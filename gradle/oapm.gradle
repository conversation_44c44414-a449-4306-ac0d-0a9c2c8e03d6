apply from: "${rootDir}/gradle/ExtMethods.gradle"

if (useOapm.toBoolean()) {
    apply plugin: 'oapm-perf'
    apply plugin: 'osigner'
    osign {
        brand = 'PSW'
        project = 'Oplus_prj'
        platform = 'Oplus_key'
        signType = 'oppo_data_app_std'
        signVersion = 'single'
    }
    OApmConfig {
        // Usage referenc:http://perf.itest.adc.com/guid
        defaultConfig {
            applicationClassName 'com/coloros/gallery3d/app/App'
            // 是否监控所有进程，默认只监控主进程。true则监控所有进程
            monitorAllProcess true
            autoDependencies {
                enabled true
                variantFilters 'opporealmeDeviceallExportApilevelallOapmImplementation',
                        'opporealmeDeviceallDomesticApilevelallOapmImplementation',
                        'opporealmeDeviceallGdprApilevelallOapmImplementation'
            }
            soFilters 'arm64-v8a'
            logLevel 2
            iconEnabled true
        }
        // 内存监控配置
        memory {
            enabled true
            // 内存泄露监控配置
            leak {
                enabled true
                // 配置发生泄露后是否进行导航栏提示，默认开启提示，接入方希望不进行提示时
                showTipsOnNav true
                // 内存泄露是否在服务端分析，默认 fasle，若希望服务端分析并下载hprof文件、或开启重复bitmap分析，设为 true 即可
                analysisOnServer true
            }
        }
        // 卡顿监控配置
        block {
            // 卡顿监控开关，默认开启
            enabled true
            // 配置卡顿阈值，单位: ms，取值范围为(100, 5000)，范围外取默认值 1000
            threshold 300
            excludes "kotlin/"
        }
        // io监控配置
        io {
            // io监控开关，默认开启
            enabled true
            nativeDetect {
                // native hook的检测开关，默认开启，关闭后应用将不包含 oapm 相关 so
                enabled true
            }
        }
        // 启动速度监控配置
        startupSpeed {
            enabled true
            // Activity启动速度检测
            type 1
        }
        // 流畅度监检测配置
        frame {
            // 流畅度检测开关，默认开启
            enabled true
            //type为1时，会采集应用所有Activity的掉帧数据，然后以Activity为单位，衡量Activity的流畅情况。
            type 1
        }
        // SQLite监检测配置
        sqlite {
            // SQLite检测开关，默认开启
            enabled true
        }
        //页面加载速度监控
        page {
            enabled true
        }
        //为了抓trace能看到项目全量方法的trace，需要增加otrace配置
        oTrace {
            enabled true
        }
    }

    android {
        signingConfigs {
            testkey {
                storeFile file("${prop_keyPath}/testkey.keystore")
                keyAlias 'androidtestkeykey'
                keyPassword prop_keyPassword
                storePassword prop_storePassword
                v1SigningEnabled prop_apkSignatureSchemeV1.toBoolean()
                v2SigningEnabled prop_apkSignatureSchemeV2.toBoolean()
            }
        }
        buildTypes {
            oapm {
                initWith(buildTypes.release)
                matchingFallbacks = ['release']
                //config enable proGuard
                minifyEnabled false
                //config enable shrink unused resources
                if (isBundleFun()) {
                    // aab包在已有签名情况下无法再次进行重签名，因此构建时不进行签名
                    //config enable shrink unused resources
                    shrinkResources false
                } else {
                    signingConfig signingConfigs.testkey
                    shrinkResources false
                }
                //proGuard rules files
                proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            }
        }
    }

    dependencies {
        oapmImplementation "otestPlatform:coverageLibDisk:$coverageVersion"
    }
}