/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MapViewFragment.kt
 * Description: 显示地图页
 * Version: 1.0
 * Date: 2020/11/2
 * Author: huang.linpeng@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * huang.linpeng@Apps.Gallery3D      2020/11/2     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.mappage

import android.Manifest
import android.content.Context
import android.graphics.Color
import android.graphics.Point
import android.location.LocationManager
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.coui.appcompat.uiutil.ShadowUtils
import com.google.android.material.appbar.AppBarLayout
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_ALBUM_SUPPORT_PERSONAL_FILTER
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_ALBUM_TITLE
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_FROM_IMAGE_DETAIL
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_FROM_TIME_LINE
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_HIDE_INTERNAL_TOOLBAR
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_IMAGE_DETAIL_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_MAP_DEFAULT_ZOOM
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_MEDIA_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_PATH_CHILD_INDEX
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.ALBUM_FRAGMENT
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.initMaskBlur
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.findMapContainerId
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.IFragmentImmersiveJudger
import com.oplus.gallery.basebiz.uikit.fragment.IMapAgreeStateChangeCallback
import com.oplus.gallery.basebiz.uikit.fragment.IMapTravelTabController
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentStartType
import com.oplus.gallery.business_lib.helper.MapPageJumper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.data.location.set.MapLocationAlbum
import com.oplus.gallery.business_lib.model.data.map.MapAlbum
import com.oplus.gallery.foundation.ui.helper.enablePressFeedback
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.ui.systembar.statusBarHeight
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.map.IMapAbility
import com.oplus.gallery.framework.abilities.map.IMapAbility.ILocationResultCallback
import com.oplus.gallery.framework.abilities.map.IMapAbility.MapStateConfigKeys
import com.oplus.gallery.framework.abilities.map.LatLng
import com.oplus.gallery.framework.abilities.map.MapConstants.LOCATE_DEFAULT_ZOOM
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_MAP_PAGE
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.KEY_FROM_PAGE
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@RouterNormal(RouterConstants.RouterName.MAPVIEW_FRAGMENT)
class MapViewFragment : TemplateFragment(), IFragmentImmersiveJudger, IMapAgreeStateChangeCallback {
    companion object {
        private const val TAG = "MapViewFragment"
        private const val MAP_PAGE_TITLE = "map_page_title"
        private const val GOOGLE_PLAY_SERVICE_PAKCAGE_NAME = "com.google.android.gms"
        private const val CLICK_INTERVAL = 700
    }

    private var pathChildIndex = 0
    private var mediaSetPath: String? = null
    private var mMediaAlbumMap: MapLocationAlbum? = null
    private var mapAbility: IMapAbility? = null
    private val mapActivity: BaseActivity by lazy {
        requireActivity() as BaseActivity
    }
    private var lauchFromImageDetail: Boolean = false
    private var launchFromTimeline: Boolean = false
    private val locationManager by lazy { context?.getSystemService(Context.LOCATION_SERVICE) as LocationManager }

    // <EMAIL>, add for bug791430
    // Prevents against the situation that binder failed
    private var isShowMapOnlyMode = false
    private var isEnterLocationSetting = false
    private var isEnterPermissionSetting = false
    private val isRegionCN: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN)
    }
    // <EMAIL> end
    private var mapAlbumPath: Path? = null
    private var singelItemPathString: String? = null
    private var defaultZoomLevel: Float? = null

    private var nearByShowButton: COUIButton? = null
    private var modeChange: IMapAbility.NearbyModeChangeCallback = object : IMapAbility.NearbyModeChangeCallback {
        override fun nearByModeChange(showNearBy: Boolean) {
            GLog.d(TAG, LogFlag.DL, "nearByModeChange nearByModeChange $showNearBy")
            //这里后面需要修改为动效切换
            nearByShowButton?.setText(if (showNearBy) R.string.show_nearby_photo else R.string.hide_nearby_photo)
        }
    }

    private var currentMapWrapperId: String = ""
    private val locationPermissionsRequest = registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
        if (permissions[Manifest.permission.ACCESS_FINE_LOCATION] == true) {
            isGpsEnabled()
        } else if (permissions[Manifest.permission.ACCESS_COARSE_LOCATION] == true) {
            isGpsEnabled()
        } else {
            GLog.d(TAG, LogFlag.DL, "registerForActivityResult:denied all access")
            showGpsDialog(true) {
                isEnterPermissionSetting = true
            }
        }
    }

    private val isDomestic: Boolean by lazy {
        val configAbility = ContextGetter.context.getAppAbility<IConfigAbility>()
        val isDomestic = configAbility?.use {
            it.getBooleanConfig(ConfigID.Common.SystemInfo.IS_REGION_CN, defValue = false) ?: false
        } ?: false.also {
            GLog.e(TAG, LogFlag.DL, "getBoolean configAbility is null, configId:${ConfigID.Common.SystemInfo.IS_REGION_CN}")
        }
        isDomestic
    }

    private val locationCallback = object : ILocationResultCallback {
        override fun onLocationResult(resultCode: Int, extra: String, latLng: LatLng?) {
            GLog.d(TAG, LogFlag.DL, "onLocationResult resultCode: $resultCode, extra $extra")
            if (resultCode == ILocationResultCallback.CallbackResultCode.NETWORK_LOCATION_FAILED) {
                //定位失败时提示
                val toastResId = if (extra == ILocationResultCallback.LocationExtra.FAILURE_NETWORK_UNAVAILABLE) {
                    R.string.mappage_locate_error_type_network
                } else {
                    R.string.mappage_locate_error_type_unknown
                }
                ToastUtil.showShortToast(toastResId)
            }
        }
    }

    //顶部渐变蒙层
    private val topTranslucentView: ImageView? by lazy { view?.findViewById(R.id.top_Translucent_View) }

    //底部渐变蒙层
    private val bottomTranslucentView: ImageView? by lazy { view?.findViewById(R.id.bottom_Translucent_View) }

    //顶部toolbar区域的appbarLayout
    private val appBarLayout: AppBarLayout? by lazy { view?.findViewById(R.id.toolbar_outer_appbar) }

    //顶部toolbar区域back按钮
    private var backIcon: ImageView? = null

    //顶部toolbar区域定位按钮
    private var locationIcon: ImageView? = null

    /**
     * 地图的父布局
     */
    private var mapViewContainer: FrameLayout? = null

    //获取父Fragment作为tab持有者，父fragment为MapTravelTabFragment
    private val parentTabController by lazy { parentFragment as? IMapTravelTabController }


    override fun onCreate(savedInstanceState: Bundle?) {
        GLog.d(TAG, LogFlag.DL, "onCreate $savedInstanceState")
        super.onCreate(savedInstanceState)
        //初始化sdk
        mapAbility = context?.getAppAbility<IMapAbility>()
        mapAbility?.initMapSdk(this.mapActivity)
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        GLog.d(TAG, LogFlag.DL, "doViewCreated $view savedInstanceState $savedInstanceState")
        super.doViewCreated(view, savedInstanceState)
        initViews(view)
        initToolbar()
        initDataAndSetupMapViewFromInputBundle(savedInstanceState)
        initTopTranslucentView()
        initBottomTranslucentView()
        //触发MapTravelTab页进行检查是否进入沉浸态
        checkPageImmersiveState()
    }

    private fun initDataAndSetupMapViewFromInputBundle(savedInstanceState: Bundle?) {
        //判断是否从MapActivity过来还是从MapTravelFragment过来，分别取不同的输入参数bundle
        val bundle = if (!isFromMapActivity()) {
            GLog.d(TAG, LogFlag.DL) { " doViewCreated from map travel fragment, bundle from arguments" }
            this.arguments
        } else {
            GLog.d(TAG, LogFlag.DL) { " doViewCreated from map activity, bundle from arguments" }
            mapActivity.intent.extras
        }
        bundle?.let {
            initDataAndSetupMapView(it, savedInstanceState)
        }
    }

    private fun initViews(view: View) {
        nearByShowButton = view.findViewById(R.id.mode_switch)
        backIcon = view.findViewById(R.id.backIcon)
        locationIcon = view.findViewById(R.id.locationIcon)
        mapViewContainer = view.findViewById(R.id.mapContainer)
    }


    private fun initToolbar() {
        backIcon?.setOnClickListenerWithPressFeedback {
            //点击事件处理
            mapActivity.onBackPressed()
        }
        locationIcon?.setOnClickListenerWithPressFeedback {
            //点击事件处理
            if (DoubleClickUtils.isFastDoubleClick(CLICK_INTERVAL)) {
                return@setOnClickListenerWithPressFeedback
            }
            requestLocationPermissions()
        }
    }

    /**
     * 设置顶部渐变遮罩的模糊效果
     * -> 由于底部MapView是surfaceView实现，这个代码不生效。具体限制见：https://odocs.myoas.com/docs/Wr3DVex5Ljcp2lkJ 密码：wvadcy
     */
    private fun initTopTranslucentView() {
        // 刷新模糊顶部遮罩质感模糊效果
        GLog.d(TAG, LogFlag.DL, "setTopTranslucentViewBlure")
        topTranslucentView?.let {
            it.initMaskBlur(true)
            it.requestLayout()
        }
    }


    private fun initBottomTranslucentView() {
        GLog.d(TAG, LogFlag.DL, "initBottomTranslucentView")
        bottomTranslucentView?.visibility = View.VISIBLE
    }


    /**
     * 检查页面的沉浸式状态
     */
    private fun checkPageImmersiveState(
        position: Int = 0,
        isForceStart: Boolean = false,
        isNeedAnimation: Boolean = true
    ) {
        GLog.d(TAG, LogFlag.DL, "checkPageImmersiveState mainTabController $parentTabController")
        parentTabController?.checkPageImmersiveState(position, isForceStart, isNeedAnimation)
    }

    override fun onBackPressed(): Boolean {
        GLog.d(TAG, LogFlag.DL, "onBackPressed $this")
        return true
    }

    private fun createAlbum(items: ArrayList<Path>?, index: Int): Path? {
        if (items.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL, "createAlbum, Path items are null or empty")
            return null
        }
        return (DataManager.getMediaSet(Local.PATH_ALBUM_MAP_ADDRESS.getChild(index)) as? MapAlbum)
            ?.apply {
                setMediaItems(items)
            }?.path
    }

    private fun jumpToAlbum(index: Int) {
        val bundle = Bundle().apply {
            putString(KEY_MEDIA_PATH, mapAlbumPath?.toString())
            putBoolean(KEY_HIDE_INTERNAL_TOOLBAR, false)
            putInt(KEY_FROM_PAGE, FROM_MAP_PAGE)
            putString(KEY_ALBUM_TITLE, activity?.title?.toString())
            putBoolean(KEY_ALBUM_SUPPORT_PERSONAL_FILTER, false)
            putInt(KEY_PATH_CHILD_INDEX, index)
        }
        val isFromMapActivity = isFromMapActivity()
        val containerResId = if (isFromMapActivity) {
            //MapActivity跳转过来时，使用MapActivity内部的container
            R.id.base_fragment_container
        } else {
            //MapTravelFragment跳转过来时，使用MainActivity的全屏的container
            activity.findMapContainerId()
        }
        startByStack<BaseFragment>(
            startType = FragmentStartType.ADD, resId = containerResId, postCard = PostCard(ALBUM_FRAGMENT), data = bundle, anim = DEFAULT_ANIM_ARRAY
        )
    }

    /**
     * 2020/11/3 huang.linpeng 可以生成假数据，暂时保留
     * val time = cleatHour(System.currentTimeMillis() - 60 * 60 * 24 * 1000)
     * mediaSetPath = "${LocationSet.SET_PATH_STRING}/$time"
     * val set: LocationSet? = LocationSet.getLocalAlbum(activity, time) as LocationSet
     */
    private fun initDataAndSetupMapView(data: Bundle, savedInstanceState: Bundle?) {
        mediaSetPath = data.getString(KEY_MEDIA_PATH)
        lauchFromImageDetail = data.getBoolean(KEY_FROM_IMAGE_DETAIL, false)
        launchFromTimeline = data.getBoolean(KEY_FROM_TIME_LINE, false)
        singelItemPathString = data.getString(KEY_IMAGE_DETAIL_PATH, null)
        defaultZoomLevel = data.getFloat(KEY_MAP_DEFAULT_ZOOM)
        AppScope.launch(Dispatchers.IO) {
            val mapAgreed = context?.let { MapPageJumper.Companion.checkMapAgreed(it) } ?: false
            GLog.d(TAG, LogFlag.DF, "initDataAndView mapAgreedRead $mapAgreed")
            val set = DataManager.getMediaSet(Path.fromString(mediaSetPath)) as? MapLocationAlbum
            val mediaItemList = set?.let {
                mMediaAlbumMap = set
                if (lauchFromImageDetail) {
                    LocalMediaDataHelper.getSingleItemForPath(singelItemPathString)
                } else {
                    set.getSubMediaItem(0, 1)
                }
            }
            if (mediaItemList == null) {
                GLog.e(TAG, LogFlag.DL) { "initData MediaSet is not LocationSet. Path = $mediaSetPath" }
            }
            //这里在协程中初始化国家码配置
            mapAbility?.initCountryMapConfig(false, true)
            runOnUiThread {
                setupMapViewLayout(savedInstanceState, mediaItemList, mapAgreed)
            }
        }
        GLog.d(
            TAG, LogFlag.DL, "initData mediaSetPath $mediaSetPath, mMediaAlbumMap $mMediaAlbumMap, " +
                    "lauchFromImageDetail $lauchFromImageDetail"
        )
    }

    private fun setupMapViewLayout(savedInstanceState: Bundle?, mediaItems: List<MediaItem>?, mapAgreed: Boolean) {
        isShowMapOnlyMode = mediaItems.isNullOrEmpty() || !mapAgreed
        GLog.d(TAG, "initView mediaItems ${mediaItems?.size}, mapAgreed $mapAgreed, isShowMapOnlyMode $isShowMapOnlyMode")
        //这里要把子View给remove掉，防止重复添加mapView
        mapViewContainer?.removeAllViews()
        val latlong = DoubleArray(2)
        val hintItem = if (mediaItems?.isNotEmpty() == true) {
            (mediaItems[0] as LocalMediaItem).apply {
                getLatLong(latlong)
            }
        } else null
        mediaSetPath?.let {
            val mapConfig = if (lauchFromImageDetail) {
                IMapAbility.InitMapConfig(IMapAbility.EntranceType.IMAGE_DETAIL_INNER, it)
            } else if (launchFromTimeline) {
                IMapAbility.InitMapConfig(IMapAbility.EntranceType.MAP_PAGE_TIMELINE, it)
            } else {
                IMapAbility.InitMapConfig(IMapAbility.EntranceType.MAP_PAGE_NORMAL, it)
            }
            mapConfig.apply {
                showMarker = !isShowMapOnlyMode
                initLocation = LatLng(latlong[0], latlong[1])
                hintImage = hintItem
                markerClickCallback = object : IMapAbility.MarkerClickListener {
                    override fun onPick(items: ArrayList<Path>, point: Point) {
                        GLog.d(TAG, LogFlag.DL, "onPick items ${items.size}, point $point")
                        processMarkerPick(items, point)
                    }
                }
                if (lauchFromImageDetail) {
                    singleItemPath = singelItemPathString
                }
                defaultZoomLevel?.let {
                    defaultZoom = it
                }
                //对外的注册接口去掉了，这里通过配置注册callback在MapAbility内部完成callback的注册
                nearbyModeChangeCallback = modeChange
            }
            mapViewContainer?.let { container ->
                currentMapWrapperId = mapAbility?.initMapView(container, savedInstanceState, mapConfig) ?: ""
            }
            GLog.d(TAG, LogFlag.DL, "initView mapWrapperId $currentMapWrapperId, mapConfig showMarker ${mapConfig.showMarker}")
        }
        //这里需要在在initMapView之后调用
        setupNearBySwitch()
    }

    /**
     * 这个回调是处理从MapTravelFragment中点击地图授权弹窗，地图授权的逻辑
     */
    override fun onMapAgreeStateChanged(mapAgreed: Boolean) {
        GLog.d(TAG, "onMapAgreeStateChanged $mapAgreed")
        contentView?.let {
            //重新初始化MapView
            initDataAndSetupMapViewFromInputBundle(null)
        }
    }

    private fun setupNearBySwitch() {
        if (lauchFromImageDetail) {
            nearByShowButton?.visibility = View.VISIBLE
        } else {
            nearByShowButton?.visibility = View.GONE
        }
        GLog.d(TAG, LogFlag.DL, "initNearBySwitch lauchFromImageDetail $lauchFromImageDetail")
        nearByShowButton?.setOnClickListener {
            val showNearBy = mapAbility?.getCurrentNearByMode(currentMapWrapperId)
            showNearBy?.let {
                mapAbility?.switchShowNearbyImage(currentMapWrapperId, !showNearBy)
            }
        }
        nearByShowButton?.let {
            ShadowUtils.setElevationToView(it, ShadowUtils.SHADOW_LV2)
            it.roundType = COUIButton.SMOOTH_ROUND
            it.setAnimEnable(true)
            it.setAnimType(COUIButton.FILL_BUTTON_ANIM)
        }
        val textId = if (mapAbility?.getCurrentNearByMode(currentMapWrapperId) == true) R.string.show_nearby_photo else R.string.hide_nearby_photo
        nearByShowButton?.setText(textId)
    }

    private fun processMarkerPick(items: ArrayList<Path>?, point: Point) {
        val index = pathChildIndex++
        mapAlbumPath = createAlbum(items, index)
        jumpToAlbum(index)
    }


    override fun getLayoutId() = R.layout.map_fragment_mapview_layout

    override fun onStart() {
        super.onStart()
        GLog.d(TAG, LogFlag.DL, "onStart")
        mapAbility?.onResume(currentMapWrapperId)
    }

    override fun onResume() {
        super.onResume()
        GLog.d(TAG, LogFlag.DL, "onResume")
        view?.findViewById<View>(com.oplus.gallery.basebiz.R.id.divider)?.visibility = View.GONE
        if (isEnterPermissionSetting) {
            isEnterPermissionSetting = false
            GLog.d(TAG, LogFlag.DL, "onResume check location permissions again")
            requestLocationPermissions()
        }
        if (isEnterLocationSetting) {
            isEnterLocationSetting = false
            if (locationManager.isLocationEnabled) {
                GLog.d(TAG, LogFlag.DL, "onResume gps settings have been turned on")
                //从设置页面回到MapViewFragment，此时同意了定位服务，触发定位
                mapAbility?.locateInCurrentPosition(mapInstanceId = currentMapWrapperId, callback = locationCallback)
            }
        }
        // 这里检查是否需要对MapView的父容器设置paddingTop
        context?.let { trigerCheckUpdateMapViewPadding(it) }
    }

    override fun onStop() {
        GLog.d(TAG, LogFlag.DL, "onStop")
        super.onStop()
        mapAbility?.onPause(currentMapWrapperId)
    }

    override fun onDestroy() {
        GLog.d(TAG, LogFlag.DL, "onDestroy")
        super.onDestroy()
        mapAbility?.onDestroy(currentMapWrapperId)
    }

    /**
     * 这里设置MapViewFragment全屏显示，顶部和底部padding=0
     * 这里PaddingFragmentSystemBarStyle
     */
    override fun getSystemBarStyle() = object : FragmentSystemBarStyle(this) {

        override fun onInit() {
            GLog.d(TAG, LogFlag.DL, "onInit windowInsets")
            setMockNaviBarEnable(true)
            refreshNavigationBarColor()
        }

        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            super.onUpdate(windowInsets, isForeground)
            GLog.d(TAG, LogFlag.DL, "onUpdate windowInsets $windowInsets, isForeground $isForeground")
            //设置底部导航栏颜色为透明
            setNaviBarColor(Color.TRANSPARENT)
            //设置顶部状态栏颜色为透明
            setStatusBarColor(Color.TRANSPARENT)
            //顶部状态栏的夜间模式还是日间模式
            if (isForeground) {
                // 解决从地图图集返回到地图页状态栏显示白色
                setStatusBarAppearance(isStatusBarLightAppearance())
            }
            refreshNavigationBarColor()
            windowInsets.naviBarInsets().apply {
                // 底部沉浸显示和顶部全部延伸到屏幕顶端和底部
                setContentPadding(left, 0, right, 0)
                val buttomMarginAboveSystemBar = context?.resources?.getDimension(R.dimen.show_nearby_buttom_margin_buttom)
                buttomMarginAboveSystemBar?.let {
                    val marginBottom = this.bottom + it
                    GLog.d(TAG, LogFlag.DL, "onUpdate marginBottom $marginBottom, navigationBar.bottom: " + this.bottom)
                    nearByShowButton?.updateMargin(bottom = marginBottom.toInt())
                }
            }
            //顶部toolbar区域更新margin
            val statusBarHeight = windowInsets.statusBarHeight()
            appBarLayout?.updateMargin(top = statusBarHeight)
            GLog.d(TAG, LogFlag.DL, "onUpdate statusBarHeight $statusBarHeight, appBarLayout $appBarLayout")
        }
    }

    /**
     * 沉浸后，除了图集选择模式以外，虚拟导航栏背景都是透明
     */
    private fun refreshNavigationBarColor() {
        if (!isResumed) return
        context?.let {
            setMockNaviBarColor(Color.TRANSPARENT)
        }
    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val mapStateConfig = mapAbility?.getCurrentMapStateConfig(currentMapWrapperId)
        mapStateConfig?.let {
            GLog.d(
                TAG,
                LogFlag.DL,
                "onSaveInstanceState rotate:${it.rotate}, centerTarget:${it.centerTarget}, zoom:${it.zoom}, overlook:${it.overlook}"
            )
            outState.putFloat(MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_ROTATE, it.rotate ?: 0f)
            outState.putParcelable(MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_CENTER_TARGET, it.centerTarget)
            outState.putFloat(MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_ZOOM, it.zoom ?: LOCATE_DEFAULT_ZOOM)
            outState.putFloat(MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_OVERLOOK, it.overlook ?: 0f)
        }
    }

    /**
     * check Gps is open
     */
    private fun isGpsEnabled() {
        GLog.d(TAG, LogFlag.DL, "isGpsEnabled ${locationManager.isLocationEnabled}")
        if (locationManager.isLocationEnabled) {
            mapAbility?.locateInCurrentPosition(mapInstanceId = currentMapWrapperId, callback = locationCallback)
            return
        }
        showGpsDialog {
            isEnterLocationSetting = true
        }
    }

    private fun requestLocationPermissions() {
        locationPermissionsRequest.launch(
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION
            )
        )
    }

    private fun trigerCheckUpdateMapViewPadding(context: Context) {
        AppScope.launch(Dispatchers.IO) {
            val needUpdate = checkNeedUpdateMapViewPadding(context)
            processUpdateMapViewPadding(needUpdate)
        }
    }

    /**
     * 检测Gms是否被禁用
     */
    private fun checkNeedUpdateMapViewPadding(context: Context): Boolean {
        if (isDomestic) {
            GLog.d(TAG, LogFlag.DL, "checkNeedUpdateMapViewPadding isDomestic return false")
            return false
        }
        val isGmsEnable = PackageInfoUtils.isPackageEnabled(context, GOOGLE_PLAY_SERVICE_PAKCAGE_NAME)
        GLog.d(TAG, LogFlag.DL, "checkNeedUpdateMapViewPadding isGmsEnable $isGmsEnable")
        return !isGmsEnable
    }

    /**
     * 根据gms是否被禁用，更新mapView的父容器的padingTop
     * gms禁用时，paddingTop = appbarLayout的height
     * gms未被禁用时，paddingTop = 0
     */
    private fun processUpdateMapViewPadding(needPadding: Boolean) {
        appBarLayout?.let {
            it.post {
                val height = it.height
                GLog.d(TAG, LogFlag.DL, "processUpdateMapViewPadding $height, needPadding $needPadding")
                val mapViewContainer = view?.findViewById<FrameLayout>(R.id.mapContainer)
                if (needPadding) {
                    mapViewContainer?.setPadding(0, height, 0, 0)
                } else {
                    mapViewContainer?.setPadding(0, 0, 0, 0)
                }
            }
        }
    }

    /**
     * 判断当前页面是否是从MapActivity跳转过来
     * @return false 时则为从MapTravelTabFragment
     */
    private fun isFromMapActivity(): Boolean {
        val isFromMapActivity = this.parentFragment == null
        return isFromMapActivity
    }


    /**
     * @return 当前底部是否为沉浸式，默认返回 true
     */
    override fun isBottomImmersiveCurrently(): Boolean = true

    /**
     * @return 当前顶部是否为沉浸式，默认返回 true
     */
    override fun isTopImmersiveCurrently(): Boolean = true


    private fun View.setOnClickListenerWithPressFeedback(onClick: () -> Unit) {
        setOnClickListener {
            if (DoubleClickUtils.isFastDoubleClick()) return@setOnClickListener
            onClick()
        }
        // 增加点击时按压态缩放动效
        COUIPressFeedbackHelper(this, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK).enablePressFeedback(this)
    }
}
