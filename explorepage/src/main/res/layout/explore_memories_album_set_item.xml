<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true">

    <com.oplus.gallery.explorepage.view.MaxHeightRectImageView
        android:id="@+id/main_explorer_album_set_item_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:forceDarkAllowed="false"
        android:importantForAccessibility="no"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_explorer_album_set_item_title_text"
        style="@style/main_SingleColumnAlbumSetItemText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/main_single_column_album_item_margin_start"
        android:layout_marginEnd="@dimen/main_single_column_album_item_margin_end"
        android:layout_marginBottom="@dimen/main_single_column_album_item_title_margin_bottom"
        android:includeFontPadding="false"
        android:maxLines="@integer/main_memories_title_text_max_line"
        android:textAppearance="@style/gTextAppearanceHeadline2"
        android:textDirection="locale"
        app:layout_constraintBottom_toTopOf="@+id/main_explorer_album_set_item_sub_title_text"
        app:layout_constraintEnd_toStartOf="@id/main_memories_album_set_item_checkbox"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_goneMarginEnd="@dimen/main_normal_album_set_fragment_checkbox_margin_right" />

    <TextView
        android:id="@+id/main_explorer_album_set_item_sub_title_text"
        style="@style/main_SingleColumnAlbumSetItemText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/main_single_column_album_item_sub_title_margin_bottom"
        android:ellipsize="end"
        android:maxLines="@integer/main_memories_sub_title_text_max_line"
        android:textAppearance="@style/gTextAppearanceDescription"
        android:textDirection="locale"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/main_explorer_album_set_item_title_text"
        app:layout_constraintStart_toStartOf="@id/main_explorer_album_set_item_title_text" />

    <ViewStub
        android:id="@+id/main_memories_album_set_item_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/main_normal_album_set_fragment_checkbox_margin_right"
        android:layout_marginBottom="@dimen/main_normal_album_set_fragment_checkbox_margin_bottom"
        android:button="@null"
        android:clickable="false"
        android:focusable="false"
        android:forceDarkAllowed="false"
        android:inflatedId="@+id/main_memories_album_set_item_checkbox"
        android:layout="@layout/base_checkbox_view"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>