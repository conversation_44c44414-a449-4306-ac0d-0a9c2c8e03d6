/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelAlbumSetFragment.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/07
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>	2020/09/07		1.0		create
 *********************************************************************************/
package com.oplus.gallery.explorepage.label

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.updateLayoutParams
import androidx.core.view.updateMargins
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GalleryGridLayoutManager
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.TrackConstant.ALBUM_NAVIGATION_TRACK_PAGE_ID
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.NetworkFloatingViewHelper
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isFlat
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.widget.FloatingTipsView
import com.oplus.gallery.business_lib.helper.PermissionDialogHelper
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.label.set.LabelAlbum.INVALID_LABEL_ID
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumSetFragment
import com.oplus.gallery.business_lib.ui.fragment.TimeNodeFragment
import com.oplus.gallery.business_lib.ui.view.CardAlbumSetViewDataBinding
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.TYPE_ALBUMS_ACTION
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_LABEL_ALBUM
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.ui.R
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.LabelModelGetter.Companion.TYPE_LABEL_ALBUM_SET
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.basebiz.R as BasebizR
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_LABEL_SECOND
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter

@RouterNormal(RouterConstants.RouterName.LABEL_FRAGMENT)
class LabelAlbumSetFragment : BaseAlbumSetFragment() {

    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_LABEL_ALBUM_SET_FRAGMENT
    private val networkFloatingViewAsync = AsyncObj(lifecycleScope) {
        NetworkFloatingViewHelper.initNetWorkViewByType(
            type = NetworkFloatingViewHelper.TipsType.LABEL,
            context = requireContext(),
            onClickCallback = fun(_: NetworkFloatingViewHelper.ClickType) {
                refreshNetworkFloatingView()
            }
        ).apply {
            view?.post {
                val lp = CoordinatorLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                lp.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                (view as? ViewGroup)?.addView(this, lp)
                updateFloatingTipsMargin()
            }
        }
    }

    private fun FloatingTipsView.updateFloatingTipsMargin() {
        layoutParams ?: return run {
            GLog.e(TAG, "updateFloatingTipsMargin layoutParams is null return")
        }
        updateLayoutParams<ViewGroup.MarginLayoutParams> {
            val leftOrRightMargin = resources.getDimensionPixelSize(BasebizR.dimen.main_floating_network_margin_horizontal)
            updateMargins(
                left = leftOrRightMargin,
                right = leftOrRightMargin,
                bottom = if (ScreenUtils.isMiddleAndLargeScreen(context)) {
                    resources.getDimensionPixelSize(BasebizR.dimen.main_floating_layout_margin_bottom_height)
                } else {
                    resources.getDimensionPixelSize(com.support.snackbar.R.dimen.coui_snack_bar_margin_bottom)
                }
            )
        }
    }

    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> {
        return ViewModelProvider(this).get(LabelAlbumSetViewModel::class.java)
    }

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        context?.let {
            val labelThumbStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
                put(StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS, resources.getDimension(BasebizR.dimen.label_album_set_item_cover_corners_radius))
                put(StyleData.KEY_THUMB_SIZE_TYPE, resources.getDimension(BasebizR.dimen.label_album_set_item_cover_width))
                put(StyleData.KEY_THUMB_STROKE_WIDTH, resources.getDimension(BasebizR.dimen.label_album_set_item_cover_frame_stroke_width))
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, it.getColor(BasebizR.color.label_album_set_frame_stroke_color))
                put(StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR, COUIContextUtil.getAttrColor(it, R.attr.gColorPressBackground))
            }
            viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, labelThumbStyleData)
        }
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        baseListViewModel?.setViewData(
            AlbumViewData(
                //fixme 阿昌，修改为type
                id = SourceConstants.Local.PATH_SET_LABEL_ANY.toString(),
                position = 0,
                modelType = TYPE_LABEL_ALBUM_SET,
                isMediaAlbum = true,
                version = 0,
                totalCount = 0,
                title = ""
            )
        )
        (recyclerView as? COUIRecyclerView)?.apply {
            setItemClickableWhileSlowScrolling(false)
            setItemClickableWhileOverScrolling(false)
        }
    }

    override fun refreshToolbar() {
        super.refreshToolbar()
        if (!isInSelectionMode() && showInternalToolbar) {
            toolbarSetter?.setTitle(getText(BasebizR.string.label_group_title))
        }
    }

    override fun onResume() {
        super.onResume()
        refreshNetworkFloatingView()
    }

    override fun onTotalCountChanged(totalCount: Int) {
        super.onTotalCountChanged(totalCount)
        refreshNetworkFloatingView()
    }

    private suspend fun shouldShowNetworkFloatingView(): Boolean {
        return NetworkFloatingViewHelper.isFirstClosedLabelAlbumNetworkTip()
                && (!NetworkPermissionManager.isUseOpenNetwork)
                && isPrivacyAuthorizedSuspend()
    }

    private fun refreshNetworkFloatingView() {
        networkFloatingViewAsync.getIt { networkFloatingView ->
            NetworkFloatingViewHelper.refreshNetworkFloatingView(networkFloatingView, lifecycleScope) {
                shouldShowNetworkFloatingView()
            }
        }
    }

    override fun getAdapterItemConfigs(): List<ItemConfig<AlbumViewData>> {
        return mutableListOf<ItemConfig<AlbumViewData>>().apply {
            context?.let { context ->
                add(
                    ItemConfig(
                        AlbumViewData::class.java,
                        CardAlbumSetViewDataBinding(context = context, stylePool = baseListViewModel)
                    )
                )
            }
        }
    }

    override fun initLayoutDetail(context: Context): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        parentWidth = getContentWidth()
        edgeWidth = context.resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_horizontal_page_margin).toInt()
        gapWidth = context.resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_horizontal_gap).toInt()
        spanCount = getSpanCount()
    }.build().apply {
        itemDecorationGapPx.top = context.resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_top_gap)
        itemDecorationGapPx.bottom = context.resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_bottom_gap)
    }

    override fun isPageItemSelectable(): Boolean = false

    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
        GLog.d(TAG, "onItemClick: viewData = viewData")
        viewData?.let { data ->
            val context = activity ?: run {
                GLog.w(TAG, "onItemClick , context is null")
                return
            }
            AlbumsActionTrackHelper.trackAndSendAlbumsClick(
                    currentPage = getUserActionCurPage(TYPE_ALBUMS_ACTION),
                    path = data.id,
                    albumName = data.title,
                    albumType = AlbumsActionTackConstant.Value.ALBUMS_ACTION_LABEL_ALBUM_VALUE
                )
                AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                    totalCount = data.totalCount.toString(),
                    albumName = data.title
                )
                data.title?.let {
                    var labelId = INVALID_LABEL_ID
                    runCatching {
                        labelId = TextUtil.getPathSuffix(data.id)?.toInt() ?: INVALID_LABEL_ID
                    }.onFailure {
                        GLog.e(TAG, "onItemClick id=${data.id} error: ", it)
                    }
                    baseListViewModel?.loadLabelGalleryIdList(context.applicationContext, it, labelId) {
                        resetSeamlessAnimRootNodeConfig(
                            resources.configuration.orientation,
                            resources.configuration.screenWidthDp,
                            resources.configuration.screenHeightDp
                        )
                        val floatingWindowOffset = IntArray(2)
                        if (isFloatingWindowMode()) view?.rootView?.getLocationOnScreen(floatingWindowOffset)
                        val clickedItemView = recyclerAdapter.getItemByIndex(position)
                        startByStack<TimeNodeFragment>(
                            postCard = PostCard(RouterConstants.RouterName.TIME_NODE_FRAGMENT),
                            anim = SEAMLESS_ANIM_ARRAY,
                            data = Bundle().apply {
                                putString(IntentConstant.ViewGalleryConstant.KEY_GALLERY_ID_LIST, it)
                                putString(ALBUM_NAVIGATION_TRACK_PAGE_ID, CUR_PAGE_LABEL_ALBUM)
                                putString(IntentConstant.LabelConstant.KEY_LABEL_NAME, viewData.title)
                                putInt(IntentConstant.LabelConstant.KEY_LABEL_ID, labelId)
                                FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                                    context,
                                    this,
                                    clickedItemView,
                                    TriggerViewRectGetter(clickedItemView, floatingWindowOffset),
                                    ENTRANCE_LABEL_SECOND
                                )
                            }
                        )?.let {
                            viewData.modelType = DataRepository.TimelineModelGetter.TYPE_TIMELINE_ALBUM
                            it.setViewData(viewData)
                        }
                    }
                }
        }
    }

    override fun getUserActionCurPage(trackType: String?): String? {
        return when (trackType) {
            TYPE_ALBUMS_ACTION -> AlbumsActionTackConstant.Value.ALBUMS_ACTION_INTELLIGENCE_ALBUM_PAGE_VALUE
            else -> LaunchExitPopupConstant.Value.LABEL_ALBUM_SET_PAGE
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        if (config.orientation.isChanged()) {
            networkFloatingViewAsync.getIt {
                it.updateFloatingTipsMargin()
            }
            notifyConfigurationChanged(false)
        }
    }

    private fun notifyConfigurationChanged(sidePaneChange: Boolean) {
        layoutDetail = refreshGridLayoutDetail()
        (recyclerView?.layoutManager as? GalleryGridLayoutManager)?.let {
            (layoutDetail as? GridLayoutDetail)?.replaceGaps((layoutDetail as GridLayoutDetail).itemHorizontalGaps)
            removeAllItemDecorations()
            addItemDecoration()
            it.spanCount = layoutDetail.spanCount
        }
        recyclerAdapter.let {
            if (sidePaneChange) {
                it.notifyItemRangeChanged(0, it.itemCount)
            } else {
                it.notifyDataSetChanged()
            }
        }
    }

    private fun refreshGridLayoutDetail(): GridLayoutDetail {
        return GridLayoutDetail.HorizontalGapsBuilder().apply {
            parentWidth = getCurrentAppUiConfig().windowWidth.current
            edgeWidth = resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_horizontal_page_margin).toInt()
            gapWidth = resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_horizontal_gap).toInt()
            spanCount = getSpanCount()
        }.build().apply {
            itemDecorationGapPx.top = resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_top_gap)
            itemDecorationGapPx.bottom = resources.getDimension(BasebizR.dimen.label_album_set_fragment_item_view_bottom_gap)
        }
    }

    override fun createSidePaneListener(): ISidePaneListener {
        return this
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        super.onSidePaneSlideStart(newState)
        notifyConfigurationChanged(true)
    }

    override fun getPrivacyAuthorizeType() = AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD

    override fun showPrivacyDialog(context: Context) {
        PermissionDialogHelper.showDownloadPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    activity?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
                        it.authorizePrivacy(AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
                    }
                    NetworkPermissionManager.openNetwork(context)
                    refreshNetworkFloatingView()
                }
            })
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    /**
     * 根据屏幕宽度计算列数
     * 1、屏幕宽度小于600dp-->2列
     * 2、屏幕宽度600dp至840dp-->3列
     * 3、 屏幕宽度大于840dp-->4列
     */
    private fun getSpanCount(): Int {
        return when {
            COUIResponsiveUtils.isSmallScreen(
                context,
                getContentWidth()
            ) -> resources.getInteger(BasebizR.integer.label_album_set_colunms_count_small)

            COUIResponsiveUtils.isMediumScreen(
                context,
                getContentWidth()
            ) -> resources.getInteger(BasebizR.integer.label_album_set_colunms_count_medium)

            COUIResponsiveUtils.isLargeScreen(
                context,
                getContentWidth()
            ) -> resources.getInteger(BasebizR.integer.label_album_set_colunms_count_large)

            else -> resources.getInteger(BasebizR.integer.label_album_set_colunms_count_small)
        }
    }

    override fun getContentWidth(): Int {
        var slideWidth = 0
        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let {
                slideWidth = if (it.isFlat() && it.isOpen()) it.getSlideWidth() else 0
            }
        }
        return getCurrentAppUiConfig().windowWidth.current - slideWidth
    }

    override fun getEmptyPageIconResType(): String = EMPTY_ICON_RES_TYPE_DRAWABLE

    override fun getEmptyPageIconAnimRes(): Int = BasebizR.drawable.albumsetpage_label_empty_status

    override fun getEmptyPageSubtitleRes(): Int = BasebizR.string.smart_classify_empty_state_desc

    companion object {
        private const val TAG = "LabelAlbumSetFragment"
    }
}