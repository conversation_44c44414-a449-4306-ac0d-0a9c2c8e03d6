/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MemoriesHeaderView.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/07/30
 ** Author: wang<PERSON><EMAIL>
 ** TAG: OPLUS_THUMBNAIL
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wa<PERSON><PERSON><PERSON>@Apps.Gallery		2020/07/30		1.0		OPLUS_MEMORIESHEADERVIEW
 *********************************************************************************/
package com.oplus.gallery.explorepage.memories

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.oplus.gallery.explorepage.R
import com.oplus.gallery.foundation.ui.helper.enablePressFeedback
import com.oplus.gallery.foundation.util.debug.GLog

class MemoriesHeaderView : ConstraintLayout {
    companion object {
        private const val TAG = "MemoriesHeaderView"
    }

    private val headerContainer: ViewGroup by lazy {
        LayoutInflater.from(context).inflate(
            com.oplus.gallery.basebiz.R.layout.main_memories_header_view_layout,
                this, true) as ViewGroup
    }

    private val cover: ViewGroup by lazy {
        headerContainer.findViewById<ViewGroup>(com.oplus.gallery.basebiz.R.id.memories_cover)
    }

    private val backgroundCover: ImageView by lazy {
        headerContainer.findViewById<ImageView>(com.oplus.gallery.basebiz.R.id.memories_background)
    }

    private val titleTextView: TextView by lazy {
        headerContainer.findViewById<TextView>(com.oplus.gallery.basebiz.R.id.memories_title_text)
    }

    private val dateTextView: TextView by lazy {
        headerContainer.findViewById<TextView>(com.oplus.gallery.basebiz.R.id.memories_date_text)
    }

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
            context, attrs, defStyleAttr
    ) {
        headerContainer.findViewById<Button>(com.oplus.gallery.basebiz.R.id.memories_play_button).apply {
            setOnClickListener(OnClickListener {
                playButtonCallback?.invoke()
                GLog.d(TAG, "clicked: ${playButtonCallback.hashCode()} + ${this.hashCode()}")
            })
            COUIPressFeedbackHelper(this, COUIPressFeedbackHelper.CARD_PRESS_FEEDBACK).enablePressFeedback(this)
        }
    }

    private var playButtonCallback: (() -> Unit)? = null

    fun setClickCallback(callback: (() -> Unit)? = null) {
        playButtonCallback = callback
    }

    fun setCover(cover: Drawable?) {
        this.cover.background = cover
    }

    fun setBackgroundCover(backgroundCover: Drawable?) {
        this.backgroundCover.background = backgroundCover
    }

    fun invalidateCover() {
        cover.invalidate()
    }

    fun invalidateBackgroundCover() {
        backgroundCover.invalidate()
    }

    fun setTitle(title: String?) {
        titleTextView.text = title
    }

    fun setSubTitle(subtitle: String?) {
        dateTextView.text = subtitle
        dateTextView.isVisible = (!subtitle.isNullOrBlank())
    }
}