/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MemoryAlbumSetFragment.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/07
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** dengchuk<PERSON>@Apps.Gallery	2020/09/07		1.0		create
 *********************************************************************************/
package com.oplus.gallery.explorepage.memories

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import androidx.appcompat.app.AppCompatActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.updateLayoutParams
import androidx.core.view.updateMargins
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GalleryGridLayoutManager
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.NetworkFloatingViewHelper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_MEMORY_TYPE
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isFlat
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.animation.ToolbarFadeInFadeOutAnimator
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_MEMORIES_SECOND
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.basebiz.widget.FloatingTipsView
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.helper.PermissionDialogHelper
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local.PATH_SET_MEMORIES_ANY
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumSetFragment
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.TYPE_ALBUMS_ACTION
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.NUM_ZERO
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.ui.R
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.onWidthChanged
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_GET_USUAL_LOCATION
import com.oplus.gallery.framework.abilities.data.DataRepository.MemoriesModelGetter.Companion.TYPE_MEMORIES_ALBUM_SET
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import com.oplus.gallery.basebiz.R as BasebizR

@RouterNormal(RouterConstants.RouterName.MEMORIES_ALBUM_SET_FRAGMENT)
class MemoriesAlbumSetFragment : BaseAlbumSetFragment() {

    override val diffUpdateListener: DiffCallback<AlbumViewData> by lazy {
        object : DiffCallback<AlbumViewData>(recyclerAdapter) {
            override fun onInserted(position: Int, count: Int) {
                super.onInserted(position, count)
                // 新建图集时，滚至顶部展示动画
                if ((position == 0) && recyclerAdapter.visibleRange.inRange(0)) {
                    recyclerView?.scrollToPosition(0)
                }
            }
        }
    }

    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_MEMORIES_ALBUM_SET_FRAGMENT
    override val recyclerViewPaddingBottom: Int by lazy {
        resources.getDimensionPixelSize(BasebizR.dimen.main_memories_album_set_recyclerview_padding_bottom)
    }
    private val networkFloatingViewAsync = AsyncObj(lifecycleScope) {
        NetworkFloatingViewHelper.initNetWorkViewByType(
            type = NetworkFloatingViewHelper.TipsType.MEMORIES,
            context = requireContext(),
            onClickCallback = fun(_: NetworkFloatingViewHelper.ClickType) {
                refreshNetworkFloatingView()
            }
        ).apply {
            view?.post {
                val lp = CoordinatorLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT)
                lp.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                (view as? ViewGroup)?.addView(this, lp)
                updateFloatingTipsMargin()
            }
        }
    }

    private var menuEventNotifier: MenuOperation.EventNotifier? = null

    private fun FloatingTipsView.updateFloatingTipsMargin() {
        layoutParams ?: return run {
            GLog.e(TAG, "updateFloatingTipsMargin layoutParams is null return")
        }
        updateLayoutParams<ViewGroup.MarginLayoutParams> {
            val leftOrRightMargin = resources.getDimensionPixelSize(BasebizR.dimen.main_floating_network_margin_horizontal)
            updateMargins(
                left = leftOrRightMargin,
                right = leftOrRightMargin,
                bottom = if (ScreenUtils.isMiddleAndLargeScreen(context)) {
                    resources.getDimensionPixelSize(BasebizR.dimen.main_floating_layout_margin_bottom_height)
                } else {
                    resources.getDimensionPixelSize(com.support.snackbar.R.dimen.coui_snack_bar_margin_bottom)
                }
            )
        }
    }

    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> =
        ViewModelProvider(this).get(MemoriesAlbumSetViewModel::class.java)

    override fun getAdapterItemConfigs(): List<ItemConfig<AlbumViewData>> {
        return mutableListOf<ItemConfig<AlbumViewData>>().apply {
            context?.let { context ->
                add(
                    ItemConfig(
                        AlbumViewData::class.java,
                        MemoriesAlbumSetViewDataBinding(
                            context,
                            stylePool = baseListViewModel,
                            isMaskVisibleGet = { isMaskVisible() },
                            checkboxAnimEnableGet = { checkboxAnimEnable() }
                        )
                    )
                )
            }
        }
    }

    override fun initLayoutDetail(context: Context): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        parentWidth = getCurrentAppUiConfig().windowWidth.current.toFloat().toInt()
        edgeWidth = context.resources.getDimension(BasebizR.dimen.base_memories_album_set_fragment_item_view_horizontal_page_margin).toInt()
        gapWidth = context.resources.getDimension(BasebizR.dimen.base_memories_album_set_fragment_item_view_horizontal_gap).toInt()
        spanCount = getSpanCount()
    }.build().apply {
        itemDecorationGapPx.top = 0f
        itemDecorationGapPx.bottom = context.resources.getDimension(BasebizR.dimen.base_memories_album_set_fragment_item_view_horizontal_gap)
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        baseListViewModel?.setViewData(
            AlbumViewData(
                // fixme 阿昌，修改为type
                id = PATH_SET_MEMORIES_ANY.toString(),
                position = 0,
                modelType = TYPE_MEMORIES_ALBUM_SET,
                isMediaAlbum = true,
                version = 0,
                totalCount = 0,
                title = ""
            )
        )
        baseListViewModel?.releaseInvisibleViewDataWhenPause = true
        needBottomPaddingWhenEdit = true
        (recyclerView as? COUIRecyclerView)?.apply {
            setItemClickableWhileSlowScrolling(false)
            setItemClickableWhileOverScrolling(false)
        }
    }

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        /*
         * 1、精彩回忆中少于5张图片时，封面只显示一张图片（大图显示）
         * 2、精彩回忆中不少于5张图片时，封面显示5张图片（缩图显示）
         */
        context?.let {
            val itemWidth = layoutDetail.itemWidth
            val childWidth = resources.getDimensionPixelSize(BasebizR.dimen.memories_album_set_fragment_item_background_bitmap_size)
            val roundThumbStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
                put(StyleData.KEY_THUMB_SIZE_TYPE, getThumbSizeType(itemWidth))
                put(
                    StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS,
                    resources.getDimension(BasebizR.dimen.main_memories_album_item_cover_image_corner_radius)
                )
                put(StyleData.KEY_THUMB_STROKE_WIDTH, resources.getDimension(R.dimen.common_round_drawable_frame_stroke_width))
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, resources.getColor(BasebizR.color.common_transparent, null))
                put(StyleData.KEY_THUMB_BACKGROUND, ColorDrawable(COUIContextUtil.getColor(context, BasebizR.color.common_transparent)))
            }

            val multiThumbStyleData = roundThumbStyleData.copy().apply {
                put(StyleData.KEY_THUMB_SIZE_TYPE, childWidth)
                put(
                    StyleData.KEY_THUMB_LAYOUT_VERTICAL_GAP_BETWEEN_CHILD,
                    it.resources.getDimension(BasebizR.dimen.memories_album_set_fragment_item_child_vertical_gap)
                )
                put(
                    StyleData.KEY_THUMB_LAYOUT_HORIZONTAL_GAP_BETWEEN_CHILD,
                    it.resources.getDimension(BasebizR.dimen.memories_album_set_fragment_item_child_horizontal_gap)
                )
                put(StyleData.KEY_THUMB_SIZE_WIDTH, itemWidth)
                put(StyleData.KEY_THUMB_SIZE_HEIGHT, itemWidth / MathUtils.TWO)
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, resources.getColor(BasebizR.color.common_transparent, null))
                // 精彩回忆封面由五张缩图构成，subStyleData为五张缩图所用的样式
                put(StyleData.KEY_SUB_STYLE, roundThumbStyleData.copy().apply {
                    put(
                        StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS,
                        resources.getDimension(BasebizR.dimen.main_memories_album_item_cover_image_corner_radius)
                    )
                    put(
                        StyleData.KEY_THUMB_STROKE_PAINT_COLOR,
                        resources.getColor(BasebizR.color.memories_album_set_sub_drawable_frame_stroke_color, null)
                    )
                })
            }
            viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, roundThumbStyleData)
            viewModel.addStyle(StyleType.TYPE_MEMORIES_THUMB_STYLE, multiThumbStyleData)
        }
    }

    override fun getThumbSizeType(itemWidth: Int): Int {
        return ThumbnailSizeUtils.TYPE_THUMBNAIL
    }

    override fun onTotalCountChanged(totalCount: Int) {
        super.onTotalCountChanged(totalCount)
        refreshNetworkFloatingView()
    }

    private suspend fun shouldShowNetworkFloatingView(): Boolean {
        return !isInSelectionMode()
            && FeatureUtils.isRegionCN
            && NetworkFloatingViewHelper.isFirstClosedMemoriesAlbumNetworkTip()
            && (!NetworkPermissionManager.isUseOpenNetwork)
            && isPrivacyAuthorizedSuspend()
    }

    private fun refreshNetworkFloatingView() {
        networkFloatingViewAsync.getIt { networkFloatingView ->
            NetworkFloatingViewHelper.refreshNetworkFloatingView(networkFloatingView, lifecycleScope) {
                shouldShowNetworkFloatingView()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        refreshNetworkFloatingView()
    }

    override fun refreshToolbar() {
        super.refreshToolbar()
        if (!isInSelectionMode() && showInternalToolbar) {
            toolbarSetter?.setTitle(getText(BasebizR.string.main_album_group_recommend_memories_title))
        }
    }

    override fun onStart() {
        super.onStart()
        // 新建回忆后回到回忆列表刷新缓存
        updateCache()
    }

    override fun getBottomBarMenuId(): Int = BasebizR.menu.main_selection_memories

    override fun getEmptyPageIconAnimRes(): Int = BasebizR.raw.albumsetpage_memories_empty_status

    override fun getEmptyPageSubtitleRes(): Int = BasebizR.string.wonderful_memory_empty_state_desc

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (!isResumed) return
        toolbar?.clearMenu()
        if (isInSelectionMode()) {
            toolbar?.isTitleCenterStyle = true
            setDisplayHomeAsUpEnabled(false)
            inflater.inflate(BasebizR.menu.base_opt_album_cancel, menu)
            menu.findItem(BasebizR.id.action_cancel)?.let { menuItem ->
                val actionCancel = menuItem.actionView as? COUITextView
                actionCancel?.let { actionCancel ->
                    actionCancel.setOnClickListener {
                        ClickUtil.clickable()
                        onOptionsItemSelected(menuItem)
                    }
                }
            }
        } else {
            setDisplayHomeAsUpEnabled(true)
            toolbar?.isTitleCenterStyle = false
            inflater.inflate(BasebizR.menu.main_opt_memories_album_set, menu)
            (activity as? AppCompatActivity)?.supportActionBar?.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            (activity as? AppCompatActivity)?.supportActionBar?.setHomeActionContentDescription(BasebizR.string.base_description_home_as_up)
        }
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        if (!isResumed) return
        menu.findItem(BasebizR.id.action_create_memories)?.isVisible = !isInSelectionMode()
        menu.findItem(BasebizR.id.memories_album_fragment_edit)?.isVisible = !isInSelectionMode()
    }

    override fun dispatchLongClick(position: Int): Boolean {
        return isInSelectionMode()
    }

    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
        if (isInSelectionMode()) {
            viewData?.apply {
                baseListViewModel?.toggleItemSelection(this.position)
            }
        } else {
            resetSeamlessAnimRootNodeConfig(
                resources.configuration.orientation,
                resources.configuration.screenWidthDp,
                resources.configuration.screenHeightDp
            )
            viewData?.let { data ->
                AlbumsActionTrackHelper.trackAndSendAlbumsClick(
                    currentPage = getUserActionCurPage(TYPE_ALBUMS_ACTION),
                    path = data.id,
                    albumName = data.title,
                    albumType = AlbumsActionTackConstant.Value.ALBUMS_ACTION_MEMORY_ALBUM_VALUE
                )
                AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                    totalCount = data.totalCount.toString(),
                    albumName = data.title,
                    memoryType = data.supportedAbilities?.getInt(SUPPORT_MEMORY_TYPE, NUM_ZERO).toString()
                )
                val floatingWindowOffset = IntArray(2)
                if (isFloatingWindowMode()) view?.rootView?.getLocationOnScreen(floatingWindowOffset)
                val clickedItemView = recyclerAdapter.getItemByIndex(position)
                startByStack<MemoriesDetailsFragment>(
                    postCard = PostCard(RouterConstants.RouterName.MEMORIES_DETAIL_FRAGMENT),
                    anim = SEAMLESS_ANIM_ARRAY,
                    data = Bundle().also {
                        it.putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
                        FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                            context,
                            it,
                            clickedItemView,
                            TriggerViewRectGetter(clickedItemView, floatingWindowOffset),
                            ENTRANCE_MEMORIES_SECOND
                        )
                    }
                )
            }
        }
    }

    override fun onSelectionStateChange() {
        super.onSelectionStateChange()
        baseListViewModel?.let { viewModel ->
            val selectedCount = viewModel.getSelectedItemCount()
            bottomMenuBar?.menu?.apply {
                findItem(BasebizR.id.action_delete)?.let {
                    if (it.isEnabled && (selectedCount < SELECTED_SIZE_ONE)) {
                        it.isEnabled = false
                    } else if (!it.isEnabled && (selectedCount >= SELECTED_SIZE_ONE)) {
                        it.isEnabled = true
                    }
                }
            }
            if (getString(BasebizR.string.main_album_group_recommend_memories_title) != toolbar?.title) {
                updateToolbarEditTitle(
                    defaultTitleResId = BasebizR.string.base_title_select_image,
                    count = selectedCount
                )
            }
        }
    }

    override fun onEnterSelection() {
        super.onEnterSelection()
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener(object : ToolbarFadeInFadeOutAnimator.ToolbarAnimationListener {

                override fun onSwitch() {
                    updateSelectionTitleLater = false
                    updateToolbarEditTitle(
                        defaultTitleResId = BasebizR.string.base_title_select_image,
                        count = baseListViewModel?.getSelectedItemCount() ?: 0
                    )
                    refreshNetworkFloatingView()
                    activity?.invalidateOptionsMenu()
                }
            })
            start()
        }
    }

    override fun onExitSelection() {
        super.onExitSelection()
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener(object : ToolbarFadeInFadeOutAnimator.ToolbarAnimationListener {

                override fun onSwitch() {
                    updateSelectionTitleLater = true
                    toolbar?.apply {
                        title = getString(BasebizR.string.main_album_group_recommend_memories_title)
                    }
                    activity?.invalidateOptionsMenu()
                }
            })
            start()
        }

        bottomMenuBar?.menu?.apply {
            findItem(BasebizR.id.action_delete)?.let {
                if (it.isEnabled) {
                    it.isEnabled = false
                }
            }
        }
        refreshNetworkFloatingView()
    }

    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        super.onBottomMenuBarItemClicked(menuItem)
        when (menuItem.itemId) {
            BasebizR.id.action_delete -> {
                menuHelper?.doRecycleMemoryAlbumAction(
                    trackCallerEntry = TrackCallerEntry(
                        page = trackPage,
                        path = baseListViewModel?.trackPath,
                        albumsActionCurrentPage = getUserActionCurPage(TYPE_ALBUMS_ACTION)
                    ),
                    callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                )
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!isResumed) return false
        when (item.itemId) {
            BasebizR.id.action_create_memories -> {
                if (!ClickUtil.clickable()) return true
                // 新建图集时onPause不释放，避免回页面时滚动至顶出现灰图。
                baseListViewModel?.releaseInvisibleViewDataWhenPause = false
                menuEventNotifier = MenuOperation.EventNotifier()
                MenuOperationManager.doAction(
                    action = MenuAction.MANUAL_CREATE_MEMORY,
                    paramMap = MenuActionGetter.manualCreateMemory.builder
                        .setFragment(this@MemoriesAlbumSetFragment)
                        .setEventNotifier(menuEventNotifier)
                        .setTrackCallerEntry(TrackCallerEntry(trackPage, baseListViewModel?.trackPath))
                        .build(),
                    onCompleted = { result: String, resultMap: Map<String, Any>? ->
                        baseListViewModel?.releaseInvisibleViewDataWhenPause = true
                        if (result == MenuAction.RESULT_SUCCESS) {
                            // 成功新建回忆图集后滚到列表头部展示新增动画
                            recyclerView?.scrollToPosition(0)
                        }
                        AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                            memoryType = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CREATE_MEMORY_TYPE,
                            memoryName = resultMap?.get(AlbumsActionTackConstant.Key.ALBUMS_ACTION_ALBUM_NAME_KEY).toString(),
                            createMemoryImageCount = resultMap?.get(
                                AlbumsActionTackConstant.Key.ALBUMS_ACTION_CREATE_MEMORY_IMAGE_COUNT_KEY
                            ).toString(),
                            click = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CREATE_MEMORY_VALUE
                        )
                        menuEventNotifier = null
                    }
                )
            }
            BasebizR.id.memories_album_fragment_edit -> {
                AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                    enterSelect = ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE
                )
                enterSelectionMode()
            }
            BasebizR.id.action_cancel -> exitSelectionMode()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun createSidePaneListener(): ISidePaneListener {
        return this
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        super.onSidePaneSlideStart(newState)
        notifyConfigurationChanged(true)
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        if (config.windowWidth.isChanged()) {
            notifyConfigurationChanged(false)
        }
        if (config.orientation.isChanged()) {
            networkFloatingViewAsync.getIt {
                it.updateFloatingTipsMargin()
            }
        }
        config.onWidthChanged {
            menuEventNotifier?.sendEvent(MenuOperation.EventNotifier.EVENT_ID_SCREEN_CHANGED)
        }
    }

    private fun notifyConfigurationChanged(sidePaneChange: Boolean) {
        layoutDetail = refreshGridLayoutDetail()
        (recyclerView?.layoutManager as? GalleryGridLayoutManager)?.let {
            (layoutDetail as? GridLayoutDetail)?.replaceGaps((layoutDetail as GridLayoutDetail).itemHorizontalGaps)
            removeAllItemDecorations()
            addItemDecoration()
            it.spanCount = layoutDetail.spanCount
        }
        recyclerAdapter.let {
            if (sidePaneChange) {
                it.notifyItemRangeChanged(0, it.itemCount)
            } else {
                it.notifyDataSetChanged()
            }
        }
    }

    private fun refreshGridLayoutDetail(): GridLayoutDetail {
        val spanNum = getSpanCount()
        return GridLayoutDetail.HorizontalGapsBuilder().apply {
            parentWidth = getCurrentAppUiConfig().windowWidth.current
            edgeWidth = resources.getDimension(BasebizR.dimen.base_memories_album_set_fragment_item_view_horizontal_page_margin).toInt()
            gapWidth = resources.getDimension(BasebizR.dimen.base_memories_album_set_fragment_item_view_horizontal_gap).toInt()
            spanCount = spanNum
        }.build().apply {
            itemDecorationGapPx.top = 0f
            itemDecorationGapPx.bottom = resources.getDimension(BasebizR.dimen.base_memories_album_set_fragment_item_view_horizontal_gap)
        }
    }

    /**
     * 根据屏幕宽度计算列数
     * 1、屏幕宽度小于600dp-->1列
     * 2、屏幕宽度大于等于600dp小于840dp-->2列
     * 3、屏幕宽度大于等于840dp-->-->3列
     */
    private fun getSpanCount(): Int {
        return when {
            COUIResponsiveUtils.isSmallScreen(context, getContentWidth()) -> resources.getInteger(BasebizR.integer.main_memories_list_column_normal)
            COUIResponsiveUtils.isMediumScreen(context, getContentWidth()) -> resources.getInteger(BasebizR.integer.main_memories_list_column_medium)
            COUIResponsiveUtils.isLargeScreen(context, getContentWidth()) -> resources.getInteger(BasebizR.integer.main_memories_list_column_large)
            else -> resources.getInteger(BasebizR.integer.main_memories_list_column_normal)
        }
    }

    override fun getContentWidth(): Int {
        var slideWidth = 0
        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let {
                slideWidth = if (it.isFlat() && it.isOpen()) it.getSlideWidth() else 0
            }
        }
        return getCurrentAppUiConfig().windowWidth.current - slideWidth
    }

    override fun getUserActionCurPage(trackType: String?): String? {
        return when (trackType) {
            TYPE_ALBUMS_ACTION -> AlbumsActionTackConstant.Value.ALBUMS_ACTION_MEMORY_ALBUM_PAGE_VALUE
            else -> LaunchExitPopupConstant.Value.MEMORY_ALBUM_SET_PAGE
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        menuEventNotifier = null
    }

    override fun getPrivacyAuthorizeType() = AUTHORIZE_GET_USUAL_LOCATION

    override fun showPrivacyDialog(context: Context) {
        PermissionDialogHelper.showUsualLocationPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    activity?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
                        it.authorizePrivacy(AUTHORIZE_GET_USUAL_LOCATION)
                    }
                    NetworkPermissionManager.openNetwork(context)
                    refreshNetworkFloatingView()
                }
            })
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    companion object {
        private const val TAG = "MemoriesAlbumSetFragment"
        private const val SELECTED_SIZE_ONE = 1
    }
}