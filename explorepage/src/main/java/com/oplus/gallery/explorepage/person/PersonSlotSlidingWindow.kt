/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonSlotSlidingWindow.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/07/01
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_PERSON
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2021/07/01		1.0		OPLUS_FEATURE_PERSON
 *********************************************************************************/
package com.oplus.gallery.explorepage.person

import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.ICON_CSHOT
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.ICON_NULL
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_MEDIA_ICON_TYPE
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_REF_PATH
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_SELECTED
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.iconType
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.person.set.FaceItem
import com.oplus.gallery.business_lib.timeline.data.TimeViewData
import com.oplus.gallery.business_lib.timeline.viewmodel.SWConfig
import com.oplus.gallery.business_lib.timeline.viewmodel.SlotSlidingWindow
import com.oplus.gallery.business_lib.util.TimelineUtils.TIMELINE_TAG
import com.oplus.gallery.foundation.util.collections.ExtraMap
import com.oplus.gallery.framework.abilities.data.model.BaseModel

class PersonSlotSlidingWindow(
    swConfig: SWConfig,
    onModelGetter: () -> BaseModel<MediaItem>,
    defaultOrder: Boolean
) : SlotSlidingWindow(swConfig, onModelGetter, defaultOrder) {
    override val tag = "${TIMELINE_TAG}PersonSlotSlidingWindow#${swConfig.type}"

    override fun updateProperties(map: ExtraMap, item: MediaItem?) {
        item ?: return
        super.updateProperties(map, item)
        map.apply {
            val isSelected = (item as FaceItem).refItem?.path?.let { selectionModel?.isItemSelected(it) }
            this[SUPPORT_SELECTED] = isSelected == true
            this[SUPPORT_REF_PATH] = item.path.toString()
            // 人物详情列表页，连拍图标禁止显示
            this[SUPPORT_MEDIA_ICON_TYPE] = item.iconType().takeIf { it != ICON_CSHOT } ?: ICON_NULL
        }
    }

    override fun onCreateViewData(mediaItem: MediaItem, index: Int): TimeViewData {
        return super.onCreateViewData(mediaItem, index).apply {
            id = (mediaItem as FaceItem).refItem?.path.toString()
        }
    }

    override fun onOrderChanged(isPositiveOrder: Boolean) {
        super.onOrderChanged(isPositiveOrder)
        // 人物详情页采用数据反序方案
        model.setPositiveOrder(isPositiveOrder)
    }
}