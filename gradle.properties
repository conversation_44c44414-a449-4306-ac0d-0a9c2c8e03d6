# ---------------------------- 服务器编译配置 -------------------------
#Define oppo maven repositories url,generally no need to modify
prop_oppoMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/
prop_oppoTestMavenUrl=http://maven.scm.adc.com:8081/nexus/repository/nearme_os_public/
#Define support OAPM maven url
prop_oppoOapmUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/
#Jacoco mavem branch_io Url
prop_oppoMavenUrlBranchIO=http://nexus.os.adc.com/nexus/content/repositories/branch_io/
#Jacoco mavem snapshots Url
prop_oppoMavenUrlSnapshots=http://maven.scm.adc.com:8081/nexus/content/repositories/snapshots/
prop_oppoMavenUrlSnapshot=http://maven.scm.adc.com:8081/nexus/content/groups/snapshots/
prop_oppoMavenUrlSnapshots_nexus=http://nexus.os.adc.com/nexus/content/repositories/snapshots/
#Jacoco mavem snapshots Url
prop_oppoMavenUrlRelease=http://maven.scm.adc.com:8081/nexus/content/groups/stable-public/
# maven上传的地址
releasesRepoUrl=http://maven.scm.adc.com:8081/nexus/content/repositories/releases/
#目前智能裁剪SDK发布在一加maven，后续更新到软工发布的版本后移除
prop_oneplusMavenUrl=http://m2repo.onepluscorp.cn:8081/nexus/content/repositories/oneplus_share_libs
# 通过ars发布的sdk的maven地址
prop_sdkMavenUrlRelease=http://mirror-maven.myoas.com/repository/ars-sdk-release/
# Cloudkit SDK发布地址
prop_sdkNexusUrlRelease=http://nexus.os.adc.com/nexus/content/repositories/iss_official_releases

# 合影闭眼修复 SDK发布地址
prop_sdkNexusPublicUrlRelease=http://nexus.os.adc.com/nexus/content/groups/public/
prop_sdkNexusBasicUrlRelease=http://nexus.os.adc.com/nexus/content/repositories/basic_client_releases/

# realme AI去模糊SDK的maven地址
prop_realmeMavenUrlaonRelease=http://maven.scm.adc.com:8081/nexus/repository/aon-release/

# maven服务器账号
sonatypeUsername=swdp
# maven服务器密码
sonatypePassword=swdp
prop_appPrebuildSdkVersion=android-35
prop_decoupleSdk=true
prop_internalSdk=false
#Define APK name
prop_archivesBaseName=OppoGallery2
#Define versionCommit, used in AndroidManifest.xml
prop_versionCommit=12345678
#Define versionDate, used in AndroidManifest.xml
prop_versionDate=160701
#Define app disable resource subpackage,default is comment off!!!!
prop_disableSubPackage=false
#Define resource subpacage params,default is empty
#CI Server will introduce the correct params when build
#app can verify locally use params as below
#hdpi resource subpacakge params
#    prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,hdpi
#xhdpi resource subpacakge params
#    prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xhdpi
#xxhdpi resource subpacakge params
#    prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxhdpi
#2.5K resource subpacakge params
#    prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxxhdpi
#1080p with 2.5K resource subpacakge params
#    prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxhdpi,xxxhdpi
prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxhdpi
#服务器端会修改这个值为外销的所有语言
prop_exp_resConfig=zh_CN,en_US,zh_TW,es_MX,es_US,es_ES,es_CO,es_AR
#Define keystore file directory path,should use '\' escape on Windows
prop_keyPath=\\\\172.17.121.34\\rom_sdk\\release\\sdk\\rom_sdk\\keystore\\keystore
#######Don't modify below#####
prop_keyPassword=android
prop_storePassword=android
#####Don't modify above#####
#Define the APK Signature Scheme: v1 Scheme, enabled by default
prop_apkSignatureSchemeV1=true
#Define the APK Signature Scheme: v2 Scheme, enabled by default
prop_apkSignatureSchemeV2=true
#代码扫描时的编译命令，如果需要扫描其他变体，需要创建自定义任务集合，不能是assemble开头的命令
codeScanCommand=codeScanTask
#ocoverage插件不需要单元测试的module配置
blackProjects=foundation,compiler,annotations,abilityapt,features,:features:export,:features:gdpr,gdpr,export
# ----------------------------- gradle 配置 -----------------------------
prop_compileSdkVersion=android-35
#Define build tools version
prop_buildToolsVersion=35.0.0-rc1
#Define targetSdkVersion
prop_targetSdkVersion=35
#Define minSdkVersion
prop_minSdkVersion=31
#Define gradle plugin version
prop_gradleVersion=8.1.4
#指定服务器编译使用java 17
prop_targetCompatibility=17
#Define build-plugin version
innoBuildPluginVersion=1.7.0
#detekt
detekt_version=1.18.1
#Define OAPM version
prop_oppoOapmVersion=3.1.0
#Define renderscriptTargetApi version
prop_renderscriptTargetApi=19
# maven的groupId
mavenGroupName=com.oplus.gallery
# ------------------------------- 编译优化 -------------------------------
#of jvm memory depletion, setup this property could reslove it
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
android.injected.testOnly=false
#开启gradle并行编译，开启daemon，调整jvm内存大小
org.gradle.jvmargs=-Xmx16g -XX:MaxMetaspaceSize=4g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseParallelGC
org.gradle.parallel=true
org.gradle.workers.max=6
org.gradle.daemon=false
# obuild 1.6.6要求关闭此开关，否则jacocoReport执行不过
org.gradle.configureondemand=false
#开启gradle缓存
#为收集构建数据到Gradle Enterprise，用以分析项目构建行为，先禁用构建缓存，直到在后续优化时开启。
org.gradle.caching=true
#开启kotlin的增量和并行编译
kotlin.incremental=true
kotlin.incremental.java=true
kotlin.incremental.js=true
kotlin.caching.enabled=true
#非传递R，可以优化编译速度和apk大小
android.nonTransitiveRClass=true
#优化kapt
#并行运行kapt1.2.60版本以上支持
kapt.use.worker.api=true
#增量编译 kapt1.3.30版本以上支持
kapt.incremental.apt=true
#kapt avoiding 如果用kapt依赖的内容没有变化，会完全重用编译内容，省掉最上图中的:app:kaptGenerateStubsDebugKotlin的时间
kapt.include.compile.classpath=false
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# --------------------------- 引用的sdk的版本号 ---------------------------
# 测试相关
junitVersion=4.13.2
androidxJunitVersion=1.1.3
espressoVersion=3.4.0
testCoreVersion=1.5.0-alpha02
archCoreTestCoreVersion=2.1.0
mockitoCoreVersion=3.12.4
mockkVersion=1.12.7
robolectricVersion=4.7.3
googleTruthVersion=1.1.3
powermockVersion=2.0.9
fragmentTestingVersion=1.4.0
androidxExifVersion=1.2.0
kotlinxCoroutinesTestVersion=1.7.3
phenisFrameworkAndroidVersion=3.1.8
testRulesVersion=1.4.0
testRunnerVersion=1.4.0
uiautomatorVersion=2.2.0
mokitoAndroidVersion=3.12.4
otestPlatformTestLibVersion=1.7.2@aar
#Jacoco覆盖率相关
#Jacoco coverage version
jacocoVersionX=0.8.8
#Jacoco coverage version
coverageVersion=2.1.7@aar
#Jacoco coverage Inst plugin verison
coverageInstPluginVerison = 1.9.27-SNAPSHOT
#Jaoco required use OBuild Plugin
useOBuildPlugin=true

# 开发相关
androidToolsCommonVersion=27.1.3
commonsIoVersion=2.4
asmCommonsVersion=9.5
#Define KotlinVersion

prop_kotlin_version=1.9.23
kotlinxCoroutinesCore=1.7.3
kotlinCoroutinesAndroid=1.7.3
coreKtxVersion=1.12.0
graphicsCore=1.0.2-gallery-alphaa05cbf0-SNAPSHOT
playCoreVersion=1.10.3
featureDeliveryVersion=2.1.0
appcompatVersion=1.3.0
localbroadcastManagerVersion=1.1.0
multidexVersion=2.0.1
baiduLBSVersion=6.4.2
settingsLibVersion=2.0.2
feedbackVersion=15.0.4
feedbackEnvVersion=1.0.1
cloudConfigSdkVersion=3.2.1
cloudConfigSdkEnvVersion=3.2.1
materialVersion=1.11.0
constraintlayoutVersion=2.1.2
androidxAnnotationVersion=1.2.0
asyncLayoutInflaterVersion=1.0.0
googleAutoServceVersion=1.0-rc6
kotlinpoetVersion=1.4.0
synergysdkVersion=14.1.10
# 临时先用快照版本
routerPluginVersion=12.12.0-alphac32d820-SNAPSHOT
routerCompilerVersion=12.10.0001
routerAnnotationsVersion=12.11.0
# olive photo sdk version
oliveViewplayerVersion=1.0.8-alphaaf84050-SNAPSHOT
oliveEditorVersion=1.1.2-alphadab46cd-SNAPSHOT
oliveDecoderVersion=1.1.2-alphadab46cd-SNAPSHOT
dlnaVersion=1.0.4
wallpaperVersion=1.0.0
gsonVersion=2.10.1
viewpager2Version=1.0.0
prop_addonSdkVersion=16.10.0_preview
prop_lintPluginVersion=1.0.0
okioVersion=3.4.0
prop_uiSupportAppcompatVersion=16.0.2
prop_UIPrivacyPolicyVersion=1.0.2-alpha04
prop_uiSupportMaterialColorVersion=1.0.1
prop_supportSdkVersion=12.6.0
androidxPreferenceVersion=1.1.1
facebookReboundVersion=0.3.8
sqliteVersion=2.1.0
prop_exifVersion=1.0.12
thundersoftFrameworkVersion=1.0.0
thundersoftPolicyVersion=1.0.0
stdIdSdkVersion=1.0.8
openIdSdkVersion=1.0.8
clientIdSdkVersion=1.0.0
lifecycleLiveDataKtxVersion=2.4.0
lifecycleViewModelKtxVersion=2.4.0
lifecycleViewModelVersion=2.6.1
lifecycleProcessVersion=2.4.0
startupRuntimeVersion=1.2.0
recyclerviewVersion=1.2.0
fragmentKtxVersion=1.3.3
prop_market_token_version=4.0.0
okhttpVersion=3.12.0
stVideoFaceGroupVersion=1.0.1
stVideoSceneClassifyVersion=1.0.0
polarrAlbumVersion=1.0.6
probDetReleaseVersion=1.0.5
imageapiVersion=5.2.7
sauaarVersion=4.0.3
breakpadJniVersion=2.0.5
NativeMemoryVersion=1.0.2
OPAlgoCamSinglePortraitVersion=1.0.7
extendFileVersion=1.0.2
arcPortraitBlurVersion=3.0.18
arcPortraitBlurJarVersion=3.0.18
arcMpBaseVersion=1.0.2
sharedhardwarebuffer=1.0.0
aigcface=1.0.3-alphadf360b5-SNAPSHOT
javolutionVersion=5.5.1
faceAttributeDetectVersion=1.0.1
faceFdApiVersion=2.0.9
cvFaceApiVersion=2.5.2
jniCvFaceApiVersion=2.5.1
tblMediaInfoVersion=1.7.9PRO
TBLPlayerVersion=1.7.9PRO
syFaceDetectionApiVersion=1.7.0
jniSyFaceDetectionApiVersion=1.7.0
syFaceGroupVersion=1.7.9-beta-20250604.015628-1
dlnaSdkVersion=1.0.4
androidxCollectionKtxVersion=1.2.0
fileExtenderVersion=1.0.0
# 自适应投屏： HeyCast SDK提供手机投屏相关功能API， V13.0.0版本开始在原有投屏相关功能接口基础之上新增自适应投屏相关功能API
heycastVersion=13.0.3@jar
# 自适应投屏： 主要提供对外的功能接口，包括设备搜索，投屏
linkerSynergyCastkitSdkVersion=1.3.1@aar
# 自适应投屏： 主要是投屏引擎管理
linkerSynergyCoreSdkVersion=1.1.1
polarrEditorVersion=1.5.34-Alpha
arcSpotlightVersion=1.0.11
tencentDocVersion=1.3.3.24
tencentYTCommonVersion=1.3.3.24
downloadsVersion=2.0.0
AppFeatureHelperVersion=1.0.2

# 美摄SDK
meicamSdkVersion=3.10.3.58-Alpha
cloudBaseVersion=1.2.0
cloudFullAmount=1.5.8
cloudShareAlbum=2.0.1
addonAdapterVersion=11.2.5
sysApiSdkVersion=13.0.3
tinkVersion=1.4.0
colorConvertlib=1.0.1
heytapStatisticTool=1.0.1
heytapClientIdVersion=1.0.20
heytapPaySdkCnVersion=2.1.2-cn
heytapPaySdkOsVersion=2.1.2-os
backupSdkVersion=2.0.3
wireRuntimeVersion=3.5.0
prop_roomVersion=2.4.0
pixelateSdkVersion=3.0.3-SNAPSHOT
androidxLoaderVersion=1.1.0
heifWriterVersion=1.0.0
pushSdkVersion=3.0.0
accountSdkVersion=1.3.1
adobeXmpCoreVersion=5.1.3
appproviderSettingsCompatVersion=2.1.1
# 埋点sdk版本
staticticsVersion=3.0.12
trackinglibVersion=1.0.3
#美研视频标签库版本
ouaVideoClassifierVersion=2.0.11
aiBoostVersion=4.2.10_gallery_aar3
superTextVersion=15.0.1-alphadc72d96-SNAPSHOT
prop_anLintVersion=27.0.2

#AIUnit core AIUnit 抠图、合影等使用
aiUnitVisionVersion=1.4.5-alphab3a4eba-SNAPSHOT
aiUnitCoreVersion=2.0.2-alpha15c8f08-SNAPSHOT
aiUnitToolkitsVersion=2.0.2-alpha15c8f08-SNAPSHOT
aiUnitDownloadVersion=2.0.2-alpha15c8f08-SNAPSHOT
#gradle可能会集成到1.4.3的其他哈希后缀的版本，所以在版本号升级到高于1.4.3前需要保留“!!”后缀强制指定版本
aiUnitEraseVersion=1.4.8-alpha47184e7-SNAPSHOT
ocrSdkVersion=2.0.0-beta4b86934-SNAPSHOT
aiUnitPetVersion=1.1.1-alphacdb2f81-SNAPSHOT

eliminateVfxSdkVersion=1.1.2-alpha3ef47f2-SNAPSHOT
aiUnitPixelateVersion=1.1.0-alpha8258ab1-SNAPSHOT
#realme AI去模糊算法SDK
aiUnitMeowaiVersion=2.0.13
#影像下变化算法sdk
nativeBridgeApi=1.0.50

#编辑能力算法插件池sdk 新增算法 parameter valueType 和 extras
editorPlugin_pool=2.1.0-alpha32ed9bd-SNAPSHOT
editorPlugin_common=2.1.2-alphae72c8c1-SNAPSHOT
editorPlugin_foundationBasicTone=2.1.4-alphaccba333-SNAPSHOT
editorPlugin_foundationIpu=2.1.4-alphaccba333-SNAPSHOT
editorPlugin_ipuFilter=2.1.4-alphaccba333-SNAPSHOT
editorPlugin_foundationMeicam=2.1.0-alpha32ed9bd-SNAPSHOT
editorPlugin_watermark=2.2.0-alphab9d4b52-SNAPSHOT
editorPlugin_aiEliminate=2.0.13-alpha526177c-SNAPSHOT
editorPlugin_magicEliminate=2.0.0master-alpha48b4027-SNAPSHOT
editorPlugin_blur=2.1.0-alphade16e0b-SNAPSHOT
editorPlugin_filter=2.1.0-alpha32ed9bd-SNAPSHOT
editorPlugin_transform=2.2.1-alpha04c4e02-SNAPSHOT
editorPlugin_aiRepair=2.0.2-alpha028cccc-SNAPSHOT
editorPlugin_imageQualityEnhance=2.0.2-alpha028cccc-SNAPSHOT
editorPlugin_aiComposition=2.0.2-alpha8c862f7-SNAPSHOT
editorPlugin_adjustment=2.1.0-alpha32ed9bd-SNAPSHOT
editorPlugin_beauty=2.1.0-alpha6ea7d5c-SNAPSHOT
editorPlugin_ipubeauty=2.0.2-alpha8fba7b9-SNAPSHOT
editorPlugin_photoFrame=2.0.0master-alpha4199fd2-SNAPSHOT
editorPlugin_textureBlend=2.0.2-alpha08cc672-SNAPSHOT
editorPlugin_aiWatermark=2.0.2-alphaffc8bb9-SNAPSHOT
editorPlugin_repair=2.0.0master-alpha4199fd2-SNAPSHOT
editorPlugin_mosaic=2.0.3-alpha1dcdb6f-SNAPSHOT
editorPlugin_aihd=2.0.5-alpha264e557-SNAPSHOT
editorPlugin_imageReplace=2.1.0-alpha32ed9bd-SNAPSHOT
editorPlugin_aigraffiti=2.0.2-alpha8a77ffb-SNAPSHOT
editorPlugin_aibesttake=2.0.2-alphab490e25-SNAPSHOT
editorPlugin_slowmotion=2.0.2HU

#抠图特效sdk版本
lnsVfxSdkVersion=1.0.4-beta4ab7907-SNAPSHOT

#合影1.5闭眼修复SDK版本
groupPhotoBestTakeVersion=1.0.12

#Google相册备份SDK
googleBackupSdkVersion=1.1.3-Alpha

#Olint代码检查
olintVersion=1.2.3

#自研消除笔SDK版本
inpaintingComponentVersion=1.6.2

#多模态搜索[图文检索SDK]
slpClipVersion = 4.1.0-083ace9b-SNAPSHOT

#tapHttp库版本，用于联网相关，内置了com.squareup.okhttp3源码
tapHttpVersion=*******
#Cloudkit SDK库版本
cloudkitSdkVersion=2.3.22
#heytap测试版本
heytapTestVersion=0.0.7
#共享图集引入库
retrofitVersion = 2.7.0
#glide版本
glideVersion = 4.12.0
#微信sdk版本
wechatSdkVersion = 6.8.0
#联系人common_sdk，用于选择联系人数据
colorosContactsCommonVersion = 1.0.0
oplusContactsCommonVersion = 1.1.0

#手写笔SDK版本
doodleSdkVersion=5.0.17
ipeSdkVersion =2.0.0beta1
#google guava库版本,Guava工程包含了若干被Google的 Java项目广泛依赖的核心库:集合、缓存、并发、I/O等。
guavaVersion=33.3.1-android
#osdk库版本, 有很多接口是直接集成在系统里面的，应用这边拿不到，编译会通不过，通过这个包可以保证编译通过。
#osdkVersion=*********-preview
osdkVersion=*********-preview@aar

#Local Hdr Rus决策jni库版本
localHdrRusVersion=1.0.3
uHdrUtilVersion=1.0.3

#相机提供的用于写iccProfile的so库
libIccProfileVersion = 1.0.3
libApsultrahdrVersion = 1.0.0
libcPlusVersion = 1.0.3
libJpegVersion = 2.0.7

# ai widgets sdk
aiWidgetsVersion = 1.0.1-20250412.014115-37

hdrTransformVersion = 1.0.1

#dmp-sdk分词
dmpSdkVersion = 2.3.10
#记忆大师-问答控件
aiAskUIVersion = 1.1.4
#sqlcipher数据库加密版本号
sqlcipherVersion = 4.5.3
#cv图谱数据库解密sdk版本号
cvcipherVersion = 1.0.1
#安盾sdk
cryptoAndroid = 2.1.1
#融合搜索Kit
andesSearchKitVersion=2.1.4
# -------------------------- Gallery Sdk 版本号 ----------------------------
gstartupLibVersion=1.0.0
uiLibVersion=12.5.2
busLibVersion=12.5.2
businessLibVersion=12.5.2
cloudsyncLibVersion=12.5.2
collageLibVersion=12.5.2
mediadbsyncLibVersion=12.5.2
photoeditorLibVersion=12.5.2
routerLibVersion=12.5.2
routerLibAnnotationsVersion=12.11.0
routerLibCompilerVersion=12.5.2
routerLibPluginVersion=12.12.0
dfmResPluginVersion=1.0.0
scanLibVersion=12.5.2
standardLibVersion=12.5.2
additionLibVersion=12.5.2
effectLibVersion=12.5.2
mainLibVersion=12.5.2
pictureLibVersion=12.5.2
recyclebinLibVersion=12.5.2
exifLibVersion=12.5.3
transformLibVersion=12.5.2
frameextractorLibVersion=12.5.2
foundationLibVersion=12.5.2
frameworkLibVersion=12.5.2
abilityapiLibVersion=1.0.7
colormanagementLibVersion=12.5.2
hardwareLibVersion=12.5.2
resourcingLibVersion=12.5.2
#Olive1.0编辑后选帧放大SDK版本
updatescalebitmapVersion=1.0.5-alpha7ea83e6-SNAPSHOT
#Olive2.0封面增强SDK版本
olivecoverenhancer_version=2.02.02-beta01-SNAPSHOT
#Olive2.0 xdr封面SDK版本
olivecoverinversetmc_version=1.0.0-alpha2699bc5-SNAPSHOT
#markby dingyong 合入master测试完成后，再更新正式版本
photopagerLibVersion=1.3.0-alpha591414d-SNAPSHOT
widgetLibVersion=12.5.2
cardwidgetVersion=2.0.5
smartenginehelperVersion=1.0.23
protobufJavaVersion=3.23.3
retrofit2ConverterProtobufVersion=2.2.0
#转码使用文档：https://odocs.myoas.com/docs/5rk9dzZ1OdFe7wqx?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI4MDM3NTYwNSIsImlzcyI6IlRHVC05NzEyNjgteGV3bVkxMVhuTnpHNXZadkh1TUZrMjVEVmVsb1hSb2JsN24yMjB6SVNGMVBweVlhQ3ktU0lBTSIsIm5hbWUiOiJTSUFNVEdUIiwidHlwZSI6IlRHVCIsImlhdCI6MTY2MzgzNDU2NX0.RUdPHTxXly-vTB69pCSqNgNgjE0PVogatCZfEFJiFxw&tt_lang=zh-CN&tt_zone=%2B8&new_client=1&ticket=APPURLWITHTICKETd603fd917fc5389ebd4c4e8076eb0c4d&client_id=100000&expire_time=1663838165062&msgShowStyle=31
#tblcreator sdk
tblcreatorTranscoder=1.6.19
cvCropVersion=1.2.0
#media3播放器:谷歌云需求引入
media3SessionVersion=1.0.0
#ipu版本
ipuVersion=4.101.107
#ocs display version
ocsDisplayVersion=1.0.0
#ocs base version
ocsBaseVersion=1.0.16-betaa1731df-SNAPSHOT
#PTC(OAF NFC)version
pantaConnectVersion=1.0.2
#------------------gradle enterprise插件版本---------------------------------
gradleEnterpriseVersion=3.10.2
customUserDataGradlePlugin=1.7.2
# ------------------------------ 自动签名 ----------------------------------
# false：使用maven中的lib，每次编译会重新下载；true：使用本地缓存的lib，重点：当本地没有缓存的lib或lib版本不对时，需要先设置为false编译一次，缓存lib
compilingLocalLibs=false
# 使用Oapm插件
useOapm=true
# 使用自动签名
useAutoSign=false
autoSignUsername=00000000
# your password
autoSignPassword=xxxxxxxx
# app signature
autoSignSignature=oppo_data_app_std
# app in phone's sign
autoSignVersion=single
# V2为新平台签名标识
autoSignApiVer=V2
# phone platform
autoSignPlatform=SM6125_10.0
# sign brand
autoSignBrand=PSW
# sign project number
autoSingProjectNum=19328
#=============================================================================#

versionSuffix=
mainVersionName=16.0.1
# 当前版本号为8位，如果位数有增减，同步到[HttpHeader.VERSION_CODE_LENGTH]
mainVersionCode=16000001

#本地单模块编译的配置
prop_cacheMavenUrl=http://mirror-maven.myoas.com/repository/ars-sdk-snapshot/
#预置变量，插件会动态修改该值，进行maven编译，开发不需要关注。如果需要使用本地maven，需要修改该值为true。
buildForCache=false
#使用本地maven缓存，本地一直为true，提交代码的时候需要是false。可以再.gradle目录，放一个gradle.properties。声明useMavenCache为true，就会覆盖改值
useLocalMaven=false
#使用远程maven缓存（master分支），本地一直为true，提交代码的时候需要是false。可以再user根目录，放一个gradle.properties。声明useMavenCache为true，就会覆盖，就会覆盖改值
useRemoteMavenCache=false
#缓存的版本号，固定为这个，极小，保证不会影响正式版本
versionMavenCache=0.0.1_alphacache
changeProjectList=:app
#用于插件资源引用的排查和自动替换，应该仅在个人开发时开启
enableDfmResPlugin=false
#end
#设置需要启动响应的module
launchModule=app
#是否启用本地app bundle
localAppBundle=false
#coe动效sdk
vfxsdkversion=1.1.3
#targetsdk35默认值改变
android.defaults.buildfeatures.buildconfig = true
android.defaults.buildfeatures.aidl = true
android.defaults.buildfeatures.renderscript = true
android.nonFinalResIds = false
android.enableR8.fullMode = false
android.suppressUnsupportedCompileSdk=35
#补光算法
editorPlugin_aiLighting=2.0.2-alpha583c307-SNAPSHOT
#DeepThinker
DTSdkVersion=2.12.2