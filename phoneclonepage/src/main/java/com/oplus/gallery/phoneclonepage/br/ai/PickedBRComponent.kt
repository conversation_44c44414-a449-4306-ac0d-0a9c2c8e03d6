/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PickedBRComponent.kt
 ** Description: local表中的精选字段搬家模块，涉及quality_score，quality_version，crop_rect，crop_rect_version共4个字段
 ** Version: 1.0
 ** Date: 2023/03/23
 ** Author: houdonggu@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  houdonggu@Apps.Gallery3D    2023/03/23        1.0         build this module
 *************************************************************************************************/

package com.oplus.gallery.phoneclonepage.br.ai

import android.annotation.SuppressLint
import android.content.Context
import android.database.Cursor
import com.google.gson.reflect.TypeToken
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.ConstantUtils.ASTERISK
import com.oplus.gallery.foundation.database.util.ConstantUtils.DISTINCT
import com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_TO
import com.oplus.gallery.foundation.database.util.ConstantUtils.FROM
import com.oplus.gallery.foundation.database.util.ConstantUtils.IS_NULL
import com.oplus.gallery.foundation.database.util.ConstantUtils.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.ConstantUtils.LESS_EQUAL_THAN_ZERO
import com.oplus.gallery.foundation.database.util.ConstantUtils.OR
import com.oplus.gallery.foundation.database.util.ConstantUtils.ORDER_BY
import com.oplus.gallery.foundation.database.util.ConstantUtils.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.ConstantUtils.SELECT
import com.oplus.gallery.foundation.database.util.ConstantUtils.WHERE
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.DatabaseUtils.projectionsToString
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.phoneclonepage.br.bean.BaseAIBRData
import com.oplus.gallery.phoneclonepage.br.bean.PickedBRData
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.lang.reflect.Type

/**
 * 精选年月日视图的数据搬家，主要涉及local_media表中的quality_score，quality_version，crop_rect，crop_rect_version共4个字段
 * a搬到b，在恢复数据时，只有更新，因为local_media表不能通过搬家插入，需要先扫描媒体库获取到local_media表，local_media表必然是完整的，那么只需要后续判断是否更新质量分和裁剪区域
 * 算法字段的统一更新策略：
 * a的字段version高于b的字段version（包含b没有和b版本低）时，把a的字段值和字段version搬到b
 */
class PickedBRComponent(existFileMap: MutableMap<String, Boolean>) : BaseAIBRComponent(existFileMap) {

    override val tag: String = TAG

    override val brFolder = BR_FOLDER_PICKED

    override val tableType = GalleryDbDao.TableType.LOCAL_MEDIA

    override val querySelect = getAllScanPickedSelect()

    override val mainKey =
        GalleryStore.GalleryColumns.LocalColumns.DATA

    override val isRecoveryInMediaSyncEnd: Boolean = true

    /**
     * sql解读：挑选出local_media表的所有item的所有字段进行备份
     * select * 全部字段
     * from local_media 相似特征表
     * order by _data 以_data字段进行排序
     */
    private fun getAllScanPickedSelect(): String {
        return StringBuilder()
            .append(SELECT)
            .append(ASTERISK)
            .append(FROM)
            .append(GalleryDbDao.TableType.LOCAL_MEDIA)
            .append(ORDER_BY)
            .append(GalleryStore.GalleryColumns.LocalColumns.DATA)
            .toString()
    }

    override fun getComponentVersion(context: Context): Int = PICKED_COMPONENT_VERSION

    override fun getDataByCursor(cursor: Cursor): BaseAIBRData? {
        val pickedBRData = PickedBRData()
        pickedBRData.initByCursor(cursor)
        if (pickedBRData.updateFieldByFile(pickedBRData.data)) return pickedBRData
        return null
    }

    override fun parseDataList(jsonStr: String, existSet: HashSet<String>): Map<String, List<BaseAIBRData>> {
        val listType: Type = object : TypeToken<List<PickedBRData>>() {}.type
        val all = JsonUtil.fromJson<List<PickedBRData>>(jsonStr, listType)
        GLog.d(TAG, LogFlag.DL, "parseDataList $brFolder ${all.size}")
        val syncMap: MutableMap<String, MutableList<PickedBRData>> = mutableMapOf()
        all?.forEach {
            it.mainValue = it.data
            if (it.mainValue.isNullOrEmpty()) {
                if (DEBUG) {
                    GLog.d(TAG, LogFlag.DL, "parseDataList $brFolder mainValue is empty: $it")
                }
            } else {
                if (syncMap.containsKey(it.mainValue)) {
                    syncMap[it.mainValue]?.add(it)
                } else {
                    it.mainValue?.apply {
                        syncMap[this] = mutableListOf(it)
                    }
                }
            }
        }
        return syncMap
    }

    override fun syncToLocalDB(list: List<BaseAIBRData>): Array<BatchResult> {
        val operations: MutableList<UpdateReq> = ArrayList()
        val existMap = getExistMainValueMap()
        for (aiBRData in list) {
            val newValue = (aiBRData as? PickedBRData)?.getContentValuesByExist(existMap)
            val updateReq = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setTableType(tableType)
                .setWhere(mainKey + EQUAL_TO)
                .setWhareArgs(arrayOf(aiBRData.mainValue))
                .setConvert { newValue }
                .build()
            operations.add(updateReq)
        }
        // batch to update backup label DB
        val results = BatchReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .addDataReqs(operations)
            .build().exec()
        notifyChange()
        GLog.d(TAG, LogFlag.DL, "updateToLocalDB  brFolder = $brFolder operations.size:${operations.size} results.size:${results?.size}")
        return results
    }

    @SuppressLint("Range")
    private fun getExistMainValueMap(): HashMap<String, PickedBRData> {
        val map = HashMap<String, PickedBRData>()
        val rawQueryReq = RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(tableType)
            .setQuerySql(getAllExistNeedUpdatePickedSelect())
            .setSqlArgs(null)
            .setConvert(CursorConvert())
            .build()
        runCatching {
            DataAccess.getAccess().rawQuery(rawQueryReq)?.use {
                while (it.moveToNext()) {
                    val mainValue = it.getString(it.getColumnIndex(mainKey))
                    val pickedBRData = PickedBRData()
                    pickedBRData.loadExistValuesByCursor(it)
                    if (DEBUG) {
                        GLog.d(TAG, LogFlag.DL, "getExitMainValueMap $mainValue $pickedBRData")
                    }
                    map[mainValue] = pickedBRData
                }
            }
        }.onFailure {
            GLog.w(TAG, LogFlag.DL, "getExitMainValueMap, Exception = $it")
        }
        return map
    }

    /**
     * sql解读：挑选出local_media表的所有item的_data、quality_version、crop_rect_version
     * 条件是quality_version字段空或小于等于0、crop_rect_version字段空或小于等于0
     * 在恢复数据时进行版本判断比较
     * select _data, quality_version, crop_rect_version
     * from local_media 相似特征表
     * where (quality_version is null
     * or quality_version <= 0
     * or crop_rect_version is null
     * or crop_rect_version <= 0)
     * order by _data 以_data字段进行排序
     */
    private fun getAllExistNeedUpdatePickedSelect(): String {
        return StringBuilder().append(SELECT)
            .append(DISTINCT)
            .append(projectionsToString(ALL_PICKED_KEY_VERSION))
            .append(FROM)
            .append(GalleryDbDao.TableType.LOCAL_MEDIA)
            .append(WHERE)
            .append(LEFT_BRACKETS)
            .append(GalleryStore.GalleryColumns.LocalColumns.QUALITY_VERSION).append(IS_NULL)
            .append(OR)
            .append(GalleryStore.GalleryColumns.LocalColumns.QUALITY_VERSION).append(LESS_EQUAL_THAN_ZERO)
            .append(OR)
            .append(GalleryStore.GalleryColumns.LocalColumns.CROP_RECT_VERSION).append(IS_NULL)
            .append(OR)
            .append(GalleryStore.GalleryColumns.LocalColumns.CROP_RECT_VERSION).append(LESS_EQUAL_THAN_ZERO)
            .append(RIGHT_BRACKETS)
            .append(ORDER_BY)
            .append(GalleryStore.GalleryColumns.LocalColumns.DATA)
            .toString()
    }

    override fun notifyChange() {
        ContextGetter.context.contentResolver.notifyChange(GalleryStore.GalleryMedia.getContentUri(), null)
    }

    companion object {
        private const val TAG = "PickedBRComponent"
        private const val BR_FOLDER_PICKED = "/Gallery/Picked/"
        private const val PICKED_COMPONENT_VERSION = 1

        private val ALL_PICKED_KEY_VERSION = arrayOf(
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            GalleryStore.GalleryColumns.LocalColumns.QUALITY_VERSION,
            GalleryStore.GalleryColumns.LocalColumns.CROP_RECT_VERSION
        )
    }
}