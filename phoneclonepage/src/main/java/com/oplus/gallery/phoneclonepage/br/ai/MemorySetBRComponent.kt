/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MemorySetBRComponent.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/25
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/25    1.0              build this module
 *************************************************************************************************/
package com.oplus.gallery.phoneclonepage.br.ai

import android.content.Context
import android.database.Cursor
import android.text.TextUtils
import com.google.gson.reflect.TypeToken
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.gallery.business_lib.helper.UserAssetsTrackHelper
import com.oplus.gallery.phoneclonepage.br.bean.BaseAIBRData
import com.oplus.gallery.phoneclonepage.br.bean.MemorySetBRData
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.ConstantUtils
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.util.ext.parseId
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.phoneclonepage.br.bean.PhoneCloneConstant
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import org.json.JSONObject
import java.lang.reflect.Type

class MemorySetBRComponent(existFileMap: MutableMap<String, Boolean>) : BaseAIBRComponent(existFileMap) {

    companion object {
        private const val TAG = "MemorySetBRComponent"
        const val BR_FOLDER_MEMORY_SET = "/Gallery/MemorySets/"
        const val MEMORY_SET_COMPONENT_VERSION = 1
        const val TRACK_MEMORYSET_COUNT = "track_memoryset_count"
    }
    override val tag: String = TAG

    private var insertIdMap = HashMap<Long, Long>()

    override val brFolder = BR_FOLDER_MEMORY_SET

    override val tableType = GalleryDbDao.TableType.MEMORIES_SET

    override val querySelect = getAllMemorySetSelect()

    override val mainKey = GalleryStore.MemoriesSetColumns.TAKEN

    override fun getComponentVersion(context: Context): Int {
        return MEMORY_SET_COMPONENT_VERSION
    }

    private fun getAllMemorySetSelect(): String {
        return ConstantUtils.SELECT + ConstantUtils.ASTERISK + ConstantUtils.FROM +
                GalleryDbDao.TableType.MEMORIES_SET + ConstantUtils.ORDER_BY + GalleryStore.MemoriesSetColumns.NAME
    }

    override fun getDataByCursor(cursor: Cursor): BaseAIBRData? {
        val memorySetBRData = MemorySetBRData()
        memorySetBRData.initByCursor(cursor)
        if (DEBUG) {
            GLog.d(TAG, LogFlag.DL, "getDataByCursor $memorySetBRData")
        }
        return memorySetBRData
    }

    override fun backup(context: Context, rootPath: String, plugin: AbstractPlugin): String {
        super.backup(context, rootPath, plugin)
        return MemorySetMapBRComponent(null, existFileMap).backup(context, rootPath, plugin)
    }

    override fun restore(context: Context, rootPath: String, plugin: AbstractPlugin): String {
        super.restore(context, rootPath, plugin)
        return MemorySetMapBRComponent(null, existFileMap).restore(context, rootPath, plugin)
    }

    override fun restoreFromFile(context: Context, rootPath: String): Boolean {
        insertIdMap.clear()

        super.restoreFromFile(context, rootPath)

        MemorySetMapBRComponent(insertIdMap, existFileMap).restoreFromFile(context, rootPath)
        return true
    }

    override fun needInsert(baseAIBRData: BaseAIBRData, existSet: Set<String>): Boolean {
        return !(TextUtils.isEmpty(baseAIBRData.mainValue) || existSet.contains(baseAIBRData.mainValue))
    }

    override fun parseDataList(jsonStr: String, existSet: HashSet<String>): Map<String, MutableList<BaseAIBRData>> {
        return mutableMapOf()
    }

    /**
     * 精彩回忆集合不需要按照taken或者路径分类，直接解析数据即可，taken一致则不插入数据
     */
    override fun insertToLocalDBByJSONArray(jsonStr: String, existSet: HashSet<String>) {
        val time = System.currentTimeMillis()
        val listType: Type = object : TypeToken<List<MemorySetBRData>>() {}.type
        val all = JsonUtil.fromJson<List<MemorySetBRData>>(jsonStr, listType)
        val infoList: MutableList<BaseAIBRData> = mutableListOf()
        all?.forEach {
            it.mainValue = it.taken?.toString()
            if (needInsert(it, existSet)) {
                infoList.add(it)
            }
        }
        if (infoList != null) {
            if (DEBUG) {
                infoList.forEach {
                    GLog.d(TAG, LogFlag.DL, "insertToLocalDBByJSONArray insertInfo : $it")
                }
            }
            if (infoList.isNotEmpty()) {
                syncToLocalDB(infoList)
            }
            /**
             * 埋点恢复数据
             */
            UserAssetsTrackHelper.trackPhoneClone(
                TAG,
                true,
                PhoneCloneConstant.PHONE_CLONE_RESTORE,
                getTrackData(infoList.size)
            )
            GLog.d(TAG, LogFlag.DL, "insertToLocalDBByJSONArray ${infoList.size} ${GLog.getTime(time)}")
        }
    }

    /**
     * 插入数据库后，需要保存对应关系，旧的id、新的id
     */
    override fun syncToLocalDB(list: List<BaseAIBRData>): Array<BatchResult> {
        val results = super.syncToLocalDB(list)
        for (i in list.indices) {
            val data = list[i] as MemorySetBRData
            val oldId = data.id
            val newId = results[i].uri.parseId()
            if (DEBUG) {
                GLog.d(TAG, LogFlag.DL, "insertToLocalDB ids: $oldId $newId")
            }
            oldId?.apply {
                if (DEBUG) {
                    GLog.d(TAG, LogFlag.DL, "insertToLocalDB $oldId $newId")
                }
                insertIdMap[oldId] = newId
            }
        }
        return results
    }

    override fun notifyChange() {
        ContextGetter.context.contentResolver.notifyChange(GalleryStore.MemoriesSet.getContentUri(), null)
    }

    /**
     * 获取埋点数据
     */
    private fun getTrackData(fileCount: Int): String {
        val trackJsonObject = JSONObject()
        trackJsonObject.put(TRACK_MEMORYSET_COUNT, fileCount)
        return trackJsonObject.toString()
    }
}