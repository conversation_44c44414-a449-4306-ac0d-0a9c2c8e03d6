/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EditInfoBRComponent.kt
 ** Description : 编辑搬家组件
 ** Version     : 1.0
 ** Date        : 2024/6/22
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D          2024/6/22    1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.phoneclonepage.br

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.helper.UserAssetsTrackHelper
import com.oplus.gallery.business_lib.photoeditor.ParametricDataEntity
import com.oplus.gallery.business_lib.photoeditor.ParametricProjectDataHelper
import com.oplus.gallery.foundation.database.store.GalleryStore.EditInfoColumns
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.framework.abilities.parameterization.ParametricConst
import com.oplus.gallery.phoneclonepage.br.BRComponentConst.DEFAULT_COMPONENT_PREVIEW_COUNT
import com.oplus.gallery.phoneclonepage.br.bean.BRConfigEntity
import com.oplus.gallery.phoneclonepage.br.bean.BRErrorCode.IO_EXCEPTION
import com.oplus.gallery.phoneclonepage.br.bean.BRErrorCode.SUCCESS
import com.oplus.gallery.phoneclonepage.br.bean.PhoneCloneConstant
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import org.json.JSONObject

/**
 * 备份和恢复内容：
 * 1.私有目录编辑文件
 * 2.编辑db
 * 3.编辑搬家配置文件
 *
 * 备份：
 * 私有目录编辑文件->编辑db->编辑搬家配置文件
 * 中断处理：每一步失败即退出
 * 取消处理：备份编辑图片cancel即退出
 *
 * 恢复：
 * 编辑搬家配置文件->私有目录编辑文件->编辑db
 * 中断处理：每一步失败即退出
 * 取消处理：恢复编辑图片cancel即退出
 *
 * 通过编辑搬家配置文件的顺序设计，只要备份没彻底完成，恢复就不会执行
 */
class EditInfoBRComponent() : BaseBRComponent() {

    private var isCanceled: Boolean = false
    private var backupDataCacheList: List<ParametricDataEntity>? = null
    private var restoreDataCacheList: List<EditInfoBRData>? = null

    override fun backup(context: Context, rootPath: String, plugin: AbstractPlugin): String {
        runCatching {
            // 获取合法记录，并校验记录中私有目录文件是否存在
            val startTime = System.currentTimeMillis()
            val dataList = getDataListForBackup() ?: run {
                GLog.d(TAG) { "backup, no data need backup, return" }
                return SUCCESS
            }
            GLog.d(TAG) { "backup, costTime=${System.currentTimeMillis() - startTime}" }

            // 备份文件，花的时间较长，可能被取消，取消直接结束
            backupFile(rootPath, plugin, dataList)
                .takeIf { it != ProcessResult.SUCCESS }
                ?.let {
                    if (it == ProcessResult.CANCEL) {
                        /**
                         * 埋点备份文件取消
                         */
                        UserAssetsTrackHelper.trackPhoneClone(
                            TAG,
                            false,
                            PhoneCloneConstant.PHONE_CLONE_BACK_UP,
                            PhoneCloneConstant.PHONE_CLONE_CANCELED_PROGRESSED
                        )
                    } else if (it == ProcessResult.NO_PENDING_DATA) {
                        /**
                         * 埋点备份文件无待备份
                         */
                        UserAssetsTrackHelper.trackPhoneClone(
                            TAG,
                            false,
                            PhoneCloneConstant.PHONE_CLONE_BACK_UP,
                            PhoneCloneConstant.PHONE_CLONE_NO_PENDING_DATA
                        )
                    } else {
                        /**
                         * 埋点备份文件失败
                         */
                        UserAssetsTrackHelper.trackPhoneClone(
                            TAG,
                            false,
                            PhoneCloneConstant.PHONE_CLONE_BACK_UP,
                            PhoneCloneConstant.PHONE_CLONE_MESSAGE_FAIL
                        )
                    }
                    GLog.i(TAG, "backup, backup file result = $it, return")
                    return SUCCESS
                }

            backupDatabase(rootPath, plugin, dataList)
            backupConfig(rootPath, plugin)
            /**
             * 埋点备份文件成功
             */
            UserAssetsTrackHelper.trackPhoneClone(
                TAG,
                true,
                PhoneCloneConstant.PHONE_CLONE_BACK_UP,
                getTrackData(dataList, TRACK_BACKUP_FILE_COUNT)
            )
        }.onFailure {
            GLog.d(TAG, "backup, failed! error=", it)
            return IO_EXCEPTION
        }
        return SUCCESS
    }

    /**
     * 获取埋点数据
     */
    private fun <T> getTrackData(dataList: List<T>, jsonKey: String): String {
        val trackJsonObject = JSONObject()
        trackJsonObject.put(jsonKey, dataList.size)
        return trackJsonObject.toString()
    }


    private fun getDataListForBackup(): List<ParametricDataEntity>? {
        return backupDataCacheList?.toList()
        // 获取合法记录
            ?: ParametricProjectDataHelper.queryAllValidProject()
                .also { GLog.d(TAG) { "getDataListForBackup, db valid data List = ${it.size}" } }
                // 校验记录文件是否存在（彻底删除的文件记录还存在）
                .filter {
                    it.isDataFileExist() && it.isOriginalFileExist() && (it.hasContentEditFile().not() || it.isContentEditFileExist())
                }
                .takeIf { it.isNotEmpty() }
                ?.also { backupDataCacheList = it }
                .also { GLog.d(TAG) { "getDataListForBackup, file valid data list = ${it?.size}" } }
                ?.toList()
    }

    /**
     * 备份文件
     */
    private fun backupFile(rootPath: String, plugin: AbstractPlugin, dataList: List<ParametricDataEntity>): ProcessResult {
        // 获取文件列表
        val fileList = dataList.flatMap { listOf(it.getOriginalFilePath(), it.getContentEditFilePath()) }
            .toSet()
            .filter { it.isNullOrEmpty().not() }
            .map { File(it) }
            .takeIf { it.isNotEmpty() } ?: run {
            GLog.d(TAG, "backup, fileList is null, no need backup, return")
            return ProcessResult.NO_PENDING_DATA
        }

        // 备份文件
        GLog.d(TAG) { "backupFile, fileList=${fileList.size}" }
        for (index in fileList.indices) {
            if (isCanceled) {
                GLog.d(TAG) { "backupFile, cancel call, current is $index, return" }
                return ProcessResult.CANCEL
            }
            BRFileHelper.copyFileForBackup(plugin, getBREditFilesDir(rootPath), fileList[index])
            processCountChangedAction?.invoke(index + 1)
        }
        return ProcessResult.SUCCESS
    }

    private fun getFileListFromBackupDataList(dataList: List<ParametricDataEntity>): List<File>? {
        return dataList.flatMap { listOf(it.getOriginalFilePath(), it.getContentEditFilePath()) }
            .filter { it.isNullOrEmpty().not() }
            .map { File(it) }
            .takeIf { it.isNotEmpty() }
    }

    /**
     * 备份数据库
     */
    private fun backupDatabase(rootPath: String, plugin: AbstractPlugin, dataList: List<ParametricDataEntity>) {
        dataList.map {
            EditInfoBRData().apply {
                data = it.filePath
                originalData = it.originFileSubPath
                contentEditData = it.contentEditFileSubPath
                invalid = it.invalid
            }
        }.also { GLog.d(TAG) { "backupDatabase, data size = ${it.size}" } }
            .takeIf { it.isNotEmpty() }
            ?.let { JsonUtil.toJson(it) }
            ?.let { BRFileHelper.writeToFile(plugin.getFileDescriptor(getBREditDBJsonFilePath(rootPath)), it) }
    }

    /**
     * 备份搬家配置文件
     */
    private fun backupConfig(rootPath: String, plugin: AbstractPlugin) {
        BRConfigEntity(COMPONENT_VERSION)
            .let { JsonUtil.toJson(it) }
            .let { BRFileHelper.writeToFile(plugin.getFileDescriptor(getBRConfigFilePath(rootPath)), it) }
    }

    override fun restore(context: Context, rootPath: String, plugin: AbstractPlugin): String {
        ApiDmManager.getScanDM().setEditInfoScanEnable(false)
        val result = doRestore(context, rootPath, plugin)
        ApiDmManager.getScanDM().setEditInfoScanEnable(true)
        return if (result) SUCCESS else IO_EXCEPTION
    }

    private fun doRestore(context: Context, rootPath: String, plugin: AbstractPlugin): Boolean {
        runCatching {
            // 检查备份数据版本是否可恢复
            if (checkConfigIfCanRestore(rootPath, plugin).not()) {
                GLog.i(TAG, "restore, checkConfigIfCanRestore backup data version larger than current, skip")
                return true
            }

            // 恢复db，考虑执行速度，分开搬文件和数据库记录，如果后续文件中断，冗余记录通过后台扫描清理
            val validDataList: List<EditInfoBRData> = restoreDatabase(rootPath, plugin) ?: run {
                return true
            }

            // 恢复文件
            val result = restoreFile(context, rootPath, plugin, validDataList)
            /**
             * 埋点恢复文件
             */
            if (result == ProcessResult.SUCCESS) {
                UserAssetsTrackHelper.trackPhoneClone(
                    TAG,
                    true,
                    PhoneCloneConstant.PHONE_CLONE_RESTORE,
                    getTrackData(validDataList, TRACK_RESTORE_FILE_COUNT)
                )
            } else if (result == ProcessResult.CANCEL) {
                UserAssetsTrackHelper.trackPhoneClone(
                    TAG,
                    false,
                    PhoneCloneConstant.PHONE_CLONE_RESTORE,
                    PhoneCloneConstant.PHONE_CLONE_CANCELED_PROGRESSED
                )
            } else if (result == ProcessResult.NO_PENDING_DATA) {
                UserAssetsTrackHelper.trackPhoneClone(
                    TAG,
                    false,
                    PhoneCloneConstant.PHONE_CLONE_RESTORE,
                    PhoneCloneConstant.PHONE_CLONE_NO_PENDING_DATA
                )
            } else {
                UserAssetsTrackHelper.trackPhoneClone(
                    TAG,
                    false,
                    PhoneCloneConstant.PHONE_CLONE_RESTORE,
                    PhoneCloneConstant.PHONE_CLONE_MESSAGE_FAIL
                )
            }
            GLog.d(TAG, LogFlag.DL, "restore, restoreFile result = $result")
        }.onFailure {
            GLog.d(TAG, LogFlag.DL, "restore, failed! error= $it")
            /**
             * 埋点恢复文件失败
             */
            UserAssetsTrackHelper.trackPhoneClone(
                TAG,
                false,
                PhoneCloneConstant.PHONE_CLONE_RESTORE,
                PhoneCloneConstant.PHONE_CLONE_MESSAGE_FAIL
            )
        }
        return true
    }

    /**
     * 检查配置文件，当前相册版本是否支持恢复
     */
    private fun checkConfigIfCanRestore(rootPath: String, plugin: AbstractPlugin): Boolean {
        val backupComponentVersion = getObjectFromFile(plugin, getBRConfigFilePath(rootPath), object : TypeToken<BRConfigEntity>() {})
            ?.componentVersion
            ?: run {
                GLog.e(TAG, "checkConfigIfCanRestore, parse config failed!")
                return false
            }
        GLog.d(TAG) { "checkConfigIfCanRestore, backupVersion=$backupComponentVersion, currentVersion=$COMPONENT_VERSION" }
        return backupComponentVersion <= COMPONENT_VERSION
    }

    /**
     * 恢复数据库
     */
    private fun restoreDatabase(rootPath: String, plugin: AbstractPlugin): List<EditInfoBRData>? {
        if (isCanceled) {
            GLog.e(TAG, "restoreDatabase, cancel call, return")
            return null
        }

        // 从 db json 文件解析出 对象实体
        val restoreDataList = getDataListForRestore(rootPath, plugin) ?: run {
            GLog.d(TAG, "restoreDatabase, restoreDataList = ${restoreDataCacheList?.size}")
            return null
        }

        // 获取恢复数据和本地表中重复的部分
        val existFileSet = restoreDataList.mapNotNull { it.data }
            .let { ParametricProjectDataHelper.filterExistData(it, EditInfoColumns.DATA) }
            ?.toHashSet() ?: emptySet()
        val existDataSet = restoreDataList.filter { existFileSet.contains(it.data) }.toSet()

        // 更新重复部分，目的是将 invalid 刷新
        val updateResult = existDataSet.takeIf { it.isNotEmpty() }
            ?.mapNotNull { editData ->
                editData.data?.let {
                    ParametricProjectDataHelper.getUpdateReq(it, editData.getContentValues(), true)
                }
            }?.let { ParametricProjectDataHelper.batchUpdate(it) }

        // 插入非重复的部分
        val insertResult = (restoreDataList - existDataSet)
            .mapNotNull { ParametricProjectDataHelper.getInsertReq(it.getContentValues(), true) }
            .let { ParametricProjectDataHelper.batchInsert(it) }

        GLog.d(TAG) {
            "restoreDatabase, restoreDataList = ${restoreDataList.size}, " +
                    "existDataSet = ${existDataSet.size}, " +
                    "updateResult = ${BatchResult.getResultDesc(updateResult)}, " +
                    "insertResult = ${BatchResult.getResultDesc(insertResult)}"
        }
        return restoreDataList
    }

    private fun getDataListForRestore(rootPath: String, plugin: AbstractPlugin): List<EditInfoBRData>? {
        if (checkConfigIfCanRestore(rootPath, plugin).not()) {
            GLog.i(TAG, "getDataListForRestore, checkConfigIfCanRestore failed!")
            return null
        }
        return restoreDataCacheList?.toList()
            ?: getObjectFromFile(plugin, getBREditDBJsonFilePath(rootPath), object : TypeToken<List<EditInfoBRData>>() {})
                .also { GLog.d(TAG, "getDataListForRestore, jsonDataList = ${it?.size}") }
                ?.filter { File.checkExist(it.data) }
                ?.takeIf { it.isNotEmpty() }
                ?.also { restoreDataCacheList = it }
                .also { GLog.d(TAG, "getDataListForRestore, restoreDataList = ${it?.size}") }
                ?.toList()
    }

    /**
     * 恢复文件
     */
    private fun restoreFile(context: Context, rootPath: String, plugin: AbstractPlugin, dataList: List<EditInfoBRData>): ProcessResult {
        // 获取文件名列表
        val fileNameList = dataList.flatMap {
            listOfNotNull(
                FilePathUtils.getDisplayName(it.originalData),
                FilePathUtils.getDisplayName(it.contentEditData)
            )
        }

        GLog.d(TAG, "restoreFile, fileNameList = ${fileNameList.size}")
        val targetFileList = fileNameList
            .filter { it.isNotEmpty() }
            .map { File(getLocalEditFileDir(context), it) }
            .filter { it.exists().not() }
            .also { GLog.d(TAG, "restoreFile, not exist fileList = ${it.size} ") }
            .takeIf { it.isNotEmpty() } ?: run {
            GLog.d(TAG, "restoreFile, all file exist, return")
            return ProcessResult.NO_PENDING_DATA
        }

        if (FileOperationUtils.ensureMakeDirectory(getLocalEditFileDir(context)).not()) {
            GLog.e(TAG, "restoreFile, target dir not exist, return")
            return ProcessResult.FAIL
        }

        // 恢复文件
        GLog.d(TAG) { "restoreFile, targetFilePathList=${targetFileList.size}" }
        for (index in targetFileList.indices) {
            // 搬家插件取消，停止搬文件
            if (isCanceled) {
                GLog.d(TAG) { "restoreFile, cancel call, current is $index, return" }
                return ProcessResult.CANCEL
            }
            BRFileHelper.copyFileForRestore(plugin, getBREditFilesDir(rootPath), targetFileList[index]).also {
                if (it.not()) {
                    GLog.e(TAG) { "restoreFile, copyFile failed! data=${PathMask.mask(targetFileList[index].absolutePath)}" }
                }
            }
            processCountChangedAction?.invoke(index + 1)
        }
        return ProcessResult.SUCCESS
    }

    private fun <T> getObjectFromFile(plugin: AbstractPlugin, path: String, typeToken: TypeToken<T>): T? {
        return runCatching {
            val fd = plugin.getFileDescriptor(path) ?: run {
                GLog.e(TAG, "getObjectFromFile, failed! fd is null1")
                return null
            }
            val contentStr = BRFileHelper.readFromFileDescriptor(fd) ?: run {
                GLog.e(TAG, "getObjectFromFile, failed! contentStr is null")
                return null
            }
            JsonUtil.fromJson(contentStr, typeToken)
        }.onFailure {
            GLog.e(TAG, "getObjectFromFile, failed! error=", it)
        }.getOrNull()
    }

    override fun onCancel() {
        isCanceled = true
    }

    override fun getBackupInfo(): BRComponentInfo {
        val fileList = getDataListForBackup()?.let { getFileListFromBackupDataList(it) }
        val size = fileList?.sumOf { it.length() } ?: 0L
        val count = fileList?.size ?: DEFAULT_COMPONENT_PREVIEW_COUNT
        GLog.d(TAG, LogFlag.DL, "getBackupInfo, fileList.size = ${fileList?.size}, count = $count")
        return BRComponentInfo(size, count)
    }

    override fun getRecoveryInfo(rootPath: String, plugin: AbstractPlugin): BRComponentInfo {
        val fileList = getDataListForRestore(rootPath, plugin)
            ?.let { getFileListFromRecoveryDataList(it) }
        val count = fileList?.size ?: DEFAULT_COMPONENT_PREVIEW_COUNT
        GLog.d(TAG, LogFlag.DL, "getRecoveryInfo, fileList.size = ${fileList?.size}, count = $count")
        return BRComponentInfo(0, count)
    }

    private fun getFileListFromRecoveryDataList(dataList: List<EditInfoBRData>): List<File>? {
        return dataList.flatMap { listOf(it.originalData, it.contentEditData) }
            .filter { it.isNullOrEmpty().not() }
            .map { File(it) }
            .takeIf { it.isNotEmpty() }
    }

    private fun getLocalEditFileDir(context: Context): String =
        File(context.filesDir, ParametricConst.DIR_PROJECT_ORIGINAL_FILE).absolutePath

    private fun getBREditFilesDir(rootPath: String): String = rootPath + PATH_SEGMENT_EDIT + PATH_SEGMENT_FLIES
    private fun getBRConfigFilePath(rootPath: String): String = rootPath + PATH_SEGMENT_EDIT + FILE_NAME_CONFIG
    private fun getBREditDBJsonFilePath(rootPath: String): String = rootPath + PATH_SEGMENT_EDIT + FILE_NAME_EDIT_INFO_DB

    /**
     * 搬家子阶段的处理结果
     */
    enum class ProcessResult {
        /** 成功 */
        SUCCESS,

        /** 取消 */
        CANCEL,

        /** 失败 */
        FAIL,

        /** 无待处理数据 */
        NO_PENDING_DATA
    }

    companion object {
        private const val TAG = "EditInfoBRComponent"
        private const val PATH_SEGMENT_EDIT = "/GallerySysData/Edit/"
        private const val PATH_SEGMENT_FLIES = "File/"
        private const val FILE_NAME_EDIT_INFO_DB = "edit_info_db.txt"
        private const val FILE_NAME_CONFIG = "config.txt"

        /**
         * 备份文件key
         */
        private const val TRACK_BACKUP_FILE_COUNT = "track_backup_file_count"

        /**
         * 恢复文件key
         */
        private const val TRACK_RESTORE_FILE_COUNT = "track_restore_file_count"

        /**
         * 组件版本号，用于判断备份的数据是否可以恢复，如果备份的数据版本大于当前，不允许恢复
         */
        private const val COMPONENT_VERSION = 1
    }
}

@SuppressLint("Range")
class EditInfoBRData(
    @SerializedName(EditInfoColumns.DATA) var data: String? = "",
    @SerializedName(EditInfoColumns.ORIGINAL_DATA) var originalData: String? = "",
    @SerializedName(EditInfoColumns.CONTENT_EDIT_DATA) var contentEditData: String? = "",
    @SerializedName(EditInfoColumns.INVALID) var invalid: Int? = EditInfoColumns.INVALID_DEFAULT
) {

    fun getContentValues(): ContentValues = ContentValues().apply {
        put(EditInfoColumns.DATA, data)
        put(EditInfoColumns.ORIGINAL_DATA, originalData)
        put(EditInfoColumns.CONTENT_EDIT_DATA, contentEditData)
        put(EditInfoColumns.INVALID, invalid)
    }

    override fun toString(): String {
        return "[data=${FilePathUtils.getDisplayName(data)}," +
                "originalData=${FilePathUtils.getDisplayName(originalData)}," +
                "contentEditData=${FilePathUtils.getDisplayName(contentEditData)}," +
                "invalid=$invalid]"
    }
}