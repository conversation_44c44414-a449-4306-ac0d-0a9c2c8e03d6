<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:id="@+id/action_restore_recycled"
        android:enabled="true"
        android:icon="@drawable/base_ic_restore_selector"
        android:title="@string/base_restore"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_delete_recycled"
        android:enabled="true"
        android:icon="@drawable/base_ic_delete_selector"
        android:title="@string/base_delete_single_permanently"
        app:showAsAction="ifRoom" />

</menu>
