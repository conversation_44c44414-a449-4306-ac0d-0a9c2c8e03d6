<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName"
    tools:parentTag="androidx.coordinatorlayout.widget.CoordinatorLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottomMenuBarContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/common_transparent"
        android:visibility="gone">

        <View
            android:id="@+id/bottomMenuBlurLayer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/common_transparent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>