<resources>
    <dimen name="base_privacy_policy_tip_vertical_space">10dp</dimen>

    <!--statement end-->
    <dimen name="base_album_set_item_title_margin_top">6dp</dimen>
    <dimen name="base_album_set_item_red_dot_padding">4dp</dimen>
    <dimen name="base_album_item_cover_image_corner_radius">16dp</dimen>
    <dimen name="base_album_item_sub_cover_image_corner_radius">@dimen/coui_round_corner_m</dimen>
    <dimen name="base_album_small_item_cover_image_corner_radius">8dp</dimen>
    <dimen name="base_all_album_item_title_text_size">16sp</dimen>
    <dimen name="base_all_album_item_media_overlay_duration_text_size">9sp</dimen>
    <dimen name="base_all_album_item_card_title_text_size">14sp</dimen>
    <dimen name="base_album_set_item_cover_mark_icon_margin_end">4dp</dimen>
    <dimen name="base_album_set_item_cover_mark_icon_margin_bottom">4dp</dimen>
    <dimen name="base_album_set_item_cover_mark_icon_margin_top">8dp</dimen>
    <dimen name="base_album_set_item_cover_mark_icon_width">24dp</dimen>
    <dimen name="base_album_set_item_cover_mark_icon_height">24dp</dimen>

    <dimen name="base_album_set_item_cover_right_bottom_mark_icon_margin_end">4dp</dimen>
    <dimen name="base_album_set_item_cover_right_bottom_mark_icon_margin_bottom">4dp</dimen>
    <dimen name="base_album_set_item_cover_right_bottom_mark_icon_width">18dp</dimen>
    <dimen name="base_album_set_item_cover_right_bottom_mark_icon_height">18dp</dimen>

    <dimen name="base_album_item_cover_mark_icon_width">24dp</dimen>
    <dimen name="base_album_item_cover_mark_icon_height">24dp</dimen>
    <dimen name="base_album_item_cover_mark_icon_margin_end">2dp</dimen>
    <dimen name="base_album_item_cover_mark_icon_margin_top">2dp</dimen>
    <dimen name="base_album_set_item_checkbox_margin_end">6dp</dimen>

    <!--timeline-->
    <dimen name="base_timeline_scroll_slop">2dp</dimen>
    <dimen name="base_timeline_pinch_distance_step">24dp</dimen>

    <dimen name="base_timeline_layout_year_gap">0.67dp</dimen>
    <dimen name="base_timeline_layout_month_gap">0.67dp</dimen>
    <dimen name="base_timeline_layout_picked_year_item_radius">@dimen/coui_round_corner_xl_radius</dimen>
    <dimen name="base_timeline_layout_picked_year_month_gap">8dp</dimen>
    <dimen name="base_timeline_layout_picked_year_month_item_gap">12dp</dimen>
    <dimen name="base_timeline_layout_picked_month_radius">@dimen/coui_round_corner_l</dimen>
    <dimen name="base_timeline_layout_picked_year_month_margin_start_end">24dp</dimen>
    <dimen name="base_timeline_layout_picked_year_month_margin_start_end_portrait">12dp</dimen>
    <dimen name="base_timeline_layout_picked_year_month_margin_start_end_landscape">20dp</dimen>
    <dimen name="base_timeline_layout_picked_year_constraint_padding">24dp</dimen>
    <dimen name="base_timeline_layout_day_gap">1dp</dimen>
    <dimen name="base_timeline_title_region_height">36dp</dimen>
    <dimen name="base_timeline_title_region_height_large">38dp</dimen>
    <dimen name="base_timeline_title_region_padding_start">16dp</dimen>
    <dimen name="base_timeline_time_text_region_padding_end">16dp</dimen>
    <dimen name="base_timeline_title_region_padding_end">20dp</dimen>
    <dimen name="base_timeline_title_region_padding_top">0dp</dimen>
    <dimen name="base_timeline_title_region_padding_bottom">0dp</dimen>
    <dimen name="base_timeline_time_node_bottom_margin">8dp</dimen>
    <dimen name="base_timeline_time_title_text_size">14sp</dimen>
    <dimen name="base_timeline_time_title_start_space_from_next_title">12dp</dimen>
    <dimen name="base_timeline_time_title_end_space_from_next_title">5dp</dimen>
    <dimen name="base_timeline_checkbox_wh">24dp</dimen>
    <dimen name="base_timeline_checkbox_margin_end">20dp</dimen>
    <dimen name="base_timeline_slot_border_width">0.67dp</dimen>
    <dimen name="base_timeline_slot_border_width_inside">1.4dp</dimen>
    <dimen name="base_timeline_title_region_dot_width">18dp</dimen>
    <dimen name="base_timeline_title_region_dot_radius">2dp</dimen>
    <dimen name="base_timeline_immersive_time_title_height">24dp</dimen>
    <dimen name="base_timeline_immersive_time_title_height_large">36dp</dimen>
    <dimen name="base_timeline_immersive_time_title_padding_start_end">8dp</dimen>
    <dimen name="base_timeline_immersive_time_title_radius">6dp</dimen>

    <dimen name="base_timeline_picked_year_title_text_size">28sp</dimen>
    <dimen name="base_timeline_picked_year_title_layout_padding_top">16dp</dimen>
    <dimen name="base_timeline_picked_year_title_layout_padding_start">20dp</dimen>
    <dimen name="base_timeline_picked_year_title_layout_padding_end">20dp</dimen>

    <dimen name="base_timeline_picked_month_node_padding_title">20dp</dimen>
    <dimen name="base_timeline_picked_month_node_title_region_height">32dp</dimen>
    <dimen name="base_timeline_picked_month_node_title_text_size">20sp</dimen>
    <dimen name="base_timeline_picked_month_title_text_shadow_dy">2dp</dimen>
    <dimen name="base_timeline_picked_month_title_padding_top">12dp</dimen>
    <dimen name="base_timeline_picked_month_title_padding_start">14dp</dimen>
    <dimen name="base_timeline_picked_month_title_padding_end">14dp</dimen>
    <dimen name="base_timeline_picked_month_title_overlay_padding">2dp</dimen>
    <dimen name="base_timeline_picked_month_subtitle_layout_padding_top">1dp</dimen>
    <dimen name="base_timeline_picked_month_subtitle_text_size">12sp</dimen>

    <dimen name="base_timeline_media_overlay_icon_margin_vertical">2.5dp</dimen>
    <dimen name="base_timeline_media_overlay_icon_margin_horizontal">4dp</dimen>
    <dimen name="base_timeline_media_overlay_icon_wh">12dp</dimen>
    <dimen name="base_timeline_media_overlay_favorite_margin_end">5dp</dimen>
    <dimen name="base_timeline_media_overlay_favorite_margin_top">4dp</dimen>
    <dimen name="base_timeline_media_overlay_duration_margin_start">1dp</dimen>
    <dimen name="base_timeline_media_overlay_duration_margin_end">5dp</dimen>
    <dimen name="base_timeline_media_overlay_bg_width">20dp</dimen>
    <dimen name="base_timeline_media_overlay_bg_height">17dp</dimen>
    <dimen name="base_timeline_media_overlay_bg_margin_start">5dp</dimen>
    <dimen name="base_timeline_media_overlay_bg_margin_bottom">5.5dp</dimen>
    <dimen name="base_timeline_media_overlay_live_margin_start">2dp</dimen>
    <dimen name="base_timeline_media_overlay_live_margin_bottom">2dp</dimen>
    <dimen name="base_timeline_media_overlay_live_width">24dp</dimen>
    <dimen name="base_timeline_media_overlay_checkbox_margin">2dp</dimen>
    <dimen name="base_timeline_media_overlay_checkbox_wh">24dp</dimen>
    <dimen name="base_timeline_media_overlay_duration_text_size">9sp</dimen>
    <!--正序：自然排序为降序-->
    <dimen name="base_timeline_footerview_padding_top">8dp</dimen>
    <dimen name="base_timeline_footerview_padding_bottom">12dp</dimen>

    <!--picked_day -->
    <dimen name="base_picked_day_min_width">480dp</dimen>
    <dimen name="base_picked_day_remain_padding_start_end">13dp</dimen>
    <dimen name="base_picked_day_remain_height">26dp</dimen>
    <dimen name="base_picked_day_remain_margin_bottom">24dp</dimen>
    <dimen name="base_picked_day_remain_margin_end">24dp</dimen>
    <dimen name="base_picked_day_title_region_padding_end">11dp</dimen>
    <dimen name="base_picked_day_toolbar_icon_width">84dp</dimen>
    <dimen name="base_picked_day_toolbar_icon_padding_end">11dp</dimen>
    <dimen name="base_picked_day_title_text_size">18sp</dimen>
    <dimen name="base_picked_day_sub_title_text_size">12sp</dimen>
    <dimen name="base_picked_day_sub_title_margin_top">6dp</dimen>
    <dimen name="base_picked_day_title_region_height">70dp</dimen>
    <dimen name="base_picked_day_min_space_from_next_title">55dp</dimen>
    <dimen name="base_picked_day_max_space_from_next_title">140dp</dimen>
    <dimen name="base_picked_day_floating_title_offset">0dp</dimen>

    <item name="base_text_line_spacing" format="float" type="dimen">1.0</item>
    <dimen name="base_timeline_footer_cloud_sync_margin_top">16dp</dimen>
    <dimen name="base_timeline_header_view_margin_top">20dp</dimen>
    <dimen name="base_timeline_header_view_margin_bottom">16dp</dimen>
    <dimen name="base_timeline_default_count_view_height">12dp</dimen>
    <dimen name="base_timeline_cloud_view_margin_top">16dp</dimen>
    <dimen name="base_timeline_footer_start_fade_in_distance">40dp</dimen>
    <dimen name="base_timeline_count_view_margin_bottom">16dp</dimen>
    <dimen name="base_timeline_cloud_sync_icon_margin_start">12dp</dimen>

    <dimen name="base_list_slot_view_tag_rect_width">20dp</dimen>
    <dimen name="base_list_slot_view_tag_rect_height">17dp</dimen>
    <dimen name="base_list_slot_view_tag_corner_radius">8.5dp</dimen>
    <dimen name="base_list_slot_view_tag_corner_around_radius">15dp</dimen>
    <dimen name="base_list_slot_view_tag_rect_margin_bottom">5dp</dimen>
    <dimen name="base_list_slot_view_tag_rect_margin_start">5dp</dimen>
    <dimen name="base_list_slot_view_check_box_rect_width">24dp</dimen>
    <dimen name="base_list_slot_view_check_box_rect_height">24dp</dimen>
    <dimen name="base_list_slot_view_duration_text_size">9dp</dimen>
    <dimen name="base_list_slot_view_type_icon_width">12dp</dimen>
    <dimen name="base_list_slot_view_type_icon_height">12dp</dimen>
    <dimen name="base_list_slot_view_type_icon_live_photo_wh">24dp</dimen>
    <dimen name="base_list_slot_view_type_icon_live_photo_margin_start">2dp</dimen>
    <dimen name="base_list_slot_view_type_icon_live_photo_tag_rect_margin_bottom">2dp</dimen>
    <dimen name="base_list_slot_view_type_icon_margin_top">2.3dp</dimen>
    <dimen name="base_list_slot_view_type_icon_margin_start">4dp</dimen>
    <dimen name="base_list_slot_view_mark_icon_width">24dp</dimen>
    <dimen name="base_list_slot_view_mark_icon_height">24dp</dimen>
    <dimen name="base_list_slot_view_mark_icon_margin_top">2dp</dimen>
    <dimen name="base_list_slot_view_mark_icon_margin_end">4dp</dimen>
    <dimen name="base_list_slot_view_duration_text_padding_left">1dp</dimen>
    <dimen name="base_list_slot_view_duration_text_no_type_icon_padding_right">4.67dp</dimen>

    <dimen name="base_memory_album_vertical_gap">10dp</dimen>
    <dimen name="base_memory_details_header_text_view_padding_bottom">8dp</dimen>

    <dimen name="base_album_set_fragment_item_view_top_gap">0dp</dimen>
    <dimen name="base_album_set_fragment_item_view_bottom_gap">8dp</dimen>
    <dimen name="base_album_set_fragment_item_view_horizontal_gap">8dp</dimen>
    <dimen name="base_album_set_fragment_item_view_horizontal_page_margin">16dp</dimen>
    <dimen name="main_tab_album_set_fragment_item_view_top_gap">16dp</dimen>
    <!--标题中的箭头有12dp的热区-->
    <dimen name="base_album_set_fragment_item_view_title_end_margin">12dp</dimen>
    <dimen name="base_album_set_fragment_item_view_title_arrow_padding_end">7dp</dimen>
    <dimen name="base_explorer_container_padding_top">10dp</dimen>
    <dimen name="base_explorer_container_padding_bottom">32dp</dimen>
    <dimen name="base_memories_album_set_fragment_item_view_horizontal_page_margin">16dp</dimen>
    <dimen name="base_memories_album_set_fragment_item_view_horizontal_gap">8dp</dimen>
    <dimen name="base_normal_album_fragment_item_padding_top">1dp</dimen>
    <dimen name="base_album_fragment_item_view_horizontal_gap">1dp</dimen>

    <dimen name="base_dialog_customview_padding_right">18dp</dimen>
    <dimen name="base_dialog_customview_padding_left">18dp</dimen>
    <dimen name="base_details_layout_padding_vertical">12dp</dimen>
    <dimen name="base_details_layout_margin_left">24dp</dimen>
    <dimen name="base_details_layout_margin_right">24dp</dimen>
    <dimen name="base_details_layout_padding_bottom">16dp</dimen>
    <dimen name="base_details_tab_item_min_height_tb">64dp</dimen>
    <dimen name="base_details_table_row_right_margin_top">3dp</dimen>

    <dimen name="base_album_count_string_padding_horizontal">16dp</dimen>
    <dimen name="base_album_count_string_padding_bottom">24dp</dimen>
    <dimen name="base_album_count_string_padding_top">12dp</dimen>
    <dimen name="base_recycle_text_top">4dp</dimen>

    <dimen name="base_delete_cloud_dialog_image_width">266.7dp</dimen>
    <dimen name="base_delete_cloud_dialog_image_height">166.7dp</dimen>
    <dimen name="base_delete_cloud_dialog_image_margin_start_end">26.7dp</dimen>
    <dimen name="base_delete_cloud_dialog_image_margin_top">20dp</dimen>
    <dimen name="base_delete_cloud_dialog_title_margin_start_end">30dp</dimen>
    <dimen name="base_delete_cloud_dialog_old_device_title_height">58dp</dimen>

    <dimen name="base_album_recyclerview_padding_top">3dp</dimen>
    <dimen name="base_album_recyclerview_padding_bottom">32dp</dimen>
    <dimen name="base_album_set_recyclerview_padding_bottom">16dp</dimen>

    <dimen name="base_shape_corner_radius">16dp</dimen>
    <integer name="base_editor_menu_item_text_max_lines">2</integer>
    <!--照片列表item最大最小范围（折叠屏小屏分屏）-->
    <dimen name="base_album_list_min_item_width">74dp</dimen>
    <dimen name="base_album_list_max_item_width">99dp</dimen>
    <!--精彩回忆封面最大高度、默认高度-->
    <dimen name="base_default_memory_cover_height">360dp</dimen>
    <dimen name="base_default_memory_cover_height_landspace_samll">180dp</dimen>
    <dimen name="base_default_memory_cover_height_large">450dp</dimen>

    <!-- 照片视图引导弹窗 Begin -->
    <dimen name="base_present_type_guide_padding_vertical">16dp</dimen>
    <dimen name="base_present_type_guide_margin_middle">8dp</dimen>
    <dimen name="base_present_type_guide_title_margin_top">12dp</dimen>
    <dimen name="base_present_type_guide_child_text_margin_top">12dp</dimen>
    <dimen name="base_present_type_guide_child_rb_wh">24dp</dimen>
    <dimen name="base_present_type_guide_child_rb_margin">8dp</dimen>

    <!-- default -->
    <dimen name="base_present_type_guide_img_placeholder_width">116dp</dimen>
    <dimen name="base_present_type_guide_img_placeholder_height">180dp</dimen>
    <dimen name="base_present_type_guide_info_margin_horizontal">16dp</dimen>
    <dimen name="base_present_type_guide_info_title_min_height">10dp</dimen>
    <dimen name="base_present_type_guide_info_title_max_width">52dp</dimen>
    <dimen name="base_present_type_guide_info_title_margin_top">8.25dp</dimen>
    <dimen name="base_present_type_guide_info_title_padding_horizonal">4.3dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_min_height">8.2dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_max_width">73dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_padding_horizontal">4dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_today_margin_top">27dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_yesterday_margin_top">82.5dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_other_day_margin_top">138.5dp</dimen>
    <dimen name="base_present_type_guide_info_title_text_size_immersive">5.4dp</dimen>
    <dimen name="base_present_type_guide_info_title_text_size_date">7.56dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_text_size">4dp</dimen>

    <!-- sw480dp -->
    <dimen name="base_present_type_guide_img_placeholder_width_sw480">141.5dp</dimen>
    <dimen name="base_present_type_guide_img_placeholder_height_sw480">154dp</dimen>
    <dimen name="base_present_type_guide_info_margin_horizontal_sw480">4.29dp</dimen>
    <dimen name="base_present_type_guide_info_title_min_height_sw480">10dp</dimen>
    <dimen name="base_present_type_guide_info_title_max_width_sw480">102dp</dimen>
    <dimen name="base_present_type_guide_info_title_margin_top_sw480">8.5dp</dimen>
    <dimen name="base_present_type_guide_info_title_padding_horizonal_sw480">6dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_min_height_sw480">10dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_max_width_sw480">127dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_padding_horizontal_sw480">5.5dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_today_margin_top_sw480">25.5dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_yesterday_margin_top_sw480">88.5dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_other_day_margin_top_sw480">0dp</dimen>
    <dimen name="base_present_type_guide_info_title_text_size_immersive_sw480">7dp</dimen>
    <dimen name="base_present_type_guide_info_title_text_size_date_sw480">9dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_text_size_sw480">5dp</dimen>

    <!-- sw700dp -->
    <dimen name="base_present_type_guide_img_placeholder_width_sw700">152dp</dimen>
    <dimen name="base_present_type_guide_img_placeholder_height_sw700">109dp</dimen>
    <dimen name="base_present_type_guide_info_margin_horizontal_sw700">2dp</dimen>
    <dimen name="base_present_type_guide_info_title_min_height_sw700">10dp</dimen>
    <dimen name="base_present_type_guide_info_title_max_width_sw700">110dp</dimen>
    <dimen name="base_present_type_guide_info_title_margin_top_sw700">7.4dp</dimen>
    <dimen name="base_present_type_guide_info_title_padding_horizonal_sw700">6dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_min_height_sw700">9dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_max_width_sw700">136dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_padding_horizontal_sw700">5.5dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_today_margin_top_sw700">26dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_yesterday_margin_top_sw700">78dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_other_day_margin_top_sw700">0dp</dimen>
    <dimen name="base_present_type_guide_info_title_text_size_immersive_sw700">7dp</dimen>
    <dimen name="base_present_type_guide_info_title_text_size_date_sw700">9dp</dimen>
    <dimen name="base_present_type_guide_info_sub_title_text_size_sw700">4.5dp</dimen>
    <!-- 照片视图引导弹窗 End -->

    <dimen name="base_timeline_pinch_update_distance_threshold">1.33dp</dimen>

    <dimen name="business_synergy_drag_slop">6dp</dimen>

    <dimen name="business_lib_editor_menu_padding">24dp</dimen>
    <dimen name="business_lib_editor_menu_gap">8dp</dimen>
    <dimen name="business_lib_editor_menu_need_padding_width">840dp</dimen>
    <dimen name="business_lib_editor_menu_default_margin_bottom">16dp</dimen>

    <dimen name="business_lib_memories_editor_photo_image_item_width">40dp</dimen>
    <dimen name="business_lib_grid_window_default_padding">24dp</dimen>
    <dimen name="business_lib_grid_window_width_divided_by12">840dp</dimen>
    <dimen name="business_lib_grid_window_width_divided_by8">640dp</dimen>
    <dimen name="business_lib_grid_window_height_divided_by12">840dp</dimen>
    <dimen name="business_lib_grid_window_height_divided_by8">540dp</dimen>
    <dimen name="business_lib_grid_window_window_gap">8dp</dimen>
    <dimen name="business_lib_grid_window_no_padding_height">72dp</dimen>

    <dimen name="business_lib_landscape_toolbar_margin">24dp</dimen>
    <dimen name="business_lib_editor_safe_edge_width_default_landscape">24dp</dimen>
    <dimen name="business_lib_editor_safe_edge_width_table_landscape">40dp</dimen>

    <!-- 竖屏情况下，title_bar_container新增了12dp的paddingTop，所以这里高度需要增加12dp  -->
    <dimen name="business_lib_editor_title_bar_height_default_window_old">62dp</dimen>
    <!-- 竖屏情况下，该高度应该是按钮高度（50dp）+padding(16dp) = 66dp，这里高度需要增加16dp ，按钮高度与视屏时一致为50dp（video_editor_titlebar_height） -->
    <dimen name="business_lib_editor_title_bar_height_default_window">66dp</dimen>
    <dimen name="business_lib_editor_title_bar_height_micro_window">52dp</dimen>
    <!-- 横屏情况下，title_bar_container的高度  -->
    <dimen name="business_lib_editor_title_bar_land_height_default_window">50dp</dimen>
    <dimen name="business_lib_editor_title_bar_land_height_micro_window">40dp</dimen>
    <dimen name="base_editor_menu_item_name_textview_margin_top">3dp</dimen>
    <dimen name="base_editor_title_bar_container_margin_top">5dp</dimen>

    <!--编辑公用-->
    <dimen name="base_editor_fading_edge_width">20dp</dimen>

    <!--    menu item-->
    <dimen name="base_editor_menu_item_filter_text_height">28dp</dimen>
    <dimen name="base_editor_menu_item_text_height">45dp</dimen>
    <dimen name="base_editor_menu_item_text_height_vertical">14dp</dimen>
    <dimen name="base_editor_menu_item_view_width">72dp</dimen>
    <dimen name="base_editor_menu_item_view_height">104dp</dimen>
    <dimen name="base_editor_filter_item_view_height">102dp</dimen>
    <dimen name="base_editor_menu_item_corner_radius">28.335dp</dimen>
    <dimen name="base_editor_menu_item_icon_height">76dp</dimen>
    <dimen name="base_editor_menu_item_icon_height_landscape">90dp</dimen>
    <dimen name="base_editor_menu_item_icon_width">62dp</dimen>
    <dimen name="base_editor_menu_item_out_border_radius">32dp</dimen>
    <dimen name="base_editor_menu_adjust_item_image_margin_top">8dp</dimen>
    <dimen name="base_editor_menu_item_text_shadow_size">12dp</dimen>
    <dimen name="base_editor_menu_item_icon_shadow_size">4dp</dimen>
    <dimen name="base_editor_menu_item_support_text_size">10dp</dimen>
    <dimen name="base_editor_menu_item_progress_stroke_width">2.6dp</dimen>
    <dimen name="base_editor_menu_item_progress_radius">10dp</dimen>
    <dimen name="base_editor_menu_item_icon_shadow_dy">4dp</dimen>
    <dimen name="base_editor_menu_item_tip_width">54dp</dimen>
    <dimen name="base_editor_menu_item_tip_height">14dp</dimen>
    <dimen name="base_editor_menu_item_radius">10.335dp</dimen>
    <dimen name="base_editor_menu_item_layout_width">64dp</dimen>
    <dimen name="base_editor_menu_item_layout_height">77dp</dimen>
    <dimen name="base_editor_menu_item_filter_size">54dp</dimen>
    <dimen name="base_editor_menu_item_text_margin_top">11dp</dimen>
    <dimen name="base_editor_menu_item_tag_radius">8dp</dimen>
    <dimen name="base_editor_menu_item_border_stroke_width">2.4dp</dimen>
    <dimen name="base_editor_menu_filter_border_stroke_width">0.6dp</dimen>
    <dimen name="base_editor_menu_item_border_gap_width">2dp</dimen>
    <dimen name="base_editor_menu_item_landscape_layout_height">79dp</dimen>
    <dimen name="base_editor_menu_item_tag_margin_bottom">-3dp</dimen>
    <dimen name="base_editor_menu_item_line_spacing_extra">-4dp</dimen>
    <dimen name="base_editor_menu_item_out_border">0dp</dimen>
    <dimen name="base_editor_menu_special_item_spacing">17dp</dimen>
    <dimen name="base_editor_menu_special_item_vertical_spacing">21dp</dimen>
    <dimen name="base_editor_menu_diver_vertical_margin_top">12dp</dimen>

    <!-- 图片列表-->
    <dimen name="base_editor_menu_image_list_padding">3dp</dimen>
    <dimen name="base_editor_color_icon_layout_size">42dp</dimen>

    <dimen name="base_editor_menu_item_image_outline">0.67dp</dimen>

    <!--     普通-->
    <dimen name="base_editor_menu_item_width_height">56dp</dimen>
    <dimen name="base_editor_menu_item_top_tips_size">4dp</dimen>
    <dimen name="base_editor_menu_item_top_tips_margin_top_vertical">38dp</dimen>
    <dimen name="base_editor_menu_item_stroke_width">0.67dp</dimen>
    <dimen name="base_editor_menu_item_icon_padding">16dp</dimen>
    <dimen name="base_editor_menu_item_icon_height_width">24dp</dimen>
    <dimen name="base_editor_menu_item_full_size">72dp</dimen>
    <dimen name="base_editor_menu_item_out_border_stroke_width">2.33dp</dimen>
    <dimen name="base_editor_menu_item_out_border_gap_width">1.67dp</dimen>
    <dimen name="base_editor_menu_item_image_margin_top">8dp</dimen>
    <dimen name="base_editor_menu_item_out_progress_stroke_width">1.6dp</dimen>
    <!--base_editor_menu_item_corner_radius - base_editor_menu_item_out_progress_stroke_width/2 -->
    <dimen name="base_editor_menu_item_out_progress_radius">27.535dp</dimen>

    <!--     调节-->
    <dimen name="base_editor_menu_adjust_item_top_tips_margin_top">4dp</dimen>
    <dimen name="base_editor_menu_adjust_item_width_vertical">76dp</dimen>

    <dimen name="base_editor_menu_state_pressed_alpha">0.8</dimen>
    <dimen name="base_editor_menu_state_default_alpha">1</dimen>
    <dimen name="base_editor_menu_state_disable_alpha">0.3</dimen>
    <dimen name="base_editor_menu_state_disable_gone_alpha">0.1</dimen>
    <dimen name="base_editor_menu_text_color_unselected_alpha">0.85</dimen>

    <dimen name="base_grid_drawable_addition_text_size">11.76sp</dimen>
    <dimen name="base_sort_guide_confirm_btn_width">220dp</dimen>

    <!-- Old hidden collection -->
    <dimen name="base_old_hidden_dialog_title_size">24sp</dimen>
    <dimen name="base_old_hidden_dialog_discontinued_image_marginTop">38dp</dimen>
    <dimen name="base_old_hidden_dialog_discontinued_msg_marginHorizontal">24dp</dimen>
    <dimen name="base_old_hidden_dialog_discontinued_msg_marginTop">12dp</dimen>

    <!-- Private Atlas vertical screen-->
    <dimen name="base_dialog_safe_box_entry_tips_title_marginTop">24dp</dimen>
    <dimen name="base_dialog_safe_box_entry_tips_msg_marginHorizontal">24dp</dimen>
    <dimen name="base_dialog_safe_box_entry_tips_msg_marginTop">12dp</dimen>
    <dimen name="base_dialog_safe_box_entry_tips_msg_size">14sp</dimen>
    <dimen name="base_dialog_safe_box_dialog_button_width_small">220dp</dimen>
    <dimen name="base_dialog_safe_box_dialog_divider_width">2000dp</dimen>
    <dimen name="base_dialog_safe_box_dialog_image_margin_top">6dp</dimen>
    <dimen name="base_dialog_safe_box_dialog_scroll_view_padding_bottom">26dp</dimen>
    <dimen name="base_dialog_safe_box_dialog_text_margin_start">6dp</dimen>
    <dimen name="base_dialog_safe_box_button_margin_top">28dp</dimen>
    <dimen name="base_dialog_safe_box_text_margin_bottom">24dp</dimen>
    <dimen name="base_dialog_oldhidden_text_margin_bottom">92dp</dimen>

    <!-- Private Atlas horizontal screen-->
    <dimen name="base_dialog_landscape_safe_box_entry_tips_icon_width">160dp</dimen>
    <dimen name="base_dialog_landscape_safe_box_entry_tips_title_marginHorizontal">24dp</dimen>
    <dimen name="base_dialog_safe_box_entry_tips_button_width">44dp</dimen>
    <dimen name="base_dialog_landscape_safe_box_entry_tips_text_margin_top">49dp</dimen>

    <!--Preference-->
    <dimen name="preference_headerView_height">93dp</dimen>

    <dimen name="base_dialog_old_hidden_collection_image_with">160dp</dimen>
    <dimen name="base_dialog_old_hidden_collection_image_top">18dp</dimen>
    <dimen name="base_dialog_old_hidden_collection_btn_height">44dp</dimen>
    <dimen name="base_dialog_old_hidden_collection_statement_vertical">16dp</dimen>
    <dimen name="base_dialog_old_hidden_collection_image_size">360dp</dimen>
    <dimen name="base_dialog_old_hidden_collection_txt_exit_bottom">24dp</dimen>
    <dimen name="base_dialog_old_hidden_collection_txt_exit_bottom_land">8dp</dimen>

    <dimen name="base_editor_color_selected_border_width">2dp</dimen>
    <dimen name="base_editor_color_selected_border_radius">30dp</dimen>
    <dimen name="base_editor_color_selected_border_padding">3dp</dimen>

    <dimen name="base_editor_button_bg_radius">360dp</dimen>
    <dimen name="base_dialog_old_hidden_land_text_margin_top">29dp</dimen>
    <dimen name="base_dialog_old_hidden_land_image_margin_top">-8dp</dimen>
    <dimen name="base_dialog_old_hidden_collection_image_size_pad">324dp</dimen>
    <dimen name="base_dialog_oldhidden_text_margin_bottom_pad">47dp</dimen>

    <!--放大镜默认参数-->
    <dimen name="base_magnifier_default_width">100dp</dimen>
    <dimen name="base_magnifier_default_height">48dp</dimen>
    <dimen name="base_magnifier_default_corner">8dp</dimen>
    <dimen name="base_magnifier_default_vertical_offset">60dp</dimen>

    <dimen name="base_highlight_material_title_margin_start">16dp</dimen>
    <dimen name="base_highlight_material_title_margin_bottom">7dp</dimen>
    <dimen name="base_highlight_tool_bar_min_height">62dp</dimen>
    <dimen name="base_large_btn_width">280dp</dimen>
    <dimen name="base_large_btn_height">44dp</dimen>
    <dimen name="base_common_padding">24dp</dimen>

    <dimen name="base_ic_corner_small_size">12dp</dimen>

    <dimen name="base_year_dragging_slot">16dp</dimen>
    <dimen name="base_year_switch_animation_distance">30dp</dimen>

    <dimen name="base_dialog_safe_box_progress_size">80dp</dimen>
    <dimen name="base_dialog_safe_box_title_margin_top">12dp</dimen>
    <dimen name="base_dialog_safe_box_cancel_height">60dp</dimen>

    <!-- 抠图 start -->
    <dimen name="base_multiple_page_guides_image_field_margin_top">8dp</dimen>
    <dimen name="base_multiple_page_guides_fading_edge_length">30dp</dimen>
    <dimen name="base_multiple_page_guides_text_field_margin_start">24dp</dimen>
    <dimen name="base_multiple_page_guides_view_pager_margin_bottom">28dp</dimen>
    <dimen name="base_multiple_page_guides_divider_height">1px</dimen>
    <dimen name="base_multiple_page_guides_image_field_max_size">360dp</dimen>
    <dimen name="base_multiple_page_guides_image_field_min_size">160dp</dimen>
    <dimen name="base_multiple_page_guides_text_field_max_size">268dp</dimen>
    <dimen name="base_multiple_page_guides_text_desc_margin_top">12dp</dimen>
    <dimen name="base_multiple_page_guides_text_title_size">24sp</dimen>
    <dimen name="base_multiple_page_guides_text_desc_size">14sp</dimen>
    <dimen name="base_multiple_page_guides_text_field_padding_bottom">24dp</dimen>
    <dimen name="base_multiple_page_guides_button_text_size">16sp</dimen>
    <dimen name="base_multiple_page_guides_text_title_line_height">32sp</dimen>
    <dimen name="base_multiple_page_guides_text_desc_line_height">20sp</dimen>
    <dimen name="base_multiple_page_guides_text_field_margin_top">@dimen/base_common_padding</dimen>
    <!-- 抠图 end -->
    <dimen name="base_authority_panel_scroll_height">100dp</dimen>
    <dimen name="base_authority_panel_scroll_text_size">10sp</dimen>
    <dimen name="base_authority_panel_scroll_text_portrait_height">100dp</dimen>
    <dimen name="privacy_content_vertical_logo_margin_top">110dp</dimen>
    <dimen name="privacy_content_horizontal_logo_margin_top">80dp</dimen>
    <dimen name="base_authority_panel_policy_margin_top">12dp</dimen>
    <dimen name="base_authority_panel_bottom_button_margin_top">20dp</dimen>
    <dimen name="base_authority_panel_bottom_land_exit_margin_end">8dp</dimen>
    <dimen name="base_authority_panel_bottom_exit_margin_top">16dp</dimen>
    <dimen name="privacy_content_horizontal_logo_layout_padding_end">38dp</dimen>
    <dimen name="privacy_content_horizontal_logo_layout_padding_start">48dp</dimen>
    <dimen name="privacy_content_horizontal_layout_padding">24dp</dimen>
    <dimen name="privacy_content_bottom_layout_margin_bottom">24dp</dimen>
    <dimen name="privacy_content_vertical_margin_screen">64dp</dimen>
    <dimen name="privacy_content_vertical_app_name_margin_bottom">8dp</dimen>

    <dimen name="filing_info_text_size">12sp</dimen>
    <dimen name="filing_info_text_margin_bottom">48dp</dimen>
    <dimen name="base_toolbar_left_cancel_action_bar_text_button_padding_horizontal">12dp</dimen>
    <dimen name="base_toolbar_left_cancel_action_bar_text_button_padding_vertical">4dp</dimen>

    <!-- 人&宠主页 -->
    <dimen name="base_person_pet_group_empty_lay_padding_v">4dp</dimen>
    <dimen name="base_person_pet_group_title_padding_h">16dp</dimen>
    <dimen name="base_person_pet_group_title_padding_v">5dp</dimen>

    <!-- 一碰分享 -->
    <dimen name="base_one_touch_share_guide_title_margin_top">24dp</dimen>
    <dimen name="base_one_touch_share_guide_desc_margin_top">12dp</dimen>

    <!-- 筛选框的Item 高度 -->
    <dimen name="base_timeline_filter_item_height">32dp</dimen>

    <!-- Toolbar的paddingRight   -->
    <dimen name="toolbar_normal_menu_padding_right">@dimen/toolbar_normal_menu_padding_right_compat</dimen>

    <dimen name="base_video_duration_bg_stroke_width">0.3dp</dimen>

    <!-- 底部预览所有选项条 -->
    <dimen name="base_selection_preview_bottom_bar_padding_top_bottom">10dp</dimen>
    <dimen name="base_selection_preview_bottom_bar_padding_top_bottom_offset">8dp</dimen>
    <dimen name="base_selection_preview_bottom_bar_padding_horizontal">16dp</dimen>
    <dimen name="base_selection_preview_bottom_bar_options_padding_horizontal">16dp</dimen>
    <dimen name="base_selection_preview_bottom_bar_add_height">28dp</dimen>
    <dimen name="base_selection_preview_bottom_bar_add_text_size">14sp</dimen>

    <dimen name="base_indicator_animation_view_size">18dp</dimen>
    <item name="view_item_scale" format="float" type="dimen">1.1</item>
    <item name="album_gourp_sort_view_item_scale" format="float" type="dimen">1.03</item>

    <dimen name="label_album_set_fragment_item_view_horizontal_page_margin">16dp</dimen>
    <dimen name="label_album_set_fragment_item_view_horizontal_gap">8dp</dimen>
    <dimen name="label_album_set_fragment_item_view_top_gap">0dp</dimen>
    <dimen name="label_album_set_fragment_item_view_bottom_gap">8dp</dimen>
    <dimen name="label_album_set_item_title_margin_start">6dp</dimen>
    <dimen name="label_album_set_item_cover_width">50dp</dimen>
    <dimen name="label_album_set_item_cover_corners_radius">8dp</dimen>
    <dimen name="label_album_set_item_cover_frame_stroke_width">0.5dp</dimen>
    <dimen name="label_album_set_item_padding_start">10dp</dimen>
    <dimen name="label_album_set_item_padding_vertical">10dp</dimen>
    <dimen name="label_album_set_item_padding_end">6dp</dimen>
    <dimen name="label_album_set_item_margin_card">2dp</dimen>
    <dimen name="label_album_set_item_check_box_width">24dp</dimen>

    <dimen name="shortcut_view_recycle_padding_bottom">0dp</dimen>
    <dimen name="shortcut_view_recycle_padding_left">0dp</dimen>
    <dimen name="shortcut_view_recycle_padding_right">0dp</dimen>
    <dimen name="shortcut_view_layout_detail_edge_width">0dp</dimen>
    <dimen name="shortcut_view_layout_detail_gap_width">8dp</dimen>
    <dimen name="shortcut_view_layout_detail_gap_px_top">0dp</dimen>
    <dimen name="shortcut_view_layout_detail_gap_px_bottom">6dp</dimen>
    <dimen name="shortcut_view_layout_detail_vertical_gap">6dp</dimen>


    <dimen name="basebiz_travel_details_header_height">360dp</dimen>
    <dimen name="basebiz_travel_details_header_height_landscape_small">180dp</dimen>
    <dimen name="basebiz_travel_details_header_height_large">450dp</dimen>

    <dimen name="basebiz_travel_album_set_fragment_item_view_gap">8dp</dimen>
    <dimen name="basebiz_travel_album_set_fragment_item_edge_width">16dp</dimen>

    <dimen name="main_tab_toolbar_top_padding">10dp</dimen>
    <dimen name="main_tab_toolbar_bottom_padding">16dp</dimen>
    <dimen name="main_tab_toolbar_horizontal_padding">16dp</dimen>
    <dimen name="main_tab_toolbar_subtitle_text_size_normal">14sp</dimen>
    <dimen name="main_tab_toolbar_subtitle_text_size_select_mode">12sp</dimen>
    <dimen name="main_tab_toolbar_subtitle_margin_top">4dp</dimen>
    <dimen name="main_tab_toolbar_cancel_padding_horizontal">12dp</dimen>
    <dimen name="main_tab_toolbar_cancel_padding_vertical">4dp</dimen>
    <!-- 顶栏按钮字体大小设置为 14dp，不跟随系统字体大小变化 -->
    <dimen name="main_tab_toolbar_cancel_text_size">14dp</dimen>

    <!--图集tab页常用图集相关-->
    <dimen name="album_set_tab_commonly_used_module_padding_horizontal">16dp</dimen>

    <dimen name="selection_album_set_middle_screen_width">600dp</dimen>
    <dimen name="selection_album_set_large_screen_width">840dp</dimen>

    <dimen name="map_travel_tab_stroke_width">0.66dp</dimen>
</resources>