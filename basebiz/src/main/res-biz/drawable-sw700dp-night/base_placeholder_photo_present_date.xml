<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="152dp"
    android:height="110dp"
    android:viewportWidth="152"
    android:viewportHeight="110">
  <group>
    <clip-path
        android:pathData="M10,2L142,2A8,8 0,0 1,150 10L150,99.71A8,8 0,0 1,142 107.71L10,107.71A8,8 0,0 1,2 99.71L2,10A8,8 0,0 1,10 2z"/>
    <path
        android:pathData="M10,2L142,2A8,8 0,0 1,150 10L150,99.71A8,8 0,0 1,142 107.71L10,107.71A8,8 0,0 1,2 99.71L2,10A8,8 0,0 1,10 2z"
        android:fillColor="#2E3642"/>
    <group>
      <clip-path
          android:pathData="M6,9.47h4.5v4.5h-4.5z"/>
      <path
          android:pathData="M9.88,10.09C10.09,10.09 10.26,10.25 10.26,10.46V12.97C10.26,13.18 10.09,13.35 9.88,13.35H6.62C6.41,13.35 6.24,13.18 6.24,12.97V10.46C6.24,10.25 6.41,10.09 6.62,10.09H9.88ZM6.62,10.35C6.56,10.35 6.51,10.4 6.51,10.46V12.97C6.51,13.03 6.56,13.09 6.62,13.09H7.74V10.35H6.62ZM8.01,13.09H9.88C9.94,13.09 9.99,13.03 9.99,12.97V10.46C9.99,10.4 9.94,10.35 9.88,10.35H8.01V13.09ZM7.36,11.7C7.4,11.72 7.43,11.76 7.43,11.81C7.43,11.86 7.4,11.9 7.36,11.92L7.31,11.93H6.94C6.87,11.93 6.82,11.88 6.82,11.81C6.82,11.74 6.87,11.69 6.94,11.69H7.31L7.36,11.7ZM7.36,11.23C7.4,11.25 7.43,11.29 7.43,11.34C7.43,11.39 7.4,11.44 7.36,11.45L7.31,11.46H6.94C6.87,11.46 6.82,11.41 6.82,11.34C6.82,11.27 6.87,11.22 6.94,11.22H7.31L7.36,11.23ZM7.36,10.76C7.4,10.78 7.43,10.82 7.43,10.87C7.43,10.92 7.4,10.97 7.36,10.99L7.31,11H6.94C6.87,11 6.82,10.94 6.82,10.87C6.82,10.81 6.87,10.75 6.94,10.75H7.31L7.36,10.76Z"
          android:fillColor="#ffffff"
          android:fillAlpha="0.9"/>
    </group>
    <path
        android:pathData="M12.82,13.57C12.91,13.43 13,13.26 13.1,13.08C13.19,12.89 13.27,12.71 13.33,12.55L13.78,12.68C13.72,12.85 13.64,13.04 13.55,13.24C13.45,13.44 13.36,13.62 13.26,13.78L12.82,13.57ZM14.45,12.66C14.49,12.8 14.53,12.95 14.56,13.1C14.61,13.3 14.65,13.46 14.66,13.6L14.19,13.68C14.18,13.54 14.15,13.38 14.11,13.19C14.1,13.15 14.09,13.1 14.08,13.05C14.07,13 14.05,12.96 14.04,12.91L14,12.75L14.45,12.66ZM15.38,12.62C15.43,12.72 15.5,12.87 15.58,13.07C15.65,13.25 15.69,13.4 15.72,13.52L15.28,13.66C15.24,13.5 15.19,13.34 15.13,13.17C15.05,12.95 14.99,12.81 14.96,12.75L15.38,12.62ZM16.31,12.54C16.38,12.64 16.47,12.79 16.6,13C16.74,13.24 16.83,13.41 16.87,13.5L16.43,13.74C16.38,13.6 16.29,13.43 16.17,13.22C16.05,13.01 15.96,12.85 15.89,12.74L16.31,12.54ZM13.25,10.89H14.22V11.3H13.25V10.89ZM13.07,9.77H14.42V12.47H13.07V9.77ZM13.51,10.2V12.02H13.97V10.2H13.51ZM14.67,11.23H16.6V12.54H14.67V11.23ZM15.11,11.61V12.11H16.17V11.61H15.11ZM16.66,9.74C16.65,10.09 16.64,10.37 16.62,10.58C16.6,10.69 16.58,10.77 16.55,10.83C16.52,10.89 16.47,10.94 16.4,10.98C16.34,11.01 16.25,11.03 16.13,11.04C15.97,11.06 15.8,11.07 15.62,11.07L15.54,10.66C15.72,10.67 15.86,10.68 15.95,10.67C16.02,10.67 16.06,10.66 16.09,10.66C16.12,10.65 16.14,10.63 16.16,10.61C16.17,10.59 16.18,10.55 16.18,10.5C16.2,10.38 16.2,10.26 16.21,10.16H14.6V9.74H16.66ZM14.39,10.94C14.53,10.86 14.64,10.79 14.72,10.72C14.8,10.65 14.87,10.58 14.92,10.51C14.99,10.4 15.03,10.26 15.05,10.1L15.06,9.9H15.51L15.5,10.08C15.47,10.34 15.41,10.55 15.29,10.73C15.23,10.83 15.15,10.92 15.06,11.01C14.96,11.09 14.84,11.18 14.7,11.28L14.39,10.94ZM17.3,13.46C17.42,13.31 17.52,13.16 17.6,13.01C17.68,12.86 17.74,12.7 17.79,12.54C17.83,12.36 17.87,12.15 17.89,11.92C17.91,11.68 17.92,11.4 17.92,11.07V9.67H18.4V11.06C18.4,11.44 18.39,11.76 18.36,12.02C18.34,12.28 18.31,12.5 18.26,12.69C18.21,12.87 18.15,13.04 18.07,13.21C17.99,13.38 17.88,13.55 17.75,13.74L17.3,13.46ZM19.63,9.59H20.11V10.9H19.63V9.59ZM18.16,10.68H21.19V11.14H18.16V10.68ZM18.12,11.75H20.43V13.71H19.93V12.23H18.12V11.75Z"
        android:fillColor="#ffffff"
        android:fillAlpha="0.9"/>
    <path
        android:pathData="M134.62,11.72m-3,0a3,3 0,1 1,5.99 0a3,3 0,1 1,-5.99 0"
        android:fillColor="#525A66"/>
    <path
        android:pathData="M134.45,10.34C135.13,10.34 135.68,10.88 135.68,11.56C135.68,11.85 135.57,12.11 135.41,12.32L135.83,12.74C135.88,12.79 135.88,12.88 135.83,12.93C135.77,12.98 135.69,12.98 135.64,12.93L135.22,12.51C135.01,12.68 134.74,12.78 134.45,12.78C133.78,12.78 133.23,12.23 133.23,11.56C133.23,10.88 133.78,10.34 134.45,10.34ZM134.45,10.6C133.92,10.6 133.49,11.03 133.49,11.56C133.49,12.09 133.92,12.52 134.45,12.52C134.98,12.52 135.41,12.09 135.41,11.56C135.41,11.03 134.98,10.6 134.45,10.6Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M143,11.72m-3,0a3,3 0,1 1,5.99 0a3,3 0,1 1,-5.99 0"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M141.21,9.92h3.6v3.6h-3.6z"/>
      <path
          android:pathData="M143,11.98C143.15,11.98 143.27,11.86 143.27,11.72C143.27,11.57 143.15,11.46 143,11.46C142.86,11.46 142.74,11.57 142.74,11.72C142.74,11.86 142.86,11.98 143,11.98Z"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M143,10.91C143.15,10.91 143.27,10.8 143.27,10.65C143.27,10.51 143.15,10.39 143,10.39C142.86,10.39 142.74,10.51 142.74,10.65C142.74,10.8 142.86,10.91 143,10.91Z"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M143,13.05C143.15,13.05 143.27,12.93 143.27,12.79C143.27,12.64 143.15,12.52 143,12.52C142.86,12.52 142.74,12.64 142.74,12.79C142.74,12.93 142.86,13.05 143,13.05Z"
          android:fillColor="#ffffff"/>
    </group>
    <path
        android:pathData="M7.06,21.76C7.09,21.78 7.13,21.83 7.2,21.91L7.29,22.03C7.38,22.14 7.44,22.21 7.47,22.26L7.26,22.42C7.25,22.39 7.23,22.35 7.2,22.31C7.16,22.27 7.13,22.22 7.09,22.17C7,22.05 6.93,21.96 6.88,21.9L7.06,21.76ZM6.21,22.45H8.1V22.71C7.99,22.87 7.88,23.04 7.74,23.21C7.61,23.38 7.47,23.54 7.32,23.68L7.1,23.52C7.23,23.4 7.35,23.27 7.47,23.13C7.59,22.98 7.69,22.84 7.77,22.71H6.21V22.45ZM5.9,22.09C6.16,21.96 6.39,21.8 6.59,21.62C6.79,21.44 6.96,21.25 7.09,21.04H7.31C7.44,21.25 7.6,21.45 7.8,21.62C8,21.8 8.23,21.95 8.49,22.09L8.35,22.33C8.11,22.2 7.89,22.05 7.68,21.87C7.47,21.69 7.31,21.51 7.2,21.33C7.08,21.5 6.91,21.68 6.71,21.86C6.52,22.03 6.3,22.18 6.04,22.33L5.9,22.09ZM8.79,22.07H11.16V22.31H8.79V22.07ZM8.94,21.26H11.01V21.5H8.94V21.26ZM8.72,23.45C9.09,23.27 9.36,23.07 9.55,22.84C9.74,22.61 9.84,22.36 9.84,22.09V21.37H10.1V22.09C10.1,22.29 10.06,22.48 9.98,22.66C9.9,22.85 9.77,23.03 9.58,23.2C9.41,23.37 9.17,23.53 8.89,23.68L8.72,23.45ZM10.09,22.14C10.15,22.47 10.3,22.74 10.51,22.96C10.69,23.14 10.93,23.3 11.23,23.43L11.08,23.68C10.74,23.51 10.47,23.32 10.28,23.1C10.08,22.88 9.95,22.6 9.89,22.28L10.09,22.14Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M13.23,22.35m-0.32,0a0.32,0.32 0,1 1,0.63 0a0.32,0.32 0,1 1,-0.63 0"
        android:fillColor="#ffffff"
        android:fillAlpha="0.54"/>
    <path
        android:pathData="M14.86,23.55C14.91,23.44 14.96,23.29 15.02,23.08C15.09,22.88 15.14,22.71 15.16,22.59L15.39,22.65C15.37,22.79 15.32,22.96 15.26,23.16C15.19,23.37 15.13,23.53 15.09,23.63L14.86,23.55ZM15.5,22.38H17.23V22.62H15.5V22.38ZM16.22,22.07H16.47V23.66H16.22V22.07ZM14.93,21.79C15,21.84 15.09,21.9 15.19,21.99C15.2,22 15.22,22.01 15.23,22.02C15.25,22.03 15.26,22.05 15.27,22.06C15.32,22.09 15.37,22.14 15.43,22.2L15.28,22.41C15.21,22.33 15.13,22.26 15.04,22.18C14.94,22.1 14.86,22.03 14.78,21.98L14.93,21.79ZM15.02,21.17C15.09,21.22 15.18,21.28 15.27,21.35C15.35,21.42 15.42,21.47 15.48,21.53L15.51,21.55L15.35,21.76C15.33,21.74 15.3,21.71 15.27,21.68C15.24,21.65 15.2,21.62 15.17,21.59C15.16,21.58 15.15,21.58 15.14,21.57C15.14,21.57 15.13,21.56 15.12,21.55C15.02,21.47 14.94,21.41 14.88,21.36L15.02,21.17ZM15.31,23.33C15.52,23.2 15.7,23.07 15.83,22.93C15.97,22.78 16.07,22.62 16.15,22.44L16.31,22.55C16.23,22.75 16.12,22.94 15.98,23.1C15.85,23.26 15.67,23.41 15.46,23.55L15.31,23.33ZM16.52,22.44C16.61,22.63 16.73,22.8 16.86,22.94C17,23.08 17.16,23.21 17.35,23.31L17.2,23.54C17.03,23.43 16.87,23.29 16.72,23.12C16.57,22.95 16.46,22.76 16.38,22.56L16.52,22.44ZM15.49,22.1C15.6,22.04 15.71,21.97 15.82,21.87C15.92,21.8 16.01,21.72 16.1,21.63L16.26,21.8C16.17,21.89 16.07,21.98 15.96,22.07C15.84,22.16 15.73,22.24 15.63,22.3L15.49,22.1ZM16.55,21.63C16.62,21.66 16.73,21.73 16.89,21.83C17.02,21.93 17.13,22.01 17.21,22.08L17.07,22.28L17.03,22.25C16.95,22.18 16.85,22.1 16.74,22.02C16.61,21.93 16.51,21.86 16.43,21.81L16.55,21.63ZM15.51,21.25H17.2V21.77H16.96V21.47H15.75V21.77H15.51V21.25ZM17.63,21.77H18.44V22.02H17.63V21.77ZM17.57,23.05C17.68,23.02 17.83,22.98 18,22.92C18.21,22.85 18.35,22.8 18.43,22.77L18.47,23.03C18.35,23.08 18.22,23.13 18.05,23.19C17.85,23.25 17.72,23.29 17.64,23.31L17.57,23.05ZM17.92,21.1H18.17V23.07H17.92V21.1ZM19.11,21.25H19.35V23.47H19.11V21.25ZM19.67,21.12H19.91V23.64H19.67V21.12ZM18.18,23.56C18.35,23.32 18.46,23.12 18.5,22.95C18.53,22.78 18.55,22.49 18.55,22.07V21.13H18.8V22.06C18.8,22.5 18.77,22.83 18.71,23.06C18.69,23.17 18.65,23.27 18.6,23.38C18.55,23.48 18.49,23.59 18.41,23.7L18.18,23.56ZM21.49,21.52H21.74V23.66H21.49V21.52ZM20.4,21.4H22.83V21.64H20.4V21.4ZM21.67,21.07C21.69,21.11 21.71,21.17 21.74,21.26C21.75,21.29 21.76,21.33 21.77,21.37C21.78,21.4 21.79,21.43 21.8,21.46L21.54,21.5C21.53,21.46 21.51,21.39 21.48,21.3C21.44,21.19 21.42,21.13 21.41,21.11L21.67,21.07ZM20.62,22H22.62V23C22.62,23.08 22.61,23.14 22.59,23.19C22.57,23.24 22.55,23.27 22.51,23.29C22.47,23.32 22.42,23.34 22.35,23.35C22.22,23.36 22.1,23.36 22,23.36L21.95,23.1C22.05,23.11 22.14,23.11 22.23,23.1C22.27,23.1 22.3,23.1 22.31,23.09C22.33,23.08 22.35,23.07 22.35,23.05C22.36,23.03 22.36,23 22.36,22.95V22.24H20.87V23.37H20.62V22ZM24.02,22.04C24.03,22.06 24.04,22.07 24.06,22.09C24.11,22.15 24.14,22.19 24.15,22.2C24.2,22.26 24.24,22.31 24.27,22.37L24.09,22.5C24.06,22.45 24.02,22.39 23.97,22.32C23.93,22.26 23.89,22.21 23.85,22.17L24.02,22.04ZM24.26,21.07H24.51V21.94H24.26V21.07ZM23.16,21.35H25.61V21.59H23.16V21.35ZM23.71,22.43H25.05V22.64H23.71V22.43ZM24.26,22.53H24.5V23.56H24.26V22.53ZM23.65,22.87H25.11V23.08H23.65V22.87ZM23.31,21.83H25.45V23.26C25.45,23.35 25.44,23.42 25.42,23.47C25.41,23.52 25.38,23.56 25.34,23.58C25.3,23.6 25.24,23.62 25.17,23.62C25.05,23.63 24.94,23.63 24.84,23.63L24.8,23.38C24.87,23.39 24.96,23.39 25.06,23.39C25.1,23.39 25.14,23.39 25.16,23.38C25.18,23.37 25.19,23.36 25.19,23.34C25.2,23.32 25.21,23.29 25.21,23.24V22.05H23.56V23.63H23.31V21.83ZM24.42,22.47C24.47,22.41 24.53,22.34 24.58,22.26C24.62,22.21 24.66,22.14 24.72,22.05L24.91,22.16C24.86,22.24 24.81,22.31 24.77,22.38C24.71,22.46 24.65,22.53 24.6,22.59L24.42,22.47ZM26.13,21.56H26.38V23.29H28V21.56H28.25V23.55H26.13V21.56ZM27.06,21.11H27.32V23.42H27.06V21.11ZM29.53,21.56C29.74,21.72 30,21.95 30.29,22.24C30.6,22.55 30.83,22.81 31,23.03L30.79,23.22C30.65,23.02 30.42,22.77 30.1,22.44C29.8,22.14 29.55,21.9 29.35,21.74L29.53,21.56ZM29.24,22.99C29.51,22.78 29.77,22.55 30.01,22.3C30.26,22.04 30.48,21.78 30.66,21.53L30.88,21.69C30.68,21.97 30.45,22.23 30.21,22.48C29.95,22.76 29.68,23.01 29.42,23.21L29.24,22.99ZM28.93,21.22H31.14V21.47H29.19V23.3H31.19V23.55H28.93V21.22Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M2,25.21h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M2,25.21h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M2.04,25.24h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M23.43,25.21h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M23.43,25.21h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M23.47,25.24h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M44.86,25.21h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M44.86,25.21h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M44.89,25.24h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M66.29,25.21h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M66.29,25.21h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M66.32,25.24h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M87.72,25.21h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M87.72,25.21h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M87.75,25.24h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M109.15,25.21h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M109.15,25.21h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M109.18,25.24h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M130.58,25.21h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M130.58,25.21h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M130.61,25.24h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M2,46.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M2,46.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M2.04,46.67h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M23.43,46.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M23.43,46.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M23.47,46.67h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M44.86,46.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M44.86,46.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M44.89,46.67h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M66.29,46.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M66.29,46.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M66.32,46.67h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M87.72,46.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M87.72,46.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M87.75,46.67h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M109.15,46.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M109.15,46.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M109.18,46.67h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M130.58,46.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M130.58,46.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M130.61,46.67h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M6.17,70.54H6.75V70.77H6.17V70.54ZM7.38,69.93H7.63V71.97H7.38V69.93ZM6.06,69.62H6.88V71.72H6.06V69.62ZM6.29,69.86V71.48H6.65V69.86H6.29ZM6.8,70.43C7.03,70.07 7.19,69.72 7.29,69.38L7.54,69.43C7.47,69.65 7.39,69.86 7.3,70.05C7.22,70.24 7.12,70.42 7.02,70.58L6.8,70.43ZM7.49,70.44H8.31V70.68H7.49V70.44ZM7.2,69.78H8.44V70.02H7.2V69.78ZM7.48,71.12H8.37V71.36H7.48V71.12ZM8.79,70.37H11.16V70.61H8.79V70.37ZM8.94,69.56H11.01V69.8H8.94V69.56ZM8.72,71.75C9.09,71.57 9.36,71.37 9.55,71.14C9.74,70.91 9.84,70.66 9.84,70.39V69.67H10.1V70.39C10.1,70.59 10.06,70.78 9.98,70.97C9.9,71.15 9.77,71.33 9.58,71.5C9.41,71.67 9.17,71.84 8.89,71.98L8.72,71.75ZM10.09,70.44C10.15,70.77 10.3,71.04 10.51,71.26C10.69,71.44 10.93,71.6 11.23,71.73L11.08,71.98C10.74,71.81 10.47,71.62 10.28,71.4C10.08,71.18 9.95,70.9 9.89,70.58L10.09,70.44Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M13.23,70.65m-0.32,0a0.32,0.32 0,1 1,0.63 0a0.32,0.32 0,1 1,-0.63 0"
        android:fillColor="#ffffff"
        android:fillAlpha="0.54"/>
    <path
        android:pathData="M14.86,71.85C14.91,71.74 14.96,71.59 15.02,71.39C15.09,71.18 15.14,71.01 15.16,70.89L15.39,70.95C15.37,71.1 15.32,71.26 15.26,71.46C15.19,71.67 15.13,71.83 15.09,71.93L14.86,71.85ZM15.5,70.69H17.23V70.92H15.5V70.69ZM16.22,70.37H16.47V71.96H16.22V70.37ZM14.93,70.09C15,70.14 15.09,70.2 15.19,70.29C15.2,70.3 15.22,70.31 15.23,70.32C15.25,70.34 15.26,70.35 15.27,70.36C15.32,70.39 15.37,70.44 15.43,70.5L15.28,70.71C15.21,70.64 15.13,70.56 15.04,70.48C14.94,70.4 14.86,70.33 14.78,70.28L14.93,70.09ZM15.02,69.47C15.09,69.52 15.18,69.58 15.27,69.66C15.35,69.72 15.42,69.78 15.48,69.83L15.51,69.85L15.35,70.06C15.33,70.04 15.3,70.01 15.27,69.98C15.24,69.95 15.2,69.92 15.17,69.89C15.16,69.88 15.15,69.88 15.14,69.87C15.14,69.87 15.13,69.86 15.12,69.85C15.02,69.77 14.94,69.71 14.88,69.66L15.02,69.47ZM15.31,71.63C15.52,71.5 15.7,71.37 15.83,71.23C15.97,71.08 16.07,70.92 16.15,70.74L16.31,70.85C16.23,71.05 16.12,71.24 15.98,71.4C15.85,71.56 15.67,71.71 15.46,71.85L15.31,71.63ZM16.52,70.74C16.61,70.93 16.73,71.1 16.86,71.24C17,71.38 17.16,71.51 17.35,71.61L17.2,71.84C17.03,71.73 16.87,71.59 16.72,71.42C16.57,71.25 16.46,71.06 16.38,70.86L16.52,70.74ZM15.49,70.41C15.6,70.34 15.71,70.27 15.82,70.17C15.92,70.1 16.01,70.02 16.1,69.93L16.26,70.1C16.17,70.19 16.07,70.28 15.96,70.37C15.84,70.46 15.73,70.54 15.63,70.6L15.49,70.41ZM16.55,69.93C16.62,69.96 16.73,70.03 16.89,70.13C17.02,70.23 17.13,70.31 17.21,70.38L17.07,70.58L17.03,70.55C16.95,70.48 16.85,70.4 16.74,70.32C16.61,70.23 16.51,70.16 16.43,70.11L16.55,69.93ZM15.51,69.55H17.2V70.08H16.96V69.78H15.75V70.07H15.51V69.55ZM17.63,70.07H18.44V70.32H17.63V70.07ZM17.57,71.35C17.68,71.32 17.83,71.28 18,71.22C18.21,71.15 18.35,71.1 18.43,71.07L18.47,71.34C18.35,71.38 18.22,71.43 18.05,71.49C17.85,71.55 17.72,71.59 17.64,71.61L17.57,71.35ZM17.92,69.4H18.17V71.37H17.92V69.4ZM19.11,69.55H19.35V71.77H19.11V69.55ZM19.67,69.43H19.91V71.94H19.67V69.43ZM18.18,71.86C18.35,71.62 18.46,71.42 18.5,71.25C18.53,71.08 18.55,70.79 18.55,70.37V69.43H18.8V70.36C18.8,70.8 18.77,71.13 18.71,71.36C18.69,71.47 18.65,71.57 18.6,71.68C18.55,71.78 18.49,71.89 18.41,72L18.18,71.86ZM21.49,69.82H21.74V71.96H21.49V69.82ZM20.4,69.7H22.83V69.94H20.4V69.7ZM21.67,69.37C21.69,69.41 21.71,69.47 21.74,69.56C21.75,69.59 21.76,69.63 21.77,69.67C21.78,69.7 21.79,69.73 21.8,69.76L21.54,69.8C21.53,69.76 21.51,69.69 21.48,69.6C21.44,69.49 21.42,69.43 21.41,69.41L21.67,69.37ZM20.62,70.3H22.62V71.3C22.62,71.38 22.61,71.45 22.59,71.49C22.57,71.54 22.55,71.57 22.51,71.6C22.47,71.62 22.42,71.64 22.35,71.65C22.22,71.66 22.1,71.66 22,71.66L21.95,71.4C22.05,71.41 22.14,71.41 22.23,71.41C22.27,71.4 22.3,71.4 22.31,71.39C22.33,71.38 22.35,71.37 22.35,71.35C22.36,71.33 22.36,71.3 22.36,71.25V70.54H20.87V71.67H20.62V70.3ZM24.02,70.34C24.03,70.36 24.04,70.37 24.06,70.39C24.11,70.45 24.14,70.49 24.15,70.5C24.2,70.56 24.24,70.61 24.27,70.67L24.09,70.8C24.06,70.75 24.02,70.69 23.97,70.62C23.93,70.56 23.89,70.51 23.85,70.47L24.02,70.34ZM24.26,69.38H24.51V70.24H24.26V69.38ZM23.16,69.66H25.61V69.89H23.16V69.66ZM23.71,70.73H25.05V70.94H23.71V70.73ZM24.26,70.83H24.5V71.86H24.26V70.83ZM23.65,71.17H25.11V71.38H23.65V71.17ZM23.31,70.13H25.45V71.56C25.45,71.65 25.44,71.72 25.42,71.77C25.41,71.82 25.38,71.86 25.34,71.88C25.3,71.9 25.24,71.92 25.17,71.92C25.05,71.93 24.94,71.94 24.84,71.93L24.8,71.68C24.87,71.69 24.96,71.69 25.06,71.69C25.1,71.69 25.14,71.69 25.16,71.68C25.18,71.67 25.19,71.66 25.19,71.64C25.2,71.62 25.21,71.59 25.21,71.55V70.36H23.56V71.93H23.31V70.13ZM24.42,70.77C24.47,70.71 24.53,70.64 24.58,70.56C24.62,70.51 24.66,70.44 24.72,70.35L24.91,70.46C24.86,70.54 24.81,70.62 24.77,70.68C24.71,70.76 24.65,70.83 24.6,70.89L24.42,70.77ZM26.13,69.86H26.38V71.6H28V69.86H28.25V71.85H26.13V69.86ZM27.06,69.41H27.32V71.72H27.06V69.41ZM29.53,69.86C29.74,70.02 30,70.25 30.29,70.55C30.6,70.85 30.83,71.11 31,71.33L30.79,71.52C30.65,71.33 30.42,71.07 30.1,70.74C29.8,70.44 29.55,70.2 29.35,70.04L29.53,69.86ZM29.24,71.29C29.51,71.08 29.77,70.85 30.01,70.6C30.26,70.34 30.48,70.08 30.66,69.83L30.88,69.99C30.68,70.27 30.45,70.53 30.21,70.78C29.95,71.07 29.68,71.31 29.42,71.51L29.24,71.29ZM28.93,69.52H31.14V69.78H29.19V71.6H31.19V71.85H28.93V69.52Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M2,73.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M2,73.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M2.04,73.68h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M23.43,73.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M23.43,73.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M23.47,73.68h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M44.86,73.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M44.86,73.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M44.89,73.68h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M66.29,73.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M66.29,73.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M66.32,73.68h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M87.72,73.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M87.72,73.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M87.75,73.68h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M109.15,73.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M109.15,73.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M109.18,73.68h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M130.58,73.64h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M130.58,73.64h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M130.61,73.68h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M2,95.07h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M2,95.07h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M2.04,95.11h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M23.43,95.07h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M23.43,95.07h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M23.47,95.11h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M44.86,95.07h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M44.86,95.07h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M44.89,95.11h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M66.29,95.07h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M66.29,95.07h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M66.32,95.11h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M87.72,95.07h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M87.72,95.07h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M87.75,95.11h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M109.15,95.07h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M109.15,95.07h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M109.18,95.11h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M130.58,95.07h21.03v21.03h-21.03z"
        android:fillColor="#525A66"/>
    <group>
      <clip-path
          android:pathData="M130.58,95.07h21.03v21.03h-21.03z"/>
    </group>
    <path
        android:pathData="M130.61,95.11h20.96v20.96h-20.96z"
        android:strokeAlpha="0.04"
        android:strokeWidth="0.0709863"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M150.8,107.71l-149.61,-0l-0,-33.4l149.61,-0z"
        android:fillAlpha="0.15">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="76"
            android:startY="74.31"
            android:endX="76"
            android:endY="107.71"
            android:type="linear">
          <item android:offset="0" android:color="#00000000"/>
          <item android:offset="0.72" android:color="#B8000000"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M45.4,93.71L45.4,93.71A4.5,4.5 0,0 1,49.9 98.21L49.9,98.21A4.5,4.5 0,0 1,45.4 102.71L45.4,102.71A4.5,4.5 0,0 1,40.9 98.21L40.9,98.21A4.5,4.5 0,0 1,45.4 93.71z"
        android:fillColor="#82868E"/>
    <group>
      <clip-path
          android:pathData="M43.38,96.18h4.05v4.05h-4.05z"/>
      <path
          android:pathData="M46.97,96.83C47.01,96.77 46.98,96.69 46.9,96.69H43.9C43.83,96.69 43.79,96.77 43.83,96.83L45,98.18C45.02,98.2 45.03,98.22 45.03,98.24V99.47C45.03,99.5 45.04,99.53 45.07,99.54L45.65,99.83C45.71,99.86 45.78,99.82 45.78,99.76V98.24C45.78,98.22 45.78,98.2 45.8,98.18L46.97,96.83Z"
          android:strokeAlpha="0.9"
          android:strokeLineJoin="round"
          android:strokeWidth="0.118125"
          android:fillColor="#ffffff"
          android:fillAlpha="0.9"
          android:fillType="evenOdd"
          android:strokeColor="#ffffff"
          android:strokeLineCap="round"/>
    </group>
    <path
        android:pathData="M61.6,93.71L90.4,93.71A4.5,4.5 0,0 1,94.9 98.21L94.9,98.21A4.5,4.5 0,0 1,90.4 102.71L61.6,102.71A4.5,4.5 0,0 1,57.1 98.21L57.1,98.21A4.5,4.5 0,0 1,61.6 93.71z"
        android:fillColor="#82868E"/>
    <path
        android:pathData="M61.8,94.41L72.2,94.41A3.8,3.8 0,0 1,76 98.21L76,98.21A3.8,3.8 0,0 1,72.2 102.01L61.8,102.01A3.8,3.8 0,0 1,58 98.21L58,98.21A3.8,3.8 0,0 1,61.8 94.41z"
        android:fillColor="#585E68"/>
    <group>
      <clip-path
          android:pathData="M61.8,94.41L72.2,94.41A3.8,3.8 0,0 1,76 98.21L76,98.21A3.8,3.8 0,0 1,72.2 102.01L61.8,102.01A3.8,3.8 0,0 1,58 98.21L58,98.21A3.8,3.8 0,0 1,61.8 94.41z"/>
      <path
          android:pathData="M64.44,99.42C64.49,99.33 64.55,99.23 64.6,99.12C64.66,99.01 64.71,98.9 64.74,98.8L65.01,98.88C64.98,98.99 64.93,99.1 64.87,99.22C64.82,99.34 64.76,99.45 64.7,99.54L64.44,99.42ZM65.41,98.87C65.44,98.96 65.47,99.05 65.48,99.14C65.52,99.26 65.53,99.36 65.54,99.44L65.26,99.49C65.25,99.4 65.24,99.3 65.21,99.19C65.21,99.16 65.2,99.14 65.19,99.11C65.19,99.08 65.18,99.05 65.17,99.02L65.14,98.93L65.41,98.87ZM65.98,98.85C66.01,98.91 66.05,99 66.09,99.12C66.13,99.23 66.16,99.32 66.18,99.39L65.92,99.47C65.89,99.38 65.86,99.28 65.82,99.18C65.77,99.05 65.74,98.97 65.72,98.93L65.98,98.85ZM66.53,98.8C66.57,98.86 66.63,98.95 66.71,99.08C66.79,99.22 66.85,99.32 66.87,99.37L66.6,99.52C66.57,99.44 66.52,99.33 66.45,99.21C66.38,99.08 66.32,98.99 66.28,98.92L66.53,98.8ZM64.7,97.81H65.28V98.06H64.7V97.81ZM64.59,97.14H65.4V98.76H64.59V97.14ZM64.85,97.4V98.49H65.13V97.4H64.85ZM65.55,98.02H66.71V98.8H65.55V98.02ZM65.81,98.25V98.54H66.45V98.25H65.81ZM66.74,97.12C66.74,97.33 66.73,97.5 66.72,97.63C66.71,97.69 66.7,97.74 66.68,97.78C66.66,97.81 66.63,97.84 66.59,97.86C66.55,97.88 66.49,97.9 66.42,97.9C66.33,97.92 66.23,97.92 66.12,97.92L66.07,97.67C66.18,97.68 66.26,97.68 66.32,97.68C66.36,97.68 66.38,97.67 66.4,97.67C66.42,97.67 66.43,97.66 66.44,97.64C66.45,97.63 66.45,97.61 66.46,97.58C66.46,97.51 66.47,97.44 66.47,97.37H65.51V97.12H66.74ZM65.38,97.84C65.46,97.79 65.53,97.75 65.58,97.71C65.63,97.67 65.67,97.63 65.7,97.58C65.74,97.52 65.77,97.43 65.78,97.34L65.78,97.21H66.05L66.04,97.33C66.03,97.48 65.99,97.61 65.92,97.72C65.89,97.77 65.84,97.83 65.78,97.88C65.72,97.93 65.65,97.99 65.57,98.04L65.38,97.84ZM67.12,99.36C67.2,99.26 67.26,99.17 67.31,99.08C67.35,98.99 67.39,98.9 67.42,98.8C67.45,98.69 67.47,98.57 67.48,98.43C67.49,98.29 67.5,98.12 67.5,97.92V97.08H67.79V97.91C67.79,98.14 67.78,98.33 67.76,98.49C67.75,98.65 67.73,98.78 67.7,98.89C67.68,99 67.64,99.1 67.59,99.2C67.54,99.3 67.48,99.41 67.4,99.52L67.12,99.36ZM68.53,97.03H68.81V97.82H68.53V97.03ZM67.64,97.68H69.46V97.96H67.64V97.68ZM67.62,98.32H69V99.5H68.71V98.61H67.62V98.32Z"
          android:fillColor="#ffffff"
          android:fillAlpha="0.9"/>
    </group>
    <group>
      <clip-path
          android:pathData="M79.8,94.41L90.2,94.41A3.8,3.8 0,0 1,94 98.21L94,98.21A3.8,3.8 0,0 1,90.2 102.01L79.8,102.01A3.8,3.8 0,0 1,76 98.21L76,98.21A3.8,3.8 0,0 1,79.8 94.41z"/>
      <path
          android:pathData="M82.57,97.12H84.74V99.45H82.57V97.12ZM82.85,97.37V99.19H84.47V97.37H82.85ZM83.29,98.35C83.4,98.37 83.53,98.39 83.67,98.42C83.83,98.46 83.96,98.5 84.07,98.53L84,98.75C83.9,98.71 83.77,98.68 83.63,98.65C83.47,98.61 83.35,98.59 83.25,98.57L83.29,98.35ZM83.1,98.7C83.23,98.71 83.4,98.74 83.62,98.79C83.84,98.84 84.03,98.88 84.18,98.94L84.11,99.16C83.98,99.11 83.8,99.06 83.58,99.01C83.37,98.97 83.19,98.94 83.05,98.92L83.1,98.7ZM82.85,97.93C82.94,97.87 83.02,97.78 83.12,97.67C83.2,97.59 83.26,97.5 83.32,97.4L83.54,97.49C83.48,97.59 83.4,97.69 83.31,97.8C83.21,97.91 83.12,98.01 83.01,98.09L82.85,97.93ZM83.31,97.72C83.37,97.79 83.44,97.86 83.51,97.92C83.59,97.98 83.67,98.03 83.77,98.07C83.95,98.16 84.18,98.23 84.45,98.28L84.36,98.52C84.05,98.44 83.8,98.34 83.6,98.23C83.51,98.17 83.42,98.11 83.34,98.05C83.26,97.98 83.18,97.9 83.11,97.82L83.31,97.72ZM82.85,98.3C82.98,98.27 83.1,98.24 83.21,98.2C83.32,98.16 83.43,98.11 83.54,98.07C83.62,98.02 83.7,97.98 83.77,97.94C83.84,97.89 83.91,97.85 83.96,97.8H83.22L83.27,97.6H84.31V97.8C84.12,97.97 83.91,98.12 83.7,98.24C83.6,98.29 83.48,98.34 83.36,98.39C83.23,98.44 83.1,98.49 82.95,98.53L82.85,98.3ZM85.21,98.64H87.48V98.87H85.21V98.64ZM86.2,98.5H86.49V99.49H86.2V98.5ZM85.45,97.42L85.73,97.33V98.54H85.45V97.42ZM86.35,97.36H86.63V98.34H86.35V97.36ZM85.62,98.25H87.44V98.45H85.62V98.25ZM85.62,97.28H87.39V97.47H85.62V97.28ZM85.62,97.6H87.33V97.79H85.62V97.6ZM85.62,97.92H87.33V98.11H85.62V97.92ZM85.13,99.22C85.34,99.17 85.52,99.1 85.67,99.02C85.82,98.94 85.94,98.84 86.03,98.73L86.22,98.86C86.17,98.94 86.11,99.01 86.05,99.07C85.98,99.13 85.91,99.18 85.82,99.23C85.65,99.33 85.47,99.41 85.26,99.47L85.13,99.22ZM86.63,98.71C86.71,98.82 86.83,98.92 86.99,99C87.16,99.09 87.35,99.16 87.56,99.21L87.43,99.47C87.22,99.4 87.03,99.32 86.87,99.22C86.7,99.13 86.57,99.01 86.47,98.86L86.63,98.71ZM85.08,97.76C85.2,97.65 85.32,97.53 85.42,97.38C85.51,97.26 85.59,97.13 85.66,96.99L85.92,97.1C85.84,97.26 85.75,97.4 85.65,97.54C85.54,97.69 85.42,97.83 85.28,97.96L85.08,97.76ZM86.56,97C86.58,97.06 86.6,97.12 86.62,97.17C86.65,97.26 86.66,97.32 86.67,97.35L86.39,97.39C86.37,97.32 86.35,97.27 86.34,97.22C86.34,97.21 86.33,97.2 86.33,97.19C86.33,97.18 86.32,97.17 86.32,97.16C86.31,97.14 86.31,97.13 86.3,97.11C86.29,97.1 86.29,97.08 86.28,97.07L86.56,97Z"
          android:fillColor="#ffffff"
          android:fillAlpha="0.54"/>
      <path
          android:pathData="M82.57,97.12H84.74V99.45H82.57V97.12ZM82.85,97.37V99.19H84.47V97.37H82.85ZM83.29,98.35C83.4,98.37 83.53,98.39 83.67,98.42C83.83,98.46 83.96,98.5 84.07,98.53L84,98.75C83.9,98.71 83.77,98.68 83.63,98.65C83.47,98.61 83.35,98.59 83.25,98.57L83.29,98.35ZM83.1,98.7C83.23,98.71 83.4,98.74 83.62,98.79C83.84,98.84 84.03,98.88 84.18,98.94L84.11,99.16C83.98,99.11 83.8,99.06 83.58,99.01C83.37,98.97 83.19,98.94 83.05,98.92L83.1,98.7ZM82.85,97.93C82.94,97.87 83.02,97.78 83.12,97.67C83.2,97.59 83.26,97.5 83.32,97.4L83.54,97.49C83.48,97.59 83.4,97.69 83.31,97.8C83.21,97.91 83.12,98.01 83.01,98.09L82.85,97.93ZM83.31,97.72C83.37,97.79 83.44,97.86 83.51,97.92C83.59,97.98 83.67,98.03 83.77,98.07C83.95,98.16 84.18,98.23 84.45,98.28L84.36,98.52C84.05,98.44 83.8,98.34 83.6,98.23C83.51,98.17 83.42,98.11 83.34,98.05C83.26,97.98 83.18,97.9 83.11,97.82L83.31,97.72ZM82.85,98.3C82.98,98.27 83.1,98.24 83.21,98.2C83.32,98.16 83.43,98.11 83.54,98.07C83.62,98.02 83.7,97.98 83.77,97.94C83.84,97.89 83.91,97.85 83.96,97.8H83.22L83.27,97.6H84.31V97.8C84.12,97.97 83.91,98.12 83.7,98.24C83.6,98.29 83.48,98.34 83.36,98.39C83.23,98.44 83.1,98.49 82.95,98.53L82.85,98.3ZM85.21,98.64H87.48V98.87H85.21V98.64ZM86.2,98.5H86.49V99.49H86.2V98.5ZM85.45,97.42L85.73,97.33V98.54H85.45V97.42ZM86.35,97.36H86.63V98.34H86.35V97.36ZM85.62,98.25H87.44V98.45H85.62V98.25ZM85.62,97.28H87.39V97.47H85.62V97.28ZM85.62,97.6H87.33V97.79H85.62V97.6ZM85.62,97.92H87.33V98.11H85.62V97.92ZM85.13,99.22C85.34,99.17 85.52,99.1 85.67,99.02C85.82,98.94 85.94,98.84 86.03,98.73L86.22,98.86C86.17,98.94 86.11,99.01 86.05,99.07C85.98,99.13 85.91,99.18 85.82,99.23C85.65,99.33 85.47,99.41 85.26,99.47L85.13,99.22ZM86.63,98.71C86.71,98.82 86.83,98.92 86.99,99C87.16,99.09 87.35,99.16 87.56,99.21L87.43,99.47C87.22,99.4 87.03,99.32 86.87,99.22C86.7,99.13 86.57,99.01 86.47,98.86L86.63,98.71ZM85.08,97.76C85.2,97.65 85.32,97.53 85.42,97.38C85.51,97.26 85.59,97.13 85.66,96.99L85.92,97.1C85.84,97.26 85.75,97.4 85.65,97.54C85.54,97.69 85.42,97.83 85.28,97.96L85.08,97.76ZM86.56,97C86.58,97.06 86.6,97.12 86.62,97.17C86.65,97.26 86.66,97.32 86.67,97.35L86.39,97.39C86.37,97.32 86.35,97.27 86.34,97.22C86.34,97.21 86.33,97.2 86.33,97.19C86.33,97.18 86.32,97.17 86.32,97.16C86.31,97.14 86.31,97.13 86.3,97.11C86.29,97.1 86.29,97.08 86.28,97.07L86.56,97Z"
          android:fillColor="#ffffff"
          android:fillAlpha="0.9"/>
    </group>
    <path
        android:pathData="M106.6,93.71L106.6,93.71A4.5,4.5 0,0 1,111.1 98.21L111.1,98.21A4.5,4.5 0,0 1,106.6 102.71L106.6,102.71A4.5,4.5 0,0 1,102.1 98.21L102.1,98.21A4.5,4.5 0,0 1,106.6 93.71z"
        android:fillColor="#82868E"/>
    <group>
      <clip-path
          android:pathData="M104.57,96.18h4.05v4.05h-4.05z"/>
      <path
          android:pathData="M105.5,99.01C105.61,99.01 105.66,99.01 105.7,99.03C105.73,99.05 105.76,99.08 105.78,99.11C105.8,99.15 105.8,99.2 105.8,99.31V99.51C105.8,99.61 105.8,99.66 105.78,99.7C105.76,99.74 105.73,99.77 105.7,99.79C105.66,99.81 105.61,99.81 105.5,99.81H105.3C105.2,99.81 105.15,99.81 105.11,99.79C105.07,99.77 105.04,99.74 105.02,99.7C105,99.66 105,99.61 105,99.51V99.31C105,99.2 105,99.15 105.02,99.11C105.04,99.08 105.07,99.05 105.11,99.03C105.15,99.01 105.2,99.01 105.3,99.01H105.5ZM106.7,99.01C106.8,99.01 106.85,99.01 106.89,99.03C106.93,99.05 106.96,99.08 106.98,99.11C107,99.15 107,99.2 107,99.31V99.51C107,99.61 107,99.66 106.98,99.7C106.96,99.74 106.93,99.77 106.89,99.79C106.85,99.81 106.8,99.81 106.7,99.81H106.5C106.4,99.81 106.34,99.81 106.31,99.79C106.27,99.77 106.24,99.74 106.22,99.7C106.2,99.66 106.2,99.61 106.2,99.51V99.31C106.2,99.2 106.2,99.15 106.22,99.11C106.24,99.08 106.27,99.05 106.31,99.03C106.34,99.01 106.4,99.01 106.5,99.01H106.7ZM107.9,99.01C108,99.01 108.05,99.01 108.09,99.03C108.13,99.05 108.16,99.08 108.18,99.11C108.19,99.15 108.19,99.2 108.19,99.31V99.51C108.19,99.61 108.19,99.66 108.18,99.7C108.16,99.74 108.13,99.77 108.09,99.79C108.05,99.81 108,99.81 107.9,99.81H107.69C107.59,99.81 107.54,99.81 107.5,99.79C107.46,99.77 107.43,99.74 107.42,99.7C107.4,99.66 107.4,99.61 107.4,99.51V99.31C107.4,99.2 107.4,99.15 107.42,99.11C107.43,99.08 107.46,99.05 107.5,99.03C107.54,99.01 107.59,99.01 107.69,99.01H107.9ZM105.5,97.81C105.61,97.81 105.66,97.81 105.7,97.83C105.73,97.85 105.76,97.88 105.78,97.92C105.8,97.96 105.8,98.01 105.8,98.11V98.31C105.8,98.41 105.8,98.47 105.78,98.5C105.76,98.54 105.73,98.57 105.7,98.59C105.66,98.61 105.61,98.61 105.5,98.61H105.3C105.2,98.61 105.15,98.61 105.11,98.59C105.07,98.57 105.04,98.54 105.02,98.5C105,98.47 105,98.41 105,98.31V98.11C105,98.01 105,97.96 105.02,97.92C105.04,97.88 105.07,97.85 105.11,97.83C105.15,97.81 105.2,97.81 105.3,97.81H105.5ZM106.7,97.81C106.8,97.81 106.85,97.81 106.89,97.83C106.93,97.85 106.96,97.88 106.98,97.92C107,97.96 107,98.01 107,98.11V98.31C107,98.41 107,98.47 106.98,98.5C106.96,98.54 106.93,98.57 106.89,98.59C106.85,98.61 106.8,98.61 106.7,98.61H106.5C106.4,98.61 106.34,98.61 106.31,98.59C106.27,98.57 106.24,98.54 106.22,98.5C106.2,98.47 106.2,98.41 106.2,98.31V98.11C106.2,98.01 106.2,97.96 106.22,97.92C106.24,97.88 106.27,97.85 106.31,97.83C106.34,97.81 106.4,97.81 106.5,97.81H106.7ZM107.9,97.81C108,97.81 108.05,97.81 108.09,97.83C108.13,97.85 108.16,97.88 108.18,97.92C108.19,97.96 108.19,98.01 108.19,98.11V98.31C108.19,98.41 108.19,98.47 108.18,98.5C108.16,98.54 108.13,98.57 108.09,98.59C108.05,98.61 108,98.61 107.9,98.61H107.69C107.59,98.61 107.54,98.61 107.5,98.59C107.46,98.57 107.43,98.54 107.42,98.5C107.4,98.47 107.4,98.41 107.4,98.31V98.11C107.4,98.01 107.4,97.96 107.42,97.92C107.43,97.88 107.46,97.85 107.5,97.83C107.54,97.81 107.59,97.81 107.69,97.81H107.9ZM105.5,96.62C105.61,96.62 105.66,96.62 105.7,96.63C105.73,96.65 105.76,96.68 105.78,96.72C105.8,96.76 105.8,96.81 105.8,96.91V97.11C105.8,97.22 105.8,97.27 105.78,97.31C105.76,97.35 105.73,97.38 105.7,97.39C105.66,97.41 105.61,97.41 105.5,97.41H105.3C105.2,97.41 105.15,97.41 105.11,97.39C105.07,97.38 105.04,97.35 105.02,97.31C105,97.27 105,97.22 105,97.11V96.91C105,96.81 105,96.76 105.02,96.72C105.04,96.68 105.07,96.65 105.11,96.63C105.15,96.62 105.2,96.62 105.3,96.62H105.5ZM106.7,96.62C106.8,96.62 106.85,96.62 106.89,96.63C106.93,96.65 106.96,96.68 106.98,96.72C107,96.76 107,96.81 107,96.91V97.11C107,97.22 107,97.27 106.98,97.31C106.96,97.35 106.93,97.38 106.89,97.39C106.85,97.41 106.8,97.41 106.7,97.41H106.5C106.4,97.41 106.34,97.41 106.31,97.39C106.27,97.38 106.24,97.35 106.22,97.31C106.2,97.27 106.2,97.22 106.2,97.11V96.91C106.2,96.81 106.2,96.76 106.22,96.72C106.24,96.68 106.27,96.65 106.31,96.63C106.34,96.62 106.4,96.62 106.5,96.62H106.7ZM107.9,96.62C108,96.62 108.05,96.62 108.09,96.63C108.13,96.65 108.16,96.68 108.18,96.72C108.19,96.76 108.19,96.81 108.19,96.91V97.11C108.19,97.22 108.19,97.27 108.18,97.31C108.16,97.35 108.13,97.38 108.09,97.39C108.05,97.41 108,97.41 107.9,97.41H107.69C107.59,97.41 107.54,97.41 107.5,97.39C107.46,97.38 107.43,97.35 107.42,97.31C107.4,97.27 107.4,97.22 107.4,97.11V96.91C107.4,96.81 107.4,96.76 107.42,96.72C107.43,96.68 107.46,96.65 107.5,96.63C107.54,96.62 107.59,96.62 107.69,96.62H107.9Z"
          android:fillColor="#ffffff"
          android:fillAlpha="0.9"/>
    </group>
  </group>
  <path
      android:pathData="M10,1.2L142,1.2A8.8,8.8 0,0 1,150.8 10L150.8,99.71A8.8,8.8 0,0 1,142 108.51L10,108.51A8.8,8.8 0,0 1,1.2 99.71L1.2,10A8.8,8.8 0,0 1,10 1.2z"
      android:strokeWidth="1.6"
      android:fillColor="#00000000"
      android:strokeColor="#666666"/>
</vector>
