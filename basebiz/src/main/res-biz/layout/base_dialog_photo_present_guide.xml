<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.dialog.widget.COUIMaxHeightNestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scroll_view"
    style="@style/COUIAlertDialogCustomScrollViewStyle"
    tools:ignore="ResourceName">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/scroll_view_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/base_present_type_guide_padding_vertical">

            <com.oplus.gallery.basebiz.widget.PresentTypeGuideView
                android:id="@+id/child_view_photo_present_guide_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/base_present_type_guide_margin_middle"
                android:layout_weight="1"
                android:gravity="end|center_vertical"
                android:src="@drawable/base_placeholder_photo_present_date_current"
                android:text="@string/base_photos_present_type_date"
                app:presentType="date" />

            <com.oplus.gallery.basebiz.widget.PresentTypeGuideView
                android:id="@+id/child_view_photo_present_guide_immerse"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_present_type_guide_margin_middle"
                android:layout_weight="1"
                android:gravity="start|center_vertical"
                android:src="@drawable/base_placeholder_photo_present_immerse"
                android:text="@string/base_photos_present_type_immerse"
                app:presentType="immersive" />
        </LinearLayout>

        <com.coui.appcompat.dialog.widget.COUIDialogTitle
            android:id="@+id/custom_title"
            style="@style/COUIAlertDialogTitleStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_present_type_guide_title_margin_top"
            android:gravity="center_horizontal"
            android:paddingStart="@dimen/coui_alert_dialog_message_padding_left"
            android:paddingEnd="@dimen/coui_alert_dialog_message_padding_left"
            android:text="@string/base_photos_present_type_guide_title" />

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/custom_message"
            style="@style/COUIAlertDialogMessageStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/coui_alert_dialog_customer_layout_textview1_margin_top"
            android:gravity="center_horizontal"
            android:text="@string/base_photos_present_type_des_date"
            android:textColor="?attr/couiColorSecondNeutral" />
    </LinearLayout>

</com.coui.appcompat.dialog.widget.COUIMaxHeightNestedScrollView>