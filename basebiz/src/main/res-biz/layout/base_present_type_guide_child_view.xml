<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    tools:ignore="ResourceName">

    <FrameLayout
        android:id="@+id/fl_present_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layoutDirection="ltr"
        tools:ignore="ResourceName">

        <ImageView
            android:id="@+id/iv_present"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:contentDescription="@null"
            android:maxWidth="@dimen/base_present_type_guide_img_placeholder_width"
            android:maxHeight="@dimen/base_present_type_guide_img_placeholder_height"
            android:scaleType="fitXY"
            android:src="@drawable/base_placeholder_photo_present_date_current" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.coui.appcompat.textview.COUITextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/base_present_type_guide_info_margin_horizontal"
                android:layout_marginTop="@dimen/base_present_type_guide_info_title_margin_top"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:layoutDirection="ltr"
                android:maxWidth="@dimen/base_present_type_guide_info_title_max_width"
                android:maxLines="2"
                android:minHeight="@dimen/base_present_type_guide_info_title_min_height"
                android:paddingHorizontal="@dimen/base_present_type_guide_info_title_padding_horizonal"
                android:singleLine="true"
                android:text="@string/main_fragment_title_timeline"
                android:textColor="@color/coui_color_primary_blue"
                android:textFontWeight="600"
                android:textSize="@dimen/base_present_type_guide_info_title_text_size_date"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage" />

            <com.coui.appcompat.textview.COUITextView
                android:id="@+id/tv_sub_title_toady"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/base_present_type_guide_info_margin_horizontal"
                android:layout_marginTop="@dimen/base_present_type_guide_info_sub_title_today_margin_top"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:layoutDirection="ltr"
                android:maxWidth="@dimen/base_present_type_guide_info_sub_title_max_width"
                android:maxLines="1"
                android:minHeight="@dimen/base_present_type_guide_info_sub_title_min_height"
                android:paddingHorizontal="@dimen/base_present_type_guide_info_sub_title_padding_horizontal"
                android:singleLine="true"
                android:text="@string/common_today"
                android:textColor="@color/coui_color_primary_blue"
                android:textFontWeight="500"
                android:textSize="@dimen/base_present_type_guide_info_sub_title_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage" />

            <com.coui.appcompat.textview.COUITextView
                android:id="@+id/tv_sub_title_yesterday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/base_present_type_guide_info_margin_horizontal"
                android:layout_marginTop="@dimen/base_present_type_guide_info_sub_title_yesterday_margin_top"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:layoutDirection="ltr"
                android:maxWidth="@dimen/base_present_type_guide_info_sub_title_max_width"
                android:maxLines="1"
                android:minHeight="@dimen/base_present_type_guide_info_sub_title_min_height"
                android:paddingHorizontal="@dimen/base_present_type_guide_info_sub_title_padding_horizontal"
                android:singleLine="true"
                android:text="@string/common_yesterday"
                android:textColor="@color/coui_color_primary_blue"
                android:textFontWeight="500"
                android:textSize="@dimen/base_present_type_guide_info_sub_title_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage" />

            <com.coui.appcompat.textview.COUITextView
                android:id="@+id/tv_sub_title_other_day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/base_present_type_guide_info_margin_horizontal"
                android:layout_marginTop="@dimen/base_present_type_guide_info_sub_title_other_day_margin_top"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:layoutDirection="ltr"
                android:maxWidth="@dimen/base_present_type_guide_info_sub_title_max_width"
                android:maxLines="1"
                android:minHeight="@dimen/base_present_type_guide_info_sub_title_min_height"
                android:paddingHorizontal="@dimen/base_present_type_guide_info_sub_title_padding_horizontal"
                android:singleLine="true"
                android:text="@string/base_common_location_nanshan_shenzhen"
                android:textColor="@color/coui_color_primary_blue"
                android:textFontWeight="500"
                android:textSize="@dimen/base_present_type_guide_info_sub_title_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <TextView
        android:id="@+id/tv_present"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_present_type_guide_child_text_margin_top"
        android:gravity="center"
        android:textAppearance="@style/PresentTypeGuideDialogChildTextAppearance"
        android:textColor="@color/coui_color_label_primary" />

    <RadioButton
        android:id="@+id/rb_present"
        android:layout_width="@dimen/base_present_type_guide_child_rb_wh"
        android:layout_height="@dimen/base_present_type_guide_child_rb_wh"
        android:layout_margin="@dimen/base_present_type_guide_child_rb_margin"
        android:clickable="false" />
</LinearLayout>