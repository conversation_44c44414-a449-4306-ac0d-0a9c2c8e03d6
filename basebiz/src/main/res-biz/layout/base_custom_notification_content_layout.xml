<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/notification_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:ellipsize="end"
        android:textDirection="anyRtl"
        android:textAlignment="viewStart"
        style="@style/TextAppearance.Compat.Notification.Title" />

    <TextView
        android:id="@+id/notification_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:alpha="0.55"
        android:textDirection="anyRtl"
        android:textAlignment="viewStart"
        android:layout_marginTop="@dimen/base_editor_menu_adjust_item_top_tips_margin_top"
        style="@style/TextAppearance.Compat.Notification" />
</LinearLayout>