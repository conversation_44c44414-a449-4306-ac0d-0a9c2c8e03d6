<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:id="@+id/action_share"
        android:enabled="false"
        android:icon="@drawable/base_ic_send_selector"
        android:title="@string/base_share"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_photo_jigsaw"
        android:enabled="false"
        android:icon="@drawable/base_ic_menu_jigsaw"
        android:title="@string/base_jigsaw_item"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_remove_from_label"
        android:enabled="false"
        android:icon="@drawable/base_ic_remove_selector"
        android:title="@string/base_remove_from_label"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_recycle"
        android:enabled="false"
        android:icon="@drawable/base_ic_delete_selector"
        android:title="@string/base_delete"
        android:visible="true"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/action_more_info"
        android:enabled="false"
        android:icon="@drawable/base_ic_action_more_selector"
        android:title="@string/base_more"
        app:showAsAction="ifRoom" />
</menu>
