/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseFragment.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/29
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_FRAGMENTSTACK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/08/29		1.0		init
 *********************************************************************************/

package com.oplus.gallery.basebiz.uikit.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.os.Bundle
import android.view.DragEvent
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.animation.Animation
import androidx.core.view.forEach
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.statusbar.COUIStatusBarResponseUtil
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.app.IGalleryController
import com.oplus.gallery.basebiz.helper.NO_ANIM_ARRAY
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.basebiz.permission.helper.CTAHelper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentStartType
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentState
import com.oplus.gallery.basebiz.uikit.fragment.base.IFragmentStack
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionAnimation
import com.oplus.gallery.basebiz.uikit.fragment.transition.OnFragmentTransitionListener
import com.oplus.gallery.basebiz.uikit.fragment.transition.SlideTransitionAnimation
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.foundation.tracing.TrackPage
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.ui.BaseUiFragment
import com.oplus.gallery.foundation.uikit.app.PreInflateViewHelper
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder.AppUiConfig.Companion.emptyAppUiConfig
import com.oplus.gallery.foundation.uikit.responsiveui.IAppUiObserver
import com.oplus.gallery.foundation.uikit.responsiveui.IAppUiResponder
import com.oplus.gallery.foundation.uikit.systembar.StatusBarClickManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.multiprocess.IResultCallback
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.abilities.search.ISearchAbility
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.RouterManager
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.Keys.DEFAULT_TAG
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import com.oplus.gallery.standard_lib.scheduler.DefaultScheduler
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

abstract class BaseFragment : BaseUiFragment(),
    TrackPage,
    IAppUiObserver,
    COUIStatusBarResponseUtil.StatusBarClickListener,
    OnFragmentTransitionListener {

    protected var isFirstDraw = true
    protected var lifeEvent = Lifecycle.Event.ON_ANY
    protected val clazzSimpleName: String by lazy { this.javaClass.simpleName }

    val window: Window?
        get() = activity?.window

    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_BASE_FRAGMENT
    open var trackAlbumPageInfo: String? = LaunchExitPopupConstant.Value.CUR_PAGE_SYSTEM_ALBUM

    var resultCallback: IResultCallback? = null

    protected open val isPageClickPenetrableEnabled: Boolean = true

    var fragmentState: FragmentState? = null

    /**
     * 是否打开转场动画
     *
     * 在一些特殊场景需要屏蔽转场动画，比如侧边栏在进行跳转的时候会进行多个fragment同时pop，需要禁用动画，
     * 所以设置animationEnable为false，返回一个无操作动画，如果不返回null，会走进原生通过xml加载动画的流程
     */
    open var transitionEnable: (() -> Boolean) = { true }

    /**
     * 是否在转场动画过程支持背景圆角
     */
    var transitionRoundBackgroundEnable = true

    /**
     * 当 [BaseFragment.onDestroy] 执行时，是否需要执行 viewModelStore.clear，用于清理 ViewModel
     *
     * 默认值为 true，即默认会清理
     *
     * Marked，非 常规用法，因为相册 MVVM 使用有些飞，不得不以此兼容；相册大图界面使用常规 MVVM 实现，不需要清理，故添加此标志位用于拦截
     */
    protected open val needClearViewModelStoreOnDestroy: Boolean = true

    private val controller: IGalleryController? by lazy {
        activity as? IGalleryController
    }

    protected var contentView: View? = null
        private set
    private var isReuseView = false

    protected val session by lazy { DefaultScheduler.scheduler.createPrioritySession(name = this.javaClass.simpleName) }

    private val clickListener = View.OnClickListener {
        // 拦截界面穿透点击事件
        GLog.d(TAG, "clickListener, Intercept click events")
    }
    protected var isTransitionAnimationRunning = false
        private set

    override fun onStatusBarClicked() {
        // do nothing
    }

    private fun getAppUiResponder(): IAppUiResponder? {
        return activity as? IAppUiResponder
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifeEvent = Lifecycle.Event.ON_CREATE
        RealShowTimeInstrument.startRecord(clazzSimpleName)
        getAppUiResponder()?.registerAppUiObserver(this, this)
        GLog.d(TAG, LogFlag.DL) { "onCreate: fragment:<$clazzSimpleName> ,tag : $tag" }
    }

    final override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        GLog.d(TAG, LogFlag.DL) { "onCreateView: fragment:<${<EMAIL>}> ,tag : $tag, contentView: $contentView" }
        fragmentState ?: FragmentState(parentFragmentManager, (requireActivity() as IFragmentStack).getStateMap()).also {
            fragmentState = it
        }
        return GTrace.trace("$clazzSimpleName.onCreateView") {
            contentView?.apply {
                // 如果进行转场动画返回会出现contentView已存在parent而无法重新add的问题, 需在此强行中断动画
                (this.parent as? ViewGroup)?.endViewTransition(this)
                isReuseView = true
                GLog.d(TAG, LogFlag.DL) { "onCreateView: fragment is reuse last one view , fragment is: ${<EMAIL>}" }
            } ?: context?.let {
                PreInflateViewHelper.getCacheView(getLayoutId())?.apply {
                    GLog.d(TAG, LogFlag.DL) { "onCreateView: use cache view" }
                    isReuseView = false
                    <EMAIL> = this
                }
            } ?: inflater.inflate(getLayoutId(), container, false).apply {
                isReuseView = false
                <EMAIL> = this
            }
        }
    }

    final override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        GTrace.trace("$clazzSimpleName.onViewCreated") {
            super.onViewCreated(view, savedInstanceState)
            if (!isReuseView) {
                (contentView as? ViewGroup)?.isMotionEventSplittingEnabled = false
                GTrace.trace("$clazzSimpleName.doViewCreated") {
                    if (isPageClickPenetrableEnabled) {
                        contentView?.setOnClickListener(clickListener)
                    } else {
                        contentView?.isClickable = false
                    }
                    doViewCreated(view, savedInstanceState)
                }
            }
        }
    }

    /**
     * 这个函数在Fragment出栈时可能不会被调用，注册逻辑放在这里要慎重
     * @param view
     * @param savedInstanceState
     */
    open fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        checkIfNeedShowPrivacyDialog()
        /** 不支持的fragment进行drop消费拦截，使得不会透传到底下的fragment上 */
        if (needIterceptDrop()) {
            contentView?.setOnDragListener { _, event ->
                when (event.action) {
                    DragEvent.ACTION_DRAG_STARTED -> true
                    DragEvent.ACTION_DROP -> {
                        GLog.w(TAG, "when drop not support, intercept drop event, fragment=$clazzSimpleName")
                        false
                    }

                    else -> false
                }
            }
        }
    }

    @SuppressLint("RestrictedApi")
    override fun onResume() {
        super.onResume()
        lifeEvent = Lifecycle.Event.ON_RESUME
        session.bringToFront()
        GLog.d(TAG, LogFlag.DL) { "onResume: <clazzSimpleName>：$clazzSimpleName" }
        if (supportClickStatusBar()) {
            StatusBarClickManager.registerListener(this)
        }
        if (hasOptionsMenu()) {
            activity?.invalidateOptionsMenu()
        }
    }

    override fun onPause() {
        super.onPause()
        lifeEvent = Lifecycle.Event.ON_PAUSE
        session.bringToBack()
        GLog.d(TAG, LogFlag.DL) { "onPause: <clazzSimpleName>：$clazzSimpleName" }

        if (supportClickStatusBar()) {
            StatusBarClickManager.unRegisterListener(this)
        }
    }

    override fun onStart() {
        super.onStart()
        lifeEvent = Lifecycle.Event.ON_START
        GLog.d(TAG, LogFlag.DL) { "onStart: <clazzSimpleName>：$clazzSimpleName" }
    }

    override fun onStop() {
        super.onStop()
        lifeEvent = Lifecycle.Event.ON_STOP
        GLog.d(TAG, LogFlag.DL) { "onStop: <clazzSimpleName>：$clazzSimpleName" }
    }

    protected open fun getUserActionCurPage(trackType: String? = null): String? = null

    override fun onDestroyView() {
        super.onDestroyView()
        GLog.d(TAG, LogFlag.DL) { "onDestroyView: <clazzSimpleName>：$clazzSimpleName" }
    }

    override fun onDestroy() {
        super.onDestroy()
        lifeEvent = Lifecycle.Event.ON_DESTROY
        session.terminal()
        fragmentState?.removeParent()
        contentView?.setOnDragListener(null)
        contentView = null
        getAppUiResponder()?.unregisterAppUiObserver(this)
        if (needClearViewModelStoreOnDestroy) {
            viewModelStore.clear()
        }
        GLog.d(TAG, LogFlag.DL) { "onDestroy: <clazzSimpleName>：$clazzSimpleName" }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        GLog.d(TAG, LogFlag.DL) { "onCreateOptionsMenu: <clazzSimpleName>：$clazzSimpleName" }
    }

    fun sendResult(resultCode: Int, intent: Intent?) {
        targetFragment?.onActivityResult(targetRequestCode, resultCode, intent) ?: run {
            activity?.setResult(resultCode, intent)
        }
    }

    override val defaultViewModelProviderFactory
        get() = ObserverFactory(lifecycle, super.defaultViewModelProviderFactory)

    override fun onCreateAnimation(transit: Int, enter: Boolean, nextAnim: Int): Animation? {
        // 关闭转场动画时，给一个空动画对象，避免走默认动画
        if (transitionEnable.invoke().not()) {
            return object : Animation() {}.apply { duration = 0L }
        }

        val transitionAnimation = FragmentTransitionAnimation.generateAnimation(this, nextAnim)
        transitionAnimation.addTransitionListener(this)
        transitionAnimation.frameCornerRadius = if (transitionRoundBackgroundEnable && !getCurrentAppUiConfig().isInMultiWindow.current) {
            // 仅在非分屏模式下，且在转场动画过程中需要圆角
            ScreenUtils.baseWindowCornerRadius
        } else {
            0f
        }
        return transitionAnimation.prepareAnimation(enter, nextAnim)
    }

    override fun onFragmentTransitionStart(animation: Animation) {
        // do nothing
    }

    override fun onFragmentTransitionEnd(animation: Animation) {
        // do nothing
    }

    /**
     * 声明弹窗操作
     */
    open fun onUserOPStatementDialog(op: CTAHelper.CTAUserOp) {}

    fun isFloatingWindowMode(): Boolean = controller?.isFloatingWindowMode() ?: false

    /**
     * 支持drop的页面设置为true
     * 支持的情况：原则上图集列表和图片列表中的非弹框，非外部跳转的基本都要支持
     * 1. 现在首页的3个tab都支持
     * 2. 图集详情（图片列表），图集 大部分都支持。
     * 3. 弹框类不支持（像移动到的图集选择页面不支持）
     * 4. 大图，设置页面等不支持
     */
    protected open fun isSupportDrop(): Boolean = false

    /**
     * 设置是否需要拦截drop事件
     *
     * 现在drop事件注册在了activity的android.R.id.content这个view上，所以drop事件整个activity会逐层消费事件，这样就会导致，fragment上层还有其他fragment的情况时
     * 上层fragment没有注册，被下层fragment消费了drop事件，导致fragment即使没有在栈顶也收到了drop。。这种情况需要处理，非栈顶的不能收到drop，需要在上层进行一下拦截
     *
     * 1. 跳转页面为activity不需要处理（像SettingActivity）
     * 2. 大图需要处理（刚好满足了大图和首页在一个activiy，同时大图frament不需要消费drop，而底下的首页注册了，从而导致drop到大图也能有drop触发）
     */
    protected open fun needIterceptDrop(): Boolean = false

    /**
     * getLayoutId 获得布局的资源id
     *
     * @return 布局的资源id
     */
    abstract fun getLayoutId(): Int

    open fun onBackPressed(): Boolean = true

    open fun onKeyDown(keyCode: Int): Boolean = false

    open fun onKeyUp(keyCode: Int): Boolean = false

    open fun onKeyLongPress(keyCode: Int): Boolean = false

    open fun onKeyMultiple(keyCode: Int, repeatCount: Int): Boolean = false

    @FragmentStartType
    open fun startType(): Int = FragmentStartType.REPLACE

    /**
     * 方法为启动Fragment的操作，在Fragment中启动的Fragment皆将添加到Fragment栈中，必须指定泛型
     * @param startType 启动方式 [FragmentStartType.ADD] or [FragmentStartType.REPLACE]
     * @param resId Fragment容器
     * @param postCard 路由Fragment的PostCard信息
     * @param tag tag
     * @param data 传输的数据
     * @param anim Fragment的入出动画
     *
     * @return 返回启动的Fragment，当Fragment在FragmentManager中存在，则使用缓存中的Fragment；
     *          当Fragment在回退栈中，则退栈道此Fragment位置；
     */
    @Suppress("UNCHECKED_CAST", "LongParameterList")
    fun <T : BaseFragment> startByStack(
        @FragmentStartType startType: Int? = null,
        resId: Int = R.id.base_fragment_container,
        postCard: PostCard,
        tag: String = DEFAULT_TAG,
        requestCode: Int? = null,
        data: Bundle? = null,
        anim: IntArray = intArrayOf(
            Resources.ID_NULL,
            Resources.ID_NULL,
            Resources.ID_NULL,
            Resources.ID_NULL
        )
    ): T? {
        return (RouterManager.routerCenter.getRouter(postCard)?.getRouterClass() as? Class<out BaseFragment>)?.run {
            startByStack(startType, resId, this, tag, requestCode, data, anim)
        }
    }

    /**
     * 方法为启动Fragment的操作，在Fragment中启动的Fragment皆将添加到Fragment栈中，必须指定泛型
     * @param startType 启动方式 [FragmentStartType.ADD] or [FragmentStartType.REPLACE]
     * @param resId Fragment容器
     * @param fragmentClass 路由Fragment的Class
     * @param tag tag
     * @param data 传输的数据
     * @param anim Fragment的入出动画
     *
     * @return 返回启动的Fragment，当Fragment在FragmentManager中存在，则使用缓存中的Fragment；
     *          当Fragment在回退栈中，则退栈道此Fragment位置；
     */
    @Suppress("UNCHECKED_CAST", "LongParameterList")
    fun <T : BaseFragment> startByStack(
        @FragmentStartType startType: Int? = null,
        resId: Int = R.id.base_fragment_container,
        fragmentClass: Class<out BaseFragment>,
        tag: String = DEFAULT_TAG,
        requestCode: Int? = null,
        data: Bundle? = null,
        anim: IntArray = NO_ANIM_ARRAY
    ): T? {
        return (activity as? BaseActivity)?.run {
            supportFragmentManager.start(
                startType,
                resId,
                fragmentClass,
                tag,
                requestCode,
                data,
                this,
                true,
                anim
            ) as? T
        }
    }

    protected fun updateCache() {
        session.submit(UpdateCacheJob())
    }

    class ObserverFactory(
        val lifecycle: Lifecycle,
        private val factory: ViewModelProvider.Factory
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return factory.create(modelClass).also {
                if (it is LifecycleObserver) {
                    lifecycle.addObserver(it)
                }
            }
        }
    }

    private class UpdateCacheJob : Job<Any?> {
        override fun call(jc: JobContext): Any? {
            (ContextGetter.context as GalleryApplication).getAppAbility(ISearchAbility::class.java)?.use {
                it.notifyDataDirty(SearchSuggestionProviderUtil.BACKGROUND_COLLECT_URI)
            }
            return null
        }
    }

    fun getCurrentAppUiConfig(): AppUiResponder.AppUiConfig = getAppUiResponder()?.getCurrentAppUiConfig() ?: emptyAppUiConfig()

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        /*
        当从一个Fragment跳到另一个Fragment后，当Ui状态改变只能调用当前Fragment的onConfigurationChanged，导致从当前Fragment返回上一个Fragment后，
        上一个页面不会执行onConfigurationChanged方法，从而导致View同样不执行onConfigurationChanged，使显示异常，但相册适用了AppUiResponder，
        Ui状态改变后，返回上一个Fragment后会执行onAppUiStateChanged，这里即重新让View执行dispatchConfigurationChanged，使View能够执行状态刷新
         */
        if (uiConfig.isChanged()) {
            view?.apply {
                dispatchConfigurationChanged(resources.configuration)
                notifyAllIAppUiObserverView(this, uiConfig)
            }
        }
    }

    private fun notifyAllIAppUiObserverView(view: View, uiConfig: AppUiResponder.AppUiConfig) {
        (view as? ViewGroup)?.forEach {
            notifyAllIAppUiObserverView(it, uiConfig)
        }
        (view as? IAppUiObserver)?.onAppUiStateChanged(uiConfig)
    }

    /**
     * 是否需要隐私政策授权弹框，只适用于单个授权
     */
    open fun checkIfNeedShowPrivacyDialog() {
        if (getPrivacyAuthorizeType() == TextUtil.EMPTY_STRING) {
            return
        }
        if (!isPrivacyAuthorized(getPrivacyAuthorizeType())) {
            context?.apply {
                GLog.d(TAG, "checkIfNeedShowPrivacyDialog, showPrivacyDialog")
                showPrivacyDialog(this)
            }
        }
    }

    /**
     * 显示隐私政策授权框，具体的显示逻辑由子类重写
     */
    open fun showPrivacyDialog(context: Context) {}

    /**
     * 隐私政策的功能类型，由子类重写
     */
    open fun getPrivacyAuthorizeType(): String = TextUtil.EMPTY_STRING

    /**
     * 支持点击顶部状态栏
     * 默认为false，不会监听状态栏的点击事件
     * 如果是true，那么就会注册监听状态栏的点击事件，回调到onStatusBarClicked方法中，需要的页面重写该方法即可
     */
    open fun supportClickStatusBar(): Boolean = false

    /**
     * 是否同意传入的隐私权限
     * 没配置能力对象，即ability为空，返回false
     * 配置能力对象，但是接口返回空，返回false
     * 最后根据授权标志位返回记录的boolean值
     */
    private fun isPrivacyAuthorized(privacyType: String = getPrivacyAuthorizeType()): Boolean {
        activity?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            return it.isPrivacyAuthorized(privacyType) ?: run {
                GLog.d(TAG, "isPrivacyAuthorized, isPrivacyAuthorized get null")
                false
            }
        } ?: return true
    }

    /**
     * 判断权限是否授予，默认值为页面配置的值
     */
    suspend fun isPrivacyAuthorizedSuspend(privacyType: String = getPrivacyAuthorizeType()): Boolean {
        return withContext(Dispatchers.SINGLE_UN_BUSY) {
            isPrivacyAuthorized(privacyType)
        }
    }

    companion object {
        private const val TAG = "BaseFragment"

        val DEFAULT_ANIM_ARRAY = SlideTransitionAnimation.SLIDE_ANIM_ARRAY
    }
}