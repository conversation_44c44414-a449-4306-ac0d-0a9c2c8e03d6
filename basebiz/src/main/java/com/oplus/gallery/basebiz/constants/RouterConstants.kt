/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Constants.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/09/05
 ** Author: Wangrunxin@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                       <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Wangrunxin@Apps.Gallery3D      2020/09/05      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.basebiz.constants

import com.oplus.gallery.business_lib.BusinessLibHelper
import com.oplus.gallery.router_lib.utils.Constants.URI_ROUTER

object RouterConstants {

    object RouterName {
        // app
        const val MAIN_ACTIVITY = URI_ROUTER + "app/main_activity"
        const val DEV_ACTIVITY = URI_ROUTER + "app/dev_activity"

        // base
        const val SELECTION_TAB_FRAGMENT = URI_ROUTER + "base/selection_tab_fragment"
        const val SELECTION_PREVIEW_BTN_FRAGMENT = URI_ROUTER + "base/selection_preview_btn_fragment"
        const val SELECTION_FRAGMENT = URI_ROUTER + "base/selection_fragment"
        const val SELECTION_MAIN_ALBUM_SET_TAB_FRAGMENT = URI_ROUTER + "base/selection_main_album_set_tab_fragment"
        const val SELECTION_ALBUM_SET_TAB_FRAGMENT = URI_ROUTER + "base/selection_album_set_tab_fragment"
        const val SELECTION_ALBUM_SET_FRAGMENT = URI_ROUTER + "base/selection_album_set_fragment"
        const val SELECTION_PERSON_PET_ALBUM_SET_FRAGMENT = URI_ROUTER + "base/selection_person_pet_album_set_fragment"
        const val SELECTION_OTHER_ALBUM_SET_FRAGMENT = URI_ROUTER + "base/selection_other_album_set_fragment"
        const val SELECTION_OTHER_ALBUM_SET_TAB_FRAGMENT = URI_ROUTER + "base/selection_other_album_set_tab_fragment"
        const val SELECTION_ALBUM_FRAGMENT = URI_ROUTER + "base/selection_album_fragment"
        const val SELECTION_ALBUM_ONLY_FRAGMENT = URI_ROUTER + "base/selection_album_only_fragment"
        const val SELECTION_SECONDARY_ALBUM_FRAGMENT = URI_ROUTER + "base/selection_secondary_album_fragment"
        const val SELECTION_TIMELINE_FRAGMENT = URI_ROUTER + "base/selection_timeline_fragment"

        // main
        const val SIDE_PANE_FRAGMENT = URI_ROUTER + "main/side_pane_fragment"
        const val SIDE_PANE_ALBUM_CONTAINER_FRAGMENT = URI_ROUTER + "main/side_pane_album_container_fragment"
        const val TAB_FRAGMENT = URI_ROUTER + "main/tab_fragment"
        const val TIMELINE_TAB_FRAGMENT = URI_ROUTER + "main/timeline_tab_fragment"
        // 现图集主页tab
        const val MAIN_TAB_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/main_tab_album_set_fragment"
        // 原图集主页tab
        const val ALBUM_SET_TAB_FRAGMENT = URI_ROUTER + "main/album_tab_fragment"
        const val EXPLORER_TAB_FRAGMENT = URI_ROUTER + "main/explorer_tab_fragment"
        const val PERSON_FRAGMENT = URI_ROUTER + "main/person_fragment"
        const val PERSON_PET_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/person_pet_album_set_fragment"
        const val PERSON_PET_GROUP_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/person_pet_group_album_set_fragment"
        const val PERSON_PET_DETAIL_FRAGMENT = URI_ROUTER + "main/person_pet_detail_fragment"
        const val PERSON_PET_RELATIONSHIP_WITH_ME_FRAGMENT = URI_ROUTER + "main/person_pet_relationship_with_me_fragment"
        const val OTHER_PERSON_FRAGMENT = URI_ROUTER + "main/other_person_fragment"
        const val PERSON_DETAIL_FRAGMENT = URI_ROUTER + "main/person_detail_fragment"
        const val LOCATION_FRAGMENT = URI_ROUTER + "main/location_fragment"
        const val LABEL_FRAGMENT = URI_ROUTER + "main/label_fragment"
        const val TRAVEL_ALL_FRAGMENT = URI_ROUTER + "main/travel_all_fragment"
        const val TRAVEL_YEAR_FRAGMENT = URI_ROUTER + "main/travel_year_fragment"
        const val ALBUM_FRAGMENT = URI_ROUTER + "main/album_fragment"
        const val MEMORIES_DETAIL_FRAGMENT = URI_ROUTER + "main/memories_detail_fragment"
        const val TRAVEL_DETAIL_FRAGMENT = URI_ROUTER + "main/travel_detail_fragment"
        const val MEMORIES_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/memories_album_set_fragment"
        const val TIME_NODE_FRAGMENT = URI_ROUTER + "main/time_node_fragment"
        const val RECYCLE_FRAGMENT = URI_ROUTER + "main/recycle_fragment"
        const val SHARE_DIALOG_FRAGMENT = URI_ROUTER + "main/share_dialogfragment"
        const val OTHER_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/other_album_set_fragment"
        const val MY_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/my_album_set_fragment"
        //我的图集页面
        const val MY_ALBUM_SET_TAB_FRAGMENT = URI_ROUTER + "main/my_album_set_tab_fragment"
        //旅程页面
        const val TRAVEL_ALBUM_SET_TAB_FRAGMENT = URI_ROUTER + "main/travel_album_set_tab_fragment"
        const val SHARE_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/share_album_set_fragment"
        const val SHARED_DYNAMIC_PANEL_DIALOG = URI_ROUTER + "main/share_dynamic_panel_dialog"
        const val VIEW_GALLERY_ACTIVITY = URI_ROUTER + "main/view_gallery_activity"
        const val INNER_GALLERY_ACTIVITY = URI_ROUTER + "main/inner_gallery_activity"
        const val SELECTION_PANEL_DIALOG = URI_ROUTER + "main/selection_panel_dialog"
        const val SELECTION_PREVIEW_PANEL_DIALOG = URI_ROUTER + "main/selection_preview_panel_dialog"
        const val SELECTION_PREVIEW_PANEL_FRAGMENT = URI_ROUTER + "main/selection_preview_panel_fragment"
        const val SELECTION_PREVIEW_TOP_FRAGMENT = URI_ROUTER + "main/selection_preview_top_fragment"
        const val SELECTION_PREVIEW_BOTTOM_FRAGMENT = URI_ROUTER + "main/selection_preview_bottom_fragment"
        const val SELECTION_ALBUM_PANEL_DIALOG = URI_ROUTER + "main/selection_album_panel_dialog"
        const val CARD_CASE_FRAGMENT = URI_ROUTER + "main/card_case_fragment"
        const val SORT_GUIDE_PANEL_DIALOG = URI_ROUTER + "main/sort_guide_panel_dialog"
        const val LNS_GUIDE_PANEL_DIALOG = URI_ROUTER + "main/lns_guide_panel_dialog"
        const val VIDEO_GUIDE_PANEL_DIALOG = URI_ROUTER + "main/video_guide_panel_dialog"
        const val GROUP_PHOTO_GUIDE_PANEL_DIALOG = URI_ROUTER + "main/group_photo_guide_panel_dialog"
        const val SPRING_FESTIVAL_WATERMARK_GUIDE_PANEL_DIALOG = URI_ROUTER + "main/spring_festival_watermark_guide_panel_dialog"
        const val ALBUM_GROUP_SORT_DIALOG = URI_ROUTER + "main/album_group_sort_dialog"
        const val PERSON_PET_RELATIONSHIP_WITH_ME_PANEL_DIALOG = URI_ROUTER + "main/person_pet_relationship_with_me_panel_dialog"
        //picture
        const val SET_AS_WALLPAPER_ACTIVITY = URI_ROUTER + "picture/set_as_wallpaper_activity"

        // picture3d
        const val PICTURE_FRAGMENT = URI_ROUTER + "picture3d/picture_fragment"
        const val CROP_IMAGE_FRAGMENT = URI_ROUTER + "picture3d/crop_image_fragment"
        const val NOTIFY_JUMP_TO_RECYCLE_FRAGMENT = URI_ROUTER + "photopage/notify_jump_to_recycle_fragment"

        /**
         * 指定编辑项的编辑页
         * 老的编辑fragment
         */
        const val PHOTO_EDITOR_FRAGMENT = URI_ROUTER + "picture3d/photoeditor_fragment"
        const val PHOTO_EDITOR_ACTIVITY = URI_ROUTER + "picture3d/photoeditor_activity"
        const val GALLERY_ACTION_TRANSIT_ACTIVITY = URI_ROUTER + "picture3d/gallery_action_transit_activity"
        const val SLIDING_SHOW_FRAGMENT = URI_ROUTER + "picture3d/sliding_show_fragment"
        /**
         * 编辑页fragment
         * 新的编辑框架fragment
         */
        const val EDITING_FRAGMENT = URI_ROUTER + "photoedit/editing_fragment"

        const val CSHOT_ACTIVITY = URI_ROUTER + "photopage/cshot_activity"

        //Marked by zhangwenming 组件化临时迁移support/business_lib模块下的model/data部分时，会有依赖这里的逻辑。
        //但因其他依赖不能直接将UserProfileSettings一同迁移过去, 在组件化彻底将model/data部分的依赖解耦后，需要恢复该部分逻辑。
        // search
        //const val SEARCH_ACTIVITY = URI_ROUTER + "search/search_activity"
        const val SEARCH_ACTIVITY = BusinessLibHelper.RouterName.SEARCH_ACTIVITY

        // settings
        const val SETTING_ACTIVITY = URI_ROUTER + "setting/settings_activity"
        const val CLOUD_CHANNEL_SELECT_ACTIVITY = URI_ROUTER + "setting/cloud_channel_select_activity"
        const val GOOGLE_SETTING_ACTIVITY = URI_ROUTER + "setting/google_settings_activity"
        const val SETTINGS_ABOUT_ACTIVITY = URI_ROUTER + "setting/settings_about_activity"
        const val COMMON_WEB_ACTIVITY = URI_ROUTER + "base/common_web_activity"
        const val OPEN_SECURITY_ACTIVITY = URI_ROUTER + "setting/open_security_activity"
        const val SETTINGS_HDR_IMAGE_ACTIVITY = URI_ROUTER + "setting/settings_hdr_image_activity"
        const val WATERMARK_INTRODUCTION_ACTIVITY = URI_ROUTER + "setting/watermark_introduction_activity"
        const val AUTOMATIC_CODING_INTRODUCTION_ACTIVITY = URI_ROUTER + "setting/automatic_coding_introduction_activity"

        /**
         * 兼容格式页面的路由地址
         */
        const val OPEN_COMPATIBLE_FORMAT_ACTIVITY = URI_ROUTER + "setting/compatible_format_activity"
        const val SETTING_PRIVACY_POLICY = URI_ROUTER + "setting/privacy_policy"
        const val SETTING_PRIVACY = URI_ROUTER + "setting/privacy"
        const val SETTING_COLLECTION_OF_PERSONAL_INFO = URI_ROUTER + "setting/collection_of_personal_info"
        const val SETTING_COLLECTION_OF_PERSONAL_COLLECT_INFO = URI_ROUTER + "setting/collection_of_personal_collect_info"
        const val SETTING_COLLECTION_OF_PERSONAL_COLLECT_INFO_DETAIL = URI_ROUTER + "setting/collection_of_personal_collect_info_detail"
        const val SETTING_COLLECTION_OF_INFORMATION_CONTENT_DETAIL = URI_ROUTER + "setting/collection_of_information_content_detail"
        const val SETTING_COLLECTION_OF_INFORMATION_SHARE_WITH_FUNCTION_DETAIL = URI_ROUTER +
                "setting/collection_of_information_share_with_function_detail"
        const val SETTING_THIRD_PARTY_INFO_SHARING = URI_ROUTER + "setting/third_party_info_sharing"
        const val SETTING_FUNCTION = URI_ROUTER + "setting/function"
        const val SETTING_USER_AGREEMENT = URI_ROUTER + "setting/user_agreement"

        // map
        const val MAPVIEW_FRAGMENT = URI_ROUTER + "map/mapview_fragment"
        const val MAP_ACTIVITY = URI_ROUTER + "map/map_activity"

        // 地图旅程合一的fragment
        const val MAP_TRAVEL_FRAGMENT = URI_ROUTER + "maptravel/maptravel_fragment"

        const val ADD_TO_FRAGMENT = URI_ROUTER + "base/add_to_album_fragment"

        // video editor
        const val VIDEO_EDITOR_ACTIVITY = URI_ROUTER + "videoeditor/video_editor_activity"

        //collage
        const val COLLAGE_ACTIVITY = URI_ROUTER + "collage/collage_activity"

        // 中转Activity:处理所有跳转选择页的Action
        const val SELECTION_ACTIVITY = URI_ROUTER + "main/selection_action_activity"

        // gallery share
        const val GALLERY_SHARE_ACTIVITY = URI_ROUTER + "main/gallery_share_activity"
        // 内部跳转分享Activity
        const val GALLERY_SHARE_INNER_ACTIVITY = URI_ROUTER + "main/gallery_share_inner_activity"
        // 外部碰一碰拉起相册
        const val RECEIVE_SHARE_ACTIVITY = URI_ROUTER + "main/receive_share_activity"
        // memories activity
        const val MEMORIES_ACTIVITY = URI_ROUTER + "videoeditor/memories_activity"
        const val MEMORIES_INNER_GALLERY_ACTIVITY = URI_ROUTER + "videoeditor/memories_inner_gallery_activity"

        // VideoEditorSendActivity
        const val VIDEO_EDITOR_SEND_ACTIVITY = URI_ROUTER + "videoeditor/video_editor_send_activity"

        // 用于大图的下一级页面 see PictureExtendActivity
        const val PICTURE_EXTEND_ACTIVITY = URI_ROUTER + "main/picture_extend_activity"

        // 桌面卡片模式选择页
        const val WIDGET_MODE_SELECTION_ACTIVITY = URI_ROUTER + "widget/mode_selection_activity"
        const val WIDGET_MODE_RECOMMENDED_ACTIVITY = URI_ROUTER + "widget/mode_recommended_activity"
        const val WIDGET_MODE_SELECTION_DIALOG = URI_ROUTER + "widget/mode_selection_dialog"
        const val WIDGET_MODE_RECOMMENDED_DIALOG = URI_ROUTER + "widget/mode_recommended_dialog"
        // 桌面卡片自定义模式的管理页面
        const val WIDGET_DISPLAY_LIST_ACTIVITY = URI_ROUTER + "widget/display_list_activity"
        const val WIDGET_DISPLAY_LIST_FRAGMENT = URI_ROUTER + "widget/display_list_fragment"

        // 自分屏
        const val SELF_SPLIT_ACTIVITY = URI_ROUTER + "selfsplit/selfsplit_activity"

        //共享图集
        const val SHARED_ALBUM_MAIN_ACTIVITY = URI_ROUTER + "shared_album/main_activity"
        const val SHARED_ALBUM_MAIN_FRAGMENT = URI_ROUTER + "shared_album/main_fragment"
        const val SHARED_ALBUM_TAB_FRAGMENT = URI_ROUTER + "shared_album/tab_fragment"
        const val SHARED_ALBUM_HOME_TAB_FRAGMENT = URI_ROUTER + "shared_album/tab_home"
        const val SHARED_ALBUM_DYNAMIC_TAB_FRAGMENT = URI_ROUTER + "shared_album/tab_dynamic"
        const val SHARED_ALBUM_CHOOSE_FRAGMENT = URI_ROUTER + "shared_album/choose_fragment"
        const val SHARED_ALBUM_TIMELINE_FRAGMENT = URI_ROUTER + "shared_album/timeline_fragment"
        const val SHARED_ALBUM_SETTING_FRAGMENT = URI_ROUTER + "shared_album/setting_activity"
        const val SHARED_ALBUM_MEMBER_TAB_FRAGMENT = URI_ROUTER + "shared_album/member_tab_fragment"
        const val SHARED_ALBUM_MEMBER_LIST_FRAGMENT = URI_ROUTER + "shared_album/member_list_fragment"
        const val SHARED_ALBUM_INVITE_LIST_FRAGMENT = URI_ROUTER + "shared_album/invite_list_fragment"
        const val SHARED_ALBUM_ACCOUNT_INVITE_FRAGMENT = URI_ROUTER + "shared_album/account_invite_fragment"
        // 确认是否同步删除云端私密照片
        const val CLOUD_SAFE_BOX_DELETE_ACTIVITY = URI_ROUTER + "cloud/safe_box_delete_activity"

        const val PRIVACY_STATEMENT_FRAGMENT = URI_ROUTER + "main/privacy_statement_fragment"

        /**
         * 图集页常用编辑态
         * */
        const val EDIT_SHORTCUT_ALBUM_SET_FRAGMENT = URI_ROUTER + "main/edit_shortcut_album_set_fragment"

        const val CLOUD_SYNC_OPEN_DEVICES_ACTIVITY = URI_ROUTER + "cloud/sync_open_devices"

        /**
         * 导出实况编辑页
         */
        const val EXPORT_OLIVE_FRAGMENT = URI_ROUTER + "videoeditor/export_olive_fragment"
    }

    object RouterKey {
        const val KEY_MODEL_TYPE = "key_model_type"
        const val KEY_ID = "key_id"
        const val KEY_TITLE = "key_title"
        const val KEY_URL = "key_url"
    }
}