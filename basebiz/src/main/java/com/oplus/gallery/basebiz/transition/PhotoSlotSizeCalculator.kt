/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: PhotoSlotSizeCalculator.kt
 ** Description: 大图图片缩放比修改器
 ** Version: 1.0
 ** Date : 2022/07/14
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Apps.gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>               <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Apps.gallery  2022/07/14   1.0          build this module
 ********************************************************************************/
package com.oplus.gallery.basebiz.transition

import android.view.animation.PathInterpolator
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.graphic.ImageUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.photopager.animationcontrol.AnimationPropertyProvider
import com.oplus.gallery.photopager.animationcontrol.AnimationScrollerParameter
import com.oplus.gallery.photopager.animationcontrol.DoubleTapParameter
import com.oplus.gallery.photopager.animationcontrol.FlingZoomParameter
import com.oplus.gallery.photopager.animationcontrol.ScrollerParameter
import com.oplus.gallery.photopager.animationcontrol.mix
import kotlin.math.abs
import kotlin.math.max

/**
 * 业务类：图片缩放比修改器
 */
class PhotoSlotSizeCalculator {
    /**
     * 双击放大的动画参数
     */
    private val doubleTapParameter by lazy {
        DoubleTapParameter(
            scaleParameter = ScrollerParameter(interpolator = doubleTapPathInterpolator, duration = DOUBLE_TAP_DURATION),
            translateParameter = ScrollerParameter(interpolator = doubleTapPathInterpolator, duration = DOUBLE_TAP_DURATION)
        )
    }

    /**
     * 放大后fling参数
     */
    private val flingZoomParameter by lazy {
        FlingZoomParameter(
            interpolator = flingZoomPathInterpolator,
            velocityRatio = FLING_VELOCITY_RATIO_WHEN_ZOOM
        )
    }

    // Stupid code style asks to start an new line here
    private val ruleSet =
        mutableSetOf<SizeRule>(
            SmallGifSizeRule() // Gif within 500px
        )

    /**
     * 复写指定[transitionPreviewData]的动画属性
     */
    fun overrideSlotAnimationPropertiesIfNeed(
        transitionPreviewData: TransitionPreviewData,
        provider: AnimationPropertyProvider
    ) {
        provider.maxZoomScale = getMaxZoomScale(provider)
        provider.doubleTapZoomInScale = getDoubleTapZoomInScale(provider)
        provider.minZoomScale = null
        provider.overZoomInScale = null
        provider.overZoomOutScale = null
        provider.overScrollDistance = null
        provider.overFlingDistanceX = null
        provider.overFlingDistanceY = null
        provider.minRotationThreshold = { THRESHOLD_ANGLE_FOR_LAUNCH_ROTATING }
        provider.animationScrollerParameter = {
            AnimationScrollerParameter(
                doubleTapEventParameter = doubleTapParameter,
                scaleRotateParameter = null,
                flingZoomParameter = flingZoomParameter
            )
        }
        ruleSet.forEach { rule ->
            if (rule.match(transitionPreviewData)) {
                rule.onOverrideAnimationProperties(provider)
            }
        }
    }

    /**
     * 获取最大的缩放倍率
     *
     * 对图片和视频大小划分等级获取realSize对应的maxScale：
     *
     * 1. realSize < 1000 * 1000 ===> maxScale = 1.0f
     * 2. 1000 * 1000 <= realSize < 3000 * 3000 ===> maxScale = 5.5f
     * 3. 3000 * 3000 <= realSize < 6000 * 6000===> maxScale = 15f
     * 4. 6000 * 6000 <= realSize < 8000 * 8000===> maxScale = 30f
     * 5. 8000 * 8000 <= realSize ===> maxScale = 50f
     * 6. 比较maxScale与（短边占满屏幕的最大缩放倍率 * 2）的最大值，返回最大的scale值
     *
     * @param provider 动画属性适配器
     * @return 缩放倍率
     */
    private fun getMaxZoomScale(provider: AnimationPropertyProvider): (() -> Float) {
        return { getSnapbackZoomScale(provider).let { it * getMaxZoomScale(it, provider) } }
    }

    /**
     * 获取双击放大的缩放倍率
     * @param provider 动画属性适配器
     * @return 缩放倍率
     */
    private fun getDoubleTapZoomInScale(provider: AnimationPropertyProvider): (() -> Float) {
        return { getSnapbackZoomScale(provider).let { it * getDoubleTapZoomScale(it, provider) } }
    }

    private fun getDoubleTapZoomScale(
        snapBackZoomScale: Float,
        provider: AnimationPropertyProvider
    ): Float {
        if (canConstraintPose(provider).not()) {
            return DEFAULT_MAX_ZOOM_RATING
        }
        val shortSideZoomScale = getShortSideMaxZoomScale(snapBackZoomScale, provider)
        return max(DEFAULT_MAX_ZOOM_RATING, shortSideZoomScale)
    }

    private fun getMaxZoomScale(
        snapBackZoomScale: Float,
        provider: AnimationPropertyProvider
    ): Float {
        if (canConstraintPose(provider).not()) {
            return DEFAULT_MAX_ZOOM_RATING
        }
        val shortSideZoomScale = getShortSideMaxZoomScale(snapBackZoomScale, provider)
        provider.apply {
            val realSize = contentSize.width * contentSize.height
            val maxScale: Float = when {
                (realSize < PIC_SIZE_1M) -> SMALL_ZOOM_RATING
                (realSize in PIC_SIZE_1M until PIC_SIZE_9M) -> MID_ZOOM_RATING
                (realSize in PIC_SIZE_9M until PIC_SIZE_36M) -> LARGE_ZOOM_RATING
                (realSize in PIC_SIZE_36M until PIC_SIZE_64M) -> VERY_LARGE_ZOOM_RATING
                else -> SUPER_LARGE_ZOOM_RATING
            }
            val maxZoomScale = max(maxScale, shortSideZoomScale * DOUBLE_EXTRA_LARGE_ZOOM_RATING)
            if (GProperty.DEBUG_PHOTO_GESTURE) {
                GLog.d(TAG) {
                    "[getMaxZoomScale] calc scale = $maxScale, short side scale = $shortSideZoomScale, maxZoomScale = $maxZoomScale"
                }
            }
            return maxZoomScale
        }
    }

    /**
     * 获取短边占满屏幕的最大缩放倍率，当前需求就是默认短边占满屏幕，使用场景如双击放大
     * 考虑到比例不能太小，如果缩放倍率低于2.5，返回2.5
     * @param provider 动画属性适配器
     * @param snapbackZoomScale 将图片原尺寸缩放为能在屏幕里完整显示的 scale 值
     * @return 缩放倍率
     */
    private fun getShortSideMaxZoomScale(snapbackZoomScale: Float, provider: AnimationPropertyProvider): Float {
        if (canConstraintPose(provider).not()) {
            return DEFAULT_MAX_ZOOM_RATING
        }
        provider.apply {
            /* currentWidth,currentHeight 当前在屏幕中显示的内容大小，比如在1080 * 2412的手机上，
            12288 * 16384图片的 currentWidth 为 1080 ，currentHeight 为 1440*/
            val currentWidth = contentSize.width * snapbackZoomScale
            val currentHeight = contentSize.height * snapbackZoomScale
            return if ((currentWidth <= 0f) || (currentHeight <= 0f)) {
                DEFAULT_MAX_ZOOM_RATING
            } else {
                val widthScale = displaySize.width / currentWidth
                val heightScale = displaySize.height / currentHeight
                // 选择图片比例相比屏幕比例，较短的边占满屏幕
                val greaterScale = max(widthScale, heightScale)
                // 比例如果小于默认值，选择默认值
                max(greaterScale, DEFAULT_MAX_ZOOM_RATING)
            }
        }
    }

    /**
     * 是否已经可以计算约束了，当[AnimationPropertyProvider.displaySize]和[AnimationPropertyProvider.contentSize]没有设置时，
     * 约束是无法被计算出来的
     * @param provider 动画属性适配器
     * @return 是否已经可以计算约束
     */
    private fun canConstraintPose(provider: AnimationPropertyProvider): Boolean {
        provider.apply {
            return (displaySize.width > 0)
                    && (displaySize.height > 0)
                    && (contentSize.width > 0)
                    && (contentSize.height > 0)
        }
    }

    /**
     * 缩放回全屏显示时的缩放比率，适配了旋转状态。
     * 传入[ImageUtils.computeInsideScale]的值是当前的显示内容的宽高，可能是旋转过后的，这样计算的好处是不需要考虑旋转角度。
     * 如果没有旋转缩放过，直接传原始宽高。
     * @param provider 动画属性适配器
     * @return 缩放回全屏显示时的缩放比率
     */
    private fun getSnapbackZoomScale(provider: AnimationPropertyProvider): Float {
        val scale = provider.animationControl.finalPose.let { it.scaleX mix it.scaleY }
        (scale.isFinite() && (scale != 0.0f)).let { isScaleValid ->
            provider.apply {
                val srcWidth = if (isScaleValid) (abs(finalAabb.right - finalAabb.left) / scale).toInt() else contentSize.width
                val srcHeight = if (isScaleValid) (abs(finalAabb.bottom - finalAabb.top) / scale).toInt() else contentSize.height
                return ImageUtils.computeInsideScale(
                    srcWidth.toFloat(),
                    srcHeight.toFloat(),
                    displaySize.width.toFloat(),
                    displaySize.height.toFloat()
                )
            }
        }
    }

    companion object {
        private const val TAG = "PhotoSlotSizeCalculator"
        private const val DEFAULT_MAX_ZOOM_RATING = 2.5f
        private const val SMALL_ZOOM_RATING = 1.0f
        private const val MID_ZOOM_RATING = 5.5f
        private const val LARGE_ZOOM_RATING = 15f
        private const val VERY_LARGE_ZOOM_RATING = 30f
        private const val SUPER_LARGE_ZOOM_RATING = 50f
        private const val DOUBLE_EXTRA_LARGE_ZOOM_RATING = 2f
        private const val PIC_SIZE_1M = 1000000
        private const val PIC_SIZE_9M = 9000000
        private const val PIC_SIZE_36M = 36000000
        private const val PIC_SIZE_64M = 64000000

        /**
         * 图片旋转判定所需的最小阈值角度
         */
        private const val THRESHOLD_ANGLE_FOR_LAUNCH_ROTATING: Float = 35.0f

        /**
         * 放大状态下的滑动速度缩小倍数
         */
        private const val FLING_VELOCITY_RATIO_WHEN_ZOOM: Float = 4.2f

        /**
         * 放大后滑动的曲线
         */
        private val flingZoomPathInterpolator = PathInterpolator(0.17f, 0.17f, 0.1f, 1.0f)

        /**
         * 双击放大的时长
         */
        private const val DOUBLE_TAP_DURATION = 300L

        /**
         * 双击放大的曲线
         */
        private val doubleTapPathInterpolator = PathInterpolator(0.33f, 0.0f, 0.67f, 1.0f)
    }
}

/**
 * 尺寸规则
 */
private interface SizeRule {
    /**
     * 当前的[transitionPreviewData]是否能够满足修改的条件
     * @return 如需要修改此[transitionPreviewData]的尺寸，则返回[true]
     */
    fun match(transitionPreviewData: TransitionPreviewData): Boolean

    /**
     * 如果[viewData]满足修改条件，则会回调此方法来要求修改具体的动画属性
     */
    fun onOverrideAnimationProperties(provider: AnimationPropertyProvider): Unit
}

/**
 * 500px以内的Gif图尺寸规则：
 *  - 最小尺寸为屏幕的50%
 *  - 最大尺寸为屏幕的100%（顶满全屏）
 */
private class SmallGifSizeRule : SizeRule {
    override fun match(transitionPreviewData: TransitionPreviewData): Boolean {
        val mimeType = transitionPreviewData.mimeType
        val width = transitionPreviewData.size.width
        val height = transitionPreviewData.size.height
        return ((mimeType == OVERRIDE_MIME_TYPE) && max(width, height) < OVERRIDE_MAX_SIZE)
    }

    override fun onOverrideAnimationProperties(provider: AnimationPropertyProvider) {
        provider.maxZoomScale = {
            provider.animationControl.let { animationControl ->
                val width = (provider.finalAabb.width() / animationControl.finalPose.scaleX).let {
                    if (it > 0) it else provider.contentSize.width
                }
                val height = (provider.finalAabb.height() / animationControl.finalPose.scaleY).let {
                    if (it > 0) it else provider.contentSize.height
                }
                ImageUtils.scaleImage(
                    width.toDouble(),
                    height.toDouble(),
                    provider.displaySize.width.toDouble(), provider.displaySize.height.toDouble(),
                    ImageUtils.SCALE_MODE_INSIDE
                ).toFloat().apply {
                    if (GProperty.DEBUG_PHOTO_GESTURE) {
                        GLog.d(TAG) {
                            "[getMaxZoomScale] current maxScale = $this"
                        }
                    }
                }
            }
        }

        provider.doubleTapZoomInScale = {
            provider.animationControl.let { animationControl ->
                val width = (provider.finalAabb.width() / animationControl.finalPose.scaleX).let {
                    if (it > 0) it else provider.contentSize.width
                }
                val height = (provider.finalAabb.height() / animationControl.finalPose.scaleY).let {
                    if (it > 0) it else provider.contentSize.height
                }
                ImageUtils.scaleImage(
                    width.toDouble(),
                    height.toDouble(),
                    provider.displaySize.width.toDouble(), provider.displaySize.height.toDouble(),
                    ImageUtils.SCALE_MODE_INSIDE
                ).toFloat()
            }
        }

        provider.minZoomScale = {
            provider.animationControl.let { animationControl ->
                val width = (provider.finalAabb.width() / animationControl.finalPose.scaleX).let {
                    if (it > 0) it else provider.contentSize.width
                }
                val height = (provider.finalAabb.height() / animationControl.finalPose.scaleY).let {
                    if (it > 0) it else provider.contentSize.height
                }
                ImageUtils.scaleImage(
                    width.toDouble(),
                    height.toDouble(),
                    provider.displaySize.width.toDouble() / 2, provider.displaySize.height.toDouble() / 2,
                    ImageUtils.SCALE_MODE_INSIDE
                ).toFloat()
            }
        }
    }

    companion object {
        private const val TAG = "SmallGifSizeRule"
        private const val OVERRIDE_MIME_TYPE = MimeTypeUtils.MIME_TYPE_IMAGE_GIF
        private const val OVERRIDE_MAX_SIZE = 500
    }
}