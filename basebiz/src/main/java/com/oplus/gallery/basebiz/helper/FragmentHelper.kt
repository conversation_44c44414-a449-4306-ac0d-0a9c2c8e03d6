/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FragmentHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/07/30
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yegua<PERSON><PERSON>@Apps.Gallery		2020/07/30		1.0		OPLUS_ARCH_EXTENDS
 ** wang<PERSON><PERSON>@Apps.Gallery		2020/08/11		1.1		OPLUS_ARCH_EXTENDS
 ** <EMAIL>         2020/08/29      1.2     OPLUS_ARCH_FRAGMENTSTACK
 *********************************************************************************/
package com.oplus.gallery.basebiz.helper

import android.content.res.Resources
import android.os.Bundle
import android.os.Looper
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentManager.POP_BACK_STACK_INCLUSIVE
import androidx.fragment.app.commit
import androidx.lifecycle.Lifecycle
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentStartType
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentState
import com.oplus.gallery.basebiz.uikit.fragment.base.IFragmentStack
import com.oplus.gallery.basebiz.uikit.fragment.base.StackEntry
import com.oplus.gallery.addon.osense.CpuFrequencyManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.router_lib.RouterManager
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.Keys.DEFAULT_TAG

private const val TAG = "FragmentHelper"
private const val ENTER_INDEX = 0
private const val EXIT_INDEX = 1
private const val POP_ENTER_INDEX = 2
private const val POP_EXIT_INDEX = 3
val NO_ANIM_ARRAY = intArrayOf(Resources.ID_NULL, Resources.ID_NULL, Resources.ID_NULL, Resources.ID_NULL)

/**
 * 方法为启动Fragment的操作
 * @param startType 启动方式 [FragmentStartType.ADD] or [FragmentStartType.REPLACE]
 * @param resId Fragment容器
 * @param postCard 路由Fragment的PostCard信息
 * @param tag tag
 * @param data 传输的数据
 * @param fragmentStack fragment栈，使用BaseActivity即可
 * @param addToBackStack 是否添加的回退栈
 * @param anim Fragment的入出动画
 *
 * @return 返回启动的Fragment，当Fragment在FragmentManager中存在，则使用缓存中的Fragment；
 *          当Fragment在回退栈中，则退栈道此Fragment位置；
 */
@Suppress("UNCHECKED_CAST")
fun FragmentManager.start(
    @FragmentStartType startType: Int? = null,
    resId: Int,
    postCard: PostCard,
    tag: String = DEFAULT_TAG,
    requestCode: Int? = null,
    data: Bundle? = null,
    fragmentStack: IFragmentStack,
    addToBackStack: Boolean = false,
    anim: IntArray = NO_ANIM_ARRAY,
    flags: Int = 0
): Fragment? {
    return RouterManager.routerCenter.getRouter(postCard)?.getRouterClass()?.run {
        start(startType, resId, this as Class<out BaseFragment>, tag, requestCode, data, fragmentStack, addToBackStack, anim, flags)
    }
}

/**
 * 方法为启动Fragment的操作
 * @param startType 启动方式 [FragmentStartType.ADD] or [FragmentStartType.REPLACE]
 * @param resId Fragment容器
 * @param fragmentClass 需要启动的Fragment的Class
 * @param tag tag
 * @param data 传输的数据
 * @param localStack fragment栈，使用BaseActivity即可
 * @param addToBackStack 是否添加的回退栈
 * @param anim Fragment的入出动画
 *
 * @return 返回启动的Fragment，当Fragment在FragmentManager中存在，则使用缓存中的Fragment；
 *          当Fragment在回退栈中，则退栈道此Fragment位置；
 */
fun FragmentManager.start(
    @FragmentStartType startType: Int? = null,
    resId: Int,
    fragmentClass: Class<out BaseFragment>,
    tag: String = DEFAULT_TAG,
    requestCode: Int? = null,
    data: Bundle? = null,
    localStack: IFragmentStack,
    addToBackStack: Boolean = false,
    anim: IntArray = NO_ANIM_ARRAY,
    flags: Int = 0
): Fragment? = start(startType, resId, fragmentClass, tag, requestCode, data, localStack, addToBackStack, anim, flags, true)

/**
 * 方法为启动Fragment的操作
 * @param startType 启动方式 [FragmentStartType.ADD] or [FragmentStartType.REPLACE]
 * @param resId Fragment容器
 * @param fragmentClass 需要启动的Fragment的Class
 * @param tag tag
 * @param data 传输的数据
 * @param localStack fragment栈，使用BaseActivity即可
 * @param addToBackStack 是否添加的回退栈
 * @param anim Fragment的入出动画
 * @param needParent 是否需要设置父亲
 *
 * @return 返回启动的Fragment，当Fragment在FragmentManager中存在，则使用缓存中的Fragment；
 *          当Fragment在回退栈中，则退栈道此Fragment位置；
 */
@Suppress("LongMethod")
fun FragmentManager.start(
    @FragmentStartType startType: Int? = null,
    resId: Int,
    fragmentClass: Class<out BaseFragment>,
    tag: String = DEFAULT_TAG,
    requestCode: Int? = null,
    data: Bundle? = null,
    localStack: IFragmentStack,
    addToBackStack: Boolean = false,
    anim: IntArray = NO_ANIM_ARRAY,
    flags: Int = 0,
    needParent: Boolean
): Fragment? = GTrace.trace("$TAG.start") {
    CpuFrequencyManager.setAction(CpuFrequencyManager.Action.GAME_BOOST_L3, CpuFrequencyManager.TIMEOUT_120MS)
    if (!Looper.getMainLooper().isCurrentThread) {
        if (GProperty.DEBUG) {
            throw IllegalArgumentException("Must invoke FragmentManager.start() in main thread!")
        } else {
            GLog.e(TAG, "FragmentManager.start(). Must invoke FragmentManager.start() in main thread!")
        }
    }
    val realTag = tag.takeIf { it != DEFAULT_TAG } ?: fragmentClass.name
    // 解决BUG 4871669,主线程卡顿时,触发两次start,导致前一次的Tag没有更新到FragmentStore中,所以还需要check一次localStack
    val localFragment = findFragmentByTag(realTag)
    if ((localFragment == null) && (localStack.findFragment(realTag) != null)) {
        GLog.w(TAG, "FragmentManager.start(). localFragmentTag not post to FragmentStore!")
        return@trace null
    }
    return@trace (localFragment as? BaseFragment)?.let { fragment ->
        if (localStack.findFragment(realTag) == null) {
            commit(true) {
                remove(localFragment)
            }
            GLog.e(TAG, "FragmentManager.start localFragment:$localFragment but localStack don't have realTag:$realTag")
            return@let null
        }
        if (isStateSaved) {
            GLog.e(TAG, "start: isStateSaved")
            return@trace null
        }
        fragment.arguments = data
        localStack.findFragment(realTag)?.apply {
            getFragment(this@start)?.fragmentState = null
            localStack.pop(realTag, flags)
        }
        if (flags == POP_BACK_STACK_INCLUSIVE) {
            null
        } else {
            /*
            * 解决BUG 3563718，时间轴连续快速点击2次图片进入大图，大图下拉回到时间轴，滑动灰图
            * 连续2次启动大图页面时，第二次启动会复用第一次启动的大图Fragment
            * 由于上面把fragmentState置空了，此处需要给复用的对象重新创建FragmentState并设置它的parent
            * 大图退出onDestroy时就可以走FragmentState的removeParent方法来触发TimelineTabFragment走onResume，使得时间轴回到前台
            * localStack.peek(1) 是找栈顶的上一个，也就是要复用的Fragment的parent
            */
            val parentStackEntry = localStack.peek(1)
            val fragmentState = fragment.fragmentState
                ?: FragmentState(this@start, localStack.getStateMap()).also { fragment.fragmentState = it }
            setFragmentStateParent(fragment, addToBackStack, realTag, parentStackEntry, requestCode, fragmentState)
            fragment
        }
    } ?: fragmentClass.newInstance().apply {
        arguments = data
        val topStackEntry = if (needParent) localStack.peek() else null
        commit(true) {
            setCustomAnimations(anim[ENTER_INDEX], anim[EXIT_INDEX], anim[POP_ENTER_INDEX], anim[POP_EXIT_INDEX])
            when (startType ?: startType()) {
                FragmentStartType.ADD -> add(resId, this@apply, realTag)
                FragmentStartType.REPLACE -> replace(resId, this@apply, realTag)
                else -> throw IllegalArgumentException("do not use type <FragmentStartType.UNDEFINED>")
            }
            if (addToBackStack) {
                /*
                * 当第一个Fragment入栈时，需要让其加入到fragmentStack的栈，但不加入到FragmentManager的栈，
                * 目的是为了控制Fragment的生命周期 需求id ：2106072
                */
                if (localStack.fragmentStackSize() > 0) {
                    addToBackStack(realTag)
                }
                localStack.push(StackEntry(realTag))
            }
        }
        val fragmentState = fragmentState ?: FragmentState(this@start, localStack.getStateMap()).also { fragmentState = it }
        setFragmentStateParent(this, addToBackStack, realTag, topStackEntry, requestCode, fragmentState)
    }
}

fun FragmentManager.setFragmentStateParent(
    fragment: Fragment,
    addToBackStack: Boolean,
    localTag: String,
    topStackEntry: StackEntry?,
    requestCode: Int?,
    fragmentState: FragmentState
) {
    if (addToBackStack) {
        fragmentState.setParent(localTag, Lifecycle.State.RESUMED, topStackEntry?.tag, Lifecycle.State.STARTED)
        requestCode?.let { fragment.setTargetFragment(topStackEntry?.getFragment(this), it) }
    } else {
        fragmentState.setParent(localTag, Lifecycle.State.RESUMED, topStackEntry?.tag, Lifecycle.State.RESUMED)
    }
}

fun FragmentManager.hide(tag: String) {
    findFragmentByTag(tag)?.apply {
        commit(true) { hide(this@apply) }
    }
}

fun FragmentManager.show(tag: String) {
    findFragmentByTag(tag)?.apply {
        commit(true) { show(this@apply) }
    }
}

fun FragmentManager.findFragmentByPostCard(postCard: PostCard): Fragment? {
    return RouterManager.routerCenter.getRouter(postCard)?.clazz?.let {
        findFragmentByTag(it)
    }
}

fun createFragmentByPostCard(postCard: PostCard): Fragment? {
    return RouterManager.routerCenter.getRouter(postCard)?.getRouterClass()?.let {
        it.newInstance() as Fragment
    }
}