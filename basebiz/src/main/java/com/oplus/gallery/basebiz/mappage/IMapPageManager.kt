/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - IMapPageManager.kt
 * Description: 对外暴露的地图业务接口，实现类在MapPageManager中
 * Version: 1.0
 * Date: 2024/5/6
 * Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>      2025/5/6     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.basebiz.mappage

import android.content.Context
import android.view.ViewGroup
import com.oplus.gallery.framework.abilities.map.IMapAbility.CaptureMapRegionListener

interface IMapPageManager {
    /**
     * 初始化Map的sdk，需要在Application或Map使用前调用
     */
    fun initMapSdk(context: Context)

    /**
     * 判断MapSdk是否已经初始化
     */
    fun isMapSdkInited(context: Context): Boolean

    /**
     * 图片详情页小地图显示
     * @param mapViewContainer mapView所属的外部父布局
     * @param imageId 图片Path
     */
    fun initDetailOuter(context: Context, mapViewContainer: ViewGroup?, imageId: String?, listener: CaptureMapRegionListener?)

    /**
     * 生命周期函数，所在的Activity或Fragment的onPause或onStop的时候调用
     */
    fun onPause()

    /**
     * 生命周期函数，所在的Activity或Fragment的onStart或onResume的时候调用
     */
    fun onResume()

    /**
     * 生命周期函数，所在的Activity或Fragment的onDestroy的时候调用
     */
    fun onDestroy()

    /**
     * 触发手机定位，定位到手机当前位置
     */
    fun locateInCurrentPosition()

    /**
     * 初始化发现页地图入口的MapView，根据入参initMapConfig对MapView以及内部的图集聚合以及图集显示类进行初始化
     * @param mapViewContainer mapView所属的外部父布局
     */
    fun initExplorerPageMapView(mapViewContainer: ViewGroup?, listener: CaptureMapRegionListener?)
}