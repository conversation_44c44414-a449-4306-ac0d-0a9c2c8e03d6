/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WatermarkSupportCheckStrategy
 ** Description: 水印下载支持检查策略
 ** Version: 1.0
 ** Date : 2024/8/15
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2024/8/15    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.basebiz.helper.function.restrict

import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.helper.function.restrict.MaterialSupportCheckStrategy.Companion.SIZE
import com.oplus.gallery.basebiz.uiflowinterceptor.StorageDialogInterceptor
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * 水印下载支持检查策略
 * @param needSilence 是否静默下载, 拦截器不提示失败操作
 * @param isClearRedundantResources 是否清除冗余资源, 下载完成后是否删除旧的水印资源
 * @param requestLimits 请求限制，4小时请求控制，时间轴页面做控制，大师水印页不做控制
 */
class WatermarkSupportCheckStrategy(
    private val needSilence: Boolean = false,
    private val isClearRedundantResources: Boolean = false,
    private val requestLimits: Boolean = false
) : ISupportCheckStrategy {

    /**
     * 拦截器的链路
     */
    private var chain: RealInterceptorChain? = null

    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        val interceptors = listOf(
            // 检视是否已允许联网下载
            WatermarkAllowInterceptor(postNotificationAction),
            // 检视是否已允许后台下载权限
            PrivacyPermissionInterceptor(postNotificationAction),
            // 检视是否有可用网络
            NetworkConfirmInterceptor(postNotificationAction, needSilence),
            // 检视是否有足够的存储空间
            StorageDialogInterceptor(
                context,
                SIZE,
                R.string.picture_editor_no_storage_space,
                R.string.picture_editor_download_again_after_free_up,
                postNotificationAction),
            // 通过网络拉取元数据，检视是否需要更新
            CheckWatermarkUpdateInterceptor(needSilence, requestLimits, postNotificationAction),
            // 下载水印样式json
            WatermarkDownloadInterceptor(needSilence, isClearRedundantResources, postNotificationAction)
        )

        chain?.destroy()
        chain = RealInterceptorChain(
            interceptors,
            onSuccessCallback = {
                function.invoke(true)
            },
            onFailCallback = {
                function.invoke(false)
            }
        )
        chain?.proceed(Bundle())
    }

    override fun destroy() {
        chain?.destroy()
        chain = null
    }
}

/**
 * 水印下载条件检查策略
 */
class MaterialSupportCheckStrategy : ISupportCheckStrategy {

    /**
     * 拦截器的链路
     */
    private var chain: RealInterceptorChain? = null

    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        val interceptors = listOf(
            // 检视是否已允许联网下载
            WatermarkAllowInterceptor(postNotificationAction),
            // 检视是否已允许后台下载权限
            PrivacyPermissionInterceptor(postNotificationAction),
            // 检视是否有可用网络
            NetworkConfirmInterceptor(postNotificationAction),
            // 检视是否有足够的存储空间
            StorageDialogInterceptor(
                context,
                SIZE,
                R.string.picture_editor_no_storage_space,
                R.string.picture_editor_download_again_after_free_up,
                postNotificationAction)
        )

        chain?.destroy()
        chain = RealInterceptorChain(
            interceptors,
            onSuccessCallback = {
                function.invoke(true)
            },
            onFailCallback = {
                function.invoke(false)
            }
        )
        chain?.proceed(Bundle())
    }

    override fun destroy() {
        chain?.destroy()
        chain = null
    }

    companion object {
        /**
         * 水印下载至少预留存储空间 20MB
         */
        const val SIZE = 20 * AppConstants.Capacity.UNIT_MB2B
    }
}
