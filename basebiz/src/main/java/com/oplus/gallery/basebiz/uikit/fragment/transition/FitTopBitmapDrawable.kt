/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FitTopBitmapDrawable.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/06/11
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2025/06/11		1.0		OPLUS_FEATURE_TAB
 *********************************************************************************/

package com.oplus.gallery.basebiz.uikit.fragment.transition

import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.ColorFilter
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.Shader.TileMode
import android.graphics.drawable.Drawable

/**
 * bitmap居顶绘制且填充风格为 TileMode.CLAMP 的drawable
 */
class FitTopBitmapDrawable(
    private var bitmap: Bitmap,
    radius: Int,
) : Drawable() {
    private val clampPaint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG)
    private var backgroundShader: BitmapShader? = null
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG)
    private val bitmapBounds = Rect(0, 0, bitmap.width, bitmap.height)
    private val layerPaint = Paint()

    init {
        /**获取bitmap除开描边的最后一行的像素的颜色**/
        val lastRowPixelColorArray = IntArray(bitmap.width)
        bitmap.getPixels(lastRowPixelColorArray, 0, bitmap.width, 0, (bitmap.height - 1 - ACCESS_COLOR_OFFSET), bitmap.width, 1)

        /**因为是圆角，使用左侧radius半径后的有效的像素的颜色来覆盖左侧半径内其他透明的或半透明的像素的颜色**/
        val leftFillColor = lastRowPixelColorArray[radius]

        /**因为是圆角，使用右侧radius半径后的有效的像素的颜色来覆盖右侧半径内其他透明的或半透明的像素的颜色**/
        val rightFillColor = lastRowPixelColorArray[lastRowPixelColorArray.lastIndex - radius]
        for (i in 0..radius) {
            lastRowPixelColorArray[i] = leftFillColor
            lastRowPixelColorArray[lastRowPixelColorArray.lastIndex - i] = rightFillColor
        }

        /**生成一个高度为1的bitmap，使用这个bitmap就在它颜色对应的哪一行的位置绘制，像素会在X、Y方向上延伸铺满**/
        Bitmap.createBitmap(bitmap.width, 1, Bitmap.Config.ARGB_8888).apply {
            setPixels(lastRowPixelColorArray, 0, width, 0, 0, width, 1)
            backgroundShader = BitmapShader(this, TileMode.CLAMP, TileMode.CLAMP)
            clampPaint.setShader(backgroundShader)
        }
    }

    override fun onBoundsChange(bounds: Rect) {
        updateShaderMatrix()
    }

    private fun updateShaderMatrix() {
        val bounds = bounds
        if (bounds.width() <= 0 || bounds.height() <= 0) {
            return
        }
        val realHeight = bounds.width() * bitmap.height / bitmap.width
        bitmapBounds.set(0, 0, bounds.width(), realHeight)

        val clampMatrix = Matrix()
        val scale = bounds.width().toFloat() / bitmap.width
        clampMatrix.setScale(scale, scale)

        /**高度为1的bitmap位移到描边之上绘制，像素延伸在X，Y两个方向上进行**/
        val dy = bitmap.height * scale - ACCESS_COLOR_OFFSET
        clampMatrix.postTranslate(0f, dy)

        backgroundShader?.setLocalMatrix(clampMatrix)
    }

    override fun draw(canvas: Canvas) {
        val layerId = canvas.saveLayer(0f, 0f, bounds.width().toFloat(), bounds.height().toFloat(), layerPaint)
        try {
            canvas.drawRect(bounds, clampPaint)
            paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_ATOP))
            canvas.drawBitmap(bitmap, null, bitmapBounds, paint)
            paint.setXfermode(null)
        } finally {
            canvas.restoreToCount(layerId)
        }
    }

    override fun setAlpha(alpha: Int) {
        clampPaint.alpha = alpha
        paint.alpha = alpha
        layerPaint.alpha = alpha
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        clampPaint.colorFilter = colorFilter
        paint.colorFilter = colorFilter
        layerPaint.colorFilter = colorFilter
    }

    override fun getOpacity(): Int {
        return PixelFormat.TRANSLUCENT
    }

    companion object {
        /**
         * 取色时，选取像素行数的位移量
         * 考虑到bitmap可能会有描边的情况出现，综合考虑将位移量定值为 3
         */
        private const val ACCESS_COLOR_OFFSET = 3
    }
}