/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AiCloudRequester.kt
 ** Description: AI云端请求的通用封装,自动完成token的构建和发起请求
 ** Version: 1.0
 ** Date : 2022/4/22
 ** Author: xie<PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: AiCloudRequester
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xie<PERSON><PERSON><PERSON>@Apps.Gallery3D      2022/04/22    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.basebiz.helper

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusNetServiceManager
import com.oplus.gallery.basebiz.permission.NetSendPrivacyPermissionRule
import com.oplus.gallery.foundation.networkaccess.INetSendRule
import com.oplus.gallery.foundation.networkaccess.base.BaseRequestParam
import com.oplus.gallery.foundation.networkaccess.convert.OkhttpResponseConvert
import com.oplus.gallery.foundation.networkaccess.param.JsonTextRequestParam
import com.oplus.gallery.foundation.security.EncryptUtils
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.download.IDownloadAbility
import com.oplus.gallery.framework.abilities.download.networkrequest.CommonCallbackConvert
import com.oplus.gallery.framework.abilities.download.networkrequest.PostRequest
import com.oplus.gallery.framework.abilities.download.networkrequest.header.HttpHeader
import com.oplus.gallery.framework.abilities.download.networkrequest.header.ParamKey.AUTHORIZATION
import com.oplus.gallery.framework.abilities.download.networkrequest.header.ParamKey.CONTENT_TYPE
import com.oplus.gallery.framework.app.getAppAbility
import java.util.TreeMap

/**
 * ai能力的网络构造器
 */
class AiCloudRequester private constructor(
    private val context: Context,
    private val subUrl: String,
    private val body: String,
    private val params: Map<String, String>,
    private val headers: Map<String, String>
) {

    /**
     * 构建验证的token并发起网络请求
     * @param converter 结果的转换器,将返回的字节流数据转为期望的值返回
     * @return 返回网络请求的结果,此结果经过[converter]转换
     */
    fun <R> execute(converter: OkhttpResponseConvert<R?>): R? {
        val baseUrl = ApiDmManager.getAppDM().getSecurityUrl()?.aiCloudHostName ?: let {
            GLog.w(TAG, "execute fail, ISecurityUrl is null")
            return null
        }
        val token = buildToken(System.currentTimeMillis())
        return runCatching {
            var result: R? = null
            context.getAppAbility<IDownloadAbility>()?.use { downloadAbility ->
                val aiNetworkRequest =
                    AiNetworkRequest(context, NetSendPrivacyPermissionRule(), "$baseUrl$subUrl", buildHeader(token), body, converter)
                result = downloadAbility.execute(context, aiNetworkRequest)?.response
            }
            result
        }.onFailure {
            GLog.w(TAG, "execute error", it)
        }.getOrNull()
    }

    @VisibleForTesting
    fun buildToken(timeStamp: Long): String {
        // 1. 构建认证字符串前缀，格式为 ai-auth-v1/{appId}/{timestamp}, timestamp为时间戳，精确到毫秒，用以验证请求是否失效
        val authTimestampPrefix = "$AUTH_PREFIX$timeStamp/"
        val sb = StringBuilder(authTimestampPrefix)
        // 2. 构建url参数字符串，按照参数名字典序升序排列
        val treeMap: Map<String, String> = TreeMap(params)
        treeMap.forEach { (key: String, value: String) ->
            sb.append(key).append("=").append(value).append("&")
        }
        // 3. 拼接签名原文字符串
        val signStr = sb.append(body).toString()
        // 4. hmac_sha_256算法签名
        val signature = EncryptUtils.encryptWithHMac256(
            OplusNetServiceManager.getInstance().aiCloudTokenPassword,
            signStr
        )
        // 5. 拼接认证字符串
        return authTimestampPrefix + signature
    }

    private fun buildHeader(token: String): MutableMap<String, String?> {
        return mutableMapOf<String, String?>().apply {
            putAll(HttpHeader.getOplusHeaders(
                ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not() && PROPERTY_IS_OP_BRAND)
            )
            putAll(headers)
            this[AUTHORIZATION] = token
            this[CONTENT_TYPE] = CONTENT_TYPE_JSON
        }
    }

    /**
     * 通过此Build来构建AiCloudRequester
     */
    class Builder(private val context: Context) {

        private val headers = HashMap<String, String>()
        private var body = ""
        private var params: Map<String, String>? = null
        private var subUrl = ""

        fun addHeaders(headers: Map<String, String>): Builder {
            this.headers.putAll(headers)
            return this
        }

        fun setBody(body: String): Builder {
            this.body = body
            return this
        }

        fun setParams(params: Map<String, String>): Builder {
            this.params = params
            return this
        }

        fun setSubUrl(subUrl: String): Builder {
            this.subUrl = subUrl
            return this
        }

        fun setRequestCryptoParam(requestParam: String): Builder {
            headers[AI_CRYPTO_REQUEST_PARAM] = requestParam
            return this
        }

        fun setResponseCryptoParam(responseParam: String): Builder {
            headers[AI_CRYPTO_RESPONSE_PARAM] = responseParam
            return this
        }

        fun setCryptoKey(cryptoKey: String): Builder {
            headers[AI_CRYPTO_KEY] = cryptoKey
            return this
        }

        fun setCryptoAlgorithm(algorithm: String): Builder {
            headers[AI_CRYPTO_ALGORITHM_KEY] = algorithm
            return this
        }

        fun build(): AiCloudRequester {
            return AiCloudRequester(context, subUrl, body, params ?: emptyMap(), headers)
        }
    }

    companion object {
        const val AI_CRYPTO_ALGORITHM_AES_RSA = "AES_RSA"

        private const val APP_ID = "25617"
        private const val TAG = "AiCloudRequester"
        private const val AI_CRYPTO_KEY = "ai-api-crypto-key"
        private const val AUTH_PREFIX = "ai-auth-v1/$APP_ID/"
        private const val CONTENT_TYPE_JSON = "application/json"
        private const val AI_CRYPTO_REQUEST_PARAM = "ai-api-crypto-request"
        private const val AI_CRYPTO_RESPONSE_PARAM = "ai-api-crypto-response"
        private const val AI_CRYPTO_ALGORITHM_KEY = "ai-api-crypto-algorithm"
    }
}

/**
 * ai能力的网络请求
 * @param url 请求的url
 * @param header 请求头
 * @param body 请求体
 * @param convert responseBean的转换器
 */
class AiNetworkRequest<R>(
    context: Context,
    allowRequestRule: INetSendRule,
    override val url: String,
    override val header: MutableMap<String, String?>,
    body: String,
    convert: OkhttpResponseConvert<R?>
) : PostRequest<String, R?>(context, allowRequestRule) {

    override val logTag = TAG
    override val requestParam: BaseRequestParam<String> = JsonTextRequestParam(body)
    override val responseConvert = convert
    override val callbackConvert = CommonCallbackConvert<R>()

    companion object {
        const val TAG = "AIRequest"
    }
}