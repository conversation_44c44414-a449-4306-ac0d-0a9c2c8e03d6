/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIUnitModelDownloadInterceptor.kt
 ** Description: AIUnit模型下载的拦截器
 ** Version: 1.0
 ** Date : 2024/7/1
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2024/07/01    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.basebiz.uiflowinterceptor.aiunit

import android.content.Context
import android.os.Bundle
import com.oplus.aiunit.core.data.DetectName
import com.oplus.aiunit.core.data.SceneName
import com.oplus.aiunit.meowai.detector.DetectName.CLOUD_IMAGE_AI_GRAFFITI
import com.oplus.aiunit.meowai.detector.DetectName.CLOUD_IMAGE_DEGLARE
import com.oplus.aiunit.meowai.detector.DetectName.VISION_IMAGE_SHARPEN
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.notification.NoNetworkToastAction
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.ext.runOnWorkThread
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.DETECTOR_NAME_KEY_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_UNIT_CONFIG_AVAILABLE
import com.oplus.gallery.framework.abilities.download.AIUnitDownloadListener
import com.oplus.gallery.framework.abilities.download.AIUnitDownloadWord
import com.oplus.gallery.framework.abilities.download.AIUnitPluginID
import com.oplus.gallery.framework.abilities.download.DetectNamePluginID
import com.oplus.gallery.framework.abilities.download.IAIUnitDownloadAbility
import com.oplus.gallery.framework.abilities.download.IAIUnitPluginState
import com.oplus.gallery.framework.abilities.download.SceneNamePluginID
import com.oplus.gallery.framework.abilities.download.aiunit.AIUnitDownloadState
import com.oplus.gallery.framework.abilities.scan.face.AIFaceConstants
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * AIUnit模型下载的拦截器
 * @param context Context
 * @param pluginState 插件及插件的状态
 * @param word 下载的相关词条，默认值为 null。当静默下载时可不弹框。
 * @param updateUI UI更新通知，默认值为空实现。
 * @param enableUI 是否启用UI
 * @param startDownload 开始下载的回调
 *
 * mark by xiewujie:合理的设计是将所有AI编辑的拦截器放到能力层做成可配置的，改造工作量太大，先放这里
 */
class AIUnitModelDownloadInterceptor(
    private val context: Context,
    private val pluginState: IAIUnitPluginState,
    private val word: AIUnitDownloadWord? = null,
    private val updateUI: (NotificationAction) -> Unit = {},
    private val enableUI: () -> Boolean = { true },
    private val startDownload: (() -> Unit)? = null
) : ConditionInterceptor() {

    private var listener: AIUnitDownloadListener? = null
    private var downloadAbility: IAIUnitDownloadAbility? = null

    init {
        downloadAbility = context.getAppAbility<IAIUnitDownloadAbility>()
    }

    /**
     * 下载插件的条件
     * 1. 需要下载或者需要更新
     * 2. aiunit插件状态无效
     */
    override fun onCheckCondition(param: Bundle): Boolean {
        val downloadable = pluginState.downloadable()
        val condition = (downloadable || pluginState.updatable()).not() && pluginState.isStateValid()
        GLog.d(TAG, "onCheckCondition, condition:$condition, state：$pluginState")
        return condition
    }

    override fun onConditionFailed(chain: IInterceptor.IChain<Bundle, Unit>) {
        if (NetworkMonitor.isNetworkValidated()) {
            download(chain)
        } else {
            updateUI(NoNetworkToastAction())
        }
    }

    private fun download(chain: IInterceptor.IChain<Bundle, Unit>) {
        AppScope.launch(Dispatchers.IO) {
            if (pluginState.updatable()) {
                context.getAppAbility<ISettingsAbility>()?.use {
                    it.setAIUnitModelUpdateTimestamp(pluginState.updateTimeStampConfigId, System.currentTimeMillis())
                }
            }
            listener = createListener(chain)
            getDownloadAbility()?.let {
                pluginState.pluginId.sceneName?.let { sceneName -> it.unregisterListener(sceneName) }
                it.startDownload(
                    context,
                    pluginState,
                    word,
                    enableUI.invoke(),
                    chain.param.getBoolean(KEY_IS_SKIP_CONFIRM),
                    listener
                )
                runOnUiThread {
                    startDownload?.invoke()
                }
            } ?: chain.fail(chain.param)
        }
    }

    private fun getDownloadAbility(): IAIUnitDownloadAbility? {
        if (downloadAbility == null) {
            downloadAbility = context.getAppAbility<IAIUnitDownloadAbility>()
        }
        return downloadAbility
    }

    override fun destroy() {
        super.destroy()
        listener?.let { downloadAbility?.unregisterListener(pluginState.pluginId.requestID) }
        listener = null
        downloadAbility?.close()
        downloadAbility = null
    }

    private fun createListener(chain: IInterceptor.IChain<Bundle, Unit>): AIUnitDownloadListener {
        return object : AIUnitDownloadListener {
            override fun onCancel() {
                GLog.d(TAG, "[onCancel] onCancel")
                runOnUiThread {
                    chain.param.putBoolean(MODEL_DOWNLOAD_CANCEL, true)
                    chain.fail(chain.param)
                }
            }

            override fun onFail(err: Int) {
                GLog.e(TAG, "[onFail] ---$this ------plugin: ${pluginState.pluginId}, errorCode:$err")
                runOnUiThread {
                    chain.param.putInt(MODEL_DOWNLOAD_ERROR_CODE, err)
                    chain.fail(chain.param)
                }
                // 静默下载时，下载失败不会有任何提示语，需要相册主动toast提示
                if (enableUI.invoke().not()) {
                    word?.downloadRetryResId?.let {
                        ToastUtil.showLongToast(it)
                    }
                }
            }

            override fun onInstall() {
                GLog.d(TAG, "[onInstall]")
            }

            override fun onPrepare(fullSize: Long, offsetSize: Long) {
                GLog.d(TAG, "[onPrepare] fulSize:$fullSize, offsetSize:$offsetSize")
            }

            override fun onProgress(fullSize: Long, offsetSize: Long, speed: Long) {
                if (DEBUG_SEARCH) {
                    GLog.d(TAG, "[onProgress] plugin: ${pluginState.pluginId}, fulSize:$fullSize, offsetSize:$offsetSize, speed: $speed")
                }
            }

            override fun onStart(fullSize: Long, offsetSize: Long) {
                chain.param.getString(KEY_CACHE_PLUGIN_CONFIG_ID)?.let { configId ->
                    context.getAppAbility<ISettingsAbility>()?.use {
                        it.setPluginSize(configId, fullSize)
                    }
                }
                chain.param.putBoolean(MODEL_DOWNLOAD_START, true)
                runOnUiThread {
                    chain.progress(chain.param)
                }
                GLog.d(TAG, "[onStart]")
            }

            override fun onSuccess(fullSize: Long, downloadSize: Long, fromBreakpoint: Boolean) {
                GLog.d(TAG, "[onSuccess] fulSize:$fullSize, downloadSize:$downloadSize, fromBreakPoint:$fromBreakpoint")
                notifySuccess()
            }

            override fun onUnnecessaryDownload() {
                GLog.d(TAG, LogFlag.DL, "[onUnnecessaryDownload]")
                runOnUiThread {
                    chain.proceed(chain.param)
                }
            }

            private fun notifySuccess() {
                //下载完成后，按需更新下aiUnit的状态
                runOnWorkThread {
                    pluginState.sync(context)
                    runOnUiThread {
                        chain.param.putBoolean(MODEL_DOWNLOAD_SUCCESS, true)
                        chain.proceed(chain.param)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "AIUnitModelDownloadInterceptor"

        /**
         * 标记模型下载成功的key
         */
        const val MODEL_DOWNLOAD_SUCCESS = "model_download_success"

        /**
         * 标记模型下载手动取消
         */
        const val MODEL_DOWNLOAD_CANCEL = "model_download_cancel"

        /**
         * 标记模型下载开始
         */
        const val MODEL_DOWNLOAD_START = "model_download_start"

        /**
         * 模型下载失败错误吗
         */
        const val MODEL_DOWNLOAD_ERROR_CODE = "model_download_error_code"

        const val KEY_CACHE_PLUGIN_CONFIG_ID = "key_cache_plugin_config_id"

        /**
         * 是否跳过下载确认
         */
        const val KEY_IS_SKIP_CONFIRM = "key_is_skip_confirm"
    }
}

/**
 * 旅程封面优选算法场景名称
 */
private const val SCENE_NAME_TRAVEL_COVER_SELECT = "travel_album"

/**
 * 融合搜索所需的 clip 和 text embedding 算法场景名称
 */
private const val SCENE_NAME_ANDES_SEARCH = "gallery_search"

/**
 * 宠物模型算法名称
 */
private const val SCENE_NAME_AI_PETS = "ai_pets"

/**
 * CAPTION 模型下载所需场景名称
 */
const val SCENE_NAME_AI_CAPTION = "ai_caption"

enum class AIUnitPlugin(val downloadPlugin: AIUnitPluginID) {
    /**
     * 缺陷推荐模型插件
     */
    INTELLI_FUNC(DetectNamePluginID(DetectName.VISION_IMAGE_DEFECT_RECOGNITION)),

    /**
     * realme去模糊插件
     */
    RM_AI_DEBLUR_LOCAL(DetectNamePluginID(VISION_IMAGE_SHARPEN)),

    /**
     *  AI去模糊功能插件，包含去模糊和隐性水印插件
     */
    AI_DEBLUR(DetectNamePluginID(DetectName.VISION_IMAGE_DEBLUR, DetectName.VISION_IMAGE_IMPLICIT_WATER_MASK)),

    /**
     * AI去反光功能插件，包含去反光和隐性水印插件
     */
    AI_DEREFLECTION(DetectNamePluginID(DetectName.VISION_IMAGE_DEREFLECTION, DetectName.VISION_IMAGE_IMPLICIT_WATER_MASK)),

    /**
     * realmeAI涂鸦功能插件，包含去反光和隐性水印插件
     */
    AI_GRAFFITI(DetectNamePluginID(CLOUD_IMAGE_AI_GRAFFITI, DetectName.VISION_IMAGE_IMPLICIT_WATER_MASK)),

    /**
     * AI 构图功能插件
     */
    AI_COMPOSITION(DetectNamePluginID(DetectName.VISION_IMAGE_AI_COMPOSITION)),

    /**
     * AI 补光功能插件
     */
    AI_LIGHTING(DetectNamePluginID(DETECTOR_NAME_KEY_AI_LIGHTING)),

    /**
     * AI 最佳表情，没有sceneName，需要通过detectName去下载
     */
    AI_BEST_TAKE(DetectNamePluginID(AIFaceConstants.DETECT_NAME)),

    /**
     * 画质增强的增强模型
     */
    IMAGE_QUALITY_ENHANCE(SceneNamePluginID(SceneName.CLOUD_PICTURE_QUALITY_ENHANCEMENT)),

    /**
     * 自动打码
     */
    AUTO_MOSAIC(SceneNamePluginID(SceneName.AI_PRIVACY_MOSAIC)),

    /**
    * AI 去眩光功能插件
    */
    AI_DEGLARE(DetectNamePluginID(CLOUD_IMAGE_DEGLARE, DetectName.VISION_IMAGE_IMPLICIT_WATER_MASK)),

    /**
     * 融合搜索需要的 Clip 模型和 Ocr Embedding 模型
     */
    ANDES_SEARCH(SceneNamePluginID(SCENE_NAME_ANDES_SEARCH)),

    /**
     * 旅程封面优选
     */
    TRAVEL_COVER_SELECT(SceneNamePluginID(SCENE_NAME_TRAVEL_COVER_SELECT)),

    /**
     * 宠物模型
     */
    AI_PETS(SceneNamePluginID(SCENE_NAME_AI_PETS)),

    /**
     * ai_caption
     */
    AI_CAPTION(SceneNamePluginID(SCENE_NAME_AI_CAPTION))
}

/**
 * 通过sceneName进行插件下载的插件状态
 * @param pluginId 插件
 * @param downloadableConfigID 是否可下载的配置ID
 * @param updatableConfigID 是否可更新的配置ID
 * @param updateTimeStampConfigId 更新的时间戳
 */
data class SceneNamePluginState(
    override val pluginId: AIUnitPluginID,
    val downloadableConfigID: String,
    val updatableConfigID: String,
    override val updateTimeStampConfigId: String,
) : IAIUnitPluginState {

    override val downloadable: () -> Boolean = {
        ConfigAbilityWrapper.getBoolean(downloadableConfigID, false)
    }

    override val updatable: () -> Boolean = {
        ConfigAbilityWrapper.getBoolean(updatableConfigID, false)
    }
    override val stateChangId: String = downloadableConfigID

    override val isStateValid: () -> Boolean = {
        ConfigAbilityWrapper.getBoolean(IS_AI_UNIT_CONFIG_AVAILABLE)
    }

    override fun sync(context: Context) {
        context.getAppAbility<ISettingsAbility>()?.use {
            it.updateBlockingConfig(
                context,
                downloadableConfigID,
                updatableConfigID,
                IS_AI_UNIT_CONFIG_AVAILABLE
            )
        }
    }
}

/**
 * aiunit插件下载的插件状态
 * @param pluginId 插件
 * @param downloadStateConfigId 插件的下载状态
 * @param updateTimeStampConfigId 更新插件的时间戳
 */
data class AIUnitPluginState(
    override val pluginId: AIUnitPluginID,
    val downloadStateConfigId: String,
    override val updateTimeStampConfigId: String,
) : IAIUnitPluginState {

    override val downloadable: () -> Boolean = {
        val state = ConfigAbilityWrapper.getInt(downloadStateConfigId, 0)
        state == AIUnitDownloadState.DOWNLOAD_LOST
    }

    override val updatable: () -> Boolean = {
        val state = ConfigAbilityWrapper.getInt(downloadStateConfigId, 0)
        state == AIUnitDownloadState.DOWNLOAD_NEW
    }

    override val isStateValid: () -> Boolean = {
        ConfigAbilityWrapper.getBoolean(IS_AI_UNIT_CONFIG_AVAILABLE)
    }

    override val stateChangId: String = downloadStateConfigId

    override fun sync(context: Context) {
        context.getAppAbility<ISettingsAbility>()?.use {
            it.updateBlockingConfig(
                context,
                downloadStateConfigId,
                IS_AI_UNIT_CONFIG_AVAILABLE
            )
        }
    }

    override fun toString(): String {
        return "AIUnitPluginState(id:$pluginId, downloadable:${downloadable()}, updatable:${updatable()}, stateValid:${isStateValid()})"
    }
}