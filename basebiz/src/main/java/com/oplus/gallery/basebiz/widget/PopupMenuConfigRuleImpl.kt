/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PopupMenuConfigRuleHelper.kt
 ** Description: 长按图集弹出菜单，左右对齐的帮助类.
 **
 **
 ** Version: 1.0
 ** Date: 2025/06/10
 ** Author: <EMAIL>
 ** TAG: PopupMenuConfigRuleHelper
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2025/06/10		1.0		PopupMenuConfigRuleHelper
 *********************************************************************************/
package com.oplus.gallery.basebiz.widget

import android.app.Activity
import android.content.Context
import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.RectF
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.poplist.PopupMenuConfigRule
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils

/**
 * 长按图集弹出菜单，左右对齐的帮助类.
 */
class PopupMenuConfigRuleImpl(val view: View, val context: Context) : PopupMenuConfigOffsetXRule {
    //自锚点与菜单间距区域
    private val outSets: Rect = Rect()

    //控制组件的左右对齐生效变量
    private var popupRuleEnable = false
    private var offsetX = 0
    override fun setOffsetX(offsetX: Int) {
        <EMAIL> = offsetX
    }

    private var windowWidth = 0

    /**
     * 自定义菜单显示区域的类型TYPE_BARRIER
     */
    override fun getType(): Int = PopupMenuConfigRule.TYPE_BARRIER

    /**
     * 判断view是否在是否在屏幕左侧或者中间 (这种情况菜单都往右偏)
     */
    private fun isViewInLeftHalfOrCenterScreen(): Boolean {
        windowWidth = (context as? Activity)?.let { ScreenUtils.getWindowSize(it) }?.x ?: run {
            val width = context.resources.displayMetrics.widthPixels
            GLog.d(TAG, LogFlag.DL) { "isViewInLeftHalfOrCenterScreen : widthPixels : $width" }
            width
        }
        var localWindowWidth = windowWidth
        if (offsetX != 0) {
            localWindowWidth -= offsetX
        }
        // 获取视图的中心点
        val viewOnScreenX = getViewOriginalXInWindow(view, false)
        val viewCenterX = viewOnScreenX + view.width / 2
        // 判断视图中心点是否在屏幕的左半部分
        val isLeft = (viewCenterX <= localWindowWidth / 2)
        GLog.d(
            TAG,
            LogFlag.DL
        ) { "isViewInLeftHalfOrCenterScreen return: $isLeft screenWidth:$windowWidth localWindowWidth:$localWindowWidth" }
        return isLeft
    }

    /**
     * 锚点view在屏幕左侧或者中间，菜单都往右偏(设置左侧屏障)
     * 锚点view在屏幕右侧，菜单都往左偏(设置右侧屏障)
     */
    override fun getBarrierDirection(): Int =
        if (isViewInLeftHalfOrCenterScreen()) PopupMenuConfigRule.BARRIER_FROM_LEFT else PopupMenuConfigRule.BARRIER_FROM_RIGHT

    /**
     * 获取菜单显示区域
     */
    override fun getDisplayFrame(): Rect {
        val displayFrame = Rect()
        val viewOnScreenX = getViewOriginalXInWindow(view, true)
        val params = view.layoutParams as ViewGroup.MarginLayoutParams
        GLog.d(
            TAG,
            LogFlag.DL
        ) { "getDisplayFrame():: ${view.measuredWidth},screenX: $viewOnScreenX,params.getMarginStart: ${params.marginStart}" }
        val margin = ResourceUtils.dp2px(context, HORIZONTAL_MARGIN)
        if (isViewInLeftHalfOrCenterScreen()) {
            //菜单将显示于锚点View左对齐，displayFrame.right为菜单左侧的X坐标
            displayFrame.right = adjustInLeftHalfViewXForRtlLayout(viewOnScreenX, margin)
        } else {
            //菜单将显示于锚点View右对齐，displayFrame.left为菜单右侧的X坐标
            displayFrame.left = adjustInRightHalfViewXForRtlLayout(viewOnScreenX, margin)
        }
        return displayFrame
    }

    /**
     * view在左边
     * 有侧边栏的情况下，菜单右侧显示应该在侧边栏的左侧，不能覆盖在侧边栏上面
     */
    private fun adjustInLeftHalfViewXForRtlLayout(viewOnScreenX: Int, margin: Int): Int {
        var viewOnScreenXCopy = viewOnScreenX
        if (ResourceUtils.isRTL(context).not()) {
            if ((viewOnScreenXCopy < (offsetX + margin)) || (viewOnScreenXCopy < margin)) {
                viewOnScreenXCopy = offsetX + margin
            }
        } else {
            if ((viewOnScreenXCopy < margin)) {
                viewOnScreenXCopy = margin
            }
        }
        return viewOnScreenXCopy
    }

    /**
     * view在右边
     * 在显示区域超过了屏幕宽度，需要间隔屏幕8dp处显示
     */
    private fun adjustInRightHalfViewXForRtlLayout(viewOnScreenX: Int, margin: Int): Int {
        var viewOnScreenXCopy = viewOnScreenX
        viewOnScreenXCopy += view.measuredWidth
        if (ResourceUtils.isRTL(context).not()) {
            if (viewOnScreenXCopy > (windowWidth - margin)) {
                viewOnScreenXCopy = windowWidth - margin
            }
        } else {
            if (viewOnScreenXCopy > (windowWidth - offsetX - margin)) {
                viewOnScreenXCopy = windowWidth - offsetX - margin
            }
        }
        return viewOnScreenXCopy
    }

    /**
     * 获取视图在窗口中的原始X坐标（考虑变换矩阵）
     */
    private fun getViewOriginalXInWindow(view: View, isLocationInWindow: Boolean): Int {
        val location = IntArray(2)
        view.getLocationInWindow(location)
        // 长按down时，originView有缩小，需要使用matrix计算原始区域
        val originalRect = RectF(0f, 0f, view.width.toFloat(), view.height.toFloat())
        //获取 view 的变换矩阵
        val matrix = view.matrix
        //检查 view 是否发生了变换
        if (!matrix.isIdentity) {
            val inverseMatrix = Matrix()
            //计算当前变换矩阵的 逆矩阵
            matrix.invert(inverseMatrix)
            //对 originalRect 应用逆矩阵变换，使其反映 变换前的原始坐标
            inverseMatrix.mapRect(originalRect)
        }
        if ((offsetX != 0) && isLocationInWindow.not() && ResourceUtils.isRTL(context).not()) {
            location[0] -= offsetX
        }
        return (location[0] + originalRect.left).toInt()
    }

    /**
     * 获取自锚点与菜单间距区域，默认为8dp
     */
    override fun getOutsets(): Rect = outSets

    override fun setPopupMenuRuleEnabled(enable: Boolean) {
        popupRuleEnable = enable
    }

    /**
     * 控制组件的左右对齐生效变量，返回true才生效
     */
    override fun getPopupMenuRuleEnabled(): Boolean = popupRuleEnable

    companion object {
        private const val TAG = "PopupMenuConfigRuleHelper"
        //视觉要求，菜单距离屏幕的边距16dp，不分大小屏.
        private const val HORIZONTAL_MARGIN = 16f
    }
}