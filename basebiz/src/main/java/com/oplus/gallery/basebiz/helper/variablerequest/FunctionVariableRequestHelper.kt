/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VariableRequestHelper
 ** Description: 云参数请求能力类
 **
 ** Version: 1.0
 ** Date: 2022/05/05
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/05/05  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.basebiz.helper.variablerequest

import android.content.Context
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.permission.NetSendPrivacyPermissionRule
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig
import com.oplus.gallery.business_lib.http.cloudparameter.base.GroupResponseBean
import com.oplus.gallery.business_lib.http.cloudparameter.base.GroupResponseBean.Companion.SP_KEY_PRE
import com.oplus.gallery.foundation.networkaccess.callback.AppResultCallback
import com.oplus.gallery.foundation.networkaccess.other.Nullable
import com.oplus.gallery.foundation.networkaccess.param.JsonRequestParam
import com.oplus.gallery.foundation.security.AppsAESUtil.DEFAULT_CIPHER_ALGORITHM
import com.oplus.gallery.foundation.security.AppsAESUtil.encryptAsString
import com.oplus.gallery.foundation.security.AppsSecurityUtil.generateKey
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_FUNCTION_VARIABLE
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.download.IDownloadAbility
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusNetServiceManager
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusResponseData
import com.oplus.gallery.framework.abilities.download.networkrequest.request.FunctionVariableRequest
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.launch

/**
 * 功能变量请求工具类
 */
class FunctionVariableRequestHelper {

    private val requestParam = GroupRequestParam()

    fun request(context: Context, groupName: String, listener: OnCloudParamRequestListener? = null) {
        if (!NetworkPermissionManager.isUseOpenNetwork) {
            listener?.onFinish()
            GLog.d(TAG, "request, group=$groupName, isUseOpenNetwork false return")
            return
        }
        requestParam.add(groupName, null)
        AppScope.launch {
            GLog.d(TAG, "request, group=$groupName")
            context.getAppAbility<IDownloadAbility>()?.use { downloadAbility ->
                val rsa: String = OplusNetServiceManager.getInstance().serverDataPubKey
                val appsSecurityData = generateKey(rsa) ?: run {
                    GLog.d(TAG, "request, appsSecurityData is null return")
                    listener?.onFinish()
                    return@use
                }

                val requestData = HashMap<String, String>()
                val source = JsonUtil.toJson(requestParam.varRequestList)
                if (GProperty.DEBUG_NETWORK) {
                    GLog.d(TAG, "request, source=$source")
                }
                val encryptData = encryptAsString(source, appsSecurityData.aesKey, DEFAULT_CIPHER_ALGORITHM)
                encryptData?.let { requestData[DATA] = it }
                val requestParam = JsonRequestParam(requestData)

                val isOnePlusExport = ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not()
                        && PROPERTY_IS_OP_BRAND
                downloadAbility.enqueue(
                    context,
                    FunctionVariableRequest(
                        context, NetSendPrivacyPermissionRule(), appsSecurityData, isOnePlusExport, requestParam,
                        getRequestKey(groupName)
                    ),
                    createCallback(groupName, listener)
                )
            } ?: run {
                GLog.d(TAG, "request, group=$groupName, no downloadAbility return")
                listener?.onFinish()
            }
        }
    }

    private fun createCallback(
        groupName: String,
        listener: OnCloudParamRequestListener? = null
    ): AppResultCallback<OplusResponseData<GroupResponseBean>?> {
        return object : AppResultCallback<OplusResponseData<GroupResponseBean>?> {
            override fun onFailed(code: Int, @Nullable msg: String?) {
                GLog.e(TAG, "onFailed, group=$groupName onFailed $code, $msg")
                listener?.onFinish()
            }

            override fun onSuccess(data: OplusResponseData<GroupResponseBean>?) {
                data?.data?.let { responseBean ->
                    // 处理 group
                    val group = responseBean.getGroup(groupName) ?: run {
                        GLog.w(TAG, "onSuccess, group=$groupName, group is null, return")
                        return@let
                    }
                    if (group.status == GroupResponseBean.STATE_FORBIDDEN) {
                        GLog.w(TAG, "onSuccess, group=$groupName forbidden")
                        CloudParamConfig.clearConfig(groupName)
                        return@let
                    }

                    // 处理 module
                    val modules = group.modules ?: run {
                        GLog.w(TAG, "onSuccess, group=$groupName, module is null, return")
                        return@let
                    }
                    val sp = SPUtils.getSp(ContextGetter.context, CLOUD_PARAM_STORAGE).edit()
                    for (module in modules) {
                        val moduleName = module.moduleName
                        if (module.status == GroupResponseBean.STATE_FORBIDDEN) {
                            GLog.w(TAG, "onSuccess, module=$moduleName forbidden")
                            CloudParamConfig.clearConfig(groupName, module.moduleName)
                            continue
                        }

                        // 处理 variable
                        val variables = module.vars
                        if (variables.isNullOrEmpty()) {
                            GLog.w(TAG, "onSuccess, group=$groupName, module=$moduleName, variables is null, continue")
                            continue
                        }
                        for (variable in variables) {
                            val varDesc = variable.desc
                            if (variable.status == GroupResponseBean.STATE_FORBIDDEN) {
                                GLog.w(TAG, "onSuccess, variable=$varDesc forbidden")
                                CloudParamConfig.clearConfig(groupName, moduleName, varDesc)
                                continue
                            }
                            val key = CloudParamConfig.getSPKey(groupName, moduleName, varDesc)
                            sp.putString(key, variable.value)
                            if (DEBUG_FUNCTION_VARIABLE) {
                                GLog.d(TAG, "onSuccess, group=$groupName, module=$moduleName, variable=$varDesc, values=${variable.value}")
                            }
                        }
                    }
                    sp.apply()
                }
                listener?.onFinish()
            }
        }
    }

    private fun getRequestKey(group: String): String {
        return SP_KEY_PRE + group
    }

    interface OnCloudParamRequestListener {
        fun onFinish()
    }

    companion object {
        private const val TAG = "FunctionVariableRequestHelper"
        private const val CLOUD_PARAM_STORAGE = "pref_cloud_param_storage"
        private const val DATA = "data"
    }
}