/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - GalleryNotificationActions.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/4/11        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.basebiz.notification

import com.oplus.gallery.basebiz.R
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.authorizing.R as AuthorizingR
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizingFrameworkR

/**
 * 无网络toast
 */
class NoNetworkToastAction : NotificationAction.ToastAction(R.string.common_network_disconnected)

/**
 * 隐私权限弹窗
 */
class PrivacyDialogAction(
    val contentResId: Int,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    dialogStyleResId = null,
    titleResId = null,
    positiveButtonTextResId = AuthorizingR.string.authorizing_option_agree_and_use,
    negativeButtonTextResId = AuthorizingR.string.authorizing_option_disagree,
    confirmCallback = confirmCallback
)

/**
 * 网络授权弹窗
 */
class NetworkAuthorizationDialogAction(
    titleResId: Int = AuthorizingFrameworkR.string.authorizing_request_network_title,
    messageResId: Int? = null,
    positiveButtonTextResId: Int = AuthorizingFrameworkR.string.authorizing_option_allow,
    negativeButtonTextResId: Int = AuthorizingFrameworkR.string.authorizing_option_refuse,
    confirmCallback: IConfirmCallback,
    titleString: String? = null,
    messageString: String? = null
) : NotificationAction.ConfirmDialogAction(
    titleResId = titleResId,
    messageResId = messageResId,
    positiveButtonTextResId = positiveButtonTextResId,
    negativeButtonTextResId = negativeButtonTextResId,
    confirmCallback = confirmCallback
) {
    init {
        super.titleString = titleString
        super.messageString = messageString
    }
}

/**
 * 用户须知弹窗
 */
class UserAgreementDialogAction(
    val contentSpan: ContentSpan,
    val linkSpan: ContentSpan?,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    dialogStyleResId = null,
    titleResId = null,
    positiveButtonTextResId = AuthorizingR.string.authorizing_option_agree_and_use,
    negativeButtonTextResId = AuthorizingR.string.authorizing_option_disagree,
    confirmCallback = confirmCallback
) {
    sealed class ContentSpan(
        val containerStringResId: Int
    ) {
        /**
         * [containerStringResId]不包含占位符
         */
        class DefaultTextSpan(
            stringResId: Int
        ) : ContentSpan(stringResId)

        /**
         * [containerStringResId]必须是包含1个占位符的字符串，如："详情查看 %1$s"
         */
        class ContentSpanWithPrivacy(
            containerStringResId: Int
        ) : ContentSpan(containerStringResId)

        /**
         * [containerStringResId]必须是包含2个占位符的字符串，如："详情查看 %1$s、%2$s"
         */
        class ContentSpanWithUserAgreementAndPrivacy(
            containerStringResId: Int
        ) : ContentSpan(containerStringResId)
    }
}

/**
 * 人脸扫描授权弹窗
 * @param confirmCallback 确认回调
 */
class FaceScanPermissionDialogAction(
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = null,
    positiveButtonTextResId = null,
    negativeButtonTextResId = null,
    confirmCallback = confirmCallback
)

/**
 * 移动数据流量弹窗
 */
class MobileWarnDialogAction(
    messageResId: Int,
    positiveButtonTextResId: Int,
    negativeButtonTextResId: Int,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = null,
    messageResId = messageResId,
    positiveButtonTextResId = positiveButtonTextResId,
    negativeButtonTextResId = negativeButtonTextResId,
    confirmCallback = confirmCallback
)

/**
 * 存储空间不足弹窗
 */
class StorageDialogAction(
    titleResId: Int = R.string.base_no_storage_space,
    messageString: String,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = titleResId,
    positiveButtonTextResId = R.string.base_copy_clean_up,
    negativeButtonTextResId = R.string.common_cancel,
    confirmCallback = confirmCallback
) {
    init { super.messageString = messageString }
}

/**
 * realme存储空间不足弹窗(不重试版本)
 */
class StorageSpaceNotEnoughDialogAction(
    messageString: String,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = R.string.photopage_group_photo_install_fail_tips_title,
    positiveButtonTextResId = R.string.base_copy_clean_up,
    negativeButtonTextResId = R.string.common_cancel,
    confirmCallback = confirmCallback
) {
    init { super.messageString = messageString }
}