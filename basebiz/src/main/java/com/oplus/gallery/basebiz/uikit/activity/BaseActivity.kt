/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseActivity.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/08/29
 ** Author: hucanh<PERSON>@Apps.Gallery
 ** TAG: OPLUS_ARCH_FRAGMENTSTACK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/08/29		1.0		init
 *********************************************************************************/

package com.oplus.gallery.basebiz.uikit.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.pm.ActivityInfo.COLOR_MODE_HDR
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import androidx.activity.OnBackPressedCallback
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.theme.COUIThemeOverlay
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.addon.view.FlexibleWindowManagerWrapper
import com.oplus.gallery.addon.view.WindowManagerWrapper
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.app.IGalleryController
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentStack
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentState
import com.oplus.gallery.basebiz.uikit.fragment.base.IFragmentStack
import com.oplus.gallery.basebiz.uikit.fragment.base.StackEntry
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cache.diskcache.DiskCacheManagerService
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.foundation.ui.BaseUiActivity
import com.oplus.gallery.foundation.ui.systembar.ActivitySystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.ImmersiveActivitySystemBarStyle
import com.oplus.gallery.foundation.uikit.activityresult.IntentSenderNotifier
import com.oplus.gallery.foundation.uikit.app.ZoomWindowManager
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder.AppUiConfig
import com.oplus.gallery.foundation.uikit.responsiveui.IAppUiObserver
import com.oplus.gallery.foundation.uikit.responsiveui.IAppUiResponder
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.cpuLevel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.display.DensityUtils
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.isOutOfBounds
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.cast.ICastAbility
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.search.ISearchAbility
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.scheduler.DefaultScheduler
import com.oplus.gallery.standard_lib.scheduler.DefaultScheduler.THUMBNAIL_JOB_LIMIT
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.FileDescriptor
import java.io.PrintWriter
import java.lang.ref.WeakReference

open class BaseActivity : BaseUiActivity(), IFragmentStack, IGalleryController,
    IAppUiResponder, IAppUiObserver, ZoomWindowManager.ZoomWindowState {
    protected val clazzSimpleName: String by lazy { this.javaClass.simpleName }

    var fragmentState: FragmentState? = null
    protected open var needBackToBackground = false

    // 是否当日首次前台冷启动，目前只计MainActivity
    var isFirstColdStartToday = false

    private lateinit var fragmentStack: IFragmentStack

    protected var isFromCamera = false
    protected var isDisplayOnLock = false
    private var lastUiMode = Configuration.UI_MODE_NIGHT_NO
    private var lastWindowLayoutParams: WindowManager.LayoutParams? = null

    val session by lazy { DefaultScheduler.scheduler.createPrioritySession(name = clazzSimpleName) }

    /**
     * 解码专用session
     */
    val decodeSession by lazy { DefaultScheduler.decodeScheduler.createPrioritySession(THUMBNAIL_JOB_LIMIT, "$clazzSimpleName#Decode") }
    protected val handler: Handler by lazy {
        Handler(Looper.getMainLooper())
    }
    protected var activityRef: WeakReference<Activity?>? = null
        private set

    private lateinit var appUiResponder: AppUiResponder
    var intentSenderNotifier: IntentSenderNotifier? = null
        private set
    private val touchEventInterceptorListeners = HashSet<OnTouchEventInterceptor>()
    private var isFirstWindowFocusChangedHasFocus = true

    private val userInteractionListeners: ArrayList<OnUserInteractionListener> = ArrayList()
    private val hdrModeChangeListeners: ArrayList<OnWindowHdrModeChangeListener> = ArrayList()

    open val pageType: PageType = PageType.DEFAULT

    open val needSaveFragmentInstanceState: Boolean = true

    open fun isSupportTextSizeChange(context: Context): Boolean = true

    override fun getSystemBarStyle(): ActivitySystemBarStyle = ImmersiveActivitySystemBarStyle(this)

    private var backCallback: OnBackPressedCallback? = null
    // fragment是否选择态映射
    val fragmentSelectStateMap: MutableMap<Fragment, Boolean> = mutableMapOf()
    // 是否需要响应预测性返回
    open var isNeedPredictiveBackWithActivity = false

    private val objectTag by lazy {
        "$clazzSimpleName@${hashCode()}"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        appUiResponder = AppUiResponder(this)
        intentSenderNotifier = IntentSenderNotifier(this)
        initFragmentStack(savedInstanceState)
        super.onCreate(savedInstanceState)
        GTrace.traceBegin("$TAG.onCreate")
        GLog.d(TAG, DL) { "onCreate: <$objectTag>" }
        val curUIMode = resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        if ((savedInstanceState != null) && (savedInstanceState.getBoolean(KEY_SHOULD_FINISH_ACTIVITY, false))) {
            // 判断是否切换了暗色模式，如果切换了，则关闭activity
            val lastSavedUIMode = savedInstanceState.getInt(KEY_SAVE_INSTANCE_CURRENT_UI_MODE, Configuration.UI_MODE_NIGHT_NO)
            if (lastSavedUIMode != curUIMode) {
                finish()
                return
            }
        }
        lastUiMode = curUIMode
        activityRef = WeakReference(this)
        registerAppUiObserver(this, this)
        registerZoomWindowObserver()
        registerFlexibleActivityOnOutsideTouchListener()
        /**
         * 设置屏蔽系统拖拽(drag)松手动画,只有大图是需要系统动画的，其他页面都不需要，所以大图特殊处理，其他全部设置
         */
        if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)) {
            WindowManagerWrapper.setDropSysAnimation(this.window, true)
        }

        backCallback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                onBackPressed()
            }
        }.also {
            onBackPressedDispatcher.addCallback(this, it)
        }

        GTrace.traceEnd()
    }

    private fun initFragmentStack(savedInstanceState: Bundle?) {
        GTrace.traceBegin("$TAG.initFragmentStack")
        fragmentStack = FragmentStack(supportFragmentManager)
        fragmentState = FragmentState(supportFragmentManager, getStateMap())

        savedInstanceState?.getParcelableArrayList<StackEntry>(KEY_FRAGMENT_STACK)?.apply {
            // 恢复fragment栈
            val stack = (fragmentStack as FragmentStack).stack
            stack.clear()
            stack.addAll(this)
            // 恢复每个fragment中的fragmentState
            for (index in 0 until this.size) {
                // 从源码的逻辑上看，supportFragmentManager在super.oncreate恢复，这里应该是拿不到的。但不确定之前的版本是不是可以
                this[index].getFragment(supportFragmentManager)?.also {
                    if (it.fragmentState == null) {
                        it.fragmentState = FragmentState(supportFragmentManager, getStateMap())
                    }
                    it.fragmentState!!.setParent(
                        this[index].tag,
                        if (index < this.size - 1) Lifecycle.State.STARTED else Lifecycle.State.RESUMED,
                        this.getOrNull(index - 1)?.tag,
                        Lifecycle.State.STARTED
                    )
                }
            }
        }
        GTrace.traceEnd()
    }

    private fun registerZoomWindowObserver() {
        GTrace.trace("$TAG.registerZoomWindowObserver") {
            zoomWindowManager.registerZoomWindowObserver(this)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putInt(KEY_SAVE_INSTANCE_CURRENT_UI_MODE, lastUiMode)
        outState.putBoolean(KEY_SHOULD_FINISH_ACTIVITY, shouldFinishWhenUIModeChanged())
        super.onSaveInstanceState(outState)
        // 不需要保存fragment信息，就手动清空一下系统保留的fragmentState信息
        if (needSaveFragmentInstanceState.not()) {
            val fragmentStateBundle = outState.getBundle(BUNDLABLE_SAVED_STATEREGISTRY_KEY)
            fragmentStateBundle?.remove(ANDROID_SUPPORT_FRAGMENT_KEY)
        } else {
            outState.putParcelableArrayList(KEY_FRAGMENT_STACK, ArrayList((fragmentStack as FragmentStack).stack))
        }
        GLog.d(TAG, DL, "onSaveInstanceState")
    }

    fun setContentView() {
        FrameLayout(this).apply {
            id = R.id.base_fragment_container
            setContentView(this, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
        }
    }

    fun addContentView(@IdRes layoutId: Int = com.oplus.gallery.foundation.ui.R.id.base_fragment_container2) {
        FrameLayout(this).apply {
            id = layoutId
            addContentView(this, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
        }
    }

    override fun setContentView(layoutResID: Int) {
        super.setContentView(layoutResID)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
    }

    override fun setContentView(view: View?) {
        super.setContentView(view)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
    }

    override fun setContentView(view: View?, params: ViewGroup.LayoutParams?) {
        super.setContentView(view, params)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
    }

    override fun onResume() {
        super.onResume()
        logCurrentTemperature(lifecycleScope, applicationContext, "onResume", clazzSimpleName)
        GTrace.trace("$TAG.onResume") {
            GLog.d(TAG, DL) { "onResume: <$objectTag>" }
            DensityUtils.updateConfiguration(resources, isSupportDisplaySizeChange(this), isSupportTextSizeChange(this))
            session.bringToFront()
            decodeSession.bringToFront()
            setCurrentLanguage(lifecycleScope)
            setAutoBacklight(lifecycleScope, application, false)
        }
    }

    protected fun checkWideGamutColor() {
        GTrace.trace("$TAG.checkWideGamutColor") {
            application.getAppAbility<IColorManagementAbility>()?.use {
                // Marked: 2022/4/18 加锁的范围有点大，而通过 getAppAbility 获取的能力皆为代理对象，不是固定的一个实例，不能用作锁
                synchronized(IColorManagementAbility::class.java) {
                    // 给window设置colorMode时需要立即执行
                    val needDeleteCache = it.tryAdjustColorMode(window)
                    if (needDeleteCache == true) {
                        AppScope.launch {
                            DiskCacheManagerService.deleteCacheFiles(ContextGetter.context)
                            GLog.w(TAG, DL) { "[checkWideGamutColor] already delete cache files!" }
                        }
                    }
                }
            } ?: let {
                GLog.w(TAG, DL) { "[checkWideGamutColor] color management not supported" }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        GTrace.traceBegin("$TAG.onPause")
        GLog.d(TAG, DL) { "onPause: <$objectTag>" }
        setAutoBacklight(lifecycleScope, application, true)
        session.bringToBack()
        decodeSession.bringToBack()
        GTrace.traceEnd()
        logCurrentTemperature(lifecycleScope, applicationContext, "onPause", clazzSimpleName)
    }

    override fun onStart() {
        GTrace.traceBegin("BaseActivity.onStart")
        super.onStart()
        checkWideGamutColor()
        session.submit(RunInOnStartJob())
        GTrace.traceEnd()
    }

    override fun onDestroy() {
        super.onDestroy()
        GLog.d(TAG, DL) { "onDestroy: <$objectTag>" }
        session.terminal()
        decodeSession.terminal()
        handler.removeCallbacksAndMessages(null)
        zoomWindowManager.unregisterZoomWindowObserver(this)
        appUiResponder.finish()
        intentSenderNotifier?.release()
        intentSenderNotifier = null
    }

    override fun dump(prefix: String, fd: FileDescriptor?, writer: PrintWriter, args: Array<out String>?) {
        DefaultScheduler.scheduler.dump(writer)
        super.dump(prefix, fd, writer, args)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        setTheme(com.oplus.gallery.foundation.ui.R.style.CommonDefaultTheme)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        /*系统分发时会new 2个Configuration对象，一个给resources，一个分发给onConfigurationChanged方法；
        相册更新了resources的Configuration对象, 因此传入AppUiResponder框架的Configuration对象也要同步更新，
        而不能使用系统原始分发的Configuration对象。 若传递系统原始分发的对象，将会导致配置获取的不一致。*/
        DensityUtils.updateConfiguration(resources, isSupportDisplaySizeChange(this), isSupportTextSizeChange(this))
        appUiResponder.onActivityConfigurationChanged(Configuration(resources.configuration))
    }

    override fun onWindowAttributesChanged(params: WindowManager.LayoutParams) {
        super.onWindowAttributesChanged(params)

        val isHdrModeLast = lastWindowLayoutParams?.colorMode == COLOR_MODE_HDR
        val isHdrMode = params.colorMode == COLOR_MODE_HDR
        if (isHdrMode != isHdrModeLast) {
            hdrModeChangeListeners.forEach { it.onWindowHdrModeChanged(isHdrMode) }
        }
        lastWindowLayoutParams = params
    }

    /**
     * 子类有自己业务需求，可能不希望替换 BaseContext。如果有需要，同时规避无效 super 回调，请重写 [onAttachBaseContext] 函数代替
     */
    final override fun attachBaseContext(context: Context) {
        super.attachBaseContext(onAttachBaseContext(context))
    }

    protected open fun onAttachBaseContext(context: Context): Context {
        return DensityUtils.createConfigurationContext(
            context,
            isSupportDisplaySizeChange(context),
            false,
            isSupportTextSizeChange(context)
        )
    }

    /**
     * Activity overrides this interface to configure whether it needs to change with the system display size.
     *
     * @return true -> changing with the system display size.
     */
    protected open fun isSupportDisplaySizeChange(context: Context): Boolean = false

    override val zoomWindowManager: ZoomWindowManager by lazy {
        ZoomWindowManager.getInstance(
            packageName,
            this.javaClass.name
        )
    }

    override fun isFloatingWindowMode(): Boolean = zoomWindowManager.isFloatingWindowMode

    /**
     * 设置是否拦截返回
     * 用于是否响应 预测性返回特性
     * 设置为false时注意检查是否有需要拦截的事务，比如多选、自定义弹窗等
     *
     * @param enabled true: 拦截，即需要在[backCallback]里处理自定义返回逻辑
     *                false: 不拦截，即交予系统处理成带预测性返回动画的返回
     */
    fun setBackInterception(enabled: Boolean) {
        if (ApiLevelUtil.isApiLevelAtLeastB()) {
            // 仅在安卓16上响应预测性返回特性
            if (enabled || (fragmentStackSize() <= FINISH_FRAGMENT_STACK_SIZE)) {
                backCallback?.isEnabled = enabled
                GLog.d(TAG, DL) { "Predictive back setBackInterception: $enabled" }
            }
        }
    }

    @Deprecated("Deprecated", ReplaceWith("onBackPressedWithPredictive()"))
    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        onBackPressedWithPredictive()
    }

    /**
     * onBackPressed方法适配预测性返回
     */
    open fun onBackPressedWithPredictive() {
        val stackSize = fragmentStackSize()
        if (stackSize > FINISH_FRAGMENT_STACK_SIZE) {
            if (backPressed()) {
                pop()
            }

            /**
             * 如果当前Activity的FragmentStack已经没有Fragment了，那直接finish掉当前activity，以暂时规避 #7885227
             */
            if (fragmentStackSize() == 0) {
                AppScope.launch(Dispatchers.IO) {
                    val dumpInfo = getDumpInfo()
                    GLog.e(TAG, DL) { "dump fragmentStack:$dumpInfo" }
                }
                GLog.e(TAG, DL) { "Error, fragmentSize is 0,finish the activity." }
                finish()
                return
            }
        } else if ((stackSize < FINISH_FRAGMENT_STACK_SIZE) || backPressed()) {
            /**
             * 使用onBackPressed
             * 场景：
             * 在图集列表页，进入设置后，在手势返回到图集列表页，再手势退出到桌面，再次进入相册，未回到图集列表页，到了照片页。
             *
             * 因为在桌面进入MainActivity的时候，mLaunchSourceType为Home，但是在应用类去startActivity()后，
             * mLaunchSourceType会改变成application,安卓源码判断如果mLaunchSourceType是application后，
             * 调用onBackPressed（）就会优先finish掉activity,所以在应用内在进入了其他的activity后，手势返回到桌面
             * ，MainActivity会被finish
             * 这里使用moveTaskToBack（），是退到后台，不会被finish
             * marked by：预测性返回，此处调用moveTaskToBack仅在设置拦截返回后生效
             */
            if (needBackToBackground) {
                moveTaskToBack(false)
            } else {
                backCallback?.isEnabled = false
                onBackPressedDispatcher.onBackPressed()
            }
        }
    }

    /**
     * 是否有任意Fragment处于多选状态
     */
    fun hasAnyFragmentInSelected(): Boolean {
        fragmentSelectStateMap.forEach {
            if (it.value) {
                return true
            }
        }
        return false
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean = keyDown(keyCode) || super.onKeyDown(keyCode, event)

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean = keyUp(keyCode) || super.onKeyUp(keyCode, event)

    override fun onKeyLongPress(keyCode: Int, event: KeyEvent?): Boolean = keyLongPress(keyCode) || super.onKeyLongPress(keyCode, event)

    override fun onKeyMultiple(keyCode: Int, repeatCount: Int, event: KeyEvent?): Boolean = keyMultiple(keyCode, repeatCount)

    fun startFragment(resId: Int, routerName: String, data: Bundle? = null, addToBackStack: Boolean = false) {
        startFragment(resId, routerName, data, this, null, addToBackStack, intArrayOf(0, 0, 0, 0))
    }

    protected fun startFragment(
        resId: Int,
        routerName: String,
        data: Bundle? = null,
        fragmentStack: IFragmentStack = this,
        requestCode: Int? = null,
        addToBackStack: Boolean = false,
        anim: IntArray = intArrayOf(0, 0, 0, 0),
    ): Fragment? {
        return supportFragmentManager.start(
            resId = resId,
            postCard = PostCard(routerName),
            data = data,
            fragmentStack = fragmentStack,
            requestCode = requestCode,
            addToBackStack = addToBackStack,
            anim = anim
        )
    }

    /**
     * 在切换暗色模式或亮色模式时，是否关闭activity（存在有可能有的Activity既是1级页面又是N级页面
     * 因为不同情况，故使用覆盖方法，外加参数的方式，来控制是否关闭页面。1级时不关闭，N级时关闭。
     * 1级基本是外部调用，不会带上intent的extra，所以拿到extra可知是内部多层级或者其他特殊场景调用。此时以参数为准。
     */
    open fun shouldFinishWhenUIModeChanged(): Boolean {
        return intent?.getBooleanExtra(IntentConstant.ViewGalleryConstant.KEY_SHOULD_FINISH_ACTIVITY, false) == true
    }

    override fun getStateMap() = fragmentStack.getStateMap()

    override fun getStackClone() = fragmentStack.getStackClone()

    override fun findFragment(tag: String): StackEntry? = fragmentStack.findFragment(tag)

    override fun peek(index: Int): StackEntry? = fragmentStack.peek(index)

    override fun push(stackEntry: StackEntry) = fragmentStack.push(stackEntry)

    override fun pop(): Boolean = fragmentStack.pop()

    override fun pop(tag: String, flag: Int): Boolean = fragmentStack.pop(tag, flag)

    override fun pop(tag: String, flag: Int, onlyPop: Boolean, immediately: Boolean): Boolean =
        fragmentStack.pop(tag, flag, onlyPop, immediately)

    override fun backPressed(): Boolean = fragmentStack.backPressed()

    override fun keyDown(keyCode: Int): Boolean = fragmentStack.keyDown(keyCode)

    override fun keyUp(keyCode: Int): Boolean = fragmentStack.keyUp(keyCode)

    override fun keyLongPress(keyCode: Int): Boolean = fragmentStack.keyLongPress(keyCode)

    override fun keyMultiple(keyCode: Int, repeatCount: Int): Boolean = fragmentStack.keyMultiple(keyCode, repeatCount)

    override fun fragmentStackSize(): Int = fragmentStack.fragmentStackSize()

    override fun isTopStackFragment(tag: String): Boolean = fragmentStack.isTopStackFragment(tag)
    override fun getDumpInfo(): String = fragmentStack.getDumpInfo()

    protected fun isPositiveOrder(): Boolean = ApiDmManager.getSettingDM().isPositiveOrder()

    private class RunInOnStartJob : Job<Any?> {
        override fun call(jc: JobContext): Any? {
            ApiDmManager.getScanDM().interruptScanForReasonScreenOn()
            (ContextGetter.context as GalleryApplication).getAppAbility(ISearchAbility::class.java)?.use {
                it.notifyDataDirty(SearchSuggestionProviderUtil.BACKGROUND_COLLECT_URI)
            }
            return null
        }
    }

    override fun onAppUiStateChanged(uiConfig: AppUiConfig) {
        val isFloatingWindowChanged = uiConfig.isInFloatingWindow.isChanged()
        val isMultiWindowChanged = uiConfig.isInMultiWindow.isChanged()
        val isWidthChanged = uiConfig.windowWidth.isChanged()
        val isCurrentWindowFloating = uiConfig.isInFloatingWindow.current
        val isCurrentWindowMulti = uiConfig.isInMultiWindow.current
        GLog.d(TAG, DL) {
            "onAppUiStateChanged isFloatingWindowChanged=$isFloatingWindowChanged isMultiWindowChanged=$isMultiWindowChanged " +
                    "isWidthChanged=$isWidthChanged isCurrentWindowFloating=$isCurrentWindowFloating isCurrentWindowMulti=$isCurrentWindowMulti"
        }
        if (isFloatingWindowChanged || isWidthChanged) {
            ScreenUtils.update(this, isCurrentWindowFloating)
        }
        stopCastIfNeed(
            lifecycleScope,
            applicationContext,
            isFloatingWindowChanged,
            isMultiWindowChanged,
            isCurrentWindowFloating,
            isCurrentWindowMulti
        )
    }

    override fun onFloatingWindowStateChanged(isFloatingWindowMode: Boolean) = Unit

    final override fun registerAppUiObserver(owner: LifecycleOwner, observer: IAppUiObserver) {
        GTrace.trace("$TAG.registerAppUiObserver") {
            appUiResponder.registerAppUiObserver(owner, observer)
        }
    }

    final override fun unregisterAppUiObserver(observer: IAppUiObserver) {
        appUiResponder.unregisterAppUiObserver(observer)
    }

    final override fun getCurrentAppUiConfig(): AppUiConfig = appUiResponder.getCurrentAppUiConfig()

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            if (isFirstWindowFocusChangedHasFocus.not()) {
                /**
                 * updateFloatingWindowModeOnWindowFocusChanged内部的isFloatingWindowModeInner耗时，
                 * 但是又必须在主线程,onCreate会调用一次isFloatingWindowModeInner,第一次启动的时候又会回调该函数
                 * 因此第一次启动的时候重复调用了，可以去掉一次调用，优化性能。
                 */
                zoomWindowManager.updateFloatingWindowModeOnWindowFocusChanged()
                GLog.d(TAG, DL) { "onWindowFocusChanged updateFloatingWindowModeOnWindowFocusChanged" }
            }
            isFirstWindowFocusChangedHasFocus = false
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            userInteractionListeners.forEach {
                it.onUserInteraction(InteractionType.TOUCH)
            }
            touchEventInterceptorListeners.forEach {
                if (it.shouldInterceptTouchEvent(ev)) {
                    GLog.d(TAG, DL) { "dispatchTouchEvent: the intercept listener is ${it.javaClass.simpleName}" }
                    return true
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        userInteractionListeners.forEach {
            it.onUserInteraction(InteractionType.KEY)
        }
        return super.dispatchKeyEvent(event)
    }

    /**
     * 添加用户交互事件监听，目前只监听了触屏和按键事件，可根据需求添加更多事件
     */
    fun addUserInteractionListener(listener: OnUserInteractionListener) {
        if (!userInteractionListeners.contains(listener)) {
            userInteractionListeners.add(listener)
        }
    }

    /**
     * 移除用户交互事件监听
     */
    fun removeUserInteractionListener(listener: OnUserInteractionListener) {
        userInteractionListeners.remove(listener)
    }

    /**
     * 添加window HDR模式变化监听
     */
    fun addHdrModeChangeListener(listener: OnWindowHdrModeChangeListener) {
        if (!hdrModeChangeListeners.contains(listener)) {
            hdrModeChangeListeners.add(listener)
        }
    }

    /**
     * 移除window HDR模式变化监听
     */
    fun removeHdrModeChangeListener(listener: OnWindowHdrModeChangeListener) {
        hdrModeChangeListeners.remove(listener)
    }

    /**
     * 添加拦截屏幕事件监听
     */
    fun addInterceptEventListener(eventListener: OnTouchEventInterceptor) {
        if (!touchEventInterceptorListeners.contains(eventListener)) {
            touchEventInterceptorListeners.add(eventListener)
        }
    }

    /**
     * 移除屏幕事件监听
     */
    fun removeInterceptEventListener(eventListener: OnTouchEventInterceptor) {
        touchEventInterceptorListeners.remove(eventListener)
    }

    @SuppressLint("ClickableViewAccessibility")
    /**
     * 为顶层视图注册监听，当点击区域超出当前activity，就用 isFlexibleActivity() 将所有以跟手面板形式显示的activity finish掉
     */
    private fun registerFlexibleActivityOnOutsideTouchListener() {
        val isTablet = ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_TABLET)
        val isFold = ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_FOLD)
        // 当前 activity不是浮窗Activity，无需处理
        if (FlexibleWindowManagerWrapper.isFlexibleActivity(isTablet, isFold, resources.configuration).not()) {
            GLog.w(TAG, DL) { "<registerFlexibleActivityOnOutsideTouchListener> is not FlexibleActivity" }
            return
        }
        window.decorView.setOnTouchListener { decorView, event ->
            if ((event?.action == MotionEvent.ACTION_DOWN) && decorView.isOutOfBounds(event)) {
                GLog.d(TAG, DL) { "<$clazzSimpleName> decorView ACTION_DOWN and OutOfBounds" }
                for (itemActivity in ActivityLifecycle.getActivityList()) {
                    itemActivity.get()?.let {
                        if (FlexibleWindowManagerWrapper.isFlexibleActivity(isTablet, isFold, it.resources.configuration)) {
                            GLog.d(TAG, DL) { "<${it.localClassName} close when touch out side of FlexibleActivity" }
                            it.finish()
                        }
                    }
                }
            }
            false
        }
    }

    companion object {
        const val TAG = "BaseActivity"

        /**
         *  activity销毁重建时，存储上一次的uiMode是暗色还是亮色，方便判断后续是否切换了亮暗色模式
         */
        private const val KEY_SAVE_INSTANCE_CURRENT_UI_MODE = "key_save_instance_current_ui_mode"

        /**
         * activity销毁重建时，存储之前shouldFinishWhenUIModeChanged方法的值，避免重建后，数据丢失，结果不对。
         */
        private const val KEY_SHOULD_FINISH_ACTIVITY = "key_should_finish_activity"
        private const val KEY_FRAGMENT_STACK = "fragmentStack.key"
        const val FINISH_FRAGMENT_STACK_SIZE = 1

        /**
         * 在Activity 或Fragment 生命周期方法(如 onSaveInstanceState())SavedStateRegistryController 会调用 performSave() 方法，将
         * 需要保存的状态数据传递给SavedStateRegistry，最终保存到Bundle 中，以下面的string作key
         *
         * 注意：这个是从源码是copy过来的，不要随便更改。需要对齐[androidx.savedstate.SavedStateRegistry#SAVED_COMPONENTS_KEY]
         */
        private const val BUNDLABLE_SAVED_STATEREGISTRY_KEY = "androidx.lifecycle.BundlableSavedStateRegistry.key"

        /**
         * 这个key是存储fragment状态的key，在Fragment 的onSaveInstanceState() 方法中会调用，在oncreate中用户恢复fragment状态
         *
         * 注意：这个是从源码是copy过来的，不要随便更改。需要对齐[androidx.fragment.app.FragmentActivity.init] 中的key值
         */
        private const val ANDROID_SUPPORT_FRAGMENT_KEY = "android:support:fragments"

        private fun stopCastIfNeed(
            lifecycleScope: LifecycleCoroutineScope,
            applicationContext: Context,
            isFloatingWindowChanged: Boolean,
            isMultiWindowChanged: Boolean,
            isCurrentWindowFloating: Boolean,
            isCurrentWindowMulti: Boolean
        ) {
            lifecycleScope.launch(Dispatchers.CPU) {
                (applicationContext as? GalleryApplication)?.getAppAbility<ICastAbility>()?.use { castAbility ->
                    // 如果浮窗没有变化，分配模式也没有变化，则不需要对投屏做操作. 否则如果正在投屏中，进入到浮窗模式或者分屏模式时，退出投屏
                    if (isFloatingWindowChanged.not() && isMultiWindowChanged.not()) return@use
                    val isNotSupportCast = isCurrentWindowFloating || isCurrentWindowMulti
                    if (isNotSupportCast && (castAbility.castChannel?.isCasting() == true)) {
                        // 分屏和浮窗模式，主动退出内容投屏
                        castAbility.castChannel?.stopCast(fromUser = false, changeToMirrorIfNecessary = true)
                    }
                }
            }
        }

        private fun setAutoBacklight(lifecycleScope: LifecycleCoroutineScope, application: Application, isEnable: Boolean) {
            // 内部是io逻辑，所以用IO更合适
            lifecycleScope.launch(Dispatchers.IO) {
                GTrace.trace("$TAG.setAutoBacklight.$isEnable") {
                    application.getAppAbility<IHardwareAbility>()?.use { it.power?.isAutoBacklightEnabled = isEnable }
                }
            }
        }

        /**
         * 为了方便定位一些性能相关的问题
         * 打印当前相册的温度、电量、时间
         */
        private fun logCurrentTemperature(
            lifecycleScope: LifecycleCoroutineScope,
            appContext: Context,
            tag: String,
            clazzSimpleName: String,
        ) {
            lifecycleScope.launch(Dispatchers.CPU) {
                val temperature = TemperatureUtil.getCurrentTemperature()
                val batteryLevel = BatteryStatusUtil.getBatteryLevel(appContext)
                val currentTime = TimeUtils.getFormatDateTime()
                GLog.w(TAG, DL) {
                    "$tag: <$clazzSimpleName> temperature:$temperature, battery:$batteryLevel,cpu=$cpuLevel,time:$currentTime"
                }
            }
        }

        private fun setCurrentLanguage(lifecycleScope: LifecycleCoroutineScope) {
            lifecycleScope.launch(Dispatchers.CPU) {
                ResourceUtils.setCurrentLanguage()
            }
        }
    }

    enum class PageType {
        /**
         * 默认页面
         */
        DEFAULT,
        /**
         * 主页面
         */
        MAIN
    }

    /**
     * 页面与用户交互的通知
     */
    fun interface OnUserInteractionListener {
        /**
         * 页面执行用户操作时，执行此回调
         *
         * @param type 用户操作类型
         */
        fun onUserInteraction(type: InteractionType)
    }

    /**
     * window HDR模式变化监听
     */
    fun interface OnWindowHdrModeChangeListener {
        /**
         * window HDR模式变化监听
         *
         * @param isHdrMode 是否为HDR模式
         */
        fun onWindowHdrModeChanged(isHdrMode: Boolean)
    }

    enum class InteractionType {
        /**
         * 触屏交互
         */
        TOUCH,

        /**
         * 按键交互
         */
        KEY
    }
}

/**
 * 获取全屏的Fragment的容器id
 * 优先选择full_fragment_container(侧边栏需求引入),找不到则使用base_fragment_container
 * @return 返回view的ResId作为Fragment的容器id
 */
fun Activity?.findFullScreenContainerId(): Int {
    return if (this?.findViewById<View>(com.oplus.gallery.foundation.ui.R.id.full_fragment_container) != null) {
        com.oplus.gallery.foundation.ui.R.id.full_fragment_container
    } else {
        R.id.base_fragment_container
    }
}

/**
 * 获取地图TravelFragment的全屏布局容器id
 */
fun Activity?.findMapContainerId(): Int {
    return if (this?.findViewById<View>(com.oplus.gallery.foundation.ui.R.id.map_fragment_container) != null) {
        com.oplus.gallery.foundation.ui.R.id.map_fragment_container
    } else {
        R.id.base_fragment_container
    }
}