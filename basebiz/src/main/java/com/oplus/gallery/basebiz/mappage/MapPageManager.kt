/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MapPageManager.kt
 * Description: Map business management
 * Version: 1.0
 * Date: 2024/5/6
 * Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>      2025/5/6     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.basebiz.mappage

import android.content.Context
import android.graphics.Bitmap
import android.view.ViewGroup
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.BusinessLibHelper.isPositiveOrder
import com.oplus.gallery.business_lib.model.data.base.Path.SEGMENT_ALL
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.data.location.set.MapLocationAlbum
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.isNullOrEmpty
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.map.IMapAbility
import com.oplus.gallery.framework.abilities.map.LatLng
import com.oplus.gallery.framework.abilities.map.MapConstants
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class MapPageManager : IMapPageManager {

    private var mapAbility: IMapAbility? = null

    /**
     * 记录当前的mapWrapper的Id值
     */
    private var mapWrapperId: String = ""


    private val isDomestic: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN, false)
    }

    override fun initMapSdk(context: Context) {
        initAbilityIfNeed(context)
        mapAbility?.initMapSdk(context)
    }

    override fun isMapSdkInited(context: Context): Boolean {
        initAbilityIfNeed(context)
        return mapAbility?.isMapSdkInited() ?: false
    }

    private fun initAbilityIfNeed(context: Context) {
        if (mapAbility == null) {
            mapAbility = context.getAppAbility<IMapAbility>()
        }
    }

    override fun initDetailOuter(context: Context, mapViewContainer: ViewGroup?, imageId: String?, listener: IMapAbility.CaptureMapRegionListener?) {
        if (mapWrapperId.isNotEmpty()) {
            //initDetailOuter
            GLog.w(TAG, LogFlag.DL, "initDetailOuter this $this last wrapperId $mapWrapperId, need onDestroy last map")
            mapAbility?.onDestroy(mapWrapperId)
            mapWrapperId = TextUtil.EMPTY_STRING
        }
        val mapLocationAlbum: MapLocationAlbum =
            DataManager.getMediaObject(SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(SEGMENT_ALL)) as MapLocationAlbum
        mapLocationAlbum.setOrder(mapLocationAlbum.getDefaultOrder(isPositiveOrder()))
        val mediaPath = mapLocationAlbum.path.toString()
        GLog.d(TAG, LogFlag.DL, "initDetailOuter: this $this mediaPath:$mediaPath imageId:$imageId")
        AppScope.launch(Dispatchers.IO) {
            imageId?.let {
                val mediaItemList = LocalMediaDataHelper.getSingleItemForPath(it)
                runOnUiThread {
                    if (mediaItemList.isNullOrEmpty()) {
                        return@runOnUiThread
                    }
                    val latLong = DoubleArray(2)
                    val image = (mediaItemList[0] as LocalMediaItem).apply {
                        getLatLong(latLong)
                    }
                    val locationFromImage = LatLng(latLong[0], latLong[1])
                    //这里为了图钉居中，将地图中心点位置的纬度增加少许数值，保持地图图钉中央在外部地图处于中部位置
                    val adjustLocation = getOffsetPosition(locationFromImage)
                    val mapConfig = IMapAbility.InitMapConfig(IMapAbility.EntranceType.IMAGE_DETAIL_OUTER, mapLocationAlbum.path.toString())
                    mapConfig.apply {
                        // 大图详情页这里图钉不在地图内以addMarker的方式添加，采用在container中添加ImageView的方式进行展示，避免native内存泄漏问题
                        showMarker = false
                        initLocation = adjustLocation
                        hintImage = image
                        this.singleItemPath = imageId
                        defaultZoom = getDefaultZoomForMap()
                        supportGesture = false
                        useTextureView = true
                    }
                    mapViewContainer?.let {
                        mapWrapperId = mapAbility?.initMapView(mapViewContainer, null, mapConfig) ?: ""
                        GLog.d(TAG, LogFlag.DL, "initDetailOuter current wrapperId $mapWrapperId")
                    }
                    val captureCallbackProxy = object : IMapAbility.CaptureMapRegionListener {
                        override fun onCaptureMapRegion(location: LatLng, bitmap: Bitmap?) {
                            //这里的经纬度替换为初始化的经纬度（由于上面的这个location是地图转换过之后的经纬度，非gps坐标经纬度，所以这里做了一个代理，转换一下）
                            listener?.onCaptureMapRegion(locationFromImage, bitmap)
                        }
                    }
                    //这里截图的逻辑保留，防止后面UI核查要使用截图方案，底部的判定checkNeedCaptureForDetailOuter不会走截图方法。
                    mapWrapperId.let {
                        if (checkNeedCaptureForDetailOuter()) {
                            mapAbility?.justTrigerMapCapture(it, captureCallbackProxy)
                        } else {
                            GLog.d(TAG, "initDetailOuter no need to triger capture")
                        }
                    }
                }
            }
        }
    }

    fun getDefaultZoomForMap(): Float {
        //内外销的缩放级别不同，便于图钉位置在地图中央
        return if (!isDomestic) {
            MapConstants.ZOOM_LEVEL_500M
        } else {
            MapConstants.ZOOM_LEVEL_1KM
        }
    }

    private fun getOffsetPosition(inputLocation: LatLng): LatLng {
        val latitudOffset = getOffsetLatitudeForCenterPoint()
        return LatLng(inputLocation.latitude + latitudOffset, inputLocation.longitude)
    }

    private fun getOffsetLatitudeForCenterPoint(): Float {
        val isDomestic = ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN, false)
        return if (!isDomestic) {
            LOCATION_LATITUDE_OFFSET_EXPORT
        } else {
            LOCATION_LATITUDE_OFFSET_DOMESTIC
        }
    }


    private fun checkNeedCaptureForDetailOuter(): Boolean {
        val snapShotSupported = mapAbility?.isSnapshotSupported() ?: false
        val isDomestic = ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN, false)
        val result = if (!snapShotSupported) {
            false
        } else {
            //外销因为非surfaceView实现，在做动效时不存在问题，可以不用走截图方案
            isDomestic
        }
        GLog.d(TAG, LogFlag.DL, "checkNeedCaptureForDetailOuter result $result, isDomestic $isDomestic, snapShotSupported $snapShotSupported")
        return false
    }


    override fun onPause() {
        GLog.d(TAG, LogFlag.DL, "onPause $this mapWrapperId $mapWrapperId")
        mapAbility?.onPause(mapWrapperId)
    }

    override fun onResume() {
        GLog.d(TAG, LogFlag.DL, "onResume $this mapWrapperId $mapWrapperId")
        mapAbility?.onResume(mapWrapperId)
    }

    override fun onDestroy() {
        GLog.d(TAG, LogFlag.DL, "onDestroy $this mapWrapperId $mapWrapperId, mapWrapperId cleared")
        mapAbility?.onDestroy(mapWrapperId)
        mapWrapperId = TextUtil.EMPTY_STRING
    }

    override fun locateInCurrentPosition() {
        mapAbility?.locateInCurrentPosition(mapWrapperId, isCaptureMap = true)
    }

    override fun initExplorerPageMapView(mapViewContainer: ViewGroup?, listener: IMapAbility.CaptureMapRegionListener?) {
        if (mapWrapperId.isNotEmpty()) {
            //initDetailOuter
            GLog.w(TAG, LogFlag.DL, "initExplorerPageMapView last wrapperId $mapWrapperId, need onDestroy last map")
            mapAbility?.onDestroy(mapWrapperId)
            mapWrapperId = ""
        }
        mapViewContainer?.let {
            val mapConfig = IMapAbility.InitMapConfig(IMapAbility.EntranceType.EXPLORER_MAP_ENTRANCE, null)
            mapConfig.showMarker = false
            mapConfig.supportGesture = false
            mapAbility?.setCaptureMapRegionListener(listener)
            mapWrapperId = mapAbility?.initMapView(mapViewContainer, null, mapConfig) ?: ""
        }
    }

    private companion object {
        const val TAG = "MapPageManager"
        const val LOCATION_LATITUDE_OFFSET_EXPORT = 0.006f
        const val LOCATION_LATITUDE_OFFSET_DOMESTIC = 0.010f
    }
}
