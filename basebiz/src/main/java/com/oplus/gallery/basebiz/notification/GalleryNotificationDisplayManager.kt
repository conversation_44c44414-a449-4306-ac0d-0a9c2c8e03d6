/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - GalleryNotificationDisplayManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/4/30        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.basebiz.notification

import android.app.Activity
import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.authorizing.ui.NormalStatementBuilder
import com.oplus.gallery.foundation.authorizing.ui.text.style.SpanParams
import com.oplus.gallery.foundation.authorizing.ui.text.style.StringSpanParams
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.ui.notification.NotificationDisplayManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.foundation.authorizing.R as AuthorizingR
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizingFrameworkR

/**
 * 相册的通知显示管理器，扩展实现相册的定制通知交互UI，如隐私权限弹窗和用户须知弹窗
 */
open class GalleryNotificationDisplayManager(activity: Activity) : NotificationDisplayManager(activity) {

    override fun onUpdate(activity: Activity, notificationAction: NotificationAction) {
        when (notificationAction) {
            is PrivacyDialogAction -> showPrivacyDialog(activity, notificationAction)
            is UserAgreementDialogAction -> showUserAgreementDialog(activity, notificationAction)
            is FaceScanPermissionDialogAction -> showFaceScanPermissionDialog(activity, notificationAction)
            else -> super.onUpdate(activity, notificationAction)
        }
    }

    private fun showFaceScanPermissionDialog(activity: Activity, action: FaceScanPermissionDialogAction) {
        val contentSpanParams = activity.getString(AuthorizingFrameworkR.string.authorizing_person_album_statement)
        val linkSpanParams = StringSpanParams(
            activity.getString(
                AuthorizingFrameworkR.string.authorizing_check_details_msg_1,
                activity.getString(AuthorizingFrameworkR.string.authorizing_privacy_policy)
            ),
            intArrayOf(AuthorizingFrameworkR.string.authorizing_privacy_policy),
            arrayOf(
                StringSpanParams.OnSpanClickListener {
                    Starter.ActivityStarter(activity, Bundle(), PostCard(RouterName.SETTING_PRIVACY_POLICY)).start()
                }
            )
        ).getText(activity)
        NormalStatementBuilder(activity)
            .setDescribeMessage(contentSpanParams)
            .setDetailsLink(linkSpanParams)
            .setTitle(AuthorizingR.string.authorizing_agree_use_of_privacy_title)
            .setPositiveButton(AuthorizingR.string.authorizing_option_agree_and_use) { dialog, _ ->
                dialog.dismiss()
                action.confirmCallback.onPositiveButtonClicked(activity)
            }
            .setNegativeButton(AuthorizingR.string.authorizing_option_disagree) { dialog, _ ->
                dialog.dismiss()
                action.confirmCallback.onNegativeButtonClicked()
            }
            .show()
    }

    private fun showPrivacyDialog(activity: Activity, action: PrivacyDialogAction) {
        val contentSpanParams = activity.getString(action.contentResId)
        val linkSpanParams = SpanParams(
            AuthorizingFrameworkR.string.authorizing_check_details_msg_2,
            intArrayOf(
                AuthorizingFrameworkR.string.authorizing_privacy_policy,
                AuthorizingFrameworkR.string.authorizing_privacy_sharing_list
            ),
            arrayOf(
                SpanParams.OnSpanClickListener {
                    goToPrivacyPolicyPage(activity)
                },
                SpanParams.OnSpanClickListener {
                    Starter.ActivityStarter(activity, Bundle(), PostCard(RouterName.SETTING_THIRD_PARTY_INFO_SHARING)).start()
                }
            )
        ).getText(activity)
        NormalStatementBuilder(activity)
            .setDescribeMessage(contentSpanParams)
            .setDetailsLink(linkSpanParams)
            .setTitle(AuthorizingR.string.authorizing_agree_use_of_privacy_title)
            .setPositiveButton(AuthorizingR.string.authorizing_option_agree_and_use) { dialog, _ ->
                dialog.dismiss()
                action.confirmCallback.onPositiveButtonClicked(activity)
            }
            .setNegativeButton(AuthorizingR.string.authorizing_option_disagree) { dialog, _ ->
                dialog.dismiss()
                action.confirmCallback.onNegativeButtonClicked()
            }
            .show()
    }

    private fun showUserAgreementDialog(activity: Activity, action: UserAgreementDialogAction) {
        val contentSpanParam = createSpanParam(activity, action.contentSpan).getText(activity)
        val linkSpanParam = action.linkSpan?.let {
            createSpanParam(activity, it).getText(activity)
        }
        NormalStatementBuilder(activity)
            .setDescribeMessage(contentSpanParam)
            .setDetailsLink(linkSpanParam)
            .setTitle(AuthorizingR.string.authorizing_agree_use_of_privacy_title)
            .setPositiveButton(AuthorizingR.string.authorizing_option_agree_and_use) { dialog, _ ->
                dialog.dismiss()
                action.confirmCallback.onPositiveButtonClicked(activity)
            }
            .setNegativeButton(AuthorizingR.string.authorizing_option_disagree) { dialog, _ ->
                dialog.dismiss()
                action.confirmCallback.onNegativeButtonClicked()
            }
            .show()
    }

    private fun createSpanParam(activity: Activity, textSpan: UserAgreementDialogAction.ContentSpan): SpanParams {
        return when (textSpan) {
            is UserAgreementDialogAction.ContentSpan.DefaultTextSpan -> SpanParams(textSpan.containerStringResId)
            is UserAgreementDialogAction.ContentSpan.ContentSpanWithPrivacy -> {
                SpanParams(
                    textSpan.containerStringResId,
                    intArrayOf(AuthorizingFrameworkR.string.authorizing_privacy_policy),
                    arrayOf(
                        SpanParams.OnSpanClickListener {
                            goToPrivacyPolicyPage(activity)
                        }
                    ),
                    isFromLast = true
                )
            }
            is UserAgreementDialogAction.ContentSpan.ContentSpanWithUserAgreementAndPrivacy -> {
                SpanParams(
                    textSpan.containerStringResId,
                    intArrayOf(
                        R.string.base_user_agreement,
                        AuthorizingFrameworkR.string.authorizing_privacy_policy,
                    ),
                    arrayOf(
                        SpanParams.OnSpanClickListener {
                            Starter.ActivityStarter(activity, Bundle(), PostCard(RouterName.SETTING_USER_AGREEMENT)).start()
                        },
                        SpanParams.OnSpanClickListener {
                            goToPrivacyPolicyPage(activity)
                        }
                    ),
                    isFromLast = true
                )
            }
        }
    }

    private fun goToPrivacyPolicyPage(context: Context) {
        val regionPrivacySetting = ApiDmManager.getSettingDM().getRegionPrivacySetting()
        regionPrivacySetting?.let {
            if (it.isPrivacyAgreementRequired()) {
                Starter.ActivityStarter(context, Bundle(), PostCard(RouterName.SETTING_PRIVACY_POLICY)).start()
            } else {
                ApiDmManager.getSettingDM().getPrivacyNoticeIntent()?.let { intent ->
                    runCatching {
                        context.startActivity(intent)
                    }.onFailure { ex ->
                        GLog.e(TAG, "goToPrivacyPolicyPage failed", ex)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "G.N.D.M"
    }
}