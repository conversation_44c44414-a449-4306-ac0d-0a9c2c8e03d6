/*******************************************************************************
 * Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PresentTypeGuideView.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2025/04/08
 * Author: <PERSON>H<PERSON>@Apps.Gallery
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON>@Apps.Gallery		2025/04/08		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.basebiz.widget

import android.content.Context
import android.content.res.Resources
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.widget.Checkable
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import java.text.SimpleDateFormat
import java.util.Locale
import com.support.appcompat.R as CouiR

/**
 * 视图设置引导弹窗子View（含图片、文字、RadioButton）
 */
class PresentTypeGuideView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : LinearLayout(context, attrs, defStyleAttr, defStyleRes), Checkable {
    private var srcId: Int
    private var isTypeDate: Boolean
    private val overInfoTitle: COUITextView
    private val overInfoSubTitleToday: COUITextView
    private val overInfoSubTitleYesterday: COUITextView
    private var overInfoSubTitleOtherDay: COUITextView
    private val ivPresent: ImageView
    private val tvPresent: TextView
    private val rbPresent: RadioButton
    private val dateFormat: SimpleDateFormat by lazy {
        SimpleDateFormat(TimeUtils.FORMAT_YYYY_MM_DD, Locale.getDefault())
    }
    private val titleDate: String by lazy {
        context.getString(R.string.main_fragment_title_timeline)
    }
    private val titleImmersive: String by lazy {
        TimeUtils.getYearMonthDayDate(dateFormat.parse(DATE_3_18))
    }
    private val locationInfo: String by lazy {
        context.getString(R.string.base_common_location_nanshan_shenzhen)
    }
    private val titleToday: String by lazy {
        context.getString(R.string.common_today) + SEPARATOR_POINT + locationInfo
    }
    private val titleYesterday: String by lazy {
        context.getString(R.string.common_yesterday) + SEPARATOR_POINT + locationInfo
    }
    private val titleOtherDay: String by lazy {
        TimeUtils.getMonthDayDate(dateFormat.parse(DATE_3_16)) + SEPARATOR_POINT + locationInfo
    }

    /**
     * 当前对应的资源目录
     *
     * - 对应 sw700dp、sw480dp、default
     */
    private val currentResValues: Int
        get() {
            val smallestScreenWidthDp: Int = resources.configuration.smallestScreenWidthDp
            return when {
                smallestScreenWidthDp >= SW_700DP -> VALUES_SW_700DP
                smallestScreenWidthDp >= SW_480DP -> VALUES_SW_480DP
                else -> VALUES_SW_DEFAULT
            }
        }

    /**
     * 是否需要其他日期的标题
     *
     * - 中大屏不需要
     */
    private val isNeedSubTitleOtherDay: Boolean
        get() {
            return currentResValues == VALUES_SW_DEFAULT
        }

    private var checked: Boolean = false
    var checkChangeListener: OnCheckedChangeListener? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.base_present_type_guide_child_view, this, true)
        val attr = context.obtainStyledAttributes(attrs, R.styleable.PresentTypeGuideView, defStyleAttr, defStyleRes)
        val text = attr.getText(R.styleable.PresentTypeGuideView_android_text)
        srcId = attr.getResourceId(R.styleable.PresentTypeGuideView_android_src, Resources.ID_NULL)
        isTypeDate = attr.getInt(R.styleable.PresentTypeGuideView_presentType, 0) == 0
        attr.recycle()
        orientation = VERTICAL
        isClickable = true

        overInfoTitle = requireViewById(R.id.tv_title)
        overInfoSubTitleToday = requireViewById(R.id.tv_sub_title_toady)
        overInfoSubTitleYesterday = requireViewById(R.id.tv_sub_title_yesterday)
        overInfoSubTitleOtherDay = requireViewById(R.id.tv_sub_title_other_day)
        ivPresent = requireViewById<ImageView?>(R.id.iv_present).apply {
            this.setImageResource(srcId)
        }
        tvPresent = requireViewById<TextView?>(R.id.tv_present).apply {
            this.text = text
        }
        rbPresent = requireViewById(R.id.rb_present)

        initOverInfo()
        updateLayout()
    }

    override fun setChecked(checked: Boolean) {
        if (this.checked == checked) return
        this.checked = checked
        rbPresent.isChecked = checked
        updateOverInfoTextColor(isChecked)
        checkChangeListener?.onCheckedChanged(this, checked)
    }

    private fun updateOverInfoTextColor(isChecked: Boolean) {
        val textColor = context.getColor(if (isChecked) CouiR.color.coui_color_primary_blue else CouiR.color.coui_color_label_primary)
        overInfoTitle.setTextColor(textColor)
        overInfoSubTitleToday.setTextColor(textColor)
        overInfoSubTitleYesterday.setTextColor(textColor)
        overInfoSubTitleOtherDay.setTextColor(textColor)
    }

    override fun isChecked(): Boolean = checked

    override fun toggle() {
        isChecked = !checked
    }

    override fun performClick(): Boolean {
        isChecked = true
        return super.performClick()
    }

    fun setImageResource(@DrawableRes resId: Int) {
        ivPresent.setImageResource(resId)
    }

    fun setText(@StringRes resId: Int) {
        tvPresent.setText(resId)
    }

    /**
     * 引导图文本信息
     */
    private fun initOverInfo() {
        overInfoTitle.apply {
            text = if (isTypeDate) titleDate else titleImmersive
        }
        overInfoSubTitleToday.apply {
            text = if (isTypeDate) titleToday else TextUtil.EMPTY_STRING
        }
        overInfoSubTitleYesterday.apply {
            text = if (isTypeDate) titleYesterday else TextUtil.EMPTY_STRING
        }
        overInfoSubTitleOtherDay.apply {
            text = if (isTypeDate) titleOtherDay else TextUtil.EMPTY_STRING
        }
    }

    /**
     * 更新布局：布局参数、TextSize、Visible等 - 适配大中小屏等变化场景
     *
     * - 1. 此View是弹窗ContentView内部自定义的View，直接在sw480dp、sw700dp目录中定义不同的资源值或者 requestLayout 均无效，
     *  目前只能参考COUI的方案，在 onConfigurationChanged 中主动刷新,即等于是需要自行处理不同sw的布局参数;
     * - 2. 交互设计，引导图及其文本信息，不支持RTL，在Layout文件中已配置；
     * - 3. 为避免适配浮窗/分屏等造成的耦合，交互变更为了悬浮窗/分屏不显示弹窗；
     * - 4. 引导图上的文字，不随系统字体大小缩放.
     */
    fun updateLayout() {
        when (currentResValues) {
            VALUES_SW_700DP -> updateLayoutSw700dp()

            VALUES_SW_480DP -> updateLayoutSw480dp()

            else -> updateLayoutDefault()
        }
    }

    private fun updateLayoutDefault() {
        val marginHorizontal = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_margin_horizontal)
        val subTitleMinHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_min_height)
        val subTitleMaxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_max_width)
        val subTitleTextSize = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_text_size).toFloat()
        val subTitlePaddingHorizontal = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_padding_horizontal)
        ivPresent.apply {
            maxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_img_placeholder_width)
            maxHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_img_placeholder_height)
        }
        overInfoTitle.apply {
            minHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_min_height)
            maxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_max_width)
            setTextSize(
                TypedValue.COMPLEX_UNIT_PX, resources.getDimensionPixelSize(
                    if (isTypeDate) {
                        R.dimen.base_present_type_guide_info_title_text_size_date
                    } else {
                        R.dimen.base_present_type_guide_info_title_text_size_immersive
                    }
                ).toFloat()
            )
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_margin_top)
            val paddingHorizontal = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_padding_horizonal)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = paddingHorizontal, right = 0)
        }

        overInfoSubTitleToday.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_today_margin_top)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
        }

        overInfoSubTitleYesterday.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_yesterday_margin_top)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
        }

        overInfoSubTitleOtherDay.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_other_day_margin_top)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
            isVisible = isTypeDate && isNeedSubTitleOtherDay
        }
    }

    private fun updateLayoutSw480dp() {
        val marginHorizontal = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_margin_horizontal_sw480)
        val subTitleMinHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_min_height_sw480)
        val subTitleMaxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_max_width_sw480)
        val subTitleTextSize = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_text_size_sw480).toFloat()
        val subTitlePaddingHorizontal =
            resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_padding_horizontal_sw480)
        ivPresent.apply {
            maxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_img_placeholder_width_sw480)
            maxHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_img_placeholder_height_sw480)
        }
        overInfoTitle.apply {
            minHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_min_height_sw480)
            maxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_max_width_sw480)
            setTextSize(
                TypedValue.COMPLEX_UNIT_PX, resources.getDimensionPixelSize(
                    if (isTypeDate) {
                        R.dimen.base_present_type_guide_info_title_text_size_date_sw480
                    } else {
                        R.dimen.base_present_type_guide_info_title_text_size_immersive_sw480
                    }
                ).toFloat()
            )
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_margin_top_sw480)
            val paddingHorizontal = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_padding_horizonal_sw480)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = paddingHorizontal, right = 0)
        }
        overInfoSubTitleToday.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_today_margin_top_sw480)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
        }

        overInfoSubTitleYesterday.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_yesterday_margin_top_sw480)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
        }

        overInfoSubTitleOtherDay.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_other_day_margin_top_sw480)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
            isVisible = isTypeDate && isNeedSubTitleOtherDay
        }
    }

    private fun updateLayoutSw700dp() {
        val marginHorizontal = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_margin_horizontal_sw700)
        val subTitleMinHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_min_height_sw700)
        val subTitleMaxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_max_width_sw700)
        val subTitleTextSize = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_text_size_sw700).toFloat()
        val subTitlePaddingHorizontal =
            resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_padding_horizontal_sw700)
        ivPresent.apply {
            maxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_img_placeholder_width_sw700)
            maxHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_img_placeholder_height_sw700)
        }
        overInfoTitle.apply {
            minHeight = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_min_height_sw700)
            maxWidth = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_max_width_sw700)
            setTextSize(
                TypedValue.COMPLEX_UNIT_PX, resources.getDimensionPixelSize(
                    if (isTypeDate) {
                        R.dimen.base_present_type_guide_info_title_text_size_date_sw700
                    } else {
                        R.dimen.base_present_type_guide_info_title_text_size_immersive_sw700
                    }
                ).toFloat()
            )

            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_margin_top_sw700)
            val paddingHorizontal = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_title_padding_horizonal_sw700)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = paddingHorizontal, right = 0)
        }
        overInfoSubTitleToday.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_today_margin_top_sw700)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
        }

        overInfoSubTitleYesterday.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_yesterday_margin_top_sw700)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
        }

        overInfoSubTitleOtherDay.apply {
            minHeight = subTitleMinHeight
            maxWidth = subTitleMaxWidth
            setTextSize(TypedValue.COMPLEX_UNIT_PX, subTitleTextSize)
            val marginTop = resources.getDimensionPixelSize(R.dimen.base_present_type_guide_info_sub_title_other_day_margin_top_sw700)
            updateMargin(left = marginHorizontal, top = marginTop, right = 0, bottom = 0)
            updatePadding(left = subTitlePaddingHorizontal, right = subTitlePaddingHorizontal)
            isVisible = isTypeDate && isNeedSubTitleOtherDay
        }
    }

    interface OnCheckedChangeListener {
        fun onCheckedChanged(view: PresentTypeGuideView, isChecked: Boolean)
    }

    companion object {
        // 对应sw480dp、sw700dp 和 default
        private const val VALUES_SW_DEFAULT = 0
        private const val VALUES_SW_480DP = 1
        private const val VALUES_SW_700DP = 2
        private const val SW_480DP = 480
        private const val SW_700DP = 700

        // 用于拼接引导图静态文本的相关字符串
        private const val SEPARATOR_POINT = " · "
        private const val DATE_3_16 = "2025-03-16"
        private const val DATE_3_18 = "2025-03-18"
    }
}

/**
 * 管理多个PresentTypeGuideView的RadioButton
 */
class PresentCheckGroup(
    private val items: Array<PresentTypeGuideView?>,
    private val onSelectChangedListener: OnSelectChangedListener?
) : PresentTypeGuideView.OnCheckedChangeListener {
    init {
        items.forEach { it?.checkChangeListener = this }
    }

    override fun onCheckedChanged(view: PresentTypeGuideView, isChecked: Boolean) {
        if (!isChecked) return
        items.forEachIndexed { index, item ->
            if (item === view) {
                onSelectChangedListener?.onSelected(index, view)
            } else {
                item?.isChecked = false
            }
        }
    }

    fun setSelected(index: Int) {
        items[index]?.isChecked = true
    }

    fun interface OnSelectChangedListener {
        fun onSelected(index: Int, view: PresentTypeGuideView)
    }
}