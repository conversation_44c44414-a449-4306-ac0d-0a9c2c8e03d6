/*********************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        : - NetSendPrivacyPermissionRule.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/10/25
 ** Author      : liuyining@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  liuyining@Apps.Gallery3D  2021/10/25  1.0        build this module
 ***********************************************************************/

package com.oplus.gallery.basebiz.permission

import com.oplus.gallery.foundation.networkaccess.INetSendRule
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter

class NetSendPrivacyPermissionRule : INetSendRule {
    override fun checkIfPermitted(): Boolean {
        // ability 为空，返回true，代表未配置授权能力，即默认通过.
        (ContextGetter.context as? GalleryApplication)?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            return it.isPrivacyAuthorized(AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD) ?: run {
                GLog.w(TAG, "checkIfPermitted, isPrivacyAuthorized == null, return false")
                false // 接口为空，调用错误，不允许使用
            }
        } ?: return true
    }

    companion object {
        const val TAG = "NetSendPrivacyPermissionRule"
    }
}