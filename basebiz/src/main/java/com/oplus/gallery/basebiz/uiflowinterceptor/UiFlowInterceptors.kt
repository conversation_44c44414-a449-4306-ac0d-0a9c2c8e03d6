/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - UiFlowInterceptors.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/4/12        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.basebiz.uiflowinterceptor

import android.app.Activity
import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.StorageTipsHelper
import com.oplus.gallery.basebiz.notification.FaceScanPermissionDialogAction
import com.oplus.gallery.basebiz.notification.MobileWarnDialogAction
import com.oplus.gallery.basebiz.notification.NetworkAuthorizationDialogAction
import com.oplus.gallery.basebiz.notification.NoNetworkToastAction
import com.oplus.gallery.basebiz.notification.PrivacyDialogAction
import com.oplus.gallery.basebiz.notification.StorageDialogAction
import com.oplus.gallery.basebiz.notification.StorageSpaceNotEnoughDialogAction
import com.oplus.gallery.basebiz.notification.UserAgreementDialogAction
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConfirmInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.ToastInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizingR

/**
 * 移动网络拦截器
 *
 * 如果是移动网络且进程内未同意过移动网络下载，则：
 *   显示确认弹窗（回调通知外部实现）：
 *     同意 -> 记录同意移动网络下载，下一步
 *     拒绝 -> 中断流程
 * 否则：
 *   下一步
 */
class MobileNetworkInterceptor(
    private val titleResId: Int,
    private val positiveButtonTextResId: Int,
    private val negativeButtonTextResId: Int,
    updateUI: (NotificationAction) -> Unit
) : ConfirmInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        val isMobileValidated = NetworkMonitor.isMobileValidated()
        val isAllowDownloadOnMobile = NetworkPermissionManager.isAllowDownloadOnMobile
        return isMobileValidated.not() || isAllowDownloadOnMobile
    }

    override fun onAgreed(activity: Activity) {
        NetworkPermissionManager.isAllowDownloadOnMobile = true
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction = NotificationAction.ConfirmDialogAction(
        titleResId = titleResId,
        positiveButtonTextResId = positiveButtonTextResId,
        negativeButtonTextResId = negativeButtonTextResId,
        confirmCallback = confirmCallback
    )
}

/**
 * 网络授权拦截器
 *
 * 如果未开启联网权限，则：
 *   显示确认弹窗（回调通知外部实现）：
 *     同意 -> 开启联网权限，下一步
 *     拒绝 -> 中断流程
 * 否则：
 *   下一步
 */
class NetworkAuthorizationInterceptor(
    private val messageResId: Int? = null,
    updateUI: (NotificationAction) -> Unit,
    private val messageString: String? = null
) : ConfirmInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        return NetworkPermissionManager.isUseOpenNetwork
    }

    override fun onAgreed(activity: Activity) {
        NetworkPermissionManager.isUseOpenNetwork = true
    }

    override fun onRefused() {
        NetworkPermissionManager.isUseOpenNetwork = false
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction = NetworkAuthorizationDialogAction(
        messageResId = messageResId,
        messageString = messageString,
        confirmCallback = confirmCallback
    )
}

/**
 * 网络拦截器
 *
 * 如果无网络，则：
 *   显示toast（回调通知外部实现）
 * 否则：
 *   下一步
 */
class NetworkInterceptor(updateUI: (NotificationAction) -> Unit) : ToastInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        return NetworkMonitor.isNetworkValidated()
    }

    override fun createToastAction(): NotificationAction.ToastAction = NoNetworkToastAction()
}

/**
 * 隐私权限拦截器
 *
 * 如果未同意隐私权限，则：
 *   显示确认弹窗（回调通知外部实现）：
 *     同意 -> 记录同意隐私权限，开启联网权限，下一步
 *     拒绝 -> 中断流程
 * 否则：
 *   下一步
 */
class PrivacyInterceptor(
    private val context: Context,
    private val privacyList: List<String>,
    private val contentResId: Int,
    updateUI: (NotificationAction) -> Unit
) : ConfirmInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        context.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            // 需列表内全部权限都授权了, 才可以进入. 否则不能进入
            privacyList.forEach { privacy ->
                val authorized = it.isPrivacyAuthorized(privacy) ?: false
                if (authorized.not()) {
                    return false
                }
            }
            return true
        }
        // 外销无授权能力，直接返回true
        return true
    }

    override fun onAgreed(activity: Activity) {
        context.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            // 用户同意了即表示同意了一组权限
            privacyList.forEach { privacy ->
                it.authorizePrivacy(privacy)
            }
        }
        NetworkPermissionManager.isUseOpenNetwork = true
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction = PrivacyDialogAction(contentResId, confirmCallback)
}

/**
 * 用户须知拦截器
 *
 * 如果未同意使用相应功能，则：
 *   显示确认弹窗（回调通知外部实现）：
 *     同意 -> 记录同意使用相应功能，下一步
 *     拒绝 -> 中断流程
 * 否则：
 *   下一步
 */
abstract class UserAgreementInterceptor(
    protected val context: Context,
    private val userAgreementParams: UserAgreementParams,
    updateUI: (NotificationAction) -> Unit
) : ConfirmInterceptor(updateUI) {

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction {
        val contentSpan: UserAgreementDialogAction.ContentSpan
        var linkSpan: UserAgreementDialogAction.ContentSpan? = null
        val showUserAgreement = ConfigAbilityWrapper.getBoolean(ConfigID.Common.Permission.IS_AGREE_USER_AGREEMENT, false).not()
        when (userAgreementParams) {
            is UserAgreementParams.OnlyContent -> {
                contentSpan = if (showUserAgreement) {
                    UserAgreementDialogAction.ContentSpan.ContentSpanWithPrivacy(
                        userAgreementParams.privacyContainerContentStringId
                    )
                } else {
                    UserAgreementDialogAction.ContentSpan.ContentSpanWithUserAgreementAndPrivacy(
                        userAgreementParams.agreementAndPrivacyContainerContentStringId
                    )
                }
            }

            is UserAgreementParams.ContentAndLink -> {
                contentSpan = UserAgreementDialogAction.ContentSpan.DefaultTextSpan(userAgreementParams.contentStringId)
                linkSpan = if (showUserAgreement) {
                    UserAgreementDialogAction.ContentSpan.ContentSpanWithUserAgreementAndPrivacy(
                        userAgreementParams.agreementAndPrivacyContainerLinkStringId
                    )
                } else {
                    UserAgreementDialogAction.ContentSpan.ContentSpanWithPrivacy(
                        userAgreementParams.privacyContainerLinkStringId
                    )
                }
            }

            is UserAgreementParams.ContentWithNoNeedAgreement -> {
                contentSpan = UserAgreementDialogAction.ContentSpan.ContentSpanWithUserAgreementAndPrivacy(
                    userAgreementParams.agreementAndPrivacyContainerLinkStringId
                )
            }
        }
        return UserAgreementDialogAction(contentSpan, linkSpan, confirmCallback)
    }

    sealed class UserAgreementParams {
        class OnlyContent(
            val privacyContainerContentStringId: Int,
            val agreementAndPrivacyContainerContentStringId: Int
        ) : UserAgreementParams()

        class ContentAndLink(
            val contentStringId: Int,
            val privacyContainerLinkStringId: Int,
            val agreementAndPrivacyContainerLinkStringId: Int
        ) : UserAgreementParams()

        class ContentWithNoNeedAgreement(
            val agreementAndPrivacyContainerLinkStringId: Int
        ) : UserAgreementParams()
    }
}

/**
 * 人脸扫描权限拦截器
 * @param context 上下文
 * @param updateUI 弹框拉起
 * @see [AppAbilityManager]
 */
class FaceScanPermissionInterceptor(
    private val context: Context,
    updateUI: (NotificationAction) -> Unit
) : ConfirmInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        // 这里如果没有IPrivacyAuthorizingAbility的话默认返回true，因为外销上没有配置这个ability（太隐蔽了吧）
        context.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            return it.isPrivacyAuthorized(AUTHORIZE_FACE_SCAN) ?: run {
                false
            }
        } ?: return true
    }

    override fun onAgreed(activity: Activity) {
        context.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            it.authorizePrivacy(AUTHORIZE_FACE_SCAN)
        }
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction = FaceScanPermissionDialogAction(confirmCallback)
}

/**
 * 移动数据流量消耗警告弹框拦截器
 *
 * wifi关闭，数据连接状态，弹起dialog，提示流量消耗，需要授权才能进行下一步操作
 */
class MobileWarnInterceptor(
    private val messageResId: Int,
    private val positiveButtonTextResId: Int,
    private val negativeButtonTextResId: Int,
    updateUI: (NotificationAction) -> Unit
) : ConfirmInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        val isMobileValidated = NetworkMonitor.isMobileValidated()
        val isAllowDownloadOnMobile = NetworkPermissionManager.isAllowDownloadOnMobile
        GLog.d(
            TAG,
            LogFlag.DL
        ) { "[onCheckCondition] isMobileValidated=$isMobileValidated, isAllowDownloadOnMobile=$isAllowDownloadOnMobile" }
        return (!isMobileValidated || isAllowDownloadOnMobile)
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction = MobileWarnDialogAction(
        messageResId = messageResId,
        positiveButtonTextResId = positiveButtonTextResId,
        negativeButtonTextResId = negativeButtonTextResId,
        confirmCallback
    )

    internal companion object {
        const val TAG = "MobileWarnInterceptor"
    }
}

/**
 * 存储空间不足拦截器
 *
 * 当存储空间不足支持完整的下载和安装插件时，会弹窗提示，并且结束链路
 */
class StorageDialogInterceptor(
    private val context: Context,
    private val size: Long,
    private val titleResId: Int = R.string.base_no_storage_space,
    private val msgResId: Int = R.string.base_install_again_after_free_up,
    updateUI: (NotificationAction) -> Unit
) : ConfirmInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        return StorageLimitHelper.hasEnoughStorageSpace(OplusEnvironment.StorageType.PHONE_STORAGE, size)
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction {
        val messageString = context.getString(msgResId, FilePathUtils.getUnitValue(context, size))
        return StorageDialogAction(titleResId, messageString, confirmCallback)
    }

    override fun onAgreed(activity: Activity) {
        StorageTipsHelper.startCleanUpActivity(activity)
    }

    override fun onAgreedProcessChain(chain: IInterceptor.IChain<Bundle, Unit>) {
        chain.fail(chain.param)
    }
}

/**
 * realme存储空间不足拦截器（无重试）
 *
 * 当存储空间不足支持完整的下载和安装插件并保存图片时，会弹窗提示，并且结束链路
 */
class StorageSpaceNotEnoughDialogInterceptor(
    private val context: Context,
    private val size: Long,
    updateUI: (NotificationAction) -> Unit
) : ConfirmInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        return StorageLimitHelper.hasEnoughStorageSpace(OplusEnvironment.StorageType.PHONE_STORAGE, size)
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction {
        val messageString = context.getString(
            R.string.photopage_group_photo_ai_graffiti_fail_tips_content,
            FilePathUtils.getUnitValue(context, size).toInt()
        )
        return StorageSpaceNotEnoughDialogAction(messageString, confirmCallback)
    }

    override fun onAgreed(activity: Activity) {
        StorageTipsHelper.startCleanUpActivity(activity)
    }

    override fun onAgreedProcessChain(chain: IInterceptor.IChain<Bundle, Unit>) {
        chain.fail(chain.param)
    }
}

/**
 * 通知权限拦截器
 *
 * 如果未获得通知权限，则：
 *   显示确认弹窗：
 *     同意 / 拒绝 -> 下一步
 * 否则：
 *   下一步
 */
class NotificationInterceptor(
    private val context: Context,
    private val postNotificationAction: (NotificationAction) -> Unit,
) : ConfirmInterceptor(postNotificationAction) {
    /**
     * @return 没有通知权限，弹窗阻断，返回 false。否则直接进入下一步，返回 true。
     */
    override fun onCheckCondition(param: Bundle): Boolean {
        return PermissionHelper.isNotificationsEnabled(context)
    }

    override fun onAgreed(activity: Activity) {
        PermissionHelper.requestNotificationPermission(activity)
    }

    override fun onRefused() {
        postNotificationAction(NotificationAction.ToastAction(AuthorizingR.string.authorizing_download_notification_blocked))
    }

    override fun onRefusedProcessChain(chain: IInterceptor.IChain<Bundle, Unit>) {
        chain.proceed(chain.param)
    }

    override fun createConfirmDialogAction(
        confirmCallback: NotificationAction.IConfirmCallback
    ): NotificationAction.ConfirmDialogAction = NotificationAction.ConfirmDialogAction(
        titleResId = AuthorizingR.string.authorizing_request_notification_title,
        messageResId = AuthorizingR.string.authorizing_request_notification_for_download,
        positiveButtonTextResId = R.string.common_confirm,
        negativeButtonTextResId = R.string.common_cancel,
        confirmCallback = confirmCallback
    )
}