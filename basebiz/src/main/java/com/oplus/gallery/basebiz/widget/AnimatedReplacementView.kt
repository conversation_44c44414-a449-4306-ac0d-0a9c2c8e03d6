/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AnimatedReplacementView.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-05-15
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/05/15  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.basebiz.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.children
import androidx.core.view.isEmpty
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 在切换内部 view（[replaceContent]）或移除内部 view（[removeContent]）时会做动画的容器，
 * 内部现在只会持有最多两个子 view（老的 content 和新的 content），
 *
 * ！外部不要直接调此 view 的原生 addViewXxx() 和 removeViewXxx() 相关 API。
 */
open class AnimatedReplacementView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    private val showAnimationParam: AnimationParam = DEFAULT_ANIMATION_PARAM,
    private val hideAnimationParam: AnimationParam = DEFAULT_ANIMATION_PARAM
) : FrameLayout(context, attrs, defStyleAttr) {

    /**
     * 新 content 加入时出现的位置，默认出现在下层
     */
    protected open val showDirection: ShowDirection = ShowDirection.SHOW_AT_BOTTOM

    /**
     * 获取当前的 content，
     * 如果当前 content 正在做消失动画则不记。
     */
    val currentContent: View? get() = when (showDirection) {
        ShowDirection.SHOW_AT_TOP -> children.lastOrNull { it != childIfHiding }
        ShowDirection.SHOW_AT_BOTTOM -> children.firstOrNull { it != childIfHiding }
    }

    /**
     * 最老的 content 的 index
     */
    private val oldestIndex: Int get() = when (showDirection) {
        ShowDirection.SHOW_AT_TOP -> if (isEmpty()) -1 else 0
        ShowDirection.SHOW_AT_BOTTOM -> childCount - 1
    }

    /**
     * 新 content 添加的位置
     */
    private val indexToAddContent: Int get() = when (showDirection) {
        // 添加到最后
        ShowDirection.SHOW_AT_TOP -> -1
        ShowDirection.SHOW_AT_BOTTOM -> if (isEmpty()) -1 else 0
    }

    /**
     * 隐藏动画，完成或取消后会设为 null
     */
    private var hideAnimation: COUISpringAnimation? = null
        set(value) {
            // 如果先前的动画正在跑，则取消
            if (field?.isRunning == true) field?.cancel()
            field = value
        }

    /**
     * 正在做隐藏动画的子 view，
     * 会在动画 start 后设置，在动画结束或 cancel 时被清空。
     */
    private var childIfHiding: View? = null

    /**
     * 显示动画，完成或取消后会设为 null
     */
    private var showAnimation: COUISpringAnimation? = null
        set(value) {
            // 如果先前的动画正在跑，则取消
            if (field?.isRunning == true) field?.cancel()
            field = value
        }

    init {
        layoutParams = LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    /**
     * 添加新的 content
     *
     * @param child 新的 view
     * @param withAnimation 是否需要做动画
     * @param showAlphaValue 显示时的 alpha 值，默认 1f
     * @param onContentShown child 显示动画完成后的回调（[withAnimation] 为 false 时则添加后回调）
     * @param showAnimDelayTime 显示动画的延迟时间，默认 0
     */
    fun replaceContent(
        child: View,
        withAnimation: Boolean,
        showAlphaValue: Float = TARGET_ALPHA_SHOW,
        onContentShown: (() -> Unit)? = null,
        showAnimDelayTime: Long = 0L
    ) {
        if (!withAnimation || child == childIfHiding) {
            // 如果传入的 child 还在做隐藏动画，则需要立马移除，否则 addView 时会出问题
            cancelAnimation()
            super.removeAllViews()
        } else {
            currentContent?.hideWithAnimation(hideAnimationParam)
        }

        child.alpha = if (withAnimation) TARGET_ALPHA_HIDE else showAlphaValue
        addViewStrictCount(child)

        if (withAnimation) {
            runImmediateOrDelay(showAnimDelayTime) { child.showWithAnimation(showAnimationParam, showAlphaValue, onContentShown) }
        } else {
            onContentShown?.invoke()
        }
    }

    /**
     * 移除当前 content
     *
     * @param withAnimation 是否需要做动画
     * @param onEnd 结束后的回调
     */
    fun removeContent(withAnimation: Boolean, onEnd: (() -> Unit)? = null) {
        if (withAnimation) {
            currentContent?.hideWithAnimation(hideAnimationParam, onEnd) ?: onEnd?.invoke()
        } else {
            currentContent?.let {
                super.removeView(it)
            }
            onEnd?.invoke()
        }
    }

    /**
     * 取消当前进行的所有动画
     */
    fun cancelAnimation() {
        showAnimation = null
        hideAnimation = null
    }

    private fun runImmediateOrDelay(delay: Long, action: () -> Unit) {
        if (delay <= 0L) {
            action()
        } else {
            postDelayed({ action() }, delay)
        }
    }

    /**
     * 添加子 view，并保持 childCount <= [MAX_CHILDREN_COUNT]
     */
    private fun addViewStrictCount(child: View) {
        // 清理多余的最老的子 view
        while ((childCount + 1) > MAX_CHILDREN_COUNT) {
            if (oldestIndex in 0 until childCount) {
                super.removeViewAt(oldestIndex)
            } else {
                // 正常情况下不会走到这，做兜底和打印
                GLog.e(TAG, LogFlag.DL) { "[addViewStrictCount] oldestIndex is out of bound: $oldestIndex, childCount: $childCount" }
                break
            }
        }
        super.addView(child, indexToAddContent)
    }

    /**
     * 创建 Alpha 的 SpringAnimation，
     * 此处不设置 startValue, 会从 view 当前的 alpha 开始跑。避免场景： show 到一半取消后，hide 开始时突然把 alpha 变成 1f。
     */
    private fun View.createAlphaSpringAnimation(targetValue: Float, bounce: Float, response: Float): COUISpringAnimation {
        val springForce = COUISpringForce(targetValue).setBounce(bounce).setResponse(response)
        return COUISpringAnimation(this, COUIDynamicAnimation.ALPHA).setSpring(springForce)
    }

    /**
     * 跑隐藏动画,
     * 动画结束或取消时会将 view 进行移除。
     */
    private fun View.hideWithAnimation(param: AnimationParam, onEnd: (() -> Unit)? = null) {
        val animation = createAlphaSpringAnimation(TARGET_ALPHA_HIDE, param.bounce, param.response)
        hideAnimation = animation
        childIfHiding = this
        animation.addEndListener { _, _, _, _ ->
            // cancel 或动画结束都会走到这
            childIfHiding = null
            hideAnimation = null
            removeView(this)
            onEnd?.invoke()
        }
        animation.start()
    }

    /**
     * 跑显示动画
     */
    private fun View.showWithAnimation(param: AnimationParam, showAlphaValue: Float, onEnd: (() -> Unit)? = null) {
        val animation = createAlphaSpringAnimation(showAlphaValue, param.bounce, param.response)
        showAnimation = animation
        animation.addEndListener { _, _, _, _ ->
            showAnimation = null
            onEnd?.invoke()
        }
        animation.start()
    }

    /**
     * 动画的参数，
     * 目前使用 COUISpringAnimation
     */
    data class AnimationParam(val bounce: Float, val response: Float)

    /**
     * 新 content 出现的位置
     */
    enum class ShowDirection {
        /**
         * 显示在老 content 的上面，动画时会直接出现在上层，
         * 插入时采用尾插法
         */
        SHOW_AT_TOP,

        /**
         * 显示在老 content 的下面，动画时会先出现在下层
         * 插入时采用头插法
         */
        SHOW_AT_BOTTOM
    }

    companion object {
        private const val TAG = "AnimatedReplacementView"

        /**
         * 隐藏动画的目标 alpha
         */
        private const val TARGET_ALPHA_HIDE = 0f

        /**
         * 显示动画的目标 alpha
         */
        private const val TARGET_ALPHA_SHOW = 1f

        /**
         * 最多同时存在两个 children（新 content 和老 content）
         */
        private const val MAX_CHILDREN_COUNT = 2

        /**
         * 默认的动画参数
         */
        private val DEFAULT_ANIMATION_PARAM = AnimationParam(0f, 0.3f)
    }
}