/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IntentConstant.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/09/03
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>          <version>    <desc>
 ** ------------------------------------------------------------------------------
 *  <PERSON><PERSON><PERSON>@Apps.Gallery3D   2020/09/03      1.0          OPLUS_FEATURE_APP_LOG_MONITOR
 *********************************************************************************/
package com.oplus.gallery.basebiz.constants

import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.ShareSession
import com.oplus.gallery.standard_lib.app.AppConstants

class IntentConstant {
    // for picturePage
    object ViewGalleryConstant {
        // camera or other image/video preview action(standard)
        const val ACTION_REVIEW = "com.android.camera.action.REVIEW"
        // image crop action
        const val ACTION_CROP = "com.android.camera.action.CROP"
        // for InnerAlbumViewAction
        const val ACTION_PHOTO_INNER_ALBUM = "com.coloros.gallery.action.INNER_ALBUM"
        // for AlbumViewAction
        const val ACTION_INNER_ALBUM_SET = "com.coloros.gallery.action.INNER_ALBUM_SET"

        // only for create activity by file manager
        const val KEY_ORDER_TYPE = "order-type"
        const val KEY_FILE_TYPE = "file-type"

        // bucketPath list
        const val KEY_MEDIA_FILE_LIST = "media-file-list"

        /**
         * Bundle中存放Uri列表的关键字。
         * 以bundle.putStringArray()/getStringArray()进行存取
         */
        const val KEY_URI_ARRAY = DataRepository.UriModelGetter.KEY_URI_ARRAY
        const val KEY_MIME_TYPE_ARRAY = DataRepository.UriModelGetter.KEY_MIME_TYPE_ARRAY
        const val SCHEME_HTTP = "http"
        const val SCHEME_HTTPS = "https"

        // only for create activity by bluetooth
        const val FROM_BLUETOOTH = AppConstants.Package.PACKAGE_NAME_BLUETOOTH

        const val KEY_MEDIA_FROM = "media-from"
        const val KEY_FROM_CAMERA = AppConstants.ParentPageConstants.KEY_FROM_CAMERA
        const val KEY_FROM_SELL = "from_sell"

        // Make sure the ai photo camera mode slides in the same direction as the other modes.
        const val KEY_FROM_AI_ID_PHOTO = AppConstants.ParentPageConstants.KEY_FROM_AI_ID_PHOTO
        const val KEY_FROM_FILE_MANAGER = AppConstants.ParentPageConstants.KEY_FROM_FILE_MANAGER
        const val KEY_FROM_WIDGET_CARD = "from_widget_card"
        const val KEY_EXTERNAL = "from-external"
        /**
         * 文管最近进入的标记，特殊表现为：大图无标题，连拍单张删除是删除单张
         */
        const val KEY_FROM_FILE_MANAGER_RECENT = AppConstants.ParentPageConstants.KEY_FROM_FILE_MANAGER_RECENT

        const val KEY_MEDIA_SET_PATH = "media-set-path"
        const val KEY_MEDIA_ITEM_PATH = DataRepository.ExtAlbumModelGetter.KEY_MEDIA_ITEM_PATH
        const val KEY_MEDIA_ID_LIST = "media-id-list"

        /**
         * 入场动画是否有被提前到点击缩略图时就做
         */
        const val KEY_HAS_PRE_TRANSITION = "has_pre_transition"

        /**
         * 外部传入的自带顺序的 media id list，不需要相册使用其他 order 信息来做排序
         */
        const val KEY_ORDER_MEDIA_ID_LIST = "order-media-id-list"
        const val KEY_GALLERY_ID_LIST = "gallery-id-list"
        const val KEY_MEDIA_FOLDER_PATH = "media-folder-path"
        const val KEY_MEDIA_MODEL_TYPE = "media_model_type"

        /* 编辑保存图片时，获取保存图片对应的Rect的Key值，用于设置过渡动画的显示区域 */
        const val KEY_MEDIA_ITEM_RECT = "media-item-rect"

        const val KEY_TITLE = "title"
        const val KEY_SINGLE_ITEM_ONLY = "SingleItemOnly"
        const val KEY_TREAT_BACK_AS_UP = "treat-back-as-up"
        /**
         * 标记本次进入大图时，删除连拍内单张图片是删除单张，还是删除整个连拍集合。
         * true 单独删除当前文件；false 删除当前文件所在的整个连拍图片。
         * 如“文管-绝对路径”、“文管-最近“场景为单删，”文管-视图“和其他所有情况都是删除整个目录。
         * 有个例外，具体为[KEY_FROM_FILE_MANAGER_RECENT]，不用这个规则控制，但是也是单删。
         */
        const val KEY_NOT_DISPLAY_CSHOT_BTN = "not_display_cshot_btn"
        const val KEY_ENABLE_PLAYBACK_PREVIEW = "enable_playback_preview"
        const val KEY_ENABLE_THUMBLINE_PREVIEW = "enable_thumbLine_preview"
        const val KEY_DISPLAY_SET_COVER_MENU = "display_set_cover_menu"

        // for mms
        const val KEY_MEDIA_MENU_FLAG = "media-menu-flag"
        const val KEY_MEDIA_SAVE_PATH = "media-save-path"

        //for ACTION_GET_CONTENT
        const val KEY_MAX_COUNT_LIMIT = "max-count-limit"
        const val KEY_MAX_SIZE_LIMIT = "max-size-limit"
        const val KEY_SINGLE_IMAGE_MAX_SIZE_LIMIT = "single-image-max-size-limit"
        const val KEY_SINGLE_VIDEO_MAX_SIZE_LIMIT = "single-video-max-size-limit"
        const val KEY_MEDIA_URI_LIST = "media-uri-list"
        const val KEY_CLIP_DESCRIPTION_LABEL = "gallerySelectMultiPhotos"

        /**
         * 页面的初始化Screen orientation
         * 可取值为:[android.content.pm.ActivityInfo.ScreenOrientation]中定义的常量SCREEN_ORIENTATION_XX:设为该值
         */
        const val KEY_INIT_SCREEN_ORIENTATION = "init_screen_orientation"

        /**
         * 相机进相册动画的类型
         * value = 1 强制做quick动画
         * value = 0 不需要强制做动画
         */
        const val KEY_CAMERA_TRANSITION_TYPE = "requestedTransitionType"

        /**
         * 启动大图时，是否期望大图的窗口 [android.app.Activity] 为透明背景
         */
        const val KEY_IS_ACTIVITY_TRANSPARENT = "isActivityTransparent"

        /**
         * 其他应用支持 无缝动画 + 提前启动，则使用该值，功能同[KEY_CAMERA_SHOULD_WAIT_FOR_CAMERA_ANIMATION]
         * true 提前启动流程，需要等待  false or null，非提前启动流程，无需等待
         */
        const val KEY_SHOULD_WAIT_FOR_TRANSITION_FINISH = "shouldWaitForTransitionFinish"

        /**
         * 相机进入相册，相机因为会提前启动相册，且动画是相机负责。相册在此情况下，需要等待相机的动画完成通知。
         * true 提前启动流程，需要等待  false or null，非提前启动流程，无需等待
         */
        const val KEY_CAMERA_SHOULD_WAIT_FOR_CAMERA_ANIMATION = "shouldWaitForCameraAnimation"

        /**
         * 相机进入相册，相机给定的quick图，是否已经做过角度旋转了
         * value：false； 表示quick图没有叠加任何角度，与final图直接解码出来的方向是一致的
         * value: true; 表示quick图已经叠加了角度。
         */
        const val KEY_IS_QUICK_ALREADY_ROTATED = "is_quick_already_rotated"

        /**
         * 来自哪条链路
         */
        const val KEY_CHAIN_FROM = "chain_from"

        /**
         * 来自哪条链路：相机
         */
        const val VALUE_CHAIN_FROM_CAMERA = "camera"

        /**
         * 大图页面的类型
         * int类型
         * 0，全功能大图，默认值
         * 1，预览大图
         */
        const val KEY_PHOTO_TYPE = "key_photo_type"
        const val KEY_PHOTO_TYPE_DEFAULT = 0
        const val KEY_PHOTO_TYPE_PREVIEW = 1

        /**
         * 拖拽到外部的手势是否可用
         */
        const val KEY_ENABLE_DRAG_OUT = "key_enable_drag_out"
        const val VALUE_ENABLE_DRAG_OUT_DEFAULT = true
        const val VALUE_ENABLE_DRAG_OUT_DISABLE = false

        /**
         * 旋转的手势是否可用
         */
        const val KEY_ENABLE_ROTATE = "key_enable_rotate"
        const val VALUE_ENABLE_ROTATE_DEFAULT = true
        const val VALUE_ENABLE_ROTATE_DISABLE = false

        /**
         * 选择数据的集合id，SelectionModel可以基于此创建选择数据集
         */
        const val KEY_SELECTION_DATA_ID = "key_selection_data_id"

        /**
         * 是否在退出时强制finishActivity，默认为false
         * 外部启动activity时，如果未明确告知强制关闭，那么在退出时可能不会关闭activity，最好能明确告知
         */
        const val KEY_FORCE_FINISH_ACTIVITY = "key_force_finish_activity"

        /** 是否需要切换暗色模式之后关闭activity */
        const val KEY_SHOULD_FINISH_ACTIVITY = "key_should_finish_activity"
        /**
         * 选图页面是否是多选模式，默认是单选
         */
        const val KEY_PHOTO_PREVIEW_SELECT_TYPE = "key_photo_select_multi_type"
        const val VALUE_PHOTO_PREVIEW_SELECT_SINGLE = 0
        const val VALUE_PHOTO_PREVIEW_SELECT_MULTI = 1

        /**
         * 另存为GIF保存跳转到gif大图页面后，gif大图页退出时，是否需要清除动画
         */
        const val IS_REMOVE_ANIMATION = "is_remove_animation"

        /**
         * 大图显示单张文件标记，例如，文管快捷方式进大图
         */
        const val KEY_SINGLE_MEDIA_ID = "single-media-id"

        /**
         * 视频大图进视频编辑时携带的bitmap所对应的key
         */
        const val KEY_EDIT_THUMBNAIL = "editor_thumbnail"

        /**
         * 锁屏状态下，点击菜单时携带的是否需要解锁屏幕的状态对应的key
         * value=true, 锁屏状态下需要解锁
         * value=false, 锁屏状态下不需要解锁
         */
        const val KEY_MENU_UNLOCK_SCREEN = "key_menu_unlock_screen"

        //for set video wallpaper
        /**
         * 设置视频壁纸，进入视频编辑页的标志
         * value=true, 进入视频编辑进行设置视频壁纸操作
         * value=false, 进入视频编辑进行非设置视频壁纸操作
         */
        const val KEY_IS_VIDEO_WALLPAPER_CUT = "isVideoWallPaperCut"
        /**
         * 是否被动跳转到设置壁纸页
         * value=true, 通过setResult返回，让壁纸自己处理
         * value=false, 通过startActivity, 主动跳转
         */
        const val KEY_FINISH_VIDEO_WALLPAPER_CUT = "finish_VideoWallPaperCut"
        /**
         * 视频裁剪的最短时长，单位ms。
         */
        const val KEY_VIDEO_TRIM_MIN_DURATION: String = "video-trim-min-duration"
        /**
         * 视频裁剪的最大时长，单位ms
         */
        const val KEY_VIDEO_TRIM_MAX_DURATION: String = "video-trim-max-duration"
        /**
         * 视频裁剪后的目标最大像素宽度，如1980，相册端根据目标宽度按照原视频比例进行等比降低分辨率接口。
         */
        const val KEY_VIDEO_TRIM_TARGET_MAX_WIDTH: String = "video-trim-target-max-width"
        /**
         * 视频裁剪后的目标最大像素高度，如1980，相册端根据目标宽度按照原视频比例进行等比降低分辨率接口。
         */
        const val KEY_VIDEO_TRIM_TARGET_MAX_HEIGHT: String = "video-trim-target-max-height"
        /**
         * 视频裁剪后的目标最大像素边长，如1980，相册端根据目标宽度按照原视频比例进行等比降低分辨率接口。
         */
        const val KEY_VIDEO_TRIM_TARGET_MAX_SIDE: String = "video-trim-target-max-side"
        /**
         * 视频裁剪后的最大目标帧率，如30。若原视频帧率是低于它，则使用原视频帧率即可；若超过它，则进行降帧率处理。
         */
        const val KEY_VIDEO_TRIM_TARGET_MAX_FRAME_RATE: String = "video-trim-target-max-frame-rate"
        /**
         * 视频裁剪后的目标码率。若原视频码率是低于它，则使用原视频帧率即可；若超过它，则进行降码率处理。
         */
        const val KEY_VIDEO_TRIM_TARGET_MAX_BIT_RATE: String = "video-trim-target-max-bit-rate"
        /**
         * 视频裁剪后的容器封装格式，使用mimetype字符串描述，如:video/mp4
         */
        const val KEY_VIDEO_TRIM_TARGET_CONTAINER_FORMAT: String = "video-trim-target-container-format"
        /**
         * 视频裁剪后的编码格式
         * 美摄目前支持"hevc"(h.265), "vp8"(audio格式必须是vorbis，文件格式必须是Webm)，默认"h.264"
         */
        const val KEY_VIDEO_TRIM_TARGET_CODEC_FORMAT: String = "video-trim-target-codec-format"
        /**
         * 视频时长裁剪后是否保留HDR，默认true。取值false或true
         */
        const val KEY_VIDEO_TRIM_KEEP_HDR: String = "video-trim-keep-hdr"

        /**
         * 指定ViewGallery的主题。需要更改ViewGallery主题时指定此参数即可。
         */
        const val KEY_VIEW_GALLERY_THEME: String = "view_gallery_theme"

        /**
         * 默认主题。即ViewGallery自定义的默认主题。
         */
        const val VALUE_VIEW_GALLERY_THEME_DEFAULT: Int = 0

        /**
         * 通用主题主题。即BaseActivity通用的CommonDefaultTheme。
         * 用于跳转图集页等通用页面。
         */
        const val VALUE_VIEW_GALLERY_THEME_COMMON: Int = 1

        /**
         * 当viewGallery退出时，是否需要关闭转场动画的alpha效果
         */
        const val KEY_VIEW_GALLERY_EXIT_ALPHA_DISABLE = "key_view_gallery_exit_alpha_disable"
    }

    object CShotPageConstant {
        /**
         * 连拍归档图集中的最优照片，通常会是封面照片。
         * 类型 ： Int
         */
        const val BEST_PICTURE_MEDIA_ID = "best-picture-media-id"

        /**
         * 连拍归档图所在的图集。
         *
         * 通常是从某个图集进入大图，然后进入连拍页面，此处指的就是进入大图的图集。
         */
        const val CSHOT_ORIGIN_SET_PATH = "cshot-origin_set_path"

        /**
         * 进入连拍的业务类型参数
         */
        const val KEY_CSHOT_JUMP_TO_TYPE = "cshot_jump_to_type"

        /**
         * 传递给连拍页面的 selectionSessionId Key
         */
        const val KEY_CSHOT_SELECTION_SESSION_ID = "cshot_selection_session_id"

        /**
         * 大图页加载的连拍图包封面缩图，通过此 Key 以 binder 方式传递给连拍页
         */
        const val KEY_CSHOT_COVER_THUMBNAIL = "cshot_cover_thumbnail"
        /**
         * 连拍页面勾选业务
         * CSHOT_RELEASE_DEFAULT: 解锁连拍勾选
         * CSHOT_GIF_SYNTHESIS_EDITOR: GIF合成连拍选图勾选
         */
        enum class Feature {
            CSHOT_RELEASE_DEFAULT,
            CSHOT_GIF_SYNTHESIS_EDITOR
        }
    }

    object PicturePageConstant {
        const val KEY_INDEX_HINT = "index-hint"

        /**
         * 大图共享数据的 [ShareSession] 的 id
         */
        const val KEY_SHARED_ID = "shared_id"

        // The photo is jumped from the photo without entity folder.
        const val KEY_FROM_TIMELINE_PAGE = "from_timeline_page"
        const val KEY_FROM_FAVORITES_ALBUM = "from_favorites_album"
        const val KEY_IS_COLLAGE_PHOTO = "from_collage_photo"
        const val KEY_OPEN_ANIMATION_RECT = "open-animation-rect"
        const val KEY_POSITION_CONTROLLER = "position_controller"

        /**
         * 注意：使用此字段传入预览图时，需要保证bitmap处于正确的rotate，要不然大图入场过渡动画会出飞机
         * path：大图->移动到->新建图集->打开新图集图片，重复以上操作两次查看是否有问题
         */
        const val KEY_TRANSITION_THUMBNAIL = "transition_thumbnail"
        const val KEY_ACTION_FLAG = "action-flag"
        const val KEY_GET_CONTENT = "get-content"
        const val KEY_GET_ALBUM = "get-album"
        const val KEY_CURRENT_PAGE = "current_page"
        const val KEY_ENTER_PHOTO_ANIMATE = "key_enter_photo_animate"
        const val KEY_CLOSE_CAMERA_ENTER_ANIM = "close_camera_enter_anim"
        const val KEY_VIEW_REVERT = "key_view_revert"
        const val KEY_STATUSBAR_TINT = "key_statusbar_tint"
        const val KEY_THUMB_SIZE_TYPE = "key_thumb_size_type"

        /**
         * 共享图集id
         */
        const val KEY_SHARED_ALBUM_ID = "key_shared_album_id"

        /**
         * 共享图集创建者用户id
         */
        const val KEY_SHARED_ALBUM_CREATE_USER_ID = "key_shared_album_create_user_id"
        const val KEY_PLAYBACK_POSITION = "playback_position"
        const val KEY_FROM_SELF_SPLIT = "key_from_self_split"

        const val KEY_VIEW_TYPE = "key_view_type"
        const val KEY_CROP = "key_crop"
        /**
         * 不同数据源进入大图时，判断是否需要触发解缩图的key
         * */
        const val KEY_SHOULD_DECODE_MICRO_PREVIEW_SHOT = "key_should_decode_micro_preview_shot"
    }

    object LabelConstant {
        const val KEY_LABEL_ID = "key_label_id"
        const val KEY_FROM_LABEL = "key_from_label"
        const val KEY_LABEL_NAME = "key_label_name"
        /**
         * 标签来源类型
         */
        const val KEY_LABEL_TYPE = "key_label_type"
    }

    object LocationConstant {
        const val KEY_LOCATION_SORTER = DataRepository.LocationModelGetter.KEY_LOCATION_SORTER

        /**
         * 从地点图集跳转到地点详情的跳转动画
         */
        const val KEY_JUMP_DETAIL_ANIM = "key_jump_detail_anim"
    }

    object MemoryConstant {
        const val KEY_MEDIA_PATH = "media-path"
    }

    object EditablePhotoConstant {
        const val KEY_EDIT_CSHOT = "edit_cshot"
        const val KEY_EDIT_IMAGE_URI = "editor_image_uri"
        const val KEY_EDIT_FILE_PATH = "editor_image_path" // 跟编辑约定的key值，不改动
        const val KEY_EDIT_FROM_PHOTO = "editor_from_photo"
        const val KEY_IS_FROM_EXTERNAL = "is_from_external"
        const val KEY_EDIT_MEDIA_ITEM_PATH = "editor_media_item_path"
        const val KEY_EDIT_MEDIA_SET_PATH = "editor_media_set_path"
        const val KEY_EDIT_INVOKER = "invoker"
        const val KEY_EDIT_INVOKER_TOKEN = "invoker_token"
        const val KEY_GALLERY_EDIT_INVOKE = "invoke_from_gallery"
        const val KEY_EDIT_SKILL = "edit_skill"
        const val KEY_EDITOR_MODE = "editor_mode"

        const val KEY_EDIT_SHOW_BACK_TITLE = "show_back_title"
        const val KEY_EDIT_SUPPORT_LOSS_LESS_CACHE = "support_loss_less_cache"
        const val KEY_EDIT_ROTATION = "rotation"
        const val KEY_EDIT_THUMBNAIL = "editor_thumbnail"
        const val KEY_EDIT_EXTRAS = "editor_extras"

        const val KEY_PORTRAIT_BLUR_CONFIG = "blurConfig"
        const val KEY_DROP_PICTURE_URI = "drop_picture_uri"

        const val KEY_HASSEL_WATERMARK_EDITABLE = "hassel_watermark_editable"
        const val KEY_HAS_HASSEL_WATERMARK = "has_hassel_watermark"

        /**
         * 灰度图,可能带虚化等效果
         */
        const val KEY_EDIT_BRIGHTEN_GRAY_IMAGE = "brighten_gray_image"

        /**
         * 元数据
         */
        const val KEY_EDIT_BRIGHTEN_METADATA = "brighten_metadata"

        const val KEY_MEDIA_ID = "media_id"

        /**
         * 大图进编辑，获取大图位置
         */
        const val KEY_PHOTO_POSITION_FOR_EDITOR = "photo_position_for_editor"

        /**
         * 编辑类型
         */
        const val KEY_EDIT_TYPE_TAG = "editor_type_tag"

        /**
         * 是否需要禁用保存按键
         * */
        const val KEY_EDIT_DISABLE_SAVE = "is_disable_save"

        /**
         * 是否是未修改返回
         * */
        const val KEY_IS_CANCEL_FROM_EDIT = "is_cancel_from_edit"
    }

    // Marked by 林辉 跳转GIF合成页参数Key 重新定义一下，需要与GIF合成页对齐
    object GifSynthesisConstant {
        const val KEY_GIF_CONST_JUMP_TYPE = "action_gif_jump_type"

        const val KEY_GIF_CONST_IMAGE_URI = "editor_image_uri"
        const val KEY_GIF_CONST_IMAGE_PATH = "editor_image_path"
        const val KEY_GIF_CONST_CONTENT_DISPLAY_RECT = "editor_content_display_rect"
        const val KEY_GIF_CONST_CONSTRAINT_DISPLAY_RECT = "editor_constraint_display_rect"
        const val KEY_GIF_CONST_THUMBNAIL = "editor_thumbnail"
        const val KEY_GIF_CONST_INVOKE_TOKEN = "invoker_token"
        const val KEY_GIF_CONST_FROM_PHOTO = "editor_from_photo"
        const val KEY_GIF_CONST_IS_LOCK_MODE = "action_gif_is_lock_mode"

        const val KEY_GIF_CONST_FORM_CSHOT = "is_form_cshot"
        const val KEY_GIF_CONST_FORM_OLIVE = "is_from_olive"
        const val KEY_GIF_CONST_BESH_CSHOT_ID = "action_gif_best_cshot_Id"
        const val KEY_GIF_CONST_ORIGIN_SET_PATH = "action_gif_origin_set_path"
        const val KEY_GIF_CONST_CSHOT_BEST_PIC_MEDIA_ID = "action_gif_best_pic_media_id"


        const val KEY_GIF_CONST_PATH_LIST = "path_list"
        const val KEY_GIF_CONST_INVALID_COUNT = "invalid_image_count"
        /**
         * JUMP_FORM_CSHOT = 0 来自大图连拍页
         * JUMP_FORM_MULTIPLE_SELECTION = 1 来自多选
         * JUMP_FORM_OLIVE = 2 后期olive
         */
        const val JUMP_FORM_CSHOT = 0
        const val JUMP_FORM_MULTIPLE_SELECTION = JUMP_FORM_CSHOT + 1
        const val JUMP_FORM_OLIVE = JUMP_FORM_MULTIPLE_SELECTION + 1
    }

    object AlbumConstant {
        const val KEY_HIDE_INTERNAL_TOOLBAR = "hide_internal_toolbar"
        const val KEY_FROM_SEARCH_ACTIVITY = "from_search_activity"
        //跳转到MapTravelFragment的时候，默认选择map页还是默认选择travel页， true=map, false = travel
        const val KEY_TARGET_MAP = "target_map"
        const val KEY_MEDIA_PATH = "media-path"
        //判断是否是从照片详情页面进入的MapActivity的intent值
        const val KEY_FROM_IMAGE_DETAIL = "from_image_detail"
        //从图片详情页进入MapActivity时带入的具体的图片的Path：local/item/image/id
        const val KEY_IMAGE_DETAIL_PATH = "image_detail_item_path"
        //从图片详情页进入MapActivity时带入的Map的默认缩放比例
        const val KEY_MAP_DEFAULT_ZOOM = "map_default_zoom"
        //判断是否是从时间轴进入MapActivity的intent值
        const val KEY_FROM_TIME_LINE = "from_time_line"
        const val KEY_MODEL_TYPE = "model_type"
        const val KEY_ALBUM_TITLE = "album_title"
        /** 地图页跳转时携带的path child index */
        const val KEY_PATH_CHILD_INDEX = "path_child_index"
        /** 是否支持个性化筛选，true为默认，表示支持 */
        const val KEY_ALBUM_SUPPORT_PERSONAL_FILTER = "album_support_personal_filter"

        /**是否需要滚动到底部，isPositiveOrder()为默认，false表示列表不需要滚动到底部 */
        const val KEY_NEED_SCROLL_TO_BOTTOM = "need_scroll_to_bottom"
    }

    object TimelineConstant {
        const val KEY_SHOW_MAP_TITLE = "show_map_title"
    }

    object TrackConstant {
        const val ALBUM_NAVIGATION_TRACK_PAGE_ID = "ALBUM_NAVIGATION_TRACK_PAGE_ID"
    }

    object SearchConstant {
        const val FONT_SIZE = "fontSize"
        const val TEXT_COLOR = "color"
        const val KEY_JUMP_SOURCE = "jump_source"
    }

    object SelectionConstant {
        const val SELECTION_BUNDLE = "operation_bundle"
        const val KEY_FINISH_WHEN_SWITCH_NIGHT_MODE = "switch_night_mode"
        const val REQUEST_OPERATION_SELECTION = -100
    }

    object WidgetConstant {
        const val KEY_WIDGET_CODE = "widget_code"
        const val KEY_DISPLAY_LIST_ID = "display_list_id"
        const val KEY_WIDGET_MODE = "widget_mode"
        const val KEY_TEXT_ID_OF_REMOVE_FROM_LIST_MENU = "text_of_remove_from_list_menu"
        const val KEY_TOAST_TEXT_ID_AFTER_ALL_REMOVED = "toast_after_all_removed"
        const val KEY_WIDGET_TRACK_MODE_NAME = "widget_track_mode_name"
        const val KEY_WIDGET_RECOMMENDED_TEXT = "widget_recommended_text"
        const val KEY_NEED_LAUNCHER_ANIM  = "isNeedLauncherAnim"
        const val NEED_LAUNCHER_ANIM_FALSE  = "false"
    }

    object GalleryShareConstant {
        /**
         * 分享页过渡动画
         */
        const val KEY_USE_TRANSITION_PAGE_ANIM = "key_use_transition_page_anim"
        const val KEY_POSITION_CONTROLLER = "position_controller"
        const val KEY_SHARE_BITMAP = "key_picture_share_page_bitmap"
        const val KEY_SHARE_ASPECT_RATIO = "key_picture_share_aspect_ratio"
    }

    object PreDecoderConstant {
        /**
         * 是否强制解码超大图（2亿像素）
         */
        const val FORCE_DECODE_FOR_LARGE_IMAGE = "forceDecodeForLargeImage"
    }

    /**
     * 翻译功能需求所需常量，用于与语音翻译交互
     */
    object TranslateConstant {
        /**
         * 启动翻译浮窗时的action
         */
        const val GLOBAL_TRANSLATION_ACTION = "oplus.intent.action.GLOBAL_TRANSLATION"
        /**
         * 启动翻译浮窗和退出浮窗时都需要传入应用包名，语音翻译内部会做逻辑处理，只有拉起浮窗的应用才能做退出操作
         */
        const val KEY_TRANSLATION_FROM_PACKAGE = "extra_from_package"
        /**
         * 启动语音翻译浮窗的类型
         * -1:默认值，会拉起翻译的功能浮窗
         * 0:会拉起翻译的功能浮窗，同时会自动开启区域翻译功能
         * 1:会拉起翻译的功能浮窗，同时会自动开启全屏翻译功能
         */
        const val KEY_TRANSLATION_TYPE = "type"
        /**
         * 1代表拉起翻译的功能浮窗，同时会自动开启全屏翻译功能
         */
        const val LAUNCH_TRANSLATION_TYPE = 1
        /**
         * 需要退出翻译功能时，需要给语音翻译发送的广播
         */
        const val GLOBAL_TRANSLATION_EXIT_ACTION = "oplus.intent.action.GLOBAL_TRANSLATION_EXIT"
    }

    /**
     * 扫一扫功能需求所需常量
     */
    object  OcrScannerConstant {
        /**
         * 跳转到 扫一扫需要的Action
         * OCR_SCANNER_ACTION_DOCUMENT_CONVERT: 文档转化
         * OCR_SCANNER_ACTION_SUPER_TEXT: 超级文本
         */
        const val OCR_SCANNER_ACTION_DOCUMENT_CONVERT = "coloros.intent.action.DOCUMENT_CONVERT"
        const val OCR_SCANNER_ACTION_SUPER_TEXT = "coloros.intent.action.SUPER_TEXT"
        /*
        * 是否需要转pdf
        * 相册点击AI文档处理按钮，该字段传false
        * 相册点击转pdf按钮，该字段传true
        * */
        const val KEY_OCR_SCANNER_TYPE_PDF = "extra_export_pdf"
        /** 图片uri列表 */
        const val KEY_OCR_SCANNER_PIC_LIST = "pic_uri_list"
        /** 功能 */
        const val KEY_OCR_SCANNER_ENTRANCE_FUNCTION = "extra_entrance_function"

        /**
         * 跳转到 扫一扫 功能对应的value
         * VALUE_OCR_SCANNER_ENTRANCE_FUNCTION_HQ: 跳转到 高清ppt功能
         * VALUE_OCR_SCANNER_ENTRANCE_FUNCTION_SUPER_TEXT: 跳转到 扫一扫主页的 超级文本功能tab页(识文)
         */
        const val VALUE_OCR_SCANNER_ENTRANCE_FUNCTION_HQ = "convert_hq_doc"
        const val VALUE_OCR_SCANNER_ENTRANCE_FUNCTION_SUPER_TEXT = "super_text"
        /** 源应用包名 */
        const val KEY_OCR_SCANNER_REQUEST_PACKAGE = "extra_from_package"
    }
}