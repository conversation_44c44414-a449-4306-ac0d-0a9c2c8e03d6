/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DelegateLaunchingActivity.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/11/14
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2022/11/14  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.basebiz.uikit.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Display
import androidx.appcompat.app.AppCompatActivity

/**
 * 代理启动 [Intent] 的 Activity
 *
 * > 特性：
 *
 * - 会自动从 [Activity.getIntent] 获取 [Intent]，是个参数容器，包含启动参数
 * - 当 [DelegateLaunchingActivity.onCreate] 时 从参数容器中解析出启动参数目标 [Intent] 和 [Bundle]
 * - 紧接着，根据解析出的参数，优先启动目标 [Activity]，然后立即 [finish] 掉自己
 *
 * > 蜻蜓小屏权限弹窗场景：
 *
 * 背景：
 *
 * - 蜻蜓机器，折叠状态，内销版本，首次进入相册界面，此时应弹出权限界面，但为保证体验一致性，所有隐私弹窗需要到内屏展示，并由系统弹出“展开屏幕继续查看”的提示弹窗
 * - 启动 [Activity] 时，需要添加特殊 OplusFlag，系统才会弹出“展开屏幕继续查看”的提示弹窗；并且展开后，系统才会真正启动目标 Activity
 * - 当该弹窗消失时（5 秒内未展开屏幕则自动消失、或者手动下滑消失），**对应应用收不到任何回调与通知**
 *
 * 需求：
 *
 * - 小屏相机首次进入相册，相册需要弹出“展开屏幕继续查看”的提示弹窗，如果 5 秒内未展开屏幕或者手动下滑弹窗，弹窗消失 + 回到相机界面
 *
 * 方案：
 *
 * - 参考相机实现，以相机进大图为例，首次进入大图 [Activity]，会检查权限弹窗
 * - 首先收集相机进大图 [Activity] 的 [Intent] 和启动选项 [Bundle] (需要携带 [Display.getDisplayId] 信息)
 * - 然后将收集的参数构建 [Intent]，并设置 OplusFlags，通过它启动一个代理 [DelegateLaunchingActivity]
 * - 此时会唤出系统的“展开屏幕继续查看”提示弹窗，但代理 [DelegateLaunchingActivity] 并不会真正启动，只有展开的时候才会启动
 * - 接着调用 [Activity.finish]，退出当前大图 [Activity]，此时就只剩下系统“展开屏幕继续查看”提示弹窗
 *
 * 结果：
 *
 * - 如果展开屏幕：[DelegateLaunchingActivity] 会被系统启动，间接在主屏启动相册大图（和相机进大图一样，参数也一致）
 * - 如果弹窗消失（5 秒内未展开、或者下滑退出）：则自然回到相机界面，代理 [DelegateLaunchingActivity] 也不会启动
 */
class DelegateLaunchingActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val launchIntent = intent.getParcelableExtra<Intent>(EXTRA_LAUNCH_ACTIVITY_INTENT)
        val launchOptions = intent.getParcelableExtra<Bundle>(EXTRA_LAUNCH_ACTIVITY_OPTIONS)
        launchIntent?.let { startActivity(launchIntent, launchOptions) }

        finish()
    }

    companion object {
        /**
         * 索引 [Intent] key：启动 [Activity] 的 [Intent]
         */
        const val EXTRA_LAUNCH_ACTIVITY_INTENT = "launchIntent"

        /**
         * 索引 [Bundle] key：启动 [Activity] 的 launchOptions
         */
        const val EXTRA_LAUNCH_ACTIVITY_OPTIONS = "launchOptions"
    }
}