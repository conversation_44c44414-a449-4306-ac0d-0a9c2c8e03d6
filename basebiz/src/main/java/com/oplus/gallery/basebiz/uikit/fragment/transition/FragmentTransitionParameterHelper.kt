/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FragmentTransitionParameterHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/06/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2025/06/04		1.0		OPLUS_FEATURE_TAB
 *********************************************************************************/

package com.oplus.gallery.basebiz.uikit.fragment.transition

import android.content.Context
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Color
import android.os.Bundle
import android.view.View
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.cardview.COUICardView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.gallery.basebiz.R as BaseBizR
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.KEY_SEAMLESS_TRIGGER_VIEW_DRAWING_BITMAP
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.KEY_SEAMLESS_TRIGGER_VIEW_RADIUS
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.KEY_SEAMLESS_TRIGGER_VIEW_RECT_GETTER
import com.oplus.gallery.basebiz.widget.SlotView
import com.oplus.gallery.foundation.util.ext.getViewBitmap
import com.oplus.gallery.foundation.util.ext.getViewGroupBitmap
import com.oplus.gallery.foundation.util.multiprocess.TransBitmapBinder
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.support.appcompat.R as AppCompatR

object FragmentTransitionParameterHelper {
    /**图集列表的“常用图集”模块（排除地图、卡证档案）*/
    const val ENTRANCE_NORMAL = 1

    /**图集列表的“我的图集”模块*/
    const val ENTRANCE_MY_ALBUM_SET = 2

    /**图集列表点击“我的图集”模块右边的箭头打开的“我的图集”详细列表页面*/
    const val ENTRANCE_MY_ALBUM_SET_SECOND = 22

    /**图集列表的精选模块中的“旅程”模块*/
    const val ENTRANCE_TRAVEL = 3

    /**“旅程”模块中旅程普通列表页面*/
    const val ENTRANCE_TRAVEL_SECOND = 32

    /**图集列表的精选模块中的“人宠”模块*/
    const val ENTRANCE_PERSON_PET = 4

    /**“人宠”模块中的合照列表页面*/
    const val ENTRANCE_PERSON_PET_SECOND = 42

    /**“人宠”模块中的单一合照详情页面*/
    const val ENTRANCE_PERSON_PET_THIRD = 43

    /**图集列表的精选模块中的“回忆”模块*/
    const val ENTRANCE_MEMORIES = 5

    /**“回忆”模块中的回忆详情页面*/
    const val ENTRANCE_MEMORIES_SECOND = 52

    /**图集列表的精选模块中的“智能分类”模块*/
    const val ENTRANCE_LABEL = 6

    /**“智能分类”模块的图集列表页面*/
    const val ENTRANCE_LABEL_SECOND = 62

    /**图集列表的常用模块中的“地图”图集*/
    const val ENTRANCE_MAP = 7

    /**图集列表中的“我的图集”模块中的“地图”图集，UI布局不一样*/
    const val ENTRANCE_MAP_OF_MY_ALBUM_SET = 72

    /**图集列表的常用模块中的“卡证档案”图集*/
    const val ENTRANCE_CARD = 8

    /**图集列表中的“我的图集”模块中的“卡证档案”图集，UI布局不一样*/
    const val ENTRANCE_CARD_OF_MY_ALBUM_SET = 82

    /**图集列表的常用模块中的“最近删除”图集*/
    const val ENTRANCE_RECYCLE = 9

    @JvmStatic
    fun isNightMode(): Boolean {
        return (ContextGetter.context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES
    }

    @JvmStatic
    fun putTransitionParameterInBundle(
        context: Context?,
        bundle: Bundle,
        clickItemView: View?,
        triggerViewRectGetter: TriggerViewRectGetter,
        entrance: Int,
        radius: Float = 0f,
        bitmapViewId: Int = BaseBizR.id.base_album_set_item
    ) {
        bundle.apply {
            clickItemView?.let {
                putParcelable(KEY_SEAMLESS_TRIGGER_VIEW_RECT_GETTER, triggerViewRectGetter)
                var realRadius = radius
                var realCoverBitmap: Bitmap? = null
                when (entrance) {
                    ENTRANCE_NORMAL,
                    ENTRANCE_MAP,
                    ENTRANCE_CARD,
                    ENTRANCE_RECYCLE,
                    ENTRANCE_TRAVEL,
                    ENTRANCE_PERSON_PET,
                    ENTRANCE_MEMORIES,
                    ENTRANCE_LABEL -> {
                        /** 图集列表中“常用”模块和“精选”模块中的子图集项是用SlotView显示的
                         * 常用模块的SlotView的id是 base_album_set_item
                         * 精选模块的SlotView的id是 image_album_set_cover */
                        (it.findViewById(bitmapViewId) as? SlotView)?.apply {
                            /** 暗色模式下：
                             * “常用”模块的子图集项的都是实色缩率图，都是不透明的
                             * “精选”模块的子图集项的背景色是#1AFFFFFF (10%不透明度，90%透明度)，视觉上是通过跟页面的背景色（#000000）叠加呈现
                             * 所以，当暗色模式下，需要获取页面的背景色作为点击的子图集项的bitmap的底色，然后再将子图集项View绘制在底色上面。 */
                            realCoverBitmap = if (isNightMode() && (bitmapViewId != BaseBizR.id.base_album_set_item)) {
                                val backgroundColor = COUIContextUtil.getColor(context, AppCompatR.color.coui_color_background_with_card_dark)
                                this.getViewBitmap(backgroundColor = backgroundColor, radius = radius)
                            } else {
                                this.getViewBitmap()
                            }
                        }
                    }

                    ENTRANCE_MY_ALBUM_SET,
                    ENTRANCE_MY_ALBUM_SET_SECOND,
                    ENTRANCE_MAP_OF_MY_ALBUM_SET,
                    ENTRANCE_CARD_OF_MY_ALBUM_SET,
                    ENTRANCE_TRAVEL_SECOND,
                    ENTRANCE_PERSON_PET_SECOND,
                    ENTRANCE_PERSON_PET_THIRD,
                    ENTRANCE_MEMORIES_SECOND,
                    ENTRANCE_LABEL_SECOND -> {
                        when (it) {
                            is COUICardView -> {
                                realRadius = it.radius
                                /** COUICardView 是继承自FrameLayout，它的圆角是在init的时候直接设置给background，并没有复写draw() 方法，
                                 * 所以采取 getViewBitmap() 方法获取到的bitmap是没有圆角的，在亮色模式下，就是白色的底色上面绘制了带有圆角的SlotView或者ImageView。
                                 * 使用这个bitmap作为封面，白色的直角就会显得很奇怪，所以将getViewBitmap() 方法调整为 getViewGroupBitmap() 方法*/
                                realCoverBitmap = it.getViewGroupBitmap(Color.TRANSPARENT, 0)
                            }

                            is COUICardListSelectedItemLayout -> {
                                realRadius = it.radius
                                /** 暗色模式下：“我的图集”模块的子图集项的背景色是#1AFFFFFF (10%不透明度，90%透明度)，视觉上是通过跟页面的背景色（#000000）叠加呈现
                                 * 所以，当暗色模式下，需要获取页面的背景色作为点击的子图集项的bitmap的底色，然后再将子图集项View绘制在底色上面。*/
                                val firstBackgroundColor = COUIContextUtil.getAttrColor(context, AppCompatR.attr.couiColorCardBackground)
                                val secondBackgroundColor = if (isNightMode()) {
                                    COUIContextUtil.getColor(context, AppCompatR.color.coui_color_background_with_card_dark)
                                } else {
                                    0
                                }
                                realCoverBitmap = it.getViewGroupBitmap(firstBackgroundColor, secondBackgroundColor)
                            }

                            else -> realCoverBitmap = it.getViewGroupBitmap(isNeedDrawParent = true)
                        }
                    }
                }

                putFloat(KEY_SEAMLESS_TRIGGER_VIEW_RADIUS, realRadius)
                realCoverBitmap?.let { bitmap -> putBinder(KEY_SEAMLESS_TRIGGER_VIEW_DRAWING_BITMAP, TransBitmapBinder(bitmap)) }
            }
        }
    }
}