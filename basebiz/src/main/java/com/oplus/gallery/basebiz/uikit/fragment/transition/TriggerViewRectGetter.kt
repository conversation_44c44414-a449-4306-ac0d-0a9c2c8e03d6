/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TriggerViewRectGetter.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/07/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2025/07/04		1.0		OPLUS_FEATURE_TAB
 *********************************************************************************/

package com.oplus.gallery.basebiz.uikit.fragment.transition

import android.graphics.Rect
import android.os.Parcel
import android.os.Parcelable
import android.view.View
import com.oplus.gallery.foundation.util.ext.getGlobalRectOnScreen
import java.lang.ref.WeakReference

class TriggerViewRectGetter(
    triggerView: View?,
    val offset: IntArray,
    val toolBarBottomY: Float = 0f,
    private val isAdjustRect: Boolean = true
) : Parcelable {
    private var weakViewRef: WeakReference<View> = WeakReference(triggerView)
    private val view: View?
        get() = weakViewRef.get()

    private var viewId: Int = view?.id ?: View.NO_ID

    constructor(parcel: Parcel) : this(
        null,
        parcel.createIntArray() ?: IntArray(2),
        parcel.readFloat(),
        parcel.readBoolean()
        ) {
        viewId = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(viewId)
        parcel.writeIntArray(offset)
        parcel.writeFloat(toolBarBottomY)
        parcel.writeBoolean(isAdjustRect)
    }

    override fun describeContents(): Int = 0

    companion object CREATOR : Parcelable.Creator<TriggerViewRectGetter> {
        override fun createFromParcel(parcel: Parcel): TriggerViewRectGetter {
            return TriggerViewRectGetter(parcel)
        }

        override fun newArray(size: Int): Array<TriggerViewRectGetter?> {
            return arrayOfNulls(size)
        }
    }

    fun getTriggerViewRect(outRect: Rect) {
        weakViewRef.get()?.getGlobalRectOnScreen()?.let {
            /**处理浮窗模式下的偏移*/
            it.offset(-offset[0], -offset[1])
            if (it.width() < it.height() && isAdjustRect) {
                /**处理竖着的长方形的item只取与宽相同的正方形区域做封面*/
                it.bottom = it.top + it.width()
            }
            outRect.set(it)
        }
    }
}