/**************************************************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 * OPLUS Java File Skip Rule:MethodLength
 ** File        : GalleryOpenProvider.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2019/1/17
 ** Author      : <PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 **
 ** --------------------- --------------- Revision History ----------------------------------------
 **  <author>                        <data>         <version >     <desc>
 **  <EMAIL>     2019/1/17      1.0            Build this module
 *************************************************************************************************/
package com.oplus.gallery.openabilitypage;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.ContentProvider;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.UriMatcher;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.res.Resources;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.CancellationSignal;
import android.os.ParcelFileDescriptor;
import android.provider.MediaStore;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.andes.photos.kit.search.data.CompositeData;
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureHelper;
import com.oplus.gallery.basebiz.permission.helper.CTAHelper;
import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.cache.diskcache.CoverCachedHelper;
import com.oplus.gallery.business_lib.helper.AllAlbumDataHelper;
import com.oplus.gallery.business_lib.model.config.allowlist.GalleryOpenListConfig;
import com.oplus.gallery.business_lib.model.config.allowlist.GalleryOpenListConfig.PackageBean;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local;
import com.oplus.gallery.business_lib.model.data.base.utils.Constants;
import com.oplus.gallery.business_lib.model.data.base.utils.MediaSetUtils;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil;
import com.oplus.gallery.business_lib.model.data.local.set.ClassifiedOliveAlbum;
import com.oplus.gallery.business_lib.model.data.local.set.FavoritesAlbum;
import com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet;
import com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.VirtualAlbumEntry;
import com.oplus.gallery.business_lib.model.data.memories.set.MemoriesAlbum;
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper;
import com.oplus.gallery.business_lib.track.OpenAbilityTrackHelper;
import com.oplus.gallery.business_lib.track.RecycleTrackHelper;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns;
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns;
import com.oplus.gallery.foundation.database.store.GalleryStore.SeniorMediaColumns;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.database.util.SQLGrammar;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag;
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper;
import com.oplus.gallery.foundation.gstartup.GStartup;
import com.oplus.gallery.foundation.security.EncryptUtils;
import com.oplus.gallery.foundation.security.ProviderSecurityUtils;
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant;
import com.oplus.gallery.foundation.tracing.constant.OpenAbilityTrackConstant.OpenFeatureName;
import com.oplus.gallery.foundation.tracing.constant.OpenAbilityTrackConstant.Value.CallResult;
import com.oplus.gallery.foundation.tracing.constant.OpenAbilityTrackConstant.Value.SearchHasResult;
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value;
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.ext.DeferredExtKt;
import com.oplus.gallery.foundation.util.ext.StringExtKt;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.foundation.util.version.ApiLevelUtil;
import com.oplus.gallery.foundation.util.version.AppVersionUtils;
import com.oplus.gallery.framework.abilities.search.SearchType;
import com.oplus.gallery.framework.abilities.search.permission.AccessPackageManager;
import com.oplus.gallery.openabilitypage.watermark.WaterMarkUriMatcher;
import com.oplus.gallery.openabilitypage.watermark.WatermarkOperator;
import com.oplus.gallery.framework.abilities.search.query.DmpQuery;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.app.multiapp.MultiAppUtils;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.standard_lib.scheduler.BatchProcess;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import kotlin.coroutines.Continuation;
import kotlin.coroutines.CoroutineContext;
import kotlin.jvm.Synchronized;
import kotlin.jvm.functions.Function2;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineScope;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Deferred;
import kotlinx.coroutines.Dispatchers;

import static android.provider.BaseColumns._ID;
import static com.oplus.andes.photos.kit.search.data.AndesMultiQueryResult.SEARCH_COMPOSITE_RESULT_ENTRIES;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ONLY_LOCAL_FILE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_IMAGE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_VIDEO;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_INPUT;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_ALL_PIC;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_CSHOT;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_FASTVIDEO;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_FAVORIES;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_GIF;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_OLIVE;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_PANORAMA;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_PORTRAIT_BLUR;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_SLOW_MOTION;
import static com.oplus.gallery.business_lib.model.data.local.set.VirtualAlbumSet.ALBUM_TYPE_VIDEO;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_MARK_DELAY_RECYCLE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.MemoriesSetmapColumns.SET_ID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.BOTTOM;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.GROUP_ID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.GROUP_NAME;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.LEFT;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.RIGHT;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.THUMB_H;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.THUMB_W;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.TOP;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.AND;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.AS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ASC;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ASTERISK;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.DESC;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.DOT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_TO;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.FROM;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.GROUP_CONCAT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.IN;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_OUTER_JOIN;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LIMIT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.NOT_EQUAL_ONE;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.OFFSET;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ON;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.OR;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ORDER_ASC;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ORDER_DESC;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE;
import static com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_OLIVE;
import static com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_STAR;
import static com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_VERTICAL_LINE;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.CLOSE_CLOUD_SYNC;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.CLOSE_EYES_REPAIR;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.ELIMINATE_PASSERBY;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.FACE_RESOLUTION;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.GET_PHOTO_SHOW_STATE;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.IMAGE_COMPOSITION;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.IMAGE_DEBLURING;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.IMAGE_DEREFLECTIVE;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.IMAGE_ENHANCE;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.KEY_BREENO_INVOKED_RESULT;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.OPEN_AIGC_ELIMINATE;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.OPEN_CLOUD_SYNC;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.OPEN_CLOUD_SYNC_SETTING;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.OPEN_GROUP_PHOTO;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.SwitchCloudSyncResult.FAIL_NO_AUTH;
import static com.oplus.gallery.openabilitypage.OpenAbilityCallManager.getThumbnailByBundle;
import static com.oplus.gallery.openabilitypage.OpenProviderQueryArgsParser.getIsOCSFromArgs;
import static com.oplus.gallery.openabilitypage.OpenProviderQueryArgsParser.getOCSPackageBean;
import static com.oplus.gallery.openabilitypage.OpenProviderQueryArgsParser.joinQueryArgs;
import static com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.GET_CURRENT_SHOW_PHOTO_URI;
import static com.oplus.gallery.openabilitypage.SmartOpenProvider.STRING_IS_FROM_SMART;

public class GalleryOpenProvider extends ContentProvider {
    public static final String TAG = "GalleryOpenProvider";

    public static final String AUTHORITY = "com.oppo.gallery3d.open.provider";
    public static final Uri BASE_URI = Uri.parse("content://" + AUTHORITY);

    public static final String SUGGESTION_PROVIDER_AUTHORITY = "com.oplus.gallery.searchpage.provider.searchsuggestionprovider";
    public static final Uri URI_SUGGESTION = Uri.parse("content://" + SUGGESTION_PROVIDER_AUTHORITY);

    public static final String START_INDEX = "startIndex";
    public static final String IS_NEED_VIRTUAL = "isNeedVirtual";
    public static final String IS_VIRTUAL = "isVirtual";

    /**
     * 用来判断在查询图集详情时是否只需要olive的查询参数
     * 此前已开放给小红书使用，更改字符串会影响原来的接口，只更改常量名便于开发理解
     */
    public static final String FILTER_OLIVE = "isOlive";

    public static final String COLUMN_MEDIA_ID = "media_id";
    public static final String COLUMN_PATH = "path";
    public static final String COLUMN_DATE_TAKEN = "datetaken";
    public static final String COLUMN_RESULT_ID = "result_id";
    public static final String COLUMN_ALBUM_TYPE = "album_type";
    public static final String COLUMN_TYPE = "type";
    public static final String COLUMN_NAME = "name";
    public static final String COLUMN_EXTRA_NAMES = "extra_names";
    public static final String COLUMN_ID_LIST = "id_list";
    public static final String COLUMN_COVER_ID = "cover_id";
    public static final String COLUMN_COVER_URI = "cover_Uri";
    public static final String COLUMN_COUNT = "count";
    public static final String COLUMN_KEY = "key";
    public static final String COLUMN_IS_HIDDEN = "is_hidden";
    public static final String COLUMN_IS_CSHOT = "is_cshot";
    public static final String COLUMN_ALBUM_TAG = "albumTag";

    public static final String COLUMN_MEMORIES_SUB_TITLE = "memory_sub_title";
    public static final String COLUMN_MEMORIES_ID = "memory_id";
    public static final String COLUMN_SCENE_ID = "scene_id";
    public static final String COLUMN_SIZE = "size";
    public static final String COLUMN_WIDTH = "width";
    public static final String COLUMN_HEIGHT = "height";
    public static final String COLUMN_MIME_TYPE = "mime_type";
    public static final String COLUMN_DATE_ADD = "date_added";
    public static final String COLUMN_DISPLAY_NAME = "display_name";
    public static final String COLUMN_ADDRESS = "address";
    public static final String COLUMN_BUCKET_DISPLAY_NAME = "bucket_display_name";
    public static final String COLUMN_BUCKET_NAME = "bucket_name";
    public static final String COLUMN_VOLUME_NAME = "volume_name";
    public static final String COLUMN_URI = "uri";
    public static final String COLUMN_DESCRIPTION = "description";
    public static final String COLUMN_OCR = "ocr";
    public static final String COLUMN_TAGS = "tags";

    public static final String COLUMN_ITEM_OPEN_DATA = "_data";
    public static final String COLUMN_ITEM_OPEN_DISPLAY_NAME = "_display_name";
    public static final String COLUMN_ITEM_OPEN_SIZE = "_size";

    public static final String COLUMN_ITEM_ID = "_id";
    public static final String COLUMN_ITEM_DATA = "_data";
    public static final String COLUMN_ITEM_MIME = "mime_type";
    public static final String COLUMN_ITEM_DATETAKEN = "datetaken";
    public static final String COLUMN_ITEM_MODIFIED = "date_modified";
    public static final String COLUMN_ITEM_DURATION = "duration";

    public static final String CATEGORY_ID = "categoryID";
    public static final String CATEGORY_NAME = "categoryName";
    public static final String CATEGORY_CAPACITY = "categoryCapacity";

    public static final String IS_SUPPORT = "isSupport";
    /**
     * 美学评分
     */
    public static final String COLUMN_SENIOR_SCORE = SeniorMediaColumns.SENIOR_SCORE;

    public static final String MEMORY_ID = "memoryId";
    public static final String SET_PATH = "set_path";
    public static final String ALBUM_TYPE = "album_type";
    public static final String ALBUM_TYPES = "albumTypes";
    public static final String SEARCH_RESULT_TYPE = "search_result_type";
    public static final String IGNORE_SEARCH_RECYCLE = "IgnoreSearchRecycle";

    public static final String QUERY_RECOMMEND_ALL = "recommend/all";
    public static final String QUERY_RECOMMEND_DATE = "recommend/time/date";
    public static final String QUERY_RECOMMEND_YEAR = "recommend/time/year";
    public static final String QUERY_RECOMMEND_FESTIVAL = "recommend/time/festival";
    public static final String QUERY_RECOMMEND_MONTH = "recommend/time/month";
    public static final String QUERY_RECOMMEND_LOCATION = "recommend/location";
    public static final String QUERY_RECOMMEND_PERSON = "recommend/person";
    public static final String QUERY_RECOMMEND_LABEL = "recommend/label";
    public static final String QUERY_RECOMMEND_MEMORIES = "recommend/memories";
    public static final String QUERY_RECOMMEND_RECENTLY_ADDED = "recommend/recently/added";

    public static final String RECOMMEND_YEAR = "RecommendYear";
    public static final String RECOMMEND_FESTIVAL = "RecommendFestival";
    public static final String RECOMMEND_MONTH = "RecommendMonth";
    public static final String RECOMMEND_LOCATION = "RecommendLocation";
    public static final String RECOMMEND_PERSON = "RecommendPerson";
    public static final String RECOMMEND_LABEL = "RecommendLabel";
    public static final String RECOMMEND_MEMORIES = "RecommendMemories";

    public static final String VIRTUAL_ALL_PIC = "VirtualAllPicture";
    public static final String VIRTUAL_FAVORIES = "VirtualFavorites";
    public static final String VIRTUAL_VIDEO = "VirtualVideo";
    public static final String VIRTUAL_GIF = "VirtualGif";
    public static final String VIRTUAL_CSHOT = "VirtualCshot";
    public static final String VIRTUAL_PANORAMA = "VirtualPanorama";
    public static final String VIRTUAL_FASTVIDEO = "VirtualFastVideo";
    public static final String VIRTUAL_SLOW_MOTION = "VirtualSlowMotion";
    public static final String VIRTUAL_PORTRAIT_BLUR = "VirtualPortraitBlur";
    public static final String VIRTUAL_OLIVE = "VirtualOlive";

    public static final String TAG_TIMELINE_DATA = "TimelineCount";
    public static final String TAG_RECYCLE_FILES = "RecycleFiles";
    public static final String TAG_LOCKED_PICTURES = "LockedPictures";
    public static final String TAG_SELL_MODE = "SellMode";
    public static final String TAG_FAST_CAPTURES = "FastCaptures";
    public static final String TAG_GET_THUMBNAIL = "GetThumbnail";

    public static final String TAG_SEARCH_INFO = "SearchInfo";
    public static final String TAG_SEARCH_INFO_VERSION = "SearchInfoVersion";

    public static final String ALBUM_TAG_OCR = "ocr";
    public static final String ALBUM_TAG_FAVORITE = "favorite";
    public static final String ALBUM_TAG_CROPAREA = "cropArea";
    public static final String ALBUM_TAG_CROPTYPE = "cropType";
    public static final String ALBUM_TAG_LEFT = "left";
    public static final String ALBUM_TAG_TOP = "top";
    public static final String ALBUM_TAG_RIGHT = "right";
    public static final String ALBUM_TAG_BOTTOM = "bottom";
    public static final String ALBUM_TAG_COVERTYPE = "coverType";

    public static final String STRING_TRUE = "true";
    public static final String STRING_FALSE = "false";

    // 提供给三方判断相册是否有推荐数据（年月节日、地点、人物、标签、回忆）
    public static final String IS_SMART_GALLERY_AVAILABLE = "isSmartGalleryAvailable";
    // 提供给三方获取相册缩略图
    public static final String GET_THUMBNAIL = "getThumbnail";
    // 提供给实现ocs接口的应用获取OpenCapability服务，调用相关能力
    public static final String GET_OPEN_CAPABILITY_SERVICE = "getOpenAbilityService";
    // 提供给能力开放平台(OpenCapabilityService)，向相册写入鉴权结果
    public static final String OCS_WRITE_PERMIT = "OcsWritePermit";
    // 提供给中子搜索引擎，获取相册资源版本列表
    public static final String GET_RESOURCE_VERSION_LIST = "getResourceVersionList";
    /**
     * 查询图片标签id、美学评分
     */
    public static final String GET_LABEL_IDS_SENIOR_SCORE = "getLabelIdsAndSeniorScore";

    /**
     * 从指定的Uris列表中过滤出所有Olive图
     */
    public static final String FILTER_OLIVE_PICTURES = "filterOlivePictures";

    /**
     * 获取删除保护目录，给媒体库提供，用以决定拦截哪些目录下的图片/视频删除
     */
    public static final String GET_PROTECTED_DIRS = "getProtectedDirs";

    /**
     * 获取受信App的包名，给媒体库提供，媒体库不会拦截受信应用的删除
     */
    public static final String GET_TRUSTED_PACKAGE_NAMES = "getTrustedPackageNames";

    /**
     * 插入相册回收站，媒体库会调用，把数据插入到相册回收站
     */
    public static final String INSERT_RECYCLE_BIN = "insertRecycleBin";

    public static final String FROM_OCS = "from_ocs";

    public static final String FORCE_QUERY = "force";
    public static final String QUERY_RECOMMEND_YEAR_FORCE = "recommend/time/year?force=";
    public static final String QUERY_RECOMMEND_MONTH_FORCE = "recommend/time/month?force=";
    public static final String QUERY_RECOMMEND_FESTIVAL_FORCE = "recommend/time/festival?force=";
    public static final String QUERY_RECOMMEND_LOCATION_FORCE = "recommend/location?force=";
    public static final String QUERY_RECOMMEND_PERSON_FORCE = "recommend/person?force=";
    public static final String QUERY_RECOMMEND_LABEL_FORCE = "recommend/label?force=";
    public static final String QUERY_RECOMMEND_MEMORIES_FORCE = "recommend/memories?force=";

    public static final int ALBUM_CROP_TYPE = 1;
    public static final int TYPE_RECOMMEND_ALL = 0xFFF;
    public static final int TYPE_RECOMMEND_DATA = 0x0E0;
    public static final int TYPE_RECOMMEND_PERSON = 0x001;
    public static final int TYPE_RECOMMEND_LOCATION = 0x002;
    public static final int TYPE_RECOMMEND_LABEL = 0x004;
    public static final int TYPE_RECOMMEND_RECENTLY = 0x008;
    public static final int TYPE_RECOMMEND_MEMORIES = 0x010;
    public static final int TYPE_RECOMMEND_MONTH = 0x020;
    public static final int TYPE_RECOMMEND_YEAR = 0x040;
    public static final int TYPE_RECOMMEND_FESTIVAL = 0x080;
    protected static final int INVALID_COUNT = -1;
    protected static final int INVALID_START_INDEX = -1;
    private static final UriMatcher URI_MATCHER = new UriMatcher(UriMatcher.NO_MATCH);
    private static final int SEARCH_ALBUMS = 1;
    private static final int SEARCH_ALBUM_INFO = SEARCH_ALBUMS + 1;
    private static final int RECOMMEND_ALBUMS = SEARCH_ALBUM_INFO + 1;
    private static final int GET_ALBUMS = RECOMMEND_ALBUMS + 1;
    private static final int GET_FAVORITE_ALBUMS = GET_ALBUMS + 1;
    private static final int GET_SUPPORT_CATEGORIES = GET_FAVORITE_ALBUMS + 1;
    private static final int VIRTUAL_ALBUMS = GET_SUPPORT_CATEGORIES + 1;
    private static final int TIMELINE_DATA = VIRTUAL_ALBUMS + 1;
    private static final int LOCKED_PICTURES = TIMELINE_DATA + 1;
    private static final int RECYCLE_FILES = LOCKED_PICTURES + 1;
    private static final int FAST_CAPTURES = RECYCLE_FILES + 1;
    private static final int SHARE = FAST_CAPTURES + 1;
    private static final int SEARCH_INFO = SHARE + 1;
    private static final int SUPPORT_QUERY = SEARCH_INFO + 1;
    private static final int SEARCH_ALBUM_DETAIL = SUPPORT_QUERY + 1;
    private static final int AI_SEARCH = SEARCH_ALBUM_DETAIL + 1;
    private static final int TYPE_ALL = 0;
    private static final int TYPE_IMAGE = 1;
    private static final int TYPE_VIDEO = 2;

    /**
     * searchAlbumsDetail查询接口中传参：查询结果的最大行数
     */
    public static final String SEARCH_ALBUM_DETAIL_RESULT_ROW_LIMIT = "row_limit";

    /**
     * searchAlbumsDetail接口返回的cursor的最大行数
     */
    private static final int SEARCH_ALBUM_DETAIL_RESULT_MAX_ROW = 12;

    /**
     * [LABEL_SENIOR_SCORE]传入的bundle[imageUri]
     */
    private static final String IMAGE_URI = "imageUri";

    /**
     * 部分权限开启下，用户选择的图片Uri列表
     */
    private static final String USER_SELECT_URIS = "userSelectUris";

    /**
     * 打包到bundle返回的是olive的图
     */
    private static final String OLIVE_URIS = "oliveUris";

    /**
     * 查询收藏图集
     */
    private static final String QUERY_FAVORITES_ALBUM_PATH = "getFavoritesAlbum";

    /**
     * 查询图集
     */
    private static final String QUERY_ALBUMS_PATH = "getAlbums";

    /**
     * 搜索图集
     */
    private static final String SEARCH_ALBUMS_PATH = "searchAlbums";

    /**
     * 查询图集详情
     */
    private static final String QUERY_ALBUM_DETAIL_PATH = "albumInfo";

    /**
     * 查询推荐图集
     */
    private static final String QUERY_RECOMMEND_ALBUMS_PATH = "recommendAlbums";

    /**
     * 查询support category
     */
    private static final String QUERY_SUPPORT_CATEGORIES_PATH = "supportCategories";

    /**
     * 查询虚拟图集
     */
    private static final String QUERY_VIRTUAL_ALBUMS_PATH = "virtualAlbums";

    /**
     * 时间轴数据
     */
    private static final String QUERY_TIMELINE_PATH = "timeline";

    /**
     * 锁屏图片
     */
    private static final String QUERY_LOCKED_PICTURES_PATH = "locked_pictures";

    /**
     * 查询回收站文件
     */
    private static final String QUERY_RECYCLE_FILES_PATH = "recyclefiles";

    /**
     * 查询快照
     */
    private static final String QUERY_FAST_CAPTURES_PATH = "fast_captures";

    /**
     * 查询共享
     */
    private static final String QUERY_SHARE_PATH = "share/*";

    /**
     * 搜索
     */
    private static final String QUERY_SEARCH_INFO_PATH = "searchInfo";

    /**
     * 是否支持查询
     */
    private static final String QUERY_SUPPORT_QUERY = "supportQuery";

    /**
     * 外部（小布助手）直接通过搜索获取目标图集详情
     */
    private static final String QUERY_SEARCH_ALBUMS_DETAIL = "searchAlbumsDetail";

    /**
     * aisearch接口，记忆大师调用
     */
    private static final String QUERY_AI_SEARCH = "aisearch";

    /**
     * aisearch接口，查询最大梳理限制
     */
    private static final int AI_SEARCH_ALBUM_DETAIL_RESULT_MAX_ROW = -1;

    private static final String ERROR_CODE = "error_code";
    private static final String ERROR_MSG = "error_msg";
    private static final String ERROR_CODE_NO_PERMISSION = "1";
    private static final String ERROR_MSG_NO_QUERY_PERMISSION = "no query permission";

    private static final String[] ITEM_PROJECTION = new String[]{
            COLUMN_MEDIA_ID,
            COLUMN_PATH,
            COLUMN_DATE_TAKEN,
    };

    private static final String[] MEMORIES_ITEM_PROJECTION = new String[]{
            COLUMN_MEDIA_ID,
            COLUMN_PATH,
            COLUMN_DATE_TAKEN,
            COLUMN_SIZE,
            COLUMN_WIDTH,
            COLUMN_HEIGHT,
            COLUMN_MIME_TYPE,
            COLUMN_DATE_ADD,
            COLUMN_DISPLAY_NAME,
    };

    private static final String[] LOCAL_ITEM_PROJECTION = new String[]{
            COLUMN_MEDIA_ID,
            COLUMN_PATH,
            COLUMN_DATE_TAKEN,
            COLUMN_IS_CSHOT,
    };

    private static final String[] FAVORITE_ITEM_PROJECTION = new String[]{
            COLUMN_MEDIA_ID,
            COLUMN_PATH,
            COLUMN_NAME,
            COLUMN_DATE_TAKEN,
    };

    private static final String[] CATEGORY_PROJECTION = new String[]{
            CATEGORY_ID,
            CATEGORY_NAME,
            CATEGORY_CAPACITY,
    };

    private static final String[] SUPPORT_QUERY_PROJECTION = new String[]{
            IS_SUPPORT
    };

    private static final String[] GROUP_PROJECTION = new String[]{
            COLUMN_RESULT_ID,
            COLUMN_TYPE,
            COLUMN_ALBUM_TYPE,
            COLUMN_NAME,
            COLUMN_ID_LIST,
            COLUMN_COVER_ID,
            COLUMN_COVER_URI,
            COLUMN_COUNT,
    };

    private static final String[] RECOMMEND_COLUMN_GROUP = new String[]{
            COLUMN_TYPE,
            COLUMN_NAME,
            COLUMN_KEY,
            COLUMN_COVER_ID,
            COLUMN_COVER_URI,
            COLUMN_COUNT,
            COLUMN_ALBUM_TAG,
            COLUMN_MEMORIES_SUB_TITLE,
            COLUMN_MEMORIES_ID,
            COLUMN_SCENE_ID
    };

    private static final String[] COLUMN_GROUP = new String[]{
            COLUMN_TYPE,
            COLUMN_NAME,
            COLUMN_KEY,
            COLUMN_COVER_ID,
            COLUMN_COVER_URI,
            COLUMN_COUNT,
            COLUMN_ALBUM_TAG
    };

    private static final String[] LOCAL_COLUMN_GROUP = new String[]{
            COLUMN_TYPE,
            COLUMN_NAME,
            COLUMN_KEY,
            COLUMN_COVER_ID,
            COLUMN_COVER_URI,
            COLUMN_COUNT,
            COLUMN_IS_HIDDEN,
    };

    private static final String[] ALBUM_ITEM_COLUMN = new String[]{
            COLUMN_ITEM_ID,
            COLUMN_ITEM_DATA,
            COLUMN_ITEM_MIME,
            COLUMN_ITEM_DATETAKEN,
            COLUMN_ITEM_MODIFIED,
            COLUMN_ITEM_DURATION,
            COLUMN_WIDTH,
            COLUMN_HEIGHT
    };

    private static final String[] TIMELINE_COUNT_COLUMN = new String[]{
            LocalColumns.MEDIA_TYPE,
            LocalColumns.SIZE
    };

    private static final String[] RECYCLE_FILES_COLUMN = new String[]{
            LocalColumns.MEDIA_TYPE,
            LocalColumns.DATA, // recycle table is _recycle_data
            LocalColumns.SIZE,
            LocalColumns.DATE_ADDED,
            LocalColumns.TITLE,
            LocalColumns.DURATION

    };

    private static final String[] OPEN_SHARE_ITEM_COLUMN = new String[]{
            COLUMN_ITEM_OPEN_DATA,
            COLUMN_ITEM_OPEN_DISPLAY_NAME,
            COLUMN_ITEM_OPEN_SIZE
    };

    private static final String[] AI_SEARCH_CURSOR = new String[]{
            COLUMN_MEDIA_ID,
            COLUMN_URI,
            COLUMN_NAME,
            COLUMN_DATE_TAKEN,
            COLUMN_ADDRESS,
            COLUMN_MIME_TYPE,
            COLUMN_PATH,
            COLUMN_BUCKET_DISPLAY_NAME,
            COLUMN_DESCRIPTION,
            COLUMN_OCR,
            COLUMN_TAGS,
            COLUMN_TYPE
    };

    private static final String[] AI_SEARCH_DMP_PROJECTION = new String[]{
            COLUMN_ITEM_ID,
            COLUMN_ITEM_OPEN_DISPLAY_NAME,
            COLUMN_DATE_TAKEN,
            COLUMN_ADDRESS,
            COLUMN_MIME_TYPE,
            COLUMN_PATH,
            COLUMN_BUCKET_NAME,
            COLUMN_OCR,
            COLUMN_TAGS
    };

    private static final String[] AI_SEARCH_LOCAL_MEDIA_PROJECTION = new String[]{
            COLUMN_ITEM_ID,
            COLUMN_MEDIA_ID,
            COLUMN_VOLUME_NAME,
            COLUMN_DESCRIPTION,
    };

    private static final int INDEX_NOT_FOUND = -1;

    static {
        URI_MATCHER.addURI(AUTHORITY, QUERY_FAVORITES_ALBUM_PATH, GET_FAVORITE_ALBUMS);
        URI_MATCHER.addURI(AUTHORITY, QUERY_ALBUMS_PATH, GET_ALBUMS);
        URI_MATCHER.addURI(AUTHORITY, SEARCH_ALBUMS_PATH, SEARCH_ALBUMS);
        URI_MATCHER.addURI(AUTHORITY, QUERY_ALBUM_DETAIL_PATH, SEARCH_ALBUM_INFO);
        URI_MATCHER.addURI(AUTHORITY, QUERY_RECOMMEND_ALBUMS_PATH, RECOMMEND_ALBUMS);
        URI_MATCHER.addURI(AUTHORITY, QUERY_SUPPORT_CATEGORIES_PATH, GET_SUPPORT_CATEGORIES);
        URI_MATCHER.addURI(AUTHORITY, QUERY_VIRTUAL_ALBUMS_PATH, VIRTUAL_ALBUMS);
        URI_MATCHER.addURI(AUTHORITY, QUERY_TIMELINE_PATH, TIMELINE_DATA);
        URI_MATCHER.addURI(AUTHORITY, QUERY_LOCKED_PICTURES_PATH, LOCKED_PICTURES);
        URI_MATCHER.addURI(AUTHORITY, QUERY_RECYCLE_FILES_PATH, RECYCLE_FILES);
        URI_MATCHER.addURI(AUTHORITY, QUERY_FAST_CAPTURES_PATH, FAST_CAPTURES);
        URI_MATCHER.addURI(AUTHORITY, QUERY_SHARE_PATH, SHARE);
        URI_MATCHER.addURI(AUTHORITY, QUERY_SEARCH_INFO_PATH, SEARCH_INFO);
        URI_MATCHER.addURI(AUTHORITY, QUERY_SUPPORT_QUERY, SUPPORT_QUERY);
        URI_MATCHER.addURI(AUTHORITY, QUERY_SEARCH_ALBUMS_DETAIL, SEARCH_ALBUM_DETAIL);
        URI_MATCHER.addURI(AUTHORITY, QUERY_AI_SEARCH, AI_SEARCH);
    }

    /**
     * 初始化能力开放配置的lock
     */
    private final Object mOpenListConfigFutureInitLock = new Object();

    /**
     * 初始化能力开放配置，这是外部访问能力开放的门禁条件
     */
    private volatile Deferred<Boolean> mOpenListConfigFuture = null;

    /**
     * 能力开放配置信息
     */
    private GalleryOpenListConfig mOpenListConfig = null;

    /**
     * 融合搜索的融合搜索结果信息
     */
    private ArrayList<CompositeData> mCompositeDataList = new ArrayList<>();

    private Deferred<Boolean> getFuture() {
        if (mOpenListConfigFuture == null) {
            synchronized (mOpenListConfigFutureInitLock) {
                if (mOpenListConfigFuture == null) {
                    mOpenListConfigFuture = BuildersKt.async(
                            AppScope.INSTANCE,
                            (CoroutineContext) Dispatchers.getIO(),
                            CoroutineStart.DEFAULT,
                            (Function2<CoroutineScope, Continuation<? super Boolean>, Boolean>) (coroutineScope, continuation) -> {
                                GTrace.traceBegin("GalleryOpenProvider.getFuture");
                                mOpenListConfig = GalleryOpenListConfig.getInstance();
                                OpenAbilityCallManager.getOcsAuthResultFromSP();
                                GTrace.traceEnd();
                                return true;
                            }
                    );
                }
            }
        }
        return mOpenListConfigFuture;
    }

    @Override
    public boolean onCreate() {
        GStartup.doOnCPUIdle((taskCollector, entrance) -> getFuture());
        return true;
    }

    public static FaceInfo getFaceInfo(Context context, long id) {
        Cursor cs = null;
        FaceInfo faceInfo = null;
        try {
            StringBuilder builder = new StringBuilder();
            builder.append("SELECT ");
            builder.append(GalleryStore.ScanFace.TAB + "." + _ID + ", ");
            builder.append(LocalColumns.MEDIA_ID + ", ");
            builder.append(GROUP_NAME + ", ");
            builder.append(LEFT + ", ");
            builder.append(TOP + ", ");
            builder.append(RIGHT + ", ");
            builder.append(BOTTOM + ", ");
            builder.append(THUMB_W + ", ");
            builder.append(THUMB_H + ", ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.MEDIA_TYPE + " FROM ");
            builder.append(GalleryStore.ScanFace.TAB + " INNER JOIN " + GalleryStore.GalleryMedia.TAB + " ON ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATA + "=" + GalleryStore.ScanFace.TAB + "." + LocalColumns.DATA);
            builder.append(" WHERE ");
            builder.append(GalleryStore.ScanFace.TAB + "." + _ID + "=").append(id);
            builder.append(AND);
            builder.append(DatabaseUtils.getOnlyLocalWhere());
            RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setQuerySql(builder.toString())
                    .setConvert(new CursorConvert())
                    .build();
            cs = DataAccess.getAccess().rawQuery(rawQueryReq);
            if (cs != null) {
                int indexMediaId = cs.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexName = cs.getColumnIndex(GROUP_NAME);
                int indexLeft = cs.getColumnIndex(LEFT);
                int indexTop = cs.getColumnIndex(TOP);
                int indexRight = cs.getColumnIndex(RIGHT);
                int indexBottom = cs.getColumnIndex(BOTTOM);
                int indexWidth = cs.getColumnIndex(THUMB_W);
                int indexHeight = cs.getColumnIndex(THUMB_H);
                int indexMediaType = cs.getColumnIndex(LocalColumns.MEDIA_TYPE);
                if (cs.moveToNext()) {
                    String name = cs.getString(indexName);
                    long mediaId = cs.getLong(indexMediaId);
                    float left = cs.getFloat(indexLeft);
                    float top = cs.getFloat(indexTop);
                    float right = cs.getFloat(indexRight);
                    float bottom = cs.getFloat(indexBottom);
                    float width = cs.getFloat(indexWidth);
                    float height = cs.getFloat(indexHeight);
                    int mediaType = cs.getInt(indexMediaType);
                    faceInfo = new FaceInfo(name, mediaId, left, top, right, bottom, width, height, mediaType);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getFaceInfo exception:", e);
        } finally {
            IOUtils.closeQuietly(cs);
        }
        return faceInfo;
    }

    // FIXME: 2020/6/18 wangrunxin 后续重构时，将SmartOpenProvider与GalleryOpenProvider解耦之后，
    //  可直接调用getCallingPackage方法
    // 不再允许捕获SecurityException
    private String getCallPackage() {
        String callPackage = null;
        try {
            callPackage = getCallingPackage();
        } catch (SecurityException e) {
            callPackage = Objects.requireNonNull(getContext()).getPackageManager().getNameForUid(Binder.getCallingUid());
            GLog.w(TAG, "getCallingPackage e:" + e);
        }
        return callPackage;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        long token = Binder.clearCallingIdentity();
        try {
            return getMimeType(uri);
        } finally {
            Binder.restoreCallingIdentity(token);
        }
    }

    /**
     * 获取mimeType
     * @param uri Uri
     * @return mimeType
     */
    @SuppressLint("getLastPathSegmentRisk")
    public static String getMimeType(@NonNull Uri uri) {
        Path path = Path.fromString(uri.getPath());
        MediaItem item = (MediaItem) DataManager.getMediaObject(path);
        if (item != null) {
            String mimeType = item.getMimeType();
            return mimeType;
        }
        String lastPathSegment = uri.getLastPathSegment();
        if (lastPathSegment != null) {
            final File file = new File(lastPathSegment);
            if (file.exists() && file.isFile()) {
                String mimeType = MimeTypeUtils.getMimeType(file.getAbsolutePath(), null);
                GLog.d(TAG, LogFlag.DL, "getMimeType, " + mimeType + " by file suffix");
                return mimeType;
            }
        }
        return null;
    }

    private Bundle matchMethod(@NonNull String method, String arg, Bundle extras, PackageBean packageBean, boolean isPermit) {
        switch (method) {
            case IS_SMART_GALLERY_AVAILABLE:
                return OpenAbilityCallManager.isSmartGalleryAvailable(packageBean);
            case OCS_WRITE_PERMIT:
                return OpenAbilityCallManager.ocsWritePermit(extras, packageBean, isPermit);
            case GET_RESOURCE_VERSION_LIST:
                return OpenAbilityCallManager.getResourceVersionList(packageBean);
            case GET_LABEL_IDS_SENIOR_SCORE:
                return queryLabelIdsAndSeniorScore(extras);
            case FILTER_OLIVE_PICTURES:
                return filterOlivePictures(extras);
            case OPEN_CLOUD_SYNC:
                return OpenAbilityCallManager.enableAlbumSyncSwitch(true);
            case CLOSE_CLOUD_SYNC:
                return OpenAbilityCallManager.enableAlbumSyncSwitch(false);
            case OPEN_CLOUD_SYNC_SETTING:
                return OpenAbilityCallManager.jumpToAlbumCloudSyncSettingPage();
            case OPEN_AIGC_ELIMINATE:
            case ELIMINATE_PASSERBY:
            case OPEN_GROUP_PHOTO:
            case FACE_RESOLUTION:
            case CLOSE_EYES_REPAIR:
            case IMAGE_DEBLURING:
            case IMAGE_DEREFLECTIVE:
            case IMAGE_ENHANCE:
            case IMAGE_COMPOSITION:
                return OpenAbilityCallManager.checkPhotoPageAndCallAIGCFunc(method, arg, extras);
            case GET_PROTECTED_DIRS:
                return DeleteProtectHelper.getProtectedDirsBundle();
            case GET_TRUSTED_PACKAGE_NAMES:
                return DeleteProtectHelper.getTrustedPackageNamesBundle();
            case INSERT_RECYCLE_BIN:
                return DeleteProtectHelper.insertRecycleBin(extras);
            case GET_THUMBNAIL:
                return getThumbnailByBundle(packageBean, extras);
            // 是否有水印
            case WaterMarkUriMatcher.METHOD_HAS_WATERMARK:
                return new WatermarkOperator().hasWatermark(extras);
            // 删除水印
            case WaterMarkUriMatcher.METHOD_REMOVE_WATERMARK:
                return new WatermarkOperator().removeWatermark(extras);
            case GET_CURRENT_SHOW_PHOTO_URI:
                return OpenAbilityCallManager.getCurrentShowPhotoUri(method);
            case GET_PHOTO_SHOW_STATE:
                return OpenAbilityCallManager.getPhotoShowState(method);
            default:
                GLog.d(TAG, "matchMethod default method:" + method);
                return super.call(method, arg, extras);
        }
    }

    private String createAlbumTagJson(FaceInfo faceInfo) {
        JSONObject albumTagJson = new JSONObject();
        try {
            JSONObject cropArea = new JSONObject();
            if (faceInfo == null) {
                return albumTagJson.toString();
            }
            if (faceInfo.mMediaType == LocalColumns.MEDIA_TYPE_IMAGE) {
                cropArea.put(ALBUM_TAG_CROPTYPE, ALBUM_CROP_TYPE);
                if ((faceInfo.getWidth() <= 0) || (faceInfo.getHeight() <= 0)) {
                    return albumTagJson.toString();
                }
                Rect rect = CoverCachedHelper.getScaleRect(new Rect((int) faceInfo.getLeft(), (int) faceInfo.getTop(),
                                (int) faceInfo.getRight(), (int) faceInfo.getBottom()), CoverCachedHelper.LEFT_PADDING_SCALE,
                        CoverCachedHelper.TOP_PADDING_SCALE, CoverCachedHelper.RIGHT_PADDING_SCALE, CoverCachedHelper.BOTTOM_PADDING_SCALE,
                        (int) faceInfo.getWidth(), (int) faceInfo.getHeight());
                cropArea.put(ALBUM_TAG_LEFT, (rect.left / faceInfo.getWidth()));
                cropArea.put(ALBUM_TAG_TOP, (rect.top / faceInfo.getHeight()));
                cropArea.put(ALBUM_TAG_RIGHT, (rect.right / faceInfo.getWidth()));
                cropArea.put(ALBUM_TAG_BOTTOM, (rect.bottom / faceInfo.getHeight()));
                albumTagJson.put(ALBUM_TAG_COVERTYPE, faceInfo.mMediaType);
            } else if (faceInfo.mMediaType == LocalColumns.MEDIA_TYPE_VIDEO) {
                cropArea.put(ALBUM_TAG_CROPTYPE, 0);
                albumTagJson.put(ALBUM_TAG_COVERTYPE, faceInfo.mMediaType);
            }
            albumTagJson.put(ALBUM_TAG_CROPAREA, cropArea);
        } catch (JSONException e) {
            GLog.e(TAG, "createAlbumTagJson", e);
        }
        return albumTagJson.toString();
    }

    // fix bug[161349]
    private boolean noNeedDetectPermission(int match) {
        return ((match == LOCKED_PICTURES) || (match == FAST_CAPTURES) || (match == SHARE));
    }

    @SuppressLint("getLastPathSegmentRisk")
    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection, @Nullable String[] selectionArgs,
                        @Nullable String sortOrder) {
        GLog.d(TAG, "query, uriInfo: " + uri + " " + uri.getPath() + " " + uri.getQuery() + " " + uri.getAuthority() + " " + uri.getHost() + " "
                + uri.getScheme() + " " + uri.getLastPathSegment() + " getPathSegments=" + uri.getPathSegments()
                + " selection=" + selection + " selectionArgs=" + Arrays.toString(selectionArgs) + " projection=" + Arrays.toString(projection));
        int match = URI_MATCHER.match(uri);
        String callPackage = getCallPackage();
        if ((match != SHARE) && !checkReadExternalPermission(getContext(), callPackage)) {
            return null;
        }
        long getRequestTime = System.currentTimeMillis();
        SearchTrackHelper.providerCallPackage = callPackage;
        long token = Binder.clearCallingIdentity();
        try {
            String queryArgs = joinQueryArgs(selectionArgs);
            boolean isFromOCS = getIsOCSFromArgs(queryArgs);
            PackageBean packageBean = null;
            if (isFromOCS) {
//                来自ocs的已经鉴权了，直接获取pakageBean
                packageBean = getOCSPackageBean(queryArgs);
            } else {
                packageBean = checkQueryPermission(callPackage, Binder.getCallingUid(), match);
            }
            if (match != SHARE && packageBean == null) {
                return null;
            }
            switch (match) {
                case SEARCH_ALBUMS:
                    return querySearchAlbums(SEARCH_ALBUMS_PATH, projection, selection, selectionArgs, sortOrder, packageBean);
                case SEARCH_ALBUM_INFO:
                    return queryAlbumInfo(QUERY_ALBUM_DETAIL_PATH, selectionArgs); // 获取相册详情
                case RECOMMEND_ALBUMS:
                    return queryRecommendAlbums(QUERY_RECOMMEND_ALBUMS_PATH, selectionArgs, packageBean);
                case GET_ALBUMS:
                    return queryLocalEntrySetItem(selectionArgs);
                case GET_FAVORITE_ALBUMS:
                    return queryFavoriteAlbums();
                case GET_SUPPORT_CATEGORIES:
                    return queryCategories();
                case VIRTUAL_ALBUMS:
                    return queryVirtualAlbumSet(QUERY_VIRTUAL_ALBUMS_PATH, selectionArgs, packageBean);
                case TIMELINE_DATA:
                    if ((packageBean.getTags() != null) && packageBean.getTags().contains(TAG_TIMELINE_DATA)) {
                        return queryTimelineCount(projection);
                    }
                    return null;
                case LOCKED_PICTURES:
                    if ((packageBean.getTags() != null) && packageBean.getTags().contains(TAG_LOCKED_PICTURES)) {
                        return LockedPicHelper.query(projection, selection, selectionArgs, sortOrder);
                    }
                    return null;
                case RECYCLE_FILES:
                    if ((packageBean.getTags() != null) && packageBean.getTags().contains(TAG_RECYCLE_FILES)) {
                        return queryRecycleFiles(projection, selection, selectionArgs, sortOrder);
                    }
                    return null;
                case FAST_CAPTURES:
                    if ((packageBean.getTags() != null) && packageBean.getTags().contains(TAG_FAST_CAPTURES)) {
                        return FastCaptureHelper.query(projection, selection, selectionArgs, sortOrder);
                    }
                    return null;
                case SHARE:
                    return getShareCursor(uri, projection);
                case SUPPORT_QUERY:
                    return querySupportQuery();
                case SEARCH_ALBUM_DETAIL:
                    return querySearchAlbumDetail(projection, selection, selectionArgs, sortOrder, packageBean);
                case AI_SEARCH:
                    return queryAiSearch(projection, selection, selectionArgs, sortOrder, packageBean);
                default:
                    break;
            }
        } catch (Exception e) {
            GLog.e(TAG, "query exception", e);
        } finally {
            GLog.d(TAG, String.format(Locale.ENGLISH, "time for this query is %d ms", GLog.getTime(getRequestTime)));
            Binder.restoreCallingIdentity(token);
        }
        GLog.w(TAG, "query, there is no such path");
        return null;
    }

    @SuppressLint("getLastPathSegmentRisk")
    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable Bundle queryArgs,
                        @Nullable CancellationSignal cancellationSignal) {
        long getRequestTime = System.currentTimeMillis();
        GLog.d(TAG, "query, uriInfo: " + uri + " " + uri.getPath() + " " + uri.getQuery() + " " + uri.getAuthority() + " "
                + uri.getHost() + " " + uri.getScheme() + " " + uri.getLastPathSegment() + " getPathSegments=" + uri.getPathSegments());
        String callPackage = getCallPackage();
        int callingUid = Binder.getCallingUid();
        int match = URI_MATCHER.match(uri);
        if ((match != SHARE) && !checkReadExternalPermission(getContext(), callPackage)) {
            return null;
        }
        SearchTrackHelper.providerCallPackage = callPackage;
        long token = Binder.clearCallingIdentity();
        try {
            if (match == SEARCH_INFO) {
                PackageBean packageBean = checkQueryPermission(callPackage, callingUid, match);
                if (packageBean == null) {
                    Cursor cursor = new MatrixCursor(CATEGORY_PROJECTION);
                    Bundle bundle = new Bundle();
                    bundle.putString(ERROR_CODE, ERROR_CODE_NO_PERMISSION);
                    bundle.putString(ERROR_MSG, ERROR_MSG_NO_QUERY_PERMISSION);
                    cursor.setExtras(bundle);
                    return cursor;
                }
                return querySearchInfo(projection, queryArgs, packageBean);
            }
        } catch (Exception e) {
            GLog.e(TAG, "query exception", e);
        } finally {
            long runTime = GLog.getTime(getRequestTime);
            GLog.d(TAG, String.format(Locale.ENGLISH, "time for this query is %d ms", runTime));
            Binder.restoreCallingIdentity(token);
        }
        return super.query(uri, projection, queryArgs, cancellationSignal);
    }

    private PackageBean checkQueryPermission(String callPackage, int callingUid, int match) {
        if (!noNeedDetectPermission(match) && CTAHelper.hasNoPermissionToQuery(getContext(), TAG)) {
            return null;
        }
        if (!DeferredExtKt.getOnBlocking(getFuture())) {
            GLog.d(TAG, "checkQueryPermission, open list config init failed!");
            return null;
        }
        PackageBean packageBean = mOpenListConfig.getAllowedPackageBean(callPackage, callingUid);
        if (packageBean == null) {
            GLog.d(TAG, "checkQueryPermission, callPackage:" + callPackage + " isNotAllow");
            return null;
        }
        GLog.d(TAG, "checkQueryPermission, callPackage:" + callPackage);
        AccessPackageManager.addAccessPackage(callPackage);
        return packageBean;
    }

    /**
     * 该方法为特殊方法
     */
    private boolean isSpecialMethod(String method) {
        //ocs仅仅是通知相册其他应用的鉴权结果，不需要进行鉴权!
        return method.equals(OCS_WRITE_PERMIT) || method.equals(FILTER_OLIVE_PICTURES);
    }

    /**
     * 不需要CTA授权的特殊方法
     * 目前媒体库获取相册删除保护名单和应用白名单，可以直接获取，不需要鉴权
     * @param method
     * @return
     */
    private boolean isSpecialMethodNoNeedDetectPermission(String method) {
        return method.equals(GET_PROTECTED_DIRS) || method.equals(GET_TRUSTED_PACKAGE_NAMES);
    }

    /**
     * 检查调用者是否有读取相册数据的权限
     *
     * @param context     上下文
     * @param callPackage 调用者包名
     * @param extras      Bundle
     * @return 是否有读取相册数据的权限
     */
    public static boolean checkReadPermission(Context context, String callPackage, Bundle extras) {
        return checkReadExternalPermission(context, callPackage) || isAllowAccessUri(context, callPackage, extras);
    }

    /**
     * 三方应用需具备读取存储的权限，否则无法访问相册数据
     */
    public synchronized static boolean checkReadExternalPermission(Context context, String callPackage) {
        if (!TextUtils.isEmpty(callPackage)) {
            if (ApiLevelUtil.isAtLeastAndroidT()
                    && (CTAHelper.checkPermission(context, Manifest.permission.MANAGE_EXTERNAL_STORAGE, callPackage)
                    || CTAHelper.checkPermission(context, Manifest.permission.READ_MEDIA_IMAGES, callPackage)
                    || CTAHelper.checkPermission(context, Manifest.permission.READ_MEDIA_VIDEO, callPackage))) {
                return true;
            } else {
                if (CTAHelper.checkPermission(context, Manifest.permission.MANAGE_EXTERNAL_STORAGE, callPackage)
                        || CTAHelper.checkPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE, callPackage)) {
                    return true;
                }
            }
        }
        GLog.w(TAG, "checkReadExternalPermission, " + callPackage + " has no read external storage permission");
        return false;
    }

    /**
     * 是否允许访问uri
     *
     * @param context     上下文
     * @param callPackage 调用者包名
     * @param extras      Bundle
     * @return 是否允许访问uri
     */
    private static boolean isAllowAccessUri(Context context, String callPackage, Bundle extras) {
        if (extras == null) {
            GLog.w(TAG, "isAllowAccessUri, extras is null, return false");
            return false;
        }
        Uri uri = extras.getParcelable(IMAGE_URI);
        if (uri == null) {
            GLog.w(TAG, "isAllowAccessUri, uri is null, return false");
            return false;
        }
        try {
            Context tmpContext = context.createPackageContext(callPackage, Context.CONTEXT_IGNORE_SECURITY
                    | Context.CONTEXT_INCLUDE_CODE);
            int result = tmpContext.checkCallingUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
            return result == PackageManager.PERMISSION_GRANTED;
        } catch (NameNotFoundException e) {
            GLog.e(TAG, "isAllowAccessUri, checkCallingUriPermission", e);
        }
        return false;
    }

    private Cursor querySearchInfo(String[] projection, Bundle queryArgs, GalleryOpenListConfig.PackageBean packageBean) {
        if ((packageBean.getTags() == null) || (packageBean.getTags().isEmpty())) {
            GLog.d(TAG, "querySearchInfo, packageBean or packageBean.getTags() is null");
            return null;
        }
        if (!packageBean.getTags().contains(TAG_SEARCH_INFO)) {
            GLog.d(TAG, "querySearchInfo, Tags is not contains searchInfo, Tags:" + packageBean.getTags().toString());
            return null;
        }
        Uri uri = Uri.parse("content://" + SUGGESTION_PROVIDER_AUTHORITY + "/searchInfo");
        return ContextGetter.context.getContentResolver().query(uri, projection, queryArgs, null);
    }

    @SuppressLint("ContentProviderCall")
    @Override
    public Bundle call(@NonNull String method, String arg, Bundle extras) {
        if (GProperty.DEBUG) {
            GLog.d(TAG, "call, method: " + method + ", arg:" + arg);
            if (extras != null) {
                GLog.d(TAG, "call," + " extras:" + EncryptUtils.mixPath(extras.toString()));
            }
        }
        if (method.isEmpty()) {
            GLog.d(TAG, "[call] method isEmpty");
            return super.call(method, arg, extras);
        }
        String callPackage = getCallPackage();
        if ((!isSpecialMethodNoNeedDetectPermission(method) && CTAHelper.hasNoPermissionToQuery(getContext(), TAG))
                || (!isSpecialMethod(method) && !checkReadPermission(getContext(), callPackage, extras))) {
            Bundle bundle = new Bundle();
            bundle.putInt(KEY_BREENO_INVOKED_RESULT, FAIL_NO_AUTH);
            return bundle;
        }
        long getRequestTime = System.currentTimeMillis();
        int callingPid = Binder.getCallingPid();
        int callingUid = Binder.getCallingUid();
        SearchTrackHelper.providerCallPackage = callPackage;
        long token = Binder.clearCallingIdentity();
        try {
            GalleryOpenListConfig.PackageBean packageBean = checkPackageBean(callPackage, callingUid);
            //三方应用通过调用ocs接口方法
            if (method.equals(GET_OPEN_CAPABILITY_SERVICE)) {
                return OpenAbilityCallManager.getMethodByBinder(callingPid, callingUid);
            }

            boolean isPermit = CTAHelper.checkPermission(getContext(), AppConstants.Permission.SAFE_AUTHENTICATE_PERMISSION, callPackage);
            if ((packageBean == null) && !isPermit) {
                return null;
            }
            return matchMethod(method, arg, extras, packageBean, isPermit);
        } catch (Exception e) {
            GLog.e(TAG, "GalleryOpenProvider call exception", e);
        } finally {
            long runTime = GLog.getTime(getRequestTime);
            GLog.d(TAG, String.format(Locale.ENGLISH, "[call] time for this call is %d ms", runTime));
            Binder.restoreCallingIdentity(token);
        }
        GLog.d(TAG, "call super method:" + method);
        return super.call(method, arg, extras);
    }

    private GalleryOpenListConfig.PackageBean checkPackageBean(String callPackage, int uid) {
        if (!DeferredExtKt.getOnBlocking(getFuture())) {
            GLog.d(TAG, "[checkPackageBean] open list config init failed!");
            return null;
        }
        GalleryOpenListConfig.PackageBean packageBean = mOpenListConfig.getAllowedPackageBean(callPackage, uid);
        if (packageBean == null) {
            GLog.d(TAG, "[checkPackageBean] callPackage:" + callPackage + " isNotAllow");
            return null;
        }
        GLog.d(TAG, "[checkPackageBean] callPackage:" + callPackage);
        if ((packageBean.getTags() == null)) {
            GLog.d(TAG, "[checkPackageBean] packageBean.getTags() is null");
            return null;
        }
        return packageBean;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        GLog.d(TAG, "insert, uriInfo: " + uri);
        int match = URI_MATCHER.match(uri);
        String callPackage = getCallPackage();
        int callingUid = Binder.getCallingUid();
        long token = Binder.clearCallingIdentity();
        try {
            if (!noNeedDetectPermission(match) && CTAHelper.hasNoPermissionToQuery(getContext(), TAG)) {
                return null;
            }
            if (!DeferredExtKt.getOnBlocking(getFuture())) {
                GLog.d(TAG, "insert, open list config init failed!");
                return null;
            }
            GalleryOpenListConfig.PackageBean packageBean = mOpenListConfig.getAllowedPackageBean(callPackage, callingUid);
            if (packageBean == null) {
                GLog.d(TAG, "insert, callPackage:" + callPackage + " isNotAllow");
                return null;
            }
            if ((match == LOCKED_PICTURES) && (packageBean.getTags() != null) && packageBean.getTags().contains(TAG_LOCKED_PICTURES)) {
                return LockedPicHelper.insert(values);
            } else if ((match == FAST_CAPTURES) && (packageBean.getTags() != null) && packageBean.getTags().contains(TAG_FAST_CAPTURES)) {
                return FastCaptureHelper.insert(values);
            } else {
                throw new UnsupportedOperationException("insert, uri: " + uri + ", callPackage: " + callPackage);
            }
        } finally {
            Binder.restoreCallingIdentity(token);
        }
    }

    private boolean getForceQuery(String keyword) {
        Uri queryUri = Uri.parse(keyword);
        String queryValue = queryUri.getQueryParameter(FORCE_QUERY);
        return "true".equalsIgnoreCase(queryValue);
    }

    private Cursor queryCategories() {
        Resources resources = ContextGetter.context.getResources();
        String titleDate = resources.getString(com.oplus.gallery.basebiz.R.string.common_search_recommend_subtitle_date);
        String titlePerson = resources.getString(com.oplus.gallery.basebiz.R.string.common_search_recommend_subtitle_person);
        String titleLocation = resources.getString(com.oplus.gallery.basebiz.R.string.common_search_recommend_subtitle_location);
        String titleLabel = resources.getString(com.oplus.gallery.basebiz.R.string.label_group_title);
        String titleMemories = resources.getString(com.oplus.gallery.basebiz.R.string.common_tab_memories);
        MatrixCursor cursor = new MatrixCursor(CATEGORY_PROJECTION);
        cursor.addRow(new Object[]{QUERY_RECOMMEND_DATE, titleDate, -1});
        cursor.addRow(new Object[]{QUERY_RECOMMEND_LOCATION, titleLocation, -1});
        cursor.addRow(new Object[]{QUERY_RECOMMEND_PERSON, titlePerson, -1});
        cursor.addRow(new Object[]{QUERY_RECOMMEND_LABEL, titleLabel, -1});
        cursor.addRow(new Object[]{QUERY_RECOMMEND_MEMORIES, titleMemories, -1});
        cursor.moveToPosition(-1);
        return cursor;
    }

    /**
     * 是否支持查询
     *
     * @return cursor
     * | 字段名        |  类型   |        描述        |       备注        |
     * | ------------ | :-----: | :----------------: | :---------------: |
     * | isSupport    | Integer |    是否支持        |                   |
     * <p>
     * 如果直接获取数据，给它们返回null的情况，它们无法区分是相册无数据还是不支持获取
     * 如果是不支持获取，它们将不会继续获取相册数据
     * 如果此接口返回 isSupport = 1，表示版本中已配置SHA1，它们将继续获取数据
     * <p>
     * 这里直接返回isSupport = 1，告诉调用者"已支持"即可
     */
    private Cursor querySupportQuery() {
        MatrixCursor cursor = new MatrixCursor(SUPPORT_QUERY_PROJECTION);
        cursor.addRow(new Object[]{1});
        cursor.moveToPosition(-1);
        return cursor;
    }

    /**
     * 查询图片标签id、美学评分
     * <p>
     * SELECT  group_concat (scan_label.scene_id,'|') AS scene_id,
     * senior_media.senior_score AS senior_score
     * FROM local_media
     * LEFT OUTER JOIN scan_label ON local_media._original_data = scan_label._data
     * LEFT OUTER JOIN senior_media ON local_media._original_data = senior_media._data
     * WHERE local_media.media_id = ?
     * And local_media.is_trashed != 1
     */
    private Bundle queryLabelIdsAndSeniorScore(Bundle extras) {
        if (extras == null) {
            GLog.w(TAG, "queryLabelIdsAndSeniorScore, extras is null, return");
            return null;
        }
        Uri uri = extras.getParcelable(IMAGE_URI);
        if (uri == null) {
            GLog.w(TAG, "queryLabelIdsAndSeniorScore, uri is null, return");
            return null;
        }
        long mediaId = 0;
        try {
            mediaId = ContentUris.parseId(uri);
        } catch (NumberFormatException e) {
            GLog.e(TAG, "queryLabelIdsAndSeniorScore, uri parseId error");
        }
        if (mediaId <= 0) {
            GLog.w(TAG, "queryLabelIdsAndSeniorScore, mediaId error, return");
            return null;
        }

        String commaOrRightBracket = ",'|')";
        String sql = SELECT
                + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.ScanLabel.TAB + DOT + ScanLabelColumns.SCENE_ID + commaOrRightBracket
                + AS + ScanLabelColumns.SCENE_ID + COMMA
                + GalleryStore.SeniorMedia.TAB + DOT + SeniorMediaColumns.SENIOR_SCORE + AS + SeniorMediaColumns.SENIOR_SCORE
                + FROM + GalleryStore.GalleryMedia.TAB
                + LEFT_OUTER_JOIN + GalleryStore.ScanLabel.TAB + ON
                + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.ORIGINAL_DATA + EQUAL
                + GalleryStore.ScanLabel.TAB + DOT + ScanLabelColumns.DATA
                + LEFT_OUTER_JOIN + GalleryStore.SeniorMedia.TAB + ON
                + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.ORIGINAL_DATA + EQUAL
                + GalleryStore.SeniorMedia.TAB + DOT + SeniorMediaColumns.DATA
                + WHERE + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.MEDIA_ID + EQUAL_TO
                + AND + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.IS_TRASHED + NOT_EQUAL_ONE;
        Bundle bundle = new Bundle();
        try (Cursor cursor = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setQuerySql(sql)
                .setSqlArgs(new String[]{String.valueOf(mediaId)})
                .setConvert(new CursorConvert())
                .build()
                .exec()) {
            if (cursor != null) {
                cursor.moveToPosition(-1);
                int indexSceneId = cursor.getColumnIndex(COLUMN_SCENE_ID);
                int indexSeniorScore = cursor.getColumnIndex(COLUMN_SENIOR_SCORE);
                while (cursor.moveToNext()) {
                    bundle.putString(COLUMN_SCENE_ID, cursor.getString(indexSceneId));
                    bundle.putFloat(COLUMN_SENIOR_SCORE, cursor.getFloat(indexSeniorScore));
                }
            }
        }
        return bundle;
    }

    /**
     * SELECT  *  FROM local_media WHERE media_id IN (19615,19640,19634,19641,19025) AND
     * (tagflags > 0  AND media_type = 1 AND tagflags&8388608 = 8388608)
     *
     * @param extras bundle
     * @return bundle
     */
    private Bundle filterOlivePictures(Bundle extras) {
        long startTime = System.currentTimeMillis();
        if (extras == null) {
            GLog.w(TAG, "filterOlivePictures, extras is null, return");
            return null;
        }
        ArrayList<String> userSelectUris = extras.getStringArrayList(USER_SELECT_URIS);
        if (userSelectUris == null) {
            GLog.w(TAG, "filterOlivePictures, no select uris input");
            return null;
        }
        StringBuilder mediaIdSb = new StringBuilder();
        for (int i = 0; i < userSelectUris.size(); i++) {
            String uri = userSelectUris.get(i);
            try {
                long mediaId = ContentUris.parseId(Uri.parse(uri));
                if (i == userSelectUris.size() - 1) {
                    mediaIdSb.append(mediaId);
                } else {
                    mediaIdSb.append(mediaId).append(TextUtil.SPLIT_COMMA_SEPARATOR);
                }
            } catch (Exception exception) {
                GLog.e(TAG, "filterOlivePictures parseId exception", exception);
            }
        }
        if (mediaIdSb.toString().isEmpty()) {
            GLog.w(TAG, "filterOlivePictures, no valid mediaIds");
            return null;
        }
        String sql = SELECT + ASTERISK + FROM + GalleryStore.GalleryMedia.TAB + WHERE
                + LocalColumns.MEDIA_ID + IN + LEFT_BRACKETS + mediaIdSb
                + RIGHT_BRACKETS + SQLGrammar.AND + ClassifiedOliveAlbum.BASE_WHERE_CLAUSE;
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setQuerySql(sql)
                .setSqlArgs(null)
                .setConvert(new CursorConvert())
                .build();

        Cursor cursor = null;
        ArrayList<String> oliveUris = new ArrayList<>();
        try {
            cursor = DataAccess.getAccess().rawQuery(rawQueryReq);
            if ((cursor == null) || (cursor.getCount() <= 0)) {
                GLog.w(TAG, "filterOlivePictures no result");
                IOUtils.closeQuietly(cursor);
                return null;
            }
            final int idIndex = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
            while (cursor.moveToNext()) {
                int mediaId = cursor.getInt(idIndex);
                String uri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, mediaId).toString();
                oliveUris.add(uri);
            }
        } catch (Exception e) {
            GLog.w(TAG, "filterOlivePictures, exception query");
        } finally {
            IOUtils.closeQuietly(cursor);
        }

        if (oliveUris.isEmpty()) {
            GLog.w(TAG, "filterOlivePictures, no olive result");
            return null;
        }
        Bundle bundle = new Bundle();
        bundle.putStringArrayList(OLIVE_URIS, oliveUris);
        GLog.v(TAG, "filterOlivePictures costTime:" + (System.currentTimeMillis() - startTime));
        return bundle;
    }

    private Cursor queryFavoriteAlbums() {
        MatrixCursor cursor = new MatrixCursor(FAVORITE_ITEM_PROJECTION);
        MediaSet favoritesAlbum = null;
        if (FavoritesAlbum.checkFavoritesAvailable(MEDIA_TYPE_SUPPORT_ALL | MEDIA_TYPE_SUPPORT_ONLY_LOCAL_FILE)) {
            Path path = Local.PATH_ALBUM_OPEN_FAVORITE;
            favoritesAlbum = DataManager.getMediaSet(path);
        }
        if (favoritesAlbum != null) {
            int count = favoritesAlbum.getCount();
            List<MediaItem> mediaItems = favoritesAlbum.getSubMediaItem(0, count);
            for (MediaItem mediaItem : mediaItems) {
                cursor.addRow(new Object[]{mediaItem.getMediaId(), mediaItem.getFilePath(), mediaItem.getName(),
                        mediaItem.getDateTakenInMs()});
            }
        }
        cursor.moveToPosition(-1);
        return cursor;
    }

    private MatrixCursor queryLocalEntrySetItem(String[] selectionArgs) {
        String queryArgs = joinQueryArgs(selectionArgs);
        int type = getTypeFromArgs(queryArgs);
        int startIndex = getStartIndexFromArgs(queryArgs);
        int itemCount = getCountFromArgs(queryArgs);
        boolean isNeedVirtual = getIsNeedVirtualFromArgs(queryArgs);

        final List<MediaSet> mediaSets = AllAlbumDataHelper.getAllAlbums(
                getSupportMediaType(type), !isNeedVirtual, true);
        return albumsToCursor(startIndex, itemCount, mediaSets);
    }

    private int getSupportMediaType(int type) {
        switch (type) {
            case TYPE_IMAGE:
                return MEDIA_TYPE_SUPPORT_IMAGE;
            case TYPE_VIDEO:
                return MEDIA_TYPE_SUPPORT_VIDEO;
            default:
                return MEDIA_TYPE_SUPPORT_ALL;
        }
    }

    private MatrixCursor albumsToCursor(int startIndex, int itemCount, List<MediaSet> albumList) {
        MatrixCursor cursor = new MatrixCursor(LOCAL_COLUMN_GROUP);
        if ((albumList == null) || albumList.isEmpty()) {
            GLog.w(TAG, "albumsToCursor is null or empty!");
            return cursor;
        }
        int normalStartIndex = 0;
        int normalEndIndex = 0;
        int size = albumList.size();
        if ((itemCount == INVALID_COUNT) || (startIndex == INVALID_START_INDEX)) {
            normalEndIndex = size;
        } else {
            normalStartIndex = startIndex;
            normalEndIndex = startIndex + itemCount;
            if (normalStartIndex > size) {
                normalStartIndex = size;
            }
            if (normalEndIndex > size) {
                normalEndIndex = size;
            }
        }
        MediaSet album = null;
        boolean isVirtualAlbum = false;
        long startTime = 0;
        long getCountTime = 0;
        long getCoverItemTime = 0;
        int count = 0;
        MediaItem coverItem = null;
        List<MediaItem> coverItemList = null;
        for (int i = normalStartIndex; i < normalEndIndex; i++) {
            album = albumList.get(i);
            startTime = System.currentTimeMillis();
            count = album.getCount();
            getCountTime = GLog.getTime(startTime);
            startTime = System.currentTimeMillis();
            coverItemList = album.getCoverItems();
            coverItem = coverItemList.isEmpty() ? null : coverItemList.get(0);
            getCoverItemTime = GLog.getTime(startTime);
            String name = album.getName();
            Path path = album.getPath();
            int coverId = (coverItem == null) ? 0 : coverItem.getMediaId();
            Uri coverUri = getUriById((long) coverId);
            String key = "?" + SET_PATH + "=" + path + "&type=" + SearchType.TYPE_ALBUM;
            cursor.addRow(new Object[]{SearchType.TYPE_ALBUM, name, key, coverId, coverUri, count, 0});
            if (GProperty.DEBUG) {
                GLog.d(TAG, String.format(
                        Locale.ENGLISH,
                        "albumsToCursor: name=%s, coverId=%d, count=%d, key=%s, getCountTime=%d, getCoverItemTime=%d, isVirtualAlbum=%s",
                        name, coverId, count, key, getCountTime, getCoverItemTime,
                        (isVirtualAlbum ? STRING_TRUE : STRING_FALSE)));
            }
        }
        cursor.moveToPosition(-1);
        return cursor;
    }

    private Cursor queryRecommendAlbums(String interfaceName, String[] selectionArgs, PackageBean packageBean) {
        QueryResult result = null;
        String keyword = joinQueryArgs(selectionArgs);
        String lowerKeyword = keyword.toLowerCase();

        if (lowerKeyword.startsWith(QUERY_RECOMMEND_RECENTLY_ADDED)) {
            return queryRecommendForRecentlyAdded();
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_YEAR)) {
            result = new QueryResult(TYPE_RECOMMEND_YEAR);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_FESTIVAL)) {
            result = new QueryResult(TYPE_RECOMMEND_FESTIVAL);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_MONTH)) {
            result = new QueryResult(TYPE_RECOMMEND_MONTH);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_LOCATION)) {
            result = new QueryResult(TYPE_RECOMMEND_LOCATION);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_PERSON)) {
            result = new QueryResult(TYPE_RECOMMEND_PERSON);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_LABEL)) {
            result = new QueryResult(TYPE_RECOMMEND_LABEL);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_MEMORIES)) {
            result = new QueryResult(TYPE_RECOMMEND_MEMORIES);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_DATE)) {
            result = new QueryResult(TYPE_RECOMMEND_DATA);
        } else if (lowerKeyword.startsWith(QUERY_RECOMMEND_ALL)) {
            result = new QueryResult(TYPE_RECOMMEND_ALL);
        } else {
            result = new QueryResult(-1);
        }
        return result.getCursorFromResult(interfaceName, keyword, packageBean);
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        GLog.d(TAG, "delete, uriInfo: " + uri);
        int match = URI_MATCHER.match(uri);
        String callPackage = getCallPackage();
        int callingUid = Binder.getCallingUid();
        long token = Binder.clearCallingIdentity();
        try {
            if (!noNeedDetectPermission(match) && CTAHelper.hasNoPermissionToQuery(getContext(), TAG)) {
                return 0;
            }
            if (!DeferredExtKt.getOnBlocking(mOpenListConfigFuture)) {
                GLog.d(TAG, "delete, open list config init failed!");
                return 0;
            }
            GalleryOpenListConfig.PackageBean packageBean = mOpenListConfig.getAllowedPackageBean(callPackage, callingUid);
            if (packageBean == null) {
                GLog.d(TAG, "delete, callPackage:" + callPackage + " isNotAllow");
                return 0;
            }
            if ((match == LOCKED_PICTURES) && (packageBean.getTags() != null) && packageBean.getTags().contains(TAG_LOCKED_PICTURES)) {
                return LockedPicHelper.delete(selection, selectionArgs);
            } else if ((match == RECYCLE_FILES) && (packageBean.getTags() != null) && packageBean.getTags().contains(TAG_RECYCLE_FILES)) {
                return deleteRecycleFiles(packageBean.getPackageName(), selection, selectionArgs);
            } else if ((match == FAST_CAPTURES) && (packageBean.getTags() != null) && packageBean.getTags().contains(TAG_FAST_CAPTURES)) {
                return FastCaptureHelper.delete(selection, selectionArgs);
            } else {
                throw new UnsupportedOperationException("delete, uri: " + uri + ", callPackage: " + callPackage);
            }
        } finally {
            Binder.restoreCallingIdentity(token);
        }
    }

    private void sortQueryResultByCount(List<HashMap<String, Object>> dataSetList, String keyword) {
        if (dataSetList.isEmpty()) {
            return;
        }
        Collections.sort(dataSetList, new Comparator<HashMap<String, Object>>() {
            @Override
            public int compare(HashMap<String, Object> o1, HashMap<String, Object> o2) {
                int o1value = (Integer) o1.get(SearchSuggestionProviderUtil.SEARCH_RESULT_COUNT);
                int o2value = (Integer) o2.get(SearchSuggestionProviderUtil.SEARCH_RESULT_COUNT);
                return o2value - o1value;
            }
        });
        String labelName = null;
        List<HashMap<String, Object>> childLabelList = new ArrayList<>();
        for (int i = 0, n = dataSetList.size(); i < n; i++) {
            HashMap<String, Object> dataSet = dataSetList.get(i);
            int resultType = (Integer) dataSet.get(SearchSuggestionProviderUtil.SEARCH_RESULT_TYPE);
            String resultName = (String) dataSet.get(SearchSuggestionProviderUtil.SEARCH_RESULT_NAME);
            if (resultType == SearchType.TYPE_LABEL) {
                if (!TextUtils.isEmpty(resultName) && resultName.equalsIgnoreCase(keyword)) {
                    labelName = resultName;
                }
            } else if (resultType == SearchType.TYPE_CHILD_LABEL) {
                childLabelList.add(dataSet);
            }
        }
        if (labelName == null || TextUtils.isEmpty(labelName) || childLabelList.isEmpty()) {
            return;
        }
        dataSetList.removeAll(childLabelList);
        for (int i = 0, n = dataSetList.size(); i < n; i++) {
            HashMap<String, Object> dataSet = dataSetList.get(i);
            String resultName = (String) dataSet.get(SearchSuggestionProviderUtil.SEARCH_RESULT_NAME);
            int resultType = (Integer) dataSet.get(SearchSuggestionProviderUtil.SEARCH_RESULT_TYPE);
            if ((resultType == SearchType.TYPE_LABEL) && labelName.equalsIgnoreCase(resultName)) {
                dataSetList.addAll(i + 1, childLabelList);
                break;
            }
        }
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String selection, @Nullable String[] selectionArgs) {
        GLog.d(TAG, "update, uriInfo: " + uri);
        int match = URI_MATCHER.match(uri);
        String callPackage = getCallPackage();
        int callingUid = Binder.getCallingUid();
        long token = Binder.clearCallingIdentity();
        try {
            if (!noNeedDetectPermission(match) && CTAHelper.hasNoPermissionToQuery(getContext(), TAG)) {
                return 0;
            }
            if (!DeferredExtKt.getOnBlocking(mOpenListConfigFuture)) {
                GLog.d(TAG, "update, open list config init failed!");
                return 0;
            }
            GalleryOpenListConfig.PackageBean packageBean = mOpenListConfig.getAllowedPackageBean(callPackage, callingUid);
            if (packageBean == null) {
                GLog.d(TAG, "update, callPackage:" + callPackage + " isNotAllow");
                return 0;
            }
            if ((match == LOCKED_PICTURES) && (packageBean.getTags() != null) && packageBean.getTags().contains(TAG_LOCKED_PICTURES)) {
                return LockedPicHelper.update(values, selection, selectionArgs);
            } else if ((match == FAST_CAPTURES) && (packageBean.getTags() != null) && packageBean.getTags().contains(TAG_FAST_CAPTURES)) {
                return FastCaptureHelper.update(values, selection, selectionArgs);
            } else {
                throw new UnsupportedOperationException("update, uri: " + uri + ", callPackage: " + callPackage);
            }
        } finally {
            Binder.restoreCallingIdentity(token);
        }
    }

    public Cursor getAlbumInfoByPath(String path, boolean isFromSmart, String queryArgs) {
        MediaSet mediaSet = DataManager.getMediaSet(Path.fromString(path));
        boolean filterOlive = shouldFilterOlive(queryArgs);
        if (mediaSet == null) {
            GLog.d(TAG, " getAlbumInfoByPath " + path + " error");
            return null;
        }
        GLog.d(TAG, " getAlbumInfoByPath " + path);
        int count = mediaSet.getCount();
        if (count == 0) {
            return null;
        }
        List<MediaItem> mediaItems = mediaSet.getSubMediaItem(0, count);
        int size = (mediaItems == null) ? 0 : mediaItems.size();
        if (size == 0) {
            return null;
        }
        MatrixCursor cursor = null;
        if (isFromSmart) {
            cursor = new MatrixCursor(ALBUM_ITEM_COLUMN);
        } else {
            cursor = new MatrixCursor(LOCAL_ITEM_PROJECTION);
        }
        for (MediaItem mediaItem : mediaItems) {
            String filePath = mediaItem.getFilePath();
            filePath = MultiAppUtils.INSTANCE.convertToSystemPath(filePath);
            long dateTaken = mediaItem.getDateTakenInMs();
            int isCShot = 0;
            if (mediaItem instanceof LocalImage) {
                isCShot = DatabaseUtils.isCShotIdValid(mediaItem.getCShotID()) ? 1 : 0;
            }
            String mimeType = mediaItem.getMimeType();
            long dataModified = mediaItem.getDateModifiedInSec();
            int duration = mediaItem.getDuration();
            int width = mediaItem.getWidth();
            int height = mediaItem.getHeight();
            // 全部查询 或者 过滤出olive且该图片是olive时，才添加到Cursor中
            if (filterMediaItemForCursor(filterOlive, mediaItem)) {
                if (isFromSmart) {
                    cursor.addRow(new Object[]{mediaItem.getMediaId(), filePath, mimeType, dateTaken, dataModified, duration, width, height});
                } else {
                    cursor.addRow(new Object[]{mediaItem.getMediaId(), filePath, dateTaken, isCShot});
                }
            }
        }
        cursor.moveToPosition(-1);
        return cursor;
    }

    /**
     * getAlbumInfoByPath调用，判断传入的媒体项是否应该添加到Cursor
     * @param filterOlive 是否过滤olive
     * @param mediaItem 传入的媒体项
     * @return 是否是olive
     */
    private boolean filterMediaItemForCursor(boolean filterOlive, MediaItem mediaItem) {
        if (mediaItem == null) {
            return false;
        }
        //若不需要过滤出olive,则直接返回true,表示可以添加
        if (!filterOlive) {
            return true;
        }
        //若需要过滤出olive,则需判断是否是olive图片
        long tagFlag = mediaItem.getTagFlags();
        return (tagFlag != OplusExifTag.INVALID) && ((tagFlag & EXIF_TAG_OLIVE) == EXIF_TAG_OLIVE);
    }

    private Cursor queryAlbumInfo(String interfaceName, String[] selectionArgs) {
        long startTime = System.currentTimeMillis();
        String queryArgs = joinQueryArgs(selectionArgs);
        int type = getTypeFromArgs(queryArgs);
        String recommendName = getRecommendNameFromArgs(queryArgs);
        String extraNames = getExtraNameFromArgs(queryArgs);
        boolean isFromSmart = getFromFromArgs(queryArgs);
        switch (type) {
            case SearchType.TYPE_PERSON:
                trackQueryAlbumInfo(interfaceName, startTime);
                return getPersonsByGroupId(getContext(), Long.parseLong(recommendName), isFromSmart);
            case SearchType.TYPE_MEMORIES_ALBUM:
                trackQueryAlbumInfo(interfaceName, startTime);
                int memoryId = getMemoryIdFromArgs(queryArgs);
                return getMemoryMediaIDListBySetId(getContext(), memoryId, isFromSmart);
            case SearchType.TYPE_ALBUM:
                String path = getSetPathFromArgs(queryArgs);
                boolean isVirtual = getIsVirtualArgs(queryArgs);
                if (isVirtual) {
                    int albumType = getAlbumTypeFromArgs(queryArgs);
                    int count = getCountFromArgs(queryArgs);
                    return getVirtualAlbumInfoByPath(path, count, albumType, queryArgs);
                } else {
                    trackQueryAlbumInfo(interfaceName, startTime);
                    return getAlbumInfoByPath(path, isFromSmart, queryArgs);
                }
            case SearchType.TYPE_RECENT_TIME:
            case SearchType.TYPE_LOCATION:
            case SearchType.TYPE_DATETIME:
            case SearchType.TYPE_LABEL:
                return getAlbumInfoByName(type, recommendName, extraNames, isFromSmart);
            default:
                String idList = getIdListFromArgs(queryArgs);
                trackQueryAlbumInfo(interfaceName, startTime);
                return searchAlbumInfoByIdList(idList, isFromSmart);
        }
    }

    /**
     * 获取图集详情埋点
     */
    private void trackQueryAlbumInfo(String interfaceName, long startTime) {
        OpenAbilityTrackHelper.INSTANCE.trackOpenAbilityCall(OpenAbilityTrackHelper.INSTANCE.buildCommonParamMap(
                OpenFeatureName.QUERY_ALBUMS,
                interfaceName,
                SearchTrackHelper.providerCallPackage,
                CallResult.SUCCESS,
                TextUtil.EMPTY_STRING,
                String.valueOf(GLog.getTime(startTime))), null);
    }

    public Cursor searchAlbumInfoByIdList(String idList, boolean isFromSmart) {
        if ((idList == null) || (TextUtils.isEmpty(idList))) {
            return null;
        }
        MatrixCursor cursor = null;
        if (isFromSmart) {
            cursor = new MatrixCursor(ALBUM_ITEM_COLUMN);
        } else {
            cursor = new MatrixCursor(LOCAL_ITEM_PROJECTION);
        }
        ArrayList<MediaItem> listMediaItem = SearchDBHelper.getMediaItemListFromIdList(
                LocalColumns.MEDIA_ID, idList, 0, idList.length());
        for (MediaItem mediaItem : listMediaItem) {
            String path = mediaItem.getFilePath();
            path = MultiAppUtils.INSTANCE.convertToSystemPath(path);
            long dateTaken = mediaItem.getDateTakenInMs();
            int isCShot = 0;
            if (mediaItem instanceof LocalImage) {
                isCShot = DatabaseUtils.isCShotIdValid(mediaItem.getCShotID()) ? 1 : 0;
            }
            String mimeType = mediaItem.getMimeType();
            int duration = mediaItem.getDuration();
            long dataModified = mediaItem.getDateModifiedInSec();
            int width = mediaItem.getWidth();
            int height = mediaItem.getHeight();
            if (isFromSmart) {
                cursor.addRow(new Object[]{mediaItem.getMediaId(), path, mimeType, dateTaken, dataModified, duration, width, height});
            } else {
                cursor.addRow(new Object[]{mediaItem.getMediaId(), path, dateTaken, isCShot});
            }
        }
        cursor.moveToPosition(-1);
        return cursor;
    }

    private String buildWhereClause(String queryArgs) {
        if (TextUtils.isEmpty(queryArgs)) {
            return null;
        }
        StringBuilder whereClause = new StringBuilder();
        String mimeType = getMimeTypeFromArgs(queryArgs);
        if (!TextUtils.isEmpty(mimeType)) {
            whereClause.append(SQLGrammar.AND)
                    .append(LocalColumns.MIME_TYPE)
                    .append(SQLGrammar.LIKE)
                    .append(SQLGrammar.QUOTE)
                    .append(SQLGrammar.LIKE_PERCENT)
                    .append(mimeType)
                    .append(SQLGrammar.LIKE_PERCENT)
                    .append(SQLGrammar.QUOTE);
        }
        String dateTaken = getDateTakenFromArgs(queryArgs);
        if (!TextUtils.isEmpty(dateTaken)) {
            long date = Long.parseLong(dateTaken);
            whereClause.append(SQLGrammar.AND)
                    .append(LocalColumns.DATE_TAKEN)
                    .append(SQLGrammar.GREATER_THAN)
                    .append(date);
        }
        String dateModified = getDataModifiedFromArgs(queryArgs);
        if (!TextUtils.isEmpty(dateModified)) {
            long date = Long.parseLong(dateModified);
            whereClause.append(SQLGrammar.AND)
                    .append(LocalColumns.DATE_MODIFIED)
                    .append(SQLGrammar.GREATER_THAN).append(date);
        }
        boolean filterOlive = shouldFilterOlive(queryArgs);
        if (filterOlive) {
            whereClause.append(SQLGrammar.AND)
                    .append(LEFT_BRACKETS)
                    .append(GalleryStore.GalleryColumns.LocalColumns.TAGFLAGS)
                    .append(SQLGrammar.TRIM_BIT_AND)
                    .append(Constants.CameraMode.FLAG_SUPPORT_OLIVE)
                    .append(SQLGrammar.TRIM_EQUAL)
                    .append(Constants.CameraMode.FLAG_SUPPORT_OLIVE)
                    .append(RIGHT_BRACKETS);
        }
        return whereClause.toString();
    }

    /**
     * 分页查询语句
     *  LIMIT * OFFSET *
     * @param queryArgs
     * @return
     */
    private String buildPagingClause(String queryArgs) {
        if (TextUtils.isEmpty(queryArgs)) {
            return null;
        }
        StringBuilder pagingClause = new StringBuilder();
        String offset = getOffsetFromArgs(queryArgs);
        String limit = getLimitFromArgs(queryArgs);
        if (!TextUtils.isEmpty(offset) && !TextUtils.isEmpty(limit)) {
            pagingClause.append(LIMIT)
                    .append(limit)
                    .append(OFFSET)
                    .append(offset);
        }
        return pagingClause.toString();
    }

    /**
     * searchAlbumsDetail搜索接口：通过融合搜索直接获取图集详情
     */
    private Cursor querySearchAlbumDetail(@Nullable String[] projection, @Nullable String selection,
                                     @Nullable String[] selectionArgs, @Nullable String sortOrder,
                                     GalleryOpenListConfig.PackageBean packageBean) {
        long startTime = System.currentTimeMillis();
        Cursor albumCursor = null;
        Cursor detailCursor = null;
        MatrixCursor cursor = new MatrixCursor(LOCAL_ITEM_PROJECTION);

        // 0. 参数中获取查询结果最大行数，未传参则用默认值 SEARCH_ALBUM_DETAIL_RESULT_MAX_ROW
        int rowLimit = getQueryRowLimit(selectionArgs, SEARCH_ALBUM_DETAIL_RESULT_MAX_ROW);

        try {
            // 1. 先通过融合搜索得到图集的搜索结果
            albumCursor = querySearchAlbums(SEARCH_ALBUMS_PATH, projection, selection, selectionArgs, sortOrder, packageBean);
            if ((albumCursor == null) || (albumCursor.getCount() <= 0)) {
                GLog.w(TAG, "[querySearchAlbumDetail] null or empty when querySearchAlbums. empty result will be returned!");
                return cursor;
            }

            // 2. 再遍历图集搜索结果，通过key拿到每个图集对应的图集详情结果
            int keyIndex = albumCursor.getColumnIndex(COLUMN_KEY);
            if (keyIndex < 0) {
                GLog.w(TAG, "[querySearchAlbumDetail] <key> column not found in querySearchAlbums result. " +
                        "empty result will be returned!");
                return cursor;
            }
            while(albumCursor.moveToNext()) {
                String albumKey = albumCursor.getString(keyIndex);
                detailCursor = queryAlbumInfo(QUERY_ALBUM_DETAIL_PATH, new String[] {albumKey});
                fillUpWithDetailCursor(detailCursor, cursor, rowLimit);
                if (detailCursor != null) {
                    detailCursor.close();
                }
                // 2.1 检查结果，够了就可以直接return了。不够则继续遍历
                if (cursor.getCount() >= rowLimit) {
                    break;
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "[querySearchAlbumDetail] exception:", e);
        } finally {
            IOUtils.closeQuietly(albumCursor);
            IOUtils.closeQuietly(detailCursor);
        }
        cursor.moveToPosition(-1);
        GLog.d(TAG, "[querySearchAlbumDetail] resultCursor count=" + cursor.getCount() + ", cost=" + GLog.getTime(startTime));
        return cursor;
    }

    /**
     * 在query前调用，确定查询的最大行数
     */
    private int getQueryRowLimit(@Nullable String[] selectionArgs, int maxRowLimit) {
        String keyword = joinQueryArgs(selectionArgs);
        Uri queryUri = Uri.parse(keyword);
        String rowLimitParam = queryUri.getQueryParameter(SEARCH_ALBUM_DETAIL_RESULT_ROW_LIMIT);
        int rowLimit = 0;
        if (rowLimitParam == null || rowLimitParam.isEmpty()) {
            rowLimit = maxRowLimit;
        } else {
            try {
                rowLimit = Integer.parseInt(rowLimitParam);
            } catch (Exception e) {
                rowLimit = maxRowLimit;
                GLog.w(TAG, LogFlag.DL, () -> "[getQueryRowLimit] error occurred when get rowLimit, exception: " + e);
            }
        }
        return rowLimit;
    }

    /**
     * 将detailCursor的查询结果添加至targetCursor中，
     * 并确保targetCursor最大行数不超过limit。
     */
    private void fillUpWithDetailCursor(Cursor detailCursor, MatrixCursor targetCursor, int limit) {
        if ((detailCursor == null) || (targetCursor == null) ||
                (detailCursor.getCount() < 0) || (targetCursor.getCount() >= limit)) {
            return;
        }
        // 遍历图集详情，依次加入到targetCursor中
        int mediaIdIdx = detailCursor.getColumnIndex(COLUMN_MEDIA_ID);
        int pathIdx = detailCursor.getColumnIndex(COLUMN_PATH);
        int dateTakenIdx = detailCursor.getColumnIndex(COLUMN_DATE_TAKEN);
        int isCShotIdx = detailCursor.getColumnIndex(COLUMN_IS_CSHOT);
        while(detailCursor.moveToNext()) {
            // 这些字段不一定都有，没有的填null
            Integer mediaId = mediaIdIdx >= 0 ? detailCursor.getInt(mediaIdIdx): null;
            String path = pathIdx >= 0 ? detailCursor.getString(pathIdx): null;
            Integer dateTaken = dateTakenIdx >= 0 ? detailCursor.getInt(dateTakenIdx): null;
            Integer isCShot = isCShotIdx >= 0 ? detailCursor.getInt(isCShotIdx): null;

            targetCursor.addRow(new Object[]{mediaId, path, dateTaken, isCShot});

            // 检查结果是否足够，够了直接return，不够继续加
            if (targetCursor.getCount() >= limit) {
                return;
            }
        }
    }

    private Cursor querySearchAlbums(@Nullable String interfaceName, @Nullable String[] projection,
                                     @Nullable String selection, @Nullable String[] selectionArgs,
                                     @Nullable String sortOrder, GalleryOpenListConfig.PackageBean packageBean) {
        if ((packageBean == null) || (packageBean.getTags() == null)) {
            GLog.d(TAG, "querySearchAlbums,packageBean or packageBean.getTags() is null");
            return null;
        }
        long startTime = System.currentTimeMillis();
        Uri uri = Uri.parse("content://" + SUGGESTION_PROVIDER_AUTHORITY);
        String queryArgs = joinQueryArgs(selectionArgs);
        String keyword = getQueryWord(queryArgs.toLowerCase(), true);
        GLog.d(TAG, "querySearchAlbums keyword:" + keyword + ",queryArgs:" + queryArgs);
        MatrixCursor cursor = new MatrixCursor(COLUMN_GROUP);
        Cursor originalCursor = null;
        StringBuilder staticSb = new StringBuilder();
        try {
            GTrace.traceBegin("querySearchAlbums");
            originalCursor = ContextGetter.context.getContentResolver().query(uri, projection, selection, selectionArgs, sortOrder);
            if (originalCursor != null) {
                List<HashMap<String, Object>> dataSet = transformCursorToList(packageBean, originalCursor);
                if (!packageBean.getTags().contains(SmartOpenProvider.IGNORE_SEARCH_ORDER)) {
                    sortQueryResultByCount(dataSet, keyword);
                }
                for (int i = 0, n = dataSet.size(); i < n; i++) {
                    HashMap<String, Object> set = dataSet.get(i);
                    Long coverId = (Long) set.get(COLUMN_COVER_ID);
                    String name = (String) set.get(COLUMN_NAME);
                    Integer itemCount = (Integer) set.get(COLUMN_COUNT);
                    Integer type = (Integer) set.get(COLUMN_TYPE);
                    String key = (String) set.get(COLUMN_KEY);
                    Uri coverUri = (Uri) set.get(COLUMN_COVER_URI);
                    String tag = (String) set.get(COLUMN_ALBUM_TAG);
                    if (GProperty.DEBUG) {
                        GLog.d(TAG, String.format(Locale.ENGLISH,
                                "querySearchAlbums: name=%s, coverId=%d, itemCount=%d, key=%s, tag=%s",
                                name, coverId, itemCount, key, tag));
                    }
                    if (TextUtils.isEmpty(name)) {
                        GLog.w(TAG, "querySearchAlbums: name is null, continue");
                        continue;
                    }
                    cursor.addRow(new Object[]{type, name, key, coverId, coverUri, itemCount, tag});
                    staticSb.append(getCurrentType(type));
                    staticSb.append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE);
                }
                if (-1 != staticSb.lastIndexOf(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE)) {
                    staticSb.deleteCharAt(staticSb.lastIndexOf(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE));
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "querySearchAlbums exception:", e);
        } finally {
            IOUtils.closeQuietly(originalCursor);
            GTrace.traceEnd();
        }
        cursor.moveToPosition(-1);
        // 搜索图集完成后埋点上报
        OpenAbilityTrackHelper.INSTANCE.trackOpenAbilityCall(OpenAbilityTrackHelper.INSTANCE.buildCommonParamMap(
                OpenFeatureName.SEARCH,
                interfaceName,
                SearchTrackHelper.providerCallPackage,
                CallResult.SUCCESS,
                TextUtil.EMPTY_STRING,
                String.valueOf(GLog.getTime(startTime))
        ), OpenAbilityTrackHelper.INSTANCE.buildSearchParamMap(staticSb.toString(), keyword,
                (staticSb.length() > 0) ? SearchHasResult.HAS_RESULT : SearchHasResult.NO_RESULT));

        return cursor;
    }

    @NotNull
    private List<HashMap<String, Object>> transformCursorToList(PackageBean packageBean, Cursor originalCursor) {
        int indexId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_ID);
        int indexType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_TYPE);
        int indexAlbumType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_ALBUM_TYPE);
        int indexName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_NAME);
        int indexCount = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_COUNT);
        int indexIdList = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_ID_LIST);
        int indexCoverId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_ID);
        int indexMediaType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE);
        String albumTag = "";
        List<HashMap<String, Object>> dataSet = new ArrayList<>();
        while (originalCursor.moveToNext()) {
            int id = originalCursor.getInt(indexId);
            long coverId = originalCursor.getLong(indexCoverId);
            String name = originalCursor.getString(indexName);
            int itemCount = originalCursor.getInt(indexCount);
            int type = originalCursor.getInt(indexType);
            int albumType = originalCursor.getInt(indexAlbumType);
            String idList = originalCursor.getString(indexIdList);
            int mediaType = originalCursor.getInt(indexMediaType);
            StringBuilder key = new StringBuilder();
            switch (type) {
                case SearchType.TYPE_PERSON:
                    // ?type=2&name=id&extra_names=null
                    key.append("?").append(COLUMN_TYPE).append("=").append(type)
                            .append("&").append(COLUMN_NAME).append("=").append(id)
                            .append("&").append(COLUMN_EXTRA_NAMES).append("=null");
                    FaceInfo faceInfo = getFaceInfo(getContext(), coverId);
                    if (faceInfo != null) {
                        coverId = faceInfo.getMediaId();
                        albumTag = createAlbumTagJson(faceInfo);
                    }
                    break;
                case SearchType.TYPE_OCR:
                    // ?id_list=2,3,4&search_result_type=1
                    key.append("?").append(COLUMN_ID_LIST).append("=").append(idList)
                            .append("&").append(SEARCH_RESULT_TYPE).append("=").append(type);
                    albumTag = createAlbumTagJson(true, mediaType);
                    break;
                case SearchType.TYPE_MEMORIES_ALBUM:
                    // ?type=1024&extra_names=&memoryId=coverId
                    key.append("?").append(COLUMN_TYPE).append("=").append(type)
                            .append("&").append(COLUMN_EXTRA_NAMES).append("=")
                            .append("&").append(MEMORY_ID).append("=").append(coverId);
                    Path coverPath = Local.PATH_ALBUM_MEMORIES_ANY.getChild(coverId);
                    MemoriesAlbum mediaSet = (MemoriesAlbum) DataManager.getMediaSet(coverPath);
                    if (mediaSet != null) {
                        boolean isOrderAsc = ApiDmManager.getSettingDM().isPositiveOrder();
                        mediaSet.setInputEntry(new MemoriesAlbum.MemoriesAlbumEntry(name, (int) coverId, itemCount,
                                mediaSet.getDefaultOrder(isOrderAsc), isOrderAsc));
                        if (!mediaSet.getCoverItems().isEmpty()) {
                            coverId = mediaSet.getCoverItems().get(0).getMediaId();
                        }
                    }
                    albumTag = "";
                    break;
                case SearchType.TYPE_LABEL:
                case SearchType.TYPE_DATETIME:
                case SearchType.TYPE_LOCATION:
                case SearchType.TYPE_ALBUM:
                case SearchType.TYPE_FILE_NAME:
                    if ((albumType == AlbumEntry.TYPE_RECYCLE_ALBUM) && (packageBean.getTags().contains(IGNORE_SEARCH_RECYCLE))) {
                        continue;
                    }
                    // ?id_list=2,3,4&search_result_type=1
                    key.append("?").append(COLUMN_ID_LIST).append("=").append(idList)
                            .append("&").append(SEARCH_RESULT_TYPE).append("=").append(type);
                    albumTag = createAlbumTagJson(false, mediaType);
                    break;
                default:
                    // ?id_list=2,3,4&search_result_type=1
                    key.append("?").append(COLUMN_ID_LIST).append("=").append(idList)
                            .append("&").append(SEARCH_RESULT_TYPE).append("=").append(type);
                    albumTag = "";
                    break;
            }
            Uri coverUri = getUriById(coverId);
            HashMap<String, Object> map = new HashMap<>();
            map.put(COLUMN_TYPE, type);
            map.put(COLUMN_NAME, name);
            map.put(COLUMN_COUNT, itemCount);
            map.put(COLUMN_COVER_ID, coverId);
            map.put(COLUMN_KEY, key.toString());
            map.put(COLUMN_COVER_URI, coverUri);
            map.put(COLUMN_ALBUM_TAG, albumTag);
            dataSet.add(map);
        }
        return dataSet;
    }

    /**
     * 通过融合搜索直接获取图集详情，再根据图集信息去调用DmpQuery获取aiSearch需要的大部分信息
     * 最后查询Local_media获取description和volume_name信息(转化成uri信息)
     *
     * mark by pusong：此处复用DmpQuery查询数据信息，可优化针对具体信息单独写sql语句
     */
    private Cursor queryAiSearch(@Nullable String[] projection, @Nullable String selection,
                                          @Nullable String[] selectionArgs, @Nullable String sortOrder,
                                          PackageBean packageBean) {
        long startTime = System.currentTimeMillis();
        Map<Integer, AiSearchData> dataMap = new HashMap<>();
        MatrixCursor cursor = new MatrixCursor(AI_SEARCH_CURSOR);

        // 0. 参数中获取查询结果最大行数，未传参则用默认值 SEARCH_ALBUM_DETAIL_RESULT_MAX_ROW
        int limit = getQueryRowLimit(selectionArgs, AI_SEARCH_ALBUM_DETAIL_RESULT_MAX_ROW);

        // 1. 先通过融合搜索得到搜索结果，将召回类型type与数据表主键_id对应
        dataMap = doAndesSearch(projection, selection, selectionArgs, sortOrder, limit, packageBean);
        if (dataMap.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, () -> "[queryAiSearch] search result is empty, return");
            return cursor;
        }
        Set<Integer> keys = dataMap.keySet();

        // 2.从Local_media表获取media_id, volume, description信息
        getDataFormLocalMediaForAiSearch(dataMap, AI_SEARCH_LOCAL_MEDIA_PROJECTION,
                COLUMN_ITEM_ID, keys.stream().map(String::valueOf).toArray(String[]::new), sortOrder);

        // 3. 复用DmpQuery获取详情信息
        Cursor dmpCursor = getDetailDataForAiSearch(AI_SEARCH_DMP_PROJECTION,
                keys.stream().map(String::valueOf).toArray(String[]::new), sortOrder);

        // 4. 填充aiSearch接口的返回信息
        generateCursorForAiSearch(cursor, dmpCursor, dataMap);

        GLog.d(TAG, LogFlag.DL, () -> "[queryAiSearch] resultCursor count=" + cursor.getCount() + ", row=" + cursor.getColumnCount()
                + ", limit=" + limit + ", cost=" + GLog.getTime(startTime));
        return cursor;
    }

    /**
     * 在queryAiSearch中调用，获取query的召回信息，并将_id和type映射（一个_id对应多个type）
     * @return Map<Integer, AiSearchData>, _id为key，AiSearchData为value
     */
    private Map<Integer, AiSearchData> doAndesSearch( @Nullable String[] projection, @Nullable String selection,
                                                          @Nullable String[] selectionArgs, @Nullable String sortOrder,
                                                          @Nullable int limit, GalleryOpenListConfig.PackageBean packageBean) {
        Map<Integer, AiSearchData> dataMap = new HashMap<>();
        if ((packageBean == null) || (packageBean.getTags() == null)) {
            GLog.d(TAG, LogFlag.DL, () -> "[doAndesSearch], packageBean or packageBean.getTags() is null");
            return dataMap;
        }
        long startTime = System.currentTimeMillis();
        Uri uri = Uri.parse("content://" + SUGGESTION_PROVIDER_AUTHORITY);
        String queryArgs = joinQueryArgs(selectionArgs);
        String keyword = getQueryWord(queryArgs.toLowerCase(), true);
        GLog.d(TAG, LogFlag.DL, () -> "[doAndesSearch] keyword:" + keyword + ", queryArgs:" + queryArgs);
        Cursor cursor = null;
        try {
            cursor = ContextGetter.context.getContentResolver().query(uri, projection, selection, selectionArgs, sortOrder);
            if (cursor != null) {
                int indexType = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_TYPE);
                int indexCount = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_GALLERY_COUNT);
                int indexIdList = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_GALLERY_ID_LIST);
                int indexAlbumType = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_ALBUM_TYPE);
                if ((indexCount == -1) || (indexIdList == -1) || (indexType == -1) || (indexAlbumType == -1)) {
                    GLog.w(TAG, LogFlag.DL, () -> "[doAndesSearch] invalid index!");
                    return dataMap;
                }
                dataMap = getAndesSearchFusionData(cursor, limit);
                if (dataMap.isEmpty()) {
                    GLog.w(TAG, LogFlag.DL, () -> "[doAndesSearch] search result is empty, return");
                    return dataMap;
                }
                while (cursor.moveToNext()) {
                    int type = cursor.getInt(indexType);
                    int count = cursor.getInt(indexCount);
                    String idStr = cursor.getString(indexIdList);
                    int albumType = cursor.getInt(indexAlbumType);
                    GLog.d(TAG, LogFlag.DL, () -> "[doAndesSearch] type = " + type + ", count = " + count + ", albumType = " + albumType);
                    if (albumType == AlbumEntry.TYPE_RECYCLE_ALBUM) { continue; }
                    if (idStr.isEmpty()) { continue; }
                    String[] idList = idStr.trim().split(COMMA);
                    for (String str : idList) {
                        try {
                            Integer id = Integer.parseInt(str);
                            if (dataMap.containsKey(id)) {
                                Objects.requireNonNull(dataMap.get(id)).addType(type);
                            }
                        } catch (NumberFormatException e) {
                            GLog.e(TAG, LogFlag.DL, e, () -> "[doAndesSearch] error occurred when get id" );
                        }
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, e, () -> "[doAndesSearch] error occurred when query" );
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        int size = dataMap.size();
        GLog.d(TAG, LogFlag.DL, () -> "[doAndesSearch], count=" + size + ", cost=" + GLog.getTime(startTime));
        return dataMap;
    }

    /**
     * 获取融合搜索中间宫格的显示信息
     */
    @Synchronized
    private Map<Integer, AiSearchData> getAndesSearchFusionData(Cursor cursor, int limit) {
        Map<Integer, AiSearchData> dataMap = new HashMap<>();
        mCompositeDataList = cursor.getExtras().getParcelableArrayList(SEARCH_COMPOSITE_RESULT_ENTRIES);
        if (mCompositeDataList == null) {
            GLog.w(TAG, LogFlag.DL, () -> "[getAndesSearchFusionData], mCompositeDataList is null");
            return dataMap;
        }
        int resultSize = 0;
        int compositeDataListSize = mCompositeDataList.size();
        if (limit != AI_SEARCH_ALBUM_DETAIL_RESULT_MAX_ROW) {
            resultSize = Math.min(limit, compositeDataListSize);
        } else {
            resultSize = compositeDataListSize;
        }
        for (int i = 0; i < resultSize; i++) {
            int id = mCompositeDataList.get(i).galleryId;
            AiSearchData aiSearchData = new AiSearchData(id);
            dataMap.put(id, aiSearchData);
            if (GProperty.getDEBUG_SEARCH()) {
                CompositeData compositeData = mCompositeDataList.get(i);
                GLog.d(TAG, LogFlag.DL, () -> "[getAndesSearchFusionData], ID:" + compositeData.galleryId + ", score:" + compositeData.rrfScore);
            }
        }
        GLog.d(TAG, LogFlag.DL, () -> "[getAndesSearchFusionData], size:" + dataMap.size() + ", limit:" + limit
                + ", CompositeDataList size:" + compositeDataListSize);
        return dataMap;
    }

    /**
     * 在queryAiSearch中获取了图集详细信息后，通过local_media查询aiSearch需要的media_id，description和volume_name，并将volume_name转为uri
     * 无返回信息，查询的信息更新到传入参数dataMap中
     */
    private void getDataFormLocalMediaForAiSearch(
            Map<Integer, AiSearchData> dataMap,
            String[] projection,
            String selection,
            String[] selectionArgs,
            String sortOrder) {
        long startTime = System.currentTimeMillis();
        BatchProcess.doBatch(Arrays.asList(selectionArgs), BatchProcess.PAGE_SIZE_999, batchFile -> {
            try (Cursor cursor = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(projection)
                    .setWhere(DatabaseUtils.getOnlyLocalWhere() + SQLGrammar.AND + DatabaseUtils.getWhereQueryIn(selection, batchFile.size()))
                    .setWhereArgs(batchFile.toArray(new String[0]))
                    .setOrderBy(sortOrder)
                    .setConvert(new CursorConvert())
                    .build().exec()) {
                if ((cursor != null) && (cursor.getCount() > 0)) {
                    int idx = cursor.getColumnIndex(COLUMN_ITEM_ID);
                    int mediaIdIdx = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                    int volumeNameIdx = cursor.getColumnIndex(LocalColumns.VOLUME_NAME);
                    int descriptionIdx = cursor.getColumnIndex(LocalColumns.DESCRIPTION);
                    while (cursor.moveToNext()) {
                        Integer id = (idx >= 0) ? cursor.getInt(idx) : null;
                        Integer mediaId = (mediaIdIdx >= 0) ? cursor.getInt(mediaIdIdx) : null;
                        if ((id != null) && (mediaId != null)) {
                            AiSearchData data = dataMap.get(id);
                            if (data != null) {
                                data.setMediaId(mediaId);
                                data.setUri(String.valueOf(MediaStore.Images.Media.getContentUri(
                                        (volumeNameIdx >= 0) ? cursor.getString(volumeNameIdx) : null)));
                                data.setDescription((descriptionIdx >= 0) ? cursor.getString(descriptionIdx) : null);
                                dataMap.put(id, data);
                            }
                        }
                    }
                } else {
                    GLog.e(TAG, LogFlag.DL, null, () -> "getDataFormLocalMediaForAiSearch error cursor is null or cursor count is lower 0");
                }
            }
            return Collections.emptyList();
        });
        GLog.d(TAG, LogFlag.DL, () -> "getDataFormLocalMediaForAiSearch, count=" + dataMap.size() + ", cost=" + GLog.getTime(startTime));
    }

    /**
     * 在queryAiSearch中获取了图集详细信息，通过DmpQuery查询aiSearch接口所需的信息
     * @return Cursor DmpQuery的cursor,里面包含aiSearch接口所需的大部分信息，除了description和uri
     */
    private Cursor getDetailDataForAiSearch(String[] projection, String[] selectionArgs, String sortOrder) {
        StringBuilder selection = new StringBuilder();
        selection.append(COLUMN_ITEM_ID + IN + SQLGrammar.LEFT_BRACKETS);
        for (int i = 0; i < selectionArgs.length; i++) {
            selection.append(SQLGrammar.VARIABLE_PLACEHOLDER);
            if (i < selectionArgs.length - 1) {
                selection.append(SQLGrammar.COMMA_SPACE);
            }
        }
        selection.append(RIGHT_BRACKETS);
        Bundle bundle = new Bundle();
        bundle.putString("selection", selection.toString());
        bundle.putStringArray("selectionArgs", selectionArgs);
        bundle.putString("order", sortOrder);
        DmpQuery dmpQuery = new DmpQuery(projection, bundle);

        CompletableFuture<Cursor> future = CompletableFuture.supplyAsync(() -> {
            return dmpQuery.query();
        }).exceptionally(throwable -> {
            GLog.e(TAG, LogFlag.DL, throwable, () -> "[getDetailDataForAiSearch] Exception in CompletableFuture supplyAsync");
            return null;
        });

        try {
            return future.get();
        } catch (InterruptedException | ExecutionException e) {
            GLog.e(TAG, LogFlag.DL, e, () -> "Exception in dmp query");
            return null;
        }
    }

    /**
     * 为aiSearch的cursor填充信息
     */
    private void generateCursorForAiSearch(MatrixCursor targetCursor, Cursor dmpCursor, Map<Integer, AiSearchData> dataMap) {
        if ((dmpCursor != null) && (!mCompositeDataList.isEmpty())) {
            int idx = dmpCursor.getColumnIndex(COLUMN_ITEM_ID);
            int nameIdx = dmpCursor.getColumnIndex(COLUMN_ITEM_OPEN_DISPLAY_NAME);
            int dateTakenIdx = dmpCursor.getColumnIndex(COLUMN_DATE_TAKEN);
            int addressIdx = dmpCursor.getColumnIndex(COLUMN_ADDRESS);
            int mineTypeIdx = dmpCursor.getColumnIndex(COLUMN_MIME_TYPE);
            int pathIdx = dmpCursor.getColumnIndex(COLUMN_ITEM_DATA);
            int bucketNameIdx = dmpCursor.getColumnIndex(COLUMN_BUCKET_NAME);
            int ocrIdx = dmpCursor.getColumnIndex(COLUMN_OCR);
            int tagsIdx = dmpCursor.getColumnIndex(COLUMN_TAGS);
            while (dmpCursor.moveToNext()) {
                Integer id = (idx >= 0) ? dmpCursor.getInt(idx) : null;
                if (id != null) {
                    AiSearchData aiSearchData = dataMap.get(id);
                    if (aiSearchData != null) {
                        aiSearchData.setName(dmpCursor.getString(nameIdx));
                        aiSearchData.setDateTaken(dmpCursor.getLong(dateTakenIdx));
                        aiSearchData.setAddress(dmpCursor.getString(addressIdx));
                        aiSearchData.setMineType(dmpCursor.getString(mineTypeIdx));
                        aiSearchData.setPath(dmpCursor.getString(pathIdx));
                        aiSearchData.setBucketName(dmpCursor.getString(bucketNameIdx));
                        aiSearchData.setOcr(dmpCursor.getString(ocrIdx));
                        aiSearchData.setTags(dmpCursor.getString(tagsIdx));
                    }
                }
            }
            dmpCursor.close();
            for (int i = 0; i < dataMap.size(); i++) {
                AiSearchData aiSearchData = dataMap.get(mCompositeDataList.get(i).galleryId);
                if (aiSearchData != null) {
                    targetCursor.addRow(new Object[]{
                            aiSearchData.getMediaId(),
                            aiSearchData.getUri(),
                            aiSearchData.getName(),
                            aiSearchData.getDateTaken(),
                            aiSearchData.getAddress(),
                            aiSearchData.getMineType(),
                            aiSearchData.getPath(),
                            aiSearchData.getBucketName(),
                            aiSearchData.getDescription(),
                            aiSearchData.getOcr(),
                            aiSearchData.getTags(),
                            aiSearchData.getType()
                    });
                }
            }
        }
        targetCursor.moveToPosition(-1);
    }

    public Cursor getAlbumInfoByName(int type, String recommendName, String extraNames, boolean isFromSmart) {
        // If extraNames exists, we need collect all idList of each mName.
        if ((extraNames != null) && !TextUtils.isEmpty(extraNames) && !extraNames.equals("null")) {
            List<String> extraNameList = StringExtKt.convertStringToList(extraNames);
            if (extraNameList == null) {
                return null;
            }
            StringBuilder sb = new StringBuilder();
            for (String name : extraNameList) {
                Cursor cursor = null;
                try {
                    cursor = ContextGetter.context.getContentResolver().query(URI_SUGGESTION, ITEM_PROJECTION, "keyword",
                            new String[]{SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_LOCATION
                                    + "?" + QUERY_INPUT + "=" + name}, null);
                    if ((cursor != null) && cursor.moveToFirst()) {
                        String idList = cursor.getString(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil.SEARCH_RESULT_ID_LIST));
                        sb.append(idList);
                        sb.append(",");
                    }
                } catch (Exception e) {
                    GLog.e(TAG, "getAlbumInfoByName exception:", e);
                } finally {
                    IOUtils.closeQuietly(cursor);
                }
            }
            int index = sb.lastIndexOf(",");
            if (index != -1) {
                sb.deleteCharAt(index);
            }
            return searchAlbumInfoByIdList(sb.toString(), isFromSmart);
        }
        Cursor cursor = null;
        try {
            String uriPrefix = null;
            if (type == SearchType.TYPE_RECENT_TIME) {
                uriPrefix = SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_TIME;
            } else if (type == SearchType.TYPE_DATETIME) {
                uriPrefix = SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_TIME;
            } else if (type == SearchType.TYPE_LOCATION) {
                uriPrefix = SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_LOCATION;
            } else if (type == SearchType.TYPE_LABEL) {
                uriPrefix = SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_LABEL;
            } else {
                uriPrefix = SearchSuggestionProviderUtil.QUERY_ALL_DIMENSIONS;
            }
            cursor = ContextGetter.context.getContentResolver().query(URI_SUGGESTION, GROUP_PROJECTION, "keyword", new String[]{uriPrefix
                    + "?" + QUERY_INPUT + "=" + recommendName}, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                String idList = cursor.getString(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil
                        .SEARCH_RESULT_ID_LIST));
                return searchAlbumInfoByIdList(idList, isFromSmart);
            }
        } catch (Exception e) {
            GLog.e(TAG, "getAlbumInfoByName querySearchSuggestionProvider Exception:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return null;
    }

    public Cursor getVirtualAlbumInfoByPath(String path, int albumCount, int albumType, String queryArgs) {
        GLog.d(TAG, "getVirtualAlbumInfoByPath path:" + path + ", albumCount:" + albumCount + ", albumType:" + albumType);
        long startTime = System.currentTimeMillis();
        MediaSet mediaSet = DataManager.getMediaSet(Path.fromString(path));
        StringBuilder querySb = new StringBuilder();
        querySb.append(albumType);
        querySb.append(SYMBOL_STAR);
        if (mediaSet == null) {
            GLog.d(TAG, "getVirtualAlbumInfoByPath " + path + " error");
            querySb.append(albumCount);
            OpenAbilityTrackHelper.INSTANCE.trackOpenAbilityCall(OpenAbilityTrackHelper.INSTANCE.buildCommonParamMap(
                    OpenFeatureName.QUERY_ALBUMS,
                    path,
                    SearchTrackHelper.providerCallPackage,
                    CallResult.FAILED,
                    "getVirtualAlbumInfoByPath " + path + " error",
                    String.valueOf(GLog.getTime(startTime))
            ), OpenAbilityTrackHelper.INSTANCE.buildGetVirtualAlbumParamMap(querySb.toString()));
            return null;
        }
        Cursor cursor = getAllMediaItemCursor(mediaSet, buildWhereClause(queryArgs), buildPagingClause(queryArgs));
        querySb.append(albumCount);

        OpenAbilityTrackHelper.INSTANCE.trackOpenAbilityCall(OpenAbilityTrackHelper.INSTANCE.buildCommonParamMap(
                OpenFeatureName.QUERY_ALBUMS,
                path,
                SearchTrackHelper.providerCallPackage,
                CallResult.SUCCESS,
                TextUtil.EMPTY_STRING,
                String.valueOf(GLog.getTime(startTime))
        ), OpenAbilityTrackHelper.INSTANCE.buildGetVirtualAlbumParamMap(querySb.toString()));
        if (cursor == null) {
            GLog.d(TAG, " getVirtualAlbumInfoByPath cursor is null");
            return null;
        }
        cursor.moveToPosition(-1);
        return cursor;
    }

    public Cursor getMemoryMediaIDListBySetId(Context context, long mMemoriesId, boolean isFromSmart) {
        long time = System.currentTimeMillis();
        MatrixCursor resultCursor = null;
        if (isFromSmart) {
            resultCursor = new MatrixCursor(ALBUM_ITEM_COLUMN);
        } else {
            resultCursor = new MatrixCursor(MEMORIES_ITEM_PROJECTION);
        }
        Cursor cursor = null;
        try {
            //get from map, meta in map not order so get all item
            String whereClause = SET_ID + " = " + mMemoriesId + AND + DatabaseUtils.getOnlyLocalWhere();

            StringBuilder builder = new StringBuilder();
            builder.append("SELECT ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.MEDIA_ID + ", ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATA + ", ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATE_TAKEN + ", ");
            builder.append(LocalColumns.DATE_MODIFIED + ", ");
            builder.append(LocalColumns.DATE_ADDED + ", ");
            builder.append(LocalColumns.DURATION + ",");
            builder.append(LocalColumns.SIZE + ", ");
            builder.append(LocalColumns.WIDTH + ", ");
            builder.append(LocalColumns.HEIGHT + ", ");
            builder.append(LocalColumns.DISPLAY_NAME + ", ");
            builder.append(LocalColumns.MIME_TYPE + " FROM ");
            builder.append(GalleryStore.MemoriesSetmap.TAB + " INNER JOIN " + GalleryStore.GalleryMedia.TAB + " ON ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATA + "=" + GalleryStore.MemoriesSetmap.TAB + "." + LocalColumns.DATA);
            builder.append(" WHERE ");
            builder.append(whereClause);
            builder.append(" ORDER BY ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATE_TAKEN + " ASC");
            RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setQuerySql(builder.toString())
                    .setSqlArgs(null)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().rawQuery(rawQueryReq);
            if (cursor == null) {
                GLog.w(TAG, "getMemoryMediaIDListBySetId cursor is " + null);
                IOUtils.closeQuietly(resultCursor);
                return null;
            }
            if (cursor.getCount() <= 0) {
                GLog.w(TAG, "getMemoryMediaIDListBySetId cursor size is " + cursor.getCount());
                IOUtils.closeQuietly(resultCursor);
                return null;
            }
            final int idIndex = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
            final int filePathIndex = cursor.getColumnIndex(LocalColumns.DATA);
            final int dateTakenIndex = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
            final int sizeIndex = cursor.getColumnIndex(LocalColumns.SIZE);
            final int widthIndex = cursor.getColumnIndex(LocalColumns.WIDTH);
            final int heightIndex = cursor.getColumnIndex(LocalColumns.HEIGHT);
            final int mimeTypeIndex = cursor.getColumnIndex(LocalColumns.MIME_TYPE);
            final int dateAddIndex = cursor.getColumnIndex(LocalColumns.DATE_ADDED);
            final int displayNameIndex = cursor.getColumnIndex(LocalColumns.DISPLAY_NAME);
            final int dateModifiedIndex = cursor.getColumnIndex(LocalColumns.DATE_MODIFIED);
            final int durationIndex = cursor.getColumnIndex(LocalColumns.DURATION);
            while (cursor.moveToNext()) {
                int id = cursor.getInt(idIndex);
                String path = cursor.getString(filePathIndex);
                path = MultiAppUtils.INSTANCE.convertToSystemPath(path);
                long dateTaken = cursor.getLong(dateTakenIndex);
                String mimeType = cursor.getString(mimeTypeIndex);
                int duration = cursor.getInt(durationIndex);
                int width = cursor.getInt(widthIndex);
                int height = cursor.getInt(heightIndex);
                if (isFromSmart) {
                    String dateModified = cursor.getString(dateModifiedIndex);
                    resultCursor.addRow(new Object[]{id, path, mimeType, dateTaken, dateModified, duration, width, height});
                } else {
                    long size = cursor.getLong(sizeIndex);
                    int dateAdd = cursor.getInt(dateAddIndex);
                    String displayName = cursor.getString(displayNameIndex);
                    resultCursor.addRow(new Object[]{id, path, dateTaken, size, width, height, mimeType, dateAdd, displayName});
                }
            }
            long costTime = GLog.getTime(time);
            GLog.d(TAG, "getMemoryMediaIDListBySetId costTime = " + costTime);
        } catch (Exception e) {
            GLog.w(TAG, "getMemoryMediaIDListBySetId queryMemory exception:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        resultCursor.moveToPosition(-1);
        return resultCursor;
    }

    private Cursor getAllMediaItemCursor(MediaSet mediaSet, String whereClause, String pagingClause) {
        if (mediaSet.getInputEntry() == null) {
            return null;
        }
        boolean isAscOrder = isAscOrder(mediaSet.getInputEntry().getOrder());
        String sortStr = isAscOrder ? ORDER_ASC : ORDER_DESC;
        String orderClause = GalleryStore.GalleryMedia.LocalColumns.DATE_TAKEN + sortStr + COMMA + _ID + sortStr;
        String sql = SearchDBHelper.getAllMediaItemSql(
                mediaSet.getInputEntry().getWhere()
                        + AND + DatabaseUtils.getOnlyLocalWhere()
                        + AND + DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB)
                        + whereClause,
                orderClause,
                pagingClause
        );
        String[] args = SearchDBHelper.getAllMediaItemSqlArgs();

        return new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setQuerySql(sql)
                .setSqlArgs(args)
                .setConvert(new CursorConvert())
                .build()
                .exec();
    }

    private boolean isAscOrder(String order) {
        int descStart = order.indexOf(DESC.trim());
        int ascStart = order.indexOf(ASC.trim());
        return ((ascStart > INDEX_NOT_FOUND) && ((descStart == INDEX_NOT_FOUND) || (ascStart < descStart)));
    }

    /**
     * get cursor for recent 30 days by querying local_media,
     *
     * @return
     */
    private Cursor queryRecommendForRecentlyAdded() {
        MatrixCursor cursor = new MatrixCursor(ITEM_PROJECTION);

        SearchDBHelper.getMediaItemListByRecentlyAdded(getContext(), cursor);
        return cursor;
    }

    public Cursor getPersonsByGroupId(Context context, long groupId, boolean isFromSmart) {
        long time = System.currentTimeMillis();
        MatrixCursor resultCursor = null;
        if (isFromSmart) {
            resultCursor = new MatrixCursor(ALBUM_ITEM_COLUMN);
        } else {
            resultCursor = new MatrixCursor(ITEM_PROJECTION);
        }
        Cursor cursor = null;
        try {
            String[] selectionArgs = new String[]{String.valueOf(groupId)};
            StringBuilder builder = new StringBuilder();
            builder.append("SELECT ");
            builder.append(LocalColumns.MEDIA_ID + ", ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATA + ", ");
            builder.append(LocalColumns.DATE_TAKEN + ", ");
            builder.append(LocalColumns.DATE_MODIFIED + ", ");
            builder.append(LocalColumns.MIME_TYPE + ", ");
            builder.append(LocalColumns.DURATION + ", ");
            builder.append(LocalColumns.WIDTH + ", ");
            builder.append(LocalColumns.HEIGHT + " FROM ");
            builder.append(GalleryStore.ScanFace.TAB + " INNER JOIN " + GalleryStore.GalleryMedia.TAB + " ON ");
            builder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATA + "=" + GalleryStore.ScanFace.TAB + "." + LocalColumns.DATA);
            builder.append(" WHERE ");
            builder.append(GroupHelper.FACECLUSER_CLAUSE);
            builder.append(AND);
            builder.append(DatabaseUtils.getOnlyLocalWhere());
            builder.append(" GROUP BY " + LocalColumns.MEDIA_ID);
            RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setQuerySql(builder.toString())
                    .setSqlArgs(selectionArgs)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().rawQuery(rawQueryReq);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                IOUtils.closeQuietly(resultCursor);
                return null;
            }
            int mediaIdIndex = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
            int filePathIndex = cursor.getColumnIndex(LocalColumns.DATA);
            int dateTakenIndex = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
            int modifyIndex = cursor.getColumnIndex(LocalColumns.DATE_MODIFIED);
            int mimeTypeIndex = cursor.getColumnIndex(LocalColumns.MIME_TYPE);
            int durationIndex = cursor.getColumnIndex(LocalColumns.DURATION);
            int widthIndex = cursor.getColumnIndex(LocalColumns.WIDTH);
            int heightIndex = cursor.getColumnIndex(LocalColumns.HEIGHT);
            while (cursor.moveToNext()) {
                int mediaId = cursor.getInt(mediaIdIndex);
                String filePath = cursor.getString(filePathIndex);
                filePath = MultiAppUtils.INSTANCE.convertToSystemPath(filePath);
                long dateTaken = cursor.getLong(dateTakenIndex);
                int duration = cursor.getInt(durationIndex);
                int width = cursor.getInt(widthIndex);
                int height = cursor.getInt(heightIndex);
                if (isFromSmart) {
                    long dataModified = cursor.getLong(modifyIndex);
                    String mimeType = cursor.getString(mimeTypeIndex);
                    resultCursor.addRow(new Object[]{mediaId, filePath, mimeType, dateTaken, dataModified, duration, width, height});
                } else {
                    resultCursor.addRow(new Object[]{mediaId, filePath, dateTaken});
                }
            }
            long costTime = GLog.getTime(time);
            GLog.d(TAG, "getPersonsByGroupId costTime = " + costTime);
        } catch (Exception e) {
            GLog.e(TAG, "getPersonsByGroupId queryPerson Exception:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        resultCursor.moveToPosition(-1);
        return resultCursor;
    }

    private Cursor queryVirtualAlbumSet(String interfaceName, String[] selectionArgs, GalleryOpenListConfig.PackageBean packageBean) {
        long startTime = System.currentTimeMillis();
        String queryArgs = joinQueryArgs(selectionArgs);
        ArrayList<Integer> albumTypes = getAlbumTypesFromArgs(queryArgs);
        GLog.d(TAG, "queryVirtualAlbumSet albumTypes:" + albumTypes);
        if ((packageBean.getTags() == null) || (packageBean.getTags().isEmpty())) {
            GLog.d(TAG, "queryVirtualAlbumSet, packageBean or packageBean.getTags() is null");
            addOtherVirtualFail(interfaceName, startTime, albumTypes, "tags is null or empty");
            return null;
        }
        MediaSet mediaSet = DataManager.getMediaSet(Local.PATH_SET_VIRTUAL_ANY);
        if (mediaSet == null) {
            GLog.e(TAG, "queryVirtualAlbumSet, mediaSet is null, return null.");
            addOtherVirtualFail(interfaceName, startTime, albumTypes, "mediaSet is null");
            return null;
        }
        VirtualAlbumSet virtualAlbumSet = (VirtualAlbumSet) mediaSet;
        if (albumTypes.size() > 0) {
            ArrayList<Integer> filterAlbumTypes = filterAlbumType(packageBean, albumTypes);
            virtualAlbumSet.setAlbumTypes(filterAlbumTypes);
        } else {
            ArrayList<Integer> addAlbumTypes = addAlbumTypes(packageBean);
            virtualAlbumSet.setAlbumTypes(addAlbumTypes);
        }
        long reloadTime = System.currentTimeMillis();
        virtualAlbumSet.reload();
        long reloadEndTime = GLog.getTime(reloadTime);

        StringBuilder querySb = new StringBuilder();
        long fillCursorTime = System.currentTimeMillis();
        MatrixCursor cursor = fillCursor(virtualAlbumSet, querySb);
        long fillCursorEndTime = GLog.getTime(fillCursorTime);

        GLog.d(TAG, String.format(Locale.ENGLISH, "queryVirtualAlbumSet: reloadTime=%d, fillCursorTime=%d",
                reloadEndTime, fillCursorEndTime));
        cursor.moveToPosition(-1);
        // 获取虚拟图集成功
        OpenAbilityTrackHelper.INSTANCE.trackOpenAbilityCall(OpenAbilityTrackHelper.INSTANCE.buildCommonParamMap(
                OpenFeatureName.QUERY_ALBUMS,
                interfaceName,
                SearchTrackHelper.providerCallPackage,
                CallResult.SUCCESS,
                TextUtil.EMPTY_STRING,
                String.valueOf(GLog.getTime(startTime))
        ), OpenAbilityTrackHelper.INSTANCE.buildGetVirtualAlbumParamMap(querySb.toString()));

        return cursor;
    }

    private MatrixCursor fillCursor(VirtualAlbumSet virtualAlbumSet, StringBuilder querySb) {
        MatrixCursor cursor = new MatrixCursor(SmartOpenProvider.ALBUMS_COLUMN);
        ArrayList<Integer> albumTypes = virtualAlbumSet.getAlbumTypes();
        for (int i = 0; i < albumTypes.size(); i++) {
            long beginTime = System.currentTimeMillis();
            int type = albumTypes.get(i);
            HashMap<Integer, VirtualAlbumEntry> virtualAlbumEntryHashMap = virtualAlbumSet.getVirtualAlbumEntryHashMap();
            if ((virtualAlbumEntryHashMap == null) || virtualAlbumEntryHashMap.isEmpty()) {
                GLog.d(TAG, "fillCursor " + virtualAlbumEntryHashMap + " is null");
                return cursor;
            }
            VirtualAlbumEntry entry = virtualAlbumEntryHashMap.get(type);
            if (entry != null) {
                int count = entry.mCount;
                if (count == 0) {
                    GLog.d(TAG, "fillCursor " + entry.mBucketName + " entry.mCount = 0");
                    continue;
                }

                String name = entry.mBucketName;
                Path path = entry.mSetPath;
                int coverId = entry.mCoverId;
                String filePath = entry.mCoverPath;

                boolean isFavorite = type == ALBUM_TYPE_FAVORIES;
                String albumTag = "";
                int mediaType = entry.mCoverType;
                if (mediaType == LocalColumns.MEDIA_TYPE_IMAGE) {
                    albumTag = createAlbumTagJson(MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE, isFavorite);
                } else if (mediaType == LocalColumns.MEDIA_TYPE_VIDEO) {
                    albumTag = createAlbumTagJson(MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO, isFavorite);
                }
                String key = "?" + SET_PATH + "=" + path + "&type=" + SearchType.TYPE_ALBUM + "&" + ALBUM_TYPE + "=" + type
                        + "&" + IS_VIRTUAL + "=true" + "&count=" + count;
                cursor.addRow(new Object[]{key, name, count, SearchType.TYPE_ALBUM, name, coverId, filePath, albumTag});
                querySb.append(type);
                querySb.append(SYMBOL_STAR);
                querySb.append(count);
                querySb.append(SYMBOL_VERTICAL_LINE);
                GLog.d(TAG, String.format(Locale.ENGLISH,
                        "fillCursor: name=%s, path=%s, count=%d, fillCursorTime=%d",
                        name, path, count, GLog.getTime(beginTime)));
            }
        }
        if (-1 != querySb.lastIndexOf(SYMBOL_VERTICAL_LINE)) {
            querySb.deleteCharAt(querySb.lastIndexOf(SYMBOL_VERTICAL_LINE));
        }
        return cursor;
    }

    private ArrayList<Integer> filterAlbumType(GalleryOpenListConfig.PackageBean packageBean, ArrayList<Integer> albumTypes) {
        Iterator<Integer> iterable = albumTypes.iterator();
        while (iterable.hasNext()) {
            int type = iterable.next();
            switch (type) {
                case ALBUM_TYPE_ALL_PIC:
                    if (!packageBean.getTags().contains(VIRTUAL_ALL_PIC)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_FAVORIES:
                    if (!packageBean.getTags().contains(VIRTUAL_FAVORIES)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_VIDEO:
                    if (!packageBean.getTags().contains(VIRTUAL_VIDEO)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_GIF:
                    if (!packageBean.getTags().contains(VIRTUAL_GIF)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_CSHOT:
                    if (!packageBean.getTags().contains(VIRTUAL_CSHOT)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_PANORAMA:
                    if (!packageBean.getTags().contains(VIRTUAL_PANORAMA)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_FASTVIDEO:
                    if (!packageBean.getTags().contains(VIRTUAL_FASTVIDEO)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_SLOW_MOTION:
                    if (!packageBean.getTags().contains(VIRTUAL_SLOW_MOTION)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_PORTRAIT_BLUR:
                    if (!packageBean.getTags().contains(VIRTUAL_PORTRAIT_BLUR)) {
                        iterable.remove();
                    }
                    break;
                case ALBUM_TYPE_OLIVE:
                    if (!packageBean.getTags().contains(VIRTUAL_OLIVE)) {
                        iterable.remove();
                    }
                    break;
                default:
                    break;
            }
        }
        return albumTypes;
    }

    private ArrayList<Integer> addAlbumTypes(GalleryOpenListConfig.PackageBean packageBean) {
        ArrayList<Integer> albumTypes = new ArrayList<>();
        for (String tag : packageBean.getTags()) {
            switch (tag) {
                case VIRTUAL_ALL_PIC:
                    albumTypes.add(ALBUM_TYPE_ALL_PIC);
                    break;
                case VIRTUAL_FAVORIES:
                    albumTypes.add(ALBUM_TYPE_FAVORIES);
                    break;
                case VIRTUAL_VIDEO:
                    albumTypes.add(ALBUM_TYPE_VIDEO);
                    break;
                case VIRTUAL_GIF:
                    albumTypes.add(ALBUM_TYPE_GIF);
                    break;
                case VIRTUAL_CSHOT:
                    albumTypes.add(ALBUM_TYPE_CSHOT);
                    break;
                case VIRTUAL_PANORAMA:
                    albumTypes.add(ALBUM_TYPE_PANORAMA);
                    break;
                case VIRTUAL_FASTVIDEO:
                    albumTypes.add(ALBUM_TYPE_FASTVIDEO);
                    break;
                case VIRTUAL_SLOW_MOTION:
                    albumTypes.add(ALBUM_TYPE_SLOW_MOTION);
                    break;
                case VIRTUAL_PORTRAIT_BLUR:
                    albumTypes.add(ALBUM_TYPE_PORTRAIT_BLUR);
                    break;
                case VIRTUAL_OLIVE:
                    albumTypes.add(ALBUM_TYPE_OLIVE);
                    break;
                default:
                    break;
            }
        }
        return albumTypes;
    }

    private void addOtherVirtualFail(String path, long startTime, ArrayList<Integer> albumTypes, String reason) {
        StringBuilder querySb = new StringBuilder();
        for (Integer type : albumTypes) {
            querySb.append(type);
            querySb.append(SYMBOL_STAR);
            querySb.append(0);
            querySb.append(SYMBOL_VERTICAL_LINE);
        }
        if (-1 != querySb.lastIndexOf(SYMBOL_VERTICAL_LINE)) {
            querySb.deleteCharAt(querySb.lastIndexOf(SYMBOL_VERTICAL_LINE));
        }
        // 获取虚拟图集失败
        OpenAbilityTrackHelper.INSTANCE.trackOpenAbilityCall(OpenAbilityTrackHelper.INSTANCE.buildCommonParamMap(
                OpenFeatureName.QUERY_ALBUMS,
                path,
                SearchTrackHelper.providerCallPackage,
                CallResult.FAILED,
                reason,
                String.valueOf(GLog.getTime(startTime))
        ), OpenAbilityTrackHelper.INSTANCE.buildGetVirtualAlbumParamMap(querySb.toString()));
    }

    private String getCurrentType(Integer type) {
        String resultSet = null;
        if (type == SearchType.TYPE_PERSON) {
            resultSet = Value.PERSON;
        } else if (type == SearchType.TYPE_MEMORIES_ALBUM) {
            resultSet = Value.MEMORIES;
        } else if (type == SearchType.TYPE_RECENT_TIME) {
            resultSet = Value.RECENT;
        } else if (type == SearchType.TYPE_LOCATION) {
            resultSet = Value.LOCATION;
        } else if (type == SearchType.TYPE_DATETIME) {
            resultSet = Value.DATE;
        } else if (type == SearchType.TYPE_LABEL) {
            resultSet = Value.LABEL;
        } else if (type == SearchType.TYPE_OCR) {
            resultSet = Value.OCR;
        } else if (type == SearchType.TYPE_MULTI_LABEL) {
            resultSet = Value.MULTI_LABEL;
        } else if (type == SearchType.TYPE_ALBUM) {
            resultSet = Value.ALBUM;
        } else if (type == SearchType.TYPE_CHILD_LABEL) {
            resultSet = Value.CHILD_LABEL;
        } else if (type == SearchType.TYPE_FILE_NAME) {
            resultSet = Value.FILE_NAME;
        } else {
            resultSet = Value.ALBUM;
        }
        return resultSet;
    }

    private int getTypeFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryType = queryUri.getQueryParameter(COLUMN_TYPE);
        int queryValue = -1;
        if ((queryType != null) && !queryType.isEmpty()) {
            try {
                queryValue = Integer.parseInt(queryType);
            } catch (NumberFormatException e) {
                GLog.e(TAG, "getTypeFromArgs", e);
            }
        }
        return queryValue;
    }

    private int getStartIndexFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryStartIndex = queryUri.getQueryParameter(START_INDEX);
        int startIndex = -1;
        if ((queryStartIndex != null) && !queryStartIndex.isEmpty()) {
            try {
                startIndex = Integer.parseInt(queryStartIndex);
            } catch (NumberFormatException e) {
                GLog.e(TAG, "getStartIndexFromArgs", e);
            }
        }
        return startIndex;
    }

    private boolean getIsNeedVirtualFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryValue = queryUri.getQueryParameter(IS_NEED_VIRTUAL);
        return STRING_TRUE.equalsIgnoreCase(queryValue);
    }

    private String getOffsetFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(OFFSET.trim());
    }

    private String getLimitFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(LIMIT.trim());
    }

    private String getIdListFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(COLUMN_ID_LIST);
    }

    private String getMimeTypeFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(LocalColumns.MIME_TYPE);
    }

    private String getDateTakenFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(LocalColumns.DATE_TAKEN);
    }

    private String getDataModifiedFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(LocalColumns.DATE_MODIFIED);
    }

    /**
     * 解析query方法的SelectionArgs中是否包含“isOlive=true",若包含，后续会执行过滤出olive返回给第三方
     * @param args SelectionArgs拼接的字符串
     * @return SelectionArgs中包含isOlive=true 则返回true
     */
    private boolean shouldFilterOlive(String args) {
        Uri queryUri = Uri.parse(args);
        String queryValue = queryUri.getQueryParameter(FILTER_OLIVE);
        return STRING_TRUE.equalsIgnoreCase(queryValue);
    }


    private String getRecommendNameFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(COLUMN_NAME);
    }

    private String getExtraNameFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(COLUMN_EXTRA_NAMES);
    }

    private boolean getFromFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryValue = queryUri.getQueryParameter(STRING_IS_FROM_SMART);
        return STRING_TRUE.equalsIgnoreCase(queryValue);
    }

    private boolean getIsVirtualArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryValue = queryUri.getQueryParameter(IS_VIRTUAL);
        return STRING_TRUE.equalsIgnoreCase(queryValue);
    }

    private int getCountFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryCount = queryUri.getQueryParameter(COLUMN_COUNT);
        int count = -1;
        if ((queryCount != null) && !queryCount.isEmpty()) {
            try {
                count = Integer.parseInt(queryCount);
            } catch (NumberFormatException e) {
                GLog.e(TAG, "getCountFromArgs", e);
            }
        }
        return count;
    }

    private int getMemoryIdFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String memoriesID = queryUri.getQueryParameter(MEMORY_ID);
        int id = -1;
        if ((memoriesID != null) && !memoriesID.isEmpty()) {
            try {
                id = Integer.parseInt(memoriesID);
            } catch (NumberFormatException e) {
                GLog.e(TAG, "getMemoryIdFromArgs", e);
            }
        }
        return id;
    }

    private int getAlbumTypeFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryAlbumType = queryUri.getQueryParameter(ALBUM_TYPE);
        int albumType = -1;
        if ((queryAlbumType != null) && !queryAlbumType.isEmpty()) {
            try {
                albumType = Integer.parseInt(queryAlbumType);
            } catch (NumberFormatException e) {
                GLog.e(TAG, "getAlbumTypeFromArgs", e);
            }
        }
        return albumType;
    }

    private String getSetPathFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        return queryUri.getQueryParameter(SET_PATH);
    }

    private int getSearchResultTypeFromArgs(String args) {
        Uri queryUri = Uri.parse(args);
        String queryType = queryUri.getQueryParameter(SEARCH_RESULT_TYPE);
        int queryValue = -1;
        if ((queryType != null) && !queryType.isEmpty()) {
            try {
                queryValue = Integer.parseInt(queryType);
            } catch (NumberFormatException e) {
                GLog.e(TAG, "getSearchResultTypeFromArgs", e);
            }
        }
        return queryValue;
    }

    private String getQueryWord(String keyword, boolean toLowerCase) {
        Uri queryUri = Uri.parse(keyword);
        String word = queryUri.getQueryParameter(QUERY_INPUT);
        GLog.v(TAG, "[getQueryWord] word = " + word + ", keyword = " + keyword);
        if ((word != null) && toLowerCase) {
            return word.toLowerCase();
        }
        return word;
    }

    public static Uri getUriById(Long id) {
        return MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL, id);
    }

    private ArrayList<Integer> getAlbumTypesFromArgs(String args) {
        ArrayList<Integer> albumTypes = new ArrayList<>();
        try {
            Uri queryUri = Uri.parse(args);
            String types = queryUri.getQueryParameter(ALBUM_TYPES);
            if (TextUtils.isEmpty(types)) {
                return albumTypes;
            }
            String[] type = types.split(",");
            for (String str : type) {
                if (!TextUtils.isEmpty(str)) {
                    albumTypes.add(Integer.parseInt(str));
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getAlbumTypesFromArgs", e);
        }
        return albumTypes;
    }

    private String createAlbumTagJson(boolean isOcr, int mediaType) {
        JSONObject albumTagJson = new JSONObject();
        try {
            if (isOcr) {
                albumTagJson.put(ALBUM_TAG_OCR, true);
                JSONObject cropArea = new JSONObject();
                cropArea.put(ALBUM_TAG_CROPTYPE, 0);
                albumTagJson.put(ALBUM_TAG_CROPAREA, cropArea);
            } else {
                albumTagJson.put(ALBUM_TAG_COVERTYPE, mediaType);
            }
        } catch (JSONException e) {
            GLog.e(TAG, "createAlbumTagJson", e);
        }
        return albumTagJson.toString();
    }

    private Cursor queryTimelineCount(String[] projection) {
        long time = System.currentTimeMillis();
        if (!TextUtils.equals(Arrays.toString(TIMELINE_COUNT_COLUMN), Arrays.toString(projection))) {
            GLog.e(TAG, "queryTimelineCount, projection wrong, projection: " + Arrays.toString(projection));
            return null;
        }
        String whereClause = MediaSetUtils.getTimelineWhereClause(getContext(), MEDIA_TYPE_SUPPORT_ALL, false)
                + AND + DatabaseUtils.getOnlyLocalWhere();
        MatrixCursor matrixCursor = new MatrixCursor(TIMELINE_COUNT_COLUMN);
        try (Cursor cursor = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(TIMELINE_COUNT_COLUMN)
                .setWhere(whereClause)
                .setConvert(new CursorConvert())
                .build().exec()) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    matrixCursor.addRow(new Object[]{cursor.getInt(0), cursor.getInt(1)});
                }
            }
        }
        GLog.d(TAG, "queryTimelineCount, count: " + matrixCursor.getCount()
                + ", time: " + GLog.getTime(time));
        return matrixCursor;
    }

    private Cursor queryRecycleFiles(String[] projection, String selection, String[] selectionArgs, String sortOrder) {
        long time = System.currentTimeMillis();
        if (!TextUtils.equals(Arrays.toString(RECYCLE_FILES_COLUMN), Arrays.toString(projection))) {
            GLog.e(TAG, "queryRecycleFiles, projection wrong, projection: " + Arrays.toString(projection));
            return null;
        }
        String order = sortOrder;
        //sortOrder为空才使用相册的排序
        if (TextUtils.isEmpty(sortOrder)) {
            order = DatabaseUtils.getOrderClauseDateRecycled(false);
        }

        final String[] recycleProjection = new String[]{
                LocalColumns.MEDIA_TYPE,
                LocalColumns.DATA,
                LocalColumns.SIZE,
                LocalColumns.DATE_ADDED,
                LocalColumns.TITLE,
                LocalColumns.DURATION
        };

        //保持和相册最近删除一样的查询条件，不需要再从外部传入条件占位参数。故将setWhereArgs(selectionArgs)去掉
        StringBuilder whereClause = new StringBuilder();
        whereClause.append(LEFT_BRACKETS).append(LEFT_BRACKETS);
        whereClause.append(DatabaseUtils.getOnlyTrashedWhere(GalleryStore.GalleryMedia.TAB)).append(SQLGrammar.AND);
        whereClause.append(DatabaseUtils.getDataValidWhere()).append(RIGHT_BRACKETS);
        whereClause.append(OR).append(INVALID).append(EQUAL).append(INVALID_MARK_DELAY_RECYCLE);
        whereClause.append(RIGHT_BRACKETS).append(AND);
        whereClause.append(LEFT_BRACKETS).append(DatabaseUtils.getWhereIsNotCShot());
        whereClause.append(OR + _ID + IN + LEFT_BRACKETS);
        whereClause.append(DatabaseUtils.getCShotBestPicWhereOrderByOrigData(GalleryStore.GalleryMedia.TAB, _ID));
        whereClause.append(RIGHT_BRACKETS).append(RIGHT_BRACKETS);

        MatrixCursor matrixCursor = new MatrixCursor(RECYCLE_FILES_COLUMN);
        try (Cursor cursor = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(recycleProjection)
                .setWhere(whereClause.toString())
                .setOrderBy(order)
                .setConvert(new CursorConvert())
                .build().exec()) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int mediaTypeIdx = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                int dataIdx = cursor.getColumnIndex(LocalColumns.DATA);
                int sizeIdx = cursor.getColumnIndex(LocalColumns.SIZE);
                int dateAddedIdx = cursor.getColumnIndex(LocalColumns.DATE_ADDED);
                int titleIdx = cursor.getColumnIndex(LocalColumns.TITLE);
                int durationIdx = cursor.getColumnIndex(LocalColumns.DURATION);
                while (cursor.moveToNext()) {
                    matrixCursor.addRow(new Object[]{
                            cursor.getInt(mediaTypeIdx),
                            cursor.getString(dataIdx),
                            cursor.getInt(sizeIdx),
                            cursor.getLong(dateAddedIdx),
                            cursor.getString(titleIdx),
                            cursor.getLong(durationIdx)});
                }
            }
        }
        GLog.d(TAG, "queryRecycleFiles, count: " + matrixCursor.getCount()
                + ", time: " + GLog.getTime(time));
        return matrixCursor;
    }

    @SuppressLint("getLastPathSegmentRisk")
    public Cursor getShareCursor(Uri uri, String[] projection) {
        GLog.d(TAG, "getShareCursor, query shareHeif");
        String path = uri.getLastPathSegment();
        final File file = new File(path);

        if (projection == null) {
            projection = OPEN_SHARE_ITEM_COLUMN;
        }

        String[] cols = new String[projection.length];
        Object[] values = new Object[projection.length];

        int i = 0;
        for (String col : projection) {
            if (COLUMN_ITEM_OPEN_DATA.equals(col)) {
                cols[i] = COLUMN_ITEM_OPEN_DATA;
                values[i++] = path;
            } else if (COLUMN_ITEM_OPEN_DISPLAY_NAME.equals(col)) {
                cols[i] = COLUMN_ITEM_OPEN_DISPLAY_NAME;
                values[i++] = file.getName();
            } else if (COLUMN_ITEM_OPEN_SIZE.equals(col)) {
                cols[i] = COLUMN_ITEM_OPEN_SIZE;
                values[i++] = file.length();
            }
        }

        cols = copyOf(cols, i);
        values = copyOf(values, i);

        final MatrixCursor cursor = new MatrixCursor(cols, 1);
        cursor.addRow(values);
        return cursor;
    }

    @SuppressLint("getLastPathSegmentRisk")
    @Override
    public ParcelFileDescriptor openFile(Uri paramUri, String mode)
            throws FileNotFoundException {
        //路径穿越检测
        if (ProviderSecurityUtils.isIllegalParameterUri(paramUri)) {
            throw new SecurityException("Permission denied");
        }
        int match = URI_MATCHER.match(paramUri);
        if (match == SHARE) {
            GLog.d(TAG, "openFile, open shareHeif for fd");
            File file = new File(paramUri.getLastPathSegment());
            if (file.exists()) {
                String parent = file.getParent() + File.separator;
                if (!isOpenFileAccessibleDir(parent)) {
                    throw new SecurityException("Permission denied");
                }
                //heif转码临时文件当前支持只读模式
                return ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY);
            }
            throw new FileNotFoundException("file not found");
        } else {
            return super.openFile(paramUri, mode);
        }
    }

    private boolean isOpenFileAccessibleDir(String dirPath) {
        String internalPath = OplusEnvironment.getInternalPath();
        if (TextUtils.isEmpty(internalPath)) {
            return false;
        }
        String pathConvertSecurityDir = internalPath + AppConstants.Path.getPATH_CONVERT_SECURITY_DIR();
        String pathConvertHeifDir = internalPath + AppConstants.Path.getPATH_CONVERT_CACHE_DIR();
        return Objects.equals(pathConvertSecurityDir, dirPath)
                || Objects.equals(pathConvertHeifDir, dirPath);
    }

    /**
     * 三方调用删除 回收站文件
     *
     * @param selection     selection
     * @param selectionArgs selectionArgs
     * @return count
     */
    public static int deleteRecycleFiles(String packageName, String selection, String[] selectionArgs) {
        Cursor cursor = null;
        int deleteMediaStoreCount = 0;
        int deleteRecycleDbCount = 0;
        int imageCount = 0;
        int videoCount = 0;
        String fullSelection = DatabaseUtils.getOnlyTrashedWhere(GalleryStore.GalleryMedia.TAB)
                + AND + DatabaseUtils.getOnlyLocalWhere()
                + AND + selection;
        try {
            cursor = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(new String[]{
                            LocalColumns.MEDIA_TYPE,
                            LocalColumns.MEDIA_ID,
                            LocalColumns.DATA
                    })
                    .setWhere(fullSelection)
                    .setWhereArgs(selectionArgs)
                    .setConvert(new CursorConvert())
                    .build().exec();
            ArrayList<String> mediaIdList = new ArrayList<>();
            ArrayList<ContentProviderOperation> deleteOps = new ArrayList<>();
            if ((cursor != null) && (cursor.getCount() > 0)) {
                final int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                final int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                final int indexFilePath = cursor.getColumnIndex(LocalColumns.DATA);
                while (cursor.moveToNext()) {
                    int mediaType = cursor.getInt(indexMediaType);
                    if (mediaType == LocalColumns.MEDIA_TYPE_IMAGE) {
                        imageCount++;
                    } else if (mediaType == LocalColumns.MEDIA_TYPE_VIDEO) {
                        videoCount++;
                    }
                    String mediaId = cursor.getString(indexMediaId);
                    String filePath = cursor.getString(indexFilePath);
                    Uri uri = MediaStoreUriHelper.getUri(mediaId, FilePathUtils.getVolumeName(filePath), mediaType);
                    mediaIdList.add(mediaId);
                    deleteOps.add(ContentProviderOperation.newDelete(uri).build());
                }
            }
            if (deleteOps.isEmpty() || mediaIdList.isEmpty()) {
                GLog.d(TAG, "deleteRecycleFiles isEmpty");
                return 0;
            }
            // 通过媒体库删除文件
            ContentProviderResult[] providerResults = ContextGetter.context.getContentResolver()
                    .applyBatch(MediaStore.AUTHORITY, deleteOps);
            if ((providerResults == null) || (providerResults.length <= 0)) {
                GLog.d(TAG, "deleteRecycleFiles providerResults = null or length = 0 ");
                return 0;
            }
            deleteMediaStoreCount = providerResults.length;
            String where = DatabaseUtils.getOnlyTrashedWhere(GalleryStore.GalleryMedia.TAB)
                    + AND + DatabaseUtils.getOnlyLocalWhere()
                    + AND + DatabaseUtils.getWhereIn(LocalColumns.MEDIA_ID, mediaIdList);
            if (!TextUtils.isEmpty(where)) {
                // 删除相册db记录
                deleteRecycleDbCount = new DeleteReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                        .setWhere(where)
                        .setWhereArgs(selectionArgs)
                        .build().exec();
            }

        } catch (Exception e) {
            GLog.e(TAG, "deleteRecycleFiles", e);
        } finally {
            String versionName = AppVersionUtils.getVersionName(ContextGetter.context, packageName);
            GLog.d(TAG, "deleteRecycleFiles, del packageName: " + packageName
                    + ", versionName: " + versionName
                    + ", selection: " + selection
                    + ", selectionArgs: " + Arrays.toString(selectionArgs)
                    + ", imageCount: " + imageCount
                    + ", videoCount: " + videoCount
                    + ", deleteMediaStoreCount: " + deleteMediaStoreCount
                    + ", deleteRecycleDbCount: " + deleteRecycleDbCount
            );
            RecycleTrackHelper.trackGalleryOpenProviderDeleteRecycle(packageName, versionName, deleteMediaStoreCount, deleteRecycleDbCount);
            IOUtils.closeQuietly(cursor);
        }
        return deleteMediaStoreCount;
    }

    private String createAlbumTagJson(int mediaType, boolean isFavorite) {
        JSONObject albumTagJson = new JSONObject();
        try {
            albumTagJson.put(ALBUM_TAG_COVERTYPE, mediaType);
            albumTagJson.put(ALBUM_TAG_FAVORITE, isFavorite);
        } catch (JSONException e) {
            GLog.e(TAG, "createAlbumTagJson", e);
        }
        return albumTagJson.toString();
    }

    public class QueryResult {

        private int mType = -1;

        public QueryResult(int mType) {
            this.mType = mType;
        }

        public Cursor getCursorFromResult(String interfaceName, String keyword, PackageBean packageBean) {
            if (mType == -1) {
                GLog.d(TAG, "getCursorFromResult,mType is wrong");
                return null;
            }
            if ((packageBean == null) || (packageBean.getTags() == null)) {
                GLog.d(TAG, "getCursorFromResult,packageBean or packageBean.getTags() is null");
                return null;
            }
            long getCursorStartTime = System.currentTimeMillis();
            MatrixCursor cursor = new MatrixCursor(RECOMMEND_COLUMN_GROUP);
            boolean force = getForceQuery(keyword);
            StringBuilder querySb = new StringBuilder();
            StringBuilder countSb = new StringBuilder();
            fillRecommendCursor(cursor, force, packageBean, querySb, countSb);
            GLog.d(TAG, String.format(Locale.ENGLISH, "time for get recommend cursor is %d ms", GLog.getTime(getCursorStartTime)));
            if (-1 != countSb.lastIndexOf(SYMBOL_VERTICAL_LINE)) {
                countSb.deleteCharAt(countSb.lastIndexOf(SYMBOL_VERTICAL_LINE));
            }
            if (-1 != querySb.lastIndexOf(SYMBOL_VERTICAL_LINE)) {
                querySb.deleteCharAt(querySb.lastIndexOf(SYMBOL_VERTICAL_LINE));
            }
            OpenAbilityTrackHelper.INSTANCE.trackOpenAbilityCall(OpenAbilityTrackHelper.INSTANCE.buildCommonParamMap(
                    OpenFeatureName.QUERY_ALBUMS,
                    interfaceName,
                    SearchTrackHelper.providerCallPackage,
                    CallResult.SUCCESS,
                    TextUtil.EMPTY_STRING,
                    String.valueOf(GLog.getTime(getCursorStartTime))
            ), OpenAbilityTrackHelper.INSTANCE.buildGetRecommendAlbumParamMap(querySb.toString(), "", countSb.toString()));

            cursor.moveToPosition(-1);
            return cursor;
        }

        private void fillRecommendCursor(MatrixCursor cursor, boolean force,
                                         PackageBean packageBean, StringBuilder querySb, StringBuilder countSb) {
            Uri uri = URI_SUGGESTION;
            if (((mType & TYPE_RECOMMEND_YEAR) != 0) && (packageBean.getTags().contains(RECOMMEND_YEAR))) {
                GTrace.traceBegin("GalleryOpenProvider.RecommendYear");
                long startTime = System.currentTimeMillis();
                queryRecommendYear(uri, cursor, force, countSb);
                GLog.d(TAG, LogFlag.DL, String.format(Locale.ENGLISH, "time for recommend year is %d ms", GLog.getTime(startTime)));
                querySb.append(Value.YEAR).append(SYMBOL_STAR).append(GLog.getTime(startTime)).append(SYMBOL_VERTICAL_LINE);
                GTrace.traceEnd();
            }
            if (((mType & TYPE_RECOMMEND_MONTH) != 0) && (packageBean.getTags().contains(RECOMMEND_MONTH))) {
                GTrace.traceBegin("GalleryOpenProvider.RecommendMonth");
                long startTime = System.currentTimeMillis();
                queryRecommendMonth(uri, cursor, force, countSb);
                GLog.d(TAG, LogFlag.DL, String.format(Locale.ENGLISH, "time for recommend month is %d ms", GLog.getTime(startTime)));
                querySb.append(Value.MONTH).append(SYMBOL_STAR).append(GLog.getTime(startTime)).append(SYMBOL_VERTICAL_LINE);
                GTrace.traceEnd();
            }
            if (((mType & TYPE_RECOMMEND_FESTIVAL) != 0) && (packageBean.getTags().contains(RECOMMEND_FESTIVAL))) {
                GTrace.traceBegin("GalleryOpenProvider.RecommendFestival");
                long startTime = System.currentTimeMillis();
                queryRecommendFestival(uri, cursor, force, countSb);
                GLog.d(TAG, LogFlag.DL, String.format(Locale.ENGLISH, "time for recommend festival is %d ms", GLog.getTime(startTime)));
                querySb.append(Value.FESTIVAL).append(SYMBOL_STAR).append(GLog.getTime(startTime)).append(SYMBOL_VERTICAL_LINE);
                GTrace.traceEnd();
            }
            if (((mType & TYPE_RECOMMEND_LOCATION) != 0) && (packageBean.getTags().contains(RECOMMEND_LOCATION))) {
                GTrace.traceBegin("GalleryOpenProvider.RecommendLocation");
                long startTime = System.currentTimeMillis();
                queryRecommendLocation(uri, cursor, force, countSb);
                GLog.d(TAG, LogFlag.DL, String.format(Locale.ENGLISH, "time for recommend location is %d ms", GLog.getTime(startTime)));
                querySb.append(Value.LOCATION).append(SYMBOL_STAR).append(GLog.getTime(startTime)).append(SYMBOL_VERTICAL_LINE);
                GTrace.traceEnd();
            }
            if (((mType & TYPE_RECOMMEND_PERSON) != 0) && (packageBean.getTags().contains(RECOMMEND_PERSON))) {
                GTrace.traceBegin("GalleryOpenProvider.RecommendPerson");
                long startTime = System.currentTimeMillis();
                queryRecommendPerson(uri, cursor, force, countSb);
                GLog.d(TAG, LogFlag.DL, String.format(Locale.ENGLISH, "time for recommend person is %d ms", GLog.getTime(startTime)));
                querySb.append(Value.PERSON).append(SYMBOL_STAR).append(GLog.getTime(startTime)).append(SYMBOL_VERTICAL_LINE);
                GTrace.traceEnd();
            }
            if (((mType & TYPE_RECOMMEND_LABEL) != 0) && (packageBean.getTags().contains(RECOMMEND_LABEL))) {
                GTrace.traceBegin("GalleryOpenProvider.RecommendLabel");
                long startTime = System.currentTimeMillis();
                queryRecommendLabel(uri, cursor, force, countSb);
                GLog.d(TAG, LogFlag.DL, String.format(Locale.ENGLISH, "time for recommend label is %d ms", GLog.getTime(startTime)));
                querySb.append(Value.LABEL).append(SYMBOL_STAR).append(GLog.getTime(startTime)).append(SYMBOL_VERTICAL_LINE);
                GTrace.traceEnd();
            }
            if (((mType & TYPE_RECOMMEND_MEMORIES) != 0) && (packageBean.getTags().contains(RECOMMEND_MEMORIES))) {
                GTrace.traceBegin("GalleryOpenProvider.RecommendMemories");
                long startTime = System.currentTimeMillis();
                queryRecommendMemories(uri, cursor, force, countSb);
                GLog.d(TAG, LogFlag.DL, String.format(Locale.ENGLISH, "time for recommend memories is %d ms", GLog.getTime(startTime)));
                querySb.append(Value.MEMORIES).append(SYMBOL_STAR).append(GLog.getTime(startTime)).append(SYMBOL_VERTICAL_LINE);
                GTrace.traceEnd();
            }
        }

        private void queryRecommendMemories(Uri uri, MatrixCursor cursor, boolean force, StringBuilder countSb) {
            String albumTag = "";
            try (Cursor originalCursor = ContextGetter.context.getContentResolver().query(uri, null,
                    null, new String[]{QUERY_RECOMMEND_MEMORIES_FORCE + force}, null)) {
                if (originalCursor != null) {
                    int indexCoverId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_ID);
                    int indexName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMNNAME);
                    int indexCount = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_COUNT);
                    int indexMemoriesId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEMORIES_ID);
                    int indexMemoriesSubTitle = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEMORIES_SUB_TITLE);
                    while (originalCursor.moveToNext()) {
                        int coverId = originalCursor.getInt(indexCoverId);
                        String name = originalCursor.getString(indexName);
                        int count = originalCursor.getInt(indexCount);
                        int memoryId = originalCursor.getInt(indexMemoriesId);
                        String memorySubTitle = originalCursor.getString(indexMemoriesSubTitle);
                        int currentType = SearchType.TYPE_MEMORIES_ALBUM;
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("?").append(COLUMN_TYPE).append("=").append(currentType)
                                .append("&").append(COLUMN_EXTRA_NAMES).append("=")
                                .append("&").append(MEMORY_ID).append("=").append(memoryId);
                        String key = stringBuilder.toString();
                        Uri coverUri = getUriById((long) coverId);
                        cursor.addRow(new Object[]{currentType, name, key, coverId, coverUri, count, albumTag, memorySubTitle,
                                memoryId, null});
                        GLog.d(TAG, String.format(Locale.ENGLISH,
                                "queryRecommendMemories: name=%s, coverId=%d, count=%d, key=%s, albumTag=%s",
                                name, coverId, count, key, albumTag));
                    }
                    countSb.append(Value.MEMORIES);
                    countSb.append(SYMBOL_STAR);
                    countSb.append(originalCursor.getCount());
                    countSb.append(SYMBOL_VERTICAL_LINE);
                }
            } catch (Exception e) {
                GLog.e(TAG, "queryRecommendMemories exception:", e);
            }
        }

        private void queryRecommendLabel(Uri uri, MatrixCursor cursor, boolean force, StringBuilder countSb) {
            try (Cursor originalCursor = ContextGetter.context.getContentResolver().query(uri, null,
                    null, new String[]{QUERY_RECOMMEND_LABEL_FORCE + force}, null)) {
                if (originalCursor != null) {
                    int indexCoverId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_ID);
                    int indexName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMNNAME);
                    int indexCount = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_COUNT);
                    int indexMediaType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE);
                    int indexSceneId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_SCENE_ID);
                    while (originalCursor.moveToNext()) {
                        int coverId = originalCursor.getInt(indexCoverId);
                        String name = originalCursor.getString(indexName);
                        int count = originalCursor.getInt(indexCount);
                        int mediaType = originalCursor.getInt(indexMediaType);
                        int sceneId = originalCursor.getInt(indexSceneId);
                        int currentType = SearchType.TYPE_LABEL;
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("?").append(COLUMN_TYPE).append("=").append(currentType)
                                .append("&").append(COLUMN_NAME).append("=").append(name)
                                .append("&").append(COLUMN_EXTRA_NAMES).append("=null");
                        String key = stringBuilder.toString();
                        Uri coverUri = getUriById((long) coverId);
                        String albumTag = createAlbumTagJson(false, mediaType);
                        cursor.addRow(new Object[]{currentType, name, key, coverId, coverUri, count, albumTag, null, null, sceneId});
                        GLog.d(TAG, String.format(Locale.ENGLISH,
                                "queryRecommendLabel: name=%s, coverId=%d, count=%d, key=%s, albumTag=%s",
                                name, coverId, count, key, albumTag));
                    }
                    countSb.append(Value.LABEL);
                    countSb.append(SYMBOL_STAR);
                    countSb.append(originalCursor.getCount());
                    countSb.append(SYMBOL_VERTICAL_LINE);
                }
            } catch (Exception e) {
                GLog.e(TAG, "queryRecommendLabel exception:", e);
            }
        }

        private void queryRecommendPerson(Uri uri, MatrixCursor cursor, boolean force, StringBuilder countSb) {
            try (Cursor originalCursor = ContextGetter.context.getContentResolver().query(uri, null,
                    null, new String[]{QUERY_RECOMMEND_PERSON_FORCE + force}, null)) {
                if (originalCursor != null) {
                    int indexCoverId = originalCursor.getColumnIndex(LocalColumns.MEDIA_ID);

                    //we can find a certain person  album by group id
                    int indexGroupId = originalCursor.getColumnIndex(GROUP_ID);
                    int indexName = originalCursor.getColumnIndex(GROUP_NAME);
                    int indexCount = originalCursor.getColumnIndex(COLUMN_COUNT);
                    int indexLeft = originalCursor.getColumnIndex(LEFT);
                    int indexTop = originalCursor.getColumnIndex(TOP);
                    int indexRight = originalCursor.getColumnIndex(RIGHT);
                    int indexBottom = originalCursor.getColumnIndex(BOTTOM);
                    int indexWidth = originalCursor.getColumnIndex(THUMB_W);
                    int indexHeight = originalCursor.getColumnIndex(THUMB_H);
                    int indexMediaType = originalCursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                    while (originalCursor.moveToNext()) {
                        long coverId = originalCursor.getLong(indexCoverId);
                        String groupId = originalCursor.getString(indexGroupId);
                        String coverName = originalCursor.getString(indexName);
                        int currentType = SearchType.TYPE_PERSON;
                        int count = originalCursor.getInt(indexCount);
                        float left = originalCursor.getFloat(indexLeft);
                        float top = originalCursor.getFloat(indexTop);
                        float right = originalCursor.getFloat(indexRight);
                        float bottom = originalCursor.getFloat(indexBottom);
                        float width = originalCursor.getFloat(indexWidth);
                        float height = originalCursor.getFloat(indexHeight);
                        int mediaType = originalCursor.getInt(indexMediaType);
                        FaceInfo faceInfo = new FaceInfo(coverName, coverId, left, top, right, bottom, width, height, mediaType);
                        Uri coverUri = getUriById(coverId);
                        String albumTag = createAlbumTagJson(faceInfo);
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("?").append(COLUMN_TYPE).append("=").append(currentType)
                                .append("&").append(COLUMN_NAME).append("=").append(groupId)
                                .append("&").append(COLUMN_EXTRA_NAMES).append("=null");
                        String key = stringBuilder.toString();
                        cursor.addRow(new Object[]{currentType, coverName, key, coverId, coverUri, count, albumTag, null, null, null});
                        GLog.d(TAG, String.format(Locale.ENGLISH,
                                "queryRecommendPerson: name=%s, coverId=%d, count=%d, key=%s, albumTag=%s",
                                coverName, coverId, count, key, albumTag));
                    }
                    countSb.append(Value.PERSON);
                    countSb.append(SYMBOL_STAR);
                    countSb.append(originalCursor.getCount());
                    countSb.append(SYMBOL_VERTICAL_LINE);
                }
            } catch (Exception e) {
                GLog.e(TAG, "queryRecommendPerson exception:", e);
            }
        }

        private void queryRecommendLocation(Uri uri, MatrixCursor cursor, boolean force, StringBuilder countSb) {
            try (Cursor originalCursor = ContextGetter.context.getContentResolver().query(uri, null,
                    null, new String[]{QUERY_RECOMMEND_LOCATION_FORCE + force}, null)) {

                if (originalCursor != null) {
                    int indexCoverId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_ID);
                    int indexName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMNNAME);
                    int indexCount = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_COUNT);
                    int indexExtrasName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_EXTRANAMES);
                    int indexMediaType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE);
                    while (originalCursor.moveToNext()) {
                        int coverId = originalCursor.getInt(indexCoverId);
                        String name = originalCursor.getString(indexName);
                        if (TextUtils.isEmpty(name)) {
                            continue;
                        }
                        int count = originalCursor.getInt(indexCount);
                        String extrasName = originalCursor.getString(indexExtrasName);
                        int mediaType = originalCursor.getInt(indexMediaType);
                        int currentType = SearchType.TYPE_LOCATION;
                        Uri coverUri = getUriById((long) coverId);
                        String albumTag = createAlbumTagJson(false, mediaType);
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("?").append(COLUMN_TYPE).append("=").append(currentType)
                                .append("&").append(COLUMN_NAME).append("=").append(name)
                                .append("&").append(COLUMN_EXTRA_NAMES).append("=").append(extrasName);
                        String key = stringBuilder.toString();
                        cursor.addRow(new Object[]{currentType, name, key, coverId, coverUri, count, albumTag, null, null, null});
                    }
                    countSb.append(Value.LOCATION);
                    countSb.append(SYMBOL_STAR);
                    countSb.append(originalCursor.getCount());
                    countSb.append(SYMBOL_VERTICAL_LINE);
                }
            } catch (Exception e) {
                GLog.e(TAG, "queryRecommendLocation exception:", e);
            }
        }

        private void queryRecommendFestival(Uri uri, MatrixCursor cursor, boolean force, StringBuilder countSb) {
            try (Cursor originalCursor = ContextGetter.context.getContentResolver().query(uri, null,
                    null, new String[]{QUERY_RECOMMEND_FESTIVAL_FORCE + force}, null)) {
                if (originalCursor != null) {
                    int indexCoverId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_ID);
                    int indexName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMNNAME);
                    int indexCount = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_COUNT);
                    int indexMediaType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE);
                    while (originalCursor.moveToNext()) {
                        int coverId = originalCursor.getInt(indexCoverId);
                        String name = originalCursor.getString(indexName);
                        int count = originalCursor.getInt(indexCount);
                        int mediaType = originalCursor.getInt(indexMediaType);
                        int currentType = SearchType.TYPE_DATETIME;
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("?").append(COLUMN_TYPE).append("=").append(currentType)
                                .append("&").append(COLUMN_NAME).append("=").append(name)
                                .append("&").append(COLUMN_EXTRA_NAMES).append("=");
                        String key = stringBuilder.toString();
                        Uri coverUri = getUriById((long) coverId);
                        String albumTag = createAlbumTagJson(false, mediaType);
                        cursor.addRow(new Object[]{currentType, name, key, coverId, coverUri, count, albumTag, null, null, null});
                        GLog.d(TAG, String.format(Locale.ENGLISH,
                                "queryRecommendFestival: name=%s, coverId=%d, count=%d, key=%s, albumTag=%s",
                                name, coverId, count, key, albumTag));
                    }
                    countSb.append(Value.FESTIVAL);
                    countSb.append(SYMBOL_STAR);
                    countSb.append(originalCursor.getCount());
                    countSb.append(SYMBOL_VERTICAL_LINE);
                }
            } catch (Exception e) {
                GLog.e(TAG, "queryRecommendFestival exception:", e);
            }
        }

        private void queryRecommendMonth(Uri uri, MatrixCursor cursor, boolean force, StringBuilder countSb) {
            try (Cursor originalCursor = ContextGetter.context.getContentResolver().query(uri, null, null,
                    new String[]{QUERY_RECOMMEND_MONTH_FORCE + force}, null)) {
                if (originalCursor != null) {
                    int indexCoverId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_ID);
                    int indexName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMNNAME);
                    int indexCount = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_COUNT);
                    int indexMediaType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE);
                    while (originalCursor.moveToNext()) {
                        int coverId = originalCursor.getInt(indexCoverId);
                        String name = originalCursor.getString(indexName);
                        int count = originalCursor.getInt(indexCount);
                        int mediaType = originalCursor.getInt(indexMediaType);
                        int currentType = SearchType.TYPE_DATETIME;
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("?").append(COLUMN_TYPE).append("=").append(currentType)
                                .append("&").append(COLUMN_NAME).append("=").append(name)
                                .append("&").append(COLUMN_EXTRA_NAMES).append("=");
                        String key = stringBuilder.toString();
                        Uri coverUri = getUriById((long) coverId);
                        String albumTag = createAlbumTagJson(false, mediaType);
                        cursor.addRow(new Object[]{currentType, name, key, coverId, coverUri, count, albumTag, null, null, null});
                        GLog.d(TAG, String.format(Locale.ENGLISH,
                                "queryRecommendMonth: name=%s, coverId=%d, count=%d, key=%s, albumTag=%s",
                                name, coverId, count, key, albumTag));
                    }
                    countSb.append(Value.MONTH);
                    countSb.append(SYMBOL_STAR);
                    countSb.append(originalCursor.getCount());
                    countSb.append(SYMBOL_VERTICAL_LINE);
                }
            } catch (Exception e) {
                GLog.e(TAG, "queryRecommendMonth exception:", e);
            }
        }

        private void queryRecommendYear(Uri uri, MatrixCursor cursor, boolean force, StringBuilder countSb) {
            try (Cursor originalCursor = ContextGetter.context.getContentResolver().query(uri, null, null,
                    new String[]{QUERY_RECOMMEND_YEAR_FORCE + force}, null)) {
                if (originalCursor != null) {
                    int indexCoverId = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_ID);
                    int indexName = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMNNAME);
                    int indexCount = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_COUNT);
                    int indexMediaType = originalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE);
                    while (originalCursor.moveToNext()) {
                        int coverId = originalCursor.getInt(indexCoverId);
                        String name = originalCursor.getString(indexName);
                        int count = originalCursor.getInt(indexCount);
                        int mediaType = originalCursor.getInt(indexMediaType);
                        int currentType = SearchType.TYPE_DATETIME;
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("?").append(COLUMN_TYPE).append("=").append(currentType)
                                .append("&").append(COLUMN_NAME).append("=").append(name)
                                .append("&").append(COLUMN_EXTRA_NAMES).append("=");
                        String key = stringBuilder.toString();
                        Uri coverUri = getUriById((long) coverId);
                        String albumTag = createAlbumTagJson(false, mediaType);
                        cursor.addRow(new Object[]{currentType, name, key, coverId, coverUri, count, albumTag, null, null, null});
                        GLog.d(TAG, String.format(Locale.ENGLISH,
                                "queryRecommendYear: name=%s, coverId=%d, count=%d, key=%s, albumTag=%s",
                                name, coverId, count, key, albumTag));
                    }
                    countSb.append(Value.YEAR);
                    countSb.append(SYMBOL_STAR);
                    countSb.append(originalCursor.getCount());
                    countSb.append(SYMBOL_VERTICAL_LINE);
                }
            } catch (Exception e) {
                GLog.e(TAG, "queryRecommendYear exception:", e);
            }
        }
    }

    private String[] copyOf(String[] original, int newLength) {
        final String[] result = new String[newLength];
        System.arraycopy(original, 0, result, 0, newLength);
        return result;
    }

    private Object[] copyOf(Object[] original, int newLength) {
        final Object[] result = new Object[newLength];
        System.arraycopy(original, 0, result, 0, newLength);
        return result;
    }

    public static class FaceInfo {
        private String mName;
        private long mMediaId;
        private float mLeft;
        private float mTop;
        private float mRight;
        private float mBottom;
        private float mWidth;
        private float mHeight;
        private int mMediaType;

        public FaceInfo(String name, long mediaId, float left, float top, float right, float bottom, float width, float height,
                        int mediaType) {
            this.mName = name;
            this.mMediaId = mediaId;
            this.mLeft = left;
            this.mTop = top;
            this.mRight = right;
            this.mBottom = bottom;
            this.mWidth = width;
            this.mHeight = height;
            this.mMediaType = mediaType;
        }

        public int getMediaType() {
            return mMediaType;
        }

        public void setMediaType(int mMediaType) {
            this.mMediaType = mMediaType;
        }

        public long getMediaId() {
            return mMediaId;
        }

        public void setMediaId(int mediaId) {
            this.mMediaId = mediaId;
        }

        public String getName() {
            return mName;
        }

        public void setName(String name) {
            this.mName = name;
        }

        public float getLeft() {
            return mLeft;
        }

        public void setLeft(float left) {
            this.mLeft = left;
        }

        public float getTop() {
            return mTop;
        }

        public void setTop(float top) {
            this.mTop = top;
        }

        public float getRight() {
            return mRight;
        }

        public void setRight(float right) {
            this.mRight = right;
        }

        public float getBottom() {
            return mBottom;
        }

        public void setBottom(float bottom) {
            this.mBottom = bottom;
        }

        public float getWidth() {
            return mWidth;
        }

        public void setWidth(float width) {
            this.mWidth = width;
        }

        public float getHeight() {
            return mHeight;
        }

        public void setHeight(float height) {
            this.mHeight = height;
        }
    }
    public class AiSearchData {
        private int mId;
        private int mMediaId;
        private String mUri;
        private String mName;
        private long mDateTaken;
        private String mAddress;
        private String mMineType;
        private String mPath;
        private String mBucketName;
        private String mDescription;
        private String mOcr;
        private String mTags;
        private String mType;

        public AiSearchData(int id) {
            this.mId = id;
        }

        public int getId() {
            return mId;
        }

        public void setMediaId(int mediaId) {
            this.mMediaId = mediaId;
        }

        public int getMediaId() {
            return mMediaId;
        }

        public void setUri(String uri) {
            this.mUri = uri;
        }

        public String getUri() {
            return mUri;
        }

        public void setName(String name) {
            this.mName = name;
        }

        public String getName() {
            return mName;
        }

        public void setDateTaken(long dateTaken) {
            this.mDateTaken = dateTaken;
        }

        public long getDateTaken() {
            return mDateTaken;
        }

        public void setAddress(String address) {
            this.mAddress = address;
        }

        public String getAddress() {
            return mAddress;
        }

        public void setMineType(String format) {
            this.mMineType = format;
        }

        public String getMineType() {
            return mMineType;
        }

        public void setPath(String path) {
            this.mPath = path;
        }

        public String getPath() {
            return mPath;
        }

        public void setBucketName(String bucketName) {
            this.mBucketName = bucketName;
        }

        public String getBucketName() {
            return mBucketName;
        }

        public void setDescription(String description) {
            this.mDescription = description;
        }

        public String getDescription() {
            return mDescription;
        }

        public void setOcr(String ocr) {
            this.mOcr = ocr;
        }

        public String getOcr() {
            return mOcr;
        }

        public void setTags(String tags) {
            this.mTags = tags;
        }

        public String getTags() {
            return mTags;
        }

        public void addType(int type) {
            int searchType = type;
            if (type == SearchType.TYPE_OCR_EMBEDDING) {
                searchType = SearchType.TYPE_OCR;
            }
            if (mType == null) {
                mType = String.valueOf(searchType);
            } else if (!mType.contains(String.valueOf(searchType))) {
                mType = mType + COMMA + searchType;
            }
        }

        public String getType() {
            return mType;
        }
    }
}
