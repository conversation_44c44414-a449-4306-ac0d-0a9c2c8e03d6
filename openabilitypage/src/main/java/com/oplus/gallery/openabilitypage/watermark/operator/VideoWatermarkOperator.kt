/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : VideoWatermarkOperator.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/12/06
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** tan<PERSON><PERSON><PERSON>@Apps.Gallery          2024/12/06    1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.openabilitypage.watermark.operator

import android.content.ContentResolver
import android.graphics.Rect
import android.graphics.RectF
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.Bundle
import android.os.ParcelFileDescriptor
import android.os.Parcelable
import android.system.Int64Ref
import android.system.Os
import androidx.annotation.WorkerThread
import androidx.core.graphics.toRect
import androidx.core.net.toFile
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.ExportVideoEngineFactory
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.ExportVideoEngineType
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.ExportVideoOptions
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.IExportVideoCallback
import com.oplus.gallery.foundation.fileaccess.FileConstants.FileMode
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getFileDescriptorSafely
import com.oplus.gallery.foundation.util.ext.isContentUri
import com.oplus.gallery.foundation.util.ext.isFileUri
import com.oplus.gallery.frameextractor_lib.IMediaMetadataRetriever
import com.oplus.gallery.framework.abilities.watermark.IWatermarkAbility
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.olive_decoder.utils.toHexString
import com.oplus.gallery.openabilitypage.watermark.WatermarkDefinition.InputParam
import com.oplus.gallery.openabilitypage.watermark.WatermarkDefinition.OutputParam
import com.oplus.gallery.openabilitypage.watermark.WatermarkDefinition.OutputParam.RESULT_CODE
import com.oplus.gallery.openabilitypage.watermark.WatermarkDefinition.ResultCode
import com.oplus.gallery.openabilitypage.watermark.filesaver.WatermarkFileHelper
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_270
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_90
import com.oplus.gallery.standard_lib.codec.player.effect.EffectUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditor.data.VideoSpec
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import java.io.File
import java.io.FileDescriptor
import java.io.FileInputStream

/**
 * 视频资源水印去除：只针对视频相关处理
 * 使用范围：仅支持在当前目录下使用，不对外
 * 功能：
 * [hasWatermark] 是否有水印
 * [removeWatermark] 去除水印
 */
internal class VideoWatermarkOperator : IWatermarkOperator {

    override fun hasWatermark(input: Bundle): Bundle {
        // 获取原图Uri
        val srcUri = (input.getParcelable(InputParam.SRC_URI) as? Uri) ?: return Bundle().apply {
            putInt(RESULT_CODE, ResultCode.SRC_FILE_NOT_FOUND)
            GLog.e(TAG, LogFlag.DL) { "[hasWatermark] ${InputParam.SRC_URI} is not Uri" }
        }

        ContextGetter.context.getAppAbility<IWatermarkAbility>().use { ability ->
            ability ?: return Bundle().apply {
                GLog.e(TAG, LogFlag.DL, "[hasWatermark] cannot get ability")
                putInt(RESULT_CODE, ResultCode.ERROR_IDENTIFY_WATERMARK)
            }

            ability.newWatermarkFileOperator(srcUri, OpenFileMode.MODE_READ).use { fileOperator ->
                fileOperator ?: return Bundle().apply {
                    GLog.e(TAG, LogFlag.DL, "[hasWatermark] cannot get fileOperator")
                    putInt(RESULT_CODE, ResultCode.ERROR_IDENTIFY_WATERMARK)
                }

                val watermarkInfo = fileOperator.readWatermarkInfo().takeIf {
                    it.hasWatermark()
                } ?: return Bundle().apply {
                    GLog.d(TAG, LogFlag.DL, "[hasWatermark] hasWatermark = false")
                    putInt(RESULT_CODE, ResultCode.SUCCESS)
                    putBoolean(OutputParam.WATERMARK_RESULT, false)
                }

                return Bundle().apply {
                    putInt(RESULT_CODE, ResultCode.SUCCESS)
                    putBoolean(
                        OutputParam.WATERMARK_RESULT,
                        /*
                         * 当期仅支持olive大师水印的判断，后续有了其他加并列条件即可
                         * 1.大师水印只有videoDisplayRect 为有效值时，才认为有视频有水印
                         */
                        watermarkInfo.aiWatermarkFileExtendInfo?.videoDisplayRect?.isEmpty?.not() ?: false
                    )
                }
            }
        }
    }

    override fun removeWatermark(input: Bundle): Bundle {
        val startTime = System.currentTimeMillis()
        // 获取原图Uri
        val srcUri = (input.getParcelable(InputParam.SRC_URI) as? Uri) ?: return Bundle().apply {
            putInt(RESULT_CODE, ResultCode.SRC_FILE_NOT_FOUND)
            GLog.e(TAG, LogFlag.DL) { "[removeWatermark] ${InputParam.SRC_URI} is not Uri" }
        }

        // 获取输出目录
        val dstUri = (input.getParcelable(InputParam.DST_URI) as? Uri) ?: return Bundle().apply {
            putInt(RESULT_CODE, ResultCode.DST_FILE_NOT_FOUND)
            GLog.e(TAG, LogFlag.DL) { "[removeWatermark] ${InputParam.DST_URI} is not Uri" }
        }

        val result = srcUri.getFileDescriptorSafely(ContextGetter.context, FileMode.MODE_READ, TAG).use { srcFd ->
            srcFd ?: return Bundle().apply {
                putInt(RESULT_CODE, ResultCode.SRC_FILE_READ_NO_PERMISSION)
                GLog.e(TAG, LogFlag.DL) { "[removeWatermark] ${InputParam.SRC_URI} cannot read" }
            }

            dstUri.getFileDescriptorSafely(ContextGetter.context, FileMode.MODE_WRITE, TAG).use { dstFd ->
                dstFd ?: return Bundle().apply {
                    putInt(RESULT_CODE, ResultCode.DST_FILE_WRITE_NO_PERMISSION)
                    GLog.e(TAG, LogFlag.DL) { "[removeWatermark] ${InputParam.DST_URI} cannot write" }
                }

                // 如果视频位于文件中的某部分，则需要传入 "video_info"
                val videoInfo = input.getParcelable(KEY_VIDEO_INFO) as? VideoInfo
                removeAiMasterWatermark(srcUri, srcFd, dstUri, dstFd, videoInfo).run {
                    Bundle().apply { putInt(RESULT_CODE, this@run) }
                }
            }
        }

        GLog.d(TAG, LogFlag.DL) {
            "[removeWatermark] finish. " +
                    "$RESULT_CODE = ${result.getInt(RESULT_CODE, ResultCode.INVALID).toHexString()}, " +
                    "cost = ${System.currentTimeMillis() - startTime}ms, " +
                    "${InputParam.SRC_URI} = $srcUri, " +
                    "${InputParam.DST_URI} = $dstUri"
        }
        return result
    }

    private fun removeAiMasterWatermark(
        srcUri: Uri,
        srcFd: ParcelFileDescriptor,
        dstUri: Uri,
        dstFd: ParcelFileDescriptor,
        videoInfo: VideoInfo?
    ): Int {
        // 获取实际视频的范围
        val videoDisplayRect: RectF = ContextGetter.context.getAppAbility<IWatermarkMasterAbility>().use { ability ->
            ability ?: run {
                GLog.d(TAG, LogFlag.DL, "[removeAiMasterWatermark] ability is null")
                return ResultCode.ERROR_REMOVE_WATERMARK
            }

            ability.newWatermarkFileOperator(srcUri, OpenFileMode.MODE_READ).use { fileOperator ->
                fileOperator ?: run {
                    GLog.d(TAG, LogFlag.DL, "[removeAiMasterWatermark] fileOperator is null")
                    return ResultCode.ERROR_REMOVE_WATERMARK
                }

                val watermarkInfo = fileOperator.readWatermarkInfo().takeIf {
                    // 当前能力仅支持视频AI大师水印，其他资源均认为是不支持的操作
                    it.hasWatermark() && it.isAiMasterWatermark()
                } ?: run {
                    GLog.d(TAG, LogFlag.DL, "[removeAiMasterWatermark] hasAiMasterWatermark = false")
                    return ResultCode.UNSUPPORTED_OP
                }

                // 必须是有效的videoDisplayRect
                watermarkInfo.aiWatermarkFileExtendInfo?.videoDisplayRect?.takeIf {
                    it.isEmpty.not()
                } ?: run {
                    /*
                     * 会存在没有videoDisplayRect但是大师水印类型的olive，如：大师水印上后普通的悬浮水印模式，
                     * 这里认为其是无水印的，并非异常流程
                     */
                    GLog.w(TAG, LogFlag.DL, "[removeAiMasterWatermark] videoDisplayRect is null")
                    return ResultCode.SRC_FILE_NO_WATERMARK
                }
            }
        }

        // 美摄并不支持多线程操作，这里需要全局锁，避免多线程操作
        synchronized(VideoWatermarkOperator::class.java) {
            return removeWatermarkByRect(srcUri, srcFd, dstUri, dstFd, videoDisplayRect.toRect(), videoInfo)
        }
    }

    @Suppress("LongMethod")
    private fun removeWatermarkByRect(
        srcUri: Uri,
        srcFd: ParcelFileDescriptor,
        dstUri: Uri,
        dstFd: ParcelFileDescriptor,
        displayRect: Rect,
        videoInfo: VideoInfo?
    ): Int = runBlocking {
        if (displayRect.isEmpty) {
            // 无水印
            GLog.e(TAG, LogFlag.DL) { "[removeWatermarkByRect] invalid displayRect" }
            return@runBlocking ResultCode.ERROR_REMOVE_WATERMARK
        }

        val specification = getVideoSpecification(srcFd.fileDescriptor, videoInfo) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[removeWatermarkByRect] Failed to get specification" }
            return@runBlocking ResultCode.ERROR_REMOVE_WATERMARK
        }

        val videoOffset = videoInfo?.filePartOffset ?: 0L
        val videoLength = videoInfo?.filePartLength ?: Os.fstat(srcFd.fileDescriptor).st_size
        // 去水印能力如果原图有eis数据，需要迁移原来图片中的eis数据到新导出的视频
        val eisData = getOliveVideoEisData(srcUri, videoOffset)?.let { if (it.isNotEmpty()) "LivePhotoExtension$it" else null }

        return@runBlocking ExportVideoEngineFactory.create(ExportVideoEngineType.MEICAM).apply {
            initEngine(ContextGetter.context, srcUri, videoOffset, videoLength, specification)
        }.use { exportEngine ->
            // 添加视频片段
            exportEngine.appendVideoClip(srcUri, videoOffset, videoLength)

            // 剪裁
            exportEngine.cropVideo(displayRect)

            // 导出，美摄为异步导出，所以这里转为同步。
            val exportResult = CompletableDeferred<Boolean>()
            exportEngine.registerExportVideoCallback(object : IExportVideoCallback {
                override fun onProgress(position: Int) = Unit

                override fun onComplete(isSuccess: Boolean) {
                    GLog.d(TAG, LogFlag.DL) { "[removeWatermarkByRect] complete, isSuccess: $isSuccess" }
                    exportResult.complete(isSuccess)
                }

                override fun onFailed(isSuccess: Boolean) {
                    GLog.w(TAG, LogFlag.DL) { "[removeWatermarkByRect] failed, isSuccess: $isSuccess" }
                    exportResult.complete(isSuccess)
                }
            })

            /**
             * 获取美摄的导出路径
             */
            val dstFile: File = if (dstUri.isFileUri() && (videoInfo != null) && dstUri.path.isNullOrEmpty().not()) {
                dstUri.toFile()
            } else {
                WatermarkFileHelper.createFileByUriType(srcUri, FILE_TAG)
            } ?: return@use ResultCode.ERROR_REMOVE_WATERMARK

            val isSaveStart = exportEngine.saveVideo(
                filePath = dstFile.absolutePath,
                options = ExportVideoOptions(System.currentTimeMillis(), displayRect.height(), eisData)
            )
            if (!isSaveStart) {
                // 启动导出流程失败
                clearCurrentOwnerTmpFile(dstUri, dstFile)
                GLog.e(TAG, LogFlag.DL) { "[removeWatermarkByRect] isSaveStart = false" }
                return@use ResultCode.ERROR_REMOVE_WATERMARK
            }

            // 等待导出到临时文件完成
            val saveResult = exportResult.await()
            if (saveResult.not()) {
                clearCurrentOwnerTmpFile(dstUri, dstFile)
                GLog.e(TAG, LogFlag.DL) { "[removeWatermarkByRect] saveResult = false" }
                return@use ResultCode.ERROR_REMOVE_WATERMARK
            }

            // 导出到 dst
            val exportToDstResult = if (dstUri.path == dstFile.absolutePath) {
                // 已直接导出到 dst
                true
            } else {
                // 导出到临时文件，需转到 dst
                withContext(Dispatchers.IO) {
                    ParcelFileDescriptor.open(dstFile, ParcelFileDescriptor.MODE_READ_ONLY).use {
                        extractClipToDst(it.fileDescriptor, dstFd.fileDescriptor, 0L)
                    }
                }
            }
            clearCurrentOwnerTmpFile(dstUri, dstFile)
            return@use if (exportToDstResult) {
                ResultCode.SUCCESS
            } else {
                ResultCode.ERROR_REMOVE_WATERMARK
            }
        }
    }

    /**
     *获取实况图片中的eis数据，需要迁移到新导出的视频中
     *
     * @param uri 实况图片文件uri
     * @param offset 视频在文件中的偏移量
     */
    private fun getOliveVideoEisData(uri: Uri, offset: Long): String? {
        if (offset <= 0) {
            return null
        }

        val eisData = EffectUtil.getEisData(uri = uri, offset = offset)
        return eisData
    }

    /**
     * 导出视频
     * @param srcUri 可以是视频文件或者olive文件
     * @param dstUri 目标地址, 目前只支持file
     * @param offset 视频数据在文件的位置，可以不传，默认从初始位置读取
     * @param length 视频数据的长度，可以不传，默认取整个src文件数据
     */
    @WorkerThread
    fun exportVideo(srcUri: Uri, dstUri: Uri, offset: Long = 0L, length: Long = 0L): Boolean {
        if (srcUri.isContentUri().not()) {
            // 当前仅支持content类型srcUri 和 file类型dstUri
            GLog.e(TAG, LogFlag.DL) { "[exportVideo] uri is supported" }
            return false
        }
        srcUri.getFileDescriptorSafely(ContextGetter.context, FileMode.MODE_READ, TAG)?.use { srcPfd ->
            dstUri.getFileDescriptorSafely(ContextGetter.context, FileMode.MODE_READ_WRITE, TAG)?.use { dstPfd ->
                return extractClipToDst(srcPfd.fileDescriptor, dstPfd.fileDescriptor, offset, length)
            }
        }
        GLog.e(TAG, LogFlag.DL) { "[exportVideo] open uri fail." }
        return false
    }

    /**
     * 如果 length 为 0 则复制从 offset 起的所有 bytes.
     */
    private fun extractClipToDst(srcFd: FileDescriptor, dstFd: FileDescriptor, offset: Long = 0L, length: Long = 0L): Boolean {
        val count = if (length == 0L) {
            Os.fstat(srcFd).st_size - offset
        } else {
            length
        }
        if (count <= 0) {
            GLog.w(TAG, LogFlag.DL) { "[extractClipToDst] count is invalid! count: $count" }
            return false
        }
        return kotlin.runCatching {
            Os.sendfile(dstFd, srcFd, Int64Ref(offset), count)
            true
        }.getOrElse {
            GLog.e(TAG, LogFlag.DL) { "[extractClipToDst] error: $it" }
            false
        }
    }

    private fun getVideoSpecification(fd: FileDescriptor, videoInfo: VideoInfo?): VideoSpec? {
        runCatching {
            FileInputStream(fd).use { inputStream ->
                IMediaMetadataRetriever.create(IMediaMetadataRetriever.MediaMetadataRetrieverVendor.TBL).use { mediaMetadataRetriever ->
                    videoInfo?.let {
                        mediaMetadataRetriever.setDataSource(inputStream.fd, it.filePartOffset, it.filePartLength)
                    } ?: mediaMetadataRetriever.setDataSource(inputStream.fd)

                    val width = mediaMetadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toInt() ?: 0
                    val height = mediaMetadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toInt() ?: 0
                    val rotation = mediaMetadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.toInt() ?: 0
                    if (rotation == DEGREE_90 || rotation == DEGREE_270) {
                        return VideoSpec(height, width, ENGINE_VIDEO_FPS)
                    } else {
                        return VideoSpec(width, height, ENGINE_VIDEO_FPS)
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DF) { "[getVideoSpecification] retrieve video size got error: ${it.message}" }
        }
        return null
    }

    /**
     * 清理掉当前Operator创建的临时文件
     * 遵循谁创建，谁对文件生命周期管控
     * @param dstUri 调用者传入的输出地址 相册内为[ContentResolver.SCHEME_FILE] ,相册外为[ContentResolver.SCHEME_CONTENT]
     */
    private fun clearCurrentOwnerTmpFile(dstUri: Uri, tmpDstFile: File) {
        // 校验合法性，外部传入的file指向文件，不清理，不是本Operator创建
        if (dstUri.isFileUri() || dstUri.path.isNullOrEmpty()) {
            return
        }

        // 外部输出地址与内部临时文件一样时，不做操作，证明不是本Operator创建，文件生命周期由创建者管控
        if (dstUri.path.equals(tmpDstFile.absolutePath)) {
            return
        }

        WatermarkFileHelper.deleteFile(tmpDstFile)
    }

    @Parcelize
    internal data class VideoInfo(
        /**
         * 视频位于文件中的位置 offset
         */
        val filePartOffset: Long = 0L,
        /**
         * 视频在文件中的长度
         */
        val filePartLength: Long = 0L
    ) : Parcelable

    companion object {
        private const val TAG = "VideoWatermarkOperator"

        internal const val KEY_VIDEO_INFO = "video_info"
        private const val ENGINE_VIDEO_FPS = 30F
        private const val FILE_TAG = "video"
    }
}