/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OpenThumbnailManager.kt
 ** Description:
 **     Open Thumbnail Manager
 **
 ** Version: 1.0
 ** Date: 2020-07-14
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2020/07/14     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.openabilitypage.thumbnal

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.provider.MediaStore
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.track.OpenAbilityTrackHelper
import com.oplus.gallery.openabilitypage.GalleryOpenProvider.TAG_GET_THUMBNAIL
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BytesBuffer
import com.oplus.gallery.foundation.cache.memorycache.BytesBufferPool
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.tracing.constant.OpenAbilityTrackConstant
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.ocs.gallery.galleryService.OptionsParcelable
import com.oplus.gallery.openabilitypage.OpenAbilityCallManager.ERROR_CODE_ARG
import com.oplus.gallery.openabilitypage.OpenAbilityCallManager.ERROR_CODE_NO_CACHE
import com.oplus.gallery.openabilitypage.OpenAbilityCallManager.ERROR_CODE_OTHER
import com.oplus.gallery.openabilitypage.OpenAbilityCallManager.SUCCESS_CODE
import com.oplus.gallery.standard_lib.util.os.ContextGetter

object OpenThumbnailManager {

    private const val TAG = "OpenThumbnailManager"

    /**
     * same ratio thumbnail which width and height <=512
     */
    private const val TYPE_MICRO_THUMBNAIL_SCALE = 1

    /**
     * grid thumbnail which width and height is 256
     */
    private const val TYPE_MICRO_THUMBNAIL = 2

    private const val TYPE_MICRO_THUMBNAIL_SCALE_VIDEO_SIZE = 512

    // fixme wangrunxin 在屏幕状态变化后(横屏、竖屏、折叠屏等)更新thumbnailType
    private val microThumbnailType = ThumbnailSizeUtils.getMicroThumbnailKey()

    @JvmStatic
    fun executeThumbnailRequest(id: Int, filePath: String, modify: Long, mediaType: Int, type: Int, o: OptionsParcelable?): Bitmap? {
        val options = transformOptionsParcelableToOptions(o)
        return executeThumbnailRequest(id, filePath, modify, mediaType, type, options).bitmap
    }

    @JvmStatic
    @Suppress("LongMethod")
    fun executeThumbnailRequest(
        id: Int,
        filePath: String,
        modify: Long,
        mediaType: Int,
        type: Int,
        option: BitmapFactory.Options?
    ): ThumbnailResponse {
        GLog.d(TAG, LogFlag.DL) {
            "executeThumbnailRequest id:$id, modify:$modify, mediaType:$mediaType, type:$type, OptionsParcelable:$option"
        }
        val startTime = System.currentTimeMillis()
        val buffer = BytesBufferPool.get(0)
        val resultThumbnail = ThumbnailResponse()
        val cachingAbility = ContextGetter.context.getAppAbility<ICachingAbility>() ?: return resultThumbnail
        try {
            val thumbnailType = getThumbnailType(type) ?: let {
                GLog.d(TAG, LogFlag.DL) { "executeThumbnailRequest, thumbnail type error type is: $type" }
                // 缩略图类型错误
                OpenAbilityTrackHelper.trackOpenAbilityCall(
                    OpenAbilityTrackHelper.buildCommonParamMap(
                        featureName = OpenAbilityTrackConstant.OpenFeatureName.GET_THUMBNAIL,
                        interfaceName = TAG_GET_THUMBNAIL,
                        callPackage = SearchTrackHelper.providerCallPackage,
                        callResult = OpenAbilityTrackConstant.Value.CallResult.FAILED,
                        callFailReason = OpenAbilityTrackConstant.Value.GetThumbNailFailReason.TYPE_ERROR,
                        callCostTime = GLog.getTime(startTime).toString()
                    )
                )
                resultThumbnail.resultCode = ERROR_CODE_ARG
                return resultThumbnail
            }
            GLog.d(TAG, LogFlag.DL) { "executeThumbnailRequest thumbnailType:$thumbnailType" }
            //由于缩图的path属性是用ITEM_PATH和相册数据库中的_id拼接的而不是用media_id,因此要查询出_id
            val path = LocalMediaDataHelper.getPathByFilePath(filePath) ?: let {
                GLog.d(TAG, LogFlag.DL) { "executeThumbnailRequest, id is: $id mediaType is: $mediaType" }
                // 媒体类型错误
                OpenAbilityTrackHelper.trackOpenAbilityCall(
                    OpenAbilityTrackHelper.buildCommonParamMap(
                        featureName = OpenAbilityTrackConstant.OpenFeatureName.GET_THUMBNAIL,
                        interfaceName = TAG_GET_THUMBNAIL,
                        callPackage = SearchTrackHelper.providerCallPackage,
                        callResult = OpenAbilityTrackConstant.Value.CallResult.FAILED,
                        callFailReason = OpenAbilityTrackConstant.Value.GetThumbNailFailReason.MEDIA_TYPE_ERROR,
                        callCostTime = GLog.getTime(startTime).toString()
                    )
                )
                resultThumbnail.resultCode = ERROR_CODE_ARG
                return resultThumbnail
            }
            val newFileTime = System.currentTimeMillis()
            path.cacheKey = filePath + File(filePath).length()
            GLog.d(TAG, LogFlag.DL) { "executeThumbnailRequest path:$path, cost time:${GLog.getTime(newFileTime)}" }
            val isScreenNail = ThumbnailSizeUtils.isFullThumbnailKey(thumbnailType)
            val localMediaCacheKey = CacheKeyFactory.createLocalMediaCacheKey(path, modify)
            val cacheOptions = CacheOptions(thumbnailType)
            val found = if (isScreenNail) {
                cachingAbility.screenNailCache?.getImageData(localMediaCacheKey, cacheOptions, buffer) ?: false
            } else {
                cachingAbility.thumbnailCache?.getImageData(localMediaCacheKey, cacheOptions, buffer) ?: false
            }
            GLog.d(TAG, LogFlag.DL) { "executeThumbnailRequest thumbnail cache is $found" }
            if (found) {
                val bitmap = decodeBitmapByCache(option, buffer, thumbnailType)
                GLog.d(TAG, LogFlag.DL) { "executeThumbnailRequest get thumbnail, cost time:${GLog.getTime(startTime)}" }
                bitmap ?: let {
                    resultThumbnail.resultCode = ERROR_CODE_OTHER
                    return resultThumbnail
                }
                resultThumbnail.resultCode = SUCCESS_CODE
                resultThumbnail.bitmap = resizeBitmap(type, bitmap)
                return resultThumbnail
            }
            // 无缩图缓存
            OpenAbilityTrackHelper.trackOpenAbilityCall(
                OpenAbilityTrackHelper.buildCommonParamMap(
                    featureName = OpenAbilityTrackConstant.OpenFeatureName.GET_THUMBNAIL,
                    interfaceName = TAG_GET_THUMBNAIL,
                    callPackage = SearchTrackHelper.providerCallPackage,
                    callResult = OpenAbilityTrackConstant.Value.CallResult.FAILED,
                    callFailReason = OpenAbilityTrackConstant.Value.GetThumbNailFailReason.NO_CACHE,
                    callCostTime = GLog.getTime(startTime).toString()
                )
            )
            resultThumbnail.resultCode = ERROR_CODE_NO_CACHE
            GLog.d(TAG, LogFlag.DL) { "executeThumbnailRequest thumbnail cache is not find" }
        } catch (e: Exception) {
            GLog.e(TAG, LogFlag.DL) { "executeThumbnailRequest exception:$e" }
            // 无缩图缓存
            OpenAbilityTrackHelper.trackOpenAbilityCall(
                OpenAbilityTrackHelper.buildCommonParamMap(
                    featureName = OpenAbilityTrackConstant.OpenFeatureName.GET_THUMBNAIL,
                    interfaceName = TAG_GET_THUMBNAIL,
                    callPackage = SearchTrackHelper.providerCallPackage,
                    callResult = OpenAbilityTrackConstant.Value.CallResult.FAILED,
                    callFailReason = OpenAbilityTrackConstant.Value.GetThumbNailFailReason.OTHER_ERROR,
                    callCostTime = GLog.getTime(startTime).toString()
                )
            )
            resultThumbnail.resultCode = ERROR_CODE_OTHER
        } finally {
            BytesBufferPool.recycle(buffer)
            cachingAbility.close()
        }
        return resultThumbnail
    }

    private fun resizeBitmap(type: Int, bitmap: Bitmap?) = when (type) {
        TYPE_MICRO_THUMBNAIL_SCALE -> BitmapUtils.resizeAndCropCenter(bitmap, TYPE_MICRO_THUMBNAIL_SCALE_VIDEO_SIZE, true)
        else -> bitmap
    }

    private fun getPath(id: Int, mediaType: Int) = when (mediaType) {
        MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE -> LocalImage.ITEM_PATH.getChild(id)
        MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO -> LocalVideo.ITEM_PATH.getChild(id)
        else -> null
    }

    private fun getThumbnailType(type: Int) = when (type) {
        TYPE_MICRO_THUMBNAIL_SCALE -> ThumbnailSizeUtils.getFullThumbnailKey()
        TYPE_MICRO_THUMBNAIL -> microThumbnailType
        else -> null
    }

    private fun decodeBitmapByCache(options: BitmapFactory.Options?, buffer: BytesBuffer, type: Int): Bitmap? {
        return if (ThumbnailSizeUtils.isMicroThumbnailKey(type) || ThumbnailSizeUtils.isFullThumbnailKey(type)) {
            BitmapPools.decode(buffer, options)
        } else null
    }

    private fun transformOptionsParcelableToOptions(o: OptionsParcelable?): BitmapFactory.Options? {
        return BitmapFactory.Options().apply {
            o ?: return null
            inBitmap = o.inBitmap
            inMutable = o.inMutable
            inJustDecodeBounds = o.inJustDecodeBounds
            inSampleSize = o.inSampleSize
            inPreferredConfig = o.inPreferredConfig
            inPremultiplied = o.inPremultiplied
            inDensity = o.inDensity
            inTargetDensity = o.inTargetDensity
            inScreenDensity = o.inScreenDensity
            inScaled = o.inScaled
            outWidth = o.outWidth
            outHeight = o.outHeight
            outMimeType = o.outMimeType
            outConfig = o.outConfig
            inTempStorage = o.inTempStorage
        }
    }
}