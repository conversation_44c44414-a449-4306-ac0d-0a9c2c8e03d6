/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PushConstants.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/5/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/5/19        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.push

import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties

object PushConstants {
    private const val TEST_APP_KEY = "e7da81322178423aa250a858f6d75104"
    private const val TEST_APP_SECRET = "04be36d43c244d4d8e1845238afb2c5c"
    private const val RELEASE_APP_KEY = "4f3cf53492e7439d94b2cb50e2b77e4e"
    private const val RELEASE_APP_SECRET = "b55679b1401847af8370678be814bfbb"
    private val TEST_ENABLE = GallerySystemProperties
        .getBoolean("debug.gallery.push.test", false)
    val APP_KEY = if (TEST_ENABLE) TEST_APP_KEY else RELEASE_APP_KEY
    val APP_SECRET = if (TEST_ENABLE) TEST_APP_SECRET else RELEASE_APP_SECRET
}