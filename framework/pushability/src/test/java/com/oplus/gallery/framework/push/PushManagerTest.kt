/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PushManagerTest.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/12/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/12/15        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.push

import android.content.Context
import com.oplus.gallery.business_lib.api.IPushDM
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.Before
import org.junit.Test

class PushManagerTest {
    private val testProcessor = spyk(object : IPushDM.IPushDataMessageProcessor {
        override fun process(context: Context, pushMessage: IPushDM.PushMessage): Boolean {
            if (pushMessage.content == TEST) {
                return true
            }
            return false
        }
    })

    @Before
    fun setUp() {
        PushManager.addProcessor(testProcessor)
    }

    @Test
    fun should_process_message_by_test_when_processMessage_with_test() {
        PushManager.processMessage(mockk(), mockk(), IPushDM.PushMessage("", TEST))
        verify(exactly = 1) { testProcessor.process(any(), any()) }
    }

    companion object {
        private const val TEST = "test"
    }
}