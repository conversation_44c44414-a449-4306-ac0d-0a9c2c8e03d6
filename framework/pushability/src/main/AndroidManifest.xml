<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!--推送权限-->
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE"/>

    <application
        tools:replace="android:allowBackup"
        android:allowBackup="false">
        <service
            android:name="com.oplus.gallery.framework.push.GalleryDataMessageCallbackService"
            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE"
            android:exported="true">
            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE"/>
                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE"/>
            </intent-filter>
        </service>
        <receiver
            android:name="com.oplus.gallery.framework.push.PushNotificationDeleteReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.oplus.gallery.action.PUSH_NOTIFICATION_DELETE"/>
            </intent-filter>
        </receiver>
    </application>
</manifest>