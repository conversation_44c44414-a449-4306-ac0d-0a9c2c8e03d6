/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - GalleryDataMessageCallbackService.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/07/14
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/07/14        1.0           Add
 **************************************************************************************************/

package com.oplus.gallery.framework.push

import android.content.Context
import com.heytap.msp.push.mode.DataMessage
import com.heytap.msp.push.service.DataMessageCallbackService
import com.oplus.gallery.business_lib.api.IPushDM
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.foundation.util.debug.GLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class GalleryDataMessageCallbackService : DataMessageCallbackService() {
    companion object {
        private const val TAG = "GalleryPushService"
        private const val DELAY_MILLIS = 1000L
    }

    override fun processMessage(context: Context?, dataMessage: DataMessage?) {
        super.processMessage(context, dataMessage)
        AppScope.launch(Dispatchers.IO) {
            /*
            延迟一秒处理push消息，否则在相册进程未拉起的情况下触发 push, 消息不会被处理
            因为 PushManager 的 processors 的填充是其他线程处理的,存在时序问题
             */
            delay(DELAY_MILLIS)
            dataMessage?.let {
                GLog.d(TAG, "processMessage: ${it.globalId}, delay time $DELAY_MILLIS ms")
                try {
                    val pushMessage = IPushDM.PushMessage(it.globalId, it.content)
                    PushManager.processMessage(applicationContext, it, pushMessage)
                } catch (e: Exception) {
                    GLog.e(TAG, "processMessage: err=$e")
                }
            }
        }
    }
}