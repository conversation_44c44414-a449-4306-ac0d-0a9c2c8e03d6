/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PushDM.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/10/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/10/25        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.push

import android.content.Context
import com.oplus.gallery.business_lib.api.IPushDM
import com.oplus.gallery.router_lib.annotations.Component

@Component(interfaceName = "com.oplus.gallery.business_lib.api.IPushDM")
class PushDM : IPushDM {

    override fun init(context: Context) {
        PushManager.init(context)
    }

    override fun getRegisterId(): String? = PushManager.getRegisterId()

    override fun addPushMessageProcessor(processor: IPushDM.IPushDataMessageProcessor) {
        PushManager.addProcessor(processor)
    }

    override fun trackEvent(context: Context, event: IPushDM.PushEvent, messageId: String?) {
        PushManager.trackEvent(context, event, messageId)
    }
}