/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PushNotificationDeleteReceiver.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/8/3
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/8/3        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.push

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.oplus.gallery.business_lib.api.ACTION_PUSH_NOTIFICATION_DELETE
import com.oplus.gallery.business_lib.api.IPushDM
import com.oplus.gallery.business_lib.api.MESSAGE_ID
import com.oplus.gallery.foundation.util.systemcore.IntentUtils

class PushNotificationDeleteReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        context ?: return
        intent ?: return
        /**
         * 增加广播接收器action校验，确保代码严谨性
         */
        if (ACTION_PUSH_NOTIFICATION_DELETE == intent.action) {
            val id = IntentUtils.getStringExtra(intent, MESSAGE_ID)
            PushManager.trackEvent(context, IPushDM.PushEvent.PUSH_DELETE, id)
            PushManager.removeMessage(id)
        }
    }
}