/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PushManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/3/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/3/7        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.push

import android.content.Context
import android.os.ConditionVariable
import androidx.annotation.WorkerThread
import com.heytap.msp.push.HeytapPushManager
import com.heytap.msp.push.callback.ICallBackResultService
import com.heytap.msp.push.constant.EventConstant
import com.heytap.msp.push.mode.DataMessage
import com.oplus.gallery.business_lib.api.IPushDM
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

internal object PushManager {
    private const val TAG = "PushManager"
    private const val RESULT_CODE_SUCCESS = 0
    private const val REGISTER_TIMEOUT = TimeUtils.TIME_10_SEC_IN_MS.toLong()

    private val processors = mutableSetOf<IPushDM.IPushDataMessageProcessor>()
    private val messageMap = mutableMapOf<String, DataMessage>()
    private val eventMap = mapOf(
        IPushDM.PushEvent.PUSH_SHOW to EventConstant.EventId.EVENT_ID_PUSH_SHOW,
        IPushDM.PushEvent.PUSH_CLICK to EventConstant.EventId.EVENT_ID_PUSH_CLICK,
        IPushDM.PushEvent.PUSH_DELETE to EventConstant.EventId.EVENT_ID_PUSH_DELETE,
    )

    private var isInitiated = false

    fun init(context: Context) {
        if (isInitiated) return

        if (!com.oplus.gallery.basebiz.permission.NetworkPermissionManager.isUseOpenNetwork) {
            GLog.d(TAG, "init: network not allowed, skip")
            return
        }

        GTrace.trace("initPushManager") {
            HeytapPushManager.init(context, false)
        }

        isInitiated = true
    }

    private fun register(context: Context): String? {
        init(context)

        var result: String? = null
        val appKey = PushConstants.APP_KEY
        val appSecret = PushConstants.APP_SECRET
        val conditionVariable = ConditionVariable()
        HeytapPushManager.register(context, appKey, appSecret, object : ICallBackResultService {
            override fun onRegister(responseCode: Int, registerId: String?) {
                GLog.d(TAG, "onRegister: $responseCode $registerId")
                if ((responseCode == RESULT_CODE_SUCCESS) && !registerId.isNullOrEmpty()) {
                    ComponentPrefUtils.setStringPref(context, ComponentPrefUtils.REGISTER_ID, registerId)
                    result = registerId
                    conditionVariable.open()
                }
            }

            override fun onUnRegister(p0: Int) {
                // do nothing
            }

            override fun onSetPushTime(p0: Int, p1: String?) {
                // do nothing
            }

            override fun onGetPushStatus(p0: Int, p1: Int) {
                // do nothing
            }

            override fun onGetNotificationStatus(p0: Int, p1: Int) {
                // do nothing
            }

            override fun onError(p0: Int, p1: String?) {
                // do nothing
            }
        })
        conditionVariable.block(REGISTER_TIMEOUT)
        return result
    }

    @WorkerThread
    @Synchronized
    fun getRegisterId(): String? {
        return ComponentPrefUtils.getStringPref(
            ContextGetter.context,
            ComponentPrefUtils.REGISTER_ID,
            null
        ) ?: register(ContextGetter.context)
    }

    fun addProcessor(processor: IPushDM.IPushDataMessageProcessor) {
        processors.add(processor)
    }

    fun processMessage(context: Context, dataMessage: DataMessage, pushMessage: IPushDM.PushMessage) {
        GLog.d(TAG, "processMessage: dataMessage = $dataMessage")
        messageMap[pushMessage.id] = dataMessage
        processors.find {
            it.process(context, pushMessage)
        }?.run {
            GLog.d(TAG, "processMessage: processed by: $this")
        } ?: run {
            GLog.w(TAG, "processMessage: no processor match")
        }
    }

    fun removeMessage(messageId: String) {
        messageMap.remove(messageId)
    }

    fun trackEvent(context: Context, event: IPushDM.PushEvent, messageId: String?) {
        val eventId = eventMap[event] ?: return
        messageMap[messageId]?.apply {
            HeytapPushManager.statisticEvent(context, eventId, this)
        }
    }
}