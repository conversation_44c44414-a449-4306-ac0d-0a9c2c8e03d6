/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LensChannelImplTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>                   <date>       <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     <EMAIL>     2022/06/29     1.0              create
 ******************************************************************************/
package com.oplus.gallery.framework.abilities.sharingability.channel

import android.app.Activity
import android.net.Uri
import androidx.lifecycle.Lifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class LensChannelImplTest {
    private lateinit var lensChannelImpl: LensChannelImpl

    @MockK
    private lateinit var lifecycle: Lifecycle

    @MockK
    private lateinit var lensWrapper: LensWrapper

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        lensChannelImpl = spyk(LensChannelImpl(mockk())) {
            every { createLensWrapper(any()) } returns lensWrapper
        }
        val activity: Activity = mockk<Activity>()
        val uri: Uri = mockk<Uri>()
        every { lensWrapper.startLens(activity) } returns true
        every { lifecycle.removeObserver(any()) } just runs
        every { lifecycle.addObserver(any()) } just runs
    }
    @Ignore
    @Test
    fun `should addObserver when registerDataAdapter`() {
        // when
        lensChannelImpl.registerDataAdapter(lifecycle, mockk())

        // then
        verify {
            lifecycle.addObserver(any())
        }
    }
    @Ignore
    @Test
    fun `should removeObserver when registerDataAdapter repetition`() {
        //given
        lensChannelImpl.registerDataAdapter(lifecycle, mockk())

        // when
        lensChannelImpl.registerDataAdapter(lifecycle, mockk())

        // then
        verify {
            lifecycle.removeObserver(any())
        }
    }
    @Ignore
    @Test
    fun `should removeObserver when unregisterDataAdapter`() {
        //given
        lensChannelImpl.registerDataAdapter(lifecycle, mockk())

        // when
        lensChannelImpl.unregisterDataAdapter(lifecycle, mockk())

        // then
        verify {
            lifecycle.removeObserver(any())
        }
    }

    @Test
    fun `should isEnabled is false when have no register`() {
        //given
        val expected = false

        // when
        val actual = lensChannelImpl.isEnabled(lifecycle)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should isEnabled is false when register but lensWrapper is not enabled`() {
        //given
        val expected = false
        lensChannelImpl.registerDataAdapter(lifecycle, mockk())
        every { lensWrapper.isEnabled } returns false

        // when
        val actual = lensChannelImpl.isEnabled(lifecycle)

        // then
        Assert.assertEquals(expected, actual)
    }

    @Test
    fun `should isEnabled is true when register and lensWrapper is enabled`() {
        //given
        val expected = true
        lensChannelImpl.registerDataAdapter(lifecycle, mockk())
        every { lensWrapper.isEnabled } returns true

        // when
        val actual = lensChannelImpl.isEnabled(lifecycle)

        // then
        Assert.assertEquals(expected, actual)
    }
}