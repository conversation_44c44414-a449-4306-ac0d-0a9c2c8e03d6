/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - NfcChannelImplTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>                   <date>       <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     <EMAIL>     2022/06/29     1.0              create
 ******************************************************************************/
package com.oplus.gallery.framework.abilities.sharingability.channel

import android.app.Activity
import com.oplus.gallery.framework.abilities.sharing.channel.NfcDataAdapter
import com.oplus.gallery.foundation.util.debug.GLog
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class NfcChannelImplTest {

    private lateinit var nfcChannelImpl: NfcChannelImpl

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        nfcChannelImpl = spyk() {
            every { ensureNfcAdapter(any()) } just runs
        }
    }

    @Test
    fun `should have true size when registerDataAdapter`() {
        // when
        nfcChannelImpl.registerDataAdapter(mockk(), mockk())

        // then
        Assert.assertEquals(nfcChannelImpl.dataAdapters.size, 1)
    }

    @Test
    fun `should close when unregisterDataAdapter and size is 0`() {
        // when
        val key = mockk<Activity>()
        val dataAdapter = mockk<NfcDataAdapter>()
        nfcChannelImpl.registerDataAdapter(key, dataAdapter)
        nfcChannelImpl.unregisterDataAdapter(key, dataAdapter)

        // then
        Assert.assertEquals(nfcChannelImpl.dataAdapters.size, 0)
        verify {
            nfcChannelImpl.close()
        }
    }
}