/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - NFCChannelImpl.kt
 * Description: NFC使用的通道
 * Version: 1.0
 * Date: 2022/05/10
 * Author: lichengli@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * lichengli@Apps.Gallery3D      2020/05/10       1.0           create file
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.sharingability.channel

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.nfc.NfcAdapter
import android.nfc.NfcEvent
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.sharing.channel.INfcChannel
import com.oplus.gallery.framework.abilities.sharing.channel.NfcDataAdapter

/**
 * NFC使用的通道。
 * - 内部持有一个NfcAdapter用于执行NFC分享操作。
 * - 持有一个NfcDataAdapter的集合，当该集合为空时会释放NfcAdapter，
 */
class NfcChannelImpl : INfcChannel, AutoCloseable {

    /**
     * 本地 NFC 适配器
     */
    private var nfcAdapter: NfcAdapter? = null

    /**
     * u之后删除了setBeamPushUrisCallback方法，此时返回null
     * u之前预期返回非null
     */
    private val nfcSetBeamUrisCallbackMethod = kotlin.runCatching {
        NfcAdapter::class.java.getMethod(FUN_SET_BEAM_PUSH_URIS_CALLBACK, Array<Uri>::class.java, Activity::class.java)
    }.getOrNull()

    /**
     * 已经注册的[NfcDataAdapter]。
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal val dataAdapters = mutableSetOf<NfcDataAdapter>()

    @Suppress("DEPRECATION")
    @Synchronized
    override fun registerDataAdapter(
        key: Activity,
        dataAdapter: NfcDataAdapter
    ) {
        ensureNfcAdapter(key)

        GLog.d(TAG, "[registerDataAdapter], keyActivity : ${key.javaClass.canonicalName}")

        val createBeamUrisCallBack = { _: NfcEvent ->
            dataAdapter.onGetShareUris().toTypedArray()
        }
        nfcAdapter?.let {
            runCatching {
                nfcSetBeamUrisCallbackMethod?.invoke(it, createBeamUrisCallBack, key)
            }.onFailure {
                GLog.e(TAG, "registerDataAdapter: ", it)
            }
        }
        dataAdapters.add(dataAdapter)
    }

    @Suppress("DEPRECATION")
    @Synchronized
    override fun unregisterDataAdapter(key: Activity, dataAdapter: NfcDataAdapter) {
        GLog.d(TAG, "[unregisterDataAdapter], keyActivity : ${key.javaClass.canonicalName}")

        runCatching {
            // 在activity destory后，调用此方法会抛出异常
            nfcAdapter?.let {
                runCatching {
                    nfcSetBeamUrisCallbackMethod?.invoke(it, null, key)
                }.onFailure {
                    GLog.e(TAG, "unregisterDataAdapter: ", it)
                }
            }
        }.onFailure {
            GLog.e(TAG, "unregisterDataAdapter: ", it)
        }
        dataAdapters.remove(dataAdapter)

        // 尝试close
        close()
    }

    /**
     * 确认[nfcAdapter]不为空, 若为空则尝试获取。
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun ensureNfcAdapter(context: Context) {
        nfcAdapter ?: NfcAdapter.getDefaultAdapter(context).also {
            GLog.d(TAG, "[ensureNfcAdapter] new NfcAdapter, $it")
            nfcAdapter = it
        }
    }

    /**
     * 若[dataAdapters]未空，释放[nfcAdapter]
     */
    @Synchronized
    override fun close() {
        if (dataAdapters.isEmpty()) {
            nfcAdapter = null
            GLog.d(TAG, "[close] close NfcChannel success")
        }
    }

    companion object {
        private const val TAG = "NfcChannelImpl"
        private const val FUN_SET_BEAM_PUSH_URIS_CALLBACK = "setBeamPushUrisCallback"
    }
}