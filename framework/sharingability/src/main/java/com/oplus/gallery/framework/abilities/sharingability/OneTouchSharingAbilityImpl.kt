/**************************************************************************************************
 * Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - OneTouchSharingAbilityImpl.kt
 * Description: 一碰分享能力接口实现
 * Version: 1.0
 * Date: 2024/2/22
 * Author: HuiTang@Apps.Gallery3D
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * HuiTang@Apps.Gallery3D      2024/2/22      1.0           create file
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.sharingability

import android.content.Context
import android.os.Bundle
import com.heytap.accessory.nfc.sdk.INfcEventCallback
import com.heytap.accessory.nfc.sdk.NfcManager
import com.heytap.accessory.nfc.sdk.NfcScenes
import com.heytap.accessory.nfc.sdk.NfcShareEvent
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.sharing.IOneTouchSharingAbility
import com.oplus.gallery.framework.abilities.sharing.nfc.Constants.ContractEventType
import com.oplus.gallery.framework.abilities.sharing.nfc.Constants.EventRoleType
import com.oplus.gallery.framework.abilities.sharing.nfc.Constants.NfcScene
import com.oplus.gallery.framework.abilities.sharing.nfc.Constants.OafNfcKey
import com.oplus.gallery.framework.abilities.sharing.nfc.Constants.ShareType
import com.oplus.gallery.framework.abilities.sharing.nfc.IEventCallback

/**
 * 实现一碰传的能力接口，初始化、注册等，即调用 OAF NFC sdk 中的NFCManager的相关方法实现功能
 */
class OneTouchSharingAbilityImpl(private val context: Context) : AbsAppAbility(), IOneTouchSharingAbility {
    override val domainInstance: AutoCloseable = this
    private val nfcManager: NfcManager = NfcManager.create(context)
    private val callbackList: MutableList<IEventCallback?> = mutableListOf()

    /**
     * 当前处于顶部页面的业务逻辑的 callback ，用于处理回调时发送数据、错误处理等。
     */
    private var currentCallback: IEventCallback? = null

    /**
     * 接口回调，详见文档：https://odocs.myoas.com/docs/KlkKVe5dDvSQxVqd
     */
    private val nfcEventCallback = object : INfcEventCallback {
        override fun onError(errorCode: Int) {
            GLog.d(TAG, "[EventCallback] onError errorCode = $errorCode, currentCallback = $currentCallback")
            currentCallback?.onError(errorCode)
        }

        override fun onRegistered(result: Boolean) {
            GLog.d(TAG, "[EventCallback] onRegistered result = $result, currentCallback = $currentCallback")
            currentCallback?.onRegistered(result)
        }

        @Deprecated(message = "Use onTransmitReady(bundle: Bundle) instead")
        override fun onTransmitReady() {
            /** 已废弃，新sdk不要在此处理业务 , SDK中未删除，保留空实现 */
        }

        override fun onNfcContractEvent(bundle: Bundle): Boolean {
            runCatching {
                val nfcContractEventType = bundle.getInt(OafNfcKey.KEY_NFC_CONTRACT_EVENT)
                val nfcRoleType = bundle.getInt(OafNfcKey.KEY_NFC_EVENT_ROLE)
                GLog.d(
                    TAG, "[EventCallback] onNfcContractEvent [eventType = $nfcContractEventType , roleType = $nfcRoleType]"
                )
                if (nfcContractEventType != ContractEventType.CONTRACT_START.type) {
                    GLog.w(TAG, "[EventCallback] onNfcContractEvent : Unknown NFC contract event type!")
                    return false
                }

                /** 目前相册仅在作为发送端时需要处理该事件 */
                if (nfcRoleType == EventRoleType.SENDER.type) {
                    GLog.d(TAG, "[EventCallback] onTransmitPreparing currentCallback = $currentCallback")
                    return currentCallback?.onTransmitPreparing() ?: false
                }
            }.onFailure {
                GLog.e(TAG, "[EventCallback] handle onNfcContractEvent exception!")
                return false
            }

            /**
             * 对于相册业务来说，仅需要在作为发送端时处理业务，并根据执行情况返回true/false，
             * 因为此接口并非专为相册服务,其他无异常或相册不处理的情况，按照SDK要求返回默认值true，以免干扰互联其他业务!*/
            return true
        }

        override fun onTransmitReady(bundle: Bundle) {
            GLog.d(TAG, "[EventCallback] onTransmitReady bundle = $bundle, currentCallback = $currentCallback")
            currentCallback?.onTransmitReady(bundle)
        }
    }

    override fun registerShareEvent(callback: IEventCallback?) {
        callback?.let {
            GLog.d(TAG, "[registerShareEvent] >>> shareType = ${it.shareType} , count = ${it.count} , scene = ${it.scene}, callback = $it")
            when (it.shareType) {
                ShareType.SHARE_TYPE_FILE, ShareType.SHARE_TYPE_IMAGE -> {
                    if (callbackList.contains(it).not()) {
                        GLog.d(TAG, "[registerShareEvent] Callback not in callback list, register NFC event ")
                        callbackList.add(it)
                        registerEvent(it, currentCallback)
                        currentCallback = it
                    } else if (currentCallback == it) {
                        // 只要 callback 是属于顶部的页面，就可以重复进行注册 NFC 事件，这不会影响正常的业务逻辑。
                        GLog.d(TAG, "[registerShareEvent] Same as current callback, register NFC event again")
                        registerEvent(it)
                    } else {
                        GLog.d(TAG, "[registerShareEvent] Skip register NFC event")
                    }
                }

                else -> GLog.d(TAG, "[registerShareEvent] Invalid share type!")
            }
            GLog.d(TAG, "[registerShareEvent] <<< callback list size = ${callbackList.size}")
        }
    }

    override fun unRegisterShareEvent(callback: IEventCallback?) {
        GLog.d(TAG, "[unRegisterShareEvent] >>> callback = $callback")
        if (callbackList.contains(callback)) {
            callbackList.remove(callback)
            GLog.d(TAG, "[unRegisterShareEvent] remove callback = $callback")
            callbackList.lastOrNull()?.let {
                GLog.d(TAG, "[unRegisterShareEvent] get last callback to register NFC event")
                currentCallback = it
                registerEvent(it, callback)
            }
        }
        if (callbackList.isEmpty()) {
            currentCallback = null
            unRegisterEvent()
        }
        GLog.d(TAG, "[unRegisterShareEvent] <<< callback list size= ${callbackList.size}")
    }

    override fun unRegisterEvent() {
        GLog.d(TAG, "[unRegisterEvent] Unregister NFC event")
        nfcManager.unRegisterEvent()
    }

    override fun unRegisterAllShareEvent() {
        GLog.d(TAG, "[unRegisterAllShareEvent] Clear callbacks && Unregister NFC event")
        callbackList.clear()
        currentCallback = null
        unRegisterEvent()
    }

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        GLog.d(TAG, "[onLoad]")
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        GLog.d(TAG, "[onTrimMemory]")
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        GLog.d(TAG, "[onUnload]")
        if (callbackList.isEmpty()) {
            currentCallback = null
            unRegisterEvent()
        }
    }

    override fun close() {
        GLog.d(TAG, "[close]")
        if (callbackList.isEmpty()) {
            currentCallback = null
            unRegisterEvent()
        }
    }

    /**
     * 注册 NFC 事件
     */
    private fun registerEvent(callback: IEventCallback?) {
        callback?.run {
            GLog.d(TAG, "[registerEvent] >>> [NFC] shareType = $shareType , count = $count , scene = $scene")
            nfcManager.registerEvent(NfcShareEvent(shareType, count), getNfcTrackScene(scene), nfcEventCallback)
        }
    }

    /**
     * 当新 callback 的类型/数量/埋点场景与旧 callback 的不一致时，需要重新注册 NFC 事件。否则不需要注册。
     */
    private fun registerEvent(newCallback: IEventCallback?, oldCallback: IEventCallback?) {
        newCallback?.run {
            if ((shareType != oldCallback?.shareType) || (count != oldCallback.count) || (scene != oldCallback.scene)) {
                GLog.d(TAG, "[registerEvent] Callback(Data) changed , register NFC event")
                registerEvent(this)
            } else {
                GLog.d(TAG, "[registerEvent] Callback(Data) not changed, skip register NFC event")
            }
        }
    }

    /**
     * 获取NFC埋点场景
     */
    private fun getNfcTrackScene(scene: Int): NfcScenes {
        return if (scene == NfcScene.SCENE_SHARE) NfcScenes.GALLERY_SHARE else NfcScenes.GALLERY_SELECT
    }

    companion object {
        private const val TAG = "OneTouchSharingAbility"
    }
}