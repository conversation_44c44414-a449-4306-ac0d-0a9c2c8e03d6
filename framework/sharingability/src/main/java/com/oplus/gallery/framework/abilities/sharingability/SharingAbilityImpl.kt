/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SharingAbilityImpl.kt
 * Description: 分享能力的实现
 * Version: 1.0
 * Date: 2022/05/10
 * Author: lichengli@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * lichengli@Apps.Gallery3D      2020/05/10       1.0           create file
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.sharingability

import android.content.Context
import com.oplus.appability.AbsAppAbility
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.sharing.ISharingAbility
import com.oplus.gallery.framework.abilities.sharing.channel.ILensChannel
import com.oplus.gallery.framework.abilities.sharing.channel.INfcChannel
import com.oplus.gallery.framework.abilities.sharing.channel.IShareChannel
import com.oplus.gallery.framework.abilities.sharingability.channel.LensChannelImpl
import com.oplus.gallery.framework.abilities.sharingability.channel.NfcChannelImpl
import com.oplus.gallery.framework.abilities.sharingability.channel.ShareChannelImpl

/**
 * 分享能力的实现。
 *
 * 内部包含以下三个分享通道：
 * - NfcChannel
 * - LensChannel
 * - ShareChannel
 *
 * 内部通道是一层封装，一直被能力所持有，其生命周期体现在其内部持有的资源的获取与释放上。
 * - 通道首次被调用时，会初始化一个通道实例，但不会立即初始化其中的资源。
 * - 在通道被使用时才会初始化资源，当调用[close]时，若通道未被使用，会释放内部持有的资源。
 */
class SharingAbilityImpl(private val context: Context) : AbsAppAbility(), ISharingAbility {

    override val domainInstance: AutoCloseable = this

    override val nfcChannel: INfcChannel? by lazy {
        /*
         * Marked by: 2022/5/12 lichengli 后续配置NFCFeature，在Feature不可用时，返回null
         * 注意：u版本之后，不再支持nfc分享uri
         */
        if (ApiLevelUtil.isAtLeastAndroidU()) null else NfcChannelImpl()
    }

    override val lensChannel: ILensChannel? by lazy { LensChannelImpl(context) }

    override val shareChannel: IShareChannel? by lazy { ShareChannelImpl() }


    override fun close() {
        (nfcChannel as? NfcChannelImpl)?.close()
        (lensChannel as? LensChannelImpl)?.close()
        (shareChannel as? ShareChannelImpl)?.close()
    }
}