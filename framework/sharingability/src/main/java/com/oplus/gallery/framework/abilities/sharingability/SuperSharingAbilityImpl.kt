/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: SuperSharingAbilityImpl
 ** Description:
 ** Version: 1.0
 ** Date : 2025/5/27
 ** Author: XinHuan.Wu
 ** ---------------------Revision History: ---------------------
 **  <author>      <date>      <version>    <desc>
 **  XinHuan.Wu    2025/5/27     1.0          create
 ****************************************************************/
package com.oplus.gallery.framework.abilities.sharingability

import android.content.Context
import android.content.Intent
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.sharing.ISuperSharingAbility
import com.oplus.gallery.framework.abilities.sharing.nfc.Constants.ShareType
import com.oplus.gallery.framework.abilities.sharing.nfc.IEventCallback
import com.oplus.interconnect.share.sdk.Share
import com.oplus.interconnect.share.sdk.ShareAgent
import com.oplus.interconnect.share.sdk.ShareEventCallback
import com.oplus.interconnect.share.sdk.data.RejectReason
import com.oplus.interconnect.share.sdk.data.ResultCode
import com.oplus.interconnect.share.sdk.data.ShareEvent
import com.oplus.interconnect.share.sdk.data.SharedData
import com.oplus.interconnect.share.sdk.data.TaskResult
import java.util.concurrent.CompletableFuture
import java.util.concurrent.atomic.AtomicReference

/**
 * 实现OS16.0超级一碰分享接口
 */
class SuperSharingAbilityImpl(private val context: Context) : AbsAppAbility(), ISuperSharingAbility {
    override val domainInstance: AutoCloseable = this
    private val shareManager = Share.create(context)
    private var foundAgent: ShareAgent? = null
    private val registeringFuture = AtomicReference<CompletableFuture<TaskResult>>(null)
    private val registerLock = Any()

    /**
     * 当前处于顶部页面的业务逻辑的 callback ，用于处理回调时发送数据、错误处理等。
     */
    private var currentCallback: IEventCallback? = null
    private val callbackList: MutableList<IEventCallback?> = mutableListOf()

    /**
     * 超级碰一碰sdk回调，一碰发现设备后开始分享
     */
    private val shareCallback: ShareEventCallback = object : ShareEventCallback {
        override fun onAgentFound(agent: ShareAgent) {
            GLog.d(TAG, LogFlag.DL, "[onAgentFound] agent: $agent")
            foundAgent = agent
            if (currentCallback?.onTransmitPreparing() == true) {
                currentCallback?.onAgentFound(agent)
            } else {
                agent.rejectShare(RejectReason.CONDITION_LIMIT)
                GLog.d(TAG, LogFlag.DL, "[onAgentFound] Transmit prepare fail")
            }
        }
    }

    override fun on(): CompletableFuture<TaskResult> {
        synchronized(registerLock) {
            val currentFuture = registeringFuture.get()
            if ((currentFuture != null) && (!currentFuture.isDone)) {
                GLog.d(TAG, LogFlag.DL, "[on] registration is already in progress.")
                return currentFuture
            }

            val newFuture = shareManager.on(ShareEvent.TAP_SHARE, shareCallback).thenApplyAsync {
                GLog.d(TAG, LogFlag.DL, "[on] register share on result: $it")
                registeringFuture.set(null)
                it
            }.exceptionally {
                GLog.e(TAG, LogFlag.DL, "[on] register share error=${it.message}", it)
                registeringFuture.set(null)
                TaskResult(ResultCode.INTERNAL_ERROR.value)
            }

            registeringFuture.set(newFuture)
            return newFuture
        }
    }

    override fun off(): CompletableFuture<TaskResult> {
        return shareManager.off(ShareEvent.TAP_SHARE).whenComplete { result, throwable ->
            if (throwable == null) {
                GLog.d(TAG, LogFlag.DL, "[off] unregister share off: $result")
            } else {
                GLog.w(TAG, LogFlag.DL, "[off] unregister share error: $throwable")
            }
        }
    }

    override fun registerShareEvent(callback: IEventCallback?) {
        callback?.let {
            GLog.d(TAG, LogFlag.DL, "[registerShareEvent] shareType = ${it.shareType} , count = ${it.count} ," +
                        "scene = ${it.scene}, callback = $it"
            )

            when (it.shareType) {
                ShareType.SHARE_TYPE_FILE, ShareType.SHARE_TYPE_IMAGE -> {
                    on()
                    if (!callbackList.contains(it)) {
                        GLog.d(TAG, LogFlag.DL, "[registerShareEvent] Callback not in callback list, register NFC event")
                        callbackList.add(it)
                        currentCallback = it
                    } else {
                        GLog.d(TAG, LogFlag.DL, "[registerShareEvent] Skip register NFC event")
                    }
                }

                else -> GLog.w(TAG, LogFlag.DL, "[registerShareEvent] Invalid share type")
            }
            GLog.d(TAG, LogFlag.DL, "[registerShareEvent] callback list size = ${callbackList.size}")
        }
    }

    override fun unRegisterShareEvent(callback: IEventCallback?) {
        GLog.d(TAG, LogFlag.DL, "[unRegisterShareEvent] callback: $callback")
        if (callbackList.contains(callback)) {
            callbackList.remove(callback)
            GLog.d(TAG, LogFlag.DL, "[unRegisterShareEvent] remove callback: $callback")
            callbackList.lastOrNull()?.let {
                GLog.d(TAG, LogFlag.DL, "[unRegisterShareEvent] get last callback to register NFC event")
                currentCallback = it
            }
        }
        if (callbackList.isEmpty()) {
            currentCallback = null
            off()
        }
        GLog.d(TAG, LogFlag.DL, "[unRegisterShareEvent] callback list size= ${callbackList.size}")
    }

    override fun unRegisterAllShareEvent() {
        GLog.d(TAG, LogFlag.DL, "[unRegisterAllShareEvent] Clear callbacks && Unregister Share event")
        callbackList.clear()
        currentCallback = null
        off()
    }

    override fun getSharedData(intent: Intent): SharedData? {
        return shareManager.getSharedData(intent).also {
            GLog.d(TAG, LogFlag.DL, "[getSharedData] shareData: $it, shareRecord: ${it?.getRecords()}")
        }
    }

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        GLog.d(TAG, LogFlag.DL, "[onLoad]")
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        GLog.d(TAG, LogFlag.DL, "[onTrimMemory]")
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        GLog.d(TAG, LogFlag.DL, "[onUnload]")
        if (callbackList.isEmpty()) {
            currentCallback = null
            off()
        }
    }

    override fun close() {
        GLog.d(TAG, LogFlag.DL, "[close]${this.hashCode()}")
        off()
    }

    companion object {
        private const val TAG = "SuperSharingAbility"
    }
}