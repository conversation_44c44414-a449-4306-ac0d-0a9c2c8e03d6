/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ShareChannelImpl.kt
 * Description: 分享使用的通道
 * Version: 1.0
 * Date: 2022/05/10
 * Author: lichengli@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * lichengli@Apps.Gallery3D      2020/05/10       1.0           create file
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.sharingability.channel

import com.oplus.gallery.framework.abilities.sharing.channel.IShareChannel

/**
 * 分享使用的通道。
 */
class ShareChannelImpl : IShareChannel, AutoCloseable {

    override fun close() = Unit
}