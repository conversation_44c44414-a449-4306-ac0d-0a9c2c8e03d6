/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - LensChannelImpl.kt
 * Description: Google Lens 使用的通道
 * Version: 1.0
 * Date: 2022/05/10
 * Author: lichengli@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * lichengli@Apps.Gallery3D      2020/05/10       1.0           create file
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.sharingability.channel

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.Lifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

import com.oplus.gallery.framework.abilities.sharing.channel.ILensChannel
import com.oplus.gallery.framework.abilities.sharing.channel.ILensAdapter
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * Google Lens 使用的通道。
 */
class LensChannelImpl(private val context: Context) : ILensChannel, AutoCloseable {

    /**
     * 存放已被申请的Lens
     */
    private val lensMap = mutableMapOf<Lifecycle, LensWrapper?>()

    override fun isEnabled(invoker: Lifecycle): Boolean =
        synchronized(lensMap) {
            lensMap[invoker]?.isEnabled ?: false
        }

    override fun setEnableLens(invoker: Lifecycle, enable: Boolean) =
        synchronized(lensMap) { lensMap[invoker]?.isEnabled = enable }

    override fun startLens(activity: Activity, invoker: Lifecycle): Boolean =
        synchronized(lensMap) { lensMap[invoker]?.startLens(activity) ?: false }

    override fun registerDataAdapter(
        invoker: Lifecycle,
        adapter: ILensAdapter
    ) = synchronized(lensMap) {
        createLensWrapper(adapter).let { lensWrapper ->
            //注册
            lensMap[invoker] = lensWrapper
        }
    }

    override fun unregisterDataAdapter(
        invoker: Lifecycle,
        adapter: ILensAdapter
    ): Unit = synchronized(lensMap) {
        lensMap[invoker]?.let { lensWrapper ->
            //取消注册
            if (lensWrapper.isAuthorized) {
                //如果已经授权，则需要回收权限
                lensWrapper.activity?.revokeUriPermission(AppConstants.Package.GOOGLE_QUICK_SEARCH_BOX_PACKAGES,
                    adapter.onGetLensUri(), Intent.FLAG_GRANT_READ_URI_PERMISSION)
                lensWrapper.isAuthorized = false
                lensWrapper.activity = null
            }
            lensMap.remove(invoker)
        }
    }

    /**
     * 创建一个 [LensWrapper]
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun createLensWrapper(adapter: ILensAdapter) = LensWrapper(context, adapter)

    override fun close() = Unit
}

/**
 * Lens的包装类，内部对Lens的时机进行了进一步封装
 */
internal class LensWrapper(
    context: Context,
    private val adapter: ILensAdapter
) {
    //是否授权过
    var isAuthorized = false
    //当前activity
    var activity: Activity? = null
    init {
        checkLensAvailability()
    }

    /**
     * Lens是否可用
     */
    var isEnabled: Boolean
        get() = expectLensEnable && isLensApiEnabled
        set(value) {
            expectLensEnable = value
        }

    @Volatile
    private var expectLensEnable = false
        set(value) {
            val oldEnable = isEnabled
            field = value
            if (isEnabled != oldEnable) {
                adapter.onLensEnableStateChanged(isEnabled)
            }
        }

    /**
     * LensApi是否可用。
     *
     * LensApi可用，不代表Lens可用，外部可能手动禁用。
     */
    @Volatile
    private var isLensApiEnabled: Boolean = false
        set(value) {
            val oldEnable = isEnabled
            field = value
            if (isEnabled != oldEnable) {
                adapter.onLensEnableStateChanged(isEnabled)
            }
        }

    /**
     * 新的google api判定lens是否支持，是读取build属性值判定：
     * 1.如果支持，则build的"ro.com.google.lens.oem_image_package"属性值返回当前声明应用的包名
     * 2.如果不支持，则build属性值可能返回空字符串或者null
     * 3.谷歌官方文档说明：https://docs.partner.android.com/gms/building/apps/lens?authuser=1&hl=zh-cn#integration-steps
     */
    private fun checkLensAvailability(): Boolean {
        this.isLensApiEnabled = adapter.checkLensAvailable()
        return this.isLensApiEnabled
    }

    /**
     * 执行Lens操作。
     * 返回lens执行操作结果，只会在[isEnabled]为 true 时才执行，否则直接返回 false
     * for bugfix 7521772，废弃lensApi.launchLensActivityWithBitmap函数，改用startActivityForResult形式
     */
    fun startLens(activity: Activity): Boolean {
        if (isEnabled) {
            if (TextUtils.isEmpty(adapter.onGetLensUri()?.path)) {
                GLog.e(TAG, LogFlag.DL) { "startLens uri.path ie empty" }
                return false
            }
            var result = true
            runCatching {
                activity.grantUriPermission(AppConstants.Package.GOOGLE_QUICK_SEARCH_BOX_PACKAGES,
                    adapter.onGetLensUri(), Intent.FLAG_GRANT_READ_URI_PERMISSION)
                this.isAuthorized = true
                this.activity = activity
                val imageUri = Uri.parse(LENS_URI).buildUpon().appendQueryParameter(IMAGE_URI_KEY, adapter.onGetLensUri().toString()).build()
                val intent = Intent(Intent.ACTION_VIEW).setPackage(AppConstants.Package.GOOGLE_QUICK_SEARCH_BOX_PACKAGES)
                    .setData(imageUri).setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                activity.startActivityForResult(intent, REQUEST_CODE)
            }.onFailure {
                result = false
                GLog.e(TAG, LogFlag.DL) { "startLens fail it =$it" }
            }
            return result
        }
        return false
    }

    companion object {
        private const val TAG = "LensWrapper"
        private const val LENS_URI = "google://lens"
        private const val IMAGE_URI_KEY = "image_uri"
        private const val REQUEST_CODE = 20241220
    }
}