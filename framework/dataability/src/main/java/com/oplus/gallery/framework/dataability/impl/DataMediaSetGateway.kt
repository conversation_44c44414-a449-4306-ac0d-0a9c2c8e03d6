/*********************************************************************************
 ** Copyright (C), 2023-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DataMediaSetGateway.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/04/10
 ** Author      : <EMAIL>
 ** TAG         : DataMediaSetGateway
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>             2024/04/10  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.dataability.impl

import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.framework.abilities.data.DataKey
import com.oplus.gallery.framework.abilities.data.Detail
import com.oplus.gallery.framework.abilities.data.IOperator
import com.oplus.gallery.framework.abilities.data.LoadOptions
import com.oplus.gallery.framework.abilities.data.Snapshot
import com.oplus.gallery.framework.abilities.data.model.OnContentChangedListener
import com.oplus.gallery.framework.dataability.DataGateWay
import java.util.concurrent.atomic.AtomicBoolean

class DataMediaSetGateway<DATA>(
    override val id: DataKey,
    private val mediaSet: MediaSet,
    override val operator: IOperator? = null
) : DataGateWay<DATA> {
    override val isClosed: Boolean get() = _isClosed.get()
    private val _isClosed: AtomicBoolean = AtomicBoolean(false)

    override fun registerContentChangedListener(onContentChangedListener: OnContentChangedListener) = Unit

    override fun unregisterContentChangedListener(onContentChangedListener: OnContentChangedListener) = Unit

    override fun getDetail(): Detail {
        return Detail.INVALID_DETAIL
    }

    override fun findIndex(path: Path, referIndex: Int, options: LoadOptions): Int {
        return INVALID_INDEX
    }

    override fun getData(start: Int, count: Int, options: LoadOptions): Snapshot<DATA> {
        return Snapshot.INVALID_SNAPSHOT as Snapshot<DATA>
    }

    override fun getData(path: Path, count: Int, options: LoadOptions): Snapshot<DATA> {
        return Snapshot.INVALID_SNAPSHOT as Snapshot<DATA>
    }

    override fun close() = Unit

    private companion object {
        private const val INVALID_INDEX: Int = -1
    }
}