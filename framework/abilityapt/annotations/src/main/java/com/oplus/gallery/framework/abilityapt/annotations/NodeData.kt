/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - NodeData
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/5/6
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/6      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilityapt.annotations

/**
 * 节点数据类
 */
data class NodeData(val configType: Int, val storageConfig: IntArray, val dataType: Int) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NodeData

        if (configType != other.configType) return false
        if (!storageConfig.contentEquals(other.storageConfig)) return false
        if (dataType != other.dataType) return false

        return true
    }

    override fun hashCode(): Int {
        var result = configType
        result = 31 * result + storageConfig.contentHashCode()
        result = 31 * result + dataType
        return result
    }
}