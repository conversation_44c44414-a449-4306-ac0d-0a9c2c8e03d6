/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigNode
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/4/26
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/4/26      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilityapt.annotations

/**
 * 配置节点抽象。它表示的是配置树形结构中的一个节点。
 * 由于配置是按照功能树进行分类管理的，因此配置树类似一颗功能树的结构。
 *
 * 它的特点是：
 *
 * 1. 所有的父节点都是功能类别，有一级、二级、三级...
 * 2. 所有的叶子节点都是具体的一个个配置项
 */
class ConfigNode constructor(
    /**
     * 节点的ID值，唯一
     */
    var id: String,

    /**
     * 父节点ID
     */
    var parentId: String? = null,

    /**
     * 节点数据
     */
    var nodeData: NodeData? = null
) {

    /**
     * 子节点
     *
     * 此变量在编译期生成代码，如果要改名或者变更，
     * 请同步变更[com.oplus.gallery.framework.abilityapt.compiler.ConfigNodeProcessor]中对应的逻辑
     */
    var children: MutableList<ConfigNode>? = null

    /**
     * 节点所属的一级功能类别。此类别名称也是作为sp文件名，避免sp文件过多。
     */
    val topFunctionCategory: String by lazy {
        getFirstLevelNodeId()
    }

    /**
     * 全路径的KEY名
     */
    var qualifiedKey: String? = null

    /**
     * 父节点
     */
    var parentNode: ConfigNode? = null

    /**
     * 节点是否为根节点
     */
    fun root(): Boolean {
        return parentId == null
    }

    /**
     * 是否为叶子节点
     */
    fun isLeave(): Boolean {
        return children == null
    }

    /**
     * 添加子节点
     */
    fun addChild(configNode: ConfigNode) {
        children?.add(configNode)
    }

    /**
     * 获取第一个层级的节点ID，也就是一级功能的类别
     * 输入：business_search_searchAbility
     * 输出: business_search_config
     */
    private fun getFirstLevelNodeId(): String {
        val stringBuilder = StringBuilder()
        qualifiedKey?.let {
            val index = findIndex(it, SEPARATOR, FUNCTION_LEVEL_AS_SP_NAME_PREFIX + 1)
            return if (index == -1) {
                stringBuilder.append(it).append(CONFIG_FILE_SUFFIX).toString()
            } else {
                stringBuilder.append(it.substring(0, index)).append(CONFIG_FILE_SUFFIX).toString()
            }
        }
        return stringBuilder.toString()
    }

    /**
     * 找到一个字符在索引中的位置
     */
    private fun findIndex(originString: String, separator: String, num: Int): Int {
        var x = originString.indexOf(separator)
        if (x == -1 || num == 1) {
            return x
        }
        for (i in 1 until num) {
            x = originString.indexOf(separator, x + 1)
        }
        return x
    }

    /**
     * 从根路径到当前节点构成的完整KEY
     */
    private fun getConfigKey(): String {
        if (root()) {
            return id
        }
        val stringBuilder = StringBuilder()
        appendParentSegment(this, stringBuilder)
        return inverseKeySegment(stringBuilder.toString())
    }

    /**
     * 调换segment顺序;分段倒序
     */
    private fun inverseKeySegment(origin: String): String {
        val splits = origin.split(TMP_CONNECTOR)
        val stringBuilder = StringBuilder()
        for (i in splits.indices.reversed()) {
            if (i == 0) {
                stringBuilder.append(splits[i])
            } else {
                stringBuilder.append(splits[i]).append(SEPARATOR)
            }
        }
        return stringBuilder.toString()
    }

    private fun appendParentSegment(keyNode: ConfigNode?, stringBuilder: StringBuilder) {
        keyNode?.let {
            if (it.root()) {
                stringBuilder.append(it.id)
            } else {
                stringBuilder.append(it.id).append(TMP_CONNECTOR)
            }
            appendParentSegment(it.parentNode, stringBuilder)
        }
    }

    override fun toString(): String {
        return "{" +
                "id='" + id + '\'' +
                ", parentId ='" + parentId + '\'' +
                ", qualifiedKey='" + qualifiedKey + '\'' +
                ", spName ='" + topFunctionCategory + '\'' +
                ", children=" + children +
                '}'
    }

    companion object {

        /**
         * sp文件名后缀
         */
        const val CONFIG_FILE_SUFFIX = "_config"

        /**
         * 临时辅助拼接符
         */
        const val TMP_CONNECTOR = "#"

        /**
         * 分隔符
         */
        const val SEPARATOR = "_"

        /**
         * 配置sp文件名以几级功能类别为名, 建议为1
         */
        const val FUNCTION_LEVEL_AS_SP_NAME_PREFIX = 1
    }
}