/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Node.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/5/24
 ** Author: <EMAIL>

 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/24      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilityapt.annotations

/**
 * 配置节点的注解
 *
 * @property configType 配置所属类型
 * @property storageConfig 指定配置的多级存储,配置将会存储在哪些存储类型，决定配置的读写。
 * @property dataType 指定该配置以何种数据类型存储
 */
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.SOURCE)
annotation class Node(
    val configType: Int,
    val storageConfig: IntArray,
    val dataType: Int
)