/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Logger.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/5/24
 ** Author: <EMAIL>

 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/24      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.abilityapt.compiler

import java.lang.StringBuilder
import javax.annotation.processing.Messager
import javax.tools.Diagnostic

internal class Logger(messager: Messager?) {

    private val msg: Messager? = messager

    /**
     * Print info log.
     */
    fun info(info: CharSequence) {
        if (isNotEmpty(info.toString())) {
            msg?.printMessage(Diagnostic.Kind.NOTE, PREFIX_OF_LOGGER + info)
        }
    }

    fun error(error: CharSequence) {
        if (isNotEmpty(error.toString())) {
            msg?.printMessage(Diagnostic.Kind.ERROR, PREFIX_OF_LOGGER + "An exception is encountered, [" + error + "]")
        }
    }

    fun error(error: Throwable?) {
        if (null != error) {
            msg?.printMessage(
                Diagnostic.Kind.ERROR,
                PREFIX_OF_LOGGER + "An exception is encountered, [" + error.message + "]" + "\n" + formatStackTrace(error.stackTrace)
            )
        }
    }

    fun warning(warning: CharSequence) {
        if (isNotEmpty(warning.toString())) {
            msg?.printMessage(Diagnostic.Kind.WARNING, PREFIX_OF_LOGGER + warning)
        }
    }

    private fun isNotEmpty(string: String?): Boolean {
        return string != null && string.isNotEmpty()
    }

    private fun formatStackTrace(stackTrace: Array<StackTraceElement>): String {
        val sb = StringBuilder()
        for (element in stackTrace) {
            sb.append("    at ").append(element.toString())
            sb.append("\n")
        }
        return sb.toString()
    }

    companion object {
        const val PREFIX_OF_LOGGER = "::Compiler "
    }
}