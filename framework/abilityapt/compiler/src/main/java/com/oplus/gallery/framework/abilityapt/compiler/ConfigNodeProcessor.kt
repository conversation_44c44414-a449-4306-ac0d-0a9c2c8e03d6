/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigNodeProcessor.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/5/24
 ** Author: <EMAIL>

 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/24      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilityapt.compiler

import com.google.auto.service.AutoService
import com.google.common.annotations.VisibleForTesting
import com.oplus.gallery.framework.abilityapt.annotations.ConfigNode.Companion.SEPARATOR
import com.oplus.gallery.framework.abilityapt.annotations.Node
import com.oplus.gallery.framework.abilityapt.annotations.NodeGroup
import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.FileSpec
import com.squareup.kotlinpoet.FunSpec
import com.squareup.kotlinpoet.KModifier
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import com.squareup.kotlinpoet.TypeSpec
import com.squareup.kotlinpoet.asTypeName
import javax.annotation.processing.AbstractProcessor
import javax.annotation.processing.ProcessingEnvironment
import javax.annotation.processing.Processor
import javax.annotation.processing.RoundEnvironment
import javax.annotation.processing.SupportedSourceVersion
import javax.lang.model.SourceVersion
import javax.lang.model.element.Element
import javax.lang.model.element.TypeElement
import javax.lang.model.element.VariableElement

@AutoService(Processor::class)
@SupportedSourceVersion(SourceVersion.RELEASE_8)
class ConfigNodeProcessor : AbstractProcessor() {

    private var mapKey: ClassName = ClassName("kotlin", "String")

    private var configNode: ClassName = ClassName(PKG_NAME, "ConfigNode")

    private var nodeData: ClassName = ClassName(PKG_NAME, "NodeData")

    override fun init(processingEnv: ProcessingEnvironment?) {
        super.init(processingEnv)
    }

    override fun getSupportedAnnotationTypes(): MutableSet<String> {
        val set = mutableSetOf<String>()
        set.add(NodeGroup::class.java.name)
        set.add(Node::class.java.name)

        return set
    }

    override fun process(annotations: MutableSet<out TypeElement>?, roundEnvironment: RoundEnvironment?): Boolean {
        if ((annotations != null) && (annotations.isNotEmpty())) {
            val nodeGroupElement = roundEnvironment?.getElementsAnnotatedWith(NodeGroup::class.java)
            nodeGroupElement?.let {
                parseNodeGroup(it)
                return true
            }
        }
        return false
    }


    /**
     * 从id中得到合法的变量名称
     *
     * @param id 原始节点ID字符串
     * @return 合法的kotlin变量名称
     */
    @VisibleForTesting
    fun toValidKotlinVariable(id: String): String {
        return id.replace("[^0-9a-zA-z_]".toRegex(), "_")
    }

    @VisibleForTesting
    fun arrayToString(joinToString: IntArray): String {
        val stringBuilder = StringBuilder()
        for (i in joinToString.indices) {
            stringBuilder.append(joinToString[i])
            if (i < joinToString.size - 1) {
                stringBuilder.append(",")
            }
        }
        return stringBuilder.toString()
    }

    private fun createNodeSingle(funSpecBuilder: FunSpec.Builder, element: Element, id: String, parentId: String) {
        val nodeAnnotation = element.getAnnotation(Node::class.java)
        val configType = nodeAnnotation.configType
        val storageConfig = nodeAnnotation.storageConfig
        val dataType = nodeAnnotation.dataType
        funSpecBuilder.addCode(
            """                 
            |createNodeSingle($CONFIG_NODE_MAP_NAME, "$id", "$parentId", 
            |${parentId}ChildList, $configType, intArrayOf(${arrayToString(storageConfig)}), $dataType)
            |
            """.trimMargin()
        )
    }

    /**
     * 获取函数建造者，用来生成创建NodeGroup的代码
     * 如下：
     *   @JvmStatic
     *   private fun createNodeGroupSingle(
     *     configNodeMap: Map<String, ConfigNode>,
     *     id: String,
     *     parentId: String?,
     *     childNodeList: List<ConfigNode>?
     *   ) {
     *     val mutableConfigNodeMap = configNodeMap as MutableMap<String, ConfigNode>
     *     if(mutableConfigNodeMap[id]== null) {
     *         ConfigNode(
     *             id,
     *             parentId
     *         ).apply {
     *             this.parentNode = if(parentId == null) null else mutableConfigNodeMap[parentId]
     *             qualifiedKey = if(parentNode != null)
     *                 parentNode?.qualifiedKey + "_" + id else
     *                  id
     *             mutableConfigNodeMap[id] = this
     *             if(childNodeList != null) {
     *                 (childNodeList as MutableList<ConfigNode>).add(this)
     *             }
     *         }
     *     }
     *   }
     */
    private fun getFunBuilderCreateNodeGroupSingle(): FunSpec.Builder {
        val funSpecBuilder: FunSpec.Builder = FunSpec.builder("createNodeGroupSingle")
            .addModifiers(KModifier.PRIVATE)
            .addAnnotation(JvmStatic::class.java)
            .addParameter("configNodeMap", MutableMap::class.asTypeName().parameterizedBy(mapKey, configNode))
            .addParameter("id", String::class)
            .addParameter("parentId", String::class.asTypeName().copy(nullable = true))
            .addParameter("childNodeList", MutableList::class.asTypeName().parameterizedBy(configNode).copy(nullable = true))
        funSpecBuilder.addCode(
            """
               |val mutableConfigNodeMap = configNodeMap as MutableMap<String, ConfigNode>
               |if(mutableConfigNodeMap[id]== null) {
               |    ConfigNode(
               |        id,
               |        parentId
               |    ).apply {
               |        this.parentNode = if(parentId == null) null else mutableConfigNodeMap[parentId]
               |        qualifiedKey = if(parentNode != null) 
               |            parentNode?.qualifiedKey + "_" + id else  id
               |        mutableConfigNodeMap[id] = this
               |        if(childNodeList != null) {
               |            (childNodeList as MutableList<ConfigNode>).add(this)
               |        }
               |    }
               |}
               |          
            """.trimMargin()
        )
        return funSpecBuilder
    }

    /**
     * 获取函数创建者，用来生成创建Node的代码
     * 如下：
     *   @JvmStatic
     *   private fun createNodeSingle(
     *     configNodeMap: Map<String, ConfigNode>,
     *     id: String,
     *     parentId: String?,
     *     childNodeList: List<ConfigNode>?,
     *     configType: Int,
     *     storageConfig: IntArray,
     *     dataType: Int
     *   ) {
     *     val mutableConfigNodeMap = configNodeMap as MutableMap<String, ConfigNode>
     *     ConfigNode(id,
     *     parentId,
     *     NodeData(configType, storageConfig, dataType)
     *     ).apply {
     *            parentNode = mutableConfigNodeMap[parentId]
     *            qualifiedKey = parentNode?.qualifiedKey + "_" + id
     *            mutableConfigNodeMap[id] = this
     *            if(childNodeList != null) {
     *                (childNodeList as MutableList<ConfigNode>).add(this)
     *            }
     *        }
     *
     *   }
     */
    private fun getFunBuilderCreateNodeSingle(): FunSpec.Builder {
        val funSpecBuilder: FunSpec.Builder = FunSpec.builder("createNodeSingle")
            .addModifiers(KModifier.PRIVATE)
            .addAnnotation(JvmStatic::class.java)
            .addParameter("configNodeMap", MutableMap::class.asTypeName().parameterizedBy(mapKey, configNode))
            .addParameter("id", String::class)
            .addParameter("parentId", String::class.asTypeName().copy(nullable = true))
            .addParameter("childNodeList", MutableList::class.asTypeName().parameterizedBy(configNode).copy(nullable = true))
            .addParameter("configType", Int::class)
            .addParameter("storageConfig", IntArray::class)
            .addParameter("dataType", Int::class)

        funSpecBuilder.addCode(
            """
                |val mutableConfigNodeMap = configNodeMap as MutableMap<String, ConfigNode>
                |ConfigNode(id, 
                |parentId,
                |NodeData(configType, storageConfig, dataType)
                |).apply {
                |       parentNode = mutableConfigNodeMap[parentId]
                |       qualifiedKey = parentNode?.qualifiedKey + "$SEPARATOR" + id
                |       mutableConfigNodeMap[id] = this           
                |       if(childNodeList != null) {
                |           (childNodeList as MutableList<ConfigNode>).add(this)
                |       }
                |   }                       
            """.trimMargin()
        )
        return funSpecBuilder
    }

    /**
     * 解析节点信息，编译时会树状遍历，从上到下遍历：根节点一直到子节点
     *
     * 这里会在遍历时关联父子关系，生成获取整个配置树的代码
     */
    private fun parseNodeGroup(nodeGroupElement: MutableSet<out Element>) {
        val funSpecBuilder: FunSpec.Builder = FunSpec.builder("generateNodes")
            .addModifiers(KModifier.PUBLIC)
            .addAnnotation(JvmStatic::class.java)
            .returns(
                MutableMap::class.asTypeName().parameterizedBy(mapKey, configNode)
            )
        funSpecBuilder.addStatement("val nodeDataForImport = %1T(%2L, intArrayOf(%3L), %4L)", nodeData, 0, arrayToString(intArrayOf(0)), 0)
        funSpecBuilder.addStatement("val %1L = mutableMapOf<%2T, %3T>()", CONFIG_NODE_MAP_NAME, mapKey, configNode)
        nodeGroupElement.forEach { parentElement ->
            val nodeGroup = parentElement.getAnnotation(NodeGroup::class.java)
            // 子节点队列，遍历完成后，会加入到父节点中
            funSpecBuilder.addStatement("val %1LChildList = mutableListOf<%2T>()", nodeGroup.id, configNode)
            // 创建父节点nodeGroup
            funSpecBuilder.addCode(
                """                 
                    |createNodeGroupSingle(configNodeMap, "${nodeGroup.id}", null, null)
                    |
                """.trimMargin()
            )
            parentElement.enclosedElements.forEach {
                if (it.simpleName.toString() != "<init>" &&
                    it.simpleName.toString() != "INSTANCE" &&
                    it.simpleName.toString() != "Companion"
                ) {
                    if (it.kind.isField) {
                        val variableElement = it as VariableElement
                        val nodeId = variableElement.constantValue.toString()
                        // 创建node
                        createNodeSingle(funSpecBuilder, it, nodeId, nodeGroup.id)
                    } else {
                        val childNodeGroup = it.getAnnotation(NodeGroup::class.java)
                        funSpecBuilder.addCode(
                            """                 
                            |createNodeGroupSingle(configNodeMap, "${childNodeGroup.id}", "${nodeGroup.id}", ${nodeGroup.id}ChildList)
                            |
                            """.trimMargin()
                        )
                    }
                }
            }
            funSpecBuilder.addStatement("%1L[%2S]?.children = %3LChildList", CONFIG_NODE_MAP_NAME, nodeGroup.id, nodeGroup.id)
        }
        funSpecBuilder.addStatement("return %1L", CONFIG_NODE_MAP_NAME)


        val typeSpecBuilder = TypeSpec.objectBuilder(GEN_CLS_NAME)
        typeSpecBuilder.addFunction(funSpecBuilder.build())
        val funSpecBuilderCreateNodeGroup: FunSpec.Builder = getFunBuilderCreateNodeGroupSingle()
        val funSpecBuilderCreateNodeSingle: FunSpec.Builder = getFunBuilderCreateNodeSingle()
        typeSpecBuilder.addFunction(funSpecBuilderCreateNodeGroup.build())
        typeSpecBuilder.addFunction(funSpecBuilderCreateNodeSingle.build())
        val fileSpec: FileSpec = FileSpec.builder(GEN_PKG, GEN_CLS_NAME)
            .addType(typeSpecBuilder.build())
            .build()
        fileSpec.writeTo(processingEnv.filer)
    }

    companion object {

        private const val PKG_NAME = "com.oplus.gallery.framework.abilityapt.annotations"

        private const val GEN_PKG = "com.oplus.gallery.framework.abilities.config.generated"

        private const val GEN_CLS_NAME = "ConfigNodeGenerator"

        private const val CONFIG_NODE_MAP_NAME = "configNodeMap"
    }
}