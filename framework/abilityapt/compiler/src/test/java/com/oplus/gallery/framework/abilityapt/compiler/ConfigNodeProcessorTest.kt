/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigNodeProcessorTest.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/7/08
 ** Author: <EMAIL>

 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/7/08      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilityapt.compiler

import org.junit.Assert.assertEquals
import org.junit.Test

class ConfigNodeProcessorTest {

    private val configNodeProcessor = ConfigNodeProcessor()

    @Test
    fun `should return right kotlinVarName when toValidKotlinVariable with specialCharacter$`() {
        // given
        val originString = "key_x_${'$'}a.#"

        // when
        val result = configNodeProcessor.toValidKotlinVariable(originString)

        // then
        assertEquals("key_x__a__", result)
    }

    @Test
    fun `should return right kotlinVarName when toValidKotlinVariable with otherSpecialCharacter`() {
        // given
        val originString = "key_x_a._test?&#"

        // when
        val result = configNodeProcessor.toValidKotlinVariable(originString)

        // then
        assertEquals("key_x_a__test___", result)
    }

    @Test
    fun `should return right kotlinVarName when toValidKotlinVariable with noSpecialCharacter`() {
        // given
        val originString = "key_test_gallery_debug_environment"

        // when
        val result = configNodeProcessor.toValidKotlinVariable(originString)

        // then
        assertEquals(originString, result)
    }

    @Test
    fun `should return joinString when arrayToString with intArray`() {
        // given
        val intArray = intArrayOf(1, 3, 5, 6)

        // when
        val result = configNodeProcessor.arrayToString(intArray)

        // then
        assertEquals("1,3,5,6", result)
    }
}