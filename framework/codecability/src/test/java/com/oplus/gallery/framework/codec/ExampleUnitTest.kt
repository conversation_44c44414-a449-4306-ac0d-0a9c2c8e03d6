/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PictureEditorModule.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/2/17
 ** Author: Jin<PERSON><PERSON>@Rom.Apps.Gallery
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  Jin<PERSON><EMAIL>       2022/2/17       1.0           Add
 **************************************************************************************************/

package com.oplus.gallery.framework.codec

import org.junit.Test

import org.junit.Assert.assertEquals

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }
}