/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MediaExpandUtils.kt
 ** Description : 媒体解析工具
 ** Version     : 1.0
 ** Date        : 2025/3/5
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D          2025/3/5    1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.mediadbsync.parse.expand

import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_BIT_RATE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_COLOR_STANDARD
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_COLOR_TRANSFER
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_FPS
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_HEIGHT
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_HEIGHT_WITHOUT_WATERMARK
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_LENGTH
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_PROFILE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_START_OFFSET
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_WIDTH
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_WIDTH_WITHOUT_WATERMARK
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_PICTURE_HEIGHT_WITHOUT_WATERMARK
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_PICTURE_WIDTH_WITHOUT_WATERMARK
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_VIDEO_FRAME_RATE
import com.oplus.gallery.foundation.util.ext.putIfMatch
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil

/**
 * 媒体解析工具
 */
object MediaExpandUtils {
    /**
     * 将 extra_msg 的 map 转成 json，并且将值为默认值的都置为 null
     */
    @JvmStatic
    fun convertExtraMsgMapToJsonWithTrim(extraMsgMap: MutableMap<String, Any?>): String? {
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_COLOR_STANDARD, null) { value -> value == 0 }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_COLOR_TRANSFER, null) { value -> value == 0 }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_PROFILE, null) { value -> value == -1 }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_WIDTH, null) { value -> value == 0 }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_HEIGHT, null) { value -> value == 0 }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_FPS, null) { value -> value == 0F }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE, null) { value -> value == TextUtil.EMPTY_STRING }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_BIT_RATE, null) { value -> value == 0L }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_START_OFFSET, null) { value -> value == 0L }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_LENGTH, null) { value -> value == 0L }
        extraMsgMap.putIfMatch(EXTRA_KEY_VIDEO_FRAME_RATE, null) { value -> value == 0f }
        extraMsgMap.putIfMatch(EXTRA_KEY_PICTURE_WIDTH_WITHOUT_WATERMARK, null) { value -> value == 0 }
        extraMsgMap.putIfMatch(EXTRA_KEY_PICTURE_HEIGHT_WITHOUT_WATERMARK, null) { value -> value == 0 }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_WIDTH_WITHOUT_WATERMARK, null) { value -> value == 0 }
        extraMsgMap.putIfMatch(EXTRA_KEY_OLIVE_VIDEO_HEIGHT_WITHOUT_WATERMARK, null) { value -> value == 0 }
        return JsonUtil.toJson(extraMsgMap)
    }
}