/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ExtTagFlagParser.kt
 ** Description: Parser for EXT_TAG_FLAGS column on table local_media
 **
 ** Version: 1.0
 ** Date:  2023/03/28
 ** Author: WUDANYANG
 ** TAG:ExtTagFlagParser
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** WUDANYANG                      2023/03/28		1.0		ExtTagFlagParser
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.mediadbsync.parse

import android.media.ExifInterface
import android.os.Build
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.util.ext.getStringArraySafe
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.foundation.util.text.TextUtil.SPLIT_LINE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.MARKET_NAME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.PHONE_MODEL
import com.oplus.gallery.framework.abilities.groupphoto.IGroupPhotoAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * EXT_TAG_FLAGS字段专用解析类
 */
class ExtTagFlagsParser {

    /**
     * 是否是合影优化后的图片
     * @param filePath 文件路径
     * @param tagFlags 文件的TagFlag
     * @return 是否是合影优化后的图片
     */
    fun isGroupPhotoOptimized(filePath: String, tagFlags: Int): Boolean {
        if ((tagFlags and Constants.CameraMode.FLAG_SUPPORT_GROUP_PHOTO) == 0) {
            return false
        }
        var isOptimized = false

        ContextGetter.context.getAppAbility<IGroupPhotoAbility>()?.use { ability ->
            val state = ability.getGroupPhotoOptimizedState(filePath)
            if (state.isOptimized) {
                isOptimized = true
            }
        } ?: let {
            GLog.d(TAG, "[isGroupPhotoOptimized] IGroupPhotoAbility is null")
        }

        return isOptimized
    }

    /**
     * 是否拍摄自本机机型相同机型
     * @param exifInterface exif接口
     * @return 是否是本机相同机型拍摄的照片
     */
    fun isShotBySameDevice(exifInterface: ExifInterface): Boolean {
        val model: String? = exifInterface.getAttribute(ExifInterface.TAG_MODEL)
        val marketName = ConfigAbilityWrapper.getString(MARKET_NAME)
        // Mark by wudanyang 稍后改成config调用
        val marketEnName = GallerySystemProperties.get(FeatureUtils.KEY_RO_OPLUS_MARKET_ENNAME, Build.MODEL)
        val phoneModel = ConfigAbilityWrapper.getString(PHONE_MODEL)
        /**
         * 判断当前机型的marketName是否等于图片的model
         * 1、model等于marketName，或者包含时，判断是同一机型
         * 2、model等于phoneModel（非商用版本model会写入这个值），判断是同一机型
         */
        if (!model.isNullOrEmpty() && !marketName.isNullOrEmpty() && isSameDevice(model, marketName, marketEnName, phoneModel)) {
            return true
        }
        return false
    }

    /**
     * 判定是否需要优化Local HDR图片metadata的版本号
     * @param exifInterface ExifInterface
     * @param tagFlags Int
     * @return Boolean
     */
    fun shouldOptimizeLhdrMetaVersion(exifInterface: ExifInterface, tagFlags: Int): Boolean {
        /**
         * 如果不是Local HDR图片，不需要优化
         */
        if (tagFlags and OplusExifTag.EXIF_TAG_LOCAL_HDR != OplusExifTag.EXIF_TAG_LOCAL_HDR) {
            return false
        }

        /**
         * 如果图片的Model字段，不在需要优化的名单中，不需要优化
         * 进名单中的机型拍摄的图片，才需要被优化
         */
        val model = exifInterface.getAttribute(ExifInterface.TAG_MODEL)
        if (model.isNullOrEmpty()) {
            return false
        }

        if (forceOptimizeLhdrMetaVersionDevices.contains(model).not()) {
            return false
        }

        return true
    }

    /**
     * 白名单中的机型认为是相同机型
     */
    private fun isSameDeviceOnWhitelist(model: String): Boolean {
        if (devices.isNullOrEmpty()) return false
        return devices.contains(model)
    }

    /**
     * 判断是否相同机型：
     * 1、照片exif中的model值等于当前手机marketName（对应大多数case）
     * 2、照片exif中的model值等于当前手机model（部分case写到exif中的model是手机model）
     * 3、照片exif中的model值等于当前手机marketEnName
     * 4、特殊白名单下（如FindX 6），认为该系列机型属于相同机型（为了兼容旧的wukong、luna的数据）
     */
    private fun isSameDevice(
        model: String,
        marketName: String,
        marketEnName: String?,
        phoneModel: String?
    ) = (model == marketName) || (model == marketEnName) || (model == phoneModel) || isSameDeviceOnWhitelist(model)

    companion object {
        private const val TAG = "ExtTagFlagsParser"

        private val devices by lazy {
            getDevicesOnWhitelist()
        }

        private val forceOptimizeLhdrMetaVersionDevices: Set<String> by lazy {
            ContextGetter.context.getStringArraySafe(com.oplus.gallery.basebiz.R.array.force_optimize_lhdr_meta_version).toSet()
        }

        private fun getDevicesOnWhitelist(): Set<String> {
            val series = ContextGetter.context.getStringArraySafe(com.oplus.gallery.basebiz.R.array.support_blur_edit_after_share_series)
            ConfigAbilityWrapper.getString(MARKET_NAME)?.also { name ->
                for (item in series) {
                    val devices = item.split(SPLIT_LINE).toSet()
                    // 匹配到本机机型
                    if (devices.contains(name)) {
                        return devices
                    }
                }
            }
            return emptySet()
        }
    }
}