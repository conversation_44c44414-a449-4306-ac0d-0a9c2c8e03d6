/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ScopedStorageMediaExpandParser .java
 ** Description:
 **     parseMediaExpandFieldForScopedStorage
 **
 ** Version: 1.0
 ** Date: 2020-04-15
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_PARSE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2020/04/15   1.0          Build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.mediadbsync.parse.expand;

import static com.oplus.breakpad.BreakpadUtil.generateKey;
import static com.oplus.breakpad.NativeCrashGuardKt.runNativeGuarding;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_ENHANCE_TEXT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_PORTRAIT_BLUR;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_SUPPORT_OLIVE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_ULTRA_HDR;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_WATERMARK;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.ExtendAttributes.EXT_OLIVE_DOLBY_VIDEO;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.ExtendAttributes.EXT_OLIVE_HDR_VIDEO;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_BIT_RATE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_COLOR_STANDARD;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_COLOR_TRANSFER;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_FPS;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_HEIGHT;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_HEIGHT_WITHOUT_WATERMARK;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_LENGTH;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_PROFILE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_START_OFFSET;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_WIDTH;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_OLIVE_VIDEO_WIDTH_WITHOUT_WATERMARK;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_PICTURE_HEIGHT_WITHOUT_WATERMARK;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXTRA_KEY_PICTURE_WIDTH_WITHOUT_WATERMARK;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_HDR_VIDEO;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ULTRA_HDR;

import android.content.ContentResolver;
import android.content.Context;
import android.graphics.RectF;
import android.media.ExifInterface;
import android.media.MediaFormat;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Size;

import com.oplus.gallery.addon.graphics.OplusImageHdrWrapper;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.business_lib.model.data.base.utils.Constants;
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils;
import com.oplus.gallery.business_lib.videoedit.IMetadataGetter;
import com.oplus.gallery.business_lib.videoedit.MetadataGetterFactory;
import com.oplus.gallery.business_lib.videoedit.MetadataKey;
import com.oplus.gallery.foundation.codec.supertext.SuperTextCodecFactory;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.exif.R;
import com.oplus.gallery.foundation.exif.oplus.OplusExifInterface;
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag;
import com.oplus.gallery.foundation.exif.raw.ExifUtils;
import com.oplus.gallery.foundation.exif.utils.ExifTagParser;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest;
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer;
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;
import com.oplus.gallery.frameextractor_lib.IMediaMetadataRetriever;
import com.oplus.gallery.framework.abilities.mediadbsync.parse.LocalMediaDataManager;
import com.oplus.gallery.framework.abilities.mediadbsync.parse.latlng.LatLngParser;
import com.oplus.gallery.framework.abilities.watermark.IWatermarkFileOperator;
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility;
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo;
import com.oplus.gallery.framework.abilities.watermark.masterextend.AiWatermarkFileExtendInfo;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.olive_decoder.OLiveDecode;
import com.oplus.gallery.olive_decoder.OLivePhoto;
import com.oplus.gallery.standard_lib.codec.yuv.common.YuvUtils;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Pair;
import kotlin.Triple;

public class ScopedStorageMediaExpandParser extends MediaExpandParser {
    private static final String TAG = "ScopedStorageMediaExpandParser";
    private static final String[] TAGFLAGS_PREFIXES = ContextGetter.context.getResources().getStringArray(R.array.base_tagflags_prefixes);
    private static final String OPEN_FD_MODE_R = "r";
    private static final String OPEN_FD_MODE_RW = "rw";
    private static final String END_DELIMITER = "\0";
    private static final String THUMB_GLOBAL_ID_KEY = "thumb_globalid";
    private static final int TAG_FLAG_NONE = 0;
    private static final Long INVALID_TIME = -1L;
    private static final int DEFAULT_DIMENSION_SIZE = 0;
    private final boolean mIsFeatureSupportUHdr;
    private final boolean mIsFeatureSupportOlive;
    private final boolean mIsFeatureSupportOliveHdrVideo;
    private final ContentResolver mContentResolver;


    public ScopedStorageMediaExpandParser(ContentResolver resolver) {
        if (resolver == null) {
            throw new NullPointerException("ContentResolver must not be null");
        }
        mContentResolver = resolver;
        mIsFeatureSupportUHdr = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ULTRA_HDR);
        mIsFeatureSupportOlive = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OLIVE);
        mIsFeatureSupportOliveHdrVideo = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OLIVE_HDR_VIDEO);
    }

    @Override
    public void parseMediaExpandFieldByEntries(List<LocalMediaDataManager.Entry> entries,
                                               MediaExpandParseCallback<LocalMediaDataManager.Entry> callback) {
        if (entries == null) {
            GLog.w(TAG, "parseMediaExpandFieldByEntries, entries is null");
            return;
        }
        if (callback == null) {
            GLog.w(TAG, "parseMediaExpandFieldByEntries, callback is null");
            return;
        }
        int count = entries.size();
        for (int i = 0; i < count; i++) {
            LocalMediaDataManager.Entry entry = entries.get(i);
            if (entry == null) {
                GLog.w(TAG, "parseMediaExpandFieldByEntries, entry is null");
                continue;
            }
            if (!callback.onResult(entry, i, parseMediaExpandFieldByEntry(entry))) {
                return;
            }
        }
    }

    public MediaExpandEntry parseMediaExpandFieldByEntry(LocalMediaDataManager.Entry entry) {
        if (entry == null) {
            return null;
        }
        if (entry.mMediaId > 0) {
            return parseByMediaId(mContentResolver, entry);
        } else {
            return parseByFilePath(entry);
        }
    }

    private MediaExpandEntry parseByMediaId(ContentResolver contentResolver, LocalMediaDataManager.Entry entry) {
        if (TextUtils.isEmpty(entry.mPath)) {
            GLog.e(TAG, "parseByMediaId, filePath is null or empty");
            return null;
        }
        File file = new File(entry.mPath);
        if (file.length() <= 0) {
            MediaExpandEntry resultEntry = new MediaExpandEntry();
            resultEntry.setInvalidFile(true);
            GLog.e(TAG, "parseByMediaId file.length() <= 0");
            return resultEntry;
        }
        if (entry.mType == MEDIA_TYPE_IMAGE) {
            MediaExpandEntry expandEntry = getImageMediaExpandByMediaId(contentResolver, entry);
            if (LocalMediaDataManager.DEBUG) {
                GLog.d(TAG, "parseByMediaId, image: " + expandEntry);
            }
            return expandEntry;
        } else if (entry.mType == MEDIA_TYPE_VIDEO) {
            MediaExpandEntry expandEntry = getVideoMediaExpandByMediaId(contentResolver, entry,
                    IMediaMetadataRetriever.create(IMediaMetadataRetriever.MediaMetadataRetrieverVendor.DEFAULT));
            /**
             * 14.0.0开始新U项目系统移除了ffmpeg。
             * 需要在系统MediaMetadataRetriever报错或视频时长获取小于等于0后再尝试用TBLMediaMetadataRetriever解析一次。
             * 直接全部替换为TBLMediaMetadataRetriever验证性能比MediaMetadataRetriever差，因此采用此兜底方案。
             */

            if (expandEntry == null || expandEntry.getDuration() <= 0) {
                expandEntry = getVideoMediaExpandByMediaId(contentResolver, entry,
                        IMediaMetadataRetriever.create(IMediaMetadataRetriever.MediaMetadataRetrieverVendor.TBL));
            }
            if (LocalMediaDataManager.DEBUG) {
                GLog.d(TAG, "parseByMediaId, video: " + expandEntry);
            }
            return expandEntry;
        } else {
            GLog.e(TAG, "parseByMediaId, invalid type: " + entry.mType);
        }
        return null;
    }

    private MediaExpandEntry getVideoMediaExpandByMediaId(ContentResolver contentResolver, LocalMediaDataManager.Entry entry,
                                                          IMediaMetadataRetriever metadataRetriever) {
        Uri uri = Uri.withAppendedPath(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, String.valueOf(entry.mMediaId));
        uri = MediaStore.setRequireOriginal(uri);
        ParcelFileDescriptor descriptor = null;
        try {
            if (contentResolver == null) {
                GLog.w(TAG, "getVideoMediaExpandByMediaId, contentResolver is null");
                return null;
            }
            descriptor = contentResolver.openFileDescriptor(uri, "r");
            if (descriptor == null) {
                GLog.w(TAG, "getVideoMediaExpandByMediaId, descriptor is null");
                return null;
            }

            FileDescriptor fileDescriptor = descriptor.getFileDescriptor();
            if (fileDescriptor == null) {
                GLog.w(TAG, "getVideoMediaExpandByMediaId, fileDescriptor is null");
                return null;
            }

            metadataRetriever.setDataSource(fileDescriptor);

            MediaExpandEntry expandEntry = new MediaExpandEntry();
            expandEntry.setId(entry.mMediaId);
            parseVideoLocation(expandEntry, metadataRetriever);

            parseTagFlags(entry, expandEntry, metadataRetriever);
            parseVideoExtTagFlags(entry, expandEntry, metadataRetriever);

            String widthValue = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
            String heightValue = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
            int width = parseSubstring(widthValue, 0, 0);
            int height = parseSubstring(heightValue, 0, 0);
            if ((width > 0) && (height > 0)) {
                expandEntry.setResolution(width + "x" + height);
            }

            String orientationValue = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION);
            int orientation = parseSubstring(orientationValue, 0, 0);
            expandEntry.setOrientation(orientation);

            final String durationStr = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            if (!TextUtils.isEmpty(durationStr)) {
                int duration = Integer.parseInt(durationStr);
                if (duration > 0) {
                    expandEntry.setDuration(duration);
                }
            }
            getVideoCommonAttribute(entry, expandEntry, metadataRetriever);
            return expandEntry;
        } catch (Exception e) {
            GLog.w(TAG, "getVideoMediaExpandByMediaId, e:" + e);
        } finally {
            // 需要调用release方法，避免native内存不释放
            metadataRetriever.release();
            // 后调用，避免提前关闭导致metadataRetriever异常
            IOUtils.closeQuietly(descriptor);
        }
        return null;
    }

    /**
     * 解析视频位置信息
     */
    private void parseVideoLocation(MediaExpandEntry expandEntry, IMediaMetadataRetriever metadataRetriever) {
        String locationStr = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_LOCATION);
        float[] latlng = LatLngParser.parseVideoLocationStr(locationStr);
        expandEntry.setLatitude(latlng[0]);
        expandEntry.setLongitude(latlng[1]);
    }

    private MediaExpandEntry getImageMediaExpandByMediaId(ContentResolver contentResolver, LocalMediaDataManager.Entry entry) {
        MediaExpandEntry expandEntry = new MediaExpandEntry();
        expandEntry.setId(entry.mMediaId);
        parseBitFormat(contentResolver, expandEntry, entry);
        if (ExifUtils.isSupportedExif(entry.mMimeType)) {
            getMediaExpandEntryFromExifInterface(contentResolver, entry, expandEntry);
        } else {
            getImageCommonAttribute(entry, expandEntry, null);
        }
        OpMediaExpandUtils.parseOpCaptureMode(contentResolver, entry, expandEntry);
        return expandEntry;
    }

    private void getMediaExpandEntryFromExifInterface(ContentResolver contentResolver, LocalMediaDataManager.Entry entry,
                                                      MediaExpandEntry expandEntry) {
        Uri mediaUri = Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, String.valueOf(entry.mMediaId));
        Uri originUri = MediaStore.setRequireOriginal(mediaUri);
        if (contentResolver == null) {
            GLog.w(TAG, "[getImageMediaExpandByMediaId] contentResolver is null");
            return;
        }
        ExifInterface exifInterface = null;
        File file = new File(entry.mPath);
        String openFileDescriptorMode = getOpenFileDescriptorMode(entry, file);
        ParcelFileDescriptor parcelFileDescriptor = null;
        ArrayList<Long> confirmedExtTagFlags = new ArrayList<>();
        try {
            parcelFileDescriptor = contentResolver.openFileDescriptor(originUri, openFileDescriptorMode);
            if (parcelFileDescriptor == null) {
                GLog.w(TAG, "[getImageMediaExpandByMediaId] parcelFileDescriptor is null");
                return;
            }
            FileDescriptor fileDescriptor = parcelFileDescriptor.getFileDescriptor();
            if (fileDescriptor == null) {
                GLog.w(TAG, "[getImageMediaExpandByMediaId] fileDescriptor is null");
                return;
            }
            exifInterface = new ExifInterface(fileDescriptor);

            parseCommon(exifInterface, expandEntry);

            long tagFlags = ExifTagParser.getImageTagFlag(exifInterface, fileDescriptor, null, TAGFLAGS_PREFIXES);
            tagFlags = modifyTagFlagsAndWriteExifForSlimmingFileBug(tagFlags, exifInterface, file, parcelFileDescriptor, openFileDescriptorMode);
            tagFlags = modifyTagFlagsForSuperTextMissingBug(tagFlags, entry);
            tagFlags = modifyTagFlagsForUHDRImage(tagFlags, entry, fileDescriptor);

            Map<String, Object> extraMsgMap = new HashMap<>();
            tagFlags = parseOliveFlagsAndVideoInfo(tagFlags, entry, mediaUri, parcelFileDescriptor, confirmedExtTagFlags, expandEntry, extraMsgMap);
            parseSizeWithoutWatermark(parcelFileDescriptor, exifInterface, extraMsgMap);

            expandEntry.setExtraMsg(MediaExpandUtils.convertExtraMsgMapToJsonWithTrim(extraMsgMap));

            if (tagFlags != INVALID) {
                expandEntry.setTagFlags(tagFlags);
            }
            setExtTagFlags(entry, expandEntry, exifInterface, confirmedExtTagFlags);
        } catch (Exception e) {
            if (LocalMediaDataManager.DEBUG) {
                GLog.w(TAG, "[getImageMediaExpandByMediaId] e:" + e);
            }
        } finally {
            getImageCommonAttribute(entry, expandEntry, exifInterface);
            IOUtils.closeQuietly(parcelFileDescriptor);
        }
    }

    /**
     * 解析文件通用属性
     * @param exifInterface exif对象
     * @param expandEntry 输出对象
     */
    private void parseCommon(ExifInterface exifInterface, MediaExpandEntry expandEntry) {
        float[] latLng = LatLngParser.createDefaultLatLng();
        exifInterface.getLatLong(latLng);
        expandEntry.setLatitude(latLng[0]);
        expandEntry.setLongitude(latLng[1]);

        int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, INVALID);
        if (orientation != INVALID) {
            int degree = ExifUtils.orientationToDegree(orientation);
            expandEntry.setOrientation(degree);
        }
    }

    /**
     * 修复超级文本 tagFlags 缺失问题
     */
    public long modifyTagFlagsForSuperTextMissingBug(long inputTagFlags, LocalMediaDataManager.Entry entry) {
        long tagFlags = inputTagFlags;
        if ((tagFlags == INVALID) || ((tagFlags & FLAG_ENHANCE_TEXT) == 0)) {
            /*
             * 如果exif中解析出来的tagFlag不是超级文本，但是文件解析是超级文本，则认为是超级文本。
             * 解决历史上相机有一批超级文本图片没有写入超级文本tagFlag的问题
             */
            if (SuperTextCodecFactory.create(entry.mPath, entry.mMediaId, false).isEnhanceText()) {
                tagFlags = resetTagFlagsIfInvalid(tagFlags) | Constants.CameraMode.FLAG_ENHANCE_TEXT;
            }
        }
        return tagFlags;
    }

    /**
     * 判断 UHDR 文件，更新 tagFlags
     */
    public long modifyTagFlagsForUHDRImage(long inputTagFlags, LocalMediaDataManager.Entry entry, FileDescriptor fileDescriptor) {
        long tagFlags = inputTagFlags;
        if (mIsFeatureSupportUHdr && ((tagFlags == INVALID) || ((tagFlags & FLAG_ULTRA_HDR) == 0))) {
            boolean isUHdrImage = OplusImageHdrWrapper.isUhdrImage(fileDescriptor);
            if (GProperty.getDEBUG_HDR_DISPLAY()) {
                GLog.d(TAG, "[modifyTagFlagsForUHDRImage] tagFlags:" + tagFlags
                        + " ,isUHdrImage:" + isUHdrImage
                        + " ,mMediaId:" + entry.mMediaId);
            }
            if (isUHdrImage) {
                tagFlags = resetTagFlagsIfInvalid(tagFlags) | FLAG_ULTRA_HDR;
            }
        }
        return tagFlags;
    }

    /**
     * 解析olive文件的 tagFlags，extTagFlags，以及 extra_msg 中定义的相关字段
     * @return tagFlags 需要更新的 tagFlags
     */
    public long parseOliveFlagsAndVideoInfo(long inputTagFlags, LocalMediaDataManager.Entry entry, Uri uri, ParcelFileDescriptor parcelFileDescriptor,
                                           ArrayList<Long> confirmedExtTagFlags, MediaExpandEntry expandEntry, Map<String, Object> extraMsgMap) {
        long tagFlags = inputTagFlags;
        if (mIsFeatureSupportOlive && (tagFlags != INVALID) && ((tagFlags & FLAG_SUPPORT_OLIVE) != 0)) {
            Triple<Boolean, Long, Long> infos = checkInvalidOliveFile(parcelFileDescriptor, entry.mPath, entry);
            boolean isOliveInvalid = infos.getFirst();
            long videoOffset = infos.getSecond();
            long videoLength = infos.getThird();

            if (isOliveInvalid) { // 不是有效的Olive文件
                /* 当：
                 * 1.当机器支持olive图时： 当feature不支持olive需求时，不做任何校验，减少功耗
                 * 2.当tagFlags为olive时：只对有olive tag的文件进行校验，当前此tag是识别olive图片的基础必要条件
                 * 3.当具备olive tag文件是无效文件时：需要检验olive文件/其基础信息是否完整
                 *
                 * 需要摸除当前已存在的olive tag
                 */
                tagFlags ^= FLAG_SUPPORT_OLIVE;
            } else {
                if ((videoOffset <= 0) || (videoLength <= 0)) {
                    GLog.e(TAG, LogFlag.DL, "[parseOliveFlagsAndVideoInfo] video data is wrong(videoOffset=" + videoOffset
                            + " videoLength=" + videoLength + "), " + "skip retrieve.");
                    return tagFlags;
                }
                // 解析 extra msg 所需要的信息
                IMetadataGetter defaultGetter = MetadataGetterFactory.create(MetadataGetterFactory.DEFAULT_GETTER);
                Map<Integer, Object> metadataResultMap = defaultGetter.parse(
                        MetadataKey.getDefaultMetadataList(),
                        parcelFileDescriptor.getFileDescriptor(),
                        new Pair<>(videoOffset, videoLength)
                );

                // 填充 ExtraMsg
                convertBatchMetaMapToExtraMsgMap(metadataResultMap, extraMsgMap);
                extraMsgMap.put(EXTRA_KEY_OLIVE_VIDEO_START_OFFSET, videoOffset);
                extraMsgMap.put(EXTRA_KEY_OLIVE_VIDEO_LENGTH, videoLength);

                // 填充 extTagFlag
                if (mIsFeatureSupportOliveHdrVideo) { // 有效的Olive文件，去判断是否支持HDR
                    confirmedExtTagFlags.addAll(retrieveOliveVideoExtTagFlags(metadataResultMap));
                }
            }
        }
        return tagFlags;
    }

    /**
     * 获取不包含水印的图片尺寸
     *
     * @param pfd 文件描述符
     * @param exif ExifInterface
     * @param extraMsgMap 存储结果的 map
     */
    private void parseSizeWithoutWatermark(ParcelFileDescriptor pfd, ExifInterface exif, Map<String, Object> extraMsgMap) {
        Size pictureSize = new Size(
                exif.getAttributeInt(ExifInterface.TAG_IMAGE_WIDTH, DEFAULT_DIMENSION_SIZE),
                exif.getAttributeInt(ExifInterface.TAG_IMAGE_LENGTH, DEFAULT_DIMENSION_SIZE)
        );
        Object videoWidth = extraMsgMap.getOrDefault(EXTRA_KEY_OLIVE_VIDEO_WIDTH, DEFAULT_DIMENSION_SIZE);
        Object videoHeight = extraMsgMap.getOrDefault(EXTRA_KEY_OLIVE_VIDEO_HEIGHT, DEFAULT_DIMENSION_SIZE);
        Size videoSize = new Size(
                videoWidth instanceof Integer ? (int) videoWidth : DEFAULT_DIMENSION_SIZE,
                videoHeight instanceof Integer ? (int) videoHeight : DEFAULT_DIMENSION_SIZE
        );

        Context appContext = ContextGetter.context.getApplicationContext();
        if (appContext instanceof GalleryApplication) {
            GalleryApplication application = (GalleryApplication) appContext;
            try (IWatermarkMasterAbility ability = application.getAppAbility(IWatermarkMasterAbility.class)) {
                if (ability != null) {
                    IWatermarkFileOperator operator = ability.newWatermarkFileOperator(pfd, OpenFileMode.MODE_READ);
                    if (operator != null) {
                        try {
                            Pair<Size, Size> pair = extractSizeFromWatermarkOperator(operator, pictureSize, videoSize);
                            pictureSize = pair.getFirst();
                            videoSize = pair.getSecond();
                        } catch (Exception e) {
                            GLog.e(TAG, LogFlag.DL, "[parseVideoExtraMsg] error inside operator", e);
                        } finally {
                            operator.close();
                        }
                    }
                }
            } catch (Exception e) {
                GLog.e(TAG, LogFlag.DL, "[parseVideoExtraMsg] error inside ability", e);
            }
        } else {
            GLog.e(TAG, LogFlag.DL, "[parseVideoExtraMsg] appContext is not valid");
        }

        extraMsgMap.put(EXTRA_KEY_PICTURE_WIDTH_WITHOUT_WATERMARK, pictureSize.getWidth());
        extraMsgMap.put(EXTRA_KEY_PICTURE_HEIGHT_WITHOUT_WATERMARK, pictureSize.getHeight());
        extraMsgMap.put(EXTRA_KEY_OLIVE_VIDEO_WIDTH_WITHOUT_WATERMARK, videoSize.getWidth());
        extraMsgMap.put(EXTRA_KEY_OLIVE_VIDEO_HEIGHT_WITHOUT_WATERMARK, videoSize.getHeight());
    }

    private Pair<Size, Size> extractSizeFromWatermarkOperator(IWatermarkFileOperator operator, Size defaultPictureSize, Size defaultVideoSize) {
        Size pictureSize = defaultPictureSize;
        Size videoSize = defaultVideoSize;
        WatermarkInfo info = operator.readWatermarkInfo();
        AiWatermarkFileExtendInfo extInfo = operator.getAiMasterWatermarkExtInfo();
        boolean hasWatermark = info.hasWatermark() && (extInfo != null);
        if (hasWatermark && (extInfo.isOnlyFrameWatermark() || extInfo.isFrameAndOverlayWatermark())) {
            // 有画框水印
            RectF imageRect = extInfo.getImageDisplayRect();
            pictureSize = new Size((int) imageRect.width(), (int) imageRect.height());

            RectF videoRect = extInfo.getVideoDisplayRect();
            if (videoRect != null) {
                videoSize = new Size((int) videoRect.width(), (int) videoRect.height());
            }
        }

        return new Pair<>(pictureSize, videoSize);
    }

    /**
     * 将 MetadataKey 的类型转成 extraMsg 的key，
     * 会将 map 中的东西装载到 targetMap 中。
     */
    private void convertBatchMetaMapToExtraMsgMap(Map<Integer, Object> map, Map<String, Object> targetMap) {
        for (Map.Entry<Integer, Object> entry : map.entrySet()) {
            Integer key = entry.getKey();
            switch (key) {
                case MetadataKey.COLOR_STANDARD:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_COLOR_STANDARD, entry.getValue());
                    break;
                case MetadataKey.COLOR_TRANSFER:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_COLOR_TRANSFER, entry.getValue());
                    break;
                case MetadataKey.PROFILE:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_PROFILE, entry.getValue());
                    break;
                case MetadataKey.CODEC_TYPE:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_CODEC_TYPE, entry.getValue());
                    break;
                case MetadataKey.FPS:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_FPS, entry.getValue());
                    break;
                case MetadataKey.BITRATE:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_BIT_RATE, entry.getValue());
                    break;
                case MetadataKey.WIDTH:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_WIDTH, entry.getValue());
                    break;
                case MetadataKey.HEIGHT:
                    targetMap.put(EXTRA_KEY_OLIVE_VIDEO_HEIGHT, entry.getValue());
                    break;
                default:
                    GLog.d(TAG, LogFlag.DL, "convertBatchMetaMapToExtraMsgMap, unknown type, " + key);
            }
        }
    }

    private long resetTagFlagsIfInvalid(long tagFlags) {
        if (tagFlags == INVALID) {
            return TAG_FLAG_NONE;
        } else {
            return tagFlags;
        }
    }

    /**
     * Marked by dingyong,修复专业模式相机拍照进入相册,无法删除图片问题。
     * 1、相机专业模式（打开heif，选raw模式）拍照，相机没有写入is_pedding ,文件还没写完， 已经插入到媒体库库了
     * 2、针对没有is_pedding 的文件相册会进行exif解析和校验，校验时使用rw方式通过媒体库打开fd
     * 3、媒体库使用rw的方式访问这种文件，文件系统接口长时间卡主(原生bug)，导致相册的删除线程卡住
     * 4、相册的删除有同步锁，会导致相册所有的删除卡主
     * 恢复方式：杀掉相册后，删除其他文件正常，删除这个异常文件又会卡主，导致bug复现
     * 5、临时方案为：创建时间大于1小时的，设为rw，小于一小时的给r，防止出现文件还没写完就去更新exi，需要紧急出版本，所以先临时规避该问题，
     * 后续再想办法解决该问题 @xiewujie
     *
     * @param entry LocalMediaDataManager.Entry
     * @param file  File
     * @return @return "rw" or "r"
     */
    private String getOpenFileDescriptorMode(LocalMediaDataManager.Entry entry, File file) {
        long mediaTime = entry.mDateTaken;
        if (mediaTime <= 0) {
            if (entry.mDateModified > 0) {
                mediaTime = entry.mDateModified * TimeUtils.TIME_1_SEC_IN_MS;
            } else {
                long lastModified = file.lastModified();
                if (lastModified > 0) {
                    mediaTime = lastModified;
                } else {
                    return OPEN_FD_MODE_R;
                }
            }
        }
        if ((System.currentTimeMillis() - mediaTime) > TimeUtils.TIME_1_HOUR_IN_MS) {
            return OPEN_FD_MODE_RW;
        } else {
            return OPEN_FD_MODE_R;
        }
    }

    /**
     * bugfix：5198460 瘦身的图片会将以前3d版本的超级文本tagFlags（1<<16， 1<<17)写到exif，
     * 为了区分图片是否是真的景深照片或者哈苏水印照片，需要检查文件的特定标记，如果文件tagFlags写错了，
     * 需要将exif纠正，并返回正确的tagFlags
     *
     * @param tagFlags       文件当前tagFlags
     * @param exifInterface  文件对应的exif读写接口
     * @param file           文件的File对象
     * @param fileDescriptor 文件对应的fd
     * @return 文件编辑后的tagFlags
     */
    private long modifyTagFlagsAndWriteExifForSlimmingFileBug(long tagFlags, ExifInterface exifInterface, File
            file, ParcelFileDescriptor fileDescriptor, String openFileDescriptorMode) {
        if (!openFileDescriptorMode.equals(OPEN_FD_MODE_RW)) {
            return tagFlags;
        }
        if (tagFlags == INVALID) {
            return tagFlags;
        }
        long targetTagFlags = tagFlags;
        if ((null == file) || (TextUtils.isEmpty(file.getPath()))) {
            GLog.w(TAG, "modifyTagFlagsAndWriteExifForSlimmingFileBug file or file.getPath is null");
            return targetTagFlags;
        }
        if (((tagFlags & FLAG_PORTRAIT_BLUR) == FLAG_PORTRAIT_BLUR) && !isPortraitBlurPhoto(fileDescriptor, file.getPath())) {
            targetTagFlags = targetTagFlags ^ FLAG_PORTRAIT_BLUR;
        }
        if (((tagFlags & FLAG_WATERMARK) == FLAG_WATERMARK) && !isWatermarkPhoto(fileDescriptor, file.getPath())) {
            targetTagFlags = targetTagFlags ^ FLAG_WATERMARK;
        }
        if (tagFlags != targetTagFlags) {
            long lastModified = file.lastModified();
            writeExifTagFlags(exifInterface, tagFlags, targetTagFlags);
            // 文件编辑后，lastModified会变为当前时间，需要反写修改前的时间，保证lastModified不会变更
            file.setLastModified(lastModified);
        }
        return targetTagFlags;
    }

    private boolean isPortraitBlurPhoto(ParcelFileDescriptor fileDescriptor, String filepath) {
        if (TextUtils.isEmpty(filepath)) {
            GLog.w(TAG, "isPortraitBlurPhoto filepath is empty or null ");
            return false;
        }
        FileExtendedContainer fileExtendedContainer = new FileExtendedContainer();
        Boolean isExtendedData = false;
        boolean isPortraitBlurPhoto = false;
        try {
            isExtendedData = runNativeGuarding(
                    "isPortraitBlurPhoto",
                    generateKey(filepath, 0L),
                    () -> fileExtendedContainer.setDataSource(fileDescriptor, OpenFileMode.MODE_READ, false)
            ).onNativeFailure(() -> {
                GLog.w(TAG, "isPortraitBlurPhoto crash file =" + PathMask.INSTANCE.mask(filepath));
            }).getOrNull();
            if ((isExtendedData != null) && isExtendedData) {
                isPortraitBlurPhoto = isPortraitBlurPhoto(fileExtendedContainer);
            }
        } finally {
            IOUtils.closeQuietly(fileExtendedContainer);
        }
        return isPortraitBlurPhoto;
    }

    /**
     * 去检索Olive视频的extTagFlags
     * 为什么不用tagFlag中的EXIF_TAG_HLG_HDR呢？因为这个tagFlag是对视频有用，对于图片由于该值为7，最终在图片上表现为包含4（全景图），所以会在时间轴上显示成全景图
     *
     * @return 要在扫描时确认添加的extTagFlags, 包含是否为HDR视频、是否为Dolby视频
     */
    private List<Long> retrieveOliveVideoExtTagFlags(Map<Integer, Object> map) {
        ArrayList<Long> oliveVideoExtTagFlags = new ArrayList<>();
        Object colorTransferObj = map.get(MetadataKey.COLOR_TRANSFER);
        if (colorTransferObj == null) {
            return oliveVideoExtTagFlags;
        }
        int colorTransfer = (int) colorTransferObj;
        String codecType = null;
        if ((MediaFormat.COLOR_TRANSFER_HLG == colorTransfer)
                || MediaFormat.COLOR_TRANSFER_ST2084 == colorTransfer) {
            oliveVideoExtTagFlags.add(EXT_OLIVE_HDR_VIDEO);

            Object codecTypeObject = map.get(MetadataKey.CODEC_TYPE);
            if (codecTypeObject == null) {
                return oliveVideoExtTagFlags;
            }
            codecType = (String) codecTypeObject;
            if (VideoTypeUtils.isDolbyVideo(codecType)) { // Olive资源是否为Dolby的视频
                oliveVideoExtTagFlags.add(EXT_OLIVE_DOLBY_VIDEO);
            }
        }
        GLog.d(TAG, LogFlag.DF, "[retrieverOliveVideoExtTagFlags] retriever HDR, colorTransfer=" + colorTransfer + ", codecType=" + codecType);
        return oliveVideoExtTagFlags;
    }

    /**
     * 检验olive文件是否为无效文件：
     * 注意：这里只识别原图，当图片不是原图时我们都认为他是有效的
     * <p>
     * 异常文件判断标准：
     * 1.封面时间小于0 -- 需要在视频的时长内正整数才是正产改的
     * 2.videoOffset 小于等于0 -- 如果为0或小于0就无法读取视频资源/没有封面图
     * 3.videoLength 小于等于0 -- 如果为0或小于0就无法读取视频资源/没有视频
     * 4.offset + length 大于整个文件大小
     *
     * @return Triple Boolean:是否无效，Long:videoOffset, Long:videoLength
     */
    private Triple<Boolean, Long, Long> checkInvalidOliveFile(ParcelFileDescriptor descriptor, String filePath, LocalMediaDataManager.Entry entry) {
        try {
            // 1.如果是瘦身图，不做识别直接认为是有效的
            if (isCloudCompressedFile(filePath, entry.mMediaId)) {
                return new Triple<>(false, INVALID_TIME, INVALID_TIME);
            }

            OLivePhoto olivePhoto = OLiveDecode.create(filePath).decode();

            if ((olivePhoto == null) || (olivePhoto.getMicroVideo() == null)) {
                throw new NullPointerException("olive file decoding failed !!! olivePhoto = " + olivePhoto);
            }

            long coverTime = olivePhoto.getCoverTimeInUs();
            long videoOffset = olivePhoto.getMicroVideo().getOffset();
            long videoLength = olivePhoto.getMicroVideo().getLength();
            if ((coverTime < 0) || (videoOffset <= 0) || (videoLength <= 0) || ((videoOffset + videoLength) > descriptor.getStatSize())) {
                GLog.d(TAG, "[isInvalidOLiveFile]"
                        + " coverTime = " + coverTime
                        + " videoOffset = " + videoOffset
                        + " videoLength = " + videoLength
                        + " fileSize = " + descriptor.getStatSize()
                        + " filePath = " + PathMask.INSTANCE.mask(filePath));
                return new Triple<>(true, INVALID_TIME, INVALID_TIME);
            }

            return new Triple<>(false, videoOffset, videoLength);

        } catch (Exception e) {
            GLog.e(TAG, "[checkInvalidOLiveFile] " + e.getMessage());
            return new Triple<>(true, INVALID_TIME, INVALID_TIME);
        }
    }

    /**
     * 用于判断当前图片是否为瘦身图：true为瘦身图，反之则不是
     * 判断依据：USER_COMMENT 是否包含云服务相关标记
     *
     * 方法出处详见 [CloudFileUtil#getFileGlobalId]
     */
    public static boolean isCloudCompressedFile(String filePath, int mediaId) {
        if (TextUtils.isEmpty(filePath) || !(new File(filePath).exists())) {
            return false;
        }
        String globalId = null;
        InputStream is = null;
        try {
            /*
            USER_COMMENT的格式为Oplus_32/0{"thumb_globalid":"5910e9cadf594432bd33c78c0c06cd47"}
            由于带有/0，使用原生接口exifInterface.getAttribute(ExifInterface.TAG_USER_COMMENT)，只能读取到Oplus_32
             */
            OpenFileRequest request = new OpenFileRequest.Builder()
                    .setFile(filePath)
                    .setMediaId((mediaId > 0) ? String.valueOf(mediaId) : null)
                    .setImage(true)
                    .builder();
            is = FileAccessManager.getInstance().getInputStream(ContextGetter.context, request);
            OplusExifInterface oplusExif = new OplusExifInterface();
            oplusExif.readExif(is);
            OplusExifTag exifTag = oplusExif.getTag(OplusExifInterface.TAG_USER_COMMENT);
            if (exifTag != null) {
                String exifTagValue = exifTag.getValueAsString();
                if (!TextUtils.isEmpty(exifTagValue) && exifTagValue.contains(THUMB_GLOBAL_ID_KEY)) {
                    /*
                     * get the exif tag UserComment json content after "\0" eg: "op**_3
                     * {"thumb_globalid":"f7ae4906ddcf446db9329453dcef5ebc"}"
                     */
                    String json = exifTagValue.substring(exifTagValue.indexOf(END_DELIMITER) + 1);
                    JSONObject jsonObject = new JSONObject(json);
                    globalId = jsonObject.get(THUMB_GLOBAL_ID_KEY).toString();
                }
            }
        } catch (FileNotFoundException f) {
            GLog.e(TAG, "isCloudCompressedFile, not found. e=" + f);
        } catch (IOException | JSONException e) {
            GLog.e(TAG, "isCloudCompressedFile, get gid failed. e=" + e);
        } catch (Exception t) {
            // 其它异常情况：非法文件路径抛空指针异常
            GLog.e(TAG, "isCloudCompressedFile, err cannot set exif", t);
        } finally {
            IOUtils.closeQuietly(is);
        }
        return globalId != null;
    }

    private boolean isWatermarkPhoto(ParcelFileDescriptor fileDescriptor, String filepath) {
        if (TextUtils.isEmpty(filepath)) {
            GLog.w(TAG, "isWaterMarkPhoto filepath is null");
            return false;
        }
        FileExtendedContainer fileExtendedContainer = new FileExtendedContainer();
        Boolean isExtendedData = false;
        boolean isWatermarkPhoto = false;
        try {
            isExtendedData = runNativeGuarding(
                    "isWaterMarkPhoto",
                    generateKey(filepath, 0L),
                    () -> fileExtendedContainer.setDataSource(fileDescriptor, OpenFileMode.MODE_READ, false)
            ).onNativeFailure(() -> {
                        GLog.w(TAG, "isWaterMarkPhoto crash file " + PathMask.INSTANCE.mask(filepath));
                    }
            ).getOrNull();
            if ((isExtendedData != null) && isExtendedData) {
                isWatermarkPhoto = isWatermarkPhoto(fileExtendedContainer);
            }
        } finally {
            IOUtils.closeQuietly(fileExtendedContainer);
        }
        return isWatermarkPhoto;
    }

    public void parseBitFormat(ContentResolver contentResolver, MediaExpandEntry
            expandEntry, LocalMediaDataManager.Entry entry) {
        String mimeType = entry.mMimeType;
        int format = GalleryStore.GalleryColumns.LocalColumns.BIT_FORMAT_BITMAP;
        if (MimeTypeUtils.isHeifOrHeic(mimeType)) {
            String mediaId = String.valueOf(entry.mMediaId);
            Uri uri = Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, mediaId);
            try (ParcelFileDescriptor descriptor = contentResolver.openFileDescriptor(uri, "r")) {
                if (descriptor != null) {
                    format = YuvUtils.getYuvFormat(descriptor.getFileDescriptor());
                }
            } catch (Exception e) {
                GLog.e(TAG, "parseBitFormat = ", e);
            }
        }
        expandEntry.setBitFormat(format);
    }
}