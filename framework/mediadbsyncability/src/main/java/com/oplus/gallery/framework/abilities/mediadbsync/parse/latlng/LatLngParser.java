package com.oplus.gallery.framework.abilities.mediadbsync.parse.latlng;

import android.media.ExifInterface;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.framework.abilities.mediadbsync.parse.LocalMediaDataManager;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.io.FileDescriptor;
import java.util.ArrayList;
import java.util.List;

public abstract class LatLngParser {
    private static final String TAG = "LatLngParser";

    private static final int MEDIA_TYPE_IMAGE = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE;
    private static final int MEDIA_TYPE_VIDEO = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO;

    public static float[] createDefaultLatLng(){
        return new float[]{Float.NaN, Float.NaN};
    }

    public float[] parseLocal(LocalMediaDataManager.Entry entry) {
        if (entry != null) {
            return parse(entry.mMediaId, entry.mPath, entry.mType);
        }
        return createDefaultLatLng();
    }

    public abstract float[] parse(int id, String path, int type);

    private float[] parseImageByMediaId(int mediaId) {
        float[] latLng = createDefaultLatLng();
        Uri uri = Uri.withAppendedPath(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, String.valueOf(mediaId));
        uri = MediaStore.setRequireOriginal(uri);
        ParcelFileDescriptor descriptor = null;
        try {
            descriptor = FileAccessManager.getInstance().openFile(ContextGetter.context, uri);
            if (descriptor != null) {
                FileDescriptor fileDescriptor = descriptor.getFileDescriptor();
                if (fileDescriptor != null) {
                    ExifInterface exifInterface = new ExifInterface(fileDescriptor);
                    boolean result = exifInterface.getLatLong(latLng);
                    if (result) {
                        GLog.d(TAG, "parseImageByMediaId, has latLng.");
                        return latLng;
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "parseImageByMediaId, e: " + e);
        } finally {
            IOUtils.closeQuietly(descriptor);
        }
        return latLng;
    }

    private float[] parseVideoByMediaId(int mediaId) {
        Uri uri = Uri.withAppendedPath(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, String.valueOf(mediaId));
        uri = MediaStore.setRequireOriginal(uri);
        ParcelFileDescriptor descriptor = null;
        try (MediaMetadataRetriever metadataRetriever = new MediaMetadataRetriever()) {
            uri = MediaStore.setRequireOriginal(uri);
            descriptor = FileAccessManager.getInstance().openFile(ContextGetter.context, uri);
            if (descriptor != null) {
                FileDescriptor fileDescriptor = descriptor.getFileDescriptor();
                if (fileDescriptor != null) {
                    metadataRetriever.setDataSource(fileDescriptor);
                    String locationStr = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_LOCATION);
                    return parseVideoLocationStr(locationStr);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "parseVideoByMediaId, e: " + e);
        } finally {
            IOUtils.closeQuietly(descriptor);
        }
        return createDefaultLatLng();
    }

    float[] parseByMediaId(int mediaId, int type) {
        if (type == MEDIA_TYPE_IMAGE) {
            return parseImageByMediaId(mediaId);
        } else if (type == MEDIA_TYPE_VIDEO) {
            return parseVideoByMediaId(mediaId);
        } else {
            GLog.d(TAG, "parseByMediaId, invalid type: " + type);
        }
        return createDefaultLatLng();
    }

    float[] parseByFilePath(String path, int type) {
        float[] latLng = createDefaultLatLng();
        if (type == MEDIA_TYPE_IMAGE) {
            try {
                ExifInterface exifInterface = new ExifInterface(path);
                boolean result = exifInterface.getLatLong(latLng);
                if (result) {
                    if (LocalMediaDataManager.DEBUG) {
                        GLog.d(TAG, "parseByFilePath, image has latLng.");
                    }
                    return latLng;
                }
            } catch (Exception e) {
                GLog.e(TAG, "parseByFilePath, image e: " + e);
            }
        } else if (type == MEDIA_TYPE_VIDEO) {
            try (MediaMetadataRetriever metadataRetriever = new MediaMetadataRetriever()) {
                metadataRetriever.setDataSource(path);
                String locationStr = metadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_LOCATION);
                return parseVideoLocationStr(locationStr);
            } catch (Exception e) {
                GLog.e(TAG, "parseByFilePath, video e: " + e);
            }
        } else {
            GLog.d(TAG, "parseByFilePath, invalid type: " + type);
        }
        return latLng;
    }

    public static float[] parseVideoLocationStr(String locationStr) {
        float[] location = createDefaultLatLng();
        // string like [xy][+22.5180+113.9430/] or [xyz][+64.1501-021.9484+017.257/]
        if (!TextUtils.isEmpty(locationStr)) {
            String[] splits = getLocationStr(locationStr);
            if (splits == null) {
                return location;
            }
            String latitude = null;
            String longitude = null;
            if (splits.length >= 2) {
                int index = 0;
                if (TextUtils.isEmpty(splits[0])) {
                    index++;
                }
                latitude = splits[index++];
                longitude = splits[index];
            }
            if (TextUtils.isEmpty(latitude) || TextUtils.isEmpty(longitude)) {
                GLog.w(TAG, "parseVideoLocationStr, video has no location.");
                return location;
            }

            int slashIndex = longitude.lastIndexOf('/');
            if (slashIndex == longitude.length() - 1) {
                longitude = longitude.substring(0, slashIndex);
            }
            location[0] = Float.parseFloat(latitude);
            location[1] = Float.parseFloat(longitude);
        }
        return location;
    }

    private static String[] getLocationStr(String locationStr) {
        if (TextUtils.isEmpty(locationStr)) {
            return null;
        }

        List<Integer> indexList = new ArrayList();
        char[] locations = locationStr.toCharArray();
        for (int i = 0; i < locations.length; i++) {
            if ((locations[i] == '+') || (locations[i] == '-')) {
                indexList.add(i);
            }
        }

        String[] locationsLatLngs = new String[indexList.size()];
        for (int i = 0; i < indexList.size(); i++) {
            if (i == (indexList.size() - 1)) {
                locationsLatLngs[i] = locationStr.substring(indexList.get(i));
                break;
            }
            locationsLatLngs[i] = locationStr.substring(indexList.get(i), indexList.get(i + 1));
        }
        return locationsLatLngs;
    }
}
