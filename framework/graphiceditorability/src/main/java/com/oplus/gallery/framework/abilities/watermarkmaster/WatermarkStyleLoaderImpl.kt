/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WatermarkStyleLoaderImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/8/19
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2024/8/19      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.watermarkmaster

import android.content.Context
import android.text.TextUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_AI_WATERMARK_MASTER_STYLES
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.watermark.AssetStyleFileSource
import com.oplus.gallery.framework.abilities.watermark.FileStyleSource
import com.oplus.gallery.framework.abilities.watermark.IWatermarkStyleLoader
import com.oplus.gallery.framework.abilities.watermark.IWatermarkStyleOperator
import com.oplus.gallery.framework.abilities.watermark.WatermarkStyleSource
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle
import com.oplus.gallery.framework.abilities.watermarkmaster.dbhelper.RestrictWatermarkMasterResourceDataHelper
import com.oplus.gallery.standard_lib.file.File
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 水印样式加载器实现类
 */
internal class WatermarkStyleLoaderImpl(private val context: Context) : IWatermarkStyleLoader {

    private val debugStylesPath = context.filesDir.absolutePath + File.separator + DEBUG_STYLES_DIR + File.separator

    private fun getStyleSources(styles: List<String>): MutableList<WatermarkStyleSource> {
        val list = mutableListOf<WatermarkStyleSource>()
        return if (DEBUG_AI_WATERMARK_MASTER_STYLES) {
            styles.forEach {
                list.add(FileStyleSource(debugStylesPath + it + JSON_FILE_DOT_SUFFIX))
            }
            list
        } else {
            styles.forEach {
                list.add(AssetStyleFileSource(context, it))
            }
            list
        }
    }

    private fun getBuiltInHasselStyleSources(): MutableList<WatermarkStyleSource> {
        val list = mutableListOf<WatermarkStyleSource>()
        return if (DEBUG_AI_WATERMARK_MASTER_STYLES) {
            WatermarkMasterStyle.getBuiltInHasselStyles().forEach {
                list.add(FileStyleSource(debugStylesPath + it.first + JSON_FILE_DOT_SUFFIX))
            }
            list
        } else {
            WatermarkMasterStyle.getBuiltInHasselStyles().forEach {
                list.add(AssetStyleFileSource(context, it.second))
            }
            list
        }
    }

    private fun getBuiltInPersonalizeStyleSources(
        isOppo: Boolean,
        isRealme: Boolean,
        isOnePlus: Boolean,
        isExport: Boolean,
        isSupportLumo: Boolean
    ): MutableList<WatermarkStyleSource> {
        val list = mutableListOf<WatermarkStyleSource>()
        return if (DEBUG_AI_WATERMARK_MASTER_STYLES) {
            WatermarkMasterStyle.getBuiltInPersonalStyles(isOppo, isRealme, isOnePlus, isExport, isSupportLumo).forEach {
                list.add(FileStyleSource(debugStylesPath + it.first + JSON_FILE_DOT_SUFFIX))
            }
            list
        } else {
            WatermarkMasterStyle.getBuiltInPersonalStyles(isOppo, isRealme, isOnePlus, isExport, isSupportLumo).forEach {
                list.add(AssetStyleFileSource(context, it.second))
            }
            list
        }
    }

    private fun getBuiltInTextStyleSources(): MutableList<WatermarkStyleSource> {
        val list = mutableListOf<WatermarkStyleSource>()
        return if (DEBUG_AI_WATERMARK_MASTER_STYLES) {
            WatermarkMasterStyle.getBuiltInTextStyles().forEach {
                list.add(FileStyleSource(debugStylesPath + it.first + JSON_FILE_DOT_SUFFIX))
            }
            list
        } else {
            WatermarkMasterStyle.getBuiltInTextStyles().forEach {
                list.add(AssetStyleFileSource(context, it.second))
            }
            list
        }
    }

    private fun getBuiltInRestrictStyleSources(): MutableList<WatermarkStyleSource> {
        val list = mutableListOf<WatermarkStyleSource>()
        return if (DEBUG_AI_WATERMARK_MASTER_STYLES) {
            WatermarkMasterStyle.getBuiltInRestrictStyles().forEach {
                list.add(FileStyleSource(debugStylesPath + it.first + JSON_FILE_DOT_SUFFIX))
            }
            list
        } else {
            WatermarkMasterStyle.getBuiltInRestrictStyles().forEach {
                list.add(AssetStyleFileSource(context, it.second))
            }
            list
        }
    }

    private fun isLoaded(styleSource: WatermarkStyleSource, isFrameCameraEdit: Boolean): Boolean {
        return WatermarkStyleCache.get(WatermarkStyleCache.CacheKey(styleSource, isFrameCameraEdit)) != null
    }

    override fun getStyleOperatorCache(
        styleSource: WatermarkStyleSource,
        isFrameCameraEdit: Boolean
    ): Pair<WatermarkMasterStyle, IWatermarkStyleOperator>? {
        return WatermarkStyleCache.get(WatermarkStyleCache.CacheKey(styleSource, isFrameCameraEdit))
    }

    override fun getStyleFromCache(styleSource: WatermarkStyleSource, isFrameCameraEdit: Boolean): WatermarkMasterStyle? {
        return WatermarkStyleCache.get(WatermarkStyleCache.CacheKey(styleSource, isFrameCameraEdit))?.first
    }

    override fun getOperatorFromCache(styleSource: WatermarkStyleSource, isFrameCameraEdit: Boolean): IWatermarkStyleOperator? {
        return WatermarkStyleCache.get(WatermarkStyleCache.CacheKey(styleSource, isFrameCameraEdit))?.second
    }

    override fun loadStyleSync(
        styleSource: WatermarkStyleSource,
        isFrameCameraEdit: Boolean,
        ignoreCache: Boolean
    ): Pair<WatermarkMasterStyle, IWatermarkStyleOperator>? {
        val startTime = System.currentTimeMillis()
        val targetStyleSource = covertToDebugStyleSource(styleSource)
        if (isLoaded(targetStyleSource, isFrameCameraEdit).not() || ignoreCache) {
            val operator = WatermarkStyleOperatorImpl(targetStyleSource, isFrameCameraEdit)
            val style = operator.parseStyle()
            style?.let {
                WatermarkStyleCache.put(
                    WatermarkStyleCache.CacheKey(targetStyleSource, isFrameCameraEdit),
                    Pair<WatermarkMasterStyle, IWatermarkStyleOperator>(it, operator)
                )
                deepCopyStyleAndCache(targetStyleSource, isFrameCameraEdit, it)
                GLog.d(TAG, LogFlag.DL, "[loadStyleSync] costTime:${System.currentTimeMillis() - startTime}")
                return Pair(it, operator)
            }
        } else {
            return WatermarkStyleCache.get(WatermarkStyleCache.CacheKey(targetStyleSource, isFrameCameraEdit))
        }
        return null
    }

    /**
     * cache不复用，但是json解析可以复用，避免重复解析
     */
    private fun deepCopyStyleAndCache(targetStyleSource: WatermarkStyleSource, isFrameCameraEdit: Boolean, style: WatermarkMasterStyle) {
        if (isLoaded(targetStyleSource, isFrameCameraEdit.not()).not()) {
            val operatorVers = WatermarkStyleOperatorImpl(targetStyleSource, isFrameCameraEdit.not())
            val styleVers = style.deepCopy()
            operatorVers.updateWatermarkStyle(styleVers, isFrameCameraEdit.not())
            WatermarkStyleCache.put(
                WatermarkStyleCache.CacheKey(targetStyleSource, isFrameCameraEdit.not()),
                Pair(styleVers, operatorVers)
            )
        }
    }

    override fun loadStyleAsync(
        scope: CoroutineScope,
        styleSource: WatermarkStyleSource,
        isFrameCameraEdit: Boolean,
        callback: (style: WatermarkMasterStyle?, operator: IWatermarkStyleOperator?) -> Unit,
        ignoreCache: Boolean
    ) {
        scope.launch(Dispatchers.IO) {
            val startTime = System.currentTimeMillis()
            val targetStyleSource = covertToDebugStyleSource(styleSource)
            if (isLoaded(targetStyleSource, isFrameCameraEdit).not() || ignoreCache) {
                val operator = WatermarkStyleOperatorImpl(targetStyleSource, isFrameCameraEdit)
                val style = operator.parseStyle()
                style?.let {
                    WatermarkStyleCache.put(
                        WatermarkStyleCache.CacheKey(targetStyleSource, isFrameCameraEdit),
                        Pair<WatermarkMasterStyle, IWatermarkStyleOperator>(it, operator)
                    )
                    deepCopyStyleAndCache(targetStyleSource, isFrameCameraEdit, it)
                }
                GLog.d(TAG, LogFlag.DL, "[loadStyleAsync] costTime:${System.currentTimeMillis() - startTime}")
                callback.invoke(style, operator)
            } else {
                val cacheMap = WatermarkStyleCache.get(WatermarkStyleCache.CacheKey(targetStyleSource, isFrameCameraEdit))
                callback.invoke(cacheMap?.first, cacheMap?.second)
            }
        }
    }

    private fun covertToDebugStyleSource(styleSource: WatermarkStyleSource): WatermarkStyleSource {
        var finalStyleSource: WatermarkStyleSource = styleSource
        if ((DEBUG_AI_WATERMARK_MASTER_STYLES) && (styleSource is AssetStyleFileSource)) {
            finalStyleSource = FileStyleSource(debugStylesPath + styleSource.assetName.split(TextUtil.LEFT_SLASH)[1])
        }
        return finalStyleSource
    }

    override fun loadBuiltInAllStyles(
        scope: CoroutineScope,
        isFrameCameraEdit: Boolean,
        isOppo: Boolean,
        isRealme: Boolean,
        isOnePlus: Boolean,
        isExport: Boolean,
        isNeedRestrictStyle: Boolean,
        isSupportLumo: Boolean,
        callback: (styleOperatorMap: MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator>) -> Unit
    ) {
        loadStyleList(
            scope,
            getBuiltInHasselStyleSources() + getBuiltInTextStyleSources()
                    + getBuiltInPersonalizeStyleSources(isOppo, isRealme, isOnePlus, isExport, isSupportLumo)
                    + (if (isNeedRestrictStyle) getBuiltInRestrictStyleSources() else emptyList()),
            isFrameCameraEdit, callback
        )
    }

    override fun loadSpecifyStyles(
        scope: CoroutineScope,
        styles: List<String>,
        isFrameCameraEdit: Boolean,
        callback: (styleOperatorMap: MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator>) -> Unit
    ) {
        loadStyleList(scope, getStyleSources(styles), isFrameCameraEdit, callback)
    }

    override fun loadBuiltInHasselStyles(
        scope: CoroutineScope,
        isFrameCameraEdit: Boolean,
        callback: (styleOperatorMap: MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator>) -> Unit
    ) {
        loadStyleList(scope, getBuiltInHasselStyleSources(), isFrameCameraEdit, callback)
    }

    override fun loadBuildInTextStyles(
        scope: CoroutineScope,
        isFrameCameraEdit: Boolean,
        callback: (styleOperatorMap: MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator>) -> Unit
    ) {
        loadStyleList(scope, getBuiltInTextStyleSources(), isFrameCameraEdit, callback)
    }

    override fun loadRestrictStyleSync(isFrameCameraEdit: Boolean): MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator> {
        val resEntityList = RestrictWatermarkMasterResourceDataHelper.queryAllStyleJsonEntity()
        val styleSourceList = mutableListOf<FileStyleSource>()
        resEntityList.forEach { entity ->
            entity.resFilepath.takeUnless { TextUtils.isEmpty(it) }?.also { path ->
                styleSourceList.add(FileStyleSource(path))
            }
        }
        return loadStyleListSync(styleSourceList, isFrameCameraEdit)
    }

    override fun loadRestrictStyleAsync(
        scope: CoroutineScope,
        isFrameCameraEdit: Boolean,
        callback: (styleOperatorMap: MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator>) -> Unit
    ) {
        val resEntityList = RestrictWatermarkMasterResourceDataHelper.queryAllStyleJsonEntity()
        val styleSourceList = mutableListOf<FileStyleSource>()
        resEntityList.forEach { entity ->
            entity.resFilepath.takeUnless { TextUtils.isEmpty(it) }?.also { path ->
                styleSourceList.add(FileStyleSource(path))
            }
        }
        loadStyleList(scope, styleSourceList, isFrameCameraEdit, callback)
    }

    private fun loadStyleListSync(
        styleSources: List<WatermarkStyleSource>,
        isFrameCameraEdit: Boolean
    ): MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator> {
        val startTime = System.currentTimeMillis()
        val styleOperators = mutableMapOf<WatermarkMasterStyle, IWatermarkStyleOperator>()
        styleSources.forEach { styleSource ->
            if (isLoaded(styleSource, isFrameCameraEdit).not()) {
                val operator = WatermarkStyleOperatorImpl(styleSource, isFrameCameraEdit)
                val style = operator.parseStyle()
                style?.let {
                    styleOperators[it] = operator
                    WatermarkStyleCache.put(WatermarkStyleCache.CacheKey(styleSource, isFrameCameraEdit), Pair(it, operator))
                    deepCopyStyleAndCache(styleSource, isFrameCameraEdit, it)
                }
            } else {
                GLog.d(TAG, LogFlag.DL, "[loadStyleList] this styleSource:$styleSource already loaded.")
                val key = getStyleFromCache(styleSource, isFrameCameraEdit)
                val value = getOperatorFromCache(styleSource, isFrameCameraEdit)
                if ((key != null) && (value != null)) styleOperators[key] = value
            }
        }
        GLog.d(TAG, LogFlag.DL, "[loadStyleList] costTime:${System.currentTimeMillis() - startTime}, size: ${styleOperators.size}")
        return styleOperators
    }

    private fun loadStyleList(
        scope: CoroutineScope,
        styleSources: List<WatermarkStyleSource>,
        isFrameCameraEdit: Boolean,
        callback: (styleOperatorMap: MutableMap<WatermarkMasterStyle, IWatermarkStyleOperator>) -> Unit
    ) {
        scope.launch(Dispatchers.IO) {
            val startTime = System.currentTimeMillis()
            val styleOperators = mutableMapOf<WatermarkMasterStyle, IWatermarkStyleOperator>()
            styleSources.forEach { styleSource ->
                if (isLoaded(styleSource, isFrameCameraEdit).not()) {
                    val operator = WatermarkStyleOperatorImpl(styleSource, isFrameCameraEdit)
                    val style = operator.parseStyle()
                    style?.let {
                        styleOperators[it] = operator
                        WatermarkStyleCache.put(WatermarkStyleCache.CacheKey(styleSource, isFrameCameraEdit), Pair(it, operator))
                        // cache不复用，但是json解析可以复用，避免重复解析
                        if (isLoaded(styleSource, isFrameCameraEdit.not()).not()) {
                            val operatorVers = WatermarkStyleOperatorImpl(styleSource, isFrameCameraEdit.not())
                            val styleVers = it.deepCopy()
                            operatorVers.updateWatermarkStyle(it.deepCopy(), isFrameCameraEdit.not())
                            WatermarkStyleCache.put(
                                WatermarkStyleCache.CacheKey(styleSource, isFrameCameraEdit.not()),
                                Pair(styleVers, operatorVers)
                            )
                        }
                    }
                } else {
                    GLog.d(TAG, LogFlag.DL, "[loadStyleList] this styleSource:$styleSource already loaded.")
                    val key = getStyleFromCache(styleSource, isFrameCameraEdit)
                    val value = getOperatorFromCache(styleSource, isFrameCameraEdit)
                    if ((key != null) && (value != null)) styleOperators[key] = value
                }
            }
            GLog.d(TAG, LogFlag.DL, "[loadStyleList] costTime:${System.currentTimeMillis() - startTime}, size: ${styleOperators.size}")
            callback.invoke(styleOperators)
        }
    }

    companion object {
        private const val TAG = "WatermarkStyleLoader"

        private const val JSON_FILE_DOT_SUFFIX = ".json"
        private const val DEBUG_STYLES_DIR = "debugStyles"
    }
}