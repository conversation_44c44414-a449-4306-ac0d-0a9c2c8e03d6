/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - XqipResourceRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/04/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     yaoweihe       2024/04/22    1.0                create
 ******************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ColorSpace
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.foundation.effect.BlurHelper
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaXqipResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions.Companion.INVALID_BLUR_RADIUS
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 *
 * 获取Xqip小缩图
 * 获取流程：
 *     1.优先从[XqipCacheManager]缓存获取
 *     2.通过[LocalImageBitmapRequester]以及[LocalVideoBitmapRequester]来实现获取图片
 *       (可能从已有缓存，也可能直接从原图解码)，得到256缩图后开始resize得到小缩图
 *
 */
internal class XqipResourceRequester(
    private val context: Context,
    private val originalResource: LocalMediaXqipResourceKey,
    private val options: ResourceGetOptions,
    private val abilityBus: IAbilityBus,
    private val cachingAbility: ICachingAbility?,
    private val colorManagementAbility: IColorManagementAbility?,
    private val cancelable: ICancelable?
) : IBitmapRequester {
    override fun request(): ImageResult<Bitmap>? {
        if (cancelable?.isCancelled() == true) {
            return null
        }

        // 此处解析缓存操作
        val canReadDiskCache = CacheOperation.ReadDiskCache in options.inCacheOperation
        val needWriteCache = CacheOperation.WriteDiskCache in options.inCacheOperation

        var imageResult: ImageResult<Bitmap>? = null
        kotlin.runCatching {
            val bitmap = getBitmapFromXqipCache(canReadDiskCache.not())?.let { cacheBitmap ->
                adjustColorSpace(cacheBitmap)
                cacheBitmap
            } ?: let {
                // 当缓存中不存在且需要写入缓存，执行以下操作，生成磁盘缓存
                if (needWriteCache && (imageResult == null) && (originalResource.localMediaItem != null)) {
                    getBitmapFromOtherCache(originalResource.localMediaItem)
                } else null
            }

            imageResult = bitmap?.let {
                ImageResult(it)
            }
        }

        return imageResult
    }

    private fun getBitmapFromXqipCache(readFromMemory: Boolean = true): Bitmap? {
        return cachingAbility?.let { cachingAbility ->
            // 优先使用medialItem读取缓存
            if (originalResource.blockItemKey != null) {
                cachingAbility.xqipCache?.getItemBitmap(originalResource.blockItemKey!!, readFromMemory)
            } else {
                cachingAbility.xqipCache?.getItemBitmap(
                    originalResource.uniqueScopeToken,
                    originalResource.index,
                    readFromMemory
                )
            }
        }
    }

    private fun getBitmapFromOtherCache(localMediaItem: LocalMediaItem?): Bitmap? {
        val xqipBlurRadius = options.inBlurRadius
        return (ResourceKeyFactory.createResourceKey(localMediaItem) as? LocalMediaResourceKey)?.let { localMediaResourceKey ->
            getLocalBitmapRequester(
                resourceKey = localMediaResourceKey,
                options = options.also { it.inBlurRadius = INVALID_BLUR_RADIUS },
                cachingAbility = cachingAbility,
                colorManagementAbility = colorManagementAbility,
                cancelable = cancelable
            )?.request()?.result?.let { bitmap256 ->
                options.inBlurRadius = xqipBlurRadius
                val targetSize = ICachingAbility.IXqipCache.blockCacheType.slotWidth
                BitmapUtils.resizeBitmapByScale(bitmap256, targetSize.toFloat() / bitmap256.width, true, false).let {
                    blurBitmapIfNeeded(it, true)
                }
            }
        }
    }

    private fun getLocalBitmapRequester(
        resourceKey: LocalMediaResourceKey,
        options: ResourceGetOptions,
        cachingAbility: ICachingAbility?,
        colorManagementAbility: IColorManagementAbility?,
        cancelable: ICancelable?
    ) = when {
        MimeTypeUtils.isImage(resourceKey.mimeType) || TypeFilterUtils.isImage(resourceKey.path) -> {
            LocalImageBitmapRequester(context, resourceKey, options, abilityBus, cachingAbility, colorManagementAbility, cancelable)
        }

        MimeTypeUtils.isVideo(resourceKey.mimeType) || TypeFilterUtils.isVideo(resourceKey.path) -> {
            LocalVideoBitmapRequester(context, resourceKey, options, abilityBus, cachingAbility, colorManagementAbility, cancelable)
        }

        else -> {
            GLog.e(TAG) {
                "getLocalBitmapRequester: Not support the mimeType ${resourceKey.mimeType} for LocalMediaResourceKey."
            }
            null
        }
    }

    private fun blurBitmapIfNeeded(bitmap: Bitmap, recycle: Boolean): Bitmap {
        val blurRadius = options.inBlurRadius
        if (blurRadius <= 0) {
            return bitmap
        }
        return BlurHelper.getInstance(context).createBlurBitmap(bitmap, blurRadius).apply { if (recycle) bitmap.recycle() }
    }

    private fun adjustColorSpace(bitmap: Bitmap) {
        colorManagementAbility?.let {
            if (it.isWideColorGamutEnabled == false) {
                it.adjustColorSpace(bitmap, ColorSpace.get(ColorSpace.Named.SRGB))
            }
        }
    }

    companion object {
        private const val TAG = "XqipResourceRequesterCopy"
    }
}