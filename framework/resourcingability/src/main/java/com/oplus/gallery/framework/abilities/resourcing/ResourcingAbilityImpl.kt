/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ResourcingAbilityImpl.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.Lifecycle
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.fastcapture.FastCaptureManager
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec.IS_SINGLE_DOLBY_CODEC_DEVICE
import com.oplus.gallery.framework.abilities.resourcing.key.ByteArrayResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.FaceMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaXqipResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.MtpResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.PredecodeResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.key.SharedMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.SharedThumbnailResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.TileResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.YuvDataResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.AvPlayerGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceSaveOptions
import com.oplus.gallery.framework.abilities.resourcing.requester.SdCardStateManager
import com.oplus.gallery.framework.abilities.resourcing.requester.avplayer.AVPlayerRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.avplayer.IAVPlayerRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.avplayer.SharedAlbumAVPlayerRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.CloudMediaBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.FaceItemBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.FastCaptureBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.IBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.LocalImageBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.LocalVideoBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.MtpImageBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.PredecodeBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.SharedMediaBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.SharedThumbnailBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.TileBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.UriImageBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.UriVideoBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.XqipResourceRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.drawable.HdrImageDrawableRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.drawable.IDrawableRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.drawable.YuvBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.drawable.YuvDrawableRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.ByteArrayRegionDecoderRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.IRegionDecoderRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.LocalImageRegionDecoderRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.MtpImageRegionDecoder
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.SharedImageRegionDecoderRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.UriImageRegionDecoderRequest
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.player.AVPlayer

class ResourcingAbilityImpl(private val context: Context) : AbsAppAbility(), IResourcingAbility {

    override val domainInstance: IResourcingAbility = this

    private lateinit var fastCaptureManager: FastCaptureManager
    private var configAbility: IConfigAbility? = null

    // 传递给 AbsGalleryMediaBitmapRequester 在获取缓存缩图时使用，目的： 四个解码线程 并发 获取 缓存能力，会在获取时卡锁
    private var cachingAbility: ICachingAbility? = null
    private var colorManagementAbility: IColorManagementAbility? = null

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        SdCardStateManager.updateSdCardState()
        SdCardStateManager.registerSdCardStateMonitor()
        fastCaptureManager = FastCaptureManager(context, abilityBus)
        configAbility = abilityBus.requireAbility(IConfigAbility::class.java)
        cachingAbility = abilityBus.requireAbility(ICachingAbility::class.java)
        colorManagementAbility = abilityBus.requireAbility(IColorManagementAbility::class.java)
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        SdCardStateManager.unRegisterSdCardStateMonitor()
        fastCaptureManager.release()
        configAbility?.close()
        configAbility = null
        cachingAbility?.close()
        cachingAbility = null
        colorManagementAbility?.close()
        colorManagementAbility = null
    }

    override fun requestBitmap(
        key: ResourceKey,
        options: ResourceGetOptions,
        cancelable: ICancelable?,
        lifecycle: Lifecycle?
    ): ImageResult<Bitmap>? {
        return runCatching {
            val isSingleDolbyCodeDevice =
                configAbility?.getBooleanConfig(IS_SINGLE_DOLBY_CODEC_DEVICE) ?: false
            options.isSingleDolbyCodeDevice = isSingleDolbyCodeDevice
            getBitmapRequester(key, options, cancelable, lifecycle)?.request()
        }.onFailure {
            GLog.e(TAG, it) { "[requestBitmap] failed to requestBitmap, msg=$it" }
        }.getOrNull()
    }

    override fun <T> requestYuvResource(key: ResourceKey, cancelable: ICancelable?, specifiedOutputType: Class<out T>?): ImageResult<T>? {
        return when (key) {
            is YuvDataResourceKey -> YuvDrawableRequester(key).request() as? ImageResult<T>
            is LocalMediaResourceKey -> {
                if (ApiLevelUtil.isAtLeastAndroidU()) {
                    YuvBitmapRequester(context, key, abilityBus, cachingAbility, cancelable).request() as? ImageResult<T>
                } else null
            }

            else -> null
        }
    }


    override fun requestRegionDecoder(
        key: ResourceKey,
        options: ResourceGetOptions,
        cancelable: ICancelable?,
        lifecycle: Lifecycle?
    ): IRegionDecoder? {
        return getRegionDecoderRequester(key, cancelable)?.request()
    }

    override fun <T : Drawable> requestDrawable(
        key: ResourceKey,
        options: ResourceGetOptions,
        cancelable: ICancelable?,
        lifecycle: Lifecycle?,
        specifiedOutputType: Class<out T>?
    ): ImageResult<T>? {
        //Marked by: 10bit封装完毕后，待进一步完善该部分。
        return getDrawableRequester<T>(key, options, specifiedOutputType)?.request() as? ImageResult<T>
    }

    override fun requestAVPlayer(key: ResourceKey, options: AvPlayerGetOptions, cancelable: ICancelable?): AVPlayer? {
        //Marked by: 未来待实现。
        return getAVPlayerRequester(key, options, cancelable)?.request()
    }

    override fun saveThumbnail(key: ResourceKey, bitmap: Bitmap, options: ResourceSaveOptions, cancelable: ICancelable?): Boolean {
        //Marked by: 未来有业务需要时，再实现。
        return false
    }

    override fun saveThumbnail(key: ResourceKey, intent: Intent, options: ResourceSaveOptions, cancelable: ICancelable?): Bitmap? {
        //Marked by: 未来有业务需要时，再实现。
        return when (key) {
            is LocalMediaResourceKey -> {
                // 当前仅为实现功能，未经过设计，后续整个 ResourcingAbility 除了公开接口外，内部实现需要重构
                fastCaptureManager.trySaveThumbnail(key, intent, options)
            }

            else -> {
                GLog.d(TAG) { "[saveThumbnail] key not support, key=$key" }
                null
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun getBitmapRequester(
        resourceKey: ResourceKey,
        options: ResourceGetOptions,
        cancelable: ICancelable?,
        lifecycle: Lifecycle?
    ): IBitmapRequester? {
        when (resourceKey) {
            is PredecodeResourceKey -> return PredecodeBitmapRequester(context, resourceKey, options, abilityBus, cancelable)

            is LocalMediaXqipResourceKey -> {
                return XqipResourceRequester(context, resourceKey, options, abilityBus, cachingAbility, colorManagementAbility, cancelable)
            }

            is LocalMediaResourceKey -> {
                //判断是否为快拍类型，若是则返回快拍的 FastCaptureDetector
                val fastCaptureDetector = createFastCaptureDetectorIfNeeded(resourceKey)
                return when {
                    (fastCaptureDetector != null) -> {
                        // 返回快拍的 FastCaptureBitmapRequester
                        FastCaptureBitmapRequester(
                            context,
                            resourceKey,
                            options,
                            abilityBus,
                            cachingAbility,
                            colorManagementAbility,
                            fastCaptureDetector,
                            cancelable
                        )
                    }

                    (DatabaseUtils.isInMediaStore(resourceKey.mediaId) || resourceKey.isLocalFileExist) -> {
                        //分别根据是Image还是Video类型，返回LocalImage或LocalVideo的Requester
                        getLocalBitmapRequester(resourceKey, options, cancelable)
                    }

                    resourceKey.isCloudFileExist -> {
                        CloudMediaBitmapRequester(
                            context,
                            resourceKey,
                            options,
                            abilityBus,
                            cachingAbility,
                            colorManagementAbility,
                            cancelable,
                            lifecycle
                        )
                    }

                    else -> {
                        GLog.e(TAG, "[getBitmapRequester] Not support not exist local or cloud")
                        null
                    }
                }
            }

            is FaceMediaResourceKey -> {
                if (options.inThumbnailType == ThumbnailSizeUtils.TYPE_FACE_THUMBNAIL) {
                    return FaceItemBitmapRequester(resourceKey, options, abilityBus, cancelable)
                }
                return (ResourceKeyFactory.createResourceKey(resourceKey.item.refItem) as? LocalMediaResourceKey)?.let {
                    getLocalBitmapRequester(it, options, cancelable)
                } ?: let {
                    GLog.e(TAG, "[getBitmapRequester] Not support type of refItem")
                    null
                }
            }

            is MtpResourceKey -> return MtpImageBitmapRequester(resourceKey, options, cancelable)

            is UriMediaResourceKey -> {
                return when {
                    MimeTypeUtils.isImage(resourceKey.mimeType) -> UriImageBitmapRequester(resourceKey, options, cancelable)

                    MimeTypeUtils.isVideo(resourceKey.mimeType) -> UriVideoBitmapRequester(resourceKey, options, cancelable)

                    else -> {
                        GLog.e(TAG) {
                            "[getBitmapRequester] Not support mime type ${resourceKey.mimeType} for UriMediaResourceKey."
                        }
                        null
                    }
                }
            }

            is TileResourceKey -> return TileBitmapRequester(resourceKey, options, abilityBus)
            is SharedMediaResourceKey -> {
                return SharedMediaBitmapRequester(context, resourceKey, options, abilityBus, cachingAbility, colorManagementAbility, cancelable)
            }
            is SharedThumbnailResourceKey -> return SharedThumbnailBitmapRequester(resourceKey, options, abilityBus)
            else -> {
                GLog.e(TAG, "[getBitmapRequester] Not support the resource key when request bitmap.")
                return null
            }
        }
    }

    private fun getLocalBitmapRequester(
        resourceKey: LocalMediaResourceKey,
        options: ResourceGetOptions,
        cancelable: ICancelable?
    ) = when {
        MimeTypeUtils.isImage(resourceKey.mimeType) || TypeFilterUtils.isImage(resourceKey.path) -> {
            LocalImageBitmapRequester(context, resourceKey, options, abilityBus, cachingAbility, colorManagementAbility, cancelable)
        }

        MimeTypeUtils.isVideo(resourceKey.mimeType) || TypeFilterUtils.isVideo(resourceKey.path) -> {
            LocalVideoBitmapRequester(context, resourceKey, options, abilityBus, cachingAbility, colorManagementAbility, cancelable)
        }

        else -> {
            GLog.e(TAG) {
                "getLocalBitmapRequester: Not support the mimeType ${resourceKey.mimeType} for LocalMediaResourceKey."
            }
            null
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun getRegionDecoderRequester(
        resourceKey: ResourceKey,
        cancelable: ICancelable?
    ): IRegionDecoderRequester? {
        return when (resourceKey) {
            is LocalMediaResourceKey -> {
                /*
                文件还没有进入媒体库或者相册还没从媒体库中同步到media id则认为文件是没有准备好的，不支持区域解码
                实体文件不存在，不支持区域解码
                 */
                if (DatabaseUtils.isInMediaStore(resourceKey.mediaId).not()) {
                    GLog.d(TAG, "getRegionDecoderRequester, file not exit or not ready return null")
                    return null
                }
                //判断是否为快拍类型，若是则返回快拍的 FastCaptureDetector
                createFastCaptureDetectorIfNeeded(resourceKey)?.let {
                    GLog.d(TAG, "getRegionDecoderRequester, fastCapture not supported when request RegionDecoder.")
                    return null
                }

                LocalImageRegionDecoderRequester(context, resourceKey, abilityBus)
            }

            is UriMediaResourceKey -> UriImageRegionDecoderRequest(resourceKey, cancelable)
            is MtpResourceKey -> MtpImageRegionDecoder(resourceKey)
            is SharedMediaResourceKey -> SharedImageRegionDecoderRequester(resourceKey)
            is ByteArrayResourceKey -> ByteArrayRegionDecoderRequester(resourceKey)
            else -> {
                GLog.e(TAG, "getRegionDecoderRequester, Not support resource key when request RegionDecoder.")
                null
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun createFastCaptureDetectorIfNeeded(resourceKey: LocalMediaResourceKey): FastCaptureDetector? {
        return fastCaptureManager.createFastCaptureDetectorIfNeeded(resourceKey)
    }

    private fun <T> getDrawableRequester(
        resourceKey: ResourceKey,
        options: ResourceGetOptions,
        specifiedOutputType: Class<out T>?
    ): IDrawableRequester? {
        return when (specifiedOutputType) {
            HdrImageDrawable::class.java -> getHdrImageDrawableRequester(resourceKey, options)
            else -> null
        }
    }

    private fun getHdrImageDrawableRequester(resourceKey: ResourceKey, options: ResourceGetOptions): IDrawableRequester {
        return HdrImageDrawableRequester(context, resourceKey, options, cachingAbility)
    }

    private fun getAVPlayerRequester(
        resourceKey: ResourceKey,
        options: AvPlayerGetOptions,
        cancelable: ICancelable?
    ): IAVPlayerRequester? {
        return when (resourceKey) {
            is LocalMediaResourceKey -> AVPlayerRequester(context, resourceKey, options, cancelable)
            is SharedMediaResourceKey -> SharedAlbumAVPlayerRequester(context, resourceKey, options, cancelable)
            else -> {
                GLog.e(TAG, "resourceKey not supported when request AVPlayer.")
                null
            }
        }
    }

    private companion object {
        private const val TAG = "ResourcingAbilityImpl"
    }
}