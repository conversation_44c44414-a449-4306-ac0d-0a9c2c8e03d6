/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : TileCacheRequest.kt
 ** Description : Tile获取缓存缩图请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON>eng@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import android.graphics.BitmapFactory
import android.graphics.ColorSpace
import com.oplus.gallery.addon.media.HeifDecodeFrameWrapper
import com.oplus.gallery.business_lib.cache.CacheType
import com.oplus.gallery.business_lib.cache.TileMediaCacheKey
import com.oplus.gallery.business_lib.cache.diskcache.DiskCacheManagerService
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.framework.abilities.resourcing.TileParams
import com.oplus.gallery.foundation.cache.memorycache.BytesBufferPool
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.cache.memorycache.BytesBuffer
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.display.ColorModelManager
import com.oplus.gallery.foundation.util.math.ByteArrUtils
import com.oplus.gallery.standard_lib.codec.DecodeUtils.DECODE_CONFIG
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.yuv.decoder.YuvData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import java.nio.ByteBuffer

class TileCacheRequest(
    private val path: Path,
    private val tileParam: TileParams,
    val dateModifiedInSec: Long,
    private val bitmapPool: BitmapPools.IBitmapPool? = null,
    private val isYuv: Boolean = false
) : Job<ImageData> {
    override fun call(jc: JobContext): ImageData? {
        GTrace.traceBegin("PreTile-Disk")
        val buffer = BytesBufferPool.get(0)
        try {
            var found = false
            if (dateModifiedInSec != INVALID_MODIFIED_TIME) {
                var cacheType = CacheType.TILE_BLOB_CACHE
                if (isYuv) {
                    cacheType = CacheType.TILE_YUV_BLOB_CACHE
                }
                val cacheKey = TileMediaCacheKey(path, cacheType, tileParam, dateModifiedInSec)
                found = DiskCacheManagerService.getImageData(cacheKey, buffer)
            }
            if (found) {
                return if (!isYuv) {
                    (bitmapPool?.decode(buffer.data, buffer.offset, buffer.length))?.let {
                        ColorModelManager.adjustBitmapColorSpaceIfNeeded(it)
                        ImageData(it)
                    }
                } else {
                    getYuvImageDataFromBuffer(buffer)
                }
            }
        } finally {
            BytesBufferPool.recycle(buffer)
            GTrace.traceEnd()
        }
        return null
    }

    companion object {
        const val TAG = "TileCacheRequest"

        // TODO JinPeng 统一PreTileDecode里面的同样的常量
        const val INVALID_MODIFIED_TIME = -1L
        private const val YUV_COLOR_SPACE_SRGB = 0
        private const val YUV_COLOR_SPACE_DISPLAYP3 = 1
        private const val YUV_COLOR_SPACE_DCIP3 = 2

        @JvmStatic
        fun getImageDataFromBuffer(bitmapPool: BitmapPools.IBitmapPool, buffer: BytesBuffer): ImageData {
            val imageData = ImageData()
            val options = BitmapFactory.Options()
            options.inPreferredConfig = DECODE_CONFIG
            val bitmap = bitmapPool.decode(buffer.data, buffer.offset,
                    buffer.length, options)
            imageData.bitmap = bitmap
            return imageData
        }

        @JvmStatic
        fun getYuvImageDataFromBuffer(buffer: BytesBuffer): ImageData? {
            try {
                // 10bit 渲染的时候需要宽和高以及色域，所以，后12位分别存宽和高以及色域
                val yuvData = ByteArrUtils.subByteArray(
                    buffer.data,
                    buffer.offset,
                    buffer.data.size - buffer.offset - ByteArrUtils.INT_TO_BYTE_ARRAY_SIZE_TWELVE
                )
                val widthArr = ByteArrUtils.subByteArray(
                    buffer.data,
                    buffer.data.size - ByteArrUtils.INT_TO_BYTE_ARRAY_SIZE_TWELVE,
                    ByteArrUtils.INT_TO_BYTE_ARRAY_SIZE_FOUR
                )
                val heightArr = ByteArrUtils.subByteArray(
                    buffer.data,
                    buffer.data.size - ByteArrUtils.INT_TO_BYTE_ARRAY_SIZE_EIGHT,
                    ByteArrUtils.INT_TO_BYTE_ARRAY_SIZE_FOUR
                )
                val colorSpaceArr = ByteArrUtils.subByteArray(
                    buffer.data,
                    buffer.data.size - ByteArrUtils.INT_TO_BYTE_ARRAY_SIZE_FOUR,
                    ByteArrUtils.INT_TO_BYTE_ARRAY_SIZE_FOUR
                )
                val height = ByteArrUtils.byteArrayToInt(heightArr)
                val width = ByteArrUtils.byteArrayToInt(widthArr)
                val colorSpace = ByteArrUtils.byteArrayToInt(colorSpaceArr)
                val heifDecodeFrameWrapper = HeifDecodeFrameWrapper.newInstance(yuvData, width, height, 0)
                when (colorSpace) {
                    YUV_COLOR_SPACE_SRGB -> heifDecodeFrameWrapper.colorSpace = ColorSpace.get(ColorSpace.Named.SRGB)
                    YUV_COLOR_SPACE_DCIP3 -> heifDecodeFrameWrapper.colorSpace = ColorSpace.get(ColorSpace.Named.DCI_P3)
                    YUV_COLOR_SPACE_DISPLAYP3 -> heifDecodeFrameWrapper.colorSpace = ColorSpace.get(ColorSpace.Named.DISPLAY_P3)
                    else -> heifDecodeFrameWrapper.colorSpace = ColorSpace.get(ColorSpace.Named.SRGB)
                }
                if (!ColorModelManager.isWideColorGamutOpen) {
                    heifDecodeFrameWrapper.colorSpace = ColorSpace.get(ColorSpace.Named.SRGB)
                }
                return YuvData(heifDecodeFrameWrapper)
            } catch (e: Exception) {
                GLog.e(TAG, "getImageDataFromBuffer", e)
            }
            return null
        }

        @JvmStatic
        fun getPutTileArray(imageData: ImageData?): ByteArray? {
            if (imageData is YuvData) {
                val array = imageData.yuvData
                array?.let {
                    val originColorSpace = imageData.getColorSpace()
                    var colorSpace = YUV_COLOR_SPACE_SRGB
                    originColorSpace?.let { cp ->
                        colorSpace = when (cp) {
                            ColorSpace.get(ColorSpace.Named.SRGB) -> YUV_COLOR_SPACE_SRGB
                            ColorSpace.get(ColorSpace.Named.DISPLAY_P3) -> YUV_COLOR_SPACE_DISPLAYP3
                            ColorSpace.get(ColorSpace.Named.DCI_P3) -> YUV_COLOR_SPACE_DCIP3
                            else -> YUV_COLOR_SPACE_SRGB
                        }
                    }
                    val widthArr = ByteArrUtils.intToByteArray(imageData.frameWidth)
                    val heightArr = ByteArrUtils.intToByteArray(imageData.frameHeight)
                    val colorSpaceArr = ByteArrUtils.intToByteArray(colorSpace)
                    // 10bit 渲染的时候需要宽和高以及色域，所以， 后12位分别存 宽和高以及色域
                    val buffer = ByteBuffer.allocate(it.size + widthArr.size + heightArr.size + colorSpaceArr.size)
                    buffer.put(array)
                    buffer.put(widthArr)
                    buffer.put(heightArr)
                    buffer.put(colorSpaceArr)
                    return buffer.array()
                }
            }
            return null
        }
    }
}