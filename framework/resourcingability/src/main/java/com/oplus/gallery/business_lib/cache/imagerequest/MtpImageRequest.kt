/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MtpImageRequest.kt
 ** Description : MTP图片资源获取图片解码请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import com.oplus.gallery.business_lib.model.data.base.item.MtpItem
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions

class MtpImageRequest(
    options: ResourceGetOptions,
    private val mtpImage: MtpItem
) : Job<ImageData> {
    private val inThumbnailType = options.inThumbnailType
    override fun call(jc: JobContext): ImageData? {
        val bytes: ByteArray? = if (ThumbnailSizeUtils.isMicroThumbnailKey(inThumbnailType)) {
            mtpImage.thumbnailData ?: mtpImage.imageData
        } else {
            mtpImage.imageData
        }

        bytes ?: let {
            GLog.w(TAG, "decoding thumbnail failed bytes is null!")
            return null
        }

        runCatching {
            CodecHelper.decodeThumbnailArray(jc, bytes, null, MTP_THUMBNAIL_SIZE, inThumbnailType)?.let {
                return if (ThumbnailSizeUtils.isMicroThumbnailKey(inThumbnailType)) {
                    BitmapUtils.resizeAndCropCenter(it, MTP_THUMBNAIL_SIZE, true)
                } else {
                    BitmapUtils.resizeByLongSide(it, MTP_THUMBNAIL_SIZE, true)
                }?.let { bitmap -> ImageData(bitmap) }
            }
        }.onFailure {
            GLog.w(TAG, "decoding thumbnail failed=${it.message}")
        }
        return null
    }

    companion object {
        const val TAG = "MtpImageRequest"
        const val MTP_THUMBNAIL_SIZE = 960
    }
}