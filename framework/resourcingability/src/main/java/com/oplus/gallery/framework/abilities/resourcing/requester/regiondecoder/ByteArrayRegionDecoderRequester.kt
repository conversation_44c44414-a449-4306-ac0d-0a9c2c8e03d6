/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ByteArrayRegionDecoderRequester.kt
 ** Description : 字节数组RegionDecoder的Requester
 ** Version     : 1.0
 ** Date        : 2023/12/07
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2023/12/07      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.key.ByteArrayResourceKey
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder

/**
 * 字节数组RegionDecoder的Requester
 * @param byteArrayResource 外部可通过ResourceKeyFactory.createByteArrayResourceKey创建[ByteArrayResourceKey]
 * 依据Key中的字节数组数据来创建RegionDecoder
 */
internal class ByteArrayRegionDecoderRequester(private val byteArrayResource: ByteArrayResourceKey) : IRegionDecoderRequester {

    override fun request(): IRegionDecoder? {
        return runCatching {
            byteArrayResource.data?.let { bytes ->
                if (bytes.isNotEmpty()) {
                    DecoderWrapper.getIRegionDecoder(bytes, byteArrayResource.offset, byteArrayResource.length).apply {
                        setTombstone(BreakpadTombstone(byteArrayResource.mediaItem.filePath, byteArrayResource.mediaItem.dateModifiedInSec))
                    }
                } else {
                    GLog.w(TAG, "[request] byteArrayResource.data is empty, so we return null.")
                    null
                }
            } ?: run {
                GLog.w(TAG, "[request] byteArrayResource.data is null, so we return null.")
                null
            }
        }.onFailure {
            GLog.w(TAG, "[request] decode fail e=${it.message}")
        }.getOrNull()
    }

    private companion object {
        private const val TAG = "ByteArrayRegionDecoderRequester"
    }
}