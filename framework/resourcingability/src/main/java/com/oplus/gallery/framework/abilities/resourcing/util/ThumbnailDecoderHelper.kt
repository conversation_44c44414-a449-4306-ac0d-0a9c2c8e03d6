/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThumbnailDecoderHelper
 ** Description: 缩图解码辅助类
 **
 ** Version: 1.0
 ** Date: 2024/05/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2024/05/26  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.util

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.standard_lib.codec.videoframe.DecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.DecoderType
import com.oplus.gallery.standard_lib.codec.videoframe.DecoderVendor
import com.oplus.gallery.standard_lib.codec.videoframe.HardwareDecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils.meicamHardwareDecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils.meicamHardwareDolbyGpuDecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils.meicamHardwareDolbyGpuWithP010DecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils.meicamSoftwareDecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils.systemDecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils.tblDecoderConfig

/**
 * 缩图解码辅助类
 */
object ThumbnailDecoderHelper {

    /**
     * 获取解码配置
     * @param mediaItem MediaItem数据
     * @return 返回解码配置
     */
    @JvmStatic
    fun getDecoderConfig(mediaItem: MediaItem, supportDolbyVideoHardwareDecoder: Boolean): DecoderConfig {
        return when {
            // 美摄硬解依赖杜比解码器，需要适配多种平台，目前还有问题，先上软解
            VideoTypeUtils.isDolbyVideo(mediaItem) -> getDecoderConfig(supportDolbyVideoHardwareDecoder)
            VideoTypeUtils.isHlgVideo(mediaItem) -> meicamSoftwareDecoderConfig
            else -> tblDecoderConfig
        }
    }

    /**
     * 更新解码配置
     * @param decoderConfig 解码器配置
     * @return 返回解码配置
     */
    @JvmStatic
    fun updateDecoderConfig(
        decoderConfig: DecoderConfig,
        hardwareDecoderConfig: HardwareDecoderConfig
    ): DecoderConfig {
        if (decoderConfig.vendor == DecoderVendor.MEICAM && decoderConfig.type == DecoderType.HARD) {
            if (hardwareDecoderConfig.isSupportDolbyGPU && hardwareDecoderConfig.isSupportDolbyGPUWithP010) {
                return meicamHardwareDolbyGpuWithP010DecoderConfig
            }
            if (hardwareDecoderConfig.isSupportDolbyGPU) {
                return meicamHardwareDolbyGpuDecoderConfig
            }
        }
        return decoderConfig
    }


    /**
     * 获取解码配置
     * @param codecType 编码类型，目前仅杜比视界可准确判断
     * @return 返回解码配置
     */
    @JvmStatic
    fun getDecoderConfig(codecType: String?, supportDolbyVideoHardwareDecoder: Boolean): DecoderConfig {
        return when {
            VideoTypeUtils.isDolbyVideo(codecType) -> getDecoderConfig(supportDolbyVideoHardwareDecoder)
            else -> systemDecoderConfig
        }
    }

    /**
     * 根据是否支持杜比视频走硬件解码来获取解码配置
     * @param isSupportDolbyVideoHardwareDocoder 是否支持杜比视频制式Dvhe05走硬件解码
     * @return 返回解码配置
     */
    private fun getDecoderConfig(supportDolbyVideoHardwareDecoder: Boolean): DecoderConfig {
        if (supportDolbyVideoHardwareDecoder) {
            return meicamHardwareDecoderConfig
        }
        return meicamSoftwareDecoderConfig
    }
}
