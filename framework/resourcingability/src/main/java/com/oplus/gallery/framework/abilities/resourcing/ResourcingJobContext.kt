/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ResourcingJobContext.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing

import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.taskscheduling.IOnCancelListener
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * ICancelable到JobContext的转换类
 * Marked：by zhangwenming 待Decode相关逻辑替换掉ICancelable之后，该类可以删除
 */
internal class ResourcingJobContext(private val cancelable: ICancelable?) : JobContext {
    override fun isCancelled(): Boolean {
        return cancelable?.isCancelled() == true
    }

    override fun setCancelListener(cancelListener: (() -> Unit)?) {
        cancelable?.let {
            if (cancelListener == null) {
                cancelable.setOnCancelListener(null)
            } else {
                cancelable.setOnCancelListener(object : IOnCancelListener {
                    override fun onCancel() {
                        cancelListener.invoke()
                    }
                })
            }
        } ?: let {
            GLog.d(TAG, "Cancelable is null when setCancelListener")
        }
    }

    companion object {
        private const val TAG = "ResourcingJobContext"
    }
}