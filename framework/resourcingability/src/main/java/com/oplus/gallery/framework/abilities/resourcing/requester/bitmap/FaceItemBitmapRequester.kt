/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - FaceItemBitmapRequester.kt
 ** Description:FaceItem，Bitmap资源加载请求的实现类。
 ** Version: 1.0
 ** Date : 2023/2/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON>ir<PERSON>@Rom.Apps.Gallery    2023/2/22        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.graphics.Bitmap
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.cache.diskcache.CoverCachedHelper
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.key.FaceMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 * FaceItem，Bitmap资源加载请求的实现类。
 */
internal class FaceItemBitmapRequester(
    internal val originalResource: FaceMediaResourceKey,
    internal val options: ResourceGetOptions,
    internal val abilityBus: IAbilityBus,
    internal val cancelable: ICancelable?
) : IBitmapRequester {
    override fun request(): ImageResult<Bitmap>? {
        var resultBitmap = getBitmapFromCache()
        resultBitmap?.let {
            return ImageResult(it)
        }
        if (cancelable?.isCancelled() == true) {
            return null
        }

        val faceItem = originalResource.item
        when (val refItem = faceItem.refItem) {
            is LocalImage -> {
                requestBitmap(refItem, ThumbnailSizeUtils.TYPE_THUMBNAIL)?.also { thumb ->
                    CoverCachedHelper.getFaceDataFromThumb(
                        thumb,
                        faceItem.thumbWInRefItem,
                        faceItem.thumbHInRefItem,
                        faceItem.regionInRefItem
                    )?.also {
                        resultBitmap = it
                    } ?: also {
                        GLog.e(TAG, "[request] failed to get face thumb, use thumb instead")
                        resultBitmap = thumb
                    }
                }
            }
            is LocalVideo -> {
                requestBitmap(refItem, ThumbnailSizeUtils.getMicroThumbnailKey())?.also {
                    resultBitmap = it
                }
            }
            else -> GLog.e(TAG, "[request] not support type: $refItem")
        }

        resultBitmap?.also {
            abilityBus.requireAbility(ICachingAbility::class.java)?.use { cacheAbility ->
                val cacheKey = CacheKeyFactory.createCacheKey(originalResource, options.inCropParams) ?: return null
                val cacheOptions = CacheOptions(inThumbnailType = options.inThumbnailType, inStorageQuality = options.inStorageQuality)
                cacheAbility.thumbnailCache?.putBitmap(cacheKey, it, cacheOptions)
            }
        }

        return resultBitmap?.let { ImageResult(it) }
    }

    private fun requestBitmap(item: MediaItem, thumbnailType: Int): Bitmap? {
        abilityBus.requireAbility(IResourcingAbility::class.java)?.use { cacheAbility ->
            ResourceKeyFactory.createResourceKey(item)?.also {
                return cacheAbility.requestBitmap(it, ResourceGetOptions(thumbnailType))?.result
            }
        }
        return null
    }

    private fun getBitmapFromCache(): Bitmap? {
        return abilityBus.requireAbility(ICachingAbility::class.java)?.use { cacheAbility ->
            val cacheKey = CacheKeyFactory.createCacheKey(originalResource, options.inCropParams) ?: return null
            val inStorageType = when {
                (CacheOperation.ReadAllCache in options.inCacheOperation) -> StorageType.MEMORY_AND_DISK
                (CacheOperation.ReadMemCache in options.inCacheOperation) -> StorageType.MEMORY_ONLY
                (CacheOperation.ReadDiskCache in options.inCacheOperation) -> StorageType.MEMORY_AND_DISK
                else -> return null
            }
            val cacheOptions = CacheOptions(
                inThumbnailType = options.inThumbnailType,
                inStorageQuality = options.inStorageQuality,
                inStorageType = inStorageType
            )

            return runCatching {
                cacheAbility.thumbnailCache?.getBitmap(cacheKey, cacheOptions)
            }.onFailure {
                GLog.e(TAG, it) { "[getBitmapFromCache] error! exception=$it" }
            }.getOrNull()
        }
    }

    companion object {
        private const val TAG = "FaceItemBitmapRequester"
    }
}