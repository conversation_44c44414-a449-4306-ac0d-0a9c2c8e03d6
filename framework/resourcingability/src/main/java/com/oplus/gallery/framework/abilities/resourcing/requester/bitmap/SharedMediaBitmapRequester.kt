/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SharedMediaBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>          <date>      <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhongxuechang       2022/12/09   1.0               create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.provider.MediaStore
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.resourcing.ResourcingJobContext
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.key.SharedMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.scheduler.JobContext

/**
 * SharedMediaItem，Bitmap资源加载请求的实现类。
 */
internal class SharedMediaBitmapRequester(
    private val context: Context,
    private val resourceKey: SharedMediaResourceKey,
    private val options: ResourceGetOptions,
    private val abilityBus: IAbilityBus,
    private val cachingAbility: ICachingAbility?,
    private val colorManagementAbility: IColorManagementAbility?,
    private val cancelable: ICancelable?
) : IBitmapRequester {

    override fun request(): ImageResult<Bitmap>? {
        val resourcingJobContext = ResourcingJobContext(cancelable)
        val mediaItem = resourceKey.mediaItem
        if (mediaItem.isCloudFileExist.not()) {
            // 加载本地视频封面
            if (mediaItem.mediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO) {
                GLog.d(TAG, "request: local video thumbnail")
                val resourceKey = mediaItem.refVideo?.let { ResourceKeyFactory.createResourceKey(it) as? LocalMediaResourceKey } ?: return null
                return LocalVideoBitmapRequester(
                    context,
                    resourceKey,
                    options,
                    abilityBus,
                    cachingAbility,
                    colorManagementAbility,
                    cancelable
                ).request()
            }
            GLog.d(TAG, "request: local image thumbnail")
            return getOriginalBitmap(resourcingJobContext, mediaItem.filePath)?.let { ImageResult(it) }
        }
        // 已上传加载缩略图
        val fileId = mediaItem.fileId ?: TextUtil.EMPTY_STRING
        if (fileId.isNotEmpty()) {
            GLog.d(TAG, "request: thumbnail")
            return getThumbnailBitmap(fileId)?.let { ImageResult(it) }
        }
        GLog.d(TAG, "request: thumbnail null")
        return null
    }

    private fun getThumbnailBitmap(fileId: String): Bitmap? {
        return cachingAbility?.let {
            val cacheOptions = CacheOptions(inThumbnailType = options.inThumbnailType)
            it.thumbnailCache?.getBitmap(
                CacheKeyFactory.createSharedMediaCacheKey(fileId),
                cacheOptions
            )?.let { bitmap ->
                val targetSize = ThumbnailSizeUtils.getTargetSizeInType(options.inThumbnailType)
                // 共享图集的缩图下载是后台下载的大缩图，列表加载时需要将其压缩到小缩图，其他情况加载时需要等比例缩放
                if (ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType)
                    || ThumbnailSizeUtils.isVideoMicroThumbnailKey(options.inThumbnailType)
                ) {
                    BitmapUtils.resizeAndCropCenter(bitmap, targetSize, true)
                } else {
                    BitmapUtils.resizeByShortSide(bitmap, null, targetSize, false, true)
                }
            }
        }
    }

    private fun getOriginalBitmap(jc: JobContext, filePath: String): Bitmap? {
        return CodecHelper.decodeThumbnail(
            jc,
            filePath,
            File(filePath).toUri(),
            BitmapFactory.Options().apply { inPreferredConfig = Bitmap.Config.RGB_565 },
            ThumbnailSizeUtils.getTargetSizeInType(options.inThumbnailType),
            options.inThumbnailType,
            false,
            resourceKey.mediaItem.dateTakenInMs,
            false
        ).bitmap
    }

    companion object {
        const val TAG = "SharedMediaBitmapRequester"
    }
}