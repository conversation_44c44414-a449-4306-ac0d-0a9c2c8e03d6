/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PredecodeBitmapRequester.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/05/16
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/05/16       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.annotation.VisibleForTesting
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.framework.abilities.resourcing.util.ThumbnailDecoderHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getThumbnailRotation
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.getTargetSizeInType
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.crop.isNoCrop
import com.oplus.gallery.framework.abilities.resourcing.key.PredecodeResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.JobContextStub
import java.io.File
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DX4_PLATFORM
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_DOLBY_VIDEO_HARDWARE_DECODER
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_GPU_DECODE
import com.oplus.gallery.standard_lib.codec.videoframe.HardwareDecoderConfig

/**
 * PredecodeBitmapRequester 仅用于相机从相册获取缩图，其他场景禁止使用
 **/
internal class PredecodeBitmapRequester(
    private val context: Context,
    private val resourcingKey: PredecodeResourceKey,
    private val options: ResourceGetOptions,
    private val abilityBus: IAbilityBus,
    private val cancelable: ICancelable? = null
) : IBitmapRequester {

    private val isCancelled: Boolean get() = cancelable?.isCancelled() == true

    override fun request(): ImageResult<Bitmap>? = GTrace.trace({ "$TAG.request" }) {
        return@trace runCatching {
            val targetSize = if (resourcingKey.targetSize > 0) {
                resourcingKey.targetSize
            } else {
                getTargetSizeInType(options.inThumbnailType)
            }


            /**
             * Step 1: 读取内存缓存
             * 从缓存中拿到的 bitmap 均为副本，不是本体，因此旋转后，可以将缓存的 bitmap 回收掉
             */
            // Step 2: 读取磁盘缓存
            if (CacheOperation.ReadAllCache in options.inCacheOperation) {
                GLog.d(TAG, "[request] CacheOperation.ReadAllCache in cache")
                loadFromCache()?.let {
                    GLog.d(TAG, "[request] from cache find bitmap")
                    return@runCatching ImageResult(rotateBitmapIfNeeded(it, true))
                }
                if (isCancelled) {
                    return@runCatching null
                }
            }


            // Step 3:解码原图或加载大图缩略图
            var rawBitmap: Bitmap? = null
            /*Step 3.1: 如果目标尺寸小于大图缩略图尺寸，则尝试加载大图缩略图作为基础图
             *mark by zhangyanbin，能力层不合适添加此类业务逻辑，后续采用ResourceSaveOptions的方案进行优化
             */
            if (targetSize < getTargetSizeInType(ThumbnailSizeUtils.getFullThumbnailKey())) {
                rawBitmap = loadFromCache(ThumbnailSizeUtils.getFullThumbnailKey())
                GLog.d(TAG, "[request] load thumbnail bitmap for resize: ${rawBitmap != null}")
            }

            if (rawBitmap == null) {
                // Step 3.2: 解码原图
                rawBitmap = loadSuitableThumbnail(targetSize) ?: return@runCatching null
            }

            if (isCancelled) {
                rawBitmap.recycle()
                return@runCatching null
            }

            // Step 4: 裁剪缩图
            val predecodeBitmap = if (options.inCropParams.isNoCrop()) {
                BitmapUtils.resizeByLongSide(rawBitmap, targetSize, true) ?: return@runCatching null
            } else {
                BitmapUtils.resizeAndCropCenter(rawBitmap, targetSize, true) ?: return@runCatching null
            }


            if (isCancelled) {
                predecodeBitmap.recycle()
                return@runCatching null
            }


            // Step 5: 存储内存缓存
            if ((CacheOperation.WriteMemCache in options.inCacheOperation) && isCancelled) {
                // Marked by: 2022/5/17 zhangjisong 从内存缓存中读取，内存缓存还未实现，暂不实现
                predecodeBitmap.recycle()
                return@runCatching null
            }

            // Step 6: 存储磁盘缓存
            if (CacheOperation.WriteDiskCache in options.inCacheOperation) {
                saveToCache(predecodeBitmap)
            }

            /**
             * Step 7: 旋转原图
             * predecodeBitmap 可能会保存到缓存中，bitmap 不能回收掉，因此 recycle 为 false
             */
            val rotateBitmap = rotateBitmapIfNeeded(predecodeBitmap, false)

            /*
             * Step 8:
             * 返回结果
             */
            ImageResult(rotateBitmap)
        }.onFailure {
            GLog.w(TAG, "[request] failed: $it", it)
        }.getOrNull()
    }

    /**
     * 不同的资源加载缩图方式不同，需要使用不同的策略
     *
     * 此处使用不同的 [IThumbnailLoader] 封装细节进行加载
     */
    private fun loadSuitableThumbnail(targetSize: Int): Bitmap? {
        return findSuitableThumbnailLoader(targetSize).load()
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun findSuitableThumbnailLoader(targetSize: Int): IThumbnailLoader = when {
        // 如果资源是视频类型，使用 VideoThumbnailLoader
        resourcingKey.path.isVideo -> {
            VideoThumbnailLoader(
                context, resourcingKey, targetSize, abilityBus
            )
        }
        // 否则其他类型使用 ImageThumbnailLoader
        else -> ImageThumbnailLoader(JobContextStub, resourcingKey, options, targetSize)
    }


    private fun loadFromCache(): Bitmap? {
        return abilityBus.requireAbility(ICachingAbility::class.java)?.use {
            val screenNailCache = it.screenNailCache ?: return null
            val cacheKey = CacheKeyFactory.createCacheKey(resourcingKey) ?: return null
            val cacheOptions = CacheOptions(
                inThumbnailType = options.inThumbnailType,
                inStorageQuality = StorageQuality.LOSSLESS,
                inStorageType = StorageType.MEMORY_AND_DISK
            )
            return screenNailCache.getBitmap(cacheKey, cacheOptions)
        }
    }

    private fun loadFromCache(thumbnailType: Int): Bitmap? {
        return abilityBus.requireAbility(ICachingAbility::class.java)?.use {
            val screenNailCache = it.screenNailCache ?: return null
            val cacheKey = CacheKeyFactory.createCacheKey(resourcingKey) ?: return null
            val cacheOptions = CacheOptions(
                inThumbnailType = thumbnailType,
                inStorageQuality = StorageQuality.LOSSLESS,
                inStorageType = StorageType.MEMORY_AND_DISK
            )
            return screenNailCache.getBitmap(cacheKey, cacheOptions)
        }
    }
    private fun rotateBitmapIfNeeded(bitmap: Bitmap, recycle: Boolean): Bitmap {
        return resourcingKey.uri.let {
            GTrace.trace("PredecodeBitmapRequester.getLocalMediaItem") {
                LocalMediaDataHelper.getLocalMediaItem(
                    DataManager.findPathByUri(
                        it,
                        null
                    ), true
                )
            }
        }?.let { mediaItem ->
            BitmapUtils.rotateBitmap(bitmap, mediaItem.getThumbnailRotation(), recycle)
        } ?: bitmap
    }

    private fun saveToCache(bitmap: Bitmap) {
        updatePathCacheKeyIfNeeded(resourcingKey.path)
        abilityBus.requireAbility(ICachingAbility::class.java)?.use {
            val startTime = System.currentTimeMillis()
            synchronized(cacheLock) {
                val screenNailCache = it.screenNailCache ?: return
                val cacheKey = CacheKeyFactory.createCacheKey(resourcingKey, options.inCropParams) ?: return
                val mimeType = MimeTypeUtils.getMimeType(context, resourcingKey.uri)
                val isLosslessCache = ImageTypeUtils.supportLosslessCache(mimeType)
                val cacheOptions = CacheOptions(
                    inThumbnailType = options.inThumbnailType,
                    inStorageQuality = if (isLosslessCache) StorageQuality.LOSSLESS else StorageQuality.HIGH_QUALITY,
                    inStorageType = StorageType.DISK_ONLY
                )
                /*
                 *如果是未裁剪的图，采用screenNailCache缓存
                 *如果是裁剪的图，采用thumbnailCache缓存
                 */
                if (options.inCropParams.isNoCrop()) {
                    screenNailCache.putBitmap(cacheKey, bitmap, cacheOptions)
                } else {
                    it.thumbnailCache?.putBitmap(cacheKey, bitmap, cacheOptions)
                }

                GLog.d(
                    TAG, "saveToCache, isLosslessCache = $isLosslessCache, " +
                            "cost time = ${System.currentTimeMillis() - startTime}ms"
                )
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun updatePathCacheKeyIfNeeded(path: Path) {
        val filePath = resourcingKey.filePath
        if (filePath.isNullOrEmpty()) {
            return
        }
        val cacheKeyPrefix = filePath.replace(FILE_NAME_APPENDIX_TEMP, "")
        val fileSize = File(filePath).run { if (exists()) length() else 0 }
        path.cacheKey = "$cacheKeyPrefix$fileSize"
    }


    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun interface IThumbnailLoader {
        fun load(): Bitmap?
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal class VideoThumbnailLoader(
        private val context: Context,
        private val resourcingKey: PredecodeResourceKey,
        private val targetSize: Int,
        private val abilityBus: IAbilityBus
    ) : IThumbnailLoader {

        override fun load(): Bitmap? {
            return runCatching {
                getThumbnailDecoder().use { videoThumbnailDecoder ->
                    videoThumbnailDecoder.setDataSource(context, resourcingKey.uri)
                    videoThumbnailDecoder.setThumbnailSize(targetSize)
                    /**
                     * 相机是使用系统取帧，并且取第一帧。
                     * 若不使用第一帧，相机点缩略图跳转过来时，会闪其他帧
                     */
                    videoThumbnailDecoder.decodeFrameBitmapAtTime(0)
                }
            }.onFailure {
                GLog.e(TAG, "load, error", it)
            }.getOrNull()
        }

        /**
         * 参考 [LocalVideoBitmapRequester.getThumbnailDecoder]
         * 杜比视频使用美摄的取帧，一般视频用系统取帧
         */
        private fun getThumbnailDecoder(): VideoThumbnailDecoder {
            val decoderConfig = ThumbnailDecoderHelper.updateDecoderConfig(
                ThumbnailDecoderHelper.getDecoderConfig(
                    resourcingKey.codecType,
                    isSupportDolbyVideoHardwareDecoder
                ), getHardwareDecoderConfig()
            )
            return VideoThumbnailDecoderUtils.getVideoThumbnailDecoder(decoderConfig)
        }

        /**
         * 是否支持杜比视频走硬件解码
         * @return 返回知否支持硬件解码
         */
        private val isSupportDolbyVideoHardwareDecoder: Boolean by lazy  {
            abilityBus.requireAbility(IConfigAbility::class.java)?.use {
                it.getBooleanConfig(FEATURE_IS_SUPPORT_DOLBY_VIDEO_HARDWARE_DECODER)
            } ?: false
        }

        /**
         * fix 9071494 解决杜比格式5视频缩图抽偏绿的问题
         * 是否支持杜比视频GPU方案
         */
        private val isSupportDolbyGPUDecoder: Boolean by lazy {
            abilityBus.requireAbility(IConfigAbility::class.java)?.use {
                it.getBooleanConfig(IS_SUPPORT_DOLBY_GPU_DECODE)
            } ?: false
        }

        /**
         * 是否为DX4平台
         */
        private val isDX4Platform: Boolean by lazy {
            abilityBus.requireAbility(IConfigAbility::class.java)?.use {
                it.getBooleanConfig(FEATURE_IS_DX4_PLATFORM)
            } ?: false
        }

        /**
         * 获取硬解配置
         */
        private fun getHardwareDecoderConfig(): HardwareDecoderConfig {
            val isSupportDolbyDecoder = isSupportDolbyVideoHardwareDecoder
            val isSupportDolbyGPUDecoder = isSupportDolbyGPUDecoder
            val isDX4Platform = isDX4Platform
            val isSupportDolbyGPUWithP010Decoder = isSupportDolbyDecoder && isSupportDolbyGPUDecoder && isDX4Platform
            return HardwareDecoderConfig(isSupportDolbyDecoder, isSupportDolbyGPUDecoder, isSupportDolbyGPUWithP010Decoder)
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal class ImageThumbnailLoader(
        private val jobContext: JobContext,
        private val resourcingKey: PredecodeResourceKey,
        private val options: ResourceGetOptions,
        private val targetSize: Int
    ) : IThumbnailLoader {

        override fun load(): Bitmap? {
            val bitmapOptions = BitmapFactory.Options().apply {
                inPreferredConfig = Bitmap.Config.ARGB_8888
            }
            return CodecHelper.decodeThumbnail(
                jobContext,
                resourcingKey.filePath,
                resourcingKey.uri,
                bitmapOptions,
                targetSize,
                options.inThumbnailType,
                resourcingKey.path.isHeif,
                resourcingKey.dateModifiedInSec,
                true
            )?.bitmap
        }
    }

    private companion object {
        private const val TAG = "PredecodeBitmapRequester"

        private const val FILE_NAME_APPENDIX_TEMP = "_tmp"

        private val cacheLock: Any = Any()

        private val Path.isVideo: Boolean
            get() = TypeFilterUtils.isVideo(this)

        private val Path?.mediaSupportFormat: Long?
            get() = (this?.`object` as? MediaItem)?.getSupportFormat(Constants.IMediaItemSupportFormat.FORMAT_HEIF)

        private val Path.isHeif: Boolean
            get() = this.mediaSupportFormat == Constants.IMediaItemSupportFormat.FORMAT_HEIF
    }
}

