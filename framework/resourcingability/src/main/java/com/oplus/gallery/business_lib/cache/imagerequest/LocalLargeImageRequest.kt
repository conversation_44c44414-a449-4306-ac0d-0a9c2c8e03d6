/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LocalLargeImageRequest.kt
 ** Description : 本地图片资源获取区域图片解码请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** JinPeng@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.foundation.util.debug.GLog
import java.lang.ref.WeakReference

class LocalLargeImageRequest(
    private val localImage: LocalImage
) : Job<IRegionDecoder> {

    private val weakReferenceLocalImage = WeakReference(localImage)

    override fun call(jc: JobContext): IRegionDecoder? {
        val loadStatus = weakReferenceLocalImage.get()?.loadStatus ?: LocalMediaItem.LOAD_STATUS_INVALID
        if (loadStatus == LocalMediaItem.LOAD_STATUS_INVALID) {
            GLog.e(TAG, "LocalImage ${weakReferenceLocalImage.get()} not init")
            return null
        }
        runCatching {
            return DecoderWrapper.getIRegionDecoder(localImage.filePath, isUseFd = false)?.apply {
                setTombstone(BreakpadTombstone(localImage.filePath, localImage.dateModifiedInSec))
            }
        }.onFailure {
            GLog.e(TAG, "Region decode fail ${it.message}")
        }
        return null
    }

    companion object {
        const val TAG = "LocalLargeImageRequest"
    }
}