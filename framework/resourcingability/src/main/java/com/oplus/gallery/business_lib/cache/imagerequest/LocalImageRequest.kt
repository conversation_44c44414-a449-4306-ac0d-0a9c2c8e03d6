/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LocalImageRequest.kt
 ** Description : 本地图片资源获取缓存缩图请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON>eng@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.Context
import android.graphics.BitmapFactory
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.isExtendThumbnailKey
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.isMicroThumbnailKey
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.RegionDecoder
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import java.lang.ref.WeakReference

open class LocalImageRequest(
    context: Context,
    session: WorkerSession,
    options: ResourceGetOptions,
    private val localImage: LocalImage
) : ImageCacheRequest(context, session, options, localImage) {

    private val weakReferenceLocalImage = WeakReference(localImage)

    override fun call(jc: JobContext): ImageData? {
        return runCatching {
            GTrace.trace("LocalImageRequest ${localImage.path}") {
                /*
                 * 原提交：2018/3/27 hash:8d632f3306f42ad3a0b7a08c35c4ff965ead3da4：
                 * 大图打开heif图片显示慢，原因是heif不支持多线程解码，且单个heif解码时间过长，所以需要在解码照片详情页的缩图时，
                 * 解码960图（作为大图的缩略图）当打开大图时，显示heif960缩图会快些
                 * 而新增了240dp 320dp 360dp的缩图,当缩图的heif图片 （场景是：精选画廊）
                 * 小于等于于TYPE_THUMBNAIL的960时，需要解码960的图片缓存起来(当点击查看大图时可以加快显示速度)，
                 * 当大于960时,只在保存时将解码的图片压缩成960即可
                 */
                if ((isMicroThumbnailKey(options.inThumbnailType) || isExtendThumbnailKey(options.inThumbnailType)) &&
                    ImageTypeUtils.isHeif(localImage.path)) {
                    synchronized(localImage.path) {
                        super.call(jc)
                    }
                } else {
                    super.call(jc)
                }
            }
        }.onFailure {
            GLog.e(TAG, "call: ", it)
        }.getOrNull()
    }

    @Suppress("LongMethod")
    override fun onDecodeOriginal(jc: JobContext, type: Int): ImageData? {
        if (requestFlags and REQUEST_FLAT_NOT_DECODE_ORIGINAL == REQUEST_FLAT_NOT_DECODE_ORIGINAL) {
            GLog.d(TAG) { "onDecodeOriginal. can't decode original. skip. id=${localItem.mediaId}, requestFlags=$requestFlags" }
            return null
        }

        val options = BitmapFactory.Options().apply {
            inPreferredConfig = DecodeUtils.DECODE_CONFIG
        }

        val mediaId = localImage.mediaId.toString()
        val targetSize = ThumbnailSizeUtils.getTargetSizeInType(type)
        // 启用智能裁剪时，该解原图方法不去做居中裁剪
        val crop = localImage.cropRectSet == null
        weakReferenceLocalImage.get()?.let { image ->
            image.mimeType?.let { mimeType ->
                if (MimeTypeUtils.isBmp(mimeType)) {
                    var imageWidth = image.width
                    var imageHeight = image.height
                    if ((imageWidth <= 0) || (imageHeight <= 0) || ((imageWidth * imageHeight) >= DECODE_LOCK_MAX_SIZE)) {
                        DecodeUtils.decodeBounds(context, localImage.contentUri, image.filePath, options)
                        imageWidth = options.outWidth
                        imageHeight = options.outHeight
                    }
                    if ((imageWidth > 0) && (imageHeight > 0) && ((imageWidth * imageHeight) < DECODE_LOCK_MAX_SIZE)) {
                        synchronized(weakReferenceLocalImage) {
                            return if (jc.isCancelled()) {
                                null
                            } else {
                                CodecHelper.decodeThumbnail(
                                    jc,
                                    image.filePath,
                                    mediaId,
                                    options,
                                    targetSize,
                                    type,
                                    localImage.isYuvAndLargePage(type),
                                    image.dateModifiedInSec,
                                    crop
                                )
                            }
                        }
                    }
                }
            }

            return if (FeatureUtils.isQualcommPlatform) {
                localImage.apply {
                    if (CodecHelper.isInterceptDecode(context, width.toLong(), height.toLong(), filePath, mediaId)) {
                        GLog.d(TAG, "isInterceptDecode so return")
                        return null
                    }
                }
                if (RegionDecoder.isSupportRegionDecode(image.mimeType)) {
                    CodecHelper.decodeRegionFullRectThumbnail(
                        jc,
                        image.filePath,
                        mediaId,
                        options,
                        targetSize,
                        type,
                        localImage.isYuvAndLargePage(type),
                        image.dateModifiedInSec,
                        crop
                    )
                } else {
                    CodecHelper.decodeThumbnail(
                        jc,
                        image.filePath,
                        mediaId,
                        options,
                        targetSize,
                        type,
                        localImage.isYuvAndLargePage(type),
                        image.dateModifiedInSec,
                        crop
                    )
                }
            } else {
                CodecHelper.decodeThumbnail(
                    jc,
                    image.filePath,
                    mediaId,
                    options,
                    targetSize,
                    type,
                    localImage.isYuvAndLargePage(type),
                    image.dateModifiedInSec,
                    crop
                )
            }
        } ?: return null
    }

    companion object {
        const val TAG = "LocalImageRequest"
        private const val DECODE_LOCK_MAX_SIZE = 4000 * 4000
    }
}