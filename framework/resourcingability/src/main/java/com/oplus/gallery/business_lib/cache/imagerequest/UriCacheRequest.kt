/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : UriCacheRequest.kt
 ** Description : Uri图片或Uri视频资源获取缓存图片请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** JinPeng@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.os.ParcelFileDescriptor
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.cache.DownloadCache
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.scheduler.DefaultScheduler
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.net.URI
import java.net.URL

abstract class UriCacheRequest(
    val context: Context = ContextGetter.context,
    val session: WorkerSession = DefaultScheduler.lowPrioritySession,
    val uri: Uri,
    options: ResourceGetOptions,
    var contentType: String,
    val uriItem: BaseUriItem
) {
    val inThumbnailType = options.inThumbnailType
    private var dateModifiedInSec: Long = -1L
    var cacheEntry: DownloadCache.Entry? = null
    var pfd: ParcelFileDescriptor? = null
    var state: Int = STATE_INIT
    private val lock = Object()

    fun getBitmap(jc: JobContext): ImageData? {
        if (!prepareInputFile(jc)) {
            return null
        }
        readFileDateModify()

        // fixme dajian 2021/2/4 为解决1085967 去掉缓存代码 3d上是区分为UriImage和UriShareImage此处逻辑后续得再对比看下
        val targetSize = updateThumbnailTargetSize()
        val imageData = decodeThumbnailFromFile(jc, targetSize)

        dealReload()

        if (jc.isCancelled() || imageData == null) {
            return null
        }

        imageData.bitmap = if (ThumbnailSizeUtils.isMicroThumbnailKey(inThumbnailType)) {
            BitmapUtils.resizeAndCropCenter(imageData.bitmap, targetSize, true)
        } else {
            BitmapUtils.resizeByLongSide(imageData.bitmap, targetSize, true)
        }

        if (jc.isCancelled()) {
            return null
        }

        return imageData
    }

    fun prepareInputFile(jc: JobContext): Boolean {
        jc.setCancelListener {
            synchronized(lock) {
                lock.notifyAll()
            }
        }

        while (true) {
            synchronized(lock) {
                if (jc.isCancelled()) return false

                when (state) {
                    STATE_INIT -> {
                        state = STATE_DOWNLOADING
                        openFileOrDownloadTempFile(jc)
                    }
                    STATE_DOWNLOADED -> return true
                    STATE_ERROR -> return false
                    else /*STATE_DOWNLOADING*/ -> {
                        runCatching { lock.wait() }
                        return@synchronized
                    }
                }
            }
        }
    }

    private fun openFileOrDownloadTempFile(jc: JobContext) {
        val downloadState = openOrDownloadInner(jc)
        synchronized(lock) {
            state = downloadState
            if ((downloadState != STATE_DOWNLOADED) && (pfd != null)) {
                IOUtils.closeQuietly(pfd)
                pfd = null
            }
            lock.notifyAll()
        }
    }

    private fun openOrDownloadInner(jc: JobContext): Int {
        GLog.d(UriImageRequest.TAG, "openOrDownloadInner uri: $uri")

        if ((ContentResolver.SCHEME_CONTENT == uri.scheme) ||
            (ContentResolver.SCHEME_ANDROID_RESOURCE == uri.scheme) ||
            (ContentResolver.SCHEME_FILE == uri.scheme)) {
            runCatching {
                updateContentIfNeeded()
                pfd = context.contentResolver.openFileDescriptor(uri, "r")
            }.onFailure {
                GLog.w(UriImageRequest.TAG, "open fail e=${it.message}")
                return STATE_ERROR
            }
            if (jc.isCancelled()) {
                return STATE_INIT
            }
            return STATE_DOWNLOADED
        } else {
            runCatching {
                val url: URL = URI(uri.toString()).toURL()
                cacheEntry = DownloadCache.getDownloadCache().download(jc, session, url)?.apply {
                    val openFileRequest: OpenFileRequest = OpenFileRequest.Builder().apply {
                        setUri(uri)
                        setFile(cacheEntry?.mCacheFile)
                    }.builder()
                    pfd = FileAccessManager.getInstance().openFile(ContextGetter.context, openFileRequest)
                    if (uriItem.needDecodeOrientation()) {
                        FileAccessManager.getInstance().getInputStream(ContextGetter.context, openFileRequest)?.use {
                            uriItem.updateRotation(it)
                        }
                    }
                } ?: let {
                    GLog.w(UriImageRequest.TAG, "download failed ")
                    return STATE_ERROR
                }
            }.onFailure {
                GLog.w(UriImageRequest.TAG, "open fail e=${it.message}")
                return STATE_ERROR
            }
            if (jc.isCancelled()) {
                return STATE_INIT
            }
            return STATE_DOWNLOADED
        }
    }

    /**
     * 根据需要更新内容
     */
    abstract fun updateContentIfNeeded()

    /**
     * 更新缩略图的目标尺寸
     * @return 缩略图的目标尺寸
     */
    abstract fun updateThumbnailTargetSize(): Int

    /**
     * 从文件中解码缩略图
     * @param jc JobContext
     * @param targetSize 缩略图的目标尺寸
     * @return 解码后的图像数据
     */
    abstract fun decodeThumbnailFromFile(jc: JobContext, targetSize: Int): ImageData?

    protected open fun readFileDateModify() {
        if (dateModifiedInSec >= 0) {
            GLog.d(TAG, "fetchDateModify already fetch")
            return
        }
        var file: File? = null
        try {
            file = if (ContentResolver.SCHEME_FILE == uri.scheme) {
                File(uri.path)
            } else {
                cacheEntry?.mCacheFile
            }
        } catch (ex: Exception) {
            GLog.d(TAG, "fetchDateModify getFile error:$ex")
        }
        dateModifiedInSec = file?.lastModified() ?: 0
    }

    fun isSharable(): Boolean {
        /*
         * We cannot grant read permission to the receiver since we put
         * the data URI in EXTRA_STREAM instead of the data part of an intent
         * And there are issues in MediaUploader and Bluetooth file sender to
         * share a general image data. So, we only share for local file.
         */
        return ContentResolver.SCHEME_FILE == uri.scheme
    }

    fun dealReload() {
        if (uriItem.mAlwaysReload && (state == STATE_DOWNLOADED) && (pfd != null)) {
            IOUtils.closeQuietly(pfd)
            pfd = null
        }
        state = STATE_INIT
    }

    companion object {
        private const val TAG = "UriCacheRequest"
        const val STATE_ERROR = -1
        const val STATE_INIT = 0
        const val STATE_DOWNLOADING = 1
        const val STATE_DOWNLOADED = 2
    }
}