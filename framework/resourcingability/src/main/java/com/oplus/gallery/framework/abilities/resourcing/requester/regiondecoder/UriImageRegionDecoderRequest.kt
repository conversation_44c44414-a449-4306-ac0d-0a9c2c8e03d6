/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UriImageRegionDecoderRequest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.business_lib.cache.imagerequest.UriCacheRequest
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.framework.abilities.resourcing.ResourcingJobContext
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions

/**
 * UriImage， IRegionDecoder资源加载实现类
 */
internal class UriImageRegionDecoderRequest(
    originalResource: UriMediaResourceKey,
    private val cancelable: ICancelable?
) : UriCacheRequest(
    uri = originalResource.contentUri,
    options = ResourceGetOptions(inThumbnailType = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL),
    contentType = originalResource.mimeType,
    uriItem = originalResource.uriItem
), IRegionDecoderRequester {

    @Suppress("TooGenericExceptionCaught")
    override fun request(): IRegionDecoder? {
        val resourcingJobContext = ResourcingJobContext(cancelable)
        if (!prepareInputFile(resourcingJobContext)) {
            return null
        }
        /*
         * BitmapRegionDecoder decoder =
         * DecodeUtils.createBitmapRegionDecoder(
         * jc, mFileDescriptor.getFileDescriptor(), false);
         * LiuJunChao modify for MPO file
         */
        var decoder: IRegionDecoder? = null
        runCatching {
            // in android 7.1, decodeFileDescriptor will cause out of memory, if the uri is a file, use decodeFile instead.
            val tombstone = BreakpadTombstone(uriItem.filePath, uriItem.dateModifiedInSec)
            decoder = if (isSharable()) {
                pfd?.fileDescriptor?.let {
                    DecoderWrapper.getIRegionDecoder(it)
                }
            } else {
                pfd?.fileDescriptor?.let {
                    DecoderWrapper.getIRegionDecoder(it)
                }
            }
            decoder?.setTombstone(tombstone)
        }.onFailure {
            GLog.e(TAG, "RegionDecoderJob", it)
        }
        dealReload()

        return decoder?.apply {
            uriItem.width = getWidth()
            uriItem.height = getHeight()
        }
    }

    override fun updateContentIfNeeded() {
        // do nothing
    }

    override fun updateThumbnailTargetSize(): Int = 0

    override fun decodeThumbnailFromFile(jc: JobContext, targetSize: Int): ImageData? = null

    companion object {
        const val TAG = "UriImageRegionDecoderRequest"
    }
}