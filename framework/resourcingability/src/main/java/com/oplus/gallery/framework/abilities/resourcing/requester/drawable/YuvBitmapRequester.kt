/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : YuvBitmapRequester.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/4/29 18:40
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/4/29      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.drawable

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.ColorSpace
import android.graphics.Paint
import android.os.Build
import androidx.annotation.RequiresApi
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.standard_lib.codec.yuv.decoder.YuvImageDecoder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.fillByteArray
import com.oplus.gallery.foundation.util.ext.hasGainmapCompat
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.requester.IRequester
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.LHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.convertToGainmap

/**
 * YuvBitmap资源加载实现类
 */
@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
internal class YuvBitmapRequester(
    private val context: Context,
    private val resourceKey: LocalMediaResourceKey,
    private val abilityBus: IAbilityBus,
    private val cachingAbility: ICachingAbility?,
    private val cancelable: ICancelable?
) : IRequester<ImageResult<Bitmap>> {

    private val isUHDRSupported: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ULTRA_HDR) ?: false
        } ?: false
    }

    override fun request(): ImageResult<Bitmap>? {
        val start = System.currentTimeMillis()
        var resultBitmap = GTrace.trace("getBitmapFromCache#${resourceKey.mediaId}") {
            getBitmapFromCache()
        }
        if (cancelable?.isCancelled() == true) {
            return null
        }
        if (resultBitmap == null) {
            resultBitmap = decodeLocalFile()
        }

        // 为避免缓存中图片缺失gainmap，需要在解出bitmap后再次check，如有缺失，则再解一下gainmap
        resultBitmap?.prepareGainmap()

        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, LogFlag.DL) { "request. mediaId=$${resourceKey.mediaId} consume=${System.currentTimeMillis() - start}" }
        }
        return resultBitmap?.let { ImageResult(it) }
    }

    private fun getBitmapFromCache(): Bitmap? {
        return abilityBus.requireAbility(ICachingAbility::class.java)?.use { cacheAbility ->
            val cacheKey = CacheKeyFactory.createCacheKey(resourceKey, CropParams.noCrop()) ?: return null
            val cacheOptions = CacheOptions(
                inThumbnailType = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL,
                inStorageQuality = StorageQuality.HIGH_QUALITY,
                inStorageType = StorageType.MEMORY_AND_DISK
            )

            runCatching {
                return cacheAbility.thumbnailCache?.getBitmap(cacheKey, cacheOptions)
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, it) { "[getBitmapFromCache] error! mediaId=$${resourceKey.mediaId}" }
            }
            return null
        }
    }

    private fun decodeLocalFile(): Bitmap? {
        val bitmap = GTrace.trace("decodeYuvOriginal$${resourceKey.mediaId}") {
            decodeOriginal()
        } ?: run {
            GLog.e(TAG, LogFlag.DL) { "decodeLocalFile. decodeOriginal failed bimap is null. mediaId=${resourceKey.mediaId}" }
            return null
        }

        bitmap.prepareGainmap()

        GTrace.trace("saveBitmapToCache#${resourceKey.mediaId}") {
            saveBitmapToCache(bitmap)
        }

        if (cancelable?.isCancelled() == true) {
            GLog.d(TAG, LogFlag.DL, "decodeLocalFile. cancelled, mediaId=${resourceKey.mediaId}")
            return null
        }

        return bitmap
    }

    private fun saveBitmapToCache(bitmap: Bitmap): Boolean {
        abilityBus.requireAbility(ICachingAbility::class.java)?.use { cacheAbility ->
            runCatching {
                abilityBus.requireAbility(IColorManagementAbility::class.java)?.use { colorManagementAbility ->
                    if (colorManagementAbility.isWideColorGamutEnabled == true) {
                        colorManagementAbility.convertBitmapPixels(bitmap, ColorSpace.get(ColorSpace.Named.DISPLAY_P3))
                    }
                }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, it) { "saveBitmapToCache. failed to adjustColorSpace" }
                return false
            }

            val cacheKey = CacheKeyFactory.createCacheKey(resourceKey, CropParams.noCrop()) ?: return false
            val cacheOptions = CacheOptions(
                inThumbnailType = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL,
                inStorageQuality = StorageQuality.HIGH_QUALITY,
                inStorageType = StorageType.MEMORY_AND_DISK
            )
            val result = cacheAbility.thumbnailCache?.putBitmap(cacheKey, bitmap, cacheOptions)
            return result?.isSuccess() ?: false
        } ?: let {
            GLog.e(TAG, LogFlag.DL) { "saveBitmapToCache. ICachingAbility is null when save cache." }
            return false
        }
    }

    private fun decodeOriginal(): Bitmap? {
        val options = BitmapFactory.Options().apply {
            inSampleSize = 0
        }
        runCatching {
            val start = System.currentTimeMillis()
            val yuvData = CodecHelper.getDecodeThumbnailStream(resourceKey.filePath, resourceKey.mediaId.toString())?.use { fin ->
                YuvImageDecoder().decode(fin, options, false)
            } ?: let {
                GLog.e(TAG, LogFlag.DL) { "decodeOriginal. decode YuvData failed." }
                return null
            }
            val byteArray = yuvData.yuvData ?: run {
                GLog.e(TAG, LogFlag.DL) { "decodeOriginal. decode YuvData.yuvData Failed." }
                return null
            }
            /*
             * 由于RGBA_1010102格式照片不支持jpg编解码，这里抖动绘制成ARGB_8888格式来做磁盘缓存
             */
            val bitmap1010102 = BitmapPools.getBitmap(yuvData.frameWidth, yuvData.frameHeight, Bitmap.Config.RGBA_1010102)
                ?: Bitmap.createBitmap(yuvData.frameWidth, yuvData.frameHeight, Bitmap.Config.RGBA_1010102)
            bitmap1010102.fillByteArray(byteArray)
            val bitmap8888 = BitmapPools.getBitmap(yuvData.frameWidth, yuvData.frameHeight, Bitmap.Config.ARGB_8888)
                ?: Bitmap.createBitmap(yuvData.frameWidth, yuvData.frameHeight, Bitmap.Config.ARGB_8888)
            Canvas(bitmap8888).apply {
                drawBitmap(bitmap1010102, 0f, 0f, Paint(Paint.DITHER_FLAG))
            }
            BitmapPools.recycle(bitmap1010102)
            GLog.d(TAG, LogFlag.DL) { "decodeOriginal. Successful. consume=${System.currentTimeMillis() - start}" }
            return bitmap8888
        }.onFailure {
            GLog.e(TAG, LogFlag.DF, it) { "decodeOriginal. failed." }
        }
        return null
    }

    /**
     * 为bitmap准备hdr增益信息gainmap，用于hdr渲染
     */
    private fun Bitmap.prepareGainmap() {
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            if (isUHDRSupported.not()) {
                // 不支持UHDR机型或版本，不支持hdr的缩图类型，删除缓存中的gainmap，避免后处理(resize等操作)耗时
                gainmap = null
            } else if (hasGainmapCompat().not()) {
                // 支持UHDR机型，如果为localhdr资源，或者heif资源，无法直接解出gainmap，需要调用oplus接口来解码gainmap
                val hdrDrawable = GTrace.trace("Decode Gainmap") {
                    HdrImageDrawableRequester(context, resourceKey, ResourceGetOptions(), cachingAbility).request()?.result as? HdrImageDrawable
                }
                gainmap = if (hdrDrawable?.metadata is LHdrMetadataPack) {
                    GTrace.trace("Convert LHDR to UHDR") {
                        hdrDrawable.convertToGainmap(this)
                    }
                } else {
                    hdrDrawable?.toGainmap()
                }
            }
        }
    }

    companion object {
        private const val TAG = "YuvBitmapRequester"
    }
}