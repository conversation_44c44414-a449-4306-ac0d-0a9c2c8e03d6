/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UriImageBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.graphics.Bitmap
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.cache.imagerequest.UriCacheRequest
import com.oplus.gallery.foundation.exif.raw.ExifUtils
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.ResourcingJobContext
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 *  UriImage Bitmap资源请求加载类。
 */
internal class UriImageBitmapRequester(
    originalResource: UriMediaResourceKey,
    options: ResourceGetOptions,
    private val cancelable: ICancelable?
) : UriCacheRequest(
    uri = originalResource.contentUri,
    options = options,
    contentType = originalResource.mimeType,
    uriItem = originalResource.uriItem
), IBitmapRequester {
    override fun request(): ImageResult<Bitmap>? {
        val resourcingJobContext = ResourcingJobContext(cancelable)
        return getBitmap(resourcingJobContext)?.bitmap?.let { ImageResult(it) }
    }

    override fun updateContentIfNeeded() {
        updateRotationIfNeed()
        updateMimeTypeIfNeed()
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun updateRotationIfNeed() {
        if (MimeTypeUtils.MIME_TYPE_IMAGE_JPEG.equals(contentType, true) && (uriItem.rotation == INVALIDATE_ROTATION)) {
            runCatching {
                context.contentResolver.openInputStream(uri).use {
                    uriItem.rotation = ExifUtils.getOrientation(it)
                }
            }.onFailure {
                GLog.e(TAG, "updateRotationIfNeed catch exception ${it.message}")
            }
        }
    }

    private fun updateMimeTypeIfNeed() {
        if (MimeTypeUtils.MIME_TYPE_IMAGE_JPEG.equals(contentType, true) && uriItem.mAlwaysReload) {
            runCatching {
                context.contentResolver.openInputStream(uri).use { ins ->
                    val outMimeType = DecodeUtils.decodeBounds(ins, null).outMimeType
                    if (TextUtils.isEmpty(outMimeType)) contentType = outMimeType
                }
            }.onFailure {
                GLog.e(TAG, "getOptions e=${it.message}")
            }
        }
    }

    override fun updateThumbnailTargetSize(): Int {
        return ThumbnailSizeUtils.getTargetSizeInType(inThumbnailType)
    }

    override fun decodeThumbnailFromFile(jc: JobContext, targetSize: Int): ImageData? {
        /*
         * We cannot grant read permission to the receiver since we put
         * the data URI in EXTRA_STREAM instead of the data part of an intent
         * And there are issues in MediaUploader and Bluetooth file sender to
         * share a general image data. So, we only share for local file.
         */
        return if (isSharable()) {
            CodecHelper.decodeThumbnail(jc, uri.path, uri, null,
                targetSize, inThumbnailType,
                uriItem.isYuvAndLargePage(inThumbnailType),
                uriItem.dateModifiedInSec, true)
        } else {
            CodecHelper.decodeThumbnail(jc, pfd, null,
                targetSize, inThumbnailType,
                uriItem.isYuvAndLargePage(inThumbnailType),
                uriItem.dateModifiedInSec, true)
        }
    }

    companion object {
        const val TAG = "UriImageRequest"
        const val INVALIDATE_ROTATION = -1
    }
}