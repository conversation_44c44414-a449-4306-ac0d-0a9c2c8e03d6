/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : UriImageRequest.kt
 ** Description : Uri图片资源获取缓存缩图请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/
package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.Context
import android.net.Uri
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.foundation.exif.raw.ExifUtils
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions

class UriImageRequest(
    context: Context,
    session: WorkerSession,
    path: Path,
    uri: Uri,
    options: ResourceGetOptions,
    contentType: String,
    uriItem: BaseUriItem
) : UriCacheRequest(context, session, uri, options, contentType, uriItem), Job<ImageData> {

    override fun call(jc: JobContext): ImageData? {
        return getBitmap(jc)
    }

    override fun updateContentIfNeeded() {
        updateRotationIfNeed()
        updateMimeTypeIfNeed()
    }

    private fun updateRotationIfNeed() {
        if (MIME_TYPE_IMAGE_JPEG.equals(contentType, true) && (uriItem.rotation == INVALIDATE_ROTATION)) {
            runCatching {
                context.contentResolver.openInputStream(uri).use {
                    uriItem.rotation = ExifUtils.getOrientation(it)
                }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "getOptions e=${it.message}" }
            }
        }
    }

    private fun updateMimeTypeIfNeed() {
        if (MIME_TYPE_IMAGE_JPEG.equals(contentType, true) && uriItem.mAlwaysReload) {
            runCatching {
                context.contentResolver.openInputStream(uri).use { ins ->
                    val outMimeType = DecodeUtils.decodeBounds(ins, null).outMimeType
                    if (TextUtils.isEmpty(outMimeType)) contentType = outMimeType
                }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "getOptions e=${it.message}" }
            }
        }
    }

    override fun updateThumbnailTargetSize(): Int {
        return ThumbnailSizeUtils.getTargetSizeInType(inThumbnailType)
    }

    override fun decodeThumbnailFromFile(jc: JobContext, targetSize: Int): ImageData? {
        /*
         * We cannot grant read permission to the receiver since we put
         * the data URI in EXTRA_STREAM instead of the data part of an intent
         * And there are issues in MediaUploader and Bluetooth file sender to
         * share a general image data. So, we only share for local file.
         */
        return if (isSharable()) {
            CodecHelper.decodeThumbnail(jc, uri.path, uri, null,
                targetSize, inThumbnailType,
                uriItem.isYuvAndLargePage(inThumbnailType),
                uriItem.dateModifiedInSec, true
            )
        } else {
            CodecHelper.decodeThumbnail(jc, pfd, null,
                targetSize, inThumbnailType,
                uriItem.isYuvAndLargePage(inThumbnailType),
                uriItem.dateModifiedInSec, true
            )
        }
    }

    companion object {
        const val TAG = "UriImageRequest"
        const val INVALIDATE_ROTATION = -1
    }
}