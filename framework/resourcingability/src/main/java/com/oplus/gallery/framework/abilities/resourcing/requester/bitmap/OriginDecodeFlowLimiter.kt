/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : OriginDecodeFlowLimiter.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/7/20 15:06
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2023/7/20  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.math.MathUtil.clamp
import com.oplus.gallery.foundation.util.systemcore.LowMemUtils
import com.oplus.gallery.standard_lib.app.AppConstants.Capacity.MEM_1G
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Semaphore

/**
 * 原图解码限流阀
 * 为避免同时解码多个超大图，导致应用内存占用过高被杀进程，新增限流阀模块
 */
internal object OriginDecodeFlowLimiter {
    private const val TAG = "OriginDecodeFlowLimiter"

    /**
     * 每GB内存的像素限流阀值，比如4GB内存手机总阀值为4亿像素
     */
    private const val TRAFFIC_THRESHOLD_PER_G_MEM = 10000 * 10000

    private const val TIMEOUT_DEFAULT = 10000L

    /**
     * 最小内存阈值（单位GB）
     */
    private const val MEMORY_MIN = 4

    /**
     * 最大内存阈值（单位GB，不能超过22，否则wholePermits会超过Int.MAX_VALUE）
     */
    private const val MEMORY_MAX = 16

    /**
     * 总信号量，也是信号量阈值，请求总数大于该值，则阻塞
     */
    private val wholePermits =
        clamp((LowMemUtils.getTotalMemSize() / MEM_1G).toInt(), MEMORY_MIN, MEMORY_MAX) * TRAFFIC_THRESHOLD_PER_G_MEM
    /**
     * 当前各线程的信号量请求情况，<Long, Int> 对于 <线程ID, 已请求信号量>
     */
    private val permitsMap = ConcurrentHashMap<Long, Int>()

    /**
     * 当前各线程的超时任务，<Long, Job> 对于 <线程ID, 超时任务>
     */
    private val timeoutJobMap = ConcurrentHashMap<Long, Job>()

    /**
     * 配置为公平模式，先来先执行
     */
    private val semaphore = Semaphore(wholePermits, true)

    /**
     * 为当前线程请求信号量，如果信号量已满，则阻塞等待
     *
     * @param permits 要求信号量
     */
    fun acquire(permits: Int, timeout: Long = TIMEOUT_DEFAULT) {
        if (permits <= 0) {
            GLog.e(TAG) { "acquire. Permits[$permits] is invalid. Skip." }
            return
        }

        val currentThreadId = Thread.currentThread().id
        release(currentThreadId, cancelTimeoutJob = true)
        // 为避免单次permits大于阈值，导致永远无法执行，进行最大值矫正
        val availablePermits = permits.coerceAtMost(wholePermits)
        semaphore.acquire(availablePermits)
        permitsMap[currentThreadId] = availablePermits

        // 获得信号量，开始执行任务时，启动超时检查
        timeoutJobMap[currentThreadId] = AppScope.launch(Dispatchers.IO) {
            delay(timeout)
            release(currentThreadId, cancelTimeoutJob = false)
        }
    }

    /**
     * 释放当前线程所有信号量
     */
    fun release() {
        release(Thread.currentThread().id, cancelTimeoutJob = true)
    }

    private fun release(threadId: Long, cancelTimeoutJob: Boolean) {
        if (cancelTimeoutJob) {
            timeoutJobMap.remove(threadId)?.cancel()
        }
        permitsMap.remove(threadId)?.let { permits ->
            semaphore.release(permits)
        }
    }
}