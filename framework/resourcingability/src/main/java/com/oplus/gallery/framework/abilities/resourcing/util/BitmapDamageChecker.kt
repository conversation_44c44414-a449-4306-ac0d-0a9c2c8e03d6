/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BitmapDamageChecker
 ** Description : Bitmap对应图片文件是否损坏的判断方法
 **
 ** Version     : 1.0
 ** Date        : 2025/6/27
 ** Author      : 80412226
 ** TAG         : NA
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80412226                         2025/6/27      1.0         NA
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.util

import android.graphics.Bitmap
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.tracing.constant.PictureDecodeTrackConstant
import com.oplus.gallery.foundation.tracing.helper.PictureDecodeTrackHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.maskPath
import com.oplus.media.OplusBitmapChecker


private const val TAG = "BitmapDamageChecker"

/**
 * 判断图片是否缺损
 */
fun checkBitmapDamage(bitmap: Bitmap, path: String): Boolean {
    return runCatching {
        OplusBitmapChecker.getIncompleteFlag(bitmap).also {
            if (it) {
                PictureDecodeTrackHelper.trackPictureDecodeStatus(
                    PictureDecodeTrackConstant.PictureDecodeStatus.DAMAGE,
                    DatabaseUtils.PATTERN_CAMERA.matcher(path).find()
                )
                GLog.d(TAG, LogFlag.DF) { "checkBitmapDamage: $it path: ${path.maskPath()}" }
            }
        }
    }.onFailure {
        GLog.d(TAG, LogFlag.DF) { "checkBitmapDamage, e: ${it.message}" }
    }.getOrDefault(false)
}