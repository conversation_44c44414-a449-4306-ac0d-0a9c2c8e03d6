/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - YuvDrawableRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.drawable

import android.graphics.BitmapFactory
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.key.YuvDataResourceKey
import com.oplus.gallery.standard_lib.codec.yuv.decoder.YuvImageDecoder
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.resourcing.requester.IRequester
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.standard_lib.codec.yuv.drawable.YuvDrawable

/**
 * YuvDrawable资源加载实现类
 */
internal class YuvDrawableRequester(private val resourceKey: YuvDataResourceKey) : IRequester<ImageResult<YuvDrawable>> {
    override fun request(): ImageResult<YuvDrawable>? {
        val options = BitmapFactory.Options().apply {
            inSampleSize = 0
        }
        runCatching {
            val start = System.currentTimeMillis()
            val yuvData = CodecHelper.getDecodeThumbnailStream(resourceKey.filePath, resourceKey.mediaId.toString())?.use { fin ->
                YuvImageDecoder().decode(fin, options, true)
            } ?: let {
                GLog.e(TAG, LogFlag.DL) {
                    "[request] open stream failed. mediaId=${resourceKey.mediaId}, filePath=${PathMask.mask(resourceKey.filePath)}]"
                }
                return null
            }
            GLog.d(TAG, LogFlag.DL) { "[request] decodeYuvImage: mediaId=${resourceKey.mediaId} consume=${System.currentTimeMillis() - start}" }
            return ImageResult(YuvDrawable(yuvData))
        }.onFailure {
            GLog.e(TAG, LogFlag.DF, it) {
                "[request] YuvImage decode failed. mediaId=${resourceKey.mediaId}, filePath=${PathMask.mask(resourceKey.filePath)}"
            }
        }
        return null
    }

    companion object {
        private const val TAG = "YuvDrawableRequester"
    }
}