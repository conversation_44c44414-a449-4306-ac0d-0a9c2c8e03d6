/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MtpLargeImageRequest.kt
 ** Description : MTP图片资源获取区域图片解码请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** JinPeng@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/
package com.oplus.gallery.business_lib.cache.imagerequest

import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.business_lib.model.data.base.item.MtpItem
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.foundation.util.debug.GLog

class MtpLargeImageRequest(private val mtpImage: MtpItem) : Job<IRegionDecoder> {

    override fun call(jc: JobContext): IRegionDecoder? {
        runCatching {
            mtpImage.imageData?.let { bytes ->
                if (bytes.isNotEmpty()) {
                    return DecoderWrapper.getIRegionDecoder(bytes, 0, bytes.size).apply {
                        setTombstone(BreakpadTombstone(mtpImage.filePath, mtpImage.dateModifiedInSec))
                    }
                }
            }
        }.onFailure {
            GLog.w(TAG, "Region decode fail e=${it.message}")
        }
        return null
    }

    companion object {
        const val TAG = "MtpLargeImageRequest"
    }
}