/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SharedThumbnailBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/12/09
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>          <date>      <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhongxuechang       2022/12/09   1.0               create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.graphics.Bitmap
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.key.SharedThumbnailResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 * SharedThumbnailImage，Bitmap资源加载请求的实现类。
 */
internal class SharedThumbnailBitmapRequester(
    private val resourceKey: SharedThumbnailResourceKey,
    private val options: ResourceGetOptions,
    private val abilityBus: IAbilityBus
) : IBitmapRequester {

    override fun request(): ImageResult<Bitmap>? {
        if (resourceKey.mediaItem.fileId.isNotEmpty()) {
            return abilityBus.requireAbility(ICachingAbility::class.java)?.use {
                it.thumbnailCache?.getBitmap(
                    CacheKeyFactory.createSharedMediaCacheKey(resourceKey.mediaItem.fileId),
                    CacheOptions(inThumbnailType = options.inThumbnailType)
                )?.let { ImageResult(it) }
            }
        }
        return null
    }
}