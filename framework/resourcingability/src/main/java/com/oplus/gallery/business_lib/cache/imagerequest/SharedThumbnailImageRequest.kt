/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SharedThumbnailImageRequest.kt
 ** Description:共享图集缩图bitmap加载类
 **
 ** Version: 1.0
 ** Date: 2022/12/05
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>     <version >      <desc>
 ** ------------------------------------------------------------------------------
 **     zhongxuechang       2022/12/05    1.0             create
 ******************************************************************************/
package com.oplus.gallery.business_lib.cache.imagerequest

import android.graphics.BitmapFactory
import com.oplus.gallery.business_lib.cache.CacheType
import com.oplus.gallery.business_lib.cache.ImageCacheKey
import com.oplus.gallery.business_lib.cache.diskcache.DiskCacheManagerService
import com.oplus.gallery.business_lib.model.data.base.item.SharedThumbnailImage
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BytesBufferPool
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext

/**
 * 共享图集封面缩图加载
 */
class SharedThumbnailImageRequest(
    options: ResourceGetOptions,
    private val mediaItem: SharedThumbnailImage
) : Job<ImageData> {
    private val inThumbnailType = options.inThumbnailType

    override fun call(jc: JobContext): ImageData? {
        val cacheKey = ImageCacheKey(
            mediaItem.path,
            CacheType.IMAGE_BLOB_CACHE,
            ThumbnailSizeUtils.TYPE_THUMBNAIL,
            0
        )
        val buffer = BytesBufferPool.get(0)
        try {
            // 这个包下都无法使用能力层接口，只能直接调用，后续一起整改
            if (DiskCacheManagerService.getImageData(cacheKey, buffer)) {
                return CodecHelper.decodeThumbnailArray(
                    jc,
                    buffer.data,
                    buffer.offset,
                    buffer.length,
                    BitmapFactory.Options().apply { inPreferredConfig = DecodeUtils.DECODE_CONFIG },
                    ThumbnailSizeUtils.getTargetSizeInType(inThumbnailType),
                    inThumbnailType
                )?.let { ImageData(it) }
            }
            return null
        } finally {
            BytesBufferPool.recycle(buffer)
        }
    }
}