/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MtpImageRegionDecoder.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.framework.abilities.resourcing.key.MtpResourceKey
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.foundation.util.debug.GLog

/**
 *  MtpImage，IRegionDecoder资源加载实现类
 */
internal class MtpImageRegionDecoder(private val mtpResource: MtpResourceKey) : IRegionDecoderRequester {

    override fun request(): IRegionDecoder? {
        runCatching {
            mtpResource.mtpItem.imageData?.let { bytes ->
                if (bytes.isNotEmpty()) {
                    return DecoderWrapper.getIRegionDecoder(bytes, 0, bytes.size).apply {
                        setTombstone(BreakpadTombstone(mtpResource.mtpItem.filePath, mtpResource.mtpItem.dateModifiedInSec))
                    }
                }
            }
        }.onFailure {
            GLog.w(TAG, "Region decode fail e=${it.message}")
        }
        return null
    }

    companion object {
        const val TAG = "MtpImageRegionDecoder"
    }
}