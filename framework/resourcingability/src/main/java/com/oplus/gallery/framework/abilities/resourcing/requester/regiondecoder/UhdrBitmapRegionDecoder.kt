/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : BitmapRegionDecoderCompat.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/4/21 17:49
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/21  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import android.graphics.BitmapFactory.Options
import android.graphics.Canvas
import android.graphics.Rect
import android.net.Uri
import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.HDR_GAIN_MAP_MAX_HEIGHT
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.codec.hdr.HdrExtendRequester
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.convertAlpha8ToRgba8888
import com.oplus.gallery.foundation.util.ext.isGainmapLowQuality
import com.oplus.gallery.foundation.util.ext.toShortString
import com.oplus.gallery.foundation.util.math.MathUtil.roundToInt
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.codec.ImageData

/**
 * 常规Ultra HDR资源区域解码器，支持gainmap的区域解码
 */
class UhdrBitmapRegionDecoder(
    private val contentRegionDecoder: IRegionDecoder,
    private val context: Context,
    private val contentUri: Uri
) : IRegionDecoder {
    private val imageWidth by lazy { contentRegionDecoder.getWidth() }
    private val imageHeight by lazy { contentRegionDecoder.getHeight() }

    private var fullGainBitmap: Bitmap? = null
    private var isDestroyed = false

    override fun createDecoder() {
        contentRegionDecoder.createDecoder()
    }

    override fun destroyDecoder() {
        contentRegionDecoder.destroyDecoder()
        synchronized(this) {
            fullGainBitmap?.recycle()
            fullGainBitmap = null
            isDestroyed = true
        }
    }

    override fun isYuvImage(fd: Int): Boolean {
        return contentRegionDecoder.isYuvImage(fd)
    }

    override fun decodeRegion(rect: Rect, options: Options?, isDirectBuffer: Boolean): ImageData? {
        val inGainBitmap: Bitmap? = if (ApiLevelUtil.isAtLeastAndroidU()) {
            options?.inBitmap?.gainmap?.gainmapContents
        } else null
        val imageData = contentRegionDecoder.decodeRegion(rect, options, isDirectBuffer)?.apply {
            if (ApiLevelUtil.isAtLeastAndroidU()) {
                val bitmap = this.bitmap ?: return@apply
                /**
                 * 由于原生图片解码无法单独配置gainmap的inSampleSize，如果图片尺寸超过gainmap两倍大小，那在同样samplesize下
                 * 就会出现gainmap清晰度过低问题，这时需要清掉并单独解码gainmap
                 */
                if (bitmap.isGainmapLowQuality()) {
                    decodeGainRegion(rect, bitmap.width / 2, bitmap.height / 2, inGainBitmap)?.let { regionGainBitmap ->
                        bitmap.gainmap?.gainmapContents = regionGainBitmap
                    }
                }
            }
        }
        // 如果被销毁，则返回null，避免后面将gainmap错误的图片存入文件缓存
        return if (isDestroyed) null else imageData
    }

    @Synchronized
    private fun decodeGainRegion(
        imageDecodeRegion: Rect,
        desireWidth: Int,
        desireHeight: Int,
        inGainBitmap: Bitmap?
    ): Bitmap? {
        if (isDestroyed) {
            GLog.w(TAG, LogFlag.DL) { "decodeGainRegion. destroyed. skip. decodeRegion=$imageDecodeRegion" }
            return null
        }

        if (imageDecodeRegion.isEmpty || (desireWidth <= 0) || (desireHeight <= 0)) {
            GLog.e(TAG, LogFlag.DL) {
                "decodeGainRegion. Invalid size. decodeRegion=$imageDecodeRegion. desire=${desireWidth}x$desireHeight. Skip."
            }
            return null
        }

        runCatching {
            val decodeStart = System.currentTimeMillis()
            if (fullGainBitmap == null) {
                fullGainBitmap = HdrExtendRequester.requestUhdrDrawable(context, contentUri, HDR_GAIN_MAP_MAX_HEIGHT)?.grayImage
                fullGainBitmap?.takeIf { it.config == Config.ALPHA_8 }?.let { bitmap ->
                    val start = System.currentTimeMillis()
                    fullGainBitmap = bitmap.convertAlpha8ToRgba8888(
                        BitmapPools.getBitmap(bitmap.width, bitmap.height, Config.ARGB_8888)
                    )
                    GLog.w(TAG, LogFlag.DL) { "decodeGainRegion. full gain is alpha8 format. convert to rgba8888. consume=${GLog.getTime(start)}" }
                }
                fullGainBitmap?.let {
                    GLog.d(TAG, LogFlag.DL) { "decodeGainRegion. decode full gain bitmap succeed. ${fullGainBitmap?.toShortString()}" }
                } ?: let {
                    GLog.e(TAG, LogFlag.DL) { "decodeGainRegion. decode full gain bitmap failed. ${fullGainBitmap?.toShortString()}" }
                    return null
                }
            }
            val fullGainBitmap = fullGainBitmap ?: return null
            val scaleW = fullGainBitmap.width * 1f / imageWidth
            val scaleH = fullGainBitmap.height * 1f / imageHeight
            val gainDecodeRegion = Rect(
                (imageDecodeRegion.left * scaleW).roundToInt(),
                (imageDecodeRegion.top * scaleH).roundToInt(),
                (imageDecodeRegion.right * scaleW).roundToInt(),
                (imageDecodeRegion.bottom * scaleH).roundToInt()
            )
            val regionGainBitmap = inGainBitmap
                ?: BitmapPools.getBitmap(desireWidth, desireHeight, Config.ARGB_8888)
                ?: Bitmap.createBitmap(desireWidth, desireHeight, Config.ARGB_8888)

            val canvas = Canvas(regionGainBitmap)
            canvas.drawBitmap(fullGainBitmap, gainDecodeRegion, Rect(0, 0, regionGainBitmap.width, regionGainBitmap.height), null)
            GLog.d(TAG, LogFlag.DL) {
                "decodeGainRegion. success. decodeRegion=$gainDecodeRegion, " +
                    "fullGain#${fullGainBitmap.toShortString()}, regionGainBitmap#${regionGainBitmap.toShortString()}," +
                    "decodeTime=${System.currentTimeMillis() - decodeStart}"
            }
            return regionGainBitmap
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "decodeGainRegion. Failed. $imageDecodeRegion" }
        }
        return null
    }

    override fun getWidth(): Int {
        return contentRegionDecoder.getWidth()
    }

    override fun getHeight(): Int {
        return contentRegionDecoder.getHeight()
    }

    override fun isRecycled(): Boolean {
        return contentRegionDecoder.isRecycled()
    }

    override fun isValid(): Boolean {
        return contentRegionDecoder.isValid()
    }

    override fun getDecoderName(): String {
        return contentRegionDecoder.getDecoderName()
    }

    override fun isSupportMultiRegionDecode(): Boolean {
        return contentRegionDecoder.isSupportMultiRegionDecode()
    }

    override fun setTombstone(tombstone: BreakpadTombstone) {
        contentRegionDecoder.setTombstone(tombstone)
    }

    override fun getTombstone(): BreakpadTombstone? {
        return contentRegionDecoder.getTombstone()
    }

    companion object {
        private const val TAG = "UhdrBitmapRegionDecoder"
    }
}