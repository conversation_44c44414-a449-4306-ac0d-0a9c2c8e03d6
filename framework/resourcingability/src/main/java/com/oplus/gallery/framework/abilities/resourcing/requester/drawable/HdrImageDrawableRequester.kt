/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - HdrImageDrawableRequester.kt
 ** Description: HDR 资源加载实现类
 **
 ** Version: 1.0
 ** Date: 2023/07/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     caimengting       2023/07/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.drawable

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import com.oplus.gallery.addon.graphics.toByteArray
import com.oplus.gallery.addon.graphics.toUltraHdrInfo
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BytesBufferPool
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.codec.hdr.HdrExtendRequester
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_LOCAL_HDR
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_OPLUS_UHDR
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_ULTRA_HDR
import com.oplus.gallery.foundation.exif.utils.hasFlag
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_PHOTO_PAGE_BITMAP_DEBUG_INFO
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.addDebugText
import com.oplus.gallery.foundation.util.ext.convertAlpha8ToRgba8888
import com.oplus.gallery.foundation.util.ext.toShortString
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.IHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.LHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack

/**
 * HDR 资源加载实现类
 * @param context context
 * @param resourceKey 资源加载请求的Key
 * @param options 相册定义的请求资源时的Option（非BitmapFactory的Options）
 * @param cachingAbility 缓存能力
 */
internal class HdrImageDrawableRequester(
    private val context: Context,
    private val resourceKey: ResourceKey,
    private val options: ResourceGetOptions,
    private val cachingAbility: ICachingAbility?
) : IDrawableRequester {

    override fun request(): ImageResult<Drawable>? {
        return when (resourceKey) {
            is LocalMediaResourceKey -> {
                // 先从缓存中获取hdr信息，再从文件解析
                val hdrDrawable = getHdrInfoFromCache(resourceKey) ?: getHdrInfoFromFile(resourceKey)
                hdrDrawable?.toImageResult()?.apply {
                    addTagToGrayImage(this.result, resourceKey.filePath, resourceKey.tagFlags)
                }
            }

            else -> {
                GLog.d(TAG, DL) { "[request], not support the key." }
                null
            }
        }
    }

    /**
     * 从文件中解析hdr信息
     */
    private fun getHdrInfoFromFile(resourceKey: LocalMediaResourceKey): HdrImageDrawable? {
        val contentUri = resourceKey.contentUri
        if (contentUri == null) {
            GLog.e(TAG, DL) { "[getHdrInfoFromFile], contentUri is null." }
            return null
        }
        // 增益图取内容图一半大小的尺寸
        var maxLength = (ThumbnailSizeUtils.getTargetSizeInType(options.inThumbnailType) / 2).coerceAtMost(ThumbnailSizeUtils.HDR_GAIN_MAP_MAX_HEIGHT)
        // 当未指定最大增益图尺寸时，取最大限制尺寸
        if (maxLength <= 0) {
            maxLength = ThumbnailSizeUtils.HDR_GAIN_MAP_MAX_HEIGHT
        }
        val startTime = System.currentTimeMillis()
        return when {
            resourceKey.mediaItem.tagFlags.hasFlag(EXIF_TAG_LOCAL_HDR) -> {
                HdrExtendRequester.requestLocalHdrDrawable(context, resourceKey.filePath, contentUri, maxLength)
            }

            resourceKey.mediaItem.tagFlags.hasFlag(EXIF_TAG_OPLUS_UHDR) -> {
                HdrExtendRequester.requestOplusUhdrDrawable(context, resourceKey.filePath, contentUri, maxLength)
            }

            resourceKey.mediaItem.tagFlags.hasFlag(EXIF_TAG_ULTRA_HDR) -> HdrExtendRequester.requestUhdrDrawable(context, contentUri, maxLength)

            else -> {
                GLog.d(TAG, DL) { "[getHdrInfoFromFile] LocalMediaResourceKey is other resource" }
                null
            }
        }?.apply {
            saveHdrInfoToCache(resourceKey, grayImage, metadata)
            GLog.d(TAG, DL) { "[getHdrInfoFromFile] cost time=${GLog.getTime(startTime)}" }
        }
    }

    /**
     * 给grayimage增加debug标志
     */
    private fun addTagToGrayImage(drawable: Drawable?, filePath: String, tagFlags: Long) {
        if (DEBUG_PHOTO_PAGE_BITMAP_DEBUG_INFO.not()) return

        val hdrDrawable = (drawable as? HdrImageDrawable) ?: return
        val prefix = if (FastCaptureUtils.isQuickFile(filePath, tagFlags)) "Quick#" else TextUtil.EMPTY_STRING
        val tag: String = when (resourceKey) {
            is LocalMediaResourceKey -> {
                when {
                    resourceKey.mediaItem.tagFlags.hasFlag(EXIF_TAG_LOCAL_HDR) -> "LHDR#${resourceKey.mediaId}"
                    resourceKey.mediaItem.tagFlags.hasFlag(EXIF_TAG_OPLUS_UHDR) -> "OUHDR#${resourceKey.mediaId}"
                    resourceKey.mediaItem.tagFlags.hasFlag(EXIF_TAG_ULTRA_HDR) -> "UHDR#${resourceKey.mediaId}"
                    else -> "QuickUHDR#${resourceKey.mediaId}"
                }
            }

            else -> TextUtil.EMPTY_STRING
        }
        hdrDrawable.grayImage.addDebugText("$prefix$tag#${hdrDrawable.grayImage.width}x${hdrDrawable.grayImage.height}", MathUtils.SIX)
    }

    /**
     * 从缓存中读取hdr信息：增益图 和 提亮信息。
     *
     * @return HdrImageDrawable 如果 增益图 和 提亮信息 都可用，则返回缓存数据信息，否则返回null。
     */
    private fun getHdrInfoFromCache(originalResource: LocalMediaResourceKey): HdrImageDrawable? {
        if (GProperty.SUPPORT_CAMERA_THUMB_SHARE.not()) return null
        val screenNailCache = cachingAbility?.screenNailCache ?: return null

        val startTime = System.currentTimeMillis()

        // 构建 CacheOptions
        val cacheOptions = createCacheOption() ?: return null

        // 获取 gray bitmap
        val grayBmpCacheKey = CacheKeyFactory.createLocalMediaCacheKey(
            // 这里使用 PATH_ITEM_GRAY_IAMGE，和相机共享的缩图信息保持一个key
            path = SourceConstants.Cache.PATH_ITEM_GRAY_IAMGE.getChild(originalResource.mediaId),
            dateModifiedInSec = originalResource.dateModifiedInSecond,
            mimeType = originalResource.mimeType
        )
        val bitmap = screenNailCache.getBitmap(grayBmpCacheKey, cacheOptions) ?: return null

        // 获取 hdr info
        val hdrInfoCacheKey = CacheKeyFactory.createLocalMediaCacheKey(
            path = SourceConstants.Cache.PATH_ITEM_GRAYINFO_IAMGE.getChild(originalResource.mediaId),
            dateModifiedInSec = originalResource.dateModifiedInSecond,
            mimeType = originalResource.mimeType
        )
        val buffer = BytesBufferPool.get(0)
        screenNailCache.getData(hdrInfoCacheKey, cacheOptions, buffer)
        if (buffer.data.isEmpty()) return null

        // 数据打包成 HdrImageDrawable
        val tagFlags = originalResource.mediaItem.tagFlags
        val hdrImageDrawable = when {
            tagFlags.hasFlag(EXIF_TAG_LOCAL_HDR) -> {
                val content = BytesBufferPool.get(buffer.length)
                buffer.data.copyInto(content.data, 0, buffer.offset, buffer.length)
                HdrImageDrawable(bitmap, LHdrMetadataPack(content.data)).also {
                    if (DEBUG) GLog.d(TAG, DL) { "[getHdrInfoFromCache] lHdr grayImage:${bitmap.toShortString()}" }
                }
            }

            tagFlags.hasFlag(EXIF_TAG_OPLUS_UHDR) || tagFlags.hasFlag(EXIF_TAG_ULTRA_HDR) -> {
                buffer.data.toUltraHdrInfo(buffer.offset, buffer.length)?.let { uHdrInfo ->
                    HdrImageDrawable(bitmap, UHdrMetadataPack(uHdrInfo)).also {
                        if (DEBUG) {
                            GLog.d(TAG, DL) { "[getHdrInfoFromCache] uHdr grayImage:${bitmap.toShortString()} hdrInfo:${uHdrInfo.toDetailString()}" }
                        }
                    }
                }
            }

            else -> {
                GLog.d(TAG, DL) { "[getHdrInfoFromCache] is other resourceKey. tagFlags:$tagFlags" }
                null
            }
        }

        BytesBufferPool.recycle(buffer)

        GLog.d(TAG, DL) { "[getHdrInfoFromCache] hdr hit cache!! cost time=${(System.currentTimeMillis() - startTime)}" }
        return hdrImageDrawable
    }

    /**
     * 保存hdr信息到缓存中：包含 增益图 和 提亮信息。
     */
    private fun saveHdrInfoToCache(originalResource: LocalMediaResourceKey, grayImage: Bitmap, metadata: IHdrMetadataPack) {
        if (GProperty.SUPPORT_CAMERA_THUMB_SHARE.not()) return
        val screenNailCache = cachingAbility?.screenNailCache ?: return

        val startTime = System.currentTimeMillis()

        // 构建 CacheOptions
        val cacheOptions = createCacheOption() ?: return

        // metadata 转换成 ByteArray
        val tagFlags = originalResource.mediaItem.tagFlags
        val bytesBuffer = when {
            tagFlags.hasFlag(EXIF_TAG_LOCAL_HDR) -> {
                (metadata as? LHdrMetadataPack)?.metadata?.let {
                    BytesBufferPool.get(it.size)
                }
            }

            tagFlags.hasFlag(EXIF_TAG_OPLUS_UHDR) || tagFlags.hasFlag(EXIF_TAG_ULTRA_HDR) -> {
                (metadata as? UHdrMetadataPack)?.metadata?.toByteArray()?.let {
                    BytesBufferPool.get(it.size).apply { data = it }
                }
            }

            else -> null
        } ?: let {
            GLog.d(TAG, DL) { "[saveHdrInfoToCache] failure. convert failed, cost time=${GLog.getTime(startTime)}" }
            return
        }

        // 保存 gray bitmap
        val grayBmpCacheKey = CacheKeyFactory.createLocalMediaCacheKey(
            path = SourceConstants.Cache.PATH_ITEM_GRAY_IAMGE.getChild(originalResource.mediaId),
            dateModifiedInSec = originalResource.dateModifiedInSecond,
            mimeType = originalResource.mimeType
        )
        val grayBmpRgba8888 = if (grayImage.config == Bitmap.Config.ALPHA_8) {
            grayImage.convertAlpha8ToRgba8888()
        } else {
            grayImage
        }
        screenNailCache.putBitmap(grayBmpCacheKey, grayBmpRgba8888, cacheOptions, null)

        // 保存 hdr info
        val hdrInfoCacheKey = CacheKeyFactory.createLocalMediaCacheKey(
            path = SourceConstants.Cache.PATH_ITEM_GRAYINFO_IAMGE.getChild(originalResource.mediaId),
            dateModifiedInSec = originalResource.dateModifiedInSecond,
            mimeType = originalResource.mimeType
        )
        screenNailCache.putData(hdrInfoCacheKey, cacheOptions, bytesBuffer, null)

        GLog.d(TAG, DL) { "[saveHdrInfoToCache] cached success. cost time=${GLog.getTime(startTime)}" }
    }

    private fun createCacheOption(): CacheOptions? {
        val inStorageType = when {
            (CacheOperation.ReadAllCache in options.inCacheOperation) -> StorageType.MEMORY_AND_DISK
            (CacheOperation.ReadMemCache in options.inCacheOperation) -> StorageType.MEMORY_ONLY
            (CacheOperation.ReadDiskCache in options.inCacheOperation) -> StorageType.DISK_ONLY
            else -> return null
        }
        return CacheOptions(
            // 这里使用 THUMB_SIZE_1440，和相机共享的缩图信息保持一个key
            inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(ThumbnailSizeUtils.THUMB_SIZE_1440),
            inStorageQuality = options.inStorageQuality,
            inStorageType = inStorageType
        )
    }

    companion object {
        private const val TAG = "HdrImageDrawableRequester"

        private val DEBUG by lazy { GProperty.BITMAP_REQUESTER }

        /**
         * HdrImageDrawable转换为ImageResult<Drawable>, 可null
         */
        private fun HdrImageDrawable?.toImageResult(): ImageResult<Drawable>? = this?.let { ImageResult(this) }
    }
}