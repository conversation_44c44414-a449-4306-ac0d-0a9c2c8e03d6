/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : UriVideoRequest.kt
 ** Description : Uri视频资源获取缓存缩图请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON>eng@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions

class UriVideoRequest(
    context: Context,
    session: WorkerSession,
    path: Path,
    uri: Uri,
    options: ResourceGetOptions,
    contentType: String,
    uriItem: BaseUriItem
) : UriCacheRequest(context, session, uri, options, contentType, uriItem), Job<ImageData> {

    override fun call(jc: JobContext): ImageData? {
        return getBitmap(jc)
    }

    override fun updateContentIfNeeded() {
        if (uriItem.mAlwaysReload) {
            processFile(context, uri)
        }
    }

    private fun processFile(context: Context, uri: Uri) {
        GLog.d(TAG, "processFile uri: $uri")
        MediaMetadataRetriever().let { mediaRetriever ->
            try {
                mediaRetriever.setDataSource(context, uri)

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.let {
                    if (it.isNotEmpty()) uriItem.width = parseSubstring(it, start = 0, defValue = 0)
                }

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.let {
                    if (it.isNotEmpty()) uriItem.height = parseSubstring(it, start = 0, defValue = 0)
                }

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.let {
                    if (it.isNotEmpty()) uriItem.rotation = parseSubstring(it, start = 0, defValue = 0)
                }

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE)?.let {
                    if (it.isNotEmpty()) contentType = it
                }

                GLog.d(TAG, "processFile width=${uriItem.width}, height=${uriItem.height}," +
                    "rotation=${uriItem.rotation} contentType=$contentType")
            } catch (e: Exception) {
                GLog.e(TAG, "processFile, process failed!", e)
            } finally {
                mediaRetriever.release()
            }
        }
    }

    override fun updateThumbnailTargetSize(): Int = ThumbnailSizeUtils.getTargetSizeInType(inThumbnailType)

    override fun decodeThumbnailFromFile(jc: JobContext, targetSize: Int): ImageData? =
            (DecodeUtils.decodeVideoThumbnail(pfd, false))?.let { ImageData(it) }

    companion object {
        const val TAG = "UriVideoRequest"
        private const val NUMBER_TEN = 10

        private fun parseSubstring(str: String, start: Int, defValue: Int): Int {
            var startIndex = start
            if (TextUtils.isEmpty(str) ||
                (startIndex == str.length)) {
                return defValue
            }
            var ch = str[startIndex++]
            // return defaultValue if we have no integer at all
            if (ch < '0' || ch > '9') return defValue

            var result = ch - '0'
            while (startIndex < str.length) {
                ch = str[startIndex++]
                if (ch < '0' || ch > '9') {
                    return result
                }
                result = result * NUMBER_TEN + (ch - '0')
            }
            return result
        }
    }
}