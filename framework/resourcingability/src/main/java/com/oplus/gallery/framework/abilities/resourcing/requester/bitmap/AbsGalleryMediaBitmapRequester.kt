/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsGalleryMediaBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.Rect
import android.graphics.RectF
import android.net.Uri
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.annotation.VisibleForTesting
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cache.diskcache.SDCardMediaCacheManager
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getCropRatio
import com.oplus.gallery.business_lib.model.data.base.item.getThumbnailRotation
import com.oplus.gallery.business_lib.model.data.base.source.DataSourceType
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_LOSS_LESS_CACHE
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.getTargetSizeInType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.isShortestSideThumbnail
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.cvimageprocess.autocrop.CropRatio
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_TO
import com.oplus.gallery.foundation.dbaccess.convert.ContentValuesConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.effect.BlurHelper
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.ui.autocrop.CropRect
import com.oplus.gallery.foundation.ui.autocrop.CropRectSet
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.clearGainmap
import com.oplus.gallery.foundation.util.ext.convertAlpha8ToRgba8888
import com.oplus.gallery.foundation.util.ext.toShortString
import com.oplus.gallery.foundation.util.ext.getGainmapSafely
import com.oplus.gallery.foundation.util.ext.hasGainmapCompat
import com.oplus.gallery.foundation.util.ext.isGainmapLowQuality
import com.oplus.gallery.foundation.util.graphic.BitmapCreator
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.CacheOperation.WriteAllCache
import com.oplus.gallery.framework.abilities.caching.CacheOperation.WriteDiskCache
import com.oplus.gallery.framework.abilities.caching.CacheOperation.WriteMemCache
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.crop.isCenterRectCrop
import com.oplus.gallery.framework.abilities.resourcing.crop.isCropContentValid
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.requester.SdCardStateManager
import com.oplus.gallery.framework.abilities.resourcing.requester.drawable.HdrImageDrawableRequester
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.framework.abilities.resourcing.util.ImageLoadingLockManager
import com.oplus.gallery.framework.abilities.resourcing.util.checkBitmapDamage
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.LHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.convertToGainmap

/**
 * LocalImage、LocalVideo Bitmap资源请求的基类。
 */
internal abstract class AbsGalleryMediaBitmapRequester(
    protected val context: Context,
    internal val originalResource: LocalMediaResourceKey,
    internal val options: ResourceGetOptions,
    internal val abilityBus: IAbilityBus,
    internal val cachingAbility: ICachingAbility?,
    internal val colorManagementAbility: IColorManagementAbility?,
    internal val cancelable: ICancelable?,
    private val requesterBySync: Boolean = false
) : IBitmapRequester {
    protected val debug = GProperty.BITMAP_REQUESTER
    private val debugTag = getDebugTag(originalResource.path, options.inThumbnailType)
    private val requestedThumbnailType = options.inThumbnailType
    private val sourceType = DataSourceType.judgeSourceType(originalResource.filePath)
    private val cropOperator = CropOperator(context, originalResource.mediaItem)
    private val screenNailThumbnailType: Int
        get() = ThumbnailSizeUtils.getFullThumbnailKey()
    private val screenNailSize: Int
        get() = getTargetSizeInType(screenNailThumbnailType)

    open fun getTag(): String = TAG

    private val isUHDRSupported: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ULTRA_HDR) ?: false
        } ?: false
    }

    /**
     * 是否中国区域
     */
    val isRegionCN: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use { configAbility ->
            configAbility.getBooleanConfig(IS_REGION_CN)
        } ?: false
    }

    @Suppress("LongMethod")
    override fun request(): ImageResult<Bitmap>? {
        if ((DEBUG_REQUEST_ID > 0) && (originalResource.mediaId != DEBUG_REQUEST_ID)) {
            return null
        }

        if (debug) GLog.d(getTag(), LogFlag.DL) { "[request] start. $originalResource $options" }

        //第一步，从缓存中查找
        var resultBitmap = if (DEBUG_CLOSE_CACHE) null else getBitmapFromCache()
        if (cancelable?.isCancelled() == true) {
            if (debug) GLog.d(getTag(), LogFlag.DL) { "[request] end. request is cancelled" }
            return null
        }

        var shouldRecycleBitmap = true
        if (resultBitmap == null) {
            //从资源文件中加载图片的Block块方法
            val sourceBlock = {
                val start = System.currentTimeMillis()
                resultBitmap = requestSource()
                if (debug) {
                    GLog.d(getTag(), LogFlag.DL) { "[request] sourceBlock ${resultBitmap?.toShortString()}" }
                    GLog.d(getTag(), LogFlag.DL) { "[request] cache invalid, total=${GLog.getTime(start)}ms, $options" }
                }
                // resultBitmap 是新 decode 出来的，会存到缓存里面，不要回收 recycle
                shouldRecycleBitmap = false
                true
            }
            //从缓存中加载图片的Block块方法
            val cacheBlock = {
                val start = System.currentTimeMillis()
                //缓存中不存在，且已经有其他线程正在从原图中decode资源，await后重新从缓存中获取一次
                resultBitmap = getBitmapFromCache()
                //保存到缓存操作是异步操作，从原图中获取并保存到缓存中有可能还没完成，不要马上回收
                shouldRecycleBitmap = false
                if (debug) {
                    GLog.d(getTag(), LogFlag.DL) { "[request] cacheBlock ${resultBitmap?.toShortString()}" }
                    GLog.d(getTag(), LogFlag.DL) {
                        "[request] $originalResource cache invalid, get bitmap from cache after await. total=${GLog.getTime(start)}ms, $options"
                    }
                }
                //bug id:7908132 兜底，缓存中还不存在，再从原文件加载并保存到缓存
                if (resultBitmap == null) {
                    GLog.w(getTag(), LogFlag.DL) { "[request] get bitmap from cache fail" }
                    false
                } else {
                    true
                }
            }
            // 相同尺寸图片加载是否需要添加同步操作，仅可写磁盘的任务支持同步操作，否则阻塞等待的任务也取不到前面任务的磁盘缓存
            if (requesterBySync && (WriteDiskCache in options.inCacheOperation)) {
                getCacheKey()?.let {
                    ImageLoadingLockManager.loadingMediaBitmap(it, cacheBlock, sourceBlock)
                } ?: let {
                    GLog.w(getTag(), LogFlag.DL) { "[request] getMediaCacheKey is null, originalResource or ICachingAbility may occurred error" }
                }
            } else {
                sourceBlock()
            }
        } else {
            if (debug) {
                GLog.d(getTag(), LogFlag.DL) { "[request] hit cache" }
            }
        }

        // 从缓存或磁盘中解出的图片，需要再检查下gainmap，避免系统升级前或apk升级前磁盘缓存中的缩图没有gainmap，后面升级后一直没有gainmap
        if (ApiLevelUtil.isAtLeastAndroidU()) {
            resultBitmap?.prepareGainmap()
        }

        resultBitmap = resultBitmap?.let { blurBitmapIfNeeded(bitmap = it, recycle = shouldRecycleBitmap) }

        // 当前只有精选年月日需要裁剪再返回
        resultBitmap?.let {
            if (ThumbnailSizeUtils.isExtendThumbnailKey(requestedThumbnailType)) {
                resultBitmap = cropOperator.cropBitmap(it, requestedThumbnailType, options)
            }
        }

        //色域转换
        resultBitmap?.let {
            if (debug) {
                GLog.d(getTag(), LogFlag.DL) { "[request] adjustColorSpace ${resultBitmap?.toShortString()}" }
            }
            runCatching {
                adjustColorSpace(it)
            }.onFailure {
                GLog.e(getTag(), LogFlag.DL, it) { "[request] failed to adjustColorSpace, msg=$it" }
            }
        }

        if (debug) GLog.d(getTag(), LogFlag.DL) { "[request] end. ${resultBitmap?.toShortString()}" }
        return resultBitmap?.let { ImageResult(it, lowResolution = options.outLowResolution) }
    }

    /**
     * 获取此MediaItem对应的CacheKey，用于锁同步
     */
    private fun getCacheKey(): Long? {
        return cachingAbility?.let { cacheAbility ->
            val cacheKey = CacheKeyFactory.createCacheKey(originalResource, CropParams.noCrop()) ?: return null
            val inStorageType = when {
                (CacheOperation.ReadAllCache in options.inCacheOperation) -> StorageType.MEMORY_AND_DISK
                (CacheOperation.ReadMemCache in options.inCacheOperation) -> StorageType.MEMORY_ONLY
                (CacheOperation.ReadDiskCache in options.inCacheOperation) -> StorageType.DISK_ONLY
                else -> return null
            }
            val cacheOptions = CacheOptions(
                inThumbnailType = options.inThumbnailType,
                inStorageQuality = options.inStorageQuality,
                inStorageType = inStorageType
            )
            cacheAbility.screenNailCache?.getCacheKey(cacheKey, cacheOptions)
        }
    }

    /**
     * 当没有有效缓存时，请求相应的来源获取Bitmap
     */
    protected abstract fun requestSource(): Bitmap?

    /**
     * 解码原文件（这里的原文件是相对于缓存说的）
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    abstract fun onDecodeOriginal(
        decodeOption: DecodeOption,
        type: Int,
        options: ResourceGetOptions,
        cancelable: ICancelable?
    ): Bitmap?

    /**
     * 解码本地文件
     * 解码-resize-保存缓存
     */
    protected fun decodeLocalFile(
        decodeOption: DecodeOption,
        shouldSaveToCache: Boolean = true
    ): Bitmap? {
        // 进行Decode操作
        val decodeResult = GTrace.trace("${getTag()}.decodeOriginal") {
            decodeOriginal(decodeOption, requestedThumbnailType)
        }
        if (decodeResult == null) {
            GLog.e(getTag(), LogFlag.DL) { "[decodeLocalFile] $originalResource, decodeOriginal failed bimap is null" }
            return null
        }
        // 检查解码后的图片有没有报损坏的异常，损耗了就不要保存到缓存
        val saveBitmap = shouldSaveToCache && !checkBitmapDamage(decodeResult.bitmap, decodeOption.filePath)

        if (ApiLevelUtil.isAtLeastAndroidU()) {
            decodeResult.bitmap.prepareGainmapBeforeSaveCache()
        }

        // Resize，会得到多个resize结果。
        val resizedList = if (options.inSkipResize) {
            listOf(ResizedBitmapInfo(decodeResult.bitmap, requestedThumbnailType, CropParams.noCrop(), true))
        } else {
            GTrace.trace("resizeBitmap") {
                resizeBitmap(decodeOption, decodeResult, requestedThumbnailType)
            }
        }
        if (saveBitmap) {
            // Save into cache
            GTrace.trace("decodeLocalFile.saveBitmapIntoCache") {
                saveBitmapIntoCache(resizedList, DataSourceType.judgeSourceType(decodeOption.filePath))
            }
        }

        if (cancelable?.isCancelled() == true) {
            GLog.d(getTag(), LogFlag.DL) { "[decodeLocalFile] cancelable isCancelled " }
            return null
        }

        // 裁剪
        return resizedList.firstOrNull { it.isRequested && !it.isRecycled() }?.let { cropBitmap(it.bitmap, requestedThumbnailType)?.bitmap }
    }

    /**
     * 在存入磁盘缓存前，为bitmap准备正确增益信息
     */
    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun Bitmap.prepareGainmapBeforeSaveCache() {
        /**
         * 由于原生图片解码无法单独配置gainmap的inSampleSize，如果图片尺寸超过gainmap两倍大小，那在同样samplesize下
         * 就会出现gainmap清晰度过低问题，这时需要清掉后面重新单独解码gainmap
         */
        if (isGainmapLowQuality()) {
            GLog.w(TAG, LogFlag.DL) { "prepareGainmapBeforeSaveCache. gainmap is low quality. clear." }
            clearGainmap()
        }

        prepareGainmap()

        getGainmapSafely()?.let {
            if (it.gainmapContents.config == Bitmap.Config.ALPHA_8) {
                it.gainmapContents = it.gainmapContents.convertAlpha8ToRgba8888()
                GLog.d(TAG, LogFlag.DL) { "prepareGainmapBeforeSaveCache. convert gainmap from alpha8 to rgba8888" }
            }
        }
    }

    /**
     * 为bitmap准备hdr增益信息gainmap，用于hdr渲染
     */
    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun Bitmap.prepareGainmap() {
            val isGainmapSupport = isUHDRSupported && ThumbnailSizeUtils.isSupportThumbnailGainmap(requestedThumbnailType)
            if (isGainmapSupport.not()) {
                // 不支持UHDR机型或版本，不支持hdr的缩图类型，删除缓存中的gainmap，避免后处理(resize等操作)耗时
                gainmap = null
            } else if (hasGainmapCompat().not()) {
                // 支持UHDR机型，如果为localhdr资源，或者heif资源，无法直接解出gainmap，需要调用oplus接口来解码gainmap
                val hdrDrawable = GTrace.trace("Decode Gainmap") {
                    HdrImageDrawableRequester(context, originalResource, options, cachingAbility).request()?.result as? HdrImageDrawable
                }
                gainmap = if (hdrDrawable?.metadata is LHdrMetadataPack) {
                    GTrace.trace("Convert LHDR to UHDR") {
                        hdrDrawable.convertToGainmap(this)
                }
            } else {
                hdrDrawable?.toGainmap()
            }
        }
    }

    private fun canDecodeOriginal(requestedType: Int, sourceType: Int): Boolean {
        val isSdcardStateReady = SdCardStateManager.getSdCardState().isNullOrEmpty()
        val isMediaMounted = SdCardStateManager.isMediaMounted()
        val isNotMicroThumbnailType = !ThumbnailSizeUtils.isMicroThumbnailKey(requestedType)
        val isSourceNotFromSdcard = (sourceType != DataSourceType.TYPE_SDCARD)
        return (isSdcardStateReady || isMediaMounted || isNotMicroThumbnailType || isSourceNotFromSdcard)
    }

    private fun blurBitmapIfNeeded(bitmap: Bitmap, recycle: Boolean): Bitmap {
        val blurRadius = options.inBlurRadius
        if (blurRadius <= 0) {
            return bitmap
        }
        return BlurHelper.getInstance(context).createBlurBitmap(bitmap, blurRadius).apply { if (recycle) bitmap.recycle() }
    }

    private fun getDebugTag(path: Path?, type: Int): String {
        var debugTag = path.toString()
        debugTag += when {
            type == ThumbnailSizeUtils.TYPE_THUMBNAIL -> ",THUMBNAIL"
            ThumbnailSizeUtils.isFullThumbnailKey(type) -> ",FULL_THUMBNAIL"
            ThumbnailSizeUtils.isMicroThumbnailKey(type) -> ",MICRO_THUMBNAIL"
            ThumbnailSizeUtils.isExtendThumbnailKey(type) -> ",EXTEND_THUMBNAIL"
            type == ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL -> ",LARGE_THUMBNAIL"
            else -> ",$type"
        }
        return debugTag
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    open fun getBitmapFromCache(): Bitmap? {
        cachingAbility ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getBitmapFromCache] cacheAbility is null" }
            return null
        }
        val cacheBitmap = getBitmapFromGalleryThumbInfo(cachingAbility)
            ?: getBitmapFromCameraThumbInfo(cachingAbility)?.apply {
                val decodeOption = DecodeOption(originalResource)
                val resizedList = listOf(ResizedBitmapInfo(this, requestedThumbnailType, CropParams.noCrop(), true))
                GTrace.trace("saveBitmapIntoCache") {
                    saveBitmapIntoCache(resizedList, DataSourceType.judgeSourceType(decodeOption.filePath))
                }
            }
            ?: return null
        return cropBitmap(cacheBitmap, requestedThumbnailType)?.takeIf { it.isRequested && !it.isRecycled() }?.bitmap
    }

    /**
     * 尝试从相册自身保存的缓存中获取
     */
    private fun getBitmapFromGalleryThumbInfo(cacheAbility: ICachingAbility): Bitmap? {
        val cacheKey = CacheKeyFactory.createCacheKey(originalResource, CropParams.noCrop()) ?: return null
        val startTime = System.currentTimeMillis()
        val inStorageType = when {
            (CacheOperation.ReadAllCache in options.inCacheOperation) -> StorageType.MEMORY_AND_DISK
            (CacheOperation.ReadMemCache in options.inCacheOperation) -> StorageType.MEMORY_ONLY
            (CacheOperation.ReadDiskCache in options.inCacheOperation) -> StorageType.DISK_ONLY
            else -> return null
        }
        val cacheOptions = CacheOptions(
            inThumbnailType = options.inThumbnailType,
            inStorageQuality = options.inStorageQuality,
            inStorageType = inStorageType
        )
        return runCatching {
            if (isUseScreenNailCache(requestedThumbnailType)) {
                cacheAbility.screenNailCache?.getBitmap(cacheKey, cacheOptions)
            } else {
                cacheAbility.thumbnailCache?.getBitmap(cacheKey, cacheOptions)
            }
        }.onFailure {
            GLog.e(getTag(), LogFlag.DL, it) { "[getBitmapFromGalleryThumbInfo] error: $it" }
        }.getOrNull().also {
            GLog.d(getTag(), LogFlag.DL) {
                "[getBitmapFromGalleryThumbInfo] cost time=${GLog.getTime(startTime)}" +
                        ", cacheKey:$cacheKey cacheOptions:$cacheOptions ${options.inCacheOperation} ${it?.toShortString()}"
            }
        }
    }

    /**
     * 尝试从相机保存的缓存中获取
     *
     * 相机拍照时共享缓存，通过OplusPreTileDecodeService保存到相册的cache中。
     */
    protected open fun getBitmapFromCameraThumbInfo(cacheAbility: ICachingAbility): Bitmap? = null

    /**
     * 判断是否使用ScreenNail缓存。
     *
     * 以下视为非ScreenNail：
     * 小缩图/视频截帧小缩图/扩展缩图。
     * @param type 缩图类型
     * @return Boolean 结果
     */
    private fun isUseScreenNailCache(type: Int): Boolean {
        return ThumbnailSizeUtils.isMicroThumbnailKey(type).not() &&
                ThumbnailSizeUtils.isVideoMicroThumbnailKey(type).not() &&
                ThumbnailSizeUtils.isExtendThumbnailKey(type).not()
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun decodeOriginal(decodeOption: DecodeOption, requestedType: Int): DecodedBitmapInfo? {
        if (!canDecodeOriginal(requestedType, DataSourceType.judgeSourceType(decodeOption.filePath))) {
            GLog.e(getTag(), LogFlag.DL) { "[decodeOriginal] canDecodeOriginal=false, ImageRequest return null" }
            return null
        }

        val decodeBitmap: Bitmap?
        val decodedType: Int
        try {
            OriginDecodeFlowLimiter.acquire(originalResource.width * originalResource.height)
            decodedType = requestedType
            GTrace.traceBegin("${getTag()}.onDecode.${decodeOption.filePath.split("/").last()}")
            decodeBitmap = onDecodeOriginal(decodeOption, decodedType, options, cancelable)
            GTrace.traceEnd()
        } finally {
            OriginDecodeFlowLimiter.release()
        }

        //Mark：需要在此处判断是否为partial decode，若是需要将该参数一同返回
        options.outIsPartialImage = false
        return decodeBitmap?.let { bitmap ->
            DecodedBitmapInfo(bitmap, decodedType, options.outIsPartialImage)
        } ?: let {
            GLog.e(getTag(), LogFlag.DL) { "[decodeOriginal] decodeBitmap is null" }
            null
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun resizeBitmap(
        decodeOption: DecodeOption,
        decodeBitmapInfo: DecodedBitmapInfo,
        requestedType: Int
    ): List<ResizedBitmapInfo> {
        val start = System.currentTimeMillis()
        val targetSize = getTargetSizeInType(requestedType)
        val resultList = ArrayList<ResizedBitmapInfo>()
        var shouldReleaseDecodedBitmap = true
        val bitmap = decodeBitmapInfo.bitmap

        /* 原提交：2018/3/27 hash:8d632f3306f42ad3a0b7a08c35c4ff965ead3da4
           场景：解码heif缩图时，提前解码大图用的缩图缓存起，为了点击查看大图时可以加快显示速度 */
        if (isCacheScreenNail(decodeOption.mimeType, requestedType, decodeBitmapInfo)) {
            BitmapUtils.resizeByLongSide(bitmap, bitmapCreator, screenNailSize, false, false)?.let {
                shouldReleaseDecodedBitmap = (it != bitmap)
                //此处额外插入了一次保存ScreenNail Bitmap的操作, 按照现有的逻辑
                resultList.add(ResizedBitmapInfo(it, screenNailThumbnailType, CropParams.noCrop(), false))
            } ?: GLog.w(getTag(), LogFlag.DL) { "[resizeBitmap] resize bitmap failed, bitmap is null! $debugTag" }
        }

        val requestedBitmap = if (isResizeByShortSide(requestedType)) {
            BitmapUtils.resizeByShortSide(bitmap, bitmapCreator, targetSize, false, false)
        } else {
            BitmapUtils.resizeByLongSide(bitmap, bitmapCreator, targetSize, false, false)
        }
        shouldReleaseDecodedBitmap = shouldReleaseDecodedBitmap && (requestedBitmap != bitmap)
        if (shouldReleaseDecodedBitmap) {
            BitmapPools.recycle(bitmap)
        }
        requestedBitmap?.also {
            resultList.add(ResizedBitmapInfo(it, requestedThumbnailType, CropParams.noCrop(), true))
        }
        if (debug) {
            GLog.d(getTag(), LogFlag.DL) {
                "[resizeBitmap] consume=${GLog.getTime(start)}, recycle=$shouldReleaseDecodedBitmap, " +
                    "${decodeBitmapInfo.bitmap.width}x${decodeBitmapInfo.bitmap.height}->${requestedBitmap.width}x${requestedBitmap.height}"
            }
        }
        return resultList
    }

    /**
     * 是否以短边缩放，若不是则表示以长边缩放
     *
     * 目前只有小缩图/视频截帧小缩图/扩展缩图以短边缩放，其他缩图类型均以长边缩放
     */
    private fun isResizeByShortSide(thumbnailType: Int): Boolean {
        return ThumbnailSizeUtils.isMicroThumbnailKey(thumbnailType) ||
                ThumbnailSizeUtils.isVideoMicroThumbnailKey(thumbnailType) ||
                ThumbnailSizeUtils.isExtendThumbnailKey(thumbnailType)
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun cropBitmap(
        bitmap: Bitmap,
        requestedType: Int
    ): ResizedBitmapInfo? {
        val targetSize = getTargetSizeInType(requestedType)
        var shouldReleaseDecodedBitmap = true

        val cropParams = cropOperator.getCropParams(requestedType, options.inCropParams)
        val requestedBitmap = when {
            //以指定CropRect参数裁剪
            cropParams.isCropContentValid() -> {
                when (val cropContent = cropParams.getCropContent()) {
                    is RectF -> {
                        val cropRect = CropRect.getCropRect(cropContent, bitmap.width, bitmap.height)
                        BitmapUtils.resizeAndCrop(bitmap, targetSize, false, false, cropRect, bitmapCreator)
                    }
                    else -> {
                        GLog.e(getTag(), LogFlag.DL, "[cropBitmap] Not support the crop params types..")
                        null
                    }
                }
            }
            cropParams.isCenterRectCrop() -> {
                //中心矩形裁剪
                BitmapUtils.resizeAndCrop(bitmap, targetSize, true, false, null, bitmapCreator)
            }
            isShortestSideThumbnail(requestedType) -> {
                //不裁剪，当前仅用于GIF合成，业务流程要求即使图片分辨率不足，也必须缩放短边至指定大小
                BitmapUtils.resizeByShortSide(bitmap, bitmapCreator, targetSize, true, false)
            }
            else -> {
                //不裁剪，已缩放过，直接返回
                bitmap
            }
        }
        shouldReleaseDecodedBitmap = shouldReleaseDecodedBitmap && (requestedBitmap != bitmap)
        if (shouldReleaseDecodedBitmap) {
            BitmapPools.recycle(bitmap)
        }
        return requestedBitmap?.let {
            ResizedBitmapInfo(it, requestedThumbnailType, cropParams, true)
        }
    }

    /**
     * 是否可以提前缓存大图缩图
     */
    private fun isCacheScreenNail(mimeType: String?, requestedType: Int, decodeBitmapInfo: DecodedBitmapInfo): Boolean {
        if (decodeBitmapInfo.isPartial) return false
        if (!(ThumbnailSizeUtils.isMicroThumbnailKey(requestedType) || ThumbnailSizeUtils.isExtendThumbnailKey(requestedType))) {
            // 当前如果不是请求列表缩图，无须提前缓存大图
            return false
        }
        // 如果解码用的图大于等于缩图，可用来缩放至大图缩图尺寸
        return ((getTargetSizeInType(decodeBitmapInfo.thumbnailType) >= screenNailSize) && MimeTypeUtils.isHeifOrHeic(mimeType))
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun saveBitmapIntoCache(cachedList: List<ResizedBitmapInfo>, sourceType: Int) {
        val start = System.currentTimeMillis()
        if (options.outLowResolution) {
            GLog.d(getTag(), LogFlag.DL) { "[saveBitmapIntoCache] low resolution, no need save to cache." }
            return
        }

        if (options.outIsPartialImage) {
            GLog.d(getTag(), LogFlag.DL) { "[saveBitmapToCache] Partial image, no need save to cache." }
            return
        }

        if ((WriteDiskCache !in options.inCacheOperation) && (WriteMemCache !in options.inCacheOperation)) {
            GLog.d(getTag(), LogFlag.DL) { "[saveBitmapToCache] cache operation, no need save to cache. ${options.inCacheOperation}" }
            return
        }

        val savingBitmapInfoList = if (WriteMemCache in options.inCacheOperation) {
            // 会有并发风险，提前 copy 一份
            cachedList.map { resizedBitmapInfo ->
                val copyBmp = BitmapPools.copyBitmap(resizedBitmapInfo.bitmap) ?: resizedBitmapInfo.bitmap
                resizedBitmapInfo.copy(bitmap = copyBmp)
            }
        } else {
            cachedList
        }

        saveBitmapIntoCacheSynchronized(savingBitmapInfoList, sourceType)
        if (debug) {
            GLog.d(getTag(), LogFlag.DL) { "[saveBitmapIntoCache] consume=${GLog.getTime(start)}" }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun saveBitmapIntoCacheSynchronized(cachedList: List<ResizedBitmapInfo>, sourceType: Int) {
        cachedList.forEach {
            // 保存到缓存中的 Bitmap 不要主动回收，缓存管理会直接持有，使用时才会拷贝副本
            saveBitmapToCache(it)

            //Marked by: by zhangwenming 后面支持了options.outIsPartialImage之后，需要删除掉该调用。
            addSdCardCacheInfo(it.thumbnailType, sourceType)
        }
    }

    /**
     * 将Bitmap存入缓存中。
     *
     * 此处原本就是临时实现，为减少内存缓存占用，移除内存缓存，之前加内存缓存，仅仅是锦上添花，无太大影响
     *
     * @param resizedBitmapInfo 解码缩图的信息
     * @return 是否保存成功
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun saveBitmapToCache(resizedBitmapInfo: ResizedBitmapInfo): Boolean {
        cachingAbility?.let { cacheAbility ->
            runCatching {
                colorManagementAbility?.let { colorManagementAbility ->
                    if (colorManagementAbility.isWideColorGamutEnabled == true) {
                        colorManagementAbility.convertBitmapPixels(resizedBitmapInfo.bitmap, ColorSpace.get(ColorSpace.Named.DISPLAY_P3))
                    }
                }
            }.onFailure {
                GLog.e(getTag(), LogFlag.DL, it) { "[saveBitmapToCache] failed to adjustColorSpace, msg=$it" }
                return false
            }

            val storageType = when {
                WriteAllCache in options.inCacheOperation -> StorageType.MEMORY_AND_DISK
                WriteDiskCache in options.inCacheOperation -> StorageType.DISK_ONLY
                WriteMemCache in options.inCacheOperation -> StorageType.MEMORY_ONLY
                else -> return false
            }

            val cacheKey = CacheKeyFactory.createCacheKey(originalResource, CropParams.noCrop()) ?: return false
            val quality = if ((options.inStorageQuality == StorageQuality.NOT_CARE) && isLossLessCache(options.inThumbnailType)) {
                StorageQuality.LOSSLESS
            } else {
                options.inStorageQuality
            }
            val cacheOptions = CacheOptions(
                inThumbnailType = resizedBitmapInfo.thumbnailType,
                inStorageQuality = quality,
                inStorageType = storageType,
            )
            if (debug) {
                GLog.d(getTag(), LogFlag.DL) { "[saveBitmapToCache] cacheKey:$cacheKey cacheOptions:$cacheOptions" }
            }
            val result = if (isUseScreenNailCache(resizedBitmapInfo.thumbnailType)) {
                cacheAbility.screenNailCache?.putBitmap(cacheKey, resizedBitmapInfo.bitmap, cacheOptions)
            } else {
                cacheAbility.thumbnailCache?.putBitmap(cacheKey, resizedBitmapInfo.bitmap, cacheOptions)
            }
            return result?.isSuccess() ?: false
        } ?: let {
            GLog.e(getTag(), LogFlag.DL, "[saveBitmapToCache] Cache ability is null when save cache.")
            return false
        }
    }

    /**
     * 是否无损缓存
     */
    private fun isLossLessCache(thumbnailType: Int): Boolean {
        return ((thumbnailType == ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL) && MimeTypeUtils.isBmp(originalResource.mediaItem.mimeType)) ||
            (originalResource.mediaItem.getSupportFormat(FORMAT_LOSS_LESS_CACHE) == FORMAT_LOSS_LESS_CACHE)
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun addSdCardCacheInfo(decodedType: Int, sourceType: Int) {
        /*
         * 提交hash:78b68d5bcd1ddbbfffed76978ddd64e7dc43bc7b: 2017/9/11 add for bug:1100375
         * 时间轴第一次加载SD卡图片，解码过程中拔卡，解出来的图片bitmap不完整，会有黑块，且已写入blobCache文件缓存中，导致年月视图也存在黑块
         * 解决如下：在解码sd卡的图片且保存到blobCache时，同时记录图片，最多记录最后64张,此时拔卡，则删除blobCache和年月的blockcache对应的图片信息，下次进入则重新解码
         */
        if ((sourceType == DataSourceType.TYPE_SDCARD) &&
            (ThumbnailSizeUtils.isMicroThumbnailKey(decodedType)
                    || ThumbnailSizeUtils.isExtendThumbnailKey(decodedType)
                    || ThumbnailSizeUtils.isVideoMicroThumbnailKey(decodedType))
        ) {
            SDCardMediaCacheManager.addSdCardCacheInfo(originalResource.mediaItem, decodedType)
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun adjustColorSpace(bitmap: Bitmap): Boolean {
        colorManagementAbility?.let {
            return if (it.isWideColorGamutEnabled == false) {
                it.adjustColorSpace(bitmap, ColorSpace.get(ColorSpace.Named.SRGB)) ?: false
            } else {
                return true
            }
        }
        return false
    }

    companion object {
        private const val TAG = "AbsGalleryMediaBitmapRequester"
        /** 是否关闭缩图缓存 */
        private val DEBUG_CLOSE_CACHE by lazy {
            GallerySystemProperties.getBoolean("debug.bitmap.requester.cache.close", false)
        }
        /** 只加载指定的mediaId */
        private val DEBUG_REQUEST_ID by lazy {
            GallerySystemProperties.getInt("debug.bitmap.requester.id", -1)
        }

        /**
         * 当前Version版本是否15及以上
         */
        val isAtLeastOS15: Boolean by lazy { OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0) }

        /**
         * 当前Version版本是否16及以上
         */
        val isAtLeastOS16: Boolean by lazy { OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_16_0_0) }

        val bitmapCreator: BitmapCreator by lazy {
            object : BitmapCreator {
                override fun create(width: Int, height: Int, config: Bitmap.Config): Bitmap? {
                    return BitmapPools.getBitmap(width, height, config)
                }
            }
        }

        /** 多媒体图片缩略图relativePath路径 */
        const val MEDIA_PICTURES_THUMBNAIL_RELATIVE_PATH = "/Pictures/.thumbnails"

        /** 多媒体视频缩略图relativePath路径 */
        const val MEDIA_MOVIES_THUMBNAIL_RELATIVE_PATH = "/Movies/.thumbnails"

        /** jpg */
        const val JPG: String = ".jpg"
    }

    /**
     * 处理裁剪相关
     */
    class CropOperator(private val context: Context, private val localItem: LocalMediaItem) {

        /** 智能裁剪框信息 */
        private val autoCropRectMap = CropRectSet.getCropRectMap(localItem.cropRectSet)

        /**
         * 获取裁剪参数
         */
        fun getCropParams(thumbnailType: Int, inCropParams: CropParams): CropParams {
            if (isSupportCrop(thumbnailType).not()) {
                return CropParams.noCrop()
            }
            return if (inCropParams.isCenterRectCrop()) {
                // 优先使用智能裁剪
                autoCropRectMap[CropRatio.RATIO_1_1]?.getPercentRectF(0)?.let {
                    CropParams.create(it)
                } ?: CropParams.centerRectCrop()
            } else inCropParams
        }

        /**
         * 智能裁剪
         */
        fun cropBitmap(srcBitmap: Bitmap, thumbnailType: Int, options: ResourceGetOptions): Bitmap {
            var recommendRect: Rect? = null
            if (autoCropRectMap.isNotEmpty()) {
                val cropRatio = getAutoCropRatio(thumbnailType, options)
                val cropRect = autoCropRectMap[cropRatio] ?: let {
                    // 还没有扫描刷新存量数据，不能用最新版本库扫，因为如果将该裁剪框保存至数据库并更新版本号，其他裁剪比例的数据可能是旧的
                    if (ApiDmManager.getScanDM().isNewestAutoCropDataVersion(localItem.cropRectVersion) != true) return@let null
                    val startTime = System.currentTimeMillis()
                    val cropRect = ApiDmManager.getScanDM().getCropRects(context, localItem, arrayOf(cropRatio))?.firstOrNull() ?: return@let null
                    // 追加新的裁剪框数据，存入数据库
                    val list = mutableListOf<CropRect>()
                    list.addAll(autoCropRectMap.values)
                    list.add(cropRect)
                    localItem.cropRectSet = CropRectSet(
                        list
                    ).toString()
                    saveCropRectToDB(localItem.cropRectSet)
                    GLog.d(TAG, LogFlag.DL) { "cropBitmap, append crop info, cost:${System.currentTimeMillis() - startTime}" }
                    cropRect
                }
                recommendRect = cropRect?.getRect(srcBitmap.width, srcBitmap.height)
            }
            val time = System.currentTimeMillis()
            var limitSize = getTargetSizeInType(thumbnailType)
            if ((options.targetWidth > 0) && (options.targetHeight > 0)) {
                limitSize = limitSize.coerceAtMost(options.targetWidth.coerceAtMost(options.targetHeight))
                recommendRect = recommendRect ?: getDeaultCropRect(srcBitmap.width, srcBitmap.height, options.targetWidth, options.targetHeight)
            }
            /* 当裁剪区域小于limitSize时，说明分辨率不足，没有必要放大bitmap，不超过limitSize即可 */
            val result = BitmapUtils.resizeAndCrop(srcBitmap, limitSize, false, false, recommendRect, bitmapCreator)
            if (result != srcBitmap) {
                BitmapPools.recycle(srcBitmap)
            }
            if (GProperty.DEBUG) {
                val ratioInfo = recommendRect?.let { if (it.height() > 0) "auto_${it.width() / it.height()}" else "error rect:$it" } ?: "center_1.0"
                GLog.d(TAG, LogFlag.DL) { "cropBitmap, ratio:$ratioInfo, cost:${System.currentTimeMillis() - time}" }
            }
            return result
        }

        /**
         * 是否支持裁剪，目前只有小缩图/视频截帧小缩图支持裁剪
         *
         * 以长边缩放后缓存的缩图类型不支持裁剪（以960缓存960x720为例，若裁剪成720x720再放大成960x960，额外处理增加耗时，bitmap占用内存更大，
         * 但是正方形ImageView居中裁剪显示的清晰度一样）。
         * 扩展缩图虽然是以短边缓存，但是目前没有需要裁剪的场景，所以也视为不支持裁剪。
         */
        private fun isSupportCrop(thumbnailType: Int): Boolean {
            return ThumbnailSizeUtils.isMicroThumbnailKey(thumbnailType) || ThumbnailSizeUtils.isVideoMicroThumbnailKey(thumbnailType)
        }

        /**
         * 获取裁剪比例
         */
        private fun getAutoCropRatio(thumbnailType: Int, options: ResourceGetOptions): CropRatio {
            if (ThumbnailSizeUtils.isMicroThumbnailKey(thumbnailType) ||
                ThumbnailSizeUtils.isVideoMicroThumbnailKey(thumbnailType) ||
                (options.targetWidth <= 0) ||
                (options.targetHeight <= 0)
            ) {
                return CropRatio.RATIO_1_1
            }

            val cropRatio = localItem.getCropRatio(options.targetWidth, options.targetHeight)
            return if ((autoCropRectMap[cropRatio] != null) || GProperty.DEBUG_REAL_TIME_AUTO_CROP) {
                // 已有裁剪数据或者支持实时裁剪，才使用最接近的裁剪类型
                cropRatio
            } else {
                CropRatio.RATIO_1_1
            }
        }

        /**
         * 当没有智能裁剪数据时，默认用目标宽高比生成居中裁剪数据
         */
        private fun getDeaultCropRect(srcWidth: Int, srcHeight: Int, targetWidth: Int, targetHeight: Int): Rect {
            // 对于图像转了90度、270度的，需要宽高对调一下获取裁剪数据
            return if ((localItem.getThumbnailRotation() % AppConstants.Degree.DEGREE_180) == AppConstants.Degree.DEGREE_90) {
                BitmapUtils.calculateCropRect(srcWidth, srcHeight, targetHeight, targetWidth)
            } else {
                BitmapUtils.calculateCropRect(srcWidth, srcHeight, targetWidth, targetHeight)
            }
        }

        /**
         * 将新的裁剪框信息存入数据库
         */
        private fun saveCropRectToDB(cropRectInfo: String) {
            val contentValues = ContentValues()
            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.CROP_RECT, cropRectInfo)
            UpdateReq.Builder()
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setWhere(GalleryStore.GalleryColumns.LocalColumns.DATA + EQUAL_TO)
                .setWhareArgs(arrayOf(localItem.filePath))
                .setConvert(ContentValuesConvert(contentValues))
                .build()
                .exec()
        }
    }

    /**
     * 存储Decode请求参数
     */
    internal data class DecodeOption(
        /**
         * 真实文件路径
         */
        val filePath: String,
        /**
         * 文件uri，当文件不在公共存储路径时为null
         */
        val fileUri: Uri? = null,
        /**
         * 媒体库id，当文件不在公共存储路径时为0
         */
        val mediaId: Int = 0,
        /**
         * mime type，云端下载的小缩图的文件格式可能和原始文件的不同
         */
        val mimeType: String,
        /**
         * 是yuv和大尺寸图bitmap4096
         */
        val isYuvAndLargePage: (Int) -> Boolean = { false }
    ) {
        constructor(localMediaResourceKey: LocalMediaResourceKey) : this(
            localMediaResourceKey.filePath,
            localMediaResourceKey.contentUri,
            localMediaResourceKey.mediaId,
            localMediaResourceKey.mimeType,
            isYuvAndLargePage = {
                localMediaResourceKey.mediaItem.isYuvAndLargePage(it)
            }
        )
    }

    /**
     * 存储Decode之后的Bitmap的信息
     */
    internal data class DecodedBitmapInfo(
        /**
         * Decoded之后的bitmap实例
         */
        val bitmap: Bitmap,
        /**
         * 表示该被Decoded的Bitmap是按照什么尺寸Decoded的。
         */
        val thumbnailType: Int,
        /**
         * 表示该被Decode的Bitmap是否为Partial Image。
         */
        val isPartial: Boolean = false
    )

    /**
     * 表示被Resized的bitmap的信息
     */
    internal data class ResizedBitmapInfo(
        /**
         * 被Resize过后的Bitmap
         */
        val bitmap: Bitmap,
        /**
         * 被Resize过后的bitmap尺寸信息
         */
        val thumbnailType: Int,
        /**
         * 该Bitmap被裁剪的信息
         */
        val cropParams: CropParams,
        /**
         * 该Bitmap是被请求的结果，还是Resize的临时Bitmap。isRequested为true的bitmap会返回给业务端，为false的不会返回给业务端，仅存在于资源加载服务中。
         * 若为该参数为false，需要注意在合适的地方recycle。
         */
        val isRequested: Boolean = false,

        /**
         * 记录当前bitmap对象的色域信息。
         */
        val colorSpace: ColorSpace? = bitmap.colorSpace
    ) {

        fun recycle() = bitmap.recycle()

        fun isRecycled(): Boolean = bitmap.isRecycled
    }
}