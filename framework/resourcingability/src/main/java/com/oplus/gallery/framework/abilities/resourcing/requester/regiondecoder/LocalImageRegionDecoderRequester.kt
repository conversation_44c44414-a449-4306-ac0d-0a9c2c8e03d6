/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalImageRegionDecoderRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import android.content.Context
import android.text.TextUtils
import com.oplus.appability.IAbilityBus
import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.exif.utils.hasFlag
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder

/**
 * LocalImage, IRegionDecoder资源加载实现类。
 */
internal class LocalImageRegionDecoderRequester(
    private val context: Context,
    private val resourceKey: LocalMediaResourceKey,
    private val abilityBus: IAbilityBus
) : IRegionDecoderRequester {

    private val isUHDRSupported: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ULTRA_HDR) ?: false
        } ?: false
    }

    override fun request(): IRegionDecoder? {
        if (resourceKey.loadStatus == LocalMediaItem.LOAD_STATUS_INVALID) {
            GLog.e(TAG, "loadStatus ${resourceKey.loadStatus} not valid.")
            return null
        }

        val filePath = resourceKey.filePath
        if (TextUtils.isEmpty(filePath)) {
            GLog.e(TAG, "filePath is empty")
            return null
        }
        val contentUri = resourceKey.contentUri
        if (contentUri == null) {
            GLog.e(TAG, LogFlag.DL) { "[request], contentUri is null." }
            return null
        }
        runCatching {
            val contentDecoder = DecoderWrapper.getIRegionDecoder(filePath, isUseFd = false)?.apply {
                setTombstone(BreakpadTombstone(filePath, resourceKey.dateModifiedInSecond))
            }
            if (contentDecoder == null) {
                GLog.e(TAG, LogFlag.DL) { "request. Get content decoder failed." }
                return null
            }

            return when {
                // 不支持UHDR机型不用解码gainmap，减少不必要的性能损耗
                isUHDRSupported.not() -> contentDecoder

                resourceKey.mediaItem.tagFlags.hasFlag(OplusExifTag.EXIF_TAG_LOCAL_HDR) -> {
                    LocalHdrBitmapRegionDecoder(contentDecoder, filePath, context, contentUri)
                }

                resourceKey.mediaItem.tagFlags.hasFlag(OplusExifTag.EXIF_TAG_OPLUS_UHDR) -> {
                    OplusUhdrBitmapRegionDecoder(contentDecoder, filePath, context, contentUri)
                }

                else -> UhdrBitmapRegionDecoder(contentDecoder, context, contentUri)
            }
        }.onFailure {
            GLog.e(TAG, "Region decode fail ${it.message}")
        }
        return null
    }

    companion object {
        const val TAG = "LocalImageRegionDecoderRequester"
    }
}