/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LocalVideoRequest.kt
 ** Description : 本地视频资源获取缓存缩图请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON>eng@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.Context
import android.graphics.Bitmap
import android.os.ParcelFileDescriptor
import com.oplus.gallery.framework.abilities.resourcing.util.ThumbnailDecoderHelper
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.videoframe.HardwareDecoderConfig
import com.oplus.gallery.standard_lib.codec.videoframe.MeicamVideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.standard_lib.util.io.IOUtils

class LocalVideoRequest(
    context: Context,
    session: WorkerSession,
    options: ResourceGetOptions,
    private val localVideo: LocalVideo,
    private val hardwareDecoderConfig: HardwareDecoderConfig
) : ImageCacheRequest(context, session, options, localVideo) {
    override fun onDecodeOriginal(jc: JobContext, type: Int): ImageData? {
        if (requestFlags and REQUEST_FLAT_NOT_DECODE_ORIGINAL == REQUEST_FLAT_NOT_DECODE_ORIGINAL) {
            GLog.d(TAG) { "onDecodeOriginal. can't decode original. skip. id=${localVideo.mediaId} requestFlags=$requestFlags" }
            return null
        }

        return GTrace.trace("LocalVideo.origin") {
            val bitmap = decodeVideoThumbnailWithKeyFrame(ThumbnailSizeUtils.getTargetSizeInType(type))
            if ((bitmap == null) || jc.isCancelled()) null else ImageData(bitmap)
        }
    }

    private fun decodeVideoThumbnailWithKeyFrame(size: Int): Bitmap? {
        var tmpParcelFileDescriptor: ParcelFileDescriptor? = null
        try {
            getThumbnailDecoder().use { videoThumbnailDecoder ->
                when {
                    (videoThumbnailDecoder is MeicamVideoThumbnailDecoder) -> {
                        // 美摄不支持fd,需要使用uri
                        videoThumbnailDecoder.setDataSource(context, localVideo.contentUri)
                    }
                    else -> {
                        MediaStoreUriHelper.getVideoUri(localVideo.filePath, localVideo.mediaId.toString()).let { uri ->
                            context.contentResolver.openFileDescriptor(uri, "r")?.let { parcelFileDescriptor ->
                                videoThumbnailDecoder.setDataSource(parcelFileDescriptor.fileDescriptor)
                                tmpParcelFileDescriptor = parcelFileDescriptor
                            }
                        }
                    }
                }
                videoThumbnailDecoder.setThumbnailSize(size)
                return videoThumbnailDecoder.decodeCoverBitmap()?.let {
                    // FIXME JinPeng 2020/10/10
                    DecodeUtils.ensureGLCompatibleBitmap(it)
                }
            }
        } catch (e: Exception) {
            GLog.e(TAG, "decodeVideoThumbnailWithKeyFrame, error", e)
        } finally {
            IOUtils.closeQuietly(tmpParcelFileDescriptor)
        }
        return null
    }

    private fun getThumbnailDecoder(): VideoThumbnailDecoder {
        val decoderConfig =
            ThumbnailDecoderHelper.updateDecoderConfig(
                ThumbnailDecoderHelper.getDecoderConfig(localVideo, hardwareDecoderConfig.isSupportDolby),
                hardwareDecoderConfig
            )
        return VideoThumbnailDecoderUtils.getVideoThumbnailDecoder(decoderConfig)
    }

    companion object {
        const val TAG = "LocalVideoRequest"
    }
}