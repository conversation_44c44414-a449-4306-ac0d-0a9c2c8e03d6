/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TileBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.graphics.Bitmap
import android.graphics.ColorSpace
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.resourcing.key.TileResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 *  Tile区块Bitmap资源请求加载类
 */
internal class TileBitmapRequester(
    private val tileResource: TileResourceKey,
    private val options: ResourceGetOptions,
    private val abilityBus: IAbilityBus
) : IBitmapRequester {

    override fun request(): ImageResult<Bitmap>? {
        GTrace.traceBegin("PreTile-Disk")
        try {
            val bitmap = getBitmapFromCache() ?: return null
            adjustColorSpace(bitmap)
            return bitmap.let { ImageResult(it) }
        } finally {
            GTrace.traceEnd()
        }
    }

    private fun getBitmapFromCache(): Bitmap? {
        CacheKeyFactory.createCacheKey(tileResource)?.let { tileCacheKey ->
            abilityBus.requireAbility(ICachingAbility::class.java)?.use { cacheAbility ->
                return cacheAbility.tileCache?.getBitmap(
                    tileCacheKey,
                    CacheOptions(inThumbnailType = options.inThumbnailType, inStorageQuality = options.inStorageQuality)
                )
            }
        } ?: let {
            GLog.e(TAG, "CacheKey is null when getBitmapFromCache")
            return null
        }
    }

    private fun adjustColorSpace(bitmap: Bitmap) {
        abilityBus.requireAbility(IColorManagementAbility::class.java)?.use {
            if (it.isWideColorGamutEnabled == false) {
                it.adjustColorSpace(bitmap, ColorSpace.get(ColorSpace.Named.SRGB))
            }
        }
    }

    private companion object {
        const val TAG = "TileBitmapRequester"
    }
}