/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ImageCacheRequest.kt
 ** Description : 图片或视频资源获取缓存图片请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.graphics.Rect
import android.media.MediaFormat
import android.os.Bundle
import com.oplus.gallery.foundation.ui.autocrop.CropRect
import com.oplus.gallery.foundation.ui.autocrop.CropRectSet
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cache.ImageCacheKey
import com.oplus.gallery.business_lib.cache.MediaCacheKey
import com.oplus.gallery.business_lib.cache.diskcache.DiskCacheManagerService
import com.oplus.gallery.business_lib.cache.diskcache.SDCardMediaCacheManager
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getCropRatio
import com.oplus.gallery.business_lib.model.data.base.item.getThumbnailRotation
import com.oplus.gallery.business_lib.model.data.base.source.DataSourceType
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_LOSS_LESS_CACHE
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.display.ColorModelManager
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.cache.memorycache.BytesBufferPool
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.TYPE_THUMBNAIL
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.getFullThumbnailKey
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.getTargetSizeInType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.isExtendThumbnailKey
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.isFullThumbnailKey
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.isMicroThumbnailKey
import com.oplus.gallery.foundation.cache.memorycache.BytesBuffer
import com.oplus.gallery.foundation.cvimageprocess.autocrop.CropRatio
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.ConstantUtils
import com.oplus.gallery.foundation.dbaccess.convert.ContentValuesConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.effect.BlurHelper
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.foundation.util.graphic.BitmapCreator
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.OriginDecodeFlowLimiter
import com.oplus.gallery.standard_lib.app.AppConstants

abstract class ImageCacheRequest(
    val context: Context,
    val session: WorkerSession,
    internal val options: ResourceGetOptions,
    protected val localItem: LocalMediaItem,
    private val videoTimeMills: Long = 0,
    var requestFlags: Int = REQUEST_FLAG_NONE
) : Job<ImageData> {
    private val inThumbnailType = options.inThumbnailType
    private val targetSize = getTargetSizeInType(inThumbnailType)
    private val sourceType = DataSourceType.judgeSourceType(localItem.filePath ?: "")
    private val cropOperator = CropOperator(context, localItem)
    private val screenNailThumbnailType: Int
        get() = getFullThumbnailKey()
    private val screenNailSize: Int
        get() = getTargetSizeInType(screenNailThumbnailType)
    private val defaultDebugTag: String by lazy {
        "${localItem.path}${
            when {
                inThumbnailType == TYPE_THUMBNAIL -> ",THUMBNAIL"
                isFullThumbnailKey(inThumbnailType) -> ",FULL_THUMBNAIL"
                isMicroThumbnailKey(inThumbnailType) -> ",MICRO_THUMBNAIL"
                isExtendThumbnailKey(inThumbnailType) -> ",MINOR_THUMBNAIL"
                inThumbnailType == TYPE_LARGE_THUMBNAIL -> ",LARGE_THUMBNAIL"
                else -> ",?"
            }
        }"
    }

    override fun call(jc: JobContext): ImageData? {
        if ((DEBUG_SPECIFY_ID > 0) && (localItem.mediaId != DEBUG_SPECIFY_ID)) {
            return null
        }
        val specifiedAttrs = localItem.getSpecifiedAttributes(
            Bundle().apply {
                putInt(Constants.SpecifiedAttributes.KEY_REQUIRE_SPECIFIED_ATTRS, Constants.SpecifiedAttributes.SPECIFIED_ATTR_FAST_CAPTURE)
            }
        )
        val isFastCapture = specifiedAttrs.getBoolean(Constants.SpecifiedAttributes.RESULT_KEY_IS_FAST_CAPTURE_TEMPORARY)
        var imageData: ImageData? = null
        if (isFastCapture) {
            imageData = loadFromFastCapture(specifiedAttrs)
        } else {
            BytesBufferPool.use(0) { buffer ->
                val found = !DEBUG_CLOSE_CACHE && DiskCacheManagerService.getImageData(createCacheKey(inThumbnailType), buffer)
                if (found) {
                    imageData = loadFromCache(buffer)
                }
                //如果缓存获取失败，解码正常，尝试重新解码避免显示灰图
                if (imageData == null) {
                    imageData = decodeOriginalAndSaveCache(jc, isLossLessCache(inThumbnailType), defaultDebugTag)
                }
                val bitmap = imageData?.bitmap ?: return@use
                if (isExtendThumbnailKey(inThumbnailType)) {
                    // 当前只有精选年月日需要裁剪再返回
                    imageData?.bitmap = cropOperator.cropBitmap(bitmap, inThumbnailType, options)
                }
            }
        }
        return imageData?.apply { bitmap?.let { ColorModelManager.adjustBitmapColorSpaceIfNeeded(it) } }
    }

    private fun loadFromFastCapture(specifiedAttrs: Bundle): ImageData? {
        val isFastCaptureTmp = specifiedAttrs.getBoolean(Constants.SpecifiedAttributes.RESULT_KEY_IS_FAST_CAPTURE_TEMPORARY_TMP)
        val isFastCaptureQuick = specifiedAttrs.getBoolean(Constants.SpecifiedAttributes.RESULT_KEY_IS_FAST_CAPTURE_TEMPORARY_QUICK)
        if (isFastCaptureTmp) {
            // file not exist, to get thumbnail from DiskLruCache where camera store the thumbnail
            val bitmap = DiskCacheManagerService.getBurstCacheData(localItem.name).let {
                when {
                    /*
                     * 快拍后:相机还未生成照片（媒体库查找不到），就进入了相册
                     * 场景：1.相机-连拍-缩图-相册大图 2.相机-连拍，桌面-打开相册时间轴、精选画廊
                     */
                    cropOperator.isAllowCrop(inThumbnailType) -> BitmapUtils.resizeAndCropCenter(it, targetSize, true)
                    ThumbnailSizeUtils.isFullThumbnailKey(inThumbnailType) -> {
                        if (FeatureUtils.isSupportBlurredLoading) {
                            // do scale
                            val bitmap = BitmapUtils.resizeByLongSide(it, CACHE_BITMAP_TARGET_SIZE, true)
                            // do blur
                            BlurHelper.getInstance(context).createBlurBitmap(bitmap, CACHE_BITMAP_BLUR_RADIUS)
                        } else {
                            it
                        }
                    }
                    else -> BitmapUtils.resizeByLongSide(it, targetSize, true)
                }
            }
            if (bitmap == null) {
                GLog.e(TAG, "loadFromFastCapture, getBurstCacheData bitmap is null ")
                return null
            }
            return ImageData(bitmap)
        } else if (isFastCaptureQuick) {
            val bitmap = DiskCacheManagerService.getQuickCacheData(localItem.name, targetSize = targetSize)
            if (bitmap == null) {
                GLog.e(TAG, "loadFromFastCapture, getQuickCacheData bitmap is null ")
                return null
            }
            return ImageData(bitmap)
        }
        return null
    }

    private fun loadFromCache(buffer: BytesBuffer): ImageData? {
        return BitmapPools.decode(buffer)?.let {
            ImageData(it)
        } ?: let {
            GLog.w(TAG) { "loadFromCache failed $defaultDebugTag" }
            null
        }
    }

    /**
     * 是否无损缓存
     */
    private fun isLossLessCache(thumbnailType: Int): Boolean {
        return ((thumbnailType == TYPE_LARGE_THUMBNAIL) && MimeTypeUtils.isBmp(localItem.mimeType)) ||
                (localItem.getSupportFormat(FORMAT_LOSS_LESS_CACHE) == FORMAT_LOSS_LESS_CACHE)
    }

    /**
     * 目前只有杜比视频需要刷新缩图，所以需要此codecType作为key
     */
    private fun getKeyOfCodecType(mediaItem: MediaItem): String? {
        return if (VideoTypeUtils.isDolbyVideo(mediaItem)) {
            MediaFormat.MIMETYPE_VIDEO_DOLBY_VISION
        } else {
            null
        }
    }

    /**
     * 根据给定的任务上下文和图像类型解码原始图像数据
     * @param jc JobContext
     * @param type 图像类型
     * @return 解码后的图像数据
     */
    abstract fun onDecodeOriginal(jc: JobContext, type: Int): ImageData?

    open fun canDecodeOriginal(type: Int, sourceType: Int): Boolean {
        val isSdcardStateReady = SDCardMediaCacheManager.sdcardState.isNullOrEmpty()
        val isMediaMounted = (OplusEnvironment.MEDIA_MOUNTED == SDCardMediaCacheManager.sdcardState)
        val isNotMicroThumbnailType = !isMicroThumbnailKey(type)
        val isSourceNotFromSdcard = (sourceType != DataSourceType.TYPE_SDCARD)

        val canDecodeOriginal = isSdcardStateReady || isMediaMounted || isNotMicroThumbnailType || isSourceNotFromSdcard
        GLog.d(
            TAG,
            "canDecodeOriginal canDecodeOriginal=$canDecodeOriginal, isSdcardStateReady=$isSdcardStateReady, isMediaMounted=$isMediaMounted, " +
                    "isNotMicroThumbnailType=$isNotMicroThumbnailType, isSourceNotFromSdcard=$isSourceNotFromSdcard"
        )
        return canDecodeOriginal
    }

    @Suppress("LongMethod")
    open fun decodeOriginalAndSaveCache(jc: JobContext, losslessCache: Boolean, debugTag: String): ImageData? {
        return if (canDecodeOriginal(inThumbnailType, sourceType)) {
            val judgeType = inThumbnailType
            val time = System.currentTimeMillis()
            val imageData: ImageData?
            try {
                OriginDecodeFlowLimiter.acquire(localItem.width * localItem.height)
                imageData = onDecodeOriginal(jc, inThumbnailType)
            } finally {
                OriginDecodeFlowLimiter.release()
            }
            if (GProperty.DEBUG) {
                GLog.d(
                    TAG, "decodeOriginalAndSaveCache, onDecodeOriginal debugTag={$debugTag}" +
                        ", file:${PathMask.mask(localItem.filePath)}" +
                        ", spend={${GLog.getTime(time)}}ms"
                )
            }
            if (jc.isCancelled() || (imageData == null)) {
                return null
            }
            val originalBitmap = imageData.bitmap ?: let {
                /*
                 * 提交hash:78b68d5bcd1ddbbfffed76978ddd64e7dc43bc7b: 2017/9/11 add for bug:1100375
                 * 时间轴第一次加载SD卡图片，解码过程中拔卡，解出来的图片bitmap不完整，会有黑块，且已写入blobCache文件缓存中，导致年月视图也存在黑块
                 * 解决如下：在解码sd卡的图片且保存到blobCache时，同时记录图片，最多记录最后64张,此时拔卡，
                 * 则删除blobCache和年月的blockcache对应的图片信息，下次进入则重新解码
                 */
                if ((sourceType == DataSourceType.TYPE_SDCARD) && (isMicroThumbnailKey(inThumbnailType) || isExtendThumbnailKey(inThumbnailType))) {
                    SDCardMediaCacheManager.sdcardState = OplusEnvironment.getexternalSdState()
                }
                GLog.w(TAG) { "decodeOriginalAndSaveCache orig failed, bmp is null. debugTag=$debugTag" }
                return null
            }
            val isExtendThumbnailType = isExtendThumbnailKey(inThumbnailType)
            val resultBitmap = if (isMicroThumbnailKey(inThumbnailType) ||
                ThumbnailSizeUtils.isVideoMicroThumbnailKey(inThumbnailType) ||
                isExtendThumbnailType
            ) {
                /* 原提交：2018/3/27 hash:8d632f3306f42ad3a0b7a08c35c4ff965ead3da4
                   场景：解码heif缩图时，提前解码大图用的缩图缓存起，为了点击查看大图时可以加快显示速度 */
                if (isCacheScreenNail(judgeType)) {
                    BitmapUtils.resizeByLongSide(originalBitmap, bitmapCreator, screenNailSize, false, false)?.apply {
                        saveBitmapToCache(jc, this, screenNailThumbnailType, losslessCache)
                    } ?: GLog.w(TAG, "decodeOriginalAndSaveCache resize bitmap failed, bitmap is null! $debugTag")
                }
                val recommendRect: Rect? = when {
                    // 原比例缩放即可，短边不超过限制
                    isExtendThumbnailType -> Rect(0, 0, originalBitmap.width, originalBitmap.height)
                    // 目前只有小缩图直接以1x1缓存，其他都是原比例
                    cropOperator.autoCropRectMap.isNotEmpty() -> {
                        cropOperator.autoCropRectMap[CropRatio.RATIO_1_1]?.getRect(
                            originalBitmap.width,
                            originalBitmap.height
                        )
                    }
                    else -> null
                }
                BitmapUtils.resizeAndCrop(originalBitmap, targetSize, false, false, recommendRect, bitmapCreator)
            } else {
                BitmapUtils.resizeByLongSide(originalBitmap, bitmapCreator, targetSize, false, false)
            }
            if (originalBitmap != resultBitmap) {
                BitmapPools.recycle(originalBitmap)
            }
            if (jc.isCancelled()) {
                return null
            }
            saveBitmapToCache(jc, resultBitmap, losslessCache)
            ImageData(resultBitmap)
        } else {
            GLog.e(TAG, "decodeOriginalAndSaveCache canDecodeOriginal=false, ImageRequest return null")
            null
        }
    }

    /**
     * 保存的条件：
     *条件1.judgeType == [screenNailThumbnailType]：列表缩图是heif图，缩图尺寸都不会超过[screenNailSize]
     *条件2.是heif图且扩展缩图尺寸 > [screenNailSize]
     */
    private fun isCacheScreenNail(judgeType: Int): Boolean {
        return isFullThumbnailKey(judgeType) ||
                (isExtendThumbnailKey(judgeType) && (targetSize >= screenNailSize) && ImageTypeUtils.isHeif(localItem.path))
    }

    protected open fun saveBitmapToCache(jc: JobContext, bitmap: Bitmap, losslessCache: Boolean) {
        if (requestFlags and REQUEST_FLAG_ASYNC_SAVE_CACHE == REQUEST_FLAG_ASYNC_SAVE_CACHE) {
            GLog.d(TAG, "saveBitmapToCache in async")
            val bitmapColorSpace = bitmap.colorSpace
            session.submit {
                // 并发拷贝，避免 bitmap 正在使用时，更改色彩空间或者转换色域
                val bitmapToSave = bitmap.copy(bitmap.getConfigSafely(), true)
                bitmapToSave.setColorSpace(bitmapColorSpace ?: ColorModelManager.SRGB)
                saveBitmapToCache(it, bitmapToSave, inThumbnailType, losslessCache)
                bitmapToSave.recycle()
            }
        } else {
            GLog.d(TAG, "saveBitmapToCache in sync")
            saveBitmapToCache(jc, bitmap, inThumbnailType, losslessCache)
        }
    }

    protected open fun saveBitmapToCache(
        jc: JobContext,
        bitmap: Bitmap,
        judgeType: Int,
        losslessCache: Boolean
    ) {
        if (ColorModelManager.isWideColorGamutOpen && ColorModelManager.isSupportColorManagementV2.not()) {
            ColorModelManager.convertBitmapColor(bitmap, ColorModelManager.DISPLAY_P3)
        }

        val array = if (!losslessCache) {
            BitmapUtils.compressLowQualityToBytes(bitmap)
        } else {
            BitmapUtils.compressToBytes(bitmap, CompressFormat.PNG)
        }
        if (jc.isCancelled()) return

        DiskCacheManagerService.putImageData(createCacheKey(judgeType), array)

        /*
         * 提交hash:78b68d5bcd1ddbbfffed76978ddd64e7dc43bc7b: 2017/9/11 add for bug:1100375
         * 时间轴第一次加载SD卡图片，解码过程中拔卡，解出来的图片bitmap不完整，会有黑块，且已写入blobCache文件缓存中，导致年月视图也存在黑块
         * 解决如下：在解码sd卡的图片且保存到blobCache时，同时记录图片，最多记录最后64张,此时拔卡，则删除blobCache和年月的blockcache对应的图片信息，下次进入则重新解码
         */
        if ((sourceType == DataSourceType.TYPE_SDCARD) &&
            (isMicroThumbnailKey(judgeType) || isExtendThumbnailKey(judgeType) || ThumbnailSizeUtils.isVideoMicroThumbnailKey(judgeType))
        ) {
            SDCardMediaCacheManager.addSdCardCacheInfo(localItem, judgeType)
        }
    }

    private fun createCacheKey(thumbnailType: Int): MediaCacheKey {
        var cropRect: Rect? = null
        // 年月日缩图都是原比例缓存，无需带入裁剪信息
        if (cropOperator.isAutoCrop(thumbnailType) && !isExtendThumbnailKey(thumbnailType)) {
            cropRect = cropOperator.autoCropRectMap[cropOperator.getAutoCropRatio(thumbnailType, options)]?.rect
        }
        return ImageCacheKey.create(
            thumbnailType = thumbnailType,
            path = localItem.path,
            dateModifiedInSec = localItem.dateModifiedInSec,
            videoTimeMills = videoTimeMills,
            cropRect = cropRect,
            getKeyOfCodecType(localItem)
        )
    }

    companion object {
        private const val TAG = "ImageCacheRequest"
        const val CACHE_BITMAP_TARGET_SIZE = 128
        const val CACHE_BITMAP_BLUR_RADIUS = 15F

        const val REQUEST_FLAG_NONE = 0x0000
        const val REQUEST_FLAG_ASYNC_SAVE_CACHE = 0x0001

        /**
         * 跳过原图解码标记，置位时，不进行原图解码
         */
        const val REQUEST_FLAT_NOT_DECODE_ORIGINAL = 0x0008

        private val DEBUG_CLOSE_CACHE = GallerySystemProperties.getBoolean("debug.imageRequest.closeCache", false)
        private val DEBUG_SPECIFY_ID = GallerySystemProperties.getInt("debug.imageRequest.id", -1)
        private val bitmapCreator: BitmapCreator by lazy {
            object : BitmapCreator {
                override fun create(width: Int, height: Int, config: Bitmap.Config): Bitmap? {
                    return BitmapPools.getBitmap(width, height, config)
                }
            }
        }
    }

    /**
     * 处理智能裁剪相关
     */
    private class CropOperator(private val context: Context, private val localItem: LocalMediaItem) {

        /** 智能裁剪框信息 */
        val autoCropRectMap = CropRectSet.getCropRectMap(localItem.cropRectSet)

        /**
         * 缩图类型是否允许被裁剪（历史遗留，此逻辑不应该写在这里）
         */
        fun isAllowCrop(thumbnailType: Int): Boolean {
            return isMicroThumbnailKey(thumbnailType) ||
                    isExtendThumbnailKey(thumbnailType) ||
                    ThumbnailSizeUtils.isVideoMicroThumbnailKey(thumbnailType)
        }

        /**
         * 裁剪类型
         */
        fun isAutoCrop(thumbnailType: Int): Boolean {
            return isAllowCrop(thumbnailType) && autoCropRectMap.isNotEmpty()
        }

        fun getAutoCropRatio(thumbnailType: Int, options: ResourceGetOptions): CropRatio {
            if (isMicroThumbnailKey(thumbnailType) ||
                ThumbnailSizeUtils.isVideoMicroThumbnailKey(thumbnailType) ||
                (options.targetWidth <= 0) ||
                (options.targetHeight <= 0)
            ) {
                return CropRatio.RATIO_1_1
            }

            val cropRatio = localItem.getCropRatio(options.targetWidth, options.targetHeight)
            return if ((autoCropRectMap[cropRatio] != null) || GProperty.DEBUG_REAL_TIME_AUTO_CROP) {
                // 已有裁剪数据或者支持实时裁剪，才使用最接近的裁剪类型
                cropRatio
            } else {
                CropRatio.RATIO_1_1
            }
        }

        /**
         * 智能裁剪
         */
        fun cropBitmap(srcBitmap: Bitmap, thumbnailType: Int, options: ResourceGetOptions): Bitmap {
            var recommendRect: Rect? = null
            if (autoCropRectMap.isNotEmpty()) {
                val cropRatio = getAutoCropRatio(thumbnailType, options)
                val cropRect = autoCropRectMap[cropRatio] ?: let {
                    // 还没有扫描刷新存量数据，不能用最新版本库扫，因为如果将该裁剪框保存至数据库并更新版本号，其他裁剪比例的数据可能是旧的
                    if (ApiDmManager.getScanDM().isNewestAutoCropDataVersion(localItem.cropRectVersion) != true) return@let null
                    val startTime = System.currentTimeMillis()
                    val cropRect = ApiDmManager.getScanDM().getCropRects(context, localItem, arrayOf(cropRatio))?.firstOrNull() ?: return@let null
                    // 追加新的裁剪框数据，存入数据库
                    val list = mutableListOf<CropRect>()
                    list.addAll(autoCropRectMap.values)
                    list.add(cropRect)
                    localItem.cropRectSet = CropRectSet(
                        list
                    ).toString()
                    saveCropRectToDB(localItem.cropRectSet)
                    GLog.d(TAG, "cropBitmap, append crop info, cost:${System.currentTimeMillis() - startTime}}")
                    cropRect
                }
                recommendRect = cropRect?.getRect(srcBitmap.width, srcBitmap.height)
            }
            val time = System.currentTimeMillis()
            var limitSize = getTargetSizeInType(thumbnailType)
            if ((options.targetWidth > 0) && (options.targetHeight > 0)) {
                limitSize = limitSize.coerceAtMost(options.targetWidth.coerceAtMost(options.targetHeight))
                recommendRect =
                    recommendRect ?: getDefaultCropRect(srcBitmap.width, srcBitmap.height, options.targetWidth, options.targetHeight)
            }
            /* 当裁剪区域小于limitSize时，说明分辨率不足，没有必要放大bitmap，不超过limitSize即可 */
            val result = BitmapUtils.resizeAndCrop(srcBitmap, limitSize, false, false, recommendRect, bitmapCreator)
            if (result != srcBitmap) {
                BitmapPools.recycle(srcBitmap)
            }
            if (GProperty.DEBUG) {
                val ratioInfo = recommendRect?.let {
                    if (it.height() > 0) "auto_${it.width() / it.height()}" else "error rect:$it"
                } ?: "center_1.0"
                GLog.d(TAG, "cropBitmap, ratio:$ratioInfo, cost:${System.currentTimeMillis() - time}")
            }
            return result
        }

        /**
         * 当没有智能裁剪数据时，默认用目标宽高比生成居中裁剪数据
         */
        private fun getDefaultCropRect(srcWidth: Int, srcHeight: Int, targetWidth: Int, targetHeight: Int): Rect {
            // 对于图像转了90度、270度的，需要宽高对调一下获取裁剪数据
            return if ((localItem.getThumbnailRotation() % AppConstants.Degree.DEGREE_180) == AppConstants.Degree.DEGREE_90) {
                BitmapUtils.calculateCropRect(srcWidth, srcHeight, targetHeight, targetWidth)
            } else {
                BitmapUtils.calculateCropRect(srcWidth, srcHeight, targetWidth, targetHeight)
            }
        }

        /**
         * 将新的裁剪框信息存入数据库
         */
        fun saveCropRectToDB(cropRectInfo: String) {
            val contentValues = ContentValues()
            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.CROP_RECT, cropRectInfo)
            UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setWhere(GalleryStore.GalleryColumns.LocalColumns.DATA + ConstantUtils.EQUAL_TO)
                .setWhareArgs(arrayOf(localItem.filePath))
                .setConvert(ContentValuesConvert(contentValues))
                .build()
                .exec()
        }
    }
}