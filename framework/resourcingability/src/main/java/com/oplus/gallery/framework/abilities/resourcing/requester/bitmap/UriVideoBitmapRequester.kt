/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UriVideoBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.cache.imagerequest.UriCacheRequest
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.framework.abilities.resourcing.ResourcingJobContext
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 *  UriVideo Bitmap资源加载请求类。
 */
internal class UriVideoBitmapRequester(
    originalResource: UriMediaResourceKey,
    options: ResourceGetOptions,
    private val cancelable: ICancelable?
) : UriCacheRequest(
    uri = originalResource.contentUri,
    options = options,
    contentType = originalResource.mimeType,
    uriItem = originalResource.uriItem
), IBitmapRequester {

    override fun request(): ImageResult<Bitmap>? {
        val resourcingJobContext = ResourcingJobContext(cancelable)
        return getBitmap(resourcingJobContext)?.bitmap?.let { ImageResult(it) }
    }

    override fun updateContentIfNeeded() {
        if (uriItem.mAlwaysReload) {
            processFile(context, uri)
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    @Suppress("TooGenericExceptionCaught")
    internal fun processFile(context: Context, uri: Uri) {
        GLog.d(TAG, "processFile uri: $uri")
        MediaMetadataRetriever().let { mediaRetriever ->
            try {
                mediaRetriever.setDataSource(context, uri)

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.let {
                    if (it.isNotEmpty()) uriItem.width = parseSubstring(it, start = 0, defValue = 0)
                }

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.let {
                    if (it.isNotEmpty()) uriItem.height = parseSubstring(it, start = 0, defValue = 0)
                }

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.let {
                    if (it.isNotEmpty()) uriItem.rotation = parseSubstring(it, start = 0, defValue = 0)
                }

                mediaRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE)?.let {
                    if (it.isNotEmpty()) contentType = it
                }

                GLog.d(
                    TAG, "processFile width=${uriItem.width}, height=${uriItem.height}," +
                            "rotation=${uriItem.rotation} contentType=$contentType"
                )
            } catch (e: Exception) {
                GLog.e(TAG, "processFile, process failed!", e)
            } finally {
                mediaRetriever.release()
            }
        }
    }

    override fun updateThumbnailTargetSize(): Int = ThumbnailSizeUtils.getTargetSizeInType(inThumbnailType)

    override fun decodeThumbnailFromFile(jc: JobContext, targetSize: Int): ImageData? =
        (DecodeUtils.decodeVideoThumbnail(pfd, false))?.let { ImageData(it) }

    companion object {
        const val TAG = "UriVideoRequest"
        private const val NUMBER_TEN = 10

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        internal fun parseSubstring(str: String, start: Int, defValue: Int): Int {
            var startIndex = start
            if (TextUtils.isEmpty(str) || (startIndex == str.length)) {
                return defValue
            }
            var ch = str[startIndex++]
            // return defaultValue if we have no integer at all
            if (ch < '0' || ch > '9') return defValue

            var result = ch - '0'
            while (startIndex < str.length) {
                ch = str[startIndex++]
                if (ch < '0' || ch > '9') {
                    return result
                }
                result = result * NUMBER_TEN + (ch - '0')
            }
            return result
        }
    }
}