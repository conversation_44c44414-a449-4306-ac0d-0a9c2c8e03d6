/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SharedImageRegionDecoderRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/09/09
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>      <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     gaozhiqiang          2022/09/09  1.0               create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.key.SharedMediaResourceKey

/**
 * SharedImage, IRegionDecoder资源加载实现类。
 */
internal class SharedImageRegionDecoderRequester(private val sharedMediaResource: SharedMediaResourceKey) : IRegionDecoderRequester {
    override fun request(): IRegionDecoder? {
        val filePath = sharedMediaResource.filePath
        if (filePath == null) {
            GLog.e(TAG, "request: filePath is null.")
            return null
        }
        runCatching {
            return DecoderWrapper.getIRegionDecoder(filePath, isUseFd = false)?.apply {
                setTombstone(BreakpadTombstone(filePath, sharedMediaResource.dateModifiedInSecond))
            }
        }.onFailure {
            GLog.e(TAG, "request: Region decode fail ${it.message}")
        }
        return null
    }

    companion object {
        const val TAG = "SharedImageRegionDecoderRequester"
    }
}