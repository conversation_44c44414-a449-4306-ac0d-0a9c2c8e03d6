/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - AbsGalleryImageBitmapRequester.kt
 ** Description:图片 Bitmap资源请求的基类
 ** Version: 1.0
 ** Date : 2023/3/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2023/3/18        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ColorSpace
import android.media.ExifInterface
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_EXIF_THUMB
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.hasGainmapCompat
import com.oplus.gallery.foundation.util.mask.maskPath
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec.IS_ENABLE_IMAGE_REGION_DECODE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_CAMERA_JPEG_EXIF_THUMBNAIL
import com.oplus.gallery.framework.abilities.resourcing.ResourcingJobContext
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_0
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.RegionDecoder
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 图片 Bitmap资源请求的基类
 */
internal abstract class AbsGalleryImageBitmapRequester(
    context: Context,
    val key: LocalMediaResourceKey,
    options: ResourceGetOptions,
    abilityBus: IAbilityBus,
    cachingAbility: ICachingAbility?,
    colorManagementAbility: IColorManagementAbility?,
    cancelable: ICancelable?,
    requesterBySync: Boolean = false
) : AbsGalleryMediaBitmapRequester(context, key, options, abilityBus, cachingAbility, colorManagementAbility, cancelable, requesterBySync) {

    /**
     * 是否支持exif缩图，这个值不会变化，所以用 by lazy
     */
    private val isSupportExifThumbnail: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(FEATURE_IS_SUPPORT_CAMERA_JPEG_EXIF_THUMBNAIL)
        } == true
    }

    /**
     * 是否区域解码缩图(仅高通平台)
     *
     * 区域解码有更好的性能，但是稳定性太差了，多媒体那边迟迟无法改善，因此默认禁用区域解码
     *
     * 目前支持在线配置，后续多媒体修复问题后，把区域解码放开，mark by wuhengze
     */
    private val shouldRegionDecodeThumbnail: Boolean by lazy {
        FeatureUtils.isQualcommPlatform && (abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(IS_ENABLE_IMAGE_REGION_DECODE)
        } == true)
    }

    @Suppress("LongMethod")
    override fun onDecodeOriginal(
        decodeOption: DecodeOption,
        type: Int,
        resourceOptions: ResourceGetOptions,
        cancelable: ICancelable?
    ): Bitmap? {
        val bitmapOptions = BitmapFactory.Options().apply {
            inPreferredConfig = DecodeUtils.DECODE_CONFIG
        }

        val targetSize = ThumbnailSizeUtils.getTargetSizeInType(type)

        if (shouldDecodeBmp(decodeOption, bitmapOptions)) {
            return decodeBmp(cancelable, decodeOption, bitmapOptions, targetSize, type)
        }

        decodeFromExifIfNeed(decodeOption, resourceOptions, targetSize)?.let { bitmap ->
            return bitmap
        }

        val time = System.currentTimeMillis()
        if (shouldDecodeFromMedia(resourceOptions)) {
            getMediaThumbnail(decodeOption, resourceOptions, targetSize)?.let { bitmap ->
                if (GProperty.LOG_OPEN_DEBUG_DECODE) {
                    GLog.d(TAG, LogFlag.DL) {
                        "getMediaThumbnail: ${decodeOption.mediaId} cost: ${System.currentTimeMillis() - time}ms"
                    }
                }
                return bitmap
            }
        }
        val bitmap = decodeOriginal(decodeOption, cancelable, bitmapOptions, targetSize, type)
        if (GProperty.LOG_OPEN_DEBUG_DECODE) {
            GLog.d(TAG, LogFlag.DL) {
                "decodeOriginal targetSize = $targetSize, type = $type, hasGain=${bitmap?.hasGainmapCompat()}, filePath = ${decodeOption.filePath}" +
                        ",  ${decodeOption.mediaId} cost: ${System.currentTimeMillis() - time}ms"
            }
        }
        return bitmap
    }

    private fun decodeOriginal(
        decodeOption: DecodeOption,
        cancelable: ICancelable?,
        bitmapOptions: BitmapFactory.Options,
        targetSize: Int,
        type: Int
    ) = if (shouldRegionDecodeThumbnail) {
        originalResource.mediaItem.apply {
            if (CodecHelper.isInterceptDecode(context, width.toLong(), height.toLong(), filePath, mediaId.toString())) {
                GLog.d(TAG, LogFlag.DF, "isInterceptDecode so return")
                return null
            }
        }
        if (RegionDecoder.isSupportRegionDecode(decodeOption.mimeType)) {
            CodecHelper.decodeRegionFullRectThumbnail(
                ResourcingJobContext(cancelable),
                decodeOption.filePath,
                decodeOption.mediaId.toString(),
                bitmapOptions,
                targetSize,
                type,
                decodeOption.isYuvAndLargePage(type),
                originalResource.dateModifiedInSecond,
                false
            )?.bitmap
        } else {
            CodecHelper.decodeThumbnail(
                ResourcingJobContext(cancelable),
                decodeOption.filePath,
                decodeOption.mediaId.toString(),
                bitmapOptions,
                targetSize,
                type,
                decodeOption.isYuvAndLargePage(type),
                originalResource.dateModifiedInSecond,
                false
            )?.bitmap
        }
    } else {
        CodecHelper.decodeThumbnail(
            ResourcingJobContext(cancelable),
            decodeOption.filePath,
            decodeOption.mediaId.toString(),
            bitmapOptions,
            targetSize,
            type,
            decodeOption.isYuvAndLargePage(type),
            originalResource.dateModifiedInSecond,
            false
        )?.bitmap
    }

    private fun shouldDecodeBmp(decodeOption: DecodeOption, bitmapOptions: BitmapFactory.Options): Boolean {
        if (MimeTypeUtils.isBmp(decodeOption.mimeType)) {
            var imageWidth = originalResource.width
            var imageHeight = originalResource.height
            if ((imageWidth <= 0) || (imageHeight <= 0) || ((imageWidth * imageHeight) >= DECODE_LOCK_MAX_SIZE)) {
                DecodeUtils.decodeBounds(ContextGetter.context, decodeOption.fileUri, decodeOption.filePath, bitmapOptions)
                imageWidth = bitmapOptions.outWidth
                imageHeight = bitmapOptions.outHeight
            }
            if ((imageWidth > 0) && (imageHeight > 0) && ((imageWidth * imageHeight) < DECODE_LOCK_MAX_SIZE)) {
                return true
            }
        }
        return false
    }

    private fun decodeBmp(
        cancelable: ICancelable?,
        decodeOption: DecodeOption,
        options: BitmapFactory.Options,
        targetSize: Int,
        type: Int
    ): Bitmap? {
        return if (cancelable?.isCancelled() == true) {
            null
        } else {
            CodecHelper.decodeThumbnail(
                ResourcingJobContext(cancelable),
                decodeOption.filePath,
                decodeOption.mediaId.toString(),
                options,
                targetSize,
                type,
                decodeOption.isYuvAndLargePage(type),
                originalResource.dateModifiedInSecond,
                false
            ).bitmap
        }
    }

    /**
     * 从exif中解码小缩图
     */
    private fun decodeFromExifIfNeed(decodeOption: DecodeOption, resourceOptions: ResourceGetOptions, targetSize: Int): Bitmap? {
        // mark@houdonggu 彻底解决同步filePath不对的问题
       runCatching {
            if (shouldDecodeFromExif(decodeOption, resourceOptions)) {
                GTrace.traceBegin { "decodeFromExif.${decodeOption.mediaId}"}
                getExifThumbnail(decodeOption)?.takeIf {
                    (it.width >= resourceOptions.minWidth) && (it.height >= resourceOptions.minHeight)
                }?.also { bitmap ->
                    // exif缩图，由于是240为短边的，所以允许有一定的范围误差，误差以内不再请求原图解码了
                    val minTargetSize = targetSize * WIDTH_HEIGHT_ACCEPT_MIN_RATIO
                    resourceOptions.outLowResolution = (bitmap.width < minTargetSize) || (bitmap.height < minTargetSize)
                    if (resourceOptions.outLowResolution.not()) {
                        adjustColorSpace(bitmap, decodeOption)
                    }
                    GTrace.traceEnd()
                    return bitmap
                }
                GTrace.traceEnd()
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "decodeFromExifIfNeed filePath:${decodeOption.filePath.maskPath()} error:${it.message}" }
            GTrace.traceEnd()
        }
        return null
    }

    private fun shouldDecodeFromExif(decodeOption: DecodeOption, options: ResourceGetOptions) =
        options.decodeLowResolutionEnabled &&
            isSupportExifThumbnail(decodeOption.mimeType, decodeOption.filePath) &&
            options.inLowResolutionRetry.not() &&
            (options.minWidth <= MAX_EXIF_THUMB_SIZE) &&
            (options.minHeight <= MAX_EXIF_THUMB_SIZE)

    /**
     * 解Exif耗时，所以限制部分Exif有缩略图的mimeType
     */
    private fun isSupportExifThumbnail(mimeType: String, filePath: String): Boolean {
        return when (mimeType) {
            MimeTypeUtils.MIME_TYPE_IMAGE_JPEG -> {
                if (filePath.contains(Dir.CAMERA.dirPath) && isSupportExifThumbnail) {
                    true
                } else {
                    originalResource.fileSize >= MIN_EXIF_THUMB_JPG_FILE_SIZE
                }
            }
            MimeTypeUtils.MIME_TYPE_IMAGE_HEIF,
            MimeTypeUtils.MIME_TYPE_IMAGE_HEIC -> return true
            else -> return false
        }
    }

    private fun getExifThumbnail(decodeOption: DecodeOption): Bitmap? {
        GTrace.traceBegin("ExifThumbnail.new")
        val time = System.currentTimeMillis()
        val exifInterface = ExifInterface(decodeOption.filePath)
        GTrace.traceEnd()
        if (DEBUG_EXIF_THUMB) {
            GLog.d(TAG, LogFlag.DL) {
                "getExifThumbnail: construct exif ${decodeOption.filePath} cost: ${System.currentTimeMillis() - time}ms"
            }
        }

        if (exifInterface.hasThumbnail() && isNeedGetThumbnail(decodeOption, exifInterface)) {
            GTrace.traceBegin("ExifThumbnail.thumbnailBitmap")
            return exifInterface.thumbnailBitmap?.apply {
                GTrace.traceEnd()
                if (DEBUG_EXIF_THUMB) {
                    GLog.d(
                        TAG, LogFlag.DL, "getExifThumbnail: success file: ${decodeOption.filePath.maskPath()} thumbnail: $width*$height " +
                                "cost: ${System.currentTimeMillis() - time}ms"
                    )
                }
            }
        }
        return null
    }

    /**
     * 问题背景：8940558问题使用的图片为Canon 拍摄的jpeg图片，其实exif里面带的图片经过了色域的处理，会出现和主图不一致的情况。
     * 这里通过查询exif里面的TAG_MAKE信息进行黑名单管控，避免获取exif缩略图。
     */
    private fun isNeedGetThumbnail(decodeOption: DecodeOption, exifInterface: ExifInterface): Boolean {
        //仅在jpeg的条件下才检查TAG_MAKE
        if (decodeOption.mimeType == MimeTypeUtils.MIME_TYPE_IMAGE_JPEG) {
            exifInterface.getAttribute(ExifInterface.TAG_MAKE)?.let {
                return (it != EXIF_TAG_MAKE_CANON)
            }
        }
        return true
    }

    /**
     * 适配不同情况下的色域
     */
    private fun adjustColorSpace(bitmap: Bitmap, decodeOption: DecodeOption) {
        //mark@houdonggu 临时先用这个方案解决exif缩图的色域问题，后续等80264337/80354221修改系统代码彻底解决后删除这块逻辑
        val startTime = System.currentTimeMillis()
        val colorOptions = DecodeUtils.decodeBounds(decodeOption.filePath, null)
        // 色彩管理打开
        abilityBus.requireAbility(IColorManagementAbility::class.java)?.apply {
            // 色彩管理打开
            if (isWideColorGamutEnabled == true) {
                bitmap.setColorSpace(colorOptions.outColorSpace)
                // 色彩管理1.0 自然模式
                if (isDisplaySupportWCG.not()) {
                    convertBitmapPixels(bitmap, ColorSpace.get(ColorSpace.Named.DISPLAY_P3))
                }
            } else {
                adjustColorSpace(bitmap, colorOptions.outColorSpace)
            }
        }
        if (DEBUG_EXIF_THUMB) {
            GLog.d(TAG, LogFlag.DF) { "adjustColorSpace after setColorSpace cost:${System.currentTimeMillis() - startTime}" }
        }
    }

    /**
     * 从多媒体图片缩略图路径解析Bitmap
     */
    private fun getMediaThumbnail(decodeOption: DecodeOption, resourceOptions: ResourceGetOptions, targetSize: Int): Bitmap? {
        kotlin.runCatching {
            val checkFile =
                FileOperationUtils.checkFileAlreadyExit((originalResource.mediaId.toString() + JPG), MEDIA_PICTURES_THUMBNAIL_RELATIVE_PATH)
            checkFile?.path?.let { path ->
                BitmapFactory.decodeFile(path)?.let { bitmap ->
                    val minTargetSize = targetSize * WIDTH_HEIGHT_ACCEPT_MIN_RATIO
                    resourceOptions.outLowResolution = (bitmap.width < minTargetSize) || (bitmap.height < minTargetSize)
                    if (resourceOptions.outLowResolution.not()) {
                        adjustColorSpace(bitmap, decodeOption)
                    }
                    return bitmap
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getMediaThumbnail mediaId:${originalResource.mediaId} error:${it.message}" }
        }
        return null
    }

    /**
     * 系统相册接入媒体库缓存缩略图
     * 判断小图 且(内销15.0.0以上 或 外销16.0.0以上)
     * @return Boolean
     */
    private fun shouldDecodeFromMedia(options: ResourceGetOptions): Boolean {
        /***
         * 从媒体库中取缩图，如果有图片角度，则媒体库的缩图会自校正角度，但是相册的角度校正都是在绘制的时候进行统一校正的。
         * 所以在这种情况下图片的角度被多次校正了，导致方向不对
         * 这种情况现有的框架无法兼容，因此有角度的图片不再充媒体库中获取。
         */
        if (key.mediaItem.rotation != DEGREE_0) {
            return false
        }
        return options.decodeLowResolutionEnabled && options.inLowResolutionRetry.not()
                && ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType)
                && ((isRegionCN && isAtLeastOS15) || (!isRegionCN && isAtLeastOS16))
                && !MimeTypeUtils.isImageSupportAlphaChannel(originalResource.mimeType) // 媒体库缩图都是 jpg 格式，不支持 alpha 通道
    }

    companion object {
        private const val DECODE_LOCK_MAX_SIZE = 4000 * 4000
        private const val TAG = "AbsGalleryImageBitmapRequester"
        //由于相机的缩略图是以短边240为准缩放的，时间轴等缩图请求有256*256和360*360，如果都要能接受的话，那么就需要一定的宽高误差范围，以240/360=0.66为标准
        private const val WIDTH_HEIGHT_ACCEPT_MIN_RATIO = 0.66f

        /**
         * 用于判断是否需要获取Exif缩略图的最大尺寸
         *
         * 在解析Exif前推断，按照一般Exif缩略图尺寸，若解析Exif获取到的缩略图肯定不满足尺寸要求，那么就跳过解析Exif节省时间
         * 目前常见的Exif缩略图里尺寸最大的是pangu等新机型拍摄的照片256
         */
        private const val MAX_EXIF_THUMB_SIZE = 256

        /**
         * 对jpg图片，大于5MB，有Exif缩略图的可能性更大（比如数码相机拍摄的照片），并且获取Exif缩略图相对于解码图片速度提升效果更明显
         */
        private const val MIN_EXIF_THUMB_JPG_FILE_SIZE = 5 * 1024 * 1024 // 10MB

        /**
         * 定义Canon相机拍摄的图片的TAG_MAKE
         */
        private const val EXIF_TAG_MAKE_CANON = "Canon"
    }
}