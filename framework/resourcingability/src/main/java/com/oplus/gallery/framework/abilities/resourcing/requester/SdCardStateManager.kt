/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SdCardStateManager.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 管理SdCard状态
 */
internal object SdCardStateManager {
    private const val TAG = "SDCardStateManager"

    private var sdcardState: String? = null

    private var sdCardReceiver: SdCardReceiver? = null

    @JvmStatic
    fun updateSdCardState() {
        sdcardState = OplusEnvironment.getexternalSdState()
    }

    @JvmStatic
    fun isMediaMounted(): Boolean {
        return OplusEnvironment.MEDIA_MOUNTED == sdcardState
    }

    @JvmStatic
    fun getSdCardState(): String? {
        return sdcardState
    }

    @JvmStatic
    @Synchronized
    fun registerSdCardStateMonitor() {
        if (sdCardReceiver != null) {
            return
        }

        sdCardReceiver = SdCardReceiver()
        val filter = IntentFilter(Intent.ACTION_MEDIA_EJECT)
        filter.addAction(Intent.ACTION_MEDIA_UNMOUNTED)
        filter.addAction(Intent.ACTION_MEDIA_MOUNTED)
        filter.addDataScheme("file")
        BroadcastDispatcher.registerReceiver(ContextGetter.context, sdCardReceiver, filter)
    }

    @JvmStatic
    @Synchronized
    fun unRegisterSdCardStateMonitor() {
        sdCardReceiver?.let {
            BroadcastDispatcher.unregisterReceiver(ContextGetter.context, it)
            sdCardReceiver = null
        }
    }

    private class SdCardReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            GLog.d(TAG, "onReceive action:$action")
            // add for bug:1100375
            updateSdCardState()
        }
    }
}