/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EmptyImageRequest.kt
 ** Description : 无需加载缩图的请求，只为回调 futureListener 去加载本地drawable
 ** Version     : 1.0
 ** Date        : 2021/09/29
 ** Author      : <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery3D           2021/09/29   1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.cache.imagerequest

import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext

class EmptyImageRequest() : Job<ImageData> {
    override fun call(jc: JobContext): ImageData? {
        return null
    }
}