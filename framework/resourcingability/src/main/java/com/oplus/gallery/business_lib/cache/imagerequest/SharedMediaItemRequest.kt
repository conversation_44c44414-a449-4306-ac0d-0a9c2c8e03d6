/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedMediaItemRequest
 ** Description:
 ** Version: 1.0
 ** Date : 2022/4/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>            2022/4/8        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.provider.MediaStore
import com.oplus.gallery.business_lib.cache.CacheType
import com.oplus.gallery.business_lib.cache.ImageCacheKey
import com.oplus.gallery.business_lib.cache.diskcache.DiskCacheManagerService
import com.oplus.gallery.business_lib.model.data.base.item.SharedMediaItem
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BytesBufferPool
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.videoframe.HardwareDecoderConfig
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession

/**
 * 共享图集列表页缩图加载
 */
class SharedMediaItemRequest(
    private val context: Context,
    private val session: WorkerSession,
    private val options: ResourceGetOptions,
    private val mediaItem: SharedMediaItem,
    private val hardwareDecoderConfig: HardwareDecoderConfig,
) : Job<ImageData> {
    private val inThumbnailType = options.inThumbnailType

    override fun call(jc: JobContext): ImageData? {
        // 本地未上传直接使用文件路径
        if (mediaItem.isCloudFileExist.not()) {
            // 加载本地视频封面
            if (mediaItem.mediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO) {
                GLog.d(TAG, "call local video")
                return mediaItem.refVideo?.let {
                    LocalVideoRequest(context, session, options, it, hardwareDecoderConfig).call(jc)
                }
            }
            GLog.d(TAG, "call local image")
            return getOriginalImageData(jc, mediaItem.filePath)
        }
        // 已上传加载缩略图
        val fileId = mediaItem.fileId ?: TextUtil.EMPTY_STRING
        if (fileId.isNotEmpty()) {
            GLog.d(TAG, "call thumbnail")
            return getThumbnailImageData(jc, fileId)
        }
        GLog.d(TAG, "call null")
        return null
    }

    private fun getThumbnailImageData(jc: JobContext, fileId: String): ImageData? {
        val buffer = BytesBufferPool.get(0)
        try {
            val cacheKey = ImageCacheKey(
                SourceConstants.Shared.PATH_ITEM_THUMBNAIL_IMAGE.getChild(fileId),
                CacheType.IMAGE_BLOB_CACHE,
                ThumbnailSizeUtils.TYPE_THUMBNAIL,
                0
            )
            if (DiskCacheManagerService.getImageData(cacheKey, buffer)) {
                return CodecHelper.decodeThumbnailArray(
                    jc,
                    buffer.data,
                    buffer.offset,
                    buffer.length,
                    BitmapFactory.Options().apply { inPreferredConfig = DecodeUtils.DECODE_CONFIG },
                    ThumbnailSizeUtils.getTargetSizeInType(inThumbnailType),
                    inThumbnailType
                )?.let { ImageData(it) }
            }
            return null
        } finally {
            BytesBufferPool.recycle(buffer)
        }
    }

    private fun getOriginalImageData(jc: JobContext, filePath: String): ImageData? {
        return CodecHelper.decodeThumbnail(
            jc,
            filePath,
            File(filePath).toUri(),
            BitmapFactory.Options().apply { inPreferredConfig = Bitmap.Config.RGB_565 },
            ThumbnailSizeUtils.getTargetSizeInType(inThumbnailType),
            inThumbnailType,
            false,
            mediaItem.dateTakenInMs,
            false
        )
    }

    companion object {
        private const val TAG = "SharedMediaItemRequest"
    }
}