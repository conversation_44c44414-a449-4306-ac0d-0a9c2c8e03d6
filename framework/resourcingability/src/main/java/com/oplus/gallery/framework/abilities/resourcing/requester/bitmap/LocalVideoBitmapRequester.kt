/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalVideoBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.ParcelFileDescriptor
import androidx.annotation.VisibleForTesting
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DX4_PLATFORM
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation
import com.oplus.gallery.framework.abilities.resourcing.util.ThumbnailDecoderHelper
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.videoframe.MeicamVideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_GPU_DECODE
import com.oplus.gallery.standard_lib.codec.videoframe.HardwareDecoderConfig

/**
 * LocalVideo，Bitmap资源请求加载类
 */
internal class LocalVideoBitmapRequester(
    context: Context,
    key: LocalMediaResourceKey,
    option: ResourceGetOptions,
    abilityBus: IAbilityBus,
    cachingAbility: ICachingAbility?,
    colorManagementAbility: IColorManagementAbility?,
    cancelable: ICancelable?
) : AbsGalleryMediaBitmapRequester(context, key, option, abilityBus, cachingAbility, colorManagementAbility, cancelable) {

    override fun getTag(): String = TAG

    override fun requestSource(): Bitmap? {
        return if (SourceOperation.ReadLocal in options.inSourceOperation) {
            decodeLocalFile(DecodeOption(originalResource))
        } else null
    }

    override fun onDecodeOriginal(
        decodeOption: DecodeOption,
        type: Int,
        options: ResourceGetOptions,
        cancelable: ICancelable?
    ): Bitmap? {
        return GTrace.trace("LocalVideo.origin") {
            var bitmap: Bitmap? = null
            val time = System.currentTimeMillis()
            if (shouldDecodeFromMedia(options)) {
                getMediaThumbnail()?.let {
                    if (GProperty.LOG_OPEN_DEBUG_DECODE) {
                        GLog.d(TAG, LogFlag.DL) {
                            "getMediaThumbnail: ${decodeOption.mediaId} cost: ${System.currentTimeMillis() - time}ms"
                        }
                    }
                    bitmap = it
                }
            }
            if (bitmap == null) {
                bitmap = decodeVideoThumbnailWithKeyFrame(
                    decodeOption.mediaId,
                    decodeOption.filePath,
                    ThumbnailSizeUtils.getTargetSizeInType(type)
                )
                if (bitmap == null) {
                    GLog.d(TAG, LogFlag.DL, "[onDecodeOriginal] bitmap is null LocalMediaResourceKey = $originalResource")
                    return@trace null
                } else {
                    if (GProperty.LOG_OPEN_DEBUG_DECODE) {
                        GLog.d(TAG, LogFlag.DL) {
                            "decodeVideoThumbnailWithKeyFrame origin , type = $type, filePath = ${decodeOption.filePath}" +
                                    ",  ${decodeOption.mediaId} cost: ${System.currentTimeMillis() - time}ms"
                        }
                    }
                }
                if (cancelable?.isCancelled() == true) {
                    GLog.d(TAG, LogFlag.DL, "[onDecodeOriginal] isCancelled LocalMediaResourceKey = $originalResource")
                    return@trace null
                }
            }
            return@trace bitmap
        }
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun decodeVideoThumbnailWithKeyFrame(mediaId: Int, filePath: String, size: Int): Bitmap? {
        var tmpParcelFileDescriptor: ParcelFileDescriptor? = null
        return runCatching {
            getThumbnailDecoder().use { videoThumbnailDecoder ->
                val contentUri = MediaStoreUriHelper.getVideoUri(filePath, mediaId.toString())
                GLog.d(TAG) { "[decodeVideoThumbnailWithKeyFrame] contentUri=$contentUri" }
                when {
                    mediaId <= 0 -> {
                        GLog.d(TAG, LogFlag.DL) { "[decodeVideoThumbnailWithKeyFrame] decode with filePath, " +
                                "filePath=${PathMask.mask(filePath)}" }
                        videoThumbnailDecoder.setDataSource(filePath)
                    }
                    (videoThumbnailDecoder is MeicamVideoThumbnailDecoder) -> {
                        // 美摄不支持fd,需要使用uri
                        GLog.d(TAG, LogFlag.DL) {
                            "[decodeVideoThumbnailWithKeyFrame] videoThumbnailDecoder is MeicamVideoThumbnailDecoder"
                        }
                        videoThumbnailDecoder.setDataSource(context, contentUri)
                    }
                    else -> {
                        context.contentResolver.openFileDescriptor(contentUri, "r")?.let { parcelFileDescriptor ->
                            GLog.d(TAG, LogFlag.DL) {
                                "[decodeVideoThumbnailWithKeyFrame] " +
                                        "openFileDescriptor fileDescriptor=${parcelFileDescriptor.fileDescriptor}"
                            }
                            videoThumbnailDecoder.setDataSource(parcelFileDescriptor.fileDescriptor)
                            tmpParcelFileDescriptor = parcelFileDescriptor
                        }
                    }
                }
                videoThumbnailDecoder.setThumbnailSize(size)
                videoThumbnailDecoder.decodeCoverBitmap()?.let {
                    // FIXME JinPeng 2020/10/10
                    DecodeUtils.ensureGLCompatibleBitmap(it)
                }
            }
        }.onFailure {
            GLog.e(TAG, "decodeVideoThumbnailWithKeyFrame, error", it)
        }.also {
            IOUtils.closeQuietly(tmpParcelFileDescriptor)
        }.getOrNull()
    }


    /**
     * 是否支持杜比视频走硬件解码
     * @return 返回知否支持硬件解码
     */
    private val isSupportDolbyVideoHardwareDecoder: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_DOLBY_VIDEO_HARDWARE_DECODER)
        } ?: false
    }

    /**
     * fix 9071494 解决杜比格式5视频缩图抽偏绿的问题
     * 是否支持杜比视频GPU方案
     */
    private val isSupportDolbyGPUDecoder: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(IS_SUPPORT_DOLBY_GPU_DECODE)
        } ?: false
    }

    /**
     * 是否为DX4平台
     */
    private val isDX4Platform: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(FEATURE_IS_DX4_PLATFORM)
        } ?: false
    }

    /**
     * 获取硬解配置
     */
    private fun getHardwareDecoderConfig(): HardwareDecoderConfig {
        val isSupportDolbyDecoder = isSupportDolbyVideoHardwareDecoder
        val isSupportDolbyGPUDecoder = isSupportDolbyGPUDecoder
        val isDX4Platform = isDX4Platform
        val isSupportDolbyGPUWithP010Decoder = isSupportDolbyDecoder && isSupportDolbyGPUDecoder && isDX4Platform
        return HardwareDecoderConfig(isSupportDolbyDecoder, isSupportDolbyGPUDecoder, isSupportDolbyGPUWithP010Decoder)
    }

    private fun getThumbnailDecoder(): VideoThumbnailDecoder {
        val decoderConfig = ThumbnailDecoderHelper.updateDecoderConfig(
            ThumbnailDecoderHelper.getDecoderConfig(originalResource.mediaItem, isSupportDolbyVideoHardwareDecoder),
            getHardwareDecoderConfig()
        )
        return VideoThumbnailDecoderUtils.getVideoThumbnailDecoder(decoderConfig, options.isSingleDolbyCodeDevice)
    }

    /**
     * 系统相册接入媒体库缓存缩略图
     * 判断小图 且(os16.0.0以上) 视频类型(把杜比视频给拦截，因为多媒体那边的缩图不支持杜比效果)
     * @return Boolean
     */
    private fun shouldDecodeFromMedia(options: ResourceGetOptions): Boolean {
        return options.decodeLowResolutionEnabled && options.inLowResolutionRetry.not()
                && ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType) && isAtLeastOS16
                && !VideoTypeUtils.isDolbyVideo(originalResource.mediaItem)
    }

    /**
     * 从多媒体视频缩略图路径解析Bitmap
     */
    private fun getMediaThumbnail(): Bitmap? {
        kotlin.runCatching {
            val checkFile = FileOperationUtils.checkFileAlreadyExit((originalResource.mediaId.toString() + JPG), MEDIA_MOVIES_THUMBNAIL_RELATIVE_PATH)
            checkFile?.path?.let { path ->
                return BitmapFactory.decodeFile(path)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getMediaThumbnail mediaId:${originalResource.mediaId} error:${it.message}" }
        }
        return null
    }

    companion object {
        const val TAG = "LocalVideoBitmapRequester"
    }
}