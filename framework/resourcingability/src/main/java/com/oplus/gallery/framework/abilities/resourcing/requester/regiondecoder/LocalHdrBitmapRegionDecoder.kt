/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : BitmapRegionDecoderCompat.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/4/21 17:49
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/21  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Gainmap
import android.graphics.Rect
import android.net.Uri
import android.os.Build
import androidx.annotation.RequiresApi
import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_LINEAR_MASK
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_META_DATA
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.copyRChannelToAlpha8
import com.oplus.gallery.foundation.util.ext.resizeWithoutContentScale
import com.oplus.gallery.foundation.util.ext.toShortString
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.math.MathUtil.roundToInt
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.bitmap.BitmapRegionDecoder
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.LHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.convertToGainmap

/**
 * Local HDR资源区域解码器，支持gainmap的区域解码
 */
class LocalHdrBitmapRegionDecoder(
    private val contentRegionDecoder: IRegionDecoder,
    filePath: String,
    context: Context,
    contentUri: Uri
) : IRegionDecoder {
    private var metadata: ByteArray? = null
    private var gainmapContentsDecoder: IRegionDecoder? = null

    private val imageWidth = contentRegionDecoder.getWidth()
    private val imageHeight = contentRegionDecoder.getHeight()
    private var gainmapWidth = 0
    private var gainmapHeight = 0

    init {
        runCatching {
            FileExtendedContainer().use {
                it.setDataSource(context, contentUri, openFileMode = OpenFileMode.MODE_READ)
                metadata = it.getExtensionData(EXTEND_KEY_LOCAL_HDR_META_DATA)
                it.getExtensionData(EXTEND_KEY_LOCAL_HDR_LINEAR_MASK)?.let { maskData ->
                    gainmapContentsDecoder = BitmapRegionDecoder(maskData, 0, maskData.size)
                    gainmapWidth = gainmapContentsDecoder?.getWidth() ?: 0
                    gainmapHeight = gainmapContentsDecoder?.getHeight() ?: 0
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "init. create failed. path=${PathMask.mask(filePath)}" }
        }
    }

    override fun createDecoder() {
        contentRegionDecoder.createDecoder()
        gainmapContentsDecoder?.createDecoder()
    }

    override fun destroyDecoder() {
        contentRegionDecoder.destroyDecoder()
        gainmapContentsDecoder?.destroyDecoder()
        metadata = null
    }

    override fun isYuvImage(fd: Int): Boolean {
        return contentRegionDecoder.isYuvImage(fd)
    }

    override fun decodeRegion(rect: Rect, options: BitmapFactory.Options?, isDirectBuffer: Boolean): ImageData? {
        return contentRegionDecoder.decodeRegion(rect, options, isDirectBuffer)?.apply {
            this.bitmap?.let {
                if (ApiLevelUtil.isAtLeastAndroidU() && (it.gainmap == null)) {
                    it.gainmap = decodeGainRegion(it, rect, options)
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun decodeGainRegion(
        regionImage: Bitmap,
        imageDecodeRegion: Rect,
        originOptions: BitmapFactory.Options?
    ): Gainmap? {
        runCatching {
            val gainMetadata = metadata ?: run {
                GLog.e(TAG, LogFlag.DL) { "decodeGainRegion. No gainmap metadata. Skip." }
                return null
            }
            val gainDecoder = gainmapContentsDecoder ?: run {
                GLog.e(TAG, LogFlag.DL) { "decodeGainRegion. No gainmap decoder. Skip." }
                return null
            }
            if ((imageWidth <= 0) || (imageHeight <= 0) || (gainmapWidth <= 0) || (gainmapHeight <= 0)) {
                GLog.e(TAG, LogFlag.DL) {
                    "decodeGainRegion. Invalid size. image=${imageWidth}x$imageHeight. gainmap=${gainmapWidth}x$gainmapHeight. Skip."
                }
                return null
            }

            val scaleW = gainmapWidth * 1f / imageWidth
            val scaleH = gainmapHeight * 1f / imageHeight
            val gainDecodeRegion = Rect(
                (imageDecodeRegion.left * scaleW).roundToInt(),
                (imageDecodeRegion.top * scaleH).roundToInt(),
                (imageDecodeRegion.right * scaleW).roundToInt(),
                (imageDecodeRegion.bottom * scaleH).roundToInt()
            )
            val options = BitmapFactory.Options().apply {
                // Local HDR图片中目前存放的灰图编码为单通道8bit，解码用该config。
                inPreferredConfig = Bitmap.Config.ALPHA_8
                inSampleSize = originOptions?.inSampleSize ?: 1
            }
            val decodeStart = System.currentTimeMillis()
            gainDecoder.decodeRegion(gainDecodeRegion, options)?.bitmap?.let { gainContents ->
                // 部分机型无法区域解码出alpha8格式gainmap, 需要手动转换成alpha8格式
                val alpha8GainContents = if (gainContents.config == Bitmap.Config.ARGB_8888) {
                    gainContents.copyRChannelToAlpha8() ?: run {
                        GLog.e(TAG, LogFlag.DL) { "decodeGainRegion. copyRChannelToAlpha8 failed. ${gainContents.toShortString()}" }
                        return null
                    }
                } else gainContents

                /**
                 * regionImage通常都是固定1024x1024图片，如果外部没有输入等比例的inGainBitmap，此处解出的gain会是实际内容比例，
                 * 如果不进行尺寸校正，会出现gain图和原图显示没对齐问题
                 */
                val desireWidth = gainDecodeRegion.width() / options.inSampleSize
                val desireHeight = gainDecodeRegion.height() / options.inSampleSize
                val resizedGainContents = if ((alpha8GainContents.width != desireWidth) || (alpha8GainContents.height != desireHeight)) {
                    val inBitmap = BitmapPools.getBitmap(desireWidth, desireHeight, alpha8GainContents.config)
                    alpha8GainContents.resizeWithoutContentScale(inBitmap, desireWidth, desireHeight).apply {
                        if (this != alpha8GainContents) BitmapPools.recycle(alpha8GainContents)
                    }
                } else alpha8GainContents
                val decodeEnd = System.currentTimeMillis()
                val lhdrDrawable = HdrImageDrawable(resizedGainContents, LHdrMetadataPack(gainMetadata))
                val uhdrGainmap = lhdrDrawable.convertToGainmap(regionImage)
                GLog.d(TAG, LogFlag.DL) {
                    "decodeGainRegion. success. decodeRegion=$gainDecodeRegion, " +
                        "gain#${alpha8GainContents.toShortString()}, resizedGain#${resizedGainContents.toShortString()}," +
                        "decodeTime=${decodeEnd - decodeStart}, convertTime=${System.currentTimeMillis() - decodeEnd}"
                }
                if (alpha8GainContents != resizedGainContents) {
                    alpha8GainContents.recycle()
                }
                return uhdrGainmap
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "decodeGainRegion. Failed. $imageDecodeRegion" }
        }
        return null
    }

    override fun getWidth(): Int {
        return contentRegionDecoder.getWidth()
    }

    override fun getHeight(): Int {
        return contentRegionDecoder.getHeight()
    }

    override fun isRecycled(): Boolean {
        return contentRegionDecoder.isRecycled()
    }

    override fun isValid(): Boolean {
        return contentRegionDecoder.isValid()
    }

    override fun getDecoderName(): String {
        return contentRegionDecoder.getDecoderName()
    }

    override fun isSupportMultiRegionDecode(): Boolean {
        return contentRegionDecoder.isSupportMultiRegionDecode()
    }

    override fun setTombstone(tombstone: BreakpadTombstone) {
        contentRegionDecoder.setTombstone(tombstone)
    }

    override fun getTombstone(): BreakpadTombstone? {
        return contentRegionDecoder.getTombstone()
    }

    companion object {
        private const val TAG = "LocalHdrBitmapRegionDecoder"
    }
}