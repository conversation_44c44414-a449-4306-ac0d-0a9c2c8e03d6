/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : UriLargeImageRequest.kt
 ** Description : Uri图片资源获取区域图片解码请求.
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** JinPeng@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.cache.imagerequest

import android.content.Context
import android.net.Uri
import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions

class UriLargeImageRequest(
    context: Context,
    session: WorkerSession,
    path: Path,
    uri: Uri,
    options: ResourceGetOptions,
    contentType: String,
    uriItem: BaseUriItem
) : UriCacheRequest(context, session, uri, options, contentType, uriItem), Job<IRegionDecoder> {

    override fun call(jc: JobContext): IRegionDecoder? {
        if (!prepareInputFile(jc)) {
            return null
        }
        /*
         * BitmapRegionDecoder decoder =
         * DecodeUtils.createBitmapRegionDecoder(
         * jc, mFileDescriptor.getFileDescriptor(), false);
         * LiuJunChao modify for MPO file
         */
        var decoder: IRegionDecoder? = null
        try {
            // in android 7.1, decodeFileDescriptor will cause out of memory, if the uri is a file, use decodeFile instead
            val tombstone = BreakpadTombstone(uriItem.filePath, uriItem.dateModifiedInSec)
            decoder = if (isSharable()) {
                pfd?.fileDescriptor?.let {
                    DecoderWrapper.getIRegionDecoder(it)
                }
            } else {
                pfd?.fileDescriptor?.let {
                    DecoderWrapper.getIRegionDecoder(it)
                }
            }
            decoder?.setTombstone(tombstone)
        } catch (t: Throwable) {
            GLog.e(TAG, "RegionDecoderJob", t)
        }
        dealReload()

        return decoder?.apply {
            uriItem.width = getWidth()
            uriItem.height = getHeight()
        }
    }

    override fun updateContentIfNeeded() {
        // do nothing
    }

    override fun updateThumbnailTargetSize(): Int = 0

    override fun decodeThumbnailFromFile(jc: JobContext, targetSize: Int): ImageData? = null

    companion object {
        const val TAG = "UriLargeImageRequest"
    }
}