/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FastCaptureBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ColorSpace
import androidx.annotation.VisibleForTesting
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.cache.diskcache.DiskCacheManagerService
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.diskcache.DiskLruCacheLoader
import com.oplus.gallery.foundation.cache.diskcache.DiskLruCacheUri
import com.oplus.gallery.foundation.effect.BlurHelper
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.clearGainmap
import com.oplus.gallery.foundation.util.ext.hasGainmapCompat
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKey
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.resourcing.crop.isNoCrop
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 * 快拍相关Bitmap资源请求基类
 */
internal class FastCaptureBitmapRequester(
    private val context: Context,
    private val resourceKey: LocalMediaResourceKey,
    private val options: ResourceGetOptions,
    private val abilityBus: IAbilityBus,
    private val cachingAbility: ICachingAbility?,
    private val colorManagementAbility: IColorManagementAbility?,
    private val fastCaptureDetector: FastCaptureDetector,
    private val cancelable: ICancelable?
) : IBitmapRequester {

    private val targetSize: Int = ThumbnailSizeUtils.getTargetSizeInType(options.inThumbnailType)
    private val isCanceled: Boolean = cancelable?.isCancelled() == true

    private val cacheKey: CacheKey? by lazy {
        /**
         * Marked by zhangwenming，注意这里的cachekey并没有实际区分出来是tmp和quick
         * 当前CacheKey中也没有类似自定义字段来让业务端做区分，后面改造CachingAbility的时候，需要考虑到这点。
         * 加上cropParams，避免无影连拍使用裁剪过的显示，导致连拍的缩图轴没有水印
         */
        resourceKey.path?.let {
            CacheKeyFactory.createLocalMediaCacheKey(path = it, cropParams = options.inCropParams)
        }
    }

    /**
     * 是否支持exif缩图，这个值不会变化，所以用 by lazy
     */
    private val isSupportExifThumbnail: Boolean by lazy {
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            it.getBooleanConfig(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_CAMERA_JPEG_EXIF_THUMBNAIL)
        } == true
    }

    override fun request(): ImageResult<Bitmap>? {
        val startTime = System.currentTimeMillis()
        var resultBitmap = GTrace.trace("$TAG.getBitmapFromCache($cacheKey.${options.inThumbnailType}.$isCanceled)") {
            getBitmapFromCache()
        }
        if (isCanceled) {
            return null
        }

        //根据缓存结果，确定是否需要重新decode。
        if (resultBitmap == null) {
            // 解码
            val rawBitmap = GTrace.trace("$TAG.requestSource") {
                requestSource()
            }
            resultBitmap = if (!options.inCropParams.isNoCrop()) {
                /**
                 * 中心矩形裁剪，跟AbsGalleryImageBitmapRequester的decodeLocalFile方法中的操作保持一致
                 * 否则大图缩图轴中小缩图在quick图阶段和原图阶段会显示不一致
                 */
                BitmapUtils.resizeAndCropCenter(rawBitmap, targetSize, true)
            } else rawBitmap

            // 存储到缓存
            resultBitmap?.apply { GTrace.trace("$TAG.saveBitmapToCache") { saveBitmapToCache(this) } }
        }
        //模糊图片
        resultBitmap = resultBitmap?.let { blurBitmapIfNeeded(it) }
        return resultBitmap?.let { ImageResult(it, lowResolution = options.outLowResolution) }.apply {
            GLog.d(TAG, LogFlag.DL) {
                "<request> total=${GLog.getTime(startTime)}ms, id=${resourceKey.itemId}, $options, " +
                    "size=${resultBitmap?.width}x${resultBitmap?.height}, hasGain=${resultBitmap?.hasGainmapCompat()}"
            }
        }
    }

    /**
     * 解码
     * */
    private fun requestSource(): Bitmap? {
        val bitmap = getBitmapFromDiskCacheIfPermitted()?.let {
            GLog.d(TAG, LogFlag.EMPTY) {
                "<requestSource> got bitmap from disk cache, bmpWidth=${it.width}, bmpHeight=${it.height} " +
                    "resPath=${resourceKey.path}"
            }
            it
        } ?: getBitmapFromOriginalFile() ?: kotlin.run {
            GLog.e(TAG, LogFlag.DF) { "<requestSource> get bitmap from origin file fail! return null" }
            null
        }

        // OS15及以下版本，有水印时，相机写入quick图的gainmap位置错误，暂不支持提亮
        if (ApiLevelUtil.isApiLevelAtLeastB().not() && (bitmap?.hasGainmapCompat() == true)) {
            bitmap.clearGainmap()
            GLog.w(TAG, LogFlag.DL) { "requestSource. clean gainmap when apilevel < B." }
        }

        return bitmap
    }

    /**
     * 从缓存中读取
     * */
    private fun getBitmapFromCache(): Bitmap? {
        val cacheKey = cacheKey ?: let {
            GLog.w(TAG, LogFlag.DL) { "[getBitmapFromCache] can't create CacheKey, resPath=${resourceKey.path}" }
            return null
        }

        val cacheOptions = CacheOptions(
            inThumbnailType = options.inThumbnailType,
            inStorageType = StorageType.MEMORY_AND_DISK,
            inBlurRadius = getCacheOptionBlurRadius()
        )

        return cachingAbility?.let { cacheAbility ->
            if (isUseScreenNailCache(options.inThumbnailType)) {
                cacheAbility.screenNailCache?.getBitmap(cacheKey, cacheOptions)
            } else {
                cacheAbility.thumbnailCache?.getBitmap(cacheKey, cacheOptions) ?: let {
                    // 尝试从内存中的大缩图 压缩 小缩图，以节省时间
                    cacheAbility.screenNailCache?.getBitmap(
                        cacheKey,
                        cacheOptions.copy(
                            // 使用quick图保存(FastCaptureManager.trySaveThumbnail)时的Options配置获取大缩图
                            inThumbnailType = ThumbnailSizeUtils.getQuickBitmapThumbnailKey(),
                            inStorageType = StorageType.MEMORY_ONLY
                        )
                    )?.let { bitmap ->
                        GLog.d(TAG, LogFlag.DL) { "[getBitmapFromCache] quick cache bitmap:${bitmap.width} ${bitmap.height}" }
                        BitmapUtils.scaleBitmap(bitmap, targetSize, targetSize)
                    }
                }
            }
        }
    }

    /**
     * 判断是否使用ScreenNail缓存。
     *
     * 以下视为非ScreenNail：
     * 小缩图/视频截帧小缩图/扩展缩图。
     * @param type 缩图类型
     * @return Boolean 结果
     */
    private fun isUseScreenNailCache(type: Int): Boolean {
        return ThumbnailSizeUtils.isMicroThumbnailKey(type).not() &&
                ThumbnailSizeUtils.isVideoMicroThumbnailKey(type).not() &&
                ThumbnailSizeUtils.isExtendThumbnailKey(type).not()
    }

    private fun isMicroThumbnailKey(type: Int) = ThumbnailSizeUtils.isMicroThumbnailKey(type) && ThumbnailSizeUtils.isVideoMicroThumbnailKey(type)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun getBitmapFromDiskCacheIfPermitted(): Bitmap? {
        if (CacheOperation.ReadDiskCache !in options.inCacheOperation) {
            GLog.d(TAG, LogFlag.DL) { "[getBitmapFromDiskCacheIfPermitted] not permitted, resPath=${resourceKey.path}" }
            return null
        }

        if (isCanceled) {
            GLog.d(TAG, LogFlag.DL) { "[getBitmapFromDiskCacheIfPermitted] cancel get from disk cache, resPath=${resourceKey.path}" }
            return null
        }

        val uri = fastCaptureDetector.getFastCaptureFileUri(context)
        val diskLruCacheUri = uri.let(::DiskLruCacheUri)
        if (diskLruCacheUri.isInvalidUri().not()) {
            val startTime = System.currentTimeMillis()
            val lruCache = GTrace.trace("loadLruCacheBitmap.$targetSize") {
                DiskLruCacheLoader.loadBitmap(diskLruCacheUri)
            }
            GLog.d(TAG, LogFlag.DF) {
                val successStatus = if (lruCache != null) "success" else "failed "
                "[getBitmapFromDiskCacheIfPermitted] " +
                        "decode $successStatus from lruCache, " +
                        "resPath=${resourceKey.path}, " +
                        "cost time=${GLog.getTime(startTime)}"
            }
            lruCache?.let { return lruCache }
        }

        when {
            fastCaptureDetector.isTemporaryQuick() -> {
                val startTime = System.currentTimeMillis()
                val quickCache = GTrace.trace("getQuickCacheData.$targetSize") {
                    // 相机支持exif缩图，此时请求小缩图，优先走exif 缩图解析
                    val decodeFromExifFirst = isSupportExifThumbnail and isMicroThumbnailKey(options.inThumbnailType)
                    DiskCacheManagerService.getQuickCacheData(resourceKey.mediaName, uri, targetSize, decodeFromExifFirst)
                }
                GLog.d(TAG, LogFlag.DF) {
                    val successStatus = if (quickCache != null) "success" else "failed "
                    "[getBitmapFromDiskCacheIfPermitted] " +
                            "decode $successStatus from quickCache, " +
                            "resPath=${resourceKey.path}, " +
                            "cost time=${GLog.getTime(startTime)}"
                }
                quickCache?.let { return quickCache }
            }
            fastCaptureDetector.isTemporaryTmp() -> {
                val startTime = System.currentTimeMillis()
                val tmpCache = GTrace.trace("getBurstCacheData.$targetSize") {
                    DiskCacheManagerService.getBurstCacheData(resourceKey.mediaName, uri)
                }
                GLog.e(TAG, LogFlag.DF) {
                    val successStatus = if (tmpCache != null) "success" else "failed "
                    "[getBitmapFromDiskCacheIfPermitted] " +
                            "decode $successStatus from tmpCache, " +
                            "resPath=${resourceKey.path}, " +
                            "cost time=${GLog.getTime(startTime)}"
                }
                tmpCache?.let { return tmpCache }
            }
        }
        return null
    }

    private fun saveBitmapToCache(bitmap: Bitmap) {
        cachingAbility?.let { cacheAbility ->
            val start = System.currentTimeMillis()
            kotlin.runCatching {
                adjustColorSpace(bitmap)
            }.onFailure {
               GLog.e(TAG, LogFlag.DL) { "<saveBitmapToCache> failed to adjustColorSpace, msg=$it" }
            }
            val cacheKey = cacheKey ?: let {
                GLog.w(TAG, LogFlag.DL) { "<saveBitmapToCache> can't create CacheKey, resPath=${resourceKey.path}" }
                return
            }

            val quality = options.inStorageQuality
            val cacheOptions = CacheOptions(
                inThumbnailType = options.inThumbnailType,
                inStorageQuality = quality,
                inStorageType = StorageType.DISK_ONLY,
                inBlurRadius = getCacheOptionBlurRadius()
            )
            val result = if (isUseScreenNailCache(options.inThumbnailType)) {
                cacheAbility.screenNailCache?.putBitmap(cacheKey, bitmap, cacheOptions)
            } else {
                cacheAbility.thumbnailCache?.putBitmap(cacheKey, bitmap, cacheOptions)
            }
            GLog.i(TAG, LogFlag.EMPTY) { "<saveBitmapToCache> result=$result, consume=${GLog.getTime(start)}ms" }
        } ?: let {
            GLog.e(TAG, LogFlag.DL, "Cache ability is null when save cache.")
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun getBitmapFromOriginalFile(): Bitmap? {
        if (isCanceled) {
            return null
        }

        return LocalImageBitmapRequester(
            context,
            resourceKey,
            options,
            abilityBus,
            cachingAbility,
            colorManagementAbility,
            cancelable
        ).request()?.result.apply {
            GLog.d(
                TAG,
                LogFlag.DL
            ) { "[getBitmapFromOriginalFile] decode ${if (this != null) "success" else "failed "} from file, resPath=${resourceKey.path}" }
        }
    }

    private fun blurBitmapIfNeeded(bitmap: Bitmap): Bitmap {
        val blurRadius = getCacheOptionBlurRadius()
        if (blurRadius <= 0) {
            return bitmap
        }
        return BlurHelper.getInstance(context).createBlurBitmap(bitmap, blurRadius).apply { bitmap.recycle() }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun adjustColorSpace(bitmap: Bitmap) {
        colorManagementAbility?.let {
            if (it.isWideColorGamutEnabled == false) {
                it.adjustColorSpace(bitmap, ColorSpace.get(ColorSpace.Named.SRGB))
            }
        }
    }

    /**
     * 获取缓存所用的模糊半径参数
     * @return
     */
    private fun getCacheOptionBlurRadius(): Float {
        if (options.inBlurRadius != ResourceGetOptions.INVALID_BLUR_RADIUS) {
            return options.inBlurRadius
        }

        return CacheOptions.DEFAULT_BLUR_RADIUS
    }

    private companion object {
        private const val TAG = "FastCaptureBitmapRequester"
    }
}