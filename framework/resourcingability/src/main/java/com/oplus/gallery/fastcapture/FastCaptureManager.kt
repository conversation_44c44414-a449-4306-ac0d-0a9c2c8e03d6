/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : FastCaptureManager.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/08/13
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2022/08/13  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.fastcapture

import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.os.IBinder
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.cache.imagerequest.ImageCacheRequest
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.fastcapture.FastCaptureManager.RemoteBitmapProxy
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.RECYCLER_RELATIVE_PATH_R
import com.oplus.gallery.foundation.effect.BlurHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.addDebugTextWithGain
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceSaveOptions
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import kotlin.math.max
import kotlin.system.measureTimeMillis
import com.oplus.gallery.bitmap.ICameraTransBitmap as IOplusCameraTransBitmap
import com.oppo.gallery3d.bitmap.ICameraTransBitmap as IOutdatedCameraTransBitmap

/**
 * 用于执行快拍缩图相关的功能，包含功能：
 *
 * - 检测 [com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem] 是否为快拍
 * - 从 [Intent] 里面解析快拍缩图并缓存到内存和磁盘
 *
 * 当前仅为实现功能，未经过设计，后续整个 ResourcingAbility 除了公开接口外，内部实现需要重构
 */
internal class FastCaptureManager(context: Context, abilityBus: IAbilityBus) {

    private val saveThumbnailLock: Any = Any()
    private val cachingAbility: ICachingAbility? by lazy { abilityBus.requireAbility(ICachingAbility::class.java) }
    private val blurHelper: BlurHelper = BlurHelper.getInstance(context.applicationContext)

    /**
     * 检测 [com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem] 是否为快拍
     *
     * @return 如果是快拍，则会返回 [FastCaptureDetector] 对象，否则返回 null
     */
    fun createFastCaptureDetectorIfNeeded(resourceKey: LocalMediaResourceKey): FastCaptureDetector? {
        var filePath: String? = resourceKey.filePath
        if (filePath.isNullOrEmpty()) {
            filePath = LocalMediaDataHelper.getLocalMediaItem(resourceKey.path)?.filePath
        }
        // 错峰的图，我们可以直接操作媒体库，之后_data会被更新为.trashed-xxx，如果直接基于这个去相机查数据是查不到的，相机保存的是之前通知我们的xxx，所以需要转换
        filePath = filePath?.let {
            if (it.contains(RECYCLER_RELATIVE_PATH_R)) {
                FilePathUtils.parsePendingOrTrashedFilePath(it)
            } else it
        }
        filePath ?: let {
            GLog.d(TAG) { "[createFastCaptureDetectorIfNeeded] ignore, filePath is null or empty, resourceKey=$resourceKey" }
            return null
        }
        return FastCaptureDetector(
            filePath,
            fileSize = resourceKey.fileSize,
            width = resourceKey.width,
            height = resourceKey.height,
            tagFlags = resourceKey.tagFlags
        ).detector().run { if (isFastCapture()) this else null }
    }

    fun release() {
        cachingAbility?.close()
    }

    /**
     * 从 [Intent] 里面解析快拍缩图并缓存到内存，包含 OPPO 和 OnePlus 两种快拍图
     */
    fun trySaveThumbnail(
        resourceKey: LocalMediaResourceKey,
        intent: Intent,
        options: ResourceSaveOptions
    ): Bitmap? = synchronized(saveThumbnailLock) {
        // 快拍图，不需要磁盘缓存
        val cacheOptions = CacheOptions(
            inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(),
            inStorageType = StorageType.MEMORY_ONLY
        )

        GLog.d(TAG) { "[trySaveThumbnail] resourceKey=$resourceKey" }
        var targetBitmap = tryGetParcelBitmap(resourceKey, intent)
            ?: tryGetRemoteBitmap(resourceKey, intent, cacheOptions)
            ?: tryGetSharedBitmap(resourceKey, intent)
            ?: let {
                GLog.d(TAG) { "[trySaveThumbnail] ignore, thumbnail not found." }
                clearFastCaptureExtras(intent)
                return null
            }
        targetBitmap = targetBitmap.addQuickDebugInfo()

        clearFastCaptureExtras(intent)

        if (options.thumbnailList.isEmpty().not()) {
            val bitmapLongSize = targetBitmap.width.coerceAtLeast(targetBitmap.height)
            val targetLongSize = bitmapLongSize.coerceAtMost(options.thumbnailList[0])
            cacheOptions.inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(targetLongSize)
        }
        saveBitmapToCaching(resourceKey, targetBitmap, cacheOptions).also {
            // 保存当前quick bitmap尺寸到全局共享对象
            ThumbnailSizeUtils.setQuickBitmapThumbnailKey(cacheOptions.inThumbnailType)
            GLog.d(TAG) { "[trySaveThumbnail] save bitmap to caching isSaveSuccessful=$it" }
        }
        return targetBitmap
    }

    private fun Bitmap.addQuickDebugInfo(): Bitmap {
        return if (GProperty.DEBUG_PHOTO_PAGE_BITMAP_DEBUG_INFO) {
            val targetBmp = if (!isMutable) copy(getConfigSafely(), true) else this
            targetBmp.addDebugTextWithGain("QuickFromIntent", MathUtils.TWO)
        } else {
            this
        }
    }

    /**
     * 相机相册一体化-Intent传递Quick图
     */
    private fun tryGetParcelBitmap(
        resourceKey: LocalMediaResourceKey,
        intent: Intent,
    ): Bitmap? {
        runCatching {
            val time = System.currentTimeMillis()
            val clipData: ClipData? = intent.getClipData()
            if (clipData == null) {
                GLog.d(TAG, LogFlag.DL) { "[tryGetParcelBitmap] ignore, clipData is null." }
                return null
            }

            val item = clipData.getItemAt(0)
            if (item == null) {
                GLog.d(TAG, LogFlag.DL) { "[tryGetParcelBitmap] ignore, item is null." }
                return null
            }

            val intent = item.getIntent()
            if (intent == null) {
                GLog.d(TAG, LogFlag.DL) { "[tryGetParcelBitmap] ignore, intent is null." }
                return null
            }

            val bundle: Bundle? = intent.extras
            if (bundle == null) {
                GLog.d(TAG, LogFlag.DL) { "[tryGetParcelBitmap] ignore, bundle is null." }
                return null
            }

            var parcelBitmap = (bundle.get(KEY_PREVIEW_BITMAP) as? Bitmap) ?: let {
                GLog.d(TAG, LogFlag.DL) { "[tryGetParcelBitmap] ignore, bitmap is null." }
                return null
            }
            val parcelBitmapWidth = parcelBitmap.width
            val parcelBitmapHeight = parcelBitmap.height
            // quick最长边超出2048时进行缩放
            if (max(parcelBitmapWidth, parcelBitmapHeight) > MAX_QUICK_IMAGE_SIZE) {
                GLog.w(TAG, LogFlag.DL) { "<tryGetParcelBitmap> quick image is too large, try resizeByLongSide to MAX_QUICK_IMAGE_SIZE!" }
                parcelBitmap = BitmapUtils.resizeByLongSide(parcelBitmap, MAX_QUICK_IMAGE_SIZE, true)
            }
            GLog.i(TAG, LogFlag.DL) {
                "[tryGetParcelBitmap] got, " +
                        "targetBitmapSize=$parcelBitmapWidth x $parcelBitmapHeight, " +
                        "orientation=${resourceKey.orientation}, " +
                        "costTime=${System.currentTimeMillis() - time} ms"
            }
            return parcelBitmap
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[tryGetParcelBitmap] fail., e: ", it)
        }

        return null
    }

    private fun tryGetSharedBitmap(resourceKey: LocalMediaResourceKey, intent: Intent): Bitmap? {
        val sharedBitmapOrientation = intent.getIntExtra(KEY_SHARED_ELEMENT_ORIENTATION, 0)
        val sharedBitmapBytes = intent.getByteArrayExtra(KEY_SHARED_ELEMENT_THUMB) ?: let {
            GLog.d(TAG) { "[tryGetSharedBitmap] ignore, sharedBitmapBytes not found." }
            return null
        }

        val originalBitmap = BitmapFactory.decodeByteArray(sharedBitmapBytes, 0, sharedBitmapBytes.size)
        val originalBitmapWidth = originalBitmap.width
        val originalBitmapHeight = originalBitmap.height

        val targetBitmap = BitmapUtils.rotateBitmap(originalBitmap, sharedBitmapOrientation, true)
        val targetBitmapWidth = targetBitmap.width
        val targetBitmapHeight = targetBitmap.height

        GLog.i(TAG) {
            "[tryGetSharedBitmap] got, " +
                    "originalBitmapSize=$originalBitmapWidth x $originalBitmapHeight, " +
                    "targetBitmapSize=$targetBitmapWidth x $targetBitmapHeight, " +
                    "sharedBitmapOrientation=$sharedBitmapOrientation"
        }
        return targetBitmap
    }

    private fun tryGetRemoteBitmap(
        resourceKey: LocalMediaResourceKey,
        intent: Intent,
        cacheOptions: CacheOptions
    ): Bitmap? {
        runCatching {
            val time = System.currentTimeMillis()
            val remoteBitmapProxy = tryCreateRemoteBitmapProxy(intent) ?: let {
                GLog.d(TAG) { "[tryGetRemoteBitmap] ignore, bitmapProxy not found remoteBitmapProxy." }
                return null
            }

            val bitmapMapping = remoteBitmapProxy.getBitmapMapping() ?: let {
                GLog.d(TAG) { "[tryGetRemoteBitmap] ignore, bitmapMapping not found from remoteBitmapProxy." }
                return null
            }

            val remoteBitmap = findBitmapFromBitmapMapping(intent, bitmapMapping, cacheOptions) ?: let {
                GLog.d(TAG) { "[tryGetRemoteBitmap] ignore, bitmap not found from bitmapMapping." }
                return null
            }
            GLog.i(TAG) {
                "[tryGetRemoteBitmap] got, " +
                        "targetBitmapSize=${remoteBitmap.width} x ${remoteBitmap.height}, " +
                        "orientation=${resourceKey.orientation}, " +
                        "costTime=${System.currentTimeMillis() - time} ms"
            }
            return remoteBitmap
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[tryGetRemoteBitmap] fail., e: ", it)
        }
        return null
    }

    private fun tryCreateRemoteBitmapProxy(intent: Intent): RemoteBitmapProxy? {
        return intent.extras
            ?.getBinder(KEY_CAMERA_TRANS_BITMAP_BINDER)
            ?.asRemoteBitmapProxy()
    }

    private fun findBitmapFromBitmapMapping(intent: Intent, bitmapMapping: Map<String, Bitmap?>, cacheOptions: CacheOptions): Bitmap? {
        // 先尝试使用quick图
        var bitmap: Bitmap? = bitmapMapping[KEY_QUICK_BITMAP]?.apply {
            GLog.i(TAG) { "[findBitmapFromBitmapMapping] Use quick bitmap" }
        }

        // 如quick图不存在,则尝试使用temp图,此处的操作均有空安全判断
        if (bitmap == null) {
            val tempBitmap: Bitmap? = bitmapMapping[KEY_TEMP_BITMAP]
            val scaledTempBitmap: Bitmap? = BitmapUtils
                .resizeByLongSide(tempBitmap, ImageCacheRequest.CACHE_BITMAP_TARGET_SIZE, true)

            val integrationUIGestureToken = IntentUtils.getBinderExtra(intent, KEY_INTEGRATION_UI_GESTURE_TOKEN)
            val hasIntegrationUITransition = integrationUIGestureToken != null
            bitmap = if (hasIntegrationUITransition.not()) {
                cacheOptions.inBlurRadius = ImageCacheRequest.CACHE_BITMAP_BLUR_RADIUS
                blurHelper.createBlurBitmap(scaledTempBitmap, ImageCacheRequest.CACHE_BITMAP_BLUR_RADIUS)
            } else {
                scaledTempBitmap
            }
            GLog.i(TAG) { "[findBitmapFromBitmapMapping] Use temp bitmap, do blur is ${hasIntegrationUITransition.not()}" }
        }

        if (bitmap?.isRecycled == true) {
            GLog.d(TAG) { "[findBitmapFromBitmapMapping] Bitmap is recycled" }
            return null
        }

        return bitmap
    }

    private fun clearFastCaptureExtras(intent: Intent) {
        intent.removeExtra(KEY_SHARED_ELEMENT_THUMB)
        intent.removeExtra(KEY_SHARED_ELEMENT_ORIENTATION)
        intent.removeExtra(KEY_CAMERA_TRANS_BITMAP_BINDER)
        intent.clipData = null
    }

    private fun IBinder.asRemoteBitmapProxy(): RemoteBitmapProxy? {
        val time = System.currentTimeMillis()
        val result = when (interfaceDescriptor) {
            IOplusCameraTransBitmap.DESCRIPTOR -> {
                val remoteBitmapMappingInterface = IOplusCameraTransBitmap.Stub.asInterface(this)
                RemoteBitmapProxy { remoteBitmapMappingInterface.cameraBitmap }
            }
            IOutdatedCameraTransBitmap.DESCRIPTOR -> {
                val remoteBitmapMappingInterface = IOutdatedCameraTransBitmap.Stub.asInterface(this)
                RemoteBitmapProxy { remoteBitmapMappingInterface.cameraBitmap }
            }
            else -> {
                GLog.e(TAG) { "[asBitmapMappingProxy] Unknown Binder($interfaceDescriptor)" }
                null
            }
        }
        GLog.d(TAG, "[asRemoteBitmapProxy.Stub.asInterface] cost time = ${System.currentTimeMillis() - time} ms")
        return result
    }

    private fun saveBitmapToCaching(
        resourceKey: LocalMediaResourceKey,
        bitmap: Bitmap,
        cacheOptions: CacheOptions
    ): Boolean {
        GLog.d(TAG) { "[saveBitmapToCaching] saving. cacheOption=$cacheOptions" }

        val screenNailCache = cachingAbility?.screenNailCache ?: let {
            GLog.w(TAG) { "[saveBitmapToMemCaching] ignore, no screenNailCache." }
            return false
        }

        /**
         * 杜比视频解码慢，必须依赖相机 Binder 传过来的缩图用于快速显示。
         *
         * 如果 [LocalMediaItem] 是视频，并且尺寸小于 [MIN_VIDEO_CACHE_SIZE]，说明是个比较糊的图，不是 quick 类型的，
         * 在 wukong 机型（包含有相机进相册动画优化需求）上，如果将这个图缓存使用，特殊路径下会出现 清晰 -》糊 -》清晰 的情况。
         *
         * 操作路径（wukong 机型）：清晰 -》糊 -》清晰 的情况
         *
         * - 相机拍摄杜比视频，按 home 键回到桌面
         * - 再点击相机图标进入相机
         * - 点击相机缩图进入相册大图
         *
         * 为应对上述情况，已找相机伙伴（曾文鸿）沟通，建议在上述路径下进入大图时，传入的图片也是 quick 类型，不要太糊
         */
        if (resourceKey.mediaItem.isVideo && (maxOf(bitmap.width, bitmap.height)) < MIN_VIDEO_CACHE_SIZE) {
            GLog.d(TAG) { "[saveBitmapToMemCaching] ignore, video cache must not less than size($MIN_VIDEO_CACHE_SIZE)." }
            return false
        }

        /**
         * 杜比视频解码慢，必须依赖相机 Binder 传过来的缩图用于快速显示。
         *
         * 但是针对每个 mediaId，对应的 [LocalMediaItem] 是全局唯一的，存在同步问题，
         * 存在的情况是：保存缩图时 dateModifiedInSecond 是 0；但是取缩图时就变成了其他值，导致匹配不到缓存 key。在杜比视频场景下，概率较高。
         *
         * 同时，视频和图片不同，视频是没有磁盘 tmp、qucik 的，只能通过相机 Binder 获取；
         *
         * 为针对上述情况，如果发现 isVideo && dateModifiedInSecond == 0，则强制更新一波，推
         */
        var targetMediaItem: LocalMediaItem = resourceKey.mediaItem
        if (resourceKey.mediaItem.isVideo && (resourceKey.dateModifiedInSecond == 0L)) {
            val syncTime = measureTimeMillis {
                targetMediaItem = LocalMediaDataHelper.getLocalMediaItem(resourceKey.path, true) as? LocalMediaItem ?: targetMediaItem
            }
            GLog.i(TAG) { "[saveBitmapToMemCaching] sync, syncTime=$syncTime." }
        }

        val cacheKey = when {
            targetMediaItem.isVideo -> CacheKeyFactory.createLocalMediaCacheKey(targetMediaItem)
            else -> resourceKey.path?.let(CacheKeyFactory::createLocalMediaCacheKey)
        } ?: let {
            GLog.w(TAG) { "[saveBitmapToMemCaching] can't create CacheKey." }
            return false
        }

        val cacheResult = screenNailCache.putBitmap(cacheKey, bitmap, cacheOptions)
        return cacheResult?.isSuccess() ?: false
    }

    private fun interface RemoteBitmapProxy {
        fun getBitmapMapping(): MutableMap<String, Bitmap?>?
    }

    private companion object {
        private const val TAG = "FastCaptureManager"

        const val KEY_CAMERA_TRANS_BITMAP_BINDER = "key_camera_transfer_bitmap_to_gallery"
        const val KEY_TEMP_BITMAP = "tempBitmap"
        const val KEY_QUICK_BITMAP = "quickBitmap"
        const val KEY_SHARED_ELEMENT_THUMB = "SharedElementThumb"
        const val KEY_SHARED_ELEMENT_ORIENTATION = "Orientation"
        const val KEY_PREVIEW_BITMAP = "preview_bitmap"

        /**
         * 一体化 UI 动画的触摸手势代理 token 的 key 值，目前此参数为相机所用
         * 此参数在 PhotoInputArgumentsViewModel 里也有定义，如修改需同步修改
         */
        private const val KEY_INTEGRATION_UI_GESTURE_TOKEN = "com.oplus.camera.inputtoken"

        private const val MIN_VIDEO_CACHE_SIZE = 540

        /**
         * 相机进相册场景，存在相机会传入过大的bitmap(7000*8000及更大的)
         * 相册准备缩图时会从相机获取，此时就会上传超大的纹理，造成滑动卡顿，
         * 因此相册需要限制quick图的最大尺寸，当前按经验默认指定最长边为2048
         * */
        private const val MAX_QUICK_IMAGE_SIZE = 2048
    }
}
