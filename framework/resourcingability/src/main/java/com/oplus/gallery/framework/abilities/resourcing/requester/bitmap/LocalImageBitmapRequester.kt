/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalImageBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.model.data.base.item.isSupportCameraThumbInfo
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.toShortString
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.standard_lib.file.File

/**
 * LocalImage，Bitmap资源加载请求的实现类。
 */
internal class LocalImageBitmapRequester(
    context: Context,
    key: LocalMediaResourceKey,
    options: ResourceGetOptions,
    abilityBus: IAbilityBus,
    cachingAbility: ICachingAbility?,
    colorManagementAbility: IColorManagementAbility?,
    cancelable: ICancelable?
) : AbsGalleryImageBitmapRequester(context, key, options, abilityBus, cachingAbility, colorManagementAbility, cancelable, requesterBySync = true) {

    override fun getTag(): String = TAG

    override fun request(): ImageResult<Bitmap>? {
        return kotlin.runCatching {
            /*
             * 原提交：2018/3/27 hash:8d632f3306f42ad3a0b7a08c35c4ff965ead3da4：
             * 大图打开heif图片显示慢，原因是heif不支持多线程解码，且单个heif解码时间过长，所以需要在解码照片详情页的缩图时，
             * 解码960图（作为大图的缩略图）当打开大图时，显示heif960缩图会快些
             * 而新增了240dp 320dp 360dp的缩图,当缩图的heif图片 （场景是：精选画廊）
             * 小于等于于TYPE_THUMBNAIL的960时，需要解码960的图片缓存起来(当点击查看大图时可以加快显示速度)，
             * 当大于960时,只在保存时将解码的图片压缩成960即可
             */
            val path = originalResource.path
            if ((ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType) ||
                    ThumbnailSizeUtils.isExtendThumbnailKey(options.inThumbnailType)) &&
                (path != null) && ImageTypeUtils.isHeif(path)
            ) {
                synchronized(path) {
                    super.request()
                }
            } else {
                super.request()
            }
        }.onFailure { exception ->
            val filePath = key.mediaItem.filePath
            var isExists = true
            if (File(filePath).exists().not()) {
                isExists = false
            }
            GLog.e(
                TAG, LogFlag.DL, "request: isRecycledItem = ${key.mediaItem.isRecycledItem}, "
                        + "mediaId = ${key.mediaId}, "
                        + "path = ${key.path}, "
                        + "filePath = ${PathMask.mask(filePath)}"
                        + "isExists = $isExists", exception
            )
        }.getOrNull()
    }

    override fun requestSource(): Bitmap? {
        return if (SourceOperation.ReadLocal in options.inSourceOperation) {
            decodeLocalFile(DecodeOption(originalResource))
        } else {
            GLog.e(TAG, LogFlag.DL, "requestSource bitmap is null")
            return null
        }
    }

    override fun getBitmapFromCameraThumbInfo(cacheAbility: ICachingAbility): Bitmap? {
        if (GProperty.SUPPORT_CAMERA_THUMB_SHARE.not()) return null
        val screenNailCache = cacheAbility.screenNailCache ?: return null
        if (originalResource.mediaItem.isSupportCameraThumbInfo().not()) return null

        val startTime = System.currentTimeMillis()
        val cacheKey = CacheKeyFactory.createLocalMediaCacheKey(
            path = SourceConstants.Cache.PATH_ITEM_THUMB_IMAGE.getChild(originalResource.mediaId),
            dateModifiedInSec = originalResource.dateModifiedInSecond,
            mimeType = originalResource.mimeType
        )
        val cacheOptions = CacheOptions(
            inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(ThumbnailSizeUtils.THUMB_SIZE_1440),
            inStorageType = StorageType.MEMORY_AND_DISK
        )
        return runCatching {
            screenNailCache.getBitmap(cacheKey, cacheOptions)?.let { bitmap ->
                if (debug) GLog.d(TAG, LogFlag.DL) { "[getBitmapFromCameraThumbInfo] get bitmap cost time:${GLog.getTime(startTime)}" }
                if (ThumbnailSizeUtils.isFullThumbnailKey(options.inThumbnailType)) {
                    bitmap
                } else {
                    val targetSize: Int = ThumbnailSizeUtils.getTargetSizeInType(options.inThumbnailType)
                    val time = System.currentTimeMillis()
                    BitmapUtils.scaleBitmap(bitmap, targetSize, targetSize).apply {
                        if (debug) {
                            GLog.d(TAG, LogFlag.DL) {
                                "[getBitmapFromCameraThumbInfo] scale cost time:${GLog.getTime(time)} " +
                                        "cache:${bitmap.toShortString()} scaled:${toShortString()} "
                            }
                        }
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "[getBitmapFromCameraThumbInfo] error: $it" }
        }.getOrNull().also {
            GLog.d(TAG, LogFlag.DL) {
                "[getBitmapFromCameraThumbInfo] cost time=${GLog.getTime(startTime)}" +
                        " cacheKey:$cacheKey cacheOptions:$cacheOptions ${it?.toShortString()}"
            }
        }
    }

    companion object {
        const val TAG = "LocalImageBitmapRequester"
    }
}