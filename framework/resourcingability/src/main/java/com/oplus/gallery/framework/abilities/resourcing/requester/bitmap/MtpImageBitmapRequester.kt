/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MtpImageBitmapRequester.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.graphics.Bitmap
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.ResourcingJobContext
import com.oplus.gallery.framework.abilities.resourcing.key.MtpResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult

/**
 *  Mtp Bitmap资源请求加载类
 */
internal class MtpImageBitmapRequester(
    private val mtpResource: MtpResourceKey,
    private val options: ResourceGetOptions,
    private val cancelable: ICancelable?
) : IBitmapRequester {
    override fun request(): ImageResult<Bitmap>? {
        val bytes: ByteArray? = if (ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType)) {
            mtpResource.mtpItem.thumbnailData ?: mtpResource.mtpItem.imageData
        } else {
            mtpResource.mtpItem.imageData
        }

        bytes ?: let {
            GLog.w(TAG, "decoding thumbnail failed bytes is null!")
            return null
        }

        runCatching {
            val resourcingJobContext = ResourcingJobContext(cancelable)
            CodecHelper.decodeThumbnailArray(resourcingJobContext, bytes, null, MTP_THUMBNAIL_SIZE, options.inThumbnailType)?.let {
                return if (ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType)) {
                    BitmapUtils.resizeAndCropCenter(it, MTP_THUMBNAIL_SIZE, true)
                } else {
                    BitmapUtils.resizeByLongSide(it, MTP_THUMBNAIL_SIZE, true)
                }.let { ImageResult(it) }
            }
        }.onFailure {
            GLog.w(TAG, "decoding thumbnail failed=${it.message}")
        }
        return null
    }

    companion object {
        const val TAG = "MtpImageRequest"
        const val MTP_THUMBNAIL_SIZE = 960
    }
}