/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ImageLoadingLockManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/3/6
 ** Author      : <PERSON><PERSON>.Zhu<PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>.<PERSON><PERSON>@Apps.Gallery3D     2023/12/07      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.util

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * 加载缩图同步锁管理类
 */
object ImageLoadingLockManager {

    private const val TAG = "ImageLoadingLockManager"

    /**
     * 存放加载相同图片相同尺寸时被持有锁的容器
     */
    private val countDownLatchMap by lazy {
        HashMap<Long, CountDownLatch>()
    }

    /**
     * 加载图片：
     * 1.如果相同尺寸图片没有其他任务在执行，直接从原文件中decode，加载完成后通知其他等待任务继续执行（其他任务直接从缓存中获取）
     * 2.如果相同尺寸图片已经有其他任务在执行了，则await等待被唤醒后从缓存中获取
     * @param key 通过mediaCacheKey转换来的用于标识相同资源尺寸的key
     * @param cacheBlock 从缓存中获取图片block方法块
     * @param sourceBlock 从原文件中decode图片并保存到缓存中block方法块
     */
    fun loadingMediaBitmap(
        key: Long,
        cacheBlock: () -> Boolean,
        sourceBlock: () -> Boolean
    ) {
        var countDownLatch: CountDownLatch? = null
        synchronized(countDownLatchMap) {
            countDownLatch = countDownLatchMap[key]
            if (countDownLatch == null) {
                countDownLatchMap[key] = CountDownLatch(1)
            }
        }

        countDownLatch?.let {
            GLog.d(TAG) { "[loadingMediaBitmap] key: $key await" }
            val completed = it.await(1, TimeUnit.SECONDS)
            if (completed) {
                //bug id:7908132 兜底，缓存中还不存在，再从原文件加载并保存到缓存
                if (cacheBlock().not()) {
                    GLog.w(TAG, LogFlag.DL) { "[loadingMediaBitmap] key: $key can not get bitmap from cache, need to decode" }
                    sourceBlock()
                } else {
                    GLog.d(TAG, LogFlag.DL) { "[loadingMediaBitmap] key: $key get bitmap from cache" }
                }
            } else {
                GLog.w(TAG) { "[loadingMediaBitmap] key: $key await timeout" }
                sourceBlock()
            }
        } ?: let {
            sourceBlock()
            synchronized(countDownLatchMap) {
                countDownLatchMap.remove(key)?.countDown()
            }
        }
    }
}