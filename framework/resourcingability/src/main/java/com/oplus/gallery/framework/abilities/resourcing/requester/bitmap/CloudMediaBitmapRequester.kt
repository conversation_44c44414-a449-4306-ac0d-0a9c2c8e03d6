/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CloudMediaBitmapRequester.kt
 ** Description:云端的图片和视频的Bitmap资源请求的实现类
 ** Version: 1.0
 ** Date : 2023/3/17
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2023/3/17        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cloud.DownloadData
import com.oplus.gallery.business_lib.cloud.DownloadSpec
import com.oplus.gallery.business_lib.cloud.TransferPriority
import com.oplus.gallery.business_lib.cloudsync.FileDownloadListener
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.fileaccess.FileConstants.MediaType.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.ext.parseId
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation

/**
 * 云端的图片和视频的Bitmap资源请求的实现类
 */
internal class CloudMediaBitmapRequester(
    context: Context,
    key: LocalMediaResourceKey,
    options: ResourceGetOptions,
    abilityBus: IAbilityBus,
    cachingAbility: ICachingAbility?,
    colorManagementAbility: IColorManagementAbility?,
    cancelable: ICancelable?,
    private val lifecycle: Lifecycle?
) : AbsGalleryImageBitmapRequester(context, key, options, abilityBus, cachingAbility, colorManagementAbility, cancelable) {

    override fun getTag(): String = TAG

    override fun requestSource(): Bitmap? {
        var bitmap: Bitmap? = null
        var downloadSpec: DownloadSpec? = null
        val bestDownloadSpec = getBestDownloadSpec()
        if (debug) {
            GLog.d(TAG, "[requestSource] $originalResource bestDownloadSpec=$bestDownloadSpec")
        }
        val cloudGlobalId: String = originalResource.cloudGlobalId ?: let {
            GLog.e(TAG, "[requestSource] miss cloudGlobalId")
            return null
        }
        val cloudFileId: String = originalResource.cloudFileId ?: let {
            GLog.e(TAG, "[requestSource] miss cloudFileId")
            return null
        }
        val fileMD5 = originalResource.fileMD5 ?: let {
            GLog.e(TAG, "[requestSource] miss fileMD5")
            return null
        }
        var asyncDownload = true
        ApiDmManager.getCloudSyncDM().getLargestCachedThumbFile(cloudGlobalId)?.also { cacheFile ->
            if (debug) {
                GLog.d(TAG, "[requestSource] $originalResource found cache file $cacheFile")
            }
            val isBest = cacheFile.downloadSpec >= bestDownloadSpec
            bitmap = if (SourceOperation.ReadLocal in options.inSourceOperation) {
                decodeLocalFile(
                    DecodeOption(
                        cacheFile.file.absolutePath,
                        mimeType = MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
                    ),
                    shouldSaveToCache = isBest
                )
            } else null
            if (!isBest) {
                downloadSpec = cacheFile.downloadSpec.getNext()
            }
        } ?: also {
            if (debug) {
                GLog.d(TAG, "[requestSource] $originalResource not found cache file")
            }
            downloadSpec = DownloadSpec.values().first()
            asyncDownload = false
        }
        if (SourceOperation.ReadCloud in options.inSourceOperation) {
            downloadSpec?.also {
                if (debug) GLog.d(TAG, "[requestSource] $originalResource download $downloadSpec asyncDownload=$asyncDownload")
                if (asyncDownload) {
                    downloadAsync(cloudFileId, fileMD5, it)
                } else {
                    downloadSync(cloudFileId, fileMD5, it).let { mediaStoreUri ->
                        bitmap = decodeBitmap(cloudGlobalId, mediaStoreUri, bestDownloadSpec)
                    }
                }
            }
        }
        return bitmap
    }

    private fun downloadSync(
        cloudFileId: String,
        fileMD5: String,
        downloadSpec: DownloadSpec
    ): Uri? {
        val lock = Object()
        var mediaStoreUri: Uri? = null
        val time = System.currentTimeMillis()
        val fileDownloadListener: FileDownloadListener = object : FileDownloadListener {
            override fun onProgressUpdate(galleryId: Int, progress: Int) {}
            override fun onDownloadFinish(galleryId: Int, state: Int, isOrigin: Boolean, uri: Uri?) {
                if (galleryId == originalResource.itemId) {
                    mediaStoreUri = uri
                    synchronized(lock) {
                        lock.notify()
                    }
                }
            }
        }
        ApiDmManager.getCloudSyncDM().registerFileDownloadListener(fileDownloadListener)
        downloadAsync(cloudFileId, fileMD5, downloadSpec)

        if (mediaStoreUri == null) {
            synchronized(lock) {
                lock.wait(SYNC_DOWNLOAD_TIMEOUT)
            }
        }
        ApiDmManager.getCloudSyncDM().unregisterFileDownloadListener(fileDownloadListener)
        if (debug) GLog.d(TAG, "[downloadSync] $originalResource download finish cost=${System.currentTimeMillis() - time}")
        return mediaStoreUri
    }

    private fun decodeBitmap(
        cloudGlobalId: String,
        mediaStoreUri: Uri?,
        bestDownloadSpec: DownloadSpec
    ): Bitmap? {
        mediaStoreUri?.let {
            val bitmap = decodeLocalFile(
                DecodeOption(
                    filePath = originalResource.filePath,
                    fileUri = it,
                    mediaId = it.parseId().toInt(),
                    mimeType = MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
                )
            )
            return bitmap
        } ?: let {
            ApiDmManager.getCloudSyncDM().getLargestCachedThumbFile(cloudGlobalId)?.also { cacheFile ->
                val isBest = cacheFile.downloadSpec >= bestDownloadSpec
                val bitmap = decodeLocalFile(
                    DecodeOption(
                        cacheFile.file.absolutePath,
                        mimeType = MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
                    ),
                    shouldSaveToCache = isBest
                )
                return bitmap
            } ?: let {
                GLog.e(TAG, "[decodeBitmap] $originalResource no cache file")
            }
        }
        return null
    }

    private fun downloadAsync(
        cloudFileId: String,
        fileMD5: String,
        downloadSpec: DownloadSpec
    ) {
        ApiDmManager.getCloudSyncDM().downloadFile(
            DownloadData(
                originalResource.itemId,
                originalResource.mediaItem.cloudGlobalId,
                cloudFileId,
                originalResource.filePath,
                fileMD5,
                originalResource.cloudWidth,
                originalResource.cloudHeight,
                originalResource.mediaType,
                originalResource.cloudFileSize,
                originalResource.mimeType,
                originalResource.isSupportCompressed,
                downloadSpec,
                getDownloadPriority()
            )
        ).also { taskId ->
            addObserverForCancelDownload(taskId)
        }
    }

    private fun addObserverForCancelDownload(taskId: Int) {
        lifecycle?.also {
            Handler(Looper.getMainLooper()).post {
                it.addObserver(object : DefaultLifecycleObserver {
                    override fun onDestroy(owner: LifecycleOwner) {
                        if (debug) {
                            GLog.d(TAG, "[onDestroy] cancel download $originalResource")
                        }
                        ApiDmManager.getCloudSyncDM().cancelFileTask(taskId)
                    }
                })
            }
        }
        cancelable?.setOnCancelListener {
            if (debug) {
                GLog.d(TAG, "[onCancel] cancel download $originalResource")
            }
            ApiDmManager.getCloudSyncDM().cancelFileTask(taskId)
        }
    }

    /**
     * 根据请求大小获取最佳的下载规格
     *
     * 图片：满足请求大小和缩图大小的比例不超过[MAX_RATIO_VIEW_SIZE_TO_THUMB_SIZE]的最小下载规格
     * 视频：直接使用抽帧
     */
    private fun getBestDownloadSpec(): DownloadSpec {
        if (originalResource.mediaType == MEDIA_TYPE_IMAGE) {
            val targetSize = ThumbnailSizeUtils.getTargetSizeInType(options.inThumbnailType)
            var result = DownloadSpec.values().first()
            DownloadSpec.values().forEach { downloadSpec ->
                result = downloadSpec
                if (downloadSpec.thumbSize * MAX_RATIO_VIEW_SIZE_TO_THUMB_SIZE >= targetSize) {
                    return result
                }
            }
            return result
        }
        return DownloadSpec.THUMB
    }

    private fun getDownloadPriority(): TransferPriority {
        if (ThumbnailSizeUtils.isFullThumbnailKey(options.inThumbnailType)) {
            return TransferPriority.HIGH
        }
        return TransferPriority.DEFAULT
    }

    companion object {
        private const val TAG = "CloudMediaBitmapRequester"

        /**
         * 视图大小 比 缩图大小 的最大比例
         * 在一定范围内，显示大小略大于缩图大小也就是缩图略微放大后显示是可以接受的，没必要下载更大的缩图
         */
        private const val MAX_RATIO_VIEW_SIZE_TO_THUMB_SIZE: Float = 1.2f

        private const val SYNC_DOWNLOAD_TIMEOUT = 1000L
    }
}