/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AVPlayerRequester.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/08/17
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2022/08/17  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.avplayer

import android.content.Context
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.player.MediaPlayerFactory
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.AvPlayerGetOptions
import com.oplus.gallery.standard_lib.codec.player.AVPlayer

internal class AVPlayerRequester(
    private val context: Context,
    private val resourceKey: LocalMediaResourceKey,
    private val options: AvPlayerGetOptions,
    private val cancelable: ICancelable?
) : IAVPlayerRequester {
    override fun request(): AVPlayer? {
        if (cancelable?.isCancelled() == true) return null

        val resMediaItem = resourceKey.mediaItem
        val mediaItem = when {
            ((resMediaItem is LocalImage) && resMediaItem.isOlivePhoto()) -> resMediaItem
            resMediaItem is LocalVideo -> resMediaItem
            else -> {
                GLog.e(TAG) { "[request] the mediaItem used to create the player must be LocalVideo or OlivePhoto, return null" }
                return null
            }
        }

        // 组装播放器参数，创建播放器实例
        val avPlayerOptions = AVPlayer.AVPlayerOptions(
            colorSpace = options.targetColorSpace,
            videoEffect = options.videoPlayEffect,
            shouldPlaybackAt120Fps = options.shouldPlaybackAt120Fps,
            playType = options.playType,
            subVideoInfo = options.oliveSubVideoInfo

        )
        return MediaPlayerFactory.createAVPlayer(context, mediaItem, avPlayerOptions)
    }

    companion object {
        private const val TAG = "AVPlayerRequester"
    }
}