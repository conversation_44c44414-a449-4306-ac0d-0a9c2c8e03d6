/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : BitmapRegionDecoderCompat.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/4/21 17:49
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2025/4/21  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory.Options
import android.graphics.Gainmap
import android.graphics.Rect
import android.net.Uri
import com.oplus.breakpad.BreakpadTombstone
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.addon.graphics.toGainmap
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_UHDR_GAINMAP_IMAGE
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_UHDR_GAINMAP_INFO
import com.oplus.gallery.foundation.codec.extend.OplusUhdrInfoStruct
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.resizeWithoutContentScale
import com.oplus.gallery.foundation.util.ext.toShortString
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.math.MathUtil.roundToInt
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.bitmap.BitmapRegionDecoder

/**
 * Oplus Heif格式Ultra HDR资源区域解码器，支持gainmap的区域解码
 */
class OplusUhdrBitmapRegionDecoder(
    private val contentRegionDecoder: IRegionDecoder,
    filePath: String,
    context: Context,
    contentUri: Uri
) : IRegionDecoder {
    private var metadata: UltraHdrInfo? = null
    private var gainmapContentsDecoder: IRegionDecoder? = null

    private val imageWidth = contentRegionDecoder.getWidth()
    private val imageHeight = contentRegionDecoder.getHeight()
    private var gainmapWidth = 0
    private var gainmapHeight = 0

    init {
        runCatching {
            FileExtendedContainer().use {
                it.setDataSource(context, contentUri, openFileMode = OpenFileMode.MODE_READ)

                metadata = it.getExtensionData(EXTEND_KEY_UHDR_GAINMAP_INFO)?.let { data ->
                    OplusUhdrInfoStruct(data).toData()
                }
                it.getExtensionData(EXTEND_KEY_UHDR_GAINMAP_IMAGE)?.let { maskData ->
                    gainmapContentsDecoder = BitmapRegionDecoder(maskData, 0, maskData.size)
                    gainmapWidth = gainmapContentsDecoder?.getWidth() ?: 0
                    gainmapHeight = gainmapContentsDecoder?.getHeight() ?: 0
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "init. create failed. path=${PathMask.mask(filePath)}" }
        }
    }

    override fun createDecoder() {
        contentRegionDecoder.createDecoder()
        gainmapContentsDecoder?.createDecoder()
    }

    override fun destroyDecoder() {
        contentRegionDecoder.destroyDecoder()
        gainmapContentsDecoder?.destroyDecoder()
        metadata = null
    }

    override fun isYuvImage(fd: Int): Boolean {
        return contentRegionDecoder.isYuvImage(fd)
    }

    override fun decodeRegion(rect: Rect, options: Options?, isDirectBuffer: Boolean): ImageData? {
        val inGainBitmap: Bitmap? = if (ApiLevelUtil.isAtLeastAndroidU()) {
            options?.inBitmap?.gainmap?.gainmapContents.also {
                options?.inBitmap?.gainmap = null
            }
        } else null
        return contentRegionDecoder.decodeRegion(rect, options, isDirectBuffer)?.apply {
            this.bitmap?.let {
                if (ApiLevelUtil.isAtLeastAndroidU() && (it.gainmap == null)) {
                    it.gainmap = decodeGainRegion(rect, options, inGainBitmap)
                }
            }
        }
    }

    private fun decodeGainRegion(
        imageDecodeRegion: Rect,
        originOptions: Options?,
        inGainBitmap: Bitmap?
    ): Gainmap? {
        runCatching {
            val gainMetadata = metadata ?: run {
                GLog.e(TAG, LogFlag.DL) { "decodeGainRegion. No gainmap metadata. Skip." }
                return null
            }
            val gainDecoder = gainmapContentsDecoder ?: run {
                GLog.e(TAG, LogFlag.DL) { "decodeGainRegion. No gainmap decoder. Skip." }
                return null
            }
            if ((imageWidth <= 0) || (imageHeight <= 0) || (gainmapWidth <= 0) || (gainmapHeight <= 0)) {
                GLog.e(TAG, LogFlag.DL) {
                    "decodeGainRegion. Invalid size. image=${imageWidth}x$imageHeight. gainmap=${gainmapWidth}x$gainmapHeight. Skip."
                }
                return null
            }

            val scaleW = gainmapWidth * 1f / imageWidth
            val scaleH = gainmapHeight * 1f / imageHeight
            val gainDecodeRegion = Rect(
                (imageDecodeRegion.left * scaleW).roundToInt(),
                (imageDecodeRegion.top * scaleH).roundToInt(),
                (imageDecodeRegion.right * scaleW).roundToInt(),
                (imageDecodeRegion.bottom * scaleH).roundToInt()
            )
            val options = Options().apply {
                inBitmap = inGainBitmap
                inSampleSize = originOptions?.inSampleSize ?: 1
            }
            val decodeStart = System.currentTimeMillis()
            gainDecoder.decodeRegion(gainDecodeRegion, options)?.bitmap?.let { gainContents ->
                GLog.d(TAG, LogFlag.DL) { "decodeGainRegion. success $imageDecodeRegion $gainDecodeRegion" }
                val resizedGainContents = if ((inGainBitmap == null) || (gainContents != inGainBitmap)) {
                    /**
                     * 内容图通常都是固定1024x1024图片，如果外部没有输入等比例的inGainBitmap，此处解出的gain会是实际内容比例，
                     * 如果不进行尺寸校正，会出现gain图和原图显示没对齐问题
                     */
                    val desireWidth = gainDecodeRegion.width() / options.inSampleSize
                    val desireHeight = gainDecodeRegion.height() / options.inSampleSize
                    gainContents.resizeWithoutContentScale(null, desireWidth, desireHeight)
                } else gainContents
                GLog.d(TAG, LogFlag.DL) {
                    "decodeGainRegion. success. decodeRegion=$gainDecodeRegion, " +
                        "gain#${gainContents.toShortString()}, resizedGain#${resizedGainContents.toShortString()}," +
                        "decodeTime=${System.currentTimeMillis() - decodeStart}"
                }
                return gainMetadata.toGainmap(resizedGainContents)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "decodeGainRegion. Failed. $imageDecodeRegion" }
        }
        return null
    }

    override fun getWidth(): Int {
        return contentRegionDecoder.getWidth()
    }

    override fun getHeight(): Int {
        return contentRegionDecoder.getHeight()
    }

    override fun isRecycled(): Boolean {
        return contentRegionDecoder.isRecycled()
    }

    override fun isValid(): Boolean {
        return contentRegionDecoder.isValid()
    }

    override fun getDecoderName(): String {
        return contentRegionDecoder.getDecoderName()
    }

    override fun isSupportMultiRegionDecode(): Boolean {
        return contentRegionDecoder.isSupportMultiRegionDecode()
    }

    override fun setTombstone(tombstone: BreakpadTombstone) {
        contentRegionDecoder.setTombstone(tombstone)
    }

    override fun getTombstone(): BreakpadTombstone? {
        return contentRegionDecoder.getTombstone()
    }

    companion object {
        private const val TAG = "OplusUhdrBitmapRegionDecoder"
    }
}