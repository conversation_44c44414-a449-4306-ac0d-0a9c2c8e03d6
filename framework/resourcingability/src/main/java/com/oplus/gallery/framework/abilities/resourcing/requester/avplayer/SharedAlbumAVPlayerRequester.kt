/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SharedAlbumAVPlayerRequester.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/10/12
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** zhen<PERSON>ir<PERSON>@Rom.Apps.Gallery    2022/10/12        1.0           Add
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.avplayer

import android.content.Context
import android.net.Uri
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.item.SharedMediaItem
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.player.MediaPlayerFactory
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.framework.abilities.resourcing.key.SharedMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.AvPlayerGetOptions
import com.oplus.gallery.standard_lib.codec.player.AVPlayer
import java.io.File

/**
 * 区别于[AVPlayerRequester]，本类对应的resourceKey为SharedMediaResourceKey
 */
internal class SharedAlbumAVPlayerRequester(
    private val context: Context,
    private val resourceKey: SharedMediaResourceKey,
    private val options: AvPlayerGetOptions,
    private val cancelable: ICancelable?
) : IAVPlayerRequester {
    override fun request(): AVPlayer? {
        if (cancelable?.isCancelled() == true) return null

        val localVideo = (resourceKey.mediaItem as? SharedMediaItem)?.refVideo ?: object :
            LocalVideo(
                SourceConstants.Local.PATH_ITEM_VIDEO.getChild(0), null, false
            ) {
            override fun isLoaded(): Boolean {
                return true
            }

            override fun getFilePath(): String = resourceKey.mediaItem.filePath

            override fun getContentUri(): Uri = Uri.fromFile(File(filePath))
        }

        // 组装播放器参数，创建播放器实例
        val avPlayerOptions = AVPlayer.AVPlayerOptions(
            shouldPlaybackAt120Fps = options.shouldPlaybackAt120Fps
        )
        return MediaPlayerFactory.createAVPlayer(context, localVideo, avPlayerOptions)
    }
}