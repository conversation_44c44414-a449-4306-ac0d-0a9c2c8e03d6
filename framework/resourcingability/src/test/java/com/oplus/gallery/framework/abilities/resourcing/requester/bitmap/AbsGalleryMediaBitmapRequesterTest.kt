/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsGalleryMediaBitmapRequesterTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataSourceType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.codec.RegionDecoder
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.*

class AbsGalleryMediaBitmapRequesterTest {
    private lateinit var resourceKey: LocalMediaResourceKey
    private lateinit var abilityBus: IAbilityBus
    private lateinit var cancelable: Cancelable
    private lateinit var option: ResourceGetOptions
    private lateinit var requester: LocalImageBitmapRequester
    private val context = mockk<Context>()
    private val path = mockk<Path>()
    private val defaultFilePath = "default_file_path"
    private val sourceType = 0
    private val mediaId = 1

    @Before
    fun before() {
        ContextGetter.context = context

        resourceKey = mockk()
        every { resourceKey.path } returns path
        every { resourceKey.filePath } returns defaultFilePath
        every { resourceKey.mediaId } returns mediaId

        mockkStatic(DataSourceType::class)
        every { DataSourceType.judgeSourceType(defaultFilePath) } returns sourceType

        abilityBus = mockk()
        cancelable = mockk()
        option = ResourceGetOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL)
        requester = spyk(LocalImageBitmapRequester(context, resourceKey, option, abilityBus, null, null, cancelable))

        mockkStatic(ThumbnailSizeUtils::class)
    }

    @After
    fun after() {
        unmockkAll()
    }

    /*
    @Test
    fun `should return null when onDecodeOriginal with width negative`() {
        //Given
        val decodeWidth = 2
        val decodeHeight = 4
        mockkConstructor(BitmapFactory.Options::class)
        every { anyConstructed<BitmapFactory.Options>().outWidth} returns decodeWidth
        every { anyConstructed<BitmapFactory.Options>().outHeight = decodeHeight }

        val type = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val targetSize = 100
        every { ThumbnailSizeUtils.getTargetSizeInType(type) } returns targetSize

        val mimeType = "mimeType"
        val filePath = "filepath"
        val width = -2
        val height = 3
        val uri = mockk<Uri>()
        val mediaItem = mockk<LocalMediaItem>()
        val dateTime = 0L
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.width } returns width
        every { resourceKey.height } returns height
        every { resourceKey.contentUri } returns uri
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mediaItem } returns mediaItem
        every { resourceKey.dateModifiedInSecond } returns dateTime

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isBmp(mimeType) } returns true

        mockkStatic(DecodeUtils::class)
        every { DecodeUtils.decodeBounds(context, uri, filePath, any()) } returns mockk()

        val bitmap = mockk<Bitmap>()
        val imageData = mockk<ImageData>()
        every { imageData.bitmap } returns bitmap
        every { mediaItem.isYuvAndLargePage(type) } returns true
        mockkStatic(CodecHelper::class)
        every { CodecHelper.decodeThumbnail(any(), filePath, mediaId.toString(), any(), targetSize, type, any(), dateTime, any()) } returns imageData

        //When
        val result = requester.onDecodeOriginal(type, option, cancelable)

        //Then
        Assert.assertEquals(bitmap, result)
    }*/

    @Ignore
    @Test
    fun `should return null when onDecodeOriginal with type bigmap and cancelable true`() {
        //Given
        val type = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val targetSize = 100
        every { ThumbnailSizeUtils.getTargetSizeInType(type) } returns targetSize

        val mimeType = "mimeType"
        val width = 2
        val height = 3
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.width } returns width
        every { resourceKey.height } returns height

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isBmp(mimeType) } returns true
        every { cancelable.isCancelled() } returns true

        val decodeOption = AbsGalleryMediaBitmapRequester.DecodeOption(
            filePath = "",
            mimeType = mimeType
        )

        //When
        val result = requester.onDecodeOriginal(decodeOption, type, option, cancelable)

        //Then
        Assert.assertNull(result)
    }

    @Ignore
    @Test
    fun `should return bitmap when onDecodeOriginal with qualcom platform and support regiondecoder`() {
        //Given
        val type = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val targetSize = 100
        every { ThumbnailSizeUtils.getTargetSizeInType(type) } returns targetSize

        val mimeType = "mimeType"
        val filePath = "filepath"
        val width = 4001
        val height = 4000
        val uri = mockk<Uri>()
        val mediaItem = mockk<LocalMediaItem>()
        val dateTime = 0L
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.width } returns width
        every { resourceKey.height } returns height
        every { resourceKey.contentUri } returns uri
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mediaItem } returns mediaItem
        every { resourceKey.dateModifiedInSecond } returns dateTime

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isBmp(mimeType) } returns true
        every { cancelable.isCancelled() } returns true

        mockkStatic(DecodeUtils::class)
        every { DecodeUtils.decodeBounds(context, uri, filePath, any()) } returns mockk()

        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isQualcommPlatform } returns true

        mockkStatic(RegionDecoder::class)
        every { RegionDecoder.isSupportRegionDecode(mimeType) } returns true

        val bitmap = mockk<Bitmap>()
        val imageData = mockk<ImageData>()
        every { imageData.bitmap } returns bitmap
        every { mediaItem.isYuvAndLargePage(type) } returns true
        mockkStatic(CodecHelper::class)
        every {
            CodecHelper.decodeRegionFullRectThumbnail(
                any(),
                filePath,
                mediaId.toString(),
                any(),
                targetSize,
                type,
                any(),
                dateTime,
                any()
            )
        } returns imageData

        val decodeOption = AbsGalleryMediaBitmapRequester.DecodeOption(
            filePath = filePath,
            fileUri = uri,
            mediaId = mediaId,
            mimeType = mimeType
        )

        //When
        val result = requester.onDecodeOriginal(decodeOption, type, option, cancelable)

        //Then
        Assert.assertEquals(bitmap, result)
    }

    @Ignore
    @Test
    fun `should return bitmap when onDecodeOriginal with not qualcom platform`() {
        //Given
        val type = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val targetSize = 100
        every { ThumbnailSizeUtils.getTargetSizeInType(type) } returns targetSize

        val mimeType = "mimeType"
        val filePath = "filepath"
        val width = 4001
        val height = 4000
        val uri = mockk<Uri>()
        val mediaItem = mockk<LocalMediaItem>()
        val dateTime = 0L
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.width } returns width
        every { resourceKey.height } returns height
        every { resourceKey.contentUri } returns uri
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mediaItem } returns mediaItem
        every { resourceKey.dateModifiedInSecond } returns dateTime

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isBmp(mimeType) } returns true
        every { cancelable.isCancelled() } returns true

        mockkStatic(DecodeUtils::class)
        every { DecodeUtils.decodeBounds(context, uri, filePath, any()) } returns mockk()

        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isQualcommPlatform } returns false

        val bitmap = mockk<Bitmap>()
        val imageData = mockk<ImageData>()
        every { imageData.bitmap } returns bitmap
        every { mediaItem.isYuvAndLargePage(type) } returns true
        mockkStatic(CodecHelper::class)
        every {
            CodecHelper.decodeThumbnail(
                any(),
                filePath,
                mediaId.toString(),
                any(),
                targetSize,
                type,
                any(),
                dateTime,
                any()
            )
        } returns imageData

        val decodeOption = AbsGalleryMediaBitmapRequester.DecodeOption(
            filePath = filePath,
            fileUri = uri,
            mediaId = mediaId,
            mimeType = mimeType
        )

        //When
        val result = requester.onDecodeOriginal(decodeOption, type, option, cancelable)

        //Then
        Assert.assertEquals(bitmap, result)
    }
}