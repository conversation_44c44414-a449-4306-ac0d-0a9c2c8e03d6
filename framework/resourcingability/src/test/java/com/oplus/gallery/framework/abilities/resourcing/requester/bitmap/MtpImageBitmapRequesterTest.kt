/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MtpImageBitmapRequesterTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.graphics.Bitmap
import com.oplus.gallery.business_lib.model.data.base.item.MtpItem
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.resourcing.key.MtpResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.MtpImageBitmapRequester.Companion.MTP_THUMBNAIL_SIZE
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.*

class MtpImageBitmapRequesterTest {
    private lateinit var resourceKey: MtpResourceKey
    private lateinit var options: ResourceGetOptions
    private lateinit var cancelable: Cancelable
    private lateinit var requester: MtpImageBitmapRequester

    @Before
    fun before() {
        resourceKey = mockk()
        options = ResourceGetOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL)
        cancelable = mockk()

        requester = spyk(MtpImageBitmapRequester(resourceKey, options, cancelable))
    }

    @After
    fun after() {
        unmockkAll()
    }

    @Ignore
    @Test
    fun `should return null when request with microThumbnail bytes null`() {
        //Given
        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType) } returns true

        val mtpItem = mockk<MtpItem>()
        every { resourceKey.mtpItem } returns mtpItem
        val byteThumbnailData = ByteArray(1)
        val byteImageData = ByteArray(2)
        every { mtpItem.thumbnailData } returns null
        every { mtpItem.imageData } returns null

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
        verify { mtpItem.thumbnailData }
        verify { mtpItem.imageData }
    }

    @Ignore
    @Test
    fun `should return null when request with not microThumbnail bytes null`() {
        //Given
        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType) } returns false

        val mtpItem = mockk<MtpItem>()
        every { resourceKey.mtpItem } returns mtpItem
        val byteThumbnailData = ByteArray(1)
        val byteImageData = ByteArray(2)
        every { mtpItem.thumbnailData } returns null
        every { mtpItem.imageData } returns null

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
        verify(inverse = true) { mtpItem.thumbnailData }
        verify { mtpItem.imageData }
    }

    @Ignore
    @Test
    fun `should call resizeAndCropCenter when request with microThumbnail`() {
        //Given
        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType) } returns true

        val mtpItem = mockk<MtpItem>()
        every { resourceKey.mtpItem } returns mtpItem
        val byteThumbnailData = ByteArray(1)
        val byteImageData = ByteArray(2)
        every { mtpItem.thumbnailData } returns byteThumbnailData
        every { mtpItem.imageData } returns byteImageData

        val bitmap = mockk<Bitmap>()
        mockkStatic(CodecHelper::class)
        every {
            CodecHelper.decodeThumbnailArray(
                any(),
                byteThumbnailData,
                null,
                MTP_THUMBNAIL_SIZE,
                options.inThumbnailType
            )
        } returns bitmap

        val resizedBitmap = mockk<Bitmap>()
        mockkStatic(BitmapUtils::class)
        every { BitmapUtils.resizeAndCropCenter(bitmap, MTP_THUMBNAIL_SIZE, true) } returns resizedBitmap

        //When
        val result = requester.request()

        //Then
        Assert.assertEquals(resizedBitmap, result)
        verify { mtpItem.thumbnailData }
        verify(inverse = true) { mtpItem.imageData }
        verify { BitmapUtils.resizeAndCropCenter(bitmap, MTP_THUMBNAIL_SIZE, true) }
    }

    @Ignore
    @Test
    fun `should call resizeDownBySideLength when request with microThumbnail false`() {
        //Given
        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType) } returns false

        val mtpItem = mockk<MtpItem>()
        every { resourceKey.mtpItem } returns mtpItem
        val byteThumbnailData = ByteArray(1)
        val byteImageData = ByteArray(2)
        every { mtpItem.thumbnailData } returns byteThumbnailData
        every { mtpItem.imageData } returns byteImageData

        val bitmap = mockk<Bitmap>()
        mockkStatic(CodecHelper::class)
        every {
            CodecHelper.decodeThumbnailArray(
                any(),
                byteImageData,
                null,
                MTP_THUMBNAIL_SIZE,
                options.inThumbnailType
            )
        } returns bitmap

        val resizedBitmap = mockk<Bitmap>()
        mockkStatic(BitmapUtils::class)
        every { BitmapUtils.resizeByLongSide(bitmap, MTP_THUMBNAIL_SIZE, true) } returns resizedBitmap

        //When
        val result = requester.request()

        //Then
        Assert.assertEquals(resizedBitmap, result)
        verify(inverse = true) { mtpItem.thumbnailData }
        verify { mtpItem.imageData }
        verify { BitmapUtils.resizeByLongSide(bitmap, MTP_THUMBNAIL_SIZE, true) }
    }

    @Ignore
    @Test
    fun `should return null when request with decode throws Exception`() {
        //Given
        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.isMicroThumbnailKey(options.inThumbnailType) } returns false

        val mtpItem = mockk<MtpItem>()
        every { resourceKey.mtpItem } returns mtpItem
        val byteThumbnailData = ByteArray(1)
        val byteImageData = ByteArray(2)
        every { mtpItem.thumbnailData } returns byteThumbnailData
        every { mtpItem.imageData } returns byteImageData

        val bitmap = mockk<Bitmap>()
        mockkStatic(CodecHelper::class)
        every {
            CodecHelper.decodeThumbnailArray(
                any(),
                byteImageData,
                null,
                MTP_THUMBNAIL_SIZE,
                options.inThumbnailType
            )
        } throws Exception()

        val resizedBitmap = mockk<Bitmap>()
        mockkStatic(BitmapUtils::class)
        every { BitmapUtils.resizeByLongSide(bitmap, MTP_THUMBNAIL_SIZE, true) } returns resizedBitmap

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
        verify(inverse = true) { mtpItem.thumbnailData }
        verify { mtpItem.imageData }
    }
}