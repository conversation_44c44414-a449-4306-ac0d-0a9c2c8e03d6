/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ResourcingAbilityImplTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing

import android.content.Context
import android.net.Uri
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.business_lib.model.data.base.source.DataSourceType
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.MtpResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.PredecodeResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.TileResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.requester.SdCardStateManager
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.FastCaptureBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.LocalImageBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.LocalVideoBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.MtpImageBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.PredecodeBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.TileBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.UriImageBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.bitmap.UriVideoBitmapRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.LocalImageRegionDecoderRequester
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.MtpImageRegionDecoder
import com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder.UriImageRegionDecoderRequest
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

@Ignore("Marked by zhangjisong to zhangwenming, 神仙代码，怎么解都搞不掉，后面处理")
class ResourcingAbilityImplTest {
    private lateinit var resourcingAbilityImpl: ResourcingAbilityImpl
    private val abilityBus = mockk<IAbilityBus>()
    private val abilityConfig = mockk<IAbilityConfig>()

    private val options = ResourceGetOptions(inThumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL, inCacheOperation = CacheOperation.ReadDiskCache)
    private val cancelable = mockk<ICancelable>()
    private val context = mockk<Context>()

    @Before
    fun before() {
        ContextGetter.context = context
        resourcingAbilityImpl = spyk(ResourcingAbilityImpl(context))

        mockkStatic(SdCardStateManager::class)
        every { SdCardStateManager.updateSdCardState() } returns Unit
        every { SdCardStateManager.registerSdCardStateMonitor() } returns Unit

        resourcingAbilityImpl.onLoad(abilityBus, abilityConfig)
    }

    @After
    fun after() {
        unmockkAll()
    }

    /**
     * Test getBitmapRequester
     */
    @Test
    fun `should return PredecodeBitmapRequester when getBitmapRequester with PredecodeResourceKey`() {
        //Given
        val resourceKey = mockk<PredecodeResourceKey>()

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is PredecodeBitmapRequester)
    }

    /**
     * 该类用到的FastCaptureDetector类，实际为
     * framework/resourcingability/src/test/java/com/oplus/gallery/foundation/fastcapture/FastCaptureDetector.kt
     */
    @Test
    fun `should return FastCaptureBitmapRequester when getBitmapRequester with isFastCapture true`() {
        //Given
        val filePath = "filePath"
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.mimeType } returns EMPTY_STRING
        every { resourceKey.filePath } returns filePath

        val fastCaptureDetector = mockk<FastCaptureDetector>()
        every { fastCaptureDetector.detector().isFastCapture() } returns true
        every { resourcingAbilityImpl.createFastCaptureDetectorIfNeeded(any()) } returns fastCaptureDetector

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is FastCaptureBitmapRequester)
    }

    @Test
    fun `should return LocalImageBitmapRequester when getBitmapRequester with isImage true`() {
        //Given
        val filePath = "filePath"
        val mimeType = "mimetype"
        val path = mockk<Path>()
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.path } returns path

        every { resourcingAbilityImpl.createFastCaptureDetectorIfNeeded(any()) } returns null

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns true

        mockkStatic(DataSourceType::class)
        every { DataSourceType.judgeSourceType(filePath) } returns 0

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is LocalImageBitmapRequester)
    }

    @Test
    fun `should return LocalImageBitmapRequester when getBitmapRequester with isImage false path is image true`() {
        //Given
        val filePath = "filePath"
        val mimeType = "mimetype"
        val path = mockk<Path>()
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.path } returns path

        mockkConstructor(FastCaptureDetector::class)
        every { anyConstructed<FastCaptureDetector>().detector().isFastCapture() } returns false

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns false

        mockkObject(TypeFilterUtils)
        every { TypeFilterUtils.isImage(path) } returns true

        mockkStatic(DataSourceType::class)
        every { DataSourceType.judgeSourceType(filePath) } returns 0

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is LocalImageBitmapRequester)
    }

    @Test
    fun `should return LocalImageBitmapRequester when getBitmapRequester with isVideo true`() {
        //Given
        val filePath = "filePath"
        val mimeType = "mimetype"
        val path = mockk<Path>()
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.path } returns path

        every { resourcingAbilityImpl.createFastCaptureDetectorIfNeeded(any()) } returns null

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns false
        every { MimeTypeUtils.isVideo(mimeType) } returns true

        mockkObject(TypeFilterUtils)
        every { TypeFilterUtils.isImage(path) } returns false

        mockkStatic(DataSourceType::class)
        every { DataSourceType.judgeSourceType(filePath) } returns 0

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is LocalVideoBitmapRequester)
    }

    @Test
    fun `should return LocalImageBitmapRequester when getBitmapRequester with path video true`() {
        //Given
        val filePath = "filePath"
        val mimeType = "mimetype"
        val path = mockk<Path>()
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.path } returns path

        every { resourcingAbilityImpl.createFastCaptureDetectorIfNeeded(any()) } returns null

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns false
        every { MimeTypeUtils.isVideo(mimeType) } returns false

        mockkObject(TypeFilterUtils)
        every { TypeFilterUtils.isImage(path) } returns false
        every { TypeFilterUtils.isVideo(path) } returns true

        mockkStatic(DataSourceType::class)
        every { DataSourceType.judgeSourceType(filePath) } returns 0

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is LocalVideoBitmapRequester)
    }

    @Test
    fun `should return null when getBitmapRequester with LocalMediaResourceKey but not image and video`() {
        //Given
        val mimeType = "mimetype"
        val filePath = "filepath"
        val path = mockk<Path>()
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.filePath } returns filePath
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.path } returns path

        every { resourcingAbilityImpl.createFastCaptureDetectorIfNeeded(any()) } returns null

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns false
        every { MimeTypeUtils.isVideo(mimeType) } returns false

        mockkObject(TypeFilterUtils)
        every { TypeFilterUtils.isImage(path) } returns false
        every { TypeFilterUtils.isVideo(path) } returns false

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return MtpImageBitmapRequester when getBitmapRequester with MtpResourceKey`() {
        //Given
        val resourceKey = mockk<MtpResourceKey>()

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is MtpImageBitmapRequester)
    }

    @Test
    fun `should return UriImageBitmapRequester when getBitmapRequester with UriMediaResourceKey and isImage true`() {
        //Given
        val mimeType = "mime"
        val uri = mockk<Uri>()
        val uriItem = mockk<BaseUriItem>()
        val resourceKey = mockk<UriMediaResourceKey>()
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.contentUri } returns uri
        every { resourceKey.uriItem } returns uriItem

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns true

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is UriImageBitmapRequester)
    }

    @Test
    fun `should return UriVideoBitmapRequester when getBitmapRequester with UriMediaResourceKey and isVideo true`() {
        //Given
        val mimeType = "mime"
        val uri = mockk<Uri>()
        val uriItem = mockk<BaseUriItem>()
        val resourceKey = mockk<UriMediaResourceKey>()
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.contentUri } returns uri
        every { resourceKey.uriItem } returns uriItem

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns false
        every { MimeTypeUtils.isVideo(mimeType) } returns true

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is UriVideoBitmapRequester)
    }

    @Test
    fun `should return null when getBitmapRequester with UriMediaResourceKey and not image and not video`() {
        //Given
        val mimeType = "mime"
        val uri = mockk<Uri>()
        val uriItem = mockk<BaseUriItem>()
        val resourceKey = mockk<UriMediaResourceKey>()
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.contentUri } returns uri
        every { resourceKey.uriItem } returns uriItem

        mockkStatic(MimeTypeUtils::class)
        every { MimeTypeUtils.isImage(mimeType) } returns false
        every { MimeTypeUtils.isVideo(mimeType) } returns false

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return TileBitmapRequester when getBitmapRequester with TileResourceKey`() {
        //Given
        val resourceKey = mockk<TileResourceKey>()

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertTrue(result is TileBitmapRequester)
    }

    @Test
    fun `should return null when getBitmapRequester with ResourceKey not recognized`() {
        //Given
        val resourceKey = mockk<ResourceKey>()

        //When
        val result = resourcingAbilityImpl.getBitmapRequester(resourceKey, options, cancelable, null)

        //Then
        Assert.assertNull(result)
    }

    /**
     * Test getBitmapRequester
     */
    @Test
    fun `should return LocalImageRegionDecoderRequester when getRegionDecoderRequester with LocalMediaResourceKey`() {
        //Given
        val resourceKey = mockk<LocalMediaResourceKey>()

        every { resourcingAbilityImpl.createFastCaptureDetectorIfNeeded(any()) } returns null

        //When
        val result = resourcingAbilityImpl.getRegionDecoderRequester(resourceKey, cancelable)

        //Then
        Assert.assertTrue(result is LocalImageRegionDecoderRequester)
    }

    @Test
    fun `should return UriImageRegionDecoderRequest when getRegionDecoderRequester with UriMediaResourceKey`() {
        //Given
        val mimeType = "mime"
        val uri = mockk<Uri>()
        val uriItem = mockk<BaseUriItem>()
        val resourceKey = mockk<UriMediaResourceKey>()
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.contentUri } returns uri
        every { resourceKey.uriItem } returns uriItem

        //When
        val result = resourcingAbilityImpl.getRegionDecoderRequester(resourceKey, cancelable)

        //Then
        Assert.assertTrue(result is UriImageRegionDecoderRequest)
    }

    @Test
    fun `should return MtpImageRegionDecoder when getRegionDecoderRequester with MtpResourceKey`() {
        //Given
        val resourceKey = mockk<MtpResourceKey>()

        //When
        val result = resourcingAbilityImpl.getRegionDecoderRequester(resourceKey, cancelable)

        //Then
        Assert.assertTrue(result is MtpImageRegionDecoder)
    }
}