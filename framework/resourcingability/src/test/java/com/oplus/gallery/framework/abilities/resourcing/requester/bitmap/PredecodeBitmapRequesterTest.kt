/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PredecodeBitmapRequesterTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/06/30
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/06/30       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.framework.abilities.resourcing.key.PredecodeResourceKey
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Rule
import org.junit.Test
import org.junit.rules.TemporaryFolder

class PredecodeBitmapRequesterTest {

    @Test
    fun `should return VideoThumbnailLoader when calling findSuitableThumbnailLoader with video resourcingKey`() {
        // Given
        val resourcingKey = mockk<PredecodeResourceKey>()
        every { resourcingKey.path } returns LocalVideo.ITEM_PATH.getChild("10010")
        val requester = PredecodeBitmapRequester(mockk(), resourcingKey, mockk(), mockk())
        val expectedClass = PredecodeBitmapRequester.VideoThumbnailLoader::class.java

        // When
        val actualLoader = requester.findSuitableThumbnailLoader(0)
        val actualClass = actualLoader.javaClass

        // Then
        Assert.assertEquals(expectedClass, actualClass)
    }

    @Test
    fun `should return ImageThumbnailLoader when calling findSuitableThumbnailLoader with not video resourcingKey`() {
        // Given
        val resourcingKey = mockk<PredecodeResourceKey>()
        val requester = PredecodeBitmapRequester(mockk(), resourcingKey, mockk(), mockk())
        val expectedClass = PredecodeBitmapRequester.ImageThumbnailLoader::class.java
        every { resourcingKey.path } returns LocalImage.ITEM_PATH.getChild("10010")

        // When
        val actualLoader = requester.findSuitableThumbnailLoader(0)
        val actualClass = actualLoader.javaClass

        // Then
        Assert.assertEquals(expectedClass, actualClass)
    }

    @Rule
    @JvmField
    val tmpFolder = TemporaryFolder()

    @Test
    fun `should return expected cacheKey when updatePathCacheKeyIfNeeded`() {
        // Given
        val tmpFile = tmpFolder.newFile("10010_tmp.jpg").apply { writeText("10086") }
        val path = LocalImage.ITEM_PATH.getChild("10010")
        val resourcingKey = mockk<PredecodeResourceKey>()
        val requester = PredecodeBitmapRequester(mockk(), resourcingKey, mockk(), mockk())
        val expectedCacheKey = "${tmpFile.absolutePath.replace("_tmp", "")}${tmpFile.length()}"
        every { resourcingKey.filePath } returns tmpFile.absolutePath

        // When
        requester.updatePathCacheKeyIfNeeded(path)
        val actualCacheKey = path.cacheKey

        // Then
        Assert.assertEquals(expectedCacheKey, actualCacheKey)
    }
}