/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsLocalMediaBitmapRequesterTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/27
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/27    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ColorSpace
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.source.DataSourceType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.ICancelable
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.caching.CacheOperation.WriteAllCache
import com.oplus.gallery.framework.abilities.caching.CacheResult
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.ResultCode
import com.oplus.gallery.framework.abilities.caching.key.CacheKey
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

@Ignore
class AbsLocalMediaBitmapRequesterTest {
    private val cancelable = mockk<ICancelable>()
    private val resourceKey = mockk<LocalMediaResourceKey>()
    private val abilityBus = mockk<IAbilityBus>()
    private val path = mockk<Path>()
    private val context = mockk<Context>()
    private lateinit var options: ResourceGetOptions
    private lateinit var requester: AbsGalleryMediaBitmapRequester
    private val defaultFilePath = "default_file_path"
    private val sourceType = 0

    @Before
    fun before() {
        options = ResourceGetOptions(inThumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL, inCacheOperation = WriteAllCache)
        every { resourceKey.path } returns path
        every { resourceKey.filePath } returns defaultFilePath
        every { resourceKey.contentUri } returns mockk()
        every { resourceKey.mediaId } returns 0
        every { resourceKey.mimeType } returns MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
        mockkStatic(DataSourceType::class)
        every { DataSourceType.judgeSourceType(defaultFilePath) } returns sourceType
        requester = spyk(TestLocalMediaBitmapRequester(context, resourceKey, options, abilityBus, cancelable))
    }

    @After
    fun after() {
        unmockkAll()
    }

    /**
     * Test request
     */
    @Test
    fun `should return null when request with job canceled`() {
        //Given
        every { requester.getBitmapFromCache() } returns mockk()
        every { cancelable.isCancelled() } returns true

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return the cachedBitmap when request with shouldDecodeOriginal false`() {
        //Given
        val cachedBitmap = mockk<Bitmap>()
        every { requester.getBitmapFromCache() } returns cachedBitmap
        every { cancelable.isCancelled() } returns false

        every { requester.adjustColorSpace(cachedBitmap) } returns true

        //When
        val result = requester.request()

        //Then
        Assert.assertEquals(cachedBitmap, result)
    }

    @Test
    fun `should return null when request with decodeOriginal result null`() {
        //Given
        val cachedBitmap = mockk<Bitmap>()
        every { cachedBitmap.recycle() } returns Unit

        every { requester.getBitmapFromCache() } returns cachedBitmap
        every { cancelable.isCancelled() } returns false

        every { requester.decodeOriginal(any(), options.inThumbnailType) } returns null

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
        verify { cachedBitmap.recycle() }
    }

    @Test
    fun `should return resize and save bitmap with call request with decodeOriginal not null`() {
        //Given
        val cachedBitmap = mockk<Bitmap>()
        every { cachedBitmap.recycle() } returns Unit

        every { requester.getBitmapFromCache() } returns cachedBitmap
        every { cancelable.isCancelled() } returns false

        val decodedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.DecodedBitmapInfo>()
        every { requester.decodeOriginal(any(), options.inThumbnailType) } returns decodedBitmapInfo

        val resizedBitmap = mockk<Bitmap>()
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        every { resizedBitmapInfo.bitmap } returns resizedBitmap
        every { resizedBitmapInfo.isRequested } returns true
        every { resizedBitmapInfo.isRecycled() } returns false

        val resizeBitmapList = listOf(resizedBitmapInfo)
        every { requester.resizeBitmap(any(), decodedBitmapInfo, options.inThumbnailType) } returns resizeBitmapList
        every { requester.saveBitmapIntoCache(resizeBitmapList, any()) } returns Unit
        every { requester.adjustColorSpace(resizedBitmap) } returns true

        //When
        val result = requester.request()

        //Then
        Assert.assertEquals(resizedBitmap, result)
        verify { cachedBitmap.recycle() }
    }

    /**
     * Start Test getBitmapFromCache
     */
    @Test
    fun `should return null when getBitmapFromCache with requireAbility ICachingAbility null`() {
        //Given
        every { abilityBus.requireAbility(ICachingAbility::class.java) } returns null

        //When
        val result = requester.getBitmapFromCache()

        //Then
        Assert.assertNull(result)
    }

    /**
     * Test saveBitmapIntoCache
     */
    @Test
    fun `should not call saveBitmapIntoCacheSynchronized when saveBitmapIntoCache with outLowResolution true`() {
        //Given
        options.outLowResolution = true

        //When
        requester.saveBitmapIntoCache(mockk(), DataSourceType.TYPE_PHONE)

        //Then
        verify(inverse = true) { requester.saveBitmapIntoCacheSynchronized(any(), any()) }
    }

    /**
     * Test saveBitmapIntoCache
     */
    @Test
    fun `should not call saveBitmapIntoCacheSynchronized when saveBitmapIntoCache with outIsPartialImage true`() {
        //Given
        options.outIsPartialImage = true

        //When
        requester.saveBitmapIntoCache(mockk(), DataSourceType.TYPE_PHONE)

        //Then
        verify(inverse = true) { requester.saveBitmapIntoCacheSynchronized(any(), any()) }
    }

    @Test
    fun `should not call saveBitmapToCache when saveBitmapIntoCacheSynchronized with job canceled`() {
        //Given
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        val list = listOf(resizedBitmapInfo)
        every { cancelable.isCancelled() } returns true

        //When
        requester.saveBitmapIntoCacheSynchronized(list, DataSourceType.TYPE_PHONE)

        //Then
        verify(inverse = true) { requester.saveBitmapToCache(any()) }
    }

    @Test
    fun `should not not copy bitmap when saveBitmapIntoCacheSynchronized with useBitmapCopy false`() {
        //Given
        val bitmap = mockk<Bitmap>()
        val thumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        every { resizedBitmapInfo.isRequested } returns true
        every { resizedBitmapInfo.bitmap } returns bitmap
        every { resizedBitmapInfo.thumbnailType } returns thumbnailType

        every { cancelable.isCancelled() } returns false
        every { requester.saveBitmapToCache(any()) } returns true
        every { requester.addSdCardCacheInfo(thumbnailType, sourceType) } returns Unit

        //When
        requester.saveBitmapIntoCacheSynchronized(listOf(resizedBitmapInfo), DataSourceType.TYPE_PHONE)

        //Then
        verify { requester.saveBitmapToCache(resizedBitmapInfo) }
        verify(inverse = true) { bitmap.copy(any(), any()) }
        verify(inverse = true) { resizedBitmapInfo.recycle() }
    }

    @Test
    fun `should not not copy bitmap when saveBitmapIntoCacheSynchronized with requested false`() {
        //Given
        val bitmap = mockk<Bitmap>()
        val thumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        every { resizedBitmapInfo.isRequested } returns false
        every { resizedBitmapInfo.bitmap } returns bitmap
        every { resizedBitmapInfo.thumbnailType } returns thumbnailType
        every { resizedBitmapInfo.recycle() } returns Unit

        every { cancelable.isCancelled() } returns false
        every { requester.saveBitmapToCache(any()) } returns true
        every { requester.addSdCardCacheInfo(thumbnailType, sourceType) } returns Unit

        //When
        requester.saveBitmapIntoCacheSynchronized(listOf(resizedBitmapInfo), DataSourceType.TYPE_PHONE)

        //Then
        verify { requester.saveBitmapToCache(resizedBitmapInfo) }
        verify(inverse = true) { bitmap.copy(any(), any()) }
        verify(inverse = true) { resizedBitmapInfo.recycle() }
    }

    /**
     * Test saveBitmapToCache
     */
    @Test
    fun `should return false when saveBitmapToCache with cacheAbility null`() {
        //Given
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        every { abilityBus.requireAbility(ICachingAbility::class.java) } returns null

        //When
        val result = requester.saveBitmapToCache(resizedBitmapInfo)

        //Then
        Assert.assertFalse(result)
    }

    @Test
    fun `should return false when saveBitmapToCache with job canceled`() {
        //Given
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        val cacheAbility = mockk<ICachingAbility>()
        every { abilityBus.requireAbility(ICachingAbility::class.java) } returns cacheAbility
        every { abilityBus.requireAbility(IColorManagementAbility::class.java) } returns null
        every { cancelable.isCancelled() } returns true
        every { cacheAbility.close() } returns Unit

        //When
        val result = requester.saveBitmapToCache(resizedBitmapInfo)

        //Then
        Assert.assertFalse(result)
        verify { cacheAbility.close() }
    }

    @Test
    fun `should call screenNailCache putBitmap when saveBitmapToCache with screennail request`() {
        //Given
        val cropParams = CropParams.noCrop()
        val thumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val resizedBitmap = mockk<Bitmap>()
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        every { resizedBitmapInfo.cropParams } returns cropParams
        every { resizedBitmapInfo.thumbnailType } returns thumbnailType
        every { resizedBitmapInfo.bitmap } returns resizedBitmap

        val cacheKey = mockk<CacheKey>()
        mockkStatic(CacheKeyFactory::class)
        every { CacheKeyFactory.createCacheKey(requester.originalResource, cropParams) } returns cacheKey

        val cacheAbility = mockk<ICachingAbility>()
        every { cacheAbility.screenNailCache?.putBitmap(cacheKey, resizedBitmap, any()) } returns CacheResult(ResultCode.SUCCESS)
        every { cacheAbility.close() } returns Unit

        every { abilityBus.requireAbility(ICachingAbility::class.java) } returns cacheAbility
        every { abilityBus.requireAbility(IColorManagementAbility::class.java) } returns null
        every { cancelable.isCancelled() } returns false


        //When
        val result = requester.saveBitmapToCache(resizedBitmapInfo)

        //Then
        Assert.assertTrue(result)
        verify { cacheAbility.close() }
    }

    @Test
    fun `should call thumbnailCache putBitmap when saveBitmapToCache with screennail request`() {
        //Given
        val cropParams = CropParams.centerRectCrop()
        val thumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL
        val resizedBitmap = mockk<Bitmap>()
        val resizedBitmapInfo = mockk<AbsGalleryMediaBitmapRequester.ResizedBitmapInfo>()
        every { resizedBitmapInfo.cropParams } returns cropParams
        every { resizedBitmapInfo.thumbnailType } returns thumbnailType
        every { resizedBitmapInfo.bitmap } returns resizedBitmap

        val cacheKey = mockk<CacheKey>()
        mockkStatic(CacheKeyFactory::class)
        every { CacheKeyFactory.createCacheKey(requester.originalResource, cropParams) } returns cacheKey

        val cacheAbility = mockk<ICachingAbility>()
        every { cacheAbility.thumbnailCache?.putBitmap(cacheKey, resizedBitmap, any()) } returns CacheResult(ResultCode.SUCCESS)
        every { cacheAbility.close() } returns Unit

        every { abilityBus.requireAbility(ICachingAbility::class.java) } returns cacheAbility
        every { abilityBus.requireAbility(IColorManagementAbility::class.java) } returns null
        every { cancelable.isCancelled() } returns false


        //When
        val result = requester.saveBitmapToCache(resizedBitmapInfo)

        //Then
        Assert.assertTrue(result)
        verify { cacheAbility.close() }
    }

    /**
     * Test adjustColorSpace
     */
    @Test
    fun `should return false when adjustColorSpace with colorability null`() {
        //Given
        val bitmap = mockk<Bitmap>()
        every { abilityBus.requireAbility(IColorManagementAbility::class.java) } returns null

        //When
        val result = requester.adjustColorSpace(bitmap)
        //Then
        Assert.assertFalse(result)
    }

    @Test
    fun `should return true when adjustColorSpace with isWideColorGamutEnabled true`() {
        //Given
        val bitmap = mockk<Bitmap>()

        val colorAbility = mockk<IColorManagementAbility>()
        every { abilityBus.requireAbility(IColorManagementAbility::class.java) } returns colorAbility
        every { colorAbility.isWideColorGamutEnabled } returns true
        every { colorAbility.close() } returns Unit

        //When
        val result = requester.adjustColorSpace(bitmap)
        //Then
        Assert.assertTrue(result)
        verify(inverse = true) { colorAbility.adjustColorSpace(any(), any()) }
        verify { colorAbility.close() }
    }

    @Test
    fun `should return true when adjustColorSpace with isWideColorGamutEnabled false`() {
        //Given
        val bitmap = mockk<Bitmap>()

        val colorAbility = mockk<IColorManagementAbility>()
        every { abilityBus.requireAbility(IColorManagementAbility::class.java) } returns colorAbility
        every { colorAbility.isWideColorGamutEnabled } returns false

        val targetColorSpace = mockk<ColorSpace>()
        mockkStatic(ColorSpace::class)
        every { ColorSpace.get(ColorSpace.Named.SRGB) } returns targetColorSpace

        every { colorAbility.adjustColorSpace(bitmap, targetColorSpace) } returns true
        every { colorAbility.close() } returns Unit

        //When
        val result = requester.adjustColorSpace(bitmap)
        //Then
        Assert.assertTrue(result)
        verify { colorAbility.adjustColorSpace(bitmap, targetColorSpace) }
        verify { colorAbility.close() }
    }

    @Test
    fun `should return false when adjustColorSpace with isWideColorGamutEnabled null`() {
        //Given
        val bitmap = mockk<Bitmap>()

        val colorAbility = mockk<IColorManagementAbility>()
        every { abilityBus.requireAbility(IColorManagementAbility::class.java) } returns colorAbility
        every { colorAbility.isWideColorGamutEnabled } returns false

        val targetColorSpace = mockk<ColorSpace>()
        mockkStatic(ColorSpace::class)
        every { ColorSpace.get(ColorSpace.Named.SRGB) } returns targetColorSpace
        every { colorAbility.adjustColorSpace(bitmap, targetColorSpace) } returns null
        every { colorAbility.close() } returns Unit

        //When
        val result = requester.adjustColorSpace(bitmap)
        //Then
        Assert.assertFalse(result)
        verify { colorAbility.adjustColorSpace(bitmap, targetColorSpace) }
        verify { colorAbility.close() }
    }

    private class TestLocalMediaBitmapRequester(
        context: Context,
        originalResource: LocalMediaResourceKey,
        options: ResourceGetOptions,
        abilityBus: IAbilityBus,
        cancelable: ICancelable?
    ) : AbsGalleryMediaBitmapRequester(context, originalResource, options, abilityBus, null, null, cancelable) {
        override fun requestSource(): Bitmap? {
            return decodeLocalFile(DecodeOption(originalResource))
        }

        override fun onDecodeOriginal(
            decodeOption: DecodeOption,
            type: Int,
            options: ResourceGetOptions,
            cancelable: ICancelable?
        ): Bitmap? {
            return null
        }
    }
}