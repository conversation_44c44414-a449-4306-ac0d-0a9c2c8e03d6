/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UriImageRegionDecoderRequestTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.regiondecoder

import android.content.Context
import android.net.Uri
import android.os.ParcelFileDescriptor
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.standard_lib.codec.DecoderWrapper
import com.oplus.gallery.standard_lib.codec.IRegionDecoder
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.FileDescriptor

class UriImageRegionDecoderRequestTest {
    private lateinit var resourceKey: UriMediaResourceKey
    private lateinit var cancelable: Cancelable
    private lateinit var requester: UriImageRegionDecoderRequest
    private lateinit var uriItem: BaseUriItem
    private val uri = mockk<Uri>()
    private val mimeType = "mimeType"
    private val context = mockk<Context>()
    private val filePath = "filePath"
    private val dateTime = 22L

    @Before
    fun before() {
        uriItem = mockk()
        every { uriItem.filePath } returns filePath
        every { uriItem.dateModifiedInSec } returns dateTime
        resourceKey = mockk()
        every { resourceKey.contentUri } returns uri
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.uriItem } returns uriItem

        cancelable = mockk()
        ContextGetter.context = context

        requester = spyk(UriImageRegionDecoderRequest(resourceKey, cancelable))
    }

    @After
    fun after() {
        unmockkAll()
    }

    @Test
    fun `should return null when request with prepareInputFile false`() {
        every { requester.prepareInputFile(any()) } returns false

        val result = requester.request()

        Assert.assertNull(result)
    }

    @Test
    fun `should return null when request with isSharable true getIRegionDecoder null`() {
        every { requester.prepareInputFile(any()) } returns true

        every { requester.isSharable() } returns true

        val fd = mockk<FileDescriptor>()
        val parcelFileDescriptor = mockk<ParcelFileDescriptor>()
        every { parcelFileDescriptor.fileDescriptor } returns fd
        every { requester.pfd } returns parcelFileDescriptor

        val type = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL
        every { requester.inThumbnailType } returns type
        val isYuvAndLarge = true
        every { uriItem.isYuvAndLargePage(type) } returns isYuvAndLarge

        mockkStatic(DecoderWrapper::class)
        every { DecoderWrapper.getIRegionDecoder(fd) } returns null

        every { requester.dealReload() } returns Unit

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return null when request with isSharable false getIRegionDecoder null`() {
        every { requester.prepareInputFile(any()) } returns true

        every { requester.isSharable() } returns false

        val fd = mockk<FileDescriptor>()
        val parcelFileDescriptor = mockk<ParcelFileDescriptor>()
        every { parcelFileDescriptor.fileDescriptor } returns fd
        every { requester.pfd } returns parcelFileDescriptor

        val type = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL
        every { requester.inThumbnailType } returns type
        val isYuvAndLarge = true
        every { uriItem.isYuvAndLargePage(type) } returns isYuvAndLarge

        mockkStatic(DecoderWrapper::class)
        every { DecoderWrapper.getIRegionDecoder(fd) } returns null

        every { requester.dealReload() } returns Unit

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return null when request with isSharable false getIRegionDecoder throws exception`() {
        every { requester.prepareInputFile(any()) } returns true

        every { requester.isSharable() } returns false

        val fd = mockk<FileDescriptor>()
        val parcelFileDescriptor = mockk<ParcelFileDescriptor>()
        every { parcelFileDescriptor.fileDescriptor } returns fd
        every { requester.pfd } returns parcelFileDescriptor

        val type = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL
        every { requester.inThumbnailType } returns type
        val isYuvAndLarge = true
        every { uriItem.isYuvAndLargePage(type) } returns isYuvAndLarge

        mockkStatic(DecoderWrapper::class)
        every { DecoderWrapper.getIRegionDecoder(fd) } throws Exception()

        every { requester.dealReload() } returns Unit

        //When
        val result = requester.request()

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return decoder when request with isSharable false getIRegionDecoder not null`() {
        every { requester.prepareInputFile(any()) } returns true

        every { requester.isSharable() } returns false

        val fd = mockk<FileDescriptor>()
        val parcelFileDescriptor = mockk<ParcelFileDescriptor>()
        every { parcelFileDescriptor.fileDescriptor } returns fd
        every { requester.pfd } returns parcelFileDescriptor

        val type = ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL
        every { requester.inThumbnailType } returns type
        val isYuvAndLarge = true
        every { uriItem.isYuvAndLargePage(type) } returns isYuvAndLarge

        val width = 1
        val height = 2
        val decoder = mockk<IRegionDecoder>()
        every { decoder.setTombstone(any()) } returns Unit
        every { decoder.getWidth() } returns width
        every { decoder.getHeight() } returns height

        mockkStatic(DecoderWrapper::class)
        every { DecoderWrapper.getIRegionDecoder(fd) } returns decoder

        every { requester.dealReload() } returns Unit
        every { uriItem.width = any() } returns Unit
        every { uriItem.height = any() } returns Unit

        //When
        val result = requester.request()

        //Then
        Assert.assertEquals(decoder, result)
        verify { uriItem.width = width }
        verify { uriItem.height = height }
    }
}