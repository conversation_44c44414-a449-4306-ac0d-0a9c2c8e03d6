/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalVideoBitmapRequesterTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.net.Uri
import android.os.ParcelFileDescriptor
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataSourceType
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoder
import com.oplus.gallery.standard_lib.codec.videoframe.VideoThumbnailDecoderUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import java.io.FileDescriptor

@Ignore
class LocalVideoBitmapRequesterTest {
    private lateinit var resourceKey: LocalMediaResourceKey
    private lateinit var abilityBus: IAbilityBus
    private lateinit var cancelable: Cancelable
    private lateinit var option: ResourceGetOptions
    private lateinit var requester: LocalVideoBitmapRequester
    private lateinit var mediaItem: LocalVideo
    private val context = mockk<Context>()
    private val path = mockk<Path>()
    private val defaultFilePath = "default_file_path"
    private val sourceType = 0
    private val mediaId = 1

    @Before
    fun before() {
        ContextGetter.context = context

        mediaItem = mockk()
        resourceKey = mockk()
        every { resourceKey.path } returns path
        every { resourceKey.filePath } returns defaultFilePath
        every { resourceKey.mediaId } returns mediaId
        every { resourceKey.mediaItem } returns mediaItem
        every { resourceKey.mediaItem.codecType } returns "video/avc"

        mockkStatic(DataSourceType::class)
        every { DataSourceType.judgeSourceType(defaultFilePath) } returns sourceType

        abilityBus = mockk()
        every { abilityBus.requireAbility(IConfigAbility::class.java) } returns null
        cancelable = mockk()
        option = ResourceGetOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL)
        requester = spyk(LocalVideoBitmapRequester(context, resourceKey, option, abilityBus, null,null, cancelable))

        mockkStatic(ThumbnailSizeUtils::class)
    }

    @After
    fun after() {
        unmockkAll()
    }

    /**
     * Test decodeVideoThumbnailWithKeyFrame
     */
    @Test
    fun `should return null when decodeVideoThumbnailWithKeyFrame with exception`() {
        //Given
        val size = 2
        val decoder = mockk<VideoThumbnailDecoder>()
        mockkStatic(VideoTypeUtils::class)
        every { VideoTypeUtils.isDolbyVideo(any<MediaItem>()) } returns false
        mockkStatic(VideoThumbnailDecoderUtils::class)
        every { VideoThumbnailDecoderUtils.getVideoThumbnailDecoder() } returns decoder

        mockkStatic(ApiLevelUtil::class)
        every { ApiLevelUtil.isAtLeastAndroidR() } returns false
        every { decoder.setDataSource(defaultFilePath) } returns Unit
        every { decoder.setThumbnailSize(size) } returns Unit
        every { decoder.decodeCoverBitmap() } throws Exception()

        //When
        val result = requester.decodeVideoThumbnailWithKeyFrame(mediaId, defaultFilePath, size)

        //Then
        Assert.assertNull(result)
        verify { decoder.close() }
    }

    @Test
    fun `should return null when decodeVideoThumbnailWithKeyFrame with isAtLeastAndroidR true and throws exceptions`() {
        //Given
        val size = 2
        val decoder = mockk<VideoThumbnailDecoder>()
        mockkStatic(VideoThumbnailDecoderUtils::class)
        mockkStatic(VideoTypeUtils::class)
        every { VideoTypeUtils.isDolbyVideo(any<MediaItem>()) } returns false
        every { VideoThumbnailDecoderUtils.getVideoThumbnailDecoder() } returns decoder

        mockkStatic(ApiLevelUtil::class)
        every { decoder.setThumbnailSize(size) } returns Unit
        every { decoder.decodeCoverBitmap() } throws Exception()
        val uri = mockk<Uri>()
        mockkStatic(MediaStoreUriHelper::class)
        every { MediaStoreUriHelper.getVideoUri(defaultFilePath, mediaId.toString()) } returns uri

        val fd = mockk<FileDescriptor>()
        val parcelFileDescriptor = mockk<ParcelFileDescriptor>()
        every { parcelFileDescriptor.fileDescriptor } returns fd
        every { parcelFileDescriptor.close() } returns Unit
        every { context.contentResolver.openFileDescriptor(uri, "r") } returns parcelFileDescriptor
        every { decoder.setDataSource(fd) } returns Unit

        //When
        val result = requester.decodeVideoThumbnailWithKeyFrame(mediaId, defaultFilePath, size)

        //Then
        Assert.assertNull(result)
        verify { decoder.close() }
        verify { parcelFileDescriptor.close() }
    }
}