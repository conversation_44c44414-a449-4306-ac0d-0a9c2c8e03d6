/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UriVideoBitmapRequesterTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized

class UriVideoBitmapRequesterTest {
    private lateinit var resourceKey: UriMediaResourceKey
    private lateinit var options: ResourceGetOptions
    private lateinit var cancelable: Cancelable
    private lateinit var uri: Uri
    private lateinit var uriItem: BaseUriItem
    private lateinit var requester: UriVideoBitmapRequester
    private val thumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL
    private val mimeType = "mime"
    private val context = mockk<Context>()

    @Before
    fun before() {
        uri = mockk()
        uriItem = mockk()
        resourceKey = mockk()
        every { resourceKey.contentUri } returns uri
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.uriItem } returns uriItem

        ContextGetter.context = context
        options = ResourceGetOptions(thumbnailType)
        cancelable = mockk()
        requester = spyk(UriVideoBitmapRequester(resourceKey, options, cancelable))
    }

    @After
    fun after() {
        unmockkAll()
    }

    /**
     * Test updateContentIfNeeded
     */
    @Test
    fun `should not call processFile when updateContentIfNeeded with mAlwaysReload false`() {
        //Given
        uriItem.mAlwaysReload = false

        //When
        requester.updateContentIfNeeded()

        //Then
        verify(inverse = true) { requester.processFile(any(), any()) }
    }

    @Test
    fun `should call processFile when updateContentIfNeeded with mAlwaysReload true`() {
        //Given
        uriItem.mAlwaysReload = true
        every { requester.processFile(context, uri) } returns Unit

        //When
        requester.updateContentIfNeeded()

        //Then
        verify { requester.processFile(context, uri) }
    }

    /**
     * Test processFile
     */
    @Test
    fun `should not set value when processFile with meta data null`() {
        //Given
        mockkConstructor(MediaMetadataRetriever::class)
        every { anyConstructed<MediaMetadataRetriever>().setDataSource(context, uri) } returns Unit
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH) } returns null
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT) } returns null
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION) } returns null
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE) } returns null

        //When
        requester.processFile(context, uri)

        //Then
        verify { anyConstructed<MediaMetadataRetriever>().release() }
    }

    @Test
    fun `should not set value when processFile with meta data empty`() {
        //Given
        mockkConstructor(MediaMetadataRetriever::class)
        every { anyConstructed<MediaMetadataRetriever>().setDataSource(context, uri) } returns Unit
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH) } returns ""
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT) } returns ""
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION) } returns ""
        every { anyConstructed<MediaMetadataRetriever>().extractMetadata(MediaMetadataRetriever.METADATA_KEY_MIMETYPE) } returns ""

        //When
        requester.processFile(context, uri)

        //Then
        verify { anyConstructed<MediaMetadataRetriever>().release() }
    }

    @Test
    fun `should call set value when processFile with meta data empty`() {
        //Given
        val metaDataWidth = 1
        val metaDataHeight = 2
        val metaDataRoataion = 3
        val metaDataContentType = "meta_content"

        every { uriItem.width = metaDataWidth } returns Unit
        every { uriItem.height = metaDataHeight } returns Unit
        every { uriItem.rotation = metaDataRoataion } returns Unit

        mockkConstructor(MediaMetadataRetriever::class)
        every { anyConstructed<MediaMetadataRetriever>().setDataSource(context, uri) } returns Unit
        every {
            anyConstructed<MediaMetadataRetriever>().extractMetadata(
                MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH
            )
        } returns metaDataWidth.toString()
        every {
            anyConstructed<MediaMetadataRetriever>().extractMetadata(
                MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT
            )
        } returns metaDataHeight.toString()
        every {
            anyConstructed<MediaMetadataRetriever>().extractMetadata(
                MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION
            )
        } returns metaDataRoataion.toString()
        every {
            anyConstructed<MediaMetadataRetriever>().extractMetadata(
                MediaMetadataRetriever.METADATA_KEY_MIMETYPE
            )
        } returns metaDataContentType

        //When
        requester.processFile(context, uri)

        //Then
        verify { uriItem.width = metaDataWidth }
        verify { uriItem.height = metaDataHeight }
        verify { uriItem.rotation = metaDataRoataion }
        Assert.assertEquals(metaDataContentType, requester.contentType)
        verify { anyConstructed<MediaMetadataRetriever>().release() }
    }
}

@RunWith(Parameterized::class)
class TestParseSubstring(
    private val inStr: String,
    private val inStartIndex: Int,
    private val inDefaultValue: Int,
    private val expectedValue: Int
) {
    @Test
    fun test_parseSubstring() {
        val result = UriVideoBitmapRequester.parseSubstring(inStr, inStartIndex, inDefaultValue)
        Assert.assertEquals(expectedValue, result)
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        fun getParams(): Collection<*> {
            return listOf(
                arrayOf("", 0, -1, -1),
                arrayOf("ddd", 3, -1, -1),
                arrayOf("a12", 0, -1, -1),
                arrayOf("a12b", 1, -1, 12)
            )
        }
    }
}