/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FastCaptureBitmapRequesterTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.diskcache.DiskLruCacheLoader
import com.oplus.gallery.foundation.cache.diskcache.DiskLruCacheUri
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class FastCaptureBitmapRequesterTest {
    private lateinit var resourceKey: LocalMediaResourceKey
    private lateinit var fastCaptureDetector: FastCaptureDetector
    private lateinit var options: ResourceGetOptions
    private lateinit var abilityBus: IAbilityBus
    private lateinit var fastCaptureBitmapRequester: FastCaptureBitmapRequester
    private val context = mockk<Context>()
    private val targetSize = 2

    @Before
    fun before() {
        ContextGetter.context = context

        resourceKey = mockk()
        fastCaptureDetector = mockk()
        options = ResourceGetOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL, inCacheOperation = CacheOperation.ReadDiskCache)
        abilityBus = mockk()

        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.getTargetSizeInType(options.inThumbnailType) } returns targetSize
        fastCaptureBitmapRequester = spyk(FastCaptureBitmapRequester(context, resourceKey, options, abilityBus, null, null, fastCaptureDetector, null))
    }

    @After
    fun after() {
        unmockkAll()
    }

    @Ignore
    @Test
    fun `should return null when request with getBitmapFromCache null`() {
        //Given
        val uri = mockk<Uri>()
        every { fastCaptureDetector.getFastCaptureFileUri(context) } returns uri
        every { fastCaptureBitmapRequester.getBitmapFromDiskCacheIfPermitted() } returns null
        every { fastCaptureBitmapRequester.getBitmapFromOriginalFile() } returns null

        //When
        val result = fastCaptureBitmapRequester.request()

        //Then
        Assert.assertNull(result)
    }

    /**
     * Test getBitmapFromCache
     */
    @Ignore
    @Test
    fun `should return null when getBitmapFromCache with diskLruCacheUri invalid`() {
        //Given
        val uri = mockk<Uri>()
        every { uri.scheme } returns ""
        every { uri.authority } returns ""
        every { uri.path } returns ""
        every { fastCaptureDetector.getFastCaptureFileUri(any()) } returns uri
        every { fastCaptureDetector.isTemporaryTmp() } returns false
        every { fastCaptureDetector.isTemporaryQuick() } returns false

        mockkConstructor(DiskLruCacheUri::class)
        every { anyConstructed<DiskLruCacheUri>().isInvalidUri() } returns true

        //When
        val result = fastCaptureBitmapRequester.getBitmapFromDiskCacheIfPermitted()

        //Then
        Assert.assertNull(result)
    }

    @Ignore
    @Test
    fun `should return null when getBitmapFromCache with loadBitmap null`() {
        //Given
        val uri = mockk<Uri>()
        every { uri.scheme } returns ""
        every { uri.authority } returns ""
        every { uri.path } returns ""
        every { fastCaptureDetector.getFastCaptureFileUri(any()) } returns uri
        every { fastCaptureDetector.isTemporaryTmp() } returns false
        every { fastCaptureDetector.isTemporaryQuick() } returns false

        mockkConstructor(DiskLruCacheUri::class)
        every { anyConstructed<DiskLruCacheUri>().isInvalidUri() } returns false

        mockkStatic(DiskLruCacheLoader::class)
        every { DiskLruCacheLoader.loadBitmap(any()) } returns null

        //When
        val result = fastCaptureBitmapRequester.getBitmapFromDiskCacheIfPermitted()

        //Then
        Assert.assertNull(result)
    }

    @Ignore
    @Test
    fun `should return null when getBitmapFromCache with loadBitmap returns cached`() {
        //Given
        val uri = mockk<Uri>()
        every { uri.scheme } returns ""
        every { uri.authority } returns ""
        every { uri.path } returns ""
        every { fastCaptureDetector.getFastCaptureFileUri(any()) } returns uri
        every { fastCaptureDetector.isTemporaryTmp() } returns false
        every { fastCaptureDetector.isTemporaryQuick() } returns false

        mockkConstructor(DiskLruCacheUri::class)
        every { anyConstructed<DiskLruCacheUri>().isInvalidUri() } returns false

        val cachedBitmap = mockk<Bitmap>()
        mockkStatic(DiskLruCacheLoader::class)
        every { DiskLruCacheLoader.loadBitmap(any()) } returns cachedBitmap

        //When
        val result = fastCaptureBitmapRequester.getBitmapFromDiskCacheIfPermitted()

        //Then
        Assert.assertEquals(cachedBitmap, result)
    }
}