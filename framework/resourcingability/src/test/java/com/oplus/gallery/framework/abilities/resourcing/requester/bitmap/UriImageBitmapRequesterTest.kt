/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UriImageBitmapRequesterTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/29    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.requester.bitmap

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.exif.raw.ExifUtils
import com.oplus.gallery.foundation.taskscheduling.Cancelable
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.standard_lib.codec.RegionDecoder
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import java.io.InputStream

class UriImageBitmapRequesterTest {
    private lateinit var resourceKey: UriMediaResourceKey
    private lateinit var options: ResourceGetOptions
    private lateinit var cancelable: Cancelable
    private lateinit var uri: Uri
    private lateinit var uriItem: BaseUriItem
    private lateinit var requester: UriImageBitmapRequester
    private val thumbnailType = ThumbnailSizeUtils.TYPE_THUMBNAIL
    private val mimeType = "mime"
    private val context = mockk<Context>()

    @Before
    fun before() {
        uri = mockk()
        uriItem = mockk()
        resourceKey = mockk()
        every { resourceKey.contentUri } returns uri
        every { resourceKey.mimeType } returns mimeType
        every { resourceKey.uriItem } returns uriItem

        ContextGetter.context = context
        options = ResourceGetOptions(thumbnailType)
        cancelable = mockk()
        requester = spyk(UriImageBitmapRequester(resourceKey, options, cancelable))
    }

    @After
    fun after() {
        unmockkAll()
    }

    /**
     * Test updateRotationIfNeed
     */
    @Test
    fun `should not set rotation when updateRotationIfNeed with contentType not JPEG`() {
        //given
        every { requester.contentType } returns "mimetype"

        //When
        requester.updateRotationIfNeed()

        //Then
        verify(inverse = true) { uriItem.rotation = any() }
    }

    /**
     * Test updateRotationIfNeed
     */
    @Test
    fun `should not set rotation when updateRotationIfNeed with rotation validate`() {
        //given
        every { requester.contentType } returns "mimetype"
        every { uriItem.rotation } returns 0

        //When
        requester.updateRotationIfNeed()

        //Then
        verify(inverse = true) { uriItem.rotation = any() }
    }

    @Test
    fun `should not set rotation when updateRotationIfNeed with openInputStream throw exception`() {
        //given
        every { requester.contentType } returns MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
        every { uriItem.rotation } returns UriImageBitmapRequester.INVALIDATE_ROTATION

        val contentResolver = mockk<ContentResolver>()
        every { context.contentResolver } returns contentResolver
        every { contentResolver.openInputStream(uri) } throws Exception()

        //When
        requester.updateRotationIfNeed()

        //Then
        verify(inverse = true) { uriItem.rotation = any() }
    }

    @Test
    fun `should not set rotation when updateRotationIfNeed with openInputStream success`() {
        //given
        val rotation = 90
        every { requester.contentType } returns MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
        every { uriItem.rotation } returns UriImageBitmapRequester.INVALIDATE_ROTATION
        every { uriItem.rotation = rotation } returns Unit


        val contentResolver = mockk<ContentResolver>()
        every { context.contentResolver } returns contentResolver

        val inputStream = mockk<InputStream>()
        every { contentResolver.openInputStream(uri) } returns inputStream

        mockkStatic(ExifUtils::class)
        every { ExifUtils.getOrientation(inputStream) } returns rotation

        //When
        requester.updateRotationIfNeed()

        //Then
        verify { uriItem.rotation = rotation }
        verify { inputStream.close() }
    }

    /**
     * Test updateThumbnailTargetSize
     */
    @Ignore
    @Test
    fun `should use TYPE_LARGE_THUMBNAIL when updateThumbnailTargetSize with type TYPE_THUMBNAIL and not support regiondecoder`() {
        //Given
        every { requester.inThumbnailType } returns ThumbnailSizeUtils.TYPE_THUMBNAIL

        mockkStatic(RegionDecoder::class)
        every { RegionDecoder.isSupportRegionDecode(requester.contentType) } returns false

        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.getTargetSizeInType(ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL) } returns 0

        //When
        requester.updateThumbnailTargetSize()

        //Then
        verify { ThumbnailSizeUtils.getTargetSizeInType(ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL) }
    }

    @Ignore
    @Test
    fun `should use original type when updateThumbnailTargetSize with type not TYPE_THUMBNAIL`() {
        //Given
        val type = ThumbnailSizeUtils.getVideoMicroThumbnailKey()
        every { requester.inThumbnailType } returns type

        mockkStatic(RegionDecoder::class)
        every { RegionDecoder.isSupportRegionDecode(requester.contentType) } returns false

        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.getTargetSizeInType(type) } returns 0

        //When
        requester.updateThumbnailTargetSize()

        //Then
        verify { ThumbnailSizeUtils.getTargetSizeInType(type) }
    }

    @Ignore
    @Test
    fun `should use original type when updateThumbnailTargetSize with type nsupport regiondecoder`() {
        //Given
        val type = ThumbnailSizeUtils.TYPE_THUMBNAIL
        every { requester.inThumbnailType } returns type

        mockkStatic(RegionDecoder::class)
        every { RegionDecoder.isSupportRegionDecode(mimeType) } returns true

        mockkStatic(ThumbnailSizeUtils::class)
        every { ThumbnailSizeUtils.getTargetSizeInType(type) } returns 0

        //When
        requester.updateThumbnailTargetSize()

        //Then
        verify { ThumbnailSizeUtils.getTargetSizeInType(type) }
    }
}