/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CacheKeyFactoryTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/21
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/21    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.caching.key

import android.net.Uri
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.BaseUriItem
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.framework.abilities.resourcing.TileParams
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.LocalMediaResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.PredecodeResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.TileResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.UriMediaResourceKey
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CacheKeyFactoryTest {

    @Before
    fun setup() {
        mockkStatic(VideoTypeUtils::class)
        every { VideoTypeUtils.getCodecType(any()) } returns null
    }

    @Test
    fun `should return null when createCacheKey with LocalMediaItem with path null`() {
        //Given
        val localMediaItem = mockk<LocalMediaItem>()
        every { localMediaItem.path } returns null

        //When
        val result = CacheKeyFactory.createLocalMediaCacheKey(localMediaItem)

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return LocalMediaCacheKey when createCacheKey with LocalMediaItem`() {
        //Given
        val path = mockk<Path>()
        val mimeType = "image"
        val localMediaItem = mockk<LocalMediaItem>()
        every { localMediaItem.path } returns path
        every { localMediaItem.dateModifiedInSec } returns 0L
        every { localMediaItem.mimeType } returns mimeType
        every { localMediaItem.mediaType } returns MEDIA_TYPE_VIDEO

        //When
        val result = CacheKeyFactory.createLocalMediaCacheKey(localMediaItem)

        //Then
        Assert.assertNotNull(result)
        Assert.assertTrue(result is LocalMediaCacheKey)
        Assert.assertEquals(path, (result as LocalMediaCacheKey).path)
        Assert.assertEquals(0L, result.dateModifiedInSec)
        Assert.assertEquals(mimeType, result.mimeType)
        Assert.assertEquals(CropParams.centerRectCrop(), result.cropParams)
        Assert.assertEquals(null, result.codecType)
    }

    @Test
    fun `should return null when createCacheKey with BaseUriItem with uri null`() {
        //Given
        val uriItem = mockk<BaseUriItem>()
        every { uriItem.contentUri } returns null

        //When
        val result = CacheKeyFactory.createUriCacheKey(uriItem)

        //Then
        Assert.assertNull(result)
    }

    @Test
    fun `should return UriCacheKey when createCacheKey with BaseUriItem with uri valid`() {
        //Given
        val uri = mockk<Uri>()
        val uriItem = mockk<BaseUriItem>()
        every { uriItem.contentUri } returns uri

        //When
        val result = CacheKeyFactory.createUriCacheKey(uriItem)

        //Then
        Assert.assertNotNull(result)
        Assert.assertTrue(result is UriCacheKey)
        Assert.assertEquals(uri, (result as UriCacheKey).contentUri)
        Assert.assertEquals(CropParams.centerRectCrop(), result.cropParams)
    }

    @Test
    fun `should return TileCacheKey when createCacheKey with tileParams with uri valid`() {
        //Given
        val path = mockk<Path>()
        val tileParams = TileParams(0, 0, 1, 2, 2, false)
        val dateModifiedInSec = 0L

        //When
        val result = CacheKeyFactory.createTileCacheKey(path, tileParams, dateModifiedInSec)

        //Then
        Assert.assertNotNull(result)
        Assert.assertTrue(result is TileCacheKey)
        Assert.assertEquals(path, (result as TileCacheKey).path)
        Assert.assertEquals(tileParams, result.tileParams)
        Assert.assertEquals(dateModifiedInSec, result.dateModifiedInSecond)
    }

    @Test
    fun `should return GeoLocationCacheKey when createCacheKey with latitude and longitude`() {
        //Given
        val longitude = 112.0
        val latitude = 22.0

        //When
        val result = CacheKeyFactory.createGeoCacheKey(latitude, longitude)

        //Then
        Assert.assertNotNull(result)
        Assert.assertTrue(result is GeoLocationCacheKey)
    }

    @Test
    fun `should return null when createCacheKey with LocalMediaCacheKey exception`() {
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.path } returns null

        val result = CacheKeyFactory.createCacheKey(resourceKey)

        Assert.assertNull(result)
    }

    @Test
    fun `should return LocalMediaCacheKey when createCacheKey with LocalResourceKey`() {
        val path = mockk<Path>()
        val resourceKey = mockk<LocalMediaResourceKey>()
        every { resourceKey.path } returns path
        every { resourceKey.dateModifiedInSecond } returns 0L
        every { resourceKey.mimeType } returns "Image"
        every { resourceKey.mediaItem.codecType } returns null
        every { resourceKey.orientation } returns 0
        val result = CacheKeyFactory.createCacheKey(resourceKey, CropParams.noCrop())

        Assert.assertNotNull(result)
        Assert.assertTrue(result is LocalMediaCacheKey)
        Assert.assertEquals(CropParams.noCrop(), (result as LocalMediaCacheKey).cropParams)
    }

    @Test
    fun `should return LocalMediaCacheKey when createCacheKey with PredecodeResourceKey`() {
        val path = mockk<Path>()
        val resourceKey = mockk<PredecodeResourceKey>()
        every { resourceKey.path } returns path
        every { resourceKey.dateModifiedInSec } returns 0L
        every { resourceKey.codecType } returns null

        val result = CacheKeyFactory.createCacheKey(resourceKey)

        Assert.assertNotNull(result)
        Assert.assertTrue(result is LocalMediaCacheKey)
        Assert.assertEquals(CropParams.centerRectCrop(), (result as LocalMediaCacheKey).cropParams)
        Assert.assertEquals(path, result.path)
        Assert.assertEquals(0L, result.dateModifiedInSec)
        Assert.assertNull(result.mimeType)
    }

    @Test
    fun `should return UriCacheKey when createCacheKey with UriMediaResourceKey`() {
        val uri = mockk<Uri>()
        val resourceKey = mockk<UriMediaResourceKey>()
        every { resourceKey.contentUri } returns uri

        val result = CacheKeyFactory.createCacheKey(resourceKey)

        Assert.assertNotNull(result)
        Assert.assertTrue(result is UriCacheKey)
        Assert.assertEquals(CropParams.centerRectCrop(), (result as UriCacheKey).cropParams)
        Assert.assertEquals(uri, result.contentUri)
    }

    @Test
    fun `should return TileCacheKey when createCacheKey with TileResourceKey`() {
        val path = mockk<Path>()
        val tileParams = TileParams(0, 0, 1, 2, 2, false)
        val resourceKey = mockk<TileResourceKey>()
        every { resourceKey.path } returns path
        every { resourceKey.tileParams } returns tileParams
        every { resourceKey.dateModifiedInSecond } returns 0L

        val result = CacheKeyFactory.createCacheKey(resourceKey)

        Assert.assertNotNull(result)
        Assert.assertTrue(result is TileCacheKey)
        Assert.assertEquals(path, (result as TileCacheKey).path)
        Assert.assertEquals(tileParams, result.tileParams)
        Assert.assertEquals(0L, result.dateModifiedInSecond)
    }

    @Test
    fun `should return null when createCacheKey with TileResourceKey`() {
        val resourceKey = mockk<ResourceKey>()

        val result = CacheKeyFactory.createCacheKey(resourceKey)

        Assert.assertNull(result)
    }

    @Test
    fun should_return_right_key_when_createSuperTextCacheKey_with_input() {
        // given
        val filePath = "filePath"
        val dateModifiedInSec = 20220815L
        val size = 123L
        val orientation = 270
        // when
        val result = CacheKeyFactory.createSuperTextCacheKey(filePath, dateModifiedInSec, size, orientation)
        // then
        if (result is LocalMediaCacheKey) {
            Assert.assertEquals(filePath, result.filePath)
            Assert.assertEquals(dateModifiedInSec, result.dateModifiedInSec)
            Assert.assertEquals(size, result.size)
            Assert.assertEquals(orientation, result.orientation)
        } else {
            Assert.fail()
        }
    }
}