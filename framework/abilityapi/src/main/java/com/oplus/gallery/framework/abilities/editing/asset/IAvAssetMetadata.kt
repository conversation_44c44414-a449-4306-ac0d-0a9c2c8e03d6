/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : IAvAssetMetadata.kt
 ** Description: 资源元信息读写接口
 ** Version    : 1.0
 ** Date       : 2024/6/4
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2024/6/4    1.0              init
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.editing.asset

import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_META_DATA
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_ULTRA_HDR_INFO
import com.oplus.gallery.foundation.codec.extend.ExtendStruct

/**
 * 资源元信息读写接口。
 * 提供资源元信息(宽高等基本信息、Exif信息、Extended信息、xmp信息)的读写。
 *
 * 使用方式：
 * ```
 * 单个字段获取：
 * getMetadata(MetadataType.EXIF, ExifDataKey.TAG_ORIENTATION)
 *
 * 多个字段获取：
 * getMetadata(
 *     mutableMapOf<IAvAssetMetadata.MetadataType, List<String>>().apply {
 *         put(IAvAssetMetadata.MetadataType.EXTENDED, mutableListOf<String>().apply {
 *             add(IAvAssetMetadata.ExifDataKey.TAG_DATETIME)
 *             add(IAvAssetMetadata.ExifDataKey.TAG_DATETIME)
 *         })
 *         put(IAvAssetMetadata.MetadataType.EXIF, mutableListOf<String>().apply {
 *             add(IAvAssetMetadata.ExtendedDataKey.CROP_REGION)
 *             add(IAvAssetMetadata.ExtendedDataKey.FILTER)
 *         })
 *     }
 * )
 *
 * 最后在针对获取的对象进行强转，得到实际的数据类型
 * ```
 * Marked by Johnny 2024/6/5 先提供读接口，管线只需要读。业务暂时也不需要写接口，适配工作量会比较大。
 */
interface IAvAssetMetadata {

    /**
     * 获取指定资源元信息字段的值，业务获取后要自行强转
     * 该方法用于获取元信息裸流
     */
    fun getMetadata(type: MetadataType, dataKey: String): Any?

    /**
     * 获取一组指定资源元信息字段的值，业务获取后要自行强转
     * 该方法用于获取元信息裸流
     */
    fun getMetadata(dataKeys: Map<MetadataType, List<String>>): Map<String, Any?>?

    /**
     * 保存一组指定资源元信息字段的值
     * 该方法用于设置元信息裸流
     */
    fun setMetadata(type: MetadataType, dataKey: String, value: Any)

    /**
     * 获取文件扩展信息对应的ExtendStruct
     */
    fun getMetadataStruct(type: MetadataType, dataKey: String): ExtendStruct<*>?

    /**
     * 保存一组指定资源元信息对应的ExtendStruct
     */
    fun setMetadataStruct(type: MetadataType, dataKey: String, struct: ExtendStruct<*>)

    /**
     * 释放缓存
     * */
    fun releaseCache()

    companion object {
        /**
         * 不能获取附加asset的资源的key
         */
        val EXCLUDED_ASSOCIATE_METADATA_KEYS: Set<String> by lazy { setOf(EXTEND_KEY_ULTRA_HDR_INFO, EXTEND_KEY_LOCAL_HDR_META_DATA) }
    }
}