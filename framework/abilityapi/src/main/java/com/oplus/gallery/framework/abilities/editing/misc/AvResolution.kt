/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvResolution
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/2/27 18:18
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/2/27		  1.0		 AvResolution
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.misc

/**
 * 媒体文件分辨率，默认支持4K，业务也可自行配置
 * @param width 定义的输出宽度
 * @param height 定义的输出高度
 * @param depth 定义的输出位深信息
 */
data class AvResolution(
    val width: Int,
    val height: Int,
    val depth: Int = DEFAULT_DEPTH_SIZE
) {
    companion object {
        const val DEFAULT_DEPTH_SIZE = 8
        private const val DEFAULT_LIMIT_LENGTH = 4096

        /**
         * 默认效果图预览分辨率，默认支持到4K
         */
        @Deprecated("业务不要使用这个resolution，这是固定分辨率的，没有适配不同平台")
        val DEFAULT_PREVIEW: AvResolution = AvResolution(DEFAULT_LIMIT_LENGTH, DEFAULT_LIMIT_LENGTH, DEFAULT_DEPTH_SIZE)

        /**
         * 默认效果图保存分辨率，默认支持到4K
         */
        @Deprecated("业务不要使用这个resolution，这是固定分辨率的，没有适配不同平台")
        val DEFAULT_OUTPUT: AvResolution = AvResolution(DEFAULT_LIMIT_LENGTH, DEFAULT_LIMIT_LENGTH, DEFAULT_DEPTH_SIZE)
    }
}