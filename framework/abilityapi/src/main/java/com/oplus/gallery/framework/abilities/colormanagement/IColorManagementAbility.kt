/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IColorManagementAbility.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/02/11
 ** Author: zhangjisong
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>            <version>        <desc>
 ** ------------------------------------------------------------------------------
 ** zhangjisong                     2022/02/11        1.0
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.colormanagement

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.view.Window
import androidx.annotation.MainThread

/**
 * 色彩管理能力接口定义
 *
 * 提供色彩管理相关操作，主要职责包含如下：
 * 1. 当前广色域是否开启（读、写）
 * 2. 根据当前广色域是否开启，得到目前使用的色彩空间（开启广色域：DISPLAY_P3；关闭广色域：SRGB）
 * 3. 设置 [Bitmap] 的色彩空间为指定的 [ColorSpace]，仅仅设置标志位而已，不涉及 [Bitmap] 内部数据
 * 4. 根据提供的色彩空间，转换 [Bitmap] 内部数据
 */
interface IColorManagementAbility : AutoCloseable {
    /**
     * 广色域是否被启用
     * 主要适用于色彩管理版本，需要相册主动处理色域变换
     *
     * - true：广色域已被启用
     *          （1）色彩管理1.0自然模式，需相册自己转换相素值到P3
     *          （2）色彩管理2.0（设置---显示与亮度---屏幕色彩模式）
     *
     * - false：广色域已被禁用
     *          色彩管理1.0的生动模式或不支持色彩管理的机器的状态，不支持色彩管理，默认把色域均设为ColorSpace.SRGB，效果偏艳
     *
     *   isWideColorGamutEnabled=true 色彩管理2.0也是设置为true
     *
     * 业务方可以修改该值，建议修改时机：
     * 1. [android.app.Activity.onCreate] 中读取 [android.view.Window.isWideColorGamut] 修改
     * 2. [android.app.Activity.onStart] 中读取 [android.view.Window.isWideColorGamut] 修改
     */
    var isWideColorGamutEnabled: Boolean?

    /**
     * 显示层layer是否支持色彩管理2.0，需要配合屏幕是否支持广色域一起使用
     *
     * - true：显示层layer支持广色域，由系统进行色彩管理（全模式生效）
     * - false：显示层layer支持广色域，由相册内部进行色彩管理（仅自然模式生效）
     */
    val isDisplaySupportWCG: Boolean

    /**
     * 根据当前广色域是否开启，得到目前使用的色彩空间（开启广色域：DISPLAY_P3；关闭广色域：SRGB）
     *
     * - DISPLAY_P3: [isWideColorGamutEnabled] == true
     * - SRGB: [isWideColorGamutEnabled] != true
     */
    val colorSpace: ColorSpace?

    /**
     * 如果在不支持色彩管理的机器上，需要将bitmap的colorSpace设置为sRGB，
     * 在一些异常场景下，比如图片的colorSpace为空或者未知的时候，设置为sRGB
     */
    fun adjustBitmapColorSpace(bitmap: Bitmap?)


    /**
     * 设置 [srcBitmap] 的色彩空间为指定的 [targetColorSpace]，仅仅设置标志位而已，不涉及 [Bitmap] 内部像素数据
     *
     * @param srcBitmap 需要设置色彩空间的 [Bitmap]
     * @param targetColorSpace 目标色彩空间，如果参数 [targetColorSpace] 为 null，则使用 [colorSpace] 代替
     *
     * @return 是否设置成功，即函数执行完成后，[Bitmap] 的色彩空间是否与目标色彩空间一致
     */
    fun adjustColorSpace(srcBitmap: Bitmap, targetColorSpace: ColorSpace? = null): Boolean?


    /**
     * 设置 [window] 的色彩模式
     *
     * 注意：给window设置colorMode需要立即执行！！！
     *
     * 尝试调用 ability 调整色彩模式，对window执行两种处理策略
     *
     * 使用策略处理当前 window，并获取返回值（返回值代表是否有调整 window.colorMode）
     *
     * - 针对色彩管理1.0：
     *      - 判断window是否开启了WCG，用于刷新isWideColorGamutEnabled;
     * - 针对色彩管理2.0：
     *      - 判断底层是否配置了色彩管理2.0的feature，
     *      - 设置window的colorMode到WCG
     *      - 用于刷新ColorModelManager.isSupportColorManagementV2;
     * @return 是否有调整，目前只针对色彩管理一期的调整需要返回值
     */
    @MainThread
    fun tryAdjustColorMode(window: Window): Boolean?

    /**
     *
     * @param srcBitmap 需要转换像素数据的 [Bitmap]
     * @param targetColorSpace 目标色彩空间，如果参数 [ColorSpace] 为 null，则使用 [colorSpace] 代替
     *
     * @return 转换之后的 [Bitmap]，有如下场景：
     * - 如果不需要转换像素数据，则直接返回参数 [srcBitmap]
     * - 如果需要转换像素数据，但参数 [Bitmap] 是 [Bitmap.isMutable]，则转换后返回 [srcBitmap]
     * - 如果需要转换像素数据，但参数 [Bitmap] 不是 [Bitmap.isMutable]，则复制一份新的 [Bitmap]，转换后返回
     */
    fun convertBitmapPixels(srcBitmap: Bitmap, targetColorSpace: ColorSpace? = null): Bitmap?

    /**
     * HDR提亮模式切换，打开时屏幕提亮，并渲染bitmap增益信息gainmap，并压暗无gainmap元素
     *
     * @param window
     * @param switchOn true-打开HDR模式，false-关闭HDR模式
     * @return 返回hdr模式是否变化，true-有变化，false-无变化
     */
    @MainThread
    fun switchHdrColorMode(window: Window, switchOn: Boolean): Boolean

    /**
     * 10bit模式切换，打开时Layer的RGBA位宽修正为1010102状态
     *
     * @param window
     * @param switchOn true-打开10bit模式，false-关闭10bit模式
     */
    @MainThread
    fun switch10bitColorMode(window: Window, switchOn: Boolean)

    /**
     * [AutoCloseable] 的关闭接口
     */
    override fun close() = Unit
}
