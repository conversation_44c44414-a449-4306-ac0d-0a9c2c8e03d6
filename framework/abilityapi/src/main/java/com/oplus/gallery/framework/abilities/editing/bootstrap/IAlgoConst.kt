/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IAlgoConst
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/7/4
 ** Author: liyunting
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** liyunting                        2024/7/4		  1.0		 IAlgoConst
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.bootstrap

/**
 * 编辑事务特效算法配置常量
 *
 * CPU算法
 * 普通消除：libmagiceliminateplugin
 * AI消除：libaieliminateplugin
 * AI修复：libairepairplugin
 * AI隐性水印：libaiwatermarkplugin
 * 图像增强：libimagequalityenhanceplugin
 * 空插件：libemptyplugin
 * 水印：libwatermarkplugin
 *
 * GPU算法
 * 裁剪旋转：libtransformplugin
 * 画框：libphotoframeplugin
 * 纹理融合：libtextureblendplugin
 * 马赛克：libmosaicplugin
 * 图像替换：libimagereplaceplugin
 * 调节：libadjustmentplugin
 * 滤镜：libfilterplugin
 * 虚化：libblurplugin
 * 美颜：libbeautyplugin
 * IPU滤镜：libipufilterplugin
 * 表情优化：libaibesttakeplugin
 */
interface IAlgoConst {
    companion object {
        // 以下是 GPU 算法
        const val TRANSFORM = "transform_plugin"                        //GPU算法
        const val ADJUST = "adjustment_plugin"                          //GPU算法
        const val FILTER = "filter_plugin"                              //GPU算法
        const val FILTER_IPU = "ipu_filter_plugin"                      //GPU算法
        const val MOSAIC = "mosaic_plugin"                              //GPU算法
        const val BEAUTY = "beauty_plugin"                              //GPU算法
        const val BLUR = "blur_plugin"                                  //GPU算法
        const val BORDER = "photoframe_plugin"                          //GPU算法
        const val AIGRAFFITI = "ai_graffiti_plugin"                     //GPU算法
        const val TEXTURE_BLEND = "texture_blend_plugin"                //GPU算法
        const val IMAGE_REPLACE = "image_replace_plugin"                //GPU算法
        const val AI_BEST_TAKE = "ai_best_take_plugin"                  //GPU算法
        // 以下是 CPU 算法
        const val WATERMARK = "watermark_plugin"                        //CPU算法
        const val AIREPAIR = "ai_repair_plugin"                         //CPU算法
        const val IMAGE_QUALITY_ENHANCE = "image_quality_enhance_plugin"//CPU算法
        const val AI_COMPOSITION = "ai_composition_plugin"              //CPU算法
        const val AI_LIGHTING = "ai_lighting_plugin"                    //CPU算法
        const val AIWATERMARK = "ai_watermark_plugin"                   //CPU算法
        const val AIELIMINATE = "ai_eliminate_plugin"                   //CPU算法
        const val MAGICELIMINATE = "magic_eliminate_plugin"             //CPU算法
        const val AIHD = "aihd_plugin"                                  //CPU算法
        const val BEAUTY_IPU = "ipu_beauty_plugin"
        const val SLOW_MOTION = "slowmotion_plugin"                     //CPU算法
        const val REPAIR = "empty_plugin"                               //CPU算法

        const val IPU = "ipu"
        const val MEICAM = "meicam"
        const val OPPO = "oppo"

        /**
         * 用于算法插件注册反射生成对应插件MetaProvider
         * 配合算法修复插件注册失败问题
         */
        const val TRANSFORM_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.transform.provider.TransformPluginMetaProvider"
        const val ADJUST_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.adjustment.provider.AdjustmentPluginMetaProvider"
        const val FILTER_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.filter.provider.FilterPluginMetaProvider"
        const val FILTER_IPU_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.ipufilter.provider.IpuFilterPluginMetaProvider"
        const val WATERMARK_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.watermark.provider.WatermarkPluginMetaProvider"
        const val AIWATERMARK_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.aiwatermark.provider.AIWatermarkPluginMetaProvider"
        const val MOSAIC_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.mosaic.provider.MosaicPluginMetaProvider"
        const val BEAUTY_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.beauty.provider.BeautyPluginMetaProvider"
        const val BEAUTY_IPU_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.ipubeauty.provider.IpuBeautyPluginMetaProvider"
        const val AIREPAIR_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.airepair.AIRepairPluginMetaProvider"
        const val IMAGE_QUALITY_ENHANCE_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.imagequalityenhance.ImageQualityEnhancePluginMetaProvider"
        const val IMAGE_AI_COMPOSITION_PROVIDER = "com.oplus.tbluniformeditor.plugins.aicomposition.AiCompositionPluginMetaProvider"
        const val AIELIMINATE_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.aieliminate.provider.AIEliminatePluginMetaProvider"
        const val MAGICELIMINATE_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.magiceliminate.provider.MagicEliminatePluginMetaProvider"
        const val BLUR_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.blur.provider.BlurPluginMetaProvider"
        const val BORDER_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.photoframe.provider.PhotoframePluginMetaProvider"
        const val TEXTURE_BLEND_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.textureblend.provider.TextureBlendPluginMetaProvider"
        const val REPAIR_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.empty.provider.EmptyPluginMetaProvider"
        const val AIHD_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.aihd.provider.AihdPluginMetaProvider"
        const val IMAGE_REPLACE_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.imagereplace.provider.ImageReplacePluginMetaProvider"
        const val AI_GRAFFITI_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.aigraffiti.provider.AIGraffitiPluginMetaProvider"
        const val AI_BEST_TAKE_PROVIDER = "com.oplus.tbluniformeditor.plugins.aibesttake.provider.AIBestTakePluginMetaProvider"
        const val AI_LIGHTING_META_PROVIDER = "com.oplus.tbluniformeditor.plugins.ailighting.AiLightingPluginMetaProvider"
        const val SLOW_MOTION_PROVIDER = "com.oplus.gallery.plugins.slowmotion.provider.SlowMotionPluginMetaProvider"
    }
}

/**
 * 算法的输入类型，即可以表示这个算法是 CPU 算法还是 GPU 算法
 */
enum class InputSourceType(val typeName: String) {
    CPU("CPU"),
    GPU("GPU");

    companion object {
        @JvmStatic
        fun fromValue(sourceTypeName: String): InputSourceType {
            return entries.find { it.typeName.equals(sourceTypeName, ignoreCase = true) }
                ?: throw IllegalArgumentException("Invalid value: $sourceTypeName. Valid values are: ${entries.joinToString { it.typeName }}")
        }
    }
}