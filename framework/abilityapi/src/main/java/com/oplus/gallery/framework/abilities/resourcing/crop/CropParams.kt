/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CropParams.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/06/07
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/06/07    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.resourcing.crop

import android.graphics.RectF
import com.oplus.gallery.framework.annotation.GalleryApp
import com.oplus.gallery.framework.annotation.GalleryFramework

/**
 * 定义裁剪参数基类，支持不裁剪、中心矩形裁剪、指定裁剪参数的矩形裁剪，可扩展其他形状的裁剪等。
 * 若无需裁剪，可取值 CropParams.noCrop()
 * 若为中心矩形裁剪，可取值 CropParams.centerCropRectF()
 * 若指定裁剪区域，可调用 CropParams.create()方法创建
 */
@GalleryApp
abstract class CropParams {

    /**
     * 获取裁剪参数的内容，不同裁剪方式，返回的数据类型不同。
     * 若为NoCrop，则返回为空。在调用之前，可通过CropParams.isNoCrop判断。
     * 若为中心矩形裁剪，该方法同样返回空，在调用之前，可通过CropParams.isNoCrop判断。
     * 若为其他裁剪方式，则直接返回对应的裁剪参数数据。如智能矩形裁剪，返回的是RectF对象，表示的裁剪区域的百分比。
     * @return Any?
     */
    open fun getCropContent(): Any? = null

    companion object {

        /**
         * 根据指定的参数，创建矩形裁剪实例，参数RectF为裁剪区域的百分比。
         * @param cropRectInPercent RectF
         * @return CropParams
         */
        @JvmStatic
        fun create(cropRectInPercent: RectF): CropParams = CropRectFParams(cropRectInPercent)

        /**
         * 返回中心裁剪的RectF实例对象，实例内容无实际意义，需使用对象class类型判断Crop的类型。
         * @return CropParams
         */
        @JvmStatic
        fun centerRectCrop(): CropParams = CenterRectCrop

        /**
         * 返回不需要裁剪的CropParams实例对象，实例内容无实际意义，需使用对象class类型判断Crop的类型。
         * @return CropParams
         */
        @JvmStatic
        fun noCrop(): CropParams = NoCrop
    }
}

/**
 * 中心矩形裁剪RectF实例定义，实例内容无实际意义。
 */
object CenterRectCrop : CropParams() {
    /**
     * 这里没法知道这个裁剪的矩阵，为了区分noCrop和centerRectCrop，centerRectCrop用一个固定的值
     */
    private const val CENTER_RECT_CROP_RECT_PARAM = -1F

    override fun getCropContent(): RectF = RectF(
        CENTER_RECT_CROP_RECT_PARAM,
        CENTER_RECT_CROP_RECT_PARAM,
        CENTER_RECT_CROP_RECT_PARAM,
        CENTER_RECT_CROP_RECT_PARAM
    )

    override fun toString(): String = "CenterRectCrop"
}

/**
 * 无需裁剪实例定义，实例内容无实际意义。
 */
private object NoCrop : CropParams() {

    override fun toString(): String = "NoCrop"
}

/**
 * 判断当前RectF对象的内容是否有意义，即是否可以用该对象的内容作为参数进行Bitmap的裁剪
 * @receiver CropParams
 * @return Boolean
 */
@GalleryFramework
fun CropParams.isCropContentValid(): Boolean = (!isCenterRectCrop() && !isNoCrop())

/**
 * 判断当前RectF对象是否表示为中心裁剪
 * @receiver CropParams
 * @return Boolean
 */
@GalleryFramework
fun CropParams.isCenterRectCrop(): Boolean = (this === CenterRectCrop)

/**
 * 判断当前RectF对象是否表示为无需要裁剪
 * @receiver CropParams
 * @return Boolean
 */
@GalleryFramework
fun CropParams.isNoCrop(): Boolean = (this === NoCrop)