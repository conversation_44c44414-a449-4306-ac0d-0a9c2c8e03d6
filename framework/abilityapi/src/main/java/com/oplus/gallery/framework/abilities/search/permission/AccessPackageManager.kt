/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AccessPackageManager.kt
 ** Description:访问包名管理，提供授权
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.permission

import com.oplus.gallery.foundation.util.debug.GLog

/**
 * 访问包名管理，提供授权
 */
object AccessPackageManager {

    private const val TAG = "AccessPackageManager"

    private var accessPackages: ArrayList<String> = ArrayList()

    /**
     * 清除sAccessPackages
     */
    @JvmStatic
    fun clearAccessPackages() {
        accessPackages.clear()
    }

    /**
     * 获取sAccessPackages元素个数
     */
    @JvmStatic
    fun getAccessPackagesSize(): Int {
        return accessPackages.size
    }

    /**
     * 删除sAccessPackages中index位置的元素
     */
    @JvmStatic
    fun removeAccessPackage(index: Int): String {
        return accessPackages.removeAt(index)
    }

    /**
     * add the access packageName, and this will use to enable to submit task
     * when onChange on SuggestionCacheContentObserver，only sAccessPackages is not empty.
     *
     * @param packageName
     */
    @JvmStatic
    fun addAccessPackage(packageName: String) {
        if (!accessPackages.contains(packageName)) {
            GLog.d(TAG, "[addAccessPackage]  packageName = $packageName")
            accessPackages.add(packageName)
        }
    }
}