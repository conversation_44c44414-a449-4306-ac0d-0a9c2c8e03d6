/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : UpdateContentAction.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/03/08
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/03/08      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.cast.action

import androidx.annotation.VisibleForTesting
import com.oplus.gallery.framework.abilities.cast.ICastAction
import com.oplus.gallery.framework.abilities.cast.action.CastSlideTo.SLIDE_TO_LEFT
import com.oplus.gallery.framework.abilities.cast.action.CastSlideTo.SLIDE_TO_NONE
import com.oplus.gallery.framework.abilities.cast.action.CastSlideTo.SLIDE_TO_RIGHT

/**
 * 投屏时资源滑动到哪一边
 * - [SLIDE_TO_NONE] 不需要滑动
 * - [SLIDE_TO_LEFT] 向左滑动
 * - [SLIDE_TO_RIGHT] 向右滑动
 */
enum class CastSlideTo {
    SLIDE_TO_NONE,
    SLIDE_TO_LEFT,
    SLIDE_TO_RIGHT
}

/**
 * 通知投屏更新当前item内容
 */
class UpdateContentAction private constructor(
    builder: Builder
) : ICastAction {

    override val requestId: Long = builder.requestId
    val replay: Boolean = builder.replay
    val updateCurrent: Boolean = builder.updateCurrent
    val slideTo: CastSlideTo = builder.slideTo
    val allowDuplicateRes: Boolean = builder.allowDuplicateRes
    val shouldPlayRestart: Boolean = builder.shouldPlayRestart

    /**
     * 图集中第几个资源（索引）
     */
    val slot: Int = builder.slot

    override fun toString(): String =
        "UpdateContentAction(" +
                "${super.toString()}, " +
                "requestId=$requestId, " +
                "replay=$replay, " +
                "updateCurrent=$updateCurrent, " +
                "slideTo=$slideTo, " +
                "allowDuplicateRes=$allowDuplicateRes, " +
                "slot=$slot" +
                ")"

    @VisibleForTesting(otherwise = VisibleForTesting.PACKAGE_PRIVATE)
    class Builder {
        /**
         * 标记一条指令的唯一标识，可以用于识别和跟踪某条指令的执行
         */
        var requestId = 0L

        /**
         * 是否重新播放，默认为false, 且暂时只用于DLNA投屏
         */
        var replay: Boolean = false

        /**
         * 是否更新当前索引的内容，默认为false, 仅当neighborIndex=0时 该参数有效（故意显示提示当前索引内容是否更新）
         */
        var updateCurrent: Boolean = false

        /**
         * 滑动到哪一边（左、右、none会有默认）
         */
        var slideTo: CastSlideTo = CastSlideTo.SLIDE_TO_NONE

        /**
         * 是否允许重复资源发送？默认为false
         * - false，表示不能重复发送同一个资源（去重，TV端兼容性不好）
         * - true，表示可以重复发送同一个资源 ，如视频播放完毕之后，点击播放，需要发送同一个资源给TV（TV侧可能之前的Uri已经过了有效期）
         */
        var allowDuplicateRes: Boolean = false

        /**
         * 图集中第几个资源（索引）
         */
        var slot: Int = -1

        /**
         * 判断dlna投屏情况下，是否时滑动切换数据
         */
        var shouldPlayRestart: Boolean = false

        fun build(): ICastAction {
            return UpdateContentAction(this)
        }
    }
}