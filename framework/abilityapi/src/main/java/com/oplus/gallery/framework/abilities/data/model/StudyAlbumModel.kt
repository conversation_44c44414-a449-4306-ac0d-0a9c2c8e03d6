/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - StudyAlbumModel.kt
 ** Description: 学习图集数据模型
 ** Version: 1.0
 ** Date: 2025/5/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2025/5/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.data.model

import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.framework.abilities.data.DataRepository.StudyModelGetter.Companion.TYPE_STUDY_ALBUM

/**
 * 学习图集的数据模型
 *
 * @param path 路径对象
 * @param isPositiveOrder 是否正序
 */
class StudyAlbumModel(path: Path, isPositiveOrder: Boolean) : AlbumModel(TYPE_STUDY_ALBUM, path, isPositiveOrder) {

    override fun getTag(): String {
        return TAG
    }

    companion object {
        private const val TAG = "StudyAlbumModel"
    }
}