/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvTransition
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/5/6 10:21
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/5/6		  1.0		 AvTransition
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.effect

/**
 * 转场特效
 * TODO(Marked by wanglian 目前转场设计还不明确)
 */
sealed class AvTransition : AvEffect() {
    /**
     * 在两段片段转换过程中，一段逐渐变暗，直到完全黑色，然后另一段便开始逐渐变亮。常用于表达时间或空间的过渡。
     */
    object FadeTransition : AvTransition() { override val name = FADE_IN_OUT }

    /**
     * 一种片段慢慢缩小或者放大，过渡到下一种片段。
     */
    object ZoomInOutTransition : AvTransition() { override val name = ZOOM_IN_OUT }

    companion object {
        private const val FADE_IN_OUT = "FadeInOutEffect"
        private const val ZOOM_IN_OUT = "ZoomInOutEffect"
    }
}