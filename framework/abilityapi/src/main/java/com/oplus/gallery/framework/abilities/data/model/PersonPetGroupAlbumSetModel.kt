/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PersonPetGroupAlbumSetModel.kt
 ** Description : 人宠群组图集列表数据模型
 ** Version     : 1.0
 ** Date        : 2025/04/08
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/04/08  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.data.model

import android.content.Context
import android.util.Pair
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.PersonPet.PATH_SET_PERSON_PET_GROUP_ANY
import com.oplus.gallery.business_lib.model.data.personpetgroup.set.PersonPetGroupAlbumSet
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonPetModelGetter.Companion.TYPE_PERSON_PET_GROUP_ALBUM_SET

/**
 * 人宠群组图集列表数据模型
 */
class PersonPetGroupAlbumSetModel(isPositiveOrder: Boolean) : AlbumSetModel(
    PATH_SET_PERSON_PET_GROUP_ANY,
    TYPE_PERSON_PET_GROUP_ALBUM_SET,
    isPositiveOrder
) {

    /**
     * 批量去加载人物与宠物图集列表封面Item，从start开始，加载count项,
     * @param start 起始位置，闭区间
     * @param count 从start开始，加载count项
     */
    override fun prepareCoverItems(start: Int, count: Int) {
        (mediaSet as? PersonPetGroupAlbumSet)?.prepareCoverItems(start, count)
    }

    fun createPersonPetGroup(context: Context, paths: List<Path>): Pair<Int, Int>? {
        return (mediaSet as? PersonPetGroupAlbumSet)?.createPersonPetGroup(context, paths)
    }

    fun disbandPersonPet(context: Context, path: Path): Int {
        return (mediaSet as? PersonPetGroupAlbumSet)?.disbandPersonPet(context, path) ?: 0
    }

    /**
     * 获取过滤空合照集之后的图集集合
     */
    fun getFilterEmptyGroupItems(start: Int, count: Int): List<MediaSet> =
        (mediaSet as? PersonPetGroupAlbumSet)?.getFilterEmptyGroupSubMediaSet(start, count) ?: emptyList()

    companion object {
        private const val TAG = "PersonPetGroupAlbumSetModel"
    }
}