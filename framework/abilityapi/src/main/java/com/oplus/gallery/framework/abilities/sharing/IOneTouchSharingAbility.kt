/**************************************************************************************************
 * Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ISenseSharingAbility.kt
 * Description: 一碰分享/靠近感应分享能力接口
 * Version: 1.0
 * Date: 2024/2/22
 * Author: HuiTang@Apps.Gallery3D
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * HuiTang@Apps.Gallery3D      2024/2/22      1.0           create file
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.sharing

import com.oplus.gallery.framework.abilities.sharing.nfc.IEventCallback

/**
 * 一碰分享能力由 OAF NFC 和 OShare，其中 OAF NFC 负责设备之间的连接，OShare 负责发送数据，
 * 基本逻辑就是在 OAF NFC 完成一些列设备相关操作满足文件传输条件时，使用 OShare 发送数据即可。
 *
 * 【OAF NFC】: NFC 初始化、事件注册等，即封装 com.heytap.accessory.nfc.sdk.NFCManager
 *  SDK文档：https://noah.myoas.com/micro-app/sdk/?id=119266166527626134&sdkSource=6&sourceType=details
 *  接口文档：https://odocs.myoas.com/docs/KlkKVe5dDvSQxVqd
 *  方案设计文档：https://odocs.myoas.com/docs/m4kMLxremzT1bxqD?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle
 *  =1&lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
 */
interface IOneTouchSharingAbility : AutoCloseable {
    /**
     * 注册 NFC 分享业务事件回调,以便在特定的 NFC 动作发生时接收通知
     *
     * @Note：
     * 1. 分享业务，即 NFCAction 为 SHARE ,仅分享图片时，建议使用SHARE_TYPE_IMAGE，其他情况使用SHARE_TYPE_FILE，
     * （其实都可以使用SHARE_TYPE_FILE进行分享，之所以区分，主要是便于对端根据不同类型，展现不同的UI等），另外，当类型改变时，
     * 直接重新 register 即可，不需要先unRegister 再 register，比如如下场景：
     * 先注册了仅图片分享事件，但是在分享前又增加了视频等，则直接再次注册文件分享事件即可，不需要调用unRegisterEvent
     *
     * 2. 分享业务在 register 和 unregister 时，需要注意，NFC感应时，OAF 框架会弹出流体云弹窗，
     * 会导致页面先 onPause，然后 onResume ，因此需要注意二者调用时机
     *
     * @param callback NFC事件回调，用于处理 NFC 事件的回调接口
     */
    fun registerShareEvent(callback: IEventCallback?)

    /**
     * 注销 NFC 分享业务事件回调
     *
     * @param callback NFC事件回调，用于处理 NFC 事件的回调接口
     */
    fun unRegisterShareEvent(callback: IEventCallback?)

    /**
     * 取消注册 NFC 事件回调，停止接收 NFC 事件的通知
     *
     * @Note：
     * 按照SDK要求和方案设计文档，需要注意如下几点：
     * 1. 退出场景时才需要unRegister，重新注册的场景不需要调用；
     * 2. 退到后台时（onStop），需要unRegister，重新进入时，重新register；
     * 3. 选择分享的数据为0时，需要unRegister，避免影响一碰其他业务，如无数据选择分享时，一碰感应也可以触发投屏、镜像等互联业务；
     */
    fun unRegisterEvent()

    /**
     * 反注销所有 NFC 分享业务事件
     *
     * @Note 此方法清空所有回调并注销NFC事件, 仅供如【BugID：7730570】中的特殊场景调用：跳转互传，导致分享页和编辑模式同时退出的场景
     */
    fun unRegisterAllShareEvent()
}