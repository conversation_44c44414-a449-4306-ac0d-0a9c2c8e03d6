/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ITransformChannel
 ** Description: 转码通道，可派生出HDR转码通道、HEIF转码通道。。。
 **
 ** Version: 1.0
 ** Date: 2022/10/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  Yeguangjin@Apps.Gallery3D  2022/10/26  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.transform.channel

import com.oplus.gallery.framework.abilities.transform.TransformCallback
import com.oplus.gallery.framework.abilities.transform.TransformLimit
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.framework.abilities.transform.data.BaseTransformInfo

interface ITransformChannel {

    /**
     * 获取当前转码通道的样式
     * @return 返回当前转码样式，详见 TransformType
     */
    fun getType(): TransformType

    /**
     * 是否支持转码到指定包名的应用
     * 由白名单控制，而非能力不支持
     * @param targetPackageName 目标包名
     * @return 返回是否支持
     */
    fun isSupport(targetPackageName: String): Boolean

    /**
     * 设置转码通用的配置项
     * @param config 相关配置项
     */
    fun setConfigurations(config: MutableMap<String, Any>)

    /**
     * 设置要转码的文件相关信息，支持对多个进行转码
     * @param infoList 要转码的文件相关信息
     */
    fun setDataSource(infoList: MutableList<BaseTransformInfo>)

    /**
     * 增加转码条件限制
     * @param limit 转码限制
     */
    fun setLimit(limit: TransformLimit)

    /**
     * 清理转码缓存
     * @return 表示是否有执行缓存清理
     */
    fun cleanCache(): Boolean

    /**
     * 开始转码，有多个的情况下，从列表第一个开始
     * @param callback 转码回调 详见TransformCallback
     */
    fun start(callback: TransformCallback?)

    /**
     * 关闭转码，会中断转码流程
     */
    fun cancel()

    /**
     * 销毁转码相关资源
     * @return 是否销毁成功
     */
    fun release()
}