/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveVideoSaveListener.kt
 ** Description: 导出实况照片功能逻辑实现类
 ** Version: 1.0
 ** Date: 2025/2/12
 ** Author: zhouzihao
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhouzihao                       2025/2/12       1.0       created
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.videoedit

/**
 * 监听自定义编译任务状态的回调接口
 */
interface OnCustomCompileTaskStatusListener {
    /**
     * 保存完成
     */
    fun onVideoSaveCompleted(isCanceled: Boolean)
    /**
     * 保存失败
     */
    fun onVideoSaveFail()
    /**
     * 保存进度，用于更新process dialog
     * @param process: 保存进度
     */
    fun onVideoSaveProcess(process: Int)
}