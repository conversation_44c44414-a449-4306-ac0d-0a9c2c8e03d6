/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ISettingsAbility.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/7/27
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2022/7/27      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.config

import android.content.Context
import androidx.annotation.WorkerThread
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_REFUSE_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_REFUSE_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_REFUSE_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_REFUSE_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_REFUSE_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_REFUSE_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REALME_EDIT_PIC_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REALME_EDIT_PIC_REFUSE_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REALME_EDIT_PIC_REFUSE_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_REFUSE_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_REFUSE_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.ELIMINATE_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.ELIMINATE_REFUSE_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.ELIMINATE_REFUSE_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IMAGE_QUALITY_ENHANCE_BANNED_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IMAGE_QUALITY_ENHANCE_REFUSE_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IMAGE_QUALITY_ENHANCE_REFUSE_TIME

/**
 * 业务组件层不能获得写权限，但是设置页面因为其特殊性需要写配置。
 * 此能力提供的接口用于提供设置页的设置读写能力。
 */
interface ISettingsAbility : AutoCloseable {
    /**
     * olive 封面编辑 proxdr效果模型本地版本
     *
     * @param localComponentVersion
     */
    fun setOliveCoverProXDRLocalVersion(localComponentVersion: Int)

    /**
     * olive 封面编辑 proxdr效果模型安转权限设置
     *
     * @param isMobileNetwork 是否为移动网络
     * @param isAgree 是否同意
     */
    fun setOliveCoverProXDRInstallationPermissions(isMobileNetwork: Boolean, isAgree: Boolean)

    /**
     * olive proXDR 参见保存path
     * @param saveFilePath
     */
    fun setOliveCoverProXDRComponentSavePath(saveFilePath: String)

    /**
     * olive xdr插件是否可以后台下载
     */
    fun setOliveCoverProXDRComponentAllowBackgroundDownload(isAllow: Boolean)

    /**
     * 美颜模型本地版本
     *
     * @param localComponentVersion
     */
    fun setBeautyLocalVersion(localComponentVersion: Int)

    /**
     * 记录 消除笔模型的  版本和忽略次数，格式: 10_1 ， 11_3
     *
     * @param ignoreVersionCount 忽略版本和次数组合 eg：10_1，11_5
     */
    fun setEliminateIgnoreVersionCount(ignoreVersionCount: String)

    /**
     * 记录 美颜模型的  提示更新的时间
     */
    fun markBeautyUpgradeTime(time: Long)

    /**
     * 消除笔模型本地版本
     *
     * @param localComponentVersion
     */
    fun setEliminateLocalVersion(localComponentVersion: Int)

    /**
     * 消除笔模型保存路径
     *
     * @param saveFilePath
     */
    fun setEliminateComponentSavePath(saveFilePath: String)

    /**
     * Local Hdr用户偏好设置开关
     *
     * @param enabled true开启，false关闭
     */
    fun setHdrImageSwitchEnable(enabled: Boolean)

    /**
     * 提亮效果对比 用户偏好设置开关。如Local HDR效果对比。
     * @param enabled Boolean
     */
    fun setBrightenCompareSwitchEnable(enabled: Boolean)

    /**
     * 当前Local HDR图片不支持编辑，在Local HDR进入到编辑页时，需要弹窗提示用户。
     * 弹窗有个选项，不再提醒。当用户选择不再提醒时，会调用该方法。
     *
     * @param enabled true点击，false未点击
     */
    fun setNoMoreRemindWhenEditHdrImageImage(enabled: Boolean)

    /**
     * 当前Local HDR图片支持编辑，本期仅支持裁剪旋转、调节、滤镜，点击其他不支持小项时，需要弹窗提示用户。
     * 弹窗有个选项，不再提醒。当用户选择不再提醒时，会调用该方法。
     *
     * @param enabled true点击，false未点击
     */
    fun setNoMoreRemindInPhotoEdit(enabled: Boolean)

    /**
     * 实况消失的弹窗
     * 弹窗有个选项，不再提醒。当用户选择不再提醒时，会调用该方法。
     *
     */
    fun markNoMoreRemindLoseOliveAndHdrDialog()

    /**
     * 实况和ProXDR同时消失的弹窗
     * 弹窗有个选项，不再提醒。当用户选择不再提醒时，会调用该方法。
     *
     */
    fun markNoMoreRemindLoseOliveDialog()

    /**
     * 实况照片在编辑一级页面点击不支持的编辑项时，同时丢失实况和proxdr效果的弹窗
     * 弹窗有个选项，不再提醒。当用户选择不再提醒时，会调用该方法。
     *
     */
    fun markNoMoreRemindLoseOliveAndHdrDialogEnterSubEdit()

    /**
     * 实况照片在编辑一级页面点击不支持的编辑项时丢失实况效果的弹窗
     * 弹窗有个选项，不再提醒。当用户选择不再提醒时，会调用该方法。
     *
     */
    fun markNoMoreRemindLoseOliveDialogEnterSubEdit()

    /**
     * 标记当前是否使用过智能消除，用于下架弹窗
     */
    fun setIsAIEliminateUsed(isUsed: Boolean)

    /**
     * 设置智能消除是否已经下架，用于下架弹窗
     */
    fun setIsAIEliminateOffline(isOffline: Boolean)

    /**
     * 设置智能消除当前的能力状态，包含是否下线、无网络、低内存等状态
     * @param state 能力状态
     */
    fun setAIEliminateDetectState(state: Int)

    /**
     * 设置AI闭眼修复当前的能力状态，包含是否下线、无网络、低内存等状态
     * @param state 能力状态
     */
    fun setAIGroupPhotoDetectState(state: Int)

    /**
     * 设置智能消除路人消除是否支持
     * @param isSupportPassersEliminate 是否支持路人消除
     */
    fun setIsSupportPassersEliminate(isSupportPassersEliminate: Boolean)

    /**
     * 设置智能消除路人消除模型是否需要下载
     * @param needDownload 是否需要下载
     */
    fun setIsNeedDownloadAIEliminate(needDownload: Boolean)

    /**
     * 设置AI最佳合影模型是否需要下载
     * @param needDownload 是否需要下载
     */
    fun setIsNeedDownloadAIGroupPhoto(needDownload: Boolean)

    /**
     * 设置智能消除路人消除模型是否需要更新
     * @param needUpdate 是否需要更新
     */
    fun setIsNeedUpdatePassersEliminate(needUpdate: Boolean)

    /**
     * 设置智能消除路人消除模型更新弹窗显示的时间
     * @param timeStamp 当前时间
     */
    fun setUpdatePassersEliminateShowTimestamp(timeStamp: Long)

    /**
     * 更新耗时的配置，比如AIUnit相关的配置，涉及跨进程调用，必须在子线程中调用
     * @param context 上下文
     * @param configIds 需要写入的配置项，此配置项需要在BlockingConfigWriter配置表内置有
     */
    @WorkerThread
    fun updateBlockingConfig(context: Context, vararg configIds: String)

    /**
     * 设置AIUnit插件更新的时间
     * @param configId 配置的ID
     * @param timeStamp 当前时间
     */
    fun setAIUnitModelUpdateTimestamp(configId: String, timeStamp: Long)

    /**
     * 设置画质增强更新弹窗显示的时间
     * @param timeStamp 当前时间
     */
    fun setUpdateImageQualityEnhanceShowTimestamp(timeStamp: Long)

    /**
     * 设置是否允许自动播放视频
     * @param enabled true 允许   false 不允许
     */
    fun setAutoPlayVideoEnable(enabled: Boolean)

    /**
     * 标记合影优化已经使用过了
     * 用于大图页面 合影图片 是否自动弹tips
     */
    fun markGroupPhotoUsed()

    /**
     * 标记Ai超清已经使用过了
     * 用于大图页面 Ai超清 是否自动弹tips
     */
    fun markAihdUsed()

    /**
     * 标记Ai涂鸦已经使用过了
     * 用于大图页面 Ai涂鸦 是否展示小红点
     */
    fun markAiGraffitiUsed()

    /**
     * 标记合影优化是否已经介绍过了
     * 用于编辑页面 合影图片 是否自动介绍弹框
     */
    fun markGroupPhotoIntroduced()

    /**
     * 标记AI超清是否已经介绍过了
     * 用于编辑页面 AI超清 是否自动介绍弹框
     */
    fun markAihdIntroduced()

    /**
     * 当前合影的实况图片保存后会丢失实况特性，在保存实况合影图时，需要弹窗提示用户。
     * 弹窗有个选项，不再提醒。当用户选择不再提醒时，会调用该方法。
     */
    fun markNoMoreRemindWhenSaveOliveGroupPhoto()

    /**
     * 隐藏图集开关设置
     *
     * @param enabled true开启，false关闭
     */
    fun setSafeBoxAlbumShowEnable(enabled: Boolean)

    /**
     * 智能编辑推荐设置
     *
     * @param enabled true开启，false关闭
     */
    fun setIntelliFuncEnable(enabled: Boolean)

    /**
     * 标记“智能编辑推荐”上报过埋点
     * OS 16.0可删除
     */
    fun markIntelliFuncTracked()

    /**
     * 设置插件的大小
     * @param pluginConfigId 插件的配置ID
     * @param size 插件大小，单位byte
     */
    fun setPluginSize(pluginConfigId: String, size: Long)

    /**
     * 设置缺陷识别模型更新弹窗显示的时间
     * @param timeStamp 当前时间
     */
    fun setUpdateIntelliFuncShowTimestamp(timeStamp: Long)

    /**
     * 标记限定水印已经使用过
     */
    fun setRestrictWatermarkTagEnable(enabled: Boolean)

    /**
     * 标记色彩水印已经使用过了
     */
    fun markColorWatermarkUsed()

    /**
     * 标记边框编辑已经使用过了
     */
    fun markBorderUsed()

    /**
     * 标记Local HDR对比功能已经使用过了。
     */
    fun markHdrImageCompareUsed()

    /**
     * 设置自动打码是否支持
     * @param support 是否支持
     */
    fun setIsSupportAutoMosaic(support: Boolean)

    /**
     * 设置自动打码模型是否需要下载
     * @param needDownload 是否需要下载
     */
    fun setIsNeedDownloadAutoMosaicModel(needDownload: Boolean)

    /**
     * 设置自动打码模型是否需要更新
     * @param needUpdate 是否需要更新
     */
    fun setIsNeedUpdateAutoMosaicModel(needUpdate: Boolean)

    /**
     * 设置自动打码模型更新弹窗显示的时间
     * @param timeStamp 当前时间
     */
    fun setUpdateAutoMosaicModelShowTimestamp(timeStamp: Long)

    /**
     * 设置水印编辑上一次输入并保存的自定义信息
     */
    fun setWatermarkCustomInfo(customInfo: String?)

    /**
     * 设置水印编辑上一次输入并保存的自定义信息(隐私水印)
     */
    fun setPrivacyWatermarkCustomInfo(customInfo: String?)

    /**
     * 设置图片编辑后的保存方式
     * @param status 保存方式
     */
    fun setImageEditorSaveStatus(status: Int)

    /**
     * 设置里面旅程定位权限开关状态
     * @param status 开关状态
     */
    fun setTravelLocateStatus(status: Int)

    /**
     * 设置照片视图类型
     *
     * @param type 视图类型（沉浸视图/日期视图）
     */
    fun setPhotoPresentType(type: Int)

    /**
     * 标记不再显示照片视图设置引导弹窗
     */
    fun markNoMoreShowPhotoPresentTypeGuideDialog()

    /**
     * 标记智能消除功能介绍已经使用过了
     */
    fun markAIEliminateIntroductionUsed()

    /**
     * 标记路人消除功能介绍已经使用过了
     */
    fun markPasserbyEliminateIntroductionUsed()

    /**
     * 标记路人消除的“全部清除”功能已被使用过了
     */
    fun markEliminateAllPasserbyUsed()

    /**
     * 标记路人消除功能的点选引导已经使用过了
     */
    fun markPasserbyEliminateTipsUsed()

    /**
     * 标记AI 最佳表情已经使用过
     */
    fun markAIBestTakeUsed()

    /**
     * 记忆消除拒识次数
     * @param 拒识的次数
     */
    fun markEliminateRefuseCount(count: Int)

    /**
     * 记忆当前拒识的时间
     */
    fun markEliminateRefuseTime(time: Long)

    /**
     * 记忆消除封禁时间
     */
    fun markEliminateBannedTime(time: Long)

    /**
     * 标记智能功能的引导已经使用过了
     * @param configId 配置ID
     */
    fun markAIFuncIntroductionUsed(configId: String)

    /**
     * 标记智能编辑敏感图禁用状态
     */
    fun markAIFuncBanTime(item: SensitiveFuncBanItem)

    /**
     * 写入AI最佳合影-闭眼修复支持性的配置,耗时动作
     */
    fun writeAIGroupPhotoSupportConfig(context: Context)

    /**
     * 设置用户在AI涂鸦的使用次数
     * @param photoUsed 从后端获取的照片生成次数
     */
    fun setAIGraffitiUsedTime(photoUsed: String)

    /**
     * 设置用户在保存时选择的分辨率类型
     * @param definitionType 分辨率类型值
     */
    fun setGifLastChosenSaveDefinitionType(definitionType: String)

    /**
     * 设置当前时区
     * @param timeZone 当前时区
     */
    fun setTimeZone(timeZone: String)

    /**
     * 存储 系统 externalCacheDir（目的：冷启动是 获取比较耗时，所以存起来，加快冷启动）
     * @param dir 系统 externalCacheDir
     */
    fun setExternalCacheDir(dir: String)

    /**
     * 设置ocs授权成功的系统时间
     *
     * ocs授权比较耗时，且会拉起ocs服务，故sf会针对包名进行授权缓存，系统重启后失效，故判定授权时间在重启之前，则需要重新请求授权建立缓存。
     *
     * @param timeMillis 授权时间
     */
    fun setOcsAuthTimeMillis(timeMillis: Long)

    /**
     * 贴纸功能中数据库版本低于3的本地贴纸文件是否需要被删除清空
     */
    fun setNeedDeleteStickerFileLowerThanVersion3(enabled: Boolean)


    /**
     * 标记AI增强功能介绍已经使用过了
     */
    fun markImageQualityEnhanceFirstUse(isFirstUse: Boolean)

    /**
     * 设置用户云空间可用大小
     */
    fun setCloudAvailableSpace(availableAmount: Int)

    /**
     * 设置用户储存空间展示
     */
    fun setCloudSpaceInfo(pageType: Long, usageStorage: Long, availableStorage: Long, allStorage: Long)

    /**
     * 设置云同步一体化是否可用
     */
    fun setCloudSyncGlobalEnabled(isEnabled: Boolean)

    /**
     * 设置是否是手动触发欢太云下载原图（弹窗中点击“下载原始图片”）
     */
    fun setHeyTapDownloadingOriginManually(isDownloadOrigin: Boolean)

    /**
     * 设置是否是手动触发欢太云下载原图（弹窗中点击“下载原始图片”）
     */
    fun setGoogleDownloadingOriginManually(isDownloadOrigin: Boolean)

    /**
     * 记录录上次显示云空间不足的时间
     * LAST_SHOW_CLOUD_SPACE_NOT_ENOUGH_TIPS_TIME
     */
    fun setLastShowCloudSpaceNotEnoughTipsTime(time: Long)

    /**
     * 云服务的本地数据版本号
     */
    fun setCloudDataVersion(version: Int)

    /**
     * 手动关闭瘦身置为true,这个标记只用于控制下载原图是否提示
     */
    fun setAllowShowOriginalDownloading(status: Boolean)

    /**
     * 记录上次上报云服务响应数据非法的时间
     * @param lastTimeMillisecond 上次上报时间
     */
    fun setLastTimeTrackSysVersionInvalid(lastTimeMillisecond: Long)

    /**
     * 记录上次上报本地gid非法的时间
     * @param lastTimeMillisecond 上次上报时间
     */
    fun setLastTimeTrackSysRecordIdInvalid(lastTimeMillisecond: Long)

    /**
     * 记录上次检查Camera目录同步开关
     * @param lastTimeMillisecond 上次上报时间
     */
    fun setLastTimeTrackCameraDirSwitchState(lastTimeMillisecond: Long)

    /**
     * 设置是否首次进入分享页
     * @param enable 是否首次进入分享页，true为非首次，false为首次
     */
    fun setSharePageAsFirstEnter(enable: Boolean)

    /**
     * 设置开启了分享后删除功能
     * @param enable 删除功能开启还是关闭的值。true为开启，false为关闭
     */
    fun setShareDeleteSelectedEnable(enable: Boolean)

    /**
     * 是否首次显示分享后删除tips弹窗气泡
     * @param enable 弹窗气泡是否是首次显示。true为非首次，false为首次
     */
    fun setShareDeleteTipsAsFirstShow(enable: Boolean)

    /** 设置已经显示过同步目录开启引导 */
    fun setSyncAlbumDirOpenGuideShow()

    /**
     * 最近删除图集-删除弹窗 是否第一次显示
     * @param enable 弹窗是否是首次显示。true为非首次，false为首次
     */
    fun setRecycledDeleteTipsAsFirstShow(enable: Boolean)

    /**
     * 【图集、大图页】删除弹窗-是否第一次显示（不包括最近删除图集）
     * @param enable 弹窗是否是首次显示。true为非首次，false为首次
     */
    fun setRecycleTipsAsFirstShow(enable: Boolean)

    /**
     * 是否首次更新data_recycle字段的值,
     * 给外销云场景，升级”最近删除60天新文案“版本使用
     * @param enable 是否首次更新data_recycle
     */
    fun setFirstUpdateRecycleTime(enable: Boolean)

    /**
     * 私密保险箱弹框使用提示
     * @param hide 为true时，下次不再显示
     */
    fun setSafeBoxNotRemindAgain(hide: Boolean)

    /**
     * 清理 Google 云同步锚点信息
     */
    fun clearGCloudSyncToken()

    /**
     * 设置 Google 云同步锚点信息，用于标记当前同步进度以实现增量返回
     */
    fun setGCloudSyncToken(token: String)

    /**
     * 记录谷歌备份引导面板点击【不要备份】或者返回关闭的时间
     */
    fun setGCloudConnectionPromoNegativeTime(time: Long)

    /**
     * 标记曾经获得过 Google 相册授权
     */
    fun markHadConnected()

    /**
     * 记录 Google Photos 登录账号发生了变化
     */
    fun setGAccountChange(changed: Boolean)

    /**
     * 记录 Google Photos 登录的账号
     */
    fun markGAccountName(accountName: String?)

    /**
     * 记录顶部提示语忽略时间，key对应不同的业务
     */
    fun markIgnoreTimes(key: String, value: String?)

    /**
     * 记录清理建议是否显示小红点
     */
    fun setPhotoCleanIsSupportShowRedDotLocal(complete: Boolean)

    /**
     * 授权访问媒体文件
     *
     * @param authorized true/false
     */
    fun authorizeAccessMedia(authorized: Boolean)

    /**
     * 授权使用Ai消除功能 ——大图-编辑-消除
     *
     * @param authorized true/false
     */
    fun authorizeAccessAiEliminate(authorized: Boolean)

    /**
     * 授权使用Ai超清功能 ——大图-编辑-AI 超清
     *
     * @param authorized true/false
     */
    fun authorizeAccessAihd(authorized: Boolean)

    /**
     * 使用Ai超清功能体验授权
     *
     * @param authorized true/false
     */
    fun useAihdAuthorizeAccess(authorized: Boolean)

    /**
     * 授权使用自动打码下载功能
     *
     * @param authorized true/false
     */
    fun authorizeAccessAutoMosaicDownload(authorized: Boolean)

    /**
     * 授权使用AI 最佳合影功能
     *
     * @param authorized true/false
     */
    fun authorizeAccessAIGroupPhoto(authorized: Boolean)

    /**
     * 授权外销隐私
     *
     * @param funcConfigId 需授权功能的configId
     */
    fun authorizeExportPrivacy(funcConfigId: String)

    /**
     * 同意用户协议-开屏弹窗点击
     *
     * @param isAgree true/false
     */
    fun agreeUserAgreement(isAgree: Boolean)

    /**
     * 记录用户是否开始了云同步（埋点使用）
     */
    fun setTrackSyncStart(isStart: Boolean)

    /**
     * 把运营位配置数据缓存到本地
     */
    fun setCloudConfigDataCache(json: String)

    /**
     * 把最近删除引导文案缓存到本地
     */
    fun setRecycleConfigDataCache(value: String)

    /**
     * 记录最近删除页面底部引导文案的查询时间
     */
    fun markQueryRecycleConfigTime()

    /**
     * 设置运营位配置数据的曝光状态
     */
    fun setCloudConfigDataExposed(value: Boolean)

    /**
     * 为开关分类的运营位配置数据，记录忽略时间点
     */
    fun markIgnoreTimeForSW()

    /**
     * 为付费分类的运营位配置数据，记录忽略时间点
     */
    fun markIgnoreTimeForPay()

    /**
     * 记录运营位配置点击操作的时间点
     */
    fun markCloudConfigOperationTime()

    /**
     * 记录已显示的运营位配置ID
     */
    fun setCloudConfigIdExposed(id: Int)

    /**
     * 更新智能编辑推荐次数
     * @param count 次数
     */
    fun updateIntelliFuncCount(count: Int)

    /**
     * 更新用户主动关闭引导页的时间
     * @param time 时间
     */
    fun updateCloseGuideTime(time: Long)

    /**
     * 语言发生变化时，更新语言状态
     *
     * @param language 当前语言
     */
    fun updateLanguageState(language: String)

    /**
     * 设置包名
     *
     * 删除保护，由于没有通知权限，无法通知，这时需要把应用名记录下来
     */
    fun setApplicationNameUnTipsForDeleteProtect(applicationName: String)

    /**
     * 增加请求通知权限的次数
     */
    fun increaseRequestNotificationPermissionCount()

    /**
     * 标记隐私密码小红点已经使用过了
     */
    fun markPrivacyPasswordRedDotFirstUse()

    /**
     * 标记隐私密码提示是否已经使用过
     */
    fun markPrivacyPasswordHeadFirstUse()

    /**
     * 记录CDN素材下载时间，【服务器成本优化：CDN拉平请求】，有gson转化操作，耗时，需要在子线程调用
     * @param metadataId CDN所在元数据ID
     * @param willDownloadTime 预计下载的时间格式化字符串
     */
    fun setCDNResourceWillDownloadTime(metadataId: String, willDownloadTime: String)

    /**
     * 获取录CDN素材下载时间，【服务器成本优化：CDN拉平请求】，有gson转化操作，耗时，需要在子线程调用
     * @param metadataId CDN所在元数据ID
     * @return 返回下载时间的格式化字符串
     */
    fun getCDNResourceWillDownloadTime(metadataId: String): String?

    /**
     * 记录搬家完成插入附表的时间点
     *
     * @param currentTimeMillisecond 插入附表的时间点
     */
    fun setInsertScheduleCurrentTime(currentTimeMillisecond: Long)

    /**
     * 记录Texture能支持的最大size
     * @param maxTextureSize Texture能支持的最大size
     */
    fun setMaxTextureSize(maxTextureSize: Int)

    /**
     * 记录相册相机通信参数协议版本号
     */
    fun setGalleryCameraConfigVersion(version: Int)

    override fun close() = Unit
}

/**
 * AI敏感图拒识惩罚功能枚举，目前区分AI消除、AI修复
 */
enum class SensitiveFuncBanItem(
    val refuseTimeConfigId: String,
    val refuseCountConfigId: String,
    val bannedTimeConfigId: String
) {
    AI_REPAIR(
        AI_REPAIR_REFUSE_TIME,
        AI_REPAIR_REFUSE_COUNT,
        AI_REPAIR_BANNED_TIME
    ),
    AI_ELIMINATE(
        ELIMINATE_REFUSE_TIME,
        ELIMINATE_REFUSE_COUNT,
        ELIMINATE_BANNED_TIME
    ),
    IMAGE_QUALITY_ENHANCE(
        IMAGE_QUALITY_ENHANCE_REFUSE_TIME,
        IMAGE_QUALITY_ENHANCE_REFUSE_COUNT,
        IMAGE_QUALITY_ENHANCE_BANNED_TIME
    ),
    AI_COMPOSITION(
        AI_COMPOSITION_REFUSE_TIME,
        AI_COMPOSITION_REFUSE_COUNT,
        AI_COMPOSITION_BANNED_TIME
    ),
    AI_BEST_TAKE(
        AI_BEST_TAKE_REFUSE_TIME,
        AI_BEST_TAKE_REFUSE_COUNT,
        AI_BEST_TAKE_BANNED_TIME
    ),
    AI_LIGHTING(
        AI_LIGHTING_REFUSE_TIME,
        AI_LIGHTING_REFUSE_COUNT,
        AI_LIGHTING_BANNED_TIME
    ),
    AI_REALME_EDIT_PIC(
        AI_REALME_EDIT_PIC_REFUSE_TIME,
        AI_REALME_EDIT_PIC_REFUSE_COUNT,
        AI_REALME_EDIT_PIC_BANNED_TIME
    )
}