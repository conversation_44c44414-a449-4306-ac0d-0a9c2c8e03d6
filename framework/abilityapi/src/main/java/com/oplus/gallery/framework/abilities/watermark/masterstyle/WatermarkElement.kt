/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WatermarkElement.kt
 ** Description:水印元素抽象
 ** Version: 1.0
 ** Date: 2024/8/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2024/8/16      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.watermark.masterstyle

import androidx.annotation.IntDef
import androidx.annotation.StringDef
import com.google.gson.JsonObject

// 水印元素类型
const val CONTENT_TYPE_TEXT = "text"
const val CONTENT_TYPE_SHAPE = "shape"
const val CONTENT_TYPE_SPACE = "space"
const val CONTENT_TYPE_IMAGE = "image"
const val CONTENT_TYPE_ELEMENTS = "elements"

@StringDef(value = [CONTENT_TYPE_TEXT, CONTENT_TYPE_SHAPE, CONTENT_TYPE_SPACE, CONTENT_TYPE_IMAGE, CONTENT_TYPE_ELEMENTS])
@Retention(AnnotationRetention.SOURCE)
annotation class ContentType

// 水印文本元素的内容源
const val TEXT_SOURCE_NONE = -1
const val TEXT_SOURCE_MODEL = 0
const val TEXT_SOURCE_IMAGE_PARAMS = 1
const val TEXT_SOURCE_TIME_LOCATION = 2
const val TEXT_SOURCE_TIME = 3
const val TEXT_SOURCE_LOCATION = 4
//原TEXT_SOURCE_AI_CAPTION = 5，改为小布福签功能
const val TEXT_SOURCE_GREETING = 5
const val TEXT_SOURCE_CUSTOM = 6
const val TEXT_SOURCE_FILTER = 7

@IntDef(
    value = [TEXT_SOURCE_NONE, TEXT_SOURCE_MODEL, TEXT_SOURCE_IMAGE_PARAMS, TEXT_SOURCE_LOCATION,
        TEXT_SOURCE_TIME, TEXT_SOURCE_TIME_LOCATION, TEXT_SOURCE_GREETING, TEXT_SOURCE_CUSTOM, TEXT_SOURCE_FILTER]
)
@Retention(AnnotationRetention.SOURCE)
annotation class TextSource

// 文本元素内容的Part
const val SOURCE_PART_NONE = "None"
const val SOURCE_PART_ISO_TITLE = "ISOTitle"
const val SOURCE_PART_ISO_VALUE = "ISOValue"
const val SOURCE_PART_MM_TITLE = "ISOTitle"
const val SOURCE_PART_MM_VALUE = "ISOValue"
const val SOURCE_PART_F_TITLE = "ISOTitle"
const val SOURCE_PART_F_VALUE = "ISOValue"
const val SOURCE_PART_S_TITLE = "ISOTitle"
const val SOURCE_PART_S_VALUE = "ISOValue"

@StringDef(
    value = [SOURCE_PART_NONE, SOURCE_PART_ISO_TITLE, SOURCE_PART_ISO_VALUE, SOURCE_PART_MM_TITLE, SOURCE_PART_MM_VALUE,
        SOURCE_PART_F_TITLE, SOURCE_PART_F_VALUE, SOURCE_PART_S_TITLE, SOURCE_PART_S_VALUE]
)
@Retention(AnnotationRetention.SOURCE)
annotation class TextSourcePart

// 图像元素的位图类型
const val BITMAP_TYPE_NONE = -1
const val BITMAP_TYPE_RES_NAME = 0
const val BITMAP_TYPE_FILEPATH = 1
const val BITMAP_TYPE_URL = 2
const val BITMAP_TYPE_PROVIDER = 3
const val BITMAP_TYPE_COLOR_CARD = 4

@IntDef(
    value = [BITMAP_TYPE_NONE, BITMAP_TYPE_RES_NAME, BITMAP_TYPE_FILEPATH, BITMAP_TYPE_URL, BITMAP_TYPE_PROVIDER, BITMAP_TYPE_COLOR_CARD]
)
@Retention(AnnotationRetention.SOURCE)
annotation class BitmapSourceType

// 元素处于父元素中的位置
const val LEFT_TOP = "leftTop"
const val RIGHT_TOP = "rightTop"
const val LEFT_BOTTOM = "leftBottom"
const val RIGHT_BOTTOM = "rightBottom"
const val LEFT_VERTICAL_CENTER = "leftVerticalCenter"
const val RIGHT_VERTICAL_CENTER = "rightVerticalCenter"
const val TOP_HORIZONTAL_CENTER = "topHorCenter"
const val BOTTOM_HORIZONTAL_CENTER = "bottomHorCenter"
const val HORIZONTAL_CENTER = "horCenter"
const val VERTICAL_CENTER = "verticalCenter"
const val CENTER = "center"

@StringDef(
    value = [LEFT_TOP, RIGHT_TOP, LEFT_BOTTOM, RIGHT_BOTTOM, LEFT_VERTICAL_CENTER,
        RIGHT_VERTICAL_CENTER, TOP_HORIZONTAL_CENTER, BOTTOM_HORIZONTAL_CENTER, HORIZONTAL_CENTER, VERTICAL_CENTER, CENTER]
)
@Retention(AnnotationRetention.SOURCE)
annotation class ElementLocation

// 字体风格
const val FONT_STYLE_NONE = -1
const val FONT_WEIGHT_THIN = 100
const val FONT_WEIGHT_EXTRA_LIGHT = 200
const val FONT_WEIGHT_LIGHT = 300
const val FONT_WEIGHT_NORMAL = 400
const val FONT_WEIGHT_MEDIUM = 500
const val FONT_WEIGHT_SEMI_BOLD = 600
const val FONT_WEIGHT_BOLD = 700
const val FONT_WEIGHT_EXTRA_BOLD = 800
const val FONT_WEIGHT_BLACK = 900

@IntDef(
    value = [FONT_STYLE_NONE, FONT_WEIGHT_THIN, FONT_WEIGHT_EXTRA_LIGHT, FONT_WEIGHT_LIGHT, FONT_WEIGHT_NORMAL, FONT_WEIGHT_MEDIUM,
        FONT_WEIGHT_SEMI_BOLD, FONT_WEIGHT_BOLD, FONT_WEIGHT_EXTRA_BOLD, FONT_WEIGHT_BLACK]
)
@Retention(AnnotationRetention.SOURCE)
annotation class FontStyle

// Font的表示类型, xx.ttf
const val FONT_TYPE_FILE_PATH = 0
const val FONT_TYPE_FILE_NAME = 1
const val FONT_TYPE_URL = 2

@IntDef(
    value = [FONT_TYPE_FILE_PATH, FONT_TYPE_FILE_NAME, FONT_TYPE_URL]
)
@Retention(AnnotationRetention.SOURCE)
annotation class FontType


const val FONT_FILE_TYPE_TTC = 0
const val FONT_FILE_TYPE_TTF = 1
const val FONT_FILE_TYPE_OTF = 2

@IntDef(
    value = [FONT_FILE_TYPE_TTC, FONT_FILE_TYPE_TTF, FONT_FILE_TYPE_OTF]
)
@Retention(AnnotationRetention.SOURCE)
annotation class FontFileType

// 渐变类型
const val GRADIENT_TYPE_NONE = -1
const val GRADIENT_TYPE_LINEAR = 0
const val GRADIENT_TYPE_RADIAL = 1
const val GRADIENT_TYPE_SWEEP = 2

@IntDef(
    value = [GRADIENT_TYPE_NONE, GRADIENT_TYPE_LINEAR, GRADIENT_TYPE_RADIAL, GRADIENT_TYPE_SWEEP]
)
@Retention(AnnotationRetention.SOURCE)
annotation class GradientType

// 形状
const val SHAPE_CIRCLE = "circle"
const val SHAPE_RECTANGLE = "rectangle"

@StringDef(
    value = [SHAPE_CIRCLE, SHAPE_RECTANGLE]
)
@Retention(AnnotationRetention.SOURCE)
annotation class Shape


// image scale type
const val SCALE_TYPE_CENTER = 0
const val SCALE_TYPE_CENTER_CROP = 1
const val SCALE_TYPE_CENTER_INSIDE = 2
const val SCALE_TYPE_FIT_CENTER = 3
const val SCALE_TYPE_FIT_START = 4
const val SCALE_TYPE_FIT_END = 5

@IntDef(
    value = [SCALE_TYPE_CENTER, SCALE_TYPE_CENTER_CROP, SCALE_TYPE_CENTER_INSIDE, SCALE_TYPE_FIT_CENTER,
        SCALE_TYPE_FIT_START, SCALE_TYPE_FIT_END]
)
@Retention(AnnotationRetention.SOURCE)
annotation class ScaleType

/**
 * 左对齐可以无需设置，按照水平布局或垂直布局的原始方向进行即可
 */
const val SPACE_USE_OCCUPY = "occupy"
const val SPACE_USE_BOTTOM_ALIGN = "bottomAlign"
const val SPACE_USE_TOP_ALIGN = "topAlign"
const val SPACE_USE_RIGHT_ALIGN = "rightAlign"
const val SPACE_USE_LEFT_ALIGN = "leftAlign"

@StringDef(
    value = [SPACE_USE_OCCUPY, SPACE_USE_BOTTOM_ALIGN, SPACE_USE_TOP_ALIGN, SPACE_USE_LEFT_ALIGN, SPACE_USE_RIGHT_ALIGN]
)
@Retention(AnnotationRetention.SOURCE)
annotation class SpaceUseAlign

/**
 * 水印元素
 */
class WatermarkElement {

    var id: Int = 0
    var groupId: Int = 0
    var visible: Boolean = true
    var editable: Boolean = false
    ///没有配置该属性（兼容老项目），或该属性配置为0时，默认创建线性布局；配置为1时，表示帧布局
    @ElementsLayout
    var innerLayout: Int? = null

    /**
     * 标记space元素的用途
     */
    @SpaceUseAlign
    var spaceUse: String = SPACE_USE_OCCUPY

    /**
     * 元素的layoutGravity是否可变, 目前仅支持overlay水印去设置该属性
     * 当设置overlay的对齐方式时，需要调整overlay水印中所有元素的对齐方式，此时需要修改layoutGravity, 这是应该标记元素varyGravity为true
     */
    var varyGravity: Boolean? = null

    /**
     * 亲近的元素ID。
     * 处理多个元素的关联显示或隐藏
     */
    var affinityId: Int? = null

    /**
     * 元素可能的下一个元素的ID列表, 需要用时才需要配置。通常给特定的shape配置
     * 元素需要根据可能存在的下个元素来决策关联显示时是否真的要显示
     */
    var possibleNextIds: List<Int>? = null

    /**
     * 元素可能的上一个元素的ID列表, 需要用时才需要配置。通常给特定的shape配置
     * 元素需要根据可能存在的上个元素来决策关联显示时是否真的要显示
     */
    var possiblePreviousIds: List<Int>? = null

    /**
     * 当一个元素被隐藏，则其指定的replaceId需要替换它显示
     */
    var replaceId: Int? = null
    var content: ElementContent? = null
    var position: ElementPosition? = null
    var paint: ElementPaint? = null
    private var layoutWeight: Int? = null
    var elements: List<WatermarkElement>? = null

    /**
     * 是否需要对容器内的所有元素当做一组来处理：主要就是显示和隐藏。
     * 仅支持对elements容器设置该属性。
     */
    var asGroup: Boolean? = null

    fun isAsGroupElements(): Boolean {
        return isElements() && (asGroup == true)
    }

    fun isElements(): Boolean {
        return content?.type == CONTENT_TYPE_ELEMENTS
    }

    fun isText(): Boolean {
        return content?.type == CONTENT_TYPE_TEXT
    }

    fun isShape(): Boolean {
        return content?.type == CONTENT_TYPE_SHAPE
    }

    fun isRectangleShape(): Boolean {
        return content?.type == CONTENT_TYPE_SHAPE && (content?.shape == SHAPE_RECTANGLE)
    }

    fun isCircleShape(): Boolean {
        return content?.type == CONTENT_TYPE_SHAPE && (content?.shape == SHAPE_CIRCLE)
    }

    fun isImage(): Boolean {
        return content?.type == CONTENT_TYPE_IMAGE
    }

    override fun toString(): String {
        return "WatermarkElement(id=$id, groupId=$groupId, visible=$visible, editable=$editable, varyGravity=$varyGravity, " +
            "affinityId=$affinityId, possibleNextIds=$possibleNextIds, possiblePreviousIds=$possiblePreviousIds, " +
            "replaceId=$replaceId, content=$content, position=$position, paint=$paint, layoutWeight=$layoutWeight, " +
            "elements=$elements, asGroup=$asGroup, spaceUse=$spaceUse, innerLayout=$innerLayout)"
    }

    inner class ElementContent {
        @ContentType
        var type: String = CONTENT_TYPE_TEXT

        @TextSource
        var textSource: Int? = null

        @TextSourcePart
        var textSourcePart: String? = null

        @ElementsOrientation
        var orientation: Int? = null

        var isExtendModel: Boolean? = false

        var modelSuffix: String? = null

        @Shape
        var shape: String? = null
        var diameter: Float? = null
        var width: Float? = null
        var height: Float? = null

        @ScaleType
        private var scaleType: Int? = null

        /**
         * 固定文本内容
         */
        var content: String? = null

        /**
         * ai文本的内容，textSource是TEXT_SOURCE_GREETING才需写入
         * TEXT_SOURCE_AI_CAPTION 改为 TEXT_SOURCE_GREETING
         */
        var aiContent: String? = null

        /**
         * 祝福语默认索引
         */
        var aiContentIndex: Int = 0

        /**
         * 文本内容最大宽度
         */
        private var maxWidth: Float? = null

        @BitmapSourceType
        var bitmapSourceType: Int? = null

        var bitmap: String? = null
        var bitmapResName: String? = null
        var md5: String? = null
        var colorCard: ContentColorCard? = null

        override fun toString(): String {
            return "ElementContent(type='$type', textSource=$textSource, textSourcePart=$textSourcePart, orientation=$orientation, " +
                "shape=$shape, diameter=$diameter, width=$width, height=$height, scaleType=$scaleType, content=$content, " +
                "aiContent=$aiContent, aiContentIndex=$aiContentIndex, maxWidth=$maxWidth, bitmapSourceType=$bitmapSourceType, md5=$md5, " +
                "bitmap=$bitmap, bitmapResName=$bitmapResName, colorCard=$colorCard)"
        }
    }

    class ContentColorCard {
        var colorX: Int? = null
        var colorY: Int? = null
        var colorDistance: Int? = null
        var colorCount: Int? = null
        var colorRadius: Int? = null
        var colorBlurScope: Int? = null

        @ElementsOrientation
        var colorType: Int = 0

        override fun toString(): String {
            return "ContentColorCard(colorX='$colorX', colorY=$colorY, colorDistance=$colorDistance, orientation=$colorType, " +
                    "colorCount=$colorCount, colorRadius=$colorRadius, colorBlurScope=$colorBlurScope)"
        }
    }

    inner class ElementPosition {
        @ElementLocation
        var layoutGravity: String? = null
        var layoutGravityEnable: Boolean = true

        @ElementLocation
        private var gravity: String? = null
        var leftMargin: Float = 0f
        var topMargin: Float = 0f
        var rightMargin: Float = 0f
        var bottomMargin: Float = 0f
        // 仅针对特殊的样式中的机型元素才使用
        var withLogoBottomMargin: Float? = null
        var noLogoBottomMargin: Float? = null

        override fun toString(): String {
            return "ElementPosition(layoutGravity=$layoutGravity, layoutGravityEnable=$layoutGravityEnable, gravity=$gravity," +
                    " leftMargin=$leftMargin, topMargin=$topMargin, rightMargin=$rightMargin, bottomMargin=$bottomMargin, " +
                    "withLogoBottomMargin=$withLogoBottomMargin, noLogoBottomMargin=$noLogoBottomMargin)"
        }
    }

    inner class ElementPaint {

        @FontType
        var fontType: Int = 0
        var font: String? = null
        var fontName: String? = null
        var md5: String? = null

        @FontFileType
        var fontFileType: Int = FONT_FILE_TYPE_TTF
        var ttcIndex: Int = -1
        var fontWeight: Int = 0
        var textSize: Float = 0f
        // 机型名称限定长度
        var largeModelLength: Int? = 0
        //机型名称超过限定长度才使用
        var largeModelTextSize: Float? = 0f
        // 长文本的判定阈值
        var longTextLengthThreshold: Int? = 0
        //长文本内容的textsize
        var longContentTextSize: Float? = 0f
        //默认文本的textSize
        var defaultTextSize: Float? = null
        // 仅针对特殊的样式中的机型元素才使用
        var withLogoTextSize: Float? = null
        var noLogoTextSize: Float? = null
        var alpha: Float = 1f
        var lightAlpha: Float? = null
        var darkAlpha: Float? = null
        var letterSpacing: Float = 0f
        var lineHeight: Float = 0f

        @GradientType
        var gradientType = GRADIENT_TYPE_NONE
        var colors: List<String> = mutableListOf()
        // 普通文字颜色，用于非P3色域的图片
        var colorsNormal: List<String>? = mutableListOf()
        // p3色域的文字颜色，用于P3色域的图片
        var colorsP3: List<String>? = mutableListOf()

        // 文字主题色配置，用于跟随背景切换
        var themeColors: JsonObject? = null

        var lightColor: String? = null
        var darkColor: String? = null
        var colorPositions: List<Float>? = null
        var coordinateArray: List<Float>? = null

        // 阴影配置
        var withShadow: Boolean = false

        // 如果withShadow是true，需要配置shadowValues，内容为数组，[shadowRadius, x, y]
        var shadowValues: List<Double>? = null
        var shadowColor: String? = null
        override fun toString(): String {
            return "ElementPaint(fontType=$fontType, font=$font, fontName=$fontName, md5=$md5, fontFileType=$fontFileType, " +
                    "ttcIndex=$ttcIndex, fontWeight=$fontWeight, textSize=$textSize, alpha=$alpha, letterSpacing=$letterSpacing, " +
                    "lineHeight=$lineHeight, gradientType=$gradientType, colors=$colors, colorsP3=$colorsP3, colorPositions=$colorPositions, " +
                    "coordinateArray=$coordinateArray, withShadow=$withShadow, shadowValues=$shadowValues, shadowColor=$shadowColor, " +
                    "largeModelTextSize=$largeModelTextSize, largeModelLength=$largeModelLength)"
        }
    }
}