/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : HEIFFileMimeTypeDetectTask.kt
 ** Description : 文件格式检测Task
 ** Version     : 1.0
 ** Date        : 2020/08/20
 ** Author      : Jin<PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Jin<PERSON><PERSON>@Apps.Gallery3D             2020/08/20   1.0         build this module
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.transform.heif;

import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.standard_lib.codec.DecodeUtils;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.JobContext;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;

import java.util.ArrayList;
import java.util.List;

public class HEIFFileMimeTypeDetectTask implements Job<List<Path>> {
    List<Path> mImagePaths;

    public HEIFFileMimeTypeDetectTask(List<Path> expandedPaths) {
        mImagePaths = expandedPaths;
    }

    @Override
    public List<Path> call(JobContext jc) {
        final ArrayList<Path> heifFilePaths = new ArrayList<>();
        if (mImagePaths != null) {
            for (Path path : mImagePaths) {
                if (MimeTypeUtils.isHeifOrHeic(getFileMimeType(path))) {
                    heifFilePaths.add(path);
                }
            }
        }
        return heifFilePaths;
    }

    private String getFileMimeType(Path path) {
        if (path == null) {
            return null;
        }
        String type = null;
        MediaItem item = (MediaItem) DataManager.getMediaObject(path);
        if (item != null) {
            type = item.getMimeType();
        }
        if (TextUtils.isEmpty(type)) {
            type = DecodeUtils.decodeBounds(getFilePath(path), null).outMimeType;
        }
        return type;
    }

    private String getFilePath(Path path) {
        String filePath = null;
        MediaItem item = (MediaItem) DataManager.getMediaObject(path);
        if (item != null) {
            filePath = item.getFilePath();
        }
        return filePath;
    }
}
