/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File		: - ${FILE_NAME}
 ** Description	: build this module
 ** Version		: 1.0
 ** Date		: 2019/6/26
 ** Author		: <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>     <data>     <version>  <desc>
 **  <EMAIL>   2019/6/26  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.framework.abilities.download.networkrequest;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ModelResponseData {

    @SerializedName("modelList")
    private List<ModelListBean> mModelList;

    public List<ModelListBean> getMusicModelList() {
        return mModelList;
    }

    public void setModelList(List<ModelListBean> modelList) {
        this.mModelList = modelList;
    }

    public static class ModelListBean {
        @SerializedName("modelId")
        private int mModelId;
        @SerializedName("modelName")
        private String mModelName;
        @SerializedName("modelVersion")
        private String mModelVersion;
        @SerializedName("sendAppVersion")
        private String mSendAppVersion;
        @SerializedName("zipPath")
        private String mZipPath;
        @SerializedName("updateTime")
        private String mUpdateTime;
        @SerializedName("zipMd5")
        private String mZipMd5;

        public String getZipMd5() {
            return mZipMd5;
        }

        public void setZipMd5(String zipMd5) {
            this.mZipMd5 = zipMd5;
        }

        public String getModelName() {
            return mModelName;
        }

        public void setModelName(String modelName) {
            this.mModelName = modelName;
        }

        public String getZipPath() {
            return mZipPath;
        }

        public void setZipPath(String zipPath) {
            this.mZipPath = zipPath;
        }

        public int getModelId() {
            return mModelId;
        }

        public void setModelId(int modelId) {
            this.mModelId = modelId;
        }

        public String getUpdateTime() {
            return mUpdateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.mUpdateTime = updateTime;
        }

        public String getModelVersion() {
            return mModelVersion;
        }

        public void setModelVersion(String mModelVersion) {
            this.mModelVersion = mModelVersion;
        }

        public String getSendAppVersion() {
            return mSendAppVersion;
        }

        public void setSendAppVersion(String mSendAppVersion) {
            this.mSendAppVersion = mSendAppVersion;
        }

        @Override
        public String toString() {
            return "ModelListBean{"
                    + "mModelName='" + mModelName + '\''
                    + ", mModelId=" + mModelId
                    + ", mModelVersion='" + mModelVersion + '\''
                    + ", mSendAppVersion='" + mSendAppVersion + '\''
                    + ", mUpdateTime='" + mUpdateTime + '\''
                    + ", mZipMd5='" + mZipMd5 + '\''
                    + '}';
        }
    }

    @Override
    public String toString() {
        return "ModelResponseData{"
                + "mModelList=" + mModelList
                + '}';
    }
}
