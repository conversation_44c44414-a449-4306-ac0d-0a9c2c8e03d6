/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IAvEditor
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/1/31 21:51
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/1/31		  1.0		 IAvEditor
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.editor

import android.graphics.Bitmap
import android.net.Uri
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.editing.asset.IAvAsset
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.effect.AvTransition
import com.oplus.gallery.framework.abilities.editing.misc.Timestamp
import com.oplus.gallery.framework.abilities.editing.track.IAvClip
import com.oplus.gallery.framework.abilities.editing.track.IAvTrack
import com.oplus.gallery.framework.abilities.editing.track.timeline.IAvTimeline
import com.oplus.gallery.framework.abilities.editing.track.timeline.PlaybackEvent
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsageScope
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvTransitionUsage
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvTransitionUsageScope

/**
 * 管线编辑器，编辑能力
 */
interface IAvEditor {
    /**
     * 轨道的时间线
     */
    val timeline: IAvTimeline

    /**
     * 添加资源
     * @param path 资源uri
     * @param filePath 资源文件路径
     * @param type 资源类型 [IAvAsset.AvAssetType]
     * @return 返回添加的资源本身
     */
    fun addAsset(path: Uri, filePath: String?, type: IAvAsset.AvAssetType): IAvAsset

    /**
     * 添加一个内容为[Bitmap]的资源，资源类型为 [IAvAsset.AvAssetType.BITMAP]
     * @param bitmap 资源内容，这里是Bitmap形式的任意内容
     */
    fun addAsset(bitmap: Bitmap): IAvAsset

    /**
     * 添加一个内容为[Texture]的资源，资源类型为 [IAvAsset.AvAssetType.TEXTURE]
     * @param textureId 资源内容，这里是纹理id
     */
    fun addAsset(textureId: Int): IAvAsset

    /**
     * 添加一个内容为[ByteArray]的资源，资源类型为 [IAvAsset.AvAssetType.BYTEARRAY]
     * @param bytes 资源内容，这里是字节流形式的任意内容
     */
    fun addAsset(bytes: ByteArray): IAvAsset

    /**
     * 查找资源
     * @param path 资源路径
     * @param type 资源类型 [IAvAsset.AvAssetType]
     * @return 返回查找的资源
     */
    fun findAsset(path: Uri, type: IAvAsset.AvAssetType): IAvAsset?

    /**
     * 删除资源
     * @param asset 待删除的资源
     */
    fun removeAsset(asset: IAvAsset)

    /**
     * 添加轨道
     * @param name 轨道的名称
     * @param type 轨道的类型 [IAvTrack.AvTrackType]
     * @return 返回当前添加的轨道
     */
    fun addTrack(name: String, type: IAvTrack.AvTrackType): IAvTrack

    /**
     * 查找轨道
     * @param name 轨道的名称
     * @return 返回查找到的轨道
     */
    fun findTrack(name: String): IAvTrack?

    /**
     * 删除轨道
     * @param track 待删除的轨道
     */
    fun removeTrack(track: IAvTrack)

    /**
     * 给轨道添加可编辑的资源[IAvClip]
     * @param track 待添加资源的轨道
     * @param fromAsset 添加的资源 [IAvAsset]
     * @param position 资源添加到轨道上的时间点 [Timestamp]
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @return 返回当前添加的可编辑资源对象
     */
    fun addClip(
        track: IAvTrack,
        fromAsset: IAvAsset,
        position: Timestamp = Timestamp.ZERO,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING
    ): IAvClip

    /**
     * 删除对应轨道上的某个资源
     * @param track 选择的需要删除资源的轨道
     * @param clip 轨道上待删除的资源
     * @param sinkDescriptionKey
     */
    fun removeClip(
        track: IAvTrack,
        clip: IAvClip,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING
    )

    /**
     * 为Clip添加特效
     * @param clip 对某个资源clip片段添加特效
     * @param effect 待添加的特效 [AvEffect]
     * @param effectUsageScope 特效设置参数域
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param render 是否触发一帧渲染
     * @return 返回当前添加的特效对象
     */
    fun addEffect(
        clip: IAvClip,
        effect: AvEffect,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true,
        effectUsageScope: IAvEffectUsageScope.() -> Unit
    ): IAvEffectUsage

    /**
     * 为track添加特效
     * @param track 对某个轨道track添加特效
     * @param effect 待添加的特效 [AvEffect]
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param render 是否触发一帧渲染
     * @param effectUsageScope 特效设置参数域
     * @return 返回当前添加的特效对象
     */
    fun addEffect(
        track: IAvTrack,
        effect: AvEffect,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true,
        effectUsageScope: IAvEffectUsageScope.() -> Unit
    ): IAvEffectUsage

    /**
     * 将一个特效effectUsage插入到 clip特效列表 index索引特效之前
     * val index = clip.effects.indexOf(effectUsage)
     * @param clip 操作的目标clip
     * @param index  待插入的index
     * @param effect 待插入的特效
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param render 是否触发一帧渲染
     * @param effectUsageScope 特效设置参数域
     * @return 是否插入成功
     */
    fun addEffect(
        clip: IAvClip,
        index: Int,
        effect: AvEffect,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true,
        effectUsageScope: IAvEffectUsageScope.() -> Unit
    ): IAvEffectUsage

    /**
     * 为AvTrack添加转场特效
     * @param track 待添加转场特效的轨道
     * @param transition 添加转场特效[AvTransition]
     * @param position 添加到轨道上的时间t
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param render 是否触发一帧渲染
     * @param transitionUsageScope 转场特效设置域
     */
    fun addEffect(
        track: IAvTrack,
        position: Timestamp,
        transition: AvTransition,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true,
        transitionUsageScope: IAvTransitionUsageScope.() -> Unit
    ): IAvTransitionUsage

    /**
     * 删除AvClip上挂载的特效
     * @param clip
     * @param effectUsage 删除已经添加的特效
     * @param removeCache 删除特效，是否删除缓存，有些业务场景<添加特效完，没有保存，点击取消，希望下次重新做特效的时候不命中缓存>
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param render 是否触发一帧渲染
     * @return 代表removeEffect删除特效引起的一帧渲染的requestCode
     */
    fun removeEffect(
        clip: IAvClip,
        effectUsage: IAvEffectUsage,
        removeCache: Boolean = false,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true
    ): Int

    /**
     * 删除AvClip上挂载的转场特效
     * @param track
     * @param effectUsage 删除已经添加的特效
     * @param removeCache 删除特效，是否删除缓存，有些业务场景<添加特效完，没有保存，点击取消，希望下次重新做特效的时候不命中缓存>
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param render 是否触发一帧渲染
     * @return 代表removeEffect删除特效引起的一帧渲染的requestCode
     */
    fun removeEffect(
        track: IAvTrack,
        effectUsage: IAvEffectUsage,
        removeCache: Boolean = false,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true
    ): Int

    /**
     * 将AvTrack上的某个转场特效删除
     * @param track 待删除转场特效的轨道
     * @param transitionUsage 添加转场特效[IAvTransitionUsage]
     * @param removeCache 删除特效，是否删除缓存，有些业务场景<添加特效完，没有保存，点击取消，希望下次重新做特效的时候不命中缓存>
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param render 是否触发一帧渲染
     * @return 代表removeEffect删除特效引起的一帧渲染的requestCode
     */
    fun removeEffect(
        track: IAvTrack,
        transitionUsage: IAvTransitionUsage,
        removeCache: Boolean = false,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true
    ): Int

    /**
     * 修改某个特效的参数信息
     * @param effectUsage 修改的特效参数
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param effectUsageScope 特效设置参数域
     */
    fun modifyEffect(
        effectUsage: IAvEffectUsage,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true,
        effectUsageScope: IAvEffectUsageScope.() -> Unit
    )

    /**
     * 修改某个转场特效
     * @param transitionUsage 待修改的转场特效
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param transitionUsageScope 转场特效设置域
     */
    fun modifyEffect(
        transitionUsage: IAvTransitionUsage,
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        render: Boolean = true,
        transitionUsageScope: IAvTransitionUsageScope.() -> Unit
    )

    /**
     * 取消某个特效的执行
     * @param clip 执行的clip资源目标
     * @param effectUsage 取消指定的某个特效
     */
    fun cancelEffectRendering(clip: IAvClip, effectUsage: IAvEffectUsage)

    /**
     * 刷新管线再出一帧
     * 一般使用场景：管线执行特效完毕，但是业务期望能再触发管线空跑一次
     *
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @return 代表invalidateRendering刷新管线引起的一帧渲染的requestCode
     */
    fun invalidateRendering(sinkDescriptionKey: String = TextUtil.EMPTY_STRING): Int

    /**
     * 返回指定特效effectUsage在clip特效列表中第一次出现的索引，如果指定effectUsage不在Clip的特效列表中，则返回 -1
     * @param clip 正对于哪一个clip的特效列表进行查询指定特效
     * @param effectUsage 待查询的特效
     * @return 指定effectUsage在Clip的特效列表中的索引，不存在则返回-1
     */
    fun indexOfEffect(clip: IAvClip, effectUsage: IAvEffectUsage): Int

    /**
     * 批量处理编辑特效，且在执行批量操作时，会先停止管线渲染，进行批量操作，然后再触发渲染
     * 及当前批量操作的内容，只触发一帧渲染，触发一帧渲染后，管线会恢复之前的渲染模式
     *
     * @param sinkDescriptionKey 当前请求携带的sink描述，描述能够被解析，确定出当前的一帧输出到哪个sink上
     *        sinkDescriptionKey = TextUtil.EMPTY_STRING 输出指向AvSinkManager默认的defaultPreviewSink上
     *        如果defaultPreviewSink被移除，则默认往defaultEmptySink上输出
     *        可以使用EmptySink的描述，让档次操作进入管线缓存但不上屏
     * @param withRenderingOnce 需要批量执行的操作
     * @return
     */
    fun withRenderingOnce(
        sinkDescriptionKey: String = TextUtil.EMPTY_STRING,
        withRenderingOnce: IAvEditor.() -> Unit
    ): Int

    /**
     * 对当前编辑器状态的监听
     * @param onPlaybackEventChanged 当前的播控状态
     */
    fun setPlaybackEventChanged(onPlaybackEventChanged: (PlaybackEvent) -> Unit)

    /**
     * 取消所有的渲染请求，并且进入手动渲染模式，及轨道拓扑结构变化也需要主调调用触发渲染
     *  1. 取消所有的渲染请求
     *  2. 不在自动触发渲染请求（手动模式）
     */
    fun suspendRendering()

    /**
     * 启动渲染请求，进入自动渲染模式
     * 自动渲染模式中，轨道拓扑结构发生变化都会自动触发帧渲染请求，无需再手动调用触发渲染
     */
    fun resumeRendering()

    /**
     * 取消所有的渲染请求
     * 对应的所有的渲染请求也会被清除
     */
    fun abortRendering()

    /**
     * TODO(Marked by wanglian
     *      判断当前轨道是否存在需要被渲染的情况,及是否还有未被渲染的数据
     *      目前AvTrackCompositor reply的机制中，渲染成功、失败、取消都做了参数融合，那么如果当前一帧渲染失败或者取消，而参数没有被融合的情况下，isValidated是否应该就为false
     *      那么是否需要重新触发一帧（像图片编辑中），那现在参数不管什么状态都融合了，按理说图片编辑不会再回调中主动再触发一帧，那么图片编辑的修改参数并未被真正的使用上。
     *      1.所有这个uiOverlay是不是还需要标记消费的状态
     *      2.图片编辑 触发帧渲染是否需要依赖这个isValidated
     *      <--实现成本高的话可以后期实现,暂时没有发现有什么用处-->
     *      )
     * 现在有一个场景 业务在进行 effectUsage修改的时候 modifyArg uiOverlayArg 会将参数装载到uiOverlayArg上
     * 1.如果在修改的时候，管线中当前有一帧正在执行，那么他会在这一帧执行完之后 执行merge，通知再渲染一帧，将修改后的参数渲染执行
     * 2.但是有一种场景，就是管线中没有当前的帧在渲染，按照通用的流程 modifyArg也是放在 uiOverlayArg，这个时候触发一帧没有merge，参数就不会被作用上。
     *
     * 所以这个可能需要当前这个字段，还有数据没有融合进去，需要主动merge一下
     */
    val isValidated: Boolean
}