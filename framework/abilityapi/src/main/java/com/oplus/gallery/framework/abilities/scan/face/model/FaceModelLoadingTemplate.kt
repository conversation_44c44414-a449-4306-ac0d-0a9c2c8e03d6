/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : FaceModelLoadingTemplate.kt
 ** Description : FaceModelSource模型下载类
 ** Version     : 1.0
 ** Date        : 2024/3/19
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                            2024/3/19      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.face.model

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * FaceModelSource模型下载类
 */
class FaceModelLoadingTemplate(context: Context, modelConfig: ModelConfig, allowContinueAction: (() -> Boolean)? = null) :
    CommonModelLoadingTemplate(context, modelConfig, allowContinueAction) {

    override val tag: String = TAG

    override val modelName: String = ModelName.FACE_MODEL_SOURCE

    companion object {
        private const val TAG = "FaceModelLoadingTemplate"
    }
}