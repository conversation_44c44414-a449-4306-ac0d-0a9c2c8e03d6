/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IMapAbility.kt
 ** Description : 对外暴露的地图能力接口，实现类在MapAbilityImpl中
 **
 ** Version     : 1.0
 ** Date        : 2025/4/7
 ** Author      : huangyuanwang
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** ******** Pengcheng Lin      2024/6/20     1.0        Add KFile header describe
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.map

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Point
import android.os.Bundle
import android.view.ViewGroup
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem

interface IMapAbility : AutoCloseable {

    object MapStateConfigKeys {
        /**
         * 代表地图旋转角度的键值
         */
        const val MAP_STATE_CONFIG_KEYS_ROTATE = "map_state_config_rotate"

        /**
         * 代表地图中心点经纬度的键值
         */
        const val MAP_STATE_CONFIG_KEYS_CENTER_TARGET = "map_state_config_center_target"

        /**
         * 代表地图缩放级别的键值
         */
        const val MAP_STATE_CONFIG_KEYS_ZOOM = "map_state_config_zoom"

        /**
         * 代表地图俯仰角度的键值
         */
        const val MAP_STATE_CONFIG_KEYS_OVERLOOK = "map_state_config_overlook"
    }

    /**
     * 判断是否支持地图
     */
    fun isMapSupported(): Boolean


    /**
     * 判断地图是否支持截图接口
     */
    fun isSnapshotSupported(): Boolean


    /**
     * 看是否支持在大图详情页面显示入口
     */
    fun canShowInDetailOuter(): Boolean

    /**
     * 初始化Map的sdk，需要在Application或Map使用前调用
     */
    fun initMapSdk(context: Context)

    /**
     * 判断MapSdk是否已经初始化
     */
    fun isMapSdkInited(): Boolean


    /**
     * 初始化MapView，根据入参initMapConfig对MapView以及内部的图集聚合以及图集显示类进行初始化
     * @param mapViewContainer mapView所属的外部父布局
     * @param savedInstanceState mapView所在的activity的savedInstanceState的bundle
     * @param initMapConfig 初始化配置项
     * @return 返回的mapWraper的id，代表这一次添加进去的mapView的实例
     */
    fun initMapView(mapViewContainer: ViewGroup, savedInstanceState: Bundle?, initMapConfig: InitMapConfig): String

    /**
     * 生命周期函数，所在的Activity或Fragment的onPause或onStop的时候调用
     * @param mapInstanceId 需要pause的mapWrapperId
     */
    fun onPause(mapInstanceId: String)

    /**
     * 生命周期函数，所在的Activity或Fragment的onStart或onResume的时候调用
     * @param mapInstanceId 需要resume的mapWrapperId
     */
    fun onResume(mapInstanceId: String)

    /**
     * 生命周期函数，所在的Activity或Fragment的onDestroy的时候调用
     * @param mapInstanceId 需要destroy的mapWrapperId
     */
    fun onDestroy(mapInstanceId: String)


    /**
     * 初始化国家编码和location之间的映射配置
     * @param async 异步执行还是同步执行, true = 异步， false = 同步
     * @param checkupdate 是否后台检查更新配置
     */
    fun initCountryMapConfig(async: Boolean, checkUpdate: Boolean)

    /**
     * 打开或关闭 显示附近图片开关，这里只有在InitMapConfig中EntranceType==IMAGE_DETAIL_INNER 时才有实现
     * @param showSingle true显示单独的图片， false显示所有图片
     * @param mapInstanceId 需要切换显示附近图片开关的mapWrapperId
     */
    fun switchShowNearbyImage(mapInstanceId: String, showSingle: Boolean)

    /**
     * 获取当前显示附近图片开关的状态,
     * @param mapInstanceId 需要获取切换显示附近图片开关状态的MapView对应的mapWrapperId
     */
    fun getCurrentNearByMode(mapInstanceId: String): Boolean

    /**
     * 触发手机定位，定位到手机当前位置
     * @param mapInstanceId 需要定位到手机位置的的MapView对应的mapWrapperId
     * @param isCaptureMap 是否需要地图截图
     */
    fun locateInCurrentPosition(mapInstanceId: String, isCaptureMap: Boolean = false, callback: ILocationResultCallback? = null)

    /**
     * 触发截图加载
     */
    fun justTrigerMapCapture(mapInstanceId: String, listener: CaptureMapRegionListener?)


    fun setCaptureMapRegionListener(listener: CaptureMapRegionListener?)

    /**
     * 获取当前地图的状态信息，可用于异常状态下地图位置信息的恢复
     * @param mapInstanceId 需要获取地图位置等状态信息的MapView对应的mapWrapperId
     */
    fun getCurrentMapStateConfig(mapInstanceId: String): MapStateConfig?

    /**
     * marker点击事件处理
     */
    interface MarkerClickListener {

        /**
         * 点击marker的点击事件处理
         * @param items 当前marker中包含的所有的图片的路径列表
         * @param point 当前marker处于mapView中的位置（x，y）坐标
         */
        fun onPick(items: ArrayList<Path>, point: Point)
    }


    enum class EntranceType {
        /**
         * 代表从主页->发现->地图入口进入地图详情页
         */
        MAP_PAGE_NORMAL,

        /**
         * 代表从主页->时间轴入口进入地图详情页
         */
        MAP_PAGE_TIMELINE,

        /**
         * 代表从照片详情页显示外部小地图
         */
        IMAGE_DETAIL_OUTER,

        /**
         * 代表从照片详情页点击进入地图详情页
         */
        IMAGE_DETAIL_INNER,

        /**
         * 代表发现页显示小地图入口
         */
        EXPLORER_MAP_ENTRANCE,
    }

    data class MapStateConfig(
        /**
         * 当前地图的旋转角度
         */
        var rotate: Float?,

        /**
         * 当前地图所处的中心经纬度
         */
        var centerTarget: LatLng?,

        /**
         * 当前地图的缩放系数
         */
        var zoom: Float?,

        /**
         * 当前地图的俯仰角度
         */
        var overlook: Float?
    )


    data class InitMapConfig(
        /**
         * 配置的入口类型
         */
        var entranceType: EntranceType?,

        /**
         * MapLocationAlbum的path的字符串
         */
        var mapLocationAlbumPath: String?
    ) {
        companion object {
            /**
             * 全屏地图时的logo的padding
             */
            const val PADDING_STYLE_DFAULT = 0

            /**
             * 大图详情页小地图时的logo的padding
             */
            const val PADDING_STYLE_SMALL = 1

            /**
             * logo没有padding
             */
            const val PADDING_STYLE_NO_PADDING = 2
        }


        /**
         * Map中是否显示mark，默认为true，不显示时，不用初始化MapHelper中的CeocodeClustering和OverlayAdapter
         */
        var showMarker: Boolean = true

        /**
         * 进入MapView中的默认中心点经纬度，
         */
        var initLocation: LatLng? = null

        /**
         * 进入MapView中的默认封面item
         */
        var hintImage: LocalMediaItem? = null

        /**
         * MapView内marker点击响应事件处理callback
         */
        var markerClickCallback: MarkerClickListener? = null

        /**
         * 单独显示一张图片时，需要显示的图片的Path，
         */
        var singleItemPath: String? = null

        /**
         * 进入MapView时的默认缩放级别
         */
        var defaultZoom: Float? = null

        /**
         * 是否支持地图缩放等手势,图片详情小地图时为false禁用一切手势
         */
        var supportGesture: Boolean = true

        /**
         * 切换显示附近图片回调callback，
         * 不需要支持切换显示附近图片时，该值为空即可，
         * 需要设置时保持不为空，MapAbility内部实现会注册和解注册这个callback
         */
        var nearbyModeChangeCallback: NearbyModeChangeCallback? = null

        /**
         * 地图是否使用TextureView
         */
        var useTextureView: Boolean = false
    }

    /**
     * 地图入口，地图截图的回调callback
     */
    interface CaptureMapRegionListener {
        fun onCaptureMapRegion(location: LatLng, bitmap: Bitmap?)
    }

    /**
     * 显示附近图片
     */
    interface NearbyModeChangeCallback {

        /**
         * 显示附近图片状态发生切换，用于回调改变button上面的文字
         * @param showNearBy 是否显示附近照片
         */
        fun nearByModeChange(showNearBy: Boolean)
    }

    interface ILocationResultCallback {

        object CallbackResultCode {
            // 检查执行定位的初始化环境
            const val LOCATION_ENVIRONMENT_CHECK = 1

            // 底层定位开始执行定位
            const val NETWORK_LOCATION_START = 2

            // 底层定位执行成功
            const val NETWORK_LOCATION_SUCCESS = 3

            // 底层定位执行失败
            const val NETWORK_LOCATION_FAILED = 4

            // 底层定位超时重试流程
            const val NETWORK_LOCATION_TIMEOUT_RETRY = 5

            // 底层定位失败重试流程
            const val NETWORK_LOCATION_FAIL_RETRY = 6
        }

        object LocationExtra {
            //定位服务不可用
            const val FAILURE_LOCATION_SERVICE_UNAVAILABLE = "location service unavailable"

            //定位服务不可用
            const val FAILURE_LOCATION_NET_PROVIDER_UNAVAILABLE = "location net provider unavailable"

            //定位权限未授权
            const val FAILURE_LOCATION_PERMISSION_UNAVAILABLE = "location permission unavailable"

            // 网络不可用
            const val FAILURE_NETWORK_UNAVAILABLE = "network unavailable"

            // LocationManager为空
            const val FAILURE_LOCATION_MANAGER_NULL = "location manager null"

            // 无效的经纬度坐标
            const val FAILURE_COORDINATE_UNAVAILABLE = "coordinate unavailable"

            // 检查执行定位的初始化环境
            const val LOCATION_ENV_CHECK = "check location environment start"

            // 底层网络定位开始执行定位
            const val NETWORK_LOCATION_START = "network location start"

            // 底层网络定位执行成功
            const val NETWORK_LOCATION_SUCCESS = "network location success"

            // 底层网络定位执行失败
            const val NETWORK_LOCATION_FAILED = "network location failed"

            // 定位超时
            const val FAILURE_TIMEOUT = "location timeout"

            // 底层网络定位超时重试流程
            const val NETWORK_LOCATION_TIMEOUT_RETRY = "network location timeout retry"

            // 底层网络定位失败重试流程
            const val NETWORK_LOCATION_FAIL_RETRY = "network location failed retry"

            // 底层网络定位超时重试,重试依旧识别
            const val NETWORK_LOCATION_TIMEOUT_RETRY_FAILED = "network location timeout retry,finally failed"

            // 底层网络定位失败重试流程,重试依旧识别
            const val NETWORK_LOCATION_FAIL_RETRY_FAILED = "network location failed retry,finally failed"

            //未知错误
            const val UNKNOWN_ERROR = "unknown error"

            //发生异常
            const val HAS_EXCEPTION = "has exception："

            // 检查执行定位的初始化环境
            const val LOCATION_IS_RUNNING_NEED_IGNORE_THIS = "current is locating, ignore this"
        }


        /**
         *仅定位结果回调接口
         */
        fun onLocationResult(
            //定位结果码，对应CallbackResultCode中的定义的值
            resultCode: Int,
            //定位定位结果说明，对应LocationExtra中的定义的值
            extra: String,
            //定位的经纬度
            latLng: LatLng? = null
        )
    }
}