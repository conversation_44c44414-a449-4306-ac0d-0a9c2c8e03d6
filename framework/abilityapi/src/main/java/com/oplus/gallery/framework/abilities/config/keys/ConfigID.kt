/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigID.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/5/18
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/18      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.config.keys

import com.oplus.gallery.foundation.database.helper.Constants
import com.oplus.gallery.framework.abilities.config.constant.ConfigType
import com.oplus.gallery.framework.abilities.config.constant.DataType.BOOL
import com.oplus.gallery.framework.abilities.config.constant.DataType.INT
import com.oplus.gallery.framework.abilities.config.constant.DataType.LONG
import com.oplus.gallery.framework.abilities.config.constant.DataType.OBJECT
import com.oplus.gallery.framework.abilities.config.constant.DataType.STRING
import com.oplus.gallery.framework.abilities.config.constant.StorageType
import com.oplus.gallery.framework.abilityapt.annotations.Node
import com.oplus.gallery.framework.abilityapt.annotations.NodeGroup

/**
 * 配置Key定义的顶层类。
 *
 * 分类规范：https://odocs.myoas.com/mindmaps/KlkKVZDjp8hEmPqd
 *
 * 引用KEY的示例
 *
 * ```
 * ConfigID.Common.Debug.DEBUG_TEST_ENVIRONMENT
 * ```
 * 节点ID的命名规范：
 * 1. 以字母开始
 * 2. 单词与单词之间用下划线或.，建议.只用于系统属性类型的配置
 *
 */
class ConfigID {
    /**
     * 业务不相关或无直接关系的配置
     */
    @NodeGroup(id = "common")
    class Common {

        /**
         * 调试和测试相关配置
         */
        @NodeGroup(id = "debug")
        object Debug {
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val DEBUG_TEST_ENVIRONMENT = "test_environment"

            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val DEBUG_GALLERY_ACCOUNT_TEST = "gallery_account_test"

            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val DEBUG_TEST_AIIDPHOTO_RESP_CODE = "test_aiidphoto_resp_code"

            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val DEBUG_TEST_GALLERY_DB_WAL = "test_gallery_db_wal"
        }

        /**
         * 设备或系统基本信息
         */
        @NodeGroup(id = "systemInfo")
        object SystemInfo {

            /**
             * 用户设置区域，可变，功能需要区分区域时一般使用[REGION_MARK]
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val REGION = "region"

            /**
             * 打点区域，即出货区域，不变，功能需要区分区域时一般使用这个
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val REGION_MARK = "region_mark"

            /**
             * 印度区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_REGION_INDIA = "is_region_india"

            /**
             * 美国区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_REGION_US = "is_region_us"

            /**
             * 越南区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_REGION_VIETNAM = "is_region_vietnam"

            /**
             * 土耳其区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_REGION_TURKEY = "is_region_turkey"

            /**
             * 俄罗斯区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_REGION_RUSSIA = "is_region_russia"

            /**
             * 白俄罗斯区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_REGION_BELARUS = "is_region_belarus"

            /**
             * 中国区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val IS_REGION_CN = "is_region_cn"

            /**
             * 售后区域
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val AFTER_SALE_REGION = "after_sale_region"

            /**
             * 运营商
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val OPERATOR = "operator"

            /**
             * 运营商是否为中国移动
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_CMCC = "is_cmcc"

            /**
             * 运营商是否为软银
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SOFTBANK = "is_softbank"

            /**
             * 市场名称
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SYSTEM_PROP], dataType = STRING)
            const val MARKET_NAME = "market_name"

            /**
             * 相机水印设置页，预览图水印使用的市场名称
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SYSTEM_PROP], dataType = STRING)
            const val CAMERA_WATERMARK_PREVIEW_MARKET_NAME = "camera_watermark_preview_market_name"

            /**
             * 是否为MTK平台
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_MTK_PLATFORM = "is_mtk_platform"

            /**
             * 是否为高通平台
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_QUALCOMM_PLATFORM = "is_qualcomm_platform"

            /**
             * 是否为低性能平台
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_LOW_PERFORMANCE_PLATFORM = "is_low_performance_platform"

            /**
             * OS版本
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val OS_VERSION = "os_version"

            /**
             * OTA版本
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val OTA_VERSION = "ota_version"

            /**
             * ROM版本
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val ROM_VERSION = "rom_version"

            /**
             * Android版本
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val ANDROID_VERSION = "android_version"

            /**
             * 产品平台
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val PRODUCT_BRAND = "product_brand"

            /**
             * 是否是realme品牌
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_REALME_BRAND = "is_realme_brand"

            /**
             * 是否是OPPO品牌
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_OPPO_BRAND = "is_oppo_brand"

            /**
             * 是否是OnePlus品牌
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_ONEPLUS_BRAND = "is_oneplus_brand"

            /**
             * 主板系统
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val BOARD_PLATFORM = "board_platform"

            /**
             * CPU  part number
             * 例：SDM845
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val CPU_INFO_PART_NUMBER = "cpu_info_part_number"

            /**
             * CPU 信息, 其中包含part number
             * 例：Hardware : Qualcomm Technologies, Inc SM8250
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val CPU_INFO = "cpu_info"

            /**
             * 产品名称
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val PRODUCT_NAME = "product_name"

            /**
             * 产品系列
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val PRODUCT_SERIES = "product_series"

            /**
             * 构建时间
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val BUILD_TIME = "build_time"

            /**
             * 手机型号
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = STRING)
            const val PHONE_MODEL = "phone_model"

            /**
             * 是否为平板
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_TABLET = "is_tablet"

            /**
             * 是否为折叠屏
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_FOLD = "is_fold"

            /**
             * 搭载双屏的设备是否禁用大小屏切换能力（官方描述）
             *
             * - 此 Feature 仅在折叠屏、搭载双屏的设备有效
             * - 如果有此 Feature，说明设备的每个屏幕各分配一个 Display，而不是共用同一个 Display
             *
             * 参考：[外屏应用适配参考](https://odocs.myoas.com/docs/RKAWVRaRo0hbbRk8)
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_FOLD_REMAP_DISPLAY_DISABLED = "is_fold_remap_display_disabled"


            /**
             * 折叠屏，是否支持双屏接力
             *
             * 是否为折叠屏 && 搭载双屏的设备是启用大小屏切换能力
             *
             * @see IS_FOLD
             * @see IS_FOLD_REMAP_DISPLAY_DISABLED
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_FOLD_REMAP_DISPLAY_ENABLED = "is_fold_remap_display_enabled"

            /**
             * 是否为多 [android.view.Display] 设备
             *
             * 是否为折叠屏 && 搭载双屏的设备是否禁用大小屏切换能力
             *
             * 参考：[外屏应用适配参考](https://odocs.myoas.com/docs/RKAWVRaRo0hbbRk8)
             *
             * @see IS_FOLD
             * @see IS_FOLD_REMAP_DISPLAY_DISABLED
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_MULTI_DISPLAY = "is_multi_display"

            /**
             * API Level
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = INT)
            const val API_LEVEL = "api_level"

            /**
             * First API Level
             * 系统初始版本 即 出厂版本，升级项目该值保持不变
             * 该值可用于判断当前项目是否是升级项目
             * api_level 30升级到31，33升级到34的项目，first_api_level保持不变，始终是初始版本的值
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = INT)
            const val FIRST_API_LEVEL = "first_api_level"

            /**
             * 当前时区，在appModule更新
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
            const val TIME_ZONE = "time_zone"

            /**
             * 存储 系统 externalCacheDir（目的：冷启动是 获取比较耗时，所以存起来，加快冷启动）
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP, StorageType.MEMORY], dataType = STRING)
            const val EXTERNAL_CACHE_DIR = "external_cache_dir"

            /**
             * 最大亮度
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = INT)
            const val MAX_BRIGHTNESS = "max_brightness"

            /**
             * 是否支持背光优化
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_BACKLIGHT_OPTIMIZE = "is_support_backlight_optimize"

            /**
             * 是否支持 进入相册亮度一致性
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_BRIGHTEN_UNIFORMITY = "is_support_brighten_uniformity"

            /**
             * 是否支持自动亮度动画
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_AUTO_BRIGHTNESS_ANIMATION = "is_support_auto_brightness_animation"

            /**
             * 是否支持高背光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_SUPPORT_HIGH_BRIGHTNESS = "is_support_high_brightness"

            /**
             * 是否主用户
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_PRIMARY_USER = "is_primary_user"

            /**
             * 政企模式
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_CONTAINER_USER = "is_container_user"

            /**
             * 是否儿童空间
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_CHILDREN_MODE = "is_children_mode"

            /**
             * 文件级加密 (FBE) 确认 ro.crypto.type 是否已设置为 file
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_FBE_VERSION = "is_fbe_version"

            /**
             * 用户是否解锁设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_USER_UNLOCKED = "is_user_unlocked"

            /**
             * 是否系统分身
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_MULTI_SYSTEM_USER = "is_multi_system_user"

            /**
             * 默认显示设备圆角（ro.oplus.display.rc.size、ro.display.rc.size）
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val DISPLAY_CORNER_RADIUS = "display_corner_radius"

            /**
             * 第二显示设备圆角（ro.oplus.display.secondary.rc.size）
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = STRING)
            const val SECONDARY_DISPLAY_CORNER_RADIUS = "secondary_display_corner_radius"

            /**
             * 个人信息保护界面，是否特殊处理公司信息
             * 一加 + 内销 + android T + 无feature，特殊处理，显示oppo
             * 其余显示默认
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SHOW_SPECIAL_COMPANY_NAME = "is_show_special_company_name"

            /**
             * localHdrVersion
             * 当前系统支持Local HDR效果的版本号，版本号从1开始
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = INT)
            const val BRIGHTEN_VERSION_LOCAL_HDR = "brighten_version_local_hdr"

            /**
             * dolbyEdrVersion
             * 当前系统支持EDR效果，如UI Dimming或Local HDR效果的版本号，版本号从1开始
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = INT)
            const val BRIGHTEN_VERSION_DOLBY = "brighten_version_dolby"

            /**
             * 是否支持杜比的解码
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_DOLBY_DECODE = "is_support_dolby_decode"

            /**
             * 是否支持杜比的编码
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_DOLBY_ENCODE = "is_support_dolby_encode"

            /**
             * 是否支持杜比GPU解码方案
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_DOLBY_GPU_DECODE = "is_support_dolby_gpu_decode"

            /**
             * 是否支持杜比的提速编码:
             * 1.支持的情况下，设备能够支撑美摄SDK根据[android.media.MediaCodecInfo.VideoCapabilities.getSupportedFrameRatesFor]返回值来为MediaCodec
             *   设置更高的[android.media.MediaFormat.KEY_OPERATING_RATE]值，以便尽可能的快地完成编码；
             * 2.不支持的情况下，设备无法支撑美摄SDK为MediaCodec设置的KEY_OPERATING_RATE值，应该交由MediaCodec自行决定处理速度
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_DOLBY_ENCODE_ACCELERATE = "is_support_dolby_encode_accelerate"

            /**
             * 是否支持Hlg的编码
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_HLG_ENCODE = "is_support_hlg_encode"

            /**
             * 是否支持相册提亮
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SYSTEM_PROP], dataType = BOOL)
            const val IS_SUPPORT_BRIGHTNESS_ENHANCE = "is_support_brightness_enhance"


            /**
             * 是否支持设定VelocityTracker Strategy为IMPULSE
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_VELOCITYTRACKER_STRATEGY_IMPULSE = "is_support_velocitytracker_strategy_impulse"

            /**
             * 是否为支持大图Tile块Level优化计算
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SUPPORT_TILE_LEVEL_OPTIMIZATION = "is_support_tile_level_optimization"

            /**
             * 是否为轻量低
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_PRODUCT_LIGHT_LOW = "is_product_light_low"

            /**
             * 是否为轻量高
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_PRODUCT_LIGHT_HIGH = "is_product_light_high"

            /**
             * 是否为轻量os
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_PRODUCT_LIGHT = "is_product_light"

            /**
             * 当前媒体库是否支持_camera_quick_uri字段。这是错峰需求中，通知媒体库删除错峰图的必要字段，如果不支持，那么就不能调用给媒体库，否则会报错
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_SUPPORT_MEDIA_STORE_CAMERA_QUICK_URI = "is_support_media_store_camera_quick_uri"

            /**
             * 从媒体库查询判断是否支持_camera_quick_uri字段
             * 不建议外部使用，外部推荐用[IS_SUPPORT_MEDIA_STORE_CAMERA_QUICK_URI]
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val GET_MEDIA_STORE_CAMERA_QUICK_URI = "get_media_store_camera_quick_uri"

            /**
             * 是否仅支持单SurfaceView实例，当前仅在部分低端机器上存在可能，如【24253】
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER, StorageType.MEMORY], dataType = BOOL)
            const val IS_SINGLE_SURFACEVIEW_DEVICE = "is_single_surfaceview_device"

            /**
             * ocs授权时的系统时间，用来判定是否需要再次进行授权
             *
             * ocs授权比较耗时，且会拉起ocs服务，故sf会针对包名进行授权缓存，系统重启后失效，故判定授权时间在重启之前，则需要重新请求授权建立缓存。
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
            const val OCS_AUTH_TIME_MILLIS = "ocs_auth_time_millis"
        }

        /**
         * 用户偏好设置
         */
        @NodeGroup(id = "userProfile")
        class UserProfile {
            /**
             *  浏览行为相关偏好设置
             */
            @NodeGroup(id = "viewBehavior")
            object ViewBehavior {
                /**
                 * Local Hdr用户设置
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val HDR_IMAGE_SWITCH = "local_hdr_switch"

                /**
                 * 提亮效果对比的设置，如Local HDR提亮效果对比。
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val BRIGHTEN_COMPARE_SWITCH = "brighten_compare_switch"

                /**
                 * 视频自动播放用户设置
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val VIDEO_AUTO_PLAY = "video_auto_play"

                /**
                 * 视频自动播放用户设置的默认值
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
                const val VIDEO_AUTO_PLAY_DEFAULT_VALUE = "video_auto_play_default_value"

                /**
                 * 显示隐藏图集项设置
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val SHOW_SAFE_BOX_ALBUM = "show_safe_box_album"

                /**
                 * 不再显示照片视图类型设置引导弹窗
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val NO_MORE_SHOW_PHOTO_VIEW_PRESENT_TYPE_GUIDE = "no_more_show_photo_view_present_type_guide"

                /**
                 * 时间轴的显示视图
                 * 0 - 沉浸视图，时间轴的图片会紧密罗列，不显示日期信息
                 * 1 - 日期视图，时间轴的常规视图，显示日期信息
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val PHOTO_VIEW_PRESENT_TYPE = "photo_view_present_type"

                /**
                 * 时间轴的默认显示视图
                 * 1.老机型：日期视图: OS16以下 || (OS16+ && 出厂版本V及以下)
                 * 2.新机型：沉浸式视图
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = INT)
                const val PHOTO_VIEW_DEFAULT_PRESENT_TYPE = "photo_view_default_present_type"

                /**
                 * 智能编辑推荐设置
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val INTELLIGENT_EDITING_RECOMMENDATION = "intelligent_editing_recommendation"

                /**
                 * 智能编辑推荐开关埋点状态，是否上报过该埋点
                 * OS 16.0可删除
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val INTELLIGENT_EDITING_RECOMMEND_TRACKED = "intelligent_editing_recommend_tracked"
            }

            /**
             * 用户隐私相关配置
             */
            @NodeGroup(id = "privacy")
            object Privacy {

                /**
                 * 隐私政策版本号
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val PRIVACY_STATEMENT_VERSION = "privacy_statement_version"

                /**
                 * 后台功能更新与资源下载能力授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD = "authorize_request_server_update_and_download"

                /**
                 * 所有需要使用云端能力的编辑功能的(内销)的必要前置授权.
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_CLOUD_EDITOR = "authorize_cloud_editor"

                /**
                 * 地点解析能力授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_LOCATION_RESOLVE = "authorize_location_resolve"

                /**
                 * AI修复功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_AI_REPAIR = "authorize_ai_repair"

                /**
                 * AI超清功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_AIHD = "authorize_aihd"

                /**
                 * AI涂鸦功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_AI_GRAFFITI = "authorize_ai_graffiti"

                /**
                 * AI证件照功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_AI_ID_PHOTO = "authorize_ai_id_photo"

                /**
                 * 获取用户常驻地能力授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_GET_USUAL_LOCATION = "authorize_get_usual_location"

                /**
                 * 共享图集功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_SHARE_ALBUM = "authorize_share_album"

                /**
                 * 超级文本功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_ENHANCE_TEXT = "authorize_enhance_text"

                /**
                 * OCR服务文本识别功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_TEXT_OCR_SERVICE = "authorize_text_ocr_service"

                /**
                 * 人脸扫描能力授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_FACE_SCAN = "authorize_face_scan"

                /**
                 * 旅程扫描能力授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_TRAVEL_SCAN = "authorize_travel_scan"

                /**
                 * 地图相册功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_GALLERY_MAP = "authorize_gallery_map"

                /**
                 * 云同步功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_CLOUD_SYNC = "authorize_cloud_sync"

                /**
                 * AI去眩光功能授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZE_AI_DEGLARE = "authorize_ai_deglare"

                /**
                 * 是否已确认授权
                 *
                 * 隐私协议更新后用户重新确认前为false，其他情况为true
                 * 用于功能授权的额外判断，例如：隐私协议更新后用户重新确认前，虽然重置功能授权，但暂时还要保留相关数据
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val AUTHORIZATION_RECONFIRMED = "authorization_reconfirmed"
            }
        }

        /**
         * 权限授权相关配置
         */
        @NodeGroup(id = "permission")
        object Permission {
            // 先占坑，后续业务补充
            /**
             * 授权访问媒体文件
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val AUTHORIZE_ACCESS_MEDIA = "authorize_access_media"

            /**
             * 外销授权使用画质增强
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_IMAGE_QUALITY_ENHANCE = "is_authorize_access_image_quality_enhance"

            /**
             * 外销授权使用消除功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_ELIMINATE = "is_authorize_access_ai_eliminate"

            /**
             * 外销授权使用AI 超清功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AIHD = "is_authorize_access_aihd"

            /**
             * 外销授权使用自动打码下载功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AUTO_MOSAIC = "is_authorize_access_auto_mosaic"

            /**
             * 外销授权使用AI最佳合影的下载功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_GROUP_PHOTO = "is_authorize_access_ai_group_photo"

            /**
             * 外销授权使用AI去模糊
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_DE_BLUR = "is_authorize_access_ai_de_blur"

            /**
             * 外销授权使用AI去反光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_DE_REFLECTION = "is_authorize_access_ai_de_reflection"

            /**
             * 是否同意用户协议
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AGREE_USER_AGREEMENT = "is_agree_user_agreement"

            /**
             * 外销授权使用AI涂鸦
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_GRAFFITI = "is_authorize_access_ai_graffiti"

            /**
             * 外销授权使用AI构图
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_COMPOSITION = "is_authorize_access_ai_composition"

            /**
             * 外销授权使用AI最佳表情
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_BEST_TAKE = "is_authorize_access_ai_best_take"

            /**
             * 外销授权使用AI 补光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_LIGHTING = "is_authorize_access_ai_lighting"

            /**
             * 外销授权使用AI去眩光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_AUTHORIZE_ACCESS_AI_DE_GLARE = "is_authorize_access_ai_de_glare"
        }
    }

    /**
     * 业务强相关配置，按代码实现层面的功能划分的，而非产品角度划分
     */
    @NodeGroup(id = "business")
    class Business {
        /**
         * Feature开关配置
         */
        @NodeGroup(id = "featureSwitch")
        object FeatureSwitch {
            /**
             * 是否支持背光优化
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_BACKLIGHT_OPTIMIZE = "feature_is_support_backlight_optimize"

            /**
             * 是否支持高背光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HIGH_BRIGHTNESS = "feature_is_support_high_brightness"

            /**
             * 是否支持杜比视频走硬件解码
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_DOLBY_VIDEO_HARDWARE_DECODER = "feature_is_support_dolby_video_hardware_decoder"

            /**
             * 配置项:是否支持视频标签跑CPU而不走GPU
             * 此问题是为了修复BUG 7953659，分析原因是对应的GPU OpenCL库有问题，需要在此平台上走CPU而不走GPU
             * 目前仅7550和7150平台支持
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_VIDEO_CLASSIFY_RUN_ON_CPU = "feature_is_support_video_classify_run_on_cpu"

            /**
             * 配置项:是否支持持延后100ms隐藏无缝动画
             * 此问题是为了修复BUG 8377196，分析原因是对应的sf queueBuffer有问题，需要在此平台上延迟隐藏无缝动画
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_DELAY_HIDE_SEAMLESS_TRANSITION_BY_MODEL_100 = "feature_is_support_dealy_100_hide_seamless_transition"

            /**
             * 配置项:是否支持持延后1500ms隐藏无缝动画
             * 此问题是为了兜底BUG 9217708 照片模式后置，关闭自动旋转，横竖屏拍照，横屏查看照片，闪屏;确认相机需求引入，相机下掉需求后兜底回退
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_DELAY_HIDE_SEAMLESS_TRANSITION_BY_MODEL_1500 = "feature_is_support_dealy_1500_hide_seamless_transition"

            /**
             * 手机搬家是否禁用
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_PHONE_CLONE_DISABLED = "feature_is_phone_clone_disabled"

            /**
             * 是否支持超级文本2.0,支持2.0的设备，1.0就自然不支持了，不支持2.0的设备1.0还会继续生效
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SUPER_TEXT_V2 = "feature_is_support_super_text_v2"

            /**
             * 是否支持超级文本 1.0 的 Ocr
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR = "feature_is_support_super_text_v1_ocr"

            /**
             * 是否支持时间轴全视图显示
             * 否：不显示 年视图、月视图、三列日视图
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_TIMELINE_PAGE_MULTI_VIEW_SWITCH = "feature_is_support_timeline_page_multi_view_switch"

            /**
             * 是否支持消除笔功能
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_ELIMINATE_PEN = "feature_is_support_eliminate_pen"

            /**
             * 是否支持虚化功能
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_BLURRING = "feature_is_support_blurring"

            /**
             * 是否支持多模态
             * 仅代表相册环境是否支持，不代表相册内就有多模态能力，涉及插件云端部署
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_MULTIMODAL = "feature_is_support_multimodal"

            /**
             * 是否支持OcrEmbedding
             * 仅代表相册环境是否支持，不代表相册内就有embedding能力，涉及插件云端部署
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OCREMBEDDING = "feature_is_support_ocrembedding"

            /**
             * 是否支持Caption
             * 仅代表相册环境是否支持，不代表相册内就有caption能力，涉及插件云端部署
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_CAPTION = "feature_is_support_caption"

            /**
             * 个保是否支持 Caption
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_PRIVACY_IS_SUPPORT_CAPTION = "feature_privacy_is_support_caption"

            /**
             * 是否支持中文知识图谱
             * 使用LocaleUtils#isChinaMainland()判断
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LABEL_GRAPH = "feature_is_support_label_graph"

            /**
             * 是否支持英文知识图谱
             * 英文 支持，否则不支持
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_ENGLISH_LABEL_GRAPH = "feature_is_support_english_label_graph"

            /**
             * 是否支持超级文本 1.0 的转文档
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_DOC = "feature_is_support_super_text_v1_convert_doc"

            /**
             * 是否支持超级文本 1.0 的 转PPT 文档
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_PPT_DOC = "feature_is_support_super_text_v1_convert_ppt_doc"

            /**
             * 是否支持超级文本 1.0 的转 word 文档
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_WORD_DOC = "feature_is_support_super_text_v1_convert_word_doc"

            /**
             * 是否支持超级文本 1.0 的转 excel 文档
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_EXCEL_DOC = "feature_is_support_super_text_v1_convert_excel_doc"

            /**
             * 是否支持自适应投屏
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_CAST_ADAPTION = "feature_is_support_cast_adaption"

            /**
             * 是否支持Presentation投屏
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_CAST_PRESENTATION = "feature_is_support_cast_presentation"

            /**
             * 是否支持合成GIF
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_GIF_SYNTHESIS = "feature_is_support_gif_synthesis"

            /**
             * 是否支持跳转地图页
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_MAP_PAGE = "feature_is_support_map_page"

            /**
             * 是否支持隐私保护助手
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PRIVACY_PROTECTION_ASSISTANT = "feature_is_support_privacy_protection_assistant"

            /**
             * 是否支持AI证件照
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_ID_PHOTO = "feature_is_support_ai_id_photo"


            /**
             * 是否支持Raw 编辑
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_RAW_EDIT = "feature_is_support_raw_edit"

            /**
             * 是否支持Local Hdr
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LOCAL_HDR = "feature_is_support_local_hdr"

            /**
             * 是否仅支持Local Hdr不支持Ultra Hdr，可能的情况：
             * 1. OS13.x支持Local Hdr的机型，必定不支持Ultra Hdr
             * 2. 部分14.0机型的早期MP版本可能存在支持LHDR，但未适配UHDR的情况，但存在这种用户的可能性较低
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LOCAL_HDR_ONLY = "feature_is_support_local_hdr_only"

            /**
             * 是否支持Local Hdr 编辑
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LOCAL_HDR_EDIT = "feature_is_support_local_hdr_edit"

            /**
             * 是否支持人像景深Hdr 编辑
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PORTRAIT_BLUR_HDR_EDIT = "feature_is_support_portrait_blur_hdr_edit"

            /**
             * 副屏是否支持Local HDR
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LOCAL_HDR_ON_SUB_DISPLAY = "feature_is_support_local_hdr_on_sub_display"

            /**
             * 是否支持Ultra Hdr
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_ULTRA_HDR = "feature_is_support_ultra_hdr"

            /**
             * 是否支持UHdr 编辑
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_UHDR_EDIT = "feature_is_support_uhdr_edit"

            /**
             * 是否支持UHdr的下变换算法
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_UHDR_TRANSFORM = "feature_is_support_uhdr_transform"

            /**
             * 是否支持提亮效果对比功能，如Local HDR提亮效果对比。
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_BRIGHTEN_COMPARE = "feature_is_support_brighten_compare"

            /**
             * 是否支持单图 HDR显示编辑
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HDR_COMBINE_DISPLAY_EDIT = "feature_is_support_hdr_combine_display_edit"

            /**
             * 是否支持dolby的提亮
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_DOLBY_BRIGHTEN = "feature_is_support_dolby_brighten"

            /**
             * 是否支持臻彩世界的提亮
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HDR_VISION_BRIGHTEN = "feature_is_support_hdr_vision_brighten"

            /**
             * 是否支持编辑中视频的提亮
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_EDIT_VIDEO_HDR_BRIGHTEN = "feature_is_support_edit_video_hdr_brighten"

            /*
             * 是否支持私密相册
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM = "feature_is_support_safe_box_album"

            /**
             * 版本是否支持私密保险箱图集加密功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_ALBUM_SAFE_BOX = "feature_is_support_album_safe_box"

            /**
             * 政企定制版本是否支持相册分享功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE = "feature_is_support_user_custom_gallery_share"

            /**
             * 政企定制版本是否支持OPPO互传功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_USER_CUSTOM_OSHARE = "feature_is_support_user_custom_oshare"

            /**
             * 政企定制版本是否支持私密保险箱功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX = "feature_is_support_user_custom_safe_box"

            /**
             * 平台是否支持HDR功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HDR = "feature_is_support_hdr"

            /*
             * 是否支持人像景深
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PORTRAITBLUR = "feature_is_support_portraitblur"

            /**
             * 是否支持哈苏水印编辑
             * 这个值是依赖相机配置文件，判断是否支持哈苏水印编辑
             * 为了限制 图片编辑--哈苏水印入口 只在OS13.1上开放
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HASSEL_WATERMARK = "feature_is_support_hassel_watermark"

            /**
             * 是否为哈苏联名设备
             * 这个值是依赖相机配置文件，判断是否是支持哈苏的设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HASSEL_DEVICE = "feature_is_support_hassel_device"

            /**
             * 是否支持哈苏水印编辑  值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_HASSLE_WATERMARK_SUPPORTED = "is_hassel_watermark_supported"

            /**
             * 是否为哈苏联名设备  值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_SUPPORT_HASSEL_DEVICE = "is_support_hassel_device"

            /**
             * 是否支持孤独星球水印编辑
             * 这个值是依赖相机配置文件，判断是否支持孤独星球水印编辑
             * 为了限制 图片编辑--孤独星球水印入口 只在OS13.1上开放
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LONELY_PLANET_WATERMARK = "feature_is_support_lonely_planet_watermark"

            /**
             * 是否支持孤独星球水印编辑  值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_LONELY_PLANET_WATERMARK_SUPPORTED = "is_lonely_planet_watermark_supported"

            /**
             * 是否是新机型
             * 这个值是依赖相机配置文件，判断是否是新机型，相机对于新旧机型的地理位置解析规则不一样
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_CAMERA_CONFIG_NEW_PROJECT = "feature_is_camera_config_new_project"

            /**
             * 根据相机的配置文件判断是否是新机型，该值用于水印功能的地理位置解析规则参数使用  值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_CAMERA_CONFIG_WATERMARK_NEW = "is_camera_config_watermark_new"

            /**
             * 是否支持水印（目前已全面切换到大师水印）
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK = "feature_is_support_photo_editor_watermark"

            /**
             * 是否支持水印大师编辑
             * 这个值是为了限制 图片编辑--水印大师入口，
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_WATERMARK_MASTER = "feature_is_support_watermark_master"

            /**
             * 是否支持显示大图缩略图轴
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PHOTO_THUMB_LINE = "feature_is_support_photo_thumb_line"

            /**
             * 是否支持精选视图
             * 否：不显示 精选年视图、精选月视图、精选日视图
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SENIOR_PICKED = "feature_is_support_senior_picked"

            /**
             * 是否启用侧边栏功能
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SIDE_PANE = "feature_is_support_side_pane"

            /**
             * 是否支持LocalHdr图层改成非LocalHdr类型
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_DISABLE_HDR_IMAGE_LAYER = "feature_is_support_disable_local_hdr_layer"

            /**
             * 多媒体是否支持10亿色解码，调用耗时60ms左右，需要放入子线程调用
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_10BIT_DECODE = "feature_is_support_10bit_decode"

            /**
             * 是否只支持LHDR Dimming类型
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HDR_IMAGE_ONLY_DIMMING = "feature_is_support_hdr_image_only_dimming"

            /**
             * 是否支持 Google 相册云服务（本地+云端）
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_GOOGLE_CLOUD = "feature_is_support_google_cloud"

            /**
             * 是否支持 Google 相册云服务（本地）
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_GOOGLE_CLOUD_LOCAL = "feature_is_support_google_cloud_local"

            /**
             * 是否为卖场模式
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SELL_MODE = "feature_is_sell_mode"

            /**
             * 是否支持超级加密
             * 需要手机有安全芯片 并且 私密保险箱支持安全芯片
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SUPER_ENCRYPTION = "feature_is_support_super_encryption"

            /**
             * 是否支持隐私水印
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PRIVACY_WATERMARK = "feature_is_support_privacy_watermark"

            /**
             * 是否支持新春水印
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SPRING_FESTIVAL_WATERMARK = "feature_is_support_spring_festival_watermark"

            /**
             * 是否支持街拍水印
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_STREET_WATERMARK = "feature_is_support_street_watermark"

            /**
             * 是否支持街拍水印  值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_STREET_WATERMARK_SUPPORTED = "IS_STREET_WATERMARK_SUPPORTED"

            /**
             * 是否支持色彩水印
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_COLOR_WATERMARK = "feature_is_support_color_watermark"

            /**
             * 是否支持色彩水印 值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_COLOR_WATERMARK_SUPPORTED = "is_color_watermark_supported"

            /**
             * 是否Quick动画时间为短时长
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_QUICK_ANIM_DURATION_SHOT = "feature_is_quick_anim_duration_shot"

            /**
             * 是否支持边框-影幕编辑
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_BORDER_CAPTION = "feature_is_support_border_caption"

            /**
             * 是否支持外屏连拍接续功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_CSHOT_CONTINUATION = "feature_is_support_cshot_continuation"

            /**
             * 是否支持抠图
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LNS = "feature_is_support_lns"

            /**
             * 针对OLive资源，是否支持抠图
             * 低端机OLive资源长按会先触发抠图，400ms之后再触发OLive播放。但OLive资源抠图结果返回会比较久，且在400ms之内没有抠图结果返回
             * 此时才会触发OLive播放，会给用户感觉顿挫感。产品决策，低端机暂时针对OLive资源禁用掉抠图，待抠图与OLive手势冲突解决后，再放开。
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LNS_FOR_OLIVE = "feature_is_support_lns_for_olive"

            /**
             * 是否支持使用相机image process unit的水印后编辑功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_IPU_WATERMARK = "feature_is_support_ipu_watermark"

            /**
             * 是否支持使用相机IPU(Image Process Unit)的后置景深编辑功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_IPU_REAR_BLUR = "feature_is_support_ipu_rear_blur"

            /**
             * 是否支持使用相机IPU(Image Process Unit)的前置景深编辑功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_IPU_FRONT_BLUR = "feature_is_support_ipu_front_blur"

            /**
             * 是否支持画框水印编辑
             * 这个值是为了限制 非哈苏机型画框水印 只在14.0非升级项目上开放
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_FRAME_WATERMARK = "feature_is_support_photo_editor_frame_watermark"

            /**
             * 是否开放融合搜索能力
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_ANDES_SEARCH = "feature_is_support_andes_search"

            /**
             * 是否支持quick图删除
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_QUICK_PHOTO_DELETE = "feature_is_support_quick_photo_delete"

            /**
             * 相机配置，三天更新一次
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
            const val CAMERA_CONFIG_EXPIRY = "camera_config_expiry"

            /**
             * 是否支持quick图公开
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_QUICK_PHOTO_PUBLIC = "feature_is_support_quick_photo_public"

            /**
             * 是否支持quick图公开  值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_QUICK_PHOTO_PUBLIC_SUPPORTED = "is_quick_photo_public_supported"

            /**
             * 相机是否支持在设置中设置照片查看方向
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_CAMERA_SUPPORT_PREVIEW_ROTATE = "feature_is_camera_review_rotate_supported"

            /**
             * 是否支持相机设置中设置照片查看方向  值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_CAMERA_SUPPORT_PREVIEW_ROTATE = "is_camera_review_rotate_supported"

            /**
             * 是否支持外屏授权功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AUTHORIZING_ON_SECONDARY_DISPLAY = "feature_is_support_authorizing_on_secondary_display"

            /**
             * 是否支持合影优化
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_GROUP_PHOTO = "feature_is_support_group_photo"

            /**
             * 是否支持OLive功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE = "feature_is_support_olive"

            /**
             * 是否支持OLive的HDR视频（视频播放也提亮）的功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_HDR_VIDEO = "feature_is_support_olive_hdr_video"

            /**
             * 是否支持Ai超清大图显示入口
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AIHD_PAGE = "feature_is_support_aihd_page"

            /**
             * 是否支持Ai超清2.0-realme超清功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_RM_IMAGE_QUALITY_ENHANCE = "feature_is_support_rm_image_quality_enhance"

            /**
             * 是否支持Ai 人脸超清-realme超清功能
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_FACE_HD = "feature_is_support_ai_face_hd"

            /**
             * 是否支持Ai涂鸦
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_GRAFFITI = "feature_is_support_ai_graffiti"

            /**
             * 是否支持Ai去眩光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_DEGLARE = "feature_is_support_ai_deglare"

            /**
             * 是否支持OLive的FOV
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_FOV = "feature_is_support_olive_fov"

            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_SLIDE_PLAY = "feature_is_support_olive_slide_play"

            /**
             * 是否支持Olive的补帧
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_FRAME_FILLTER = "feature_is_support_olive_frame_fillter"

            /**
             * 是否支持Olive的HEIF
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_HEIF = "feature_is_support_olive_heif"

            /**
             * 大图是否支持HDR视频的Olive的下变换
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM = "feature_is_support_photo_page_hdr_video_olive_transform"

            /**
             * 是否支持OLive编辑封面优化，olive2.0版本
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_COVER_FRAME_ENHANCER = "feature_is_support_olive_cover_frame_enhancer"

            /**
             * 是否支持olive编辑后保存还是OLive
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SAVE_OLIVE = "feature_is_support_save_olive"

            /**
             * 是否支持LUMO凝光影像水印
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LUMO_WATERMARK = "feature_is_support_lumo_watermark"

            /**
             * 是否支持将SDR的VIDEO选帧后，支持ProXDR效果
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_SDR_TO_HDR = "feature_is_support_olive_sdr_to_hdr"

            /**
             * 是否支持 AI调色
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_FILTER = "feature_is_support_ai_filter"

            /**
             * 是否支持智能消除
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_ELIMINATE = "feature_is_support_ai_eliminate"

            /**
             * 设备是否支持智能消除，设备支持并不代表功能就能用，还需要经过排队报名通过后功能才开放
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE = "feature_is_device_support_ai_eliminate"

            /**
             * 是否支持路人消除的推荐
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PASSERBY_RECOMMEND = "feature_is_support_passerby_recommend"

            /**
             * 是否支持AI去模糊推荐
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_DEBLUR_RECOMMEND = "feature_is_support_deblur_recommend"

            /**
             * 是否支持AI去反光推荐
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_DEREFLECTION_RECOMMEND = "feature_is_support_dereflection_recommend"

            /**
             * 智能消除是否支持交互式分割
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_INTERACTIVE_SEGMENT_SUPPORT = "is_interactive_segment_support"

            /**
             * 是否支持realme去模糊算法
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_RM_AI_DEBLUE = "feature_is_support_rm_ai_deblur"

            /**
             * 是否支持AI去模糊
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_DEBLUE = "feature_is_support_ai_deblur"

            /**
             * 是否支持AI去反光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_DEREFLECTION = "feature_is_support_ai_dereflection"

            /**
             * 是否支持智能功能推荐
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_INTELLI_FUNC_RECOMMEND = "feature_is_support_intelli_func_recommend"

            /**
             * 是否支持画质增强
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE = "feature_is_support_image_quality_enhance"

            /**
             * 是否支持最佳表情优化
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_BEST_TAKE = "feature_is_support_ai_best_take"

            /**
             * 是否是功能解密的机器，目前只有konka和yala为true，其余为false
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_FUNC_DECRYPT_DEVICE = "feature_is_func_decrypt"

            /**
             * 是否支持画质增强推荐？（设备支持+aiunit支持+插件下载ok等判定; 用于大图推荐入口展示)
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE_RECOMMEND = "feature_is_support_image_quality_enhance_recommend"

            /**
             * 是否支持AI表情优化推荐（aiunit支持+插件下载ok+权限ok+有扫描结果且结果ok等判定；用于大图推荐入口显示）
             * */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_BEST_TAKE_RECOMMEND = "feature_is_support_ai_best_take_recommend"

            /**
             * 是否支持AI补光推荐
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_LIGHTING_RECOMMEND = "feature_is_support_ai_lighting_recommend"

            /**
             * 是否支持缺陷推荐
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_FLAW_RECOMMEND = "feature_is_support_flaw_recommend"

            /**
             * 是否支持提高编解码器的速率
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_USE_OPERATE_RATE = "is_support_use_operate_rate"

            /**
             * 是否支持ocs鉴权
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OCS_AUTH = "feature_is_support_ocs_auth"

            /**
             * 是否支持Edr使用新的背光通路
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_EDR_LISTENER = "feature_is_support_edr_listener"

            /**
             * 是否启用新的时间戳解析算法
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_NEW_DATE_TAKEN_PARSE_ENABLED = "feature_is_new_date_taken_parse_enabled"

            /**
             * 合影1.5 判断AIUnit是否支持闭眼修复
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_EXPRESSION_OPT = "feature_is_support_expression_opt"

            /**
             * 设备是否支持AI闭眼修复，设备支持并不代表功能就能用，还需要经过下载通过后功能才开放
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val FEATURE_IS_DEVICE_SUPPORT_AI_EXPRESSION_OPT = "feature_is_device_support_ai_expression_opt"

            /**
             * 是否为支持进入大图做增强提亮
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_OPEN_IMAGE_BRIGHTNESS_ENHANCE = "feature_is_open_image_brightness_enhance"

            /**
             * 是否支持从系统设置搜索页搜索
             */
            @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LNS_STICKER = "feature_is_support_lns_sticker"

            /**
             * 智能编辑推荐计数
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
            const val FEATURE_INTELLIGENT_EDITING_RECOMMENDATION_COUNT = "feature_intelligent_editing_recommendation_count"

            /**
             * 用户主动关闭引导页的时间
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
            const val CLOSE_GUIDE_TIME = "feature_close_guide_time"

            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SYSTEM_SETTING_SEARCH = "feature_is_support_system_setting_search"

            /**
             * 是否支持系统设置显示插入项
             * */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SYSTEM_SETTINGS_DISPLAY_INSERTION = "feature_is_support_system_settings_display_insertion"

            /**
             * 是否支持显示安全分享
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_SHOW_SECURITY_SHARE = "feature_is_support_show_security_share"

            /**
             * 是否为支持25M编辑直出
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_25M_LEVEL = "feature_is_open_edit_25M_level"

            /**
             * 是否为 DX4 平台
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_DX4_PLATFORM = "feature_is_dx4_platform"

            /**
             * 是否支持相机生的jpeg的exif中缩略图
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_CAMERA_JPEG_EXIF_THUMBNAIL = "feature_is_support_camera_jpeg_exif_thumbnail"

            /**
             * 是否支持相机生的jpeg的exif中缩略图 值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_CAMERA_JPEG_EXIF_THUMBNAIL_SUPPORTED = "is_camera_jpeg_exif_thumbnail_supported"

            /**
             * 相机是否支持proxdr提亮
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER, StorageType.SP], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_CAMERA_PROXDR = "feature_is_support_camera_proxdr"

            /**
             * 相机是否支持proxdr提亮 值在项目一开始就确定了，为降低读取频率，放到sp中，不允许业务直接使用，值可能是不对的
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_CAMERA_PROXDR_SUPPORTED = "is_camera_proxdr_supported"

            /**
             * 是否为支持点阵动效feature
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_LNS_DOT_MATRIX_EFFECT = "feature_is_support_lns_dot_matrix_effect"

            /**
             * 是否支持项目化保存
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PROJECT_SAVE = "feature_is_support_project_save"

            /**
             * 是否支持ipu滤镜
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_IPU_FILTER = "feature_is_support_ipu_filter"

            /**
             * 是否支持ipu美颜
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_IPU_BEAUTY = "feature_is_support_ipu_beauty"

            /**
             * 是否支持三通道UHDR
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_YUV_UHDR = "feature_is_support_yuv_uhdr"

            /**
             * 是否支持登机牌扫描并加入Google Wallet
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_GOOGLE_PASS_SCAN = "feature_is_support_google_pass_scan"

            /**
             * 是否支持 AI 构图
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_COMPOSITION = "feature_is_support_ai_composition"

            /**
             * 是否支持 AI 补光
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AI_LIGHTING = "feature_is_support_ai_lighting"

            /**
             * 是否支持一碰分享
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE = "feature_is_support_one_touch_share"

            /**
             * 是否启用 大图菜单延后加载
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_ENABLE_MENU_INFLATE_POSTPONE = "feature_is_enable_menu_inflate_postpone"

            /**
             * 是否支持账号核身校验（二次校验）
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_AC_VERIFY = "feature_is_support_ac_verify"

            /**
             * 是否支持google lens
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_GOOGLE_LENS = "feature_is_support_google_lens"

            /**
             * 是否支持最近删除加密
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_RECYCLE_ALBUM_PASSWD = "feature_is_support_recycle_album_passwd"

            /**
             * AI 最佳表情是否保密，产品发布后删除
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_AI_BEST_TAKE_IS_SECRET = "feature_ai_best_take_is_secret"

            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_LOW_PERFORMANCE_DEVICE = "feature_is_low_performance_device"

            /**
             * 系统是否支持120fps视频播放
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK = "feature_is_system_support_120_fps_playback"

            /**
             * 是否支持实况导出超清帧
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OLIVE_EXTRACT_UHDR_FRAME = "feature_is_support_olive_extract_uhdr_frame"

            /**
             * 是否支持视频导出Olive
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_VIDEO_EXTRACT_OLIVE = "feature_is_support_video_extract_olive"

            /**
             * 系统是否支持视频壁纸
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_VIDEO_WALLPAPER = "feature_is_support_video_wallpaper"

            /**
             * OS16.0系统之后的版本是否支持人像景深
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PORTRAIT_BLUR_AFTER_OS16 = "feature_is_support_portrait_blur_after_os16"

            /**
             * OS16.0系统之前的版本是否支持人像景深
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PORTRAIT_BLUR_BEFORE_OS16 = "feature_is_support_portrait_blur_before_os16"

            /**
             * 是否支持主题分类
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_THEME_CLASSIFY = "feature_is_support_theme_classify"

            /**
             * 是否支持学习图集
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_STUDY_CLASSIFY = "feature_is_support_study_classify"

            /**
             * 是否支持旅行图集
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_TRAVEL_CLASSIFY = "feature_is_support_travel_classify"

            /**
             * 是否支持健康分类
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_HEALTH_CLASSIFY = "feature_is_support_health_classify"

            /**
             * 是否支持旅程封面优选
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_TRAVEL_COVER_SELECTION = "feature_is_support_travel_cover_selection"

            /**
             * 是否支持宠物分类
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_PET_CLASSIFY = "feature_is_support_pet_classify"

            /**
             * Pre Transition动画是否被手动关掉
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_PRE_TRANSITION_DISABLED = "feature_is_pre_transition_disabled"

            /**
             * 是否支持超规格4k
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
            const val FEATURE_IS_SUPPORT_OVER_4K_VIDEO = "feature_is_support_over_4k_video"
        }

        /**
         * 编辑功能特定配置
         */
        @NodeGroup(id = "editor")
        class Editor {
            /**
             * 图片编辑
             */
            @NodeGroup(id = "photoEditor")
            object PhotoEditor {
                /**
                 * 贴纸STICKER URL配置
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = STRING)
                const val STICKER_URL = "sticker_url"

                /**
                 * 获取Ai超清 url
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = STRING)
                const val EDITOR_AIHD_URL = "feature_aihd_url"

                /**
                 * 自动打码模型是否支持
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_AUTO_MOSAIC = "is_support_auto_mosaic"

                /**
                 * 自动打码模型是否需要下载
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_AUTO_MOSAIC_MODEL = "is_need_download_auto_mosaic_model"

                /**
                 * 自动打码模型是否需要更新
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_UPDATE_AUTO_MOSAIC_MODEL = "is_need_update_auto_mosaic_model"

                /**
                 * 自动打码模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AUTO_MOSAIC_MODEL_SHOW_TIMESTAMP = "update_auto_mosaic_model_show_timestamp"

                /**
                 * AI 最佳表情是否需要下载
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_AI_BEST_TAKE_MODEL = "is_need_download_ai_best_take_model"

                /**
                 * AI 最佳表情是否需要更新
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_UPDATE_AI_BEST_TAKE_MODEL = "is_need_update_ai_best_take_model"

                /**
                 * AI 最佳表情模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_BEST_TAKE_MODEL_SHOW_TIMESTAMP = "update_ai_best_take_model_show_timestamp"

                /**
                 * AI 最佳表情插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_BEST_TAKE_DOWNLOAD_STATE = "ai_best_take_download_state"

                /**
                 * 贴纸功能中数据库版本低于3的本地贴纸文件是否已经被删除清空
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val NEED_DELETE_STICKER_FILE_LOWER_THAN_VERSION3 = "need_delete_sticker_file_lower_than_version3"

                /**
                 * 判断敏感图接口超时时长
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val MOSAIC_DETECT_SENSITIVE_TIMEOUT = "mosaic_detect_sensitive_timeout"

                /**
                 * 获取敏感区域接口超时时长
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val MOSAIC_PARSE_SENSITIVE_REGION_TIMEOUT = "mosaic_parse_sensitive_region_timeout"

                /**
                 * 首次使用限定水印功能，用于限定水印tag
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_RESTRICT_WATERMARK_TAG = "first_using_restrict_watermark_tag"

                /**
                 * 首次使用色彩水印功能
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SP], dataType = BOOL)
                const val FIRST_USING_COLOR_WATERMARK = "first_using_color_watermark"

                /**
                 * 首次使用边框编辑
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SP], dataType = BOOL)
                const val FIRST_USING_BORDER = "first_using_border"

                /**
                 * 美颜so本地版本号
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val BEAUTY_COMPONENT_VERSION = "beauty_component_version"

                /**
                 * 记录 美颜模型的  更新弹窗的显示时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val BEAUTY_UPGRADE_DIALOG_SHOW_TIME = "beauty_upgrade_dialog_show_time"

                /**
                 * 消除笔SDK本地版本号（两位数字组成，eg:10，由相册后台决定）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val ELIMINATE_PEN_LOCAL_COMPONENT_VERSION = "eliminate_pen_component_version_v2"

                /**
                 * 记录 消除笔模型的  版本和忽略次数，格式: 10_1 ， 11_3
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val ELIMINATE_PEN_VERSION_IGNORE_COUNT = "eliminate_pen_component_refuse_count_v2"

                /**
                 * 消除笔so文件的保存路径
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val ELIMINATE_PEN_COMPONENT_SAVE_PATH = "eliminate_pen_component_current_component_dir_v2"

                /**
                 * 水印编辑记忆上一次输入并保存的自定义信息
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val WATERMARK_CUSTOM_INFO_KEY = "watermark_custom_info_key"

                /**
                 * 水印编辑记忆上一次输入并保存的自定义信息(隐私水印)
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val PRIVACY_WATERMARK_CUSTOM_INFO_KEY = "privacy_watermark_custom_info_key"

                /**
                 * 图片编辑后的保存方式
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val IMAGE_EDITOR_SAVE_STATUS = "image_editor_save_status"

                /**
                 * 首次使用智能消除功能介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_AI_ELIMINATE_INTRODUCTION = "first_using_ai_eliminate_introduction"

                /**
                 * 首次使用路人消除功能介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_PASSERBY_ELIMINATE_INTRODUCTION = "first_using_passerby_eliminate_introduction"


                /**
                 * 智能编辑推荐插件的大小
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val INTELLIGENT_PLUGIN_SIZE = "intelligent_plugin_size"

                /**
                 * 是否使用过一键消除
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_ELIMINATE_ALL_PASSERBY_USED = "is_eliminate_all_passerby_used"

                /**
                 * 路人消除的路人点选消除引导是否已经使用过
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_PASSERBY_ELIMINATE_TIPS_USED = "is_passerby_eliminate_tips_used"

                /**
                 * 最佳表情功能是否已经使用过
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_AI_BEST_TAKE_USED = "is_ai_best_take_used"

                /**
                 * 消除拒识的次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val ELIMINATE_REFUSE_COUNT = "eliminate_refuse_count"

                /**
                 * 记录拒识的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val ELIMINATE_REFUSE_TIME = "eliminate_refuse_time"

                /**
                 * 消除拒识封禁时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val ELIMINATE_BANNED_TIME = "eliminate_banned_count"

                /**
                 * 智能消除补全的能力状态，包含下线、低内存、省电模式等不可用的状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_ELIMINATE_INPAINTING_STATE = "ai_eliminate_inpainting_state"

                /**
                 * 智能消除检测的能力状态，包含下线、低内存、省电模式等不可用的状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_ELIMINATE_DETECT_STATE = "ai_eliminate_detect_state"

                /**
                 * 路人消除检测的能力状态，包含下线、低内存、省电模式等不可用的状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val PASSERBY_ELIMINATE_DETECT_STATE = "passerby_eliminate_detect_state"

                /**
                 * 智能消除分割算法是否支持
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_AI_ELIMINATE_SEGMENTATION = "is_support_ai_eliminate_segmentation"

                /**
                 * 智能消除补全算法是否支持
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_AI_ELIMINATE_INPAINTING = "is_support_ai_eliminate_inpainting"

                /**
                 * 智能消除路人消除是否支持
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_PASSERS_ELIMINATE = "is_support_passers_eliminate"

                /**
                 * 是否支持隐性水印
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_AI_IMPLICIT_WATERMARK = "is_support_ai_emplicit_watermark"

                /**
                 * 是否支持补光
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_AI_LIGHTING = "is_support_ai_lighting"

                /**
                 * 是否需要下载智能消除的插件
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_AI_ELIMINATE = "is_need_download_ai_eliminate"

                /**
                 * 是否需要下载Ai补光插件
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_AI_LIGHTING = "is_need_download_ai_lighting"

                /**
                 * 是否需要下载路人消除的插件
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_PASSERBY_ELIMINATE = "is_need_download_passerby_eliminate"

                /**
                 * 智能消除路人消除模型是否需要更新
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_UPDATE_PASSERS_ELIMINATE = "is_need_update_passers_eliminate"


                /**
                 * 智能消除模型是否需要更新
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_UPDATE_AI_ELIMINATE = "is_need_update_ai_eliminate"

                /**
                 * 智能消除路人消除模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_PASSERS_ELIMINATE_SHOW_TIMESTAMP = "update_passers_eliminate_show_timestamp"

                /**
                 * 贴纸使用数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = INT)
                const val STICKER_QUANTITY_LIMIT = "sticker_quantity_limit"

                /**
                 * AIUnit隐私声明是否同意
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_AGREE_AI_UNIT_PRIVACY = "is_agree_ai_unit_privacy"

                /**
                 * 智能消除是否已授权(登录)
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_AI_ELIMINATE_AUTHORIZED = "is_ai_eliminate_authorized"

                /**
                 * AI修复是否已授权(登录)
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_AI_REPAIR_AUTHORIZED = "is_ai_repair_authorized"

                /**
                 * AIUnit的配置是否可用
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_AI_UNIT_CONFIG_AVAILABLE = "is_aiunit_config_available"

                /**
                 * 首次使用AI去反光功能介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_AI_DEREFLECTION_INTRODUCTION = "first_using_ai_dereflection_introduction"

                /**
                 * 首次使用AI去模糊功能介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_AI_DEBLUR_INTRODUCTION = "first_using_ai_deblur_introduction"

                /**
                 * AI去模糊算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_REPAIR_DE_BLUR_DETECT_STATE = "ai_repair_de_blur_detect_state"

                /**
                 * AI构图算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_COMPOSITION_DETECT_STATE = "ai_composition_detect_state"

                /**
                 * AI补光算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_LIGHTING_DETECT_STATE = "ai_lighting_detect_state"

                /**
                 * AI去模糊插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_DE_BLUR_DOWNLOAD_STATE = "ai_de_blur_download_state"

                /**
                 * AI构图插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_COMPOSITION_DOWNLOAD_STATE = "ai_composition_download_state"

                /**
                 * AI补光插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_LIGHTING_DOWNLOAD_STATE = "ai_lighting_download_state"

                /**
                 * 补光算法模型名称
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY], dataType = STRING)
                const val DETECTOR_NAME_KEY_AI_LIGHTING = "cloud_fill_light"

                /**
                 * 补光算法模型插件下载名称
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY], dataType = STRING)
                const val DETECTOR_DOWN_NAME_KEY_AI_LIGHTING = "edge_cloud_fill_light"

                /**
                 * realme AI去模糊插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val RM_AI_DE_BLUR_DOWNLOAD_STATE = "rm_ai_de_blur_download_state"

                /**
                 * AI修复去反光算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_REPAIR_DE_REFLECTION_DETECT_STATE = "ai_repair_de_reflection_detect_state"

                /**
                 * AI去反光插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_DEREFLECTION_DOWNLOAD_STATE = "ai_dereflection_download_state"

                /**
                 * AI涂鸦插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_GRAFFITI_DOWNLOAD_STATE = "ai_graffiti_download_state"

                /**
                 * 缺陷识别算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val FLAW_RECOGNIZE_DETECT_STATE = "flaw_recognize_detect_state"

                /**
                 * 缺陷识别插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val FLAW_RECOGNIZED_DOWNLOAD_STATE = "flaw_recognize_download_state"

                /**
                 * AI去模糊模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_DEBLUR_SHOW_TIMESTAMP = "update_ai_deblur_show_timestamp"

                /**
                 * AI构图模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_COMPOSITION_SHOW_TIMESTAMP = "update_ai_composition_show_timestamp"

                /**
                 * AI补光模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_LIGHTING_SHOW_TIMESTAMP = "update_ai_lighting_show_timestamp"

                /**
                 * AI构图拒识的次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_COMPOSITION_REFUSE_COUNT = "ai_composition_refuse_count"

                /**
                 * AI构图记录拒识的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_COMPOSITION_REFUSE_TIME = "ai_composition_refuse_time"

                /**
                 * AI构图拒识封禁时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_COMPOSITION_BANNED_TIME = "ai_composition_banned_time"

                /**
                 * realme AI去模糊模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_RM_AI_DEBLUR_SHOW_TIMESTAMP = "update_rm_ai_deblur_show_timestamp"

                /**
                 * AI去反光模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_DEREFLECTION_SHOW_TIMESTAMP = "update_ai_dereflection_show_timestamp"

                /**
                 * AI涂鸦模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_GRAFFITI_SHOW_TIMESTAMP = "update_ai_graffiti_show_timestamp"

                /**
                 * 缺陷检测更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_FLAW_RECOGNIZE_SHOW_TIMESTAMP = "update_flaw_recognize_show_timestamp"

                /**
                 * 是否需要下载智能消除的插件
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_AI_GROUP_PHOTO = "is_need_download_ai_group_photo"

                /**
                 * AI闭眼修复的能力状态，包含下线、低内存、省电模式等不可用的状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_GROUP_PHOTO_DETECT_STATE = "ai_group_photo_detect_state"

                /**
                 * AI修复拒识的次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_REPAIR_REFUSE_COUNT = "ai_repair_refuse_count"

                /**
                 * 记录AI修复拒识的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_REPAIR_REFUSE_TIME = "ai_repair_refuse_time"

                /**
                 * AI修复拒识封禁时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_REPAIR_BANNED_TIME = "ai_repair_banned_time"

                /**
                 * 首次使用AI画质增强的
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_IMAGE_QUALITY_ENHANCE = "first_using_image_quality_enhance"

                /**
                 * AI画质增强是否已授权(登录)
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_IMAGE_QUALITY_ENHANCE_AUTHORIZED = "is_image_quality_enhance_authorized"

                /**
                 * AI画质增强算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val IMAGE_QUALITY_ENHANCE_DETECT_STATE = "image_quality_enhance_detect_state"

                /**
                 * AiUnit的AI画质增强插件是否需要下载？
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_IMAGE_QUALITY_ENHANCE = "is_need_download_image_quality_enhance"

                /**
                 * AiUnit的AI构图插件是否需要下载？
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_DOWNLOAD_IMAGE_COMPOSITION = "is_need_download_image_composition"

                /**
                 * AiUnit的AI构图插件是否需要更新？
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_UPDATE_IMAGE_COMPOSITION = "is_need_update_image_composition"

                /**
                 * AiUnit的AI画质增强插件是否需要更新？
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_NEED_UPDATE_IMAGE_QUALITY_ENHANCE = "is_need_update_image_quality_enhance"

                /**
                 * 画质增强模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_IMAGE_QUALITY_ENHANCE_SHOW_TIMESTAMP = "update_image_quality_enhance_show_timestamp"

                /**
                 * 画质增强拒识的次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val IMAGE_QUALITY_ENHANCE_REFUSE_COUNT = "image_quality_enhance_refuse_count"

                /**
                 * 画质增强记录拒识的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val IMAGE_QUALITY_ENHANCE_REFUSE_TIME = "image_quality_enhance_refuse_time"

                /**
                 * 画质增强拒识封禁时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val IMAGE_QUALITY_ENHANCE_BANNED_TIME = "image_quality_enhance_banned_time"

                /**
                 * 缺陷识别模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_INTELLI_FUNC_SHOW_TIMESTAMP = "update_intelli_func_show_timestamp"

                /**
                 * 是否点击了不再提醒
                 * 此处给 实况照片在编辑一级页面点击不支持的编辑项时丢失实况效果的弹窗 使用
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val NO_MORE_REMIND_WITH_LOSE_OLIVE_DIALOG_ENTER_SUB_EDIT = "no_more_remind_with_lose_olive_dialog_enter_sub_edit"

                /**
                 * 是否点击了不再提醒
                 * 此处给 实况照片在编辑一级页面点击不支持的编辑项时，同时丢失实况和proxdr效果的弹窗 使用
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val NO_MORE_REMIND_WHEN_LOST_OLIVE_HDR_DIALOG_ENTER_SUB_EDIT =
                    "no_more_remind_when_edit_olive_group_photo_enter_sub_edit"

                /**
                 * 记录上一次语言
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY], dataType = STRING)
                const val LAST_LANGUAGE = "last_language"

                /**
                 * 首次使用最近删除隐私密码红点提示
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_PRIVACY_PASSWORD_RED_DOT = "first_using_privacy_password_red_dot"

                /**
                 * 首次使用最近删除隐私密码头部提示
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_PRIVACY_PASSWORD_HEAD = "first_using_privacy_password_head"

                /**
                 * 使用AI 超清授权功能体验说明
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val USE_AI_HD_FUNCTIONAL_EXPERIENCE = "use_ai_hd_functional_experience"

                /**
                 * AI 最佳表情的检测状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_BEST_TAKE_DETECT_STATE = "ai_best_take_detect_state"

                /**
                 * 最佳表情拒识的次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_BEST_TAKE_REFUSE_COUNT = "ai_best_take_refuse_count"

                /**
                 * 最佳表情记录拒识的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_BEST_TAKE_REFUSE_TIME = "ai_best_take_refuse_time"

                /**
                 * 最佳表情拒识封禁时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_BEST_TAKE_BANNED_TIME = "ai_best_take_banned_time"

                /*
                 * 记录限定水印元数据预下载时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val RESTRICT_WATERMARK_METADATA_DOWNLOAD_TIME = "restrict_watermark_metadata_download_time"

                /**
                 * Texture能支持的最大size
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val LOCAL_GL_MAX_TEXTURE_SIZE = "local_gl_max_texture_size"

                /**
                 * Ai补光记录拒识的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_LIGHTING_REFUSE_TIME = "ai_lighting_refuse_time"

                /**
                 * Ai补光拒识的次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_LIGHTING_REFUSE_COUNT = "ai_lighting_refuse_count"

                /**
                 * Ai补光拒识封禁时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_LIGHTING_BANNED_TIME = "ai_lighting_banned_time"

                /**
                 *  * AI去眩光算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_EDIT_PICTURE_DE_GLARE_STATE = "ai_edit_picture_de_glare_state"

                /**
                 * AI去眩光插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_DEGLARE_DOWNLOAD_STATE = "ai_deglare_download_state"

                /**
                 * AI去眩光模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_DEGLARE_SHOW_TIMESTAMP = "update_ai_deglare_show_timestamp"

                /**
                 * AI去雾算法的能力状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_EDIT_PICTURE_DE_FOG_MONGO_STATE = "ai_edit_picture_de_fog_mongo_state"

                /**
                 * AI照片编辑拒识的次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_REALME_EDIT_PIC_REFUSE_COUNT = "ai_realme_edit_pic_refuse_count"

                /**
                 * 记录AI照片编辑拒识的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_REALME_EDIT_PIC_REFUSE_TIME = "ai_realme_edit_pic_refuse_time"

                /**
                 * AI照片编辑拒识封禁时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_REALME_EDIT_PIC_BANNED_TIME = "ai_realme_edit_pic_banned_time"

                /**
                 * 首次使用AI去眩光功能介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_AI_DEGLARE_INTRODUCTION = "first_using_ai_deglare_introduction"

                /**
                 * 首次使用AI去雾功能介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_AI_DEFOG_INTRODUCTION = "first_using_ai_defog_introduction"
            }

            /**
             * 视频编辑
             */
            @NodeGroup(id = "videoEditor")
            object VideoEditor {
                /**
                 * 视频编辑URL配置
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = STRING)
                const val VIDEO_EDITOR_URL = "video_editor_url"
            }

            /**
             * 拼图
             */
            @NodeGroup(id = "collage")
            object Collage {
                // 先占坑，后续业务补充
            }

            /**
             * 即录
             */
            @NodeGroup(id = "soloop")
            object Soloop {
                // 先占坑，后续业务补充
            }

            /**
             * 超级文本功能
             */
            @NodeGroup(id = "supertext")
            object SuperText {
                /**
                 * 获取是否支持超级文本3.0，通过应用扫一扫的状态判断
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
                const val GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED = "get_super_text_v3_jump_ocr_scanner_supported"

                /**
                 * 获取是否支持超级文本2.0，通过feature开关和sdk联合判断，比较耗时，不推荐在业务方直接调用
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
                const val GET_SUPER_TEXT_V2_SUPPORTED_SUSPEND = "get_super_text_v2_supported_suspend"

                /**
                 * 获取是否支持超级文本1.0 的 Ocr，通过feature开关和sdk联合判断，比较耗时，不推荐在业务方直接调用
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
                const val GET_SUPER_TEXT_V1_OCR_SUPPORTED_SUSPEND = "get_super_text_v1_ocr_supported_suspend"
            }

            /**
             * 全屏翻译
             */
            @NodeGroup(id = "screentranslate")
            object ScreenTranslate {
                /**
                 * 获取是否支持全屏翻译
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
                const val GET_TRANSLATE_SUPPORTED = "get_translate_supported"
            }

            /**
             * 合成GIF
             */
            @NodeGroup(id = "gifSynthesis")
            object GifSynthesis {
                /**
                 * 上次选用的保存的分辨率类型
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SP], dataType = STRING)
                const val LAST_CHOSEN_SAVE_DEFINITION_TYPE = "last_chosen_save_definition_type"
            }

            /**
             * 合影优化
             */
            @NodeGroup(id = "groupPhoto")
            object GroupPhoto {
                /**
                 * 是否使用过合影优化
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_GROUP_PHOTO = "first_using_group_photo"

                /**
                 * 是否使用过合影优化介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_GROUP_PHOTO_INTRODUCE = "first_using_group_photo_introduce"
            }

            /**
             * AI超清
             */
            @NodeGroup(id = "aihd")
            object Aihd {
                /**
                 * 是否使用过ai超清
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_AIHD = "first_using_aihd"

                /**
                 * 是否使用过ai超清介绍
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_USING_AIHD_INTRODUCE = "first_using_aihd_introduce"
            }

            /**
             * olive编辑
             */
            @NodeGroup(id = "oliveEdit")
            object OliveEdit {
                /**
                 * olive封面插件本地版本
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val OLIVE_COVER_PROXDR_COMPONENT_VERSION = "olive_cover_proxdr_component_version"

                /**
                 * olive封面proxdr插件 数据下载是否同意
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val OLIVE_COVER_PROXDR_PLUGINS_NETWORK_INSTALL_AGREED = "olive_cover_proxdr_plugins_network_install_agreed"

                /**
                 * olive封面proxdr插件 默认下载是否同意
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val OLIVE_COVER_PROXDR_PLUGINS_NORMAL_INSTALL_AGREED = "olive_cover_proxdr_plugins_normal_install_agreed"

                /**
                 * olive 封面 so文件的保存路径
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val OLIVE_COVER_PROXDR_COMPONENT_CURRENT_COMPONENT_DIR = "olive_cover_proxdr_component_current_component_dir"

                /**
                 * olive 允许封面插件后台下载 --用户前台触发--和静默更新无关
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val OLIVE_COVER_PROXDR_COMPONENT_ALLOW_BACKGROUND_DOWNLOAD = "olive_cover_proxdr_component_allow_background_download"
            }

            /**
             * AI涂鸦
             */
            @NodeGroup(id = "aigraffiti")
            object AIGraffiti {
                /**
                 * 是否首次使用AI 涂鸦
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_FIRST_USEAI_GRAFFITI_USED_TIME = "is_first_use_ai_graffiti"

                /**
                 * AI 涂鸦使用次数
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val AI_GRAFFITI_USED_TIME = "ai_graffiti_used_time"
            }
        }

        /**
         * 云相册功能特定配置
         */
        @NodeGroup(id = "cloudSync")
        class CloudSync {
            /**
             * 瘦身：优化存储空间
             */
            @NodeGroup(id = "albumSlimming")
            class AlbumSlimming {

                /**
                 * 图片瘦身
                 */
                @NodeGroup(id = "imageSlimming")
                object ImageSlimming {
                    // 先占坑，后续业务补充
                }

                /**
                 * 视频瘦身
                 */
                @NodeGroup(id = "videoSlimming")
                object VideoSlimming {
                    // 先占坑，后续业务补充
                }
            }

            /**
             * 相册云同步
             */
            @NodeGroup(id = "albumSyncing")
            object AlbumSyncing {
                /**
                 * 记录是否需要拉取云端数据
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val NEED_FETCH = "need_fetch"

                /**
                 * 记录上次成功元数据同步的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val LAST_METADATA_SYNC_SUCCESS_TIME = "last_metadata_sync_success_time"

                /**
                 * bug:5318990：自定义同步图集多条配置冲突问题，修复过程清锚点，写标记
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val CLEAR_SYS_VERSION_FOR_CONFIG_INCONSISTENT = "clear_sys_version_for_config_inconsistent"

                /**
                 * 图集数据的版本。版本有升级时，需要清锚点
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val DEFAULT_ALBUM_DATA_VERSION = "default_album_data_version"

                /**
                 * 云服务的本地数据版本号
                 *
                 * 升级这个版本号会重新拉取云端数据
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val CLOUD_DATA_VERSION = "cloud_data_version"

                /**
                 * 手动关闭瘦身置为true,第一次判断全部原图全部下载完成置为false，这个标记只用于控制下载原图是否提示
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val ALLOW_SHOW_ORIGINAL_DOWNLOADING = "allow_show_original_downloading"

                /**
                 * 记录上次云端下发异常数据后上报埋点的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val LAST_TIME_TRACK_SYSVERSION_INVALID = "last_time_track_sys_version_invalid"

                /**
                 * 记录上次本地gid异常数据后上报埋点的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val LAST_TIME_TRACK_SYS_RECORD_ID_INVALID = "last_time_track_sys_record_id_invalid"

                /**
                 * 记录上次的时间检查camera目录开关的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val LAST_TIME_TRACK_CAMERA_DIR_SWITCH_STATE = "last_time_track_camera_dir_switch_state"

                /**
                 * 是否已经显示过同步目录开启引导，用户有未开启的同步目录则进行引导，同设备只显示一次
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val HAD_SHOW_ALBUM_DIR_OPEN_GUIDE = "had_show_album_dir_open_guide"

                /**
                 * 谷歌云同步的token
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_TOKEN = "g_token"

                /**
                 * 谷歌备份引导面板点击【不要备份】或者返回关闭的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val G_CONNECTION_PROMO_NEGATIVE_TIME = "g_connection_promo_negative_time"

                /**
                 * 是否曾经获得过谷歌相册授权
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val G_HAD_CONNECTED = "google_photos_had_connected"

                /**
                 * 谷歌云存储空间不足10%时,记录忽略显示次数和时间，格式:[次数-时间]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_STORAGE_LOW_IGNORE_TIMES = "google_storage_low_ignore_times"

                /**
                 * 谷歌云停用提示出现时,记录忽略显示次数和时间，格式:[次数-时间]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_APP_DISABLED_INFO_TIMES = "google_app_disabled_info_ignore_times"

                /**
                 * 未安装谷歌相册提示,记录忽略显示次数和时间，格式:[次数-时间]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_INSTALL_STATE_INFO_TIMES = "google_install_state_info_ignore_times"

                /**
                 * 谷歌相册版本低提示,记录忽略显示次数和时间，格式:[次数-时间]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_UPDATE_STATE_INFO_TIMES = "google_update_state_info_ignore_times"

                /**
                 * 谷歌相册授权失效提示,记录忽略显示次数和时间，格式:[次数-时间]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_GRANT_PERMISSION_STATE_INFO_TIMES = "google_grant_permission_state_info_ignore_times"

                /**
                 * 谷歌相册账号信息
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_PHOTOS_ACCOUNT_NAME = "google_photos_account_name"

                /**
                 * 记录谷歌相册登录账号发生了改变，用于判断切换账号的顶部提示语的出现时机。
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val G_PHOTOS_ACCOUNT_CHANGED = "google_photos_account_changed"

                /**
                 * 记录用户至少授权过Google一次
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val G_GRANTED_AT_LEASE_ONCE = "google_granted_at_lease_once"

                /**
                 * 云同步打开提示，记录忽略时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val G_CLOUD_SYNC_CLOSED_INFO_IGNORE_TIME = "google_cloud_sync_closed_info_ignore_time"

                /**
                 * 是否开始进行同步，用于标记同步是否被异常退出
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val TRACK_SYNC_START = "track_sync_start"

                /**
                 * trace_id，标记同一批同步事件
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val TRACK_SYNC_TRACE_ID = "track_sync_trace_id"

                /**
                 * 运营位配置数据缓存，Json格式字符串
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val KEY_CLOUD_CONFIG_DATA = "key_cloud_config_data"

                /**
                 * 最近删除页面底部引导文案缓存，字符串
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val KEY_RECYCLE_CONFIG_DATA = "key_recycle_config_data"

                /**
                 * 记录最近删除页面底部引导文案的查询时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val KEY_QUERY_RECYCLE_CONFIG_TIME = "key_query_recycle_config_time"

                /**
                 * 忽略开关引导类运营位的时间，单位 ms
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val KEY_IGNORE_TIME_FOR_SW = "key_ignore_time_for_sw"

                /**
                 * 忽略付费引导类运营位的时间，单位 ms
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val KEY_IGNORE_TIME_FOR_PAY = "key_ignore_time_for_pay"

                /**
                 * 记录运营位配置点击操作的时间点，单位 ms
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val KEY_CLOUD_OPERATION_TIME = "key_cloud_operation_time"

                /**
                 * 运营位配置数据已曝光
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val KEY_CLOUD_CONFIG_DATA_EXPOSED = "key_cloud_config_data_exposed"

                /**
                 * 已显示过的运营位ID，此SP为埋点服务
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val KEY_CLOUD_CONFIG_ID_EXPOSED = "key_cloud_config_id_exposed"

                /**
                 * 云同步打开提示，记录忽略时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val O_CLOUD_SYNC_CLOSED_INFO_IGNORE_TIME = "cloud_sync_closed_info_ignore_time"

                /**
                 * 本地空间不足提示，记录忽略时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val O_NO_LOCAL_SPACE_INFO_WITH_GUIDE_IGNORE_TIME = "no_local_space_info_with_guide_ignore_time"

                /**
                 * 有大量文件待下载
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val HAVE_BULK_CLOUD_FILES_NEED_TO_DOWNLOAD = "have_bulk_cloud_files_need_to_download"

                /**
                 * 端云不一致需要重新执行全量标识
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val NEED_CLEAR_AND_FULL_SYNC = "need_clear_and_full_sync"

                /**
                 * 端云不一致需要修复originaldata为空异常数据
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val NEED_REPAIR_ERROR_DATA = "need_repair_error_data"
            }

            /**
             * 共享相册
             */
            @NodeGroup(id = "sharedAlbums")
            object SharedAlbums {
                // 先占坑，后续业务补充
            }

            /**
             * 分享页
             */
            @NodeGroup(id = "sharePage")
            object SharePage {
                /**
                 * 是否首次进入分享页
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val SHARE_PAGE_AS_FIRST_ENTER = "share_page_as_first_enter"

                /**
                 * 设置开启了分享后删除功能
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val ALLOW_SHARE_DELETE = "allow_share_delete"

                /**
                 * 是否首次显示分享后删除tips弹窗
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val SHARE_DELETE_TIPS_AS_FIRST_SHOW = "share_delete_tips_as_first_show"

                /**
                 * 一碰分享引导弹窗动画是否使用折叠动画(目前仅 Find N3 使用ptc折叠动画,其余均使用默认动画）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
                const val IS_SHARE_GUIDE_USE_FOLD_PTC_NFC_ANIM = "is_use_fold_ptc_nfc_anim"
            }

            /**
             * 云空间
             */
            @NodeGroup(id = "cloudSpace")
            object CloudSpace {
                /**
                 * 记录上次显示云空间不足的时间
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val LAST_SHOW_CLOUD_SPACE_NOT_ENOUGH_TIPS_TIME = "last_show_cloud_space_not_enough_tips_time"

                /**
                 * 用户剩余云空间大小
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AVAILABLE_AMOUNT = "available_Amount"

                /**
                 * 用户云空间信息
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val CLOUD_STORAGE_SPACE_INFO = "cloud_storage_space_info"
            }

            @NodeGroup(id = "albumDelete")
            object AlbumDelete {
                /**
                 * 最近删除图集-删除弹窗 是否第一次显示
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val RECYCLED_DELETE_TIPS_AS_FIRST_SHOW = "recently_delete_tips_as_first_show"

                /**
                 * 【图集、大图页】删除弹窗-是否第一次显示（不包括最近删除图集）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val RECYCLE_TIPS_AS_FIRST_SHOW = "recycle_tips_as_first_show"

                /**
                 * 是否首次更新data_recycle字段的值,
                 * 给外销云场景，升级”最近删除60天新文案“版本使用
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_FIRST_UPDATE_RECYCLE_TIME = "is_first_update_recycle_time"
            }

            /**
             * 全球一体化开启状态和功能范围
             */
            @NodeGroup(id = "oCloudState")
            object OCloudState {
                /**
                 * 是否显示国内完整云同步功能
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val GLOBAL_ENABLED = "global_enabled"

                /**
                 * 是否是手动触发欢太云下载原图（弹窗中点击“下载原始图片”）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val HEYTAP_DOWNLOADING_ORIGIN_MANUALLY = "heytap_downloading_origin_manually"

                /**
                 * 是否是手动触发谷歌云下载原图（弹窗中点击“下载原始图片”）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val GOOGLE_DOWNLOADING_ORIGIN_MANUALLY = "google_downloading_origin_manually"
            }
        }

        /**
         * 搜索功能特定配置
         */
        @NodeGroup(id = "search")
        class Search {
            /**
             * 搜索页面的配置
             */
            @NodeGroup(id = "searchPage")
            object SearchPage {
                // 先占坑，后续业务补充
            }

            /**
             * 搜索能力的配置
             */
            @NodeGroup(id = "searchAbility")
            object SearchAbility {
                /**
                 * 执行融合搜索中子全量同步完成
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val ANDES_SEARCH_SYNC_ALL_COMPLETED = "andes_search_sync_all_completed"

                /**
                 * 融合搜索 Clip 模型和 Ocr Embedding 模型插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val CLIP_AND_TEXT_EMBEDDING_PLUGIN_DOWNLOAD_STATE = "clip_and_text_embedding_plugin_search_download_state"

                /**
                 * 融合搜索 Clip 模型和 Ocr Embedding 模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_CLIP_AND_TEXT_EMBEDDING_PLUGIN_SHOW_TIMESTAMP = "update_clip_and_text_embedding_plugin_show_timestamp"

                /**
                 * caption插件下载状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_CAPTION_PLUGIN_DOWNLOAD_STATE = "ai_caption_plugin_download_state"

                /**
                 * caption模型更新弹窗弹出的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val UPDATE_AI_CAPTION_PLUGIN_SHOW_TIMESTAMP = "update_ai_caption_plugin_show_timestamp"

                /**
                 * 第一次打开相册搜索框标记
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_FIRST_ANDES_SEARCH = "is_first_andes_search"
            }
        }

        /**
         * 开放能力特定配置
         */
        @NodeGroup(id = "openCapacity")
        class OpenCapacity {
            // 先占坑，后续业务补充
        }

        /**
         * 推送功能特定配置
         */
        @NodeGroup(id = "pushing")
        object Pushing {
            // 先占坑，后续业务补充
        }

        /**
         * 资源特定配置
         */
        @NodeGroup(id = "resourcing")
        object Resourcing {
            // 先占坑，后续业务补充
        }

        /**
         * 互联功能特定配置
         */
        @NodeGroup(id = "synergy")
        object Synergy {
            // 先占坑，后续业务补充
        }

        /**
         * 提亮功能配置
         */
        @NodeGroup(id = "brightness")
        object Brightness {
            /**
             * 提亮功能下，视频触发防烧屏功能的时间周期
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = LONG)
            const val VIDEO_SCREEN_BURN_IN_WARN_DURATION = "video_screen_burn_in_warn_duration"

            /**
             * 提亮功能下，图片触发防烧屏功能的时间周期
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = LONG)
            const val IMAGE_SCREEN_BURN_IN_WARN_DURATION = "image_screen_burn_in_warn_duration"
        }

        /**
         * 编解码功能特定配置
         */
        @NodeGroup(id = "codec")
        object Codec {

            /**
             * 是否是单解码器的设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SINGLE_CODEC_DEVICE = "is_single_codec_device"

            /**
             * 是否是只支持单个4k杜比视频解码的设备,有些设备解多个4k杜比视频会造成内存占用过大,
             * 造成卡顿和闪退,需要通过这个变量来限制4k杜比视频的解码实例个数
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SINGLE_4K_DOLBY_CODEC_DEVICE = "is_single_4k_dolby_codec_device"

            /**
             * 是否限制单个杜比解码器的设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SINGLE_DOLBY_CODEC_DEVICE = "single_dolby_codec_devices"

            /**
             * 是否是单4k解码器的设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_SINGLE_4K_CODEC_DEVICE = "is_single_4k_codec_device"

            /**
             * 是否支持杜比编码设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_DOLBY_ENCODE_DEVICE = "is_dolby_encode_device"

            /**
             * 是否支持杜比解码设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_DOLBY_DECODE_DEVICE = "is_dolby_decode_device"

            /**
             * 是否支持杜比GPU解码方案设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
            const val IS_DOLBY_GPU_DECODE_DEVICE = "is_dolby_gpu_decoder_device"


            /**
             * 支持哈苏水印的设备
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = OBJECT)
            const val CLOUD_HASSEL_DEVICES = "cloud_hassel_devices"

            /**
             * 是否可以使用图片区域解码
             *
             * 区域解码有更好的性能，但是稳定性太差了，多媒体那边迟迟无法改善，因此默认禁用区域解码
             *
             * 目前支持在线配置，后续多媒体修复问题后，把区域解码放开，mark by wuhengze
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_ENABLE_IMAGE_REGION_DECODE = "is_enable_image_region_decode"

            /**
             * 平台编解码能力查询。编解码能力作为平台相关属性，本质上属于一种配置项。需要在此定义。
             * - 需要带参查询。
             * - 对应参数为[com.oplus.gallery.framework.abilities.config.args.ConfigExtraArgs.CodecConfigExtraArgs]
             *
             * ---
             * marked by caiconghu 待修改
             * configID只是一个string，无法携带更多信息。要改就要考虑所有configID都改成结构体对象，内部能定义返回值、params这些信息。
             * 见入口接口评论：[com.oplus.gallery.framework.abilities.config.IConfigAbility.getConfigWithArgs]
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = OBJECT)
            const val PLATFORM_CODEC_CAPABILITY_QUERY = "platform_codec_capability_query"
        }

        /**
         * 色彩管理功能特定配置
         */
        @NodeGroup(id = "colorManagement")
        object ColorManagement {

            /**
             * 自定义色彩管理值
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
            const val IS_WIDE_COLOR_GAMUT_ENABLED = "is_wide_color_gamut_enabled"

            /**
             * 表示Display是否支持色彩管理2.0
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = BOOL)
            const val IS_DISPLAY_SUPPORT_WCG_V2 = "is_display_support_wcg_v2"
        }

        /**
         * 埋点统计功能特定配置
         */
        @NodeGroup(id = "statistics")
        object Statistics {
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
            const val GALLERY_MEDIA_COUNT = "gallery_media_count"
        }

        /**
         * 备份与搬家功能特定配置
         */
        @NodeGroup(id = "backupAndRestore")
        object BackupAndRestore {
            /**
             * 搬家恢复,插入数据到附表
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
            const val INSERT_SCHEDULE_CURRENT_TIME = "insert_schedule_current_time"
        }

        /**
         * 扫描功能特定配置
         */
        @NodeGroup(id = "scanning")
        object Scanning {
            /**
             * 模型下载相关的配置信息
             */
            @NodeGroup(id = "download")
            object Download {
                /**
                 * multi modal current version path
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val MULTI_MODAL_CURRENT_VERSION_PATH = "multi_modal_current_version_path"

                /**
                 * multi modal dict_vulgarisms path
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val MULTI_MODAL_DICT_VULGARISMS_PATH = "multi_modal_dict_vulgarisms_path"

                /**
                 * ocr embedding current version path
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val OCR_EMBEDDING_CURRENT_VERSION_PATH = "ocr_embedding_current_version_path"

                /**
                 * POI 模型当前版本
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val POI_CURRENT_VERSION = "poi_cur_version"

                /**
                 * POI 模型已扫描版本
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val POI_SCANNED_VERSION = "poi_scanned_version"
            }

            /**
             * 多模态的配置信息
             */
            @NodeGroup(id = "multi_modal")
            object MultiModal {
                /**
                 * 多模态能力是否可用
                 * 需要相册环境支持多模态 + 插件部署（扫描时成功初始化插件）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val ABILITY_IS_SUPPORT_MULTIMODAL = "ability_is_support_multimodal"

                /**
                 * 24小时内扫描数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val SCAN_COUNT_24H = "scan_count_24h"

                /**
                 * 是否是第一次扫描
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val FIRST_SCAN = "first_scan"

                /**
                 * 所有扫描数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val SCAN_COUNT_ALL = "scan_count_all"
            }

            /**
             * OCR语义扫描的配置信息
             */
            @NodeGroup(id = "ocr_embedding")
            object OcrEmbedding {
                /**
                 * OcrEmbedding能力是否可用
                 * 需要相册环境支持OcrEmbedding + 插件部署（扫描时成功初始化插件）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val ABILITY_IS_SUPPORT_OCR_EMBEDDING = "ability_is_support_ocr_embedding"

                /**
                 * 24小时内扫描数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val OCR_EMBEDDING_SCAN_COUNT_24H = "ocr_embedding_scan_count_24h"

                /**
                 * 是否是第一次扫描
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val OCR_EMBEDDING_FIRST_SCAN = "ocr_embedding_first_scan"

                /**
                 * 所有扫描数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val OCR_EMBEDDING_SCAN_COUNT_ALL = "ocr_embedding_scan_count_all"
            }

            /**
             * 旅行扫描的配置信息
             */
            @NodeGroup(id = "travel")
            object Travel {
                /**
                 * 24小时内扫描数量（仅旅程生成，不含旅程封面）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val TRAVEL_SCAN_COUNT_24H = "travel_scan_count_24h"

                /**
                 * 旅程数据是否是第一次扫描（仅旅程生成，不含旅程封面）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val TRAVEL_FIRST_SCAN = "travel_first_scan"

                /**
                 * 旅程数据的所有扫描数量（仅旅程生成，不含旅程封面）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val TRAVEL_SCAN_COUNT_ALL = "travel_scan_count_all"

                /**
                 * 设置 - 旅程定位权限开启状态
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val TRAVEL_LOCATE_STATUS = "travel_locate_status"

                /**
                 * 某段旅程开始时的常驻地信息
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val DT_RESIDENCE_DATA = "dt_residence_data"

                /**
                 * 24小时内旅程封面扫描数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val TRAVEL_COVER_SCAN_COUNT_24H = "travel_cover_scan_count_24h"

                /**
                 * 24小时内第一次旅程封面扫描标志
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val TRAVEL_COVER_FIRST_SCAN = "travel_cover_first_scan"

                /**
                 * 旅程封面扫描数量总数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val TRAVEL_COVER_SCAN_COUNT_ALL = "travel_cover_scan_count_all"
            }

            /**
             * 宠物扫描的配置信息
             */
            @NodeGroup(id = "pet")
            object Pet {
                /**
                 * 当前宠物扫描算法版本
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val PET_VERSION = "pet_version"

                /**
                 * 宠物扫描 sdk 算法升级成功标记，用于触发宠物数据的更新扫描。
                 *
                 * 升级成功时，赋值true。
                 *
                 * 当数据更新扫描完成后，赋值为false。
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val PET_SDK_UPDATED = "pet_sdk_updated"
            }

            /**
             * Caption扫描的配置信息
             */
            @NodeGroup(id = "caption")
            object ImageCaption {
                /**
                 * CaptionEmbedding能力是否可用
                 * 需要相册环境支持CaptionEmbedding + 插件部署（扫描时成功初始化插件）
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val ABILITY_IS_SUPPORT_CAPTION_EMBEDDING = "ability_is_support_caption_embedding"

                /**
                 * 24小时内扫描数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val CAPTION_SCAN_COUNT_24H = "caption_scan_count_24h"

                /**
                 * 是否是第一次扫描
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val CAPTION_FIRST_SCAN = "caption_first_scan"

                /**
                 * 所有扫描数量
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val CAPTION_SCAN_COUNT_ALL = "caption_scan_count_all"

                /**
                 * 用户手动取消下载
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val CAPTION_MODEL_USER_CANCEL_DOWNLOAD = "caption_model_user_cancel_download"
            }
        }

        /**
         * 数据层特定配置
         */
        @NodeGroup(id = "dataing")
        object Dataing {
            // 先占坑，后续业务补充
        }

        /**
         * 图集分类特定配置
         */
        @NodeGroup(id = "classification")
        class Classification {
            @NodeGroup(id = "labeling")
            class Labeling {
                /**
                 * 标签扫描功能
                 */
                @NodeGroup(id = "labelScanning")
                object LabelScanning {
                    // 先占坑，后续业务补充
                }

                /**
                 * 标签相册
                 */
                @NodeGroup(id = "labelAlbum")
                object LabelAlbum {
                    // 先占坑，后续业务补充
                }

                /**
                 * 标签相册集
                 */
                @NodeGroup(id = "labelAlbumSet")
                object LabelAlbumSet {
                    // 先占坑，后续业务补充
                }

                /**
                 * 标签页面
                 */
                @NodeGroup(id = "labelPage")
                object LabelPage {
                    // 先占坑，后续业务补充
                }
            }

            @NodeGroup(id = "person")
            class Person {
                @NodeGroup(id = "personPage")
                object PersonPage {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "personScanning")
                object PersonScanning {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "personAlbum")
                object PersonAlbum {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "personAlbumSet")
                object PersonAlbumSet {
                    // 先占坑，后续业务补充
                }
            }

            @NodeGroup(id = "recycle")
            class Recycle {
                @NodeGroup(id = "recycleAlbum")
                object RecycleAlbum {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "recyclePage")
                object RecyclePage {
                    /**
                     * 由于没有通知权限，导致无法通知的应用名。记录下来，在进入回收站时提示用户删除情况和申请授权
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                    const val PACKAGE_LABEL_UN_TIPS = "package_label_un_tips"

                    /**
                     * 请求通知的次数
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                    const val REQUEST_NOTIFICATION_PERMISSION_COUNT = "request_notification_permission_count_for_recycle"
                }
            }

            @NodeGroup(id = "memory")
            class Memory {
                @NodeGroup(id = "memoryPage")
                object MemoryPage {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "memoryScanning")
                object MemoryScanning {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "memoryAlbum")
                object MemoryAlbum {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "memoryAlbumSet")
                object MemoryAlbumSet {
                    // 先占坑，后续业务补充
                }
            }

            @NodeGroup(id = "location")
            class Location {
                @NodeGroup(id = "locationPage")
                object LocationPage {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "locationScanning")
                object LocationScanning {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "locationAlbum")
                object LocationAlbum {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "locationAlbumSet")
                object LocationAlbumSet {
                    // 先占坑，后续业务补充
                }
            }

            /**
             * 随身卡包
             */
            @NodeGroup(id = "cardCase")
            class CardCase {
                @NodeGroup(id = "cardCasePage")
                object CardCasePage {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "cardCaseScanning")
                object CardCaseScanning {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "cardCaseAlbum")
                object CardCaseAlbum {
                    // 先占坑，后续业务补充
                }

                @NodeGroup(id = "cardCaseAlbumSet")
                object CardCaseAlbumSet {
                    // 先占坑，后续业务补充
                }
            }

            /**
             * 私密保险箱
             */
            @NodeGroup(id = "safeBox")
            object SafeBox {
                /**
                 * 私密保险箱是否提示小红点
                 */
                @Node(ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_SHOW_RED_DOT_LOCAL = "is_support_show_red_dot_local"

                /**
                 * 私密保险箱弹框提示（不再提醒）
                 */
                @Node(ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val SAFE_BOX_CONFIRM_NOT_REMIND_AGAIN = "safe_box_confirm_not_remind_again"
            }

            /**
             * 清理建议
             */
            @NodeGroup(id = "photoClean")
            object PhotoClean {
                /**
                 * 清理建议是否提示小红点
                 */
                @Node(ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val PHOTO_CLEAN_IS_SUPPORT_SHOW_RED_DOT_LOCAL = "photo_clean_is_support_show_red_dot_local"
            }

            /**
             * 桌面卡片
             */
            @NodeGroup(id = "launcherWidget")
            object LauncherWidget {
                // 先占坑，后续业务补充
            }

            /**
             * 精选画廊
             * @deprecated
             */
            @NodeGroup(id = "artShow")
            object PickedDay {

                /**
                 * 精选画廊本地配置
                 * @deprecated
                 */
                @Node(ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_PICKED_DAY_LOCAL = "is_support_art_show_local"

                /**
                 * 精选画廊云端配置
                 * @deprecated
                 */
                @Node(ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SUPPORT_PICKED_DAY_CLOUD = "is_support_art_show_cloud"

                /**
                 * 精选画廊支持的API LEVEL列表
                 * @deprecated
                 */
                @Node(ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = STRING)
                const val SUPPORT_PICKED_DAY_API_LEVEL = "support_art_show_api_level"
            }

            /**
             * 收藏
             */
            @NodeGroup(id = "favorite")
            object Favorites {
                // 先占坑，后续业务补充
            }

            /**
             * 闪速选片
             */
            @NodeGroup(id = "flashPicSelect")
            object FlashPicSelect {

                /**
                 * 闪速选片模型插件下载状态
                 * 状态值 @see [com.oplus.gallery.framework.abilities.download.aiunit.AIUnitDownloadState]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val FLASH_PIC_SELECT_PLUGIN_DOWNLOAD_STATE = "flash_pic_select_plugin_search_download_state"

                /**
                 * 闪速选片模型更新的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val FLASH_PIC_SELECT_PLUGIN_SHOW_TIMESTAMP = "flash_pic_select_plugin_show_timestamp"
            }

            /**
             * 宠物识别
             */
            @NodeGroup(id = "aiPets")
            object AiPets {

                /**
                 * 宠物识别模型插件下载状态
                 * 状态值 @see [com.oplus.gallery.framework.abilities.download.aiunit.AIUnitDownloadState]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val AI_PETS_PLUGIN_DOWNLOAD_STATE = "ai_pets_plugin_download_state"

                /**
                 * 宠物识别模型更新的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val AI_PETS_PLUGIN_SHOW_TIMESTAMP = "ai_pets_plugin_show_timestamp"
            }

            @NodeGroup(id = "study")
            object StudyAlbum {
                /**
                 * 学习分类模型插件下载状态
                 * 状态值 @see [com.oplus.gallery.framework.abilities.download.aiunit.AIUnitDownloadState]
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = INT)
                const val STUDY_CLASSIFY_PLUGIN_DOWNLOAD_STATE = "study_classify_plugin_search_download_state"

                /**
                 * 学习分类模型更新的时间戳
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                const val STUDY_CLASSIFY_PLUGIN_SHOW_TIMESTAMP = "study_classfiy_plugin_show_timestamp"
            }
        }

        /**
         * 页面功能特定配置。
         */
        @NodeGroup(id = "pages")
        class Pages {

            /**
             * 通用功能配置
             */
            @NodeGroup(id = "pagesCommon")
            object PagesCommon {

                /**
                 * 大缓存模式级数
                 * 定义：比8G内存大多少倍，则是对应级数
                 * 注意：余数应该落在 6< y <= 8G 才算作多一倍，(使用+2再对结果向下取整)示例如下：
                 * x < 14 level 为 0
                 * 14 <= x < 22 level 为 1
                 * 22 <= x < 30 level 为 2
                 * ...
                 */
                @Node(ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = INT)
                const val BIG_CACHE_MODE_LEVEL = "feature_big_cache_mode_level"
            }

            /**
             * TabPage功能配置
             */
            @NodeGroup(id = "tabPage")
            object TabPage {
                // 先占坑，后续业务补充
            }

            /**
             * 时间轴TabPage功能配置
             */
            @NodeGroup(id = "timelineTabPage")
            object TimelineTabPage {
                // 先占坑，后续业务补充
            }

            /**
             * 图集TabPage功能配置
             */
            @NodeGroup(id = "albumSetTabPage")
            object AlbumSetTabPage {
                /**
                 * 将图集tab中图集列表数据同步到图集表和封面表中的标志位
                 * true:已同步
                 * false:未同步过数据
                 */
                @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                const val IS_SYNC_ALBUM_TO_TABLE = Constants.SP.IS_SYNC_ALBUM_TO_TABLE
            }

            /**
             * 发现TabPage功能配置
             */
            @NodeGroup(id = "explorerTabPage")
            object ExplorerTabPage {
                // 先占坑，后续业务补充
            }

            /**
             * 时间轴页功能配置
             */
            @NodeGroup(id = "timelinePage")
            object TimelinePage {
                // 先占坑，后续业务补充
            }

            /**
             * 图集页功能配置
             */
            @NodeGroup(id = "albumSetPage")
            object AlbumSetPage {
                // 先占坑，后续业务补充
            }

            /**
             * AlbumPage功能配置
             */
            @NodeGroup(id = "albumPage")
            object AlbumPage {
                // 先占坑，后续业务补充
            }

            /**
             * MapPage功能配置
             */
            @NodeGroup(id = "mapPage")
            object MapPage {
                // 先占坑，后续业务补充
            }

            /**
             * PhotoPage功能配置
             */
            @NodeGroup(id = "photoPage")
            class PhotoPage {
                /**
                 * 气泡弹窗
                 */
                @NodeGroup(id = "bubbleWindow")
                object BubbleWindow {
                    /**
                     * 气泡窗口弹出时长
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = LONG)
                    const val TOAST_DURATION = "toast_duration"
                }

                /**
                 * Dialog弹窗
                 */
                @NodeGroup(id = "dialogWindow")
                object DialogWindow {
                    /**
                     * 是否点击不再提醒
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                    const val NO_MORE_REMIND_WHEN_EDIT_LOCAL_HDR = "no_more_remind_when_edit_local_hdr"

                    /**
                     * 是否点击不再提醒
                     * 此处给编辑界面弹窗通用
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                    const val NO_MORE_REMIND_IN_PHOTO_EDIT = "no_more_remind_in_photo_edit"

                    /**
                     * 是否点击不再提醒
                     * 此处是失去实况效果的弹窗
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                    const val NO_MORE_REMIND_WITH_LOSE_OLIVE_DIALOG = "no_more_remind_with_lose_olive_dialog"

                    /**
                     * 是否点击不再提醒
                     * 此处给编辑界面弹窗通用
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                    const val NO_MORE_REMIND_WHEN_EDIT_OLIVE_GROUP_PHOTO = "no_more_remind_when_edit_olive_group_photo"

                    /**
                     * 标记是否使用过智能消除，用于紧急下架
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                    const val AI_ELIMINATE_HAS_USED = "ai_eliminate_has_used"

                    /**
                     * 标记是否显示过下架弹窗，用于紧急下架
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                    const val ELIMINATE_AI_HAS_SHOWN_OFFLINE = "eliminate_ai_has_shown_offline"
                }

                /**
                 * 菜单
                 */
                @NodeGroup(id = "menu")
                object Menu {
                    // 占坑，后续业务按需补充
                }

                /**
                 * 提亮相关，目前包括Local HDR和杜比视频提亮
                 */
                @NodeGroup(id = "brighten")
                object Brighten {
                    /**
                     * 是否适用过Local HDR提亮效果对比
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.SP], dataType = BOOL)
                    const val FIRST_USING_LOCAL_HDR_COMPARE = "first_using_local_hdr_compare"

                    /**
                     * 是否支持调整图片提亮速率
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER, StorageType.MEMORY], dataType = BOOL)
                    const val IS_SUPPORT_ADJUST_IMAGE_EDR_RATE = "is_support_image_edr_rate"

                    /**
                     * 是否支持OLive图片与视频切换时调整提亮速率
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER, StorageType.MEMORY], dataType = BOOL)
                    const val IS_SUPPORT_ADJUST_OLIVE_EDR_RATE = "is_support_olive_edr_rate"
                }

                /**
                 * 手势事件相关
                 */
                @NodeGroup(id = "motionEvent")
                object MotionEvent {
                    /**
                     * 抠图 短长按 时长， 以ms为单位
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER, StorageType.MEMORY], dataType = LONG)
                    const val LNS_SHORT_LONG_PRESS_TIME_OUT_IN_MS = "lns_short_long_press_time_out_in_ms"
                }

                /**
                 * olive
                 */
                @NodeGroup(id = "olive")
                object Olive {
                    /**
                     * 抠图打断之后继续显示抠图的Olive播放时长阈值 时长， 以ms为单位
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER, StorageType.MEMORY], dataType = LONG)
                    const val OLIVE_PLAYED_THRESHOLD_TIME_FOR_LNS_SHOW_IN_MS = "olive_played_threshold_time_for_lns_show_in_ms"

                    /**
                     * olive区间播放最大限制，单位：ms
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER, StorageType.MEMORY], dataType = LONG)
                    const val OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT_IN_MS = "olive_clip_play_position_max_limit_in_ms"
                }

                /**
                 * 动画
                 */
                @NodeGroup(id = "animation")
                object Animation {
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER, StorageType.MEMORY], dataType = BOOL)
                    const val ANIMATION_QUICK_SHORT = "anim_quick_short"

                    /**
                     * 大图过渡动画是否启用render线程动画。V以上默认启用。
                     */
                    @Node(ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.OTHER], dataType = BOOL)
                    const val IS_SUPPORT_TRANSITION_RENDER_ANIM = "is_support_transition_render_anim"
                }

                /**
                 * Tile
                 */
                @NodeGroup(id = "tile")
                object Tile {
                    /**
                     * Tile块最大数量
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.OTHER], dataType = INT)
                    const val DEFAULT_TILE_MAX_COUNT = "default_tile_max_count"
                }

                /**
                 * 相机调相册的解码服务
                 */
                @NodeGroup(id = "preDecodeService")
                object PreDecodeService {
                    /**
                     * 相机访问相册的协议版本号
                     */
                    @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SP], dataType = INT)
                    const val GALLERY_CAMERA_CONFIG_VERSION = "gallery_camera_config_version"
                }
            }
        }

        /**
         * Rus
         */
        @NodeGroup(id = "rus")
        object Rus {
            /**
             * Local Hdr Rus决策条件内容
             */
            @Node(configType = ConfigType.LOCAL, storageConfig = [StorageType.MEMORY, StorageType.SP], dataType = STRING)
            const val LOCAL_HDR_RUS_CONTENT = "local_hdr_rus_content"
        }
    }
}