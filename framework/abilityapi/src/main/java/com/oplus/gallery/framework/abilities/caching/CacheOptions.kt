/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CacheOptions.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.caching

import com.oplus.gallery.framework.annotation.GalleryApp

/**
 * 从缓存中读取资源或将资源放入缓存时的配置参数。其中inXX为业务端的请求参数，outXX为缓存服务回写回业务端的参数或状态
 */
@GalleryApp
data class CacheOptions(
    /**
     * 指定thumbnail的类型，业务端调用时，需要指定图片尺寸。
     * 若存储的是非图像相关资源，如Geo Address信息，业务端传入的inThumbnailType值会被忽略。
     */
    var inThumbnailType: Int,

    /**
     * 从缓存中获取数据，将数据放入缓存等操作时，可选择指定存储类型。
     * 默认为内存和磁盘缓存同时操作。
     */
    val inStorageType: StorageType = StorageType.MEMORY_AND_DISK,

    /**
     * 指定存储的图片资源编码类型，如无损、有损高质量，有损低质量
     * 业务端若不关心存储质量，可直接传NOT_CARE。服务端会内部决定具体使用哪一种类型。
     */
    val inStorageQuality: StorageQuality = StorageQuality.NOT_CARE,

    /**
     * 存储Media相关资源时，指定媒体的时间点，如video、gif等资源某一帧的时间点
     * 默认值为 0
     */
    val inFrameAtTime: Long = 0L,

    /**
     * 模糊半径，默认是 [DEFAULT_BLUR_RADIUS]，代表无效
     *
     * 注意：如果该参数小于等于 0，则直接任务无效，不进行高斯模糊处理
     */
    var inBlurRadius: Float = DEFAULT_BLUR_RADIUS,
) {
    companion object {
        const val DEFAULT_BLUR_RADIUS: Float = 0f
    }
}