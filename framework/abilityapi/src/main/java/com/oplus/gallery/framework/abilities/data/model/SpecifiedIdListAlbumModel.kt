/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SpecifiedIdListAlbumModel.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/11/13
 ** Author: wangrunxin@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                       <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** wangrunxin@Apps.Gallery3D      2020/11/13    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.data.model

import com.oplus.gallery.framework.abilities.data.DataRepository.SpecifiedDataAlbumModelGetter.Companion.TYPE_ID_LIST_ALBUM
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaAlbum
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.internal.IdListAlbum

class SpecifiedIdListAlbumModel(
    path: Path,
    mediaTypeSupport: Int,
    idList: ArrayList<String>,
    orderType: Int,
    isPositiveOrder: Boolean
) : AlbumModel(TYPE_ID_LIST_ALBUM, path) {
    init {
        mediaSet = (DataManager.getMediaObject(path) as? MediaAlbum)?.apply {
            inputEntry = IdListAlbum.IdsInputEntry(mediaTypeSupport, orderType, isPositiveOrder, idList)
        }
    }

    override fun getTag(): String = TAG

    companion object {
        private const val TAG = "SpecifiedIdListAlbumModel"
    }
}