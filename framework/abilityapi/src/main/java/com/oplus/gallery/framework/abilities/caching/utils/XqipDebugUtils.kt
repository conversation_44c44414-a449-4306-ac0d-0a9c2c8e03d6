/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - XqipDebugUtils.kt
 ** Description:  调试用，需求稳定后删除
 **
 ** Version: 1.0
 ** Date: 2024/04/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     yaoweihe       2024/04/22    1.0                create
 ******************************************************************************/
package com.oplus.gallery.framework.abilities.caching.utils

import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties

/**
 * 用于Xqip缓存相关，debug用
 * 后续需求完成后考虑删除
 */
object XqipDebugUtils {
    private const val DEFAULT_BLUR_RADIUS = 0
    private const val DEFAULT_SIZE = 30
    private const val DEFAULT_COLUMN = 20
    private const val DEFAULT_ROW = 4
    // 30M / 2表示Bitmap.Config.ARGB_565
    private const val DEFAULT_THRESHOLD_BLOCK = 30 * 1024 * 1024 / (DEFAULT_COLUMN * DEFAULT_ROW * DEFAULT_SIZE * DEFAULT_SIZE * 2)
    private const val DURATION = 200L
    private const val DEFAULT_THRESHOLD_COUNT = DEFAULT_THRESHOLD_BLOCK * DEFAULT_ROW * DEFAULT_COLUMN

    val enableXqipCache = GallerySystemProperties.getBoolean("debug.album.xqip.enable", true)
    val blurRadius = GallerySystemProperties.getInt("debug.xqip.blur.radius", DEFAULT_BLUR_RADIUS)
    val threshold = GallerySystemProperties.getInt("debug.xqip.threshold", DEFAULT_THRESHOLD_COUNT)

    val width = GallerySystemProperties.getInt("debug.xqip.cache.width", DEFAULT_SIZE)
    // 月
    val columnOfBlock = GallerySystemProperties.getInt("debug.xqip.cache.column", DEFAULT_COLUMN)
    val rowOfBlock = GallerySystemProperties.getInt("debug.xqip.cache.row", DEFAULT_ROW)

    val blockCount = GallerySystemProperties.getInt("debug.xqip.threshold.block", DEFAULT_THRESHOLD_BLOCK)

    val duration = GallerySystemProperties.getLong("debug.xqip.duration", DURATION)
}