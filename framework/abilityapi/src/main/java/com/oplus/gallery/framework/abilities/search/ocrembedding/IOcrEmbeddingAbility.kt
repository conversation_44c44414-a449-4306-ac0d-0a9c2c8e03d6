/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IOcrEmbeddingAbility.kt
 ** Description : Ocr语义扫描接口
 ** Version     : 1.0
 ** Date        : 2024/11/21
 ** Author      : 80377872
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80377872                            2024/11/21     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.search.ocrembedding

typealias IAndesOcrEmbeddingAbility = com.oplus.andes.photos.kit.search.ability.IOcrEmbeddingAbility

/**
 * OcrEmbedding接口类
 */
interface IOcrEmbeddingAbility : AutoCloseable, IAndesOcrEmbeddingAbility {
    /**
     * 当前模型的路径。字符串，例如 2.0.0
     */
    var currentModelVersion: String

    /**
     * 是否在扫描中
     */
    var isScanning: Boolean

    /**
     * 是否在下载模型中
     */
    var isDownloading: Boolean

    /**
     * 初始化引擎
     *
     * @return 是否初始化成功。true成功，false失败
     */
    fun init(): Boolean

    fun processImageText(localMediaTableId: Long, text: String): Boolean

    fun search(keyword: String): List<OcrEmbeddingImageInfo>

    /**
     * 内存数据输出到文件
     * processImage之后、deleteImage之后需要调用此方法
     * @return 成功true，失败false
     */
    fun flushData(): Boolean

    /**
     * 释放
     */
    override fun release()

    /**
     * 删除图片向量
     * @param needDeleteList 需要删除的图片列表
     * @return 成功true，失败false
     */
    fun deleteDataByLocalMediaTableIds(needDeleteList: MutableList<OcrEmbeddingImageInfo>): MutableList<Long>

    /**
     * 是否有新版本
     * @param versionPath 模型路径
     * @return 是true，否false
     */
    fun hasNewVersion(versionPath: String): Boolean

    /**
     * 检查是否具有老的模型的数据
     * @return  true 表示存在， false 表示不存在
     */
    fun hasOldModelData(): Boolean

    /**
     * 清除老模型的数据
     * @return  true 表示执行成功，false 表示执行失败
     */
    fun removeOldModelData(): Boolean

    /**
     * 检查数据是否可用
     * @return true，可用；false，不可用
     */
    override fun checkDataValid(): Boolean

    /**
     * 检查ocr文本是否符合做embedding条件
     */
    fun isAllowEmbedding(text: String): Boolean

    override fun close() = Unit

    companion object {

        /**
         * 当图片经过ocr提取后没有文字或者只有一两个文字，该图片没有太大的语义信息，不应该由ocr语义召回
         * 召回应该由ocr文字识别，标签等其他策略召回，所以设置做OcrEmbedding所需要的最小ocr文字长度，防止ocr语义误召回
         */
        const val MIN_OCR_TEXT_LENGTH = 5
    }
}