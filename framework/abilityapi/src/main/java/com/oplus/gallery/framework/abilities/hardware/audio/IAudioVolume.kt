/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IAudioVolume.kt
 ** Description : 音量控制器的管理类
 ** Version     : 1.0
 ** Date        : 2022/08/05
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/08/05      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.hardware.audio

/**
 * 音量控制器的管理类
 *
 * - 生成AudioController对象并加入到缓存
 * - 移除AudioController对象
 */
interface IAudioVolume : AutoCloseable {

    /**
     * 生成一个[IAudioVolumeController]对象
     * @param forceMuteWhenInit 初始化时是否强制设置为静音状态
     * @param uniqueName 生成[IAudioVolumeController]对象的名称，由业务端决定是否需要传递uniqueName
     * 不传入uniqueName：由音频控制管理类随机创建一个音量控制器，页面销毁后会释放控制器
     * 传入指定的uniqueName：创建一个指定名称的音量控制器，页面销毁后不会释放控制器
     */
    fun createAudioVolumeController(uniqueName: String = NO_SPECIFIED_NAME, forceMuteWhenInit: Boolean): IAudioVolumeController

    /**
     * 销毁一个[IAudioVolumeController]
     * @param uniqueName 要销毁的[IAudioVolumeController]的uniqueName
     */
    fun destroyAudioVolumeController(uniqueName: String)

    /**
     * 依据uniqueName去缓存拿取[IAudioVolumeController]对象
     */
    fun getAudioVolumeController(uniqueName: String): IAudioVolumeController?

    companion object {
        const val NO_SPECIFIED_NAME: String = ""
    }
}