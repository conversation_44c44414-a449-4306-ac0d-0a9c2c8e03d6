/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvEffectUsage
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/4/3 14:17
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/4/3		  1.0		 AvEffectUsage
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.track.usage

import androidx.annotation.WorkerThread
import com.oplus.gallery.framework.abilities.editing.editor.EditorResultState
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect

/**
 * 特效参数用度
 */
interface IAvEffectUsage {
    /**
     * 使用的特效
     */
    val effect: AvEffect

    /**
     * 获取特效参数
     * @param key 获取的特效参数key值
     * @return 获取到的特效参数值
     */
    fun <T> getArg(key: String): T?

    /**
     * 添加参数变化观测，观测某一个键值的变化观测
     * @param key 观测的减值
     * @param observer 键值变化的观测者
     */
    fun addValueObserver(key: String, observer: ValueObserver)

    fun addValueObservers(vararg key: String, observer: ValueObserver)

    /**
     * 删掉对某个减值变化的观测者
     * @param key 观测的减值
     * @param observer 待删除键值变化的观测者
     */
    fun removeValueObserver(key: String, observer: ValueObserver)

    fun removeValueObservers(vararg key: String, observer: ValueObserver)

/**
     * 添加结果监听，回调后自动移除
     * @param listener 结果监听
     */
    fun addResultListener(listener: (EditorResultState) -> Unit)

    /**
     * 移除结果监听
     * @param listener 结果监听
     */
    fun removeResultListener(listener: (EditorResultState) -> Unit)

    /**
     * 移除所有的监听
     */
    fun removeAllResultListener()

    /**
     * 参数变化观测器
     */
    fun interface ValueObserver {
        /**
         * 当被观测的键值发生变化时会触发回调
         *
         * ！！ 数据监测回调 需要再工作线程接收处理，避免影响发送端渲染流程
         *
         * @param key 被观测的键的名称
         * @param value 对应key变化后的值
         */
        @WorkerThread
        fun onValueChanged(key: String, value: Any?)
    }

    /**
     * requestCode 特效或者修改特效的请求码，一次效果执行的唯一标识
     */
    val requestCode: Int
}