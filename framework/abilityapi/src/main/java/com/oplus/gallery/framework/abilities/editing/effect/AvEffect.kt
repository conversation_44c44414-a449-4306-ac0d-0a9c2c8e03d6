/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvEffect
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/1/31 22:13
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/1/31		  1.0		 AvEffect
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.effect

/**
 * 编辑特效
 */
sealed class AvEffect {
    /**
     * 实况照片特效
     */
    object OliveEffect : AvEffect() {
        override val name = OLIVE_EFFECT
        override val forceCaching: Boolean = true
    }

    /**
     * 裁剪旋转特效
     */
    object TransformEffect : AvEffect() { override val name = TRANSFORM_EFFECT }

    /**
     * 调节特效
     */
    object AdjustEffect : AvEffect() { override val name = ADJUST_EFFECT }

    /**
     * 滤镜特效
     */
    object FilterEffect : AvEffect() {
        override val name = FILTER_EFFECT
        override val needCache: Boolean = true
    }

    /**
     * 文字特效
     */
    object TextEffect : AvEffect() { override val name = TEXT_EFFECT }

    /**
     * 水印特效
     */
    object WatermarkEffect : AvEffect() { override val name = WATERMARK_EFFECT }

    /**
     * 马赛克特效
     */
    object MosaicEffect : AvEffect() { override val name = MOSAIC_EFFECT }

    /**
     * 美颜特效
     */
    object BeautyEffect : AvEffect() { override val name = BEAUTY_EFFECT }

    /**
     * AI修复特效
     */
    object AiRepairEffect : AvEffect() { override val name = AI_REPAIR_EFFECT }

    /**
     * 画质增强特效
     */
    object ImageQualityEnhanceEffect : AvEffect() { override val name = IMAGE_QUALITY_ENHANCE_EFFECT }

    /**
     * AI 构图特效
     */
    object AiCompositionEffect : AvEffect() { override val name = AI_COMPOSITION_EFFECT }

    /**
     * AI消除特效
     */
    object AiEliminateEffect : AvEffect() { override val name = AI_ELIMINATE_EFFECT }

    /**
     * AI最佳表情特效
     */
    object AiBestTakeEffect : AvEffect() { override val name = AI_BESTTAKE_EFFECT }

    /**
     * AI功能隐性水印特效
     */
    object AiWatermarkEffect : AvEffect() { override val name = AI_WATERMARK_EFFECT }

    /**
     * 消除笔特效
     */
    object MagicEliminateEffect : AvEffect() { override val name = MAGIC_ELIMINATE_EFFECT }

    /**
     * 虚化特效
     */
    object BlurEffect : AvEffect() { override val name = BLUR_EFFECT }

    /**
     * 贴纸特效
     */
    object StickerEffect : AvEffect() { override val name = STICKER_EFFECT }

    /**
     * 标记特效
     */
    object DoodleEffect : AvEffect() { override val name = DOODLE_EFFECT }

    /**
     * 边框特效
     */
    object BorderEffect : AvEffect() { override val name = BORDER_EFFECT }

    /**
     * AI 超清特效
     */
    object AihdEffect : AvEffect() { override val name = AIHD_EFFECT }

    /**
     * AI 风光特效
     */
    object AISceneryEffect : AvEffect() { override val name = AI_SCENERY_EFFECT }

    /**
     * AI 涂鸦特效
     */
    object AiGraffitiEffect : AvEffect() { override val name = AI_GRAFFITI_EFFECT }

    /**
     * 保存-融合特效
     */
    object TextureBlendEffect : AvEffect() { override val name = TEXTURE_BLEND_EFFECT }

    /**
     * Pm贴纸特效
     */
    object PmStickerEffect : AvEffect() { override val name = PM_STICKER_EFFECT }

    /**
     * 修复特效
     */
    object RepairEffect : AvEffect() { override val name = REPAIR_EFFECT }

    /**
     * 换图特效
     */
    object ImageReplaceEffect : AvEffect() {
        override val name = IMAGE_REPLACE_EFFECT
        override val forceCaching: Boolean = true
    }

    /**
     * ai补光特效
     */
    object AiLightingEffect : AvEffect() { override val name = AI_LIGHTING_EFFECT }

    /**
     * 慢动作特效
     */
    object SlowMotionEffect : AvEffect() { override val name = SLOW_MOTION_EFFECT }

    /**
     * 编辑特效名称
     */
    abstract val name: String

    /**
     * 特效强制缓存
     * 如换图， olive这种插件，他可能在做的时候变换了顺序，而单步操作完之后参数纹理可能已经被回收
     * 如果重新触发换图渲染，这个时候出图要么黑图，要么花图
     * 其他特效不能设置强制缓存 ！！ 影响性能
     */
    open val forceCaching: Boolean = false

    /**
     * 特效信息是否需要缓存
     */
    open val needCache: Boolean = false

    /**
     * 枚举类，它表示特效参数的类型，包括字符串、布尔值、整数和浮点数等
     */
    enum class ArgumentType {
        /**
         * String类型
         */
        STRING,

        /**
         * boolean类型
         */
        BOOL,

        /**
         * int类型
         */
        INT,

        /**
         * float类型
         */
        FLOAT,

        /**
         * 未知类型
         */
        UNKNOWN
        // TODO("Not yet implemented VEC MAT .............")
    }

    /**
     * 算法子项相关信息
     * @param name 特效子项算法名称
     * @param handle 算法交互的句柄
     * TODO(Marked by wanglian
     *      1.这里ArgumentType应该还是需要的，需要tbl sdk那边元信息提供参数类型，目前他们那边想要沟通确定是否有必要添加
     *      2.Argument本身不可变，属性也不可变，没有多线程问题一旦赋值就不可改变
     *      )
     * @param type 特效子项算法参数类型[ArgumentType]
     * @param extras ParameterInfo 的额外携带信息，例如像IPU滤镜，自滤镜效果有大师字样，ipu滤镜会返回自滤镜翻译，自定义json格式业务自己拆包
     */
    data class Argument(val name: String, val handle: String, val type: ArgumentType, val extras: String)

    override fun toString(): String = "AvEffect(name='$name')"

    companion object {

        /**
         * 根据特效名称获取对应算法特效
         * @param effectName 特效名称
         * @return 返回对应名称特效
         */
        fun getEffect(effectName: String): AvEffect? {
            return when (effectName) {
                TRANSFORM_EFFECT -> TransformEffect
                ADJUST_EFFECT -> AdjustEffect
                FILTER_EFFECT -> FilterEffect
                OLIVE_EFFECT -> OliveEffect
                TEXT_EFFECT -> TextEffect
                WATERMARK_EFFECT -> WatermarkEffect
                MOSAIC_EFFECT -> MosaicEffect
                BEAUTY_EFFECT -> BeautyEffect
                AI_REPAIR_EFFECT -> AiRepairEffect
                AI_WATERMARK_EFFECT -> AiWatermarkEffect
                IMAGE_QUALITY_ENHANCE_EFFECT -> ImageQualityEnhanceEffect
                AI_COMPOSITION_EFFECT -> AiCompositionEffect
                AI_ELIMINATE_EFFECT -> AiEliminateEffect
                MAGIC_ELIMINATE_EFFECT -> MagicEliminateEffect
                BLUR_EFFECT -> BlurEffect
                AI_SCENERY_EFFECT -> AISceneryEffect
                STICKER_EFFECT -> StickerEffect
                DOODLE_EFFECT -> DoodleEffect
                BORDER_EFFECT -> BorderEffect
                AIHD_EFFECT -> AihdEffect
                TEXTURE_BLEND_EFFECT -> TextureBlendEffect
                PM_STICKER_EFFECT -> PmStickerEffect
                REPAIR_EFFECT -> RepairEffect
                IMAGE_REPLACE_EFFECT -> ImageReplaceEffect
                AI_GRAFFITI_EFFECT -> AiGraffitiEffect
                AI_BESTTAKE_EFFECT -> AiBestTakeEffect
                AI_LIGHTING_EFFECT -> AiLightingEffect
                SLOW_MOTION_EFFECT -> SlowMotionEffect
                else -> null
            }
        }

        const val TRANSFORM_EFFECT = "TransformEffect"
        const val ADJUST_EFFECT = "AdjustEffect"
        const val OLIVE_EFFECT = "OliveEffect"
        const val FILTER_EFFECT = "FilterEffect"
        const val TEXT_EFFECT = "TextEffect"
        const val WATERMARK_EFFECT = "WaterMarkEffect"
        const val MOSAIC_EFFECT = "MosaicEffect"
        const val BEAUTY_EFFECT = "BeautyEffect"
        const val AI_REPAIR_EFFECT = "AiRepairEffect"
        const val IMAGE_QUALITY_ENHANCE_EFFECT = "ImageQualityEnhanceEffect"
        const val AI_COMPOSITION_EFFECT = "AiCompositionEffect"
        const val AI_ELIMINATE_EFFECT = "AiEliminateEffect"
        const val AI_BESTTAKE_EFFECT = "AiBestTakeEffect"
        const val MAGIC_ELIMINATE_EFFECT = "MagicEliminateEffect"
        const val BLUR_EFFECT = "BlurEffect"
        const val STICKER_EFFECT = "StickerEffect"
        const val DOODLE_EFFECT = "DoodleEffect"
        const val BORDER_EFFECT = "BorderEffect"
        const val PM_STICKER_EFFECT = "PmStickerEffect"
        private const val AI_WATERMARK_EFFECT = "AiWatermarkEffect"
        private const val AIHD_EFFECT = "AihdEffect"
        private const val AI_SCENERY_EFFECT = "AISceneryEffect"
        private const val TEXTURE_BLEND_EFFECT = "TextureBlendEffect"
        private const val REPAIR_EFFECT = "RepairEffect"
        private const val IMAGE_REPLACE_EFFECT = "ImageReplaceEffect"
        private const val AI_GRAFFITI_EFFECT = "AiGraffitiEffect"
        private const val AI_LIGHTING_EFFECT = "AiLightingEffect"
        private const val SLOW_MOTION_EFFECT = "SlowMotionEffect"
    }
}

