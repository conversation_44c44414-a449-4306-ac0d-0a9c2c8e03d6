/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvTrack
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/1/31 22:09
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/1/31		  1.0		 AvTrack
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.track

/**
 * 它表示一个容纳编辑内容（AvClip）的媒体轨道
 */
interface IAvTrack {
    /**
     * 表示媒体轨道的名称
     */
    val name: String

    /**
     * 表示媒体轨道的类型，包括视频/图片轨、音频轨、字幕轨三种类型[AvTrackType]
     */
    val type: AvTrackType

    /**
     * AvTrackType是一个枚举类型，它表示媒体轨道的类型，包括视频/图片轨、音频轨、字幕轨三种类型
     */
    enum class AvTrackType {
        /**
         * 视频轨道
         */
        VIDEO,

        /**
         * 音频轨道
         */
        AUDIO,

        /**
         * 字母轨道
         */
        SUBTITLE
    }
}