/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvPlayerGetOptions.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/7/15
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangweichao       2024/7/15    1.0                create
 ******************************************************************************/
package com.oplus.gallery.framework.abilities.resourcing.options

import android.graphics.ColorSpace
import com.oplus.gallery.standard_lib.codec.player.AVPlayer

/**
 * 播放器请求资源时的Option, 用于保存业务端设置的播放器参数
 */
class AvPlayerGetOptions : ResourceGetOptions() {
    /**
     * 视频对应的色彩空间
     */
    var targetColorSpace: ColorSpace? = null

    /**
     * 实况照片视频参数：视频播放特效列表
     */
    var videoPlayEffect: MutableList<String>? = null

    /**
     * 视频是否需要以120FPS播放
     */
    var shouldPlaybackAt120Fps: Boolean = false

    /**
     * 实况照片视频参数：视频播放类型
     */
    var playType: AVPlayer.VideoPlayType? = null

    /**
     *实况照片视频参数：小视频信息
     */
    var oliveSubVideoInfo: AVPlayer.VideoInfo? = null
}