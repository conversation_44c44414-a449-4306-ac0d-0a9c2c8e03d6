/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IDownloadAbility.kt
 ** Description : 下载能力类，总体而言是指从云端拉取数据，包括下载模型，资源，进行网络请求。
 ** Version     : 1.0
 ** Date        : 2024/2/23
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D           2024/2/23      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.download

import android.content.Context
import androidx.annotation.WorkerThread
import com.oplus.gallery.foundation.networkaccess.ResponseBean
import com.oplus.gallery.foundation.networkaccess.callback.AppResultCallback
import com.oplus.gallery.framework.abilities.download.networkrequest.NetworkRequest
import com.oplus.gallery.framework.abilities.download.processor.ProcessResult
import com.oplus.gallery.framework.abilities.download.template.AbsLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate

/**
 * 下载能力，总体而言是指从云端拉取数据，包括下载模型，资源等各类文件，以及进行网络请求（可视为一种数据）。
 */
interface IDownloadAbility : AutoCloseable {

    /**
     * 异步下载文件
     * @param template 文件下载的模板
     * @param priority 文件下载的优先级
     * @param mergeWhenSameTaskRunning 当已经有相同任务在队列里，true则合并任务（即增加监听），false则入列失败，返回null
     * @param loadingListener 监听
     * @return 返回任务id，可通过此id取消任务
     */
    fun downloadFileAsync(
        template: AbsLoadingTemplate,
        priority: Priority,
        mergeWhenSameTaskRunning: Boolean = false,
        loadingListener: AbsLoadingTemplate.LoadingListener? = null
    ): Int?

    /**
     * 同步下载文件
     * @param template 文件下载的模板
     * @param loadingListener 监听
     * @return 返回ProcessResult
     */
    fun downloadFileSync(
        template: AbsLoadingTemplate,
        loadingListener: AbsLoadingTemplate.LoadingListener? = null
    ): ProcessResult

    /**
     * 是否下载文件空闲，即是否有任务正在执行或等待执行。
     */
    fun isDownloadIdle(): Boolean

    /**
     * 是否正在下载中：taskId正在执行或者是处于等待执行中
     *
     * @param taskId 异步下载文件接口[downloadFileAsync]返回的值
     *
     * @return Boolean
     */
    fun isDownloading(taskId: Int): Boolean

    /**
     * 下载优先级
     */
    enum class Priority(val level: Int) {
        /**
         * 后台下载，低优先级，先进先出
         */
        BACKGROUND(0),

        /**
         * 前台下载，中优先级，先进先出
         * 如前台交互中有资源列表需要逐个点击下载，期望顺序为先点击的先下载，使用此优先级
         */
        FOREGROUND(1),

        /**
         * 前台最高优先级，后进先出
         * 如前台交互中有资源列表需要逐个点击下载，期望顺序为后点击的先下载，或是处于焦点内的资源先下载，使用此优先级
         */
        FOREGROUND_MAX(2);
    }

    /**
     * 取消文件下载
     * @param id 任务id
     */
    fun cancelDownload(id: Int)

    /**
     * 网络请求，同步
     */
    @WorkerThread
    fun <Param, Return> execute(context: Context, request: NetworkRequest<Param, Return>): ResponseBean<Return>?

    /**
     * 网络请求，异步
     */
    fun <Param, Return> enqueue(
        context: Context,
        request: NetworkRequest<Param, Return>,
        appResultCallback: AppResultCallback<Return>
    ): String?

    /**
     * 移除监听
     * @param downloadTaskId 任务id
     * @param loadingListener 监听
     */
    fun removeLoadingListener(downloadTaskId: Int, loadingListener: AbsLoadingTemplate.LoadingListener)

    /**
     * 移除所有监听
     * @param downloadTaskId 任务id
     */
    fun removeAllLoadingListener(downloadTaskId: Int)

    override fun close() = Unit
}

/**
 * 默认的模型下载监听器实现，方便继承单独实现其中部分接口
 */
class DefaultLoadingListener : CommonModelLoadingTemplate.ModelLoadingListener {
    override fun onFileDownloadSuccess() = Unit
    override fun onFileDownloadFailure(result: ProcessResult) = Unit
    override fun onSuccess() = Unit
    override fun onFailure(result: ProcessResult) = Unit
    override fun onProgress(progress: Int) = Unit
}

/**
 * 远程模型信息对象
 * @param name 模型名
 * @param version 版本号
 * @param zipMd5 md5
 * @param downloadUrl 下载链接
 */
data class RemoteModelInfo(
    val name: String,
    var version: Int = Constants.INVALID_VERSION,
    var zipMd5: String? = null,
    var downloadUrl: String? = null
)

object Constants {
    const val INVALID_VERSION = -1
}