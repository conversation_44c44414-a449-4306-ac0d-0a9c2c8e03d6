/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PersonPetAlbumSetModel.kt
 ** Description : 人物与宠物图集列表数据模型
 ** Version     : 1.0
 ** Date        : 2025/04/08
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/04/08  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.data.model

import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.PersonPet.PATH_SET_PERSON_PET_ANY
import com.oplus.gallery.business_lib.model.data.personpet.set.PersonPetAlbumSet
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonPetModelGetter.Companion.TYPE_PERSON_PET_ALBUM_SET

/**
 * 人物与宠物图集列表数据模型
 */
class PersonPetAlbumSetModel(isPositiveOrder: Boolean) : AlbumSetModel(
    PATH_SET_PERSON_PET_ANY,
    TYPE_PERSON_PET_ALBUM_SET,
    isPositiveOrder
) {

    /**
     * 批量去加载人物与宠物图集列表封面Item，从start开始，加载count项,
     * @param start 起始位置，闭区间
     * @param count 从start开始，加载count项
     */
    override fun prepareCoverItems(start: Int, count: Int) {
        (mediaSet as? PersonPetAlbumSet)?.prepareCoverItems(start, count)
    }

    companion object {
        private const val TAG = "PersonPetAlbumSetModel"
    }
}