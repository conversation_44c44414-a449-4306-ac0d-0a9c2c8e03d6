/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IVideoEditEngine
 ** Description: 视频编辑引擎接口
 **
 ** Version: 1.0
 ** Date: 2022/08/03
 ** Author: Yegua<PERSON><PERSON>@Apps.Gallery
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/08/03  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.videoedit

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.ColorSpace
import android.graphics.PointF
import android.util.Rational
import android.view.ViewGroup
import androidx.annotation.MainThread
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.SDR_VIDEO_TYPE
import com.oplus.gallery.business_lib.model.data.location.api.ConfigAddress
import com.oplus.gallery.framework.abilities.videoedit.data.FilterInfo
import com.oplus.gallery.framework.abilities.videoedit.data.FxInfo
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo
import com.oplus.gallery.framework.abilities.videoedit.data.SongInfo
import com.oplus.gallery.framework.abilities.videoedit.data.StickerInfo
import com.oplus.gallery.framework.abilities.videoedit.data.SubTitleInfo
import com.oplus.gallery.framework.abilities.videoedit.data.TemplateInfo
import com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio
import com.oplus.gallery.framework.abilities.videoedit.data.SaveVideoInfo
import com.oplus.gallery.videoeditor.data.VideoSpec
import java.io.PrintWriter
import java.util.function.Function

/**
 * 视频编辑能力
 */
interface IVideoEditAbility : AutoCloseable {

    /**---------------- core interface begin ----------------*/
    /**
     * 初始化VideoFileInfo
     *
     * @param videoUri uri.toString或者filePath（传给美摄接口，美摄做了兼容。。。）
     * @param videoFilePath 源文件的文件路径
     * @param isHfrSlowMotion
     * @param videoType see [VideoEditConstant.VideoType]
     */
    fun initVideoFileInfo(videoUri: String?, videoFilePath: String, isHfrSlowMotion: Boolean, videoType: Int = SDR_VIDEO_TYPE): Boolean

    /**
     * 初始化引擎
     *
     * @param mediaItemPath 相册Path的string，可以调用Path.fromString(mediaItemPath)转化为相册path
     */
    fun initEngine(mediaItemPath: String?): Boolean

    /**
     * 初始化engine
     *
     * @param mediaItemPath 相册Path的string，可以调用Path.fromString(mediaItemPath)转化为相册path
     * @param videoSpec  视频规格尺寸
     * @param checkVideoSpecification 是否检查视频规格
     * @return 是否初始化engine成功
     */
    fun initEngine(mediaItemPath: String?, videoSpec: VideoSpec, checkVideoSpecification: Boolean): Boolean

    /**
     * 创建视频预览窗口
     *
     * @param parent     视频窗口的载体view
     * @param shouldCrop 是否裁剪
     * @return 是否创建成功
     */
    fun createLiveWindow(parent: ViewGroup, shouldCrop: Boolean): Boolean

    /**
     * 数据准备完成
     * 通知引擎数据已经准备完成
     */
    fun dataReady()
    /**---------------- core interface end ----------------*/

    /**---------------- player interface begin ----------------*/

    /**
     * 是否正在播放
     */
    fun isPlaying(): Boolean

    /**
     * 开始播放，表示从头播放
     */
    fun play()

    /**
     * 开始播放，从startTime 开始播放
     */
    fun play(startTime: Long, endTime: Long)

    /**
     * 继续播放，暂停后调此接口
     */
    fun resume()

    /**
     * 暂停播放
     */
    fun pause()

    /**
     * 停止播放
     */
    fun stop()

    /**
     * 强制停止播放
     * @param force 是否强制
     */
    fun stop(force: Boolean)

    /**
     * 设置进度
     * @param time 要设置的时间点，单位：ms
     */
    fun seekTo(time: Long)

    /**
     * 重置
     */
    fun reset()

    /**
     * 准备
     */
    fun prepare()

    /**
     * 注册编辑器状态监听器
     * 反注册会在destroy接口中完成
     */
    fun registerStateListener(listener: OnVideoEditStateListener)

    fun unregisterStateListener(listener: OnVideoEditStateListener)

    /**---------------- player interface end ----------------*/

    /**---------------- video interface begin ----------------*/

    /**
     * 限制预览的最大分辨率，若是视频的高度大于maxHeight，则等比缩小宽高
     *
     * @param maxHeight 限制预览的最大高度
     */
    fun setPreviewMaxHeight(maxHeight: Int)

    fun getTimeBase(): Long

    fun getCurrentTime(): Long

    /**
     * 从美摄接口直接获取时间轴的原始时间位置（未经末尾偏移调整）
     * @return Long 微秒
     */
    fun getRealCurrentTime(): Long

    fun getTotalTime(): Long

    fun getFrameDuration(): Int

    fun getCurrentFrame(): Bitmap?

    fun repaintFrame()

    fun destroy(cleanAllContext: Boolean)

    fun getVideoHeight(): Int

    fun getVideoWidth(): Int

    fun getVideoFileHeight(): Int

    fun getVideoUri(): String?

    /**
     * 获取文件绝对路径
     */
    fun getVideoFilePath(): String?

    fun getVideoFps(): Number?

    fun getSlowMotionPlayFps(): Int

    fun getVideoFileWidth(): Int

    fun getVideoDuration(): Long

    fun getVideoHdrType(): Int

    fun getMeicamVideoTimeline(): Any?
    /**---------------- video interface end ----------------*/

    /**---------------- save interface begin ----------------*/
    fun saveVideo(stringUri: String?): Boolean

    fun saveVideo(
        stringUri: String?,
        videoHeight: Int,
        videoDateTaken: Long,
        videoPath: String
    ): Boolean

    /**
     * 保存视频到指定路径，可指定起始时刻与帧率
     * @param filePath: 保存路径
     * @param startTime: 起始时刻
     * @param endTime: 结束时刻
     * @param fps: 帧率
     */
    fun saveVideo(filePath: String, startTime: Long, endTime: Long, fps: Int): Boolean

    /**
     * 获取导出的视频的 hdr 类型
     * @return type 参考 [VideoTypeParser.VideoType]
     */
    fun getExportVideoHdrType(): Int

    fun saveShareVideo(
        stringUri: String?,
        start: Long,
        end: Long,
        videoHeight: Int
    ): Boolean

    /**
     * 定制化生成视频
     */
    fun saveVideo(
        info: SaveVideoInfo
    ): Boolean

    /**
     * 获取timeline指定帧时刻图片，并且管控是否继续播放
     * @param frameTime: 取图帧时刻
     * @param endTime: 播放停止时刻
     * @param isResumePlay: 播放暂停后是否继续播放
     *
     */
    suspend fun grabImageFromTimelineAsync(frameTime: Long, endTime: Long, isResumePlay: Boolean): Bitmap?

    /**
     * 基于GPU的方式从原视频中抽目标色域帧
     * 适用于视频抽帧
     * 由于美摄限制，需要在 MAIN 线程中调用（主线程 + 挂起函数）
     * @param timeUs 时间点，微妙
     * @param config 目标config
     * @param colorSpace 目标色域
     * @return 目标色域的 bitmap
     */
    @MainThread
    suspend fun grabImageFromTimelineAsync(timeUs: Long, config: Bitmap.Config, colorSpace: ColorSpace): Bitmap?

    /**
     * 注册监听自定义编译任务状态的回调接口
     */
    fun setCustomCompileTaskListener(videoListener: OnCustomCompileTaskStatusListener?)

    /**
     * 更新自定义编译任务执行状态
     * @param isRunning: 是否正在执行
     */
    fun updateCustomCompileTaskStatus(isRunning: Boolean)

    /**
     * 当前是否允许SeekTo操作
     */
    fun isSeekEnable(): Boolean

    /**---------------- save interface end ----------------*/

    /**---------------- cut rotate interface begin ----------------*/
    fun intoCutRotateMode()

    fun exitCutRotateMode()

    fun setSelectRect(ratio: VideoRatio)

    fun updateRatio(ratio: VideoRatio)

    fun cutVideo(isAnimation: Boolean)

    fun rotateVideo(rotation: Int)

    fun setSelectPosition(position: Int)

    fun getSelectPosition(): Int

    fun getRotation(): Int

    fun getAspectRatio(): Int

    fun setAspectRatio(rational: Rational)

    fun recover(ratio: VideoRatio, rotation: Int, isCut: Boolean, isAnimation: Boolean)

    fun getRatio(): VideoRatio
    /**---------------- cut rotate interface end ----------------*/

    /**---------------- video clip interface begin ----------------*/
    fun addVideoClip(uri: String?, filePath: String?, dataTaken: Long): Boolean

    fun addVideoClip(uri: String?, filePath: String?, dataTaken: Long, trimIn: Long, trimOut: Long): Boolean

    fun addClip(
        type: ClipType,
        filePath: String?,
        trimIn: Long,
        trimOut: Long
    ): IClip?

    fun addClip(
        type: ClipType,
        bitmap: Bitmap,
        trimIn: Long,
        trimOut: Long
    ): IClip?

    fun getClip(type: ClipType, index: Int): IClip?

    /**
     * 某一个片段是否预准备好
     * 注意：SDK暂时只支持判断（当前播放中或Seek结束后）片段后的片段，其余片段不可以判断
     */
    fun isClipPrePrepared(index: Int): Boolean

    fun checkVideoSupported(path: String?, context: Context?): Boolean

    fun checkVideoSoftSupported(path: String?, context: Context?): Boolean

    fun trimVideo(trimIn: Long, trimOut: Long)

    fun addSlowMotionVideoClip(
        fileUri: String?,
        filePath: String?,
        dataTaken: Long,
        slowfps: Int,
        slowtimelist: LongArray?,
        fullslowmotion: Boolean
    ): Boolean

    fun addHfrFullSlowMotionVideoClip(
        fileUri: String?,
        filePath: String?,
        dataTaken: Long,
        slowfps: Int,
        playfps: Int
    ): Boolean

    fun changeSlowMotion(
        enterA: Float,
        outA: Float,
        enterB: Float,
        outB: Float
    ): Boolean

    fun isSupportSlowMotionMode(): Boolean

    fun isHfrSlowMotion(): Boolean

    fun getSlowMotionList(): FloatArray?

    fun getSlowMotionOriginalList(): FloatArray?

    fun getTrimInTime(): Long

    fun getTrimOutTime(): Long

    fun setOriginalMusicMute(isMute: Boolean)

    fun getOriginalMusicMuted(): Boolean

    fun changeAudioSpeed(speed: Float): Boolean

    fun changeVideoSpeed(speed: Float): Boolean

    fun getVideoSpeed(): Float

    fun setVideoSpeedChanged(changed: Boolean)

    fun setVideoClipChanged(changed: Boolean)

    fun setExtraVideoRotation(rotation: Int)

    fun getExtralVideoRotation(): Int
    /**---------------- video clip interface end  ----------------*/

    /**---------------- theme helper interface begin ----------------*/
    fun initThemeVideoClips(mediaInfos: ArrayList<MediaInfo?>?): Boolean

    fun insertThemeVideoClip(infoList: ArrayList<MediaInfo?>?): Boolean

    fun deleteThemeVideoClip(info: MediaInfo?): Boolean

    fun getThemeVideoClipList(): ArrayList<String?>?

    fun getThemeVideoClipCount(): Int

    fun addThemeVideoCover(info: MediaInfo?): Boolean

    fun removeThemeVideoCover(): Boolean

    fun getThemeVideoCover(): MediaInfo?

    fun setThemeMusicMute(isMute: Boolean)

    fun addThemeMusic(musicId: String?)

    fun addThemeTrimMusic(musicId: String?, startTime: Long, endTime: Long)

    fun removeThemeMusic(force: Boolean)

    fun getCurrentThemeMusic(): String?

    fun getThemeCurrentMusicPos(): Int

    fun addTheme(theme: ThemeInfo?): Boolean

    fun getCurrentTheme(): String?

    fun getThemeCurrentThemePos(): Int

    fun addThemeCaption(title: String?, hint: String?): Boolean

    fun updateThemeCaptionTitle(title: String?): Boolean

    fun removeThemeCaption()

    fun reAddThemeCaption()

    fun getThemeTitleText(): String?

    fun changeThemeDuration(time: Long): Boolean

    fun getThemeMinTotalTime(): Long

    fun getThemeMaxTotalTime(): Long

    fun seekToThemePosition(index: Int)

    fun cleanBuiltinTransition()
    /**---------------- theme helper interface end ----------------*/

    /**---------------- template interface begin ----------------*/
    fun removeTemplateElements(keepBGM: Boolean)

    fun setCurrentTemplateInfo(templateInfo: TemplateInfo?)

    fun getCurrentTemplateInfo(): TemplateInfo?
    /**---------------- template interface end ----------------*/

    /**---------------- song interface begin ----------------*/
    /**
     * 添加内置音乐
     * @param songInfo 需要添加的内置音乐
     */
    fun applySong(songInfo: SongInfo?): Boolean

    /**
     * 添加音乐本地音乐
     * @param songInfo 需要添加的音乐
     */
    fun applyLocalSong(songInfo: SongInfo?): Boolean

    fun getCurrentSongInfo(): SongInfo?

    fun setCurrentSongInfo(songInfo: SongInfo?)

    /**
     * 添加本地音乐
     * @param contentUri 需要添加的音乐地址
     * @param start 起始时间
     * @param end 结束时间
     * @param duration 时长
     */
    fun addTrimMusic(
        contentUri: String?,
        filePath: String?,
        start: Long,
        end: Long,
        duration: Long
    ): Boolean

    fun reAlignMusic(startTime: Long): Boolean

    fun setLocalMusicIndex(index: Int)

    fun resetLocalMusicIndex()

    fun getLocalMusicUri(): String?

    fun getLocalMusicPath(): String?

    fun getLocalMusicTrimStart(): Long

    fun getLocalMusicTrimEnd(): Long

    fun getLocalMusicDuration(): Long

    fun saveMusicState()

    fun resetMusicState()
    /**---------------- song interface end ----------------*/

    /**---------------- filter interface begin ----------------*/
    fun applyVideoFilter(filterInfo: FilterInfo?, index: Int): Boolean

    fun applyVideoFilter(path: String): Boolean

    fun getCurrentFilter(): FilterInfo?

    fun getCurrentFilterIndex(): Int

    fun getCurrentFilterPath(): String?

    fun setCurrentFilter(filterInfo: FilterInfo?)
    /**---------------- filter interface end ----------------*/

    /**---------------- subtitle interface begin ----------------*/
    fun updateWaterMarkPosition(hideWaterMark: Boolean)

    fun hadHideWaterMark(): Boolean

    fun installSubTitleStyleRes()
    fun setWaterMark(model: String?, timeAndAddress: String?): Boolean

    fun updateWaterMarkDuration()

    fun hasWaterMark(): Boolean

    fun addSubTitle(text: String?, context: Context?): Long

    fun addSubTitleInfo(info: SubTitleInfo?, context: Context?)

    fun renameSubTitle(subTitleIndex: Long, text: String?)


    fun removeSubTitle(subtitlePos: Long, needSeek: Boolean): SubTitleInfo?

    fun moveSubTitle(subTitleIndex: Long, prePointF: PointF?, nowPointF: PointF?)

    fun checkAndGetSubTitleEditPos(curPos: Long): List<SubTitleInfo?>?

    fun getSubTitleList(): ArrayList<SubTitleInfo?>?

    fun reAlignSubTitle(offsetUs: Long)

    fun reAlignSubTitle(offsetCalculateFunction: Function<Long?, Long?>?)

    fun reAlignSubTitle(speed: Float)

    fun computeLocationInfo(latitude: Double, longitude: Double)

    fun clearLocationInfo()

    fun getAddress(): ConfigAddress?
    /**---------------- subtitle interface end ----------------*/

    /**---------------- sticker interface begin ---------------*/
    fun applyVideoSticker(stickerInfo: StickerInfo)

    fun reAlignVideoSticker(inTimeNs: Long)
    /**---------------- sticker interface end ----------------*/

    /**---------------- Fx interface begin ---------------*/
    fun getAppliedFxTime(): Long

    fun setAppliedFxTime(time: Long)

    fun setCurrentFxInfo(fxInfo: FxInfo?)

    fun getCurrentFxInfo(): FxInfo?

    fun removeVideoFx()

    fun installVideoFx(fxInfo: FxInfo)

    fun applyVideoFx(fxInfo: FxInfo, startPos: Long)

    fun reAlignVideoFx(offsetUs: Long)

    fun reAlignVideoFx(speed: Float)

    fun clearLastFx()

    fun reAlignVideoFx(offsetCalculateFunction: Function<Long?, Long?>?)
    /**---------------- Fx interface end ---------------*/

    /**---------------- thumbnail view interface begin ----------------*/
    fun showVideoThumbLoader(trimView: ViewGroup?)

    fun showTrimDetailThumbLoader(trimView: ViewGroup?, time: Long)

    fun showFxThumbLoader(trimView: ViewGroup?)

    fun showSubTitleThumbLoader(trimView: ViewGroup?)

    /**
     * 加载视频导出olive缩图轴组件
     * @param trimView 父容器
     */
    fun showVideoOliveThumbLoader(trimView: ViewGroup?)

    /**
     * 加载自定义剪辑缩图轴组件
     */
    fun showCustomTrimThumbLoader(trimView: ViewGroup?, time: Long, padding: Int)
    /**---------------- thumbnail view interface begin ----------------*/

    /**---------------- transition interface begin ----------------*/

    /**
     * 添加转场
     * @param transitionIndex  转场索引，{@link VideoEditConstant.Transition}
     * @param clipIndex  片段索引，转场要添加在此clip之后
     * @param duration   转场时间，单位ms
     */
    fun appendTransition(
        transitionIndex: Int,
        clipIndex: Int,
        duration: Long,
    ): Boolean

    /**
     * 添加转场
     * @param transitionIndex  转场索引
     * @param clip        片段对象，转场要添加在此clip之后
     * @param duration   转场时间，单位ms
     */
    fun appendTransition(
        transitionIndex: Int,
        clip: IClip,
        duration: Long,
    ): Boolean

    /**
     * 移除转场
     * @param clipIndex  片段索引
     * @param index      转场的index
     */
    fun removeTransition(
        clipIndex: Int,
        index: Int
    ): Boolean

    /**
     * 移除转场
     * @param clip    片段
     * @param index   转场的index
     */
    fun removeTransition(
        clip: IClip,
        index: Int
    ): Boolean

    /**
     * 重置转场
     * @param clipIndex 片段索引,其后的转场将被重置
     * @param duration 转场的时间
     */
    fun resizeTransition(clipIndex: Int, duration: Long): Boolean

    /**
     * 重置转场
     * @param clip 片段索引,其后的转场将被重置
     * @param duration 转场的时间
     */
    fun resizeTransition(clip: IClip, duration: Long): Boolean

    /**---------------- transition interface end ----------------*/

    /**---------------- other interface begin ----------------*/
    fun setVideoBackgroundColor(color: Color?)

    fun hasVideoChanged(): Boolean

    fun dumpEngineInfo(writer: PrintWriter?)
    /**---------------- other interface end ----------------*/
}