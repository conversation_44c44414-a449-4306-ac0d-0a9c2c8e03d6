/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ShowTimeInstrument.kt
 ** Description:记录各种类型的真实显示时间
 ** Version: 1.0
 ** Date: 2023/6/21
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/21      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.taskmanage

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties

/**
 * 记录各种类型的真实显示时间
 */
object RealShowTimeInstrument {

    private const val TAG = "RealShowTimeInstrument"

    val DEBUG = GallerySystemProperties.getBoolean("debug.realshowtime.switch", false)

    /**
     * 大图删除
     */
    const val PHOTO_RECYCLE_TAG = "PhotoRecycle"

    /**
     * 图集删除
     */
    const val ALBUM_RECYCLE_TAG = "AlbumRecycle"

    /**
     * 时间轴Tab页显示
     */
    const val TIMELINE_TAB_SHOW_TAG = "TimelineTabFragment"

    /**
     * 大图缩图显示
     */
    const val PHOTO_SHOW_TAG = "PhotoFragment"

    /**
     * 发现页显示
     */
    const val EXPLORE_TAB_FRAGMENT = "ExplorerTabFragment"

    /**
     * 从用户输入到返回结果
     */
    const val SEARCH = "Search"

    /**
     * 搜索全链路: 从用户输入到界面第一帧显示
     */
    const val SEARCH_FULL_LINK = "SearchFullLink"

    /**
     * 特定类型的搜索:从用户输入到返回结果
     */
    const val SPECIAL_TYPE_SEARCH = "SpecialTypeSearch"

    /**
     * 特定类型的搜索:从用户输入到界面第一帧显示
     */
    const val SPECIAL_TYPE_SEARCH_FULL_LINK = "SpecialTypeSearchFullLink"

    /**
     * AI大师水印从点击到渲染完成的耗时
     */
    const val AI_MASTER_WATERMARK = "AiMasterWaterMark"

    /**
     * key用页面名称
     * value是实际显示的时间
     */
    private val showTimeMap = mutableMapOf<String, ShowTime>()

    @JvmStatic
    fun startRecord(tag: String) {
        if (DEBUG.not()) return
        GLog.w(TAG, "startRecord $tag")
        val showTime = ShowTime()
        showTime.startTime = System.currentTimeMillis()
        showTimeMap[tag] = showTime
    }

    @JvmStatic
    fun endRecord(tag: String) {
        if (DEBUG.not()) return
        val showTime = showTimeMap[tag]
        showTime?.let {
            val startTime = it.startTime
            val endTime = System.currentTimeMillis()
            val costTime = endTime - startTime
            it.endTime = endTime
            it.costTime = costTime
            GLog.w(TAG, "$tag costTime:$costTime")
            return
        }
        // 清除无效的tag
        showTimeMap.remove(tag)
        return
    }

    fun remove(tag: String) {
        showTimeMap.remove(tag)
    }

    fun clear() {
        showTimeMap.clear()
    }

    class ShowTime {
        var startTime: Long = 0
        var endTime: Long = 0
        var costTime: Long = 0
        override fun toString(): String {
            return "ShowTime(costTime=${costTime}ms)"
        }
    }
}