/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IAvAlgoInfo
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/2/28 16:54
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/2/28		  1.0		 IAvAlgoInfo
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.editing.algo

import com.oplus.gallery.framework.abilities.editing.bootstrap.InputSourceType

/**
 * 算法信息接口
 * 该接口描述了算法相关的基本信息，和算法子项参数信息列表，可以通过算法名称来唯一确认算法实例对象
 */
interface IAvAlgoInfo {
    /**
     * 算法的版本号
     */
    val version: Long

    /**
     * 算法的标识符
     */
    val identifier: String

    /**
     * 算法的名称
     */
    val algoName: String

    /**
     * 算法的版权信息
     */
    val copyright: String

    /**
     * 算法的描述信息
     */
    val description: String

    /**
     * 算法的作者信息
     */
    val creator: String

    /**
     * 算法是否支持HDR
     */
    val supportHDR: Boolean

    /**
     * 支持的HDR的类型 local uhdr
     * String SCHEME_LOCAL_HDR = "local_hdr"
     * String SCHEME_ULTRA_HDR = "ultra_hdr"
     * String SCHEME_HDR_HLG = "hdr_hlg"
     * String SCHEME_HDR_PQ = "hdr_pq"
     * String SCHEME_HDR_DOLBY = "hdr_dolby"
     * String SCHEME_HDR_NONE = "hdr_none"
     */
    val hdrSchemes: List<String>

    /**
     * hdr 编辑的形式  分离、合成、不支持
     * String EDIT_SCHEME_INDEPENDENT = "independent"
     * String EDIT_SCHEME_COMBINE = "combine"
     * String EDIT_SCHEME_NONE = "none"
     */
    val hdrEditSchemes: List<String>

    /**
     * 算法子功能项的参数信息列表
     */
    val parameters: List<IParameterInfo<*>>

    /**
     * 算法的输入类型，是一个CPU算法还是一个GPU算法
     */
    val inputSourceType: InputSourceType

    /**
     * 算法特效是否会改变颜色
     */
    val inputEffectChangeColor: Boolean

    /**
     * 获取元信息中，某一个键 identifier 对应的 Parameter
     * @param identifier 查找的键值
     * @param default 默认的Parameter、
     * @return 根据identifier 查找到的 Parameter，可空类型
     */
    fun <T> getParameter(identifier: String, default: IParameterInfo<T>? = null): IParameterInfo<T>?
}
