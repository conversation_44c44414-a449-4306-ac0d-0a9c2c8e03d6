/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - HdrOliveTransformImpl
 ** Description: hdr的olive图进行兼容格式转换实现
 **
 ** Version: 1.0
 ** Date: 2024/12/18
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  houdong<PERSON>@Apps.Gallery3D      2024/12/18        1.0      build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.channel.livephoto

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import android.util.Size
import com.meicam.sdk.NvsStreamingContext
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.abilities.transform.channel.IVideoTransform
import com.oplus.gallery.abilities.transform.channel.hdr.HDRVideoTransformEngine
import com.oplus.gallery.abilities.transform.util.MigrateFileDataUtil
import com.oplus.gallery.addon.os.BuildWrapper
import com.oplus.gallery.addon.osense.CpuFrequencyManager
import com.oplus.gallery.addon.osense.CpuFrequencyManager.setAction
import com.oplus.gallery.business_lib.model.data.base.item.getMediaUri
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_HDR_TRANSFORM_DATA
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_OLIVE_SUB_VIDEO
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_OLIVE_SUB_VIDEO_SIZE
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider.Companion.fromFile
import com.oplus.gallery.foundation.fileaccess.data.DeleteFileRequest
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainerExt
import com.oplus.gallery.foundation.fileaccess.extension.OliveFileExtenderContainerHelper
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.expand
import com.oplus.gallery.foundation.util.ext.toNormalizedCoord
import com.oplus.gallery.foundation.util.graphic.BitmapSaveUtils
import com.oplus.gallery.foundation.util.mask.maskPath
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper.SCAN_FILE_TIME_OUT
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_OLIVE_FILE_NAME_HEAD
import com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_OLIVE_SUFFIX
import com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_OLIVE_TRANSFORM_SUFFIX_TMP
import com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_VIDEO_FILE_NAME_HEAD
import com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_VIDEO_SUFFIX
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool.deleteHdrOliveTransformTempFile
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool.deleteHdrTransformTempFile
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool.getHDROliveTransformTempFileInfo
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool.getHDRTransformTempFileInfo
import com.oplus.gallery.framework.abilities.transform.TransformCallback
import com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF
import com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF
import com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF
import com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF
import com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_URI_PREF
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.hdr.HdrVideoTransformListener
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_PURE_COLOR
import com.oplus.gallery.framework.abilities.watermark.masterstyle.PURE_COLOR_WHITE
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.olive_decoder.OLivePhoto
import com.oplus.gallery.olive_editor.OLiveCreator
import com.oplus.gallery.olive_editor.OLiveEditor
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_0xFF
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.tblplayer.mediainfo.Mp4Util
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.nio.ByteBuffer
import java.util.Hashtable

/**
 * hdr视频的olive图进行兼容格式转换实现
 * 需要先把olive拆分问图和视频，再把hdr的频转换成sdr的，最后再用olive的sdk把图和视频组成新的olive文件
 * 特殊的，先用olive sdk把文件导出成视频，再用美摄进行hdr转sdr，不直接用美摄因为无法读取到视频的宽高，以及美摄从olive直接读取视频有问题
 * 由于olive sdk的导出和组装都没有进度回调，虽然美摄的处理有回调，但是不好组合进度，所以只能通过定时刷新模拟整个进度了
 * 参考[HDRVideoTransformImpl]，历史逻辑isAutoTransform，只看出来文件路经需要区分，其他的没太大区分的意义
 */
class HdrOliveTransformImpl(
    private val context: Context,
    private val storage: TransformStorage,
    private val abilityBus: IAbilityBus,
    private val hdrTransformEngineListener: HdrVideoTransformListener
) : IVideoTransform {

    private val isSaveToCacheTransform: Boolean = (storage == TransformStorage.APP_PRIV_CACHE)

    private val hdrVideoTransformEngine: HDRVideoTransformEngine by lazy {
        HDRVideoTransformEngine(context, object : HdrVideoTransformListener {
            override fun onExportStatusChange(state: Int) {
                hdrTransformEngineListener.onExportStatusChange(state)
            }

            override fun onExportProgressChange(progress: Int) {
                // 已经模拟实现进度变化，美摄回调的就忽略了
            }
        })
    }

    private var oriParentFile: File? = null
    private var oriOliveMimeType: String? = null
    private var oriOlivePhoto: OLivePhoto? = null
    private var oriOliveDecode: OLiveDecode? = null
    private var oriOliveFilePath: String? = null

    private var targetOliveSaveFileDir: File? = null
    private var targetVideoFileName: String? = null
    private var targetOliveFileName: String? = null

    private var savedOliveUri: Uri? = null

    private var mVideoEisData: String? = null

    /**
     * 由于olive导出视频，olive组装成文件都没有进度通知，所以只能定时刷新，实现虚假的进度推动
     */
    private var nextStep: Int = 0
    private var progress: Int = 0
    private val mainHandler: Handler by lazy {
        object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                if (msg.what != REFRESH_PROGRESS_MESSAGE) {
                    return
                }
                progress += REFRESH_PROGRESS_STEP
                progress = progress.coerceAtMost(nextStep)
                hdrTransformEngineListener.onExportProgressChange(progress)
                if (progress < PROGRESS_FINISH) {
                    mainHandler.sendEmptyMessageDelayed(REFRESH_PROGRESS_MESSAGE, REFRESH_PROGRESS_DELAY_MS)
                }
            }
        }
    }

    override fun checkSupported(totalVideoSize: Long, callback: TransformCallback?): Boolean {
        val enlargeTotalVideoSize = totalVideoSize * TOTAL_SIZE_TIMES_2
        if (isSaveToCacheTransform) {
            if (!StorageLimitHelper.hasEnoughStorageSpace(OplusEnvironment.StorageType.PHONE_STORAGE, enlargeTotalVideoSize)) {
                oriParentFile = null
                callback?.onNoStorage(OplusEnvironment.StorageType.PHONE_STORAGE)
            } else {
                oriParentFile = StorageLimitHelper.createCacheDir(context)
            }
        } else {
            var result = FileOperationUtils.createDir(OplusEnvironment.StorageType.PHONE_STORAGE, null)
            var isFull = !StorageLimitHelper.hasEnoughStorageSpace(OplusEnvironment.StorageType.PHONE_STORAGE, enlargeTotalVideoSize)
            if (isFull) {
                val hasExternal = OplusEnvironment.isExternalMounted()
                if (!hasExternal) {
                    GLog.w(TAG, LogFlag.DL, "checkStorageEnough show phone storage no space dialog.")
                    callback?.onNoStorage(OplusEnvironment.StorageType.PHONE_STORAGE)
                    result = null
                }
                result = FileOperationUtils.createDir(OplusEnvironment.StorageType.SDCARD_STORAGE, null)
                // check external storage enough or not
                isFull = !StorageLimitHelper.hasEnoughStorageSpace(OplusEnvironment.StorageType.SDCARD_STORAGE, enlargeTotalVideoSize)
                if (isFull) {
                    GLog.w(TAG, LogFlag.DL, "checkStorageEnough show sdcard storage no space dialog.")
                    callback?.onNoStorage(OplusEnvironment.StorageType.PHONE_STORAGE)
                    result = null
                }
            }
            oriParentFile = result
        }
        GLog.d(TAG, LogFlag.DL, "checkSupported totalVideoSize:$totalVideoSize oriParentFile:$oriParentFile")
        return (oriParentFile != null)
    }

    override fun beforeTransform(uri: String?, filePath: String?): Boolean {
        filePath ?: let {
            GLog.e(TAG, LogFlag.DL, "beforeTransform error!! filePath is null")
            return false
        }
        oriOliveFilePath = filePath

        prepareConvertOliveToImageAndVideo(filePath)
        return true
    }

    override fun startTransform(dateTaken: Long): Boolean {
        GLog.d(TAG, LogFlag.DL, "startTransform dateTaken:$dateTaken")
        progress = 0
        nextStep = PROGRESS_VIDEO_HDR_DONE
        mainHandler.sendEmptyMessageDelayed(REFRESH_PROGRESS_MESSAGE, REFRESH_PROGRESS_DELAY_MS)

        val oriOliveFilePath = oriOliveFilePath ?: return false
        /*
         把视频从hdr转成sdr
         由于美摄的输入只支持uri或者filePath，所以需要把视频保存成临时文件，然后把这个文件的path传给美摄
         调用美摄的sdk实现转换
         */
        val currentTime = TimeUtils.getFormatDateTime()
        if (TextUtils.isEmpty(currentTime)) {
            GLog.w(TAG, LogFlag.DL, "createVideoFile dateTime is null")
            return false
        }
        val oldVideoFileInfo = getHDRTransformTempFileInfo(context)
        val oldHdrOliveFileInfo = getHDROliveTransformTempFileInfo(context)
        deleteHdrTransformTempFile(context, oldVideoFileInfo)
        deleteHdrOliveTransformTempFile(context, oldHdrOliveFileInfo)

        if (oriParentFile == null) {
            GLog.w(TAG, LogFlag.DL, "[createVideoFile] mCurSaveParentDir is null")
            return false
        }
        if (isSaveToCacheTransform) {
            targetOliveSaveFileDir = File(oriParentFile, TransformCacheTool.HDR_SHARE_CACHE_DIRECTORY)
            targetVideoFileName = DEFAULT_VIDEO_FILE_NAME_HEAD + currentTime + DEFAULT_VIDEO_SUFFIX
            targetOliveFileName = DEFAULT_OLIVE_FILE_NAME_HEAD + currentTime + DEFAULT_OLIVE_SUFFIX
        } else {
            if (storage == TransformStorage.HIDDEN_DIR) {
                targetOliveSaveFileDir = File(oriParentFile, AppConstants.Path.PATH_CONVERT_CACHE_DIR)
                targetOliveSaveFileDir?.let {
                    if (!it.exists()) {
                        it.mkdir()
                        MigrateFileDataUtil.createNoMediaFile(it.path)
                    }
                }
            } else {
                targetOliveSaveFileDir = File(oriParentFile, FilePathUtils.getRelativePath(oriOliveFilePath))
            }
            targetVideoFileName = DEFAULT_VIDEO_FILE_NAME_HEAD + currentTime + DEFAULT_VIDEO_SUFFIX
            val mimeType = oriOliveMimeType?.let { ".${MimeTypeUtils.getSuffixByMimeType(it)}" } ?: DEFAULT_OLIVE_SUFFIX
            targetOliveFileName = DEFAULT_OLIVE_FILE_NAME_HEAD + currentTime + mimeType
        }

        if (!initEngineTimeLine(oriOliveFilePath)) {
            return false
        }

        createVideoFile(dateTaken)
        createOliveFile(dateTaken)
        return true
    }

    override fun saveTransform(lastModified: Long): Pair<Boolean, Uri?> {
        GLog.d(TAG, LogFlag.DL, "saveTransform lastModified:$lastModified")
        if (exportOliveImage().not()) {
            return Pair(isSaveToCacheTransform, null)
        }
        var savedUri: Uri? = null
        if (updateSavedOliveFile(lastModified)) {
            savedUri = getTransformedOliveUri()
        }
        GLog.d(TAG, LogFlag.DL) { "[saveTransform] savedUri: $savedUri" }
        return Pair(isSaveToCacheTransform, savedUri)
    }

    override fun afterTransform(listener: MediaStoreScannerHelper.SingleFileScanCompleteListener?) {
        GLog.d(TAG, LogFlag.DL, "afterTransform listener:$listener")
        MediaStoreScannerHelper.scanFileByMediaStoreSingle(
            context,
            getTransformedOliveFile().absolutePath,
            listener
        )
        deleteFile(getHdrVideoFile())
        deleteFile(getSdrVideoFile())
        nextStep = PROGRESS_FINISH
    }

    @Suppress("TooGenericExceptionCaught")
    override fun cancelTransform() {
        GLog.d(TAG, LogFlag.DL, "cancel")
        try {
            nextStep = PROGRESS_CANCEL
            mainHandler.removeMessages(REFRESH_PROGRESS_MESSAGE)
            deleteFile(getHdrVideoFile())
            deleteFile(getSdrVideoFile())
            if (!TextUtils.isEmpty(targetVideoFileName) && targetOliveSaveFileDir != null) {
                val mediaFile: File = getTransformedOliveFile()
                val deleteFileRequest = DeleteFileRequest.Builder()
                    .setFile(mediaFile)
                    .setUri(savedOliveUri)
                    .setImage(false)
                    .builder()
                val result = FileAccessManager.getInstance().delete(context, deleteFileRequest)
                if (!result) {
                    GLog.w(TAG, LogFlag.DL, "deleteSavedVideoIfNeed, delete file failed. mediaFile = $mediaFile")
                }
            }
        } catch (e: Exception) {
            GLog.w(TAG, LogFlag.DL, "deleteSavedVideoIfNeed, Exception:$e")
        }
    }

    override fun clear() {
        GLog.d(TAG, LogFlag.DL, "clear")
        hdrVideoTransformEngine.removeTimeLine()
    }

    override fun release() {
        GLog.d(TAG, LogFlag.DL, "release")
        hdrVideoTransformEngine.release()
    }

    private fun createVideoFile(dateTaken: Long) {
        SPUtils.setString(context, null, KEY_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF, targetVideoFileName)
        var targetVideoFile: File = getSdrVideoFile()
        targetVideoFile = FilePathUtils.switchToPublicDir(targetVideoFile, false)
        SPUtils.setString(context, null, KEY_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF, targetVideoFile.parent)
        GLog.d(TAG, LogFlag.DL, "createVideoFile, dateTaken:$dateTaken targetVideoFileName = $targetVideoFileName, file = $targetVideoFile")
        setAction(CpuFrequencyManager.Action.STORAGE_BOOST, TimeUtils.TIME_2_MIN_IN_MS)
        exportVideo(targetVideoFile, dateTaken)
    }

    private fun exportVideo(targetFile: File, dateTaken: Long) {
        var config: Hashtable<String, Any>? = null
        if (mVideoEisData != null) {
            config = Hashtable<String, Any>().also {
                it[NvsStreamingContext.COMPILE_OPPO_SPACIAL_DATA] = VIDEO_EIS_HEADER + mVideoEisData
            }
        }

        hdrVideoTransformEngine.saveTransformVideo(targetFile.absolutePath, dateTaken, config)
        nextStep = PROGRESS_OLIVE_DONE
    }

    private fun createOliveFile(dateTaken: Long) {
        SPUtils.setString(context, null, KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF, targetOliveFileName)
        var targetOliveFile: File = getTransformedOliveFile()
        targetOliveFile = FilePathUtils.switchToPublicDir(targetOliveFile, MimeTypeUtils.isImage(oriOliveMimeType))
        SPUtils.setString(context, null, KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF, targetOliveFile.parent)
        setAction(CpuFrequencyManager.Action.STORAGE_BOOST, TimeUtils.TIME_2_MIN_IN_MS)
        deleteAndCreateNewFile(targetOliveFile)
        savedOliveUri = MediaStoreScannerHelper.scanFileByMediaStoreSingle(context, targetOliveFile.absolutePath, SCAN_FILE_TIME_OUT.toLong())
        GLog.d(TAG, LogFlag.DL) {
            "createOliveFile, dateTaken:$dateTaken targetOliveFileName = $targetOliveFileName, file = $targetOliveFile, savedOliveUri:$savedOliveUri"
        }
        savedOliveUri?.let {
            SPUtils.setString(context, null, KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_URI_PREF, it.toString())
        }
    }

    private fun deleteAndCreateNewFile(file: File) {
        runCatching {
            if (file.exists()) {
                file.delete()
            }
            file.createNewFile()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "deleteAndCreateNewFile", it)
        }
    }

    private fun deleteFile(file: File) {
        runCatching {
            if (file.exists()) {
                file.delete()
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "deleteFile", it)
        }
    }

    /**
     * 把olive视频拆分为图片和视频，然后分别进行hdr转sdr的处理
     */
    private fun prepareConvertOliveToImageAndVideo(filePath: String?) {
        val tmpFilePath = filePath ?: return
        GLog.d(TAG, LogFlag.DL) { "convertOliveToImageAndVideo filePath:${tmpFilePath.maskPath()}" }
        runCatching {
            LocalMediaDataHelper.getLocalMediaItem(LocalMediaDataHelper.getPathByFilePath(tmpFilePath))?.let { mediaItem ->
                oriOliveMimeType = mediaItem.mimeType
                OLiveDecode.create(mediaItem.filePath).run {
                    oriOliveDecode = this
                    oriOlivePhoto = decode()
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "convertOliveToImageAndVideo filePath:${tmpFilePath.maskPath()}", it)
        }
    }

    /**
     * 导出olive的图片，因为图片不需要转，所以直接写入到已经生成的olive文件即可
     */
    @SuppressLint("NewApi")
    private fun exportOliveImage(): Boolean {
        GLog.d(TAG, LogFlag.DL, "convertImagerHdrToSdr")
        runCatching {
            oriOliveDecode?.getCoverStream()?.use { input ->
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = false
                }

                val offset = oriOlivePhoto?.getCoverImage()?.offset ?: 0
                val length = oriOlivePhoto?.getCoverImage()?.size?.toInt() ?: 0
                val buffer = ByteBuffer.allocate(length)
                input.skip(offset)
                val bytesRead = input.read(buffer.array(), 0, length)
                if (bytesRead != length) {
                    GLog.e(TAG, LogFlag.DL, "convertImagerHdrToSdr Failed to read the required number of bytes: $bytesRead != $length")
                    return false
                }
                buffer.position(0)
                buffer.asInputStream().use { tmpInputStream ->
                    val bitmap = BitmapFactory.decodeStream(tmpInputStream, null, options) ?: let {
                        GLog.e(TAG, LogFlag.DL, "convertImagerHdrToSdr error!! bitmap is null, oriOliveDecode:$oriOliveDecode")
                        return false
                    }
                    if (BuildWrapper.isAtLeastAndroidU()) {
                        // 产品当前定义，hdr的olive在转兼容后，只修改视频，原先的封面图和文件本身的hdr不改变，这里必须拷贝gaimap，如果只依赖后续的extend拷贝，会无法搬增益图
                        bitmap.gainmap = BitmapFactory.decodeFile(oriOliveFilePath)?.gainmap
                    }
                    BitmapSaveUtils.saveBitmap(
                        bitmap,
                        bitmap.colorSpace,
                        getTransformedOliveFile(),
                        Bitmap.CompressFormat.JPEG,
                        COMPRESS_QUALITY
                    )
                }
            } ?: let {
                GLog.e(TAG, LogFlag.DL, "convertImagerHdrToSdr error!! inputStreamSrc is null, oriOliveDecode:$oriOliveDecode")
                return false
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "convertImagerHdrToSdr error!! oriOliveDecode:$oriOliveDecode", it)
            return false
        }
        return true
    }

    /**
     * 将sdr的图片和视频通过sdk构成新的olive文件
     */
    private fun convertImageAndVideoToOlive(videoPath: String): Boolean {
        GLog.d(TAG, LogFlag.DL, "convertImageAndVideoToOlive videoPath:$videoPath")
        var result: Int? = null
        FileInputStream(videoPath).use { videoInputStream ->
            val coverTime = oriOlivePhoto?.coverTimeInUs ?: 0L
            result = oriOlivePhoto?.microVideo?.let { microVideo ->
                val videoResult = microVideo.mime?.let {
                    OLiveCreator.create(ContextGetter.context, getTransformedOliveFile().absolutePath)?.setVideoData(
                        videoInputStream, coverTime, it, oriOlivePhoto?.oLivePhotoOwner ?: OLIVE_OWNER)
                }
                OLiveEditor.create(ContextGetter.context, getTransformedOliveFile().absolutePath)?.let {
                    it.setOLiveEnable(true)
                    val startUs = microVideo.videoStartUs ?: return@let
                    val endUs = microVideo.videoEndUs ?: return@let
                    it.setVideoTrimRange(startUs, endUs)
                }
                videoResult
            }
            if (result != 0) {
                GLog.e(TAG, LogFlag.DL, "convertImageAndVideoToOlive error!! videoPath:$videoPath result is null")
            }
        }
        nextStep = PROGRESS_OLIVE_DONE
        return result == 0
    }

    /**
     * 初始化美摄的timeline
     * 需要先基于olive sdk将olive的视频导出，再把文件给到美摄进行初始化
     * 因为olive图片的分辨率和视频的分辨率没什么关系，要拿到准确的分辨率需要先导出视频，既然导出了视频，那么就直接让美摄基于这个视频进行sdr转码即可
     */
    @Suppress("TooGenericExceptionCaught")
    private fun initEngineTimeLine(filePath: String): Boolean {
        GLog.d(TAG, LogFlag.DL, "initEngineTimeLine filePath:$filePath")
        val tmpOliveExportVideoFile = getHdrVideoFile()
        deleteAndCreateNewFile(tmpOliveExportVideoFile)
        var outputStream: FileOutputStream? = null
        try {
            runCatching {
                outputStream = FileOutputStream(tmpOliveExportVideoFile.file)
                outputStream?.let { oriOliveDecode?.exportVideo(it) }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, "initEngineTimeLine outputStream fail, error", it)
            }
        } finally {
            IOUtils.closeQuietly(outputStream)
        }
        nextStep = PROGRESS_VIDEO_SDR_DONE

        if (!hdrVideoTransformEngine.initVideoFileInfo(tmpOliveExportVideoFile.absolutePath)) {
            return false
        } else {
            val videoWidth: Int = hdrVideoTransformEngine.videoFileWidth
            val videoHeight: Int = hdrVideoTransformEngine.videoFileHeight
            hdrVideoTransformEngine.initTimeLine(videoWidth, videoHeight, ENGINE_VIDEO_FPS).not()
        }
        hdrVideoTransformEngine.setHdrFx(tmpOliveExportVideoFile.absolutePath)

        //获取实况照片视频中的eis数据
        try {
            mVideoEisData = Mp4Util.getLivePhotoExtension(tmpOliveExportVideoFile.file, 0)
        } catch (e: IOException) {
            GLog.e(TAG, LogFlag.DL, "[initEngineTimeLine] get video eis data error$e")
        }

        try {
            val oriOliveFilePath = oriOliveFilePath ?: return false
            FileExtendedContainer().use { container ->
                container.setDataSource(path = oriOliveFilePath, openFileMode = OpenFileMode.MODE_READ)
                val hdrTransformData = container.getExtensionData(EXTEND_KEY_HDR_TRANSFORM_DATA)?.let { HdrTransformDataStruct(it) }?.toData()
                // 设置自定义图像处理器
                if (ApiLevelUtil.isAtLeastAndroidU() && (hdrTransformData != null)) {
                    val transformConfig = TransformConfig(
                        outputTextureIsSDR = true,
                        needBuddyFrame = false,
                    )
                    hdrVideoTransformEngine.adjustCustomFx(OliveHdrVideoFx(hdrTransformData, transformConfig), getVideoDisplayRegion())
                } else {
                    hdrVideoTransformEngine.adjustCustomFx(null, null)
                }
            }
        } catch (e: Exception) {
            GLog.e(TAG, LogFlag.DL, "initEngineTimeLine adjustCustomFx: ", e)
        }
        return true
    }

    /**
     * livephoto图片是带画框水印的，则返回视频中无水印的显示区域
     * @return 视频中无水印的显示区域(转换为美摄SDK坐标系)
     */
    private fun getVideoDisplayRegion(): FloatArray? {
        val fileUri = LocalMediaDataHelper.getLocalMediaItem(oriOliveFilePath?.let {
            LocalMediaDataHelper.getPathByFilePath(it)
        })?.getMediaUri() ?: run {
            GLog.e(TAG, LogFlag.DL, "getVideoDisplayRegion can not get file uri,return")
            return null
        }

        abilityBus.requireAbility(IWatermarkMasterAbility::class.java)?.use { ability ->
            ability.newWatermarkFileOperator(fileUri)?.use { operator ->
                val extInfo = operator.getAiMasterWatermarkExtInfo()?.takeIf {
                    it.isOnlyFrameWatermark()
                } ?: return null
                val contentRect = extInfo.videoDisplayRect ?: return null
                extInfo.background.takeIf {
                    ((it.backgroundType.toInt() == BG_TYPE_PURE_COLOR) && (it.bgColor == PURE_COLOR_WHITE)).not()
                }?.let {
                    // 非白色背景的水印可能会出现白边，扩展下 contentRect，确保不超出边界
                    contentRect.expand(EXTRA_OFFSET)
                }

                val videoWidth: Int = hdrVideoTransformEngine.videoFileWidth
                val videoHeight: Int = hdrVideoTransformEngine.videoFileHeight
                // 设置变换区域
                return contentRect.toNormalizedCoord(Size(videoWidth, videoHeight))?.run {
                    floatArrayOf(
                        left, top,      // 左上
                        left, bottom,    // 左下
                        right, bottom,   // 右下
                        right, top       // 右上
                    )
                }
            }
        }
        return null
    }

    /**
     * 生成最终的olive文件
     */
    private fun updateSavedOliveFile(dateTaken: Long): Boolean {
        GLog.d(TAG, LogFlag.DL, "updateSavedOliveFile dateTaken:$dateTaken")
        if (TextUtils.isEmpty(targetOliveFileName)) {
            GLog.w(TAG, LogFlag.DL, "updateSavedOliveFile targetOliveFileName is empty!")
            return false
        }

        val mediaFile: File = getTransformedOliveFile()
        if (!mediaFile.exists()) {
            GLog.e(TAG, LogFlag.DL, "updateSavedOliveFile failed. the file does not exists, mediaFile = $mediaFile")
            return false
        }

        val tmpFilename = targetOliveFileName?.replace(
            DEFAULT_OLIVE_TRANSFORM_SUFFIX_TMP,
            DEFAULT_OLIVE_SUFFIX
        )
        val newFile: File = File(targetOliveSaveFileDir, tmpFilename)
        val result = mediaFile.renameTo(newFile) && convertImageAndVideoToOlive(getSdrVideoFile().absolutePath)

        if (result) {
            targetOliveFileName = tmpFilename
            if (!isSaveToCacheTransform) {
                newFile.setLastModified(dateTaken)
            }
            oriOliveFilePath?.let { oriPath ->
                MigrateFileDataUtil.migrateExtensionData(oldPath = oriPath, newPath = newFile.absolutePath)
                //双视频：如果是双视频的实况需要在转兼容格式后删除小视频
                FileExtendedContainerExt.eraseExtensionData(context, newFile.absolutePath, listOf(
                    EXTEND_KEY_OLIVE_SUB_VIDEO_SIZE,
                    EXTEND_KEY_OLIVE_SUB_VIDEO
                ))
                MigrateFileDataUtil.migrateExifData(oldPath = oriPath, newPath = newFile.absolutePath) {
                    /* 产品当前定义，hdr的olive在转兼容后，只修改视频，原先的封面图和文件本身的hdr不改变，所以这里不需要修改tagFlag
                    val sdrUserComment = ExifUtils.replaceTagFlagInComment(it.userComment,
                        OplusExifTag.EXIF_TAG_ULTRA_HDR or OplusExifTag.EXIF_TAG_OPLUS_UHDR or OplusExifTag.EXIF_TAG_LOCAL_HDR, false)
                    it.userComment = sdrUserComment*/
                }
                OliveFileExtenderContainerHelper.refreshOliveVideoLength(newFile.absolutePath)
            }

            SPUtils.setString(context, null, KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF, null)
            SPUtils.setString(context, null, KEY_OLIVE_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF, null)
            return true
        }
        GLog.e(TAG, LogFlag.DL, "updateSavedOliveFile failed")
        return false
    }

    /**
     * 获取最终的olive文件的uri
     */
    private fun getTransformedOliveUri(): Uri? {
        GLog.d(TAG, LogFlag.DL, "getTransformedOliveUri")
        return if (isSaveToCacheTransform) {
            fromFile(context, getTransformedOliveFile())
        } else {
            savedOliveUri
        }
    }

    /**
     * 获取最终的olive文件
     */
    private fun getTransformedOliveFile(): File {
        GLog.d(TAG, LogFlag.DL, "getTransformedOliveFile targetOliveFileName:$targetOliveFileName")
        return File(targetOliveSaveFileDir, targetOliveFileName)
    }

    /**
     * 获取中间过程的SDR视频文件，这个是美摄导出的文件
     */
    private fun getSdrVideoFile(): File {
        GLog.d(TAG, LogFlag.DL, "getSdrVideoFile targetVideoFileName:$targetVideoFileName")
        return File(targetOliveSaveFileDir, targetVideoFileName)
    }

    /**
     * 获取中间过程的HDR视频文件，这个是olive sdk导出的
     */
    private fun getHdrVideoFile(): File {
        GLog.d(TAG, LogFlag.DL, "getHdrVideoFile name:$OLIVE_EXPORT_TEMP_FILE_START$targetVideoFileName")
        return File(targetOliveSaveFileDir, "$OLIVE_EXPORT_TEMP_FILE_START$targetVideoFileName")
    }

    /**
     * 扩展 ByteBuffer 以提供 InputStream 接口
     */
    private fun ByteBuffer.asInputStream(): InputStream {
        return object : InputStream() {
            override fun read(): Int {
                return if (position() < limit()) {
                    get().toInt() and NUMBER_0xFF
                } else {
                    -1
                }
            }

            override fun read(b: ByteArray, off: Int, len: Int): Int {
                val remaining = remaining()
                if (remaining == 0) return -1
                val actualLen = minOf(len, remaining)
                get(b, off, actualLen)
                return actualLen
            }
        }
    }

    companion object {
        private const val TAG = "HdrOliveTransformImpl"

        /**
         * 现在通过region限制后，视频和画框衔接处可能会出现白线，先在各边多补4个像素
         */
        private const val EXTRA_OFFSET = 4
        private const val COMPRESS_QUALITY: Int = 100
        private const val OLIVE_OWNER = "oplus"
        private const val OLIVE_EXPORT_TEMP_FILE_START = "tmp_"

        private const val REFRESH_PROGRESS_MESSAGE = 99999
        private const val REFRESH_PROGRESS_DELAY_MS = 30L
        private const val REFRESH_PROGRESS_STEP = 3
        private const val PROGRESS_VIDEO_HDR_DONE = 10
        private const val PROGRESS_VIDEO_SDR_DONE = 60
        private const val PROGRESS_FINISH = 100
        private const val PROGRESS_CANCEL = PROGRESS_FINISH - REFRESH_PROGRESS_STEP - 1
        private const val PROGRESS_OLIVE_DONE = PROGRESS_FINISH - REFRESH_PROGRESS_STEP - 1

        /**
         * 总体大小的倍数
         * 因为美摄要传入的必须是真实的文件名，所以需要把olive的视频真的生成一个临时文件，再基于临时文件转换，所以需要2倍的空间
         */
        private const val TOTAL_SIZE_TIMES_2 = 2
        private const val ENGINE_VIDEO_FPS = 30

        /**
         * 视频如果有eis数据，会以这个常量作为起始标记
         */
        private const val VIDEO_EIS_HEADER = "LivePhotoExtension"
    }
}