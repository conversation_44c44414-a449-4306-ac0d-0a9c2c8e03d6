/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IVideoTransform
 ** Description: 视频转码处理流程的接口定义
 **
 ** Version: 1.0
 ** Date: 2024/12/18
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  houdong<PERSON>@Apps.Gallery3D      2024/12/18       1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.channel

import android.net.Uri
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper.SingleFileScanCompleteListener
import com.oplus.gallery.framework.abilities.transform.TransformCallback

/**
 * 视频转码处理流程的接口定义
 * 并不是真的完成转码任务，而是对前后的工作进行封装，给到外部进行流程的调度
 */
interface IVideoTransform {

    /**
     * 检查是否支持转换
     *
     * 由于视频转换需要临时占用额外的内存空间，所以如果手机空间不满足了，那么就不能转换了
     *
     * @param totalVideoSize 整体视频的大小。因为在分享前需要先转换好，所以需要全部的空间
     * @param callback 不支持的回调
     * @return true表示支持转换
     */
    fun checkSupported(totalVideoSize: Long, callback: TransformCallback?): Boolean

    /**
     * 转换前的准备工作
     *
     * 对单个资源进行
     * @param uri 资源的uri
     * @param filePath 资源的文件路经
     */
    fun beforeTransform(uri: String?, filePath: String?): Boolean

    /**
     * 转换单个资源
     * @param dateTaken 开始转换的时间戳
     */
    fun startTransform(dateTaken: Long): Boolean

    /**
     * 转换的数据进行保存
     * @param lastModified 文件之前被修改的时间戳
     */
    fun saveTransform(lastModified: Long): Pair<Boolean, Uri?>

    /**
     * 转换后的处理工作
     * @param listener 完成后的回调
     */
    fun afterTransform(listener: SingleFileScanCompleteListener?)

    /**
     * 取消转码
     */
    fun cancelTransform()

    /**
     * 清空当前的数据，一般是在一次转换完之后，清空了准备进行下一次
     */
    fun clear()

    /**
     * 销毁相关的资源
     */
    fun release()
}