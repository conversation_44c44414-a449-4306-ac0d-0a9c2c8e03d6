/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - HdrVideoTransformChannel
 ** Description: HDR转码通道
 **
 ** Version: 1.0
 ** Date: 2022/10/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/10/26  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.channel.hdr

import android.content.Context
import com.oplus.gallery.abilities.transform.channel.AbstractVideoTransformChannel
import com.oplus.gallery.abilities.transform.channel.IVideoTransform
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession

class HdrVideoTransformChannel(
    context: Context,
    storage: TransformStorage,
    workerSession: WorkerSession
) : AbstractVideoTransformChannel(context, storage, workerSession) {
    override val tag: String = TAG

    override var videoTransformImpl: IVideoTransform? = null
        get() {
            if (field == null) {
                field = HDRVideoTransformImpl(
                    context,
                    storage,
                    hdrTransformEngineListener
                )
            }
            return field
        }

    companion object {
        private const val TAG = "HdrVideoTransformChannel"
    }
}