/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbstractVideoTransformChannel
 ** Description: 视频转码通道的基类
 **
 ** Version: 1.0
 ** Date: 2024/12/18
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  houdong<PERSON>@Apps.Gallery3D      2024/12/18       1.0     build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.channel

import android.content.Context
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.os.Message
import com.oplus.gallery.abilities.transform.channel.hdr.HdrVideoTransformChannel
import com.oplus.gallery.addon.osense.CpuFrequencyManager
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool
import com.oplus.gallery.framework.abilities.transform.TransformCallback
import com.oplus.gallery.framework.abilities.transform.TransformConstant.EXPORT_STATUS_CANCEL
import com.oplus.gallery.framework.abilities.transform.TransformConstant.EXPORT_STATUS_COMPLETE
import com.oplus.gallery.framework.abilities.transform.TransformConstant.EXPORT_STATUS_ERROR
import com.oplus.gallery.framework.abilities.transform.TransformConstant.Error.DURATION_TOO_LONG
import com.oplus.gallery.framework.abilities.transform.TransformConstant.Error.EXPORT_ERROR
import com.oplus.gallery.framework.abilities.transform.TransformConstant.Error.INIT_ERROR
import com.oplus.gallery.framework.abilities.transform.TransformConstant.Error.NOT_ENOUGH_SPACE
import com.oplus.gallery.framework.abilities.transform.TransformConstant.HDR_VIDEO_KEY
import com.oplus.gallery.framework.abilities.transform.TransformLimit
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.framework.abilities.transform.data.BaseTransformInfo
import com.oplus.gallery.framework.abilities.transform.data.VideoTransformInfo
import com.oplus.gallery.framework.abilities.transform.hdr.HdrVideoTransformListener
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.MeicamEdit
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 视频转码通道的基类
 * 内部主要封装了数据的基础统计和真实处理时回调的更新管理逻辑
 * 借鉴于[HdrVideoTransformChannel]，抽取公共逻辑的基础类，并没有最核心的[IVideoTransform]转换实现
 * @param context
 * @param storage 转码存储的类型，缓存还是文件
 * @param workerSession 线程会话
 */
abstract class AbstractVideoTransformChannel(
    context: Context,
    storage: TransformStorage,
    workerSession: WorkerSession
) : BaseTransformChannel(context, storage, workerSession, null) {
    /**
     * 视频转码的核心实现，主要通过这里进行转码
     */
    abstract var videoTransformImpl: IVideoTransform?
    abstract val tag: String

    private val hdrToSdrMap: ConcurrentHashMap<String, Uri> = ConcurrentHashMap<String, Uri>()
    private var hdrTransStatus = Pair<String, String?>(LaunchExitPopupConstant.Value.TRANSCODING_NO_NEED, null)
    private var convertedStatusMap: HashMap<String, Pair<String, String?>> = hashMapOf(HDR_VIDEO_KEY to hdrTransStatus)
    private var totalDuration: Long = 0
    private var totalVideoSize: Long = 0
    private var currentVideoInfo: VideoTransformInfo? = null
    private var currentPosition: Int = 0
    private var oldProgress: Int = 0
    private var curProgress: Int = 0

    private val mainHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(message: Message) {
            when (message.what) {
                MSG_TRANSFORM_END -> {
                    GLog.d(tag, LogFlag.DL, "MSG_TRANSFORM_END")
                    transformCallback?.onFinish(false)
                    convertedStatusMap[HDR_VIDEO_KEY] = Pair<String, String?>(LaunchExitPopupConstant.Value.TRANSCODING_SUCCESS, null)
                    release()
                    transformCallback?.onResult(hdrToSdrMap, convertedStatusMap)
                }
                MSG_TRANSFORM_NEXT -> {
                    GLog.d(tag, LogFlag.DL, "MSG_TRANSFORM_NEXT")
                    runOnMeicamEditThread(TAG_NEXT) {
                        videoTransformImpl?.clear()
                        currentPosition++
                        transformNextVideo()
                    }
                }

                // 只有转码成功或者不需要转码时有回调，转码错误直接取消，没有回调，后续埋点需要可以修改返回值的第二个参数
                MSG_TRANSFORM_EXIT_WITH_LISTENER -> {
                    GLog.d(tag, LogFlag.DL, "MSG_TRANSFORM_EXIT_WITH_LISTENER")
                    transformCallback?.onFinish(true)
                    convertedStatusMap[HDR_VIDEO_KEY] = Pair<String, String?>(LaunchExitPopupConstant.Value.NOT_TRANSCODING, null)
                    transformCallback?.onResult(hdrToSdrMap, convertedStatusMap)
                    hdrToSdrMap.clear()
                    release()
                }

                MSG_TRANSFORM_EXIT_IS_CANCELED -> {
                    GLog.d(tag, LogFlag.DL, "MSG_TRANSFORM_EXIT_IS_CANCELED")
                    transformCallback?.onFinish(true)
                    convertedStatusMap[HDR_VIDEO_KEY] = Pair<String, String?>(LaunchExitPopupConstant.Value.TRANSCODING_NO_NEED, null)
                    transformCallback?.onResult(hdrToSdrMap, convertedStatusMap)
                    hdrToSdrMap.clear()
                    release()
                }

                MSG_TRANSFORM_EXIT -> {
                    GLog.d(tag, LogFlag.DL, "MSG_TRANSFORM_EXIT")
                    transformCallback?.onError(message.arg1)
                    convertedStatusMap[HDR_VIDEO_KEY] = Pair<String, String?>(LaunchExitPopupConstant.Value.TRANSCODING_FAIL, null)
                    transformCallback?.onResult(hdrToSdrMap, convertedStatusMap)
                    hdrToSdrMap.clear()
                    release()
                }
            }
        }
    }

    protected val hdrTransformEngineListener: HdrVideoTransformListener = object : HdrVideoTransformListener {
        override fun onExportProgressChange(progress: Int) {
            setProgress(progress)
        }

        private fun resetTotalDurationIfNeed() {
            if (totalDuration.equals(0)) {
                GLog.d(tag, LogFlag.DL, "totalDuration is 0, reset 1")
                totalDuration = 1
            }
        }

        /**
         * save mp4 callback
         */
        override fun onExportStatusChange(state: Int) {
            GLog.d(tag, LogFlag.DL, "TransformEngineListener state = $state")
            when (state) {
                EXPORT_STATUS_COMPLETE -> onComplete()
                EXPORT_STATUS_CANCEL -> {
                    GLog.d(tag, LogFlag.DL, "onExportStatusChange, is canceled!")
                    cancelAndExit(isCanceled = true)
                }
                EXPORT_STATUS_ERROR -> cancelAndExit(isCanceled = false, EXPORT_ERROR)
            }
        }

        private fun onComplete() {
            runOnMeicamEditThread(TAG_COMPLETE) {
                val videoTransformImpl = videoTransformImpl ?: return@runOnMeicamEditThread
                currentVideoInfo?.let { info ->
                    val lastModified = File(info.filePath).lastModified()
                    val pair = videoTransformImpl.saveTransform(lastModified)
                    val isCacheTransform = pair.first
                    val savedUri = pair.second
                    if (!isCacheTransform) {
                        if (savedUri != null) {
                            hdrToSdrMap[info.filePath] = savedUri
                        } else {
                            cancelAndExit(isCanceled = false, DURATION_TOO_LONG)
                            return@runOnMeicamEditThread
                        }
                    } else if (savedUri != null) {
                        hdrToSdrMap[info.filePath] = savedUri
                    }
                }
                videoTransformImpl.afterTransform {
                    currentVideoInfo?.let {
                        resetTotalDurationIfNeed()
                        oldProgress += (it.duration / totalDuration.toFloat() * COMPILE_VIDEO_MAX).toInt()
                    }
                    mainHandler.removeMessages(MSG_TRANSFORM_NEXT)
                    mainHandler.sendEmptyMessage(MSG_TRANSFORM_NEXT)
                }
            }
        }

        private fun setProgress(progress: Int) {
            currentVideoInfo?.let {
                val newProgress = (it.duration / totalDuration.toFloat() * progress).toInt() + oldProgress
                if (newProgress >= COMPILE_VIDEO_MAX) {
                    curProgress = COMPILE_VIDEO_MAX
                } else {
                    if (curProgress < newProgress) {
                        curProgress = newProgress
                    }
                }
                transformCallback?.onProgress(curProgress)
            }
        }
    }

    override fun getType(): TransformType {
        GLog.d(tag, LogFlag.DL) { "getType" }
        return TransformType.HDR_VIDEO
    }

    override fun isSupport(targetPackageName: String): Boolean {
        GLog.d(tag, LogFlag.DL) { "isSupport" }
        return false
    }

    override fun setDataSource(infoList: MutableList<BaseTransformInfo>) {
        totalDuration = 0
        totalVideoSize = 0
        this.infoList.clear()
        this.infoList.addAll(infoList)
        for (info in this.infoList) {
            (info as? VideoTransformInfo)?.apply {
                totalDuration += duration
                totalVideoSize += size
            } ?: kotlin.run {
                GLog.e(tag, LogFlag.DL, "setDataSource. infoList is not HdrVideoTransformInfo!")
            }
        }
        GLog.d(tag, LogFlag.DL, "setDataSource. $totalDuration, $totalVideoSize")
    }

    override fun setLimit(limit: TransformLimit) {
        // do nothing
        GLog.d(tag, LogFlag.DL) { "setLimit" }
    }

    override fun cleanCache(): Boolean {
        return TransformCacheTool.cleanHdrCache()
    }

    override fun start(callback: TransformCallback?) {
        GLog.d(tag, LogFlag.DL) { "start" }
        oldProgress = 0
        curProgress = 0
        this.transformCallback = callback
        transformCallback?.onStart(0)
        runOnMeicamEditThread(TAG_START) {
            if (checkSupportTransform(callback)) {
                currentPosition = 0
                transformNextVideo()
            }
        }
    }

    override fun cancel() {
        GLog.d(tag, LogFlag.DL) { "cancel" }
        cancelAndExit(isCanceled = true)
    }

    override fun release() {
        GLog.d(tag, LogFlag.DL) { "release" }
        CpuFrequencyManager.setAction(CpuFrequencyManager.Action.STORAGE_BOOST, CpuFrequencyManager.TIMEOUT_CLOSE)
        runOnMeicamEditThread(TAG_RELEASE) {
            videoTransformImpl?.release()
            videoTransformImpl = null
            mainHandler.removeCallbacksAndMessages(null)
        }
    }

    /**
     * @param isCanceled 转码失败，是否是取消导致的转码失败，此时没有转码失败的toast
     */
    private fun cancelAndExit(isCanceled: Boolean, errCode: Int = -1) {
        workerSession.submit {
            videoTransformImpl?.cancelTransform()
        }
        when {
            isCanceled -> {
                mainHandler.removeMessages(MSG_TRANSFORM_EXIT_IS_CANCELED)
                mainHandler.sendEmptyMessage(MSG_TRANSFORM_EXIT_IS_CANCELED)
            }
            else -> {
                mainHandler.removeMessages(MSG_TRANSFORM_EXIT)
                val msg = Message()
                msg.what = MSG_TRANSFORM_EXIT
                msg.arg1 = errCode
                mainHandler.sendMessage(msg)
            }
        }
    }

    /**
     * 当有多个视频需要转码的时候，需要循环执行transformNextVideo方法
     */
    private fun transformNextVideo() {
        if (currentPosition < infoList.size) {
            (infoList[currentPosition] as? VideoTransformInfo)?.let {
                currentVideoInfo = it
                videoTransformImpl?.let { hdrVideoTransformImpl ->
                    if (hdrVideoTransformImpl.beforeTransform(it.uri.toString(), it.filePath)) {
                        hdrVideoTransformImpl.startTransform(it.dateTaken)
                    } else {
                        GLog.e(tag, LogFlag.DL, "init videoTransformManager error!")
                        cancelAndExit(isCanceled = false, INIT_ERROR)
                    }
                }
            }
        } else {
            mainHandler.removeMessages(MSG_TRANSFORM_END)
            mainHandler.sendEmptyMessageDelayed(MSG_TRANSFORM_END, TimeUtils.TIME_1_SEC_IN_MS.toLong())
        }
    }

    private fun checkSupportTransform(callback: TransformCallback?): Boolean {
        videoTransformImpl?.let { hdrVideoTransformImpl ->
            if (!hdrVideoTransformImpl.checkSupported(totalVideoSize, callback)) {
                GLog.w(tag, LogFlag.DL, "checkSupportTransform, storage is not enough, exit!")
                cancelAndExit(isCanceled = false, NOT_ENOUGH_SPACE)
                return false
            }
        }
        return true
    }

    /**
     * 在美摄编辑线程中执行
     *
     * 任何调美摄的关键动作，都必须post到美摄编辑线程执行，否则可能出现多线程调用导致美摄native crash！
     */
    private fun runOnMeicamEditThread(
        functionTag: String,
        errorAction: ((Throwable) -> Unit)? = null,
        runAction: () -> Unit
    ) {
        AppScope.launch(Dispatchers.MeicamEdit) {
            GTrace.trace("${tag}_$functionTag") {
                val start = System.currentTimeMillis()
                kotlin.runCatching {
                    runAction()
                }.onFailure {
                    GLog.e(tag, LogFlag.DL) { "<$functionTag>  transform error $it" }
                    errorAction?.invoke(it)
                }
                GLog.d(tag, LogFlag.DL) { "<$functionTag> timeSpent ${System.currentTimeMillis() - start}" }
            }
        }
    }

    companion object {
        private const val MSG_TRANSFORM_END = 1
        private const val MSG_TRANSFORM_NEXT = 2
        private const val MSG_TRANSFORM_EXIT_WITH_LISTENER = 3
        private const val MSG_TRANSFORM_EXIT_IS_CANCELED = 4
        private const val MSG_TRANSFORM_EXIT = 5
        private const val COMPILE_VIDEO_MAX = 100
        private const val TAG_START = "start"
        private const val TAG_COMPLETE = "complete"
        private const val TAG_NEXT = "next"
        private const val TAG_RELEASE = "release"
    }
}