/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TransformAbilityImpl
 ** Description: 转码能力实现类
 **
 ** Version: 1.0
 ** Date: 2022/10/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/10/26  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform

import android.content.Context
import com.oplus.appability.AbsAppAbility
import com.oplus.gallery.abilities.transform.channel.hdr.HdrVideoTransformChannel
import com.oplus.gallery.abilities.transform.channel.heif.HeifTransformChannel
import com.oplus.gallery.abilities.transform.channel.livephoto.HdrOliveTransformChannel
import com.oplus.gallery.framework.abilities.transform.ITransformAbility
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.channel.ITransformChannel
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession

/**
 * 转码能力
 */
class TransformAbilityImpl : AbsAppAbility(), ITransformAbility {
    override fun isSupportTransform(type: TransformType): Boolean {
        return when (type) {
            TransformType.HDR_VIDEO -> true
            TransformType.HEIF -> true
            TransformType.OLIVE -> true
        }
    }

    override fun createChannel(
        type: TransformType,
        context: Context,
        storage: TransformStorage,
        workerSession: WorkerSession
    ): ITransformChannel {
        return when (type) {
            TransformType.HDR_VIDEO -> HdrVideoTransformChannel(context, storage, workerSession)
            TransformType.HEIF -> HeifTransformChannel(context, storage, workerSession)
            TransformType.OLIVE -> HdrOliveTransformChannel(context, storage, abilityBus, workerSession)
        }
    }

    override fun close() = Unit

    override val domainInstance: AutoCloseable
        get() = this

    companion object {
        private const val TAG = "TransformAbilityImpl"
    }
}