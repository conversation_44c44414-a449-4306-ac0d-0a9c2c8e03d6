/***************************************************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - HEIFTranscodingJpegTask.java
 ** Description: Task for Transcoding Heif to Jpeg.
 ** Version: 1.0
 ** Date : 2020/05/08
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  Peng.<PERSON>@Rom.Apps.Gallery    2020/05/08        1.0            build this module
 **************************************************************************************************/

package com.oplus.gallery.abilities.transform.channel.heif;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ColorSpace;
import android.text.TextUtils;
import androidx.annotation.Nullable;

import com.oplus.gallery.abilities.transform.util.MigrateFileDataUtil;
import com.oplus.gallery.addon.media.OplusHeifConverterWrapper;
import com.oplus.gallery.foundation.exif.R;
import com.oplus.gallery.foundation.exif.oplus.OplusExifInterface;
import com.oplus.gallery.foundation.exif.raw.ExifUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.graphic.BitmapSaveUtils;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.JobContext;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import java.io.FileInputStream;
import java.io.FileOutputStream;

public class HEIFTranscodingJpegTask implements Job<File> {
    private static final String[] TAGFLAGS_PREFIXES = ContextGetter.context.getResources().getStringArray(R.array.base_tagflags_prefixes);
    private static final String TAG = "HeifTranscodingJpegTask";
    private static final String HEIF_STRCAT_CHAR = "_";
    private static final String JPEG_BITMAP_SUFFIX = ".jpg";
    private final String mImagePath;
    private final File mFileDir;
    private final int mQuality;
    private final int mRotation;

    public HEIFTranscodingJpegTask(String imagePath, File fileDir, int quality, int rotation) {
        mImagePath = imagePath;
        mFileDir = fileDir;
        mQuality = quality;
        mRotation = rotation;
    }

    @Override
    public File call(JobContext jc) {
        if (isAbort(jc)) {
            GLog.e(TAG, "Heif transcoding Failed");
            return null;
        }

        if ((mFileDir == null) || TextUtils.isEmpty(mImagePath)) {
            GLog.e(TAG, "Heif transcoding Failed");
            return null;
        }

        long start = System.currentTimeMillis();

        String fileNameSrc = FilePathUtils.getFileName(mImagePath);
        int index = fileNameSrc.indexOf('.');
        String prePath = TextUtil.EMPTY_STRING;
        if (index > 0) {
            prePath = fileNameSrc.substring(0, index);
        }
        prePath = prePath + HEIF_STRCAT_CHAR + MigrateFileDataUtil.getOutputCacheFileName();
        String savePath = mFileDir.getAbsolutePath() + File.separator + prePath
                + JPEG_BITMAP_SUFFIX;

        File cacheTempFile = new File(savePath);
        if (cacheTempFile.exists() && cacheTempFile.isDirectory()) {
            if (!cacheTempFile.delete()) {
                GLog.w(TAG, "HeifTranscodingJpegTask, delete fail!");
            } else {
                cacheTempFile = new File(savePath);
            }
        }
        cacheTempFile = convert(savePath, cacheTempFile);

        MigrateFileDataUtil.createNoMediaFile(mFileDir.getPath());

        long end = System.currentTimeMillis();
        GLog.d(TAG, "Transcoding HEIF use time : " + (end - start)
                + "; isAbort : " + isAbort(jc));
        return cacheTempFile;
    }

    @Nullable
    private File convert(String savePath, File cacheTempFile) {
        if (!cacheTempFile.exists() || !cacheTempFile.isFile()) {
            FileOutputStream out = null;
            FileInputStream inputStream = null;
            try {
                out = new FileOutputStream(cacheTempFile.getFile());
                inputStream = new FileInputStream(mImagePath);
                boolean success = OplusHeifConverterWrapper.convertHeifToJpegFromStream(inputStream,
                        mQuality, out);
                GLog.d(TAG, "Transcoding success:" + success);

                if (!success) {
                    // 打开过的流使用过可能会被关，继续使用概率存在问题，需要先关闭在重新创建新的流
                    IOUtils.closeQuietly(inputStream, out);
                    inputStream = new FileInputStream(mImagePath);
                    out = new FileOutputStream(cacheTempFile.getFile());
                    Bitmap bitmap = BitmapFactory.decodeStream(inputStream);
                    ColorSpace srcColorSpace = bitmap.getColorSpace();
                    // 保存失败会删除tempFile
                    BitmapSaveUtils.saveBitmap(bitmap, srcColorSpace, cacheTempFile, Bitmap.CompressFormat.JPEG, mQuality);
                    success = cacheTempFile.exists();
                }

                if (!success) {
                    cacheTempFile = null;
                }

                // 转码成功后需对其迁移信息（扩展信息和exif信息）
                if (success) {
                    MigrateFileDataUtil.migrateExtensionData(mImagePath, savePath);
                    MigrateFileDataUtil.migrateExifData(mImagePath, savePath, (ExifUtils.ExifEntry exifEntry) -> {
                        int orientation = OplusExifInterface.getOrientationValueForRotation(mRotation);
                        exifEntry.setOrientation(String.valueOf(orientation));
                        return null;
                    });
                }
            } catch (Exception e) {
                GLog.e(TAG, "Unable to transcode stream: " + e);
                cacheTempFile = null;
            } finally {
                IOUtils.closeQuietly(out, inputStream);
            }
        }
        return cacheTempFile;
    }

    private boolean isAbort(JobContext jc) {
        return (jc != null) && jc.isCancelled();
    }
}
