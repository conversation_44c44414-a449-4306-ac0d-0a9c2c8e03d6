/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - HdrOliveTransformChannel
 ** Description: olivePhoto分享时转化
 **
 ** Version: 1.0
 ** Date: 2024/11/1 10:33
 ** Author: houdonggu@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** houdo<PERSON><PERSON>@Apps.Gallery3D       2024/11/1		  1.0		 HdrType
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.channel.livephoto

import android.content.Context
import com.oplus.appability.IAbilityBus
import com.oplus.gallery.abilities.transform.channel.AbstractVideoTransformChannel
import com.oplus.gallery.abilities.transform.channel.IVideoTransform
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool
import com.oplus.gallery.framework.abilities.transform.TransformCallback
import com.oplus.gallery.framework.abilities.transform.TransformLimit
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.framework.abilities.transform.data.BaseTransformInfo
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession

/**
 * olivePhoto分享时转化
 * 1. 如果是sdr的olivePhoto，不用变化，直接输出文件即可
 * 2. 如果是hdr的olivePhoto，需要将其中的图片和视频从hdr转换成sdr，再通过olive sdk将这两个封装为文件进而输出
 * 2.1 图片hdr转成sdr，直接获取主图即可
 * 2.2 视频hdr转成sdr，通过美摄sdk完成。需要先生成临时文件，再给到美摄进行转换
 *
 * 特殊的：当前的美摄视频处理不支持多线程，所以整个过程只能单线程运行
 */
internal class HdrOliveTransformChannel(
    context: Context,
    storage: TransformStorage,
    private val abilityBus: IAbilityBus,
    workerSession: WorkerSession
) : AbstractVideoTransformChannel(context, storage, workerSession) {
    override val tag: String = TAG

    override var videoTransformImpl: IVideoTransform? = null
        get() {
            if (field == null) {
                field = HdrOliveTransformImpl(context, storage, abilityBus, hdrTransformEngineListener)
            }
            return field
        }

    override fun getType(): TransformType = TransformType.OLIVE

    override fun cleanCache(): Boolean {
        GLog.d(tag, LogFlag.DL) { "cleanCache" }
        return TransformCacheTool.cleanOLiveCache()
    }

    companion object {
        private const val TAG = "HdrOliveTransformChannel"
    }
}