/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - OliveHdrVideoFx.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/29
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/29		1.0			OPLUS_ARCH_EXTENDS
 * <EMAIL>		2024/12/17		2.0			改路经
 ******************************************************************************/

package com.oplus.gallery.abilities.transform.channel.livephoto

import android.opengl.GLES30
import android.os.Build
import androidx.annotation.RequiresApi
import com.meicam.sdk.NvsCustomVideoFx
import com.meicam.sdk.NvsCustomVideoFx.FxRequirement
import com.meicam.sdk.NvsCustomVideoFx.NV_VIDEO_FRAME_FORMAT_RGBA16F
import com.meicam.sdk.NvsCustomVideoFx.RendererExt
import com.oplus.gallery.foundation.codec.extend.HdrTransformData
import com.oplus.gallery.foundation.hdrtransform.HdrTransformFactory
import com.oplus.gallery.foundation.hdrtransform.HdrTransformType
import com.oplus.gallery.foundation.hdrtransform.data.InitParam
import com.oplus.gallery.foundation.hdrtransform.data.TransformGpuData
import com.oplus.gallery.foundation.hdrtransform.data.TransformParam
import com.oplus.gallery.foundation.opengl2.GlUtil
import com.oplus.gallery.foundation.opengl2.program.singleInt
import com.oplus.gallery.foundation.opengl2.render.renderer.helper.RenderHelper
import com.oplus.gallery.foundation.opengl2.render.renderer.helper.RenderHelper.toGainGpuData
import com.oplus.gallery.foundation.opengl2.render.renderer.helper.RenderHelper.toGpuData
import com.oplus.gallery.foundation.opengl2.render.renderer.helper.addToPool
import com.oplus.gallery.foundation.opengl2.render.renderer.image.UhdrRenderer
import com.oplus.gallery.foundation.opengl2.render.renderer.normal.GamutRenderer
import com.oplus.gallery.foundation.opengl2.render.renderer.normal.Renderer
import com.oplus.gallery.foundation.opengl2.texture.GainRawTexture
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.opengl2.texture.IdTexture
import com.oplus.gallery.foundation.opengl2.texture.RawTexture
import com.oplus.gallery.foundation.opengl2.texture.TexConfig
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.SRGB
import com.oplus.gallery.framework.abilities.hardware.IScreen.Companion.DEFAULT_DISPLAY_RATIO
import com.oplus.gallery.framework.abilities.hardware.IScreen.Companion.HLG_HDR_MAX_DISPLAY_RATIO

/**
 * Olive的视频HDR，执行下变换操作的逻辑处理
 *
 * @param data 下变换算法需要的参数
 * @param transformConfig 美摄执行下变换的配置
 */
@RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
class OliveHdrVideoFx(private val data: HdrTransformData, private val transformConfig: TransformConfig) : RendererExt {
    private val transform = HdrTransformFactory.createHdrTransform(HdrTransformType.GPU)
    private val renderArgs = RenderHelper.obtainArgs()

    /**
     *  美摄SDK对自定义视频特效调用此方法以便让用户初始化一些资源。
     *  这个方法在自定义视频特效的生命周期里最多只会被调用一次。如果该特效从未被真正使用过，则这个方法将不会被调用。
     *
     *  **注意**：这个方法是在美摄SDK引擎的特效渲染线程里调用，并且当前线程已经绑定了一个EGL Context。
     */
    override fun onInit() {
        // tmcMode不能使用AI_TM，需要强制使用CV_TM；处理olive视频时，对点灯（lightUpEnable）关闭，性能处理不来
        transform.init(InitParam(InitParam.TMC_CV, data.cameraMode, data.gammaEnable, data.lutEnable, data.sharpenEnable, false))
        // olive播放的时候才会回调[onPreloadResources]，转兼容之类的场景就需要主动调用了
        onPreloadResources()
    }

    /**
     *  美摄SDK对自定义视频特效调用此方法以便让用户清理资源。
     *  这个方法在自定义视频特效的生命周期里最多只会被调用一次，而且一定会在onInit之后调用，如果onInit没有被调用则也不会调用该方法。
     *
     *  **注意**：这个方法是在美摄SDK引擎的特效渲染线程里调用，并且当前线程已经绑定了一个EGL Context。
     */
    override fun onCleanup() {
        transform.destroy()
    }

    /**
     *  美摄SDK对自定义视频特效调用此方法以便让进行一些资源预处理。
     *  这个方法在自定义视频特效的生命周期里会被多次调用，而且一定会在onInit之后调用，一般来讲是在每次播放时间线之前调用。
     *  一般来讲用户需要在此函数里面进行诸如构建shader program的工作。
     *
     *  **注意**：这个方法是在美摄SDK引擎的特效渲染线程里调用，并且当前线程已经绑定了一个EGL Context。
     */
    override fun onPreloadResources() {
        Renderer.get(UhdrRenderer::class.java).install()
        Renderer.get(GamutRenderer::class.java).install()
    }

    /**
     *  美摄SDK对自定义视频特效调用此方法以便对输入视频帧进行特效处理。
     *  用户实现这个方法对输入视频帧进行处理并将结果写入到输出视频帧中去以便完成特效渲染。
     *
     *  **注意**：
     *  1. 这个方法是在美摄SDK引擎的特效渲染线程里调用，并且当前线程已经绑定了一个EGL Context。
     *  当前线程已经绑定了一个FBO，用户只需在相应的attachment point上面绑定color buffer, depth buffer...即可。
     *
     *  2. 请务必在渲染完成后，将OpenGL ES context的状态复位到默认状态，比如用户渲染过程中调用了glEnable(GL_BLEND),
     *  则渲染完成后一定要调用glDisable(GL_BLEND),因为默认状态下blend是关闭的。关于OpenGL ES context的默认状态。
     *  请参考[OpenGL网站](https://www.khronos.org/opengles/)
     *
     *  **警告**：如果渲染完成后，没有将OpenGL ES context的状态复位到默认状态，则可能导致后续特效渲染发生错误！
     */
    override fun onRender(renderContext: NvsCustomVideoFx.RenderContext) {
        val meicamFboId = singleInt { GLES30.glGetIntegerv(GLES30.GL_FRAMEBUFFER_BINDING, it, 0) }

        // 回收上次使用的纹理
        RenderHelper.recycleTexture(renderArgs)

        // 算法要求的输出的纹理色域是P3+sRGB
        val outputAlgoTexture = RawTexture(renderContext.outputVideoFrame.width, renderContext.outputVideoFrame.height, DISPLAY_P3)
            .addToPool(renderArgs)
            .also(ITexture::load)

        val outputAlgoGainRawTexture = GainRawTexture(renderContext.outputVideoFrame.width / 2, renderContext.outputVideoFrame.height / 2)
            .addToPool(renderArgs)
            .also(ITexture::load)

        // 将美摄的纹理转换成相册的纹理格式，这里是假定美摄提供的纹理是HDR的，按照与美摄的约定，其使用的gamma是sRGB，纹理归一化范围是[0,4.9]
        val inputTexture = renderContext.inputVideoFrame.toTexture(HLG_HDR_MAX_DISPLAY_RATIO)
        // 转换色域，转换成算法需要的色域，美摄提供的纹理为sRGB的色域+sRGB的gamma
        val gamutTexture = RenderHelper.transformColorSpace(renderArgs, inputTexture, ColorSpaceExt.DISPLAY_P3_HLG!!)

        // 构造下变换参数
        val inputData = gamutTexture.toGpuData()
        val outputData = outputAlgoTexture.toGpuData()
        val gainTextureData = outputAlgoGainRawTexture.toGainGpuData()
        val transformParam = TransformParam(
            TransformGpuData(inputData, outputData, gainTextureData),
            data.cameraMode,
            data.luxIndex,
            data.faceLumaRatio,
            data.faceNum,
            data.zoomFactor,
            data.hlgDstGammaTable,
            data.srgbDstGammaTable,
            data.sharpenRadius,
            data.sharpenSigma,
            data.ccmData,
            data.imageCoeff
        )
        // 执行下变换，得到
        transform.process(transformParam)
        outputAlgoGainRawTexture.metadataPack = gainTextureData.metadataPack

        // 合成hdr
        val hdrTexture = RenderHelper.combineHdr(renderArgs, outputAlgoTexture, outputAlgoGainRawTexture, transformConfig.hdrSdrRatioFunc())

        // 转换美摄的输出纹理为相册的格式，这里暂时假定要输出美摄的纹理是SDR的，故clamp的约束范围要限制到[0,1]
        val outputTexture = renderContext.outputVideoFrame.toTexture(transformConfig.hdrSdrRatioFunc())
        // 转换到美摄所需要的纹理格式
        RenderHelper.transformColorSpace(renderArgs, hdrTexture, SRGB, outputTexture)
//        transformColorSpace(outputAlgoTexture, ColorSpaceExt.SRGB, outputTexture)

        // 需要将fbo绑定回去
        GLES30.glBindFramebuffer(GLES30.GL_FRAMEBUFFER, meicamFboId)
        GlUtil.checkGlError()
    }

    override fun onClearCacheResources() {
        RenderHelper.release(renderArgs)
    }

    override fun onCollectionReq(): FxRequirement {
        return FxRequirement().apply {
            needBuddyFrame = transformConfig.needBuddyFrame
            outputTextureIsSDR = transformConfig.outputTextureIsSDR
        }
    }

    /**
     * 将[NvsCustomVideoFx.VideoFrame]转换成[ITexture]
     *
     * @param clamp 纹理归一化的范围
     */
    private fun NvsCustomVideoFx.VideoFrame.toTexture(clamp: Float): ITexture {
        val texConfig = if (texFormat == NV_VIDEO_FRAME_FORMAT_RGBA16F) TexConfig.RGBA_F16 else TexConfig.ARGB_8888

        return IdTexture(texId, width, height, texConfig, SRGB, clamp).toVirtual().apply {
            load()
        }
    }

    companion object {
        private const val TAG = "OliveHdrVideoFx"
    }
}

/**
 * 美摄执行下变换的配置类。
 *
 * @param outputTextureIsSDR 输出的文件是否是SDR图
 * @param hdrSdrRatioFunc 获取当前HDR/SDR比例的回调方法，默认实现为回调1.0f，即显示sdr效果
 * @param needBuddyFrame xxx
 */
class TransformConfig(
    var outputTextureIsSDR: Boolean = false,
    val hdrSdrRatioFunc: () -> Float = { DEFAULT_DISPLAY_RATIO },
    var needBuddyFrame: Boolean = false
)