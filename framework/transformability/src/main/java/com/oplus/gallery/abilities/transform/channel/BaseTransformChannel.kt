/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseTransformChannel
 ** Description: 转码通道抽象类
 **
 ** Version: 1.0
 ** Date: 2022/10/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/10/26  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.channel

import android.content.Context
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool.createCacheFolder
import com.oplus.gallery.framework.abilities.transform.TransformCallback
import com.oplus.gallery.framework.abilities.transform.TransformState
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.channel.ITransformChannel
import com.oplus.gallery.framework.abilities.transform.data.BaseTransformInfo
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession

abstract class BaseTransformChannel(
    protected val context: Context,
    protected val storage: TransformStorage,
    protected val workerSession: WorkerSession,
    cacheFolderPath: String?
) : ITransformChannel {

    /*存储要转码的媒体信息*/
    protected val infoList = mutableListOf<BaseTransformInfo>()
    /*缓存文件目录*/
    protected val cacheFolder = createCacheFolder(cacheFolderPath)
    /*转码配置信息*/
    protected val configs = mutableMapOf<String, Any>()
    /*转码回调*/
    protected var transformCallback: TransformCallback? = null
    /*当前转码状态*/
    protected var currentState = TransformState.IDLE

    override fun setConfigurations(config: MutableMap<String, Any>) {
        configs.putAll(config)
    }

    companion object {
        private const val TAG = "BaseTransformChannel"
    }
}