/*********************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  - HDRVideoTransformEngine.java
 * * Description : interface of meicaim sdk
 * * Version     : 1.0
 * * Date        : 2021/04/06
 * * Author      : xiang.cheng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  xiang.cheng@Apps.Gallery3D  2021/04/06  1.0       build this model
 ***********************************************************************/

package com.oplus.gallery.abilities.transform.channel.hdr;

import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE;
import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_HARDWARE_ENCODER;
import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DONT_USE_INPUT_SURFACE;
import static com.meicam.sdk.NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE;
import static com.meicam.sdk.NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT;
import static com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_BT2020;
import static com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_DISPLAY_P3;
import static com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_HLG;
import static com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_SDR_VIDEO;
import static com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_ST2084;
import static com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_NONE;
import static com.oplus.gallery.framework.abilities.transform.TransformConstant.EXPORT_STATUS_CANCEL;
import static com.oplus.gallery.framework.abilities.transform.TransformConstant.EXPORT_STATUS_COMPLETE;
import static com.oplus.gallery.framework.abilities.transform.TransformConstant.EXPORT_STATUS_ERROR;
import static com.oplus.gallery.framework.abilities.transform.TransformConstant.EXPORT_STATUS_FINISH;

import android.content.Context;
import android.graphics.ColorSpace;

import androidx.annotation.Nullable;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsAudioResolution;
import com.meicam.sdk.NvsCustomVideoFx;
import com.meicam.sdk.NvsRational;
import com.meicam.sdk.NvsSize;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.meicam.sdk.NvsVideoResolution;
import com.meicam.sdk.NvsVideoStreamInfo;
import com.meicam.sdk.NvsVideoTrack;
import com.oplus.gallery.foundation.hdrtransform.HdrTransformFactory;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.version.ApiLevelUtil;
import com.oplus.gallery.framework.abilities.transform.hdr.HdrVideoTransformListener;
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

import java.util.Hashtable;
import java.util.Locale;

public class HDRVideoTransformEngine {

    private static final String TAG = "HDRVideoTransformEngine";
    private static final String HDR_BUILTIN_FX = "Hdr";
    private static final String VIDEO_TYPE_KEY = "Type";
    private static final String HLG_TYPE_VALUE = "hlg";
    private static final int VIDEO_WIDTH_DIV_ZERO_VALUE = 4;
    private static final int VIDEO_HEIGHT_DIV_ZERO_VALUE = 2;
    private static final int NUM_ZERO = 0;
    private static final int SAMPLE_RETE = 44100;
    private static final int CHANNEL_COUNT = 2;
    private HdrVideoTransformListener mTransformEngineListener;
    private NvsStreamingContext mNvsContext;
    private NvsTimeline mNvsTimeLine;
    private NvsVideoTrack mVideoTrack;
    private NvsVideoClip mNvsVideoClip;
    private int mVideoWidth = 0;
    private int mVideoHeight = 0;
    private ColorSpace mColorSpace = ColorSpaceExt.getSRGB();
    private int mHdrType = HDR_TYPE_NONE;

    // callback
    private HDRVideoTransformCallback.SDKCallbackInterface mSDKCallback = new HDRVideoTransformCallback.SDKCallbackInterface() {
        //NvsStreamingContext.CompileCallback
        @Override
        public void onCompileProgress(NvsTimeline timeline, int progress) {
            if (mNvsTimeLine != timeline) {
                return;
            }
            if (mTransformEngineListener != null) {
                mTransformEngineListener.onExportProgressChange(progress);
            }
        }

        @Override
        public void onCompileFinished(NvsTimeline timeline) {
            if (mNvsTimeLine != timeline) {
                return;
            }
            if (mTransformEngineListener != null) {
                mTransformEngineListener.onExportStatusChange(EXPORT_STATUS_FINISH);
            }
        }

        @Override
        public void onCompileFailed(NvsTimeline timeline) {
            if (mNvsTimeLine != timeline) {
                return;
            }
            if (mTransformEngineListener != null) {
                mTransformEngineListener.onExportStatusChange(EXPORT_STATUS_ERROR);
            }
        }

        //NvsStreamingContext.CompileCallback2
        @Override
        public void onCompileCompleted(NvsTimeline timeline, boolean isCanceled) {
            if (mNvsTimeLine != timeline) {
                return;
            }
            if (mTransformEngineListener != null) {
                mTransformEngineListener.onExportStatusChange(isCanceled
                    ? EXPORT_STATUS_CANCEL : EXPORT_STATUS_COMPLETE);
            }
        }
    };

    public HDRVideoTransformEngine(Context context, HdrVideoTransformListener listener) {
        mTransformEngineListener = listener;
        int debugLevel = GProperty.DEBUG ? NvsStreamingContext.DEBUG_LEVEL_DEBUG : NvsStreamingContext.DEBUG_LEVEL_ERROR;
        NvsStreamingContext.setDebugLevel(debugLevel);
        mNvsContext = MeicamContextAliveCounter.INSTANCE.requestContext(context, TAG);
        if (mNvsContext == null) {
            GLog.e(TAG, "HDRVideoTransformEngine mNvsContext is null.");
        }
    }

    public boolean initVideoFileInfo(String filePath) {
        if (mNvsContext == null) {
            return false;
        }
        NvsAVFileInfo info = mNvsContext.getAVFileInfo(filePath);
        if (info != null) {
            // get colorspace
            int colorPrimaries = info.getVideoStreamColorPrimaries(0);
            int colorTranser = info.getVideoStreamColorTranfer(0);
            if ((colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTranser == COLOR_TRANSFER_HLG)) {
                mColorSpace = ColorSpaceExt.getBT2020_HLG();
            } else if ((colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTranser == COLOR_TRANSFER_ST2084)) {
                mColorSpace = ColorSpaceExt.getBT2020_PQ();
            } else if ((colorPrimaries == COLOR_PRIMARIES_DISPLAY_P3) && (colorTranser == COLOR_TRANSFER_SDR_VIDEO)) {
                mColorSpace = ColorSpaceExt.getDISPLAY_P3();
            }

            mHdrType = info.getVideoStreamHDRType(0);

            NvsSize size = info.getVideoStreamDimension(0);
            if (size != null) {
                int rotation = info.getVideoStreamRotation(0);
                if ((rotation == NvsVideoStreamInfo.VIDEO_ROTATION_90) || (rotation == NvsVideoStreamInfo.VIDEO_ROTATION_270)) {
                    mVideoHeight = size.width;
                    mVideoWidth = size.height;
                } else {
                    mVideoHeight = size.height;
                    mVideoWidth = size.width;
                }
                GLog.d(TAG, "initVideoFileInfo()  mVideoHeight:" + mVideoHeight
                        + ", mVideoWidth:" + mVideoWidth
                        + ", rotation:" + rotation
                        + ", hdrType:" + mHdrType
                        + ", avFileType:" + info.getAVFileType());
                return true;
            }
        }
        return false;
    }

    @Nullable
    public ColorSpace getVideoColorSpace() {
        return mColorSpace;
    }

    /**
     * 获取视频的 hdr 类型
     * @return mHdrType hdr 类型，见 NvsVideoStreamInfo
     */
    public int getVideoHDRType() {
        return mHdrType;
    }

    public void setHdrFx(String mediaPath) {
        if ((mediaPath == null) || (mVideoTrack == null)) {
            GLog.d(TAG, "mediaPath or mVideoTrack is null.");
            return;
        }
        mNvsVideoClip = mVideoTrack.appendClip(mediaPath);
    }

    /**
     * 调节下变化。在视频转换时，可以设置自定义的下变化算法[hdrVideoFx]。如果没有自定义算法，那么就禁用色域转换
     * 在某些情况下，美摄默认实现的下变化算法可能不合适，那么就需要使用自定义的
     * @param hdrVideoFx 下变化算法的实现，可空
     * @param hdrRegion 视频显示区域，默认是全部区域，若是视频带了画框类水印则画框部分不需要执行下变换
     */
    public void adjustCustomFx(@Nullable NvsCustomVideoFx.RendererExt hdrVideoFx, @Nullable float[] hdrRegion) {
        if (ApiLevelUtil.isAtLeastAndroidU() && (hdrVideoFx != null) && HdrTransformFactory.isSupportHdrTransform()) {
            NvsVideoFx fx = mNvsVideoClip.appendCustomFx(hdrVideoFx);
            if (hdrRegion != null) {
                fx.setRegional(true);
                fx.setRegion(hdrRegion);
            }
            // 自己要做HDR到SDR变化，就禁止SDK内部做。必须调用！！
            mNvsVideoClip.disableHDRTonemappingToSDR(true);
        } else {
            mNvsVideoClip.disableClipColorPrimariesConvert(true);
        }
    }

    public boolean saveTransformVideo(String mediaPath, long dateTaken) {
        return saveTransformVideo(mediaPath, dateTaken, null);
    }

    public boolean saveTransformVideo(String mediaPath, long dateTaken, Hashtable<String, Object> compileConfiguration) {
        final NvsStreamingContext nvsContext = mNvsContext;
        if (nvsContext == null) {
            GLog.w(TAG, "saveVideo: NvsStreaming context isn't initialized or has been recycled");
            return false;
        }

        Hashtable<String, Object> config = new Hashtable<>();
        config.put(NvsStreamingContext.COMPILE_CREATION_TIME, TimeUtils.getFileDateTakenFormat(dateTaken));
        if (compileConfiguration != null) {
            config.putAll(compileConfiguration);
        }
        nvsContext.setCompileConfigurations(config);
        GLog.d(TAG, LogFlag.DL, "saveTransformVideo mVideoHeight:" + mVideoHeight + " mediaPath:" + PathMask.INSTANCE.mask(mediaPath));
        nvsContext.setCustomCompileVideoHeight(mVideoHeight);
        // 经测试使用surface的硬解码，视频编码结果和TBL一致，
        int flag = buildCompileFlag(false, false);
        boolean success = nvsContext.compileTimeline(mNvsTimeLine, 0,
                mNvsTimeLine.getDuration(), mediaPath,
                NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
                NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH, flag);
        nvsContext.setCompileConfigurations(null);
        GLog.d(TAG, String.format(Locale.ENGLISH,
                "saveVideo path:" + PathMask.INSTANCE.mask(mediaPath) + " : success ? %s", success));
        return success;
    }

    public boolean initTimeLine(int videoWidth, int videoHeight, int fps) {
        GLog.d(TAG, "initTimeLine w:" + videoWidth + ", h:" + videoHeight + ", fps:" + fps);

        int[] newVideoResolution = updateVideoResolution(videoWidth, videoHeight);
        videoWidth = newVideoResolution[0];
        videoHeight = newVideoResolution[1];
        GLog.d(TAG, "initTimeLine after updateVideoResolution w:" + videoWidth + ", h:" + videoHeight);
        if (mNvsContext == null) {
            GLog.e(TAG, "initTimeLine error: NvsStreaming context isn't initialized or has been recycled");
            return false;
        }
        mNvsTimeLine = mNvsContext.createTimeline(getVideoResolution(videoWidth, videoHeight),
                getFpsRate(fps), getAudioResolution());

        if (mNvsTimeLine == null) {
            GLog.e(TAG, "initTimeLine error w:" + videoWidth + ", h:" + videoHeight + ", fps:" + fps);
            return false;
        }
        mVideoTrack = mNvsTimeLine.appendVideoTrack();

        HDRVideoTransformCallback hdrTransformInterface = new HDRVideoTransformCallback(mSDKCallback);
        // set save mp4 callback
        hdrTransformInterface.setCompileCallback(mNvsContext);
        // set save mp4 callback2
        hdrTransformInterface.setCompileCallback2(mNvsContext);
        return true;
    }

    private int buildCompileFlag(boolean noUseInputSurface, boolean needDisableHardwareEncoder) {
        //生成文件输出的特殊标志，如果没有特殊需求，请填写0
        int compileFlag = 0;
        if (noUseInputSurface) {
            compileFlag = compileFlag | STREAMING_ENGINE_COMPILE_FLAG_DONT_USE_INPUT_SURFACE;
        }
        if (needDisableHardwareEncoder) {
            compileFlag = compileFlag | STREAMING_ENGINE_COMPILE_FLAG_DISABLE_HARDWARE_ENCODER;
        }
        // 禁用字节对齐，否则去水印时坐标就不准确了
        compileFlag = compileFlag | STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE;

        compileFlag = compileFlag | STREAMING_ENGINE_COMPILE_FLAG_IGNORE_TIMELINE_VIDEO_SIZE;
        return compileFlag;
    }

    public NvsVideoResolution getVideoResolution(int width, int height) {
        NvsVideoResolution videoRes = new NvsVideoResolution();
        videoRes.imageWidth = width;
        videoRes.imageHeight = height;
        videoRes.imagePAR = new NvsRational(1, 1);
        videoRes.bitDepth = VIDEO_RESOLUTION_BIT_DEPTH_8_BIT;
        return videoRes;
    }

    public NvsRational getFpsRate(int fps) {
        return new NvsRational(fps, 1);
    }

    public NvsAudioResolution getAudioResolution() {
        NvsAudioResolution audioRes = new NvsAudioResolution();
        audioRes.sampleRate = SAMPLE_RETE;
        audioRes.channelCount = CHANNEL_COUNT;
        return audioRes;
    }

    public int getVideoFileWidth() {
        return mVideoWidth;
    }

    public int getVideoFileHeight() {
        return mVideoHeight;
    }

    private int[] updateVideoResolution(int videoWidth, int videoHeight) {
        // for meicam. video width % 4 must equals 0, and video height % 2 must equals 0
        int divCheck = videoWidth % VIDEO_WIDTH_DIV_ZERO_VALUE;
        if (divCheck != NUM_ZERO) {
            videoWidth -= divCheck;
            GLog.d(TAG, "updateVideoResolution, need change videoWidth:" + videoWidth);
        }
        divCheck = videoHeight % VIDEO_HEIGHT_DIV_ZERO_VALUE;
        if (divCheck != NUM_ZERO) {
            videoHeight -= divCheck;
            GLog.d(TAG, "updateVideoResolution, need change mVideoHeight:" + videoHeight);
        }
        return new int[]{videoWidth, videoHeight};
    }

    public void removeTimeLine() {
        if (mNvsContext != null) {
            mNvsContext.removeTimeline(mNvsTimeLine);
        }
    }

    public void release() {
        if (mNvsContext != null) {
            mNvsContext.stop(NvsStreamingContext.STREAMING_ENGINE_STOP_FLAG_FORCE_STOP_COMPILATION);
            mNvsContext.removeTimeline(mNvsTimeLine);
            mNvsContext.setCompileCallback(null);
            mNvsContext.setCompileCallback2(null);
            mNvsContext = null;
            MeicamContextAliveCounter.INSTANCE.tryCloseContext(TAG);
        }
    }

    public static class HDRVideoTransformCallback {

        private SDKCallbackInterface mSDKCallback;

        public HDRVideoTransformCallback(SDKCallbackInterface callback) {
            mSDKCallback = callback;
        }

        private NvsStreamingContext.CompileCallback mCompileCallback = new NvsStreamingContext.CompileCallback() {
            @Override
            public void onCompileProgress(NvsTimeline nvsTimeline, int position) {
                final SDKCallbackInterface sdkCallback = mSDKCallback;
                if (sdkCallback != null) {
                    sdkCallback.onCompileProgress(nvsTimeline, position);
                }
            }

            @Override
            public void onCompileFinished(NvsTimeline nvsTimeline) {
                final SDKCallbackInterface sdkCallback = mSDKCallback;
                if (sdkCallback != null) {
                    sdkCallback.onCompileFinished(nvsTimeline);
                }
            }

            @Override
            public void onCompileFailed(NvsTimeline nvsTimeline) {
                final SDKCallbackInterface sdkCallback = mSDKCallback;
                if (sdkCallback != null) {
                    sdkCallback.onCompileFailed(nvsTimeline);
                }
            }
        };

        private NvsStreamingContext.CompileCallback2 mCompileCallback2 = new NvsStreamingContext.CompileCallback2() {

            @Override
            public void onCompileCompleted(NvsTimeline nvsTimeline, boolean isCanceled) {
                final SDKCallbackInterface sdkCallback = mSDKCallback;
                if (sdkCallback != null) {
                    sdkCallback.onCompileCompleted(nvsTimeline, isCanceled);
                }
            }
        };

        public void setCompileCallback(NvsStreamingContext nvsContext) {
            nvsContext.setCompileCallback(mCompileCallback);
        }

        public void setCompileCallback2(NvsStreamingContext nvsContext) {
            nvsContext.setCompileCallback2(mCompileCallback2);
        }

        public interface SDKCallbackInterface {
            //NvsStreamingContext.CompileCallback
            void onCompileProgress(NvsTimeline nvsTimeline, int position);

            void onCompileFinished(NvsTimeline nvsTimeline);

            void onCompileFailed(NvsTimeline nvsTimeline);

            //NvsStreamingContext.CompileCallback2
            void onCompileCompleted(NvsTimeline nvsTimeline, boolean isCanceled);
        }
    }
}
