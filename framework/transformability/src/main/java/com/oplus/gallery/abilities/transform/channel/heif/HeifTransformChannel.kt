/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - HeifTransformChannel
 ** Description: HEIF转码通道
 **
 ** Version: 1.0
 ** Date: 2022/10/26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/10/26  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.channel.heif

import android.content.Context
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import com.oplus.gallery.abilities.transform.channel.BaseTransformChannel
import com.oplus.gallery.business_lib.model.config.allowlist.HeifShareSupportAppConfig
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.foundation.security.EncryptUtils
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.osVersion
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.phoneModel
import com.oplus.gallery.foundation.util.version.AppVersionUtils.getVersionCode
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool.cleanHeifCache
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool.getCacheDir
import com.oplus.gallery.framework.abilities.transform.TransformConstant
import com.oplus.gallery.framework.abilities.transform.TransformCallback
import com.oplus.gallery.framework.abilities.transform.TransformConstant.Error.FILE_IS_NULL
import com.oplus.gallery.framework.abilities.transform.TransformConstant.FULL_QUALIFIED
import com.oplus.gallery.framework.abilities.transform.TransformConstant.HEIF_IMAGE_KEY
import com.oplus.gallery.framework.abilities.transform.TransformLimit
import com.oplus.gallery.framework.abilities.transform.TransformState
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.framework.abilities.transform.data.BaseTransformInfo
import com.oplus.gallery.framework.abilities.transform.data.HeifTransformInfo
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import java.util.Locale
import java.util.concurrent.Future
import java.util.concurrent.atomic.AtomicInteger

class HeifTransformChannel(
    context: Context,
    storage: TransformStorage,
    workerSession: WorkerSession
) : BaseTransformChannel(
    context,
    storage,
    workerSession,
    getCacheDir()
) {
    // status_msg 失败时才有msg
    private val convertedStatusMaps = HashMap<String, Pair<String, String?>>()
    private val heif2JpegMap = mutableMapOf<String, Uri>()
    private val fileFutureList = mutableListOf<Future<File>>()
    private val futureDoneCount = AtomicInteger(0)
    private val handler = MainThreadHandler(context.mainLooper)
    private var imageCount = 0
    private var progressStepValue = PROGRESS_STEP
    private var currentProgress = 0
    private var startTime = 0L
    private val isSaveToCacheDirTransform: Boolean = (storage == TransformStorage.APP_PRIV_CACHE)

    override fun getType(): TransformType {
        return TransformType.HEIF
    }

    override fun isSupport(targetPackageName: String): Boolean {
        if (TextUtils.isEmpty(targetPackageName)) {
            return false
        }
        val supportHEIFAppInfos = HeifShareSupportAppConfig.getDefaultAppInfos()
        val result = supportHEIFAppInfos.contains(targetPackageName.lowercase(Locale.getDefault()))
        GLog.v(TAG, LogFlag.DL) { "isSupport $targetPackageName is support result:$result" }
        return result
    }

    override fun setDataSource(infoList: MutableList<BaseTransformInfo>) {
        imageCount = infoList.size
        this.infoList.clear()
        this.infoList.addAll(infoList)
    }

    override fun setLimit(limit: TransformLimit) {
        // doNoting
    }

    override fun cleanCache(): Boolean {
        return cleanHeifCache()
    }

    override fun start(callback: TransformCallback?) {
        if ((imageCount <= 0) || (currentState == TransformState.START)) {
            return
        }
        currentState = TransformState.START
        futureDoneCount.set(0)
        heif2JpegMap.clear()
        fileFutureList.clear()
        transformCallback = callback
        handler.sendEmptyMessage(MSG_START)
        for ((index, info) in infoList.withIndex()) {
            transform(index, info)
        }
    }

    private fun transform(index: Int, transformInfo: BaseTransformInfo) {
        val imagePath = transformInfo.filePath
        val rotation = (transformInfo as? HeifTransformInfo)?.rotation ?: 0
        GLog.e(TAG, LogFlag.DL) { "transform path = ${PathMask.mask(imagePath)}, $index" }
        val quality = (configs[TransformConstant.Configs.QUALITY] as? Int) ?: FULL_QUALIFIED
        val fileFuture: Future<File> = workerSession.submit(
            HEIFTranscodingJpegTask(imagePath, cacheFolder, quality, rotation)
        ) { future ->
            futureDoneCount.getAndIncrement()
            GLog.v(TAG, LogFlag.DL) {
                "transform.onFutureDone futureDoneCount=$futureDoneCount currentState=$currentState"
            }
            var file: File? = null
            kotlin.runCatching {
                file = future.get()
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, "transform: ", it)
            }
            file?.also {
                val uri = if (isSaveToCacheDirTransform) {
                    GalleryFileProvider.fromOpenFile(it)
                } else {
                    MediaStoreScannerHelper.scanFileByMediaStoreSingle(context, it.absolutePath, MediaStoreScannerHelper.SCAN_FILE_TIME_OUT.toLong())
                }
                GLog.d(TAG, LogFlag.DL) { "transform.onFutureDone uri path = ${EncryptUtils.mixPath(it.path)} uri: $uri" }
                heif2JpegMap[imagePath] = uri
                if (futureDoneCount.get() == imageCount) {
                    // 埋点**********：用户在分享界面点击互传、应用icon、取消时上报
                    convertedStatusMaps[HEIF_IMAGE_KEY] =
                        Pair<String, String?>(LaunchExitPopupConstant.Value.TRANSCODING_SUCCESS, null)
                    handler.sendEmptyMessage(MSG_COMPLETE)
                }
            } ?: run {
                GLog.e(TAG, LogFlag.DL, "transform.onFutureDone file null")
                if ((imageCount == 1) && (currentState == TransformState.START)) {
                    addTransformFailTrack()
                    val msg = Message()
                    msg.what = MSG_FAIL
                    msg.arg1 = FILE_IS_NULL
                    handler.sendMessage(msg)
                    cancel()
                }
                return@submit
            }
        }
        fileFutureList.add(fileFuture)
    }

    private fun addTransformFailTrack() {
        // 埋点**********：用户在分享界面点击互传、应用icon、取消时上报
        val phoneInfo = StringBuilder()
        phoneInfo.append(phoneModel)
        phoneInfo.append(CommonTrackConstant.SYMBOL_UNDERLINE)
        phoneInfo.append(osVersion)
        phoneInfo.append(CommonTrackConstant.SYMBOL_UNDERLINE)
        phoneInfo.append(getVersionCode(context))
        convertedStatusMaps[HEIF_IMAGE_KEY] = Pair(
            LaunchExitPopupConstant.Value.TRANSCODING_FAIL,
            phoneInfo.toString()
        )
    }

    override fun cancel() {
        cancelTranscodingTask()
    }

    private fun cancelTranscodingTask() {
        GLog.v(TAG, LogFlag.DL) { "cancelTranscodingTask currentState = $currentState" }
        if (currentState == TransformState.CANCEL) {
            return
        }
        currentState = TransformState.CANCEL
        for (future in fileFutureList) {
            if (!future.isDone) {
                future.cancel(false)
            }
        }
        handler.sendEmptyMessage(MSG_CANCEL)
    }

    override fun release() {
        //do nothing
    }

    private inner class MainThreadHandler(looper: Looper) : Handler(looper) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_START -> handlerMsgStart()
                MSG_PROGRESS_UPDATE -> handlerMsgUpdate()
                MSG_COMPLETE -> handlerMsgComplete()
                MSG_CANCEL -> handlerMsgCancel()
                MSG_FAIL -> handlerMsgFail(msg)
                else -> GLog.w(TAG, LogFlag.DL) { "handleMessage, Unhandled message, what = " + msg.what }
            }
        }
    }

    private fun handlerMsgStart() {
        GLog.i(TAG, LogFlag.DL, "handleMessage. MSG_START")
        startTime = System.currentTimeMillis()
        currentProgress = 0
        progressStepValue = PROGRESS_STEP
        transformCallback?.onStart(0)
        handler.sendEmptyMessageDelayed(MSG_PROGRESS_UPDATE, DURATION_REFRESH.toLong())
    }

    private fun handlerMsgUpdate() {
        if (progressStepValue != MAX_PROGRESS_STEP) {
            progressStepValue = if (currentProgress < futureDoneCount.get() * MAX_PROGRESS / imageCount) {
                PROGRESS_STEP
            } else {
                0
            }
        }
        currentProgress += progressStepValue
        transformCallback?.onProgress(currentProgress)
        if (currentProgress < MAX_PROGRESS) {
            handler.removeMessages(MSG_PROGRESS_UPDATE)
            handler.sendEmptyMessageDelayed(MSG_PROGRESS_UPDATE, DURATION_REFRESH.toLong())
        } else {
            currentProgress = MAX_PROGRESS
            handler.removeMessages(MSG_COMPLETE)
            handler.sendEmptyMessageDelayed(MSG_COMPLETE, DURATION_REFRESH.toLong())
        }
    }

    private fun handlerMsgComplete() {
        GLog.d(TAG, LogFlag.DL) { "handleMessage. MSG_COMPLETE $currentProgress" }
        val currentTime = System.currentTimeMillis()
        handler.removeMessages(MSG_COMPLETE)
        if (currentState != TransformState.START) {
            return
        }
        if ((currentTime - startTime > DURATION_MIN_LOADING_SHOW) && (currentProgress >= MAX_PROGRESS)) {
            GLog.i(TAG, LogFlag.DL) { "handleMessage. time cost = " + (currentTime - startTime) }
            currentState = TransformState.FINISHED
            transformCallback?.onFinish(false)
            transformCallback?.onResult(heif2JpegMap, convertedStatusMaps)
        } else {
            progressStepValue = MAX_PROGRESS_STEP
            GLog.d(TAG, LogFlag.DL) { "handleMessage. continue update " + (currentTime - startTime) }
            handler.sendEmptyMessageDelayed(MSG_PROGRESS_UPDATE, DURATION_REFRESH.toLong())
        }
    }

    private fun handlerMsgCancel() {
        handler.removeMessages(MSG_COMPLETE)
        transformCallback?.onFinish(true)
        heif2JpegMap.clear()
        transformCallback?.onResult(heif2JpegMap, convertedStatusMaps)
    }

    private fun handlerMsgFail(msg: Message) {
        transformCallback?.onError(msg.arg1)
        transformCallback?.onResult(heif2JpegMap, convertedStatusMaps)
    }

    companion object {
        private const val TAG = "HeifTransformChannel"
        private const val MSG_START = 10000
        private const val MSG_PROGRESS_UPDATE = 10001
        private const val MSG_COMPLETE = 10002
        private const val MSG_CANCEL = 10003
        private const val DURATION_REFRESH = 10
        private const val PROGRESS_STEP = 1
        private const val MAX_PROGRESS_STEP = 5
        private const val MAX_PROGRESS = 100
        private const val DURATION_MIN_LOADING_SHOW = 500
        private const val MSG_FAIL = 10004
    }
}