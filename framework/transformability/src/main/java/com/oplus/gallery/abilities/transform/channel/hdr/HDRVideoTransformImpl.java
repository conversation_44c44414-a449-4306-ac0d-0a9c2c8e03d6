/*********************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  - HDRVideoTransformImpl.java
 * * Description : implement of transform for one video
 * * Version     : 1.0
 * * Date        : 2021/04/06
 * * Author      : xiang.cheng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  xiang.cheng@Apps.Gallery3D  2021/04/06  1.0       build this model
 ***********************************************************************/

package com.oplus.gallery.abilities.transform.channel.hdr;

import static com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_HLG;
import static com.oplus.gallery.foundation.codec.extend.ExtendKeyKt.EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA;
import static com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper.SCAN_FILE_TIME_OUT;
import static com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_VIDEO_FILE_NAME_HEAD;
import static com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_VIDEO_SUFFIX;
import static com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils.DEFAULT_VIDEO_TRANSFORM_SUFFIX_TMP;
import static com.oplus.gallery.foundation.util.storage.StorageLimitHelper.hasEnoughStorageSpace;
import static com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF;
import static com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF;
import static com.oplus.gallery.framework.abilities.transform.TransformConstant.KEY_HDR_TRANSFORM_TEMP_SAVE_URI_PREF;

import android.content.Context;
import android.graphics.ColorSpace;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.oplus.gallery.abilities.transform.channel.IVideoTransform;
import com.oplus.gallery.abilities.transform.channel.livephoto.OliveHdrVideoFx;
import com.oplus.gallery.abilities.transform.channel.livephoto.TransformConfig;
import com.oplus.gallery.abilities.transform.util.MigrateFileDataUtil;
import com.oplus.gallery.addon.osense.CpuFrequencyManager;
import com.oplus.gallery.foundation.codec.extend.HdrTransformData;
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct;
import com.oplus.gallery.foundation.codec.extend.VideoTransformStruct;
import com.oplus.gallery.foundation.codec.extend.VideoTransformData;
import com.oplus.gallery.foundation.codec.extend.ExtendDataUtils;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider;
import com.oplus.gallery.foundation.fileaccess.data.DeleteFileRequest;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt;
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper;
import com.oplus.gallery.foundation.util.mediaStore.VideoMediaStoreUtils;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment.StorageType;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper;
import com.oplus.gallery.foundation.util.version.ApiLevelUtil;
import com.oplus.gallery.framework.abilities.transform.TransformCacheTool;
import com.oplus.gallery.framework.abilities.transform.TransformCallback;
import com.oplus.gallery.framework.abilities.transform.TransformStorage;
import com.oplus.gallery.framework.abilities.transform.hdr.HdrVideoTransformListener;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

import java.util.List;

import kotlin.Pair;
import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

public class HDRVideoTransformImpl implements IVideoTransform {
    private static final String TAG = "HDRVideoTransformImpl";
    private static final String PATH_SLASHES_SEPARATION = "/";
    private static final long ENSURE_SPACE_MB = 100 * 1024 * 1024;
    private static final int SAVE_VIDEO_DURATION = TimeUtils.TIME_2_MIN_IN_MS;
    private static final int ENGINE_VIDEO_FPS = 30;
    private HDRVideoTransformEngine mHDRVideoTransformEngine;
    private File mCurSaveParentDir;
    private File mCurSaveDir;
    private String mVideoFileName;
    private String mTmpVideoFilename;
    private Uri mSavedUri;
    private String mOriVideoPath;
    private Context mContext;
    private TransformStorage mStorage;

    public HDRVideoTransformImpl(Context context, TransformStorage storage, HdrVideoTransformListener listener) {
        this.mContext = context;
        mStorage = storage;
        mHDRVideoTransformEngine = new HDRVideoTransformEngine(context, listener);
    }

    /**
     * 取100M和原视频的三分之一的最大值，作为有足够转码空间的条件
     *
     * @param totalVideoSize
     * @return Marked by limao：该接口需要改造。不应该与页面的显示方式耦合。暂时通过callback方式解决依赖问题
     */
    @Override
    public boolean checkSupported(long totalVideoSize, TransformCallback callback) {
        return checkStorageEnough(totalVideoSize, callback);
    }

    @Override
    public boolean beforeTransform(String uri, String filePath) {
        mOriVideoPath = filePath;
        if (!initEngineTimeLine(filePath)) {
            return false;
        }

        mHDRVideoTransformEngine.setHdrFx(filePath);
        ColorSpace videoColorSpace = mHDRVideoTransformEngine.getVideoColorSpace();
        int videoHDRType = mHDRVideoTransformEngine.getVideoHDRType();
        boolean isTryReadGamma = ApiLevelUtil.isAtLeastAndroidU()
                && (uri != null)
                && ColorSpaceExt.INSTANCE.isHdrGamma(videoColorSpace)
                && videoHDRType == HDR_TYPE_HLG;

        GLog.d(TAG, LogFlag.DL, "beforeTransform, isTryReadGamma=" + isTryReadGamma
                + ", videoColorSpace=" + videoColorSpace
                + ", videoHDRType=" + videoHDRType);
        /*
        1. 只有 hdr 的视频需要使用影像的下变换算法，其他的不需要，可能是空间音频轨+sdr视频轨的组合，此时不能走下变换算法
        2. 只有写入 gamma 曲线的才使用影像的下变换算法
        3. 影像在杜比上写 gamma 曲线的效果还没调试好，先只读 hlg 视频里的 gamma 曲线
         */
        if (isTryReadGamma) {
            HdrTransformData data = HdrTransformDataStruct.Companion.dolbyHdrTransformData();
            VideoTransformData videoTransformData = ExtendDataUtils.getExtendData(Uri.parse(uri), EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA,
                    VideoTransformStruct.class);

            if (videoTransformData != null) {
                TransformConfig config = new TransformConfig();
                config.setOutputTextureIsSDR(true);
                data = data.copyWithGamma(videoTransformData.getHlgGamma(), videoTransformData.getSrgbGamma());
                GLog.d(TAG, LogFlag.DL, "beforeTransform, data=" + data);

                mHDRVideoTransformEngine.adjustCustomFx(new OliveHdrVideoFx(data, config), null);
            }
        }
        return true;
    }

    @Override
    public boolean startTransform(long dateTaken) {
        return createVideoFile(dateTaken);
    }

    @NonNull
    @Override
    public Pair<Boolean, Uri> saveTransform(long lastModified) {
        boolean isCache = isSaveToCacheDirTransform();
        Uri savedUri = null;
        if ((!isCache && updateSavedFileSandbox(lastModified)) || updateSavedFile(lastModified)) {
            savedUri = getTransformedUri();
        }
        GLog.d(TAG, LogFlag.DL, "[saveTransform] savedUri: " + savedUri);
        return new Pair<>(isCache, savedUri);
    }

    @Override
    public void afterTransform(MediaStoreScannerHelper.SingleFileScanCompleteListener listener) {
        scanFileWhenSaved(listener);
    }

    @Override
    public void cancelTransform() {
        deleteSavedVideoIfNeed();
    }

    @Override
    public void clear() {
        removeTimeLine();
    }

    @Override
    public void release() {
        if (mHDRVideoTransformEngine != null) {
            mHDRVideoTransformEngine.release();
        }
    }

    /**
     * 获取转换后的文件路经
     * @return
     */
    public String getTransformedFilePath() {
        return new File(mCurSaveDir, mVideoFileName).getPath();
    }

    private boolean checkStorageEnough(long totalVideoSize, TransformCallback callback) {
        if (isSaveToCacheDirTransform()) {
            if (!StorageLimitHelper.hasEnoughStorageSpace(StorageType.PHONE_STORAGE, totalVideoSize)) {
                mCurSaveParentDir = null;
                if (callback != null) {
                    callback.onNoStorage(StorageType.PHONE_STORAGE);
                }
            } else {
                mCurSaveParentDir = StorageLimitHelper.createCacheDir(mContext);
            }
        } else {
            File result = FileOperationUtils.createDir(StorageType.PHONE_STORAGE, null);
            boolean isFull = !hasEnoughStorageSpace(StorageType.PHONE_STORAGE, totalVideoSize);
            if (isFull) {
                boolean hasExternal = OplusEnvironment.isExternalMounted();
                if (!hasExternal) {
                    GLog.w(TAG, "checkStorageEnough show phone storage no space dialog.");
                    if (callback != null) {
                        callback.onNoStorage(StorageType.PHONE_STORAGE);
                    }
                    result = null;
                }
                result = FileOperationUtils.createDir(StorageType.SDCARD_STORAGE, null);
                // check external storage enough or not
                isFull = !hasEnoughStorageSpace(StorageType.SDCARD_STORAGE, totalVideoSize);
                if (isFull) {
                    GLog.w(TAG, "checkStorageEnough show sdcard storage no space dialog.");
                    if (callback != null) {
                        callback.onNoStorage(StorageType.PHONE_STORAGE);
                    }
                    result = null;
                }
            }
            mCurSaveParentDir = result;
        }
        return (mCurSaveParentDir != null);
    }

    public boolean saveTransformVideo(String filePath) {
        return mHDRVideoTransformEngine.saveTransformVideo(filePath, System.currentTimeMillis());
    }

    private boolean createVideoFile(long dateTaken) {
        String currentTime = TimeUtils.getFormatDateTimeMillis();
        if (TextUtils.isEmpty(currentTime)) {
            GLog.w(TAG, "createVideoFile dateTime is null");
            return false;
        }
        List<String> tempFileInfo = TransformCacheTool.getHDRTransformTempFileInfo(mContext);
        BuildersKt.launch(
                AppScope.INSTANCE,
                (CoroutineContext) Dispatchers.getIO(),
                CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    TransformCacheTool.deleteHdrTransformTempFile(mContext, tempFileInfo);
                    return null;
                }
        );
        if (mCurSaveParentDir == null) {
            GLog.w(TAG, "[createVideoFile] mCurSaveParentDir is null");
            return false;
        }
        if (isSaveToCacheDirTransform()) {
            mCurSaveDir = new File(mCurSaveParentDir, TransformCacheTool.HDR_SHARE_CACHE_DIRECTORY);
            mVideoFileName = DEFAULT_VIDEO_FILE_NAME_HEAD + currentTime + DEFAULT_VIDEO_TRANSFORM_SUFFIX_TMP;
        } else {
            if (mStorage == TransformStorage.HIDDEN_DIR) {
                mCurSaveDir = new File(mCurSaveParentDir, AppConstants.Path.getPATH_CONVERT_CACHE_DIR());
                if (!mCurSaveDir.exists()) {
                    mCurSaveDir.mkdir();
                    MigrateFileDataUtil.createNoMediaFile(mCurSaveDir.getPath());
                }
            } else {
                mCurSaveDir = new File(mCurSaveParentDir, FilePathUtils.getRelativePath(mOriVideoPath));
            }
            mVideoFileName = DEFAULT_VIDEO_FILE_NAME_HEAD + currentTime + DEFAULT_VIDEO_SUFFIX;
        }
        SPUtils.setString(mContext, null, KEY_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF, mVideoFileName);
        File file = new File(mCurSaveDir, mVideoFileName);
        file = FilePathUtils.switchToPublicDir(file, false);
        SPUtils.setString(mContext, null, KEY_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF, file.getParent());
        GLog.d(TAG, "createVideoFile, mVideoFileName = " + mVideoFileName + ", file = " + file);
        CpuFrequencyManager.setAction(CpuFrequencyManager.Action.STORAGE_BOOST, SAVE_VIDEO_DURATION);
        if (!isSaveToCacheDirTransform()) {
            Uri uri = null;
            // 如果转码后图片需要对相册隐藏，先保存文件，再手动触发媒体库扫描返回uri，否则通过插入媒体库返回uri
            if (mStorage == TransformStorage.HIDDEN_DIR) {
                mHDRVideoTransformEngine.saveTransformVideo(file.getAbsolutePath(), dateTaken);
                uri = MediaStoreScannerHelper.scanFileByMediaStoreSingle(mContext, file.getAbsolutePath(), SCAN_FILE_TIME_OUT);
            } else {
                uri = VideoMediaStoreUtils.insertVideoSandbox(mContext, file, mVideoFileName, System.currentTimeMillis());
                mHDRVideoTransformEngine.saveTransformVideo(file.getAbsolutePath(), dateTaken);
            }
            if (uri != null) {
                mSavedUri = uri;
                SPUtils.setString(mContext, null, KEY_HDR_TRANSFORM_TEMP_SAVE_URI_PREF, mSavedUri.toString());
            }
            GLog.d(TAG, LogFlag.DL, "createVideoFile, save uri  = " + mSavedUri);
        } else {
            mHDRVideoTransformEngine.saveTransformVideo(file.getAbsolutePath(), dateTaken);
        }
        return true;
    }

    private boolean initEngineTimeLine(String filePath) {
        if (!mHDRVideoTransformEngine.initVideoFileInfo(filePath)) {
            return false;
        } else {
            int videoWidth = mHDRVideoTransformEngine.getVideoFileWidth();
            int videoHeight = mHDRVideoTransformEngine.getVideoFileHeight();
            return mHDRVideoTransformEngine.initTimeLine(videoWidth, videoHeight, ENGINE_VIDEO_FPS);
        }
    }

    private void deleteSavedVideoIfNeed() {
        try {
            if (!TextUtils.isEmpty(mVideoFileName) && mCurSaveDir != null) {
                final File mediaFile = new File(mCurSaveDir, mVideoFileName);
                GLog.d(TAG, "deleteSavedVideoIfNeed, delete file.");
                DeleteFileRequest deleteFileRequest = new DeleteFileRequest.Builder()
                        .setFile(mediaFile)
                        .setUri(mSavedUri)
                        .setImage(false)
                        .builder();
                boolean result = FileAccessManager.getInstance().delete(mContext, deleteFileRequest);
                if (!result) {
                    GLog.w(TAG, "deleteSavedVideoIfNeed, delete file failed. mediaFile = " + mediaFile);
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, "deleteSavedVideoIfNeed, Exception:", e);
        }
    }

    private boolean updateSavedFileSandbox(long lastModified) {
        if (TextUtils.isEmpty(mVideoFileName)) {
            GLog.w(TAG, "updateSavedFileSandbox mVideoFileName is empty!");
            return false;
        }

        File mediaFile = new File(mCurSaveDir, mVideoFileName);
        if (!mediaFile.exists()) {
            GLog.e(TAG, "updateSavedFileSandbox failed. the file does not exists, mediaFile = " + mediaFile);
            return false;
        }
        mediaFile.setLastModified(lastModified);

        // 美摄sdk写文件是通过fuse，先插入媒体库记录，再通过fuse写文件时会产生另外的新记录，之后如果再更新媒体库记录就会因为冲突把记录删除，需要媒体库扫描
        mSavedUri = MediaStoreScannerHelper.scanFileByMediaStoreSingle(mContext, mediaFile.getAbsolutePath(), SCAN_FILE_TIME_OUT);
        if (mSavedUri != null) {
            VideoMediaStoreUtils.updateLocalMedia(mContext, mSavedUri, mediaFile);
            SPUtils.setString(mContext, null, KEY_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF, null);
            SPUtils.setString(mContext, null, KEY_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF, null);
            return true;
        }
        GLog.e(TAG, "updateSavedFileSandbox failed");
        return false;
    }

    private void scanFileWhenSaved(MediaStoreScannerHelper.SingleFileScanCompleteListener listener) {
        MediaStoreScannerHelper.scanFileByMediaStoreSingle(mContext, mCurSaveDir + PATH_SLASHES_SEPARATION + mVideoFileName, listener);
    }

    private boolean updateSavedFile(long dateTaken) {
        if (TextUtils.isEmpty(mVideoFileName)) {
            GLog.w(TAG, "updateSavedFile mVideoFileName is empty!");
            return false;
        }

        File mediaFile = new File(mCurSaveDir, mVideoFileName);
        if (!mediaFile.exists()) {
            GLog.e(TAG, "updateSavedFile failed. the file does not exists, mediaFile = " + mediaFile);
            return false;
        }
        if (!mVideoFileName.endsWith(VideoMediaStoreUtils.DEFAULT_VIDEO_TRANSFORM_SUFFIX_TMP)) {
            GLog.e(TAG, "updateSavedFile failed. the file does not end with DEFAULT_VIDEO_SUFFIX_TMP");
            return false;
        }

        mTmpVideoFilename = mVideoFileName.replace(VideoMediaStoreUtils.DEFAULT_VIDEO_TRANSFORM_SUFFIX_TMP,
                VideoMediaStoreUtils.DEFAULT_VIDEO_SUFFIX);
        File newFile = new File(mCurSaveDir, mTmpVideoFilename);
        boolean result = mediaFile.renameTo(newFile);
        if (!isSaveToCacheDirTransform()) {
            newFile.setLastModified(dateTaken);
        }

        if (result) {
            mVideoFileName = mTmpVideoFilename;
            Uri uri = VideoMediaStoreUtils.insertVideoToMediaStore(mContext, newFile, mVideoFileName, dateTaken, false);
            GLog.d(TAG, LogFlag.DL, "updateSavedFile uri:" + uri);
            if (uri != null) {
                mSavedUri = uri;
            }
            SPUtils.setString(mContext, null, KEY_HDR_TRANSFORM_TEMP_SAVE_NAME_PREF, null);
            SPUtils.setString(mContext, null, KEY_HDR_TRANSFORM_TEMP_SAVE_DIR_PREF, null);
            return true;
        }
        GLog.e(TAG, "updateSavedFile failed");
        return false;
    }

    private Uri getTransformedUri() {
        if (isSaveToCacheDirTransform()) {
            return GalleryFileProvider.fromFile(mContext, new File(mCurSaveDir, mVideoFileName));
        } else {
            return mSavedUri;
        }
    }

    private void removeTimeLine() {
        if (mHDRVideoTransformEngine != null) {
            mHDRVideoTransformEngine.removeTimeLine();
        }
    }

    private boolean isSaveToCacheDirTransform() {
        return mStorage == TransformStorage.APP_PRIV_CACHE;
    }
}
