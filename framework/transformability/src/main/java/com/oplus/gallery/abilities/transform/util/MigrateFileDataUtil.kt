/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MigrateFileDataUtil
 ** Description: 迁移文件的数据工具类
 **
 ** Version: 1.0
 ** Date: 2024/12/18
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  houdong<PERSON>@Apps.Gallery3D      2024/12/18        1.0      build this module
 *********************************************************************************/

package com.oplus.gallery.abilities.transform.util

import com.oplus.gallery.foundation.exif.raw.ExifUtils
import com.oplus.gallery.foundation.fileaccess.extension.CloneMode
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.fileaccess.extension.setDataSource
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppConstants.Path.PATH_NO_MEDIA
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale

/**
 * 迁移文件的数据工具类
 * 包括但不限于exif，extend等等
 */
internal object MigrateFileDataUtil {
    private const val TAG = "MigrateFileDataUtil"

    /**
     * 图片转码后，对其迁移扩展信息
     * 使其分享到支持扩展格式的手机上，依旧可以使用扩展数据
     *
     * @param newPath 新的文件路径
     * @param oldPath 旧的文件路经
     */
    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun migrateExtensionData(oldPath: String, newPath: String) {
        FileExtendedContainer().use {
            kotlin.runCatching {
                it.setDataSource(ContextGetter.context, oldPath, -1, OpenFileMode.MODE_READ)
                val success = it.clone(newPath, CloneMode.EXTENSION_ONLY)
                GLog.i(TAG, LogFlag.DL, "migrateExtensionData clone files result: $success")
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, "migrateExtensionData, exp: ", it)
            }
        }
    }

    /**
     * 迁移原图的exif信息，否则分享出去的图片无法识别其图片标签、水印信息等
     *
     * @param oldPath 旧的文件路经
     * @param newPath 新的文件路经th
     * @param callback 在写入新的exif前，回调当前的exifEntry，如果有需求的需要可以在这个时候完成
     */
    @JvmStatic
    fun migrateExifData(oldPath: String, newPath: String, callback: ((exifEntry: ExifUtils.ExifEntry) -> Unit)? = null) {
        val exifEntry = ExifUtils.readExif(oldPath)
        if (exifEntry == null) {
            GLog.e(TAG, LogFlag.DL, "migrateExifData exifEntry is null.")
            return
        }
        callback?.invoke(exifEntry)
        ExifUtils.writeExif(newPath, exifEntry)
    }

    /**
     * 获取临时的缓存
     */
    @JvmStatic
    fun getOutputCacheFileName(): String {
        val dateTimeFormatter = DateTimeFormatter.ofPattern(TimeUtils.TIME_FORMAT, Locale.US)
        return dateTimeFormatter.format(LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault()))
    }

    /**
     * 创建非媒体文件文件
     * @param path 文件路经
     */
    @JvmStatic
    fun createNoMediaFile(path: String) {
        kotlin.runCatching {
            val noMediaFile: File = File(path, PATH_NO_MEDIA)
            if (!noMediaFile.exists()) {
                noMediaFile.createNewFile()
            } else {
                GLog.d(TAG, LogFlag.DL, "createNoMediaFile, create nomedia success")
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "createNoMediaFile, create nomedia fail", it)
        }
    }
}