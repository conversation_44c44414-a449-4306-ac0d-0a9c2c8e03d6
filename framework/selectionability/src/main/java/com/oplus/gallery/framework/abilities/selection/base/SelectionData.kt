/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SelectionData.kt
 * Description:
 * Version: 1.0
 * Date: 2020/09/22
 * Author: Wangrunxin@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Wangrunxin@Apps.Gallery3D      2020/09/22      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.selection.base

import android.os.Bundle
import com.oplus.gallery.framework.abilities.selection.ISelectionData
import com.oplus.gallery.framework.abilities.selection.OnSelectionListener
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * TODO 暂时实现，供大图使用，后续选择重构时覆盖实现
 */
open class SelectionData<T> : ISelectionData<T> {
    protected val onSelectionListenerList: MutableSet<OnSelectionListener> = mutableSetOf()

    /**
     * SelectionData 的唯一id
     */
    override val selectionDataId: Int = hashCode()

    /**
     * 存储已选中所有数据的唯一标识，比如 path、media_id 等
     */
    private val selectedItems = LinkedHashSet<T>()

    private val lock = ReentrantLock()

    /**
     * 当用户选择某个 item 时，将选中的数据唯一标识存储到 selectedItems 中
     * @param item 选中的数据唯一标识
     */

    override fun selectItem(item: T, position: Int): Boolean = lock.withLock {
        if (GProperty.DEBUG) {
            GLog.d(TAG, "selectItem, item:$item")
        }
        selectedItems.add(item)

        GlobalScope.launch(Dispatchers.UI) {
            for (listener in onSelectionListenerList) {
                GLog.w(TAG, "selectItem, listener.onSelectionChange position:$position")
                listener.onSelectionChanged(selectionDataId, position, true)
            }
        }
        return true
    }

    /**
     * 当用户取消选择某个 item 时，将选中的数据唯一标识存从 selectedItems 中移除
     * @param item 选中的数据唯一标识
     */
    override fun unselectItem(item: T, position: Int): Boolean {
        var result = false
        lock.withLock {
            if (selectedItems.contains(item)) {
                selectedItems.remove(item)
                result = true
            }
        }
        if (GProperty.DEBUG_SELECTION) {
            GLog.d(TAG, "unSelectItem item:$item, result:$result")
        }

        GlobalScope.launch(Dispatchers.UI) {
            for (listener in onSelectionListenerList) {
                GLog.w(TAG, "selectItem, listener.onSelectionChange position:$position")
                listener.onSelectionChanged(selectionDataId, position, false)
            }
        }
        return result
    }

    /**
     * 当用户选择多个 item 时，将所有选中的数据唯一标识存储到 selectedItems 中
     * @param items 选中的所有数据唯一标识
     */
    override fun selectItems(items: Set<T>): Boolean {
        GLog.d(TAG, "selectItems, items:$items")
        if (items.isEmpty()) {
            GLog.d(TAG, "selectItems, items isNullOrEmpty")
            return false
        }
        if (GProperty.DEBUG_SELECTION) {
            GLog.d(TAG, "selectItems, items:${getItemsLogInfo(items)}, count:${items.size}")
        }
        lock.withLock {
            selectedItems.addAll(items)
        }
        return true
    }

    /**
     * 当用户取消选择多个 item 时，将所有取消选中的数据唯一标识存从 selectedItems 中移除
     * @param items 取消选中的所有数据唯一标识
     */
    override fun unselectItems(items: Set<T>): Boolean {
        GLog.d(TAG, "unSelectItems, items:$items")
        if (items.isEmpty()) {
            GLog.d(TAG, "unSelectItems, items isNullOrEmpty")
            return false
        }
        if (GProperty.DEBUG_SELECTION) {
            GLog.d(TAG, "unSelectItems, items:${getItemsLogInfo(items)}, count:${items.size}")
        }
        lock.withLock {
            items.forEach {
                if (selectedItems.contains(it)) {
                    selectedItems.remove(it)
                }
            }
        }
        return true
    }

    /**
     * 当用户取消选择所有 item 时，清除 selectedItems 中的所有数据
     */
    override fun unselectAllItems() = lock.withLock {
        GLog.d(TAG, "unSelectAllItems, selectedItems is clear")
        selectedItems.clear()
    }

    /**
     * 获取该 data 中所有选中数据的唯一标识
     * @return set of items
     */
    override fun getSelectedItems(): Set<T> = lock.withLock {
        return LinkedHashSet<T>(selectedItems)
    }

    /**
     * 判断该 data 中是否存在某个 item
     * @return 若 data 中包含该 item，则返回true，否则，返回false
     */
    override fun isItemSelected(item: T): Boolean = lock.withLock {
        val result = selectedItems.contains(item)
        if (GProperty.DEBUG_SELECTION) {
            GLog.d(TAG, "isItemSelected item:$item, result:$result")
        }
        return result
    }

    /**
     * 获取该 data 中 selectedItems 的size
     * @return selectedItems.size
     */
    override fun getSelectedItemCount(): Int = lock.withLock {
        if (GProperty.DEBUG_SELECTION) {
            GLog.d(TAG, "getSelectedItemCount count:${selectedItems.size}")
        }
        return selectedItems.size
    }


    /**
     * 退出选择模式时，selectionDataManager 会调用该方法，清空选中数据
     */
    override fun clear() = lock.withLock {
        GLog.d(TAG, "cancel")
        selectedItems.clear()
    }

    override fun selectAllItems() = Unit

    /**
     * 会话提交，返回选中数据
     * @return selectedItems
     */
    override fun submit(completedHandle: (Set<T>) -> Unit) = lock.withLock {
        GLog.d(TAG, "submit count:${selectedItems.size}")
        completedHandle.invoke(selectedItems)
    }

    private fun getItemsLogInfo(collection: Collection<T>): String {
        if (collection.isNullOrEmpty()) return ""
        val builder = StringBuilder()
        val iterator = collection.iterator()
        var count = 0
        while (iterator.hasNext() && (count++ < LOG_ITEM_MAX_COUNT)) {
            builder.append(iterator.next())
            builder.append(",")
        }
        return builder.toString()
    }

    override fun queryCountsFromDataSource(): Bundle = Bundle()

    override fun registerOnSelectionListener(onSelectionListener: OnSelectionListener) {
        if (onSelectionListenerList.contains(onSelectionListener)) {
            GLog.d(TAG, "registerOnSelectionListener, already add this listener")
            return
        }
        onSelectionListenerList.add(onSelectionListener)
    }

    override fun unregisterOnSelectionListener(onSelectionListener: OnSelectionListener) {
        onSelectionListenerList.remove(onSelectionListener)
    }

    companion object {
        private const val TAG = "SelectionData"
        const val INVALID_ID = -1
        private const val LOG_ITEM_MAX_COUNT = 5
    }
}