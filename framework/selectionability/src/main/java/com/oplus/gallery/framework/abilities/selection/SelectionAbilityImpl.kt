/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SelectionAbilityImpl.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/06/13
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                         <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2022/06/13       1.0      create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.selection

import com.oplus.appability.AbsAppAbility
import com.oplus.gallery.framework.abilities.selection.base.SelectionData
import java.util.concurrent.ConcurrentHashMap

/**
 * TODO 暂时实现，供大图使用，后续选择重构时覆盖实现
 */
class SelectionAbilityImpl : AbsAppAbility(), ISelectionAbility {
    private val selectionDataSet = ConcurrentHashMap<Int, SelectionData<*>>()

    override val domainInstance: AutoCloseable = this

    override fun <T> createSelectionData(dataSourceType: ISelectionAbility.DataSourceType): SelectionData<T> {
        return SelectionData<T>().apply {
            selectionDataSet[selectionDataId] = this
        }
    }

    override fun <T> getSelectionData(selectionDataId: Int): SelectionData<T>? =
        selectionDataSet[selectionDataId] as? SelectionData<T>

    override fun destroySelectionData(selectionDataId: Int) {
        selectionDataSet.remove(selectionDataId)
    }

    override fun close() = Unit
}