/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MapAbilityImpl.kt
 ** Description : MapAbility的能力实现类，实现IMapAbility接口，对外暴露显示地图+marker的能力
 **
 ** Version     : 1.0
 ** Date        : 2025/4/1
 ** Author      : huangyuanwang
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** ******** Pengcheng Lin      2024/6/20     1.0        Add KFile header describe
 *********************************************************************************/
package com.oplus.gallery.framework.mapability

import android.content.Context
import android.os.Bundle
import android.view.ViewGroup
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.version.AppVersionUtils
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_MAP_PAGE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_PRODUCT_LIGHT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.download.IDownloadAbility
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility
import com.oplus.gallery.framework.abilities.map.IMapAbility
import com.oplus.gallery.framework.abilities.map.IMapAbility.MapStateConfigKeys
import com.oplus.gallery.framework.abilities.map.LatLng
import com.oplus.gallery.framework.abilities.map.MapConstants
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.mapability.location.CountryCodePositionDecoder
import com.oplus.gallery.framework.mapability.mapsdk.MapSdkInitImpl
import com.oplus.gallery.standard_lib.scheduler.DefaultScheduler
import com.oplus.gallery.standard_lib.util.os.ContextGetter

class MapAbilityImpl : AbsAppAbility(), IMapAbility {

    companion object {
        const val TAG = "MapAbilityImpl"

        //标尺距离显示5公里左右的标尺11.5-12.5 之间显示 5Km的缩放，这里取12.0
        const val LOCATE_DEFAULT_ZOOM = MapConstants.ZOOM_LEVEL_200M
    }

    private var mapSdkInit: IMapSdkInit = MapSdkInitImpl()
    private val mapWrapperMap: MutableMap<String, MapWrapper> = mutableMapOf()

    /**
     * 部分机型onResume回调时间早于initMapView，导致mapWrapperId无效，无法执行map的onResume，从而地图加载异常
     * 添加此字段用于记录首次onResume回调时是否执行了map的onResume回调
     */
    private val mapWrapperIdResumedMap: MutableMap<String, Boolean> = mutableMapOf()

    private val session by lazy { DefaultScheduler.scheduler.createPrioritySession(name = this.javaClass.simpleName) }
    private var resourceAbility: IResourcingAbility? = null
    private var downloadAbility: IDownloadAbility? = null
    private var configAbility: IConfigAbility? = null
    private var hardwareAbility: IHardwareAbility? = null

    private var countryCodeDecoder: CountryCodePositionDecoder? = null
    private var captureMapRegionListener: IMapAbility.CaptureMapRegionListener? = null

    private var mapSdkInited: Boolean = false

    /**
     * 是否时内销
     */
    private val isDomestic: Boolean by lazy {
        val isDomestic = configAbility?.let {
            it.getBooleanConfig(IS_REGION_CN, defValue = false) ?: false
        } ?: false.also {
            GLog.e(TAG, LogFlag.DL, "getBoolean configAbility is null, configId:$IS_REGION_CN")
        }
        GLog.d(TAG, LogFlag.DL, "isDomestic $isDomestic")
        isDomestic
    }


    /**
     * 是否是轻量OS
     */
    private val isLightOs: Boolean by lazy {
        val isLightOs = configAbility?.let {
            it.getBooleanConfig(IS_PRODUCT_LIGHT, defValue = false) ?: false
        } ?: false.also {
            GLog.e(TAG, LogFlag.DL, "getBoolean configAbility is null, configId:$IS_PRODUCT_LIGHT")
        }
        GLog.d(TAG, LogFlag.DL, "isLightOs $isLightOs")
        isLightOs
    }


    /**
     * 内销地图组件版本号
     */
    private val mapCrameVersionCode: Int by lazy {
        val result = AppVersionUtils.getMapFrameVersionCode(ContextGetter.context)
        GLog.d(TAG, LogFlag.DL, "mapCrameVersionCode $result")
        result
    }


    /**
     * 判断是否是一加外销
     */
    private val isOnePlusExport: Boolean by lazy {
        val isOnePlus = AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
        val result = isDomestic.not() && isOnePlus
        GLog.d(TAG, LogFlag.DL, "isOnePlusExport $result, isDomestic $isDomestic, isOnePlus $isOnePlus")
        result
    }

    /**
     * 检测是否支持地图页面
     * 轻量OS不支持  ****
     * 内销OS12.1以下不支持  *****
     * 其余需要看 os.graphic.gallery.photolistview.map_page 这个feature的配置情况，没有配置时默认为true，配置了false才不显示
     */
    private val mapPageFeatureSupported: Boolean by lazy {
        //内部已经有内外销和OS12.1的判定
        val result = configAbility?.let {
            it.getBooleanConfig(FEATURE_IS_SUPPORT_MAP_PAGE, defValue = false) ?: false
        } ?: false.also {
            GLog.e(TAG, LogFlag.DL, "getBoolean configAbility is null, configId:$FEATURE_IS_SUPPORT_MAP_PAGE")
        }
        GLog.d(TAG, LogFlag.DL, "mapPageFeatureSupported $result")
        result
    }


    /**
     * 判断整体地图功能是否支持
     */
    private val mapSupported: Boolean by lazy {
        // OS类型是否满足：内、外销轻量机型不支持地图模式
        val osTypeMeets = !isLightOs
        val mapCrameMeets = if (isDomestic) {
            // 地图库组件需要高于1.1.13版本才支持
            mapCrameVersionCode >= AppVersionUtils.MAP_SUPPORT_BITMAP_VERSION
        } else {
            // 外销不依赖于OPPO地图库组件，所以默认符合
            true
        }
        val result = mapPageFeatureSupported && osTypeMeets && mapCrameMeets
        GLog.d(TAG, LogFlag.DL, "supportMap, supportMapPage: $mapPageFeatureSupported, osTypeMeets: $osTypeMeets" +
                ", mapComFrameMeets: $mapCrameMeets, result $result")
        result
    }


    /**
     * 是否功能上支持截图能力
     */
    private val supportSnapShot: Boolean by lazy {
        val mapCrameVersionMeet = if (isDomestic) {
            mapCrameVersionCode >= AppVersionUtils.MAP_SUPPORT_SNAP_SHOT_VERSION
        } else {
            true
        }
        mapCrameVersionMeet && mapSupported
    }


    /**
     * 判断是否支持地图在详情页入口显示
     */
    private val canShowInDetailOuter: Boolean by lazy {
        val mapCrameVersionMeet = if (isDomestic) {
            mapCrameVersionCode >= AppVersionUtils.MAP_NO_ANR_VERSION
        } else {
            true
        }
        val result = mapCrameVersionMeet && mapSupported
        GLog.d(TAG, LogFlag.DL, "canShowInDetailOuter $result, mapFrameVersionCode $mapCrameVersionMeet, " +
                "mapSupported $mapSupported")
        result
    }


    override fun isMapSupported(): Boolean {
        return mapSupported
    }

    override fun isSnapshotSupported(): Boolean {
        return supportSnapShot
    }

    override fun canShowInDetailOuter(): Boolean {
        return canShowInDetailOuter
    }

    /**
     * 初始化sdk的接口，时许上最先调用
     */
    override fun initMapSdk(context: Context) {
        if (!mapSdkInited) {
            val mapType = if (isDomestic) IMapSdkInit.MapType.BAIDU_MAP else IMapSdkInit.MapType.GOOGLE_MAP
            GLog.d(TAG, LogFlag.DL, "initMapSdk, this: $this, mapType $mapType")
            mapSdkInited = mapSdkInit.initMapSdk(context, mapType)
        } else {
            GLog.d(TAG, LogFlag.DL, "initMapSdk, mapSdk already inited, no need to init again this: $this")
        }
    }

    override fun isMapSdkInited(): Boolean {
        return mapSdkInited
    }


    override fun initMapView(mapViewContainer: ViewGroup, savedInstanceState: Bundle?, initMapConfig: IMapAbility.InitMapConfig): String {
        val showMarker = initMapConfig.showMarker
        GLog.d(TAG, LogFlag.DL, "initMapView mapViewContainer $mapViewContainer, initMapConfig $initMapConfig, showMarker: $showMarker")
        val mapWrapper = MapWrapper()
        val mapWrapperId = if (showMarker) {
            val positionInfo = getPositionInfo(mapViewContainer, savedInstanceState, initMapConfig)
            mapWrapper.initMapViewWithMarker(
                mapViewContainer, savedInstanceState,
                initMapConfig, resourceAbility, session, positionInfo, configAbility, hardwareAbility
            )
        } else {
            if (initMapConfig.entranceType == IMapAbility.EntranceType.EXPLORER_MAP_ENTRANCE) {
                mapWrapper.setCaptureMapRegionListener(captureMapRegionListener)
            }
            val positionInfo = getPositionInfo(mapViewContainer, savedInstanceState, initMapConfig)
            mapWrapper.initMapViewWithoutMarker(mapViewContainer, savedInstanceState, initMapConfig, resourceAbility, session, positionInfo)
        }
        //map中放入相应的mapWrapper
        mapWrapperMap.set(mapWrapperId, mapWrapper)
        if (!mapWrapperIdResumedMap.getOrDefault(mapWrapperId, false) && mapWrapperId.isNotEmpty()) {
            // 首次onResume回调时，initMapView还未执行，需要手动调用onResume
            onResume(mapWrapperId)
        }
        GLog.d(TAG, LogFlag.DL, "initMapView mapWrapperId $mapWrapperId, mapSize ${mapWrapperMap.size}")
        return mapWrapperId
    }

    private fun getPositionInfo(
        mapViewContainer: ViewGroup,
        savedInstanceState: Bundle?,
        initMapConfig: IMapAbility.InitMapConfig
    ): IMapAbility.MapStateConfig {
        val viewContext = mapViewContainer.context
        // 这里需要根据savedInstanceState去恢复之前保存的经纬度
        val latLng = if (savedInstanceState != null) {
            savedInstanceState.getParcelable<LatLng>(MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_CENTER_TARGET)
                ?: getLatLng(viewContext, initMapConfig)
        } else {
            getLatLng(viewContext, initMapConfig)
        }
        // 这里需要根据savedInstanceState去恢复之前保存的zoom级别
        val zoomLevel = savedInstanceState?.getFloat(
            MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_ZOOM, getZoomLevel(viewContext, initMapConfig)
                ?: LOCATE_DEFAULT_ZOOM
        ) ?: getZoomLevel(viewContext, initMapConfig)
        val overlookLevel = savedInstanceState?.getFloat(MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_OVERLOOK, 0.0f) ?: 0.0f
        val rotate = savedInstanceState?.getFloat(MapStateConfigKeys.MAP_STATE_CONFIG_KEYS_ROTATE, 0.0f) ?: 0.0f
        val positionInfo = IMapAbility.MapStateConfig(rotate, latLng, zoomLevel, overlookLevel)
        return positionInfo
    }

    private fun getLatLng(context: Context, initMapConfig: IMapAbility.InitMapConfig): LatLng {
        val initLatLng = initMapConfig.initLocation ?: LatLng(Double.NaN, Double.NaN)
        val result = if (initMapConfig.entranceType == IMapAbility.EntranceType.MAP_PAGE_NORMAL) {
            val countryCode = context.resources.configuration.locale.country
            GLog.d(TAG, LogFlag.DL, "getLatLng $countryCode, countryCodeDecoder $countryCodeDecoder")
            countryCodeDecoder?.getPositionByCountryCode(countryCode) ?: LatLng(Double.NaN, Double.NaN)
        } else if (initMapConfig.entranceType == IMapAbility.EntranceType.IMAGE_DETAIL_INNER) {
            initLatLng
        } else {
            initLatLng
        }
        GLog.d(TAG, LogFlag.DL, "getLatLng entranceType ${initMapConfig.entranceType}, initLocaiton $initLatLng, result $result")
        return result
    }

    private fun getZoomLevel(context: Context, initMapConfig: IMapAbility.InitMapConfig): Float? {
        val initZoom = initMapConfig.defaultZoom
        val result = if (initMapConfig.entranceType == IMapAbility.EntranceType.MAP_PAGE_NORMAL) {
            val countryCode = context.resources.configuration.locale.country
            countryCodeDecoder?.getZoomLevelByCountryCode(countryCode)
        } else {
            initZoom
        }
        GLog.d(TAG, LogFlag.DL, "getZoomLevel $result, initZoom: $initZoom")
        return result
    }

    override fun onPause(mapInstanceId: String) {
        val mapWrapper = mapWrapperMap[mapInstanceId]
        mapWrapper?.let {
            mapWrapper.onPause()
        } ?: GLog.e(TAG, LogFlag.DL, "onPause error map $mapInstanceId not found")
    }

    override fun onResume(mapInstanceId: String) {
        GLog.d(TAG, LogFlag.DL, "onResume $mapInstanceId")
        val mapWrapper = mapWrapperMap[mapInstanceId]
        mapWrapper?.let {
            mapWrapper.onResume()
            mapWrapperIdResumedMap[mapInstanceId] = true
        } ?: GLog.e(TAG, LogFlag.DL, "onResume error map $mapInstanceId not found")
    }

    override fun onDestroy(mapInstanceId: String) {
        val mapWrapper = mapWrapperMap[mapInstanceId]
        mapWrapper?.let {
            mapWrapper.onDestroy()
        } ?: GLog.e(TAG, LogFlag.DL, "onDestroy error map $mapInstanceId not found")
        //Map中移除相应的mapWrapper
        mapWrapperMap.remove(mapInstanceId)
        mapWrapperIdResumedMap.remove(mapInstanceId)
        GLog.d(TAG, LogFlag.DL, "onDestroy mapWrapperMap size ${mapWrapperMap.size}, remove $mapInstanceId, mapWrapper $mapWrapper")
    }

    override fun initCountryMapConfig(async: Boolean, checkUpdate: Boolean) {
        if (async) {
            countryCodeDecoder?.initConfigAndCheckConfigAsync(checkUpdate)
        } else {
            countryCodeDecoder?.initConfigAndCheckConfigSync(checkUpdate)
        }
    }

    override fun getCurrentNearByMode(mapInstanceId: String): Boolean {
        val mapWrapper = mapWrapperMap[mapInstanceId]
        return mapWrapper?.let {
            mapWrapper.getNearbyMode()
        } ?: false
    }

    override fun switchShowNearbyImage(mapInstanceId: String, showSingle: Boolean) {
        val mapWrapper = mapWrapperMap[mapInstanceId]
        mapWrapper?.let {
            mapWrapper.setNearbyMode(showSingle)
        } ?: GLog.e(TAG, LogFlag.DL, "switchShowNearbyImage error not found mapId: $mapInstanceId")
    }


    override fun locateInCurrentPosition(mapInstanceId: String, isCaptureMap: Boolean, callback: IMapAbility.ILocationResultCallback?) {
        val mapWrapper = mapWrapperMap[mapInstanceId]
        mapWrapper?.let {
            mapWrapper.locateInCurrentPosition(callback, isCaptureMap)
        } ?: GLog.e(TAG, LogFlag.DL, "locateInCurrentPosition error not found mapId: $mapInstanceId")
    }


    override fun justTrigerMapCapture(mapInstanceId: String, listener: IMapAbility.CaptureMapRegionListener?) {
        val mapWrapper = mapWrapperMap[mapInstanceId]
        mapWrapper?.let {
            mapWrapper.trigerCapture(listener)
        } ?: GLog.e(TAG, LogFlag.DL, "justTrigerMapCapture error not found mapId: $mapInstanceId")
    }

    override fun setCaptureMapRegionListener(listener: IMapAbility.CaptureMapRegionListener?) {
        captureMapRegionListener = listener
    }

    override fun close() = Unit

    /**
     * 实现AbsAppAbility虚拟类必须
     */
    override val domainInstance: IMapAbility = this


    /**
     * 获取resourceAbility，加载marker图片缩略图时需要
     */
    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        resourceAbility = abilityBus.requireAbility(IResourcingAbility::class.java)
        configAbility = abilityBus.requireAbility(IConfigAbility::class.java)
        downloadAbility = abilityBus.requireAbility(IDownloadAbility::class.java)
        downloadAbility?.let {
            countryCodeDecoder = CountryCodePositionDecoder(it, isOnePlusExport)
        }
        hardwareAbility = abilityBus.requireAbility(IHardwareAbility::class.java)
        GLog.d(TAG, LogFlag.DL, "onLoad resourceAbility $resourceAbility")
    }


    /**
     * 释放resourceAbility，
     */
    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        GLog.d(TAG, LogFlag.DL, "onUnload resourceAbility $resourceAbility")
        resourceAbility?.close()
        resourceAbility = null
        downloadAbility?.close()
        downloadAbility = null
        configAbility?.close()
        configAbility = null
        hardwareAbility?.close()
        hardwareAbility = null
        countryCodeDecoder = null
        captureMapRegionListener = null
    }

    /**
     * 获取当前地图的位置状态信息：Rotate、LatLong、Zoom、Overlook
     */
    override fun getCurrentMapStateConfig(mapInstanceId: String): IMapAbility.MapStateConfig? {
        val mapWrapper = mapWrapperMap[mapInstanceId]
        mapWrapper?.let {
            val mapStatusConfig = mapWrapper.getCurrentMapStateStatus()
            mapStatusConfig?.let {
                return IMapAbility.MapStateConfig(
                    it.rotate,
                    it.centerTarget,
                    it.zoom,
                    it.overlook
                )
            }
            GLog.w(TAG, LogFlag.DL, "mapStatusConfig is null")
            return null
        } ?: GLog.e(TAG, LogFlag.DL, "getCurrentMapStateConfig error not found mapId: $mapInstanceId")
        return null
    }
}