/**************************************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File        : - PrivacyAuthorizingAbilityImpl.kt
 ** Description :
 *
 ** Version     : 1.0
 ** Date        : 2022/06/15
 ** Author      : <EMAIL>
 **
 ** ------------------------------------- Revision History ----------------------------------------
 **  <author>                    <data>         <version >     <desc>
 **  <EMAIL>      2022/06/15      1.0            init
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.authorizing

import android.content.Context
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil.TYPE_BOOLEAN
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZATION_RECONFIRMED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_AIHD
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_CLOUD_EDITOR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_AI_DEGLARE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_AI_GRAFFITI
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_AI_ID_PHOTO
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_AI_REPAIR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_CLOUD_SYNC
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_ENHANCE_TEXT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_GALLERY_MAP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_GET_USUAL_LOCATION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_LOCATION_RESOLVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_SHARE_ALBUM
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_TEXT_OCR_SERVICE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_TRAVEL_SCAN
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility

class PrivacyAuthorizingAbilityImpl : AbsAppAbility(), IPrivacyAuthorizingAbility {

    override val domainInstance: IPrivacyAuthorizingAbility = this

    private var configAbility: IConfigSetterAbility? = null

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        configAbility = abilityBus.requireAbility(IConfigSetterAbility::class.java)
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        configAbility?.close()
        configAbility = null
    }

    override fun getCurrentPrivacyVersion(): Int {
        return PRIVACY_STATEMENT_LATEST_VERSION
    }

    override fun getUserAgreePrivacyVersion(): Int? {
        return configAbility?.getIntConfig(
            ConfigID.Common.UserProfile.Privacy.PRIVACY_STATEMENT_VERSION,
            PRIVACY_STATEMENT_DEFAULT_VERSION
        ) ?: PRIVACY_STATEMENT_DEFAULT_VERSION
    }

    override fun isPrivacyStatementVersionUpdated(): Boolean? {
        val privacyVersion = configAbility?.getIntConfig(
            ConfigID.Common.UserProfile.Privacy.PRIVACY_STATEMENT_VERSION,
            PRIVACY_STATEMENT_DEFAULT_VERSION
        ) ?: PRIVACY_STATEMENT_DEFAULT_VERSION
        return (getCurrentPrivacyVersion() > privacyVersion)
    }

    override fun updatePrivacyStatementVersion() {
        configAbility?.setIntConfig(
            ConfigID.Common.UserProfile.Privacy.PRIVACY_STATEMENT_VERSION,
            getCurrentPrivacyVersion()
        )
    }

    override fun setUserAgreePrivacyVersion(version: Int) {
        configAbility?.setIntConfig(
            ConfigID.Common.UserProfile.Privacy.PRIVACY_STATEMENT_VERSION,
            version
        )
    }

    override fun isPrivacyAuthorized(key: String): Boolean? {
        return MultiProcessSpUtil.getBoolean(key, false)
    }

    override fun authorizePrivacy(key: String) {
        if (normalPrivacyList.contains(key) || specialPrivacyList.contains(key)) {
            MultiProcessSpUtil.save(key, true)
        }
        PrivacyTrackHelper.trackFunctionIndividualAgree(key)
    }

    override fun authorizeAllNormalPrivacy() {
        normalPrivacyList.forEach {
            MultiProcessSpUtil.save(it, true)
        }
    }

    override fun cancelAllPrivacyAuthorizations() {
        normalPrivacyList.forEach {
            MultiProcessSpUtil.save(it, false)
        }
        specialPrivacyList.forEach {
            MultiProcessSpUtil.save(it, false)
        }
    }

    override fun cancelPrivacyAuthorization(key: String) {
        if (normalPrivacyList.contains(key) || specialPrivacyList.contains(key)) {
            MultiProcessSpUtil.save(key, false)
        }
    }

    override fun hasAnyPrivacyAuthorized(): Boolean? {
        normalPrivacyList.forEach {
            if (MultiProcessSpUtil.getBoolean(it, false)) {
                return true
            }
        }
        specialPrivacyList.forEach {
            if (MultiProcessSpUtil.getBoolean(it, false)) {
                return true
            }
        }
        return false
    }

    override fun registerPrivacyObserver(context: Context, privacyType: String, observer: HandlerContentObserver) {
        MultiProcessSpUtil.registerSPObserver(context, privacyType, TYPE_BOOLEAN, observer)
    }

    override fun unregisterPrivacyObserver(context: Context, observer: HandlerContentObserver) {
        MultiProcessSpUtil.unregisterSPObserver(context, observer)
    }

    override fun trackUserNoticeAgreeAll() {
        PrivacyTrackHelper.trackUserNoticeAgreeAll()
    }

    override fun trackUserNoticeAgreeBase() {
        PrivacyTrackHelper.trackUserNoticeAgreeBase()
    }

    override fun trackUserNoticeUpdateAgreeAll() {
        PrivacyTrackHelper.trackUserNoticeUpdateAgreeAll()
    }

    override fun trackUserNoticeUpdateAgreeBase() {
        PrivacyTrackHelper.trackUserNoticeUpdateAgreeBase()
    }

    override fun isAuthorizationReconfirmed(): Boolean {
        return MultiProcessSpUtil.getBoolean(AUTHORIZATION_RECONFIRMED, true)
    }

    override fun setAuthorizationReconfirmed(value: Boolean) {
        MultiProcessSpUtil.save(AUTHORIZATION_RECONFIRMED, value)
    }

    override fun close() = Unit

    companion object {
        //这里改为了1305改为了1304，后面@李清峰 改回来
        private const val PRIVACY_STATEMENT_LATEST_VERSION = 1304
        private const val PRIVACY_STATEMENT_DEFAULT_VERSION = -1

        private val normalPrivacyList =
            listOf(
                AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD,
                AUTHORIZE_CLOUD_EDITOR,
                AUTHORIZE_LOCATION_RESOLVE,
                AUTHORIZE_AI_REPAIR,
                AUTHORIZE_AI_ID_PHOTO,
                AUTHORIZE_GET_USUAL_LOCATION,
                AUTHORIZE_SHARE_ALBUM,
                AUTHORIZE_ENHANCE_TEXT,
                AUTHORIZE_TEXT_OCR_SERVICE,
                AUTHORIZE_CLOUD_SYNC,
                AUTHORIZE_AIHD,
                AUTHORIZE_AI_GRAFFITI,
                AUTHORIZE_AI_DEGLARE
            )
        private val specialPrivacyList =
            listOf(
                AUTHORIZE_FACE_SCAN,
                AUTHORIZE_TRAVEL_SCAN,
                AUTHORIZE_GALLERY_MAP
            )
    }
}