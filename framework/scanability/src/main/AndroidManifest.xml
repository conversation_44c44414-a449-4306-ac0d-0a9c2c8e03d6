<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!--接入SAU版本升级需要对应的权限-->
    <uses-permission android:name="com.oplus.permission.safe.SAU" />
    <!--下载功能需要网络权限-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <!--PowerManager#newWakeLock-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!--如果应用Target API 级别是31或者更高，如果要使用以下接口，必须配置新的权限Manifest.permission.SCHEDULE_EXACT_ALARM。-->
    <!--setExactAndAllowWhileIdle(int，long，PendingIntent)  setAlarmClock(AlarmClockInfo，PendingIntent) -->
    <!--面向android 12的应用，有调用这些接口，如果没有配置Manifest.permission.SCHEDULE_EXACT_ALARM权限，将会抛出SecurityException异常。-->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <!-- 多模态扫描要求填写此权限-start -->
    <uses-permission android:name="mediatek.permission.ACCESS_APU_SYS" />
    <!-- 多模态扫描要求填写此权限-end -->

    <application
        android:allowBackup="false"
        tools:replace="android:allowBackup">

        <!-- 多模态扫描要求填写-start -->
        <uses-native-library
            android:name="libneuron_runtime.8.so"
            android:required="false" />

        <uses-native-library
            android:name="libcdsprpc.so"
            android:required="false" />
        <!-- 多模态扫描要求填写-end -->

        <service
            android:name="com.oplus.gallery.framework.abilities.scan.manager.GalleryScanService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <receiver
            android:name="com.oplus.gallery.framework.abilities.scan.component.AlarmReceiver"
            android:exported="false"
            android:enabled="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="com.oplus.gallery.action.trigger.detect" />
            </intent-filter>
        </receiver>

    </application>
</manifest>