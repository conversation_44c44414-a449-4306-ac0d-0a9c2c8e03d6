/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** <p>
 ** File: - CoverBgProcessor.java
 ** Description: 人物封面后台处理器
 ** <p>
 **
 ** Version: 1.0
 ** Date: 2016-11-25
 ** Author: xiuhua.ke
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xiuhua.ke                       2016-11-25      1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.face;

import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaItemSupportFormat.FORMAT_LOSS_LESS_CACHE;
import static com.oplus.gallery.foundation.util.debug.LogFlag.DL;


import android.graphics.Bitmap;
import android.graphics.Rect;

import com.oplus.gallery.business_lib.cache.CacheType;
import com.oplus.gallery.business_lib.cache.FaceCacheKey;
import com.oplus.gallery.business_lib.cache.diskcache.CoverCachedHelper;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.ext.ObjectExtKt;
import com.oplus.gallery.framework.abilities.caching.CacheOperation;
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility;
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory;
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions;
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation;
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.util.ArrayList;

public class CoverBgProcessor {
    private static final String TAG = "CoverBgProcessor";
    private final ArrayList<CoverTask> mTaskQueue = new ArrayList<>();
    private Thread mCoverThread;
    private boolean mSelfExit = false;

    public void addTask(CoverTask task) {
        synchronized (mTaskQueue) {
            mTaskQueue.add(task);
            try {
                initCoverThread();
                mTaskQueue.notifyAll();
            } catch (Exception e) {
                GLog.w(TAG, DL, "[addTask] error: " + e);
            }
        }
    }

    public void existWhenFinished() {
        synchronized (mTaskQueue) {
            mSelfExit = true;
            try {
                initCoverThread();
                mTaskQueue.notifyAll();
            } catch (Exception e) {
                GLog.w(TAG, DL, "[existWhenFinished] error: " + e);
            }
        }
    }

    private void initCoverThread() {
        if (mCoverThread == null) {
            mCoverThread = new CoverThread(TAG);
            mCoverThread.start();
        }
    }

    class CoverThread extends Thread {

        CoverThread(String name) {
            super(name);
        }

        @Override
        public void run() {
            while (!isInterrupted()) {
                CoverTask task = null;
                synchronized (mTaskQueue) {
                    try {
                        if (mTaskQueue.isEmpty()) {
                            if (!mSelfExit) {
                                ObjectExtKt.waitWithoutInterrupt(mTaskQueue);
                            } else {
                                interrupt();
                            }
                        } else {
                            task = mTaskQueue.remove(0);
                        }
                    } catch (Exception e) {
                        interrupt();
                    }
                }
                if (task != null) {
                    MediaItem item = task.mMediaItem;
                    GLog.i(TAG, "CoverTask, thumbNail: " + task.mThumbnail + ", personId: " + task.mPersonId
                            + ", thumbW: " + task.mThumbW + ", thumbH: " + task.mThumbH + ", faceRect: " + task.mFaceRect
                            + ", modifyTime: " + item.getDateModifiedInSec());
                    if (task.mThumbnail == null) {
                        Bitmap faceBitmap = null;
                        if (task.mMediaItem.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
                            Bitmap thumbnail = requestBitmap(task.mMediaItem, task.mThumbW, task.mThumbH);
                            faceBitmap = CoverCachedHelper.getFaceDataFromThumb(thumbnail, task.mThumbW, task.mThumbH, task.mFaceRect);
                        } else if (item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
                            faceBitmap = requestBitmap(task.mMediaItem, task.mThumbW, task.mThumbH);
                        }
                        if (faceBitmap != null) {
                            boolean lossLessCache = item.getSupportFormat(FORMAT_LOSS_LESS_CACHE) == FORMAT_LOSS_LESS_CACHE;
                            FaceCacheKey faceCacheKey = new FaceCacheKey(item.getPath(),
                                    CacheType.FACE_BLOB_CACHE, task.mPersonId, item.getDateModifiedInSec());
                            CoverCachedHelper.writeFaceDataToCache(faceBitmap, faceCacheKey, lossLessCache);
                        }
                    }
                }
            }
        }

        private Bitmap requestBitmap(MediaItem mediaItem, int targetWidth, int targetHeight) {
            ResourceKey resourceKey = ResourceKeyFactory.createResourceKey(mediaItem);
            IResourcingAbility resourcingAbility = ((GalleryApplication) ContextGetter.context.getApplicationContext())
                    .getAppAbility(IResourcingAbility.class);
            ResourceGetOptions options = new ResourceGetOptions(
                    ThumbnailSizeUtils.TYPE_THUMBNAIL,
                    targetWidth,
                    targetHeight,
                    CropParams.centerRectCrop(),
                    CacheOperation.ReadWriteAllCache.INSTANCE,
                    SourceOperation.ReadLocal.INSTANCE
            );
            if ((resourceKey != null) && (resourcingAbility != null)) {
                try {
                    ImageResult<Bitmap> result = resourcingAbility.requestBitmap(resourceKey, options, null, null);
                    if (result != null) {
                        return result.getResult();
                    }
                } finally {
                    resourcingAbility.close();
                }
            }
            return null;
        }
    }

    public static class CoverTask {
        public long mPersonId;
        public int mThumbW;
        public int mThumbH;
        public Rect mFaceRect;
        public MediaItem mMediaItem;
        public Bitmap mThumbnail;
    }
}
