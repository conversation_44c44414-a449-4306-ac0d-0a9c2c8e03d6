/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SinglePersonFaceMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/14
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D     2018/3/14     1.0        build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;
import android.database.Cursor;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.bean.CreateMemoriesEntry;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.util.ArrayList;
import java.util.List;

import static com.oplus.gallery.foundation.database.store.GalleryStore.MetaColumns.IS_RECYCLED;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.GROUP_ID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.IS_SINGLE_FACE;
import static com.oplus.gallery.foundation.database.util.DatabaseUtils.getCountProjection;

public class SinglePersonFaceMemories extends Memories {
    private static final String TAG = "SinglePersonFaceMemories";
    private static final String STOP_REASON_SCAN_FAILED = TAG + "_ScanFailed";
    private static final String STOP_REASON_CREATED_FAILED = TAG + "_CreatedFailed";
    private static final String STOP_REASON_ITEM_LIST_EMPTY = TAG + "_ItemListEmpty";
    private static final String STOP_REASON_LESS_THAN_4_DAYS = TAG + "_LessThan4Days";
    private int mNameType = -1;

    public SinglePersonFaceMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        long lastMemoriesTime = MemoriesProviderHelper.getLastSpecifiedMemoriesTime(getMemoriesId());
        if ((lastMemoriesTime != 0) && (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(lastMemoriesTime,
                System.currentTimeMillis()) < getIntOptimalConfig(FACE_MEMORIES_INTERVAL))) {
            GLog.d(TAG, "prepareMemories,current time interval last SinglePersonFaceMemories time less than 4 days!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_LESS_THAN_4_DAYS;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        ArrayList<Long> groupIdList = GroupHelper.getTopGroupIdForMemories(getIntOptimalConfig(SINGLE_PERSON_MEMORIES_TOP_COUNT));
        for (long groupId : groupIdList) {
            MemoriesScannerHelper.GroupIdListInfo info = new MemoriesScannerHelper.GroupIdListInfo();
            info.mIdList = new ArrayList<>();
            info.mIdList.add(groupId);
            info.mIdListString = MemoriesScannerHelper.GroupIdListInfo.convertIdListToString(info.mIdList);
            //current time interval last FACE_SINGLE_PERSON_MEMORIES time with same person should more than 3 months
            long samePersonLastMemoriesTime = MemoriesProviderHelper
                    .getSamePersonLastMemoriesTime(getMemoriesId(), info.mIdListString);
            if ((samePersonLastMemoriesTime != 0) && (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(samePersonLastMemoriesTime,
                    System.currentTimeMillis()) < getIntOptimalConfig(SAME_FACE_MEMORIES_INTERVAL))) {
                GLog.d(TAG, "scanMemories, current time interval last"
                        + " SinglePersonFaceMemories time with same person less than 30 days!");
                continue;
            }
            //pictures with single face must more than 5
            if (getPictureCountOfSingleFace(mContext, info.mIdList.get(0)) < getIntOptimalConfig(MEMORIES_WITH_SINGLE_FACE_PIC_COUNT_MIN)) {
                GLog.d(TAG, "scanMemories, pictures with single face is less than 5!");
                continue;
            }

            MemoriesScannerHelper.DateRange dateRange = new MemoriesScannerHelper.DateRange();
            dateRange.mStart = samePersonLastMemoriesTime;
            dateRange.mEnd = System.currentTimeMillis();
            ArrayList<MediaItem> items = MemoriesProviderHelper.getItemListOfSinglePerson(mContext, groupId, dateRange, mMemoriesPicMin);
            List<MediaItem> filteredItemList = MemoriesProviderHelper.getFilteredItemList(items, getLabelFilterIdList());
            GLog.v(TAG, "scanMemories, filteredItemList size:" + filteredItemList.size());
            if (filteredItemList.size() > mMemoriesPicMin) {
                if (createMemories(filteredItemList, info.mIdListString)) {
                    return true;
                }
            }
        }
        // 扫描中断，记录原因
        mStopReason = STOP_REASON_SCAN_FAILED;
        return false;
    }

    @Override
    public boolean createMemories() {
        return true;
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.FACE_SINGLE_PERSON_MEMORIES;
    }

    @Override
    public int getNameType() {
        if (mNameType < 0) {
            mNameType = MemoriesNameRules.getFaceRandomNameType();
        }
        return mNameType;
    }

    private static int getPictureCountOfSingleFace(Context context, long groupId) {
        int count = 0;
        Cursor cursor = null;
        try {
            String sb = MemoriesProviderHelper.getWhereClauseOfMemoriesScanFolders(context)
                    + " AND " + DatabaseUtils.getDataValidWhere()
                    + " AND " + IS_RECYCLED + " != 1"
                    + " AND " + GROUP_ID + " = " + groupId
                    + " AND " + IS_SINGLE_FACE + " = 1 ";
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setProjection(getCountProjection())
                    .setWhere(sb)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor != null) && cursor.moveToNext()) {
                count = cursor.getInt(0);
            }
        } catch (Exception e) {
            GLog.w(TAG, "getPictureCountOfSingleFace exception:" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return count;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    private boolean createMemories(List<MediaItem> itemList, String groupIdStr) {
        if ((itemList == null) || (itemList.isEmpty())) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_ITEM_LIST_EMPTY;
            return false;
        }
        GLog.v(TAG, "createMemories size:" + itemList.size() + ", type:" + getMemoriesId());
        MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        CreateMemoriesEntry.CreateFaceMemoriesEntry createFaceMemoriesEntry = new CreateMemoriesEntry.CreateFaceMemoriesEntry.Builder()
                .setName(getName(groupIdStr))
                .setType(getMemoriesId())
                .setGroupIdStr(groupIdStr)
                .setStartTime(dateRange.mStart)
                .setEndTime(dateRange.mEnd)
                .setNameType(getNameType())
                .build();
        int setId = MemoriesProviderHelper.createFaceMemories(createFaceMemoriesEntry);
        if (setId == -1) {
            GLog.w(TAG, "createMemories create memories failed!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATED_FAILED;
            return false;
        }
        return processMemoriesItems(setId, itemList, dateRange.mStart, dateRange.mEnd);
    }

    private String getName(String groupIdStr) {
        if (TextUtils.isEmpty(groupIdStr)) {
            return getMemoriesName();
        }
        return MemoriesProviderHelper.getFaceName(Long.parseLong(groupIdStr));
    }
}