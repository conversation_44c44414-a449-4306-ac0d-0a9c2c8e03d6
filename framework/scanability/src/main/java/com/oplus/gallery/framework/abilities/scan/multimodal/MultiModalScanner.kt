/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MultiModalScanner.kt
 ** Description:多模态扫描
 ** Version: 1.0
 ** Date: 2023/9/25
 ** Author: W9009912
 ** TAG:
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                     <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** W9009912        2023/9/25        1.0         first created
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.scan.multimodal

import android.content.Context
import android.provider.MediaStore.Files.FileColumns
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.MultiModal.ABILITY_IS_SUPPORT_MULTIMODAL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.MultiModal.FIRST_SCAN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.MultiModal.SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.MultiModal.SCAN_COUNT_ALL
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.refreshScanRecordingIfNecessary
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanPersonPetProviderHelper
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.abilities.search.multimodal.IMultiModalAbility
import com.oplus.gallery.framework.abilities.search.multimodal.MultiModalCode
import com.oplus.gallery.framework.abilities.search.multimodal.MultiModalImageInfo
import com.oplus.gallery.framework.abilities.search.multimodal.MultiModalScanProviderHelper
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility

/**
 * 多模态扫描
 */
class MultiModalScanner(val context: Context) : GalleryScan(context) {

    /**
     * 24小时内已经扫描的数量
     */
    private var scannedCountIn24hours = 0

    /**
     * 本次扫描总共扫描的数量
     */
    private var scannedTotalCount = 0

    /**
     * 所有图片列表
     */
    private var allImageList: MutableList<MultiModalImageInfo> = mutableListOf()

    /**
     * 上次扫描的图片列表
     */
    private var lastScannedImageList: MutableList<MultiModalImageInfo> = mutableListOf()

    /**
     * 获取缩图的总时间
     */
    private var getThumbTotalTime: Long = 0L

    /**
     * 每个批次获取缩图的总时间
     */
    private var getThumbTimeBatch: Long = 0L

    /**
     * 多模态processImage接口的执行总时间
     */
    private var processTotalTime: Long = 0L

    override fun getScanType(): Int {
        return MULTI_MODAL_SCAN
    }

    override fun getSceneName(): String = SCENE_NAME

    override fun onScan(triggerType: Int, config: ScanConfig) {
        GLog.w(TAG, "onScan, start scan multimodal triggerType = $triggerType")
        super.onScan(triggerType, config)
        scanData(triggerType)
        GLog.w(TAG, "onScan, multimodal scan end")
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }

    @Suppress("LongMethod")
    private fun scanData(scanTriggerType: Int) {
        GLog.d(TAG, "scanData, start, scanTriggerType is $scanTriggerType")
        markConditionsOnStartScan()
        recordScanDataIn24H()
        context.getAppAbility<IMultiModalAbility>()?.use {
            try {
                if (it.isDownloading) {
                    // multiModalAbility下载模型中，直接return
                    GLog.e(TAG, "scanData, multiModalAbility is downloading, return")
                    updateAbilityIsSupported(false)
                    return
                }
                val scanStartTime = System.currentTimeMillis()
                it.isScanning = true

                allImageList = getAllMediaList()
                if (allImageList.isEmpty()) {
                    GLog.w(TAG, "scanData, allImageList is empty")
                    return
                }

                // 获取已扫描人脸的图片的人脸状态，拒识需要用到人脸状态
                val faceStatus = GalleryScanPersonPetProviderHelper.getFaceStatus()

                if (it.initSlpSdk().not()) {
                    // 初始化失败，直接return
                    GLog.e(TAG, "scanData, init multi modal engine fail, return")
                    updateAbilityIsSupported(false)
                    return
                }
                // 模型更新，需要先删除旧模型生成的向量数据库和清除相册数据库
                if (it.hasOldModelData()) {
                    val removeStatus = it.removeOldModelData()
                    var delCount = 0
                    if (removeStatus) {
                        delCount = DeleteReq.Builder()
                            .setDaoType(IDao.DaoType.GALLERY)
                            .setTableType(GalleryDbDao.TableType.SCAN_MULTI_MODAL)
                            .build().exec()
                    }
                    GLog.d(TAG, LogFlag.DL, "scanData, model change need clear db. removeStatus: $removeStatus, delCount: $delCount")
                }
                updateAbilityIsSupported(true)
                scannedTotalCount = 0
                getThumbTotalTime = 0
                processTotalTime = 0

                // 获取已扫描数据
                lastScannedImageList = MultiModalScanProviderHelper.getAllMultiModalScanData()
                // 需要全新扫描的媒体列表
                val needScanMediaList = getNeedScanMediaList()
                // 需要重新扫描:更新向量的媒体列表
                val needRescanList = getNeedRescanMediaList(it)
                if (needScanMediaList.isEmpty() && needRescanList.isEmpty()) {
                    GLog.w(TAG, "scanData, needScanMediaList,needOnlyUpdateDbMediaList and needRescanList is empty")
                    // 记录扫描时间戳
                    updateLastScanTime()
                    return
                }

                // 对新数据执行扫描
                doScan(it, needScanMediaList, faceStatus, true)
                // 对需要重扫的数据执行重新扫描
                doScan(it, needRescanList, faceStatus, false)

                // 处理需要删除的
                val needDeleteList = getNeedDeleteList()
                handleDelete(it, needDeleteList)

                // 记录扫描时间戳
                updateLastScanTime()

                val totalScanCostTime = System.currentTimeMillis() - scanStartTime
                if (scannedTotalCount > 0) {
                    GLog.w(
                        TAG, "scanData, multimodalScan costTime total: $totalScanCostTime ms, " +
                                "scannedTotalCount:$scannedTotalCount, "
                                + "perItemScanTime:${totalScanCostTime / scannedTotalCount.toFloat()}ms"
                                + ",getThumbTotalTime:$getThumbTotalTime, "
                                + "perItemGetThumbTime:${getThumbTotalTime / scannedTotalCount.toFloat()}ms"
                                + ",processTotalTime:$processTotalTime, "
                                + "perItemProcessTime:${processTotalTime / scannedTotalCount.toFloat()}ms"
                    )
                } else {
                    GLog.w(TAG, "scanData, scannedTotalCount:$scannedTotalCount")
                }
            } finally {
                GLog.d(TAG, "scanData, finally, release and set multiModalAbility is scanning to false")
                it.release()
                it.isScanning = false
            }
        }
    }

    /**
     * 判断是否是24H内的第一次扫描，并获取24H小时内的扫描数据
     */
    private fun recordScanDataIn24H() {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            mIsFirstScan = it.getBooleanConfig(FIRST_SCAN, true) ?: true
            if (mIsFirstScan) {
                it.setBooleanConfig(FIRST_SCAN, false)
            }
            // 24小时内扫描数量
            scannedCountIn24hours = it.getIntConfig(SCAN_COUNT_24H, 0) ?: 0
        }
    }

    /**
     * 更新能力是否可用
     */
    private fun updateAbilityIsSupported(isSupported: Boolean) {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            it.setBooleanConfig(ABILITY_IS_SUPPORT_MULTIMODAL, isSupported)
        }
    }

    /**
     * 获取所有数据
     */
    private fun getAllMediaList(): MutableList<MultiModalImageInfo> {
        return GalleryScanProviderHelper.getAllImageFromFileTable(mContext).map {
            format2MultiModalImageInfo(it)
        }.toMutableList()
    }

    /**
     * 获取需要扫描的图片列表
     */
    private fun getNeedScanMediaList(): MutableList<MultiModalImageInfo> {
        // 已经扫描的列表
        val map = GalleryScanDataManager.translateListToPathMap(lastScannedImageList)
        val needScanImageList = allImageList.filter {
            (map[it.mFilePath.lowercase()] == null) && (isSupportMediaType(it.mMediaType))
        }.toMutableList()
        GLog.d(
            TAG, "getNeedScanMediaItemList, allImageList.size = ${allImageList.size}, " +
                    "lastScannedImageList.size = ${lastScannedImageList.size},  needScanImageList.size = ${needScanImageList.size}"
        )
        return needScanImageList
    }

    /**
     * 获取需要删除的图片列表
     */
    private fun getNeedDeleteList(): MutableList<MultiModalImageInfo> {
        val map = GalleryScanDataManager.translateListToPathMap(allImageList)
        val needDeleteList = lastScannedImageList.filter {
            map[it.mFilePath.lowercase()] == null
        }.toMutableList()
        GLog.d(
            TAG, "getNeedDeleteList, allImageList.size = ${allImageList.size}, " +
                    "lastScannedImageList.size = ${lastScannedImageList.size}, needDeleteList.size = ${needDeleteList.size}"
        )
        return needDeleteList
    }

    /**
     * 获取需要重新扫描的图片列表，即需要重新生成向量和更新数据库的列表
     */
    private fun getNeedRescanMediaList(ability: IMultiModalAbility): MutableList<MultiModalImageInfo> {
        val notDataValid = ability.checkDataValid().not()
        val needRescanList = mutableListOf<MultiModalImageInfo>()

        /*
        * 需要重新扫描的列表
        * 来源1：notDataValid为true的数据
         */
        val notDataValidList = lastScannedImageList.filter {
            // 是支持的文件类型 && 有新版本 && (notDataValid == true)
            (isSupportMediaType(it.mMediaType)
                    && ability.hasNewVersion(it.versionModelPath)
                    && notDataValid)
        }.toMutableList()

        // 如果notDataValid为true，所有数据需要重新扫描。将数据库中所有状态设置为CODE_NEED_RESCAN
        if (notDataValid) {
            notDataValidList.forEach {
                it.mScanState = MultiModalCode.CODE_NEED_RESCAN
            }
            handleUpdateDb(notDataValidList, null)
        }

        /*
        * 需要重新扫描的列表
        * 来源2：数据库中scanState为CODE_NEED_RESCAN的数据
        * 因为中途打断了扫描，checkDataValid()发生了变化，所以必须从数据库中读取
         */
        val stateNeedRescanList = lastScannedImageList.filter {
            it.mScanState == MultiModalCode.CODE_NEED_RESCAN
        }

        needRescanList.addAll(stateNeedRescanList)
        needRescanList.addAll(notDataValidList)

        GLog.d(
            TAG, "getNeedReScanList, notDataValid = $notDataValid, lastScannedImageList.size = ${lastScannedImageList.size}, " +
                    "stateNeedRescanList.size = ${stateNeedRescanList.size}, " +
                    "notDataValidList.size = ${notDataValidList.size}, " +
                    "result.size = ${needRescanList.toMutableSet().toMutableList().size}"
        )

        return needRescanList.toMutableSet().toMutableList()
    }

    /**
     * 处理需要新增的图片
     *
     * 1、getOneScanCountList()，获取小于等于ONE_SCAN_COUNT个item
     * 2、使用这若干个item调用convertImageToVector()，调用底层processImage()转换成向量
     * 3、recordScanCount()，记录扫描数量信息
     * 4、调用底层flushData()，如果返回true，insertMultiModalScanData()，将扫描成功的item新增到数据库
     * 5、将被中断扫描的item，累加到interruptList
     * 6、休眠SLEEP_TIME_EACH_BACH毫秒
     */
    private fun doScan(
        ability: IMultiModalAbility,
        needScanList: MutableList<MultiModalImageInfo>,
        faceStatus: Map<String, Boolean>,
        isAdd: Boolean
    ): MutableList<MultiModalImageInfo> {
        val interruptList = mutableListOf<MultiModalImageInfo>()
        while ((needScanList.size > 0)
            && isAllowContinueScan(scannedCountIn24hours, GalleryScanMonitor.MULTI_MODAL_SCAN_COUNT_24H_MAX, true)
        ) {
            val startTime = System.currentTimeMillis()
            val oneScanCountList = getOneScanCountList(needScanList)
            val convertResultList = convertImageToVector(ability, oneScanCountList, faceStatus)
            val flushResult = ability.flushData()
            GLog.d(
                TAG, "doScan, flushResult = $flushResult, " +
                        "convertResultList = ${convertResultList.map { it.mScanState }}"
            )
            if (flushResult) {
                val dbStartTime = System.currentTimeMillis()
                if (isAdd) {
                    MultiModalScanProviderHelper.insertMultiModalScanData(convertResultList, ability.currentModelVersion)
                } else {
                    handleUpdateDb(convertResultList, ability.currentModelVersion)
                }
                if (GProperty.DEBUG_SCAN) {
                    GLog.d(TAG, "doScan multimodalScan costTime dbCostTime:${GLog.getTime(dbStartTime)}ms")
                }
            }
            convertResultList.forEach {
                if (it.mScanState == MultiModalCode.CODE_INTERRUPT) {
                    interruptList.add(it)
                }
            }
            recordScanCount(oneScanCountList.size)
            scannedTotalCount += oneScanCountList.size
            val batchTotalCount = convertResultList.size
            if (batchTotalCount > 0) {
                val batchTotalTime = System.currentTimeMillis() - startTime
                GLog.d(
                    TAG, "doScan multimodalScan costTime batchTotalTime:$batchTotalTime, batchTotalCount:$batchTotalCount, "
                            + "scannedTotalCount:$scannedTotalCount, " + "perItemTime:${batchTotalTime / batchTotalCount.toFloat()}ms"
                            + ",perItemGetThumbTime:${getThumbTimeBatch / batchTotalCount.toFloat()}ms"
                )
            }
            if (isInterrupt.not() && (SLEEP_TIME_EACH_BACH > 0)) {
                sleep(SLEEP_TIME_EACH_BACH)
            }
            bindSmallCore()
        }
        return interruptList
    }

    /**
     * 处理删除图片向量
     */
    private fun handleDelete(
        ability: IMultiModalAbility,
        needDeleteList: MutableList<MultiModalImageInfo>
    ) {
        val successDeleteList = ability.deleteDataByLocalMediaTableIds(needDeleteList)
        GLog.d(TAG, "handleDelete, successDeleteList.size = ${successDeleteList.size}")
        MultiModalScanProviderHelper.deleteMultiModalScanData(successDeleteList)
        bindSmallCore()
    }

    /**
     * 更新数据库
     */
    private fun handleUpdateDb(
        needUpdateDbList: MutableList<MultiModalImageInfo>,
        version: String?
    ) {
        MultiModalScanProviderHelper.updateMultiModalScanData(needUpdateDbList, version)
        bindSmallCore()
    }

    /**
     * 获取小于等于ONE_SCAN_COUNT的list
     */
    private fun getOneScanCountList(
        allList: MutableList<MultiModalImageInfo>
    ): MutableList<MultiModalImageInfo> {
        val subAddSize = if (allList.size >= ONE_SCAN_COUNT) {
            ONE_SCAN_COUNT
        } else {
            allList.size
        }
        val subList = mutableListOf<MultiModalImageInfo>()
        for (i in 0 until subAddSize) {
            subList.add(allList.removeAt(0))
        }
        return subList
    }

    /**
     * 记录 SCAN_COUNT_ALL 和 SCAN_COUNT_24H 的数据
     */
    private fun recordScanCount(count: Int) {
        addScanCountIfNoCharging(count)
        recordScanInfoIfNeed(count)
    }

    /**
     * 图片转换成向量
     *
     * 1、获取bitmap
     * 2、调用ability.processImage()
     * 3、释放bitmap
     * 4、返回resultList扫描结果list，里面包含扫描成功失败状态码
     *
     * 扫描成功失败状态码：
     * 调用ability.processImage()返回true时，状态码赋为MultiModalCode.CODE_SUCCESS
     * 调用ability.processImage()返回false时，说明底层发生错误，状态码赋为MultiModalCode.CODE_SDK_ERROR
     * 调用过程中被中断，比如亮屏、高温，状态码赋为MultiModalCode.CODE_INTERRUPT
     * 获取bitmap为空，状态码赋为MultiModalCode.CODE_INTERRUPT
     */
    private fun convertImageToVector(
        ability: IMultiModalAbility,
        subList: MutableList<MultiModalImageInfo>,
        faceStatus: Map<String, Boolean>
    ): MutableList<MultiModalImageInfo> {
        val resultList = mutableListOf<MultiModalImageInfo>()
        val mediaItems = GalleryScanProviderHelper.convertImageToMediaItem(subList as List<BaseImageInfo>)

        GLog.d(TAG, "processImage, mediaItems.size = ${mediaItems.size}")
        getThumbTimeBatch = 0
        subList.forEach { info ->
            if (isInterrupt.not()) {
                val startTime = System.currentTimeMillis()
                val maskPath = PathMask.mask(info.mFilePath)
                val hasFace = if (faceStatus.containsKey(info.mFilePath)) {
                    val result = faceStatus.getValue(info.mFilePath)
                    GLog.d(TAG, "processImage, mFilePath[ $maskPath ], hasFace = $result")
                    result
                } else {
                    GLog.d(TAG, "processImage, mFilePath[ $maskPath ] is not in faceStatusMap")
                    false
                }
                val mediaItem = mediaItems.find { it.id.toLong() == info.mGalleryId }
                val getThumbTime = System.currentTimeMillis()
                val bitmap = getThumbnail(mediaItem)
                getThumbTotalTime += GLog.getTime(getThumbTime)
                if (GProperty.DEBUG_SCAN) {
                    getThumbTimeBatch += GLog.getTime(getThumbTime)
                    GLog.d(TAG, "convertImageToVector multimodalScan costTime getThumbTime:${GLog.getTime(getThumbTime)}")
                }
                val processTime = System.currentTimeMillis()
                bitmap?.let {
                    if (ability.processImage(info.mGalleryId, it, hasFace)) {
                        info.mScanState = MultiModalCode.CODE_SUCCESS
                    } else {
                        info.mScanState = MultiModalCode.CODE_SDK_ERROR
                    }
                    it.recycle()
                } ?: let {
                    GLog.d(TAG, "processImage, bitmap is null, filePath = $maskPath")
                    info.mScanState = MultiModalCode.CODE_SDK_ERROR
                }
                processTotalTime += GLog.getTime(processTime)
                if (GProperty.DEBUG_SCAN) {
                    GLog.d(TAG, "convertImageToVector multimodalScan costTime processTime:${GLog.getTime(processTime)}")
                }
                mScanImageCount++
                if (GProperty.DEBUG_SCAN) {
                    GLog.d(TAG, "convertImageToVector multimodalScan costTime oneScanTime:${GLog.getTime(startTime)}")
                }
            } else {
                info.mScanState = MultiModalCode.CODE_INTERRUPT
            }
            resultList.add(info)
        }
        return resultList
    }

    /**
     * 是否是支持的文件类型。目前只支持图片
     */
    private fun isSupportMediaType(mMediaType: Int): Boolean {
        return mMediaType == FileColumns.MEDIA_TYPE_IMAGE
    }

    private fun addScanCountIfNoCharging(count: Int): Boolean {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true
        }
        refreshScanRecordingIfNecessary(mContext)
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            scannedCountIn24hours = (it.getIntConfig(SCAN_COUNT_24H, 0) ?: 0) + count
            it.setIntConfig(SCAN_COUNT_24H, scannedCountIn24hours)
        }
        return false
    }

    private fun recordScanInfoIfNeed(count: Int) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return
        }
        (context.applicationContext as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            val scanCountAll = (it.getIntConfig(SCAN_COUNT_ALL, 0) ?: 0) + count
            it.setIntConfig(SCAN_COUNT_ALL, scanCountAll)
            GalleryScanUtils.recordInfo(mContext, TAG, scannedCountIn24hours.toLong(), scanCountAll.toLong())
        }
    }

    /**
     * BaseImageInfo转成MultiModalImageInfo
     */
    private fun format2MultiModalImageInfo(baseImageInfo: BaseImageInfo): MultiModalImageInfo {
        val info = MultiModalImageInfo()
        info.mGalleryId = baseImageInfo.mGalleryId
        info.mMediaId = baseImageInfo.mMediaId
        info.mMediaType = baseImageInfo.mMediaType
        info.mDateModifiedInSec = baseImageInfo.mDateModifiedInSec
        info.mDateTaken = baseImageInfo.mDateTaken
        info.mFilePath = baseImageInfo.mFilePath
        info.mInvalid = baseImageInfo.mInvalid
        info.mIsRecycled = baseImageInfo.mIsRecycled
        info.mNewMediaId = baseImageInfo.mNewMediaId
        info.mNewFilePath = baseImageInfo.mNewFilePath
        info.mOrientation = baseImageInfo.mOrientation
        info.mSize = baseImageInfo.mSize
        info.mExtTagFlags = baseImageInfo.mExtTagFlags
        info.mScanState = baseImageInfo.mScanState
        return info
    }

    companion object {
        private const val TAG = "${ScanConst.TAG}MultiModalScanner"
        private const val SCENE_NAME = "MultiModalScanner"

        /**
         * 单次扫描的数量 30张
         */
        private const val ONE_SCAN_COUNT = 30

        /**
         * 每次扫描间隔300秒
         */
        private const val SLEEP_TIME_EACH_BACH = 200
    }
}