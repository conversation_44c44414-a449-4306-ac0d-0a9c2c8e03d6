/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlarmReceiver.java
 ** Description:
 **     GalleryScanService，通过JobService启动扫描。
 **
 ** Version: 1.0
 ** Date: 2016-11-22
 ** Author: xiuhua.ke
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xiuhua.ke                       2016-11-22   1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.manager;

import static com.oplus.gallery.standard_lib.app.AppConstants.Action.LOCAL_ACTION_SCAN_SERVICE_JOB;
import static com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.Value;
import static com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_1_HOUR_IN_MS;

import android.app.job.JobInfo;
import android.app.job.JobParameters;
import android.app.job.JobScheduler;
import android.app.job.JobService;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.oplus.gallery.basebiz.helper.RemoteModelInfoManager;
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.systemcore.WakeLockUtil;
import com.oplus.gallery.foundation.util.version.ApiLevelUtil;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.standard_lib.scheduler.JobServiceIdPool;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.addon.scheduler.JobInfoWrapper;

public class GalleryScanService extends JobService {
    private static final String TAG = ScanConst.TAG + "GalleryScanService";
    private static final String SCENE_RUN_SCAN_ON_JOB_START = "RunScanOnJobStart";
    private static final String SCENE_CREATE_JOB_SCHEDULE_TO_SCAN = "CreateJobScheduleToScan";
    private static final long TIME_DELAY_MILLIS_DEFAULT_TEST = 25 * TimeUtils.TIME_1_SEC_IN_MS; // 25s
    private static final long TIME_BACKOFF_MILLIS_DEFAULT_TEST = TimeUtils.TIME_10_SEC_IN_MS; // 10s
    private static final String KEY_WANTS_RESCHEDULE = "wantsReschedule.key";
    private JobParameters mParameters;

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (mParameters != null) {
                jobFinished(mParameters, intent.getBooleanExtra(KEY_WANTS_RESCHEDULE, false));
            }
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        long time = System.currentTimeMillis();
        GTrace.traceBegin(TAG + ".onCreate");
        try {
            IntentFilter filter = new IntentFilter(LOCAL_ACTION_SCAN_SERVICE_JOB);
            LocalBroadcastManager.getInstance(this).registerReceiver(mReceiver, filter);
        } finally {
            GTrace.traceEnd();
            GLog.w(TAG, "onCreate, registerReceiver cost time=" + (System.currentTimeMillis() - time));
        }
    }

    @Override
    public boolean onStartJob(JobParameters parameters) {
        GLog.d(TAG, "onStartJob");
        //初始化下载相关的组件
        RemoteModelInfoManager.fetch(this, null, false, GalleryScanService.TAG);
        realStartScan(parameters);
        return true;
    }

    private void realStartScan(JobParameters parameters) {
        GLog.d(TAG, "JobParameters parameters onStartJob, jobId: " + parameters.getJobId());
        mParameters = parameters;
        if (GalleryScanMonitor.isAllowStartScan(this, false, false)) {
            /*
            add function in this place before sendScanMsg, because system will forceStop gallery after sendScanMsg and doLabelDataScan.
            start job schedule scan.
            */
            GalleryScanSyncTaskManager.INSTANCE.doAllDataScan(Value.SCHEDULE_JOB_SCAN_TRIGGER);
            GLog.w(TAG, "onStartJob, jobId: " + parameters.getJobId() + " is running");
        } else {
            // 扫描条件不满足，埋点记录
            ScanTrackHelper.trackStopReasonOnStart(this, SCENE_RUN_SCAN_ON_JOB_START, Value.SCHEDULE_JOB_SCAN_TRIGGER);
            GLog.w(TAG, "onStartJob: jobId: " + parameters.getJobId() + " is not not allow. it will restart next time.");
            jobFinished(parameters, true);
        }
    }

    @Override
    public boolean onStopJob(JobParameters parameters) {
        if (parameters == null) {
            GLog.e(TAG, "onStopJob, parameters is null!");
        } else if (ApiLevelUtil.isAtLeastAndroidS()) {
            GLog.w(TAG, "onStopJob, jobId: " + parameters.getJobId()
                    + ", reason:" + parameters.getStopReason()
                    + ", isHeld:" + WakeLockUtil.isHeld());
        } else {
            GLog.w(TAG, "onStopJob, jobId: " + parameters.getJobId()
                    + ", isHeld:" + WakeLockUtil.isHeld());
        }
        /*
        onStopJob回调经常在电量、温控等良好的情况下调用，导致扫描被停止，故去掉此中断条件
        GalleryScanSyncTaskManager.INSTANCE.interruptScan(GalleryScanMonitor.ReasonType.REASON_SYS_INTERRUPT);
        return true. it means will restart job
        */
        return needRescheduleJob();
    }

    private boolean needRescheduleJob() {
        return !GalleryScanMonitor.isScreenOnAndKeyguardUnlocked();
    }

    @Override
    public void onDestroy() {
        GLog.w(TAG, "onDestroy, wakeLock isHeld:" + WakeLockUtil.isHeld());
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mReceiver);
    }

    public static void finishJob(Context context, boolean wantsReschedule) {
        Intent intent = new Intent(LOCAL_ACTION_SCAN_SERVICE_JOB);
        intent.putExtra(KEY_WANTS_RESCHEDULE, wantsReschedule);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
        GLog.d(TAG, "finishJob: jobId: " + JobServiceIdPool.GALLERY_SCAN_SERVICE.getJobId());
    }

    public static void scheduleJob(Context context) {
        ComponentName componentName = new ComponentName(context, GalleryScanService.class);
        JobScheduler js = (JobScheduler) context.getSystemService(Context.JOB_SCHEDULER_SERVICE);
        JobInfo.Builder builder = new JobInfo.Builder(JobServiceIdPool.GALLERY_SCAN_SERVICE.getJobId(), componentName);
        if (GalleryScanUtils.isTestMode()) {
            builder.setMinimumLatency(TIME_DELAY_MILLIS_DEFAULT_TEST)
                    .setOverrideDeadline(TIME_DELAY_MILLIS_DEFAULT_TEST)
                    .setBackoffCriteria(TIME_BACKOFF_MILLIS_DEFAULT_TEST, JobInfo.BACKOFF_POLICY_LINEAR);
        } else {
            builder.setBackoffCriteria(TIME_1_HOUR_IN_MS, JobInfo.BACKOFF_POLICY_LINEAR);
            JobInfoWrapper.setRequiresBattIdle(builder, true, 0);
        }
        JobInfo job = builder.build();
        if (js != null) {
            int scheduleId = js.schedule(job);
            GLog.d(TAG, LogFlag.DL, "scheduleJob, jobId: " + JobServiceIdPool.GALLERY_SCAN_SERVICE.getJobId()
                    + ",realStatus: " + scheduleId + ",testModel: " + GalleryScanUtils.isTestMode());
        } else {
            // job任务初始化失败，埋点记录
            ScanTrackHelper.trackStopReasonOnStart(context, SCENE_CREATE_JOB_SCHEDULE_TO_SCAN, Value.SCHEDULE_JOB_SCAN_TRIGGER);
            GLog.d(TAG, LogFlag.DL, "JobScheduler init failed.");
        }
    }
}