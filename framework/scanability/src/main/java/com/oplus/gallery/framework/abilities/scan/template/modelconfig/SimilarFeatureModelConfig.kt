/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SimilarFeatureModelConfig.kt
 ** Description : 相似特征ModelConfig
 ** Version     : 1.0
 ** Date        : 2024/3/22
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/3/22      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * 相似特征ModelConfig
 */
class SimilarFeatureModelConfig(context: Context) : SeniorSelectModelConfig(context, ModelName.SIMILAR_FEATURE_SOURCE) {

    override val builtInVersionKey = SIMILAR_FEATURE_COMPONENT_DEFAULT_VERSION_NAME

    override val modelFileName: String = SIMILAR_FEATURE_MODEL_NAME

    companion object {
        /**
         * 从manifest.ini中读取版本号时的key
         */
        const val SIMILAR_FEATURE_COMPONENT_DEFAULT_VERSION_NAME = "similar_feature_component_default_version"

        /**
         * 加载模型文件
         */
        private const val SIMILAR_FEATURE_MODEL_NAME = "similar_feature.model"
    }
}