/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EnglishLabelGraphModelConfig.kt
 ** Description : 英文知识图谱资源配置类
 ** Version     : 1.0
 ** Date        : 2024/8/9
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                            2024/8/9      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.framework.abilities.extraction.labelgraph.BaseLabelGraphModelDownloadConfig
import com.oplus.gallery.framework.abilities.extraction.labelgraph.EnglishLabelGraphModelDownloadConfig

/**
 * 英文知识图谱资源配置类
 */
class EnglishLabelGraphModelConfig(val context: Context) : ModelConfig(ModelName.ENGLISH_LABEL_GRAPH_SOURCE) {

    override val modelDataRefreshFlagKey: String = EnglishLabelGraphModelDownloadConfig.hasLabelGraphDataUpdateSuccessKey

    override var currentVersion: Int
        get() = EnglishLabelGraphModelDownloadConfig.getModelVersion(context)
        set(value) {
            EnglishLabelGraphModelDownloadConfig.setModelVersion(context, value)
        }

    override fun isModelCanRW(): Boolean = checkDefaultPathFileCanRW(EnglishLabelGraphModelDownloadConfig.labelGraphDbName)
            && checkDefaultPathFileCanRW(BaseLabelGraphModelDownloadConfig.CV_LABEL_GRAPH_SO_NAME)
            && checkDefaultPathFileCanRW(BaseLabelGraphModelDownloadConfig.SQL_CIPHER_SO_NAME)
}