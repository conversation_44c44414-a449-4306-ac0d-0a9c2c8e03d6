/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryScanProgressCollector
 ** Description: 扫描进度收集器
 **
 ** Version: 1.0
 ** Date: 2025/05/02
 ** Author: 80348695
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80348695                          2025/05/02  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.manager

import android.content.Context
import android.content.Intent
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.api.IScanDM
import com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.Key.REASON_TYPE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate.Companion.MAX_PROGRESS
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.CollectorScanType.entries
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.getCloud24HMaxImageScanCount
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.getReasonTypeOnStartByOnTop
import com.oplus.gallery.standard_lib.app.AppConstants.Action.BACKGROUND_SCAN_LOADING_ACTION
import com.oplus.gallery.standard_lib.app.AppConstants.Action.BACKGROUND_SCAN_PENDING_ACTION
import com.oplus.gallery.standard_lib.app.AppConstants.Action.BACKGROUND_SCAN_SUCCESS_ACTION
import com.oplus.gallery.standard_lib.util.os.ContextGetter.context
import kotlin.math.roundToInt

/**
 * 扫描进度收集器
 */
class GalleryScanProgressCollector(
    targetCollectorScanTypes: Set<CollectorScanType>
) {
    /**
     * 存储各扫描类型的进度数据
     */
    private val progressMap = mutableMapOf<CollectorScanType, ScanProgress>()

    init {
        // 初始化目标扫描类型的进度
        targetCollectorScanTypes.forEach { type ->
            progressMap[type] = ScanProgress(weight = type.weight)
        }
    }

    private val scanDM: IScanDM by lazy {
        ApiDmManager.getScanDM()
    }

    /**
     * 更新某个扫描类型的进度
     *
     * @param type 扫描类型
     * @param total 总任务数
     * @param completed 已完成数
     */
    fun updateProgress(type: Int, total: Int, completed: Int) {
        val collectorScanType = getCollectorScanTypeById(type) ?: return
        val progress = progressMap[collectorScanType] ?: return
        progress.totalTasks = total
        progress.completedTasks = completed
        GLog.d(TAG, LogFlag.DL) { "updateProgress, totalTasks:$total, completedTasks:$completed, collectorScanType:$collectorScanType" }
        checkAndNotifyTotalProgress()
    }

    /**
     * 标记某个扫描类型为已完成
     */
    fun markAsCompleted(type: Int) {
        val collectorScanType = getCollectorScanTypeById(type) ?: return
        val progress = progressMap[collectorScanType] ?: return
        progress.isCompleted = true
        GLog.d(TAG, LogFlag.DL) { "markAsCompleted, collectorScanType:$collectorScanType" }
        checkAndNotifyCompletion()
    }

    /**
     * 扫描启动前的中断
     * @param context 上下文
     */
    fun onInterruptScan(type: Int?, reason: Int, context: Context?) {
        type?.let {
            val collectorScanType = getCollectorScanTypeById(type)
            GLog.d(TAG, LogFlag.DL) { "onInterruptScan, reason:$reason, collectorScanType:$collectorScanType" }
        }
        val maxScanCount = getCloud24HMaxImageScanCount()
        val reasonType = if (reason != GalleryScanMonitor.ReasonType.REASON_UNDEFINED) {
            reason
        } else {
            getReasonTypeOnStartByOnTop(context, 0, maxScanCount)
        }
        handleScanResult(reasonType)
    }


    /**
     * 处理扫描结果
     * @param reason 扫描结果 0：成功，其他：失败原因
     */
    private fun handleScanResult(reason: Int) {
        // 扫描停止时，将进度条恢复到初始值
        scanDM.updateScanProgress(INVALID_PROGRESS)
        when (reason) {
            GalleryScanMonitor.ReasonType.REASON_OK -> {
                GLog.d(TAG, LogFlag.DL) { "scan  success, reason is zero " }
                context.sendBroadcast(Intent(BACKGROUND_SCAN_SUCCESS_ACTION))
            }

            else -> {
                val intent = Intent(BACKGROUND_SCAN_PENDING_ACTION)
                intent.putExtra(REASON_TYPE, reason)
                context.sendBroadcast(intent)
            }
        }
    }

    /**
     * 根据 id 获取对应的 CollectorScanType 实例
     *
     * @param id 扫描类型的唯一标识符
     * @return 对应的 CollectorScanType 实例
     */
    private fun getCollectorScanTypeById(id: Int): CollectorScanType? {
        return entries.firstOrNull { it.id == id }
    }

    /**
     * 计算总进度并通知
     */
    private fun checkAndNotifyTotalProgress() {
        var weightedSum = 0f

        progressMap.forEach { (type, progress) ->
            val total = progress.totalTasks
            val completed = progress.completedTasks
            val ratio = when {
                // 任务已初始化且总任务数有效，计算实际比例
                total > 0 -> completed.toFloat() / total
                // 总任务为0但完成数非0，视为异常情况，比例设为0
                completed != 0 -> 0f
                // 任务未激活（总任务和完成数均为0），不影响进度
                else -> 0f
            }
            weightedSum += ratio * type.weight
        }

        val progress = (weightedSum * MAX_PROGRESS).roundToInt()
        GLog.d(TAG, LogFlag.DL) { "checkAndNotifyTotalProgress, progress:$progress" }
        val intent = Intent(BACKGROUND_SCAN_LOADING_ACTION)
        intent.putExtra(SCAN_PROGRESS, progress)
        scanDM.updateScanProgress(progress)
        context.sendBroadcast(intent)
    }

    /**
     * 检查所有扫描是否完成
     */
    private fun checkAndNotifyCompletion() {
        val allCompleted = progressMap.values.all { it.isCompleted }
        if (allCompleted) {
            GLog.d(TAG, LogFlag.DL) { "checkAndNotifyCompletion, allCompleted sendBroadcast" }
            scanDM.updateScanProgress(INVALID_PROGRESS)
            context.sendBroadcast(Intent(BACKGROUND_SCAN_SUCCESS_ACTION))
        }
    }

    companion object {
        private const val TAG = ScanConst.TAG + "GalleryScanProgressCollector"
        private const val SCAN_PROGRESS = "scan_progress"
        private const val INVALID_PROGRESS = -1
    }

    /**
     * 扫描监听器
     */
    interface ScanListener {
        fun onSuccess()
        fun onFailure(errorCode: Int)
        fun onProgress(progress: Int)
    }

    /**
     * 空对象默认实现
     */
    object NoOpScanListener : ScanListener {
        override fun onSuccess() = Unit
        override fun onFailure(errorCode: Int) = Unit
        override fun onProgress(progress: Int) = Unit
    }
}

/**
 * 定义所有支持的扫描类型 提供后续的扩展
 *
 * @property id 唯一标识符，可用于序列化或持久化
 * @property weight 扫描类型权重，用于计算总进度
 */
enum class CollectorScanType(
    val id: Int,
    val weight: Float
) {
    FACE(id = GalleryScan.FACE_SCAN, weight = 0.5f),
    HD_FACE(id = GalleryScan.HD_FACE_SCAN, weight = 0.5f)
}

/**
 * 扫描进度数据
 *
 * @property totalTasks 总任务数
 * @property completedTasks 已完成任务数
 * @property isCompleted 是否已完成
 * @property weight 扫描类型权重，用于计算总进度
 */
data class ScanProgress(
    var totalTasks: Int = 0,
    var completedTasks: Int = 0,
    var isCompleted: Boolean = false,
    var weight: Float = 1f
)