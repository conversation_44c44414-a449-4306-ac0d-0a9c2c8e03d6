/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FeatureResult.java
 ** Description:
 **     wrapper for polarr resultItem.
 **
 ** Version: 1.0
 ** Date:2017/12/07
 ** Author:<PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Rom.Apps.Gallery         2017/12/07   1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories;

import co.polarr.processing.entities.ResultItem;

public class FeatureResult {
    public static final String KEY_EMOTION = "metric_emotion";
    public static final String KEY_EXPOSURE = "metric_exposure";
    public static final String KEY_CLARITY = "metric_clarity";
    public static final String KEY_COLORFULNESS = "metric_colorfulness";
    public static final String KEY_RATING_ALL = "rating_all";

    public FeatureResult(ResultItem item) {
        mFilePath = item.filePath;
        if (item.features.get(KEY_EXPOSURE) != null) {
            mExposure = (Float) item.features.get(KEY_EXPOSURE);
        }
        if (item.features.get(KEY_EMOTION) != null) {
            mEmotion = (Float) item.features.get(KEY_EMOTION);
        }
        if (item.features.get(KEY_CLARITY) != null) {
            mClarity = (Float) item.features.get(KEY_CLARITY);
        }
        if (item.features.get(KEY_COLORFULNESS) != null) {
            mColorfulness = (Float) item.features.get(KEY_COLORFULNESS);
        }
        if (item.features.get(KEY_RATING_ALL) != null) {
            mRatingAll = (Float) item.features.get(KEY_RATING_ALL);
        }
    }

    public String toString() {
        return "file path=" + mFilePath + ", emotion=" + mEmotion
                + ", exposure=" + mExposure + ", clarity=" + mClarity
                + ", mColorfulness=" + mColorfulness + ", rating all=" + mRatingAll;
    }

    private String mFilePath;
    private float mEmotion;
    private float mExposure;
    private float mClarity;
    private float mColorfulness;
    private float mRatingAll;
    private boolean mInVideo;
    private int mTagId;

    public String getFilePath() {
        return mFilePath;
    }

    public void setFilePath(String filePath) {
        mFilePath = filePath;
    }

    public float getEmotion() {
        return mEmotion;
    }

    public void setEmotion(float emotion) {
        mEmotion = emotion;
    }

    public float getExposure() {
        return mExposure;
    }

    public void setExposure(float exposure) {
        mExposure = exposure;
    }

    public float getClarity() {
        return mClarity;
    }

    public void setClarity(float clarity) {
        mClarity = clarity;
    }

    public float getColorfulness() {
        return mColorfulness;
    }

    public void setColorfulness(float colorfulness) {
        mColorfulness = colorfulness;
    }

    public float getRatingAll() {
        return mRatingAll;
    }

    public void setRatingAll(float ratingAll) {
        mRatingAll = ratingAll;
    }

    public boolean isInVideo() {
        return mInVideo;
    }

    public void setInVideo(boolean inVideo) {
        mInVideo = inVideo;
    }

    public int getTagId() {
        return mTagId;
    }

    public void setTagId(int tagId) {
        mTagId = tagId;
    }

    /*int tag_removal;
    long created_at;

    int param_aec;
    int image_width;
    int image_height;
    List<Float> param_phash;
    float[] feature_bucketed_histogram;*/


}
