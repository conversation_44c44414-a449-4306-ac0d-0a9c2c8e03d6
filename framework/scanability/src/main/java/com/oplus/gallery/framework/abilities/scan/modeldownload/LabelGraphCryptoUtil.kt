/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelGraphCryptoUtil.kt
 ** Description: 视觉知识图谱数字信封请求工具，包括安盾的sdk初始化、解密等
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/12/14
 ** Author: <EMAIL>
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/12/14     1.0         init
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import com.allawn.cryptography.Crypto
import com.allawn.cryptography.entity.CryptoInitParameters
import com.allawn.cryptography.entity.EncryptAlgorithmEnum

import com.allawn.cryptography.entity.NegotiationAlgorithmEnum

import com.allawn.cryptography.entity.SceneConfig
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.permission.NetSendPrivacyPermissionRule
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.networkaccess.callback.AppResultCallback
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.download.IDownloadAbility
import com.oplus.gallery.framework.abilities.extraction.labelgraph.LabelGraphModelDownloadConfig
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.stdid.bean.StdIDInfo
import com.oplus.stdid.sdk.StdIDSDK

object LabelGraphCryptoUtil {
    @JvmStatic
    @Deprecated(message = "改为app启动时统一初始化, 见AppModule")
    private fun initCrypto(context: Context): Boolean {
        kotlin.runCatching {
            GLog.d(TAG, "[initCrypto] start init crypto sdk")
            val sceneConfig = SceneConfig().apply {
                /**
                 * 场景名称，业务方自己指定，保证不同场景之间名称不冲突。
                 * 这边由相册后台指定
                 */
                scene = SCENE
                negotiationAlgorithm = NegotiationAlgorithmEnum.RSA
                encryptAlgorithm = EncryptAlgorithmEnum.AES_GCM_NOPADDING_256
                /**
                 * 是否复用会话密钥。复用情况下，第二次创建同scene的session时，将复用上一次缓存的对称密钥（若未过期）
                 * 复用下，会话密钥存储在Crypto单例中
                 * 非复用下，会话密钥存储在Session对象中
                 */
                isNeedReuse = false
                /**
                 * 是否长期复用会话密钥。
                 * 该设置下，会话密钥存储在Crypto单例中，并本地存储
                 * app重启仍可复用此会话密钥（若未过期）
                 */
                isNeedLongTermReuse = false
                expireTime = EXPIRE_TIME
            }
            val sceneList = mutableListOf(sceneConfig)
            val bizMap = mutableMapOf(
                Pair(BIZ_ID, ApiDmManager.getAppDM().getSecurityUrl()?.videoEditorHostName)
            )
            StdIDSDK.init(context)
            val deviceId = StdIDSDK.getStdIds(context, StdIDInfo.Type_GUID).GUID
            val parameters = CryptoInitParameters.Builder(context)
                .setDeviceId(deviceId)  //用noise功能必须传入
                .setBizHostnameMap(bizMap)
                .setSceneConfigList(sceneList)
                .build()
            Crypto.init(parameters)
        }.onFailure {
            GLog.e(TAG, "[initCrypto] init crypto sdk onFailure: $it")
            return false
        }
        GLog.d(TAG, "[initCrypto] init crypto sdk success")
        return true
    }

    @JvmStatic
    fun getLabelGraphKey(context: Context, labelGraphVersion: Int, call: (serverKey: String) -> Unit) {
        GLog.d(TAG, "[getLabelGraphKey] start")
        // 2.创建session，并获取cipherInfo
        val session = Crypto.acquireSession(BIZ_ID).apply {
            encrypt(TextUtil.EMPTY_STRING, SCENE)
        }
        val cipherInfo = session.getHeader(SCENE)
        StdIDSDK.init(context)
        val deviceId = StdIDSDK.getStdIds(context, StdIDInfo.Type_GUID).GUID
        GLog.d(TAG, "[getLabelGraphKey] start getting LabelGraphKey from gallery3d server")
        context.getAppAbility<IDownloadAbility>()?.use { downloadAbility ->
            val isOnePlusExport = ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not()
                    && PROPERTY_IS_OP_BRAND
            downloadAbility.enqueue(
                context,
                LabelGraphRequest(context, NetSendPrivacyPermissionRule(), isOnePlusExport, cipherInfo, deviceId, labelGraphVersion),
                object : AppResultCallback<LabelGraphKeyResponseData?> {
                    override fun onSuccess(response: LabelGraphKeyResponseData?) {
                        response?.data?.let {
                            GLog.d(TAG, "[getLabelGraphKey] state of getLabelGraphKey: success")
                            // 3.解密服务器下发的Key
                            val serverKey = session.decrypt(it.graphKey, SCENE)
                            call(serverKey)
                        } ?: {
                            GLog.e(TAG, "[getLabelGraphKey] state of getLabelGraphKey: failed")
                            LabelGraphModelDownloadConfig.resetLabelGraphModel(context)
                        }
                        Crypto.releaseSession(session)
                    }

                    override fun onFailed(code: Int, msg: String?) {
                        GLog.e(TAG, "[getLabelGraphKey] onFailed: getLabelGraphKey failed. code: $code msg: $msg")
                        Crypto.releaseSession(session)
                        // 重置图谱资源版本，下次重新下载
                        LabelGraphModelDownloadConfig.resetLabelGraphModel(context)
                    }
                })
        }
    }

    private const val SCENE = "gcm"

    /**
     * 在剑门关后台申请了相关密钥/证书资料对应的业务编码bizId
     */
    private const val BIZ_ID = "video-clip"
    private const val TAG = "LabelGraphCryptoUtil"

    /**
     * 会话密钥过期时间（秒），这个是和相册后台约定的，不能随意更改
     */
    private const val EXPIRE_TIME = 24 * 60 * 60
}