/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FlawScanner
 ** Description:缺陷扫描
 ** Version: 1.0
 ** Date: 2024-06-11
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2024-06-11     1.0
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.flaw

import android.content.Context
import android.database.Cursor
import android.os.Bundle
import androidx.core.database.getStringOrNull
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.DatabaseUtils.projectionsToString
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_IMAGE_GIF
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.app.getAppAbility

class FlawScanner(private val context: Context) : GalleryScan(context) {
    override fun getScanType(): Int = FLAW_SCANNER

    override fun getSceneName(): String = TAG

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        super.onScan(triggerType, config)
        GLog.d(TAG, LogFlag.DF) { "onScan triggerType:$triggerType, config:$config" }
        markConditionsOnStartScan()
        clearCacheWhenVersionChange()
        val scanData = queryScanData()
        if (scanData.isEmpty()) return

        val flawDetectAbility = context.getAppAbility<IFlawDetectAbility>() ?: let {
            GLog.e(TAG, LogFlag.DL, "onScan, flawDetectAbility is null")
            return
        }
        // 缺陷扫描必须是充电中
        while (isAllowContinueScan(0, Int.MAX_VALUE, true)) {
            bindSmallCore()
            val batchList = scanData.subList(mScanImageCount, (mScanImageCount + BATCH_COUNT).coerceAtMost(scanData.size))
            flawDetectAbility.detect(batchList)
            mScanImageCount += batchList.size
            if (mScanImageCount >= scanData.size) break
        }
        flawDetectAbility.close()
        updateLastScanTime()
        GLog.d(TAG) { "onScan end, count:$mScanImageCount, cost:${System.currentTimeMillis() - mStartScanTime}" }
    }

    override fun runTaskIsNeedCharging(): Boolean {
        // 必须要在充电情况下执行
        return true
    }

    private fun clearCacheWhenVersionChange() {
        val oldVersion = GalleryScanUtils.getFlawScanVersion(mContext)
        if (DATA_VERSION != oldVersion) {
            GLog.d(TAG) { "clearCacheWhenVersionChange, currentVersion:$DATA_VERSION oldVersion:$oldVersion" }
            resetScanState()
            GalleryScanUtils.setFlawScanVersion(mContext, DATA_VERSION)
        }
    }

    private fun resetScanState() {
        val mark = (Constants.AIFunc.MASK shl Constants.AIFunc.SHIFT_BLUR) or
                (Constants.AIFunc.MASK shl Constants.AIFunc.SHIFT_REFLECTION) or
                (Constants.AIFunc.MASK shl Constants.AIFunc.SHIFT_PASSERBY) or
                (Constants.AIFunc.MASK shl Constants.AIFunc.SHIFT_IMAGE_QUALITY_ENHANCE) or
                (Constants.AIFunc.MASK shl Constants.AIFunc.SHIFT_AI_LIGHTING)
        // update local_media set ext_tag_flags=(ext_tag_flags & ~(ob111111<<32)) where (ext_tag_flags & (ob111111<<32)) > 0
        val sql = "update ${GalleryStore.GalleryMedia.TAB} " +
                "set ${LocalColumns.EXT_TAG_FLAGS}=(${LocalColumns.EXT_TAG_FLAGS} & ${mark.inv()}) " +
                "where (${LocalColumns.EXT_TAG_FLAGS} & $mark)>0"
        runCatching {
            val time = System.currentTimeMillis()
            context.contentResolver.call(
                GalleryStore.AUTHORITY,
                DatabaseUtils.METHOD_RAW_EXECUTE_SQL,
                null,
                Bundle().apply { putString(DatabaseUtils.KEY_SQL, sql) }
            )?.let {
                GLog.d(TAG) { "resetScanState success, cost:${System.currentTimeMillis() - time}" }
            } ?: GLog.e(TAG, "resetScanState fail")
        }.onFailure {
            GLog.e(TAG, "resetScanState, error: ", it)
        }
    }

    private fun queryScanData(): List<DetectItem> {
        val sql = "select ${projectionsToString(LocalMediaItem.PROJECTION, GalleryStore.GalleryMedia.TAB)}, " +
                "group_concat(${ScanLabelColumns.SCENE_ID}, '$COMMA') as $COLUMNS_LABEL_IDS " +
                "from ${GalleryStore.GalleryMedia.TAB} inner join ${GalleryStore.ScanLabel.TAB} " +
                "on ${GalleryStore.ScanLabel.TAB}.${ScanLabelColumns.DATA} = ${GalleryStore.GalleryMedia.TAB}.${LocalColumns.DATA} " +
                "where ${GalleryStore.GalleryMedia.TAB}.${LocalColumns.MEDIA_TYPE}=$MEDIA_TYPE_IMAGE " +
                "and ${DatabaseUtils.getDataValidWhere(GalleryStore.GalleryMedia.TAB)} " +
                "and ${DatabaseUtils.getFilterOutTrashedWhere(null)} " +
                "and ${DatabaseUtils.getOnlyLocalFileWhere()} " +
                "and ${LocalColumns.MIME_TYPE}!='$MIME_TYPE_IMAGE_GIF' " +
                "and ${LocalColumns.WIDTH}>=${Constants.AIFunc.MIN_IMAGE_SIZE} and ${LocalColumns.WIDTH}<=${Constants.AIFunc.MAX_IMAGE_SIZE} " +
                "and ${LocalColumns.HEIGHT}>=${Constants.AIFunc.MIN_IMAGE_SIZE} and ${LocalColumns.HEIGHT}<=${Constants.AIFunc.MAX_IMAGE_SIZE} " +
                "and " +
                " (${getFlawNotDetectedWhere(Constants.AIFunc.SHIFT_BLUR)}" +
                " or ${getFlawNotDetectedWhere(Constants.AIFunc.SHIFT_REFLECTION)}" +
                " or ${getFlawNotDetectedWhere(Constants.AIFunc.SHIFT_PASSERBY)}" +
                " or ${getFlawNotDetectedWhere(Constants.AIFunc.SHIFT_IMAGE_QUALITY_ENHANCE)}) " +
                " or ${getFlawNotDetectedWhere(Constants.AIFunc.SHIFT_AI_LIGHTING)}) " +
                "group by ${LocalColumns.MEDIA_ID} " +
                "order by ${LocalColumns.DATE_TAKEN} DESC"
        return RawQueryReq.Builder<List<DetectItem>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(
                object : IConvert<Cursor, List<DetectItem>> {
                    override fun convert(cursor: Cursor?): List<DetectItem> {
                        cursor ?: return emptyList()
                        if (cursor.count <= 0) return emptyList()
                        val result = mutableListOf<DetectItem>()
                        val labelIdsIndex = cursor.getColumnIndex(COLUMNS_LABEL_IDS)
                        while (cursor.moveToNext()) {
                            val id = cursor.getInt(LocalMediaItem.INDEX_ID)
                            val childPath = when (cursor.getInt(LocalMediaItem.INDEX_MEDIA_TYPE)) {
                                MEDIA_TYPE_IMAGE -> LocalImage.ITEM_PATH.getChild(id)
                                MEDIA_TYPE_VIDEO -> LocalVideo.ITEM_PATH.getChild(id)
                                else -> continue
                            }
                            val labelIds = cursor.getStringOrNull(labelIdsIndex)
                                ?.split(COMMA)
                                ?.mapNotNull { it.toIntOrNull() }
                                ?: emptyList()
                            result.add(DetectItem(LocalMediaItem.loadOrUpdateItem(childPath, cursor), labelIds))
                        }
                        return result
                    }
                }
            )
            .setQuerySql(sql)
            .build()
            .exec()
    }

    private fun getFlawNotDetectedWhere(shiftFlag: Int): String {
        return "((${LocalColumns.EXT_TAG_FLAGS}>>$shiftFlag) & ${Constants.AIFunc.MASK} == ${Constants.AIFunc.STATUS_NOT_DETECTED})"
    }

    companion object {
        private const val TAG = "FlawScanner"
        private const val COLUMNS_LABEL_IDS = "labelIds"
        private const val BATCH_COUNT = 10

        /**
         * 数据版本，如果更新了算法SDK需要重新扫描，这个数字+1即可
         */
        private const val DATA_VERSION = 1
    }
}