/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LabelGraphRequest.kt
 ** Description : 请求知识图谱配置的网络请求
 ** Version     : 1.0
 ** Date        : 2024/3/11
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D          2024/3/11     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.networkaccess.INetSendRule
import com.oplus.gallery.foundation.networkaccess.base.BaseRequestParam
import com.oplus.gallery.foundation.networkaccess.convert.OkhttpResponseConvert
import com.oplus.gallery.foundation.networkaccess.other.RtnCode
import com.oplus.gallery.foundation.networkaccess.param.JsonRequestParam
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.download.networkrequest.CommonCallbackConvert
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusNetServiceManager
import com.oplus.gallery.framework.abilities.download.networkrequest.PostRequest
import com.oplus.gallery.framework.abilities.download.networkrequest.header.HttpHeader
import okhttp3.Response

/**
 * 请求知识图谱配置的网络请求
 */
class LabelGraphRequest(
    context: Context,
    allowRequestRule: INetSendRule,
    private val isOnePlusExport: Boolean,
    private val cipherInfo: String,
    private val deviceId: String,
    private val labelGraphVersion: Int
) : PostRequest<Any, LabelGraphKeyResponseData?>(context, allowRequestRule) {

    override val logTag: String = TAG

    override val url: String? = OplusNetServiceManager.getInstance().buildUrl(PATH)

    override val header: MutableMap<String, String?>
        get() = HttpHeader.getOplusHeaders(isOnePlusExport).apply {
            put(CIPHER_INFO, cipherInfo)
            put(DEVICE_ID, deviceId)
            put(LABEL_GRAPH_VERSION, labelGraphVersion.toString())
        }

    override val requestParam: BaseRequestParam<Any> = JsonRequestParam(HashMap<Any, Any>())

    override val responseConvert = LabelGraphKeyResponseConvert()

    override val callbackConvert = CommonCallbackConvert<LabelGraphKeyResponseData?>()

    companion object {
        private const val TAG = "LabelGraphRequest"
        private const val PATH = "/v1/crypto/config"
        private const val CIPHER_INFO = "cipherInfo"
        private const val DEVICE_ID = "deviceId"
        private const val LABEL_GRAPH_VERSION = "label_graph_version"
    }
}

/**
 * 知识图谱的网络结果解析
 */
class LabelGraphKeyResponseConvert : OkhttpResponseConvert<LabelGraphKeyResponseData?>() {
    override fun convert(response: Response?): LabelGraphKeyResponseData? {
        val lastTime = System.currentTimeMillis()
        var responseData: LabelGraphKeyResponseData? = null
        if (response?.body != null) {
            responseData = JsonUtil.fromJson(
                response.body?.string(),
                LabelGraphKeyResponseData::class.java
            )
        }
        GLog.d(TAG, "parse time :" + GLog.getTime(lastTime))
        return responseData
    }

    companion object {
        private const val TAG = "LabelGraphKeyResponseConvert"
    }
}

/**
 * 知识图谱的网络结果对象
 */
data class LabelGraphKeyResponseData(
    @SerializedName("code") val code: Int = RtnCode.CODE_FAILED,
    @SerializedName("message") val message: String = TextUtil.EMPTY_STRING,
    @SerializedName("data") val data: LabelGraphData? = null
)

/**
 * 知识图谱key
 */
data class LabelGraphData(
    @SerializedName("graphKey") val graphKey: String = TextUtil.EMPTY_STRING
)