/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Memories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/13
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D    2018/3/13     1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.memories.bean.FeatureResultEntity;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.business_lib.model.data.memories.utils.OptimalConfigInterface;
import com.oplus.gallery.business_lib.model.data.memories.utils.OptimalScannerConfig;
import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.framework.abilities.scan.R;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants;
import com.oplus.gallery.standard_lib.codec.DecodeUtils;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.standard_lib.ui.notifacation.NotificationHelper;
import com.oplus.gallery.foundation.util.debug.GLog;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

public abstract class Memories implements OptimalConfigInterface, Comparable<Memories> {
    protected static final String STOP_REASON_OVER_SIZE = "_OverSize";
    protected static final String STOP_REASON_DATE_RANGE_NULL = "_DateRangeNull";
    protected static final String STOP_REASON_NOT_BEFORE_TODAY = "_NotBeforeToday";
    protected static final String STOP_REASON_CREATED_IN_DATE_RANGE = "_CreatedInDateRange";
    protected static final String STOP_REASON_SCAN_SIZE_NOT_ENOUGH = "_ScanSizeNotEnough";

    private static final String SCENE_SCANNER_NOT_READY = "MemoriesScanner_NotReady_";
    private static final String SCENE_SCANNER_NOT_PREPARE = "MemoriesScanner_NotPrepare_";
    private static final String SCENE_SCANNER_NOT_SCAN = "MemoriesScanner_NotScan_";
    private static final String SCENE_SCANNER_NOT_CREATE = "MemoriesScanner_NotCreate_";

    private static final String STOP_REASON_CREATE_EMPTY = "_CreateEmpty";
    private static final String STOP_REASON_CREATE_FAILED = "_CreateFailed";
    private static final String STOP_REASON_PROCESS_LIST_EMPTY = "_ProcessListEmpty";
    private static final String STOP_REASON_PROCESS_IMAGE_LIST_EMPTY = "_ProcessImageListEmpty";
    private static final String STOP_REASON_PROCESS_FEATURE_IS_NULL = "_ProcessFeatureIsNull";

    private static final String TAG = "Memories";

    protected String mStopReason;
    protected String mStopSubReason;
    protected final Context mContext;
    protected int mMemoriesPicMin = 30;
    protected int mInVideoPicMin = 15;
    protected int mInVideoPicMax = 40;
    protected List<MediaItem> mMediaItemList;
    private int mMemoriesGroupType = -1;
    private int mHighPriority = -1;
    private int mNormalPriority = -1;
    private boolean mInterrupt = false;
    private String mMemoriesName;
    private List<Integer> mLabelFilterIdList = new ArrayList<>();

    public Memories(Context context) {
        mContext = context;
        mMemoriesPicMin = OptimalScannerConfig.getIntConfig(MEMORIES_PIC_COUNT_MIN);
        List<String> labelFilterList = OptimalScannerConfig.getInstance().getLabelFilterList();
        for (String label : labelFilterList) {
            int id = LabelDictionary.getLabelId(label);
            if (id != -1) {
                mLabelFilterIdList.add(id);
            }
        }
    }

    public void interrupt() {
        GLog.d(TAG, "interrupt");
        mInterrupt = true;
    }

    public boolean isReadyContinue() {
        return !mInterrupt && GalleryScanMonitor.isAllowContinueScan(mContext, false, false);
    }

    protected boolean processMemoriesItems(int setId, List<MediaItem> list, long startTime, long endTime) {
        if ((list == null) || list.isEmpty()) {
            // 扫描中断，记录原因
            mStopReason = getTag() + STOP_REASON_PROCESS_LIST_EMPTY;
            return false;
        }
        List<MediaItem> imageList = getNewListIfTooMany(list);
        if (imageList.isEmpty()) {
            // 扫描中断，记录原因
            mStopReason = getTag() + STOP_REASON_PROCESS_IMAGE_LIST_EMPTY;
            return false;
        }
        GroupingResult result = null;
        List<String> pathList = new ArrayList<>();
        Map<String, Integer> rotationMap = new HashMap<>();
        for (MediaItem item : imageList) {
            String filePath = item.getFilePath();
            pathList.add(filePath);
            rotationMap.put(filePath, item.getRotation());
        }
        // FIXME: 2020/8/22 chenzengxin 看GroupResult能不能放到scan里
        result = PolarrRating.getInstance().ratingGroup(mContext, imageList, mInVideoPicMin, mInVideoPicMax);
        if ((result == null) || !result.available()) {
            int count = MemoriesProviderHelper.deleteMemories(setId);
            // deleteMemories 通知
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.MEMORIES_SET, IDao.DaoType.GALLERY);
            GLog.w(TAG, "processMemoriesItems features is null. set id:" + setId + ", delete count:" + count);
            // 扫描中断，记录原因
            mStopReason = getTag() + STOP_REASON_PROCESS_FEATURE_IS_NULL;
            return false;
        }
        Map<String, FeatureResultEntity> featureMap = new HashMap<>();
        result.getItems(featureMap);
        String coverPath = result.getBestItemPath();
        int count = MemoriesProviderHelper.insertMeta(mContext, setId, pathList, featureMap, coverPath);
        MemoriesProviderHelper.setMemoriesCoverPath(setId, coverPath);
        //update memories cover to avoid duplication
        int type = getMemoriesId();
        MemoriesProviderHelper.updateMemoriesCover(setId, type, true, null);
        int videoCount = MemoriesHelper.getMemoriesVideoCount(setId);
        ScanTrackHelper.trackMemoriesScanResult(count, type, videoCount);
        GLog.v(TAG, "processMemoriesItems end. set id:" + setId + ", insert count:" + count + ",videoCount:" + videoCount);
        if (needShowNotification()) {
            Bitmap cover = DecodeUtils.decode(coverPath);
            cover = BitmapUtils.rotateBitmap(cover, rotationMap.get(coverPath), true);
            String subTitle = MemoriesScannerHelper.getMemoriesSubTitle(mContext, startTime, endTime, type);
            showNotification(getMemoriesName(), subTitle, cover, setId, type, count);
            MemoriesHelper.setMemoriesNoticeState(setId, true);
        }
        return true;
    }

    protected boolean createMemories(List<MediaItem> itemList) {
        if ((itemList == null) || itemList.isEmpty()) {
            // 扫描中断，记录原因
            mStopReason = getTag() + STOP_REASON_CREATE_EMPTY;
            return false;
        }
        GLog.v(TAG, "createMemories size:" + itemList.size() + ",type:" + getMemoriesId());
        MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        int setId = MemoriesProviderHelper.createDateMemories(getMemoriesName(), getMemoriesId(), dateRange.mStart, dateRange.mEnd,
            getNameType());
        if (setId == -1) {
            GLog.w(TAG, "createMemories create memories failed!");
            // 扫描中断，记录原因
            mStopReason = getTag() + STOP_REASON_CREATE_FAILED;
            return false;
        }
        return processMemoriesItems(setId, itemList, dateRange.mStart, dateRange.mEnd);
    }

    public static List<MediaItem> getNewListIfTooMany(List<MediaItem> imageList) {
        int maxCount = getIntOptimalConfig(MEMORIES_PIC_COUNT_MAX);
        int imageListSize = imageList.size();
        if (imageListSize <= maxCount) {
            return imageList;
        }
        List<MediaItem> newList = new ArrayList<>();
        Set<Integer> set = new HashSet<>();
        Random random = new Random();
        int count = 0;
        while (count < maxCount) {
            int index = random.nextInt(imageListSize);
            if (!set.contains(index)) {
                set.add(index);
                newList.add(imageList.get(index));
                count++;
            }
        }
        GLog.d(TAG, "getNewListIfTooMany, new list size=" + newList.size() + ", origin list size=" + imageListSize);
        return newList;
    }

    public static int getIntOptimalConfig(String key) {
        return OptimalScannerConfig.getIntConfig(key);
    }

    public List<Integer> getLabelFilterIdList() {
        return mLabelFilterIdList;
    }

    public void setMemoriesGroupType(int groupId) {
        mMemoriesGroupType = groupId;
    }

    public int getMemoriesGroupType() {
        return mMemoriesGroupType;
    }

    public void setHighPriority(int priority) {
        mHighPriority = priority;
    }

    public int getHighPriority() {
        return mHighPriority;
    }

    public void setNormalPriority(int priority) {
        mNormalPriority = priority;
    }

    public int getNormalPriority() {
        return mNormalPriority;
    }

    public void setMemoriesName(String name) {
        mMemoriesName = name;
    }

    public String getMemoriesName() {
        return mMemoriesName;
    }

    public boolean isHighPriorityMemories() {
        return mHighPriority != -1;
    }

    public boolean doScan(int triggerType) {
        GLog.d(TAG, "doScan, memories is: " + this);
        boolean result = isReadyContinue();
        String stopScene = null;
        if (result) {
            //1:prepare
            result = prepareMemories();
            if (!result) {
                stopScene = getStopScene(SCENE_SCANNER_NOT_PREPARE);
            }
            GLog.d(TAG, "doScan, prepareMemories result:" + result);
        } else {
            stopScene = getStopScene(SCENE_SCANNER_NOT_READY);
        }

        if (result && isReadyContinue()) {
            //2:scan
            result = scanMemories();
            if (!result) {
                stopScene = getStopScene(SCENE_SCANNER_NOT_SCAN);
            }
            GLog.d(TAG, "doScan, scanMemories result:" + result);
        } else if (stopScene != null) {
            stopScene = getStopScene(SCENE_SCANNER_NOT_READY);
        }

        if (result && isReadyContinue()) {
            //3:create
            result = createMemories();
            if (!result) {
                stopScene = getStopScene(SCENE_SCANNER_NOT_CREATE);
            }
            GLog.d(TAG, "doScan, createMemories result:" + result);
        } else if (stopScene != null) {
            stopScene = getStopScene(SCENE_SCANNER_NOT_READY);
        }

        if (!result) {
            ScanTrackHelper.trackStopReasonOnStart(mContext, stopScene, triggerType);
        }
        return result;
    }

    @Override
    public int compareTo(Memories out) {
        // order by HighPriority,GroupType,NormalPriority like 1,2,3,4 ASC.
        if (getHighPriority() >= 0) {
            if (out.getHighPriority() >= 0) {
                return getHighPriority() - out.getHighPriority();
            } else {
                return 1;
            }
        } else {
            if (out.getHighPriority() >= 0) {
                return 1;
            } else {
                if (getMemoriesGroupType() > out.getMemoriesGroupType()) {
                    return 1;
                } else if (getMemoriesGroupType() == out.getMemoriesGroupType()) {
                    return getNormalPriority() - out.getNormalPriority();
                } else {
                    return -1;
                }
            }
        }
    }

    /**
     * particular memories should send notification
     */
    protected boolean needShowNotification() {
        return false;
    }

    private void showNotification(String memoriesName, String subTitle, Bitmap cover, int setId, int type, int count) {
        // 由于去品牌化等要求导致action可能存在多个，内部应用发送action只发最后的（最新的）
        String[] viewMemoriesAlbumActions = AppBrandConstants.Action.getACTION_VIEW_MEMORIES_ALBUM();
        int viewMemoriesAlbumActionsLen = viewMemoriesAlbumActions.length;
        if (viewMemoriesAlbumActionsLen <= 0) {
            GLog.d(TAG, "showNotification viewMemoriesAlbumActionsLen=" + viewMemoriesAlbumActionsLen);
            return;
        }
        NotificationHelper.postNotification(
            mContext,
            NotificationHelper.DEFAULT_CHANNEL_ID,
            NotificationHelper.NOTIFICATION_ID_MEMORIES,
            String.valueOf(setId),
            (builder -> {
                int titleResId = R.string.scan_notification_memories_title;
                String title = mContext.getString(titleResId);
                builder.setContentTitle(title)
                    .setContentText(memoriesName)
                    .setTicker(title)
                    .setLargeIcon(cover)
                    .setWhen(System.currentTimeMillis())
                    .setAutoCancel(true)
                    .setShowWhen(true);
                String action = viewMemoriesAlbumActions[viewMemoriesAlbumActionsLen - 1];
                Intent contentIntent = new Intent(action);
                contentIntent.setPackage(mContext.getPackageName());
                contentIntent.putExtra(MemoriesUtils.KEY_MEMORIES_SET_ID, setId);
                contentIntent.putExtra(MemoriesUtils.KEY_MEMORIES_TITLE, memoriesName);
                contentIntent.putExtra(MemoriesUtils.KEY_MEMORIES_TYPE, type);
                contentIntent.putExtra(MemoriesUtils.KEY_MEMORIES_COUNT, count);
                contentIntent.putExtra(MemoriesUtils.KEY_MEMORIES_SUB_TITLE, subTitle);
                contentIntent.putExtra(MemoriesUtils.KEY_MEMORIES_FROM_NOTICE, true);
                /*
                 * Android S 开始
                 * 如果您的应用试图在不设置任何可变标志的情况下创建 PendingIntent 对象，系统会抛出 IllegalArgumentException
                 */
                builder.setContentIntent(PendingIntent.getActivity(mContext, setId, contentIntent, PendingIntent.FLAG_IMMUTABLE));
                return null;
            }));
    }

    private String getStopScene(String scene) {
        return scene + mStopReason;
    }

    /**
     * 获取子类型的TAG值，可用于埋点。
     * @return 返回子类型的TAG
     */
    protected abstract String getTag();

    public abstract boolean prepareMemories();

    public abstract boolean scanMemories();

    public abstract boolean createMemories();

    public abstract int getMemoriesId();

    public abstract int getNameType();
}
