/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlarmReceiver.java
 ** Description:
 ** Version: 1.0
 ** Date: 2021/1/18
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** xiuhua.ke@Apps.Gallery3D   2016/11/29     1.0              build
 ** Dajian@Apps.Gallery3D      2021/1/18      1.1              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.scan.component;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.PowerManager;

import com.oplus.gallery.basebiz.helper.RemoteModelInfoManager;
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil;
import com.oplus.gallery.framework.abilities.download.RemoteModelInfo;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanSyncTaskManager;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.Calendar;
import java.util.HashMap;

import static com.oplus.gallery.standard_lib.app.AppConstants.Action.ACTION_SCAN_ALARM;

import androidx.annotation.NonNull;

public class AlarmReceiver extends BroadcastReceiver {
    public static final int SCAN_USER_ACTION_ALARM_SCAN_TRIGGER_VALUE = 0;
    private static final String TAG = "AlarmReceiver";
    /**
     * 02:00:00--05:00:00 to alarm
     */
    private static final int START_HOUR_MIN = 2;
    private static final int START_HOUR_MAX = 5;
    private static final long TIME_DELAY_TO_ALARM = AlarmManager.INTERVAL_HALF_HOUR;
    private static final long TIME_DELAY_TO_ALARM_NEXT_DAY = AlarmManager.INTERVAL_DAY;
    private static final long TIME_DELAY_TO_ALARM_SCAN_FINISH = AlarmManager.INTERVAL_DAY * 2;

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        GLog.d(TAG, "onReceive, action:" + action);
        if ((action != null) && action.equalsIgnoreCase(ACTION_SCAN_ALARM)) {
            PowerManager pw = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            Calendar calendar = Calendar.getInstance();
            long currentTime = System.currentTimeMillis();
            long startTimeMax = getSpecialTime(calendar, START_HOUR_MAX);

            if (GalleryScanUtils.isNormalScanPolicy()) {
                if ((pw != null) && (pw.isInteractive())) {
                    long nextTime = currentTime + TIME_DELAY_TO_ALARM;
                    if (nextTime < startTimeMax) {
                        GLog.d(TAG, "onReceive, screen on, delay half hour to alarm!");
                    } else {
                        /*
                        当天触发后没有执行成功，下一次触发周期为一天；
                        重新随机触发时间点2-5之间，因为当前的时间距离5点太近了，容易失败。
                        */
                        GLog.d(TAG, "onReceive, screen on, half hour later will exceed max start time, alarm abandon!");
                        nextTime = getRandomTime() + TIME_DELAY_TO_ALARM_NEXT_DAY;
                    }
                    sendIntent(context, nextTime);
                    return;
                } else {
                    GLog.d(TAG, "onReceive, screen off, start scan!");
                }
            } else {
                GLog.d(TAG, "onReceive, test mode, start scan!");
            }
            // 成功触发，下一次触发时间间隔定为2天，否则时间间隔依然为1天。
            long timeDelay = TIME_DELAY_TO_ALARM_NEXT_DAY;
            if (BatteryStatusUtil.isBatteryInCharging(false)
                    || GalleryScanMonitor.allowScanWithAlarmTrigger(context)) {
                //初始化下载相关的组件
                RemoteModelInfoManager.fetch(context, new RemoteModelInfoManager.RequestListener() {
                    @Override
                    public void onSuccess(@NonNull HashMap<String, RemoteModelInfo> modelInfoMap) {
                        GalleryScanSyncTaskManager.INSTANCE.doAllDataScan(
                                SCAN_USER_ACTION_ALARM_SCAN_TRIGGER_VALUE
                        );
                    }

                    @Override
                    public void onFailure(int code) {
                        GalleryScanSyncTaskManager.INSTANCE.doAllDataScan(
                                SCAN_USER_ACTION_ALARM_SCAN_TRIGGER_VALUE
                        );
                    }
                }, false, AlarmReceiver.TAG);
                long intervalTime = GalleryScanMonitor.getCloudPhotoScanIntervalTime();
                if (intervalTime == 0) {
                    intervalTime = TIME_DELAY_TO_ALARM_SCAN_FINISH;
                }
                GLog.v(TAG, "sendIntent delay time " + intervalTime);
                timeDelay = intervalTime;
            }
            // Alarm触发有可能延迟，超过最大触发时间，则重新随机触发时间。
            if (currentTime > startTimeMax) {
                sendIntent(context, currentTime + timeDelay);
            } else {
                sendIntent(context, getRandomTime() + timeDelay);
            }
        }
    }

    /**
     * Limit OcrScanner between 2 and 5 o 'clock per day to avoid heavy CPU usage
     */
    public static boolean isInOcrTimeRange() {
        long currentTime = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();
        long startTimeMax = getSpecialTime(calendar, START_HOUR_MAX);
        long startTimeMin = getSpecialTime(calendar, START_HOUR_MIN);
        return (currentTime >= startTimeMin) && (currentTime <= startTimeMax);
    }

    private static void sendIntent(Context context, long time) {
        GLog.d(TAG, "sendIntent: " + TimeUtils.getFormatDateTime(time));
        Intent intent = new Intent(context, AlarmReceiver.class);
        intent.setAction(ACTION_SCAN_ALARM);
        /*
         * Android S 开始
         * 如果您的应用试图在不设置任何可变标志的情况下创建 PendingIntent 对象，系统会抛出 IllegalArgumentException
         */
        PendingIntent sender = PendingIntent.getBroadcast(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (am != null) {
            int version = Build.VERSION.SDK_INT;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                /*
                *  Android U上，精确闹钟权限默认拒绝，调用接口前检查权限AlarmManager.canScheduleExactAlarms()
                *  需要做权限检查，否则直接调用setExactAndAllowWhileIdle会导致抛出异常。
                */
                if (am.canScheduleExactAlarms()) {
                    am.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, time, sender);
                } else {
                    // 如果用户未授权，则屏蔽应用精确闹钟的功能。
                    am.setWindow(AlarmManager.RTC_WAKEUP, time, TIME_DELAY_TO_ALARM_NEXT_DAY, sender);
                }
            } else if (version >= Build.VERSION_CODES.M) {
                am.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, time, sender);
            } else {
                am.setExact(AlarmManager.RTC_WAKEUP, time, sender);
            }
        }
    }

    private static long getSpecialTime(Calendar calendar, int hour) {
        long now = System.currentTimeMillis();
        calendar.setTimeInMillis(now);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    private static long getRandomTime() {
        Calendar calendar = Calendar.getInstance();
        long startTimeMin = getSpecialTime(calendar, START_HOUR_MIN);
        long startTimeMax = getSpecialTime(calendar, START_HOUR_MAX);
        return startTimeMin + (long) (Math.random() * (startTimeMax - startTimeMin));
    }

    /**
     * set Alarm
     *
     * @param context
     * @return void
     */
    public static void setAlarmReceiver(Context context) {
        if (!GalleryScanMonitor.allowScanWithAlarmTrigger(context)) {
            return;
        }
        long now = System.currentTimeMillis();
        long startTimeRand = getRandomTime();
        if (now > startTimeRand) {
            startTimeRand = startTimeRand + TIME_DELAY_TO_ALARM_NEXT_DAY;
        }
        sendIntent(context, startTimeRand);
    }

    /**
     * cancel Alarm
     *
     * @param context
     * @return void
     */
    public static void cancelAlarmReceiver(Context context) {
        GLog.d(TAG, "cancelAlarmReceiver");
        Intent intent = new Intent(context, AlarmReceiver.class);
        intent.setAction(ACTION_SCAN_ALARM);
        /*
         * update_current can cancel old one;
         * and cancel_current can`t cancel when Intent.setAction()
         * Android S 开始
         * 如果您的应用试图在不设置任何可变标志的情况下创建 PendingIntent 对象，系统会抛出 IllegalArgumentException
         */
        PendingIntent sender = PendingIntent.getBroadcast(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
        AlarmManager am = (AlarmManager) context
                .getSystemService(Context.ALARM_SERVICE);
        am.cancel(sender);
    }
}
