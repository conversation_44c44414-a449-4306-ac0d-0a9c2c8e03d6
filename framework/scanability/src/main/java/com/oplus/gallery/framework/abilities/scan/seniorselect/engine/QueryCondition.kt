/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : QueryCondition.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/11/08
 ** Author      : wenshuguang
 ** TAG         : OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>        <date>         <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** wenshuguang     2021/11/08      1.0            OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 *********************************************************************************/


package com.oplus.gallery.framework.abilities.scan.seniorselect.engine

import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DATE_ADDED
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.SIZE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.RELATIVE_PATH
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.WIDTH
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.HEIGHT
import com.oplus.gallery.foundation.database.util.ConstantUtils.AND
import com.oplus.gallery.foundation.database.util.ConstantUtils.VARIABLE_PLACEHOLDER
import com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_TO
import com.oplus.gallery.foundation.database.util.ConstantUtils.ORDER_ASC
import com.oplus.gallery.foundation.database.util.ConstantUtils.ORDER_DESC
import com.oplus.gallery.foundation.database.util.ConstantUtils.COMMA
import com.oplus.gallery.foundation.database.util.DatabaseUtils

data class QueryCondition(
    val whereClause: String,
    val whereArgs: MutableList<String>?,
    val orderBy: String?
) {

    class Builder {

        companion object {
            private const val MIN_TYPE_FILTER = 1

            private const val TYPE_FILTER_MIN_SIDE_SIZE = MIN_TYPE_FILTER
            private const val TYPE_FILTER_MAX_SIDE_SIZE = TYPE_FILTER_MIN_SIDE_SIZE shl 1
            private const val TYPE_FILTER_MIME_TYPE = TYPE_FILTER_MAX_SIDE_SIZE shl 1
            private const val TYPE_FILTER_MIN_FILE_SIZE = TYPE_FILTER_MIME_TYPE shl 1
            private const val TYPE_FILTER_MAX_FILE_SIZE = TYPE_FILTER_MIN_FILE_SIZE shl 1
            private const val TYPE_FILTER_BUCKET_ID = TYPE_FILTER_MAX_FILE_SIZE shl 1
            private const val TYPE_FILTER_MIN_TAKEN_DATE = TYPE_FILTER_BUCKET_ID shl 1
            private const val TYPE_FILTER_MAX_TAKEN_DATE = TYPE_FILTER_MIN_TAKEN_DATE shl 1
            private const val TYPE_FILTER_MIN_ADDED_DATE = TYPE_FILTER_MAX_TAKEN_DATE shl 1
            private const val TYPE_FILTER_MAX_ADDED_DATE = TYPE_FILTER_MIN_ADDED_DATE shl 1
            private const val TYPE_FILTER_MIN_MODIFIED_DATE = TYPE_FILTER_MAX_ADDED_DATE shl 1
            private const val TYPE_FILTER_MAX_MODIFIED_DATE = TYPE_FILTER_MIN_MODIFIED_DATE shl 1
            private const val TYPE_FILTER_MEDIA_TYPE = TYPE_FILTER_MAX_MODIFIED_DATE shl 1

            private const val MAX_TYPE_FILTER = TYPE_FILTER_MEDIA_TYPE
        }

        private var type: Int = 0
        private var minSize: Int = 0
        private var maxSize: Int = 0
        private var mimeTypes: List<String>? = null
        private var inverseMimeType = false
        private var minFileSize: Long = 0
        private var maxFileSize: Long = 0
        private var relativePaths: List<String>? = null
        private var minTakenDate: Long = 0
        private var maxTakenDate: Long = 0
        private var minAddedDate: Long = 0
        private var maxAddedDate: Long = 0
        private var minModifiedDate: Long = 0
        private var maxModifiedDate: Long = 0
        private var mediaType: Int = 0
        private var orderBy: String? = null

        private val attrFilterMap = mapOf(
            TYPE_FILTER_MIN_SIDE_SIZE to this::appendMinSideSize,
            TYPE_FILTER_MAX_SIDE_SIZE to this::appendMaxSideSize,
            TYPE_FILTER_MIME_TYPE to this::appendMimeTypes,
            TYPE_FILTER_MIN_FILE_SIZE to this::appendMinFileSize,
            TYPE_FILTER_MAX_FILE_SIZE to this::appendMaxFileSize,
            TYPE_FILTER_BUCKET_ID to this::appendBucketIds,
            TYPE_FILTER_MIN_TAKEN_DATE to this::appendMinTakenDate,
            TYPE_FILTER_MAX_TAKEN_DATE to this::appendMaxTakenDate,
            TYPE_FILTER_MIN_ADDED_DATE to this::appendMinAddedDate,
            TYPE_FILTER_MAX_ADDED_DATE to this::appendMaxAddedDate,
            TYPE_FILTER_MIN_MODIFIED_DATE to this::appendMinModifiedDate,
            TYPE_FILTER_MAX_MODIFIED_DATE to this::appendMaxModifiedDate,
            TYPE_FILTER_MEDIA_TYPE to this::appendMediaType
        )

        fun build(): QueryCondition {
            val whereArgs = mutableListOf<Any>()
            val stringBuilder = StringBuilder("0 = 0")
            var i = MIN_TYPE_FILTER
            while (i <= MAX_TYPE_FILTER) {
                if ((type and i) > 0) {
                    attrFilterMap[i]?.let { it(stringBuilder, whereArgs) }
                }
                i = i shl 1
            }
            return QueryCondition(
                stringBuilder.toString(),
                DatabaseUtils.getWhereArgs(whereArgs)?.toMutableList(),
                orderBy
            )
        }

        private fun appendMediaType(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(MEDIA_TYPE)
            stringBuilder.append(EQUAL_TO)
            whereArgs.add(mediaType)
        }

        private fun appendMaxTakenDate(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(DATE_TAKEN)
            stringBuilder.append(" <= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(maxTakenDate)
        }

        private fun appendMinTakenDate(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(DATE_TAKEN)
            stringBuilder.append(" >= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(minTakenDate)
        }

        private fun appendMaxModifiedDate(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(DATE_MODIFIED)
            stringBuilder.append(" <= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(maxModifiedDate)
        }

        private fun appendMinModifiedDate(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(DATE_ADDED)
            stringBuilder.append(" >= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(minModifiedDate)
        }

        private fun appendMaxAddedDate(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(DATE_ADDED)
            stringBuilder.append(" <= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(maxAddedDate)
        }

        private fun appendMinAddedDate(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(DATE_ADDED)
            stringBuilder.append(" >= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(minAddedDate)
        }

        private fun appendBucketIds(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            relativePaths?.let {
                stringBuilder.append(AND)
                stringBuilder.append(DatabaseUtils.getWhereQueryIn(RELATIVE_PATH, it.size))
                whereArgs.addAll(it)
            }
        }

        private fun appendMaxFileSize(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(SIZE)
            stringBuilder.append(" <= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(maxFileSize)
        }

        private fun appendMinFileSize(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(SIZE)
            stringBuilder.append(" >= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(minFileSize)
        }

        private fun appendMimeTypes(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            mimeTypes?.let {
                stringBuilder.append(AND)
                stringBuilder.append(
                    if (inverseMimeType) {
                        DatabaseUtils.getWhereQueryNotIn(MIME_TYPE, it.size)
                    } else {
                        DatabaseUtils.getWhereQueryIn(MIME_TYPE, it.size)
                    }
                )
                whereArgs.addAll(it)
            }
        }

        private fun appendMaxSideSize(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(WIDTH)
            stringBuilder.append(" <= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            stringBuilder.append(AND)
            stringBuilder.append(HEIGHT)
            stringBuilder.append(" <= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(maxSize)
            whereArgs.add(maxSize)
        }

        private fun appendMinSideSize(stringBuilder: StringBuilder, whereArgs: MutableList<Any>) {
            stringBuilder.append(AND)
            stringBuilder.append(WIDTH)
            stringBuilder.append(" >= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            stringBuilder.append(AND)
            stringBuilder.append(HEIGHT)
            stringBuilder.append(" >= ")
            stringBuilder.append(VARIABLE_PLACEHOLDER)
            whereArgs.add(minSize)
            whereArgs.add(minSize)
        }


        fun filterMinSize(minSize: Int): Builder {
            type = type or TYPE_FILTER_MIN_SIDE_SIZE
            this.minSize = minSize
            return this
        }

        fun filterMaxSize(maxSize: Int): Builder {
            type = type or TYPE_FILTER_MAX_SIDE_SIZE
            this.maxSize = maxSize
            return this
        }

        fun filterMimeType(mimeTypes: List<String>, inverse: Boolean = false): Builder {
            type = type or TYPE_FILTER_MIME_TYPE
            this.mimeTypes = mimeTypes
            this.inverseMimeType = inverse
            return this
        }

        fun filterMinFileSize(minFileSize: Long): Builder {
            type = type or TYPE_FILTER_MIN_FILE_SIZE
            this.minFileSize = minFileSize
            return this
        }

        fun filterMaxFileSize(maxFileSize: Long): Builder {
            type = type or TYPE_FILTER_MAX_FILE_SIZE
            this.maxFileSize = maxFileSize
            return this
        }

        fun filterRelativePaths(relativePaths: List<String>): Builder {
            type = type or TYPE_FILTER_BUCKET_ID
            this.relativePaths = relativePaths
            return this
        }

        fun filterMinTakenDate(minTakenDate: Long): Builder {
            type = type or TYPE_FILTER_MIN_TAKEN_DATE
            this.minTakenDate = minTakenDate
            return this
        }

        fun filterMaxTakenDate(maxTakenDate: Long): Builder {
            type = type or TYPE_FILTER_MAX_TAKEN_DATE
            this.maxTakenDate = maxTakenDate
            return this
        }

        fun filterMinAddedDate(minAddedDate: Long): Builder {
            type = type or TYPE_FILTER_MIN_ADDED_DATE
            this.minAddedDate = minAddedDate
            return this
        }

        fun filterMaxAddedDate(maxAddedDate: Long): Builder {
            type = type or TYPE_FILTER_MAX_ADDED_DATE
            this.maxAddedDate = maxAddedDate
            return this
        }

        fun filterMinModifiedDate(minModifiedDate: Long): Builder {
            type = type or TYPE_FILTER_MIN_MODIFIED_DATE
            this.minModifiedDate = minModifiedDate
            return this
        }

        fun filterMaxModifiedDate(maxModifiedDate: Long): Builder {
            type = type or TYPE_FILTER_MAX_MODIFIED_DATE
            this.maxModifiedDate = maxModifiedDate
            return this
        }

        fun filterMediaType(mediaType: Int): Builder {
            type = type or TYPE_FILTER_MEDIA_TYPE
            this.mediaType = mediaType
            return this
        }

        fun orderBy(column: String, asc: Boolean) {
            if (orderBy == null) {
                orderBy = column + if (asc) ORDER_ASC else ORDER_DESC
            } else {
                orderBy += COMMA + column + if (asc) ORDER_ASC else ORDER_DESC
            }
        }
    }
}