/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OneDayMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/20
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D     2018/3/20    1.0        build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;


import static com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.GPS_KEY;

import android.content.Context;
import android.database.Cursor;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.location.utils.GeoDBHelper;
import com.oplus.gallery.business_lib.model.data.location.utils.LocationHelper;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.userprofile.UserProfileSettings;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper.DateRange;
import com.oplus.gallery.framework.abilities.scan.R;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OneDayMemories extends Memories {
    private static final String TAG = "OneDayMemories";
    private static final String STOP_REASON_PERMANENT_EMPTY = TAG + "_PermanentEmpty";
    private static final String STOP_REASON_ROUTER_NOT_FOUND = TAG + "_RouterNotFound";
    private static final String STOP_REASON_WIFI_DISCONNECTED = TAG + "_WifiDisconnected";
    private static final String STOP_REASON_CREATED_THIS_WEEK = TAG + "_CreatedThisWeek";
    private static final String STOP_REASON_SCAN_SIZE_NOT_ENOUGH = TAG + "_ScanSizeNotEnough";
    private static final String STOP_REASON_PERMISSION_UNAVAILABLE = TAG + "_PermissionUnavailable";
    private static final String STOP_REASON_UPDATE_PERMANENT_FAILED = TAG + "_UpdatePermanentFailed";
    private static final boolean DEBUG = false;
    private UserProfileSettings.Permanent mPermanent;

    public OneDayMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        if (MemoriesProviderHelper.hasMemoriesCreatedInThisWeek(getMemoriesGroupType())) {
            GLog.v(TAG, "prepareMemories, optimal date memories has created in this week!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATED_THIS_WEEK;
            return false;
        }
        if (PermissionHelper.isPermissionUnavailable()) {
            GLog.d(TAG, "prepareMemories, permission unavailable.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_PERMISSION_UNAVAILABLE;
            return false;
        }
        if (UserProfileSettings.needUpdatePermanentInfo(mContext, false)) {
            if (!NetworkMonitor.isWifiValidated()) {
                GLog.d(TAG, "prepareMemories, wifi is not connected.");
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_WIFI_DISCONNECTED;
                return false;
            }
            if (!UserProfileSettings.updatePermanent(mContext)) {
                GLog.d(TAG, "prepareMemories, updatePermanent failed.");
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_UPDATE_PERMANENT_FAILED;
                return false;
            }
        }
        mPermanent = UserProfileSettings.getPermanent(mContext);
        if ((mPermanent == null) || mPermanent.isEmpty()) {
            GLog.e(TAG, "prepareMemories, There is no permanent place.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_PERMANENT_EMPTY;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        Map<Long, String[]> searchRoute = getSearchRoute(mContext);
        if ((searchRoute == null) || searchRoute.isEmpty()) {
            GLog.d(TAG, "scanMemories, not found search route.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_ROUTER_NOT_FOUND;
            return false;
        }
        List<MediaItem> itemList = getOneDayMediaItems(searchRoute, mMemoriesPicMin);
        mMediaItemList = MemoriesProviderHelper.getFilteredItemList(itemList, getLabelFilterIdList());
        GLog.v(TAG, "scanMemories, filteredItemList size:" + mMediaItemList.size());
        if (mMediaItemList.size() <= mMemoriesPicMin) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            return false;
        }
        String name = TimeUtils.getMDDate(mContext, new Date(mMediaItemList.get(0).getDateTakenInMs()));
        setMemoriesName(name);
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return createMemories(mMediaItemList);
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.DATE_ONE_DAY_MEMORIES;
    }

    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_DATE_ONE_DAY;
    }

    private Map<Long, String[]> getSearchRoute(Context context) {
        GeoDBHelper geoDBHelper = null;
        Cursor cursor = null;
        try {
            geoDBHelper = new GeoDBHelper(context);
            cursor = geoDBHelper.queryLocalMediaTable(null, null, null, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                Map<Long, String[]> locations = getAllAddress(context);
                if ((locations == null) || locations.isEmpty()) {
                    GLog.w(TAG, "getSearchRoute, not found location.");
                    return null;
                }
                Map<Long, String[]> map = new HashMap<>();
                int indexGspKey = cursor.getColumnIndex(GPS_KEY);
                while (cursor.moveToNext()) {
                    String[] route = locations.get(cursor.getLong(indexGspKey));
                    map.put(cursor.getLong(indexGspKey), route);
                    if (DEBUG) {
                        GLog.v(TAG, "getSearchRoute, route=" + Arrays.toString(route));
                    }
                }
                GLog.d(TAG, "getSearchRoute, size=" + map.size());
                return map;
            } else {
                GLog.w(TAG, "getSearchRoute, not found geo id.");
            }
        } catch (Exception e) {
            GLog.w(TAG, "getSearchRoute, e=" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return null;
    }

    private Map<Long, String[]> getAllAddress(Context context) {
        GeoDBHelper geoDBHelper = new GeoDBHelper(context);
        return geoDBHelper.getAllAddress();
    }

    private List<MediaItem> getOneDayMediaItems(Map<Long, String[]> searchLocation, int minCount) {
        ArrayList<MediaItem> itemList = new ArrayList<>();
        Cursor cursor = null;
        try {
            String where = MemoriesProviderHelper.getOptimalScanWhereClause(mContext);
            String order = MediaStore.Images.Media.DATE_TAKEN + " DESC";
            cursor = new QueryReq.Builder<Cursor>().
                    setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalMediaItem.getProjection())
                    .setWhere(where)
                    .setOrderBy(order)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursor == null) || (cursor.getCount() < minCount)) {
                GLog.w(TAG, "getOneDayMediaItems, cursor is null.");
                return itemList;
            }
            Calendar calendar = Calendar.getInstance();
            long curTime = calendar.getTimeInMillis();
            DateRange hasMemoriesCreatedDateRange = new DateRange();
            List<Integer> typeList = new ArrayList<>();
            typeList.add(MemoriesType.DATE_ONE_DAY_MEMORIES);
            typeList.add(MemoriesType.EVENT_BIRTHDAY_MEMORIES);
            typeList.add(MemoriesType.EVENT_WEDDING_MEMORIES);
            typeList.add(MemoriesType.EVENT_FESTIVAL_MEMORIES);
            typeList.add(MemoriesType.EVENT_LAST_YEAR_FESTIVAL_MEMORIES);
            typeList.add(MemoriesType.EVENT_DAY_LAST_YEAR_MEMORIES);

            int idIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns._ID);
            int dateTakenIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN);
            int latitudeIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.LATITUDE);
            int longitudeIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.LONGITUDE);
            long currentDayInMillis = 0;
            while (cursor.moveToNext()) {
                long dateTaken = cursor.getLong(dateTakenIndex);
                if (MemoriesScannerHelper.getDaysBetweenTwoDates(dateTaken, curTime) <= 0) {
                    // we only generate OneDayMemories before today
                    continue;
                }
                if ((dateTaken >= hasMemoriesCreatedDateRange.mStart) && (dateTaken <= hasMemoriesCreatedDateRange.mEnd)) {
                    // current day has generate OneDayMemories already
                    continue;
                }
                if (hasMemoriesCreated(mContext, dateTaken, typeList)) {
                    hasMemoriesCreatedDateRange = MemoriesScannerHelper.getOneDayRangeByTime(dateTaken);
                }

                int id = cursor.getInt(idIndex);

                double latitude = cursor.getDouble(latitudeIndex);
                double longitude = cursor.getDouble(longitudeIndex);

                long key = getKey(latitude, longitude);
                String[] route = searchLocation.get(key);
                if ((route == null) || (route.length < (GeoDBHelper.LEVEL_CITY + 1))) {
                    continue;
                }
                String country = route[GeoDBHelper.LEVEL_COUNTRY];
                String city = route[GeoDBHelper.LEVEL_CITY];
                if (TextUtils.isEmpty(country) || TextUtils.isEmpty(city)) {
                    GLog.d(TAG, "getOneDayMediaItems, empty country or city.");
                    continue;
                }
                calendar.setTimeInMillis(dateTaken);
                if (currentDayInMillis <= 0) {
                    currentDayInMillis = getDayStartTime(calendar);
                }
                long tempDayInMillis = getDayStartTime(calendar);
                if (isPermanent(route, mPermanent)) {
                    Path childPath = LocalImage.ITEM_PATH.getChild(id);
                    MediaItem mediaItem = LocalMediaItem.loadOrUpdateItem(childPath, cursor);
                    if (currentDayInMillis != tempDayInMillis) {
                        currentDayInMillis = tempDayInMillis;
                        if (itemList.size() > minCount) {
                            return itemList;
                        }
                        itemList.clear();
                    }
                    itemList.add(mediaItem);
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return itemList;
    }

    /**
     * If item of one day is in memories already, include OneDayMemories, BirthdayMemories,
     * WeedingMemories or FestivalMemories, we need skip this day.
     *
     * @param context
     * @param dateTaken
     * @param typeList
     * @return
     */
    private boolean hasMemoriesCreated(Context context, long dateTaken, List<Integer> typeList) {
        if ((typeList == null) || (typeList.isEmpty())) {
            return false;
        }
        Cursor cursor = null;
        try {
            //Uri queryUri = MemoriesStore.Set.getContentUri();
            String[] projection = new String[]{GalleryStore.MemoriesSet.TYPE};
            StringBuilder sb = new StringBuilder();
            sb.append(GalleryStore.MemoriesSet.TYPE + " IN (");
            for (Integer type : typeList) {
                sb.append(type);
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append(") AND " + GalleryStore.MemoriesSet.START_TIME + " <= ");
            sb.append(dateTaken);
            sb.append(" AND " + GalleryStore.MemoriesSet.END_TIME + " >= ");
            sb.append(dateTaken);
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setProjection(projection)
                    .setWhere(sb.toString())
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return false;
            }
        } catch (Exception e) {
            GLog.w(TAG, "hasMemoriesCreated exception:" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return true;
    }

    private long getKey(double latitude, double longitude) {
        double cutOff = LocationHelper.PRECISION_CUT_OFF;
        return LocationHelper.makeKey(((int) (latitude * cutOff)) / cutOff,
                ((int) (longitude * cutOff)) / cutOff);
    }

    private boolean isPermanent(String[] route, UserProfileSettings.Permanent curPermanent) {
        if (curPermanent.mCountry.equals(route[GeoDBHelper.LEVEL_COUNTRY])) {
            String routeCity = route[GeoDBHelper.LEVEL_CITY];
            if (TextUtils.isEmpty(curPermanent.mCity)) {
                return true;
            }
            boolean sameCity = curPermanent.mCity.equals(routeCity)
                    || curPermanent.mCity.equals(getRealCityName(routeCity));
            if (sameCity) {
                String routeProvince = route[GeoDBHelper.LEVEL_PROVINCE];
                if (TextUtils.isEmpty(curPermanent.mProvince) || TextUtils.isEmpty(routeProvince)) {
                    return true;
                }
                boolean sameProvince = curPermanent.mProvince.equals(routeProvince)
                        || curPermanent.mProvince.equals(getRealProvinceName(routeProvince));
                if (sameProvince) {
                    return true;
                }
            }
        }
        return false;
    }

    private String getRealCityName(String city) {
        if (TextUtils.isEmpty(city)) {
            return city;
        }
        try {
            if (city.endsWith(ContextGetter.context.getString(R.string.scan_memory_city))) {
                return city.substring(0, city.length() - 1);
            }
        } catch (Exception e) {
            GLog.d(TAG, "getRealCityName, e=" + e);
        }
        return city;
    }

    private String getRealProvinceName(String province) {
        if (TextUtils.isEmpty(province)) {
            return province;
        }
        try {
            if (province.endsWith(ContextGetter.context.getResources().getString(R.string.scan_memory_province))) {
                return province.substring(0, province.length() - 1);
            }
        } catch (Exception e) {
            GLog.d(TAG, "getRealProvinceName, e=" + e);
        }
        return province;
    }

    private long getDayStartTime(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }
}
