/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SeniorSelectModelLoadingTemplate.kt
 ** Description : 精选的ModelLoadingTemplate
 ** Version     : 1.0
 ** Date        : 2024/3/22
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/3/22      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.business_lib.util.AssetHelper
import com.oplus.gallery.framework.abilities.download.ProcessResultCode
import com.oplus.gallery.framework.abilities.download.processor.ProcessResult
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.standard_lib.file.File

/**
 * 精选的ModelLoadingTemplate
 *
 * 当前只考虑使用内置模型，若后续上云，需要动态设置currentComponentPath
 */
abstract class SeniorSelectModelLoadingTemplate(context: Context, modelConfig: ModelConfig) : CommonModelLoadingTemplate(context, modelConfig) {
    /**
     * 需要加载的模型文件名
     * 比如iqa_quality.model、ssa_quality.model
     */
    abstract val modelFileName: String

    /**
     * 内置版本，比如manifest.ini中的version
     */
    override val buildInVersion: Int
        get() = modelConfig.buildInVersion

    /**
     * 使用目录
     */
    override val useModelComponentDir: File
        get() {
            return File(modelConfig.currentComponentPath)
        }

    /**
     * 加载内置模型
     * 若不存在同名同md5文件，会将[modelFileName]复制到[modelConfig.currentComponentPath]目录；存在将跳过复制
     */
    override fun loadBuildInModel(): ProcessResult {
        if (AssetHelper.getInstance().copyModelIfNeed(context, modelFileName, modelConfig.currentComponentPath).not()) {
            notifyFailure(ProcessResult(ProcessResultCode.FAIL))
            return ProcessResult(ProcessResultCode.FAIL)
        }
        notifySuccess()

        return ProcessResult(ProcessResultCode.SUCCESS)
    }

    /**
     * 检查是否需要加载内置模型
     */
    override fun checkIfNeedLoadBuildInModel(): Boolean {
        return buildInVersion > currentVersion
    }
}