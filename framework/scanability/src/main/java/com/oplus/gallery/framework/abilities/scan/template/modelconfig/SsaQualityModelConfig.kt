/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SsaQualityModelConfig.kt
 ** Description : 相似图美学评价ModelConfig
 ** Version     : 1.0
 ** Date        : 2024/3/22
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/3/22      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * 相似图美学评价ModelConfig
 */
class SsaQualityModelConfig(context: Context) : SeniorSelectModelConfig(context, ModelName.SSA_QUALITY_SOURCE) {

    override val builtInVersionKey = SSA_QUALITY_COMPONENT_DEFAULT_VERSION_NAME

    override val modelFileName: String = SSA_QUALITY_MODEL_NAME

    companion object {
        /**
         * 从manifest.ini中读取版本号时的key
         */
        const val SSA_QUALITY_COMPONENT_DEFAULT_VERSION_NAME = "ssa_quality_component_default_version"

        /**
         * 加载模型文件
         */
        private const val SSA_QUALITY_MODEL_NAME = "ssa_quality.model"
    }
}