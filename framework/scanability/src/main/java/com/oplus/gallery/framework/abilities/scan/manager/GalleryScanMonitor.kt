/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GalleryScanMonitor.kt
 * Description: 扫描条件的工具类
 * Version: 1.0
 * Date: 2016/11/23
 ** Author: <EMAIL>
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>     2016/11/23     1.0            Build this module
 * <EMAIL>    2019/05/06     1.1            Move the power related interface to BatteryStatusUtil
 * <EMAIL>    2022/4/19      1.2            Convert this class from java to kt
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.scan.manager

import android.app.AlarmManager
import android.content.Context
import android.os.SystemClock
import androidx.annotation.IntDef
import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig
import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig.getDefaultGroupValue
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.Value.SCHEDULE_JOB_SCAN_TRIGGER
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.storage.SPUtils.getSp
import com.oplus.gallery.foundation.util.systemcore.ActivityManagerUtils
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.foundation.util.systemcore.KeyguardManagerUtils
import com.oplus.gallery.foundation.util.systemcore.PowerManagerUtils
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.CAPTION_SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.MultiModal
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_COVER_SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.download.IDownloadAbility
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.AUTO_CROP_SCAN_COUNT_24H_KEY
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.FACE_SCAN_COUNT_24h_KEY
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.LABEL_SCAN_COUNT_24h_KEY
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.MEMORIES_SCAN_COUNT_24h_KEY
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.OCR_SCAN_COUNT_24h_KEY
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.PET_SCAN_COUNT_24H_KEY
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.SENIOR_SELECT_SCAN_COUNT_24H_KEY
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils.STUDY_SCAN_COUNT_24H_KEY
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.abs

object GalleryScanMonitor {
    /**
     * 云端设置未充电时的最大扫描时长
     */
    private const val KEY_MAX_SCAN_COST_TIME_NO_CHARGING = "float_max_scan_cost_time_no_charging"
    /**
     * 云端设置单批次图片扫描数量
     */
    private const val KEY_IMAGE_ONCE_SCAN_COUNT = "int_image_once_scan_count"
    /**
     * 云端设置单批次视频扫描数量
     */
    private const val KEY_VIDEO_ONCE_SCAN_COUNT = "int_video_once_scan_count"
    /**
     * 云端设置24小时内最大扫描图片数量
     */
    private const val KEY_24H_MAX_IMAGE_SCAN_COUNT = "int_24h_max_image_scan_count"
    /**
     * 云端设置24小时内最大扫描视频数量
     */
    private const val KEY_24H_MAX_VIDEO_SCAN_COUNT = "int_24h_max_video_scan_count"

    /**
     * 默认24小时内最大扫描数量，5k
     */
    private const val SCAN_COUNT_DEFAULT_24H_MAX = 5 * 1000
    /**
     * 默认24小时内最大扫描图片数量，5k
     */
    private const val DEFAULT_IMAGE_SCAN_COUNT_24H_MAX = SCAN_COUNT_DEFAULT_24H_MAX
    /**
     * 默认24小时内最大扫描视频数量，625
     */
    private const val DEFAULT_VIDEO_SCAN_COUNT_24H_MAX = SCAN_COUNT_DEFAULT_24H_MAX / 8
    /**
     * 默认未充电扫描时长，1h
     */
    private const val DEFAULT_MAX_SCAN_COST_TIME_NO_CHARGING = TimeUtils.TIME_1_HOUR_IN_MS
    /**
     * 默认单批次图片扫描数量
     */
    private const val DEFAULT_IMAGE_ONCE_SCAN_COUNT = 500
    /**
     * 默认单批次视频扫描数量
     */
    private const val DEFAULT_VIDEO_ONCE_SCAN_COUNT = 62
    private const val TAG = ScanConst.TAG + "GalleryScanMonitor"
    private const val SCENE_OVER_MEMORY_SIZE = "OverMemorySize"
    private const val INTERVAL_2_DAY = AlarmManager.INTERVAL_DAY * 2
    private const val BATTERY_LEVEL_ALLOW_NOT_CHARGING_ON_CONTINUE_SCAN = 0.65f
    private const val BATTERY_LEVEL_ALLOW_IN_CHARGING = 0.30f
    private const val BATTERY_LEVEL_ALLOW_NOT_CHARGING_ON_START_SCAN = 0.70f
    private const val BATTERY_LEVEL_ALLOW_NOT_CHARGING_ON_CONTINUE_SCAN_COST = 0.02f
    private const val DELTA = 0.0001f
    private const val TEST_SCALE = 10
    private var sScanStartTime: Long = 0
    private var sScanCostTime24h: Long = 0
    private var sStartScanBatteryPct = 0.0f
    private var sScanStartTime24h = 0.0f
    private var sScanOnTopStartTime: Long = 0
    /**
     * 24小时内最大扫描标签数量，1k
     */
    const val LABEL_SCAN_COUNT_24H_MAX = 1000
    /**
     * 24小时内最大扫描人脸数量，5k
     */
    const val FACE_SCAN_COUNT_24H_MAX = SCAN_COUNT_DEFAULT_24H_MAX
    /**
     * 24小时内最大扫描宠物数量，5k
     */
    const val PET_SCAN_COUNT_24H_MAX = SCAN_COUNT_DEFAULT_24H_MAX
    /**
     * 24小时内最大扫描多模态数量，5k
     */
    const val MULTI_MODAL_SCAN_COUNT_24H_MAX = SCAN_COUNT_DEFAULT_24H_MAX
    /**
     * 24小时内最大扫描精彩回忆数量，10
     */
    const val MEMORIES_SCAN_COUNT_24H_MAX = 10
    const val OCR_SCAN_COUNT_24H_MAX = 1000
    /**
     * 24小时内最大扫描OCR语义数量，5k
     */
    const val OCR_ENBEDDING_SCAN_COUNT_24H_MAX = SCAN_COUNT_DEFAULT_24H_MAX
    /**
     * 旅程24小时内最大扫描数量(旅程生成数量和旅程封面打分数量)
     */
    const val TRAVEL_SCAN_COUNT_24H_MAX = SCAN_COUNT_DEFAULT_24H_MAX

    /**
     * 24小时内最大扫描学习数量，20
     */
    const val STUDY_SCAN_COUNT_24H_MAX = 20

    /*
    * 24小时内最大扫描caption数量，200
     */
    const val CAPTION_SCAN_COUNT_24_MAX = 200

    private val SCAN_COUNT_KEYS = arrayOf(
        FACE_SCAN_COUNT_24h_KEY,
        LABEL_SCAN_COUNT_24h_KEY,
        MEMORIES_SCAN_COUNT_24h_KEY,
        OCR_SCAN_COUNT_24h_KEY,
        SENIOR_SELECT_SCAN_COUNT_24H_KEY,
        AUTO_CROP_SCAN_COUNT_24H_KEY,
        PET_SCAN_COUNT_24H_KEY,
        STUDY_SCAN_COUNT_24H_KEY
    )

    /**
     * 云端设置未充电时的最大扫描时长
     *
     * 默认值 1小时
     * @see DEFAULT_MAX_SCAN_COST_TIME_NO_CHARGING
     *
     * @return 云端设置未充电时的最大扫描时长
     */
    @JvmStatic
    fun getCloudMaxScanTimeNoCharging(): Long {
        val cloudValue = getCloudValue(KEY_MAX_SCAN_COST_TIME_NO_CHARGING) as? Float ?: 0f

        // 服务器未设值或设值为小于等于0，返回默认值
        val result = if (cloudValue <= 0) {
            DEFAULT_MAX_SCAN_COST_TIME_NO_CHARGING.toLong()
        } else {
            (cloudValue * TimeUtils.TIME_1_HOUR_IN_MS).toLong()
        }
        GLog.d(TAG, LogFlag.DL, "getCloudMaxScanTimeNoCharging, cloudValue = $cloudValue, return result = $result")
        return result
    }

    /**
     * 云端设置单批次图片扫描数量
     *
     * 默认值 500
     * @see DEFAULT_IMAGE_ONCE_SCAN_COUNT
     *
     * @return 云端设置单批次图片扫描数量
     */
    @JvmStatic
    fun getCloudImageOnceScanCount(): Int {
        val cloudValue = getCloudValue(KEY_IMAGE_ONCE_SCAN_COUNT) as? Int ?: 0

        // 服务器未设值或设值为小于等于0，返回默认值
        val result = if (cloudValue <= 0) {
            DEFAULT_IMAGE_ONCE_SCAN_COUNT
        } else {
            cloudValue
        }
        GLog.d(TAG, LogFlag.DL, "getCloudImageOnceScanCount, cloudValue = $cloudValue, return result = $result")
        return result
    }

    /**
     * 云端设置单批次视频扫描数量
     *
     * 默认值 62
     * @see DEFAULT_VIDEO_ONCE_SCAN_COUNT
     *
     * @return 云端设置单批次视频扫描数量
     */
    @JvmStatic
    fun getCloudVideoOnceScanCount(): Int {
        val cloudValue = getCloudValue(KEY_VIDEO_ONCE_SCAN_COUNT) as? Int ?: 0

        // 服务器未设值或设值为小于等于0，返回默认值
        val result = if (cloudValue <= 0) {
            DEFAULT_VIDEO_ONCE_SCAN_COUNT
        } else {
            cloudValue
        }
        GLog.d(TAG, LogFlag.DL, "getCloudVideoOnceScanCount, cloudValue = $cloudValue, return result = $result")
        return result
    }

    /**
     * 云端设置24小时内最大扫描图片数量
     *
     * 默认值 5k
     * @see DEFAULT_IMAGE_SCAN_COUNT_24H_MAX
     *
     * @return 云端设置24小时内最大扫描图片数量
     */
    @JvmStatic
    fun getCloud24HMaxImageScanCount(): Int {
        val cloudValue = getCloudValue(KEY_24H_MAX_IMAGE_SCAN_COUNT) as? Int ?: 0

        // 服务器未设值或设值为小于等于0，返回默认值
        val result = if (cloudValue <= 0) {
            DEFAULT_IMAGE_SCAN_COUNT_24H_MAX
        } else {
            cloudValue
        }
        GLog.d(TAG, LogFlag.DL, "getCloud24HMaxImageScanCount, cloudValue = $cloudValue, return result = $result")
        return result
    }

    /**
     * 云端设置24小时内最大扫描视频数量
     *
     * 默认值 625
     * @see DEFAULT_VIDEO_SCAN_COUNT_24H_MAX
     *
     * @return 云端设置24小时内最大扫描视频数量
     */
    @JvmStatic
    fun getCloud24HMaxVideoScanCount(): Int {
        val cloudValue = getCloudValue(KEY_24H_MAX_VIDEO_SCAN_COUNT) as? Int ?: 0

        // 服务器未设值或设值为小于等于0，返回默认值
        val result = if (cloudValue <= 0) {
            DEFAULT_VIDEO_SCAN_COUNT_24H_MAX
        } else {
            cloudValue
        }
        GLog.d(TAG, LogFlag.DL, "getCloud24HMaxVideoScanCount, cloudValue = $cloudValue, return result = $result")
        return result
    }

    private fun getCloudValue(
        key: String,
        module: String = ScanConst.MODULE_NAME,
        defaultValue: String = "0",
        group: String = CloudParamConfig.DEFAULT_GROUP_NAME
    ): Any {
        return getDefaultGroupValue(module, key, defaultValue, group)
    }

    @JvmStatic
    fun resetScanStartTime() {
        sScanStartTime = SystemClock.uptimeMillis()
    }

    @JvmStatic
    fun resetScanOnTopStartTime() {
        sScanOnTopStartTime = SystemClock.uptimeMillis()
    }

    @JvmStatic
    fun initialScanTimeInfo24h(context: Context?) {
        sScanCostTime24h = GalleryScanUtils.getScanCostTime24h(context)
        sScanStartTime24h = GalleryScanUtils.getScanStartTime24h(context).toFloat()
    }

    @JvmStatic
    fun refreshScanRecordingIfNecessary(context: Context?) {
        context?.let {
            val time = System.currentTimeMillis()
            if (time - sScanStartTime24h > AlarmManager.INTERVAL_DAY) {
                val editor = getSp(it, ComponentPrefUtils.COMPONENT_PREF_NAME).edit()
                SCAN_COUNT_KEYS.forEach {
                    editor.putInt(it, 0)
                }
                editor.putLong(GalleryScanUtils.SCAN_COST_TIME_24h_KEY, 0)
                editor.putLong(GalleryScanUtils.SCAN_START_TIME_24h_KEY, time)
                editor.apply()
                (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use { it1 ->
                    it1.setIntConfig(MultiModal.SCAN_COUNT_24H, 0)
                    it1.setIntConfig(TRAVEL_SCAN_COUNT_24H, 0)
                    it1.setIntConfig(TRAVEL_COVER_SCAN_COUNT_24H, 0)
                    it1.setIntConfig(CAPTION_SCAN_COUNT_24H, 0)
                }
                initialScanTimeInfo24h(it)
            } else if (time < sScanStartTime24h) {
                GalleryScanUtils.setScanStartTime24h(it, time)
                initialScanTimeInfo24h(it)
            }
        }
    }

    @JvmStatic
    fun setStartBatteryPct(context: Context?) {
        sStartScanBatteryPct = BatteryStatusUtil.getCurrentBatteryPercent(context)
    }

    /**
     * 检查是否允许启动扫描。当同时满足下面的条件时，允许启动扫描，返回 true。否则返回false。
     * - 充电时，电量 >= 30% ；非充电时，电量 >= 70%
     * - 电池温度 < 37℃
     * - 息屏/已锁屏
     */
    @JvmStatic
    fun isAllowStartScan(context: Context?, needCharging: Boolean, canScanWhenGalleryRunOnTop: Boolean): Boolean {
        if (canScanWhenGalleryRunOnTop) {
            if (!BatteryStatusUtil.isBatteryAllowStart()) {
                GLog.d(TAG, LogFlag.DL) { "isAllowStartScan, isBatteryAllowStart is false" }
                return false
            }
        } else {
            if (!isCurrentBatteryAllowStartScan(context, needCharging)) {
                GLog.d(TAG, LogFlag.DL) { "isAllowStartScan, isCurrentBatteryAllowStartScan is false" }
                return false
            }
        }
        if  (canScanWhenGalleryRunOnTop) {
            if (!TemperatureUtil.isTemperatureAllowStartScanOnTopScan()) {
                GLog.d(TAG, LogFlag.DL, "isAllowStartScan, isTemperatureAllowStartScan is false")
                return false
            }
        } else {
            if (!TemperatureUtil.isTemperatureAllowStartScan()) {
                GLog.d(TAG, LogFlag.DL, "isAllowStartScan, isTemperatureAllowStartScan is false")
                return false
            }
        }

        if (isScreenOnAndKeyguardUnlocked && !canScanWhenGalleryRunOnTop) {
            GLog.d(TAG, LogFlag.DL, "isAllowStartScan, isScreenOnAndKeyguardUnlocked is true")
            return false
        }
        return true
    }

    /**
     * 检查是否允许继续扫描。当同时满足下面的条件时，允许继续扫描，返回 true。否则返回false。
     * - 非充电时，扫描用时 <= 2 个小时
     * - 电池温度 < 38℃
     * - 息屏/已锁屏
     * - 充电时，扫描用时不限制，电量 >= 30% ；非充电时，电量 >= 65%
     * - 消耗的电量是否在 2% 以内
     * - 前台业务发起的扫描，扫描用时 <= 20分钟  电池温度 < 41℃  #目前AI最佳表情使用
     */
    @JvmStatic
    fun isAllowContinueScan(context: Context?, needCharging: Boolean, canScanWhenGalleryRunOnTop: Boolean): Boolean {

        val isCostTimeAllowScan = if (canScanWhenGalleryRunOnTop) {
            isCostTimeAllowScanOnTop()
        } else {
            isCostTimeAllowScan(context)
        }
        if (!isCostTimeAllowScan) {
            GLog.w(TAG, LogFlag.DL, "isAllowContinueScan cost too much time, not allow to continue scan")
            return false
        }
        if (DEBUG) {
            GLog.d(TAG, LogFlag.DL, "isAllowContinueScan isNormalScanPolicy:" + GalleryScanUtils.isNormalScanPolicy() + ","
                    + "isScreenOnAndKeyguardUnlocked:$isScreenOnAndKeyguardUnlocked, " +
                    "isTemperatureAll:${TemperatureUtil.isTemperatureAllowContinueScan()}," +
                    "isCurrentBatteryAllowContinueScan:${isCurrentBatteryAllowContinueScan(context, needCharging)}," +
                    "isCostBatteryAllow:${isCostBatteryAllow(context)}")
        }
        val isTemperatureAllowContinueScan = if (canScanWhenGalleryRunOnTop) {
            TemperatureUtil.isTemperatureAllowContinueOnTopScan()
        } else {
            TemperatureUtil.isTemperatureAllowContinueScan()
        }

        // 前台扫描低电量与其他扫描分开
        val isCurrentBatteryAllow = if (canScanWhenGalleryRunOnTop) {
            BatteryStatusUtil.isBatteryAllowStart()
        } else {
            isCurrentBatteryAllowContinueScan(context, needCharging)
        }

        // 如果是前台扫描不考虑掉电，直接返回true
        val isCostBatteryAllow = if (canScanWhenGalleryRunOnTop) {
            true
        } else {
            isCostBatteryAllow(context)
        }

        return if (GalleryScanUtils.isNormalScanPolicy() && isScreenOnAndKeyguardUnlocked && !canScanWhenGalleryRunOnTop) {
            false
        } else {
            isTemperatureAllowContinueScan && isCurrentBatteryAllow && isCostBatteryAllow
        }
    }

    /**
     * 启动扫描前，根据中断条件，转换成对应的状态代码。
     */
    @JvmStatic
    fun getReasonTypeOnStart(
        context: Context?,
        needCharging: Boolean,
        scanCount: Int,
        maxScanCount: Int,
        lastScanTime: Long,
        checkOnTop: Boolean
    ): Int {
        return when {
            !isCurrentBatteryAllowStartScan(context, needCharging) -> ReasonType.REASON_POWER_LOW
            !TemperatureUtil.isTemperatureAllowStartScan() -> ReasonType.REASON_TEMPERATURE_HIGH
            else -> getReasonType(context, scanCount, maxScanCount, lastScanTime, checkOnTop)
        }
    }

    /**
     * 启动扫描前，根据中断条件，转换成对应的状态代码。前端触发的扫描使用
     * @param context
     * @param scanCount
     * @param maxScanCount
     * @return 获取扫描中断原因
     */
    @JvmStatic
    fun getReasonTypeOnStartByOnTop(
        context: Context?,
        scanCount: Int,
        maxScanCount: Int
    ): Int {
        return when {
            !BatteryStatusUtil.isBatteryAllowStart() -> ReasonType.REASON_POWER_LOW
            !TemperatureUtil.isTemperatureAllowStartScanOnTopScan() -> ReasonType.REASON_TEMPERATURE_HIGH
            !isCostBatteryAllow(context) -> ReasonType.REASON_POWER_CONSUMPTION
            !isCostTimeAllowScanOnTop() -> ReasonType.REASON_COST_TIME_20_MINUTES
            !isCountAllow(scanCount, maxScanCount) -> ReasonType.REASON_EXCEEDING_MAX_COUNT
            else -> ReasonType.REASON_OK
        }
    }

    /**
     * 扫描过程中，根据中断条件，转换成对应的状态代码。
     */
    @JvmStatic
    fun getReasonTypeOnContinue(
        context: Context?,
        needCharging: Boolean,
        scanCount: Int,
        maxScanCount: Int,
        lastScanTime: Long,
        checkOnTop: Boolean
    ): Int {
        return when {
            !isCurrentBatteryAllowContinueScan(context, needCharging) -> ReasonType.REASON_POWER_LOW
            !TemperatureUtil.isTemperatureAllowContinueScan() -> ReasonType.REASON_TEMPERATURE_HIGH
            else -> getReasonType(context, scanCount, maxScanCount, lastScanTime, checkOnTop)
        }
    }

    @JvmStatic
    private fun getReasonType(
        context: Context?,
        scanCount: Int,
        maxScanCount: Int,
        lastScanTime: Long,
        checkOnTop: Boolean
    ): Int {
        return when {
            isScreenOnAndKeyguardUnlocked -> ReasonType.REASON_SCREEN_ON
            !isCostBatteryAllow(context) -> ReasonType.REASON_POWER_CONSUMPTION
            !isCostTimeAllowScan(context) -> ReasonType.REASON_RUN_2_HOURS
            !isCountAllow(scanCount, maxScanCount) -> ReasonType.REASON_EXCEEDING_MAX_COUNT
            (isLastScanWithinOneHour(lastScanTime) == true) -> ReasonType.REASON_WITHIN_1_HOURS
            checkOnTop && isGalleryRunOnTop() -> ReasonType.REASON_ON_TOP
            else -> ReasonType.REASON_OK
        }
    }

    /**
     * 检查扫描用时是否超时。[ReasonType.REASON_RUN_2_HOURS]
     * - 非充电时，扫描用时 <= 2 个小时，返回 true
     * - 充电时，扫描用时不限制，返回 true
     * - 否则，返回 false
     */
    @JvmStatic
    fun isCostTimeAllowScan(context: Context?): Boolean {
        if (!BatteryStatusUtil.isBatteryInCharging(false)) {
            sScanCostTime24h += SystemClock.uptimeMillis() - sScanStartTime
            GalleryScanUtils.setScanCostTime24h(context, sScanCostTime24h)
            val maxScanCostTime = if (GalleryScanUtils.isCostTimeTestModel()) {
                GalleryScanUtils.getTestMaxScanCostTime()
            } else {
                getCloudMaxScanTimeNoCharging()
            }
            val allow = sScanCostTime24h <= maxScanCostTime
            if (!allow) {
                GLog.w(TAG, LogFlag.DL, "isCostTimeAllowScan, condition unmet. Scan too much time: $sScanCostTime24h")
            }
            resetScanStartTime()
            return allow
        }
        resetScanStartTime()
        return true
    }

    /**
     * 检查前台扫描用时是否超时。
     * @return true/false
     * 超过20分钟，则返回 false
     * 否则，返回 true
     */
    @JvmStatic
    fun isCostTimeAllowScanOnTop(): Boolean {
        val currentCost = SystemClock.uptimeMillis() - sScanOnTopStartTime

        // 判断扫描耗时是否超过20分钟
        val allow = currentCost <= TimeUtils.TIME_20_MIN_IN_MS

        // 如果超过20分钟，则记录日志
        if (!allow) {
            GLog.w(TAG, LogFlag.DL, "isCostTimeAllowScanOnTop, scanning time exceeded 20 minutes: $currentCost ms")
        }

        // 返回判断结果
        return allow
    }

    /**
     * 检查上一次扫描时间是否在 1 小时内。[ReasonType.REASON_WITHIN_1_HOURS]
     * - 1 小时内，返回 true。
     * - 否则，返回 false。
     * - 参数无效（-1）时，返回 null。
     */
    @JvmStatic
    fun isLastScanWithinOneHour(lastScanTime: Long): Boolean? {
        return when {
            (lastScanTime == GalleryScanUtils.DEFAULT_SCAN_TIME) -> null
            (abs(System.currentTimeMillis() - lastScanTime) < AlarmManager.INTERVAL_HOUR) -> true
            else -> false
        }
    }

    /**
     * 检查是否亮屏 + 已解锁/无锁。[ReasonType.REASON_SCREEN_ON]
     * - 亮屏 + 有锁屏密码 + 已解锁，返回 true
     * - 亮屏 + 无锁屏密码，返回 true
     * - 否则， 返回 false
     */
    @JvmStatic
    val isScreenOnAndKeyguardUnlocked: Boolean
        get() {
            if (PowerManagerUtils.isInteractive() && !KeyguardManagerUtils.isKeyguardLocked()) {
                GLog.w(TAG, LogFlag.DL, "isScreenOnAndKeyguardUnlocked, condition unmet. Screen on and keyguard is unlocked.")
                return true
            }
            return false
        }

    /**
     * 检查当前电量是否允许继续扫描。[ReasonType.REASON_POWER_LOW]
     * - 充电时，电量 >= 30%，返回 true
     * - 非充电时， 电量 >= 65%，返回 true
     * - 否则， 返回 false
     */
    @JvmStatic
    fun isCurrentBatteryAllowContinueScan(context: Context?, needCharging: Boolean): Boolean {
        val allow = isCurrentBatteryAllowScan(context, needCharging, BATTERY_LEVEL_ALLOW_NOT_CHARGING_ON_CONTINUE_SCAN)
        if (DEBUG && !allow) {
            val batteryPct = BatteryStatusUtil.getCurrentBatteryPercent(context)
            GLog.w(
                TAG, LogFlag.DL,
                "isCurrentBatteryAllowContinueScan, condition unmet. current battery:" + batteryPct
                        + " isCharging:" + BatteryStatusUtil.isBatteryInCharging(false)
                        + " cost battery:" + (sStartScanBatteryPct - batteryPct)
            )
        }
        return allow
    }

    /**
     * 检查当前电量是否允许开始扫描。[ReasonType.REASON_POWER_LOW]
     * - 充电时，电量 >= 30%，返回 true
     * - 非充电时， 电量 >= 70%，返回 true
     * - 否则， 返回 false
     */
    @JvmStatic
    fun isCurrentBatteryAllowStartScan(context: Context?, needCharging: Boolean): Boolean {
        val allow = isCurrentBatteryAllowScan(context, needCharging, BATTERY_LEVEL_ALLOW_NOT_CHARGING_ON_START_SCAN)
        if (DEBUG && !allow) {
            val batteryPct = BatteryStatusUtil.getCurrentBatteryPercent(context)
            GLog.w(
                TAG, LogFlag.DL,
                "isCurrentBatteryAllowStartScan, condition unmet. low battery:" + batteryPct
                        + " isCharging:" + BatteryStatusUtil.isBatteryInCharging(false)
            )
        }
        return allow
    }

    @JvmStatic
    private fun isCurrentBatteryAllowScan(context: Context?, needCharging: Boolean, batteryLevelAllowNotCharging: Float): Boolean {
        if (BatteryStatusUtil.isPowerTestModel()) {
            return true
        }
        val batteryPct = BatteryStatusUtil.getCurrentBatteryPercent(context)
        return if (needCharging) {
            BatteryStatusUtil.isBatteryInCharging(false)
                    && (batteryPct >= BATTERY_LEVEL_ALLOW_IN_CHARGING)
        } else {
            if (BatteryStatusUtil.isBatteryInCharging(false)) {
                batteryPct >= BATTERY_LEVEL_ALLOW_IN_CHARGING
            } else {
                batteryPct >= batteryLevelAllowNotCharging
            }
        }
    }

    /**
     * 检查消耗的电量是否在 2% 以内。[ReasonType.REASON_POWER_CONSUMPTION]
     * - 充电时，电量一直在增加，返回 true
     * - 耗电量超过 2%，返回 false
     * - 否则，返回 true
     */
    @JvmStatic
    fun isCostBatteryAllow(context: Context?): Boolean {
        if (BatteryStatusUtil.isPowerTestModel()) {
            return true
        }
        val currentBatteryPct = BatteryStatusUtil.getCurrentBatteryPercent(context)
        // 在充电的时候，电量一直在增长，需要更新开始扫描的电量到当前电量
        if (BatteryStatusUtil.isBatteryInCharging(false)
            && (currentBatteryPct > sStartScanBatteryPct)) {
            sStartScanBatteryPct = currentBatteryPct
            GLog.d(TAG, LogFlag.DL, "isCostBatteryAllow, the battery increase, so update start battery, return true")
            return true
        }
        val allow = sStartScanBatteryPct - currentBatteryPct - BATTERY_LEVEL_ALLOW_NOT_CHARGING_ON_CONTINUE_SCAN_COST < DELTA
        if (!allow) {
            GLog.w(
                TAG, LogFlag.DL, "isCostBatteryAllow, condition unmet. start battery=" + sStartScanBatteryPct
                        + ", cur battery=" + currentBatteryPct
            )
        }
        return allow
    }

    /**
     * 扫描数量超限。[ReasonType.REASON_EXCEEDING_MAX_COUNT]
     */
    @JvmStatic
    fun isCountAllow(scanCount: Int, maxScanCount: Int): Boolean {
        if (GalleryScanUtils.isCountTestModel()) {
            return true
        }
        val allow = scanCount < maxScanCount
        if (!allow) {
            GLog.w(
                TAG, LogFlag.DL,
                "isCountAllow, condition unmet. count exceeds limit, scanCount:$scanCount, maxScanCount:$maxScanCount"
            )
        }
        return allow
    }

    /**
     * 检查相册是否处于前台。[ReasonType.REASON_ON_TOP]
     * - 处于前台，返回 true。否则，返回 false。
     */
    @JvmStatic
    fun isGalleryRunOnTop(): Boolean {
        if (!GalleryScanUtils.isNormalScanPolicy()) {
            return false
        }
        val isTop = ActivityLifecycle.isRunningForeground()
        if (isTop) {
            GLog.d(TAG, LogFlag.DL, "isGalleryRunOnTop, condition unmet. Gallery app is in the foreground.")
        }
        return isTop
    }

    /**
     * 是否距离上次扫描开始时间已经超期
     * 当前时间-上次扫描开始时间>云端拉取的时间间隔 || 当前时间小于扫描开始时间（用户手动调时间回到过去，扫描开始即会刷新）
     */
    @JvmStatic
    fun isLastScanExpired(context: Context?): Boolean {
        val scanTaskStartTime = GalleryScanUtils.getScanTaskStartTime(context)
        val time = System.currentTimeMillis()
        val intervalTime = getCloudPhotoScanIntervalTime().let { time -> if (time != 0L) time else INTERVAL_2_DAY }
        GLog.w(
            TAG, LogFlag.DL, "isLastScanExpired, time=" + TimeUtils.getFormatDateTime(time)
                    + ", scanTaskStartTime=" + TimeUtils.getFormatDateTime(scanTaskStartTime)
                    + ", intervalTime=" + intervalTime
        )
        return ((time - scanTaskStartTime) > intervalTime) || (time < scanTaskStartTime)
    }

    /**
     * 云端设置下次触发扫描的间隔时间
     * @return 下次扫描时间间隔
     */
    @JvmStatic
    fun getCloudPhotoScanIntervalTime(): Long {
        val triggerPhotoScanIntervalTime = "int_trigger_photo_scan_interval_time"
        val intervalTime = getDefaultGroupValue(
            ScanConst.MODULE_NAME,
            triggerPhotoScanIntervalTime,
            "0",
            CloudParamConfig.DEFAULT_GROUP_NAME) as? Int ?: 0
        GLog.d(TAG, LogFlag.DL, "getCloudPhotoScanInterval $intervalTime")
        return intervalTime * AlarmManager.INTERVAL_DAY
    }

    /**
     * 是否允许通过Alarm来触发扫描
     * 是否距离上次扫描超期 || 是否上次标签和人脸没有正常执行
     */
    @JvmStatic
    fun allowScanWithAlarmTrigger(context: Context?): Boolean {
        return (isLastScanExpired(context)
                || !GalleryScanUtils.getScanWentWellFlag(context, GalleryScanUtils.PREF_FACE_SCAN_WENT_WELL_KEY)
                || !GalleryScanUtils.getScanWentWellFlag(context, GalleryScanUtils.PREF_LABEL_SCAN_WENT_WELL_KEY))
    }

    @JvmStatic
    fun refreshScanTaskStartTime(context: Context?) {
        if (isLastScanExpired(context)) {
            GLog.d(TAG, LogFlag.DL, "refreshScanTaskStartTime, reset")
            GalleryScanUtils.setScanTaskStartTime(context, System.currentTimeMillis())
            GalleryScanUtils.setScanWentWellFlag(context, GalleryScanUtils.PREF_FACE_SCAN_WENT_WELL_KEY, false)
            GalleryScanUtils.setScanWentWellFlag(context, GalleryScanUtils.PREF_LABEL_SCAN_WENT_WELL_KEY, false)
        }
    }

    @JvmStatic
    fun refreshScanWentWellFlag(context: Context?, prefsKey: String?, flag: Boolean) {
        GLog.d(
            TAG, LogFlag.DL,
            "refreshScanWentWellFlag, flag=$flag"
        )
        if (flag && !GalleryScanUtils.getScanWentWellFlag(context, prefsKey)) {
            GalleryScanUtils.setScanWentWellFlag(context, prefsKey, true)
        }
    }

    @JvmStatic
    fun killProcessIfNeed(context: Context, delayTime: Long) {
        AppScope.launch {
            delay(delayTime)
            // kill process
            val isInCharging = BatteryStatusUtil.isBatteryInCharging(false)
            val isModelDownloadIdle = context.getAppAbility<IDownloadAbility>()?.use { downloadAbility ->
                downloadAbility.isDownloadIdle()
            } ?: true
            val needKillProcess = !ActivityLifecycle.isRunningForeground() && isInCharging && isModelDownloadIdle
            GLog.d(TAG, LogFlag.DL) {
                "killProcessIfNeed, needKillProcess=$needKillProcess, isInCharging=$isInCharging, " +
                        "isModelDownloadIdle=$isModelDownloadIdle"
            }
            if (needKillProcess) {
                ScanTrackHelper.trackStopReasonOnStart(context, SCENE_OVER_MEMORY_SIZE, SCHEDULE_JOB_SCAN_TRIGGER)
                PowerManagerUtils.removeStageProtect(context)
                ActivityManagerUtils.forceStopGallery(context)
            }
        }
    }

    @kotlin.annotation.Retention(AnnotationRetention.SOURCE)
    @IntDef(
        ReasonType.REASON_UNDEFINED,
        ReasonType.REASON_OK,
        ReasonType.REASON_POWER_LOW,
        ReasonType.REASON_POWER_CONSUMPTION,
        ReasonType.REASON_RUN_2_HOURS,
        ReasonType.REASON_TEMPERATURE_HIGH,
        ReasonType.REASON_EXCEEDING_MAX_COUNT,
        ReasonType.REASON_SCREEN_ON,
        ReasonType.REASON_SYS_INTERRUPT,
        ReasonType.REASON_LOW_POWER_CONSUMPTION,
        ReasonType.REASON_WITHIN_1_HOURS,
        ReasonType.REASON_ON_TOP,
        ReasonType.REASON_COST_TIME_20_MINUTES,
        ReasonType.REASON_MANUAL_CANCEL
    )

    annotation class ReasonType {
        companion object {

            /**
             * 未定义的原因
             */
            const val REASON_UNDEFINED = -1

            /**
             * 正常结束
             */
            const val REASON_OK = 0

            /**
             * 电量过低。充电时，电量 < 30% ；非充电时， 电量 < 70%
             */
            const val REASON_POWER_LOW = 1

            /**
             * 电量消耗超过 2%
             */
            const val REASON_POWER_CONSUMPTION = 2

            /**
             * 非充电时，扫描运行超过 2 个小时
             */
            const val REASON_RUN_2_HOURS = 3

            /**
             * 温度过高。持续扫描时，电池温度过高 >= 38℃
             */
            const val REASON_TEMPERATURE_HIGH = 4

            /**
             * 数量超限
             */
            const val REASON_EXCEEDING_MAX_COUNT = 5

            /**
             * 亮屏
             */
            const val REASON_SCREEN_ON = 6

            /**
             * 系统中断扫描
             */
            const val REASON_SYS_INTERRUPT = 7

            /**
             * 非充电省功耗
             */
            const val REASON_LOW_POWER_CONSUMPTION = 8

            /**
             * 上一次扫描时间是否在 1 个小时内
             */
            const val REASON_WITHIN_1_HOURS = 9

            /**
             * 应用处于前台
             */
            const val REASON_ON_TOP = 10

            /**
             * 扫描耗时超过20分钟
             */
            const val REASON_COST_TIME_20_MINUTES = 11

            /**
             * 通知栏中手动取消扫描
             */
            const val REASON_MANUAL_CANCEL = 12
        }
    }
}