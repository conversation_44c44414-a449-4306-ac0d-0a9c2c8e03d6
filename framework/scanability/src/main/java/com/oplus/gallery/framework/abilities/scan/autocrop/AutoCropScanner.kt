/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AutoCropScanner
 ** Description: 智能裁剪的扫描类，负责将图片逐一进行裁剪，并把结果存储到本地媒体库中
 **
 ** Version: 1.0
 ** Date: 2022/03/17
 ** Author: Yegua<PERSON><PERSON>@Apps.Gallery
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  Yeguangjin@Apps.Gallery3D  2022/03/17  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.autocrop

import android.content.Context
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.cvimageprocess.autocrop.CropRatio
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import java.lang.Integer.min

class AutoCropScanner(context: Context) : GalleryScan(context) {

    companion object {
        /** 更新智能裁剪数据版本，触发重扫 */
        const val AUTO_CROP_DATA_VERSION = 4
        private const val TAG = "${ScanConst.TAG}AutoCropScanner"
        private const val SCENE_NAME = "AutoCropScanner"
        private const val AUTO_CROP_SCAN_COUNT_24H_MAX = 5 * 1000 //5k
        private const val BATCH_COUNT = 50 //50一批次
    }

    private val dbOperation = AutoCropDBOperation()
    private val supportCropRatio = if (GProperty.DEBUG_REAL_TIME_AUTO_CROP) {
        arrayOf(CropRatio.RATIO_1_1, CropRatio.RATIO_16_9)
    } else {
        CropRatio.values()
    }

    override fun getScanType(): Int {
        return AUTO_CROP_SCAN
    }

    override fun getSceneName(): String = SCENE_NAME

    override fun onScan(triggerType: Int, config: ScanConfig) {
        GLog.w(TAG, LogFlag.DL, "onScan, start scan autoCrop triggerType = $triggerType")
        super.onScan(triggerType, config)
        scan()
        release()
        GLog.w(TAG, "onScan, autoCrop scan end")
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return false
    }

    private fun scan() {
        GLog.d(TAG, LogFlag.DL, "scan.")
        val time = System.currentTimeMillis()
        val notScanImageInfo = getScanData()
        val batchData = mutableListOf<BaseImageInfo>()
        markConditionsOnStartScan()
        readScanCountNotInCharging(GalleryScanUtils.AUTO_CROP_SCAN_COUNT_24H_KEY)
        while ((notScanImageInfo.size > 0) && isAllowContinueScan(AUTO_CROP_SCAN_COUNT_24H_MAX)) {
            /**
             * 开始批次扫描
             */
            val size = min(notScanImageInfo.size, BATCH_COUNT)
            batchData.addAll(notScanImageInfo.subList(0, size))
            notScanImageInfo.removeAll(batchData)
            batchScan(batchData)
            batchData.clear()
        }
        updateLastScanTime()
        track(null, false)
        GLog.d(TAG, LogFlag.DL, "scan. cost: ${(System.currentTimeMillis() - time)}")
    }

    private fun getScanData(): MutableList<BaseImageInfo> = dbOperation.queryAllImageWhichCanExistInPickedDay(AUTO_CROP_DATA_VERSION)

    /**
     * 批次扫描
     * @param imageInfoList
     */
    private fun batchScan(imageInfoList: MutableList<BaseImageInfo>) {
        val notScannedMediaList = GalleryScanProviderHelper.convertImageToMediaItem(ArrayList(imageInfoList))
        val cropRectMap = AutoCrop.getCropRects(mContext, notScannedMediaList, supportCropRatio)
        dbOperation.updateCropRect(cropRectMap, AUTO_CROP_DATA_VERSION)
        mScanImageCount += notScannedMediaList.size
        addScanCountIfNoCharging(GalleryScanUtils.AUTO_CROP_SCAN_COUNT_24H_KEY, notScannedMediaList.size)
        GLog.d(TAG, LogFlag.DL, "batchScan. total count: ${notScannedMediaList.size}, update: ${cropRectMap.size}, all: $mScanImageCount")
        bindSmallCore()
    }

    private fun release() {
        AutoCrop.release()
    }

    override fun onMonitorRecord(event: MonitorEvent, reachThreshold: Boolean) {
        super.onMonitorRecord(event, reachThreshold)
        markConditionsOnStartScan()
        track(event, true)
    }

    private fun track(event: MonitorEvent?, isMonitor: Boolean) {
        val reasonType = getReasonType(false, mScanCountNotInCharging, AUTO_CROP_SCAN_COUNT_24H_MAX)
        val endNativeHeapSize = getEndNativeHeapSize(event)
        val additionalMap = HashMap<String, String>()
        additionalMap[IS_MONITOR] = isMonitor.toString()
        val scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap)
        GLog.d(TAG, LogFlag.DL, "track, recordItem = $scanTrackInfo")
        ScanTrackHelper.trackScanResult(scanTrackInfo)
    }
}