/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceScanner.java
 ** Description:
 ** Version: 1.0
 ** Date: 2021/1/18
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** xiuhua.ke@Apps.Gallery3D   2016/11/22     1.0              build this module
 ** Dajian@Apps.Gallery3D      2021/1/18      1.1              modify
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.scan.face;

import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.os.Debug;

import com.oplus.breakpad.BreakpadMaster;
import com.oplus.gallery.basebiz.helper.RemoteModelInfoManager;
import com.oplus.gallery.business_lib.bean.BaseImageInfo;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster;
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo;
import com.oplus.gallery.business_lib.model.data.face.data.ImageFeature;
import com.oplus.gallery.business_lib.model.data.label.bean.FaceInfo;
import com.oplus.gallery.business_lib.model.data.utils.GalleryScanConstants;
import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.BatchReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil;
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil;
import com.oplus.gallery.framework.abilities.download.RemoteModelInfo;
import com.oplus.gallery.framework.abilities.download.template.ModelConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.face.CoverBgProcessor.CoverTask;
import com.oplus.gallery.framework.abilities.scan.face.model.FaceModelConfig;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackInfo;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanProgressCollector;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanPersonPetProviderHelper;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import androidx.annotation.NonNull;

import static com.oplus.gallery.business_lib.model.data.face.data.ImageFeatureKt.imageFeatureToCvFaceClustersList;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.FACE_SCAN_COUNT_24H_MAX;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.ReasonType.REASON_UNDEFINED;

public class FaceScanner extends GalleryScan {

    private static final String TAG = ScanConst.TAG + "FaceScanner";
    private static final String SCENE_NAME = "FaceScanner";
    private static final String SCENE_SCANNER_GROUP = SCENE_NAME + "_Group";
    private static final String SCENE_SCANNER_EMPTY = SCENE_NAME + "_Empty";
    private static final String SCENE_SCANNER_INIT_FAILED = SCENE_NAME + "_InitFailed";
    private static final String SCENE_SCANNER_GROUP_EMPTY = SCENE_NAME + "_GroupEmpty";
    private static final String SCENE_SCANNER_NO_FACE_TO_GROUP = SCENE_NAME + "_NoFaceToGroup";
    private static final String ENGINE_INIT_FAILED = "engine init failed";

    private static final String FACE_GROUP_NUMBER = "FaceGroupNumber";
    private static final String VIRTUAL_PATH_FOR_GROUP_ABORT_FILE = "/tag/face/group/AbortFile";
    private static final String GROUP_FACE_COUNT = "group_face_count";
    private static final String SCAN_FACE_COUNT = "scan_face_count";
    private static final int SET_THUMBNAIL_OF_FEATURE_NUM_MIN = 3;
    private static final int MOST_LIKELY_IMAGE_COUNT = 100;
    private static final Object mLock = new Object();
    private static final int ONE_SCAN_COUNT = 50;
    private static final int GROUP_FIRST_SHOW_NUM_MIN = 10;
    private CoverBgProcessor mCoverProcessor;
    private int mCurrentVersion = 0;
    private boolean mUpdateSuccess = false;
    private ArrayList<Long> mManualPersonIdList = new ArrayList<>();
    private HashMap<String, Integer> mAbortFile = new HashMap<>();
    private int mInvisibleFaceScanImageCount;
    private int mExcludeGroupTwoFaceCount;
    private ArrayList<String> mExcludeGroupTwoImageIdList = new ArrayList<>();
    private ArrayList<String> mExcludeGroupTwoVideoIdList = new ArrayList<>();
    private IFaceAnalyzer mCvFaceEngine;
    private IFaceScanAbility mFaceScanAbility;
    private ArrayList<MediaItem> mNewImage = new ArrayList<>();
    private ArrayList<MediaItem> mUpdateImage = new ArrayList<>();
    private ArrayList<MediaItem> mInvisibleFaceScanImage = new ArrayList<>();
    /**
     * 非充电时扫描的图片或视频个数
     */
    private int mFaceScanCountNotInCharging = 0;
    /**
     * 人脸扫描到的人脸个数
     */
    private int mScanFaceCount = 0;
    private long mStartGroupTime = 0;
    private float mStartGroupTemperature;
    private float mStartGroupBattery;
    private boolean mIsFirstGroup = false;

    public FaceScanner(Context context, GalleryScanProgressCollector mGalleryScanProgressCollector, boolean canScanWhenGalleryRunOnTop) {
        super(context);
        mFaceScanAbility = ((GalleryApplication) context.getApplicationContext()).getAppAbility(IFaceScanAbility.class);
        if (mFaceScanAbility != null) {
            mCvFaceEngine = mFaceScanAbility.newFaceAnalyzer(context);
        }
        super.mGalleryScanProgressCollector = mGalleryScanProgressCollector;
        super.mCanScanWhenGalleryRunOnTop = canScanWhenGalleryRunOnTop;
    }

    private boolean init() {
        //initialize Engine
        ModelConfig modelConfig = new FaceModelConfig();
        RemoteModelInfo remoteModelInfo = RemoteModelInfoManager.fetchCacheRemoteModelInfo(modelConfig.getModelName(), TAG);
        boolean result = mCvFaceEngine.init(false, modelConfig, remoteModelInfo);
        SharedPreferences sp = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME);
        mUpdateSuccess = sp.getBoolean(ComponentPrefUtils.HAS_FACE_DATA_UPDATE_SUCCESS_KEY, false);
        mCurrentVersion = sp.getInt(ComponentPrefUtils.FACE_COMPONENT_VERSION_KEY, 0);
        GLog.v(TAG, LogFlag.DL, "init, mCurrentVersion:" + mCurrentVersion + ", mUpdateSuccess:"
                + mUpdateSuccess);
        return result;
    }

    private void release() {
        //release Engine
        IOUtils.closeQuietly(mCvFaceEngine);
        IOUtils.closeQuietly(mFaceScanAbility);
    }

    private int loopDealItems(List<MediaItem> items, Function<List<MediaItem>, Boolean> function, boolean persist) {
        int count = 0;
        int size = items.size();
        List<MediaItem> list = new ArrayList<>();
        GLog.d(TAG, LogFlag.DL, "loopDealItems items' size = " + size);
        while ((size > 0) && isAllowContinueScan(mFaceScanCountNotInCharging, FACE_SCAN_COUNT_24H_MAX)) {
            count = 0;
            while (count < ONE_SCAN_COUNT) {
                synchronized (mLock) {
                    if (!items.isEmpty()) {
                        list.add(items.remove(0));
                        count++;
                    } else {
                        break;
                    }
                }
            }
            synchronized (mLock) {
                size = items.size();
            }
            function.apply(list);
            if (persist) {
                addScanCountIfNoCharging(list.size());
                recodeScanInfoIfNeed(list.size());
                bindSmallCore();
            }
            list.clear();
        }
        return size;
    }

    private void deltaScan(int scanTriggerType) {
        /************* closed by sandy 2021-9-30;
         * because face attribute detect is done with get face feature for new engine,
         * so a new item inserted into db will have attributes info, there will not appear item without face attributes.
         // do face attribute detect for remain data in face db.
         mHasFaceAttributeEngineInitialized = mFaceAttributeDetect.init(mContext);
         if (mHasFaceAttributeEngineInitialized) {
         doFaceAttributeScan(mContext);
         }
         *************/
        /*
        1.do update server faces first
        2.then deal the local faces
        */
        //STEP 1
        synchronized (mLock) {
            //noFace image MUST included
            mInvisibleFaceScanImage = GalleryScanPersonPetProviderHelper.getInvisibleFaceScannImageList(mContext);
            mInvisibleFaceScanImageCount = mInvisibleFaceScanImage.size();
        }
        boolean hasEngineInitialized = false;
        if (mInvisibleFaceScanImageCount > 0) {
            //update mInvisibleFaceScanImage MUST init FaceEngine
            hasEngineInitialized = init();
            if (!hasEngineInitialized) {
                GLog.w(TAG, "deltaScan init failed! mInvisibleFaceScanImage is not empty!!!");
                release();
                // 扫描中断，记录原因
                onInterruptScan(getScanType(), GalleryScanMonitor.ReasonType.REASON_SYS_INTERRUPT);
                ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_INIT_FAILED, scanTriggerType);
                return;
            }
        }
        long startInvisibleFaceScanTime = System.currentTimeMillis();
        mFaceScanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.FACE_SCAN_COUNT_24h_KEY, 0);
        synchronized (mLock) {
            loopDealItems(mInvisibleFaceScanImage, this::loopScanUpdateInvisibleFace, false);
        }
        GLog.d(TAG, "deltaScan mInvisibleFaceScanImage, time: " + (System.currentTimeMillis() - startInvisibleFaceScanTime) + "ms");

        //STEP 2
        if (!hasEngineInitialized) {
            hasEngineInitialized = init();
            if (!hasEngineInitialized) {
                GLog.w(TAG, "deltaScan init failed!");
                release();
                // 扫描中断，记录原因
                onInterruptScan(getScanType(), GalleryScanMonitor.ReasonType.REASON_SYS_INTERRUPT);
                ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_INIT_FAILED, scanTriggerType);
                return;
            }
        }
        if (mUpdateSuccess) {
            GalleryScanPersonPetProviderHelper.deleteInvalidFace(mCurrentVersion);
        }
        mIsFirstScan = !GalleryScanUtils.hadAllScan(mContext);
        GLog.d(TAG, "deltaScan mIsFirstScan: " + mIsFirstScan);
        prepareScanItems();
        //initialize count
        synchronized (mLock) {
            mNewCount = mNewImage.size();
            mUpdateCount = mUpdateImage.size();
        }
        mAllCount = mNewCount + mUpdateCount;
        GLog.d(TAG, "deltaScan, mNewCount = " + mNewCount + " mUpdateCount = " + mUpdateCount);
        if ((mAllCount == 0) && (!GalleryScanPersonPetProviderHelper.hadNoGroupFace()) && (mInvisibleFaceScanImageCount == 0)) {
            GLog.v(TAG, "deltaScan has no new image and update image, do not need to continue!");
            updateLastScanTime();
            release();
            // 扫描中断
            onInterruptScan(getScanType(), REASON_UNDEFINED);
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_EMPTY, scanTriggerType);
            return;
        }

        scanImage(scanTriggerType);
        group();
        // 扫描结束，扫描进度收集器监听为扫描完成
        completedScan(getScanType());
        if (mIsFirstScan) {
            GalleryScanUtils.setAllScanPref(mContext);
        }
        //set current time when finished
        if (GalleryScanMonitor.isAllowContinueScan(mContext, false, mCanScanWhenGalleryRunOnTop)) {
            updateLastScanTime();
            if (mUpdateSuccess) {
                // 前台扫描的场景不消费这个更新标记位，等到后台扫描才消费。
                if (!mCanScanWhenGalleryRunOnTop) {
                    ComponentPrefUtils.setBooleanPref(mContext, ComponentPrefUtils.HAS_FACE_DATA_UPDATE_SUCCESS_KEY, false);
                }
            }
        }
        release();
    }

    private void prepareScanItems() {
        if (mIsFirstScan || mUpdateSuccess) {
            getAllScanImage();
        } else {
            if (!GalleryScanUtils.hadUpdateFaceBestScore(mContext)) {
                checkUpdateFaceScoreImage();
            }
            filterIncreaseImage();
        }
    }

    private boolean addScanCountIfNoCharging(int count) {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true;
        }
        GalleryScanMonitor.refreshScanRecordingIfNecessary(mContext);
        mFaceScanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.FACE_SCAN_COUNT_24h_KEY, 0) + count;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.FACE_SCAN_COUNT_24h_KEY, mFaceScanCountNotInCharging);
        return false;
    }

    private void recodeScanInfoIfNeed(int count) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return;
        }
        int faceScanCountAll = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.FACE_SCAN_COUNT_ALL_KEY, 0) + count;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.FACE_SCAN_COUNT_ALL_KEY, faceScanCountAll);
        GalleryScanUtils.recordInfo(mContext, TAG, mFaceScanCountNotInCharging, faceScanCountAll);
    }

    private void scanImage(int scanTriggerType) {
        GLog.i(TAG, "scanImage start, scanTriggerType is  " + scanTriggerType);
        markConditionsOnStartScan();
        mScanImageCount = 0;
        mScanVideoCount = 0;
        //update abort file and get abort file list
        updateAbortFile();
        mAbortFile = GalleryScanProviderHelper.getAbortFile();
        synchronized (mLock) {
            mRemainedNewCount = loopDealItems(mNewImage, this::loopScanNew, true);
            loopDealItems(mUpdateImage, this::loopScanUpdate, true);
            GLog.d(TAG, LogFlag.DL, "scanImage: mRemainedNewCount = " + mRemainedNewCount
                    + " , mNewImage = " + mNewImage.size() + " , mUpdateImage = " + mUpdateImage.size());
        }
        //mCover task all push to queue, so interrupter thread when finished
        synchronized (this) {
            if (mCoverProcessor != null) {
                mCoverProcessor.existWhenFinished();
            }
        }
        boolean hasScanOver = mRemainedNewCount <= 0;
        ComponentPrefUtils.setBooleanPref(mContext, ComponentPrefUtils.HAS_FACE_SCAN_OVER_KEY, hasScanOver);
        trackFaceScan(null, false);
        GLog.i(TAG, "scanImage end now imageCount = " + mScanImageCount
                + ", time: " + GLog.getTime(mStartScanTime) + "ms");
    }

    private void updateAbortFile() {
        String prevAbortFile = GalleryScanUtils.readAbortFile(mContext);
        if ((prevAbortFile != null)
                && !prevAbortFile.trim().isEmpty()
                && !prevAbortFile.equals(VIRTUAL_PATH_FOR_GROUP_ABORT_FILE)
        ) {
            GalleryScanProviderHelper.updateAbortFile(prevAbortFile);
        }
    }

    private void group() {
        GLog.d(TAG, "group start");
        //run only really had face to group
        if ((mInvisibleFaceScanImageCount == 0) && !mUpdateSuccess && !GalleryScanPersonPetProviderHelper.hadNoGroupFace()) {
            GLog.w(TAG, "group, had no face to group!");
            // 扫描中断，记录原因
            onInterruptScan(getScanType(), REASON_UNDEFINED);
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_NO_FACE_TO_GROUP, mScanTriggerType);
            return;
        }
        ArrayList<CvFaceInfo> faceInfoList = GalleryScanPersonPetProviderHelper.getAllFaceWithFeature();
        if (!faceInfoList.isEmpty()) {
            mStartGroupTime = System.currentTimeMillis();
            mStartGroupBattery = BatteryStatusUtil.getCurrentBatteryPercent(mContext);
            mStartGroupTemperature = TemperatureUtil.getCurrentTemperature();
            mStartNativeHeapSize = Debug.getNativeHeapSize();

            if (isInterrupt() || (!GalleryScanMonitor.isAllowContinueScan(mContext, false, mCanScanWhenGalleryRunOnTop))) {
                GLog.w(TAG, "group, Scan task is interrupt!");
                // 扫描中断，记录原因
                onInterruptScan(getScanType(), REASON_UNDEFINED);
                trackFaceGroup(null, false);
                return;
            }

            //Reset special group id before re-group
            for (CvFaceInfo info : faceInfoList) {
                if ((info.getGroupId() > GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE)
                        || (info.getGroupId() == GalleryScanUtils.GROUP_ID_2)) {
                    info.setGroupId(GalleryScanUtils.GROUP_ID_0);
                }
            }
            GLog.d(TAG, "group, size: " + faceInfoList.size());
            /*
             * if old face is not manual, we need set new face's group_id to 0 to regroup when component updated on background.
             * 但是当扫描是前台触发的时候，为了避免重聚类导致groupID变化，进而导致前台数据集异常，所以前台扫描场景不触发重聚类
             */
            if ((mUpdateSuccess && !mCanScanWhenGalleryRunOnTop) || (mInvisibleFaceScanImageCount > 0)) {
                mManualPersonIdList = GalleryScanPersonPetProviderHelper.getAllManualPersonId();
                for (CvFaceInfo cvFaceInfo : faceInfoList) {
                    if (!mManualPersonIdList.contains(cvFaceInfo.getId())) {
                        cvFaceInfo.setGroupId(GalleryScanUtils.GROUP_ID_0);
                    }
                }
            }
            ArrayList<CvFaceInfo> updateList = new ArrayList<>();
            int maxGroup = 0;
            int size = faceInfoList.size();
            long time = System.currentTimeMillis();
            //set virtual path to distinguish with scan abort file
            BreakpadMaster.INSTANCE.setFilePath(VIRTUAL_PATH_FOR_GROUP_ABORT_FILE);
            int[] oldGroups = new int[size];
            for (int i = 0; i < size; i++) {
                CvFaceInfo face = faceInfoList.get(i);
                oldGroups[i] = (int) face.getGroupId();
            }
            int[] groups = mCvFaceEngine.groupFace(faceInfoList);
            if (groups != null) {
                //find the max group in engine
                int maxGroupInEngine = 0;
                for (int group : groups) {
                    maxGroupInEngine = Math.max(group, maxGroupInEngine);
                }

                //group which from server
                ArrayList<Integer> noFeatureGroupList = GalleryScanPersonPetProviderHelper.getAllNoFeatureGroupIds();
                //find the max group in no feature group
                int maxGroupInNoFeatureGroupList = 0;
                for (int temp : noFeatureGroupList) {
                    maxGroupInNoFeatureGroupList = Math.max(temp, maxGroupInNoFeatureGroupList);
                }

                //get the max group
                maxGroup = Math.max(maxGroupInEngine, maxGroupInNoFeatureGroupList);
                Map<Integer, Integer> changedGroupIdMap = new HashMap<>();
                for (int i = 0; i < groups.length; i++) {
                    int key = groups[i];
                    //changed to max,because of this id was used
                    if (noFeatureGroupList.contains(key)) {
                        if (!changedGroupIdMap.containsKey(key)) {
                            changedGroupIdMap.put(key, (int) ++maxGroup);
                        }
                        groups[i] = changedGroupIdMap.get(key);
                    }
                }

                GLog.d(TAG, "group, cluster-time: " + (System.currentTimeMillis() - time) + "ms");
                time = System.currentTimeMillis();
                ArrayList<CvFaceInfo> addGroupList = new ArrayList<>();
                // 获取对应 list数据
                getUpdateList(size, oldGroups, groups, addGroupList, updateList, faceInfoList);

                /*
                group 1,2: should be group
                changed to a virtual for single
                */
                if (!addGroupList.isEmpty()) {
                    // 存在多次人脸聚类，不能从固定值开始set，从数据库中获取最大值，保证GroupId唯一性
                    maxGroup = GalleryScanProviderHelper.queryMaxGroupIDForScanFaceTable();
                    if (maxGroup < GalleryScanConstants.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE) {
                        maxGroup = GalleryScanConstants.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE;
                    }
                    for (CvFaceInfo face : addGroupList) {
                        face.setGroupId(++maxGroup);
                    }
                }
            }

            //update provider
            if (!updateList.isEmpty()) {
                synchronized (GalleryScanDataManager.mGroupSyncLock) {
                    if (isInterrupt()) {
                        GLog.w(TAG, "group, Scan task is interrupt!");
                        // 扫描中断，记录原因
                        onInterruptScan(getScanType(), REASON_UNDEFINED);
                        trackFaceGroup(null, false);
                        return;
                    }
                    GalleryScanProviderHelper.batchUpdateFaceGroupId(updateList, mUpdateSuccess);
                }
            } else {
                GLog.w(TAG, "group, updateList is empty!");
            }
            //make face visible
            setScannedFaceVisible();
            setHasBigFaceState();
            // setScannedFaceVisible setHasBigFaceState 一起通知
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
            //set group state
            setGroupPrioWhenScanFinished();
            /*
            get best cover and set as default
            we should do this after provider has updated, otherwise getPreDefaultCover will become
            invalid because group has reset to 0 when components updating.
            */
            ArrayList<Integer> allGroupList = GalleryScanPersonPetProviderHelper.getAllGroupIds();
            if (allGroupList != null) {
                setDefaultCover(faceInfoList, allGroupList);
            }
            GLog.d(TAG, "group, update-time: " + (System.currentTimeMillis() - time) + "ms");
            if (GalleryScanUtils.isModelVerify()) {
                GLog.d(TAG, "group, faces to Json!");
                TagsJsonUtils.facesToJsonObject(mContext);
            }
        } else {
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_GROUP_EMPTY, mScanTriggerType);
            GLog.w(TAG, "group, groupList is empty!");
        }
        if (GalleryScanUtils.isModelRecodeScanInfo()) {
            GalleryScanUtils.recordInfo(mContext, FACE_GROUP_NUMBER, mExcludeGroupTwoImageIdList.size(), mExcludeGroupTwoFaceCount);
        }

        GLog.i(TAG, "group end, AllFaceImageCount:" + mExcludeGroupTwoImageIdList.size()
                + ",AllFaceCount:" + mExcludeGroupTwoFaceCount);
    }

    /**
     * 获取对应 list数据
     *
     * @param size         集合size
     * @param oldGroups    老的group id集合
     * @param groups       新的group id集合
     * @param addGroupList 需要在赋值的数据集合，结果
     * @param updateList   需要更新数据的集合，结果
     * @param faceInfoList 遍历数据
     */
    private void getUpdateList(int size, int[] oldGroups, int[] groups, ArrayList<CvFaceInfo> addGroupList, ArrayList<CvFaceInfo> updateList,
                               ArrayList<CvFaceInfo> faceInfoList) {
        for (int i = 0; i < size; i++) {
            CvFaceInfo face = faceInfoList.get(i);
            int oldGroup = oldGroups[i];
            int newGroup = groups[i];
            //changed
            if (oldGroup != newGroup) {
                if (oldGroup != GalleryScanUtils.GROUP_ID_0) {
                    //had group yet, but regroup...should reset?
                    GLog.w(TAG, "group id exist, but changed when regroup! oldGroup: " + oldGroup
                            + ", newGroup: " + newGroup);
                    // 老的oldGroup为自定义，且新的newGroup大于1，即由相册单张人脸状态增加多多张人脸状态，需要以newGroup为准
                    if (oldGroup >= GalleryScanConstants.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE
                            && newGroup > GalleryScanUtils.GROUP_ID_1) {
                        updateList.add(face);
                        computeFaceCountForTrack(newGroup, face);
                    }
                } else {
                    //group == GalleryScanUtils.GROUP_ID_2 will be hide
                    if (newGroup == GalleryScanUtils.GROUP_ID_1) {
                        addGroupList.add(face);
                    } else {
                        face.setGroupId(newGroup);
                    }
                    updateList.add(face);
                    computeFaceCountForTrack(newGroup, face);
                }
            }
        }
    }

    /**
     * compute face image count and face count for cloud statics
     *
     * @param newGroup
     * @param face
     */
    private void computeFaceCountForTrack(int newGroup, CvFaceInfo face) {
        if (newGroup != GalleryScanUtils.GROUP_ID_2) {
            mExcludeGroupTwoFaceCount++;
            if ((face.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
                    && !mExcludeGroupTwoImageIdList.contains(face.getData())) {
                mExcludeGroupTwoImageIdList.add(face.getData());
            }

            if ((face.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)
                    && !mExcludeGroupTwoVideoIdList.contains(face.getData())) {
                mExcludeGroupTwoVideoIdList.add(face.getData());
            }
        }
    }

    private void setScannedFaceVisible() {
        try {
            ContentValues cv = new ContentValues();
            cv.put(GalleryStore.ScanFaceColumns.FACE_VISIBLE, 1);
            String whereClause = GalleryStore.ScanFaceColumns.NO_FACE + " != 1"
                    + " AND " + GalleryStore.ScanFaceColumns.FACE_REMOVABLE + " != 1"
                    + " AND " + GalleryStore.ScanFaceColumns.FACE_VISIBLE + " != 1"
                    + " AND " + GalleryStore.ScanFaceColumns.SCAN_DATE + " IS NOT NULL";
            new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(whereClause)
                    .setConvert(aVoid -> cv).build().exec();
        } catch (Exception e) {
            GLog.w(TAG, "setScannedFaceVisible e = " + e);
        }
    }

    /**
     * 设置人物默认封面，主要通过sdk提供的getTopBestCoverIndex接口获取最优封面
     * 1.如果同时有视频和图片，不取视频
     * 2.如果同时有图片和gif，不取gif
     */
    @SuppressWarnings("ConstantConditions")
    private void setDefaultCover(ArrayList<CvFaceInfo> allUpdateList, ArrayList<Integer> allGroupList) {
        ArrayList<Integer> getBestCoverList = new ArrayList<>();
        for (int i = 0; i < allGroupList.size(); i++) {
            int groupId = allGroupList.get(i);
            try {
                if (getBestCoverList.contains(groupId)) {
                    continue;
                } else {
                    getBestCoverList.add(groupId);
                }

                /**
                 * sdk提供的接口已经整合美学、人数等等因素综合排序，取出来直接用就可以了
                 */
                int[] topIndex = mCvFaceEngine.getTopBestCoverIndex(groupId, MOST_LIKELY_IMAGE_COUNT);
                if ((topIndex == null) || (topIndex.length == 0)) {
                    continue;
                }

                boolean isMixedMediaType = false;
                boolean isMixedImageType = false;
                int videoCount = 0;
                int imageCount = 0;
                int gifCount = 0;

                for (int index : topIndex) {
                    if (index == -1) {
                        break;
                    }
                    CvFaceInfo faceInfo = allUpdateList.get(index);
                    if ((faceInfo == null) || (faceInfo.getData() == null)) {
                        break;
                    }
                    if (faceInfo.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
                        videoCount++;
                    } else if (faceInfo.getData().endsWith(".gif")) {
                        gifCount++;
                        imageCount++;
                    } else {
                        imageCount++;
                    }
                }
                if ((videoCount > 0) && (imageCount > 0)) {
                    isMixedMediaType = true;
                }
                if ((gifCount > 0) && (gifCount < imageCount)) {
                    isMixedImageType = true;
                }

                for (int index : topIndex) {
                    if ((index >= 0) && (index < allUpdateList.size())) {
                        CvFaceInfo faceInfo = allUpdateList.get(index);
                        if (faceInfo == null) {
                            continue;
                        }
                        // If this groupId contains both images and videos, ignore videos
                        if (isMixedMediaType && (faceInfo.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)) {
                            continue;
                        }
                        // If images in this groupId contains GIFs, ignore GIFs
                        if (isMixedImageType && (faceInfo.getData().endsWith(".gif"))) {
                            continue;
                        }
                        // 直接用该图作为默认封面，并且跳出循环
                        GalleryScanDataManager.getInstance().setDefaultCover(mContext, groupId, faceInfo.getId());
                        break;
                    }
                }
            } catch (Throwable throwable) {
                GLog.e(TAG, "setDefaultCover error:" + throwable);
            }
        }
    }

    private ArrayList<GroupInfo> setGroupPrioWhenScanFinished() {
        ArrayList<GroupInfo> list = GalleryScanPersonPetProviderHelper.getSpecialNumGroupInfo(0);
        ArrayList<Long> updateList = new ArrayList<>();
        mIsFirstGroup = GalleryScanUtils.isFirstGroup(mContext);
        int count = 0;
        if (mIsFirstGroup) {
            final int showGroupCount = GalleryScanPersonPetProviderHelper.getShowGroupCount();
            final int remainingCount = GROUP_FIRST_SHOW_NUM_MIN - showGroupCount;
            for (GroupInfo group : list) {
                if ((group.mNumberOfFace >= GalleryScanUtils.GROUP_NUM_FACE_THRESHOLD) || (count < remainingCount)) {
                    count++;
                    updateList.add(group.mGroupId);
                } else {
                    break;
                }
            }
        } else {
            for (GroupInfo group : list) {
                if (group.mNumberOfFace >= GalleryScanUtils.GROUP_NUM_FACE_THRESHOLD) {
                    updateList.add(group.mGroupId);
                }
            }
        }
        GLog.d(TAG, "setGroupPrioWhenScanFinished, updateGroup.size: " + updateList.size());
        //update group
        if (!updateList.isEmpty()) {
            GalleryScanPersonPetProviderHelper.setGroupShowEnable(updateList, true, false, 0);
        }

        //only really group will set this preference
        if (!list.isEmpty()) {
            if (mIsFirstGroup) {
                // set first group pref to false
                GalleryScanUtils.setFirstGroupPref(mContext);
            }
        }
        trackFaceGroup(null, false);
        return list;
    }

    /**
     * if group has big face, set has_big_face to true, then use this flag to set group show or hide.
     */
    private void setHasBigFaceState() {
        ArrayList<GroupInfo> list = GalleryScanPersonPetProviderHelper.getBigFaceGroupInfo();
        ArrayList<Long> groupIdList = new ArrayList<>();
        for (GroupInfo group : list) {
            groupIdList.add(group.mGroupId);
        }
        if (!groupIdList.isEmpty()) {
            GalleryScanPersonPetProviderHelper.setIsHasBigFaceState(groupIdList, true);
        }
    }

    private void getAllScanImage() {
        //collection image data
        GLog.d(TAG, "getAllScanImage");
        ArrayList<BaseImageInfo> images = new ArrayList<>();
        ArrayList<FaceInfo> imagesScanned = null;
        boolean resumeScan = false;
        if (isInterrupt()) {
            return;
        }
        GalleryScanPersonPetProviderHelper.deleteAllInvisibleFace();

        imagesScanned = GalleryScanPersonPetProviderHelper.getScannedItemsWithoutRecycled();

        if (!imagesScanned.isEmpty()) {
            BaseImageInfo imageInfo = imagesScanned.get(imagesScanned.size() - 1);
            //delete last one, we don't know last one is scan finished
            imagesScanned.remove(imageInfo);
            ArrayList<BaseImageInfo> list = new ArrayList<>();
            list.add(imageInfo);
            GalleryScanPersonPetProviderHelper.deleteImage(list);
            // deleteImage 通知
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
            if (isInterrupt()) {
                return;
            }
            //interrupt and continue to scan
            images = GalleryScanProviderHelper.getAllImageFromFileTable(mContext);
            resumeScan = true;
        }
        if (!resumeScan) {
            ArrayList<MediaItem> list = GalleryScanProviderHelper.getMediaItemFromMediaProvider(mContext);
            synchronized (mLock) {
                mNewImage = list;
            }
        } else {
            compareAndGetScanData(images, imagesScanned);
        }
    }

    private void filterIncreaseImage() {
        //collection image data
        GLog.d(TAG, "filterIncreaseImage");
        ArrayList<BaseImageInfo> images = null;
        ArrayList<FaceInfo> imagesFace = null;
        if (isInterrupt()) {
            return;
        }
        images = GalleryScanProviderHelper.getAllImageFromFileTable(mContext);
        if (isInterrupt()) {
            return;
        }
        imagesFace = GalleryScanPersonPetProviderHelper.getScannedItemsWithoutRecycled();
        compareAndGetScanData(images, imagesFace);
    }

    private void checkUpdateFaceScoreImage() {
        GLog.d(TAG, "checkUpdateFaceScoreImage");
        ArrayList<ImageFeature> imageFeatureArrayList = new ArrayList<>();
        Cursor cursor = null;
        try {
            long time = System.currentTimeMillis();
            String[] queryProject = new String[]{
                    GalleryStore.ScanFaceColumns._ID,
                    GalleryStore.ScanFaceColumns.DATA,
//                    GalleryStore.ScanFaceColumns.MEDIA_ID,
                    GalleryStore.ScanFaceColumns.THUMB_W,
                    GalleryStore.ScanFaceColumns.THUMB_H,
                    GalleryStore.ScanFaceColumns.SCORE,
                    GalleryStore.ScanFaceColumns.YAW,
                    GalleryStore.ScanFaceColumns.PITCH,
                    GalleryStore.ScanFaceColumns.LEFT,
                    GalleryStore.ScanFaceColumns.TOP,
                    GalleryStore.ScanFaceColumns.RIGHT,
                    GalleryStore.ScanFaceColumns.BOTTOM,
                    GalleryStore.ScanFaceColumns.BEST_SCORE};
            String whereClause = GalleryStore.ScanFaceColumns.NO_FACE + " != 1 " + " AND "
                    + GalleryStore.ScanFaceColumns.FACE_REMOVABLE + " != 1 " + " AND "
                    + GalleryStore.ScanFaceColumns.FACE_VISIBLE + " == 1 " + " AND "
                    + GalleryStore.ScanFaceColumns.LUMINANCE + " is null " + " AND "
                    + GalleryStore.ScanFaceColumns.BEST_SCORE + " > 0 " + " AND "
                    + GalleryStore.ScanFaceColumns.GROUP_ID + " > 0 ";
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setProjection(queryProject)
                    .setWhere(whereClause)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            int updateCount = ((cursor == null) ? 0 : cursor.getCount());
            GLog.d(TAG, "checkUpdateFaceScoreImage, update face size:" + updateCount);
            HashMap<String, Integer> faceCountMap = new HashMap<>();
            if ((cursor != null) && (updateCount > 0)) {
                while (cursor.moveToNext()) {
                    ImageFeature feature = new ImageFeature();
                    feature.parseCursor(cursor);
                    imageFeatureArrayList.add(feature);
                    if (faceCountMap.containsKey(feature.getFilePath())) {
                        int num = faceCountMap.get(feature.getFilePath());
                        faceCountMap.put(feature.getFilePath(), num + 1);
                    } else {
                        faceCountMap.put(feature.getFilePath(), 1);
                    }
                }
            }
            if (imageFeatureArrayList.isEmpty()) {
                GLog.w(TAG, "checkUpdateFaceScoreImage, no need update, return");
                return;
            }
            List<UpdateReq> operations = new ArrayList<>();
            for (ImageFeature feature : imageFeatureArrayList) {
                int faceCount = faceCountMap.get(feature.getFilePath());
                UpdateReq updateReq = new UpdateReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                        .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                        .setConvert(aVoid -> {
                            ContentValues value = new ContentValues();
                            value.put(GalleryStore.ScanFaceColumns.IS_SINGLE_FACE, (faceCount == 1) ? "1" : "0");
                            return value;
                        })
                        .setWhere(GalleryStore.ScanFaceColumns.DATA + " = ? ")
                        .setWhareArgs(new String[]{String.valueOf(feature.getFilePath())})
                        .build();
                operations.add(updateReq);
            }
            BatchResult[] results = new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build().exec();
            if (!imageFeatureArrayList.isEmpty()) {
                UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
            }
            int result = results.length;
            GLog.d(TAG, "checkUpdateFaceScoreImage, result: " + result);
            GLog.d(TAG, " checkUpdateFaceScoreImage cost:" + (System.currentTimeMillis() - time));
            if (result == updateCount) {
                GalleryScanUtils.setUpdateFaceBestScore(mContext);
            }
        } catch (Exception e) {
            GLog.e(TAG, "checkUpdateFaceScoreImage, error:" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
    }

    private void compareAndGetScanData(@NotNull ArrayList<BaseImageInfo> imagesInLocal, @NotNull ArrayList<FaceInfo> imagesScanned) {
        GLog.d(TAG, "compareAndGetScanData, imagesInLocal.size: " + imagesInLocal.size() + ", imagesScanned.size: " + imagesScanned.size());
        long time = System.currentTimeMillis();
        GalleryScanPersonPetProviderHelper.deleteAllInvisibleFace();
        HashMap<String, BaseImageInfo> localImagePathMap = GalleryScanDataManager.translateListToPathMap(imagesInLocal);
        ArrayList<BaseImageInfo> deleteImage = new ArrayList<>();
        ArrayList<BaseImageInfo> updateImageInvalid = new ArrayList<>();
        ArrayList<BaseImageInfo> updateImageInvalidWithMediaId = new ArrayList<>();
        for (BaseImageInfo image : imagesScanned) {
            BaseImageInfo im = localImagePathMap.get(image.mFilePath.toLowerCase());
            if (im == null) {
                deleteImage.add(image);
            } else {
                final boolean invalidDiff = (im.mInvalid != image.mInvalid);
                final boolean mediaIdDiff = (im.mMediaId != image.mMediaId);
                if (invalidDiff || mediaIdDiff) {
                    image.mInvalid = im.mInvalid;
                    image.mMediaId = im.mMediaId;
                    if (mediaIdDiff) {
                        updateImageInvalidWithMediaId.add(image);
                    } else {
                        updateImageInvalid.add(image);
                    }
                }
            }
        }
        GalleryScanPersonPetProviderHelper.deleteImage(deleteImage);
        GalleryScanPersonPetProviderHelper.updateImageInvalid(updateImageInvalid);
        GalleryScanPersonPetProviderHelper.updateImageInvalidWithMediaId(updateImageInvalidWithMediaId);
        GLog.d(TAG, "compareAndGetScanData, deleteImage.size: " + deleteImage.size()
                + ", updateImage.size: " + (updateImageInvalid.size() + updateImageInvalidWithMediaId.size()));
        if (!deleteImage.isEmpty() || !updateImageInvalid.isEmpty() || !updateImageInvalidWithMediaId.isEmpty()) {
            // deleteImage、updateImageInvalid、updateImageInvalidWithMediaId 合并通知
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
        }
        HashMap<String, FaceInfo> scannedImageMap = GalleryScanDataManager.translateListToPathMap(imagesScanned);
        if (!deleteImage.isEmpty()) {
            for (BaseImageInfo image : deleteImage) {
                scannedImageMap.remove(image.mFilePath.toLowerCase());
            }
        }
        // 对比来自local_media和scan_face中的数据，区分出新增和更新的图片
        ArrayList<BaseImageInfo> newImage = new ArrayList<>();
        ArrayList<BaseImageInfo> updateImage = new ArrayList<>();
        for (BaseImageInfo image : imagesInLocal) {
            if (!image.mInvalid) {
                FaceInfo faceInfo = scannedImageMap.get(image.mFilePath.toLowerCase());
                if (faceInfo == null) {
                    newImage.add(image);
                } else if (faceInfo.getModelVersion() != mCurrentVersion) {
                    updateImage.add(image);
                }
            }
        }
        ArrayList<MediaItem> newList = GalleryScanProviderHelper.convertImageToMediaItem(newImage);
        ArrayList<MediaItem> updateList = GalleryScanProviderHelper.convertImageToMediaItem(updateImage);
        synchronized (mLock) {
            mNewImage = newList;
            mUpdateImage = updateList;
        }
        GLog.d(TAG, "compareAndGetScanData, time: " + (System.currentTimeMillis() - time) + "ms"
                + " newList.size:" + newList.size() + " updateList.size:" + updateList.size());
    }

    /**
     * FIXME: face scan, once (see@ONE_SCAN_COUNT)
     */
    private boolean loopScanNew(List<MediaItem> list) {
        if ((list == null) || list.isEmpty()) {
            return false;
        }
        GLog.d(TAG, "loopScanNew");
        long time = System.currentTimeMillis();
        List<CvFaceCluster> insertList = new ArrayList<>();
        list.forEach(item -> {
            if (item instanceof LocalImage) {
                mScanImageCount++;
            } else if (item instanceof LocalVideo) {
                mScanVideoCount++;
            }
            calculateScanFormatCount(item);
            String filePath = item.getFilePath();
            if (mAbortFile.containsKey(filePath)) {
                GLog.w(TAG, "loopScanNew, path: " + item.getPath() + ",  this file is abort file!");
                return;
            }
            BreakpadMaster.INSTANCE.setFilePath(filePath);
            List<CvFaceCluster> newFaceInfoList = getCvFaceClusters(item);
            // 计算进度并通知
            updateProgress(getScanType());
            if ((newFaceInfoList == null)) {
                return;
            }
            insertList.addAll(newFaceInfoList);

        });
        insertData(insertList);
        GLog.d(TAG, "loopScanNew, time: " + (System.currentTimeMillis() - time) + "ms");
        return true;
    }

    private List<CvFaceCluster> getCvFaceClusters(MediaItem item) {
        ImageFeature[] imageFeatures = null;
        Bitmap thumbnail = null;
        if (item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
            thumbnail = getThumbnail(item);
            if (thumbnail != null) {
                GLog.d(TAG, "getCvFaceClusters, image.path = " + item.getPath()
                        + ", width=" + thumbnail.getWidth() + ", height=" + thumbnail.getHeight());
                imageFeatures = mCvFaceEngine.scanImageFeatures(item, thumbnail, item.getRotation());
            } else {
                GLog.w(TAG, "getCvFaceClusters, thumbnail: null, LocalImage: " + item);
                return null;
            }
        } else if ((item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)
                && (isVideoAllowScan((LocalVideo) item))) {
            imageFeatures = mCvFaceEngine.scanVideoFeatures(item.getMediaId(), item.getFilePath());
        }
        // 特征为null时，是检测时发生了crash，为了防止偶发的crash导致用户文件一直不能扫描出来，先不要标记已经扫描，下次重试
        if (imageFeatures == null) {
            GLog.w(TAG, LogFlag.DL, "getCvFaceClusters, imageFeature is null, LocalImage: " + item);
            return null;
        }
        if (GalleryScanUtils.isTestMode()) {
            GLog.i(TAG, "getCvFaceClusters, imageFeature: " + imageFeatures.length);
            GLog.i(TAG, "getCvFaceClusters, filePath-end: " + item.getFilePath());
        }
        ArrayList<CvFaceCluster> newFaceInfoList = new ArrayList<>();
        if (imageFeatures.length > 0) {
            newFaceInfoList.addAll(imageFeatureToCvFaceClustersList(imageFeatures, item, thumbnail, mCurrentVersion, false));
            mScanFaceCount++;
            /*
            扫描过的图片/视频也记录到数据库中，不然会重复扫描，
            对于视频，如果长度过小或者过大不允许扫描，直接写入数据库中，下次就不扫这个视频了
            如果满足扫描条件，认为扫描过了，也直接写入数据库中
             */
        } else if ((item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
                || ((item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)
                && (!isVideoDurationSupportScan((LocalVideo) item) || isVideoAllowScan((LocalVideo) item)))
        ) {
            CvFaceCluster cluster = new CvFaceCluster();
            cluster.setImageInfo(item, mCurrentVersion);
            cluster.setNoFace(true);
            newFaceInfoList.add(cluster);
        }
        return newFaceInfoList;
    }

    private boolean loopScanUpdate(List<MediaItem> list) {
        if ((list == null) || list.isEmpty()) {
            return false;
        }
        GLog.d(TAG, "loopScanUpdate");
        long time = System.currentTimeMillis();
        //get feature
        HashMap<String, List<CvFaceCluster>> dataMap = new HashMap<>();
        for (MediaItem item : list) {
            if (item instanceof LocalImage) {
                mScanImageCount++;
            } else if (item instanceof LocalVideo) {
                mScanVideoCount++;
            }
            calculateScanFormatCount(item);
            String filePath = item.getFilePath();
            if (mAbortFile.containsKey(filePath)) {
                GLog.w(TAG, "loopScanUpdate, path: " + item.getPath() + ",  this file is abort file!");
                continue;
            }
            BreakpadMaster.INSTANCE.setFilePath(filePath);
            List<CvFaceCluster> newFaceInfoList = getCvFaceClusters(item);
            // 计算进度并通知
            updateProgress(getScanType());
            dataMap.put(filePath, newFaceInfoList);
        }
        updateData(dataMap);
        GLog.d(TAG, "loopScanUpdate, time: " + (System.currentTimeMillis() - time) + "ms");
        return true;
    }

    /**
     * Scan for update invisible Face(particular no Face image MUST included)
     */
    private boolean loopScanUpdateInvisibleFace(List<MediaItem> list) {
        GLog.d(TAG, "loopScanUpdateInvisibleFace");
        long time = System.currentTimeMillis();
        Map<Long, ContentValues> mapUpdateFaceList = new HashMap<>();
        ArrayList<String> filePathList = new ArrayList<>();
        for (MediaItem item : list) {
            filePathList.add(item.getFilePath());
        }
        //noFace image MUST included
        HashMap<String, ArrayList<CvFaceInfo>> mapFaceList =
                GalleryScanPersonPetProviderHelper.getInvisibleFaceByPath(mContext, filePathList, false);
        for (MediaItem item : list) {
            String filePath = item.getFilePath();
            if (mAbortFile.containsKey(filePath)) {
                GLog.w(TAG, "loopScanUpdateInvisibleFace, path: " + item.getPath() + ",  this file is abort file!");
                continue;
            }
            BreakpadMaster.INSTANCE.setFilePath(item.getFilePath());
            //getImageFeature
            ImageFeature[] imageFeature = null;
            if (GalleryScanUtils.isTestMode()) {
                GLog.i(TAG, "loopScanUpdateInvisibleFace, path: " + item.getPath());
            }
            if (item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
                Bitmap thumbnail = getThumbnail(item);
                if (thumbnail != null) {
                    imageFeature = mCvFaceEngine.scanImageFeatures(item, thumbnail, item.getRotation());
                } else {
                    GLog.w(TAG, "loopScanUpdateInvisibleFace, thumbnail is null, LocalImage: " + item);
                    continue;
                }
            } else {
                if (isVideoAllowScan((LocalVideo) item)) {
                    imageFeature = mCvFaceEngine.scanVideoFeatures((long) item.getMediaId(), filePath);
                }
            }
            if (imageFeature == null) {
                GLog.w(TAG, LogFlag.DL, "loopScanUpdateInvisibleFace, imageFeature is null, LocalImage: " + item);
                continue;
            }
            if (GalleryScanUtils.isTestMode()) {
                GLog.i(TAG, "loopScanUpdateInvisibleFace, filePath-end: " + filePath);
            }
            GLog.d(TAG, "loopScanUpdateInvisibleFace, filePath" + filePath);
            long faceScanDate = System.currentTimeMillis();
            ArrayList<CvFaceInfo> faceList = mapFaceList.get(filePath);
            if ((faceList != null) && (!faceList.isEmpty())) {
                if (imageFeature.length > 0) {
                    for (ImageFeature feature : imageFeature) {
                        GLog.d(TAG, null, () -> "loopScanUpdateInvisibleFace, feature.mThumbWidth = "
                                + feature.getThumbWidth() + ", feature.mThumbHeight = " + feature.getThumbHeight()
                                + ", feature.faceRect = " + feature.getFaceInfo().getFaceRect());
                        Iterator<CvFaceInfo> faceIterator = faceList.iterator();
                        while (faceIterator.hasNext()) {
                            CvFaceInfo cvFaceInfo = faceIterator.next();
                            GLog.d(TAG, null, () -> "loopScanUpdateInvisibleFace, cvFaceInfo.mThumbWidth = "
                                    + cvFaceInfo.getThumbWidth() + ", cvFaceInfo.mThumbHeight = "
                                    + cvFaceInfo.getThumbHeight() + ", cvFaceInfo.mRect=" + cvFaceInfo.getRect());
                            boolean isSame = CvFaceCluster.isSameFace(feature.getThumbWidth(), feature.getThumbHeight(),
                                    feature.getFaceInfo().getFaceRect(), cvFaceInfo.getThumbWidth(),
                                    cvFaceInfo.getThumbHeight(), cvFaceInfo.getRect());
                            GLog.d(TAG, null, () -> "loopScanUpdateInvisibleFace, isSame=" + isSame);
                            if (isSame) {
                                ContentValues contentValues = buildServerImageContentValues(item, faceScanDate);
                                contentValues.put(GalleryStore.ScanFaceColumns.SCORE, feature.getFaceInfo().getScore());
                                contentValues.put(GalleryStore.ScanFaceColumns.YAW, feature.getFaceInfo().getYaw());
                                contentValues.put(GalleryStore.ScanFaceColumns.PITCH, feature.getFaceInfo().getPitch());
                                contentValues.put(GalleryStore.ScanFaceColumns.ROLL, feature.getFaceInfo().getRoll());
                                contentValues.put(GalleryStore.ScanFaceColumns.EYE_DIST, feature.getFaceInfo().getEyeDist());
                                contentValues.put(GalleryStore.ScanFaceColumns.FEATURE, feature.getFeature());
                                contentValues.put(GalleryStore.ScanFaceColumns.BEST_SCORE, feature.getBestScore());
                                mapUpdateFaceList.put(cvFaceInfo.getId(), contentValues);
                                faceIterator.remove();
                                //Not break for dirty face consideration,leon.chen add 20171031;
                            }
                        }
                    }
                }
                if (!faceList.isEmpty()) {
                    for (CvFaceInfo cvFaceInfo : faceList) {
                        GLog.d(TAG, null, () -> "loopScanUpdateInvisibleFace, personId=" + cvFaceInfo.getId());
                        ContentValues contentValues = buildServerImageContentValues(item, faceScanDate);
                        mapUpdateFaceList.put(cvFaceInfo.getId(), contentValues);
                    }
                }
            }
        }
        //update face feature and other data
        if (!mapUpdateFaceList.isEmpty()) {
            List<UpdateReq> operations = new ArrayList<>();
            for (Map.Entry<Long, ContentValues> entry : mapUpdateFaceList.entrySet()) {
                UpdateReq updateReq = new UpdateReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                        .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                        .setConvert(aVoid -> entry.getValue())
                        .setWhere(GalleryStore.ScanFaceColumns._ID + " = ?")
                        .setWhareArgs(new String[]{String.valueOf(entry.getKey())})
                        .build();
                operations.add(updateReq);
            }
            try {
                GLog.d(TAG, "loopScanUpdateInvisibleFace, operations.size()=" + operations.size());
                new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                        .addDataReqs(operations)
                        .build().exec();
                UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
            } catch (Exception e) {
                GLog.w(TAG, e);
            }
        }
        GLog.d(TAG, "loopScanUpdateInvisibleFace, time: " + (System.currentTimeMillis() - time) + "ms");
        return true;
    }

    private ContentValues buildServerImageContentValues(MediaItem item, long faceScanDate) {
        ContentValues values = new ContentValues();
        //sepcial values
        values.put(GalleryStore.ScanFaceColumns.INVALID, 0);
        //media values
        if (item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
            values.put(GalleryStore.ScanFaceColumns.MEDIA_TYPE, GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE);
        } else if (item.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
            values.put(GalleryStore.ScanFaceColumns.MEDIA_TYPE, GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO);
        }
        if (item instanceof LocalMediaItem) {
            values.put(GalleryStore.ScanFace.IS_RECYCLED, item.isRecycledItem());
        }
        //other values
        values.put(GalleryStore.ScanFaceColumns.SCAN_DATE, faceScanDate);
        values.put(GalleryStore.ScanFaceColumns.GROUP_DATE, faceScanDate);
        values.put(GalleryStore.ScanFaceColumns.MODEL_VERSION, mCurrentVersion);
        return values;
    }

    private void insertData(List<CvFaceCluster> insertList) {
        if ((insertList == null) || insertList.isEmpty()) {
            GLog.w(TAG, "insertData, insetList is empty!");
            return;
        }
        long time = System.currentTimeMillis();
        try {
            BatchResult[] result = GalleryScanProviderHelper.insertFaceInfoList(insertList);
            if (result == null) {
                GLog.d(TAG, null, () -> "insertData failed, time: " + (System.currentTimeMillis() - time) + "ms");
                return;
            }
            //trigger cover task
            for (int i = 0; i < insertList.size(); i++) {
                CvFaceCluster cluster = (CvFaceCluster) insertList.get(i);
                if (cluster.getMediaItemRef() != null) {
                    cluster.setId(ContentUris.parseId(result[i].getUri()));
                    triggerCoverTask(cluster.getMediaItemRef(), cluster, cluster.getThumbnail());
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
        }
        GLog.d(TAG, null, () -> "insertData, time: " + (System.currentTimeMillis() - time) + "ms");
    }

    private void updateData(HashMap<String, List<CvFaceCluster>> dataMap) {
        List<CvFaceCluster> updateFaceInfoList = new ArrayList<>();
        List<Long> deletePersonIdList = new ArrayList<>();
        List<CvFaceCluster> insertFaceInfoList = new ArrayList<>();
        for (Map.Entry<String, List<CvFaceCluster>> entry : dataMap.entrySet()) {
            String filePath = entry.getKey();
            List<CvFaceCluster> newFaceInfoList = entry.getValue();
            if ((newFaceInfoList == null) || newFaceInfoList.isEmpty()) {
                GLog.w(TAG, "updateData, newFaceInfoList is empty!");
                continue;
            }
            insertFaceInfoList.addAll(newFaceInfoList);
            List<CvFaceInfo> oldFaceInfoList = GalleryScanPersonPetProviderHelper.getFaceInfoByFilePath(filePath);
            for (CvFaceInfo oldFaceInfo : oldFaceInfoList) {
                long oldId = oldFaceInfo.getId();
                Rect oldRect = oldFaceInfo.getRect();
                boolean isFaceMatched = false;
                for (CvFaceCluster newFaceInfo : newFaceInfoList) {
                    Rect newRect = newFaceInfo.getRect();
                    Rect rectWithoutForehead = newFaceInfo.getRectWithoutForehead();
                    boolean isSameFace = CvFaceCluster.isSameFace(oldFaceInfo.getThumbWidth(),
                            oldFaceInfo.getThumbHeight(), oldRect, newFaceInfo.getThumbWidth(), newFaceInfo.getThumbHeight(), newRect);
                    // 版本15之前的人脸框数据不带额头，之后的人脸框数据带额头，判断是否相同人脸需要补充用不带额头的人脸框数据和旧数据匹配
                    if (!isSameFace && !newRect.equals(rectWithoutForehead)) {
                        isSameFace = CvFaceCluster.isSameFace(oldFaceInfo.getThumbWidth(),
                                oldFaceInfo.getThumbHeight(),
                                oldRect,
                                newFaceInfo.getThumbWidth(),
                                newFaceInfo.getThumbHeight(),
                                rectWithoutForehead);
                        GLog.v(TAG, "updateData: rectWithoutForehead:" + rectWithoutForehead);
                    }
                    boolean finalIsSameFace = isSameFace;
                    GLog.v(TAG, null, () -> "updateData, oldRect:" + oldRect + ", oldThumbWidth = "
                            + oldFaceInfo.getThumbWidth() + ", oldThumbHeight = " + oldFaceInfo.getThumbHeight()
                            + ", newRect:" + newRect + ", newThumbWidth = " + newFaceInfo.getThumbWidth()
                            + ", newThumbHeight = " + newFaceInfo.getThumbHeight() + ", isSameFace:" + finalIsSameFace);
                    if (isSameFace) {
                        newFaceInfo.setId(oldId);
                        updateFaceInfoList.add(newFaceInfo);
                        isFaceMatched = true;
                        break;
                    }
                }
                if (!isFaceMatched) {
                    deletePersonIdList.add(oldId);
                }
            }
        }
        insertFaceInfoList.removeAll(updateFaceInfoList);
        GLog.d(TAG, LogFlag.DL, "updateData, updateFaceInfoList size:" + updateFaceInfoList.size()
                + ",deletePersonIdList size:" + deletePersonIdList.size()
                + ",insertFaceInfoList size:" + insertFaceInfoList.size());
        //1.If old face rect can match new face rect, update face with new info
        GalleryScanProviderHelper.updateFaceInfoList(updateFaceInfoList);
        //2.If old face rect can not match, delete from db
        GalleryScanPersonPetProviderHelper.deletePersonId(deletePersonIdList);

        //3.If new face rect can not match, insert to db
        insertData(insertFaceInfoList);
    }

    /**
     * FIXME: trigger cover task
     */
    private void triggerCoverTask(MediaItem image, CvFaceCluster cluster, Bitmap thumbnail) {
        CoverTask cover = new CoverTask();
        cover.mPersonId = cluster.getId();
        cover.mThumbW = cluster.getThumbWidth();
        cover.mThumbH = cluster.getThumbHeight();
        cover.mFaceRect = new Rect(cluster.getRectLeft(), cluster.getRectTop(), cluster.getRectRight(), cluster.getRectBottom());
        cover.mMediaItem = image;
        cover.mThumbnail = thumbnail;
        synchronized (this) {
            if (mCoverProcessor == null) {
                mCoverProcessor = new CoverBgProcessor();
            }
            mCoverProcessor.addTask(cover);
        }
    }

    @Override
    public int getScanType() {
        return GalleryScan.FACE_SCAN;
    }

    @Override
    public String getSceneName() {
        return SCENE_NAME;
    }

    @Override
    public void onScan(int triggerType, ScanConfig config) {
        GLog.w(TAG, "onScan, start face scan triggerType =" + triggerType);
        super.onScan(triggerType, config);
        //功耗优化，非充电模式下如果有人脸数据则不进行扫描, 前端业务发起的扫描不在此中断
        if (isLowPowerConsumptionStopScan() && !mCanScanWhenGalleryRunOnTop) {
            // 停止扫描
            trackStopFaceScan(GalleryScanMonitor.ReasonType.REASON_LOW_POWER_CONSUMPTION);
            GLog.d(TAG, "onScan, run in low power consumption mode, no need to scan");
            return;
        }
        deltaScan(triggerType);
        GalleryScanMonitor.refreshScanWentWellFlag(mContext, GalleryScanUtils.PREF_FACE_SCAN_WENT_WELL_KEY, isScanWentWell());
        GLog.w(TAG, "onScan, face scan end");
    }

    @Override
    public boolean isScanWentWell() {
        if (mRemainedNewCount == 0) {
            return true;
        }
        if ((System.currentTimeMillis() - mStartTime) > GalleryScan.SCAN_WENT_WELL_COST_TIME) {
            return true;
        }
        int scanCount = mNewCount - mRemainedNewCount;
        GLog.d(TAG, null, () -> "isScanWentWell, mNewCount=" + mNewCount + " mRemainedNewCount=" + mRemainedNewCount);
        return (scanCount > FACE_SCAN_COUNT_24H_MAX) || (scanCount * GalleryScan.MULTIPLE_3 > mNewCount);
    }

    @Override
    public void onMonitorRecord(@NonNull MonitorEvent event, boolean reachThreshold) {
        super.onMonitorRecord(event, reachThreshold);
        markConditionsOnStartScan();
        GLog.d(TAG, null, () -> "onMonitorRecord, event = " + event.toString() + ", reachThreshold = " + reachThreshold);
        if (mStartGroupTime > 0) {
            trackFaceGroup(event, true);
        } else {
            trackFaceScan(event, true);
        }
    }

    @Override
    protected boolean runTaskIsNeedCharging() {
        return false;
    }

    private boolean isLowPowerConsumptionStopScan() {
        if (!isCloudAllowLowConsumptionScan()) {
            GLog.d(TAG, "inLowPowerConsumptionScan cloud disable");
            return false;
        }
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            GLog.d(TAG, "inLowPowerConsumptionScan battery charging");
            return false;
        }
        ArrayList<FaceInfo> result = GalleryScanPersonPetProviderHelper.getScannedItemsWithoutRecycled();
        GLog.d(TAG, null, () -> "inLowPowerConsumptionScan: result=" + ((result != null) ? result.size() : -1));
        return (result != null) && !result.isEmpty();
    }

    private void trackFaceScan(MonitorEvent event, boolean isMonitor) {
        int reasonType = getReasonType(false, mFaceScanCountNotInCharging, FACE_SCAN_COUNT_24H_MAX);
        long endNativeHeapSize = getEndNativeHeapSize(event);
        Map<String, String> additionalMap = new HashMap<>();
        additionalMap.put(SCAN_FACE_COUNT, String.valueOf(mScanFaceCount));
        additionalMap.put(SCAN_FORMAT_COUNT, mScanFormatCount.toString());
        additionalMap.put(IS_MONITOR, String.valueOf(isMonitor));
        ScanTrackInfo scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap);
        GLog.d(TAG, null, () -> "trackFaceScan, recordItem = " + scanTrackInfo.toString());
        ScanTrackHelper.trackScanResult(scanTrackInfo);
    }

    private void trackStopFaceScan(@GalleryScanMonitor.ReasonType int reason) {
        markConditionsOnStartScan();
        long endNativeHeapSize = getEndNativeHeapSize(null);
        ScanTrackInfo scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reason, null);
        ScanTrackHelper.trackScanResult(scanTrackInfo);
    }

    private void trackFaceGroup(MonitorEvent event, boolean isMonitor) {
        int reasonType = getReasonType(false, mFaceScanCountNotInCharging, FACE_SCAN_COUNT_24H_MAX);
        long endNativeHeapSize = getEndNativeHeapSize(event);
        Map<String, String> additionalMap = new HashMap<>();
        additionalMap.put(GROUP_FACE_COUNT, String.valueOf(mExcludeGroupTwoFaceCount));
        additionalMap.put(IS_MONITOR, String.valueOf(isMonitor));
        ScanTrackInfo scanTrackInfo = new ScanTrackInfo(
                SCENE_SCANNER_GROUP,
                mStartGroupTime,
                System.currentTimeMillis(),
                mStartGroupBattery,
                BatteryStatusUtil.getCurrentBatteryPercent(mContext),
                mIsFirstGroup,
                mScanTriggerType,
                mExcludeGroupTwoImageIdList.size(),
                mExcludeGroupTwoVideoIdList.size(),
                mStartGroupTemperature,
                TemperatureUtil.getCurrentTemperature(),
                mStartNativeHeapSize,
                endNativeHeapSize,
                reasonType,
                BatteryStatusUtil.isBatteryInCharging(false),
                additionalMap);
        GLog.d(TAG, null, () -> "trackFaceGroup, recordItem = " + scanTrackInfo.toString());
        ScanTrackHelper.trackScanResult(scanTrackInfo);
    }
}