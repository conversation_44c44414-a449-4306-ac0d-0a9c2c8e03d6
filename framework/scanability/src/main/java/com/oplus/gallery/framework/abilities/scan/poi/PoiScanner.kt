/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - PoiScanner
 ** Description:
 ** Version: 1.0
 ** Date : 2025/3/28
 ** Author: qiupeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** 80398228 2025/3/28 1.0 build this module
 ****************************************************************/
package com.oplus.gallery.framework.abilities.scan.poi

import android.content.Context
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.location.service.PoiUpdateJob
import com.oplus.gallery.business_lib.model.data.location.utils.PoiConfigFileParser
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.download.utils.ConfigChecker
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.PoiModelConfig
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * POI地址扫描任务
 *
 * @property context
 */
class PoiScanner(val context: Context) : GalleryScan(context) {

    private val poiModelConfig = PoiModelConfig(context)

    private val poiUpdateJob = PoiUpdateJob(context, object : PoiUpdateJob.PoiFileCallback {
        override fun getPoiFileStatus(): PoiUpdateJob.PoiFileStatus {
            if (!poiModelConfig.isModelReady()) {
                return PoiUpdateJob.PoiFileStatus(-1, -1)
            }
            val scannedVersion = poiModelConfig.scannedVersion
            val currentVersion = poiModelConfig.currentVersion
            return PoiUpdateJob.PoiFileStatus(currentVersion, scannedVersion)
        }

        override fun loadPoiFile(): List<File> {
            val dir = poiModelConfig.defaultComponentDirPath.listFiles()
            if (dir.isNullOrEmpty()) {
                return emptyList()
            }
            return dir.filter { it.name != ConfigChecker.CONFIG && it.exists() }.map { it.file }
        }

        override fun onPoiFileLoaded() {
            if (poiModelConfig.currentVersion != poiModelConfig.scannedVersion) {
                poiModelConfig.scannedVersion = poiModelConfig.currentVersion
            }
        }
    })
    private val isScanning = AtomicBoolean(false)
    override fun getScanType(): Int {
        return POI_SCAN
    }

    override fun getSceneName(): String {
        return SCENE_NAME
    }

    override fun onMonitorRecord(event: MonitorEvent, reachThreshold: Boolean) {
        super.onMonitorRecord(event, reachThreshold)
        markConditionsOnStartScan()
        GLog.w(TAG, LogFlag.DL) { "onMonitorRecord, event = $event, reachThreshold = $reachThreshold" }
        trackPoiScan(event, true)
    }

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        GLog.w(TAG, LogFlag.DL) { "onScan, start scan poi triggerType:$triggerType" }
        super.onScan(triggerType, config)
        if (ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ANDES_SEARCH) && !isScanning.get()) {
            isScanning.set(true)
            scanData()
        } else {
            GLog.d(TAG, LogFlag.DL) { "onScan, poi scan cancel" }
        }
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return false
    }

    private fun trackPoiScan(event: MonitorEvent?, isMonitor: Boolean) {
        val reasonType = getReasonType(false, poiUpdateJob.unsolvedGpsCount, POI_SCAN_COUNT_24H_MAX)
        val endNativeHeapSize = getEndNativeHeapSize(event)
        val additionalMap: MutableMap<String, String> = mutableMapOf()
        additionalMap[IS_MONITOR] = isMonitor.toString()
        additionalMap[SCAN_UNSOLVED_GPS_COUNT] = poiUpdateJob.unsolvedGpsCount.toString()
        additionalMap[SCAN_POI_CACHE_COUNT] = poiUpdateJob.scanInCacheCnt.toString()
        additionalMap[SCAN_POI_CLOUD_COUNT] = poiUpdateJob.scanInCloud.toString()
        val scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap)
        GLog.w(TAG, LogFlag.DL) { "trackPoiScan, recordItem = $scanTrackInfo" }
        ScanTrackHelper.trackScanResult(scanTrackInfo)
    }

    private fun scanData() {
        markConditionsOnStartScan()
        val modelVersion = if (poiModelConfig.isModelReady() && (poiModelConfig.currentVersion > PoiConfigFileParser.ADDRESS_DICT_VERSION)) {
            poiModelConfig.currentVersion
        } else {
            PoiConfigFileParser.ADDRESS_DICT_VERSION
        }
        poiUpdateJob.execute(modelVersion, object : PoiUpdateJob.JobCallback {
            override fun isJobContinue(): Boolean {
                mScanImageCount = poiUpdateJob.scanInCacheCnt + poiUpdateJob.scanInCloud
                return isAllowContinueScan(0, Int.MAX_VALUE, true)
            }

            override fun onJobEnd() {
                GLog.d(TAG, LogFlag.DL) { "onScan, poi scan end" }
                poiUpdateJob.clear()
                trackPoiScan(null, false)
            }
        })
    }

    companion object {
        private const val TAG = "${ScanConst.TAG}PoiScanner"
        private const val SCENE_NAME = "PoiScanner"

        // 当前没有限制POI 24小时扫描count，只限制了云侧请求扫描次数
        const val POI_SCAN_COUNT_24H_MAX = Integer.MAX_VALUE
        private const val SCAN_POI_CACHE_COUNT = "scan_poi_cache_count"
        private const val SCAN_POI_CLOUD_COUNT = "scan_poi_cloud_count"
        private const val SCAN_UNSOLVED_GPS_COUNT = "scan_unsolved_gps_count"
    }
}