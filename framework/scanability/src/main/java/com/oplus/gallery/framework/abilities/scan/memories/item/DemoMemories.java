/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DemoMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/14
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  hailong.zhang@Apps.Gallery3D    2018/3/14    1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;

import com.oplus.gallery.framework.abilities.scan.memories.Memories;

/*
* 1：增加回忆必须实现prepareMemories()，scanMemories()，createMemories()，最终将其数据写入回忆数据库才算
* 正在生成回忆参考@Memories类中processMemoriesItems方法
* 2：回忆实现完成后按照@MemoriesFactory中的loadConfigInfo方法添加对类名，聚类组名，高优先，普通优先级之后就会按照配置生成扫描顺序，完
* 成回忆生成。
* 3：回忆扫描顺序参看@Memories类中的compareTo的排序实现
* 4：优先级排序参看@MemoriesFactory中的filterMemories实现
* 5：回忆扫描调用规则参看@MemoriesScanner中的onScan实现
* */
public class DemoMemories extends Memories {
    private static final String TAG = "DemoMemories";

    public DemoMemories(Context context) {
        super(context);
    }

    //回忆准备工作，返回true才会继续调用scanMemories
    @Override
    public boolean prepareMemories() {
        return false;
    }

    //回忆扫描工作，返回true才会继续调用createMemories
    @Override
    public boolean scanMemories() {
        return false;
    }

    //回忆生成工作，返回true表示正在生成一个回忆
    @Override
    public boolean createMemories() {
        return false;
    }

    //回忆的唯一ID，确保每一个回忆id都不相同
    @Override
    public int getMemoriesId() {
        return 0;
    }

    //回忆显示的名称规则，根据这个id通过MemoriesNameRules转换名字
    @Override
    public int getNameType() {
        return 0;
    }

    @Override
    protected String getTag() {
        return TAG;
    }
}
