/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LabelGraphModelLoadingTemplate.kt
 ** Description : 英文知识图谱下载模板类
 ** Version     : 1.0
 ** Date        : 2024/8/9
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                            2024/8/9      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.framework.abilities.extraction.labelgraph.EnglishLabelGraphModelDownloadConfig
import com.oplus.gallery.framework.abilities.scan.modeldownload.LabelGraphCryptoUtil.getLabelGraphKey

/**
 * 英文知识图谱下载模板类
 */
class EnglishLabelGraphModelLoadingTemplate(
    context: Context,
    modelConfig: ModelConfig,
    allowContinueAction: (() -> Boolean)? = null
) : CommonModelLoadingTemplate(context, modelConfig, allowContinueAction) {

    override val tag: String = TAG

    override val modelName: String = ModelName.ENGLISH_LABEL_GRAPH_SOURCE

    override fun onSuccess() {
        EnglishLabelGraphModelDownloadConfig.onDownloadSuccess(context, currentVersion, ::getLabelGraphKey)
    }

    companion object {
        private const val TAG = "EnglishLabelGraphModelLoadingTemplate"
    }
}