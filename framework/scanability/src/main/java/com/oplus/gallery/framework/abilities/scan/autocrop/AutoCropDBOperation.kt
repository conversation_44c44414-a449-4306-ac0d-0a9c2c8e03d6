/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AutoCropDBOperation
 ** Description: 智能裁剪的数据库相关操作类
 **
 ** Version: 1.0
 ** Date: 2022/03/17
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/03/17  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.autocrop

import android.content.ContentValues
import com.oplus.gallery.business_lib.seniorpicked.PickedDayDBFilterCondition
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaCondition.buildEmptySeniorScore
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaCondition.getLocalMediaLeftJoinSeniorMedia
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaDBOperation
import com.oplus.gallery.foundation.ui.autocrop.CropRect
import com.oplus.gallery.foundation.ui.autocrop.CropRectSet
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.label.bean.ImageInfo
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaCondition
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.util.ConstantUtils
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import java.lang.StringBuilder
import java.util.ArrayList

class AutoCropDBOperation {
    /**
     * 查找可在精选日视图页展示的图片数据
     * 即精选表中评分符合以及未参与扫描的数据
     *  SELECT local_media.media_id,local_media._data,local_media.date_modified,
     *  local_media.datetaken,local_media.invalid,local_media.media_type
     *  FROM local_media LEFT JOIN senior_media ON senior_media._data = local_media._data
     *  WHERE (buildSenior() OR senior_score IS NULL)
     *  AND (crop_rect IS NULL OR crop_rect_version < 1)
     *  AND (media_type = 1 AND mime_type != 'image/gif' AND width <= 15000 AND width >= 800
     *  AND height <= 20000 AND height >= 800 AND _size <= 52428800 AND _size >= 102400
     *  AND (3 * width >= height AND width <= height * 3))
     *  AND ((relative_path != 'Pictures/Screenshots/'
     *  AND relative_path != 'DCIM/Screenshots/')  OR cshot_id != 0 )
     *  AND (invalid IN (0,4))
     *  ORDER BY datetaken DESC
     */
    @Suppress("TooGenericExceptionCaught")
    fun queryAllImageWhichCanExistInPickedDay(version: Int): MutableList<BaseImageInfo> {
        val stringBuilder = StringBuilder().apply {
            append(ConstantUtils.SELECT)
            append(DatabaseUtils.projectionsToString(ImageInfo.getMediaProviderProject(),
                GalleryStore.GalleryMedia.TAB
            ))
            append(getLocalMediaLeftJoinSeniorMedia())
            append(ConstantUtils.WHERE)
            append(ConstantUtils.LEFT_BRACKETS)
            append(SeniorMediaCondition.buildSenior())
            append(ConstantUtils.OR)
            append(buildEmptySeniorScore())
            append(ConstantUtils.RIGHT_BRACKETS)
            append(ConstantUtils.AND)
            append(buildInValidCropRect(version))
            append(ConstantUtils.AND)
            append(PickedDayDBFilterCondition.getBasicFilterCondition(mediaTypeSupport = Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_IMAGE))
            append(ConstantUtils.ORDER_BY + LocalColumns.DATE_TAKEN + ConstantUtils.DESC)
        }
        return SeniorMediaDBOperation.queryImageInfo(stringBuilder.toString())
    }

    /**
     * 更新裁剪数据
     */
    fun updateCropRect(cropRectMap: MutableMap<String, MutableList<CropRect>?>, version: Int) {
        val updateReqsList: MutableList<MutableList<UpdateReq>> = ArrayList()
        for ((key, value) in cropRectMap) {
            if (value.isNullOrEmpty()) continue
            if (updateReqsList.isEmpty()
                || (updateReqsList[updateReqsList.size - 1].size >= BATCH_SIZE)
            ) {
                updateReqsList.add(ArrayList())
            }
            updateReqsList[updateReqsList.size - 1].add(
                UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(LocalColumns.DATA + ConstantUtils.EQUAL_TO)
                    .setWhareArgs(arrayOf(key))
                    .setConvert {
                        val contentValues = ContentValues()
                        contentValues.put(LocalColumns.CROP_RECT, CropRectSet(value).toString())
                        contentValues.put(LocalColumns.CROP_RECT_VERSION, version)
                        contentValues
                    }.build()
            )
        }
        for (updateReqs in updateReqsList) {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .addDataReqs(updateReqs)
                .build().exec()
        }
        if (updateReqsList.isNotEmpty()) {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        }
    }

    /**
     * (crop_rect is null or crop_rect_version < 1)
     */
    private fun buildInValidCropRect(version: Int): StringBuilder {
        return StringBuilder().apply {
            append(ConstantUtils.LEFT_BRACKETS)
            append(LocalColumns.CROP_RECT)
            append(ConstantUtils.IS_NULL)
            append(ConstantUtils.OR)
            append(LocalColumns.CROP_RECT_VERSION)
            append(ConstantUtils.LESS_THAN)
            append(version)
            append(ConstantUtils.RIGHT_BRACKETS)
        }
    }

    companion object {
        private const val BATCH_SIZE = 500
    }
}