/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MemoriesScanner.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/6
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D     2018/3/6    1.0          build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories;

import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.MEMORIES_SCAN_COUNT_24H_MAX;

import android.content.Context;

import androidx.annotation.NonNull;

import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil;
import com.oplus.gallery.framework.abilities.scan.ScanConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackInfo;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class MemoriesScanner extends GalleryScan {
    private static final String TAG = ScanConst.TAG + "MemoriesScanner";
    private static final String SCENE_NAME = "MemoriesScanner";
    private static final String SCENE_SCANNER_INVALID_TYPE = SCENE_NAME + "_InvalidType";
    private static final String SCENE_SCANNER_NETWORK_DISCONNECT = SCENE_NAME + "_NetworkDisconnect";
    private static final String CURRENT_SCAN_MEMORIES = "current_scan_memories";
    private static final String MEMORIES_SCAN_COUNT = "memories_scan_count";
    private static final int MEMORIES_SCAN_ALL = 1;
    private static final int MEMORIES_SCAN_SINGLE = 2;
    private int mMemoriesScanCountNotInCharging = 0;
    private int mMemoriesScanCount = 0;
    private Memories mCurrentScanMemories;

    public MemoriesScanner(Context context) {
        super(context);
    }

    @Override
    public int getScanType() {
        return MEMORIES_SCAN;
    }

    @Override
    public String getSceneName() {
        return SCENE_NAME;
    }

    @Override
    public void onScan(int triggerType, ScanConfig config) {
        GLog.w(TAG, LogFlag.DL, "onScan, start memory scan triggerType =" + triggerType);
        super.onScan(triggerType, config);
        if (!NetworkPermissionManager.isUseOpenNetwork()) {
            // 扫描中断，记录原因
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_NETWORK_DISCONNECT, triggerType);
            return;
        }
        //功耗优化，非充电模式下如果有记忆数据则不进行扫描
        if (isLowPowerConsumptionStopScan()) {
            trackStopMemoriesScan(GalleryScanMonitor.ReasonType.REASON_LOW_POWER_CONSUMPTION);
            GLog.d(TAG, LogFlag.DL, "onScan, run in low power consumption mode, no need to scan");
            return;
        }
        int scanType = INVALID;
        long lastMemoriesCreatedTime = MemoriesUtils.getLastMemoriesCreatedTime(mContext);
        if (lastMemoriesCreatedTime == MemoriesUtils.DEFAULT_LAST_MEMORIES_CREATED_TIME) {
            scanType = MEMORIES_SCAN_ALL;
        } else if (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(lastMemoriesCreatedTime, System.currentTimeMillis()) > 0) {
            scanType = MEMORIES_SCAN_SINGLE;
        }
        if (scanType == INVALID) {
            GLog.d(TAG, LogFlag.DL,"onScan, less than two days ,return lastMemoriesCreatedTime ="
                    + TimeUtils.getFormatDateTime(lastMemoriesCreatedTime));
            // 扫描中断，记录原因
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_INVALID_TYPE, triggerType);
            return;
        }
        mIsFirstScan = (scanType == MEMORIES_SCAN_ALL);
        int createdMemorySize = 0;
        ArrayList<Memories> allMemories = MemoriesFactory.INSTANCE.getAllMemories();
        int lastCreateMemoriesGroupType = -1;
        markConditionsOnStartScan();
        boolean isCharging = BatteryStatusUtil.isBatteryInCharging(false);
        mMemoriesScanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.MEMORIES_SCAN_COUNT_24h_KEY, 0);
        if ((allMemories != null) && !allMemories.isEmpty()) {
            if (!LabelDictionary.isDictionaryLoaded()) {
                GLog.d(TAG, LogFlag.DL,"onScan, label dictionary is not loaded, firstly load it!");
                LabelSearchEngine.getInstance().loadDictionary(mContext, false);
            }
            for (Memories memory : allMemories) {
                if (isCancel() || isInterrupt()
                        || !GalleryScanMonitor.isCountAllow(mMemoriesScanCountNotInCharging, MEMORIES_SCAN_COUNT_24H_MAX)
                        || !GalleryScanMonitor.isAllowContinueScan(mContext, false, mCanScanWhenGalleryRunOnTop)) {
                    GLog.d(TAG, LogFlag.DL, "onScan, not ready to continue so break " + memory);
                    break;
                }
                if (lastCreateMemoriesGroupType == memory.getMemoriesGroupType()) {
                    GLog.d(TAG, LogFlag.DL,"onScan, same group type ,continue memory =" + memory);
                    continue;
                }
                synchronized (MemoriesScanner.this) {
                    mCurrentScanMemories = memory;
                }
                if (memory.doScan(triggerType)) {
                    GLog.d(TAG, LogFlag.DL, "onScan, create memories success  =" + memory);
                    lastCreateMemoriesGroupType = memory.getMemoriesGroupType();
                    createdMemorySize++;
                    setLastMemoriesCreatedTime();
                    setLastMemoriesCreatedGroupType(memory.getMemoriesGroupType());
                    isCharging = addScanCountIfNoCharging() || isCharging;
                    recodeScanInfoIfNeed();
                    if (!mIsFirstScan) {
                        synchronized (MemoriesScanner.this) {
                            mCurrentScanMemories = null;
                        }
                        break;
                    }
                } else {
                    GLog.d(TAG, LogFlag.DL, "onScan, no memeries: " + memory);
                }
                synchronized (MemoriesScanner.this) {
                    mCurrentScanMemories = null;
                }
            }
        }

        trackMemoriesScan(null, false);
        if (createdMemorySize <= 0) {
            GLog.d(TAG, LogFlag.DL, "onScan: createMemorySize is 0");
        }
        updateLastScanTime();
        GLog.i(TAG, LogFlag.DL, "onScan start auto download in Wifi");
        ApiDmManager.getVideoEditorDM().autoDownloadThemeInWifi();
        GLog.i(TAG, LogFlag.DL, "onScan, memory scan end");
    }

    private boolean isLowPowerConsumptionStopScan() {
        if (!isCloudAllowLowConsumptionScan()) {
            GLog.d(TAG, LogFlag.DL, "inLowPowerConsumptionScan cloud disable");
            return false;
        }
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            GLog.d(TAG, LogFlag.DL, "inLowPowerConsumptionScan battery charging");
            return false;
        }
        return MemoriesProviderHelper.hasMemoriesCreated();
    }

    @Override
    public void onInterrupt(int reason) {
        super.onInterrupt(reason);
        GLog.w(TAG, LogFlag.DL, "onInterrupt");
        synchronized (this) {
            if (mCurrentScanMemories != null) {
                mCurrentScanMemories.interrupt();
            }
        }
    }

    private boolean addScanCountIfNoCharging() {
        mMemoriesScanCount++;
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true;
        }
        GalleryScanMonitor.refreshScanRecordingIfNecessary(mContext);
        mMemoriesScanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.MEMORIES_SCAN_COUNT_24h_KEY, 0) + 1;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.MEMORIES_SCAN_COUNT_24h_KEY, mMemoriesScanCountNotInCharging);
        return false;
    }

    private void recodeScanInfoIfNeed() {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return;
        }
        int memoriesScanCountAll = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.MEMORIES_SCAN_COUNT_ALL_KEY, 0) + 1;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.MEMORIES_SCAN_COUNT_ALL_KEY, memoriesScanCountAll);
        GalleryScanUtils.recordInfo(mContext, TAG, mMemoriesScanCountNotInCharging, memoriesScanCountAll);
    }

    private void setLastMemoriesCreatedTime() {
        MemoriesUtils.setLastMemoriesCreatedTime(mContext, System.currentTimeMillis());
    }

    private void setLastMemoriesCreatedGroupType(int groupType) {
        MemoriesUtils.setLastMemoriesCreatedGroupType(mContext, groupType);
    }

    @Override
    public void onMonitorRecord(@NonNull MonitorEvent event, boolean reachThreshold) {
        super.onMonitorRecord(event, reachThreshold);
        markConditionsOnStartScan();
        GLog.d(TAG, LogFlag.DL, "onMonitorRecord, event = " + event + ", reachThreshold = " + reachThreshold);
        trackMemoriesScan(event, true);
    }

    @Override
    protected boolean runTaskIsNeedCharging() {
        return false;
    }

    private void trackMemoriesScan(MonitorEvent event, boolean isMonitor) {
        int reasonType = getReasonType(false, mMemoriesScanCountNotInCharging, MEMORIES_SCAN_COUNT_24H_MAX);
        long endNativeHeapSize = getEndNativeHeapSize(event);
        Map<String, String> additionalMap = new HashMap<>();
        synchronized (MemoriesScanner.this) {
            if (mCurrentScanMemories != null) {
                additionalMap.put(CURRENT_SCAN_MEMORIES, mCurrentScanMemories.getMemoriesName());
            }
        }
        additionalMap.put(MEMORIES_SCAN_COUNT, String.valueOf(mMemoriesScanCount));
        additionalMap.put(IS_MONITOR, String.valueOf(isMonitor));
        ScanTrackInfo scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap);
        GLog.d(TAG, LogFlag.DL, "trackMemoriesScan, recordItem = " + scanTrackInfo.toString());
        ScanTrackHelper.trackScanResult(scanTrackInfo);
    }

    private void trackStopMemoriesScan(@GalleryScanMonitor.ReasonType int reason) {
        markConditionsOnStartScan();
        long endNativeHeapSize = getEndNativeHeapSize(null);
        ScanTrackInfo scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reason, null);
        GLog.d(TAG, LogFlag.DL, "trackStopMemoriesScan, recordItem = " + scanTrackInfo.toString());
        ScanTrackHelper.trackScanResult(scanTrackInfo);
    }
}