/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryScanPersonPetProviderHelper.kt
 ** Description: 人物，宠物，人宠群组数据库操作helper类,从原GalleryScanProviderHelper抽离出来
 **
 ** Version: 1.0
 ** Date: 2025/04/18
 ** Author: xiaxudong@OppoGallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiaxudong@OppoGallery3D        2025/04/18   1.0          init
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.model

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.graphics.RectF
import android.text.TextUtils
import android.util.LongSparseArray
import android.util.Pair
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo
import com.oplus.gallery.business_lib.model.data.label.bean.FaceInfo
import com.oplus.gallery.business_lib.model.data.label.bean.FaceInfo.Companion.buildFaceInfoList
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.NORMAL_INVALID_CODE
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PERSON_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PERSON_PET_GROUP_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PET_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getConvertRoleType
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getCoverScoreFieldName
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getGroupIdFieldName
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getGroupIdsFieldName
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getGroupNameFieldName
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getGroupViewIdFieldName
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getRoleType
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getTableGroupViewType
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getTableType
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getTableViewType
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.getViewFinalCoverScoreFieldName
import com.oplus.gallery.business_lib.model.data.pet.RelationShipType
import com.oplus.gallery.foundation.database.notifier.UriNotifier.notifyTableChange
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetGroup
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetGroupDetailViewColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetGroupListViewColumns.MERGE_GROUP_CREATE_DATE
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetListViewColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.PetColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.PetViewColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.GROUP_VISIBLE_HIDE
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.GROUP_VISIBLE_SHOW
import com.oplus.gallery.foundation.database.util.ConstantUtils
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.convert.IntListConverter
import com.oplus.gallery.foundation.dbaccess.convert.StringConverter
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.toInt
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PET_CLASSIFY
import com.oplus.gallery.framework.abilities.scan.face.GroupInfo
import com.oplus.gallery.framework.abilities.scan.personpet.PersonPetGroupCluster
import com.oplus.gallery.framework.abilities.scan.personpet.PersonPetGroupCoverUtils
import com.oplus.gallery.framework.abilities.scan.personpet.PersonPetInfo
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.Collections
import kotlin.math.max
import kotlin.math.min

/**
 * 人物，宠物，人宠群组数据库操作helper类，从原GalleryScanProviderHelper抽离出来
 */
@Suppress("LargeClass")
object GalleryScanPersonPetProviderHelper {
    private const val TAG = "GalleryScanPersonPetProviderHelper"
    private const val GROUP_NON_DISBAND_CODE = 0
    private const val GROUP_DISBAND_CODE = 1
    private const val GROUP_SIZE = 100

    /**
     * 最高分类比较器,用于从FinalGroupDataInfo list中找到score最高的FinalGroupDataInfo对象
     */
    class PersonPetGroupDataInfoScoreComparator : Comparator<FinalGroupDataInfo> {
        override fun compare(o1: FinalGroupDataInfo, o2: FinalGroupDataInfo): Int {
            return o2.score?.let { o1.score?.compareTo(it) } ?: -1
        }
    }

    /**
     * 为了整改方法参数过多，将同类型的参数使用数据类的方式封装。
     */
    data class IdDataInfo(var majorGroupId: Long, var groupIdList: List<Long>, var updateGroupId: Long)

    /**
     * 最终满足条件的合照信息数据封装类
     */
    data class FinalGroupDataInfo(
        var filePath: String? = null,
        var mergeGroupId: Long = 0L,
        var mergeGroupName: String = TextUtil.EMPTY_STRING,
        var score: Float? = null,
        var mediaType: Int = 0,
        var invalid: Int = 0,
        var isRecycled: Int = 0,
        var isDisband: Int = 0,
        var thumbWidth: Int = 0,
        var thumbHeight: Int = 0,
        var rectLeft: Int = Int.MAX_VALUE,
        var rectTop: Int = Int.MAX_VALUE,
        var rectRight: Int = 0,
        var rectBottom: Int = 0,
        var personGroupIdList: MutableList<Long> = mutableListOf(),
        var petGroupIdList: MutableList<Long> = mutableListOf()
    )

    /**
     * 空合照查询结果数据类
     */
    data class EmptyGroupResult(
        var isExistEmptyGroup: Boolean = false,
        var mergeGroupId: Long = NORMAL_INVALID_CODE.toLong(),
        var isDisband: Int = GROUP_NON_DISBAND_CODE
    )

    /**
     * 需要更新默认合照封面的数据类
     */
    data class NeedUpdateDefaultGroupCoverDataInfo(
        var existPersonPetGroupDataInfo: FinalGroupDataInfo? = null,
        var currentPersonPetGroupDataInfo: FinalGroupDataInfo? = null,
    )

    /**
     * 人宠角色信息数据类，为减少方法参数
     */
    data class BasePersonPetDataInfo(
        val groupIdList: MutableList<Long> = mutableListOf(),
        val personGroupIdList: MutableList<Long> = mutableListOf(),
        val petGroupIdList: MutableList<Long> = mutableListOf(),
    )

    private val FACE_QUERY_PROJECT_SIMPLE = arrayOf(
        GalleryStore.ScanFace._ID,
        GalleryStore.ScanFace.GROUP_ID,
        GalleryStore.ScanFace.BEST_SCORE,
        GalleryStore.ScanFace.SCORE,
        GalleryStore.ScanFace.YAW,
        GalleryStore.ScanFace.PITCH,
        GalleryStore.ScanFace.ROLL,
        GalleryStore.ScanFace.EYE_DIST,
        GalleryStore.ScanFace.IS_CHOSEN,
        GalleryStore.ScanFace.IS_COVER,
        GalleryStore.ScanFace.FACE_COVER_SCORE
    )

    private val PET_QUERY_PROJECT_SIMPLE = arrayOf(
        PetViewColumns._ID,
        PetViewColumns.GROUP_ID,
        PetViewColumns.FINAL_SCORE,
        PetViewColumns.IS_CHOSEN
    )

    private val PERSON_PET_GROUP_QUERY_PROJECT_SIMPLE = arrayOf(
        PersonPetGroupDetailViewColumns._ID,
        PersonPetGroupDetailViewColumns.MERGE_GROUP_ID,
        PersonPetGroupDetailViewColumns.SCORE,
        PersonPetGroupDetailViewColumns.IS_CHOSEN
    )

    private val FACE_QUERY_PROJECT = arrayOf(
        GalleryStore.ScanFace._ID,
        GalleryStore.ScanFace.GROUP_ID,
        GalleryStore.ScanFace.AGE,
        GalleryStore.ScanFace.FEATURE,
        GalleryStore.ScanFace.BEST_SCORE,
        GalleryStore.ScanFace.THUMB_W,
        GalleryStore.ScanFace.THUMB_H,
        GalleryStore.ScanFace.LEFT,
        GalleryStore.ScanFace.RIGHT,
        GalleryStore.ScanFace.BOTTOM,
        GalleryStore.ScanFace.TOP,
        GalleryStore.ScanFace.IS_SINGLE_FACE,
        GalleryStore.ScanFace.DATA,
        GalleryStore.ScanFace.MEDIA_TYPE,
        GalleryStore.ScanFace.FACE_COVER_SCORE
    )

    private val FACE_QUERY_PROJECT_RECT = arrayOf(
        GalleryStore.ScanFace._ID,
        GalleryStore.ScanFace.THUMB_W,
        GalleryStore.ScanFace.THUMB_H,
        GalleryStore.ScanFace.LEFT,
        GalleryStore.ScanFace.TOP,
        GalleryStore.ScanFace.RIGHT,
        GalleryStore.ScanFace.BOTTOM
    )

    private val PERSON_GROUP_QUERY_PROJECT = arrayOf(
        ScanFaceGroupColumns.GROUP_ID,
        ScanFaceGroupColumns.GROUP_NAME,
        ScanFaceGroupColumns.GROUP_VISIBLE,
        ScanFaceGroupColumns.NUM_ALL_FACES,
        ScanFaceGroupColumns.HAS_BIG_FACE,
        ScanFaceGroupColumns.IS_MANUAL,
        ScanFaceGroupColumns.MANUAL_DATE
    )

    private val PET_GROUP_QUERY_PROJECT = arrayOf(
        ScanFaceGroupColumns.GROUP_ID,
        ScanFaceGroupColumns.GROUP_NAME,
        ScanFaceGroupColumns.NUM_ALL_FACES,
        ScanFaceGroupColumns.IS_MANUAL,
        ScanFaceGroupColumns.MANUAL_DATE
    )

    private val GROUP_QUERY_PROJECT_IN_FACECLUSTER = arrayOf(
        ScanFaceGroupColumns.GROUP_ID,
        ScanFaceGroupColumns.GROUP_NAME,
        ScanFaceGroupColumns.GROUP_VISIBLE,
        ScanFaceGroupColumns.IS_MANUAL,
        ScanFaceGroupColumns.MANUAL_DATE,
        ScanFaceColumns.RELATION_TYPE,
        ScanFaceColumns.CUSTOM_RELATION,
        ScanFaceColumns.IS_HIDE
    )

    private fun buildGroupInfo(cursor: Cursor?, albumSetType: Int): GroupInfo? {
        return when (albumSetType) {
            TYPE_PERSON_ALBUM_SET -> GroupInfo.buildGroupInfoList(cursor)[0]
            TYPE_PET_ALBUM_SET -> GroupInfo.buildPetGroupInfoList(cursor)[0]
            else -> GroupInfo.buildGroupInfoList(cursor)[0]
        }
    }

    private fun getGroupQueryProject(albumSetType: Int): Array<String> {
        return when (albumSetType) {
            TYPE_PERSON_ALBUM_SET -> PERSON_GROUP_QUERY_PROJECT
            TYPE_PET_ALBUM_SET -> PET_GROUP_QUERY_PROJECT
            else -> PERSON_GROUP_QUERY_PROJECT
        }
    }

    private fun getSimpleQueryProject(albumSetType: Int): Array<String> {
        return when (albumSetType) {
            TYPE_PERSON_ALBUM_SET -> FACE_QUERY_PROJECT_SIMPLE
            TYPE_PET_ALBUM_SET -> PET_QUERY_PROJECT_SIMPLE
            TYPE_PERSON_PET_GROUP_ALBUM_SET -> PERSON_PET_GROUP_QUERY_PROJECT_SIMPLE
            else -> FACE_QUERY_PROJECT_SIMPLE
        }
    }

    /**
     * set cover best state
     *
     * @param context
     * @param personId
     * @param isChosen
     * @return Boolean
     */
    @JvmStatic
    fun setCoverBestState(albumSetType: Int, personId: Long, isChosen: Boolean): Boolean {
        kotlin.runCatching {
            UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableType(albumSetType))
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(GalleryStore.ScanFaceView._ID + SQLGrammar.EQUAL + personId)
                .setConvert {
                    val cv = ContentValues()
                    cv.put(GalleryStore.ScanFaceView.IS_CHOSEN, isChosen.toInt())
                    cv
                }.build().exec()
            return true
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "setCoverBestState, e:$it" }
        }
        return false
    }

    /**
     * get the best cover if chosen
     *
     * @param context
     * @param groupId
     * @return FaceInfo
     */
    @JvmStatic
    fun getPreBestFace(albumSetType: Int, groupId: Long): CvFaceInfo? {
        var face: CvFaceInfo? = null
        runCatching {
            val sb = StringBuilder()
            sb.append(getGroupViewIdFieldName(albumSetType) + SQLGrammar.EQUAL + groupId)
            sb.append(SQLGrammar.AND + GalleryStore.ScanFaceView.IS_CHOSEN + SQLGrammar.EQUAL_ONE)
            //TagsStore.Faces.getContentUri()
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableViewType(albumSetType))
                .setProjection(getSimpleQueryProject(albumSetType))
                .setWhere(sb.toString())
                .setOrderBy(getViewFinalCoverScoreFieldName(albumSetType) + SQLGrammar.ORDER_DESC)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return face
                }
                face = when (albumSetType) {
                    TYPE_PERSON_ALBUM_SET -> CvFaceInfo.buildFaceInfoList(cursor)[0]
                    TYPE_PET_ALBUM_SET -> CvFaceInfo.buildPetInfoList(cursor)[0]
                    TYPE_PERSON_PET_GROUP_ALBUM_SET -> CvFaceInfo.buildPersonPetGroupInfoList(cursor)[0]
                    else -> null
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getPreBestFace, e:$it" }
        }
        return face
    }

    /**
     * 设置默认封面状态
     *
     * @param context
     * @param personId
     * @param isDefault
     * @return Boolean
     */
    @JvmStatic
    fun setDefaultCoverState(context: Context?, personId: Long, isDefault: Boolean): Boolean {
        kotlin.runCatching {
            UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(GalleryStore.ScanFaceView._ID + SQLGrammar.EQUAL + personId)
                .setConvert {
                    val cv = ContentValues()
                    cv.put(GalleryStore.ScanFaceView.IS_COVER, isDefault)
                    cv
                }.build().exec()
            return true
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "setDefaultCoverState, e:$it" }
        }
        return false
    }

    /**
     * 获取默认封面信息
     *
     * @param context
     * @param groupId
     * @return CvFaceInfo?
     */
    @JvmStatic
    fun getPreDefaultCover(context: Context?, groupId: Long): CvFaceInfo? {
        var face: CvFaceInfo? = null
        kotlin.runCatching {
            val sb = StringBuilder()
            sb.append(GalleryStore.ScanFaceView.GROUP_ID + SQLGrammar.EQUAL + groupId)
            sb.append(SQLGrammar.AND + GalleryStore.ScanFaceView.IS_COVER + SQLGrammar.EQUAL_ONE)
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE_VIEW)
                .setProjection(FACE_QUERY_PROJECT_SIMPLE)
                .setWhere(sb.toString())
                .setOrderBy(
                    if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY)) {
                        GalleryStore.ScanFaceView.FACE_COVER_SCORE
                    } else {
                        GalleryStore.ScanFaceView.BEST_SCORE
                    } + SQLGrammar.ORDER_DESC
                )
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return face
                }
                face = CvFaceInfo.buildFaceInfoList(cursor)[0]
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getPreDefaultCover, e:$it" }
        }
        return face
    }

    /**
     * 获取所有人脸图片数
     *
     * @return Int
     */
    @JvmStatic
    fun getAllFaceImageCount(): Int {
        var count = 0
        kotlin.runCatching {
            val queryProject = arrayOf("count(DISTINCT _data)")
            val whereClause = (GalleryStore.ScanFace.NO_FACE + ConstantUtils.NOT_EQUAL_ONE
                    + ConstantUtils.AND + DatabaseUtils.getDataValidWhere()
                    + ConstantUtils.AND + GalleryStore.ScanFace.FACE_REMOVABLE + ConstantUtils.NOT_EQUAL_ONE
                    + ConstantUtils.AND + GalleryStore.ScanFace.IS_RECYCLED + ConstantUtils.NOT_EQUAL_ONE)
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(queryProject)
                .setWhere(whereClause)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor != null) && (cursor.moveToNext())) {
                    count = cursor.getInt(0)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllFaceImageCount, e:$it" }
        }
        return count
    }

    /**
     * 获取有人脸的图片的数量
     *
     * @return Int
     */
    @JvmStatic
    fun getImageFaceCount(): Int {
        var count = 0
        kotlin.runCatching {
            val whereClause = (GalleryStore.ScanFace.NO_FACE + ConstantUtils.NOT_EQUAL_ONE
                    + ConstantUtils.AND + DatabaseUtils.getDataValidWhere(GalleryStore.ScanFace.TAB)
                    + ConstantUtils.AND + GalleryStore.ScanFace.IS_RECYCLED + ConstantUtils.NOT_EQUAL_ONE
                    + ConstantUtils.AND + GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
                    + ConstantUtils.EQUAL + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
            val stringBuilder = StringBuilder()
            stringBuilder.append("SELECT ")
            stringBuilder.append("count(" + GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT
                    + GalleryStore.GalleryColumns.LocalColumns._ID + SQLGrammar.RIGHT_BRACKETS)
            stringBuilder.append(" FROM ")
            stringBuilder.append(GalleryStore.GalleryMedia.TAB)
            stringBuilder.append(" INNER JOIN " + GalleryStore.ScanFace.TAB)
            stringBuilder.append(" ON ")
            stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT + ScanFaceColumns.DATA + SQLGrammar.TRIM_EQUAL
                    + GalleryStore.ScanFace.TAB + SQLGrammar.DOT + ScanFaceColumns.DATA)
            stringBuilder.append(" WHERE ")
            stringBuilder.append(whereClause)
            val rawQueryReq = RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(stringBuilder.toString())
                .setSqlArgs(null)
                .build()
            DataAccess.getAccess().rawQuery(rawQueryReq).use { cursor ->
                if ((cursor != null) && (cursor.moveToNext())) {
                    count = cursor.getInt(0)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllFaceImageCount, e:$it" }
        }
        return count
    }

    /**
     * 获取有人脸的视频的数量
     *
     * @return Int
     */
    @JvmStatic
    fun getVideoFaceCount(): Int {
        var count = 0
        kotlin.runCatching {
            val whereClause = (GalleryStore.ScanFace.NO_FACE + SQLGrammar.NOT_EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.ScanFace.TAB + SQLGrammar.DOT + GalleryStore.ScanFace.INVALID + SQLGrammar.NOT_EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.ScanFace.IS_RECYCLED + SQLGrammar.NOT_EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
                    + SQLGrammar.EQUAL + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)
            val stringBuilder = StringBuilder()
            stringBuilder.append("SELECT ")
            stringBuilder.append("count(" + GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT
                    + GalleryStore.GalleryColumns.LocalColumns._ID + SQLGrammar.RIGHT_BRACKETS)
            stringBuilder.append(" FROM ")
            stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.INNER_JOIN + GalleryStore.ScanFace.TAB + SQLGrammar.ON)
            stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT + ScanFaceColumns.DATA
                    + SQLGrammar.TRIM_EQUAL + GalleryStore.ScanFace.TAB + SQLGrammar.DOT + ScanFaceColumns.DATA)
            stringBuilder.append(" WHERE ")
            stringBuilder.append(whereClause)
            val rawQueryReq = RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(stringBuilder.toString())
                .setSqlArgs(null)
                .build()
            DataAccess.getAccess().rawQuery(rawQueryReq).use { cursor ->
                if ((cursor != null) && (cursor.moveToNext())) {
                    count = cursor.getInt(0)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getVideoFaceCount, e:$it" }
        }
        return count
    }

    /**
     * get all face record, only with feature
     *
     * @return ArrayList<CvFaceInfo>?
     */
    @JvmStatic
    fun getAllFaceWithFeature(): ArrayList<CvFaceInfo>? {
        val list = ArrayList<CvFaceInfo>()
        kotlin.runCatching {
            val whereClause = (GalleryStore.ScanFace.NO_FACE + SQLGrammar.NOT_EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.ScanFace.FACE_REMOVABLE + SQLGrammar.NOT_EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.ScanFace.FEATURE + SQLGrammar.IS_NOT_NULL)
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(FACE_QUERY_PROJECT)
                .setWhere(whereClause)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return list
                }
                list.addAll(CvFaceInfo.buildFaceInfoListWithFeature(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllFaceWithFeature, e:$it" }
        }
        return list
    }

    /**
     * get face record, only with feature
     *
     * @return HashMap<String, MutableList<CvFaceInfo>>
     */
    @JvmStatic
    fun getFaceWithFeature(filePathList: List<String>): HashMap<String, MutableList<CvFaceInfo>> {
        val list = HashMap<String, MutableList<CvFaceInfo>>()
        if ((filePathList == null) || (filePathList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL) { "getFaceWithFeature, mediaList is empty!" }
            return list
        }
        val faceInfoList = ArrayList<CvFaceInfo>()
        kotlin.runCatching {
            //Uri queryUri = TagsStore.FaceCluster.getContentUri();
            val sb = StringBuilder()
            sb.append(GalleryStore.ScanFace.DATA + SQLGrammar.IN + SQLGrammar.LEFT_BRACKETS)
            for (filePath in filePathList) {
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(filePath)
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(SQLGrammar.COMMA)
            }
            sb.deleteCharAt(sb.length - 1)
            sb.append(SQLGrammar.RIGHT_BRACKETS)
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(FACE_QUERY_PROJECT)
                .setWhere(sb.toString())
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return list
                }
                faceInfoList.addAll(CvFaceInfo.buildFaceInfoListWithFeature(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getFaceWithFeature, e:$it" }
        }
        for (face in faceInfoList) {
            val filePath = face.data
            var faceList = list[filePath]
            if (faceList == null) {
                faceList = ArrayList()
                filePath?.let { filePath ->
                    list[filePath] = faceList
                }
            }
            faceList.add(face)
        }
        return list
    }

    /**
     * 根据filepath获取不显示人脸的图片记录
     *
     * @param context
     * @param pathList
     * @param noFaceExclude
     * @return HashMap<String, ArrayList<CvFaceInfo>>
     */
    @JvmStatic
    fun getInvisibleFaceByPath(
        context: Context?,
        pathList: ArrayList<String>,
        noFaceExclude: Boolean
    ): HashMap<String, ArrayList<CvFaceInfo>> {
        val list = HashMap<String, ArrayList<CvFaceInfo>>()
        if ((pathList == null) || pathList.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "getInvisibleFaceByPath, pathList is empty!" }
            return list
        }
        val faceInfoList = ArrayList<CvFaceInfo>()
        kotlin.runCatching {
            val sb = StringBuilder()
            sb.append(GalleryStore.ScanFace.FACE_VISIBLE + SQLGrammar.NOT_EQUAL_ONE)
            if (noFaceExclude) {
                sb.append(SQLGrammar.AND + GalleryStore.ScanFace.NO_FACE + SQLGrammar.NOT_EQUAL_ONE)
            }
            sb.append(SQLGrammar.AND + GalleryStore.ScanFace.DATA + SQLGrammar.IN + SQLGrammar.LEFT_BRACKETS)
            for (filepath in pathList) {
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(filepath)
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(SQLGrammar.COMMA)
            }
            sb.deleteCharAt(sb.length - 1)
            sb.append(SQLGrammar.RIGHT_BRACKETS)
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(FACE_QUERY_PROJECT)
                .setWhere(sb.toString())
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return list
                }
                faceInfoList.addAll(CvFaceInfo.buildFaceInfoListWithFeature(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getInvisibleFaceByPath, e:$it" }
        }
        for (face in faceInfoList) {
            val path = face.data
            var faceList = list[path]
            if (faceList == null) {
                faceList = ArrayList()
                path?.let { path ->
                    list[path] = faceList
                }
            }
            faceList.add(face)
        }
        return list
    }

    /**
     * 删除所有没有人脸的记录
     *
     */
    @JvmStatic
    fun deleteAllInvisibleFace() {
        kotlin.runCatching {
            val req = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setWhere(GalleryStore.ScanFace.FACE_VISIBLE + SQLGrammar.NOT_EQUAL_ONE)
                .build()
            DataAccess.getAccess().delete(req)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteAllInvisibleFace, e:$it" }
        }
    }

    /**
     * judge if had face not group
     *
     * @return Boolean
     */
    @JvmStatic
    fun hadNoGroupFace(): Boolean {
        kotlin.runCatching {
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(arrayOf(GalleryStore.ScanFace._ID))
                .setWhere(
                    GalleryStore.ScanFace.DATA + SQLGrammar.IS_NOT_NULL
                            + SQLGrammar.AND + GalleryStore.ScanFace.INVALID + SQLGrammar.NOT_EQUAL_ONE
                            + SQLGrammar.AND + GalleryStore.ScanFace.IS_RECYCLED + SQLGrammar.NOT_EQUAL_ONE
                            + SQLGrammar.AND + GalleryStore.ScanFace.NO_FACE + SQLGrammar.NOT_EQUAL_ONE
                            + SQLGrammar.AND + GalleryStore.ScanFace.FACE_REMOVABLE + SQLGrammar.NOT_EQUAL_ONE
                            + SQLGrammar.AND + SQLGrammar.LEFT_BRACKETS + GalleryStore.ScanFace.FACE_VISIBLE + SQLGrammar.EQUAL_ONE
                            + SQLGrammar.OR + GalleryStore.ScanFace.GROUP_ID + SQLGrammar.NOT_EQUAL_ZERO + SQLGrammar.RIGHT_BRACKETS
                )
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor != null) && (cursor.count > 0)) {
                    return true
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "hadNoGroupFace, e:$it" }
        }
        return false
    }

    /**
     * 获取所有已经扫描过的图片/视频信息
     *
     * @return ArrayList<FaceInfo>
     */
    @JvmStatic
    fun getAllScannedItems(): ArrayList<FaceInfo> {
        return getScanFaceInfo(null)
    }

    private fun getAllInvisibleFaceScannImage(noFaceExclude: Boolean): ArrayList<BaseImageInfo> {
        val list = ArrayList<BaseImageInfo>()
        val sb = StringBuilder()
        sb.append(GalleryStore.ScanFace.FACE_VISIBLE + SQLGrammar.NOT_EQUAL_ONE)
        sb.append(SQLGrammar.AND + GalleryStore.ScanFace.SCAN_DATE + SQLGrammar.IS_NULL)
        if (noFaceExclude) {
            sb.append(SQLGrammar.AND + GalleryStore.ScanFace.NO_FACE + SQLGrammar.NOT_EQUAL_ONE)
        }
        sb.append(SQLGrammar.GROUP_BY + GalleryStore.ScanFace.TAB + SQLGrammar.DOT + GalleryStore.ScanFace.DATA)
        getScanFaceInfo(sb.toString()).let { list.addAll(it) }
        return list
    }

    /**
     * 获取已经扫描过并且不在回收站中的图片/视频信息
     *
     * @return ArrayList<FaceInfo>
     */
    @JvmStatic
    fun getScannedItemsWithoutRecycled(): ArrayList<FaceInfo> {
        val list = ArrayList<FaceInfo>()
        val whereClause = GalleryStore.ScanFace.IS_RECYCLED + SQLGrammar.NOT_EQUAL_ONE
        getScanFaceInfo(whereClause).let { list.addAll(it) }
        return list
    }

    /**
     * call this method, we can't keep user unmounted and recycle face info
     */
    @JvmStatic
    fun deleteInvalidFace(updateVersion: Int) {
        kotlin.runCatching {
            val whereClause = (SQLGrammar.LEFT_BRACKETS + GalleryStore.ScanFace.INVALID + " = 1 OR "
                    + GalleryStore.ScanFace.IS_RECYCLED + " = 1) AND "
                    + GalleryStore.ScanFace.MODEL_VERSION + SQLGrammar.LESS_THAN + updateVersion)
            val req = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setWhere(whereClause)
                .build()
            DataAccess.getAccess().delete(req)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteInvalidFace, e:$it" }
        }
    }

    /**
     * 获取图片中人脸的状态
     *
     * @return Map<String, Boolean>
     */
    @JvmStatic
    fun getFaceStatus(): Map<String, Boolean> {
        val map: MutableMap<String, Boolean> = HashMap()
        kotlin.runCatching {
            //TagsStore.FacesImage.getContentUri()
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(arrayOf(GalleryStore.ScanFace.DATA, GalleryStore.ScanFace.NO_FACE))
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return map
                }
                val indexPath = cursor.getColumnIndex(GalleryStore.ScanFace.DATA)
                val indexNoFace = cursor.getColumnIndex(GalleryStore.ScanFace.NO_FACE)
                while (cursor.moveToNext()) {
                    map[cursor.getString(indexPath)] = cursor.getInt(indexNoFace) == 0
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getFaceStatus, e:$it" }
        }
        return map
    }

    /**
     * 根据filepath获取人物图片信息
     *
     * @param filePath
     *
     * @return ArrayList<CvFaceInfo>
     */
    @JvmStatic
    fun getFaceInfoByFilePath(filePath: String): ArrayList<CvFaceInfo> {
        val list = ArrayList<CvFaceInfo>()
        kotlin.runCatching {
            //TagsStore.FaceCluster.getContentUri()
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(FACE_QUERY_PROJECT_RECT)
                .setWhere(GalleryStore.ScanFace.DATA + SQLGrammar.EQUAL_TO)
                .setWhereArgs(arrayOf(filePath))
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return list
                }
                list.addAll(CvFaceInfo.buildFaceRectInfoList(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getFaceInfoByFilePath, e:$it" }
        }
        return list
    }

    /**
     * 获取IS_MANUAL =1的所有记录的group id,group name
     *
     * @return ArrayList<Long>
     */
    @JvmStatic
    fun getAllManualPersonId(): ArrayList<Long> {
        val list = ArrayList<Long>()
        kotlin.runCatching {
            val project = arrayOf(GalleryStore.ScanFace._ID)
            val sb = java.lang.StringBuilder()
            sb.append(GalleryStore.ScanFace.IS_MANUAL + SQLGrammar.EQUAL + ScanFaceGroupColumns.HAS_MANUAL_STATE)
            sb.append(SQLGrammar.OR + GalleryStore.ScanFace.GROUP_NAME + SQLGrammar.IS_NOT_NULL)
            sb.append(SQLGrammar.AND + GalleryStore.ScanFace.GROUP_NAME + " != ''")
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(project)
                .setWhere(sb.toString())
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if (cursor != null) {
                    while (cursor.moveToNext()) {
                        list.add(cursor.getLong(0))
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllManualPersonId, e:$it" }
        }
        return list
    }

    /**
     * 获取no_face !=1 and face_removable !=1的所有记录的group id
     *
     * @return ArrayList<Int>?
     */
    @JvmStatic
    fun getAllGroupIds(): ArrayList<Int>? {
        val whereClause = (GalleryStore.ScanFace.NO_FACE + SQLGrammar.NOT_EQUAL_ONE
                + SQLGrammar.AND + GalleryStore.ScanFace.FACE_REMOVABLE + " != 1)"
                + " group by (" + GalleryStore.ScanFace.GROUP_ID)
        val req = QueryReq.Builder<ArrayList<Int>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCAN_FACE)
            .setProjection(arrayOf(SQLGrammar.DISTINCT + GalleryStore.ScanFace.GROUP_ID))
            .setWhere(whereClause)
            .setConvert(IntListConverter(GalleryStore.ScanFace.GROUP_ID))
            .build()
        return DataAccess.getAccess().query(req)
    }

    /**
     * 获取no_face !=1 and face_removable !=1 and groupid>GROUP_ID_2的所有记录的group id
     *
     * @return ArrayList<Int>
     */
    @JvmStatic
    fun getAllNoFeatureGroupIds(): ArrayList<Int> {
        val list = ArrayList<Int>()
        kotlin.runCatching {
            val whereClause = (GalleryStore.ScanFace.NO_FACE + SQLGrammar.NOT_EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.ScanFace.FACE_REMOVABLE + SQLGrammar.NOT_EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.ScanFace.GROUP_ID + SQLGrammar.GREATER_THAN + GalleryScanUtils.GROUP_ID_2
                    + ") group by (" + GalleryStore.ScanFace.GROUP_ID)
            val NUMBER_OF_FEATURES = "number_of_features"
            val project = arrayOf(
                "DISTINCT " + GalleryStore.ScanFace.GROUP_ID,
                "sum(case when (" + GalleryStore.ScanFace.FEATURE + " IS NOT NULL) then 1 else 0 end) AS "
                        + NUMBER_OF_FEATURES
            )
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(project)
                .setWhere(whereClause)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if (cursor != null) {
                    val featuresIndex = cursor.getColumnIndex(NUMBER_OF_FEATURES)
                    val groupIdIndex = cursor.getColumnIndex(GalleryStore.ScanFace.GROUP_ID)
                    while (cursor.moveToNext()) {
                        if (cursor.getInt(featuresIndex) == 0) {
                            list.add(cursor.getInt(groupIdIndex))
                        }
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllNoFeatureGroupIds, e:$it" }
        }
        return list
    }

    /**
     * 删除idlist中的personid记录
     *
     * @param idList
     *
     */
    @JvmStatic
    fun deletePersonId(idList: List<Long>) {
        if ((idList == null) || (idList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL) { "deletePersonId, idList is empty!" }
            return
        }
        kotlin.runCatching {
            val sb = StringBuilder()
            sb.append(GalleryStore.ScanFace._ID + " IN (")
            for (personId in idList) {
                sb.append(personId)
                sb.append(SQLGrammar.COMMA)
            }
            sb.deleteCharAt(sb.length - 1)
            sb.append(SQLGrammar.RIGHT_BRACKETS)
            val req = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setWhere(sb.toString())
                .build()
            DataAccess.getAccess().delete(req)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deletePersonId, e:$it" }
        }
    }

    /**
     * delete record by file path
     *
     * @param fileList
     * @return void
     */
    @JvmStatic
    fun deleteMediaFile(fileList: ArrayList<String>) {
        if ((fileList == null) || (fileList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL) { "deleteMediaFile, fileList is empty!" }
            return
        }
        val size = fileList.size
        GLog.d(TAG, LogFlag.DL) { "deleteMediaFile, fileList.mSize: $size" }
        kotlin.runCatching {
            val sb = java.lang.StringBuilder()
            sb.append(GalleryStore.ScanFace.DATA + " IN (")
            for (filePath in fileList) {
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(filePath)
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(SQLGrammar.COMMA)
            }
            sb.deleteCharAt(sb.length - 1)
            sb.append(SQLGrammar.RIGHT_BRACKETS)
            val req = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setWhere(sb.toString())
                .build()
            DataAccess.getAccess().delete(req)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteMediaFile, e:$it" }
        }
    }

    /**
     * 删除所有is_recycle = 1的记录
     *
     */
    @JvmStatic
    fun deleteAllRecycledFile() {
        GLog.d(TAG, LogFlag.DL) { "deleteAllRecycledFile" }
        kotlin.runCatching {
            val whereClause = (GalleryStore.ScanFace.IS_RECYCLED + SQLGrammar.EQUAL_ONE
                    + SQLGrammar.AND + GalleryStore.ScanFace.INVALID + SQLGrammar.NOT_EQUAL_ONE)
            DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setWhere(whereClause)
                .build().exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteAllRecycledFile, e:$it" }
        }
    }

    /**
     * delete record by file path
     *
     * @return void
     */
    @JvmStatic
    fun deleteImage(imageList: ArrayList<BaseImageInfo>) {
        if ((imageList == null) || (imageList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL) { "deleteImage, imageList is empty!" }
            return
        }
        kotlin.runCatching {
            val sb = StringBuilder()
            sb.append(GalleryStore.ScanFace.DATA + " IN (")
            for (image in imageList) {
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(image.mFilePath)
                sb.append(SQLGrammar.DOUBLE_QUOTE)
                sb.append(SQLGrammar.COMMA)
            }
            sb.deleteCharAt(sb.length - 1)
            sb.append(SQLGrammar.RIGHT_BRACKETS)
            val req = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(sb.toString())
                .build()
            DataAccess.getAccess().delete(req)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteImage, e:$it" }
        }
    }

    /**
     * 获取UpdateReqList
     *
     * @param imageList
     *
     * @return List<UpdateReq>
     */
    @JvmStatic
    fun getFaceUpdateReqList(imageList: List<BaseImageInfo>): List<UpdateReq> {
        val result = 0
        val operations: MutableList<UpdateReq> = ArrayList()
        if ((imageList == null) || (imageList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL) { "changedImage, imageList is empty!" }
            return operations
        }
        val size = imageList.size
        GLog.d(TAG, LogFlag.DL) { "changedImage, imageList.size: $size" }
        for (image in imageList) {
            val value = ContentValues()
            if (image.mFilePath != null && image.mNewFilePath != null
                && !image.mFilePath.equals(image.mNewFilePath, ignoreCase = true)) {
                value.put(GalleryStore.ScanFace.DATA, image.mNewFilePath)
            }
            value.put(GalleryStore.ScanFace.IS_RECYCLED, false)
            val updateReq = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setConvert { value }
                .setWhere(GalleryStore.ScanFace.DATA + SQLGrammar.EQUAL_TO)
                .setWhareArgs(arrayOf(image.mFilePath))
                .build()
            operations.add(updateReq)

            val updatePersonPetGroupReq = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                .setConvert { value }
                .setWhere(GalleryStore.PersonPetGroupColumns.DATA + SQLGrammar.EQUAL_TO)
                .setWhareArgs(arrayOf(image.mFilePath))
                .build()
            operations.add(updatePersonPetGroupReq)

            val updatePetReq = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setConvert { value }
                .setWhere(PetColumns.DATA + SQLGrammar.EQUAL_TO)
                .setWhareArgs(arrayOf(image.mFilePath))
                .build()
            operations.add(updatePetReq)
        }
        return operations
    }

    /**
     * update image invalid
     */
    @JvmStatic
    fun updateImageInvalid(imageList: ArrayList<BaseImageInfo>) {
        if ((imageList == null) || (imageList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL) { "updateImageInvalid, imageList is empty!" }
            return
        }
        val size = imageList.size
        GLog.d(TAG, LogFlag.DL) { "updateImageInvalid, imageList.size: $size" }
        val invalidValue = ContentValues()
        var invalidSb: StringBuilder? = null
        val validValue = ContentValues()
        var validSb: StringBuilder? = null
        for (image in imageList) {
            if (image.mInvalid) {
                if (invalidSb == null) {
                    invalidSb = StringBuilder()
                    invalidSb.append(GalleryStore.ScanFace.DATA + " IN (")
                    invalidValue.put(GalleryStore.ScanFace.INVALID, true)
                    invalidValue.put(GalleryStore.ScanFaceView.IS_CHOSEN, false)
                }
                invalidSb.append(SQLGrammar.DOUBLE_QUOTE)
                invalidSb.append(image.mFilePath)
                invalidSb.append(SQLGrammar.DOUBLE_QUOTE)
                invalidSb.append(SQLGrammar.COMMA)
            } else {
                if (validSb == null) {
                    validSb = StringBuilder()
                    validSb.append(GalleryStore.ScanFace.DATA + " IN (")
                    validValue.put(GalleryStore.ScanFace.INVALID, false)
                }
                validSb.append(SQLGrammar.DOUBLE_QUOTE)
                validSb.append(image.mFilePath)
                validSb.append(SQLGrammar.DOUBLE_QUOTE)
                validSb.append(SQLGrammar.COMMA)
            }
        }
        if (invalidSb != null) {
            kotlin.runCatching {
                invalidSb.deleteCharAt(invalidSb.length - 1)
                invalidSb.append(SQLGrammar.RIGHT_BRACKETS)
                UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(invalidSb.toString()).setConvert { aVoid: Void? -> invalidValue }.build().exec()
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "updateImageInvalid invalidSb, e:$it" }
            }
        }
        if (validSb != null) {
            kotlin.runCatching {
                validSb.deleteCharAt(validSb.length - 1)
                validSb.append(SQLGrammar.RIGHT_BRACKETS)
                UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(validSb.toString()).setConvert { aVoid: Void? -> validValue }.build().exec()
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "updateImageInvalid validSb, e:$it" }
            }
        }
    }

    /**
     * update image invalid and media id
     *
     * @param imageList
     * @return int
     */
    @JvmStatic
    fun updateImageInvalidWithMediaId(imageList: ArrayList<BaseImageInfo>): Int {
        var result = 0
        if ((imageList == null) || (imageList.isEmpty())) {
            GLog.w(TAG, LogFlag.DL) { "updateImageInvalidWithMediaId, imageList is empty!" }
            return result
        }
        val size = imageList.size
        GLog.d(TAG, LogFlag.DL) { "updateImageInvalidWithMediaId, imageList.size: $size" }
        val operations: MutableList<UpdateReq> = ArrayList()
        for (image in imageList) {
            val updateValue = ContentValues()
            val builder = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
            if (image.mInvalid) {
                updateValue.put(GalleryStore.ScanFace.INVALID, true)
                updateValue.put(GalleryStore.ScanFaceView.IS_CHOSEN, false)
                updateValue.put(GalleryStore.ScanFace.DATA, image.mFilePath)
                //image has a media id must not be recycled
                updateValue.put(GalleryStore.ScanFace.IS_RECYCLED, false)
            } else {
                updateValue.put(GalleryStore.ScanFace.INVALID, false)
                updateValue.put(GalleryStore.ScanFace.DATA, image.mFilePath)
                //image has a media id must not be recycled
                updateValue.put(GalleryStore.ScanFace.IS_RECYCLED, false)
            }
            builder.setConvert { aVoid: Void? -> updateValue }
            builder.setWhere(GalleryStore.ScanFace.DATA + SQLGrammar.EQUAL_TO)
                .setWhareArgs(arrayOf(image.mFilePath))
            operations.add(builder.build())
        }
        if (operations.isEmpty().not()) {
            kotlin.runCatching {
                val results = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build().exec()
                result = results.size
                GLog.d(TAG, LogFlag.DL) { "updateImageInvalidWithMediaId, result: $result" }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "updateImageInvalidWithMediaId, e: $it" }
            }
        }
        return result
    }

    /**
     * 通过 groupId 查询 SCAN_FACE_GROUP_VIEW 或 PET_GROUP_VIEW表，返回该人物或宠物 group 里的图片数量
     */
    @JvmStatic
    fun getFaceCountByGroupId(groupId: Long, albumSetType: Int): Int {
        var count = 0
        kotlin.runCatching {
            val whereClause = StringBuilder().append(getGroupViewIdFieldName(albumSetType) + SQLGrammar.EQUAL + groupId)
            if (albumSetType == TYPE_PET_ALBUM_SET) {
                whereClause.append(
                    SQLGrammar.AND + PersonPetListViewColumns.ROLE_TYPE + SQLGrammar.EQUAL
                            + SQLGrammar.QUOTE + getRoleType(albumSetType) + SQLGrammar.QUOTE
                )
            }
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GroupHelper.getTableGroupViewType(albumSetType))
                .setProjection(arrayOf(ScanFaceGroupColumns.NUM_ALL_FACES))
                .setWhere(whereClause.toString())
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return 0
                }
                val facesIndex = cursor.getColumnIndex(GalleryStore.ScanFaceGroupView.NUM_ALL_FACES)
                if (cursor.moveToFirst()) {
                    count = cursor.getInt(facesIndex)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getFaceCountByGroupId, e: $it" }
        }
        return count
    }

    /**
     * get group info
     *
     * @param groupId
     * @param albumSetType
     * @return GroupInfo?
     */
    @JvmStatic
    private fun getGroupInfo(groupId: Long, albumSetType: Int): GroupInfo? {
        var info: GroupInfo? = null
        kotlin.runCatching {
            val whereClause = StringBuilder().append(getGroupViewIdFieldName(albumSetType) + SQLGrammar.EQUAL + groupId)
            if (albumSetType == TYPE_PET_ALBUM_SET) {
                whereClause.append(
                    SQLGrammar.AND + PersonPetListViewColumns.ROLE_TYPE + SQLGrammar.EQUAL
                            + SQLGrammar.QUOTE + getRoleType(albumSetType) + SQLGrammar.QUOTE
                )
            }
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GroupHelper.getTableGroupViewType(albumSetType))
                .setProjection(getGroupQueryProject(albumSetType))
                .setWhere(whereClause.toString())
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return info
                }
                info = buildGroupInfo(cursor, albumSetType)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getGroupInfo, e: $it" }
        }
        return info
    }

    /**
     * get group name
     *
     * @param groupId
     * @return String?
     */
    @JvmStatic
    fun getGroupName(groupId: Long, albumSetType: Int): String? {
        val groupIdField = getGroupIdFieldName(albumSetType)
        val groupNameField = getGroupNameFieldName(albumSetType)
        return DataAccess.getAccess().query(
            QueryReq.Builder<String>().setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableGroupViewType(albumSetType))
                .setProjection(arrayOf(groupNameField))
                .setWhere(groupIdField + SQLGrammar.EQUAL + groupId)
                .setConvert(StringConverter(groupNameField)).build()
        )
    }

    @JvmStatic
    fun getGroupInfos(): LongSparseArray<GroupInfo> {
        val infos = LongSparseArray<GroupInfo>()
        val groupInfolist = ArrayList<GroupInfo>()
        kotlin.runCatching {
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(GROUP_QUERY_PROJECT_IN_FACECLUSTER)
                .setWhere("1) GROUP BY (" + GalleryStore.ScanFace.GROUP_ID)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return infos
                }
                groupInfolist.addAll(GroupInfo.buildGroupInfoListInFaceCluster(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getGroupInfos, e: $it" }
        }
        for (group in groupInfolist) {
            infos.put(group.mGroupId, group)
        }
        return infos
    }

    /**
     * get the max group id
     * @param albumSetType
     * @return long
     */
    @JvmStatic
    private fun getMaxGroupIdLessThanBaseId(albumSetType: Int): Long {
        var groupId: Long = 0
        kotlin.runCatching {
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableType(albumSetType))
                .setProjection(arrayOf(getGroupIdFieldName(albumSetType)))
                .setWhere(
                    GroupHelper.getGroupViewIdFieldName(albumSetType)
                            + SQLGrammar.LESS_THAN + GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE
                )
                .setOrderBy(getGroupIdFieldName(albumSetType) + SQLGrammar.DESC)
                .setLimit(0.toString() + SQLGrammar.COMMA + 1)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor != null) && cursor.moveToFirst()) {
                    groupId = (cursor.getInt(0) + 1).toLong()
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getMaxGroupIdLessThanBaseId, e: $it" }
        }
        //valid id from GROUP_ID_1
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY)) {
            if (albumSetType == TYPE_PERSON_PET_GROUP_ALBUM_SET) {
                if (groupId < GalleryScanUtils.GROUP_ID_1) {
                    groupId = GalleryScanUtils.GROUP_ID_1.toLong()
                }
            } else {
                if (groupId < GalleryScanUtils.GROUP_ID_3) {
                    groupId = GalleryScanUtils.GROUP_ID_3.toLong()
                }
            }
        } else {
            if (groupId < GalleryScanUtils.GROUP_ID_3) {
                groupId = GalleryScanUtils.GROUP_ID_3.toLong()
            }
        }
        return groupId
    }

    /**
     * rename group
     *
     * @param context
     * @param groupId
     * @param name
     * @param albumSetType
     * @return Pair<Long, Integer>
     */
    @JvmStatic
    fun renameGroup(context: Context?, groupId: Long, name: String?, albumSetType: Int): Pair<Long, Int> {
        val updateGroupId =
            if (groupId > GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE) getMaxGroupIdLessThanBaseId(albumSetType) else groupId
        var count = 0
        kotlin.runCatching {
            val current = System.currentTimeMillis()
            count = UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(GroupHelper.getTableType(albumSetType))
                .setWhere(GroupHelper.getGroupIdFieldName(albumSetType) + SQLGrammar.EQUAL + groupId)
                .setConvert { aVoid: Void? ->
                    val cv = ContentValues()
                    when (albumSetType) {
                        GroupHelper.TYPE_PERSON_ALBUM_SET, GroupHelper.TYPE_PET_ALBUM_SET -> {
                            cv.put(ScanFaceGroupColumns.GROUP_NAME, name)
                            cv.put(ScanFaceGroupColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                            cv.put(ScanFaceGroupColumns.GROUP_ID, updateGroupId)
                            cv.put(ScanFaceGroupColumns.MANUAL_DATE, current)
                        }

                        GroupHelper.TYPE_PERSON_PET_GROUP_ALBUM_SET -> {
                            cv.put(GalleryStore.PersonPetGroupListView.MERGE_GROUP_NAME, name)
                            cv.put(GalleryStore.PersonPetGroupListView.MERGE_GROUP_ID, updateGroupId)
                        }

                        else -> {}
                    }
                    cv
                }
                .build()
                .exec()
            GalleryScanUtils.setLastManualTime(context, current)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "renameGroup, e: $it" }
        }
        return Pair(updateGroupId, count)
    }

    /**
     * 创建合照时,查询已经存在的有效合照
     *
     * @param inputPersonGroupIdList 选中的人物groupIds集合
     * @param inputPetGroupIdList 选中的宠物groupIds集合
     *
     * @return Pair<Boolean, Long>是否已经存在合照
     */
    @JvmStatic
    private fun queryExistPersonPetGroup(
        inputPersonGroupIdList: MutableList<Long>,
        inputPetGroupIdList: MutableList<Long>,
        existPersonPetGroupDataInfoList: MutableList<FinalGroupDataInfo>
    ): MutableList<FinalGroupDataInfo> {
        val where = buildExistPersonPetGroupWhereClause(inputPersonGroupIdList, inputPetGroupIdList)

        kotlin.runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET))
                .setProjection(
                    arrayOf(
                        getGroupIdFieldName(TYPE_PERSON_PET_GROUP_ALBUM_SET),
                        PersonPetGroup.DATA,
                        PersonPetGroup.MERGE_GROUP_NAME,
                        PersonPetGroup.SCORE,
                        PersonPetGroup.IS_DISBAND
                    )
                )
                .setWhere(where.toString())
                .setConvert(CursorConvert())
                .build().exec().use { cursor ->
                    if ((cursor != null) && (cursor.count > 0)) {
                        val mergeGroupIdIndex = cursor.getColumnIndex(PersonPetGroup.MERGE_GROUP_ID)
                        val filePathIndex = cursor.getColumnIndex(PersonPetGroup.DATA)
                        val mergeGroupNameIndex = cursor.getColumnIndex(PersonPetGroup.MERGE_GROUP_NAME)
                        val scoreIndex = cursor.getColumnIndex(PersonPetGroup.SCORE)
                        val isDisbandIndex = cursor.getColumnIndex(PersonPetGroup.IS_DISBAND)
                        while (cursor.moveToNext()) {
                            val mergeGroupId = cursor.getLong(mergeGroupIdIndex)
                            val filePath = cursor.getString(filePathIndex)
                            val mergeGroupName = cursor.getString(mergeGroupNameIndex)
                            val score = cursor.getFloat(scoreIndex)
                            val isDisband = cursor.getInt(isDisbandIndex)
                            existPersonPetGroupDataInfoList.add(FinalGroupDataInfo().apply {
                                this.mergeGroupId = mergeGroupId
                                this.filePath = filePath
                                this.mergeGroupName = mergeGroupName
                                this.score = score
                                this.isDisband = isDisband
                            })
                        }
                    } else {
                        GLog.w(TAG, LogFlag.DL) { "queryExistPersonPetGroup, Person Pet Group is not exist." }
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "queryExistPersonPetGroup, e=$it" }
        }
        return existPersonPetGroupDataInfoList
    }

    private fun buildExistPersonPetGroupWhereClause(
        inputPersonGroupIdList: MutableList<Long>,
        inputPetGroupIdList: MutableList<Long>
    ): StringBuilder {
        // 构建人物group ids
        val sbInputPersonGroupIds = buildIdStringByList(inputPersonGroupIdList)

        // 构建宠物group ids
        val sbInputPetGroupIds = buildIdStringByList(inputPetGroupIdList)
        val personGroupIdsWhere = StringBuilder()
        if (sbInputPersonGroupIds.isNullOrEmpty()) {
            personGroupIdsWhere.append(SQLGrammar.LEFT_BRACKETS)
                .append(PersonPetGroup.PERSON_GROUP_IDS + SQLGrammar.IS_EMPTY)
                .append(SQLGrammar.OR)
                .append(PersonPetGroup.PERSON_GROUP_IDS + SQLGrammar.IS_NULL)
                .append(SQLGrammar.RIGHT_BRACKETS)
        } else {
            personGroupIdsWhere.append(
                PersonPetGroup.PERSON_GROUP_IDS + SQLGrammar.EQUAL + SQLGrammar.QUOTE + sbInputPersonGroupIds + SQLGrammar.QUOTE
            )
        }

        val petGroupIdsWhere = StringBuilder()
        if (sbInputPetGroupIds.isNullOrEmpty()) {
            petGroupIdsWhere.append(SQLGrammar.LEFT_BRACKETS)
                .append(PersonPetGroup.PET_GROUP_IDS + SQLGrammar.IS_EMPTY)
                .append(SQLGrammar.OR)
                .append(PersonPetGroup.PET_GROUP_IDS + SQLGrammar.IS_NULL)
                .append(SQLGrammar.RIGHT_BRACKETS)
        } else {
            petGroupIdsWhere.append(PersonPetGroup.PET_GROUP_IDS + SQLGrammar.EQUAL + SQLGrammar.QUOTE + sbInputPetGroupIds + SQLGrammar.QUOTE)
        }

        val where = StringBuilder()
        where.append(
            personGroupIdsWhere.toString() + SQLGrammar.AND
                    + petGroupIdsWhere + SQLGrammar.AND
                    + PersonPetGroup.DATA + SQLGrammar.IS_NOT_EMPTY + SQLGrammar.AND + PersonPetGroup.DATA + SQLGrammar.IS_NOT_NULL
                    + SQLGrammar.AND + DatabaseUtils.getDataValidWhere()
                    + SQLGrammar.AND + PersonPetGroup.IS_RECYCLED + SQLGrammar.EQUAL_ZERO
        )
        return where
    }

    /**
     * 创建合照时,是否数据库中之前存在过空的记录
     *
     * @param inputPersonGroupIdList 选中的人物groupIds集合
     * @param inputPetGroupIdList 选中的宠物groupIds集合
     *
     * @return EmptyGroupResult 查询出的空合照信息
     */
    @JvmStatic
    private fun isExistEmptyPersonPetGroup(inputPersonGroupIdList: MutableList<Long>, inputPetGroupIdList: MutableList<Long>): EmptyGroupResult {
        // 构建人物group ids
        val sbInputPersonGroupIds = buildIdStringByList(inputPersonGroupIdList)

        // 构建宠物group ids
        val sbInputPetGroupIds = buildIdStringByList(inputPetGroupIdList)
        kotlin.runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET))
                .setProjection(arrayOf(getGroupIdFieldName(TYPE_PERSON_PET_GROUP_ALBUM_SET), PersonPetGroup.DATA, PersonPetGroup.IS_DISBAND))
                .setWhere(
                    PersonPetGroup.PERSON_GROUP_IDS + SQLGrammar.EQUAL_TO + SQLGrammar.AND
                            + PersonPetGroup.PET_GROUP_IDS + SQLGrammar.EQUAL_TO
                )
                .setWhereArgs(arrayOf(sbInputPersonGroupIds.toString(), sbInputPetGroupIds.toString()))
                .setConvert(CursorConvert())
                .build().exec().use { cursor ->
                    if ((cursor != null) && (cursor.count == 1) && (cursor.moveToNext())) {
                        val mergeGroupIdIndex = cursor.getColumnIndex(PersonPetGroup.MERGE_GROUP_ID)
                        val dataIndex = cursor.getColumnIndex(PersonPetGroup.DATA)
                        val isDisbandIndex = cursor.getColumnIndex(PersonPetGroup.IS_DISBAND)
                        val mergeGroupId = cursor.getLong(mergeGroupIdIndex)
                        val filepath = cursor.getString(dataIndex)
                        val isDisband = cursor.getInt(isDisbandIndex)
                        if (filepath.isNullOrEmpty()) {
                            return EmptyGroupResult(true, mergeGroupId, isDisband)
                        }
                    } else {
                        GLog.w(TAG, LogFlag.DL) { "These roles are not exsit empty group." }
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "isPersonPetGroupExist, e=$it" }
        }
        return EmptyGroupResult(false)
    }

    /**
     * 删除群组表中的空记录
     *
     * @param personGroupIdList 空记录对应的人物groupIds集合
     * @param petGroupIdList 空记录的宠物groupIds集合
     *
     * @return void
     */
    private fun deleteEmptyPersonPetGroup(personGroupIdList: MutableList<Long>, petGroupIdList: MutableList<Long>) {
        kotlin.runCatching {
            // 人物group ids
            val sbInputPersonGroupIds = buildIdStringByList(personGroupIdList)

            // 宠物group ids
            val sbInputPetGroupIds = buildIdStringByList(petGroupIdList)
            val req = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                .setWhere(
                    PersonPetGroup.PERSON_GROUP_IDS + SQLGrammar.EQUAL_TO + SQLGrammar.AND
                            + PersonPetGroup.PET_GROUP_IDS + SQLGrammar.EQUAL_TO + SQLGrammar.AND
                            + SQLGrammar.LEFT_BRACKETS + PersonPetGroup.DATA + SQLGrammar.IS_EMPTY + SQLGrammar.OR
                            + PersonPetGroup.DATA + SQLGrammar.IS_NULL + SQLGrammar.RIGHT_BRACKETS
                )
                .setWhereArgs(arrayOf(sbInputPersonGroupIds.toString(), sbInputPetGroupIds.toString()))
                .build()
            DataAccess.getAccess().delete(req)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteEmptyPersonPetGroup, e=$it" }
        }
    }

    /**
     * 创建合照
     *
     * 一.从人宠群组表中查询是否已经存在对应角色的合照,如果已经存在,则直接返回相关状态码,后边就不必再去插入表了
     * CREATE_PERSON_GROUP_RESULT_CODE_CREATED_GROUP_EXIST 要创建的合照已经存在
     *
     * 二.如果没有查到合照,则需要执行创建合照:即向群组表中插入合照记录,具体逻辑如下:
     * 1.从人物表和宠物中将选中的每一个groupid记录的filepath取出来，放到filePathSet，然后将这些filePathSet放到list中,用于后面取交集用
     * 2.对filePathList取交集，交集的意思是：交集中的filepath会同时存在于选中的groupid的记录中,
     * 此时filepath对应图片可能包含这个groupId的所有照片:单人照,两个角色合照，三个角色合照和多个角色合照
     * 3.根据交集,找到仅包含选中的group id的合照对应的group id列表
     * 遍历交集list中的filepath,对于每一个filepath,去人物表和宠物表所有记录中查询这个filepath对应的所有groupid,得到一个groupid列表
     * (因为一张合照图片可能同时存在于人物表和宠物表中的多个groupid的记录中)
     * 举例说明:
     * 如果选中的人(groupid=5)+宠(groupid=6)创建合照:那交集中的filepath对应的图片会同时存在于人物表和宠物表对应的groupid记录中 ,那么查到的列表记录可能是
     * groupid5,groupid6,groupid7,groupid8...那么这张图就不是仅包含这个人+宠的合照,如果这张图仅仅是这个人+宠的合照,那么查到的列表记录只能是groupid5,groupid6,
     * 仅包含这个人+宠的合照,不会存在于第三个不同的groupid记录中
     *
     * 4.将第3步中找到的groupid 列表和最开始传入的group id列表作比较,如果相同,
     * 表明满足条件的合照已经找到,将相关信息封装成finalGroupDataInfo数据结构并保存到finalGroupDataInfoList中
     * 5.查找到满足条件的合照后,构建合照数据库记录信息,插入到群组表中
     *
     * @param context 上下文环境
     * @param groupIdsInfoMap 创建合照时选中的groupIds集合
     *
     * @return Pair<Integer, Integer>?
     */
    @JvmStatic
    fun createPersonPetGroup(context: Context, groupIdsInfoMap: Map<Int, ArrayList<Path>>?): Pair<Int, Int>? {
        var pair: Pair<Int, Int>? = null
        if (groupIdsInfoMap.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "createPersonPetGroup, groupIdsInfoMap is empty!" }
            return Pair(0, GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_FAIL)
        }
        val inputPersonPetDataInfo = BasePersonPetDataInfo()
        val filePathList: MutableList<Set<String>> = mutableListOf()
        /**
         * 从选中的group id列表中分别找到人物group ids 和宠物 group ids,并分别放入对应的list中
         */
        groupIdsInfoMap.forEach { (albumSetType: Int, groupIdList: ArrayList<Path>) ->
            GLog.d(TAG, LogFlag.DL) { "createPersonPetGroup, albumSetType=$albumSetType, groupIdList=$groupIdList" }
            groupIdList.forEach { path ->
                val groupId = path.suffix.toLong()
                buildGroupIdListByAlbumSetType(inputPersonPetDataInfo, albumSetType, groupId)
            }
        }

        /**
         * 如果没有查到合照,则需要执行创建合照:即向群组表中插入合照记录
         *
         * 从人物表和宠物中将选中的每一个groupid记录的filepath取出来，放到filePathSet，然后将这些filePathSet放到list中,用于后面取交集用
         */
        buildFilePathList(groupIdsInfoMap, filePathList)
        /**
         * 对filePathList取交集，交集表示：交集中的filepath会同时存在于选中的groupid的记录中
         * 此时filepath对应图片可能包含这个groupId的所有照片:单人照,两个角色合照，三个角色合照和多个角色合照
         */
        val intersectionFilePaths = filePathList.takeIf { it.isNotEmpty() }?.reduce { acc, set -> acc.intersect(set) } ?: emptySet()

        /**
         * 从交集中找到仅包含最终选中的group id的合照,并把合照信息放入list
         */
        val finalGroupDataInfoList: MutableList<FinalGroupDataInfo>? =
            queryPersonPetGroupDataList(groupIdsInfoMap, inputPersonPetDataInfo.groupIdList, intersectionFilePaths.toMutableList())
        GLog.d(TAG, LogFlag.DL) { "createPersonPetGroup, finalGroupDataInfoList.size=${finalGroupDataInfoList?.size}" }
        /**
         * 没有找到合照,向数据库插入一条空数据
         */
        // 先查询要创建的合照集在表中是否存在过空的记录
        val emptyPersonPetGroupResult = isExistEmptyPersonPetGroup(inputPersonPetDataInfo.personGroupIdList, inputPersonPetDataInfo.petGroupIdList)
        if (finalGroupDataInfoList.isNullOrEmpty()) {
            // 空合照只需要提示，不需要插入数据库
            return Pair(0, GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATE_EMPTY_GROUP)
        }
        pair = updatePersonPetGroupData(
            context,
            finalGroupDataInfoList,
            inputPersonPetDataInfo.personGroupIdList,
            inputPersonPetDataInfo.petGroupIdList,
            emptyPersonPetGroupResult
        )
        return pair
    }

    private fun updatePersonPetGroupData(
        context: Context,
        finalGroupDataInfoList: MutableList<FinalGroupDataInfo>,
        inputPersonGroupIdList: MutableList<Long>,
        inputPetGroupIdList: MutableList<Long>,
        emptyPersonPetGroupResult: EmptyGroupResult
    ): Pair<Int, Int>? {
        var pair: Pair<Int, Int>? = null
        val maxScoreFinalGroupDataInfo = Collections.max(finalGroupDataInfoList, PersonPetGroupDataInfoScoreComparator())
        kotlin.runCatching {
            val existPersonPetGroupDataInfoList = mutableListOf<FinalGroupDataInfo>()
            var mergeGroupId = getMaxGroupIdLessThanBaseId(TYPE_PERSON_PET_GROUP_ALBUM_SET)

            if (emptyPersonPetGroupResult.isExistEmptyGroup) {
                // 如果存在,则需要先这条空的记录删除，后续创建合照时，沿用此mergeGroupId
                mergeGroupId = emptyPersonPetGroupResult.mergeGroupId
                deleteEmptyPersonPetGroup(inputPersonGroupIdList, inputPetGroupIdList)
            } else {
                queryExistPersonPetGroup(inputPersonGroupIdList, inputPetGroupIdList, existPersonPetGroupDataInfoList)
                GLog.d(TAG, LogFlag.DL) { "updatePersonPetGroupData, existPersonPetGroupDataInfoList.size=${existPersonPetGroupDataInfoList.size}" }
            }
            val existPersonPetGroupFilePathList = mutableListOf<String>()
            for (existPersonPetGroupDataInfo in existPersonPetGroupDataInfoList) {
                existPersonPetGroupDataInfo.filePath?.let { filePath -> existPersonPetGroupFilePathList.add(filePath) }
            }
            if (existPersonPetGroupDataInfoList.isNotEmpty()) {
                // 如果存在合照
                val existPersonPetGroupDataInfo = existPersonPetGroupDataInfoList.first()
                mergeGroupId = existPersonPetGroupDataInfo.mergeGroupId
                val mergeGroupName = existPersonPetGroupDataInfo.mergeGroupName
                val existMaxScoreFinalGroupDataInfo = Collections.max(existPersonPetGroupDataInfoList, PersonPetGroupDataInfoScoreComparator())
                GLog.d(TAG, LogFlag.DL) { "updatePersonPetGroupData, existPersonPetGroupDataInfo mergeGroupId=$mergeGroupId" }
                val newPersonPetGroupDataInfoList = mutableListOf<FinalGroupDataInfo>()
                for (groupDataInfo in finalGroupDataInfoList) {
                    // 筛选出新增的数据
                    if (existPersonPetGroupFilePathList.contains(groupDataInfo.filePath).not()) {
                        newPersonPetGroupDataInfoList.add(groupDataInfo)
                    }
                }
                GLog.d(TAG, LogFlag.DL) { "updatePersonPetGroupData, newPersonPetGroupDataInfoList.size=${newPersonPetGroupDataInfoList.size}" }
                if (newPersonPetGroupDataInfoList.isNotEmpty()) {
                    // 如果存在新增数据，则插入新增数据，mergeGroupId沿用已经存在的mergeGroupId
                    insertPersonPetGroupInfo(newPersonPetGroupDataInfoList, maxScoreFinalGroupDataInfo, mergeGroupId, context, mergeGroupName)
                }
                val needUpdateDefaultCoverGroupDataInfo =
                    buildNeedUpdateDefaultCoverGroupDataInfo(existMaxScoreFinalGroupDataInfo, maxScoreFinalGroupDataInfo)
                val isDisband = (existPersonPetGroupDataInfo.isDisband == GROUP_DISBAND_CODE)
                pair = updateExistPersonPetGroupInfo(mergeGroupId, context, needUpdateDefaultCoverGroupDataInfo, isDisband)
            } else {
                // 如果不存在合照,则把最终的信息列表中的信息存入数据库
                pair = insertPersonPetGroupInfo(finalGroupDataInfoList, maxScoreFinalGroupDataInfo, mergeGroupId, context)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updatePersonPetGroupData, e=$it" }
        }
        return pair
    }

    private fun buildNeedUpdateDefaultCoverGroupDataInfo(
        existMaxScoreFinalGroupDataInfo: FinalGroupDataInfo,
        maxScoreFinalGroupDataInfo: FinalGroupDataInfo
    ): NeedUpdateDefaultGroupCoverDataInfo {
        val needUpdateDefaultCoverGroupDataInfo = NeedUpdateDefaultGroupCoverDataInfo()
        if (existMaxScoreFinalGroupDataInfo.filePath.equals(maxScoreFinalGroupDataInfo.filePath).not()) {
            GLog.d(TAG, LogFlag.DL) { "buildNeedUpdateDefaultCoverGroupDataInfo, default cover changed." }
            needUpdateDefaultCoverGroupDataInfo.existPersonPetGroupDataInfo = existMaxScoreFinalGroupDataInfo
            needUpdateDefaultCoverGroupDataInfo.currentPersonPetGroupDataInfo = maxScoreFinalGroupDataInfo
        }
        return needUpdateDefaultCoverGroupDataInfo
    }

    private fun updateExistPersonPetGroupInfo(
        mergeGroupId: Long,
        context: Context,
        needUpdateDefaultCoverGroupDataInfo: NeedUpdateDefaultGroupCoverDataInfo,
        isDisband: Boolean
    ): Pair<Int, Int>? {
        var pair: Pair<Int, Int>? = null
        kotlin.runCatching {
            val operations: MutableList<UpdateReq> = mutableListOf()
            val whereClause = StringBuilder().append(getGroupIdFieldName(TYPE_PERSON_PET_GROUP_ALBUM_SET))
                .append(SQLGrammar.EQUAL).append(mergeGroupId)
            val cv = ContentValues()
            cv.put(PersonPetGroup.IS_DISBAND, false)
            if (isDisband) {
                cv.put(MERGE_GROUP_CREATE_DATE, System.currentTimeMillis())
            }
            val updateReq = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET))
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert { cv }
                .setWhere(whereClause.toString())
                .build()
            operations.add(updateReq)
            if ((needUpdateDefaultCoverGroupDataInfo.existPersonPetGroupDataInfo != null)
                && (needUpdateDefaultCoverGroupDataInfo.currentPersonPetGroupDataInfo != null)) {
                updateExistDefaultCoverReq(needUpdateDefaultCoverGroupDataInfo, operations)
                updateCurrentNewDefaultCoverReq(needUpdateDefaultCoverGroupDataInfo, operations)
            }
            val results = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            notifyTableChange(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET), IDao.DaoType.GALLERY)
            GalleryScanUtils.setLastManualTime(context, System.currentTimeMillis())
            val result = results.size
            pair = if (result > 0) {
                if (isDisband) {
                    Pair(result, GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_SUCCESS)
                } else {
                    Pair(result, GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATED_GROUP_EXIST)
                }
            } else {
                Pair(result, GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_FAIL)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateExistPersonPetGroupInfo, e=$it" }
        }
        return pair
    }

    private fun updateCurrentNewDefaultCoverReq(
        needUpdateDefaultCoverGroupDataInfo: NeedUpdateDefaultGroupCoverDataInfo,
        operations: MutableList<UpdateReq>
    ) {
        val updateNewDefaultCoverWhereClause = StringBuilder().append(PersonPetGroup.DATA)
            .append(SQLGrammar.EQUAL)
            .append(SQLGrammar.QUOTE)
            .append(needUpdateDefaultCoverGroupDataInfo.currentPersonPetGroupDataInfo?.filePath)
            .append(SQLGrammar.QUOTE)
        val updateNewDefaultCoverReq = UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET))
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setConvert {
                val cv = ContentValues()
                cv.put(PersonPetGroup.IS_DEFAULT_COVER, true)
                cv
            }
            .setWhere(updateNewDefaultCoverWhereClause.toString())
            .build()
        operations.add(updateNewDefaultCoverReq)
    }

    private fun updateExistDefaultCoverReq(
        needUpdateDefaultCoverGroupDataInfo: NeedUpdateDefaultGroupCoverDataInfo,
        operations: MutableList<UpdateReq>
    ) {
        val updateExistDefaultCoverWhereClause = StringBuilder().append(PersonPetGroup.DATA)
            .append(SQLGrammar.EQUAL)
            .append(SQLGrammar.QUOTE)
            .append(needUpdateDefaultCoverGroupDataInfo.existPersonPetGroupDataInfo?.filePath)
            .append(SQLGrammar.QUOTE)

        val updateExistDefaultCoverReq = UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET))
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setConvert {
                val cv = ContentValues()
                cv.put(PersonPetGroup.IS_DEFAULT_COVER, false)
                cv
            }
            .setWhere(updateExistDefaultCoverWhereClause.toString())
            .build()
        operations.add(updateExistDefaultCoverReq)
    }

    private fun insertPersonPetGroupInfo(
        finalGroupDataInfoList: MutableList<FinalGroupDataInfo>,
        maxScoreFinalGroupDataInfo: FinalGroupDataInfo,
        mergeGroupId: Long,
        context: Context,
        mergeGroupName: String = TextUtil.EMPTY_STRING
    ): Pair<Int, Int>? {
        val startTime = System.currentTimeMillis()
        var pair: Pair<Int, Int>? = null
        kotlin.runCatching {
            val operations: MutableList<InsertReq> = mutableListOf()
            for (groupDataInfo in finalGroupDataInfoList) {
                val isDefaultCover = maxScoreFinalGroupDataInfo.filePath === groupDataInfo.filePath
                val cv = buildUpdatePersonPetGroupContentValues(groupDataInfo, isDefaultCover, mergeGroupId, mergeGroupName)
                val insertReq = InsertReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET))
                    .setConvert { cv }.build()
                operations.add(insertReq)
            }
            pair = notifyInsert(operations, context)
            GLog.d(TAG, LogFlag.DL) { "insertPersonPetGroupInfo, cost time = ${System.currentTimeMillis() - startTime}" }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "insertPersonPetGroupInfo, e=$it" }
        }
        return pair
    }

    private fun notifyInsert(operations: MutableList<InsertReq>, context: Context): Pair<Int, Int>? {
        var pair: Pair<Int, Int>? = null
        kotlin.runCatching {
            val results = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            notifyTableChange(getTableType(TYPE_PERSON_PET_GROUP_ALBUM_SET), IDao.DaoType.GALLERY)
            GalleryScanUtils.setLastManualTime(context, System.currentTimeMillis())
            val result = results.size
            pair = if (result > 0) {
                Pair(result, GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_SUCCESS)
            } else {
                Pair(result, GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_FAIL)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "notifyInsert, e=$it" }
        }
        return pair
    }

    /**
     *  将list中的数据转换为添加","的StringBuilder
     *
     *  @param inputList: MutableList<Long> list中元素的类型为Long
     */
    private fun buildIdStringByList(inputList: MutableList<Long>): StringBuilder {
        val outStringBuilder = StringBuilder()
        inputList.sort()
        for ((index, value) in inputList.withIndex()) {
            if (index > 0) {
                outStringBuilder.append(SQLGrammar.COMMA)
            }
            outStringBuilder.append(value)
        }
        return outStringBuilder
    }

    /**
     *  将list中的数据转换为添加","的StringBuilder
     *
     *  @param inputList: MutableList<String> list元素类型为String
     */
    private fun buildStringByList(inputList: MutableList<String>): StringBuilder {
        val outStringBuilder = StringBuilder()
        for ((index, value) in inputList.withIndex()) {
            if (index > 0) {
                outStringBuilder.append(SQLGrammar.COMMA)
            }
            outStringBuilder.append(value)
        }
        return outStringBuilder
    }

    @Suppress("LongMethod")
    private fun buildUpdatePersonPetGroupContentValues(
        groupDataInfo: FinalGroupDataInfo,
        isDefaultCover: Boolean,
        mergeGroupId: Long,
        mergeGroupName: String = TextUtil.EMPTY_STRING
    ): ContentValues {
        val cv = ContentValues()
        val sbPersonIds = buildIdStringByList(groupDataInfo.personGroupIdList)
        val sbPetIds = buildIdStringByList(groupDataInfo.petGroupIdList)

        cv.put(PersonPetGroup.MERGE_GROUP_ID, mergeGroupId)
        cv.put(PersonPetGroup.DATA, groupDataInfo.filePath)
        cv.put(PersonPetGroup.MEDIA_TYPE, groupDataInfo.mediaType)
        cv.put(PersonPetGroup.INVALID, groupDataInfo.invalid)
        cv.put(PersonPetGroup.IS_RECYCLED, groupDataInfo.isRecycled)
        cv.put(PersonPetGroup.MERGE_GROUP_CREATE_DATE, System.currentTimeMillis())
        cv.put(PersonPetGroup.MERGE_GROUP_CREATE_TYPE, 0)
        cv.put(PersonPetGroup.MERGE_GROUP_NAME, mergeGroupName)
        cv.put(PersonPetGroup.PERSON_GROUP_IDS, sbPersonIds.toString())
        cv.put(PersonPetGroup.PET_GROUP_IDS, sbPetIds.toString())
        cv.put(PersonPetGroup.SCORE, groupDataInfo.score)
        cv.put(PersonPetGroup.IS_DEFAULT_COVER, isDefaultCover)
        cv.put(PersonPetGroup.IS_CHOSEN, false)
        cv.put(PersonPetGroup.IS_DISBAND, false)
        cv.put(PersonPetGroup.THUMB_W, groupDataInfo.thumbWidth)
        cv.put(PersonPetGroup.THUMB_H, groupDataInfo.thumbHeight)
        cv.put(PersonPetGroup.RECT_LEFT, groupDataInfo.rectLeft)
        cv.put(PersonPetGroup.RECT_TOP, groupDataInfo.rectTop)
        cv.put(PersonPetGroup.RECT_RIGHT, groupDataInfo.rectRight)
        cv.put(PersonPetGroup.RECT_BOTTOM, groupDataInfo.rectBottom)
        return cv
    }

    private fun buildFilePathList(groupIdsInfoMap: Map<Int, ArrayList<Path>>, filePathList: MutableList<Set<String>>) {
        var filePathSet: MutableSet<String> = HashSet()
        val personFilePathList = mutableListOf<Set<String>>()
        val petFilePathList = mutableListOf<Set<String>>()
        groupIdsInfoMap.forEach { (albumSetType: Int, groupIdList: ArrayList<Path>) ->
            val tableType = getTableType(albumSetType)
            groupIdList.forEach { path ->
                val groupId = path.suffix.toLong()
                filePathSet = HashSet()
                kotlin.runCatching {
                    QueryReq.Builder<Cursor>()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(tableType)
                        .setProjection(arrayOf(ScanFaceColumns.DATA))
                        .setWhere(
                            getGroupIdFieldName(albumSetType) + SQLGrammar.EQUAL + groupId
                                    + SQLGrammar.AND + DatabaseUtils.getDataValidWhere()
                                    + SQLGrammar.AND + ScanFaceColumns.IS_RECYCLED + SQLGrammar.EQUAL_ZERO
                        )
                        .setConvert(CursorConvert())
                        .build().exec().use { cursor ->
                            if ((cursor != null) && (cursor.count > 0)) {
                                val dataIndex = cursor.getColumnIndex(ScanFaceColumns.DATA)
                                while (cursor.moveToNext()) {
                                    val data = cursor.getString(dataIndex)
                                    filePathSet.add(data)
                                }
                            }
                        }
                }.onFailure {
                    GLog.e(TAG, LogFlag.DL) { "buildFilePathList,e=$it" }
                }
                GLog.d(TAG, LogFlag.DL) { "buildFilePathList,groupId=$groupId, filePathSet.size=${filePathSet.size}" }

                // 分别找到人物 和 宠物的文件路径
                when (albumSetType) {
                    TYPE_PERSON_ALBUM_SET -> personFilePathList.add(filePathSet)
                    TYPE_PET_ALBUM_SET -> petFilePathList.add(filePathSet)
                }
            }
        }
        removeInvalidFilePath(petFilePathList, personFilePathList, filePathList)
    }

    /**
     * 如果 人物 或 宠物 其中一个的文件路径结果集为空，则需要查询另一方的表中，是否存在该路径的有效扫描角色。
     * 如果存在，则需要排除该路径的图片，因为该图已经超出了选中角色的范围。
     */
    private fun removeInvalidFilePath(
        petFilePathList: MutableList<Set<String>>,
        personFilePathList: MutableList<Set<String>>,
        filePathList: MutableList<Set<String>>
    ) {
        val finalPersonFilePathList = mutableListOf<Set<String>>()
        val finalPetFilePathList = mutableListOf<Set<String>>()
        if (personFilePathList.isEmpty() && petFilePathList.isNotEmpty()) {
            // 合照没有选中人物角色时，需要把人物也存在的有效的角色的文件路径排除在外
            petFilePathList.forEach { pathSet ->
                val filePathSet =  mutableSetOf <String>()
                pathSet.forEach { path ->
                    if (!isContainThePath(path, TYPE_PERSON_ALBUM_SET)) {
                        filePathSet.add(path)
                    }
                }
                finalPetFilePathList.add(filePathSet)
            }
        } else if (personFilePathList.isNotEmpty() && petFilePathList.isEmpty()) {
            // 合照没有选中宠物角色时，需要宠物也存在的有效的角色的文件路径排除在外
            personFilePathList.forEach { pathSet ->
                val filePathSet =  mutableSetOf <String>()
                pathSet.forEach { path ->
                    if (!isContainThePath(path, TYPE_PET_ALBUM_SET)) {
                        filePathSet.add(path)
                    }
                }
                finalPersonFilePathList.add(filePathSet)
            }
        } else {
            // 人物 和 宠物都有角色时，直接添加到最终结果集里
            finalPersonFilePathList.addAll(personFilePathList)
            finalPetFilePathList.addAll(petFilePathList)
        }
        filePathList.addAll(finalPetFilePathList)
        filePathList.addAll(finalPersonFilePathList)
    }

    /**
     * 人物表 或 宠物表中，是否包含该 [path] 路径的有效角色。包含返回 true，否则返回 false。
     */
    private fun isContainThePath(path: String, albumSetType: Int): Boolean {
        kotlin.runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableType(albumSetType))
                .setProjection(arrayOf(ScanFaceColumns._ID))
                .setWhere(
                    GalleryStore.ScanFace.DATA + SQLGrammar.EQUAL_TO
                            + SQLGrammar.AND + ScanFaceColumns.FEATURE + SQLGrammar.IS_NOT_NULL
                )
                .setWhereArgs(arrayOf(path))
                .setConvert(CursorConvert())
                .build().exec().use { cursor ->
                    if ((cursor != null) && (cursor.count > 0)) {
                        return true
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "isContainThePath, e=$it" }
        }
        return false
    }

    private fun buildGroupIdListByAlbumSetType(personPetDataInfo: BasePersonPetDataInfo, albumSetType: Int, groupId: Long) {
        personPetDataInfo.groupIdList.add(groupId)
        if (albumSetType == TYPE_PERSON_ALBUM_SET) {
            personPetDataInfo.personGroupIdList.add(groupId)
        } else if (albumSetType == TYPE_PET_ALBUM_SET) {
            personPetDataInfo.petGroupIdList.add(groupId)
        }
    }

    private fun queryPersonPetGroupDataList(
        groupIdsInfoMap: Map<Int, ArrayList<Path>>,
        selectedGroupIdList: MutableList<Long>,
        intersectionFilePaths: MutableList<String>
    ): MutableList<FinalGroupDataInfo>? {
        val finalGroupDataInfoList: MutableList<FinalGroupDataInfo> = mutableListOf()
        var finalGroupDataInfo: FinalGroupDataInfo
        for (filePath in intersectionFilePaths) {
            finalGroupDataInfo = FinalGroupDataInfo()
            val foundedPersonPetDataInfo = BasePersonPetDataInfo()
            val personPetInfoList = mutableListOf<PersonPetInfo>()
            groupIdsInfoMap.forEach { (albumSetType: Int, _) ->
                queryPersonPetGroupDataInfo(filePath, albumSetType).use { cursor ->
                    if ((cursor != null) && (cursor.count) > 0) {
                        buildFinalGroupDataInfo(cursor, albumSetType, personPetInfoList, finalGroupDataInfo, foundedPersonPetDataInfo)
                    }
                }
            }
            //如果相同,表明满足条件的合照已经找到,将相关信息封装成finalGroupDataInfo数据结构并保存到finalGroupDataInfoList中
            if (isListContentsEqual(selectedGroupIdList, foundedPersonPetDataInfo.groupIdList)) {
                finalGroupDataInfo.filePath = filePath
                finalGroupDataInfo.score = PersonPetGroupCoverUtils.calcScore(personPetInfoList)
                finalGroupDataInfo.personGroupIdList = foundedPersonPetDataInfo.personGroupIdList
                finalGroupDataInfo.petGroupIdList = foundedPersonPetDataInfo.petGroupIdList
                finalGroupDataInfoList.add(finalGroupDataInfo)
            }
        }
        return finalGroupDataInfoList
    }

    private fun buildFinalGroupDataInfo(
        cursor: Cursor,
        albumSetType: Int,
        personPetInfoList: MutableList<PersonPetInfo>,
        finalGroupDataInfo: FinalGroupDataInfo,
        foundedPersonPetDataInfo: BasePersonPetDataInfo
    ) {
        val filePathIndex = cursor.getColumnIndex(ScanFaceColumns.DATA)
        val groupIdIndex = cursor.getColumnIndex(getGroupIdFieldName(albumSetType))
        val mediaTypeIndex = cursor.getColumnIndex(ScanFaceColumns.MEDIA_TYPE)
        val scoreIndex = cursor.getColumnIndex(getCoverScoreFieldName(albumSetType))
        val thumbWidthIndex = cursor.getColumnIndex(ScanFaceColumns.THUMB_W)
        val thumbHeightIndex = cursor.getColumnIndex(ScanFaceColumns.THUMB_H)
        val leftIndex = cursor.getColumnIndex(ScanFaceColumns.LEFT)
        val topIndex = cursor.getColumnIndex(ScanFaceColumns.TOP)
        val rightIndex = cursor.getColumnIndex(ScanFaceColumns.RIGHT)
        val bottomIndex = cursor.getColumnIndex(ScanFaceColumns.BOTTOM)
        val invalidIndex = cursor.getColumnIndex(ScanFaceColumns.INVALID)
        val isRecycledIndex = cursor.getColumnIndex(ScanFaceColumns.IS_RECYCLED)
        while (cursor.moveToNext()) {
            val filePath = cursor.getString(filePathIndex)
            val groupId = cursor.getLong(groupIdIndex)
            val mediaType = cursor.getInt(mediaTypeIndex)
            val score = cursor.getFloat(scoreIndex)
            val thumbWidth = cursor.getInt(thumbWidthIndex)
            val thumbHeight = cursor.getInt(thumbHeightIndex)
            val left = cursor.getInt(leftIndex)
            val top = cursor.getInt(topIndex)
            val right = cursor.getInt(rightIndex)
            val bottom = cursor.getInt(bottomIndex)
            val invalid = cursor.getInt(invalidIndex)
            val isRecycled = cursor.getInt(isRecycledIndex)
            personPetInfoList.add(PersonPetInfo().apply {
                data = filePath
                groupID = groupId.toInt()
                this.mediaType = mediaType
                roleType = getConvertRoleType(albumSetType)
                this.score = score
                this.thumbWidth = thumbWidth
                this.thumbHeight = thumbHeight
                this.left = left
                this.top = top
                this.right = right
                this.bottom = bottom
            })
            finalGroupDataInfo.thumbWidth = thumbWidth
            finalGroupDataInfo.thumbHeight = thumbHeight
            finalGroupDataInfo.run {
                rectLeft = min(rectLeft, left)
                rectTop = min(rectTop, top)
                rectRight = max(rectRight, right)
                rectBottom = max(rectBottom, bottom)
            }
            finalGroupDataInfo.mediaType = mediaType
            finalGroupDataInfo.invalid = invalid
            finalGroupDataInfo.isRecycled = isRecycled
            buildGroupIdListByAlbumSetType(foundedPersonPetDataInfo, albumSetType, groupId)
        }
    }

    /**
     * 从人物表或宠物表查找_data = filepath的记录
     */
    private fun queryPersonPetGroupDataInfo(filePath: String, albumSetType: Int): Cursor? {
        val tableType = getTableType(albumSetType)
        var cursor: Cursor? = null
        kotlin.runCatching {
            cursor = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(tableType)
                .setProjection(
                    arrayOf<String>(
                        ScanFaceColumns.DATA,
                        getGroupIdFieldName(albumSetType),
                        ScanFaceColumns.MEDIA_TYPE,
                        getCoverScoreFieldName(albumSetType),
                        ScanFaceColumns.THUMB_W,
                        ScanFaceColumns.THUMB_H,
                        ScanFaceColumns.LEFT,
                        ScanFaceColumns.TOP,
                        ScanFaceColumns.LEFT,
                        ScanFaceColumns.RIGHT,
                        ScanFaceColumns.BOTTOM,
                        ScanFaceColumns.INVALID,
                        ScanFaceColumns.IS_RECYCLED
                    )
                )
                .setWhere(ScanFaceColumns.DATA + SQLGrammar.EQUAL_TO + SQLGrammar.AND + DatabaseUtils.getDataValidWhere() + SQLGrammar.AND
                        + ScanFaceColumns.IS_RECYCLED + SQLGrammar.EQUAL_ZERO)
                .setWhereArgs(arrayOf(filePath))
                .setConvert(CursorConvert())
                .build().exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "queryPersonPetGroupDataInfo, e=$it" }
        }
        return cursor
    }

    private fun isListContentsEqual(selectedGroupIdList: MutableList<Long>, queryGroupIdList: MutableList<Long>): Boolean {
        if (selectedGroupIdList.size != queryGroupIdList.size) return false
        val copyOfSelectedGroupIdList: MutableList<Long> = selectedGroupIdList.toMutableList()
        val copyOfQueryGroupIdList: List<Long> = queryGroupIdList.toMutableList()
        return copyOfSelectedGroupIdList.containsAll(queryGroupIdList) && copyOfQueryGroupIdList.containsAll(selectedGroupIdList)
    }

    /**
     * 解散合照
     *
     * @param context 上下文环境
     * @param groupIdList 选中的要解散的群组merge groupId list
     * @param albumSetType 当前操作的图集类型
     *
     * @return int 数据库操作成功记录数
     */
    @JvmStatic
    fun disbandPersonPetGroups(context: Context, groupIdList: MutableList<Long>, albumSetType: Int): Int {
        var result = 0
        if (groupIdList.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "disbandPersonPetGroups, groupIdList is empty!" }
            return result
        }
        val tableType = getTableType(albumSetType)
        val size = groupIdList.size
        GLog.d(TAG, LogFlag.DL) { "updatePersonGroupsDisband, groupIdList=$groupIdList, mergeGroupIdList.size: $size" }
        val current = System.currentTimeMillis()
        val operations: MutableList<UpdateReq> = mutableListOf()
        kotlin.runCatching {
            val sbDisbandGroupIds = buildIdStringByList(groupIdList)
            val whereClause = StringBuilder().append(getGroupIdFieldName(albumSetType) + SQLGrammar.IN + SQLGrammar.LEFT_BRACKETS)
            .append(sbDisbandGroupIds).append(SQLGrammar.RIGHT_BRACKETS)
                .append(SQLGrammar.AND).append(DatabaseUtils.getDataValidWhere())
                .append(SQLGrammar.AND).append(PersonPetGroup.IS_RECYCLED).append(SQLGrammar.EQUAL_ZERO)
            val cv = ContentValues()
            cv.put(PersonPetGroup.IS_DISBAND, true)
            val updateReq = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(tableType)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert { cv }
                .setWhere(whereClause.toString())
                .build()
            operations.add(updateReq)
            val results = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            notifyTableChange(tableType, IDao.DaoType.GALLERY)
            result = results.size
            GalleryScanUtils.setLastManualTime(context, current)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updatePersonGroupsDisband, e=$it" }
        }
        return result
    }

    /**
     * 设置人宠与我的关系
     *
     * @param groupId 当前操作的图集的groupid
     * @param relationName 关系名称
     * @param relationType 关系类型，是自定义关系，还是默认关系
     * @param albumSetType 当前操作的图集类型
     *
     * @return Boolean 设置我的关系是否成功
     */
    @JvmStatic
    fun setPersonPetRelationshipWithMe(groupId: Long, relationName: String, relationType: Int, albumSetType: Int): Boolean {
        var result = 0
        kotlin.runCatching {
            val cv = ContentValues()
            cv.put(PetViewColumns.RELATION_TYPE, relationType)
            if (relationType == RelationShipType.CUSTOM.ordinal) {
                cv.put(PetViewColumns.CUSTOM_RELATION, relationName)
            } else {
                cv.put(PetViewColumns.CUSTOM_RELATION, TextUtil.EMPTY_STRING)
            }
            val whereSb = StringBuilder()
            whereSb.append(getGroupIdFieldName(albumSetType) + SQLGrammar.EQUAL)
            whereSb.append(groupId)
            result = UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .setTableType(getTableType(albumSetType))
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(whereSb.toString())
                .setConvert { aVoid: Void? -> cv }
                .build()
                .exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "setPersonPetRelationshipWithMe, e=$it" }
        }
        return result > 0
    }

    /**
     * set group show able batch
     *
     * @param groupIdList
     * @param enable
     * @param albumSetType
     * @return int
     */
    @JvmStatic
    fun setGroupShowEnable(groupIdList: List<Long>, enable: Boolean, isManual: Boolean, albumSetType: Int): Int {
        var result = 0
        if (groupIdList.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "setGroupShowEnable, groupIdList is empty!" }
            return result
        }
        val size = groupIdList.size
        GLog.d(TAG, LogFlag.DL) { "setGroupShowEnable, groupIdList.size: $size" }
        val tableType = getTableType(albumSetType)
        if (isManual) {
            result = updateGroupShowEnableManual(groupIdList, enable, albumSetType, tableType)
        }
        return result
    }

    /**
     * 原始方法过长，提取为方法：手动移除场景，更新数据库表
     *
     * @param groupIdList 选中的图集对应的groupId list
     * @param enable 是否显示在图集列表中，true为显示，false为隐藏
     * @param albumSetType 图集类型：用于访问不同的数据库
     * @param tableType 表类型
     *
     * @return Int 数据库是否更新成功
     */
    private fun updateGroupShowEnableManual(groupIdList: List<Long>, enable: Boolean, albumSetType: Int, tableType: Int): Int {
        var result = 0
        val current = System.currentTimeMillis()
        var maxGroupId = getMaxGroupIdLessThanBaseId(albumSetType)
        val operations: MutableList<UpdateReq> = mutableListOf()
        val groupIdField = GroupHelper.getGroupViewIdFieldName(albumSetType)
        kotlin.runCatching {
            for (groupId in groupIdList) {
                val cv = ContentValues()
                val updateGroupId = if (groupId > GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE) maxGroupId++ else groupId
                cv.put(ScanFaceGroupColumns.GROUP_ID, updateGroupId)
                cv.put(ScanFaceGroupColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                cv.put(ScanFaceGroupColumns.MANUAL_DATE, current)
                cv.put(ScanFaceColumns.IS_HIDE, !enable)
                if (!ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY)) {
                    if (enable) {
                        cv.put(GalleryStore.ScanFaceGroupColumns.GROUP_VISIBLE, GROUP_VISIBLE_SHOW)
                    } else {
                        cv.put(GalleryStore.ScanFaceGroupColumns.GROUP_VISIBLE, GROUP_VISIBLE_HIDE)
                    }
                }
                val updateReq = UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(tableType)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setConvert { cv }
                    .setWhere(groupIdField + SQLGrammar.EQUAL_TO)
                    .setWhareArgs(arrayOf(groupId.toString()))
                    .build()
                operations.add(updateReq)
            }

            val results = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            notifyTableChange(tableType, IDao.DaoType.GALLERY)
            result = results.size
            GalleryScanUtils.setLastManualTime(ContextGetter.context, current)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateGroupShowEnableManual, e=$it" }
        }
        deletePersonPetGroup(groupIdList, albumSetType)
        return result
    }

    /**
     * 移除解色后，同时移除该角色所在的合照
     *
     * @param groupIdList 要删除的人物或宠物图集的groupid列表
     * @param albumSetType 需要操作的图集的类型
     */
    private fun deletePersonPetGroup(groupIdList: List<Long>, albumSetType: Int) {
        kotlin.runCatching {
            /**
             * 1.先查询person_pet_group_list_view中的合照记录的person_group_ids或pet_group_ids
             * 2.将查到的person_group_ids或pet_group_ids放到list中
             * 3.遍历第2步中的list，如果选中的groupid包含在第2步中的list,表明这些groupid对应的合照需要被删除，所以把需要删除的合照的merger_group_id统一先缓存到list中
             * 4.删除第3步缓存的merger_group_id对应的合照记录
             */
            val projection = arrayOf(GalleryStore.PersonPetGroupListView.MERGE_GROUP_ID, getGroupIdsFieldName(albumSetType))
            val where = StringBuilder()
            where.append(getGroupIdsFieldName(albumSetType)).append(SQLGrammar.IS_NOT_EMPTY)
                .append(SQLGrammar.AND).append(getGroupIdsFieldName(albumSetType)).append(SQLGrammar.IS_NOT_NULL)
            val needRemoveMergeGroupIdList = mutableListOf<Long>()
            val queryReq = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP_LIST_VIEW)
                .setProjection(projection)
                .setWhere(where.toString())
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(queryReq).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return
                }
                val mergeGroupIdIndex = cursor.getColumnIndex(GalleryStore.PersonPetGroupListView.MERGE_GROUP_ID)
                val personPetIdsIndex = cursor.getColumnIndex(getGroupIdsFieldName(albumSetType))
                val personPetIdsMap = mutableMapOf<Long, MutableList<Long>>()
                while (cursor.moveToNext()) {
                    val personPetIdsList = mutableListOf<Long>()
                    val mergeGroupId = cursor.getLong(mergeGroupIdIndex)
                    val personPetIds = cursor.getString(personPetIdsIndex)
                    personPetIdsList.addAll(personPetIds.split(SQLGrammar.COMMA).mapNotNull { it.toLongOrNull() }.toMutableList())
                    personPetIdsMap[mergeGroupId] = personPetIdsList
                }
                personPetIdsMap.forEach { (mergeGroupId: Long, personPetIdsList: MutableList<Long>) ->
                    personPetIdsList.forEach { groupId ->
                        if (groupIdList.contains(groupId)) {
                            needRemoveMergeGroupIdList.add(mergeGroupId)
                        }
                    }
                }
            }
            val whereClause = StringBuilder().append(
                DatabaseUtils.getDataValidWhere() + SQLGrammar.AND
                        + PersonPetGroup.IS_RECYCLED + SQLGrammar.EQUAL_ZERO + SQLGrammar.AND
            )
            whereClause.append(DatabaseUtils.getWhereIn(GalleryStore.PersonPetGroupColumns.MERGE_GROUP_ID, needRemoveMergeGroupIdList))
            GLog.d(TAG, LogFlag.DL) { "deletePersonPetGroup,groupIdList=$groupIdList,whereClause=$whereClause" }
            val req = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_FALSE)
                .setWhere(whereClause.toString())
                .build()
            DataAccess.getAccess().delete(req)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deletePersonPetGroup, e:$it" }
        }
    }

    @JvmStatic
    fun setIsHasBigFaceState(groupIdList: ArrayList<Long>, enable: Boolean): Int {
        var result = 0
        if (groupIdList.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "setIsHasBigFaceState, groupIdList is empty!" }
            return result
        }
        val size = groupIdList.size
        GLog.d(TAG, LogFlag.DL) { "setIsHasBigFaceState, groupIdList.mSize: $size" }
        kotlin.runCatching {
            val cv = ContentValues()
            val sb = StringBuilder()
            sb.append(GalleryStore.ScanFaceGroupView.GROUP_ID + " IN (")
            for (groupId in groupIdList) {
                sb.append(groupId)
                sb.append(SQLGrammar.COMMA)
            }
            sb.deleteCharAt(sb.length - 1)
            sb.append(SQLGrammar.RIGHT_BRACKETS)
            if (enable) {
                cv.put(GalleryStore.ScanFaceGroupView.HAS_BIG_FACE, ScanFaceGroupColumns.HAS_BIG_FACE_STATE)
            } else {
                cv.put(GalleryStore.ScanFaceGroupView.HAS_BIG_FACE, ScanFaceGroupColumns.HAS_NO_BIG_FACE_STATE)
            }
            result = UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(sb.toString())
                .setConvert { aVoid: Void? -> cv }
                .build()
                .exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "setIsHasBigFaceState, e=$it" }
        }
        return result
    }

    @JvmStatic
    fun setIsManualState(context: Context, groupId: Long, enable: Boolean, albumSetType: Int): Int {
        var result = 0
        val list = ArrayList<Long>()
        list.add(groupId)
        result = setIsManualState(context, list, enable, albumSetType)
        return result
    }

    private fun setIsManualState(context: Context, groupIdList: ArrayList<Long>, enable: Boolean, albumSetType: Int): Int {
        var result = 0
        if (groupIdList.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "setIsManualState, groupIdList is empty!" }
            return result
        }
        val size = groupIdList.size
        val operations: MutableList<UpdateReq> = ArrayList()
        val current = System.currentTimeMillis()
        val tableType = getTableType(albumSetType)
        val groupIdField = getGroupViewIdFieldName(albumSetType)
        for (groupId in groupIdList) {
            val updateGroupId =
                if (groupId > GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE) {
                    getMaxGroupIdLessThanBaseId(albumSetType)
                } else {
                    groupId
                }
            val value = ContentValues()
            value.put(groupIdField, updateGroupId)
            when (albumSetType) {
                TYPE_PERSON_ALBUM_SET, TYPE_PET_ALBUM_SET -> {
                    value.put(ScanFaceGroupColumns.IS_MANUAL, enable)
                    if (enable) {
                        value.put(ScanFaceGroupColumns.MANUAL_DATE, current)
                    }
                }

                else -> {}
            }
            val updateReq = UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(tableType)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert { aVoid: Void? -> value }
                .setWhere(groupIdField + SQLGrammar.EQUAL_TO)
                .setWhareArgs(arrayOf(groupId.toString()))
                .build()
            operations.add(updateReq)
        }
        kotlin.runCatching {
            val results = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            result = results.size
            GLog.d(TAG, LogFlag.DL) { "setIsManualState, result: $result" }
            if (enable) {
                GalleryScanUtils.setLastManualTime(context, current)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "setIsManualState, e: $it" }
        }
        return result
    }

    /**
     * 人物宠物的不是TA,合照的不是TA们
     *
     * @param galleryIds  Local_Media 表的 _ids
     * @param origGroupId ids 所属的 groupId
     * @param albumSetType 当前操作的图集类型
     */
    @JvmStatic
    fun freeFaceFromGroup(galleryIds: List<Long>, origGroupId: Long, albumSetType: Int): Int {
        var result = 0
        if (galleryIds.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "freeFaceFromGroup, personIdList is empty!" }
            return result
        }
        val current = System.currentTimeMillis()
        val size = galleryIds.size
        GLog.d(TAG, LogFlag.DL) { "freeFaceFromGroup, personIdList.size: $size" }
        val operations: MutableList<UpdateReq> = mutableListOf()

        // 获取该 GroupId 的图片数量
        val faceCount = getFaceCountByGroupId(origGroupId, albumSetType)
        // 获取当前可用的下一个新的 GroupId
        var maxGroupId = getMaxGroupIdLessThanBaseId(albumSetType)
        // 只选中该人物里的部分图片，并没有选中全部图片，需要把这个 GroupId 里的图片设置为手动
        if (size < faceCount) {
            val updateGroupId = if (origGroupId > GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE) maxGroupId++ else origGroupId
            val updateReq = getIsManualUpdateReq(origGroupId, updateGroupId, current, albumSetType)
            updateReq?.let { operations.add(it) }
        }
        if (size > 0) {
            val dataList = ArrayList<String>()
            val where = DatabaseUtils.getWhereIn(GalleryStore.GalleryColumns.LocalColumns._ID, galleryIds)
            kotlin.runCatching {
                getCursorFromLocalMedia(where, arrayOf(GalleryStore.GalleryColumns.LocalColumns.DATA)).use { cursor ->
                    if (cursor != null && cursor.count > 0) {
                        while (cursor.moveToNext()) {
                            dataList.add(cursor.getString(0))
                        }
                    }
                }
            }.onFailure {
                GLog.d(TAG, LogFlag.DL) { "getCursorFromLocalMedia, e: $it" }
            }
            // update the free faces
            val selection = ScanFaceColumns.DATA + " = ? AND " + getGroupIdFieldName(albumSetType) + SQLGrammar.EQUAL_TO
            var i = 0
            for (data in dataList) {
                val updateReq = getFreeFaceUpdateReq(
                    selection,
                    arrayOf(data, origGroupId.toString()),
                    maxGroupId + i++,
                    current,
                    albumSetType
                )
                updateReq?.let { operations.add(it) }
            }
        }
        kotlin.runCatching {
            val results = BatchReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            // getFreeFaceUpdateReq getIsManualUpdateReq 通知
            notifyTableChange(GroupHelper.getTableType(albumSetType), IDao.DaoType.GALLERY)
            result = results.size
            GalleryScanUtils.setLastManualTime(ContextGetter.context, current)
            GLog.d(TAG, LogFlag.DL) { "freeFaceFromGroup, result: $result" }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "freeFaceFromGroup, e: $it" }
        }
        return result
    }

    private fun getCursorFromLocalMedia(where: String?, projection: Array<String>): Cursor? {
        return QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(projection)
            .setWhere(where)
            .setConvert(CursorConvert())
            .build().exec()
    }

    private fun getIsManualUpdateReq(origGroupId: Long, groupId: Long, manualDate: Long, albumSetType: Int): UpdateReq? {
        val value = ContentValues()
        value.put(getGroupIdFieldName(albumSetType), groupId)
        if (albumSetType != TYPE_PERSON_PET_GROUP_ALBUM_SET) {
            value.put(ScanFaceGroupColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
            value.put(ScanFaceGroupColumns.MANUAL_DATE, manualDate)
        }
        return getUpdateReq(
            value,
            getGroupIdFieldName(albumSetType) + SQLGrammar.EQUAL_TO,
            arrayOf(origGroupId.toString()),
            albumSetType
        )
    }

    private fun getUpdateReq(value: ContentValues, where: String, whareArgs: Array<String>, albumSetType: Int): UpdateReq? {
        return UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(getTableType(albumSetType))
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setConvert { aVoid: Void? -> value }
            .setWhere(where)
            .setWhareArgs(whareArgs)
            .build()
    }

    private fun getFreeFaceUpdateReq(where: String, whereArgs: Array<String>, groupId: Long, manualDate: Long, albumSetType: Int): UpdateReq? {
        val value = ContentValues()
        //set new groupId
        value.put(getGroupIdFieldName(albumSetType), groupId)
        //reset group show enable, NOTIFY: this column belong Face_group View
        when (albumSetType) {
            TYPE_PERSON_ALBUM_SET -> {
                value.put(ScanFaceColumns.GROUP_VISIBLE, ScanFaceGroupColumns.GROUP_VISIBLE_INIT)
                //reset group name, NOTIFY: this column belong Face_group View
                value.putNull(ScanFaceColumns.GROUP_NAME)
                value.put(ScanFaceColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                value.put(ScanFaceColumns.MANUAL_DATE, manualDate)
            }
            TYPE_PET_ALBUM_SET -> {
                value.put(PetColumns.IS_HIDE, true)
                value.putNull(PetColumns.GROUP_NAME)
                value.put(PetColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                value.put(PetColumns.MANUAL_DATE, manualDate)
            }
            TYPE_PERSON_PET_GROUP_ALBUM_SET -> {
                value.putNull(PersonPetGroup.MERGE_GROUP_NAME)
                value.putNull(PersonPetGroup.PERSON_GROUP_IDS)
                value.putNull(PersonPetGroup.PET_GROUP_IDS)
            }
            else -> {}
        }
        //update is_chosen
        value.put(ScanFaceColumns.IS_CHOSEN, false)
        return getUpdateReq(value, where, whereArgs, albumSetType)
    }

    /**
     * 合并多个角色时，需要将被合并角色的 group id 更新为主角色的 group id
     * @param majorGroupId 合并后的主 group id
     * @param groupList 需要合并的 group id 列表，包含了 [majorGroupId]
     * @param albumSetType 人宠类型
     */
    @JvmStatic
    fun updatePersonPetGroupIdWhenMergeGroup(majorGroupId: Long, groupList: List<Long>, albumSetType: Int) {
        if (groupList.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "updatePersonPetGroupIdWhenMergeGroup, groupList is empty!" }
            return
        }
        // 非 [majorGroupId] 的列表
        val notMajorGroupList = groupList.filter { groupId -> groupId != majorGroupId }
        // 查询合照表中，需要更新的记录
        val personPetGroupItemList = queryPersonPetGroupItem(notMajorGroupList, albumSetType)
        if (personPetGroupItemList.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "updatePersonPetGroupIdWhenMergeGroup, personPetGroupItemList is empty!" }
            return
        }
        // 获取需要更新的 group id 和 合照记录 id 的 map 集合
        val updateMap = getUpdateMap(majorGroupId, personPetGroupItemList, notMajorGroupList, albumSetType)
        val operations: MutableList<UpdateReq> = mutableListOf()
        updateMap.forEach { (groupId, idList) ->
            // 每100条合照记录，打包更新数据
            idList.chunked(GROUP_SIZE).forEach { ids ->
                UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(
                        StringBuilder().apply {
                            append(PersonPetGroup._ID).append(SQLGrammar.IN).append(SQLGrammar.LEFT_BRACKETS)
                            append(ids.joinToString(SQLGrammar.COMMA)).append(SQLGrammar.RIGHT_BRACKETS)
                        }.toString()
                    )
                    .setConvert {
                        ContentValues().apply {
                            when (albumSetType) {
                                TYPE_PERSON_ALBUM_SET -> put(PersonPetGroup.PERSON_GROUP_IDS, groupId)
                                TYPE_PET_ALBUM_SET -> put(PersonPetGroup.PET_GROUP_IDS, groupId)
                            }
                        }
                    }
                    .build()
                    .let { operations.add(it) }
            }
        }
        if (operations.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "updatePersonPetGroupIdWhenMergeGroup, operations is empty!" }
            return
        }
        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY).addDataReqs(operations).build().exec()
            notifyTableChange(GalleryDbDao.TableType.PERSON_PET_GROUP, IDao.DaoType.GALLERY)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updatePersonPetGroupIdWhenMergeGroup, e: $it" }
        }
    }

    /**
     * 获取需要更新的 group id 和 合照记录 id 的 map 集合
     * 其中 key 对应更新的 group id，而 value 对应需要更新的合照记录 id
     */
    private fun getUpdateMap(
        majorGroupId: Long,
        personPetGroupItemList: List<PersonPetGroupCluster>,
        notMajorGroupList: List<Long>,
        albumSetType: Int
    ): MutableMap<String, MutableList<Int>> {
        val updateMap = mutableMapOf<String, MutableList<Int>>()

        /**
         * 更新合照表的 person_group_ids 或 pet_group_ids
         */
        personPetGroupItemList.forEach { cluster ->
            val originGroupIds = mutableListOf<String>().apply {
                if (albumSetType == TYPE_PERSON_ALBUM_SET) {
                    cluster.personGroupIds?.split(SQLGrammar.COMMA)?.let { addAll(it) }
                } else {
                    cluster.petGroupIds?.split(SQLGrammar.COMMA)?.let { addAll(it) }
                }
            }

            if (originGroupIds.isEmpty()) return@forEach

            /**
             * 将 [originGroupIds] 中出现的 [notMajorGroupList] 替换成 [majorGroupId]
             */
            originGroupIds.forEachIndexed { index, originGroupId ->
                notMajorGroupList.forEach { groupId ->
                    if (originGroupId.toLong() == groupId) {
                        originGroupIds[index] = majorGroupId.toString()
                        return@forEachIndexed
                    }
                }
            }
            /**
             * 去掉 [originGroupIds] 里重复的 group id
             */
            val key = originGroupIds.distinct().sorted().joinToString(SQLGrammar.COMMA)

            /**
             * 将这条合照记录添加到 [updateMap] 中
             */
            if (!updateMap.containsKey(key)) {
                updateMap[key] = mutableListOf()
            }
            cluster.id?.let { updateMap[key]?.add(it) }
        }
        return updateMap
    }

    /**
     * 当合并操作发生时，查询合照表中，人物的 person_group_ids 或 宠物的 pet_group_ids，只能2选1，不会同时查询两者。
     * 因为合并操作，不会出现人物与宠物的合并，只会出现人物与人物、宠物与宠物的合并，一旦合并操作发生，就确定了查询的类别。
     * @param groupList 合并的角色的 group id 列表，列表里不包含主角色的 majorGroupId
     * @param albumSetType 人宠类型
     */
    private fun queryPersonPetGroupItem(groupList: List<Long>, albumSetType: Int): List<PersonPetGroupCluster> {
        val result = mutableListOf<PersonPetGroupCluster>()
        val idCache = mutableListOf<Int>()
        val groupIdsField = getGroupIdsFieldName(albumSetType)
        groupList.forEach { groupId ->
            val groupIdsWhere = getGroupIdsLikeWhereClause(groupIdsField, groupId.toString())
            runCatching {
                QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                    .setProjection(arrayOf(PersonPetGroup._ID, groupIdsField))
                    .setWhere(groupIdsWhere.toString())
                    .setConvert(CursorConvert())
                    .build().exec()?.use { cursor ->
                        val idIndex = cursor.getColumnIndex(PersonPetGroup._ID)
                        val groupIdsIndex = cursor.getColumnIndex(groupIdsField)
                        while (cursor.moveToNext()) {
                            val id = cursor.getInt(idIndex)
                            if (idCache.contains(id)) { // 如果缓存id中存在这条合照记录，则跳过，不需要重复添加。
                                continue
                            }
                            PersonPetGroupCluster().let {
                                it.id = id
                                when (albumSetType) {
                                    TYPE_PERSON_ALBUM_SET -> it.personGroupIds = cursor.getString(groupIdsIndex)
                                    TYPE_PET_ALBUM_SET -> it.petGroupIds = cursor.getString(groupIdsIndex)
                                }
                                result.add(it)
                            }
                        }
                    }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "queryPersonPetGroupItem, e: $it" }
            }
        }
        return result
    }

    /**
     * (
     *  [groupIdsField] = '[groupId]'
     *  OR
     *  [groupIdsField] LIKE '%,[groupId],%'
     *  OR
     *  [groupIdsField] LIKE '[groupId],%'
     *  OR
     *  [groupIdsField] LIKE '%,[groupId]'
     * )
     */
    private fun getGroupIdsLikeWhereClause(groupIdsField: String, groupId: String): StringBuilder {
        return StringBuilder().append(SQLGrammar.LEFT_BRACKETS)
            .append(groupIdsField).append(SQLGrammar.EQUAL).append(SQLGrammar.QUOTE).append(groupId).append(SQLGrammar.QUOTE)
            .append(SQLGrammar.OR)
            .append(groupIdsField).append(SQLGrammar.LIKE)
            .append(SQLGrammar.QUOTE)
            .append(SQLGrammar.LIKE_PERCENT).append(SQLGrammar.COMMA).append(groupId).append(SQLGrammar.COMMA).append(SQLGrammar.LIKE_PERCENT)
            .append(SQLGrammar.QUOTE)
            .append(SQLGrammar.OR)
            .append(groupIdsField).append(SQLGrammar.LIKE)
            .append(SQLGrammar.QUOTE)
            .append(groupId).append(SQLGrammar.COMMA).append(SQLGrammar.LIKE_PERCENT)
            .append(SQLGrammar.QUOTE)
            .append(SQLGrammar.OR)
            .append(groupIdsField).append(SQLGrammar.LIKE)
            .append(SQLGrammar.QUOTE)
            .append(SQLGrammar.LIKE_PERCENT).append(SQLGrammar.COMMA).append(groupId)
            .append(SQLGrammar.QUOTE)
            .append(SQLGrammar.RIGHT_BRACKETS)
    }

    /**
     * unite groups when showState == show and then changed their ShowEnable or not modify
     *
     * @param majorGroupId
     * @param groupList
     * @param isShow
     * @param albumSetType
     * @return Boolean
     */
    @JvmStatic
    fun uniteGroups(majorGroupId: Long, groupList: List<Long>, isShow: Boolean, albumSetType: Int): Boolean {
        var result = false
        if (groupList.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "uniteGroups, groupList is empty!" }
            return result
        }
        val current = System.currentTimeMillis()
        val size = groupList.size
        val updateGroupId =
            if (majorGroupId > GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE) {
                getMaxGroupIdLessThanBaseId(albumSetType)
            } else {
                majorGroupId
            }
        GLog.d(TAG, LogFlag.DL) { "uniteGroups, majorGroupId: $majorGroupId, groupList.size: $size" }
        //reset face best score of minor group if they had
        val operations: MutableList<UpdateReq> = mutableListOf()
        operations.clear()
        val idDataInfo = IdDataInfo(majorGroupId, groupList, updateGroupId)
        updateMajorGroupIdData(idDataInfo, isShow, albumSetType, current, operations)
        result = updateNonMajorGroupIdData(majorGroupId, isShow, albumSetType, current, updateGroupId, operations)
        return result
    }

    /**
     * 合并时，更新主groupId记录的信息
     *
     * @param idDataInfo 为了整改参数过多，采用数据类封装
     * @param isShow 图集是否要在列表显示
     * @param albumSetType 图集类型：用于访问不同的数据库
     * @param current 当前时间
     * @param operations 数据库更新请求list UpdateReqList
     */
    private fun updateMajorGroupIdData(
        idDataInfo: IdDataInfo,
        isShow: Boolean,
        albumSetType: Int,
        current: Long,
        operations: MutableList<UpdateReq>
    ) {
        val info = getGroupInfo(idDataInfo.majorGroupId, albumSetType)
        val sb = StringBuilder()
        //update not majorGroupId data
        sb.append(GroupHelper.getGroupViewIdFieldName(albumSetType) + SQLGrammar.IN + SQLGrammar.LEFT_BRACKETS)
        for (groupId in idDataInfo.groupIdList) {
            if (groupId == idDataInfo.majorGroupId) {
                continue
            }
            sb.append(groupId)
            sb.append(SQLGrammar.COMMA)
        }
        sb.deleteCharAt(sb.length - 1)
        sb.append(SQLGrammar.RIGHT_BRACKETS)
        val updateReq = UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GroupHelper.getTableType(albumSetType))
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setConvert { aVoid: Void? ->
                val cv = ContentValues()
                if (albumSetType == GroupHelper.TYPE_PERSON_ALBUM_SET) {
                    cv.put(GalleryStore.ScanFaceGroupView.GROUP_ID, idDataInfo.updateGroupId)
                    cv.put(GalleryStore.ScanFaceGroupView.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                    cv.put(GalleryStore.ScanFaceGroupView.MANUAL_DATE, current)
                    if (isShow) {
                        cv.put(GalleryStore.ScanFaceGroupView.GROUP_VISIBLE, ScanFaceGroupColumns.GROUP_VISIBLE_SHOW)
                    } else {
                        cv.put(GalleryStore.ScanFaceGroupView.GROUP_VISIBLE, info?.mGroupVisible)
                    }
                    cv.put(GalleryStore.ScanFaceGroupView.GROUP_NAME, info?.mGroupName)
                    //update is_chosen, NOTIFY: this column belong Face View
                    cv.put(GalleryStore.ScanFaceView.IS_CHOSEN, false)
                } else if (albumSetType == GroupHelper.TYPE_PET_ALBUM_SET) {
                    cv.put(GalleryStore.PersonPetListViewColumns.GROUP_ID, idDataInfo.updateGroupId)
                    cv.put(GalleryStore.PersonPetListViewColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                    cv.put(GalleryStore.PersonPetListViewColumns.MANUAL_DATE, current)
                    cv.put(GalleryStore.PersonPetListViewColumns.GROUP_NAME, info?.mGroupName)
                    //update is_chosen, NOTIFY: this column belong Pet View
                    cv.put(GalleryStore.PetView.IS_CHOSEN, false)
                }
                cv
            }
            .setWhere(sb.toString())
            .build()
        operations.add(updateReq)
    }

    /**
     * 合并功能时，更新非主groupId的记录
     *
     * @param majorGroupId 合并时需要更新的主groupId
     * @param isShow 是否在对应图集列表中显示
     * @param albumSetType 图集类型，用于访问不同的数据库
     * @param current 当前时间
     * @param updateGroupId 需要更新的记录的groupId
     * @param operations 数据库更新请求列表 UpdateReq list
     * @return Boolean
     */
    private fun updateNonMajorGroupIdData(
        majorGroupId: Long,
        isShow: Boolean,
        albumSetType: Int,
        current: Long,
        updateGroupId: Long,
        operations: MutableList<UpdateReq>
    ): Boolean {
        var result = false
        //update majorGroupId data
        val updateReq = UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GroupHelper.getTableType(albumSetType))
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setConvert {
                val cv = ContentValues()
                if (albumSetType == GroupHelper.TYPE_PERSON_ALBUM_SET) {
                    cv.put(ScanFaceGroupColumns.GROUP_ID, updateGroupId)
                    cv.put(ScanFaceGroupColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                    cv.put(ScanFaceGroupColumns.MANUAL_DATE, current)
                    //if from hide to show the major group should changed ShowEnable to true
                    if (isShow) {
                        cv.put(ScanFaceGroupColumns.GROUP_VISIBLE, ScanFaceGroupColumns.GROUP_VISIBLE_SHOW)
                    }
                } else if (albumSetType == GroupHelper.TYPE_PET_ALBUM_SET) {
                    cv.put(GalleryStore.PersonPetListViewColumns.GROUP_ID, updateGroupId)
                    cv.put(GalleryStore.PersonPetListViewColumns.IS_MANUAL, ScanFaceGroupColumns.HAS_MANUAL_STATE)
                    cv.put(GalleryStore.PersonPetListViewColumns.MANUAL_DATE, current)
                    cv.put(GalleryStore.Pet.IS_HIDE, false)
                }
                cv
            }
            .setWhere(GroupHelper.getGroupViewIdFieldName(albumSetType) + SQLGrammar.EQUAL_TO)
            .setWhareArgs(arrayOf(majorGroupId.toString()))
            .build()
        operations.add(updateReq)
        kotlin.runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            notifyTableChange(GroupHelper.getTableType(albumSetType), IDao.DaoType.GALLERY)
            result = true
            GalleryScanUtils.setLastManualTime(ContextGetter.context, current)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateNonMajorGroupIdData, e: $it" }
        }
        return result
    }

    /**
     * get special number order by number_of_faces and group_id of group info
     *
     * @param number
     * @return ArrayList<GroupInfo>
     */
    @JvmStatic
    fun getSpecialNumGroupInfo(number: Int): ArrayList<GroupInfo> {
        val list = ArrayList<GroupInfo>()
        kotlin.runCatching {
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE_GROUP_VIEW)
                .setProjection(PERSON_GROUP_QUERY_PROJECT)
                .setWhere(
                    " ( " + GalleryStore.ScanFaceGroupView.GROUP_VISIBLE
                            + SQLGrammar.EQUAL + ScanFaceGroupColumns.GROUP_VISIBLE_INIT
                            + SQLGrammar.OR + GalleryStore.ScanFaceGroupView.GROUP_VISIBLE + SQLGrammar.IS_NULL
                            + ") AND " + GalleryStore.ScanFaceGroupView.NUM_BIG_FACES + SQLGrammar.GREATER_THAN_ZERO
                )
                .setOrderBy(
                    (GalleryStore.ScanFaceGroupView.NUM_ALL_FACES + " DESC , " + GalleryStore.ScanFaceGroupView.GROUP_ID
                            + SQLGrammar.ORDER_ASC)
                )
                .setConvert(CursorConvert())
                .setLimit(if (number > 0) ("0,$number") else null)
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return list
                }
                list.addAll(GroupInfo.buildGroupInfoList(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getSpecialNumGroupInfo, e: $it" }
        }
        return list
    }

    /**
     * 获取groupvisible =1的记录
     *
     * @return Int
     */
    @JvmStatic
    fun getShowGroupCount(): Int {
        var count = 0
        kotlin.runCatching {
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE_GROUP_VIEW)
                .setProjection(DatabaseUtils.getCountProjection())
                .setWhere(
                    GalleryStore.ScanFaceGroupView.GROUP_VISIBLE
                            + SQLGrammar.EQUAL + ScanFaceGroupColumns.GROUP_VISIBLE_SHOW
                            + SQLGrammar.AND + GalleryStore.ScanFaceGroupView.NUM_BIG_FACES + SQLGrammar.GREATER_THAN_ZERO
                )
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor != null) && cursor.moveToNext()) {
                    count = cursor.getInt(0)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getShowGroupCount, e: $it" }
        }
        return count
    }

    /**
     * 获取num_big_faces > 0的记录
     *
     * @return ArrayList<GroupInfo>
     */
    @JvmStatic
    fun getBigFaceGroupInfo(): ArrayList<GroupInfo> {
        val list = ArrayList<GroupInfo>()
        kotlin.runCatching {
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE_GROUP_VIEW)
                .setProjection(PERSON_GROUP_QUERY_PROJECT)
                .setWhere(GalleryStore.ScanFaceGroupView.NUM_BIG_FACES + SQLGrammar.GREATER_THAN_ZERO)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return list
                }
                list.addAll(GroupInfo.buildGroupInfoList(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getBigFaceGroupInfo, e: $it" }
        }
        return list
    }

    /**
     * 获取filepath在参数pathlist中的记录
     *
     * @return Map<String, MutableList<CvFaceCluster>>
     */
    @JvmStatic
    fun getAllFaceCluster(context: Context, list: List<String>): Map<String, MutableList<CvFaceCluster>> {
        val map: MutableMap<String, MutableList<CvFaceCluster>> = HashMap()
        val cvFaceClusterList = ArrayList<CvFaceCluster>()
        kotlin.runCatching {
            val where = StringBuilder()
            where.append(GalleryStore.ScanFace.DATA).append(" IN (")
            for (value in list) {
                where.append(SQLGrammar.QUOTE).append(value).append(SQLGrammar.QUOTE)
                where.append(SQLGrammar.COMMA)
            }
            if (where.lastIndexOf(SQLGrammar.COMMA) == where.length - 1) {
                where.deleteCharAt(where.length - 1)
            }
            where.append(SQLGrammar.RIGHT_BRACKETS)
            val sortOrder = GalleryStore.ScanFace.DATA + SQLGrammar.ORDER_ASC
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(null)
                .setWhere(where.toString())
                .setOrderBy(sortOrder)
                .setConvert(CursorConvert())
                .build()
            DataAccess.getAccess().query(req).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return map
                }
                cvFaceClusterList.addAll(CvFaceCluster.buildCvFaceClusterList(cursor))
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllFaceCluster, e: $it" }
        }
        for (face in cvFaceClusterList) {
            val filePath = face.filePath
            if (TextUtils.isEmpty(filePath)) {
                continue
            }
            var faceList = map[filePath]
            if (faceList == null) {
                faceList = ArrayList()
                filePath?.let { filePath ->
                    map[filePath] = faceList
                }
            }
            faceList.add(face)
        }
        return map
    }

    /**
     * 获取face_visible！=1的记录，将他们转成MediaItem
     *
     * @return ArrayList<MediaItem>
     */
    @JvmStatic
    fun getInvisibleFaceScannImageList(context: Context?): ArrayList<MediaItem> {
        val listImageInfo = getAllInvisibleFaceScannImage(false)
        val map = GalleryScanProviderHelper.getAllPathMediaIdMap(context)
        for (imageInfo in listImageInfo) {
            val mediaId = map[imageInfo.mFilePath]
            if (mediaId != null && mediaId > 0) {
                imageInfo.mMediaId = mediaId
            }
        }
        return GalleryScanProviderHelper.convertImageToMediaItem(listImageInfo)
    }

    private fun getScanFaceInfo(whereClause: String?): ArrayList<FaceInfo> {
        val list = ArrayList<FaceInfo>()
        val stringBuilder = StringBuilder()
        stringBuilder.append("SELECT ")
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT
                + GalleryStore.GalleryColumns.LocalColumns._ID + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT
                + GalleryStore.GalleryColumns.LocalColumns.DATA + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT
                + GalleryStore.GalleryColumns.LocalColumns.INVALID + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.ScanFace.IS_RECYCLED + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.ScanFace.MODEL_VERSION + SQLGrammar.COMMA_SPACE)
        stringBuilder.append(GalleryStore.ScanFace.TAB + SQLGrammar.DOT + GalleryStore.ScanFace.MEDIA_TYPE + SQLGrammar.FROM)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.INNER_JOIN + GalleryStore.ScanFace.TAB + SQLGrammar.ON)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + SQLGrammar.DOT + ScanFaceColumns.DATA + SQLGrammar.TRIM_EQUAL
                + GalleryStore.ScanFace.TAB + SQLGrammar.DOT + ScanFaceColumns.DATA)
        stringBuilder.append(" WHERE ")
        stringBuilder.append(DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB))
        stringBuilder.append(ConstantUtils.AND)
        stringBuilder.append(DatabaseUtils.getOnlyLocalWhere())
        if (whereClause != null) {
            stringBuilder.append(ConstantUtils.AND)
            stringBuilder.append(whereClause)
        }
        val rawQueryReq = RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(CursorConvert())
            .setQuerySql(stringBuilder.toString())
            .setSqlArgs(null)
            .build()
        kotlin.runCatching {
            DataAccess.getAccess().rawQuery(rawQueryReq).use { cursor ->
                if ((cursor == null) || (cursor.count == 0)) {
                    return list
                }
                buildFaceInfoList(cursor)?.let { list.addAll(it) }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getScanFaceInfo, e:$it" }
        }
        return list
    }

    /**
     * 查询全部与目标路径相同的媒体文件的人脸框数据，并返回列表
     */
    @JvmStatic
    fun getFaceRectFByData(data: String?): List<RectF> {
        val list = mutableListOf<RectF>()
        data ?: return list
        kotlin.runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setProjection(
                    arrayOf(
                        GalleryStore.ScanFace.LEFT,
                        GalleryStore.ScanFace.TOP,
                        GalleryStore.ScanFace.RIGHT,
                        GalleryStore.ScanFace.BOTTOM
                    )
                )
                .setWhere(GalleryStore.ScanFace.DATA + SQLGrammar.EQUAL_TO + SQLGrammar.AND + GalleryStore.ScanFace.LEFT + SQLGrammar.IS_NOT_NULL)
                .setWhereArgs(arrayOf(data))
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val leftIndex = cursor.getColumnIndex(GalleryStore.ScanFace.LEFT)
                    val topIndex = cursor.getColumnIndex(GalleryStore.ScanFace.TOP)
                    val rightIndex = cursor.getColumnIndex(GalleryStore.ScanFace.RIGHT)
                    val bottomIndex = cursor.getColumnIndex(GalleryStore.ScanFace.BOTTOM)
                    while (cursor.moveToNext()) {
                        list.add(
                            RectF(
                                cursor.getInt(leftIndex).toFloat(),
                                cursor.getInt(topIndex).toFloat(),
                                cursor.getInt(rightIndex).toFloat(),
                                cursor.getInt(bottomIndex).toFloat()
                            )
                        )
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getFaceRectFInSameMediaItem, e: $it" }
        }
        return list
    }
}