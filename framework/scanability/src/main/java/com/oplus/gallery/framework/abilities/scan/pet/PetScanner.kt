/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PetScanner.kt
 ** Description : 宠物扫描
 ** Version     : 1.0
 ** Date        : 2025/05/08
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/05/08  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.pet

import android.content.ContentUris
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Rect
import android.graphics.RectF
import android.os.Debug
import android.provider.MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE
import android.provider.MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
import androidx.core.graphics.toRect
import com.oplus.aiunit.core.data.UnitState
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.breakpad.BreakpadMaster
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.business_lib.model.data.label.bean.PetImageInfo
import com.oplus.gallery.business_lib.model.data.pet.RelationShipType
import com.oplus.gallery.foundation.ai.pet.PetClusterApi
import com.oplus.gallery.foundation.ai.pet.PetInfo
import com.oplus.gallery.foundation.database.notifier.UriNotifier.notifyTableChange
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Pet.PET_SDK_UPDATED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Pet.PET_VERSION
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.face.CoverBgProcessor
import com.oplus.gallery.framework.abilities.scan.face.CoverBgProcessor.CoverTask
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper.trackScanResult
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackInfo
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.PET_SCAN_COUNT_24H_MAX
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.refreshScanRecordingIfNecessary
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanPersonPetProviderHelper
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanPetProviderHelper
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.app.getAppAbility
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

/**
 * 宠物扫描
 */
class PetScanner(context: Context) : GalleryScan(context) {

    private var isApiUpdated = false
    private var currentVersion = TextUtil.EMPTY_STRING

    /**
     * 未扫描的媒体数据集合
     */
    private var newImages = mutableListOf<MediaItem>()

    /**
     * 需要更新的媒体数据集合
     */
    private var updateImages = mutableListOf<MediaItem>()

    /**
     * 扫描需要主动忽略的文件集合
     */
    private var abortFile = hashMapOf<String, Int>()

    /**
     * 未充电时的宠物扫描数量
     */
    private var scanCountNotInCharging = 0
    private var scanPetCount = 0
    private var coverProcessor: CoverBgProcessor? = null
    private var manualPersonIdList: List<Long>? = null
    private var startGroupTime: Long = 0
    private var startGroupTemperature = 0f
    private var startGroupBattery = 0f

    /**
     * 埋点字段：记录有效扫描出来的宠物数量。（group_id = 2，是无效的不完整的宠物，即排除它就是有效数量）
     */
    private var excludeGroupTwoPetCount = 0

    /**
     * 埋点字段：记录有效扫描出来的宠物图片数量
     */
    private var excludeGroupTwoImageIdList = mutableListOf<String>()

    /**
     * 埋点字段：记录有效扫描出来的宠物视频数量
     */
    private var excludeGroupTwoVideoIdList = mutableListOf<String>()

    private var petClusterApi: PetClusterApi? = null

    override fun getScanType() = PET_SCAN

    override fun getSceneName() = SCENE_NAME

    override fun runTaskIsNeedCharging() = false

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        GLog.d(TAG, LogFlag.DL) { "onScan, start pet scan triggerType = $triggerType" }
        super.onScan(triggerType, config)
        if (isLowPowerConsumptionStopScan()) {
            // 停止扫描
            trackLowPowerStopScan()
            GLog.d(TAG, LogFlag.DL) { "onScan, run in low power consumption mode, no need to scan" }
            return
        }

        if (!init()) {
            GLog.w(TAG, LogFlag.DL) { "onScan, init fail." }
            release()
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_INIT, triggerType) // 扫描中断，记录原因
            return
        }
        if (!prepareScanData(triggerType)) {
            GLog.w(TAG, LogFlag.DL) { "onScan, prepare scan data fail." }
            release()
            return
        }
        scan()
        group()
        release()
        GLog.d(TAG, LogFlag.DL) { "onScan, end" }
    }

    override fun onMonitorRecord(event: MonitorEvent, reachThreshold: Boolean) {
        super.onMonitorRecord(event, reachThreshold)
        markConditionsOnStartScan()
        GLog.d(TAG, LogFlag.DL) { "onMonitorRecord,  event = $event, reachThreshold = $reachThreshold" }
        if (startGroupTime > 0) {
            trackPetGroup(event, true)
        } else {
            trackPetScan(event, true)
        }
    }

    private fun isLowPowerConsumptionStopScan(): Boolean {
        if (!isCloudAllowLowConsumptionScan()) {
            GLog.d(TAG, LogFlag.DL) { "inLowPowerConsumptionScan cloud disable" }
            return false
        }
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            GLog.d(TAG, LogFlag.DL) { "inLowPowerConsumptionScan battery charging" }
            return false
        }
        return true
    }

    private fun trackLowPowerStopScan() {
        markConditionsOnStartScan()
        val endNativeHeapSize = getEndNativeHeapSize(null)
        val scanTrackInfo = generateScanTrackItem(
            SCENE_NAME,
            endNativeHeapSize,
            GalleryScanMonitor.ReasonType.REASON_LOW_POWER_CONSUMPTION,
            null
        )
        trackScanResult(scanTrackInfo)
    }

    private fun init(): Boolean {
        val aiPluginState = AISettings.getDetectData(mContext, SCENE_NAME_AI_PETS).state
        when (aiPluginState) {
            UnitState.STATE_UNAVAILABLE_NEED_DOWNLOAD -> {
                GLog.w(TAG, LogFlag.DL) { "init, ai_pets plugin not download, can't use scanner!" }
                return false
            }

            UnitState.STATE_UNAVAILABLE -> {
                GLog.w(TAG, LogFlag.DL) { "init, ai_pets plugin can't use!" }
                return false
            }
        }
        if (petClusterApi == null) petClusterApi = PetClusterApi(mContext)
        val newVersion = petClusterApi?.getModelVersionName()
        if (newVersion.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "init, ai_pets plugin version is null!!" }
            return false
        }
        val oldVersion = ConfigAbilityWrapper.getString(PET_VERSION)
        currentVersion = newVersion
        GLog.d(TAG, LogFlag.DL) { "init,  oldVersion = $oldVersion, newVersion = $newVersion" }
        if (oldVersion != newVersion) {
            mContext.applicationContext.getAppAbility<IConfigSetterAbility>()?.use {
                it.setStringConfig(PET_VERSION, newVersion)
                it.setBooleanConfig(PET_SDK_UPDATED, true)
            }
        }
        isApiUpdated = ConfigAbilityWrapper.getBoolean(PET_SDK_UPDATED)
        scanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.PET_SCAN_COUNT_24H_KEY, 0)
        if (isApiUpdated) {
            GalleryScanPetProviderHelper.deleteInvalidPet(oldVersion)
        }
        return true
    }

    /**
     * 1 准备需要扫描的数据，准备的数据结果分别保存在 [newImages] 和 [updateImages] 里。
     */
    private fun prepareScanData(triggerType: Int): Boolean {
        if (isInterrupt) return false
        //  1.1 查询已经扫描过的数据
        val imageScannedList = GalleryScanPetProviderHelper.getScannedItemsWithoutRecycled()
        GLog.d(TAG, LogFlag.DL) { "prepareScanData,  imageScannedList: ${imageScannedList.size}" }

        if (isInterrupt) return false
        if (imageScannedList.isNotEmpty()) {
            //  1.2 查询所有 local 表数据 images
            val imageLocalList = GalleryScanProviderHelper.getAllImageFromFileTable(mContext)
            GLog.d(TAG, LogFlag.DL) { "prepareScanData,  imageLocalList: ${imageLocalList.size}" }

            //  1.3 将 imageScannedList 已扫描的数据，筛选并分类到 deleteImageList, updateImageInvalidList, updateImageInvalidWithMediaIdList 的数据集合中。
            val deleteImageList = mutableListOf<BaseImageInfo>()
            val updateImageInvalidList = mutableListOf<BaseImageInfo>()
            val updateImageInvalidWithMediaIdList = mutableListOf<BaseImageInfo>()
            splitImagesScanned(imageScannedList, imageLocalList, deleteImageList, updateImageInvalidList, updateImageInvalidWithMediaIdList)
            GLog.d(TAG, LogFlag.DL) {
                "prepareScanData,  deleteImageList: ${deleteImageList.size}," +
                        " updateImageInvalidList: ${updateImageInvalidList.size}," +
                        " updateImageInvalidWithMediaIdList: ${updateImageInvalidWithMediaIdList.size}"
            }

            //  1.4 将 imageScannedList 移除包含 deleteImageList 的数据，更新 updateImageInvalidList, updateImageInvalidWithMediaIdList 数据
            GalleryScanPetProviderHelper.deleteImages(deleteImageList)
            GalleryScanPetProviderHelper.updateImagesInvalid(updateImageInvalidList)
            GalleryScanPetProviderHelper.updateImageInvalidWithMediaId(updateImageInvalidWithMediaIdList)
            if (deleteImageList.isNotEmpty() || updateImageInvalidList.isNotEmpty() || updateImageInvalidWithMediaIdList.isNotEmpty()) {
                // deleteImageList, updateImageInvalidList, updateImageInvalidWithMediaIdList 合并通知
                notifyTableChange(GalleryDbDao.TableType.PET, IDao.DaoType.GALLERY)
            }

            //  1.5 将 imageLocalList 数据分类出未扫描的数据 和 旧扫描模型的数据
            splitImagesLocal(imageScannedList, imageLocalList, deleteImageList)
        } else {
            // 首次扫描，没有任何已扫描的数据。
            newImages = GalleryScanProviderHelper.getMediaItemFromMediaProvider(mContext)
            GLog.d(TAG, LogFlag.DL) { "prepareScanData,  first time scan , newImages = ${newImages.size}" }
        }

        mNewCount = newImages.size
        mUpdateCount = updateImages.size
        mAllCount = mNewCount + mUpdateCount
        GLog.d(TAG, LogFlag.DL) { "prepareScanData,  newCount = $mNewCount, updateCount = $mUpdateCount, allCount = $mAllCount" }

        if ((mAllCount == 0) && !GalleryScanPetProviderHelper.haveNotGroupPet()) {
            GLog.i(TAG, LogFlag.DL) { "prepareScanData,  has no new image and update image, do not need to continue!" }
            updateLastScanTime()
            release()
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_EMPTY, triggerType) // 扫描中断，记录原因
            return false
        }

        return true
    }

    /**
     * 将 imagesScanned 已扫描的数据，筛选并分类到不同的数据集合中。
     * @param imageScannedList 已扫描的数据集合
     * @param imageLocalList 全部本地数据集合
     * @param deleteImageList 需要删除的已扫描数据集合，用于接收结果
     * @param updateImageInvalidList 需要更新的已扫描数据集合，用于接收结果
     * @param updateImageInvalidWithMediaIdList 需要更新的已扫描数据集合，用于接收结果
     */
    private fun splitImagesScanned(
        imageScannedList: List<PetImageInfo>,
        imageLocalList: List<BaseImageInfo>,
        deleteImageList: MutableList<BaseImageInfo>,
        updateImageInvalidList: MutableList<BaseImageInfo>,
        updateImageInvalidWithMediaIdList: MutableList<BaseImageInfo>
    ) {
        val localImagePathMap = GalleryScanDataManager.translateListToPathMap(imageLocalList)
        imageScannedList.forEach { imageScanned ->
            val localImageInfo = localImagePathMap[imageScanned.mFilePath.lowercase()]
            if (localImageInfo == null) {
                //  1.3 筛选出pet表存在，但local表中不存在的数据，添加到 deleteImageList 中
                deleteImageList.add(imageScanned)
            } else {
                val isInvalidChanged = (localImageInfo.mInvalid != imageScanned.mInvalid)
                val isMediaIdChanged = (localImageInfo.mMediaId != imageScanned.mMediaId)
                if (isInvalidChanged || isMediaIdChanged) {
                    imageScanned.mInvalid = localImageInfo.mInvalid
                    imageScanned.mMediaId = localImageInfo.mMediaId
                }
                when {
                    //  1.4 筛选出pet表与local表的 media_Id 不一样的数据 updateImageInvalidWithMediaId，更新 invalid/ is_chosen/ is_recycled
                    isMediaIdChanged -> updateImageInvalidWithMediaIdList.add(imageScanned)

                    //  1.4 筛选出pet表与local表的 invalid 不一样的数据 updateImageInvalid，更新 invalid / is_chosen
                    isInvalidChanged -> updateImageInvalidList.add(imageScanned)
                }
            }
        }
    }

    /**
     * 将 imagesLocal 的数据，筛选并分类到【未扫描】和【需要更新】的数据集合中。
     */
    private fun splitImagesLocal(
        imagesScanned: List<PetImageInfo>,
        imagesLocal: List<BaseImageInfo>,
        deleteImages: MutableList<BaseImageInfo>
    ) {
        val scannedImageMap = GalleryScanDataManager.translateListToPathMap(imagesScanned)
        if (deleteImages.isNotEmpty()) {
            deleteImages.forEach { image -> scannedImageMap.remove(image.mFilePath.lowercase()) }
        }

        // 对比来自local_media和scan_face中的数据，区分出新增和更新的图片
        val newImage = mutableListOf<BaseImageInfo>()
        val updateImage = mutableListOf<BaseImageInfo>()
        imagesLocal.forEach { image ->
            if (!image.mInvalid) {
                val petImageInfo = scannedImageMap[image.mFilePath.lowercase()]
                if (petImageInfo == null) {
                    newImage.add(image)
                } else if (petImageInfo.modelVersion != currentVersion) {
                    updateImage.add(image)
                }
            }
        }
        newImages = GalleryScanProviderHelper.convertImageToMediaItem(newImage)
        updateImages = GalleryScanProviderHelper.convertImageToMediaItem(updateImage)
    }

    /**
     * 2 扫描文件
     */
    private fun scan() {
        GLog.d(TAG, LogFlag.DL) { "scan,  start." }
        markConditionsOnStartScan()
        mScanImageCount = 0
        mScanVideoCount = 0
        updateAbortFile()
        abortFile = GalleryScanProviderHelper.getAbortFile()

        //  2.1 文件扫描，区分图片和视频，有不同的扫描接口，参数不一样
        var time = System.currentTimeMillis()
        mRemainedNewCount = loopDealItems(newImages, this::loopScanNew)
        GLog.d(TAG, LogFlag.DL) { "scan,  loopScanNew total cost:${GLog.getTime(time)}ms" }

        time = System.currentTimeMillis()
        loopDealItems(updateImages, this::loopScanUpdate)
        GLog.d(TAG, LogFlag.DL) { "scan,  loopScanUpdate total cost:${GLog.getTime(time)}ms" }

        coverProcessor?.existWhenFinished()

        trackPetScan(null, false)
        GLog.d(TAG, LogFlag.DL) {
            "scan,  end. Scan image count = $mScanImageCount, video count = $mScanVideoCount, cost:${GLog.getTime(mStartScanTime)}ms"
        }
    }

    private fun updateAbortFile() {
        GalleryScanUtils.readAbortFile(mContext)?.let { abortFile ->
            if (abortFile.trim().isNotEmpty() && (abortFile != VIRTUAL_PATH_FOR_GROUP_ABORT_FILE)) {
                GalleryScanProviderHelper.updateAbortFile(abortFile)
            }
        }
    }

    /**
     * 按每组50个 MediaItem 来运行 [function]，分批处理扫描任务
     * @param mediaItemList 被扫描的数据集合
     * @param function 扫描任务
     */
    private fun loopDealItems(mediaItemList: MutableList<MediaItem>, function: (List<MediaItem>) -> Unit): Int {
        var remainSize = mediaItemList.size
        GLog.d(TAG, LogFlag.DL) { "loopDealItems,  mediaItemList.size = $remainSize" }
        val time = System.currentTimeMillis()
        val groupList = mutableListOf<MediaItem>()
        while (remainSize > 0 && isAllowContinueScan(scanCountNotInCharging, PET_SCAN_COUNT_24H_MAX)) {
            var groupCount = 0
            while ((groupCount < ONE_SCAN_COUNT) && mediaItemList.isNotEmpty()) {
                groupList.add(mediaItemList.removeAt(0))
                groupCount++
            }
            remainSize = mediaItemList.size
            function.invoke(groupList)
            addScanCountIfNoCharging(groupList.size)
            recodeScanInfoIfNeed(groupList.size)
            bindSmallCore()
            groupList.clear()
        }
        GLog.d(TAG, LogFlag.DL) { "loopDealItems,  cost:${GLog.getTime(time)}ms" }
        return remainSize
    }

    /**
     * 更新未充电时的扫描数量
     */
    private fun addScanCountIfNoCharging(count: Int): Boolean {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true
        }
        refreshScanRecordingIfNecessary(mContext)
        scanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.PET_SCAN_COUNT_24H_KEY, 0) + count
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.PET_SCAN_COUNT_24H_KEY, scanCountNotInCharging)
        return false
    }

    private fun recodeScanInfoIfNeed(count: Int) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return
        }
        val faceScanCountAll = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.PET_SCAN_COUNT_ALL_KEY, 0) + count
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.PET_SCAN_COUNT_ALL_KEY, faceScanCountAll)
        GalleryScanUtils.recordInfo(mContext, TAG, scanCountNotInCharging.toLong(), faceScanCountAll.toLong())
    }

    private fun loopScanNew(list: List<MediaItem>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "loopScanNew,  list is empty" }
            return
        }

        val time = System.currentTimeMillis()
        val insertList = mutableListOf<PetCluster>()
        list.forEach { item ->
            when (item) {
                is LocalImage -> mScanImageCount++
                is LocalVideo -> mScanVideoCount++
            }
            calculateScanFormatCount(item)
            val filePath = item.filePath
            if (abortFile.containsKey(filePath)) {
                GLog.w(TAG, LogFlag.DL) { "loopScanNew,  path: ${item.path},  this file is abort file!" }
                return@forEach
            }
            BreakpadMaster.setFilePath(filePath)
            //  2.2 扫描文件，可能存在多个宠物角色，把每个角色的扫描封装成数据对象，添加到缓存列表中。
            val newFaceInfoList = getPetCluster(item, time) ?: return@forEach
            insertList.addAll(newFaceInfoList)
        }
        //  2.3 将缓存列表的数据插入数据库中。
        GalleryScanPetProviderHelper.insertData(insertList)?.let { result ->
            triggerCoverTask(result, insertList)
        }
    }

    private fun loopScanUpdate(list: List<MediaItem>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "loopScanUpdate,  list is empty" }
            return
        }

        val time = System.currentTimeMillis()
        val dataMap = mutableMapOf<String, List<PetCluster>>()
        list.forEach { item ->
            when (item) {
                is LocalImage -> mScanImageCount++
                is LocalVideo -> mScanVideoCount++
            }
            calculateScanFormatCount(item)
            val filePath = item.filePath
            if (abortFile.containsKey(filePath)) {
                GLog.w(TAG, LogFlag.DL) { "loopScanUpdate,  path: ${item.path},  this file is abort file!" }
                return@forEach
            }
            BreakpadMaster.setFilePath(filePath)
            //  扫描文件，可能存在多个宠物角色，把每个角色的扫描封装成数据对象，添加到缓存列表中。
            val newFaceInfoList = getPetCluster(item, time) ?: return@forEach
            dataMap[filePath] = newFaceInfoList
        }

        //  将缓存列表的数据分拣出需要插入、更新、删除的数据集合。
        val updatePetClusterList = mutableListOf<PetCluster>()
        val insertPetClusterList = mutableListOf<PetCluster>()
        val deletePetIdList = mutableListOf<Long>()
        dataMap.forEach { (filePath, newPetClusterList) ->
            if (newPetClusterList.isEmpty()) {
                GLog.w(TAG, LogFlag.DL) { "loopScanUpdate,  newPetInfoList is empty" }
                return@forEach
            }
            insertPetClusterList.addAll(newPetClusterList)
            GalleryScanPetProviderHelper.classifyPetInfoList(filePath, newPetClusterList, updatePetClusterList, deletePetIdList)
        }
        insertPetClusterList.removeAll(updatePetClusterList)

        // 分别插入、更新、删除相应的数据
        GalleryScanPetProviderHelper.updateData(updatePetClusterList)
        GalleryScanPetProviderHelper.deleteData(deletePetIdList)
        GalleryScanPetProviderHelper.insertData(insertPetClusterList)?.let { result ->
            triggerCoverTask(result, insertPetClusterList)
        }
    }

    private fun getPetCluster(item: MediaItem, time: Long): List<PetCluster>? {
        var thumbnail: Bitmap? = null
        val petInfoList = when (item.mediaType) {
            MEDIA_TYPE_IMAGE -> {
                thumbnail = getThumbnail(item) ?: return null
                val currentTime = System.currentTimeMillis()
                petClusterApi?.detect(thumbnail, item.rotation)?.apply {
                    GLog.d(TAG, LogFlag.DL) {
                        "getPetCluster,  detect image cost:${GLog.getTime(currentTime)}ms, result:$size, data:${item.filePath}"
                    }
                }
            }

            MEDIA_TYPE_VIDEO -> {
                val currentTime = System.currentTimeMillis()
                petClusterApi?.detectVideo(item.contentUri, item.rotation)?.apply {
                    GLog.d(TAG, LogFlag.DL) {
                        "getPetCluster,  detect video cost:${GLog.getTime(currentTime)}ms, result:$size, data:${item.filePath}"
                    }
                }
            }

            else -> return null
        }
        val petClusterList = mutableListOf<PetCluster>()
        if (petInfoList == null) {
            // 如果返回的是 null， 说明扫描出现异常，直接返回 null 结果。
            GLog.w(TAG, LogFlag.DL) {
                "getPetCluster,  detect result is null, media type:${item.mediaType}, uri:${item.contentUri}, data:${item.filePath}"
            }
            return null
        } else if (petInfoList.isEmpty()) {
            // 如果返回的是 空集合， 说明该图没有扫出宠物角色。
            petClusterList.add(createPetCluster(item, time))
        } else {
            val setThumbnail: Boolean = petInfoList.size >= SET_THUMBNAIL_OF_FEATURE_NUM_MIN
            val allPetRectFList = getPetRectFList(petInfoList)
            val allPetSquareList = getPetSquareList(petInfoList)
            petInfoList.forEachIndexed { index, petInfo ->
                if ((petInfo.petsFeature == null) || (petInfo.petsFeature?.size == 0)) {
                    GLog.w(TAG, LogFlag.DL) { "getPetCluster,  feature is null, do not add to list!" }
                    return@forEachIndexed
                }
                createPetCluster(item, time).apply {
                    setPetInfo(petInfo)
                    thumbWidth = thumbnail?.getWidth()
                    thumbHeight = thumbnail?.getHeight()
                    if (setThumbnail) this.thumbnail = thumbnail
                    // 获取该图除 target 以外的，相同 data 的其他宠物脸的 rect。关联查询 scan_face 表，获取该图所有的人脸框 rect。
                    val otherPetRectFList = allPetRectFList.filterIndexed { filterIndex, _ -> index != filterIndex }
                    val faceRectFList = GalleryScanPersonPetProviderHelper.getFaceRectFByData(data)
                    finalScore = calculateFinalScore(this, otherPetRectFList, faceRectFList)
                    // 计算裁切后的头像封面框
                    val otherPetSquareList = allPetSquareList.filterIndexed { filterIndex, _ -> index != filterIndex }
                    val detection = getDetection(this, otherPetSquareList, faceRectFList)
                    faceRect = cropCoverDynamic(this, detection, (thumbWidth ?: 0), (thumbHeight ?: 0))
                    petClusterList.add(this)
                }
            }
            scanPetCount++
        }
        return petClusterList
    }

    /**
     * 获取除 [target] 框以外的，其他人脸框和宠物框的集合
     * @param target 目标宠物对象
     * @param otherPetSquareList 其他宠物框的集合
     * @param faceRectFList 人脸框的集合
     */
    private fun getDetection(target: PetCluster, otherPetSquareList: List<RectF>, faceRectFList: List<RectF>): List<RectF> {
        // 目标宠物的rect
        val targetRect = RectF(
            (target.left?.toFloat() ?: 0f),
            (target.top?.toFloat() ?: 0f),
            (target.right?.toFloat() ?: 0f),
            (target.bottom?.toFloat() ?: 0f)
        )
        // 获取该图的边界 width 和 height
        val width = target.thumbWidth?.toFloat() ?: 0f
        val height = target.thumbHeight?.toFloat() ?: 0f
        val result = mutableListOf<RectF>()
        result.addAll(otherPetSquareList)
        faceRectFList.forEach { faceRect ->
            val iou = calculateIou(targetRect, faceRect, width, height)
            // 把目标宠物与人脸框进行iou计算，将人脸误检的 faceRect 排除。当 iou > 0.8 则认为该人脸框误检，不添加到 result 中
            if (iou <= NUM_80_PERCENT) {
                result.add(faceRect)
            }
        }
        return result
    }

    /**
     * 创建 PetCluster 对象
     */
    private fun createPetCluster(item: MediaItem, time: Long): PetCluster {
        return PetCluster().apply {
            setMediaItem(item)
            groupID = GalleryScanUtils.GROUP_ID_0  // 未分组
            relationType = RelationShipType.NONE.ordinal
            scanDate = time
            modelVersion = currentVersion
            isDefaultCover = false
        }
    }

    private fun triggerCoverTask(insertResult: Array<BatchResult>, list: List<PetCluster>) {
        list.forEachIndexed { index, cluster ->
            if (cluster.mediaItemRef != null) {
                cluster.id = ContentUris.parseId(insertResult[index].uri)
                val cover = CoverTask().apply {
                    cluster.id?.let { mPersonId = it }
                    cluster.thumbWidth?.let { mThumbW = it }
                    cluster.thumbHeight?.let { mThumbH = it }
                    cluster.faceRect?.let { mFaceRect = it }
                    mMediaItem = cluster.mediaItemRef
                    mThumbnail = cluster.thumbnail
                }
                if (coverProcessor == null) {
                    coverProcessor = CoverBgProcessor()
                }
                coverProcessor?.addTask(cover)
            }
        }
    }

    /**
     * 3 聚合角色
     */
    private fun group() {
        GLog.d(TAG, LogFlag.DL) { "group,  start." }
        //  3.1 查询可以聚合的扫描数据
        if (!isApiUpdated && !GalleryScanPetProviderHelper.haveNotGroupPet()) {
            GLog.w(TAG, LogFlag.DL) { "group,  had no pet to group." }
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_NO_PET_TO_GROUP, mScanTriggerType)
            return
        }
        val petClusterList = GalleryScanPetProviderHelper.getAllPetWithFeature()
        GLog.d(TAG, LogFlag.DL) { "group,  all scan pet list size ${petClusterList.size}." }
        markConditionsOnStartGroup()
        if (isInterrupt || (!GalleryScanMonitor.isAllowContinueScan(mContext, false, false))) {
            GLog.w(TAG, LogFlag.DL) { "group,  Scan task is interrupt!" }
            trackPetGroup(null, false)
            return
        }
        //  3.2 重置需要重新聚合的数据，如单图的、模型升级后的非手动合并数据
        resetGroupId(petClusterList)
        val oldGroups = IntArray(petClusterList.size)
        petClusterList.forEachIndexed { index, petCluster -> petCluster.groupID?.let { oldGroups[index] = it } }
        //  3.3 调用聚合 sdk 接口，获取聚合的 group id 数组，在 group id 数组中筛选出最大的 group id
        val groups = petClusterApi?.cluster(convertToPetInfoList(petClusterList)) ?: IntArray(0)
        if (GProperty.DEBUG) {
            groups.forEachIndexed { index, id ->
                GLog.d(TAG, LogFlag.DF) { "groups,  cluster [$index] group_id:$id, data:${petClusterList[index].data}" }
            }
        }
        //  3.4 筛选出 group id = 1 的数据（单图数据），和其他 group id 数据，分别缓存到列表中。
        val oneRoleList = mutableListOf<PetCluster>()    // 缓存 group id = 1 的数据
        val updateGroupList = mutableListOf<PetCluster>() // 缓存 group id 需要刷新的数据
        splitList(petClusterList, oldGroups, groups, oneRoleList, updateGroupList)
        if (isInterrupt) {
            GLog.w(TAG, LogFlag.DL) { "group,  Scan task is interrupt!" }
            trackPetGroup(null, false)
            return
        }
        //  3.5 增量聚类过滤热补丁，修正group id
        fixGroupId(updateGroupList)
        //  3.6 将单图的 group id 数据更新到实体对象中，group id 从VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE开始计算
        var maxGroup = GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE
        oneRoleList.forEach { petCluster -> petCluster.groupID = ++maxGroup }
        //  3.7 将 group id 的数据更新到数据库中
        GalleryScanPetProviderHelper.updateGroupData(updateGroupList)
        trackPetGroup(null, false)
        GLog.d(TAG, LogFlag.DL) { "group,  cost:${GLog.getTime(startGroupTime)}" }
        //  3.8 计算默认封面，并更新数据库。
        updateDefaultCover(petClusterList)
    }

    /**
     * 筛选出 group id = 1 的数据（单图数据），和其他 group id 数据，分别缓存到列表中
     * oldGroups 里 >= 3 的 group id 不允许改变。
     */
    private fun splitList(
        petClusterList: List<PetCluster>,
        oldGroups: IntArray,
        groups: IntArray,
        oneRoleList: MutableList<PetCluster>,
        updateGroupList: MutableList<PetCluster>
    ) {
        val size = petClusterList.size
        if ((oldGroups.size != size) || (groups.size != size)) return

        petClusterList.forEachIndexed { index, petCluster ->
            val oldGroupId = oldGroups[index]
            val newGroupId = groups[index]
            if ((oldGroupId < GalleryScanUtils.GROUP_ID_3) && (oldGroupId != newGroupId)) {
                // 旧的 group id < 3 （即当前数据未聚类成功），且新旧 group id 有变化时，需要更新 group id，进行聚类
                if (GProperty.DEBUG) {
                    GLog.d(TAG, LogFlag.DF) { "splitList,  [$index] oldGroupId:$oldGroupId , newGroupId:$newGroupId" }
                }
                if (newGroupId == GalleryScanUtils.GROUP_ID_1) {
                    oneRoleList.add(petCluster)
                } else {
                    petCluster.groupID = newGroupId
                }
                updateGroupList.add(petCluster)
                computePetCountForTrack(newGroupId, petCluster)
            }
        }
    }

    private fun markConditionsOnStartGroup() {
        startGroupTime = System.currentTimeMillis()
        startGroupBattery = BatteryStatusUtil.getCurrentBatteryPercent(mContext)
        startGroupTemperature = TemperatureUtil.getCurrentTemperature()
        mStartNativeHeapSize = Debug.getNativeHeapSize()
    }

    private fun resetGroupId(list: List<PetCluster>) {
        // 单图的数据，重新聚合。
        list.forEach { info ->
            info.groupID?.let { groupID ->
                if ((groupID > GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE) || (groupID == GalleryScanUtils.GROUP_ID_2)) {
                    info.groupID = GalleryScanUtils.GROUP_ID_0
                }
            }
        }
    }

    private fun convertToPetInfoList(list: List<PetCluster>): List<PetInfo> {
        val result = mutableListOf<PetInfo>()
        list.forEach { petCluster ->
            val left = petCluster.left ?: 0
            val top = petCluster.top ?: 0
            val right = petCluster.right ?: 0
            val bottom = petCluster.bottom ?: 0
            val rect = Rect(left, top, right, bottom)
            PetInfo(
                petsFeature = petCluster.feature,
                petsRect = rect,
                appearMul = petCluster.appearMul ?: 0,
                petCoverScore = petCluster.petCoverScore ?: 0f,
                petCoverWeight = petCluster.petCoverWeight ?: 0f,
                score = petCluster.score ?: 0f,
                groupId = petCluster.groupID ?: 0
            ).apply { result.add(this) }
        }
        return result
    }

    private fun computePetCountForTrack(newGroupId: Int, pet: PetCluster) {
        if (newGroupId != GalleryScanUtils.GROUP_ID_2) {
            excludeGroupTwoPetCount++
            when (pet.mediaType) {
                MEDIA_TYPE_IMAGE -> {
                    if (!excludeGroupTwoImageIdList.contains(pet.data)) {
                        pet.data?.let { excludeGroupTwoImageIdList.add(it) }
                    }
                }

                MEDIA_TYPE_VIDEO -> {
                    if (!excludeGroupTwoVideoIdList.contains(pet.data)) {
                        pet.data?.let { excludeGroupTwoVideoIdList.add(it) }
                    }
                }
            }
        }
    }

    /**
     * 将 [allUpdateList] 转换成 以 group id 为 Key 的数据集合，每一个 PetCluster List 就是相同图集的数据
     */
    private fun convertToMap(allUpdateList: List<PetCluster>): MutableMap<Int, MutableList<PetCluster>> {
        val map = mutableMapOf<Int, MutableList<PetCluster>>()
        // 遍历每一条记录，按 group id 进行分组，保存到 map 中
        allUpdateList.forEach { target ->
            val groupId = target.groupID ?: 0
            if ((groupId < GalleryScanUtils.GROUP_ID_3) || (groupId >= GalleryScanUtils.VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE)) {
                // group id < 3 或 group id > 1073741823，说明当前数据不是有效的聚类数据，不需要更新默认封面
                return@forEach
            }
            if (!map.containsKey(groupId)) { // 不包含 group id，说明没有添加过，需要新创建一个
                map[groupId] = mutableListOf()
            }
            map[groupId]?.add(target)
        }
        return map
    }

    /**
     * 4 计算默认封面，并更新到数据库中
     */
    private fun updateDefaultCover(allUpdateList: List<PetCluster>) {
        GLog.d(TAG, LogFlag.DL) { "updateDefaultCover, start. allUpdateList.size = ${allUpdateList.size}" }
        var time = System.currentTimeMillis()
        val map = convertToMap(allUpdateList)
        val updateClusterList = mutableListOf<PetCluster>()
        map.forEach { (_, clusterList) ->
            var imageMaxFinalScore = 0f
            var videoMaxFinalScore = 0f
            var imageMaxFinalScoreCluster: PetCluster? = null
            var videoMaxFinalScoreCluster: PetCluster? = null
            var maxFinalScoreCluster: PetCluster? = null
            var defaultCoverCluster: PetCluster? = null
            clusterList.forEach { cluster ->
                val finalScore = (cluster.finalScore ?: 0f)
                when (cluster.mediaType) {
                    MEDIA_TYPE_IMAGE -> { // 找到分数最高的图片
                        if (imageMaxFinalScore < finalScore) {
                            imageMaxFinalScore = finalScore
                            imageMaxFinalScoreCluster = cluster
                        }
                    }

                    MEDIA_TYPE_VIDEO -> { // 找到分数最高的视频
                        if (videoMaxFinalScore < finalScore) {
                            videoMaxFinalScore = finalScore
                            videoMaxFinalScoreCluster = cluster
                        }
                    }
                }
                if (cluster.isDefaultCover == true) { // 找到当前默认的封面
                    defaultCoverCluster = cluster
                }
            }

            // 优先使用图片作为封面，如果没有图片，才使用视频作为封面
            videoMaxFinalScoreCluster?.let { maxFinalScoreCluster = it }
            imageMaxFinalScoreCluster?.let { maxFinalScoreCluster = it }

            // 最高分 和 默认封面不是同一张图时，更新它们的 [isDefaultCover]
            if (defaultCoverCluster != maxFinalScoreCluster) {
                maxFinalScoreCluster?.isDefaultCover = true
                defaultCoverCluster?.isDefaultCover = false
                defaultCoverCluster?.let { updateClusterList.add(it) }
                maxFinalScoreCluster?.let { updateClusterList.add(it) }
            }
        }
        GLog.d(TAG, LogFlag.DL) {
            "updateDefaultCover, map size:${map.size}, update list size:${updateClusterList.size}, cost:${GLog.getTime(time)}ms"
        }

        time = System.currentTimeMillis()
        // 更新默认封面到数据库中
        GalleryScanPetProviderHelper.updateDefaultCover(updateClusterList)
        GLog.d(TAG, LogFlag.DL) { "updateDefaultCover, update default cover cost:${GLog.getTime(time)}ms" }
    }

    /**
     * 获取宠物的头像 rect 集合
     */
    private fun getPetRectFList(petInfoList: List<PetInfo>): List<RectF> {
        val result = mutableListOf<RectF>()
        petInfoList.forEach { petInfo ->
            petInfo.petsRect?.run {
                RectF(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat()).let { result.add(it) }
            }
        }
        return result
    }

    /**
     * 获取宠物的头像 rect 集合。如果 petInfo 是脸框时，会被转换成正方形。
     */
    private fun getPetSquareList(petInfoList: List<PetInfo>): List<RectF> {
        val result = mutableListOf<RectF>()
        petInfoList.forEach { petInfo ->
            petInfo.petsRect?.run {
                var rect = RectF(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat())
                // appearMul = 0 时，代表脸框；appearMul = 1 时，代表body。petInfo 是脸框是，则 rect 需要转换成正方形
                if (petInfo.appearMul == 0) {
                    rect = detRectangle(rect)
                }
                result.add(rect)
            }
        }
        return result
    }

    /**
     * 计算 finalScore 宠物头像的分数。该值会用于默认封面的判断。
     */
    private fun calculateFinalScore(target: PetCluster, petRectFList: List<RectF>, faceRectFList: List<RectF>): Float {
        // 目标宠物的rect
        val targetRect = RectF(
            (target.left?.toFloat() ?: 0f),
            (target.top?.toFloat() ?: 0f),
            (target.right?.toFloat() ?: 0f),
            (target.bottom?.toFloat() ?: 0f)
        )

        // 获取该图的边界 width 和 height
        val width = target.thumbWidth?.toFloat() ?: 0f
        val height = target.thumbHeight?.toFloat() ?: 0f

        // 将目标宠物的 rect 与所有其他角色的 rect 两两计算，得到最大 Iou
        val maxPetIou = calculateIou(targetRect, petRectFList, width, height)
        val maxPersonIou = calculateIou(targetRect, faceRectFList, width, height, true)
        val maxIou = max(maxPetIou, maxPersonIou)
        val dependenceScore = getDependenceScore(maxIou)

        // 封面得分公式：petCoverWeight * (dependenceScore + petCoverScore)
        val petCoverWeight = target.petCoverWeight ?: 0f
        val petCoverScore = target.petCoverScore ?: 0f
        return petCoverWeight * (dependenceScore + petCoverScore)
    }

    /**
     * 将 [det] 转换为正方形框
     */
    private fun detRectangle(det: RectF): RectF {
        val centerX = det.centerX()
        val centerY = det.centerY()
        val width = abs(det.width() + 1)
        val height = abs(det.height() + 1)
        val newSize = sqrt(width * height) * 2
        return RectF(
            (centerX - newSize * NUM_50_PERCENT),
            (centerX + newSize * NUM_50_PERCENT),
            (centerY - newSize * NUM_70_PERCENT),
            (centerX + newSize * NUM_30_PERCENT)
        )
    }

    /**
     * 判断 [box1] 和 [box2] 是否相交
     */
    private fun isInBox(box1: RectF, box2: RectF): Boolean {
        val xOverlap = !((box1.right < box2.left) || (box2.right < box1.left))
        val yOverlap = !((box1.bottom < box2.top) || (box2.bottom < box1.top))
        return xOverlap && yOverlap
    }

    /**
     * 检查是否有重叠
     */
    private fun isLineUnion(l1s: Float, l1e: Float, l2s: Float, l2e: Float): Boolean {
        return max(l1s, l2s) <= min(l1e, l2e)
    }

    /**
     * 计算 finalExpand 的值，该值用于扩充宠物框
     */
    private fun calculateFinalExpand(originExpand: Float, box: RectF, xMin: Float, yMin: Float, xMax: Float, yMax: Float): Float {
        var finalExpand = originExpand
        if (isLineUnion(box.top, box.bottom, yMin, yMax)) { // y方向有交集
            if (xMin >= box.right) {
                finalExpand = min(finalExpand, (xMin - box.right))
            } else if (xMax <= box.left) {
                finalExpand = min(finalExpand, (box.left - xMax))
            }
        } else { // y方向无交集
            if (isLineUnion(box.left, box.right, xMin, xMax)) { // x方向有交集
                if (yMin >= box.bottom) {
                    finalExpand = min(finalExpand, (yMin - box.bottom))
                } else if (yMax <= box.top) {
                    finalExpand = min(finalExpand, (box.top - yMax))
                }
            } else { // x方向无交集
                if ((box.left >= xMax) && (box.top >= yMax)) { // 右下角
                    finalExpand = min(finalExpand, max((box.left - xMax), (box.top - yMax)))
                } else if ((box.right <= xMin) && (box.bottom <= yMin)) { // 左上角
                    finalExpand = min(finalExpand, max((xMin - box.right), (yMin - box.bottom)))
                } else if ((box.left >= xMax) && (box.bottom <= yMin)) { // 右上角
                    finalExpand = min(finalExpand, max((box.left - xMax), (yMin - box.bottom)))
                } else if ((box.right <= xMin) && (box.top >= yMax)) { // 左下角
                    finalExpand = min(finalExpand, max((xMin - box.right), (box.top - yMax)))
                }
            }
        }
        return finalExpand
    }

    /**
     * 计算裁切后的头像封面框
     */
    private fun cropCoverDynamic(target: PetCluster, detection: List<RectF>, thumbWidth: Int, thumbHeight: Int): Rect {
        // 目标宠物的rect
        val targetRect = RectF(
            (target.left?.toFloat() ?: 0f),
            (target.top?.toFloat() ?: 0f),
            (target.right?.toFloat() ?: 0f),
            (target.bottom?.toFloat() ?: 0f)
        )
        val det = detRectangle(targetRect)
        val xMin = det.left
        val yMin = det.top
        val xMax = det.right
        val yMax = det.bottom
        if ((xMin < 0) || (yMin < 0) || (xMax > thumbWidth) || (yMax > thumbHeight)) {
            return det.toRect()
        }
        val width = abs(xMax - xMin + 1f) * NUM_25_PERCENT
        val margin = min(xMin, min(yMin, min((thumbWidth - xMax), (thumbHeight - yMax))))
        var finalExpand = min(width, margin)

        run {
            detection.forEach { box ->
                if (isInBox(box, det)) { // box框和det框相交
                    finalExpand = 0f
                    return@run
                } else {
                    finalExpand = calculateFinalExpand(finalExpand, box, xMin, yMin, xMax, yMax)
                }
            }
        }
        return RectF((xMin - finalExpand), (yMin - finalExpand), (xMax + finalExpand), (yMax + finalExpand)).toRect()
    }

    /**
     * 获取 dependenceScore 分数
     */
    private fun getDependenceScore(iou: Float): Float {
        return when {
            (iou <= IOU_LEVEL_ONE) -> SCORE_TEN * SCORE_WEIGHT
            (iou <= IOU_LEVEL_TWO) -> SCORE_FIVE * SCORE_WEIGHT
            else -> SCORE_WEIGHT
        }
    }

    /**
     * 计算目标box与其他box，两两之间的iou最大值。
     * @param targetBox 目标box
     * @param otherBoxes 其他box列表
     * @param width 宽度边界最大值
     * @param height 高度边界最大值
     * @param isFromPerson otherBoxes 的数据是否来自人脸表
     * @return 返回最大的iou值
     */
    private fun calculateIou(targetBox: RectF, otherBoxes: List<RectF>, width: Float, height: Float, isFromPerson: Boolean = false): Float {
        var maxIou = 0f
        otherBoxes.forEach { other ->
            var iou = calculateIou(targetBox, other, width, height)
            if (isFromPerson && (iou > NUM_90_PERCENT)) {
                iou = 0f
            }
            maxIou = max(maxIou, iou)
        }
        return maxIou
    }

    /**
     * 计算2个box的iou分数
     */
    private fun calculateIou(box1: RectF, box2: RectF, width: Float, height: Float): Float {
        val expandBox1 = expand(box1, width, height)
        val expandBox2 = expand(box2, width, height)
        // 计算面积
        val rect1Area = expandBox1.run { (bottom - top) * (right - left) }
        val rect2Area = expandBox2.run { (bottom - top) * (right - left) }
        // 面积总和
        val sumArea = rect1Area + rect2Area
        val x1 = max(expandBox1.left, expandBox2.left)
        val y1 = max(expandBox1.top, expandBox2.top)
        val x2 = min(expandBox1.right, expandBox2.right)
        val y2 = min(expandBox1.bottom, expandBox2.bottom)
        // 两个box重叠区域面积
        val intersect = max(0f, x2 - x1) * max(0f, y2 - y1)
        return max((intersect / rect1Area), (intersect / rect2Area))
    }

    /**
     * 扩展box范围
     * @param box 扩展目标的原始坐标
     * @param width 宽度边界最大值
     * @param height 高度边界最大值
     */
    private fun expand(box: RectF, width: Float, height: Float): RectF {
        // 中心点计算
        val centerX = box.centerX()
        val centerY = box.centerY()
        // 原先 box 长宽计算
        val boxWidth = box.width()
        val boxHeight = box.height()
        val area = boxWidth * boxHeight
        // 扩大后的长宽计算 sqrt 为开根号运算
        val expandWidth = sqrt(area) * NUM_TWO
        val expandHeight = sqrt(area) * NUM_TWO
        // 保持中心点不变，用扩大后的长宽计算扩大后的box左上角和右下角坐标
        val expandXMin = centerX - expandWidth * NUM_50_PERCENT
        val expandXMax = centerX + expandWidth * NUM_50_PERCENT
        val expandYMin = centerY - expandHeight * NUM_70_PERCENT
        val expandYMax = centerY + expandHeight * NUM_30_PERCENT
        // 判断是否超出图片，如果超出则已图片size为准
        val finalXStart = if (expandXMin > 0) expandXMin else 0f
        val finalYStart = if (expandYMin > 0) expandYMin else 0f
        val finalXEnd = if (expandXMax < width) expandXMax else width
        val finalYEnd = if (expandYMax < height) expandYMax else height
        return RectF(finalXStart, finalYStart, finalXEnd, finalYEnd)
    }

    private fun release() {
        GLog.d(TAG, LogFlag.DL) { "release" }
        mContext.applicationContext.getAppAbility<IConfigSetterAbility>()?.use {
            it.setBooleanConfig(PET_SDK_UPDATED, false)
        }
        petClusterApi?.release()
        petClusterApi = null
    }

    private fun trackPetScan(event: MonitorEvent?, isMonitor: Boolean) {
        val reasonType = getReasonType(false, scanCountNotInCharging, PET_SCAN_COUNT_24H_MAX)
        val endNativeHeapSize = getEndNativeHeapSize(event)
        val additionalMap: MutableMap<String, String> = HashMap()
        additionalMap[SCAN_PET_COUNT] = scanPetCount.toString()
        additionalMap[SCAN_FORMAT_COUNT] = mScanFormatCount.toString()
        additionalMap[IS_MONITOR] = isMonitor.toString()
        val scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap)
        GLog.d(TAG, LogFlag.DL) { "trackPetScan,  recordItem = $scanTrackInfo" }
        trackScanResult(scanTrackInfo)
    }

    private fun trackPetGroup(event: MonitorEvent?, isMonitor: Boolean) {
        val reasonType = getReasonType(false, scanCountNotInCharging, PET_SCAN_COUNT_24H_MAX)
        val endNativeHeapSize = getEndNativeHeapSize(event)
        val additionalMap: MutableMap<String, String> = java.util.HashMap()
        additionalMap[GROUP_PET_COUNT] = excludeGroupTwoPetCount.toString()
        additionalMap[IS_MONITOR] = isMonitor.toString()
        val scanTrackInfo = ScanTrackInfo(
            SCENE_SCANNER_GROUP,
            startGroupTime,
            System.currentTimeMillis(),
            startGroupBattery,
            BatteryStatusUtil.getCurrentBatteryPercent(mContext),
            false,
            mScanTriggerType,
            excludeGroupTwoImageIdList.size,
            excludeGroupTwoVideoIdList.size,
            startGroupTemperature,
            TemperatureUtil.getCurrentTemperature(),
            mStartNativeHeapSize,
            endNativeHeapSize,
            reasonType,
            BatteryStatusUtil.isBatteryInCharging(false),
            additionalMap
        )
        GLog.d(TAG, LogFlag.DL) { "trackPetGroup, recordItem = $scanTrackInfo" }
        trackScanResult(scanTrackInfo)
    }

    /**
     * 增量聚类过滤热补丁
     */
    private fun fixGroupId(updateGroupList: MutableList<PetCluster>) {
        val groupMap = mutableMapOf<Int, MutableList<PetCluster>>()
        // 将数据分别聚类到各自的 group id 之下
        updateGroupList.forEach { petCluster ->
            val groupId = petCluster.groupID ?: 0
            if (groupId > GalleryScanUtils.GROUP_ID_2) {
                if (!groupMap.containsKey(groupId)) {
                    groupMap[groupId] = mutableListOf()
                }
                groupMap[groupId]?.add(petCluster)
            }
        }

        // 查找未聚类的有效数据的起始位置，补丁只修改新聚类的数据。
        val minId = GalleryScanPetProviderHelper.queryMinIdNotGroup()
        groupMap.forEach { (_, clusterList) ->
            val size = clusterList.size
            var appearMulCount = 0
            var imageCount = 0
            clusterList.forEach { petCluster ->
                if (petCluster.appearMul == 0) appearMulCount++
                if (petCluster.mediaType == 1) imageCount++
            }
            val faceRatio = appearMulCount.toFloat() / size
            val imgRatio = imageCount.toFloat() / size
            if ((faceRatio < NUM_60_PERCENT) && (imgRatio > NUM_60_PERCENT)) {
                clusterList.forEach { petCluster ->
                    if ((petCluster.id ?: 0) >= minId) { // 大于等于起始位置，就是新聚类的数据
                        when (petCluster.appearMul) {
                            0 -> petCluster.groupID = GalleryScanUtils.GROUP_ID_1
                            1 -> petCluster.groupID = GalleryScanUtils.GROUP_ID_2
                        }
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = ScanConst.TAG + "PetScanner"
        private const val SCENE_NAME = "PetScanner"
        private const val SCENE_NAME_AI_PETS = "ai_pets"
        private const val SCENE_SCANNER_GROUP = "${SCENE_NAME}_Group"
        private const val SCENE_SCANNER_EMPTY = "${SCENE_NAME}_Empty"
        private const val SCENE_SCANNER_INIT = "${SCENE_NAME}_Init"
        private const val SCENE_SCANNER_NO_PET_TO_GROUP = "${SCENE_NAME}_NoPetToGroup"
        private const val GROUP_PET_COUNT = "group_pet_count"
        private const val SCAN_PET_COUNT = "scan_pet_count"
        private const val VIRTUAL_PATH_FOR_GROUP_ABORT_FILE = "/tag/pet/group/AbortFile"
        private const val ONE_SCAN_COUNT = 50
        private const val SET_THUMBNAIL_OF_FEATURE_NUM_MIN = 3
        private const val SCORE_WEIGHT = 1f
        private const val SCORE_TEN = 10
        private const val SCORE_FIVE = 5
        private const val IOU_LEVEL_ONE = 0f
        private const val IOU_LEVEL_TWO = 0.1f
        private const val NUM_TWO = 2f
        private const val NUM_90_PERCENT = 0.9f
        private const val NUM_80_PERCENT = 0.8f
        private const val NUM_70_PERCENT = 0.7f
        private const val NUM_60_PERCENT = 0.6f
        private const val NUM_50_PERCENT = 0.5f
        private const val NUM_30_PERCENT = 0.3f
        private const val NUM_25_PERCENT = 0.25f
    }
}