/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ImageLabelListConfig.kt
 * Description:
 * Version: 1.0
 * Date: 2023/6/19
 * Author: houdonggu@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * houdonggu@Apps.Gallery3D      2023/6/19      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.scan.label

import com.cv.imageapi.model.SelectionLabelInfo
import com.oplus.gallery.business_lib.model.config.allowlist.AbsAllowListParser
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil.SPLIT_COMMA_SEPARATOR
import okio.use
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets

private const val DEFAULT_CONFIG_FILE = "default_image_label_lists_config.txt" // 对应sdk提供的blacklist.txt
private const val REMOTE_CONFIG_FILE = "app_gallery_image_label_lists_config"
private const val CURRENT_VERSION_KEY = "local_image_label_version"

/**
 * 图片配置文件解析，图片黑名单过滤
 */
object ImageLabelListConfig : AbsAllowListParser(REMOTE_CONFIG_FILE, DEFAULT_CONFIG_FILE, CURRENT_VERSION_KEY) {
    private const val TAG = "ImageLabelListConfig"

    private const val LINE_ITEM_COUNT = 4
    private const val COLUMN_TOP_LABEL_ID = 0
    private const val COLUMN_LABEL_ID = 1
    private const val COLUMN_MIDDLE_LABEL = 2
    private const val COLUMN_BLACK_LABEL = 3
    private const val LABEL_YES = 1

    val allLabelMap = HashMap<Int, SelectionLabelInfo>()
    val blackLabelList = mutableListOf<Int>()

    init {
        loadConfig()
    }

    fun getVersion(): Int {
        return mCurVersion
    }

    override fun parseVersion(inputStream: InputStream?): Int {
        inputStream?.use {
            runCatching {
                return BufferedReader(InputStreamReader(inputStream, StandardCharsets.UTF_8)).use { br ->
                    br.readLine()?.toInt() ?: INVALID_VERSION
                }
            }.onFailure {
                GLog.d(TAG) { "parseVersion onFailure throw:${it.message}" }
            }
        }
        return INVALID_VERSION
    }

    override fun loadRemoteConfig(): Boolean {
        return inputStreamFromRemote?.use {
            loadLabelConfigIfNeed(it).apply {
                GLog.d(TAG) { "loadRemoteConfig result:$this" }
            }
        } ?: false
    }

    override fun loadDefaultConfig(): Boolean {
        return inputStreamFromAsset?.use {
            loadLabelConfigIfNeed(it).apply {
                GLog.d(TAG) { "loadDefaultConfig result:$this" }
            }
        } ?: false
    }

    override fun dump() {
        GLog.d(TAG, "dump, listLabelAll:$allLabelMap listLabelBlack:$blackLabelList")
    }

    override fun getTag(): String {
        return TAG
    }

    override fun saveConfig(): Boolean {
        return true
    }

    private fun loadLabelConfigIfNeed(inputStream: InputStream?): Boolean {
        if (allLabelMap.isNotEmpty() && blackLabelList.isNotEmpty()) {
            return true
        }
        runCatching {
            parseBlock(inputStream)
            return true
        }.onFailure {
            GLog.w(TAG, "loadLabelConfigIfNeed return null ", it)
        }
        return false
    }

    private fun parseBlock(inputStream: InputStream?) {
        allLabelMap.clear()
        blackLabelList.clear()
        val time = System.currentTimeMillis()
        BufferedReader(InputStreamReader(inputStream, StandardCharsets.UTF_8)).use { br ->
            var line: String?
            // 第一行为版本号，这里不用
            br.readLine().apply {
                GLog.i(TAG) { "parseBlock readLine:$this" }
            }
            // 第二行为说明，这里不用
            br.readLine().apply {
                GLog.d(TAG) { "parseBlock readLine:$this" }
            }
            while (br.readLine().also { line = it } != null) {
                val blackLabelInfo = parseLine(line!!.trim())
                blackLabelInfo?.let {
                    allLabelMap[it.labelID] = it
                    if (it.isBlackLabel) {
                        blackLabelList.add(it.labelID)
                    }
                }
            }
            GLog.d(TAG, "parseBlock listLabelBlack.size:${blackLabelList.size} cost=${(System.currentTimeMillis() - time)}")
        }
    }

    private fun parseLine(line: String): SelectionLabelInfo? {
        line.split(SPLIT_COMMA_SEPARATOR).let {
            if (it.size == LINE_ITEM_COUNT) {
                val topLabelID = it[COLUMN_TOP_LABEL_ID].toInt()
                val labelID = it[COLUMN_LABEL_ID].toInt()
                val isMiddleLabel: Boolean = it[COLUMN_MIDDLE_LABEL].toInt() == LABEL_YES
                val isBlackLabel: Boolean = it[COLUMN_BLACK_LABEL].toInt() == LABEL_YES
                return SelectionLabelInfo(labelID, topLabelID, isMiddleLabel, isBlackLabel)
            }
        }
        return null
    }
}