/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonPetGroupScanner.kt
 ** Description: 人物与宠物合照后台扫描
 **
 ** Version: 1.0
 ** Date: 2025/05/06
 ** Author: v-qiaofangchun@OppoGallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  v-qiaofangchun@OppoGallery3D        2025/05/06   1.0          init
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.personpet

import android.content.Context
import android.provider.MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE
import android.provider.MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.business_lib.model.data.personpet.utils.PersonPetDataHelper.PersonPetEntry.Companion.ROLE_PERSON
import com.oplus.gallery.business_lib.model.data.personpet.utils.PersonPetDataHelper.PersonPetEntry.Companion.ROLE_PET
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import kotlin.math.max
import kotlin.math.min

/**
 * 人物与宠物合照后台扫描类
 */
class PersonPetGroupScanner(context: Context) : GalleryScan(context) {

    /**
     * 需要插入到数据库的集合
     */
    private val newList = mutableListOf<PersonPetGroupCluster>()

    /**
     * 需要更新到数据库的集合
     */
    private val updateList = mutableListOf<PersonPetGroupCluster>()

    /**
     * 需要在数据库中删除的集合
     */
    private val deleteList = mutableListOf<PersonPetGroupCluster>()

    override fun getScanType() = PERSON_PET_GROUP_SCAN

    override fun getSceneName() = SCENE_NAME

    override fun runTaskIsNeedCharging() = false

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        GLog.d(TAG, LogFlag.DL) { "onScan, start person pet group scan triggerType = $triggerType" }
        super.onScan(triggerType, config)
        scan()
        release()
    }

    /**
     * 返回 KEY ，格式为 [person:*,* pet:*,*]
     */
    private fun getKey(personGroupIdSb: String?, petGroupIdSb: String?): String {
        val personGroupIds = personGroupIdSb ?: TextUtil.EMPTY_STRING
        val petGroupIds = petGroupIdSb ?: TextUtil.EMPTY_STRING
        return "person:$personGroupIds pet:$petGroupIds"
    }

    /**
     * 根据 [peronPetInfoList] 列表，需保证该列表的数据都是相同 data 值。创建一个 PersonPetGroupCluster 数据对象
     */
    private fun createPersonPetGroupCluster(peronPetInfoList: List<PersonPetInfo>): PersonPetGroupCluster {
        return PersonPetGroupCluster().apply {
            val info = peronPetInfoList[0]
            data = info.data
            mediaType = info.mediaType
            createDate = System.currentTimeMillis()
            createType = 1 // 1 表示自动创建
            groupName = TextUtil.EMPTY_STRING
            thumbWidth = info.thumbWidth
            thumbHeight = info.thumbHeight
            val personGroupIdSb = StringBuilder()
            val petGroupIdSb = StringBuilder()
            peronPetInfoList.forEach {
                when (it.roleType) {
                    ROLE_PERSON -> personGroupIdSb.append(it.groupID).append(COMMA)
                    ROLE_PET -> petGroupIdSb.append(it.groupID).append(COMMA)
                }
                rectLeft = min(it.left ?: 0, rectLeft ?: Int.MAX_VALUE)
                rectTop = min(it.top ?: 0, rectTop ?: Int.MAX_VALUE)
                rectRight = max(it.right ?: 0, rectRight ?: 0)
                rectBottom = max(it.bottom ?: 0, rectBottom ?: 0)
            }
            personGroupIds = if (personGroupIdSb.isNotEmpty()) {
                personGroupIdSb.deleteCharAt(personGroupIdSb.lastIndex).toString()
            } else {
                TextUtil.EMPTY_STRING
            }
            petGroupIds = if (petGroupIdSb.isNotEmpty()) {
                petGroupIdSb.deleteCharAt(petGroupIdSb.lastIndex).toString()
            } else {
                TextUtil.EMPTY_STRING
            }
            score = PersonPetGroupCoverUtils.calcScore(peronPetInfoList)
            isDefaultCover = false
            isChosen = false
        }
    }

    /**
     * 筛选出 >= 2 个角色，且 >= 10 张图的集合
     */
    private fun atLessTwoRolesAndTenCountFilter(
        groupMap: MutableMap<String, MutableList<PersonPetGroupCluster>>
    ): MutableMap<String, MutableList<PersonPetGroupCluster>> {
        val result = mutableMapOf<String, MutableList<PersonPetGroupCluster>>()
        groupMap.forEach { (groupIdKey, clusterList) ->
            if ((clusterList.size >= GROUP_SIZE) && (isAtLessTwoRoles(clusterList[0]))) {
                result[groupIdKey] = clusterList
            }
        }
        return result
    }

    /**
     * 检查是否 >= 2 个角色。>= 2 个角色时返回 true， 否则返回 false。
     */
    private fun isAtLessTwoRoles(cluster: PersonPetGroupCluster): Boolean {
        val personGroupIdsCount = cluster.personGroupIds?.run { if (isNotEmpty()) split(COMMA).size else 0 } ?: 0
        val petGroupIdsCount = cluster.petGroupIds?.run { if (isNotEmpty()) split(COMMA).size else 0 } ?: 0
        val roleCount = personGroupIdsCount + petGroupIdsCount
        return (roleCount >= COUNT_TWO)
    }

    /**
     * 将[clusterList]的原始数据进行分组。
     * key 为该合照图集的全部角色group_id的格式化字符串。
     * value 为该合照群组中的原始数据列表。
     */
    private fun convertToMap(clusterList: List<PersonPetGroupCluster>): MutableMap<String, MutableList<PersonPetGroupCluster>> {
        val groupMap = mutableMapOf<String, MutableList<PersonPetGroupCluster>>()
        clusterList.forEach { cluster ->
            val key = getKey(cluster.personGroupIds, cluster.petGroupIds)
            groupMap[key]?.add(cluster) ?: let {
                val sameGroupClusterList = mutableListOf<PersonPetGroupCluster>()
                sameGroupClusterList.add(cluster)
                groupMap.put(key, sameGroupClusterList)
            }
        }
        return groupMap
    }

    /**
     * 在 [clusterListInDB] 集合中查找是否存在相同 data 的对象。存在返回 true，否则返回 false。
     */
    private fun isExist(data: String?, clusterListInDB: List<PersonPetGroupCluster>): Boolean {
        clusterListInDB.forEach {
            if ((it.data != null) && (it.data == data)) {
                return true
            }
        }
        return false
    }

    /**
     * 将 [personPetList] 合并聚类相同 [data] 的数据，并转换成 [PersonPetGroupCluster] 的 map 集合
     */
    private fun convertToClusterList(personPetList: List<PersonPetInfo>): List<PersonPetGroupCluster> {
        val clusterList = mutableListOf<PersonPetGroupCluster>()
        var lastData = TextUtil.EMPTY_STRING
        var count = 0
        var sameDataPeronPetInfoList = mutableListOf<PersonPetInfo>() // 临时缓存相同 data 的角色数据，保存了1张图的全部角色数据
        var isGroupIdValid = true // group_id <= 2 时，说明该图存在未被聚类的角色，不可能进行合照聚类，因此判断该合照图无效，不添加到结果集
        personPetList.forEach { personPetInfo ->
            val groupId = personPetInfo.groupID ?: 0
            if (lastData == personPetInfo.data) {
                count++
                if (groupId <= GalleryScanUtils.GROUP_ID_2) {
                    isGroupIdValid = false
                }
                sameDataPeronPetInfoList.add(personPetInfo)
            } else {
                // 结束上一组相同 data 的数据
                if ((count >= COUNT_TWO) && isGroupIdValid) {
                    // 2个角色及以上的图片才可以进行合照聚类，才会被添加到[clusterList]列表中。
                    clusterList.add(createPersonPetGroupCluster(sameDataPeronPetInfoList))
                }
                // 重置下一组的数据的临时变量
                lastData = personPetInfo.data ?: TextUtil.EMPTY_STRING
                count = 1
                isGroupIdValid = true
                if (groupId <= GalleryScanUtils.GROUP_ID_2) {
                    isGroupIdValid = false
                }
                sameDataPeronPetInfoList = mutableListOf()
                sameDataPeronPetInfoList.add(personPetInfo)
            }
        }
        // 结束上一组相同 data 的数据
        if ((count >= COUNT_TWO) && isGroupIdValid) {
            // 2个角色及以上的图片才可以进行合照聚类，才会被添加到[clusterList]列表中。
            clusterList.add(createPersonPetGroupCluster(sameDataPeronPetInfoList))
        }
        return clusterList
    }

    /**
     * 筛选已存在合照图集的数据，将新数据缓存到 [newList] 中，将空合照图集数据缓存到 [deleteList] 中。
     * 返回剩余未处理的数据，后续走自动创建合照群组逻辑。
     * @param groupMap 将被处理的新数据
     * @param groupMapInDB 已保存在数据库的合照图集数据
     * @return 返回 [groupMap] 中，除去已添加到 [newList] 中的集合。
     */
    private fun existGroupFilter(
        groupMap: MutableMap<String, MutableList<PersonPetGroupCluster>>,
        groupMapInDB: MutableMap<String, MutableList<PersonPetGroupCluster>>
    ): MutableMap<String, MutableList<PersonPetGroupCluster>> {
        val resultMap = mutableMapOf<String, MutableList<PersonPetGroupCluster>>()
        groupMap.forEach { (groupIdKey, list) ->
            if (groupMapInDB.containsKey(groupIdKey)) {
                // 数据库中已有合照群组，那就需要把 合照群组的数据更新到 list 中
                val listInDB = groupMapInDB[groupIdKey] ?: mutableListOf()

                /**
                 * 由于角色可以被合并，这里 listInDB 列表可能存在多个不同的合照图集 id，但它们都是相同的角色 group id。
                 * 因此将 listInDB 根据 groupId 进行分组，把新的图片添加到每个合照图集中。
                 */
                val groupIdMapInDB = listInDB.groupBy { cluster -> cluster.groupId }
                groupIdMapInDB.onEachIndexed { index, entry ->
                    // 首个合照图集，可直接引用 list 来添加到 newList 中。而后续的合照图集则需要克隆出相同的 list 来进行添加。
                    val finalList = if (index == 0) list else cloneList(list)
                    val groupListInDB = entry.value
                    // 将数据库中的合照图集的字段，更新到 finalList 对象中。这里取队列第一个对象即可，它们更新的值都是一样的。
                    updateCluster(finalList, groupListInDB.first())

                    // 因为有新数据加入到了合照图集中，需要更新默认封面
                    updateDefaultCover(finalList, groupListInDB)

                    // 添加到 newList 中，后续插入到数据库中。
                    newList.addAll(finalList)
                }

                // 检查是否空合照图集
                if (isEmptyGroup(listInDB)) {
                    addToDeleteList(listInDB.first())
                }
            } else {
                // 数据库中没有该 groupIdKey 中的合照群组，将数据添加到 resultMap
                resultMap[groupIdKey] = list
            }
        }
        return resultMap
    }

    /**
     * 克隆 PersonPetGroupCluster 列表中的对象。
     */
    private fun cloneList(list: MutableList<PersonPetGroupCluster>): MutableList<PersonPetGroupCluster> {
        val result = mutableListOf<PersonPetGroupCluster>()
        list.forEach { cluster -> result.add(cluster.clone()) }
        return result
    }

    /**
     * 将 [cluster] 的合照图集数据，同步更新到 [list] 中
     */
    private fun updateCluster(list: List<PersonPetGroupCluster>, cluster: PersonPetGroupCluster) {
        list.forEach {
            it.createDate = cluster.createDate
            it.createType = cluster.createType
            it.groupName = cluster.groupName
            it.groupId = cluster.groupId
            it.isDisband = cluster.isDisband
        }
    }

    /**
     * 找到最高分的图，将它设置为默认封面
     */
    private fun updateDefaultCover(list: List<PersonPetGroupCluster>, listInDB: List<PersonPetGroupCluster>? = null) {
        var imageMaxScore = 0f
        var videoMaxScore = 0f
        var imageMaxScoreCluster: PersonPetGroupCluster? = null
        var videoMaxScoreCluster: PersonPetGroupCluster? = null
        var maxScoreCluster: PersonPetGroupCluster? = null
        var defaultCoverCluster: PersonPetGroupCluster? = null
        val clusterList = mutableListOf<PersonPetGroupCluster>().apply {
            addAll(list)
            listInDB?.let { addAll(it) }
        }
        clusterList.forEach { cluster ->
            val score = (cluster.score ?: 0f)
            when (cluster.mediaType) {
                MEDIA_TYPE_IMAGE -> { // 找到分数最高的图片
                    if (imageMaxScore < score) {
                        imageMaxScore = score
                        imageMaxScoreCluster = cluster
                    }
                }

                MEDIA_TYPE_VIDEO -> { // 找到分数最高的视频
                    if (videoMaxScore < score) {
                        videoMaxScore = score
                        videoMaxScoreCluster = cluster
                    }
                }
            }
            if (cluster.isDefaultCover == true) { // 找到当前默认的封面
                defaultCoverCluster = cluster
            }
        }
        // 优先使用图片作为封面，如果没有图片，才使用视频作为封面
        videoMaxScoreCluster?.let { maxScoreCluster = it }
        imageMaxScoreCluster?.let { maxScoreCluster = it }
        // 最高分 和 默认封面不是同一张图时，更新它们的 [isDefaultCover]
        if (maxScoreCluster != defaultCoverCluster) {
            defaultCoverCluster?.apply {
                isDefaultCover = false
                updateList.add(this) // 将这条数据添加到 [updateList] 中，后续更新到数据库中。
            }
            maxScoreCluster?.isDefaultCover = true
        }
    }

    /**
     * 检查是否为空合照图集。是空合照图集返回 true，否则返回 false。
     */
    private fun isEmptyGroup(groupInDB: List<PersonPetGroupCluster>): Boolean {
        // 只有 1 条数据，并且data为空，就是空合照图集。
        return (groupInDB.size == 1) && groupInDB.first().data.isNullOrEmpty()
    }

    /**
     * 将空数据的 cluster 添加到 deleteList 队列中
     */
    private fun addToDeleteList(cluster: PersonPetGroupCluster) {
        if (deleteList.isEmpty()) {
            deleteList.add(cluster)
        } else {
            var isExist = false
            run {
                deleteList.forEach {
                    if ((it.personGroupIds == cluster.personGroupIds)
                        && (it.petGroupIds == cluster.petGroupIds)
                    ) {
                        isExist = true
                        return@run
                    }
                }
            }
            if (!isExist) {
                deleteList.add(cluster)
            }
        }
    }

    /**
     * 把 [allPersonPetList] 过滤掉已存在合照群组数据库的数据，返回过滤后的数据集合。
     */
    private fun notExitInDbDataFilter(
        allPersonPetList: List<PersonPetInfo>,
        groupListInDB: List<PersonPetGroupCluster>
    ): List<PersonPetInfo> {
        val personPetList = mutableListOf<PersonPetInfo>()
        allPersonPetList.forEach { info ->
            if (!isExist(info.data, groupListInDB)) {
                personPetList.add(info)
            }
        }
        return personPetList
    }

    /**
     * 扫描已识别的图，把仅包含2个角色，且数量 >= 20张的合照，进行自动合并成合照群组。
     * 已解散的群组，不改变解散状态，将扫描的新合照插入到该合照群组中。
     * 如果已存在的手动创建的合照图集，将扫描到的相同角色的图，自动加到该合照图集中。
     */
    private fun scan() {
        val time = System.currentTimeMillis()
        // 合并关联 scan_face 表 和 pet 表，查询有效的 data,group_id,media_type 数据，查询条件 feature IS NOT NULL,invalid=0,is_recycle=0,is_hide=0，data排序
        val allPersonPetInfoList = GalleryScanPersonPetGroupProviderHelper.getAllValidPersonPetList()
        GLog.d(TAG, LogFlag.DL) { "scan,  allPersonPetInfoList:${allPersonPetInfoList.size}" }

        // 查询 person_pet_group 表，得到数据库中的集合数据，主要是为了得到 merge_group_name,merge_group_id,is_disband
        val groupListInDB = GalleryScanPersonPetGroupProviderHelper.getAllPersonPetGroupList()
        // 获取数据库中最大的 group id
        var maxGroupIdInDB = if (groupListInDB.isNotEmpty()) groupListInDB[0].groupId ?: 0 else 0
        val groupMapInDB = convertToMap(groupListInDB)
        GLog.d(TAG, LogFlag.DL) { "scan,  groupListInDB:${groupListInDB.size}, groupMapInDB:${groupMapInDB.size}" }

        // 从 allPersonPetInfoList 中分离出 person_pet_group 表不存在的 data 数据集。如果 data 已存在表中，说明该图已被扫描过，不需要再处理。
        val personPetInfoList = notExitInDbDataFilter(allPersonPetInfoList, groupListInDB)
        val groupList = convertToClusterList(personPetInfoList)
        var groupMap = convertToMap(groupList)
        GLog.d(TAG, LogFlag.DL) { "scan,  personPetInfoList:${personPetInfoList.size}" }

        // 1 将扫描的到的新数据，与数据库中的相同key的合照群组比对，筛选出所有可以直接添加到已有合照图集的新数据（包含多角色的合照图集）。
        groupMap = existGroupFilter(groupMap, groupMapInDB)

        // 2 处理自动创建合照，筛选出 > 2个角色，且 >= 10 张图的合照集合
        val atLessTwoRolesAndTenCountGroupMap = atLessTwoRolesAndTenCountFilter(groupMap)
        GLog.d(TAG, LogFlag.DL) { "scan,  multi roles and 10+ pic groupMap:${atLessTwoRolesAndTenCountGroupMap.size}" }

        // 2.1 更新默认封面，更新 merge_group_id，添加到 newList 中，后续插入到数据库中。
        atLessTwoRolesAndTenCountGroupMap.forEach { (_, list) ->
            updateDefaultCover(list)
            val mergeGroupId = ++maxGroupIdInDB
            list.forEach { it.groupId = mergeGroupId }
            newList.addAll(list)
        }

        // 3 删除、更新、插入数据。
        GLog.d(TAG, LogFlag.DL) { "scan,  deleteList:${deleteList.size}, updateList:${updateList.size}, newList:${newList.size}" }
        GalleryScanPersonPetGroupProviderHelper.deleteData(deleteList)
        GalleryScanPersonPetGroupProviderHelper.updateData(updateList)
        GalleryScanPersonPetGroupProviderHelper.insertData(newList)
        AlbumsActionTrackHelper.trackAndSendMenuOperationClickForPet(createPersonPetGroupType = "auto")
        GLog.d(TAG, LogFlag.DL) { "scan,  cost:${GLog.getTime(time)}" }
    }

    private fun release() {
        GLog.d(TAG, LogFlag.DL) { "release" }
        deleteList.clear()
        updateList.clear()
        newList.clear()
    }

    companion object {
        private const val TAG = ScanConst.TAG + "PersonPetGroupScanner"
        private const val SCENE_NAME = "PersonPetGroupScanner"
        private const val GROUP_SIZE = 10
        private const val COUNT_TWO = 2
    }
}