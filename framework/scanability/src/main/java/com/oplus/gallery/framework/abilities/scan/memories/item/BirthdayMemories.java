/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BirthdayMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/16
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D      2018/3/16   1.0          build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;


import android.content.Context;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class BirthdayMemories extends Memories {
    private static final String TAG = "BirthdayMemories";
    private static final String STOP_REASON_LABEL_NOT_IN_DATE_RANGE = TAG + "_LabelNotInDateRange";
    private static final String STOP_REASON_HIGH_PRIORITY = TAG + "_HighPriority";
    private static final String STOP_REASON_SCAN_FAILED = TAG + "_ScanFailed";
    private static final String KEY_LABEL_BIRTHDAY = "生日";
    private static final float LABEL_PIC_PERCENT = 0.5f;
    private int mLabelId = -1;

    public BirthdayMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        mLabelId = LabelDictionary.getLabelId(KEY_LABEL_BIRTHDAY);
        if (isHighPriorityMemories()) {
            MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDaysRange(MemoriesScannerHelper.BIRTHDAY_MEMORIES_TRIGGER_DAY_OFFSET);
            boolean hasLabelExist = MemoriesProviderHelper.hasLabelExistInDateRange(dateRange, mLabelId);
            GLog.v(TAG, "prepareMemories hasLabelExist:" + hasLabelExist + ",dateRange:" + dateRange);
            // 记录失败原因
            if (!hasLabelExist) {
                mStopReason = STOP_REASON_LABEL_NOT_IN_DATE_RANGE;
            }
            return hasLabelExist;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        LinkedHashMap<MemoriesScannerHelper.DateRange, Integer> rangeMap = MemoriesScannerHelper.getItemDataRangeSizeByLabelId(mContext,
                (int) (LABEL_PIC_PERCENT * mMemoriesPicMin), mLabelId);
        long curTime = System.currentTimeMillis();
        for (Map.Entry<MemoriesScannerHelper.DateRange, Integer> entry : rangeMap.entrySet()) {
            List<MediaItem> itemList = MemoriesProviderHelper.getItemListFromLocalMedia(mContext, entry.getKey(), mMemoriesPicMin);
            mMediaItemList = MemoriesProviderHelper.getFilteredItemList(itemList, getLabelFilterIdList());
            boolean allowMemoriesCreate = allowMemoriesCreate(mMediaItemList, curTime);
            GLog.d(TAG, "scanMemories, allowMemoriesCreate:" + allowMemoriesCreate
                    + ",mMediaItemList size:" + mMediaItemList.size());
            if (allowMemoriesCreate && (mMediaItemList.size() > mMemoriesPicMin)) {
                int picSize = mMediaItemList.size();
                if (entry.getValue() >= (LABEL_PIC_PERCENT * picSize)) {
                    return createMemories(mMediaItemList);
                } else {
                    mStopSubReason = STOP_REASON_OVER_SIZE;
                }
            } else if (mStopSubReason == null) {
                mStopSubReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            }
            if (isHighPriorityMemories()) {
                // 记录失败原因
                mStopReason = STOP_REASON_HIGH_PRIORITY + mStopSubReason;
                return false;
            }
        }
        // 记录失败原因
        mStopReason = STOP_REASON_SCAN_FAILED + mStopSubReason;
        return false;
    }

    private boolean allowMemoriesCreate(List<MediaItem> itemList, long curTime) {
        mStopSubReason = null;
        MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        if (dateRange == null) {
            GLog.w(TAG, "allowMemoriesCreate, dateRange is null!");
            mStopSubReason = STOP_REASON_DATE_RANGE_NULL;
            return false;
        }
        if (MemoriesScannerHelper.getDaysBetweenTwoDates(dateRange.mEnd, curTime) <= 0) {
            GLog.d(TAG, "allowMemoriesCreate, memories is not before today!");
            mStopSubReason = STOP_REASON_NOT_BEFORE_TODAY;
            return false;
        }
        if (MemoriesProviderHelper.hasMemoriesCreatedInDateRange(dateRange, getMemoriesId())) {
            GLog.d(TAG, "allowMemoriesCreate, memories has created in date range:" + dateRange);
            mStopSubReason = STOP_REASON_CREATED_IN_DATE_RANGE;
            return false;
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return true;
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.EVENT_BIRTHDAY_MEMORIES;
    }

    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_BIRTHDAY_MEMORIES;
    }

    @Override
    protected boolean needShowNotification() {
        return isHighPriorityMemories();
    }

}
