/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoLabelListConfig.kt
 * Description:
 * Version: 1.0
 * Date: 2023/6/19
 * Author: houdonggu@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * houdonggu@Apps.Gallery3D      2023/6/19      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.scan.label

import com.oplus.gallery.business_lib.model.config.allowlist.AbsAllowListParser
import com.oplus.gallery.foundation.util.debug.GLog
import okio.use
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.InputStream

private const val DEFAULT_XML_CONFIG = "default_video_label_lists_config.xml"
private const val REMOTE_XML_CONFIG = "app_gallery_video_label_lists_config"
private const val CURRENT_VERSION_KEY = "local_video_label_version"

/**
 * 视频黑白名单过滤
 */
object VideoLabelListConfig : AbsAllowListParser(REMOTE_XML_CONFIG, DEFAULT_XML_CONFIG, CURRENT_VERSION_KEY) {
    private const val TAG = "VideoLabelListConfig"

    private const val LABEL_LABEL_ELEMENT = "label"
    private const val LABEL_ID_ATTR = "id"
    private const val LABEL_NAME_ATTR = "name"
    private const val LABEL_REJECT_ATTR = "reject"

    val listLabelWhite = mutableListOf<Int>()
    val listLabelBlack = mutableListOf<Int>()

    init {
        loadConfig()
    }

    fun getVersion(): Int {
        return mCurVersion
    }

    override fun loadRemoteConfig(): Boolean {
        return inputStreamFromRemote?.use {
            loadLabelConfigIfNeed(it).apply {
                GLog.d(TAG) { "loadRemoteConfig result:$this" }
            }
        } ?: false
    }

    override fun loadDefaultConfig(): Boolean {
        return inputStreamFromAsset?.use {
            loadLabelConfigIfNeed(it).apply {
                GLog.d(TAG) { "loadDefaultConfig result:$this" }
            }
        } ?: false
    }

    override fun dump() {
        GLog.d(TAG) { "dump, listLabelWhite:$listLabelWhite listLabelBlack:$listLabelBlack" }
    }

    override fun getTag(): String {
        return TAG
    }

    override fun saveConfig(): Boolean {
        return true
    }

    private fun loadLabelConfigIfNeed(inputStream: InputStream?): Boolean {
        if (listLabelWhite.isNotEmpty() && listLabelBlack.isNotEmpty()) {
            return true
        }
        runCatching {
            parseBlock(inputStream)
            return true
        }.onFailure {
            GLog.w(TAG, "checkConfig asset read ", it)
        }
        return false
    }

    private fun parseBlock(inputStream: InputStream?) {
        val time = System.currentTimeMillis()
        val parser = XmlPullParserFactory.newInstance().newPullParser()
        parser.setInput(inputStream, null)
        parser.next()
        var evenType = parser.eventType
        while (evenType != XmlPullParser.END_DOCUMENT) {
            val name = parser.name
            when (evenType) {
                XmlPullParser.END_TAG -> {
                    if (name == LABEL_LABEL_ELEMENT) {
                        val attrId = parser.getAttributeValue(null, LABEL_ID_ATTR).toInt()
                        val attrName = parser.getAttributeValue(null, LABEL_NAME_ATTR)
                        val attrReject = parser.getAttributeValue(null, LABEL_REJECT_ATTR).toInt()
                        if ((attrReject == 0) && !listLabelWhite.contains(attrId)) {
                            listLabelWhite.add(attrId)
                        } else if ((attrReject == 1) && !listLabelBlack.contains(attrId)) {
                            listLabelBlack.add(attrId)
                        }
                    }
                }
            }
            evenType = parser.next()
        }
        GLog.d(
            TAG, "parseBlock listLabelWhite.size:${listLabelWhite.size} listLabelBlack.size:${listLabelBlack.size} " +
                "cost=${(System.currentTimeMillis() - time)}"
        )
    }
}