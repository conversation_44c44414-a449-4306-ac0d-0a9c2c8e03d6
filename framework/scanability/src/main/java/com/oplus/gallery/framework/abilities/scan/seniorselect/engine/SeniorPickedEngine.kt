/*********************************************************************************
 ** Copyright (C), 2019-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SeniorPickedEngine.kt
 ** Description : 图片相关的美学sdk接口封装以及数据组装类
 ** Version     : 1.0
 ** Date        : 2023/3/13
 ** Author      : zhu<PERSON><PERSON>@Apps.Gallery
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON>@Apps.Gallery             2023/3/13      1.0       build
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.engine

import android.content.Context
import com.cv.imageapi.GallerySelection
import com.cv.imageapi.model.CvClassifyLabel
import com.cv.imageapi.model.ImageInfo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.INVALID_SCORE
import com.oplus.gallery.business_lib.seniorpicked.PickedParamManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.scan.label.ImageLabelListConfig
import com.oplus.gallery.framework.abilities.scan.model.BaseScanLabelInfo
import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectDBOperation
import com.oplus.gallery.framework.abilities.scan.utils.SingletonHolder
import kotlin.system.measureTimeMillis

/**
 * 调用contentFilter接口和数据封装方法类
 * setSelectionLabel 需要在makeImageInfo之前（makeImageInfo有获取topLabelId）
 */
class SeniorPickedEngine private constructor(private val context: Context) {

    private val gallerySelection: GallerySelection = GallerySelection()

    /**
     * 设置内容优选的黑名单标签
     * sdk @para selectionLabelInfo 从SDK附件blacklist_*.txt构造
     */
    fun setImageLabelConfig() {
        gallerySelection.setSelectionLabel(ImageLabelListConfig.allLabelMap)
    }

    /**
     * 组装传给contentFilter的ImageInfo
     *
     * @param mediaItems 要扫描的图片
     * @param scoreMap 质量美学分数
     * @return 返回ImageInfo集合
     */
    fun getImageInfo(
        mediaItems: List<MediaItem>,
        scoreMap: Map<String, Float>,
        labelInfoList: ArrayList<BaseScanLabelInfo>
    ): HashMap<String, ImageInfo> {
        val result = HashMap<String, ImageInfo>()
        for (mediaItem in mediaItems) {
            val list = labelInfoList.filter { it.mFilePath == mediaItem.filePath }.map { labelInfo ->
                CvClassifyLabel().apply {
                    mId = labelInfo.mSceneId
                    mScore = labelInfo.mScore
                }
            }
            val qualityScore = scoreMap[mediaItem.filePath] ?: INVALID_SCORE
            if (qualityScore < 0) {
                GLog.w(TAG) { "getImageInfo. qualityScore=$qualityScore" }
            }
            if (list.isNotEmpty()) {
                val multiLabel = list.toTypedArray()
                val seniorTopLabelIdFromSdk = gallerySelection.getTopLabel(multiLabel)
                if (seniorTopLabelIdFromSdk != -1) {
                    result[mediaItem.filePath] = ImageInfo(multiLabel, seniorTopLabelIdFromSdk, qualityScore)
                }
            } else {
                GLog.e(TAG) {
                    "getImageInfo. No valid image info. itemSize=${mediaItems.size}, scoreSize=${scoreMap.size}, labelSize=${labelInfoList.size}"
                }
            }
        }
        return result
    }

    /**
     * 获取优选图集合
     * sdk文档见： https://odocs.myoas.com/file/loqeWO5N5VFoaGAn
     *
     * @param imgInfoMap 图像信息List
     * @param clusters 由CvNearDup.cvNearDup(CvFeatureInfo)返回, 图像相似图搜索结果
     * @param clusterScore 聚类结果每个类别中，图像的相似图优选分数
     * @return 返回优选图集合，key为path, value为质量优选评分
     */
    fun contentFilter(
        imgInfoMap: HashMap<String, ImageInfo>,
        clusters: List<List<String>>,
        clusterScore: List<List<Float>>
    ): Map<String, Float>? {
        val result: Map<String, Float>?
        val consume = measureTimeMillis {
            result = gallerySelection.contentFilter(imgInfoMap, clusters, clusterScore)
        }
        if (result == null) {
            GLog.e(TAG) { "contentFilter. Input illegal. imgSize=${imgInfoMap.size}, clusters=${clusters.size}, clusterScore=${clusterScore.size}" }
        } else {
            GLog.d(TAG) { "contentFilter. inputSize=${imgInfoMap.size}, outputSize=${result.size}, consume=$consume" }
        }
        return result
    }

    /**
     * 以db中的历史数据设置平均质量分（不包括当前批次），有阈值200控制，小于阈值不设置，使用sdk内部有默认的3.2
     *
     * sdk 接口描述：设置优选标签大类平均质量分数
     * sdk @param mapAvgQualityScore 一级标签包含的历史图像
     **/
    fun setTopLabelQualityAvg() {
        val seniorCount = SeniorSelectDBOperation.querySeniorResultCount()
        if (seniorCount < PickedParamManager.TOP_LABEL_SET_THRESHOLD) {
            GLog.d(TAG, "setTopLabelQualityAvg db size<200,count=$seniorCount")
            return
        }
        val mapAvgQualityScore: HashMap<Int, Float> = HashMap()
        val qualityNode = SeniorSelectDBOperation.queryQualityScoreBySeniorTopLabelId()
        for (node in qualityNode) {
            mapAvgQualityScore[node.topLabelId] = node.avg
        }
        return gallerySelection.setTopLabelQualityAvg(mapAvgQualityScore)
    }

    companion object {
        private const val TAG = "SeniorPickedEngine"
        fun getInstance(context: Context): SeniorPickedEngine {
            return SingletonHolder(::SeniorPickedEngine).getInstance(context)
        }
    }
}