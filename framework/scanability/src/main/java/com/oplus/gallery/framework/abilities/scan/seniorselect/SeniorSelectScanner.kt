/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SeniorSelectScanner
 ** Description: 精选扫描任务 负责完成素材的精选，结果存储到SeniorMediaTable
 **
 ** Version: 1.0
 ** Date: 2021/11/18
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  Yeguangjin@Apps.Gallery3D  2021/11/18  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect

import android.content.Context
import android.provider.MediaStore
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.seniorpicked.SeniorMediaInfo
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_RESULT_NOT_SELECTED
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_RESULT_SELECTED
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_SELECT_VERSION
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaDBOperation
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper
import com.oplus.gallery.framework.abilities.scan.label.ImageLabelListConfig
import com.oplus.gallery.framework.abilities.scan.label.VideoLabelListConfig
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.seniorselect.batch.BatchScanData
import com.oplus.gallery.framework.abilities.scan.seniorselect.batch.SeniorSelectBatchChain
import com.oplus.gallery.framework.abilities.scan.seniorselect.engine.MediaSelectEngine
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import kotlin.system.measureTimeMillis

class SeniorSelectScanner(context: Context) : GalleryScan(context) {
    private val selectEngine = MediaSelectEngine(mContext)

    override fun getScanType(): Int {
        return SENIOR_SELECT_SCAN
    }

    override fun onScan(triggerType: Int, config: ScanConfig) {
        GLog.d(TAG, LogFlag.DL, "onScan, start scan seniorSelect triggerType = $triggerType")
        super.onScan(triggerType, config)
        markConditionsOnStartScan()
        mIsFirstScan = GalleryScanUtils.isFirstScan(mContext, SCAN_NAME)
        if (mIsFirstScan) {
            GalleryScanUtils.setFirstScanPref(mContext, SCAN_NAME)
        }
        if (selectEngine.initialize()) {
            afterInitialize()
            scan()
            release()
        }
        track(null, false)
        GLog.d(TAG, LogFlag.DL, "onScan, seniorSelect scan end")
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return false
    }

    private fun afterInitialize() {
        SeniorSelectDBOperation.deleteAbandonSeniorMedia()
    }

    private fun scan() {
        val time = System.currentTimeMillis()
        val batchChain = SeniorSelectBatchChain()
        readScanCountNotInCharging(GalleryScanUtils.SENIOR_SELECT_SCAN_COUNT_24H_KEY)
        while (batchChain.hasNext() && isAllowContinueScan(SENIOR_SELECT_SCAN_COUNT_24H_MAX)) {
            /**
             * 开始批次扫描
             */
            val scanCount = batchScan(batchChain.getNextBatchData())
            addScanCountIfNoCharging(GalleryScanUtils.SENIOR_SELECT_SCAN_COUNT_24H_KEY, scanCount)
            bindSmallCore()
        }
        updateLastScanTime()
        GLog.d(TAG, LogFlag.DL, "scan. cost: ${(System.currentTimeMillis() - time)}")
    }

    /**
     * 批次扫描
     * @param scanData 当前批次数据，包括图片、视频，交叉已扫描、未扫描，共四类
     */
    private fun batchScan(scanData: BatchScanData?): Int {
        scanData?.apply {
            var count = 0
            if (videoNotScanned.isNotEmpty()) {
                mScanVideoCount += videoNotScanned.size
                count += videoNotScanned.size
                scanVideo(videoNotScanned)
            }

            if (imageNotScanned.isNotEmpty()) {
                mScanImageCount += imageNotScanned.size
                count += imageNotScanned.size
                scanImage(imageNotScanned, imageScanned)
            }

            GLog.d(TAG, LogFlag.DL, "batchScan. now count: $mScanImageCount, $mScanVideoCount")
            return count
        }

        GLog.e(TAG, LogFlag.DL, "batchScan. scanData is null.")
        return 0
    }

    /**
     * 图片扫描
     * @param notScannedInfoList 未扫描的图片列表
     * @param scannedInfoList 已扫描的图片列表
     *
     */
    private fun scanImage(notScannedInfoList: List<BaseImageInfo>, scannedInfoList: List<BaseImageInfo>) {
        GLog.d(TAG, LogFlag.DL) { "scanImage. start. notScannedSize=${notScannedInfoList.size}, scannedSize=${scannedInfoList.size}" }
        val notScannedItemList = GalleryScanProviderHelper.convertImageToMediaItem(notScannedInfoList)
        val scannedItemList = SeniorSelectDBOperation.queryPickedItemByImageInfo(scannedInfoList)

        val labelFilteredItemList = filterImageLabel(notScannedItemList + scannedItemList)
        val labelFilteredItemMap = labelFilteredItemList.associateBy { it.filePath }

        // 过滤出所有需要更新或插入到优选表的项，这些项不包含黑名单标签
        val seniorInfoList: List<SeniorMediaInfo>
        val oldPickedPathList = SeniorSelectDBOperation.querySeniorResult(scannedItemList.map { it.filePath })
        val filterTime = measureTimeMillis {
            seniorInfoList = selectEngine.scanImageSeniorInfo(labelFilteredItemList)
                .filter {
                    // 已扫描的数据，如果优选结果有变化才后面db操作
                    !oldPickedPathList.contains(it.filePath) ||
                        ((it.seniorResult == SENIOR_RESULT_SELECTED) xor oldPickedPathList.contains(it.filePath))
                }
        }

        // 未扫描的黑名单图片填充非精选默认值来标记为已扫描
        val blackLabelList = notScannedInfoList.filter { !labelFilteredItemMap.contains(it.mFilePath) }
        val blackSeniorInfoList = blackLabelList.map {
            SeniorMediaInfo(
                it.mFilePath,
                SENIOR_SELECT_VERSION,
                seniorResult = SENIOR_RESULT_NOT_SELECTED,
                seniorResultVersion = SeniorSelectDBOperation.imageSeniorResultVersion
            )
        }
        GLog.d(TAG, LogFlag.DL) {
            "scanImage. end. notScannedSize=${notScannedItemList.size}, scannedSize=${scannedItemList.size}, " +
                "seniorSize=${seniorInfoList.size}, blackSeniorSize=${blackSeniorInfoList.size}, " +
                "selectFilterTime=$filterTime"
        }
        (seniorInfoList + blackSeniorInfoList).takeIf { it.isNotEmpty() }?.let {
            insertOrUpdateTarget(it)
        } ?: let {
            GLog.d(TAG, LogFlag.DL) { "scanImage. Nothing insert or update. " }
        }
    }

    /**
     * 图片黑名单过滤 (之前过滤放在FilterCondition中，其中基本过滤条件在getPreFilterCondition已过滤过，所以这里只处理黑名单)
     * */
    private fun filterImageLabel(mediaItems: List<MediaItem>): List<MediaItem> {
        val blackLabelList = ImageLabelListConfig.blackLabelList
        if (blackLabelList.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "filterImage realBlackLabelInfoList null" }
            return mediaItems
        }
        val dataList = mediaItems.map { it.filePath }.toSet()
        val blackLabelDataList = GalleryScanProviderHelper.filterByLabels(dataList, blackLabelList) ?: emptySet()
        return mediaItems.filter {
            blackLabelDataList.contains(it.filePath).not() && (it.mediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE)
        }
    }

    /**
     * 视频扫描，仅处理已经过标签扫描但未进行精选扫描的视频
     *
     * @param notScannedInfoList  还未进行精选扫描的视频信息
     */
    private fun scanVideo(notScannedInfoList: List<BaseImageInfo>) {
        GLog.d(TAG, LogFlag.DL) { "scanVideo. start. notScannedSize=${notScannedInfoList.size}" }
        val notScannedItemList = GalleryScanProviderHelper.convertImageToMediaItem(notScannedInfoList)
        if (notScannedItemList.isEmpty()) {
            return
        }
        val labeledVideoList = SeniorSelectDBOperation.getVideoInScanLabel(notScannedItemList)
        if (labeledVideoList.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "scanVideo. No labeled video. Skip." }
            return
        }

        val seniorInfoList: List<SeniorMediaInfo>
        val labelFilteredVideoList = filterVideoLabel(labeledVideoList, VideoLabelListConfig.listLabelWhite, VideoLabelListConfig.listLabelBlack)
        val filterTime = measureTimeMillis {
            seniorInfoList = selectEngine.scanVideoSeniorInfo(labelFilteredVideoList)
        }

        // 经标签过滤掉的视频填充非精选默认值来标记为已扫描
        val labelFilteredOutVideoList = labeledVideoList.filterNot { labelFilteredVideoList.contains(it) }
        val labelFilteredOutInfoList = labelFilteredOutVideoList.map {
            SeniorMediaInfo(
                it.filePath,
                SENIOR_SELECT_VERSION,
                seniorResult = SENIOR_RESULT_NOT_SELECTED,
                seniorResultVersion = SeniorSelectDBOperation.videoSeniorResultVersion
            )
        }
        GLog.d(TAG, LogFlag.DL) {
            "scanVideo. end. notScannedSize=${notScannedItemList.size}, seniorSize=${seniorInfoList.size}, " +
                "filteredOutSize=${labelFilteredOutInfoList.size}, selectFilterTime=$filterTime"
        }
        (seniorInfoList + labelFilteredOutInfoList).takeIf { it.isNotEmpty() }?.let {
            insertOrUpdateTarget(it)
        } ?: let {
            GLog.d(TAG, LogFlag.DL) { "scanVideo. Nothing insert or update. " }
        }
    }

    private fun filterVideoLabel(mediaItems: List<MediaItem>, listLabelWhite: List<Int>, listLabelBlack: List<Int>): List<MediaItem> {
        if (listLabelWhite.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "filterVideo listLabelWhite is null")
            return emptyList()
        }
        val dataList = mutableSetOf<String>()
        for (mediaItem in mediaItems) {
            dataList.add(mediaItem.filePath)
        }
        val labelWhiteDataList = GalleryScanProviderHelper.filterByLabels(dataList, listLabelWhite) ?: emptySet()
        val labelBlackDataList = GalleryScanProviderHelper.filterByLabels(dataList, listLabelBlack) ?: emptySet()
        return mediaItems.filter {
            labelWhiteDataList.contains(it.filePath) &&
                !labelBlackDataList.contains(it.filePath) &&
                (it.mediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO)
        }
    }

    /**
     * 扫描最后插入senior_media表数据
     * @param targetInfoList 要插入的数据
     */
    private fun insertOrUpdateTarget(targetInfoList: List<SeniorMediaInfo>) {
        val targetPathList = targetInfoList.map { it.filePath }
        val existPathList = SeniorMediaDBOperation.querySeniorMediaData(targetPathList)
        SeniorSelectDBOperation.deleteSeniorMediaItem(targetPathList.filter { existPathList.contains(it) })
        SeniorSelectDBOperation.insertSeniorMediaItem(targetInfoList)
    }

    private fun release() {
        selectEngine.release()
    }

    override fun onMonitorRecord(event: MonitorEvent, reachThreshold: Boolean) {
        super.onMonitorRecord(event, reachThreshold)
        markConditionsOnStartScan()
        GLog.d(TAG, LogFlag.DL, "onMonitorRecord, event = $event, reachThreshold = $reachThreshold")
        track(event, true)
    }

    private fun track(event: MonitorEvent?, isMonitor: Boolean) {
        val reasonType = getReasonType(false, mScanCountNotInCharging, SENIOR_SELECT_SCAN_COUNT_24H_MAX)
        val endNativeHeapSize = getEndNativeHeapSize(event)
        val additionalMap = HashMap<String, String>()
        additionalMap[IS_MONITOR] = isMonitor.toString()
        val scene = SCENE_NAME + (selectEngine.stopReason ?: TextUtil.EMPTY_STRING)
        val scanTrackInfo = generateScanTrackItem(scene, endNativeHeapSize, reasonType, additionalMap)
        GLog.d(TAG, LogFlag.DL, "track, recordItem = $scanTrackInfo")
        ScanTrackHelper.trackScanResult(scanTrackInfo)
    }

    override fun getSceneName(): String {
        return SCENE_NAME
    }

    companion object {
        private const val TAG = ScanConst.TAG + "SeniorSelectScanner"
        private const val SCENE_NAME = "SeniorSelectScanner"
        private const val SENIOR_SELECT_SCAN_COUNT_24H_MAX = 5 * 1000 //5k
        private const val SCAN_NAME = "SeniorSelect"
    }
}