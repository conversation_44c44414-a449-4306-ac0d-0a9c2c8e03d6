/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DayLastYearMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/03/16
 ** Author: ** Date:caowei@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** caowei@Apps.Gallery3D            2018/03/16     1.0       build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;
import android.database.Cursor;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.util.ArrayList;

public class DayLastYearMemories extends Memories {
    private static final String TAG = "DayLastYearMemories";
    private static final String STOP_REASON_HAS_CREATED = TAG + "_HasCreated";
    private static final String STOP_REASON_SCAN_SIZE_NOT_ENOUGH = TAG + "_ScanSizeNotEnough";
    private static final int DAY_LAST_YEAR_OFFSET = -1;

    public DayLastYearMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        if (hasMemoriesCreated()) {
            GLog.d(TAG, "prepareMemories, DayLastYearMemories has created today!");
            // 记录失败原因
            mStopReason = STOP_REASON_HAS_CREATED;
            return false;
        }
        return true;
    }

    private boolean hasMemoriesCreated() {
        Cursor cursor = null;
        try {
            //Uri queryUri = MemoriesStore.Set.getContentUri();
            MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDaysRange(0);
            String whereClause = GalleryStore.MemoriesSet.TYPE + " = " + getMemoriesId()
                    + " AND " + GalleryStore.MemoriesSet.TAKEN + " BETWEEN " + dateRange.mStart
                    + " AND " + dateRange.mEnd;
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setProjection(new String[]{GalleryStore.MemoriesSet.TYPE, GalleryStore.MemoriesSet.TAKEN})
                    .setWhere(whereClause)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return false;
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDayOffsetYearRange(DAY_LAST_YEAR_OFFSET);
        ArrayList<MediaItem> itemList = MemoriesProviderHelper.getItemListFromLocalMedia(mContext, dateRange, mMemoriesPicMin);
        mMediaItemList = MemoriesProviderHelper.getFilteredItemList(itemList, getLabelFilterIdList());
        GLog.d(TAG, "scanMemories, mMediaItemList size:" + mMediaItemList.size());
        if (mMediaItemList.size() <= mMemoriesPicMin) {
            // 记录失败原因
            mStopReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            return false;
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return createMemories(mMediaItemList);
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.EVENT_DAY_LAST_YEAR_MEMORIES;
    }

    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_DAY_LAST_YEAR_MEMORIES;
    }

    @Override
    protected boolean needShowNotification() {
        return true;
    }
}
