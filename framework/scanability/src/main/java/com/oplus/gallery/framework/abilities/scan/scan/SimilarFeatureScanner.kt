/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SimilarFeatureScanner.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/11/08
 ** Author      : wenshuguang
 ** TAG         : OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>        <date>         <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** wenshuguang     2021/11/08      1.0            OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.scan

import android.content.Context
import com.cv.imageapi.CvNearDup
import com.cv.imageapi.model.CvFeatureInfo
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.SingletonHolder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.maskPath
import com.oplus.gallery.framework.abilities.download.template.ModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.SimilarFeatureModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SeniorSelectModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SimilarFeatureModelConfig

class SimilarFeatureScanner private constructor(context: Context) :
    AbsScanner<CvNearDup>(context) {
    override val tag: String = "SimilarFeatureScanner"

    override val modelConfig: SeniorSelectModelConfig = SimilarFeatureModelConfig(context)

    override val modelLoadingTemplate: ModelLoadingTemplate = SimilarFeatureModelLoadingTemplate(context, modelConfig)

    fun group(mediaItems: List<MediaItem>): List<List<String>>? {
        val similarFeature = target
        if (similarFeature == null) {
            GLog.e(tag, "group, SimilarFeature is not ready")
            return null
        }
        GLog.d(tag, "group, start get similar feature")
        val featureMap = getFeatureMap(mediaItems)
        val tagList = mutableListOf<String>()
        val featureList = mutableListOf<FloatArray>()
        for ((data, feature) in featureMap) {
            tagList.add(data)
            featureList.add(feature)
        }
        GLog.d(tag, "group, end get similar feature")
        val cvFeatureInfo = CvFeatureInfo(tagList, featureList, FEATURE_LENGTH, 1)
        val groupResult = mutableListOf<List<String>>()
        similarFeature.cvNearDup(cvFeatureInfo)?.let { groupResult.addAll(it) }

        // 没有特征值的项单独成组
        mediaItems.forEach {
            if (!featureMap.containsKey(it.filePath)) {
                groupResult.add(listOf(it.filePath))
            }
        }
        return groupResult
    }

    private fun getFeatureMap(mediaItems: List<MediaItem>): Map<String, FloatArray> {
        val featureMap = GalleryScanProviderHelper.getImageFeatures(
            mediaItems.map { it.filePath },
            version
        ) ?: mutableMapOf()
        val noFeatureItems = mediaItems.filter { !featureMap.containsKey(it.filePath) }
        featureMap.putAll(scanMedia(noFeatureItems))
        return featureMap
    }

    private fun scanMedia(mediaItems: List<MediaItem>): Map<String, FloatArray> {
        if (mediaItems.isEmpty()) {
            return emptyMap()
        }
        GLog.d(tag, "scanMedia, scan similar_feature features, size: ${mediaItems.size}")
        val newFeatureMap = hashMapOf<String, FloatArray>()
        for (subItems in getSubItems(mediaItems)) {
            newFeatureMap.putAll(subScanMedia(subItems))
        }
        return newFeatureMap
    }

    private fun subScanMedia(mediaItems: List<MediaItem>): Map<String, FloatArray> {
        val similarFeature = target ?: return emptyMap()
        val newFeatureMap = hashMapOf<String, FloatArray>()
        for (item in mediaItems) {
            if (item is LocalImage) {
                getThumbnail(item)?.let {
                    similarFeature.cvFeatureExtractor(it)?.apply {
                        newFeatureMap[item.filePath] = this
                    } ?: let {
                        GLog.e(tag, "subScanMedia cvFeatureExtractor, return null")
                    }
                } ?: let {
                    GLog.e(tag, "subScanMedia thumbnail null, filePath=${item.filePath.maskPath()}")
                }
            }
        }
        GalleryScanProviderHelper.insertFeature(newFeatureMap, version)
        return newFeatureMap
    }

    override fun createTarget(modelPath: String): CvNearDup {
        return CvNearDup(modelPath)
    }

    override fun releaseTarget() {
        target?.release()
        target = null
    }

    override fun modelUpdate(oldModelVersion: Int, newModelVersion: Int) {
        super.modelUpdate(oldModelVersion, newModelVersion)
        if (oldModelVersion > 0) {
            GalleryScanProviderHelper.deleteSimilarFeatures()
        }
    }

    override fun afterInit() {
        super.afterInit()
        GalleryScanProviderHelper.deleteAbandonSimilarFeature()
    }

    companion object {
        private const val FEATURE_LENGTH = 256
        fun getInstance(context: Context): SimilarFeatureScanner {
            return SingletonHolder(::SimilarFeatureScanner).getInstance(context)
        }
    }
}