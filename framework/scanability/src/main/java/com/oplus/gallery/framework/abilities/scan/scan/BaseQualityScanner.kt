/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BaseQualityScanner.kt
 ** Description : 质量分数扫描基类
 ** Version     : 1.0
 ** Date        : 2021/11/08
 ** Author      : wenshuguang
 ** TAG         : OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>        <date>         <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** wenshuguang     2021/11/08      1.0            OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.scan

import android.content.Context
import com.cv.imageapi.CvImageQualityAssessment
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.maskPath
import java.util.Random

/**
 *  质量分数扫描基类
 */
abstract class BaseQualityScanner(protected val context: Context) :
    AbsScanner<CvImageQualityAssessment>(context) {
    private val random = Random()

    /**
     *  根据传入的图片计算质量分数
     *  @param mediaItems 图片
     *  @return map 的key为KEY_HIGH_SCORE为合法数据，外部使用继续做扫描，key为 KEY_SKIP_SCORE 为非法数据，不写入db
     */
    fun rating(mediaItems: List<MediaItem>): Map<String, Float> {
        if (target == null) {
            GLog.e(tag, "rating. QualityScanner has not init.")
            return emptyMap()
        }
        val startTime = System.currentTimeMillis()
        val scoredMap = getQualityScoreMap(mediaItems.map { it.filePath }).toMutableMap()
        val unScoredItems = mediaItems.filter { !scoredMap.containsKey(it.filePath) }
        if (unScoredItems.isNotEmpty()) {
            scoredMap.putAll(scanMedia(unScoredItems))
        }

        GLog.d(tag) { "rating. unScoredSize=${unScoredItems.size}, finalScoredSize=${scoredMap.size}, consume=${GLog.getTime(startTime)}" }
        return scoredMap.differentiatedSameScore()
    }

    /**
     * 对集合中分数相同的项进行差异化处理，具体场景：
     *  当两个Item的评分完全一样时，即这是两张一样的图，获取最高分的sql不好判断选用哪一张
     *  默认是按_data字段的字符串大小选Item，但是外面有重命名功能，二者一起就会出现改名后，sql选的结果变更的情况
     *  该sql已经很长了，若再往里面加，复杂度大幅上升
     *  特拟简单方案如下：
     *  若N张图几乎完全一样，则提高其他N-1张图的评分
     */
    private fun Map<String, Float>.differentiatedSameScore(): Map<String, Float> {
        val newScoredMap = mutableMapOf<String, Float>()
        val scoreSet = mutableSetOf<Float>()
        forEach { (path, score) ->
            var newScore = score
            while (scoreSet.contains(newScore)) {
                newScore += random.nextInt(RANDOM) * SCORE_OFFSET
            }
            scoreSet.add(newScore)
            newScoredMap[path] = newScore
            if (newScore != score) {
                GLog.d(tag) { "differentiatedSameScore. $path has same score. $score->$newScore" }
            }
        }
        return newScoredMap
    }

    private fun scanMedia(mediaItems: List<MediaItem>): Map<String, Float> {
        if (mediaItems.isEmpty()) {
            return emptyMap()
        }
        GLog.d(tag, "scanMedia. scan quality, size: ${mediaItems.size}")
        val newFeatureMap = hashMapOf<String, Float>()
        for (subItems in getSubItems(mediaItems)) {
            newFeatureMap.putAll(subScanMedia(subItems))
        }
        return newFeatureMap
    }

    private fun subScanMedia(mediaItems: List<MediaItem>): Map<String, Float> {
        val imageQualityAssessment = target ?: return emptyMap()
        val newFeatureMap = hashMapOf<String, Float>()
        for (item in mediaItems) {
            getThumbnail(item)?.let {
                newFeatureMap[item.filePath] = imageQualityAssessment.cvGetScore(it)
            } ?: let {
                GLog.e(tag) { "subScanMedia. Can't get thumbnail. filePath=${item.filePath.maskPath()}" }
            }
        }
        insertScore(newFeatureMap)
        return newFeatureMap
    }

    override fun createTarget(modelPath: String): CvImageQualityAssessment {
        return CvImageQualityAssessment(modelPath)
    }

    override fun releaseTarget() {
        GLog.d(tag) { "releaseTarget." }
        target?.release()
        target = null
    }

    /**
     *  质量分数插入到db
     *  @param newFeatureMap 质量分数map
     */
    abstract fun insertScore(newFeatureMap: Map<String, Float>)

    /**
     *  获取质量分数
     *  @param dataList 图片path列表
     */
    abstract fun getQualityScoreMap(dataList: List<String>): Map<String, Float>

    companion object {
        private const val SCORE_OFFSET = 0.000001f
        private const val RANDOM = 100
    }
}