/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryScanDataManager.java
 ** Description:
 **     封装了一些扫scan db helper的逻辑。
 **     但是没封装全，有些地方还是直接使用scan db helper。
 **
 ** Version: 1.0
 ** Date: 2016-11-21
 ** Author: xiuhua.ke
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xiuhua.ke                       2016-11-21   1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.model;

import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PET_CLASSIFY;

import android.content.Context;
import android.os.Handler;
import android.util.Pair;

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.business_lib.bean.BaseImageInfo;
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper;
import com.oplus.gallery.business_lib.supertext.OcrPagesDBHelper;
import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GalleryScanDataManager {
    private static final String TAG = "GalleryScanDataManager";
    public static final Object mImageSyncLock = new Object();
    public static final int FACE_TASK_DELAY_MILLIS = TimeUtils.TIME_1_SEC_IN_MS;
    public static final Object mGroupSyncLock = new Object();
    public static final Object mLabelSyncLock = new Object();
    private static GalleryScanDataManager sInstance;
    private boolean mIsSdCardStateChanged = false;

    public synchronized static GalleryScanDataManager getInstance() {
        if (sInstance == null) {
            sInstance = new GalleryScanDataManager();
        }
        return sInstance;
    }

    private GalleryScanDataManager() {
        GroupHelper.setIsSupportPetClassify(ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY));
    }

    public void setSDCardStateChanged(boolean isSdCardStateChanged) {
        mIsSdCardStateChanged = isSdCardStateChanged;
    }

    /**
     * delete record by file path
     *
     * @param fileList
     * @return void
     */
    public void deleteMediaFile(final ArrayList<String> fileList) {
        GalleryScanPersonPetProviderHelper.deleteMediaFile(fileList);
    }

    public void deleteAllRecycledFile() {
        GalleryScanPersonPetProviderHelper.deleteAllRecycledFile();
    }

    /**
     * 获取各种表需要更新的数据库更新请求
     *
     * @param imageList
     * @return
     */
    public List<UpdateReq> getScanUpdateReqList(final List<BaseImageInfo> imageList) {
        List<UpdateReq> updateFaceReqList = GalleryScanPersonPetProviderHelper.getFaceUpdateReqList(imageList);
        List<UpdateReq> updateLabelReqList = GalleryScanProviderHelper.getLabelsUpdateReqList(imageList);
        List<UpdateReq> updateOcrReqList = OcrPagesDBHelper.getOcrPagesUpdateReqList(imageList);
        List<UpdateReq> updateSeniorMediaReqList = GalleryScanProviderHelper.getSeniorMediaUpdateReqList(imageList);
        List<UpdateReq> updateReqList = new ArrayList<>();
        updateReqList.addAll(updateFaceReqList);
        updateReqList.addAll(updateLabelReqList);
        updateReqList.addAll(updateOcrReqList);
        updateReqList.addAll(updateSeniorMediaReqList);
        return updateReqList;
    }

    /**
     * set best cover
     *
     * @param context      上下文环境
     * @param groupId      设置封面的图集的groupId
     * @param newCoverId     设置封面后，id可能变化，personId新的图集的groupId
     * @param albumSetType 图集的类型，用于访问不同的数据库表,比如移除人物需要访问相关库，移除宠物图集需要访问宠物相关表
     * @return boolean 设置封面是否成功
     */
    public boolean setPersonCover(final Context context, final long groupId, final long newCoverId, final int albumSetType) {
        GLog.d(TAG, "setBestCover, personId: " + newCoverId);
        CvFaceInfo preBestFaceInfo = GalleryScanPersonPetProviderHelper.getPreBestFace(albumSetType, groupId);
        //ignore it if set the same face
        if ((preBestFaceInfo != null) && (preBestFaceInfo.getId() == newCoverId)) {
            return true;
        }
        GalleryScanPersonPetProviderHelper.setIsManualState(context, groupId, true, albumSetType);
        boolean result = true;
        if (preBestFaceInfo != null) {
            result = GalleryScanPersonPetProviderHelper.setCoverBestState(albumSetType, preBestFaceInfo.getId(), false);
        }
        result &= GalleryScanPersonPetProviderHelper.setCoverBestState(albumSetType, newCoverId, true);
        // setIsManualState、setCoverBestState 合并一起发通知
        UriNotifier.notifyTableChange(GroupHelper.getTableType(albumSetType), IDao.DaoType.GALLERY);
        return result;
    }

    public boolean setDefaultCover(final Context context, final long groupId, final long personId) {
        GLog.d(TAG, "setDefaultCover, personId: " + personId);
        CvFaceInfo preDefaultCoverInfo = GalleryScanPersonPetProviderHelper.getPreDefaultCover(context, groupId);
        if ((preDefaultCoverInfo != null) && (preDefaultCoverInfo.getId() == personId)) {
            return true;
        }
        boolean result = true;
        if (preDefaultCoverInfo != null) {
            result = GalleryScanPersonPetProviderHelper.setDefaultCoverState(context, preDefaultCoverInfo.getId(), false);
        }
        result &= GalleryScanPersonPetProviderHelper.setDefaultCoverState(context, personId, true);
        // 两次 setDefaultCoverState 合为一次通知
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
        return result;
    }

    /**
     * free face from current group <br>
     * this call maybe cost long time to return, so do it in WorkThread
     *
     * @param galleryIds   相册数据库的ids
     * @param origGroupId  详情页需要移除的图片对应的图集groupId
     * @param albumSetType 图集的类型，用于访问不同的数据库表,比如移除人物需要访问相关库，移除宠物图集需要访问宠物相关表
     */
    public boolean freeFaceFromGroup(final List<Long> galleryIds, long origGroupId, int albumSetType) {
        if ((galleryIds == null) || galleryIds.isEmpty()) {
            GLog.w(TAG, "freeFaceFromGroup, mediaIds is empty!");
            return false;
        }

        synchronized (mGroupSyncLock) {
            int success = GalleryScanPersonPetProviderHelper.freeFaceFromGroup(galleryIds, origGroupId, albumSetType);
            return success > 0;
        }
    }

    /**
     * unite groups if isShow == true and then changed their ShowEnable or not modify <br>
     * this call maybe cost long time to return, so do it in WorkThread
     *
     * @param majorGroupId 合并时需要更新的主groupId
     * @param groupList    合并时选中的图集groupId list
     * @param isShow       是否显示在对应图集列表中
     * @param albumSetType 图集的类型，用于访问不同的数据库表,比如移除人物需要访问相关库，移除宠物图集需要访问宠物相关表
     * @return boolean 是否合并成功
     */
    public boolean uniteGroups(final long majorGroupId,
                               final List<Long> groupList, final boolean isShow, final int albumSetType) {
        if ((groupList == null) || groupList.isEmpty()) {
            GLog.w(TAG, "uniteGroups, groupList is empty!");
            return false;
        }

        synchronized (mGroupSyncLock) {
            // 这里需要一起更新合照表的 group id，将被合并的 group id 更新为主角色的 group id
            GalleryScanPersonPetProviderHelper.updatePersonPetGroupIdWhenMergeGroup(majorGroupId, groupList, albumSetType);
            return GalleryScanPersonPetProviderHelper.uniteGroups(majorGroupId, groupList, isShow, albumSetType);
        }
    }

    /**
     * rename group <br>
     *
     * @param context      上下文环境
     * @param groupId      需要重命名的图集的groupId
     * @param name         需要重命名的目标名称
     * @param albumSetType 图集的类型，用于访问不同的数据库表,比如移除人物需要访问相关库，移除宠物图集需要访问宠物相关表
     * @return long 重命名成功后的groupId
     */
    public Pair<Long, Integer> renameGroup(final Context context, final long groupId, final String name, final int albumSetType) {
        return GalleryScanPersonPetProviderHelper.renameGroup(context, groupId, name, albumSetType);
    }

    /**
     * 创建合照
     *
     * @param context      上下文环境
     * @param groupIdsInfo 需要创建的合照对应的groupId 集合
     * @return Pair<Long, Integer> long 创建合照结果码, 创建合照的结果：比如
     * 1.创建的合照已经存在，返回已经存在合照
     * 2.创建的合照不存在，创建新的合照，创建成功
     * 3.创建的合照不存在，即没有找到合照照片，需要创建空的合照集
     * 3.创建的合照失败，比如数据库操作失败
     * 这些状态码需要返回给上层作不同的ui展示
     */
    public Pair<Integer, Integer> createPersonPetGroup(final Context context, final Map<Integer, ArrayList<Path>> groupIdsInfo) {
        return GalleryScanPersonPetProviderHelper.createPersonPetGroup(context, groupIdsInfo);
    }

    /**
     * 设置人宠与我的关系
     *
     * @param groupId      当前正在操作的groupId
     * @param relationName 关系名称
     * @param relationType 关系类型，是自定义关系，还是默认关系
     * @param albumSetType 图集的类型，用于访问不同的数据库表,比如设置与我的关系，需要访问人物表或宠物表
     * @return boolean 设置人宠与我的关系是否成功
     */
    public boolean setPersonPetRelationshipWithMe(final Long groupId, final String relationName, final int relationType, int albumSetType) {
        return GalleryScanPersonPetProviderHelper.setPersonPetRelationshipWithMe(groupId, relationName, relationType, albumSetType);
    }

    /**
     * 解散合照
     *
     * @param context      上下文环境
     * @param groupIdList  需要解散的合照对应的merge group Id
     * @param albumSetType 图集的类型，用于访问不同的数据库表,比如移除人物需要访问相关库，移除宠物图集需要访问宠物相关表
     * @return int 解散合照是否成功
     */
    public int disbandPersonPetGroups(final Context context, final List<Long> groupIdList, final int albumSetType) {
        return GalleryScanPersonPetProviderHelper.disbandPersonPetGroups(context, groupIdList, albumSetType);
    }

    /**
     * get group name
     *
     * @param groupId
     * @return group name
     */
    public String getGroupName(final long groupId, final int albumSetType) {
        return GalleryScanPersonPetProviderHelper.getGroupName(groupId, albumSetType);
    }

    /**
     * set group show enable <br>
     * this call maybe cost long time to return, so do it in WorkThread
     *
     * @param groupIdList  选中的需要合并的groupId list
     * @param enable       是否显示在对应的图集列表中
     * @param albumSetType 图集的类型，用于访问不同的数据库表,比如移除人物需要访问相关库，移除宠物图集需要访问宠物相关表
     * @return void
     */
    public void setGroupShowEnable(final List<Long> groupIdList, final boolean enable, final int albumSetType) {
        if ((groupIdList == null) || groupIdList.isEmpty()) {
            GLog.w(TAG, "setGroupShowEnable, groupIdList is empty!");
            return;
        }

        synchronized (mGroupSyncLock) {
            GalleryScanPersonPetProviderHelper.setGroupShowEnable(groupIdList, enable, true, albumSetType);
        }
    }

    /**
     * remove from label <br>
     * this call maybe cost long time to return, so do it in WorkThread
     *
     * @param mediaItems 相册数据表local_media 数据列表
     * @return void
     */
    public boolean removeFromLabel(final List<MediaItem> mediaItems, int labelId) {
        if ((mediaItems == null) || mediaItems.isEmpty()) {
            GLog.w(TAG, "removeFromLabel, mediaIds is empty!");
            return false;
        }

        synchronized (mLabelSyncLock) {
            int success = GalleryScanProviderHelper.removeFromLabel(mediaItems, labelId);
            return success > 0;
        }
    }

    /**
     * start Sync Task <br>
     * do delay one time
     *
     * @param context
     * @return void
     */
    public void startSyncTask(final Context context) {
        new Handler(context.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                if (GalleryScanDataManager.getInstance().mIsSdCardStateChanged) {
                    GalleryScanDataManager.getInstance().mIsSdCardStateChanged = false;
                    MemoriesProviderHelper.startMetaSync();
                }
            }
        }, FACE_TASK_DELAY_MILLIS);
    }

    /**
     * List to Map
     *
     * @param list
     * @return HashMap<Long, ImageInfo>
     */
    public static <T extends BaseImageInfo> HashMap<String, T> translateListToMap(ArrayList<T> list) {
        HashMap<String, T> map = new HashMap<>();
        if ((list != null) && (list.size() > 0)) {
            for (int i = 0; i < list.size(); i++) {
                T info = list.get(i);
                map.put(info.mFilePath, info);
            }
        }
        return map;
    }

    /**
     * 将list转换为map，用小写的filePath做key
     *
     * @param list 列表
     * @return HashMap<String, T> 其中String会转为小写
     */
    public static <T extends BaseImageInfo> HashMap<String, T> translateListToPathMap(List<T> list) {
        HashMap<String, T> map = new HashMap<>();
        if ((list == null) || list.isEmpty()) {
            return map;
        }
        list.forEach(item -> map.put(item.mFilePath.toLowerCase(), item));
        return map;
    }
}
