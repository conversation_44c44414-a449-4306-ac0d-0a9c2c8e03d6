/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GalleryScanProviderHelper.java
 * Description:
 * Version: 1.0
 * Date: 2020/8/20
 * Author: Wangrunxin@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Wangrunxin@Apps.Gallery3D      2020/8/20      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.scan.model;

import static com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.LAST_FOREGROUND_SCAN_OVER_TIME_KEY;
import static com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo.EXPRESSION_DETAIL_DEFAULT;
import static com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.SCAN_LABEL;
import static com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.SCAN_LABEL_BACKUP;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.DATA;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.GROUP_VISIBLE_INIT;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.HAS_MANUAL_STATE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.IS_MANUAL;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns.INVALID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns.IS_RECYCLED;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns.IS_SYNC;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns.SCENE_ID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns.SCORE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.AND;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.COMMA;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.DOT;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_ONE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_TO;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_ZERO;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.FROM;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.GREATER_THAN_OR_EQUAL_TO;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.INNER_JOIN;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.IS_NULL;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.LEFT_JOIN;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.ON;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.SELECT;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.VARIABLE_PLACEHOLDER;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.WHERE;
import static com.oplus.gallery.foundation.database.util.DatabaseUtils.ORDER_DATA_DECS_SCORE_DESC;
import static com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.Key.LABEL_IMAGE;
import static com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.Key.LABEL_VIDEO;
import static com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_IMAGE_GIF;
import static com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_IMAGE_HEIC;
import static com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_IMAGE_HEIF;
import static com.oplus.gallery.framework.abilities.scan.label.LabelScanner.SCENE_SCORE;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.LongSparseArray;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;

import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.bean.BaseImageInfo;
import com.oplus.gallery.business_lib.helper.HeifHelper;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketHelper;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketIdsCacheHelper;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.common.dbaccess.convert.MediaItemListConvert;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster;
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.label.bean.FaceInfo;
import com.oplus.gallery.business_lib.model.data.label.bean.ImageInfo;
import com.oplus.gallery.business_lib.model.data.utils.MixWhereClause;
import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.ConstantUtils;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.database.util.SQLGrammar;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.convert.IConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.BatchReq;
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq;
import com.oplus.gallery.foundation.dbaccess.req.DataReq;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.framework.abilities.scan.R;
import com.oplus.gallery.framework.abilities.scan.face.GroupInfo;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

// FIXME: 2020/8/12 chenzengxin 被很多东西依赖，所以先放在model，需要拆分，然后对应的逻辑放在对应的模块
public class GalleryScanProviderHelper {

    protected static final int FILE_ABORT_COUNT_THRESHOLD = 3;
    static final String[] LABEL_QUERY_PROJECT = new String[]{
            DATA,
            SCENE_ID,
            SCORE,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE,
    };

    static final String[] LABEL_QUERY_MANUAL_PROJECT = new String[]{
            IS_MANUAL,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE,
    };


    private static final String TAG = "GalleryScanProviderHelper";
    private static final int BATCH_SIZE = 500;
    /**
     * 图片被删除，在回收站中，is_recycled状态被修改为1
     * 查询scan_face表中数据，需剔除在回收站的
     */
    private static final String IS_RECYCLED_VALUE = "1";

    enum ImageStatus {
        RECYCLED,
        NO_FACE,
        INVALID
    }

    public static ArrayList<BaseImageInfo> getAllInvisibleFaceScannImage(boolean noFaceExclude) {
        ArrayList<BaseImageInfo> list = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(GalleryStore.ScanFace.FACE_VISIBLE + " != 1");
        sb.append(" AND " + GalleryStore.ScanFace.SCAN_DATE + " IS NULL");
        if (noFaceExclude) {
            sb.append(" AND " + GalleryStore.ScanFace.NO_FACE + " != 1");
        }
        sb.append(" group by " + GalleryStore.ScanFace.TAB + "." + GalleryStore.ScanFace.DATA);
        list.addAll(getScanFaceInfo(sb.toString()));
        return list;
    }

    /**
     * 获取已经扫描过并且不在回收站中的图片/视频信息
     *
     * @return ArrayList<FaceInfo>
     */
    public static ArrayList<FaceInfo> getScannedItemsWithoutRecycled() {
        ArrayList<FaceInfo> list = new ArrayList<>();
        String whereClause = GalleryStore.ScanFace.IS_RECYCLED + " != 1 ";
        list.addAll(getScanFaceInfo(whereClause));
        return list;
    }

    /**
     * 判断是否再需要人脸扫描
     *
     * @return true:需要扫描，false：不需要扫描
     */
    public static boolean needFaceScan(Context context) {
        SharedPreferences sp = SPUtils.getSp(context, ComponentPrefUtils.COMPONENT_PREF_NAME);
        boolean scanOverOnForeground = sp.getBoolean(ComponentPrefUtils.HAS_HD_FACE_FOREGROUND_SCAN_OVER_KEY, false);
        GLog.d(TAG, LogFlag.DL, "needFaceScan: scannedOnForeground = " + scanOverOnForeground);
        // 如果算法升级后未完整所有图片的前台扫描，需要继续扫描
        if (!scanOverOnForeground) {
            return true;
        }
        long lastScanOverTime = ComponentPrefUtils.getLongPref(context, LAST_FOREGROUND_SCAN_OVER_TIME_KEY, 0L);
        // 如果距离最后一次完全扫描，有新拍的图片，也需要继续扫描
        return hasLocalMediaIncreased(lastScanOverTime);
    }

    /**
     * call this method, we can't keep user unmounted and recycle face info
     */
    public static void deleteInvalidFace(int updateVersion) {
        try {
            String whereClause = "(" + GalleryStore.ScanFace.INVALID + " = 1 OR "
                    + GalleryStore.ScanFace.IS_RECYCLED + " = 1) AND "
                    + GalleryStore.ScanFace.MODEL_VERSION + " < " + updateVersion;
            DeleteReq req = new DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setWhere(whereClause)
                    .build();
            DataAccess.getAccess().delete(req);
        } catch (Exception e) {
            GLog.w(TAG, e);
        }
    }

    public static Map<String, Boolean> getFaceStatus() {
        Map<String, Boolean> map = new HashMap<>();
        Cursor cursor = null;
        try {
            //TagsStore.FacesImage.getContentUri()
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setProjection(new String[]{GalleryStore.ScanFace.DATA, GalleryStore.ScanFace.NO_FACE})
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return map;
            }
            int indexPath = cursor.getColumnIndex(GalleryStore.ScanFace.DATA);
            int indexNoFace = cursor.getColumnIndex(GalleryStore.ScanFace.NO_FACE);
            while (cursor.moveToNext()) {
                map.put(cursor.getString(indexPath), cursor.getInt(indexNoFace) == 0);
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return map;
    }

    /**
     * get classify image without recycled, Data Synchronized Used
     *
     * @return ArrayList<ImageInfo>
     */
    public static ArrayList<BaseImageInfo> getClassifyImageWithoutRecycled(boolean updateSuccess) {
        ArrayList<BaseImageInfo> list = new ArrayList<>();
        int tableType = updateSuccess ? GalleryDbDao.TableType.SCAN_LABEL : GalleryDbDao.TableType.SCAN_LABEL_BACKUP;
        String whereClause = GalleryStore.ScanLabelColumns.IS_RECYCLED + " != 1 ";
        list.addAll(getClassifyImageInfo(tableType, whereClause));
        return list;
    }

    private static ArrayList<ImageInfo> getClassifyImageInfo(int tableType, String whereClause) {
        ArrayList<ImageInfo> list = new ArrayList<>();
        String tab = (tableType == GalleryDbDao.TableType.SCAN_LABEL) ? GalleryStore.ScanLabel.TAB : GalleryStore.ScanLabelBackup.TAB;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns._ID + ", ");
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + ", ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns.DATA + ", ");
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED + ", ");
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN + ", ");
        stringBuilder.append(tab + "." + GalleryStore.GalleryColumns.LocalColumns.INVALID + ", ");
        stringBuilder.append(GalleryStore.ScanLabelColumns.IS_RECYCLED + " FROM ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + " INNER JOIN " + tab + " ON ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + DATA + "=" + tab + "." + DATA);
        if (whereClause != null) {
            stringBuilder.append(" WHERE ");
            stringBuilder.append(whereClause);
        }
        stringBuilder.append(AND).append(DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB));

        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(new CursorConvert())
                .setQuerySql(stringBuilder.toString())
                .setSqlArgs(null)
                .build();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return list;
            }
            list.addAll(ImageInfo.buildImageInfoListForClassify(cursor));
        } catch (Exception e) {
            GLog.e(TAG, e);
        }
        return list;
    }

    /**
     * delete classify record by file path
     *
     * @return void
     */
    public static void deleteClassifyImage(ArrayList<BaseImageInfo> imageList, boolean updateSuccess) {
        if ((imageList == null) || imageList.isEmpty()) {
            GLog.w(TAG, "deleteClassifyImage, imageList is empty!");
            return;
        }
        try {
            StringBuilder sb = new StringBuilder();
            sb.append(GalleryStore.ScanLabel.DATA + " IN (");
            for (BaseImageInfo image : imageList) {
                sb.append("\"");
                sb.append(image.mFilePath);
                sb.append("\"");
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append(")");
            if (updateSuccess) {
                DeleteReq req = new DeleteReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                        .setWhere(sb.toString())
                        .build();
                DataAccess.getAccess().delete(req);
            } else {
                DeleteReq req = new DeleteReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_LABEL_BACKUP)
                        .setWhere(sb.toString())
                        .build();
                DataAccess.getAccess().delete(req);
            }
        } catch (Exception e) {
            GLog.e(TAG, e);
        }
    }

    /**
     * delete classify record by file path
     *
     * @return void
     */
    public static void deleteClassifyMediaItem(ArrayList<MediaItem> mediaList) {
        if ((mediaList == null) || mediaList.isEmpty()) {
            GLog.e(TAG, "deleteClassifyImage, imageList is empty!");
            return;
        }
        try {
            StringBuilder sb = new StringBuilder();
            sb.append(GalleryStore.ScanLabel.DATA + " IN (");
            for (MediaItem item : mediaList) {
                sb.append("\"");
                sb.append(item.getFilePath());
                sb.append("\"");
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append(")");
            DeleteReq req = new DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                    .setWhere(sb.toString())
                    .build();
            DataAccess.getAccess().delete(req);
        } catch (Exception e) {
            GLog.e(TAG, e);
        }
    }

    /**
     * 获取精选表需要更新的数据库更新请求
     *
     * @param imageList
     * @return
     */
    public static List<UpdateReq> getSeniorMediaUpdateReqList(List<BaseImageInfo> imageList) {
        List<UpdateReq> operations = new ArrayList<>();
        if ((imageList == null) || imageList.isEmpty()) {
            GLog.w(TAG, "getSeniorMediaUpdateReqList, imageList is empty!");
            return operations;
        }
        int size = imageList.size();
        GLog.d(TAG, "getSeniorMediaUpdateReqList, imageList.mSize: " + size);
        for (BaseImageInfo image : imageList) {
            ContentValues value = new ContentValues();
            if (!TextUtils.isEmpty(image.mFilePath) && !TextUtils.isEmpty(image.mNewFilePath)
                    && !image.mFilePath.equalsIgnoreCase(image.mNewFilePath)) {
                value.put(GalleryStore.SeniorMedia.DATA, image.mNewFilePath);
            }
            UpdateReq.Builder builder = new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SENIOR_MEDIA)
                    .setConvert(new IConvert<Void, ContentValues>() {
                        @Override
                        public ContentValues convert(Void aVoid) {
                            return value;
                        }
                    });
            builder.setWhere(GalleryStore.SeniorMedia.DATA + " = ?")
                    .setWhareArgs(new String[]{image.mFilePath});
            operations.add(builder.build());
        }
        return operations;
    }

    public static List<UpdateReq> getLabelsUpdateReqList(List<BaseImageInfo> imageList) {
        int result = 0;
        List<UpdateReq> operations = new ArrayList<>();
        if ((imageList == null) || imageList.isEmpty()) {
            GLog.w(TAG, "changedLabelsImage, imageList is empty!");
            return operations;
        }
        int size = imageList.size();
        GLog.d(TAG, "changedLabelsImage, imageList.mSize: " + size);
        for (BaseImageInfo image : imageList) {
            ContentValues value = new ContentValues();
            //            value.put(GalleryStore.ScanLabel.MEDIA_ID, image.mNewMediaId);
            if ((image.mFilePath != null) && (image.mNewFilePath != null)
                    && !image.mFilePath.equalsIgnoreCase(image.mNewFilePath)) {
                value.put(GalleryStore.ScanLabel.DATA, image.mNewFilePath);
            }
            value.put(GalleryStore.ScanLabel.IS_RECYCLED, false);
            UpdateReq.Builder builder = new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                    .setConvert(new IConvert<Void, ContentValues>() {
                        @Override
                        public ContentValues convert(Void aVoid) {
                            return value;
                        }
                    });
            builder.setWhere(GalleryStore.ScanLabel.DATA + " = ?")
                    .setWhareArgs(new String[]{image.mFilePath});
            operations.add(builder.build());
        }
        return operations;
    }

    public static ContentValues buildLabelEntry(BaseScanLabelInfo baseScanLabelInfo) {
        ContentValues values = new ContentValues();
        values.put(DATA, baseScanLabelInfo.mFilePath);
        values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE, baseScanLabelInfo.mMediaType);
        values.put(INVALID, baseScanLabelInfo.mInvalid);
        values.put(IS_RECYCLED, baseScanLabelInfo.mIsRecycled);
        values.put(SCENE_ID, baseScanLabelInfo.mSceneId);
        values.put(SCORE, baseScanLabelInfo.mScore);
        values.put(IS_SYNC, baseScanLabelInfo.mIsSync);
        values.put(IS_MANUAL, baseScanLabelInfo.mIsManual);
        return values;
    }

    public static ContentValues buildLabelEntry(int labelId, MediaItem mediaItem) {
        ContentValues values = new ContentValues();
        values.put(DATA, mediaItem.getFilePath());
        values.put(GalleryStore.ScanLabelColumns.MEDIA_TYPE, mediaItem.getMediaType());
        values.put(INVALID, mediaItem.getInvalid());
        values.put(IS_RECYCLED, mediaItem.isRecycledItem());
        values.put(SCENE_ID, labelId);
        values.put(SCORE, SCENE_SCORE);
        values.put(IS_SYNC, mediaItem.isSync());
        values.put(IS_MANUAL, HAS_MANUAL_STATE);
        return values;
    }

    public static boolean updateLabelData() {
        GLog.w(TAG, "updateLabelData");
        boolean result = true;
        // 1.更新scan_label_backup表中，scan_label与scan_label_backup中_data与scene_id相同且is_manual=1的数据，取交集
        ArrayList<BaseScanLabelInfo> list = queryLabelImageList(getIntersectionManualLabelDataWhereClause());
        long queryTime = System.currentTimeMillis();
        if ((list != null) && !list.isEmpty()) {
            List<DataReq<?>> reqs = new ArrayList<>();
            try {
                for (BaseScanLabelInfo labelInfo : list) {
                    DataReq req = new UpdateReq.Builder()
                            .setDaoType(IDao.DaoType.GALLERY)
                            .setTableType(GalleryDbDao.TableType.SCAN_LABEL_BACKUP)
                            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                            .setWhere(DATA + EQUAL + "\"" + labelInfo.mFilePath + "\"" + AND + SCENE_ID + EQUAL + labelInfo.mSceneId)
                            .setConvert(new IConvert<Void, ContentValues>() {
                                @Override
                                public ContentValues convert(Void aVoid) {
                                    return buildLabelEntry(labelInfo);
                                }
                            }).build();
                    reqs.add(req);
                }
                BatchResult[] results = new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                        .addDataReqs(reqs)
                        .build().exec();
                GLog.d(TAG, "updateLabelData update success count = " + results.length);
            } catch (Exception e) {
                GLog.e(TAG, "updateLabelData getIntersectionManualLabelDataWhereClause, Exception = " + e);
            } finally {
                UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_LABEL_BACKUP, IDao.DaoType.GALLERY);
                GLog.w(TAG, "queryLabelImageList, queryTime :" + GLog.getTime(queryTime));
            }
        }

        // 2.往scan_label_backup表插入scan_label中is_manual=1独有数据
        result = updateLabelImageListManual(queryTime);

        return result;
    }

    private static boolean updateLabelImageListManual(long queryTime) {
        boolean result = true;
        ArrayList<BaseScanLabelInfo> list = queryLabelImageList(getManualLabelDataWhereClause());
        if (list == null) {
            result = false;
        } else if (!list.isEmpty()) {
            List<ContentValues> updateValues = new ArrayList<>();
            for (BaseScanLabelInfo labelInfo : list) {
                updateValues.add(buildLabelEntry(labelInfo));
            }

            try {
                new BulkInsertReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_LABEL_BACKUP)
                        .setConvert(new IConvert<Void, ContentValues[]>() {
                            @Override
                            public ContentValues[] convert(Void aVoid) {
                                return updateValues.toArray(new ContentValues[0]);
                            }
                        }).build().exec();
            } catch (Exception e) {
                result = false;
                GLog.e(TAG, "updateLabelData getManualLabelDataWhereClause, Exception = " + e);
            } finally {
                GLog.w(TAG, "queryLabelImageList, queryTime :" + GLog.getTime(queryTime));
            }
        }
        return result;
    }

    /**
     * 获取label表信息
     */
    @Nullable
    public static ArrayList<BaseScanLabelInfo> queryLabelImageList(String where) {
        ArrayList<BaseScanLabelInfo> labelList = null;

        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setQuerySql(where)
                .setSqlArgs(null)
                .setConvert(new CursorConvert())
                .build();
        long queryTime = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "queryLabelImageList, queryTime :" + GLog.getTime(queryTime));
            }
            labelList = new ArrayList<>();

            if (cursor != null) {
                int indexData = cursor.getColumnIndex(GalleryStore.ScanLabelColumns.DATA);
                int indexMediaType = cursor.getColumnIndex(GalleryStore.ScanLabelColumns.MEDIA_TYPE);
                int indexInvalid = cursor.getColumnIndex(INVALID);
                int indexIsRecycled = cursor.getColumnIndex(IS_RECYCLED);
                int indexSceneId = cursor.getColumnIndex(SCENE_ID);
                int indexScore = cursor.getColumnIndex(SCORE);
                int indexIsSync = cursor.getColumnIndex(IS_SYNC);
                int indexIsManual = cursor.getColumnIndex(GalleryStore.ScanLabelColumns.IS_MANUAL);
                queryTime = System.currentTimeMillis();
                while (cursor.moveToNext()) {
                    String data = cursor.getString(indexData);
                    int mediaType = cursor.getInt(indexMediaType);
                    int invalid = cursor.getInt(indexInvalid);
                    int isRecycled = cursor.getInt(indexIsRecycled);
                    int sceneId = cursor.getInt(indexSceneId);
                    float score = cursor.getFloat(indexScore);
                    int isSync = cursor.getInt(indexIsSync);
                    int isManual = cursor.getInt(indexIsManual);
                    BaseScanLabelInfo scanLabelInfo = new BaseScanLabelInfo();

                    scanLabelInfo.mFilePath = data;
                    scanLabelInfo.mMediaType = mediaType;
                    scanLabelInfo.mInvalid = invalid;
                    scanLabelInfo.mIsRecycled = isRecycled;
                    scanLabelInfo.mSceneId = sceneId;
                    scanLabelInfo.mScore = score;
                    scanLabelInfo.mIsSync = isSync;
                    scanLabelInfo.mIsManual = isManual;
                    labelList.add(scanLabelInfo);
                }
                if (GProperty.getLOG_GALLERY_OPEN()) {
                    GLog.v(TAG, "queryLabelImageList, moveToNextTime :" + GLog.getTime(queryTime) + ", count = " + labelList.size());
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "queryLabelImageList, Exception = " + e);
        }
        return labelList;
    }

    /**
     * 获取scan_label与scan_label_backup中_data与scene_id相同且is_manual=1的数据，取交集
     * getLabelEntries From ScanLabel with sql which is
     * <p>
     * SELECT scan_label._data,scan_label.media_type,scan_label.invalid,
     * scan_label.is_recycled,scan_label.scene_id,scan_label.score,scan_label.is_sync,scan_label.is_manual
     * FROM scan_label
     * INNER JOIN scan_label_backup ON
     * scan_label._data=scan_label_backup._data and scan_label.scene_id=scan_label_backup.scene_id
     * where (scan_label.is_manual=1 and scan_label_backup.is_manual=0)
     */
    private static String getIntersectionManualLabelDataWhereClause() {
        return SELECT
                + SCAN_LABEL + DOT + DATA + COMMA
                + SCAN_LABEL + DOT + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE + COMMA
                + SCAN_LABEL + DOT + INVALID + COMMA
                + SCAN_LABEL + DOT + IS_RECYCLED + COMMA
                + SCAN_LABEL + DOT + SCENE_ID + COMMA
                + SCAN_LABEL + DOT + SCORE + COMMA
                + SCAN_LABEL + DOT + IS_SYNC + COMMA
                + SCAN_LABEL + DOT + IS_MANUAL
                + FROM + SCAN_LABEL
                + INNER_JOIN + SCAN_LABEL_BACKUP + ON
                + SCAN_LABEL + DOT + DATA + EQUAL + SCAN_LABEL_BACKUP + DOT + DATA
                + AND
                + SCAN_LABEL + DOT + SCENE_ID + EQUAL + SCAN_LABEL_BACKUP + DOT + SCENE_ID
                + WHERE
                + "("
                + SCAN_LABEL + DOT + IS_MANUAL + EQUAL_ONE
                + AND
                + SCAN_LABEL_BACKUP + DOT + IS_MANUAL + EQUAL_ZERO
                + ")";
    }

    /**
     * 获取scan_label中is_manual=1，并且与scan_label_backup中_data、scene_id不相同的数据，
     * getLabelEntries From ScanLabel with sql which is
     * <p>
     * SELECT scan_label._data,scan_label.media_type,scan_label.invalid,
     * scan_label.is_recycled,scan_label.scene_id,scan_label.score,scan_label.is_sync,scan_label.is_manual
     * FROM scan_label
     * LEFT JOIN scan_label_backup ON
     * scan_label._data=scan_label_backup._data and scan_label.scene_id=scan_label_backup.scene_id
     * where (scan_label.is_manual=1 and scan_label_backup.scene_id is null)
     */
    private static String getManualLabelDataWhereClause() {
        return SELECT
                + SCAN_LABEL + DOT + DATA + COMMA
                + SCAN_LABEL + DOT + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE + COMMA
                + SCAN_LABEL + DOT + INVALID + COMMA
                + SCAN_LABEL + DOT + IS_RECYCLED + COMMA
                + SCAN_LABEL + DOT + SCENE_ID + COMMA
                + SCAN_LABEL + DOT + SCORE + COMMA
                + SCAN_LABEL + DOT + IS_SYNC + COMMA
                + SCAN_LABEL + DOT + IS_MANUAL
                + FROM + SCAN_LABEL
                + LEFT_JOIN + SCAN_LABEL_BACKUP + ON
                + SCAN_LABEL + DOT + DATA + EQUAL + SCAN_LABEL_BACKUP + DOT + DATA
                + AND
                + SCAN_LABEL + DOT + SCENE_ID + EQUAL + SCAN_LABEL_BACKUP + DOT + SCENE_ID
                + WHERE
                + "("
                + SCAN_LABEL + DOT + IS_MANUAL + EQUAL_ONE
                + AND
                + SCAN_LABEL_BACKUP + DOT + SCENE_ID + IS_NULL
                + ")";
    }

    public static List<UpdateReq> getMemoriesUpdateReqList(List<BaseImageInfo> imageList) {
        List<UpdateReq> operations = new ArrayList<>();
        if ((imageList == null) || imageList.isEmpty()) {
            GLog.w(TAG, "changedMemoriesImage, imageList is empty!");
            return operations;
        }
        GLog.d(TAG, "changedMemoriesImage, imageList.size: " + imageList.size());
        //update file path of MediaMeta and Set table
        for (BaseImageInfo image : imageList) {
            if ((image.mFilePath == null) || (image.mNewFilePath == null)
                    || image.mFilePath.equalsIgnoreCase(image.mNewFilePath)) {
                GLog.w(TAG, "changedMemoriesImage, filePath is null or equals");
                continue;
            }
            UpdateReq reqMemoriesSet = new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setWhere(GalleryStore.MemoriesSet.COVER_PATH + " = ? ")
                    .setWhareArgs(new String[]{image.mFilePath})
                    .setConvert(aVoid -> {
                        ContentValues value = new ContentValues();
                        value.put(GalleryStore.MemoriesSet.COVER_PATH, image.mNewFilePath);
                        return value;
                    })
                    .build();
            UpdateReq reqMemoriesSetMap = new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SETMAP)
                    .setWhere(GalleryStore.MemoriesSetmap.DATA + " = ? ")
                    .setWhareArgs(new String[]{image.mFilePath})
                    .setConvert(aVoid -> {
                        ContentValues values = new ContentValues();
                        values.put(GalleryStore.MemoriesSetmap.DATA, image.mNewFilePath);
                        return values;
                    }).build();
            operations.add(reqMemoriesSet);
            operations.add(reqMemoriesSetMap);
        }
        return operations;
    }

    /**
     * update classify image invalid
     *
     * @param context
     * @param imageList
     * @return int
     */
    public static int updateClassifyImageInvalid(Context context, ArrayList<BaseImageInfo> imageList, boolean updateSuccess) {
        int result = 0;
        if ((imageList == null) || imageList.isEmpty()) {
            GLog.w(TAG, "updateClassifyImageInvalid, imageList is empty!");
            return result;
        }
        int size = imageList.size();
        GLog.d(TAG, "updateClassifyImageInvalid, imageList.size: " + size);
        //update invalid
        ContentResolver cr = context.getContentResolver();
        int tableType = updateSuccess ? GalleryDbDao.TableType.SCAN_LABEL : GalleryDbDao.TableType.SCAN_LABEL_BACKUP;
        updateInvalid(imageList, tableType);
        return result;
    }

    private static void updateInvalid(ArrayList<BaseImageInfo> imageList, int tableType) {
        ContentValues invalidValue = new ContentValues();
        StringBuilder invalidSb = null;
        ContentValues validValue = new ContentValues();
        StringBuilder validSb = null;
        for (BaseImageInfo image : imageList) {
            if (image.mInvalid) {
                if (invalidSb == null) {
                    invalidSb = new StringBuilder();
                    invalidSb.append(GalleryStore.ScanLabel.DATA + " IN (");
                    invalidValue.put(GalleryStore.ScanLabel.INVALID, true);
                }
                invalidSb.append("\"");
                invalidSb.append(image.mFilePath);
                invalidSb.append("\"");
                invalidSb.append(",");
            } else {
                if (validSb == null) {
                    validSb = new StringBuilder();
                    validSb.append(GalleryStore.ScanLabel.DATA + " IN (");
                    validValue.put(GalleryStore.ScanLabel.INVALID, false);
                }
                validSb.append("\"");
                validSb.append(image.mFilePath);
                validSb.append("\"");
                validSb.append(",");
            }
        }
        if (invalidSb != null) {
            try {
                invalidSb.deleteCharAt(invalidSb.length() - 1);
                invalidSb.append(")");
                new UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(tableType)
                        .setWhere(invalidSb.toString())
                        .setConvert(new IConvert<Void, ContentValues>() {
                            @Override
                            public ContentValues convert(Void aVoid) {
                                return invalidValue;
                            }
                        }).build().exec();
            } catch (Exception e) {
                GLog.e(TAG, e);
            }
        }
        if (validSb != null) {
            try {
                validSb.deleteCharAt(validSb.length() - 1);
                validSb.append(")");
                new UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(tableType)
                        .setWhere(validSb.toString())
                        .setConvert(aVoid -> validValue)
                        .build()
                        .exec();
            } catch (Exception e) {
                GLog.e(TAG, e);
            }
        }
    }

    /**
     * FIXME: Media Provider Interface
     */
    private static final String ORDER_CLAUSE = DatabaseUtils.ORDER_CLAUSE_DATE_TAKEN_DESC;
    private static final int IMAGE_SUPPORT_SIZE_MIN = 1 * 1024 * 1024;//1M
    private static final int IMAGE_SUPPORT_LENGTH_MIN = 300;

    /**
     * get image from File table, because this need 'invalid' column
     *
     * @param context
     * @return ArrayList<ImageInfo>
     */
    public static ArrayList<BaseImageInfo> getAllImageFromFileTable(Context context) {
        ArrayList<BaseImageInfo> list = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(ImageInfo.getMediaProviderProject())
                    .setWhere(getMediaProviderQueryWhereClause(context))
                    .setOrderBy(ORDER_CLAUSE)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return list;
            }
            list.addAll(ImageInfo.buildImageInfoListFromLocalMedia(cursor));
        } catch (Exception e) {
            GLog.e(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return list;
    }

    /**
     * get image from File table, because this need 'invalid' column
     *
     * @param context
     * @return ArrayList<ImageInfo>
     */
    public static ArrayList<BaseImageInfo> getAllImageForClassifyFromFileTable(Context context) {
        ArrayList<BaseImageInfo> list = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(ImageInfo.getMediaProviderProject())
                    .setWhere(getClassifyWhereClause(context, false))
                    .setOrderBy(ORDER_CLAUSE)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return list;
            }
            list.addAll(ImageInfo.buildImageInfoListFromLocalMedia(cursor));
        } catch (Exception e) {
            GLog.e(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return list;
    }

    /**
     * modify for: sync to TimerShaft, use MixAlbum.getWhereClause
     *
     * @param context
     * @return String
     */
    @VisibleForTesting
    public static String getOrcScanWhereClause(Context context) {
        StringBuilder sb = new StringBuilder();
        sb.append(MixWhereClause.getWhereClause(context, true, true, false, false));
        sb.append(" AND " + GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE + "!=" + "\"" + MIME_TYPE_IMAGE_GIF + "\"");
        return sb.toString();
    }

    /**
     * 从local_media中获取可以进行ocr扫描的图片
     *
     * @param context
     * @return ArrayList<ImageInfo>
     */
    public static ArrayList<BaseImageInfo> getAllImageForOcrScanFromLocalMedia(Context context) {
        ArrayList<BaseImageInfo> list = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(ImageInfo.getMediaProviderProject())
                    .setWhere(getOrcScanWhereClause(context))
                    .setOrderBy(ORDER_CLAUSE)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursor == null) || (cursor.getCount() == 0)) {
                GLog.w(TAG, "getAllImageForOcrScanFromLocalMedia, cursor is null or empty");
                return list;
            }
            list.addAll(ImageInfo.buildImageInfoListFromLocalMedia(cursor));
        } catch (Exception e) {
            GLog.e(TAG, "getAllImageForOcrScanFromLocalMedia fail, e:" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return list;
    }

    /**
     * 从特定标签中移除图片、视频
     *
     * @param mediaItems local_media 表的 数据实体封装
     * @param labelId    所属的 labelId
     */
    public static int removeFromLabel(List<MediaItem> mediaItems, int labelId) {
        int result = 0;
        if ((mediaItems == null) || mediaItems.isEmpty()) {
            GLog.w(TAG, "removeFromLabel, mediaIds is empty!");
            return result;
        }
        int size = mediaItems.size();
        GLog.d(TAG, "removeFromLabel, mediaIds.size: " + size);
        List<UpdateReq> operations = new ArrayList<>();
        ArrayList<String> dataList = new ArrayList<>();
        for (MediaItem mediaItem : mediaItems) {
            operations.add(createUpdateReq(labelId, mediaItem));
            dataList.add(mediaItem.getFilePath());
        }
        try {
            BatchResult[] results = new BatchReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build()
                    .exec();

            result = results.length;
            long start = System.currentTimeMillis();
            processIfUpdateFailed(results, mediaItems);
            GLog.d(TAG, LogFlag.DL, () -> "[removeFromLabel] processIfUpdateFailed cost " + GLog.getTime(start) + " ms");
            if (!dataList.isEmpty()) {
                UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_LABEL, IDao.DaoType.GALLERY);
            }
            GLog.d(TAG, "removeFromLabel, result: " + result);
        } catch (Exception e) {
            GLog.e(TAG, e);
        }

        BuildersKt.launch(AppScope.INSTANCE, (CoroutineContext) Dispatchers.getIO(), CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
            removeFromLabelStatics(dataList, labelId);
            return null;
        });
        return result;
    }

    private static UpdateReq createUpdateReq(int labelId, MediaItem mediaItem) {
        String selection = GalleryStore.ScanLabelColumns.DATA + " = ? AND " + GalleryStore.ScanLabelColumns.SCENE_ID + " = ?";
        ContentValues value = new ContentValues();
        value.put(GalleryStore.ScanLabelColumns.IS_MANUAL, HAS_MANUAL_STATE);
        return new UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert(aVoid -> value)
                .setWhere(selection)
                .setWhareArgs(new String[]{mediaItem.getFilePath(), String.valueOf(labelId)})
                .build();
    }

    private static InsertReq createInsertReq(int labelId, MediaItem mediaItem) {
        return new InsertReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert(unused -> buildLabelEntry(labelId, mediaItem)).build();
    }

    /**
     * 通过批量更新返回的结果是否失败，如果失败且属于系统截屏路径下的图片，就手动插入一条截屏标签数据。
     *
     * @param results 批量更新的结果集合
     * @param mediaItems 选中的图片数据集合
     */
    private static void processIfUpdateFailed(BatchResult[] results, List<MediaItem> mediaItems) {
        if (results.length != mediaItems.size()) {
            return;
        }
        List<MediaItem> failedItems = findScreenShotUpdateFailed(results, mediaItems);
        if (failedItems.isEmpty()) {
            return;
        }
        ArrayList<InsertReq> insertReqs = new ArrayList<>();
        ArrayList<UpdateReq> updateReqs = new ArrayList<>();
        int screenShotSceneId = LabelDictionary.getScreenShotLabelId();
        //更新失败的情况下，手动触发后台扫描完成后再更新不是此类
        for (MediaItem info : failedItems) {
            List<Integer> sceneIds = ApiDmManager.getScanDM().scanLabel(info, false);
            if ((null != sceneIds) && sceneIds.contains(screenShotSceneId)) {
                updateReqs.add(createUpdateReq(screenShotSceneId, info));
            } else {
                insertReqs.add(createInsertReq(screenShotSceneId, info));
            }
        }
        if (!insertReqs.isEmpty()) {
            BatchResult[] batchInsertResults = new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(insertReqs)
                    .build()
                    .exec();
            GLog.d(TAG, LogFlag.DL, "[processIfUpdateFailed], batchInsertResults: " + batchInsertResults.length);
        }
        if (!updateReqs.isEmpty()) {
            BatchResult[] batchUpdateResults = new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(updateReqs)
                    .build()
                    .exec();
            GLog.d(TAG, LogFlag.DL, "[processIfUpdateFailed], batchUpdateResults: " + batchUpdateResults.length);
        }
    }

    /**
     * 通过结果集合找出更新失败截图场景的媒体数据集合。
     *
     * @param results 更新的结果集合
     * @param mediaItems 待跟新的媒体数据集合
     * @return 更新失败的截图场景媒体数据
     */
    private static List<MediaItem> findScreenShotUpdateFailed(BatchResult[] results, List<MediaItem> mediaItems) {
        List<Integer> shotsBucketIds = BucketIdsCacheHelper.getScreenShotsBucketIds();
        List<MediaItem> failedItems = new ArrayList<>();
        for (int i = 0; i < results.length; i++) {
            MediaItem mediaItem = mediaItems.get(i);
            String filePath = mediaItem.getFilePath();
            if (TextUtils.isEmpty(filePath)) {
                continue;
            }
            //如果更新结果是成功的，继续下一次判断
            if (results[i].getCount() == 1) {
                continue;
            }
            //如果更新结果是失败的，且是系统截屏路径下的图片，那添加到待处理的集合中
            if (shotsBucketIds.contains(mediaItem.getBucketId())) {
                failedItems.add(mediaItem);
            }
        }
        return failedItems;
    }

    private static void removeFromLabelStatics(ArrayList<String> dataList, int labelId) {
        SharedPreferences sp = ComponentPrefUtils.getSp(ContextGetter.context);
        int imageLabelVersion = sp.getInt(ComponentPrefUtils.LABEL_COMPONENT_VERSION_KEY, -1);
        int videoLabelVersion = sp.getInt(ComponentPrefUtils.VIDEO_LABEL_COMPONENT_VERSION_KEY, -1);

        StringBuilder imageLabelInfo = new StringBuilder();
        StringBuilder videoLabelInfo = new StringBuilder();
        // 1.获取<500个被移除item的所有标签及其置信度,以及单次移除的标签数量
        if (dataList.size() > ScanTrackHelper.MAX_QUERY_LABEL_COUNT) {
            dataList = (ArrayList<String>) dataList.subList(0, ScanTrackHelper.MAX_QUERY_LABEL_COUNT);
        }

        int[] removeCount = getLabelInfoCountStatics(dataList, imageLabelInfo, videoLabelInfo, imageLabelVersion, videoLabelVersion);

        // 2.统计labelId相关数量
        int[] count = getLabelCountForStatics(labelId);

        // 3.上报埋点
        String labelName = LabelDictionary.getLabelName(labelId);
        String language = ResourceUtils.getCurrentLanguage();

        if (imageLabelInfo.length() != 0) {
            ScanTrackHelper.trackRemoveFromLabel(LABEL_IMAGE, imageLabelVersion,
                    labelId, labelName, language, count[0], count[2],
                    removeCount[0], imageLabelInfo.toString());
        }
        if (videoLabelInfo.length() != 0) {
            ScanTrackHelper.trackRemoveFromLabel(LABEL_VIDEO, videoLabelVersion,
                    labelId, labelName, language, count[1], count[3],
                    removeCount[1], videoLabelInfo.toString());
        }
    }

    public static Set<String> filterByLabels(Set<String> dataList,
                                             List<Integer> sceneIds) {
        if ((dataList == null) || dataList.isEmpty() || (sceneIds == null) || sceneIds.isEmpty()) {
            return null;
        }
        int maxListSize = DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER - sceneIds.size();
        Set<String> result = new HashSet<>();
        List<Set<String>> subDataLists = new ArrayList<>();
        Set<String> subDataList = null;
        for (String data : dataList) {
            if (subDataList == null) {
                subDataList = new HashSet<>();
                subDataLists.add(subDataList);
            }
            subDataList.add(data);
            if (subDataList.size() >= maxListSize) {
                subDataList = null;
            }
        }
        for (Set<String> tmpDataList : subDataLists) {
            result.addAll(subFilterByLabels(tmpDataList, sceneIds));
        }
        return result;
    }

    private static Set<String> subFilterByLabels(Set<String> dataList,
                                                 List<Integer> sceneIds) {
        if ((dataList == null) || dataList.isEmpty() || (sceneIds == null) || sceneIds.isEmpty()) {
            return null;
        }
        List<Object> whereArgs = new ArrayList<>();
        whereArgs.addAll(dataList);
        whereArgs.addAll(sceneIds);

        String whereQuery = DatabaseUtils.getWhereQueryIn(DATA, dataList.size())
                + AND + DatabaseUtils.getWhereQueryIn(SCENE_ID, sceneIds.size());
        try (Cursor cursor = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setProjection(new String[]{DATA})
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .setWhere(whereQuery)
                .setWhereArgs(DatabaseUtils.getWhereArgs(whereArgs))
                .setConvert(new CursorConvert())
                .build().exec()) {
            if (cursor != null) {
                Set<String> result = new HashSet<>();
                while (cursor.moveToNext()) {
                    result.add(cursor.getString(0));
                }
                return result;
            }
        }
        return null;
    }

    private static int[] getLabelInfoCountStatics(ArrayList<String> dataList,
                                                  StringBuilder imageLabelInfo,
                                                  StringBuilder videoLabelInfo,
                                                  int imageLabelVersion,
                                                  int videoLabelVersion) {
        int[] removeCount = new int[2];
        try {
            Cursor cursor = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setProjection(LABEL_QUERY_PROJECT)
                    .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                    .setWhere(DatabaseUtils.getWhereQueryIn(DATA, dataList.size()))
                    .setWhereArgs(DatabaseUtils.getWhereArgs(dataList))
                    .setOrderBy(ORDER_DATA_DECS_SCORE_DESC)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if (cursor != null) {
                Map<String, List<BaseScanLabelInfo>> labelInfoMap = new HashMap<>();
                boolean isImage = true;
                int indexMediaType = cursor.getColumnIndex(GalleryStore.ScanLabelColumns.MEDIA_TYPE);
                int indexSceneId = cursor.getColumnIndex(SCENE_ID);
                int indexScore = cursor.getColumnIndex(SCORE);
                int indexData = cursor.getColumnIndex(DATA);
                while (cursor.moveToNext()) {
                    BaseScanLabelInfo scanLabelInfo = new BaseScanLabelInfo();

                    String dataInDB = cursor.getString(indexData);
                    int mediaType = cursor.getInt(indexMediaType);
                    int sceneId = cursor.getInt(indexSceneId);
                    float score = cursor.getFloat(indexScore);

                    scanLabelInfo.mFilePath = dataInDB;
                    scanLabelInfo.mMediaType = mediaType;
                    scanLabelInfo.mSceneId = sceneId;
                    scanLabelInfo.mScore = score;
                    isImage = (mediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE);
                    scanLabelInfo.mVersion = isImage ? imageLabelVersion : videoLabelVersion;

                    if (null == labelInfoMap.get(dataInDB)) {
                        List<BaseScanLabelInfo> labelList = new ArrayList<>();
                        labelList.add(scanLabelInfo);
                        labelInfoMap.put(dataInDB, labelList);
                    } else {
                        labelInfoMap.get(dataInDB).add(scanLabelInfo);
                    }
                }

                int index = 0;
                for (String data : labelInfoMap.keySet()) {
                    List<BaseScanLabelInfo> list = labelInfoMap.get(data);
                    isImage = (list.get(0).mMediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE);
                    index = isImage ? 0 : 1;
                    removeCount[index]++;

                    appendLabelInfoStatics((isImage ? imageLabelInfo : videoLabelInfo), list);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getLabelInfoStatics, e", e);
        }

        return removeCount;
    }

    private static int[] getLabelCountForStatics(int labelId) {
        // 1统计图片数量
        int imageTotalCount = 0;
        // 1统计图片数量
        int videoTotalCount = 0;
        // 1统计图片中is_manual=1数量
        int manualImageCount = 0;
        // 2统计视频中is_manual=1数量
        int manualVideoCount = 0;
        try {
            Cursor cursor = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setProjection(LABEL_QUERY_MANUAL_PROJECT)
                    .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                    .setWhere(SCENE_ID + EQUAL + labelId)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if (cursor != null) {
                int indexMediaType = cursor.getColumnIndex(GalleryStore.ScanLabelColumns.MEDIA_TYPE);
                int indexIsManual = cursor.getColumnIndex(GalleryStore.ScanLabelColumns.IS_MANUAL);
                while (cursor.moveToNext()) {
                    int mediaType = cursor.getInt(indexMediaType);
                    int isManual = cursor.getInt(indexIsManual);
                    switch (mediaType) {
                        case GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE:
                            imageTotalCount++;
                            if (isManual == 1) {
                                manualImageCount++;
                            }
                            break;
                        case GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO:
                            videoTotalCount++;
                            if (isManual == 1) {
                                manualVideoCount++;
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getLabelCountForStatics, e", e);
        }

        return new int[]{imageTotalCount, videoTotalCount, manualImageCount, manualVideoCount};
    }

    private static void appendLabelInfoStatics(StringBuilder builder, List<BaseScanLabelInfo> labelInfoList) {
        if (!labelInfoList.isEmpty()) {
            builder.append("(");
            int count = 0;
            for (BaseScanLabelInfo labelInfo : labelInfoList) {
                builder.append(labelInfo.mSceneId);
                builder.append("-");
                builder.append(labelInfo.mScore);
                if (++count < labelInfoList.size()) {
                    builder.append(",");
                }
            }
            builder.append(")");
        }
    }

    /**
     * get media from local_media
     *
     * @param whereClause
     * @param whereArgs
     * @param orderBy
     * @return
     */
    public static ArrayList<MediaItem> getMediaItem(
            String whereClause, String[] whereArgs, String orderBy) {
        ArrayList<MediaItem> list = new ArrayList<>();
        try {
            list.addAll(new QueryReq.Builder<List<MediaItem>>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalMediaItem.getProjection())
                    .setWhere(whereClause)
                    .setWhereArgs(whereArgs)
                    .setOrderBy(orderBy)
                    .setConvert(new MediaItemListConvert())
                    .build().exec());
        } catch (Exception e) {
            GLog.e(TAG, e);
        }
        return list;
    }

    /**
     * get MediaItem from File Table
     * modify for: sync to TimerShaft, use MixAlbum.getWhereClause and get data from File Table
     *
     * @param context
     * @return ArrayList<MediaItem>
     */
    public static ArrayList<MediaItem> getMediaItemFromMediaProvider(Context context) {
        return getMediaItem(getWhereClause(context), null, ORDER_CLAUSE);
    }

    /**
     * 获取图片类型的 mediaItem列表
     * @param context
     * @return
     */
    public static ArrayList<MediaItem> getMediaItemForImgType(Context context) {
        String where = getWhereClause(context) + SQLGrammar.AND
                + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE + SQLGrammar.EQUAL
                + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE;
        return getMediaItem(where, null, ORDER_CLAUSE);
    }

    /**
     * convert ImageInfo to LocalImage
     *
     * @param imageList
     * @return ArrayList<LocalImage>
     */
    public static ArrayList<LocalImage> convertImageToLocalImage(List<BaseImageInfo> imageList) {
        ArrayList<LocalImage> list = new ArrayList<LocalImage>();
        if ((imageList == null) || imageList.isEmpty()) {
            GLog.w(TAG, "convertImageToLocalImage, imageList is empty!");
            return list;
        }
        Cursor cursor = null;
        try {
            StringBuilder sb = new StringBuilder();
            sb.append(GalleryStore.GalleryColumns.LocalColumns._ID + " IN (");
            for (BaseImageInfo image : imageList) {
                long id = image.mGalleryId;
                Path path = LocalImage.ITEM_PATH.getChild(id);
                LocalImage localImage = (LocalImage) DataManager.peekMediaObject(path);
                if (localImage == null) {
                    sb.append(id);
                    sb.append(",");
                } else {
                    list.add(localImage);
                }
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append(")");
            //LocalImage had load
            if (list.size() == imageList.size()) {
                return list;
            }
            cursor = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalImage.getProjection())
                    .setWhere(sb.toString() + " AND " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
                            + " = " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
                    .setOrderBy(ORDER_CLAUSE)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if (cursor == null) {
                return list;
            }
            while (cursor.moveToNext()) {
                long id = cursor.getLong(LocalMediaItem.INDEX_ID);
                Path path = LocalImage.ITEM_PATH.getChild(id);
                LocalImage localImage = (LocalImage) LocalMediaItem.loadOrUpdateItem(path, cursor);
                list.add(localImage);
            }
        } catch (Exception e) {
            GLog.e(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return list;
    }

    /**
     * convert ImageInfo to MediaItem
     *
     * @param imageList
     * @return ArrayList<MediaItem>
     */
    public static ArrayList<MediaItem> convertImageToMediaItem(List<BaseImageInfo> imageList) {
        ArrayList<MediaItem> list = new ArrayList<MediaItem>();
        if ((imageList == null) || imageList.isEmpty()) {
            GLog.w(TAG, "convertImageToMediaItem, imageList is empty!");
            return list;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(GalleryStore.GalleryColumns.LocalColumns._ID + " IN (");
        for (BaseImageInfo item : imageList) {
            long id = item.mGalleryId;
            int mediaType = item.mMediaType;
            Path path = null;
            if (mediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
                path = LocalImage.ITEM_PATH.getChild(id);
            } else if (mediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
                path = LocalVideo.ITEM_PATH.getChild(id);
            }
            MediaItem mediaItem = null;
            if (path != null) {
                mediaItem = (MediaItem) DataManager.peekMediaObject(path);
            }
            if (mediaItem == null) {
                sb.append(id);
                sb.append(",");
            } else {
                list.add(mediaItem);
            }
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        //LocalImage had load
        if (list.size() == imageList.size()) {
            return list;
        }
        loadMediaItem(sb.toString(), list);
        return list;
    }

    private static void loadMediaItem(String where, ArrayList<MediaItem> list) {
        try {
            list.addAll(new QueryReq.Builder<List<MediaItem>>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalMediaItem.getProjection())
                    .setWhere(where)
                    .setOrderBy(ORDER_CLAUSE)
                    .setConvert(new MediaItemListConvert())
                    .build().exec());
        } catch (Exception e) {
            GLog.e(TAG, "loadMediaItem, error: " + e);
        }
    }

    /**
     * filter image from media provider
     * modify for: sync to TimerShaft, getWhereClause include Media Type
     *
     * @return String
     */
    public static String getMediaProviderQueryWhereClause(Context context) {
        StringBuilder sb = new StringBuilder();
        sb.append(getWhereClause(context));
        if (!HeifHelper.isHeifScanEnable()) {
            sb.append(" AND " + MediaStore.Files.FileColumns.MIME_TYPE + "!=" + "\"" + MIME_TYPE_IMAGE_HEIF + "\"");
            sb.append(" AND " + MediaStore.Files.FileColumns.MIME_TYPE + "!=" + "\"" + MIME_TYPE_IMAGE_HEIC + "\"");
        }
        return sb.toString();
    }

    /**
     * modify for: sync to TimerShaft, use MixAlbum.getWhereClause
     *
     * @param context
     * @return String
     */
    public static String getWhereClause(Context context) {
        StringBuilder sb = new StringBuilder();
        sb.append(MixWhereClause.getWhereClause(context, true, false, false, false));
        sb.append(" AND (_size > " + IMAGE_SUPPORT_SIZE_MIN);
        sb.append(" OR (width > " + IMAGE_SUPPORT_LENGTH_MIN);
        sb.append(" AND height > " + IMAGE_SUPPORT_LENGTH_MIN);
        sb.append("))");
        sb.append(getExcludeWhereClause(context));
        return sb.toString();
    }

    /**
     * modify for: sync to TimerShaft, use MixAlbum.getWhereClause
     *
     * @param context
     * @return String
     */
    public static String getClassifyWhereClause(Context context, boolean onlyImage) {
        StringBuilder sb = new StringBuilder();
        sb.append(MixWhereClause.getWhereClause(context, true, onlyImage, false, false));
        if (!HeifHelper.isHeifScanEnable()) {
            sb.append(" AND " + GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE + "!=" + "\"" + MIME_TYPE_IMAGE_HEIF + "\"");
            sb.append(" AND " + GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE + "!=" + "\"" + MIME_TYPE_IMAGE_HEIC + "\"");
        }
        return sb.toString();
    }

    //Exclude folders not to scan
    public static String getExcludeWhereClause(Context context) {
        ArrayList<String> excludeFolders = new ArrayList<>();
        String[] strings = context.getResources().getStringArray(R.array.scan_face_scan_exclude_folders);
        for (String folder : strings) {
            addFolder(excludeFolders, folder);
        }
        String[] rootPath = OplusEnvironment.getAvailableRootPaths();
        StringBuilder sb = new StringBuilder();
        sb.append(" AND bucket_id NOT IN (");
        BucketHelper.appendBucketIds(excludeFolders, sb, rootPath);
        sb.append(")");
        return sb.toString();
    }

    public static void addFolder(ArrayList<String> list, String folder) {
        if ((list != null) && !list.contains(folder)) {
            list.add(folder);
        }
    }

    /**
     * FIXME: Abort File
     */
    /**
     * update abort file, update or insert
     *
     * @param fileName
     * @return void
     */
    public static void updateAbortFile(String fileName) {
        if (fileName == null) {
            GLog.w(TAG, "updateAbortFile, fileName is null!");
            return;
        }
        GLog.d(TAG, "updateAbortFile, fileName: " + fileName);
        Cursor cursor = null;
        try {
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_ABORT_FILE)
                    .setProjection(new String[]{GalleryStore.ScanAbortFile.COUNT})
                    .setWhere(GalleryStore.ScanAbortFile.FILE_NAME + " = ? ")
                    .setWhereArgs(new String[]{fileName})
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor != null) && cursor.moveToFirst()) {
                final int count = cursor.getInt(0) + 1;
                new UpdateReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_ABORT_FILE)
                        .setWhere(GalleryStore.ScanAbortFile.FILE_NAME + " = ? ")
                        .setWhareArgs(new String[]{fileName})
                        .setConvert(aVoid -> {
                            ContentValues cv = new ContentValues();
                            cv.put(GalleryStore.ScanAbortFile.COUNT, count);
                            return cv;
                        }).build().exec();
            } else {
                new InsertReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_ABORT_FILE)
                        .setConvert(aVoid -> {
                            ContentValues cv = new ContentValues();
                            cv.put(GalleryStore.ScanAbortFile.COUNT, 1);
                            cv.put(GalleryStore.ScanAbortFile.FILE_NAME, fileName);
                            return cv;
                        }).build().exec();
            }
        } catch (Exception e) {
            GLog.e(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
    }

    /**
     * get abort file list
     *
     * @return HashMap<String, Integer>
     */
    public static HashMap<String, Integer> getAbortFile(int threshold) {
        HashMap<String, Integer> list = new HashMap<String, Integer>();
        Cursor cursor = null;
        try {
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_ABORT_FILE)
                    .setProjection(new String[]{GalleryStore.ScanAbortFile.FILE_NAME, GalleryStore.ScanAbortFile.COUNT})
                    .setWhere(GalleryStore.ScanAbortFile.COUNT + " >= " + threshold)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    String fileName = cursor.getString(0);
                    int count = cursor.getInt(1);
                    list.put(fileName, count);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        GLog.d(TAG, "getAbortFile, size: " + list.size());
        return list;
    }

    public static HashMap<String, Integer> getAbortFile() {
        return getAbortFile(FILE_ABORT_COUNT_THRESHOLD);
    }

    public static Map<String, List<CvFaceCluster>> getAllFaceCluster(Context context, List<String> list) {
        Map<String, List<CvFaceCluster>> map = new HashMap<>();
        ArrayList<CvFaceCluster> cvFaceClusterList = new ArrayList<>();
        Cursor cursor = null;
        try {
            StringBuilder where = new StringBuilder();
            where.append(GalleryStore.ScanFace.DATA).append(" IN (");
            for (String value : list) {
                where.append("'").append(value).append("'");
                where.append(",");
            }
            if (where.lastIndexOf(",") == where.length() - 1) {
                where.deleteCharAt(where.length() - 1);
            }
            where.append(")");
            String sortOrder = GalleryStore.ScanFace.DATA + " ASC";
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setProjection(null)
                    .setWhere(where.toString())
                    .setOrderBy(sortOrder)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return map;
            }
            cvFaceClusterList.addAll(CvFaceCluster.buildCvFaceClusterList(cursor));
        } catch (Exception e) {
            GLog.e(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        for (CvFaceCluster face : cvFaceClusterList) {
            String filePath = face.getFilePath();
            if (TextUtils.isEmpty(filePath)) {
                continue;
            }
            List<CvFaceCluster> faceList = map.get(filePath);
            if (faceList == null) {
                faceList = new ArrayList<>();
                map.put(filePath, faceList);
            }
            faceList.add(face);
        }
        return map;
    }

    public static Map<String, Long> getAllPathMediaIdMap(Context context) {
        Map<String, Long> list = new HashMap<>();
        Cursor cursor = null;
        try {
            cursor = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(new String[]{GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, GalleryStore.GalleryColumns.LocalColumns.DATA})
                    .setWhere(getMediaProviderQueryWhereClause(context))
                    .setOrderBy(ORDER_CLAUSE)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if (cursor != null) {
                int mediaIdIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID);
                int filePathIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DATA);
                while (cursor.moveToNext()) {
                    long mediaId = cursor.getLong(mediaIdIndex);
                    String filePath = cursor.getString(filePathIndex);
                    list.put(filePath, mediaId);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getAllPathMediaIdMap error:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return list;
    }

    static ArrayList<FaceInfo> getScanFaceInfo(String whereClause) {
        ArrayList<FaceInfo> list = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns._ID + ", ");
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + ", ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns.DATA + ", ");
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED + ", ");
        stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN + ", ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns.INVALID + ", ");
        stringBuilder.append(GalleryStore.ScanFace.IS_RECYCLED + ", ");
        stringBuilder.append(GalleryStore.ScanFace.MODEL_VERSION + ", ");
        stringBuilder.append(GalleryStore.ScanFace.TAB + "." + GalleryStore.ScanFace.MEDIA_TYPE + " FROM ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + " INNER JOIN " + GalleryStore.ScanFace.TAB + " ON ");
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + DATA + "=" + GalleryStore.ScanFace.TAB + "." + DATA);
        stringBuilder.append(" WHERE ");
        stringBuilder.append(DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB));
        stringBuilder.append(AND);
        stringBuilder.append(DatabaseUtils.getOnlyLocalWhere());
        if (whereClause != null) {
            stringBuilder.append(AND);
            stringBuilder.append(whereClause);
        }

        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(new CursorConvert())
                .setQuerySql(stringBuilder.toString())
                .setSqlArgs(null)
                .build();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return list;
            }
            list.addAll(FaceInfo.buildFaceInfoList(cursor));
        } catch (Exception e) {
            GLog.e(TAG, e);
        }
        return list;
    }

    public static Map<String, Float> getIqaQualityScoreMap(List<String> dataList, int version) {
        if ((dataList == null) || dataList.isEmpty()) {
            return null;
        }
        int maxVariableNum = DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER - ((version < 0) ? 0 : 1);
        List<List<String>> subDataLists = new ArrayList<>();
        int subListSize = (dataList.size() - 1) / maxVariableNum + 1;
        for (int i = 0; i < subListSize; i++) {
            subDataLists.add(dataList.subList(i * maxVariableNum,
                    Math.min((i + 1) * maxVariableNum, dataList.size())));
        }
        Map<String, Float> result = new HashMap<>();
        for (List<String> subDataList : subDataLists) {
            result.putAll(getSubScoreMap(subDataList, version));
        }
        return result;
    }

    private static Map<String, Float> getSubScoreMap(List<String> dataList, int version) {
        if ((dataList == null) || dataList.isEmpty()) {
            return null;
        }
        Map<String, Float> result = new HashMap<>();

        String where = GalleryStore.GalleryColumns.LocalColumns.QUALITY_SCORE + ConstantUtils.IS_NOT_NULL + AND
                + DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns.DATA, dataList.size());
        String[] whereArgs = DatabaseUtils.getWhereArgs(dataList);
        if (version >= 0) {
            where += AND + GalleryStore.GalleryColumns.LocalColumns.QUALITY_VERSION + GREATER_THAN_OR_EQUAL_TO
                    + VARIABLE_PLACEHOLDER;
            whereArgs = Arrays.copyOf(whereArgs, whereArgs.length + 1);
            whereArgs[whereArgs.length - 1] = String.valueOf(version);
        }
        try (Cursor cursor = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(new String[]{GalleryStore.GalleryColumns.LocalColumns.DATA,
                        GalleryStore.GalleryColumns.LocalColumns.QUALITY_SCORE})
                .setWhere(where)
                .setWhereArgs(whereArgs)
                .setConvert(new CursorConvert())
                .build().exec()) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int dataIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DATA);
                int scoreIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.QUALITY_SCORE);
                while (cursor.moveToNext()) {
                    String data = cursor.getString(dataIndex);
                    float score = cursor.getFloat(scoreIndex);
                    result.put(data, score);
                }
            }
        }
        return result;
    }

    public static void insertScore(Map<String, Float> scores, int version) {
        List<List<UpdateReq>> updateReqsList = new ArrayList<>();
        for (Map.Entry<String, Float> entry : scores.entrySet()) {
            if (updateReqsList.isEmpty()
                    || (updateReqsList.get(updateReqsList.size() - 1).size() >= BATCH_SIZE)) {
                updateReqsList.add(new ArrayList<>());
            }
            updateReqsList.get(updateReqsList.size() - 1).add(new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(GalleryStore.GalleryColumns.LocalColumns.DATA + ConstantUtils.EQUAL_TO)
                    .setWhareArgs(new String[]{entry.getKey()})
                    .setConvert(new IConvert<Void, ContentValues>() {
                        @Override
                        public ContentValues convert(Void aVoid) {
                            ContentValues contentValues = new ContentValues();
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.QUALITY_SCORE, entry.getValue());
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.QUALITY_VERSION, version);
                            return contentValues;
                        }
                    }).build());
        }
        for (List<UpdateReq> updateReqs : updateReqsList) {
            new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .addDataReqs(updateReqs)
                    .build().exec();
        }
        if (!updateReqsList.isEmpty()) {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY);
        }
    }

    public static void deleteSimilarFeatures() {
        new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SIMILAR_FEATURE)
                .build()
                .exec();
    }

    public static void deleteAbandonSimilarFeature() {
        deleteWhenNotInLocalMedia(GalleryStore.SimilarFeature.DATA, GalleryDbDao.TableType.SIMILAR_FEATURE, true);
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SIMILAR_FEATURE, IDao.DaoType.GALLERY);
    }

    /**
     * _data NOT IN ( SELECT _data FROM local_media)
     *
     * @param data      素材路径
     * @param tableType 目标表
     * @param noNotify  是否通知
     */
    public static void deleteWhenNotInLocalMedia(String data, int tableType, Boolean noNotify) {
        StringBuilder sql = new StringBuilder();
        sql.append(data);
        sql.append(ConstantUtils.NOT_IN);
        sql.append(ConstantUtils.LEFT_BRACKETS);
        sql.append(ConstantUtils.SELECT);
        sql.append(GalleryStore.GalleryColumns.LocalColumns.DATA);
        sql.append(ConstantUtils.FROM);
        sql.append(GalleryStore.GalleryMedia.TAB);
        sql.append(ConstantUtils.RIGHT_BRACKETS);
        new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(tableType)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, noNotify ? DatabaseUtils.VALUE_TRUE : DatabaseUtils.VALUE_FALSE)
                .setWhere(sql.toString())
                .build()
                .exec();
    }

    public static Map<String, float[]> getImageFeatures(List<String> dataList, int version) {
        if ((dataList == null) || dataList.isEmpty()) {
            return null;
        }
        List<List<String>> subDataLists = new ArrayList<>();
        for (int i = 0; i < ((dataList.size() - 1) / DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER + 1);
             i++) {
            subDataLists.add(dataList.subList(i * DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER,
                    Math.min((i + 1) * DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER, dataList.size())));
        }
        Map<String, float[]> result = new HashMap<>();
        for (List<String> subDataList : subDataLists) {
            result.putAll(getSubImageFeatures(subDataList, version));
        }
        return result;
    }

    public static Map<String, float[]> getSubImageFeatures(List<String> dataList, int version) {
        if ((dataList == null) || dataList.isEmpty()) {
            return null;
        }
        Map<String, float[]> result = new HashMap<>();
        String where = DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns.DATA,
                dataList.size());
        String[] whereArgs = DatabaseUtils.getWhereArgs(dataList);
        if (version >= 0) {
            where = where + AND + GalleryStore.SimilarFeature.FEATURE_VERSION + EQUAL_TO;
            whereArgs = Arrays.copyOf(whereArgs, whereArgs.length + 1);
            whereArgs[whereArgs.length - 1] = String.valueOf(version);
        }
        try (Cursor cursor = (new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SIMILAR_FEATURE)
                .setProjection(new String[]{GalleryStore.SimilarFeature.DATA,
                        GalleryStore.SimilarFeature.FEATURE})
                .setWhere(where)
                .setWhereArgs(whereArgs)
                .setConvert(new CursorConvert())
                .build().exec())) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int dataIndex = cursor.getColumnIndex(DATA);
                int featureIndex = cursor.getColumnIndex(GalleryStore.SimilarFeature.FEATURE);
                while (cursor.moveToNext()) {
                    String data = cursor.getString(dataIndex);
                    byte[] feature = cursor.getBlob(featureIndex);
                    result.put(data, getFloatArray(feature));
                }
            }
        }
        return result;
    }

    public static void insertFeature(Map<String, float[]> features, int version) {
        new BulkInsertReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SIMILAR_FEATURE)
                .setConvert(unused -> {
                    List<ContentValues> contentValuesList = new ArrayList<>();
                    for (Map.Entry<String, float[]> entry : features.entrySet()) {
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(GalleryStore.SimilarFeature.DATA, entry.getKey());
                        contentValues.put(GalleryStore.SimilarFeature.FEATURE,
                                floatsTyBytes(entry.getValue()));
                        contentValues.put(GalleryStore.SimilarFeature.FEATURE_VERSION, version);
                        contentValuesList.add(contentValues);
                    }
                    return contentValuesList.toArray(new ContentValues[0]);
                })
                .build()
                .exec();
    }

    public static float[] getFloatArray(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        int floatSize = Float.SIZE / Byte.SIZE;
        float[] result = new float[bytes.length / floatSize];
        int tmp = 0;
        for (int i = 0; i < result.length; i++) {
            tmp = 0;
            for (int shiftBy = 0; shiftBy < floatSize; shiftBy++) {
                tmp |= (bytes[shiftBy + i * floatSize] & 0xff) << shiftBy * Byte.SIZE;
            }
            result[i] = Float.intBitsToFloat(tmp);
        }
        return result;
    }

    public static byte[] floatsTyBytes(float[] floats) {
        if (floats == null) {
            return null;
        }
        int floatSize = Float.SIZE / Byte.SIZE;
        byte[] result = new byte[floats.length * floatSize];
        int tmp = 0;
        for (int i = 0; i < floats.length; i++) {
            tmp = Float.floatToIntBits(floats[i]);
            for (int j = 0; j < floatSize; j++) {
                result[i * floatSize + j] = new Integer(tmp & 0xff).byteValue();
                tmp = tmp >> Byte.SIZE;
            }
        }
        return result;
    }

    /**
     * query List<CvFaceCluster> by list _data
     *
     * @param parameters 根据GalleryStore.ScanFace.DATA，查询数据库face内容. 可查询多条、和所有内容
     * @param limit 限制查询数量
     * @return result
     */
    public static @NonNull List<CvFaceCluster> queryFaceInfoList(@NonNull List<String> parameters, int limit) {
        List<CvFaceCluster> list = new ArrayList<>();
        Cursor cursor = null;
        try {

            QueryReq.Builder<Cursor> cursorBuilder = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setConvert(new CursorConvert());
            StringBuilder where = new StringBuilder();
            String[] whereArgs = new String[parameters.size()];
            where.append(GalleryStore.ScanFace.IS_RECYCLED).append(SQLGrammar.NOT_EQUAL).append(IS_RECYCLED_VALUE);
            if (parameters.size() > 0) {
                where.append(SQLGrammar.AND).append(GalleryStore.ScanFace.DATA).append(SQLGrammar.IN);
                where.append(SQLGrammar.LEFT_BRACKETS);
                for (int i = 0; i < parameters.size(); i++) {
                    if (i > 0) {
                        where.append(SQLGrammar.COMMA_SPACE);
                    }
                    where.append(SQLGrammar.VARIABLE_PLACEHOLDER);
                    whereArgs[i] = parameters.get(i);
                }
                where.append(SQLGrammar.RIGHT_BRACKETS);
            }
            cursorBuilder.setWhere(where.toString())
                    .setWhereArgs(whereArgs);
            if (limit > 0) {
                cursorBuilder.setLimit(String.valueOf(limit));
            }
            cursor = DataAccess.getAccess().query(cursorBuilder.build());
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return list;
            }
            list.addAll(CvFaceCluster.buildCvFaceClusterList(cursor));
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "queryFaceInfoList crash", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return list;
    }

    /**
     * query List<CvFaceCluster> by groupId
     *
     * @param groupId           需大于0, 为有效值.
     * @param limit             是否需要限制条数, 默认为0,不限制.
     * @param exceptData        查询值是否需要排除当前条件，即不包含某条数据,可为null.
     * @param order             face_Expression_Score排序，true 降序 false 升序。例: face_Expression_Score DESC
     * @param expressionDetails 表情详情：大笑：0 微笑：1 中立：2  默认-1不处理
     * @return result
     */
    public static @NonNull List<CvFaceCluster> queryFaceInfoListByGroupId(
            int groupId,
            int limit,
            @Nullable String exceptData,
            boolean order,
            int[] expressionDetails
    ) {
        List<CvFaceCluster> list = new ArrayList<>();
        if (groupId <= 0) {
            GLog.w(TAG, LogFlag.DL, "queryFaceInfoListByGroupId, groupId is invalid, groupId:" + groupId);
            return list;
        }
        Cursor cursor = null;
        try {
            QueryReq.Builder<Cursor> cursorBuilder = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setConvert(new CursorConvert());
            if (limit > 0) {
                cursorBuilder.setLimit(String.valueOf(limit));
            }
            /**
             * 人脸坐标不能为空，升级数据库后，老数据的face_landmark都为空，不可用作参考，也不能直接清除老数据，因为可能是前端针对某一图片操作，批量清除老数据影响过大
             */
            StringBuilder queryString = new StringBuilder()
                    .append(GalleryStore.ScanFace.IS_RECYCLED)
                    .append(SQLGrammar.NOT_EQUAL_TO)
                    .append(SQLGrammar.AND)
                    .append(GalleryStore.ScanFace.GROUP_ID)
                    .append(SQLGrammar.EQUAL_TO)
                    .append(SQLGrammar.AND)
                    .append(GalleryStore.ScanFace.FACE_LANDMARK)
                    .append(SQLGrammar.IS_NOT_NULL);
            List<String> whereArgs = new ArrayList<>();
            whereArgs.add(IS_RECYCLED_VALUE);
            whereArgs.add(String.valueOf(groupId));
            // 传入参数大于0
            if ((expressionDetails.length > 0) && (expressionDetails[0] != EXPRESSION_DETAIL_DEFAULT)) {
                String whereIn = DatabaseUtils.getWhereIn(GalleryStore.ScanFace.EXPRESSION_DETAIL,
                        Arrays.stream(expressionDetails)
                                .boxed()
                                .collect(Collectors.toCollection(ArrayList::new)));
                queryString.append(SQLGrammar.AND)
                        .append(whereIn);
            }
            if (exceptData != null) {
                queryString.append(SQLGrammar.AND)
                        .append(GalleryStore.ScanFace.DATA)
                        .append(SQLGrammar.NOT_EQUAL_TO);
                whereArgs.add(exceptData);
            }
            cursorBuilder.setWhere(queryString.toString())
                    .setWhereArgs(DatabaseUtils.getWhereArgs(whereArgs));
            cursorBuilder.setOrderBy(GalleryStore.ScanFace.FACE_EXPRESSION_SCORE + (order ? SQLGrammar.DESC : SQLGrammar.ASC));
            cursor = DataAccess.getAccess().query(cursorBuilder.build());
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return list;
            }
            list.addAll(CvFaceCluster.buildCvFaceClusterList(cursor));
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "queryFaceInfoListByGroupId crash", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return list;
    }

    /**
     * insert list<CvFaceCluster> to SCAN_FACE table
     *
     * @param faceInfoList 待插入到数据的集合数据
     * @return 插入返回结果
     */
    public static @Nullable BatchResult[] insertFaceInfoList(@NonNull List<CvFaceCluster> faceInfoList) {
        BatchResult[] batchResults = null;
        if (faceInfoList.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "insertFaceInfoList, list is empty");
            return batchResults;
        }
        try {
            int size = faceInfoList.size();
            List<InsertReq> operations = new ArrayList<>();
            for (int i = 0; i < size; i++) {
                CvFaceCluster cluster = faceInfoList.get(i);
                InsertReq req = new InsertReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                        .setConvert(aVoid -> CvFaceCluster.buildInsertContentValues(cluster))
                        .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                        .build();
                operations.add(req);
            }
            batchResults = new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build().exec();
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "insertFaceInfoList crash", e);
        }
        return batchResults;
    }

    /**
     * update list<CvFaceCluster> to SCAN_FACE table
     *
     * @param faceInfoList 待更新到数据的集合数据
     */
    public static void updateFaceInfoList(@NonNull List<CvFaceCluster> faceInfoList) {
        if (faceInfoList.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "updateFaceInfoList, list is empty");
            return;
        }
        int size = faceInfoList.size();
        List<UpdateReq> operations = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            CvFaceCluster face = faceInfoList.get(i);
            UpdateReq updateReq = new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setConvert(aVoid -> CvFaceCluster.buildUpdateContentValues(face))
                    .setWhere(GalleryStore.ScanFaceColumns._ID + SQLGrammar.EQUAL_TO)
                    .setWhareArgs(new String[]{String.valueOf(face.getId())})
                    .build();
            operations.add(updateReq);
        }
        try {
            new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build().exec();
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "updateFaceInfoList crash", e);
        }
    }

    /**
     * delete SCAN_FACE info by filePath
     *
     * @param filePath 根据path删除face info信息
     */
    public static void deleteFaceInfo(@NonNull String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            GLog.w(TAG, LogFlag.DL, "deleteFaceInfo, filePath is empty");
            return;
        }
        try {
            DeleteReq req = new DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setWhere(GalleryStore.ScanFace.DATA + SQLGrammar.EQUAL_TO)
                    .setWhereArgs(new String[]{filePath})
                    .build();
            DataAccess.getAccess().delete(req);
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "deleteFaceInfo crash", e);
        }
    }

    /**
     * 更新SCAN_FACE表groupID相关值
     *
     * @param updateList      需要更新的集合，CvFaceInfo中携带要更新的group ID
     * @param hasModelUpdated 因为版本更新，所以需要重新扫描
     */
    public static void batchUpdateFaceGroupId(@NonNull List<CvFaceInfo> updateList, boolean hasModelUpdated) {
        if (updateList.isEmpty()) {
            GLog.i(TAG, LogFlag.DL, "batchUpdateFaceGroupId, updateList is empty");
            return;
        }
        LongSparseArray<GroupInfo> existGroupInfos = GalleryScanPersonPetProviderHelper.getGroupInfos();
        ArrayList<Long> allManualPersonId = GalleryScanPersonPetProviderHelper.getAllManualPersonId();
        long groupData = System.currentTimeMillis();
        List<UpdateReq> operations = new ArrayList<>();
        int size = updateList.size();
        for (int i = 0; i < size; i++) {
            CvFaceInfo face = updateList.get(i);
            ContentValues value = new ContentValues();
            value.put(GalleryStore.ScanFaceColumns.GROUP_ID, face.getGroupId());
            value.put(GalleryStore.ScanFaceColumns.GROUP_DATE, groupData);
            GroupInfo info = existGroupInfos.get(face.getGroupId());
            if (null != info) {
                //if old face is not manual, we need set new face's show_state to 0 when component update
                if (hasModelUpdated && !allManualPersonId.contains(face.getId())) {
                    value.put(GalleryStore.ScanFaceColumns.GROUP_VISIBLE, GROUP_VISIBLE_INIT);
                } else {
                    value.put(GalleryStore.ScanFaceColumns.GROUP_VISIBLE, info.mGroupVisible);
                }
                value.put(GalleryStore.ScanFaceColumns.GROUP_NAME, info.mGroupName);
                value.put(GalleryStore.ScanFaceColumns.IS_MANUAL, info.mIsManual);
                value.put(GalleryStore.ScanFaceColumns.MANUAL_DATE, info.mManualDate);
                value.put(GalleryStore.ScanFaceColumns.RELATION_TYPE, info.mRelationType);
                value.put(GalleryStore.ScanFaceColumns.CUSTOM_RELATION, info.mCustomRelation);
                value.put(GalleryStore.ScanFaceColumns.IS_HIDE, info.mIsHide);
            }
            UpdateReq req = new UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setConvert(aVoid -> value)
                    .setWhere(GalleryStore.ScanFaceColumns._ID + SQLGrammar.EQUAL_TO)
                    .setWhareArgs(new String[]{String.valueOf(face.getId())})
                    .build();
            operations.add(req);
        }
        GLog.d(TAG, LogFlag.DL, "batchUpdateFaceGroupId, operations.size: " + operations.size());
        try {
            new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build().exec();
            if (size > 0) {
                UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY);
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "batchUpdateFaceGroupId, failed", e);
        }
    }

    /**
     * Marked by wangkang: 后续重feature均值表里查最大groupID，效率高点
     * 获取GalleryStore.ScanFace.TAB中GroupID最大值。
     * 用于赋值相册中仅有一张人脸无重复，无法聚类分组groupID的情况下，赋值groupID起始值，保证表中group id值唯一
     *
     * @return GroupID最大值
     */
    public static int queryMaxGroupIDForScanFaceTable() {
        int maxGroupID = 0;
        Cursor cursor = null;
        try {
            StringBuilder stringBuilder = new StringBuilder().append(SQLGrammar.SELECT)
                    .append(SQLGrammar.MAX)
                    .append(SQLGrammar.LEFT_BRACKETS)
                    .append(GalleryStore.ScanFaceColumns.GROUP_ID)
                    .append(SQLGrammar.RIGHT_BRACKETS)
                    .append(SQLGrammar.FROM)
                    .append(GalleryStore.ScanFace.TAB);
            RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setConvert(new CursorConvert())
                    .setQuerySql(stringBuilder.toString())
                    .setSqlArgs(null)
                    .build();
            cursor = DataAccess.getAccess().rawQuery(rawQueryReq);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return 0;
            }
            if (cursor.moveToFirst()) {
                maxGroupID = cursor.getInt(0);
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "queryMaxGroupIDForScanFaceTable, failed", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        GLog.i(TAG, LogFlag.DL, "queryMaxGroupIDForScanFaceTable, maxGroupID: " + maxGroupID);
        return maxGroupID;
    }

    /**
     * 根据指定图片路径，找对应时间范围内的所有图标数量
     * 查询语句例:
     * "$columnsName BETWEEN" +
     * " dateTakenInMs - 5000 AND " +
     * " dateTakenInMs + 5000" +
     * " AND $idName != $idValue LIMIT 5"
     *
     * @param filePath       要查询的图片路径
     * @param interval       毫秒，时间间隔范围，比如前后5秒。
     * @param limitCount     是否数量限制，0则不限制
     * @param containCurrent 查询结果是否包含当前filePath数据
     * @param dateTakenInMs  图片对应dateTaken时间
     * @return 查询结果，符合条件的MediaItem集合
     */
    public static @NotNull List<MediaItem> queryMediaTableData(
            @NotNull String filePath, long interval, int limitCount, boolean containCurrent, long dateTakenInMs) {
        List<MediaItem> list = new ArrayList<>();
        if (filePath.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "queryMediaTableData, target filePath is empty");
            return list;
        }
        try {
            StringBuilder stringBuilder = new StringBuilder();
            // "$columnsName BETWEEN a and b"
            stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN)
                    .append(SQLGrammar.BETWEEN)
                    .append(dateTakenInMs - interval)
                    .append(SQLGrammar.AND)
                    .append(dateTakenInMs + interval);

            if (!containCurrent) {
                // AND $idName != '$idValue'
                stringBuilder.append(SQLGrammar.AND)
                        .append(GalleryStore.GalleryColumns.LocalColumns.DATA).append(SQLGrammar.NOT_EQUAL)
                        .append(SQLGrammar.QUOTE).append(filePath).append(SQLGrammar.QUOTE);
            }
            // ABS($columnsName - $dateTaken)
            String order = new StringBuilder()
                    .append(SQLGrammar.ABS)
                    .append(SQLGrammar.LEFT_BRACKETS)
                    .append(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN)
                    .append(SQLGrammar.MINUS)
                    .append(dateTakenInMs)
                    .append(SQLGrammar.RIGHT_BRACKETS).toString();

            QueryReq.Builder<List<MediaItem>> builder = new QueryReq.Builder<List<MediaItem>>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalMediaItem.getProjection())
                    .setWhere(stringBuilder.toString())
                    .setOrderBy(order)
                    .setConvert(new MediaItemListConvert());
            if (limitCount > 0) {
                builder.setLimit(String.valueOf(limitCount));
            }
            List<MediaItem> mediaItems = builder.build().exec();
            if (mediaItems != null) {
                list.addAll(mediaItems);
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "queryMediaTableData, failed", e);
        } finally {
            GLog.d(TAG, LogFlag.DL, "queryMediaTableData end, list: " + list.size());
        }
        return list;
    }

    /**
     * 找对应时间范围内图片是否有新增
     * 查询语句例:
     * "$columnsName BETWEEN" +
     * " dateTakenInMs AND " +
     * " currentTimeInMs"
     *
     * @param lastScanOverTime 上次扫描完成记录的时间
     * @return 查询结果，符合条件的MediaItem集合
     */
    public static boolean hasLocalMediaIncreased(long lastScanOverTime) {
        List<MediaItem> list = new ArrayList<>();
        try {
            String stringBuilder = GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN
                    + SQLGrammar.BETWEEN
                    + lastScanOverTime
                    + SQLGrammar.AND
                    + System.currentTimeMillis();

            List<MediaItem> mediaItems = new QueryReq.Builder<List<MediaItem>>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalMediaItem.getProjection())
                    .setWhere(stringBuilder)
                    .setConvert(new MediaItemListConvert())
                    // 主要查下有没有就好了
                    .setLimit(String.valueOf(1))
                    .build().exec();
            if (mediaItems != null) {
                list.addAll(mediaItems);
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "hasLocalMediaIncreased, failed", e);
        } finally {
            GLog.d(TAG, LogFlag.DL, "hasLocalMediaIncreased end, list: " + list.size());
        }
        return !list.isEmpty();
    }

    /**
     * 查询scan_face表中所有的GroupID，在回收站除外
     *
     * @return GroupID集合，去重
     */
    public static @NotNull List<Integer> queryScanFaceAllGroupID() {
        List<Integer> list = new ArrayList<>();
        Cursor cursor = null;
        try {
            StringBuilder stringBuilder = new StringBuilder()
                    .append(SQLGrammar.SELECT)
                    .append(SQLGrammar.DISTINCT)
                    .append(GalleryStore.ScanFaceColumns.GROUP_ID)
                    .append(SQLGrammar.FROM)
                    .append(GalleryStore.ScanFace.TAB)
                    .append(SQLGrammar.WHERE)
                    .append(GalleryStore.ScanFace.IS_RECYCLED)
                    .append(SQLGrammar.NOT_EQUAL)
                    .append(IS_RECYCLED_VALUE);
            RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setConvert(new CursorConvert())
                    .setQuerySql(stringBuilder.toString())
                    .setSqlArgs(null)
                    .build();
            cursor = DataAccess.getAccess().rawQuery(rawQueryReq);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return list;
            }
            while (cursor.moveToNext()) {
                int columnIndex = cursor.getColumnIndex(GalleryStore.ScanFace.GROUP_ID);
                if (columnIndex >= 0) {
                    list.add(cursor.getInt(columnIndex));
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "queryAllGroupID crash", e);
        } finally {
            IOUtils.closeQuietly(cursor);
            GLog.d(TAG, LogFlag.DL, "queryAllGroupID end, list: " + list.size());
        }
        return list;
    }
}