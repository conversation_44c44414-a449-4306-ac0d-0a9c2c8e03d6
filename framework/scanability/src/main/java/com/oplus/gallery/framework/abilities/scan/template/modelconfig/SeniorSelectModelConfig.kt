/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SeniorSelectModelConfig.kt
 ** Description : 精选的ModelConfig
 ** Version     : 1.0
 ** Date        : 2024/3/22
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/3/22      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import android.content.Context
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.business_lib.util.AssetHelper
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.download.template.ModelConfig

/**
 * 精选的ModelConfig
 */
abstract class SeniorSelectModelConfig(val context: Context, modelName: String) : ModelConfig(modelName) {
    /**
     * 需要加载的模型文件名
     * 比如iqa_quality.model、ssa_quality.model
     */
    abstract val modelFileName: String

    /**
     * 从manifest.ini中读取版本号时的key，子类重写，返回不同的key进而读取相应的版本号
     */
    protected abstract val builtInVersionKey: String

    /**
     * 内置版本，比如manifest.ini中的version
     */
    override val buildInVersion: Int
        get() = getBuildInVersion(context, builtInVersionKey)

    /**
     * 当前模型版本，之前的运行过程中从本地或者云端加载的版本
     */
    override var currentVersion: Int
        get() {
            return ComponentPrefUtils.getIntPref(context, PREFIX_MODEL_VERSION + modelName, 0)
        }
        set(value) {
            ComponentPrefUtils.setIntPref(context, PREFIX_MODEL_VERSION + modelName, value)
        }

    /**
     * 返回模型的当前使用目录，此处直接返回files目录，不考虑云端模型下载；
     * 若后续美学扫描上云，currentComponentPath需要动态设置使用目录
     */
    override var currentComponentPath: String = TextUtil.EMPTY_STRING
        get() = rootFilesDir.absolutePath

    /**
     * 校验[currentComponentPath]目录下的[modelFileName]文件的md5
     */
    override fun isModelComplete(): Boolean {
        return AssetHelper.getInstance().checkMD5(context, modelFileName, currentComponentPath)
    }

    /**
     * 校验[currentComponentPath]目录下的[modelFileName]文件的可读可写性
     */
    override fun isModelCanRW(): Boolean {
        return checkFileCanRW(currentComponentPath, modelFileName)
    }

    companion object {
        private const val PREFIX_MODEL_VERSION = "model_version_"

        /**
         * 获取模型的内置版本号
         * 用到的模型：质量美学mode，相似图美学mode，相似图特征mode
         * 定义在asset中的manifest.ini中
         * @param versionKey 相应mode对应版本号的sp的key
         */
        internal fun getBuildInVersion(context: Context, versionKey: String): Int {
            return AssetHelper.getInstance().getComponentDefaultVersion(
                context,
                versionKey
            )
        }
    }
}