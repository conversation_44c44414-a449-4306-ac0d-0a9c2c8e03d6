/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LabelGraphModelLoadingTemplate.kt
 ** Description : 知识图谱下载模板类
 ** Version     : 1.0
 ** Date        : 2024/2/23
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D           2024/2/23      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.framework.abilities.extraction.labelgraph.LabelGraphModelDownloadConfig
import com.oplus.gallery.framework.abilities.scan.modeldownload.LabelGraphCryptoUtil.getLabelGraphKey

/**
 * 知识图谱下载模板类
 */
class LabelGraphModelLoadingTemplate(
    context: Context,
    modelConfig: ModelConfig,
    allowContinueAction: (() -> Boolean)? = null
) : CommonModelLoadingTemplate(context, modelConfig, allowContinueAction) {

    override val tag: String = TAG

    override val modelName: String = ModelName.LABEL_GRAPH_SOURCE

    override fun onSuccess() {
        LabelGraphModelDownloadConfig.onDownloadSuccess(context, currentVersion, ::getLabelGraphKey)
    }

    companion object {
        private const val TAG = "LabelGraphModelLoadingTemplate"
    }
}