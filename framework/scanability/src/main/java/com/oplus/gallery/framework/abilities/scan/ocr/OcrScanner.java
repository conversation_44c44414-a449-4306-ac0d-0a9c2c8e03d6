/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OcrScanner.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2017/11/15
 ** Author: yanchao.<PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yanchao.<PERSON>@Apps.Gallery3D    2017/11/15    1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.ocr;

import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V2;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.OCR_SCAN_COUNT_24H_MAX;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.oplus.gallery.business_lib.bean.BaseImageInfo;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.search.cloud.LabelCloudHelper;
import com.oplus.gallery.business_lib.model.search.ocr.OcrImageInfo;
import com.oplus.gallery.business_lib.supertext.OcrPagesDBHelper;
import com.oplus.gallery.business_lib.supertext.SuperTextAbility;
import com.oplus.gallery.business_lib.supertext.SuperTextOcrAbility;
import com.oplus.gallery.business_lib.supertext.data.AiUnitOcrResultWrap;
import com.oplus.gallery.business_lib.supertext.data.LightweightOcrResult;
import com.oplus.gallery.business_lib.supertext.data.SuperTextImageInfo;
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil;
import com.oplus.gallery.framework.abilities.config.IConfigAbility;
import com.oplus.gallery.framework.abilities.scan.ScanConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackInfo;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OcrScanner extends GalleryScan {
    private static final String TAG = ScanConst.TAG + "OcrScanner";
    private static final String SCENE_NAME = "OcrScanner";
    private static final String SCENE_SCANNER_EMPTY = SCENE_NAME + "_Empty";
    private static final int ONE_SCAN_COUNT = 5;
    private static final Object mLock = new Object();
    private List<BaseImageInfo> mImagesNeedToScan = new ArrayList<>();
    private SuperTextAbility mOcrAbility;
    private int mOcrScanCountNotInCharging = 0;
    /**
     * ocr前是否需要执行预分类,支持超级文本2.0则需要,否则不需要
     */
    private boolean mNeedPreClassify = isSupportSuperTextV2();

    public OcrScanner(Context context) {
        super(context);
    }

    private void doOcrScan(int scanTriggerType) {
        GLog.d(TAG, "doOcrScan, start, scanTriggerType is " + scanTriggerType);
        getAllScanImage();
        int needScanCount = 0;
        synchronized (mLock) {
            needScanCount = mImagesNeedToScan.size();
        }
        if (needScanCount == 0) {
            GLog.v(TAG, "doOcrScan, has no new image and update image, do not need to doOcrDataScan!");
            updateLastScanTime();
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_EMPTY, scanTriggerType);
            return;
        }

        markConditionsOnStartScan();
        mIsFirstScan = GalleryScanUtils.isFirstOcrScan(mContext);
        if (mIsFirstScan) {
            GalleryScanUtils.setFirstOcrScanPref(mContext);
        }

        scanImage(scanTriggerType);
        trackOcrScan(null, false);
        updateLastScanTime();

        // sync cloud ocr data into local ocr db
        LabelCloudHelper.updateCloudOcrDBInBackground(mContext);
    }

    private void getAllScanImage() {
        boolean resumeScan = false;
        if (isInterrupt()) {
            return;
        }
        ArrayList<OcrImageInfo> imagesScanned = OcrPagesDBHelper.getScannedImageList();
        if (isInterrupt()) {
            return;
        }
        if ((imagesScanned != null) && !imagesScanned.isEmpty()) {
            // interrupt and continue to scan
            ArrayList<BaseImageInfo> imagesCanScan = GalleryScanProviderHelper.getAllImageForOcrScanFromLocalMedia(mContext);
            // compare data
            compareAndGetScanData(imagesCanScan, imagesScanned);
            resumeScan = true;
        }
        if (!resumeScan) {
            List<BaseImageInfo> list = GalleryScanProviderHelper.getAllImageForOcrScanFromLocalMedia(mContext);
            synchronized (mLock) {
                mImagesNeedToScan = list;
            }
        }
    }

    private void compareAndGetScanData(ArrayList<BaseImageInfo> imagesCanScan, ArrayList<OcrImageInfo> imagesScanned) {
        long time = System.currentTimeMillis();
        // data mSource sync
        HashMap<String, BaseImageInfo> imageMap = GalleryScanDataManager.translateListToMap(imagesCanScan);
        HashMap<String, OcrImageInfo> scannedImageMap = GalleryScanDataManager.translateListToMap(imagesScanned);
        List<BaseImageInfo> deleteImageList = new ArrayList<>();
        List<BaseImageInfo> updateInvalidList = new ArrayList<>();
        boolean isExternalMounted = OplusEnvironment.isExternalMounted();
        // compare
        for (BaseImageInfo image : imagesScanned) {
            BaseImageInfo im = imageMap.get(image.mFilePath);
            if (im == null) {
                if (!isExternalMounted && OplusEnvironment.isExternalFile(image.mFilePath)) {
                    image.mInvalid = true;
                    updateInvalidList.add(image);
                } else {
                    File file = new File(image.mFilePath);
                    if (!file.exists()) {
                        deleteImageList.add(image);
                    }
                }
            } else if (im.mInvalid != image.mInvalid) {
                image.mInvalid = im.mInvalid;
                updateInvalidList.add(image);
            }
        }
        GLog.d(TAG, LogFlag.DL, "compareAndGetScanData, deleteImage.size: " + deleteImageList.size()
                + ", updateInvalidList.size: " + updateInvalidList.size());
        mOcrAbility.removeOcrResultListCache(deleteImageList);
        mOcrAbility.updateImageListInvalidInDB(updateInvalidList);

        imageMap.clear();
        imageMap.putAll(GalleryScanDataManager.translateListToMap(imagesScanned));
        if (!deleteImageList.isEmpty()) {
            for (BaseImageInfo imageInfo : deleteImageList) {
                imageMap.remove(imageInfo.mFilePath);
            }
        }
        int currentModelVersion = SuperTextAbility.getVersion(mContext);
        // compare, get increase image and update image
        List<BaseImageInfo> imagesNeedToScan = new ArrayList<>();
        for (BaseImageInfo image : imagesCanScan) {
            if (!image.mInvalid) {
                BaseImageInfo im = imageMap.get(image.mFilePath);
                OcrImageInfo scannedImage = scannedImageMap.get(image.mFilePath);
                if ((im == null)
                        || (im.mDateModifiedInSec != image.mDateModifiedInSec)
                        || mOcrAbility.needRepeatScan(image)
                        || ((scannedImage != null) && (scannedImage.mModelVersion != currentModelVersion))
                ) {
                    imagesNeedToScan.add(image);
                }
            }
        }
        synchronized (mLock) {
            mImagesNeedToScan = imagesNeedToScan;
        }
        GLog.d(TAG, LogFlag.DL, "compareAndGetScanData, time: " + (System.currentTimeMillis() - time) + "ms");
    }

    private void scanImage(int scanTriggerType) {
        GLog.i(TAG, LogFlag.DL, "scanImage start, scanTriggerType is  " + scanTriggerType);
        long startTime = System.currentTimeMillis();
        mOcrScanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.OCR_SCAN_COUNT_24h_KEY, 0);
        int size = 0;
        synchronized (mLock) {
            size = mImagesNeedToScan.size();
        }
        GLog.d(TAG, LogFlag.DL, "scanImage, new image size: " + size);
        ArrayList<BaseImageInfo> list = new ArrayList<>();
        int count = 0;
        while ((size > 0)
                && isAllowContinueScan(mOcrScanCountNotInCharging, OCR_SCAN_COUNT_24H_MAX, true)) {
            synchronized (mLock) {
                if (!mImagesNeedToScan.isEmpty()) {
                    list.add(mImagesNeedToScan.remove(0));
                }
                size = mImagesNeedToScan.size();
            }
            batchOcrScan(list);
            mScanImageCount += list.size();
            addScanCount(list.size());
            recodeScanInfoIfNeed(list.size());
            list.clear();
            count++;
            if ((count == ONE_SCAN_COUNT) || (size == 0)) {
                sleep(SLEEP_TIME_EACH_BACH);
                bindSmallCore();
                count = 0;
            }
        }
        long endTime = System.currentTimeMillis();
        GLog.i(TAG, LogFlag.DL, "scanImage end, time: " + (endTime - startTime) + "ms");
    }

    private void batchOcrScan(ArrayList<BaseImageInfo> list) {
        if ((list == null) || list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "batchOcrScan, list is empty!");
            return;
        }
        GLog.d(TAG, LogFlag.DL, "batchOcrScan, start! list size is " + list.size());
        long time = System.currentTimeMillis();
        List<SuperTextImageInfo> insertSuperTextList = new ArrayList<>();
        List<LocalImage> localImageList = GalleryScanProviderHelper.convertImageToLocalImage(list);
        int currentModelVersion = SuperTextAbility.getVersion(mContext);
        // get classify information
        for (LocalImage image : localImageList) {
            if (TextUtils.isEmpty(image.getFilePath())) {
                // path is null.not need to scan, skip
                continue;
            }
            Bitmap thumbnail = getThumbnail(image);
            if ((thumbnail == null) || thumbnail.isRecycled()) {
                GLog.d(TAG, LogFlag.DL, "batchOcrScan, thumbnail is null or is recycled");
                continue;
            }
            SuperTextImageInfo superTextImageInfo = new SuperTextImageInfo(image);
            superTextImageInfo.setModelVersion(currentModelVersion);
            thumbnail = BitmapUtils.rotateBitmap(thumbnail, image.getRotation(), true);
            // 如果不需要执行预分类,直接ocr,否则执行预分类且结果为有文字再ocr
            if (!mNeedPreClassify || mOcrAbility.detectImageText(superTextImageInfo, thumbnail)) {
                float scale = SuperTextAbility.getScaleFactor(image.getWidth(), image.getHeight(), thumbnail.getWidth(), thumbnail.getHeight());
                AiUnitOcrResultWrap resultWrap = mOcrAbility.ocrBitmapSync(thumbnail,
                        scale,
                        SuperTextOcrAbility.EngineDeviceType.GPU,
                        SuperTextOcrAbility.EngineDeviceType.CPU,
                        image.getHeight() * image.getWidth(),
                        false
                );
                if (resultWrap != null) {
                    LightweightOcrResult lightweightOcrResult = new LightweightOcrResult(resultWrap);
                    superTextImageInfo.setLightweightOcrResult(lightweightOcrResult);
                    superTextImageInfo.setAllText(resultWrap.getAllText());
                    GLog.d(TAG, LogFlag.DL, "batchOcrScan, path =" + PathMask.INSTANCE.mask(image.getFilePath())
                            + "has result = " + resultWrap.containsText()
                            + ", width = " + thumbnail.getWidth() + ", height=" + thumbnail.getHeight());
                } else {
                    GLog.w(TAG, LogFlag.DL, "batchOcrScan, result is null !");
                }
            } else {
                GLog.d(TAG, LogFlag.DL, "batchOcrScan, picture has no text when detected, not need scan."
                        + "filePath = " + PathMask.INSTANCE.mask(image.getFilePath()));
            }
            insertSuperTextList.add(superTextImageInfo);
        }

        mOcrAbility.saveOcrResultListToCache(insertSuperTextList);

        GLog.d(TAG, LogFlag.DL, "batchOcrScan, end! cost time: " + (System.currentTimeMillis() - time) + "ms");
    }

    private boolean isSupportSuperTextV2() {
        IConfigAbility configAbility = ((GalleryApplication) mContext.getApplicationContext())
            .getAppAbility(IConfigAbility.class);
        if (configAbility == null) {
            GLog.e(TAG, LogFlag.DL, "isSupportSupportText, configAbility is null, return false");
            return false;
        }
        boolean isSupportSuperText = Boolean.TRUE.equals(configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V2, false));
        configAbility.close();
        return isSupportSuperText;
    }

    private void addScanCount(int count) {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return;
        }
        GalleryScanMonitor.refreshScanRecordingIfNecessary(mContext);
        mOcrScanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.OCR_SCAN_COUNT_24h_KEY, 0) + count;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.OCR_SCAN_COUNT_24h_KEY, mOcrScanCountNotInCharging);
    }

    private void recodeScanInfoIfNeed(int count) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return;
        }
        int ocrScanCountAll = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.OCR_SCAN_COUNT_ALL_KEY, 0) + count;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.OCR_SCAN_COUNT_ALL_KEY, ocrScanCountAll);
        GalleryScanUtils.recordInfo(mContext, TAG, mOcrScanCountNotInCharging, ocrScanCountAll);
    }

    @Override
    public int getScanType() {
        return OCR_SCAN;
    }

    @Override
    public String getSceneName() {
        return SCENE_NAME;
    }

    private void onScanStart() {
        GLog.d(TAG, LogFlag.DL, "onScanStart");
        if (GalleryScanUtils.isOcrSwitcherOn()) {
            if (mOcrAbility == null) {
                mOcrAbility = new SuperTextAbility(mContext);
            }
        }
    }

    @Override
    public void onScan(int triggerType, ScanConfig config) {
        GLog.w(TAG, LogFlag.DL, "onScan, start scan ocr triggerType:" + triggerType);
        super.onScan(triggerType, config);
        onScanStart();
        if (mOcrAbility != null) {
            doOcrScan(triggerType);
        }
        onScanEnd();
        GLog.w(TAG, LogFlag.DL, "onScan, ocr scan end");
    }

    private void onScanEnd() {
        GLog.d(TAG, LogFlag.DL, "onScanEnd");
        if (mOcrAbility != null) {
            mOcrAbility.release();
            mOcrAbility = null;
        }
    }

    @Override
    public void onMonitorRecord(@NonNull MonitorEvent event, boolean reachThreshold) {
        super.onMonitorRecord(event, reachThreshold);
        markConditionsOnStartScan();
        GLog.d(TAG, LogFlag.DL, "onMonitorRecord, event = " + event.toString() + ", reachThreshold = " + reachThreshold);
        trackOcrScan(event, true);
    }

    @Override
    protected boolean runTaskIsNeedCharging() {
        return true;
    }

    private void trackOcrScan(MonitorEvent event, boolean isMonitor) {
        int reasonType = getReasonType(true, mOcrScanCountNotInCharging, OCR_SCAN_COUNT_24H_MAX);
        long endNativeHeapSize = getEndNativeHeapSize(event);
        Map<String, String> additionalMap = new HashMap<>();
        additionalMap.put(SCAN_FORMAT_COUNT, mScanFormatCount.toString());
        additionalMap.put(IS_MONITOR, String.valueOf(isMonitor));
        ScanTrackInfo scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap);
        GLog.d(TAG, LogFlag.DL, "trackOcrScan, recordItem = " + scanTrackInfo.toString());
        ScanTrackHelper.trackScanResult(scanTrackInfo);
    }
}