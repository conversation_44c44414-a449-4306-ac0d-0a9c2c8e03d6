/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LastYearFestivalMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/28
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D    2018/3/28       1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.app.SearchManager;
import android.app.SearchableInfo;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.framework.abilities.scan.R;
import com.oplus.gallery.router_lib.RouterManager;
import com.oplus.gallery.router_lib.meta.PostCard;
import com.oplus.gallery.router_lib.meta.RouterMeta;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem.INDEX_ID;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SEARCH_ACTIVITY;

public class LastYearFestivalMemories extends Memories {
    private static final String TAG = "LastYearFestivalMemories";
    private static final String STOP_REASON_SCAN_FAILED = TAG + "_ScanFailed";
    private static final String STOP_REASON_FESTIVAL_EMPTY = TAG + "_FestivalEmpty";
    private static final String STOP_REASON_ROUTER_NOT_FOUND = TAG + "_RouterNotFound";
    private static final String STOP_REASON_FESTIVAL_DATE_EMPTY = TAG + "_FestivalDateEmpty";
    private static final String ID_LIST_SEPARATOR = ",";
    private String mFestivalName = "";

    public LastYearFestivalMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        //init festival
        FestivalSelector selector = new FestivalSelector();
        selector.addCountryFestivals();
        List<FestivalSelector.FestivalInfo> festivalInfo = selector.getDisplayFestivals();
        if ((festivalInfo == null) || festivalInfo.isEmpty()) {
            GLog.w(TAG, "prepareMemories, no festival");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_FESTIVAL_EMPTY;
            return false;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        // get all offset date string
        List<String> dateStrList = new ArrayList<>();
        // should include current date, so offset <= 0
        for (int offset = MemoriesScannerHelper.LAST_YEAR_FESTIVAL_MEMORIES_TRIGGER_DAY_OFFSET; offset <= 0; offset++) {
            Date date = MemoriesScannerHelper.getDateOfOffset(new Date(), offset);
            // 阳历和阴历的去年这一天
            String lastYearDate = formatter.format(MemoriesScannerHelper.getLastYearDate(date));
            String lunarLastYearDate = formatter.format(MemoriesScannerHelper.getLunarLastYearDate(date));
            dateStrList.add(lastYearDate);
            dateStrList.add(lunarLastYearDate);
        }

        for (FestivalSelector.FestivalInfo info : festivalInfo) {
            if (info.hasJointDate(dateStrList)) {
                mFestivalName = info.getKey();
                return true;
            }
        }
        // 扫描中断，记录原因
        mStopReason = STOP_REASON_FESTIVAL_DATE_EMPTY;
        return false;
    }

    @Override
    public boolean scanMemories() {
        if (!TextUtils.isEmpty(mFestivalName)) {
            RouterMeta routerMeta = RouterManager.getRouterCenter().getRouter(new PostCard(SEARCH_ACTIVITY));
            if ((routerMeta == null) || (routerMeta.getClazz() == null)) {
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_ROUTER_NOT_FOUND;
                return false;
            }
            SearchManager searchManager = (SearchManager) mContext.getSystemService(Service.SEARCH_SERVICE);
            ComponentName componentName = new ComponentName(mContext, routerMeta.getClazz());
            SearchableInfo searchInfo = searchManager.getSearchableInfo(componentName);
            String mediaIds = getFestivalMediaIds(searchManager, searchInfo, mFestivalName, mMemoriesPicMin);
            ArrayList<MediaItem> items = getMediaItemById(mContext, convertStringToIdList(mediaIds), mMemoriesPicMin);
            mMediaItemList = MemoriesProviderHelper.getFilteredItemList(items, getLabelFilterIdList());
            boolean allowMemoriesCreate = allowMemoriesCreate(mMediaItemList);
            GLog.d(TAG, "scanMemories, allowMemoriesCreate:" + allowMemoriesCreate
                    + ",mMediaItemList size:" + mMediaItemList.size());
            if (allowMemoriesCreate && (mMediaItemList.size() > mMemoriesPicMin)) {
                Resources resources = mContext.getResources();
                String name = resources.getString(R.string.scan_last_year_festival_memories_name, mFestivalName);
                setMemoriesName(name);
                return true;
            } else if (mStopSubReason == null) {
                mStopSubReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            }
        }
        mStopReason = STOP_REASON_SCAN_FAILED + mStopSubReason;
        return false;
    }

    private boolean allowMemoriesCreate(List<MediaItem> itemList) {
        mStopSubReason = null;
        MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        if (MemoriesProviderHelper.hasMemoriesCreatedInDateRange(dateRange, getMemoriesId())) {
            GLog.d(TAG, "allowMemoriesCreate, memories has created in date range:" + dateRange);
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_CREATED_IN_DATE_RANGE;
            return false;
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return createMemories(mMediaItemList);
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.EVENT_LAST_YEAR_FESTIVAL_MEMORIES;
    }


    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_LAST_YEAR_FESTIVAL_MEMORIES;
    }

    private String getFestivalMediaIds(SearchManager searchManager, SearchableInfo searchInfo, String festivalName, int minCount) {
        Cursor cursor = null;
        try {
            final String uriPrefix = SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_TIME;
            cursor = SearchCommonUtils.getSuggestions(mContext, searchInfo, uriPrefix + "?"
                    + SearchSuggestionProviderUtil.QUERY_INPUT + "=" + Uri.encode(festivalName));
            if ((cursor != null) && cursor.moveToFirst()) {
                String name = cursor.getString(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil
                        .SEARCH_RESULT_NAME));
                int count = cursor.getInt(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil.SEARCH_RESULT_COUNT));
                int searchType = cursor.getInt(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil
                        .SEARCH_RESULT_TYPE));
                String idList = cursor.getString(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil
                        .SEARCH_RESULT_ID_LIST));
                GLog.v(TAG, String.format("[getFestivalMediaIds] Result = {name:'%s', "
                        + "count:%d, type:%d, idList:'%s'}", name, count, searchType, idList));
                if (count >= minCount) {
                    return idList;
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "query Exception:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return null;
    }

    private ArrayList<Integer> convertStringToIdList(String idListString) {
        if (TextUtils.isEmpty(idListString)) {
            return null;
        }
        ArrayList<Integer> mediaIds = new ArrayList<>();
        String[] sepString = idListString.split(ID_LIST_SEPARATOR);
        for (String str : sepString) {
            mediaIds.add(Integer.valueOf(str));
        }
        return mediaIds;
    }

    private ArrayList<MediaItem> getMediaItemById(Context context, ArrayList<Integer> mediaIds, int minCount) {
        if ((mediaIds == null) || (mediaIds.size() == 0)) {
            return null;
        }
        ArrayList<MediaItem> mediaItems = new ArrayList<>();
        Cursor cursorMedia = null;
        try {
            StringBuilder selection = new StringBuilder();
            selection.append(MemoriesProviderHelper.getWhereClauseOfMemoriesScanFolders(context));
            selection.append(" AND ");
            selection.append(MemoriesProviderHelper.getWhereClauseOfMimeTypes(context));
            selection.append(" AND ").append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID).append(" IN (");
            for (int id : mediaIds) {
                selection.append(id);
                selection.append(",");
            }
            selection.replace(selection.length() - 1, selection.length(), ")");
            String orderClause = MediaStore.Images.Media.DATE_TAKEN + " DESC";
            cursorMedia = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalMediaItem.getProjection())
                    .setWhere(selection.toString())
                    .setOrderBy(orderClause)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursorMedia == null) || (cursorMedia.getCount() < minCount)) {
                return null;
            }
            int dateTakenIndex = cursorMedia.getColumnIndex(MediaStore.Images.ImageColumns.DATE_TAKEN);
            Calendar calendar = Calendar.getInstance();
            //only last year
            int currentYear = calendar.get(Calendar.YEAR) - 1;
            while (cursorMedia.moveToNext()) {
                calendar.setTimeInMillis(cursorMedia.getLong(dateTakenIndex));
                int tempYear = calendar.get(Calendar.YEAR);
                if (currentYear == tempYear) {
                    int id = cursorMedia.getInt(INDEX_ID);
                    Path childPath = LocalImage.ITEM_PATH.getChild(id);
                    MediaItem mediaItem = LocalMediaItem.loadOrUpdateItem(childPath, cursorMedia);
                    mediaItems.add(mediaItem);
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursorMedia);
        }
        return mediaItems;
    }

}
