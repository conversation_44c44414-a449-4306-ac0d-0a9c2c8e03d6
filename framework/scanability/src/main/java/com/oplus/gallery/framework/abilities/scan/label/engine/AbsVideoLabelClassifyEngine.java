/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsVideoLabelClassifyEngine.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2021/10/11
 ** Author: Sandy.Meng@Apps.Gallery3D
 ** TAG: Video label SDK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Sandy.Meng@Apps.Gallery3D       2021/10/11   1.0         new video label
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.label.engine;


import android.content.Context;

import com.oplus.gallery.basebiz.helper.RemoteModelInfoManager;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.framework.abilities.download.template.ModelConfig;
import com.oplus.gallery.framework.abilities.download.template.ModelLoadingTemplate;
import com.oplus.gallery.framework.abilities.scan.template.VideoLabelModelLoadingTemplate;
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo;
import com.oplus.gallery.business_lib.model.data.label.bean.LabelInfo;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.framework.abilities.download.IDownloadAbility;
import com.oplus.gallery.framework.abilities.download.RemoteModelInfo;
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.VideoLabelModelConfig;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;

import java.util.HashMap;
import java.util.List;

/**
 * 视频引擎初始化，视频引擎初始化分为2部分。1.视频引擎下载，2.视频引擎mode加载, 其中一个没成功即为失败
 */
public abstract class AbsVideoLabelClassifyEngine {
    private static final String TAG = AbsVideoLabelClassifyEngine.class.getSimpleName();

    private long mTime;

    // Copy model files & Initialize video classifier engine
    public boolean initEngine(Context context) {
        GLog.d(TAG, "initEngine");
        tryDownloadModel(context);

        mTime = System.currentTimeMillis();
        boolean result = loadDefaultComponents();
        GLog.d(TAG, "initEngine, init time:" + (System.currentTimeMillis() - mTime) + "ms" + ", result:" + result);
        return result;
    }

    /**
     * 尝试更新模型。若服务器上有新版本，会进入下载；若无，不会进入下载
     * @param context Context
     */
    private void tryDownloadModel(Context context) {
        RemoteModelInfoManager.fetch(context, new RemoteModelInfoManager.RequestListener() {
            public void onSuccess(HashMap<String, RemoteModelInfo> modelInfoMap) {
                ModelConfig modelConfig = new VideoLabelModelConfig();
                ModelLoadingTemplate modelLoadingTemplate = new VideoLabelModelLoadingTemplate(context, modelConfig);
                RemoteModelInfo labelInfo = modelInfoMap.get(modelLoadingTemplate.getModelName());
                GLog.d(TAG, LogFlag.DF, () -> "onSuccess:"  + labelInfo);
                modelConfig.setRemoteModelInfo(labelInfo);
                if (context instanceof GalleryApplication) {
                    ((GalleryApplication) context).getAppAbility(IDownloadAbility.class)
                            .downloadFileAsync(modelLoadingTemplate, IDownloadAbility.Priority.BACKGROUND, true, null);
                }
            }

            public void onFailure(int code) {
                GLog.d(TAG, "onFailure, code = " + code);
            }
        }, false, TAG);
    }


    private boolean loadDefaultComponents() {
        GLog.d(TAG, "loadDefaultComponents");

        ModelConfig modelConfig = new VideoLabelModelConfig();
        if (!modelConfig.isModelReady()) {
            GLog.w(TAG, "loadDefaultComponents, VideoLabelScan is not ready, return");
            return false;
        }
        File modelComponentDir = modelConfig.getDefaultComponentDirPath();
        if (modelComponentDir == null) {
            GLog.w(TAG, "loadDefaultComponents, modelComponentDir is null, return");
            return false;
        }
        String componentDirPath = modelComponentDir.getAbsolutePath();
        boolean success = loadModels(componentDirPath);
        if (success) {
            return createVideoClassify(componentDirPath);
        }
        return false;
    }

    abstract boolean loadModels(String componentDirPath);

    abstract boolean createVideoClassify(String componentDirPath);

    // Process video files and return detected video labels
    public abstract List<LabelInfo> getVideoClassifyInfo(LocalVideo video);

    /**
     * 获取默认的（没有标签的）的labelInfo
     *
     * @param video 待扫描视频资源
     * @return LabelInfo
     */
    public abstract LabelInfo buildDefaultLabelInfo(LocalVideo video);

    // Release video classifier engine
    public abstract void release();
}
