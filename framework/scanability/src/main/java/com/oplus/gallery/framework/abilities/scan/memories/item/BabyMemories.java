/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BabyMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/04/10
 ** Author:caowei@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** caow<PERSON>@Apps.Gallery3D            2018/04/10      1.0      build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.bean.CreateMemoriesEntry;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.List;

public class BabyMemories extends Memories {
    private static final String TAG = "BabyMemories";
    private static final String STOP_REASON_SCAN_FAILED = TAG + "_ScanFailed";
    private static final String STOP_REASON_CREATE_EMPTY = TAG + "_CreateEmpty";
    private static final String STOP_REASON_CREATE_FAILED = TAG + "_CreateFailed";
    private static final String STOP_REASON_LESS_THAN_4_DAYS = TAG + "_LessThan4Days";
    private int mNameType = -1;

    public BabyMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        long lastMemoriesTime = MemoriesProviderHelper.getLastSpecifiedMemoriesTime(getMemoriesId());
        if ((lastMemoriesTime != 0) && (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(lastMemoriesTime,
                System.currentTimeMillis()) < getIntOptimalConfig(FACE_MEMORIES_INTERVAL))) {
            GLog.d(TAG, "prepareMemories, current time interval last BabyMemories time less than 4 days!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_LESS_THAN_4_DAYS;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        ArrayList<Long> groupIdList = GroupHelper.getAllGroupIdOfBaby();
        for (long groupId : groupIdList) {
            MemoriesScannerHelper.GroupIdListInfo info = new MemoriesScannerHelper.GroupIdListInfo();
            info.mIdList = new ArrayList<>();
            info.mIdList.add(groupId);
            info.mIdListString = MemoriesScannerHelper.GroupIdListInfo.convertIdListToString(info.mIdList);
            long sameBabyLastMemoriesTime = MemoriesProviderHelper.getSamePersonLastMemoriesTime(getMemoriesId(), info.mIdListString);
            if ((sameBabyLastMemoriesTime != 0) && (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(sameBabyLastMemoriesTime,
                    System.currentTimeMillis()) < getIntOptimalConfig(SAME_FACE_MEMORIES_INTERVAL))) {
                GLog.d(TAG, "scanMemories, current time interval last BabyMemories time"
                        + " with same person less than 30 days!");
                continue;
            }

            MemoriesScannerHelper.DateRange dateRange = new MemoriesScannerHelper.DateRange();
            dateRange.mStart = sameBabyLastMemoriesTime;
            dateRange.mEnd = System.currentTimeMillis();
            ArrayList<MediaItem> items = MemoriesProviderHelper.getItemListOfSinglePerson(mContext,
                    groupId, dateRange, mMemoriesPicMin);
            List<MediaItem> filteredItemList = MemoriesProviderHelper.getFilteredItemList(items, getLabelFilterIdList());
            GLog.v(TAG, "scanMemories, filteredItemList size:" + filteredItemList.size());
            if (filteredItemList.size() > mMemoriesPicMin) {
                if (createMemories(filteredItemList, info.mIdListString)) {
                    return true;
                }
            }
        }
        // 扫描失败，记录原因
        mStopReason = STOP_REASON_SCAN_FAILED;
        return false;
    }

    private boolean createMemories(List<MediaItem> itemList, String groupIdStr) {
        if ((itemList == null) || (itemList.isEmpty())) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATE_EMPTY;
            return false;
        }
        GLog.v(TAG, "createMemories size:" + itemList.size() + ", type:" + getMemoriesId());
        MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        CreateMemoriesEntry.CreateFaceMemoriesEntry createFaceMemoriesEntry = new CreateMemoriesEntry.CreateFaceMemoriesEntry.Builder()
                .setName(getMemoriesName())
                .setType(getMemoriesId())
                .setGroupIdStr(groupIdStr)
                .setStartTime(dateRange.mStart)
                .setEndTime(dateRange.mEnd)
                .setNameType(getNameType())
                .build();
        int setId = MemoriesProviderHelper.createFaceMemories(createFaceMemoriesEntry);
        if (setId == -1) {
            GLog.w(TAG, "createMemories create memories failed!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATE_FAILED;
            return false;
        }
        return processMemoriesItems(setId, itemList, dateRange.mStart, dateRange.mEnd);
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return true;
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.FACE_BABY_MEMORIES;
    }

    @Override
    public int getNameType() {
        if (mNameType < 0) {
            mNameType = MemoriesNameRules.getBabyRandomNameType();
        }
        return mNameType;
    }

}
