/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OcrEmbeddingScanner.kt
 ** Description : Ocr语义扫描
 ** Version     : 1.0
 ** Date        : 2024/11/21
 ** Author      : 80377872
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80377872                            2024/11/21     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.ocr

import android.content.Context
import android.provider.MediaStore.Files.FileColumns
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.OcrEmbedding.ABILITY_IS_SUPPORT_OCR_EMBEDDING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.OcrEmbedding.OCR_EMBEDDING_FIRST_SCAN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.OcrEmbedding.OCR_EMBEDDING_SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.OcrEmbedding.OCR_EMBEDDING_SCAN_COUNT_ALL
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.refreshScanRecordingIfNecessary
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility
import com.oplus.gallery.framework.abilities.search.ocrembedding.OcrEmbeddingCode
import com.oplus.gallery.framework.abilities.search.ocrembedding.OcrEmbeddingImageInfo
import com.oplus.gallery.framework.abilities.search.ocrembedding.OcrEmbeddingScanProviderHelper
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility

/**
 * OcrEmbedding扫描
 */
class OcrEmbeddingScanner(val context: Context) : GalleryScan(context)  {
    /**
     * 24小时内已经扫描的数量
     */
    private var scannedCountIn24hours = 0

    /**
     * 本次扫描总共扫描的数量
     */
    private var scannedTotalCount = 0

    /**
     * ocr内容数据存储，用于获取allImageList和lastScannedImageList时快速构造数据
     */
    private var ocrMap: Map<String, String> = mutableMapOf()

    /**
     * 所有图片列表
     */
    private var allImageList: MutableList<OcrEmbeddingImageInfo> = mutableListOf()

    /**
     * 上次扫描的图片列表
     */
    private var lastScannedImageList: MutableList<OcrEmbeddingImageInfo> = mutableListOf()

    /**
     * 获取缩图的总时间
     */
    private var getThumbTotalTime = 0L

    /**
     * 每个批次获取缩图的总时间
     */
    private var getThumbTimeBatch = 0L

    /**
     * 多模态processImage接口的执行总时间
     */
    private var processTotalTime = 0L

    override fun getScanType(): Int {
        return OCR_EMBEDDING_SCAN
    }

    override fun getSceneName(): String {
        return SCENE_NAME
    }

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        GLog.w(TAG, LogFlag.DL) { "onScan, start scan ocr triggerType:$triggerType" }
        super.onScan(triggerType, config)
        scanData()
        GLog.w(TAG, LogFlag.DL) { "onScan, ocr scan end" }
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }

    private fun scanData() {
        markConditionsOnStartScan()
        recordScanDataIn24H()
        context.getAppAbility<IOcrEmbeddingAbility>()?.use {
            try {
                if (it.isDownloading) {
                    GLog.e(TAG, LogFlag.DL) { "scanData, OcrEmbeddingAbility is downloading, return" }
                    updateAbilityIsSupported(false)
                    return
                }
                it.isScanning = true
                allImageList = getAllOcrPageList()
                if (allImageList.isEmpty()) {
                    GLog.w(TAG, LogFlag.DL) { "scanData, allImageList is empty" }
                    return
                }
                if (it.init().not()) {
                    GLog.e(TAG, LogFlag.DL) { "scanData, init ocr embedding engine fail, return" }
                    updateAbilityIsSupported(false)
                    return
                }
                // 模型更新，需要先删除旧模型生成的向量数据库和清除相册数据库
                if (it.hasOldModelData()) {
                    val removeStatus = it.removeOldModelData()
                    var delCount = 0
                    if (removeStatus) {
                        delCount = DeleteReq.Builder()
                            .setDaoType(IDao.DaoType.GALLERY)
                            .setTableType(GalleryDbDao.TableType.SCAN_OCR_EMBEDDING)
                            .build().exec()
                    }
                    GLog.d(TAG, LogFlag.DL, "scanData, model change need clear db. removeStatus: $removeStatus, delCount: $delCount")
                }
                updateAbilityIsSupported(true)
                scannedTotalCount = 0
                getThumbTotalTime = 0
                processTotalTime = 0
                lastScannedImageList = getLastScannedImageList()
                val needDeleteList = getNeedDeleteList()
                handleDelete(it, needDeleteList)
                val needScanMediaList = getNeedScanMediaList()
                val needRescanList = getNeedRescanMediaList(it)
                if (needScanMediaList.isEmpty() && needRescanList.isEmpty()) {
                    GLog.w(TAG, LogFlag.DL) { "scanData, needScanMediaList,needOnlyUpdateDbMediaList and needRescanList is empty" }
                    updateLastScanTime()
                    return
                }
                // 对新数据执行扫描
                doScan(it, needScanMediaList, true)
                // 对需要重扫的数据执行重新扫描
                doScan(it, needRescanList, false)
                updateLastScanTime()
            } finally {
                GLog.d(TAG, LogFlag.DL) { "scanData, finally, release and set ocrEmbeddingAbility is scanning to false" }
                it.release()
                it.isScanning = false
            }
        }
    }

    /**
     * 判断是否是24H内的第一次扫描，并获取24H小时内的扫描数据
     */
    private fun recordScanDataIn24H() {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            mIsFirstScan = it.getBooleanConfig(OCR_EMBEDDING_FIRST_SCAN, true) ?: true
            if (mIsFirstScan) {
                it.setBooleanConfig(OCR_EMBEDDING_FIRST_SCAN, false)
            }
            scannedCountIn24hours = it.getIntConfig(OCR_EMBEDDING_SCAN_COUNT_24H, 0) ?: 0
        }
    }

    /**
     * 更新能力是否可用
     */
    private fun updateAbilityIsSupported(isSupported: Boolean) {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            it.setBooleanConfig(ABILITY_IS_SUPPORT_OCR_EMBEDDING, isSupported)
        }
    }

    /**
     * 获取OCR数据表数据,并整合成新的OcrEmbeddingImageInfo 列表
     */
    private fun getAllOcrPageList(): MutableList<OcrEmbeddingImageInfo> {
        val startTime = System.currentTimeMillis()
        val ocrPageList: MutableList<OcrEmbeddingImageInfo> = mutableListOf()
        val allMediaList = OcrEmbeddingScanProviderHelper.getAllImageFromFileTable()
        GLog.d(TAG, LogFlag.DL) { "allMediaList, size:${allMediaList.size}" }
        ocrMap = OcrEmbeddingScanProviderHelper.getOcrDataMap()
        GLog.d(TAG, LogFlag.DL) { "ocrMap, size:${ocrMap.size}" }

        val allMediaMap = GalleryScanDataManager.translateListToPathMap(allMediaList)
        ocrMap.forEach { entry ->
            val path = entry.key
            val content = entry.value
            val baseImageInfo = allMediaMap[path.lowercase()]
            if (baseImageInfo != null) {
                val imageinfo = format2OcrEmbeddingImageInfo(baseImageInfo)
                imageinfo.content = content
                ocrPageList.add(imageinfo)
            }
        }
        GLog.d(TAG, LogFlag.DL) { "getAllOcrPageList size:${ocrPageList.size}, costTime:${GLog.getTime(startTime)} ms" }
        return ocrPageList
    }

    /**
     * 获取已经扫描的数据列表，并获取其content内容
     */
    private fun getLastScannedImageList(): MutableList<OcrEmbeddingImageInfo> {
        val scannedImageList = OcrEmbeddingScanProviderHelper.getAllOcrEmbeddingScanData()
        val lastScannedImageList = mutableListOf<OcrEmbeddingImageInfo>()
        scannedImageList.forEach {
            val content = ocrMap[it.mFilePath]
            if (content != null) {
                it.content = content
                lastScannedImageList.add(it)
            }
        }
        GLog.d(TAG, LogFlag.DL) { "getLastScannedImageList lastScannedImageList size = ${lastScannedImageList.size}" }
        return lastScannedImageList
    }

    /**
     * 获取需要扫描的图片列表
     */
    private fun getNeedScanMediaList(): MutableList<OcrEmbeddingImageInfo> {
        // 已经扫描的列表
        val map = GalleryScanDataManager.translateListToPathMap(lastScannedImageList)
        val needScanImageList = allImageList.filter {
            (map[it.mFilePath.lowercase()] == null) && (isSupportMediaType(it.mMediaType))
        }.toMutableList()
        GLog.d(TAG, LogFlag.DL) {
            "getNeedScanMediaItemList, allImageList.size = ${allImageList.size}, " +
                    "lastScannedImageList.size = ${lastScannedImageList.size},  needScanImageList.size = ${needScanImageList.size}"
        }
        return needScanImageList
    }

    /**
     * 获取需要删除的图片列表
     */
    private fun getNeedDeleteList(): MutableList<OcrEmbeddingImageInfo> {
        val map = GalleryScanDataManager.translateListToPathMap(allImageList)
        val needDeleteList = lastScannedImageList.filter {
            map[it.mFilePath.lowercase()] == null
        }.toMutableList()
        GLog.d(TAG, LogFlag.DL) {
            "getNeedDeleteList, allImageList.size = ${allImageList.size}, " +
                    "lastScannedImageList.size = ${lastScannedImageList.size}, needDeleteList.size = ${needDeleteList.size}"
        }
        return needDeleteList
    }

    /**
     * 获取需要重新扫描的图片列表，即需要重新生成向量和更新数据库的列表
     */
    private fun getNeedRescanMediaList(ability: IOcrEmbeddingAbility): MutableList<OcrEmbeddingImageInfo> {
        val notDataValid = ability.checkDataValid().not()
        val needRescanList = mutableListOf<OcrEmbeddingImageInfo>()

        /*
        * 需要重新扫描的列表
        * 来源1：notDataValid为true的数据
         */
        val notDataValidList = lastScannedImageList.filter {
            // 是支持的文件类型 && 有新版本 && (notDataValid == true)
            (isSupportMediaType(it.mMediaType)
                    && ability.hasNewVersion(it.versionModelPath)
                    && notDataValid)
        }.toMutableList()

        // 如果notDataValid为true，所有数据需要重新扫描。将数据库中所有状态设置为CODE_NEED_RESCAN
        if (notDataValid) {
            notDataValidList.forEach {
                it.mScanState = OcrEmbeddingCode.CODE_NEED_RESCAN
            }
            handleUpdateDb(notDataValidList, null)
        }

        /*
        * 需要重新扫描的列表
        * 来源2：数据库中scanState为CODE_NEED_RESCAN的数据
        * 因为中途打断了扫描，checkDataValid()发生了变化，所以必须从数据库中读取
         */
        val stateNeedRescanList = lastScannedImageList.filter {
            it.mScanState == OcrEmbeddingCode.CODE_NEED_RESCAN
        }

        needRescanList.addAll(stateNeedRescanList)
        needRescanList.addAll(notDataValidList)

        GLog.d(TAG, LogFlag.DL) {
            "getNeedReScanList, notDataValid = $notDataValid, lastScannedImageList.size = ${lastScannedImageList.size}, " +
                    "stateNeedRescanList.size = ${stateNeedRescanList.size}, " +
                    "notDataValidList.size = ${notDataValidList.size}, " +
                    "result.size = ${needRescanList.toMutableSet().toMutableList().size}"
        }

        return needRescanList.toMutableSet().toMutableList()
    }

    /**
     * 处理需要新增的图片
     *
     * 1、getOneScanCountList()，获取小于等于ONE_SCAN_COUNT个item
     * 2、使用这若干个item调用convertImageToVector()，调用底层processImage()转换成向量
     * 3、recordScanCount()，记录扫描数量信息
     * 4、调用底层flushData()，如果返回true，insertOcrEmbeddingScanData()，将扫描成功的item新增到数据库
     * 5、将被中断扫描的item，累加到interruptList
     * 6、休眠SLEEP_TIME_EACH_BACH毫秒
     */
    private fun doScan(
        ability: IOcrEmbeddingAbility,
        needScanList: MutableList<OcrEmbeddingImageInfo>,
        isAdd: Boolean
    ): MutableList<OcrEmbeddingImageInfo> {
        val interruptList = mutableListOf<OcrEmbeddingImageInfo>()
        while ((needScanList.size > 0)
            && isAllowContinueScan(scannedCountIn24hours, GalleryScanMonitor.OCR_ENBEDDING_SCAN_COUNT_24H_MAX, true)
        ) {
            val startTime = System.currentTimeMillis()
            val oneScanCountList = getOneScanCountList(needScanList)
            val convertResultList = convertImageToVector(ability, oneScanCountList)
            val flushResult = ability.flushData()
            GLog.d(TAG, LogFlag.DL) {
                "doScan, flushResult = $flushResult, " +
                        "convertResultList = ${convertResultList.map { it.mScanState }}"
            }
            if (flushResult) {
                val dbStartTime = System.currentTimeMillis()
                if (isAdd) {
                    OcrEmbeddingScanProviderHelper.insertOcrEmbeddingScanData(convertResultList, ability.currentModelVersion)
                } else {
                    handleUpdateDb(convertResultList, ability.currentModelVersion)
                }
                if (GProperty.DEBUG_SCAN) {
                    GLog.d(TAG, LogFlag.DL) { "doScan ocrEmbeddingScan costTime dbCostTime:${GLog.getTime(dbStartTime)}ms" }
                }
            }
            convertResultList.forEach {
                if (it.mScanState == OcrEmbeddingCode.CODE_INTERRUPT) {
                    interruptList.add(it)
                }
            }
            recordScanCount(convertResultList.size)
            scannedTotalCount += convertResultList.size
            val batchTotalCount = convertResultList.size
            if (batchTotalCount > 0) {
                val batchTotalTime = System.currentTimeMillis() - startTime
                GLog.d(TAG, LogFlag.DL) {
                    "doScan ocrEmbeddingScan costTime batchTotalTime:$batchTotalTime, batchTotalCount:$batchTotalCount, " +
                    "scannedTotalCount:$scannedTotalCount, " + "perItemTime:${batchTotalTime / batchTotalCount.toFloat()}ms" +
                    ",perItemGetThumbTime:${getThumbTimeBatch / batchTotalCount.toFloat()}ms"
                }
            }
            if (isInterrupt.not() && (SLEEP_TIME_EACH_BACH > 0)) {
                sleep(SLEEP_TIME_EACH_BACH)
            }
            bindSmallCore()
        }
        return interruptList
    }

    /**
     * OCR文字转换成向量
     *
     * 1、调用ability.processImageText()
     * 2、返回resultList扫描结果list，里面包含扫描成功失败状态码
     *
     * 扫描成功失败状态码：
     * 调用ability.processImageText()返回true时，状态码赋为OcrEmbeddingCode.CODE_SUCCESS
     * 调用ability.processImageText()返回false时，说明底层发生错误，状态码赋为OcrEmbeddingCode.CODE_SDK_ERROR
     * 调用过程中被中断，比如亮屏、高温，状态码赋为OcrEmbeddingCode.CODE_INTERRUPT
     */
    private fun convertImageToVector(
        ability: IOcrEmbeddingAbility,
        subList: MutableList<OcrEmbeddingImageInfo>,
    ): MutableList<OcrEmbeddingImageInfo> {
        val resultList = mutableListOf<OcrEmbeddingImageInfo>()
        val mediaItems = GalleryScanProviderHelper.convertImageToMediaItem(subList as List<BaseImageInfo>)

        GLog.d(TAG, LogFlag.DL) { "processImage, mediaItems.size = ${mediaItems.size}" }
        getThumbTimeBatch = 0
        subList.forEach { info ->
            val content = info.content.trim()
            if (ability.isAllowEmbedding(content)) {
                if (isInterrupt.not()) {
                    val startTime = System.currentTimeMillis()
                    val getThumbTime = System.currentTimeMillis()
                    getThumbTotalTime += GLog.getTime(getThumbTime)
                    if (GProperty.DEBUG_SCAN) {
                        getThumbTimeBatch += GLog.getTime(getThumbTime)
                        GLog.d(TAG, LogFlag.DL) { "convertImageToVector ocrEmbeddingScan costTime getThumbTime:${GLog.getTime(getThumbTime)}" }
                    }
                    val processTime = System.currentTimeMillis()

                    if (ability.processImageText(info.mGalleryId, content)) {
                        GLog.d(TAG, LogFlag.DL) { "processImageText success, localMediaTableId:${info.mGalleryId}" }
                        info.mScanState = OcrEmbeddingCode.CODE_SUCCESS
                    } else {
                        GLog.d(TAG, LogFlag.DL) { "processImageText fail, localMediaTableId:${info.mGalleryId}" }
                        info.mScanState = OcrEmbeddingCode.CODE_SDK_ERROR
                    }
                    resultList.add(info)

                    processTotalTime += GLog.getTime(processTime)
                    if (GProperty.DEBUG_SCAN) {
                        GLog.d(TAG, LogFlag.DL) { "convertImageToVector ocrEmbeddingScan costTime processTime:${GLog.getTime(processTime)}" }
                    }
                    mScanImageCount++
                    if (GProperty.DEBUG_SCAN) {
                        GLog.d(TAG, LogFlag.DL) { "convertImageToVector ocrEmbeddingScan costTime oneScanTime:${GLog.getTime(startTime)}" }
                    }
                } else {
                    info.mScanState = OcrEmbeddingCode.CODE_INTERRUPT
                    resultList.add(info)
                    return resultList
                }
            } else {
                info.mScanState = OcrEmbeddingCode.CODE_NOT_EMBEDDING
                resultList.add(info)
            }
        }
        return resultList
    }

    /**
     * 获取小于等于ONE_SCAN_COUNT的list
     */
    private fun getOneScanCountList(
        allList: MutableList<OcrEmbeddingImageInfo>
    ): MutableList<OcrEmbeddingImageInfo> {
        val subAddSize = if (allList.size >= ONE_SCAN_COUNT) {
            ONE_SCAN_COUNT
        } else {
            allList.size
        }
        val subList = mutableListOf<OcrEmbeddingImageInfo>()
        for (i in 0 until subAddSize) {
            subList.add(allList.removeAt(0))
        }
        return subList
    }

    /**
     * 处理删除OCR文字向量
     */
    private fun handleDelete(ability: IOcrEmbeddingAbility, needDeleteList: MutableList<OcrEmbeddingImageInfo>) {
        val successDeleteList = ability.deleteDataByLocalMediaTableIds(needDeleteList)
        GLog.d(TAG, LogFlag.DL) { "handleDelete, successDeleteList.size = ${successDeleteList.size}" }
        OcrEmbeddingScanProviderHelper.deleteOcrEmbeddingScanData(successDeleteList)
        bindSmallCore()
    }

    /**
     * 更新数据库
     */
    private fun handleUpdateDb(needUpdateDbList: MutableList<OcrEmbeddingImageInfo>, version: String?) {
        OcrEmbeddingScanProviderHelper.updateOcrEmbeddingScanData(needUpdateDbList, version)
        bindSmallCore()
    }

    /**
     * 记录 SCAN_COUNT_ALL 和 SCAN_COUNT_24H 的数据
     */
    private fun recordScanCount(count: Int) {
        addScanCountIfNoCharging(count)
        recordScanInfoIfNeed(count)
    }

    private fun addScanCountIfNoCharging(count: Int): Boolean {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true
        }
        refreshScanRecordingIfNecessary(mContext)
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            scannedCountIn24hours = (it.getIntConfig(OCR_EMBEDDING_SCAN_COUNT_24H, 0) ?: 0) + count
            it.setIntConfig(OCR_EMBEDDING_SCAN_COUNT_24H, scannedCountIn24hours)
        }
        return false
    }

    private fun recordScanInfoIfNeed(count: Int) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return
        }
        (context.applicationContext as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            val scanCountAll = (it.getIntConfig(OCR_EMBEDDING_SCAN_COUNT_ALL, 0) ?: 0) + count
            it.setIntConfig(OCR_EMBEDDING_SCAN_COUNT_ALL, scanCountAll)
            GalleryScanUtils.recordInfo(mContext, TAG, scannedCountIn24hours.toLong(), scanCountAll.toLong())
        }
    }

    /**
     * 是否是支持的文件类型。目前只支持图片
     */
    private fun isSupportMediaType(mMediaType: Int): Boolean {
        return mMediaType == FileColumns.MEDIA_TYPE_IMAGE
    }

    /**
     * BaseImageInfo转成OcrEmbeddingImageInfo
     */
    private fun format2OcrEmbeddingImageInfo(baseImageInfo: BaseImageInfo): OcrEmbeddingImageInfo {
        val info = OcrEmbeddingImageInfo()
        info.mGalleryId = baseImageInfo.mGalleryId
        info.mMediaId = baseImageInfo.mMediaId
        info.mMediaType = baseImageInfo.mMediaType
        info.mDateModifiedInSec = baseImageInfo.mDateModifiedInSec
        info.mDateTaken = baseImageInfo.mDateTaken
        info.mFilePath = baseImageInfo.mFilePath
        info.mInvalid = baseImageInfo.mInvalid
        info.mIsRecycled = baseImageInfo.mIsRecycled
        info.mNewMediaId = baseImageInfo.mNewMediaId
        info.mNewFilePath = baseImageInfo.mNewFilePath
        info.mOrientation = baseImageInfo.mOrientation
        info.mSize = baseImageInfo.mSize
        info.mExtTagFlags = baseImageInfo.mExtTagFlags
        info.mScanState = baseImageInfo.mScanState
        return info
    }

    companion object {
        private const val TAG = "${ScanConst.TAG}OcrEmbeddingScanner"
        private const val SCENE_NAME = "OcrEmbeddingScanner"

        /**
         * 单次扫描的数量 30张
         */
        private const val ONE_SCAN_COUNT = 30

        /**
         * 每次扫描间隔200毫秒
         */
        private const val SLEEP_TIME_EACH_BACH = 200
    }
}