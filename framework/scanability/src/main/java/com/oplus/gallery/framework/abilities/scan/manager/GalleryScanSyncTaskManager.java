/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryScanSyncTaskManager.java
 ** Description:
 **     GalleryScanSyncTaskManager，集中管理各个扫描，与soloop的互斥等。
 **
 ** Version: 1.0
 ** Date: 2018/3/8
 ** Author: hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 * *  hailong.zhang@Apps.Gallery3D    2018/3/8    1.0    build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.manager;

import static com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.Value.SCHEDULE_JOB_SCAN_TRIGGER;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_CAPTION;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_MULTIMODAL;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OCREMBEDDING;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PET_CLASSIFY;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_STUDY_CLASSIFY;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V2;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_TRAVEL_CLASSIFY;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_LOCATE_STATUS;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_TEXT_OCR_SERVICE;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_TRAVEL_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.AUTO_CROP_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.CAPTION_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.CLOUD_SYNC_SLIMMING_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.DMP_SYNC_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.EDIT_INFO_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.FACE_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.FLAW_SCANNER;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.HD_FACE_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.HIGHLIGHT_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.LABEL_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.MEDIA_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.MEMORIES_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.MODEL_DOWNLOAD_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.MULTI_MODAL_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.OCR_EMBEDDING_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.OCR_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.O_CLOUD_SYNC_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.PERSON_PET_GROUP_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.PET_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.POI_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.SENIOR_SELECT_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.STUDY_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.TRAVEL_COVER_SCANNER;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.TRAVEL_SCAN;
import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScan.XQIP_CACHE_SCAN;

import android.content.Context;
import android.os.Debug;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;

import com.heytap.cloud.sdk.utils.StaticHandler;
import com.oplus.breakpad.BreakpadMaster;
import com.oplus.gallery.addon.app.OplusWhiteListManagerWrapper;
import com.oplus.gallery.addon.osense.LongBgTaskQuotaManager;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.foundation.networkaccess.other.NonNull;
import com.oplus.gallery.foundation.taskscheduling.monitor.GalleryMonitor;
import com.oplus.gallery.foundation.taskscheduling.monitor.IMonitorStrategy;
import com.oplus.gallery.foundation.taskscheduling.monitor.MemoryMonitorStrategy;
import com.oplus.gallery.foundation.taskscheduling.monitor.debug.MemoryMonitorStrategyForDebug;
import com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant;
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle;
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.systemcore.ActivityManagerUtils;
import com.oplus.gallery.foundation.util.systemcore.PowerManagerUtils;
import com.oplus.gallery.foundation.util.systemcore.WakeLockUtil;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.framework.abilities.config.IConfigAbility;
import com.oplus.gallery.framework.abilities.download.IDownloadAbility;
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility;
import com.oplus.gallery.framework.abilities.scan.ScanConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.autocrop.AutoCropScanner;
import com.oplus.gallery.framework.abilities.scan.caption.CaptionScanner;
import com.oplus.gallery.framework.abilities.scan.edit.EditInfoScanner;
import com.oplus.gallery.framework.abilities.scan.face.FaceScanner;
import com.oplus.gallery.framework.abilities.scan.face.HDFaceScanner;
import com.oplus.gallery.framework.abilities.scan.flaw.FlawScanner;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.label.LabelScanner;
import com.oplus.gallery.framework.abilities.scan.media.MediaScanner;
import com.oplus.gallery.framework.abilities.scan.memories.MemoriesScanner;
import com.oplus.gallery.framework.abilities.scan.modeldownload.ModelDownloadScanner;
import com.oplus.gallery.framework.abilities.scan.multimodal.MultiModalScanner;
import com.oplus.gallery.framework.abilities.scan.ocloudsync.OCloudSyncScanner;
import com.oplus.gallery.framework.abilities.scan.ocr.OcrEmbeddingScanner;
import com.oplus.gallery.framework.abilities.scan.ocr.OcrScanner;
import com.oplus.gallery.framework.abilities.scan.personpet.PersonPetGroupScanner;
import com.oplus.gallery.framework.abilities.scan.pet.PetScanner;
import com.oplus.gallery.framework.abilities.scan.poi.PoiScanner;
import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectScanner;
import com.oplus.gallery.framework.abilities.scan.slimming.CloudSyncSlimmingScanner;
import com.oplus.gallery.framework.abilities.scan.study.StudyScanner;
import com.oplus.gallery.framework.abilities.scan.travel.TravelCoverScanner;
import com.oplus.gallery.framework.abilities.scan.travel.TravelScanner;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.framework.abilities.scan.xqip.XqipCacheScanner;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.scheduler.JobServiceIdPool;
import com.oplus.gallery.standard_lib.scheduler.KeepAliveUtil;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import org.jetbrains.annotations.TestOnly;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public enum GalleryScanSyncTaskManager {
    INSTANCE;
    private static final String TAG = ScanConst.TAG + "GalleryScanSyncTaskManager";
    private static final String SCANNER_THREAD_NAME = "GalleryScanSyncThread";
    private static final String SCENE_SCANNER_RUNNING = "ScannerRunning";
    private static final String SCENE_CREATE_SCANNER_EMPTY = "CreateScannerEmpty";
    private static final String SCENE_WRONG_SCANNER_TYPE = "WrongScannerType";
    private static final String SCENE_OVER_MEMORY_SIZE = "OverMemorySize";
    private static final String SCENE_RUN_SCANNER_DISABLE = "RunScannerDisable";
    private static final String SCENE_RUN_SCANNER_EXCEPTION = "RunScannerException";
    private static final int SCANNER_THREAD_PRIPORTY = Thread.NORM_PRIORITY - 2;
    private static final int DELAY_FORCE_STOP_GALLERY = 15000;
    private static final int IMAGE_ONCE_SCAN_COUNT = GalleryScanMonitor.getCloudImageOnceScanCount();
    private static final int VIDEO_ONCE_SCAN_COUNT = GalleryScanMonitor.getCloudVideoOnceScanCount();
    private static final int LOOP = 3;
    /**
     * 首批次扫描模块
     */
    private static final int FIRST_SCAN_TYPE = LABEL_SCAN
            | SENIOR_SELECT_SCAN
            | AUTO_CROP_SCAN
            | MEDIA_SCAN
            | MODEL_DOWNLOAD_SCAN
            | EDIT_INFO_SCAN;

    /**
     * 循环批次扫描模块
     */
    private static final int LOOP_SCAN_TYPE = FACE_SCAN
            | PET_SCAN
            | LABEL_SCAN
            | SENIOR_SELECT_SCAN
            | AUTO_CROP_SCAN
            | OCR_SCAN
            | MEMORIES_SCAN
            | HIGHLIGHT_SCAN
            | MULTI_MODAL_SCAN
            | DMP_SYNC_SCAN
            | XQIP_CACHE_SCAN
            | FLAW_SCANNER
            | OCR_EMBEDDING_SCAN
            | HD_FACE_SCAN
            | POI_SCAN
            | TRAVEL_SCAN
            | STUDY_SCAN
            | TRAVEL_COVER_SCANNER
            | CAPTION_SCAN;

    /**
     * check other scanner list
     */
    private static final ArrayList<String> OTHER_SCANNERS = new ArrayList<String>() {
        {
            add(AppBrandConstants.Package.getPACKAGE_NAME_SOLOOP());
        }
    };

    /**
     * 首批次，先扫描标签、精选、智能裁剪
     * 循环批次：全扫,每批次每种素材最多只扫500张
     * 默认批次：等价原全扫流程，兜底
     */
    private static final ArrayList<ScanConfig> SCAN_CONFIGS = new ArrayList<ScanConfig>() {
        {
            ScanConfig firstConfig = new ScanConfig(
                    FIRST_SCAN_TYPE,
                    IMAGE_ONCE_SCAN_COUNT,
                    VIDEO_ONCE_SCAN_COUNT,
                    false
            );
            ScanConfig loopConfig = new ScanConfig(
                    LOOP_SCAN_TYPE,
                    IMAGE_ONCE_SCAN_COUNT,
                    VIDEO_ONCE_SCAN_COUNT,
                    false
            );
            ScanConfig defaultConfig = new ScanConfig();
            add(firstConfig);
            for (int i = 0; i < LOOP; i++) {
                add(loopConfig);
            }
            add(defaultConfig);
        }
    };

    /**
     * 允许前端触发扫描的类型
     */
    private static final Set<Integer> FOREGROUND_TRIGGER_WHITELIST = Set.of(
            ScanTrackConstant.Value.AI_BEST_TAKE_SCAN_TRIGGER
    );

    private final Context mContext;
    /**
     * work handler
     */
    private Handler mWorkHandler;
    /**
     * use for memory check.
     */
    private long mStartNativeHeapFreeSize;
    /**
     * if need to scan when gallery on background.
     */
    private boolean mNeedRescan;
    /**
     * current scan task.
     */
    private ArrayList<GalleryScan> mScanners;
    /**
     * which type is scanning
     */
    private int mScanType = GalleryScan.INVALID;

    private volatile int mScanProgress = GalleryScan.INVALID;

    /**
     * 是否允许在相册运行在前台时扫描
     */
    private boolean mCanScanWhenGalleryRunOnTop = false;

    /**
     * 扫描进度收集器
     */
    private GalleryScanProgressCollector mGalleryScanProgressCollector;

    /**
     * 自定义扫描配置
     */
    private ScanConfig mScanConfig;

    GalleryScanSyncTaskManager() {
        mContext = ContextGetter.context;
        HandlerThread ht = new HandlerThread(SCANNER_THREAD_NAME);
        ht.setPriority(SCANNER_THREAD_PRIPORTY);
        ht.start();
        if (ht.getLooper() == null) {
            return;
        }
        mWorkHandler = new WorkHandler(this, ht.getLooper());
        initBreakPad();
        GLog.d(TAG, LogFlag.DL, "GalleryScanSyncTaskManager INSTANCE");
    }

    public void destroy() {
        if (mWorkHandler != null) {
            mWorkHandler.getLooper().quitSafely();
            mWorkHandler = null;
        }
    }

    @TestOnly
    public void doFaceDataScan(int triggerType) {
        GLog.d(TAG, LogFlag.DL, "doFaceDataScan, FACE_SCAN, triggerType:" + triggerType);
        sendScanMsg(triggerType, FACE_SCAN);
    }

    /**
     * 前台触发人脸扫描（目前AI最佳表情业务在用）
     *
     * @param triggerType 触发类型
     * @param scanConfig  扫描配置
     */
    public void doFaceScan(int triggerType, @NonNull ScanConfig scanConfig) {
        // 前台扫描确保每次开启的时候可以正常开启，如果上次扫描被取消了，mInterruptReason会一直是中断状态，所以我们每次开启的时候先恢复默认值
        interruptScan(GalleryScanMonitor.ReasonType.REASON_OK);

        // 允许在相册运行在前台时扫描
        synchronized (GalleryScanSyncTaskManager.this) {
            if (isAllowForegroundTrigger(triggerType)) {
                // 配置扫描进度收集器，目前只有人脸和高清人脸
                mGalleryScanProgressCollector = new GalleryScanProgressCollector(Set.of(CollectorScanType.FACE, CollectorScanType.HD_FACE));
                // 允许前台触发扫描
                mCanScanWhenGalleryRunOnTop = true;
                // 当次扫描使用的扫描配置
                mScanConfig = scanConfig;
            }
        }
        GLog.d(TAG, LogFlag.DL, "doDataScan , triggerType:" + triggerType + " , scanConfigType = " + scanConfig.getType());
        sendScanMsg(triggerType, scanConfig.getType());
    }

    public void doAllDataScan(int triggerType) {
        GLog.d(TAG, LogFlag.DL, "doAllDataScan, triggerType:" + triggerType + ", isScannerIdle:" + isScannerIdle());
        if (isScannerIdle()) {
            GLog.d(TAG, LogFlag.DL, "doAllDataScan, ALL_SCAN_TYPE, triggerType:" + triggerType);
            sendScanMsg(triggerType, GalleryScan.ALL_SCAN_TYPE);
        } else {
            // 扫描已经在运行中，不需要再次启动，埋点记录。
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_RUNNING, triggerType);
            GLog.w(TAG, LogFlag.DL, "doAllDataScan, scanner is not idle, triggerType:" + triggerType + ", mScanType:" + mScanType);
        }
    }

    public boolean isScannerIdle() {
        return (mScanType == GalleryScan.INVALID);
    }

    /**
     * 更新扫描进度
     * @param progress 扫描进度条
     */
    public void updateScanProgress(int progress) {
        mScanProgress = progress;
    }

    /**
     * 扫描的进度条
     * @return 扫描的进度条
     */
    public int getScanProgress() {
        return mScanProgress;
    }

    public void interruptScan(@GalleryScanMonitor.ReasonType int reason) {
        // 允许相册在前台时扫描，亮屏不中断扫描任务,目前这里只有亮屏会触发，其它情况不会触发。
        synchronized (GalleryScanSyncTaskManager.this) {
            if (mCanScanWhenGalleryRunOnTop && (GalleryScanMonitor.ReasonType.REASON_SCREEN_ON == reason)) {
                GLog.w(TAG, LogFlag.DL, "interruptScan: not allow to interrupt when screen on, "
                        + "mCanScanWhenGalleryRunOnTop is true.");
                return;
            }
            mScanType = GalleryScan.INVALID;
            GLog.d(TAG, LogFlag.DL, "interruptScan reason:" + reason);
            if ((mScanners != null) && !mScanners.isEmpty()) {
                for (GalleryScan scan : mScanners) {
                    scan.onInterrupt(reason);
                }
                mNeedRescan = true;
            }
        }
    }

    /**
     * 是否允许相册前台触发的扫描，目前包含以下业务场景
     * 1、AI表情优化扫描
     *
     * @param triggerType 扫描触发类型
     * @return true/false 是否允许在前台触发扫描
     */
    private boolean isAllowForegroundTrigger(int triggerType) {
        return FOREGROUND_TRIGGER_WHITELIST.contains(triggerType);
    }

    private void initBreakPad() {
        GLog.d(TAG, LogFlag.DL, "initBreakPad");
        BreakpadMaster.INSTANCE.initializeOnNonUiThread();
        BreakpadMaster.INSTANCE.setCachedFile(GalleryScanUtils.getCrashFileCached(mContext));
        BreakpadMaster.INSTANCE.setRename(false);
        GLog.d(TAG, LogFlag.DL, "initBreakPad end!");
    }

    private void sendScanMsg(int triggerType, int scanType) {
        if (mWorkHandler != null) {
            GLog.d(TAG, LogFlag.DL, "sendScanMsg MSG_DATA_SCAN, triggerType:" + triggerType + ", scanType:" + scanType);
            mWorkHandler.removeMessages(WorkHandler.MSG_DATA_SCAN);
            mWorkHandler.sendMessageDelayed(mWorkHandler.obtainMessage(WorkHandler.MSG_DATA_SCAN, triggerType, scanType),
                    WorkHandler.SCAN_DELAY);
        }
    }

    private void doMemoryCheckAndKill() {
        boolean hasNoMessagesInHandler = hasNoMessagesInHandler();
        GLog.d(TAG, LogFlag.DL, "doMemoryCheck hasNoMessagesInHandler:" + hasNoMessagesInHandler);
        if (hasNoMessagesInHandler) {
            boolean isModelDownloadIdle = true;
            IDownloadAbility downloadAbility = ((GalleryApplication) mContext).getAppAbility(IDownloadAbility.class);
            if (downloadAbility != null) {
                isModelDownloadIdle = downloadAbility.isDownloadIdle();
            }
            IOUtils.closeQuietly(downloadAbility);
            if (!isModelDownloadIdle) {
                GLog.w(TAG, LogFlag.DL, "doMemoryCheck, isModelDownloadIdle, not kill gallery!");
                LongBgTaskQuotaManager.INSTANCE.remove(mContext, TAG + ".doMemoryCheckAndKill");
                return;
            }
            if (GalleryScanMonitor.isScreenOnAndKeyguardUnlocked()) {
                GLog.w(TAG, LogFlag.DL, "doMemoryCheck, user use phone, not kill gallery!");
                LongBgTaskQuotaManager.INSTANCE.remove(mContext, TAG + ".doMemoryCheckAndKill");
                return;
            }
            boolean needKillGallery = needKillGallery(mStartNativeHeapFreeSize, Debug.getNativeHeapFreeSize());
            GLog.w(TAG, LogFlag.DL, "doMemoryCheck, needKillGallery:" + needKillGallery);
            mStartNativeHeapFreeSize = 0;
            if (needKillGallery) {
                GLog.w(TAG, LogFlag.DL, "doMemoryCheck, nativeHeapFreeSize increase too much, we need kill gallery!");
                ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_OVER_MEMORY_SIZE, SCHEDULE_JOB_SCAN_TRIGGER);
                ActivityManagerUtils.forceStopGallery(mContext);
            }
        }
        LongBgTaskQuotaManager.INSTANCE.remove(mContext, TAG + ".doMemoryCheckAndKill end");
    }

    private boolean needKillGallery(long startNativeHeapSize, long endNativeHeapSize) {
        GLog.d(TAG, LogFlag.DL, "needKillGallery startNativeHeapSize:"
                + startNativeHeapSize + ", endNativeHeapSize:" + endNativeHeapSize);
        return !ActivityLifecycle.isRunningForeground() && (startNativeHeapSize != 0)
                && ((endNativeHeapSize - startNativeHeapSize) >= GalleryScanUtils.NATIVE_HEAP_FREE_SIZE_INCREASE_MAX);
    }

    private boolean hasNoMessagesInHandler() {
        return (mWorkHandler == null) || !mWorkHandler.hasMessages(WorkHandler.MSG_DATA_SCAN);
    }

    private void runScanTask(GalleryScan galleryScan, int triggerType, ScanConfig config) {
        try {
            mScanType = galleryScan.getScanType();
            int configScanType = config.getType();
            GLog.d(TAG, LogFlag.DL, "runScanTask, start"
                    + ", triggerType=" + triggerType
                    + ", mScanType=" + mScanType
                    + ", hex:" + Integer.toHexString(mScanType)
                    + ", desc=" + GalleryScan.getScanTypeDesc(mScanType)
                    + ", configScanType=" + configScanType
                    + ", hex:" + Integer.toHexString(configScanType)
                    + ", (mScanType & configScanType)=" + (mScanType & configScanType)
                    + ", config=" + config);
            if ((mScanType & configScanType) != mScanType) {
                GLog.w(TAG, LogFlag.DL, "runScanTask, current config no need run " + mScanType);
                // 扫描中断，记录原因
                ScanTrackHelper.trackStopReasonOnStart(
                        mContext,
                        SCENE_WRONG_SCANNER_TYPE + TextUtil.DOWN_LINE + galleryScan.getSceneName(),
                        triggerType
                );
                notifyCollector(mScanType, GalleryScanMonitor.ReasonType.REASON_SYS_INTERRUPT, null);
                return;
            }

            if (!isScanEnable(galleryScan)) {
                GLog.w(TAG, LogFlag.DL, "runScanTask isScanEnable = false");
                // 扫描中断，记录原因
                ScanTrackHelper.trackStopReasonOnStart(
                        mContext,
                        SCENE_RUN_SCANNER_DISABLE + TextUtil.DOWN_LINE + galleryScan.getSceneName(),
                        triggerType,
                        galleryScan.getLastScanTime(),
                        true
                );
                notifyCollector(galleryScan.getScanType(), GalleryScanMonitor.ReasonType.REASON_UNDEFINED, mContext);
                return;
            }

            if (mStartNativeHeapFreeSize == 0) {
                mStartNativeHeapFreeSize = Debug.getNativeHeapFreeSize();
            }

            long time = System.currentTimeMillis();
            galleryScan.onScan(triggerType, config);
            if (galleryScan.isInterrupt()) {
                mNeedRescan = true;
            }
            GLog.d(TAG, LogFlag.DL, "runScanTask, end. cost time = " + (System.currentTimeMillis() - time));
        } catch (Exception e) {
            ScanTrackHelper.trackStopReasonOnStart(
                    mContext,
                    SCENE_RUN_SCANNER_EXCEPTION + TextUtil.DOWN_LINE + mScanType,
                    triggerType
            );
            GLog.e(TAG, LogFlag.DL, "runScanTask, e:" + e.getMessage());
        }
    }

    /**
     * 通知采集器
     *
     * @param scanType 扫描类型
     * @param reason   中断原因
     * @param context  上下文
     */
    private void notifyCollector(int scanType, int reason, Context context) {
        if (mGalleryScanProgressCollector != null) {
            mGalleryScanProgressCollector.onInterruptScan(scanType, reason, context);
        }
    }

    /**
     * 检查扫描条件：
     * 电量、温度、息屏、已锁屏、扫描用时、上一次扫描时间、前台应用
     */
    private Boolean isScanEnable(GalleryScan galleryScan) {
        // 在简易模式下，为方便测试，将跳过所有后台扫描条件，如充电状态、当前电量、是否锁屏等。
        if (!GalleryScanUtils.isNormalScanPolicy()) {
            return true;
        }
        synchronized (GalleryScanSyncTaskManager.this) {
            // 相册前台业务发起的扫描,运行亮屏是进行扫描
            if (!GalleryScanMonitor.isAllowStartScan(mContext, galleryScan.runTaskIsNeedCharging(), mCanScanWhenGalleryRunOnTop)) {
                GLog.w(TAG, LogFlag.DL, "runScanTask, stop scanning because not allow start scan.");
                return false;
            }

            if (!GalleryScanMonitor.isCostTimeAllowScan(mContext)) {
                GLog.w(TAG, LogFlag.DL, "runScanTask, stop scanning because it has been scanning continuously for two hours without charging.");
                return false;
            }
            // 相册前台业务发起的扫描不限制
            if (Boolean.TRUE.equals(GalleryScanMonitor.isLastScanWithinOneHour(galleryScan.getLastScanTime())) && !mCanScanWhenGalleryRunOnTop) {
                GLog.w(TAG, LogFlag.DL, "runScanTask, stop scanning because last scanning has executed within one hour,"
                        + " we give up this scan to avoid endless loop!");
                return false;
            }
            // 相册前台业务发起的扫描不限制
            if (GalleryScanMonitor.isGalleryRunOnTop() && !mCanScanWhenGalleryRunOnTop) {
                GLog.w(TAG, LogFlag.DL, "runScanTask, stop scanning because gallery app is in the foreground.");
                mNeedRescan = true;
                return false;
            }
        }
        return true;
    }

    private void handleScanMsg(int triggerType, int scanType) {
        mNeedRescan = false;
        long time = System.currentTimeMillis();
        GLog.w(TAG, LogFlag.DL, "handleScanMsg triggerType =" + triggerType + ",scanType =" + scanType);
        if (isPackagesInStageProtectList(OTHER_SCANNERS)) {
            GLog.w(TAG, LogFlag.DL, "handleScanMsg is other scanner running, return. "
                    + "triggerType =" + triggerType + ", scanType =" + scanType);
            ScanTrackHelper.trackOtherScannerProtectStatistics(System.currentTimeMillis(), triggerType);
            notifyCollector(scanType, GalleryScanMonitor.ReasonType.REASON_SYS_INTERRUPT, null);
            return;
        }
        ArrayList<GalleryScan> scanners = createScanners(scanType);
        if ((scanners == null) || scanners.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "handleScanMsg no support scanner so return");
            // 扫描中断，记录原因
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_CREATE_SCANNER_EMPTY, triggerType);
            notifyCollector(scanType, GalleryScanMonitor.ReasonType.REASON_SYS_INTERRUPT, null);
            return;
        }
        synchronized (GalleryScanSyncTaskManager.this) {
            mScanners = scanners;
        }
        mScanType = GalleryScan.INVALID;
        GalleryScanMonitor.resetScanStartTime();
        GalleryScanMonitor.resetScanOnTopStartTime();
        GalleryScanMonitor.initialScanTimeInfo24h(mContext);
        GalleryScanMonitor.refreshScanRecordingIfNecessary(mContext);
        GalleryScanMonitor.setStartBatteryPct(mContext);
        GalleryScanMonitor.refreshScanTaskStartTime(mContext);
        WakeLockUtil.acquireWakeLock(mContext, TAG);
        GalleryMonitor galleryMonitor = new GalleryMonitor();
        IMonitorStrategy monitorStrategy = new MemoryMonitorStrategy();
        if (GalleryMonitor.DEBUG) {
            monitorStrategy = new MemoryMonitorStrategyForDebug();
            galleryMonitor.setMonitorInterval(MemoryMonitorStrategyForDebug.DEBUG_MONITOR_INTERVAL);
        }
        galleryMonitor.addMonitorStrategy(monitorStrategy);
        try {
            // start Pulse service for keep scanning, delay 1s to stop self by service
            if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN)) {
                KeepAliveUtil.keepGalleryAlive(JobServiceIdPool.GALLERY_SCAN_SERVICE.getJobId());
            }
            PowerManagerUtils.addStageProtect(mContext);
            if (!scanners.isEmpty()) {
                galleryMonitor.start();
            }
            List<ScanConfig> configList = new ArrayList<>();
            synchronized (GalleryScanSyncTaskManager.this) {
                if (isAllowForegroundTrigger(triggerType)) {
                    if (mScanConfig != null) {
                        configList = List.of(mScanConfig);
                    } else {
                        GLog.w(TAG, LogFlag.DL, "triggerType is from front but mScanConfig is null , triggerType" + triggerType);
                    }
                } else {
                    configList = SCAN_CONFIGS;
                }
            }
            for (ScanConfig config : configList) {
                GLog.d(TAG, LogFlag.DL, "handleScanMsg scanConfig:" + config + ", triggerType:" + triggerType);
                for (GalleryScan scan : scanners) {
                    monitorStrategy.registerRecordListener(scan);
                    runScanTask(scan, triggerType, config);
                    monitorStrategy.unregisterRecordListener(scan);
                }
            }
        } catch (Exception e) {
            /*
            扫描中断，记录原因。如果某一个 scan 扫描出现异常，就会影响它后面的扫描任务，无法扫描。
            异常应该由每次 runScanTask 来捕获，不应该由manager统一捕获。
            */
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_RUN_SCANNER_EXCEPTION, triggerType);
            notifyCollector(scanType, GalleryScanMonitor.ReasonType.REASON_SYS_INTERRUPT, null);
            GLog.w(TAG, LogFlag.DL, "handleScanMsg, e:" + e);
        } finally {
            synchronized (GalleryScanSyncTaskManager.this) {
                mScanners = null;
                mScanConfig = null;
                mGalleryScanProgressCollector = null;
                mCanScanWhenGalleryRunOnTop = false;
            }
            galleryMonitor.removeMonitorStrategy(monitorStrategy);
            galleryMonitor.stop();
            mScanType = GalleryScan.INVALID;
            WakeLockUtil.releaseWakeLock();
            PowerManagerUtils.removeStageProtect(mContext);
            KeepAliveUtil.stopKeepAlive(JobServiceIdPool.GALLERY_SCAN_SERVICE.getJobId());
            /*
             防止当前的 ScanPulseService 已经启动，但是还没运行到 startForeground 就被 kill 了，此时如果 forceStopGallery 会报
             RemoteServiceException，所以延迟 15 秒执行，提供更多时间等待 startForeground 被执行到，解决掉手动 stop 导致的 RemoteServiceException
             而如果是 startForegroundService 之后 onCreate/onStartCommand/startForeground 出问题了没 startForeground 成功，10s 后会报 ANR 而不会
             再看到 forceStopGallery 的相关日志，此时可能是系统原因或者其他，再进一步分析。
             */
            LongBgTaskQuotaManager.INSTANCE.add(mContext, TAG + ".handleScanMsg post delayed");
            mWorkHandler.postDelayed(this::doMemoryCheckAndKill, DELAY_FORCE_STOP_GALLERY);
            GalleryScanService.finishJob(mContext, mNeedRescan);
        }
        GLog.w(TAG, LogFlag.DL, "handleScanMsg, end. cost time=" + (System.currentTimeMillis() - time));
    }

    private ArrayList<GalleryScan> createScanners(int scanTypes) {
        StringBuilder logBuilder = new StringBuilder();
        ArrayList<GalleryScan> scanners = null;
        scanTypes = scanTypes & GalleryScanUtils.SCAN_TYPES;
        if (scanTypes != GalleryScan.INVALID) {
            scanners = new ArrayList<>();
            // add local media scan
            if ((scanTypes & MEDIA_SCAN) == MEDIA_SCAN) {
                scanners.add(new MediaScanner(mContext));
                logBuilder.append("MEDIA_SCAN, ");
            }
            // add OCloudSyncScanner scan
            if ((scanTypes & O_CLOUD_SYNC_SCAN) == O_CLOUD_SYNC_SCAN) {
                scanners.add(new OCloudSyncScanner(mContext));
                logBuilder.append("O_CLOUD_SYNC_SCAN, ");
            }
            // add modelDownload scan
            if ((scanTypes & MODEL_DOWNLOAD_SCAN) == MODEL_DOWNLOAD_SCAN) {
                scanners.add(new ModelDownloadScanner(mContext));
                logBuilder.append("MODEL_DOWNLOAD_SCAN, ");
            }
            // add label scan
            if ((scanTypes & LABEL_SCAN) == LABEL_SCAN) {
                scanners.add(new LabelScanner(mContext));
                logBuilder.append("LABEL_SCAN, ");
            }
            // add senior select scan
            if ((scanTypes & SENIOR_SELECT_SCAN) == SENIOR_SELECT_SCAN) {
                scanners.add(new SeniorSelectScanner(mContext));
                logBuilder.append("SENIOR_SELECT_SCAN, ");
            }
            // add auto crop scan
            if ((scanTypes & AUTO_CROP_SCAN) == AUTO_CROP_SCAN) {
                scanners.add(new AutoCropScanner(mContext));
                logBuilder.append("AUTO_CROP_SCAN, ");
            }
            // add travel scan
            if (((scanTypes & TRAVEL_SCAN) == TRAVEL_SCAN)
                    && isScanAuthorized(AUTHORIZE_TRAVEL_SCAN)
                    && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_TRAVEL_CLASSIFY)) {
                scanners.add(new TravelScanner(mContext));
                logBuilder.append("TRAVEL_SCAN, ");
            }
            synchronized (GalleryScanSyncTaskManager.this) {
                // add face scan
                if (((scanTypes & FACE_SCAN) == FACE_SCAN)
                        && isScanAuthorized(AUTHORIZE_FACE_SCAN)) {
                    scanners.add(new FaceScanner(mContext, mGalleryScanProgressCollector, mCanScanWhenGalleryRunOnTop));
                    logBuilder.append("FACE_SCAN, ");
                }
            }
            if (((scanTypes & TRAVEL_COVER_SCANNER) == TRAVEL_COVER_SCANNER)
                    && isScanAuthorized(AUTHORIZE_TRAVEL_SCAN)
                    && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_TRAVEL_CLASSIFY)) {
                scanners.add(new TravelCoverScanner(mContext));
                logBuilder.append("TRAVEL_COVER_SCAN, ");
            }
            // add pet scan 宠物扫描
            if (((scanTypes & PET_SCAN) == PET_SCAN)
                    && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY)) {
                scanners.add(new PetScanner(mContext));
                logBuilder.append("PET_SCAN, ");
            }
            // add person pet group scanner 人宠合照扫描
            if (((scanTypes & PERSON_PET_GROUP_SCAN) == PERSON_PET_GROUP_SCAN)
                    && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY)) {
                scanners.add(new PersonPetGroupScanner(mContext));
                logBuilder.append("PERSON_PET_GROUP_SCAN, ");
            }
            // add multi modal scan
            if (((scanTypes & MULTI_MODAL_SCAN) == MULTI_MODAL_SCAN)
                    && isSupportMultiModal()) {
                scanners.add(new MultiModalScanner(mContext));
                logBuilder.append("MULTI_MODAL_SCAN, ");
            }
            // add ocr scan
            if (((scanTypes & OCR_SCAN) == OCR_SCAN)
                    && checkOCRScanAuthorizedIfNeed()) {
                scanners.add(new OcrScanner(mContext));
                logBuilder.append("OCR_SCAN, ");
            }
            // add ocr embedding scan
            if ((scanTypes & OCR_EMBEDDING_SCAN) == OCR_EMBEDDING_SCAN
                    && isSupportOcrEmbedding()) {
                scanners.add(new OcrEmbeddingScanner(mContext));
                logBuilder.append("OCR_EMBEDDING_SCAN, ");
            }
            // add XqipCache scan
            if ((scanTypes & XQIP_CACHE_SCAN) == XQIP_CACHE_SCAN) {
                scanners.add(new XqipCacheScanner(mContext));
                logBuilder.append("createScanners: xqip cache will scan");
            }
            // add memories scan
            if ((scanTypes & MEMORIES_SCAN) == MEMORIES_SCAN) {
                scanners.add(new MemoriesScanner(mContext));
                logBuilder.append("MEMORIES_SCAN, ");
            }
            // add edit info scanner
            if ((scanTypes & EDIT_INFO_SCAN) == EDIT_INFO_SCAN) {
                scanners.add(new EditInfoScanner(mContext));
                logBuilder.append("EDIT_INFO_SCAN, ");
            }
            if ((scanTypes & FLAW_SCANNER) == FLAW_SCANNER) {
                scanners.add(new FlawScanner(mContext));
                logBuilder.append("FLAW_SCANNER, ");
            }
            // add CloudSyncSlimmingScanner scan 瘦身功能搬迁到扫描中增加
            if ((scanTypes & CLOUD_SYNC_SLIMMING_SCAN) == CLOUD_SYNC_SLIMMING_SCAN) {
                scanners.add(new CloudSyncSlimmingScanner(mContext));
                logBuilder.append("CLOUD_SYNC_SLIMMING_SCAN, ");
            }
            synchronized (GalleryScanSyncTaskManager.this) {
                // add HD face scan
                if (((scanTypes & HD_FACE_SCAN) == HD_FACE_SCAN)
                        && isScanAuthorized(AUTHORIZE_FACE_SCAN)) {
                    scanners.add(new HDFaceScanner(mContext, mGalleryScanProgressCollector, mCanScanWhenGalleryRunOnTop));
                    logBuilder.append("HD FACE_SCAN, ");
                }
            }
            // add PoiGridScanner scan 相册额外地址扫描
            if ((scanTypes & POI_SCAN) == POI_SCAN) {
                scanners.add(new PoiScanner(mContext));
                logBuilder.append("POI_GRID_SCAN, ");
            }
            // add study scanner 学习分类扫描
            if (((scanTypes & STUDY_SCAN) == STUDY_SCAN)
                    && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_STUDY_CLASSIFY)) {
                scanners.add(new StudyScanner(mContext));
                logBuilder.append("STUDY_SCAN");
            }
            // add caption scan
            if (((scanTypes & CAPTION_SCAN) == CAPTION_SCAN)
                    && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_CAPTION)) {
                scanners.add(new CaptionScanner(mContext));
                logBuilder.append("CAPTION_SCAN, ");
            }
        }
        if (GProperty.DEBUG) {
            GLog.d(TAG, LogFlag.DL, "createScanners: scanTypes:" + Integer.toBinaryString(scanTypes)
                    + ", SCAN_MODELS:" + Integer.toBinaryString(GalleryScanUtils.SCAN_TYPES)
                    + ", scanners: " + logBuilder);
        }
        return scanners;
    }

    /**
     * 是否支持多模态
     */
    private boolean isSupportMultiModal() {
        if (mContext instanceof GalleryApplication) {
            IConfigAbility configAbility = ((GalleryApplication) mContext)
                    .getAppAbility(IConfigAbility.class);
            if (configAbility != null) {
                Boolean isSupport = configAbility.getBooleanConfig(
                        FEATURE_IS_SUPPORT_MULTIMODAL,
                        false
                );
                configAbility.close();
                if (Boolean.TRUE.equals(isSupport)) {
                    GLog.d(TAG, LogFlag.DL, "isSupportMultiModal, is support");
                    return true;
                }
            }
            GLog.d(TAG, LogFlag.DL, "isSupportMultiModal, is not support");
            return false;
        }
        return false;
    }

    private boolean isScanAuthorized(String key) {
        IPrivacyAuthorizingAbility ability = ((GalleryApplication) mContext)
                .getAppAbility(IPrivacyAuthorizingAbility.class);
        if (ability != null) {
            Boolean isPrivacyAuthorized = ability.isPrivacyAuthorized(key);
            IOUtils.closeQuietly(ability);
            if (isPrivacyAuthorized == null) {
                GLog.w(TAG, LogFlag.DL, "isScanAuthorized, isPrivacyAuthorized == null,return false.");
                return false;
            }
            return isPrivacyAuthorized;
        }
        // ability 为空，说明是外销，没有配置授权能力，返回true
        return true;
    }

    /**
     * 检查ocr扫描权限<br>
     * 1.支持超级文本2.0 则直接返回支持，无隐私问题<br>
     * 2.不支持超级文本2.0，则需要进行授权判断
     */
    private boolean checkOCRScanAuthorizedIfNeed() {
        IConfigAbility configAbility = ((GalleryApplication) mContext)
                .getAppAbility(IConfigAbility.class);
        if (configAbility != null) {
            Boolean isSupportSuperTextV2 = configAbility.getBooleanConfig(
                    FEATURE_IS_SUPPORT_SUPER_TEXT_V2,
                    false
            );
            configAbility.close();
            if (Boolean.TRUE.equals(isSupportSuperTextV2)) {
                //超级文本2.0算法在端，不涉及隐私权限
                GLog.d(TAG, LogFlag.DL, "checkOCRScanAuthorizedIfNeed, support super text v2");
                return true;
            }
        }
        GLog.d(TAG, LogFlag.DL, "checkOCRScanAuthorizedIfNeed, check super text v1");
        //不支持2.0，需要判断授权确认
        return isScanAuthorized(AUTHORIZE_TEXT_OCR_SERVICE);
    }

    /**
     * 是否支持OcrEmbedding
     */
    private boolean isSupportOcrEmbedding() {
        if (mContext instanceof GalleryApplication) {
            IConfigAbility configAbility = ((GalleryApplication) mContext)
                    .getAppAbility(IConfigAbility.class);
            if (configAbility != null) {
                Boolean isSupport = configAbility.getBooleanConfig(
                        FEATURE_IS_SUPPORT_OCREMBEDDING,
                        false
                );
                configAbility.close();
                if (Boolean.TRUE.equals(isSupport)) {
                    GLog.d(TAG, LogFlag.DL, "isSupportOcrEmbedding, is support");
                    return true;
                }
            }
            GLog.d(TAG, LogFlag.DL, "isSupportOcrEmbedding, is not support");
            return false;
        }
        return false;
    }

    private static class WorkHandler extends StaticHandler<GalleryScanSyncTaskManager> {
        static final int MSG_DATA_SCAN = 1;
        static final int SCAN_DELAY = 0;

        public WorkHandler(GalleryScanSyncTaskManager t, Looper looper) {
            super(t, looper);
        }

        @Override
        public void handleMessage(Message msg, GalleryScanSyncTaskManager t) {
            if (msg.what == MSG_DATA_SCAN) {
                LongBgTaskQuotaManager.INSTANCE.add(ContextGetter.context, TAG + "->WorkHandler.handleMessage");
                t.handleScanMsg(msg.arg1, msg.arg2);
                LongBgTaskQuotaManager.INSTANCE.remove(ContextGetter.context, TAG + "->WorkHandler.handleMessage");
            }
        }
    }

    private boolean isPackagesInStageProtectList(ArrayList<String> packageList) {
        GLog.d(TAG, LogFlag.DL, "isPackagesInStageProtectList, packageList = " + packageList);

        try {
            OplusWhiteListManagerWrapper owm = new OplusWhiteListManagerWrapper(mContext);
            for (String packageName : packageList) {
                ArrayList<String> list = owm.getStageProtectListFromPkg(packageName, 0);
                GLog.d(TAG, LogFlag.DL, "isPackagesInStageProtectList, list = " + list);
                if ((list != null) && !list.isEmpty()) {
                    if (list.contains(packageName)) {
                        GLog.d(TAG, LogFlag.DL, "isPackagesInStageProtectList, return true, packageName = " + packageName);
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "isPackagesInStageProtectList, error: ", e);
        }

        GLog.d(TAG, LogFlag.DL, "isPackagesInStageProtectList, return false");
        return false;
    }
}