/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PolarrRating.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2017/12/07
 ** Author:<PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Rom.Apps.Gallery         2017/12/07      1.0          build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.PointF;
import android.graphics.Rect;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils;
import com.oplus.gallery.foundation.exif.raw.ExifUtils;
import com.oplus.gallery.foundation.util.cpu.OifaceBindUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.graphic.ImageUtils;
import com.oplus.gallery.framework.abilities.caching.CacheOperation;
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility;
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory;
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions;
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation;
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult;
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceInfoMirror;
import com.oplus.gallery.framework.abilities.scan.face.IFaceDetector;
import com.oplus.gallery.framework.abilities.scan.face.IFaceScanAbility;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanPersonPetProviderHelper;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import co.polarr.processing.Processing;
import co.polarr.processing.entities.GroupingResultItem;
import co.polarr.processing.entities.ResultItem;
import co.polarr.tagging.probdet.TaggingUtil;


public class PolarrRating {
    private static final String TAG = "PolarrRating";
    private static final boolean DEBUG = false;
    /*private static final int TEST_FACE_DET_WIDTH = 720;
    private static final int TEST_FACE_DET_HEIGHT = 960;*/
    private static final int TAG_PHOTO_WIDTH = 224;
    private static final int TAG_PHOTO_HEIGHT = 224;
    private static final int MIN_RESULT = 15;
    private static final int MAX_RESULT = 40;
    private static volatile PolarrRating sPolarrRating;

    public static PolarrRating getInstance() {
        if (sPolarrRating == null) {
            synchronized (PolarrRating.class) {
                if (sPolarrRating == null) {
                    sPolarrRating = new PolarrRating();
                }
            }
        }
        return sPolarrRating;
    }

    public GroupingResult ratingGroup(Context context, List<MediaItem> imageList, int minResult, int maxResult) {
        GLog.d(TAG, "ratingGroup, size=" + imageList.size());

        GroupingResult groupingResult = grouping(context, imageList, false, false, minResult, maxResult);

        if (groupingResult == null) {
            return null;
        }
        List<List<ResultItem>> optFiles = groupingResult.mOptFiles;
        if ((optFiles == null) || optFiles.isEmpty()) {
            return null;
        }
        List<ResultItem> firstItem = optFiles.get(0);
        if ((firstItem == null) || firstItem.isEmpty()) {
            return null;
        }

        groupingResult.mBestItem = Processing.getBest(optFiles);
        Processing.sortGroupsByScore(optFiles);

        if (MemoriesUtils.FORCE_SCAN_MEMORIES) {
            groupingResult.debug(TAG, "ratingGroup");
        }

        return groupingResult;
    }

    private Map<String, Object> ratingPhoto(final Context context, final String filePath, MediaItem image, boolean isFaces, boolean isBurst,
                                            IFaceDetector faceDetector, boolean needDetect) {
        long timeAll = System.currentTimeMillis();
        long time = timeAll;

        // get bitmap
        Bitmap bitmap = requestBitmap(image);
        if (DEBUG) {
            GLog.d(TAG, "ratingPhoto, requestImage cost time=" + (System.currentTimeMillis() - time)
                    + ", bitmap=" + bitmap);
        }
        if ((bitmap == null) || bitmap.isRecycled()) {
            return null;
        }

        // get features
        time = System.currentTimeMillis();
        Map<String, Object> featureResult = Processing.processingFile(context, bitmap,
                new File(filePath).lastModified(), !isFaces && isBurst);
        if (DEBUG) {
            GLog.v(TAG, "ratingPhoto, processingFile cost time=" + (System.currentTimeMillis() - time));
        }
        if (featureResult == null) {
            return null;
        }

        // detect face
        if (needDetect && (faceDetector != null)) {
            time = System.currentTimeMillis();
            FaceInfoMirror[] ff = faceDetector.detectFaceInfo(bitmap, null);
            if (ff != null) {
                List<List<PointF>> facePoints = new ArrayList<>();
                List<Rect> faceRects = new ArrayList<>();
                for (FaceInfoMirror f : ff) {
                    if (f != null) {
                        PointF[] points = f.getFacePoints();
                        Rect rect = f.getFaceRect();
                        if ((points != null) && (rect != null)) {
                            facePoints.add(Arrays.asList(points));
                            faceRects.add(rect);
                        }
                    }
                }
                if (DEBUG) {
                    GLog.d(TAG, "ratingPhoto, face detect cost time=" + (System.currentTimeMillis() - time));
                }

                time = System.currentTimeMillis();
                if (!facePoints.isEmpty() && !faceRects.isEmpty()) {
                    Processing.computeEmotion(featureResult, facePoints, faceRects);
                }
                if (DEBUG) {
                    GLog.d(TAG, "ratingPhoto, face Emotion cost time=" + (System.currentTimeMillis() - time));
                }
            } else {
                GLog.d(TAG, "ratingPhoto, face not found. detect cost time=" + (System.currentTimeMillis() - time));
            }
        } else {
            if (DEBUG) {
                GLog.d(TAG, "ratingPhoto, no face.");
            }
        }

        // tag photo
        if (!isFaces && !isBurst) {
            if (!bitmap.isRecycled()) {
                bitmap = ImageUtils.getScaledBitmap(bitmap, image.getRotation(), TAG_PHOTO_WIDTH, TAG_PHOTO_HEIGHT);
            }
            if (bitmap == null) {
                if (DEBUG) {
                    GLog.d(TAG, "ratingPhoto, load bmp from file.");
                }
                int degree = ExifUtils.getExifDegree(filePath);
                bitmap = ImageUtils.getScaledBitmap(filePath, degree, TAG_PHOTO_WIDTH, TAG_PHOTO_HEIGHT);
            }
            if (DEBUG) {
                GLog.v(TAG, "ratingPhoto, bitmap cost time=" + (System.currentTimeMillis() - time));
            }
            if (bitmap != null) {
                time = System.currentTimeMillis();
                Map<String, Object> taggingResult = TaggingUtil.tagPhoto(context.getAssets(), bitmap);
                if ((taggingResult != null) && !taggingResult.isEmpty()) {
                    featureResult.putAll(taggingResult);
                }
                if (!bitmap.isRecycled()) {
                    bitmap.recycle();
                }
                if (DEBUG) {
                    GLog.v(TAG, "ratingPhoto, tagPhoto cost time=" + (System.currentTimeMillis() - time));
                }
            }
        }
        if (DEBUG) {
            GLog.v(TAG, "ratingPhoto, cost time=" + (System.currentTimeMillis() - timeAll));
        }
        return featureResult;
    }

    /**
     * @param isFaces is figures cover
     * @param isBurst Whether it is fast mode, used for chsot
     */
    private GroupingResult grouping(Context context, List<MediaItem> imageList,
                                    boolean isFaces, boolean isBurst, int minResult, int maxResult) {
        // rating single photo
        OifaceBindUtils.getInstance().bindTask();
        int count = 0;
        List<Map<String, Object>> features = new ArrayList<>();
        List<String> existFileList = new ArrayList<>();
        IFaceDetector faceDetector = null;
        IFaceScanAbility ability = null;
        try {
            ability = ((GalleryApplication) context.getApplicationContext()).getAppAbility(IFaceScanAbility.class);
            if (ability != null) {
                faceDetector = ability.newFaceDetector();
            }
            File file = null;
            String filePath = null;
            boolean needDetect = true;
            Map<String, Boolean> faceCache = GalleryScanPersonPetProviderHelper.getFaceStatus();
            for (MediaItem item : imageList) {
                if (GalleryScanMonitor.isGalleryRunOnTop()) {
                    GLog.d(TAG, "grouping, gallery is on top, we need give up!");
                    return null;
                }
                if (!GalleryScanMonitor.isAllowContinueScan(context, false, false)) {
                    GLog.d(TAG, "grouping, mismatch condition, we need give up!");
                    return null;
                }
                filePath = item.getFilePath();
                file = new File(filePath);
                if (file.exists()) {
                    count++;
                    if ((faceCache != null) && !faceCache.isEmpty() && faceCache.containsKey(filePath)) {
                        needDetect = faceCache.get(filePath);
                    }
                    Map<String, Object> feature = null;
                    if (GProperty.DEBUG) {
                        feature = ratingPhoto(context, item.getFilePath(), item, isFaces, isBurst, faceDetector, needDetect);
                    } else {
                        try {
                            feature = ratingPhoto(context, item.getFilePath(), item, isFaces, isBurst, faceDetector, needDetect);
                        } catch (Throwable e) {
                            GLog.e(TAG, "grouping, e=" + e);
                        }
                    }
                    if (feature != null) {
                        existFileList.add(item.getFilePath());
                        features.add(feature);
                    } else if (DEBUG) {
                        GLog.d(TAG, "grouping, not found feature.");
                    }
                } else if (DEBUG) {
                    GLog.d(TAG, "grouping, file not exist.");
                }
            }
        } finally {
            IOUtils.closeQuietly(faceDetector);
            IOUtils.closeQuietly(ability);
        }
        if (features.isEmpty()) {
            return null;
        }
        GLog.d(TAG, "grouping, rating photo end. " + ", file count=" + count);

        // group
        OifaceBindUtils.getInstance().bindTask();
        String identifier = "group1";
        GroupingResultItem result = null;
        if (isFaces) {
            result = Processing.processingFaces(features);
        } else {
            float sensitivity = 0.1f; //(0,1.0)
            result = Processing.processingGrouping(identifier, features, isBurst, sensitivity, null);
        }

        Map<Integer, List<List<Integer>>> groups = result.groups;
        List<List<Integer>> finalResult = null;
        int opt = result.optimalGroupIndex;
        if (!isFaces && !isBurst) {
            if (groups.size() < minResult) {
                finalResult = groups.get(groups.size());
            } else {
                if (opt > maxResult) {
                    finalResult = groups.get(opt);
                } else if (opt < minResult) {
                    finalResult = groups.get(minResult);
                } else {
                    finalResult = groups.get(opt);
                }
            }
        } else {
            finalResult = groups.get(opt);
        }

        GroupingResult groupingResult = new GroupingResult();
        List<Integer> grouped = new ArrayList<>();
        List<List<ResultItem>> groupdFiles = new ArrayList<>();
        for (List<Integer> subGroup : finalResult) {
            List<ResultItem> sub = new ArrayList<>();
            for (Integer index : subGroup) {
                ResultItem resultItem = new ResultItem();
                resultItem.filePath = existFileList.get(index);
                resultItem.features = features.get(index);
                grouped.add(index);

                sub.add(resultItem);
            }
            groupdFiles.add(sub);
        }

        if (!isFaces && !isBurst) {
            List<ResultItem> droppedGroup = new ArrayList<>();
            if (opt > maxResult) {
                Processing.sortGroupsByScore(groupdFiles);
                while (groupdFiles.size() > maxResult) {
                    List<ResultItem> lastGroup = groupdFiles.get(groupdFiles.size() - 1);
                    droppedGroup.addAll(lastGroup);
                    groupdFiles.remove(lastGroup);
                }
            }
            groupingResult.mDroppedFiles.addAll(droppedGroup);
        }

        groupingResult.mOptFiles.addAll(groupdFiles);

        for (int i = 0; i < existFileList.size(); i++) {
            if (!grouped.contains(i)) {
                ResultItem resultItem = new ResultItem();
                resultItem.filePath = existFileList.get(i);
                resultItem.features = features.get(i);

                groupingResult.mBadFiles.add(resultItem);
            }
        }
        return groupingResult;
    }

    private Bitmap requestBitmap(MediaItem mediaItem) {
        IResourcingAbility resourcingAbility = ((GalleryApplication) ContextGetter.context.getApplicationContext())
                .getAppAbility(IResourcingAbility.class);
        ResourceKey resourceKey = ResourceKeyFactory.createResourceKey(mediaItem);
        ResourceGetOptions options = new ResourceGetOptions(
                ThumbnailSizeUtils.TYPE_THUMBNAIL,
                -1,
                -1,
                CropParams.centerRectCrop(),
                CacheOperation.ReadWriteAllCache.INSTANCE,
                SourceOperation.ReadLocal.INSTANCE
        );
        if ((resourcingAbility != null) && (resourceKey != null)) {
            try {
                ImageResult<Bitmap> result = resourcingAbility.requestBitmap(resourceKey, options, null, null);
                if (result != null) {
                    return result.getResult();
                }
            } finally {
                resourcingAbility.close();
            }
        }
        return null;
    }
}
