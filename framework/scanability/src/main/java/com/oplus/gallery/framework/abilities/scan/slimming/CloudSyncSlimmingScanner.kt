/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CloudSyncSlimmingScanner.kt
 ** Description: 瘦身功能搬迁到扫描中增加CloudSyncSlimmingScanner
 ** Version: 1.0
 ** Date: 2024/12/23
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>             2024/12/23     1.0		   create
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.slimming

import android.content.Context
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor

/**
 * 瘦身功能搬迁到扫描中增加CloudSyncSlimmingScanner
 */
class CloudSyncSlimmingScanner(context: Context) : GalleryScan(context) {

    override fun getScanType(): Int {
        return CLOUD_SYNC_SLIMMING_SCAN
    }

    override fun getSceneName(): String = TAG

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        super.onScan(triggerType, config)
        markConditionsOnStartScan()
        val startTime = System.currentTimeMillis()
        GLog.w(TAG, LogFlag.DL, "onScan cloudSyncSlimmingScanner,triggerType = $triggerType")
        ApiDmManager.getCloudSyncDM().executeSlimming { GalleryScanMonitor.isAllowContinueScan(mContext, true, mCanScanWhenGalleryRunOnTop) }
        updateLastScanTime()
        GLog.w(TAG, LogFlag.DL, "onScan,cloudSyncSlimmingScanner scan end. cost = ${GLog.getTime(startTime)}ms")
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }

    companion object {
        private const val TAG = "CloudSyncSlimmingScanner"
    }
}