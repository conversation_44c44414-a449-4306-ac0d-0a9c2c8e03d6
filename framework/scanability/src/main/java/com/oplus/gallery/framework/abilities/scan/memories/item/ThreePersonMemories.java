/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ThreePersonMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/14
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D      2018/3/14   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.bean.CreateMemoriesEntry;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ThreePersonMemories extends Memories {
    private static final String TAG = "ThreePersonMemories";
    private static final String STOP_REASON_LESS_THAN_4_DAYS = TAG + "_LessThan4Days";
    private static final String STOP_REASON_SCAN_FAILED = TAG + "_ScanFailed";
    private static final String STOP_REASON_CREATE_EMPTY = TAG + "_CreateEmpty";
    private static final String STOP_REASON_CREATE_FAILED = TAG + "_CreateFailed";

    public ThreePersonMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        long lastMemoriesTime = MemoriesProviderHelper.getLastSpecifiedMemoriesTime(getMemoriesId());
        if ((lastMemoriesTime != 0) && (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(lastMemoriesTime,
                System.currentTimeMillis()) < getIntOptimalConfig(FACE_MEMORIES_INTERVAL))) {
            GLog.d(TAG, "prepareMemories, current time interval last ThreePersonMemories time less than 4 days!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_LESS_THAN_4_DAYS;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        HashMap<String, ArrayList<Long>> pathHashMap = MemoriesProviderHelper.getGroupIdListOfImage(mContext);
        List<Map.Entry<ArrayList<Long>, ArrayList<String>>> list = MemoriesScannerHelper.convertHashMapToList(pathHashMap,
                MemoriesScannerHelper.THREE_PERSON_ID_LIST_SIZE);
        for (Map.Entry<ArrayList<Long>, ArrayList<String>> entry : list) {
            MemoriesScannerHelper.GroupIdListInfo info = new MemoriesScannerHelper.GroupIdListInfo();
            info.mIdList = entry.getKey();
            info.mIdListString = MemoriesScannerHelper.GroupIdListInfo.convertIdListToString(info.mIdList);
            long samePersonLastMemoriesTime = MemoriesProviderHelper
                    .getSamePersonLastMemoriesTime(getMemoriesId(), info.mIdListString);
            if ((samePersonLastMemoriesTime != 0) && (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(samePersonLastMemoriesTime,
                    System.currentTimeMillis()) < getIntOptimalConfig(SAME_FACE_MEMORIES_INTERVAL))) {
                GLog.d(TAG, "scanMemories, current time interval last"
                        + " ThreePersonMemories time with same person less than 30 days!");
                continue;
            }
            MemoriesScannerHelper.DateRange dateRange = new MemoriesScannerHelper.DateRange();
            dateRange.mStart = samePersonLastMemoriesTime;
            dateRange.mEnd = System.currentTimeMillis();
            ArrayList<MediaItem> items = MemoriesProviderHelper
                    .getItemListOfSpecifiedPerson(mContext, entry.getValue(), dateRange, mMemoriesPicMin);
            List<MediaItem> filteredItemList = MemoriesProviderHelper.getFilteredItemList(items, getLabelFilterIdList());
            GLog.v(TAG, "scanMemories, filteredItemList size:" + filteredItemList.size());
            if (filteredItemList.size() > mMemoriesPicMin) {
                if (createMemories(filteredItemList, info.mIdListString)) {
                    return true;
                }
            }
        }
        // 扫描中断，记录原因
        mStopReason = STOP_REASON_SCAN_FAILED;
        return false;
    }

    @Override
    public boolean createMemories() {
        return true;
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.FACE_THREE_PERSON_MEMORIES;
    }

    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_FACE_MEMORIES_THREE_PERSON;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    private boolean createMemories(List<MediaItem> itemList, String groupIdStr) {
        if ((itemList == null) || (itemList.isEmpty())) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATE_EMPTY;
            return false;
        }
        GLog.v(TAG, "createMemories size:" + itemList.size() + ", type:" + getMemoriesId());
        MemoriesScannerHelper.DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        CreateMemoriesEntry.CreateFaceMemoriesEntry createFaceMemoriesEntry = new CreateMemoriesEntry.CreateFaceMemoriesEntry.Builder()
                .setName(getMemoriesName())
                .setType(getMemoriesId())
                .setGroupIdStr(groupIdStr)
                .setStartTime(dateRange.mStart)
                .setEndTime(dateRange.mEnd)
                .setNameType(getNameType())
                .build();
        int setId = MemoriesProviderHelper.createFaceMemories(createFaceMemoriesEntry);
        if (setId == -1) {
            GLog.w(TAG, "createMemories create memories failed!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATE_FAILED;
            return false;
        }
        return processMemoriesItems(setId, itemList, dateRange.mStart, dateRange.mEnd);
    }
}
