/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - XqipCacheScanner.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/04/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     yaoweihe       2024/04/22    1.0                create
 ******************************************************************************/
package com.oplus.gallery.framework.abilities.scan.xqip

import android.content.Context
import android.os.Bundle
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.StorageType
import com.oplus.gallery.framework.abilities.caching.options.CachePrepareOptions
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.KEY_IS_POSITIVE_ORDER
import com.oplus.gallery.framework.abilities.data.DataRepository.KEY_MODE
import com.oplus.gallery.framework.abilities.data.DataRepository.KEY_PATH_STR
import com.oplus.gallery.framework.abilities.data.DataRepository.TimelineModelGetter.Companion.TYPE_TIMELINE_ALBUM
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.app.getAppAbility
import java.lang.ref.WeakReference
import kotlin.system.measureTimeMillis

/**
 * XqipCacheScanner: 用于后台扫描时生成Xqip磁盘缓存
 */
class XqipCacheScanner(private val context: Context) : GalleryScan(context) {
    private val xqipCacheToken = ICachingAbility.IXqipCache.XQIP_CACHE_ALL_DISK_TOKEN
    private var curScanStartIndex = 0

    override fun getScanType(): Int = XQIP_CACHE_SCAN

    override fun getSceneName(): String = SCENE_NAME

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        super.onScan(triggerType, config)
        GLog.w(TAG, LogFlag.DL, "onScan, start scan xqipcache, triggerType:$triggerType")
        //是否需要监听前后台，不一定，由扫描策略控制，能及时停下来就不用
        scan()
        GLog.w(TAG, LogFlag.DL, "onScan, scan xqipcache, end")
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }


    /**
     * 1.是否满足扫描条件
     * 2.分批
     *  2.1查询数据库
     *  2.2对比BlockCache：元数据与具体缓存文件
     *  2.3有dirty的item进行刷新
     *     先从block中获取子item再进行生成，减少解码损耗
     *
     */
    private fun scan() {
        GLog.w(TAG, LogFlag.DL, "scan.")
        measureTimeMillis {
            val totalCount = getAllPictureCount()
            var alreadyScanCount = 0
            curScanStartIndex = DEFAULT_START_SCAN_INDEX
            context.getAppAbility<ICachingAbility>()?.use { cachingAbility ->
                // 注册TaskOwner，数据能够加载
                cachingAbility.xqipCache?.resume()
                val model = DataRepository.getAlbumModel(
                    TYPE_TIMELINE_ALBUM,
                    Bundle().apply {
                        putString(KEY_PATH_STR, SourceConstants.Local.PATH_ALBUM_XQIP_ALL.toString())
                        putInt(KEY_MODE, MEDIA_TYPE_SUPPORT_ALL)
                        putBoolean(KEY_IS_POSITIVE_ORDER, false)
                    }
                )

                while (isReadyContinue() &&
                    alreadyScanCount < XQIP_CACHE_SCAN_COUNT_24H_MAX &&
                    curScanStartIndex < totalCount
                ) {
                    val newRange = IntRange(curScanStartIndex, curScanStartIndex + BATCH_COUNT)
                    GLog.d(TAG, LogFlag.DL, "scan scanRange=$newRange, totalCount=$totalCount")
                    cachingAbility.xqipCache?.startPrepareCache(
                        cacheToken = xqipCacheToken,
                        cachePrepareOptions = CachePrepareOptions(
                            model = WeakReference(model),
                            range = newRange,
                            storageType = StorageType.DISK_ONLY,
                            needAsync = false
                        )
                    )

                    curScanStartIndex += BATCH_COUNT
                    alreadyScanCount += BATCH_COUNT
                }

                cachingAbility.xqipCache?.stopPrepareCache(cacheToken = xqipCacheToken)
                cachingAbility.xqipCache?.destroy()
            }
        }.let { cost ->
            GLog.w(TAG, LogFlag.DL, "scan end. cost = $cost ms")
        }
    }

    private fun getAllPictureCount(): Int {
        return DataRepository.getAlbumModel(
            DataRepository.LocalAlbumModelGetter.TYPE_ALL_PICTURE).getCount()
    }

    override fun onInterrupt(reason: Int) {
        super.onInterrupt(reason)
        GLog.d(TAG, LogFlag.DL, "onInterrupt reason = $reason")
    }

    /**
     * 是否符合后台运行的条件
     */
    private fun isReadyContinue(): Boolean {
        val isReadyContinue = !isCancel && !isInterrupt && GalleryScanMonitor.isAllowContinueScan(mContext, true, mCanScanWhenGalleryRunOnTop)
        GLog.d(TAG, LogFlag.DL, "isReadyContinue = $isReadyContinue")
        return isReadyContinue
    }

    companion object {
        private const val TAG = "${ScanConst.TAG}XqipCacheScanner"
        private const val SCENE_NAME = "XqipCacheScanner"
        // 2w,按时间来算吧
        private const val XQIP_CACHE_SCAN_COUNT_24H_MAX = 20000
        // 1000一批次
        private const val BATCH_COUNT = 1000
        private const val DEFAULT_START_SCAN_INDEX = 0
    }
}