/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MultiDayBatch
 ** Description: 精选扫描分批策略:多天分批策略
 **
 ** Version: 1.0
 ** Date: 2022/01/24
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/01/24  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.batch

import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectTimeNode
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.util.debug.GLog

class MultiDayBatch : Batch<BatchScanData>() {

    companion object {
        /**
         * 一个批次内，未扫素材最大的数目
         */
        const val BATCH_THRESHOLD_OF_IMAGE_NOT_SCANNED = 200

        /**
         * 一个批次内，已扫素材最大的数目
         */
        const val BATCH_THRESHOLD_OF_IMAGE_SCANNED = BATCH_THRESHOLD_OF_IMAGE_NOT_SCANNED * 2
        private const val TAG = "MultiDayBatch"
    }

    override fun hasNext(): Boolean {
        return notScannedNodeList.isNotEmpty() || (nextBatch?.hasNext() == true)
    }

    override fun updateDataToNextBatch() {
        val indexOfSameDate = getIndexOfSameDate(notScannedNodeList[0].date, scannedNodeList)
        val indexRange = notScannedNodeList[0].itemRange
        val notScannedInfoInDay = mutableListOf<BaseImageInfo>()
        val scannedInfoInDay = mutableListOf<BaseImageInfo>()
        notScannedInfoInDay.addAll(notScannedInfoList.subList(indexRange.first, indexRange.last + 1))
        notScannedNodeList.removeAt(0)
        if (indexOfSameDate >= 0) {
            val indexRange = scannedNodeList[indexOfSameDate].itemRange
            scannedInfoInDay.addAll(scannedInfoList.subList(indexRange.first, indexRange.last + 1))
            scannedNodeList.removeAll(scannedNodeList.subList(0, indexOfSameDate + 1))
        }
        nextBatch?.updateInfoList(notScannedInfoInDay, scannedInfoInDay)
        GLog.d(
            TAG, "updateDataToNextBatch. info: ${notScannedInfoInDay.size}. ${scannedInfoInDay.size}," +
                " node: ${notScannedNodeList.size}, ${scannedNodeList.size}"
        )
    }

    override fun isSwitchToNextBatch(): Boolean {
        if (nextBatch?.hasNext() == true) {
            return true
        }
        /**
         * 用未扫描节点 找已扫描节点同一天的index，无则返回-1
         */
        val indexOfSameDate = getIndexOfSameDate(notScannedNodeList[0].date, scannedNodeList)
        /**
         * 第一个未扫描节点的数量已超，或者第一个已扫描节点的数量超了，则：
         * 1，取出未扫描节点的index，即0
         * 2，取出已扫描节点的index
         */
        if ((notScannedNodeList[0].count > BATCH_THRESHOLD_OF_IMAGE_NOT_SCANNED)
            || ((indexOfSameDate >= 0) && (scannedNodeList[indexOfSameDate].count > BATCH_THRESHOLD_OF_IMAGE_SCANNED))
        ) {
            GLog.d(
                TAG, "isSwitchToNextBatch. node: ${notScannedNodeList[0]}" +
                    " indexOfSameDate: $indexOfSameDate"
            )
            return true
        }
        return false
    }

    /**
     * 1, 先获取下一批次的indexList数据 包括未扫描列表的indexList，以及已扫描列表的indexList
     * 2，通过getScanInfo ，依次将已扫的和未扫的indexList转成了对应的ImageInfo
     * 3，装载到result 返回
     */
    override fun getData(): BatchScanData {
        val time = System.currentTimeMillis()
        val imageScanned = mutableListOf<BaseImageInfo>()
        val videoScanned = mutableListOf<BaseImageInfo>()
        val imageNotScanned = mutableListOf<BaseImageInfo>()
        val videoNotScanned = mutableListOf<BaseImageInfo>()

        val nextBatch = getIndexBatch()
        if (nextBatch.scannedIndexList.isNotEmpty()) {
            /**
             * 取出scannedIndexList对应的已扫描的素材
             */
            val subNodeList = convertIndexToTimeNode(nextBatch.scannedIndexList, scannedNodeList)
            val scannedInfo = getScanInfo(nextBatch.scannedIndexList, subNodeList, scannedInfoList)
            GLog.d(TAG, "getData. scanned subNodeList: $subNodeList ")
            scannedNodeList.removeAll(subNodeList)
            val infoPair = classifyInfoByMediaType(scannedInfo)
            imageScanned.addAll(infoPair.first)
            videoScanned.addAll(infoPair.second)
            GLog.d(TAG, "getData. scanned image:video = {${infoPair.first.size}, ${infoPair.second.size}}")
        } else {
            /**
             * 调整已扫描节点表，减少后续循环次数
             */
            val lastNotScannedIndex = nextBatch.notScannedIndexList[nextBatch.notScannedIndexList.size - 1]
            adjustScannedNodeList(scannedNodeList, notScannedNodeList[lastNotScannedIndex].date)
        }
        /**
         * 取出notScannedIndexList对应的未扫描的素材
         */
        val subNodeList = convertIndexToTimeNode(nextBatch.notScannedIndexList, notScannedNodeList)
        val notScannedInfo = getScanInfo(nextBatch.notScannedIndexList, subNodeList, notScannedInfoList)
        GLog.d(TAG, "getData. not scanned subNodeList: $subNodeList ")
        //调整未扫描节点表
        notScannedNodeList.removeAll(subNodeList)
        val infoPair = classifyInfoByMediaType(notScannedInfo)
        imageNotScanned.addAll(infoPair.first)
        videoNotScanned.addAll(infoPair.second)
        return BatchScanData(imageNotScanned, imageScanned, videoNotScanned, videoScanned).apply {
            GLog.d(
                TAG, "getData. $this, indexList:  ${nextBatch.notScannedIndexList}, ${nextBatch.scannedIndexList}" +
                    " remain node:{${notScannedNodeList.size}, ${scannedNodeList.size}}," +
                    " allInfo:{${notScannedInfoList.size}, ${scannedInfoList.size}}," +
                    " cost: ${(System.currentTimeMillis() - time)}"
            )
        }
    }

    /**
     * 计算下一批次的indexList
     * 1，先判断第一个节点的未扫素材和已扫素材是否超，超则走一天内分批策略
     * 2，不超，则循环取素材，记录对应的index，直到加上某一天的素材后会超，停止，返回（不包含某一天当天）
     */
    private fun getIndexBatch(): IndexBatch {
        val notScannedIndexList = mutableListOf<Int>()
        val scannedIndexList = mutableListOf<Int>()
        var notScannedCount = 0
        var scannedCount = 0
        var indexOfSameDate = -1
        for ((index, node) in notScannedNodeList.withIndex()) {
            indexOfSameDate = getIndexOfSameDate(node.date, scannedNodeList)
            GLog.d(TAG, "getIndexBatch: date: ${node.date}, indexOfSameDate: $indexOfSameDate")
            when {
                /**
                 * 累加的未扫描数量 加上当前未扫描节点后超了
                 * 或者 累加的已扫描数量 加上 当前已扫描节点后超了，直接break
                 * 注意：这里当前节点的数量未被算进当前批次
                 */
                (notScannedCount + node.count) > BATCH_THRESHOLD_OF_IMAGE_NOT_SCANNED -> break
                ((indexOfSameDate >= 0)
                    && ((scannedCount + scannedNodeList[indexOfSameDate].count) >= BATCH_THRESHOLD_OF_IMAGE_SCANNED)) -> break
                else -> {
                    /**
                     * 数量不超时，开始累加，并将对应index放入当前批次内
                     */
                    notScannedCount += node.count
                    notScannedIndexList.add(index)
                    if (indexOfSameDate >= 0) {
                        scannedCount += scannedNodeList[indexOfSameDate].count
                        scannedIndexList.add(indexOfSameDate)
                    }
                }
            }
        }
        return IndexBatch(notScannedIndexList, scannedIndexList)
    }

    /**
     * 通过最后一个未扫描节点的日期，调整已扫描节点
     * 使后者调整后的节点日期都小于前者，减少后续查找日期的耗时
     */
    private fun adjustScannedNodeList(
        scannedNodeList: MutableList<SeniorSelectTimeNode>,
        lastNotScannedDate: String
    ) {
        if (scannedNodeList.isEmpty()) {
            return
        }
        var lastIndex = scannedNodeList.size
        for ((index, node) in scannedNodeList.withIndex()) {
            if (node.date < lastNotScannedDate) {
                lastIndex = index
                break
            }
        }
        GLog.d(TAG, "adjustScannedNodeList. lastIndex: $lastIndex")
        if (lastIndex > 0) {
            scannedNodeList.removeAll(scannedNodeList.subList(0, lastIndex))
        }
    }

    /**
     * 将indexList转换成对应的TimeNodeList
     */
    private fun convertIndexToTimeNode(
        indexList: MutableList<Int>,
        allNodeList: MutableList<SeniorSelectTimeNode>,
    ): MutableList<SeniorSelectTimeNode> {
        val lastIndex = indexList[indexList.size - 1]
        return allNodeList.subList(0, lastIndex + 1)
    }

    /**
     * 通过TimeNodeList，取出对应的ImageInfo
     */
    private fun getScanInfo(
        indexList: MutableList<Int>,
        subNodeList: MutableList<SeniorSelectTimeNode>,
        allImageInfo: MutableList<BaseImageInfo>
    ): MutableList<BaseImageInfo> {
        val result = mutableListOf<BaseImageInfo>()
        for ((index, node) in subNodeList.withIndex()) {
            val info = allImageInfo.subList(node.itemRange.first, node.itemRange.last + 1)
            if (index in indexList) {
                result.addAll(info)
            }
        }
        return result
    }

    data class IndexBatch(
        val notScannedIndexList: MutableList<Int>,
        val scannedIndexList: MutableList<Int>
    )
}