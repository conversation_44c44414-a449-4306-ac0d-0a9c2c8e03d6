/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IqaQualityModelLoadingTemplate.kt
 ** Description : 质量美学ModelLoadingTemplate
 ** Version     : 1.0
 ** Date        : 2024/3/22
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/3/22      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * 质量美学ModelLoadingTemplate
 */
class IqaQualityModelLoadingTemplate(context: Context, modelConfig: ModelConfig) : SeniorSelectModelLoadingTemplate(context, modelConfig) {
    override val tag: String = TAG

    override val modelName: String = ModelName.QUALITY_SOURCE

    override val modelFileName: String = IQA_QUALITY_MODEL_NAME

    companion object {
        private const val TAG = "IqaQualityModelLoadingTemplate"

        /**
         * 加载模型文件
         */
        private const val IQA_QUALITY_MODEL_NAME = "iqa_quality.model"
    }
}