/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OAVideoClassifyEngine.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2021/10/11
 ** Author: Sandy.Meng@Apps.Gallery3D
 ** TAG: Video label SDK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Sandy.Meng@Apps.Gallery3D       2021/10/11   1.0         new video label
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.label.engine

import android.content.Context
import android.net.Uri
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.label.bean.LabelInfo
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_VIDEO_CLASSIFY_RUN_ON_CPU
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.label.LabelScanner
import com.oua.albumclassifier.AlbumClassifier
import com.oua.tflite.Config
import java.io.File

class OAVideoClassifyEngine(private val context: Context?) : AbsVideoLabelClassifyEngine() {

    private var classifier: AlbumClassifier? = null

    /**
     * 是否支持视频标签跑CPU而不走GPU
     * 此问题是为了修复BUG 7953659，分析原因是对应的GPU OpenCL库有问题，需要在此平台上走CPU而不走GPU
     * 目前仅7550和7150平台支持
     */
    private val isSupportVideoClassifyRunOnCpu: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_VIDEO_CLASSIFY_RUN_ON_CPU)
    }

    override fun loadModels(componentDirPath: String?): Boolean {
        try {
            System.load(componentDirPath + File.separator + ComponentPrefUtils.VIDEO_LABEL_SO_NAME_OA)
        } catch (e: Throwable) {
            GLog.e(TAG, "load source failed", e)
            return false
        }
        return true
    }

    override fun createVideoClassify(componentDirPath: String?): Boolean {
        val classifyModelPath: String = (componentDirPath
                + File.separator + ComponentPrefUtils.VIDEO_LABEL_CLASSIFY_MODEL_NAME_OA)
        classifier = if (isSupportVideoClassifyRunOnCpu) {
            AlbumClassifier.createVideoClassifier(context, classifyModelPath, AlbumClassifier.DEVICE_CPU)
        } else {
            AlbumClassifier.createVideoClassifier(context, classifyModelPath)
        }
        return classifier != null
    }

    /**
     * 视频标签sdk在读取文件的时候，内部已经按照沙盒机制实现文件的读取
     *
     * @return List<LabelInfo>? crash发生或者引擎未就绪时，会返回null
     */
    override fun getVideoClassifyInfo(video: LocalVideo): List<LabelInfo>? {
        classifier ?: return null
        val start = System.currentTimeMillis()
        val labels = ArrayList<LabelInfo>()
        val entryList = runCatching {
            classifier?.run(Uri.parse(video.filePath))
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "getVideoClassifyInfo, fail", it)
            return null
        }.getOrNull()
        if (entryList.isNullOrEmpty()) {
            return labels
        }
        for (entry in entryList) {
            val info = convertToLabelInfo(video, entry)
            labels.add(info)
        }
        GLog.d(
            TAG,
            "cost time: ${System.currentTimeMillis() - start}  file size:  ${video.fileSize}  duration: ${video.duration}"
        )
        return labels
    }

    private fun convertToLabelInfo(video: LocalVideo, entry: Map.Entry<String, Float>): LabelInfo {
        val info = LabelInfo()
        info.mScore = entry.value
        info.mSceneId = LabelDictionary.getLabelId(entry.key)
        info.mMediaId = video.mediaId.toLong()
        info.mDateTaken = video.dateTakenInMs
        info.mDateModifiedInSec = video.dateModifiedInSec
        info.mFilePath = video.filePath
        info.mIsRecycled = video.isRecycledItem
        info.mMediaType =
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
        return info
    }

    override fun buildDefaultLabelInfo(video: LocalVideo): LabelInfo {
        val info = LabelInfo()
        info.mScore = LabelScanner.SCENE_SCORE
        info.mSceneId = LabelScanner.SCENE_ID_INVALID
        info.mMediaId = video.mediaId.toLong()
        info.mDateTaken = video.dateTakenInMs
        info.mDateModifiedInSec = video.dateModifiedInSec
        info.mFilePath = video.filePath
        info.mIsRecycled = video.isRecycledItem
        info.mMediaType =
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
        return info
    }

    override fun release() {
        classifier?.close()
    }

    companion object {
        private const val TAG = "OAVideoClassifyEngine"
    }
}