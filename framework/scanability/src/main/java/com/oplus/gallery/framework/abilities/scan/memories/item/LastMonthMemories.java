/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LastMonthMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/13
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D     2018/3/13    1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.List;

public class LastMonthMemories extends Memories {
    private static final String TAG = "LastMonthMemories";
    private static final String STOP_REASON_HAS_CREATED = TAG + "_HasCreated";
    private static final String STOP_REASON_LESS_THAN_1_MONTH = TAG + "_LessThan1Month";
    private static final String STOP_REASON_CREATED_IN_DATE_RANGE = TAG + "_CreatedInDateRange";
    private static final String STOP_REASON_SCAN_SIZE_NOT_ENOUGH = TAG + "_ScanSizeNotEnough";

    public LastMonthMemories(Context context) {
        super(context);
    }

    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_DATE_LAST_MONTH;
    }

    @Override
    public boolean prepareMemories() {
        if (MemoriesProviderHelper.hasMemoriesCreatedInThisWeek(getMemoriesGroupType())) {
            GLog.v(TAG, "prepareMemories, optimal date memories has created in this week!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_HAS_CREATED;
            return false;
        }
        long lastMemoriesTime = MemoriesProviderHelper.getLastSpecifiedMemoriesTime(getMemoriesId());
        if ((lastMemoriesTime != 0) && (MemoriesScannerHelper.getMonthsBetweenTwoDates(lastMemoriesTime,
                System.currentTimeMillis()) < MemoriesScannerHelper.LAST_MONTH_MEMORIES_INTERVAL)) {
            GLog.d(TAG, "prepareMemories, current time interval last LastMonthMemories time less than 1 month!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_LESS_THAN_1_MONTH;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        MemoriesScannerHelper.DateRange itemDateRange = MemoriesScannerHelper.getMonthRange(MemoriesUtils.LAST_MONTH_OFFSET);
        /*
        last month(exclude first complete and incomplete week) has no DATE_LAST_WEEK_MEMORIES,
        DATE_LAST_TWO_WEEK_MEMORIES, DATE_LAST_THREE_WEEK_MEMORIES created
        */
        MemoriesScannerHelper.DateRange memoriesDateRange = new MemoriesScannerHelper.DateRange();
        int dayOfWeek = MemoriesScannerHelper.getDayOfWeek(itemDateRange.mStart);
        int offset = 0;
        if (dayOfWeek == MemoriesScannerHelper.INDEX_OF_SUNDAY) {
            offset = 1 + MemoriesScannerHelper.DAYS_OF_ONE_WEEK;
        } else if (dayOfWeek == MemoriesScannerHelper.INDEX_OF_MONDAY) {
            offset = MemoriesScannerHelper.DAYS_OF_ONE_WEEK;
        } else {
            offset = MemoriesScannerHelper.DAYS_OF_ONE_WEEK * 2 - (dayOfWeek - MemoriesScannerHelper.INDEX_OF_MONDAY);
        }
        memoriesDateRange.mStart = MemoriesScannerHelper.getTimeOfOffset(itemDateRange.mStart, offset);
        memoriesDateRange.mEnd = itemDateRange.mEnd;
        List<Integer> typeList = new ArrayList<>();
        typeList.add(MemoriesType.DATE_LAST_WEEK_MEMORIES);
        typeList.add(MemoriesType.DATE_LAST_TWO_WEEK_MEMORIES);
        typeList.add(MemoriesType.DATE_LAST_THREE_WEEK_MEMORIES);
        if (MemoriesProviderHelper.hasSpecifiedMemoriesCreated(memoriesDateRange, typeList)) {
            GLog.d(TAG, "getDateOptimalItemList:memories has created in date range!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATED_IN_DATE_RANGE;
            return false;
        }
        ArrayList<MediaItem> itemList = MemoriesProviderHelper.getItemListFromLocalMedia(mContext, itemDateRange, mMemoriesPicMin);
        GLog.v(TAG, "getDateOptimalItemList itemList size:" + itemList.size());
        //filter specified labels
        mMediaItemList = MemoriesProviderHelper.getFilteredItemList(itemList, getLabelFilterIdList());
        GLog.v(TAG, "getDateOptimalItemList filteredItemList size:" + mMediaItemList.size());
        if ((mMediaItemList == null) || (mMediaItemList.size() <= mMemoriesPicMin)) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            return false;
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return createMemories(mMediaItemList);
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.DATE_LAST_MONTH_MEMORIES;
    }
}
