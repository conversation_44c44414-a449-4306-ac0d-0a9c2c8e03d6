/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - StudyScanner.kt
 ** Description: 学习算法扫描器
 ** Version: 1.0
 ** Date: 2025/5/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2025/5/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.study

import android.content.Context
import com.oplus.breakpad.BreakpadMaster
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.business_lib.model.data.study.utils.StudyDBTableHelper
import com.oplus.gallery.business_lib.model.data.study.utils.StudyImageInfo
import com.oplus.gallery.foundation.ai.study.StudyRecognizeApi
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper.trackScanResult
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.PET_SCAN_COUNT_24H_MAX
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.STUDY_SCAN_COUNT_24H_MAX
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.isAllowStartScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.refreshScanRecordingIfNecessary
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import kotlin.math.min

/**
 * 学习算法扫描器
 *
 * @property context 上下文对象
 */
class StudyScanner(val context: Context) : GalleryScan(context) {

    private val studyRecognizeApi by lazy { StudyRecognizeApi(context) }

    /**
     * 新的，此前没有扫描过的媒体列表
     */
    private var newMediaItems = mutableListOf<MediaItem>()

    /**
     * 需要重新/更新扫描的媒体列表
     */
    private var updateMediaItems = mutableListOf<MediaItem>()

    /**
     * 未充电时的学习扫描数量
     */
    private var scanCountNotInCharging = 0
    private var scanStudyCount = 0

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        GLog.w(TAG, LogFlag.DL) { "onScan, start study travel triggerType:$triggerType" }
        super.onScan(triggerType, config)
        // scanData(triggerType) marked by luhuangdao 学习需求暂未完善，先不跑
        GLog.w(TAG, LogFlag.DL) { "onScan, study scan end" }
    }

    private fun scanData(triggerType: Int) {
        GLog.d(TAG, LogFlag.DL) { "[scanData]" }
        if (isAllowStartScan(context, false, false) && isInterrupt.not()) {
            runCatching {
                if (prepareScanData(triggerType).not()) {
                    GLog.w(TAG, LogFlag.DL) { "[scanData], prepareScanData fail." }
                    return
                }
                doStudyRecognize()
                release()
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "[scanData] exception, $it" }
            }
        }
    }

    /**
     *  1.准备需要扫描的数据
     *
     * 查询local_media表、scan_study表
     * 计算需要新增的扫描列表和更新的扫描列表：
     *      1、新增：学习表不存在
     *      2、更新：学习表存在当版本号低于当前模型版本号 且未手动标记移除
     */
    private fun prepareScanData(triggerType: Int): Boolean {
        GLog.d(TAG, LogFlag.DL) { "[prepareScanData],  start." }
        scanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.STUDY_SCAN_COUNT_24H_KEY, 0)
        // 1.1 查询scan_study表中已有的数据
        val studyTableItems = StudyDBTableHelper.getAllStudyDataList()
        // 1.2 查询图片类型local_media表
        val localMediaItems = GalleryScanProviderHelper.getMediaItemForImgType(mContext)
        if (studyTableItems.isNotEmpty()) {
            // 1.3 比对local和study的数据
            val studyMap = GalleryScanDataManager.translateListToPathMap(studyTableItems)
            localMediaItems.forEach {
                if (it.invalid == 0) {
                    val studyInfo = studyMap[it.filePath.lowercase()]
                    if (studyInfo == null) {
                        // 1.4 a)、学习表不存在对应记录，需要新建扫描
                        newMediaItems.add(it)
                    } else if (studyInfo.isManual.not()
                        && studyRecognizeApi.isNewerVersion(studyInfo.version)) {
                        // 1.4 b)、不是手动移除的 并且 模型版本号更新了
                        updateMediaItems.add(it)
                    }
                }
            }
        } else {
            // 1.3 学习表为空，所有都需要扫描
            newMediaItems = localMediaItems
            GLog.d(TAG, LogFlag.DL) { "[prepareScanData],  first time scan, newImages = ${newMediaItems.size}" }
        }

        mNewCount = newMediaItems.size
        mUpdateCount = updateMediaItems.size
        mAllCount = mNewCount + mUpdateCount
        GLog.d(TAG, LogFlag.DL) { "[prepareScanData],  newCount = $mNewCount, updateCount = $mUpdateCount, allCount = $mAllCount" }

        if ((mAllCount == 0)) {
            GLog.i(TAG, LogFlag.DL) { "[prepareScanData],  has no new image and update image, do not need to continue!" }
            updateLastScanTime()
            release()
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_EMPTY, triggerType)
            return false
        }
        return true
    }

    /**
     * 2.对[newMediaItems]、[updateMediaItems]的文件执行算法扫描
     */
    private fun doStudyRecognize() {
        GLog.d(TAG, LogFlag.DL) { "[doStudyRecognize],  start." }
        markConditionsOnStartScan()
        // 2.1 分批遍历处理扫描
        var time = System.currentTimeMillis()
        mRemainedNewCount = loopDealItems(newMediaItems, this::loopScan)
        GLog.d(TAG, LogFlag.DL) { "[doStudyRecognize], loopScanNew total cost:${GLog.getTime(time)}ms" }

        time = System.currentTimeMillis()
        loopDealItems(updateMediaItems, this::loopScanUpdate)
        GLog.d(TAG, LogFlag.DL) { "[doStudyRecognize], loopScanUpdate total cost:${GLog.getTime(time)}ms" }

        trackStudyScan(null, false)
        GLog.d(TAG, LogFlag.DL) { "[doStudyRecognize], end. Scan image count = $mScanImageCount, " +
                "cost:${GLog.getTime(mStartScanTime)}ms" }
    }

    private fun loopScan(list: List<MediaItem>, isUpdate: Boolean = false) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "[loopScanNew], list is empty." }
            return
        }

        val time = System.currentTimeMillis()
        val insertList = mutableListOf<StudyImageInfo>()
        val deleteList = ArrayList<MediaItem>()
        list.forEach { item ->
            val filePath = item.filePath
            BreakpadMaster.setFilePath(filePath)
            //  2.2 扫描文件，根据api得到是否为学习
            getStudyImageInfoByLocalImage(item)?.also {
                deleteList.add(item)
                insertList.add(it)
            }
        }

        if (isUpdate) {
            // 2.3 如果是更新数据，先删除旧数据，然后执行插入
            StudyDBTableHelper.deleteStudyMediaItem(deleteList)
        }

        //  2.3 将缓存列表的数据插入数据库中。
        StudyDBTableHelper.insertStudyData(insertList)
        val loopScanTime = System.currentTimeMillis() - time
        val batchTotalCount = min(mScanImageCount, getLoopOnceCount())
        GLog.d(TAG, LogFlag.DL) {
            "[loopScan], end! Current already scanned ImageCount: = $mScanImageCount, isUpdate: $isUpdate, " +
                    "labelScan costTime this loopScanTime: $loopScanTime ms, perItemScanTime:" +
                    "${(loopScanTime / batchTotalCount.toFloat())}ms"
        }
    }

    private fun loopScanUpdate(list: List<MediaItem>) {
        loopScan(list, true)
    }

    /**
     * MediaItem -> 学习项ImageInfo
     * @return 扫描带是否学习项的数据Image
     */
    private fun getStudyImageInfoByLocalImage(item: MediaItem): StudyImageInfo? {
        val time = System.currentTimeMillis()
        val thumbnail = getThumbnail(item)
        if ((thumbnail == null) || (GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE != item.mediaType)) {
            GLog.e(TAG, LogFlag.DL, "[getStudyImageInfoByLocalImage], mediaItem is not localImage")
            return null
        }
        val isStudyResult = studyRecognizeApi.processImage(thumbnail)
        mScanImageCount++
        calculateScanFormatCount(item)
        val studyImageInfo = StudyImageInfo().apply {
            mFilePath = item.filePath
            mMediaType = item.mediaType
            isStudy = isStudyResult
            mInvalid = (item.invalid == 0).not()
            mIsRecycled = false
            isManual = false
            scanDate = time
            version = studyRecognizeApi.versionName
        }
        return studyImageInfo
    }

    /**
     * 分批处理扫描任务[function]
     * @param mediaItemList 被扫描的数据集合
     * @param function 扫描任务
     */
    private fun loopDealItems(mediaItemList: MutableList<MediaItem>, function: (List<MediaItem>) -> Unit): Int {
        var remainSize = mediaItemList.size
        GLog.d(TAG, LogFlag.DL) { "[loopDealItems],  mediaItemList.size = $remainSize" }
        val time = System.currentTimeMillis()
        val groupList = mutableListOf<MediaItem>()
        while (remainSize > 0 && isAllowContinueScan(scanCountNotInCharging, STUDY_SCAN_COUNT_24H_MAX)) {
            var groupCount = 0
            while ((groupCount < getLoopOnceCount()) && mediaItemList.isNotEmpty()) {
                groupList.add(mediaItemList.removeAt(0))
                groupCount++
            }
            remainSize = mediaItemList.size
            function.invoke(groupList)
            addScanCountIfNoCharging(groupList.size)
            recodeScanInfoIfNeed(groupList.size)
            bindSmallCore()
            groupList.clear()
        }
        GLog.d(TAG, LogFlag.DL) { "[loopDealItems],  cost:${GLog.getTime(time)}ms" }
        return remainSize
    }

    private fun getLoopOnceCount(): Int {
        return if (BatteryStatusUtil.isBatteryInCharging(false)) {
            ONE_SCAN_COUNT
        } else {
            min(ONE_SCAN_COUNT, STUDY_SCAN_COUNT_24H_MAX)
        }
    }

    /**
     * 更新未充电时的扫描数量
     */
    private fun addScanCountIfNoCharging(count: Int): Boolean {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true
        }
        refreshScanRecordingIfNecessary(mContext)
        scanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.STUDY_SCAN_COUNT_24H_KEY, 0) + count
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.STUDY_SCAN_COUNT_24H_KEY, scanCountNotInCharging)
        return false
    }

    private fun recodeScanInfoIfNeed(count: Int) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return
        }
        val scanCountAll = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.STUDY_SCAN_COUNT_ALL_KEY, 0) + count
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.STUDY_SCAN_COUNT_ALL_KEY, scanCountAll)
        GalleryScanUtils.recordInfo(mContext, TAG, scanCountNotInCharging.toLong(), scanCountAll.toLong())
    }

    private fun release() {
        GLog.d(TAG, LogFlag.DL) { "$sceneName release." }
        studyRecognizeApi.release()
    }

    override fun onMonitorRecord(event: MonitorEvent, reachThreshold: Boolean) {
        super.onMonitorRecord(event, reachThreshold)
        markConditionsOnStartScan()
        GLog.d(TAG, LogFlag.DL) { "[onMonitorRecord],  event = $event, reachThreshold = $reachThreshold" }
        trackStudyScan(event, true)
    }

    private fun trackStudyScan(event: MonitorEvent?, isMonitor: Boolean) {
        val reasonType = getReasonType(false, scanCountNotInCharging, PET_SCAN_COUNT_24H_MAX)
        val endNativeHeapSize = getEndNativeHeapSize(event)
        val additionalMap: MutableMap<String, String> = HashMap()
        additionalMap[SCAN_STUDY_COUNT] = scanStudyCount.toString()
        additionalMap[SCAN_FORMAT_COUNT] = mScanFormatCount.toString()
        additionalMap[IS_MONITOR] = isMonitor.toString()
        val scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap)
        GLog.d(TAG, LogFlag.DL) { "[trackStudyScan],  recordItem = $scanTrackInfo" }
        trackScanResult(scanTrackInfo)
    }

    override fun getScanType(): Int {
        return STUDY_SCAN
    }

    override fun getSceneName(): String {
        return SCENE_NAME
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return false
    }

    companion object {
        private const val TAG = "${ScanConst.TAG}StudyScanner"
        private const val SCENE_NAME = "StudyScanner"
        private const val SCENE_SCANNER_EMPTY = "${SCENE_NAME}_Empty"
        private const val SCAN_STUDY_COUNT = "scan_study_count"
        private const val ONE_SCAN_COUNT = 50
    }
}