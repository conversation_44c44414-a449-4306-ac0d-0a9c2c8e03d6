/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SingletonHolder.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/11/08
 ** Author      : wenshuguang
 ** TAG         : OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>        <date>         <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** wenshuguang     2021/11/08      1.0            OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 *********************************************************************************/


package com.oplus.gallery.framework.abilities.scan.utils

open class SingletonHolder<out T, in A>(creator: (A) -> T) {

    private var creator: ((A) -> T)? = creator

    @Volatile
    private var instance: T? = null

    fun getInstance(arg: A): T {
        return instance ?: synchronized(this) {
            instance ?: creator!!(arg).apply {
                instance = this
            }
        }
    }
}