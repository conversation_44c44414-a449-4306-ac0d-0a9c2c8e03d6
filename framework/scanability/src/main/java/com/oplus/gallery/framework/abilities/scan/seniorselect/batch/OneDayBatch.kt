/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OneDayBatch
 ** Description: 精选扫描分批策略:一天内的素材分批
 **
 ** Version: 1.0
 ** Date: 2022/01/19
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/01/19  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.batch

import com.oplus.gallery.framework.abilities.scan.seniorselect.batch.MultiDayBatch.Companion.BATCH_THRESHOLD_OF_IMAGE_NOT_SCANNED
import com.oplus.gallery.framework.abilities.scan.seniorselect.batch.MultiDayBatch.Companion.BATCH_THRESHOLD_OF_IMAGE_SCANNED
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import kotlin.math.ceil

class OneDayBatch : Batch<BatchScanData>() {

    companion object {
        private const val TAG = "OneDayBatch"

        /**
         * 间隔重叠
         * 用于当天有大量素材时，取一组待扫图，将其起始时间-5min，末端时间+5min，作为区间去找对应的已扫图
         */
        private const val TIME_OVERLAP = TimeUtils.TIME_5_MIN_IN_MS

        /**
         * 当上述区间的图片也有很多时，找到 BATCH_THRESHOLD_OF_IMAGE_SCANNED_IN_ONE_DAY 张强制结束
         */
        private const val BATCH_THRESHOLD_OF_IMAGE_SCANNED_IN_ONE_DAY = BATCH_THRESHOLD_OF_IMAGE_SCANNED * 5
    }

    private fun getSuggestMaxCountInDay(size: Int): Int {
        val suggestMaxCount = ceil(size.toFloat() / ceil(size.toFloat() / BATCH_THRESHOLD_OF_IMAGE_NOT_SCANNED)).toInt()
        GLog.d(TAG, "getSuggestMaxCountInDay suggestMaxCount: $suggestMaxCount, size: $size")
        return Integer.min(suggestMaxCount, size)
    }

    override fun hasNext(): Boolean {
        return notScannedInfoList.isNotEmpty()
    }

    /**
     * 获取一天内的分批素材
     * 1，未扫描素材 每次只取最多 ONCE_IMAGE_NOT_SCANNED_COUNT_MAX 张
     * 2，算出该批次素材的始末时间[A,B],加上 TIME_OVERLAP 重叠时间，即[A-TIME_OVERLAP,B-TIME_OVERLAP]
     * 3，用2算出的区间去找已扫素材，最多找 BATCH_THRESHOLD_OF_IMAGE_SCANNED_IN_ONE_DAY 张
     * 4，将二者装载返回
     */
    override fun getData(): BatchScanData {
        val imageScanned = mutableListOf<BaseImageInfo>()
        val videoScanned = mutableListOf<BaseImageInfo>()
        val imageNotScanned = mutableListOf<BaseImageInfo>()
        val videoNotScanned = mutableListOf<BaseImageInfo>()

        val notScannedInfo = mutableListOf<BaseImageInfo>()
        notScannedInfo.addAll(notScannedInfoList.subList(0, getSuggestMaxCountInDay(notScannedInfoList.size)))
        var infoPair = classifyInfoByMediaType(notScannedInfo)
        imageNotScanned.addAll(infoPair.first)
        videoNotScanned.addAll(infoPair.second)
        /**
         * 移除当前批次的素材
         */
        notScannedInfoList.removeAll(notScannedInfo)
        if (scannedInfoList.isEmpty()) {
            return BatchScanData(imageNotScanned, imageScanned, videoNotScanned, videoScanned).apply {
                GLog.d(TAG) { "getData. No scannedData. $this, remain=${notScannedInfoList.size}" }
            }
        }

        val startDateTaken = notScannedInfo[0].mDateTaken
        val endDateTaken = notScannedInfo[notScannedInfo.size - 1].mDateTaken
        val dateRange = LongRange(endDateTaken - TIME_OVERLAP, startDateTaken + TIME_OVERLAP)
        val scannedInfo = mutableListOf<BaseImageInfo>()

        /**
         * 使用该批次素材的末端时间，加5min offset后，作为下批次的大致起始时间
         */
        val nextStartDateTaken = if (notScannedInfoList.size > 0) {
            notScannedInfoList[0].mDateTaken + TIME_OVERLAP
        } else {
            -1
        }
        val overdueInfo = mutableListOf<BaseImageInfo>()
        for (info in scannedInfoList) {
            /**
             * 大于下批次的大致起始时间，即是不会参与下批次的取图循环，认为是过期的素材
             */
            if (info.mDateTaken > nextStartDateTaken) {
                overdueInfo.add(info)
            }

            if (info.mDateTaken in dateRange) {
                scannedInfo.add(info)
            }

            if ((scannedInfo.size > BATCH_THRESHOLD_OF_IMAGE_SCANNED_IN_ONE_DAY)
                || (info.mDateTaken < dateRange.first) && (info.mDateTaken < nextStartDateTaken)
            ) {
                break
            }
        }
        /**
         * 删除掉过期素材，减少下一批次的循环次数
         */
        scannedInfoList.removeAll(overdueInfo)
        infoPair = classifyInfoByMediaType(scannedInfo)
        imageScanned.addAll(infoPair.first)
        videoScanned.addAll(infoPair.second)
        return BatchScanData(imageNotScanned, imageScanned, videoNotScanned, videoScanned).apply {
            GLog.d(TAG) {
                "getData. $this, remainSize=${scannedInfoList.size}, overdueInfoSize=${overdueInfo.size} " +
                    "dateRange=$dateRange, nextStartDateTaken=$nextStartDateTaken"
            }
        }
    }

    override fun isSwitchToNextBatch(): Boolean {
        return hasNext().not()
    }

    override fun updateDataToNextBatch() {
        //do nothing
    }
}