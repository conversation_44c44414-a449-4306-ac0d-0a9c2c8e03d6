/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ScanTrackHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/30
 ** Author: <EMAIL>
 ** TAG: OPLUS_TRACK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0		INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.helpers

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.os.Debug
import android.provider.MediaStore
import android.text.TextUtils
import android.util.ArrayMap
import androidx.annotation.WorkerThread
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns
import com.oplus.gallery.foundation.database.util.MediaStoreUtils
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.NUM_10
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.NUM_100
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.STR_MS
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.SYMBOL_VERTICAL_LINE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.TRUE
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.EventId.USER_ACTIONLABEL_REMOVE
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.TYPE_PICTURE
import com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.EventId
import com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.Key
import com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant.TYPE_SCAN
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.isRegionCN
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil.getCurrentTemperature
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo.Companion.DETECTED_HANDLE_STATE
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo.Companion.STATISTICS_HANDLE_STATE
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.io.IOUtils
import java.text.SimpleDateFormat

object ScanTrackHelper {
    private const val TAG = "ScanTrackHelper"
    const val MAX_QUERY_LABEL_COUNT = 500
    private const val FACE_ATTRIBUTE_STATISTICS_TIME_INTERVAL = TimeUtils.TIME_1_WEEK_IN_MS
    private const val MAX_FACE_STATISTICS_LEN = 4
    private const val MAX_QUERY_COUNT = 500
    private val sDateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")


    // collect face attribute info
    private val faceAttrWhereClause: String
        get() {
            // collect face attribute info
            val whereClause = (ScanFaceColumns.HANDLE_STATE + " = "
                    + DETECTED_HANDLE_STATE + " AND "
                    + ScanFaceColumns.GROUP_ID + " >2 AND "
                    + ScanFaceColumns.SKIN + " IS NOT NULL")
            val stringBuilder = StringBuilder()
            stringBuilder.append("SELECT ")
            stringBuilder.append(GalleryStore.ScanFace.TAB + "." + ScanFaceColumns.DATA + ", ")
            stringBuilder.append(GalleryStore.ScanFace.GROUP_ID + ", ")
            stringBuilder.append(GalleryStore.ScanFace.TOP + ", ")
            stringBuilder.append(GalleryStore.ScanFace.BOTTOM + ", ")
            stringBuilder.append(GalleryStore.ScanFace.LEFT + ", ")
            stringBuilder.append(GalleryStore.ScanFace.RIGHT + ", ")
            stringBuilder.append(GalleryStore.ScanFace.AGE + ", ")
            stringBuilder.append(GalleryStore.ScanFace.SEX + ", ")
            stringBuilder.append(GalleryStore.ScanFace.SKIN + ", ")
            stringBuilder.append(GalleryStore.ScanFace.RACE + ", ")
            stringBuilder.append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + " FROM ")
            stringBuilder.append(GalleryStore.ScanFace.TAB + " INNER JOIN " + GalleryStore.GalleryMedia.TAB + " ON ")
            stringBuilder.append(
                GalleryStore.ScanFace.TAB + "." + GalleryStore.ScanFace.DATA
                        + "=" + GalleryStore.GalleryMedia.TAB + "." + GalleryStore.GalleryColumns.LocalColumns.DATA
            )
            stringBuilder.append(" WHERE ")
            stringBuilder.append(whereClause)
            stringBuilder.append(" LIMIT ")
            stringBuilder.append(MAX_QUERY_COUNT)
            return stringBuilder.toString()
        }


    /**
     * 2006012010
     */
    @Suppress("LongMethod")
    @JvmStatic
    fun trackFaceAttributeMap(context: Context?) {
        if (!isRegionCN) {
            return
        }
        try {
            val lastTime = ComponentPrefUtils.getLongPref(context, ComponentPrefUtils.PREF_LAST_FACE_ATTRIBUTE_STATIC_KEY, 0)
            if (System.currentTimeMillis() - lastTime <= FACE_ATTRIBUTE_STATISTICS_TIME_INTERVAL) {
                return
            }
            GLog.v(TAG, "addFaceAttributeMap, start")
            val startTime = System.currentTimeMillis()
            val mediaIdList: MutableList<Int> = ArrayList()
            val pathFeatureMap: MutableMap<String, FaceAttributeStatisticsInfo> = ArrayMap()
            val rawQueryReq = RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(faceAttrWhereClause)
                .setSqlArgs(null)
                .build()
            try {
                DataAccess.getAccess().rawQuery(rawQueryReq).use { cursor ->
                    if (cursor == null || cursor.count == 0) {
                        GLog.v(TAG, "addFaceAttributeMap, face cursor is null!")
                        return
                    }
                    // for attribute detect
                    val groupIdIndex = cursor.getColumnIndex(ScanFaceColumns.GROUP_ID)
                    val rectTopIndex = cursor.getColumnIndex(ScanFaceColumns.TOP)
                    val rectBottomIndex = cursor.getColumnIndex(ScanFaceColumns.BOTTOM)
                    val rectLeftIndex = cursor.getColumnIndex(ScanFaceColumns.LEFT)
                    val rectRightIndex = cursor.getColumnIndex(ScanFaceColumns.RIGHT)
                    val ageIndex = cursor.getColumnIndex(ScanFaceColumns.AGE)
                    val sexIndex = cursor.getColumnIndex(ScanFaceColumns.SEX)
                    val skinIndex = cursor.getColumnIndex(ScanFaceColumns.SKIN)
                    val raceIndex = cursor.getColumnIndex(ScanFaceColumns.RACE)
                    val mediaIdIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID)
                    val filePathIndex = cursor.getColumnIndex(ScanFaceColumns.DATA)
                    while (cursor.moveToNext()) {
                        val groupId = cursor.getInt(groupIdIndex)
                        val rectTop = cursor.getInt(rectTopIndex)
                        val rectBottom = cursor.getInt(rectBottomIndex)
                        val rectLeft = cursor.getInt(rectLeftIndex)
                        val rectRight = cursor.getInt(rectRightIndex)
                        val age = cursor.getInt(ageIndex)
                        val sex = cursor.getInt(sexIndex)
                        val skin = cursor.getInt(skinIndex)
                        val race = cursor.getInt(raceIndex)
                        val singleFaceInfo = SingleFaceInfo()
                        singleFaceInfo.mGroupId = groupId
                        singleFaceInfo.mRectTop = rectTop
                        singleFaceInfo.mRectBottom = rectBottom
                        singleFaceInfo.mRectLeft = rectLeft
                        singleFaceInfo.mRectRight = rectRight
                        singleFaceInfo.mAge = age
                        singleFaceInfo.mSex = sex
                        singleFaceInfo.mSkin = skin
                        singleFaceInfo.mRace = race
                        val path = cursor.getString(filePathIndex)
                        var statisticsInfo = pathFeatureMap[path]
                        if (statisticsInfo == null) {
                            statisticsInfo = FaceAttributeStatisticsInfo()
                            statisticsInfo.mSingleFaceList.add(singleFaceInfo)
                            statisticsInfo.mMediaId = cursor.getInt(mediaIdIndex)
                            statisticsInfo.mPath = path
                            pathFeatureMap[path] = statisticsInfo
                            mediaIdList.add(statisticsInfo.mMediaId)
                        } else {
                            statisticsInfo.mSingleFaceList.add(singleFaceInfo)
                        }
                    }
                }
            } catch (e: Exception) {
                GLog.e(TAG, "addFaceAttributeMap, Exception = $e")
            }
            GLog.v(TAG, "addFaceAttributeMap, pathFeatureMap size is " + pathFeatureMap.size)

            // collect picture information
            val whereBuilder = StringBuilder()
            whereBuilder.append(MediaStore.Images.Media._ID + " IN (")
            var i = 0
            val n = mediaIdList.size
            while (i < n) {
                whereBuilder.append(mediaIdList[i])
                if (i < mediaIdList.size - 1) {
                    whereBuilder.append(",")
                }
                i++
            }
            whereBuilder.append(")")
            val mediaProjection = arrayOf(
                GalleryStore.GalleryColumns.LocalColumns.TAGFLAGS,
                MediaStoreUtils.DATA,
                MediaStore.Images.Media.DATE_TAKEN,
                MediaStore.Images.Media.DISPLAY_NAME
            )
            val statisticsInfoList: MutableList<FaceAttributeStatisticsInfo> = ArrayList()
            var cursor: Cursor? = null
            try {
                cursor = QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(mediaProjection)
                    .setWhere(whereBuilder.toString())
                    .setConvert(CursorConvert())
                    .build().exec()
                if (cursor == null || cursor.count == 0) {
                    GLog.v(TAG, "addFaceAttributeMap, media cursor is null!")
                    return
                }
                val tagFlagIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.TAGFLAGS)
                val pathIndex = cursor.getColumnIndex(MediaStoreUtils.DATA)
                val dateTakenIndex = cursor.getColumnIndex(MediaStore.Images.Media.DATE_TAKEN)
                val displayNameIndex = cursor.getColumnIndex(MediaStore.Images.Media.DISPLAY_NAME)
                while (cursor.moveToNext()) {
                    val path = cursor.getString(pathIndex)
                    val statisticsInfo = pathFeatureMap[path]
                    if (statisticsInfo != null) {
                        statisticsInfo.title = cursor.getString(displayNameIndex)
                        statisticsInfo.mCameraMode = cursor.getInt(tagFlagIndex)
                        statisticsInfo.mDateTaken = cursor.getLong(dateTakenIndex)
                        statisticsInfoList.add(statisticsInfo)
                    }
                }
            } catch (e: Exception) {
                GLog.e(TAG, "addFaceAttributeMap, Exception = $e")
            } finally {
                IOUtils.closeQuietly(cursor)
            }
            if (statisticsInfoList.size > 0) {
                UpdateReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setWhere(ScanFaceColumns.HANDLE_STATE + "=" + DETECTED_HANDLE_STATE)
                    .setConvert {
                        val values = ContentValues()
                        values.put(ScanFaceColumns.HANDLE_STATE, STATISTICS_HANDLE_STATE)
                        values
                    }.build().exec()
            }
            GLog.v(TAG, "addFaceAttributeMap end, cost time is " + (System.currentTimeMillis() - startTime))
        } finally {
            ComponentPrefUtils.setLongPref(context, ComponentPrefUtils.PREF_LAST_FACE_ATTRIBUTE_STATIC_KEY, System.currentTimeMillis())
        }
    }

    @JvmStatic
    fun trackScanResult(scanTrackInfo: ScanTrackInfo): Unit = track(EventId.SCAN_RESULT) {
        it.putProperty(Key.SCENE, scanTrackInfo.scene)
        it.putProperty(Key.IS_FIRST_SCAN, scanTrackInfo.isFirstScan.toString())
        it.putProperty(Key.SCAN_TRIGGER_TYPE, scanTrackInfo.scanTriggerType.toString())
        it.putProperty(Key.SCAN_START_TIME, scanTrackInfo.startTime.toString())
        it.putProperty(Key.SCAN_COST_TIME, (scanTrackInfo.endTime - scanTrackInfo.startTime).toString() + STR_MS)
        it.putProperty(Key.SCAN_START_BATTERY, ((scanTrackInfo.startBattery * NUM_100).toInt()).toString())
        it.putProperty(Key.SCAN_END_BATTERY, ((scanTrackInfo.endBattery * NUM_100).toInt()).toString())
        it.putProperty(Key.SCAN_IMAGE_COUNT, scanTrackInfo.scanImageCount.toString())
        it.putProperty(Key.SCAN_VIDEO_COUNT, scanTrackInfo.scanVideoCount.toString())
        it.putProperty(Key.START_TEMPERATURE, ((scanTrackInfo.startTemperature * NUM_10).toInt()).toString())
        it.putProperty(Key.END_TEMPERATURE, ((scanTrackInfo.endTemperature * NUM_10).toInt()).toString())
        it.putProperty(Key.START_NATIVE_HEAP_SIZE, scanTrackInfo.startNativeHeapSize)
        it.putProperty(Key.END_NATIVE_HEAP_SIZE, scanTrackInfo.endNativeHeapSize)
        it.putProperty(Key.REASON_TYPE, scanTrackInfo.reasonType.toString())
        it.putProperty(Key.IS_CHARGING, scanTrackInfo.isCharging.toString())
        if (scanTrackInfo.additionMap != null) {
            it.putProperty(Key.ADDITIONAL_INFO, scanTrackInfo.additionMap.toString())
        }
        it.save()
    }

    /**
     * 开始扫描前，埋点中断的原因。
     */
    @JvmStatic
    fun trackStopReasonOnStart(context: Context, scene: String, triggerType: Int) {
        val maxScanCount = GalleryScanMonitor.getCloud24HMaxImageScanCount()
        val reasonType = GalleryScanMonitor.getReasonTypeOnStart(context, false, 0, maxScanCount, GalleryScanUtils.DEFAULT_SCAN_TIME, false)
        trackScanStopReason(context, scene, triggerType, reasonType)
    }

    /**
     * 开始扫描前，埋点中断的原因。
     */
    @JvmStatic
    fun trackStopReasonOnStart(context: Context, scene: String, triggerType: Int, lastScanTime: Long, checkOnTop: Boolean) {
        val maxScanCount = GalleryScanMonitor.getCloud24HMaxImageScanCount()
        val reasonType = GalleryScanMonitor.getReasonTypeOnStart(context, false, 0, maxScanCount, lastScanTime, checkOnTop)
        trackScanStopReason(context, scene, triggerType, reasonType)
    }

    @JvmStatic
    private fun trackScanStopReason(context: Context, scene: String, triggerType: Int, reasonType: Int) {
        val time = System.currentTimeMillis()
        val battery = BatteryStatusUtil.getCurrentBatteryPercent(context)
        val temperature = getCurrentTemperature()
        val nativeHeapSize = Debug.getNativeHeapSize()
        val scanTrackInfo = ScanTrackInfo(
            scene,
            time,
            time,
            battery,
            battery,
            false,
            triggerType,
            0,
            0,
            temperature,
            temperature,
            nativeHeapSize,
            nativeHeapSize,
            reasonType,
            BatteryStatusUtil.isBatteryInCharging(false),
            null
        )
        trackScanResult(scanTrackInfo)
    }

    /**
     * 2006012011
     * @param curTime
     * @param triggerType
     * @return
     */
    @JvmStatic
    fun trackOtherScannerProtectStatistics(
        curTime: Long,
        triggerType: Int
    ): Unit = track(EventId.SCAN_OTHER_PROTECTED) {
        it.putProperty(Key.SCAN_START_TIME, curTime.toString())
        it.putProperty(Key.SCAN_TRIGGER_TYPE, triggerType.toString())
        it.save()
    }

    /**
     * 2006012007
     * @param count
     * @param type
     * @param videoCount
     * @return
     */
    @JvmStatic
    fun trackMemoriesScanResult(
        count: Int,
        type: Int,
        videoCount: Int
    ): Unit = track(EventId.MEMORIES_SCAN_RESULT) {
        it.putProperty(Key.MEMORIES_SCAN_RESULT, TRUE)
        it.putProperty(Key.MEMORIES_TYPE, type.toString())
        it.putProperty(Key.MEMORIES_TOTAL_COUNT, count.toString())
        it.putProperty(Key.MEMORIES_VIDEO_COUNT, videoCount.toString())
        it.save()
    }

    @JvmStatic
    fun trackRemoveFromLabel(
        mediaType: String,
        version: Int,
        labelId: Int,
        labelName: String,
        language: String,
        labelTotalCount: Int,
        labelAllRemoveCount: Int,
        labelRemoveCount: Int,
        labelInfo: String,
    ): Unit = track(USER_ACTIONLABEL_REMOVE, type = TYPE_PICTURE) {
        it.putProperty(Key.LABEL_FILE_TYPE, mediaType)
        it.putProperty(Key.LABEL_SDK_VERSION, version)
        it.putProperty(Key.LABEL_ID, labelId)
        it.putProperty(Key.LABEL_NAME, labelName)
        it.putProperty(Key.LABEL_NAME_LANGUAGE, language)
        it.putProperty(Key.LABEL_TOTAL_COUNT, labelTotalCount)
        it.putProperty(Key.LABEL_REMOVE_COUNT, labelAllRemoveCount)
        it.putProperty(Key.LABEL_COUNT, labelRemoveCount)
        it.putProperty(Key.LABEL_FILE_INFO, labelInfo)
        it.save()
    }

    private fun track(eventId: String, type: String = TYPE_SCAN, @WorkerThread func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
            event = eventId,
            type = type,
            func = func
        )
    }

    private class FaceAttributeStatisticsInfo {
        var mPath: String? = null
        var title: String? = null
        var mMediaId = 0
        var mCameraMode = 0
        var mDateTaken: Long = 0
        var mSingleFaceList: MutableList<SingleFaceInfo> = ArrayList()
        val directoryPath: String?
            get() = if (TextUtils.isEmpty(mPath) || TextUtils.isEmpty(title)) {
                null
            } else mPath!!.replace(title!!, "")
        val createTime: String
            get() = sDateFormatter.format(mDateTaken)
        val preCamera: Int
            get() = if (mCameraMode and FLAG_PRE_CAMERA != 0) 1 else 0
        val beautyCamera: Int
            get() = if (mCameraMode and FLAG_BEAUTY_CAMERA != 0) 1 else 0
        val faceInfo: String
            get() {
                if (mSingleFaceList.size > MAX_FACE_STATISTICS_LEN) {
                    mSingleFaceList = mSingleFaceList.subList(0, MAX_FACE_STATISTICS_LEN)
                }
                val faceInfoBuilder = StringBuilder()
                var i = 0
                val n = mSingleFaceList.size
                while (i < n) {
                    val faceInfo = mSingleFaceList[i]
                    faceInfoBuilder.append(faceInfo.mGroupId)
                    faceInfoBuilder.append(SYMBOL_VERTICAL_LINE)
                    faceInfoBuilder.append(faceInfo.mAge)
                    faceInfoBuilder.append(SYMBOL_VERTICAL_LINE)
                    faceInfoBuilder.append(faceInfo.mSex)
                    faceInfoBuilder.append(SYMBOL_VERTICAL_LINE)
                    faceInfoBuilder.append(faceInfo.mRace)
                    faceInfoBuilder.append(SYMBOL_VERTICAL_LINE)
                    faceInfoBuilder.append(faceInfo.mSkin)
                    if (i < mSingleFaceList.size - 1) {
                        faceInfoBuilder.append(",")
                    }
                    i++
                }
                return faceInfoBuilder.toString()
            }

        companion object {
            private const val FLAG_PRE_CAMERA = 0x01
            private const val FLAG_BEAUTY_CAMERA = 0x02
        }
    }

    private class SingleFaceInfo : Comparable<SingleFaceInfo> {
        var mGroupId = 0
        var mSex = 0
        var mAge = 0
        var mRace = 0
        var mSkin = 0
        var mRectTop = 0
        var mRectLeft = 0
        var mRectRight = 0
        var mRectBottom = 0
        private val faceArea: Int
            get() = (mRectBottom - mRectTop) * (mRectRight - mRectLeft)

        override fun compareTo(other: SingleFaceInfo): Int {
            return -(faceArea - other.faceArea)
        }
    }
}