/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ScanTrackItem.kt
 * Description: 扫描埋点数据类
 * Version: 1.0
 * Date: 2022/4/19
 ** Author: <EMAIL>
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>      2022/4/19     1.0
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.scan.helpers

data class ScanTrackInfo(
    val scene: String,
    val startTime: Long,
    val endTime: Long,
    val startBattery: Float,
    val endBattery: Float,
    val isFirstScan: Boolean,
    val scanTriggerType: Int,
    val scanImageCount: Int,
    val scanVideoCount: Int,
    val startTemperature: Float,
    val endTemperature: Float,
    val startNativeHeapSize: Long,
    val endNativeHeapSize: Long,
    val reasonType: Int,
    val isCharging: Boolean,
    val additionMap: Map<String, String>?
)