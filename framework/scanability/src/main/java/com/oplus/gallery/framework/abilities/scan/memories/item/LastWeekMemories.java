/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LastWeekMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/13
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  hailong.zhang@Apps.Gallery3D   2018/3/13     1.0          build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;

public class LastWeekMemories extends Memories {
    private static final String TAG = "LastWeekMemories";
    private static final String STOP_REASON_HAS_CREATED = TAG + "_HasCreated";
    private static final String STOP_REASON_LESS_THAN_1_WEEK = TAG + "_LessThan1Week";
    private static final String STOP_REASON_SCAN_SIZE_NOT_ENOUGH = TAG + "_ScanSizeNotEnough";

    public LastWeekMemories(Context context) {
        super(context);
    }

    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_DATE_LAST_WEEK;
    }

    @Override
    public boolean prepareMemories() {
        if (MemoriesProviderHelper.hasMemoriesCreatedInThisWeek(getMemoriesGroupType())) {
            GLog.v(TAG, "prepareMemories, optimal date memories has created in this week!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_HAS_CREATED;
            return false;
        }
        long lastMemoriesTime = MemoriesProviderHelper.getLastSpecifiedMemoriesTime(getMemoriesId());
        if ((lastMemoriesTime != 0) && (MemoriesScannerHelper.getWeeksBetweenTwoDates(lastMemoriesTime,
                System.currentTimeMillis()) < MemoriesScannerHelper.LAST_WEEK_MEMORIES_INTERVAL)) {
            GLog.d(TAG, "prepareMemories, current time interval last LastWeekMemories time less than 1 week!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_LESS_THAN_1_WEEK;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        MemoriesScannerHelper.DateRange itemDateRange = MemoriesScannerHelper.getWeekRange(MemoriesUtils.LAST_WEEK_OFFSET);
        ArrayList<MediaItem> itemList = MemoriesProviderHelper.getItemListFromLocalMedia(mContext, itemDateRange, mMemoriesPicMin);
        GLog.v(TAG, "scanMemories itemList size:" + itemList.size());
        //filter specified labels
        mMediaItemList = MemoriesProviderHelper.getFilteredItemList(itemList, getLabelFilterIdList());
        GLog.v(TAG, "scanMemories filteredItemList size:" + mMediaItemList.size());
        if ((mMediaItemList == null) || (mMediaItemList.size() <= mMemoriesPicMin)) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            return false;
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return createMemories(mMediaItemList);
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.DATE_LAST_WEEK_MEMORIES;
    }
}
