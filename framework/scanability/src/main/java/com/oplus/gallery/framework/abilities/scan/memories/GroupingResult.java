/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GroupingResult.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2017/12/07
 ** Author:<PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Rom.Apps.Gallery         2017/12/07   1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories;

import com.oplus.gallery.business_lib.model.data.memories.bean.FeatureResultEntity;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import co.polarr.processing.entities.ResultItem;

public class GroupingResult {
    public List<List<ResultItem>> mOptFiles = new ArrayList<>();
    public List<ResultItem> mBadFiles = new ArrayList<>();
    public List<ResultItem> mDroppedFiles = new ArrayList<>();
    public ResultItem mBestItem;

    public boolean available() {
        return (mOptFiles != null) && (mOptFiles.size() > 0);
    }

    public FeatureResult getBestItem() {
        if (mBestItem != null) {
            return new FeatureResult(mBestItem);
        }
        return null;
    }

    public String getBestItemPath() {
        if (mBestItem != null) {
            return new FeatureResult(mBestItem).getFilePath();
        }
        return null;
    }

    public void getItems(Map<String, FeatureResultEntity> featureMap) {
        int tagId = 1;
        for (List<ResultItem> list : mOptFiles) {
            if ((list != null) && (list.size() > 0)) {
                int index = 0;
                for (ResultItem item : list) {
                    FeatureResult fr = new FeatureResult(item);
                    if (index++ == 0) {
                        fr.setInVideo(true);
                    }
                    fr.setTagId(tagId);
                    FeatureResultEntity entity = new FeatureResultEntity(fr.getRatingAll(),fr.isInVideo(),fr.getTagId());
                    featureMap.put(fr.getFilePath(), entity);
                }
            }
            tagId++;
        }
        if (mBadFiles != null) {
            for (ResultItem item : mBadFiles) {
                FeatureResult fr = new FeatureResult(item);
                fr.setInVideo(false);
                fr.setTagId(0);
                FeatureResultEntity entity = new FeatureResultEntity(fr.getRatingAll(),fr.isInVideo(),fr.getTagId());
                featureMap.put(fr.getFilePath(), entity);
            }
        }
        if (mDroppedFiles != null) {
            for (ResultItem item : mDroppedFiles) {
                FeatureResult fr = new FeatureResult(item);
                fr.setInVideo(false);
                fr.setTagId(0);
                FeatureResultEntity entity = new FeatureResultEntity(fr.getRatingAll(),fr.isInVideo(),fr.getTagId());
                featureMap.put(fr.getFilePath(), entity);
            }
        }
    }

    public void debug(String tag, String method) {
        try {
            if (mOptFiles != null) {
                int index = 0;
                for (List<ResultItem> list : mOptFiles) {
                    if (list == null) {
                        continue;
                    }
                    GLog.d(tag, method + "_opt, index=" + index++ + ", count=" + list.size());
                    for (ResultItem item : list) {
                        GLog.d(tag, method + "_opt, feature: " + new FeatureResult(item).toString());
                    }
                }
            }
            if (mBadFiles != null) {
                for (ResultItem item : mBadFiles) {
                    GLog.d(tag, method + "bad, feature: " + new FeatureResult(item).toString());
                }
            }

            if (mDroppedFiles != null) {
                for (ResultItem item : mDroppedFiles) {
                    GLog.d(tag, method + "dropped, feature: " + new FeatureResult(item).toString());
                }
            }
        } catch (Exception e) {
            GLog.d("GroupingResult", "debug, e=" + e);
        }
    }
}
