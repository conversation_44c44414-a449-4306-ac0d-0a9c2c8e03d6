/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OCloudSyncScanner.kt
 ** Description: 云同步下载任务
 ** Version: 1.0
 ** Date: 2024/5/7
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>             2024/5/7     1.0		   create
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.ocloudsync

import android.content.Context
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.CloudSync.AlbumSyncing.HAVE_BULK_CLOUD_FILES_NEED_TO_DOWNLOAD
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_TYPE_BACKGROUND_DOWNLOAD

/**
 * 云同步下载任务
 *
 * 首次同步(或者重新关开云同步)，有大量待下载的文件时，需要在后台下载
 * 会下载完大缩图(或者剩下的不足100张)
 */
class OCloudSyncScanner(context: Context) : GalleryScan(context) {

    override fun getScanType(): Int {
        return O_CLOUD_SYNC_SCAN
    }

    override fun getSceneName(): String = TAG

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        val startTime = System.currentTimeMillis()
        GLog.w(TAG, "onScan, triggerType = $triggerType")
        if (canStartCloudSync()) {
            ApiDmManager.getCloudSyncDM().executeCloudSync(SYNC_TYPE_BACKGROUND_DOWNLOAD)
            GLog.w(TAG, "onScan, scan end. cost = ${GLog.getTime(startTime)}ms")
        }
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }

    /**
     * 是否可以开始同步
     *
     * 满足以下条件可以开始：
     * - 首次同步(或者重新关开云同步)，有大量待下载的文件
     * - 息屏充电，温度和电量适宜
     */
    private fun canStartCloudSync(): Boolean {
        val haveBulkFileNeedToDownload = ConfigAbilityWrapper.getBoolean(HAVE_BULK_CLOUD_FILES_NEED_TO_DOWNLOAD, false)
        return (haveBulkFileNeedToDownload && GalleryScanMonitor.isAllowStartScan(mContext, true, false)).also {
            GLog.d(TAG, "canStartCloudSync, $it")
        }
    }

    companion object {
        private const val TAG = "OCloudSyncScanner"
    }
}