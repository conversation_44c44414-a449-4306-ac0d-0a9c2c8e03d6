/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - .java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/12
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D    2018/3/12     1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories;

import android.content.Context;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.bean.MemoriesConfigInfo;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.framework.abilities.scan.memories.item.BabyMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.BirthdayMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.DayLastYearMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.FestivalMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.LastMonthMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.LastThreeMonthMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.LastThreeWeekMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.LastTwoWeekMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.LastWeekMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.LastYearFestivalMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.OneDayMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.OneYearMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.PetMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.SinglePersonFaceMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.ThreePersonMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.TravelMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.TwoPersonFaceMemories;
import com.oplus.gallery.framework.abilities.scan.memories.item.WeddingMemories;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;

public enum MemoriesFactory {
    INSTANCE;
    private static final String TAG = "MemoriesFactory";

    private static final int BIRTHDAY_MEMORIES_HIGH_PRIORITY = 1;
    private static final int WEDDING_MEMORIES_HIGH_PRIORITY = 2;
    private static final int FESTIVAL_MEMORIES_HIGH_PRIORITY = 3;
    private static final int LAST_YEAR_FESTIVAL_MEMORIES_HIGH_PRIORITY = 4;
    private static final int ONE_YEAR_MEMORIES_HIGH_PRIORITY = 5;
    private static final int DAY_LAST_YEAR_MEMORIES_HIGH_PRIORITY = 6;
    private static final int ONE_DAY_MEMORIES_NORMAL_PRIORITY = 1;
    private static final int LAST_WEEK_MEMORIES_NORMAL_PRIORITY = 2;
    private static final int LAST_TWO_WEEK_MEMORIES_NORMAL_PRIORITY = 3;
    private static final int LAST_THREE_WEEK_MEMORIES_NORMAL_PRIORITY = 4;
    private static final int LAST_MONTH_MEMORIES_NORMAL_PRIORITY = 5;
    private static final int LAST_THREE_MONTH_MEMORIES_NORMAL_PRIORITY = 6;
    private static final int TRAVEL_MEMORIES_NORMAL_PRIORITY = 7;
    private static final int BABY_MEMORIES_NORMAL_PRIORITY = 8;
    private static final int SINGLE_PERSON_FACE_MEMORIES_NORMAL_PRIORITY = 9;
    private static final int TWO_PERSON_FACE_MEMORIES_NORMAL_PRIORITY = 10;
    private static final int THREE_PERSON_MEMORIES_NORMAL_PRIORITY = 11;
    private static final int ONE_YEAR_MEMORIES_NORMAL_PRIORITY = 12;
    private static final int WEDDING_MEMORIES_NORMAL_PRIORITY = 13;
    private static final int BIRTHDAY_MEMORIES_NORMAL_PRIORITY = 14;
    private static final int FESTIVAL_MEMORIES_NORMAL_PRIORITY = 15;
    private static final int PET_MEMORIES_NORMAL_PRIORITY = 16;
    private Context mContext;
    private ArrayList<MemoriesConfigInfo> mConfigInfos;

    MemoriesFactory() {
        mContext = ContextGetter.context;
        loadConfigInfo(mContext);
    }

    private void loadConfigInfo(Context context) {
        mConfigInfos = new ArrayList<>();
        // high priority
        mConfigInfos.add(new MemoriesConfigInfo(BirthdayMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, BIRTHDAY_MEMORIES_HIGH_PRIORITY, -1));
        mConfigInfos.add(new MemoriesConfigInfo(WeddingMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, WEDDING_MEMORIES_HIGH_PRIORITY, -1));
        mConfigInfos.add(new MemoriesConfigInfo(FestivalMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, FESTIVAL_MEMORIES_HIGH_PRIORITY, -1));
        mConfigInfos.add(new MemoriesConfigInfo(LastYearFestivalMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, LAST_YEAR_FESTIVAL_MEMORIES_HIGH_PRIORITY, -1));
        mConfigInfos.add(new MemoriesConfigInfo(OneYearMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, ONE_YEAR_MEMORIES_HIGH_PRIORITY, -1));
        mConfigInfos.add(new MemoriesConfigInfo(DayLastYearMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, DAY_LAST_YEAR_MEMORIES_HIGH_PRIORITY, -1));

        // normal priority
        if (FeatureUtils.isSupportMemoriesInternational()) {
            mConfigInfos.add(new MemoriesConfigInfo(OneDayMemories.class.getName(),
                    MemoriesType.DATE_MEMORIES_TYPE, -1, ONE_DAY_MEMORIES_NORMAL_PRIORITY));
        }
        mConfigInfos.add(new MemoriesConfigInfo(LastWeekMemories.class.getName(),
                MemoriesType.DATE_MEMORIES_TYPE, -1, LAST_WEEK_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(LastTwoWeekMemories.class.getName(),
                MemoriesType.DATE_MEMORIES_TYPE, -1, LAST_TWO_WEEK_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(LastThreeWeekMemories.class.getName(),
                MemoriesType.DATE_MEMORIES_TYPE, -1, LAST_THREE_WEEK_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(LastMonthMemories.class.getName(),
                MemoriesType.DATE_MEMORIES_TYPE, -1, LAST_MONTH_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(LastThreeMonthMemories.class.getName(),
                MemoriesType.DATE_MEMORIES_TYPE, -1, LAST_THREE_MONTH_MEMORIES_NORMAL_PRIORITY));
        if (FeatureUtils.isSupportMemoriesInternational()) {
            mConfigInfos.add(new MemoriesConfigInfo(TravelMemories.class.getName(),
                    MemoriesType.TRAVEL_MEMORIES_TYPE, -1, TRAVEL_MEMORIES_NORMAL_PRIORITY));
        }
        mConfigInfos.add(new MemoriesConfigInfo(BabyMemories.class.getName(),
                MemoriesType.FACE_MEMORIES_TYPE, -1, BABY_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(SinglePersonFaceMemories.class.getName(),
                MemoriesType.FACE_MEMORIES_TYPE, -1, SINGLE_PERSON_FACE_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(TwoPersonFaceMemories.class.getName(),
                MemoriesType.FACE_MEMORIES_TYPE, -1, TWO_PERSON_FACE_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(ThreePersonMemories.class.getName(),
                MemoriesType.FACE_MEMORIES_TYPE, -1, THREE_PERSON_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(OneYearMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, -1, ONE_YEAR_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(WeddingMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, -1, WEDDING_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(BirthdayMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, -1, BIRTHDAY_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(FestivalMemories.class.getName(),
                MemoriesType.EVENT_MEMORIES_TYPE, -1, FESTIVAL_MEMORIES_NORMAL_PRIORITY));
        mConfigInfos.add(new MemoriesConfigInfo(PetMemories.class.getName(),
                MemoriesType.PET_MEMORIES_TYPE, -1, PET_MEMORIES_NORMAL_PRIORITY));
    }

    private Memories createMemories(String className) {
        Memories memories = null;
        if (!TextUtils.isEmpty(className)) {
            try {
                Class<?> scanClass = Class.forName(className);
                Constructor scanConstructor = scanClass.getDeclaredConstructor(Context.class);
                scanConstructor.setAccessible(true);
                Object object = scanConstructor.newInstance(mContext);
                if (object instanceof Memories) {
                    memories = (Memories) object;
                }
            } catch (ClassNotFoundException | IllegalArgumentException | IllegalAccessException | NoSuchMethodException
                    | InvocationTargetException | InstantiationException e) {
                GLog.w(TAG, "createGalleryScan error e =" + e.getMessage());
            }
        }
        GLog.d(TAG, "createMemories memories  =" + memories);
        return memories;
    }

    public ArrayList<Memories> getAllMemories() {
        ArrayList<Memories> allMemories = new ArrayList<>();
        for (MemoriesConfigInfo info : mConfigInfos) {
            Memories memories = createMemories(info.getMemoriesClassName());
            if (memories != null) {
                memories.setMemoriesGroupType(info.getMemoriesGroupType());
                memories.setNormalPriority(info.getNormalPriority());
                memories.setHighPriority(info.getHighPriority());
                memories.setMemoriesName(MemoriesNameRules.createDefaultMemoriesName(mContext, null, memories.getNameType()));
                allMemories.add(memories);
            }
        }
        return filterMemories(allMemories);
    }

    private ArrayList<Memories> filterMemories(ArrayList<Memories> originalMemories) {
        Collections.sort(originalMemories);
        ArrayList<Memories> filterMemories = new ArrayList<>();
        for (int i = 0; i < originalMemories.size(); i++) {
            Memories memory = originalMemories.get(i);
            if (memory.getHighPriority() > 0) {
                filterMemories.add(memory);
                originalMemories.remove(i);
                i--;
            }
        }
        int lastGroupId = MemoriesUtils.getLastMemoriesCreatedGroupType(mContext);
        GLog.d(TAG, "filterMemories lastGroupId  =" + lastGroupId);
        for (int i = 0; i < originalMemories.size(); i++) {
            Memories memory = originalMemories.get(i);
            if (memory.getMemoriesGroupType() > lastGroupId) {
                filterMemories.add(memory);
                originalMemories.remove(i);
                i--;
            }
        }
        filterMemories.addAll(originalMemories);
        return filterMemories;
    }
}