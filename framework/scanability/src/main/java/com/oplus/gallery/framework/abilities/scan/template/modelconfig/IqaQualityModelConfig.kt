/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IqaQualityModelConfig.kt
 ** Description : 质量美学ModelConfig
 ** Version     : 1.0
 ** Date        : 2024/3/22
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/3/22      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * 质量美学ModelConfig
 */
class IqaQualityModelConfig(context: Context) : SeniorSelectModelConfig(context, ModelName.QUALITY_SOURCE) {

    override val builtInVersionKey = IQA_QUALITY_COMPONENT_DEFAULT_VERSION_NAME

    override val modelFileName: String = IQA_QUALITY_MODEL_NAME

    companion object {
         /**
         * 从manifest.ini中读取版本号时的key
         */
        const val IQA_QUALITY_COMPONENT_DEFAULT_VERSION_NAME = "iqa_quality_component_default_version"

        /**
         * 加载模型文件
         */
        private const val IQA_QUALITY_MODEL_NAME = "iqa_quality.model"
    }
}