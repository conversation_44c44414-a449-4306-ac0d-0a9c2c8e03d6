/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ScanHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/01/08
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2021/01/08		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.helpers

import android.content.Context
import android.database.Cursor
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesHelper
import com.oplus.gallery.business_lib.model.search.cloud.LabelCloudHelper
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns._ID
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA_SPACE
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_ID
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.GROUP_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.IN
import com.oplus.gallery.foundation.database.util.SQLGrammar.INNER_JOIN
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.LIMIT
import com.oplus.gallery.foundation.database.util.SQLGrammar.NOT_EQUAL_ONE
import com.oplus.gallery.foundation.database.util.SQLGrammar.ON
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT
import com.oplus.gallery.foundation.database.util.SQLGrammar.TRIM_BIT_AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.TRIM_EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

object ScanUserAssetsHelper {
    private const val TAG = "ScanHelper"
    private val LABEL_STATICS_PROJECTION = arrayOf(GalleryStore.ScanLabel.SCENE_ID, "count(_id)")

    /**
     * 上报的标签每个类型最多30条
     */
    private const val LABELS_COUNT_LIMIT = "30"

    /**
     * 个性化定制过滤Olive图
     */
    private const val OLIVE_BASE_WHERE_CLAUSE = (LEFT_BRACKETS + LocalColumns.TAGFLAGS + GREATER_THAN_ZERO
            + AND
            + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.MEDIA_TYPE + EQUAL + LocalColumns.MEDIA_TYPE_IMAGE
            + AND
            + LocalColumns.TAGFLAGS + TRIM_BIT_AND + Constants.CameraMode.FLAG_SUPPORT_OLIVE
            + EQUAL
            + Constants.CameraMode.FLAG_SUPPORT_OLIVE + RIGHT_BRACKETS)

    /**
     * 检测数据的有效性
     */
    private const val DATA_VALID_WHERE = (LEFT_BRACKETS + GalleryStore.ScanLabel.TAB + DOT + LocalColumns.INVALID + IN
            + LEFT_BRACKETS + LocalColumns.INVALID_NORMAL + COMMA + LocalColumns.INVALID_NORMAL_NOT_IN_MEDIA_STORE + RIGHT_BRACKETS
            + RIGHT_BRACKETS)

    fun getImageLabelTrack(): String {
        val whereBuilder = StringBuilder()
        whereBuilder.append(DatabaseUtils.getDataValidWhere())
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.ScanLabel.IS_RECYCLED + " != 1")
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.ScanLabel.MEDIA_TYPE + " == " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
        whereBuilder.append(" ) GROUP BY " + "(" + GalleryStore.ScanLabel.SCENE_ID)
        val sb = StringBuilder()
        try {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .setProjection(LABEL_STATICS_PROJECTION)
                .setWhere(whereBuilder.toString())
                .setConvert(CursorConvert())
                .build().exec()?.use {
                    val sceneIdIndex = it.getColumnIndex(GalleryStore.ScanLabel.SCENE_ID)
                    val countIndex = it.getColumnIndex("count(_id)")
                    var index = 0
                    while (it.moveToNext()) {
                        val sceneId = it.getLong(sceneIdIndex)
                        val count = it.getLong(countIndex)
                        val labelName = LabelDictionary.getLabelName(sceneId.toInt())
                        if (TextUtils.isEmpty(labelName)) {
                            continue
                        }
                        if (index++ > 0) {
                            sb.append(CommonTrackConstant.SYMBOL_VERTICAL_LINE)
                        }
                        sb.append(sceneId).append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE)
                            .append(count)
                    }
                }
        } catch (e: Exception) {
            GLog.e(TAG, "statisticsImageLabelMap, get Label query failed!", e)
        }
        return sb.toString()
    }

    fun getImageLabelCountTrack(): Int {
        val whereBuilder = StringBuilder()
        whereBuilder.append(DatabaseUtils.getDataValidWhere())
        whereBuilder.append(AND)
        whereBuilder.append(GalleryStore.ScanLabel.IS_RECYCLED + NOT_EQUAL_ONE)
        whereBuilder.append(AND)
        whereBuilder.append(GalleryStore.ScanLabel.MEDIA_TYPE + EQUAL + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
        whereBuilder.append(RIGHT_BRACKETS + GROUP_BY + LEFT_BRACKETS + GalleryStore.ScanLabel.SCENE_ID)
        var sum = 0
        return runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .setProjection(LABEL_STATICS_PROJECTION)
                .setWhere(whereBuilder.toString())
                .setConvert(CursorConvert())
                .build().exec()?.use {
                    val sceneIdIndex = it.getColumnIndex(GalleryStore.ScanLabel.SCENE_ID)
                    val countIndex = it.getColumnIndex(COUNT_ID)
                    while (it.moveToNext()) {
                        val sceneId = it.getLong(sceneIdIndex)
                        val count = it.getLong(countIndex)
                        val labelName = LabelDictionary.getLabelName(sceneId.toInt())
                        if (TextUtils.isEmpty(labelName)) {
                            continue
                        }
                        sum += count.toInt()
                    }
                }
            sum
        }.onFailure {
            GLog.e(TAG, "getVideoLabelCountTrack, get Label query failed!", it)
        }.getOrDefault(0)
    }

    fun getVideoLabelCountTrack(): Int {
        val whereBuilder = StringBuilder()
        whereBuilder.append(GalleryStore.ScanLabel.INVALID + NOT_EQUAL_ONE)
        whereBuilder.append(AND)
        whereBuilder.append(GalleryStore.ScanLabel.IS_RECYCLED + NOT_EQUAL_ONE)
        whereBuilder.append(AND)
        whereBuilder.append(GalleryStore.ScanLabel.MEDIA_TYPE + EQUAL + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)
        whereBuilder.append(RIGHT_BRACKETS + GROUP_BY + LEFT_BRACKETS + GalleryStore.ScanLabel.SCENE_ID)
        var sum = 0
        return runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .setProjection(LABEL_STATICS_PROJECTION)
                .setWhere(whereBuilder.toString())
                .setConvert(CursorConvert())
                .build().exec()?.use {
                    val sceneIdIndex = it.getColumnIndex(GalleryStore.ScanLabel.SCENE_ID)
                    val countIndex = it.getColumnIndex(COUNT_ID)
                    while (it.moveToNext()) {
                        val sceneId = it.getLong(sceneIdIndex)
                        val count = it.getLong(countIndex)
                        val labelName = LabelDictionary.getLabelName(sceneId.toInt())
                        if (TextUtils.isEmpty(labelName)) {
                            continue
                        }
                        sum += count.toInt()
                    }
                }
            sum
        }.onFailure {
            GLog.e(TAG, "getVideoLabelCountTrack, get Label query failed!", it)
        }.getOrDefault(0)
    }

    fun getVideoLabelTrack(): String {
        val whereBuilder = StringBuilder()
        whereBuilder.append(GalleryStore.ScanLabel.INVALID.toString() + " != 1")
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.ScanLabel.IS_RECYCLED.toString() + " != 1")
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.ScanLabel.MEDIA_TYPE.toString() + " == " + GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)
        whereBuilder.append(" ) GROUP BY " + "(" + GalleryStore.ScanLabel.SCENE_ID)
        val sb = StringBuilder()
        try {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .setProjection(LABEL_STATICS_PROJECTION)
                .setWhere(whereBuilder.toString())
                .setConvert(CursorConvert())
                .build().exec()?.use {
                    val sceneIdIndex = it.getColumnIndex(GalleryStore.ScanLabel.SCENE_ID)
                    val countIndex = it.getColumnIndex("count(_id)")
                    var index = 0
                    while (it.moveToNext()) {
                        if (index++ > 0) {
                            sb.append(CommonTrackConstant.SYMBOL_VERTICAL_LINE)
                        }
                        val sceneId = it.getLong(sceneIdIndex)
                        val count = it.getLong(countIndex)
                        val labelName = LabelDictionary.getLabelName(sceneId.toInt())
                        if (TextUtils.isEmpty(labelName)) {
                            continue
                        }
                        sb.append(sceneId).append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE)
                            .append(count)
                    }
                }
        } catch (e: Exception) {
            GLog.e(TAG, "getVideoLabelTrack, get Label query failed!", e)
        }
        return sb.toString()
    }

    fun getAllOcrPictureCount(): Int {
        val whereBuilder = StringBuilder()
        whereBuilder.append(GalleryStore.OcrPagesColumns.INVALID.toString() + " != 1")
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.OcrPagesColumns.IS_RECYCLED.toString() + " != 1")
        val projection = arrayOf("count(_data)")
        return QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.OCR_PAGES)
            .setProjection(projection)
            .setWhere(whereBuilder.toString())
            .setConvert(CursorConvert())
            .build().exec()?.use {
                if (it.moveToNext()) {
                    return@use it.getInt(0)
                } else null
            } ?: 0
    }

    fun getCloudOcrPictureCount(): Int {
        val whereBuilder = StringBuilder()
        whereBuilder.append(GalleryStore.OcrPagesColumns.INVALID + " != 1")
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.OcrPagesColumns.IS_RECYCLED + " != 1")
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.ScanLabel.IS_SYNC + " = " + LabelCloudHelper.HAS_UPDATED_CLOUD)
        val projection = arrayOf("count(_data)")
        return QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.OCR_PAGES)
            .setProjection(projection)
            .setWhere(whereBuilder.toString())
            .setConvert(CursorConvert())
            .build().exec()?.use {
                if (it.moveToNext()) {
                    return@use it.getInt(0)
                } else null
            } ?: 0
    }

    fun getMemoriesPropertyTrack(context: Context?): String {
        val videoCountMap = MemoriesHelper.getAllMemoriesVideoCountMap()
        val memoriesEntries = MemoriesHelper.loadAllMemoriesEntries(context)
        val sb = StringBuilder()
        memoriesEntries.forEachIndexed { index, entry ->
            if (index > 0) {
                sb.append(CommonTrackConstant.SYMBOL_VERTICAL_LINE)
            }
            val memoriesType = entry.mMemoriesType
            val memoriesName = entry.mMemoriesName
            val dataTime = entry.mMemoriesTaken
            val memoriesCount = entry.mCount
            var videoCount = 0
            val videoCountInteger = videoCountMap[entry.mMemoriesId]
            if (videoCountInteger != null) {
                videoCount = videoCountInteger
            }
            sb.append(memoriesType)
                .append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE)
                .append(memoriesName)
                .append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE)
                .append(dataTime)
                .append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE)
                .append(memoriesCount)
                .append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE)
                .append(videoCount)
        }
        return sb.toString()
    }

    /**
     * 通过MediaType或者Flags获取标签
     *
     * @param mediaType 媒体类型：图片、视频
     * @return 标签，格式为 "a,b,c,d"
     */
    fun getLabelsByMediaTypeTrack(mediaType: Int): String {
        val whereBuilder = StringBuilder()
        whereBuilder.append(DatabaseUtils.getDataValidWhere())
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.ScanLabel.IS_RECYCLED + " != 1")
        whereBuilder.append(" AND ")
        whereBuilder.append(GalleryStore.ScanLabel.MEDIA_TYPE + " == " + mediaType)
        whereBuilder.append(" ) GROUP BY " + "(" + GalleryStore.ScanLabel.SCENE_ID)
        val sb = StringBuilder()
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .setProjection(LABEL_STATICS_PROJECTION)
                .setWhere(whereBuilder.toString())
                .setLimit(LABELS_COUNT_LIMIT)
                .setConvert(CursorConvert())
                .build().exec()?.use {
                    val sceneIdIndex = it.getColumnIndex(GalleryStore.ScanLabel.SCENE_ID)
                    var index = 0
                    while (it.moveToNext()) {
                        val sceneId = it.getLong(sceneIdIndex)
                        val labelName = LabelDictionary.getLabelName(sceneId.toInt())
                        if (TextUtils.isEmpty(labelName)) {
                            continue
                        }
                        if (index++ > 0) {
                            sb.append(CommonTrackConstant.SYMBOL_COMMA)
                        }
                        sb.append(labelName)
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "getLabelsByMediaTypeTrack, get Label query failed!", it)
        }
        return sb.toString()
    }

    /**
     * 获取Olive标签,格式为 "a,b,c,d"
     */
    fun getOliveLabelsTrack(): String {
        val stringBuilder = StringBuilder()

        val whereClause = StringBuilder()
        whereClause.append(DATA_VALID_WHERE)
        whereClause.append(AND)
        whereClause.append(GalleryStore.ScanLabel.IS_RECYCLED + NOT_EQUAL_ONE)
        whereClause.append(AND)
        whereClause.append(OLIVE_BASE_WHERE_CLAUSE)
        whereClause.append(GROUP_BY + GalleryStore.ScanLabel.SCENE_ID)
        whereClause.append(LIMIT + LABELS_COUNT_LIMIT)

        stringBuilder.append(SELECT)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + DOT + _ID + COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.ScanLabel.MEDIA_TYPE + COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA + COMMA_SPACE)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.TAGFLAGS + COMMA_SPACE)
        stringBuilder.append(GalleryStore.ScanLabel.TAB + DOT + GalleryStore.ScanLabel.SCENE_ID + COMMA_SPACE)
        stringBuilder.append(GalleryStore.ScanLabel.TAB + DOT + LocalColumns.INVALID + COMMA_SPACE)
        stringBuilder.append(GalleryStore.ScanLabel.TAB + DOT + GalleryStore.ScanLabelColumns.IS_RECYCLED + FROM)
        stringBuilder.append(GalleryStore.GalleryMedia.TAB + INNER_JOIN + GalleryStore.ScanLabel.TAB + ON)
        stringBuilder.append(
            GalleryStore.GalleryMedia.TAB + DOT + ScanFaceColumns.DATA + TRIM_EQUAL
                    + GalleryStore.ScanLabel.TAB + DOT + ScanFaceColumns.DATA
        )
        stringBuilder.append(WHERE)
        stringBuilder.append(whereClause)

        val sb = StringBuilder()
        val rawQueryReq = RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(CursorConvert())
            .setQuerySql(stringBuilder.toString())
            .setSqlArgs(null)
            .build()
        kotlin.runCatching {
            DataAccess.getAccess().rawQuery(rawQueryReq)?.use { cursor ->
                val sceneIdIndex = cursor.getColumnIndex(GalleryStore.ScanLabel.SCENE_ID)
                var index = 0
                while (cursor.moveToNext()) {
                    val sceneId = cursor.getLong(sceneIdIndex)
                    val labelName = LabelDictionary.getLabelName(sceneId.toInt())
                    if (TextUtils.isEmpty(labelName)) {
                        continue
                    }
                    if (index++ > 0) {
                        sb.append(CommonTrackConstant.SYMBOL_COMMA)
                    }
                    sb.append(labelName)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "getOliveLabelsTrack, get Label query failed!", it)
        }
        return sb.toString()
    }
}