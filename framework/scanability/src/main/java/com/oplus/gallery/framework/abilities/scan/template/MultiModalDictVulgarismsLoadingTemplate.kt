/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MultiModalDictVulgarismsLoadingTemplate.kt
 ** Description : 多模态不雅词词典下载类
 ** Version     : 1.0
 ** Date        : 2024/2/23
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D           2024/3/4      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.framework.abilities.search.multimodal.IMultiModalAbility
import com.oplus.gallery.framework.app.GalleryApplication

/**
 * 多模态不雅词词典下载类
 */
class MultiModalDictVulgarismsLoadingTemplate(
    context: Context,
    modelConfig: ModelConfig,
    allowContinueAction: (() -> Boolean)? = null
) : CommonModelLoadingTemplate(context, modelConfig, allowContinueAction) {

    override val tag: String = TAG

    override val modelName: String = ModelName.DICT_VULGARISMS

    override fun onSuccess() {
        val scanSourceFile = useModelComponentDir
        if (scanSourceFile.exists()) {
            val files = scanSourceFile.listFiles()
            files.forEach { file ->
                (context.applicationContext as GalleryApplication).getAppAbility(IMultiModalAbility::class.java)?.use {
                    val absolutePath = file.absolutePath
                    GLog.d(tag, "onSuccess, multiModalDictVulgarisms absolutePath = $absolutePath")
                    if (absolutePath.contains(FILE_NAME_DICT_VULGARISMS)) {
                        it.dictVulgarismsPath = file.absolutePath
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "MultiModalDictVulgarismsLoadingTemplate"

        /**
         * 不雅词词典文件名
         */
        private const val FILE_NAME_DICT_VULGARISMS = "Dict_Vulgarisms.txt"
    }
}