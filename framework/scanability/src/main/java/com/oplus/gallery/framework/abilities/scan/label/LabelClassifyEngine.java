/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelClassifyEngine.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2017/06/08
 ** Author: yanchao.<PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yanchao.<PERSON>@Apps.Gallery3D     2017/06/08    1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.label;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Bitmap;

import com.cv.imageapi.CvImageClassify;
import com.cv.imageapi.CvImageLibrary;
import com.cv.imageapi.model.CvClassifyLabel;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.search.cloud.LabelCloudHelper;
import com.oplus.gallery.business_lib.util.AssetHelper;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.dao.CloudDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.security.Md5Utils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.standard_lib.file.File;

import java.util.Arrays;

public class LabelClassifyEngine {
    private static final String TAG = "LabelClassifyEngine";
    /**
     * Marked by liqingfeng, 由于 5.0.7 标签模型新增了部分标签，这一期由于多语言翻译等时间赶不上，所以先屏蔽掉，后续版本再放开
     * >= 1635 的标签过滤掉
     */
    private static final int TAG_FILTER_INDEX = 1635;
    private static final String LABEL_LICENSE = "label_license.lic";
    private static int sResultCode = -1;
    private CvImageClassify mCvImageClassify;
    private Context mContext;
    private long mTime;

    public LabelClassifyEngine(Context context) {
        mContext = context;
    }

    public boolean initEngine(int defaultVersion, int currentVersion) {
        GLog.d(TAG, "initEngine");
        mTime = System.currentTimeMillis();
        boolean result = false;
        SharedPreferences sp = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME);
        SharedPreferences.Editor editor = sp.edit();
        boolean hasVersionDegrade = sp.getBoolean(ComponentPrefUtils.HAS_LABEL_COMPONENT_VERSION_DEGRADE_KEY, false);
        GLog.d(TAG, "initEngine, defaultVersion:" + defaultVersion + ",currentVersion:" + currentVersion
                + ",hasVersionDegrade:" + hasVersionDegrade);
        if ((currentVersion < defaultVersion) || ((currentVersion == defaultVersion) && !hasVersionDegrade)) {
            result = initCurrentEngine();
        } else {
            result = loadUpdatedComponents(currentVersion, hasVersionDegrade);
            if (result) {
                editor.putLong(ComponentPrefUtils.LABEL_INIT_FAILED_TIME_KEY, 0);
                editor.apply();
            } else {
                long failed = sp.getLong(ComponentPrefUtils.LABEL_INIT_FAILED_TIME_KEY, 0);
                long current = System.currentTimeMillis();
                if (failed == 0) {
                    editor.putLong(ComponentPrefUtils.LABEL_INIT_FAILED_TIME_KEY, current);
                    editor.apply();
                } else if ((current - failed) > GalleryScanUtils.INIT_FAILED_MAX_INTERVAL_TIME) {
                    /*
                    Init engine has failed more then 15 days continuously, we need clear db and sp
                    to us default label components.
                    */
                    clearData();
                }
            }
        }
        if (result && (mCvImageClassify != null)) {
            LabelCloudHelper.setLocalSceneSdkVersion(mContext, mCvImageClassify.getVersion());
        }
        GLog.d(TAG, "initEngine, init time:" + (System.currentTimeMillis() - mTime) + "ms" + ",result:" + result);
        return result;
    }

    public boolean initCurrentEngine() {
        return loadDefaultComponents();
    }

    private void clearData() {
        GLog.d(TAG, "clearData");
        DeleteReq req = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.CLOUD)
                .setTableType(CloudDbDao.TableType.SYNC_LABEL)
                .build();
        DataAccess.getAccess().delete(req);
        ComponentPrefUtils.clearLabelPref(mContext);
    }

    private boolean initLicense() {
        // fixme zhaoxuan 2020/11/30 因标签自研方案修改，删掉申请证书，后续看方案再更换自研的证书
        /*if (sResultCode != 0) {
            String license = AssetHelper.getInstance().getAssetData(mContext, LABEL_LICENSE);
            sResultCode = LicenseHelper.initLicense(license);
            if (sResultCode != 0) {
                GLog.w(TAG, "initLicense, failed because resultCode is " + sResultCode);
                return false;
            }
        }*/
        return true;
    }

    private boolean loadDefaultComponents() {
        GLog.d(TAG, "loadDefaultComponents");
        boolean result = initLicense();
        synchronized (LabelClassifyEngine.class) {
            String fileDirPath = mContext.getFilesDir().getAbsolutePath();
            result &= AssetHelper.getInstance().copyModelIfNeed(mContext,
                    ComponentPrefUtils.LABEL_CLASSIFY_MODEL_NAME, fileDirPath);
            result &= AssetHelper.getInstance().copyModelIfNeed(mContext,
                    ComponentPrefUtils.LABEL_CLASSIFY_DB_NAME, fileDirPath);
            result &= AssetHelper.getInstance().copyModelIfNeed(mContext,
                    ComponentPrefUtils.LABEL_CLASSIFY_TXT_NAME_ZIP, fileDirPath);
            if (result && (mCvImageClassify == null)) {
                String modelPath = mContext.getFilesDir()
                        + File.separator + ComponentPrefUtils.LABEL_CLASSIFY_MODEL_NAME;
                String mainDBPath = mContext.getFilesDir()
                        + File.separator + ComponentPrefUtils.LABEL_CLASSIFY_DB_NAME;
                String labelNameZipPath = mContext.getFilesDir()
                        + File.separator + ComponentPrefUtils.LABEL_CLASSIFY_TXT_NAME_ZIP;
                CvImageLibrary.setDebug(false);
                String labelNamePath = mContext.getFilesDir() + File.separator + ComponentPrefUtils.LABEL_CLASSIFY_TXT_NAME;
                boolean loadLabelNameResult = loadLabelNameFile(labelNamePath, labelNameZipPath);
                if (!loadLabelNameResult) {
                    GLog.e(TAG, "loadDefaultComponents, loadLabelNameFile fail");
                    return false;
                }
                try {
                    mCvImageClassify = new CvImageClassify(modelPath, mainDBPath, labelNamePath);
                } catch (Exception e) {
                    GLog.e(TAG, "loadDefaultComponents, new CvImageClassify Exception:" + e);
                    return false;
                }
            }
        }
        return result;
    }

    private boolean loadLabelNameFile(String labelNamePath, String zipPath) {
        boolean result = true;
        File labelNameFile = new File(labelNamePath);
        if (!labelNameFile.exists()) {
            try {
                FileOperationUtils.unzipFolder(zipPath, mContext.getFilesDir().getAbsolutePath());
            } catch (Exception e) {
                result = false;
                GLog.e(TAG, "loadLabelNameFile error", e);
            }
        }
        return result;
    }

    private boolean loadUpdatedComponents(int currentVersion, boolean hasVersionDegrade) {
        GLog.d(TAG, "loadUpdatedComponents");
        SharedPreferences sp = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME);
        SharedPreferences.Editor editor = sp.edit();
        String classifyModelMD5 = sp.getString(ComponentPrefUtils.LABEL_MODEL_MD5_KEY, null);
        String mainDBMD5 = sp.getString(ComponentPrefUtils.LABEL_MAIN_DB_MD5_KEY, null);
        String labelNameMD5 = sp.getString(ComponentPrefUtils.LABEL_NAME_ZIP_MD5_KEY, null);
        String curComponentPath = mContext.getFilesDir() + ComponentPrefUtils.LABEL_COMPONENT_CURRENT_PATH;
        File classifyModelFile = new File(curComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_MODEL_NAME);
        File mainDBFile = new File(curComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_DB_NAME);
        File labelNameFile = new File(curComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_TXT_NAME_ZIP);
        //1.verify MD5
        boolean result = Md5Utils.verifyMD5IfNeedRetry(classifyModelFile, classifyModelMD5)
                && Md5Utils.verifyMD5IfNeedRetry(mainDBFile, mainDBMD5)
                && Md5Utils.verifyMD5IfNeedRetry(labelNameFile, labelNameMD5);
        if (result) {
            if (hasVersionDegrade) {
                editor.putBoolean(ComponentPrefUtils.HAS_LABEL_COMPONENT_VERSION_DEGRADE_KEY, false);
                editor.apply();
            }
            //3.init license
            result = initLicense();
            if (result) {
                CvImageLibrary.setDebug(false);
                String labelNamePath = mContext.getFilesDir()
                        + File.separator + ComponentPrefUtils.LABEL_CLASSIFY_TXT_NAME;
                boolean loadLabelNameResult = loadLabelNameFile(labelNamePath, labelNameFile.getAbsolutePath());
                if (!loadLabelNameResult) {
                    GLog.e(TAG, "loadUpdatedComponents, loadLabelNameFile false");
                    return false;
                }
                try {
                    //4.new CvImageClassify
                    mCvImageClassify = new CvImageClassify(classifyModelFile.getAbsolutePath(), mainDBFile.getAbsolutePath(), labelNamePath);
                } catch (Exception e) {
                    GLog.e(TAG, "loadUpdatedComponents, new CvImageClassify Exception:" + e);
                    return false;
                }
            } else {
                GLog.e(TAG, "loadUpdatedComponents, initLicense failed.");
            }
        } else {
            GLog.d(TAG, "loadUpdatedComponents, verify current label components MD5 failed!");
            if (hasVersionDegrade) {
                return false;
            }
            //copy backup folder components to current folder
            AssetHelper.clearFiles(curComponentPath);
            String backupComponentPath = mContext.getFilesDir() + ComponentPrefUtils.LABEL_COMPONENT_BACKUP_PATH;
            File backupClassifyModelFile = new File(backupComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_MODEL_NAME);
            File backupMainDBFile = new File(backupComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_DB_NAME);
            File backupLabelNameFile = new File(backupComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_TXT_NAME_ZIP);
            if (Md5Utils.verifyMD5IfNeedRetry(backupClassifyModelFile, classifyModelMD5)
                    && Md5Utils.verifyMD5IfNeedRetry(backupMainDBFile, mainDBMD5)
                    && Md5Utils.verifyMD5IfNeedRetry(backupLabelNameFile, labelNameMD5)) {
                classifyModelFile = new File(curComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_MODEL_NAME);
                mainDBFile = new File(curComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_DB_NAME);
                labelNameFile = new File(curComponentPath, ComponentPrefUtils.LABEL_CLASSIFY_TXT_NAME_ZIP);
                try {
                    FileOperationUtils.copyFile(backupClassifyModelFile, classifyModelFile, false);
                    FileOperationUtils.copyFile(backupMainDBFile, mainDBFile, false);
                    FileOperationUtils.copyFile(backupLabelNameFile, labelNameFile, false);
                } catch (Exception e) {
                    GLog.e(TAG, "loadUpdatedComponents, copyFile exception:" + e);
                }
            } else {
                /*
                backup folder components verify failed, we need degrade component version and wait
                for correct ones to download
                */
                AssetHelper.clearFiles(backupComponentPath);
                int degradeVersion = currentVersion - 1;
                GLog.d(TAG, "loadUpdatedComponents, verify backup label components MD5 failed, we need degrade"
                        + " component version to " + degradeVersion);
                editor.putInt(ComponentPrefUtils.LABEL_COMPONENT_VERSION_KEY, degradeVersion);
                editor.putInt(ComponentPrefUtils.REJECTED_LABEL_COMPONENT_VERSION_KEY, 0);
                editor.putBoolean(ComponentPrefUtils.HAS_LABEL_COMPONENT_VERSION_DEGRADE_KEY, true);
                editor.apply();
            }
        }
        return result;
    }

    /**
     *
     * @param bitmap
     * @return CvClassifyLabel[] 只在发生crashe时返回null
     */
    public CvClassifyLabel[] getImageClassifyInfo(Bitmap bitmap) {
        if (bitmap == null) {
            GLog.d(TAG, "getImageClassifyInfo, bitmap is null!");
            return new CvClassifyLabel[0];
        }
        long startTime = System.currentTimeMillis();
        CvClassifyLabel[] result = null;
        try {
            result = mCvImageClassify.cvClassify(bitmap);
            if (result != null) {
                // Marked by liqingfeng, 由于 5.0.7 标签模型新增了部分标签，这一期由于多语言翻译等时间赶不上，所以先屏蔽掉，后续版本再放开
                result = Arrays.stream(result)
                        .filter(cvClassifyLabel -> cvClassifyLabel.mId < TAG_FILTER_INDEX)
                        .toArray(CvClassifyLabel[]::new);
            }
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getImageClassifyInfo, fail", e);
            return null;
        }
        if (GProperty.DEBUG) {
            GLog.d(TAG, "getImageClassifyInfo labelScan costTime:" + GLog.getTime(startTime));
        }
        if (result == null) {
            return new CvClassifyLabel[0];
        }
        return result;
    }

    public void release() {
        GLog.d(TAG, "release");
        if (mCvImageClassify != null) {
            mCvImageClassify.release();
            mCvImageClassify = null;
        }
    }
}