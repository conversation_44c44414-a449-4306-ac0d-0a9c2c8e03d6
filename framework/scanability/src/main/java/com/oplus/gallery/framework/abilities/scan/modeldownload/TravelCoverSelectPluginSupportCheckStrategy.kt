/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - TravelCoverSelectPluginSupportCheckStrategy.kt
 ** Description: 旅程封面优选插件检查策略
 ** Version: 1.0
 ** Date : 2025/4/7
 ** Author: ********
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** ********      2025/4/7       1.0          created
 ***************************************************************/
package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.foundation.ai.IAIApi
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.FlashPicSelect.FLASH_PIC_SELECT_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.FlashPicSelect.FLASH_PIC_SELECT_PLUGIN_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_TRAVEL_COVER_SELECTION
import com.oplus.gallery.framework.abilities.download.IAIUnitPluginState

/**
 * 旅程封面优选插件检查策略
 * 融合搜素需要 Clip 和 TextEmbedding 插件
 * 目前策略：
 * 1. TravelCoverSelectFeatureInterceptor 旅程封面优选 feature 支持。
 * 2. AIUnitPrivacyInterceptor aiunit 隐私协议。
 * 3. AIUnitModelDownloadInterceptor 下载。静默下载。
 */
internal class TravelCoverSelectPluginSupportCheckStrategy(
    aiApi: IAIApi?,
    override val pluginState: IAIUnitPluginState = AIUnitPluginState(
        AIUnitPlugin.TRAVEL_COVER_SELECT.downloadPlugin,
        FLASH_PIC_SELECT_PLUGIN_DOWNLOAD_STATE,
        FLASH_PIC_SELECT_PLUGIN_SHOW_TIMESTAMP
    )
) : ScanPluginSupportCheckStrategy(aiApi) {
    override fun updateBlockingConfig(context: Context, settingsAbility: ISettingsAbility) {
        settingsAbility.updateBlockingConfig(context, IS_AGREE_AI_UNIT_PRIVACY)
    }

    /**
     * 获取下载前置条件拦截器
     */
    override fun getInterceptors(context: Context) = listOf(
        TravelCoverSelectFeatureInterceptor()
    )

    override fun getTag(): String = TAG

    companion object {
        private const val TAG = "TravelCoverSelectPluginSupportCheckStrategy"
    }
}

/**
 * 旅程封面优选插件Feature拦截器
 */
internal class TravelCoverSelectFeatureInterceptor : ConditionInterceptor() {
    override fun onCheckCondition(param: Bundle): Boolean {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_TRAVEL_COVER_SELECTION, defValue = true, expDefValue = false).also {
            GLog.d(TAG, LogFlag.DL) { "onCheckCondition. feature is support flash selection: $it" }
        }
    }

    companion object {
        private const val TAG = "TravelCoverSelectFeatureInterceptor"
    }
}