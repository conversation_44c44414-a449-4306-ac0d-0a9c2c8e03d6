/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PoiModalConfig.kt
 ** Description : POI资源配置类
 ** Version     : 1.0
 ** Date        : 2025/05/26
 ** Author      : W9095286
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9095286                            2025/05/26      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import android.content.Context
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Download.POI_CURRENT_VERSION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Download.POI_SCANNED_VERSION
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.framework.app.getAppAbility

/**
 * POI表下载配置
 */
class PoiModelConfig(val context: Context) : ModelConfig(ModelName.POI_ADDRESS_SOURCE) {
    override var currentVersion: Int
        get() = ConfigAbilityWrapper.getInt(POI_CURRENT_VERSION)
        set(value) {
            context.getAppAbility<IConfigSetterAbility>()?.use { it.setIntConfig(POI_CURRENT_VERSION, value) }
        }

    /**
     * 已经实际使用的version
     */
    var scannedVersion: Int
        get() = ConfigAbilityWrapper.getInt(POI_SCANNED_VERSION)
        set(value) {
            context.getAppAbility<IConfigSetterAbility>()?.use { it.setIntConfig(POI_SCANNED_VERSION, value) }
        }
}