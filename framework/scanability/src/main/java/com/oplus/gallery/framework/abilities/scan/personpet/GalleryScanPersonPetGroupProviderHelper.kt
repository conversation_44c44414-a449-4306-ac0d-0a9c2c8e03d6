/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : GalleryScanPersonPetGroupProviderHelper.kt
 ** Description : 人宠合照扫描数据库操作 helper 类
 ** Version     : 1.0
 ** Date        : 2025/05/15
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/05/15  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.personpet

import android.database.Cursor
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetGroup
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetGroupColumns.SCORE
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetListViewColumns.ROLE_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.Pet
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFace
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.AS
import com.oplus.gallery.foundation.database.util.SQLGrammar.ASTERISK
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.DESC
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN
import com.oplus.gallery.foundation.database.util.SQLGrammar.IS_NOT_NULL
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.ORDER_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT
import com.oplus.gallery.foundation.database.util.SQLGrammar.UNION_ALL
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 人宠合照扫描数据库操作 helper 类
 */
object GalleryScanPersonPetGroupProviderHelper {
    private const val TAG = "GalleryScanPersonPetGroupProviderHelper"
    private const val ROLE_PET = "'pet'"
    private const val ROLE_PERSON = "'person'"

    /**
     * SQL:
     * SELECT * FROM (
     *   SELECT _data, group_id, media_type, 'pet' AS role_type, pet_cover_score AS score,
     *       thumb_width, thumb_height, rect_left, rect_top, rect_right, rect_bottom
     *   FROM pet
     *   WHERE feature IS NOT NULL AND is_recycled = 0 AND invalid = 0 AND is_hide = 0
     *   UNION ALL
     *   SELECT _data, group_id, media_type, 'person' AS role_type, face_cover_expression AS score,
     *       thumb_width, thumb_height, rect_left, rect_top, rect_right, rect_bottom
     *   FROM scan_face
     *   WHERE feature IS NOT NULL AND is_recycled = 0 AND invalid = 0 AND is_hide = 0
     * )
     * ORDER BY _data
     */
    @JvmStatic
    private fun getAllValidPersonPetListSQL(): String {
        return StringBuilder().apply {
            append(SELECT).append(ASTERISK).append(FROM).append(LEFT_BRACKETS)
            append(SELECT)
                .append(Pet.DATA + COMMA).append(Pet.GROUP_ID + COMMA).append(Pet.MEDIA_TYPE + COMMA).append(ROLE_PET + AS + ROLE_TYPE + COMMA)
                .append(Pet.PET_COVER_SCORE + AS + SCORE + COMMA).append(Pet.THUMB_W + COMMA).append(Pet.THUMB_H + COMMA)
                .append(Pet.LEFT + COMMA).append(Pet.TOP + COMMA).append(Pet.RIGHT + COMMA).append(Pet.BOTTOM)
            append(FROM + Pet.TAB)
            append(WHERE + Pet.INVALID + EQUAL_ZERO).append(AND + Pet.IS_RECYCLED + EQUAL_ZERO).append(AND + Pet.IS_HIDE + EQUAL_ZERO)
                .append(AND + Pet.FEATURE + IS_NOT_NULL)
            append(UNION_ALL)
            append(SELECT)
                .append(ScanFace.DATA + COMMA).append(ScanFace.GROUP_ID + COMMA).append(ScanFace.MEDIA_TYPE + COMMA)
                .append(ROLE_PERSON + AS + ROLE_TYPE + COMMA).append(ScanFace.GROUP_COVER_EXPRESSION_SCORE + AS + SCORE + COMMA)
                .append(ScanFace.THUMB_W + COMMA).append(ScanFace.THUMB_H + COMMA)
                .append(ScanFace.LEFT + COMMA).append(ScanFace.TOP + COMMA).append(ScanFace.RIGHT + COMMA).append(ScanFace.BOTTOM)
            append(FROM + ScanFace.TAB)
            append(WHERE + ScanFace.INVALID + EQUAL_ZERO).append(AND + ScanFace.IS_RECYCLED + EQUAL_ZERO).append(AND + ScanFace.IS_HIDE + EQUAL_ZERO)
                .append(AND + ScanFace.FEATURE + IS_NOT_NULL)
            append(RIGHT_BRACKETS).append(ORDER_BY).append(ScanFace.DATA).append(COMMA).append(ScanFace.GROUP_ID)
        }.toString()
    }

    /**
     * 查询 pet 表和 scan_face 表的有效可见的扫描记录，返回合并的集合
     */
    @JvmStatic
    fun getAllValidPersonPetList(): List<PersonPetInfo> {
        val sql = getAllValidPersonPetListSQL()
        val list = mutableListOf<PersonPetInfo>()
        runCatching {
            RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(sql)
                .build().exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val dataIndex = cursor.getColumnIndex(Pet.DATA)
                    val groupIdIndex = cursor.getColumnIndex(Pet.GROUP_ID)
                    val mediaTypeIndex = cursor.getColumnIndex(Pet.MEDIA_TYPE)
                    val roleTypeIndex = cursor.getColumnIndex(ROLE_TYPE)
                    val scoreIndex = cursor.getColumnIndex(SCORE)
                    val thumbWidthIndex = cursor.getColumnIndex(Pet.THUMB_W)
                    val thumbHeightIndex = cursor.getColumnIndex(Pet.THUMB_H)
                    val leftIndex = cursor.getColumnIndex(Pet.LEFT)
                    val topIndex = cursor.getColumnIndex(Pet.TOP)
                    val rightIndex = cursor.getColumnIndex(Pet.RIGHT)
                    val bottomIndex = cursor.getColumnIndex(Pet.BOTTOM)
                    while (cursor.moveToNext()) {
                        PersonPetInfo().apply {
                            data = cursor.getString(dataIndex)
                            groupID = cursor.getInt(groupIdIndex)
                            mediaType = cursor.getInt(mediaTypeIndex)
                            roleType = cursor.getString(roleTypeIndex)
                            score = cursor.getFloat(scoreIndex)
                            thumbWidth = cursor.getInt(thumbWidthIndex)
                            thumbHeight = cursor.getInt(thumbHeightIndex)
                            left = cursor.getInt(leftIndex)
                            top = cursor.getInt(topIndex)
                            right = cursor.getInt(rightIndex)
                            bottom = cursor.getInt(bottomIndex)
                            list.add(this)
                        }
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllValidPersonPetList,  fail. $it" }
        }
        return list
    }

    /**
     * 查询人宠合照表中所有记录
     */
    @JvmStatic
    fun getAllPersonPetGroupList(): List<PersonPetGroupCluster> {
        val list = mutableListOf<PersonPetGroupCluster>()
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                .setProjection(arrayOf(ASTERISK))
                .setConvert(CursorConvert())
                .setOrderBy(PersonPetGroup.MERGE_GROUP_ID + DESC)
                .build().exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val idIndex = cursor.getColumnIndex(PersonPetGroup._ID)
                    val dataIndex = cursor.getColumnIndex(PersonPetGroup.DATA)
                    val mediaTypeIndex = cursor.getColumnIndex(PersonPetGroup.MEDIA_TYPE)
                    val createDateIndex = cursor.getColumnIndex(PersonPetGroup.MERGE_GROUP_CREATE_DATE)
                    val createTypeIndex = cursor.getColumnIndex(PersonPetGroup.MERGE_GROUP_CREATE_TYPE)
                    val groupNameIndex = cursor.getColumnIndex(PersonPetGroup.MERGE_GROUP_NAME)
                    val groupIdIndex = cursor.getColumnIndex(PersonPetGroup.MERGE_GROUP_ID)
                    val personGroupIdIndex = cursor.getColumnIndex(PersonPetGroup.PERSON_GROUP_IDS)
                    val petGroupIdIndex = cursor.getColumnIndex(PersonPetGroup.PET_GROUP_IDS)
                    val scoreIndex = cursor.getColumnIndex(PersonPetGroup.SCORE)
                    val isDefaultCoverIndex = cursor.getColumnIndex(PersonPetGroup.IS_DEFAULT_COVER)
                    val isDisbandIndex = cursor.getColumnIndex(PersonPetGroup.IS_DISBAND)
                    while (cursor.moveToNext()) {
                        PersonPetGroupCluster().apply {
                            id = cursor.getInt(idIndex)
                            data = cursor.getString(dataIndex)
                            groupId = cursor.getInt(groupIdIndex)
                            mediaType = cursor.getInt(mediaTypeIndex)
                            createDate = cursor.getLong(createDateIndex)
                            createType = cursor.getInt(createTypeIndex)
                            groupName = cursor.getString(groupNameIndex)
                            personGroupIds = cursor.getString(personGroupIdIndex)
                            petGroupIds = cursor.getString(petGroupIdIndex)
                            score = cursor.getFloat(scoreIndex)
                            isDefaultCover = (cursor.getInt(isDefaultCoverIndex) == 1)
                            isDisband = (cursor.getInt(isDisbandIndex) == 1)
                            list.add(this)
                        }
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllPersonPetGroupList,  fail. $it" }
        }
        return list
    }

    /**
     * 插入数据
     */
    @JvmStatic
    fun insertData(list: List<PersonPetGroupCluster>): Array<BatchResult>? {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "insertData,  list is empty!" }
            return null
        }

        val operations = mutableListOf<InsertReq>()
        list.forEach { cluster ->
            InsertReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                .setConvert { cluster.buildInsertContentValues() }
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .build()?.let { operations.add(it) }
        }

        var result: Array<BatchResult>? = null
        runCatching {
            result = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build()
                .exec()
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.PERSON_PET_GROUP, IDao.DaoType.GALLERY)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "insertData,  fail. $it" }
        }
        return result
    }

    /**
     * 更新数据
     */
    @JvmStatic
    fun updateData(list: List<PersonPetGroupCluster>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "updateData,  list is empty." }
            return
        }

        val operations = mutableListOf<UpdateReq>()
        list.forEach { cluster ->
            UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                .setConvert { cluster.buildUpdateContentValues() }
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(PersonPetGroup._ID + EQUAL + cluster.id)
                .build()?.let { operations.add(it) }
        }
        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build()
                .exec()
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.PERSON_PET_GROUP, IDao.DaoType.GALLERY)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateData,  delete fail. $it" }
        }
    }

    /**
     * 删除数据
     */
    @JvmStatic
    fun deleteData(list: List<PersonPetGroupCluster>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "deleteData,  list is empty." }
            return
        }

        val operations = mutableListOf<DeleteReq>()
        list.forEach { cluster ->
            DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PERSON_PET_GROUP)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(PersonPetGroup.MERGE_GROUP_ID + EQUAL + cluster.groupId)
                .build()?.let { operations.add(it) }
        }
        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build()
                .exec()
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.PERSON_PET_GROUP, IDao.DaoType.GALLERY)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteData,  delete fail. $it" }
        }
    }
}