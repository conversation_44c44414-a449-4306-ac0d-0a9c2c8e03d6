/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEngineFactory.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2021/10/11
 ** Author: Sandy.Meng@Apps.Gallery3D
 ** TAG: Video label SDK
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Sandy.Meng@Apps.Gallery3D       2021/10/11   1.0         new video label
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.label.engine

import android.content.Context

object VideoEngineFactory {
    @JvmStatic
    fun getEngine(context: Context): AbsVideoLabelClassifyEngine {
        return OAVideoClassifyEngine(context)
    }
}