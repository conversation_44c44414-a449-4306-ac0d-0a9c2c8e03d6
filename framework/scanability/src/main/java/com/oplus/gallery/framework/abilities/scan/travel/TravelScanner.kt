/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  TravelScanner.kt
 * * Description:  旅行数据扫描
 * * Version: 1.0
 * * Date : 2025/04/17
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/04/17     1.0        create
 ************************************************************/
package com.oplus.gallery.framework.abilities.scan.travel

import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import com.google.gson.Gson
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelCoverBlurredHelper
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelDBTableHelper
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelDBTableHelper.MIN_COUNT_IN_TRAVEL_ALBUM
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelFileExtendOperator
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelImageInfo
import com.oplus.gallery.foundation.travel.DTTravelApi
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.DT_RESIDENCE_DATA
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_FIRST_SCAN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_SCAN_COUNT_ALL
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.TRAVEL_SCAN_COUNT_24H_MAX
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.isAllowStartScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.refreshScanRecordingIfNecessary
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.app.GalleryApplication
import kotlin.math.max
import kotlin.math.min

/**
 * 旅行数据扫描
 * 1.扫描数据判断是否生成旅行图集，能生成旅行图集的数据存入scan_travel表
 * 2.扫描scan_travel表里数据，进行封面打分
 * 3.旅行图集debug——adb shell setProp debug.gallery.travel.album true，会自动从debug_travel_album_file文件去获取旅程序列
 *   该旅程序列中只要地点不是 成都/深圳 的皆为旅游地点城市，在这些城市所处的时间段内拍摄照片皆可生成旅程图集
 *   生成图集条件：单个城市照片视频数量不少于10，总旅程图片视频数量不少于20
 *   文件内每个地址代表一天，从20250301开始，每个地址的first是0:00，second是23:59
 *   文件内配置了跨年时间，跨年是20241231-20250101
 *   具体生成方式可参考：https://odocs.myoas.com/docs/KrkEVrMj9bCvv8AJ/ 《旅程debug模式》
 */
class TravelScanner(val context: Context) : GalleryScan(context) {
    /**
     * 24小时内已经扫描的数量
     */
    private var scannedCountIn24hours = 0

    /**
     * 本次扫描总共扫描的数量
     */
    private var scannedTotalCount = 0

    /**
     * 有效旅程序列
     * first：List<String>-地址
     * second：Pair<Long, Long>-开始时间和结束时间
     */
    private var validTravelList = mutableListOf<OriginalTravelData>()

    /**
     * 扫描
     * 扫描任务调用接口，调用后执行具体的扫描逻辑
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onScan(triggerType: Int, config: ScanConfig?) {
        GLog.w(TAG, LogFlag.DL) { "onScan, start scan travel triggerType:$triggerType" }
        super.onScan(triggerType, config)
        if (isAllowStartScan(context, needCharging = false, canScanWhenGalleryRunOnTop = false) && isInterrupt.not()) {
            scanData()
        }
        GLog.w(TAG, LogFlag.DL) { "onScan, travel scan end" }
    }

    /**
     * 旅行扫描
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun scanData() {
        var dtTravelApi: DTTravelApi? = DTTravelApi(context)
        runCatching {
            // 0.记录扫描相关数据，状态
            val scanStartTime = System.currentTimeMillis()
            markConditionsOnStartScan()
            recordScanDataIn24H()
            // 1.初始化DT，判断有无有效DT旅程序列
            dtTravelApi?.init()
            validTravelList.clear()
            dtTravelApi?.let {
                if (isNeedScan(it)) {
                    doScan()
                }
            }
            // 2.检查是否有新的数据满足已经生成的旅程图集条件
            dealExistNewTravelDataNotInTravelTable()
            // 3.检查图片的文件数据有旅程信息但是没有在旅程表里
            dealExistTravelInfoNotInTravelTable()
            // 4.封面做模糊背景扫描
            doBackgroundBlurredForCover()
            // 5.打印扫描记录数据
            updateLastScanTime()
            GLog.w(TAG, LogFlag.DL) { "scanData, travel scan const time:${GLog.getTime(scanStartTime)}, scannedTotalCount:$scannedTotalCount" }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "scanData exception, $it" }
        }.also {
            dtTravelApi?.release()
            dtTravelApi = null
        }
    }

    /**
     * 是否需要进行旅程扫描
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun isNeedScan(api: DTTravelApi): Boolean {
        val dtResidenceSet = api.getResidenceData().toMutableSet()
        val dtLocationAndTimeMap = api.getLocationData().toMutableMap()
        // 0.如果地址时间Map中小于2则无法确定一段旅行，没有常住地无法确定一段旅行
        if ((dtLocationAndTimeMap.size < MIN_LOCATION_AND_TIME_MAP_SIZE) || (dtResidenceSet.isEmpty())) {
            GLog.d(TAG, LogFlag.DL) { "isNeedScan, LocationAndTimeMap:${dtLocationAndTimeMap.size}, residence:${dtResidenceSet.size}, return" }
            return false
        }
        // 1.如果地址时间List中小于3则无法确定一段旅行
        val dtLocationAndTimeList = mergeDtLocationAndTimePair(dtLocationAndTimeMap)
        // 2.存储当前的常驻地信息，防止旅游城市变为常驻地城市无法识别出旅程
        updateDTResidence(dtResidenceSet, dtLocationAndTimeList)
        if (dtLocationAndTimeList.size < MIN_LOCATION_AND_TIME_LIST_SIZE) {
            GLog.d(TAG, LogFlag.DL) { "isNeedScan, LocationAndTimeList:${dtLocationAndTimeMap.size}, return" }
            return false
        }
        // 2.确定有效旅程序列
        val result = isValidDTTravelData(dtLocationAndTimeList)
        GLog.d(TAG, LogFlag.DL) { "isNeedScan, result:$result" }
        return result
    }

    /**
     * 1.将位置变化信息按照时间排序
     * 2.如果相邻的两个地址是同一个则合并
     */
    private fun mergeDtLocationAndTimePair(
        dtLocationAndTimeMap: Map<String, MutableList<Pair<Long, Long>>>
    ): MutableList<Pair<String, Pair<Long, Long>>> {
        val dtLocationAndTimeList = mutableListOf<Pair<String, Pair<Long, Long>>>()
        // 1.将位置变化信息按照时间排序
        val tempLocationAndTimeList = dtLocationAndTimeMap.flatMap { entry ->
            entry.value.map { Pair(entry.key, it) }
        }.sortedBy { it.second.first }.toMutableList()
        // 2.如果相邻的两个地址是同一个则合并
        var currentPair = tempLocationAndTimeList[0]
        for (index in 1 until tempLocationAndTimeList.size) {
            val nextPair = tempLocationAndTimeList[index]
            if (currentPair.first == nextPair.first) {
                currentPair = Pair(currentPair.first, Pair(currentPair.second.first, nextPair.second.second))
            } else {
                dtLocationAndTimeList.add(currentPair)
                currentPair = nextPair
            }
        }
        dtLocationAndTimeList.add(currentPair)
        GLog.d(TAG, LogFlag.DL) { "mergeDtLocationAndTimePair, final dtLocationAndTime size:${dtLocationAndTimeList.size}" }
        if (GProperty.DEBUG_SCAN) {
            GLog.d(TAG, LogFlag.DL) { "mergeDtLocationAndTimePair, final dtLocationAndTimeList:$dtLocationAndTimeList" }
        }
        return dtLocationAndTimeList
    }

    /**
     * 存储当前的常驻地信息，防止旅游城市变为常驻地城市无法识别出旅程
     * @param residenceSet 此刻的常驻地列表
     * @param dtLocationAndTimeList 按时间顺序排序好的地址时间信息对
     */
    private fun updateDTResidence(residenceSet: Set<String>, dtLocationAndTimeList: MutableList<Pair<String, Pair<Long, Long>>>) {
        if (dtLocationAndTimeList.size < MIN_LOCATION_AND_TIME_MAP_SIZE) {
            GLog.d(TAG, LogFlag.DL) { "updateDTResidence, dtLocationAndTimeList size low 2, not need update" }
            return
        }
        val gson = Gson()
        // 1.获取存储的常驻地信息
        var oldDTResidence = DTResidence(mutableMapOf())
        val oldDTResidenceStr = ConfigAbilityWrapper.getString(DT_RESIDENCE_DATA)
        if ((oldDTResidenceStr != null) && (oldDTResidenceStr != TextUtil.EMPTY_STRING)) {
            oldDTResidence = gson.fromJson(oldDTResidenceStr, DTResidence::class.java) ?: DTResidence(mutableMapOf())
        }
        // 2.反序找到最后所待的常驻地即可，如果每次保存较早的常驻地信息，较早常驻地信息在目前时刻的常驻地列表可能已经更新
        var isStop = false
        for (index in (dtLocationAndTimeList.size - 1) downTo 0) {
            if (!isStop) {
                val value = dtLocationAndTimeList[index]
                val key = value.first + TextUtil.COLON + value.second.first + TextUtil.STRIKE + value.second.second
                when {
                    residenceSet.contains(value.first) -> {
                        // 最后一个时间地址信息是在常驻地，无需保存
                        if (!GProperty.DEBUG_TRAVEL_ALBUM) {
                            isStop = true
                        }
                    }
                    oldDTResidence.residenceMap.containsKey(key) -> {
                        // 已经存在该key，不需要更新，因为此时可能常驻地已经变了
                        if (!GProperty.DEBUG_TRAVEL_ALBUM) {
                            isStop = true
                        }
                    }
                    ((index > 0) && residenceSet.contains(dtLocationAndTimeList[index - 1].first)) -> {
                        // 前一个地点是常驻地，则代表此时刚开始旅行，保存此时的常驻地信息，后面在某个地方旅行时间太长，该地方可能变成常驻地
                        oldDTResidence.residenceMap[key] = Pair(dtLocationAndTimeList[index - 1].first, residenceSet.toList())
                        if (!GProperty.DEBUG_TRAVEL_ALBUM) {
                            isStop = true
                        }
                    }
                }
            } else {
                break
            }
        }
        // 3.遍历列表重建dtResidence，删除旧的常驻地信息
        val newDTResidence = DTResidence(mutableMapOf())
        for (item in dtLocationAndTimeList) {
            val key = item.first + TextUtil.COLON + item.second.first + TextUtil.STRIKE + item.second.second
            if (oldDTResidence.residenceMap.containsKey(key) && (oldDTResidence.residenceMap[key] != null)) {
                newDTResidence.residenceMap[key] = oldDTResidence.residenceMap[key] as Pair
            }
        }
        // 4.更新
        val configAbility = (context as GalleryApplication).getAppAbility(IConfigSetterAbility::class.java)
        val newDTResidenceStr = gson.toJson(newDTResidence)
        configAbility?.setStringConfig(DT_RESIDENCE_DATA, newDTResidenceStr)
        GLog.d(TAG, LogFlag.DL) { "updateDTResidence, newDTResidence size:${newDTResidence.residenceMap.size}" }
        if (GProperty.DEBUG_SCAN) {
            GLog.d(TAG, LogFlag.DL) { "updateDTResidence, newDTResidence:${newDTResidence.residenceMap.toList()}" }
        }
    }

    /**
     * 是否有有效的DT旅程序列
     */
    private fun isValidDTTravelData(dtLocationAndTimeList: MutableList<Pair<String, Pair<Long, Long>>>): Boolean {
        var startResidence = TextUtil.EMPTY_STRING
        val originalTravelData = OriginalTravelData()
        var residence = TextUtil.EMPTY_STRING
        var residenceSet = mutableSetOf<String>()
        val gson = Gson()
        dtLocationAndTimeList.forEach {
            // 1.获取存储的常驻地信息，当识别到第一个旅游地点时才能提取到旅游出发时的常驻地信息
            if (startResidence == TextUtil.EMPTY_STRING) {
                val key = it.first + TextUtil.COLON + it.second.first + TextUtil.STRIKE + it.second.second
                val dtResidenceStr = ConfigAbilityWrapper.getString(DT_RESIDENCE_DATA)
                val dtResidence = gson.fromJson(dtResidenceStr, DTResidence::class.java)
                residence = dtResidence?.residenceMap?.get(key)?.first ?: TextUtil.EMPTY_STRING
                residenceSet = dtResidence?.residenceMap?.get(key)?.second?.toMutableSet() ?: mutableSetOf()
            }
            if ((residence != TextUtil.EMPTY_STRING) && residenceSet.isNotEmpty()) {
                // 2.起点常驻地为空时，确定起点常驻地
                if (startResidence == TextUtil.EMPTY_STRING) {
                    startResidence = residence
                }
                if (it.first == startResidence) {
                    // 3.当前城市 = 起点常驻地 确认为终点
                    val locationAndTimeListCopy = originalTravelData.locationAndTimeList.map { it1 ->
                        it1.copy()
                    }.toMutableList()
                    validTravelList.add(
                        OriginalTravelData(locationAndTimeListCopy, originalTravelData.travelStartTime, originalTravelData.travelEndTime)
                    )
                    originalTravelData.locationAndTimeList.clear()
                    originalTravelData.travelStartTime = 0L
                    originalTravelData.travelEndTime = 0L
                    startResidence = TextUtil.EMPTY_STRING
                    residence = TextUtil.EMPTY_STRING
                    residenceSet.clear()
                } else if (residenceSet.contains(it.first).not()) {
                    // 4.开始旅游时的常驻地列表不包含当前城市 则 确定为旅游城市
                    originalTravelData.locationAndTimeList.add(Pair(it.first, Pair(it.second.first, it.second.second)))
                    originalTravelData.travelStartTime = if (originalTravelData.travelStartTime == 0L) {
                        it.second.first
                    } else {
                        min(originalTravelData.travelStartTime, it.second.first)
                    }
                    originalTravelData.travelEndTime = max(originalTravelData.travelEndTime, it.second.second)
                } else if (residenceSet.contains(it.first)) {
                    // 5.当前城市 = 常驻地城市 且 当前城市 ！= 起点常驻地城市 则认为该序列无效（不包含旅游城市变为常驻地城市情况）
                    startResidence = TextUtil.EMPTY_STRING
                    residence = TextUtil.EMPTY_STRING
                    residenceSet.clear()
                }
            }
        }
        GLog.d(TAG, LogFlag.DL) { "isValidDTTravelData, validTravelList size:${validTravelList.size}" }
        if (GProperty.DEBUG_SCAN) {
            GLog.d(TAG, LogFlag.DL) { "isValidDTTravelData, validTravelList:$validTravelList" }
        }
        return validTravelList.isNotEmpty()
    }

    /**
     * 判断是否是24H内的第一次扫描，并获取24H小时内的扫描数据
     */
    private fun recordScanDataIn24H() {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            mIsFirstScan = it.getBooleanConfig(TRAVEL_FIRST_SCAN, true) ?: true
            if (mIsFirstScan) {
                it.setBooleanConfig(TRAVEL_FIRST_SCAN, false)
            }
            scannedCountIn24hours = it.getIntConfig(TRAVEL_SCAN_COUNT_24H, 0) ?: 0
        }
    }

    /**
     * 旅行扫描
     */
    private fun doScan() {
        for (data in validTravelList) {
            if (isAllowContinueScan(scannedCountIn24hours, TRAVEL_SCAN_COUNT_24H_MAX) && isInterrupt.not()) {
                val needScanData = TravelDBTableHelper.getNeedScanData(data.travelStartTime, data.travelEndTime)
                filterData(needScanData, data)
            } else {
                GLog.w(TAG, LogFlag.DL) { "doScan, not allow travel scan" }
            }
        }
    }

    /**
     * 过滤路过城市——照片数量小于10
     * inRangeTravelData：在某个有效旅程范围内的总的图片，已过滤路过城市
     * inRangeLocationAndTimeList：在某个有效旅程范围内的地址时间对，已过滤路过城市
     * @param needScanData 该旅程序列的总图片数
     * @param originalTravelData 该旅程序列的地址时间对，以及该地址时间对组成的旅程开始、结束时间
     */
    private fun filterData(needScanData: List<TravelImageInfo>, originalTravelData: OriginalTravelData) {
        val inRangeTravelData = mutableListOf<TravelImageInfo>()
        val inRangeLocationAndTimeList = mutableListOf<Pair<String, Pair<Long, Long>>>()

        for ((location, range) in originalTravelData.locationAndTimeList) {
            val (startTime, endTime) = range
            val imagesInRange = needScanData.filter { it.datetaken in startTime..endTime }
            if (imagesInRange.size >= THE_NUM_OF_PASSING_BY_CITY) {
                inRangeTravelData.addAll(imagesInRange)
                inRangeLocationAndTimeList.add(location to range)
            }
        }
        GLog.w(TAG, LogFlag.DL) { "filterData, inRangeTravelData size:${inRangeTravelData.size}, " +
                "inRangeLocationAndTimeList size:${inRangeLocationAndTimeList.size}, " +
                "originalLocationAndTimeList size:${originalTravelData.locationAndTimeList.size}" }
        // 只有数量大于等于20才生成旅程图集
        if (inRangeTravelData.size >= MIN_COUNT_IN_TRAVEL_ALBUM) {
            originalTravelData.locationAndTimeList.clear()
            originalTravelData.locationAndTimeList.addAll(inRangeLocationAndTimeList)
            originalTravelData.travelStartTime = originalTravelData.locationAndTimeList.first().second.first
            originalTravelData.travelEndTime = originalTravelData.locationAndTimeList.last().second.second
            generateTravelAlbum(inRangeTravelData, originalTravelData)
        }
    }

    /**
     * 生成旅程信息
     * @param data 该旅程序列的总图片数，已过滤路过城市图片
     * @param originalTravelData 该旅程序列的地址时间对，以及该地址时间对组成的旅程开始、结束时间，已过滤路过城市
     */
    private fun generateTravelAlbum(data: List<TravelImageInfo>, originalTravelData: OriginalTravelData) {
        var countryDomestic = 0
        var countryForeign = 0
        val domesticProvinceList = mutableListOf<String>()
        val domesticCityList = mutableListOf<String>()
        val domesticLocationAndTimeList = mutableListOf<Pair<String, Pair<Long, Long>>>()
        val domesticLocationList = mutableListOf<String>()
        originalTravelData.locationAndTimeList.forEach {
            // 0.确定是国内还是国外
            val domesticCountryList = mutableListOf<String>()
            splitLocationAddress(it.first, domesticCountryList, null, null)
            // 1.遍历先找出旅行种类——国内、国外、国内外
            if (DTTravelApi.localCountries.contains(domesticCountryList.first())) {
                countryDomestic++
                splitLocationAddress(it.first, null, domesticProvinceList, domesticCityList)
                domesticLocationAndTimeList.add(it)
                domesticLocationList.add(it.first)
            } else {
                // 2.国外旅程直接以国家为单位插入
                countryForeign++
                val foreignTravelDataList = data.filter { it1 ->
                    it1.datetaken in it.second.first..it.second.second
                }
                generateForeignTravelAlbum(it.first, it.second.first, it.second.second, foreignTravelDataList)
                // 3.国外旅行中有国内旅行，需拆分国内的旅行，
                generateDomesticTravelAlbum(domesticLocationAndTimeList, domesticLocationList, data)
            }
        }
        // 4.国内外混合旅行，当最后一个为国内旅行时，需要将其生成图集，上面会遗漏该情况
        if (countryForeign > 0) {
            generateDomesticTravelAlbum(domesticLocationAndTimeList, domesticLocationList, data)
        }
        // 5.只有国内情况
        if ((countryForeign == 0) && (countryDomestic != 0) && (data.size >= MIN_COUNT_IN_TRAVEL_ALBUM)) {
            generateDomesticTravelAlbum(domesticProvinceList, domesticCityList,
                originalTravelData.travelStartTime, originalTravelData.travelEndTime, data)
        }
    }

    /**
     * 解析国家、省份和城市
     */
    private fun splitLocationAddress(
        list: List<String>,
        countryList: MutableList<String>?,
        provinceList: MutableList<String>?,
        cityList: MutableList<String>?
    ) {
        list.forEach { location ->
            splitLocationAddress(location, countryList,  provinceList, cityList)
        }
    }

    /**
     * 解析国家、省份和城市
     */
    private fun splitLocationAddress(
        location: String,
        countryList: MutableList<String>?,
        provinceList: MutableList<String>?,
        cityList: MutableList<String>?
    ) {
        val locationList = location.split(TextUtil.LEFT_SLASH)
        if (countryList?.contains(locationList[0]) == false) {
            countryList.add(locationList[0])
        }
        if (provinceList?.contains(locationList[1]) == false) {
            provinceList.add(locationList[1])
        }
        if (cityList?.contains(locationList[2]) == false) {
            cityList.add(locationList[2])
        }
    }

    /**
     * 生成国外旅程图集
     */
    private fun generateForeignTravelAlbum(name: String, startTime: Long, endTime: Long, data: List<TravelImageInfo>) {
        val locationList = name.split(TextUtil.LEFT_SLASH)
        val travelImageInfoList = buildTravelImageInfoList(locationList[0], startTime, endTime, data)
        val travelId = locationList[0] + TextUtil.COLON + startTime + TextUtil.STRIKE + endTime
        GLog.d(TAG, LogFlag.DL) { "generateForeignTravelAlbum, size:${data.size}, startTime:$startTime, endTime:$endTime" }
        compareOldTravelData(travelId, travelImageInfoList)
    }

    /**
     * 生成国内旅程图集，当国内和国外旅行混合时，使用此接口生成混合旅行中的国内
     * @param locationAndTimeList 地址时间对
     * @param locationList 地址列表
     * @param data 图片数据
     */
    private fun generateDomesticTravelAlbum(
        locationAndTimeList: MutableList<Pair<String, Pair<Long, Long>>>,
        locationList: MutableList<String>,
        data: List<TravelImageInfo>
    ) {
        if (locationAndTimeList.isNotEmpty()) {
            val startTime = locationAndTimeList.first().second.first
            val endTime = locationAndTimeList.last().second.second
            val domesticTravelDataList = data.filter { it1 ->
                it1.datetaken in startTime..endTime
            }
            // 只有数量大于等于20才生成旅程图集
            if (domesticTravelDataList.size >= MIN_COUNT_IN_TRAVEL_ALBUM) {
                val tmpDomesticProvinceList = mutableListOf<String>()
                val tmpDomesticCityList = mutableListOf<String>()
                splitLocationAddress(locationList, null, tmpDomesticProvinceList, tmpDomesticCityList)
                GLog.d(TAG, LogFlag.DL) { "generateDomesticTravelAlbum, composite travel, domestic data size:${domesticTravelDataList.size}, " +
                        "tmpDomesticProvinceList size:${tmpDomesticProvinceList.size}, tmpDomesticCityList size:${tmpDomesticCityList.size}, " +
                        "startTime:$startTime, endTime:$endTime" }
                generateDomesticTravelAlbum(tmpDomesticProvinceList, tmpDomesticCityList, startTime, endTime, domesticTravelDataList)
            }
            locationAndTimeList.clear()
            locationList.clear()
        }
    }

    /**
     * 生成国内旅程图集
     */
    private fun generateDomesticTravelAlbum(
        provinceList: MutableList<String>,
        cityList: MutableList<String>,
        startTime: Long,
        endTime: Long,
        data: List<TravelImageInfo>
    ) {
        var travelImageInfoList = mutableListOf<TravelImageInfo>()
        var travelName = TextUtil.EMPTY_STRING
        if (cityList.size == 1) {
            // 单个地级行政区
            travelName = cityList.first()
        } else if (cityList.size == TWO_PROVINCE_OR_CITY) {
            // 两个地级行政区命令
            travelName = cityList.first() + context.getString(com.oplus.gallery.framework.datatmp.R.string.and) + cityList[1]
        } else {
            if (provinceList.size == 1) {
                // 三个及以上地级行政区 —— 一个省
                travelName = provinceList.first()
            } else {
                travelName = provinceList.first()
                if (provinceList.size == TWO_PROVINCE_OR_CITY) {
                    // 三个地级行政区及以上 —— 两个省
                    travelName += context.getString(com.oplus.gallery.framework.datatmp.R.string.and) + provinceList[1]
                } else {
                    // 三个地级行政区及以上 —— 三个省及以上
                    for (index in 1 until (provinceList.size - 1)) {
                        travelName += context.getString(com.oplus.gallery.framework.datatmp.R.string.name_separator) + provinceList[index]
                    }
                    travelName += context.getString(com.oplus.gallery.framework.datatmp.R.string.and) + provinceList.last()
                }
            }
        }
        if (travelName != TextUtil.EMPTY_STRING) {
            travelImageInfoList = buildTravelImageInfoList(travelName, startTime, endTime, data).toMutableList()
            val travelId = travelName + TextUtil.COLON + startTime + TextUtil.STRIKE + endTime
            GLog.d(TAG, LogFlag.DL) { "generateDomesticTravelAlbum, size:${data.size}, " +
                    "provinceList size:${provinceList.size}, cityList size:${cityList.size}, " +
                    "startTime:$startTime, endTime:$endTime" }
            compareOldTravelData(travelId, travelImageInfoList)
        }
    }

    /**
     * 生成旅程信息
     */
    private fun buildTravelImageInfoList(name: String, startTime: Long, endTime: Long, data: List<TravelImageInfo>): List<TravelImageInfo> {
        val travelData = mutableListOf<TravelImageInfo>()
        val travelId = name + TextUtil.COLON + startTime + TextUtil.STRIKE + endTime
        data.forEach {
            val imageInfo = TravelImageInfo()
            imageInfo.mFilePath = it.mFilePath
            imageInfo.mMediaType = it.mMediaType
            imageInfo.mInvalid = it.mInvalid
            imageInfo.mIsRecycled = it.mIsRecycled
            imageInfo.travelDestination = name
            imageInfo.travelStartTime = startTime
            imageInfo.travelEndTime = endTime
            imageInfo.travelId = travelId
            travelData.add(imageInfo)
        }
        return travelData
    }

    /**
     * 和已有的图集做比较
     * 如果之前未有该图集，直接插入
     * 如果之前已有该图集，找出原有图集中需要删除的删除，找出原有图集中没有的新插入
     */
    private fun compareOldTravelData(travelId: String, data: List<TravelImageInfo>) {
        val oldData = TravelDBTableHelper.getTravelData(travelId).toSet()
        val newData = data.toSet()
        val insData: MutableSet<TravelImageInfo>
        val delData: MutableSet<TravelImageInfo>
        if (oldData.isEmpty()) {
            // 第一次生成该图集直接插入数据
            if (newData.size >= MIN_COUNT_IN_TRAVEL_ALBUM) {
                clearExistTravelAlbum(data.first().travelStartTime, data.first().travelEndTime)
                TravelFileExtendOperator.writeTravelInfoToFile(context, data, data.first().travelDestination)
                TravelDBTableHelper.insertTravelData(data)
                recordScanCount(data.size)
                scannedTotalCount += data.size
                GLog.w(TAG, LogFlag.DL) { "compareOldTravelData, first generate travel album, size:${data.size}" }
            } else {
                GLog.w(TAG, LogFlag.DL) { "compareOldTravelData, first generate travel album fail, size:${data.size} < $MIN_COUNT_IN_TRAVEL_ALBUM" }
            }
        } else {
            // 已经生成旅程图集，需要对比之前的只插入新增的，删除已经不在图集里的
            delData = (oldData - newData).toMutableSet()
            insData = (newData - oldData).toMutableSet()
            TravelFileExtendOperator.writeTravelInfoToFile(context, insData.toList(), oldData.first().travelDestination)
            TravelDBTableHelper.insertTravelData(insData.toList())
            TravelDBTableHelper.deleteTravelData(delData.toList())
            recordScanCount(insData.size)
            scannedTotalCount += insData.size
            GLog.w(TAG, LogFlag.DL) { "compareOldTravelData, repeat generate travel album, oldData:${oldData.size}, newData:${newData.size}, " +
                    "delData:${delData.size}, insData:${insData.size}" }
        }
    }

    /**
     * 是否之前已经存在该范围内的图集，如果存在则删除该图集，重新生成范围更大的图集
     * 仅适用于之前生成图集时过滤了首尾城市(首尾城市照片数量小于10被识别为路过城市)，且此时DT有效旅程序列还存在
     */
    private fun clearExistTravelAlbum(startTime: Long, endTime: Long) {
        val existData = TravelDBTableHelper.getGalleryIdInTimeRange(startTime, endTime)
        GLog.d(TAG, LogFlag.DL) { "clearExistTravelAlbum, existData size:${existData.size}" }
        TravelDBTableHelper.deleteTravelData(existData)
    }

    /**
     * 记录 SCAN_COUNT_ALL 和 SCAN_COUNT_24H 的数据
     */
    private fun recordScanCount(count: Int) {
        addScanCountIfNoCharging(count)
        recordScanInfoIfNeed(count)
    }

    /**
     * 记录24小时内扫描数量在未充电情况下
     */
    private fun addScanCountIfNoCharging(count: Int): Boolean {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true
        }
        refreshScanRecordingIfNecessary(context)
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            scannedCountIn24hours = (it.getIntConfig(TRAVEL_SCAN_COUNT_24H, 0) ?: 0) + count
            it.setIntConfig(TRAVEL_SCAN_COUNT_24H, scannedCountIn24hours)
        }
        return false
    }

    /**
     * 记录总的扫描数量
     */
    private fun recordScanInfoIfNeed(count: Int) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return
        }
        (context.applicationContext as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            val scanCountAll = (it.getIntConfig(TRAVEL_SCAN_COUNT_ALL, 0) ?: 0) + count
            it.setIntConfig(TRAVEL_SCAN_COUNT_ALL, scanCountAll)
            GalleryScanUtils.recordInfo(context, TAG, scannedCountIn24hours.toLong(), scanCountAll.toLong())
        }
    }

    /**
     * 检查是否有新的数据可加入现有旅程
     */
    private fun dealExistNewTravelDataNotInTravelTable() {
        val insertNewTravelData = TravelDBTableHelper.insertExistNewTravelDataNotInTravelTable(context)
        recordScanCount(insertNewTravelData)
        scannedTotalCount += insertNewTravelData
        GLog.d(TAG, LogFlag.DL) { "dealExistNewTravelDataNotInTravelTable, insertNewTravelData size:$insertNewTravelData" }
    }

    /**
     * 检查是否存在文件里有旅程数据但却没在旅程表里，如有则生成旅程数据插入表里
     */
    private fun dealExistTravelInfoNotInTravelTable() {
        val insertExistTravelData = TravelDBTableHelper.insertExistTravelInfoNotInTravelTable(context)
        recordScanCount(insertExistTravelData)
        scannedTotalCount += insertExistTravelData
        GLog.d(TAG, LogFlag.DL) { "dealExistTravelInfoNotInTravelTable, insertExistTravelData size:$insertExistTravelData" }
    }

    /**
     * 封面图片做模糊背景
     */
    private fun doBackgroundBlurredForCover() {
        val startTime = System.currentTimeMillis()
        val travelIdList = TravelDBTableHelper.getTravelAlbumTravelId()
        val filePathMap = TravelDBTableHelper.loadCoverItemsFilePath(travelIdList)
        TravelCoverBlurredHelper.doCoverBackgroundBlurred(context, filePathMap)
        GLog.d(TAG, LogFlag.DL) { "doCoverBackgroundBlurred, album size:${filePathMap.size}, cost time:${GLog.getTime(startTime)}" }
    }

    /**
     * travel扫描非必须在充电状态下
     */
    override fun runTaskIsNeedCharging(): Boolean {
        return false
    }

    /**
     * 获取扫描类型
     */
    override fun getScanType(): Int {
        return TRAVEL_SCAN
    }

    /**
     * 获取扫描名字
     */
    override fun getSceneName(): String {
        return SCENE_NAME
    }

    companion object {
        private const val TAG = "${ScanConst.TAG}TravelScanner"
        private const val SCENE_NAME = "TravelScanner"

        /**
         * 一段旅程至少需要三个地址时间信息，此处用于map判断，map起点和终点key一样
         */
        private const val MIN_LOCATION_AND_TIME_MAP_SIZE = 2

        /**
         * 一段旅程至少需要三个地址时间信息，此处用于list判断，list中起点和终点已分离
         */
        private const val MIN_LOCATION_AND_TIME_LIST_SIZE = 3

        /**
         * 判断路过城市，当照片数量小于该值，则确定为路过城市
         */
        private const val THE_NUM_OF_PASSING_BY_CITY = 10

        /**
         * 一段旅程包含两个省级行政区或地级行政区
         */
        private const val TWO_PROVINCE_OR_CITY = 2
    }

    data class OriginalTravelData(
        var locationAndTimeList: MutableList<Pair<String, Pair<Long, Long>>> = mutableListOf(),
        var travelStartTime: Long = 0L,
        var travelEndTime: Long = 0L
    )

    /**
     * 存储某个时刻的常驻地信息，防止旅程城市变为常驻地城市无法识别出旅程
     * @param residenceMap  key：“常驻地点:该常驻地点的开始时间-该常驻地点的结束时间” value：
     * <Pair<String, Set<String>> first：起点常驻地， second：当前的所有常驻地
     */
    data class DTResidence(val residenceMap: MutableMap<String, Pair<String, List<String>>>)
}