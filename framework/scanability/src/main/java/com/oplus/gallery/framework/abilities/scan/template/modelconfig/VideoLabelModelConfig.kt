/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : VideoLabelModelConfig.kt
 ** Description : 视频标签扫描资源配置类
 ** Version     : 1.0
 ** Date        : 2024/3/19
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                            2024/3/19      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 视频标签扫描资源配置类
 */
class VideoLabelModelConfig : ModelConfig(ModelName.VIDEO_LABEL_SCAN_SOURCE_V2) {

    override val modelDataRefreshFlagKey: String = ComponentPrefUtils.HAS_VIDEO_LABEL_DATA_UPDATE_SUCCESS_KEY

    override var currentVersion: Int
        get() = ComponentPrefUtils.getIntPref(ContextGetter.context, ComponentPrefUtils.VIDEO_LABEL_COMPONENT_VERSION_KEY, 0)
        set(value) {
            ComponentPrefUtils.setIntPref(ContextGetter.context, ComponentPrefUtils.VIDEO_LABEL_COMPONENT_VERSION_KEY, value)
        }
}