/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : SeniorVersionInfo.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/6/13 16:03
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2023/6/13  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect

import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.util.text.JsonUtil
import org.json.JSONObject

/**
 * 优选数据版本信息，key，toString的顺序都慎重修改，会引起重新扫描
 */
data class SeniorVersionInfo(
    @SerializedName("iqaVersion") val iqaVersion: Int,
    @SerializedName("ssaVersion") val ssaVersion: Int,
    @SerializedName("similarFeatureVersion") val similarFeatureVersion: Int,
    @SerializedName("labelVersion") val labelVersion: Int,
    @SerializedName("imageFilterVersion") val imageFilterVersion: Int
) {
    override fun toString(): String {
        val jsonObject = JSONObject()
        jsonObject.put(TAG_IQA_VERSION, iqaVersion)
        jsonObject.put(TAG_SSA_VERSION, ssaVersion)
        jsonObject.put(TAG_SIMILAR_FEATURE_VERSION, similarFeatureVersion)
        jsonObject.put(TAG_LABEL_VERSION, labelVersion)
        jsonObject.put(TAG_IMAGE_FILTER_VERSION, imageFilterVersion)
        return jsonObject.toString()
    }

    fun toObject(json: String): SeniorVersionInfo {
        return JsonUtil.fromJson(json, SeniorVersionInfo::class.java)
    }

    companion object {
        private const val TAG_IQA_VERSION = "iqaVersion"
        private const val TAG_SSA_VERSION = "ssaVersion"
        private const val TAG_SIMILAR_FEATURE_VERSION = "similarFeatureVersion"
        private const val TAG_LABEL_VERSION = "labelVersion"
        private const val TAG_IMAGE_FILTER_VERSION = "imageFilterVersion"
    }
}