/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MultiModalDictVulgarismsConfig.kt
 ** Description : 多模态不雅词词典资源配置类
 ** Version     : 1.0
 ** Date        : 2024/3/19
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                            2024/3/19      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_MULTIMODAL
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 多模态不雅词词典资源配置类
 */
class MultiModalDictVulgarismsConfig : ModelConfig(ModelName.DICT_VULGARISMS) {

    override val isModelAllowDownload: Boolean
        get() = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_MULTIMODAL, defValue = true, expDefValue = false)

    override val modelDataRefreshFlagKey: String = ComponentPrefUtils.HAS_MULTI_MODAL_DICT_VULGARISMS_DATA_UPDATE_SUCCESS_KEY

    override var currentVersion: Int
        get() = SPUtils.getInt(
            ContextGetter.context,
            ComponentPrefUtils.COMPONENT_PREF_NAME,
            ComponentPrefUtils.MULTI_MODAL_DICT_VULGARISMS_COMPONENT_VERSION_KEY,
            0
        )
        set(value) {
            SPUtils.setInt(
                ContextGetter.context,
                ComponentPrefUtils.COMPONENT_PREF_NAME,
                ComponentPrefUtils.MULTI_MODAL_DICT_VULGARISMS_COMPONENT_VERSION_KEY,
                value
            )
        }
}