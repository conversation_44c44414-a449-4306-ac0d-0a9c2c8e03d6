/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FestivalMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/16
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D    2018/3/16       1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.app.SearchManager;
import android.app.SearchableInfo;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector;
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector.FestivalInfo;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper.DateRange;
import com.oplus.gallery.router_lib.RouterManager;
import com.oplus.gallery.router_lib.meta.PostCard;
import com.oplus.gallery.router_lib.meta.RouterMeta;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SEARCH_ACTIVITY;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_FORCE;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_INPUT;
import static com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem.INDEX_ID;

public class FestivalMemories extends Memories {
    private static final String TAG = "FestivalMemories";
    private static final String STOP_REASON_SCAN_EMPTY = TAG + "_ScanEmpty";
    private static final String STOP_REASON_HIGH_PRIORITY = TAG + "_HighPriority";
    private static final String STOP_REASON_NORMAL_PRIORITY = TAG + "_NormalPriority";
    private static final String STOP_REASON_ROUTER_NOT_FOUND = TAG + "_RouterNotFound";
    private static final String STOP_REASON_NOT_CURRENT_YEAR = "_NotCurrentYear";
    private static final String STOP_REASON_CURRENT_IN_DATE_RANGE = "_CurrentInDateRange";
    private static final String ID_LIST_SEPARATOR = ",";

    public FestivalMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        return true;
    }

    @Override
    public boolean scanMemories() {
        RouterMeta routerMeta = RouterManager.getRouterCenter().getRouter(new PostCard(SEARCH_ACTIVITY));
        if ((routerMeta == null) || (routerMeta.getClazz() == null)) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_ROUTER_NOT_FOUND;
            return false;
        }
        SearchManager searchManager = (SearchManager) mContext.getSystemService(Service.SEARCH_SERVICE);
        ComponentName componentName = new ComponentName(mContext, routerMeta.getClazz());
        SearchableInfo searchInfo = searchManager.getSearchableInfo(componentName);
        FestivalSelector selector = new FestivalSelector();
        selector.addCountryFestivals();
        List<FestivalSelector.FestivalInfo> festivalInfo = selector.getDisplayFestivals();
        if ((festivalInfo == null) || festivalInfo.isEmpty()) {
            GLog.v(TAG, "scanMemories, festivalInfo is empty!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_SCAN_EMPTY;
            return false;
        }

        if (isHighPriorityMemories()) {
            boolean scanHighPriority = scanHighPriority(searchManager, searchInfo, festivalInfo);
            GLog.v(TAG, "scanMemories, scanHighPriority:" + scanHighPriority);
            return scanHighPriority;
        } else {
            boolean scanNormalPriority = scanNormalPriority(searchManager, searchInfo, festivalInfo);
            GLog.v(TAG, "scanMemories, scanNormalPriority:" + scanNormalPriority);
            return scanNormalPriority;
        }
    }

    private boolean scanHighPriority(SearchManager searchManager, SearchableInfo searchInfo, List<FestivalInfo> festivalInfo) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        // get current date string
        Date curDate = new Date();
        String curDateStr = formatter.format(curDate);

        // get all offset date string
        List<String> dateStrList = new ArrayList<>();
        for (int offset = MemoriesScannerHelper.FESTIVAL_MEMORIES_TRIGGER_DAY_OFFSET; offset < 0; offset++) {
            Date date = MemoriesScannerHelper.getDateOfOffset(new Date(), offset);
            String dateStr = formatter.format(date);
            dateStrList.add(dateStr);
        }

        for (FestivalSelector.FestivalInfo info : festivalInfo) {
            String festivalName = null;
            if ((!info.containsDate(curDateStr) && info.hasJointDate(dateStrList))) {
                festivalName = info.getKey();
            }
            if (TextUtils.isEmpty(festivalName)) {
                continue;
            }

            String mediaIds = getFestivalMediaIds(searchManager, searchInfo, festivalName, mMemoriesPicMin);
            ArrayList<MediaItem> items = getMediaItemById(mContext, convertStringToIdList(mediaIds), mMemoriesPicMin);
            mMediaItemList = MemoriesProviderHelper.getFilteredItemList(items, getLabelFilterIdList());
            boolean allowMemoriesCreate = allowHighPriorityCreate(mMediaItemList);
            GLog.d(TAG, "scanHighPriority, allowNormalPriorityCreate:" + allowMemoriesCreate
                    + ",mMediaItemList size:" + mMediaItemList.size());
            if (allowMemoriesCreate && (mMediaItemList.size() > mMemoriesPicMin)) {
                setMemoriesName(festivalName);
                return true;
            } else if (mStopSubReason == null) {
                mStopSubReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            }
        }
        mStopReason = STOP_REASON_HIGH_PRIORITY + mStopSubReason;
        return false;
    }

    private boolean scanNormalPriority(SearchManager searchManager, SearchableInfo searchInfo, List<FestivalInfo> festivalInfo) {
        long curTime = System.currentTimeMillis();
        Date curDate = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String curDateStr = formatter.format(curDate);
        for (FestivalSelector.FestivalInfo info : festivalInfo) {
            String mediaIds = getFestivalMediaIds(searchManager, searchInfo, info.getKey(), mMemoriesPicMin);
            ArrayList<MediaItem> items = getMediaItemById(mContext, convertStringToIdList(mediaIds), mMemoriesPicMin);
            mMediaItemList = MemoriesProviderHelper.getFilteredItemList(items, getLabelFilterIdList());
            boolean allowMemoriesCreate = allowNormalPriorityCreate(mMediaItemList, curTime, info, curDateStr);
            GLog.d(TAG, "scanNormalPriority, allowNormalPriorityCreate:" + allowMemoriesCreate
                    + ",mMediaItemList size:" + mMediaItemList.size());
            if (allowMemoriesCreate && (mMediaItemList.size() > mMemoriesPicMin)) {
                setMemoriesName(info.getKey());
                return true;
            } else if (mStopSubReason == null) {
                mStopSubReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            }
        }
        // 扫描中断，记录原因
        mStopReason = STOP_REASON_NORMAL_PRIORITY + mStopSubReason;
        return false;
    }

    private boolean allowHighPriorityCreate(List<MediaItem> itemList) {
        mStopSubReason = null;
        DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        if (dateRange == null) {
            GLog.w(TAG, "allowHighPriorityCreate, dateRange is null!");
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_DATE_RANGE_NULL;
            return false;
        }
        if (!MemoriesScannerHelper.isDateInCurrentYear(new Date(dateRange.mEnd))) {
            GLog.d(TAG, "allowHighPriorityCreate, items is not in current year!");
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_NOT_CURRENT_YEAR;
            return false;
        }
        if (MemoriesProviderHelper.hasMemoriesCreatedInDateRange(dateRange, getMemoriesId())) {
            GLog.d(TAG, "allowHighPriorityCreate, memories has created in date range:" + dateRange);
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_CREATED_IN_DATE_RANGE;
            return false;
        }
        return true;
    }

    private boolean allowNormalPriorityCreate(List<MediaItem> itemList, long curTime, FestivalInfo info,
                                              String curDateStr) {
        mStopSubReason = null;
        DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        if (dateRange == null) {
            GLog.w(TAG, "allowNormalPriorityCreate, dateRange is null!");
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_DATE_RANGE_NULL;
            return false;
        }
        if (MemoriesScannerHelper.getDaysBetweenTwoDates(dateRange.mEnd, curTime) <= 0) {
            // we only generate FestivalMemories before today
            GLog.d(TAG, "allowNormalPriorityCreate, festival memories is not before today!");
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_NOT_BEFORE_TODAY;
            return false;
        }
        List<Integer> typeList = new ArrayList<>();
        typeList.add(MemoriesType.EVENT_FESTIVAL_MEMORIES);
        typeList.add(MemoriesType.EVENT_LAST_YEAR_FESTIVAL_MEMORIES);
        if (MemoriesProviderHelper.hasMemoriesCreatedInDateRange(dateRange, typeList)) {
            GLog.d(TAG, "allowNormalPriorityCreate, memories has created in date range:" + dateRange);
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_CREATED_IN_DATE_RANGE;
            return false;
        }
        if (MemoriesScannerHelper.isDateInCurrentYear(new Date(dateRange.mEnd))) {
            if (info.containsDate(curDateStr)) {
                GLog.d(TAG, "allowNormalPriorityCreate, current time is in festival date range!");
                // 扫描中断，记录原因
                mStopSubReason = STOP_REASON_CURRENT_IN_DATE_RANGE;
                return false;
            }
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return createMemories(mMediaItemList);
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.EVENT_FESTIVAL_MEMORIES;
    }


    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_FESTIVAL_MEMORIES;
    }


    private ArrayList<String> getRecommendFestivals(SearchManager searchManager, SearchableInfo searchInfo, boolean forceQuery) {
        Cursor festivalCursor = null;
        ArrayList<String> festivalNames = new ArrayList<>();
        try {
            festivalCursor = SearchCommonUtils.getSuggestions(mContext, searchInfo, SearchSuggestionProviderUtil.QUERY_RECOMMEND_FESTIVAL
                    + "?" + QUERY_FORCE + "=" + forceQuery);
            if ((festivalCursor != null) && (festivalCursor.getCount() > 0)) {
                int indexName = festivalCursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMNNAME);
                while (festivalCursor.moveToNext()) {
                    String name = festivalCursor.getString(indexName);
                    festivalNames.add(name);
                }
            }
        } catch (Throwable thr) {
            GLog.e(TAG, "-getRecommendFestivalFromDB, Throwable is " + thr);
        } finally {
            IOUtils.closeQuietly(festivalCursor);
        }
        return festivalNames;
    }

    private String getFestivalMediaIds(SearchManager searchManager, SearchableInfo searchInfo, String festivalName, int minCount) {
        Cursor cursor = null;
        try {
            final String uriPrefix = SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_TIME;
            cursor = SearchCommonUtils.getSuggestions(mContext, searchInfo, uriPrefix + "?" + QUERY_INPUT + "=" + Uri.encode(festivalName));
            if ((cursor != null) && cursor.moveToFirst()) {
                String name = cursor.getString(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil.SEARCH_RESULT_NAME));
                int count = cursor.getInt(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil.SEARCH_RESULT_GALLERY_COUNT));
                int searchType = cursor.getInt(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil.SEARCH_RESULT_TYPE));
                String idList = cursor.getString(cursor.getColumnIndexOrThrow(SearchSuggestionProviderUtil.SEARCH_RESULT_ID_LIST));
                GLog.v(TAG, String.format("[getFestivalMediaIds] Result = {name:'%s', "
                        + "count:%d, type:%d, idList:'%s'}", name, count, searchType, idList));
                if (count >= minCount) {
                    return idList;
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "query Exception:", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return null;
    }

    private ArrayList<Integer> convertStringToIdList(String idListString) {
        if (TextUtils.isEmpty(idListString)) {
            return null;
        }
        ArrayList<Integer> mediaIds = new ArrayList<>();
        String[] sepString = idListString.split(ID_LIST_SEPARATOR);
        for (String str : sepString) {
            mediaIds.add(Integer.valueOf(str));
        }
        return mediaIds;
    }

    private ArrayList<MediaItem> getMediaItemById(Context context, ArrayList<Integer> mediaIds, int minCount) {
        if ((mediaIds == null) || (mediaIds.size() == 0)) {
            return null;
        }
        ArrayList<MediaItem> mediaItems = new ArrayList<>();
        Cursor cursorMedia = null;
        try {
            StringBuilder selection = new StringBuilder();
            selection.append(MemoriesProviderHelper.getWhereClauseOfMemoriesScanFolders(context));
            selection.append(" AND ");
            selection.append(MemoriesProviderHelper.getWhereClauseOfMimeTypes(context));
            selection.append(" AND media_id IN (");
            for (int id : mediaIds) {
                selection.append(id);
                selection.append(",");
            }
            selection.replace(selection.length() - 1, selection.length(), ")");
            String orderClause = MediaStore.Images.Media.DATE_TAKEN + " DESC";
            cursorMedia = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(LocalMediaItem.getProjection())
                    .setWhere(selection.toString())
                    .setOrderBy(orderClause)
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursorMedia == null) || (cursorMedia.getCount() < minCount)) {
                return null;
            }
            int dateTakenIndex = cursorMedia.getColumnIndex(MediaStore.Images.ImageColumns.DATE_TAKEN);
            Calendar calendar = Calendar.getInstance();
            int currentYear = -1;
            List<Integer> typeList = new ArrayList<>();
            typeList.add(MemoriesType.EVENT_FESTIVAL_MEMORIES);
            typeList.add(MemoriesType.EVENT_LAST_YEAR_FESTIVAL_MEMORIES);
            while (cursorMedia.moveToNext()) {
                calendar.setTimeInMillis(cursorMedia.getLong(dateTakenIndex));
                if (currentYear < 0) {
                    currentYear = calendar.get(Calendar.YEAR);
                }
                int tempYear = calendar.get(Calendar.YEAR);
                int id = cursorMedia.getInt(INDEX_ID);
                Path childPath = LocalImage.ITEM_PATH.getChild(id);
                MediaItem mediaItem = LocalMediaItem.loadOrUpdateItem(childPath,cursorMedia);
                if (currentYear != tempYear) {
                    currentYear = tempYear;
                    // If is high priority memories, we only collect festival items of latest year.
                    if (isHighPriorityMemories()) {
                        return mediaItems;
                    }
                    if (mediaItems.size() > minCount) {
                        DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(mediaItems);
                        if (MemoriesProviderHelper.hasMemoriesCreatedInDateRange(dateRange, typeList)) {
                            GLog.d(TAG, "getMediaItemById, memories has created in date range:" + dateRange);
                        } else {
                            return mediaItems;
                        }
                    }
                    mediaItems.clear();
                }
                mediaItems.add(mediaItem);
            }
        } catch (Exception e) {
            GLog.w(TAG, "getMediaItemById, exception:" + e);
        } finally {
            IOUtils.closeQuietly(cursorMedia);
        }
        return mediaItems;
    }
}
