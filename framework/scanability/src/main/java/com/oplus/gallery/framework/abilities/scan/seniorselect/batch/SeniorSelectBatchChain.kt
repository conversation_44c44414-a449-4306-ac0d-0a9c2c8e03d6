/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SeniorSelectBatchManager
 ** Description: 精选扫描分批策略
 **
 ** Version: 1.0
 ** Date: 2022/01/08
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/01/08  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.batch

import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectDBOperation
import com.oplus.gallery.foundation.util.debug.GLog

class SeniorSelectBatchChain : Batch<BatchScanData>() {
    private val oneDayBatch = OneDayBatch()
    private val multiDayBatch = MultiDayBatch()

    /**
     *  BatchChain  --next--> multiDayBatch --next--> oneDayBatch
     *                                      <--next--
     */
    init {
        nextBatch = multiDayBatch
        multiDayBatch.nextBatch = oneDayBatch
        oneDayBatch.nextBatch = multiDayBatch
        updateDataToNextBatch()
    }

    override fun hasNext(): Boolean {
        return nextBatch?.hasNext() == true
    }

    override fun isSwitchToNextBatch(): Boolean {
        return true
    }

    override fun updateDataToNextBatch() {
        val notScannedNodeList = SeniorSelectDBOperation.queryCountOfPerNodeWhichNotScanned()
        if (notScannedNodeList.isEmpty()) {
            GLog.d(TAG) { "updateDataToNextBatch. nothing update." }
            return
        }
        val allNotScannedInfo = SeniorSelectDBOperation.queryImageInfoWhichNotScanned()
        val endDate = notScannedNodeList[0].date
        val startDate = notScannedNodeList[notScannedNodeList.size - 1].date
        val scannedNodeList = SeniorSelectDBOperation.queryCountOfPerNodeWhichScanned(startDate, endDate)
        val allScannedInfo = if (scannedNodeList.isEmpty().not()) {
            SeniorSelectDBOperation.queryImageInfoWhichScanned(startDate, endDate)
        } else {
            mutableListOf()
        }
        GLog.d(TAG) {
            "updateDataToNextBatch. notScannedNodeSize=${notScannedNodeList.size}, scannedNodeSize=${scannedNodeList.size}, " +
                "notScannedItemSize=${allNotScannedInfo.size}, scannedItemSize=${allScannedInfo.size}"
        }
        nextBatch?.let {
            it.updateNodeList(notScannedNodeList, scannedNodeList)
            it.updateInfoList(allNotScannedInfo, allScannedInfo)
        }
    }

    override fun getData(): BatchScanData {
        return BatchScanData()
    }

    companion object {
        private const val TAG = "SeniorSelectBatchChain"
    }
}