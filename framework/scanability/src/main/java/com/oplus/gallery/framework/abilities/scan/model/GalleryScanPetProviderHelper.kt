/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : GalleryScanPetProviderHelper.kt
 ** Description : 宠物扫描数据库操作 helper 类
 ** Version     : 1.0
 ** Date        : 2025/05/08
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/05/08  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.model

import android.content.ContentValues
import android.database.Cursor
import android.graphics.RectF
import androidx.collection.LongSparseArray
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.business_lib.model.data.label.bean.PetImageInfo
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryMedia
import com.oplus.gallery.foundation.database.store.GalleryStore.Pet
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.DISTINCT
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOUBLE_QUOTE
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_ONE
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_TO
import com.oplus.gallery.foundation.database.util.SQLGrammar.FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.IN
import com.oplus.gallery.foundation.database.util.SQLGrammar.INNER_JOIN
import com.oplus.gallery.foundation.database.util.SQLGrammar.IS_NOT_EMPTY
import com.oplus.gallery.foundation.database.util.SQLGrammar.IS_NOT_NULL
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.NOT_EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.NOT_EQUAL_ONE
import com.oplus.gallery.foundation.database.util.SQLGrammar.ON
import com.oplus.gallery.foundation.database.util.SQLGrammar.OR
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.pet.PetCluster

/**
 * 宠物扫描数据库操作 helper 类
 */
object GalleryScanPetProviderHelper {
    private const val TAG = "GalleryScanPetProviderHelper"
    private const val NUMBER_OF_FEATURES = "number_of_features"
    private const val DELETE_GROUP_COUNT = 50

    private val PET_QUERY_PROJECT = arrayOf(
        Pet._ID,
        Pet.DATA,
        Pet.MEDIA_TYPE,
        Pet.THUMB_W,
        Pet.THUMB_H,
        Pet.GROUP_ID,
        Pet.FEATURE,
        Pet.SCORE,
        Pet.FINAL_SCORE,
        Pet.APPEAR_MUL,
        Pet.PET_COVER_SCORE,
        Pet.PET_COVER_WEIGHT,
        Pet.LEFT,
        Pet.TOP,
        Pet.RIGHT,
        Pet.BOTTOM,
        Pet.IS_DEFAULT_COVER
    )

    /**
     * 获取已经扫描过并且不在回收站中的图片/视频信息
     *
     * SQL:
     *
     * SELECT
     *  DISTINCT local_media._id, media_id, local_media._data, date_modified, datetaken,
     *  local_media.invalid, pet.media_type, is_recycled, model_version
     * FROM local_media
     * INNER JOIN pet
     * ON local_media._data = pet._data
     * WHERE
     *  (local_media.is_trashed = 0)
     *  AND media_id > 0
     *  AND is_recycled != 1
     */
    @JvmStatic
    fun getScannedItemsWithoutRecycled(): List<PetImageInfo> {
        val sb = StringBuilder().apply {
            append(SELECT)
            append(DISTINCT + GalleryMedia.TAB + DOT + LocalColumns._ID + COMMA)
            append(LocalColumns.MEDIA_ID + COMMA)
            append(GalleryMedia.TAB + DOT + LocalColumns.DATA + COMMA)
            append(LocalColumns.DATE_MODIFIED + COMMA)
            append(LocalColumns.DATE_TAKEN + COMMA)
            append(GalleryMedia.TAB + DOT + LocalColumns.INVALID + COMMA)
            append(Pet.TAB + DOT + Pet.MEDIA_TYPE + COMMA)
            append(Pet.IS_RECYCLED + COMMA)
            append(Pet.MODEL_VERSION)
            append(FROM + GalleryMedia.TAB)
            append(INNER_JOIN + Pet.TAB)
            append(ON + GalleryMedia.TAB + DOT + LocalColumns.DATA + EQUAL + Pet.TAB + DOT + Pet.DATA)
            append(WHERE)
            append(DatabaseUtils.getFilterOutTrashedWhere(GalleryMedia.TAB))
            append(AND + DatabaseUtils.getOnlyLocalWhere())
            append(AND + Pet.IS_RECYCLED + NOT_EQUAL_ONE)
        }
        runCatching {
            RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(sb.toString())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count > 0) {
                        return PetImageInfo.buildPetImageInfoList(cursor)
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getPetImageInfoList fail. $it" }
        }
        return mutableListOf()
    }

    /**
     * 当扫描模型更新后，删除无效的宠物数据
     * SQL: DELETE FROM pet WHERE (invalid = 1 OR is_recycled = 1) AND model_version = [oldVersion]
     */
    @JvmStatic
    fun deleteInvalidPet(oldVersion: String?) {
        if (oldVersion == null) {
            GLog.w(TAG, LogFlag.DL) { "deleteInvalidPet,  old version is null, skip." }
        }
        kotlin.runCatching {
            val whereClause = (LEFT_BRACKETS + Pet.INVALID + EQUAL_ONE
                    + OR + Pet.IS_RECYCLED + EQUAL_ONE + RIGHT_BRACKETS
                    + AND + Pet.MODEL_VERSION + EQUAL_TO)
            DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setWhere(whereClause)
                .setWhereArgs(arrayOf(oldVersion))
                .build()
                .exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteInvalidPet, e:$it" }
        }
    }

    /**
     * 删除指定的宠物扫描记录
     */
    @JvmStatic
    fun deleteImages(list: List<BaseImageInfo>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "deleteImages,  list is empty." }
            return
        }

        // 分组删除，防止sql语句过长导致删除失败。
        val deleteOperations = mutableListOf<DeleteReq>()
        val size = list.size
        var index = 0

        while (index < size) {
            val fromIndex = index
            val toIndex = (index + DELETE_GROUP_COUNT).coerceAtMost(size)
            index += DELETE_GROUP_COUNT

            GLog.d(TAG, LogFlag.DL) { "deleteImages,  size=$size, index=$index, fromIndex=$fromIndex, toIndex=$toIndex" }
            StringBuilder().apply {
                append(Pet.DATA + IN + LEFT_BRACKETS)
                list.subList(fromIndex, toIndex).forEach { image ->
                    append(DOUBLE_QUOTE + image.mFilePath + DOUBLE_QUOTE + COMMA)
                }
                deleteCharAt(length - 1)
                append(RIGHT_BRACKETS)

                GLog.d(TAG, LogFlag.DL) { "deleteImages, list.size=${list.size}, Delete From pet WHERE $this" }
                DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.PET)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(this.toString())
                    .build().let {
                        deleteOperations.add(it)
                    }
            }
        }

        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(deleteOperations)
                .build()
                .exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteImages,  delete fail. $it" }
        }
    }

    /**
     * 批量更新 INVALID、IS_CHOSEN 的状态。[IS_RECYCLED 可选更新]
     */
    @JvmStatic
    fun updateImagesInvalid(list: List<BaseImageInfo>, withMediaId: Boolean = false) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "updateImagesInvalid,  list is empty." }
            return
        }

        // 分组更新，防止sql语句过长导致更新失败。
        val updateOperations = mutableListOf<UpdateReq>()
        val size = list.size
        var index = 0
        while (index < size) {
            val fromIndex = index
            val toIndex = (index + DELETE_GROUP_COUNT).coerceAtMost(size)
            index += DELETE_GROUP_COUNT
            GLog.d(TAG, LogFlag.DL) { "updateImagesInvalid[$withMediaId],  size=$size, index=$index, fromIndex=$fromIndex, toIndex=$toIndex" }
            addUpdateOperations(updateOperations, list.subList(fromIndex, toIndex), withMediaId)
        }
        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY).addDataReqs(updateOperations).build().exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateImagesInvalid,  update fail. $it" }
        }
    }

    private fun addUpdateOperations(updateOperations: MutableList<UpdateReq>, list: List<BaseImageInfo>, withMediaId: Boolean) {
        val invalidValue = ContentValues()
        val validValue = ContentValues()
        var invalidSb: StringBuilder? = null
        var validSb: StringBuilder? = null
        list.forEach { image ->
            if (image.mInvalid) {
                if (invalidSb == null) {
                    invalidSb = StringBuilder()
                    invalidSb?.append(Pet.DATA + IN + LEFT_BRACKETS)
                    invalidValue.put(Pet.INVALID, true)
                    invalidValue.put(Pet.IS_CHOSEN, false)
                    if (withMediaId) invalidValue.put(Pet.IS_RECYCLED, false)
                }
                invalidSb?.append(DOUBLE_QUOTE + image.mFilePath + DOUBLE_QUOTE + COMMA)
            } else {
                if (validSb == null) {
                    validSb = StringBuilder()
                    validSb?.append(Pet.DATA + IN + LEFT_BRACKETS)
                    validValue.put(Pet.INVALID, false)
                    if (withMediaId) validValue.put(Pet.IS_RECYCLED, false)
                }
                validSb?.append(DOUBLE_QUOTE + image.mFilePath + DOUBLE_QUOTE + COMMA)
            }
        }
        invalidSb?.apply {
            deleteCharAt(length - 1)
            append(RIGHT_BRACKETS)
            GLog.d(TAG, LogFlag.DL) { "addUpdateOperations[$withMediaId], list.size=${list.size}, Update pet WHERE $this" }
            addUpdateOperations(updateOperations, this.toString(), invalidValue)
        }
        validSb?.apply {
            deleteCharAt(length - 1)
            append(RIGHT_BRACKETS)
            GLog.d(TAG, LogFlag.DL) { "addUpdateOperations[$withMediaId], list.size=${list.size}, Update pet WHERE $this" }
            addUpdateOperations(updateOperations, this.toString(), validValue)
        }
    }

    private fun addUpdateOperations(list: MutableList<UpdateReq>, where: String, contentValues: ContentValues) {
        UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.PET)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setWhere(where)
            .setConvert { contentValues }
            .build().let {
                list.add(it)
            }
    }

    /**
     * 批量更新 INVALID、IS_CHOSEN、IS_RECYCLED 的状态。
     */
    @JvmStatic
    fun updateImageInvalidWithMediaId(list: List<BaseImageInfo>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "updateImageInvalidWithMediaId,  list is empty." }
            return
        }
        updateImagesInvalid(list, withMediaId = true)
    }

    /**
     * 查询是否存在未聚合的宠物数据。
     * @return 存在未聚合数据返回 true，否则返回 false。
     */
    @JvmStatic
    fun haveNotGroupPet(): Boolean {
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setProjection(arrayOf(Pet._ID))
                .setWhere(
                    Pet.DATA + IS_NOT_NULL
                            + AND + Pet.INVALID + NOT_EQUAL_ONE
                            + AND + Pet.IS_RECYCLED + NOT_EQUAL_ONE
                            + AND + Pet.FEATURE + IS_NOT_NULL
                            + AND + Pet.GROUP_ID + EQUAL + Pet.GROUP_ID_0
                )
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    return (cursor.count > 0)
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "haveNotGroupPet,  fail. $it" }
        }
        return false
    }

    @JvmStatic
    fun insertData(list: List<PetCluster>): Array<BatchResult>? {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "insertData,  list is empty!" }
            return null
        }

        val operations = mutableListOf<InsertReq>()
        list.forEach { petCluster ->
            InsertReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setConvert { petCluster.buildInsertContentValues() }
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .build()?.let { operations.add(it) }
        }

        var result: Array<BatchResult>? = null
        runCatching {
            result = BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build()
                .exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "insertData,  fail. $it" }
        }
        return result
    }

    @JvmStatic
    fun classifyPetInfoList(
        filePath: String,
        newPetClusterList: List<PetCluster>,
        updatePetClusterList: MutableList<PetCluster>,
        deletePetIdList: MutableList<Long>
    ) {
        val oldPetClusterList = getPetClusterByFilePath(filePath)
        oldPetClusterList.forEach { oldPetCluster ->
            var isPetMatched = false
            newPetClusterList.forEach { newPetCluster ->
                if (newPetCluster.isSamePet(oldPetCluster)) {
                    newPetCluster.id = oldPetCluster.id
                    updatePetClusterList.add(newPetCluster)
                    isPetMatched = true
                }
            }
            if (!isPetMatched) {
                oldPetCluster.id?.let { deletePetIdList.add(it) }
            }
        }
    }

    private fun getPetClusterByFilePath(filePath: String): List<PetCluster> {
        val list = mutableListOf<PetCluster>()
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setProjection(arrayOf(Pet._ID, Pet.THUMB_W, Pet.THUMB_H, Pet.LEFT, Pet.TOP, Pet.RIGHT, Pet.BOTTOM))
                .setWhere(Pet.DATA + EQUAL_TO)
                .setWhereArgs(arrayOf(filePath))
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val idIndex = cursor.getColumnIndex(Pet._ID)
                    val thumbWIndex = cursor.getColumnIndex(Pet.THUMB_W)
                    val thumbHIndex = cursor.getColumnIndex(Pet.THUMB_H)
                    val leftIndex = cursor.getColumnIndex(Pet.LEFT)
                    val topIndex = cursor.getColumnIndex(Pet.TOP)
                    val rightIndex = cursor.getColumnIndex(Pet.RIGHT)
                    val bottomIndex = cursor.getColumnIndex(Pet.BOTTOM)
                    while (cursor.moveToNext()) {
                        PetCluster().apply {
                            id = cursor.getLong(idIndex)
                            thumbWidth = cursor.getInt(thumbWIndex)
                            thumbHeight = cursor.getInt(thumbHIndex)
                            left = cursor.getInt(leftIndex)
                            top = cursor.getInt(topIndex)
                            right = cursor.getInt(rightIndex)
                            bottom = cursor.getInt(bottomIndex)
                            list.add(this)
                        }
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getPetClusterByFilePath,  fail. $it" }
        }
        return list
    }

    @JvmStatic
    fun updateData(list: List<PetCluster>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "updateData,  list is empty." }
            return
        }

        val operations = mutableListOf<UpdateReq>()
        list.forEach { petCluster ->
            UpdateReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setConvert { petCluster.buildUpdateContentValues() }
                .setWhere(Pet._ID + EQUAL + petCluster.id)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .build()?.let { operations.add(it) }
        }
        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build()
                .exec()
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.PET, IDao.DaoType.GALLERY)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateData,  delete fail. $it" }
        }
    }

    @JvmStatic
    fun deleteData(list: List<Long>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "deleteData,  list is empty." }
            return
        }

        // 分组删除，防止sql语句过长导致删除失败。
        val deleteOperations = mutableListOf<DeleteReq>()
        val size = list.size
        var index = 0

        while (index < size) {
            val fromIndex = index
            val toIndex = (index + DELETE_GROUP_COUNT).coerceAtMost(size)
            index += DELETE_GROUP_COUNT

            StringBuilder().apply {
                append(Pet._ID + IN + LEFT_BRACKETS)
                list.subList(fromIndex, toIndex).forEach { id ->
                    append(id.toString() + COMMA)
                }
                deleteCharAt(length - 1)
                append(RIGHT_BRACKETS)

                DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.PET)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(this.toString())
                    .build().let {
                        deleteOperations.add(it)
                    }
            }
        }

        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(deleteOperations)
                .build()
                .exec()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "deleteData,  delete fail. $it" }
        }
    }

    /**
     * SQL: WHERE feature is not null
     */
    @JvmStatic
    fun getAllPetWithFeature(): List<PetCluster> {
        val list = mutableListOf<PetCluster>()
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setProjection(PET_QUERY_PROJECT)
                .setWhere(Pet.FEATURE + IS_NOT_NULL)
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val idIndex = cursor.getColumnIndex(Pet._ID)
                    val dataIndex = cursor.getColumnIndex(Pet.DATA)
                    val mediaTypeIndex = cursor.getColumnIndex(Pet.MEDIA_TYPE)
                    val thumbWIndex = cursor.getColumnIndex(Pet.THUMB_W)
                    val thumbHIndex = cursor.getColumnIndex(Pet.THUMB_H)
                    val groupIdIndex = cursor.getColumnIndex(Pet.GROUP_ID)
                    val featureIndex = cursor.getColumnIndex(Pet.FEATURE)
                    val scoreIndex = cursor.getColumnIndex(Pet.SCORE)
                    val finalScoreIndex = cursor.getColumnIndex(Pet.FINAL_SCORE)
                    val appearMulIndex = cursor.getColumnIndex(Pet.APPEAR_MUL)
                    val petCoverScoreIndex = cursor.getColumnIndex(Pet.PET_COVER_SCORE)
                    val petCoverWeightIndex = cursor.getColumnIndex(Pet.PET_COVER_WEIGHT)
                    val leftIndex = cursor.getColumnIndex(Pet.LEFT)
                    val topIndex = cursor.getColumnIndex(Pet.TOP)
                    val rightIndex = cursor.getColumnIndex(Pet.RIGHT)
                    val bottomIndex = cursor.getColumnIndex(Pet.BOTTOM)
                    val isDefaultCoverIndex = cursor.getColumnIndex(Pet.IS_DEFAULT_COVER)
                    while (cursor.moveToNext()) {
                        PetCluster().apply {
                            id = cursor.getLong(idIndex)
                            data = cursor.getString(dataIndex)
                            mediaType = cursor.getInt(mediaTypeIndex)
                            thumbWidth = cursor.getInt(thumbWIndex)
                            thumbHeight = cursor.getInt(thumbHIndex)
                            groupID = cursor.getInt(groupIdIndex)
                            feature = cursor.getBlob(featureIndex)
                            score = cursor.getFloat(scoreIndex)
                            finalScore = cursor.getFloat(finalScoreIndex)
                            appearMul = cursor.getInt(appearMulIndex)
                            petCoverScore = cursor.getFloat(petCoverScoreIndex)
                            petCoverWeight = cursor.getFloat(petCoverWeightIndex)
                            left = cursor.getInt(leftIndex)
                            top = cursor.getInt(topIndex)
                            right = cursor.getInt(rightIndex)
                            bottom = cursor.getInt(bottomIndex)
                            isDefaultCover = cursor.getInt(isDefaultCoverIndex) == 1
                            list.add(this)
                        }
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllPetWithFeature,  fail. $it" }
        }
        return list
    }

    @JvmStatic
    fun getAllManualId(): List<Long> {
        val list = mutableListOf<Long>()
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setProjection(arrayOf(Pet._ID))
                .setWhere(Pet.IS_MANUAL + EQUAL_ONE + OR + Pet.GROUP_NAME + IS_NOT_NULL + AND + Pet.GROUP_NAME + IS_NOT_EMPTY)
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val idIndex = cursor.getColumnIndex(Pet._ID)
                    while (cursor.moveToNext()) {
                        list.add(cursor.getLong(idIndex))
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllManualId,  fail. $it" }
        }
        return list
    }

    /**
     * 查询未聚类的有效数据的起始位置
     * SQL:
     * SELECT _id
     * FROM pet
     * WHERE group_id = 0 AND feature is not null
     * ORDER BY _id ASC
     * LIMIT 1
     */
    @JvmStatic
    fun queryMinIdNotGroup(): Int {
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setProjection(arrayOf(Pet._ID))
                .setWhere(Pet.GROUP_ID + EQUAL + Pet.GROUP_ID_0 + AND + Pet.FEATURE + IS_NOT_NULL)
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return 0
                    }
                    val idIndex = cursor.getColumnIndex(Pet._ID)
                    while (cursor.moveToNext()) {
                        return cursor.getInt(idIndex)
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getAllValidGroupIds,  fail. $it" }
        }
        return 0
    }

    @JvmStatic
    fun updateGroupData(list: List<PetCluster>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "updatePetCluster,  list is empty." }
            return
        }

        val currentTime = System.currentTimeMillis()
        val existGroupList = getExistGroupList()
        val operations = mutableListOf<UpdateReq>()
        list.forEach { petCluster ->
            val contentValues = ContentValues().apply {
                put(Pet.GROUP_ID, petCluster.groupID)
                put(Pet.GROUP_DATE, currentTime)
                val groupInfo = petCluster.groupID?.let { existGroupList.get(it.toLong()) }
                if (groupInfo != null) {
                    put(Pet.GROUP_NAME, groupInfo.groupName)
                    put(Pet.RELATION_TYPE, groupInfo.relationType)
                    put(Pet.CUSTOM_RELATION, groupInfo.customRelation)
                    put(Pet.IS_HIDE, groupInfo.isHide)
                    put(Pet.IS_MANUAL, groupInfo.isManual)
                    put(Pet.MANUAL_DATE, groupInfo.manualDate)
                }
            }
            if (GProperty.DEBUG) {
                GLog.d(TAG, LogFlag.DF) { "updateGroupData,  group_id:${petCluster.groupID}, data:${petCluster.data}" }
            }
            val where = (Pet._ID + EQUAL + petCluster.id)
            addUpdateOperations(operations, where, contentValues)
        }

        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.PET, IDao.DaoType.GALLERY)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateGroupData,  fail. $it" }
        }
    }

    private fun getExistGroupList(): LongSparseArray<PetCluster> {
        val result = LongSparseArray<PetCluster>()
        val groupList = getValidPetGroupList()
        groupList.forEach { group ->
            group.groupID?.let { result.put(it.toLong(), group) }
        }
        return result
    }

    @JvmStatic
    fun getValidPetGroupList(): List<PetCluster> {
        val list = mutableListOf<PetCluster>()
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setProjection(
                    arrayOf(
                        Pet.GROUP_ID,
                        Pet.GROUP_NAME,
                        Pet.RELATION_TYPE,
                        Pet.CUSTOM_RELATION,
                        Pet.IS_HIDE,
                        Pet.IS_MANUAL,
                        Pet.MANUAL_DATE
                    )
                )
                .setGroupBy(Pet.GROUP_ID)
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val groupIdIndex = cursor.getColumnIndex(Pet.GROUP_ID)
                    val groupNameIndex = cursor.getColumnIndex(Pet.GROUP_NAME)
                    val relationTypeIndex = cursor.getColumnIndex(Pet.RELATION_TYPE)
                    val customRelationIndex = cursor.getColumnIndex(Pet.CUSTOM_RELATION)
                    val isHideIndex = cursor.getColumnIndex(Pet.IS_HIDE)
                    val isManualIndex = cursor.getColumnIndex(Pet.IS_MANUAL)
                    val manualDateIndex = cursor.getColumnIndex(Pet.MANUAL_DATE)
                    while (cursor.moveToNext()) {
                        PetCluster().apply {
                            groupID = cursor.getInt(groupIdIndex)
                            groupName = cursor.getString(groupNameIndex)
                            relationType = cursor.getInt(relationTypeIndex)
                            customRelation = cursor.getString(customRelationIndex)
                            isHide = cursor.getInt(isHideIndex) == 1
                            isManual = cursor.getInt(isManualIndex) == 1
                            manualDate = cursor.getLong(manualDateIndex)
                            list.add(this)
                        }
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getValidPetGroupList,  fail. $it" }
        }
        return list
    }

    /**
     * 获取该图除 target 以外的，相同 data 的其他宠物脸的 rect
     */
    @JvmStatic
    fun getOtherPetRectFByData(target: PetCluster): List<RectF> {
        val list = mutableListOf<RectF>()
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.PET)
                .setProjection(arrayOf(Pet.LEFT, Pet.TOP, Pet.RIGHT, Pet.BOTTOM))
                .setWhere(Pet.DATA + EQUAL_TO + AND + Pet.GROUP_ID + NOT_EQUAL + target.groupID)
                .setWhereArgs(arrayOf(target.data))
                .setConvert(CursorConvert())
                .build()
                .exec()?.use { cursor ->
                    if (cursor.count == 0) {
                        return list
                    }
                    val leftIndex = cursor.getColumnIndex(Pet.LEFT)
                    val topIndex = cursor.getColumnIndex(Pet.TOP)
                    val rightIndex = cursor.getColumnIndex(Pet.RIGHT)
                    val bottomIndex = cursor.getColumnIndex(Pet.BOTTOM)
                    while (cursor.moveToNext()) {
                        list.add(
                            RectF(
                                cursor.getInt(leftIndex).toFloat(),
                                cursor.getInt(topIndex).toFloat(),
                                cursor.getInt(rightIndex).toFloat(),
                                cursor.getInt(bottomIndex).toFloat()
                            )
                        )
                    }
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "getOtherPetRectFInSameMediaItem,  fail. $it" }
        }
        return list
    }

    /**
     * 更新默认封面状态
     */
    @JvmStatic
    fun updateDefaultCover(list: List<PetCluster>) {
        if (list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "updateDefaultCover,  list is empty." }
            return
        }

        val operations = mutableListOf<UpdateReq>()
        list.forEach { petCluster ->
            val contentValues = ContentValues().apply {
                put(Pet.IS_DEFAULT_COVER, petCluster.isDefaultCover)
            }
            val where = (Pet._ID + EQUAL + petCluster.id)
            addUpdateOperations(operations, where, contentValues)
        }

        runCatching {
            BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                .addDataReqs(operations)
                .build().exec()
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.PET, IDao.DaoType.GALLERY)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "updateDefaultCover,  fail. $it" }
        }
    }
}