/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MediaSelectEngine.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/11/08
 ** Author      : wenshuguang
 ** TAG         : OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>        <date>         <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** wenshuguang     2021/11/08      1.0            OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.seniorselect.engine

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.seniorpicked.PickedDayImage
import com.oplus.gallery.business_lib.model.data.seniorpicked.SeniorMediaInfo
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.INVALID_GROUP_ID
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.INVALID_SCORE
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.INVALID_TOP_LABEL_ID
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.INVALID_VERSION
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_RESULT_NOT_SELECTED
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_RESULT_SELECTED
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_SELECT_MARK_USER_ATTENTION
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_SELECT_VERSION
import com.oplus.gallery.business_lib.seniorpicked.PickedParamManager.ADD_SCORE_LABEL_LIST
import com.oplus.gallery.business_lib.seniorpicked.PickedParamManager.ADD_SCORE_TARGET
import com.oplus.gallery.business_lib.seniorpicked.PickedParamManager.DEFAULT_MAX_QUALITY_SCORE
import com.oplus.gallery.business_lib.seniorpicked.PickedParamManager.IQA_QUALITY_ADD_SCORE_THRESHOLD
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaDBOperation
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.math.MathUtil.clamp
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.scan.IqaQualityScanner
import com.oplus.gallery.framework.abilities.scan.scan.SimilarFeatureScanner
import com.oplus.gallery.framework.abilities.scan.scan.SsaQualityScanner
import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectDBOperation

/***
 * @property hasInitialized Boolean
 * @property similarFeatureFilter SimilarFeatureScanner
 * @property iqaQualityFilter IqaQualityScanner
 * @property ssaQualityFilter SsaQualityScanner
 * @constructor
 */
class MediaSelectEngine(
    private val context: Context,
) {
    @VisibleForTesting
    var hasInitialized = false
    private val seniorPickedEngine = SeniorPickedEngine.getInstance(context)

    private val similarFeatureFilter = SimilarFeatureScanner.getInstance(context)
    private val iqaQualityFilter = IqaQualityScanner.getInstance(context)
    private val ssaQualityFilter = SsaQualityScanner.getInstance(context)
    var stopReason: String? = null

    fun initialize(): Boolean {
        stopReason = null
        if (hasInitialized) {
            return true
        }
        if (!similarFeatureFilter.initialize()) {
            stopReason = SIMILAR_FEATURE_INIT_FAILED
            return false
        }
        if (!iqaQualityFilter.initialize()) {
            similarFeatureFilter.release()
            stopReason = IQA_QUALITY_INIT_FAILED
            return false
        }
        if (!ssaQualityFilter.initialize()) {
            similarFeatureFilter.release()
            iqaQualityFilter.release()
            stopReason = SSA_QUALITY_INIT_FAILED
            return false
        }
        hasInitialized = true
        return true
    }

    fun release() {
        if (!hasInitialized) {
            return
        }
        similarFeatureFilter.release()
        iqaQualityFilter.release()
        ssaQualityFilter.release()
        hasInitialized = false
    }

    /**
     * 扫描出所有输入图片资源的优选信息，该信息将被更新到senior_media表
     *
     * @param mediaItems 待扫描数据集合
     * @return 扫描出的优选数据集合
     */
    fun scanImageSeniorInfo(mediaItems: List<MediaItem>): List<SeniorMediaInfo> {
        if (!hasInitialized) {
            GLog.e(TAG, "scanImageSeniorInfo. Not initialized yet. skip.")
            return emptyList()
        }

        // 设置内容优选的黑名单标签，最先执行，防止后面有算法有label相关的操作
        seniorPickedEngine.setImageLabelConfig()
        // 设置历史平均质量美学分，在质量分获取之前执行(qualityFilter.rating中有把质量分insert db)
        seniorPickedEngine.setTopLabelQualityAvg()

        // 查询出所有待扫描图片的标签信息
        val labelInfoList = GalleryScanProviderHelper.queryLabelImageList(SeniorSelectDBOperation.getImagesClassifySql(mediaItems))
        if (labelInfoList.isNullOrEmpty()) {
            GLog.e(TAG, "filter. No label info. Must scan label before SeniorScan. Skip.")
            return emptyList()
        }

        // 通过IQA模型计算出质量美学分
        val iqaScoreMap = iqaQualityFilter.rating(mediaItems)
        val iqaScoredItemList = mediaItems.filter { iqaScoreMap.containsKey(it.filePath) }

        // 过滤出所有需要加分的item项：特殊标签加分 + 用户操作加分（收藏/编辑/分享）
        val labelAddScoreSet = labelInfoList.filter { ADD_SCORE_LABEL_LIST.contains(it.mSceneId) }.map { it.mFilePath }.toSet()
        val userAttentionAddScoreSet = SeniorSelectDBOperation.queryMarkUserAttention()
        val addScoreSet = labelAddScoreSet + userAttentionAddScoreSet

        // 进行相似图分组，并分配分组ID
        val groupList = similarFeatureFilter.group(iqaScoredItemList) ?: return emptyList()
        val singleGroupList = groupList.filterSingleGroup()
        val groupIdMap = allocateGroupId(groupList, iqaScoredItemList)

        // 通过SSA模型计算出相似图美学分
        val ssaScoreMap = ssaQualityFilter.rating(iqaScoredItemList.filterNot {
            singleGroupList.contains(it.filePath) // 单独成组的项不用计算ssa相似图美学分，后面出现相似图再算就行
        })
        val ssaScoreGroupList = getSsaQualityScoreGroupList(groupList, ssaScoreMap.addScore(addScoreSet))

        // 组装contentFilter的参数imgInfoMap
        val imageInfoMap = seniorPickedEngine.getImageInfo(
            iqaScoredItemList,
            iqaScoreMap.addScore(addScoreSet, IQA_QUALITY_ADD_SCORE_THRESHOLD),
            labelInfoList
        )
        // 调用sdk获取图片是否是优选图
        val seniorResultMap = seniorPickedEngine.contentFilter(imageInfoMap, groupList, ssaScoreGroupList) ?: let {
            GLog.e(TAG) { "filter. Can't get senior result. Skip." }
            return emptyList()
        }

        // 仅返回能获取质量分的结果，如果对异常无法获取质量分的资源执行优选扫描，会将错误结果插入到了db，需要过滤掉
        return iqaScoredItemList.map {
            SeniorMediaInfo(
                filePath = it.filePath,
                version = if (addScoreSet.contains(it.filePath)) SENIOR_SELECT_MARK_USER_ATTENTION else SENIOR_SELECT_VERSION,
                seniorScore = iqaScoreMap[it.filePath] ?: INVALID_SCORE,
                ssaQualityScore = ssaScoreMap[it.filePath] ?: INVALID_SCORE,
                ssaQualityScoreVersion = if (ssaScoreMap.containsKey(it.filePath)) ssaQualityFilter.version else INVALID_VERSION,
                topLabelId = imageInfoMap[it.filePath]?.topLabel ?: INVALID_TOP_LABEL_ID,
                similarGroupId = groupIdMap[it.filePath] ?: INVALID_GROUP_ID,
                seniorResult = if (seniorResultMap.containsKey(it.filePath)) SENIOR_RESULT_SELECTED else SENIOR_RESULT_NOT_SELECTED,
                seniorResultVersion = SeniorSelectDBOperation.imageSeniorResultVersion
            )
        }
    }

    /**
     * 扫描出所有输入视频资源的优选信息，该信息将被更新到senior_media表
     *
     * @param mediaItems 待扫描数据集合
     * @return 扫描出的优选数据集合
     */
    fun scanVideoSeniorInfo(mediaItems: List<MediaItem>): List<SeniorMediaInfo> {
        if (!hasInitialized) {
            GLog.e(TAG, "scanVideoSeniorInfo. Not initialized yet. skip.")
            return emptyList()
        }

        // 通过IQA模型计算出质量美学分
        val iqaScoreMap = iqaQualityFilter.rating(mediaItems)
        val iqaScoredItemList = mediaItems.filter { iqaScoreMap.containsKey(it.filePath) }

        // 进行分组，每个视频单独成组，并分配分组ID
        val groupList = iqaScoredItemList.map { listOf(it.filePath) }
        val groupIdMap = allocateGroupId(groupList, iqaScoredItemList)

        val seniorScoreMap = iqaScoreMap.map { (path, score) ->
            path to calculateVideoWeightedSeniorScore(score)
        }.toMap()

        // 仅返回能获取质量分的结果，如果对异常无法获取质量分的资源执行优选扫描，会将错误结果插入到了db，需要过滤掉
        return iqaScoredItemList.map {
            SeniorMediaInfo(
                filePath = it.filePath,
                version = SENIOR_SELECT_VERSION,
                seniorScore = seniorScoreMap[it.filePath] ?: INVALID_SCORE,
                similarGroupId = groupIdMap[it.filePath] ?: INVALID_GROUP_ID,
                seniorResult = SENIOR_RESULT_SELECTED,
                seniorResultVersion = SeniorSelectDBOperation.videoSeniorResultVersion
            )
        }
    }

    /**
     * 为相似图分组分配groupID, 分组中图片中已有ID的使用旧ID，无ID的使用新ID
     */
    private fun allocateGroupId(
        groupList: List<List<String>>,
        mediaItemList: List<MediaItem>
    ): Map<String, Int> {
        val existingIdMap = mediaItemList.mapNotNull {
            if ((it is PickedDayImage) && (it.similarGroupId != INVALID_GROUP_ID)) {
                it.filePath to it
            } else null
        }.toMap()

        var nextSimilarGroupId = SeniorMediaDBOperation.queryMaxSimilarGroupId() + 1
        val groupIdMap = mutableMapOf<String, Int>()
        groupList.forEach { group ->
            var groupId = group.firstNotNullOfOrNull { existingIdMap[it]?.similarGroupId } ?: INVALID_GROUP_ID
            if (groupId < 0) {
                groupId = nextSimilarGroupId++
            }
            groupIdMap.putAll(group.map { it to groupId })
        }
        return groupIdMap
    }

    /**
     * 根据加分表，对对应项进行加分
     *
     * @param addScoreSet 加分表
     * @param threshold 加分阈值，低于该阈值强制不加分
     * @return 返回加分后结果
     */
    private fun Map<String, Float>.addScore(
        addScoreSet: Set<String>,
        threshold: Float = Float.MAX_VALUE
    ): Map<String, Float> {
        val newMap = mutableMapOf<String, Float>()
        forEach { (path, score) ->
            val newScore = if ((score >= threshold) && addScoreSet.contains(path)) {
                (score + ADD_SCORE_TARGET).coerceAtMost(DEFAULT_MAX_QUALITY_SCORE)
            } else score
            newMap[path] = newScore
        }
        return newMap
    }

    /**
     * 组合传给contentFilter的SSA质量分
     * @param groupList 分组数据
     * @param ssaQualityScoreMap 相似图美学分数
     * @return 以分组的形式返回相似图美学分数，没有ssa评分的用0f填充，避免单独成组的无法成为优选
     */
    private fun getSsaQualityScoreGroupList(
        groupList: List<List<String>>,
        ssaQualityScoreMap: Map<String, Float>
    ): List<List<Float>> {
        return groupList.map { group ->
            group.map { path ->
                ssaQualityScoreMap[path] ?: 0f
            }
        }
    }

    /**
     * 过滤出单独成组的项
     */
    private fun List<List<String>>.filterSingleGroup(): List<String> {
        val singleGroups = mutableListOf<String>()
        forEach { group ->
            if (group.size == 1) {
                singleGroups.addAll(group)
            }
        }
        return singleGroups
    }

    companion object {
        private const val TAG = "MediaSelectEngine"
        private const val SIMILAR_FEATURE_INIT_FAILED = "_SimilarFeatureInitFailed"
        private const val IQA_QUALITY_INIT_FAILED = "_IqaQualityInitFailed"
        private const val SSA_QUALITY_INIT_FAILED = "_SaaQualityInitFailed"

        /**
         * 视频优选基础分
         */
        private const val VIDEO_BASIC_SCORE = 2.9F

        /**
         * 视频封面质量美学分限定范围
         */
        private const val VIDEO_IQA_SCORE_MIN = 2.5f
        private const val VIDEO_IQA_SCORE_MAX = 3.5f

        /**
         * 视频封面质量美学分最大加权分
         */
        private const val VIDEO_IQA_WEIGHT_SCORE_MAX = 0.6f

        /**
         * 视频精选分 = 基础分 + 质量美学分加权值
         */
        private fun calculateVideoWeightedSeniorScore(score: Float): Float {
            return VIDEO_BASIC_SCORE + (clamp(score, VIDEO_IQA_SCORE_MIN, VIDEO_IQA_SCORE_MAX) - VIDEO_IQA_SCORE_MIN) * VIDEO_IQA_WEIGHT_SCORE_MAX
        }
    }
}