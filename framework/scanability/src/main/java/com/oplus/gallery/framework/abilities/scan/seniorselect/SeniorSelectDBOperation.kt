/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SeniorSelectDBOperation
 ** Description: 精选扫描相关数据库操作类
 **
 ** Version: 1.0
 ** Date: 2021/11/22
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2021/11/22  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect

import android.content.Context
import android.database.Cursor
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.common.dbaccess.convert.PickedDayMediaItemListConvert
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.business_lib.model.data.label.bean.ImageInfo
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService.ORDER_CLAUSE
import com.oplus.gallery.business_lib.model.data.seniorpicked.PickedDayImage
import com.oplus.gallery.business_lib.model.data.seniorpicked.PickedDayVideo
import com.oplus.gallery.business_lib.model.data.seniorpicked.SeniorMediaInfo
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.SENIOR_RESULT_SELECTED
import com.oplus.gallery.business_lib.seniorpicked.PickedDayDBFilterCondition.getBasicFilterCondition
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaCondition
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaCondition.getConditionForDateInRange
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaCondition.getSeniorMediaInnerJoinLocalMedia
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaDBOperation
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabel
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.SeniorMedia
import com.oplus.gallery.foundation.database.store.GalleryStore.SeniorMediaColumns
import com.oplus.gallery.foundation.database.util.ConstantUtils
import com.oplus.gallery.foundation.database.util.ConstantUtils.NUM_ZERO
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.DESC
import com.oplus.gallery.foundation.database.util.SQLGrammar.DISTINCT
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN
import com.oplus.gallery.foundation.database.util.SQLGrammar.GROUP_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.IN
import com.oplus.gallery.foundation.database.util.SQLGrammar.IS_NOT_NULL
import com.oplus.gallery.foundation.database.util.SQLGrammar.NOT_IN
import com.oplus.gallery.foundation.database.util.SQLGrammar.OR
import com.oplus.gallery.foundation.database.util.SQLGrammar.ORDER_BY
import com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.DatabaseUtils.getDataInvalidWhere
import com.oplus.gallery.foundation.database.util.DatabaseUtils.projectionsToString
import com.oplus.gallery.foundation.database.util.SQLGrammar
import com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_DISTINCT
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.ext.appendBracket
import com.oplus.gallery.foundation.util.ext.bracket
import com.oplus.gallery.foundation.util.ext.quote
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.scan.label.ImageLabelListConfig
import com.oplus.gallery.framework.abilities.scan.label.VideoLabelListConfig
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectTimeNode.Companion.PROJECTION_NOT_SCANNED
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.IqaQualityModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SeniorSelectModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SimilarFeatureModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SsaQualityModelConfig
import com.oplus.gallery.standard_lib.scheduler.BatchProcess
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.Arrays

object SeniorSelectDBOperation {
    private const val TAG = "SeniorSelectDBOperation"
    private val context: Context
        get() = ContextGetter.context

    /**
     * 图片优选结果版本号
     * 优选结果版本号规则修改，会造成优选版本号变化，会全部重新扫描，慎重修改
     * 结果类似 {"iqaVersion":3,"ssaVersion":1,"similarFeatureVersion":1,"labelVersion":8,"imageFilterVersion":1}
     * 埋点也有使用，所以不要依赖 initialize
     */
    var imageSeniorResultVersion: String = TextUtil.EMPTY_STRING
        private set
        get() {
            val sp = SPUtils.getSp(context, ComponentPrefUtils.COMPONENT_PREF_NAME)
            val labelCurrentVersion = sp.getInt(ComponentPrefUtils.LABEL_COMPONENT_VERSION_KEY, -1)
            val imageLabelConfigVersion = ImageLabelListConfig.getVersion()

            val seniorVersionInfo = SeniorVersionInfo(
                iqaVersion = SeniorSelectModelConfig.getBuildInVersion(
                    context,
                    IqaQualityModelConfig.IQA_QUALITY_COMPONENT_DEFAULT_VERSION_NAME
                ),
                ssaVersion = SeniorSelectModelConfig.getBuildInVersion(
                    context,
                    SsaQualityModelConfig.SSA_QUALITY_COMPONENT_DEFAULT_VERSION_NAME
                ),
                similarFeatureVersion = SeniorSelectModelConfig.getBuildInVersion(
                    context,
                    SimilarFeatureModelConfig.SIMILAR_FEATURE_COMPONENT_DEFAULT_VERSION_NAME
                ),
                labelVersion = labelCurrentVersion,
                imageFilterVersion = imageLabelConfigVersion
            )
            return seniorVersionInfo.toString()
        }

    /**
     * 视频优选结果版本号
     * 优选结果版本号规则修改，会造成优选版本号变化，会全部重新扫描，慎重修改
     * 视频优选版本号由：IQA质量美学分版本号+标签版本号 组成，任一版本变化都会触发视频重写扫描
     */
    var videoSeniorResultVersion: String = TextUtil.EMPTY_STRING
        private set
        get() {
            val context = ContextGetter.context
            val sp = SPUtils.getSp(context, ComponentPrefUtils.COMPONENT_PREF_NAME)
            val labelCurrentVersion = sp.getInt(ComponentPrefUtils.LABEL_COMPONENT_VERSION_KEY, -1)
            val seniorVersionInfo = SeniorVersionInfo(
                iqaVersion = SeniorSelectModelConfig.getBuildInVersion(
                    context,
                    IqaQualityModelConfig.IQA_QUALITY_COMPONENT_DEFAULT_VERSION_NAME
                ),
                ssaVersion = PickedDayConstant.INVALID_VERSION,
                similarFeatureVersion = PickedDayConstant.INVALID_VERSION,
                labelVersion = labelCurrentVersion,
                imageFilterVersion = VideoLabelListConfig.getVersion()
            )
            return seniorVersionInfo.toString()
        }

    /**
     * SELECT day,count(DISTINCT similar_group_id)
     * FROM senior_media inner JOIN  local_media ON senior_media._data = local_media._data
     * WHERE buildSeniorVersionCountOfPerNodeScanned(seniorResultVersion)
     * AND getPreFilterCondition()
     * AND ( day >=  ' startDate') and ( day <=  ' endDate')
     * GROUP BY DAY
     * ORDER BY DAY DESC
     */
    fun queryCountOfPerNodeWhichScanned(
        startDate: String,
        endDate: String
    ): MutableList<SeniorSelectTimeNode> {
        val stringBuilder = StringBuilder().apply {
            append(SELECT)
            append(projectionsToString(SeniorSelectTimeNode.PROJECTION_SCANNED))
            append(getSeniorMediaInnerJoinLocalMedia())
            append(WHERE)
            append(assembleConditionSeniorScanned())
            append(AND)
            append(getBasicFilterCondition())
            append(AND)
            append(getConditionForDateInRange(startDate.toInt(), endDate.toInt()))
            append(GROUP_BY + GalleryStore.GalleryColumns.LocalColumns.DAY)
            append(ORDER_BY + GalleryStore.GalleryColumns.LocalColumns.DAY + DESC)
        }
        return queryCountOfPerNode(stringBuilder.toString())
    }

    /**
     * 获取以优选标签大类ID 分组的平均质量分
     * SELECT distict(senior_top_label_id), avg(local_media.quality_score)
     * FROM senior_media inner JOIN  local_media ON senior_media._data = local_media._data
     * WHERE local_media.quality_score > 0
     * AND senior_top_label_id > 0
     * AND media_type = 1
     * GROUP BY senior_top_label_id
     */
    fun queryQualityScoreBySeniorTopLabelId(): MutableList<IqaQualityNode> {
        val stringBuilder = StringBuilder().apply {
            append(SELECT)
            append(DISTINCT)
            append(SeniorMediaColumns.SENIOR_TOP_LABEL_ID)
            append(COMMA)
            append(SQLGrammar.avg(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.QUALITY_SCORE))
            append(getSeniorMediaInnerJoinLocalMedia())
            append(WHERE)
            append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.QUALITY_SCORE)
            append(GREATER_THAN)
            append(NUM_ZERO)
            append(AND)
            append(SeniorMediaColumns.SENIOR_TOP_LABEL_ID)
            append(GREATER_THAN)
            append(NUM_ZERO)
            append(AND)
            append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE)
            append(EQUAL)
            append(MEDIA_TYPE_IMAGE)
            append(GROUP_BY)
            append(SeniorMediaColumns.SENIOR_TOP_LABEL_ID)
        }
        return queryCountOfQualityNode(stringBuilder.toString())
    }

    /**
     * 获取db中优选图数量
     * SELECT count(DISTINCT local_media.data)
     * FROM senior_media inner JOIN  local_media ON senior_media._data = local_media._data
     * WHERE getPreFilterCondition()
     * AND media_type = 1
     * AND senior_result = 1
     */
    fun querySeniorResultCount(): Int {
        val stringBuilder = StringBuilder().apply {
            append(SELECT)
            append(COUNT_DISTINCT)
            append(GalleryStore.GalleryMedia.TAB + DOT + SeniorMediaColumns.DATA)
            append(RIGHT_BRACKETS)
            append(getSeniorMediaInnerJoinLocalMedia())
            append(WHERE)
            append(getBasicFilterCondition())
            append(AND)
            append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE)
            append(EQUAL)
            append(MEDIA_TYPE_IMAGE)
            append(AND)
            append(SeniorMedia.SENIOR_RESULT)
            append(EQUAL)
            append(SENIOR_RESULT_SELECTED)
        }
        runCatching {
            RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(stringBuilder.toString())
                .setSqlArgs(null)
                .build().exec()?.use { cursor ->
                    if ((cursor == null) || (cursor.count <= 0)) {
                        return 0
                    }
                    while (cursor.moveToNext()) {
                        return cursor.getInt(0)
                    }
                }
        }.onFailure {
            GLog.w(TAG, "querySeniorResultCount, Exception=", it)
        }
        return 0
    }

    private fun queryCountOfQualityNode(sql: String): MutableList<IqaQualityNode> {
        val list = mutableListOf<IqaQualityNode>()
        runCatching {
            RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(sql)
                .setSqlArgs(null)
                .build().exec()?.use { cursor ->
                    list.addAll(IqaQualityNode.convert(cursor))
                }
        }.onFailure {
            GLog.w(TAG, "queryCountOfQualityNode, Exception=", it)
        }
        return list
    }

    /**
     * SELECT day,count(*)
     * FROM local_media
     * WHERE buildSeniorVersionNotScanned()
     * AND getPreFilterCondition()
     * GROUP BY DAY
     * ORDER BY DAY DESC
     */
    fun queryCountOfPerNodeWhichNotScanned(): MutableList<SeniorSelectTimeNode> {
        val stringBuilder = StringBuilder().apply {
            append(SELECT)
            append(projectionsToString(PROJECTION_NOT_SCANNED))
            append(FROM)
            append(GalleryStore.GalleryMedia.TAB)
            append(WHERE + assembleConditionNotSeniorScanned())
            append(AND)
            append(getBasicFilterCondition())
            append(GROUP_BY + GalleryStore.GalleryColumns.LocalColumns.DAY)
            append(ORDER_BY + GalleryStore.GalleryColumns.LocalColumns.DAY + DESC)
        }
        return queryCountOfPerNode(stringBuilder.toString())
    }

    private fun queryCountOfPerNode(sql: String): MutableList<SeniorSelectTimeNode> {
        val list = mutableListOf<SeniorSelectTimeNode>()
        runCatching {
            RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(sql)
                .setSqlArgs(null)
                .build().exec()?.use { cursor ->
                    list.addAll(SeniorSelectTimeNode.convert(cursor))
                }
        }.onFailure {
            GLog.w(TAG, "queryCountOfPerNode, Exception=", it)
        }
        return list
    }

    /**
     *全量查找未扫描的数据
     *
     * SELECT media_id,_data,date_modified,datetaken,invalid,media_type
     * FROM local_media
     * WHERE buildSeniorVersionNotScanned()
     * AND getPreFilterCondition()
     * ORDER BY datetaken DESC
     */
    fun queryImageInfoWhichNotScanned(): MutableList<BaseImageInfo> {
        val stringBuilder = StringBuilder().apply {
            append(SELECT)
            append(projectionsToString(ImageInfo.getMediaProviderProject()))
            append(FROM + GalleryStore.GalleryMedia.TAB)
            append(WHERE + assembleConditionNotSeniorScanned())
            append(AND)
            append(getBasicFilterCondition())
            append(ORDER_BY + GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN + DESC)
        }
        return SeniorMediaDBOperation.queryImageInfo(stringBuilder.toString())
    }

    /**
     * 按传入的最大最小日期查找已扫描的数据
     *
     * SELECT media_id,_data,date_modified,datetaken,invalid,media_type
     * FROM senior_media inner JOIN  local_media ON senior_media._data = local_media._data
     * WHERE buildSeniorVersionScanned(seniorResultVersion)
     * AND getPreFilterCondition()
     * ORDER BY datetaken DESC
     */
    fun queryImageInfoWhichScanned(startDate: String, endDate: String): MutableList<BaseImageInfo> {
        val stringBuilder = StringBuilder().apply {
            append(SELECT)
            append(projectionsToString(ImageInfo.getMediaProviderProject(),
                GalleryStore.GalleryMedia.TAB
            ))
            append(getSeniorMediaInnerJoinLocalMedia())
            append(WHERE + assembleConditionSeniorScanned())
            append(AND)
            append(getBasicFilterCondition())
            append(AND)
            append(getConditionForDateInRange(startDate.toInt(), endDate.toInt()))
            append(ORDER_BY + GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN + DESC)
        }
        return SeniorMediaDBOperation.queryImageInfo(stringBuilder.toString())
    }

    fun deleteAbandonSeniorMedia() {
        GalleryScanProviderHelper.deleteWhenNotInLocalMedia(SeniorMedia.DATA, GalleryDbDao.TableType.SENIOR_MEDIA, true)
        deleteInvalidSeniorMediaItem(true)
        // 两个删除合并通知
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SENIOR_MEDIA, IDao.DaoType.GALLERY)
    }

    /**
     *  _data IN ( SELECT senior_media._data FROM senior_media INNER JOIN local_media ON senior_media._data = local_media._data
     *  WHERE (invalid NOT IN (INVALID_NORMAL, INVALID_NORMAL_NOT_IN_MEDIA_STORE)) )
     */
    private fun deleteInvalidSeniorMediaItem(noNotify: Boolean) {
        val sql = StringBuilder()
        val seniorMediaFilePath = SeniorMedia.TAB + DOT + SeniorMedia.DATA
        sql.append(SeniorMedia.DATA + IN + ConstantUtils.LEFT_BRACKETS)
        sql.append(SELECT + seniorMediaFilePath)
        sql.append(getSeniorMediaInnerJoinLocalMedia())
        sql.append(WHERE + getDataInvalidWhere())
        sql.append(ConstantUtils.RIGHT_BRACKETS)
        DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SENIOR_MEDIA)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, if (noNotify) DatabaseUtils.VALUE_TRUE else DatabaseUtils.VALUE_FALSE)
            .setWhere(sql.toString())
            .build()
            .exec()
    }

    /**
     * 删除senior_media中部分数据
     */
    fun deleteSeniorMediaItem(dataList: List<String>) {
        if (dataList.isNotEmpty()) {
            val where = DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns.DATA, dataList.size)
            val whereArgs = DatabaseUtils.getWhereArgs(dataList)
            runCatching {
                DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SENIOR_MEDIA)
                    .setWhere(where)
                    .setWhereArgs(whereArgs)
                    .build()
                    .exec()
            }.onFailure {
                GLog.e(TAG, "deleteSeniorMediaItem.", it)
            }
        }
    }

    fun insertSeniorMediaItem(insertList: List<SeniorMediaInfo>) {
        val time = System.currentTimeMillis()
        val operations = insertList.map { seniorMediaInfo ->
            InsertReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SENIOR_MEDIA)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert { SeniorMediaInfo.buildInsertContentValues(seniorMediaInfo) }
                .build()
        }
        if (operations.isNotEmpty()) {
            runCatching {
                BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build()
                    .exec()
                UriNotifier.notifyTableChange(GalleryDbDao.TableType.SENIOR_MEDIA, IDao.DaoType.GALLERY)
            }.onFailure {
                GLog.e(TAG, "insertSeniorMediaItem.", it)
            }
        }
        GLog.d(TAG) {
            "insertSeniorMediaItem. operationSize=${operations.size}, insertSize=${insertList.size}, costTime=${GLog.getTime(time)} ms"
        }
    }

    /**
     * 根据传入的imageInfo查找对应的MediaItem
     */
    fun queryPickedItemByImageInfo(imageInfoList: List<BaseImageInfo>): List<MediaItem> {
        if (imageInfoList.isEmpty()) return emptyList()

        val resultItemList = mutableListOf<MediaItem>()
        val whereClause = StringBuilder().apply {
            append(WHERE)
            append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID)
            append(IN)
            append(ConstantUtils.LEFT_BRACKETS)

            imageInfoList.forEach { imageInfo ->
                imageInfo.toPickedItem()?.let {
                    resultItemList.add(it)
                } ?: let {
                    append(imageInfo.mMediaId)
                    append(COMMA)
                }
            }

            if (resultItemList.size == imageInfoList.size) {
                return resultItemList
            } else {
                deleteCharAt(length - 1)
                append(ConstantUtils.RIGHT_BRACKETS)
            }
        }

        val sql = StringBuilder()
            .append(SELECT)
            .append(projectionsToString(LocalMediaItem.PROJECTION,
                GalleryStore.GalleryMedia.TAB
            ))
            .append(COMMA)
            .append(projectionsToString(PickedDayImage.EXTEND_PROJECT))
            .append(getSeniorMediaInnerJoinLocalMedia())
            .append(whereClause)
            .append(ORDER_BY)
            .append(ORDER_CLAUSE)
        runCatching {
            resultItemList.addAll(
                RawQueryReq.Builder<MutableList<MediaItem>>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setConvert(PickedDayMediaItemListConvert())
                    .setQuerySql(sql.toString())
                    .setSqlArgs(null)
                    .build().exec()
            )
        }.onFailure {
            GLog.w(TAG, "convertImageToSeniorMediaItem, Exception=", it)
        }
        return resultItemList.filter { it is PickedDayImage }
    }

    private fun BaseImageInfo.toPickedItem(): MediaItem? =
        when (mMediaType) {
            MEDIA_TYPE_IMAGE -> {
                val path = PickedDayImage.ITEM_PATH.getChild(mMediaId)
                DataManager.peekMediaObject(path) as? PickedDayImage
            }
            MEDIA_TYPE_VIDEO -> {
                val path = PickedDayVideo.ITEM_PATH.getChild(mMediaId)
                DataManager.peekMediaObject(path) as? PickedDayVideo
            }
            else -> null
        }

    /**
     * 获取传入图片中是优选的图片
     * SELECT _data
     * FROM senior_media
     * WHERE _data in (dataList) AND SeniorMediaCondition.buildSeniorResultSelected()
     */
    fun querySeniorResult(dataList: List<String>): List<String> {
        val result = mutableListOf<String>()
        object : BatchProcess<String>(
            dataList, DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER
        ) {
            override fun processOneBatch(items: List<String>): List<String> {
                var where = DatabaseUtils.getWhereQueryIn(SeniorMedia.DATA, items.size)
                where += AND + SeniorMediaCondition.buildSeniorResultSelected()
                QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SENIOR_MEDIA)
                    .setProjection(
                        arrayOf(
                            SeniorMedia.DATA
                        )
                    )
                    .setWhere(where)
                    .setWhereArgs(DatabaseUtils.getWhereArgs(items))
                    .setConvert(CursorConvert())
                    .build()
                    .exec()?.use { cursor ->
                        if (cursor.count > 0) {
                            val dataIndex = cursor.getColumnIndex(SeniorMedia.DATA)
                            while (cursor.moveToNext()) {
                                val data = cursor.getString(dataIndex)
                                result.add(data)
                            }
                        }
                    }
                return emptyList()
            }
        }.process()
        GLog.d(TAG, "querySeniorResult. inputSize=${dataList.size}, pickedSize=${result.size}")
        return result
    }

    /**
     * 查询优选结果数据
     * SELECT *
     * FROM senior_media
     * WHERE _data in (dataList)
     * return SeniorMediaInfo?
     */
    fun querySeniorResultAsSeniorMediaInfo(mediaItem: MediaItem): Any? {
        QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SENIOR_MEDIA)
            .setProjection(
                arrayOf(
                    ConstantUtils.ASTERISK
                )
            )
            .setWhere(DatabaseUtils.getWhereQueryIn(SeniorMedia.DATA, 1))
            .setWhereArgs(DatabaseUtils.getWhereArgs(listOf(mediaItem.filePath)))
            .setConvert(CursorConvert())
            .build()
            .exec()?.use { cursor ->
                if (cursor.count > 0) {
                    val list = SeniorMediaInfo.buildInfoListForClassify(cursor)
                    if (list.isNotEmpty()) {
                        return list[0]
                    }
                }
            }
        return null
    }

    /**
     * SELECT scan_label._data,scan_label.media_type,scan_label.invalid,scan_label.is_recycled,scan_label.scene_id,scan_label.score,scan_label.is_sync,scan_label.is_manual
     * FROM scan_label INNER JOIN local_media ON local_media._data = scan_label._data
     * WHERE local_media.media_type = 1
     * AND local_media.media_id IN (mediaItems)
     */
    fun getImagesClassifySql(mediaItems: List<MediaItem>): String {
        return StringBuilder().apply {
            append(SELECT)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.DATA)
            append(COMMA)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.MEDIA_TYPE)
            append(COMMA)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.INVALID)
            append(COMMA)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.IS_RECYCLED)
            append(COMMA)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.SCENE_ID)
            append(COMMA)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.SCORE)
            append(COMMA)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.IS_SYNC)
            append(COMMA)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.IS_MANUAL)
            append(FROM)
            append(ScanLabel.TAB + ConstantUtils.INNER_JOIN + GalleryStore.GalleryMedia.TAB + ConstantUtils.ON)
            append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.DATA)
            append(EQUAL)
            append(ScanLabel.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.DATA)
            append(WHERE)
            append(GalleryStore.GalleryMedia.TAB + DOT + ScanLabelColumns.MEDIA_TYPE)
            append(EQUAL)
            append(MEDIA_TYPE_IMAGE)
            append(AND)
            append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID)
            append(IN)
            append(ConstantUtils.LEFT_BRACKETS)
            for (mediaItem in mediaItems) {
                append(mediaItem.mediaId)
                append(COMMA)
            }
            if (mediaItems.isNotEmpty()) {
                deleteCharAt(length - 1)
            }
            append(ConstantUtils.RIGHT_BRACKETS)
        }.toString()
    }

    /**
     * 获取在scan_label表的视频
     * SELECT scan_label._data
     * FROM scan_label INNER JOIN local_media ON local_media._data = scan_label._data
     * WHERE local_media.media_type = 3
     * AND local_media.media_id IN (mediaItems)
     */
    fun getVideoInScanLabel(mediaItems: List<MediaItem>): List<MediaItem> {
        val sql = StringBuilder().apply {
            append(SELECT)
            append(ScanLabel.TAB + DOT + ScanLabelColumns.DATA)
            append(FROM)
            append(ScanLabel.TAB + ConstantUtils.INNER_JOIN + GalleryStore.GalleryMedia.TAB + ConstantUtils.ON)
            append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.DATA)
            append(EQUAL)
            append(ScanLabel.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.DATA)
            append(WHERE)
            append(GalleryStore.GalleryMedia.TAB + DOT + ScanLabelColumns.MEDIA_TYPE)
            append(EQUAL)
            append(MEDIA_TYPE_VIDEO)
            append(AND)
            append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID)
            append(IN)
            append(ConstantUtils.LEFT_BRACKETS)
            for (mediaItem in mediaItems) {
                append(mediaItem.mediaId)
                append(COMMA)
            }
            if (mediaItems.isNotEmpty()) {
                deleteCharAt(length - 1)
            }
            append(ConstantUtils.RIGHT_BRACKETS)
        }.toString()
        val datas = mutableListOf<String>()
        runCatching {
            RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(sql)
                .setSqlArgs(null)
                .build().exec()?.use { cursor ->
                    if (cursor.count <= 0) {
                        return mutableListOf()
                    }
                    while (cursor.moveToNext()) {
                        val data = cursor.getString(0)
                        if (!datas.contains(data)) {
                            datas.add(data)
                        }
                    }
                }
        }.onFailure {
            GLog.w(TAG, "getVideoInScanLabel, Exception", it)
        }
        return mediaItems.filter { datas.contains(it.filePath) }
    }

    fun getSsaQualityScoreMap(dataList: List<String>, version: Int): MutableMap<String, Float>? {
        val result: MutableMap<String, Float> = HashMap()
        object : BatchProcess<String>(
            dataList, DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER
        ) {
            override fun processOneBatch(items: List<String>): List<String> {
                var where = SeniorMedia.SSA_QUALITY_SCORE + IS_NOT_NULL +
                    AND + DatabaseUtils.getWhereQueryIn(SeniorMedia.DATA, dataList.size)
                var whereArgs = DatabaseUtils.getWhereArgs(dataList)
                if (version >= 0) {
                    where += AND + SeniorMedia.SSA_QUALITY_SCORE_VERSION + ConstantUtils.GREATER_THAN_OR_EQUAL_TO +
                        ConstantUtils.VARIABLE_PLACEHOLDER
                    whereArgs = Arrays.copyOf(whereArgs, whereArgs.size + 1)
                    whereArgs[whereArgs.size - 1] = version.toString()
                }

                QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SENIOR_MEDIA)
                    .setProjection(
                        arrayOf(
                            SeniorMedia.DATA,
                            SeniorMedia.SSA_QUALITY_SCORE
                        )
                    )
                    .setWhere(where)
                    .setWhereArgs(whereArgs)
                    .setConvert(CursorConvert())
                    .build()
                    .exec()?.use { cursor ->
                        if (cursor.count > 0) {
                            val dataIndex = cursor.getColumnIndex(SeniorMediaColumns.DATA)
                            val scoreIndex = cursor.getColumnIndex(
                                SeniorMediaColumns.SSA_QUALITY_SCORE
                            )
                            while (cursor.moveToNext()) {
                                val data = cursor.getString(dataIndex)
                                val score = cursor.getFloat(scoreIndex)
                                if (!result.containsKey(data)) {
                                    result[data] = score
                                }
                            }
                        }
                    }
                return emptyList()
            }
        }.process()
        return result
    }

    /**
     * 查询db中senior_media表中version为SENIOR_SELECT_MARK_USER_ATTENTION的值(db中version = 2为图片需要在insert，update的时候保证)
     * select distinct data
     * from senior_media
     * where version = 2
     */
    fun queryMarkUserAttention(): Set<String> {
        val stringBuilder = StringBuilder().apply {
            append(SELECT)
            append(DISTINCT)
            append(SeniorMediaColumns.DATA)
            append(FROM)
            append(SeniorMedia.TAB)
            append(WHERE)
            append(SeniorMediaColumns.VERSION)
            append(EQUAL)
            append(PickedDayConstant.SENIOR_SELECT_MARK_USER_ATTENTION)
        }
        return queryCountOfMarkUserAttentionItem(stringBuilder.toString()).toSet()
    }

    private fun queryCountOfMarkUserAttentionItem(sql: String): List<String> {
        val list = mutableListOf<String>()
        runCatching {
            RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(CursorConvert())
                .setQuerySql(sql)
                .setSqlArgs(null)
                .build().exec()?.use { cursor ->
                    val list = mutableListOf<String>()
                    if (cursor.count <= 0) {
                        return mutableListOf()
                    }
                    while (cursor.moveToNext()) {
                        val data = cursor.getString(0)
                        list.add(data)
                    }
                    return list
                }
        }.onFailure {
            GLog.w(TAG, "queryCountOfAddScoreItem, Exception", it)
        }
        return list
    }

    /**
     * 组装SQL条件语句：过滤出所有未扫描的图片和视频
     */
    fun assembleConditionNotSeniorScanned(): String =
        bracket {
            append(assembleConditionNotSeniorScanned(MEDIA_TYPE_VIDEO, videoSeniorResultVersion))
            append(OR)
            append(assembleConditionNotSeniorScanned(MEDIA_TYPE_IMAGE, imageSeniorResultVersion))
        }

    /**
     * 组装SQL条件语句：过滤出所有未扫描的资源
     * (
     *      media_type = $type
     *      AND (
     *          -- 有精选结果且精选结果版本为最新版本的为已扫描项，不在已扫描项中的项即为未扫描项
     *          local_media._data NOT IN (
     *              SELECT _data FROM senior_media WHERE (
     *                  senior_result IS NOT NULL AND senior_result_version = '$seniorResultVersion'
     *              )
     *          )
     *      )
     * )
     */
    private fun assembleConditionNotSeniorScanned(type: Int, seniorResultVersion: String): String =
        bracket {
            append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE + EQUAL + type)
            append(AND)
            appendBracket {
                append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.DATA)
                append(NOT_IN)
                appendBracket {
                    append(SELECT + SeniorMedia.DATA + FROM + SeniorMedia.TAB)
                    append(WHERE)
                    appendBracket {
                        append(SeniorMediaColumns.SENIOR_RESULT + IS_NOT_NULL)
                        append(AND)
                        append(SeniorMediaColumns.SENIOR_RESULT_VERSION + EQUAL + quote(seniorResultVersion))
                    }
                }
            }
        }

    /**
     * 组装SQL条件语句：过滤出所有已扫描的图片和视频，有精选结果且精选结果版本为最新版本的为已扫描项
     * (
     *      (
     *          media_type = 3
     *          AND
     *          senior_result IS NOT NULL
     *          AND
     *          senior_result_version = '$videoSeniorResultVersion'
     *      )
     *      OR
     *      (
     *          media_type = 1
     *          AND
     *          senior_result IS NOT NULL
     *          AND
     *          senior_result_version = '$imageSeniorResultVersion'
     *      )
     * )
     */
    private fun assembleConditionSeniorScanned(): String =
        bracket {
            appendBracket {
                append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE + EQUAL + MEDIA_TYPE_VIDEO)
                append(AND)
                append(SeniorMediaColumns.SENIOR_RESULT + IS_NOT_NULL)
                append(AND)
                append(SeniorMediaColumns.SENIOR_RESULT_VERSION + EQUAL + quote(videoSeniorResultVersion))
            }
            append(OR)
            appendBracket {
                append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE + EQUAL + MEDIA_TYPE_IMAGE)
                append(AND)
                append(SeniorMediaColumns.SENIOR_RESULT + IS_NOT_NULL)
                append(AND)
                append(SeniorMediaColumns.SENIOR_RESULT_VERSION + EQUAL + quote(imageSeniorResultVersion))
            }
        }
}