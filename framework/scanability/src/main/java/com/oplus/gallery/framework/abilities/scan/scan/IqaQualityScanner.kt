/*********************************************************************************
 ** Copyright (C), 2019-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IqaQualityScanner.kt
 ** Description : 质量美学分数扫描类
 ** Version     : 1.0
 ** Date        : 2023/3/13
 ** Author      : zhu<PERSON><PERSON>@Apps.Gallery
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON>@Apps.Gallery             2023/3/13      1.0       build
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.scan

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.template.IqaQualityModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SeniorSelectModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.IqaQualityModelConfig
import com.oplus.gallery.framework.abilities.scan.utils.SingletonHolder

/**
 * 质量美学评价 扫描（对应原来的质量扫描）
 */
class IqaQualityScanner private constructor(context: Context) :
    BaseQualityScanner(context) {
    override val tag: String = "IqaQualityScanner"

    override fun insertScore(newFeatureMap: Map<String, Float>) {
        GalleryScanProviderHelper.insertScore(newFeatureMap, version)
    }

    override fun getQualityScoreMap(dataList: List<String>): Map<String, Float> {
        return GalleryScanProviderHelper.getIqaQualityScoreMap(dataList, (version)) ?: emptyMap()
    }

    override val modelConfig: SeniorSelectModelConfig = IqaQualityModelConfig(context)

    override val modelLoadingTemplate: ModelLoadingTemplate = IqaQualityModelLoadingTemplate(context, modelConfig)

    companion object {
        fun getInstance(context: Context): IqaQualityScanner {
            return SingletonHolder(::IqaQualityScanner).getInstance(context)
        }
    }
}