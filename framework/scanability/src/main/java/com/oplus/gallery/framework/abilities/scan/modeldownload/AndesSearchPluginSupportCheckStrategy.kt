/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AndesSearchPluginSupportCheckStrategy.kt
 ** Description: 融合搜索插件检查策略
 * 融合搜素需要 Clip 和 TextEmbedding 插件
 ** Version: 1.0
 ** Date : 2025/2/25
 ** Author: ********
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** ********      2025/2/25       1.0          created
 ***************************************************************/
package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import android.os.Bundle
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_MULTIMODAL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OCREMBEDDING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.CLIP_AND_TEXT_EMBEDDING_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.UPDATE_CLIP_AND_TEXT_EMBEDDING_PLUGIN_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.search.multimodal.IMultiModalAbility
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 融合搜索插件检查策略
 * 融合搜素需要 Clip 和 TextEmbedding 插件
 * 目前策略：
 * 1. AndesSearchInterceptor 支持融合搜索 + rus支持多模态 + rus支持语义化。
 * 2. AIUnitPreCheckInterceptor aiunit 相关预检查。
 * 3. AIUnitModelDownloadInterceptor 下载。静默下载。
 */
internal class AndesSearchPluginSupportCheckStrategy : ISupportCheckStrategy {
    companion object {
        private const val TAG = "AndesSearchPluginSupportCheckStrategy"
    }
    private var chain: RealInterceptorChain? = null

    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        kotlin.runCatching {
            val bundle = Bundle()
            chain = RealInterceptorChain(
                getInterceptors(context, postNotificationAction),
                onSuccessCallback = { setAISearchAbilityDownloading(false) },
                onFailCallback = { setAISearchAbilityDownloading(false) }
            )
            chain?.proceed(bundle)
        }.onFailure {
            setAISearchAbilityDownloading(false)
        }
    }

    private fun getInterceptors(context: Context, postNotificationAction: (NotificationAction) -> Unit) = listOf(
        AndesSearchInterceptor(),
        AIUnitPreCheckInterceptor(),
        AIUnitModelDownloadInterceptor(
            context,
            AIUnitPluginState(
                AIUnitPlugin.ANDES_SEARCH.downloadPlugin,
                CLIP_AND_TEXT_EMBEDDING_PLUGIN_DOWNLOAD_STATE,
                UPDATE_CLIP_AND_TEXT_EMBEDDING_PLUGIN_SHOW_TIMESTAMP
            ),
            null,
            enableUI = { false },
            updateUI = postNotificationAction,
            startDownload = {
                setAISearchAbilityDownloading(true)
            }
        )
    )

    private fun setAISearchAbilityDownloading(value: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "setAISearchAbilityDownloading, value: $value" }
        ContextGetter.context.getAppAbility<IMultiModalAbility>()?.use { it.isDownloading = value }
        ContextGetter.context.getAppAbility<IOcrEmbeddingAbility>()?.use { it.isDownloading = value }
    }

    override fun destroy() {
        chain?.destroy()
        chain = null
    }
}

internal class AndesSearchInterceptor : ConditionInterceptor() {
    override fun onCheckCondition(param: Bundle): Boolean {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_MULTIMODAL, defValue = true, expDefValue = false)
                && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_OCREMBEDDING, defValue = true, expDefValue = false)
    }
}

internal class AIUnitPreCheckInterceptor : ConditionInterceptor() {
    override fun onCheckCondition(param: Bundle): Boolean {
        // 判断是否为全球化版本，全球化版本才支持下载
        val isGlobalDep =  AISettings.isGlobalDepSupported(ContextGetter.context)
        // AIUnit 用户协议
        val privacyAvailable = ConfigAbilityWrapper.getBoolean(IS_AGREE_AI_UNIT_PRIVACY, false)
        return isGlobalDep && privacyAvailable
    }
}