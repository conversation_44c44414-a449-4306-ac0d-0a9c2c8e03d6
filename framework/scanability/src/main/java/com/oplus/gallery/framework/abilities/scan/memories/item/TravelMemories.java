/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TravelMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/13
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D     2018/3/13     1.0       build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import static com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.GPS_KEY;

import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.config.allowlist.GeoConfig;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.location.utils.GeoDBHelper;
import com.oplus.gallery.business_lib.model.data.location.utils.LocationHelper;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.userprofile.UserProfileSettings;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.framework.abilities.scan.R;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TravelMemories extends Memories {
    private static final String TAG = "TravelMemories";
    private static final String STOP_REASON_IS_ON_TOP = TAG + "_IsOnTop";
    private static final String STOP_REASON_CREATE_FAILED = TAG + "_CreateFailed";
    private static final String STOP_REASON_PERMANENT_EMPTY = TAG + "_PermanentEmpty";
    private static final String STOP_REASON_ROUTER_NOT_FOUND = TAG + "_RouterNotFound";
    private static final String STOP_REASON_SCAN_SIZE_NOT_ENOUGH = TAG + "_ScanSizeNotEnough";
    private static final String STOP_REASON_TRAVEL_LIST_EMPTY = TAG + "_TravelListEmpty";
    private static final String STOP_REASON_WIFI_DISCONNECTED = TAG + "_WifiDisconnected";
    private static final String STOP_REASON_NOT_READY_CONTINUE = TAG + "_NotReadyContinue";
    private static final String STOP_REASON_CAMERA_TRAVELS_EMPTY = TAG + "_CameraTravelsEmpty";
    private static final String STOP_REASON_UPDATE_TRAVEL_FAILED = TAG + "_UpdateTravelFailed";
    private static final String STOP_REASON_PROCESS_TRAVEL_FAILED = TAG + "_ProcessTravelFailed";
    private static final String STOP_REASON_PERMISSION_UNAVAILABLE = TAG + "_PermissionUnavailable";
    private static final String STOP_REASON_UPDATE_PERMANENT_FAILED = TAG + "_UpdatePermanentFailed";

    private static final boolean DEBUG = false;
    private UserProfileSettings.Permanent mPermanent;
    private List<UserProfileSettings.Permanent> mPermanentList;
    private int mNameType = -1;

    public TravelMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        if (PermissionHelper.isPermissionUnavailable()) {
            GLog.d(TAG, "prepareMemories, permission unavailable.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_PERMISSION_UNAVAILABLE;
            return false;
        }
        if (UserProfileSettings.needUpdatePermanentInfo(mContext, false)) {
            if (!NetworkMonitor.isWifiValidated()) {
                GLog.d(TAG, "prepareMemories, wifi is not connected.");
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_WIFI_DISCONNECTED;
                return false;
            }
            if (!UserProfileSettings.updatePermanent(mContext)) {
                GLog.d(TAG, "prepareMemories, updatePermanent failed.");
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_UPDATE_PERMANENT_FAILED;
                return false;
            }
        }
        mPermanent = UserProfileSettings.getPermanent(mContext);
        if ((mPermanent == null) || mPermanent.isEmpty()) {
            GLog.e(TAG, "prepareMemories, There is no permanent place.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_PERMANENT_EMPTY;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        Map<Long, String[]> searchRoute = getSearchRoute(mContext);
        if ((searchRoute == null) || searchRoute.isEmpty()) {
            GLog.d(TAG, "scanMemories, not found search route.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_ROUTER_NOT_FOUND;
            return false;
        }

        List<Travel> cameraTravels = getCameraTravels(mContext, searchRoute);
        if ((cameraTravels == null) || cameraTravels.isEmpty()) {
            GLog.d(TAG, "scanMemories, not found camera travel.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CAMERA_TRAVELS_EMPTY;
            return false;
        }
        if (MemoriesScannerHelper.needRefreshExistTravel(mContext)) {
            if (MemoriesScannerHelper.refreshExistTravel(mContext)) {
                UserProfileSettings.setLastTravelLocale(mContext);
                UserProfileSettings.setLastAreaListConfigVersion(mContext, GeoConfig.INSTANCE.getCurrentVersion());
            } else {
                GLog.d(TAG, "scanMemories, update travel failed.");
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_UPDATE_TRAVEL_FAILED;
                return false;
            }
        }
        mPermanentList = UserProfileSettings.getPermanentHistoryList(mContext);
        return processTravel(cameraTravels, getExistTravels(mContext));
    }

    @Override
    public boolean createMemories() {
        return true;
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.TRAVEL_MEMORIES;
    }

    @Override
    public int getNameType() {
        if (mNameType < 0) {
            mNameType = MemoriesNameRules.getTravelRandomNameType();
        }
        return mNameType;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    private Map<String, List<Travel>> getExistTravels(Context context) {
        String where = GalleryStore.MemoriesSet.TYPE + "=" + MemoriesType.TRAVEL_MEMORIES;
        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                .setWhere(where)
                .setConvert(new CursorConvert())
                .build();
        Cursor cursor = DataAccess.getAccess().query(req);
        try {
            if (cursor != null) {
                Map<String, List<Travel>> travels = new HashMap<>();
                int indexCountry = cursor.getColumnIndex(GalleryStore.MemoriesSet.COUNTRY);
                int indexCity = cursor.getColumnIndex(GalleryStore.MemoriesSet.CITY);
                int indexIsForeign = cursor.getColumnIndex(GalleryStore.MemoriesSet.IS_FOREIGN);
                int indexStartTime = cursor.getColumnIndex(GalleryStore.MemoriesSet.START_TIME);
                int indexEndTime = cursor.getColumnIndex(GalleryStore.MemoriesSet.END_TIME);
                int indexLatitude = cursor.getColumnIndex(GalleryStore.MemoriesSet.LATITUDE);
                int indexLongitude = cursor.getColumnIndex(GalleryStore.MemoriesSet.LONGITUDE);
                while (cursor.moveToNext()) {
                    boolean isForeign = cursor.getInt(indexIsForeign) == 1;
                    String country = cursor.getString(indexCountry);
                    String city = cursor.getString(indexCity);
                    if (isForeign || TextUtils.isEmpty(city)) {
                        continue;
                    }
                    long startTime = cursor.getLong(indexStartTime);
                    long endTime = cursor.getLong(indexEndTime);
                    TravelItem ti = new TravelItem(isForeign, country, city, startTime, endTime, indexLatitude, indexLongitude);
                    travels.computeIfAbsent(city, k -> new ArrayList<>()).add(new Travel(ti, null));
                }
                GLog.d(TAG, "getExistTravels, exist travels size=" + travels.size());
                return travels;
            }
        } catch (Exception e) {
            GLog.d(TAG, "getExistTravels, e=" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return null;
    }

    private boolean processTravel(List<Travel> mediaTravels, Map<String, List<Travel>> existTravelItem) {
        if ((mediaTravels == null) || mediaTravels.isEmpty()) {
            GLog.d(TAG, "processTravel, list is empty.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CAMERA_TRAVELS_EMPTY;
            return false;
        }
        long time = System.currentTimeMillis();
        boolean haveExistTravel = (existTravelItem != null) && !existTravelItem.isEmpty();
        for (Travel travel : mediaTravels) {
            if (!isReadyContinue()) {
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_NOT_READY_CONTINUE;
                return false;
            }
            boolean haveTravel = false;
            List<Travel> travels = null;
            if (haveExistTravel) {
                if (!TextUtils.isEmpty(travel.mItem.mCity)) {
                    travels = existTravelItem.get(travel.mItem.mCity);
                }
            }
            if ((travels != null) && !travels.isEmpty()) {
                for (Travel existTravel : travels) {
                    if (existTravel.mItem.partOfTravel(travel.mItem)) {
                        haveTravel = true;
                        break;
                    }
                }
            }
            if (!haveTravel) {
                if (createMemories(travel)) {
                    GLog.d(TAG, "processTravel, this one is successful and returns.");
                    return true;
                } else if (GalleryScanMonitor.isGalleryRunOnTop()) {
                    GLog.d(TAG, "processTravel, gallery is on top, we need give up!");
                    // 扫描中断，记录原因
                    mStopReason = STOP_REASON_IS_ON_TOP;
                    return false;
                }
            }
        }
        GLog.d(TAG, "processTravel, end. cost time=" + (System.currentTimeMillis() - time));
        // 扫描中断，记录原因
        mStopReason = STOP_REASON_PROCESS_TRAVEL_FAILED;
        return false;
    }

    private boolean createMemories(Travel travel) {
        List<MediaItem> list = travel.mList;
        if ((list == null) || list.isEmpty()) {
            GLog.v(TAG, "createMemories, travel list is empty.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_TRAVEL_LIST_EMPTY;
            return false;
        }
        //filter specified labels
        travel.mList = MemoriesProviderHelper.getFilteredItemList(list, getLabelFilterIdList());
        if (travel.mList.size() <= mMemoriesPicMin) {
            GLog.d(TAG, "createMemories, Image is too little after filtered.");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            return false;
        }

        TravelItem item = travel.mItem;
        if (GProperty.DEBUG) {
            GLog.v(TAG, "createMemories, image count=" + list.size());
        }
        item.setForeign(!item.mCountry.equals(mPermanent.mCountry));
        int setId = createTravelMemories(mContext, MemoriesType.TRAVEL_MEMORIES, item);
        if (setId == -1) {
            GLog.w(TAG, "createMemories, create memories failed!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_CREATE_FAILED;
            return false;
        }
        return processMemoriesItems(setId, travel.mList, travel.mItem.mStartTime, travel.mItem.mEndTime);
    }

    private int createTravelMemories(Context context, int type, TravelItem item) {
        int setId = -1;
        try {
            String name = item.getName();
            if (!TextUtils.isEmpty(name)) {
                Uri newUri = new InsertReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setConvert(aVoid -> {
                        int nameType = getNameType();
                        if (nameType == MemoriesNameRules.NAME_TRAVEL_MEMORIES_TRIP) {
                            setMemoriesName(context.getResources().getString(R.string.scan_memory_title_travel_1, name));
                        } else if (nameType == MemoriesNameRules.NAME_TRAVEL_MEMORIES_TOURING) {
                            setMemoriesName(context.getResources().getString(R.string.scan_memory_title_travel_2, name));
                        }
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(GalleryStore.MemoriesSet.NAME, getMemoriesName());
                        contentValues.put(GalleryStore.MemoriesSet.TYPE, type);
                        contentValues.put(GalleryStore.MemoriesSet.COUNTRY, item.getCountry());
                        contentValues.put(GalleryStore.MemoriesSet.CITY, item.getCity());
                        contentValues.put(GalleryStore.MemoriesSet.IS_FOREIGN, item.isForeign() ? 1 : 0);
                        contentValues.put(GalleryStore.MemoriesSet.START_TIME, item.getStartTime());
                        contentValues.put(GalleryStore.MemoriesSet.END_TIME, item.getEndTime());
                        contentValues.put(GalleryStore.MemoriesSet.TAKEN, System.currentTimeMillis());
                        contentValues.put(GalleryStore.MemoriesSet.LATITUDE, item.getLatitude());
                        contentValues.put(GalleryStore.MemoriesSet.LONGITUDE, item.getLongitude());
                        contentValues.put(GalleryStore.MemoriesSet.NAME_TYPE, nameType);
                        return contentValues;
                    }).build().exec();
                setId = (int) ContentUris.parseId(newUri);
            }
        } catch (Exception e) {
            GLog.w(TAG, "createTravelMemories", e);
        }
        return setId;
    }

    private Map<Long, String[]> getSearchRoute(Context context) {
        GeoDBHelper geoDBHelper = null;
        Cursor cursor = null;
        try {
            geoDBHelper = new GeoDBHelper(context);
            cursor = geoDBHelper.queryLocalMediaTable(null, null, null, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                Map<Long, String[]> locations = getAllAddress(context);
                if ((locations == null) || locations.isEmpty()) {
                    GLog.w(TAG, "getSearchRoute, not found location.");
                    return null;
                }
                Map<Long, String[]> map = new HashMap<>();
                int indexGspKey = cursor.getColumnIndex(GPS_KEY);
                while (cursor.moveToNext()) {
                    String[] route = locations.get(cursor.getLong(indexGspKey));
                    map.put(cursor.getLong(indexGspKey), route);
                    if (DEBUG) {
                        GLog.v(TAG, "getSearchRoute, route=" + Arrays.toString(route));
                    }
                }
                GLog.d(TAG, "getSearchRoute, size=" + map.size());
                return map;
            } else {
                GLog.w(TAG, "getSearchRoute, not found geo id.");
            }
        } catch (Exception e) {
            GLog.w(TAG, "getSearchRoute, e=" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return null;
    }

    private List<Travel> getCameraTravels(Context context, Map<Long, String[]> searchLocation) {
        String where = MemoriesProviderHelper.getOptimalScanWhereClause(context);
        String order = MediaStore.Images.Media.DATE_TAKEN + " DESC";
        Cursor cursor = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(LocalMediaItem.getProjection())
                .setWhere(where)
                .setOrderBy(order)
                .setConvert(new CursorConvert())
                .build().exec();
        if (cursor == null) {
            GLog.w(TAG, "getCameraTravels, cursor is null.");
            return null;
        }
        try {
            LocalImage image = null;
            LocalImage latestPermanentImage = null;
            List<Travel> travelList = new ArrayList<>();
            List<MediaItem> list = null;
            String curCountry = null;
            String curCity = null;
            boolean isForeign = false;
            long curKey = -1;
            double curLatitude = 0;
            double curLongitude = 0;
            long endTime = -1;
            long statTime = -1;
            UserProfileSettings.Permanent curPermanent = null;
            while (cursor.moveToNext()) {
                int id = cursor.getInt(LocalMediaItem.INDEX_ID);
                Path childPath = LocalImage.ITEM_PATH.getChild(id);
                image = (LocalImage) LocalMediaItem.loadOrUpdateItem(childPath, cursor);
                if (DEBUG) {
                    GLog.v(TAG, "getCameraTravels, dateTaken=" + UserProfileSettings.getDate(image.getDateTakenInMs())
                            + ", file path=" + image.getFilePath());
                }
                double[] latlng = new double[2];
                image.getLatLong(latlng);
                long key = getKey(latlng[0], latlng[1]);
                String[] route = searchLocation.get(key);
                if ((route == null) || (route.length < (GeoDBHelper.LEVEL_CITY + 1))) {
                    continue;
                }

                String country = route[GeoDBHelper.LEVEL_COUNTRY];
                if (TextUtils.isEmpty(country)) {
                    GLog.d(TAG, "getCameraTravels, empty country.");
                    continue;
                }
                String city = route[GeoDBHelper.LEVEL_CITY];
                if (TextUtils.isEmpty(city)) {
                    GLog.d(TAG, "getCameraTravels, empty city.");
                    continue;
                }

                if ((latestPermanentImage == null)
                        && (image.getDateTakenInMs() > (System.currentTimeMillis() - TimeUtils.TIME_2_WEEK_IN_MS))
                        && isPermanent(route, mPermanent)) {
                    latestPermanentImage = image;
                }

                if (curPermanent == null) {
                    curPermanent = getPermanentByDatetaken(image.getDateTakenInMs());
                    GLog.d(TAG, "getCameraTravels, curPermanent = " + ((curPermanent == null) ? null : curPermanent.toString()));
                }

                if ((curPermanent != null)) {
                    boolean isPermanent = isPermanent(route, curPermanent);
                    boolean inPermanent = UserProfileSettings.isTimeInPermanent(image.getDateTakenInMs(), curPermanent);
                    if (isPermanent || !inPermanent) {
                        if ((list != null) && !list.isEmpty()) {
                            if (list.size() > mMemoriesPicMin) {
                                if (!travelList.isEmpty() || isTravelOver(list, latestPermanentImage)) {
                                    TravelItem ti = new TravelItem(isForeign, curCountry, curCity,
                                            statTime, endTime, curLatitude, curLongitude);
                                    travelList.add(new Travel(ti, list));
                                }
                            }
                            curKey = -1;
                            curPermanent = null;
                            list = null;
                        }
                        if (!inPermanent) {
                            curKey = -1;
                            curPermanent = null;
                            list = null;
                        }
                        if (isPermanent) {
                            continue;
                        }
                    }
                }

                boolean newLocation = false;
                if (curKey == -1) {
                    newLocation = true;
                } else if ((curKey == key) || (!TextUtils.isEmpty(curCity) && curCity.equals(city))) {
                    list.add(image);
                    statTime = image.getDateTakenInMs();
                } else {
                    newLocation = true;
                }

                if (newLocation) {
                    if (list != null) {
                        if (list.size() > mMemoriesPicMin) {
                            if (!travelList.isEmpty() || isTravelOver(list, latestPermanentImage)) {
                                TravelItem ti = new TravelItem(isForeign, curCountry, curCity,
                                        statTime, endTime, curLatitude, curLongitude);
                                travelList.add(new Travel(ti, list));
                            }
                        }
                    }

                    // reset
                    curCountry = route[GeoDBHelper.LEVEL_COUNTRY];
                    curCity = route[GeoDBHelper.LEVEL_CITY];
                    curKey = key;
                    curPermanent = null;
                    curLatitude = latlng[0];
                    curLongitude = latlng[1];
                    endTime = image.getDateTakenInMs();
                    statTime = image.getDateTakenInMs();
                    list = new ArrayList<>();
                    list.add(image);
                }
            }
            if (list != null) {
                if (list.size() > mMemoriesPicMin) {
                    if (!travelList.isEmpty() || isTravelOver(list, latestPermanentImage)) {
                        TravelItem ti = new TravelItem(isForeign, curCountry, curCity,
                                statTime, endTime, curLatitude, curLongitude);
                        travelList.add(new Travel(ti, list));
                    }
                }
            }
            GLog.d(TAG, "getCameraTravels, travels size=" + travelList.size());
            return travelList;
        } catch (Exception e) {
            GLog.d(TAG, "getCameraTravels, e=" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return null;
    }

    /**
     * If user have taken pictures of other locations after the current travel
     * or have not taken a photo for two weeks, we think the travel is over.
     *
     * @param list        MediaItems of current travel
     * @param latestImage Latest Image of current permanent or other locations
     * @return true
     */
    private boolean isTravelOver(List<MediaItem> list, LocalImage latestImage) {
        MediaItem mediaItem = list.get(0);
        if (mediaItem instanceof LocalMediaItem) {
            LocalMediaItem localMediaItem = (LocalMediaItem) mediaItem;
            long dateTakenMax = localMediaItem.getDateTakenInMs();
            if (((latestImage != null) && (dateTakenMax < latestImage.getDateTakenInMs()))
                    || dateTakenMax < (System.currentTimeMillis() - TimeUtils.TIME_2_WEEK_IN_MS)) {
                return true;
            } else {
                GLog.d(TAG, "addToTravelList, The travel is not over.");
                return false;
            }
        }
        return false;
    }

    private UserProfileSettings.Permanent getPermanentByDatetaken(long dateTakenInMs) {
        GLog.d(TAG, "getPermanentByDatetaken, datetaken = " + UserProfileSettings.getDate(dateTakenInMs));
        if (mPermanentList == null) {
            mPermanentList = UserProfileSettings.getPermanentHistoryList(mContext);
        }
        if ((mPermanentList == null) || mPermanentList.isEmpty()) {
            return UserProfileSettings.getPermanent(mContext);
        }
        for (UserProfileSettings.Permanent p : mPermanentList) {
            if (UserProfileSettings.isTimeInPermanent(dateTakenInMs, p)) {
                return p;
            }
        }
        return UserProfileSettings.getPermanent(mContext);
    }

    private boolean isPermanent(String[] route, UserProfileSettings.Permanent curPermanent) {
        if ((curPermanent == null) || TextUtils.isEmpty(curPermanent.mCountry)) {
            return false;
        }
        if (curPermanent.mCountry.equals(route[GeoDBHelper.LEVEL_COUNTRY])) {
            String routeCity = route[GeoDBHelper.LEVEL_CITY];
            if (TextUtils.isEmpty(curPermanent.mCity)) {
                return true;
            }
            boolean sameCity = curPermanent.mCity.equals(routeCity)
                    || curPermanent.mCity.equals(getRealCityName(routeCity));
            if (sameCity) {
                String routeProvince = route[GeoDBHelper.LEVEL_PROVINCE];
                if (TextUtils.isEmpty(curPermanent.mProvince) || TextUtils.isEmpty(routeProvince)) {
                    return true;
                }
                return curPermanent.mProvince.equals(routeProvince)
                        || curPermanent.mProvince.equals(getRealProvinceName(routeProvince));
            }
        }
        return false;
    }

    private long getKey(double latitude, double longitude) {
        double cutOff = LocationHelper.PRECISION_CUT_OFF;
        return LocationHelper.makeKey(((int) (latitude * cutOff)) / cutOff,
                ((int) (longitude * cutOff)) / cutOff);
    }

    private Map<Long, String[]> getAllAddress(Context context) {
        GeoDBHelper geoDBHelper = new GeoDBHelper(context);
        return geoDBHelper.getAllAddress();
    }

    static class Travel {
        TravelItem mItem;
        List<MediaItem> mList;

        Travel(TravelItem item, List<MediaItem> list) {
            mItem = item;
            mList = list;
            if (DEBUG && GProperty.DEBUG) {
                GLog.v(TAG, "Travel, list size = " + ((mList == null) ? null : mList.size()));
            }
        }
    }

    public static class TravelItem {
        private long mStartTime;
        private long mEndTime;
        private boolean mIsForeign;
        private String mCity;
        private String mCountry;
        private double mLatitude = 0;
        private double mLongitude = 0;

        TravelItem(boolean isForeign, String country, String city,
                   long startTime, long endTime, double latitude, double longitude) {
            mIsForeign = isForeign;
            mCity = city;
            mCountry = country;
            mStartTime = startTime;
            mEndTime = endTime;
            mLatitude = latitude;
            mLongitude = longitude;
        }

        boolean partOfTravel(TravelItem item) {
            boolean sameCity = !TextUtils.isEmpty(mCity) && mCity.equals(item.mCity);
            boolean sameCountry = !TextUtils.isEmpty(mCountry) && mCountry.equals(item.mCountry);
            return (sameCity || (sameCountry && TextUtils.isEmpty(mCity) && TextUtils.isEmpty(item.mCity)))
                    && (mStartTime >= item.mStartTime)
                    && (mEndTime <= item.mEndTime);
        }

        public String getName() {
            if (!TextUtils.isEmpty(mCity)) {
                return getRealCityName(mCity);
            } else {
                return null;
            }
        }

        public long getStartTime() {
            return mStartTime;
        }

        public void setStartTime(long startTime) {
            mStartTime = startTime;
        }

        public long getEndTime() {
            return mEndTime;
        }

        public void setEndTime(long endTime) {
            mEndTime = endTime;
        }

        public boolean isForeign() {
            return mIsForeign;
        }

        public void setForeign(boolean foreign) {
            mIsForeign = foreign;
        }

        public String getCity() {
            return mCity;
        }

        public void setCity(String city) {
            mCity = city;
        }

        public String getCountry() {
            return mCountry;
        }

        public void setCountry(String country) {
            mCountry = country;
        }

        public double getLatitude() {
            return mLatitude;
        }

        public void setLatitude(double latitude) {
            mLatitude = latitude;
        }

        public double getLongitude() {
            return mLongitude;
        }

        public void setLongitude(double longitude) {
            mLongitude = longitude;
        }
    }

    private static String getRealCityName(String city) {
        if (TextUtils.isEmpty(city)) {
            return city;
        }
        try {
            if (city.endsWith(ContextGetter.context.getResources().getString(R.string.scan_memory_city))) {
                return city.substring(0, city.length() - 1);
            }
        } catch (Exception e) {
            GLog.d(TAG, "getRealCityName, e=" + e);
        }
        return city;
    }

    private static String getRealProvinceName(String province) {
        if (TextUtils.isEmpty(province)) {
            return province;
        }
        try {
            if (province.endsWith(ContextGetter.context.getResources().getString(R.string.scan_memory_province))) {
                return province.substring(0, province.length() - 1);
            }
        } catch (Exception e) {
            GLog.d(TAG, "getRealProvinceName, e=" + e);
        }
        return province;
    }
}
