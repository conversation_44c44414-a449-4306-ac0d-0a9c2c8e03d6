/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  OneYearMemories.java
 * * Description: OneYearMemories
 * * Version: 1.0
 * * Date : 2018/3/13
 * * Author: hailong.zhang@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *  hailong.zhang@Apps.Gallery3D    2018/3/13    1.0    build this module
 ****************************************************************/
/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - .java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:
 ** Author:
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **          1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;


import android.content.Context;
import android.database.Cursor;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import static com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper.ONE_YEAR_MEMORIES_TRIGGER_NAME;

public class OneYearMemories extends Memories {
    private static final String TAG = "OneYearMemories";
    private static final String STOP_REASON_NOT_JANUARY = TAG + "_NotJanuary";
    private static final String STOP_REASON_HAS_CREATED = TAG + "_HasCreated";
    private static final String STOP_REASON_FESTIVAL_NULL = TAG + "_FestivalNull";
    private static final String STOP_REASON_HIGH_PRIORITY = TAG + "_HighPriority";
    private static final String STOP_REASON_CREATED_FAILED = TAG + "_CreatedFailed";
    private static final String STOP_REASON_DATE_RANGE_NOT_EXISTED = TAG + "_DateRangeNotExisted";
    private static final String STOP_REASON_FESTIVAL_NOT_CONTAIN_DATE = TAG + "_FestivalNotContainDate";

    public OneYearMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        if (isHighPriorityMemories()) {
            if (FeatureUtils.isRegionCN()) {
                FestivalSelector selector = new FestivalSelector();
                selector.addCountryFestivals();
                FestivalSelector.FestivalInfo festivalInfo = selector.getFestivalByName(ONE_YEAR_MEMORIES_TRIGGER_NAME);
                if (festivalInfo == null) {
                    // 扫描中断，记录原因
                    mStopReason = STOP_REASON_FESTIVAL_NULL;
                    return false;
                }

                String curSolarDateStr = TimeUtils.getYearMonthDayDate(new Date(), TimeUtils.FORMAT_YYYYMMDD, Locale.ENGLISH);
                boolean result = festivalInfo.containsDate(curSolarDateStr);
                GLog.d(TAG, "prepareMemories result:" + result + ", curSolarDateStr=" + curSolarDateStr);
                if (!result) {
                    // 扫描中断，记录原因
                    mStopReason = STOP_REASON_FESTIVAL_NOT_CONTAIN_DATE;
                }
                return result;
            } else {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                int month = calendar.get(Calendar.MONTH);
                boolean result = (month == Calendar.JANUARY);
                GLog.d(TAG, "prepareMemories result:" + result);
                if (!result) {
                    // 扫描中断，记录原因
                    mStopReason = STOP_REASON_NOT_JANUARY;
                }
                return result;
            }
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        return true;
    }

    @Override
    public boolean createMemories() {
        long oldestFileTime = MemoriesProviderHelper.getOldestFileTime(mContext);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(oldestFileTime));
        int oldestFileInYear = calendar.get(Calendar.YEAR);
        calendar.setTime(new Date());
        int currentYear = calendar.get(Calendar.YEAR);
        if (oldestFileInYear < currentYear) {
            for (int i = currentYear - 1; i >= oldestFileInYear; i--) {
                MemoriesScannerHelper.DateRange itemDateRange = null;
                if (FeatureUtils.isRegionCN()) {
                    itemDateRange = MemoriesScannerHelper.getLunarYearRange(i);
                } else {
                    itemDateRange = MemoriesScannerHelper.getYearRange(i);
                }
                if ((itemDateRange == null) || (itemDateRange.mEnd > System.currentTimeMillis())) {
                    GLog.d(TAG, "createMemories, itemDateRange not existed");
                    if (isHighPriorityMemories()) {
                        // 扫描中断，记录原因
                        mStopReason = STOP_REASON_DATE_RANGE_NOT_EXISTED;
                        return false;
                    }
                    continue;
                }
                if (hasDateOneYearOptimalCreated(mContext, itemDateRange)) {
                    GLog.d(TAG, String.format("createMemories, OneYearMemories of %d year has created already!", i));
                    if (isHighPriorityMemories()) {
                        // 扫描中断，记录原因
                        mStopReason = STOP_REASON_HAS_CREATED;
                        return false;
                    }
                    continue;
                }

                List<MediaItem> itemList = MemoriesProviderHelper.getItemListFromLocalMedia(mContext, itemDateRange, mMemoriesPicMin);
                GLog.v(TAG, "createMemories itemList size:" + itemList.size());
                //filter specified labels
                List<MediaItem> filteredItemList = MemoriesProviderHelper.getFilteredItemList(itemList, getLabelFilterIdList());
                GLog.v(TAG, "createMemories filteredItemList size:" + filteredItemList.size());
                if (filteredItemList.size() > mMemoriesPicMin) {
                    if (createMemories(filteredItemList)) {
                        return true;
                    }
                }
                if (isHighPriorityMemories()) {
                    // 扫描中断，记录原因
                    mStopReason = STOP_REASON_HIGH_PRIORITY;
                    return false;
                }
            }
        }
        // 扫描中断，记录原因
        mStopReason = STOP_REASON_CREATED_FAILED;
        return false;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.DATE_ONE_YEAR_MEMORIES;
    }

    @Override
    public int getNameType() {
        return MemoriesNameRules.NAME_DATE_ONE_YEAR;
    }

    @Override
    protected boolean needShowNotification() {
        return isHighPriorityMemories();
    }

    private boolean hasDateOneYearOptimalCreated(Context context, MemoriesScannerHelper.DateRange dateRange) {
        Cursor cursor = null;
        try {
            //Uri queryUri = MemoriesStore.Set.getContentUri();
            StringBuilder sb = new StringBuilder();
            sb.append(GalleryStore.MemoriesSet.START_TIME + " BETWEEN ").append(dateRange.mStart)
                    .append(" AND ").append(dateRange.mEnd);
            sb.append(" AND " + GalleryStore.MemoriesSet.TYPE + " = " + MemoriesType.DATE_ONE_YEAR_MEMORIES);
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.MEMORIES_SET)
                    .setProjection(new String[]{GalleryStore.MemoriesSet.TYPE, GalleryStore.MemoriesSet.START_TIME})
                    .setWhere(sb.toString())
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if ((cursor == null) || (cursor.getCount() == 0)) {
                return false;
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return true;
    }
}
