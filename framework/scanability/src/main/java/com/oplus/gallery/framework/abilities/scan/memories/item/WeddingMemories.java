/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WeddingMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/3/15
 ** Author:hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D    2018/3/15      1.0        build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import android.content.Context;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper.DateRange;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class WeddingMemories extends Memories {
    private static final String TAG = "WeddingMemories";
    private static final String STOP_REASON_SCAN_FAILED = TAG + "_ScanFailed";
    private static final String STOP_REASON_HIGH_PRIORITY = TAG + "_HighPriority";
    private static final String STOP_REASON_LABEL_NOT_EXIST = TAG + "_LabelNotExist";
    private static final String KEY_LABEL_WEDDING = "婚礼";
    private static final float LABEL_PIC_PERCENT = 0.5f;
    private int mNameType = -1;
    private int mLabelId = -1;

    public WeddingMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        mLabelId = LabelDictionary.getLabelId(KEY_LABEL_WEDDING);
        if (isHighPriorityMemories()) {
            DateRange dateRange = MemoriesScannerHelper.getDaysRange(MemoriesScannerHelper.WEDDING_MEMORIES_TRIGGER_DAY_OFFSET);
            boolean hasLabelExist = MemoriesProviderHelper.hasLabelExistInDateRange(dateRange, mLabelId);
            GLog.v(TAG, "prepareMemories hasLabelExist:" + hasLabelExist + ",dateRange:" + dateRange);
            // 扫描中断，记录原因
            if (!hasLabelExist) {
                mStopReason = STOP_REASON_LABEL_NOT_EXIST;
            }
            return hasLabelExist;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        LinkedHashMap<DateRange, Integer> rangeMap = MemoriesScannerHelper.getItemDataRangeSizeByLabelId(mContext,
                (int) (LABEL_PIC_PERCENT * mMemoriesPicMin), mLabelId);
        long curTime = System.currentTimeMillis();
        for (Map.Entry<DateRange, Integer> entry : rangeMap.entrySet()) {
            List<MediaItem> itemList = MemoriesProviderHelper.getItemListFromLocalMedia(mContext, entry.getKey(), mMemoriesPicMin);
            mMediaItemList = MemoriesProviderHelper.getFilteredItemList(itemList, getLabelFilterIdList());
            boolean allowMemoriesCreate = allowMemoriesCreate(mMediaItemList, curTime);
            GLog.d(TAG, "scanMemories, allowMemoriesCreate:" + allowMemoriesCreate
                    + ",mMediaItemList size:" + mMediaItemList.size());
            if (allowMemoriesCreate && (mMediaItemList.size() > mMemoriesPicMin)) {
                int picSize = mMediaItemList.size();
                if (entry.getValue() >= (LABEL_PIC_PERCENT * picSize)) {
                    return createMemories(mMediaItemList);
                } else {
                    mStopSubReason = STOP_REASON_OVER_SIZE;
                }
            } else if (mStopSubReason == null) {
                mStopSubReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            }
            if (isHighPriorityMemories()) {
                // 扫描中断，记录原因
                mStopReason = STOP_REASON_HIGH_PRIORITY + mStopSubReason;
                return false;
            }
        }
        // 扫描中断，记录原因
        mStopReason = STOP_REASON_SCAN_FAILED + mStopSubReason;
        return false;
    }

    private boolean allowMemoriesCreate(List<MediaItem> itemList, long curTime) {
        mStopSubReason = null;
        DateRange dateRange = MemoriesScannerHelper.getDateRangeOfItemList(itemList);
        if (dateRange == null) {
            GLog.w(TAG, "allowMemoriesCreate, dateRange is null!");
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_DATE_RANGE_NULL;
            return false;
        }
        if (MemoriesScannerHelper.getDaysBetweenTwoDates(dateRange.mEnd, curTime) <= 0) {
            GLog.d(TAG, "allowMemoriesCreate, memories is not before today!");
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_NOT_BEFORE_TODAY;
            return false;
        }
        if (MemoriesProviderHelper.hasMemoriesCreatedInDateRange(dateRange, getMemoriesId())) {
            GLog.d(TAG, "allowMemoriesCreate, memories has created in date range:" + dateRange);
            // 扫描中断，记录原因
            mStopSubReason = STOP_REASON_CREATED_IN_DATE_RANGE;
            return false;
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return true;
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.EVENT_WEDDING_MEMORIES;
    }

    @Override
    public int getNameType() {
        if (mNameType < 0) {
            mNameType = MemoriesNameRules.getWeddingRandomNameType();
        }
        return mNameType;
    }

    @Override
    protected boolean needShowNotification() {
        return isHighPriorityMemories();
    }

}
