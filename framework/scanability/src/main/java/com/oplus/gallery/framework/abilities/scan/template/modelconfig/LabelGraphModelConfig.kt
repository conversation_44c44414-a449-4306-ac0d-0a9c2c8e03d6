/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LabelGraphModelConfig.kt
 ** Description : 知识图谱资源配置类
 ** Version     : 1.0
 ** Date        : 2024/3/19
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                            2024/3/19      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template.modelconfig

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName
import com.oplus.gallery.framework.abilities.extraction.labelgraph.BaseLabelGraphModelDownloadConfig
import com.oplus.gallery.framework.abilities.extraction.labelgraph.LabelGraphModelDownloadConfig

/**
 * 知识图谱资源配置类
 */
class LabelGraphModelConfig(val context: Context) : ModelConfig(ModelName.LABEL_GRAPH_SOURCE) {

    override val modelDataRefreshFlagKey: String = LabelGraphModelDownloadConfig.hasLabelGraphDataUpdateSuccessKey

    override var currentVersion: Int
        get() = LabelGraphModelDownloadConfig.getModelVersion(context)
        set(value) {
            LabelGraphModelDownloadConfig.setModelVersion(context, value)
        }

    override fun isModelCanRW(): Boolean = checkDefaultPathFileCanRW(LabelGraphModelDownloadConfig.labelGraphDbName)
            && checkDefaultPathFileCanRW(BaseLabelGraphModelDownloadConfig.CV_LABEL_GRAPH_SO_NAME)
            && checkDefaultPathFileCanRW(BaseLabelGraphModelDownloadConfig.SQL_CIPHER_SO_NAME)
}