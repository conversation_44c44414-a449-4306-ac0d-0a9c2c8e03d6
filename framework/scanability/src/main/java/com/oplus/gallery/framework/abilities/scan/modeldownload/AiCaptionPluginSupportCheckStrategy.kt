/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  AiCaptionPluginSupportCheckStrategy.kt
 * * Description:  caption插件下载策略检查
 * * Version: 1.0
 * * Date : 2025/04/29
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/04/29     1.0        create
 ************************************************************/
package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor.Companion.MODEL_DOWNLOAD_CANCEL
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor.Companion.MODEL_DOWNLOAD_ERROR_CODE
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor.Companion.MODEL_DOWNLOAD_START
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.foundation.tracing.helper.CaptionTrackHelper.trackCaptionDownloadDialogShow
import com.oplus.gallery.foundation.tracing.helper.CaptionTrackHelper.trackCaptionDownloadDialogUserClickAction
import com.oplus.gallery.foundation.tracing.helper.CaptionTrackHelper.trackCaptionDownloadError
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_CAPTION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.CAPTION_MODEL_USER_CANCEL_DOWNLOAD
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.AI_CAPTION_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.UPDATE_AI_CAPTION_PLUGIN_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.download.AIUnitDownloadWord
import com.oplus.gallery.framework.abilities.search.caption.ICaptionAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * caption插件检查策略
 * 目前策略：
 * 1. AiCaptionInterceptor 支持caption(支持融合搜索 + caption rus开关打开)。
 * 2. AIUnitPrivacyInterceptor aiunit隐私协议加粗。
 * 3. AIUnitModelDownloadInterceptor 下载。静默下载。
 */
class AiCaptionPluginSupportCheckStrategy : ISupportCheckStrategy {
    private var chain: RealInterceptorChain? = null

    /**
     * 是否允许ui显示
     */
    private var enableUI = false

    /**
     * 允许ui显示的词条
     */
    private var uiWord: AIUnitDownloadWord? = null

    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        runCatching {
            val bundle = Bundle()
            chain = RealInterceptorChain(
                getInterceptors(context, postNotificationAction),
                onSuccessCallback = { setAiCaptionAbilityDownloading(false) },
                onFailCallback = {
                    val modelDownloadCancel = it.getBoolean(MODEL_DOWNLOAD_CANCEL, false)
                    if (modelDownloadCancel) {
                        trackCaptionDownloadDialogUserClickAction(true)
                    }
                    val modelDownloadErrorCode = it.getInt(MODEL_DOWNLOAD_ERROR_CODE, Int.MIN_VALUE)
                    if (modelDownloadErrorCode != Int.MIN_VALUE) {
                        trackCaptionDownloadError(modelDownloadErrorCode.toString())
                    }
                    ContextGetter.context.getAppAbility<IConfigSetterAbility>()?.use { configSetterAbility ->
                        configSetterAbility.setBooleanConfig(CAPTION_MODEL_USER_CANCEL_DOWNLOAD, modelDownloadCancel)
                    }
                    setAiCaptionAbilityDownloading(false)
                },
                onProgressCallback = {
                    val modelDownloadStart = it.getBoolean(MODEL_DOWNLOAD_START, false)
                    GLog.d(TAG, LogFlag.DL) { "modelDownloadStart, value: $modelDownloadStart" }
                    if (modelDownloadStart) {
                        trackCaptionDownloadDialogUserClickAction(false)
                    }
                }
            )
            chain?.proceed(bundle)
        }.onFailure {
            setAiCaptionAbilityDownloading(false)
        }
    }

    /**
     * 获取拦截器
     * 1.支持融合
     * 2.aiunit隐私协议
     * 3.aiunit模型下载
     */
    private fun getInterceptors(context: Context, postNotificationAction: (NotificationAction) -> Unit) = listOf(
        AiCaptionInterceptor(),
        AIUnitPreCheckInterceptor(),
        AIUnitModelDownloadInterceptor(
            context,
            AIUnitPluginState(
                AIUnitPlugin.AI_CAPTION.downloadPlugin,
                AI_CAPTION_PLUGIN_DOWNLOAD_STATE,
                UPDATE_AI_CAPTION_PLUGIN_SHOW_TIMESTAMP
            ),
            uiWord,
            enableUI = { enableUI },
            updateUI = postNotificationAction,
            startDownload = {
                setAiCaptionAbilityDownloading(true)
                trackCaptionDownloadDialogShow()
            }
        )
    )

    fun setEnableUi(enable: Boolean) {
        enableUI = enable
    }

    fun setUiWord() {
        uiWord = AIUnitDownloadWord(
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_download_title,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_update_des,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_content,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_download_fail,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_install_fail,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_update,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_downloading,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_update_finish,
            com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_picture_caption_dialog_download_finish
        )
    }

            /**
     * 设置caption的下载状态
     */
    private fun setAiCaptionAbilityDownloading(value: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "setAiCaptionAbilityDownloading, value: $value" }
        ContextGetter.context.getAppAbility<ICaptionAbility>()?.use { it.isDownloading = value }
    }

    override fun destroy() {
        chain?.destroy()
        chain = null
    }

    companion object {
        private const val TAG = "AiCaptionPluginSupportCheckStrategy"
    }

    /**
     * caption下载拦截器——不支持caption则拦截
     */
    internal class AiCaptionInterceptor : ConditionInterceptor() {
        override fun onCheckCondition(param: Bundle): Boolean {
            return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_CAPTION, defValue = false, expDefValue = false)
        }
    }
}