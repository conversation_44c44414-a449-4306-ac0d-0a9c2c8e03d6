/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - HDFaceScanner.java
 * Description:
 * Version: 1.0
 * Date: 2025/03/14
 * Author: wangkang@Apps.Gallery3D
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * wangkang@Apps.Gallery3D   2025/03/14     1.0              build this module
</desc></version></date></author> */
package com.oplus.gallery.framework.abilities.scan.face

import android.content.Context
import android.graphics.Bitmap
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.RemoteModelInfoManager
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.HAS_FACE_SCAN_OVER_KEY
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo.Companion.EXPRESSION_DETAIL_DEFAULT
import com.oplus.gallery.business_lib.model.data.face.data.ImageFeature
import com.oplus.gallery.business_lib.model.data.face.data.imageFeatureToCvFaceClustersList
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.data.utils.GalleryScanConstants.GROUP_ID_2
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.LARGE_THUMBNAIL_DEFAULT_TARGET_SIZE
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.MEDIUM_THUMBNAIL_DEFAULT_TARGET_SIZE
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.THUMBNAIL_SIZE_PHOTO
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_AI_BEST_TAKE_HD_SCAN_TRIGGER
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.face.model.FaceModelConfig
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.isCountAllow
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanProgressCollector
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanPersonPetProviderHelper
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.FaceClusterUtil
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.codec.DecodeUtils.decode
import com.oplus.gallery.standard_lib.util.io.IOUtils
import kotlin.math.min
import kotlin.system.measureTimeMillis

/**
 * 高清图扫描
 * @param context 上下文
 */
class HDFaceScanner(
    context: Context,
    galleryScanProgressCollector: GalleryScanProgressCollector?,
    canScanWhenGalleryRunOnTop: Boolean
) : GalleryScan(context) {

    private var faceAnalyzer: IFaceAnalyzer? = null
    private var faceScanAbility: IFaceScanAbility? = null

    private var currentVersion = 0
    private var updateSuccess = false

    /**
     * 在没有高清图片需要更新的情况下，不重复扫标记。
     * 一次扫描质量会多次执行onScan
     */
    private var scanImageOver = false

    /**
     * 当前已扫描图片数量
     * 全局变量，防止多次onScan调用多次
     */
    private var scannedCount = 0

    private val isLightLow by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_PRODUCT_LIGHT_LOW)
    }

    init {
        faceScanAbility = (context.applicationContext as GalleryApplication).getAppAbility(IFaceScanAbility::class.java)
        faceAnalyzer = faceScanAbility?.newFaceAnalyzer(context)
        scannedCount = 0
        scanImageOver = false
        mGalleryScanProgressCollector = galleryScanProgressCollector
        mCanScanWhenGalleryRunOnTop = canScanWhenGalleryRunOnTop
    }

    override fun getScanType(): Int {
        return HD_FACE_SCAN
    }

    override fun getSceneName(): String {
        return SCENE_NAME
    }

    override fun onScan(triggerType: Int, config: ScanConfig) {
        GLog.w(TAG, LogFlag.DL) { "onScan: start HD face scan triggerType =$triggerType" }
        super.onScan(triggerType, config)
        val isTriggerOn = DEBUG_AI_BEST_TAKE_HD_SCAN_TRIGGER
        if (!isTriggerOn) {
            GLog.w(TAG, LogFlag.DL) { "onScan, start HD face scan trigger off" }
            completedScan(scanType)
            return
        }
        GLog.d(TAG, LogFlag.DL) { "onScan: HD face scannedCount = $scannedCount , maxScanCount = ${getMaxScanCount()}" }
        // 一次优化有上限判断，后续多次调用onScan，拦截
        if (isCountAllow(scannedCount, getMaxScanCount()).not()) {
            GLog.d(TAG, LogFlag.DL) {
                "onScan: HD face scanned count greater than max count, max:${getMaxScanCount()} , scannedCount:$scannedCount"
            }
            completedScan(scanType)
            return
        }
        // 上次onScan已经没有待优化图片，多次执行onScan时，拦截
        if (scanImageOver) {
            GLog.d(TAG, LogFlag.DL) { "onScan: HD face all image is new, not need scan" }
            completedScan(scanType)
            return
        }
        // 功耗优化 前端业务发起的扫描不在此中断
        if (isLowPowerConsumptionStopScan() && !mCanScanWhenGalleryRunOnTop) {
            GLog.d(TAG, LogFlag.DL) { "onScan: HD face scan run in low power consumption mode, no need to scan" }
            return
        }
        if (isInterrupt && !mCanScanWhenGalleryRunOnTop) {
            GLog.d(TAG, LogFlag.DL) { "onScan: HD face scan is interrupted , not need to scan" }
            return
        }
        measureTimeMillis {
            deltaScan()
        }.let {
            GLog.i(TAG, LogFlag.DL) { "onScan: HD face scan deltaScan cost time:$it" }
        }
        // 扫描结束释放
        releaseAll()
        completedScan(scanType)
        GLog.i(TAG, LogFlag.DL) { "onScan: HD face scan end" }
    }

    /**
     * 功耗优化，拦截扫描
     *
     * @return 是否拦截扫描结果
     */
    private fun isLowPowerConsumptionStopScan(): Boolean {
        if (!isCloudAllowLowConsumptionScan()) {
            GLog.d(TAG, LogFlag.DL) {
                "isLowPowerConsumptionStopScan cloud disable"
            }
            return false
        }
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            GLog.d(TAG, LogFlag.DL) {
                "isLowPowerConsumptionStopScan battery charging"
            }
            return false
        }
        // 这里用这个方法有点浪费，先跟随FaceScanner的逻辑
        val result = GalleryScanPersonPetProviderHelper.getScannedItemsWithoutRecycled()
        GLog.d(TAG, LogFlag.DL) {
            "isLowPowerConsumptionStopScan result.size:${result?.size}"
        }
        return result.isNullOrEmpty()
    }

    /**
     * 扫描
     */
    @Suppress("LongMethod")
    private fun deltaScan() {
        // 1、初始化engine
        val engineInit = engineInit()
        if (!engineInit) {
            GLog.w(TAG, LogFlag.DL) { "deltaScan engineInit failed" }
            return
        }
        if (isInterrupt && !mCanScanWhenGalleryRunOnTop) {
            GLog.w(TAG, LogFlag.DL) { "deltaScan isInterrupt." }
            return
        }
        // 2、查询出所有的group id信息
        val allGroupIDList = GalleryScanProviderHelper.queryScanFaceAllGroupID()
        GLog.w(TAG, LogFlag.DL) { "deltaScan allGroupIDList SIZE = ${allGroupIDList.size}" }
        if (isInterrupt && !mCanScanWhenGalleryRunOnTop) {
            GLog.w(TAG, LogFlag.DL) { "deltaScan isInterrupt." }
            return
        }
        var faceInfoList = mutableListOf<CvFaceCluster>()
        // 3、过滤掉无效的filter信息
        allGroupIDList.filter { it > GROUP_ID_2 }.forEach { groupID ->
            GLog.w(TAG, LogFlag.DL) { "deltaScan allGroupIDList groupID = $groupID" }
            // 4、根据group id查询前30条数据，按表情分数从高到底排序
            val listByGroupId = GalleryScanProviderHelper.queryFaceInfoListByGroupId(
                groupID, MAX_QUERY_COUNT, null, true, intArrayOf(EXPRESSION_DETAIL_DEFAULT)
            )
            GLog.d(TAG, LogFlag.DL) {
                "deltaScan groupID:$groupID query.size: ${listByGroupId.size}"
            }
            faceInfoList.addAll(listByGroupId)
        }
        GLog.d(TAG, LogFlag.DL) {
            "deltaScan faceInfoList.size: ${faceInfoList.size}"
        }
        if (faceInfoList.isEmpty()) {
            return
        }
        if (isInterrupt && !mCanScanWhenGalleryRunOnTop) {
            GLog.w(TAG, LogFlag.DL) { "deltaScan isInterrupt." }
            return
        }
        // 5、过滤掉重复图片，同张图片只需要触发一次扫描
        faceInfoList = faceInfoList.distinctBy { it.data }.toMutableList()
        mAllCount = min(faceInfoList.size, getMaxScanCount())
        var scanImageFlag = false
        // 遍历查询人脸集合
        faceInfoList.forEach { oldCvFaceCluster ->
            oldCvFaceCluster.data?.let { data ->
                if (scannedCount >= getMaxScanCount()) {
                    GLog.d(TAG, LogFlag.DL) {
                        "deltaScan scan greater than max count, max:${getMaxScanCount()} ,scannedCount:$scannedCount"
                    }
                    // 超过上限直接结束
                    return
                }
                // 6、data查询mediaItem信息
                val mediaItem = LocalMediaDataHelper.getLocalMediaItem(LocalMediaDataHelper.getPathByFilePath(data))
                if (mediaItem == null) {
                    GLog.d(TAG, LogFlag.DL) {
                        "deltaScan mediaItem is null"
                    }
                    return@forEach
                }
                // 7、比较新扫描和老图大小，如果已经是满足要求的就不扫了
                if (!mediaItemCompareFaceCluster(mediaItem, oldCvFaceCluster)) {
                    GLog.d(TAG, LogFlag.DL) {
                        "deltaScan mediaItemCompareFaceCluster failed"
                    }
                    return@forEach
                }
                // 8、标记进行扫描
                scanImageFlag = true
                // 9、获取到新图的扫描集合
                val cvFaceClusters = getCvFaceClusters(mediaItem)
                GLog.d(TAG, LogFlag.DL) {
                    "deltaScan get scan from cache, filePath: ${PathMask.mask(mediaItem.filePath)}, size: ${cvFaceClusters?.size}"
                }
                if (cvFaceClusters.isNullOrEmpty()) {
                    return@forEach
                }
                scannedCount += 1
                mScanImageCount += 1
                // 计算并通知进度
                updateProgress(scanType)
                // 10、单个人脸直接更新信息到数据库
                if ((cvFaceClusters.size == 1) && (cvFaceClusters[0].isSingleFace)) {
                    val result = FaceClusterUtil.updateCvFaceCluster(oldCvFaceCluster, cvFaceClusters[0])
                    GalleryScanProviderHelper.updateFaceInfoList(listOf(result))
                    GLog.d(TAG, LogFlag.DL) {
                        "deltaScan isSingleFace end"
                    }
                    return@forEach
                }
                // 11、多个人脸查询数据库中图片已经存在信息，更新到数据库
                val queryFaceInfoList = GalleryScanProviderHelper.queryFaceInfoList(listOf(oldCvFaceCluster.data), 0)
                val updateList = FaceClusterUtil.compareList(queryFaceInfoList, cvFaceClusters)
                GalleryScanProviderHelper.updateFaceInfoList(updateList)
                GLog.d(TAG, LogFlag.DL) {
                    "deltaScan queryFaceInfoList.size: ${queryFaceInfoList.size} updateList.size: ${updateList.size}"
                }
            }
        }
        // 所有图片都是最新或者没有大图，即后续不需要再扫描更新。
        if (!scanImageFlag) {
            scanImageOver = true
            GLog.d(TAG, LogFlag.DL) { "deltaScan scanImageOver true" }
        }
        if (ComponentPrefUtils.getBooleanPref(mContext, HAS_FACE_SCAN_OVER_KEY, false)) {
            // 通过前台完成扫描，标记一下扫描结束状态，如果有新照片，或者下次更新算法，都会重置这个标记
            if (mCanScanWhenGalleryRunOnTop) {
                ComponentPrefUtils.setBooleanPref(mContext, ComponentPrefUtils.HAS_HD_FACE_FOREGROUND_SCAN_OVER_KEY, true)
                ComponentPrefUtils.setLongPref(mContext, ComponentPrefUtils.LAST_FOREGROUND_SCAN_OVER_TIME_KEY, System.currentTimeMillis())
            }
        } else {
            ComponentPrefUtils.setBooleanPref(mContext, ComponentPrefUtils.HAS_HD_FACE_FOREGROUND_SCAN_OVER_KEY, false)
        }
    }

    /**
     * 一次最大优化数量
     */
    private fun getMaxScanCount() = mScanConfig.imageCount.coerceAtMost(MAX_SCAN_IMAGE_COUNT_500)

    /**
     * 对比MediaItem和CvFaceCluster图的宽高情况，决定是否需要优化高清图
     * @param mediaItem MediaItem? local media表信息
     * @param cluster CvFaceCluster scan face表信息
     * @return Boolean 是否需要优化高清图
     */
    private fun mediaItemCompareFaceCluster(mediaItem: MediaItem?, cluster: CvFaceCluster): Boolean {
        mediaItem?.let {
            if ((it.width <= cluster.thumbWidth) && (it.height <= cluster.thumbHeight)) {
                return false
            }
            if ((it.height <= cluster.thumbWidth) && (it.width <= cluster.thumbHeight)) {
                return false
            }
            if ((cluster.thumbWidth > THUMBNAIL_SIZE_PHOTO) || (cluster.thumbHeight > THUMBNAIL_SIZE_PHOTO)) {
                return false
            }
            return true
        }
        return false
    }

    /**
     * MediaItem扫描转换成List<CvFaceCluster>
     *     仅对图片处理，视频不处理。
     * @param item MediaItem 图片信息
     * @return List<CvFaceCluster>? 扫描返回结果。
     */
    private fun getCvFaceClusters(item: MediaItem): List<CvFaceCluster>? {
        var imageFeatures: Array<ImageFeature>? = null
        var thumbnail: Bitmap? = null
        if (item.mediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
            val maxSize = if (isLightLow) {
                MEDIUM_THUMBNAIL_DEFAULT_TARGET_SIZE * MEDIUM_THUMBNAIL_DEFAULT_TARGET_SIZE.toLong()
            } else {
                LARGE_THUMBNAIL_DEFAULT_TARGET_SIZE * LARGE_THUMBNAIL_DEFAULT_TARGET_SIZE.toLong()
            }
            // 1、获取大图的数据扫描
            thumbnail = decode(item.filePath, maxSize)
            if (thumbnail != null) {
                GLog.d(TAG, LogFlag.DL) {
                    "getCvFaceClusters, image.path = ${item.path}, width= ${thumbnail.width}, height= ${thumbnail.height}"
                }
                // 2、扫描获取图片图像对应feature信息
                imageFeatures = faceAnalyzer?.scanImageFeatures(item, thumbnail, item.rotation)
            } else {
                GLog.w(TAG, LogFlag.DL) {
                    "getCvFaceClusters, thumbnail: null, LocalImage: $item"
                }
                return null
            }
        }
        // 3、特征为null时，是检测时发生了crash，为了防止偶发的crash导致用户文件一直不能扫描出来，先不要标记已经扫描，下次重试
        if (imageFeatures == null) {
            GLog.w(TAG, LogFlag.DL) {
                "getCvFaceClusters, imageFeature is null, LocalImage: $item"
            }
            return null
        }
        if (GalleryScanUtils.isTestMode()) {
            GLog.i(TAG, LogFlag.DL) {
                "getCvFaceClusters, imageFeature: ${imageFeatures.size}, filePath-end: ${item.filePath}"
            }
        }
        val newFaceInfoList = mutableListOf<CvFaceCluster>()
        // 4、feature转换成List<CvFaceCluster>，扫描失败的不处理，仅处理能加载成功的高清图
        if (imageFeatures.isNotEmpty()) {
            newFaceInfoList.addAll(imageFeatureToCvFaceClustersList(imageFeatures, item, thumbnail, currentVersion, saveFeatureFlag = true))
        }
        return newFaceInfoList
    }

    /**
     * 初始化mCvFaceEngine
     * @return Boolean 初始化mCvFaceEngine成功or失败
     */
    private fun engineInit(): Boolean {
        //initialize Engine
        val modelConfig: ModelConfig = FaceModelConfig()
        val remoteModelInfo = RemoteModelInfoManager.fetchCacheRemoteModelInfo(modelConfig.modelName, TAG)
        val result: Boolean = faceAnalyzer?.init(false, modelConfig, remoteModelInfo) ?: false
        val sp = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME)
        updateSuccess = sp.getBoolean(ComponentPrefUtils.HAS_FACE_DATA_UPDATE_SUCCESS_KEY, false)
        currentVersion = sp.getInt(ComponentPrefUtils.FACE_COMPONENT_VERSION_KEY, 0)
        GLog.d(TAG, LogFlag.DL) {
            "engineInit, currentVersion: $currentVersion, updateSuccess: $updateSuccess, result: $result"
        }
        return result
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }

    /**
     * 扫描结束释放操作
     */
    private fun releaseAll() {
        IOUtils.closeQuietly(faceAnalyzer)
        IOUtils.closeQuietly(faceScanAbility)
    }

    companion object {
        private const val TAG = ScanConst.TAG + "HDFaceScanner"
        private const val SCENE_NAME = "HDFaceScanner"

        /**
         * 查询同group id的个数限制
         */
        private const val MAX_QUERY_COUNT = 30

        /**
         * 一次最大优化数量 500
         */
        private const val MAX_SCAN_IMAGE_COUNT_500 = 500
    }
}