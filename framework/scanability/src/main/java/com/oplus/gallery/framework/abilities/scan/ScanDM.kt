/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ScanDM.kt
 ** Description:用来调用 Scan 模块里的代码，解决平层依赖的问题
 **
 **
 ** Version: 1.0
 ** Date:2020/09/29
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/09/29	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Rect
import android.util.Pair
import com.oplus.gallery.business_lib.api.IScanDM
import com.oplus.gallery.foundation.ui.autocrop.CropRect
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.foundation.cvimageprocess.autocrop.CropRatio
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.tracing.constant.ScanTrackConstant
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.scan.autocrop.AutoCrop
import com.oplus.gallery.framework.abilities.scan.autocrop.AutoCropScanner
import com.oplus.gallery.framework.abilities.scan.component.AlarmReceiver
import com.oplus.gallery.framework.abilities.scan.edit.EditInfoScanner
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper
import com.oplus.gallery.framework.abilities.scan.helpers.ScanUserAssetsHelper
import com.oplus.gallery.framework.abilities.scan.label.LabelScanner
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanService
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanSyncTaskManager
import com.oplus.gallery.framework.abilities.scan.memories.Memories
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanPersonPetProviderHelper
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectDBOperation
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.VideoLabelModelConfig
import com.oplus.gallery.framework.abilities.scan.utils.FaceClusterUtil
import com.oplus.gallery.router_lib.annotations.Component
import com.oplus.gallery.standard_lib.app.TrackScope
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Component(interfaceName = "com.oplus.gallery.business_lib.api.IScanDM")
class ScanDM : IScanDM {
    /**
     * 人脸扫描锁
     */
    private val scanFaceLock = Object()

    override fun initScanModule(context: Context) {
        GLog.e(TAG, "ScanDM: initScanModule")
        AlarmReceiver.setAlarmReceiver(context)
        GalleryScanService.scheduleJob(context)

        TrackScope.launch {
            // 埋点延时5000ms执行
            delay(DELAY_FIVE_THOUSAND_MS)
            // for face attribute statistics
            ScanTrackHelper.trackFaceAttributeMap(context)
        }
    }

    override fun setSDCardStateChanged(stateChanged: Boolean) {
        GalleryScanDataManager.getInstance().setSDCardStateChanged(stateChanged)
    }

    override fun freeFaceFromGroup(galleryIds: List<Long>, origGroupId: Long, albumSetType: Int): Boolean {
        return GalleryScanDataManager.getInstance().freeFaceFromGroup(galleryIds, origGroupId, albumSetType)
    }

    override fun uniteGroup(majorGroupId: Long, groupList: List<Long>, isShow: Boolean, albumSetType: Int): Boolean {
        return GalleryScanDataManager.getInstance().uniteGroups(majorGroupId, groupList, isShow, albumSetType)
    }

    override fun setGroupShowEnable(groupIdList: List<Long>, enable: Boolean, albumSetType: Int) {
        return GalleryScanDataManager.getInstance().setGroupShowEnable(groupIdList, enable, albumSetType)
    }

    override fun getNewListIfTooMany(imageList: List<MediaItem>): List<MediaItem> =
        Memories.getNewListIfTooMany(imageList)

    override fun getScanUpdateReqList(list: List<BaseImageInfo>): List<UpdateReq> {
        return GalleryScanDataManager.getInstance().getScanUpdateReqList(list)
    }

    override fun setPersonCover(context: Context, groupId: Long, newCoverId: Long, albumSetType: Int): Boolean {
        return GalleryScanDataManager.getInstance().setPersonCover(context, groupId, newCoverId, albumSetType)
    }

    override fun removeFromLabel(mediaItems: List<MediaItem?>, labelId: Int): Boolean {
        return GalleryScanDataManager.getInstance().removeFromLabel(mediaItems, labelId)
    }

    override fun renameGroup(context: Context, groupId: Long, name: String, albumSetType: Int): Pair<Long, Int> {
        return GalleryScanDataManager.getInstance().renameGroup(context, groupId, name, albumSetType)
    }

    override fun createPersonPetGroup(context: Context, groupIdsInfo: MutableMap<Int, ArrayList<Path>>): Pair<Int, Int> {
        return GalleryScanDataManager.getInstance().createPersonPetGroup(context, groupIdsInfo)
    }

    override fun disbandPersonPetGroups(context: Context, groupIdList: List<Long>, albumSetType: Int): Int {
        return GalleryScanDataManager.getInstance().disbandPersonPetGroups(context, groupIdList, albumSetType)
    }

    override fun setPersonPetRelationshipWithMe(groupId: Long, relationName: String, relationType: Int, albumSetType: Int): Boolean {
        return GalleryScanDataManager.getInstance().setPersonPetRelationshipWithMe(groupId, relationName, relationType, albumSetType)
    }

    override fun getGroupName(groupId: Long, albumSetType: Int): String? {
        return GalleryScanDataManager.getInstance().getGroupName(groupId, albumSetType)
    }

    override fun interruptScanForReasonScreenOn() {
        GalleryScanSyncTaskManager.INSTANCE.interruptScan(GalleryScanMonitor.ReasonType.REASON_SCREEN_ON)
    }

    override fun isVideoLabelScanReady(): Boolean {
        return VideoLabelModelConfig().isModelReady()
    }

    override fun getAllFaceImageCount(): Int {
        return GalleryScanPersonPetProviderHelper.getAllFaceImageCount()
    }

    override fun getImageFaceCount(): Int {
        return GalleryScanPersonPetProviderHelper.getImageFaceCount()
    }

    override fun getVideoFaceCount(): Int {
        return GalleryScanPersonPetProviderHelper.getVideoFaceCount()
    }

    override fun getCloudOcrPictureCount(): Int {
        return ScanUserAssetsHelper.getCloudOcrPictureCount()
    }

    override fun getAllOcrPictureCount(): Int {
        return ScanUserAssetsHelper.getAllOcrPictureCount()
    }

    override fun getImageLabelTrack(): String {
        return ScanUserAssetsHelper.getImageLabelTrack()
    }

    override fun getVideoLabelTrack(): String {
        return ScanUserAssetsHelper.getVideoLabelTrack()
    }

    override fun getLabelsByMediaTypeAndFlagsTrack(mediaType: Int): String {
        return ScanUserAssetsHelper.getLabelsByMediaTypeTrack(mediaType)
    }

    override fun getOliveLabelsTrack(): String {
        return ScanUserAssetsHelper.getOliveLabelsTrack()
    }

    override fun getImageLabelCountTrack(): Int {
        return ScanUserAssetsHelper.getImageLabelCountTrack()
    }

    override fun getVideoLabelCountTrack(): Int {
        return ScanUserAssetsHelper.getVideoLabelCountTrack()
    }

    override fun getMemoriesPropertyTrack(context: Context): String? {
        return ScanUserAssetsHelper.getMemoriesPropertyTrack(context)
    }

    override fun doFaceScan(maxScanCount: Int) {
        // 最大扫描张数imageCount
        val scanConfig = ScanConfig(type = GalleryScan.FACE_AND_HD_FACE_SCAN_TYPE, imageCount = maxScanCount)
        GalleryScanSyncTaskManager.INSTANCE.doFaceScan(ScanTrackConstant.Value.AI_BEST_TAKE_SCAN_TRIGGER, scanConfig)
    }

    override fun manualCancelFaceScan() {
        GalleryScanSyncTaskManager.INSTANCE.interruptScan(GalleryScanMonitor.ReasonType.REASON_MANUAL_CANCEL)
    }

    override fun updateScanProgress(progress: Int) {
        GalleryScanSyncTaskManager.INSTANCE.updateScanProgress(progress)
    }

    override fun getScanProgress(): Int {
        return GalleryScanSyncTaskManager.INSTANCE.scanProgress
    }

    override fun isScannerIdle(): Boolean {
       return GalleryScanSyncTaskManager.INSTANCE.isScannerIdle
    }

    override fun scheduleJob(context: Context) {
        GalleryScanService.scheduleJob(context)
    }

    override fun startSyncTask(context: Context) {
        GalleryScanDataManager.getInstance().startSyncTask(context)
    }

    /**
     * 根据图片列表获取对应的标签id
     * @param bitmaps: List<Bitmap>
     * @return HashSet<Long>
     */
    override fun getImageLabelIds(bitmaps: List<Bitmap>): HashSet<Long> {
        return LabelScanner(ContextGetter.context).getImageLabelId(bitmaps)
    }

    override fun scanLabel(mediaItem: MediaItem, useScreenNailCache: Boolean): List<Int>? {
        return LabelScanner(ContextGetter.context).classifyPhoto(mediaItem, useScreenNailCache)
    }

    override fun scanLabel(mediaItem: MediaItem, bitmap: Bitmap): List<Int>? {
        return LabelScanner(ContextGetter.context).classifyPhoto(mediaItem, bitmap)
    }

    override fun isNewestAutoCropDataVersion(version: Int): Boolean = version == AutoCropScanner.AUTO_CROP_DATA_VERSION

    override fun getCropRects(context: Context, item: MediaItem, targetCropTypes: Array<CropRatio>): MutableList<CropRect>? {
        return AutoCrop.getCropRects(context, item, targetCropTypes)
    }

    override fun getCropRects(context: Context, bitmap: Bitmap, imageFilePath: String, ratios: FloatArray): Array<Rect?> {
        return AutoCrop.getCropRects(context, bitmap, imageFilePath, ratios)
    }

    override fun buildSeniorVersionNotScanned(): String {
        return SeniorSelectDBOperation.assembleConditionNotSeniorScanned()
    }

    override fun querySeniorResultAsSeniorMediaInfo(mediaItem: MediaItem): Any? {
        return SeniorSelectDBOperation.querySeniorResultAsSeniorMediaInfo(mediaItem)
    }

    override fun queryLabelString(context: Context, mediaItem: MediaItem): StringBuilder {
        val result = StringBuilder()
        GalleryScanProviderHelper.queryLabelImageList(
            SeniorSelectDBOperation.getImagesClassifySql(listOf(mediaItem))
        )?.forEach { baseScanLabelInfo ->
            val labelName = LabelDictionary.getLabelName(baseScanLabelInfo.mSceneId)
            result.append(context.getString(R.string.scan_label_desc))
            result.append(baseScanLabelInfo.mSceneId)
            result.append(context.getString(R.string.scan_label_separator))
            result.append(labelName)
        }
        return result
    }

    override fun setEditInfoScanEnable(isEnable: Boolean) {
        EditInfoScanner.isEnable = isEnable
    }

    /**
     * query List<CvFaceCluster> by list _data
     *
     * @param parameters 根据GalleryStore.ScanFace.DATA，查询数据库face内容. 可查询多条、和所有内容
     * @param limit 限制查询数量
     * @return result
     */
    override fun queryFaceInfoList(limit: Int, vararg parameters: String): List<CvFaceCluster> {
        return GalleryScanProviderHelper.queryFaceInfoList(parameters.toList(), limit)
    }

    /**
     * query List<CvFaceCluster> by groupId
     *
     * @param groupId    需大于0, 为有效值.
     * @param limit      是否需要限制条数, 默认为0,不限制.
     * @param exceptData 查询值是否需要排除当前条件，即不包含某条数据,可为null.
     * @param order      face_Expression_Score排序，true 降序 false 升序。例: face_Expression_Score DESC
     * @param expressionDetails 表情详情：大笑：0 微笑：1 中立：2  默认-1不处理.
     * @return result
     */
    override fun queryFaceInfoListByGroupId(
        groupId: Int,
        limit: Int,
        exceptData: String?,
        order: Boolean,
        vararg expressionDetails: Int
    ): List<CvFaceCluster> {
        return GalleryScanProviderHelper.queryFaceInfoListByGroupId(groupId, limit, exceptData, order, expressionDetails)
    }

    /**
     * insert list<CvFaceCluster> to SCAN_FACE table
     *
     * @param faceInfoList 待插入到数据的集合数据
     * @return 插入返回结果
     */
    override fun insertFaceInfoList(faceInfoList: List<CvFaceCluster>): Array<BatchResult>? {
        return GalleryScanProviderHelper.insertFaceInfoList(faceInfoList)
    }

    /**
     * update list<CvFaceCluster> to SCAN_FACE table
     *
     * @param faceInfoList 待更新到数据的集合数据
     */
    override fun updateFaceInfoList(faceInfoList: List<CvFaceCluster>) {
        GalleryScanProviderHelper.updateFaceInfoList(faceInfoList)
    }

    /**
     * delete SCAN_FACE info by filePath
     *
     * @param filePath 根据path删除face info信息
     */
    override fun deleteFaceInfo(filePath: String) {
        GalleryScanProviderHelper.deleteFaceInfo(filePath)
    }

    /**
     * 更新SCAN_FACE表groupID相关值
     *
     * @param updateList 需要更新的集合，CvFaceInfo中携带要更新的group ID
     * @param hasModelUpdated   因为版本更新，所以需要重新扫描
     */
    override fun updateScanFaceTableGroupID(updateList: List<CvFaceInfo>, hasModelUpdated: Boolean) {
        return GalleryScanProviderHelper.batchUpdateFaceGroupId(updateList, hasModelUpdated)
    }

    /**
     * 获取GalleryStore.ScanFace.TAB中GroupID最大值。
     * 用于赋值相册中仅有一张人脸无重复，无法聚类分组groupID的情况下，赋值groupID起始值，保证表中group id值唯一
     *
     * @return GroupID最大值
     */
    override fun queryMaxGroupIDForScanFaceTable(): Int {
        return GalleryScanProviderHelper.queryMaxGroupIDForScanFaceTable()
    }

    /**
     * 根据指定图片路径，找对应时间范围内的所有图标数量
     * @param filePath 要查询的图片路径
     * @param interval 毫秒，时间间隔范围，比如前后5秒。
     * @param limitCount    是否数量限制，0则不限制
     * @param containCurrent    查询结果是否包含当前filePath数据
     * @param dateTakenInMs    图片对应dateTaken时间
     * @return 查询结果，符合条件的MediaItem集合
     */
    override fun queryMediaTableData(
        filePath: String,
        interval: Long,
        limitCount: Int,
        containCurrent: Boolean,
        dateTakenInMs: Long,
    ): List<MediaItem> {
        return GalleryScanProviderHelper.queryMediaTableData(filePath, interval, limitCount, containCurrent, dateTakenInMs)
    }

    /**
     * 获取SCAN_FACE表中所有GroupID集合
     */
    override fun queryScanFaceAllGroupID(): List<Int> {
        return GalleryScanProviderHelper.queryScanFaceAllGroupID()
    }

    /**
     * 比较两个集合并更新人脸数据
     * @param queryFaceInfoList List<CvFaceCluster> 数据库中的图片信息
     * @param newCvFaceClusters List<CvFaceCluster> 新扫描的图片信息
     * @param isSingleFace Boolean 是否为单人模式（true 表示单人，false 表示多人）
     * @return List<CvFaceCluster> 更新后的 CvFaceCluster 列表
     */
    override fun matchFaces(
        queryFaceInfoList: List<CvFaceCluster>,
        newCvFaceClusters: List<CvFaceCluster>,
        isSingleFace: Boolean
    ): List<CvFaceCluster> {
        return if (isSingleFace) {
            listOf(FaceClusterUtil.updateCvFaceCluster(queryFaceInfoList[0], newCvFaceClusters[0]))
        } else {
            FaceClusterUtil.compareList(queryFaceInfoList, newCvFaceClusters)
        }
    }

    override fun needFaceScan(context: Context): Boolean {
        return GalleryScanProviderHelper.needFaceScan(context)
    }

    companion object {
        private const val TAG = "ScanDM"
        private const val DELAY_FIVE_THOUSAND_MS = 5000L
    }
}