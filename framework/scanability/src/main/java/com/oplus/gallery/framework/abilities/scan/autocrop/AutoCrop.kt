/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - AutoCrop.kt
 ** Description: 智能裁剪能力，包含辅助裁剪的查询相册数据库逻辑
 ** Version: 1.0
 ** Date : 2021/12/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2021/12/21        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.scan.autocrop

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Rect
import android.util.Size
import androidx.annotation.WorkerThread
import com.oplus.gallery.basebiz.imagerequest.ImageRequester
import com.oplus.gallery.foundation.ui.autocrop.CropRect
import com.oplus.gallery.foundation.ui.autocrop.CropRectSet
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.cvimageprocess.autocrop.AutoCropImpl
import com.oplus.gallery.foundation.cvimageprocess.autocrop.CropRatio
import com.oplus.gallery.foundation.cvimageprocess.autocrop.IAutoCrop
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.scan.face.IFaceDetector
import com.oplus.gallery.framework.abilities.scan.face.IFaceScanAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.scheduler.JobContextStub
import com.oplus.gallery.standard_lib.util.io.IOUtils
import kotlin.math.max

object AutoCrop {
    private const val TAG = "AutoCrop"
    private val faceRectProjection = arrayOf(
        GalleryStore.ScanFace.NO_FACE,
        GalleryStore.ScanFace.DATA,
        GalleryStore.ScanFace.LEFT,
        GalleryStore.ScanFace.TOP,
        GalleryStore.ScanFace.RIGHT,
        GalleryStore.ScanFace.BOTTOM,
        GalleryStore.ScanFace.THUMB_W,
        GalleryStore.ScanFace.THUMB_H
    )
    private const val INDEX_FACE_NO_FACE = 0
    private const val INDEX_FACE_FILE_PATH = 1
    private const val INDEX_FACE_LEFT = 2
    private const val INDEX_FACE_TOP = 3
    private const val INDEX_FACE_RIGHT = 4
    private const val INDEX_FACE_BOTTOM = 5
    private const val INDEX_FACE_THUMB_W = 6
    private const val INDEX_FACE_THUMB_H = 7
    private val autoCropImpl: IAutoCrop = AutoCropImpl()
    private var faceDetector: IFaceDetector? = null
    private var faceScanAbility: IFaceScanAbility? = null
    private var initialized = false

    @Volatile
    private var usingCount = 0

    @Synchronized
    private fun init(context: Context) {
        if (initialized) {
            return
        }
        val startTime = System.currentTimeMillis()
        if (autoCropImpl.init(context)) {
            initialized = true
        }
        faceScanAbility = context.applicationContext.getAppAbility<IFaceScanAbility>()
        faceDetector = faceScanAbility?.newFaceDetector()
        GLog.d(TAG, "init, cost:${GLog.getTime(startTime)}")
    }

    /**
     * 查询单个item的裁剪区域，注意使用后调用[release]
     * @param context Context
     * @param item 请求的照片或者视频
     * @param targetCropTypes 目标裁剪类型
     * @return 智能裁剪区域
     */
    @WorkerThread
    fun getCropRects(context: Context, item: MediaItem, targetCropTypes: Array<CropRatio>): MutableList<CropRect>? {
        return getCropRects(context, listOf(item), targetCropTypes)[item.filePath]
    }

    /**
     * 查询一批item的裁剪区域，注意使用后调用[release]
     * @param context Context
     * @param items 请求的照片或者视频
     * @param targetCropTypes 目标裁剪类型
     * @return 智能裁剪区域，key为文件绝对路径
     */
    @WorkerThread
    fun getCropRects(
        context: Context,
        items: List<MediaItem>,
        targetCropTypes: Array<CropRatio>
    ): MutableMap<String, MutableList<CropRect>?> {
        init(context)
        val result = mutableMapOf<String, MutableList<CropRect>?>()
        if (items.isEmpty()) return result
        val faceData = queryFaceRectData(items.map { it.filePath })
        items.forEach { item ->
            val bitmap = getThumbnail(item) ?: return result
            val width = bitmap.width
            val height = bitmap.height
            val cropRectList = mutableListOf<CropRect>()
            val existCropRects = CropRectSet.getCropRectMap(item.cropRectSet).mapNotNull { (key, value) ->
                value.getRect(bitmap.width, bitmap.height, 0)?.let { key to it }
            }.toMap()
            getCropRects(
                bitmap,
                item.filePath,
                targetCropTypes,
                existCropRects,
                checkAndDetectFaceRect(faceData, item.filePath, bitmap)
            ).forEachIndexed { index, rect ->
                rect?.let { cropRectList.add(CropRect(targetCropTypes[index].typeName, it, width, height)) }
            }
            result[item.filePath] = cropRectList
            BitmapPools.recycle(bitmap)
        }
        return result
    }

    @WorkerThread
    fun getCropRects(
        context: Context,
        bitmap: Bitmap,
        imageFilePath: String,
        targetAspectRatios: FloatArray,
    ): Array<Rect?> {
        init(context)
        return getCropRects(bitmap, imageFilePath, targetAspectRatios.map { CropRatio.getCropRatio(it) }.toTypedArray())
    }

    /**
     * 释放相关资源，裁剪结束后调用
     */
    @Synchronized
    fun release() {
        if ((usingCount > 0) || !initialized) {
            return
        }
        val startTime = System.currentTimeMillis()
        autoCropImpl.releaseSdk()
        IOUtils.closeQuietly(faceDetector)
        IOUtils.closeQuietly(faceScanAbility)
        initialized = false
        GLog.d(TAG, "release, cost:${GLog.getTime(startTime)}")
    }

    private fun getCropRects(
        bitmap: Bitmap,
        imageFilePath: String,
        targetCropTypes: Array<CropRatio>,
    ): Array<Rect?> {
        val faceRectArray = checkAndDetectFaceRect(queryFaceRectData(mutableListOf(imageFilePath)), imageFilePath, bitmap)
        return getCropRects(bitmap, imageFilePath, targetCropTypes, pivotalRects = faceRectArray)
    }

    /**
     * 获取bitmap的智能裁剪的区域，注意使用后调用[release]
     * @param bitmap 图像
     * @param imageFilePath 图片文件，用于查询相册数据库判断是否裁剪和提升裁剪效果
     * @param targetCropTypes 目标裁剪类型
     * @param existCropRects 已有的裁剪区域，部分裁剪场景可参考已有裁剪区域，以提高裁剪速度和准确度
     * @param pivotalRects 重点显示的区域，如人脸
     * @return 智能裁剪区域，不支持裁剪或裁剪出错的情况下item项为null
     */
    private fun getCropRects(
        bitmap: Bitmap,
        imageFilePath: String,
        targetCropTypes: Array<CropRatio>,
        existCropRects: Map<CropRatio, Rect> = emptyMap(),
        pivotalRects: Array<Rect>? = null
    ): Array<Rect?> {
        if (targetCropTypes.isEmpty()) {
            throw IllegalArgumentException("targetCropTypes is empty!")
        }
        GLog.d(TAG, "getCropRect. filePath:${PathMask.mask(imageFilePath)}, recommendRects=${pivotalRects.contentToString()}")
        usingCount++
        val result = arrayOfNulls<Rect?>(targetCropTypes.size)
        try {
            val rects = autoCropImpl.getCropRects(bitmap, targetCropTypes, existCropRects, pivotalRects) ?: return result
            targetCropTypes.forEachIndexed { index, cropType ->
                result[targetCropTypes.indexOf(cropType)] = rects.getOrNull(index)
            }
        } finally {
            usingCount--
        }
        return result
    }

    /**
     * 获取缩图用于智能裁剪，这里不做角度变换
     */
    private fun getThumbnail(item: MediaItem): Bitmap? {
        var thumbnail: Bitmap? = null
        val imageData = ImageRequester.requestBitmapOnBackground(item, JobContextStub)
        if (imageData != null) {
            thumbnail = imageData.bitmap
        }
        if (thumbnail == null) {
            GLog.w(TAG, "getThumbnail, thumbnail is null, media id is " + item.mediaId)
            return null
        }
        return thumbnail
    }

    /**
     * 从数据库查询照片人脸区域，返回map<绝对路径, 区域数组>
     */
    private fun queryFaceRectData(filePaths: List<String>): Map<String, MutableList<FaceRect>?> {
        return QueryReq.Builder<Map<String, MutableList<FaceRect>?>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCAN_FACE)
            .setProjection(faceRectProjection)
            .setWhere(DatabaseUtils.getWhereQueryIn(ScanFaceColumns.DATA, filePaths.size))
            .setWhereArgs(filePaths.toTypedArray())
            .setConvert(IConvert { cursor ->
                val result = mutableMapOf<String, MutableList<FaceRect>?>()
                var filePath = ""
                var left = 0
                var top = 0
                var right = 0
                var down = 0
                var refWidth = 0
                var refHeight = 0
                while (cursor.moveToNext()) {
                    filePath = cursor.getString(INDEX_FACE_FILE_PATH)
                    if (cursor.getInt(INDEX_FACE_NO_FACE) == 1) {
                        // 设置的目的是让外面检查是否有key，有则代表照片是经过扫描确认没有人脸区域，无需再跑算法
                        result[filePath] = null
                        continue
                    }
                    left = cursor.getInt(INDEX_FACE_LEFT)
                    top = cursor.getInt(INDEX_FACE_TOP)
                    right = cursor.getInt(INDEX_FACE_RIGHT)
                    down = cursor.getInt(INDEX_FACE_BOTTOM)
                    refWidth = cursor.getInt(INDEX_FACE_THUMB_W)
                    refHeight = cursor.getInt(INDEX_FACE_THUMB_H)
                    if ((left >= 0) && (top >= 0) && (right > left) && (down > top)) {
                        result.computeIfAbsent(filePath) { mutableListOf() }?.add(FaceRect(Rect(left, top, right, down), Size(refWidth, refHeight)))
                    }
                }
                return@IConvert result
            })
            .build()
            .exec()
    }

    /**
     * 检查是否有人脸扫描结果，没有则实时扫描
     */
    private fun checkAndDetectFaceRect(faceData: Map<String, MutableList<FaceRect>?>, filePath: String, bitmap: Bitmap): Array<Rect>? {
        return if (faceData.containsKey(filePath)) {
            faceData[filePath]?.map {
                val scale = max(bitmap.width, bitmap.height).toDouble() / max(it.referSize.width, it.referSize.height).toDouble()
                Rect(
                    (it.rect.left * scale).toInt(),
                    (it.rect.top * scale).toInt(),
                    (it.rect.right * scale).toInt(),
                    (it.rect.bottom * scale).toInt()
                )
            }?.toTypedArray()
        } else {
            // 没有key说明未经过扫描进行确认，需要扫描一次
            val startTime = System.currentTimeMillis()
            val faceInfos = faceDetector?.detectRect(bitmap)?.rectList
            if (GProperty.DEBUG) {
                GLog.d(TAG, "checkAndDetectFaceRect cost:${System.currentTimeMillis() - startTime}, count:${faceInfos?.size}")
            }
            faceInfos?.toTypedArray()
        }
    }

    /**
     * 人脸区域数据结构
     * @param rect 人脸区域
     * @param referSize 参考大小（扫描人脸区域时所用的缩图大小），用于缩放人脸区域到目标大小
     */
    private data class FaceRect(
        val rect: Rect,
        val referSize: Size
    )
}