/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PersonPetGroupCoverUtils.kt
 ** Description : 人宠合照封面计算工具类
 ** Version     : 1.0
 ** Date        : 2025/05/15
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/05/15  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.personpet

import android.graphics.RectF
import com.oplus.gallery.business_lib.model.data.personpet.utils.PersonPetDataHelper.PersonPetEntry.Companion.ROLE_PERSON
import com.oplus.gallery.business_lib.model.data.personpet.utils.PersonPetDataHelper.PersonPetEntry.Companion.ROLE_PET
import kotlin.math.max
import kotlin.math.min

/**
 * 人宠合照封面计算工具类
 */
object PersonPetGroupCoverUtils {
    private const val PERSON_WEIGHT = 1.0f
    private const val PET_WEIGHT = 1.0f
    private const val CLOSE_WEIGHT = 1.0f
    private const val SIZE_RATE = 1.1f
    private const val HALF_OF = 2f
    private const val OVER_SCORE = -10000

    /**
     * 计算封面分数
     * @param peronPetInfoList 表示 1 张图里的所有角色列表， PersonPetInfo 就是 1 个角色对象
     */
    @JvmStatic
    fun calcScore(peronPetInfoList: List<PersonPetInfo>): Float {
        val closeScore = calcCloseScore(peronPetInfoList) * CLOSE_WEIGHT
        val overScore = calcOverScore(peronPetInfoList)
        var roleScore = 0f
        peronPetInfoList.forEach {
            when (it.roleType) {
                ROLE_PERSON -> it.score?.let { personScore -> roleScore += (personScore * PERSON_WEIGHT) }
                ROLE_PET -> it.score?.let { petScore -> roleScore += (petScore * PET_WEIGHT) }
            }
        }
        return roleScore + closeScore + overScore
    }

    /**
     * 计算 close score 分数，依赖 PersonPetInfo 里的 left、top、right、bottom
     */
    @JvmStatic
    private fun calcCloseScore(peronPetInfoList: List<PersonPetInfo>): Float {
        var bigLeft = Int.MAX_VALUE
        var bigTop = Int.MAX_VALUE
        var bigRight = 0
        var bigBottom = 0
        peronPetInfoList.forEach { info ->
            info.left?.let { left -> bigLeft = min(left, bigLeft) }
            info.top?.let { top -> bigTop = min(top, bigTop) }
            info.right?.let { right -> bigRight = max(right, bigRight) }
            info.bottom?.let { bottom -> bigBottom = max(bottom, bigBottom) }
        }
        val width = bigRight - bigLeft
        val height = bigBottom - bigTop
        val allArea = Array(width) { IntArray(height) }
        allArea.forEach { for (i in 0 until height) it[i] = 0 }
        peronPetInfoList.forEach { info ->
            val left = ((info.left ?: 0) - bigLeft)
            val right = ((info.right ?: 0) - bigLeft)
            val top = ((info.top ?: 0) - bigTop)
            val bottom = ((info.bottom ?: 0) - bigTop)
            for (x in left until right) {
                for (y in top until bottom) {
                    allArea[x][y] = 1
                }
            }
        }
        var sumArea = 0
        allArea.forEach { for (i in 0 until height) sumArea += it[i] }
        return (sumArea / (width * height).toFloat())
    }

    /**
     * 计算 over score 分数，依赖 PersonPetInfo 里的 left、top、right、bottom、thumbWidth、thumbHeight
     */
    @JvmStatic
    private fun calcOverScore(peronPetInfoList: List<PersonPetInfo>): Int {
        var bigLeft = Int.MAX_VALUE
        var bigTop = Int.MAX_VALUE
        var bigRight = 0
        var bigBottom = 0
        var thumbWidth = 0
        var thumbHeight = 0
        peronPetInfoList.forEach { info ->
            info.thumbWidth?.let { thumbWidth = it }
            info.thumbHeight?.let { thumbHeight = it }
            info.left?.let { left -> bigLeft = min(left, bigLeft) }
            info.top?.let { top -> bigTop = min(top, bigTop) }
            info.right?.let { right -> bigRight = max(right, bigRight) }
            info.bottom?.let { bottom -> bigBottom = max(bottom, bigBottom) }
        }
        val width = bigRight - bigLeft
        val height = bigBottom - bigTop
        val xCenter = (bigRight + bigLeft) / HALF_OF
        val yCenter = (bigBottom + bigTop) / HALF_OF
        val newSize = max(width, height) * SIZE_RATE
        val newBigBox = RectF(
            (xCenter - newSize / HALF_OF),
            (yCenter - newSize / HALF_OF),
            (xCenter + newSize / HALF_OF),
            (yCenter + newSize / HALF_OF)
        )
        return when {
            (newBigBox.left < 0)
                    || (newBigBox.top < 0)
                    || (newBigBox.right > thumbWidth)
                    || (newBigBox.bottom > thumbHeight) -> OVER_SCORE

            else -> 0
        }
    }
}