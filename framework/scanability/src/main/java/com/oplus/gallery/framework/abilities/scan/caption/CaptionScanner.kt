/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  CaptionScanner.kt
 * * Description:  caption扫描
 * * Version: 1.0
 * * Date : 2025/02/19
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/02/19     1.0        create
 ************************************************************/
package com.oplus.gallery.framework.abilities.scan.caption

import android.content.Context
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.ABILITY_IS_SUPPORT_CAPTION_EMBEDDING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.CAPTION_FIRST_SCAN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.CAPTION_SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.CAPTION_SCAN_COUNT_ALL
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper.trackScanResult
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper.trackStopReasonOnStart
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.isAllowStartScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.refreshScanRecordingIfNecessary
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.abilities.search.caption.CaptionCode.CODE_CAPTION_ERROR
import com.oplus.gallery.framework.abilities.search.caption.CaptionCode.CODE_INTERRUPT
import com.oplus.gallery.framework.abilities.search.caption.CaptionCode.CODE_NEED_EMBEDDING
import com.oplus.gallery.framework.abilities.search.caption.CaptionCode.CODE_SUCCESS
import com.oplus.gallery.framework.abilities.search.caption.CaptionImageInfo
import com.oplus.gallery.framework.abilities.search.caption.CaptionScanProviderHelper
import com.oplus.gallery.framework.abilities.search.caption.ICaptionAbility
import com.oplus.gallery.framework.abilities.search.caption.ICaptionAbility.Companion.MAX_GALLERY_ID
import com.oplus.gallery.framework.abilities.search.embedding.caption.ICaptionEmbeddingAbility
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.scheduler.BatchProcess

/**
 * caption扫描
 * 作用：对图片进行语义描述总结
 * 时机：在后台扫描任务启动时，进行扫描，该扫描任务比较耗时，排在最后
 * 功能：1.首先对图片内容进行caption扫描
 * 2.caption扫描后得到文本内容再调用ocrEmbedding的原子能力进行内容的向量化
 * 3.caption扫描 + embedding 完成后将数据存入scan_caption表
 */
class CaptionScanner(val context: Context) : GalleryScan(context) {
    /**
     * 24小时内已经扫描的数量
     */
    private var scannedCountIn24hours = 0

    /**
     * 本次扫描总共扫描的数量
     */
    private var scannedTotalCount = 0

    /**
     * 需要扫描的列表，以前未扫描
     */
    private var needScanList: MutableList<CaptionImageInfo> = mutableListOf()

    /**
     * processCaptions接口的执行总时间
     */
    private var processCaptionsTotalTime = 0L

    /**
     * 因为embedding模型更新需要重新进行 embedding 的列表，
     */
    private var needEmbeddingList: MutableList<CaptionImageInfo> = mutableListOf()

    /**
     * 调用OcrEmbeddingAbility对象执行processImageText接口的总时间
     */
    private var processEmbeddingTotalTime = 0L

    /**
     * 扫描类型
     */
    override fun getScanType(): Int {
        return CAPTION_SCAN
    }

    /**
     * 扫描类型的名字
     */
    override fun getSceneName(): String {
        return SCENE_NAME
    }


    override fun onScan(
        triggerType: Int,
        config: ScanConfig?
    ) {
        GLog.d(TAG, DL) { "onScan, start scan caption triggerType:$triggerType" }
        super.onScan(triggerType, config)
        if (isAllowStartScan(context, needCharging = true, canScanWhenGalleryRunOnTop = false) && isInterrupt.not()) {
            scanData(triggerType)
        }
        GLog.d(TAG, DL) { "onScan, caption scan end" }
    }

    /**
     * 需要在充电状态才能扫描
     */
    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }

    /**
     * caption扫描
     *
     * 1.标记开始，记录相关扫描数据
     * 2.初始化ICaptionAbility、IOcrEmbeddingAbility
     * 3.获取需要扫描、需要重新扫描、需要删除的数据
     * 4.执行扫描
     * 5.更新、删除数据
     *
     * scannedTotalCount, processCaptionsTotalTime, mScanImageCount仅记录caption扫描的数据，不记录embedding数据
     */
    private fun scanData(triggerType: Int) {
        updateLocalMediaTableId()
        markConditionsOnStartScan()
        recordScanDataIn24H()
        context.getAppAbility<ICaptionAbility>()?.use {
            try {
                if (it.isDownloading) {
                    GLog.w(TAG, DL) { "scanData, caption is downloading, return" }
                    updateAbilityIsSupported(false)
                    trackStopReasonOnStart(mContext, CAPTION_SCAN_INIT_FAILED_MODAL_DOWNLOADING, triggerType)
                    return
                }
                if (it.init().not()) {
                    GLog.e(TAG, DL) { "scanData, init caption ability fail, return" }
                    updateAbilityIsSupported(false)
                    trackStopReasonOnStart(mContext, CAPTION_SCAN_INIT_FAILED_ABILITY_INIT_FAILED, triggerType)
                    return
                }
                if (initEmbeddingAbilityImpl(it).not()) {
                    updateAbilityIsSupported(false)
                    GLog.e(TAG, DL) { "scanData, init embedding ability fail, return" }
                    trackStopReasonOnStart(mContext, CAPTION_SCAN_INIT_FAILED_EMBEDDING_INIT_FAILED, triggerType)
                    return
                }
                updateAbilityIsSupported(true)
                val scanStartTime = System.currentTimeMillis()
                // embedding模型更新，需要先删除旧模型生成的向量数据库和更新相册数据库
                if (it.embeddingAbilityImpl?.hasOldModelData() == true) {
                    val removeStatus = it.embeddingAbilityImpl?.removeOldModelData()
                    if (removeStatus == true) {
                        CaptionScanProviderHelper.updateCaptionScanState()
                    }
                    GLog.d(TAG, DL) { "scanData, model change need clear db. removeStatus: $removeStatus" }
                }
                if (isNeedCaptionScan(it).not()) {
                    GLog.w(TAG, DL) { "scanData, no data need caption scan" }
                    updateLastScanTime()
                    trackStopReasonOnStart(mContext, CAPTION_SCAN_EMPTY, triggerType)
                    return
                }
                initScanParam()
                doScan(it, needScanList, isAdd = true)
                doScan(it, needEmbeddingList, isAdd = false)
                val needDeleteList = getNeedDeleteList()
                handleDelete(it, needDeleteList)
                updateLastScanTime()
                val totalScanCostTime = System.currentTimeMillis() - scanStartTime
                if (scannedTotalCount > 0) {
                    GLog.w(TAG, DL) { "scanData, captionCaption cost time total: ${totalScanCostTime}ms, scannedTotalCount:$scannedTotalCount, " +
                                "perItemScanTime:${totalScanCostTime / scannedTotalCount.toFloat()}ms, " +
                                "processCaptionsTotalTime:$processCaptionsTotalTime, " +
                                "perItemProcessCaptionsTime:${processCaptionsTotalTime / scannedTotalCount.toFloat()}ms" }
                }
                trackCaptionScan(null, false)
            } finally {
                GLog.d(TAG, DL) { "scanData, finally, release and set caption ability is scanning to false" }
                it.release()
            }
        }
    }

    /**
     * 更新caption里的localMediaTableId
     * localMediaTableId对应主表的主键，搬家时无法找到数据在新手机的主键，在扫描时更新
     */
    private fun updateLocalMediaTableId() {
        val startTime = System.currentTimeMillis()
        val size = CaptionScanProviderHelper.getLocalMediaTableIdEqualZeroSize()
        if (size <= 0) {
            GLog.d(TAG, DL) { "updateLocalMediaTableId, no data need to update local media table id" }
            return
        }
        var limitStart = 0
        while (limitStart < size) {
            val dataList = CaptionScanProviderHelper.getLocalMediaTableIdEqualZeroData(BatchProcess.PAGE_SIZE_999.toString())
            val newIdMap = CaptionScanProviderHelper.getNewLocalMediaTableId(dataList)
            CaptionScanProviderHelper.updateLocalMediaTableId(newIdMap)
            limitStart += dataList.size
        }
        GLog.d(TAG, DL) { "updateLocalMediaTableId, size:$size, cost time:${GLog.getTime(startTime)}" }
    }

    /**
     * 判断是否是24H内的第一次扫描，并获取24H小时内的扫描数据
     */
    private fun recordScanDataIn24H() {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            mIsFirstScan = it.getBooleanConfig(CAPTION_FIRST_SCAN, true) ?: true
            if (mIsFirstScan) {
                it.setBooleanConfig(CAPTION_FIRST_SCAN, false)
            }
            scannedCountIn24hours = it.getIntConfig(CAPTION_SCAN_COUNT_24H, 0) ?: 0
        }
    }

    /**
     * 更新能力是否可用
     */
    private fun updateAbilityIsSupported(isSupported: Boolean) {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            it.setBooleanConfig(ABILITY_IS_SUPPORT_CAPTION_EMBEDDING, isSupported)
        }
    }

    /**
     * 获取IOcrEmbeddingAbility实例对象并初始化
     * @return true:初始化成功 false:初始化失败
     */
    private fun initEmbeddingAbilityImpl(ability: ICaptionAbility): Boolean {
        ability.embeddingAbilityImpl = context.getAppAbility<ICaptionEmbeddingAbility>()
        if (ability.embeddingAbilityImpl == null) {
            GLog.e(TAG, DL) { "initEmbeddingAbilityImpl, embeddingAbility is null, return" }
            return false
        }
        if (ability.embeddingAbilityImpl?.isDownloading == true) {
            GLog.e(TAG, DL) { "initEmbeddingAbilityImpl, embeddingAbility is downloading, return" }
            return false
        }
        if (ability.embeddingAbilityImpl?.init() == false) {
            GLog.e(TAG, DL) { "initEmbeddingAbilityImpl, init embedding engine fail, return" }
            return false
        }
        val majorVersion = ability.embeddingAbilityImpl?.getMajorVersion() ?: 0
        if (majorVersion < MIN_MAJOR_VERSION) {
            GLog.e(TAG, DL) { "initEmbeddingAbilityImpl, embedding version:$majorVersion is not match, return" }
            return false
        }
        ability.embeddingVersion = ability.embeddingAbilityImpl?.currentModelVersion ?: ""
        return true
    }

    /**
     * 更新扫描列表信息
     * 1.获取需要扫描的图片列表
     * 2.获取因为embedding模型变化需要重新扫描的图片列表，即需要重新生成向量，不需要重新扫描caption内容和更新scan_caption表
     */
    private fun isNeedCaptionScan(ability: ICaptionAbility): Boolean {
        needScanList = CaptionScanProviderHelper.getNeedScanList().toMutableList()
        needEmbeddingList = CaptionScanProviderHelper.getNeedEmbeddingData(ability.embeddingVersion)
        GLog.d(TAG, DL) { "isNeedCaptionScan, needScanList = ${needScanList.size},  needEmbeddingList = ${needEmbeddingList.size}" }
        return (needScanList.isNotEmpty() || needEmbeddingList.isNotEmpty())
    }

    /**
     * 更新数据库
     */
    private fun handleUpdateDb(
        needUpdateDbList: MutableList<CaptionImageInfo>,
        ability: ICaptionAbility?
    ) {
        CaptionScanProviderHelper.updateCaptionScanData(needUpdateDbList, ability)
        bindSmallCore()
    }

    /**
     * 初始化记录扫描的数据
     */
    private fun initScanParam() {
        scannedTotalCount = 0
        processCaptionsTotalTime = 0
        processEmbeddingTotalTime = 0
    }

    /**
     * 执行扫描（caption + embedding）
     *
     * 1、getOneScanCountList()，获取小于等于ONE_SCAN_COUNT个item
     * 2、使用这若干个item调用convertImageToCaptionAndVector()，生成caption内容 + embedding
     * 3、recordScanCount()，记录扫描数量信息
     * 4、调用底层flushData()，如果返回true则insertOcrEmbeddingScanData()，将扫描成功的item新增到数据库
     * 5、休眠SLEEP_TIME_EACH_BACH毫秒
     *
     * @param ability caption扫描的原子能力
     * @param scanList 需要执行扫描的列表
     * @param isAdd 是否为新增扫描的图片即第一次扫描
     * @return caption扫描的数据结构（CaptionImageInfo数据结构中未更新caption和embedding信息）
     */
    private fun doScan(
        ability: ICaptionAbility,
        scanList: MutableList<CaptionImageInfo>,
        isAdd: Boolean
    ) {
        while ((scanList.size > 0) && isAllowContinueScan(scannedCountIn24hours, GalleryScanMonitor.CAPTION_SCAN_COUNT_24_MAX, true)
            && isInterrupt.not()) {
            val startTime = System.currentTimeMillis()
            val oneScanCountList = getOneScanCountList(scanList)
            val convertResultList = convertImageToCaption(ability, oneScanCountList, isAdd)
            val flushResult = ability.embeddingAbilityImpl?.flushData() ?: false
            GLog.d(TAG, DL) { "doScan, flushResult = $flushResult, convertResultList = ${convertResultList.map { it.mScanState }}" }
            if (flushResult) {
                if (isAdd) {
                    CaptionScanProviderHelper.insertCaptionScanData(convertResultList, ability)
                } else {
                    handleUpdateDb(convertResultList, ability)
                }
            }
            if (isAdd) {
                recordScanCount(convertResultList.size)
                scannedTotalCount += convertResultList.size
                val batchTotalCount = convertResultList.size
                if (batchTotalCount > 0) {
                    val batchTotalTime = System.currentTimeMillis() - startTime
                    GLog.w(TAG, DL) { "doScan, captionScan batchTotalTime:$batchTotalTime, batchTotalCount:$batchTotalCount, " +
                            "scannedTotalCount:$scannedTotalCount, " + "perItemTime:${batchTotalTime / batchTotalCount.toFloat()}ms" }
                }
            }
            if (isInterrupt.not()) {
                sleep(SLEEP_TIME_EACH_BACH)
            }
            bindSmallCore()
        }
    }

    /**
     * 获取小于等于ONE_SCAN_COUNT的list
     */
    private fun getOneScanCountList(
        allList: MutableList<CaptionImageInfo>
    ): MutableList<CaptionImageInfo> {
        val subAddSize = if (allList.size >= ONE_SCAN_COUNT) {
            ONE_SCAN_COUNT
        } else {
            allList.size
        }
        val subList = mutableListOf<CaptionImageInfo>()
        for (i in 0 until subAddSize) {
            subList.add(allList.removeAt(0))
        }
        return subList
    }

    /**
     * 图片做caption扫描——每小轮先做caption扫描，再做embedding向量化
     *
     * 调用过程中被中断，比如亮屏、高温，状态码赋为 CODE_INTERRUPT
     *
     * @param ability caption扫描的原子能力
     * @param subList 需要执行扫描的列表
     * @param isAdd 是否新增，新增需要caption扫描（如果只是因为embedding模型变化则不需要caption扫描，仅做embedding）
     * @return caption扫描的数据结构（CaptionImageInfo数据结构中未更新caption和embedding信息）
     */
    private fun convertImageToCaption(
        ability: ICaptionAbility,
        subList: MutableList<CaptionImageInfo>,
        isAdd: Boolean
    ): MutableList<CaptionImageInfo> {
        GLog.d(TAG, DL) { "convertImageToCaption, size = ${subList.size}, isAdd = $isAdd" }
        val result = mutableListOf<CaptionImageInfo>()
        if (isAdd) {
            subList.forEach { info ->
                if (isInterrupt.not()) {
                    doProcessCaption(ability, info, result)
                } else {
                    info.mScanState = CODE_INTERRUPT
                    result.add(info)
                    return result
                }
            }
        }
        return convertImageToVector(ability, subList)
    }

    /**
     * 执行caption文本提取
     */
    private fun doProcessCaption(ability: ICaptionAbility, info: CaptionImageInfo, result: MutableList<CaptionImageInfo>) {
        val processCaptionsTime = System.currentTimeMillis()
        val content = ability.processCaptions(info.mFilePath)
        if (content.code == 0) {
            for (index in content.data.indices) {
                when (index) {
                    CONTENT_INDEX_0 -> info.captionContent0 = content.data[CONTENT_INDEX_0]
                    CONTENT_INDEX_1 -> info.captionContent1 = content.data[CONTENT_INDEX_1]
                    CONTENT_INDEX_2 -> info.captionContent2 = content.data[CONTENT_INDEX_2]
                    CONTENT_INDEX_3 -> info.captionContent3 = content.data[CONTENT_INDEX_3]
                }
            }
            generateEmbeddingContent(info)
            if (GProperty.DEBUG_SCAN) {
                GLog.d(TAG, DL) { "doProcessCaption, processCaptions cost time:${GLog.getTime(processCaptionsTime)}, " +
                        "embeddingContent0:${info.embeddingContent0}, embeddingContent1:${info.embeddingContent1}, " +
                        "embeddingContent2:${info.embeddingContent2}, embeddingContent3:${info.embeddingContent3}" }
            }
            info.mScanState = CODE_NEED_EMBEDDING
            result.add(info)
            mScanImageCount++
            processCaptionsTotalTime += GLog.getTime(processCaptionsTime)
        } else {
            info.mScanState = CODE_CAPTION_ERROR
            GLog.e(TAG, DL) { "doProcessCaption, processCaptions error, code:${content.code}, msg:${content.msg}" }
        }
        sleepInScan(processCaptionsTime)
    }

    /**
     * 拼接节日、地点、poi等
     */
    private fun generateEmbeddingContent(info: CaptionImageInfo) {
        repeat(MAX_CAPTION_CONTENT_SIZE) {
            info.setSpliceContent(it, info.getCaptionContent(it))
        }
        spliceFestival(info)
        spliceLocation(info)
        splicePoiName(info)
    }

    /**
     * 拼接节日
     */
    private fun spliceFestival(info: CaptionImageInfo) {
        if (info.festival != TextUtil.EMPTY_STRING) {
            val festival = "$CAPTION_EMBEDDING_PREFIX_DATE${info.festival}"
            info.setSpliceContent(festival)
        }
    }

    /**
     * 拼接地址
     */
    private fun spliceLocation(info: CaptionImageInfo) {
        if (info.location != TextUtil.EMPTY_STRING) {
            val text = if (info.festival.isNotBlank()) {
                TextUtil.LEFT_SLASH + CAPTION_EMBEDDING_PREFIX_LOCATION + info.location
            } else {
                CAPTION_EMBEDDING_PREFIX_LOCATION + info.location
            }
            info.setSpliceContent(text)
        }
    }

    /**
     * 拼接poi
     */
    private fun splicePoiName(info: CaptionImageInfo) {
        if (info.poiName != TextUtil.EMPTY_STRING) {
            // 已经拼过地点
            if (info.location.isNotBlank()) {
                info.setSpliceContent(TextUtil.LEFT_SLASH + info.poiName)
            } else {
                val text = if (info.festival.isNotBlank()) {
                    TextUtil.LEFT_SLASH + CAPTION_EMBEDDING_PREFIX_LOCATION + info.poiName
                } else {
                    CAPTION_EMBEDDING_PREFIX_LOCATION + info.poiName
                }
                info.setSpliceContent(text)
            }
        }
    }

    /**
     * 休眠——本次扫描时间 + 休眠时间 = SINGLE_SCAN_TOTAL_TIME
     */
    private fun sleepInScan(processCaptionsTime: Long) {
        if (!GalleryScanUtils.isTestMode()) {
            val singleSleepTime = (SINGLE_SCAN_TOTAL_TIME - (System.currentTimeMillis() - processCaptionsTime)).toInt()
            if (GProperty.DEBUG_SCAN) {
                GLog.d(TAG, DL) { "sleepInScan, single sleep time:$singleSleepTime" }
            }
            if (singleSleepTime > 0) {
                sleep(singleSleepTime)
            }
        }
    }

    /**
     * 经过caption扫描的图片做embedding
     *
     * 1、调用embeddingAbilityImpl.processImageText将caption扫描内容做embedding
     * 2、记录扫描结果resultList并返回，里面包含扫描状态码
     *
     * 扫描状态码：
     * caption + embedding成功，状态码赋为CODE_SUCCESS
     * embedding失败，说明底层发生错误，状态码赋为CODE_NEED_EMBEDDING，说明需要重新做embedding(caption只返回结果无状态)
     * 调用过程中被中断，比如亮屏、高温，状态码赋为 CODE_NEED_EMBEDDING
     *
     * @param ability caption扫描的原子能力
     * @param subList 需要执行扫描的列表
     */
    private fun convertImageToVector(
        ability: ICaptionAbility,
        subList: MutableList<CaptionImageInfo>
    ): MutableList<CaptionImageInfo> {
        GLog.d(TAG, DL) { "convertImageToVector, size = ${subList.size}" }
        val startTime = System.currentTimeMillis()
        val result = mutableListOf<CaptionImageInfo>()
        subList.forEach { info ->
            if (info.mScanState != CODE_CAPTION_ERROR) {
                if (isInterrupt.not()) {
                    val processCaptionsTime = System.currentTimeMillis()
                    generateEmbeddingContent(info)
                    if (GProperty.DEBUG_SCAN) {
                        GLog.d(TAG, DL) { "convertImageToVector, processCaptions cost time:${GLog.getTime(processCaptionsTime)}, " +
                                "embeddingContent0:${info.embeddingContent0}, embeddingContent1:${info.embeddingContent2}, " +
                                "embeddingContent2:${info.embeddingContent2}, embeddingContent3:${info.embeddingContent3}" }
                    }
                    if (doProcessImageText(ability, info)) {
                        info.mScanState = CODE_SUCCESS
                    } else {
                        info.mScanState = CODE_NEED_EMBEDDING
                    }
                    if (GProperty.DEBUG_SCAN) {
                        GLog.d(TAG, DL) { "convertImageToVector, processImageText cost time:${GLog.getTime(processCaptionsTime)}" }
                    }
                } else {
                    // 调用到此处说已经完成caption扫描，所以此时打断仅代表下次仅需embedding
                    info.mScanState = CODE_NEED_EMBEDDING
                }
            }
            result.add(info)
        }
        GLog.w(TAG, DL) { "convertImageToVector, size:${result.size}, cost time:${GLog.getTime(startTime)}" }
        return result
    }

    /**
     * 编码 Caption 原文内容
     */
    private fun encodeOriginEmbeddingId(galleryId: Long, index: Int): Long {
        return galleryId + (MAX_GALLERY_ID * ((index * AppConstants.Number.NUMBER_2) + AppConstants.Number.NUMBER_1))
    }

    /**
     * 编码 Caption 拼接后文字的内容
     */
    private fun encodeEmbeddingId(galleryId: Long, index: Int): Long {
        return galleryId + (MAX_GALLERY_ID * ((index * AppConstants.Number.NUMBER_2) + AppConstants.Number.NUMBER_2))
    }

    /**
     * 对 Caption 原文和拼接后的内容做 Embedding
     */
    private fun embeddingContent(embeddingAbility: ICaptionEmbeddingAbility, info: CaptionImageInfo, index: Int): Boolean {
        return embeddingAbility.processImageText(encodeOriginEmbeddingId(info.mGalleryId, index), info.getCaptionContent(index))
                && embeddingAbility.processImageText(encodeEmbeddingId(info.mGalleryId, index), info.getSpliceContent(index))
    }

    /**
     * caption扫描后的内容做embedding，所有的ProcessImageText返回true，否则false
     */
    private fun doProcessImageText(ability: ICaptionAbility, info: CaptionImageInfo): Boolean {
        val embeddingImpl = ability.embeddingAbilityImpl ?: return false.also {
            GLog.w(TAG, DL, "doProcessImageText. embeddingAbilityImpl is null.")
        }
        for (index in 0 until MAX_CAPTION_CONTENT_SIZE) {
            if (info.getCaptionContent(index).isBlank() || info.getSpliceContent(index).isBlank()) {
                continue
            }
            if (embeddingContent(embeddingImpl, info, index).not()) {
                GLog.w(TAG, DL, "doProcessImageText. embeddingContent fail. index: $index")
                return false
            }
        }
        return true
    }

    /**
     * 记录 SCAN_COUNT_ALL 和 SCAN_COUNT_24H 的数据
     */
    private fun recordScanCount(count: Int) {
        addScanCountIn24H(count)
        recordScanInfoIfNeed(count)
    }

    /**
     * 记录24小时内扫描数量
     * caption只在充电场景下扫描，记录充电场景下扫描数量，一天有限额同ocr
     * 测试模式下不记录扫描数量
     */
    private fun addScanCountIn24H(count: Int) {
        if (!GalleryScanUtils.isTestMode()) {
            refreshScanRecordingIfNecessary(context)
            (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
                scannedCountIn24hours = (it.getIntConfig(CAPTION_SCAN_COUNT_24H, 0) ?: 0) + count
                it.setIntConfig(CAPTION_SCAN_COUNT_24H, scannedCountIn24hours)
            }
        }
    }

    /**
     * 记录扫描数量
     */
    private fun recordScanInfoIfNeed(count: Int) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return
        }
        (context.applicationContext as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            val scanCountAll = (it.getIntConfig(CAPTION_SCAN_COUNT_ALL, 0) ?: 0) + count
            it.setIntConfig(CAPTION_SCAN_COUNT_ALL, scanCountAll)
            GalleryScanUtils.recordInfo(context, TAG, scannedCountIn24hours.toLong(), scanCountAll.toLong())
        }
    }

    /**
     * 获取需要删除的图片列表
     */
    private fun getNeedDeleteList(): MutableList<CaptionImageInfo> {
        val needDeleteList = CaptionScanProviderHelper.getNeedDeleteData()
        GLog.d(TAG, DL) { "getNeedDeleteList, needDeleteList.size = ${needDeleteList.size}" }
        return needDeleteList.toMutableList()
    }

    /**
     * 处理删除caption扫描数据
     */
    private fun handleDelete(ability: ICaptionAbility, list: MutableList<CaptionImageInfo>) {
        if (list.isEmpty()) {
            return
        }
        val successDeleteList = ability.deleteEmbeddingDataByLocalMediaTableIds(list)
        GLog.d(TAG, DL) { "handleDelete, successDeleteList.size = ${successDeleteList.size}" }
        CaptionScanProviderHelper.deleteCaptionScanData(successDeleteList)
        bindSmallCore()
    }

    /**
     * 扫描埋点数据
     */
    private fun trackCaptionScan(event: MonitorEvent?, isMonitor: Boolean) {
        val size = CaptionScanProviderHelper.getScanSize()
        GLog.d(TAG, DL) { "scanned = ${size.first} unScan = ${size.second}" }
        val reasonType = getReasonType(false, scannedCountIn24hours, GalleryScanMonitor.CAPTION_SCAN_COUNT_24_MAX)
        val endNativeHeapSize = getEndNativeHeapSize(event)
        val additionalMap: MutableMap<String, String> = HashMap()
        additionalMap[CAPTION_SCAN_COUNT] = scannedTotalCount.toString()
        additionalMap[IS_MONITOR] = isMonitor.toString()
        additionalMap[CAPTION_SCANNED_SIZE] = size.first.toString()
        additionalMap[CAPTION_UN_SCAN_SIZE] = size.second.toString()
        val scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap)
        trackScanResult(scanTrackInfo)
    }

    override fun onMonitorRecord(event: MonitorEvent, reachThreshold: Boolean) {
        super.onMonitorRecord(event, reachThreshold)
        trackCaptionScan(event, true)
    }

    companion object {
        const val TAG = "${ScanConst.TAG}.CaptionScanner"

        /**
         * 埋点扫描数量
         */
        private const val CAPTION_SCAN_COUNT = "caption_scan_count"

        /**
         * 扫描类型的名字
         */
        private const val SCENE_NAME = "CaptionScanner"

        /**
         * 扫描初始化失败-模型下载中
         */
        private const val CAPTION_SCAN_INIT_FAILED_MODAL_DOWNLOADING = SCENE_NAME + "_ModalDownloading"

        /**
         * 扫描初始化失败-能力初始化失败
         */
        private const val CAPTION_SCAN_INIT_FAILED_ABILITY_INIT_FAILED = SCENE_NAME + "_AbilityInitFailed"


        /**
         * 扫描初始化失败-Embedding初始化失败
         */
        private const val CAPTION_SCAN_INIT_FAILED_EMBEDDING_INIT_FAILED = SCENE_NAME + "_EmbeddingInitFailed"

        /**
         * 无需扫描
         */
        private const val CAPTION_SCAN_EMPTY = SCENE_NAME + "_Empty"

        /**
         * 用户已扫描数量
         */
        private const val CAPTION_SCANNED_SIZE = "caption_scanned_size"

        /**
         * 用户未扫描数量
         */
        private const val CAPTION_UN_SCAN_SIZE = "caption_un_scan_size"

        /**
         * caption功能所需的embedding最低主版本号
         */
        private const val MIN_MAJOR_VERSION = 103

        /**
         * 单次扫描的数量 20张
         */
        private const val ONE_SCAN_COUNT = 1

        /**
         * 单张扫描加睡眠总时间
         */
        private const val SINGLE_SCAN_TOTAL_TIME = 12 * 1000L

        /**
         * 每次扫描间隔200毫秒
         */
        private const val SLEEP_TIME_EACH_BACH = 200

        /**
         * caption content 数量
         */
        private const val MAX_CAPTION_CONTENT_SIZE = 4

        /**
         * content 内容编号
         */
        private const val CONTENT_INDEX_0 = 0
        private const val CONTENT_INDEX_1 = 1
        private const val CONTENT_INDEX_2 = 2
        private const val CONTENT_INDEX_3 = 3

        /**
         * Caption 做 Embedding 的文件拼接时间的前缀
         */
        private const val CAPTION_EMBEDDING_PREFIX_DATE = "拍摄时间："
        /**
         * Caption 做 Embedding 的文件拼接地点的前缀
         */
        private const val CAPTION_EMBEDDING_PREFIX_LOCATION = "拍摄地点："
    }
}