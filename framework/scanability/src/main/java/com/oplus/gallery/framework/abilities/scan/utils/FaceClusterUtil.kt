/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceClusterUtil
 ** Description:
 ** Version: 1.0
 ** Date: 2025-04-22
 ** Author: liuyanping@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** liuyanping@Apps.Gallery3D    2025-04-22     1.0
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.utils

import android.graphics.Rect
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.VALID_PERCENTAGE
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import kotlin.math.abs

object FaceClusterUtil {
    private const val TAG = "FaceClusterUtil"

    /**
     * 两个集合找同人脸位置，覆盖数据
     * @param queryFaceInfoList List<CvFaceCluster> 数据库中指定图片中，所有图片信息
     * @param newCvFaceClusters List<CvFaceCluster> 图片新扫描出来的数据信息
     */
    @JvmStatic
    fun compareList(queryFaceInfoList: List<CvFaceCluster>, newCvFaceClusters: List<CvFaceCluster>): List<CvFaceCluster> {
        val result = mutableListOf<CvFaceCluster>()
        var targetCvFaceCluster: CvFaceCluster?
        var calculateOverlapArea: Int
        // 1、遍历新扫描大图的信息
        newCvFaceClusters.forEach { newCVFaceCluster ->
            targetCvFaceCluster = null
            calculateOverlapArea = 0
            // 2、查询出来的数据集合，找newCVFaceCluster重叠区域最大对应的图片
            queryFaceInfoList.forEach { queryCVFaceCluster ->
                // 3、比较图片路径相同，并且新图的大小大于老图
                if ((newCVFaceCluster.data?.equals(queryCVFaceCluster.data) == true)
                    && (newCVFaceCluster.thumbWidth > queryCVFaceCluster.thumbWidth)
                ) {
                    // 缩放比例
                    val scale = newCVFaceCluster.thumbWidth * 1.0f / queryCVFaceCluster.thumbWidth
                    // 4、查询出来的rect，按比例缩放后的rect区别
                    val queryRect = Rect(
                        (queryCVFaceCluster.rectLeft * scale).toInt(),
                        (queryCVFaceCluster.rectTop * scale).toInt(),
                        (queryCVFaceCluster.rectRight * scale).toInt(),
                        (queryCVFaceCluster.rectBottom * scale).toInt()
                    )
                    // 新扫描的大图的Rect
                    val newRect = newCVFaceCluster.rect
                    // 之前的算法用的是不带额头的rect，所以对比的时候新扫描的也要用不带额头的去和旧的对比
                    val newRectWithoutForehead = newCVFaceCluster.rectWithoutForehead
                    // 5 计算重叠区域，找到重叠最多的rect
                    val tempArea = calculateOverlapArea(newRect, queryRect).coerceAtLeast(calculateOverlapArea(newRectWithoutForehead, queryRect))
                    if (tempArea > calculateOverlapArea) {
                        calculateOverlapArea = tempArea
                        targetCvFaceCluster = queryCVFaceCluster
                    }
                }
            }
            targetCvFaceCluster?.let {
                GLog.d(TAG, LogFlag.DL) {
                    "compareList, updateCvFaceCluster calculateOverlapArea:$calculateOverlapArea"
                }
                // 6、找到图片信息更新到数据类中，等待更新值数据库
                result.add(updateCvFaceCluster(it, newCVFaceCluster))
            }
        }
        return result
    }

    /**
     * 计算两个矩形的重叠区域面积
     * @param rect1 Rect 矩形1
     * @param rect2 Rect 矩形2
     * @return Int 重叠区域面积
     */
    @JvmStatic
    private fun calculateOverlapArea(rect1: Rect, rect2: Rect): Int {
        val overlap = Rect()
        if (overlap.setIntersect(rect1, rect2)) {
            // 重叠区域面积
            val result = overlap.width() * overlap.height()
            // 高清图面积
            val newRect = rect1.width() * rect1.height()
            // 面积差在30%范围内，才认为是同一张人脸图
            if (abs(result - newRect) * 1f / newRect < VALID_PERCENTAGE) {
                return result
            }
        }
        return 0
    }

    /**
     * 新扫描的信息保存到数据库中
     * @param oldCvFaceCluster CvFaceCluster 旧的已保存的信息
     * @param newCvFaceCluster CvFaceCluster 新扫描信息
     */
    @JvmStatic
    fun updateCvFaceCluster(oldCvFaceCluster: CvFaceCluster, newCvFaceCluster: CvFaceCluster): CvFaceCluster {
        newCvFaceCluster.imageFeature?.let {
            oldCvFaceCluster.setFaceInfo(it)
            oldCvFaceCluster.modelVersion = newCvFaceCluster.modelVersion
            oldCvFaceCluster.bestScore = newCvFaceCluster.bestScore
            oldCvFaceCluster.happyScore = newCvFaceCluster.happyScore
            oldCvFaceCluster.setFaceAttributeInfo(it.faceAttributeInfo)
        }
        return oldCvFaceCluster
    }
}