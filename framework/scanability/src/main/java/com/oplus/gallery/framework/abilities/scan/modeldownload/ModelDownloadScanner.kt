/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ModelDownloadScanner.kt
 ** Description:后台SDK扫描
 ** Version: 1.0
 ** Date: 2022/3/21
 ** Author: youpeng@Apps.Gallery3D
 ** TAG: ModelDownloadScanner
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                     <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** youpeng@Apps.Gallery3D        2022/03/21        1.0         first created
 ** dingyong@Apps.Gallery3D       2022/10/27        2.0         从HighlightScanner重命名为ModelDownloadScanner
 *  <EMAIL>         2023/08/15        3.0         重构下载流程，使得代码可以功能复用
 *  <EMAIL>           2024/01/25        4.0         添加人脸so下载
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import com.oplus.gallery.basebiz.helper.RemoteModelInfoManager
import com.oplus.gallery.foundation.ai.TravelCoverSelectApi
import com.oplus.gallery.foundation.ai.PetModelApi
import com.oplus.gallery.foundation.ai.StudyClassifyApi
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_UNIT_CONFIG_AVAILABLE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.CLIP_AND_TEXT_EMBEDDING_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.download.IDownloadAbility
import com.oplus.gallery.framework.abilities.download.RemoteModelInfo
import com.oplus.gallery.framework.abilities.download.template.ModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.face.model.FaceModelConfig
import com.oplus.gallery.framework.abilities.scan.face.model.FaceModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.template.EnglishLabelGraphModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.LabelGraphModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.MultiModalDictVulgarismsLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.PoiModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.EnglishLabelGraphModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.LabelGraphModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.MultiModalDictVulgarismsConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.PoiModelConfig
import com.oplus.gallery.framework.abilities.search.multimodal.IMultiModalAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor

/**
 * 从服务端后台下载模型至本地，当前有
 * （1）知识图谱
 * （2）英文知识图谱
 * （3）多模态
 * （4）多模态不雅词词典
 */
class ModelDownloadScanner(val context: Context) : GalleryScan(context) {

    private val allowContinueAction: () -> Boolean = {
        isCancel.not() && isInterrupt.not() && GalleryScanMonitor.isAllowContinueScan(context, false, mCanScanWhenGalleryRunOnTop)
    }

    override fun getScanType() = MODEL_DOWNLOAD_SCAN

    override fun getSceneName(): String = SCENE_NAME

    /**
     * 触发扫描
     * @param triggerType
     * @param config
     */
    override fun onScan(triggerType: Int, config: ScanConfig) {
        GLog.w(TAG, LogFlag.DL, "onScan, start download scan model triggerType:$triggerType, config:$config")
        startDownload()
        GLog.w(TAG, LogFlag.DL, "onScan, model download end")
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return false
    }

    private fun startDownload() {
        RemoteModelInfoManager.fetch(context, object : RemoteModelInfoManager.RequestListener {
            override fun onSuccess(modelInfoMap: HashMap<String, RemoteModelInfo>) {
                val templates = createTemplates(modelInfoMap)
                downloadParallel(templates)
            }

            override fun onFailure(code: Int) {
                GLog.d(TAG, LogFlag.DL, "onFailure, code=$code")
            }
        }, false, TAG)

        createSupportCheckStrategies().forEach { strategy ->
            strategy.checkSupportability(context)
        }
        downloadAndesSearchPlugin()
    }

    /**
     * 下载融合搜索需要的插件
     * clip 和 text embedding 插件下载
     */
    private fun downloadAndesSearchPlugin() {
        val isDownloading = context.getAppAbility<IMultiModalAbility>()?.use { it.isDownloading }
        GLog.d(TAG, LogFlag.DL, "downloadAndesSearchPlugin, isDownloading: $isDownloading")
        if (NetworkMonitor.isWifiValidated() && (isDownloading != true)) {
            // 这里会先将对应key的状态写入，后续策略流程中会使用到。
            context.getAppAbility<ISettingsAbility>()?.use {
                it.updateBlockingConfig(
                    context,
                    CLIP_AND_TEXT_EMBEDDING_PLUGIN_DOWNLOAD_STATE,
                    IS_AGREE_AI_UNIT_PRIVACY,
                    IS_AI_UNIT_CONFIG_AVAILABLE
                )
            }
            // 策略检查和下载
            AndesSearchPluginSupportCheckStrategy().checkSupportability(context, {}, {})
        }
    }

    /**
     * 创建需要下载的算法插件策略
     */
    private fun createSupportCheckStrategies(): List<ScanPluginSupportCheckStrategy> = listOf(
        TravelCoverSelectPluginSupportCheckStrategy(TravelCoverSelectApi),
        PetPluginSupportCheckStrategy(PetModelApi),
        StudyPluginSupportCheckStrategy(StudyClassifyApi)
    )

    /**
     * 创建待下载的模型template对象list
     * @param modelInfoMap 云端模型的信息Map
     */
    private fun createTemplates(modelInfoMap: HashMap<String, RemoteModelInfo>): MutableList<ModelLoadingTemplate> {
        val templates = mutableListOf<ModelLoadingTemplate>()
        templates.add(FaceModelLoadingTemplate(context, FaceModelConfig(), allowContinueAction))
        templates.add(LabelGraphModelLoadingTemplate(context, LabelGraphModelConfig(context), allowContinueAction))
        templates.add(EnglishLabelGraphModelLoadingTemplate(context, EnglishLabelGraphModelConfig(context), allowContinueAction))
        templates.add(MultiModalDictVulgarismsLoadingTemplate(context, MultiModalDictVulgarismsConfig(), allowContinueAction))
        templates.add(PoiModelLoadingTemplate(context, PoiModelConfig(context), allowContinueAction))

        templates.forEach {
            it.modelConfig.remoteModelInfo = modelInfoMap[it.modelName]
        }
        return templates
    }

    private fun downloadParallel(templates: MutableList<ModelLoadingTemplate>) {
        if (templates.isEmpty()) {
            GLog.e(TAG, LogFlag.DL, "downloadParallel, templates is empty, return")
            return
        }
        if (GalleryScanMonitor.isAllowContinueScan(context, false, mCanScanWhenGalleryRunOnTop).not()) {
            GLog.e(TAG, LogFlag.DL, "downloadParallel, not allow scan, return")
            return
        }
        context.getAppAbility<IDownloadAbility>()?.use { downloadAbility ->
            templates.forEach { modelTemplate ->
                val id = downloadAbility.downloadFileAsync(template = modelTemplate, priority = IDownloadAbility.Priority.BACKGROUND)
                GLog.d(TAG, LogFlag.DL, "downloadParallel, download start, modelTemplate=$modelTemplate, id=$id")
            }
        }
    }

    private companion object {
        private const val TAG = "GalleryScan.ModelDownloadScanner"
        private const val SCENE_NAME = "ModelDownloadScanner"
    }
}