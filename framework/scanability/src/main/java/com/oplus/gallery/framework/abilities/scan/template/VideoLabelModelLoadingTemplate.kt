/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : VideoLabelModelLoadingTemplate.kt
 ** Description : 视频标签扫描下载模板类
 ** Version     : 1.0
 ** Date        : 2024/3/7
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                            2024/3/7      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * 视频标签扫描下载模板类
 */
class VideoLabelModelLoadingTemplate(context: Context, modelConfig: ModelConfig) : CommonModelLoadingTemplate(context, modelConfig) {

    override val tag: String = TAG

    override val modelName: String = ModelName.VIDEO_LABEL_SCAN_SOURCE_V2

    companion object {
        private const val TAG = "VideoLabelModelLoadingTemplate"
    }
}