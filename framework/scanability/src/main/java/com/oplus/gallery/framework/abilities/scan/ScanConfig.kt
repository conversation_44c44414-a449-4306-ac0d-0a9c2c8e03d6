/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ScanConfig
 ** Description: 扫描配置类
 **
 ** Version: 1.0
 ** Date: 2021/11/18
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2023/3/13  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan

import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanProgressCollector

/**
 * 扫描配置
 * 一个配置用于一个批次扫描，如由List<ScanConfig>.size 为5 ，表示当前将进行5个批次的扫描
 */
data class ScanConfig(
    /**
     * 扫描的种类
     * 参考{ALL_SCAN_TYPE} 由各扫描type拼成
     * 注意：若扫描队列里面没有某个type的scanner，即使这里配置了也不会生效
     */
    val type: Int = GalleryScan.ALL_SCAN_TYPE,

    /**
     * 当前批次扫描图片数量
     * 会和各扫描模块的最大数量取较小值，作为当次扫描上限
     */
    val imageCount: Int = GalleryScanMonitor.getCloud24HMaxImageScanCount(),

    /**
     * 当前批次扫描视频数量
     * 会和各扫描模块的最大数量取较小值，作为当次扫描上限
     */
    val videoCount: Int = GalleryScanMonitor.getCloud24HMaxVideoScanCount(),

    /**
     * 是否记录扫描时间
     * 若是多批次(每批少量)扫描，建议前几批次不记录时间，否则可能出现后几批次不触发问题
     * 扫描时间判断机制：若上次扫描记录的时间跟当前时间比，不超过一个小时，则跳过当前批次扫描
     */
    val isRecordTime: Boolean = true
)