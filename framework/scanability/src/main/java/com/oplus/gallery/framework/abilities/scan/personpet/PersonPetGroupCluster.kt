/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PersonPetGroupCluster.kt
 ** Description : 人宠合照数据实体类
 ** Version     : 1.0
 ** Date        : 2025/05/15
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/05/15  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.personpet

import android.content.ContentValues
import com.oplus.gallery.foundation.database.store.GalleryStore.PersonPetGroup

/**
 * 人宠合照数据实体类
 */
class PersonPetGroupCluster {
    var id: Int? = null
    var data: String? = null
    var mediaType: Int? = null
    var createDate: Long? = null
    var createType: Int? = null
    var groupName: String? = null
    var groupId: Int? = null
    var personGroupIds: String? = null
    var petGroupIds: String? = null
    var score: Float? = null
    var isDefaultCover: Boolean? = null
    var isChosen: Boolean? = null
    var isDisband: Boolean? = null
    var thumbWidth: Int? = null
    var thumbHeight: Int? = null
    var rectLeft: Int? = null
    var rectTop: Int? = null
    var rectRight: Int? = null
    var rectBottom: Int? = null

    fun buildInsertContentValues(): ContentValues {
        return ContentValues().apply {
            data?.let { put(PersonPetGroup.DATA, it) }
            mediaType?.let { put(PersonPetGroup.MEDIA_TYPE, it) }
            createDate?.let { put(PersonPetGroup.MERGE_GROUP_CREATE_DATE, it) }
            createType?.let { put(PersonPetGroup.MERGE_GROUP_CREATE_TYPE, it) }
            groupName?.let { put(PersonPetGroup.MERGE_GROUP_NAME, it) }
            groupId?.let { put(PersonPetGroup.MERGE_GROUP_ID, it) }
            personGroupIds?.let { put(PersonPetGroup.PERSON_GROUP_IDS, it) }
            petGroupIds?.let { put(PersonPetGroup.PET_GROUP_IDS, it) }
            score?.let { put(PersonPetGroup.SCORE, it) }
            isDefaultCover?.let { put(PersonPetGroup.IS_DEFAULT_COVER, it) }
            isChosen?.let { put(PersonPetGroup.IS_CHOSEN, it) }
            isDisband?.let { put(PersonPetGroup.IS_DISBAND, it) }
            thumbWidth?.let { put(PersonPetGroup.THUMB_W, it) }
            thumbHeight?.let { put(PersonPetGroup.THUMB_H, it) }
            rectLeft?.let { put(PersonPetGroup.RECT_LEFT, it) }
            rectTop?.let { put(PersonPetGroup.RECT_TOP, it) }
            rectRight?.let { put(PersonPetGroup.RECT_RIGHT, it) }
            rectBottom?.let { put(PersonPetGroup.RECT_BOTTOM, it) }
        }
    }

    fun buildUpdateContentValues(): ContentValues {
        return ContentValues().apply {
            isDefaultCover?.let { put(PersonPetGroup.IS_DEFAULT_COVER, it) }
        }
    }

    fun clone(): PersonPetGroupCluster {
        return PersonPetGroupCluster().also {
            it.id = id
            it.data = data
            it.mediaType = mediaType
            it.createDate = createDate
            it.createType = createType
            it.groupName = groupName
            it.groupId = groupId
            it.personGroupIds = personGroupIds
            it.petGroupIds = petGroupIds
            it.score = score
            it.isDefaultCover = isDefaultCover
            it.isChosen = isChosen
            it.isDisband = isDisband
            it.thumbWidth = thumbWidth
            it.thumbHeight = thumbHeight
            it.rectLeft = rectLeft
            it.rectTop = rectTop
            it.rectRight = rectRight
            it.rectBottom = rectBottom
        }
    }
}