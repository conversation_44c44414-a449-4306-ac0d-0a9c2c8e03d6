/*********************************************************************************
 ** Copyright (C), 2019-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IqaQualityNode.kt
 ** Description : topLabelId 计算历史平均质量美学分数使用，用于db获取的topLabelId，avg的符合数据
 ** Version     : 1.0
 ** Date        : 2023/3/13
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** zhu<PERSON><PERSON>@Apps.Gallery             2023/3/13      1.0       build
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect

import android.database.Cursor

/**
 * 查询topLabelId的avg
 *
 * @param topLabelId：优选菜单大类id
 * @param avg：质量美学分平均分
 */
data class IqaQualityNode(val topLabelId: Int, val avg: Float = 0f) {

    companion object {
        private const val LABEL_INDEX = 0
        private const val AVG_INDEX = 1

        fun convert(cursor: Cursor): MutableList<IqaQualityNode> {
            val list = mutableListOf<IqaQualityNode>()
            if (cursor.count <= 0) {
                return mutableListOf()
            }
            while (cursor.moveToNext()) {
                val topLabelId = cursor.getInt(LABEL_INDEX)
                val avg = cursor.getFloat(AVG_INDEX)
                val node = IqaQualityNode(
                    topLabelId,
                    avg
                )
                list.add(node)
            }
            return list
        }
    }
}