/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PetCluster.kt
 ** Description : 宠物数据实体类，用于存储数据库查询的实体对象。
 ** Version     : 1.0
 ** Date        : 2025/05/08
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/05/08  1.0         create module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.pet

import android.content.ContentValues
import android.graphics.Bitmap
import android.graphics.Rect
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.ai.pet.PetInfo
import com.oplus.gallery.foundation.database.store.GalleryStore.PetColumns
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster

/**
 * 宠物数据实体类，用于存储数据库查询的实体对象。
 */
class PetCluster {
    var mediaItemRef: MediaItem? = null
    var petInfoRef: PetInfo? = null
    var id: Long? = null
    var data: String? = null
    var mediaType: Int? = null
    var invalid: Boolean? = null
    var isRecycled: Boolean? = null
    var thumbWidth: Int? = null
    var thumbHeight: Int? = null
    var groupID: Int? = null
    var groupName: String? = null
    var relationType: Int? = null
    var customRelation: String? = null
    var isHide: Boolean? = null
    var feature: ByteArray? = null
    var score: Float? = null
    var finalScore: Float? = null
    var appearMul: Int? = null
    var petCoverScore: Float? = null
    var petCoverWeight: Float? = null
    var left: Int? = null
    var top: Int? = null
    var right: Int? = null
    var bottom: Int? = null
    var tag: String? = null
    var isDefaultCover: Boolean? = null
    var isChosen: Boolean? = null
    var scanDate: Long? = null
    var groupDate: Long? = null
    var isManual: Boolean? = null
    var manualDate: Long? = null
    var modelVersion: String? = null
    var thumbnail: Bitmap? = null
    var faceRect: Rect? = null

    fun buildInsertContentValues(): ContentValues {
        return ContentValues().apply {
            data?.let { put(PetColumns.DATA, it) }
            mediaType?.let { put(PetColumns.MEDIA_TYPE, it) }
            invalid?.let { put(PetColumns.INVALID, it) }
            isRecycled?.let { put(PetColumns.IS_RECYCLED, it) }
            thumbWidth?.let { put(PetColumns.THUMB_W, it) }
            thumbHeight?.let { put(PetColumns.THUMB_H, it) }
            groupID?.let { put(PetColumns.GROUP_ID, it) }
            groupName?.let { put(PetColumns.GROUP_NAME, it) }
            relationType?.let { put(PetColumns.RELATION_TYPE, it) }
            customRelation?.let { put(PetColumns.CUSTOM_RELATION, it) }
            isHide?.let { put(PetColumns.IS_HIDE, it) }
            feature?.let { put(PetColumns.FEATURE, it) }
            score?.let { put(PetColumns.SCORE, it) }
            finalScore?.let { put(PetColumns.FINAL_SCORE, it) }
            appearMul?.let { put(PetColumns.APPEAR_MUL, it) }
            petCoverScore?.let { put(PetColumns.PET_COVER_SCORE, it) }
            petCoverWeight?.let { put(PetColumns.PET_COVER_WEIGHT, it) }
            left?.let { put(PetColumns.LEFT, it) }
            top?.let { put(PetColumns.TOP, it) }
            right?.let { put(PetColumns.RIGHT, it) }
            bottom?.let { put(PetColumns.BOTTOM, it) }
            isDefaultCover?.let { put(PetColumns.IS_DEFAULT_COVER, it) }
            isChosen?.let { put(PetColumns.IS_CHOSEN, it) }
            scanDate?.let { put(PetColumns.SCAN_DATE, it) }
            groupDate?.let { put(PetColumns.GROUP_DATE, it) }
            isManual?.let { put(PetColumns.IS_MANUAL, it) }
            manualDate?.let { put(PetColumns.MANUAL_DATE, it) }
            modelVersion?.let { put(PetColumns.MODEL_VERSION, it) }
        }
    }

    fun buildUpdateContentValues(): ContentValues {
        return ContentValues().apply {
            feature?.let { put(PetColumns.FEATURE, it) }
            score?.let { put(PetColumns.SCORE, it) }
            appearMul?.let { put(PetColumns.APPEAR_MUL, it) }
            petCoverScore?.let { put(PetColumns.PET_COVER_SCORE, it) }
            petCoverWeight?.let { put(PetColumns.PET_COVER_WEIGHT, it) }
            left?.let { put(PetColumns.LEFT, it) }
            top?.let { put(PetColumns.TOP, it) }
            right?.let { put(PetColumns.RIGHT, it) }
            bottom?.let { put(PetColumns.BOTTOM, it) }
            scanDate?.let { put(PetColumns.SCAN_DATE, it) }
            modelVersion?.let { put(PetColumns.MODEL_VERSION, it) }
        }
    }

    fun setMediaItem(item: MediaItem) {
        data = item.filePath
        mediaType = item.mediaType
        invalid = false
        isRecycled = false
    }

    fun setPetInfo(petInfo: PetInfo) {
        petInfoRef = petInfo
        feature = petInfo.petsFeature
        appearMul = petInfo.appearMul
        petCoverScore = petInfo.petCoverScore
        petCoverWeight = petInfo.petCoverWeight
        score = petInfo.score
        petInfo.petsRect?.let {
            left = it.left
            top = it.top
            right = it.right
            bottom = it.bottom
        }

        isDefaultCover = false
        isChosen = false
        isManual = false
    }

    fun isSamePet(other: PetCluster): Boolean {
        val newThumbWidth = thumbWidth ?: return false
        val newThumbHeight = thumbHeight ?: return false
        val newLeft = left ?: return false
        val newTop = top ?: return false
        val newRight = right ?: return false
        val newBottom = bottom ?: return false
        val oldThumbWidth = other.thumbWidth ?: return false
        val oldThumbHeight = other.thumbHeight ?: return false
        val oldLeft = other.left ?: return false
        val oldTop = other.top ?: return false
        val oldRight = other.right ?: return false
        val oldBottom = other.bottom ?: return false
        return CvFaceCluster.isSameFace(
            oldThumbWidth, oldThumbHeight, Rect(oldLeft, oldTop, oldRight, oldBottom),
            newThumbWidth, newThumbHeight, Rect(newLeft, newTop, newRight, newBottom)
        )
    }
}