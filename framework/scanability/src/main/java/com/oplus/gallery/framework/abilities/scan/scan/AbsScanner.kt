/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AbsScanner.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/11/08
 ** Author      : wenshuguang
 ** TAG         : OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>        <date>         <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** wenshuguang     2021/11/08      1.0            OPLUS_FEATURE_MEDIA_SELECT_ENGINE
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.scan

import android.content.Context
import android.graphics.Bitmap
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getThumbnailRotation
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.download.IDownloadAbility
import com.oplus.gallery.framework.abilities.download.template.ModelLoadingTemplate
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SeniorSelectModelConfig
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlin.math.min

abstract class AbsScanner<T>(
    private val context: Context
) {
    abstract val modelConfig: SeniorSelectModelConfig

    abstract val modelLoadingTemplate: ModelLoadingTemplate

    open val tag: String = "AbsScanner"

    @VisibleForTesting
    var target: T? = null

    private var count = 0

    var version = -1
        private set

    @Suppress("TooGenericExceptionCaught")
    @Synchronized
    fun initialize(): Boolean {
        if (target == null) {
            version = maxOf(modelConfig.buildInVersion, modelConfig.currentVersion)
            // 尝试将内置模型移动到指定目录，若目录下已有同名同md5文件会跳过复制，否则将移动文件到指定目录
            context.getAppAbility<IDownloadAbility>()?.use {
                it.downloadFileSync(modelLoadingTemplate)
            }
            val modelPath = File(modelConfig.currentComponentPath, modelConfig.modelFileName).absolutePath
            if ((modelPath != null) && (modelConfig.isModelReady())) {
                try {
                    target = createTarget(modelPath)
                    val oldModelVersion = modelConfig.currentVersion
                    if (version != oldModelVersion) {
                        modelUpdate(oldModelVersion, version)
                        modelConfig.currentVersion = version
                    }
                    afterInit()
                } catch (e: Exception) {
                    GLog.e(tag, "initialize", e)
                    return false
                }
            } else {
                return false
            }
        }
        count++
        return true
    }

    protected open fun afterInit() {
        // do nothing
    }

    protected open fun modelUpdate(oldModelVersion: Int, newModelVersion: Int) {
        GLog.d(tag, "modelUpdate, ${modelConfig.modelName} model upgrade")
    }

    /**
     * 根据给定的模型文件路径创建目标对象
     * @return 返回任意对象，表示模型文件的特征，如CvNearDup()
     */
    protected abstract fun createTarget(modelPath: String): T

    /**
     * 释放createTarget()创建的目标对象
     */
    protected abstract fun releaseTarget()

    @Synchronized
    fun release() {
        count--
        if (count < 0) {
            count = 0
        }
        if (count == 0) {
            releaseTarget()
        }
    }

    protected fun <K> getSubItems(items: List<K>): List<List<K>> {
        val subLists: MutableList<List<K>> = ArrayList()
        val subListSize: Int = (items.size - 1) / BATCH_SIZE + 1
        for (i in 0 until subListSize) {
            subLists.add(
                items.subList(
                    i * BATCH_SIZE,
                    min((i + 1) * BATCH_SIZE, items.size)
                )
            )
        }
        return subLists
    }

    protected fun getThumbnail(mediaItem: MediaItem): Bitmap? {
        var thumbnail = ContextGetter.context.getAppAbility<IResourcingAbility>()?.let { ability ->
            ResourceKeyFactory.createResourceKey(mediaItem)?.let {
                ability.requestBitmap(
                    it,
                    ResourceGetOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL, inSourceOperation = SourceOperation.ReadLocal)
                )?.result
            }
        }
        if (thumbnail == null) {
            GLog.w(tag) { "getThumbnail. Can't get thumbnail. path=${PathMask.mask(mediaItem.filePath)}" }
            return null
        }
        thumbnail = BitmapUtils.rotateBitmap(thumbnail, mediaItem.getThumbnailRotation(), true)
        return thumbnail
    }

    companion object {
        const val BATCH_SIZE = 50
    }
}