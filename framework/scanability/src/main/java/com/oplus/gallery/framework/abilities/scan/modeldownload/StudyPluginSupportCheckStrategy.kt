/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - StudyPluginSupportCheckStrategy.kt
 ** Description: 学习插件下载策略
 ** Version: 1.0
 ** Date: 2025/5/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2025/5/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.foundation.ai.IAIApi
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.StudyAlbum.STUDY_CLASSIFY_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.StudyAlbum.STUDY_CLASSIFY_PLUGIN_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_STUDY_CLASSIFY
import com.oplus.gallery.framework.abilities.download.IAIUnitPluginState

/**
 * 学习插件下载策略
 */
class StudyPluginSupportCheckStrategy(
    aiApi: IAIApi?,
    // mark by pusong：学习插件名字搭框架使用了旅程封面选片的名字，后面学习插件联调下载时更新为具体名字@卢煌道
    override val pluginState: IAIUnitPluginState = AIUnitPluginState(
        AIUnitPlugin.TRAVEL_COVER_SELECT.downloadPlugin,
        STUDY_CLASSIFY_PLUGIN_DOWNLOAD_STATE,
        STUDY_CLASSIFY_PLUGIN_SHOW_TIMESTAMP
    )
) : ScanPluginSupportCheckStrategy(aiApi) {

    override fun getInterceptors(context: Context): List<IInterceptor<Bundle, Unit>> {
        return listOf(StudyClassifyFeatureInterceptor())
    }

    override fun updateBlockingConfig(context: Context, settingsAbility: ISettingsAbility) {
        settingsAbility.updateBlockingConfig(context, IS_AGREE_AI_UNIT_PRIVACY)
    }

    override fun getTag(): String {
        return TAG
    }

    companion object {
        private const val TAG = "StudyPluginSupportCheckStrategy"
    }
}

/**
 * 学习分类Feature拦截器
 */
internal class StudyClassifyFeatureInterceptor : ConditionInterceptor() {

    override fun onCheckCondition(param: Bundle): Boolean {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_STUDY_CLASSIFY, defValue = false, expDefValue = false).also {
            GLog.d(TAG, LogFlag.DL) { "onCheckCondition. feature is support study classify: $it" }
        }
    }

    companion object {
        private const val TAG = "StudyClassifyFeatureInterceptor"
    }
}