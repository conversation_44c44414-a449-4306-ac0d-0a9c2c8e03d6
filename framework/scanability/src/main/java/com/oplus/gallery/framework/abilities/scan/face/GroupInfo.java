/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GroupInfo.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2016-11-24
 ** Author: xiuhua.ke
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xiuhua.ke                       2016-11-24   1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.face;

import android.database.Cursor;

import java.util.ArrayList;

import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.IS_HIDE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.CUSTOM_RELATION;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.GROUP_ID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.GROUP_NAME;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.GROUP_VISIBLE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.IS_MANUAL;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.MANUAL_DATE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.NUM_ALL_FACES;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceGroupColumns.RELATION_TYPE;

public class GroupInfo {
    public String mGroupName;
    public long mGroupId;
    public int mGroupVisible; // 0: not modify; 1: show; 2: hide
    public boolean mIsManual;
    public long mManualDate;
    public int mNumberOfFace;
    public int mRelationType;
    public String mCustomRelation;
    public boolean mIsHide;

    public static ArrayList<GroupInfo> buildGroupInfoList(Cursor cursor) {
        ArrayList<GroupInfo> list = new ArrayList<>();
        final int groupIdIndex = cursor.getColumnIndex(GROUP_ID);
        final int groupNameIndex = cursor.getColumnIndex(GROUP_NAME);
        final int groupVisibleIndex = cursor.getColumnIndex(GROUP_VISIBLE);
        final int numberOfFaceIndex = cursor.getColumnIndex(NUM_ALL_FACES);
        final int isManualIndex = cursor.getColumnIndex(IS_MANUAL);
        final int manualDateIndex = cursor.getColumnIndex(MANUAL_DATE);
        while (cursor.moveToNext()) {
            GroupInfo group = new GroupInfo();
            group.mGroupId = cursor.getLong(groupIdIndex);
            group.mGroupName = cursor.getString(groupNameIndex);
            group.mGroupVisible = cursor.getInt(groupVisibleIndex);
            group.mNumberOfFace = cursor.getInt(numberOfFaceIndex);
            group.mIsManual = cursor.getInt(isManualIndex) == 1;
            group.mManualDate = cursor.getLong(manualDateIndex);
            list.add(group);
        }
        return list;
    }

    public static ArrayList<GroupInfo> buildGroupInfoListInFaceCluster(Cursor cursor) {
        ArrayList<GroupInfo> list = new ArrayList<>();
        final int groupIdIndex = cursor.getColumnIndex(GROUP_ID);
        final int groupNameIndex = cursor.getColumnIndex(GROUP_NAME);
        final int groupVisibleIndex = cursor.getColumnIndex(GROUP_VISIBLE);
        final int isManualIndex = cursor.getColumnIndex(IS_MANUAL);
        final int manualDateIndex = cursor.getColumnIndex(MANUAL_DATE);
        final int relationTypeIndex = cursor.getColumnIndex(RELATION_TYPE);
        final int customRelationIndex = cursor.getColumnIndex(CUSTOM_RELATION);
        final int isHideIndex = cursor.getColumnIndex(IS_HIDE);
        while (cursor.moveToNext()) {
            GroupInfo group = new GroupInfo();
            group.mGroupId = cursor.getLong(groupIdIndex);
            group.mGroupName = cursor.getString(groupNameIndex);
            group.mGroupVisible = cursor.getInt(groupVisibleIndex);
            group.mIsManual = cursor.getInt(isManualIndex) == 1;
            group.mManualDate = cursor.getLong(manualDateIndex);
            group.mRelationType = cursor.getInt(relationTypeIndex);
            group.mCustomRelation = cursor.getString(customRelationIndex);
            group.mIsHide = cursor.getInt(isHideIndex) == 1;
            list.add(group);
        }
        return list;
    }

    public static ArrayList<GroupInfo> buildPetGroupInfoList(Cursor cursor) {
        ArrayList<GroupInfo> list = new ArrayList<>();
        final int groupIdIndex = cursor.getColumnIndex(GROUP_ID);
        final int groupNameIndex = cursor.getColumnIndex(GROUP_NAME);
        final int numberOfFaceIndex = cursor.getColumnIndex(NUM_ALL_FACES);
        final int isManualIndex = cursor.getColumnIndex(IS_MANUAL);
        final int manualDateIndex = cursor.getColumnIndex(MANUAL_DATE);
        while (cursor.moveToNext()) {
            GroupInfo group = new GroupInfo();
            group.mGroupId = cursor.getLong(groupIdIndex);
            group.mGroupName = cursor.getString(groupNameIndex);
            group.mNumberOfFace = cursor.getInt(numberOfFaceIndex);
            group.mIsManual = cursor.getInt(isManualIndex) == 1;
            group.mManualDate = cursor.getLong(manualDateIndex);
            list.add(group);
        }
        return list;
    }
}
