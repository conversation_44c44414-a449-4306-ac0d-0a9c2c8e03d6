/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ServiceConfig.kt
 ** Description : Service（LabelDictAgentService和LabelOcrAgentService）常量，临时使用的类。
 **               上述两个Service共用的常量，如果后续需要恢复两个Service的下载逻辑，这个类也还能用
 ** Version     : 1.0
 ** Date        : 2021/11/01
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2021/11/01      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.label

object ServiceConstants {
    internal const val MODULE_NAME = "album"
    internal const val DEFAULT_VERSION = -1L
    internal const val DEFAULT_LOCAL_VERSION = ""
}