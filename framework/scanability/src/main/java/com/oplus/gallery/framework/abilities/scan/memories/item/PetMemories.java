/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PetMemories.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2018/03/15
 ** Author:caowei@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** caow<PERSON>@Apps.Gallery3D           2018/03/15      1.0      build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.memories.item;

import static android.provider.BaseColumns._ID;

import android.content.Context;
import android.database.Cursor;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.memories.MemoriesType;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesNameRules;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesProviderHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper.DateRange;
import com.oplus.gallery.framework.abilities.scan.memories.Memories;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.util.ArrayList;

public class PetMemories extends Memories {
    private static final String TAG = "PetMemories";
    private static final String STOP_REASON_LESS_THAN_30_DAYS = TAG + "_LessThan30Days";
    private static final String STOP_REASON_SCAN_SIZE_NOT_ENOUGH = TAG + "_ScanSizeNotEnough";
    private static final String KEY_LABEL_PET = "宠物";
    private int mNameType = -1;
    private long mLastMemoriesTime = 0;

    public PetMemories(Context context) {
        super(context);
    }

    @Override
    public boolean prepareMemories() {
        mLastMemoriesTime = MemoriesProviderHelper.getLastSpecifiedMemoriesTime(getMemoriesId());
        if ((mLastMemoriesTime != 0) && (MemoriesScannerHelper.getAbsDaysBetweenTwoDates(mLastMemoriesTime,
                System.currentTimeMillis()) < getIntOptimalConfig(PET_MEMORIES_INTERVAL))) {
            GLog.d(TAG, "prepareMemories, current time interval last PetMemories taken time less than 30 days!");
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_LESS_THAN_30_DAYS;
            return false;
        }
        return true;
    }

    @Override
    public boolean scanMemories() {
        DateRange dateRange = new DateRange();
        dateRange.mStart = mLastMemoriesTime;
        dateRange.mEnd = System.currentTimeMillis();
        ArrayList<MediaItem> items = getItemListOfPet(mContext, dateRange, mMemoriesPicMin);
        mMediaItemList = MemoriesProviderHelper.getFilteredItemList(items, getLabelFilterIdList());
        GLog.d(TAG, "scanMemories, mMediaItemList size:" + mMediaItemList.size());
        if (mMediaItemList.size() <= mMemoriesPicMin) {
            // 扫描中断，记录原因
            mStopReason = STOP_REASON_SCAN_SIZE_NOT_ENOUGH;
            return false;
        }
        return true;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public boolean createMemories() {
        return createMemories(mMediaItemList);
    }

    @Override
    public int getMemoriesId() {
        return MemoriesType.PET_MEMORIES;
    }

    @Override
    public int getNameType() {
        if (mNameType < 0) {
            mNameType = MemoriesNameRules.getPetRandomNameType();
        }
        return mNameType;
    }

    private ArrayList<MediaItem> getItemListOfPet(Context context, DateRange dateRange, int minCount) {
        ArrayList<Integer> galleryIds = new ArrayList<>();
        Cursor cursor = null;
        try {
            int labelId = LabelDictionary.getLabelId(KEY_LABEL_PET);
            String sb = MemoriesProviderHelper.getWhereClauseOfMemoriesScanFolders(context);
            sb = sb.replace(LocalColumns.DATA + " ", GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATA + " ")
                    + " AND " + GalleryStore.ScanLabel.SCENE_ID + " = " + labelId
                    + " AND " + DatabaseUtils.getDataValidWhere(GalleryStore.ScanLabel.TAB)
                    + " AND " + GalleryStore.ScanLabel.IS_RECYCLED + " != 1"
                    + " AND " + LocalColumns.DATE_TAKEN + " BETWEEN "
                    + dateRange.mStart + " AND " + dateRange.mEnd
                    + " GROUP BY " + GalleryStore.GalleryMedia.TAB + "." + LocalColumns._ID + ";";
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("SELECT ");
            stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + _ID + " FROM ");
            stringBuilder.append(GalleryStore.GalleryMedia.TAB + " INNER JOIN " + GalleryStore.ScanLabel.TAB + " ON ");
            stringBuilder.append(GalleryStore.GalleryMedia.TAB + "." + LocalColumns.DATA + "="
                    + GalleryStore.ScanLabel.TAB + "." + LocalColumns.DATA);
            stringBuilder.append(" WHERE ");
            stringBuilder.append(sb.toString());
            RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setConvert(new CursorConvert())
                    .setQuerySql(stringBuilder.toString())
                    .setSqlArgs(null)
                    .build();
            cursor = DataAccess.getAccess().rawQuery(rawQueryReq);
            if ((cursor == null) || (cursor.getCount() <= minCount)) {
                GLog.w(TAG, "getItemListOfPet, pic count is less than " + minCount + ",dataRange =" + dateRange);
                return null;
            }
            int indexGalleryId = cursor.getColumnIndex(LocalColumns._ID);
            while (cursor.moveToNext()) {
                int id = cursor.getInt(indexGalleryId);
                galleryIds.add(id);
            }
        } catch (Exception e) {
            GLog.w(TAG, "getItemListOfPet exception:" + e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return MemoriesProviderHelper.getMediaItemById(context, galleryIds);
    }
}
