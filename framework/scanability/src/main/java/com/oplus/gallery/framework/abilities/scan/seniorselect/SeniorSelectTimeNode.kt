/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SeniorSelectTimeNode
 ** Description: 精选扫描中分批策略的时间节点类
 **
 ** Version: 1.0
 ** Date: 2022/01/19
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/01/19  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect

import android.database.Cursor
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.ConstantUtils

data class SeniorSelectTimeNode(
    val date: String,
    val itemRange: IntRange,
    val count: Int
) {
    companion object {
        private const val DATE_INDEX = 0
        private const val COUNT_ALL_INDEX = 1

        val PROJECTION_SCANNED = arrayOf(
            GalleryStore.GalleryColumns.LocalColumns.DAY,
            ConstantUtils.COUNT_DISTINCT + GalleryStore.SeniorMediaColumns.SIMILAR_GROUP_ID + ConstantUtils.RIGHT_BRACKETS
        )

        val PROJECTION_NOT_SCANNED = arrayOf(
            GalleryStore.GalleryColumns.LocalColumns.DAY,
            ConstantUtils.COUNT_ALL
        )

        fun convert(cursor: Cursor): MutableList<SeniorSelectTimeNode> {
            val list = mutableListOf<SeniorSelectTimeNode>()
            if (cursor.count <= 0) {
                return mutableListOf()
            }
            var index = 0
            while (cursor.moveToNext()) {
                val date = cursor.getString(DATE_INDEX)
                val count = cursor.getInt(COUNT_ALL_INDEX)
                val node = SeniorSelectTimeNode(
                    date,
                    IntRange(index, index + count - 1),
                    count
                )
                list.add(node)
                index += count
            }
            return list
        }
    }

    override fun toString(): String {
        return "[date='$date', itemRange=$itemRange, count=$count]"
    }
}