/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  TravelCoverScanner.kt
 * * Description:  旅行封面打分数据扫描
 * * Version: 1.0
 * * Date : 2025/06/12
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/06/12     1.0        create
 ************************************************************/
package com.oplus.gallery.framework.abilities.scan.travel

import android.content.Context
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelCoverInfo
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelDBTableHelper
import com.oplus.gallery.foundation.travel.TravelCoverSelectApi
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_COVER_FIRST_SCAN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_COVER_SCAN_COUNT_24H
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Travel.TRAVEL_COVER_SCAN_COUNT_ALL
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.TRAVEL_SCAN_COUNT_24H_MAX
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.isAllowStartScan
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.refreshScanRecordingIfNecessary
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
import com.oplus.gallery.framework.app.GalleryApplication

/**
 * 旅程数据封面打分扫描
 */
class TravelCoverScanner(val context: Context) : GalleryScan(context) {

    /**
     * 24小时内已经扫描的旅程封面数据数量
     */
    private var coverScannedCountIn24hours = 0

    /**
     * 本次旅程封面总共扫描的数量
     */
    private var coverTotalCount = 0

    /**
     * 本次旅程封面需要扫描的数据
     */
    private var coverScanList = mutableListOf<TravelCoverInfo>()

    override fun onScan(
        triggerType: Int,
        config: ScanConfig?
    ) {
        GLog.w(TAG, LogFlag.DL) { "onScan, start scan travel cover triggerType:$triggerType" }
        super.onScan(triggerType, config)
        // 只能在充电场景
        if (isAllowStartScan(context, needCharging = true, canScanWhenGalleryRunOnTop = false) && isInterrupt.not()) {
            scanData()
        }
    }

    /**
     * 旅程数据封面打分扫描
     */
    private fun scanData() {
        var travelCoverSelectApi: TravelCoverSelectApi? = TravelCoverSelectApi(context)
        if (travelCoverSelectApi?.init() != true) {
            GLog.e(TAG, LogFlag.DL) { "scanData, travelCoverSelectApi init fail, return" }
            return
        }
        // 1.记录扫描相关数据，状态
        val scanStartTime = System.currentTimeMillis()
        markConditionsOnStartScan()
        recordScanDataIn24H()
        runCatching {
            travelCoverSelectApi?.let {
                it.setScanState(true)
                if (isNeedCoverScan(it.getModelVersion())) {
                    doScan(it)
                }
            }
            GLog.w(TAG, LogFlag.DL) { "scanData, cover scan const time:${GLog.getTime(scanStartTime)}, coverTotalCount:$coverTotalCount" }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "scanData exception, $it" }
        }.also {
            travelCoverSelectApi?.setScanState(false)
            travelCoverSelectApi = null
        }
    }

    /**
     * 判断是否是24H内的第一次扫描，并获取24H小时内的扫描数据
     */
    private fun recordScanDataIn24H() {
        (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            mIsFirstScan = it.getBooleanConfig(TRAVEL_COVER_FIRST_SCAN, true) ?: true
            if (mIsFirstScan) {
                it.setBooleanConfig(TRAVEL_COVER_FIRST_SCAN, false)
            }
            coverScannedCountIn24hours = it.getIntConfig(TRAVEL_COVER_SCAN_COUNT_24H, 0) ?: 0
        }
    }

    /**
     * 是否需要进行封面扫描
     */
    private fun isNeedCoverScan(version: String): Boolean {
        coverScanList = TravelDBTableHelper.getScanCoverData(SCAN_COVER_SIZE, version)
        GLog.w(TAG, LogFlag.DL) { "isNeedCoverScan, need coverScanList:${coverScanList.size}" }
        return coverScanList.isNotEmpty()
    }

    /**
     * 封面打分
     */
    private fun doScan(travelCoverSelectApi: TravelCoverSelectApi) {
        while (coverScanList.isNotEmpty()) {
            if (isAllowContinueScan(coverScannedCountIn24hours, TRAVEL_SCAN_COUNT_24H_MAX) && isInterrupt.not()) {
                val oneScanList = getOneScanCountList()
                val scanList = doCluster(oneScanList, travelCoverSelectApi)
                val result = scanList.map { it.scanState }
                recordScanCount(scanList.size)
                coverTotalCount += scanList.size
                GLog.d(TAG, LogFlag.DL) { "doScan, oneScanList size:${oneScanList.size}, scanList:${scanList.size}, success size:${result.size}" }
                TravelDBTableHelper.updateTravelCoverScore(scanList)
            } else {
                GLog.w(TAG, LogFlag.DL) { "doScan, not allow cover scan" }
                break
            }
        }
    }

    /**
     * 图片打分
     */
    private fun doCluster(oneScanList: MutableList<TravelCoverInfo>, travelCoverSelectApi: TravelCoverSelectApi): List<TravelCoverInfo> {
        val result = mutableListOf<TravelCoverInfo>()
        for (item in oneScanList) {
            if (isInterrupt.not()) {
                val mediaItemList = GalleryScanProviderHelper.convertImageToMediaItem(listOf(item))
                if (mediaItemList.isNotEmpty()) {
                    val bitmap = getThumbnail(mediaItemList[0])
                    val score = travelCoverSelectApi.cluster(bitmap, item.faceInfoList)
                    if (score != null) {
                        item.score = score
                        item.version = travelCoverSelectApi.getModelVersion()
                        item.scanState = true
                    }
                    result.add(item)
                }
            } else {
                GLog.d(TAG, LogFlag.DL) { "doCluster, isInterrupt" }
                break
            }
        }
        return result
    }

    /**
     * 获取小于等于ONE_SCAN_COUNT的list
     */
    private fun getOneScanCountList(): MutableList<TravelCoverInfo> {
        val subAddSize = if (coverScanList.size >= ONE_SCAN_COUNT) {
            ONE_SCAN_COUNT
        } else {
            coverScanList.size
        }
        val subList = mutableListOf<TravelCoverInfo>()
        for (i in 0 until subAddSize) {
            subList.add(coverScanList.removeAt(0))
        }
        return subList
    }

    /**
     * 记录 SCAN_COUNT_ALL 和 SCAN_COUNT_24H 的数据
     */
    private fun recordScanCount(count: Int) {
        addScanCount(count)
        recordScanInfoIfNeed(count)
    }

    /**
     * 记录24小时内扫描数量在未充电情况下
     */
    private fun addScanCount(count: Int) {
        if (!GalleryScanUtils.isTestMode()) {
            BatteryStatusUtil.isBatteryInCharging(true)
            refreshScanRecordingIfNecessary(context)
            (context as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
                coverScannedCountIn24hours = (it.getIntConfig(TRAVEL_COVER_SCAN_COUNT_24H, 0) ?: 0) + count
                it.setIntConfig(TRAVEL_COVER_SCAN_COUNT_24H, coverScannedCountIn24hours)
            }
        }
    }

    /**
     * 记录总的扫描数量
     */
    private fun recordScanInfoIfNeed(count: Int) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return
        }
        (context.applicationContext as GalleryApplication).getAppAbility<IConfigSetterAbility>()?.use {
            val scanCountAll = (it.getIntConfig(TRAVEL_COVER_SCAN_COUNT_ALL, 0) ?: 0) + count
            it.setIntConfig(TRAVEL_COVER_SCAN_COUNT_ALL, scanCountAll)
            GalleryScanUtils.recordInfo(context, TAG, coverScannedCountIn24hours.toLong(), scanCountAll.toLong())
        }
    }

    override fun getScanType(): Int {
        return TRAVEL_COVER_SCANNER
    }

    override fun getSceneName(): String {
        return SCENE_NAME
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return true
    }

    companion object {
        private const val TAG = "${ScanConst.TAG}TravelCoverScanner"
        private const val SCENE_NAME = "TravelCoverScanner"

        /**
         * 封面打分每轮扫描的数量
         */
        private const val SCAN_COVER_SIZE = 500

        /**
         * 每一小轮封面打分数量
         */
        private const val ONE_SCAN_COUNT = 50
    }
}