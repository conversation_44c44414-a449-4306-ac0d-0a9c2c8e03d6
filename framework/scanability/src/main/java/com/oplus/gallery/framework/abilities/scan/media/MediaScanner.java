/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaScanner.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2019/07/15
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** YongQ<PERSON>.<PERSON>@Apps.Gallery3D       2019/07/15   1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.media;

import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_TO;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.FROM;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.LEFT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.NOT_IN;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.RIGHT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.SELECT;

import android.content.ContentValues;
import android.content.Context;
import android.text.TextUtils;

import com.oplus.gallery.basebiz.helper.PhoneClonePageHelper;
import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.framework.abilities.album.AlbumSetSyncManager;
import com.oplus.gallery.framework.abilities.album.FullSyncFrom;
import com.oplus.gallery.framework.abilities.scan.ScanConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult;
import com.oplus.gallery.foundation.dbaccess.convert.ContentValuesConvert;
import com.oplus.gallery.foundation.dbaccess.convert.FilePathsConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.BatchReq;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq;
import com.oplus.gallery.foundation.util.mask.DisplayNameMask;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.foundation.util.permission.RuntimePermissionUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class MediaScanner extends GalleryScan {
    private static final String TAG = ScanConst.TAG + "MediaScanner";
    private static final String SCENE_NAME = "MediaScanner";

    public MediaScanner(Context context) {
        super(context);
    }

    @Override
    public int getScanType() {
        return MEDIA_SCAN;
    }

    @Override
    public String getSceneName() {
        return SCENE_NAME;
    }

    @Override
    public void onScan(int triggerType, ScanConfig config) {
        GLog.d(TAG, LogFlag.DL, "onScan  triggerType =" + triggerType);
        super.onScan(triggerType, config);
        AlbumSetSyncManager.INSTANCE.fullSyncAlbumSetAsync(FullSyncFrom.FROM_SCAN);
        deltaScan();
    }

    @Override
    protected boolean runTaskIsNeedCharging() {
        return false;
    }

    private void deltaScan() {
        if (!havePermission()) {
            GLog.e(TAG, LogFlag.DL, "deltaScan error: Important permissions denied!");
            return;
        }
        correctDisplayName();
        long lastUpdateTime = PhoneClonePageHelper.getInsertScheduleCurrentTime();
        GLog.d(TAG, "lastUpdateTime = " + lastUpdateTime);
        // 搬家完成三天后,清除冗余数据。非搬家场景lastUpdateTime为0,每次扫描是可以触发清除冗余数据的
        if ((System.currentTimeMillis() - lastUpdateTime) >= TimeUtils.TIME_3_DAY_IN_MS) {
            clearRedundantData();
        } else {
            GLog.d(TAG, "Less than three days");
        }
    }

    /**
     * 查找所有_data尾部不是“/display_name”结尾的异常记录，使用_data矫正display_name
     */
    private void correctDisplayName() {
        try {
            List<String> datas = queryDisplayNameExceptionDatas();
            if ((datas == null) || datas.isEmpty()) {
                return;
            }
            final List<UpdateReq> updateReqs = createDisplayNameUpdateReqs(datas, true);
            if (updateReqs.isEmpty()) {
                return;
            }
            BatchResult[] results = new BatchReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(updateReqs)
                    .build().exec();
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY);
            GLog.d(TAG, LogFlag.DL,"correctDisplayName correct count=" + ((results == null) ? "-1" : results.length));
        } catch (Exception e) {
            GLog.d(TAG, "correctDisplayName error:", e);
        }
    }

    private List<String> queryDisplayNameExceptionDatas() {
        // _data not like '%'||'/'||display_name and invalid is not null
        String whereClause = GalleryStore.GalleryColumns.LocalColumns.DATA + " not like '%'||'/'||"
                + GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME
                + " and " + GalleryStore.GalleryColumns.LocalColumns.INVALID + " is not null";
        return new QueryReq.Builder<ArrayList<String>>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(new String[]{GalleryStore.GalleryColumns.LocalColumns.DATA})
                .setWhere(whereClause)
                .setConvert(new FilePathsConvert())
                .build().exec();
    }

    private List<UpdateReq> createDisplayNameUpdateReqs(List<String> datas, Boolean noNotify) {
        final ArrayList<UpdateReq> updateReqs = new ArrayList<>();
        final String where = GalleryStore.GalleryColumns.LocalColumns.DATA + EQUAL_TO;
        for (String data : datas) {
            String displayName = FilePathUtils.getDisplayName(data);
            if (TextUtils.isEmpty(displayName)) {
                GLog.e(TAG, "createDisplayNameUpdateReqs getDisplayName fail, data=" + PathMask.INSTANCE.mask(data)
                        + ", displayName=" + DisplayNameMask.INSTANCE.mask(displayName));
                continue;
            }
            ContentValues values = new ContentValues();
            values.put(GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME, displayName);
            updateReqs.add(new UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, noNotify ? DatabaseUtils.VALUE_TRUE : DatabaseUtils.VALUE_FALSE)
                    .setConvert(new ContentValuesConvert(values))
                    .setWhere(where)
                    .setWhareArgs(new String[]{data})
                    .build());
        }
        return updateReqs;
    }

    /**
     * 清除业务表里已被确认删除的冗余数据（既不在local_media也不在recycle_media）
     */
    private void clearRedundantData() {
        final long startTime = System.currentTimeMillis();

        final int memoriesSetmapCount = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.MEMORIES_SETMAP)
                .setWhere(dataNotInLocalDB(GalleryStore.MemoriesSetmapColumns.DATA))
                .build()
                .exec();

        final int scanFaceCount = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                .setWhere(dataNotInLocalDB(GalleryStore.ScanFaceColumns.DATA))
                .build()
                .exec();

        final int scanLabelCount = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                .setWhere(dataNotInLocalDB(GalleryStore.ScanLabelColumns.DATA))
                .build()
                .exec();

        final int ocrPagesCount = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.OCR_PAGES)
                .setWhere(dataNotInLocalDB(GalleryStore.OcrPagesColumns.DATA))
                .build()
                .exec();

        final int fileInfoCount = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.FILE_INFO)
                .setWhere(dataNotInLocalDB(GalleryStore.FileInfoColumns.DATA))
                .build()
                .exec();

        final int travelCount = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_TRAVEL)
                .setWhere(dataNotInLocalDB(GalleryStore.ScanTravelColumns.PATH))
                .build()
                .exec();

        final int captionPagesCount = new DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_CAPTION)
                .setWhere(dataNotInLocalDB(GalleryStore.ScanCaptionColumns.DATA))
                .build()
                .exec();

       /*
        * 格式问题导致checkStyle过不了，因此将条件拆开
        * Boolean expression complexity is 5 (max allowed is 4)
        */
        boolean isScanResult = (memoriesSetmapCount > 0)
                || (scanFaceCount > 0)
                || (scanLabelCount > 0)
                || (ocrPagesCount > 0)
                || (travelCount > 0)
                || (captionPagesCount > 0);
        boolean isFileFileOrHighlightResult = (fileInfoCount > 0);

        if (isScanResult || isFileFileOrHighlightResult) {
            final String msg = String.format(Locale.ENGLISH,
                    "clearRedundantData memoriesSetmapCount:%d, "
                            + "scanFaceCount:%d,"
                            + "scanLabelCount:%d, "
                            + "ocrPagesCount:%d, "
                            + "fileInfoCount:%d, "
                            + "travelCount:%d, "
                            + "captionPagesCount:%d"
                            + "costTime:%d",
                    memoriesSetmapCount,
                    scanFaceCount,
                    scanLabelCount,
                    ocrPagesCount,
                    fileInfoCount,
                    travelCount,
                    captionPagesCount,
                    GLog.getTime(startTime)
            );
            GLog.w(TAG, msg);
            ApiDmManager.getMediaDBSyncDM().onInfoTrack(TAG, msg);
        }
    }

    private String dataNotInLocalDB(String data) {
        return data + NOT_IN + LEFT_BRACKETS
                + SELECT + GalleryStore.GalleryColumns.LocalColumns.DATA + FROM + GalleryStore.GalleryMedia.TAB
                + RIGHT_BRACKETS;
    }

    private boolean havePermission() {
        return RuntimePermissionUtils.isNecessaryPermissionGranted(mContext);
    }
}