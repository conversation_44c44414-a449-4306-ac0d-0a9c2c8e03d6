/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PoiModelLoadingTemplate.kt
 ** Description : POI资源下载类
 ** Version     : 1.0
 ** Date        : 2025/05/26
 ** Author      : W9095286
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9095286                            2025/05/26      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName

private const val TAG = "PoiModelLoadingTemplate"

/**
 * POI表下载配置模板
 */
class PoiModelLoadingTemplate(context: Context, modelConfig: ModelConfig, allowContinueAction: (() -> Boolean)? = null) :
    CommonModelLoadingTemplate(context, modelConfig, allowContinueAction) {
    override val tag: String
        get() = TAG
    override val modelName: String
        get() = ModelName.POI_ADDRESS_SOURCE
}