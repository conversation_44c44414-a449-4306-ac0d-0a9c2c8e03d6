/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Batch
 ** Description: 精选扫描分批策略
 **
 ** Version: 1.0
 ** Date: 2022/01/24
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/01/24  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.batch

import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectTimeNode
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog

abstract class Batch<T> {
    companion object {
        private const val TAG = "Batch"
    }

    var nextBatch: Batch<T>? = null

    /**
     * xxxNodeList 将随着扫描不断减少
     */
    protected var notScannedNodeList: MutableList<SeniorSelectTimeNode> = mutableListOf()
    protected var scannedNodeList: MutableList<SeniorSelectTimeNode> = mutableListOf()
    protected var notScannedInfoList: MutableList<BaseImageInfo> = mutableListOf()
    protected var scannedInfoList: MutableList<BaseImageInfo> = mutableListOf()

    /**
     * 获取下一批次的素材，用ScanType分为四类：未扫的图片list、已扫的图片list、未扫的视频list，已扫的视频list
     */
    fun getNextBatchData(): T? {
        return if (isSwitchToNextBatch()) {
            GLog.d(
                TAG, "getNextBatchData. nextBatch{${nextBatch?.javaClass?.simpleName}}.getNextBatchData" +
                    "  hasNext:${nextBatch?.hasNext()}"
            )
            if (nextBatch?.hasNext() == false) {
                updateDataToNextBatch()
            }
            if (nextBatch?.hasNext() == true) {
                nextBatch?.getNextBatchData()
            } else {
                null
            }
        } else {
            getData()
        }
    }

    fun updateNodeList(
        notScannedNodes: MutableList<SeniorSelectTimeNode>,
        scannedNodes: MutableList<SeniorSelectTimeNode>
    ) {
        notScannedNodeList = notScannedNodes
        scannedNodeList = scannedNodes
    }

    fun updateInfoList(
        notScannedInfo: MutableList<BaseImageInfo>,
        scannedInfo: MutableList<BaseImageInfo>
    ) {
        notScannedInfoList = notScannedInfo
        scannedInfoList = scannedInfo
    }

    /**
     * 对数据按media type 进行分类
     * @param infoList 需进行分类的数据
     * @return 返回Pair类型，First 是imageList， Second 是videoList
     */
    protected fun classifyInfoByMediaType(infoList: MutableList<BaseImageInfo>): Pair<MutableList<BaseImageInfo>, MutableList<BaseImageInfo>> {
        val imageList = mutableListOf<BaseImageInfo>()
        val videoList = mutableListOf<BaseImageInfo>()
        for (info in infoList) {
            when (info.mMediaType) {
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE -> imageList.add(info)
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO -> videoList.add(info)
                else -> GLog.e(TAG, "classifyInfoByMediaType. unknown media type: ${info.mMediaType}")
            }
        }
        return Pair(imageList, videoList)
    }

    /**
     * 通过未扫节点的日期，查找该日期有无已扫素材，有则返回对应的index
     */
    protected fun getIndexOfSameDate(date: String, countOfPerNodeWhichScanned: MutableList<SeniorSelectTimeNode>): Int {
        for ((index, node) in countOfPerNodeWhichScanned.withIndex()) {
            if (node.date == date) {
                return index
            } else if (node.date < date) {
                return -1
            }
        }
        return -1
    }

    /**
     * 是否有下一批次
     * @return 有则继续扫描
     */
    abstract fun hasNext(): Boolean

    /**
     * 获取下一批次数据
     * @return 返回下一批次数据
     */
    abstract fun getData(): T

    /**
     * 判断是否需要切换到下一批次的数据
     * @return 返回判断结果
     */
    abstract fun isSwitchToNextBatch(): Boolean

    /**
     * 更新到下一批次的数据
     */
    abstract fun updateDataToNextBatch()
}

/**
 * 每批待扫描数据内容
 */
data class BatchScanData(
    /**
     * 未扫描图片
     */
    val imageNotScanned: List<BaseImageInfo> = emptyList(),
    /**
     * 已扫描图片
     */
    val imageScanned: List<BaseImageInfo> = emptyList(),
    /**
     * 未扫描视频
     */
    val videoNotScanned: List<BaseImageInfo> = emptyList(),
    /**
     * 已扫描视频
     */
    val videoScanned: List<BaseImageInfo> = emptyList()
) {
    override fun toString(): String {
        return "BatchScanData[imageNotScanned:${imageNotScanned.size}|imageScanned:${imageScanned.size}" +
            "|videoNotScanned:${videoNotScanned.size}|videoScanned:${videoScanned.size}]"
    }
}