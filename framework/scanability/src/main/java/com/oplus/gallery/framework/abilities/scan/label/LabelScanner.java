/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelScanner.java
 ** Description:
 ** Version: 1.0
 ** Date: 2021/1/18
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** OPLUS Java File Skip Rule:MethodLength,DeclarationOrder
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                     <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** yanchao.Chen@Apps.Gallery3D   2017/06/08    1.0              build this module
 ** Dajian@Apps.Gallery3D         2021/1/18     1.1              modify
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.scan.label;

import static com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor.LABEL_SCAN_COUNT_24H_MAX;

import android.content.ContentValues;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.text.TextUtils;

import com.cv.imageapi.model.CvClassifyLabel;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.business_lib.bean.BaseImageInfo;
import com.oplus.gallery.business_lib.cardcase.api.CardCaseDataOperationApi;
import com.oplus.gallery.business_lib.cardcase.utils.CardCaseUtils;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine;
import com.oplus.gallery.business_lib.model.data.label.bean.LabelInfo;
import com.oplus.gallery.business_lib.model.search.cloud.LabelCloudHelper;
import com.oplus.gallery.business_lib.model.search.segment.Dictionary;
import com.oplus.gallery.business_lib.util.AssetHelper;
import com.oplus.gallery.foundation.database.notifier.UriNotifier;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.BatchReq;
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.framework.abilities.scan.ScanConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackHelper;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackInfo;
import com.oplus.gallery.framework.abilities.scan.label.engine.AbsVideoLabelClassifyEngine;
import com.oplus.gallery.framework.abilities.scan.label.engine.VideoEngineFactory;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan;
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScanMonitor;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanDataManager;
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.framework.abilities.watermark.IPrivacyWatermarkAbility;
import com.oplus.gallery.framework.abilities.watermark.IWatermarkAbility;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import androidx.annotation.NonNull;

import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PRIVACY_WATERMARK;

public class LabelScanner extends GalleryScan {

    private static final String TAG = ScanConst.TAG + "LabelScanner";
    private static final String SCENE_NAME = "LabelScanner";
    private static final String SCENE_SCANNER_EMPTY = SCENE_NAME + "_Empty";
    private static final String SCENE_SCANNER_INIT_FAILED = SCENE_NAME + "_InitFailed";

    /**
     * 非法labelId（默认没有标签的labelId）
     */
    public static final int SCENE_ID_INVALID = 0;

    /**
     *  非法score（默认没有标签的score）
     */
    public static final float SCENE_SCORE = 0f;

    private static final String IMAGE_LABEL_COUNT = "image_label_count";
    private static final String VIDEO_LABEL_COUNT = "video_label_count";

    /**
     * 标签单次扫描的数量：50张
     */
    private static final int ONE_CLASSIFY_COUNT = 50;

    private static final int INVALID_VERSION = -1;
    private static final int DEFAULT_MEDIA_ID = -1;
    private static final int MAX_LENGTH = 400;

    /**
     * 图片标签分类引擎
     */
    private final LabelClassifyEngine mClassifyEngine;

    /**
     * 视频标签分类引擎
     */
    private final AbsVideoLabelClassifyEngine mVideoLabelClassifyEngine;

    private final HashMap<Integer, Integer> mLabelInfoImageMap = new HashMap<>();
    private final HashMap<Integer, Integer> mLabelInfoVideoMap = new HashMap<>();

    /**
     * 新的，此前没有扫描过的媒体列表
     */
    private ArrayList<MediaItem> mNewMediaItem = new ArrayList<>();

    /**
     * 需要重新/更新扫描的媒体列表
     */
    private ArrayList<MediaItem> mUpdateMediaItem = new ArrayList<>();


    private int mDefaultVersion = 0;
    private int mCurrentVersion = 0;
    private int mLabelScanCount = 0;
    private boolean mIsCharging = false;

    /**
     * 标签数据是否有更新
     */
    private boolean mUpdateSuccess = true;

    private long mDeleteLastMediaId = DEFAULT_MEDIA_ID;

    /**
     * mClassifyEngine 初始化是否成功
     */
    private boolean mLabelImageEnginSuccess = false;

    /**
     * mVideoLabelClassifyEngine 初始化是否成功
     */
    private boolean mLabelVideoEnginSuccess = false;

    public LabelScanner(Context context) {
        super(context);
        mClassifyEngine = new LabelClassifyEngine(context);
        mVideoLabelClassifyEngine = VideoEngineFactory.getEngine(context);
    }

    /**
     * 记录扫描开始前的基本信息：如Native堆栈大小、电池电量、温度信息、是否在充电
     */
    private void recordScanBeforeInfo() {
        markConditionsOnStartScan();
        mIsCharging = BatteryStatusUtil.isBatteryInCharging(false);
    }

    /**
     * 清理第一次扫描的标记位
     */
    private void clearFirstScanFlag() {
        mIsFirstScan = GalleryScanUtils.isFirstLabelScan(mContext);
        if (mIsFirstScan) {
            GalleryScanUtils.setFirstLabelScanPref(mContext);
        }
    }

    public void doClassify(int scanTriggerType) {
        GLog.d(TAG, LogFlag.DL, "doClassify start, scanTriggerType is " + scanTriggerType);
        recordScanBeforeInfo();
        clearFirstScanFlag();
        getComponentVersionInfo();

        // 增加标签数据升级测试模式
        if (GalleryScanUtils.DEBUG_SCAN_LABEL_UPDATE) {
            GLog.w(TAG, LogFlag.DL, "doClassify DEBUG_SCAN_LABEL_UPDATE = true");
            mUpdateSuccess = false;
        }
        GLog.v(TAG, LogFlag.DL, "doClassify, mCurrentVersion:" + mCurrentVersion + ",mDefaultVersion:" + mDefaultVersion
                + ",mHasLabelDataUpdate:" + mUpdateSuccess);
        updateComponentVersionInfo();
        deleteBackupLabelInfo();
        LabelSearchEngine.getInstance().resetLocalParsePrior(mContext);
        long dictLoadStartTime = System.currentTimeMillis();
        // 检查词典是否已经加载，若没有加载则需要先加载词典
        GalleryScanUtils.checkDictionaryLoaded(mContext);
        GLog.d(TAG, LogFlag.DL, "doClassify labelScan costTime dictLoadTime:" + GLog.getTime(dictLoadStartTime) + "ms");

        long getAllClassifyImageTime = System.currentTimeMillis();
        getAllNeedScanMediaItems();
        GLog.d(TAG, LogFlag.DL, "doClassify, labelScan costTime getAllClassifyImageTime:" + GLog.getTime(getAllClassifyImageTime) + "ms");

        mNewCount = mNewMediaItem.size();
        mUpdateCount = mUpdateMediaItem.size();
        mAllCount = mNewCount + mUpdateCount;
        if (mAllCount == 0) {
            GLog.v(TAG, LogFlag.DL, "doClassify, has no new image and update image, do not need to doClassify!");
            updateLastScanTime();
            // 扫描中断，记录原因
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_EMPTY, scanTriggerType);
            return;
        }

        // start classifying
        initClassifyEngine(mDefaultVersion, mCurrentVersion);
        if (!mLabelImageEnginSuccess && !mLabelVideoEnginSuccess) {
            GLog.w(TAG, LogFlag.DL, "doClassify, init failed!");
            // 扫描中断，记录原因
            ScanTrackHelper.trackStopReasonOnStart(mContext, SCENE_SCANNER_INIT_FAILED, scanTriggerType);
            return;
        }

        classifyAllMediaItems(scanTriggerType);
        trackLabelScan(null, false);

        //set current time when finished
        if (!isInterrupt()) {
            updateLastScanTime();
            if (!mUpdateSuccess) {
                // Before updating the label database, reset dictionary for prevention of data conflict
                Dictionary.getInstance().reset();
                // update label DB
                if (updateLabelsDB()) {
                    String curLabelListPath = mContext.getFilesDir() + ComponentPrefUtils.LABEL_COMPONENT_CURRENT_PATH;
                    String backupLabelListPath = mContext.getFilesDir() + ComponentPrefUtils.LABEL_COMPONENT_BACKUP_PATH;
                    File oldLabelListFile = new File(curLabelListPath, ComponentPrefUtils.OLD_LABEL_LIST_NAME);
                    File backupOldLabelListFile = new File(backupLabelListPath, ComponentPrefUtils.OLD_LABEL_LIST_NAME);
                    if (oldLabelListFile.exists()) {
                        boolean ans = oldLabelListFile.delete();
                        GLog.d(TAG, LogFlag.DL, "doClassify, oldLabelListFile.delete:" + ans);
                    }
                    if (backupOldLabelListFile.exists()) {
                        boolean ans = backupOldLabelListFile.delete();
                        GLog.d(TAG, LogFlag.DL, "doClassify, backupOldLabelListFile.delete:" + ans);
                    }
                    updateLabelStatus();
                    LabelSearchEngine.getInstance().loadDictionary(mContext, true);
                }
            }
        }
        releaseClassifyEngine();

        //sync cloud label data into local label db
        LabelCloudHelper.updateCloudSceneDBInBackground(mContext);
    }

    private void getComponentVersionInfo() {
        SharedPreferences sp = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME);
        mDefaultVersion = AssetHelper.getInstance().getComponentDefaultVersion(mContext,
                ComponentPrefUtils.LABEL_COMPONENT_DEFAULT_VERSION_NAME);
        mCurrentVersion = sp.getInt(ComponentPrefUtils.LABEL_COMPONENT_VERSION_KEY, INVALID_VERSION);
        mUpdateSuccess = sp.getBoolean(ComponentPrefUtils.HAS_LABEL_DATA_UPDATE_SUCCESS_KEY, true);
    }

    private void updateLabelStatus() {
        SharedPreferences sp = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME);
        SharedPreferences.Editor editor = sp.edit();
        editor.remove(ComponentPrefUtils.OLD_LABEL_LIST_VERSION_KEY);
        editor.remove(ComponentPrefUtils.OLD_LABEL_LIST_MD5_KEY);
        editor.putBoolean(ComponentPrefUtils.HAS_LABEL_DATA_UPDATE_SUCCESS_KEY, true);
        if (mCurrentVersion < mDefaultVersion) {
            editor.putInt(ComponentPrefUtils.LABEL_COMPONENT_VERSION_KEY, mDefaultVersion);
        }
        editor.apply();
    }

    /**
     * 更新组件版本信息
     */
    private void updateComponentVersionInfo() {
        SharedPreferences.Editor editor = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME).edit();
        GLog.d(TAG, LogFlag.DL, "updateComponentVersionInfo mCurrentVersion:" + mCurrentVersion + ", mDefaultVersion:" + mDefaultVersion);
        if (mCurrentVersion < mDefaultVersion) {
            GLog.v(TAG, LogFlag.DL, "doClassify, current version is less then default version, we need use asset components!");
            if (mUpdateSuccess && isLabelTableNotEmpty()) {
                mUpdateSuccess = false;
                editor.putBoolean(ComponentPrefUtils.HAS_LABEL_DATA_UPDATE_SUCCESS_KEY, false);
                editor.apply();
            }
        }
        editor.putInt(ComponentPrefUtils.LABEL_COMPONENT_VERSION_KEY, Math.max(mDefaultVersion, mCurrentVersion));
        editor.apply();
    }

    private void deleteBackupLabelInfo() {
        if (mCurrentVersion < mDefaultVersion) {
            // delete backup when updateSuccess = true and label is empty , backup is not empty
            checkTableStatus();
        }
    }

    /**
     * 处理组件升级，重新扫描完成后，数据由scan_label_backup搬迁回scan_label过程
     * @return true or false
     */
    private static boolean updateLabelsDB() {
        GLog.v(TAG, LogFlag.DL, "updateLabelsDB");
        long queryTime = System.currentTimeMillis();
        try {
            /*
            1.先刷新scan_label表中is_manual=1的数据到scan_label_backup中
            注意is_manual=1数据需要一直保留
            */
            if (!GalleryScanProviderHelper.updateLabelData()) {
                GLog.e(TAG, LogFlag.DL, "updateLabelsDB error, so interrupt updateLabelsDB");
                return false;
            }

            //2.删除scan_label表
            new DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .build().exec();

            //3.将scan_label_backup表数据批量插入scan_label
            new BulkInsertReq.Builder().setDaoType(IDao.DaoType.GALLERY).setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setConvert(aVoid -> new QueryReq.Builder<ContentValues[]>().setDaoType(IDao.DaoType.GALLERY)
                            .setTableType(GalleryDbDao.TableType.SCAN_LABEL_BACKUP)
                            .setConvert(cursor -> {
                                List<ContentValues> cvs = new ArrayList<>();
                                while (cursor.moveToNext()) {
                                    ContentValues values = new ContentValues();
                                    for (int i = 0; i < cursor.getColumnCount(); i++) {
                                        values.put(cursor.getColumnName(i), cursor.getString(i));
                                    }
                                    cvs.add(values);
                                }
                                return cvs.toArray(new ContentValues[0]);
                            }).build().exec()).build().exec();
            // 删除和插入操作(2、3)合并通知
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_LABEL, IDao.DaoType.GALLERY);
            //4.删除scan_label_backup表
            new DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_LABEL_BACKUP)
                    .build().exec();
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "updateLabelsDB, Exception = " + e);
            return false;
        } finally {
            GLog.w(TAG, LogFlag.DL, "updateLabelsDB, queryTime :" + GLog.getTime(queryTime) + "ms");
        }
        return true;
    }

    /**
     * 图片引擎和视频引擎初始化
     * @param defaultVersion 图片mode的默认版本号，为manifest.ini的LABEL_COMPONENT_DEFAULT_VERSION_NAME值
     * @param currentVersion 当前加载的图片mode版本号
     */
    private void initClassifyEngine(int defaultVersion, int currentVersion) {
        mLabelImageEnginSuccess = mClassifyEngine.initEngine(defaultVersion, currentVersion);
        mLabelVideoEnginSuccess = mVideoLabelClassifyEngine.initEngine(mContext);
    }

    private void releaseClassifyEngine() {
        mClassifyEngine.release();
        mVideoLabelClassifyEngine.release();
        mIsCharging = false;
        mUpdateSuccess = true;
        mLabelImageEnginSuccess = false;
        mLabelVideoEnginSuccess = false;
    }

    private void classifyAllMediaItems(int scanTriggerType) {
        GLog.w(TAG, LogFlag.DL, "classifyAllMediaItems start! scanTriggerType=" + scanTriggerType);
        mRemainedNewCount = batchClassifyMediaItems(mNewMediaItem, false);
        int remainedUpdateCount = batchClassifyMediaItems(mUpdateMediaItem, true);
        int scanNewCount = mNewCount - mRemainedNewCount;
        int scanUpdateCount = mUpdateMediaItem.size() - remainedUpdateCount;
        long totalScanTime = System.currentTimeMillis() - mStartTime;
        int totalScanCount = scanNewCount + scanUpdateCount;
        long averageCostTimePerItem = 0;
        if (totalScanCount != 0) {
            averageCostTimePerItem = totalScanTime / totalScanCount;
        }
        GLog.w(TAG, LogFlag.DL, "classifyAllMediaItems end! mRemainedNewCount:" + mRemainedNewCount + ", remainedUpdateCount:" + remainedUpdateCount
                + ", scanNewCount:" + scanNewCount + ", scanUpdateCount:" + scanUpdateCount + ", totalScanCount:" + (scanNewCount + scanUpdateCount)
                + ", totalScanTime:" + totalScanTime
                + ", labelScan costTime averageCostTimePerItem: " + averageCostTimePerItem);
    }

    private int batchClassifyMediaItems(List<MediaItem> items, boolean deleteOldData) {
        int remained = items.size();
        GLog.w(TAG, LogFlag.DL, "classifyMediaItem," + (deleteOldData ? " new" : " updated") + " image size: " + remained);
        ArrayList<MediaItem> list = new ArrayList<>();
        mLabelScanCount = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.LABEL_SCAN_COUNT_24h_KEY, 0);
        GLog.d(TAG, LogFlag.DL, "isCancel:" + isCancel() + ", isInterrupt:" + isInterrupt() + ", mLabelScanCount:" + mLabelScanCount
                + ", isAllowContinueScan: " + GalleryScanMonitor.isAllowContinueScan(mContext, false, mCanScanWhenGalleryRunOnTop));
        while ((remained > 0) && isAllowContinueScan(mLabelScanCount, LABEL_SCAN_COUNT_24H_MAX)) {
            int count = 0;
            while (count < ONE_CLASSIFY_COUNT) {
                if (!items.isEmpty()) {
                    list.add(items.remove(0));
                    count++;
                } else {
                    break;
                }
            }
            // 记录剩余未处理的新的MediaItems
            remained = items.size();
            // 批量标签分类
            batchClassify(list, deleteOldData);
            mIsCharging = addScanCountIfNoCharging(list.size()) || mIsCharging;
            recodeScanInfoIfNeed(list.size());
            list.clear();
            bindSmallCore();
        }
        return remained;
    }

    private void batchClassify(ArrayList<MediaItem> list, boolean deleteOldData) {
        if ((list == null) || list.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "batchClassify, list is empty!");
            return;
        }
        GLog.d(TAG, LogFlag.DL, "batchClassify, start! list size is " + list.size());
        long time = System.currentTimeMillis();
        ArrayList<LabelInfo> insertList = new ArrayList<>();
        ArrayList<MediaItem> deleteList = new ArrayList<>();
        Map<MediaItem, ArrayList<LabelInfo>> mediaLabelsMap = new HashMap<>();
        long getThumbTimeBatch = 0;
        // get classify information
        for (MediaItem mediaItem : list) {
            GLog.d(TAG, LogFlag.DL, "batchClassify, media id is " + mediaItem.getMediaId() + " " + mediaItem.getMediaType());
            if (mLabelImageEnginSuccess && mediaItem.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
                long getThumbTime = System.currentTimeMillis();
                Bitmap thumbnail = getThumbnail(mediaItem);
                if (GProperty.getDEBUG_SCAN()) {
                    GLog.d(TAG, LogFlag.DL, "batchClassify labelScan costTime getThumbTime:" + GLog.getTime(getThumbTime) + "ms");
                }
                getThumbTimeBatch += GLog.getTime(getThumbTime);
                if (thumbnail == null) {
                    GLog.w(TAG, LogFlag.DL, "batchClassify, thumbnail is null, media id is " + mediaItem.getMediaId());
                    continue;
                }
                ArrayList<LabelInfo> labelInfoList = getLabelInfoByLocalImage(mediaItem, thumbnail);
                if ((labelInfoList != null) && !labelInfoList.isEmpty()) {
                    insertList.addAll(labelInfoList);
                    deleteList.add(mediaItem);
                    mediaLabelsMap.put(mediaItem, labelInfoList);
                }
            } else if (mLabelVideoEnginSuccess && mediaItem.getMediaType() == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
                if (!GalleryScanMonitor.isAllowContinueScan(mContext, true, mCanScanWhenGalleryRunOnTop)) {
                    GLog.w(TAG, LogFlag.DL, "batchClassify,  Skip classify video Media id is " + mediaItem.getMediaId());
                    continue;
                }
                List<LabelInfo> videoInsertList = getVideoClassifyInfo(mediaItem);
                if (!videoInsertList.isEmpty()) {
                    insertList.addAll(videoInsertList);
                    deleteList.add(mediaItem);
                }
            }
        }
        /*
        only for update data, firstly delete old data
        and insert them.
        */
        if (deleteOldData) {
            GalleryScanProviderHelper.deleteClassifyMediaItem(deleteList);
        }
        classifySceneCount(insertList);
        //insert provider
        insertClassifyData(insertList);
        updateCredentialPhotoState(mediaLabelsMap);
        long batchTotalTime = System.currentTimeMillis() - time;
        int batchTotalCount = Math.min(mScanImageCount + mScanVideoCount, ONE_CLASSIFY_COUNT);
        GLog.d(TAG, LogFlag.DL,"batchClassify, end! Current already scanned ImageCount:VideoCount =  " + mScanImageCount + " : "
            + mScanVideoCount + ", labelScan costTime this batchTotalTime: " + batchTotalTime + "ms" + ", perItemScanTime:"
                + (batchTotalTime / (float)batchTotalCount) + "ms, perItemGetThumbTime:" + (getThumbTimeBatch / (float)batchTotalCount) + "ms");
    }

    /**
     * 获取视频的标签信息
     * @param mediaItem 扫描的视频
     * @return 要插入db的数据
     */
    private List<LabelInfo> getVideoClassifyInfo(MediaItem mediaItem) {
        LocalVideo localVideo = (LocalVideo) mediaItem;
        GLog.d(TAG, LogFlag.DL, "getVideoClassifyInfo come, Media id is " + mediaItem.getMediaId());

        List<LabelInfo> videoLabels = null;
        if (isVideoDurationSupportScan(localVideo)) {
            videoLabels = mVideoLabelClassifyEngine.getVideoClassifyInfo(localVideo);
            GLog.d(TAG, LogFlag.DL, "getVideoClassifyInfo duration allow, classify video: " + videoLabels);
            // 标签为null时，是检测时发生了crash，为了防止偶发的crash导致用户文件一直不能扫描出来，先不要标记已经扫描，下次重试
            if (videoLabels == null) {
                mScanVideoCount++;
                calculateScanFormatCount(mediaItem);
                return new ArrayList<>();
            }
        }
        mScanVideoCount++;
        calculateScanFormatCount(mediaItem);
        GLog.d(TAG, LogFlag.DL, "getVideoClassifyInfo classify video: " + videoLabels);

        if ((videoLabels == null) || (videoLabels.isEmpty())) {
            videoLabels = new ArrayList<>();
            videoLabels.add(mVideoLabelClassifyEngine.buildDefaultLabelInfo(localVideo));
        }

        List<Integer> ids = new ArrayList<>();
        ArrayList<LabelInfo> insertList = new ArrayList<>();
        for (LabelInfo li : videoLabels) {
            // add this if-else block to deal with the duplicate-id problem
            if (!ids.contains(li.mSceneId)) {
                ids.add(li.mSceneId);
                insertList.add(li);
            }
        }
        return insertList;
    }

    private void classifySceneCount(ArrayList<LabelInfo> insertList) {
        for (LabelInfo labelInfo : insertList) {
            if (labelInfo.mMediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
                mLabelInfoImageMap.merge(labelInfo.mSceneId, 1, Integer::sum);
            } else if (labelInfo.mMediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
                mLabelInfoVideoMap.merge(labelInfo.mSceneId, 1, Integer::sum);
            }
        }
    }

    private void checkTableStatus() {
        if (mUpdateSuccess && !isLabelTableNotEmpty()) {
            ArrayList<BaseImageInfo> imagesClassify = GalleryScanProviderHelper.getClassifyImageWithoutRecycled(false);
            if (!imagesClassify.isEmpty()) {
                try {
                    new DeleteReq.Builder()
                            .setDaoType(IDao.DaoType.GALLERY)
                            .setTableType(GalleryDbDao.TableType.SCAN_LABEL_BACKUP)
                            .build().exec();
                } catch (Exception e) {
                    GLog.e(TAG, LogFlag.DL, "checkTableStatus, Exception = " + e);
                }
            }
        }
    }

    private boolean isLabelTableNotEmpty() {
        return !GalleryScanProviderHelper.getClassifyImageWithoutRecycled(true).isEmpty();
    }

    private LabelInfo buildImageDefaultLabelInfo(MediaItem mediaItem) {
        LocalImage localImage = (LocalImage) mediaItem;
        LabelInfo info = new LabelInfo();
        info.mMediaId = localImage.getMediaId();
        info.mDateTaken = localImage.getDateTakenInMs();
        info.mDateModifiedInSec = localImage.getDateModifiedInSec();
        info.mFilePath = localImage.getFilePath();
        info.mIsRecycled = localImage.isRecycledItem();
        info.mScore = SCENE_SCORE;
        info.mSceneId = SCENE_ID_INVALID;
        info.mMediaType = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE;
        return info;
    }

    private ArrayList<LabelInfo> getLabelInfoByLocalImage(MediaItem mediaItem, Bitmap thumbnail) {
        if ((mediaItem == null) || (mediaItem.getMediaType() != GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)) {
            GLog.e(TAG, LogFlag.DL, "getLabelInfoByLocalImage, mediaItem is not localImage");
            return null;
        }
        try {
            // 下面强转可能存在ClassCastException异常，比如UriImage无法强转为LocalImage
            LocalImage localImage = (LocalImage) mediaItem;
            Bitmap thumbnailRotate = BitmapUtils.rotateBitmap(thumbnail, localImage.getRotation(), true);
            CvClassifyLabel[] classifyLabels = mClassifyEngine.getImageClassifyInfo(thumbnailRotate);
            // 如果做了旋转，主动释放一下
            if (thumbnailRotate != thumbnail) {
                BitmapUtils.recycleSilently(thumbnailRotate);
            }
            mScanImageCount++;
            calculateScanFormatCount(mediaItem);
            // 标签为null时，是检测时发生了crash，为了防止偶发的crash导致用户文件一直不能扫描出来，先不要标记已经扫描，下次重试
            if (classifyLabels == null) {
                GLog.w(TAG, LogFlag.DL, "getLabelInfoByLocalImage, imageClassify is null! " + ", media id is " + localImage.getMediaId());
                return new ArrayList<>();
            }
            ArrayList<LabelInfo> labelInfoList = new ArrayList<>();
            if (classifyLabels.length == 0) {
                labelInfoList = new ArrayList<>();
                labelInfoList.add(buildImageDefaultLabelInfo(mediaItem));
            }
            GLog.v(TAG, LogFlag.DL, "getLabelInfoByLocalImage"
                    + ", media id is " + localImage.getMediaId()
                    + ", classifyLabels mSize is " + classifyLabels.length);
            for (CvClassifyLabel label : classifyLabels) {
                LabelInfo li = new LabelInfo();
                li.mMediaId = localImage.getMediaId();
                li.mDateTaken = localImage.getDateTakenInMs();
                li.mDateModifiedInSec = localImage.getDateModifiedInSec();
                li.mFilePath = localImage.getFilePath();
                li.mIsRecycled = localImage.isRecycledItem();
                li.mScore = label.mScore;
                // 在分类之前已经切成了本地的了，所以扫描时不用再转成云词典中的id了
                li.mSceneId = label.mId;
                li.mMediaType = GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE;

                labelInfoList.add(li);
            }
            return labelInfoList;
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getLabelInfoByLocalImage, Exception = " + e);
            return new ArrayList<>();
        }
    }


    /**
     * 获取所有需要分类的媒体列表
     */
    private void getAllNeedScanMediaItems() {
        ArrayList<BaseImageInfo> images = new ArrayList<>();
        ArrayList<BaseImageInfo> imagesClassify = null;
        boolean resumeClassify = false;
        if (isInterrupt()) {
            return;
        }
        imagesClassify = GalleryScanProviderHelper.getClassifyImageWithoutRecycled(mUpdateSuccess);
        if (!imagesClassify.isEmpty()) {
            //delete last one, we don`t know last one is scan finished
            BaseImageInfo imageInfo = imagesClassify.remove(imagesClassify.size() - 1);
            ArrayList<BaseImageInfo> list = new ArrayList<>();
            list.add(imageInfo);
            /*
            每次标签扫描的的时候，都会将 scan_lab（标签）表中的最后一条数据删除，所以最后一张标签数据永远都是新增
            由于随身卡包图集需要判断是否为新增卡包，所以当最后一条数据为卡包的时候，会误判为新增，所以该处新增一个标志
            如果删除的最后一条数据为卡包，则记录该文件的mediaId
            */
            if (CardCaseUtils.isCardCaseSceneId(imageInfo.mFilePath)) {
                mDeleteLastMediaId = imageInfo.mMediaId;
            } else {
                mDeleteLastMediaId = DEFAULT_MEDIA_ID;
            }
            GalleryScanProviderHelper.deleteClassifyImage(list, mUpdateSuccess);
            refreshImagesClassify(imagesClassify, imageInfo.mFilePath);
            if (isInterrupt()) {
                return;
            }
            //interrupt and continue to scan
            images = GalleryScanProviderHelper.getAllImageForClassifyFromFileTable(mContext);
            if (images == null) {
                GLog.d(TAG, LogFlag.DL, "getAllClassifyImage, images is null as query failed, we need return!");
                return;
            }
            resumeClassify = true;
        }
        if (!resumeClassify) {
            mNewMediaItem = GalleryScanProviderHelper.getMediaItemFromMediaProvider(mContext);
        } else {
            compareAndGetScanData(images, imagesClassify, true);
        }
    }

    private void refreshImagesClassify(ArrayList<BaseImageInfo> imageInfos, String filePath) {
        if ((imageInfos == null) || (imageInfos.isEmpty()) || TextUtils.isEmpty(filePath)) {
            return;
        }
        imageInfos.removeIf(baseImageInfo -> TextUtils.equals(baseImageInfo.mFilePath, filePath));
    }

    private void compareAndGetScanData(ArrayList<BaseImageInfo> images, ArrayList<BaseImageInfo> imagesClassify, boolean needSync) {
        long time = System.currentTimeMillis();
        //data mSource sync
        HashMap<String, BaseImageInfo> imageMap = GalleryScanDataManager.translateListToMap(images);
        ArrayList<BaseImageInfo> deleteImage = new ArrayList<>();
        ArrayList<BaseImageInfo> updateImage = new ArrayList<>();
        if (needSync) {
            //compare
            for (BaseImageInfo image : imagesClassify) {
                BaseImageInfo im = imageMap.get(image.mFilePath);
                if (im == null) {
                    deleteImage.add(image);
                } else if (im.mInvalid != image.mInvalid) {
                    image.mInvalid = im.mInvalid;
                    updateImage.add(image);
                }
            }
            GLog.d(TAG, LogFlag.DL, "compareAndGetScanData, deleteImage.size: " + deleteImage.size()
                    + ", updateImage.size: " + updateImage.size());
            GalleryScanProviderHelper.deleteClassifyImage(deleteImage, mUpdateSuccess);
            GalleryScanProviderHelper.updateClassifyImageInvalid(mContext, updateImage, mUpdateSuccess);
        }

        imageMap = GalleryScanDataManager.translateListToMap(imagesClassify);
        for (BaseImageInfo image : deleteImage) {
            imageMap.remove(image.mFilePath);
        }
        //compare, get increase image and update image
        ArrayList<BaseImageInfo> newImage = new ArrayList<>();
        updateImage.clear();
        for (BaseImageInfo image : images) {
            if (!image.mInvalid) {
                BaseImageInfo im = imageMap.get(image.mFilePath);
                if (im == null) {
                    newImage.add(image);
                } else if (im.mDateModifiedInSec != image.mDateModifiedInSec) {
                    updateImage.add(image);
                }
            }
        }
        //convert ImageInfo to LocalImage
        mNewMediaItem = GalleryScanProviderHelper.convertImageToMediaItem(newImage);
        mUpdateMediaItem = GalleryScanProviderHelper.convertImageToMediaItem(updateImage);
        GLog.d(TAG, LogFlag.DL,"compareAndGetScanData, time: " + (System.currentTimeMillis() - time) + "ms");
    }

    private void insertClassifyData(ArrayList<LabelInfo> insertList) {
        if ((insertList == null) || insertList.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "insertClassifyData, insetList is empty!");
            return;
        }
        long time = System.currentTimeMillis();
        List<InsertReq> operations = buildInsertReq(insertList, mUpdateSuccess ? GalleryDbDao.TableType.SCAN_LABEL : GalleryDbDao.TableType.SCAN_LABEL_BACKUP);
        GLog.d(TAG, LogFlag.DL, "insertClassifyData, operations.size: " + operations.size() + ", insertSize: " + insertList.size());
        try {
            new BatchReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                    .addDataReqs(operations)
                    .build().exec();
            setNewCardCaseDataScannedStatus(insertList);
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "insertClassifyData", e);
        } finally {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_LABEL, IDao.DaoType.GALLERY);
        }
        GLog.d(TAG, LogFlag.DL, "insertClassifyData, cost time: " + (System.currentTimeMillis() - time) + "ms");
    }

    private void updateCredentialPhotoState(Map<MediaItem, ArrayList<LabelInfo>> mediaLabelsMap) {
        if (!ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PRIVACY_WATERMARK, false, false)) {
            GLog.d(TAG, LogFlag.DL, "[updateCredentialPhotoState] feature is not support privacy watermark");
            return;
        }
        if (mediaLabelsMap.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "[updateCredentialPhotoState], mediaLabelsMap isEmpty");
            return;
        }
        IWatermarkAbility watermarkAbility
                = ((GalleryApplication) ContextGetter.context).getAppAbility(IWatermarkAbility.class);
        if (watermarkAbility == null) {
            GLog.w(TAG, LogFlag.DL, "[updateCredentialPhotoState], watermarkAbility is null");
            return;
        }
        IPrivacyWatermarkAbility privacyWatermarkAbility = watermarkAbility.newPrivacyWatermarkAbility();
        if (privacyWatermarkAbility == null) {
            GLog.w(TAG, LogFlag.DL, "[updateCredentialPhotoState], privacyWatermarkAbility is null");
            return;
        }
        // 需要获取所有随身卡包的标签
        Set<Integer> cardCaseSceneIdSet = CardCaseUtils.getAllCardCaseSceneIdSet();
        // 批量更新扫描结果
        privacyWatermarkAbility.batchMarkEditableStates(mediaLabelsMap, cardCaseSceneIdSet);

        IOUtils.closeQuietly(privacyWatermarkAbility);
        IOUtils.closeQuietly(watermarkAbility);
    }

    private List<InsertReq> buildInsertReq(ArrayList<LabelInfo> insertList, int tableType) {
        List<InsertReq> operations = new ArrayList<>();
        int size = insertList.size();
        for (int i = 0; i < size; i++) {
            LabelInfo classifyInfo = insertList.get(i);
            ContentValues value = LabelInfo.buildInsertContentValues(classifyInfo);
            InsertReq req = new InsertReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(tableType)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setConvert(aVoid -> value)
                    .build();
            operations.add(req);
        }
        return operations;
    }

    private void setNewCardCaseDataScannedStatus(ArrayList<LabelInfo> insertList) {
        if (!FeatureUtils.isSupportCardCase()) {
            GLog.d(TAG, LogFlag.DL, "setNewCardCaseDataScannedStatus no isSupportCardCase");
            return;
        }
        if ((insertList == null) || insertList.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "setNewCardCaseDataScannedStatus insertList is nullOrEmpty");
            return;
        }
        for (LabelInfo labelInfo : insertList) {
            if (labelInfo == null) {
                return;
            }
            if (CardCaseUtils.isMovableCardCaseFile(labelInfo.mMediaType, labelInfo.mFilePath, labelInfo.mSceneId)) {
                GLog.d(TAG, LogFlag.DL, "setNewCardCaseDataScannedStatus mDeleteLastMediaId = " + mDeleteLastMediaId
                        + ", labelInfo.mMediaId = " + labelInfo.mMediaId
                        + ", labelInfo.mFilePath = " + PathMask.INSTANCE.mask(labelInfo.mFilePath)
                );
                if (mDeleteLastMediaId != labelInfo.mMediaId) {
                    CardCaseDataOperationApi.INSTANCE.setHavingNewMovableData(true);
                    //有新增，手动刷新触发数据刷新，显示小红点
                    if (FeatureUtils.isSupportCardCase() && CardCaseUtils.isTurnOnCardCaseSwitch(ContextGetter.context)) {
                        ContextGetter.context.getContentResolver().notifyChange(DataManager.CARD_CASE_RELOAD_URI, null);
                    }
                    return;
                }
            }
        }
    }

    private boolean addScanCountIfNoCharging(int count) {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return true;
        }
        GalleryScanMonitor.refreshScanRecordingIfNecessary(mContext);
        mLabelScanCount = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.LABEL_SCAN_COUNT_24h_KEY, 0) + count;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.LABEL_SCAN_COUNT_24h_KEY, mLabelScanCount);
        return false;
    }

    private void recodeScanInfoIfNeed(int count) {
        if (!GalleryScanUtils.isModelRecodeScanInfo()) {
            return;
        }
        int labelScanCountAll = ComponentPrefUtils.getIntPref(mContext, GalleryScanUtils.LABEL_SCAN_COUNT_ALL_KEY, 0) + count;
        ComponentPrefUtils.setIntPref(mContext, GalleryScanUtils.LABEL_SCAN_COUNT_ALL_KEY, labelScanCountAll);
        GalleryScanUtils.recordInfo(mContext, TAG, mLabelScanCount, labelScanCountAll);
    }

    @Override
    public int getScanType() {
        return GalleryScan.LABEL_SCAN;
    }

    @Override
    public String getSceneName() {
        return SCENE_NAME;
    }

    @Override
    public void onScan(int triggerType, ScanConfig config) {
        GLog.w(TAG, LogFlag.DL, "onScan, start scan label triggerType = " + triggerType);
        long startTime = System.currentTimeMillis();
        super.onScan(triggerType, config);
        doClassify(triggerType);
        GalleryScanMonitor.refreshScanWentWellFlag(mContext, GalleryScanUtils.PREF_LABEL_SCAN_WENT_WELL_KEY, isScanWentWell());
        GLog.w(TAG, LogFlag.DL, "onScan, label scan end, labelScan costTime:" + GLog.getTime(startTime) + "ms");
    }

    /**
     * 图片标签分类
     *
     * @param mediaItem          item
     * @param useScreenNailCache 是否使用screenNail缓存进行标签扫描
     * @return 标签集合
     */
    public List<Integer> classifyPhoto(MediaItem mediaItem, boolean useScreenNailCache) {
        return classifyPhoto(mediaItem, getThumbnail(mediaItem, useScreenNailCache));
    }

    /**
     * 图片标签分类
     *
     * @param mediaItem          item
     * @param bitmap 检测的图像
     * @return 标签集合
     */
    public List<Integer> classifyPhoto(MediaItem mediaItem, Bitmap bitmap) {
        boolean result = mClassifyEngine.initCurrentEngine();
        if (!result) {
            GLog.e(TAG, LogFlag.DL, "classifyPhoto. initCurrentEngine fail.");
            return null;
        }
        if (mediaItem == null) {
            GLog.w(TAG, LogFlag.DL, "classifyPhoto, mediaItem is null");
            return null;
        }
        if (bitmap == null) {
            GLog.w(TAG, LogFlag.DL, "classifyPhoto, thumbnail is null, media id is " + mediaItem.getMediaId());
            return null;
        }
        ArrayList<LabelInfo> labelInfoList = getLabelInfoByLocalImage(mediaItem, bitmap);
        List<Integer> idList = new ArrayList<>();
        if (labelInfoList != null) {
            for (LabelInfo info : labelInfoList) {
                idList.add(info.mSceneId);
            }
        }
        insertClassifyData(labelInfoList);
        releaseClassifyEngine();
        return idList;
    }

    @Override
    public boolean isScanWentWell() {
        // 若剩余数量为0，表示全部标签已经扫完，直接返回true
        if (mRemainedNewCount == 0) {
            return true;
        }
        // 若扫描时间超过了1个小时，那么也直接返回true
        if ((System.currentTimeMillis() - mStartTime) > GalleryScan.SCAN_WENT_WELL_COST_TIME) {
            GLog.w(TAG, LogFlag.DL,"isScanWentWell return true, because it has scanned over 1 hour");
            return true;
        }
        // 本次扫描的数量
        int scanCount = mNewCount - mRemainedNewCount;
        long totalScanTime = System.currentTimeMillis() - mStartScanTime;
        float avgScanTimePerMediaItem = 0;
        if (scanCount > 0) {
            avgScanTimePerMediaItem = totalScanTime / (float)scanCount;
        }
        GLog.d(TAG, LogFlag.DL, "isScanWentWell, mNewCount=" + mNewCount + " mRemainedNewCount=" + mRemainedNewCount + ",scanCount=" + scanCount
                + ", totalScanTime:" + totalScanTime
                + ", labelScan costTime avgScanTimePerMediaItem:" + avgScanTimePerMediaItem);
        return (scanCount > LABEL_SCAN_COUNT_24H_MAX) || (scanCount * GalleryScan.MULTIPLE_3 > mNewCount);
    }

    @Override
    public void onMonitorRecord(@NonNull MonitorEvent event, boolean reachThreshold) {
        super.onMonitorRecord(event, reachThreshold);
        markConditionsOnStartScan();
        GLog.d(TAG, LogFlag.DL, "onMonitorRecord, event = " + event + ", reachThreshold = " + reachThreshold);
        trackLabelScan(event, true);
    }

    @Override
    protected boolean runTaskIsNeedCharging() {
        // 标签不需要充电也可以执行扫描
        return false;
    }

    private void trackLabelScan(MonitorEvent event, boolean isMonitor) {
        int reasonType = getReasonType(false, mLabelScanCount, LABEL_SCAN_COUNT_24H_MAX);
        StringBuilder stringBuilder = new StringBuilder();
        for (Integer i : mLabelInfoImageMap.keySet()) {
            if (stringBuilder.length() > MAX_LENGTH) {
                break;
            }
            if (i == -1) { // image classified as unknown type
                continue;
            }
            stringBuilder.append(i).append("-").append(mLabelInfoImageMap.get(i)).append("|");
        }
        if (stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        String imageLabelCount = stringBuilder.toString();
        stringBuilder.setLength(0);

        for (Integer i : mLabelInfoVideoMap.keySet()) {
            if (stringBuilder.length() > MAX_LENGTH) {
                break;
            }
            if (i == -1) { // video classified as unknown type
                continue;
            }
            stringBuilder.append(i).append("-").append(mLabelInfoVideoMap.get(i)).append("|");
        }
        if (stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        String videoLabelCount = stringBuilder.toString();
        long endNativeHeapSize = getEndNativeHeapSize(event);
        Map<String, String> additionalMap = new HashMap<>();
        additionalMap.put(IMAGE_LABEL_COUNT, imageLabelCount);
        additionalMap.put(VIDEO_LABEL_COUNT, videoLabelCount);
        additionalMap.put(SCAN_FORMAT_COUNT, mScanFormatCount.toString());
        additionalMap.put(IS_MONITOR, String.valueOf(isMonitor));
        ScanTrackInfo scanTrackInfo = generateScanTrackItem(SCENE_NAME, endNativeHeapSize, reasonType, additionalMap);
        GLog.d(TAG, LogFlag.DL, "trackLabelScan, recordItem = " + scanTrackInfo.toString());
        ScanTrackHelper.trackScanResult(scanTrackInfo);
    }

    /**
     * 根据图片列表获取对应的标签id
     *
     * @param bitmaps: List<Bitmap>
     * @return HashSet<Long>
     */
    public HashSet<Long> getImageLabelId(List<Bitmap> bitmaps) {
        long time = System.currentTimeMillis();
        HashSet<Long> resultSet = new HashSet<>();
        boolean result = mClassifyEngine.initCurrentEngine();
        if (!result) {
            GLog.e(TAG, LogFlag.DL, "getImageClassifyInfo. initCurrentEngine fail.");
            return resultSet;
        }
        for (Bitmap bitmap : bitmaps) {
            CvClassifyLabel[] classifyLabels = mClassifyEngine.getImageClassifyInfo(bitmap);
            if ((classifyLabels == null) || (classifyLabels.length <= 0)) {
                continue;
            }
            for (CvClassifyLabel classifyLabel : classifyLabels) {
                resultSet.add((long) classifyLabel.mId);
            }
        }
        releaseClassifyEngine();
        GLog.d(TAG, LogFlag.DL, "getImageClassifyInfo"
                + " bitmaps = " + bitmaps.size()
                + " resultSet = " + resultSet.size()
                + ", total time = " + (System.currentTimeMillis() - time)
        );
        return resultSet;
    }
}