/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryScan.java
 ** Description:
 **     各业务扫描器的基类。
 **
 ** Version: 1.0
 ** Date: 2018/3/6
 ** Author: hailong.zhang@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** hailong.zhang@Apps.Gallery3D    2018/3/6     1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.manager;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Debug;

import androidx.annotation.NonNull;
import androidx.annotation.VisibleForTesting;

import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig;
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils;
import com.oplus.gallery.foundation.taskscheduling.monitor.IMonitorListener;
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEvent;
import com.oplus.gallery.foundation.taskscheduling.monitor.MonitorEventName;
import com.oplus.gallery.foundation.util.cpu.OifaceBindUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil;
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil;
import com.oplus.gallery.framework.abilities.caching.CacheOperation;
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility;
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory;
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions;
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation;
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult;
import com.oplus.gallery.framework.abilities.scan.ScanConfig;
import com.oplus.gallery.framework.abilities.scan.ScanConst;
import com.oplus.gallery.framework.abilities.scan.helpers.ScanTrackInfo;
import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

import java.util.HashMap;
import java.util.Map;

public abstract class GalleryScan implements IMonitorListener {
    protected static final int INVALID = -1;
    protected static final int FACE_SCAN = 1 << 1; // 2
    protected static final int LABEL_SCAN = 1 << 2; // 4
    protected static final int SENIOR_SELECT_SCAN = 1 << 3; // 8
    protected static final int AUTO_CROP_SCAN = 1 << 4; // 16
    protected static final int OCR_SCAN = 1 << 5; // 32
    protected static final int MEMORIES_SCAN = 1 << 6; // 64
    protected static final int MEDIA_SCAN = 1 << 7; // 128
    protected static final int MODEL_DOWNLOAD_SCAN = 1 << 8; // 256
    protected static final int HIGHLIGHT_SCAN = 1 << 9; // 512
    protected static final int MULTI_MODAL_SCAN = 1 << 10; // 1024
    @Deprecated
    protected static final int DOCUMENT_SCAN = 1 << 11; // 2048
    protected static final int DMP_SYNC_SCAN = 1 << 12; // 4096
    protected static final int O_CLOUD_SYNC_SCAN = 1 << 13; // 8192
    protected static final int XQIP_CACHE_SCAN = 1 << 14; // 16384

    /**
     * 缺陷扫描
     */
    protected static final int FLAW_SCANNER = 1 << 15; // 32768
    protected static final int EDIT_INFO_SCAN = 1 << 16; // 65536
    /**
     * 瘦身扫描
     */
    protected static final int CLOUD_SYNC_SLIMMING_SCAN = 1 << 17; // 131072
    protected static final int OCR_EMBEDDING_SCAN = 1 << 18;
    /**
     * 高清图扫描（获取更准确的人脸信息）
     */
    protected static final int HD_FACE_SCAN = 1 << 19; // 524288

    /*
     * poi扫描 1048576
     */
    protected static final int POI_SCAN = 1 << 20;
    /**
     * 旅行扫描
     */
    protected static final int TRAVEL_SCAN = 1 << 21;

    /**
     * 宠物扫描
     */
    protected static final int PET_SCAN = 1 << 22;
    /**
     * 人物与宠物合照扫描
     */
    protected static final int PERSON_PET_GROUP_SCAN = 1 << 23;

    /**
     * 学习扫描
     */
    protected static final int STUDY_SCAN = 1 << 24;

    /**
     * 旅程封面打分扫描
     */
    protected static final int TRAVEL_COVER_SCANNER = 1 << 25;

    /**
     * caption扫描
     */
    protected static final int CAPTION_SCAN = 1 << 26;

    public static final int ALL_SCAN_TYPE = FACE_SCAN // 32766
            | LABEL_SCAN
            | SENIOR_SELECT_SCAN
            | AUTO_CROP_SCAN
            | OCR_SCAN
            | MEMORIES_SCAN
            | MEDIA_SCAN
            | MODEL_DOWNLOAD_SCAN
            | HIGHLIGHT_SCAN
            | MULTI_MODAL_SCAN
            | DMP_SYNC_SCAN
            | O_CLOUD_SYNC_SCAN
            | XQIP_CACHE_SCAN
            | EDIT_INFO_SCAN
            | FLAW_SCANNER
            | CLOUD_SYNC_SLIMMING_SCAN
            | OCR_EMBEDDING_SCAN
            | HD_FACE_SCAN
            | POI_SCAN
            | PET_SCAN
            | PERSON_PET_GROUP_SCAN
            | TRAVEL_SCAN
            | STUDY_SCAN
            | TRAVEL_COVER_SCANNER
            | CAPTION_SCAN;

    /**
     * 人脸扫描和高清人脸扫描
     */
    public static final int FACE_AND_HD_FACE_SCAN_TYPE = FACE_SCAN | HD_FACE_SCAN;
    protected static final int SLEEP_TIME_EACH_BACH = 5000;
    protected static final int SCAN_WENT_WELL_COST_TIME = TimeUtils.TIME_1_HOUR_IN_MS;
    protected static final int MULTIPLE_3 = 3;
    protected static final String SCAN_FORMAT_COUNT = "scan_format_count";
    protected static final String IS_MONITOR = "is_monitor";

    private static final String TAG = "GalleryScan";
    private static final String PREF_PREFIX = "pref_scan_type_";
    private static final String PREF_LAST_SCAN_TIME_KEY_SUFFIX = "_last_scan_time_key";
    private static final String PREF_SCAN_COUNT_24h_KEY_SUFFIX = "_scan_count_24h";
    private static final int VIDEO_MIN_TIME = 2;//s
    private static final int VIDEO_MAX_TIME = 10 * 60;
    private static final long KILL_PROCESS_DELAY = 30 * 1000;

    private static final String DESC_INVALID = "INVALID";
    private static final String DESC_PET_SCAN = "PET_SCAN";
    private static final String DESC_FACE_SCAN = "FACE_SCAN";
    private static final String DESC_LABEL_SCAN = "LABEL_SCAN";
    private static final String DESC_SENIOR_SELECT_SCAN = "SENIOR_SELECT_SCAN";
    private static final String DESC_AUTO_CROP_SCAN = "AUTO_CROP_SCAN";
    private static final String DESC_OCR_SCAN = "OCR_SCAN ";
    private static final String DESC_MEMORIES_SCAN = "MEMORIES_SCAN";
    private static final String DESC_MEDIA_SCAN = "MEDIA_SCAN";
    private static final String DESC_MODEL_DOWNLOAD_SCAN = "MODEL_DOWNLOAD_SCAN";
    private static final String DESC_HIGHLIGHT_SCAN = "HIGHLIGHT_SCAN";
    private static final String DESC_MULTI_MODAL_SCAN = "MULTI_MODAL_SCAN";
    private static final String DESC_DOCUMENT_SCAN = "DOCUMENT_SCAN";
    private static final String DESC_DMP_SYNC_SCAN = "DMP_SYNC_SCAN";
    private static final String DESC_O_CLOUD_SYNC_SCAN = "O_CLOUD_SYNC_SCAN";
    private static final String DESC_XQIP_CACHE_SCAN = "XQIP_CACHE_SCAN";
    private static final String DESC_FLAW_SCANNER = "FLAW_SCANNER";
    private static final String DESC_EDIT_INFO_SCAN = "EDIT_INFO_SCAN";
    private static final String DESC_CLOUD_SYNC_SLIMMING_SCAN = "CLOUD_SYNC_SLIMMING_SCAN";
    private static final String DESC_OCR_EMBEDDING_SCAN = "OCR_EMBEDDING_SCAN ";
    private static final String DESC_HD_FACE_SCAN = "HD_FACE_SCAN";
    private static final String DESC_POI_SCAN = "POI_SCAN ";
    private static final String DESC_TRAVEL_SCAN = "TRAVEL_SCAN";
    private static final String DESC_STUDY_SCAN = "STUDY_SCAN";
    private static final String DESC_TRAVEL_COVER_SCAN = "TRAVEL_COVER_SCANNER";
    private static final String DESC_CAPTION_SCAN = "CAPTION_SCAN";

    protected ScanConfig mScanConfig;
    protected Context mContext;
    protected Map<String, Integer> mScanFormatCount = new HashMap<>();
    protected long mStartScanTime = -1;
    protected float mStartScanBattery;
    protected float mStartScanTemperature;
    protected long mStartNativeHeapSize = -1;
    protected boolean mIsFirstScan;
    protected int mScanTriggerType;
    /**
     * 本次经历过扫描的照片数，包括合格与不合格的
     */
    protected int mScanImageCount = 0;
    /**
     * 本次经历过扫描的视频数，包括合格与不合格的
     */
    protected int mScanVideoCount = 0;
    protected long mStartTime;
    protected int mAllCount;
    protected int mNewCount;
    protected int mRemainedNewCount;
    protected int mUpdateCount;

    /**
     * 扫描进度收集器，这里只是引用，实例在manager创建后传入
     */
    protected GalleryScanProgressCollector mGalleryScanProgressCollector;

    /**
     * 是否允许在相册运行在前台时扫描
     */
    protected boolean mCanScanWhenGalleryRunOnTop = false;

    /**
     * 非充电时的扫描数量，用于判断是否达到非充电数量阈值
     */
    protected int mScanCountNotInCharging = 0;

    private boolean mIsCancel;
    @GalleryScanMonitor.ReasonType
    private int mInterruptReason = GalleryScanMonitor.ReasonType.REASON_OK;

    /**
     * 输出 scanType 可阅读的描述
     *
     * @param scanType 扫描类型常量值
     * @return 扫描类型的可阅读的描述
     */
    public static String getScanTypeDesc(int scanType) {
        switch (scanType) {
            case PET_SCAN:
                return DESC_PET_SCAN;
            case FACE_SCAN:
                return DESC_FACE_SCAN;
            case LABEL_SCAN:
                return DESC_LABEL_SCAN;
            case SENIOR_SELECT_SCAN:
                return DESC_SENIOR_SELECT_SCAN;
            case AUTO_CROP_SCAN:
                return DESC_AUTO_CROP_SCAN;
            case OCR_SCAN:
                return DESC_OCR_SCAN;
            case MEMORIES_SCAN:
                return DESC_MEMORIES_SCAN;
            case MEDIA_SCAN:
                return DESC_MEDIA_SCAN;
            case MODEL_DOWNLOAD_SCAN:
                return DESC_MODEL_DOWNLOAD_SCAN;
            case HIGHLIGHT_SCAN:
                return DESC_HIGHLIGHT_SCAN;
            case MULTI_MODAL_SCAN:
                return DESC_MULTI_MODAL_SCAN;
            case DOCUMENT_SCAN:
                return DESC_DOCUMENT_SCAN;
            case DMP_SYNC_SCAN:
                return DESC_DMP_SYNC_SCAN;
            case O_CLOUD_SYNC_SCAN:
                return DESC_O_CLOUD_SYNC_SCAN;
            case XQIP_CACHE_SCAN:
                return DESC_XQIP_CACHE_SCAN;
            case FLAW_SCANNER:
                return DESC_FLAW_SCANNER;
            case EDIT_INFO_SCAN:
                return DESC_EDIT_INFO_SCAN;
            case CLOUD_SYNC_SLIMMING_SCAN:
                return DESC_CLOUD_SYNC_SLIMMING_SCAN;
            case OCR_EMBEDDING_SCAN:
                return DESC_OCR_EMBEDDING_SCAN;
            case HD_FACE_SCAN:
                return DESC_HD_FACE_SCAN;
            case POI_SCAN:
                return DESC_POI_SCAN;
            case TRAVEL_SCAN:
                return DESC_TRAVEL_SCAN;
            case STUDY_SCAN:
                return DESC_STUDY_SCAN;
            case TRAVEL_COVER_SCANNER:
                return DESC_TRAVEL_COVER_SCAN;
            case CAPTION_SCAN:
                return DESC_CAPTION_SCAN;
            default:
                return DESC_INVALID;
        }
    }

    public GalleryScan(Context context) {
        mContext = context;
        mStartTime = System.currentTimeMillis();
    }

    @VisibleForTesting
    public String getSpKeyOfLastScanTime() {
        return PREF_PREFIX + getScanType() + PREF_LAST_SCAN_TIME_KEY_SUFFIX;
    }

    protected void calculateScanFormatCount(MediaItem item) {
        Integer scanCount = mScanFormatCount.get(item.getMimeType());
        scanCount = (scanCount == null) ? 0 : scanCount;
        mScanFormatCount.put(item.getMimeType(), scanCount + 1);
    }

    protected ScanTrackInfo generateScanTrackItem(String scene, long endNativeHeapSize, int reasonType, Map<String, String> additionalMap) {
        return new ScanTrackInfo(
                scene,
                mStartScanTime,
                System.currentTimeMillis(),
                mStartScanBattery,
                BatteryStatusUtil.getCurrentBatteryPercent(mContext),
                mIsFirstScan,
                mScanTriggerType,
                mScanImageCount,
                mScanVideoCount,
                mStartScanTemperature,
                TemperatureUtil.getCurrentTemperature(),
                mStartNativeHeapSize,
                endNativeHeapSize,
                reasonType,
                BatteryStatusUtil.isBatteryInCharging(false),
                additionalMap);
    }

    private boolean isMemoryEvent(MonitorEvent event) {
        if (event != null) {
            return MonitorEventName.MEMORY.equals(event.getEventName());
        }
        return false;
    }

    protected long getEndNativeHeapSize(MonitorEvent event) {
        long endNativeHeapSize = 0L;
        if (isMemoryEvent(event)) {
            endNativeHeapSize = (long) event.getValue();
        } else {
            endNativeHeapSize = Debug.getNativeHeapSize();
        }
        return endNativeHeapSize;
    }

    public void onCancel() {
        mIsCancel = true;
    }

    public void onInterrupt(@GalleryScanMonitor.ReasonType int reason) {
        mInterruptReason = reason;
    }

    public boolean isCancel() {
        return mIsCancel;
    }

    public boolean isInterrupt() {
        return mInterruptReason != GalleryScanMonitor.ReasonType.REASON_OK;
    }

    public long getLastScanTime() {
        return GalleryScanUtils.getLastScanTime(mContext, getSpKeyOfLastScanTime());
    }

    protected void updateLastScanTime() {
        if (!isInterrupt() && mScanConfig.isRecordTime()) {
            GalleryScanUtils.setLastScanTime(mContext, getSpKeyOfLastScanTime(), System.currentTimeMillis());
        }
    }

    /**
     * 从sp读取未充电状态下扫描的图片数量并赋值到成员变量
     */
    protected void readScanCountNotInCharging(String spKey) {
        mScanCountNotInCharging = ComponentPrefUtils.getIntPref(mContext, spKey, 0);
    }

    /**
     * 如果是未充电状态下，则将扫描数量累加并保存到sp中
     */
    protected void addScanCountIfNoCharging(String spKey, int count) {
        if (BatteryStatusUtil.isBatteryInCharging(false)) {
            return;
        }
        mScanCountNotInCharging += count;
        ComponentPrefUtils.setIntPref(mContext, spKey, mScanCountNotInCharging);
        GalleryScanMonitor.refreshScanRecordingIfNecessary(mContext);
    }

    /**
     * 中断扫描，发送中断扫描事件到进度监听器, 用于前端业务发起的扫描
     *
     * @param scanType 扫描类型常量值
     * @param reason   中断原因
     */
    protected void onInterruptScan(int scanType, int reason) {
        if ((mGalleryScanProgressCollector != null) && mCanScanWhenGalleryRunOnTop) {
            mGalleryScanProgressCollector.onInterruptScan(scanType, reason, mContext);
        }
    }

    /**
     * 更新进度监听器，用于前端业务发起的扫描
     *
     * @param scanType 扫描类型常量值
     */
    protected void updateProgress(int scanType) {
        if (mGalleryScanProgressCollector != null && mCanScanWhenGalleryRunOnTop) {
            // 计算用于进度显示的最大图片数，不超过配置限制
            int total = Math.min(mAllCount, mScanConfig.getImageCount());

            // 已完成扫描的数量不能超过最大值，防止进度溢出
            int completed = Math.min(mScanImageCount, total);
            // 如果扫描已取消，不在更新进度条
            if (isInterrupt()) {
                return;
            }
            mGalleryScanProgressCollector.updateProgress(scanType, total, completed);
        }
    }

    /**
     * 完成扫描
     *
     * @param scanType 扫描类型常量值
     */
    protected void completedScan(int scanType) {
        if (mGalleryScanProgressCollector != null && mCanScanWhenGalleryRunOnTop) {
            mGalleryScanProgressCollector.markAsCompleted(scanType);
        }
    }

    public abstract int getScanType();

    /**
     * 场景名称
     *
     * @return 返回场景名称
     */
    public abstract String getSceneName();

    public void onScan(int triggerType, ScanConfig config) {
        mScanTriggerType = triggerType;
        mScanConfig = config;
        mScanVideoCount = 0;
        mScanImageCount = 0;

        bindSmallCore();
    }

    /**
     * 获取扫描任务状态的代码。
     *
     * @param needCharging 扫描时是否需要处于充电状态
     * @param scanCount    将要扫描的项目数量
     * @param maxScanCount 扫描项目数量的上限
     * @return 返回状态类型 Int
     * <p> 0 _ REASON_OK：正常结束 </p>
     * <p> 1 _ REASON_POWER_LOW：电量过低。充电时，电量 < 30% ；非充电时，电量 < 70% </p>
     * <p> 2 _ REASON_POWER_CONSUMPTION：电量消耗超过 2% </p>
     * <p> 3 _ REASON_RUN_2_HOURS：非充电时，扫描运行超过 2 个小时 </p>
     * <p> 4 _ REASON_TEMPERATURE_HIGH：持续扫描时，电池温度过高，>= 38℃ </p>
     * <p> 5 _ REASON_EXCEEDING_MAX_COUNT：数量超限 </p>
     * <p> 6 _ REASON_SCREEN_ON：亮屏 + 已解锁/无锁 </p>
     * <p> 7 _ REASON_SYS_INTERRUPT：系统中断扫描 </p>
     * <p> 8 _ REASON_LOW_POWER_CONSUMPTION：非充电省功耗 </p>
     */
    protected int getReasonType(boolean needCharging, int scanCount, int maxScanCount) {
        if (isInterrupt()) {
            return mInterruptReason;
        } else {
            return GalleryScanMonitor.getReasonTypeOnContinue(
                    mContext,
                    needCharging,
                    scanCount,
                    maxScanCount,
                    GalleryScanUtils.DEFAULT_SCAN_TIME,
                    false
            );
        }
    }

    /**
     * 记录下扫描前的状态，其中包括：启动时间、电量、电池温度、内存
     */
    protected void markConditionsOnStartScan() {
        mStartScanTime = System.currentTimeMillis();
        mStartScanBattery = BatteryStatusUtil.getCurrentBatteryPercent(mContext);
        mStartScanTemperature = TemperatureUtil.getCurrentTemperature();
        mStartNativeHeapSize = Debug.getNativeHeapSize();
    }

    public boolean isScanWentWell() {
        return true;
    }

    /**
     * Scanning while charging causes the temperature to be too high and the phone to be hot.
     * So you need to slow down the scanning process.
     */
    public void sleep(int timeInMillisecond) {
        try {
            Thread.sleep(timeInMillisecond);
        } catch (InterruptedException e) {
            GLog.e(TAG, "sleep", e);
        }
    }

    /**
     * 云端设置是否执行低功耗扫描
     *
     * @return true
     */
    protected boolean isCloudAllowLowConsumptionScan() {
        String lowPowerConsumptionEnable = "boolean_low_power_consumption_enable";
        String defaultEnable = "1";  //默认为true
        Object cloudEnable = CloudParamConfig.INSTANCE.getDefaultGroupValue(
                ScanConst.MODULE_NAME,
                lowPowerConsumptionEnable,
                defaultEnable,
                CloudParamConfig.DEFAULT_GROUP_NAME
        );
        GLog.d(TAG, "isCloudAllowLowConsumptionScan " + cloudEnable);
        return (cloudEnable != null) && (boolean) cloudEnable;
    }

    protected boolean isVideoAllowScan(LocalVideo video) {
        return GalleryScanMonitor.isAllowContinueScan(mContext, true, mCanScanWhenGalleryRunOnTop)
                && isVideoDurationSupportScan(video)
                // 前台触发扫描的话，不扫视频
                && !mCanScanWhenGalleryRunOnTop;
    }

    protected boolean isVideoDurationSupportScan(LocalVideo video) {
        int time = video.getDurationInSec();
        return (time >= VIDEO_MIN_TIME) && (time <= VIDEO_MAX_TIME);
    }

    protected boolean isAllowContinueScan(int scanCountWithoutCharging, int maxScanCountInOneDay, boolean needCharging) {
        return !isCancel()
                && !isInterrupt()
                && GalleryScanMonitor.isCountAllow(scanCountWithoutCharging, maxScanCountInOneDay)
                && GalleryScanMonitor.isCountAllow(mScanImageCount, mScanConfig.getImageCount())
                && GalleryScanMonitor.isCountAllow(mScanVideoCount, mScanConfig.getVideoCount())
                && GalleryScanMonitor.isAllowContinueScan(mContext, needCharging, mCanScanWhenGalleryRunOnTop);
    }

    protected boolean isAllowContinueScan(int maxScanCountInOneDay) {
        return isAllowContinueScan(mScanCountNotInCharging, maxScanCountInOneDay);
    }

    protected boolean isAllowContinueScan(int scanCountWithoutCharging, int maxScanCountInOneDay) {
        return isAllowContinueScan(scanCountWithoutCharging, maxScanCountInOneDay, false);
    }

    @Override
    public void onMonitorRecord(@NonNull MonitorEvent event, boolean reachThreshold) {
        if (reachThreshold) {
            GalleryScanMonitor.killProcessIfNeed(mContext, KILL_PROCESS_DELAY);
        }
    }

    protected void bindSmallCore() {
        // 在简易模式下，为方便测试，扫描线程不绑小核，提升扫描速度。
        if (GalleryScanUtils.isNormalScanPolicy()) {
            OifaceBindUtils.getInstance().bindTask();
        }
    }

    /**
     * 获取待扫描的bitmap，有缓存取缓存，没缓存重新解码
     *
     * @param mediaItem item
     * @return bitmap
     */
    protected Bitmap getThumbnail(MediaItem mediaItem) {
        return getThumbnail(mediaItem, false);
    }

    /**
     * 获取待扫描的bitmap，有缓存取缓存，没缓存重新解码
     * 这里为了避免大图扫描重新解码产生的内存开销，如果是大图发起的扫描任务，就使用大图的screenNail去扫
     *
     * @param mediaItem          item
     * @param useScreenNailCache 是否使用screenNail缓存进行
     * @return bitmap
     */
    protected Bitmap getThumbnail(MediaItem mediaItem, boolean useScreenNailCache) {
        IResourcingAbility resourcingAbility = ((GalleryApplication) mContext.getApplicationContext())
                .getAppAbility(IResourcingAbility.class);
        ResourceKey resourceKey = ResourceKeyFactory.createResourceKey(mediaItem);
        ResourceGetOptions options = new ResourceGetOptions(
                useScreenNailCache ? ThumbnailSizeUtils.getFullThumbnailKey() : ThumbnailSizeUtils.TYPE_THUMBNAIL,
                -1,
                -1,
                useScreenNailCache ? CropParams.noCrop() : CropParams.centerRectCrop(),
                CacheOperation.ReadWriteAllCache.INSTANCE,
                SourceOperation.ReadLocal.INSTANCE
        );
        if ((resourcingAbility != null) && (resourceKey != null)) {
            try {
                ImageResult<Bitmap> result = resourcingAbility.requestBitmap(resourceKey, options, null, null);
                if (result != null) {
                    return result.getResult();
                }
            } finally {
                resourcingAbility.close();
            }
        }
        return null;
    }

    /**
     * task执行的时候是否需要充电
     *
     * @return true or false
     */
    protected abstract boolean runTaskIsNeedCharging();
}
