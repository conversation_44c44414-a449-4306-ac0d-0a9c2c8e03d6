/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EditInfoScanner.kt
 ** Description : 编辑表扫描器
 ** Version     : 1.0
 ** Date        : 2024/6/11
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D         2024/6/11     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.edit

import android.content.Context
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.photoeditor.ParametricDataEntity
import com.oplus.gallery.business_lib.photoeditor.ParametricProjectDataHelper
import com.oplus.gallery.foundation.database.store.GalleryStore.EditInfoColumns
import com.oplus.gallery.foundation.dbaccess.bean.BatchResult
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PROJECT_SAVE
import com.oplus.gallery.framework.abilities.parameterization.IParameterizeAbility
import com.oplus.gallery.framework.abilities.parameterization.ParametricConst
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.framework.abilities.scan.ScanConst
import com.oplus.gallery.framework.abilities.scan.manager.GalleryScan
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils

/**
 * 编辑表扫描器
 */
class EditInfoScanner(context: Context?) : GalleryScan(context) {

    override fun getScanType(): Int {
        return EDIT_INFO_SCAN
    }

    override fun getSceneName(): String = SCENE_NAME

    override fun onScan(triggerType: Int, config: ScanConfig?) {
        GLog.d(TAG, "onScan, triggerType=$triggerType")
        super.onScan(triggerType, config)
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PROJECT_SAVE, false).not()) {
            GLog.d(TAG, "onScan, not support feature!")
            return
        }

        runCatching {
            if (isEnable.not()) {
                GLog.d(TAG, LogFlag.DF, "onScan, isEnable = false, return")
                return
            }
            // LocalMedia 是否扫描完成，后续逻辑依赖 tagFlags 准确
            if (ApiDmManager.getMediaDBSyncDM().isExpandDataParseFinished().not()) {
                GLog.d(TAG, "onScan, expend data parse not finished!")
                return
            }

            // 检查是否有没有记录到 edit_info 表里的参数化项目，有则插入
            insertNewProject()

            // 删除失效的参数化项目
            deleteInvalidProject()

            // 删除私有目录下的冗余文件
            deleteRedundantFiles()
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[onScan] scan failed! error=", it)
        }
    }

    override fun runTaskIsNeedCharging(): Boolean {
        return false
    }

    /**
     * 插入新的参数化项目
     */
    private fun insertNewProject() {
        // 查找不在 edit_info 里，但是 tagFlags 为 Edit_RENDERING 的图
        ParametricProjectDataHelper.queryNewProject()?.let { idDataMap ->
            GLog.d(TAG, "insertNewProject, new parametric project size=${idDataMap.size}")
            mContext.getAppAbility<IParameterizeAbility>()?.use {
                // 校验并插入数据库
                it.validateNewProjectAndInsert(idDataMap)
            }
        }?.also {
            GLog.d(TAG, "insertNewProject, ${BatchResult.getResultDesc(it)}")
        }
    }

    /**
     * 删除失效的参数化项目
     */
    private fun deleteInvalidProject() {
        val deleteProjectList = arrayListOf<ParametricDataEntity>()

        // 查找出在 edit_info 但是不在 local_media 表的参数化项目
        ParametricProjectDataHelper.queryInEditNotInLocalProject()?.filter {
            // 兼容 LocalMedia 异常的情况，比如媒体库重扫， LocalMedia 里没有数据，增加判断如果文件存在但 LocalMedia 没有不做删除
            File.checkExist(it.filePath).not()
        }?.let {
            GLog.d(TAG) { "scanRedundantProject, not in local size=${it.size}" }
            deleteProjectList.addAll(it)
        }

        // 查找出在 edit_info 但是 tagFlags 已经不是参数化的 参数化项目
        ParametricProjectDataHelper.queryTagFlagsInvalidProject()
            ?.also {
                GLog.d(TAG) { "scanRedundantProject, tagFlag invalid size=${it.size}" }
                deleteProjectList.addAll(it)
            }
        if (deleteProjectList.isEmpty()) {
            GLog.d(TAG, "scanRedundantProject, no redundant project")
            return
        }

        // 删除 db 记录
        deleteProjectList.mapNotNull { entity -> entity.filePath?.let { ParametricProjectDataHelper.getDeleteReq(it, true) } }
            .let { ParametricProjectDataHelper.batchDelete(it) }
            .also { GLog.d(TAG) { "scanRedundantProject, ${BatchResult.getResultDesc(it)}" } }

        if (GProperty.DEBUG_PARAMETRIC_PROJECT) {
            deleteProjectList.forEach { GLog.d(TAG, "scanRedundantProject, delete =$it") }
        }

        // 删除文件
        safeDeleteFile(deleteProjectList.mapNotNull { it.originFileSubPath }, EditInfoColumns.ORIGINAL_DATA)
        safeDeleteFile(deleteProjectList.mapNotNull { it.contentEditFileSubPath }, EditInfoColumns.CONTENT_EDIT_DATA)
    }

    /**
     * 安全的删除参数化项目在私有目录的文件
     * 删文件之前检查数据库中是否还有其他引用，如果有，不能删除
     */
    private fun safeDeleteFile(subFilePathList: List<String>, fileColumns: String) {
        ParametricProjectDataHelper.filterNotExistData(subFilePathList, fileColumns)
            ?.filter { it.isNotEmpty() }
            .also { GLog.d(TAG, LogFlag.DL) { "safeDeleteFile, needDeleteSize=${it?.size}" } }
            ?.count { subFilePath -> File(FilePathUtils.getDataFilePathFromSubPath(subFilePath)).delete() }
            .also { GLog.d(TAG) { "safeDeleteFile, fileColumns=$fileColumns, count=$it" } }
    }

    /**
     * 删除不在 db 中的冗余文件
     */
    private fun deleteRedundantFiles() {
        // 找目录下的文件
        val originFileDir = File(mContext.filesDir, ParametricConst.DIR_PROJECT_ORIGINAL_FILE)
        val diskFileList = originFileDir.listFiles()?.map { it.absolutePath }
        if (diskFileList.isNullOrEmpty()) {
            GLog.d(TAG, "deleteRedundantFiles, no disk file!")
            return
        }

        // 找 db 里存在的文件
        val dbFileSet = ParametricProjectDataHelper.queryAllProject()
            .flatMap { listOfNotNull(it.getOriginalFilePath(), it.getContentEditFilePath()) }
            .toSet()
        GLog.d(TAG) { "deleteRedundantFiles, dbFileSet=${dbFileSet.size}, diskFileList=${diskFileList.size}" }

        // 找出不在 db 的文件，删除
        (diskFileList - dbFileSet)
            .also { GLog.d(TAG) { "deleteRedundantFiles, redundantFile count=${it.size}" } }
            .count { filePath -> File(filePath).delete() }
            .also { GLog.d(TAG) { "deleteRedundantFiles, delete count=$it" } }
    }

    companion object {
        private const val TAG = ScanConst.TAG + "EditInfoScanner"
        private const val SCENE_NAME = "EditInfoScanner"
        var isEnable = true
    }
}