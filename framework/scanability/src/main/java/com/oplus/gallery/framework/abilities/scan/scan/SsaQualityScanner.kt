/*********************************************************************************
 ** Copyright (C), 2019-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SsaQualityScanner.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/13
 ** Author      : zhu<PERSON><PERSON>@Apps.Gallery
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON>@Apps.Gallery             2023/3/13      1.0       build
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.scan

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectDBOperation
import com.oplus.gallery.framework.abilities.scan.template.SsaQualityModelLoadingTemplate
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SeniorSelectModelConfig
import com.oplus.gallery.framework.abilities.scan.template.modelconfig.SsaQualityModelConfig
import com.oplus.gallery.framework.abilities.scan.utils.SingletonHolder

/**
 * 相似图美学评价 扫描
 * 说明：ssa（相似图美学评价，similar aesthetic assessment 简写）
 */
class SsaQualityScanner private constructor(context: Context) :
    BaseQualityScanner(context) {
    override val tag: String = "SsaQualityScanner"

    override val modelConfig: SeniorSelectModelConfig = SsaQualityModelConfig(context)

    override val modelLoadingTemplate: ModelLoadingTemplate = SsaQualityModelLoadingTemplate(context, modelConfig)

    override fun insertScore(newFeatureMap: Map<String, Float>) {
        // do nothing
    }

    override fun getQualityScoreMap(dataList: List<String>): Map<String, Float> {
        return SeniorSelectDBOperation.getSsaQualityScoreMap(dataList, version) ?: emptyMap()
    }

    companion object {
        fun getInstance(context: Context): SsaQualityScanner {
            return SingletonHolder(::SsaQualityScanner).getInstance(context)
        }
    }
}