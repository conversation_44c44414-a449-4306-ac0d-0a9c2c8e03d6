/***********************************************************
 * Copyright (C), 2020-2024, OPLUS Mobile Comm Corp., Ltd.
 * File: ScanPluginSupportCheckStrategy
 * Description:
 * Version: 1.0
 * Date : 2025/4/7
 * Author: ********
 *
 * ----------------------Revision History: --------------------
 *  <author>            <date>         <version >    <desc>
 *  ********            2025/4/7          1.0       first add
 ****************************************************************/
package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPrivacyInterceptor
import com.oplus.gallery.foundation.ai.IAIApi
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.download.IAIUnitPluginState
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor

/**
 * 扫描所需的算法插件通用策略类
 * 实现此类并重新 getInterceptors。将对应插件下载前的预检查拦截器实现即可。
 * 此类会自动拼接 AIUnit 协议拦截、和最后的下载。
 *
 * @param aiApi 对应算法插件的能力实现类
 */
abstract class ScanPluginSupportCheckStrategy(private val aiApi: IAIApi?) {

    /**
     * 插件状态
     */
    abstract val pluginState: IAIUnitPluginState

    /**
     * 拦截器链
     */
    private var chain: RealInterceptorChain? = null

    /**
     * 检查算法插件是否支持
     */
    @Suppress("TooGenericExceptionCaught")
    open fun checkSupportability(context: Context) {
        try {
            GLog.d(getTag(), LogFlag.DL, "checkSupportability, $aiApi start")
            if (NetworkMonitor.isWifiValidated() && aiApi?.isDownloading == true) {
                GLog.w(getTag(), LogFlag.DL, "checkSupportability, NetworkMonitor.isWifiValidated() = ${NetworkMonitor.isWifiValidated()}, " +
                        "ability?.isDownloading = ${aiApi.isDownloading}")
                return
            }
            // 更新配置：插件下载状态，AIUnit用户协议状态，AIUnit配置可用性
            pluginState.sync(context)
            context.getAppAbility<ISettingsAbility>()?.use {
                updateBlockingConfig(context, it)
            }
            val bundle = Bundle()
            val interceptors = getInterceptors(context).toMutableList()
            interceptors.addAll(listOf(
                AIUnitPrivacyInterceptor(context, false),
                AIUnitModelDownloadInterceptor(
                    context,
                    pluginState,
                    null,
                    enableUI = { false },
                    startDownload = {
                        setAbilityDownloading(true)
                    }
                )
            ))
            chain = RealInterceptorChain(
                interceptors,
                onSuccessCallback = { setAbilityDownloading(false) },
                onFailCallback = { setAbilityDownloading(false) }
            )
            chain?.proceed(bundle)
        } catch (e: Exception) {
            GLog.e(getTag(), LogFlag.DL, "checkSupportability " + e.message, e)
            setAbilityDownloading(false)
        } finally {
            destroy()
        }
    }

    /**
     * 更新需要写入的配置项，此配置项需要在BlockingConfigWriter配置表内置有
     */
    abstract fun updateBlockingConfig(context: Context, settingsAbility: ISettingsAbility)

    /**
     * 获取检查策略中的拦截项
     * @return 拦截器列表
     */
    abstract fun getInterceptors(context: Context): List<IInterceptor<Bundle, Unit>>

    /**
     * 设置算法能力下载状态
     * @param value true:下载中，false：非下载中状态
     */
    open fun setAbilityDownloading(value: Boolean) {
        GLog.d(getTag(), LogFlag.DL) { "setAbilityDownloading, value: $value" }
        aiApi?.isDownloading = value
    }

    /**
     * 获取日志打印 TAG
     */
    abstract fun getTag(): String

    /**
     * 资源释放
     */
    fun destroy() {
        GLog.e(getTag(), LogFlag.DL, "destroy ")
        chain?.destroy()
        chain = null
    }
}