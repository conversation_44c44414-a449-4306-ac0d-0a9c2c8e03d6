/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SimilarFeatureModelLoadingTemplate.kt
 ** Description : 相似特征ModelLoadingTemplate
 ** Version     : 1.0
 ** Date        : 2024/3/22
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/3/22      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.template

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * 相似特征ModelLoadingTemplate
 */
class SimilarFeatureModelLoadingTemplate(context: Context, modelConfig: ModelConfig) : SeniorSelectModelLoadingTemplate(context, modelConfig) {
    override val tag: String = TAG

    override val modelName: String = ModelName.SIMILAR_FEATURE_SOURCE

    override val modelFileName: String = SIMILAR_FEATURE_MODEL_NAME

    companion object {
        private const val TAG = "SimilarFeatureModelLoadingTemplate"

        /**
         * 加载模型文件
         */
        private const val SIMILAR_FEATURE_MODEL_NAME = "similar_feature.model"
    }
}