/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - PetPluginSupportCheckStrategy.kt
 ** Description: 宠物识别模型检查策略
 ** Version: 1.0
 ** Date : 2025/4/7
 ** Author: ********
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** ********      2025/4/7       1.0          created
 ***************************************************************/
package com.oplus.gallery.framework.abilities.scan.modeldownload

import android.content.Context
import android.os.Bundle
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.foundation.ai.IAIApi
import com.oplus.gallery.foundation.uiflowinterceptor.ConditionInterceptor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.AiPets.AI_PETS_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.AiPets.AI_PETS_PLUGIN_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_UNIT_CONFIG_AVAILABLE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PET_CLASSIFY
import com.oplus.gallery.framework.abilities.download.IAIUnitPluginState

/**
 * 宠物识别模型检查策略
 * 目前策略：
 * 1. PetFeatureSupportInterceptor 宠物识别 feature 支持。
 * 2. AIUnitPrivacyInterceptor aiunit 隐私协议。
 * 3. AIUnitModelDownloadInterceptor 下载。静默下载。
 */
internal class PetPluginSupportCheckStrategy(
    aiApi: IAIApi?,
    override val pluginState: IAIUnitPluginState = AIUnitPluginState(
        AIUnitPlugin.AI_PETS.downloadPlugin,
        AI_PETS_PLUGIN_DOWNLOAD_STATE,
        AI_PETS_PLUGIN_SHOW_TIMESTAMP
    )
) : ScanPluginSupportCheckStrategy(aiApi) {

    override fun updateBlockingConfig(context: Context, settingsAbility: ISettingsAbility) {
        settingsAbility.updateBlockingConfig(context, IS_AGREE_AI_UNIT_PRIVACY)
    }

    /**
     * 获取下载前置条件拦截器
     */
    override fun getInterceptors(context: Context) = listOf(
        PetFeatureSupportInterceptor()
    )

    override fun getTag(): String = TAG

    companion object {
        private const val TAG = "PetPluginSupportCheckStrategy"
    }
}

/**
 * 宠物Feature拦截器
 */
internal class PetFeatureSupportInterceptor : ConditionInterceptor() {
    override fun onCheckCondition(param: Bundle): Boolean {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY, defValue = true, expDefValue = false).also {
            GLog.d(TAG, LogFlag.DL) { "onCheckCondition. feature is support ai_pets scan: $it" }
        }
    }

    companion object {
        private const val TAG = "PetFeatureSupportInterceptor"
    }
}