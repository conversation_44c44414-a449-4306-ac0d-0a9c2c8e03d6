/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** <p>
 ** File: - GalleryScanUtils.java
 ** Description:
 ** <p>
 **
 ** Version: 1.0
 ** Date:2016-11-23
 ** Author:xiuhua.ke
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiuhua.ke                      2016-11-23     1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.utils;

import static com.oplus.gallery.foundation.util.debug.LogFlag.DL;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V2;
import android.app.AlarmManager;
import android.content.Context;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil;
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties;
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil;
import com.oplus.gallery.framework.abilities.config.IConfigAbility;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.Dir;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

public class GalleryScanUtils {
    /**
     * 人物聚类后设置展示的人物图集最小的照片数量（首次聚类不受限制）
     */
    public static final int GROUP_NUM_FACE_THRESHOLD = 8;
    /**
     * 未聚合的数据
     */
    public static final int GROUP_ID_0 = 0; //had not group yet
    /**
     * 只有 1 张图的人脸数据，TA 会被重新聚合
     */
    public static final int GROUP_ID_1 = 1; //only one face will group to this, we will regroup them
    /**
     * 不能确定脸部是否准确
     */
    public static final int GROUP_ID_2 = 2; //can not sure the face is exactness
    /**
     * 正常聚合的id起点
     */
    public static final int GROUP_ID_3 = 3; //normal group base

    public static final int VIRTUAL_BASE_GROUP_ID_FOR_SINGLE_FACE = (Integer.MAX_VALUE / 2);//see GROUP_ID_1

    // 记录扫描task触发时间以及人物、标签的扫描进展情况
    public static final String PREF_SCAN_TASK_START_TIME_KEY = "pref_scan_task_start_time_key";
    public static final String PREF_FACE_SCAN_WENT_WELL_KEY = "pref_face_scan_went_well_key";
    public static final String PREF_LABEL_SCAN_WENT_WELL_KEY = "pref_label_scan_went_well_key";

    // number of scanned media and scan time in one day if no charging
    public static final String FACE_SCAN_COUNT_24h_KEY = "face_scan_count_24h";
    public static final String PET_SCAN_COUNT_24H_KEY = "pet_scan_count_24h";
    public static final String LABEL_SCAN_COUNT_24h_KEY = "label_scan_count_24h";
    public static final String STUDY_SCAN_COUNT_24H_KEY = "study_scan_count_24h";
    public static final String MEMORIES_SCAN_COUNT_24h_KEY = "memories_scan_count_24h";
    public static final String OCR_SCAN_COUNT_24h_KEY = "ocr_scan_count_24h_key";
    public static final String SENIOR_SELECT_SCAN_COUNT_24H_KEY = "senior_select_scan_count_24h";
    public static final String AUTO_CROP_SCAN_COUNT_24H_KEY = "auto_crop_scan_count_24h";
    public static final String SCAN_START_TIME_24h_KEY = "scan_start_time_24h";
    public static final String SCAN_COST_TIME_24h_KEY = "scan_cost_time_24h_key";

    //all scan count to recode scan info
    public static final String FACE_SCAN_COUNT_ALL_KEY = "face_scan_count_all";
    public static final String PET_SCAN_COUNT_ALL_KEY = "pet_scan_count_all";
    public static final String LABEL_SCAN_COUNT_ALL_KEY = "label_scan_count_all";
    public static final String STUDY_SCAN_COUNT_ALL_KEY = "study_scan_count_all";
    public static final String MEMORIES_SCAN_COUNT_ALL_KEY = "memories_scan_count_all";
    public static final String OCR_SCAN_COUNT_ALL_KEY = "ocr_scan_count__all";
    private static final String PREF_FIRST_SCAN_KEY_SUFFIX = "_first_scan";
    private static final String PREF_PREFIX = "pref_scan_type_";

    //these parameters are used for hiding face albums, which all faces can not to show.
    public static final int LIMIT_FACE_COUNT_OF_GROUP_PHOTO = 5;
    public static final float SINGLE_FACE_DIVIDE_THUMB_AREA_RATIO = 0.0f;
    public static final float GROUP_FACE_DIVIDE_THUMB_AREA_RATIO = 0.05f;
    public static final long NATIVE_HEAP_FREE_SIZE_INCREASE_MAX = 20 * 1024 * 1024;
    public static final long DEFAULT_SCAN_TIME = -1;
    public static final int DEFAULT_SCAN_COUNT = 0;
    public static final float SINGLE_FACE_RATIO = 0;
    public static final float IS_SAME_FACE_RATIO = 0.7f;
    public static final long INIT_FAILED_MAX_INTERVAL_TIME = AlarmManager.INTERVAL_DAY * 15;
    //for sample cluster
    public static final int SCAN_TYPES = GallerySystemProperties.getInt("debug.gallery.scan.types", -1);
    public static final boolean DEBUG_SCAN_LABEL_UPDATE = GallerySystemProperties.getBoolean("debug.gallery.scan.label.update", false);

    private static final String TAG = "GalleryScanUtils";
    private static final long ONE_MINUTE = TimeUtils.TIME_1_MIN_IN_MS; //1min
    private static final String RECORDE_SCAN_INFO_FILE = "scan_info.csv";
    private static final String COMMA = ",";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    private static final boolean MODEL_LABEL = GallerySystemProperties.getBoolean("debug.gallery.label.test", false);
    // true:scan loop model; false: scan normal mode
    private static final boolean MODEL_TEST = GallerySystemProperties.getBoolean("debug.gallery.scan.testmodel", false);
    // true: normal policy model; false: easy policy model
    private static final boolean MODEL_SCAN_POLICY = GallerySystemProperties.getBoolean("debug.gallery.scan.normalpolicy", true);
    //true: count test model; false: count normal model
    private static final boolean MODEL_COUNT = GallerySystemProperties.getBoolean("debug.gallery.scan.count.test", false);
    //true: cost time test model; false: cost time normal model
    private static final boolean MODEL_COST_TIME = GallerySystemProperties.getBoolean("debug.gallery.scan.cost.time.test", false);
    private static final boolean MODEL_VERIFY = GallerySystemProperties.getBoolean("debug.gallery.model.verify", false);
    private static final boolean MODEL_RECODE_SCAN_INFO = GallerySystemProperties.getBoolean("debug.gallery.scan.recode.info", false);
    private static final String CRASH_CACHED_FILE = "crash.cb";
    private static final String PREF_ALL_SCAN_KEY = "pref_all_scan_key";
    private static final String PREF_FIRST_GROUP_KEY = "pref_first_group_key";
    private static final String PREF_FIRST_LABEL_SCAN_KEY = "pref_first_label_scan_key";
    private static final String PREF_FIRST_OCR_SCAN_KEY = "pref_first_ocr_scan_key";
    private static final String PREF_FACE_SCORE_UPDATE = "pref_face_score_update";
    private static final String PREF_LAST_MANUAL_GROUP_TIME_KEY = "pref_last_manual_group_time_key";
    private static final String PREF_CURRENT_OCR_SCAN_VERSION = "pref_current_ocr_scan_version";
    private static final String PREF_CURRENT_FLAW_SCAN_VERSION = "pref_current_flaw_scan_version";
    private static final String RESPONSE_REMOTE_RESULT_TYPE = "response_remote_result_type";

    // test mode, set max scan cost time
    private static final long TEST_MAX_SCAN_COST_TIME = getMaxScanCostTime();

    public static long getTestMaxScanCostTime() {
        return TEST_MAX_SCAN_COST_TIME;
    }

    private static long getMaxScanCostTime() {
        int maxCostTimeMinutes = GallerySystemProperties.getInt("debug.gallery.face.max.cost.time", 0);
        if (maxCostTimeMinutes < 0) {
            maxCostTimeMinutes = 0;
        }
        GLog.d(TAG, DL, "getMaxScanCostTimeTest, maxCostTimeMinutes: " + maxCostTimeMinutes + "minutes.");
        return maxCostTimeMinutes * ONE_MINUTE;
    }

    public static void setAllScanPref(Context context) {
        SPUtils.setBoolean(context, null, PREF_ALL_SCAN_KEY, true);
    }

    public static boolean hadAllScan(Context context) {
        return SPUtils.getBoolean(context, null, PREF_ALL_SCAN_KEY, false);
    }

    public static void setFirstGroupPref(Context context) {
        SPUtils.setBoolean(context, null, PREF_FIRST_GROUP_KEY, false);
    }

    public static boolean isFirstGroup(Context context) {
        return SPUtils.getBoolean(context, null, PREF_FIRST_GROUP_KEY, true);
    }

    public static void setFirstLabelScanPref(Context context) {
        SPUtils.setBoolean(context, null, PREF_FIRST_LABEL_SCAN_KEY, false);
    }

    public static boolean isFirstLabelScan(Context context) {
        return SPUtils.getBoolean(context, null, PREF_FIRST_LABEL_SCAN_KEY, true);
    }

    public static void setFirstOcrScanPref(Context context) {
        SPUtils.setBoolean(context, null, PREF_FIRST_OCR_SCAN_KEY, false);
    }

    public static boolean isFirstOcrScan(Context context) {
        return SPUtils.getBoolean(context, null, PREF_FIRST_OCR_SCAN_KEY, true);
    }

    public static void setFirstScanPref(Context context, String scanName) {
        if (scanName != null) {
            String prefKey = PREF_PREFIX + scanName + PREF_FIRST_SCAN_KEY_SUFFIX;
            SPUtils.setBoolean(context, null, prefKey, false);
        }
    }

    public static boolean isFirstScan(Context context, String scanName) {
        if (scanName != null) {
            String prefKey = PREF_PREFIX + scanName + PREF_FIRST_SCAN_KEY_SUFFIX;
            return SPUtils.getBoolean(context, null, prefKey, true);
        } else {
            return false;
        }
    }

    public static void setLastScanTime(Context context, String prefKey, long time) {
        SPUtils.setLong(context, null, prefKey, time);
    }

    public static long getLastScanTime(Context context, String prefKey) {
        return SPUtils.getLong(context, null, prefKey, DEFAULT_SCAN_TIME);
    }

    public static void setScanCount(Context context, String prefKey, int count) {
        SPUtils.setInt(context, null, prefKey, count);
    }

    public static int getScanCountWithoutCharging(Context context, String prefKey) {
        return SPUtils.getInt(context, null, prefKey, DEFAULT_SCAN_COUNT);
    }

    public static boolean hadUpdateFaceBestScore(Context context) {
        return SPUtils.getBoolean(context, null, PREF_FACE_SCORE_UPDATE, false);
    }

    public static void setUpdateFaceBestScore(Context context) {
        SPUtils.setBoolean(context, null, PREF_FACE_SCORE_UPDATE, true);
    }

    public static void setLastManualTime(Context context, long time) {
        SPUtils.setLong(context, null, PREF_LAST_MANUAL_GROUP_TIME_KEY, time);
    }

    /**
     * ocr扫描的开关是否打开
     * 1、支持 超级文本 2.0，开启扫描
     * 2、不支持 超级文本 2.0，支持 超级文本 1.0 的 ocr 能力，开启扫描
     * 3、不支持 超级文本 2.0，不支持 超级文本 1.0 的 ocr 能力，不开启扫描
     * @return true or false
     */
    public static boolean isOcrSwitcherOn() {
        IConfigAbility configAbility = ((GalleryApplication) ContextGetter.context).getAppAbility(IConfigAbility.class);
        if (configAbility == null) {
            return false;
        }
        boolean isSupportSuperTextV2 = Boolean.TRUE.equals(configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V2, false));
        boolean isSupportSuperTextV1Ocr = Boolean.TRUE.equals(configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR, false));
        GLog.d(TAG, DL, "isOcrSwitcherOn isSupportSuperTextV2=" + isSupportSuperTextV2 + " isSupportSuperTextV1Ocr=" + isSupportSuperTextV1Ocr);
        boolean isOpen = (isSupportSuperTextV2 || isSupportSuperTextV1Ocr) && GallerySystemProperties.getBoolean("debug.gallery.ocr.switcher", true);
        configAbility.close();
        return isOpen;
    }

    public static void setOcrScanVersion(Context context, int version) {
        SPUtils.setInt(context, null, PREF_CURRENT_OCR_SCAN_VERSION, version);
    }

    public static int getOcrScanVersion(Context context) {
        return SPUtils.getInt(context, null, PREF_CURRENT_OCR_SCAN_VERSION, -1);
    }

    public static long getScanCostTime24h(Context context) {
        return ComponentPrefUtils.getLongPref(context, GalleryScanUtils.SCAN_COST_TIME_24h_KEY, 0);
    }

    public static void setFlawScanVersion(Context context, int version) {
        SPUtils.setInt(context, null, PREF_CURRENT_FLAW_SCAN_VERSION, version);
    }

    public static int getFlawScanVersion(Context context) {
        return SPUtils.getInt(context, null, PREF_CURRENT_FLAW_SCAN_VERSION, 0);
    }

    public static long getScanStartTime24h(Context context) {
        return ComponentPrefUtils.getLongPref(context, GalleryScanUtils.SCAN_START_TIME_24h_KEY, 0);
    }

    public static void setScanCostTime24h(Context context, long costTime) {
        ComponentPrefUtils.setLongPref(context, GalleryScanUtils.SCAN_COST_TIME_24h_KEY, costTime);
    }

    public static void setScanStartTime24h(Context context, long time) {
        ComponentPrefUtils.setLongPref(context, GalleryScanUtils.SCAN_START_TIME_24h_KEY, time);
    }

    public static void setScanTaskStartTime(Context context, long time) {
        SPUtils.setLong(context, null, PREF_SCAN_TASK_START_TIME_KEY, time);
    }

    public static long getScanTaskStartTime(Context context) {
        long result = SPUtils.getLong(context, null, PREF_SCAN_TASK_START_TIME_KEY, -1);
        if (result == -1) {
            // 第一次升级版本，也需要等待2天
            result = System.currentTimeMillis();
            setScanTaskStartTime(context, result);
        }
        return result;
    }

    public static void setScanWentWellFlag(Context context, String prefsKey, boolean flag) {
        SPUtils.setBoolean(context, null, prefsKey, flag);
    }

    public static boolean getScanWentWellFlag(Context context, String prefsKey) {
        return SPUtils.getBoolean(context, null, prefsKey, true);
    }

    private static long byte2Kb(long size) {
        return size >> 10;
    }

    public static boolean isNormalScanPolicy() {
        return MODEL_SCAN_POLICY;
    }

    public static boolean isCountTestModel() {
        return MODEL_COUNT;
    }

    public static boolean isCostTimeTestModel() {
        return MODEL_COST_TIME;
    }

    public static boolean isLabelTestModel() {
        return MODEL_LABEL;
    }

    public static boolean isTestMode() {
        return MODEL_TEST;
    }

    public static boolean isModelRecodeScanInfo() {
        return MODEL_RECODE_SCAN_INFO;
    }

    public static boolean isModelVerify() {
        return MODEL_VERIFY;
    }

    public static String getCrashFileCached(Context context) {
        return context.getFilesDir().getAbsolutePath() + "/" + CRASH_CACHED_FILE;
    }

    public static String readAbortFile(Context context) {
        String filePath = getCrashFileCached(context);
        StringBuilder crashFile = new StringBuilder();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(filePath);
            byte[] buffer = new byte[512];
            int count = 0;
            while ((count = fis.read(buffer)) > 0) {
                crashFile.append(new String(buffer, 0, count, StandardCharsets.UTF_8));
            }
            GLog.d(TAG, DL, "readAbortFile crashFile:" + crashFile);
        } catch (FileNotFoundException e) {
            //TODO: do nothing, cause maybe file is no exist
        } catch (Exception e) {
            GLog.e(TAG, DL, "readAbortFile error:" + e);
        } finally {
            IOUtils.closeQuietly(fis);
            File file = new File(filePath);
            if (file.exists()) {
                boolean ans = file.delete();
                GLog.d(TAG, DL, "readAbortFile file.delete:" + ans);
            }
        }
        return crashFile.toString();
    }

    public static boolean checkCameraPath(String path) {
        if (path == null) {
            return false;
        }
        String internalPath = Dir.getDCIM().getInternalPath();
        String externalPath = Dir.getDCIM().getExternalPath();
        boolean isInternalPath = (internalPath != null) && path.startsWith(internalPath);
        boolean isExternalPath = (externalPath != null) && path.startsWith(externalPath);
        return isInternalPath || isExternalPath;
    }

    public static void recordInfo(Context context, String name, long allCountNoCharging, long allCount) {
        float temperature = TemperatureUtil.getCurrentTemperature();
        boolean isCharging = BatteryStatusUtil.isBatteryInCharging(false);
        float power = BatteryStatusUtil.getCurrentBatteryPercent(context);
        boolean isExecutingCloudSync = ApiDmManager.getCloudSyncDM().isExecutingCloudSync();
        StringBuilder stringBuilder = new StringBuilder();
        String date = DATE_FORMAT.format(new Date(System.currentTimeMillis()));
        stringBuilder.append(date); //scan date
        stringBuilder.append(COMMA);
        stringBuilder.append(name); //scan type
        stringBuilder.append(COMMA);
        stringBuilder.append(isExecutingCloudSync); //isCloudSync
        stringBuilder.append(COMMA);
        stringBuilder.append(power); //scan power
        stringBuilder.append(COMMA);
        stringBuilder.append(temperature); //scan temperature
        stringBuilder.append(COMMA);
        stringBuilder.append(isCharging); //isCharging
        stringBuilder.append(COMMA);
        stringBuilder.append(allCountNoCharging); //count no charging
        stringBuilder.append(COMMA);
        stringBuilder.append(allCount); //all count
        stringBuilder.append("\n");

        writeScanInfoToFile(context, stringBuilder.toString());
    }

    private static void writeScanInfoToFile(Context context, String scanInfo) {
        if (context == null) {
            GLog.e(TAG, DL, "writeScanInfoToFile, context is null!");
            return;
        }
        if (TextUtils.isEmpty(scanInfo)) {
            GLog.e(TAG, DL, "writeScanInfoToFile, scanInfo is empty!");
            return;
        }
        File fileDir = File.getFile(context.getExternalFilesDir(null));
        if (fileDir == null) {
            return;
        }
        if (!FileOperationUtils.ensureMakeDirectory(fileDir)) {
            GLog.w(TAG, DL, "writeScanInfoToFile, mkDir failed!");
            return;
        }
        File file = new File(fileDir.getAbsolutePath(), RECORDE_SCAN_INFO_FILE);
        if (file.exists() && file.isDirectory()) {
            boolean success = file.delete();
            if (!success) {
                GLog.w(TAG, DL, "writeScanInfoToFile, scanInfo file is a directory, but delete it failed!");
                return;
            }
        }

        try (FileOutputStream fos = new FileOutputStream(file.getFile(), true);
             OutputStreamWriter out = new OutputStreamWriter(fos, StandardCharsets.UTF_8)) {
            out.write(scanInfo);
            out.flush();
            GLog.d(TAG, DL, "writeScanInfoToFile, success!");
        } catch (Exception e) {
            GLog.e(TAG, DL, "writeScanInfoToFile, e " + e);
        }
    }

    public static void checkDictionaryLoaded(Context context) {
        if (!LabelDictionary.isDictionaryLoaded()) {
            GLog.d(TAG, DL, "label dictionary is not loaded, firstly load it!");
            LabelSearchEngine.getInstance().loadDictionary(context, false);
        }
    }
}