/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TagsJsonUtils.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2017/09/12
 ** Author: kexiuhua@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** k<PERSON>iuhua@Apps.Gallery3D         2017/09/12   1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.face;

import android.annotation.SuppressLint;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Rect;

import com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.graphic.ImageUtils;
import com.oplus.gallery.foundation.security.Md5Utils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;

import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.BOTTOM;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.DATA;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.GROUP_ID;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.GROUP_NAME;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.IS_CHOSEN;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.LEFT;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.NO_FACE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.RIGHT;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.THUMB_H;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.THUMB_W;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.TOP;

class TagsJsonUtils {
    private static final String TAG = "JsonUtils";
    private static final String RESULT_FILE = "/FaceGroup.json";
    private static final int CODE = 200;
    private static final String ERR_MSG = "success";
    private static final int INTERVAL_DAYS = 10;
    private static final int VERSION = 79000000;
    private static final String TAG_CODE = "code";
    private static final String TAG_ERR_MSG = "errmsg";
    private static final String TAG_DATA = "data";
    private static final String TAG_INTERVAL_DAYS = "intervalDays";
    private static final String TAG_VERSION = "version";
    private static final String TAG_CLUSTER = "clusterCats";
    private static final String[] QUERY_PROJECT_VERIFY = new String[]{
            GROUP_ID,
            GROUP_NAME,
            DATA,
            THUMB_W,
            THUMB_H,
            LEFT,
            TOP,
            RIGHT,
            BOTTOM,
            IS_CHOSEN,
    };
    private static final byte[] BUFFER = new byte[1024 * 8];
    private static final int THUMBNAIL_SIZE = 960;

    private TagsJsonUtils() {
    }

    static void facesToJsonObject(Context context) {
        HashMap<Integer, JsonGroup> list = new HashMap<>();
        Cursor cursor = null;
        try {
            String whereClause = NO_FACE + " != 1"
                    + " AND " + GROUP_ID + " > " + GalleryScanUtils.GROUP_ID_2;
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setProjection(QUERY_PROJECT_VERIFY)
                    .setWhere(whereClause)
                    .setConvert(new CursorConvert())
                    .build();
            cursor = DataAccess.getAccess().query(req);
            if (cursor != null) {
                int groupIdIndex = cursor.getColumnIndex(GROUP_ID);
                int groupNameIndex = cursor.getColumnIndex(GROUP_NAME);
                while (cursor.moveToNext()) {
                    int groupId = cursor.getInt(groupIdIndex);
                    JsonGroup jsonGroup = list.get(groupId);
                    if (jsonGroup == null) {
                        jsonGroup = new JsonGroup();
                        jsonGroup.mGroupId = groupId;
                        jsonGroup.mGroupName = cursor.getString(groupNameIndex);
                        list.put(groupId, jsonGroup);
                    }
                    jsonGroup.mFaces.add(JsonFace.createJsonFace(cursor));
                }
                try {
                    JSONObject resultOb = new JSONObject();
                    JSONObject jOb = new JSONObject();
                    JSONArray jsonArray = new JSONArray();
                    int count = 0;
                    for (JsonGroup group : list.values()) {
                        jsonArray.put(count, JsonGroup.groupToJson(group));
                        count++;
                    }
                    jOb.put(TAG_INTERVAL_DAYS, INTERVAL_DAYS);
                    jOb.put(TAG_VERSION, VERSION);
                    jOb.put(TAG_CLUSTER, jsonArray);

                    resultOb.put(TAG_CODE, CODE);
                    resultOb.put(TAG_ERR_MSG, ERR_MSG);
                    resultOb.put(TAG_DATA, jOb);
                    writeJsonToFile(context, resultOb.toString());
                } catch (Exception e) {
                    GLog.w(TAG, e);
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
    }

    private static void writeJsonToFile(Context context, String json) {
        OutputStream os = null;
        try {
            String filePath = context.getExternalCacheDir().getAbsolutePath() + RESULT_FILE;
            GLog.d(TAG, "writeJsonToFile, filePath: " + filePath);
            os = new FileOutputStream(filePath);
            os.write(json.getBytes("UTF-8"));
            os.flush();
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }


    private static class JsonFace {
        static final String TAG_FACE_FILE_PATH = "mFilePath";
        static final String TAG_FACE_THUMB_WIDTH = "mThumbW";
        static final String TAG_FACE_THUMB_HEIGHT = "mThumbH";
        static final String TAG_FACE_FILE_MD5 = "mFileMD5";
        static final String TAG_FACE_RECT_LEFT = "mRectLeft";
        static final String TAG_FACE_RECT_RIGHT = "mRectRight";
        static final String TAG_FACE_RECT_TOP = "mRectTop";
        static final String TAG_FACE_RECT_BOTTOM = "mRectBottom";
        static final String TAG_FACE_CHOSEN_COVER = "mIsChosenCover";

        String mFilePath;
        int mThumbW;
        int mThumbH;
        String mFileMd5;
        int mRectLeft;
        int mRectRight;
        int mRectTop;
        int mRectBottom;
        int mIsChosenCover;

        @SuppressLint("Range")
        static JsonFace createJsonFace(Cursor cursor) {
            JsonFace face = new JsonFace();
            face.mFilePath = cursor.getString(cursor.getColumnIndex(DATA));
            String md5 = Md5Utils.getMd5(new File(face.mFilePath));
            face.mFileMd5 = ((md5 != null) ? md5 : "");
            face.mIsChosenCover = cursor.getInt(cursor.getColumnIndex(IS_CHOSEN));

            //scale face for uniform in size
            int thumbW = cursor.getInt(cursor.getColumnIndex(THUMB_W));
            int thumbH = cursor.getInt(cursor.getColumnIndex(THUMB_H));
            Rect faceRect = new Rect(cursor.getInt(cursor.getColumnIndex(LEFT)),
                    cursor.getInt(cursor.getColumnIndex(TOP)),
                    cursor.getInt(cursor.getColumnIndex(RIGHT)),
                    cursor.getInt(cursor.getColumnIndex(BOTTOM)));
            final float scale = THUMBNAIL_SIZE / (float) Math.max(thumbW, thumbH);
            final float maxScale = 1.0f;
            if (scale < maxScale) {
                thumbW = (int) (thumbW * scale);
                thumbH = (int) (thumbH * scale);
                ImageUtils.scaleRect(faceRect, scale);
            }

            face.mThumbW = thumbW;
            face.mThumbH = thumbH;
            face.mRectLeft = faceRect.left;
            face.mRectRight = faceRect.right;
            face.mRectTop = faceRect.top;
            face.mRectBottom = faceRect.bottom;

            return face;
        }

        static JSONObject faceToJson(JsonFace face) {
            try {
                JSONObject jOb = new JSONObject();
                jOb.put(TAG_FACE_FILE_PATH, face.mFilePath);
                jOb.put(TAG_FACE_THUMB_WIDTH, face.mThumbW);
                jOb.put(TAG_FACE_THUMB_HEIGHT, face.mThumbH);
                jOb.put(TAG_FACE_FILE_MD5, face.mFileMd5);
                jOb.put(TAG_FACE_RECT_LEFT, face.mRectLeft);
                jOb.put(TAG_FACE_RECT_RIGHT, face.mRectRight);
                jOb.put(TAG_FACE_RECT_TOP, face.mRectTop);
                jOb.put(TAG_FACE_RECT_BOTTOM, face.mRectBottom);
                jOb.put(TAG_FACE_CHOSEN_COVER, face.mIsChosenCover);
                return jOb;
            } catch (Exception e) {
                GLog.w(TAG, e);
            }
            return null;
        }
    }

    private static class JsonGroup {
        static final String TAG_GROUP_ID = "mGroupId";
        static final String TAG_GROUP_NAME = "mGroupName";
        static final String TAG_GROUP_FACES = "mFaces";

        int mGroupId;
        String mGroupName;
        ArrayList<JsonFace> mFaces = new ArrayList<>();

        static JSONObject groupToJson(JsonGroup group) {
            try {
                JSONObject jOb = new JSONObject();
                jOb.put(TAG_GROUP_ID, group.mGroupId);
                jOb.put(TAG_GROUP_NAME, group.mGroupName);
                JSONArray jsonArray = new JSONArray();
                for (int i = 0; i < group.mFaces.size(); i++) {
                    JsonFace face = group.mFaces.get(i);
                    jsonArray.put(i, JsonFace.faceToJson(face));
                }
                jOb.put(TAG_GROUP_FACES, jsonArray);
                return jOb;
            } catch (Exception e) {
                GLog.w(TAG, e);
            }
            return null;
        }
    }
}
