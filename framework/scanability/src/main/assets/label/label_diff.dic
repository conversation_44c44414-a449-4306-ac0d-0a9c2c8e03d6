#version 20211023
#该文档用以表示不同地域的标签差异化，#号用以表示注释
#第一列是语言和区域的组合:language_region，
#第二列表示标签id:scene_id
#第三列用以表示父子近同关系表label_relationship_map.dic中对应的第几列（0开始计算）数据应当有差异，
#第四列是map.dic中的第几列应该为什么值。
#eg. th_th 泰语_泰国 标签为“建筑”（id=12）在父子近同关系映射中第3列的值（子类）是60,62,63...没有59（教堂）和61（塔）
hr_hr	12	3	59,61,62,63,147,163,215,338
hu_hu	12	3	59,61,62,63,147,163,215,338
rw_rw	12	3	59,60,61,62,147,163,215,338
sw_ke	12	3	59,60,62,63,147,163,215,338
th_th	12	3	60,62,63,147,163,215,338
ur_pk	12	3	59,60,61,62,13,147,163,215,338
