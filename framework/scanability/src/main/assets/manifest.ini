[MD5]
face_cluster.model=a730660134ea2372bd5b2453c0a51f66
face_verify.model=c8151dffcc8ec339873e0eeebb0368d8
face_Attribute.model=c43d66c7e015da701b62ac84d4e3f51b
label.model=70115cb80fee431b64a9a993472ab3f5
label_main.db=26cb74922856728b6c478502a8e90980
label_name.zip=2bc1ac6f5ca6c718d1422b4d83d3b046
gasryuv.model=d4277b61aa6803e19d899ebbb5906bb6
faceTpl0.rgb32=662962e61c1725f0aa4e800b762e74d7
faceTpl1.rgb32=662962e61c1725f0aa4e800b762e74d7
Charming.cng=dfbe1cfe86197beaf80f75e0afac58fb
tags.db=35749b4232eb40f4d235f2c6d49be365
memories.db=eee3d09eec0a7c7f2601d8ac39811f3a
similar_feature.model=15ae9e4e5ba61ad402b143841d7251f6
iqa_quality.model=6e21d4ded3c5020a63e1cf75fc61858f
ssa_quality.model=e805335944a775fb4782cc0a988cae86
;不同分支下，同一SDK版本，对应的version是一致的，不能出现版本的割裂
[Version]
# 对应全新的模型库版本（线上FaceModelSource只包含算法模型，so直接相册集成）
face_component_default_version=16
label_component_default_version=15
label_list_default_version=9
similar_feature_component_default_version=1
iqa_quality_component_default_version=4
ssa_quality_component_default_version=2
# andes_entity_dict.txt 词典版本号控制，andes_entity_dict.txt内容更新需要更新此处版本号，用于更新词典内容到kit
andes_entity_dict.txt=4
# incompatible_label_dict.txt 词典版本号控制，incompatible_label_dict.txt内容更新需要更新此处版本号，用于更新词典内容到kit
incompatible_label_dict.txt=1
# correction.txt 词典版本号控制，correction.txt内容更新需要更新此处版本号，用于更新词典内容到kit
correction.txt=1
# sensitive.txt 词典版本号控制，sensitive.txt内容更新需要更新此处版本号，用于更新词典内容到kit
sensitive.txt=1
# stop_words.txt 词典版本号控制，stop_words.txt内容更新需要更新此处版本号，用于更新词典内容到kit
stop_words.txt=7
# correction_en.txt 词典版本号控制，sensitive.txt内容更新需要更新此处版本号，用于更新词典内容到kit
correction_en.txt=1
# sensitive_en.txt 词典版本号控制，sensitive_en.txt内容更新需要更新此处版本号，用于更新词典内容到kit
sensitive_en.txt=1
# stop_words_en.txt 词典版本号控制，stop_words_en.txt内容更新需要更新此处版本号，用于更新词典内容到kit
stop_words_en.txt=1
# scene_words.txt 词典版本号控制，scene_words.txt内容更新需要更新此处版本号，用于更新词典内容到kit
scene_words.txt=3
[EncodeSecret]
doc_license.lic=DF7k6:wR_Z>[pnTryoRpwlK\[HhoeCSQ