/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - QueryConditionTest
 ** Description: 查询条件辅助的测试类
 **
 ** Version: 1.0
 ** Date: 2022/06/06
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/06/02 1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.engine

import org.junit.Assert
import org.junit.Test

class QueryConditionTest {

    @Test
    fun `should return true when initialize with filterCondition type is 0`() {
        val condition = QueryCondition.Builder().apply {
            filterMaxAddedDate(12345678910)
            filterMinAddedDate(123456789)
            filterMaxModifiedDate(12345678910)
            filterMinModifiedDate(123456789)
            filterMaxTakenDate(12345678910)
            filterMinTakenDate(123456789)
            filterMaxFileSize(10 * 1024 * 1024)
            filterMinFileSize(1024 * 1024)
            filterMaxSize(10 * 1024 * 1024)
            filterMinSize(1024 * 1024)
            filterMediaType(3)
            filterMimeType(mutableListOf("mp4", "avi"))
            filterRelativePaths(mutableListOf("DCIM", "CAMERA"))
            orderBy("3", true)
        }.build()
        Assert.assertTrue(condition.orderBy == "3 ASC")
        Assert.assertTrue(
            condition.whereClause == "0 = 0 AND width >= ? AND height >= ? AND width <= ? AND height <= ?" +
                    " AND mime_type IN (?,?) AND _size >= ? AND _size <= ? AND relative_path IN (?,?)" +
                    " AND datetaken >= ? AND datetaken <= ? AND date_added >= ? AND date_added <= ? AND date_added >= ?" +
                    " AND date_modified <= ? AND media_type = ? "
        )
        Assert.assertTrue(condition.whereArgs?.size == 17)
    }
}