/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OneDayBatchTest
 ** Description: 精选扫描分批策略:多天分批策略的测试类
 **
 ** Version: 1.0
 ** Date: 2022/05/31
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/05/31  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.batch

import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectTimeNode
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import io.mockk.MockKAnnotations
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class OneDayBatchTest {
    private val notScannedNodes = mutableListOf<SeniorSelectTimeNode>().apply {
        add(SeniorSelectTimeNode("20220601", IntRange(2, 104), 103))
        add(SeniorSelectTimeNode("20220531", IntRange(0, 1), 2))
    }
    private val scannedNodes = mutableListOf<SeniorSelectTimeNode>().apply {
        add(SeniorSelectTimeNode("20220601", IntRange(0, 1), 2))
        add(SeniorSelectTimeNode("20220529", IntRange(2, 3), 2))
    }
    private val notScannedInfoList = mutableListOf<BaseImageInfo>().apply {
        for (i in 0..104) {
            add(BaseImageInfo().apply {
                mMediaId = i.toLong()
                mMediaType =
                    GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
            })
        }
    }
    private val scannedInfoList = mutableListOf<BaseImageInfo>().apply {
        for (i in 105..108) {
            add(BaseImageInfo().apply {
                mMediaId = i.toLong()
                mMediaType =
                    GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
            })
        }
    }

    private lateinit var oneDayBatch: OneDayBatch

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        oneDayBatch = spyk(OneDayBatch())
    }

    @Test
    fun `should return true when getData with notScannedNodes is not empty`() {
        /*
         * 未扫 20220601和20220531 有图片105 视频0
         * 已扫 合适的日期是20220601 有图片4  视频0
         */
        oneDayBatch.updateNodeList(notScannedNodes, scannedNodes)
        oneDayBatch.updateInfoList(notScannedInfoList, scannedInfoList)
        val data = oneDayBatch.getData()
        Assert.assertTrue(data.imageNotScanned.size == 105)
        Assert.assertTrue(data.videoNotScanned.size == 0)
        Assert.assertTrue(data.imageScanned.size == 4)
        Assert.assertTrue(data.videoScanned.size == 0)
    }
}