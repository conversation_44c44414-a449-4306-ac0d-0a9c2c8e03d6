/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryScanProviderHelperTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/08/16
 ** Author: wuhengze@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wuhengze@Apps.Gallery3D	       2022/08/16		1.0		create
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.model

import android.content.Context
import com.oplus.gallery.business_lib.model.data.utils.MixWhereClause
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GalleryScanProviderHelperTest {
    @MockK
    lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun should_get_expect_result_when_getOrcScanWhereClause() {
        // given
        mockkStatic(MixWhereClause::class)
        every { MixWhereClause.getWhereClause(any(), any(), any(), any(), any()) } returns "test"
        // when
        val result = GalleryScanProviderHelper.getOrcScanWhereClause(context)
        // then
        val expectResult = "test AND mime_type!=\"image/gif\""
        Assert.assertEquals(result, expectResult)
    }
}