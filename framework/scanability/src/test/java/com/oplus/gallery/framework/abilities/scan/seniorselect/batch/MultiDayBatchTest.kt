/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MultiDayBatchTest
 ** Description: 精选扫描分批策略:多天分批策略的测试类
 **
 ** Version: 1.0
 ** Date: 2022/05/30
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/05/30  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.batch

import com.oplus.gallery.framework.abilities.scan.seniorselect.SeniorSelectTimeNode
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import io.mockk.MockKAnnotations
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class MultiDayBatchTest {

    private lateinit var multiDayBatch: MultiDayBatch
    private lateinit var testBatch: TestBatch
    private val notScannedNodes = mutableListOf<SeniorSelectTimeNode>().apply {
        add(SeniorSelectTimeNode("20220601", IntRange(2, 4), 3))
        add(SeniorSelectTimeNode("20220531", IntRange(0, 1), 2))
    }

    private val scannedNodes = mutableListOf<SeniorSelectTimeNode>().apply {
        add(SeniorSelectTimeNode("20220601", IntRange(0, 1), 2))
        add(SeniorSelectTimeNode("20220529", IntRange(2, 3), 2))
    }

    private val notScannedInfoList = mutableListOf<BaseImageInfo>().apply {
        add(BaseImageInfo().apply {
            mMediaId = 111
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
        })
        add(BaseImageInfo().apply {
            mMediaId = 222
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
        })
        add(BaseImageInfo().apply {
            mMediaId = 333
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
        })
        add(BaseImageInfo().apply {
            mMediaId = 444
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
        })
        add(BaseImageInfo().apply {
            mMediaId = 555
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
        })
    }

    private val scannedInfoList = mutableListOf<BaseImageInfo>().apply {
        add(BaseImageInfo().apply {
            mMediaId = 666
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
        })
        add(BaseImageInfo().apply {
            mMediaId = 777
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
        })
        add(BaseImageInfo().apply {
            mMediaId = 888
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
        })
        add(BaseImageInfo().apply {
            mMediaId = 999
            mMediaType =
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
        })
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        testBatch = TestBatch()
        multiDayBatch = spyk(MultiDayBatch())
        multiDayBatch.nextBatch = testBatch
    }

    @Test
    fun `should return false when hasNext with notScannedNodes is empty`() {
        Assert.assertFalse(multiDayBatch.hasNext())
    }

    @Test
    fun `should return true when hasNext with notScannedNodes is not empty`() {
        multiDayBatch.updateNodeList(notScannedNodes, scannedNodes)
        Assert.assertTrue(multiDayBatch.hasNext())
    }

    @Test
    fun `should return true when updateDataToNextBatch with notScannedNodes is not empty`() {
        multiDayBatch.updateNodeList(notScannedNodes, scannedNodes)
        multiDayBatch.updateInfoList(notScannedInfoList, scannedInfoList)
        multiDayBatch.updateDataToNextBatch()
        Assert.assertTrue(multiDayBatch.isSwitchToNextBatch())
    }

    @Test
    fun `should return false when isSwitchToNextBatch with few Nodes`() {
        multiDayBatch.updateNodeList(notScannedNodes, scannedNodes)
        Assert.assertFalse(multiDayBatch.isSwitchToNextBatch())
    }

    @Test
    fun `should return true when isSwitchToNextBatch with large number of nodes`() {
        val nodes = mutableListOf<SeniorSelectTimeNode>().apply {
            add(SeniorSelectTimeNode("20220601", IntRange(2, 304), 304))
            add(SeniorSelectTimeNode("20220531", IntRange(0, 1), 2))
        }
        multiDayBatch.updateNodeList(nodes, scannedNodes)
        Assert.assertTrue(multiDayBatch.isSwitchToNextBatch())
    }

    @Test
    fun `should return true when getData with notScannedNodes is not empty`() {
        /*
         * 未扫 20220601和20220531 有图片3 视频2
         * 已扫 合适的日期是20220601 有图片2
         */
        multiDayBatch.updateNodeList(notScannedNodes, scannedNodes)
        multiDayBatch.updateInfoList(notScannedInfoList, scannedInfoList)
        val data = multiDayBatch.getData()
        Assert.assertTrue(data.imageNotScanned.size == 3)
        Assert.assertTrue(data.videoNotScanned.size == 2)
        Assert.assertTrue(data.imageScanned.size == 2)
        Assert.assertTrue(data.videoScanned.size == 0)
    }


    class TestBatch : Batch<BatchScanData>() {

        override fun hasNext(): Boolean {
            //调整hasNext的实现，用于测试updateDataToNextBatch接口
            return notScannedInfoList.isNotEmpty() && scannedInfoList.isNotEmpty()
        }

        override fun getData(): BatchScanData {
            return BatchScanData()
        }

        override fun isSwitchToNextBatch(): Boolean {
            return false
        }

        override fun updateDataToNextBatch() {
            // do nothing
        }
    }
}