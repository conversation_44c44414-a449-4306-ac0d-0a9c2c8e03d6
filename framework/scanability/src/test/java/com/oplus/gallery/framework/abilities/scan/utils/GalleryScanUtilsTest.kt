/********************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GalleryScanUtilsTest.kt
 * Description:
 * Version: 1.0
 * Date : 2022/8/23
 * Author: xiewujie@Apps.Gallery3D
 * TAG: com.oplus.gallery.framework.abilities.scan.utils.GalleryScanUtils
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2022/08/23    1.0         first created
</desc></version></date></author> */
package com.oplus.gallery.framework.abilities.scan.utils

import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V2
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GalleryScanUtilsTest {

    private lateinit var configAbility: IConfigAbility

    @Before
    fun setUp() {
        mockkObject(ContextGetter)
        configAbility = mockk()
        ContextGetter.context = mockk<GalleryApplication> {
            every { getAppAbility<IConfigAbility>() } returns configAbility
        }
        mockkStatic(GallerySystemProperties::class)
        every { configAbility.close() } returns Unit
    }

    @Test
    fun should_return_false_when_configAbility_return_false() {
        // given
        every { GallerySystemProperties.getBoolean("debug.gallery.ocr.switcher", any()) } returns false
        every { configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR, any()) } returns false
        every { configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V2, any()) } returns false
        // when
        val result = GalleryScanUtils.isOcrSwitcherOn()
        // then
        Assert.assertFalse(result)
    }

    @Test
    fun should_return_false_when_debug_switcher_close() {
        // given
        every { GallerySystemProperties.getBoolean("debug.gallery.ocr.switcher", any()) } returns false
        every { configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR, any()) } returns true
        every { configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V2, any()) } returns true
        // when
        val result = GalleryScanUtils.isOcrSwitcherOn()
        // then
        Assert.assertFalse(result)
    }

    @Test
    fun should_return_true_when_debug_switcher_open_and_configAbility_return_true() {
        // given
        every { GallerySystemProperties.getBoolean("debug.gallery.ocr.switcher", any()) } returns true
        every { configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR, any()) } returns true
        every { configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V2, any()) } returns true
        // when
        val result = GalleryScanUtils.isOcrSwitcherOn()
        // then
        Assert.assertTrue(result)
    }
}