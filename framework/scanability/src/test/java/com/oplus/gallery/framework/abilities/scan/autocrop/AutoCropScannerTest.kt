/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AutoCropScannerTest
 ** Description: 智能裁剪扫描类的测试类
 **
 ** Version: 1.0
 ** Date: 2022/03/23
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/03/23 1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.autocrop

import android.graphics.Bitmap
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AutoCropScannerTest {

    companion object {
        private const val AUTO_CROP_SCAN = 1 shl 4
    }

    @MockK
    private lateinit var localImage: LocalImage

    @MockK
    private lateinit var bitmap: Bitmap

    @MockK
    private lateinit var imageData: ImageData


    @MockK
    private lateinit var mockApp: GalleryApplication

    private lateinit var autoCropScanner: AutoCropScanner

    @MockK
    private lateinit var resourcingAbility: IResourcingAbility

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        ContextGetter.context = mockApp
        every { mockApp.applicationContext } returns mockApp
        every { mockApp.getAppAbility(IResourcingAbility::class.java) } returns resourcingAbility
        every { imageData.bitmap } returns bitmap
        every { localImage.mediaId } returns 0
        autoCropScanner = AutoCropScanner(ContextGetter.context)
    }

    @Test
    fun `should return true when getScanType `() {
        Assert.assertTrue(autoCropScanner.scanType == AUTO_CROP_SCAN)
    }
}