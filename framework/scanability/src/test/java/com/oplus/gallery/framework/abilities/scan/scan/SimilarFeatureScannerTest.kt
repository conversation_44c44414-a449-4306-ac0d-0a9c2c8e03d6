/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SimilarFeatureScannerTest
 ** Description: 相似图扫描的测试类
 **
 ** Version: 1.0
 ** Date: 2022/06/06
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/06/06 1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.scan

import android.content.pm.PackageManager
import android.graphics.Bitmap
import com.cv.imageapi.CvNearDup
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult
import com.oplus.gallery.framework.abilities.scan.model.GalleryScanProviderHelper
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.codec.ImageData
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito
import java.io.File

class SimilarFeatureScannerTest {
    companion object {
        private const val MEDIA_ITEM_1_FILE_PATH = "media_item_1_file_path"
        private const val MEDIA_ITEM_2_FILE_PATH = "media_item_2_file_path"
    }

    @MockK
    private lateinit var mockApp: GalleryApplication

    @MockK
    private lateinit var mediaItem1: LocalImage

    @MockK
    private lateinit var mediaItem2: LocalImage

    @MockK
    private lateinit var bitmap: Bitmap

    @MockK
    private lateinit var imageData: ImageData

    @MockK
    private lateinit var nearDup: CvNearDup

    @MockK
    private lateinit var resourcingAbility: IResourcingAbility

    private lateinit var scanner: SimilarFeatureScanner

    private val mediaItemList = mutableListOf<MediaItem>()

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        mockkStatic(GalleryScanProviderHelper::class)
        mockkStatic(BitmapUtils::class)
        ContextGetter.context = mockApp
        every { mockApp.applicationContext } returns mockApp
        every { mockApp.getAppAbility(IResourcingAbility::class.java) } returns resourcingAbility
        every { resourcingAbility.requestBitmap(any(), any(), any()) } returns ImageResult(bitmap)
        every { resourcingAbility.close() } returns Unit
        every { GLog.w(any(), any() as String, any()) } returns Unit
        every { GLog.e(any(), any() as String, any()) } returns Unit
        every { imageData.bitmap } returns bitmap
        every { BitmapUtils.rotateBitmap(any(), any(), any()) } returns bitmap
        every { GalleryScanProviderHelper.getImageFeatures(any(), -1) } returns null
        every { GalleryScanProviderHelper.insertFeature(any(), -1) } returns Unit
        every { nearDup.cvFeatureExtractor(any() as Bitmap) } returns floatArrayOf(0.1f)
        every { nearDup.cvNearDup(any()) } returns mutableListOf(mutableListOf("test"))
        every { mediaItem1.filePath } returns MEDIA_ITEM_1_FILE_PATH
        every { mediaItem2.filePath } returns MEDIA_ITEM_2_FILE_PATH
        every { mediaItem1.rotation } returns 0
        every { mediaItem2.rotation } returns 0
        every { mediaItem1.mediaType } returns 1
        every { mediaItem2.mediaType } returns 1
        every { mockApp.filesDir } returns File("")
        every { mockApp.packageManager } returns Mockito.mock(PackageManager::class.java)
        mediaItemList.apply {
            add(mediaItem1)
            add(mediaItem2)
        }
        scanner = spyk(SimilarFeatureScanner.getInstance(ContextGetter.context))
    }

    @Test
    fun `should return true when scanMedia with not initialize`() {
        val result = scanner.group(mediaItemList)
        Assert.assertTrue(result == null)
    }

    @Test
    fun `should return true when scanMedia with mediaItemList size is 2`() {
        scanner.target = nearDup
        val result = scanner.group(mediaItemList)
        Assert.assertTrue(result!!.size == 1)
        Assert.assertTrue(result[0][0] == "test")
    }
}