/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GalleryScanTest
 ** Description: 扫描基类的测试类
 **
 ** Version: 1.0
 ** Date: 2022/04/14
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  Ye<PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/04/14 1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.manager

import android.app.Application
import android.content.Context
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.scan.ScanConfig
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.BeforeClass
import org.junit.Test

class GalleryScanTest {

    companion object {
        @BeforeClass
        fun init() {
            mockkStatic(GLog::class)
        }
    }

    @MockK
    private lateinit var mockApp: Application

    private lateinit var scanner0: Test0Scanner
    private lateinit var scanner1: Test1Scanner

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        ContextGetter.context = mockApp
        scanner0 = spyk(Test0Scanner(ContextGetter.context))
        scanner1 = spyk(Test1Scanner(ContextGetter.context))
    }

    @Test
    fun `should return true when getSpKeyOfLastScanTime with different scanner`() {
        Assert.assertTrue(scanner0.spKeyOfLastScanTime == "pref_scan_type_0_last_scan_time_key")
        Assert.assertTrue(scanner1.spKeyOfLastScanTime == "pref_scan_type_1_last_scan_time_key")
    }

    @Test
    fun `should return true when getSpKeyOfScanCount with different scanner`() {
        Assert.assertTrue(scanner0.spKeyOfScanCount == "pref_scan_type_0_scan_count_24h")
        Assert.assertTrue(scanner1.spKeyOfScanCount == "pref_scan_type_1_scan_count_24h")
    }

    private class Test0Scanner(context: Context) : GalleryScan(context) {
        override fun getScanType(): Int {
            return 0
        }

        override fun getSceneName(): String = ""

        override fun onScan(triggerType: Int, config: ScanConfig) {
            //do nothing
        }

        override fun runTaskIsNeedCharging(): Boolean {
            return false
        }
    }

    private class Test1Scanner(context: Context) : GalleryScan(context) {
        override fun getScanType(): Int {
            return 1
        }

        override fun getSceneName(): String = ""

        override fun onScan(triggerType: Int, config: ScanConfig) {
            //do nothing
        }

        override fun runTaskIsNeedCharging(): Boolean {
            return false
        }
    }
}