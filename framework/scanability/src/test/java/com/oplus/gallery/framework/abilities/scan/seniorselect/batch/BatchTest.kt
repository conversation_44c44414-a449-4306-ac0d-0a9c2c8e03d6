/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BatchTest
 ** Description: 精选扫描分批策略的测试类
 **
 ** Version: 1.0
 ** Date: 2022/05/30
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/05/30  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan.seniorselect.batch

import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.util.debug.GLog
import io.mockk.MockKAnnotations
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class BatchTest {
    private lateinit var testBatch: TestBatch
    private lateinit var testNextBatch: TestBatch

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        testBatch = spyk(TestBatch())
        testNextBatch = spyk(TestBatch())
        testBatch.nextBatch = testNextBatch
        testBatch.updateInfoList(mutableListOf(), mutableListOf())
        testBatch.updateNodeList(mutableListOf(), mutableListOf())
    }

    @Test
    fun `should return current data when getNextBatchData with not switch to next`() {
        val data = testBatch.getNextBatchData()
        Assert.assertTrue(data == testBatch.batchScanData)
    }

    @Test
    fun `should return next data when getNextBatchData with is switch to next`() {
        testBatch.switchToNextBatch = true
        val data = testBatch.getNextBatchData()
        Assert.assertTrue(data?.imageNotScanned?.get(0)?.mMediaId == 321L)
    }

    class TestBatch : Batch<BatchScanData>() {
        val batchScanData = BatchScanData(imageNotScanned = mutableListOf<BaseImageInfo>().apply {
            add(BaseImageInfo().apply {
                this.mMediaId = 123
            })
        })

        val nextBatchScanData = BatchScanData(imageNotScanned = mutableListOf<BaseImageInfo>().apply {
            add(BaseImageInfo().apply {
                this.mMediaId = 321
            })
        })

        var switchToNextBatch = false
        var hasNext = false

        override fun hasNext(): Boolean {
            return hasNext
        }

        override fun getData(): BatchScanData {
            return if (hasNext) nextBatchScanData else batchScanData
        }

        override fun isSwitchToNextBatch(): Boolean {
            return switchToNextBatch
        }

        override fun updateDataToNextBatch() {
            (nextBatch as? TestBatch)?.hasNext = true
        }
    }
}