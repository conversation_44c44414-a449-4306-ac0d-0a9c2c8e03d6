/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceScanAbilityImpl
 ** Description:人脸扫描能力实现类
 ** Version: 1.0
 ** Date: 2025-01-08
 ** Author: wudanyang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wudanyang@Apps.Gallery3D    2025-01-08     1.0
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.face

import android.content.Context
import com.oplus.appability.AbsAppAbility
import com.oplus.gallery.framework.abilities.scan.face.IFaceAnalyzer
import com.oplus.gallery.framework.abilities.scan.face.IFaceDetector
import com.oplus.gallery.framework.abilities.scan.face.IFaceScanAbility
import com.oplus.gallery.framework.abilities.scan.face.IFaceScoreCalculator

/**
 * 人脸扫描能力实现类
 */
class FaceScanAbilityImpl : AbsAppAbility(), IFaceScanAbility {

    override val domainInstance: AutoCloseable = this

    override fun newFaceAnalyzer(context: Context): IFaceAnalyzer {
        return CvFaceEngine(context, abilityBus)
    }

    override fun newFaceDetector(): IFaceDetector {
        return FaceDetector()
    }

    override fun newFaceScoreCalculator(): IFaceScoreCalculator {
        return FaceScoreCalculator()
    }

    override fun close() = Unit
}