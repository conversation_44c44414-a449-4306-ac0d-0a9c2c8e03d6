/**********************************************************************************
 *  Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 *  All rights reserved.
 *
 *  File: - GooglePassScanAbility.kt
 *  Description: Google 识别登机牌能力
 *
 *  Version: 1.0
 *  Date: 2024/09/03
 *  Author: lizhenya
 *  TAG:
 *  ------------------------------- Revision History: ----------------------------
 *  <author>                        <date>       <version>       <desc>
 *  ------------------------------------------------------------------------------
 *   lizhenya                     2024/09/03       1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.googlepassscan

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.SHIFT_GOOGLE_PASS_SCAN
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED_CLICKED
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED_UNSUPPORTED
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.convert.ContentValuesConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.scan.googlepassscan.GooglePassScanInfo
import com.oplus.gallery.framework.abilities.scan.googlepassscan.IGooglePassScanAbility
import com.oplus.gallery.framework.abilities.scan.googlepassscan.isAvailable
import com.oplus.gallery.framework.abilities.scan2.googlepassscan.GooglePassScanTracker.BACKGROUND_SCAN
import com.oplus.gallery.framework.abilities.scan2.googlepassscan.GooglePassScanTracker.FOREGROUND_SCAN
import java.io.FileNotFoundException
import java.io.IOException

/**
 * Google 识别登机牌能力
 */
class GooglePassScanAbility(private val context: Context) : AbsAppAbility(), IGooglePassScanAbility {
    override val domainInstance: AutoCloseable = this
    private var resourcingAbility: IResourcingAbility? = null

    /**
     * 扫描结果缓存
     */
    private var cachePassScanInfoMap = hashMapOf<Int, GooglePassScanInfo>()

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        resourcingAbility = abilityBus.requireAbility(IResourcingAbility::class.java)
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        resourcingAbility?.close()
    }

    /**
     * 批量登机牌扫描并获取结果信息
     */
    override fun detect(detectItems: List<MediaItem>): MutableList<GooglePassScanInfo> {
        GLog.d(TAG, LogFlag.DL) { "[detect] exec" }
        val resultList = mutableListOf<GooglePassScanInfo>()
        if (detectItems.isEmpty()) {
            GLog.e(TAG, LogFlag.DL) { "[detect] detectItems isEmpty" }
            return resultList
        }
        val newExtTagFlagsMap = mutableMapOf<MediaItem, Long>()
        val batchReqBuilder = BatchReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
        detectItems.onEachIndexed { _, mediaItem ->
            loadImage(mediaItem)?.let bmpLet@{ bmp ->
                val detectResult = ContentSuggestionsHelper.doDetect(context, bmp)
                BitmapPools.recycle(bmp)
                if (detectResult.isException) {
                    return@bmpLet
                }
                val newExtTagFlags = buildNewExtTagFlags(
                    mediaItem.extTagFlagsAsLong,
                    (if (detectResult.isAvailable()) STATUS_SCANNED else STATUS_SCANNED_UNSUPPORTED)
                )
                if (newExtTagFlags != mediaItem.extTagFlagsAsLong) {
                    newExtTagFlagsMap[mediaItem] = newExtTagFlags
                    batchReqBuilder.addDataReq(createDbUpdateReq(mediaItem, newExtTagFlags, true))
                }
                if (detectResult.isAvailable()) {
                    resultList.add(detectResult)
                }
            }
        }
        val dbResults = batchReqBuilder.build().exec()
        // createDbUpdateReq 通知
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        if (dbResults.isNotEmpty() && dbResults.all { it.count == 1 }) {
            newExtTagFlagsMap.forEach { (mediaItem, extTagFlags) ->
                mediaItem.setExtTagFlags(extTagFlags)
                GooglePassScanTracker.tracePassSuccess(BACKGROUND_SCAN)
            }
        }
        return resultList
    }

    /**
     * 登机牌扫描并获取结果信息：如果存在缓存直接使用缓存信息，否则直接启动扫描获取扫描信息
     */
    override fun detect(mediaItem: MediaItem, bitmap: Bitmap?): GooglePassScanInfo? {
        GLog.d(TAG, LogFlag.DL) { "[detect] exec" }
        val googlePassScanInfo = cachePassScanInfoMap[mediaItem.id]
        if (googlePassScanInfo.isAvailable()) {
            GLog.d(TAG, LogFlag.DL) { "[detect] cache:$googlePassScanInfo" }
            return googlePassScanInfo
        }

        if (bitmap == null) {
            val filePathBitmap = loadImage(mediaItem)
            if (filePathBitmap == null) {
                GLog.w(TAG, LogFlag.DL) { "[detect] load bitmap by filePath is null" }
                return null
            }
            val detectResult = detectAndCache(mediaItem, filePathBitmap)
            BitmapPools.recycle(filePathBitmap)
            return detectResult
        } else {
            return detectAndCache(mediaItem, bitmap)
        }
    }

    /**
     * 从文件中读取GooglePassScanInfo
     */
    override fun getCacheByMediaItem(mediaItem: MediaItem): GooglePassScanInfo? {
        return (cachePassScanInfoMap[mediaItem.id])?.also {
            GLog.d(TAG, LogFlag.DL) { "[getCacheByMediaItem] cache:$it" }
        }
    }

    override fun updateClickedMediaExtCache(mediaItem: MediaItem, scanInfo: GooglePassScanInfo): Boolean {
        val extTagFlagsAsLong = mediaItem.extTagFlagsAsLong
        val newExtTagFlags = buildClickedExtTagFlags(extTagFlagsAsLong)
        if (extTagFlagsAsLong != newExtTagFlags) {
            val cacheResult = createDbUpdateReq(mediaItem, newExtTagFlags, true).exec()
            GLog.d(TAG, LogFlag.DL) { "[updateClickedMediaExtCache] result：$cacheResult,isClicked:${isClicked(newExtTagFlags)}" }
            if (cacheResult > 0) {
                UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
                mediaItem.setExtTagFlags(newExtTagFlags)
                cachePassScanInfoMap[mediaItem.id] = scanInfo
                return true
            }
        }
        return false
    }

    override fun clearMemoryCache() {
        cachePassScanInfoMap.clear()
    }

    /**
     * 重置PassScan相关的ExtTagFlags
     */
    private fun resetPassScanExtTagFlags(currentExtTagFlags: Long): Long {
        return if (isClicked(currentExtTagFlags)) {
            currentExtTagFlags and (STATUS_SCANNED_CLICKED shl SHIFT_GOOGLE_PASS_SCAN).inv()
        } else if (isScanned(currentExtTagFlags)) {
            currentExtTagFlags and (GooglePassScanConst.MASK_SUPPORT shl SHIFT_GOOGLE_PASS_SCAN).inv()
        } else {
            currentExtTagFlags
        }
    }

    override fun close() = Unit

    /**
     * 识别并缓存数据
     */
    private fun detectAndCache(mediaItem: MediaItem, bitmap: Bitmap): GooglePassScanInfo? {
        val detectResult = ContentSuggestionsHelper.doDetect(context, bitmap)
        GLog.d(TAG, LogFlag.DL) { "[detectAndCache] result:$detectResult" }
        if (detectResult.isException) {
            return null
        }
        val newExtTagFlags = buildNewExtTagFlags(
            mediaItem.extTagFlagsAsLong,
            (if (detectResult.isAvailable()) STATUS_SCANNED else STATUS_SCANNED_UNSUPPORTED)
        )
        if (newExtTagFlags != mediaItem.extTagFlagsAsLong) {
            createDbUpdateReq(mediaItem, newExtTagFlags, true).exec()?.let { dbResult ->
                if (dbResult > 0) {
                    UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
                    mediaItem.setExtTagFlags(newExtTagFlags)
                    detectResult.let { result ->
                        cachePassScanInfoMap[mediaItem.id] = result
                        GooglePassScanTracker.tracePassSuccess(FOREGROUND_SCAN)
                    }
                }
            }
        }
        return detectResult
    }

    private fun createDbUpdateReq(mediaItem: MediaItem, newExtTagFlags: Long, noNotify: Boolean): UpdateReq {
        return UpdateReq.Builder()
            .setWhere("${GalleryStore.GalleryColumns.LocalColumns._ID}=${mediaItem.id}")
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, if (noNotify) DatabaseUtils.VALUE_TRUE else DatabaseUtils.VALUE_FALSE)
            .setConvert(ContentValuesConvert(
                ContentValues().apply {
                    put(GalleryStore.GalleryColumns.LocalColumns.EXT_TAG_FLAGS, newExtTagFlags)
                }
            ))
            .build()
    }

    /**
     * 创建按钮被点击后的ExtFlag
     */
    private fun buildClickedExtTagFlags(currentExtTagFlags: Long): Long {
        return buildNewExtTagFlags(resetPassScanExtTagFlags(currentExtTagFlags), STATUS_SCANNED_CLICKED)
    }

    /**
     * 为mediaInfo构建新的ExtTag
     */
    private fun buildNewExtTagFlags(originFlag: Long, status: Long): Long {
        return originFlag and statusShl(status).inv() or (status shl SHIFT_GOOGLE_PASS_SCAN)
    }

    /**
     * 状态左移
     */
    private fun statusShl(status: Long): Long {
        return if (STATUS_SCANNED_CLICKED == status) {
            STATUS_SCANNED_CLICKED
        } else {
            GooglePassScanConst.MASK_SUPPORT
        } shl SHIFT_GOOGLE_PASS_SCAN
    }

    /**
     * 是否是按钮被点击后的ExtTagFlags
     */
    private fun isClicked(extTagFlags: Long): Boolean {
        return (extTagFlags shr SHIFT_GOOGLE_PASS_SCAN) and STATUS_SCANNED_CLICKED == STATUS_SCANNED_CLICKED
    }

    /**
     * 是否是图片被扫描后的ExtTagFlags
     */
    private fun isScanned(extTagFlags: Long): Boolean {
        return (extTagFlags shr SHIFT_GOOGLE_PASS_SCAN) and GooglePassScanConst.MASK_SUPPORT == STATUS_SCANNED
    }

    /**
     * 根据filepath加载图片
     */
    private fun loadImage(mediaItem: MediaItem): Bitmap? {
        var bitmap: Bitmap? = null
        try {
            val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem) ?: let {
                GLog.w(TAG, LogFlag.DL, "loadImage, fail to create resourceKey, filePath:${PathMask.mask(mediaItem.filePath)}")
                return null
            }
            val options = ResourceGetOptions(
                inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(),
                inCacheOperation = CacheOperation.ReadWriteAllCache,
                inCropParams = CropParams.noCrop()
            )
            bitmap = resourcingAbility?.requestBitmap(resourceKey, options, null)?.result ?: let {
                GLog.w(TAG, LogFlag.DL, "loadImage, fail to request bitmap, resourceKey=$resourceKey")
                return null
            }
        } catch (e: FileNotFoundException) {
            GLog.d(TAG, LogFlag.DL) { "[loadImage] fail:$e" }
        } catch (e: IOException) {
            GLog.d(TAG, LogFlag.DL) { "[loadImage] fail:$e" }
        }
        return bitmap
    }

    companion object {
        private const val TAG = "GooglePassScanAbility"
    }
}