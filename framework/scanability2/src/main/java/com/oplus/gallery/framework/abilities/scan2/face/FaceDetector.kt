/***********************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - FaceDetector
 * Description:
 * Version: 1.0
 * Date : 2020/10/02
 * Author: <PERSON><PERSON>@Apps.Gallery3D
 *
 * ---------------------Revision History: ---------------------
 * <author>	<data> 	  <version>	   <desc>
 * Jun.Cheng       2020/10/02    1.0     build this module
 */
package com.oplus.gallery.framework.abilities.scan2.face

import android.graphics.Bitmap
import android.graphics.PointF
import android.graphics.Rect
import com.oplus.faceapi.utils.ColorConvertUtil
import com.oplus.facedetectionapi.FaceDetect
import com.oplus.facedetectionapi.model.CvPixelFormat
import com.oplus.facedetectionapi.model.FaceInfo
import com.oplus.facedetectionapi.model.FaceOrientation
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceInfoMirror
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.face.FaceKeyPointCount
import com.oplus.gallery.framework.abilities.scan.face.FaceRectResult
import com.oplus.gallery.framework.abilities.scan.face.IFaceDetector

/**
 * 人脸检测
 */
class FaceDetector : IFaceDetector {

    private var faceDetect: FaceDetect? = null

    @Synchronized
    private fun init(): Boolean {
        CvFaceEngine.loadCvFaceDefaultLib()
        faceDetect = FaceDetect()
        return true
    }

    @Synchronized
    override fun detectRect(bitmap: Bitmap, pointCount: FaceKeyPointCount?): FaceRectResult {
        if (faceDetect == null) {
            val result = init()
            if (!result) {
                GLog.e(TAG, LogFlag.DL, "[detectRect] failed! faceDetect is null")
                return FaceRectResult()
            }
        }
        runCatching {
            val width = bitmap.width
            val height = bitmap.height
            val stride = width * PIXEL_BYTE_SIZE
            var bgr: ByteArray? = ByteArray(stride * height)
            ColorConvertUtil.getBGRFromBitmap(bitmap, bgr)
            if ((bgr == null) || bgr.isEmpty()) {
                return FaceRectResult()
            }
            val faceInfos: Array<FaceInfo>? = faceDetect?.detect(
                bgr, CvPixelFormat.BGR888,
                width, height, stride, FaceOrientation.UP
            )
            bgr = null
            val resultCode = faceDetect?.detectResultCode ?: 0
            GLog.d(TAG) { "detectRect: result = $resultCode" }
            return if (faceInfos.isNullOrEmpty()) {
                GLog.w(TAG, LogFlag.DL, "[detect] no face")
                FaceRectResult()
            } else {
                val resultList = ArrayList<Rect>()
                faceInfos.forEach { resultList.add(it.faceRect) }
                FaceRectResult(resultList, resultCode)
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[detect] failed! exception=$it")
        }
        return FaceRectResult()
    }

    @Synchronized
    override fun detectFaceInfo(bitmap: Bitmap, pointCount: FaceKeyPointCount?): Array<FaceInfoMirror>? {
        if (faceDetect == null) {
            val result = init()
            if (!result) {
                GLog.e(TAG, LogFlag.DL, "[detectFaceInfo] failed! faceDetect is null")
                return null
            }
        }
        return runCatching {
            faceDetect?.detect(bitmap)?.let {
                convertFaceInfoListToMirror(it)
            }
        }.onFailure {
            GLog.w(TAG, LogFlag.DL, "detect error:$it")
        }.getOrNull()
    }

    @Synchronized
    override fun detectLandmark(bitmap: Bitmap, pointCount: FaceKeyPointCount?): Map<Rect, List<PointF>>? {
        if (faceDetect == null) {
            val result = init()
            if (!result) {
                GLog.e(TAG, LogFlag.DL, "[detectLandmark] failed! faceDetect is null")
                return null
            }
        }
        val faceDetMap: MutableMap<Rect, List<PointF>> = HashMap()
        runCatching {
            val faceList = faceDetect?.detect(bitmap)
            if (!faceList.isNullOrEmpty()) {
                for (face in faceList) {
                    val rect = face.faceRect
                    val points = face.facePoints
                    if (!rect.isEmpty) {
                        faceDetMap[rect] = points.asList()
                    }
                }
            }
        }.onFailure {
            GLog.w(TAG, LogFlag.DL, "detect error:$it")
        }
        return faceDetMap
    }

    override fun close() {
        faceDetect?.release()
        faceDetect = null
    }

    companion object {
        private const val PIXEL_BYTE_SIZE = 4
        private const val TAG = "FaceDetector"
    }
}