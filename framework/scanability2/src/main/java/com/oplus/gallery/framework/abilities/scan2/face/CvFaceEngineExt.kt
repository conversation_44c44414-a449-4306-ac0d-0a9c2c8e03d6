/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CvFaceEngineExt
 ** Description: AI 最佳表情扫描能力的工具类
 **
 ** Version: 1.0
 ** Date: 2025/03/10
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>  2025/03/10  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.face

import android.graphics.Bitmap
import android.graphics.Rect
import android.util.Size
import androidx.core.graphics.times
import androidx.core.graphics.toRect
import androidx.core.graphics.toRectF
import com.oplus.appability.IAbilityBus
import com.oplus.facedetectionapi.model.FaceInfo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams.Companion.noCrop
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory.createResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation

/**
 * 最小支持优化的短边
 */
const val MIN_FACE_RECT_SIZE = 160

private const val TAG = "CvFaceEngineExt"

/**
 * 是否支持表情优化
 * @param faceRect 人脸框
 * @return true/false 是否支持表情优化
 */
fun isFaceSupportOptimizing(faceRect: Rect): Boolean {
    return (faceRect.width() >= MIN_FACE_RECT_SIZE) && (faceRect.height() >= MIN_FACE_RECT_SIZE)
}

/**
 * 找到人脸最小的一张表情
 * @param faceInfoList 人脸信息列表
 * @return 最小的人脸框
 */
fun pickSmallestFaceRect(faceInfoList: Array<FaceInfo>): Rect? {
    return faceInfoList.minByOrNull { it.faceRect.width() * it.faceRect.height() }?.faceRect
}

/**
 * 根据faceRect的大小，获取不同分级下对应目标扫描图，旨在选择最节省开销的目标扫描图
 * @param abilityBus 能力总线
 * @param mediaItem mediaItem媒体库
 * @param minFaceRect 第一轮扫描结果
 * @param thumbSize 第一轮扫描结果基于的图片size
 */
fun getDetectTarget(abilityBus: IAbilityBus, mediaItem: MediaItem, minFaceRect: Rect, thumbSize: Size): Bitmap? {
    val minMediaItemSize = mediaItem.width.coerceAtMost(mediaItem.height)
    val minThumbSize = thumbSize.width.coerceAtMost(thumbSize.height)
    if (minMediaItemSize < minThumbSize) {
        GLog.w(TAG, LogFlag.DL) { "getDetectTarget: thumbSize is already greater than mediaItemSize, no need to detect again." }
        return null
    }
    val scale = minMediaItemSize.toFloat() / minThumbSize
    val originRect = minFaceRect.toRectF().times(scale).toRect()
    val minOriginFaceSize = originRect.width().coerceAtMost(originRect.height())
    val inThumbnailType = when {
        minOriginFaceSize >= MIN_FACE_RECT_SIZE * 2 -> ThumbnailSizeUtils.TYPE_CUSTOM_STICKER
        minOriginFaceSize > MIN_FACE_RECT_SIZE -> ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL
        else -> ThumbnailSizeUtils.TYPE_LARGE_THUMBNAIL
    }
    val resourcingAbility = abilityBus.requireAbility(IResourcingAbility::class.java)
    val resourceKey = createResourceKey(mediaItem)
    val options = ResourceGetOptions(
        inThumbnailType,
        -1,
        -1,
        noCrop(),
        CacheOperation.ReadWriteAllCache,
        SourceOperation.ReadLocal
    )
    if ((resourcingAbility != null) && (resourceKey != null)) {
        resourcingAbility.use {
            val result = it.requestBitmap(resourceKey, options, null, null)
            if (result != null) {
                return result.result
            }
        }
    }
    return null
}