/***********************************************************************************
 *  Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 *  All rights reserved.
 *
 *  File: - ContentSuggestionsHelper.kt
 *  Description: 登机牌识别工具类
 *
 *  Version: 1.0
 *  Date: 2024/09/05
 *  Author: lizhenya
 *  TAG:
 *  ------------------------------- Revision History: ----------------------------
 *  <author>                        <date>       <version>       <desc>
 *  ------------------------------------------------------------------------------
 *   lizhenya                     2024/09/05       1.0        build this module
 **********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.googlepassscan

import android.app.Notification
import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.googlepassscan.GooglePassScanInfo
import com.oplus.wrapper.app.contentsuggestions.ClassificationsRequest
import com.oplus.wrapper.app.contentsuggestions.ContentClassification
import com.oplus.wrapper.app.contentsuggestions.ContentSelection
import com.oplus.wrapper.app.contentsuggestions.ContentSuggestionsManager
import com.oplus.wrapper.app.contentsuggestions.SelectionsRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 登机牌识别工具类
 */
object ContentSuggestionsHelper {
    private const val TAG = "ContentSuggestionsHelper"
    private const val SETUP = "SETUP"
    private const val NOTIFY_INTER_ACTION_VERSION = 4
    private const val SUGGEST_VERSION = 6
    private const val BUNDLE_TYPED_VERSION = 3
    private const val INTERACTION_TYPE_VALUE = 5
    private const val INTERACTION_VERSION_CODE = 1
    private const val INTERACTION_SCREEN_SESSION_ID = 0L
    private const val INTERACTION_FOCUS_RECT_EXPAND_PX = 0
    private const val DETECT_TIME_OUT = 500L
    private const val SELECTION_CONTENT_SUCCESS = 0
    private const val CLASSIFY_CONTENT_SUCCESS = 0

    /**
     * 登机牌识别：同步方式
     */
    @JvmStatic
    fun doDetect(context: Context, bitmap: Bitmap): GooglePassScanInfo {

        var result: GooglePassScanInfo? = null
        val isDone = AtomicBoolean(false)
        val start = System.currentTimeMillis()

        GLog.d(TAG, LogFlag.DL) { "[doDetect] start" }
        runBlocking {
            val waitingJob = launch(Dispatchers.IO) {
                delay(DETECT_TIME_OUT)
                GLog.d(TAG, LogFlag.DL) { "[doDetect] timeout" }
            }

            async(Dispatchers.IO) {
                runCatching {
                    detectAction(context, bitmap) { detectResult ->
                        val alreadyDone = isDone.getAndSet(true)
                        GLog.d(TAG, LogFlag.DL) {
                            "[doDetect] async on detectResult:$detectResult,isActive:$isActive, alreadyDone=$alreadyDone"
                        }
                        if (alreadyDone.not()) {
                            result = detectResult ?: GooglePassScanInfo()
                        }
                        waitingJob.cancel()
                    }
                }.onFailure {
                    GLog.e(TAG, LogFlag.DL) { "[doDetect] runCatching error:${it.message}" }
                }
            }
            waitingJob.join()
            GLog.d(TAG, LogFlag.DL) { "[doDetect] done, cost time:${GLog.getTime(start)}ms" }
        }
        return result ?: GooglePassScanInfo(null, isException = true)
    }

    /**
     * 登机牌识别
     */
    private fun detectAction(context: Context, bitmap: Bitmap, detectResult: (GooglePassScanInfo?) -> Unit) {
        val startDetectTime = System.currentTimeMillis()
        GLog.d(TAG, LogFlag.DL) { "[detectAction] startDetectTime:$startDetectTime" }
        val taskId = startDetectTime.toInt()
        val contentSuggestionsManager = ContentSuggestionsManager(context)
        contentSuggestionsManager.notifyInteraction(SETUP, buildNotifyInteractionParams())
        contentSuggestionsManager.provideContextImage(taskId, buildProvideContextImageParams(bitmap))
        val suggestBundle = getSuggestBundle(context, taskId)
        val selectionsRequest = SelectionsRequest.Builder(taskId).setExtras(suggestBundle).build()
        contentSuggestionsManager.suggestContentSelections(
            selectionsRequest, Executors.newSingleThreadExecutor(),
            object : ContentSuggestionsManager.SelectionsCallback {
                override fun onContentSelectionsAvailable(selectionsCode: Int, contentSelectionList: List<ContentSelection>?) {
                    if ((selectionsCode != SELECTION_CONTENT_SUCCESS) || (contentSelectionList == null)) {
                        val endDetectTime = System.currentTimeMillis()
                        GLog.d(TAG, LogFlag.DL) {
                            "[detectAction] fail:selectionsCode:$selectionsCode,endDetectTime:$endDetectTime," +
                                    "cost time:${endDetectTime - startDetectTime}"
                        }
                        detectResult(null)
                        return
                    }
                    val classificationsRequest = ClassificationsRequest.Builder(contentSelectionList).setExtras(suggestBundle).build()
                    contentSuggestionsManager.classifyContentSelections(
                        classificationsRequest, Executors.newSingleThreadExecutor(),
                        object : ContentSuggestionsManager.ClassificationsCallback {
                            override fun onContentClassificationsAvailable(
                                classifyCode: Int,
                                contentClassificationList: MutableList<ContentClassification>?
                            ) {
                                if ((classifyCode != CLASSIFY_CONTENT_SUCCESS) || (contentClassificationList == null)) {
                                    val endDetectTime = System.currentTimeMillis()
                                    GLog.d(TAG, LogFlag.DL) {
                                        "[detectAction] fail:classifyCode:$classifyCode,endDetectTime:$endDetectTime," +
                                                "cost time:${endDetectTime - startDetectTime}"
                                    }
                                    detectResult(null)
                                    return
                                }
                                val parserClassifications = parserClassifications(contentClassificationList)
                                val googlePassScanInfo = if (parserClassifications.isNotEmpty()) parserClassifications.first() else null
                                val endDetectTime = System.currentTimeMillis()
                                GLog.d(TAG, LogFlag.DL) {
                                    "[detectAction] success:pendingIntent:${googlePassScanInfo?.pendingIntent},endDetectTime:" +
                                            "$endDetectTime,cost time:${endDetectTime - startDetectTime}"
                                }
                                detectResult(googlePassScanInfo)
                            }
                        })
                }
            }
        )
    }

    /**
     * 构建交互参数
     */
    private fun buildNotifyInteractionParams(): Bundle {
        val bundle = Bundle()
        bundle.putInt("Version", NOTIFY_INTER_ACTION_VERSION)
        return bundle
    }

    /**
     * 构建识别图片参数
     */
    private fun buildProvideContextImageParams(bitmap: Bitmap): Bundle {
        val bundle = Bundle()
        // 识别图片bitmap
        bundle.putParcelable(ContentSuggestionsManager.EXTRA_BITMAP, bitmap)
        return bundle
    }

    /**
     * 构建识别TaskID与内容参数
     */
    private fun getSuggestBundle(context: Context, taskId: Int): Bundle {
        val bundle = Bundle()
        // The version of the bundle, need to be >= 4.
        bundle.putInt("Version", SUGGEST_VERSION)
        bundle.putBundle("InteractionContext", getInteractionBundle())
        // The activity name of the calling app
        bundle.putString("PackageName", context.packageName)
        getActivityName().also { name ->
            GLog.d(TAG, LogFlag.DL) { "[getSuggestBundle] name:$name" }
            if (name.isNotEmpty()) {
                // The activity name of the calling app
                bundle.putString("ActivityName", "${context.packageName}/$name")
            }
        }
        //  Can be ingored, please try without setting this parameter
        bundle.putInt("BundleTypedVersion", BUNDLE_TYPED_VERSION)
        /**
         *  Can be ingored, default to false. pelase try without setting this parameter It's used to indicate whether this call is from
         *  work profile
         */
        bundle.putBoolean("IsManagedProfile", false)
        // The id of current task, should be unique for every call.
        bundle.putInt("TaskId", taskId)
        return bundle
    }

    /**
     * 识别区域内容参数
     */
    private fun getInteractionBundle(): Bundle {
        val interactionContextBundle = Bundle()
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBundle("previousContents", null)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBoolean("requestDebugInfo", false)
        val interactionTypeBundle = Bundle()
        // Specify the interaction type, 5 is SCREENSHOT.
        interactionTypeBundle.putInt("value", INTERACTION_TYPE_VALUE)
        interactionContextBundle.putBundle("interactionType", interactionTypeBundle)
        // Disable copy paste of the content extracted from image
        interactionContextBundle.putBoolean("disallowCopyPaste", false)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBoolean("expandFocusRect", false)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBoolean("isRtlContent", false)
        // Can be ignored, please try without setting this parameter,The version code for the UI library
        interactionContextBundle.putInt("versionCode", INTERACTION_VERSION_CODE)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putLong("screenSessionId", INTERACTION_SCREEN_SESSION_ID)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBoolean("requestStats", false)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBundle("interactionEvents", null)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBundle("focusRect", null)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putBoolean("isPrimaryTask", true)
        // Can be ignored, please try without setting this parameter
        interactionContextBundle.putInt("focusRectExpandPx", INTERACTION_FOCUS_RECT_EXPAND_PX)
        return interactionContextBundle
    }

    /**
     * 解析识别后的登机牌信息
     */
    @Suppress("DEPRECATION")
    private fun parserClassifications(classifications: List<ContentClassification>): List<GooglePassScanInfo> {
        val resultList = mutableListOf<GooglePassScanInfo>()
        classifications.forEach { classification ->
            classification.extras?.let { bundle ->
                val actionList = bundle.getParcelableArrayList<Notification.Action>("Actions") as? ArrayList<Notification.Action>
                actionList?.forEach { action ->
                    val actionIntent = action.actionIntent
                    GLog.d(TAG, LogFlag.DL) {
                        "[parserClassifications] actionIntent:$actionIntent"
                    }
                    if (actionIntent != null) {
                        resultList.add(GooglePassScanInfo(actionIntent))
                    }
                }
            }
        }
        return resultList
    }

    /**
     * 获取activity name
     */
    private fun getActivityName(): String {
        var activityName = ""
        val activityList = ActivityLifecycle.getActivityList()
        if (activityList.isNotEmpty()) {
            activityList[0].get()?.let { activity ->
                activityName = activity.componentName.className
            }
        }
        return activityName
    }
}