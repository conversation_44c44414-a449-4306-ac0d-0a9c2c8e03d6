/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIBestTakeDetectHelper
 ** Description: 最佳表情优化检测辅助类
 **
 ** Version: 1.0
 ** Date: 2025/3/28
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>  2025/3/28  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.flaw.besttake

import com.oplus.appability.IAbilityBus
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.EXPECT_REF_FACE_COUNT
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.EYE_OPEN_WIDTH_DEFAULT
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.MAX_FACE_COUNT
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.REF_FACE_LIMIT_COUNT
import com.oplus.gallery.business_lib.model.data.face.data.BestTakeExt.asAIFaceInfoMirror
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceCluster
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo.Companion.EXPRESSION_DETAIL_LAUGH
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo.Companion.EXPRESSION_DETAIL_NEUTRAL
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo.Companion.EXPRESSION_DETAIL_SMILE
import com.oplus.gallery.business_lib.model.data.face.data.mirror.AIFaceInfoMirror
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceAttrInfoMirror
import com.oplus.gallery.business_lib.model.data.utils.GalleryScanConstants.GROUP_ID_2
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.face.IFaceScanAbility
import com.oplus.gallery.framework.abilities.scan.flaw.FlawDetectFlag
import kotlin.math.abs
import kotlin.math.max

/**
 * 最佳表情推荐检测辅助类
 */
object AIBestTakeDetectHelper {

    private const val TAG = "AIBestTakeDetectHelper"

    /**
     * 闭眼与睁眼的最小差异值
     */
    private const val EYE_MIN_DIFF = 0.05f

    /**
     *单脸像素平均值
     */
    private const val MIN_SINGLE_FACE_PIXEL_MEAN = 15f

    /**
     * 所有睁眼的眼睛都不能是一只睁眼一只闭眼，都要过滤
     */
    private const val EYE_WINK_STATE = 1f

    /**
     * 人脸框最小高
     */
    private const val FACE_RECT_HEIGHT_MIN = 120

    /**
     * 人脸框最小宽
     */
    private const val FACE_RECT_WIDTH_MIN = 100

    /**
     * 左眼可见性，起始区间
     */
    private const val EYE_LEFT_START = 108

    /**
     * 左眼可见性，结束区间
     */
    private const val EYE_LEFT_END = 131

    /**
     * 右眼可见性，起始区间
     */
    private const val EYE_RIGHT_START = 136

    /**
     * 右眼可见性，结束区间
     */
    private const val EYE_RIGHT_END = 159

    /**
     * 嘴巴可见性，起始区间
     */
    private const val EYE_MOUTH_START = 191

    /**
     * 嘴巴可见性，结束区间
     */
    private const val EYE_MOUTH_END = 250

    /**
     * 眼睛、嘴巴遮挡比例
     */
    private const val VISIBLE_PERCENTAGE = 0.6

    /**
     * 人脸旋转角度异常阈值
     */
    private const val FACE_ROLL_THRESHOLD = 45f

    /**
     * 人脸偏移角度异常阈值
     */
    private const val FACE_YAW_THRESHOLD = 30f

    /**
     * 人脸倾斜角度异常阈值
     */
    private const val FACE_PITCH_THRESHOLD = 27f

    /**
     * 人脸质量分数异常阈值
     */
    private const val FACE_QUALITY_SCORE_THRESHOLD = 35

    private const val MOUTH_OPEN_OUTER_SCORE_MIN = 0.2

    private const val MOUTH_OPEN_OUTER_SCORE_MIN_SUB = 0.28

    private const val MOUTH_DOWN_H_SCORE_THRESHOLD = 0.10

    private const val MOUTH_OPEN_OUTER_SCORE_MAX = 0.6

    private const val VIS_MOUTH_RATIO_THRESHOLD = 0.97

    private const val AVG_PUPIL_AVG_CORNER_THRESHOLD = 0.16

    private const val MAX_PUPIL_CENTER_OFFSET_MAX = 0.23

    private const val MAX_PUPIL_CENTER_OFFSET_MIN = 0.14

    private const val AVG_PUPIL_CENTER_OFFSET_THRESHOLD = 0.09

    private const val MAX_PUPIL_EDGE_OFFSET = 0.09

    private const val MIN_EYE_OPEN_WIDTH = 0.35f

    // Roll最大允许差
    private const val ROLL_MAX_DIFF = 60f

    // Yaw最大允许差
    private const val YAW_MAX_DIFF = 60f

    // Pitch最大允许差
    private const val PITCH_MAX_DIFF = 30f

    private const val FACE_TOTAL_ATTISCORE_THRESHOLD = 5f

    private const val ATTI_SCORE_ROLL_PERCENTAGE = 0.2

    private const val ATTI_SCORE_YAW_PERCENTAGE = 0.4

    private const val ATTI_SCORE_PITCH_PERCENTAGE = 0.4

    private const val ATTI_SCORE_TOTAL_FACTOR = 3

    private const val ATTI_SCORE_DIFF_FACTOR = 0.5

    private const val FACE_RECT_WIDTH_MARGIN_PERCENTAGE = 0.02

    /**
     * 查询对应图是否聚类，聚类后查询是否存在相同group ID数据.
     * 用于快捷查询当前图是否可以快速使用最佳人脸替换功能
     * @param abilityBus 能力总线
     * @param filePath            目标图路径
     * @return result 是否可快速使用最佳人脸替换功能
     */
    @JvmStatic
    fun detectFlaw(abilityBus: IAbilityBus, filePath: String): FlawDetectFlag {
        // 查询是否face数据
        val mainFaceClusters: List<CvFaceCluster> = ApiDmManager.getScanDM().queryFaceInfoList(limit = 0, filePath)
        // 无数据说明未扫描过，不能返回NOT_RECOMMEND，还是保持UNDETECTED状态
        if (mainFaceClusters.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "detectFlaw failed, cvFaceClusters is empty.")
            return FlawDetectFlag.UNDETECTED
        }
        // 有数据且数据都非人脸，也判定为不推荐
        if (mainFaceClusters.all { it.isNoFace }) {
            GLog.d(TAG, LogFlag.DL, "detectFlaw failed, it's not a face.")
            return FlawDetectFlag.NOT_RECOMMEND
        }
        // 人脸数超过上限，不推荐
        if (mainFaceClusters.size > MAX_FACE_COUNT) {
            GLog.d(TAG, LogFlag.DL, "detectFlaw failed, cvFaceClusters' size = ${mainFaceClusters.size}.")
            return FlawDetectFlag.NOT_RECOMMEND
        }
        // 是否明确不推荐
        var isSolidNotRecommend = true
        for (mainCluster in mainFaceClusters) {
            // 存在无效groupId
            if (mainCluster.groupId <= GROUP_ID_2) {
                GLog.d(TAG, LogFlag.DL, "detectFlaw skipped, groupId is invalid.")
                continue
            }
            val mainFace = convertToAIFaceInfoMirror(abilityBus, mainCluster)
            if (!singleFiltrate(mainFace, mainFace, true)) {
                GLog.d(TAG, LogFlag.DL, "detectFlaw skipped, main face is invalid.")
                continue
            }
            // 主人脸闭眼+候选图人脸
            if (mainCluster.eyeOpenWidth < EYE_OPEN_WIDTH_DEFAULT) {
                // 只要有闭眼表情，后续需要能继续触发查询逻辑，实时判断当前时机的检测结果
                isSolidNotRecommend = false
                val subClusters: List<CvFaceCluster> = ApiDmManager.getScanDM().queryFaceInfoListByGroupId(
                    mainCluster.groupId, REF_FACE_LIMIT_COUNT, filePath, true,
                    EXPRESSION_DETAIL_SMILE, EXPRESSION_DETAIL_LAUGH, EXPRESSION_DETAIL_NEUTRAL
                )
                // Marked by wudanyang:现在端侧的过滤逻辑和云侧的过滤逻辑不完全匹配，如果要显示大图入口，需要有更多素材以提高优化成功的概率
                var singleFiltrateCount = 0
                for (subCluster in subClusters) {
                    val diff = subCluster.eyeOpenWidth - mainCluster.eyeOpenWidth
                    // 这里只能添加一些关键指标，并不一定跟校验接口的返回值完全一致
                    if ((diff > EYE_MIN_DIFF)
                        && singleFiltrate(convertToAIFaceInfoMirror(abilityBus, subCluster), mainFace, false)
                    ) {
                        singleFiltrateCount++
                        GLog.d(TAG, LogFlag.DL, "detectFlaw success, sub face is valid. singleFiltrateCount = $singleFiltrateCount")
                        if (singleFiltrateCount > EXPECT_REF_FACE_COUNT) {
                            return FlawDetectFlag.RECOMMEND
                        }
                    } else {
                        GLog.d(TAG, LogFlag.DL, "detectFlaw skipped, sub face is invalid.")
                    }
                }
            }
        }
        if (isSolidNotRecommend) {
            GLog.d(TAG, LogFlag.DL, "detectFlaw failed, contains no close eyes.")
            return FlawDetectFlag.NOT_RECOMMEND
        }
        GLog.d(TAG, LogFlag.DL, "detectFlaw failed, unable to determine detect result.")
        return FlawDetectFlag.UNDETECTED
    }

    private fun convertToAIFaceInfoMirror(abilityBus: IAbilityBus, cluster: CvFaceCluster): AIFaceInfoMirror {
        var attr = FaceAttrInfoMirror()
        var embedding: FloatArray? = null
        // 这里纯数学计算，开销可忽略不计
        abilityBus.requireAbility(IFaceScanAbility::class.java)?.use { ability ->
            ability.newFaceScoreCalculator().use { calculator ->
                attr = calculator.calcExpressionParams(cluster)
                cluster.feature?.let {
                    embedding = calculator.getDeserializeFeature(it)
                }
            }
        }
        return asAIFaceInfoMirror(cluster, attr, embedding)
    }

    /**
     * 单独过滤人脸
     * @param faceInfo 人脸信息
     * @param isTarget 是否主人脸
     */
    @JvmStatic
    @Suppress("LongMethod")
    fun singleFiltrate(faceInfo: AIFaceInfoMirror, mainFaceInfo: AIFaceInfoMirror, isTarget: Boolean = false): Boolean {
        return kotlin.runCatching {
            // 1.人脸框不能超出原图大小, 人脸框大于120*100(外扩后的)
            faceInfo.faceRect?.let {
                if ((it.height() < FACE_RECT_HEIGHT_MIN) || (it.width() < FACE_RECT_WIDTH_MIN)
                ) {
                    GLog.i(TAG, LogFlag.DL) { "[singleFiltrate] filter faceRect error. faceRect: ${faceInfo.faceRect}" }
                    return false
                }
                //边框增加 2%的宽度偏移兼容
                val widthMargin = it.width() * FACE_RECT_WIDTH_MARGIN_PERCENTAGE
                if ((it.left + widthMargin) < 0 ||
                    (it.right - widthMargin) > faceInfo.orgWidth ||
                    it.bottom > faceInfo.orgHeight
                ) {
                    GLog.i(TAG, LogFlag.DL) {
                        "[singleFiltrate] filter faceRect range error. faceRect: $it faceInfo.orgSize： ${faceInfo.orgWidth}-${faceInfo.orgHeight}"
                    }
                    return false
                }
            } ?: return false
            // 2.人脸姿态，roll(-45~45) yaw(-30~30) pitch(-27~27)
            if ((abs(faceInfo.roll) > FACE_ROLL_THRESHOLD) || (abs(faceInfo.yaw) > FACE_YAW_THRESHOLD) ||
                (abs(faceInfo.pitch) > FACE_PITCH_THRESHOLD)
            ) {
                GLog.i(TAG, LogFlag.DL) {
                    "[singleFiltrate] filter angle error. angle: [${faceInfo.roll}, ${faceInfo.yaw}, ${faceInfo.pitch}]"
                }
                return false
            }
            // 3.人脸质量，qualityScore（分数越小越好）< 35.0
            if (faceInfo.faceQuality > FACE_QUALITY_SCORE_THRESHOLD) {
                GLog.i(TAG, LogFlag.DL) {
                    "[singleFiltrate] filter faceQuality error. faceQuality: ${faceInfo.faceQuality}"
                }
                return false
            }
            // wink 状态下直接return
            if (faceInfo.eyeOpenWidth == EYE_WINK_STATE) return false
            // 人脸亮度不足直接返回
            if (faceInfo.facePixelMean <= MIN_SINGLE_FACE_PIXEL_MEAN) return false
            // 4.过滤嘴巴、眼睛的遮挡情况，主图和候选图逻辑需要区分
            val leftEyeVisible = leftEyeVisible(faceInfo.faceVisible)
            val rightEyeVisible = rightEyeVisible(faceInfo.faceVisible)
            val mouthVisible = mouthVisible(faceInfo.faceVisible)
            if (isTarget) {
                //过滤有一只眼睛不可见
                if ((leftEyeVisible && rightEyeVisible.not()) || (leftEyeVisible.not() && rightEyeVisible)) {
                    GLog.i(TAG, LogFlag.DL) {
                        "[singleFiltrate] filter one eyeVisible, leftEyeVisible: $leftEyeVisible, rightEyeVisible: $rightEyeVisible"
                    }
                    return false
                }
                if ((leftEyeVisible && rightEyeVisible).not() && mouthVisible.not()) {
                    GLog.i(TAG, LogFlag.DL) {
                        "[singleFiltrate] filter eyeVisible mouthVisible error. target leftEyeVisible: $leftEyeVisible, rightEyeVisible: " +
                            "$rightEyeVisible, mouthVisible: $mouthVisible"
                    }
                    return false
                }

                if (mouthVisible.not() && faceInfo.eyeOpenWidth > MIN_EYE_OPEN_WIDTH) {
                    GLog.i(TAG, LogFlag.DL) {
                        "[singleFiltrate] filter eyeOpenWidth error. target face open width ${faceInfo.eyeOpenWidth} && $mouthVisible"
                    }
                    return false
                }
            } else {
                //判断唇
                if (!checkFaceMouth(faceInfo)) return false
                //判断嘴
                if (!checkFacePupil(faceInfo)) return false
                if (getFaceTotalAttiScore(faceInfo, mainFaceInfo) < FACE_TOTAL_ATTISCORE_THRESHOLD) {
                    GLog.i(TAG, LogFlag.DL) {
                        "[singleFiltrate] filter faceTotalAttiScore ${faceInfo.faceTotalAttiScore}"
                    }
                    return false
                }
                // 眼睛或者嘴巴中的某一个被遮挡，则需要被过滤掉all(left_eye_visible，right_eye_visible，mouth_visible) = true
                if (leftEyeVisible.not() || rightEyeVisible.not() || mouthVisible.not()) {
                    GLog.i(TAG, LogFlag.DL) {
                        "[singleFiltrate] filter faceVisible error. leftEyeVisible: $leftEyeVisible, " +
                            "rightEyeVisible: $rightEyeVisible, mouthVisible: $mouthVisible"
                    }
                    return false
                }
            }
            return true
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) {
                "[singleFiltrate] filter faceVisible error: ${it.message}"
            }
        }.getOrElse {
            false
        }
    }

    private fun getFaceTotalAttiScore(faceInfo: AIFaceInfoMirror, dstFaceInfo: AIFaceInfoMirror): Float {
        val rollDelta = abs(dstFaceInfo.roll - faceInfo.roll)
        val yawDelta = abs(dstFaceInfo.yaw - faceInfo.yaw)
        val pitchDelta = abs(dstFaceInfo.pitch - faceInfo.pitch)

        val rollScore = calculateAttiScore(rollDelta, ROLL_MAX_DIFF, true)
        val yawScore = calculateAttiScore(
            yawDelta,
            YAW_MAX_DIFF,
            (abs(dstFaceInfo.yaw) > abs(faceInfo.yaw))
        )
        val pitchScore = calculateAttiScore(
            pitchDelta,
            PITCH_MAX_DIFF,
            (abs(dstFaceInfo.pitch) > abs(faceInfo.pitch))
        )

        val totalScore = (rollScore * ATTI_SCORE_ROLL_PERCENTAGE + yawScore * ATTI_SCORE_YAW_PERCENTAGE + pitchScore * ATTI_SCORE_PITCH_PERCENTAGE) *
            ATTI_SCORE_TOTAL_FACTOR
        return (totalScore.toFloat() + faceInfo.faceExpressionScore)
    }

    private fun calculateAttiScore(delta: Float, maxDiff: Float, isDstGreater: Boolean): Float {
        var score = 0f
        if (isDstGreater) {
            val temp = 1f - (delta / maxDiff) * ATTI_SCORE_DIFF_FACTOR
            score = max(0f, temp.toFloat())
        } else {
            val temp = 1f - (delta / maxDiff)
            score = max(0f, temp.toFloat())
        }
        GLog.i(TAG, LogFlag.DL) { "atti score is score $score" }
        return score
    }

    /**
     * 左眼是否可见（不被遮挡），其关键点vis被遮挡比例超过40%即被视为遮挡状态
     * 左眼点位：[108, 131]
     */
    private fun leftEyeVisible(faceVisible: ByteArray?): Boolean {
        faceVisible ?: return false
        if (faceVisible.size <= EYE_LEFT_END) {
            return false
        }
        // 计算区间内 1 的数量
        val countOnes = faceVisible.sliceArray(EYE_LEFT_START..EYE_LEFT_END).count { it == 1.toByte() }
        // 计算占比
        val total = EYE_LEFT_END - EYE_LEFT_START + 1
        val percentage = (countOnes * 1f) / total
        return percentage > VISIBLE_PERCENTAGE
    }

    /**
     * 右是否可见（不被遮挡），其关键点vis被遮挡比例超过40%即被视为遮挡状态
     * 右眼点位：[136, 159]
     */
    private fun rightEyeVisible(faceVisible: ByteArray?): Boolean {
        faceVisible ?: return false
        if (faceVisible.size <= EYE_RIGHT_END) {
            return false
        }
        // 计算区间内 1 的数量
        val countOnes =
            faceVisible.sliceArray(EYE_RIGHT_START..EYE_RIGHT_END).count { it == 1.toByte() }
        // 计算占比
        val total = EYE_RIGHT_END - EYE_RIGHT_START + 1
        val percentage = (countOnes * 1f) / total
        return percentage > VISIBLE_PERCENTAGE
    }

    /**
     * 嘴巴是否可见（不被遮挡）,其关键点vis被遮挡比例超过40%即被视为遮挡状态
     * 嘴巴点位：[191, 250]
     */
    private fun mouthVisible(faceVisible: ByteArray?): Boolean {
        faceVisible ?: return false
        if (faceVisible.size <= EYE_MOUTH_END) {
            return false
        }
        // 计算区间内 1 的数量
        val countOnes =
            faceVisible.sliceArray(EYE_MOUTH_START..EYE_MOUTH_END).count { it == 1.toByte() }
        // 计算占比
        val total = EYE_MOUTH_END - EYE_MOUTH_START + 1
        val percentage = (countOnes * 1f) / total
        return percentage > VISIBLE_PERCENTAGE
    }

    /**
     *  嘴巴是否正常判断
     *  @param faceInfo 人脸信息
     */
    private fun checkFaceMouth(faceInfo: AIFaceInfoMirror): Boolean {
        GLog.i(TAG, LogFlag.DL) {
            "[checkFaceMouth] " +
                "mouthOpenOuterScore ${faceInfo.mouthOpenOuterScore}" +
                "mouthDownHScore ${faceInfo.mouthDownHScore}" +
                "visMouthRatio ${faceInfo.visMouthRatio}"
        }

        if (faceInfo.mouthOpenOuterScore < MOUTH_OPEN_OUTER_SCORE_MIN) return false

        if ((faceInfo.mouthOpenOuterScore < MOUTH_OPEN_OUTER_SCORE_MIN_SUB) &&
            (faceInfo.mouthDownHScore < MOUTH_DOWN_H_SCORE_THRESHOLD)
        ) {
            return false
        }

        if (faceInfo.mouthOpenOuterScore > MOUTH_OPEN_OUTER_SCORE_MAX) return false

        if (faceInfo.visMouthRatio < VIS_MOUTH_RATIO_THRESHOLD) return false

        return true
    }

    /**
     * 眼睛是否正常判断
     * @param faceInfo 人脸信息
     */
    private fun checkFacePupil(faceInfo: AIFaceInfoMirror): Boolean {
        GLog.i(TAG, LogFlag.DL) {
            "[checkFacePupil] " +
                "avgPupilAvgCorner ${faceInfo.avgPupilAvgCorner}" +
                "maxPupilCenterOffset ${faceInfo.maxPupilCenterOffset}" +
                "avgPupilCenterOffset ${faceInfo.avgPupilCenterOffset}" +
                "maxPupilEdgeOffset ${faceInfo.maxPupilEdgeOffset}"
        }
        // 翻白眼
        if (faceInfo.avgPupilAvgCorner > AVG_PUPIL_AVG_CORNER_THRESHOLD) return false

        // 斜眼return
        if (faceInfo.maxPupilCenterOffset > MAX_PUPIL_CENTER_OFFSET_MAX) return false

        if ((faceInfo.maxPupilCenterOffset > MAX_PUPIL_CENTER_OFFSET_MIN) &&
            (faceInfo.avgPupilCenterOffset > AVG_PUPIL_CENTER_OFFSET_THRESHOLD)
        ) {
            return false
        }

        if (faceInfo.maxPupilEdgeOffset > MAX_PUPIL_EDGE_OFFSET) return false

        return true
    }
}