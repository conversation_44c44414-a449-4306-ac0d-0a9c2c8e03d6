/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FlawDetectAbilityImpl
 ** Description:缺陷检测能力接口的实现
 ** Version: 1.0
 ** Date: 2024-07-01
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2024-07-01     1.0
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.flaw

import android.content.ContentValues
import android.graphics.Bitmap
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.getThumbnailRotation
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper
import com.oplus.gallery.foundation.ai.FlawDetectApi
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.convert.ContentValuesConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FLAW_RECOGNIZE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_BEST_TAKE_RECOMMEND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_LIGHTING_RECOMMEND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_FLAW_RECOMMEND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE_RECOMMEND
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.scan.flaw.DetectItem
import com.oplus.gallery.framework.abilities.scan.flaw.FlawDetectFlag
import com.oplus.gallery.framework.abilities.scan.flaw.FlawDetectResult
import com.oplus.gallery.framework.abilities.scan.flaw.FlawDetectType
import com.oplus.gallery.framework.abilities.scan.flaw.IFlawDetectAbility
import com.oplus.gallery.framework.abilities.scan2.flaw.besttake.AIBestTakeDetectHelper
import com.oplus.gallery.framework.abilities.scan2.flaw.imagequalityenhance.ImageQualityEnhanceFlowDetectHelper
import com.oplus.gallery.standard_lib.util.os.ContextGetter

class FlawDetectAbilityImpl : AbsAppAbility(), IFlawDetectAbility {

    override val domainInstance: AutoCloseable = this
    private val api by lazy { FlawDetectApi(ContextGetter.context) }
    private val detectTypeSupportMap = mutableMapOf<FlawDetectType, Boolean>()
    private var resourcingAbility: IResourcingAbility? = null
    private val scanDM = ApiDmManager.getScanDM()

    @Volatile
    private var isLoad = false

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        updateConfig()
        resourcingAbility = abilityBus.requireAbility(IResourcingAbility::class.java)
        isLoad = true
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        api.release()
        resourcingAbility?.close()
        isLoad = false
    }

    override fun updateConfig() {
        abilityBus.requireAbility(ISettingsAbility::class.java)?.use {
            it.updateBlockingConfig(ContextGetter.context, FLAW_RECOGNIZE_DETECT_STATE, IS_AGREE_AI_UNIT_PRIVACY)
        }
        abilityBus.requireAbility(IConfigAbility::class.java)?.use {
            val supportAIFlawDetect = it.getBooleanConfig(FEATURE_IS_SUPPORT_FLAW_RECOMMEND) == true
            detectTypeSupportMap[FlawDetectType.PASSERBY] = supportAIFlawDetect
            detectTypeSupportMap[FlawDetectType.DEBLUR] = supportAIFlawDetect
            detectTypeSupportMap[FlawDetectType.DEREFLECTION] = supportAIFlawDetect
            detectTypeSupportMap[FlawDetectType.AILIGHTING] = it.getBooleanConfig(FEATURE_IS_SUPPORT_AI_LIGHTING_RECOMMEND) == true
            detectTypeSupportMap[FlawDetectType.IMAGE_QUALITY_ENHANCE] =
                it.getBooleanConfig(FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE_RECOMMEND) == true
            detectTypeSupportMap[FlawDetectType.BEST_TAKE] = it.getBooleanConfig(FEATURE_IS_SUPPORT_AI_BEST_TAKE_RECOMMEND) == true
        }
    }

    override fun detect(detectItem: DetectItem, detectTypes: List<FlawDetectType>): FlawDetectResult? {
        val types = checkDetectType(detectTypes)
        if (types.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "detect types is empty, detectTypes:$detectTypes, detectTypeSupportMap:$detectTypeSupportMap")
            return null
        }

        val result = detectInner(detectItem, types) ?: return null

        val mediaItem = detectItem.mediaItem
        val newExtTagFlags = getNewExtTagFlags(mediaItem.extTagFlagsAsLong, result)
        if (newExtTagFlags != mediaItem.extTagFlagsAsLong &&
            createDbUpdateReq(mediaItem, newExtTagFlags, true).exec() > 0
        ) {
            // createDbUpdateReq 的通知
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
            mediaItem.setExtTagFlags(newExtTagFlags)
        }
        return result
    }

    override fun detect(
        detectItems: List<DetectItem>,
        detectTypes: List<FlawDetectType>
    ): List<FlawDetectResult?> {
        val results = ArrayList<FlawDetectResult?>(detectItems.size)

        if (detectItems.isEmpty()) {
            GLog.e(TAG, LogFlag.DL, "detect mediaItems isEmpty")
            return results
        }
        val types = checkDetectType(detectTypes)
        if (types.isEmpty()) {
            GLog.w(TAG, LogFlag.DL, "detect types is empty, detectTypes:$detectTypes, detectTypeSupportMap:$detectTypeSupportMap")
            return results
        }
        val newExtTagFlagsMap = mutableMapOf<MediaItem, Long>()
        val batchReqBuilder = BatchReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
        detectItems.forEach { detectItem ->
            val result = detectInner(detectItem, types)
            results.add(result)
            if (null == result) {
                return@forEach
            }

            val mediaItem = detectItem.mediaItem
            val newExtTagFlags = getNewExtTagFlags(mediaItem.extTagFlagsAsLong, result)
            if (newExtTagFlags != mediaItem.extTagFlagsAsLong) {
                newExtTagFlagsMap[mediaItem] = newExtTagFlags
                batchReqBuilder.addDataReq(createDbUpdateReq(mediaItem, newExtTagFlags, true))
            }
        }
        val dbResults = batchReqBuilder.build().exec()
        // createDbUpdateReq 的通知
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        if (dbResults.isNotEmpty() && dbResults.all { it.count == 1 }) {
            newExtTagFlagsMap.forEach { (mediaItem, extTagFlags) ->
                mediaItem.setExtTagFlags(extTagFlags)
            }
        }
        return results
    }

    private fun detectInner(
        detectItem: DetectItem,
        types: List<FlawDetectType>
    ): FlawDetectResult? {
        if (isLoad.not()) {
            GLog.w(TAG, LogFlag.DL) { "detectInner, unload!" }
            return null
        }
        // 1、只跑未曾检测的
        val needDetectTypes = getNeedDetectTypes(detectItem.mediaItem, types)
        // 2、检测AI缺陷项（去模糊、去反光、路人...）
        val aiResult = detectAiFlaw(detectItem, needDetectTypes)
        // 3、检测画质增强
        val imageQualityEnhanceFlag = when {
            needDetectTypes.contains(FlawDetectType.IMAGE_QUALITY_ENHANCE).not() -> FlawDetectFlag.UNDETECTED
            ImageQualityEnhanceFlowDetectHelper.judgeImageRecommend(detectItem.mediaItem) -> FlawDetectFlag.RECOMMEND
            else -> FlawDetectFlag.NOT_RECOMMEND
        }
        // 4、检测最佳表情
        val bestTakeFlag = when {
            needDetectTypes.contains(FlawDetectType.BEST_TAKE).not() -> FlawDetectFlag.UNDETECTED
            else -> AIBestTakeDetectHelper.detectFlaw(abilityBus, detectItem.mediaItem.filePath)
        }
        GLog.d(TAG, "[detectInner] bestTakeFlag:$bestTakeFlag")
        // 5、转换结构体返回结果
        val result = FlawDetectResult(
            deblurFlag = if (needDetectTypes.contains(FlawDetectType.DEBLUR)) {
                aiResult?.deblurFlag.toDetectFlag()
            } else {
                FlawDetectFlag.UNDETECTED
            },
            passerbyFlag = if (needDetectTypes.contains(FlawDetectType.PASSERBY)) {
                aiResult?.passerbyFlag.toDetectFlag()
            } else {
                FlawDetectFlag.UNDETECTED
            },
            dereflectionFlag = if (needDetectTypes.contains(FlawDetectType.DEREFLECTION)) {
                aiResult?.dereflectionFlag.toDetectFlag()
            } else {
                FlawDetectFlag.UNDETECTED
            },
            imageQualityEnhanceFlag = imageQualityEnhanceFlag,
            bestTakeFlag = bestTakeFlag,
            aiLightingFlag = if (needDetectTypes.contains(FlawDetectType.AILIGHTING)) {
                aiResult?.aiLightingFlag.toDetectFlag()
            } else {
                FlawDetectFlag.UNDETECTED
            }
        )
        if (isLoad.not()) {
            GLog.w(TAG, LogFlag.DL) { "detectInner, after detect, unload!" }
            return null
        }
        return result
    }

    private fun getNeedDetectTypes(mediaItem: MediaItem, types: List<FlawDetectType>): List<FlawDetectType> {
        val extTagFlags = mediaItem.extTagFlagsAsLong
        return types.filter {
            when (it) {
                FlawDetectType.DEBLUR -> isUndetected(extTagFlags, Constants.AIFunc.SHIFT_BLUR)
                FlawDetectType.DEREFLECTION -> isUndetected(extTagFlags, Constants.AIFunc.SHIFT_REFLECTION)
                FlawDetectType.PASSERBY -> isUndetected(extTagFlags, Constants.AIFunc.SHIFT_PASSERBY)
                FlawDetectType.IMAGE_QUALITY_ENHANCE -> isUndetected(extTagFlags, Constants.AIFunc.SHIFT_IMAGE_QUALITY_ENHANCE)
                FlawDetectType.BEST_TAKE -> isUndetected(extTagFlags, Constants.AIFunc.SHIFT_BEST_TAKE)
                FlawDetectType.AILIGHTING -> isUndetected(extTagFlags, Constants.AIFunc.SHIFT_AI_LIGHTING)
            }
        }
    }

    private fun isUndetected(extTagFlags: Long, shift: Int): Boolean {
        return (extTagFlags shr shift) and Constants.AIFunc.MASK == Constants.AIFunc.STATUS_NOT_DETECTED
    }

    private fun detectAiFlaw(detectItem: DetectItem, types: List<FlawDetectType>): FlawDetectApi.FlawResult? {
        val detectTypes = types.mapNotNull {
            when (it) {
                FlawDetectType.DEBLUR -> FlawDetectApi.FlawType.DEBLUR
                FlawDetectType.DEREFLECTION -> FlawDetectApi.FlawType.DEREFLECTION
                FlawDetectType.PASSERBY -> FlawDetectApi.FlawType.PASSERBY
                FlawDetectType.AILIGHTING -> FlawDetectApi.FlawType.AI_LIGHTING
                else -> null
            }
        }
        if (detectTypes.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "detectAiFlaw, detectTypes is empty" }
            return null
        }

        val bitmap = detectItem.bitmap ?: requestBitmap(detectItem.mediaItem) ?: return null
        val labelIds = detectItem.labelIds ?: requestLabelIds(detectItem.mediaItem, bitmap)
        // 实际跑算法的检测项
        val passDetectTypes = checkAiFlawDetectType(detectTypes, labelIds)
        val result: FlawDetectApi.FlawResult? = if (passDetectTypes.isNotEmpty()) {
            api.detect(bitmap, passDetectTypes)
        } else {
            FlawDetectApi.FlawResult(
                FlawDetectApi.FlawFlag.NOT_RECOMMEND,
                FlawDetectApi.FlawFlag.NOT_RECOMMEND,
                FlawDetectApi.FlawFlag.NOT_RECOMMEND,
                FlawDetectApi.FlawFlag.NOT_RECOMMEND,
            )
        }

        if (bitmap != detectItem.bitmap) {
            BitmapPools.recycle(bitmap)
        }
        return result
    }

    private fun checkAiFlawDetectType(types: List<FlawDetectApi.FlawType>, labelIds: List<Int>): List<FlawDetectApi.FlawType> {
        return types.filter { type ->
            when (type) {
                FlawDetectApi.FlawType.PASSERBY -> {
                    labelIds.firstOrNull { id -> LABEL_ID_PASSERBY_BLACK_LIST.contains(id) }?.let {
                        GLog.d(TAG, LogFlag.DL) { "checkAiFlawDetectType, label id:$it in passerby black list, skip" }
                    } == null
                }

                FlawDetectApi.FlawType.DEREFLECTION -> {
                    labelIds.firstOrNull { id -> LABEL_ID_DEREFLECTION_BLACK_LIST.contains(id) }?.let {
                        GLog.d(TAG, LogFlag.DL) { "checkAiFlawDetectType, label id:$it in reflection black list, skip" }
                    } == null
                }

                FlawDetectApi.FlawType.DEBLUR -> {
                    val match = labelIds.any { id -> LABEL_ID_DEBLUR_WHITE_LIST.contains(id) }
                    if (match.not()) {
                        GLog.d(TAG, LogFlag.DL) { "checkAiFlawDetectType, label id:$labelIds not in blur white list, skip" }
                    }
                    match
                }
                FlawDetectApi.FlawType.AI_LIGHTING -> {
                    //补光没有白名单黑名单检测，直接返回 true
                    true
                }
                else -> false
            }
        }
    }

    private fun FlawDetectApi.FlawFlag?.toDetectFlag(): FlawDetectFlag {
        this ?: return FlawDetectFlag.UNDETECTED
        return when (this) {
            FlawDetectApi.FlawFlag.RECOMMEND -> FlawDetectFlag.RECOMMEND
            FlawDetectApi.FlawFlag.NOT_RECOMMEND -> FlawDetectFlag.NOT_RECOMMEND
        }
    }

    private fun checkDetectType(detectTypes: List<FlawDetectType>): List<FlawDetectType> {
        return detectTypes.filter { detectTypeSupportMap[it] == true }
    }

    private fun requestBitmap(mediaItem: MediaItem): Bitmap? {
        val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem) ?: let {
            GLog.w(TAG, LogFlag.DL, "requestBitmap, fail to create resourceKey, filePath:${PathMask.mask(mediaItem.filePath)}")
            return null
        }
        val options = ResourceGetOptions(
            inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(),
            inCacheOperation = CacheOperation.ReadWriteAllCache,
            inCropParams = CropParams.noCrop()
        )
        val tmpBitmap = resourcingAbility?.requestBitmap(resourceKey, options, null)?.result ?: let {
            GLog.w(TAG, LogFlag.DL, "requestBitmap, fail to request bitmap, resourceKey=$resourceKey")
            return null
        }
        val bitmap = BitmapUtils.rotateBitmap(tmpBitmap, mediaItem.getThumbnailRotation(), false)
        if (bitmap != tmpBitmap) {
            BitmapPools.recycle(tmpBitmap)
        }
        return bitmap
    }

    private fun requestLabelIds(mediaItem: MediaItem, bitmap: Bitmap): List<Int> {
        var labelIds = SearchDBHelper.querySceneIdsByFilePath(mediaItem.filePath)
        if (labelIds.isNullOrEmpty()) {
            labelIds = scanDM.scanLabel(mediaItem, bitmap)
        }
        return labelIds ?: emptyList()
    }

    private fun createDbUpdateReq(mediaItem: MediaItem, newExtTagFlags: Long, noNotify: Boolean): UpdateReq {
        return UpdateReq.Builder()
            .setWhere("${LocalColumns._ID}=${mediaItem.id}")
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, if (noNotify) DatabaseUtils.VALUE_TRUE else DatabaseUtils.VALUE_FALSE)
            .setConvert(ContentValuesConvert(
                ContentValues().apply {
                    put(LocalColumns.EXT_TAG_FLAGS, newExtTagFlags)
                }
            ))
            .build()
    }

    private fun getNewExtTagFlags(originExtTagFlags: Long, flawResult: FlawDetectResult): Long {
        var newExtTagFlags = originExtTagFlags
        if (flawResult.passerbyFlag != FlawDetectFlag.UNDETECTED) {
            newExtTagFlags = updateFlag(
                newExtTagFlags,
                Constants.AIFunc.SHIFT_PASSERBY,
                flawResult.passerbyFlag.toAIFuncStatus()
            )
        }
        if (flawResult.deblurFlag != FlawDetectFlag.UNDETECTED) {
            newExtTagFlags = updateFlag(
                newExtTagFlags,
                Constants.AIFunc.SHIFT_BLUR,
                flawResult.deblurFlag.toAIFuncStatus()
            )
        }
        if (flawResult.dereflectionFlag != FlawDetectFlag.UNDETECTED) {
            newExtTagFlags = updateFlag(
                newExtTagFlags,
                Constants.AIFunc.SHIFT_REFLECTION,
                flawResult.dereflectionFlag.toAIFuncStatus()
            )
        }
        if (flawResult.imageQualityEnhanceFlag != FlawDetectFlag.UNDETECTED) {
            newExtTagFlags = updateFlag(
                newExtTagFlags,
                Constants.AIFunc.SHIFT_IMAGE_QUALITY_ENHANCE,
                flawResult.imageQualityEnhanceFlag.toAIFuncStatus()
            )
        }
        if (flawResult.bestTakeFlag != FlawDetectFlag.UNDETECTED) {
            newExtTagFlags = updateFlag(
                newExtTagFlags,
                Constants.AIFunc.SHIFT_BEST_TAKE,
                flawResult.bestTakeFlag.toAIFuncStatus()
            )
        }
        if (flawResult.aiLightingFlag != FlawDetectFlag.UNDETECTED) {
            newExtTagFlags = updateFlag(
                newExtTagFlags,
                Constants.AIFunc.SHIFT_AI_LIGHTING,
                flawResult.aiLightingFlag.toAIFuncStatus()
            )
        }
        return newExtTagFlags
    }

    private fun FlawDetectFlag.toAIFuncStatus(): Long {
        return if (this == FlawDetectFlag.RECOMMEND) Constants.AIFunc.STATUS_RECOMMEND else Constants.AIFunc.STATUS_MISMATCH
    }

    /**
     * 更新flag值
     * @param originFlag 原flag
     * @param shift 更新值在flag上的偏移
     * @param value 更新到flag上的值
     */
    private fun updateFlag(originFlag: Long, shift: Int, value: Long): Long {
        return originFlag and (Constants.AIFunc.MASK shl shift).inv() or (value shl shift)
    }

    override fun close() = Unit

    companion object {
        private const val TAG = "FlawDetectAbilityImpl"

        /**
         * 去模糊的标签id白名单，目前去模糊只允许推荐成人、小孩、宠物等
         * https://odocs.myoas.com/sheets/Ee32M7RYVWfZRYA2
         */
        private val LABEL_ID_DEBLUR_WHITE_LIST = intArrayOf(
            3, 4, 15, 16, 18, 20, 26, 27, 45, 46, 47, 48, 70, 81, 85, 86, 88, 100, 101, 102, 106, 118, 119, 120, 148, 159, 168, 207,
            219, 222, 223, 246, 254, 266, 268, 284, 292, 294, 334, 335, 344, 345, 347, 355, 356, 357, 358, 377, 385, 399, 429, 449,
            484, 489, 493, 501, 502, 503, 511, 516, 532, 544, 546, 559, 562, 563, 565, 566, 567, 573, 585, 586, 590, 593, 594, 601,
            676, 682, 712, 713, 718, 745, 763, 817, 826, 847, 849, 870, 875, 876, 900, 929, 969, 971, 982, 1085, 1105, 1127, 1136,
            1146, 1254, 1267, 1276, 1435, 1443, 1448, 1476, 1488, 1491, 1559, 1587
        )

        /**
         * 去反光的标签id黑名单
         */
        private val LABEL_ID_DEREFLECTION_BLACK_LIST = intArrayOf(
            42, 90, 91, 92, 113, 114, 115, 116, 117, 128, 129, 130, 131, 132, 145, 151, 155, 156, 158, 162, 167, 169, 170, 174, 176,
            179, 186, 187, 189, 190, 197, 200, 201, 209, 210, 211, 213, 215, 216, 221, 226, 227, 232, 239, 240, 241, 248, 262, 278,
            302, 310, 337, 342, 343, 350, 365, 370, 374, 376, 402, 406, 414, 419, 428, 430, 439, 443, 456, 466, 468, 476, 493, 498,
            504, 506, 507, 524, 548, 554, 558, 564, 574, 576, 579, 595, 596, 600, 602, 605, 618, 629, 644, 647, 665, 672, 690, 693,
            694, 710, 712, 716, 719, 727, 743, 748, 749, 753, 764, 768, 772, 774, 783, 784, 798, 799, 819, 824, 825, 831, 843, 845,
            851, 853, 857, 860, 862, 872, 874, 892, 899, 905, 907, 918, 923, 956, 957, 959, 961, 970, 978, 995, 1013, 1017, 1027,
            1033, 1045, 1051, 1060, 1061, 1062, 1064, 1071, 1072, 1073, 1086, 1097, 1132, 1154, 1201, 1208, 1237, 1250, 1262, 1290,
            1296, 1297, 1298, 1299, 1306, 1308, 1312, 1328, 1331, 1333, 1378, 1389, 1397, 1421, 1430, 1431, 1440, 1454, 1500, 1501,
            1505, 1508, 1521, 1536, 1538, 1561, 1562, 1563, 1572, 1576, 1577, 1584, 1585, 1591, 1619
        )

        /**
         * 路人消除的标签id黑名单，含以下任一标签则不执行路人检测
         * 对应名称 https://odocs.myoas.com/sheets/9030MKMnEjHJ9Dqw
         */
        private val LABEL_ID_PASSERBY_BLACK_LIST = intArrayOf(
            41, 42, 43, 46, 47, 48, 77, 78, 80, 89, 90, 91, 92, 113, 114, 115, 116, 117, 127, 128, 129, 130, 131, 132, 144, 145, 150,
            151, 155, 156, 158, 159, 161, 162, 169, 170, 173, 174, 175, 179, 182, 186, 187, 189, 191, 201, 210, 211, 213, 216, 221,
            223, 225, 226, 227, 241, 245, 248, 266, 302, 337, 342, 343, 344, 347, 359, 370, 399, 402, 414, 428, 430, 456, 468, 480,
            487, 493, 498, 506, 507, 511, 524, 548, 553, 554, 564, 574, 576, 595, 596, 600, 602, 605, 614, 618, 629, 633, 644, 647,
            655, 665, 667, 671, 672, 681, 690, 694, 710, 712, 713, 719, 727, 742, 743, 760, 764, 768, 774, 778, 784, 799, 824, 825,
            831, 843, 845, 851, 853, 857, 860, 862, 872, 874, 892, 905, 907, 910, 918, 925, 945, 959, 970, 987, 1017, 1025, 1027,
            1045, 1062, 1064, 1066, 1071, 1073, 1078, 1086, 1110, 1132, 1154, 1182, 1195, 1201, 1208, 1237, 1262, 1290, 1296, 1301,
            1312, 1317, 1324, 1389, 1397, 1400, 1421, 1428, 1430, 1431, 1434, 1440, 1454, 1486, 1500, 1501, 1505, 1508, 1510, 1511,
            1513, 1518, 1519, 1521, 1522, 1534, 1536, 1538, 1561, 1562, 1563, 1572, 1576, 1577, 1584, 1585, 1591, 1597, 1604, 1619
        )
    }
}