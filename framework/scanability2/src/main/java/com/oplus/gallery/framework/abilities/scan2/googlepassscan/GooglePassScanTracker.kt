/**********************************************************************************
 *  Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 *  All rights reserved.
 *
 *  File: - GooglePassScanTracker.kt
 *  Description: 登机牌识别埋点工具类
 *
 *  Version: 1.0
 *  Date: 2024/09/12
 *  Author: lizhenya
 *  TAG:
 *  ------------------------------- Revision History: ----------------------------
 *  <author>                        <date>       <version>       <desc>
 *  ------------------------------------------------------------------------------
 *   lizhenya                     2024/09/12       1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.googlepassscan

import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 登机牌识别埋点工具类
 */
object GooglePassScanTracker {
    private const val TAG = "GooglePassScanTracker"

    /**
     * 埋点分类：大图
     */
    private const val EVENT_GROUP_TYPE = "2006007"

    /**
     * Google登机牌识别事件ID
     */
    private const val PASS_SCAN_EVENT_ID = "2006007090"

    /**
     * 后台识别成功
     */
    const val BACKGROUND_SCAN = 0

    /**
     * 前台识别成功
     */
    const val FOREGROUND_SCAN = 1

    /**
     * 上报识别登机牌成功
     * @param type 0：后台扫描 1：前台扫描(进大图触发扫描)
     */
    @JvmStatic
    fun tracePassSuccess(type: Int) {
        trace { track ->
            track.putProperty("scan_type", type)
            GLog.d(TAG, LogFlag.DL) {
                "trace google pass scan type:$type"
            }
            track.save()
        }
    }

    private fun trace(func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
            event = PASS_SCAN_EVENT_ID,
            type = EVENT_GROUP_TYPE,
            func = func
        )
    }
}