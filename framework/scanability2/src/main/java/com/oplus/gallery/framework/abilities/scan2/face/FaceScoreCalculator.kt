/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceScoreCalculator
 ** Description:人脸分数计算
 ** Version: 1.0
 ** Date: 2025-03-15
 ** Author: wudanyang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wudanyang@Apps.Gallery3D    2025-03-15     1.0
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.face

import com.oplus.faceapi.FaceAttribute
import com.oplus.faceapi.FaceVerify
import com.oplus.facedetectionapi.model.FaceInfo
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceAttrInfoMirror
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.scan.face.IFaceScoreCalculator

/**
 * 人脸分数计算器
 */
class FaceScoreCalculator : IFaceScoreCalculator {

    override fun calcExpressionParams(faceInfo: CvFaceInfo): FaceAttrInfoMirror {
        if (!CvFaceEngine.loadExistedNativeLibs()) {
            GLog.w(TAG, LogFlag.DL) { "calcExpressionParams: isLoaded = false" }
            return FaceAttrInfoMirror()
        }
        if ((faceInfo.faceLandmark == null) || (faceInfo.faceLandmarkVisible == null)) {
            GLog.w(TAG, LogFlag.DL) {
                "calcExpressionParams: faceLandmark = ${faceInfo.faceLandmark}" +
                    " , faceLandmarkVisible = ${faceInfo.faceLandmarkVisible}"
            }
            return FaceAttrInfoMirror()
        }
        if (faceInfo.faceLandmark?.size != faceInfo.faceLandmarkVisible?.size) {
            GLog.w(TAG, LogFlag.DL) {
                "calcExpressionParams: faceLandmark size = ${faceInfo.faceLandmark?.size}" +
                    " , faceLandmarkVisible size = ${faceInfo.faceLandmarkVisible?.size}"
            }
            return FaceAttrInfoMirror()
        }
        val info = FaceInfo()
        info.faceMorePoints = faceInfo.faceLandmark
        info.occlusion = faceInfo.faceLandmarkVisible
        // 这里只需要用到251个关键点和关键点可见性
        val attrInfo = FaceAttribute.expressionPara(info)
        return convertAttrInfoToMirror(attrInfo)
    }

    override fun getDeserializeFeature(feature: ByteArray): FloatArray {
        if (!CvFaceEngine.loadExistedNativeLibs()) {
            GLog.w(TAG, LogFlag.DL) { "getDeserializeFeature: isLoaded = false" }
            return FloatArray(0)
        }
        if (feature.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "getDeserializeFeature: feature is empty" }
            return FloatArray(0)
        }
        return FaceVerify.getDeserializeFeature(feature) ?: FloatArray(0)
    }

    override fun close() = Unit

    companion object {
        private const val TAG = "FaceScoreCalculator"
    }
}