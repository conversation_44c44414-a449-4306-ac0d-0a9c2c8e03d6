/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceAttributeDetect.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2018/04/09
 ** Author: yanchao.<PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yanchao.<PERSON>@Apps.Gallery3D     2018/04/09   1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.face;

import com.oplus.faceapi.FaceAttribute;
import com.oplus.faceapi.model.FaceAttrInfo;
import com.oplus.facedetectionapi.model.CvPixelFormat;
import com.oplus.facedetectionapi.model.FaceConfig;
import com.oplus.facedetectionapi.model.FaceInfo;
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo;

import static com.oplus.gallery.framework.abilities.scan2.face.FaceDataConvertorKt.convertAttrInfoToMirror;

/**
 * 人脸特性检测
 */
public class FaceAttributeDetect {
    private FaceAttribute mFaceAttribute = null;

    public FaceAttribute getFaceAttribute() {
        if (mFaceAttribute == null) {
            initFaceAttribute();
        }
        return mFaceAttribute;
    }

    private void initFaceAttribute() {
        init();
        mFaceAttribute = new FaceAttribute(null, FaceConfig.FaceImageResize.DEFAULT_CONFIG);
    }

    public boolean init() {
        CvFaceEngine.loadCvFaceDefaultLib();
        return true;
    }

    public void destroy() {
        if (mFaceAttribute != null) {
            mFaceAttribute.release();
            mFaceAttribute = null;
        }
    }

    public FaceAttributeInfo attribute(byte[] bgr, int imageWidth, int imageHeight, int imageStride, FaceInfo faceInfo) {
        FaceAttrInfo info = getFaceAttribute().attribute(
                bgr,
                CvPixelFormat.BGR888,
                imageWidth,
                imageHeight,
                imageStride,
                faceInfo);
        if (info != null) {
            /*
             * 调用完attribute函数后，info中才会有新增的2个字段
             * 然后将新增字段赋值给到faceInfo中
             */
            faceInfo.age = info.age;
            faceInfo.happyScore = info.happyScore;
            faceInfo.surpriseScore = info.surpriseScore;
            return new FaceAttributeInfo(convertAttrInfoToMirror(info));
        }
        return null;
    }
}
