/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ImageQualityEnhanceFlowDetectHelper
 ** Description:
 ** Version: 1.0
 ** Date : 2024-06-26
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  chenwenjun                      2024-06-26    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.scan2.flaw.imagequalityenhance

import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.foundation.exif.raw.ExifUtils


/**
 * 根据图片焦距，判断是否推荐做画质增强
 */
object ImageQualityEnhanceFlowDetectHelper {

    // 能推荐画质增强的 最小焦距
    private const val MIN_FOCAL_LENGTH_IN_35MM = 70

    // 能推荐画质增强的 最小焦距 （该值要 乘以焦距后 再用）
    private const val MIN_FOCAL_LENGTH_IN_35MM_2 = 6.5 * 6 // * FOCAL_LENGTH

    private const val MIN_IMAGE_SIZE = Constants.AIFunc.IMAGE_QUALITY_ENHANCE_MIN_IMAGE_SIZE
    private const val MAX_IMAGE_SIZE = Constants.AIFunc.MAX_IMAGE_SIZE

    /**
     * 根据图片焦距，判断是否推荐做画质增强
     * 注意：因为要读读片的extInfo（涉及io），所以不要在UI线程做
     */
    fun judgeImageRecommend(mediaItem: MediaItem): Boolean {
        // 1. 图片size是否满足
        if (checkSize(mediaItem).not()) {
            return false
        }

        // 2. 判断焦距是否满足
        val exif = ExifUtils.readExif(mediaItem.filePath) ?: return false
        val focalLengthIn35mm = convertFocalLength(exif.focalLengthIn35MM)
        val focalLength = convertFocalLength(exif.focalLength)
        return ((focalLengthIn35mm >= MIN_FOCAL_LENGTH_IN_35MM)
                && (focalLengthIn35mm >= MIN_FOCAL_LENGTH_IN_35MM_2 * focalLength))
    }

    private fun convertFocalLength(focalLength: String?): Float {
        focalLength ?: return 0f
        val converted = ExifUtils.convertFocalLength(focalLength)
        if (TextUtils.isEmpty(converted)) {
            return 0f
        }
        return try {
            converted.toFloat()
        } catch (e: NumberFormatException) {
            0f
        }
    }

    private fun checkSize(mediaItem: MediaItem): Boolean {
        return mediaItem.width in MIN_IMAGE_SIZE..MAX_IMAGE_SIZE &&
                mediaItem.height in MIN_IMAGE_SIZE..MAX_IMAGE_SIZE
    }
}