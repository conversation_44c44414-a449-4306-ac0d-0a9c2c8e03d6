/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceDataConvertor
 ** Description:人脸扫描算法SDK中的数据类与相册数据类的转换工具类
 ** Version: 1.0
 ** Date: 2025-01-08
 ** Author: wudanyang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wudanyang@Apps.Gallery3D    2025-01-08     1.0
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.face

import android.graphics.PointF
import android.graphics.Rect
import com.oplus.faceapi.model.FaceAttrInfo
import com.oplus.faceapi.model.FaceFeature
import com.oplus.faceapi.model.IFaceFeatureInfo
import com.oplus.facedetectionapi.model.FaceInfo
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceAttrInfoMirror
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceInfoMirror

/**
 * 将SDK中的[FaceInfo]转换成相册的镜像类[FaceInfoMirror]
 */
fun convertFaceInfoToMirror(info: FaceInfo): FaceInfoMirror {
    val faceInfo = FaceInfoMirror()
    faceInfo.faceRect = Rect()
    faceInfo.faceRect.set(info.faceRect)
    faceInfo.attributeFaceRect = Rect(info.attributeFaceRect)
    faceInfo.detectionRect = Rect(info.detectionRect)
    info.facePoints?.map { PointF(it) }?.toTypedArray()?.let { faceInfo.facePoints = it }
    info.occlusion?.copyOf()?.let { faceInfo.occlusion = it }
    info.faceMorePoints?.map { PointF(it) }?.toTypedArray()?.let { faceInfo.faceMorePoints = it }
    faceInfo.id = info.id
    faceInfo.score = info.score
    faceInfo.yaw = info.yaw
    faceInfo.pitch = info.pitch
    faceInfo.roll = info.roll
    faceInfo.eyeDist = info.eyeDist
    faceInfo.factorInside = info.factorInside
    faceInfo.factorBeyond = info.factorBeyond
    faceInfo.factorRoll = info.factorRoll
    faceInfo.factorSingle = info.factorSingle
    faceInfo.factorSource = info.factorSource
    faceInfo.factorSpoof = info.factorSpoof
    faceInfo.factorTiny = info.factorTiny
    faceInfo.happyScore = info.happyScore
    faceInfo.surpriseScore = info.surpriseScore
    faceInfo.age = info.age
    faceInfo.eyeOpenWidth = info.eyeOpenWidth
    faceInfo.facePixelMean = info.facePixelMean
    return faceInfo
}

fun convertMirrorToFaceInfo(info: FaceInfoMirror): FaceInfo {
    val faceInfo = FaceInfo()
    faceInfo.faceRect = Rect()
    faceInfo.faceRect.set(info.faceRect)
    faceInfo.attributeFaceRect = Rect(info.attributeFaceRect)
    faceInfo.detectionRect = Rect(info.detectionRect)
    faceInfo.facePoints = info.facePoints.map { PointF(it) }.toTypedArray()
    faceInfo.occlusion = info.occlusion?.copyOf()
    faceInfo.faceMorePoints = info.faceMorePoints.map { PointF(it) }.toTypedArray()
    faceInfo.id = info.id
    faceInfo.score = info.score
    faceInfo.yaw = info.yaw
    faceInfo.pitch = info.pitch
    faceInfo.roll = info.roll
    faceInfo.eyeDist = info.eyeDist
    faceInfo.factorInside = info.factorInside
    faceInfo.factorBeyond = info.factorBeyond
    faceInfo.factorRoll = info.factorRoll
    faceInfo.factorSingle = info.factorSingle
    faceInfo.factorSource = info.factorSource
    faceInfo.factorSpoof = info.factorSpoof
    faceInfo.factorTiny = info.factorTiny
    faceInfo.happyScore = info.happyScore
    faceInfo.surpriseScore = info.surpriseScore
    faceInfo.age = info.age
    faceInfo.eyeOpenWidth = info.eyeOpenWidth
    faceInfo.facePixelMean = info.facePixelMean
    return faceInfo
}

/**
 * 将相册中的[CvFaceInfo]列表转换成SDK的[IFaceFeatureInfo]列表
 */
fun convertToFaceFeature(faceList: List<CvFaceInfo>): List<IFaceFeatureInfo> {
    return faceList.map {
        FaceFeature().also { feature ->
            feature.groupId = it.groupId
            feature.feature = it.feature
        }
    }
}

/**
 * 将SDK中的[FaceInfo]列表转换成相册的镜像类[FaceInfoMirror]列表
 */
fun convertFaceInfoListToMirror(infoList: Array<FaceInfo>): Array<FaceInfoMirror> {
    return infoList.map { convertFaceInfoToMirror(it) }.toTypedArray()
}

/**
 * 将SDK中的[FaceAttrInfo]转换成相册的镜像类[FaceAttrInfoMirror]
 */
fun convertAttrInfoToMirror(faceAttrInfo: FaceAttrInfo): FaceAttrInfoMirror {
    return faceAttrInfo.let {
        FaceAttrInfoMirror(
            age = it.age,
            sex = it.sex,
            sexScore = it.sexScore,
            attractive = it.attractive,
            hasEyeGlass = it.hasEyeGlass,
            eyeGlassScore = it.eyeGlassScore,
            hasSunGlass = it.hasSunGlass,
            sunGlassScore = it.sunGlassScore,
            isSmile = it.isSmile,
            smileScore = it.smileScore,
            hasMask = it.hasMask,
            maskScore = it.maskScore,
            race = it.race,
            skinLuma = it.skinLuma,
            skinQuality = it.skinQuality,
            cover = it.cover,
            quality = it.quality,
            isEyeOpen = it.isEyeOpen,
            eyeOpenScore = it.eyeOpenScore,
            isMouthOpen = it.isMouthOpen,
            mouthOpenScore = it.mouthOpenScore,
            hasBeard = it.hasBeard,
            beardScore = it.beardScore,
            happyScore = it.happyScore,
            surpriseScore = it.surpriseScore,
            beauty = it.beauty,
            faceExpressionScore = it.faceExpressionScore,
            emotion = it.emotion,
            qualityScore = it.qualityScore,
            emotionScore = it.emotionScore,
            expressionDetail = it.expressionDetail,
            mouthOpenInnerScore = it.mouthOpenInnerScore,
            mouthOpenOuterScore = it.mouthOpenOuterScore,
            visMouthRatio = it.visMouthRatio,
            maxPupilCenterOffset = it.maxPupilCenterOffset,
            maxPupilEdgeOffset = it.maxPupilEdgeOffset,
            avgPupilCenterOffset = it.avgPupilCenterOffset,
            avgPupilAvgCorner = it.avgPupilAvgCorner,
            mouthDownHScore = it.mouthDownHScore
            /*faceCoverScore = it.faceCoverScore,
            faceCoverExpression = it.faceCoverExpression*/
        )
    }
}