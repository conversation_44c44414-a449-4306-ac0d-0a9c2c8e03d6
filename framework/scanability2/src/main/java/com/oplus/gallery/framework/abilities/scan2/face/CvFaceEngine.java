/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CvFaceEngine.java
 ** Description: 人脸处理引擎
 **
 ** Version: 1.0
 ** Date: 2016-11-25
 ** Author: xiuhua.ke
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** 80082820                       2016-11-22     1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.scan2.face;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.text.TextUtils;

import com.oplus.appability.IAbilityBus;
import com.oplus.faceapi.FaceCluster;
import com.oplus.faceapi.FaceLibrary;
import com.oplus.faceapi.FaceVerify;
import com.oplus.faceapi.FaceVideoCluster;
import com.oplus.faceapi.ModulePathSet;
import com.oplus.faceapi.model.FaceVideoInfo;
import com.oplus.faceapi.model.IFaceFeatureInfo;
import com.oplus.faceapi.utils.ColorConvertUtil;
import com.oplus.facedetectionapi.FaceDetect;
import com.oplus.facedetectionapi.model.CvPixelFormat;
import com.oplus.facedetectionapi.model.FaceConfig;
import com.oplus.facedetectionapi.model.FaceInfo;
import com.oplus.facedetectionapi.model.FaceOrientation;
import com.oplus.facedetectionapi.model.ResultCode;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils;
import com.oplus.gallery.business_lib.model.data.face.data.CvFaceInfo;
import com.oplus.gallery.business_lib.model.data.face.data.FaceAttributeInfo;
import com.oplus.gallery.business_lib.model.data.face.data.ImageFeature;
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceInfoMirror;
import com.oplus.gallery.business_lib.util.AssetHelper;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.storage.SPUtils;
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties;
import com.oplus.gallery.framework.abilities.download.IDownloadAbility;
import com.oplus.gallery.framework.abilities.download.RemoteModelInfo;
import com.oplus.gallery.framework.abilities.download.processor.ProcessResult;
import com.oplus.gallery.framework.abilities.download.template.ModelConfig;
import com.oplus.gallery.framework.abilities.download.template.ModelLoadingTemplate;
import com.oplus.gallery.framework.abilities.scan.face.IFaceAnalyzer;
import com.oplus.gallery.framework.abilities.scan.face.model.FaceModelConfig;
import com.oplus.gallery.framework.abilities.scan.face.model.FaceModelLoadingTemplate;
import com.oplus.gallery.standard_lib.file.File;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import static com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.FACE_ATTRIBUTE_MODEL;
import static com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.FACE_CLUSTER_MODEL;
import static com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.FACE_QUALITY_MODEL;
import static com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.FACE_VERIFY_MODEL;
import static com.oplus.gallery.framework.abilities.scan2.face.FaceDataConvertorKt.convertAttrInfoToMirror;
import static com.oplus.gallery.framework.abilities.scan2.face.FaceDataConvertorKt.convertFaceInfoListToMirror;
import static com.oplus.gallery.framework.abilities.scan2.face.FaceDataConvertorKt.convertFaceInfoToMirror;
import static com.oplus.gallery.framework.abilities.scan2.face.FaceDataConvertorKt.convertMirrorToFaceInfo;
import static com.oplus.gallery.framework.abilities.scan2.face.FaceDataConvertorKt.convertToFaceFeature;

public class CvFaceEngine implements IFaceAnalyzer {
    private static final String TAG = "CvFaceEngine";
    private static final String PREF_CV_FACE_VERSION_KEY = "cv_face_version_key";
    private static final String CLOUD_SYNC_FACE_STATE = "cloud_sync_face_state";
    private static final String HCI_FACE_ALBUM_API_DETECTION_CLUSTER_SO = "hci_face_album_api_detection_cluster";
    private static final String JNI_HCI_FACE_ALBUM_API_SO = "jni_hci_face_album_api";
    private static final int PIXEL_BYTE_SIZE = 3;
    private static final int ROTATION_0 = 0;
    private static final int ROTATION_90 = 90;
    private static final int ROTATION_180 = 180;
    private static final int ROTATION_270 = 270;
    private static final int LIMIT_FACE_COUNT_OF_GROUP_PHOTO = 5;

    /**
     * 标记人脸so文件+模型库是否加载成功
     */
    private static boolean sIsLoadingDone = false;
    /**
     * 标记人脸so是否加载成功
     */
    private static boolean sIsSOLoadingDone = false;
    private Context mContext;
    private FaceDetect mCvFaceDetector;
    private FaceVerify mCvFaceVerify;
    private FaceCluster mCvFaceCluster;
    private FaceVideoCluster mVideoFaceCluster = null;
    private FaceAttributeDetect mFaceAttributeDetect;
    private long mTime;
    private IAbilityBus mAbilityBus;

    public CvFaceEngine(Context context, IAbilityBus abilityBus) {
        mContext = context;
        mAbilityBus = abilityBus;
    }

    /**
     * 加载人脸的so和初始化模型库路径
     * so文件父目录 /data/data/com.coloros.gallery3d/files/component/FaceModelSource
     *
     * @return true成功，false 失败
     */
    public static boolean loadExistedNativeLibs() {
        if (sIsLoadingDone) {
            return true;
        }
        // so文件父目录 /data/data/com.coloros.gallery3d/files/component/FaceModelSource
        FaceModelConfig modelConfig = new FaceModelConfig();
        if (!modelConfig.isModelReady()) {
            GLog.d(TAG, LogFlag.DL, "loadExistedNativeLibs model is not ready yet.");
            return false;
        }
        String path = modelConfig.getDefaultComponentDirPath().getAbsolutePath();
        if (TextUtils.isEmpty(path)) {
            GLog.d(TAG, LogFlag.DL, "loadExistedNativeLibs path empty");
            return false;
        }
        try {
            GLog.d(TAG, "loadExistedNativeLibs success");
            // 加载自带的so版本
            loadCvFaceDefaultLib();
            // 初始化模型路径
            ModulePathSet.setModulePath(
                    ModulePathSet.ModuleType.AttributeType,
                    new String[]{path + File.separator + FACE_ATTRIBUTE_MODEL}
            );
            ModulePathSet.setModulePath(
                    ModulePathSet.ModuleType.ClusterType,
                    new String[]{path + File.separator + FACE_CLUSTER_MODEL}
            );
            ModulePathSet.setModulePath(
                    ModulePathSet.ModuleType.QualityType,
                    new String[]{path + File.separator + FACE_QUALITY_MODEL}
            );
            ModulePathSet.setModulePath(
                    ModulePathSet.ModuleType.VerifyType,
                    new String[]{path + File.separator + FACE_VERIFY_MODEL}
            );
            sIsLoadingDone = true;
        } catch (UnsatisfiedLinkError e) {
            GLog.w(TAG, "loadExistedNativeLibs Throwable:", e);
        }
        return sIsLoadingDone;
    }

    public static boolean loadCvFaceDefaultLib() {
        if (sIsSOLoadingDone) {
            return true;
        }
        try {
            System.loadLibrary(HCI_FACE_ALBUM_API_DETECTION_CLUSTER_SO);
            GLog.d(TAG, LogFlag.DL, "loadCvFaceDefaultLib load HCI_FACE_ALBUM_API_DETECTION_CLUSTER_SO success.");
            System.loadLibrary(JNI_HCI_FACE_ALBUM_API_SO);
            GLog.d(TAG, LogFlag.DL, "loadCvFaceDefaultLib load JNI_HCI_FACE_ALBUM_API_SO success.");
            sIsSOLoadingDone = true;
        } catch (Exception e) {
            GLog.e(TAG, "loadCvFaceDefaultLib failed, ", e);
        }
        return sIsSOLoadingDone;
    }

    @Override
    public boolean init(boolean waitUntilDownloaded, @NonNull ModelConfig modelConfig, RemoteModelInfo remoteModelInfo) {
        SharedPreferences sp = SPUtils.getSp(mContext, ComponentPrefUtils.COMPONENT_PREF_NAME);
        int currentVersion = sp.getInt(ComponentPrefUtils.FACE_COMPONENT_VERSION_KEY, 0);
        int defaultVersion = AssetHelper.getInstance().getComponentDefaultVersion(mContext,
                ComponentPrefUtils.FACE_COMPONENT_DEFAULT_VERSION_NAME);
        GLog.d(TAG, LogFlag.DL, "init defaultVersion is: " + defaultVersion + ", currentVersion is: " + currentVersion);
        mTime = System.currentTimeMillis();
        // 若服务器版本大于当前使用版本，会进入下载，并return false 下载；如果waitUntilDownloaded为true，将等待模型库下载完成
        if ((remoteModelInfo != null) && (remoteModelInfo.getVersion() > modelConfig.getCurrentVersion())) {
            ModelLoadingTemplate modelLoadingTemplate = new FaceModelLoadingTemplate(mContext, modelConfig, null);
            modelConfig.setRemoteModelInfo(remoteModelInfo);
            if (mAbilityBus != null) {
                GLog.d(TAG, LogFlag.DL, "init, start download model, remoteVersion is: " + remoteModelInfo.getVersion()
                        + " , waitUntilDownloaded = " + waitUntilDownloaded);
                // 如果需要等待下载完成，执行同步下载，等待下载完成后加载模型库
                if (waitUntilDownloaded) {
                    ProcessResult processResult = mAbilityBus.requireAbility(IDownloadAbility.class)
                            .downloadFileSync(modelLoadingTemplate, null);
                    if (!processResult.isSuccess()) {
                        return false;
                    }
                } else {
                    mAbilityBus.requireAbility(IDownloadAbility.class).downloadFileAsync(
                            modelLoadingTemplate,
                            IDownloadAbility.Priority.BACKGROUND,
                            true,
                            null);
                    return false;
                }
            }
        }

        boolean result = loadDefaultComponents(defaultVersion);
        GLog.d(TAG, LogFlag.DL, "init time:" + (System.currentTimeMillis() - mTime) + "ms" + ",result:" + result);
        return result;
    }

    private boolean loadDefaultComponents(int defaultVersion) {
        GLog.d(TAG, LogFlag.DL, "loadDefaultComponents");
        if (!loadExistedNativeLibs()) {
            GLog.w(TAG, LogFlag.DL, "loadDefaultComponents loadExistedNativeLibs fail");
            return false;
        }
        String version = FaceCluster.getClusterVersion();
        FaceLibrary.setDebug(false);
        GLog.d(TAG, LogFlag.DL, "loadDefaultComponentsLocal = " + version);
        setCvFaceVersion(mContext, version);
        ComponentPrefUtils.setIntPref(mContext, ComponentPrefUtils.FACE_COMPONENT_VERSION_KEY, defaultVersion);
        return true;
    }

    private static void setCvFaceVersion(Context context, String info) {
        String oldInfo = getCvFaceVersion(context);
        if ((oldInfo == null) || (!oldInfo.equals(info))) {
            SPUtils.setString(context, null, PREF_CV_FACE_VERSION_KEY, info);
            ComponentPrefUtils.setBooleanPref(context, ComponentPrefUtils.HAS_FACE_DATA_UPDATE_SUCCESS_KEY, true);
            ComponentPrefUtils.setBooleanPref(context, ComponentPrefUtils.HAS_HD_FACE_FOREGROUND_SCAN_OVER_KEY, false);
            ComponentPrefUtils.setBooleanPref(context, ComponentPrefUtils.HAS_FACE_SCAN_OVER_KEY, false);
        }
    }

    private static String getCvFaceVersion(Context context) {
        return SPUtils.getString(context, null, PREF_CV_FACE_VERSION_KEY, null);
    }

    private FaceAttributeDetect getFaceAttributeDetect() {
        if (mFaceAttributeDetect == null) {
            mFaceAttributeDetect = new FaceAttributeDetect();
        }
        return mFaceAttributeDetect;
    }

    private FaceDetect getFaceDetect() {
        if (mCvFaceDetector == null) {
            mCvFaceDetector = new FaceDetect(null, FaceConfig.FaceImageResize.DEFAULT_CONFIG, FaceConfig.FaceKeyPointCount.POINT_COUNT_106);
        }
        return mCvFaceDetector;
    }

    private FaceVerify getFaceVerify() {
        if (mCvFaceVerify == null) {
            mCvFaceVerify = new FaceVerify(null);
        }
        return mCvFaceVerify;
    }

    private FaceCluster getFaceCluster() {
        if (mCvFaceCluster == null) {
            mCvFaceCluster = new FaceCluster(null);
        }
        return mCvFaceCluster;
    }

    /**
     * 用于检查是否人脸聚类结果是否会有异常
     * @param result 传入人脸聚类结果
     * @param cvFaceCluster 检查是否初始化成功
     * @return 输出人脸聚类是否异常
     */
    private boolean checkGroupResultError(int result, FaceCluster cvFaceCluster) {
        // 检查人脸聚类结果不为0
        boolean isGroupError = result != ResultCode.OK.getValue();
        // 检查mCvFaceCluster是否初始化失败
        boolean isCvFaceClusterInitError = (cvFaceCluster != null) && (!cvFaceCluster.isInitialized());
        // 人脸聚类结果不为0或mCvFaceCluster初始化失败则证明获取人脸聚类失败
        GLog.d(TAG, LogFlag.DL, "[checkGroupResultError] isGroupError== " + isGroupError + "isCvFaceClusterInitError == " + isCvFaceClusterInitError);
        return isCvFaceClusterInitError || isGroupError;
    }

    private FaceVideoCluster getFaceVideoCluster() {
        if (mVideoFaceCluster == null) {
            mVideoFaceCluster = new FaceVideoCluster(null, null, null);
        }
        return mVideoFaceCluster;
    }

    private FaceOrientation getFaceOrientation(int rotation) {
        switch (rotation) {
            case ROTATION_0:
                return FaceOrientation.UP;
            case ROTATION_90:
                return FaceOrientation.LEFT;
            case ROTATION_180:
                return FaceOrientation.DOWN;
            case ROTATION_270:
                return FaceOrientation.RIGHT;
            default:
                return FaceOrientation.UNKNOWN;
        }
    }

    private byte[] getBitmapBits(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        int thumbWidth = bitmap.getWidth();
        int thumbHeight = bitmap.getHeight();
        byte[] bgr = new byte[thumbWidth * thumbHeight * PIXEL_BYTE_SIZE];
        ColorConvertUtil.getBGRFromBitmap(bitmap, bgr);
        return bgr;
    }

    /**
     * @param mediaItem
     * @param bitmap
     * @return ImageFeature[] 只有crash时会返回null
     */
    @Override
    public ImageFeature[] scanImageFeatures(MediaItem mediaItem, @NonNull Bitmap bitmap, int rotation) {
        if (bitmap == null) {
            return new ImageFeature[0];
        }
        int thumbWidth = bitmap.getWidth();
        int thumbHeight = bitmap.getHeight();
        // face detection
        mTime = System.currentTimeMillis();
        //get bitmap byte
        byte[] bgr = getBitmapBits(bitmap);
        if (bgr == null || bgr.length == 0) {
            return new ImageFeature[0];
        }
        try {
            FaceOrientation faceOrientation = getFaceOrientation(rotation);
            FaceInfo[] faceInfos = getFaceDetect().detect(bgr, CvPixelFormat.BGR888, thumbWidth, thumbHeight,
                    thumbWidth * PIXEL_BYTE_SIZE, faceOrientation);
            GLog.d(TAG, "face detection time:" + (System.currentTimeMillis() - mTime) + "ms");
            if ((faceInfos == null) || (faceInfos.length == 0)) {
                return new ImageFeature[0];
            }
            // feature extraction
            mTime = System.currentTimeMillis();
            ImageFeature[] imageFeatures = new ImageFeature[faceInfos.length];
            List<ImageFeature> list = new ArrayList<>();
            for (int i = 0; i < imageFeatures.length; i++) {
                //get image face feature and face attribute
                ImageFeature imageFeature = obtainImageFeature(bgr, faceInfos[i], thumbWidth, thumbHeight);
                if (imageFeature == null) {
                    continue;
                }
                imageFeature.setGroupPhoto(faceInfos.length >= LIMIT_FACE_COUNT_OF_GROUP_PHOTO);
                imageFeature.setMediaId(mediaItem.getMediaId());
                imageFeatures[i] = imageFeature;
                list.add(imageFeature);
            }
            GLog.d(TAG, "feature extraction time:" + (System.currentTimeMillis() - mTime) + "ms" + ", length: " + faceInfos.length
                    + ", list.size: " + list.size());
            if ((!list.isEmpty()) && (list.size() != imageFeatures.length)) {
                imageFeatures = list.toArray(new ImageFeature[list.size()]);
            } else if (list.isEmpty()) {
                return new ImageFeature[0];
            }
            return imageFeatures;
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getImageFeatures, fail", e);
        }
        return null;
    }

    private ImageFeature obtainImageFeature(byte[] bgr, FaceInfo faceInfo, int thumbWidth, int thumbHeight) {
        /**
         * 1.7.0聚类sdk提供的封面优选能力，这边需要先调用attribute后获取到一些信息赋值给到faceInfo中
         * 然后再调用getFeature，才能将新增的信息保存到feature中用于封面优选
         */
        FaceAttributeInfo attributeInfo
                = getFaceAttributeDetect().attribute(bgr, thumbWidth, thumbHeight, thumbWidth * PIXEL_BYTE_SIZE, faceInfo);
        byte[] feature = getFaceVerify().getFeature(bgr, CvPixelFormat.BGR888,
                thumbWidth, thumbHeight, thumbWidth * PIXEL_BYTE_SIZE, faceInfo);
        if (feature == null) {
            GLog.w(TAG, "getImageFeatures, feature is null!");
            return null;
        }
        if (isTestMode()) {
            GLog.i(TAG, "getImageFeatures, feature:" + Arrays.toString(feature));
        }
        ImageFeature imageFeature = new ImageFeature();
        imageFeature.setFeature(feature);
        imageFeature.setFaceInfo(convertFaceInfoToMirror(faceInfo));
        imageFeature.setThumbWidth(thumbWidth);
        imageFeature.setThumbHeight(thumbHeight);
        //NOTE:attribute must be called otherwise the face group method will crash!!!!
        imageFeature.setFaceAttributeInfo(attributeInfo);
        return imageFeature;
    }

    private boolean isTestMode() {
        return GallerySystemProperties.getBoolean("debug.gallery.scan.testmodel", false);
    }

    /**
     * @param imageFeatureList
     * @return
     */
    public int[] group(ArrayList<ImageFeature> imageFeatureList) {
        if (imageFeatureList.isEmpty()) {
            return null;
        }
        mTime = System.currentTimeMillis();
        int imageFeatureListSize = imageFeatureList.size();
        byte[][] features = new byte[imageFeatureListSize][];
        for (int i = 0; i < imageFeatureListSize; i++) {
            features[i] = imageFeatureList.get(i).getFeature();
        }
        int[] groups = new int[imageFeatureListSize];
        int result = getFaceCluster().faceCluste(features, groups);
        if (checkGroupResultError(result, getFaceCluster())) {
            GLog.e(TAG, LogFlag.DL, "[group] please check FaceCluster error");
            return null;
        }
        GLog.d(TAG, "group time:" + (System.currentTimeMillis() - mTime) + "ms" + ",   result:" + result);
        return groups;
    }

    /**
     * @param imageFeatureList
     * @param groups
     * @return
     */
    public int[] group(ArrayList<ImageFeature> imageFeatureList, int[] groups) {
        if (imageFeatureList.isEmpty()) {
            return null;
        }
        mTime = System.currentTimeMillis();
        int imageFeatureListSize = imageFeatureList.size();
        byte[][] features = new byte[imageFeatureListSize][];
        for (int i = 0; i < imageFeatureListSize; i++) {
            features[i] = imageFeatureList.get(i).getFeature();
        }
        int result = getFaceCluster().faceCluste(features, groups);
        if (checkGroupResultError(result, getFaceCluster())) {
            GLog.e(TAG, LogFlag.DL, "[group] please check FaceCluster error");
            return null;
        }
        GLog.d(TAG, "increment group time:" + (System.currentTimeMillis() - mTime) + ",result:" + result);
        return groups;
    }

    /**
     * group with faceList
     *
     * @param faceList
     * @return int[]
     */
    @Override
    public int[] groupFace(List<? extends CvFaceInfo> faceList) {
        if (faceList.isEmpty()) {
            return null;
        }
        mTime = System.currentTimeMillis();
        int imageFeatureListSize = faceList.size();
        int[] groups = new int[imageFeatureListSize];
        for (int i = 0; i < imageFeatureListSize; i++) {
            groups[i] = faceList.get(i).getGroupId();
        }
        List<IFaceFeatureInfo> iFaceFeatureInfoList = convertToFaceFeature(faceList);
        int result = getFaceCluster().faceCluste(iFaceFeatureInfoList, groups);
        if (checkGroupResultError(result, getFaceCluster())) {
            GLog.e(TAG, LogFlag.DL, "[groupFace] please check FaceCluster error");
            return null;
        }
        GLog.d(TAG, "group time:" + (System.currentTimeMillis() - mTime) + ",result:" + result);
        return groups;
    }

    public int getBestCoverIndex(int groupId) {
        if (!getFaceCluster().isInitialized()) {
            GLog.e(TAG, LogFlag.DL, "[getTopBestCoverIndex] FaceCluster initialized error");
            return -1;
        }
        return getFaceCluster().getBestCover(groupId);
    }

    @Override
    public int[] getTopBestCoverIndex(int groupId, int top) {
        if (!getFaceCluster().isInitialized()) {
            GLog.e(TAG, LogFlag.DL, "[getTopBestCoverIndex] FaceCluster initialized error");
            return null;
        }
        return getFaceCluster().getBestCover(groupId, top);
    }

    /**
     * Given the two byte[] feature to get similar of two face,
     * Maybe it will throw RuntimeException when Calling cv_verify_compare_feature() method failed!
     * If run exception,you can find the reason by ResultCode
     *
     * @param feature1 of first face
     * @param feature2 of other face
     * @return similar of two face (float[0-1])
     */
    public float compareFeature(byte[] feature1, byte[] feature2) {
        return getFaceVerify().compareFeature(feature1, feature2);
    }

    private void release() {
        GLog.d(TAG, "release");
        if (mCvFaceDetector != null) {
            mCvFaceDetector.release();
            mCvFaceDetector = null;
        }
        if (mCvFaceVerify != null) {
            mCvFaceVerify.release();
            mCvFaceVerify = null;
        }
        if (mCvFaceCluster != null) {
            mCvFaceCluster.release();
            mCvFaceCluster = null;
        }
        if (mVideoFaceCluster != null) {
            mVideoFaceCluster.release();
            mVideoFaceCluster = null;
        }
        if (mFaceAttributeDetect != null) {
            mFaceAttributeDetect.destroy();
            mFaceAttributeDetect = null;
        }
    }

    //------------------ for video face ---------------------//

    /**
     * @param mediaId
     * @param videoPath
     * @return ImageFeature[] 只有crash时会返回null
     */
    @Override
    public ImageFeature[] scanVideoFeatures(long mediaId, String videoPath) {
        try {
            FaceVideoInfo[] faceVideoInfos = videoDetect(videoPath);
            if (faceVideoInfos != null) {
                ImageFeature[] imageFeatures = new ImageFeature[faceVideoInfos.length];
                for (int i = 0; i < faceVideoInfos.length; ++i) {
                    imageFeatures[i] = faceVideoInfoToImageFeature(faceVideoInfos[i]);
                    imageFeatures[i].setMediaId(mediaId);
                    imageFeatures[i].setGroupPhoto(faceVideoInfos.length >= LIMIT_FACE_COUNT_OF_GROUP_PHOTO);
                }
                return imageFeatures;
            }
            return new ImageFeature[0];
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "getVideoFeatures, fail", e);
        }
        return null;
    }

    private ImageFeature faceVideoInfoToImageFeature(FaceVideoInfo faceVideoInfo) {
        ImageFeature imageFeature = new ImageFeature();
        imageFeature.setFeature(faceVideoInfo.feature);
        FaceInfo info = new FaceInfo();
        info.eyeDist = faceVideoInfo.eyeDist;
        info.facePoints = faceVideoInfo.points;
        info.faceRect = faceVideoInfo.rect;
        info.id = faceVideoInfo.groupId;
        info.pitch = faceVideoInfo.pitch;
        info.roll = faceVideoInfo.roll;
        info.yaw = faceVideoInfo.yaw;
        info.score = faceVideoInfo.score;
        imageFeature.setFaceInfo(convertFaceInfoToMirror(info));
        imageFeature.setFaceAttributeInfo(new FaceAttributeInfo(convertAttrInfoToMirror(faceVideoInfo.attr_info)));
        return imageFeature;
    }

    /**
     * @param videoPath
     * @return videoInfoArray [face count]
     */
    private FaceVideoInfo[] videoDetect(String videoPath) {
        if (TextUtils.isEmpty(videoPath)) {
            return null;
        }
        FaceVideoInfo[] videoInfoArray = getFaceVideoCluster().videoCluster(videoPath);
        if (videoInfoArray != null) {
            GLog.d(TAG, "videoDetect, videoInfoArray.length = " + videoInfoArray.length);
        }
        return videoInfoArray;
    }

    @Nullable
    @Override
    public FaceInfoMirror[] detectImage(@NonNull Bitmap bitmap, int rotation) {
        int thumbWidth = bitmap.getWidth();
        int thumbHeight = bitmap.getHeight();
        // face detection
        mTime = System.currentTimeMillis();
        //get bitmap byte
        byte[] bgr = getBitmapBits(bitmap);
        if (bgr == null || bgr.length == 0) {
            return new FaceInfoMirror[0];
        }
        try {
            FaceInfo[] faceInfos = getFaceDetect().detect(bgr, CvPixelFormat.BGR888, thumbWidth, thumbHeight,
                    thumbWidth * PIXEL_BYTE_SIZE, getFaceOrientation(rotation));
            bgr = null;
            return convertFaceInfoListToMirror(faceInfos);
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "detectImage, fail", e);
        }
        return new FaceInfoMirror[0];
    }

    @Nullable
    @Override
    public ImageFeature scanImageFeature(long mediaId, @NonNull Bitmap bitmap, @NonNull FaceInfoMirror faceInfo) {
        int thumbWidth = bitmap.getWidth();
        int thumbHeight = bitmap.getHeight();
        //get bitmap byte
        byte[] bgr = getBitmapBits(bitmap);
        //get image face feature and face attribute
        ImageFeature imageFeature = obtainImageFeature(bgr, convertMirrorToFaceInfo(faceInfo), thumbWidth, thumbHeight);
        if (imageFeature != null) {
            imageFeature.setMediaId(mediaId);
        }
        return imageFeature;
    }

    @Override
    public void close() throws Exception {
        release();
    }
}
