/**************************************************************************************************
 ** Copyright (C), 2023-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseCloudStatusPref.kt
 ** Description:欢太云、谷歌云通用，preference状态读写操作，公共类。
 ** Version: 1.0
 ** Date: 2023/9/04
 ** Author: <PERSON>.Wu@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  Frank.Wu@Apps.Gallery3D    2023/9/04    1.0              build this module
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.basecloudability.utils

import com.oplus.gallery.foundation.util.storage.SPUtils.getLong
import com.oplus.gallery.foundation.util.storage.SPUtils.setLong
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 欢太云、谷歌云通用，preference状态读写操作，公共类。
 */
object BaseCloudStatusPref {
    private val CLOUD_SYNC_PREF_NAME = ContextGetter.context.packageName + "_cloud_sync_info"
    private const val KEY_LAST_IGNORE_SAVE_POWER_OR_HIGH_TEMP_TIME = "key_last_ignore_save_power_time"

    /**
     * 记录最后一次同意忽略省电或高温提醒的时间
     */
    fun markLastIgnoreSavePowerOrHighTempTime() {
        setLong(ContextGetter.context, CLOUD_SYNC_PREF_NAME, KEY_LAST_IGNORE_SAVE_POWER_OR_HIGH_TEMP_TIME, System.currentTimeMillis())
    }

    /**
     * 返回最后一次同意忽略省电或高温提醒的时间
     */
    fun getLastIgnoreSavePowerOrHighTempTime(): Long {
        return getLong(ContextGetter.context, CLOUD_SYNC_PREF_NAME, KEY_LAST_IGNORE_SAVE_POWER_OR_HIGH_TEMP_TIME, 0)
    }
}