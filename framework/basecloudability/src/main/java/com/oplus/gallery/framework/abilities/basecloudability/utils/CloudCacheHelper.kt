/**************************************************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CloudCacheHelper.kt
 ** Description: 缩图缓存管理类
 ** Version: 1.0
 ** Date : 2023/03/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: --------------------------------------------------------
 **  <author>                   <data>         <version >     <desc>
 **  <EMAIL>    2023/03/22    1.0            build this module
 **
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.basecloudability.utils

import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cloud.CacheFile
import com.oplus.gallery.business_lib.cloud.DownloadSpec
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.DisplayNameMask
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

class CloudCacheHelper {

    /**
     * 从缓存路径中获取最大的缩图
     * @param cloudGlobalId 元数据唯一id
     */
    fun getLargestCachedThumbFile(cloudGlobalId: String): CacheFile? {
        DownloadSpec.values()
            .filter { ApiDmManager.getCloudSyncDM().isCache(it) && it.isOrigin.not() }
            .asReversed()
            .forEach { downloadSpec ->
                //根据类型和cloudId拼接path,如果文件存在就返回
                val filePath = getThumbPath(cloudGlobalId, downloadSpec) ?: return@forEach
                val file = File(filePath)
                if (file.exists()) {
                    return CacheFile(file, downloadSpec)
                }
            }
        //没有找到缩图，返回空
        GLog.d(TAG, "getLargestCachedThumbFile: no thumb file filter, return null")
        return null
    }

    /**
     * 清除过期的云端缓存文件
     */
    fun clearExpireCacheFiles() {
        val cacheDir = getCacheDir() ?: let {
            GLog.w(TAG, "[cacheDir] , cacheDir is null, return")
            return
        }
        val cacheFileDir = File(cacheDir)
        //文件夹不存在则直接返回
        if (!cacheFileDir.exists()) {
            return
        }
        //获取目录下所有文件
        val allFile = cacheFileDir.listFiles()
        //获取当前时间
        val endTime = System.currentTimeMillis()
        for (file in allFile) {
            //文件时间减去当前时间这样得到的差值是微秒级别
            val startTime = file.lastModified()
            val diff = endTime - startTime
            val days = diff / TimeUtils.TIME_1_DAY_IN_MS
            if (EXPIRE_DAYS <= days) {
                file.delete()
            }
        }
        // 删除废弃目录
        runCatching {
            FileOperationUtils.deleteDirectory(File(getDeprecatedCachePath()))
        }.onFailure {
            GLog.e(TAG, "clearExpireCacheFiles, deleteDirectory fail ", it)
        }
    }

    /**
     * 删除对应文件的小于指定尺寸的缩图缓存
     *
     * @param cloudGlobalId 元数据唯一id
     * @param maxThumbSizeExcluded 缩图的大小，
     */
    fun deleteSmallThumb(cloudGlobalId: String, maxThumbSizeExcluded: Int) {
        var deleteCount = 0
        DownloadSpec.values()
            .filter { ApiDmManager.getCloudSyncDM().isCache(it) && it.isOrigin.not() && (it.thumbSize < maxThumbSizeExcluded) }
            .asReversed()
            .forEach { downloadSpec ->
                //根据类型和cloudId拼接path,如果文件存在就删除
                val filePath = getThumbPath(cloudGlobalId, downloadSpec) ?: return@forEach
                val file = File(filePath)
                if (file.exists()) {
                    file.delete()
                    GLog.d(TAG) {
                        "[FileTransfer]deleteSmallThumb success: ${DisplayNameMask.mask(file.name)}"
                    }
                    deleteCount++
                } else {
                    GLog.d(TAG) {
                        "[FileTransfer]deleteSmallThumb , file has been delete : ${DisplayNameMask.mask(file.name)}"
                    }
                }
            }
        GLog.d(TAG, "[FileTransfer]deleteSmallThumb delete file count:$deleteCount")
    }

    /**
     * 清除所有的缩图缓存。如关闭云同步，把缩图缓存清掉
     */
    fun clearAllThumbCache() {
        val cacheDir = getCacheDir() ?: let {
            GLog.w(TAG, "[clearAllThumbCache] , cacheDir is null, return")
            return
        }
        runCatching {
            FileOperationUtils.deleteDirectory(File(cacheDir))
        }.onFailure {
            GLog.e(TAG, "clearAllThumbCache, fail ", it)
        }
    }

    companion object {

        private const val TAG = "CloudCacheHelper"

        /**
         * 清除缓存文件过期天数
         */
        const val EXPIRE_DAYS = 30

        /**
         * 缩略图缓存路径
         */
        const val THUMB_CACHE_PATH = "/cloud/thumb"

        /**
         * 获取某张图片的存储路径
         * @param cloudGlobalId 元数据唯一id
         * @param downloadSpec 规格参数
         */
        fun getThumbPath(cloudGlobalId: String, downloadSpec: DownloadSpec): String? {
            return getCacheDir()?.let {
                it + cloudGlobalId + "_" + downloadSpec.thumbSize
            }
        }

        /**
         * 获取存储路径
         * 主用户：
         * /storage/emulated/0/Android/data/com.coloros.gallery3d/files/cloud/thumb/
         */
        fun getCacheDir(): String? {
            return ContextGetter.context.getExternalFilesDir(null)?.absolutePath?.let {
                it + THUMB_CACHE_PATH + File.separator
            } ?: let {
                GLog.w(TAG, "getCacheDir, storage is not available, return null")
                null
            }
        }

        /**
         *  之前有个版本将缓存放到了/data/user/0/com.coloros.gallery3d/files/cloud/thumb/，
         *  而ck下载小缩图到
         *  /storage/emulated/0/Android/data/com.coloros.gallery3d/files/cloudkit_file/download/albumMetadata/
         *  这两个目录无法直接rename，需要进行copy，耗时长，转而使用[getCacheDir]，这个接口只留作删除废弃目录使用
         */
        fun getDeprecatedCachePath(): String {
            return ContextGetter.context.filesDir.path + THUMB_CACHE_PATH + File.separator
        }
    }
}