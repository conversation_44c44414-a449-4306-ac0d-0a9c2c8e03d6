/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : HeyDlnaCastTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/06/07
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>         <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/06/07      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.cast.protocol.dlna.heycastdlna

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.oplus.cast.service.sdk.config.CastDevice
import com.oplus.cast.service.sdk.config.DeviceType
import com.oplus.gallery.framework.abilities.cast.CastType
import com.oplus.gallery.framework.abilities.cast.ICastAction
import com.oplus.gallery.framework.abilities.cast.ICastCustomContent
import com.oplus.gallery.framework.abilities.cast.ICastDevice
import com.oplus.gallery.framework.abilities.cast.ICastSourceAdapter
import com.oplus.gallery.framework.abilities.cast.action.CastSlideTo
import com.oplus.gallery.framework.abilities.cast.action.UpdateContentAction
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.BitmapInfo
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.Source
import com.oplus.gallery.foundation.util.collections.ExtraMap
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito

class HeyDlnaCastTest {
    @MockK
    private lateinit var mockCastDevice: ICastDevice
    private lateinit var heyDlnaCast: HeyDlnaCast
    private var tempMimeType = "image/aaa"
    private lateinit var sourceAdapter: ICastSourceAdapter

    @Before
    fun init() {
        MockKAnnotations.init(this)
        val context = Mockito.mock(Context::class.java)
        ContextGetter.context = context
        heyDlnaCast = HeyDlnaCast()
        sourceAdapter = object : ICastSourceAdapter {
            override fun getRequestId(positionOffset: Int): Long = 1000L

            override fun getContentUri(positionOffset: Int): Uri? = Uri.EMPTY

            override fun getScreenNailBitmap(positionOffset: Int): Bitmap? = null

            override fun getName(positionOffset: Int): String = "Test"

            override fun getSupportedAbility(positionOffset: Int): ExtraMap? = null

            override fun getFilePath(positionOffset: Int): String = "aaa/bbb/ccc.jpg"

            override fun getMimeType(positionOffset: Int): String = tempMimeType

            override fun getModifiedDateInMs(positionOffset: Int): Long = 5000L

            override fun getVideoStartPosition(positionOffset: Int): Long = 0L

            override fun getWidth(positionOffset: Int): Int = 0

            override fun getHeight(positionOffset: Int): Int = 0

            override fun isResSupportCastOperation(positionOffset: Int): Boolean  = true

            override fun getCustomContent(castType: CastType): ICastCustomContent? = null

            override fun notifySourceDataChanged(customContent: ICastCustomContent?) = Unit
        }
    }

    @Test
    fun should_null_when_resetSearchedDevices_with_null() {
        // Given
        val expected = 0

        // When
        heyDlnaCast.searchedDevices.clear()
        heyDlnaCast.resetSearchedDevices(null)

        // Then
        Assert.assertEquals(expected, heyDlnaCast.searchedDevices.size)
    }

    @Test
    fun should_null_when_resetSearchedDevices_with_ip_null() {
        // Given
        val deviceInfos = mutableListOf<CastDevice>()
        deviceInfos.add(CastDevice().apply { deviceIp = null })
        val expected = 0

        // When
        heyDlnaCast.searchedDevices.clear()
        heyDlnaCast.resetSearchedDevices(deviceInfos)

        // Then
        Assert.assertEquals(expected, heyDlnaCast.searchedDevices.size)
    }

    @Test
    fun should_size1_when_resetSearchedDevices_with_1_valid_ip() {
        // Given
        val deviceInfos = mutableListOf<CastDevice>()
        deviceInfos.add(CastDevice().apply {
            uid = "abc"
            deviceIp = "ip1"
            deviceType = DeviceType.PLATINUM
        })
        val expected = 1

        // When
        heyDlnaCast.searchedDevices.clear()
        heyDlnaCast.resetSearchedDevices(deviceInfos)

        // Then
        Assert.assertEquals(expected, heyDlnaCast.searchedDevices.size)
    }

    @Test
    fun should_remove_duplicate_when_resetSearchedDevices_with_same_name_and_ip() {
        // Given
        val deviceInfos = mutableListOf<CastDevice>()
        val element = CastDevice().apply {
            deviceName = "name1"
            deviceIp = "ip1"
            uid = "abc1"
            deviceType = DeviceType.PLATINUM
        }
        deviceInfos.add(element)
        val element1 = CastDevice().apply {
            deviceName = "name1"
            deviceIp = "ip1"
            uid = "abc2"
            deviceType = DeviceType.LELINK
        }
        deviceInfos.add(element1)
        val expectedSize = 1
        val expectedKey = element1.deviceName + element1.deviceIp + CastType.DLNA

        // When
        heyDlnaCast.searchedDevices.clear()
        heyDlnaCast.resetSearchedDevices(deviceInfos)

        // Then
        Assert.assertEquals(expectedSize, heyDlnaCast.searchedDevices.size)
        Assert.assertEquals(expectedKey, heyDlnaCast.searchedDevices[0].getUniqueKey())
    }

    @Test
    fun should_not_change_searched_result_when_resetSearchedDevices() {
        // Given
        val deviceInfos = mutableListOf<CastDevice>()
        deviceInfos.add(CastDevice().apply {
            deviceName = "name1"
            deviceIp = "ip1"
            uid = "abc1"
            deviceType = DeviceType.PLATINUM
        })
        val element1 = CastDevice().apply {
            deviceName = "name1"
            deviceIp = "ip1"
            uid = "abc2"
            deviceType = DeviceType.LELINK
        }
        deviceInfos.add(element1)
        heyDlnaCast.searchedDevices.add(HeyDlnaCastDevice(element1))
        val expectedSize = 1
        val expectedKey = element1.deviceName + element1.deviceIp + CastType.DLNA

        // When
        heyDlnaCast.resetSearchedDevices(deviceInfos)

        // Then
        Assert.assertEquals(expectedSize, heyDlnaCast.searchedDevices.size)
        Assert.assertEquals(expectedKey, heyDlnaCast.searchedDevices[0].getUniqueKey())
    }

    @Test
    fun should_change_searched_result_when_resetSearchedDevices() {
        // Given
        val deviceInfos = mutableListOf<CastDevice>()
        val element0 = CastDevice().apply {
            deviceName = "name"
            deviceIp = "ip"
            uid = "abc1"
            deviceType = DeviceType.PLATINUM
        }
        deviceInfos.add(element0)
        val element1 = CastDevice().apply {
            deviceName = "name"
            deviceIp = "ip"
            uid = "abc2"
            deviceType = DeviceType.LELINK
        }
        deviceInfos.add(element1)
        heyDlnaCast.searchedDevices.add(HeyDlnaCastDevice(element0))
        val expectedSize = 1
        val expectedKey = element1.deviceName + element1.deviceIp + CastType.DLNA

        // When
        heyDlnaCast.resetSearchedDevices(deviceInfos)

        // Then
        Assert.assertEquals(expectedSize, heyDlnaCast.searchedDevices.size)
        Assert.assertEquals(expectedKey, heyDlnaCast.searchedDevices[0].getUniqueKey())
    }

    @Test
    fun should_null_when_createCastSource_with_adapter_null() {
        // Given
        val adapter: ICastSourceAdapter? = null

        // When
        val result = heyDlnaCast.createCastSource(adapter, 0, false)

        // Then
        Assert.assertNull(result)
    }

    @Test
    fun should_source_type_right_when_createCastSource_with_video() {
        // Given
        tempMimeType = "video/*"
        val adapter: ICastSourceAdapter = sourceAdapter

        // When
        val result = heyDlnaCast.createCastSource(adapter, 0, false)

        // Then
        Assert.assertTrue(result is Source)
    }

    @Test
    fun should_source_type_right_when_createCastSource_with_image() {
        // Given
        tempMimeType = "image/*"
        val adapter: ICastSourceAdapter = sourceAdapter

        // When
        val result = heyDlnaCast.createCastSource(adapter, 0, false)

        // Then
        Assert.assertTrue(result is Source)
    }

    @Test
    fun should_source_type_right_when_createCastSource_with_image_mime_heic() {
        // Given
        tempMimeType = "image/heic"
        val adapter: ICastSourceAdapter = sourceAdapter

        // When
        val result = heyDlnaCast.createCastSource(adapter, 0, false)

        // Then
        Assert.assertTrue(result is BitmapInfo)
    }

    @Test
    fun should_source_type_right_when_createCastSource_with_image_mime_endWith_dng() {
        // Given
        tempMimeType = "image/dng"
        val adapter: ICastSourceAdapter = sourceAdapter

        // When
        val result = heyDlnaCast.createCastSource(adapter, 0, false)

        // Then
        Assert.assertTrue(result is BitmapInfo)
    }

    @Test
    fun should_return_invalid_when_onUpdateContentActionHandle_with_updateCurrent_false() {
        // Given
        val action = UpdateContentAction.Builder().apply {
            updateCurrent = false
            this.replay = true
            this.slideTo = CastSlideTo.SLIDE_TO_NONE
            this.allowDuplicateRes = true
        }.build() as? UpdateContentAction

        // When
        var code = -1
        heyDlnaCast.onUpdateContentActionHandle(action!!) { stateCode, _, _ ->
            code = stateCode
        }

        // Then
        Assert.assertEquals(ICastAction.STATE_CODE_FAIL_INVALID_PARAMS, code)
    }

    @Test
    fun should_return_INVALID_SOURCE_when_onUpdateContentActionHandle_without_adapter() {
        // Given
        heyDlnaCast.connectedDevice = mockCastDevice
        val action = UpdateContentAction.Builder().apply {
            updateCurrent = true
            this.replay = true
            this.slideTo = CastSlideTo.SLIDE_TO_NONE
            this.allowDuplicateRes = true
        }.build() as? UpdateContentAction
        tempMimeType = "image/*"
        heyDlnaCast.setCastSourceAdapter(null)

        // When
        var code = -1
        heyDlnaCast.onUpdateContentActionHandle(action!!) { stateCode, _, _ ->
            code = stateCode
        }

        // Then
        Assert.assertEquals(ICastAction.STATE_CODE_FAIL_INVALID_SOURCE, code)
    }

    @Test
    fun should_return_fail_when_onUpdateContentActionHandle_without_adapter_and_connectDevice_null() {
        // Given
        val action = UpdateContentAction.Builder().apply {
            updateCurrent = true
            this.replay = true
            this.slideTo = CastSlideTo.SLIDE_TO_NONE
            this.allowDuplicateRes = true
        }.build() as? UpdateContentAction
        tempMimeType = "image/*"
        heyDlnaCast.connectedDevice = mockCastDevice
        heyDlnaCast.setCastSourceAdapter(null)

        // When
        var code = -1
        heyDlnaCast.onUpdateContentActionHandle(action!!) { stateCode, _, _ ->
            code = stateCode
        }

        // Then
        Assert.assertNotEquals(ICastAction.STATE_CODE_SUCCESS, code)
    }

    @Test
    fun should_return_success_when_onUpdateContentActionHandle_with_video() {
        // Given
        val action = UpdateContentAction.Builder().apply {
            updateCurrent = true
            this.replay = true
            this.slideTo = CastSlideTo.SLIDE_TO_NONE
            this.allowDuplicateRes = true
        }.build() as? UpdateContentAction
        tempMimeType = "video/*"
        heyDlnaCast.setCastSourceAdapter(sourceAdapter)

        // When
        var code = -1
        heyDlnaCast.onUpdateContentActionHandle(action!!) { stateCode, _, _ ->
            code = stateCode
        }

        // Then
        Assert.assertEquals(ICastAction.STATE_CODE_SUCCESS, code)
    }

    @Test
    fun should_return_success_when_onUpdateContentActionHandle_with_image_BitmapInfo() {
        // Given
        val action = UpdateContentAction.Builder().apply {
            updateCurrent = true
            this.replay = true
            this.slideTo = CastSlideTo.SLIDE_TO_NONE
            this.allowDuplicateRes = true
        }.build() as? UpdateContentAction
        tempMimeType = "image/heic"
        heyDlnaCast.setCastSourceAdapter(sourceAdapter)

        // When
        var code = -1
        heyDlnaCast.onUpdateContentActionHandle(action!!) { stateCode, _, _ ->
            code = stateCode
        }

        // Then
        Assert.assertEquals(ICastAction.STATE_CODE_SUCCESS, code)
    }
}