<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:paddingStart="@dimen/cast_dlna_item_padding_start">

    <RadioButton
        android:id="@+id/check"
        android:layout_width="@dimen/cast_dlna_check_button_size"
        android:layout_height="@dimen/cast_dlna_check_button_size"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/cast_dlna_check_button_margin_end"
        android:background="@null"
        android:clickable="false"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/cast_dlna_select_height"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@id/check"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:lines="1"
        android:textColor="@color/common_C20"
        android:textSize="@dimen/common_TD08"/>

    <ImageView
        android:id="@+id/divider"
        android:background="@drawable/coui_divider_horizontal_without_padding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:contentDescription="@null"/>

</RelativeLayout>