/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : HeyOafCastRemotePlayListener.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/04/14
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/04/14      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.cast.protocol.oaf

import com.oplus.cast.service.sdk.api.RemotePlayListener
import com.oplus.cast.service.sdk.config.PlayInfo
import com.oplus.cast.service.sdk.config.PlayState
import com.oplus.cast.service.sdk.config.RemoteDeviceInfo
import com.oplus.gallery.foundation.util.debug.GLog

internal open class HeyOafCastRemotePlayListener : RemotePlayListener {

    /**
     * 同步当前连接状态，同时也作为StreamPlayGetConnStatusCmd对应的response
     * state：连接状态，0表示连接，1表示断开；
     */
    override fun onRemoteConnectStatus(state: Int, requestId: Long) {
        GLog.d(TAG, "onRemoteConnectStatus state=$state requestId=$requestId")
    }

    /**
     * StreamPlayGetCurPosCmd对应的response，播放进度更新
     */
    override fun onPositionUpdate(duration: Long, position: Long, requestId: Long) {
        GLog.d(TAG, "onPositionUpdate duration=$duration position=$position requestId=$requestId")
    }

    /**
     * StreamPlayGetStateCmd对应的response，TV侧播放状态
     * state：TV侧播放状态；
     */
    override fun onRemotePlayState(state: PlayState?, requestId: Long) {
        GLog.d(TAG, "onRemotePlayState state=$state")
    }

    /**
     * StreamPlaySetUriCmd对应的response
     */
    override fun onSetUri(requestId: Long) {
        GLog.d(TAG, "onSetUri")
    }

    /**
     * StreamPlayGetUriCmd对应的response
     * playUri：当前播放uri；
     */
    override fun onGetUri(playUri: String?, requestId: Long) {
        GLog.d(TAG, "onGetUri playUri=$playUri")
    }

    /**
     * StreamPlayGetTriggerTypeCmd对应的response
     * type：当前触发方式，0表示NFC，1表示智能提醒
     */
    override fun onGetTriggerType(type: Int, requestId: Long) {
        GLog.d(TAG, "onGetTriggerType type=$type")
    }

    /**
     * StreamPlayPlayCmd对应的response
     */
    override fun onPlay(requestId: Long) {
        GLog.d(TAG, "onPlay")
    }

    /**
     * StreamPlayPauseCmd对应的response
     */
    override fun onPause(requestId: Long) {
        GLog.d(TAG, "onPause")
    }

    /**
     * StreamPlaySeekCmd对应的response
     */
    override fun onSeek(requestId: Long) {
        GLog.d(TAG, "onSeek")
    }

    /**
     * StreamPlayStopCmd对应的response
     */
    override fun onStop(requestId: Long) {
        GLog.d(TAG, "onPrivacyAllowed")
    }

    /**
     * StreamPlaySetVolCmd、StreamPlayAddVolCmd、StreamPlaySubVolCmd对应的response
     */
    override fun onVolumeChanged(volume: Int, requestId: Long) {
        GLog.d(TAG, "onVolumeChanged volume=$volume")
    }

    /**
     * StreamPlayMuteCmd对应的response
     */
    override fun onVolumeMuted(muted: Boolean, requestId: Long) {
        GLog.d(TAG, "onVolumeMuted muted=$muted")
    }

    /**
     * StreamPlayQuery对应的response，查询TV端播放器相关信息
     * info：同onCurrentPlayInfo中定义；
     */
    override fun onQueryResult(info: PlayInfo?, requestId: Long) {
        GLog.d(TAG, "onQueryResult")
    }

    /**
     * 同步播放媒体信息
     * info：TV端播放媒体信息
     */
    override fun onCurrentPlayInfo(info: PlayInfo?) {
        GLog.d(TAG, "onCurrentPlayInfo info=$info")
    }

    /**
     * TV侧App自定义message
     * msg：TV侧App自定义message
     */
    override fun onRemoteAppMessage(msg: String?) {
        GLog.d(TAG, "onRemoteAppMessage msg=$msg")
    }

    /**
     * TV侧透传信息回传
     */
    override fun onRemoteCustomMsg(msg: String?, requestId: Long) {
        GLog.d(TAG, "onRemoteCustomMsg msg=$msg")
    }

    /**
     * StreamPlayGetCurrentPlayer对应的response，TV 播放器名称
     * playerName：TV 播放器名称；
     */
    override fun onCurrentPlayer(playerName: String?, requestId: Long) {
        GLog.d(TAG, "onCurrentPlayer playerName=$playerName")
    }

    /**
     * StreamPlayCustomCmd对应的response
     * customExtra：自定义cmd透传信息；
     */
    override fun onCustomResult(customExtra: String?, requestId: Long) {
        GLog.d(TAG, "onCustomResult customExtra=$customExtra")
    }

    /**
     * StreamPlayGetDeviceInfoCmd对应的response
     * info：TV设备信息；
     */
    override fun onGetDeviceInfo(info: RemoteDeviceInfo?, requestId: Long) {
        GLog.d(TAG, "onGetDeviceInfo")
    }

    /**
     * 通用error info
     * errorInfo为Json字符串格式，如：{"errorCode":"-2","errorMsg":"get play info fail","errorDetail":""}
     * 其中errorDetail值为TV端CP内部错误反馈（预留），
     */
    override fun onError(errorInfo: String?, requestId: Long) {
        GLog.d(TAG, "onError errorInfo=$errorInfo")
    }

    /**
     * TV侧投屏准备完毕回调， 在这里面设置投屏资源等
     * preparedRes：返回结果，目前取值为ChannelState.REMOTE_CONTENT_PREPARED 和 ChannelState.REMOTE_MIRROR_PREPARED
     * CP侧调startOContent接口之后收到此回调表示TV侧准备完成，CP可以发送播放列表进行下一步操作。
     */
    override fun onRemotePreparedResult(preparedRes: Int) {
        GLog.d(TAG, "onRemotePreparedResult preparedRes=$preparedRes")
    }

    /**
     * StreamPlayMediaNextCmd对应的response
     */
    override fun onMediaNext(requestId: Long) {
        GLog.d(TAG, "onMediaNext")
    }

    /**
     * StreamPlayMediaPreviousCmd对应的response
     */
    override fun onMediaPrevious(requestId: Long) {
        GLog.d(TAG, "onMediaPrevious")
    }

    override fun onRemoteMediaNextCtrl(requestId: Long) {
        GLog.d(TAG, "onRemoteMediaNextCtrl")
    }

    override fun onRemoteMediaPreviousCtrl(requestId: Long) {
        GLog.d(TAG, "onRemoteMediaPreviousCtrl")
    }

    override fun onMediaScale(requestId: Long) {
        GLog.d(TAG, "onMediaScale")
    }

    override fun onMediaSyncCache(requestId: Long) {
        GLog.d(TAG, "onMediaSyncCache")
    }

    override fun onRemoteMediaSeekCtrl(position: Int, requestId: Long) {
        GLog.d(TAG, "onRemoteMediaSeekCtrl position=$position")
    }

    override fun onMediaTranslate(requestId: Long) {
        GLog.d(TAG, "onMediaTranslate")
    }

    override fun onMediaRotate(requestId: Long) {
        GLog.d(TAG, "onMediaRotate")
    }

    override fun onMediaTransform(requestId: Long) = Unit

    private companion object {
        private const val TAG = "HeyCastRemotePlayListener"
    }
}