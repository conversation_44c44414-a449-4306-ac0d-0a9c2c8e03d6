/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : CastTrackHelper.kt
 ** Description : 投屏的埋点类
 ** Version     : 1.0
 ** Date        : 2022/05/13
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/05/13      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.cast.track

import androidx.annotation.WorkerThread
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.EventId.O_CONTENT_ERROR_INFO
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.EventId.O_CONTENT_STATISTICS
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.ADD_VOLUME_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.AUTO_EXIT_O_CONTENT_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.ERROR_MSG
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.ERROR_TYPE
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.FAST_FORWARD_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.FAST_REWIND_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.MANUAL_CHANGE_TO_O_CONTENT_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.MANUAL_EXIT_O_CONTENT_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.MINUS_VOLUME_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.MIRROR_AUTO_CHANGE_TO_O_CONTENT_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.O_CONTENT_CHANGE_TO_MIRROR_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.PLAYBACK_PAUSE_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Key.PLAYBACK_RESUME_COUNT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.AUTO_EXIT_O_CONTENT_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.CAST_FILE_NAME
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.LAST_SEND_TIME_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.MANUAL_CHANGE_TO_O_CONTENT_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.MANUAL_EXIT_O_CONTENT_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.MIRROR_AUTO_CHANGE_TO_O_CONTENT_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.O_CONTENT_CHANGE_TO_MIRROR_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.O_CONTENT_REMOTE_CONTROL_ADD_VOLUME_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.O_CONTENT_REMOTE_CONTROL_FAST_FORWARD_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.O_CONTENT_REMOTE_CONTROL_FAST_REWIND_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.O_CONTENT_REMOTE_CONTROL_MINUS_VOLUME_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.O_CONTENT_REMOTE_CONTROL_PLAYBACK_PAUSE_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Pref.O_CONTENT_REMOTE_CONTROL_PLAYBACK_RESUME_COUNT_KEY
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.TYPE_CAST
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Value.ERROR_TYPE_HEY_CAST_ERROR
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Value.ERROR_TYPE_START_O_CONTENT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Value.ERROR_TYPE_SYNERGY_CHANNEL_CONNECT
import com.oplus.gallery.framework.abilities.cast.track.CastTrackConstant.Value.ERROR_TYPE_SYNERGY_QUERY_DEVICE
import com.oplus.gallery.standard_lib.app.TrackScope
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

internal object CastTrackHelper {
    private const val TAG = "CastTrackHelper"
    private const val USER_ACTION_PROPERTY_TIME_INTERVAL = TimeUtils.TIME_1_DAY_IN_MS // 1days
    private const val TIME_DELAY_SECONDS = TimeUtils.TIME_5_SEC_IN_MS.toLong() // 5s

    /**
     * 设备互联搜索设备异常
     */
    @JvmStatic
    fun trackSynergyQueryDeviceError(errorMsg: String) {
        trackErrorInfo(ERROR_TYPE_SYNERGY_QUERY_DEVICE, errorMsg)
    }

    /**
     * 设备互联通道复用异常
     */
    @JvmStatic
    fun trackSynergyChannelConnectError(errorMsg: String) {
        trackErrorInfo(ERROR_TYPE_SYNERGY_CHANNEL_CONNECT, errorMsg)
    }

    /**
     * 启动私有协议内容投屏异常
     */
    @JvmStatic
    fun trackStartOContentError(errorMsg: String) {
        trackErrorInfo(ERROR_TYPE_START_O_CONTENT, errorMsg)
    }

    /**
     * 私有协议内容投屏过程异常
     */
    @JvmStatic
    fun trackOContentHeyCastError(errorMsg: String) {
        trackErrorInfo(ERROR_TYPE_HEY_CAST_ERROR, errorMsg)
    }

    /**
     * 埋点：2006022003 私有协议内容投屏异常信息收集
     * @param errorType 异常类型
     *                  0：synergy_query_device
     *                  1：synergy_channel_connect
     *                  2：start_o_content
     *                  3：heycast_error
     * @param errorMsg 异常信息
     */
    private fun trackErrorInfo(errorType: String, errorMsg: String) {
        trackCast(O_CONTENT_ERROR_INFO) { track ->
            track.putProperty(ERROR_TYPE, errorType)
            track.putProperty(ERROR_MSG, errorMsg)
            track.save()
        }
    }

    /**
     * 每天第一次启动进程时上报（间隔为24小时）
     * 每日一次的数据埋点上报
     */
    @JvmStatic
    fun trackOnceADay() = TrackScope.launch {
        val lastTime = SPUtils.getLong(ContextGetter.context, CAST_FILE_NAME, LAST_SEND_TIME_KEY, 0)
        if ((System.currentTimeMillis() - lastTime) <= USER_ACTION_PROPERTY_TIME_INTERVAL) {
            return@launch
        }
        delay(TIME_DELAY_SECONDS)
        internalTrackOnceADay()
        SPUtils.setLong(ContextGetter.context, CAST_FILE_NAME, LAST_SEND_TIME_KEY, System.currentTimeMillis())
    }

    /**
     * 每天第一次启动进程时上报（间隔为24小时）
     * 每日一次的数据埋点上报
     * 该方法直接填充每天一次要上报的内容，时间判断和协程都由trackOnceADay控制
     */
    @WorkerThread
    private fun internalTrackOnceADay() {
        trackOContentStatusAndStatistics()
    }

    /**
     * 为私有协议自适应投屏的状态和数据埋点
     */
    @WorkerThread
    private fun trackOContentStatusAndStatistics() {
        // 触发自适应切换镜像的次数
        val changeToMirrorCount = getSPInt(O_CONTENT_CHANGE_TO_MIRROR_COUNT_KEY)
        // 手动退出私有协议自适应内容投屏次数
        val manualExitCount = getSPInt(MANUAL_EXIT_O_CONTENT_COUNT_KEY)
        // 自动退出私有协议自适应内容投屏次数
        val autoExitCount = getSPInt(AUTO_EXIT_O_CONTENT_COUNT_KEY)
        // 从镜像自动进入私有协议自适应内容投屏的次数
        val mirrorChangeToOContentCount = getSPInt(MIRROR_AUTO_CHANGE_TO_O_CONTENT_COUNT_KEY)
        // 手动进入私有协议自适应内容投屏的次数
        val manualChangeToOContentCount = getSPInt(MANUAL_CHANGE_TO_O_CONTENT_COUNT_KEY)
        // TV反控 播放次数（暂停后再播放也算）
        val remoteControlPlaybackResumeCount = getSPInt(O_CONTENT_REMOTE_CONTROL_PLAYBACK_RESUME_COUNT_KEY)
        // TV反控 暂停次数
        val remoteControlPlaybackPauseCount = getSPInt(O_CONTENT_REMOTE_CONTROL_PLAYBACK_PAUSE_COUNT_KEY)
        // TV反控 快进次数
        val remoteControlPlaybackFastForwardCount = getSPInt(O_CONTENT_REMOTE_CONTROL_FAST_FORWARD_COUNT_KEY)
        // TV反控 快退次数
        val remoteControlPlaybackFastRewindCount = getSPInt(O_CONTENT_REMOTE_CONTROL_FAST_REWIND_COUNT_KEY)
        // TV反控 加音量次数
        val remoteControlPlaybackAddVolumeCount = getSPInt(O_CONTENT_REMOTE_CONTROL_ADD_VOLUME_COUNT_KEY)
        // TV反控 减音量次数
        val remoteControlPlaybackMinusVolumeCount = getSPInt(O_CONTENT_REMOTE_CONTROL_MINUS_VOLUME_COUNT_KEY)

        // 是否使用过私有协议自适应内容投屏： 既没有进入过内容投屏也没有出去过内容投屏则说明没有用过
        val hasUsedOContent =
            (changeToMirrorCount + manualExitCount + autoExitCount + mirrorChangeToOContentCount + manualChangeToOContentCount) != 0
        // 是否在投屏过程中有用到遥控器反控
        val hasUsedRemoteControl =
            (remoteControlPlaybackResumeCount + remoteControlPlaybackPauseCount + remoteControlPlaybackFastForwardCount
                    + remoteControlPlaybackFastRewindCount + remoteControlPlaybackAddVolumeCount
                    + remoteControlPlaybackMinusVolumeCount) != 0
        // 没有使用过自适应则不上传埋点
        GLog.d(TAG, "trackOContentStatusAndStatistics hasUsedOContent=$hasUsedOContent hasUsedRemoteControl=$hasUsedRemoteControl")
        if (hasUsedOContent.not() && hasUsedRemoteControl.not()) {
            return
        }
        trackCast(O_CONTENT_STATISTICS) { track ->
            track.putProperty(O_CONTENT_CHANGE_TO_MIRROR_COUNT, changeToMirrorCount)
            track.putProperty(MANUAL_EXIT_O_CONTENT_COUNT, manualExitCount)
            track.putProperty(AUTO_EXIT_O_CONTENT_COUNT, autoExitCount)
            track.putProperty(MIRROR_AUTO_CHANGE_TO_O_CONTENT_COUNT, mirrorChangeToOContentCount)
            track.putProperty(MANUAL_CHANGE_TO_O_CONTENT_COUNT, manualChangeToOContentCount)
            track.putProperty(PLAYBACK_RESUME_COUNT, remoteControlPlaybackResumeCount)
            track.putProperty(PLAYBACK_PAUSE_COUNT, remoteControlPlaybackPauseCount)
            track.putProperty(FAST_FORWARD_COUNT, remoteControlPlaybackFastForwardCount)
            track.putProperty(FAST_REWIND_COUNT, remoteControlPlaybackFastRewindCount)
            track.putProperty(ADD_VOLUME_COUNT, remoteControlPlaybackAddVolumeCount)
            track.putProperty(MINUS_VOLUME_COUNT, remoteControlPlaybackMinusVolumeCount)
            track.save()
        }
        // 上传一次数据之后要将数据清空, 用于统计从现在开始的接下来的一天的数据
        asyncSetOContentTrackData()
    }

    @WorkerThread
    private fun trackCast(eventId: String, @WorkerThread func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
            event = eventId,
            type = TYPE_CAST,
            func = func
        )
    }

    /**
     * 重置自适应投屏的埋点数据（将SP中的数据都清空）
     */
    private fun asyncSetOContentTrackData() = TrackScope.launch {
        setSPInt(O_CONTENT_CHANGE_TO_MIRROR_COUNT_KEY, 0)
        setSPInt(MANUAL_EXIT_O_CONTENT_COUNT_KEY, 0)
        setSPInt(AUTO_EXIT_O_CONTENT_COUNT_KEY, 0)
        setSPInt(MIRROR_AUTO_CHANGE_TO_O_CONTENT_COUNT_KEY, 0)
        setSPInt(MANUAL_CHANGE_TO_O_CONTENT_COUNT_KEY, 0)
        setSPInt(O_CONTENT_REMOTE_CONTROL_PLAYBACK_RESUME_COUNT_KEY, 0)
        setSPInt(O_CONTENT_REMOTE_CONTROL_PLAYBACK_PAUSE_COUNT_KEY, 0)
        setSPInt(O_CONTENT_REMOTE_CONTROL_FAST_FORWARD_COUNT_KEY, 0)
        setSPInt(O_CONTENT_REMOTE_CONTROL_FAST_REWIND_COUNT_KEY, 0)
        setSPInt(O_CONTENT_REMOTE_CONTROL_ADD_VOLUME_COUNT_KEY, 0)
        setSPInt(O_CONTENT_REMOTE_CONTROL_MINUS_VOLUME_COUNT_KEY, 0)
    }

    /**
     * 异步增加自适应切换镜像的次数
     */
    @JvmStatic
    fun asyncAddOContentChangeToMirrorCount() =
        asyncAddOContentDataCount(O_CONTENT_CHANGE_TO_MIRROR_COUNT_KEY)

    /**
     * 异步增加手动退出私有协议自适应内容投屏次数
     */
    @JvmStatic
    fun asyncAddManualExitOContentCount() =
        asyncAddOContentDataCount(MANUAL_EXIT_O_CONTENT_COUNT_KEY)

    /**
     * 异步增加自动退出私有协议自适应内容投屏次数
     */
    @JvmStatic
    fun asyncAddAutoExitOContentCount() =
        asyncAddOContentDataCount(AUTO_EXIT_O_CONTENT_COUNT_KEY)

    /**
     * 异步增加从镜像投屏自动切换成私有协议自适应内容投屏次数
     */
    @JvmStatic
    fun asyncAddMirrorAutoChangeToOContentCount() =
        asyncAddOContentDataCount(MIRROR_AUTO_CHANGE_TO_O_CONTENT_COUNT_KEY)

    /**
     * 异步增加手动进入私有协议自适应内容投屏次数
     */
    @JvmStatic
    fun asyncAddManualChangeToOContentCount() =
        asyncAddOContentDataCount(MANUAL_CHANGE_TO_O_CONTENT_COUNT_KEY)

    /**
     * 异步增加自适应反控播放次数
     */
    @JvmStatic
    fun asyncAddOContentRemoteControlPlaybackResumeCount() =
        asyncAddOContentDataCount(O_CONTENT_REMOTE_CONTROL_PLAYBACK_RESUME_COUNT_KEY)

    /**
     * 异步增加自适应反控暂停次数
     */
    @JvmStatic
    fun asyncAddOContentRemoteControlPlaybackPauseCount() =
        asyncAddOContentDataCount(O_CONTENT_REMOTE_CONTROL_PLAYBACK_PAUSE_COUNT_KEY)

    /**
     * 异步增加自适应反控快进次数
     */
    @JvmStatic
    fun asyncAddOContentRemoteControlFastForwardCount() =
        asyncAddOContentDataCount(O_CONTENT_REMOTE_CONTROL_FAST_FORWARD_COUNT_KEY)

    /**
     * 异步增加自适应反控快退次数
     */
    @JvmStatic
    fun asyncAddOContentRemoteControlFastRewindCount() =
        asyncAddOContentDataCount(O_CONTENT_REMOTE_CONTROL_FAST_REWIND_COUNT_KEY)

    /**
     * 异步增加自适应反控加音量次数
     */
    @JvmStatic
    fun asyncAddOContentRemoteControlAddVolumeCount() =
        asyncAddOContentDataCount(O_CONTENT_REMOTE_CONTROL_ADD_VOLUME_COUNT_KEY)

    /**
     * 异步增加自适应反控减音量次数
     */
    @JvmStatic
    fun asyncAddOContentRemoteControlMinusVolumeCount() =
        asyncAddOContentDataCount(O_CONTENT_REMOTE_CONTROL_MINUS_VOLUME_COUNT_KEY)

    /**
     * 异步给自适应投屏的某些数据加计数
     */
    private fun asyncAddOContentDataCount(spKey: String) = TrackScope.launch {
        var lastCount = getSPInt(spKey)
        setSPInt(spKey, ++lastCount)
    }

    /**
     * 获取SP CAST_FILE_NAME 中的int值
     */
    private fun getSPInt(spKey: String): Int =
        SPUtils.getInt(ContextGetter.context, CAST_FILE_NAME, spKey, 0)

    /**
     * 设置SP CAST_FILE_NAME 中的int值
     */
    private fun setSPInt(spKey: String, value: Int) =
        SPUtils.setInt(ContextGetter.context, CAST_FILE_NAME, spKey, value)
}