/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : Listener.java
 ** Description : Relocation code
 ** Version     : 1.0
 ** Date        : 2020/08/27
 ** Author      : <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_LISTENER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery3D             2020/08/27  1.0         OPLUS_ARCH_LISTENER
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.cast.protocol.dlna.os12dlna;

import android.os.Bundle;

public class ProjectionListener {

    public static final int ERROR_NO_URI_PERMISSTION = 1;
    public static final int ERROR_WLAN_DISCONNECTED = 2;

    public void onBind() {

    }

    public void onUnbind() {

    }

    public void onConnect(Device deviceInfo) {

    }

    public void onDisconnect(Device deviceInfo) {

    }

    public void onStart() {

    }

    public void onPause() {

    }

    public void onCompletion() {

    }

    public void onStop() {

    }

    public void onPositionUpdate(int duration, int position) {

    }

    public void onSeekComplete(int percent) {

    }

    public void onError(int error) {

    }

    public void onInfo(Bundle bundle) {

    }

    public void onLoading() {

    }

    public void onPlayMediaTypeChange(int type) {

    }
}