/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ProjectionPlayerHelper.java
 ** Description : Relocation code
 ** Version     : 1.0
 ** Date        : 2020/08/27
 ** Author      : <PERSON><PERSON>uan@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_PROJECTIONPLAYERHELPER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery3D             2020/08/27  1.0         OPLUS_ARCH_PROJECTIONPLAYERHELPER
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.cast.protocol.dlna.os12dlna;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.oplus.gallery.framework.abilities.cast.protocol.dlna.BitmapInfo;
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.Source;
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.SourceConverter;
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.os12dlna.player.CompatDlnaPlayer;
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.os12dlna.player.DlnaPlayer;
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.os12dlna.player.OldDlnaPlayer;
import com.oplus.gallery.framework.abilities.cast.protocol.dlna.os12dlna.player.ProjectionPlayer;
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle;
import com.oplus.gallery.foundation.uikit.lifecycle.LifecycleListenerAdapter;
import com.oplus.gallery.foundation.util.version.AppVersionUtils;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.util.thread.ThreadUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.util.HashMap;

public class ProjectionPlayerHelper {

    private static final String TAG = "ProjectionPlayerHelper";

    private static final int MSG_INIT = 1;
    private static final int MSG_PERMISSION_RESULT = 2;
    private static final int MSG_RELEASE = 3;
    private static final int MSG_BIND_SERVICE = 4;
    private static final int MSG_ON_START = 5;
    private static final int MSG_ON_PAUSE = 6;
    private static final int MSG_ON_COMPLETION = 7;
    private static final int MSG_ON_STOP = 8;
    private static final int MSG_ON_SEEK_COMPLETE = 9;
    private static final int MSG_ON_ERROR = 10;
    private static final int MSG_ON_INFO = 11;
    private static final int MSG_ON_LOADING = 12;
    private static final int MSG_ON_PLAY_MEDIA_TYPE_CHANGE = 13;
    private static final int MSG_DEVICE_CONNECTED = 14;
    private static final int MSG_DEVICE_DISCONNECTED = 15;
    private static final int MSG_ON_POSITION_UPDATE = 16;
    private static final int MSG_ON_BIND = 17;
    private static final int MSG_ON_UNBIND = 18;

    private static final int MSG_SUB_PLAY_BITMAP = 1;

    private static final int LAST_PLAY_POSITION_DEFAULT = -1;

    private volatile static HashMap<Integer, ProjectionPlayerHelper> sInstanceMap = new HashMap<>();

    private final Context mContext;
    private final Handler mMainHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MSG_INIT:
                    innerInit((ProjectionListener) msg.obj);
                    break;
                case MSG_PERMISSION_RESULT:
                    if ((Boolean) msg.obj) {
                        innerStartDevicesListActivityForResult(mActivity, mRequestCode);
                    }
                    break;
                case MSG_RELEASE:
                    innerRelease();
                    break;
                case MSG_BIND_SERVICE:
                    bind();
                    break;
                case MSG_ON_START:
                    mIsVideoPlaying = true;
                    mListener.onStart();
                    break;
                case MSG_ON_PAUSE:
                    mIsVideoPlaying = false;
                    mListener.onPause();
                    break;
                case MSG_ON_COMPLETION:
                    mLastPlayPosition = LAST_PLAY_POSITION_DEFAULT;
                    mIsVideoPlaying = false;
                    mListener.onCompletion();
                    break;
                case MSG_ON_STOP:
                    mIsVideoPlaying = false;
                    mListener.onStop();
                    break;
                case MSG_ON_SEEK_COMPLETE:
                    mListener.onSeekComplete(msg.arg1);
                    break;
                case MSG_ON_ERROR:
                    mListener.onError(msg.arg1);
                    break;
                case MSG_ON_INFO:
                    mListener.onInfo((Bundle) msg.obj);
                    break;
                case MSG_ON_LOADING:
                    mListener.onLoading();
                    break;
                case MSG_ON_PLAY_MEDIA_TYPE_CHANGE:
                    if (!mIsResume) {
                        clearRecord();
                    }
                    mListener.onPlayMediaTypeChange(msg.arg1);
                    break;
                case MSG_DEVICE_CONNECTED:
                    mListener.onConnect((Device) msg.obj);
                    break;
                case MSG_DEVICE_DISCONNECTED:
                    mLastPlayPosition = LAST_PLAY_POSITION_DEFAULT;
                    mListener.onDisconnect((Device) msg.obj);
                    break;
                case MSG_ON_POSITION_UPDATE:
                    int duration = msg.arg1;
                    mLastPlayPosition = msg.arg2;
                    mListener.onPositionUpdate(duration, mLastPlayPosition);
                    break;
                case MSG_ON_BIND:
                    mListener.onBind();
                    break;
                case MSG_ON_UNBIND:
                    mListener.onUnbind();
                    break;
            }
        }
    };
    private final Handler mSubHandler;
    private final String mTempDirPath;
    private final ProjectionPlayer mPlayer;
    private final ProjectionListener mSafeListener = new ProjectionListener();
    private final SourceConverter mSourceConverter = new SourceConverter();
    private ProjectionListener mListener;
    private Source mLastSource;
    private Source mCurrentSource;
    private boolean mReplay = false;
    private BitmapInfo mCurrentBitmapInfo;
    private Activity mActivity;
    private int mRequestCode;
    private int mLastPlayPosition = LAST_PLAY_POSITION_DEFAULT;
    private boolean mIsResume = false;
    private boolean mIsVideoPlaying = false;

    public static ProjectionPlayerHelper getInstance(int activityHash) {
        GLog.d(TAG, "getInstance activityHash = " + activityHash);
        if (sInstanceMap.get(activityHash) == null) {
            synchronized (ProjectionPlayerHelper.class) {
                if (sInstanceMap.get(activityHash) == null) {
                    if (sInstanceMap.size() == 0) {
                        ActivityLifecycle.INSTANCE.addListener(new LifecycleListenerAdapter() {

                            @Override
                            public void onResumed(@NonNull Activity activity) {
                                int hash = activity.hashCode();
                                GLog.d(TAG, "onResumed, " + hash);
                                ProjectionPlayerHelper instance = sInstanceMap.get(hash);
                                if (instance != null) {
                                    instance.onResume();
                                }
                            }

                            @Override
                            public void onPaused(@NonNull Activity activity) {
                                int hash = activity.hashCode();
                                GLog.d(TAG, "onPaused, " + hash);
                                ProjectionPlayerHelper instance = sInstanceMap.get(hash);
                                if (instance != null) {
                                    instance.onPause();
                                }
                            }

                            @Override
                            public void onStopped(@NonNull Activity activity) {
                                int hash = activity.hashCode();
                                GLog.d(TAG, "onStopped, " + hash);
                                ProjectionPlayerHelper instance = sInstanceMap.get(hash);
                                if (instance != null) {
                                    instance.onStop();
                                }
                            }

                            @Override
                            public void onDestroyed(@NonNull Activity activity) {
                                int hash = activity.hashCode();
                                GLog.d(TAG, "onDestroyed, " + hash);
                                ProjectionPlayerHelper instance = sInstanceMap.get(hash);
                                if (instance != null) {
                                    instance.onDestroy();
                                    sInstanceMap.remove(hash);
                                }
                            }

                        });
                    }
                    ProjectionPlayerHelper instance = new ProjectionPlayerHelper(ContextGetter.INSTANCE.getContext());
                    sInstanceMap.put(activityHash, instance);
                }
            }
        }
        return sInstanceMap.get(activityHash);
    }

    private ProjectionPlayerHelper(Context context) {
        GLog.d(TAG, "ProjectionPlayHelper construct");
        mContext = context.getApplicationContext();
        mTempDirPath = mSourceConverter.getTempDirPath();
        mSubHandler = createSubThreadHandler();
        mPlayer = createPlayer();
    }

    private Handler createSubThreadHandler() {
        HandlerThread handlerThread = new HandlerThread(TAG);
        handlerThread.start();
        return new Handler(handlerThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case MSG_SUB_PLAY_BITMAP: {
                        if (isConnected()) {
                            BitmapInfo info = mCurrentBitmapInfo;
                            if (info != null) {
                                mCurrentSource = mSourceConverter.getBitmapMediaSource(info, file -> mPlayer.createBitmapFileUri(file));
                            }
                            innerPlaySource();
                        }
                        break;
                    }
                    default:
                        break;
                }
            }
        };
    }

    private ProjectionPlayer createPlayer() {
        ProjectionPlayer player = null;
        long versionCode = AppVersionUtils.getVersionCode(mContext, DlnaPlayer.SERVICE_NAME);
        if (versionCode >= DlnaPlayer.VERSION_CODE_NEW_DLNA) {
            player = new CompatDlnaPlayer(mContext);
        } else {
            player = new OldDlnaPlayer(mContext);
        }
        return player;
    }

    /**
     * must call {@link #release} to release
     *
     * @param listener All callback run on UI thread
     */
    public void init(final ProjectionListener listener) {
        if (ThreadUtils.isMainThread()) {
            innerInit(listener);
        } else {
            Message.obtain(mMainHandler, MSG_INIT, listener).sendToTarget();
        }
    }

    private void innerInit(ProjectionListener listener) {
        mListener = (listener != null) ? listener : mSafeListener;
        mPlayer.setListener(new ProxyListener());
        bind();
    }

    public void startDevicesListActivityForResult(Activity activity, int requestCode) {
        GLog.d(TAG, "startDevicesListActivityForResult, mPlayer=" + mPlayer);
        if (mPlayer.isBinded()) {
            mActivity = activity;
            mRequestCode = requestCode;
            mPlayer.setPermissionResultListener(new ProjectionPlayer.OnPermissionResultListener() {
                @Override
                public void onPermissionAllowed() {
                    onPermissionResult(true);
                }

                @Override
                public void onPermissionDisallowed() {
                    onPermissionResult(false);
                }

                @Override
                public void onPermissionCanceled() {
                    onPermissionResult(false);
                }
            });
        } else {
            GLog.e(TAG, "startDevicesListActivityForResult, isBind=false");
        }
    }

    public void onDevicesListActivityResult(int resultCode, Intent data) {
        if (mPlayer.isBinded()) {
            mPlayer.onDevicesListActivityResult(resultCode, data);
        }
    }

    private void onPermissionResult(boolean allowed) {
        GLog.d(TAG, "onPermissionResult:" + allowed);
        Message.obtain(mMainHandler, MSG_PERMISSION_RESULT, allowed).sendToTarget();
    }

    private void innerStartDevicesListActivityForResult(Activity activity, int requestCode) {
        mPlayer.startDevicesListActivityForResult(activity, requestCode);
    }

    public void onResume() {
        GLog.d(TAG, "onResume");
        mIsResume = true;
    }

    public void onPause() {
        GLog.d(TAG, "onPause");
        mIsResume = false;
    }

    public void onStop() {
        GLog.d(TAG, "onStop");
        mPlayer.setPermissionResultListener(null);
    }

    public void release() {
        GLog.d(TAG, "release");
        if (ThreadUtils.isMainThread()) {
            innerRelease();
        } else {
            Message.obtain(mMainHandler, MSG_RELEASE).sendToTarget();
        }
    }

    private void innerRelease() {
        mListener = mSafeListener;
        mSubHandler.removeCallbacksAndMessages(null);
        clearTempBitmapDir();
        mActivity = null;
        mCurrentSource = null;
        mCurrentBitmapInfo = null;
    }

    public void onDestroy() {
        GLog.d(TAG, "onDestroy");
        unbind();
        mSubHandler.getLooper().quit();
    }

    private void clearTempBitmapDir() {
        File dir = new File(mTempDirPath);
        if (dir.exists()) {
            File[] files = dir.listFiles();
            if ((files != null) && (files.length > 0)) {
                for (File file : files) {
                    if (file != null) {
                        file.delete();
                    }
                }
            }
        }
    }

    public void bind() {
        mPlayer.bind();
    }

    public void addAfterBindPendingOperations(Runnable op) {
        mPlayer.addAfterBindPendingOperations(op);
    }

    private void unbind() {
        mPlayer.unbind();
    }

    public boolean isBinded() {
        return mPlayer.isBinded();
    }

    public boolean isSupportImageFileMimeType(String type) {
        return mPlayer.isSupportImageFileMimeType(type);
    }

    private void clearRecord() {
        mLastSource = null;
    }

    public void playBitmap(BitmapInfo info) {
        if ((info == null) || !info.checkValid()) {
            return;
        }
        GLog.d(TAG, "playBitmap");
        mCurrentBitmapInfo = info;
        tryPlaySource();
    }

    public void playSource(Source source, boolean replay) {
        GLog.d(TAG, "playSource. replay=" + replay);
        if (!mPlayer.checkSourceValid(source)) {
            return;
        }
        mReplay = replay;
        mCurrentSource = source;
        mCurrentBitmapInfo = null;
        tryPlaySource();
    }

    public boolean isVideoPlaying() {
        return mIsVideoPlaying;
    }

    private void tryPlaySource() {
        if (mPlayer.isBinded()) {
            mSubHandler.removeMessages(MSG_SUB_PLAY_BITMAP);
            mSubHandler.sendEmptyMessage(MSG_SUB_PLAY_BITMAP);
        } else {
            if (ThreadUtils.isMainThread()) {
                bind();
            } else {
                Message.obtain(mMainHandler, MSG_BIND_SERVICE).sendToTarget();
            }
        }
    }

    private void innerPlaySource() {
        GLog.d(TAG, "innerPlaySource");
        final Source source = mCurrentSource;
        if (source == null) {
            return;
        }
        final Source lastSource = mLastSource;
        if (lastSource != null) {
            Uri lastUri = lastSource.getLocalSourceUri();
            String filePath = lastSource.getFilePath();
            if (((lastUri != null) && lastUri.equals(source.getLocalSourceUri()))
                    || (!TextUtils.isEmpty(filePath) && filePath.equals(source.getFilePath()))) {
                GLog.d(TAG, "innerPlaySource. ignore, replay=" + mReplay + ", playing=" + isVideoPlaying());
                boolean isComplete = mLastPlayPosition == LAST_PLAY_POSITION_DEFAULT;
                if (mReplay || isComplete) {
                    mPlayer.play(source);
                }
                mReplay = false;
                return;
            } else {
                mLastPlayPosition = LAST_PLAY_POSITION_DEFAULT;
            }
        }
        mLastSource = source;
        mPlayer.play(source);
    }

    public void resume() {
        mPlayer.resume();
    }

    public void pause() {
        mPlayer.pause();
    }

    public void stop() {
        mPlayer.stop();
    }

    public void seekTo(int position) {
        mLastPlayPosition = position;
        mPlayer.seekTo(position);
    }

    public void setVolume(int volume) {
        mPlayer.setVolume(volume);
    }

    /**
     * utit millisecond
     *
     * @return
     */
    public int getCurrentPosition() {
        int position = mPlayer.getCurrentPosition();
        mLastPlayPosition = position;
        return position;
    }

    public boolean isConnected() {
        return mPlayer.isConnected();
    }

    private class ProxyListener extends ProjectionListener {
        public void onBind() {
            GLog.d(TAG, "onBind");
            Message.obtain(mMainHandler, MSG_ON_BIND).sendToTarget();
        }

        public void onUnbind() {
            GLog.d(TAG, "onUnbind");
            Message.obtain(mMainHandler, MSG_ON_UNBIND).sendToTarget();
        }

        public void onConnect(Device deviceInfo) {
            GLog.d(TAG, "onConnect");
            Message.obtain(mMainHandler, MSG_DEVICE_CONNECTED, deviceInfo).sendToTarget();
        }

        public void onDisconnect(Device deviceInfo) {
            GLog.d(TAG, "onDisconnect");
            clearRecord();
            Message.obtain(mMainHandler, MSG_DEVICE_DISCONNECTED, deviceInfo).sendToTarget();
        }

        public void onStart() {
            GLog.d(TAG, "onStart");
            Message.obtain(mMainHandler, MSG_ON_START).sendToTarget();
        }

        public void onPause() {
            GLog.d(TAG, "onPause");
            Message.obtain(mMainHandler, MSG_ON_PAUSE).sendToTarget();
        }

        public void onCompletion() {
            GLog.d(TAG, "onCompletion");
            Message.obtain(mMainHandler, MSG_ON_COMPLETION).sendToTarget();
        }

        public void onStop() {
            GLog.d(TAG, "onStop");
            Message.obtain(mMainHandler, MSG_ON_STOP).sendToTarget();
        }

        public void onPositionUpdate(int duration, int position) {
            GLog.d(TAG, "onPositionUpdate duration:" + duration + " position:" + position);
            Message message = Message.obtain(mMainHandler, MSG_ON_POSITION_UPDATE);
            message.arg1 = duration;
            message.arg2 = position;
            message.sendToTarget();
        }

        public void onSeekComplete(int percent) {
            GLog.d(TAG, "onSeekComplete percent:" + percent);
            Message.obtain(mMainHandler, MSG_ON_SEEK_COMPLETE, percent).sendToTarget();
        }

        public void onError(int error) {
            boolean isConnected = isConnected();
            GLog.d(TAG, "onError error:" + error + " isConnected:" + isConnected);
            if (isConnected) {
                Message message = Message.obtain(mMainHandler, MSG_ON_ERROR);
                message.arg1 = error;
                message.sendToTarget();
            }
        }

        public void onInfo(Bundle bundle) {
            GLog.d(TAG, "onInfo");
            Message.obtain(mMainHandler, MSG_ON_INFO, bundle).sendToTarget();
        }

        public void onLoading() {
            GLog.d(TAG, "onLoading");
            Message.obtain(mMainHandler, MSG_ON_LOADING).sendToTarget();
        }

        public void onPlayMediaTypeChange(int type) {
            GLog.d(TAG, "onPlayMediaTypeChange type:" + type);
            Message message = Message.obtain(mMainHandler, MSG_ON_PLAY_MEDIA_TYPE_CHANGE);
            message.arg1 = type;
            message.sendToTarget();
        }
    }
}