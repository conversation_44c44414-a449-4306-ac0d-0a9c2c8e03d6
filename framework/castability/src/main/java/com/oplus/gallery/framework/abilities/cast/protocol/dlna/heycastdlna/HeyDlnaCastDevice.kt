/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : HeyDlnaCastDevice.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/04/18
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/04/18      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.cast.protocol.dlna.heycastdlna

import com.oplus.cast.service.sdk.config.CastDevice
import com.oplus.gallery.framework.abilities.cast.CastType
import com.oplus.gallery.framework.abilities.cast.ICastDevice

/**
 * 投屏设备信息类。
 *
 * 使用的协议为：os13及以后版本的DLNA协议
 */
internal class HeyDlnaCastDevice(var device: CastDevice) : ICastDevice {
    override fun getCastType(): CastType = CastType.DLNA

    override fun getName(): String = device.deviceName

    override fun getUniqueKey(): String = device.deviceName + device.deviceIp + CastType.DLNA

    override fun isLastConnected(): Boolean = false
}