/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DefaultConfigStrategyTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.caching.config

import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.TYPE_THUMBNAIL
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test

class DefaultConfigStrategyTest {
    private val strategy = ConfigStrategy.default

    @Ignore
    @Test
    fun getRealStorageQuality() {
        val optionsLow = CacheOptions(inThumbnailType = TYPE_THUMBNAIL, inStorageQuality = StorageQuality.LOW_QUALITY)
        Assert.assertEquals(StorageQuality.LOW_QUALITY, strategy.getRealStorageQuality(optionsLow))
        val optionsHigh = CacheOptions(inThumbnailType = TYPE_THUMBNAIL, inStorageQuality = StorageQuality.HIGH_QUALITY)
        Assert.assertEquals(StorageQuality.HIGH_QUALITY, strategy.getRealStorageQuality(optionsHigh))
        val optionsLossLess = CacheOptions(inThumbnailType = TYPE_THUMBNAIL, inStorageQuality = StorageQuality.LOSSLESS)
        Assert.assertEquals(StorageQuality.LOSSLESS, strategy.getRealStorageQuality(optionsLossLess))
        val optionsNotCare = CacheOptions(inThumbnailType = TYPE_THUMBNAIL, inStorageQuality = StorageQuality.NOT_CARE)
        Assert.assertEquals(StorageQuality.HIGH_QUALITY, strategy.getRealStorageQuality(optionsNotCare))
    }
}