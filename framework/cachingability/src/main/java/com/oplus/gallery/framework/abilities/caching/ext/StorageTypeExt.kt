/*******************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - StorageTypeExt.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 **      <author>            <date>   <version >        <desc>
 ** ------------------------------------------------------------------------------
 **     zhangwenming       2022/05/10    1.0                create
 ******************************************************************************/

package com.oplus.gallery.framework.abilities.caching.ext

import com.oplus.gallery.framework.abilities.caching.StorageType

/**
 * 判断是否使用磁盘缓存Cache
 * @receiver StorageType
 * @return Boolean
 */
internal fun StorageType.diskCache(): Boolean = (this == StorageType.MEMORY_AND_DISK || this == StorageType.DISK_ONLY)

/**
 * 判断是否使用内存缓存
 * @receiver StorageType
 * @return Boolean
 */
internal fun StorageType.memoryCache(): Boolean = (this == StorageType.MEMORY_AND_DISK || this == StorageType.MEMORY_ONLY)