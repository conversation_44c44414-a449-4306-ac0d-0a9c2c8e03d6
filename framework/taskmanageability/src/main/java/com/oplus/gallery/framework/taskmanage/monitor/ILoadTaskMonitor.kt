/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ILoadTaskMonitor.kt
 ** Description:加载任务的监控器
 ** Version: 1.0
 ** Date: 2023/6/27
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/27      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.taskmanage.monitor

import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import java.util.concurrent.PriorityBlockingQueue

/**
 * 加载任务的监控
 */
internal interface ILoadTaskMonitor {

    /**
     * 样式:子类指定
     */
    val iMonitorStyle: IMonitorStyle

    /**
     * 显示监控“面板”
     *
     * @param completedTasks 已运行完成的任务
     * @param runningTasks
     * @param canceledTasks
     * @param preparedTasks
     */
    fun showMonitorPanel(
        completedTasks: MutableList<AbsLoadTask<*>>,
        runningTasks: MutableList<AbsLoadTask<*>>,
        canceledTasks: MutableList<AbsLoadTask<*>>,
        preparedTasks: PriorityBlockingQueue<AbsLoadTask<*>>
    )
}