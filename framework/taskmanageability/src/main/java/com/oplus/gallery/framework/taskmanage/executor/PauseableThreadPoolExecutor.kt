/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PauseableThreadPoolExecutor.kt
 ** Description: 支持暂停、恢复的线程池
 ** Version: 1.0
 ** Date: 2023/6/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/6/16      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.taskmanage.executor

import com.oplus.gallery.foundation.util.debug.GTrace
import java.util.concurrent.BlockingQueue
import java.util.concurrent.RejectedExecutionHandler
import java.util.concurrent.ThreadFactory
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.Condition
import java.util.concurrent.locks.Lock
import java.util.concurrent.locks.ReentrantLock

/**
 * 支持暂停、恢复的线程池
 *
 * @param corePoolSize 核心线程数
 * @param maxPoolSize 最大线程数
 * @param keepAliveTime 线程没有任务执行时的保活时间
 * @param timeUnit 时间单位
 * @param blockingQueue 阻塞队列
 * @param threadFactory 创建线程的工厂
 * @param rejectedExecutionHandler 拒绝策略
 */
@Suppress("LongParameterList")
internal class PauseableThreadPoolExecutor(
    corePoolSize: Int,
    maxPoolSize: Int,
    keepAliveTime: Long,
    timeUnit: TimeUnit,
    blockingQueue: BlockingQueue<out Runnable>,
    threadFactory: ThreadFactory,
    rejectedExecutionHandler: RejectedExecutionHandler
) : ThreadPoolExecutor(
    corePoolSize,
    maxPoolSize,
    keepAliveTime,
    timeUnit,
    blockingQueue as BlockingQueue<Runnable>,
    threadFactory,
    rejectedExecutionHandler
) {

    constructor(
        corePoolSize: Int,
        maxPoolSize: Int,
        keepAliveTime: Long,
        timeUnit: TimeUnit,
        blockingQueue: BlockingQueue<out Runnable>,
        threadFactory: ThreadFactory,
    ) : this(corePoolSize, maxPoolSize, keepAliveTime, timeUnit, blockingQueue, threadFactory,
        RejectedExecutionHandler { _, _ -> })

    /**
     * 线程池是否处于暂停状态
     */
    private var isPaused: Boolean = false

    private var lock: Lock = ReentrantLock()

    private var pauseCondition: Condition = lock.newCondition()

    /**
     * 暂停线程池
     */
    fun pause() {
        lock.lock()
        try {
            isPaused = true
        } finally {
            lock.unlock()
        }
    }

    /**
     * 恢复线程池运行
     */
    fun resume() {
        lock.lock()
        try {
            isPaused = false
        } finally {
            lock.unlock()
        }
    }

    override fun beforeExecute(t: Thread?, r: Runnable?) {
        GTrace.traceBegin("PauseableThread.${t?.id}.${r?.javaClass?.simpleName}")
        if (isPaused) {
            lock.lock()
            try {
                pauseCondition.await()
            } finally {
                lock.unlock()
            }
            return
        }
    }

    override fun afterExecute(r: Runnable?, t: Throwable?) {
        super.afterExecute(r, t)
        GTrace.traceEnd()
    }
}