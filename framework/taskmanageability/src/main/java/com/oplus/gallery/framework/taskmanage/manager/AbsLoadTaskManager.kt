/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsLoadTaskManager.kt
 ** Description:加载任务的管理器
 ** Version: 1.0
 ** Date: 2023/6/27
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/27      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.taskmanage.manager

import androidx.lifecycle.Lifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask.Companion.CONTENT_CHANGE_ENTRANCE
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskEntranceConst.Timeline.SWITCH_SLIDING_WINDOW
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskType
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst.TASKLOAD_DEBUG
import com.oplus.gallery.framework.taskmanage.dispatcher.AbsLoadTaskDispatcher
import com.oplus.gallery.framework.taskmanage.monitor.ILoadTaskMonitor
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.PriorityQueue
import java.util.concurrent.CopyOnWriteArraySet

/**
 * 加载任务的管理器
 */
internal abstract class AbsLoadTaskManager {

    /**
     * 管理器名称
     */
    abstract val name: String

    /**
     * 该管理器管理的任务类型
     */
    abstract val type: TaskType

    /**
     * 任务调度器
     */
    abstract val taskDispatcher: AbsLoadTaskDispatcher

    /**
     * 任务监控器
     */
    abstract val taskMonitor: ILoadTaskMonitor


    /**
     * 所管理的TaskOwner列表
     */
    private val taskOwners: CopyOnWriteArraySet<ILoadTaskOwner> = CopyOnWriteArraySet()

    /**
     * 注册一个数据加载业务对象，交到管理器管理
     *
     * @param taskOwner 数据业务owner
     */
    fun register(taskOwner: ILoadTaskOwner) {
        if (taskOwners.contains(taskOwner)) {
            GLog.w(TAG, LogFlag.DL, "taskOwner:$taskOwner already registered")
            return
        }
        taskOwners.add(taskOwner)
        GLog.d(TAG, LogFlag.DL, "register taskOwner:${taskOwner.id}, after taskOwners:$taskOwners")
    }

    fun unregister(taskOwner: ILoadTaskOwner) {
        // 清理掉对TaskOwner的引用，避免内存泄漏；
        taskOwners.remove(taskOwner)
        taskDispatcher.releaseExecutor(taskOwner)
        GLog.d(TAG, LogFlag.DL, "unregister taskOwner:${taskOwner.id}, after taskOwners:$taskOwners")
    }

    /**
     * 提交一个任务到任务中心
     *
     * @param task 数据任务对象
     * @return 返回提交结果 ture 成功 false失败
     */
    fun submit(task: AbsLoadTask<*>): Boolean {
        val taskOwner = task.taskOwner
        if (hasTaskOwnerRegister(taskOwner).not()) {
            GLog.d(TAG, LogFlag.DL) { "submit: hasTaskOwnerRegister.not ${taskOwner.id} ${taskOwner.lifeCycleEvent} $taskOwner ${task.simpleMsg()}" }
            return false
        }
        if (isRepeatTask(task)) {
            GLog.d(TAG, LogFlag.DL) { "submit: isRepeatTask ${taskOwner.id} ${taskOwner.lifeCycleEvent}  $taskOwner ${task.simpleMsg()}" }
            return false
        }
        if (shouldDiscardTask(task, taskOwner)) {
            if (GProperty.DEBUG_ABS_LOAD_TASK_MANAGER) {
                GLog.d(TAG, LogFlag.DL) { "submit: shouldDiscardTask ${taskOwner.id} ${taskOwner.lifeCycleEvent} $taskOwner  ${task.simpleMsg()}" }
            }
            return false
        }

        // 延迟200ms再提交，以保证它不会覆盖掉优先级比它更高的任务
        if (isNeedToDelaySubmitTask(taskOwner)) {
            AppScope.launch {
                if (GProperty.DEBUG_ABS_LOAD_TASK_MANAGER) {
                    GLog.d(TAG, LogFlag.DL, "submit InBackground, delayTime:$DELAY_SUBMIT_TASK_TIME,task:${task.simpleMsg()}")
                }
                delay(DELAY_SUBMIT_TASK_TIME)
                taskDispatcher.addTask(task)
            }
        } else {
            taskDispatcher.addTask(task)
        }
        return true
    }

    private fun hasTaskOwnerRegister(taskOwner: ILoadTaskOwner): Boolean {
        if (taskOwners.contains(taskOwner).not()) {
            GLog.w(TAG, LogFlag.DL, "submit task, but it's taskOwner:${taskOwner.id} has not been registered, current taskOwners:$taskOwners")
            return false
        }
        return true
    }

    private fun shouldDiscardTask(task: AbsLoadTask<*>, taskOwner: ILoadTaskOwner): Boolean {
        /**
         * 若是在后台提交的任务，则需要检查如下条件
         * 1.任务是允许在后台运行
         * 2.任务是有联动需求的
         * 3.在后台触发任务提交的入口方式是：内容变更(此条件暂不开启,可能过于严格）
         */
        if (taskOwner.isInBackground()) {
            if (isAllowExecuteTaskWhenOnPause(taskOwner, task)) {
                return false
            }
            return if (taskOwner.taskConfigStrategy.isAllowBackgroundRefresh
                && (isAtLeastOneLinkageInForeground(taskOwner) || hasSameIdOwnerInForeground(taskOwner))
            ) {
                false
            } else {
                GLog.w(
                    TAG, LogFlag.DL, "shouldDiscardTask taskOwner:${taskOwner.id}, lifeCycle:${taskOwner.lifeCycleEvent}, " +
                            "task:${task.simpleMsg()}, because it's not meet the condition to submit and run in background"
                )
                true
            }
        } else if (taskOwner.isInForeground()) {
            return false
        } else if (taskOwner.isOnStart()) {
            /**
             * 特定场景优化代码：冷启动后立即从时间轴进入大图（不切换到图集TAB和发现TAB页），然后执行收藏、删除、滑动等操作。
             * 预加载过程中，如果是图集TAB/发现页TAB，那么当内容变更时，把提交的任务丢弃掉，可以提升大图编辑删除操作的性能.
             * 图集TAB、发现TAB并不是时间轴一样需要与大图联动的，并且回去时可以恢复刷新。
             * 因此，此段判定代码主要是进一步优化的作用，其他情况一律返回false即可。
             */
            if ((task.entrance == CONTENT_CHANGE_ENTRANCE)
                && ((taskOwner.id == TaskOwnerConst.ALBUM_SET_TAB)
                        || (taskOwner.id == TaskOwnerConst.EXPLORE_TAB)) && hasPhotoInForeground()
            ) {
                if (GProperty.DEBUG_ABS_LOAD_TASK_MANAGER) {
                    GLog.w(
                        TAG, LogFlag.DL, "shouldDiscardTask taskOwner:${taskOwner.id}, lifeCycle:${taskOwner.lifeCycleEvent}, " +
                                "task:${task.simpleMsg()}, because AlbumSetTab/ExploreTab preload & CONTENT_CHANGE_ENTRANCE"
                    )
                }
                return true
            }
        }
        return false
    }


    /**
     * 需要延迟提交的场景：
     * 1.当前任务生命周期处于ON_PAUSE或者ON_STOP状态，在后台提交的
     * 2.当前任务生命周期不是ON_PAUSE或者ON_STOP状态，不是大图任务且大图在前台
     * 不需要延迟提交的场景：
     * 1.从大图进入分享，由于分享页面是dialogFragment,所以进入分享时,大图的fragment生命周期不会变一直都是onResume状态,导致分享的加载任务被延迟执行
     *   此时taskOwner是ShareViewModel,hasPhotoInForeground()=true
     * @param taskOwner 提交的任务
     */
    private fun isNeedToDelaySubmitTask(taskOwner: ILoadTaskOwner): Boolean {
        return taskOwner.isInBackground() ||
            ((taskOwner.id != TaskOwnerConst.SHARE) && (taskOwner.id != TaskOwnerConst.PHOTO) && hasPhotoInForeground())
    }

    /**
     * 有大图相关的TaskOwner在前台
     */
    private fun hasPhotoInForeground(): Boolean {
        taskOwners.forEach {
            if ((it.id == TaskOwnerConst.PHOTO) && it.isInForeground()) return true
        }
        return false
    }

    /**
     * 至少有一个联动的TaskOwner在前台
     */
    private fun isAtLeastOneLinkageInForeground(taskOwner: ILoadTaskOwner): Boolean {
        taskOwner.linkages?.forEach {
            val linkageTaskOwners = findOwnersById(it)
            // 只要有一个联动的taskOwner实例在前台，那么都允许其在后台联动
            linkageTaskOwners.forEach { taskOwner ->
                if (taskOwner.isInForeground()) return true
            }
        }
        return false
    }

    /**
     * 有一个相同ID的TaskOwner在前台
     */
    private fun hasSameIdOwnerInForeground(taskOwner: ILoadTaskOwner): Boolean {
        taskOwners.forEach {
            if ((taskOwner.id == it.id) && (it.isInForeground())) return true
        }
        return false
    }

    private fun isRepeatTask(task: AbsLoadTask<*>): Boolean {
        /* Marked by limao:该条件存在低概率的时序问题，先屏蔽掉，后续看是否有更好的方式判断
        val taskOwner = task.taskOwner
        if (preparedTasks[taskOwner]?.contains(task) == true) {
            return true
        }
        if (runningTasks[taskOwner]?.contains(task) == true) {
            return true
        }*/
        return false
    }

    /**
     * 批量提交任务到任务中心
     *
     * @param tasks 任务优先级队列
     */
    fun submitBatch(tasks: PriorityQueue<AbsLoadTask<*>>) {
        tasks.forEach {
            submit(it)
        }
    }

    /**
     * 申请取消一个任务
     *
     * @param task
     */
    fun askForCancel(task: AbsLoadTask<*>) {
        if (hasTaskOwnerRegister(task.taskOwner).not()) return
        taskDispatcher.askForCancel(task.taskOwner)
    }

    fun pause() {
        taskDispatcher.pause()
    }

    fun resume() {
        taskDispatcher.resume()
    }

    fun switchToBackground(taskOwner: ILoadTaskOwner) {
        if (hasTaskOwnerRegister(taskOwner).not()) return
        GLog.d(
            TAG, LogFlag.DL, "switchToBackground taskOwner:${taskOwner.id}, " +
                    "isChild:${taskOwner.isChild()}, isContainer:${isContainer(taskOwner)}"
        )
        // 如果container切换到后台，那么除了本身以外，所有的child owner的任务都需要切换到后台
        if (isContainer(taskOwner)) {
            askForCancel(taskOwner)
            // taskDispatcher.adjustTaskOwnerPriority(taskOwner, AbsLoadTask.BG_LOWEST_PRIORITY)
            val children = findChildOwners(taskOwner)
            children?.forEach {
                askForCancel(it)
                // taskDispatcher.adjustTaskOwnerPriority(it, AbsLoadTask.BG_LOWEST_PRIORITY)
            }
        } else {
            if (isAllowExecuteTaskWhenOnPause(taskOwner)) {
                return
            }
            askForCancel(taskOwner)
            // taskDispatcher.adjustTaskOwnerPriority(it, AbsLoadTask.BG_LOWEST_PRIORITY)
        }
    }

    /**
     * 特殊场景处理：是否允许onPause状态提交的任务不取消、不拦截，继续执行
     */
    private fun isAllowExecuteTaskWhenOnPause(taskOwner: ILoadTaskOwner, task: AbsLoadTask<*>? = null): Boolean {
        /**
         * 针对相机进大图的Patch，相机锁屏情况下进入大图，有时候启动大图后，大图的生命周期还是在onPause状态，导致数据加载任务被忽略执行。
         * 对于此场景：相机进大图生命周期有时是OnResume->onPause->onResume，原因是会启动透明activity。
         * Fix BugId:6149835
         */
        if ((taskOwner.id == TaskOwnerConst.PHOTO) && (taskOwner.lifeCycleEvent == Lifecycle.Event.ON_PAUSE)) {
            GLog.w(TAG, LogFlag.DL, "isAllowExecuteTaskWhenOnPause TaskOwner:Photo is ON_PAUSE state")
            return true
        }

        /**
         * 针对弹出的选择人宠图集面板后创建合照后，触发reload-submittask时，合照列表页生命周期还处于onPause状态，导致数据加载任务被忽略执行。
         */
        if ((taskOwner.id == TaskOwnerConst.PERSON_PET_GROUP_ALBUM_SET) && (taskOwner.lifeCycleEvent == Lifecycle.Event.ON_PAUSE)) {
            GLog.w(TAG, LogFlag.DL) { "isAllowExecuteTaskWhenOnPause TaskOwner:person pet group set is ON_PAUSE state" }
            return true
        }

        /**
         * 针对雪鹰上设置页面以浮窗展示，底部页面可以看到一部分的情况，需要确保任务可以执行
         * 这样才能保证在设置影响了页面后及时刷新数据，比如正倒序
         * fix bugId:6220039
         */
        if ((taskOwner.id == TaskOwnerConst.TIMELINE_TAB) && (taskOwner.lifeCycleEvent == Lifecycle.Event.ON_PAUSE)) {
            taskOwners.forEach {
                if ((it.id == TaskOwnerConst.SETTING) && (it.isInForeground())) {
                    GLog.w(TAG, LogFlag.DL, "isAllowExecuteTaskWhenOnPause TaskOwner:TimelineTab is ON_PAUSE and TaskOwner:Setting is ON_RESUME state")
                    return true
                }
            }
            task?.apply {
                AbsLoadTask
                if (entrance == SWITCH_SLIDING_WINDOW) {
                    GLog.w(TAG, LogFlag.DL, "isAllowExecuteTaskWhenOnPause TaskOwner:TimelineTab entrance is switchSlidingWindow")
                    return true
                }
            }
        }
        /**
         * 针对相册首次拉起其他应用，应用权限弹窗覆盖在相册上面，触发亮/暗色模式切换重建Activity，侧边栏item无法加载场景
         * fix bugId:7928815
         */
        if ((taskOwner.id == TaskOwnerConst.SIDE_PANEL_SET) && (taskOwner.lifeCycleEvent == Lifecycle.Event.ON_PAUSE)) {
            task?.apply {
                GLog.w(TAG, LogFlag.DL, "isAllowExecuteTaskWhenOnPause TaskOwner:sidePaneSet is ON_PAUSE state")
                return true
            }
        }
        return false
    }

    fun switchToForeground(taskOwner: ILoadTaskOwner) {
        if (hasTaskOwnerRegister(taskOwner).not()) return
        GLog.d(TAG, LogFlag.DL) {
            "switchToForeground taskOwner:${taskOwner.id},isChild:${taskOwner.isChild()}, isContainer:${isContainer(taskOwner)}"
        }
        if (isContainer(taskOwner)) {
            if (TASKLOAD_DEBUG) {
                GLog.d(TAG, LogFlag.DL, "switchToForeground container:${taskOwner.id}")
            }
        } else if (taskOwner.isChild()) {
            adjustOwnerTasksPriority(taskOwner, AbsLoadTask.FG_HIGHEST_PRIORITY)

            val children: MutableList<ILoadTaskOwner> = mutableListOf()
            taskOwner.container?.let {
                val containerOwners = findOwnersById(it)
                containerOwners.forEach { containerOwner ->
                    findChildOwners(containerOwner)?.let { list -> children.addAll(list) }
                }
            }
            // affinity TaskOwner处理
            taskOwner.affinities?.forEach {
                val affinityOwners = findOwnersById(it)
                affinityOwners.forEach { affinityOwner ->
                    // 若还未切入后台，那么提前调整其任务优先级到后台最高优先级
                    if (affinityOwner.isInBackground().not()) {
                        adjustOwnerTasksPriority(affinityOwner, AbsLoadTask.BG_HIGHEST_PRIORITY)
                    }
                    children.remove(affinityOwner)
                }
            }
            children.remove(taskOwner)

            // container中其他child的处理
            children.forEach {
                // 若还未切入后台，那么提前取消它的任务，不必等到它切入后台时才做，提升前台页面的任务执行效率
                if (it.isInBackground().not()) {
                    askForCancel(it)
                }
            }
        } else {
            adjustOwnerTasksPriority(taskOwner, AbsLoadTask.FG_HIGHEST_PRIORITY)
        }
    }

    private fun adjustOwnerTasksPriority(taskOwner: ILoadTaskOwner, newPriority: Int) {
        GLog.d(TAG, LogFlag.DL) { "adjustOwnerTasksPriority taskOwner:${taskOwner.id}, newPriority:$newPriority" }
        taskDispatcher.adjustTaskOwnerPriority(taskOwner, newPriority)
    }

    fun askForCancel(taskOwner: ILoadTaskOwner) {
        if (hasTaskOwnerRegister(taskOwner).not()) return
        GLog.d(TAG, LogFlag.DL, "askForCancel taskOwner:$taskOwner")
        taskDispatcher.askForCancel(taskOwner)
    }

    private fun findOwnersById(id: String): MutableList<ILoadTaskOwner> {
        val list = mutableListOf<ILoadTaskOwner>()
        taskOwners.forEach {
            if (it.id == id) {
                list.add(it)
            }
        }
        return list
    }

    private fun findChildOwners(taskOwner: ILoadTaskOwner): MutableList<ILoadTaskOwner>? {
        if (taskOwner.isChild()) return null
        val list = mutableListOf<ILoadTaskOwner>()
        taskOwners.forEach {
            if (it.container == taskOwner.id) {
                list.add(it)
            }
        }
        return list
    }

    private fun isContainer(taskOwner: ILoadTaskOwner): Boolean {
        return findChildOwners(taskOwner).isNullOrEmpty().not()
    }

    companion object {
        private const val TAG = "${LoadTaskConst.TAG}AbsLoadTaskManager"


        /**
         * 默认延迟提交任务的时间
         */
        const val DELAY_SUBMIT_TASK_TIME: Long = 200L
    }
}