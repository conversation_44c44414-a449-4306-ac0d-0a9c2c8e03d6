/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DataLoadTaskDispatcher.kt
 ** Description: 数据库加载任务调度器
 ** Version: 1.0
 ** Date: 2023/6/27
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/27      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.taskmanage.dispatcher

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst
import com.oplus.gallery.framework.taskmanage.executor.PauseableThreadPoolExecutor
import com.oplus.gallery.framework.taskmanage.monitor.ILoadTaskMonitor
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_1
import java.util.concurrent.PriorityBlockingQueue
import java.util.concurrent.TimeUnit

/**
 * 数据库加载任务调度器
 */
internal class DataLoadTaskDispatcher(taskMonitor: ILoadTaskMonitor) : AbsLoadTaskDispatcher(taskMonitor) {

    override val runMode: RunMode = RunMode.SINGLE_THREAD

    override fun getExecutor(taskOwner: ILoadTaskOwner): PauseableThreadPoolExecutor {
        val executorConfig = taskOwner.taskConfigStrategy.getExecutorConfig()
        return if (executorConfig.hasPrivateExecutor) {
            executorMap.computeIfAbsent(taskOwner.getExecutorKey()) {
                createExecutor(executorConfig.threadCount, taskOwner.getExecutorKey())
            }
        } else {
            executorMap.computeIfAbsent(DEFAULT_EXECUTOR_KEY) {
                val threadCount = when (runMode) {
                    RunMode.SINGLE_THREAD -> NUMBER_1
                    RunMode.MULTI_THREAD -> Runtime.getRuntime().availableProcessors()
                }
                createExecutor(threadCount, DEFAULT_EXECUTOR_KEY)
            }
        }
    }

    private fun createExecutor(threadCount: Int, threadName: String): PauseableThreadPoolExecutor {
        GLog.d(TAG, "createExecutor: threadCount = $threadCount, threadName = $threadName")
        val blockingQueue = PriorityBlockingQueue<AbsLoadTask<*>>()
        return PauseableThreadPoolExecutor(
            threadCount,
            threadCount,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            blockingQueue
        ) { runnable ->
            val thread = Thread(runnable)
            thread.name = threadName
            thread
        }
    }

    companion object {
        private const val KEEP_ALIVE_TIME = 30L
        private const val TAG = "${LoadTaskConst.TAG}DataLoadTaskDispatcher"
        private const val DEFAULT_EXECUTOR_KEY = "DataLoadThread"
    }
}