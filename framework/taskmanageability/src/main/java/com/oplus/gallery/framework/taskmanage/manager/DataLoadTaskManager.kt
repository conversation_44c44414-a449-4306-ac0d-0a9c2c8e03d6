/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DataLoadTaskCenter.kt
 ** Description:数据加载任务管理器
 ** Version: 1.0
 ** Date: 2023/6/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/16      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.taskmanage.manager

import com.oplus.gallery.framework.abilities.taskmanage.task.TaskType
import com.oplus.gallery.framework.taskmanage.dispatcher.AbsLoadTaskDispatcher
import com.oplus.gallery.framework.taskmanage.dispatcher.DataLoadTaskDispatcher
import com.oplus.gallery.framework.taskmanage.monitor.ILoadTaskMonitor
import com.oplus.gallery.framework.taskmanage.monitor.LoadTaskMonitor

/**
 * 数据加载任务中心
 */
internal object DataLoadTaskManager : AbsLoadTaskManager() {

    override val name: String = "DataLoadTaskManager"

    override val type: TaskType = TaskType.DB

    override var taskMonitor: ILoadTaskMonitor = LoadTaskMonitor(name)

    override var taskDispatcher: AbsLoadTaskDispatcher = DataLoadTaskDispatcher(taskMonitor)
}