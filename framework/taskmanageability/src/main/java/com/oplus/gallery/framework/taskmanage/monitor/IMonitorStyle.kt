/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IMonitorStyle.kt
 ** Description:定制监控器样式的接口
 ** Version: 1.0
 ** Date: 2023/6/27
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/27      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.taskmanage.monitor

import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import java.util.concurrent.PriorityBlockingQueue

/**
 * 定制监控器样式的接口
 */
internal interface IMonitorStyle {

    /**
     * 对信息进行指定样式化处理
     */
    fun format(
        completedTasks: MutableList<AbsLoadTask<*>>,
        runningTasks: MutableList<AbsLoadTask<*>>,
        canceledTasks: MutableList<AbsLoadTask<*>>,
        preparedTasks: PriorityBlockingQueue<AbsLoadTask<*>>
    ): StringBuffer
}