/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LogcatMonitorStyle.kt
 ** Description:Logcat输出的monitor panel样式
 ** Version: 1.0
 ** Date: 2023/6/27
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/27      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.taskmanage.monitor

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst.MONITOR_MODE
import java.util.concurrent.PriorityBlockingQueue

/**
 * Logcat输出的monitor panel样式
 *
 * @property monitorTitle 标题
 */
internal class LogcatMonitorStyle(private val monitorTitle: String) : IMonitorStyle {

    override fun format(
        completedTasks: MutableList<AbsLoadTask<*>>,
        runningTasks: MutableList<AbsLoadTask<*>>,
        canceledTasks: MutableList<AbsLoadTask<*>>,
        preparedTasks: PriorityBlockingQueue<AbsLoadTask<*>>
    ): StringBuffer {
        GLog.e(
            TAG,
            "|=========================================================$monitorTitle Monitor Start=======================" +
                    "============================================================|"
        )
        GLog.e(
            TAG,
            "|===========================================================================================================" +
                    "===================================================================|"
        )
        when (MONITOR_MODE) {
            LoadTaskConst.MonitorMode.PREPARED.ordinal -> printQueueTasks(LoadTaskConst.MonitorTag.PREPARED.tag, preparedTasks)
            LoadTaskConst.MonitorMode.RUNNING.ordinal -> printTasks(LoadTaskConst.MonitorTag.RUNNING.tag, runningTasks)
            LoadTaskConst.MonitorMode.CANCELED.ordinal -> printTasks(LoadTaskConst.MonitorTag.CANCEL.tag, canceledTasks)
            LoadTaskConst.MonitorMode.COMPLETED.ordinal -> printTasks(LoadTaskConst.MonitorTag.COMPLETE.tag, completedTasks)
            LoadTaskConst.MonitorMode.PRE_COMPLETE.ordinal -> {
                printQueueTasks(LoadTaskConst.MonitorTag.PREPARED.tag, preparedTasks)
                printTasks(LoadTaskConst.MonitorTag.COMPLETE.tag, completedTasks)
            }
            LoadTaskConst.MonitorMode.PRE_RUN.ordinal -> {
                printQueueTasks(LoadTaskConst.MonitorTag.PREPARED.tag, preparedTasks)
                printTasks(LoadTaskConst.MonitorTag.RUNNING.tag, runningTasks)
            }
            LoadTaskConst.MonitorMode.PRE_RUN_COMPLETE.ordinal -> {
                printQueueTasks(LoadTaskConst.MonitorTag.PREPARED.tag, preparedTasks)
                printTasks(LoadTaskConst.MonitorTag.RUNNING.tag, runningTasks)
                printTasks(LoadTaskConst.MonitorTag.COMPLETE.tag, completedTasks)
            }
            LoadTaskConst.MonitorMode.ALL.ordinal -> {
                printQueueTasks(LoadTaskConst.MonitorTag.PREPARED.tag, preparedTasks)
                printTasks(LoadTaskConst.MonitorTag.RUNNING.tag, runningTasks)
                printTasks(LoadTaskConst.MonitorTag.COMPLETE.tag, completedTasks)
                printTasks(LoadTaskConst.MonitorTag.CANCEL.tag, canceledTasks)
            }
            LoadTaskConst.MonitorMode.COMPLETE_CANCELED.ordinal -> {
                printTasks(LoadTaskConst.MonitorTag.COMPLETE.tag, completedTasks)
                printTasks(LoadTaskConst.MonitorTag.CANCEL.tag, canceledTasks)
            }
        }
        GLog.d(
            TAG,
            "                                                                                                            " +
                    "                                                            "
        )
        return StringBuffer()
    }

    private fun printQueueTasks(tag: String, tasks: PriorityBlockingQueue<AbsLoadTask<*>>) {
        printTasks(tag,
            (if (tasks.isNotEmpty()) {
                tasks.toList().sortedBy { absLoadTask -> absLoadTask.priority } as MutableList<AbsLoadTask<*>>
            } else {
                mutableListOf()
            })
        )
    }

    private fun printTasks(tag: String, tasks: MutableList<AbsLoadTask<*>>): StringBuffer {
        GLog.w(
            TAG,
            "|********************************************************** $tag Tasks*****************************************" +
                    "**************************************************|"
        )
        val sb = StringBuffer()
        if (tasks.size > 0) {
            kotlin.runCatching {
                for (index in 0 until tasks.size) {
                    val task = tasks[index]
                    val taskInfo = "${index + 1}$task--->"
                    sb.append(TextUtil.SPLIT_LINE).append(taskInfo).append("\n")
                }
            }.onFailure {
                GLog.e(TAG, "foreach tasks exception", it)
            }
        }
        if (sb.isNotEmpty()) {
            GLog.d(TAG, sb.toString())
        }
        GLog.w(
            TAG,
            "|*********************************************************** $tag Tasks End************************************" +
                    "******************************************************|"
        )
        return sb
    }

    companion object {
        private const val TAG = "${LoadTaskConst.TAG}LogcatMonitorStyle"
    }
}