/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsLoadTaskDispatcher.kt
 ** Description: 加载任务调度器抽象类
 ** Version: 1.0
 ** Date: 2023/6/27
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/6/27      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.taskmanage.dispatcher

import android.os.Handler
import android.os.HandlerThread
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import com.oplus.gallery.framework.abilities.taskmanage.task.ILoadTaskCallback
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst.ALBUM_SET_TAB
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst.EXPLORE_TAB
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst.SIDE_PANEL_SET
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst.TIMELINE_TAB
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst
import com.oplus.gallery.framework.taskmanage.executor.PauseableThreadPoolExecutor
import com.oplus.gallery.framework.taskmanage.monitor.ILoadTaskMonitor
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.PriorityBlockingQueue

/**
 * 加载任务调度器抽象类
 */
internal abstract class AbsLoadTaskDispatcher(private val taskMonitor: ILoadTaskMonitor) : ILoadTaskCallback {

    /**
     * 执行器map
     * - key - String, 如果任务需要独占执行器可以是使用[ILoadTaskOwner.id]作key
     * - value - PauseableThreadPoolExecutor
     */
    protected val executorMap: ConcurrentHashMap<String, PauseableThreadPoolExecutor> = ConcurrentHashMap()

    private var handlerThread: HandlerThread? = null
    private var monitorHandler: Handler? = null

    /**
     * 运行模式
     */
    abstract val runMode: RunMode

    /**
     * 记录准备中的任务
     */
    private val preparedTasks: PriorityBlockingQueue<AbsLoadTask<*>> = PriorityBlockingQueue()

    /**
     * 记录正在运行中的任务
     */
    private val runningTasks: CopyOnWriteArrayList<AbsLoadTask<*>> = CopyOnWriteArrayList()

    /**
     * 记录执行成功的任务
     */
    private val completedTasks: CopyOnWriteArrayList<AbsLoadTask<*>> = CopyOnWriteArrayList()

    /**
     * 记录被取消或移除的任务
     */
    private val cancelOrRemovedTasks: CopyOnWriteArrayList<AbsLoadTask<*>> = CopyOnWriteArrayList()

    /**
     * 获取执行器
     */
    protected abstract fun getExecutor(taskOwner: ILoadTaskOwner): PauseableThreadPoolExecutor

    /**
     * 暂停执行
     */
    fun pause() {
        executorMap.forEach { (_, value) ->
            value.pause()
        }
    }

    /**
     * 恢复执行
     */
    fun resume() {
        executorMap.forEach { (_, value) ->
            value.resume()
        }
    }

    /**
     * 释放执行器
     *
     * 独占执行器的业务，需要在业务关闭的时候释放
     */
    fun releaseExecutor(taskOwner: ILoadTaskOwner) {
        val key = taskOwner.getExecutorKey()
        GLog.d(TAG, LogFlag.DL) { "[releaseExecutor] key=$key" }
        if (skipRelease(taskOwner.id)) return
        executorMap.remove(key)?.apply { shutdown() }
    }

    /**
     * 是否跳过释放执行器，首页的界面（照片页，图集页，发现页，侧边栏）不需要释放，防止重复创建
     */
    private fun skipRelease(id: String): Boolean {
        return (id == TIMELINE_TAB) || (id == ALBUM_SET_TAB) || (id == EXPLORE_TAB) || (id == SIDE_PANEL_SET)
    }

    /**
     * 请求添加任务
     *
     * @param task
     */
    fun addTask(task: AbsLoadTask<*>) {
        if (GProperty.DEBUG_ABS_LOAD_TASK_DISPATCHER) {
            GLog.i(TAG, LogFlag.DL, "[addTask] task:${task.simpleMsg()}")
        }
        task.iLoadTaskCallback = this

        getExecutor(task.taskOwner).execute(task)
        if (LoadTaskConst.MONITOR_DEBUG) {
            preparedTasks.add(task)
            showTasksInfoMonitor("addTask")
        }
    }

    override fun onStart(task: AbsLoadTask<*>) {
        if (GProperty.DEBUG_ABS_LOAD_TASK_DISPATCHER) {
            GLog.i(TAG, LogFlag.DL, "[onStart] task:${task.simpleMsg()}")
        }
        runningTasks.add(task)
        if (LoadTaskConst.MONITOR_DEBUG) {
            preparedTasks.remove(task)
            showTasksInfoMonitor("onStart")
        }
    }

    override fun onComplete(task: AbsLoadTask<*>) {
        if (GProperty.DEBUG_ABS_LOAD_TASK_DISPATCHER) {
            GLog.i(TAG, LogFlag.DL, "[onComplete] task:${task.simpleMsg()}")
        }
        runningTasks.remove(task)
        if (LoadTaskConst.MONITOR_DEBUG) {
            completedTasks.add(task)
            showTasksInfoMonitor("onComplete")
        }
    }

    override fun onCancel(task: AbsLoadTask<*>) {
        GLog.i(TAG, LogFlag.DL, "[onCancel] task:${task.simpleMsg()}")
        if (LoadTaskConst.MONITOR_DEBUG) {
            preparedTasks.remove(task)
            runningTasks.remove(task)
            cancelOrRemovedTasks.add(task)
            showTasksInfoMonitor("onCancel")
        }
    }

    /**
     * 清除指定的在排队的task记录
     */
    private fun clearTaskFromPreparedTasks(taskOwner: ILoadTaskOwner) {
        val iterator = preparedTasks.iterator()
        while (iterator.hasNext()) {
            val value = iterator.next()
            if (value.taskOwner.id == taskOwner.id) {
                iterator.remove()
            }
        }
    }

    /**
     * 根据priority，调整task优先级
     */
    fun adjustTaskOwnerPriority(taskOwner: ILoadTaskOwner, priority: Int) {
        adjustTaskOwnerPriorityInternal(getExecutor(taskOwner), taskOwner, priority)
    }

    private fun adjustTaskOwnerPriorityInternal(executor: PauseableThreadPoolExecutor, taskOwner: ILoadTaskOwner, priority: Int) {
        if (executor.queue.isNullOrEmpty()) {
            GLog.d(TAG, LogFlag.DL, "[adjustTaskOwnerPriorityInternal] executor queue isNullOrEmpty  = true . return")
            return
        }
        val taskList = executor.queue.toMutableList() as MutableList<AbsLoadTask<*>>
        executor.queue.clear()
        clearTaskFromPreparedTasks(taskOwner)
        taskList.forEach { task ->
            if (task.taskOwner == taskOwner) {
                task.priority = priority
            }
        }
        taskList.sortBy { absLoadTask -> absLoadTask.priority }
        taskList.forEach {
            reAddTask(executor, it)
        }
        GLog.v(TAG, LogFlag.DL, "[adjustTaskOwnerPriorityInternal] finished.")
        if (LoadTaskConst.MONITOR_DEBUG) {
            showTasksInfoMonitor("adjustTaskOwnerPriorityInternal")
        }
    }

    private fun reAddTask(executor: PauseableThreadPoolExecutor, task: AbsLoadTask<*>) {
        GLog.i(TAG, LogFlag.DL, "[reAddTask] task:${task.simpleMsg()}")
        executor.execute(task)

        if (LoadTaskConst.MONITOR_DEBUG) {
            preparedTasks.add(task)
            showTasksInfoMonitor("reAddTask")
        }
    }

    private fun tryToRemoveOrInterruptTask(executor: PauseableThreadPoolExecutor, task: AbsLoadTask<*>) {
        if (task.runningState == AbsLoadTask.RunningState.RUNNING) {
            if (!task.isInterruptable) {
                GLog.w(TAG, LogFlag.DL, "[tryToRemoveOrInterruptTask] task:${task.simpleMsg()} can't be Interrupted")
            } else {
                // 执行线程中断
                task.interrupt()
            }
        } else if (task.runningState == AbsLoadTask.RunningState.CREATED) {
            // 从线程池队列中移除任务
            val ret = executor.remove(task)
            GLog.d(TAG, LogFlag.DL, "[tryToRemoveOrInterruptTask] remove task:${task.simpleMsg()}, success:$ret")
            if (ret) {
                // 已经在运行的任务，task会修改状态并回调onCancelled；此处需要对准备中的任务的移除添加状态更新和回调处理
                task.runningState = AbsLoadTask.RunningState.CANCELED
                task.onCancelled()
            } else {
                GLog.w(TAG, LogFlag.DL, "[tryToRemoveOrInterruptTask] prepared task:${task.simpleMsg()} remove failed")
            }
        }
    }

    fun askForCancel(taskOwner: ILoadTaskOwner) {
        val tasks = mutableListOf<AbsLoadTask<*>>()
        val executor = getExecutor(taskOwner)
        fun addTasksByTaskOwner(executor: PauseableThreadPoolExecutor) {
            executor.queue.forEach {
                val task = it as AbsLoadTask<*>
                if (task.taskOwner == taskOwner) {
                    tasks.add(it)
                }
            }
        }

        addTasksByTaskOwner(executor)

        runningTasks.forEach {
            val task = it as AbsLoadTask<*>
            if (task.taskOwner == taskOwner) {
                tasks.add(it)
            }
        }

        tasks.forEach {
            tryToRemoveOrInterruptTask(executor, it)
        }
    }

    private fun showTasksInfoMonitor(entrance: String) {
        if (LoadTaskConst.MONITOR_DEBUG) {
            startHandlerThread()
            monitorHandler?.post {
                GLog.d(
                    TAG, LogFlag.DL, "[showTasksInfoMonitor] entrance:$entrance, " +
                            "thread:${Thread.currentThread().id}:${Thread.currentThread().name}"
                )
                taskMonitor.showMonitorPanel(completedTasks, runningTasks, cancelOrRemovedTasks, preparedTasks)
            }
        }
    }

    private fun startHandlerThread() {
        GLog.d(TAG, LogFlag.DL, "[startHandlerThread]")
        if (handlerThread == null) {
            handlerThread = HandlerThread("TaskMonitor")
            handlerThread?.start()
        }
        if (monitorHandler == null) {
            monitorHandler = handlerThread?.looper?.let { Handler(it) }
        }
    }

    companion object {
        private const val TAG = "${LoadTaskConst.TAG}AbsLoadTaskDispatcher"
    }
}

/**
 * 调度器的运行模式
 * 这个之代表执行器的模式，非整个框架的模式
 */
internal enum class RunMode {
    /**
     * 单线程模式
     */
    SINGLE_THREAD,

    /**
     * 多线程模式
     * 暂未启用，敬请期待
     */
    MULTI_THREAD
}

/**
 * 获取LoadTaskOwner的执行器的key
 * 该key用于在[AbsLoadTaskDispatcher.executorMap]中操作
 */
internal fun ILoadTaskOwner.getExecutorKey(): String {
    val executorOwner = taskConfigStrategy.getExecutorConfig().executorOwner
    return id + executorOwner
}