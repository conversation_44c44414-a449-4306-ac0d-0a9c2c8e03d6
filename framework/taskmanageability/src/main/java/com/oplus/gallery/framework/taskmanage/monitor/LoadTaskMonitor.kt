/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DataLoadTaskMonitor.kt
 ** Description:数据加载任务监控器
 ** Version: 1.0
 ** Date: 2023/6/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/16      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.taskmanage.monitor

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_LOAD_TASK_MONITOR_INTERVAL
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst
import java.util.concurrent.PriorityBlockingQueue

/**
 * 数据加载任务监控器
 * 1.收集数据
 * 2."监控面板"展示直观数据
 */
internal class LoadTaskMonitor(monitorTitle: String) : ILoadTaskMonitor {

    private var lastShowTime: Long = 0

    override val iMonitorStyle: IMonitorStyle by lazy {
        if (LoadTaskConst.MONITOR_STYLE == LoadTaskConst.MonitorStyle.LOGCAT.ordinal) {
            LogcatMonitorStyle(monitorTitle)
        } else {
            LogcatMonitorStyle(monitorTitle)
        }
    }

    /**
     * 展示监控面板
     * 需要做一个好的易读的“监控面板”
     */
    override fun showMonitorPanel(
        completedTasks: MutableList<AbsLoadTask<*>>,
        runningTasks: MutableList<AbsLoadTask<*>>,
        canceledTasks: MutableList<AbsLoadTask<*>>,
        preparedTasks: PriorityBlockingQueue<AbsLoadTask<*>>
    ) {
        if (System.currentTimeMillis() - lastShowTime >= DEBUG_LOAD_TASK_MONITOR_INTERVAL) {
            GLog.d(iMonitorStyle.format(completedTasks, runningTasks, canceledTasks, preparedTasks).toString())
            lastShowTime = System.currentTimeMillis()
        } else {
            GLog.d(TAG, "[showMonitorPanel] no need to show so frequently")
        }
    }

    companion object {
        private const val TAG = "${LoadTaskConst.TAG}LoadTaskMonitor"
    }
}