/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TaskManageAbilityImpl.kt
 ** Description: 加载任务的管理能力实现类
 ** Version: 1.0
 ** Date: 2023/7/10
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/7/10      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.taskmanage

import com.oplus.appability.AbsAppAbility
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.ITaskManageAbility
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskType
import com.oplus.gallery.framework.taskmanage.const.LoadTaskConst
import com.oplus.gallery.framework.taskmanage.manager.AbsLoadTaskManager
import com.oplus.gallery.framework.taskmanage.manager.DataLoadTaskManager
import java.util.PriorityQueue
import java.util.concurrent.ConcurrentHashMap

/**
 * 加载任务的管理能力实现类
 */
class TaskManageAbilityImpl : AbsAppAbility(), ITaskManageAbility {

    override val domainInstance: ITaskManageAbility = this

    private val loadTaskManagers = ConcurrentHashMap<TaskType, AbsLoadTaskManager>()

    init {
        loadTaskManagers[TaskType.DB] = DataLoadTaskManager
    }

    override fun register(iLoadTaskOwner: ILoadTaskOwner) {
        loadTaskManagers.forEach {
            it.value.register(iLoadTaskOwner)
        }
    }

    override fun unregister(iLoadTaskOwner: ILoadTaskOwner) {
        loadTaskManagers.forEach {
            it.value.unregister(iLoadTaskOwner)
        }
    }

    override fun submitTask(task: AbsLoadTask<*>): Boolean {
        return loadTaskManagers[task.taskType]?.submit(task) ?: let {
            GLog.w(TAG, "submitTask no found AbsLoadTaskManager")
            false
        }
    }

    override fun submitBatch(tasks: PriorityQueue<AbsLoadTask<*>>) {
        if (checkTasksType(tasks)) {
            GLog.w(TAG, "all task type must be the same")
            return
        }
        loadTaskManagers[tasks.peek().taskType]?.submitBatch(tasks) ?: let {
            GLog.w(TAG, "submitTask no found AbsLoadTaskManager")
        }
    }

    override fun requestCancel(task: AbsLoadTask<*>) {
        loadTaskManagers[task.taskType]?.askForCancel(task)
    }

    override fun requestCancel(iLoadTaskOwner: ILoadTaskOwner) {
        loadTaskManagers.forEach {
            it.value.askForCancel(iLoadTaskOwner)
        }
    }

    override fun switchToBackground(iLoadTaskOwner: ILoadTaskOwner) {
        loadTaskManagers.forEach {
            it.value.switchToBackground(iLoadTaskOwner)
        }
    }

    override fun switchToForeground(iLoadTaskOwner: ILoadTaskOwner) {
        loadTaskManagers.forEach {
            it.value.switchToForeground(iLoadTaskOwner)
        }
    }

    override fun pause() {
        loadTaskManagers.forEach {
            it.value.pause()
        }
    }

    override fun resume() {
        loadTaskManagers.forEach {
            it.value.resume()
        }
    }

    private fun checkTasksType(tasks: PriorityQueue<AbsLoadTask<*>>): Boolean {
        var oldTask: AbsLoadTask<*>? = null
        tasks.forEach {
            oldTask = if (oldTask == null) {
                it
            } else {
                if (oldTask?.taskType != it.taskType) {
                    return false
                } else {
                    it
                }
            }
        }
        return true
    }

    companion object {
        private const val TAG = "${LoadTaskConst.TAG}TaskManageAbility"
    }
}