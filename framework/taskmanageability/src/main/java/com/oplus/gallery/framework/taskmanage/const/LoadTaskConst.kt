/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LoadTaskConst.kt
 ** Description:加载任务相关常量与配置
 ** Version: 1.0
 ** Date: 2023/6/21
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/6/21      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.taskmanage.const

import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties

/**
 * 加载任务相关常量与配置
 */
internal object LoadTaskConst {

    const val TAG = "LOADTASK."

    /**
     * 任务加载的调试开关
     */
    val TASKLOAD_DEBUG = GallerySystemProperties.getBoolean("debug.gallery.taskload.control.switch", false)

    /**
     * 任务监控面板的调试开关
     */
    val MONITOR_DEBUG = GallerySystemProperties.getBoolean("debug.gallery.taskmonitor.switch", false)

    /**
     * monitor显示样式控制
     */
    val MONITOR_STYLE = GallerySystemProperties.getInt("debug.gallery.task.monitor.style", MonitorStyle.LOGCAT.ordinal)

    /**
     * monitor显示模式控制
     */
    val MONITOR_MODE = GallerySystemProperties.getInt("debug.gallery.task.monitor.mode", MonitorMode.PREPARED.ordinal)

    internal enum class MonitorMode {
        ALL,
        PREPARED,
        RUNNING,
        CANCELED,
        COMPLETED,
        PRE_RUN,
        PRE_COMPLETE,
        PRE_RUN_COMPLETE,
        COMPLETE_CANCELED
    }

    internal enum class MonitorStyle {
        /**
         * 对应LogcatMonitorStyle
         */
        LOGCAT
    }

    internal enum class MonitorTag(val tag: String) {
        PREPARED("Prepared"),
        RUNNING("Running"),
        COMPLETE("Completed"),
        CANCEL("Cancelled")
    }
}