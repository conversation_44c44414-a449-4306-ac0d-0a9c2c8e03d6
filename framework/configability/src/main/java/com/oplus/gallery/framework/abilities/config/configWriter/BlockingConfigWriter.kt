/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BlockingConfigWriter.kt
 ** Description:耗时配置的写入，比如AIUnit相关的配置
 ** Version: 1.0
 ** Date : 2024/6/18
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2024/06/18    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.config.configWriter

import android.content.Context
import com.oplus.aiunit.core.data.DetectName.VISION_IMAGE_AI_COMPOSITION
import com.oplus.aiunit.core.data.DetectName.VISION_IMAGE_DEBLUR
import com.oplus.aiunit.core.data.DetectName.VISION_IMAGE_DEFECT_RECOGNITION
import com.oplus.aiunit.core.data.DetectName.VISION_IMAGE_DEREFLECTION
import com.oplus.aiunit.core.data.DetectName.VISION_IMAGE_IMPLICIT_WATER_MASK
import com.oplus.aiunit.core.data.DetectName.VISION_IMAGE_PASSERBY_RECOGNITION
import com.oplus.aiunit.core.data.DetectName.VISION_IMAGE_QUALITY_ENHANCEMENT
import com.oplus.aiunit.core.data.DetectName.VISION_PICTURE_INPAINTING
import com.oplus.aiunit.core.data.DetectName.VISION_PICTURE_SEGMENTATION
import com.oplus.aiunit.core.data.SceneName.AI_INTERACT_SEGMENT
import com.oplus.aiunit.core.data.SceneName.AI_PRIVACY_MOSAIC
import com.oplus.aiunit.core.data.SceneName.CLOUD_AI_REMOVE
import com.oplus.aiunit.core.data.SceneName.CLOUD_PICTURE_AI_COMPOSITION
import com.oplus.aiunit.core.data.SceneName.CLOUD_PICTURE_DEBLUR
import com.oplus.aiunit.core.data.SceneName.CLOUD_PICTURE_DEFECT_RECOGNITION
import com.oplus.aiunit.core.data.SceneName.CLOUD_PICTURE_DEREFLECTION
import com.oplus.aiunit.core.data.SceneName.CLOUD_PICTURE_QUALITY_ENHANCEMENT
import com.oplus.aiunit.meowai.detector.DetectName.CLOUD_IMAGE_AI_GRAFFITI
import com.oplus.aiunit.meowai.detector.DetectName.CLOUD_IMAGE_DEGLARE
import com.oplus.aiunit.meowai.detector.DetectName.VISION_IMAGE_SHARPEN
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.FlashPicSelect.FLASH_PIC_SELECT_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Classification.AiPets.AI_PETS_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_DEGLARE_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_DEREFLECTION_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_DE_BLUR_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_ELIMINATE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_ELIMINATE_INPAINTING_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_GRAFFITI_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_BLUR_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_REFLECTION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.DETECTOR_DOWN_NAME_KEY_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.DETECTOR_NAME_KEY_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FLAW_RECOGNIZED_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FLAW_RECOGNIZE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IMAGE_QUALITY_ENHANCE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_ELIMINATE_AUTHORIZED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_REPAIR_AUTHORIZED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_UNIT_CONFIG_AVAILABLE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_IMAGE_QUALITY_ENHANCE_AUTHORIZED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_IMAGE_COMPOSITION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_PASSERBY_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_AUTO_MOSAIC_MODEL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_IMAGE_COMPOSITION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_PASSERS_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_AI_ELIMINATE_INPAINTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_AI_ELIMINATE_SEGMENTATION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_AI_IMPLICIT_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_PASSERS_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.PASSERBY_ELIMINATE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.RM_AI_DE_BLUR_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.SuperText.GET_SUPER_TEXT_V1_OCR_SUPPORTED_SUSPEND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.SuperText.GET_SUPER_TEXT_V2_SUPPORTED_SUSPEND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.CAMERA_CONFIG_EXPIRY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V2
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.AI_CAPTION_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.CLIP_AND_TEXT_EMBEDDING_PLUGIN_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.GET_MEDIA_STORE_CAMERA_QUICK_URI
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_MEDIA_STORE_CAMERA_QUICK_URI
import com.oplus.gallery.framework.abilities.scan.face.AIFaceConstants

/**
 * 耗时配置的写入，比如AIUnit相关的配置
 * @param context Context
 * @param configAbility IConfigSetterAbility
 */
class BlockingConfigWriter(private val context: Context, private val configAbility: IConfigSetterAbility) {

    private val configHandlers = listOf<IConfigHandler<*>>(
        AIUnitDetectDataHandler(),
        AIUnitSceneDataHandler(),
        CommonConfigHandler(),
        CameraConfigHandler()
    )

    private val configList: List<Config<*>> by lazy {
        listOf(
            AIUnitSupportConfig(IS_SUPPORT_AI_ELIMINATE_SEGMENTATION, VISION_PICTURE_SEGMENTATION),
            AIUnitSupportConfig(IS_SUPPORT_AI_ELIMINATE_INPAINTING, VISION_PICTURE_INPAINTING),
            AIUnitSupportConfig(IS_SUPPORT_PASSERS_ELIMINATE, VISION_IMAGE_PASSERBY_RECOGNITION),
            AIUnitSupportConfig(IS_SUPPORT_AI_IMPLICIT_WATERMARK, VISION_IMAGE_IMPLICIT_WATER_MASK),

            DetectStateConfig(AI_ELIMINATE_INPAINTING_STATE, VISION_PICTURE_INPAINTING),
            DetectStateConfig(AI_ELIMINATE_DETECT_STATE, VISION_PICTURE_SEGMENTATION),
            DetectStateConfig(AI_REPAIR_DE_REFLECTION_DETECT_STATE, VISION_IMAGE_DEREFLECTION),
            DetectStateConfig(AI_REPAIR_DE_BLUR_DETECT_STATE, VISION_IMAGE_DEBLUR),
            DetectStateConfig(AI_LIGHTING_DETECT_STATE, DETECTOR_NAME_KEY_AI_LIGHTING),
            DetectStateConfig(AI_COMPOSITION_DETECT_STATE, VISION_IMAGE_AI_COMPOSITION),
            DetectStateConfig(AI_BEST_TAKE_DETECT_STATE, AIFaceConstants.DETECT_NAME),
            DetectStateConfig(IMAGE_QUALITY_ENHANCE_DETECT_STATE, VISION_IMAGE_QUALITY_ENHANCEMENT),
            DetectStateConfig(FLAW_RECOGNIZE_DETECT_STATE, VISION_IMAGE_DEFECT_RECOGNITION),
            DetectStateConfig(PASSERBY_ELIMINATE_DETECT_STATE, VISION_IMAGE_PASSERBY_RECOGNITION),

            DownloadableConfig(IS_NEED_DOWNLOAD_AI_ELIMINATE, CLOUD_AI_REMOVE),
            DownloadableConfig(IS_NEED_DOWNLOAD_PASSERBY_ELIMINATE, AI_INTERACT_SEGMENT),
            DownloadableConfig(IS_NEED_DOWNLOAD_IMAGE_QUALITY_ENHANCE, CLOUD_PICTURE_QUALITY_ENHANCEMENT),
            DownloadableConfig(IS_NEED_DOWNLOAD_IMAGE_COMPOSITION, CLOUD_PICTURE_AI_COMPOSITION),

            UpdatableConfig(IS_NEED_UPDATE_PASSERS_ELIMINATE, AI_INTERACT_SEGMENT),
            UpdatableConfig(IS_NEED_UPDATE_AI_ELIMINATE, CLOUD_AI_REMOVE),
            UpdatableConfig(IS_NEED_UPDATE_AUTO_MOSAIC_MODEL, AI_PRIVACY_MOSAIC),
            UpdatableConfig(IS_NEED_UPDATE_IMAGE_QUALITY_ENHANCE, CLOUD_PICTURE_QUALITY_ENHANCEMENT),
            UpdatableConfig(IS_NEED_UPDATE_IMAGE_COMPOSITION, CLOUD_PICTURE_AI_COMPOSITION),

            SceneDownloadStateConfig(FLAW_RECOGNIZED_DOWNLOAD_STATE, CLOUD_PICTURE_DEFECT_RECOGNITION),
            SceneDownloadStateConfig(AI_DEREFLECTION_DOWNLOAD_STATE, CLOUD_PICTURE_DEREFLECTION),
            SceneDownloadStateConfig(AI_DE_BLUR_DOWNLOAD_STATE, CLOUD_PICTURE_DEBLUR),
            SceneDownloadStateConfig(RM_AI_DE_BLUR_DOWNLOAD_STATE, VISION_IMAGE_SHARPEN),
            SceneDownloadStateConfig(AI_GRAFFITI_DOWNLOAD_STATE, CLOUD_IMAGE_AI_GRAFFITI),
            SceneDownloadStateConfig(AI_COMPOSITION_DOWNLOAD_STATE, CLOUD_PICTURE_AI_COMPOSITION),
            SceneDownloadStateConfig(CLIP_AND_TEXT_EMBEDDING_PLUGIN_DOWNLOAD_STATE, CLIP_AND_TEXT_EMBEDDING_PLUGIN),
            SceneDownloadStateConfig(FLASH_PIC_SELECT_PLUGIN_DOWNLOAD_STATE, FLASH_PIC_SELECT_PLUGIN),
            SceneDownloadStateConfig(AI_PETS_PLUGIN_DOWNLOAD_STATE, AI_PETS_PLUGIN),
            // ai_caption模型下载配置
            SceneDownloadStateConfig(AI_CAPTION_PLUGIN_DOWNLOAD_STATE, AI_CAPTION),

            SceneDownloadStateConfig(AI_LIGHTING_DOWNLOAD_STATE, DETECTOR_NAME_KEY_AI_LIGHTING),
            DetectDownloadStateConfig(AI_BEST_TAKE_DOWNLOAD_STATE, AIFaceConstants.DETECT_NAME),

            SceneDownloadStateConfig(AI_DEGLARE_DOWNLOAD_STATE, CLOUD_IMAGE_DEGLARE),
            CommonConfig(IS_AGREE_AI_UNIT_PRIVACY) { AISettings.isPrivacyAvailable(it) },
            CommonConfig(IS_AI_ELIMINATE_AUTHORIZED) {
                AISettings.getAuthorizeStatus(it, VISION_PICTURE_INPAINTING) != 0
            },
            CommonConfig(IS_AI_REPAIR_AUTHORIZED) {
                AISettings.getAuthorizeStatus(it, VISION_IMAGE_DEBLUR) != 0
            },
            CommonConfig(IS_IMAGE_QUALITY_ENHANCE_AUTHORIZED) {
                AISettings.getAuthorizeStatus(it, VISION_IMAGE_QUALITY_ENHANCEMENT) != 0
            },
            CommonConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V2) {
                configAbility.getBooleanConfig(GET_SUPER_TEXT_V2_SUPPORTED_SUSPEND) ?: false
            },
            CommonConfig(FEATURE_IS_SUPPORT_SUPER_TEXT_V1_OCR) {
                configAbility.getBooleanConfig(GET_SUPER_TEXT_V1_OCR_SUPPORTED_SUSPEND) ?: false
            },
            CommonConfig(IS_SUPPORT_MEDIA_STORE_CAMERA_QUICK_URI) {
                configAbility.getBooleanConfig(GET_MEDIA_STORE_CAMERA_QUICK_URI) ?: false
            },
            CommonConfig(IS_AI_UNIT_CONFIG_AVAILABLE) {
                val code = AISettings.getLastProvisionTimestamp(context)
                GLog.d(TAG, "[CommonConfig] lastProvisionTimestamp:$code")
                code != -1L
            },
            CameraConfig(CAMERA_CONFIG_EXPIRY)
        )
    }

    private val configMap by lazy {
        HashMap<String, Config<*>>().apply {
            configList.forEach {
                put(it.configName, it)
            }
        }
    }

    /**
     * 更新配置
     * @param configNames 配置列表
     */
    fun updateConfig(vararg configNames: String) {
        val configMap = configNames.asSequence().mapNotNull { configMap[it] }.groupBy { it::class.java }.toMap()
        runCatching {
            configHandlers.forEach { handler ->
                val handleConfigs = mutableListOf<Config<*>>()
                handler.configs.forEach { configClazz ->
                    configMap[configClazz]?.let {
                        handleConfigs.addAll(it)
                    }
                }
                if (handleConfigs.isNotEmpty()) {
                    handler.process(context, configAbility, handleConfigs)
                }
            }
        }.onFailure {
            GLog.e(TAG, "[updateConfig] error", it)
        }
    }

    companion object {
        private const val TAG = "BlockingConfigWriter"

        /**
         * 融合搜索模式所需算法插件在 AIUnit 定义的 SceneName
         */
        private const val CLIP_AND_TEXT_EMBEDDING_PLUGIN = "gallery_search"

        /**
         * 闪速选片插件 Scene Name
         */
        private const val FLASH_PIC_SELECT_PLUGIN = "flash_pic_select"

        /**
         * 宠物算法插件
         */
        private const val AI_PETS_PLUGIN = "ai_pets"

        /**
         * caption插件在AiUnit定义的SceneName
         */
        private const val AI_CAPTION = "ai_caption"
    }
}

/**
 * 配置项
 */
internal interface Config<T> {

    /**
     * 在[ConfigID]下定义的配置项名称
     */
    val configName: String

    /**
     * 执行写入
     * @param configAbility IConfigSetterAbility
     * @param data 写入的数据
     */
    fun write(configAbility: IConfigSetterAbility, data: T)
}

/**
 * 某一类配置项的Handler
 */
internal interface IConfigHandler<T> {

    /**
     * 要处理的配置项类型
     */
    val configs: List<Class<out Config<T>>>

    /**
     * 处理配置项
     * @param context Context
     * @param configAbility IConfigSetterAbility
     * @param configs 要处理的配置项
     */
    fun process(context: Context, configAbility: IConfigSetterAbility, configs: List<Config<*>>)
}

