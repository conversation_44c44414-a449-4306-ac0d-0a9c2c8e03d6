/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IStorage
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/5/5
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/5      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.config.storage

import com.oplus.gallery.framework.abilities.config.IConfigListener
import com.oplus.gallery.framework.abilities.config.args.ConfigExtraArgs
import com.oplus.gallery.framework.abilityapt.annotations.ConfigNode

/**
 * 存储接口，提供读写、存储数据变更通知能力
 */
internal interface IStorage {

    fun read(configNode: ConfigNode, defValue: Int = 0): Int?

    fun read(configNode: ConfigNode, defValue: Long = 0): Long?

    fun read(configNode: ConfigNode, defValue: Float = 0F): Float?

    fun read(configNode: ConfigNode, defValue: Boolean = false): Boolean?

    fun read(configNode: ConfigNode, defValue: String? = null): String?

    fun readObject(configNode: ConfigNode, defValue: Any? = null): Any?

    /**
     * 带参方式，从存储接口读取配置
     */
    fun readWithArgs(configNode: ConfigNode, args: ConfigExtraArgs, defValue: Any?): Any?

    fun write(configNode: ConfigNode, value: Any?)

    /**
     * 同步写入磁盘文件
     *
     * @param configNode 配置节点
     * @param value 写入的值
     * @return true表示写入磁盘文件成功；false表示写入磁盘文件失败
     */
    fun writeSync(configNode: ConfigNode, value: Any?): Boolean

    fun writeBatch(map: Map<ConfigNode, Any>)

    /**
     * 带参方式，写入存储
     * --- marked by caiconghu 待修改
     * 1. 接口名不合理。“带参去找配置项”其实就是查询配置项。后面改queryConfig之类的，相关接口都跟着改一下。
     * 2. ConfigExtraArgs 这个参数需要和ConfigID对应上。待ConfigID改成结构体后，这里用个Map之类替代下。
     * 见入口接口评论：[com.oplus.gallery.framework.abilities.config.IConfigAbility.getConfigWithArgs]
     */
    fun writeWithArgs(configNode: ConfigNode, args: ConfigExtraArgs, value: Any?)

    fun deleteBatch(configNodeSet: Set<ConfigNode>)

    fun register(configNode: ConfigNode, listener: IConfigListener)

    fun unregister(configNode: ConfigNode, listener: IConfigListener)

    fun contains(configNode: ConfigNode): Boolean
}