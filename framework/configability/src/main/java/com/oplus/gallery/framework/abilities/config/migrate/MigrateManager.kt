/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MigrateManager.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/5/24
 ** Author: <EMAIL>

 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/24      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.config.migrate

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.storage.SPUtils
import kotlin.reflect.KClass

/**
 * 旧版本SP配置兼容，负责数据迁移到新的config能力
 */
class MigrateManager(var context: Context) {

    /**
     * 上次迁移的版本号
     */
    private var lastMigrateVersion = 0

    fun startMigrate() {
        GTrace.trace("$TAG.startMigrate") {
            lastMigrateVersion = SPUtils.getInt(context, null, CONFIG_LAST_MIGRATE_VERSION, 0)
            if (isNeedMigrate(CURRENT_VERSION, lastMigrateVersion)) {
                // 执行迁移
                migrate()
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun isNeedMigrate(version: Int, lastMigrateVersion: Int): Boolean {
        return version > lastMigrateVersion
    }

    private fun migrate() {
        var lastSaveVersion = 0
        migrateMap.forEach { (version, versionMigrateMap) ->
            if (version > lastMigrateVersion) {
                versionMigrateMap.forEach { (originSpData, targetSpData) ->
                    migrateData(originSpData, targetSpData)
                }
                // 更新版本号; HashMap是无序的，要记录的是本次迁移最大的版本号
                if (version > lastSaveVersion) {
                    SPUtils.setInt(context, null, CONFIG_LAST_MIGRATE_VERSION, version)
                    lastSaveVersion = version
                }
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun migrateData(originSpData: SpData, targetSpData: SpData) {
        // 无数据可迁移
        if (!SPUtils.contains(context, originSpData.spName, originSpData.key)) {
            return
        }
        // 已经迁移过
        if (SPUtils.contains(context, targetSpData.spName, targetSpData.key)) {
            return
        }
        when (originSpData.dataType) {
            Int::class -> {
                val originValue = SPUtils.getInt(context, originSpData.spName, originSpData.key, 0)
                SPUtils.setInt(context, targetSpData.spName, targetSpData.key, originValue)
            }
            Boolean::class -> {
                val originValue = SPUtils.getBoolean(context, originSpData.spName, originSpData.key, false)
                SPUtils.setBoolean(context, targetSpData.spName, targetSpData.key, originValue)
            }
            Float::class -> {
                val originValue = SPUtils.getFloat(context, originSpData.spName, originSpData.key, 0F)
                SPUtils.setFloat(context, targetSpData.spName, targetSpData.key, originValue)
            }
            Long::class -> {
                val originValue = SPUtils.getLong(context, originSpData.spName, originSpData.key, 0L)
                SPUtils.setLong(context, targetSpData.spName, targetSpData.key, originValue)
            }
            String::class -> {
                val originValue = SPUtils.getString(context, originSpData.spName, originSpData.key, null)
                SPUtils.setString(context, targetSpData.spName, targetSpData.key, originValue)
            }
        }
    }

    companion object {
        private const val VERSION_1 = 1

        private const val VERSION_2 = 2

        private const val VERSION_3 = 3

        private const val VERSION_4 = 4

        private const val VERSION_5 = 5

        private const val TAG = "MigrateManager"

        /**
         * 增加新的数据迁移时，记得增加此版本号
         */
        private const val CURRENT_VERSION = VERSION_5


        const val CONFIG_LAST_MIGRATE_VERSION = "config_last_migrate_version"

        /**
         * 数据迁移说明：
         * 1. 每次更新数据迁移，需要增加版本号[CURRENT_VERSION]并新声明一个 [migrateMap]
         * 2. `migrateMap` 中存放迁移前后的信息，key为旧数据，value为新数据。
         *
         * 迁移信息说明：
         * - key：旧数据，指明旧的SP信息，包括SP名字，key字段以及数据类型。
         * - value：新数据，对应于新声明的 `ConfigID`, 其中的信息定义如下：
         * 以 `ConfigID.Common.UserProfile.ViewBehavior.xxx_setting` 举例
         *    - spName：对应于 `ConfigID` 的前两个引用路径，格式为 “大类_小类_config”(取`ConfigID`后的前两级加上`_config`，且首字母小写)。
         *      上述例子对应的 `spName` 为： `common_userProfile_config`
         *    - key：对应于`ConfigID`的全引用路径。
         *      上述例子对应的 `key` 为 `common_userProfile_viewBehavior_xxx_setting`
         *    - dataType： 对应于数据的类型
         */

        /**
         * 版本1迁移的数据
         */
        private val migrateMapV1 =
            mutableMapOf(
                SpData(
                    spName = "layer_type_sp",
                    key = "layer_type",
                    dataType = Int::class
                ) to SpData(
                    spName = "business_colorManagement_config",
                    key = "business_colorManagement_is_wide_color_gamut_enabled",
                    dataType = Int::class
                )
            )

        private val migrateMapV2 =
            mutableMapOf(
                SpData(
                    spName = "com.coloros.gallery3d_preferences",
                    key = "pref_art_show_switch_state",
                    dataType = Boolean::class
                ) to SpData(
                    spName = "common_userProfile_config",
                    key = "common_userProfile_viewBehavior_art_show_switch",
                    dataType = Boolean::class
                )
            )

        private val migrateMapV3 =
            mutableMapOf(
                SpData(
                    spName = "pref_component",
                    key = "first_using_hdr_transform",
                    dataType = Boolean::class
                ) to SpData(
                    spName = "business_pages_config",
                    key = "business_pages_photoPage_menu_first_using_hdr_transform",
                    dataType = Boolean::class
                ),
                SpData(
                    spName = "pref_component",
                    key = "first_using_rename_file",
                    dataType = Boolean::class
                ) to SpData(
                    spName = "business_pages_config",
                    key = "business_pages_photoPage_menu_first_using_rename_file",
                    dataType = Boolean::class
                ),
                SpData(
                    spName = "pref_component",
                    key = "first_using_sensitive_mosaic",
                    dataType = Boolean::class
                ) to SpData(
                    spName = "business_editor_config",
                    key = "business_editor_photoEditor_first_using_sensitive_mosaic",
                    dataType = Boolean::class
                ),
                SpData(
                    spName = "pref_component",
                    key = "pref_video_auto_play_key",
                    dataType = Boolean::class
                ) to SpData(
                    spName = "common_userProfile_config",
                    key = "common_userProfile_viewBehavior_video_auto_play",
                    dataType = Boolean::class
                )
            )

        /**
         * 13.0.x 升级13.1.x 隐私策略版本号迁移
         */
        private val migrateMapV4 =
            mutableMapOf(
                SpData(
                    spName = "com.coloros.gallery3d_preferences",
                    key = "privacy_statement_version",
                    dataType = Int::class
                ) to SpData(
                    spName = "common_userProfile_config",
                    key = "common_userProfile_privacy_privacy_statement_version",
                    dataType = Int::class
                )
            )

        private val migrateMapV5 =
            mutableMapOf(
                SpData(
                    spName = "cloudkit_account_sp",
                    key = "cloudkit_sdk_version",
                    dataType = Int::class
                ) to SpData(
                    spName = "business_cloudSync_config",
                    key = "business_cloudSync_albumSyncing_cloud_data_version",
                    dataType = Int::class
                )
            )

        private val migrateMap =
            mutableMapOf<Int, Map<SpData, SpData>>(
                VERSION_1 to migrateMapV1,
                VERSION_2 to migrateMapV2,
                VERSION_3 to migrateMapV3,
                VERSION_4 to migrateMapV4,
                VERSION_5 to migrateMapV5
            )
    }

    /**
     * 记录SP数据
     *
     * @property spName sp文件名称
     * @property key key名称
     * @property dataType 要迁移的数据类型
     */
    data class SpData(val spName: String, val key: String, val dataType: KClass<*>)
}