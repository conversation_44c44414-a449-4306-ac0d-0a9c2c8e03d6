/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SystemPropStorage
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/5/7
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/7      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.config.storage

import android.content.Context
import com.oplus.gallery.foundation.util.ext.getStringArraySafe
import com.oplus.gallery.foundation.util.ext.getStringSafe
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.framework.abilities.config.IConfigListener
import com.oplus.gallery.framework.abilities.config.args.ConfigExtraArgs
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Debug
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo
import com.oplus.gallery.framework.abilityapt.annotations.ConfigNode
import com.oplus.gallery.foundation.util.R as LibUtilR

/**
 * 系统属性存储，主要是.prop文件形式存储
 */
internal class SystemPropStorage private constructor(context: Context) : IStorage {

    private object DebugProps {
        const val DEBUG_GALLERY_ACCOUNT_TEST = "debug.gallery.account.test"
    }

    /**
     * configID到系统属性名称的映射表，保持configID命名规范的一致性。
     */
    private val systemPropMap by lazy {
        mutableMapOf<String, AbsSysPropReader<*>>(
            SystemInfo.MAX_BRIGHTNESS to IntSysPropReader(context.getStringArraySafe(LibUtilR.array.property_multibrightness)),
            SystemInfo.REGION to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.property_region)),
            SystemInfo.IS_REGION_INDIA to EqualSysPropReader(context.getStringArraySafe(LibUtilR.array.property_regionmark), VALUE_REGION_INDIA),
            SystemInfo.IS_REGION_US to EqualSysPropReader(context.getStringArraySafe(LibUtilR.array.property_regionmark), VALUE_REGION_US),
            SystemInfo.IS_REGION_VIETNAM to EqualSysPropReader(context.getStringArraySafe(LibUtilR.array.property_regionmark), VALUE_REGION_VIETNAM),
            SystemInfo.IS_REGION_TURKEY to EqualSysPropReader(context.getStringArraySafe(LibUtilR.array.property_regionmark), VALUE_REGION_TURKEY),
            SystemInfo.IS_REGION_RUSSIA to EqualSysPropReader(context.getStringArraySafe(LibUtilR.array.property_regionmark), VALUE_REGION_RUSSIA),
            SystemInfo.IS_REGION_BELARUS to EqualSysPropReader(context.getStringArraySafe(LibUtilR.array.property_regionmark), VALUE_REGION_BELARUS),
            SystemInfo.REGION_MARK to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.property_regionmark)),
            SystemInfo.OPERATOR to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.property_operator)),
            SystemInfo.AFTER_SALE_REGION to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_after_sale_region)),
            SystemInfo.MARKET_NAME to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.property_market_name)),
            SystemInfo.CAMERA_WATERMARK_PREVIEW_MARKET_NAME
                to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.camera_watermark_preview_market_name)),
            SystemInfo.OTA_VERSION to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_build_version_ota)),
            SystemInfo.ROM_VERSION to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_build_display_id)),
            SystemInfo.OS_VERSION to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.property_rom_version)),
            SystemInfo.ANDROID_VERSION to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_build_version_release)),
            SystemInfo.PRODUCT_BRAND to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_product_brand)),
            SystemInfo.IS_REALME_BRAND to EqualSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_product_brand), REALME_BRAND),
            SystemInfo.IS_OPPO_BRAND to EqualSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_product_brand), OPPO_BRAND),
            SystemInfo.IS_ONEPLUS_BRAND to EqualSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_product_brand), ONEPLUS_BRAND),
            SystemInfo.BOARD_PLATFORM to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_board_platform)),
            SystemInfo.CPU_INFO_PART_NUMBER to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_cpu_info_part_number)),
            SystemInfo.CPU_INFO to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_cpu_info)),
            SystemInfo.PRODUCT_NAME to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_product_name)),
            SystemInfo.BUILD_TIME to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_build_time_fix)),
            SystemInfo.IS_FBE_VERSION to EqualSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_crypto_type), VALUE_FBE),
            SystemInfo.DISPLAY_CORNER_RADIUS to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.property_ro_display_rc_size)),
            SystemInfo.SECONDARY_DISPLAY_CORNER_RADIUS
                    to StringSysPropReader(context.getStringArraySafe(LibUtilR.array.property_ro_secondary_display_rc_size)),
            SystemInfo.IS_SUPPORT_BRIGHTNESS_ENHANCE
                    to BooleanSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_display_gallery_brightness_enhance_enable)),
            Debug.DEBUG_GALLERY_ACCOUNT_TEST to BooleanSysPropReader(DebugProps.DEBUG_GALLERY_ACCOUNT_TEST),
            SystemInfo.FIRST_API_LEVEL to IntSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_product_first_api_level)),
            SystemInfo.PRODUCT_SERIES to StringSysPropReader(context.getStringSafe(LibUtilR.string.property_ro_oplus_product_series))
        )
    }

    /**
     * 属性的Reader基类
     *
     * @property systemProps 系统属性名，String或Array<String>两种类型
     */
    internal abstract class AbsSysPropReader<T>(val systemProps: Any) {
        /**
         * 读取属性值
         *
         * @param defValue 默认值
         * @return 返回值T
         */
        abstract fun read(defValue: T?): T?
    }

    internal class IntSysPropReader(systemProps: Any) : AbsSysPropReader<Int>(systemProps) {
        override fun read(defValue: Int?): Int? {
            return if (systemProps is Array<*>) {
                GallerySystemProperties.get(systemProps as Array<String>)?.toIntOrNull()
            } else {
                GallerySystemProperties.getInt(systemProps as String, defValue ?: 0)
            }
        }
    }

    internal open class BooleanSysPropReader(systemProps: Any) : AbsSysPropReader<Boolean>(systemProps) {
        override fun read(defValue: Boolean?): Boolean? {
            return if (systemProps is Array<*>) {
                GallerySystemProperties.get(systemProps as Array<String>)?.toBoolean()
            } else {
                GallerySystemProperties.getBoolean(systemProps as String, defValue ?: false)
            }
        }
    }

    internal class LongSysPropReader(systemProps: Any) : AbsSysPropReader<Long>(systemProps) {
        override fun read(defValue: Long?): Long? {
            return if (systemProps is Array<*>) {
                GallerySystemProperties.get(systemProps as Array<String>)?.toLongOrNull()
            } else {
                GallerySystemProperties.getLong(systemProps as String, defValue ?: 0L)
            }
        }
    }

    internal class StringSysPropReader(systemProps: Any) : AbsSysPropReader<String>(systemProps) {
        override fun read(defValue: String?): String? {
            return if (systemProps is Array<*>) {
                GallerySystemProperties.get(systemProps as Array<String>)
            } else {
                GallerySystemProperties.get(systemProps as String, defValue)
            }
        }
    }

    internal class EqualSysPropReader(systemProps: Any, private val value: String) : BooleanSysPropReader(systemProps) {
        override fun read(defValue: Boolean?): Boolean? {
            return if (systemProps is Array<*>) {
                GallerySystemProperties.get(systemProps as Array<String>) == value
            } else {
                GallerySystemProperties.get(systemProps as String) == value
            }
        }
    }

    override fun read(configNode: ConfigNode, defValue: Int): Int? {
        return (systemPropMap[configNode.id] as IntSysPropReader).read(defValue)
    }

    override fun read(configNode: ConfigNode, defValue: Long): Long? {
        return (systemPropMap[configNode.id] as LongSysPropReader).read(defValue)
    }

    override fun read(configNode: ConfigNode, defValue: Float): Float? {
        throw UnsupportedOperationException("Not support float system props")
    }

    override fun read(configNode: ConfigNode, defValue: Boolean): Boolean? {
        return (systemPropMap[configNode.id] as BooleanSysPropReader).read(defValue)
    }

    override fun read(configNode: ConfigNode, defValue: String?): String? {
        return (systemPropMap[configNode.id] as StringSysPropReader).read(defValue)
    }

    override fun readObject(configNode: ConfigNode, defValue: Any?): Any? {
        throw UnsupportedOperationException("Not support to read object from system properties")
    }

    override fun readWithArgs(configNode: ConfigNode, args: ConfigExtraArgs, defValue: Any?): Any? {
        throw UnsupportedOperationException("SystemProp not support to read with param! config:${configNode.id}")
    }

    override fun write(configNode: ConfigNode, value: Any?) {
        throw UnsupportedOperationException("App have no permission to write system properties")
    }

    override fun writeSync(configNode: ConfigNode, value: Any?): Boolean {
        throw UnsupportedOperationException("App have no permission to write system properties")
    }

    override fun writeBatch(map: Map<ConfigNode, Any>) {
        throw UnsupportedOperationException("App have no permission to write system properties")
    }

    override fun writeWithArgs(configNode: ConfigNode, args: ConfigExtraArgs, value: Any?) {
        throw UnsupportedOperationException("App have no permission to write system properties")
    }

    override fun deleteBatch(configNodeSet: Set<ConfigNode>) {
        throw UnsupportedOperationException("App have no permission to delete system properties")
    }

    override fun register(configNode: ConfigNode, listener: IConfigListener) {
        // no need to implement
    }

    override fun unregister(configNode: ConfigNode, listener: IConfigListener) {
        // no need to implement
    }

    override fun contains(configNode: ConfigNode): Boolean {
        throw UnsupportedOperationException("No meaning to call contains for system prop type config:${configNode.id}")
    }

    companion object {

        @Volatile
        private var instance: SystemPropStorage? = null

        fun get(context: Context): SystemPropStorage {
            return when {
                instance != null -> instance as SystemPropStorage
                else -> {
                    synchronized(this) {
                        if (instance == null) {
                            instance = SystemPropStorage(context)
                        }
                        instance as SystemPropStorage
                    }
                }
            }
        }

        // Regions
        private const val VALUE_REGION_INDIA = "IN"
        private const val VALUE_REGION_US = "US"
        private const val VALUE_REGION_VIETNAM = "VN"
        private const val VALUE_REGION_TURKEY = "TR"
        private const val VALUE_REGION_RUSSIA = "RU"
        private const val VALUE_REGION_BELARUS = "BY"

        private const val VALUE_MSM_8953 = "msm8953"
        private const val VALUE_FBE = "file"

        // Brand
        private const val REALME_BRAND = "realme"
        private const val OPPO_BRAND = "OPPO"
        private const val ONEPLUS_BRAND = "OnePlus"
    }
}