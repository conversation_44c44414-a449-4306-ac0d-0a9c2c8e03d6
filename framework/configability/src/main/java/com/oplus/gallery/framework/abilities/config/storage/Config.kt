/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Config
 ** Description: 配置能力中，能力项的配置。
 **
 ** Version: 1.0
 ** Date: 2024/8/9
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		   2024/8/9      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.config.storage

/**
 * 配置能力中，能力项的配置。
 * 当前大部分配置项都是配置在XML里面，但是XML内容现在越来越大，届时XML的解析性能也越来越低。后面逐步转换到配置到代码中。
 */
internal object Config {
    /**
     * 配置项：是否支持OLive图片与视频切换时调整提亮速率，这里写的是黑名单；因为升级项目，功耗团队不让修改，这里将其加在黑名单中，新项目就不用添加了
     * 这里其实添加的是以前升级项目支持ProXDR的项目，不支持ProXDR的项目，也不会使用这个机制。
     * value：支持调整的具体机型，取值 Model 名称
     */
    val CONFIG_OLIVE_ADJUST_EDR_RATE_MODEL_BLACKLIST =
        arrayOf(
            "PGEM10", // Find X6 Pro 21131
            "PGFM10", // Find X6 Pro 21135
            "PHN110", // Find N3 22003
            "CPH2499", // Find N3 22203
            "CPH2551", // Find N3 22899
            "PHT110", // Find N3 Flip 22023
            "CPH2519", // Find N3 Flip 22223
            "PHY110", // Find X7 Pro 22111
            "PHY120", // Find X7 22112
            "PHZ110", // Find X7 PHZ110 22113
            "PJV110", // Reno12 Pro 23081
            "PJW110", // Reno12 23083
            "CPH2629", //Reno12 23265
            "CPH2625", //Reno12 23261
            "PJX110", // 一加 12
        )

    /**
     * 配置项：是否支持LUMO 凝光影像
     * 珠湖海起，往后的所有Find机型
     */
    val CONFIG_LUMO_IMAGE_MODEL_BLACKLIST = arrayOf(
        "CPH2671", // Petrel 24201
        "PKH110", // Petrel 24001
        "PKH120", // Petrel 24002
        "PKC110", // Konka 23105
        "PKC130", // Konka 23106
        "CPH2659", // Konka 23216
        "PKB110", // Yala 23101
        "CPH2651" // Yala 23205
    )

    /**
     * 配置项:是否支持视频标签跑CPU而不走GPU
     * 此问题是为了修复BUG 7953659，分析原因是对应的GPU OpenCL库有问题，需要在此平台上走CPU而不走GPU
     * 目前仅7550和7150平台支持
     */
    val CONFIG_SUPPORT_VIDEOCLASSIFY_RUN_ON_CPU_LIST by lazy {
        arrayOf(
            "SM7550",
            "SM7150",
        )
    }

    /**
     * 配置项: 支持大图菜单延后加载的机型名单
     */
    val CONFIG_SUPPORT_MENU_INFLATE_POSTPONE_MODEL_LIST by lazy {
        arrayOf(
            "PKK110", // Milkyway-C1 24021
            "PKM110", // Milkyway C2 24023
        )
    }

    /**
     * 配置项: 配置延后100隐藏无缝动画机型名单
     */
    val CONFIG_SUPPORT_DELAY_100_HIDE_SEAMLESS_TRANSITION_MODEL_LIST by lazy {
        arrayOf(
            "CPH2699", // Milkyway S3-5G 24263、24264、24265
        )
    }

    /**
     * 配置项: 配置延后1500隐藏无缝动画机型名单
     */
    val CONFIG_SUPPORT_DELAY_1500_HIDE_SEAMLESS_TRANSITION_MODEL_LIST by lazy {
        arrayOf(
            "CPH2707", // Lexus 24881
            "CPH2709", // Lexus 24882
            "CPH2743", // Zhuque-S3-5G 24336
            "RMX5101",  // Neutron 24756、24757、25674
            "RMX5111" // Chopard-A 外销
        )
    }

    /**
     * 配置项：针对LivePhoto资源，禁用抠图的机型
     */
    val CONFIG_SHOULD_DISABLE_LNS_FOR_OLIVE by lazy {
        arrayOf(
            "CPH2711", // AL4 24281, 24282, 24283,
            "PKV110", // AL5 24059,
            "CPH2695", // AL5 24251, 24254, 24252, 24253
            "CPH2725", //AB4 24302
            "CPH2727", //AB4 24301
            "PKW110", //AB5 24053
            "PKW120",
            "CPH2733",
            "CPH2735", //AB5 24313, 24314
        )
    }

    /**
     * 配置项：支持quick图超短动画的机型名称
     */
    val PHOTO_QUICK_SHORT_ANIM: List<String> by lazy {
        listOf(
            "CPH2671", // Petrel 24201
            "PKH110",  // Petrel 24001
            "PKH120",  // Petrel 24002
            "PKZ110",  // ZhuQueC1 24071
            "PLA110",  // ZhuQueC2 24075
            "CPH2739", // ZhuQueS1 24325
            "CPH2737", // ZhuQueS2 24322
        )
    }

    val CODEC_SINGLE_CODEC_BOARD_PLATFORMS: List<String> by lazy {
        listOf(
            "lito",
            "holi",
            "bengal",
            "mt6789",
            "blair",
            "parrot"
        )
    }

    /**
     * 部分平台上只支持1个4k解码实例,后续如果有其他分辨率的也可在这里扩展 cpu型号|分辨率阈值
     */
    val CODEC_SINGLE_CODEC_DEVICES_BY_RESOLUTION: List<String> by lazy {
        listOf(
            "SM7325|3840",
            "SDM778G|3840",
            "SM7125|3840",
            "SDM720G|3840",
            "SDM782G|3840",
            "MT6877V_T/TTZA|3840",
            "MT6983Z|3840",
            "SM7435|3840",
            "SM6450|3840",
            "SM7675|3840",
            "SM7550|3840",
            "MT6899_ZK|3840",
            //24211 原本是SM7675，后来改名了
            "SM7675_AVALON|3840",
            //casio MT6878更名
            "D7300 Energy|3840",
            "SM7750|3840"
        )
    }

    /**
     * 只支持单个4k杜比解码实例的设备
     */
    val SINGLE_4K_DOLBY_CODEC_DEVICES: List<String> by lazy {
        listOf(
            "OPD2201",
            "OPD2202",
            "OPD2203",
            //Caihong-O
            "OPD2403",
            "OPD2404",
            //Bale-D
            "RMX3853",
            //BRZ-B
            "RMX5085",
            //Cayman-B
            "RMX5061",
            //KM外销 24297
            "CH2761",
            //KM内销 24069
            "PLM110"
        )
    }

    /**
     * 支持AI功能的14.1机型白名单
     */
    val AI_REFLECTION_DEBLUR_CLARIFY_DEVICES_WHITELIST_14_1: List<String> by lazy {
        listOf(
            //oppo caihong 内销
            "OPD2401",
            //oppo caihong 外销
            "OPD2402"
        )
    }

    /**
     * 支持AI功能（去反光、去拖影、超清像素）的15.0机型白名单
     */
    val AI_REFLECTION_DEBLUR_CLARIFY_DEVICES_WHITELIST_15_0: List<String> by lazy {
        listOf(
            //realme机型通过名单开放
            //piloti 26420 内销
            "RMX5090",
            //yamaha 24740，24742 外销
            "RMX5030",
            "RMX5031",
            //piaget-p 24623 外销
            "RMX5032",
            //casio-N 24750 外销
            "RMX5033",
            //dongfeng 内外销
            "RMX5070",
            "RMX5075",
            "RMX5071",
            "RMX5079",
            //dongfen25 24751，24752，24782 外销
            "RMX5078",
            "RMX5074",
            //BRZ-B 24720 外销
            "RMX5085",
            //cayman-B 24749 外销
            "RMX5061",
            //Enzo 23607 内销
            "RMX3888",
            //Divo 23631 内销
            "RMX3800",
            //pista  内外销
            "RMX5010",
            "RMX5011",
            //BRZ-A 24613 内销
            "RMX5080",
            //cayman-A 24610 内销
            "RMX5060",
            //bale-C 23718 外销
            "RMX3851",
            //bale-D 24687 外销
            "RMX3853",
            //Targa 24618 外销
            "RMX6688",
            //Cayman-C 24622 内销
            "RMX5062",
            //Casio-Y 24626，24780，24781，25664 内外销
            "RMX5105",
            "RMX5106",
            "RMX5110",
            //Neutron 24625，24756，24757 内外销
            "RMX5100",
            "RMX5101",
            //Neutron-P 25675
            "RMX5116",
            //Chopard 25604，25668，25669，25679 内外销
            "RMX5111",
            "RMX5112",
            //lafa内外销
            "RMX5200",
            "RMX5210",
            //Moscow-A
            "RMX5555",
            //Mumbai-A\B
            "RMX5250",
            "RMX5253"
        )
    }

    /**
     * 支持AI功能（去反光、去拖影、超清像素、AI构图）的15.0机型黑名单
     */
    val AI_REFLECTION_DEBLUR_CLARIFY_COMPOSITION_DEVICES_BLACKLIST_15_0: List<String> by lazy {
        listOf(
            //reno8内销首发os12，外销首发os13，为保持内外销一致都不支持该功能，外销要做特殊屏蔽
            "CPH2505RU",
            "CPH2505"
        )
    }

    /**
     * 支持AI推荐的设备白名单
     */
    val AI_RECOMMEND_DEVICES_WHITELIST: List<String> by lazy {
        listOf(
            /* 原需求设计上，轻量机器就不该支持智能推荐，会隐藏入口,但新轻量机器的系统轻量属性key改了，
            没有通知app去适配，导致老版本相册轻量判断失效，所以入口出现了，新版本相册适配轻量低高后判断正
            常了，入口又消失了。
            为了防止已打开“智能编辑推荐”开关的用户发现入口消失造成舆情，评估CPU性能后，决定放开已到市场的
            以下轻量高机器，其他轻量依旧隐藏入口 */
            "CPH2701", "CPH2699", "PKP110", "CPH2705", "PKQ110", "CPH2721"
        )
    }

    /**
     * 支持Google Pass Scan功能的机型白名单
     */
    val GOOGLE_PASS_SCAN_FUNC_DEVICES_WHITELIST: List<String> by lazy {
        listOf(
            //Dodge印版
            "CPH2649",
            //OnePlus Ace系列（Giulia，印度）
            "CPH2691"
        )
    }

    /**
     * 单个杜比解码实例的设备
     */
    val SINGLE_DOLBY_CODEC_DEVICES: List<String> by lazy {
        listOf(
            "RMX3853",
            "CH2761", //KM外销 24297
            "PLM110" //KM内销 24069
        )
    }

    /**
     * 不支持视频编解码提速的黑名单，以cpu型号来控制
     */
    val VIDEO_USE_OPERATE_RATE_CPU_BLACKLIST: List<String> by lazy {
        listOf(
            "SM6375"
        )
    }

    /**
     * 不支持大图Tile块Level优化计算的设备,Lijing-A的三个机型,加 Avatar L4,L5(23305, 23111)机型,
     * Avatar B4,B5(23318, 24231, 24232, 24031, 24234)机型, Alpha L4机型
     */
    val DISABLE_TILE_LEVEL_OPTIMIZATION_DEVICES: List<String> by lazy {
        listOf(
            "PJS110",
            "PJU110",
            //23111
            "PKD110",
            "CPH2617",
            "CPH2631",
            //23305
            "CPH2665",
            //23318
            "CPH2669",
            //24231
            "CPH2681",
            //24232
            "CPH2683",
            //24234
            "CPH2693",
            //23319
            "CPH3669",
            //23311, 23312, 23313
            "CPH2641",
            //24031
            "PKL110",
            //24281, 24282, 24283, 24284
            "CPH2711",
            //24312, 24313, 24314
            "CPH2735",
            //24053
            "PKW110",
            //24054
            "PKW120",
            //24311
            "CPH2733",
            //24315
            "CPH2751",
            //24316
            "CPH2753"
        )
    }

    /**
     * 8650、dx3、dx4平台支持25M编辑，其他平台依然保持4K
     * DX-3(MT6989)
     * DX-4(MT6991)
     */
    val EDIT_25M_LEVEL_WHITELIST: List<String> by lazy {
        listOf(
            "SM8650",
            //23926/23976 原本是SM8650，后来改名了
            "SM8650AC",
            "SM8750",
            "MT6989",
            //cayman项目由MT6989改名成MT6989T
            "MT6989T",
        ).plus(DX4_PLATFORM_LIST)
    }

    /**
     * DX4平台
     */
    val DX4_PLATFORM_LIST: List<String> by lazy {
        listOf(
            "MT6991"
        )
    }

    /**
     * 支持点阵动效的平台
     */
    val SUPPORT_DOT_MATRIX_EFFECT_WHITELIST: List<String> by lazy {
        listOf(
            "MT6835T",
            "SM6225",
            "MT6835",
            "MT6769"
        )
    }

    /**
     * 针对“ro.product.first_api_level” 是Android V的机器，即Android V以来的PDT项目，默认支持抠图。如果某个项目不支持，使用黑名单控制
     */
    val NOT_SUPPORT_LNS_PHONE_MODEL_BLACKLIST: List<String> by lazy {
        listOf()
    }

    /**
     * AMG-4G、A-L5轻量OS回落支持抠图2.0需求的机型名称
     */
    val SUPPORT_LNS_LOS_PHONE_MODEL_WHITELIST: List<String> by lazy {
        listOf(
            "CPH2687",
            "PKD110",
            "RMX3940",
            "RMX3941",
            //RMX3942更名
            "RMX3953",
            "RMX3943",
            "RMX3944",
            "RMX3945",
            "RMX3946",
            "RMX3960",
            "RMX3962",
            "RMX5020",
            //轻量OS回落支持抠图2.0:Alpha-H、Alpha-M、Alpha-L4、Alpha-L5、Milkyway S3-4G、Milkyway S3-5G
            "PKP110",
            "CPH2705",
            "PKQ110",
            "CPH2721",
            "CPH2711",
            "PKV110",
            "CPH2695",
            "CPH2701",
            "CPH2699",
            //dongfeng25新轻量低
            "RMX5078",
            "RMX5074",
            //Fiji 轻量平板
            "OPD2417",
            "OPD2419",
            "OPD2420",
            //Rado EF
            "RMX3944",
            //AB4、AB5支持抠图
            "CPH2725",
            "CPH2727",
            "PKW110",
            "PKW120",
            "CPH2733",
            "CPH2735",
            //RADO系列新增
            "RMX3949",
            "RMX3948",
            "RMX3963",
        )
    }

    /**
     * 最佳表情优化支持机型白名单
     */
    val AI_BEST_TAKE_DEVICES_WHITELIST: List<String> by lazy {
        listOf(
            "CPH2739",
            "CPH2737",
            "CPH2743",
            "PKZ110",
            "PLA110",
            "PKX110",
            "PLC110",
            "PLF110",
            "PLL110",
            "PLN110",
            "PLE110",
            "PLM110"
        )
    }

    /**
     * AI 构图白名单
     */
    val REALME_AI_COMPOSITION_WHITELIST: List<String> by lazy {
        listOf(
            // Pista
            "RMX5010",
            // Piloti
            "RMX5090",
            // Targa
            "RMX6688",
            // Cayman-A
            "RMX5060",
            // BRZ
            "RMX5080",
            // Divo
            "RMX3800",
            // Bale-A
            "RMX3850",
            // Bale-B
            "RMX3852",
            // Enzo
            "RMX3888",
            // Zonda
            "RMX3820",
            "RMX3823",
            // Casio-Y
            "RMX5105",
            // Neutron
            "RMX5100",
            // Cayman-C
            "RMX5062"
        )
    }

    /**
     * 超清导出支持机型白名单
     */
    val OLIVE_EXPORT_DEVICES_WHITELIST: List<String> by lazy {
        listOf(
            "OPD2401",
            "OPD2402",
            "OPD2403",
            "OPD2404",
            "OPD2409",
            "OPD2411",
            "OPD2413",
            "OPD2415",
        )
    }

    /**
     * 支持AI 去眩光的白名单
     */
    val SUPPORT_AI_DEGLARE: List<String> by lazy {
        listOf(
            //Cayman-B
            "RMX5061",
            //BRZ
            "RMX5085",
            //Neutron
            "RMX5101",
            "RMX5116",
            //Casio-Y
            "RMX5106",
            //Chopard
            "RMX5111",
        )
    }

    /**
     * 支持超规格4k的机型名单
     */
    val OVER_4K_VIDEO_DEVICE_WHITELIST: List<String> by lazy {
        listOf(
            "PLG120"
        )
    }
}