/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ConfigTree
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/4/26
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/4/26      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.config.nodes

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.generated.ConfigNodeGenerator
import com.oplus.gallery.framework.abilityapt.annotations.ConfigNode

/**
 * 配置树是一个抽象的概念。由于配置是按照功能树进行分类管理的，因此配置树类似一颗功能树的结构。
 *
 * 它的特点是：
 * 1. 所有的父节点都是功能类别，有一级、二级、三级...
 * 2. 所有的叶子节点都是具体的一个个配置项
 */
internal class ConfigTree {

    /**
     * 树的根节点，可以有多个。比如虚拟出来的common、business节点
     */
    private var rootNodes: List<ConfigNode>? = null

    /**
     * 初始化配置节点树
     */
    fun initTrees() {
        GTrace.trace("$TAG.initTrees") {
            val startTime = System.currentTimeMillis()
            nodeDataMap = generateConfigTreeData()
            rootNodes = getRoots(nodeDataMap.values)
            GLog.d(TAG, LogFlag.DL) { "initTrees: cost = ${GLog.getTime(startTime)}" }
        }
    }

    /**
     * 根据节点ID查找节点对象
     */
    fun findNode(id: String): ConfigNode? {
        return nodeDataMap[id]
    }

    /**
     * 根据节点的全路径KEY查找节点
     *
     * @param qualifiedKey
     * @return
     */
    fun findNodeWithQualifiedKey(qualifiedKey: String): ConfigNode? {
        qualifiedKeyMap[qualifiedKey]?.let {
            return it
        }
        rootNodes?.forEach { rootNode ->
            if (rootNode.qualifiedKey == qualifiedKey) {
                qualifiedKeyMap[qualifiedKey] = rootNode
                return rootNode
            }
            // 从孩子节点找
            val ret = findNodeWithQualifiedKeyInChildren(qualifiedKey, rootNode)
            // 找到后退出循环
            ret?.let {
                qualifiedKeyMap[qualifiedKey] = it
                return ret
            }
        }
        return null
    }

    /**
     * 获取指定节点下的所有叶子节点, 可用于获取所有配置项，用于扩展组请求
     *
     * @param parent 父节点
     * @return 叶子节点
     */
    fun getLeaves(parent: ConfigNode): List<ConfigNode> {
        val leaves: MutableList<ConfigNode> = ArrayList()
        fillLeaf(parent, leaves)

        return leaves
    }

    /**
     * 生成configTree，父子关系已经建立
     */
    private fun generateConfigTreeData(): MutableMap<String, ConfigNode> {
        return ConfigNodeGenerator.generateNodes() as MutableMap<String, ConfigNode>
    }

    private fun findNodeInChildren(id: String, parentNode: ConfigNode): ConfigNode? {
        // 去子节点搜索
        parentNode.children?.forEach { child ->
            if (id == child.id) {
                return child
            }
            val ret = findNodeInChildren(id, child)
            if (ret != null) {
                return ret
            }
        }
        return null
    }

    private fun findNodeWithQualifiedKeyInChildren(qualifiedKey: String, parentNode: ConfigNode): ConfigNode? {
        // 去子节点搜索
        parentNode.children?.forEach { child ->
            if (qualifiedKey == child.qualifiedKey) {
                return child
            }
            val ret = findNodeWithQualifiedKeyInChildren(qualifiedKey, child)
            if (ret != null) {
                return ret
            }
        }
        return null
    }

    /**
     * 根据所有树节点列表，生成含有所有树形结构的列表
     *
     * @param nodes 节点数据
     * @return 树形结构列表
     */
    private fun getRoots(nodes: Collection<ConfigNode>): List<ConfigNode> {
        val roots: MutableList<ConfigNode> = ArrayList()
        nodes.forEach { node ->
            if (node.root()) {
                roots.add(node)
            }
        }
        return roots
    }

    /**
     * 将parent的所有叶子节点填充至叶子节点列表中
     *
     * @param parent 父节点
     * @param leaves  叶子节点列表
     */
    private fun fillLeaf(parent: ConfigNode, leaves: MutableList<ConfigNode>) {
        val children: MutableList<ConfigNode>? = parent.children
        // 如果节点没有子节点则说明为叶子节点
        if (children.isNullOrEmpty()) {
            leaves.add(parent)
            return
        }
        // 递归调用子节点，查找叶子节点
        for (child in children) {
            fillLeaf(child, leaves)
        }
    }

    private companion object {
        var nodeDataMap = emptyMap<String, ConfigNode>()
        var qualifiedKeyMap = mutableMapOf<String, ConfigNode>()
        private const val TAG = "ConfigTree"
    }
}