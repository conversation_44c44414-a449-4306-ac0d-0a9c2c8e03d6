/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OtherSystemStorage
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/5/6
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		            2022/5/6      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.config.storage

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.PackageManager.FEATURE_STRONGBOX_KEYSTORE
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaCodecInfo.CodecCapabilities
import android.media.MediaCodecList
import android.media.MediaFormat
import android.os.Build
import android.os.Process
import android.provider.MediaStore
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import com.oplus.aiunit.core.data.UnitState
import com.oplus.aiunit.core.data.UnitState.STATE_AVAILABLE
import com.oplus.aiunit.core.data.UnitState.STATE_AVAILABLE_AND_NEW_DOWNLOAD
import com.oplus.aiunit.core.data.UnitState.STATE_AVAILABLE_INTERNET
import com.oplus.aiunit.core.data.UnitState.STATE_AVAILABLE_LOCAL
import com.oplus.aiunit.core.data.UnitState.STATE_UNAVAILABLE_NO_INTERNET
import com.oplus.aiunit.vision.util.OcrCompatUtil
import com.oplus.coreapp.appfeature.AppFeatureProviderUtils
import com.oplus.framework.flags.Flags
import com.oplus.gallery.addon.content.OplusFeatureConfigManagerWrapper
import com.oplus.gallery.addon.flags.FlagsWrapper
import com.oplus.gallery.addon.graphics.display.HdrSdrManagerOs13
import com.oplus.gallery.addon.multiuser.OplusMultiUserManagerWrapper
import com.oplus.gallery.addon.os.customize.OplusCustomizeRestrictionManagerWrapper
import com.oplus.gallery.addon.os.oplusdevicepolicy.OplusDevicepolicyManagerWrapper
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.addon.utils.OSVersionUtils.OPLUS_OS_15_0_2
import com.oplus.gallery.addon.utils.OSVersionUtils.OPLUS_OS_16_0_0
import com.oplus.gallery.addon.view.EdrUtilsWrapper
import com.oplus.gallery.addon.view.EdrUtilsWrapper.DEFAULT_HDR_VISION_EDR_VERSION
import com.oplus.gallery.business_lib.BusinessLibHelper.PREFERENCE_USE_OPEN_NETWORK
import com.oplus.gallery.business_lib.cast.CastSwitcherManager
import com.oplus.gallery.business_lib.function.FunctionSwitcherConfig
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisSwitcherManager
import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig
import com.oplus.gallery.business_lib.http.cloudparameter.base.GroupResponseBean
import com.oplus.gallery.business_lib.model.config.allowlist.AndesSearchRusConfig
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.DOLBY_VIDEO_TYPE
import com.oplus.gallery.business_lib.model.data.config.PhotoPresentType
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureUtils
import com.oplus.gallery.business_lib.photoeditor.AiEliminateSwitcherManager
import com.oplus.gallery.business_lib.photoeditor.BlurSwitchParser
import com.oplus.gallery.business_lib.photopage.AIGraffitiSwitchManager
import com.oplus.gallery.business_lib.photopage.AihdSwitchManager
import com.oplus.gallery.business_lib.photopage.GroupPhotoSwitchManager
import com.oplus.gallery.business_lib.photopage.LiftAndShiftParamManager
import com.oplus.gallery.business_lib.photopage.LnSSwitchParser
import com.oplus.gallery.business_lib.photopage.OliveParamManager
import com.oplus.gallery.business_lib.photopage.PhotoThumbLineSwitchHelper
import com.oplus.gallery.business_lib.travel.TravelSupportCheckerImpl
import com.oplus.gallery.foundation.codec.CodecMediaType
import com.oplus.gallery.foundation.codec.MediaCodecDetector
import com.oplus.gallery.foundation.hdrtransform.HdrTransformFactory
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_NAME_ACCOUNT
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_NAME_TRANSLATE
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_OCR_SCANNER
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
import com.oplus.gallery.foundation.util.brandconfig.SeriesType
import com.oplus.gallery.foundation.util.brandconfig.SeriesUtils
import com.oplus.gallery.foundation.util.cpu.CPUInfoUtils
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.DEFAULT_LEVEL
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.KEY_OPLUS_ANIM_LEVEL
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.PerformanceLevel
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.animationLevel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_AI_COMPOSITION
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_BIG_CACHE_MODE_LEVEL
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_DEBLUR
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_DEREFLECTION
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_FORCE_DISABLE_120FPS_PLAYBACK
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_GIF_SYNTHESIS
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_INTERACTIVE_SEGMENT
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OCS_AUTH
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OLIVE_EDIT_TEST_MODEL
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OLIVE_EXTRACT_UHDR_FRAME
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OLIVE_FRAME_FILL
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OLIVE_SLIDE_PLAY
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SECRET
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_UHDR_EDIT
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_VIDEO_EXTRACT_OLIVE
import com.oplus.gallery.foundation.util.debug.GProperty.FEATURE_FORCE_DISABLE
import com.oplus.gallery.foundation.util.debug.GProperty.FEATURE_FORCE_ENABLE
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getBooleanSafe
import com.oplus.gallery.foundation.util.ext.getStringArraySafe
import com.oplus.gallery.foundation.util.ext.getStringSafe
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil
import com.oplus.gallery.foundation.util.systemcore.ActivityManagerUtils
import com.oplus.gallery.foundation.util.systemcore.ContainerManagerUtils
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.systemcore.LowMemUtils
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils
import com.oplus.gallery.foundation.util.systemcore.UserManagerUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.foundation.util.version.AppVersionUtils
import com.oplus.gallery.framework.abilities.BuildConfig
import com.oplus.gallery.framework.abilities.codec.CodecHelper
import com.oplus.gallery.framework.abilities.config.IConfigListener
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.R
import com.oplus.gallery.framework.abilities.config.args.ConfigExtraArgs
import com.oplus.gallery.framework.abilities.config.initial.CameraConfigInitializer
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Brightness
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.CloudSync.SharePage
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec.IS_DOLBY_DECODE_DEVICE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec.IS_DOLBY_ENCODE_DEVICE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Codec.IS_DOLBY_GPU_DECODE_DEVICE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.ColorManagement
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_ELIMINATE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_ELIMINATE_INPAINTING_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_BLUR_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_REFLECTION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FLAW_RECOGNIZE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IMAGE_QUALITY_ENHANCE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_AI_ELIMINATE_INPAINTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_AI_ELIMINATE_SEGMENTATION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_SUPPORT_PASSERS_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.PASSERBY_ELIMINATE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.STICKER_QUANTITY_LIMIT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.ScreenTranslate
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.SuperText
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.VideoEditor
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DEVICE_SUPPORT_AI_EXPRESSION_OPT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PagesCommon.BIG_CACHE_MODE_LEVEL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.MotionEvent
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Pages.PhotoPage.Tile.DEFAULT_TILE_MAX_COUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_DE_BLUR
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_DE_REFLECTION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.Permission.IS_AUTHORIZE_ACCESS_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.CPU_INFO
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.CPU_INFO_PART_NUMBER
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_ONEPLUS_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_BELARUS
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_RUSSIA
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_TURKEY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_TABLET
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.PHONE_MODEL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.REGION_MARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.ViewBehavior.PHOTO_VIEW_DEFAULT_PRESENT_TYPE
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilityapt.annotations.ConfigNode
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppConstants.Capacity.MEM_16G
import com.oplus.gallery.standard_lib.app.AppConstants.Capacity.MEM_6G
import com.oplus.gallery.standard_lib.app.AppConstants.Capacity.MEM_8G
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.ocs.camera.ipuapi.IPUClient
import com.oplus.supertext.core.utils.VersionUtils
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import java.util.regex.Pattern
import kotlin.math.floor
import com.oplus.gallery.foundation.util.R as LibUtilR

/**
 * 有些配置可能存储在系统的其他未知，由于比较零散，避免类爆炸，因此放在OtherSystemStorage统一处理，当然可以根据情况扩展IStorage，类似[SystemPropStorage]的做法。
 */
@Suppress("LargeClass")
internal class OtherSystemStorage(private val context: Context, private val configAbility: IConfigSetterAbility) : IStorage {

    private val keyDisplaySupportWCGV2 by lazy { context.getStringSafe(LibUtilR.string.sys_feature_display_support_wcg_v2) }
    private val keyBacklightOptimizeQ by lazy { context.getStringSafe(LibUtilR.string.sys_feature_optimize_q) }
    private val keyCameraBrightenUniformity by lazy { context.getStringSafe(LibUtilR.string.sys_feature_camera_brighten_uniformity) }
    private val keySupportHighBrightness by lazy { context.getStringSafe(LibUtilR.string.feature_support_hight_brightness) }
    private val keySupportLHdrOnlyDimming by lazy { context.getStringSafe(LibUtilR.string.sys_oplus_feature_lhdr_only_dimming) }
    private val keySupportAutoBrightAnimationQ by lazy { context.getStringSafe(LibUtilR.string.sys_feature_support_bright_animation_q) }
    private val keyIsDisablePhoneClone by lazy { context.getStringSafe(LibUtilR.string.app_feature_disable_phone_clone) }
    private val keyCMCCVersionNameR by lazy { context.getStringSafe(LibUtilR.string.sys_feature_cmcc_version_name_r) }
    private val keyCMCCVersionNameQ by lazy { context.getStringSafe(LibUtilR.string.sys_feature_cmcc_version_name_q) }
    private val keyMtkDisablePQ by lazy { context.getStringSafe(LibUtilR.string.sys_feature_mtk_pq_disable_q) }
    private val keyChildrenModeOn by lazy { context.getStringSafe(LibUtilR.string.attr_children_mode_on) }
    private val keyIsCompatOldMainBodyName by lazy { context.getStringSafe(LibUtilR.string.oplus_companyname_not_support) }
    private val keyVideoEditorUrl by lazy { context.getStringSafe(LibUtilR.string.app_feature_videoeditor_url) }
    private val keyStickerUrl by lazy { context.getStringSafe(LibUtilR.string.app_feature_sticker_url) }
    private val keyAihdUrl by lazy { context.getStringSafe(LibUtilR.string.app_feature_aihd_url) }
    private val keySupportAihd by lazy { context.getStringSafe(LibUtilR.string.app_feature_aihd_support) }
    private val keySupportAiFaceHd by lazy { context.getStringSafe(LibUtilR.string.app_feature_ai_face_hd_support) }
    private val keySupportAIGraffiti by lazy { context.getStringSafe(LibUtilR.string.app_feature_ai_graffiti_support) }
    private val keyIsTablet by lazy { context.getStringSafe(LibUtilR.string.sys_feature_is_tablet) }
    private val keyIsFold by lazy { context.getStringSafe(LibUtilR.string.sys_feature_is_fold) }
    private val keyIsFoldRemapDisplayDisabled by lazy { context.getStringSafe(LibUtilR.string.sys_feature_is_fold_remap_display_disabled) }
    private val keyPtcNfcJsonAnimation by lazy { context.getStringSafe(LibUtilR.string.app_feature_ptc_nfc_json_animation) }
    private val keySupportSecureElementEse2 by lazy { context.getStringSafe(LibUtilR.string.oplus_hardware_secure_element_ese2) }
    private val keySupportNFCSecure by lazy { context.getStringSafe(LibUtilR.string.oplus_secure_nfc_ese) }
    private val isRegionCN by lazy { configAbility.getBooleanConfig(IS_REGION_CN) ?: false }
    private val keyQuickAnimDurationShot by lazy { context.getStringSafe(LibUtilR.string.app_feature_quick_anim_duration_shot) }
    private val keyProductLightLow by lazy { context.getStringSafe(LibUtilR.string.sys_feature_product_light) }
    private val keyProductLightHigh by lazy { context.getStringSafe(LibUtilR.string.sys_feature_product_light_h) }
    private val keySupportBorderCaption by lazy { context.getStringSafe(LibUtilR.string.app_feature_border_caption_support) }
    private val keySupportRmDeblur by lazy { context.getStringSafe(LibUtilR.string.app_feature_rm_ai_deblur_support) }
    private val keyIsRmDeblurCloudType by lazy { context.getStringSafe(LibUtilR.string.app_feature_rm_ai_deblur_is_cloud) }
    private val keySupportEdrListener by lazy { context.getStringSafe(LibUtilR.string.property_persist_sys_feature_support_edrlistener) }
    private val keySupportUHdr by lazy { context.getStringSafe(LibUtilR.string.property_persist_sys_feature_support_uhdr) }
    private val keyOpenImageBrightnessEnhance by lazy { context.getStringSafe(LibUtilR.string.app_feature_open_image_brightness_enhance) }

    override fun read(configNode: ConfigNode, defValue: Int): Int? {
        return when (configNode.id) {
            SystemInfo.API_LEVEL -> Build.VERSION.SDK_INT
            SystemInfo.BRIGHTEN_VERSION_LOCAL_HDR -> brightenVersionLocalHdr
            SystemInfo.BRIGHTEN_VERSION_DOLBY -> brightenVersionDolby
            DEFAULT_TILE_MAX_COUNT -> tileMaxCount
            STICKER_QUANTITY_LIMIT -> stickerQuantityLimit
            BIG_CACHE_MODE_LEVEL -> bigCacheModeLevel
            PHOTO_VIEW_DEFAULT_PRESENT_TYPE -> defaultPhotoPresentType
            else -> null
        }
    }

    override fun read(configNode: ConfigNode, defValue: Long): Long? {
        return when (configNode.id) {
            Brightness.IMAGE_SCREEN_BURN_IN_WARN_DURATION -> IMAGE_SCREEN_BURN_IN_WARN_DELAY_TIME
            Brightness.VIDEO_SCREEN_BURN_IN_WARN_DURATION -> VIDEO_SCREEN_BURN_IN_WARN_DELAY_TIME
            MotionEvent.LNS_SHORT_LONG_PRESS_TIME_OUT_IN_MS -> liftAndShiftShortLongPressTimeOutInMs
            ConfigID.Business.Pages.PhotoPage.Olive.OLIVE_PLAYED_THRESHOLD_TIME_FOR_LNS_SHOW_IN_MS -> olivePlayedThresholdTimeForLnsShowInMs
            ConfigID.Business.Pages.PhotoPage.Olive.OLIVE_CLIP_PLAY_POSITION_MAX_LIMIT_IN_MS -> oliveClipPlayPositionMaxLimitInMs
            else -> null
        }
    }

    override fun read(configNode: ConfigNode, defValue: Float): Float? {
        return null
    }

    @Suppress("LongMethod")
    override fun read(configNode: ConfigNode, defValue: Boolean): Boolean? {
        return when (configNode.id) {
            Codec.IS_SINGLE_CODEC_DEVICE -> isSingleCodecDevice
            Codec.IS_SINGLE_4K_DOLBY_CODEC_DEVICE -> isSingle4kDolbyCodecDevice
            Codec.IS_SINGLE_DOLBY_CODEC_DEVICE -> isSingleDolbyCodecDevice
            Codec.IS_SINGLE_4K_CODEC_DEVICE -> isSingle4KCodecDevice
            ColorManagement.IS_DISPLAY_SUPPORT_WCG_V2 -> hasSystemFeature(keyDisplaySupportWCGV2)
            SharePage.IS_SHARE_GUIDE_USE_FOLD_PTC_NFC_ANIM -> isShareGuideUsePtcNfcAnim()
            SystemInfo.IS_SUPPORT_BACKLIGHT_OPTIMIZE -> hasSystemFeature(keyBacklightOptimizeQ)
            SystemInfo.IS_SUPPORT_BRIGHTEN_UNIFORMITY -> isSupportBrightenUniformity
            SystemInfo.IS_SUPPORT_AUTO_BRIGHTNESS_ANIMATION -> hasSystemFeature(keySupportAutoBrightAnimationQ)
            SystemInfo.IS_SUPPORT_HIGH_BRIGHTNESS -> hasSystemFeature(keySupportHighBrightness)
            SystemInfo.IS_SUPPORT_TILE_LEVEL_OPTIMIZATION -> isSupportTileLevelOptimization
            SystemInfo.IS_SHOW_SPECIAL_COMPANY_NAME -> isShowSpecialCompanyName
            SystemInfo.IS_CMCC -> hasAppFeature(context, keyCMCCVersionNameR, keyCMCCVersionNameQ)
            SystemInfo.IS_SOFTBANK -> configAbility.getStringConfig(SystemInfo.OPERATOR) == VALUE_OPERATOR_SOFTBANK
            SystemInfo.IS_MTK_PLATFORM -> isMtkPlatform
            SystemInfo.IS_QUALCOMM_PLATFORM -> isQualcommPlatform
            SystemInfo.IS_PRIMARY_USER -> isPrimaryUser
            SystemInfo.IS_CONTAINER_USER -> isContainerUser
            SystemInfo.IS_CHILDREN_MODE -> isChildrenMode
            SystemInfo.IS_USER_UNLOCKED -> isUserUnlocked
            SystemInfo.IS_MULTI_SYSTEM_USER -> isMultiSystemUser
            SystemInfo.IS_TABLET -> isTablet
            SystemInfo.IS_FOLD -> isFold
            SystemInfo.IS_FOLD_REMAP_DISPLAY_DISABLED -> isFoldRemapDisplayDisabled
            SystemInfo.IS_SUPPORT_DOLBY_GPU_DECODE -> isSupportDolbyGPUDecode
            SystemInfo.IS_FOLD_REMAP_DISPLAY_ENABLED -> isFold && isFoldRemapDisplayDisabled.not()
            SystemInfo.IS_MULTI_DISPLAY -> isFold && isFoldRemapDisplayDisabled
            SystemInfo.IS_SUPPORT_DOLBY_DECODE -> isSupportDolbyDecode
            SystemInfo.IS_SUPPORT_DOLBY_ENCODE -> isSupportDolbyEncode
            SystemInfo.IS_SUPPORT_DOLBY_ENCODE_ACCELERATE -> isSupportDolbyEncodeAccelerate
            SystemInfo.IS_SUPPORT_HLG_ENCODE -> isSupportHlgEncode
            SystemInfo.IS_SUPPORT_VELOCITYTRACKER_STRATEGY_IMPULSE -> isSupportVelocityTrackerStrategyImpulse
            SystemInfo.IS_REGION_CN -> isDomestic
            SystemInfo.IS_PRODUCT_LIGHT_LOW -> isProductLightLow
            SystemInfo.IS_PRODUCT_LIGHT_HIGH -> isProductLightHigh
            SystemInfo.IS_PRODUCT_LIGHT -> isProductLight
            SystemInfo.GET_MEDIA_STORE_CAMERA_QUICK_URI -> isSupportMediaStoreCameraQuickUri
            FeatureSwitch.FEATURE_IS_SUPPORT_BACKLIGHT_OPTIMIZE -> isBackLightOptimize
            FeatureSwitch.FEATURE_IS_SUPPORT_HIGH_BRIGHTNESS -> isSupportHighBrightness
            FeatureSwitch.FEATURE_IS_PHONE_CLONE_DISABLED -> {
                AppFeatureProviderUtils.isFeatureSupport(
                    context.contentResolver,
                    keyIsDisablePhoneClone
                )
            }

            FeatureSwitch.FEATURE_IS_SUPPORT_TIMELINE_PAGE_MULTI_VIEW_SWITCH -> isTimelinePageMultiViewSwitchSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_ELIMINATE_PEN -> isEliminatePenSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_BLURRING -> isBlurringSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_MULTIMODAL -> isMultiModalSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_OCREMBEDDING -> isOcrEmbeddingSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_CAPTION -> isCaptionSupported
            FeatureSwitch.FEATURE_PRIVACY_IS_SUPPORT_CAPTION -> isPrivacyCaptionSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_LABEL_GRAPH -> isLabelGraphSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_ENGLISH_LABEL_GRAPH -> isEnglishLabelGraphSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_DOC -> isSuperTextV1ConvertDocSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_PPT_DOC -> isSuperTextV1ConvertPptDocSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_WORD_DOC -> isSuperTextV1ConvertWordDocSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_TEXT_V1_CONVERT_EXCEL_DOC -> isSuperTextV1ConvertExcelDocSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_CAST_ADAPTION -> isCastAdaptionSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_CAST_PRESENTATION -> isPresentationCastSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_GIF_SYNTHESIS -> isGifSynthesisSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_MAP_PAGE -> isMapPageSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_PRIVACY_PROTECTION_ASSISTANT -> isPrivacyProtectionAssistantSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_ID_PHOTO -> isAiIdPhotoSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_LOCAL_HDR -> isLocalHdrSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_LOCAL_HDR_ONLY -> isLocalHdrSupported && isUltraHdrSupported.not()
            FeatureSwitch.FEATURE_IS_SUPPORT_PORTRAIT_BLUR_HDR_EDIT -> isPortraitBlurHdrEditSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_ULTRA_HDR -> isUltraHdrSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_UHDR_EDIT -> isUHdrEditSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_UHDR_TRANSFORM -> isUHdrTransformSupport
            FeatureSwitch.FEATURE_IS_SUPPORT_HDR_COMBINE_DISPLAY_EDIT -> isHDRCombineDisplaySupported
            FeatureSwitch.FEATURE_IS_SUPPORT_LOCAL_HDR_EDIT -> isLocalHdrEditSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_RAW_EDIT -> isRawEditSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_LOCAL_HDR_ON_SUB_DISPLAY -> isLocalHdrSupportedOnSubDisplay
            FeatureSwitch.FEATURE_IS_SUPPORT_BRIGHTEN_COMPARE -> isBrightenCompareSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_DOLBY_BRIGHTEN -> isDolbyBrightenSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_HDR_VISION_BRIGHTEN -> isHdrVisionSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_EDIT_VIDEO_HDR_BRIGHTEN -> isEditVideoHdrBrightenSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM -> isPrimaryUser && !isContainerUser && !isChildrenMode
            FeatureSwitch.FEATURE_IS_SUPPORT_ALBUM_SAFE_BOX -> isPrimaryUser && !isContainerUser && !isChildrenMode && isSupportAlbumEncryption()
            FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE -> !isGalleryShareDisabled()
            FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_OSHARE -> !isOshareDisabled()
            FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX -> !isSafeBoxDisabled()
            FeatureSwitch.FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE -> isOneTouchShareSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_HDR -> isSupportHDR
            FeatureSwitch.FEATURE_IS_SUPPORT_PORTRAITBLUR -> isPortraitBlurSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_PORTRAIT_BLUR_AFTER_OS16 -> isSupportPortraitBlurAfterOS16
            FeatureSwitch.FEATURE_IS_SUPPORT_PORTRAIT_BLUR_BEFORE_OS16 -> isSupportPortraitBlurBeforeOS16
            FeatureSwitch.FEATURE_IS_SUPPORT_HASSEL_WATERMARK -> isHasselWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_HASSEL_DEVICE -> isSupportHasselDevice
            FeatureSwitch.FEATURE_IS_SUPPORT_LONELY_PLANET_WATERMARK -> isLonelyPlanetWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_WATERMARK_MASTER -> isSupportAiWatermarkMaster
            FeatureSwitch.FEATURE_IS_CAMERA_CONFIG_NEW_PROJECT -> isCameraConfigWatermarkNew
            FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK -> isPhotoEditorWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_THUMB_LINE -> isSupportPhotoThumbLine
            FeatureSwitch.FEATURE_IS_SUPPORT_SENIOR_PICKED -> isSupportSeniorPicked
            FeatureSwitch.FEATURE_IS_SUPPORT_SIDE_PANE -> isSupportSidePane
            FeatureSwitch.FEATURE_IS_SUPPORT_DISABLE_HDR_IMAGE_LAYER -> isSupportDisableLocalHdrLayer
            FeatureSwitch.FEATURE_IS_SUPPORT_HDR_IMAGE_ONLY_DIMMING -> isSupportLHDROnlyDimming
            FeatureSwitch.FEATURE_IS_SUPPORT_GOOGLE_CLOUD_LOCAL -> isSupportGoogleCloudLocal
            FeatureSwitch.FEATURE_IS_SUPPORT_GOOGLE_CLOUD -> isSupportGoogleCloudLocal
            FeatureSwitch.FEATURE_IS_SELL_MODE -> isSellMode()
            FeatureSwitch.FEATURE_IS_SUPPORT_SUPER_ENCRYPTION -> isSupportSuperEncryption
            FeatureSwitch.FEATURE_IS_SUPPORT_PRIVACY_WATERMARK -> isPrivacyWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_SPRING_FESTIVAL_WATERMARK -> isSpringFestivalWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_STREET_WATERMARK -> isStreetWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_COLOR_WATERMARK -> isColorWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_CSHOT_CONTINUATION -> isSupportCshotContinuation
            FeatureSwitch.FEATURE_IS_SUPPORT_LNS -> isLnSSupported || isSupportLnsLOsPhoneModel
            FeatureSwitch.FEATURE_IS_SUPPORT_LNS_FOR_OLIVE -> isSupportLnsForOLive
            FeatureSwitch.FEATURE_IS_SUPPORT_DOLBY_VIDEO_HARDWARE_DECODER -> isSupportDolbyVideoHardwareDecoder
            FeatureSwitch.FEATURE_IS_SUPPORT_IPU_WATERMARK -> isIPUWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_IPU_REAR_BLUR -> isIPURearBlurSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_IPU_FRONT_BLUR -> isIPUFrontBlurSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_FRAME_WATERMARK -> isFrameWatermarkSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_USE_OPERATE_RATE -> isSupportUseOperateRate
            FeatureSwitch.FEATURE_IS_SUPPORT_ANDES_SEARCH -> isSupportAndesSearch
            FeatureSwitch.FEATURE_IS_SUPPORT_QUICK_PHOTO_DELETE -> isQuickPhotoDeleteSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_QUICK_PHOTO_PUBLIC -> isQuickPhotoPublicSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_AUTHORIZING_ON_SECONDARY_DISPLAY -> isSupportAuthorizingOnSecondaryDisplay
            FeatureSwitch.FEATURE_IS_SUPPORT_GROUP_PHOTO -> isSupportGroupPhoto
            FeatureSwitch.FEATURE_IS_SUPPORT_RM_IMAGE_QUALITY_ENHANCE -> isSupportAihd || isSupportAiFaceHd
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_FACE_HD -> isSupportAiFaceHd
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_GRAFFITI -> isSupportAiGraffiti
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEGLARE -> isSupportAIGlare
            FeatureSwitch.FEATURE_IS_SUPPORT_AIHD_PAGE -> isSupportAihdPage
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_HEIF -> isSupportOliveHeif
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_COVER_FRAME_ENHANCER -> isSupportOliveCoverFrameEnhancer
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_FRAME_FILLTER -> isSupportOliveFrameFiller
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_SLIDE_PLAY -> isSupportOliveSlidePlay
            FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM -> isSupportPhotoPageHdrVideoOliveTransform
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_FILTER -> isSupportAiFilter
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_ELIMINATE -> isAiEliminateSupported
            FeatureSwitch.FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE -> isDeviceSupportAiEliminate
            FeatureSwitch.FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE -> isSupportImageQualityEnhance
            FeatureSwitch.FEATURE_IS_FUNC_DECRYPT_DEVICE -> isFuncDecryptDevice
            FeatureSwitch.FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE_RECOMMEND -> isSupportImageQualityEnhanceRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_FLAW_RECOMMEND -> isSupportFlawRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_PASSERBY_RECOMMEND -> isSupportPasserbyRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_DEBLUR_RECOMMEND -> isSupportDeblurRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_DEREFLECTION_RECOMMEND -> isSupportDeReflectionRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_BEST_TAKE_RECOMMEND  -> isSupportAIBestTakeRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_BEST_TAKE -> isSupportAIBestTake()
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_LIGHTING_RECOMMEND -> isSupportAilightingRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_LIGHTING -> isSupportAiLighting
            FeatureSwitch.FEATURE_IS_INTERACTIVE_SEGMENT_SUPPORT -> isInteractiveSegmentSupport
            FeatureSwitch.FEATURE_IS_QUICK_ANIM_DURATION_SHOT -> isQuickAnimDurationShot
            FeatureSwitch.FEATURE_IS_SUPPORT_OCS_AUTH -> isSupportOcsAuth
            FeatureSwitch.FEATURE_IS_SUPPORT_BORDER_CAPTION -> isSupportBorderCaption
            FeatureSwitch.FEATURE_IS_SUPPORT_EDR_LISTENER -> isSupportEdrListener
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE -> isSupportOLive
            FeatureSwitch.FEATURE_IS_SUPPORT_SAVE_OLIVE -> isSupportSaveOlive
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_SDR_TO_HDR -> isSupportOliveSDR2HDR
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_HDR_VIDEO -> isSupportOliveHdrVideo
            FeatureSwitch.FEATURE_IS_SUPPORT_LUMO_WATERMARK -> isSupportLumoWatermark
            FeatureSwitch.FEATURE_IS_NEW_DATE_TAKEN_PARSE_ENABLED -> isNewDateTakenParseEnable
            FeatureSwitch.FEATURE_IS_SUPPORT_EXPRESSION_OPT -> isAiSupportExpressionOpt
            FeatureSwitch.FEATURE_IS_OPEN_IMAGE_BRIGHTNESS_ENHANCE -> isSupportAloneBrightnessEnhance
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEREFLECTION -> isSupportDeReflection
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEBLUE -> isSupportDeblur
            FeatureSwitch.FEATURE_IS_SUPPORT_RM_AI_DEBLUE -> isSupportRmDeblur
            FeatureSwitch.FEATURE_IS_SUPPORT_INTELLI_FUNC_RECOMMEND -> isSupportIntelliFuncRecommend
            FeatureSwitch.FEATURE_IS_SUPPORT_SYSTEM_SETTING_SEARCH -> isSupportSystemSettingSearch
            FeatureSwitch.FEATURE_IS_SUPPORT_SYSTEM_SETTINGS_DISPLAY_INSERTION -> isSupportSystemSettingDisplayInsertion
            FeatureSwitch.FEATURE_IS_SUPPORT_SHOW_SECURITY_SHARE -> isSupportShowSecurityShare
            FeatureSwitch.FEATURE_IS_SUPPORT_25M_LEVEL -> isSupport25MLevel
            FeatureSwitch.FEATURE_IS_DX4_PLATFORM -> isDX4Platform
            FeatureSwitch.FEATURE_IS_SUPPORT_CAMERA_JPEG_EXIF_THUMBNAIL -> isCameraJpegExifThumbnailSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_CAMERA_PROXDR -> isCameraProxdrSuppoted
            FeatureSwitch.FEATURE_IS_SUPPORT_LNS_DOT_MATRIX_EFFECT -> isSupportDotMatrixEffect
            FeatureSwitch.FEATURE_IS_SUPPORT_10BIT_DECODE -> isMediaSupport10Bit
            FeatureSwitch.FEATURE_IS_SUPPORT_PROJECT_SAVE -> isSupportProjectSave
            FeatureSwitch.FEATURE_IS_SUPPORT_IPU_FILTER -> isSupportIpuFilter
            FeatureSwitch.FEATURE_IS_SUPPORT_IPU_BEAUTY -> isSupportIpuBeauty
            FeatureSwitch.FEATURE_IS_SUPPORT_GOOGLE_PASS_SCAN -> isSupportGooglePassScan
            FeatureSwitch.FEATURE_IS_SUPPORT_AC_VERIFY -> isSupportAcVerify
            FeatureSwitch.FEATURE_IS_SUPPORT_GOOGLE_LENS -> isSupportGoogleLens
            /**
             * 是否支持视频标签跑CPU而不走GPU
             * 此问题是为了修复BUG 7953659，分析原因是对应的GPU OpenCL库有问题，需要在此平台上走CPU而不走GPU
             * 目前仅7550和7150平台支持
             */
            FeatureSwitch.FEATURE_IS_SUPPORT_VIDEO_CLASSIFY_RUN_ON_CPU -> isSupportVideoClassifyRunOnCpu
            FeatureSwitch.FEATURE_IS_SUPPORT_AI_COMPOSITION -> isSupportAiComposition
            FeatureSwitch.FEATURE_IS_ENABLE_MENU_INFLATE_POSTPONE -> isEnableMenuInflatePostpone
            SuperText.GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED -> isSuperTextJumpOcrScannerSupported
            SuperText.GET_SUPER_TEXT_V2_SUPPORTED_SUSPEND -> isSuperTextV2Supported
            SuperText.GET_SUPER_TEXT_V1_OCR_SUPPORTED_SUSPEND -> isSuperTextV1OcrSupported
            ScreenTranslate.GET_TRANSLATE_SUPPORTED -> isTranslateSupported
            Pages.PhotoPage.Brighten.IS_SUPPORT_ADJUST_IMAGE_EDR_RATE -> isSupportAdjustImageEdrRate
            Pages.PhotoPage.Brighten.IS_SUPPORT_ADJUST_OLIVE_EDR_RATE -> isSupportAdjustOLiveEdrRate
            Codec.IS_ENABLE_IMAGE_REGION_DECODE -> isEnableImageRegionDecode
            SystemInfo.IS_SINGLE_SURFACEVIEW_DEVICE -> isSingleSurfaceViewDevice
            FeatureSwitch.FEATURE_IS_SUPPORT_RECYCLE_ALBUM_PASSWD -> isSupportRecycleAlbumPasswd
            FeatureSwitch.FEATURE_IS_SUPPORT_DELAY_HIDE_SEAMLESS_TRANSITION_BY_MODEL_100 -> isSupportDelayHideSeamlessTransition100
            FeatureSwitch.FEATURE_IS_SUPPORT_DELAY_HIDE_SEAMLESS_TRANSITION_BY_MODEL_1500 -> isSupportDelayHideSeamlessTransition1500
            FeatureSwitch.FEATURE_AI_BEST_TAKE_IS_SECRET -> FlagsWrapper.coloros1502ConfidentialZhuque()
            FeatureSwitch.FEATURE_IS_LOW_PERFORMANCE_DEVICE -> isLowPerformanceDevice
            Pages.PhotoPage.Animation.ANIMATION_QUICK_SHORT -> isPhotoQuickShortAnim
            Pages.PhotoPage.Animation.IS_SUPPORT_TRANSITION_RENDER_ANIM -> isSupportPhotoTransitionRenderAnim
            ConfigID.Common.UserProfile.ViewBehavior.VIDEO_AUTO_PLAY_DEFAULT_VALUE -> videoAutoPlayDefaultValue
            FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_EXTRACT_UHDR_FRAME -> isSupportOliveExtractUhdrFrame
            FeatureSwitch.FEATURE_IS_SUPPORT_VIDEO_EXTRACT_OLIVE -> isSupportVideoExtractOlive
            FeatureSwitch.FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK -> isSystemSupportPlaybackAt120Fps
            FeatureSwitch.FEATURE_IS_SUPPORT_VIDEO_WALLPAPER -> isSupportVideoWallpaper
            FeatureSwitch.FEATURE_IS_SUPPORT_THEME_CLASSIFY -> isSupportThemeClassify
            FeatureSwitch.FEATURE_IS_SUPPORT_STUDY_CLASSIFY -> isSupportStudyClassify
            FeatureSwitch.FEATURE_IS_SUPPORT_HEALTH_CLASSIFY -> isSupportHealthClassify
            FeatureSwitch.FEATURE_IS_SUPPORT_TRAVEL_COVER_SELECTION -> isSupportTravelCoverSelection
            FeatureSwitch.FEATURE_IS_SUPPORT_TRAVEL_CLASSIFY -> isSupportTravelClassify
            FeatureSwitch.FEATURE_IS_SUPPORT_PET_CLASSIFY -> isSupportPetClassify
            FeatureSwitch.FEATURE_IS_PRE_TRANSITION_DISABLED -> isPreTransitionDisabled
            FeatureSwitch.FEATURE_IS_CAMERA_SUPPORT_PREVIEW_ROTATE -> isCameraPreviewRotateSupported
            FeatureSwitch.FEATURE_IS_SUPPORT_OVER_4K_VIDEO -> isSupportOver4kVideo
            else -> null
        }
    }

    override fun read(configNode: ConfigNode, defValue: String?): String? {
        return when (configNode.id) {
            PhotoEditor.STICKER_URL -> AppFeatureProviderUtils.getString(context.contentResolver, keyVideoEditorUrl, "")
            VideoEditor.VIDEO_EDITOR_URL -> AppFeatureProviderUtils.getString(context.contentResolver, keyStickerUrl, "")
            PhotoEditor.EDITOR_AIHD_URL -> AppFeatureProviderUtils.getString(context.contentResolver, keyAihdUrl, "")
            PHONE_MODEL -> Build.MODEL
            else -> null
        }
    }

    /**
     * 带参方式，允许携带参数读取某个配置项
     * @param args 配置项查询参数
     */
    override fun readWithArgs(configNode: ConfigNode, args: ConfigExtraArgs, defValue: Any?): Any? {
        // configID和ConfigExtraArgs类型要能对应上
        return when {
            (configNode.id == Codec.PLATFORM_CODEC_CAPABILITY_QUERY) && (args is ConfigExtraArgs.CodecConfigExtraArgs) ->
                getCodecCapabilitiesByCodecConfig(args.codecType, args.isEncoder)
            else -> null
        }
    }

    /**
     * 是否支持账号核身校验(二次校验)
     *
     * 必要条件：内销 && 系统版本 ≥ OS13.1 && 账号APK版本 ≥ 9.0
     */
    private val isSupportAcVerify: Boolean by lazy {
        (configAbility.getBooleanConfig(IS_REGION_CN) == true)
            && OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_1)
            && AppVersionUtils.getVersionCode(ContextGetter.context, PACKAGE_NAME_ACCOUNT) >= VERSION_OPLUS_ACCOUNT_AT_LEAST
    }

    /**
     * 是否支持google lens
     */
    private val isSupportGoogleLens: Boolean by lazy {
        val propertyValue = GallerySystemProperties.get(BasicOsFeature.GOOGLE_LENS_IS_SUPPORT_KEY)
        val packageName = context.packageName
        // Google Lens调整：截屏在GOOGLE_LENS_IS_SUPPORT_KEY新增了包名，相册需要修改为contains
        (propertyValue.isNullOrEmpty().not() && propertyValue.contains(packageName)).apply {
            GLog.d(TAG, LogFlag.DL) { "[isSupportGoogleLens] isSupport:$this" }
        }
    }

    /**
     * 判断多媒体是否支持10亿色，适配有些CPU平台不支持10亿色解码，点开搬家过来的图片黑图没有提示,耗时60ms，调用需要放入子线程
     */
    private val isMediaSupport10Bit: Boolean by lazy {
        var isMediaSupport = false
        var mediaCodec: MediaCodec? = null
        kotlin.runCatching {
            mediaCodec = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_HEVC).apply {
                codecInfo.getCapabilitiesForType(MimeTypeUtils.MIME_TYPE_VIDEO_HEVC).profileLevels?.find { levels ->
                    levels.profile == MediaCodecInfo.CodecProfileLevel.HEVCProfileMain10
                }?.let {
                    isMediaSupport = true
                }
            }
        }.onFailure {
            GLog.e(TAG) { "[isMediaSupport10Bit] createDecoderByType fail $it ,media not support 10 bit." }
        }
        mediaCodec?.apply {
            stop()
            release()
        }
        GLog.d(TAG) { "[isMediaSupport10Bit] isMediaSupport: $isMediaSupport" }
        isMediaSupport
    }

    /**
     * 翻译功能入口显示规则；只有内销、存在翻译app应用，并且查询到对应meta data为1时才显示
     * @return 返回是否需要过滤。true：支持翻译功能；false：不支持翻译功能
     */
    private val isTranslateSupported: Boolean by lazy {
        when {
            !isRegionCN -> {
                GLog.w(TAG, "isTranslateSupported, not domestic")
                false
            }

            PACKAGE_NAME_TRANSLATE.isEmpty() -> {
                GLog.w(TAG, "isTranslateSupported, translate package is not exit")
                false
            }

            PackageInfoUtils.getMetaData<Int>(PACKAGE_NAME_TRANSLATE, TRANSLATE_METADATA) != 1 -> {
                GLog.w(TAG, "isTranslateSupported, meta data not support")
                false
            }

            isSuperTextV2Supported == false -> {
                GLog.w(TAG, "isTranslateSupported, not support")
                false
            }

            else -> {
                GLog.d(TAG, "isTranslateSupported, is supported")
                true
            }
        }
    }

    /**
     * 是否支持 超级文本 2.0 能力
     */
    private var isSuperTextV2Supported: Boolean? = null
        get() {
            if (field == null) {
                try {
                    runBlocking {
                        withTimeout(TIME_THREE_SECOND) {
                            field = FeatureGetter(
                                context = context,
                                configAbility = configAbility,
                                osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.SUPER_TEXT_V2),
                                iFeatureBasicCondition = {
                                    val isSupportSuperTextV2 = runCatching {
                                        val isSupportSuperText = VersionUtils.isSupportSuperText(context, true)
                                        GLog.d(TAG, "isSupportSuperText = $isSupportSuperText")
                                        isSupportSuperText && VersionUtils.isSuperTextToolBarSupported().apply {
                                            GLog.d(TAG, "isSuperTextV2Supported = $this")
                                        }
                                    }.onFailure {
                                        GLog.w(TAG, "isSupportSuperText: permission denied.")
                                    }.getOrNull() ?: false
                                    isSupportSuperTextV2 && isRegionCN
                                }
                            ).get()
                        }
                    }
                } catch (ex: TimeoutCancellationException) {
                    GLog.e(TAG, "isSuperTextV2Supported get failed, ${ex.message}")
                }
            }
            return field
        }

    /**
     * 是否支持 超级文本 1.0 的 OCR 能力
     */
    private val isSuperTextV1OcrSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.SUPER_TEXT_V1_OCR),
            iFeatureBasicCondition = {
                isRegionCN && (isSupportOcrService || OcrCompatUtil.isOcr1Supported(context, NetworkMonitor.isNetworkValidated()))
            }
        ).get()
    }

    /**
     * 跳转扫一扫功能入口显示规则；只有存在扫一扫app应用，并且查询到对应meta data为1时才显示
     * @return 返回是否支持跳转扫一扫功能。true：支持；false：不支持
     */
    private val isSuperTextJumpOcrScannerSupported: Boolean by lazy {
        when {
            !isRegionCN -> {
                GLog.w(TAG, LogFlag.DL) { "isSuperTextJumpOcrScannerSupported, not domestic" }
                false
            }

            !OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_2) -> {
                GLog.w(TAG, LogFlag.DL) { "isSuperTextJumpOcrScannerSupported, version is low" }
                false
            }

            /**
             *  "ro.oplus.cfg_fun_flag" :
             *  1) false表示realme产品。保密需求，不能被realme提前带出。需要解密后，才能支持realme
             *  2) realme产品永远返回false
             *
             *  coloros1502ConfidentialPagani:仅对OPPO和一加有效果，realme永远返回flase
             *  pagani项目发布前：
             *  1）pagani项目-内部普通版本，coloros1502ConfidentialPagani返回false
             *  2) pagani项目-媒体版本，coloros1502ConfidentialPagani返回true
             *  3)其他非pagani对外版本, coloros1502ConfidentialPagani返回true
             *  pagani项目发布后：
             *  1）pagani项目-内部普通版本，coloros1502ConfidentialPagani返回false
             *  2) pagani项目-媒体版本，coloros1502ConfidentialPagani返回false
             *  3)其他非pagani对外版本, coloros1502ConfidentialPagani返回false
             */
            if (Flags.coloros1502ConfidentialPagani()) {
                // 只要是pagani，不区分媒体版本还是普通版本，都返回true
                listOf("PKX110, CPH2723").find { it == configAbility.getStringConfig(SystemInfo.PRODUCT_NAME) } != null
            } else {
                // 在pagani发布之后，且如果需要上realme，需要删除这段代码，替换为return false
                GallerySystemProperties.getBoolean(FEATURE_CONFIG_FUN_FLAG, false)
            }.not() -> {
                GLog.w(TAG, LogFlag.DL) { "isSuperTextJumpOcrScannerSupported, keep secret" }
                false
            }

            PACKAGE_OCR_SCANNER.isEmpty() -> {
                GLog.w(TAG, LogFlag.DL) { "isSuperTextJumpOcrScannerSupported, OcrScanner package is not exit" }
                false
            }

            PackageInfoUtils.getMetaData<Int>(PACKAGE_OCR_SCANNER, OCR_SCANNER_DOCUMENT_VERSION) != OCR_SCANNER_DOCUMENT_HQ -> {
                GLog.w(TAG, LogFlag.DL) { "isSuperTextJumpOcrScannerSupported, meta data not support" }
                false
            }

            else -> {
                GLog.d(TAG, LogFlag.DL) { "isSuperTextJumpOcrScannerSupported, is supported" }
                true
            }
        }
    }

    private val isSupportOcrService: Boolean by lazy {
        val isSupOcrSer = context.packageManager.queryIntentServices(
            Intent(AppBrandConstants.Action.ACTION_OCR),
            0
        ).size > 0
        GLog.d(TAG) { "[isSupportOcrService] isSupportOcrService=$isSupOcrSer" }
        isSupOcrSer
    }

    private val isTimelinePageMultiViewSwitchSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.TIMELINE_PAGE_MULTI_VIEW_SWITCH),
        ).get()
    }

    private val isEliminatePenSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.ELIMINATE_PEN),
        ).get()
    }

    private val isBlurringSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.BLURRING),
            iFeatureBasicCondition = {
                !OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
            },
            iCloudFeatureGetter = LegacyCloudFeatureGetter {
                BlurSwitchParser().isCloudSwitchOn
            }
        ).get()
    }


    /**
     * 是否支持多模态
     * 1.RUS名单开关
     * 2.isSupportAndesSearch
     */
    private val isMultiModalSupported: Boolean
        get() {
            val isMultiModalEnabled = if (DEBUG_SEARCH) {
                SearchConst.MULTI_MODAL_SWITCH
            } else {
                AndesSearchRusConfig.andesSearchConfig?.isMultiModalEnabled ?: false
            }
            return isSupportAndesSearch && isMultiModalEnabled
        }

    /**
     * 是否支持OcrEmbedding
     * 1.RUS名单开关
     * 2.isSupportAndesSearch
     */
    private val isOcrEmbeddingSupported: Boolean
        get() {
            val isOcrEmbeddingEnabled = if (DEBUG_SEARCH) {
                SearchConst.OCR_EMBEDDING_SWITCH
            } else {
                AndesSearchRusConfig.andesSearchConfig?.isOcrEmbeddingEnabled ?: false
            }
            return isSupportAndesSearch && isOcrEmbeddingEnabled
        }

    /**
     * 是否支持Caption
     * 1.RUS名单开关
     * 2.支持融合搜索
     * 3.内销中文环境
     * 4.OS15及以上
     */
    private val isCaptionSupported: Boolean
        get() {
            return LocaleUtils.isChinaMainland(context) && isPrivacyCaptionSupported && isSupportAndesSearch
        }

    /**
     * 个保是否支持 Caption
     * 1.RUS名单开关
     * 2.支持融合搜索
     * 3.内销
     * 4.OS15及以上
     */
    private val isPrivacyCaptionSupported: Boolean
        get() {
            val isCaptionRusEnabled = if (DEBUG_SEARCH) {
                SearchConst.CAPTION_SWITCH
            } else {
                AndesSearchRusConfig.andesSearchConfig?.isCaptionEnabled ?: true
            }
            val isOsVersionEnabled = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
            return (isCaptionRusEnabled && isSupportAndesSearchNotLimitLanguage && isRegionCN && isOsVersionEnabled)
        }

    /**
     * 是否支持中文知识图谱
     * 使用LocaleUtils#isChinaMainland()判断
     *
     * 注意：不可改为by lazy或等于号赋值，否则内销切换为非中文，此处依旧返回true
     */
    private val isLabelGraphSupported: Boolean
        get() {
            return LocaleUtils.isChinaMainland(context)
        }

    /**
     * 是否支持英文知识图谱
     * 1.英文
     *
     * 注意：不可改为by lazy或等于号赋值，原因同isLabelGraphSupported
     */
    private val isEnglishLabelGraphSupported: Boolean
        get() {
            return LocaleUtils.isEnglish(context)
        }

    private val isSuperTextV1ConvertDocSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.SUPER_TEXT_V1_CONVERT_DOC),
            iCloudFeatureGetter = LegacyCloudFeatureGetter { !FunctionSwitcherConfig.docConvertDisable() },
            iFeatureBasicCondition = { configAbility.getBooleanConfig(IS_REGION_CN) ?: false },
        ).get()
    }

    private val isSuperTextV1ConvertPptDocSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.SUPER_TEXT_V1_CONVERT_DOC),
            iCloudFeatureGetter = LegacyCloudFeatureGetter { FunctionSwitcherConfig.functionEnable(FunctionSwitcherConfig.PPT_FUNCTION_ID) },
            iFeatureBasicCondition = { configAbility.getBooleanConfig(IS_REGION_CN) ?: false },
        ).get()
    }

    private val isSuperTextV1ConvertWordDocSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.SUPER_TEXT_V1_CONVERT_DOC),
            iCloudFeatureGetter = LegacyCloudFeatureGetter { FunctionSwitcherConfig.functionEnable(FunctionSwitcherConfig.WORD_FUNCTION_ID) },
            iFeatureBasicCondition = { configAbility.getBooleanConfig(IS_REGION_CN) ?: false },
        ).get()
    }

    private val isNewDateTakenParseEnable: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            iCloudFeatureGetter = LegacyCloudFeatureGetter {
                FunctionSwitcherConfig.getFunctionEnableResult(
                    FunctionSwitcherConfig.PREFERENCE_NEW_DATE_TAKEN_PARSE_ENABLE,
                    true
                )
            },
        ).get()
    }

    private val isSuperTextV1ConvertExcelDocSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.SUPER_TEXT_V1_CONVERT_DOC),
            iCloudFeatureGetter = LegacyCloudFeatureGetter { FunctionSwitcherConfig.functionEnable(FunctionSwitcherConfig.EXCEL_FUNCTION_ID) },
            iFeatureBasicCondition = { configAbility.getBooleanConfig(IS_REGION_CN) ?: false },
        ).get()
    }

    /**
     * 是否支持 进入相册亮度一致性 （目前仅支持相机相册亮度一致性）
     * feature满足只能说明相册有这个能力支持，但是是否要提亮等操作需要在业务中去判断
     *
     * 需要同时满足以下条件：
     * 1.os14.0及以上
     * 2.支持单独做提亮策略的项目禁用（机型的相册亮度单独定义过）（Luna项目有单独支持提亮）
     * Luna项目有自定义一套相册内部进大图的提亮策略
     * 3.系统有配置 支持相机相册亮度一致性
     */
    private val isSupportBrightenUniformity: Boolean by lazy {
        if (GProperty.DEBUG_BRIGHTEN_UNIFORMITY) {
            true
        } else {
            // 1.os14.0及以上
            val isAtLeastOs14 = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
            // 2.支持单独做提亮策略的项目禁用
            val isNotBrightEnhance = configAbility.getBooleanConfig(SystemInfo.IS_SUPPORT_BRIGHTNESS_ENHANCE) == false
            // 3.系统有配置 支持相机相册亮度一致性
            val hasUniformityFeature = hasOplusFeature(context, keyCameraBrightenUniformity, keyCameraBrightenUniformity)
            GLog.d(TAG) {
                "[isSupportBrightenUniformity] isAtLeastOs14=$isAtLeastOs14 isNotBrightEnhance=$isNotBrightEnhance " +
                    "hasUniformityFeature=$hasUniformityFeature"
            }
            isAtLeastOs14 && isNotBrightEnhance && hasUniformityFeature
        }
    }

    /**
     * Pre Transition动画是否被禁用
     */
    private val isPreTransitionDisabled: Boolean by lazy {
        GProperty.DEBUG_PRE_TRANSITION_DISABLE
    }

    /**
     * 是否支持抠图，需要同时满足以下条件
     * 1. os14.0及以上
     * 2. 抠图sdk支持抠图
     * 3. 此机型处于cpu白名单内
     * 4. 针对“ro.product.first_api_level” 是Android V的机器，即Android V以来的PDT项目，默认支持抠图。如果某个项目不支持，使用黑名单
     * NOT_SUPPORT_LNS_LOS_PHONE_MODEL_BLACKLIST 控制
     *
     * 调试开关：adb shell setprop debug.gallery.lns true
     */
    private val isLnSSupported: Boolean by lazy {
        when {
            GProperty.DEBUG_LNS -> true
            ApiLevelUtil.isFirstApiLevelAtLeastV() -> {
                Config.NOT_SUPPORT_LNS_PHONE_MODEL_BLACKLIST.contains(
                    configAbility.getStringConfig(
                        PHONE_MODEL,
                        EMPTY_STRING
                    )
                ).not()
            }
            else -> {
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.LNS),
                    iFeatureBasicCondition = {
                        // mark by:xiewujie, 等抠图sdk接入了加上sdk的判断
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
                    },
                    iCloudFeatureGetter = LegacyCloudFeatureGetter {
                        LnSSwitchParser().isDeviceSupportLnS(context, CPUInfoUtils.getCpuPartNumber(
                            { configAbility.getStringConfig(CPU_INFO_PART_NUMBER) },
                            { configAbility.getStringConfig(CPU_INFO) }
                        ))
                    }
                ).get().also {
                    GLog.i(TAG, "[isLnSSupported] lift and shift is support:$it")
                }
            }
        }
    }

    private val isSupportDotMatrixEffect: Boolean by lazy {
        Config.SUPPORT_DOT_MATRIX_EFFECT_WHITELIST.contains(
            CPUInfoUtils.getCpuPartNumber(
                { configAbility.getStringConfig(CPU_INFO_PART_NUMBER) },
                { configAbility.getStringConfig(CPU_INFO) })
        )
    }

    /**
     * 抠图2.0需求回落轻量OS项目，轻量os项目要支持抠图
     * 轻量OS机型是否支持抠图
     */
    private val isSupportLnsLOsPhoneModel: Boolean by lazy {
        Config.SUPPORT_LNS_LOS_PHONE_MODEL_WHITELIST.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    /**
     * 是否支持Olive资源抠图
     * 在黑名单中的机型，针对Olive资源，不能支持抠图。
     */
    private val isSupportLnsForOLive: Boolean by lazy {
        Config.CONFIG_SHOULD_DISABLE_LNS_FOR_OLIVE.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING)).not()
    }

    /**
     * 是否支持杜比视频走硬件解码
     */
    private val isSupportDolbyVideoHardwareDecoder: Boolean by lazy {
        if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0).not()) {
            false
        } else {
            isSupportDolbyDecode
        }
    }

    /**
     * 是否支持视频标签跑CPU而不走GPU
     * 此问题是为了修复BUG 7953659，分析原因是对应的GPU OpenCL库有问题，需要在此平台上走CPU而不走GPU
     * 目前仅7550和7150平台支持
     */
    private val isSupportVideoClassifyRunOnCpu: Boolean by lazy {
        Config.CONFIG_SUPPORT_VIDEOCLASSIFY_RUN_ON_CPU_LIST.contains(CPUInfoUtils.getCpuPartNumber(
            { configAbility.getStringConfig(CPU_INFO_PART_NUMBER) },
            { configAbility.getStringConfig(CPU_INFO) }
        ))
    }


    private val isCastAdaptionSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.HEY_CAST),
            iFeatureBasicCondition = {
                val isHeyCastSupportAdaption =
                    PackageInfoUtils.getMetaData<Boolean>(HEY_CAST_PKG, META_DATA_KEY_SUPPORT_ADAPTION) ?: false
                val isSynergySupportAdaption =
                    PackageInfoUtils.getMetaData<Boolean>(SYNERGY_PKG, META_DATA_KEY_SUPPORT_ADAPTION) ?: false
                isHeyCastSupportAdaption && isSynergySupportAdaption
            },
        ).get()
    }

    /**
     * 是否支持“合成GIF”功能
     * 必要的基础条件：OS13.2及以上才有该功能
     * 系统配置：如果某系统（如轻量OS）配置了BasicOsFeature.GIF_SYNTHESIS为false,则不支持“合成GIF”功能, 否则为支持该功能
     * 云端配置：在满足上述两个条件后判断云端是否有关闭该机型的该功能
     */
    private val isGifSynthesisSupported: Boolean by lazy {
        if (DEBUG_GIF_SYNTHESIS) {
            // 调试时 强制默认支持合成GIF，即使在轻量OS或旧OS等机型上都能调试
            true
        } else {
            GLog.i(TAG, "isGifSynthesisSupported, start")
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = {
                    // OS13.2及以上才有该功能
                    OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_2)
                },
                osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.GIF_SYNTHESIS),
                iCloudFeatureGetter = LegacyCloudFeatureGetter {
                    GifSynthesisSwitcherManager().isGifSynthesisCloudSwitchOn
                }
            ).get().also {
                GLog.i(TAG, "isGifSynthesisSupported, end, isGifSynthesisSupported=$it")
            }
        }
    }

    /**
     * 是否支持Presentation投屏
     * 必要的基础条件：非单解码器设备
     * 其他的完全由网络后台配置的机型（限定下发OS版本）来控制
     */
    private val isPresentationCastSupported: Boolean by lazy {
        if (CastSwitcherManager.DEBUG_SUPPORT_PRESENTATION) {
            // 调试时 强制默认支持Presentation投屏
            true
        } else {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = {
                    val isSingleCodecDevice = configAbility.getBooleanConfig(Codec.IS_SINGLE_CODEC_DEVICE, false) ?: false
                    GLog.d(TAG, "isPresentationCastSupported isSingleCodecDevice=$isSingleCodecDevice")
                    // 非单解码器设备 才支持
                    isSingleCodecDevice.not()
                },
                iCloudFeatureGetter = LegacyCloudFeatureGetter {
                    CastSwitcherManager().isPresentationCloudSwitchOn
                }
            ).get()
        }
    }

    private val isMapPageSupported: Boolean by lazy {
        //BasicOsFeature.MAP_PAGE 这个feature在轻量OS上配置了false，非轻量OS上未配置该Feature
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.MAP_PAGE),
            iFeatureBasicCondition = {
                //这里先判断内外销大于12.1时判定上述feature，外销直接判定是否存在上述feature
                val leastos121 = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_12_1)
                val result = if (isRegionCN) {
                    leastos121
                } else {
                    true
                }
                result
            }
        ).get()
    }

    private val isPrivacyProtectionAssistantSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.PRIVACY_PROTECTION_ASSISTANT),
            iFeatureBasicCondition = {
                OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_0)
                    && (AppFeatureGetter(context, BasicOsFeature.STDPPS_FEATRUE_NAME).get() == true)
                    && isProductLight.not()
            },
        ).get()
    }

    private val isAiIdPhotoSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            iFeatureBasicCondition = {
                PackageInfoUtils.getBooleanFromMetaData(
                    context.packageName,
                    META_DATA_AI_ID_PHOTO_KEY
                ) && configAbility.getBooleanConfig(IS_REGION_CN) ?: false
            },
        ).get()
    }

    /**
     * olive 区间播放最大限制， 单位：ms
     */
    private val oliveClipPlayPositionMaxLimitInMs: Long by lazy {
        OliveParamManager().oliveClipPlayPositionMaxLimitInMs.also {
            GLog.d(TAG, "oliveClipPlayPositionMaxLimitInMs = $it ms")
        }
    }

    /**
     * 抠图打断之后继续显示抠图的Olive播放时长阈值 时长， 以ms为单位
     */
    private val olivePlayedThresholdTimeForLnsShowInMs: Long by lazy {
        OliveParamManager().olivePlayedThresholdTimeForLnsShowInMs.also {
            GLog.d(TAG, "olive played threshold time for lns show is $it ms")
        }
    }

    /**
     * 抠图 短长按 时长， 以ms为单位
     */
    private val liftAndShiftShortLongPressTimeOutInMs: Long by lazy {
        LiftAndShiftParamManager().shortLongPressTimeOutInMs.also {
            GLog.d(TAG, "lift and shift short long press time out is $it ms")
        }
    }

    /**
     * ```
     * 是否显示大图缩略图轴
     *
     * 以下条件满足则返回true ：
     * 1.os版本大于等于13.2(29)
     * 2.动画等级为中或高
     * 3.机器不在机型黑名单里
     *
     * 调试：adb shell setprop debug.gallery.photo.photothumbline true
     *
     * ```
     */
    private val isSupportPhotoThumbLine: Boolean by lazy {
        when (GProperty.DEBUG_SUPPORT_PHOTOTHUMBLINE) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = { OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_2) },
                    osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.THUMB_LINE),
                    iCloudFeatureGetter = LegacyCloudFeatureGetter {
                        PhotoThumbLineSwitchHelper().let {
                            val isHighOrMiddleAnimationLevel = animationLevel?.let { level ->
                                (level == PerformanceLevel.HIGH) || (level == PerformanceLevel.MIDDLE)
                            } ?: false
                            if (isHighOrMiddleAnimationLevel.not()) {
                                false
                            } else if (it.deviceModelBlacklist.isNullOrEmpty()) {
                                true
                            } else {
                                val isInModelBlacklist = it.deviceModelBlacklist.contains(configAbility.getStringConfig(PHONE_MODEL))
                                isInModelBlacklist.not()
                            }
                        }
                    }
                ).get()
            }
        }
    }

    /**
     * 通过判断LocalHdr版本来判断是否将LocalHdr图层设置为no type
     */
    private val isSurfaceSupportDisableLocalHdrLayer: Boolean by lazy {
        brightenVersionLocalHdr >= EdrUtilsWrapper.SURFACE_SUPPORT_DISABLE_LOCAL_HDR_LAYER_VERSION
    }

    /**
     * 是否支持LocalHdr图层改成非LocalHdr类型
     *
     * 相册功耗优化 - LocalHdr&杜比需求描述：
     * 1.首次打开大图，如果是杜比视频，下发0（no type），如果不是则下发Enable Local Hdr Flag；
     * 2.打开大图左右两边如果是HDR图，两边都不提前下发Enable Local Hdr Flag，只有当HDR图部分可见时，才下发Enable Local Hdr Flag；
     * 3.从图片滑入dolby视频, 图片完全不可见时, 给大图surface下发0（no type）；
     * 4.从dolby视频划入到图片, 如果是HDR图片且图片部分可见时, 下发Enable Local Hdr Flag，如果不是HDR图，不做处理；
     *
     * 注释：
     * 1.下发0（no type） 主要用于当Dolby、HLG等HDR视频可见时，通过给显示框架下发0，这样大图surface（LocalHdr图层）不需再走GPU合成，进而降低功耗；
     * 2.该需求需要device支持LocalHdr和HDR视频播放功能；
     *
     * 支持条件：支持LocalHdr功能 + 支持HDR提亮和HDR解码 + (Android T系统上LocalHdr版本 >=2  或者 Android U系统上）
     */
    private val isSupportDisableLocalHdrLayer: Boolean by lazy {
        if (GProperty.DEBUG_GALLERY_BRIGHTEN) {
            true
        } else {
            GLog.d(TAG, LogFlag.DL) {
                "isSupportDisableLocalHdrLayer isLocalHdrSupported: $isLocalHdrSupported," +
                    " isDolbyBrightenSupported: $isDolbyBrightenSupported, isSupportDolbyDecode: $isSupportDolbyDecode," +
                    " isSupportHlgEncode: $isSupportHlgEncode, isHdrVisionSupported: $isHdrVisionSupported"
            }

            val isSupportDolby = isDolbyBrightenSupported && isSupportDolbyDecode
            val isSupportHlg = isSupportHlgEncode && isHdrVisionSupported
            if (isLocalHdrSupported
                && (isSupportHlg || isSupportDolby)
            ) {
                (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
                    || (isSurfaceSupportDisableLocalHdrLayer && OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_0)))
            } else {
                false
            }
        }
    }

    /**
     * 是否支持超级加密
     * 1.针对oppo设备，需要手机有安全芯片 并且 私密保险箱支持安全芯片
     * 2.针对realme设备，支持NFC加密，StrongBoxKeyStore
     */
    private val isSupportSuperEncryption: Boolean by lazy {
        (hasSystemFeature(FEATURE_STRONGBOX_KEYSTORE) && hasSystemFeature(keySupportNFCSecure))
            || (hasSystemFeature(keySupportSecureElementEse2) && PackageInfoUtils.getBooleanFromMetaData(
            FILE_ENCRYPTION_PKG,
            SUPPORT_SE_CHIP_KEY
        ))
            || GProperty.DEBUG_SUPER_ENCRYPTION
    }

    /**
     * 获取系统支持EDR效果的版本号，如Local HD或UI Dimming效果
     * 默认值与系统保持一致为-1
     * 第一个版本version值，从1开始
     */
    private val brightenVersionLocalHdr: Int by lazy {
        EdrUtilsWrapper.getLocalHdrVersion().also {
            GLog.d(TAG, "getLocalHdrVersion success, version：$it")
        }
    }

    /**
     * 获取系统支持EDR效果的版本号，用于杜比
     * 默认值与系统保持一致为-1
     * 第一个版本version值，从1开始
     */
    private val brightenVersionDolby: Int by lazy {
        EdrUtilsWrapper.getDolbyEdrVersion().also {
            GLog.d(TAG, "getDolbyEdrVersion success, version：$it")
        }
    }

    private val isLocalHdrSupported: Boolean by lazy {
        //edrVersion大于0，LocalHDR可用
        (brightenVersionLocalHdr > 0).also {
            GLog.d(TAG) { "[isLocalHdrSupported] it is supported localHdr：$it" }
        }
    }

    /**
     * 满足以下条件即表示支持人像景深的Hdr编辑(包括UHDR、LHDR)
     * 1，大图支持localhdr 或者支持UHDR
     * 2，系统版本在os14及以上
     * 3，非轻量OS
     */
    private val isPortraitBlurHdrEditSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.LOCAL_HDR_EDIT),
            iFeatureBasicCondition = {
                ((isLocalHdrSupported && GProperty.DEBUG_LOCAL_HDR_EDIT) || (isUltraHdrSupported && DEBUG_UHDR_EDIT))
                    && OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
            }
        ).get()
    }

    /**
     * 是否支持Ultra HDR的显示
     * 1.OS 14.0.0通过SF配置的属性判断；
     * 2.OS 14.0.1及以上版本通过SF提供的接口判断；
     * 3.提供上面两种判断方式的原因是因为：UHdr需求开发时，SF新增的判断是否支持UHdr显示的接口赶不上OSDK 14.0.0的发版周期，反射判断机制会消耗性能
     *   所以14.0.0上通过属性来判断，14.0.1及以上版本通过反射判断；
     * 4.这里有个TODO项，待SF提供的接口在OSDK上发布后，需要调整EdrUtilsWrapper的isUHDRSupport实现方式，通过OSDK提供的接口直接获取判断，不再使用反射方式；
     */
    private val isUltraHdrSupported: Boolean by lazy {
        if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0_1)) {
            EdrUtilsWrapper.isUHDRSupport().also {
                GLog.d(TAG) { "[isUltraHdrSupported] OS version is at least 14.0.1：$it" }
            }
        } else if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)) {
            GallerySystemProperties.getBoolean(keySupportUHdr, false).also {
                GLog.d(TAG) { "[isUltraHdrSupported] OS version is at least 14.0.0：$it" }
            }
        } else false
    }

    /**
     * 设备是否支持单图HDR显示
     */
    private val isHDRCombineDisplaySupported: Boolean by lazy {
        if (GProperty.HDR_EDIT_COMBINE_DISPLAY_MODE_DEBUG) {
            true
        } else {
            if (isUltraHdrSupported) {
                // Marked by wanglian 这里需要根据显示框架接口进一步判断当前设备是否支持单图HDR显示
                true
            } else {
                false
            }
        }
    }

    /**
     * 是否支持HDR的下变换算法
     */
    private val isUHdrTransformSupport: Boolean by lazy {
        val start = System.currentTimeMillis()
        val isSupport = ApiLevelUtil.isAtLeastAndroidU() && HdrTransformFactory.isSupportHdrTransform()
        GLog.d(TAG, LogFlag.DL, "[isUHdrTransformSupport] isSupport:$isSupport, cost time:${GLog.getTime(start)} ms")
        isSupport
    }

    /**
     * 满足以下条件即表示支持UHdr编辑
     * 1，大图支持UHdr
     * 2，系统版本在os14及以上
     * 3，[GProperty.DEBUG_UHDR_EDIT] 为true
     */
    private val isUHdrEditSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            iFeatureBasicCondition = {
                isUltraHdrSupported
                    && OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
                    && GProperty.DEBUG_UHDR_EDIT
            }
        ).get()
    }

    /**
     * 满足以下条件即表示支持localHdr编辑
     * 1，大图支持localhdr
     * 2，系统版本在os14及以上
     * 3.内销版本
     * 4，[GProperty.DEBUG_LOCAL_HDR_EDIT] 为true
     * 5，非轻量OS
     */
    private val isLocalHdrEditSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.LOCAL_HDR_EDIT),
            iFeatureBasicCondition = {
                isLocalHdrSupported
                    && OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
                    && GProperty.DEBUG_LOCAL_HDR_EDIT
            }
        ).get()
    }

    /**
     * 是否支持Raw编辑、
     * 满足以下条件则支持raw编辑
     * 1，系统版本在os14及以上
     * 2，[GProperty.DEBUG_RAW_EDIT] 为true
     * 3，非轻量OS
     */
    private val isRawEditSupported: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.RAW_EDIT),
            iFeatureBasicCondition = {
                OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
                    && GProperty.DEBUG_RAW_EDIT
            }
        ).get()
    }

    private val isLocalHdrSupportedOnSubDisplay: Boolean by lazy {
        EdrUtilsWrapper.supportLocalHdrOnSubDisplay()
    }

    private val isBrightenCompareSupported: Boolean by lazy {
        HdrSdrManagerOs13.SUPPORT_NOTIFY_BRIGHTNESS_SCALE
    }

    private val isDolbyBrightenSupported: Boolean by lazy {
        //brightenVersionDolby大于0，表示dolby提亮可用
        brightenVersionDolby > 0
    }

    /**
     * 获取是否支持臻彩视界。
     *
     * 通过获取系统支持EDR效果的版本号，用于臻彩视界
     * 默认值与系统保持一致为-1
     * 第一个版本version值，从1开始
     *
     * @since 31.21
     */
    private val isHdrVisionSupported: Boolean by lazy {
        //hdrVisionBrightenVersion大于0，表示HDR提亮可用
        val hdrVisionVersion = if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)) {
            EdrUtilsWrapper.supportHdrVision().also {
                GLog.d(TAG, "supportHdrVisionVersion success, version：$it")
            }
        } else DEFAULT_HDR_VISION_EDR_VERSION

        hdrVisionVersion > 0
    }

    /**
     * 获取是否支持编辑中视频提亮。
     */
    private val isEditVideoHdrBrightenSupported: Boolean by lazy {
        when (GProperty.DEBUG_EDIT_VIDEO_HDR_BRIGHTEN) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_1) && isHdrVisionSupported
            }
        }
    }

    /**
     * 是否支持人像景深，包括OS16.0之前和OS16.0之后的判断
     */
    private val isPortraitBlurSupported: Boolean by lazy {
        isSupportPortraitBlurBeforeOS16 || isSupportPortraitBlurAfterOS16
    }

    /**
     * 判断OS16.0之前的系统是否支持人像景深功能
     * 默认都支持
     * 16.0之前的版本和原来保持一致，显示大图入口，从大图跳转人像景深页面
     */
    private val isSupportPortraitBlurBeforeOS16: Boolean by lazy {
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_1)
                && OSVersionUtils.isAtMost(OPLUS_OS_15_0_2)
    }

    /**
     * 判断OS16.0之后的系统是否支持人像景深功能
     * 默认都支持
     * 16.0之后的版本人像景深移入编辑二级页，并支持参数化编辑，不显示大图入口
     */
    private val isSupportPortraitBlurAfterOS16: Boolean by lazy {
        GProperty.DEBUG_PORTRAIT_BLUR_EDITING
                || OSVersionUtils.isAtLeast(OPLUS_OS_16_0_0)
    }

    /**
     * 是否支持相机生的jpeg的exif中缩略图
     */
    private val isCameraJpegExifThumbnailSupported: Boolean by lazy {
        (CameraConfigInitializer.getInstance(context, configAbility).isCameraJpegExifThumbnailSupported()).apply {
            GLog.d(TAG) { "isCameraJpegExifThumbnailSupported:$this" }
        }
    }

    /**
     * 相机是否支持proxdr提亮
     */
    private val isCameraProxdrSuppoted: Boolean by lazy {
        (CameraConfigInitializer.getInstance(context, configAbility).isCameraProxdrSuppoted()).apply {
            GLog.d(TAG) { "isCameraProxdrSuppoted:$this" }
        }
    }

    /**
     * 是否支持哈苏水印编辑
     */
    private val isHasselWatermarkSupported: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isHasselWatermarkSupported()
    }

    /**
     * 是否为哈苏联名设备
     */
    private val isSupportHasselDevice: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isSupportHasselDevice().apply {
            GLog.d(TAG) { "isSupportHasselDevice:$this" }
        }
    }

    /**
     * 是否支持孤独星球水印编辑
     */
    private val isLonelyPlanetWatermarkSupported: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isLonelyPlanetWatermarkSupported()
    }

    /**
     * 根据相机的配置文件判断是否是新机型，该值用于水印功能的地理位置解析规则参数使用
     */
    private val isCameraConfigWatermarkNew: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isCameraConfigWatermarkNew()
    }

    /**
     * 是否支持水印（已全面切换到大师水印通用框架）
     * OS 13.1开始有哈苏水印
     * OS 13.2开始有个性化水印的支持
     * 因此设定从OS 13.1开始有水印支持的情况下，全部用大师水印替换
     */
    private val isPhotoEditorWatermarkSupported: Boolean by lazy {
        // 方便在OS13.1上调试个性化水印，这里加一个可控开关 "debug.gallery.photo.editor.watermark.switcher"
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_1)
            || GallerySystemProperties.getBoolean(PHOTO_EDITOR_WATERMARK_SWITCHER, false)
    }

    /**
     * 是否支持Ai水印大师
     * UI目前关于品牌的差异化资源需要晚点提供，避免在一加或realme上出现oppo或coloros的logo，暂时只对oppo品牌开放。
     * IPU需要支持AI大师水印：影像通过项目集成，若支持返回true；若项目不支持，返回false
     */
    private val isSupportAiWatermarkMaster: Boolean by lazy {
        when (GProperty.DEBUG_AI_WATERMARK_MASTER) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = {
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
                                && IPUClient.isFeatureSupport(context, IPUClient.IPUFeature.AI_MASTER_WATERMARK)
                    }
                ).get()
            }
        }
    }

    /**
     * 是否支持隐私水印，隐私水印只在内销机型+OS13.2以上支持（同水印编辑）
     */
    private val isPrivacyWatermarkSupported: Boolean by lazy {
        // 隐私水印是水印编辑的子集，所以需要支持水印编辑，才能支持隐私水印
        isPhotoEditorWatermarkSupported && if (GProperty.DEBUG_PRIVACY_WATERMARK) {
            true
        } else {
            (configAbility.getBooleanConfig(IS_REGION_CN) == true)
        }
    }

    /**
     * 是否支持新春水印，新春水印只在14.0及以上内销机型
     * 并且下架时间为2024年2月25号
     *
     * 2024/3/18 mark by dengwenhao：避免系统初始时间在下架时间前，导致功能依旧存在，该功能判断feature置为false
     */
    private val isSpringFestivalWatermarkSupported: Boolean = false

    private val isIPUWatermarkSupported: Boolean by lazy {
        GProperty.DEBUG_ENABLE_IPU
    }

    private val isIPURearBlurSupported: Boolean by lazy {
        GProperty.DEBUG_ENABLE_IPU && IPUClient.isFeatureSupport(context, IPUClient.IPUFeature.BACK_BLUR)
    }

    private val isIPUFrontBlurSupported: Boolean by lazy {
        GProperty.DEBUG_ENABLE_IPU && IPUClient.isFeatureSupport(context, IPUClient.IPUFeature.FRONT_BLUR)
    }

    /**
     * 是否支持街拍水印
     */
    private val isStreetWatermarkSupported: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isStreetWatermarkSupported()
    }

    /**
     * 是否支持色彩水印
     */
    private val isColorWatermarkSupported: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isColorWatermarkSupported()
    }

    /**
     * 是否Quick动画时间为短时长
     */
    private val isQuickAnimDurationShot: Boolean by lazy {
        hasAppFeature(context, keyQuickAnimDurationShot, keyQuickAnimDurationShot)
    }

    /**
     * 是否支持OCS鉴权
     */
    private val isSupportOcsAuth: Boolean by lazy {
        if (DEBUG_OCS_AUTH) {
            true
        } else {
            OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_1_0)
        }
    }

    /**
     * 是否支持边框-影幕编辑
     */
    private val isSupportBorderCaption: Boolean by lazy {
        if (GProperty.DEBUG_BORDER_EDITOR) {
            // 调试时 强制默认支持边框编辑
            true
        } else {
            hasAppFeature(context, keySupportBorderCaption, keySupportBorderCaption)
        }
    }

    /**
     * 是否支持quick图公开
     */
    private val isQuickPhotoPublicSupported: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isQuickPhotoPublicSupported()
    }

    /**
     * 相机是否支持设置查看照片方向
     */
    private val isCameraPreviewRotateSupported: Boolean by lazy {
        CameraConfigInitializer.getInstance(context, configAbility).isCameraPreviewRotateSupported()
    }

    /**
     * 当前项目是否支持超规格4k视频，此规格会超过3840*2160
     */
    private val isSupportOver4kVideo: Boolean by lazy {
        Config.OVER_4K_VIDEO_DEVICE_WHITELIST.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    /**
     * 是否支持画框水印，画框水印只在14.0以上非升级项目开放
     */
    private val isFrameWatermarkSupported: Boolean by lazy {
        if (GProperty.DEBUG_FRAME_WATERMARK) {
            true
        } else {
            ApiLevelUtil.isFirstApiLevelAtLeastU(configAbility.getIntConfig(SystemInfo.FIRST_API_LEVEL) ?: 0)
        }
    }

    private val isSupport25MLevel: Boolean by lazy {
        Config.EDIT_25M_LEVEL_WHITELIST.contains(
            CPUInfoUtils.getCpuPartNumber(
                { configAbility.getStringConfig(CPU_INFO_PART_NUMBER) },
                { configAbility.getStringConfig(CPU_INFO) })
        )
    }

    /**
     * 是否为DX4平台
     */
    private val isDX4Platform: Boolean by lazy {
        Config.DX4_PLATFORM_LIST.contains(
            CPUInfoUtils.getCpuPartNumber(
                { configAbility.getStringConfig(CPU_INFO_PART_NUMBER) },
                { configAbility.getStringConfig(CPU_INFO) }
            )
        )
    }

    /**
     * 是否支持ipu滤镜
     */
    private val isSupportIpuFilter: Boolean by lazy {
        if (GProperty.DEBUG_IPU_FILTER) {
            GLog.d(TAG, LogFlag.DL, "[isSupportIpuFilter] result true, debug switch open")
            true
        } else {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = { IPUClient.isFeatureSupport(context, IPUClient.IPUFeature.FILTER) }
            ).get().also {
                GLog.d(TAG, "[isSupportIpuFilter] isSupport:$it")
            }
        }
    }

    /**
     * 是否支持ipu美颜
     */
    private val isSupportIpuBeauty: Boolean by lazy {
        val debugValue = GProperty.DEBUG_IPU_BEAUTY
        if (debugValue < 0) {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = { IPUClient.isFeatureSupport(context, IPUClient.IPUFeature.FACE_BEAUTY) }
            ).get().also {
                GLog.d(TAG, "[isSupportIpuBeauty] isSupport:$it")
            }
        } else {
            /*
             * debugValue < 0 不进行调试
             * debugValue = 0 模拟非IPU美颜场景
             * debugValue > 0 模拟IPU美颜场景
             */
            GLog.d(TAG, LogFlag.DL, "[isSupportIpuBeauty] debugValue $debugValue")
            debugValue > 0
        }
    }


    /**
     * 是否支持合影优化-闭眼修复
     * 1. 设备是否支持从AiUnit读取，相册冷启动会写到config上
     * 2. OS版本大于等于14.1
     */
    private val isAiSupportExpressionOpt: Boolean by lazy {
        when (GProperty.DEBUG_GROUP_PHOTO_EXPRESSION) {
            FEATURE_FORCE_ENABLE -> {
                GLog.d(TAG) { "<isAiSupportFaceExpression> force open group photo!" }
                true
            }

            FEATURE_FORCE_DISABLE -> {
                GLog.d(TAG) { "<isAiSupportFaceExpression> force close group photo!" }
                false
            }

            else -> {
                val start = System.currentTimeMillis()
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = {
                        val isOSSupport = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_1_0)
                        val isDeviceSupport = configAbility.getBooleanConfig(FEATURE_IS_DEVICE_SUPPORT_AI_EXPRESSION_OPT, false) ?: false
                        GLog.d(
                            TAG, "<isAiSupportFaceExpression> " +
                                "isOSSupport:$isOSSupport, " +
                                "isDeviceSupport=$isDeviceSupport," +
                                "cost time:${GLog.getTime(start)}ms"
                        )
                        // 可用代表睁眼能力一定支持；而能力支持，功能未必能跑，要看具体的网络状态等因素
                        isOSSupport && isDeviceSupport
                    }
                ).get()
            }
        }
    }

    /**
     * 是否支持画质增强能力，需满足OS15且支持AI能力，AI能力支持见[AIFuncFeatureHelper.isAIReflectionDeblurClarifySupport]
     * realme机型AI超清算法与OPPO的不一致
     */
    private val isSupportImageQualityEnhance: Boolean by lazy {
        if (BuildConfig.DEBUG && DEBUG_IMAGE_QUALITY_ENHANCE) {
            GLog.d(TAG, "[isSupportImageQualityEnhance] is support from debug command")
            true
        } else {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = { isAIReflectionDeblurClarifySupport && (configAbility.getBooleanConfig(IS_REALME_BRAND) != true) }
            ).get().also {
                GLog.i(TAG) {
                    "[isSupportImageQualityEnhance] support:$it"
                }
            }
        }
    }

    private val isFuncDecryptDevice by lazy {
        AppFeatureGetter(context, BasicOsFeature.OS15_SECRECY_FUNC, DEBUG_SECRET).get()
    }

    /**
     * 大图页是否可以展示画质增项的推荐入口
     */
    private val isSupportImageQualityEnhanceRecommend: Boolean
        get() {
            val time = System.currentTimeMillis()
            // debug开关
            if (DEBUG_IMAGE_QUALITY_ENHANCE) {
                GLog.d(TAG, "[isSupportImageQualityEnhanceRecommend] is support from debug command")
                return true
            }

            if (configAbility.getBooleanConfig(IS_REALME_BRAND) == true) {
                // realme 未添加到aiunit里，通过feature配置的
                if (isSupportAihd.not()) {
                    GLog.d(TAG, "[isSupportImageQualityEnhanceRecommend] isSupport:false, because device is not support")
                    return false
                }
            } else {
                // 当前设备是否支持画质增强
                if (isSupportImageQualityEnhance.not()) {
                    GLog.d(TAG, "[isSupportImageQualityEnhanceRecommend] isSupport:false, because device is not support")
                    return false
                }

                // aiunit插件是否满足可用条件
                if (isPluginRecommendAvailable(IMAGE_QUALITY_ENHANCE_DETECT_STATE).not()) {
                    GLog.d(TAG, "[isSupportImageQualityEnhanceRecommend] plugin unavailable")
                    return false
                }
            }

            // 是否支持缺陷推荐
            if (isSupportFlawRecommend.not()) {
                GLog.d(TAG, "[isSupportImageQualityEnhanceRecommend] isSupport:false because don't support flawRecommend")
                return false
            }

            // 外销上是否同意相册用户须知
            if (isDomestic.not() && (configAbility.getBooleanConfig(IS_AUTHORIZE_ACCESS_IMAGE_QUALITY_ENHANCE) != true)) {
                GLog.d(TAG, "[isSupportImageQualityEnhanceRecommend] isSupport:false, because don't agree gallery export privacy")
                return false
            }

            GLog.d(TAG, "[isSupportImageQualityEnhanceRecommend] support:true. cost time: ${System.currentTimeMillis() - time}")
            return true
        }

    /**
     * 是否支持智能消除, 是否支持分为设备是否支持以及功能是否支持，均由AiUnit控制，同时满足才为true。
     * 1. 设备是否支持从AiUnit读取，相册冷启动会写到config上
     * 2. 功能是否支持会在进入编辑页时检查并写入到config，分为
     * 2.1 是否排队成功
     * 2.2 是否已下架
     *
     * 调试开关 "adb shell setprop debug.gallery.ai.eliminate.switcher true"
     */
    private val isAiEliminateSupported: Boolean
        get() = if (BuildConfig.DEBUG && GProperty.DEBUG_AI_ELIMINATE) {
            true
        } else {
            val isDeviceSupport = configAbility.getBooleanConfig(FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE, false) ?: false
            val state = configAbility.getIntConfig(AI_ELIMINATE_DETECT_STATE)
            val needQueue = (state == UnitState.STATE_UNAVAILABLE_USER_NO_APPLY)
                || (state == UnitState.STATE_UNAVAILABLE_USER_APPLYING)
                || (state == UnitState.STATE_UNAVAILABLE_USER_APPLY_FAILED)
            val isOffline = state == UnitState.STATE_UNAVAILABLE_OFFLINE
            val isSupport = isDeviceSupport && needQueue.not() && isOffline.not()
            GLog.d(TAG) {
                "[isAiEliminateSupported] isSupport:$isSupport, isDeviceSupport:$isDeviceSupport, isQueued:$needQueue, isOffline:$isOffline"
            }
            isSupport
        }

    /**
     * 设备是否支持AI消除
     *
     * 调试开关 "adb shell setprop debug.gallery.device.ai.eliminate 1" ，1表示支持，-1表示不支持
     */
    private val isDeviceSupportAiEliminate: Boolean
        get() {
            when {
                BuildConfig.DEBUG && GProperty.DEBUG_DEVICE_AI_ELIMINATE == 1 -> return true
                BuildConfig.DEBUG && GProperty.DEBUG_DEVICE_AI_ELIMINATE == -1 -> return false
                else -> {
                    val start = System.currentTimeMillis()
                    val segmentationSupport = configAbility.getBooleanConfig(IS_SUPPORT_AI_ELIMINATE_SEGMENTATION) ?: false
                    val inpaintingSupport = configAbility.getBooleanConfig(IS_SUPPORT_AI_ELIMINATE_INPAINTING) ?: false
                    val isDeviceSupport = segmentationSupport && inpaintingSupport
                    GLog.d(TAG) {
                        "[isDeviceSupportAiEliminate] segmentationSupport:$segmentationSupport, inpaintingSupport:$inpaintingSupport, " +
                            "isDeviceSupport:$isDeviceSupport, cost time:${GLog.getTime(start)}ms"
                    }
                    return isDeviceSupport
                }
            }
        }

    private val isSupportFlawRecommend: Boolean
        get() {
            if (isSupportIntelliFuncRecommend.not()) {
                GLog.d(TAG, "[isSupportFlawRecommend] isSupport:false because device not support")
                return false
            }
            // 是否同意相册隐私权限
            if (isDomestic && !MultiProcessSpUtil.getBoolean(AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD, false)) {
                GLog.d(TAG, LogFlag.DL) { "[isSupportFlawRecommend] isSupport:false, because gallery privacy don't agree" }
                return false
            }
            // 是否同意相册联网权限
            if (MultiProcessSpUtil.getBoolean(PREFERENCE_USE_OPEN_NETWORK, false).not()) {
                GLog.d(TAG, LogFlag.DL) { "[isSupportFlawRecommend] isSupport:false, because network permission don't agree" }
                return false
            }
            if (isPluginRecommendAvailable(FLAW_RECOGNIZE_DETECT_STATE).not()) {
                GLog.d(TAG, LogFlag.DL) { "[isSupportFlawRecommend] plugin unavailable" }
                return false
            }
            GLog.d(TAG, "[isSupportFlawRecommend] support:true")
            return true
        }

    private val isSupportPasserbyRecommend: Boolean
        get() {
            // 是否支持智能消除
            if (isAiEliminateSupported.not()) {
                GLog.w(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] isSupport:false, because don't support ai eliminate")
                return false
            }
            // 是否支持路人消除
            if (configAbility.getBooleanConfig(IS_SUPPORT_PASSERS_ELIMINATE) != true) {
                GLog.w(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] isSupport:false, because don't support passerby eliminate")
                return false
            }
            // 是否支持缺陷推荐
            if (isSupportFlawRecommend.not()) {
                GLog.w(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] isSupport:false because don't support flawRecommend")
                return false
            }
            // 补全是否满足可用条件
            if (isPluginRecommendAvailable(AI_ELIMINATE_INPAINTING_STATE).not()) {
                GLog.w(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] inpainting plugin unavailable")
                return false
            }
            // aiunit插件是否满足可用条件
            if (isPluginRecommendAvailable(PASSERBY_ELIMINATE_DETECT_STATE).not()) {
                GLog.w(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] detect plugin unavailable")
                return false
            }
            // 路人消除插件是否满足可用条件
            if (isPluginRecommendAvailable(AI_ELIMINATE_DETECT_STATE).not()) {
                GLog.w(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] eliminate plugin unavailable")
                return false
            }
            // 外销上是否同意相册用户须知
            if (isDomestic.not() && (configAbility.getBooleanConfig(IS_AUTHORIZE_ACCESS_AI_ELIMINATE) != true)) {
                GLog.w(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] isSupport:false, because don't agree gallery export privacy")
                return false
            }
            GLog.d(TAG, LogFlag.DL, "[isSupportPasserbyRecommend] isSupport:true")
            return true
        }

    private val isSupportDeblurRecommend: Boolean
        get() {
            // 是否支持去模糊
            if (isSupportDeblur.not()) {
                GLog.d(TAG, "[isSupportDeblurRecommend] isSupport:false, because device is not support")
                return false
            }
            // 是否支持缺陷推荐
            if (isSupportFlawRecommend.not()) {
                GLog.d(TAG, "[isSupportDeblurRecommend] isSupport:false because don't support flawRecommend")
                return false
            }
            // aiunit插件是否满足可用条件,realme的端侧去拖影算法通过feature配置的，需要跳过此判断
            if (isPluginRecommendAvailable(AI_REPAIR_DE_BLUR_DETECT_STATE).not() && isSupportRmDeblur.not()) {
                GLog.d(TAG, "[isSupportDeblurRecommend] plugin unavailable")
                return false
            }
            // 外销上是否同意相册用户须知
            if (isDomestic.not() && (configAbility.getBooleanConfig(IS_AUTHORIZE_ACCESS_AI_DE_BLUR) != true)) {
                GLog.d(TAG, "[isSupportDeblurRecommend] isSupport:false, because don't agree gallery export privacy")
                return false
            }
            GLog.d(TAG, "[isSupportDeblurRecommend] isSupport:true")
            return true
        }


    private val isSupportAIBestTakeRecommend: Boolean
        get() {
            // 是否支持AI表情优化（改为每次读取，避免后续更改“控制功能支持情况”后无法生效
            if (!isSupportAIBestTake()) {
                GLog.d(TAG, "[isSupportAIBestTakeRecommend] isSupport:false, because device is not support")
                return false
            }
            if (isSupportIntelliFuncRecommend.not()) {
                GLog.d(TAG, "[isSupportAIBestTakeRecommend] isSupport:false, because not support intellifunc recommend")
                return false
            }
            // 是否同意相册隐私权限
            if (isDomestic && !MultiProcessSpUtil.getBoolean(AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD, false)) {
                GLog.d(TAG, LogFlag.DL) { "[isSupportAIBestTakeRecommend] isSupport:false, because gallery privacy don't agree" }
                return false
            }
            // 是否同意相册联网权限
            if (MultiProcessSpUtil.getBoolean(PREFERENCE_USE_OPEN_NETWORK, false).not()) {
                GLog.d(TAG, LogFlag.DL) { "[isSupportAIBestTakeRecommend] isSupport:false, because network permission don't agree" }
                return false
            }
            // aiunit插件是否满足可用条件
            if (isPluginRecommendAvailable(AI_BEST_TAKE_DETECT_STATE).not()) {
                GLog.d(TAG, "[isSupportAIBestTakeRecommend] is not support PluginRecommendAvailable")
                return false
            }
            if (configAbility.getBooleanConfig(PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY, false) != true) {
                GLog.d(TAG, "[isSupportAIBestTakeRecommend] is not support AGREE_AI_UNIT_PRIVACY")
                return false
            }
            GLog.d(TAG, "[isSupportAIBestTakeRecommend] isSupport:true, IS_AGREE_AI_UNIT_PRIVACY:" +
                "${configAbility.getBooleanConfig(PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY, false)}")
            return true
        }

    /**
     * 是否支持AI表情优化，需满足
     * 1. OS 15.0.0
     * 2. AI能力，AI能力支持见[AIFuncFeatureHelper.isAIBestTakeSupported]
     *
     * 调试开关：adb shell setprop debug.gallery.ai_best_take_func_recommend true
     */
    private fun isSupportAIBestTake(): Boolean {
        if (GProperty.DEBUG_AI_BESTTAKE_FUNC_RECOMMEND) {
            GLog.d(TAG, "[isSupportAIBestTake] is support from debug command")
            return true
        }
        // realme的不支持
        if (configAbility.getBooleanConfig(IS_REALME_BRAND) == true) {
            GLog.d(TAG, "[isSupportAIBestTake] is support REALME_BRAND")
            return false
        }
        // 指定这些机型强制放开，确保feature配置覆盖完全后或功能完全去除feature依赖后再移除
        if (isAIBestTake1502WhiteModel()) {
            return true
        }
        // 按策略内外销15.0以上放开
        return isAIBestTakeSupported
    }

    private fun isAIBestTake1502WhiteModel(): Boolean {
        return AIFuncFeatureHelper.isAIBestTake1502WhiteModel(configAbility)
    }

    private fun isPluginRecommendAvailable(pluginStateConfig: String): Boolean {
        return when (configAbility.getIntConfig(pluginStateConfig, 0)) {
            // 能力可用
            STATE_AVAILABLE,
                // 能力可用且是本地能力
            STATE_AVAILABLE_LOCAL,
                // 能力可用，且是云侧能力
            STATE_AVAILABLE_INTERNET,
                // 未联网导致能力不可用，但是可以推荐
            STATE_UNAVAILABLE_NO_INTERNET,
                // 能力可用且有新版本可下载
            STATE_AVAILABLE_AND_NEW_DOWNLOAD -> true

            else -> false
        }
    }

    private val isSupportDeReflectionRecommend: Boolean
        get() {
            // 是否支持去反光
            if (isSupportDeReflection.not()) {
                GLog.d(TAG, "[isSupportDeReflectionRecommend] isSupport:false, because device is not support")
                return false
            }
            // 是否支持缺陷推荐
            if (isSupportFlawRecommend.not()) {
                GLog.e(TAG, "[isSupportDeReflectionRecommend] isSupport:false because don't support flawRecommend")
                return false
            }
            // aiunit插件是否满足可用条件
            if (isPluginRecommendAvailable(AI_REPAIR_DE_REFLECTION_DETECT_STATE).not()) {
                GLog.d(TAG, "[isSupportDeReflectionRecommend] plugin unavailable")
                return false
            }
            // 外销上是否同意相册用户须知
            if (isDomestic.not() && (configAbility.getBooleanConfig(IS_AUTHORIZE_ACCESS_AI_DE_REFLECTION) != true)) {
                GLog.d(TAG, "[isSupportDeReflectionRecommend] isSupport:false, because don't agree gallery export privacy")
                return false
            }
            GLog.d(TAG, "[isSupportDeReflectionRecommend] isSupport:true")
            return true
        }

    private val isSupportAilightingRecommend: Boolean
        get() {
            // 是否支持ai补光
            if (isSupportAiLighting.not()) {
                GLog.e(TAG, LogFlag.DL) { "[isSupportAilightingRecommend] isSupport:false, because device is not support" }
                return false
            }
            // 是否支持缺陷推荐
            if (isSupportFlawRecommend.not()) {
                GLog.e(TAG, LogFlag.DL) { "[isSupportAilightingRecommend] isSupport:false because don't support flawRecommend" }
                return false
            }
            // aiunit插件是否满足可用条件
            if (isPluginRecommendAvailable(AI_LIGHTING_DETECT_STATE).not()) {
                GLog.e(TAG, LogFlag.DL) { "[isSupportAilightingRecommend] plugin unavailable" }
                return false
            }
            // 外销上是否同意相册用户须知
            if (isDomestic.not() && (configAbility.getBooleanConfig(IS_AUTHORIZE_ACCESS_AI_LIGHTING) != true)) {
                GLog.e(TAG, LogFlag.DL) { "[isSupportAilightingRecommend] isSupport:false, because don't agree gallery export privacy" }
                return false
            }
            GLog.d(TAG, LogFlag.DL) { "[isSupportAilightingRecommend] isSupport:true" }
            return true
        }

    /**
     * 是否支持AI去反光，需满足OS15且支持AI能力，AI能力支持见[AIFuncFeatureHelper.isAIReflectionDeblurClarifySupport]
     *
     * 调试指令：adb shell setprop debug.gallery.ai_dereflection true
     */
    private val isSupportDeReflection: Boolean by lazy {
        if (BuildConfig.DEBUG && DEBUG_DEREFLECTION) {
            GLog.d(TAG, "[isSupportDeReflection] is support from debug command")
            true
        } else {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = { isAIReflectionDeblurClarifySupport }
            ).get().also {
                GLog.i(TAG) {
                    "[isSupportDeReflection] support:$it， cpuLevel:${PerformanceLevelUtils.cpuLevel}," +
                        "gpuLevel:${PerformanceLevelUtils.gpuLevel}"
                }
            }
        }
    }

    /**
     * 是否支持AI补光
     *
     * 调试指令：adb shell setprop debug.gallery.ai_lighting true
     */
    private val isSupportAiLighting: Boolean by lazy {
        if (GProperty.DEBUG_AI_LIGHTING) {
            GLog.d(TAG, "[isSupportAiLighting] is support from debug command")
            true
        } else {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.AI_LIGHTING),
                iFeatureBasicCondition = { AIFuncFeatureHelper.buildAiLightCondition(configAbility) }
            ).get().also {
                GLog.i(TAG) {
                    "[isSupportAiLighting] support:$it"
                }
            }
        }
    }

    /**
     * realme机型是否支持AI去模糊算法，realme通过feature配置
     */
    private val isSupportRmDeblur: Boolean by lazy {
        if (configAbility.getBooleanConfig(IS_REALME_BRAND) == true) {
            hasAppFeature(context, keySupportRmDeblur, keySupportRmDeblur)
        } else {
            false
        }
    }

    /**
     * 是否支持AI去模糊，需满足OS15且支持AI能力，AI能力支持见[AIFuncFeatureHelper.isAIReflectionDeblurClarifySupport]
     * 调试指令：adb shell setprop debug.gallery.ai_deblur true
     */
    private val isSupportDeblur: Boolean by lazy {
        if (BuildConfig.DEBUG && DEBUG_DEBLUR) {
            GLog.d(TAG, "[isSupportDeblur] is support from debug command")
            true
        } else {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = { isAIReflectionDeblurClarifySupport || isSupportRmDeblur }
            ).get().also {
                GLog.i(TAG) { "[isSupportDeblur] support:$it" }
            }
        }
    }

    private val isAIReflectionDeblurClarifySupport: Boolean by lazy {
        AIFuncFeatureHelper.isAIReflectionDeblurClarifySupport(configAbility)
    }

    private val isAIBestTakeSupported: Boolean by lazy {
        val isSupport = AIFuncFeatureHelper.isAIBestTakeSupported(configAbility)
        GLog.i(TAG) { "[isAIBestTakeSupported] AIFuncFeatureHelper isSupport:$isSupport" }
        return@lazy isSupport
    }

    /**
     * fix 9071494 解决杜比格式5视频缩图抽偏绿的问题
     * 是否支持杜比视频GPU方案
     */
    private val isSupportDolbyGPUDecode: Boolean by lazy {
        val startTime = System.currentTimeMillis()
        val isDolbyGPUDecodeDevice = if (configAbility.contains(IS_DOLBY_GPU_DECODE_DEVICE)) {
            configAbility.getBooleanConfig(IS_DOLBY_GPU_DECODE_DEVICE) ?: false
        } else {
            CodecHelper.isSupportDolbyGpuDecoder().also {
                configAbility.setBooleanConfig(IS_DOLBY_GPU_DECODE_DEVICE, it)
            }
        }
        val result = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_1) && isDolbyGPUDecodeDevice
        GLog.d(TAG, LogFlag.DL) { "isSupportDolbyGPUDecode isSupportDolbyGPUDecode = $result cost time = ${GLog.getTime(startTime)}" }
        result
    }

    /**
     * 是否支持智能推荐，需满足
     * 1. OS15
     * 2. AI能力，AI能力支持见[AIFuncFeatureHelper.isAIReflectionDeblurClarifySupport]
     * 3. 非轻量OS
     * 4.realme AI超清像素词条需替换，暂时关闭智能推荐，待词条替换后再放开
     *
     * 调试开关：adb shell setprop debug.gallery.intelli_func_recommend true
     */
    private val isSupportIntelliFuncRecommend: Boolean by lazy {
        if (BuildConfig.DEBUG && GProperty.DEBUG_INTELLI_FUNC_RECOMMEND) {
            GLog.d(TAG, "[isSupportIntelliFuncRecommend] is support from debug command")
            true
        } else {
            FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = { AIFuncFeatureHelper.isRecommendSupport(configAbility) }
            ).get().also {
                GLog.i(TAG) {
                    "[isSupportIntelliFuncRecommend] support:$it"
                }
            }
        }
    }

    private val isInteractiveSegmentSupport: Boolean by lazy {
        if (DEBUG_INTERACTIVE_SEGMENT) {
            GLog.d(TAG, "[isInteractiveSegmentSupport] isInteractiveSegmentSupport from debug value is true")
            true
        } else {
            AiEliminateSwitcherManager().isInteractiveSegmentSupport().also {
                GLog.d(TAG, "[isInteractiveSegmentSupport] isSupport = $it")
            }
        }
    }


    /**
     * 是否支持外屏连拍接续功能，只在13.2及以上支持
     */
    private val isSupportCshotContinuation: Boolean by lazy {
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_2)
    }

    /**
     * 是否支持quick图删除，目前quick图删除采用打标记+延迟回收的方案，只在14.0及以上机型
     */
    private val isQuickPhotoDeleteSupported: Boolean by lazy {
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0) || GProperty.DEBUG_QUICK_PHOTO_DELETE
    }

    /**
     * 是否支持外屏授权功能，只在13.2及以上支持
     */
    private val isSupportAuthorizingOnSecondaryDisplay: Boolean by lazy {
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_2)
    }

    /**
     * 是否支持AI 调色
     */
    private val isSupportAiFilter: Boolean by lazy {
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0_1).not()
    }

    private val isPrimaryUser: Boolean by lazy { UserManagerUtils.isPrimaryUser() }

    private val isContainerUser: Boolean by lazy { ContainerManagerUtils.isCurrentContainerUser() }

    private val isChildrenMode: Boolean by lazy {
        Settings.Global.getInt(context.contentResolver, keyChildrenModeOn, 0) == 1
    }

    private val isSupportDolbyDecode: Boolean by lazy {
        val isDolbyDecodeDevice = if (configAbility.contains(IS_DOLBY_DECODE_DEVICE)) {
            configAbility.getBooleanConfig(IS_DOLBY_DECODE_DEVICE) ?: false
        } else {
            CodecHelper.isSupportCodecWithSpecifyMimeType(MediaFormat.MIMETYPE_VIDEO_DOLBY_VISION, false).also {
                configAbility.setBooleanConfig(IS_DOLBY_DECODE_DEVICE, it)
            }
        }
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_1) && isDolbyDecodeDevice
    }

    private val isSupportDolbyEncode: Boolean by lazy {
        val isDolbyEncodeDevice = if (configAbility.contains(IS_DOLBY_ENCODE_DEVICE)) {
            configAbility.getBooleanConfig(IS_DOLBY_ENCODE_DEVICE) ?: false
        } else {
            CodecHelper.isSupportCodecWithSpecifyMimeType(MediaFormat.MIMETYPE_VIDEO_DOLBY_VISION, true).also {
                configAbility.setBooleanConfig(IS_DOLBY_ENCODE_DEVICE, it)
            }
        }
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_1) && isDolbyEncodeDevice
    }

    /**
     * 是否支持杜比的提速编码:
     * 1.支持的情况下，设备能够支撑美摄SDK根据[android.media.MediaCodecInfo.VideoCapabilities.getSupportedFrameRatesFor]返回值来为MediaCodec
     *   设置更高的[android.media.MediaFormat.KEY_OPERATING_RATE]值，以便尽可能的快地完成编码；
     * 2.不支持的情况下，设备无法支撑美摄SDK为MediaCodec设置的KEY_OPERATING_RATE值，应该交由MediaCodec自行决定处理速度
     */
    private val isSupportDolbyEncodeAccelerate: Boolean by lazy {
        CodecHelper.isSupportEncodeAccelerate(DOLBY_VIDEO_TYPE)
    }

    private val isSupportHlgEncode: Boolean by lazy {
        MediaCodecDetector.isSupportCodecForMediaType(CodecMediaType.HLG)
    }

    /**
     * 是否支持侧边栏
     *
     * 蜻蜓是折叠屏,但均是小屏的不需要支持侧边导航栏
     *
     * 平板及普通折叠屏是均支持侧边导航栏
     */
    private val isSupportSidePane: Boolean by lazy {
        isTablet || (isFold && !isFoldRemapDisplayDisabled)
    }

    private val isUserUnlocked: Boolean by lazy {
        !(configAbility.getBooleanConfig(SystemInfo.IS_FBE_VERSION) ?: false) && UserManagerUtils.isUserUnLocked()
    }

    private val isMultiSystemUser: Boolean by lazy {
        OplusMultiUserManagerWrapper.isMultiSystemUserHandle(Process.myUserHandle())
    }

    /**
     * 个人信息保护界面，是否特殊处理公司信息
     * 一加 + 内销 + android T + 无feature，特殊处理，显示oppo
     * 其余显示默认
     */
    private val isShowSpecialCompanyName: Boolean by lazy {
        if (PROPERTY_IS_OP_BRAND
            && (configAbility.getBooleanConfig(IS_REGION_CN) == true)
            && (Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2)
        ) {
            hasSystemFeature(keyIsCompatOldMainBodyName).not()
        } else {
            false
        }
    }

    private val isMtkPlatform: Boolean by lazy {
        Build.HARDWARE?.matches(Regex(MTK_REGEX_PATTERN)) ?: false
    }

    private val isQualcommPlatform: Boolean by lazy {
        Pattern.matches(QCOM_VENDER, Build.HARDWARE)
    }

    private val isTablet by lazy {
        hasOplusFeature(context, keyIsTablet, keyIsTablet)
    }

    private val isFold by lazy {
        hasOplusFeature(context, keyIsFold, keyIsFold)
    }

    private val isFoldRemapDisplayDisabled by lazy {
        hasOplusFeature(context, keyIsFoldRemapDisplayDisabled, keyIsFoldRemapDisplayDisabled)
    }

    private val isBackLightOptimize: Boolean by lazy {
        configAbility.getBooleanConfig(SystemInfo.IS_SUPPORT_BACKLIGHT_OPTIMIZE) ?: false
    }

    private val isSupportHighBrightness: Boolean by lazy {
        configAbility.getBooleanConfig(SystemInfo.IS_SUPPORT_HIGH_BRIGHTNESS) ?: false
    }

    private val isSupportHDR: Boolean by lazy {
        for (platform in DISABLE_HDR_PLATFORM) {
            // MTK在codec2的架构上(Android S)，已经不支持通过解码器处理SurfaceTexture场景下的HDR效果
            // 相册判断在S以上的机器上HDR效果生效，非S的机器上采用MTK的HDR效果
            !(Build.HARDWARE == platform && !ApiLevelUtil.isAtLeastAndroidS())
        }
        true
    }

    /**
     * 是否支持合影优化
     *
     * 1. OS版本14.0.1及以上
     * 2. 相机支持合影优化功能
     * 3. 云端总控开关打开
     * 4. 该机型不在Feature的机型黑名单内
     */
    private val isSupportGroupPhoto: Boolean by lazy {
        when (GProperty.DEBUG_GROUP_PHOTO) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                // 合影优化Feature开关
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.GROUP_PHOTO),
                    iFeatureBasicCondition = {
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0_1)
                    },
                    iCloudFeatureGetter = LegacyCloudFeatureGetter {
                        GroupPhotoSwitchManager().isCloudSwitchOn
                    }
                ).get()
            }
        }
    }


    /**
     * 是否支持olive2.0编辑 封面帧增强算法
     * 1. OS版本15.0及以上
     * 2. find/reno/一加数字系列/一加ace系列
     */
    private val isSupportOliveCoverFrameEnhancer: Boolean by lazy {
        when (GProperty.DEBUG_OLIVE_PHOTO_COVER_FRAME_ENHANCER) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = {
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0) && SeriesUtils.checkSeries(
                            configAbility.getStringConfig(SystemInfo.PRODUCT_SERIES).toString(),
                            SeriesType.SERIES_FIND,
                            SeriesType.SERIES_RENO,
                            SeriesType.SERIES_ONEPLUS,
                            SeriesType.SERIES_ONEPLUS_ACE
                        )
                    }
                ).get()
            }
        }
    }

    /**
     * 是否支持Olive的补帧
     *
     * 1. OS版本15.0及以上
     * 2. find/reno/数字/ace系列
     * 3. 中高端机
     */
    private val isSupportOliveFrameFiller: Boolean by lazy {
        when (DEBUG_OLIVE_FRAME_FILL) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                // 补帧的Feature开关
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = {
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
                                && ApiLevelUtil.isFirstApiLevelAtLeastV()
                                && ((PerformanceLevelUtils.animationLevelV2 == PerformanceLevel.MIDDLE) ||
                                    (PerformanceLevelUtils.animationLevelV2 == PerformanceLevel.HIGH))
                    }
                ).get()
            }
        }
    }

    /**
     * 是否支持Olive滑动播放
     * 1,平台属于非低动效等级
     * 2,非单解码器设备
     */
    private val isSupportOliveSlidePlay: Boolean by lazy {
        when (DEBUG_OLIVE_SLIDE_PLAY) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = {
                        isSingleCodecDevice.not()
                                && isSingleSurfaceViewDevice.not()
                                && ((PerformanceLevelUtils.animationLevelV2 == PerformanceLevel.MIDDLE)
                                || (PerformanceLevelUtils.animationLevelV2 == PerformanceLevel.HIGH))
                    }
                ).get()
            }
        }
    }

    /**
     * 大图 HDR视频的Olive 是否支持 下变换
     *
     * - 1、至少 OS15.0.1
     * - 2、支持下变换HDR的下变换算法
     */
    private val isSupportPhotoPageHdrVideoOliveTransform: Boolean by lazy {
        when (GProperty.DEBUG_PHOTO_PAGE_HDR_VIDEO_OLIVE_SUPPORT_TRANSFORM) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = {
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0) && isUHdrTransformSupport
                    }
                ).get()
            }
        }
    }

    /**
     * 是否支持Olive的heif
     *
     * 1. 非轻量os
     * 2. OS版本15.0及以上
     * 3. 支持Olive
     */
    private val isSupportOliveHeif: Boolean by lazy {
        when (GProperty.DEBUG_OLIVE_PHOTO) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                // Olive的HEIF的Feature开关
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    osFeatureGetter = AndroidFeatureGetter(context, FEATURE_SUPPORT_LIVE_PHOTO),
                    iFeatureBasicCondition = {
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
                    }
                ).get()
            }
        }
    }

    /**
     * 是否支持Olive应用特效后保存新图还是LivePhoto
     *
     * 1.支持OLIVE
     * 3.支持参数化保存，编辑新框架
     * 4.非轻量OS
     * 5.非RealMe设备
     * 6.指定项目支持，目前仅支持这些项目，线上配置【23205， 24021， 24023， 24261， 24222， 24267， 24263， 24264】
     */
    private val isSupportSaveOlive: Boolean by lazy {
        if (DEBUG_OLIVE_EDIT_TEST_MODEL) {
            //olive编辑的测试模式默认打开，用于各种机型进行摸底，开关默认的关闭的。
            true
        } else {
            when (GProperty.DEBUG_OLIVE_PHOTO) {
                FEATURE_FORCE_ENABLE -> true
                FEATURE_FORCE_DISABLE -> false
                else -> {
                    // fov的Feature开关
                    FeatureGetter(
                        context = context,
                        configAbility = configAbility,
                        iFeatureBasicCondition = {
                            isSupportOLive && isSupportProjectSave
                        }
                    ).get()
                }
            }
        }
    }

    /**
     * 是否支持将OLive中SDR的Video选帧之后，生成ProXDR的封面。
     */
    private val isSupportOliveSDR2HDR: Boolean by lazy {
        when (GProperty.DEBUG_OLIVE_SDR_TO_HDR) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                // fov的Feature开关
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    iFeatureBasicCondition = {
                        isUHdrEditSupported
                    }
                ).get()
            }
        }
    }

    /**
     * 是否支持Ai 超清
     */
    private val isSupportAihd: Boolean by lazy {
        if (GProperty.DEBUG_AIHD) {
            true
        } else {
            val isSupport = AihdSwitchManager().isCloudSwitchOn && hasAppFeature(context, keySupportAihd, keySupportAihd)
                    && (configAbility.getBooleanConfig(IS_REALME_BRAND) == true)
            GLog.d(TAG, LogFlag.DL) { "[isSupportAihd] : $isSupport" }
            isSupport
        }
    }

    /**
     * 是否支持Ai 涂鸦
     */
    private val isSupportAiGraffiti: Boolean by lazy {
        if (GProperty.DEBUG_AI_GRAFFITI) {
            true
        } else {
            val isSupport = AIGraffitiSwitchManager().isCloudSwitchOn && hasAppFeature(context, keySupportAIGraffiti, keySupportAIGraffiti)
            GLog.d(TAG, LogFlag.DL) { "[isSupportAiGraffiti] : $isSupport" }
            isSupport
        }
    }

    /**
     * 是否支持Ai 超清 大图入口
     */
    private val isSupportAihdPage: Boolean by lazy {
        GProperty.DEBUG_AIHD_PAGE && isSupportAihd
    }

    /**
     * 是否支持Ai 人脸超清
     * realme的 rado和oris项目起所有新的外销的轻量OS
     */
    private val isSupportAiFaceHd: Boolean by lazy {
        if (GProperty.DEBUG_AI_FACE_HD) {
            true
        } else {
            hasAppFeature(context, keySupportAiFaceHd, keySupportAiFaceHd)
                    && (configAbility.getBooleanConfig(IS_REALME_BRAND) == true)
        }
    }

    /**
     * 一碰分享是否被系统支持
     *
     * 系统版本支持范围：
     * 1. OS15.0及以上， 轻量和非轻量，均支持；
     * 2. OS14.0及以上， 仅非轻量支持；
     * 3. 其余不支持；
     *
     * 其他必要依赖支持：
     * 1. 支持OShare
     * 2. 支持NFC（跟PTC保持一致，使用"android.hardware.nfc.any"判断）|| 平板设备（平板是外接NFC键盘，不需要判断NFC）
     */
    private val isOneTouchShareSupported: Boolean by lazy {
        (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
            || (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0) && isProductLight.not()))
            && FeatureUtils.isSupportOplusShare && (hasSystemFeature(FEATURE_NFC_ANY) || (configAbility.getBooleanConfig(IS_TABLET) == true))
    }

    /**
     * 是否支持最近删除加密入口
     * 设置app中是否含有指定meta-data
     * && 主用户(即非系统分身、非多用户子用户)
     */
    private val isSupportRecycleAlbumPasswd: Boolean by lazy {
        // 设置app中包含此meta-data，说明支持[最近删除验证]开关
        val isSupportRecentlyDeletedSwitch =
            PackageInfoUtils.getMetaData<Boolean>(AppConstants.Package.PACKAGE_NAME_SETTINGS, KEY_RECENTLY_DELETED_METADATA) ?: false

        isSupportRecentlyDeletedSwitch && isPrimaryUser
    }

    private val isSingleCodecDevice: Boolean
        get() = Config.CODEC_SINGLE_CODEC_BOARD_PLATFORMS.contains(configAbility.getStringConfig(SystemInfo.BOARD_PLATFORM, EMPTY_STRING))

    private val isSingle4kDolbyCodecDevice: Boolean by lazy {
        Config.SINGLE_4K_DOLBY_CODEC_DEVICES.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    private val isSingleDolbyCodecDevice: Boolean by lazy {
        Config.SINGLE_DOLBY_CODEC_DEVICES.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    private val isSingle4KCodecDevice: Boolean by lazy {
        getSingle4KCodeDevices(Config.CODEC_SINGLE_CODEC_DEVICES_BY_RESOLUTION).contains(
            CPUInfoUtils.getCpuPartNumber(
                { configAbility.getStringConfig(CPU_INFO_PART_NUMBER) },
                { configAbility.getStringConfig(CPU_INFO) })
        )
    }

    private val isPhotoQuickShortAnim: Boolean by lazy {
        Config.PHOTO_QUICK_SHORT_ANIM.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    private val getHasselDevicesFromCloud: List<String> by lazy {
        getHasselDevicesFromCloud()
    }

    private fun getSingle4KCodeDevices(arrays: List<String>): List<String> {
        val singleCodeDevices = mutableListOf<String>()
        arrays.forEach {
            it.split(TextUtil.SPLIT_LINE).toTypedArray().also { devices ->
                if (devices[INDEX_RESOLUTION].toInt() >= MAX_VIDEO_SIZE_FOR_4K) {
                    singleCodeDevices.add(devices[INDEX_DEVICE])
                }
            }
        }
        return singleCodeDevices
    }

    private fun getHasselDevicesFromCloud(): List<String> {
        val cloudConfigStr = CloudParamConfig.getDefaultGroupValue(
            MODULE_HASSEL_WATERMARK,
            DEVICES_SUPPORT_HASSEL_EDIT
        ) as? String
        if (cloudConfigStr.isNullOrEmpty()) {
            GLog.d(TAG) { "[getCloudConfigs] $DEVICES_SUPPORT_HASSEL_EDIT form cloud is empty" }
            return context.getStringArraySafe(LibUtilR.array.hassel_devices_from_cloud).toList()
        }
        GLog.d(TAG) { "[getCloudConfigs] $DEVICES_SUPPORT_HASSEL_EDIT form cloud is $cloudConfigStr" }
        return runCatching {
            cloudConfigStr.split(GroupResponseBean.ARRAY_SPLIT)
        }.onFailure {
            GLog.e(TAG) { "[getCloudConfigs] parse ${Codec.CLOUD_HASSEL_DEVICES} data err" }
        }.getOrDefault(
            context.getStringArraySafe(LibUtilR.array.hassel_devices_from_cloud).toList()
        )
    }

    /**
     * Q以上需要处理政企模式，是否支持分享功能需要调用系统提供的接口判断
     * Q及以下不需要处理政企模式，默认支持分享功能
     */
    private fun isGalleryShareDisabled(): Boolean {
        kotlin.runCatching {
            return OplusDevicepolicyManagerWrapper.getBoolean(
                KEY_METHOD_FILE_SHARED_DISABLED,
                OplusDevicepolicyManagerWrapper.CUSTOMIZE_DATA_TYPE, false
            )
        }.onFailure {
            GLog.e(TAG, "isGalleryShareDisabled error, $it")
        }
        return false
    }

    private fun isOshareDisabled(): Boolean {
        return (context.packageManager?.getApplicationEnabledSetting(OSHARE_PKG)) == PackageManager.COMPONENT_ENABLED_STATE_DISABLED
    }

    /**
     * 分享页一碰分享引导动画是否使用PTC.NFC动画
     */
    private fun isShareGuideUsePtcNfcAnim(): Boolean {
        return AppFeatureProviderUtils.getInt(context.contentResolver, keyPtcNfcJsonAnimation, INTEGER_0) == INTEGER_1
    }

    /**
     * android S 及以上版本时，isPrivateSafeDisabled()方法才会生效。
     * android S 以下版本时，返回false，表示此方法不管控SafeBox的功能。
     */
    private fun isSafeBoxDisabled() =
        if (ApiLevelUtil.isAtLeastAndroidS()) OplusCustomizeRestrictionManagerWrapper.isPrivateSafeDisabled(context) else false

    /**
     * 图集加密功能只支持os15及以上
     * 私密保险箱版本号为15.4.0时，才支持图集加密功能
     * @return boolean true 支持图集加密，false 不支持图集加密
     */
    private fun isSupportAlbumEncryption() = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
        && AppVersionUtils.getVersionCode(context, FILE_ENCRYPTION_PKG) >= VERSION_FILE_ENCRYPTION

    /**
     * 大缓存模式级数
     * 定义：比8G内存大多少倍，则是对应级数
     * 注意：余数应该落在 6< y <= 8G 才算作多一倍，(使用-8+2再对结果向下取整)示例如下：
     * x < 14 level 为 0
     * 14 <= x < 22 level 为 1
     * 22 <= x < 30 level 为 2
     * ...
     */
    private val bigCacheModeLevel: Int by lazy {
        if (DEBUG_BIG_CACHE_MODE_LEVEL > 0) {
            DEBUG_BIG_CACHE_MODE_LEVEL
        } else {
            val curTotalMem = ActivityManagerUtils.getMemoryInfo().totalMem
            floor((curTotalMem - MEM_6G).toFloat() / MEM_8G).toInt().coerceAtLeast(0)
        }
    }

    /**
     * 根据系统内存大小，限制使用贴纸数量:
     * totalMem < 8G ,10张;
     * 8G <= totalMem < 16G ,20张;
     * 16G < totalMem ,30张.
     */
    private val stickerQuantityLimit: Int by lazy {
        val curTotalMem = ActivityManagerUtils.getMemoryInfo().totalMem
        when {
            curTotalMem < MEM_8G -> STICKER_LIMIT_LOW
            ((MEM_8G <= curTotalMem) && (curTotalMem < MEM_16G)) -> STICKER_LIMIT_MEDIUM
            MEM_16G <= curTotalMem -> STICKER_LIMIT_HIGH
            else -> STICKER_LIMIT_LOW
        }
    }

    /**
     * 大图过渡动画是否支持Render动画。后者是直接跑在RenderThread上的，不会因为主线程插事件而掉帧。
     * 注：Version>=V上才能启用。V以下会直接crash。
     */
    private val isSupportPhotoTransitionRenderAnim: Boolean by lazy {
        when (GProperty.DEBUG_ENABLE_PHOTO_TRANSITION_RENDER_ANIM) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                // Render异步动画器仅在V及以上可以。否则会crash (NoSuchMethodError)
                OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
            }
        }
    }

    private val isSupportSeniorPicked: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.SENIOR_PICKED),
            iFeatureBasicCondition = {
                /**
                 * 1、Android 版本必须大于或等于 R ,
                 * 2、系统是 大于或等于 os13.1 则支持 ,
                 * 3、如果小于 os13.1，系统内存大于或等于 6G 则支持
                 * 4、如果小于 os13.1，系统内存小于 6G 则不支持
                 */
                !(OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_1).not() && LowMemUtils.isRamLessThan6G())
            },
        ).get()
    }

    /**
     * 是否为折叠屏 && 搭载双屏的设备
     * Mark by liyunting 当前根据该判断条件处理小折叠屏(蜻蜓/火烈鸟)图片显示问题(摩尔纹、噪点等)
     * Tile重构的时候需要考虑是否移除该条件，在PhotoPager仓中统一处理level的计算
     */
    private val isMultiDisplayDevice: Boolean by lazy {
        (isFold && isFoldRemapDisplayDisabled)
    }

    /**
     * 判断当前设备是否为不支持大图Tile Level优化计算的设备
     */
    private val isDisableTileLevelOptimizationDevices: Boolean by lazy {
        Config.DISABLE_TILE_LEVEL_OPTIMIZATION_DEVICES.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    /**
     * 是否支持大图Tile Level优化计算
     * 1.正常情况下，设备是支持Tile优化的；
     * 2.在一些特殊的设备上，比如蜻蜓/火烈鸟/lijing-A系列设备上，不支持Tile优化，因为副屏(蜻蜓/火烈鸟)或者主屏(lijing-A系列) 上查看大图，摩尔纹现象比较严重
     */
    private val isSupportTileLevelOptimization: Boolean by lazy {
        (isMultiDisplayDevice.not() && isDisableTileLevelOptimizationDevices.not())
    }

    /**
     * 是否支持融合搜索
     * 1.内销
     * 2.非轻量OS
     * 3.简体中文-大陆
     * 4.RUS名单开关
     * 5.>= os14
     */
    private val isSupportAndesSearch: Boolean
        get() {
            if (DEBUG_SEARCH) {
                GLog.d(TAG, LogFlag.DL) { "isSupportAndesSearch, debug, search mode=${SearchConst.SEARCH_MODE}" }
                when (SearchConst.SEARCH_MODE) {
                    SearchConst.SEARCH_MODE_FORCE_TRADITION_SEARCH -> return false
                    SearchConst.SEARCH_MODE_FORCE_ANDES_SEARCH -> return true
                    SearchConst.SEARCH_MODE_NORMAL -> {}
                }
            }
            val isOsVersionEnabled = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
            val isMatchCN = (configAbility.getBooleanConfig(IS_REGION_CN) ?: false) && LocaleUtils.isChinaMainland(context)
            val isAndesSearchEnabled = AndesSearchRusConfig.andesSearchConfig?.isAndesSearchEnabled ?: false
            val andesSearchFeature =  FeatureGetter(
                context = context,
                configAbility = configAbility,
                osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.ANDES_SEARCH),
                iFeatureBasicCondition = { isOsVersionEnabled && isMatchCN },
                iCloudFeatureGetter = LegacyCloudFeatureGetter { isAndesSearchEnabled }
            ).get()
            val isSupportAndesSearch = andesSearchFeature && !isProductLight
            GLog.d(TAG, LogFlag.DL) { "isSupportAndesSearch, result=$isSupportAndesSearch, andesSearchFeature=$andesSearchFeature, " +
                    "isOsVersionEnabled=$isOsVersionEnabled, isMatchCN=$isMatchCN, isAndesSearchEnabled=$isAndesSearchEnabled, " +
                    "isProductLight=$isProductLight" }
            return isSupportAndesSearch
        }

    /**
     * 只是用于实现，在内销范围内所有语言都可以显示 caption 个保
     */
    private val isSupportAndesSearchNotLimitLanguage: Boolean
        get() {
            if (DEBUG_SEARCH) {
                GLog.d(TAG, LogFlag.DL) { "isSupportAndesSearch, debug, search mode=${SearchConst.SEARCH_MODE}" }
                when (SearchConst.SEARCH_MODE) {
                    SearchConst.SEARCH_MODE_FORCE_TRADITION_SEARCH -> return false
                    SearchConst.SEARCH_MODE_FORCE_ANDES_SEARCH -> return true
                    SearchConst.SEARCH_MODE_NORMAL -> {}
                }
            }
            val isOsVersionEnabled = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_0)
            val isAndesSearchEnabled = AndesSearchRusConfig.andesSearchConfig?.isAndesSearchEnabled ?: false
            val andesSearchFeature =  FeatureGetter(
                context = context,
                configAbility = configAbility,
                osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.ANDES_SEARCH),
                iFeatureBasicCondition = { isOsVersionEnabled && isRegionCN },
                iCloudFeatureGetter = LegacyCloudFeatureGetter { isAndesSearchEnabled }
            ).get()
            val isSupportAndesSearch = andesSearchFeature && !isProductLight
            GLog.d(TAG, LogFlag.DL) { "isSupportAndesSearch, result=$isSupportAndesSearch, andesSearchFeature=$andesSearchFeature, " +
                    "isOsVersionEnabled=$isOsVersionEnabled, isRegionCN=$isRegionCN, isAndesSearchEnabled=$isAndesSearchEnabled, " +
                    "isProductLight=$isProductLight" }
            return isSupportAndesSearch
        }

    /**
     * 是否支持dimming提亮
     */
    private val isSupportLHDROnlyDimming: Boolean by lazy {
        hasOplusFeature(context, keySupportLHdrOnlyDimming, keySupportLHdrOnlyDimming)
    }

    /**
     * 是否支持Edr使用新的背光通路
     * 通过SF提供的属性和Android U版本进行判断
     * 备注：新的背光通路使用到的接口至少Android U及以上，这里添加上Android U的判断，防止出现异常
     */
    private val isSupportEdrListener: Boolean by lazy {
        val isAtLeastAndroidU = ApiLevelUtil.isAtLeastAndroidU()
        val isSupportEdr = GallerySystemProperties.getBoolean(keySupportEdrListener, false)
        (isAtLeastAndroidU && isSupportEdr).also {
            GLog.d(TAG) {
                "[isSupportEdrListener] it is supported edr listener：$it, " +
                    "isAtLeastAndroidU:$isAtLeastAndroidU, " +
                    "isSupportEdr:$isSupportEdr"
            }
        }
    }

    /**
     * 是否支持提高编解码器的速率,需满足如下条件
     * 1，初始api版本大于等于U
     * 2，一加机型（Oppo机型在美摄SDk内部已经开发，本次先尝试开放一加机型）
     * 3，不是黑名单芯片机型
     */
    private val isSupportUseOperateRate: Boolean by lazy {
        ApiLevelUtil.isFirstApiLevelAtLeastU()
            && (configAbility.getBooleanConfig(IS_ONEPLUS_BRAND, false) ?: false)
            && (Config.VIDEO_USE_OPERATE_RATE_CPU_BLACKLIST.contains(
            CPUInfoUtils.getCpuPartNumber(
                { configAbility.getStringConfig(CPU_INFO_PART_NUMBER) },
                { configAbility.getStringConfig(CPU_INFO) }
            ))
            ).not()
    }

    /**
     * 大图页是否支持展示"将登机卡添加到Google Wallet"入口
     */
    private val isSupportGooglePassScan: Boolean
        get() {
            // debug开关
            if (GProperty.DEBUG_GOOGLE_PASS_SCAN) {
                GLog.d(TAG, LogFlag.DL) { "[isSupportGooglePassScan] is support from debug command" }
                return true
            }

            return FeatureGetter(
                context = context,
                configAbility = configAbility,
                iFeatureBasicCondition = {
                    OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
                        && (configAbility.getBooleanConfig(IS_ONEPLUS_BRAND) ?: false)
                        && isDomestic.not() && isProductLight.not()
                    // 2024.10.10 需求变更：coloros15.0+，且 oneplus 外销，都要支持该功能。 && GooglePassScanFeatureHelper.isPlatformSupport(context, configAbility)
                }
            ).get().also {
                GLog.d(TAG, LogFlag.DL) {
                    "[isSupportGooglePassScan] support:$it， osVersion:${OSVersionUtils.oplusOsVersion}," +
                        "is_oneplus_brand:${configAbility.getBooleanConfig(IS_ONEPLUS_BRAND) ?: false}" +
                        "export:${isDomestic.not()}"
                }
            }
        }

    /**
     * 是否支持AI构图
     */
    private val isSupportAiComposition: Boolean
        get() {
            // Debug开关
            if (BuildConfig.DEBUG && DEBUG_AI_COMPOSITION) {
                GLog.d(TAG, LogFlag.DL) { "[isSupportAiComposition] is support from debug command" }
                return true
            }
            return FeatureGetter(
                context = context,
                configAbility = configAbility,
                osFeatureGetter = AppFeatureGetter(context, BasicOsFeature.AI_COMPOSITION, true),
                iFeatureBasicCondition = { AIFuncFeatureHelper.isAiCompositionSupport(configAbility) }
            ).get().also {
                GLog.d(TAG, LogFlag.DL) {
                    "[isSupportAiComposition] support:$it， osVersion:${OSVersionUtils.oplusOsVersion}," +
                        "product_series:${configAbility.getStringConfig(SystemInfo.PRODUCT_SERIES)}"
                }
            }
        }

    /**
     * 是否支持 Google 云服务（本地）
     */
    private val isSupportGoogleCloudLocal: Boolean by lazy {
        if (GProperty.DEBUG_GOOGLE_CLOUD) {
            return@lazy true
        }

        GLog.i(TAG) { "isSupportGoogleCloudLocal, start" }
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            iFeatureBasicCondition = {
                /**
                 * 海外发售区域，除土耳其，俄罗斯，白俄罗斯外
                 */
                GLog.d(
                    TAG,
                    "isSupportGoogleCloudLocal regionmark[${configAbility.getStringConfig(REGION_MARK)}]," +
                        " TU-${configAbility.getBooleanConfig(IS_REGION_TURKEY)}," +
                        " RU-${configAbility.getBooleanConfig(IS_REGION_RUSSIA)}," +
                        " BE-${configAbility.getBooleanConfig(IS_REGION_BELARUS)}"
                )
                configAbility.getBooleanConfig(IS_REGION_CN)?.not() ?: false
                    && configAbility.getBooleanConfig(IS_REGION_TURKEY)?.not() ?: false
                    && configAbility.getBooleanConfig(IS_REGION_RUSSIA)?.not() ?: false
                    && configAbility.getBooleanConfig(IS_REGION_BELARUS)?.not() ?: false
            }
        ).get().also {
            GLog.i(TAG) { "isSupportGoogleCloudLocal=$it" }
        }
    }

    /**
     * 判定是否支持OLive
     */
    private val isSupportOLive: Boolean by lazy {
        when (GProperty.DEBUG_OLIVE_PHOTO) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> {
                FeatureGetter(
                    context = context,
                    configAbility = configAbility,
                    osFeatureGetter = AndroidFeatureGetter(
                        context,
                        FEATURE_SUPPORT_LIVE_PHOTO
                    ),
                    iFeatureBasicCondition = {
                        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_1_0)
                    }
                ).get().also {
                    GLog.i(TAG, "isSupportOLive=$it")
                }
            }
        }
    }

    /**
     * 是否支持OLive的HDR视频（视频播放也提亮）的功能
     *
     * 默认返回true
     */
    private val isSupportOliveHdrVideo: Boolean by lazy {
        when (GProperty.DEBUG_OLIVE_PHOTO_HDR_VIDEO) {
            FEATURE_FORCE_ENABLE -> true
            FEATURE_FORCE_DISABLE -> false
            else -> true
        }.also {
            GLog.d(TAG, LogFlag.DL) { "isSupportOLiveHdrVideo=$it" }
        }
    }

    /**
     * 是否支持LUMO凝光影像水印
     * 从包括珠湖海起，往后的所有Find机型
     */
    private val isSupportLumoWatermark: Boolean by lazy {
        // 检查出厂OS版本是否支持
        val isApiLevelSupported = ApiLevelUtil.isFirstApiLevelAtLeastV()

        val isFeatureSupport = FeatureGetter(
            context = context,
            configAbility = configAbility,
            iFeatureBasicCondition = {
                OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_1) && SeriesUtils.checkSeries(
                    configAbility.getStringConfig(SystemInfo.PRODUCT_SERIES).toString(),
                    SeriesType.SERIES_FIND
                )
            }
        ).get()
        val isModelSupport = Config.CONFIG_LUMO_IMAGE_MODEL_BLACKLIST.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING)).not()
        isApiLevelSupported && isFeatureSupport && isModelSupport
    }

    /**
     * 判断是否卖场模式
     */
    private fun isSellMode(): Boolean {
        return if (OplusFeatureConfigManagerWrapper.hasFeature(ContextGetter.context, FEATURE_SALEMODE_LEAST_R)) {
            true
        } else {
            ContextGetter.context.packageManager.hasSystemFeature(FEATURE_SALEMODE_Q)
        }
    }

    /**
     * 底软配置的Feature，用于标记当前机型，是否可以配置VelocityTracker的strategy为Impulse。
     * 功能主要是屏幕有水滴场景下，避免滑动出现回钩的现象。
     */
    private val isSupportVelocityTrackerStrategyImpulse: Boolean by lazy {
        hasOplusFeature(
            context,
            OPLUS_FEATURE_SUPPORT_VELOCITYTRACKER_STATEGY_IMPULSE,
            OPLUS_FEATURE_SUPPORT_VELOCITYTRACKER_STATEGY_IMPULSE
        )
    }

    /**
     * 是否是轻量低
     */
    private val isProductLightLow: Boolean by lazy {
        hasOplusFeature(context, keyProductLightLow, keyProductLightLow)
    }

    /**
     * 是否是轻量高
     */
    private val isProductLightHigh: Boolean by lazy {
        hasOplusFeature(context, keyProductLightHigh, keyProductLightHigh)
    }

    /**
     * 是否是轻量os
     */

    private val isProductLight: Boolean by lazy {
        GLog.d(TAG,LogFlag.DL,"isProductLightLow == $isProductLightLow isProductLightHigh == $isProductLightHigh")
        isProductLightLow || isProductLightHigh
    }


    /**
     * 是否为支持进入大图做增强提亮(Barley项目)
     * 备注：用于某些项目上常规场景(常规场景不包含从相机进入相册)进入相册大图做增强提亮策略，此策略与常规亮度模式策略区别在于：
     * 1.原先的NORMAL提亮模式逻辑保持不变，提亮亮度是在现在亮度的基础上再提亮50%.
     * 2.为了避免反复打开照片时反复提亮影响体验，最终做的是退出相册APP时取消提亮.
     */
    private val isSupportAloneBrightnessEnhance: Boolean by lazy {
        hasAppFeature(context, keyOpenImageBrightnessEnhance, keyOpenImageBrightnessEnhance)
    }

    /**
     * 是否支持系统设置搜索
     * 1：OS系统在15.0及以上
     * 2：内销
     */
    private val isSupportSystemSettingSearch: Boolean by lazy {
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            iFeatureBasicCondition = {
                OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0) && (configAbility.getBooleanConfig(IS_REGION_CN) == true)
            }
        ).get().also {
            GLog.i(TAG, "isSupportSystemSettingSearch=$it")
        }
    }

    /**
     * 是否支持系统设置显示插入项
     * OS系统在15.0及以上
     */
    private val isSupportSystemSettingDisplayInsertion: Boolean by lazy {
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
    }

    /**
     * 是否支持项目化保存需要同时满足以下条件
     * 1.OS系统在14.1及以上
     * 2.动画等级大于等于B+ 或者 动画等级为B且未Reno系列（目前只有milkway S3 4G）
     * 3.轻量高OS也支持，条件2和3满足任意一个就可
     */
    private val isSupportProjectSave: Boolean by lazy {
        if (GProperty.DEBUG_PARAMETRIC_PROJECT) {
            GLog.d(TAG, LogFlag.DL, "[isSupportProjectSave] result true, debug switch open")
            true
        } else {
            val osSupport = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_14_1_0)
            val isLowLevel = GallerySystemProperties.getInt(KEY_OPLUS_ANIM_LEVEL, DEFAULT_LEVEL) > AppConstants.Number.NUMBER_3
            val isReno = SeriesUtils.checkSeries(
                configAbility.getStringConfig(SystemInfo.PRODUCT_SERIES).toString(),
                SeriesType.SERIES_RENO
            )
            (osSupport && (isLowLevel.not() || (isReno && isLowLevel) || isProductLightHigh)).also {
                GLog.d(TAG, LogFlag.DL) {
                    "[isSupportProjectSave] result = $it, osSupport = $osSupport, isLowLevel = $isLowLevel, isReno = $isReno}"
                }
            }
        }
    }

    /**
     * 是否支持显示安全分享
     */
    private val isSupportShowSecurityShare: Boolean by lazy {
        true
    }

    private val isDomestic: Boolean by lazy {
        ContextGetter.context.getBooleanSafe(R.bool.property_domestic)
    }

    private fun hasSystemFeature(featureName: String): Boolean {
        return context.packageManager.hasSystemFeature(featureName)
    }

    /**
     * 是否支持调整图片的提亮速率
     */
    private val isSupportAdjustImageEdrRate: Boolean by lazy {
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
    }

    /**
     * 是否支持OLive图片与视频切换时调整提亮速率
     */
    private val isSupportAdjustOLiveEdrRate: Boolean by lazy {
        Config.CONFIG_OLIVE_ADJUST_EDR_RATE_MODEL_BLACKLIST.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING)).not()
    }

    /**
     * 是否支持延后隐藏无缝动画100ms
     */
    private val isSupportDelayHideSeamlessTransition100: Boolean by lazy {
        Config.CONFIG_SUPPORT_DELAY_100_HIDE_SEAMLESS_TRANSITION_MODEL_LIST.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    /**
     * 是否支持延后隐藏无缝动画1500ms
     */
    private val isSupportDelayHideSeamlessTransition1500: Boolean by lazy {
        Config.CONFIG_SUPPORT_DELAY_1500_HIDE_SEAMLESS_TRANSITION_MODEL_LIST.contains(configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING))
    }

    /**
     * 通过尝试查询一次媒体库的表示，观察是否有KEY_CALL_MEDIA_STORE字段
     * @WorkerThread
     */
    private val isSupportMediaStoreCameraQuickUri by lazy {
        var result = false
        runCatching {
            val baseUri = MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL)
            context.contentResolver.query(
                baseUri,
                arrayOf(FastCaptureUtils.KEY_MEDIA_STORE_DJ_QUICK_URI),
                null,
                null
            )?.use {
                if (it.getColumnIndex(FastCaptureUtils.KEY_MEDIA_STORE_DJ_QUICK_URI) >= 0) {
                    result = true
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "configList media store camera quick uri is error," + it.message }
        }
        result
    }

    private val isEnableImageRegionDecode: Boolean by lazy {
        (CloudParamConfig.getDefaultGroupValue(
            MODULE_IMAGE_CODEC,
            Codec.IS_ENABLE_IMAGE_REGION_DECODE
        ) as? Boolean ?: false).also {
            GLog.d(TAG, LogFlag.DL) { "isEnableImageRegionDecode, $it" }
        }
    }

    /**
     * 是否是仅支持单SurfaceView的项目
     */
    private val isSingleSurfaceViewDevice: Boolean by lazy {
        GallerySystemProperties.getInt(PROP_IS_SUPPORT_SINGLE_SURFACEVIEW_DEVICE, PROP_VALUE_INTEGER_DISABLED) == PROP_VALUE_INTEGER_ENABLED
    }

    private val tileMaxCount: Int by lazy {
        if (isTablet) {
            TILE_MAX_COUNT_12
        } else {
            TILE_MAX_COUNT
        }.also {
            GLog.d(TAG, LogFlag.DL) { "tileMaxCount, $it" }
        }
    }

    /**
     * 此项目是否支持大图菜单延后加载。
     * 主要是一些性能有限的机器。大图菜单在进大图时加载会因资源锁竞争而导致入场动画掉帧。
     */
    private val isEnableMenuInflatePostpone: Boolean by lazy {
        if (GProperty.DEBUG_ENABLE_MENU_INFLATE_POSTPONE) {
            true
        } else {
            Config.CONFIG_SUPPORT_MENU_INFLATE_POSTPONE_MODEL_LIST.contains(
                configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING)
            )
        }
    }

    /**
     * 此项目是否支持AI 去眩光
     */
    private val isSupportAIGlare: Boolean by lazy {
        Config.SUPPORT_AI_DEGLARE.contains(
            configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING)
        )
    }


    /**
     * 判定当前项目，视频是否默认自动播放。
     * 注意：后面需要产品定一下具体策略，当前暂时只限定AL4和AL5这两款机型。
     * 另外，后面需要考虑，只有用户清除数据之后，才会让默认值生效。
     */
    private val videoAutoPlayDefaultValue: Boolean by lazy {
        isNeedVideoAutoPlay()
    }

    /**
     * 是否为低端机
     */
    private val isLowPerformanceDevice: Boolean by lazy {
        (PerformanceLevelUtils.animationLevelV2 == PerformanceLevel.LOW)
            || (PerformanceLevelUtils.animationLevelV2 == PerformanceLevel.LOWER)
    }

    /**
     * 平台所有编解码器信息
     */
    private val mediaCodecInfos by lazy {
        MediaCodecList(MediaCodecList.REGULAR_CODECS).codecInfos
    }

    /**
     * 系统是否支持120fps视频播放
     * 1. OS版本15.0及以上
     * 2. 系统支持相册无操作不掉帧
     */
    private val isSystemSupportPlaybackAt120Fps: Boolean by lazy {
        if (DEBUG_FORCE_DISABLE_120FPS_PLAYBACK) {
            return@lazy false
        }

        val isSystemSupportDisableOti = GallerySystemProperties.getBoolean(KEY_IS_SYSTEM_SUPPORT_DISABLE_OTI, false)
        OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0) && isSystemSupportDisableOti
    }

    /**
     * 是否支持主题分类
     */
    private val isSupportThemeClassify: Boolean by lazy {
        val isDebugOn = GallerySystemProperties.getBoolean(DEBUG_THEME_CLASSIFY, false)
        if (isDebugOn) {
            true
        } else {
            val isOS1600 = OSVersionUtils.isAtLeast(OPLUS_OS_16_0_0)
            val isSupport = (isOS1600 && !Flags.coloros1600ConfidentialOdc())
            GLog.d(TAG, LogFlag.DL) {
                "[isSupportThemeClassify] result: $isSupport, isOS16.0.0:$isOS1600"
            }
            isSupport
        }
    }

    /**
     * 是否支持旅程封面优选
     */
    private val isSupportTravelCoverSelection: Boolean by lazy {
        isSupportTravelClassify
    }

    /**
     * 是否支持学习分类
     * marked by luhuangdao 学习需求暂未完善，先不跑
     */
    private val isSupportStudyClassify: Boolean by lazy {
        isSupportThemeClassify && isRegionCN && false
    }

    /**
     * 是否支持健康医疗分类
     */
    private val isSupportHealthClassify: Boolean by lazy {
        isSupportThemeClassify && isRegionCN
    }

    /**
     * 是否支持旅行分类
     * marked by liqingfeng 目前旅程未提测，现屏蔽
     */
    private val isSupportTravelClassify: Boolean by lazy {
        isSupportThemeClassify && isRegionCN && TravelSupportCheckerImpl.isTravelFeatureOn()
    }

    /**
     * 是否支人物与宠物分类
     */
    private val isSupportPetClassify: Boolean by lazy {
        isSupportThemeClassify
    }

    /**
     * 时间轴照片视图的默认值
     *
     * 1.老机型默认日期视图:  OS16以下 || (OS16+ && 出厂版本V及以下)
     * 2.新机型沉浸式视图
     */
    private val defaultPhotoPresentType: Int by lazy {
        val isOldDevice = OSVersionUtils.isAtMost(OSVersionUtils.OPLUS_OS_15_0_2)
                || (OSVersionUtils.isAtLeast(OPLUS_OS_16_0_0) && ApiLevelUtil.isFirstApiLevelAtMostV())
        if (isOldDevice) PhotoPresentType.DATE.value() else PhotoPresentType.IMMERSE.value()
    }


    /**
     * 是否支持实况导出超清帧
     */
    private val isSupportOliveExtractUhdrFrame: Boolean by lazy {
        val isDebugOn = DEBUG_OLIVE_EXTRACT_UHDR_FRAME
        val isFeatureSupport = isSupportVideoAndOliveUhdrExportFeature

        val isSupport = isDebugOn || isFeatureSupport
        GLog.d(TAG, LogFlag.DL) {
            "[isSupportOliveExtractUhdrFrame] result: $isSupport (isDebugOn: $isDebugOn, isFeatureSupport: $isFeatureSupport)"
        }
        isSupport
    }

    /**
     * 是否支持视频导出实况
     */
    private val isSupportVideoExtractOlive: Boolean by lazy {
        val isDebugOn = DEBUG_VIDEO_EXTRACT_OLIVE
        val isFeatureSupport = isSupportVideoAndOliveUhdrExportFeature

        val isSupport = isDebugOn || isFeatureSupport
        GLog.d(TAG, LogFlag.DL) {
            "[isSupportVideoExtractOlive] result: $isSupport (isDebugOn: $isDebugOn, isFeatureSupport: $isFeatureSupport)"
        }
        isSupport
    }

    /**
     * 实况&视频超清导出的开关判定条件是相同的，
     * 在此提取共同的判断逻辑，
     * 用于: [isSupportOliveExtractUhdrFrame] 和 [isSupportVideoExtractOlive]
     */
    private val isSupportVideoAndOliveUhdrExportFeature: Boolean by lazy {
        // 增加 15.0.1 且 在白名单配置内 放开洱海和彩虹项目对应的机型
        val oplusOs1501 = OSVersionUtils.isVersion(OSVersionUtils.OPLUS_OS_15_0_1)
        val device = configAbility.getStringConfig(PHONE_MODEL, EMPTY_STRING)
        val whiteListSupport = Config.OLIVE_EXPORT_DEVICES_WHITELIST.contains(device)
        val version1501Support = oplusOs1501 && whiteListSupport

        val version1502Support = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_2)
        FeatureGetter(
            context = context,
            configAbility = configAbility,
            iFeatureBasicCondition = { (version1501Support || version1502Support) && isSupportOLive }
        ).get().also {
            GLog.d(TAG, LogFlag.DL) {
                "[isSupportVideoAndOliveUhdrExportFeature] result=$it, version1501Support=$version1501Support," +
                        "version1502Support=$version1502Support, isSupportOLive=$isSupportOLive"
            }
        }
    }

    /**
     * 判断当前系统是否支持视频壁纸功能
     * 相关代码是壁纸那边给到的
     */
    private val isSupportVideoWallpaper: Boolean by lazy {

        val isOS16Plus = OSVersionUtils.isAtLeast(OPLUS_OS_16_0_0)

        /*
         * 是否为保密版本
         * 如果是保密版本 则系统不支持设置视频壁纸
         */
        val isOs16Confidential = if (isOS16Plus) {
            Flags.coloros1600ConfidentialInfinite() || Flags.coloros1600ConfidentialOdc()
        } else {
            false
        }

        val result = if (isOS16Plus.not()) {
            GLog.d(TAG, LogFlag.DL) { "[isSupportVideoWallpaper] Video wallpaper must be on OS16 or OS16+" }
            false
        } else if (isOs16Confidential) {
            GLog.d(TAG, LogFlag.DL) { "[isSupportVideoWallpaper] false due to Confidential feature" }
            false
        } else {
            AppFeatureProviderUtils.getBoolean(context.contentResolver, KEY_IS_SUPPORT_VIDEO_WALLPAPER, true)
        }

        result.also {
            GLog.d(TAG, LogFlag.DL) { "[isSupportVideoWallpaper] is $it, if true will show the entrance of setting as wallpaper" }
        }
    }

    private fun hasAppFeature(context: Context, featureNameR: String, featureNameQ: String): Boolean {
        return AppFeatureProviderUtils.isFeatureSupport(context.contentResolver, featureNameR)
    }

    private fun isNeedVideoAutoPlay(): Boolean {
        // 针对OS15.0以前的，均支持自动播放
        if (OSVersionUtils.oplusOsVersion < OSVersionUtils.OPLUS_OS_15_0_0) {
            return true
        }
        if (ApiLevelUtil.isFirstApiLevelAtLeastV().not()) {
            GLog.e(TAG) { "isNeedViewAutoPlay isFirstApiLevelAtLeastV not " }
            return true
        }
        val isNeed = if (isProductLightLow) false else true
        return isNeed
    }

    /**
     * isSupportCameraHeif Heif的feature依赖相机，需要采用
     * OplusFeatureConfigManager.getInstance(context).hasFeature(featureR) 获取
     */
    private fun hasOplusFeature(context: Context, featureR: String, featureQ: String): Boolean {
        return context.packageManager?.let {
            (OplusFeatureConfigManagerWrapper.hasFeature(context, featureR)
                || it.hasSystemFeature(featureQ))
        } ?: false
    }

    /**
     * 根据特定编解码类型，查询平台对应的编解码能力。
     * @param codecMimeType 查询参数：mimeType类型
     * @param isEncoder 查询参数：是否为编码器
     * @return CodecCapabilities 编解码器能力
     */
    private fun getCodecCapabilitiesByCodecConfig(codecMimeType: String, isEncoder: Boolean): CodecCapabilities? {
        if (mediaCodecInfos.isNullOrEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "[getMediaCodecInfoByCodecMimeType] failed to getMediaCodecInfo. cause codecInfos is null/empty" }
            return null
        }

        val codecInfo = mediaCodecInfos.find { info ->
            // 查询编/解码器 && 支持特定的codecType
            (info.isEncoder == isEncoder) && (info.supportedTypes.find { it == codecMimeType } != null)
        }
        return codecInfo?.getCapabilitiesForType(codecMimeType)
    }

    override fun readObject(configNode: ConfigNode, defValue: Any?): Any? {
        return when (configNode.id) {
            Codec.CLOUD_HASSEL_DEVICES -> getHasselDevicesFromCloud
            else -> null
        }
    }

    override fun write(configNode: ConfigNode, value: Any?) {
        throw UnsupportedOperationException("Not support for this config:${configNode.id}")
    }

    override fun writeSync(configNode: ConfigNode, value: Any?): Boolean {
        throw UnsupportedOperationException("Not support for this config:${configNode.id}")
    }

    override fun writeBatch(map: Map<ConfigNode, Any>) {
        throw UnsupportedOperationException("Not support for these configs:$map")
    }

    override fun writeWithArgs(configNode: ConfigNode, args: ConfigExtraArgs, value: Any?) {
        throw UnsupportedOperationException("Not support for write config with params, config:${configNode.id}")
    }

    override fun deleteBatch(configNodeSet: Set<ConfigNode>) {
        throw UnsupportedOperationException("Not support for these configs:$configNodeSet")
    }

    override fun register(configNode: ConfigNode, listener: IConfigListener) {
        throw UnsupportedOperationException("Not support for this config:${configNode.id}")
    }

    override fun unregister(configNode: ConfigNode, listener: IConfigListener) {
        throw UnsupportedOperationException("Not support for this config:${configNode.id}")
    }

    override fun contains(configNode: ConfigNode): Boolean {
        throw UnsupportedOperationException("No meaning to call contains for other type config:${configNode.id}")
    }

    companion object {

        @Volatile
        private var instance: OtherSystemStorage? = null

        fun get(context: Context, configAbility: IConfigSetterAbility): OtherSystemStorage {
            return when {
                instance != null -> instance as OtherSystemStorage
                else -> {
                    synchronized(this) {
                        if (instance == null) {
                            instance = OtherSystemStorage(context, configAbility)
                        }
                        instance as OtherSystemStorage
                    }
                }
            }
        }

        //新春水印下架时间
        private const val SPRING_WATERMARK_OFFTIME = "**************"

        private const val TAG = "OtherSystemStorage"

        private const val TIME_THREE_SECOND = 3000L

        private const val VALUE_OPERATOR_SOFTBANK = "SOFTBANK"

        private const val MTK_REGEX_PATTERN = "mt[0-9]*"

        private const val QCOM_VENDER = "qcom"

        private const val META_DATA_AI_ID_PHOTO_KEY = "isSupportAIIDPhoto"

        private const val META_DATA_PORTRAIT_BLUR_KEY = "isSupportBlurEdit"

        private const val SUPPORT_CAMERA_JPEG_EXIF_THUMBNAIL_TAG = "com.oplus.aps.jpeg.thumbnail.support"

        private const val SUPPORT_HASSELBLAD_WATERMARK_TAG = "com.oplus.camera.support.custom.hasselblad.watermark"

        private const val SUPPORT_LONELY_PLANET_WATERMARK_TAG = "com.oplus.camera.support.custom.lonely.planet.watermark"

        private const val SUPPORT_STREET_WATERMARK_TAG = "com.oplus.feature.custom.makeup.watermark.support"

        private const val SUPPORT_COLOR_WATERMARK_TAG = "com.oplus.camera.support.custom.color.watermark"

        private const val SUPPORT_QUICK_PHOTO_PUBLIC_TAG = "com.oplus.camera.feature.capture_defer.support"

        private const val CAMERA_WATERMARK_IS_NEW_TAG = "com.oplus.watermark.is.new.project.behavior"

        private const val CAMERA_PROXDR_SUPPORTED = "com.oplus.camera.capture.hdr.support"

        private const val PHOTO_EDITOR_WATERMARK_SWITCHER = "debug.gallery.photo.editor.watermark.switcher"

        private const val DEBUG_THEME_CLASSIFY = "debug.gallery.theme_classify"

        // marked by caiconghu 目前相机还未提供接口名，在这里占个位置，后面补上
        private const val CAMERA_SUPPORT_GROUP_PHOTO = "dont.use.this.group_photo"

        private const val FEATURE_SALEMODE_Q = "oppo.specialversion.exp.sellmode"

        private const val FEATURE_SALEMODE_LEAST_R = "oplus.software.pms_sellmode"

        private const val SCANNER_CORRECTION_MATE_DATA_NAME = "isSupport_HQ_DocScan"

        private const val FEATURE_SUPPORT_LIVE_PHOTO = "oplus.software.gallery.olive"

        private const val FEATURE_CONFIG_FUN_FLAG = "ro.oplus.cfg_fun_flag"

        private const val MAX_VIDEO_SIZE_FOR_4K = 3840
        private const val INDEX_DEVICE = 0
        private const val INDEX_RESOLUTION = 1

        /**
         * 增加App-feature控制功能支持情况：
         *  最佳表情优化功能支持轻量高档，不支持轻量低档。
         *  【1】全量：1 （表示支持全量功能）
         *  【2】轻量高档：2
         *
         */
        private const val BEST_TAKE_FEATURE_SUPPORT_FULL_FUNCTION = 1
        private const val BEST_TAKE_FEATURE_LIGHT_HIGH = 2

        /**
         * 该字段为翻译应用定义是否支持屏幕翻译的metadata数据，获取该字段的值用于判断是否支持屏幕翻译，值为1代表支持
         */
        private const val TRANSLATE_METADATA = "support_screen_translation"

        private const val MODULE_HASSEL_WATERMARK = "module_hassel_watermark"
        private const val DEVICES_SUPPORT_HASSEL_EDIT = "${GroupResponseBean.STRING}${GroupResponseBean.CONNECT}devices_support_hassel_edit"

        private const val MODULE_IMAGE_CODEC = "module_image_codec"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val HEY_CAST_PKG = "com.oplus.cast"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val SYNERGY_PKG = "com.oplus.linker"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val OSHARE_PKG = "com.coloros.oshare"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val RESTRICTION_PAG = "android.os.customize.OplusCustomizeRestrictionManager"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val META_DATA_KEY_SUPPORT_ADAPTION = "self_adaption_relay_enable"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val KEY_METHOD_FILE_SHARED_DISABLED = "persist.sys.disable_file_shared"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val KEY_METHOD_PRIVATE_SAFE_DISABLED = "isPrivateSafeDisabled"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        const val KEY_GET_INSTANCE = "getInstance"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        private const val FILE_ENCRYPTION_PKG = "com.oplus.encryption"

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        private const val VERSION_FILE_ENCRYPTION = 150040000L

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        private const val SUPPORT_SE_CHIP_KEY = "support_se_chip"

        /**
         * 是否仅支持单SurfaceView的配置
         */
        private const val PROP_IS_SUPPORT_SINGLE_SURFACEVIEW_DEVICE = "ro.oplus.gallery3d.single.SurfaceView.enable"
        private const val PROP_VALUE_INTEGER_ENABLED = 1
        private const val PROP_VALUE_INTEGER_DISABLED = 0

        /**
         * Oplus Feature定义
         *
         * 底软配置的Oplus Feature，判定当前机型是否支持配置VelocityTracker为IMPULSE策略，目前22851 release T版本支持。
         */
        private const val OPLUS_FEATURE_SUPPORT_VELOCITYTRACKER_STATEGY_IMPULSE = "oplus.velocitytracker.strategy.support"

        /**
         * @see android.content.pm.PackageManager#FEATURE_NFC_ANY
         */
        private const val FEATURE_NFC_ANY = "android.hardware.nfc.any"

        private val DISABLE_HDR_PLATFORM = arrayOf(
            "mt6885",
            "mt6779",
            "mt6853",
            "mt6873"
        )

        /**
         * 使用贴纸数量限制
         */
        private const val STICKER_LIMIT_HIGH = 30
        private const val STICKER_LIMIT_MEDIUM = 20
        private const val STICKER_LIMIT_LOW = 10

        /***
         * Integer 常量 0、1
         */
        private const val INTEGER_0 = 0
        private const val INTEGER_1 = 1

        /**
         * 账号App支持核身校验的最低版本（9.0）
         */
        private const val VERSION_OPLUS_ACCOUNT_AT_LEAST = 900000L

        /**
         * 支持[最近删除验证]开关的设置版本，含有此meta-data
         */
        private const val KEY_RECENTLY_DELETED_METADATA = "privacy_password_protection_v2"


        /**
         * Tile块默认最大数量
         */
        private const val TILE_MAX_COUNT = 9

        /**
         * 某些项目（比如平板），Tile块最大数量可以放大点，这样清晰度会好点
         */
        private const val TILE_MAX_COUNT_12 = 12

        /**
         * 系统框架是否支持禁用无触控降帧对应key
         */
        private const val KEY_IS_SYSTEM_SUPPORT_DISABLE_OTI = "sys.display.vrr.vote.support"

        /**
         * 系统框架是否支持设置视频壁纸对应key
         */
        private const val KEY_IS_SUPPORT_VIDEO_WALLPAPER = "com.oplus.wallpapers.video_wallpaper"

        /**
         * 扫一扫处理功能 meta的key和value
         */
        private const val OCR_SCANNER_DOCUMENT_VERSION = "document_support_version"
        private const val OCR_SCANNER_DOCUMENT_HQ = 1

        /**
         * 提亮功能下，视频触发防烧屏功能的时间周期,默认为1分钟
         */
        private const val VIDEO_SCREEN_BURN_IN_WARN_DELAY_TIME = 1 * 60 * 1000L

        /**
         * 提亮功能下，图片触发防烧屏功能的时间周期，默认为5分钟
         */
        private const val IMAGE_SCREEN_BURN_IN_WARN_DELAY_TIME = 5 * 60 * 1000L
    }
}
