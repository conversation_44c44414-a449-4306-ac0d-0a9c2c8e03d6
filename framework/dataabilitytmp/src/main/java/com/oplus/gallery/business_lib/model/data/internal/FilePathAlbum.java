/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FilePathAlbum.java
 ** Description:指定 filePaths 数据源
 **
 ** Version: 1.0
 ** Date: 2020/11/14
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_PICTURE
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/11/14		1.0		OPLUS_FEATURE_PICTURE
 *********************************************************************************/

package com.oplus.gallery.business_lib.model.data.internal;

import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_ALBUM_ORDER;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SET_COVER;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_IMAGE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_VIDEO;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.AND;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.COMMA;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.DESC;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL;

import android.content.Context;

import com.oplus.gallery.business_lib.model.data.base.IMediaInputEntry;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.local.set.LocalAlbum;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;

public class FilePathAlbum extends LocalAlbum {
    public static final int ORDER_NAME = 0;
    public static final int ORDER_SIZE = 1;
    public static final int ORDER_TYPE_DATE_TAKEN = 2;
    public static final int ORDER_TYPE_MODIFY_TIME = 3;
    private static final String TAG = "FilePathAlbum";

    public FilePathAlbum(Context application, Path path) {
        super(path, application);
    }

    @Override
    public @NotNull String getTag() {
        return TAG;
    }

    @Override
    public long getSupportedOperations() {
        long flag  = super.getSupportedOperations();
        flag &= ~OPERATION_SUPPORT_SET_COVER;
        flag &= ~OPERATION_SUPPORT_ALBUM_ORDER;
        return flag;
    }

    public static class FilePathInputEntry extends LocalAlbumEntry {
        private String mWhereClause;
        private String mOrderClause;

        private FilePathInputEntry() {
            super(MEDIA_TYPE_SUPPORT_ALL, null, null);
        }

        public FilePathInputEntry(int mediaTypeSupport, int orderType, boolean isPositiveOrder, ArrayList<String> listFilePath) {
            super(mediaTypeSupport, null, null);
            mOrderClause = getOrderClause(orderType, isPositiveOrder);
            mWhereClause = getWhereClause(listFilePath);
        }

        @Override
        public String getWhere() {
            return mWhereClause;
        }

        @Override
        public String getOrder() {
            return mOrderClause;
        }

        private String getWhereClause(ArrayList<String> filePath) {
            StringBuilder sb = new StringBuilder();
            sb.append(DatabaseUtils.getWhereIn(GalleryStore.GalleryColumns.LocalColumns.DATA, filePath));
            if (sb.length() > 0) {
                if (getMediaTypeSupport() == MEDIA_TYPE_SUPPORT_IMAGE) {
                    sb.append(AND);
                    sb.append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE).append(EQUAL)
                            .append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE);
                } else if (getMediaTypeSupport() == MEDIA_TYPE_SUPPORT_VIDEO) {
                    sb.append(AND);
                    sb.append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE).append(EQUAL)
                            .append(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO);
                }
            }
            return sb.toString();
        }

        private String getOrderClause(int order, boolean positive) {
            switch (order) {
                case ORDER_NAME:
                    return GalleryStore.GalleryColumns.LocalColumns.TITLE + COMMA + GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + DESC;
                case ORDER_SIZE:
                    return GalleryStore.GalleryColumns.LocalColumns.SIZE + DESC + COMMA + GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + DESC;
                case ORDER_TYPE_MODIFY_TIME:
                   return DatabaseUtils.getOrderClauseDateModified(positive);
                default:
                    return DatabaseUtils.getOrderClauseDateTaken(positive);
            }
        }

        @Override
        public IMediaInputEntry copy(IMediaInputEntry targetEntry) {
            FilePathInputEntry resultEntry = null;
            if (targetEntry instanceof FilePathInputEntry) {
                resultEntry = (FilePathInputEntry) targetEntry;
            } else  {
                resultEntry = new FilePathInputEntry();
            }
            resultEntry.mWhereClause = mWhereClause;
            resultEntry.mOrderClause = mOrderClause;
            return super.copy(resultEntry);
        }
    }
}
