/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PickedSource
 ** Description: 精选视图数据源
 **
 ** Version: 1.0
 ** Date: 2022/04/15
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/04/15  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.business_lib.model.data.base.source

import com.oplus.gallery.business_lib.model.data.base.MediaObject
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.timeline.PickedDayAlbum
import com.oplus.gallery.business_lib.model.data.base.timeline.PickedYearMonthAlbum
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL
import com.oplus.gallery.business_lib.model.data.seniorpicked.PickedDayImage
import com.oplus.gallery.business_lib.model.data.seniorpicked.PickedDayVideo

internal class PickedSource : MediaSource(SourceConstants.Picked.SRC) {

    init {
        initKindMap()
        setupMatcher()
    }

    private fun initKindMap() {
        mAllChildPathToKindMap[PickedDayImage.ITEM_PATH] = M_ITEM_IMAGE
        mAllChildPathToKindMap[PickedDayVideo.ITEM_PATH] = M_ITEM_VIDEO

        mPathToKindMap[SourceConstants.Picked.PATH_ALBUM_DAY_ANY] = M_ALBUM_DAY_ANY
        mPathToKindMap[SourceConstants.Picked.PATH_ALBUM_YEAR_MONTH_ANY] = M_ALBUM_YEAR_MONTH_ANY
    }

    override fun createMediaObject(path: Path): MediaObject {
        return when (mMatcher.match(path)) {
            M_ITEM_IMAGE -> {
                PickedDayImage(path, mContext).also {
                    /*
                     PickedDayImage 需要持有path，这里主动调用一下realPath，
                     防止编译时realPath被优化掉。bugid:3944788
                     */
                    it.realPath
                }
            }
            M_ITEM_VIDEO -> {
                PickedDayVideo(path, mContext).also {
                    /*
                     PickedDayVideo 需要持有path，这里主动调用一下realPath，
                     防止编译时realPath被优化掉。bugid:3944788
                     */
                    it.realPath
                }
            }
            M_ALBUM_DAY_ANY -> PickedDayAlbum(path, mContext, MEDIA_TYPE_SUPPORT_ALL)
            M_ALBUM_YEAR_MONTH_ANY -> PickedYearMonthAlbum(path, mContext, MEDIA_TYPE_SUPPORT_ALL)
            else -> throw IllegalArgumentException("createMediaObject path illegal!")
        }
    }

    override fun isKeepOriginalPageAfterEdit(path: Path?): Boolean {
        return when (mMatcher.match(path)) {
            M_ALBUM_DAY_ANY -> true
            else -> false
        }
    }

    companion object {
        // Matcher code - picked item
        private const val M_ITEM_IMAGE = 1
        private const val M_ITEM_VIDEO = M_ITEM_IMAGE + 1

        private const val M_ALBUM_DAY_ANY = M_ITEM_VIDEO + 1
        private const val M_ALBUM_YEAR_MONTH_ANY = M_ALBUM_DAY_ANY + 1
    }
}