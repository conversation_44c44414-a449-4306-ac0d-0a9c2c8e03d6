/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - LabelDictionary.java
 * Description:
 * Version: 1.0
 * Date: 2020/8/20
 * Author: Wangrunxin@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Wangrunxin@Apps.Gallery3D      2020/8/20      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.model.data.label;

import android.text.TextUtils;

import com.oplus.gallery.bus_lib.Bus;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.text.SpecialSplitter;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LabelDictionary {
    private static final String TAG = "LabelDictionary";
    private static final String KEY_LABEL_SLIDE = "幻灯片";
    private static final String KEY_LABEL_SCREENSHOT = "截图";

    private static final List<RejectLabel> sRejectLabels = new ArrayList<>();

    private static final Map<Integer, List<String>> sIdLabelsMap = new HashMap<>();

    private static final Map<Integer, List<String>> sLocalIdLabelsMap = new HashMap<>();
    private static final Map<Integer, List<String>> sCloudIdLabelsMap = new HashMap<>();

    private static final Map<Integer, String> LOCAL_ID_LABEL_KEY_MAP = new ConcurrentHashMap<Integer, String>();
    private static final Map<Integer, String> CLOUD_ID_LABEL_KEY_MAP = new ConcurrentHashMap<Integer, String>();
    private static final int INVALID_LABEL_ID = -1;

    private static boolean sIsPickedDictionary = false;
    private static boolean sIsUseCloudDictionary = false;
    private static int sLocalOtherId = INVALID_LABEL_ID;
    private static int sCloudOtherId = INVALID_LABEL_ID;
    private static int sScreenShotLabelId = INVALID_LABEL_ID;
    /**
     * 英文(en_us)词典操作类
     */
    private static ILabelDictionary sEnglishDictionary = new EnglishLabelDictionary();
    /**
     * 默认标签是否是英文(en_us)
     */
    private static boolean sIsDefaultEnglish = false;

    /**
     * 是否选择了使用云词典
     */
    public static boolean isPickedCloudDictionary() {
        return sIsPickedDictionary && sIsUseCloudDictionary;
    }

    /**
     * 设置默认标签是否是英文(en_us)
     * @param isEnUs true 英文(en_us)  false 非英文
     * 默认标签是英文时，直接从LabelDictionary中获取标签数据，不从EnglishLabelDictionary类中获取
     */
    public static void setDefaultEnglish(boolean isEnUs) {
        GLog.d(TAG, "setDefaultEnglish, isEnUs = " + isEnUs);
        sIsDefaultEnglish = isEnUs;
    }

    /**
     * get screenShot label id for label name,
     * maybe it is local label id or cloud label id
     *
     * @return label id
     */
    public static int getScreenShotLabelId() {
        if (sScreenShotLabelId != INVALID_LABEL_ID) {
            return sScreenShotLabelId;
        }
        Map<Integer, String> labelIdMap = sIsUseCloudDictionary ? CLOUD_ID_LABEL_KEY_MAP : LOCAL_ID_LABEL_KEY_MAP;
        for (Map.Entry<Integer, String> entry : labelIdMap.entrySet()) {
            if (KEY_LABEL_SCREENSHOT.equalsIgnoreCase(entry.getValue())) {
                sScreenShotLabelId = entry.getKey();
                return sScreenShotLabelId;
            }
        }
        return INVALID_LABEL_ID;
    }

    /**
     * get slide label id for label name
     * maybe it is local label id or cloud label id
     *
     * @return label id
     */
    public static int getSlideLabelId() {
        Map<Integer, String> labelIdMap = sIsUseCloudDictionary ? CLOUD_ID_LABEL_KEY_MAP : LOCAL_ID_LABEL_KEY_MAP;
        for (Map.Entry<Integer, String> entry : labelIdMap.entrySet()) {
            if (KEY_LABEL_SLIDE.equalsIgnoreCase(entry.getValue())) {
                return entry.getKey();
            }
        }
        return -1;
    }

    /**
     * get label name by label id which maybe it is local label id
     * or cloud label id
     *
     * @param sceneId
     * @return label name
     */
    public static String getLabelName(int sceneId) {
        List<String> labels = sIdLabelsMap.get(sceneId);
        if ((labels == null) || labels.isEmpty()) {
            return null;
        } else {
            return labels.get(0);
        }
    }

    /**
     * get english label name by label id which maybe it is local label id
     * or cloud label id
     *
     * @param sceneId
     * @return label name
     */
    public static String getEnglishLabelName(int sceneId) {
        if (sIsDefaultEnglish) {
            return getLabelName(sceneId);
        }
        return sEnglishDictionary.getLabelName(sceneId);
    }

    /**
     * get label name, child label name and synonym by label id which maybe it is
     * local label id or cloud label id
     *
     * @param sceneId
     * @return label name list
     */
    public static List<String> getLabelNames(int sceneId) {
        return sIdLabelsMap.get(sceneId);
    }

    /**
     * get label key by label id which maybe it is
     * local label id or cloud label id, their label key
     * is the same
     *
     * @param sceneId
     * @return label key
     */
    public static String getLabelKey(int sceneId) {
        if (sIsUseCloudDictionary) {
            return CLOUD_ID_LABEL_KEY_MAP.get(sceneId);
        }
        return LOCAL_ID_LABEL_KEY_MAP.get(sceneId);
    }

    /**
     * get label id which maybe it is local label id or cloud label id
     * by label key.
     *
     * @param labelKey
     * @return label id
     */
    public static int getLabelId(String labelKey) {
        if (TextUtils.isEmpty(labelKey)) {
            return -1;
        }
        Map<Integer, String> labelIdMap = sIsUseCloudDictionary ? CLOUD_ID_LABEL_KEY_MAP : LOCAL_ID_LABEL_KEY_MAP;
        for (Map.Entry<Integer, String> entry : labelIdMap.entrySet()) {
            if (labelKey.equalsIgnoreCase(entry.getValue())) {
                return entry.getKey();
            }
        }
        return -1;
    }

    /**
     * get cloud label other id and local label other id
     *
     * @return other label id
     */
    public static int getOtherId() {
        return sIsUseCloudDictionary ? sCloudOtherId : sLocalOtherId;
    }

    /**
     * get cloud label other id
     *
     * @return other label id
     */
    public static int getCloudOtherId() {
        return sCloudOtherId;
    }

    /**
     * set local other label id, v2.0.8 - 49
     * v 2.0.9 - 89
     *
     * @param id
     */
    public static void setLocalOtherId(int id) {
        sLocalOtherId = id;
    }

    /**
     * reset local dictionary
     */
    public static void resetLocalDictionary() {
        sLocalIdLabelsMap.clear();
        LOCAL_ID_LABEL_KEY_MAP.clear();
    }

    /**
     * reset cloud dictionary
     */
    public static void resetCloudDictionary() {
        sCloudIdLabelsMap.clear();
        CLOUD_ID_LABEL_KEY_MAP.clear();
    }

    /**
     * adjust whether this dictionary to be loaded ot not
     *
     * @return
     */
    public static boolean isDictionaryLoaded() {
        return sIsPickedDictionary;
    }

    /**
     * 英文(en_us)词典是否未加载
     * @return true 未加载或加载中， false 已加载
     */
    public static boolean isEnglishDictionaryUnLoaded() {
        return sEnglishDictionary.isDictionaryUnLoaded();
    }

    /**
     * 设置英文(en_us)词典状态为加载中
     */
    public static void setEnglishDictionaryLoading() {
        sEnglishDictionary.setDictionaryLoading();
    }

    /**
     * pick whether to use local dictionary or cloud dictionary
     *
     * @param isUseCloudDictionary
     */
    public static void pickDictionary(boolean isUseCloudDictionary) {
        if (isUseCloudDictionary != sIsUseCloudDictionary) {
            sScreenShotLabelId = INVALID_LABEL_ID;
        }
        sIsUseCloudDictionary = isUseCloudDictionary;
        /*
        clear sIdLabelsMap, then pick sCloudIdLabelsMap or
        sLocalIdLabelsMap by isUseCloudDictionary
        */
        GLog.d(TAG, "pickDictionary, isUseCloudDictionary is " + isUseCloudDictionary);
        sIdLabelsMap.clear();
        if (isUseCloudDictionary) {
            sIdLabelsMap.putAll((Map<Integer, List<String>>) deeplyCloneObject(sCloudIdLabelsMap));
        } else {
            sIdLabelsMap.putAll((Map<Integer, List<String>>) deeplyCloneObject(sLocalIdLabelsMap));
        }

        sIsPickedDictionary = true;
        // 词典数据准备后，通知上层获取数据
        Bus.INSTANCE.post(new DataManager.ReloadMessage());
    }

    /**
     * pick whether to use local dictionary or cloud dictionary
     *
     * @param isUseCloudDictionary
     */
    public static void pickEnglishDictionary(boolean isUseCloudDictionary) {
        sEnglishDictionary.pickDictionary(isUseCloudDictionary);
    }

    /**
     * according by label key , convert local label id
     * to cloud label id
     *
     * @param labelId
     * @return cloud label id
     */
    public static int convertCloudId(int labelId) {
        GLog.d(TAG, "convertCloudId, labelId is " + labelId);
        if (CLOUD_ID_LABEL_KEY_MAP.isEmpty()) {
            GLog.d(TAG, "convertCloudId, sCloudIdLabelKeyMap is null!");
            return labelId;
        }
        if (labelId == sLocalOtherId) {
            /*
            local dictionary otherId mapping
            cloud dictionary otherId -1;
            */
            GLog.d(TAG, "convertCloudId, labelId is other Id!");
            return sCloudOtherId;
        }
        String labelKey = LOCAL_ID_LABEL_KEY_MAP.get(labelId);
        if (TextUtils.isEmpty(labelKey)) {
            GLog.w(TAG, "convert, labelKey is null , labelId is " + labelId);
            return sCloudOtherId;
        }
        GLog.d(TAG, "convertCloudId, labelKey is " + labelKey);
        // find cloud id by labelKey in local label list.
        for (Map.Entry<Integer, String> entry : CLOUD_ID_LABEL_KEY_MAP.entrySet()) {
            if (labelKey.equalsIgnoreCase(entry.getValue())) {
                labelId = entry.getKey();
                GLog.d(TAG, "convertCloudId, labelId converted is " + labelId);
                return labelId;
            }
        }
        GLog.d(TAG, "convertCloudId, failed to convert, convert is " + labelId);
        return sCloudOtherId;
    }

    /**
     * 把云scene_id转换成本地词典的scene_id
     */
    public static int convertCloudIdToLocalId(int labelId) {
        GLog.d(TAG, "convertCloudIdToLocalId, labelId is " + labelId);
        if (LOCAL_ID_LABEL_KEY_MAP.isEmpty()) {
            GLog.d(TAG, "convertCloudIdToLocalId, sLocalIdLabelKeyMap is null!");
            return labelId;
        }
        if (labelId == sCloudOtherId) {
            /*
            cloud dictionary otherId -1 mapping
            local dictionary otherId 93;
            */
            GLog.d(TAG, "convertCloudIdToLocalId, labelId is other Id!");
            return sLocalOtherId;
        }
        String labelKey = CLOUD_ID_LABEL_KEY_MAP.get(labelId);
        if (TextUtils.isEmpty(labelKey)) {
            GLog.w(TAG, "convertCloudIdToLocalId, labelKey is null , labelId is " + labelId);
            return sLocalOtherId;
        }
        GLog.d(TAG, "convertCloudIdToLocalId, labelKey is " + labelKey);
        // find local id by labelKey in cloud label list.
        for (Map.Entry<Integer, String> entry : LOCAL_ID_LABEL_KEY_MAP.entrySet()) {
            if (labelKey.equalsIgnoreCase(entry.getValue())) {
                labelId = entry.getKey();
                GLog.d(TAG, "convertCloudIdToLocalId, labelId converted is " + labelId);
                return labelId;
            }
        }
        GLog.d(TAG, "convertCloudIdToLocalId, failed to convert, convert is " + labelId);

        return sLocalOtherId;
    }
    /**
     * get cloud label id by label key
     *
     * @param labelKey
     * @return cloud label id
     */
    public static int getCloudSceneId(String labelKey) {
        for (Map.Entry<Integer, String> entry : CLOUD_ID_LABEL_KEY_MAP.entrySet()) {
            if (labelKey.equalsIgnoreCase(entry.getValue())) {
                return entry.getKey();
            }
        }
        return sCloudOtherId;
    }

    /**
     * add reject label for filtering
     *
     * @param rejectLabels
     */
    public static void addRejectLabels(List<String> rejectLabels) {
        sRejectLabels.clear();
        if (rejectLabels == null) {
            GLog.d(TAG, "addRejectLabels, rejectLabels is null!");
            return;
        }
        GTrace.traceBegin(TAG + ".addRejectLabels");
        for (String label : rejectLabels) {
            RejectLabel rejectLabel = new RejectLabel();
            rejectLabel.setLabelKey(label);
            sRejectLabels.add(rejectLabel);
        }
        GTrace.traceEnd();
    }

    /**
     * add label info into local/cloud dictionary
     *
     * @param sceneId     扫描出的标签Id
     * @param label       名称
     * @param labelKey    键值
     * @param isKey       传入的sceneId是作为键
     * @param isFromCloud false:Local true:Cloud
     * @param isCurSysLanguage 是否当前语言，是当前语言添加到默认词典；非当前词典，添加到英文(en_us)词典
     */
    public static void addLabelDictionary(
            int sceneId,
            String label,
            String labelKey,
            boolean isKey,
            boolean isFromCloud,
            boolean isCurSysLanguage
    ) {
        if (!isCurSysLanguage) {
            sEnglishDictionary.addLabelDictionary(sceneId, label, labelKey, isKey, isFromCloud);
            return;
        }

        Map<Integer, List<String>> idLabelsMap = isFromCloud ? sCloudIdLabelsMap : sLocalIdLabelsMap;
        List<String> labels = idLabelsMap.get(sceneId);
        if (labels == null) {
            labels = new ArrayList<>();
            labels.add(label);
            idLabelsMap.put(sceneId, labels);
        } else if (isKey) {
            labels.remove(label);
            labels.add(0, label);
        } else if (!labels.contains(label)) {
            labels.add(label);
        }
        if (isKey) {
            (isFromCloud ? CLOUD_ID_LABEL_KEY_MAP : LOCAL_ID_LABEL_KEY_MAP).put(sceneId, labelKey);
        }
    }

    /**
     * class of reject label
     */
    public static class RejectLabel {
        private int mCloudSceneId = -1;
        private int mLocalSceneId = -1;
        private String mLabelKey = null;

        public void setLabelKey(String label) {
            mLabelKey = label;
        }

        public String getLabelKey() {
            return mLabelKey;
        }

        public int getSceneId(boolean isCloudParser) {
            return isCloudParser ? mCloudSceneId : mLocalSceneId;
        }

        public void setSceneId(boolean isCloudParser, int sceneId) {
            if (isCloudParser) {
                mCloudSceneId = sceneId;
            } else {
                mLocalSceneId = sceneId;
            }
        }
    }

    /**
     * adjust whether to be reject label or not
     *
     * @param sceneId
     * @param label
     * @return true mean to be reject label
     */
    public static boolean isRejectLabel(String label, int sceneId, boolean isCloudParser) {
        for (RejectLabel rejectLabel : sRejectLabels) {
            if (rejectLabel.getSceneId(isCloudParser) == sceneId) {
                return true;
            }
            String labelKey = rejectLabel.getLabelKey();
            if (!TextUtils.isEmpty(labelKey)) {
                if (labelKey.equalsIgnoreCase(label)) {
                    rejectLabel.setSceneId(isCloudParser, sceneId);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * adjust whether to be reject synonym label or not
     *
     * @param synonymIds
     * @param labelKey
     * @return true mean to be reject synonym label
     */
    public static boolean isRejectSynonymLabel(String labelKey, String synonymIds, boolean isCloudParser) {
        List<String> synonymIdArray = SpecialSplitter.fastSplit(synonymIds, LabelSearchEngine.ITEM_SPLIT_CHAR);
        if (synonymIdArray != null) {
            for (String item : synonymIdArray) {
                int synonymId = LabelSearchEngine.tryParseInt(item);
                if (synonymId != LabelSearchEngine.INVALID_ID) {
                    if (isRejectLabel(labelKey, synonymId, isCloudParser)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * clone object deeply, used to be copied map
     * here
     *
     * @param obj
     * @return obj
     */
    private static Object deeplyCloneObject(Object obj) {
        ByteArrayOutputStream boStream = null;
        ObjectOutputStream ooStream = null;
        ByteArrayInputStream biStream = null;
        ObjectInputStream oiStream = null;
        try {
            // write obj
            boStream = new ByteArrayOutputStream();
            ooStream = new ObjectOutputStream(boStream);
            ooStream.writeObject(obj);
            // read obj
            biStream = new ByteArrayInputStream(boStream.toByteArray());
            oiStream = new ObjectInputStream(biStream);
            return oiStream.readObject();
        } catch (Exception e) {
            GLog.w(TAG, "deepClone, Exception = " + e);
        } finally {
            IOUtils.closeQuietly(oiStream, biStream, ooStream, boStream);
        }
        return null;
    }
}
