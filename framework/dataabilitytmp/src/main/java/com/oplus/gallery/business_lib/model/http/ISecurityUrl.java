/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ISecurityUrl.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/11/20
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.business_lib.model.http;

public interface ISecurityUrl {

    String getVideoEditorHostName();

    String getUserProfileHostName();

    String getStickerHostName();

    String getCloudHostName();

    String getCloudDns();

    String getOCloudAiRepairHostName();

    String getOCloudAiPhotoHostName();

    String getCloudKitHost();

    String getCloudKitApiHost();

    String getOCloudSpaceManagerUrl();
    /**
     * 聚集ai能力的平台,旧的ai能力应迁移到此平台,见{@link #getOCloudAiPhotoHostName()},
     * {@link #getOCloudAiRepairHostName()}
     *
     * @return 此ai能力平台的通用url前缀
     */
    String getAiCloudHostName();

    /**
     * 获取欢太云下线公告域名
     * @return 域名
     */
    String getOfflineCloudAnnounceDomainName();

    /**
     * 共享图集DNS地址
     * @return 域名
     */
    String getSharedDnsHost();

    /**
     * 共享图集服务器地址
     * @return 域名
     */
    String getSharedDefaultHost();

    /**
     * 云服务意见反馈配置项的服务器地址
     * @return 域名
     */
    String getSharedFaqHost();

    /**
     * 工信部备案信息的服务器地址
     * @return 域名
     */
    String getFilingInfoHost();

    /**
     * 获取跳转云相册h5URL
     * @return h5URL
     */
    String getCloudAlbumH5Url();
}
