/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AndesSearchRusConfig
 ** Description: 融合搜索配置名单.
 ** Version: 1.0
 ** Date : 2023/10/7
 ** Author: <EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wenhao.Deng   2023/10/7      1.0        created
 ***************************************************************/
package com.oplus.gallery.business_lib.model.config.allowlist

import androidx.annotation.WorkerThread
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.business_lib.model.config.allowlist.AndesSearchRusConfig.RemoteJsonContent
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.COMPONENT_PREF_NAME
import com.oplus.gallery.business_lib.util.AssetHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.safeUse
import com.oplus.gallery.foundation.util.storage.SPUtils
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.io.BufferedReader
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets

private const val DEFAULT_XML_CONFIG = "default_andes_search_config.xml"
private const val CURRENT_VERSION_KEY = "andes_search_config_version"
private const val REMOTE_XML_CONFIG = "app_gallery_andes_search_config"

/**
 * 融合搜索配置名单相关类，负责加载rus名单
 * RUS业务编码：app_gallery_andes_search_config
 * 通过json方式解析融合搜索的配置，包含信息见
 * @see RemoteJsonContent
 */
object AndesSearchRusConfig : AbsAllowListParser(REMOTE_XML_CONFIG, DEFAULT_XML_CONFIG, CURRENT_VERSION_KEY) {
    private const val TAG = "AndesSearchRusConfig"
    private const val SEARCH_TIMEOUT = 2000L

    private const val ANDES_ENTITY_DICT_FILE_NAME = "andes_entity_dict.txt"
    private const val INCOMPATIBLE_LABEL_DICT_FILE_NAME = "incompatible_label_dict.txt"
    private const val CORRECTION_FILE_NAME = "correction.txt"
    private const val SENSITIVE_FILE_NAME = "sensitive.txt"
    private const val STOP_WORDS_FILE_NAME = "stop_words.txt"
    private const val SCENE_WORDS_FILE_NAME = "scene_words.txt"

    private const val CORRECTION_FILE_NAME_EN = "correction_en.txt"
    private const val SENSITIVE_FILE_NAME_EN = "sensitive_en.txt"
    private const val STOP_WORDS_FILE_NAME_EN = "stop_words_en.txt"

    private const val DIR_CUSTOM_DICT = "custom_dict"
    private const val DIR_INCOMPATIBLE_LABEL = "incompatible"
    private const val DIR_CORRECTION = "correction"
    private const val DIR_SENSITIVE = "sensitive"
    private const val DIR_STOP_WORDS = "stop_words"
    private const val DIR_SCENE_WORDS = "scene_words"

    private const val DICT_FOLDER_NAME = "dict"

    private var andesRusListener: OnAndesRusConfigLoadListener? = null

    var andesSearchConfig: RemoteJsonContent? = RemoteJsonContent()

    private var defaultVersion = 0L

    init {
        loadConfig()
        // 调试模式下，无RUS文件，走不到 loadRemoteConfig
        if (DEBUG_SEARCH) {
            // 当前词典文件是从本地assets拷贝到配置的路径下，还不支持从远程拉取文件
            loadFileFromAssetsToDict()
            andesRusListener?.onLoadFinished()
        }
    }

    override fun loadRemoteConfig(): Boolean {
        GLog.d(TAG) { "loadRemoteConfig:$mRemoteVersion" }
        return (inputStreamFromRemote?.safeUse(AndesSearchRusConfig::parseBlock) != null)
    }

    override fun loadDefaultConfig(): Boolean {
        GLog.d(TAG) { "loadDefaultConfig:$mDefaultVersion" }
        return false
    }

    override fun loadLocalConfig(): Boolean {
        return false
    }

    override fun saveConfig(): Boolean {
        return false
    }

    override fun dump() {
        GLog.d(TAG) { "dump: currentVersion: $currentVersion" }
    }

    override fun getTag(): String {
        return TAG
    }

    @WorkerThread
    @Throws(IllegalArgumentException::class)
    private fun parseBlock(inputStream: InputStream?) {
        inputStream ?: run {
            GLog.d(TAG) { "parseBlock: inputStream is null." }
            return
        }
        runCatching {
            val startTime = System.currentTimeMillis()
            InputStreamReader(inputStream, StandardCharsets.UTF_8).use { inputReader ->
                BufferedReader(inputReader).use { bufReader ->
                    val jsonString = bufReader.readText()
                    val result = JsonUtil.fromJson(jsonString, RemoteJsonContent::class.java)
                    result?.run {
                        GLog.d(TAG, LogFlag.DL) { "parseBlock, result=$result" }
                        andesSearchConfig = result
                    }
                }
            }
            GLog.d(TAG, "[parseBlock] costTime:${GLog.getTime(startTime)}")
        }.onFailure {
            GLog.e(TAG) { "[parseBlock] get config failure: ${it.message}" }
        }
        // 当前词典文件是从本地assets拷贝到配置的路径下，还不支持从远程拉取文件
        loadFileFromAssetsToDict()
        andesRusListener?.onLoadFinished()
    }

    /**
     * 从 asset 里拷贝 query 查询相关的词典（如敏感词，停止词），并设置给 search kit
     * 包含中文和英文的，query不区分语言
     */
    private fun loadFileFromAssetsToDict() {
        loadCNFileFromAssetsToDict()
        loadEnFileFromAssetsToDict()
        GLog.d(TAG, LogFlag.DL) { "loadFileFromAssetsToDict, result=$andesSearchConfig" }
    }

    /**
     * 加载中文词典
     */
    private fun loadCNFileFromAssetsToDict() {
        loadFileFromAssetsToDict(ANDES_ENTITY_DICT_FILE_NAME, DIR_CUSTOM_DICT)
        loadFileFromAssetsToDict(INCOMPATIBLE_LABEL_DICT_FILE_NAME, DIR_INCOMPATIBLE_LABEL)
        loadFileFromAssetsToDict(CORRECTION_FILE_NAME, DIR_CORRECTION)
        loadFileFromAssetsToDict(SENSITIVE_FILE_NAME, DIR_SENSITIVE)
        loadFileFromAssetsToDict(STOP_WORDS_FILE_NAME, DIR_STOP_WORDS)
        loadFileFromAssetsToDict(SCENE_WORDS_FILE_NAME, DIR_SCENE_WORDS)
    }

    /**
     * 加载英文词典
     */
    private fun loadEnFileFromAssetsToDict() {
        loadFileFromAssetsToDict(CORRECTION_FILE_NAME_EN, DIR_CORRECTION)
        loadFileFromAssetsToDict(SENSITIVE_FILE_NAME_EN, DIR_SENSITIVE)
        loadFileFromAssetsToDict(STOP_WORDS_FILE_NAME_EN, DIR_STOP_WORDS)
    }

    @WorkerThread
    private fun loadFileFromAssetsToDict(dictFileName: String, dictDir: String) {
        runCatching {
            val internalStorageFilesPath = ContextGetter.context.filesDir.path
            // com.coloros.gallery3d/files/dict/各种词典目录/
            val targetDictDir = File(internalStorageFilesPath + File.separator + DICT_FOLDER_NAME + File.separator + dictDir)
            if (!targetDictDir.exists()) {
                targetDictDir.mkdirs()
            }
            val targetDictPath = targetDictDir.path + File.separator + dictFileName
            val targetDictFile = File(targetDictPath)

            // 本地文件的拷贝是随应用更新来更新的，不要重复拷贝
            if (targetDictFile.isFile && targetDictFile.exists() && targetDictFile.length() > 0 && !checkDictUpdate(dictFileName)) {
                // 设置 dict 路径给 AndesConfig
                setDictDirToAndesConfig(dictFileName, targetDictDir, targetDictPath)
                return
            }

            // 从 asset 拷贝词典到 com.coloros.gallery3d/files/dict/各种词典目录/
            val inputStream = ContextGetter.context.assets.open(dictFileName)
            val outputStream = FileOutputStream(targetDictFile)
            inputStream.copyTo(outputStream)

            // 设置 dict 路径给 AndesConfig
            setDictDirToAndesConfig(dictFileName, targetDictDir, targetDictPath)

            // 更新完词典再更新sp标记
            SPUtils.setLong(ContextGetter.context, COMPONENT_PREF_NAME, dictFileName, defaultVersion)
            IOUtils.closeQuietly(inputStream)
            IOUtils.closeQuietly(outputStream)
        }.onFailure {
            GLog.e(TAG, "[loadFileFromAssetsToDict] fail", it)
        }
    }

    /**
     * 给 search kit 设置对应的 query 词典路径
     * 其中 不兼容词 由于历史原因使用的是文件路径，不是目录，
     * 其余词典都使用目录，往目录里放多少文件都可以，都会当做对应含义的词典，比如中英的纠错词词典，都放到 andesSearchConfig?.correctionDictDir 里
     */
    private fun setDictDirToAndesConfig(dictFileName: String, dictDir: File, dictFilePath: String) {
        when (dictFileName) {
            ANDES_ENTITY_DICT_FILE_NAME -> andesSearchConfig?.customEntityDict = dictDir.absolutePath
            INCOMPATIBLE_LABEL_DICT_FILE_NAME -> andesSearchConfig?.incompatibleLabelDict = dictFilePath
            CORRECTION_FILE_NAME, CORRECTION_FILE_NAME_EN -> andesSearchConfig?.correctionDictDir = dictDir.absolutePath
            SENSITIVE_FILE_NAME, SENSITIVE_FILE_NAME_EN -> andesSearchConfig?.sensitiveDictDir = dictDir.absolutePath
            STOP_WORDS_FILE_NAME, STOP_WORDS_FILE_NAME_EN -> andesSearchConfig?.stopWordDictDir = dictDir.absolutePath
            SCENE_WORDS_FILE_NAME -> andesSearchConfig?.sceneWordDict = dictFilePath
        }
    }

    fun setAndesRusListener(listener: OnAndesRusConfigLoadListener?) {
        andesRusListener = listener
    }

    interface OnAndesRusConfigLoadListener {
        fun onLoadFinished()
    }

    /**
     * 判断词典是否更新
     */
    private fun checkDictUpdate(dictFileName: String): Boolean {
        runCatching {
            defaultVersion = AssetHelper.getInstance().getComponentDefaultVersion(ContextGetter.context, dictFileName).toLong()
            val currentVersion = SPUtils.getLong(ContextGetter.context, COMPONENT_PREF_NAME, dictFileName, 0)
            GLog.d(TAG, LogFlag.DL) { "checkDictUpdate dictFileName:$dictFileName, defaultVersion:$defaultVersion, currentVersion:$currentVersion" }
            if (defaultVersion > currentVersion) {
                return true
            }
        }
        return false
    }

    /**
     * Andes Search rus 内容解析类
     */
    data class RemoteJsonContent(
        /**
         * 融合搜索服务已推送的版本号
         */
        @SerializedName("version") val version: String? = null,

        /**
         * 融合搜索Kit开关
         */
        @SerializedName("isAndesSearchEnabled") val isAndesSearchEnabled: Boolean? = true,

        /**
         * 索引引擎开关
         */
        @SerializedName("isIndexEngineEnabled") val isIndexEngineEnabled: Boolean? = true,

        /**
         * 多模态开关
         */
        @SerializedName("isMultiModalEnabled") val isMultiModalEnabled: Boolean? = true,

        /**
         * OcrEmbedding开关
         */
        @SerializedName("isOcrEmbeddingEnabled") val isOcrEmbeddingEnabled: Boolean? = true,

        /**
         * 搜索超时时间
         */
        @SerializedName("searchTimeout") val searchTimeout: Long? = SEARCH_TIMEOUT,

        /**
         * 融合搜索字典路径
         */
        @SerializedName("customEntityDict") var customEntityDict: String? = null,

        /**
         * 自定义不兼容标签字典文件
         */
        @SerializedName("incompatibleLabelDict") var incompatibleLabelDict: String? = null,

        /**
         * 单标签和单关键字，是否多模态可用
         */
        @SerializedName("isSingleKeywordMMSearchEnabled") var isSingleKeywordMMSearchEnabled: Boolean? = null,

        /**
         * 纠错字典路径:对搜索词纠错，生份证-->身份证；自型车-->自行车
         */
        @SerializedName("correctionDictDir") var correctionDictDir: String? = null,

        /**
         * 敏感词字典路径:搜索语句中命中敏感词，拒识整条搜索语句
         */
        @SerializedName("sensitiveDictDir") var sensitiveDictDir: String? = null,

        /**
         * 停止词字典路径:搜索中包含停止词，不搜索停止词，“在4月1号”，仅搜索“4月1号”
         */
        @SerializedName("stopWordDictDir") var stopWordDictDir: String? = null,

        /**
         * 场景词字典路径:搜索中包含场景词，做为标记，用于显示排序和搜索处理
         */
        @SerializedName("sceneWordDict") var sceneWordDict: String? = null,

        /**
         * 是否开启云端sdk POI查询，默认关闭
         */
        @SerializedName("isPoiSdkEnabled") var isPoiSdkEnabled: Boolean = false,

        /**
         * 是否开启端侧POI搜索，默认开启
         */
        @SerializedName("isPoiSearchEnabled") var isPoiSearchEnabled: Boolean = true
    )
}