/**
 * IK Analyzer release 5.0
 * <p>
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * <p>
 * provided by Linliangyi and copyright 2012 by Oolong studio
 *******************************************************/
package com.oplus.gallery.business_lib.model.search.segment;

import android.text.TextUtils;

public class Dictionary {
    private static volatile Dictionary sSingleton;

    public static Dictionary getInstance() {
        if (sSingleton == null) {
            synchronized (Dictionary.class) {
                if (sSingleton == null) {
                    sSingleton = new Dictionary();
                }
            }
        }
        return sSingleton;
    }

    private DictSegment mMainDict;

    private Dictionary() {
        mMainDict = new DictSegment((char) 0);
    }

    public void reset() {
        mMainDict = new DictSegment((char) 0);
    }

    /**
     * Add a word
     *
     * @param word
     */
    public void addWord(String word) {
        // As keyword had changed to lower case, so we need parse the dict to lower case as well.
        if (!TextUtils.isEmpty(word)) {
            mMainDict.fillSegment(word.toLowerCase().toCharArray());
        }
    }

    public Hit matchInMainDict(char[] charArray, int begin, int length) {
        return mMainDict.match(charArray, begin, length);
    }

    /**
     * match charArray from Hit
     *
     * @param charArray
     * @param currentIndex
     * @param matchedHit
     * @return Hit
     */
    public Hit matchWithHit(char[] charArray, int currentIndex, Hit matchedHit) {
        DictSegment ds = matchedHit.getMatchedDictSegment();
        return ds.match(charArray, currentIndex, 1, matchedHit);
    }
}
