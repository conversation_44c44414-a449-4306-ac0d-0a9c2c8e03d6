/***********************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * OPLUS_EDIT
 * File:  - SyncResultConstants.java
 * Description:
 * Version: 1.0
 * Date : 2018/07/23
 * Author: <PERSON><PERSON>.<PERSON>@Apps.Gallery3D
 *
 * ---------------------Revision History: ---------------------
 * <author>                         <data>    <version>       <desc>
 * Zhi.Dong@Apps.Gallery3D       2018/07/23    1.0           build this module
</desc></version></data></author> */
package com.oplus.gallery.business_lib.cloudsync

/**
 * 添加新的异常值要在埋点上报处添加对应的result[com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant]
 */
object SyncResultConstants {
    const val RESULT_SUCCESS = 0
    const val RESULT_FAIL = 1

    /**
     * 恢复时有更多数据
     */
    const val RESULT_RECOVERY_HAS_MORE = 2

    /**
     * 开始同步元数据
     */
    const val RESULT_SYNC_META_DATA_START = 3

    // ---------------以下是相册判断不满足进行同步的条件-------------

    /**
     * 外部条件限制停止，保留省电模式、电量、温度、等待
     */
    const val RESULT_LIMIT = 101

    /**
     * 权限拒绝
     */
    const val RESULT_PERMISSION_DENIED = 102

    /**
     * 无联网权限
     */
    const val RESULT_NO_NETWORK_PERMISSION = 103

    /**
     * 省电模式
     */
    const val RESULT_POWER_SAVING_MODE = 104

    /**
     * 低电量
     */
    const val RESULT_LOW_BATTERY = 105

    /**
     * 充电时低电量
     */
    const val RESULT_LITTLE_LOW_BATTERY_NOT_CHARGING = 106

    /**
     * 温度过高
     */
    const val RESULT_TEMPERATURE_HIGH = 107

    /**
     * 未登录或者开关关闭
     */
    const val RESULT_NOT_LOGIN_OR_SWITCH_CLOSE = 108

    /**
     * 网络无连接
     */
    const val RESULT_NETWORK_NO_CONNECT = 109

    /**
     * 数据流量限制
     */
    const val RESULT_TRAFFIC_LIMIT = 110

    /**
     * 第三方应用在前台，并且全屏
     */
    const val RESULT_THIRD_APP_FULL_FRONT = 111

    /**
     * 不满足充电条件
     */
    const val RESULT_NOT_MEETING_CHARGING_CONDITIONS = 112

    /**
     * 禁止同步的场景
     */
    const val RESULT_DISALLOW_SYNC_SCENE = 113

    /**
     * 温度过高，且不对用户展示
     *
     * 避免温度在前台阈值和后台阈值之间时，进入前台触发同步切换提示有延迟导致闪现高温提示
     */
    const val RESULT_TEMPERATURE_HIGH_HIDE = 114

    /**
     * 不指代具体情况，只作为范围阈值
     */
    const val RESULT_CONDITION_FAILED_MAX = 999

    //--------------以下是cloudkit返回的错误码-------------------

    /**
     * 网络异常
     */
    const val RESULT_NETWORK_ERROR = 1001

    /**
     * 登录态过期，token过期，需要重新获取token
     */
    const val RESULT_AUTH_ERROR = 1002

    /**
     * 云端空间不足
     */
    const val RESULT_INSUFFICIENT_SPACE = 1003

    /**
     * 本地手机空间不足
     */
    const val RESULT_LOCAL_INSUFFICIENT_SPACE = 1004

    /**
     * 请求频繁，限流
     */
    const val RESULT_REQUEST_TOO_FREQUENT = 1005

    /**
     * 用户的数据被迁移或者归档变成冷存储
     */
    const val RESULT_DATA_COLD_STANDBY = 1006

    /**
     * 服务器下发配置把元数据和io功能下线，当前仅可能出现在外销部分地区服务下线时
     */
    const val RESULT_SERVICE_OFFLINE = 1009

    /**
     * 服务端异常
     */
    const val RESULT_SERVICE_ERROR = 1010

    /**
     * 已经删除云端数据的场景下，继续通过接口去删除云端数据，会返回此错误码
     */
    const val RESULT_SERVER_CLEARED_DATA = 1011
}