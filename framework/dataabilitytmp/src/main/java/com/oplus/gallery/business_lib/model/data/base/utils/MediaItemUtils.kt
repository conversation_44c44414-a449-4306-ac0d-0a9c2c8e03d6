/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaItemUtils.java
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/12
 ** Author: huang.linpeng@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** huang.linpeng@Apps.Gallery3D      2020/9/12      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.data.base.utils

import android.util.Size
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.UriImage
import com.oplus.gallery.foundation.util.systemcore.LowMemUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.app.AppConstants

object MediaItemUtils {

    private const val TAG = "MediaItemUtils"

    /**
     * 两亿像素照片尺寸
     */
    private const val TWO_HUNDRED_MILLION_PIXELS_PHOTO_SIZE = 12000 * 16000 // width * height

    /**
     * 小内存（<=4G）机型下文件大小判定阈值
     */
    private const val MAX_FILE_SIZE = 200 * 1024 * 1024 // 200MB
    private const val MIDDLE_FILE_SIZE = 50 * 1024 * 1024 // 50MB

    /**
     * 小内存（<=4G）机型下图片解码尺寸判定阈值
     */
    private const val MAX_DECODE_SIZE = 20000 * 20000 // width * height
    private const val MIDDLE_DECODE_SIZE = TWO_HUNDRED_MILLION_PIXELS_PHOTO_SIZE

    /**
     * 判断item是否200m像素照片
     */
    @JvmStatic
    fun is200mImageItem(item: MediaItem?): Boolean {
        item ?: let {
            GLog.e(TAG) { "is200mImageItem, item == null" }
            return false
        }
        val width: Long = item.width.toLong()
        val height: Long = item.height.toLong()
        val is200mImage = is200mImageItem(item.width, item.height)
        GLog.d(TAG) { "is200mImageItem, width:$width x height:$height, is200mImage:$is200mImage " }
        return is200mImage
    }

    /**
     * 判断item是否200m像素照片
     */
    @JvmStatic
    fun is200mImageItem(width: Int, height: Int): Boolean =
        width.toLong() * height.toLong() > TWO_HUNDRED_MILLION_PIXELS_PHOTO_SIZE

    /**
     * 判断MediaItem是否为超大图
     */
    @JvmStatic
    fun isLargeItem(item: MediaItem?): Boolean {
        item ?: let {
            GLog.e(TAG, "isLargeItem, item == null")
            return false
        }
        val isLarge = isLargeItem(item.width, item.height, item.fileSize)
        GLog.d(TAG) {
            "isLargeItem, FileSize:${item.fileSize} Byte, width:${item.width} x height:${item.height}," +
                " isLarge:$isLarge , isLowMem:${LowMemUtils.isLowMem()} "
        }
        return isLarge
    }

    /**
     * 判断是否为超大图，不同机器内存下，超大图标准不同
     *
     * @param width 图片宽度
     * @param height 图片高度
     * @param fileSize 资源文件大小
     * @return
     */
    @JvmStatic
    fun isLargeItem(width: Int, height: Int, fileSize: Long): Boolean {
        val decodeSize = width * height
        val isLarge = if (LowMemUtils.isRamLessThan4G()) {
            ((fileSize > MAX_FILE_SIZE) ||
                (decodeSize > MAX_DECODE_SIZE) ||
                ((decodeSize > MIDDLE_DECODE_SIZE) && (fileSize > MIDDLE_FILE_SIZE)))
        } else false
        return isLarge
    }

    /**
     * 专门用于MediaItem的子类进行类型判断
     */
    @JvmStatic
    fun typeOf(src: MediaItem?, dst: Class<out MediaItem>): Boolean {
        return src?.itemType == dst.name
    }

    /**
     * 当前MediaItem是否具有对应的format
     */
    @JvmStatic
    fun isItemSupportFormat(item: MediaItem?, format: Int): Boolean {
        return item?.getSupportFormat(format) == format
    }

    /**
     * 当前MediaItem是否支持NFC功能
     */
    @JvmStatic
    fun isSupportNFC(item: MediaItem?): Boolean {
        return item?.let {
            typeOf(it, UriImage::class.java) ||
                    typeOf(it, LocalImage::class.java) ||
                    typeOf(it, LocalVideo::class.java)
        } ?: false
    }

    /**
     * LocalVideo.width和height的值由于旋转的问题可能反过来了,需要纠正
     * @return 返回的size 是真实文件的宽高
     */
    @JvmStatic
    fun getRealVideoSize(videoItem: LocalVideo): Size {
        return if (videoItem.rotation % AppConstants.Degree.DEGREE_180 != 0) {
            //需要旋转
            Size(videoItem.height, videoItem.width)
        } else {
            Size(videoItem.width, videoItem.height)
        }
    }

    /**
     * 某个目标模式的tagFlag值是否有变更
     * @param newTagFlags 新的tagFlags
     * @param oldTagFlags 旧的tagFlags
     * @param targetMode 不同模式的tagFlag值，如[FLAG_SUPPORT_OLIVE]
     * @return 新旧tagFlags中有关targetMode的值 是否有变动
     */
    @JvmStatic
    fun isTargetModeTagFlagChanged(newTagFlags: Int, oldTagFlags: Int, targetMode: Int): Boolean {
        return ((newTagFlags and targetMode) != (oldTagFlags and targetMode))
    }

    /**
     * 某个目标模式的extTagFlag值是否有变更
     * @param newExtTagFlags 新的extTagFlag
     * @param oldExtTagFlags 旧的extTagFlag
     * @param targetMode 不同模式的tagFlag值，如[EXT_OLIVE_HDR_VIDEO]
     * @return 新旧extTagFlag中有关targetMode的值 是否有变动
     */
    @JvmStatic
    fun isTargetModeExtTagFlagChanged(newExtTagFlags: Long, oldExtTagFlags: Long, targetMode: Long): Boolean {
        return ((newExtTagFlags and targetMode) != (oldExtTagFlags and targetMode))
    }
}