/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : AlbumDataCacheManager.kt
 ** Description: 图集数据缓存管理
 ** Version    : 1.0
 ** Date       : 2024/11/20
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2024/11/20    1.0              init
 *************************************************************************************************/
package com.oplus.gallery.business_lib.model.data.picture.set

import android.content.ContentValues
import android.os.Bundle
import android.provider.MediaStore
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.convertToCursor
import com.oplus.gallery.business_lib.model.data.base.item.isRecycle
import com.oplus.gallery.business_lib.model.data.base.item.isValidData
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.trigger.operator.OperationType
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.util.concurrent.ConcurrentHashMap

/**
 * 图集数据缓存类接口
 */
internal interface IAlbumDataCache {
    /**
     * 图集标记，区分不同图集
     */
    val tag: String

    /**
     * 缓存同步策略类，业务图集根据具体情况实现
     */
    val syncStrategy: IAlbumDataSyncStrategy

    /**
     * 获取数据对象
     * @param range 获取数据的范围
     * @return 返回MediaItem对象列表
     */
    fun getItems(range: IntRange): List<MediaItem>

    /**
     * 更新数据缓存，在数据未获取到缓存，从数据库查询数据后更新缓存时调用
     *
     * 业务加载数据后，保存到缓存中。
     * 1. 首次更新，直接写入
     * 2. 再次更新，有交集或相邻，则确定为有效区间，合并进去，否则抛弃
     *
     * @param range 更新缓存数据的范围
     * @param items 更新缓存数据的MediaItem
     */
    fun setItems(range: IntRange, items: List<MediaItem>): Unit

    /**
     * 更新数据缓存，在数据库出现diff后调用。
     *
     * 接口生效条件：
     * 1. 缓存进行初始化之后生效
     * 2. diff数据在缓存范围内，超出范围后无法确定其连续性
     */
    fun setItems(items: Pair<OperationType, Pair<ContentValues, ContentValues?>>): Boolean

    /**
     * 更新缓存item数量
     */
    fun setItemCount(totalCount: Int)

    /**
     * 获取缓存item数量
     */
    fun getItemCount(): Int

    /**
     * 更新缓存Image数量
     */
    fun setImageCount(imageCount: Int)

    /**
     * 获取缓存Image数量
     * @return Triple<totalCount: Int, imageCount: Int, videoCount: Int>
     */
    fun getImageCount(): Int

    /**
     * 更新缓存Video数量
     */
    fun setVideoCount(videoCount: Int)

    /**
     * 获取缓存Video数量
     * @return Triple<totalCount: Int, imageCount: Int, videoCount: Int>
     */
    fun getVideoCount(): Int

    /**
     * 获取指定的缓存数据字段
     * @param keys 指定的缓存字段key
     */
    fun getSpecifiedAttributes(keys: List<String>): Bundle

    /**
     * 更新指定的缓存字段
     * @param inBundle 缓存数据 key-value
     */
    fun setSpecifiedAttributes(inBundle: Bundle)

    /**
     * 获取指定item在缓存中的index
     */
    fun getIndexOfItem(path: Path): Int

    /**
     * 缓存数据是否已经初始化
     */
    fun isCacheInit(): Boolean
}

/**
 * 图集数据同步策略类。
 * 1. decideOperation接口，确定diff的数据需要执行的缓存操作：删除、插入、更新
 * 2. orderCompare接口，实现排序能力，确定diff数据操作在缓存中的index，将数据diff更新到对应的缓存位置
 */
internal interface IAlbumDataSyncStrategy {
    /**
     * 业务方根据输入的diff信息，决策缓存更新的操作类型。
     * 1. 根据图集具体的查询条件，过滤非本图集记录，过滤条件：目录、是否回收站、invalid状态等
     * 2. 根据变化前后的数据，决策改数据在缓存的操作类型：插入、更新、删除
     *     - 数据库的操作不一定等同于图集数据缓存更新的操作，例如：回收站的操作是update，但是对于图集数据缓存却是[删除/新增]
     *
     * @param operationType 数据库操作的类型，@see[OperationType]。
     * @param valueChangedPair 数据库数据变更情况。Pair<new:ContentValues, old:ContentValues?>
     * @return 缓存操作类型 @see[OperationType]，返回null代表无需处理此数据
     */
    fun decideOperation(
        operationType: OperationType,
        valueChangedPair: Pair<ContentValues, ContentValues?>
    ): OperationType?

    /**
     * 比较两个入参对象的排序位置。用来确定diff的数据条目在缓存中的排序位置。
     *
     * 注意：相同的order，要按照其id进行排序，所有的item order必须唯一，故实现排序时要加入唯一key，例如:_id
     *
     * @param item1 缓存中的对象
     * @param item2 diff新增的对象
     *
     * @return 排序位置的比较结果
     * -  0 : 排序位置相同
     * - -1 : diff新增对象排序靠后
     * -  1 : diff新增对象排序靠前
     */
    fun orderCompare(item1: MediaItem, item2: ContentValues): Int

    /**
     * 比较两个入参对象的排序位置。用来确定diff的数据条目在缓存中的排序位置。
     *
     * 注意：相同的order，要按照其id进行排序，所有的item order必须唯一，故实现排序时要加入唯一key，例如:_id
     *
     * @param item1 缓存中的对象
     * @param item2 diff新增的对象
     *
     * @return 排序位置的比较结果
     * -  0 : 排序位置相同
     * - -1 : diff新增对象排序靠后
     * -  1 : diff新增对象排序靠前
     */
    fun orderCompare(item1: MediaItem, item2: MediaItem): Int

    /**
     * 构建Item对象的Path
     * @param values diff回调的更新后的数据库信息
     * @return 返回构建好的Path，若values数据异常导致构建失败，则返回null
     */
    fun generatePath(values: ContentValues): Path?

    /**
     * 构建Item对象
     * @param values diff回调的更新后的数据库信息
     * @return 返回构建好的Item对象，若values数据异常导致构建失败，则返回null
     */
    fun generateItem(values: ContentValues): MediaItem?
}

internal abstract class LocalAlbumDataSyncStrategy(val projection: Array<String>) : IAlbumDataSyncStrategy {
    override fun decideOperation(
        operationType: OperationType,
        valueChangedPair: Pair<ContentValues, ContentValues?>
    ): OperationType? {

        // 过滤非法数据：修改前或修改后，有一个满足
        val curIsValidData = valueChangedPair.first.isValidData()
        val lastIsValidData = valueChangedPair.second?.isValidData() == true
        val isDeleteDirectly = curIsValidData.not() && lastIsValidData
        val isInsertDirectly = curIsValidData && lastIsValidData.not()
        if (curIsValidData.not() && lastIsValidData.not()) {
            return null
        }

        // 过滤回收站：修改前或修改后，有一个满足
        val curIsRecycle = valueChangedPair.first.isRecycle()
        val lastIsRecycle = valueChangedPair.second?.isRecycle() ?: curIsRecycle
        val isOpRecycle = curIsRecycle && lastIsRecycle.not()
        val isOpReStore = curIsRecycle.not() && lastIsRecycle
        // 无需处理回收站数据的更新、删除、插入。lastIsRecycle在插入和删除时，和当前状态保持一致，故赋值为curIsRecycle。
        if (curIsRecycle && lastIsRecycle) {
            return null
        }

        // 过滤目录：修改前或修改后，有一个满足
        val curIsInBucket = isInAlbumBucket(valueChangedPair.first)
        val lastIsInBucket = isInAlbumBucket(valueChangedPair.second)
        val isOpMoveOut = curIsInBucket.not() && lastIsInBucket
        val isOpMoveIn = curIsInBucket && lastIsInBucket.not()
        if (curIsInBucket.not() && lastIsInBucket.not()) {
            return null
        }

        val isInsertOp = operationType == OperationType.INSERT_LOCAL
        val isDeleteOp = operationType == OperationType.DELETE_LOCAL
        // 找出新增数据，无需处理回收站新增数据
        val isInsert = isInsertOp || (isDeleteOp.not() && (isInsertDirectly || isOpReStore || isOpMoveIn))
        if (isInsert) return OperationType.INSERT_LOCAL

        // 找出删除数据，无需处理回收站删除数据
        val isDelete = isDeleteOp || (isDeleteDirectly || isOpRecycle || isOpMoveOut)
        if (isDelete) return OperationType.DELETE_LOCAL

        // 剩余的就是update
        return OperationType.UPDATE_LOCAL
    }

    /**
     * 是否是图集目录范围内的数据。
     *
     * @param values 变更的数据内容
     *
     * @return 是否该图集几目录内的数据变更
     */
    abstract fun isInAlbumBucket(values: ContentValues?): Boolean

    override fun generatePath(values: ContentValues): Path? {
        val mediaType = values.getAsInteger(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE) ?: return null
        val id = values.getAsInteger(GalleryStore.GalleryColumns.LocalColumns._ID) ?: return null
        return when (mediaType) {
            MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE -> LocalImage.ITEM_PATH.getChild(id)
            MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO -> LocalVideo.ITEM_PATH.getChild(id)
            else -> null
        }
    }

    override fun generateItem(values: ContentValues): MediaItem? {
        return generatePath(values)?.let { childPath ->
            values.convertToCursor(projection)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    LocalMediaItem.loadOrUpdateItem(childPath, cursor)
                } else {
                    GLog.w(TAG, LogFlag.DL) { "[generateItem] moveToFirst failed, return null!" }
                    null
                }
            } ?: kotlin.run {
                GLog.w(TAG, LogFlag.DL) { "[generateItem] convertToCursor failed, return null!" }
                null
            }
        } ?: run {
            GLog.w(TAG, LogFlag.DL) { "[generateItem] generatePath failed, return null!" }
            null
        }
    }

    private companion object {
        private const val TAG = "LocalAlbumDataSyncStrategy"
    }
}

/**
 * 图集数据缓存管理类。
 */
internal object AlbumDataCacheManager {
    private val caches = ConcurrentHashMap<String, IAlbumDataCache>()

    /**
     * 获取[key]对应的缓存实例，如果没有实例，需要业务创建。
     * @param key 缓存key
     * @param creator 缓存实例创建接口
     * @return 缓存实例
     */
    fun getAlbumDataCache(key: String, creator: () -> IAlbumDataCache): IAlbumDataCache {
        return caches.computeIfAbsent(key) { creator.invoke() }
    }
}