/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PoiUpdateJob.kt
 ** Description : poi地点更新任务
 ** Version     : 1.0
 ** Date        : 2025/4/2 15:35
 ** Author      : 80398228
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** qiupeng              2025/4/2      1.0     OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.business_lib.model.data.location.service

import android.content.Context
import android.location.Location
import android.os.Handler
import android.os.Looper
import com.oplus.gallery.business_lib.model.data.location.LocationManager.lookupReverseAddresses
import com.oplus.gallery.business_lib.model.data.location.api.GpsImg
import com.oplus.gallery.business_lib.model.data.location.api.LocationReverseGeocode
import com.oplus.gallery.business_lib.model.data.location.api.LocationReverseGeocode.Companion.READ_CACHE_OR_GEOCODER
import com.oplus.gallery.business_lib.model.data.location.api.PoiReverseAddress
import com.oplus.gallery.business_lib.model.data.location.impl.PoiGridInfo
import com.oplus.gallery.business_lib.model.data.location.impl.PoiRouteInfo
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.business_lib.model.data.location.utils.PoiCacheService
import com.oplus.gallery.business_lib.model.data.location.utils.PoiConfigFileParser
import com.oplus.gallery.business_lib.model.data.location.utils.PoiConfigHelper
import com.oplus.gallery.business_lib.model.data.location.utils.PoiDBHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GLog.getTime
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import java.io.File
import java.util.LinkedList
import java.util.Queue

/**
 * 地点更新任务
 * 查询出所有本地的地点信息，到服务端进行对比和更新
 */
class PoiUpdateJob(private val context: Context, private val poiFileCallback: PoiFileCallback) {
    private var poiCacheService: PoiCacheService? = null
    private var reschedule = false
    private var poiConfigHelper: PoiConfigHelper = PoiConfigHelper
    private var poiConfigFileParser: PoiConfigFileParser? = null

    private var handler: Handler? = null

    /**
     * 当前模型版本号
     */
    private var curModelVersion: Int = 0

    /**
     * poi内存缓存
     */
    private val poiCache: HashMap<Double, MutableList<PoiRouteInfo>> = HashMap()
    private val poiTask: Queue<GpsImg> = LinkedList()
    private var jobCallback: JobCallback? = null
    var scanInCacheCnt = 0
    var scanInCloud = 0
    var unsolvedGpsCount = 0

    private var poiLimitCount = poiConfigHelper.getPoiLimitedReqCount()

    init {
        val startTime = System.currentTimeMillis()
        poiCacheService = PoiCacheService
        Looper.myLooper()?.let {
            handler = Handler(it)
        }
        GLog.d(TAG, LogFlag.DL) { "[init] costTime:" + getTime(startTime) }
    }

    fun execute(modelVersion: Int, callback: JobCallback) {
        curModelVersion = modelVersion
        jobCallback = callback
        GTrace.traceBegin("startPoiUpdateJob")
        reschedule = executeImpl()
        GTrace.traceEnd()
    }

    private fun executeImpl(): Boolean {
        if (!PoiConfigHelper.isPoiSearchEnable()) {
            GLog.d(TAG, LogFlag.DL) { "PoiUpdateJob is stop by poiEnable = false" }
            return false
        }
        if (!loadPoiFile(curModelVersion)) {
            GLog.d(TAG, LogFlag.DL) { "PoiUpdateJob is stop by loadPoiFile = false" }
            return false
        }
        val startTime = System.currentTimeMillis()
        //更新poi数据库
        updatePoiDb()
        //查询需要poi查询的gps
        val unsolvedGps = poiCacheService?.collectUnresolvedPoiGps(curModelVersion)
        if (unsolvedGps.isNullOrEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "unsolved=0,duration: ${System.currentTimeMillis() - startTime}" }
            clear()
            return false
        }
        poiTask.addAll(unsolvedGps)
        unsolvedGpsCount = unsolvedGps.size
        poiLimitCount = if (GProperty.DEBUG_POI_SEARCH_ENABLE) {
            Integer.MAX_VALUE
        } else {
            poiConfigHelper.getPoiLimitedReqCount()
        }
        GLog.d(TAG, LogFlag.DL) { "unsolved=$unsolvedGpsCount,duration: ${System.currentTimeMillis() - startTime}" }
        if (unsolvedGpsCount > 0) {
            tryPollPoiTask()
        }
        return false
    }

    fun clear() {
        poiCache.clear()
        poiConfigFileParser?.clear()
        poiTask.clear()
    }

    /**
     * 加载预制POI文件到内存
     *
     */
    private fun loadPoiFile(modelVersion: Int): Boolean {
        val files = poiFileCallback.loadPoiFile()
        GLog.d(TAG, LogFlag.DL) { "loadPoiFile, poiFilesSize: ${files.size}" }
        poiConfigFileParser = PoiConfigFileParser(context, files, modelVersion)
        poiConfigFileParser?.getGpsKeyMap()?.let { poiCache.putAll(it) }
        if (poiCache.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "loadPoiFile, poiCache is empty" }
            return false
        }
        poiFileCallback.onPoiFileLoaded()
        return true
    }

    /**
     * 更新poi_grid_route表
     */
    private fun updatePoiDb() {
        val scannedPoiImgs = PoiDBHelper.getInstance().scannedPoiImgList.toHashSet()
        val canScanPoiImgs = PoiDBHelper.getInstance().allImageForPoiScanFromLocalMedia.associateBy { it.gpsKey }
        val deleteImageList = ArrayList<Long>()
        val updateImageList = ArrayList<GpsImg>()

        val invalidPoiRouteIds = PoiDBHelper.getInstance().allPoiAddress.filter {
            val invalidKey = it.poiHash?.toDouble() ?: 0
            !poiCache.containsKey(invalidKey)
        }.map { it.id }
        scannedPoiImgs.forEach {
            if (!canScanPoiImgs.containsKey(it)) { //若图片被删除
                deleteImageList.add(it)
            }
        }
        canScanPoiImgs.forEach {
            if (!scannedPoiImgs.contains(it.key)) {
                updateImageList.add(it.value)
            }
        }
        //清理poi_grid_route表
        PoiDBHelper.getInstance().deletePoiGrid(deleteImageList)
        PoiDBHelper.getInstance().deletePoiGridByIds(invalidPoiRouteIds)
        GLog.d(TAG, LogFlag.DL) { "deletePicture=${deleteImageList.size},deletePoi=${invalidPoiRouteIds.size}" }
    }

    private fun tryPollPoiTask() {
        val scanCount = scanInCloud + scanInCacheCnt
        if (scanCount % SCAN_BATCH == 0) { //每50次扫描等待一次
            try {
                Thread.sleep(POI_SCAN_DURATION)
            } catch (e: InterruptedException) {
                GLog.e(TAG, LogFlag.DL, e) { "sleep" }
            }
        }
        if ((scanCount % SCAN_BATCH == 0) && (jobCallback?.isJobContinue() != true)) {
            jobCallback?.onJobEnd()
            return
        }
        handler?.post {
            val unsolvedGps = poiTask.poll()
            if (unsolvedGps == null) {
                jobCallback?.onJobEnd()
                return@post
            }
            val gpsKey = unsolvedGps.gpsKey
            val lat = unsolvedGps.lat
            val lng = unsolvedGps.lng
            val id = unsolvedGps.galleryId
            if (GeoCacheService.ENABLE_MONITOR_BATTERY) {
                val battery = BatteryStatusUtil.getCurrentBatteryPercent(context)
                if (!BatteryStatusUtil.isBatteryInCharging(false) && (battery < GeoCacheService.LOWER_MONITOR_BATTERY)) {
                    GLog.e(TAG, LogFlag.DL) { "[execute] Battery is low and not in charging, mission rejected" }
                    return@post
                }
            }
            GLog.d(TAG, LogFlag.DL) { "[PoiUpdateJob] id=$id,lat=$lat,lng=$lng,gpsKey=$gpsKey" }
            geocoder(id, gpsKey.toDouble(), lat, lng)
        }
    }

    /**
     * 根据经纬度生成POI地址信息
     *
     * @param id
     * @param gpsKey 图片的gpsKey
     * @param lat 图片经度
     * @param lng 图片纬度
     */
    private fun geocoder(id: Int, gpsKey: Double, lat: Double, lng: Double) {
        //1.内存缓存
        if (geocoderByCache(id, gpsKey, lat, lng)) {
            return
        }
        //2.云侧查询
        geocoderByCloud(id, gpsKey, lat, lng)
    }

    /**
     * 根据缓存数据，获取POI地址信息
     * 1.优先根据Gps_key判断是否有类似地址
     * 2.根据距离判断，poi经纬度中心+半径确定范围，落在该范围的图片支持使用该poi搜索
     * @param id 图片唯一_id
     * @param gpsKey 图片gps_key
     * @param lat 维度
     * @param lng 经度
     * @return
     */
    private fun geocoderByCache(id: Int, gpsKey: Double, lat: Double, lng: Double): Boolean {
        val poiCacheAddresses = mutableListOf<Pair<Float, PoiRouteInfo>>()
        poiCache[gpsKey]?.let {
            it.forEach { route ->
                poiCacheAddresses.add(Pair(0f, route))
            }
        }
        if (poiCacheAddresses.isEmpty()) {
            poiCache.values.forEach { pois ->
                pois.forEach {
                    val result = FloatArray(1)
                    Location.distanceBetween(lat, lng, it.latitude, it.longitude, result)
                    val cacheRadius = if (it.radius > 0) {
                        it.radius
                    } else {
                        poiConfigHelper.getRadiusByType(it.poiAddrType)
                    }
                    if (result[0] <= cacheRadius) {
                        poiCacheAddresses.add(Pair(result[0], it))
                    }
                }
            }
        }
        scanInCacheCnt++
        if (poiCacheAddresses.isNotEmpty()) {
            val minItem = poiCacheAddresses.minBy { it.first }
            //获取local_media图片_id和poi_id
            recordPoiLocations(id, gpsKey, listOf(minItem.second))
            GLog.d(TAG, LogFlag.DL) { "1.geocoder by cache,$gpsKey, $poiCacheAddresses" }
            tryPollPoiTask()
            return true
        }
        //若没有在缓存中匹配到poi，则该gpsKey后续不再扫描。若缓存版本有更新，更新该gpsKey数据。后续版本上DT动态更新时处理
        poiCacheService?.writePoiGridBatch(listOf(PoiGridInfo(gpsKey.toLong(), id.toLong(), listOf(INVALID_POI_ID))), curModelVersion)
        GLog.d(TAG, LogFlag.DL) { "1.geocoder by cache invalid,$gpsKey, $poiCacheAddresses" }
        tryPollPoiTask()
        return true
    }

    /**
     * 云端查询POI信息，默认受RUS管控
     * AndesSearchRusConfig.andesSearchConfig?.isPoiSdkEnabled = true，则支持geocoder和poi sdk查询
     * @param id 图片唯一_id
     * @param gpsKey 图片gps_key
     * @param lat 维度
     * @param lng 经度
     */
    private fun geocoderByCloud(id: Int, gpsKey: Double, lat: Double, lng: Double): Boolean {
        val radius = poiConfigHelper.getPoiReverseDefaultRadius()
        //poi sdk访问
        if ((!PoiConfigHelper.isPoiSdkEnable() || poiConfigHelper.getCurrentPoiReqCountDaily(context) > poiLimitCount)
            && !GProperty.DEBUG_POI_SEARCH_ENABLE
        ) {
            GLog.d(TAG, LogFlag.DL) { "2.[interrupted] interrupted by poiReq limit=$poiLimitCount,poi cloud=${PoiConfigHelper.isPoiSdkEnable()}" }
            tryPollPoiTask()
            return false
        }
        poiConfigHelper.setCurrentPoiReqCountDaily(poiConfigHelper.getCurrentPoiReqCountDaily(context) + 1, context)
        scanInCloud++
        lookupReverseAddresses(lat, lng, radius, READ_CACHE_OR_GEOCODER,
            object : LocationReverseGeocode.IPoiReverseListener {
                override fun onGetReverseAddress(reverseResult: PoiReverseAddress?) {
                    GLog.d(TAG, LogFlag.DL) { "2.geocoder by poi sdk,$gpsKey,$reverseResult" }
                    if (reverseResult?.poiList.isNullOrEmpty()) { //该地址无法获取POI，则不支持poi检索
                        poiCacheService?.writePoiGridBatch(listOf(PoiGridInfo(gpsKey.toLong(), id.toLong(), listOf(INVALID_POI_ID))), curModelVersion)
                    } else {
                        reverseResult?.let {
                            createPoiAddress(id, gpsKey, lat, lng, it)
                        }
                    }
                    tryPollPoiTask()
                    return
                }
            }
        )
        return false
    }

    /**
     * 根据poi地址创建poi_route数据库可存储的信息，并保存到数据库和缓存中
     *
     * @param id 图片唯一_id
     * @param gpsKey 图片gps_key
     * @param address poi地址信息，包含多个poi地址
     */
    private fun createPoiAddress(id: Int, gpsKey: Double, lat: Double, lng: Double, address: PoiReverseAddress) {
        val locations: ArrayList<PoiRouteInfo> = ArrayList()
        //存储语义描述等信息
        /*address.poiList?.forEach {
            val location = PoiRouteInfo(
                gpsKey.toLong(), it.latitude, it.longitude, it.poiName, it.fullAddress, poiConfigHelper.getPoiReverseDefaultRadius(),
                LocationHelper.makeKey(it.latitude, it.longitude), null, "", POI_ROUTE_INFO_TYPE_REVERSE,
                POI_ROUTE_INFO_TYPE_REVERSE_V1, true, curModelVersion, it.poiType
            )
            locations.add(location)
        }*/
        extendPoiRadius(gpsKey, lat, lng, locations)
        recordPoiLocations(id, gpsKey, locations)
    }

    /**
     * 记录POI信息到poi_route、poi_grid_route、内存缓存中
     *
     * @param id 图片唯一_id
     * @param gpsKey 图片gps_key
     * @param locations poi列表
     */
    private fun recordPoiLocations(id: Int, gpsKey: Double, locations: List<PoiRouteInfo>) {
        if (locations.isNotEmpty()) {
            //获取local_media图片_id和poi_id
            poiCacheService?.writePoiBatch(locations)

            //存入内存
            locations.forEach {
                poiCache[it.poiHash.toDouble()] = mutableListOf(it)
            }
            val firstLocation = locations.first()
            //存入poigrid表，优先选择距离最近的
            createPoiGridInfo(id, gpsKey, firstLocation)
        }
    }

    /**
     * 扩展POI半径
     *
     * @param gpsKey 图片gps_key
     * @param lat 图片维度
     * @param lng 图片经度
     * @param poiRouteInfos
     */
    private fun extendPoiRadius(gpsKey: Double, lat: Double, lng: Double, poiRouteInfos: List<PoiRouteInfo>) {
        //判断数据库是否有相同地址，有则扩展POI范围
        val updateLocations = HashMap<Double, MutableList<PoiRouteInfo>>()
        for ((key, poiList) in poiCache) {
            val poi = poiList.firstOrNull() ?: continue
            poiRouteInfos.forEach { item ->
                if ((poi.poiName == item.poiName) && (poi.latitude == item.latitude) && (poi.longitude == item.longitude)) {
                    val oldRadius = poi.radius
                    val newCenterLat = poi.latitude
                    val newCenterLng = poi.longitude
                    val distance = FloatArray(1)
                    Location.distanceBetween(poi.latitude, poi.longitude, lat, lng, distance)
                    val newRadius = distance[0].toInt()
                    if (newRadius > oldRadius) {
                        val newPoi = PoiRouteInfo(
                            gpsKey.toLong(), newCenterLat, newCenterLng, poi.poiName, poi.fullAddress, newRadius, item.poiHash, poi.alias, poi.poiId,
                            poi.poiType, poi.poiTypeVersion, poi.poiEnable, poi.poiAddrType
                        )
                        if (updateLocations.containsKey(key)) {
                            updateLocations[key]?.add(newPoi)
                        } else {
                            updateLocations[key] = mutableListOf(newPoi)
                        }
                    }
                }
            }
        }
        //更新数据库和缓存
        updateLocations.forEach { (k, pois) ->
            poiCache[k] = pois
            pois.forEach { updatePoi ->
                poiCacheService?.updatePoiBatch(updatePoi.poiHash, updatePoi.latitude, updatePoi.longitude, updatePoi.radius)
            }
            GLog.d(TAG, LogFlag.DL) { "4.update poi radius,$pois" }
        }
    }

    /**
     * 存储poi_id和图片gps_key关联到poi_grid_route表
     *
     * @param id 图片_id
     * @param gpsKey 图片gps_key
     * @param poiRouteInfo poi信息
     */
    private fun createPoiGridInfo(id: Int, gpsKey: Double, poiRouteInfo: PoiRouteInfo) {
        val ids = poiCacheService?.getAllPoiIds(listOf(poiRouteInfo.poiHash.toString()))
        if (!ids.isNullOrEmpty()) {
            poiCacheService?.writePoiGridBatch(listOf(PoiGridInfo(gpsKey.toLong(), id.toLong(), ids)), curModelVersion)
        }
    }

    /**
     * poi扫描任务状态回调
     *
     */
    interface JobCallback {
        /**
         * 是否能继续执行任务
         *
         * @return 默认可以执行
         */
        fun isJobContinue(): Boolean {
            return true
        }

        /**
         * 任务结果回调
         *
         */
        fun onJobEnd()
    }

    /**
     * 加载poi配置文件
     */
    interface PoiFileCallback {

        /**
         * 获取poi文件状态
         */
        fun getPoiFileStatus(): PoiFileStatus

        /**
         * 加载poi文件
         */
        fun loadPoiFile(): List<File>


        /**
         * 此版本poi文件已成功使用
         */
        fun onPoiFileLoaded()
    }

    data class PoiFileStatus(
        /**
         * 当前文件版本
         */
        val currentVersion: Int,
        /**
         * 已经使用过的文件的版本
         */
        val scannedVersion: Int
    )

    companion object {
        private const val TAG = "PoiUpdateJob"
        private const val INVALID_POI_ID = -1
        private const val POI_SCAN_DURATION = 50L
        private const val SCAN_BATCH = 50
    }
}