/*********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: StudyImageInfo.kt
 ** Description: 学习扫描的数据信息
 ** Version: 1.0
 ** Date: 2025/05/09
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>               2025/05/09  1.0         create
 *********************************************************************************/
package com.oplus.gallery.business_lib.model.data.study.utils

import android.database.Cursor
import com.oplus.gallery.business_lib.bean.BaseImageInfo
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.StudyColumns
import com.oplus.gallery.foundation.util.text.TextUtil

/**
 * 学习扫描的数据信息
 */
class StudyImageInfo : BaseImageInfo() {

    /**
     * 是否扫描为学习
     */
    var isStudy = false

    /**
     * 是否手动移除
     */
    var isManual = false

    /**
     * 扫描时间
     */
    var scanDate = 0L

    /**
     * 扫描所用的模型版本号
     */
    var version: String = TextUtil.EMPTY_STRING

    companion object {

        /**
         * 将cursor数据转为StudyImageInfo
         */
        fun buildImageInfoList(cursor: Cursor): List<StudyImageInfo> {
            val list = ArrayList<StudyImageInfo>()
            val idIndex = cursor.getColumnIndex(LocalColumns._ID)
            val mediaIdIndex = cursor.getColumnIndex(LocalColumns.MEDIA_ID)
            val pathIdx = cursor.getColumnIndex(StudyColumns.DATA)
            val mediaTypeIdx = cursor.getColumnIndex(StudyColumns.MEDIA_TYPE)
            val isStudyIdx = cursor.getColumnIndex(StudyColumns.IS_STUDY)
            val invalidIdx = cursor.getColumnIndex(StudyColumns.INVALID)
            val isRecycleIdx = cursor.getColumnIndex(StudyColumns.IS_RECYCLED)
            val isManualIdx = cursor.getColumnIndex(StudyColumns.IS_MANUAL)
            val scanDateIdx = cursor.getColumnIndex(StudyColumns.SCAN_DATE)
            val versionIdx = cursor.getColumnIndex(StudyColumns.MODEL_VERSION)
            while (cursor.moveToNext()) {
                StudyImageInfo().apply {
                    mGalleryId = cursor.getInt(idIndex).toLong()
                    mMediaId = cursor.getInt(mediaIdIndex).toLong()
                    mFilePath = cursor.getString(pathIdx)
                    mMediaType = cursor.getInt(mediaTypeIdx)
                    isStudy = (cursor.getInt(isStudyIdx) == 0).not()
                    mInvalid = (cursor.getInt(invalidIdx) == 0).not()
                    mIsRecycled = cursor.getInt(isRecycleIdx) == 1
                    isManual = cursor.getInt(isManualIdx) == 1
                    scanDate = cursor.getLong(scanDateIdx)
                    version = cursor.getString(versionIdx)
                    list.add(this)
                }
            }
            return list
        }
    }
}