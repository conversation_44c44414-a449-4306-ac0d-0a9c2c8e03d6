/************************************************************
 ** Copyright 2010-2020 OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_RDIT
 ** File: -HttpClientHelper.java
 ** Description: Visited http/https network helper
 ** Version: 1.0
 ** Date: 2017/06/29
 ** Author: <EMAIL>
 **
 **---------------------Revision History:---------------------
 **  <author>        <data>     <version>   <desc>
 **  xiongwanjiang   2017/06/29   1.0       build this module
 ************************************************************/
package com.oplus.gallery.business_lib.model.data.memories.userprofile.protocol;

import android.net.TrafficStats;
import android.os.Process;

import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Map;
import java.util.Set;

public class HttpClientHelper {
    public static final int DEFAULT_HTTP_CONNECT_TIMEOUT = TimeUtils.TIME_30_SEC_IN_MS; //30s
    public static final int DEFAULT_HTTP_READ_TIMEOUT = TimeUtils.TIME_30_SEC_IN_MS; //30s
    private static final String TAG = "HttpClientHelper";
    private static final HttpClientHelper sHttpClient = new HttpClientHelper();
    private static final int BUFFSIZE = 2048;
    private static final String POST = "POST";
    private static final String GET = "GET";

    public static HttpClientHelper getInstance() {
        return sHttpClient;
    }

    public byte[] post(Map<String, String> headers, String httpUrl, byte[] body) {
        return execute(headers, POST, httpUrl, body);
    }

    public byte[] get(Map<String, String> headers, String httpUrl) {
        return execute(headers, GET, httpUrl, null);
    }

    private byte[] execute(Map<String, String> headers, String method, String httpUrl, byte[] body) {
        long beforeExecBytes = TrafficStats.getUidTxBytes(Process.myUid())
                + TrafficStats.getUidRxBytes(Process.myUid());
        URL url = null;
        try {
            url = new URL(httpUrl);
        } catch (MalformedURLException e) {
            GLog.d(TAG, "execute, error: " + e);
        }
        if (url == null) {
            return null;
        }

        byte[] response = null;
        HttpURLConnection connection = null;
        OutputStream outputStream = null;
        InputStream inputStream = null;
        try {
            connection = (HttpURLConnection) url.openConnection();
            if (connection != null) {
                //add request header.
                String key = null;
                String value = null;
                Set<Map.Entry<String, String>> entrySet = headers.entrySet();
                for (Map.Entry<String, String> entry : entrySet) {
                    key = entry.getKey();
                    value = entry.getValue();
                    connection.setRequestProperty(key, value);
                }
                connection.setRequestProperty("Content-Type", "application/x-protobuf");
                //set timeout.
                connection.setConnectTimeout(DEFAULT_HTTP_CONNECT_TIMEOUT);
                connection.setReadTimeout(DEFAULT_HTTP_READ_TIMEOUT);
                //set request method
                connection.setRequestMethod(method);
                if (body != null) {
                    outputStream = connection.getOutputStream();
                    outputStream.write(body);
                }
                connection.connect();
                GLog.d(TAG, "execute, getResponseCode=" + connection.getResponseCode() + " " + connection.getResponseMessage());
                if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                    inputStream = connection.getInputStream();
                    response = input2byte(inputStream, connection.getContentLength());
                    if ((response != null) && (response.length <= 0)) {
                        response = null;
                    }
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, "execute, connection error. e = " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            IOUtils.closeQuietly(outputStream);
            IOUtils.closeQuietly(inputStream);
        }
        long afterExecBytes = TrafficStats.getUidTxBytes(Process.myUid()) + TrafficStats.getUidRxBytes(Process.myUid());
        GLog.i(TAG, "execute, use traffic = " + (afterExecBytes - beforeExecBytes) + " Bytes");
        return response;
    }

    private static byte[] input2byte(InputStream inStream, int bufferSize) throws IOException {
        if (bufferSize > 0) {
            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            byte[] buff = new byte[Math.min(bufferSize, BUFFSIZE)];
            int rc = 0;
            while ((rc = inStream.read(buff, 0, buff.length)) > 0) {
                swapStream.write(buff, 0, rc);
            }
            return swapStream.toByteArray();
        }
        return null;
    }
}
