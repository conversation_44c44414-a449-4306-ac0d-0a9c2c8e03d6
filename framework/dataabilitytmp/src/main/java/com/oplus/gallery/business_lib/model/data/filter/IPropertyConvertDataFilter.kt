/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IPropertyConvertDataFilter.kt
 ** Description : 转换单个属性的筛选条件
 ** Version     : 1.0
 ** Date        : 2024/7/24 15:35
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2024/7/24      1.0     OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.business_lib.model.data.filter

import com.oplus.gallery.foundation.database.util.SQLGrammar

/**
 * 转换单个属性的筛选条件
 */
interface IPropertyConvertDataFilter {
    /**
     * 转换为sql的where string
     *
     * @param valueSet 数据集合
     * @return 用于sql的where string
     */
    fun convert(valueSet: Set<Int>): String

    companion object {
        fun tryAppendOr(index: Int, builder: StringBuilder) {
            if (index > 0) {
                builder.append(SQLGrammar.OR)
            }
        }
    }
}
