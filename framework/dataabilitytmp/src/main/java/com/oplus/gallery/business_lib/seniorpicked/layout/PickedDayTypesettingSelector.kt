/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PickedDayTypesettingSelector
 ** Description: 排版选择器
 **
 ** Version: 1.0
 ** Date: 2021/11/15
 ** Author: Yegua<PERSON><EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2021/11/15  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.seniorpicked.layout

import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.COLUMN_FOUR
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.COLUMN_SIX
import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant.COLUMN_THREE
import com.oplus.gallery.business_lib.seniorpicked.layout.data.PickedDayTypesettingConfig
import com.oplus.gallery.business_lib.seniorpicked.layout.gridfactory.FourGridFactory
import com.oplus.gallery.business_lib.seniorpicked.layout.gridfactory.SixGridFactory
import com.oplus.gallery.business_lib.seniorpicked.layout.gridfactory.ThreeGridFactory
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

class PickedDayTypesettingSelector private constructor() {

    companion object {
        private const val TAG = "PickedDayTypesettingSelector"
        val instance: PickedDayTypesettingSelector by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            PickedDayTypesettingSelector()
        }
    }

    /*
     * json文件编码方案
     *
     * 每个json文件是个二维数据
     * 外层表示模板集合
     * 里层表示不同种类的格子集合
     * 默认第一个模板是循环模板，模板内格子数为M
     * 剩下的模板是尾部模板
     * 一个完整布局是：一个大图 + N 个循环模板 + 1一个尾部模板
     * 设素材总数S，有 余数Y = (S - 1) % M,
     * 则按Y可取出索引为Y的尾部模板
     * 例如3格模板，是由12个数组组成的数组，json[0]即是循环模板，根据总数S求出Y后，json[Y]就是尾部模板
     * 假设 S = 15，有M = 12，则N = 1，Y = 2
     *
     * 各个格子样式如下：
     *  小图1_1    中图    大图     小图2_1
     *   _        _ _    _ _ _     _ _
     *  |_|      |   |  |     |   |_ _|
     *           |_ _|  |     |
     *                  |_ _ _|
     *
     * S = 15 的期望结果如下：
     *   _ _ _      _ _
     *  |     |      |
     *  |     |    大图位
     *  |_ _ _|     _|_
     *  |_|_|_|      |
     *  |   |_|      |
     *  |_ _|_|      |
     *  |_|_|_|   循环模板 json[0]
     *  |_|   |      |
     *  |_|_ _|     _|_
     *  |_ _|_|    尾部模板 json[2]
     *
     * 每个模板都由类似下面的的item组成
     * {"type": 格子类型,"x": "X轴格子编码","y": "Y轴格子编码"}
     *
     * type： 格子类型，参数见PickedDayConstant的 xxx_GRID_xxx
     * 例如 0 即是 SMALL_GRID_1_1 表示小图1_1
     * 1 即是 MEDIUM_GRID 表示中图
     *
     * "x": X轴格子编码，表示当前格子距离当前模板左上角横向间隔了多少个不同种类的格子
     * "y"：Y轴格子编码，表示当前格子距离当前模板左上角纵向间隔了多少个不同种类的格子
     *
     * 编码样式：A-B-C-D-E... 由自然数加"-"连接而成
     * 解释：第N个数字W，表示有W个N类型的格子
     * 例如y的值是"A-B"， 就说明有 A个SMALL_GRID_1_1 + B个MEDIUM_GRID
     * 表示当前格子距离模板左上角纵向间隔了A个小图1_1 和B个中图
     *
     * 对照json[0]的内容以及上面的期望布局中的循环模板进行理解
     *
     * {"type": 0,"x": "0","y": "0"},   json[0][0],小图1_1，左边没有格子，x为0，上边没有格子 y为0
     * {"type": 0,"x": "1","y": "0"},   json[0][1]，小图1_1，左边有一个小图，x为1，上边没有格子 y为0
     * {"type": 0,"x": "2","y": "0"},
     * {"type": 1,"x": "0","y": "1"},   json[0][3]，中图，左边没有格子，x为0，上边有一个小图1_1，y为1
     * {"type": 0,"x": "0-1","y": "1"}, json[0][4]，小图1_1，左边有一个中图，但是无小图，x为"0-1"，上边有一个小图1_1，y为1
     * {"type": 0,"x": "0-1","y": "2"},
     * {"type": 0,"x": "0","y": "1-1"},
     * {"type": 0,"x": "1","y": "1-1"},
     * {"type": 0,"x": "2","y": "3"},
     * {"type": 0,"x": "0","y": "2-1"},
     * {"type": 0,"x": "0","y": "3-1"}, json[0][10]，小图1_1，左边没有格子，x为0，上边共有3个小图1_1，1个中图，则 y 为"3-1"
     * {"type": 1,"x": "1","y": "4"}
     */
    private enum class TypesettingConfig(val config: String) {
        THREE_GRID("picked_day_three_grid_typesetting.json"),
        FOUR_GRID("picked_day_four_grid_typesetting.json"),
        SIX_GRID("picked_day_six_grid_typesetting.json")
    }

    /**
     * View宽度W <= 480dp，按3格显示
     * 否则，判断高宽比：
     * H:W >= 3:4 按4格显示
     * H:W < 3:4 按6格显示
     */
    private enum class Typesetting(val column: Int) {
        THREE_GRID(COLUMN_THREE),
        FOUR_GRID(COLUMN_FOUR),
        SIX_GRID(COLUMN_SIX)
    }

    private val pickedDayTypesettingMap: HashMap<Typesetting, PickedDayTypesetting?> = HashMap()

    fun load() {
        GLog.d(TAG, LogFlag.DL, "load.")
        loadTypeSetting(Typesetting.THREE_GRID)
        loadTypeSetting(Typesetting.FOUR_GRID)
        loadTypeSetting(Typesetting.SIX_GRID)
    }

    private fun loadTypeSetting(type: Typesetting): PickedDayTypesetting {
        pickedDayTypesettingMap[type]?.let {
            return it
        }
        val typesetting = when (type) {
            Typesetting.THREE_GRID -> {
                PickedDayTypesetting(
                    Typesetting.THREE_GRID.column,
                    PickedDayTemplater(TypesettingConfig.THREE_GRID.config, ThreeGridFactory())
                )
            }
            Typesetting.FOUR_GRID -> {
                PickedDayTypesetting(
                    Typesetting.FOUR_GRID.column,
                    PickedDayTemplater(TypesettingConfig.FOUR_GRID.config, FourGridFactory())
                )
            }
            Typesetting.SIX_GRID -> {
                PickedDayTypesetting(
                    Typesetting.SIX_GRID.column,
                    PickedDayTemplater(TypesettingConfig.SIX_GRID.config, SixGridFactory())
                )
            }
        }.apply { initialize() }
        pickedDayTypesettingMap[type] = typesetting
        return typesetting
    }

    private fun columnToTypesetting(column: Int): Typesetting? {
        return when (column) {
            Typesetting.THREE_GRID.column -> Typesetting.THREE_GRID
            Typesetting.FOUR_GRID.column -> Typesetting.FOUR_GRID
            Typesetting.SIX_GRID.column -> Typesetting.SIX_GRID
            else -> null
        }
    }

    fun select(column: Int, aspectRatio: Float): PickedDayTypesettingConfig? {
        if (pickedDayTypesettingMap.isEmpty()) load()

        if (aspectRatio.compareTo(0.0f) != 0) {
            columnToTypesetting(column)?.let {
                val typesetting = pickedDayTypesettingMap[it]
                return typesetting?.makeConfig(aspectRatio)
            }
        }
        return null
    }

    fun clear() {
        pickedDayTypesettingMap.clear()
    }
}