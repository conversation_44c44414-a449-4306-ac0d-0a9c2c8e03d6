/*********************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: OpVideoType.kt
 * Description: Video type definition for video taken by OnePlus camera.
 *
 * Version: 1.0
 * Date: 2021/08/30
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>		   2021/08/30		1.0		Add this file
 *********************************************************************************/

package com.oplus.gallery.business_lib.model.data.base.utils

import android.media.MediaMetadataRetriever
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.frameextractor_lib.IMediaMetadataRetriever

enum class OpVideoType(var exifTag: String? = null) {
    UNKNOWN,
    TIME_LAPSE("${OplusExifTag.EXIF_TAG_PREFIX}${OplusExifTag.EXIF_TAG_FAST_VIDEO}"),
    SLOW_MOTION;

    companion object {
        private const val TAG = "OpVideoType"
        private const val FAST_VIDEO_MIN_PLAY_SPEED = 3f
        private const val SLOW_MOTION_VIDEO_MAX_PLAY_SPEED = 1f / 3f

        @JvmStatic
        fun from(retriever: IMediaMetadataRetriever): OpVideoType {
            val captureFrameRateStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE)
            val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            val frameCountStr = retriever.extractMetadata(
                MediaMetadataRetriever.METADATA_KEY_VIDEO_FRAME_COUNT
            )
            GLog.d(TAG, "from, captureFrameRate: $captureFrameRateStr, duration: $durationStr, frameCount: $frameCountStr")
            try {
                val captureFrameRate = captureFrameRateStr?.toFloat() ?: return UNKNOWN
                val frameCount = frameCountStr?.toLong() ?: return UNKNOWN
                val duration = durationStr?.toLong() ?: return UNKNOWN
                val framePerSec = frameCount / (duration.toFloat() / 1000f)
                val playbackSpeed = framePerSec / captureFrameRate
                return when {
                    playbackSpeed > FAST_VIDEO_MIN_PLAY_SPEED -> TIME_LAPSE
                    playbackSpeed < SLOW_MOTION_VIDEO_MAX_PLAY_SPEED -> {
                        SLOW_MOTION.apply {
                            exifTag = composeSlowMotionVideoTitle(captureFrameRate)
                        }
                    }
                    else -> UNKNOWN
                }
            } catch (e: NumberFormatException) {
                GLog.w(TAG, "from, e: $e")
            }
            return UNKNOWN
        }

        private fun composeSlowMotionVideoTitle(captureFrame: Float): String {
            val prefix = OplusExifTag.EXIF_TAG_PREFIX
            val fps = when {
                120f.compareTo(captureFrame) == 0 -> VideoTypeUtils.SUPPORT_HFR_SLOW_MOTION_FPS_120
                240f.compareTo(captureFrame) == 0 -> VideoTypeUtils.SUPPORT_HFR_SLOW_MOTION_FPS_240
                480f.compareTo(captureFrame) == 0 -> VideoTypeUtils.SUPPORT_HFR_SLOW_MOTION_FPS_480
                else -> VideoTypeUtils.SUPPORT_HFR_SLOW_MOTION_FPS_240
            }
            val motion = "0,0,0,0"
            return "$prefix$fps:$motion"
        }
    }
}