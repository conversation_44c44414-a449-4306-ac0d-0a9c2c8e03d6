/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - LabelEntry.kt
 * Description: LabelEntry
 * Version: 1.0
 * Date: 2023/8/7
 * Author: W9009912
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * W9009912      2023/8/7      1.0              create file
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.data.base.entry

/**
 * 标签信息
 */
class LabelEntry {
    @JvmField
    var sceneId = 0

    @JvmField
    var labelKey: String? = null

    @JvmField
    var labelName: String? = null

    @JvmField
    var coverMediaEntry: CoverMediaEntry? = null

    @JvmField
    var count = 0

    override fun toString(): String {
        return ("LabelEntry{"
                + "mSceneId=" + sceneId
                + ", mLabelKey='" + labelKey + '\''
                + ", mLabelName='" + labelName + '\''
                + ", mCoverMediaEntry=" + coverMediaEntry
                + ", mCount=" + count
                + '}')
    }
}