/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaAlbum.java
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/4
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** OPLUS Java File Skip Rule:MethodLength
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2020/8/4      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 ** biao.chen@Apps.Gallery3D   2020/10/15    1.1              add time nodes
 *************************************************************************************************/
package com.oplus.gallery.business_lib.model.data.base.set;

import static com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.END_INDEX_MONTH;
import static com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.END_INDEX_YEAR;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_CREATE_MEMORIES;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_DELETE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_DELETE_ALBUM;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_INFO;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_RENAME_ALBUM;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_SHARE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.KEY_REQUIRE_PARAM_VALID;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.KEY_REQUIRE_SPECIFIED_ATTRS;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.RESULT_KEY_CREATE_MEMORIES_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.RESULT_KEY_IMAGE_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.RESULT_KEY_VIDEO_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.SPECIFIED_ATTR_CREATE_MEMORIES_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.SPECIFIED_ATTR_IMAGE_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedAttributes.SPECIFIED_ATTR_VIDEO_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.KEY_CREATE_MEMORIES_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.KEY_IMAGE_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.KEY_REQUIRE_SPECIFIED_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.KEY_VIDEO_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.SPECIFIED_COUNT_CREATE_MEMORIES;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.SPECIFIED_COUNT_IMAGE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.SPECIFIED_COUNT_VIDEO;
import static com.oplus.gallery.business_lib.util.TimelineUtils.TIMELINE_TAG;
import static com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.getROOT_ALBUM_ID;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.MAX;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.MIN;
import static com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao.TableType.LOCAL_MEDIA;
import static com.oplus.gallery.foundation.util.math.MathUtil.max;
import static com.oplus.gallery.foundation.util.math.MathUtil.min;
import static com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING;
import static kotlinx.coroutines.BuildersKt.launch;

import android.content.Context;
import android.database.Cursor;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.oplus.gallery.business_lib.model.config.allowlist.AlbumAllowListConfig;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode;
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNodeType;
import com.oplus.gallery.business_lib.model.data.common.dbaccess.convert.MediaItemPathListConvert;
import com.oplus.gallery.business_lib.model.data.filter.ConvertDataFilterManager;
import com.oplus.gallery.business_lib.model.data.filter.DataFilterStorage;
import com.oplus.gallery.business_lib.model.data.filter.DataFilterType;
import com.oplus.gallery.business_lib.model.data.filter.IDataFilter;
import com.oplus.gallery.business_lib.util.TimelineUtils;
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns;
import com.oplus.gallery.foundation.database.util.ConstantUtils;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kotlin.jvm.Volatile;
import kotlin.ranges.IntRange;
import kotlin.ranges.LongRange;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

public abstract class MediaAlbum extends MediaSet implements IDataFilter {
    /** 根目录的albumId就是0,不能以0作为无效id */
    public static final int INVALID_ALBUM_ID = -1;
    private static final String TAG = "MediaAlbum";
    private static final int UN_SUPPORT_CACHE_COUNT = -2;

    protected static final int NEED_REVERT_COUNT_LIMIT = 2000;

    /**
     *  year/month/day time node list
     */
    protected final Map<TimeNodeType, List<TimeNode>> mTimeNodesMap = new HashMap<>();

    protected boolean mCachedCountLoading = false;
    protected int mCachedCount = INVALID_CACHE_COUNT;
    protected int mImageCount = INVALID_CACHE_COUNT;
    protected int mVideoCount = INVALID_CACHE_COUNT;
    protected boolean mIsOrderChanged = false;
    @Volatile
    protected int mTimeNodeItemCount = 0;

    /**
     * 用于存放图集列表的唯一标识
     */
    protected int mAlbumId = INVALID_ALBUM_ID;

    /**
     * constructor
     */
    public MediaAlbum(Context application) {
        super(nextVersionNumber());
        mApplication = application;
    }

    @Override
    public final boolean isMediaAlbum() {
        return true;
    }

    @Override
    public boolean isSelfAlbum() {
        return false;
    }

    @Override
    public long getSupportedOperations() {
        long flag = OPERATION_SUPPORT_DELETE | OPERATION_SUPPORT_SHARE | OPERATION_SUPPORT_INFO;
        /*
        虚拟图集都不支持在图集列表界面直接删除图集操作(超级文本除外)，当前交互要求超级文本支持删除
        虚拟图集不支持图集重命名
        */
        if (!isVirtualAlbum()) {
            flag |= OPERATION_SUPPORT_DELETE_ALBUM;
            flag |= OPERATION_SUPPORT_RENAME_ALBUM;
        }
        if (isSelfAlbum()) {
            flag |= OPERATION_SUPPORT_CREATE_MEMORIES;
        }
        return flag;
    }

    protected void onClearCache() {
        synchronized (mTimeNodesMap) {
            mTimeNodesMap.clear();
            mTimeNodeItemCount = 0;
        }
    }

    @Override
    public synchronized long reload() {
        boolean isDirty = isDirty();
        if (mIsOrderChanged || isDirty) {
            mIsOrderChanged = false;
            onClearCache();
        }
        if (isDirty) {
            updateCount(INVALID_COUNT, INVALID_COUNT, INVALID_COUNT);
            // 大图设置自定义封面场景下，reload时，不清除缓存，防止在图集详情页获取封面时mCoverItems被clear,从而得不到正确的封面缓存数据
            if (!isCustomCover()) {
                mCoverItems.clear();
            }
            mIsPathsDirty = true;
            onReload();
            mDataVersion = nextVersionNumber();
        }
        return mDataVersion;
    }

    public boolean isCustomCover() {
        return false;
    }

    protected void onReload() {
    }

    public void loadCountAsyncIfNeed() {
        if (mCachedCountLoading || (mCachedCount > INVALID_COUNT)) {
            return;
        }
        //不需要加锁,仅用于过滤重复任务,即使多次loading也无碍
        long time = System.currentTimeMillis();
        mCachedCountLoading = true;
        launch(AppScope.INSTANCE, Dispatchers.getIO(), CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
            mCachedCountLoading = false;
            int lastCount = mCachedCount;
            int count = loadCount();
            if (lastCount != count) {
                notifyContentChanged();
            }
            GLog.d(getTag(), null,
                    () -> "loadCountAsyncIfNeed end cost=" + GLog.getTime(time) + ",v=" + mDataVersion + "," + lastCount + "->" + mCachedCount
                            + ",isRegister=" + isContentListenerEmpty() + "," + mPath);
            return null;
        });
    }

    @Override
    public final int getCount() {
        return getSubMediaItemCount(false);
    }

    @Override
    protected int getSubMediaItemCount(boolean requireSpecifiedCount) {
        // 使用querySubMediaItemCount查询子项的图集不支持查询具体的视频数量和图片数量
        boolean supportQuerySpecifiedCount = (mVideoCount != UN_SUPPORT_CACHE_COUNT) && (mImageCount != UN_SUPPORT_CACHE_COUNT);
        int lastCacheCount = mCachedCount;
        // 是否需要查询具体的视频数量和图片数量
        boolean shouldQuerySpecifiedCount = requireSpecifiedCount && supportQuerySpecifiedCount
                && ((mImageCount == INVALID_CACHE_COUNT) || (mVideoCount == INVALID_CACHE_COUNT) || (lastCacheCount != (mImageCount + mVideoCount)));
        // 是否需要进行数据库查询
        boolean shouldQuery = (lastCacheCount == INVALID_CACHE_COUNT) || shouldQuerySpecifiedCount;
        int count = mCachedCount;
        if (shouldQuery) {
            count = loadCount();
            GLog.d(getTag(), LogFlag.DL, "getSubMediaItemCount shouldQuery:" + true
                    + ", mCachedCount:" + mCachedCount
                    + ", count:" + lastCacheCount + " -> " + count
                    + ", isSpecified =" + shouldQuerySpecifiedCount
                    + ", mPath = " + mPath
            );
        }
        return Math.max(count, 0);
    }

    /**
     * 加载子项的数量，该方法会更新mCachedCount，mImageCount，mVideoCount的值。
     * <p>
     * 为什么updateCount 还需要有返回值，因为mCachedCount可能被更改，导致执行完loadCount之后，
     * mCachedCount的值其实不是真实数据，比如loadCount之后被reload复写为-1，此时getSubMediaItemCount
     * 返回值会是错误的，所以需要返回真实的count值。
     * @return 真实的查询后count值
     */
    private int loadCount() {
        try {
            GTrace.traceBegin("queryCount " + mPath);
            Integer count = querySubMediaItemCount();
            if (count != null) {
                // 在不支持查询具体的视频数量和图片数量时将视频数量和图片数量赋值为 UN_SUPPORT_CACHE_COUNT，表示不支持
                updateCount(count, UN_SUPPORT_CACHE_COUNT, UN_SUPPORT_CACHE_COUNT);
                return count;
            } else {
                int[] typeCount = querySubMediaItemTypeCount();
                if (typeCount != null) {
                    if ((typeCount[1] + typeCount[2]) != typeCount[0]) {
                        GLog.e(TAG, "loadCount count error:" + Arrays.toString(typeCount)
                                + ", path:" + mPath);
                    }
                    updateCount(typeCount[0], typeCount[1], typeCount[2]);
                    return typeCount[0];
                }
            }
            GLog.w(TAG, LogFlag.DL, "loadCount not override query function or query failed");
            // 查询失败，返回0，需检查配置是否正确，或者sql查询异常
            return 0;
        } finally {
            GTrace.traceEnd();
        }
    }

    @Override
    public Bundle getSpecifiedCount(Bundle inBundle) {
        Bundle bundle = new Bundle();
        if (inBundle == null) {
            GLog.w(TAG, "getSpecifiedCount, inBundle is null");
            return bundle;
        }

        int requireSpecifiedCount = inBundle.getInt(KEY_REQUIRE_SPECIFIED_COUNT, 0);
        if (requireSpecifiedCount == 0) {
            GLog.w(TAG, "getSpecifiedCount, specifiedCount is 0");
            return bundle;
        }

        getSubMediaItemCount(true);

        if ((requireSpecifiedCount & SPECIFIED_COUNT_IMAGE) != 0) {
            bundle.putInt(KEY_IMAGE_COUNT, mImageCount);
        }
        if ((requireSpecifiedCount & SPECIFIED_COUNT_VIDEO) != 0) {
            bundle.putInt(KEY_VIDEO_COUNT, mVideoCount);
        }
        if ((requireSpecifiedCount & SPECIFIED_COUNT_CREATE_MEMORIES) != 0) {
            bundle.putInt(KEY_CREATE_MEMORIES_COUNT, getMemoriesItemCount());
        }
        return bundle;
    }

    @Override
    public Bundle getSpecifiedAttributes(Bundle inBundle) {
        Bundle outBundle = super.getSpecifiedAttributes(inBundle);
        if (!outBundle.getBoolean(KEY_REQUIRE_PARAM_VALID)) {
            return outBundle;
        }
        int specifiedAttr = inBundle.getInt(KEY_REQUIRE_SPECIFIED_ATTRS, 0);
        if ((specifiedAttr & SPECIFIED_ATTR_IMAGE_COUNT) != 0) {
            outBundle.putInt(RESULT_KEY_IMAGE_COUNT, mImageCount);
        }
        if ((specifiedAttr & SPECIFIED_ATTR_VIDEO_COUNT) != 0) {
            outBundle.putInt(RESULT_KEY_VIDEO_COUNT, mVideoCount);
        }
        if ((specifiedAttr & SPECIFIED_ATTR_CREATE_MEMORIES_COUNT) != 0) {
            outBundle.putInt(RESULT_KEY_CREATE_MEMORIES_COUNT, getMemoriesItemCount());
        }
        return outBundle;
    }

    @Override
    public List<MediaItem> getCoverItems() {
        if (mCoverItems.isEmpty()) {
            try {
                GTrace.traceBegin("queryCoverItems " + mPath);
                mCoverItems.addAll(super.getCoverItems());
            } finally {
                GTrace.traceEnd();
            }
        }
        return new ArrayList<>(mCoverItems);
    }

    protected synchronized void updateCount(int cacheCount, int imageCount, int videoCount) {
        mCachedCount = cacheCount;
        mImageCount = imageCount;
        mVideoCount = videoCount;
    }

    /**
     * 返回图集缓存的数量
     */
    public synchronized int getCacheCount() {
        return mCachedCount;
    }

    public void updateCount(int cacheCount) {
        updateCount(cacheCount, INVALID_COUNT, INVALID_COUNT);
    }

    protected Integer querySubMediaItemCount() {
        return null;
    }

    protected @Nullable int[] querySubMediaItemTypeCount() {
        return null;
    }

    public void setCovers(List<MediaItem> mediaItems) {
        if ((mediaItems != null) && (mediaItems.size() > 0)) {
            mCoverItems.clear();
            mCoverItems.addAll(mediaItems);
        } else {
            GLog.w(TAG, LogFlag.DL, "[setCovers] mediaItems is null");
        }
    }

    public void clearCoverItems() {
        mCoverItems.clear();
    }

    protected String[] getProjection() {
        return (getInputEntry() == null) ? null : getInputEntry().getProjection();
    }

    @Override
    protected String getOrder() {
        return (getInputEntry() == null) ? mOrder : getInputEntry().getOrder();
    }

    @Override
    protected String getWhere() {
        return DatabaseUtils.appendSqlWhere((getInputEntry() == null) ? null : getInputEntry().getWhere(),
                ConvertDataFilterManager.convert(getAllFilters()));
    }

    public String getBucketPath() {
        return null;
    }

    @Override
    public List<Path> getPaths(int start, int count) {
        long startTime = System.currentTimeMillis();
        String where = getWhere();
        List<Path> result = new QueryReq.Builder<List<Path>>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(LOCAL_MEDIA)
                .setProjection(MediaItemPathListConvert.getPROJECTION())
                .setWhere(where)
                .setConvert(new MediaItemPathListConvert())
                .setLimit(start + ", " + count)
                .setOrderBy(getOrder())
                .build().exec();
        if (result == null) {
            result = new ArrayList<>();
            GLog.e(TAG, "getPaths, result is null! tableType:" + LOCAL_MEDIA + " where:" + where + "  start:" + start + "  count:" + count);
        } else {
            GLog.d(TAG, "getPaths, result size is " + result.size() + ", cost time:" + GLog.getTime(startTime));
        }
        return result;
    }

    /**
     * 图集排序
     */
    @Override
    public int getSortId() {
        return getIdByPath();
    }

    protected TimeNode getTimeNodeByDate(TimeNodeType type, int dateKey) {
        synchronized (mTimeNodesMap) {
            List<TimeNode> timeNodes = mTimeNodesMap.get(type);
            if (timeNodes == null) {
                return null;
            }
            for (TimeNode timeNode : timeNodes) {
                if (Integer.parseInt(timeNode.getId()) == dateKey) {
                    return timeNode;
                }
            }
            return null;
        }
    }

    protected TimeNode getTimeNodeByIndex(TimeNodeType type, int itemIndex) {
        synchronized (mTimeNodesMap) {
            List<TimeNode> timeNodes = getTimeNodeList(type);
            if ((timeNodes != null) && (!timeNodes.isEmpty())) {
                int searchNodeIndex = findTimeNodeByItemIndex(timeNodes, 0, timeNodes.size() - 1, itemIndex);
                if ((searchNodeIndex < timeNodes.size()) && (searchNodeIndex > TimelineUtils.INVALID_INDEX)) {
                    return timeNodes.get(searchNodeIndex);
                } else {
                    return timeNodes.get(timeNodes.size() - 1);
                }
            } else {
                return null;
            }
        }
    }

    private int findTimeNodeByItemIndex(List<TimeNode> timeNodes, int left, int right, int targetItemIndex) {
        if ((left > right)
                || (left < 0)
                || (left >= timeNodes.size())
                || (targetItemIndex < timeNodes.get(left).getItemRange().getFirst())
                || (right >= timeNodes.size())
                || (targetItemIndex > timeNodes.get(right).getItemRange().getLast())
        ) {
            return TimelineUtils.INVALID_INDEX;
        }
        if (left == right) {
            return left;
        }

        int mid = left + (right - left) * (targetItemIndex - timeNodes.get(left).getItemRange().getFirst())
                / (timeNodes.get(right).getItemRange().getLast() - timeNodes.get(left).getItemRange().getFirst());
        if (targetItemIndex > timeNodes.get(mid).getItemRange().getLast()) {
            return findTimeNodeByItemIndex(timeNodes, mid + 1, right, targetItemIndex);
        } else if (targetItemIndex < timeNodes.get(mid).getItemRange().getFirst()) {
            return findTimeNodeByItemIndex(timeNodes, left, mid - 1, targetItemIndex);
        } else {
            return mid;
        }
    }

    /**
     * 时间轴模式
     */
    public List<TimeNode> getTimeNodeList(TimeNodeType type) {
        synchronized (mTimeNodesMap) {
            List<TimeNode> list = mTimeNodesMap.get(type);
            if ((list == null) || list.isEmpty()) {
                updateTimeNodes();
            } else if (getTimeNodeItemCount(list) != mTimeNodeItemCount) {
                GLog.e(TAG, "getTimeNode: last:" + mTimeNodeItemCount + ", current:" + getTimeNodeItemCount(list));
                mTimeNodesMap.clear();
                updateTimeNodes();
            }
            list = mTimeNodesMap.get(type);
            if (list != null) {
                mTimeNodeItemCount = getTimeNodeItemCount(list);
            }
            return list;
        }
    }

    private int getTimeNodeItemCount(List<TimeNode> timeNodes) {
        if (timeNodes.size() > 0) {
            return timeNodes.get(timeNodes.size() - 1).getItemRange().getLast();
        }
        return 0;
    }

    protected Cursor queryTimeNodes(TimeNodeType type) {
        String column = TimeNodeType.columnOfType(type);
        String[] projection = {column, ConstantUtils.COUNT_ALL, MIN + "(" + LocalColumns.DATE_TAKEN + ")", MAX + "(" + LocalColumns.DATE_TAKEN + ")"};
        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(projection)
                .setWhere(getWhere())
                .setOrderBy(getOrder())
                .setGroupBy(column)
                .setConvert(new CursorConvert())
                .build();
        return DataAccess.getAccess().query(req);
    }

    protected void updateTimeNodes() {
        Cursor cursor = null;
        try {
            long time = System.currentTimeMillis();
            cursor = queryTimeNodes(TimeNodeType.DAY);
            long queryTime = System.currentTimeMillis() - time;
            List<TimeNode> dayNodes = new ArrayList<>();
            List<TimeNode> monthNodes = new ArrayList<>();
            List<TimeNode> yearNodes = new ArrayList<>();
            if ((cursor != null) && (cursor.getCount() > 0)) {
                final int dateIndex = 0;
                final int countStarIndex = 1;
                final int minDateTakenIndex = 2;
                final int maxDateTakenIndex = 3;
                int index = 0;
                int count = 0;

                long startTime = 0;
                long endTime = 0;
                String date = null;
                String currentYear = EMPTY_STRING;
                List<TimeNode> nodesInCurrentMonth = new ArrayList<>();
                String currentMonth = EMPTY_STRING;
                List<TimeNode> nodesInCurrentYear = new ArrayList<>();
                while (cursor.moveToNext()) {
                    date = cursor.getString(dateIndex); // YEAR/MONTH/DAY
                    count = cursor.getInt(countStarIndex); // count(*)
                    if (TextUtils.isEmpty(date) || (count <= 0)) {
                        GLog.e(TIMELINE_TAG + TAG, "updateTimeNodes. error! date=" + date + ", count=" + count);
                        continue;
                    }

                    startTime = cursor.getLong(minDateTakenIndex); // min(datetaken)
                    endTime = cursor.getLong(maxDateTakenIndex); // max(datetaken)

                    TimeNode node = new TimeNode(
                            date,
                            dayNodes.size(),
                            TimeUtils.getDayStartTime(startTime),
                            new LongRange(startTime, endTime),
                            new IntRange(index, index + count - 1),
                            IntRange.Companion.getEMPTY(),
                            null, null, null);
                    dayNodes.add(node);

                    String year = date.substring(0, END_INDEX_YEAR);
                    if (!year.equals(currentYear)) {
                        if (!nodesInCurrentYear.isEmpty()) {
                            yearNodes.add(mergeTimeNodes(currentYear, yearNodes.size(), nodesInCurrentYear));
                        }
                        nodesInCurrentYear.clear();
                        currentYear = year;
                    }
                    nodesInCurrentYear.add(node);

                    String month = date.substring(0, END_INDEX_MONTH);
                    if (!month.equals(currentMonth)) {
                        if (!nodesInCurrentMonth.isEmpty()) {
                            monthNodes.add(mergeTimeNodes(currentMonth, monthNodes.size(), nodesInCurrentMonth));
                        }
                        currentMonth = month;
                        nodesInCurrentMonth.clear();
                    }
                    nodesInCurrentMonth.add(node);

                    index += count;
                }

                if (!nodesInCurrentYear.isEmpty()) {
                    yearNodes.add(mergeTimeNodes(currentYear, yearNodes.size(), nodesInCurrentYear));
                }

                if (!nodesInCurrentMonth.isEmpty()) {
                    monthNodes.add(mergeTimeNodes(currentMonth, monthNodes.size(), nodesInCurrentMonth));
                }

                if (GProperty.DEBUG) {
                    GLog.d(TIMELINE_TAG + TAG, "updateTimeNodes. "
                            + ", dayNodeSize=" + dayNodes.size()
                            + ", monthNodeSize=" + monthNodes.size()
                            + ", yearNodeSize=" + yearNodes.size()
                            + ", queryTime=" + queryTime
                            + ", totalTime=" + GLog.getTime(time));
                }

                synchronized (mTimeNodesMap) {
                    mTimeNodesMap.put(TimeNodeType.DAY, dayNodes);
                    mTimeNodesMap.put(TimeNodeType.MONTH, monthNodes);
                    mTimeNodesMap.put(TimeNodeType.YEAR, yearNodes);
                }
            }
        } catch (Exception e) {
            GLog.e(TIMELINE_TAG + TAG, "updateTimeNodes, error: ", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
    }

    private TimeNode mergeTimeNodes(String id, int index, List<TimeNode> timeNodes) {
        TimeNode first = timeNodes.get(0);
        TimeNode last = timeNodes.get(timeNodes.size() - 1);
        long timeStamp = min(first.getTimestamp(), last.getTimestamp());
        long start = min(first.getTimeRange().getFirst(), last.getTimeRange().getFirst());
        long end = max(first.getTimeRange().getLast(), last.getTimeRange().getLast());
        return new TimeNode(
                id,
                index,
                timeStamp,
                new LongRange(start, end),
                new IntRange(first.getItemRange().getFirst(), last.getItemRange().getLast()),
                IntRange.Companion.getEMPTY(),
                null, null, null
        );
    }

    public void setOrder(String order) {
        if (getInputEntry() != null) {
            getInputEntry().setOrder(order);
        } else {
            mOrder = order;
        }
    }

    public String getDefaultOrder(boolean positive) {
        return getOrder();
    }

    /**
     * 返回当前的排序方式是否为升序
     * @return true 升序 false 降序
     */
    public boolean isAscOrder() {
        String order = getOrder();
        int descStart = order.indexOf("DESC");
        int ascStart = order.indexOf("ASC");
        return ((ascStart > INDEX_NOT_FOUND) && ((descStart == INDEX_NOT_FOUND) || (ascStart < descStart)));
    }

    public int getAlbumId() {
        if (mAlbumId == INVALID_ALBUM_ID) {
            int albumId = getIdByPath();
            if (albumId != INVALID_ALBUM_ID) {
                setAlbumId(albumId);
            } else {
                GLog.d(TAG, "getAlbumId. albumId = 0");
            }
        }
        return mAlbumId;
    }

    public void setAlbumId(int albumId) {
        if (albumId == getROOT_ALBUM_ID()) {
            //补充根目录的log,之前根目录出问题,无法确定是哪个图集出问题
            GLog.d(getTag(), LogFlag.DL, "setAlbumId: MediaAlbum " + albumId + "," + getName());
        }
        this.mAlbumId = albumId;
    }

    private int getIdByPath() {
        String mergePath = mergePath();
        if (mergePath != null) {
            return FilePathUtils.getBucketIdWithParentPath(mergePath);
        }
        if (!TextUtils.isEmpty(getBucketPath())) {
            return FilePathUtils.getBucketIdWithParentPath(getBucketPath());
        } else if (isVirtualAlbum() && (mPath != null)) {
            return FilePathUtils.getBucketIdWithParentPath(mPath.toString());
        }
        return INVALID_ALBUM_ID;
    }

    /**
     * 获取白名单中的mergePath
     */
    public String mergePath() {
        List<Integer> bucketIds = getBucketIds();
        if (!bucketIds.isEmpty()) {
            return AlbumAllowListConfig.getInstance().getNeedMergeBucketIds().get(bucketIds.get(0));
        }
        return null;
    }

    @Override
    public void addFilter(DataFilterType.DataFilterProperty property, int value) {
        DataFilterStorage.INSTANCE.addFilter(getPath().toString(), property, value);
    }

    @Override
    public void removeFilter(DataFilterType.DataFilterProperty property, int value) {
        DataFilterStorage.INSTANCE.removeFilter(getPath().toString(), property, value);
    }

    @androidx.annotation.NonNull
    @Override
    public Map<DataFilterType.DataFilterProperty, Set<Integer>> getAllFilters() {
        return DataFilterStorage.INSTANCE.getAllFilters(getPath().toString());
    }

    @Override
    public void clearAllFilters() {
        DataFilterStorage.INSTANCE.clearAllFilters(getPath().toString());
    }
}