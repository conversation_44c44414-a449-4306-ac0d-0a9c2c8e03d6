/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BucketIdsCacheHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/12/7
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2020/12/7    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.data.base.buckets

import android.content.Context
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.framework.datatmp.R
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

object BucketIdsCacheHelper {

    @Volatile
    private var sRootBucketIds: MutableList<Int>? = null

    @Volatile
    private var sCameraFolderBucketIds: MutableList<Int>? = null

    @Volatile
    private var sScreenShotsBucketIds: MutableList<Int>? = null

    @Volatile
    private var sCardCaseBucketIds: MutableList<Int>? = null

    /**
     * Sdcard变更，刷新缓存
     */
    @JvmStatic
    fun notifySDCardChange() {
        sRootBucketIds = null
        sCameraFolderBucketIds = null
        sScreenShotsBucketIds = null
        sCardCaseBucketIds = null
    }

    /**
     * 根目录
     */
    @JvmStatic
    fun getRootBucketIds(): List<Int> {
        if (sRootBucketIds == null) {
            sRootBucketIds = mutableListOf<Int>().apply {
                add(OplusEnvironment.getInternalSdBucketId())
                add(OplusEnvironment.getExternalSdBucketId())
            }
        }
        return sRootBucketIds?.apply { ArrayList(this) } ?: emptyList()
    }

    @JvmStatic
    fun isRootBucketIds(bucketId: Int): Boolean {
        return getRootBucketIds().contains(bucketId)
    }

    /**
     * 相机
     */
    @JvmStatic
    fun getCameraBucketIds(): List<Int> {
        if (sCameraFolderBucketIds == null) {
            sCameraFolderBucketIds = getCameraBucketIds(ContextGetter.context)
        }
        return sCameraFolderBucketIds?.apply { ArrayList(this) } ?: emptyList()
    }

    @JvmStatic
    fun isCameraBucketIds(context: Context, bucketId: Int): Boolean {
        return getCameraBucketIds(context).contains(bucketId)
    }

    private fun getCameraBucketIds(context: Context): MutableList<Int> {
        return getBucketIds(R.array.model_camera_folders, context)
    }

    /**
     * 截屏、录屏
     */
    @JvmStatic
    fun getScreenShotsBucketIds(): List<Int> {
        if (sScreenShotsBucketIds == null) {
            sScreenShotsBucketIds = getScreenShotsIds(ContextGetter.context)
        }
        return sScreenShotsBucketIds?.apply { ArrayList(this) } ?: emptyList()
    }

    private fun getScreenShotsIds(context: Context): MutableList<Int>? {
        return getBucketIds(R.array.model_srceenshots_folders, context)
    }

    /**
     * 随身卡包和卡证档案合并为卡证档案
     */
    @JvmStatic
    fun getCardCaseBucketIds(): List<Int> {
        if (sCardCaseBucketIds == null) {
            sCardCaseBucketIds = getCardCaseBucketIds(ContextGetter.context)
        }
        return sCardCaseBucketIds?.apply { ArrayList(this) } ?: emptyList()
    }

    private fun getCardCaseBucketIds(context: Context): MutableList<Int> {
        return getBucketIds(R.array.model_card_folders, context)
    }

    private fun getBucketIds(arrayId: Int, context: Context): MutableList<Int> {
        val res = context.resources
        val strings = res.getStringArray(arrayId)
        val list = strings.toList()
        return getBucketIds(context, list)
    }

    private fun getBucketIds(context: Context, list: List<String>): MutableList<Int> {
        val result = mutableListOf<Int>()
        val rootPaths = OplusEnvironment.getAvailableRootPaths()
        for (folder in list) {
            for (root in rootPaths) {
                result.add(FilePathUtils.getBucketIdWithParentPath("$root/$folder"))
            }
        }
        return result
    }
}