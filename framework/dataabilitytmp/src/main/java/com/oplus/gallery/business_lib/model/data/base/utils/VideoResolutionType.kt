/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - VideoResolutionType.kt
 * Description:
 * Version: 1.0
 * Date: 2025/07/24
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * huca<PERSON><PERSON>@Apps.Gallery        2025/07/24      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.business_lib.model.data.base.utils

import android.util.Size
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.foundation.util.ext.minLen
import kotlin.math.max
import kotlin.math.min

/**
 * 视频分辨率归属类型
 */
enum class VideoResolutionType(val width: Int, val height: Int) {
    R_720P(1280, 720),
    R_1080P(1920, 1080),
    R_2K(2560, 1440),
    R_4K(3840, 2160),
    R_8K(7680, 4320);

    /**
     * 分辨率值
     */
    val resolution = width * height

    /**
     * 最短边长
     */
    val minLen: Int = min(width, height)

    /**
     * 最长边长
     */
    val maxLen: Int = max(width, height)

    companion object {
        /**
         * 获取满足分辨率的[VideoResolutionType]
         * @param size 分辨率
         * @return 分辨率类型
         */
        fun getType(size: Size): VideoResolutionType {
            return entries.find {
                (size.maxLen() <= it.maxLen) && (size.minLen() <= it.minLen)
            } ?: entries.last()
        }
    }
}