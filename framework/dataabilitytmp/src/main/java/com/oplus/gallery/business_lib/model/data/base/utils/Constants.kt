/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - Constants.java
 * Description:
 * Version: 1.0
 * Date: 2020/8/4
 * Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <PERSON>jian@Apps.Gallery3D      2020/8/4      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
***************************************************************************************************/
package com.oplus.gallery.business_lib.model.data.base.utils

import com.oplus.gallery.business_lib.model.data.base.utils.Constants.AIFunc.SHIFT_BLUR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.AIFunc.SHIFT_PASSERBY
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.AIFunc.SHIFT_REFLECTION
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.AIFunc.STATUS_RECOMMEND
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_EDIT_RENDERING
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_LOCAL_HDR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_PORTRAIT_BLUR
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_SUPPORT_GROUP_PHOTO
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_SUPPORT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.CameraMode.FLAG_WATERMARK
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.SHIFT_GOOGLE_PASS_SCAN
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.GooglePassScanConst.STATUS_SCANNED_CLICKED
import com.oplus.gallery.foundation.database.helper.Constants
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag

class Constants {
    object CameraMode {
        const val TAGFLAGS = OplusExifTag.EXIF_KEY_TAGFLAGS
        const val FLAG_UNKNOWN = 0L
        const val FLAG_FRONT_CAMERA = OplusExifTag.EXIF_TAG_FRONT_CAMERA
        const val FLAG_FACE_BEAUTY = OplusExifTag.EXIF_TAG_FACE_BEAUTY
        const val FLAG_PANORAMA = OplusExifTag.EXIF_TAG_PANORAMA
        const val FLAG_FAST_VIDEO = OplusExifTag.EXIF_TAG_FAST_VIDEO
        const val FLAG_BOKEH = OplusExifTag.EXIF_TAG_BOKEH
        const val FLAG_WITH_ORIENTATION = OplusExifTag.EXIF_TAG_WITH_ORIENTATION
        const val FLAG_ENHANCE_TEXT = OplusExifTag.EXIF_TAG_ENHANCE_TEXT
        const val FLAG_SUPER_HIGH_RESOLUTION = OplusExifTag.EXIF_TAG_SUPER_HIGH_RESOLUTION
        const val FLAG_AI_ID = OplusExifTag.EXIF_TAG_AI_ID_PHOTO
        const val FLAG_LOCAL_HDR = OplusExifTag.EXIF_TAG_LOCAL_HDR
        const val FLAG_DOLBY_VISION = OplusExifTag.EXIF_TAG_DOLBY_VISION
        const val FLAG_PORTRAIT_BLUR = OplusExifTag.EXIF_TAG_PORTRAIT_BLUR
        const val FLAG_WATERMARK = OplusExifTag.EXIF_TAG_WATER_MARK
        // 标记为哈苏机器拍摄的照片
        const val FLAG_SUPPORT_HASSEL_WATER_MARK = OplusExifTag.EXIF_TAG_SUPPORT_HASSEL_WATER_MARK
        // 标记为合影优化的图片
        const val FLAG_SUPPORT_GROUP_PHOTO = OplusExifTag.EXIF_TAG_GROUP_PHOTO
        // 标记为Olive的图片
        const val FLAG_SUPPORT_OLIVE = OplusExifTag.EXIF_TAG_OLIVE
        // 标记为AI 风光的图片
        const val FLAG_SUPPORT_AI_SCENERY = OplusExifTag.EXIF_TAG_AI_SCENERY
        const val FLAG_ULTRA_HDR = OplusExifTag.EXIF_TAG_ULTRA_HDR
        const val FLAG_OPLUS_UHDR = OplusExifTag.EXIF_TAG_OPLUS_UHDR

        /**
         * 标记为参数化项目的图片
         */
        const val FLAG_EDIT_RENDERING = OplusExifTag.EXIF_TAG_EDIT_RENDERING

        /**
         * 相册生成的quick图
         */
        const val FLAG_FAST_QUICK_IMAGE = OplusExifTag.EXIF_TAG_FAST_QUICK_IMAGE

        /**
         * 相机生成的无影连拍图
         */
        const val FLAG_FAST_CSHOT_IMAGE = OplusExifTag.EXIF_TAG_FAST_CSHOT_IMAGE

        /**
         * 相机错峰生成的quick图
         */
        const val FLAG_DEFER_JOB_QUICK_IMAGE = OplusExifTag.EXIF_TAG_DEFER_JOB_QUICK_IMAGE

        /**
         * 获取某个 tagFlag 值所包含的所有内容的描述
         * 比如 16777248，输出为 [旋转,参数化]
         */
        @JvmStatic
        fun getTagFlagDesc(inputTagFlags: Long?): String? {
            inputTagFlags ?: return null
            val map = mapOf(
                FLAG_FRONT_CAMERA to "前置",
                FLAG_FACE_BEAUTY to "美颜",
                FLAG_PANORAMA to "全景",
                FLAG_FAST_VIDEO to "延时摄影",
                FLAG_BOKEH to "BOK",
                FLAG_WITH_ORIENTATION to "旋转",
                FLAG_ENHANCE_TEXT to "超级文本",
                FLAG_SUPER_HIGH_RESOLUTION to "超清",
                FLAG_AI_ID to "证件照",
                FLAG_LOCAL_HDR to "LOCAL_HDR",
                FLAG_DOLBY_VISION to "杜比",
                FLAG_PORTRAIT_BLUR to "人像景深",
                FLAG_WATERMARK to "水印",
                FLAG_SUPPORT_HASSEL_WATER_MARK to "哈苏水印",
                FLAG_SUPPORT_GROUP_PHOTO to "合影",
                FLAG_SUPPORT_OLIVE to "olive",
                FLAG_ULTRA_HDR to "ULTRA_HDR",
                FLAG_OPLUS_UHDR to "OPLUS_ULTRA_HDR",
                FLAG_EDIT_RENDERING to "项目化",
                FLAG_FAST_QUICK_IMAGE to "相册生成的quick图",
                FLAG_FAST_CSHOT_IMAGE to "相机生成的无影连拍图",
                FLAG_DEFER_JOB_QUICK_IMAGE to "相机错峰生成的quick图"
            )
            val stringBuilder = StringBuilder()
            map.forEach { (tagFlag, desc) ->
                if (inputTagFlags and tagFlag == tagFlag) {
                    stringBuilder.append(desc).append(",")
                }
            }
            var result: String = stringBuilder.toString()
            if (result.isNotEmpty()) {
                result = result.substring(0, result.length - 1)
            }
            return "[$result]"
        }
    }

    /**
     * 复合状态
     */
    object MultiCameraMode {
        // 使用扩展信息的文件格式，这里统一声明，方便管理
        const val EXTENDED_FILE_FLAGS =
            FLAG_LOCAL_HDR or FLAG_PORTRAIT_BLUR or FLAG_WATERMARK or FLAG_SUPPORT_GROUP_PHOTO or FLAG_SUPPORT_OLIVE or FLAG_EDIT_RENDERING
    }

    object IMediaTypeSupport {
        const val MEDIA_TYPE_SUPPORT_UNKNOWN = 0
        const val MEDIA_TYPE_SUPPORT_IMAGE = 1
        const val MEDIA_TYPE_SUPPORT_VIDEO = 1 shl 1
        const val MEDIA_TYPE_SUPPORT_ALL = MEDIA_TYPE_SUPPORT_IMAGE or MEDIA_TYPE_SUPPORT_VIDEO
        const val MEDIA_TYPE_SUPPORT_VIDEO_CAMERA = 1 shl 2
        const val MEDIA_TYPE_SUPPORT_CAMERA = 1 shl 3
        // 支持地图图集
        const val MEDIA_TYPE_SUPPORT_MAP = 1 shl 4
        const val MEDIA_TYPE_SUPPORT_RECYCLE = 1 shl 5
        const val MEDIA_TYPE_SUPPORT_VIRTUAL = 1 shl 6
        const val MEDIA_TYPE_SUPPORT_SELF_ALBUM = 1 shl 7
        const val MEDIA_TYPE_SUPPORT_MEMORIES = 1 shl 8
        const val MEDIA_TYPE_SUPPORT_CARD_CASE = 1 shl 9
        // 桌面卡片
        const val MEDIA_TYPE_SUPPORT_WIDGET = 1 shl 10

        /**
         * 仅支持本地图片和视频
         */
        const val MEDIA_TYPE_SUPPORT_ONLY_LOCAL_FILE = 1 shl 11

        // 显示随身卡包空图集
        const val MEDIA_TYPE_SUPPORT_EMPTY_CARD_ALBUM = 1 shl 12

        // 移动到或复制到 16.0上由于设计未定,暂时先用之前的列表,选图列表需要显示相机图集
        const val MEDIA_TYPE_SUPPORT_ONLY_REAL_ALBUM = 1 shl 13
        // 选图图集tab:如新建图集-图集tab 支持所有非空图集:即不包含特殊图集（如地图图集、空的随身卡包、私密图集、清理建议、回收站）
        const val MEDIA_TYPE_SUPPORT_ALBUM_NOT_EMPTY_ALL = 1 shl 14

        const val MEDIA_TYPE_SUPPORT_IMAGE_STRING = "image"
        const val MEDIA_TYPE_SUPPORT_VIDEO_STRING = "video"
        const val MEDIA_TYPE_SUPPORT_ALL_STRING = "all"
    }

    object IMediaSupportOperations {
        // These are the bits returned from getSupportedOperations():
        const val OPERATION_SUPPORT_NONE = 0L
        const val OPERATION_SUPPORT_DELETE = 1L
        const val OPERATION_SUPPORT_ROTATE = 1L shl 1 // Reserved
        const val OPERATION_SUPPORT_SHARE = 1L shl 2
        const val OPERATION_SUPPORT_CROP = 1L shl 3 // Reserved
        const val OPERATION_SUPPORT_SHOW_ON_MAP = 1L shl 4 // Reserved
        const val OPERATION_SUPPORT_SETAS_WALLPAPER = 1L shl 5
        const val OPERATION_SUPPORT_FULL_IMAGE = 1L shl 6 // Reserved
        const val OPERATION_SUPPORT_PLAY = 1L shl 7 // Reserved
        const val OPERATION_SUPPORT_TRANSFORM_HEIF_TO_JPEG = 1L shl 8
        const val OPERATION_SUPPORT_EDIT = 1L shl 9
        const val OPERATION_SUPPORT_INFO = 1L shl 10
        const val OPERATION_SUPPORT_IMPORT = 1L shl 11
        const val OPERATION_SUPPORT_RENAME = 1L shl 12
        const val OPERATION_SUPPORT_FAVORITES = 1L shl 13
        const val OPERATION_SUPPORT_SETAS_CONTACT = 1L shl 14
        const val OPERATION_SUPPORT_SAFE_BOX = 1L shl 15
        const val OPERATION_SUPPORT_MORE = 1L shl 16
        const val OPERATION_SUPPORT_SLIDESHOW = 1L shl 17
        const val OPERATION_SUPPORT_FREE_FACE = 1L shl 18
        const val OPERATION_SUPPORT_SAVE = 1L shl 19
        const val OPERATION_SUPPORT_JIGSAW = 1L shl 20
        const val OPERATION_SUPPORT_MOVE = 1L shl 21
        const val OPERATION_SUPPORT_APPEND = 1L shl 22
        const val OPERATION_SUPPORT_CSHOT = 1L shl 23
        const val OPERATION_SUPPORT_PORTRAIT = 1L shl 24 // Reserved
        const val OPERATION_SUPPORT_ENHANCE_TEXT = 1L shl 25
        const val OPERATION_SUPPORT_DLNA = 1L shl 26
        const val OPERATION_SUPPORT_VIDEO_GET_FRAME = 1L shl 27
        const val OPERATION_SUPPORT_CREATE_MEMORIES = 1L shl 28
        const val OPERATION_SUPPORT_LENS = 1L shl 29
        const val OPERATION_SUPPORT_MULTI_REGION_DECODER = 1L shl 30 // Reserved
        // support delete album
        const val OPERATION_SUPPORT_DELETE_ALBUM = 1L shl 31
        // support hlg或dolby转sdr
        const val OPERATION_SUPPORT_TRANSFORM_TO_SDR = 1L shl 32
        const val OPERATION_SUPPORT_REMOVE_LABEL = 1L shl 33
        const val OPERATION_SUPPORT_ONLY_IMAGE = 1L shl 34
        const val OPERATION_SUPPORT_RENAME_FILE = 1L shl 35
        const val OPERATION_SUPPORT_RENAME_ALBUM = 1L shl 36
        const val OPERATION_SUPPORT_CUSTOM_REMOVE_WIDGET_LIST = 1L shl 37
        const val OPERATION_SUPPORT_AI_ID = 1L shl 38
        const val OPERATION_SUPPORT_SHARED_MEDIA_TIP = 1L shl 39
        const val OPERATION_SUPPORT_SHARED_MEDIA_DELETE = 1L shl 40
        const val OPERATION_SUPPORT_SHARED_MEDIA_SAVE = 1L shl 41
        const val OPERATION_SUPPORT_SELF_SPLIT = 1L shl 42
        const val OPERATION_SUPPORT_PORTRAIT_BLUR = 1L shl 43
        const val OPERATION_SUPPORT_SAVE_TO_GIF = 1L shl 44
        const val OPERATION_SUPPORT_CLOUD_DOWNLOAD = 1L shl 45
        const val OPERATION_SUPPORT_COPY = 1L shl 46
        const val OPERATION_SUPPORT_GROUP_PHOTO = 1L shl 47
        const val OPERATION_SUPPORT_PREVIEW_CHECK = 1L shl 48
        const val OPERATION_SUPPORT_OLIVE_PHOTO = 1L shl 49
        const val OPERATION_SUPPORT_EXPORT_VIDEO = 1L shl 50
        const val OPERATION_SUPPORT_RECOMMENDED_REMOVE_WIDGET_LIST = 1L shl 51

        const val OPERATION_SUPPORT_PREVIEW_SELECT = 1L shl 52
        const val OPERATION_SUPPORT_SET_COVER = 1L shl 54
        const val OPERATION_SUPPORT_ALBUM_ORDER = 1L shl 55
        const val OPERATION_SUPPORT_CONVERT_PDF = 1L shl 56
        const val OPERATION_SUPPORT_GOOGLE_PASS_SCAN = 1L shl 57
        const val OPERATION_SUPPORT_OPEN_IN_SYSTEM_PLAYER = 1L shl 58
        const val OPERATION_SUPPORT_EXPORT_OLIVE = 1L shl 59
        const val OPERATION_SUPPORT_ALL = -0x1L
    }

    object IMediaItemSupportFormat {
        // These are the bits returned from getSupportedOperations():
        const val INT_NONE = 0
        const val FORMAT_NONE = 0L
        const val FORMAT_LARGE_ITEM = 1L
        const val FORMAT_RAW = 1L shl 1
        const val FORMAT_HEIF = 1L shl 2
        const val FORMAT_JPEG = 1L shl 3
        const val FORMAT_TEXT = 1L shl 4
        const val FORMAT_ENHANCE_TEXT = 1L shl 5
        const val FORMAT_GIF = 1L shl 6
        const val FORMAT_CSHOT = 1L shl 7
        const val FORMAT_AI_SCENERY = 1L shl 8
        const val FORMAT_SLOW_MOTION = 1L shl 10
        const val FORMAT_OLD_SLOW_MOTION = 1L shl 11
        const val FORMAT_MEMORY_VIDEO = 1L shl 12
        const val FORMAT_FAST_VIDEO = 1L shl 13
        const val FORMAT_LOG_VIDEO = 1L shl 14
        const val FORMAT_AI_ID = 1L shl 15
        const val FORMAT_LOSS_LESS_CACHE = 1L shl 16
        const val FORMAT_HLG_HDR = 1L shl 17
        const val FORMAT_TEN_BIT_FORMAT = 1L shl 18
        const val FORMAT_SUPER_HIGH_RESOLUTION = 1L shl 19
        const val FORMAT_PORTRAIT_BLUR = 1L shl 20
        const val FORMAT_DOLBY_VISION = 1L shl 21
        const val FORMAT_WATERMARK = 1L shl 22
        const val FORMAT_LOCAL_HDR = 1L shl 23
        const val FORMAT_CREDENTIAL = 1L shl 24
        const val FORMAT_GROUP_PHOTO = 1L shl 25
        const val FORMAT_ULTRA_HDR = 1L shl 26
        const val FORMAT_OLIVE = 1L shl 27
        const val FORMAT_SPATIAL_AUDIO = 1L shl 28
        const val FORMAT_PANORAMA = 1L shl 29
        const val FORMAT_FAST_CSHOT_QUICK_IMAGE = 1L shl 30
        const val FORMAT_OPLUS_ULTRA_HDR = 1L shl 31
        const val FORMAT_ALL = -0x1
    }

    object IEditItemSupportFormat {
        // These are the bits returned from getSupportedOperations():
        const val SUPPORT_FORMAT_NONE = 0
        const val SUPPORT_FORMAT_OLIVE = 1 shl 1
        const val SUPPORT_FORMAT_PRO_XDR = 1 shl 2
    }

    object SpecifiedCount {
        const val KEY_REQUIRE_SPECIFIED_COUNT = "key_require_specified_count"
        const val KEY_IMAGE_COUNT = "key_image_count"
        const val KEY_VIDEO_COUNT = "key_video_count"
        const val KEY_OLIVE_COUNT = "key_olive_image_count"
        const val KEY_CREATE_MEMORIES_COUNT = "key_create_memories_count"

        const val SPECIFIED_COUNT_IMAGE = 1
        const val SPECIFIED_COUNT_VIDEO = 1 shl 1
        const val SPECIFIED_COUNT_CREATE_MEMORIES = 1 shl 2
    }

    /**
     * 定义 获取底层数据结构MediaObject及其子类的相关属性 的Key
     * @see com.oplus.gallery.business_lib.model.data.base.MediaObject.getSpecifiedAttributes(Bundle)
     */
    object SpecifiedAttributes {
        // 描述MediaObject及其子类属性的Key和Attr
        /**
         * 入参Key，指定需要返回的属性。(KEY_REQUIRE_SPECIFIED_ATTRS, SPECIFIED_ATTR_IMAGE_COUNT | SPECIFIED_ATTR_VIDEO_COUNT)
         */
        const val KEY_REQUIRE_SPECIFIED_ATTRS = "key_require_specified_attrs"

        /**
         * 校验入参，如果入参异常，则不会再继续执行后边的流程
         */
        const val KEY_REQUIRE_PARAM_VALID = "key_require_param_valid"

        /**
         * 返回所有的属性
         */
        const val SPECIFIED_ATTR_ALL = -0x1L

        // 描述MediaItem（图片和视频）及其子类属性的Key和Attr
        /**
         * 入参指定需要返回快拍图片相关属性
         */
        const val SPECIFIED_ATTR_FAST_CAPTURE = 1

        /**
         * 当前Item是否为快拍缩图，返回类型为 Boolean
         */
        const val RESULT_KEY_IS_FAST_CAPTURE_TEMPORARY = "result_key_is_fast_capture_temporary"

        /**
         * 当前Item是否为tmp类型的快拍缩图，返回类型为 Boolean
         */
        const val RESULT_KEY_IS_FAST_CAPTURE_TEMPORARY_TMP = "result_key_is_fast_capture_temporary_tmp"

        /**
         * 当前Item是否为quick类型的快拍缩图，返回类型为 Boolean
         */
        const val RESULT_KEY_IS_FAST_CAPTURE_TEMPORARY_QUICK = "result_key_is_fast_capture_temporary_quick"

        /**
         * 当前Item是否为 superClear 类型，返回类型为 Boolean
         */
        const val RESULT_KEY_IS_FAST_CAPTURE_SUPER_CLEAR = "result_key_is_fast_capture_super_clear"

        /**
         * 当前Item是否为 superResolution 类型，返回类型为 Boolean
         */
        const val RESULT_KEY_IS_FAST_CAPTURE_SUPER_RESOLUTION = "result_key_is_fast_capture_super_resolution"

        /**
         * 当前连拍图片是否正在生成中，返回类型为 Boolean
         */
        const val RESULT_KEY_IS_CSHOT_BEING_TAKEN = "result_key_is_cshot_being_taken"

        // 描述MediaAlbum（图集）及其子类属性的Key和Attr
        /**
         * 入参指定需要返回图集中图片的数量，返回类型为 Int
         */
        const val SPECIFIED_ATTR_IMAGE_COUNT = 1

        /**
         * 图集中图片的数量，返回类型为 Int
         */
        const val RESULT_KEY_IMAGE_COUNT = "result_key_image_count"

        /**
         *  入参指定需要返回图集中的视频数量
         */
        const val SPECIFIED_ATTR_VIDEO_COUNT = 1 shl 1

        /**
         * 图集中视频的数量，返回类型为 Int
         */
        const val RESULT_KEY_VIDEO_COUNT = "result_key_video_count"

        /**
         * 入参指定需要返回当前图集可用于创建回忆的item数量
         */
        const val SPECIFIED_ATTR_CREATE_MEMORIES_COUNT = 1 shl 2

        /**
         * 图集中可以用于创建回忆的图片和视频数量，返回类型为 Int
         */
        const val RESULT_KEY_CREATE_MEMORIES_COUNT = "result_key_create_memories_count"

        /**
         * 标记MediaItem中的mTagFlag属性，返回值类型为 Int
         */
        const val KEY_SPECIFIED_ATTR_TAG_FLAGS = "key_tag_flags"

        /**
         * 标记MediaItem中的mExtTagFlags属性，返回值类型为 Long
         */
        const val KEY_SPECIFIED_ATTR_EXT_TAG_FLAGS = "key_ext_tag_flags"

        /**
         * 标记 [com.oplus.gallery.business_lib.model.data.base.item.SharedMediaItem.isThumbnailDownloaded] ，返回值类型为 Boolean
         */
        const val KEY_SHARED_THUMBNAIL_DOWNLOADED = "key_shared_thumbnail_downloaded"

        /**
         * 标记 [com.oplus.gallery.business_lib.model.data.base.item.SharedMediaItem.refVideo] ，返回值类型为 Boolean
         */
        const val KEY_SHARED_VIDEO_DOWNLOADED = "key_shared_video_downloaded"

        /**
         * 标记这个MediaItem是否为共享文件 ，返回值类型为 Boolean
         */
        const val KEY_SHARED_FILE = "key_shared_file"
    }

    /**
     * local_media中有一个字段ext_tag_flags是记录图片的扩展属性的（如相册算法归类出来的图片）
     * @see com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.EXT_TAG_FLAGS
     * 标签以位的形式存储，这里定义的对应的常量值
     */
    object ExtendAttributes {
        /**
         * extTagFlag 无效值
         */
        const val EXT_INVALID_VALUE = -1L

        /**
         * 是否是文本图片
         */
        const val EXT_TEXT_PHOTO = 1L

        /**
         * 是否本机相同机型拍摄图片（特殊格式图片，只在本机型上支持后处理，如人像景深）
         * EXT_SHOT_BY_SAME_DEVICE_PHOTO = 1 shl 1
         */
        const val EXT_SHOT_BY_SAME_DEVICE_PHOTO = Constants.Album.VirtualAlbum.EXT_SHOT_BY_SAME_DEVICE_PHOTO

        /**
         * 是否证件、卡类、证明材料（同随身卡包覆盖规则，这里不使用CardCase命名是不希望跟业务的随身卡包混在一起，它应该是图片属性，而不是图片归属）
         */
        const val EXT_CREDENTIAL_PHOTO: Long = 1 shl 2

        /**
         * 是否是合影优化后的图片
         */
        const val EXT_GROUP_PHOTO_OPTIMIZED = 1 shl 3

        /**
         * 针对Local hdr图片，是否需要优化其metadata的version信息。
         */
        const val EXT_LHDR_OPTIMIZE_META_VERSION = 1 shl 4

        /**
         * @deprecated 可图片矫正类型图片（已废弃）
         */
        @Deprecated("可图片矫正类型图片，已废弃")
        const val EXT_PIC_CORRECTION_PHOTO = 1 shl 5

        /**
         * 是否是空间音频（即5.1声道视频)
         */
        const val EXT_SPATIAL_AUDIO = 1 shl 6

        /**
         * 是否是HDR视频的Olive资源
         */
        const val EXT_OLIVE_HDR_VIDEO = 1L shl 7

        /**
         * 是否是Dolby视频的Olive资源
         */
        const val EXT_OLIVE_DOLBY_VIDEO = 1L shl 8

        /**
         * 是否是HLG资源
         * 从MMR通过解析METADATA_KEY_COLOR_TRANSFER 若是色彩曲线COLOR_TRANSFER_HLG，则写入此字段
         */
        const val EXT_HLG_HDR = 1L shl 9

        /**
         * 相机相册约定，针对错峰生成final图比较慢的场景，需要标记该图片类型
         * 例如：相机拍摄60X以上的图
         */
        const val EXT_IS_IMAGE_LOAD_SLOWLY = 1L shl 10

        /**
         * 已经执行过补光的图
         */
        const val EXT_IS_IMAGE_LIGHTING = 1L shl 11

        /**
         * 模糊：已检测，推荐
         */
        const val EXT_BLUR_RECOMMEND = STATUS_RECOMMEND shl SHIFT_BLUR

        /**
         * 反光：已检测，推荐
         */
        const val EXT_REFLECTION_RECOMMEND = STATUS_RECOMMEND shl SHIFT_REFLECTION

        /**
         * 路人：已检测，推荐
         */
        const val EXT_PASSERBY_RECOMMEND = STATUS_RECOMMEND shl SHIFT_PASSERBY


        /**
         * Google 检出登机牌
         */
        const val EXT_GOOGLE_PASS_SCANNED = STATUS_SCANNED shl SHIFT_GOOGLE_PASS_SCAN

        /**
         * Google 检出登机牌并被点击
         */
        const val EXT_GOOGLE_PASS_SCANNED_CLICKED = STATUS_SCANNED_CLICKED shl SHIFT_GOOGLE_PASS_SCAN
    }

    /**
     * 智能功能的相关常量值
     */
    object AIFunc {
        const val IMAGE_QUALITY_ENHANCE_MIN_IMAGE_SIZE = 100
        const val MIN_IMAGE_SIZE = 256
        const val MAX_IMAGE_SIZE = 10800
        const val AI_COMPOSITION_MIN_IMAGE_SIZE = 512
        const val AI_COMPOSITION_SUPPORTED_MAX_RATIO = 10

        /**
         * 未检测
         */
        const val STATUS_NOT_DETECTED = 0L

        /**
         * 已检测，但没有对应特征
         */
        const val STATUS_MISMATCH = 0b01L

        /**
         * 已检测，有对应特征
         */
        const val STATUS_MATCH = 0b10L

        /**
         * 已检测，推荐
         */
        const val STATUS_RECOMMEND = 0b11L

        /**
         * 缺陷值全位过滤遮罩 这里要+2递增，不然会出错
         */
        const val MASK = 0b11L

        const val SHIFT_BLUR = 32

        const val SHIFT_REFLECTION = 34

        const val SHIFT_PASSERBY = 36

        const val SHIFT_IMAGE_QUALITY_ENHANCE = 38 // 画质增强-长焦增强扫描

        /**
         * “最佳表情”使用40、41位记录状态
         * */
        const val SHIFT_BEST_TAKE = 40

        /**
         * AI 补光
         */
        const val SHIFT_AI_LIGHTING = 42

        /**
         * ai补光图片限制
         * 1.长宽最大比例 3
         * 2.最小边长 512
         */
        const val AI_LIGHTING_SUPPORTED_MAX_RATIO = 3
        const val AI_LIGHTING_SUPPORTED_MIN_SIZE = 512
    }

    /**
     * Google Pass Scan相关常量值
     */
    object GooglePassScanConst {
        const val MASK_SUPPORT = 0b10L

        /**
         * 未扫描
         */
        const val STATUS_NOT_SCANNED = 0L

        /**
         * 扫描未检出
         */
        const val STATUS_SCANNED_UNSUPPORTED = 0b01L

        /**
         * 扫描检出
         */
        const val STATUS_SCANNED = 0b10L

        /**
         * 扫描检出且用户点击过
         */
        const val STATUS_SCANNED_CLICKED = 0b11L

        /**
         * 登机牌扫描添加到Google Wallet起始位数
         */
        const val SHIFT_GOOGLE_PASS_SCAN = 40

        /**
         * 获取“添加到 Google Wallet”按钮点击事件来源的字典key
         */
        const val KEY_CLICK_SOURCE = "add_to_google_wallet_click_source"

        /**
         * 触发点击事件源--缺省值(更多菜单列表)
         */
        const val CLICK_SOURCE_DEFAULT = 1

        /**
         * 触发点击事件源--底部虚浮胶囊按钮
         */
        const val CLICK_SOURCE_FUNC_PRESENT = 2
    }
    /**
     * 音频声道数
     */
    object AudioChannelCount {
        /**
         * 0声道
         */
        const val CHANNEL_COUNT_0 = 0

        /**
         * 6声道
         */
        const val CHANNEL_COUNT_6 = 6
    }

    //ai 模型对应的厂商标记
    object AiModelType {
        const val ST = "ST" // 商汤
        const val SY = "SY" // 上研
        const val OA = "OA" // 美研
    }
}
