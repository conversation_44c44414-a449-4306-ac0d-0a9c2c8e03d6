/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PoiConfigFileParser.kt
 ** Description : 离线POI库加载
 ** Version     : 1.0
 ** Date        : 2025/4/2 15:35
 ** Author      : 80398228
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** qiupeng              2025/4/2      1.0     OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.model.data.location.utils

import android.content.Context
import com.oplus.gallery.business_lib.model.data.location.impl.PoiRouteInfo
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppConstants
import java.io.BufferedReader
import java.io.File
import java.io.IOException
import java.io.InputStreamReader

/**
 * poi表配置文件解析器
 * @param modelVersion 模型版本号
 */
class PoiConfigFileParser(val context: Context, val files: List<File>, var modelVersion: Int) {

    private var poiGpsKeyMap: HashMap<Double, MutableList<PoiRouteInfo>> = HashMap()

    init {
        if (files.isEmpty()) {
            parseLocalConfigFile()
        } else {
            parseConfigFile()
        }
    }

    fun getGpsKeyMap(): HashMap<Double, MutableList<PoiRouteInfo>> {
        return poiGpsKeyMap
    }

    /**
     * 解析 POI 端侧文件
     */
    private fun parseLocalConfigFile() {
        try {
            val startTime = System.currentTimeMillis()
            val inputStream = context.assets.open(ADDRESS_DICT_FILE_NAME)
            val reader = BufferedReader(InputStreamReader(inputStream))
            reader.useLines { lines ->
                val poiLocalList = lines
                    .filterNot { it.startsWith(COMMENT_PREFIX) }
                    .toList()
                parseFiledForLineString(poiLocalList)
            }
            GLog.d(TAG, LogFlag.DL) { "[PoiConfigFileParser] cost ${GLog.getTime(startTime)} ms, size = ${poiGpsKeyMap.size}" }
        } catch (e: IOException) {
            GLog.e(TAG, LogFlag.DL) { "[PoiConfigFileParser] parseConfigFile error: ${e.message}" }
        }
    }

    /**
     * 解析 POI 云侧文件
     */
    private fun parseConfigFile() {
        try {
            val startTime = System.currentTimeMillis()
            for (file in files) {
                file.useLines { lines ->
                    val poiLocalList = lines
                        .filterNot { it.startsWith(COMMENT_PREFIX) }
                        .toList()
                    parseFiledForLineString(poiLocalList)
                }
            }
            GLog.d(TAG, LogFlag.DL) { "[PoiConfigFileParser] cost ${GLog.getTime(startTime)} ms, size = ${poiGpsKeyMap.size}" }
        } catch (e: IOException) {
            GLog.e(TAG, LogFlag.DL) { "[PoiConfigFileParser] parseConfigFile error: ${e.message}" }
        }
    }

    /**
     * 从每一行字符串中提取 POI 信息
     * @param poiLocalList POI 云侧文件的每一行字符串组成的列表
     */
    private fun parseFiledForLineString(poiLocalList: List<String>) {
        for (it in poiLocalList) {
            val parts = it.split(TextUtil.SEMICOLON)
            // 目前是固定 8 列，如果少了，则认为数据有错误，忽略
            if (parts.size <= IDX_POI_ENABLE) {
                GLog.w(TAG, LogFlag.DL) { "parseFiledForLineString. parts size error. size: ${parts.size} <= $IDX_POI_ENABLE, $parts" }
                continue
            }
            val coordinates = parts[IDX_COORDINATES].split(TextUtil.SPLIT_COMMA_SEPARATOR)
            if (coordinates.size != AppConstants.Number.NUMBER_2) {
                continue
            }
            val latitude = coordinates[AppConstants.Number.NUMBER_0].trim().toDoubleOrNull()
            val longitude = coordinates[AppConstants.Number.NUMBER_1].trim().toDoubleOrNull()
            if (latitude != null && longitude != null) {
                val gpsKey = LocationHelper.makeKey(latitude, longitude)
                val poiRouteInfos = mutableListOf<PoiRouteInfo>()
                poiRouteInfos.add(
                    PoiRouteInfo(
                        gpsKey,
                        latitude,
                        longitude,
                        parts[IDX_ADDRESS_NAME].trim(),
                        parts[IDX_ADDRESS_NAME].trim(),
                        parts[IDX_POI_RADIUS].trim().toInt(),
                        gpsKey,
                        parts[IDX_ALIAS_LIST].split(TextUtil.SPLIT_COMMA_SEPARATOR).filter { it.isNotEmpty() }.ifEmpty { null },
                        parts[IDX_OPPO_POI_ID].trim(),
                        parts[IDX_POI_SOURCE].trim(),
                        parts[IDX_POI_SOURCE_VERSION].trim(),
                        parts[IDX_POI_ENABLE].trim() == ENABLE_TEXT,
                        parts[IDX_POI_ADDR_TYPE].trim().toInt()
                    )
                )
                poiGpsKeyMap[gpsKey.toDouble()] = poiRouteInfos
            }
        }
    }

    fun clear() {
        poiGpsKeyMap.clear()
    }

    companion object {
        private const val TAG = "PoiConfigFileParser"

        /**
         * POI 预制本地文件名称
         */
        private const val ADDRESS_DICT_FILE_NAME = "addressDict.txt"

        /**
         * POI 预制本地文件版本号
         */
        const val ADDRESS_DICT_VERSION = 1
        private const val IDX_ADDRESS_NAME = 0
        private const val IDX_ALIAS_LIST = 1
        private const val IDX_COORDINATES = 2

        private const val IDX_OPPO_POI_ID = 3

        private const val IDX_POI_SOURCE = 4
        private const val IDX_POI_SOURCE_VERSION = 5

        /**
         * POI 类型。 1：高效 2：景区
         */
        private const val IDX_POI_ADDR_TYPE = 6
        /**
         * 当前 POI 所囊括的半径范围
         */
        private const val IDX_POI_RADIUS = 7
        private const val IDX_POI_ENABLE = 8
        private const val ENABLE_TEXT = "1"

        /**
         * 注释前缀
         */
        private const val COMMENT_PREFIX = "//"
    }
}