/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : PoiConfigFileParser.kt
 ** Description : 离线POI库加载
 ** Version     : 1.0
 ** Date        : 2025/4/2 15:35
 ** Author      : 80398228
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** qiupeng              2025/4/2      1.0     OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.model.data.location.utils

import android.content.Context
import com.oplus.gallery.business_lib.model.data.location.impl.PoiRouteInfo
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader

class PoiConfigFileParser(val context: Context) {
    private var poiConfigHelper: PoiConfigHelper = PoiConfigHelper

    private var poiGpsKeyMap: HashMap<Double, MutableList<PoiRouteInfo>> = HashMap()

    init {
        parseConfigFile()
    }

    fun getGpsKeyMap(): HashMap<Double, MutableList<PoiRouteInfo>> {
        return poiGpsKeyMap
    }

    private fun parseConfigFile() {
        try {
            val startTime = System.currentTimeMillis()
            val inputStream = context.assets.open(ADDRESS_DICT_FILE_NAME) ?: return
            val reader = BufferedReader(InputStreamReader(inputStream))
            var line: String?
            reader.readLine() //首行无效
            val poiLocalList = ArrayList<String>()
            var lineCount = 0
            while (reader.readLine().also { line = it } != null) {
                lineCount++
                line?.let { poiLocalList.add(it) }
            }
            reader.close()
            inputStream.close()
            for (it in poiLocalList) {
                val parts = it.split(TextUtil.SEMICOLON)
                val partSize = parts.size
                if (partSize <= IDX_FULL_ADDRESS) {
                    continue
                }
                val poiName = parts[IDX_ADDRESS_NAME].trim()
                val aliasList = ArrayList<String>()
                parts[IDX_ALIAS_LIST].split(TextUtil.SPLIT_COMMA_SEPARATOR).forEach {
                    val aliaTrim = it.trim()
                    if (aliaTrim.isNotEmpty()) {
                        aliasList.add(aliaTrim)
                    }
                }
                val coordinates = parts[IDX_COORDINATES].split(TextUtil.SPLIT_COMMA_SEPARATOR)
                val fullAddress = parts[IDX_FULL_ADDRESS].trim()
                if (coordinates.size < 2) {
                    continue
                }
                val latitude = coordinates[0].trim().toDoubleOrNull()
                val longitude = coordinates[1].trim().toDoubleOrNull()
                val radius = poiConfigHelper.getPoiReverseDefaultAoiRadius()
                if (latitude != null && longitude != null) {
                    val gpsKey = LocationHelper.makeKey(latitude, longitude)
                    val poiRouteInfos = mutableListOf<PoiRouteInfo>()
                    poiRouteInfos.add(
                        PoiRouteInfo(
                            gpsKey, latitude, longitude, poiName, fullAddress, radius,
                            LocationHelper.makeKey(latitude, longitude),
                            aliasList.ifEmpty { null }
                        )
                    )
                    poiGpsKeyMap[gpsKey.toDouble()] = poiRouteInfos
                }
            }
            GLog.d(TAG, LogFlag.DL) { "[PoiConfigFileParser] cost ${System.currentTimeMillis() - startTime} ms" }
        } catch (e: IOException) {
            GLog.e(TAG, LogFlag.DL) { "[PoiConfigFileParser] parseConfigFile error: ${e.message}" }
        }
    }

    fun clear() {
        poiGpsKeyMap.clear()
    }

    companion object {
        private const val TAG = "PoiConfigFileParser"
        private const val ADDRESS_DICT_FILE_NAME = "addressDict.txt"
        private const val IDX_ADDRESS_NAME = 0
        private const val IDX_ALIAS_LIST = 1
        private const val IDX_COORDINATES = 2
        private const val IDX_FULL_ADDRESS = 3
    }
}