/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SharedSource.java
 * Description:共享图集
 * Version: 1.0
 * Date: 2022/11/22
 * Author: zhongxuechang@Apps.Gallery3D
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * zhongxuechang@Apps.Gallery3D 2022/11/22     1.0      build this module
 ***************************************************************************************************/
package com.oplus.gallery.business_lib.model.data.base.source

import com.oplus.gallery.business_lib.model.data.base.MediaObject
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.SharedMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.SharedThumbnailImage
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Shared
import com.oplus.gallery.business_lib.model.data.share.SharedAlbum
import com.oplus.gallery.business_lib.model.data.share.SharedAlbumSet

internal class SharedSource : MediaSource(Shared.SRC) {

    init {
        initKindMap()
        setupMatcher()
    }

    private fun initKindMap() {
        mPathToKindMap[Shared.PATH_SET_ALL_ANY] = M_SET_ALL_ANY
        mAllChildPathToKindMap[Shared.PATH_ALBUM_ALL_ANY] = M_ALBUM_ALL_ANY
        mAllChildPathToKindMap[Shared.PATH_ITEM_IMAGE] = M_ITEM_SHARED_IMAGE
        mAllChildPathToKindMap[Shared.PATH_ITEM_VIDEO] = M_ITEM_SHARED_VIDEO
        mAllChildPathToKindMap[Shared.PATH_ITEM_THUMBNAIL_IMAGE] = M_ITEM_THUMBNAIL_IMAGE
    }

    override fun createMediaObject(path: Path): MediaObject {
        return when (mMatcher.match(path)) {
            M_SET_ALL_ANY -> SharedAlbumSet(path)
            M_ALBUM_ALL_ANY -> SharedAlbum(mContext, path)
            M_ITEM_SHARED_IMAGE, M_ITEM_SHARED_VIDEO -> SharedMediaItem(path)
            M_ITEM_THUMBNAIL_IMAGE -> SharedThumbnailImage(path)
            else -> throw IllegalArgumentException("bad path: $path")
        }
    }

    companion object {
        private const val M_SET_ALL_ANY = 0
        private const val M_ALBUM_ALL_ANY = M_SET_ALL_ANY + 1
        private const val M_ITEM_SHARED_IMAGE = M_ALBUM_ALL_ANY + 1
        private const val M_ITEM_SHARED_VIDEO = M_ITEM_SHARED_IMAGE + 1
        private const val M_ITEM_THUMBNAIL_IMAGE = M_ITEM_SHARED_VIDEO + 1
    }
}