/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : AlbumDataCache.kt
 ** Description: 图集数据缓存类
 ** Version    : 1.0
 ** Date       : 2024/11/20
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2024/11/20    1.0              init
 *************************************************************************************************/
package com.oplus.gallery.business_lib.model.data.picture.set

import android.content.ContentValues
import android.os.Bundle
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.database.trigger.operator.OperationType
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.isIndexOutOfBounds
import com.oplus.gallery.foundation.util.ext.merge
import com.oplus.gallery.foundation.util.math.contains
import com.oplus.gallery.foundation.util.math.length
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.foundation.util.text.TextUtil
import java.util.Collections
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.withLock

/**
 * 数据缓存类
 */
private data class CacheEntry(
    val capacity: Int = 0,
    val key: String = TextUtil.EMPTY_STRING,
    var dataInitialized: Boolean = false,
    var range: IntRange = IntRange.EMPTY,
    var items: MutableList<MediaItem> = mutableListOf(),
    var totalCount: Int = -1,
    var imageCount: Int = -1,
    var videoCount: Int = -1
) {

    fun isEmpty(): Boolean = range.isEmpty() || items.isEmpty()

    fun increaseCount(isVideo: Boolean) {
        if (totalCount >= 0) totalCount++
        if (isVideo) {
            if (videoCount >= 0) videoCount++
        } else {
            if (imageCount >= 0) imageCount++
        }
    }

    fun decreaseCount(isVideo: Boolean) {
        if (totalCount >= 0) totalCount--
        if (isVideo) {
            if (videoCount >= 0) videoCount--
        } else {
            if (imageCount >= 0) imageCount--
        }
    }

    override fun toString(): String {
        return "CacheEntry(" +
                "hashCode=${hashCode()}, " +
                "capacity=$capacity, " +
                "key=$key, " +
                "dataInitialized=$dataInitialized, " +
                "range=$range, " +
                "items=${items.size}, " +
                "totalCount=$totalCount, " +
                "imageCount=$imageCount, " +
                "videoCount=$videoCount" +
                ")"
    }
}

/**
 * 图集数据缓存类。
 *
 * 功能：提供图集基本信息和Item的缓存能力，减少数据库查询，提升性能
 *
 * @param syncStrategy 数据同步的策略类，提供过滤筛选、排序、Item对象构建等策略
 * @param capacity 缓存容量大小
 * @param capacityTrimThreshold 触发因缓存容量过大裁剪的阈值，默认值=[capacity]*[CAPACITY_TRIM_THRESHOLD_PERCENTAGE]
 */
internal class AlbumDataCache(
    override val tag: String,
    override val syncStrategy: IAlbumDataSyncStrategy,
    private val capacity: Int = CAPACITY_DEFAULT_VALUE,
    private val capacityTrimThreshold: Int = (capacity * CAPACITY_TRIM_THRESHOLD_PERCENTAGE).toInt()
) : IAlbumDataCache {

    /**
     * 图集缓存类，存储图集数据信息
     */
    private val cacheEntry = CacheEntry(capacity, tag)

    private val readWriteLock by lazy { ReentrantReadWriteLock() }

    /**
     * 连拍id与封面的MediaItem的映射表，用来过滤非连拍封面的Item，进行去重，只保留封面Item即可。
     *
     * 连拍数据变更后，重新确定封面item，刷新缓存。此缓存只保留每一个连拍的封面item。
     */
    private val cShotCovers: MutableMap<Long, MediaItem> = mutableMapOf()

    override fun getItems(range: IntRange): List<MediaItem> = readWriteLock.readLock().withLock {
        if (range.isEmpty()) {
            GLog.e(TAG, LogFlag.DL) { "[getItems] request range invalid! $range" }
            return emptyList()
        }
        if (cacheEntry.dataInitialized.not() || cacheEntry.isEmpty()) {
            if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[getItems] cache invalid for $cacheEntry" }
            return emptyList()
        }
        return if (cacheEntry.range.contains(range)) {
            if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[getItems] found range:$range for $cacheEntry" }
            val start = range.first - cacheEntry.range.first
            cacheEntry.items.subList(start, start + range.length())
        } else {
            if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[getItems] not found range:$range for $cacheEntry" }
            emptyList()
        }
    }

    /**
     * 业务加载数据后，保存到缓存中。
     * 1. 首次更新，直接写入
     * 2. 再次更新，有交集或相邻，则确定为有效区间，合并进去，否则抛弃
     */
    override fun setItems(range: IntRange, items: List<MediaItem>) = readWriteLock.writeLock().withLock {
        if (items.isEmpty() || (range.length() != items.size)) {
            GLog.d(TAG, LogFlag.DL) { "[setItems-items] end. invalid params, range:$range itemCount:${items.size}" }
            return
        }

        // 首次更新
        if (cacheEntry.range.isEmpty()) {
            cacheEntry.range = range
            cacheEntry.items.clear()
            cacheEntry.items.addAll(items)
            cacheEntry.dataInitialized = true
            items.forEach { updateCShotCovers(it) }
            GLog.d(TAG, LogFlag.DL) { "[setItems-items] end. init cache, add all, $cacheEntry" }
            return
        }

        if (cacheEntry.range.length() >= capacity) {
            GLog.d(TAG, LogFlag.DL) { "[setItems-items] end. ignore by cache full, $cacheEntry" }
            return
        }

        // 再次更新，有交集或相邻，合并进去
        val startTime = System.currentTimeMillis()
        cacheEntry.range.merge(range)?.let { mergedRange ->
            var startIndex = range.first
            items.forEach { mediaItem ->
                if (cacheEntry.range.contains(startIndex)) {
                    cacheEntry.items[startIndex] = mediaItem
                } else {
                    cacheEntry.items.add(startIndex, mediaItem)
                }
                startIndex++
                updateCShotCovers(mediaItem)
            }
            cacheEntry.range = mergedRange
            cacheEntry.dataInitialized = true
            GLog.d(TAG, LogFlag.DL) {
                "[setItems-items] end. cost time:${GLog.getTime(startTime)}, update range:$range $cacheEntry"
            }
        } ?: GLog.e(TAG, LogFlag.DL) {
            "[setItems-items] end. cost time:${GLog.getTime(startTime)}, range merge failed! $cacheEntry"
        }
    }

    /**
     * 过滤掉连拍重非封面图的item。
     *
     * 1. 连拍拍摄时，只需要显示封面图，故需要过滤掉非封面的item
     * 2. 连拍拍摄时，无法保证cover item会第一个插入数据库，故需要刷新cover item
     *
     * @param values diff后的对象数据
     * @param getMediaItem 使用[values]构建，并返回需要缓存的item
     *
     * @return true:需要过滤 false：无需过滤
     */
    private fun filterCShotItem(values: ContentValues, getMediaItem: () -> MediaItem?): Boolean {
        val cShotID = values.getAsLong(LocalColumns.CSHOT_ID)
        // 非连拍，无需过滤
        if (DatabaseUtils.isCShotIdValid(cShotID).not()) {
            getMediaItem.invoke()
            return false
        }

        val mediaItem = getMediaItem.invoke() ?: run {
            GLog.w(TAG, LogFlag.DL) { "[filterCShotItem] getMediaItem is null!" }
            return false
        }

        // 新连拍，加入连拍封面记录，无需过滤；已缓存的连拍，过滤并尝试更新封面。
        return cShotCovers[cShotID]?.let {
            updateCShotCovers(mediaItem)
            true
        } ?: run {
            updateCShotCovers(mediaItem)
            false
        }
    }

    /**
     * 更新连拍封面。第一次初始化数据和后续更新数据时，同步更新连拍封面。
     */
    private fun updateCShotCovers(item: MediaItem) {
        val cShotID = item.cShotID
        if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[updateCShotCovers] cShotID:$cShotID cShotCovers:${cShotCovers.size} ${cShotCovers.contains(cShotID)}" }
        if (DatabaseUtils.isCShotIdValid(cShotID).not()) return
        val coverItem = cShotCovers[cShotID]
        coverItem?.let { curCoverItem ->
            // 连拍缓存已经存在，需要过滤，然后更新连拍封面item
            if (item.filePath < curCoverItem.filePath) {
                cShotCovers[cShotID] = item
                // 封面变更后，更新缓存item为新的封面item
                cacheEntry.items.indexOfFirst { it.path == curCoverItem.path }.takeIf { it >= 0 }?.let {
                    cacheEntry.items[it] = item
                }
                if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[updateCShotCovers] update cache item" }
            }
        } ?: let {
            cShotCovers[cShotID] = item
        }
    }

    /**
     * 缓存超过[capacityTrimThreshold]后，进行裁剪
     *
     * 插入新数据到缓存后，需要检查缓存是否超限。
     */
    private fun trimCache() {
        val cacheSize = cacheEntry.range.length()
        (cacheSize - capacityTrimThreshold).takeIf { it > 0 }?.let {
            val removeCount = cacheSize - capacity
            cacheEntry.range = IntRange(cacheEntry.range.first, cacheEntry.range.last - removeCount)
            cacheEntry.items = cacheEntry.items.subList(0, cacheEntry.items.size - removeCount)
            GLog.d(TAG, LogFlag.DL) { "[trimCache] remove last $removeCount items <<< $cacheEntry" }
        }
    }

    /**
     * 数据库数据变更后会发送diff，按照指定的排序规则，diff信息在已经确定正确位置的区间，则可以直接更新缓存，此时缓存和数据库片段一致。
     *
     * 1. [0, total - 1] 排序位置确定，可以直接更新
     * 2. (-- ,0] 排序位置确定，可以更新
     * 3. [total, ++) 抛弃，无法确定从index=total开始，此数据到底应该排序在哪个index
     *
     * @param items diff数据
     * @return 缓存是否变更 true：已变更
     */
    override fun setItems(items: Pair<OperationType, Pair<ContentValues, ContentValues?>>): Boolean = readWriteLock.writeLock().withLock {
        if (cacheEntry.dataInitialized.not()) {
            GLog.e(TAG, LogFlag.DL) { "[setItems-values] cache invalid for $cacheEntry" }
            return false
        }
        val startTime = System.currentTimeMillis()
        val operationType = items.first
        val valueChangedPair = items.second
        if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[setItems-values] decideOperation first: ${valueChangedPair.first} second: ${valueChangedPair.second}" }
        val opType = syncStrategy.decideOperation(operationType, valueChangedPair) ?: let {
            if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[setItems-values] decideOperation Non-current album data!" }
            return false
        }
        if (DEBUG) {
            GLog.d(TAG, LogFlag.DL) { "[setItems-values] decideOperation cost time=${GLog.getTime(startTime)} opType: $opType $cacheEntry" }
        }
        val cacheChanged = when (opType) {
            OperationType.DELETE_LOCAL -> deleteItem(valueChangedPair.first)
            OperationType.UPDATE_LOCAL -> updateOrInsertItem(valueChangedPair.first, false)
            OperationType.INSERT_LOCAL -> updateOrInsertItem(valueChangedPair.first, true)
        }
        trimCache()
        if (DEBUG) {
            GLog.d(TAG, LogFlag.DL) { "[setItems-values] cost time=${GLog.getTime(startTime)} $cacheEntry" }
        }
        return cacheChanged
    }

    override fun isCacheInit(): Boolean {
        return cacheEntry.dataInitialized
    }

    /**
     * 删除[values]指定item的缓存
     */
    private fun deleteItem(values: ContentValues): Boolean {
        if (cacheEntry.dataInitialized.not()) {
            if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[deleteItem] cache invalid for $cacheEntry" }
            return false
        }
        val startTime = System.currentTimeMillis()
        val localId = values.getAsInteger(LocalColumns._ID)
        val video = MEDIA_TYPE_VIDEO == values.getAsInteger(LocalColumns.MEDIA_TYPE)
        val cShotID = values.getAsLong(LocalColumns.CSHOT_ID)
        val cacheIndex = findItemIndex(localId)
        cacheEntry.decreaseCount(video)
        if (cacheEntry.items.isIndexOutOfBounds(cacheIndex)) {
            GLog.d(TAG, LogFlag.DL) { "[deleteItem] ignore by not found delete index" }
            return false
        }
        cacheEntry.items.removeAt(cacheIndex)
        cacheEntry.range = IntRange(cacheEntry.range.first, cacheEntry.range.last - 1)
        // 删除cShotCovers中对应的连拍记录
        cShotCovers.remove(cShotID)
        if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[deleteItem] $cacheEntry cost time=${GLog.getTime(startTime)}" }
        return true
    }

    /**
     * 更新|插入 [values]指定的item
     * 1. 已在缓存中，更新内容和排序
     * 2. 不在缓存中，且是重复的连拍，过滤掉
     * 3. 插入到缓存
     */
    private fun updateOrInsertItem(values: ContentValues, isInsert: Boolean): Boolean {
        var mediaItem: MediaItem? = null

        // 1 已经在缓存中，无需插入，更新内容；如果排序变更了，重新排序
        val localId = values.getAsInteger(LocalColumns._ID)
        val cacheIndex = findItemIndex(localId)
        val hasCache = cacheIndex >= 0
        if (hasCache) {
            val orderChange = syncStrategy.orderCompare(cacheEntry.items[cacheIndex], values) != 0
            // 更新内容
            mediaItem = syncStrategy.generateItem(values)
            // 排序变更后，重新排序
            if (orderChange) cacheEntry.items.sort()
            return true
        }

        // 2 过滤连拍堆叠的重复图
        val shouldFilter = filterCShotItem(values) {
            // 连拍拍摄过程中，构建MediaItem会有binder耗时，故非必要不要构建
            mediaItem ?: syncStrategy.generateItem(values).apply { mediaItem = this }
        }
        if (shouldFilter) {
            if (DEBUG) GLog.w(TAG, LogFlag.DL) { "[updateOrInsertItem] return=$shouldFilter" }
            return false
        }
        val shouldCachedItem = mediaItem ?: return false

        // 3 找不到当前item需要插入的位置，无需插入缓存，则只更新count即可；否则，插入缓存
        val shouldInsertIndex = computeInsertIndex(values)
        if (shouldInsertIndex < 0) {
            if (isInsert) cacheEntry.increaseCount(shouldCachedItem.isVideo)
        } else {
            cacheEntry.items.add(shouldInsertIndex, shouldCachedItem)
            cacheEntry.range = IntRange(cacheEntry.range.first, cacheEntry.range.last + 1)
            if (isInsert) cacheEntry.increaseCount(shouldCachedItem.isVideo)
            if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[updateOrInsertItem] $cacheEntry" }
        }
        return true
    }

    /**
     * 缓存order变更后重新对缓存排序
     */
    private fun MutableList<MediaItem>.sort() {
        val startTime = System.currentTimeMillis()
        Collections.sort(this, Comparator { o1, o2 ->
            syncStrategy.orderCompare(o1, o2)
            return@Comparator 0
        })
        GLog.d(TAG, LogFlag.DL) { "[sort] cost time=${(System.currentTimeMillis() - startTime)}" }
    }

    override fun getItemCount(): Int = readWriteLock.readLock().withLock {
        return cacheEntry.totalCount
    }

    override fun setItemCount(totalCount: Int) = readWriteLock.writeLock().withLock {
        cacheEntry.totalCount = totalCount
        GLog.d(TAG, LogFlag.DL) { "[setItemCount] $cacheEntry" }
    }

    override fun getImageCount(): Int = readWriteLock.readLock().withLock {
        return cacheEntry.imageCount
    }

    override fun setImageCount(imageCount: Int) = readWriteLock.writeLock().withLock {
        cacheEntry.imageCount = imageCount
    }

    override fun getVideoCount(): Int = readWriteLock.readLock().withLock {
        return cacheEntry.videoCount
    }

    override fun setVideoCount(videoCount: Int) = readWriteLock.writeLock().withLock {
        cacheEntry.videoCount = videoCount
    }

    override fun getSpecifiedAttributes(keys: List<String>): Bundle = readWriteLock.readLock().withLock {
        return Bundle()
    }

    override fun setSpecifiedAttributes(inBundle: Bundle) = readWriteLock.writeLock().withLock {
        // ignore
    }

    override fun getIndexOfItem(path: Path): Int = readWriteLock.readLock().withLock {
        return cacheEntry.items.indexOfFirst {
            it.path == path
        }.also { index ->
            if (DEBUG && (index >= 0)) GLog.d(TAG, LogFlag.DL) { "[getIndexOfItem] hit cache for path:$path index:$index $cacheEntry" }
        } + cacheEntry.range.first
    }

    /**
     * 找到values在缓存中的index，这里只能使用主键去找
     *
     * @param localId 数据库主键：_id
     *
     * @return index -1 代表没有找到
     */
    private fun findItemIndex(localId: Int): Int {
        return cacheEntry.items.indexOfFirst { it.id == localId }
    }

    /**
     * 遍历[cacheEntry]，找出需要插入的index。
     *
     * @param values 待处理数据
     *
     * @return 需要插入新数据的index
     */
    private fun computeInsertIndex(values: ContentValues): Int {
        if(cacheEntry.range.isEmpty()) return -1
        val startTime = System.currentTimeMillis()
        // range.last后的图片，无法确定其是否是连续的，抛弃
        val lastIndex = cacheEntry.range.length() - 1
        val lastItem = cacheEntry.items[lastIndex]
        if (syncStrategy.orderCompare(lastItem, values) < 0) {
            if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[findIndex] ignore by less than mim order" }
            return -1
        }

        var index = 0
        cacheEntry.items.forEach {
            syncStrategy.orderCompare(it, values).let { orderComparedResult ->
                when {
                    // 排序位置与当前item相同，返回当前item位置，且与当前item为同一
                    orderComparedResult == 0 -> return index
                    // 排序位置在当前item之前，返回当前item位置
                    orderComparedResult > 0 -> return index
                    // 排序比当前item靠后，继续寻找
                    else -> index++
                }
            }
        }

        if (DEBUG) GLog.d(TAG, LogFlag.DL) { "[computeInsertIndex] cost time=${(System.currentTimeMillis() - startTime)}" }
        return -1
    }

    companion object {
        private const val TAG = "AlbumDataCache"

        /**
         * 调试开关：图集数据缓存
         */
        private val DEBUG: Boolean = GallerySystemProperties.getBoolean("debug.gallery.album.datacache", false)

        /**
         * 緩存默认容量。大图加载一次大概350左右，缓存1000个足够相机进大图使用。
         */
        private const val CAPACITY_DEFAULT_VALUE: Int = 1000

        /**
         * 缓存容量裁剪阈值的比例，超过此比例，则需要进行缓存裁剪
         */
        private const val CAPACITY_TRIM_THRESHOLD_PERCENTAGE: Float = 1.1f
    }
}