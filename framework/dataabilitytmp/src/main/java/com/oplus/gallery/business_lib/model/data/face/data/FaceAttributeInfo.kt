/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceAttributeInfo
 ** Description:数据类
 ** Version: 1.0
 ** Date: 2025-01-08
 ** Author: wudanyang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wudanyang@Apps.Gallery3D    2025-01-08     1.0
 ********************************************************************************/
package com.oplus.gallery.business_lib.model.data.face.data

import android.graphics.Rect
import com.oplus.gallery.business_lib.model.data.face.data.mirror.FaceAttrInfoMirror
import java.util.Collections

/**
 * 人脸特征信息，旧的数据类迁移kt
 */
class FaceAttributeInfo {
    private var age = 0 //< score(0-100)
    private var race = 0 //< Yellow 0, Black 1, White 2, Brown 3
    private var sex = 0 //1 male, 0 female
    private val skin = 0 //< score(0-100)  The lower the score, the better the skin is.
    private val faceRect: Rect? = null

    private val yellowScore = 0
    private val blackScore = 0
    private val whiteScore = 0
    private val brownScore = 0
    private var quality = 0
    private var cover = 0

    /**
     * 人脸质量分数
     */
    private var qualityScore = 0

    /**
     * 人脸表情分数
     */
    private var faceExpressionScore = 0f

    /**
     * 人脸表情详细细分
     * 大笑：0
     * 微笑：1
     * 中性：2
     * 其它：3
     */
    private var expressionDetail = EXPRESSION_DETAIL_NEUTRAL

    /**
     * 快乐分数
     */
    private var happyScore = 0f

    /**
     * 表情分类:
     * NEUTRAL(0),
     * HAPPY(1),
     * SAD(2),
     * SURPRISE(3),
     * FEAR(4),
     * DISGUST(5),
     * ANGRY(6)
     */
    private var emotion: Int = EMOTION_NEUTRAL

    /**
     * 单人物封面综合得分
     * 用于单人物封面优选
     */
    private var faceCoverScore = 0f

    /**
     * 多角色人脸表情分数
     * 用于合照图集封面计算
     */
    private var faceCoverExpression = 0f

    constructor(faceInfo: FaceAttrInfoMirror) {
        age = faceInfo.age
        sex = faceInfo.sex
        race = faceInfo.race
        //上研sdk版本中只有cover的值是有效的
        quality = faceInfo.cover
        cover = faceInfo.cover
        qualityScore = faceInfo.qualityScore
        faceExpressionScore = faceInfo.faceExpressionScore
        happyScore = faceInfo.happyScore
        expressionDetail = faceInfo.expressionDetail
        emotion = faceInfo.emotion
        faceCoverScore = faceInfo.faceCoverScore
        faceCoverExpression = faceInfo.faceCoverExpression
    }

    /**
     * 获取人脸质量分数
     * @return Int 人脸质量分数
     */
    fun getQualityScore(): Int {
        return qualityScore
    }

    /**
     * 获取人脸表情分数
     * @return Float 人脸表情分数
     */
    fun getFaceExpressionScore(): Float {
        return faceExpressionScore
    }

    /**
     * 人脸表情详细细分
     * @return Int 细分值
     */
    fun getExpressionDetail(): Int {
        return expressionDetail
    }

    /**
     * 获取快乐分数
     * @return Float 快乐分数值
     */
    fun getHappyScore(): Float {
        return happyScore
    }

    /**
     * 获取表情分类
     * @return Int 表情分类
     */
    fun getEmotion(): Int {
        return emotion
    }

    fun getCover(): Int {
        return cover
    }

    fun getBestScore(): Float {
        return cover.toFloat() / PERCENT
    }

    fun getFaceRect(): Rect? {
        return faceRect
    }

    fun getAge(): Int {
        return age
    }

    /**
     * 获取单人脸封面综合得分
     * @return Float 人脸封面综合得分
     */
    fun getFaceCoverScore(): Float {
        return faceCoverScore
    }

    /**
     * 获取多角色合照表情得分
     * @return Float 角色合照表情得分
     */
    fun getFaceCoverExpression(): Float {
        return faceCoverExpression
    }

    fun getRace(): Int {
        val raceScoreList: ArrayList<Int> = ArrayList()
        raceScoreList.add(RACE_YELLOW, yellowScore)
        raceScoreList.add(RACE_BLACK, blackScore)
        raceScoreList.add(RACE_WHITE, whiteScore)
        raceScoreList.add(RACE_BROWN, brownScore)
        return raceScoreList.indexOf(Collections.max(raceScoreList))
    }

    fun getSex(): Int {
        return if ((sex == MALE)) MALE else FEMALE
    }

    fun getSkin(): Int {
        return skin
    }

    companion object {
        const val TAG: String = "FaceAttributeInfo"
        const val DEFAULT_HANDLE_STATE: Int = 0
        const val DETECTED_HANDLE_STATE: Int = 1
        const val STATISTICS_HANDLE_STATE: Int = 2
        const val RACE_YELLOW: Int = 0
        const val RACE_BLACK: Int = 1
        const val RACE_WHITE: Int = 2
        const val RACE_BROWN: Int = 3
        const val FEMALE: Int = 0
        const val MALE: Int = 1
        const val PERCENT: Int = 100

        const val EMOTION_NEUTRAL = 0
        const val EMOTION_HAPPY = 1
        const val EMOTION_SAD = 2
        const val EMOTION_SURPRISE = 3
        const val EMOTION_FEAR = 4
        const val EMOTION_DISGUST = 5
        const val EMOTION_ANGRY = 6

        const val EXPRESSION_DETAIL_DEFAULT = -1
        const val EXPRESSION_DETAIL_LAUGH = 0
        const val EXPRESSION_DETAIL_SMILE = 1
        const val EXPRESSION_DETAIL_NEUTRAL = 2
        const val EXPRESSION_DETAIL_OTHERS = 3
    }
}