/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ChangeNotifier.java
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/4
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2020/8/4      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/


package com.oplus.gallery.business_lib.model.data.base;

import static com.oplus.gallery.bus_lib.annotations.FinderType.CLASS_METHOD_FINDER;

import android.content.Context;
import android.net.Uri;

import com.oplus.gallery.bus_lib.Bus;
import com.oplus.gallery.bus_lib.annotations.Subscribe;
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;

import java.util.concurrent.atomic.AtomicBoolean;

// This handles change notification for media sets.
public class ChangeNotifier {

    private static final String TAG = "ChangeNotifier";
    private MediaSet mMediaSet;
    private AtomicBoolean mContentDirty = new AtomicBoolean(true);

    public ChangeNotifier(MediaSet set, Uri uri, Context application) {
        mMediaSet = set;
        DataManager.registerChangeNotifier(uri, this);
    }

    public ChangeNotifier(MediaSet set, Uri[] uris, Context application, boolean observeReloadMessage) {
        mMediaSet = set;
        for (Uri uri : uris) {
            DataManager.registerChangeNotifier(uri, this);
        }
        if (observeReloadMessage) {
            Bus.INSTANCE.register(this, CLASS_METHOD_FINDER);
        }
    }

    public void forceDirty() {
        mContentDirty.compareAndSet(false, true);
    }

    // Returns the dirty flag and clear it.
    public boolean isDirty() {
        return mContentDirty.compareAndSet(true, false);
    }

    // For debugging only.
    public void fakeChange() {
        onChange(false);
    }

    public void onChange(boolean selfChange) {
        if (GProperty.getRELOAD_DEBUG()) {
            GLog.d(TAG, "onChange mContentDirty.get=" + mContentDirty.get() + " mMediaSet=" + mMediaSet);
        }
        if (mContentDirty.compareAndSet(false, true)) {
            mMediaSet.notifyContentChanged();
        }
    }

    @Subscribe
    public void onChange(DataManager.ReloadMessage reloadMessage) {
        if (GProperty.getRELOAD_DEBUG()) {
            GLog.d(TAG, "onChange: change dirty.");
        }
        onChange(false);
    }
}