/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SixGridFactory
 ** Description: 6格排版下的定制化格子尺寸的计算
 **
 ** Version: 1.0
 ** Date: 2021/12/30
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2021/12/30  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.business_lib.seniorpicked.layout.gridfactory

import com.oplus.gallery.business_lib.seniorpicked.PickedDayConstant
import com.oplus.gallery.business_lib.seniorpicked.layout.data.Grid
import kotlin.math.floor

class SixGridFactory :
    BaseGridFactory(
        mutableListOf(
            PickedDayConstant.SMALL_GRID_1_1,
            PickedDayConstant.LARGE_GRID,
            PickedDayConstant.MEDIUM_GRID,
            PickedDayConstant.SMALL_GRID_2_1,
            PickedDayConstant.SMALL_GRID_2_1DOT5,
            PickedDayConstant.SMALL_GRID_2_2,
            PickedDayConstant.SMALL_GRID_3_3
        )
    ) {

    override val tag: String
        get() = "SixGridFactory"

    override fun makeGrid(type: Int, aspectRatio: Float, columnF: Float): Grid {
        return when (type) {
            PickedDayConstant.MEDIUM_GRID -> {
                Grid(
                    PickedDayConstant.MEDIUM_GRID,
                    floor(PickedDayConstant.MEDIUM_WIDTH_RATIO * columnF),
                    columnF / 2
                )
            }
            PickedDayConstant.SMALL_GRID_2_1DOT5 -> {
                Grid(
                    PickedDayConstant.SMALL_GRID_2_1DOT5,
                    PickedDayConstant.GRID_MULTIPLE_2,
                    PickedDayConstant.GRID_MULTIPLE_1DOT5
                )
            }
            PickedDayConstant.SMALL_GRID_2_2 -> {
                Grid(
                    PickedDayConstant.SMALL_GRID_2_2,
                    PickedDayConstant.GRID_MULTIPLE_2,
                    PickedDayConstant.GRID_MULTIPLE_2
                )
            }
            PickedDayConstant.SMALL_GRID_3_3 -> {
                Grid(
                    PickedDayConstant.SMALL_GRID_3_3,
                    PickedDayConstant.GRID_MULTIPLE_3,
                    PickedDayConstant.GRID_MULTIPLE_3
                )
            }
            else -> super.makeGrid(type, aspectRatio, columnF)
        }
    }
}