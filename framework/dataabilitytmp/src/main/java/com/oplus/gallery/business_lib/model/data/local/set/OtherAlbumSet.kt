/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - NotCommonlyUsedAlbum.java
 * Description:
 * Version: 1.0
 * Date: 2020/08/05
 * Author: zhongxuechang@Apps.Gallery3D
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * zhongxuechang@Apps.Gallery3D   2020/08/05    1.0     OPLUS_ARCH_EXTENDS
 *************************************************************************************************/
package com.oplus.gallery.business_lib.model.data.local.set

import android.content.Context
import com.oplus.gallery.business_lib.model.data.base.MediaObject
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.set.MediaAlbumSet
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.foundation.database.store.ConfigStore
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.framework.datatmp.R
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.Locale

open class OtherAlbumSet(context: Context, path: Path) : MediaAlbumSet(MediaObject.nextVersionNumber()) {

    companion object {
        private const val TAG = "OtherAlbumSet"
        private const val SEGMENTS_COUNT = 5
        private val WATCH_URI = arrayOf(
            GalleryStore.GalleryMedia.getLocalContentUri(),
            GalleryStore.OtherAlbumSet.getContentUri(),
            DataManager.ALBUMS_SET_RELOAD_URI,
            ConfigStore.AlbumNameMultiLang.getContentUri()
        )
        const val MAX_THUBNAIL_COUNT = 4
    }
    private var mediaSets: Array<MediaSet?> = emptyArray()
    private var locale: Locale? = null

    init {
        setPath(path)
        mApplication = context
        val segments = path.split()
        if (segments.size < SEGMENTS_COUNT) {
            GLog.w(tag, "bad path:$path")
        } else {
            mediaSets = DataManager.getMediaSetsFromString(segments[SEGMENTS_COUNT - 1])
            if (mediaSets.isEmpty()) {
                GLog.w(tag, "mediaSets is invalid, path:$path")
            }
        }
        registerNotifier(WATCH_URI)
    }

    override fun getName(): String? {
        if (locale != LocaleUtils.getLocale(ContextGetter.context)) {
            mFolderName = mApplication.resources.getString(R.string.model_other_album)
            locale = LocaleUtils.getLocale(ContextGetter.context)
        }
        return mFolderName
    }

    override fun getCoverItems(): List<MediaItem> {
        val result = mutableListOf<MediaItem>()
        for (set in mediaSets) {
            val list = set?.coverItems
            if (!list.isNullOrEmpty()) {
                result.add(list[0])
                if (result.size == MAX_THUBNAIL_COUNT) {
                    break
                }
            }
        }
        return result
    }

    override fun getSubMediaSetCount(): Int = mediaSets.size

    override fun getSubMediaSet(index: Int): MediaSet? = if (index >= mediaSets.size) null else mediaSets[index]

    override fun getSubMediaSet(start: Int, count: Int): List<MediaSet> {
        val sets = mutableListOf<MediaSet>()
        mediaSets.slice(start until (start + count)).forEach { it?.run { sets.add(this) } }
        return sets
    }

    override fun getTag(): String {
        return TAG
    }
}