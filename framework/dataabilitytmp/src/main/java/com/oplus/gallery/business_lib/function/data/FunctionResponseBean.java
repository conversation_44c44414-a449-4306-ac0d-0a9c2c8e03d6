package com.oplus.gallery.business_lib.function.data;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File        :  - FunctionResponseBean.java
 ** Description : XXXXXXXXXXXXXXXXXXXXX.
 ** Version     : 1.0
 ** Date        : 2019/10/25
 ** Author      : Yong<PERSON><PERSON>.<PERSON>@Apps.Gallery3D
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                 <data>      <version>  <desc>
 **  YongQ<PERSON>.<PERSON>@Apps.Gallery3D  2019/10/25  1.0        build this module
 ***********************************************************************/
public class FunctionResponseBean {
    @SerializedName("functionSwitchs")
    private List<FunctionListBean> mFunctionList;

    public List<FunctionListBean> getFunctionList() {
        return mFunctionList;
    }


    public static class FunctionListBean {
        @SerializedName("id")
        private int mId;
        @SerializedName("functionId")
        private String mFunctionId;
        @SerializedName("functionName")
        private String mFunctionName;
        @SerializedName("updateTime")
        private String mUpdateTime;
        @SerializedName("groupName")
        private String mGroupName;
        @SerializedName("status")
        private boolean mSwitchEnable;

        public int getId() {
            return mId;
        }

        public String getFunctionId() {
            return mFunctionId;
        }

        public String getFunctionName() {
            return mFunctionName;
        }

        public String getUpdateTime() {
            return mUpdateTime;
        }

        public String getGroupName() {
            return mGroupName;
        }

        public void setId(int id) {
            mId = id;
        }

        public void setFunctionId(String functionId) {
            mFunctionId = functionId;
        }

        public void setFunctionName(String functionName) {
            mFunctionName = functionName;
        }

        public void setUpdateTime(String updateTime) {
            mUpdateTime = updateTime;
        }

        public void setGroupName(String groupName) {
            mGroupName = groupName;
        }

        public boolean isSwitchEnable() {
            return mSwitchEnable;
        }

        public void setSwitchEnable(boolean switchEnable) {
            this.mSwitchEnable = switchEnable;
        }
    }
}
