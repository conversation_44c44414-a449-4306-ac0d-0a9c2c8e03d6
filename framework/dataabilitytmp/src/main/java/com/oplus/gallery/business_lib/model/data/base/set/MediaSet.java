/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaSet.java
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/5
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2020/8/5      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.data.base.set;

import static android.provider.BaseColumns._ID;
import static com.oplus.gallery.business_lib.model.data.base.source.LocalSource.MEDIAITEM_BATCH_FETCH_COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_NONE;
import static com.oplus.gallery.foundation.database.helper.Constants.AlbumOrderType.DISPLAY_DEFAULT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.AND;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_ID;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.FROM;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ORDER_BY;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.QUOTE;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE;

import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.oplus.gallery.business_lib.model.data.base.ChangeNotifier;
import com.oplus.gallery.business_lib.model.data.base.IMediaInputEntry;
import com.oplus.gallery.business_lib.model.data.base.MediaDetails;
import com.oplus.gallery.business_lib.model.data.base.MediaObject;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.utils.Constants;
import com.oplus.gallery.foundation.database.album.entry.CoverItem;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.database.util.SQLGrammar;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.WeakHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import androidx.annotation.Nullable;

public abstract class MediaSet extends MediaObject {
    public static final int INVALID_COVER_ID = -1;
    protected static final int INDEX_NOT_FOUND = -1;
    private static final int MAX_CALLBACK_EXECUTE_TIME = 5;
    private static final String TAG = "MediaSet";

    protected final List<MediaItem> mCoverItems = new CopyOnWriteArrayList<>();
    private final Object mListenerLock = new Object();
    private final WeakHashMap<ContentListener, Object> mListeners = new WeakHashMap<>();
    protected String mOrder = DatabaseUtils.ORDER_CLAUSE_DATE_TAKEN_ASC;
    protected List<Path> mPaths = new ArrayList<>();
    protected String mNoteName; // 相册图集名称（1.用户自定义的图集名  2. 应用定义的名字）
    protected String mCustomName; // 用户自定义的图集名
    protected String mAppFixedNoteName; // 相册图集名称（应用定义的名字）
    protected String mFolderName; // 真实的文件夹名称
    /**
     * 注意：使用该变量时，需要加线程锁，避免出现多线程安全问题。
     */
    protected boolean mIsPathsDirty = true;
    protected String[] mFolderPaths = null;
    protected int mSupportMediaType = Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_UNKNOWN;

    private ChangeNotifier mNotifier;

    private volatile boolean mIsLoading;

    /**
     * constructor
     */
    public MediaSet(long version) {
        super(version);
    }

    @Override
    public boolean isMediaItem() {
        return false;
    }

    /**
     * 此接口是为了区分当前MediaSet到底是 Album 还是 AlbumSet，并不是说是一个媒体类型数据的Album
     * @return true: 表示当前 MediaSet 是 Album。 false: 表示当前 MediaSet 是 AlbumSet
     */
    public abstract boolean isMediaAlbum();

    /**
     * 图集支持的操作，默认不支持任何操作
     * 例如：编辑状态下支持删除整个图集操作，则配置 OPERATION_SUPPORT_DELETE_ALBUM
     */
    @Override
    public long getSupportedOperations() {
        return OPERATION_SUPPORT_NONE;
    }

    @Override
    public MediaDetails getDetails(Context context) {
        MediaDetails details = super.getDetails(context);
        details.addDetail(MediaDetails.INDEX_TITLE, getName());
        return details;
    }

    public void forceDirty() {
        if (mNotifier != null) {
            mNotifier.forceDirty();
        }
    }

    protected void setLoading(boolean isLoading) {
        mIsLoading = isLoading;
    }

    public boolean isLoading() {
        return mIsLoading;
    }

    /**
     * Reload the content. Return the current data version.
     * reload() should be called in the same thread as getMediaItem(int, int) and getSubMediaSet(int).
     * 不要在onContentDirty直接执行，要另起线程，否则可能会引起死锁
     * @return dataVersion
     */
    public abstract long reload();

    /**
     * This should be called by subclasses when the content is changed.
     */
    public void notifyContentChanged() {
        synchronized (mListenerLock) {
            final ContentListener[] listenerSet = new ContentListener[mListeners.size()];
            mListeners.keySet().toArray(listenerSet);
            long time = 0;
            long costTime = 0;
            for (ContentListener listener : listenerSet) {
                if (listener != null) {
                    time = System.currentTimeMillis();
                    // 不能在回调里直接执行耗时操作，特别是reload和DataManager.getMediaObject，会造成死锁
                    listener.onContentDirty();
                    costTime = System.currentTimeMillis() - time;
                    if (costTime > MAX_CALLBACK_EXECUTE_TIME) {
                        GLog.w(TAG, "notifyContentChanged cost so many time:" + costTime
                                + ", mediaSet:" + this + ", listener:" + listener);
                    }
                }
            }
        }
    }

    /**
     * NOTE: The MediaSet only keeps a weak reference to the listener.
     * The listener is automatically removed when there is no other reference to the listener.
     *
     * @param listener
     */
    public void registerContentListener(ContentListener listener) {
        synchronized (mListenerLock) {
            if (mListeners.containsKey(listener)) {
                GLog.d(TAG, "addContentListener, already add this listener");
                return;
            }
            mListeners.put(listener, null);
        }
    }

    public boolean isContentListenerEmpty() {
        synchronized (mListenerLock) {
            return mListeners.isEmpty();
        }
    }

    /**
     * NOTE: The MediaSet only keeps a weak reference to the listener.
     * The listener is automatically removed when there is no other reference to the listener.
     *
     * @param listener
     */
    public void unregisterContentListener(ContentListener listener) {
        synchronized (mListenerLock) {
            if (!mListeners.containsKey(listener)) {
                GLog.d(TAG, "removeContentListener, listener do not exist");
                return;
            }
            mListeners.remove(listener);
        }
    }

    public void setInputEntry(IMediaInputEntry inputEntry) {
        throw new UnsupportedOperationException();
    }

    public IMediaInputEntry getInputEntry() {
        return null;
    }

    /**
     * @return bucket name
     */
    public String getName() {
        return mFolderName;
    }

    public String getDirName() {
        return mFolderName;
    }

    public void setName(String name) {
        mFolderName = name;
    }

    /**
     * Folder note name from RUS-list which made a renaming for local album.
     *
     * @return if getNoteName is null, we use getName instead.
     */
    public String getNoteName() {
        return mNoteName;
    }

    public void setNoteName(String noteName) {
        mNoteName = noteName;
    }

    public String getCustomName() {
        return mCustomName;
    }

    public void setCustomName(String name) {
        mCustomName = name;
    }

    public List<Integer> getBucketIds() {
        return new ArrayList<>();
    }

    /**
     * @return sub items count, include sub-MediaItem and sub-MediaSet.
     */
    public int getCount() {
        return 0;
    }

    public Bundle getSpecifiedCount(Bundle inBundle) {
        return new Bundle();
    }

    /**
     * sub MediaSets in MediaAlbumSet.
     *
     * @param start
     * @param count
     * @return MediaAlbumSet: maybe contains both items and sets, but only need return sets.
     */
    public List<MediaSet> getSubMediaSet(int start, int count) {
        return new ArrayList<>();
    }

    public MediaSet getSubMediaSet(int index) {
        throw new IndexOutOfBoundsException();
    }

    public int getSubMediaSetCount() {
        return 0;
    }

    /**
     * sub MediaItem in MediaAlbum or MediaAlbumSet.
     * MediaAlbumSet: maybe contains both items and sets, but only need return items.
     *
     * @param start
     * @param count
     * @return List<MediaItem>
     */
    @NotNull
    public List<MediaItem> getSubMediaItem(int start, int count) {
        return new ArrayList<>();
    }

    @NotNull
    public MediaItem getSubMediaItem(int index) {
        throw new IndexOutOfBoundsException();
    }

    /**
     * 返回需要进行同步的媒体库uris，及时将媒体库数据同步到相册local_media
     * 加到基类理论上所有图集都可以支持此机制，不仅仅限于外部App传入的参数
     */
    public Uri[] getNeedSyncUris(int start, int count) {
        return null;
    }

    /**
     * 根据当前位置，往前后offset查找index
     *
     * 即：根据给定的offset，查找对应position前后的数据，如果能找到就直接返回，加快index的查找速度
     */
    public int getIndexOfItemOffset(int position, Path path, int offset) {
        int start = Math.max(0, position - offset);
        int end = position + offset;
        int count = end - start;
        List<MediaItem> list = getSubMediaItem(start, count);
        int index = getIndexOf(path, list);
        if (index != INDEX_NOT_FOUND) {
            return start + index;
        }
        return INDEX_NOT_FOUND;
    }

    public int getIndexOfItem(Path path, int hint) {
        /*
        hint < 0 is handled below
        first, try to find it around the hint
        */
        int start = Math.max(0, hint - MEDIAITEM_BATCH_FETCH_COUNT / 2);
        List<MediaItem> list = getSubMediaItem(start, MEDIAITEM_BATCH_FETCH_COUNT);
        int index = getIndexOf(path, list);
        if (index != INDEX_NOT_FOUND) {
            return start + index;
        }

        // try to find it globally
        start = (start == 0) ? MEDIAITEM_BATCH_FETCH_COUNT : 0;
        list = getSubMediaItem(start, MEDIAITEM_BATCH_FETCH_COUNT);
        while (true) {
            index = getIndexOf(path, list);
            if (index != INDEX_NOT_FOUND) {
                return start + index;
            }
            if (list.size() < MEDIAITEM_BATCH_FETCH_COUNT) {
                return INDEX_NOT_FOUND;
            }
            start += MEDIAITEM_BATCH_FETCH_COUNT;
            list = getSubMediaItem(start, MEDIAITEM_BATCH_FETCH_COUNT);
        }
    }

    private int getIndexOf(Path path, List<MediaItem> list) {
        if ((list == null) || list.isEmpty()) {
            return INDEX_NOT_FOUND;
        }
        for (int i = 0, n = list.size(); i < n; ++i) {
            if (list.get(i).getPath() == path) {
                return i;
            }
        }
        return INDEX_NOT_FOUND;
    }

    /**
     * 相比于getIndexOfItem，此方法性能要高很多. 47000张，此方法耗时稳定在100ms左右，getIndexOfItem耗时大概要2-3秒。
     * @param path 路径对象
     * @param hint 索引提示
     * @return 返回索引值
     */
    public int getIndexOfItemHighPerformance(Path path, int hint) {
        int index = INDEX_NOT_FOUND;
        try {
            final String order = getOrder();
            if (order.equals(DatabaseUtils.ORDER_CLAUSE_DATE_MODIFIED_ASC)) {
                index = getIndexOfItemWhenFixOrder(path, true);
                return index;
            }
            if (order.equals(DatabaseUtils.ORDER_CLAUSE_DATE_MODIFIED_DESC)) {
                index = getIndexOfItemWhenFixOrder(path, false);
                return index;
            }
            index = getIndexOfItemByPath(path, -1, -1);
            return index;
        } catch (Exception e) {
            GLog.e(TAG, "getIndexOfItem error:", e);
        }
        index = getIndexOfItem(path, hint);
        return index;
    }

    /**
     * 设置图集详情图片的排序方法
     * @param sortOrder 排序方式 {@link com.oplus.gallery.foundation.database.helper.Constants.OrderType}
     * @return 设置成功与否
     */
    public boolean setSortOrder(int sortOrder) {
        return false;
    }

    /**
     * 获取图集详情的排序方式
     * @return {@link com.oplus.gallery.foundation.database.helper.Constants.OrderType}
     */
    public int getSortOrder() {
        return DISPLAY_DEFAULT;
    }

    /**
     * 针对固定已知的排序进行优化查询
     */
    private int getIndexOfItemWhenFixOrder(Path path, boolean positive) {
        final long startTime = System.currentTimeMillis();
        long dateModified = -1;
        try (Cursor cursor = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(new String[] {LocalColumns.DATE_MODIFIED})
                .setWhere(_ID + EQUAL + path.getSuffix() + AND + DatabaseUtils.getDataValidWhere())
                .setConvert(new CursorConvert())
                .setLimit("1")
                .build()
                .exec()) {
            if (cursor == null) {
                GLog.e(TAG, "getIndexOfItemWhenFixOrder, cursor is null!");
                return INDEX_NOT_FOUND;
            }
            if (cursor.moveToFirst()) {
                dateModified = cursor.getLong(0);
            } else {
                GLog.d(TAG, "getIndexOfItemWhenFixOrder, query dateModified, removed!");
                return INDEX_NOT_FOUND;
            }
        }
        final String galleryId = path.getSuffix();
        final StringBuilder sqlBuilder = new StringBuilder(SELECT + COUNT_ID + FROM + GalleryStore.GalleryMedia.TAB + WHERE)
                .append(DatabaseUtils.getOrderCompareWhere(LocalColumns.DATE_MODIFIED, dateModified, _ID, galleryId, positive))
                .append(AND).append(DatabaseUtils.getDataValidWhere())
                .append(AND).append(DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB));
        final String where = getWhere();
        if (!TextUtils.isEmpty(where)) {
            sqlBuilder.append(AND).append(LEFT_BRACKETS).append(where).append(RIGHT_BRACKETS);
        }
        try (Cursor cursor = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(new CursorConvert())
                .setQuerySql(sqlBuilder.toString())
                .build().exec()) {
            if (cursor == null) {
                GLog.e(TAG, "getIndexOfItemWhenFixOrder query fail");
                return INDEX_NOT_FOUND;
            }
            if (!cursor.moveToFirst()) {
                GLog.e(TAG, "getIndexOfItemWhenFixOrder moveToFirst fail");
                return INDEX_NOT_FOUND;
            }
            final int index = cursor.getInt(0);
            GLog.d(TAG, "getIndexOfItemWhenFixOrder index:" + index + ", cost:" + GLog.getTime(startTime));
            return index;
        }
    }

    protected @Nullable String getWhere() {
        return null;
    }

    protected String getOrder() {
        return mOrder;
    }

    /**
     * select group_concat(_id, ',') from (select _id from local_media where ... order by ... limit..)
     */
    protected String createQueryMediaIdsSortSQL(int start, int count) {
        final StringBuilder sqlBuilder = new StringBuilder("select group_concat(");
        sqlBuilder.append(_ID + COMMA + QUOTE + COMMA + QUOTE)
                .append(") from (select ")
                .append(_ID)
                .append(FROM)
                .append(GalleryStore.GalleryMedia.TAB)
                .append(WHERE)
                .append(DatabaseUtils.getDataValidWhere())
                .append(AND)
                .append(DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB));
        final String where = getWhere();
        if (!TextUtils.isEmpty(where)) {
            sqlBuilder.append(AND).append(LEFT_BRACKETS).append(where).append(RIGHT_BRACKETS);
        }
        final String order = getOrder();
        if (!TextUtils.isEmpty(order)) {
            sqlBuilder.append(ORDER_BY).append(order);
        }
        if ((start >= 0) && (count > 0)) {
            sqlBuilder.append(SQLGrammar.LIMIT).append(start).append(SQLGrammar.COMMA).append(count);
        }
        sqlBuilder.append(RIGHT_BRACKETS);
        return sqlBuilder.toString();
    }

    protected int getIndexOfItemByPath(Path path, int start, int count) {
        int index = INDEX_NOT_FOUND;
        long queryCostTime = 0;
        long startTime = System.currentTimeMillis();
        try (Cursor cursor = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setConvert(new CursorConvert())
                .setQuerySql(createQueryMediaIdsSortSQL(start, count))
                .setSqlArgs(null)
                .build().exec()) {
            if (cursor == null) {
                GLog.e(TAG, "getIndexOfItemByPath query fail");
                return INDEX_NOT_FOUND;
            }
            if (!cursor.moveToFirst()) {
                GLog.e(TAG, "getIndexOfItemByPath moveToFirst fail");
                return INDEX_NOT_FOUND;
            }
            final String galleryIdsStr = cursor.getString(0);
            queryCostTime = GLog.getTime(startTime);
            if (TextUtils.isEmpty(galleryIdsStr)) {
                GLog.e(TAG, "getIndexOfItemByPath mediaIdsStr isEmpty");
                return INDEX_NOT_FOUND;
            }
            final String[] galleryIds = galleryIdsStr.split(COMMA);
            if (galleryIds.length <= 0) {
                GLog.e(TAG, "getIndexOfItemByPath mediaIds.len:" + galleryIds.length);
                return INDEX_NOT_FOUND;
            }
            final String galleryId = path.getSuffix();
            if (GProperty.getDEBUG_ALBUM()) {
                GLog.d(TAG, "getIndexOfItemByPath mediaId:" + Arrays.toString(galleryIds) + ", mediaIds len:" + galleryIds.length);
            }

            // 很多场景传0或者最后一位，优先判断
            if (galleryId.equals(galleryIds[0])) {
                index = 0;
            }
            final int lastIndex = galleryIds.length - 1;
            if ((index == INDEX_NOT_FOUND) && (lastIndex != 0) && galleryId.equals(galleryIds[lastIndex])) {
                index = lastIndex;
            }
            if (index == INDEX_NOT_FOUND) {
                for (int i = 1; i < lastIndex; i++) {
                    if (galleryId.equals(galleryIds[i])) {
                        index = i;
                        break;
                    }
                }
            }
            if (GProperty.DEBUG) {
                GLog.d(TAG, "getIndexOfItemByPath, index:" + index
                        + ", queryCost:" + queryCostTime
                        + ", allCostTime:" + GLog.getTime(startTime));
            }
        }
        return index;
    }

    protected int getSubMediaItemCount(boolean requireSpecifiedCount) {
        return 0;
    }

    /**
     * All item in the current directory and its subdirectories.
     *
     * @return TotalMediaItemCount
     */
    public int getTotalMediaItemCount() {
        int total = getSubMediaItemCount(false);
        for (int i = 0, n = getSubMediaSetCount(); i < n; i++) {
            MediaSet set = getSubMediaSet(i);
            if (set != null) {
                total += set.getTotalMediaItemCount();
            }
        }
        return total;
    }

    public boolean isCoverItemsEmpty() {
        return mCoverItems.isEmpty();
    }

    public boolean setCoverItems(@NotNull List<CoverItem> covers) {
        return false;
    }

    public List<MediaItem> getCoverItems() {
        List<MediaItem> items = getSubMediaItem(getCoverItemStartIndex(), 1);
        if (!items.isEmpty()) {
            return new ArrayList<>(items);
        }
        for (int i = 0, n = getSubMediaSetCount(); i < n; i++) {
            MediaSet set = getSubMediaSet(i);
            if (set == null) {
                continue;
            }
            items = set.getCoverItems();
            if (items != null) {
                return new ArrayList<>(items);
            }
        }
        return new ArrayList<>();
    }

    protected int getCoverItemStartIndex() {
        final int count = getCount();
        if ((count <= 0) || (getInputEntry() == null)) {
            return 0;
        }
        String order = getInputEntry().getOrder();
        if (TextUtils.isEmpty(order)) {
            return 0;
        }
        order = order.toUpperCase();
        // 封面默认选用最先显示的照片，所以只要DESC排前面就使用第一张，否则使用最后一张
        final int descStart = order.indexOf("DESC");
        final int ascStart = order.indexOf("ASC");
        return ((ascStart > INDEX_NOT_FOUND) && ((descStart == INDEX_NOT_FOUND) || (ascStart < descStart)))
                ? (count - 1) : 0;
    }

    /**
     * 刷新封面信息(封面缓存有更新时)
     * 分为二种情况: 1、更改排序方式   2、恢复成默认封面
     *
     * @param forceRefreshCover 是否强制刷新封面信息(恢复默认封面场景需要强制)
     * @return 刷新封面缓存是否成功
     */
    public boolean refreshCover(boolean forceRefreshCover) {
        return false;
    }

    /**
     * 获取回忆专用的 items 会在查询时加一些过滤条件，子类去覆写
     *
     * @return 回忆 item 的数量
     */
    public int getMemoriesItemCount() {
        return getCount();
    }

    /**
     * 获取回忆专用的 items 会在查询时加一些过滤条件，子类去覆写
     *
     * @return 回忆 item 的列表
     */
    public List<MediaItem> getMemoriesMediaItem(int start, int count) {
        return getSubMediaItem(start, count);
    }

    public int getSortId() {
        return 0;
    }

    public boolean isVirtualAlbum() {
        return false;
    }

    /**
     * 是否是自建图集
     * @return true：自建图集，false：非自建图集
     */
    public boolean isSelfAlbum() {
        return false;
    }

    public String[] getFolderPaths() {
        return mFolderPaths;
    }

    public int getSupportMediaType() {
        return mSupportMediaType;
    }

    protected void registerNotifier(Uri uri) {
        mNotifier = new ChangeNotifier(this, uri, mApplication);
    }

    protected void registerNotifier(Uri[] uris) {
        mNotifier = new ChangeNotifier(this, uris, mApplication, false);
    }

    protected void registerNotifier(Uri[] uris, boolean observeReloadMessage) {
        mNotifier = new ChangeNotifier(this, uris, mApplication, observeReloadMessage);
    }

    protected boolean isDirty() {
        return (mNotifier != null) && mNotifier.isDirty();
    }

    public synchronized List<Path> getAllPath() {
        if (mIsPathsDirty) {
            mIsPathsDirty = false;
            mPaths = getPaths(0, getCount());
        }
        if (null == mPaths) {
            GLog.w(TAG, LogFlag.DL, () -> "getAllPath mPaths is null.");
            return new ArrayList<>();
        }
        return new ArrayList<>(mPaths);
    }

    public List<Path> getPaths(int start, int count) {
        return new ArrayList<>();
    }

    /**
     * 内容是否是异步加载出来的？默认的都不是,但是像mtp这种就是异步加载内部内容
     */
    public boolean isAlbumContentLoadAsync() {
        return false;
    }

    @NotNull
    public String getTag() {
        return TAG;
    }

    /**
     * 当前复制到targetMediaSet
     * 因为可能需要基于旧的mediaSet生成新的mediaSet，那么就不只是new个对象，还需要拷贝很多重要的数据，否则会导致查询出错
     * 比如在个性化筛选时，新的path对应新的mediaSet，也需要旧mediaSet的重要数据确保mediaSet的功能完整，
     * 比如标签/地点等图集，在set中有赋值inputEntry，如果不拷贝这个，会导致最终查询的语句没有足够的条件，导致出错
     * @param targetMediaSet
     */
    public void copyTo(MediaSet targetMediaSet) {
        // 复制inputEntry，因为很多album的真实的where语句都在entry中拼接出来，这里面有很多重要的外部传入的参数
        if ((getInputEntry() != null)) {
            // copy方法传null，是为了保证从子类到父类的完整深拷贝。null就表示子类需要创建出自己需要的entry对象，父类基于此可以直接复制内容
            targetMediaSet.setInputEntry(getInputEntry().copy(null));
        }
        // 复制ContentListener，为了确保在数据变化后，可以正确的通知到上层进行刷新操作，否则会存在数据变化了页面没有变化的bug
        if (!mListeners.isEmpty()) {
            targetMediaSet.mListeners.clear();
            targetMediaSet.mListeners.putAll(mListeners);
        }
        // 复制真实的文件名称，为了确保在复制到/移动到功能时，可能需要这个来决定最终把数据修改到正确的位置。比如自建图集详情中，点击toolbar的添加按钮，选完数据后依赖这个字段
        if (mFolderName != null) {
            targetMediaSet.setName(mFolderName);
        }
    }
}