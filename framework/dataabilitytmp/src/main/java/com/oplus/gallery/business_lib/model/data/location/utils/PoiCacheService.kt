/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - PoiCacheService
 ** Description:
 ** Version: 1.0
 ** Date : 2025/4/7
 ** Author: qiupeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** 80398228 2025/4/7 1.0 build this module
 ****************************************************************/
package com.oplus.gallery.business_lib.model.data.location.utils

import android.database.Cursor
import android.database.SQLException
import android.provider.BaseColumns
import com.oplus.gallery.business_lib.model.data.location.api.GpsImg
import com.oplus.gallery.business_lib.model.data.location.api.PoiDbInfo
import com.oplus.gallery.business_lib.model.data.location.impl.GpsLocation
import com.oplus.gallery.business_lib.model.data.location.impl.PoiGridInfo
import com.oplus.gallery.business_lib.model.data.location.impl.PoiRouteInfo
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GLog.e
import com.oplus.gallery.foundation.util.debug.GLog.v
import java.util.Locale
import java.util.function.Consumer

/**
 * Poi存储服务
 * 1.转换Poi信息为表数据结构
 * 2.转换Poi数据库表查询信息
 */
object PoiCacheService {
    private const val TAG = "PoiCacheService"
    private val mPoiDBHelper: PoiDBHelper = PoiDBHelper.getInstance()

    /**
     * 批量写入Poi_route表
     * @param resolvedLocations
     */
    fun writePoiBatchByGpsLocation(resolvedLocations: List<GpsLocation>) {
        val startTime = System.currentTimeMillis()
        for (location in resolvedLocations) {
            val latitude = location.latitude
            val longitude = location.longitude
            val gpsKey = location.gpsKey
            val address = location.address
            if ((address.getPoiName() == null) || (address.getPoiName().isNullOrEmpty())) {
                continue
            }
            val radius = PoiConfigHelper.getPoiReverseDefaultRadius()
            try {
                val countryName = address.getCountry()
                val province = address.getProvince()
                val city = address.getCity(false)
                val district = address.getDistrict(false)
                val street = address.getStreet(false)
                val poiName = address.getPoiName()
                val fullAddress = String.format(
                    "%s, %s, %s, %s, %s, %s",
                    if ((countryName == null)) "" else countryName,
                    if ((province == null)) "" else province,
                    if ((city == null)) "" else city,
                    if ((district == null)) "" else district,
                    if ((street == null)) "" else street,
                    if ((poiName == null)) "" else poiName
                )
                mPoiDBHelper.insertPoiIfNoAddress(latitude, longitude, gpsKey, address.getPoiName(), fullAddress, radius)
            } catch (exp: SQLException) {
                e(TAG, "failed to writePoiBatchByGpsLocation, SQLException is  ", exp)
            }
        }
        GLog.d(
            TAG, String.format(
                Locale.ENGLISH, "[writePoiBatchByGpsLocation] Batch write %d records to PoiCache, duration: %dms",
                resolvedLocations.size, (System.currentTimeMillis() - startTime)
            )
        )
    }

    fun collectUnresolvedPoiGps(): ArrayList<GpsImg>? {
        val stringBuilder = StringBuilder()
        stringBuilder.append(GeoCacheService.getMediaProviderQueryWhereClauseWithLocation())
            .append(" AND ")
            .append(GeoColumns.GPS_KEY)
            .append(" NOT IN ")
            .append("(")
            .append("SELECT ").append(GeoColumns.GPS_KEY).append(" FROM ").append(GalleryStore.PoiGridRoute.TAB)
            .append(")")
        val queryReq = QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(
                arrayOf(
                    BaseColumns._ID,
                    GeoColumns.GPS_KEY,
                    LocalColumns.LATITUDE,
                    LocalColumns.LONGITUDE
                )
            )
            .setWhere(stringBuilder.toString())
            .setWhereArgs(null)
            .setOrderBy(GeoCacheService.ORDER_CLAUSE)
            .setConvert(CursorConvert())
            .build()
        try {
            DataAccess.getAccess().query(queryReq).use { fileCursor ->
                if ((fileCursor != null) && (fileCursor.count > 0)) {
                    val indexId = fileCursor.getColumnIndex(BaseColumns._ID)
                    val indexGpsKey = fileCursor.getColumnIndex(GeoColumns.GPS_KEY)
                    val indexLatitude = fileCursor.getColumnIndex(LocalColumns.LATITUDE)
                    val indexLongitude = fileCursor.getColumnIndex(LocalColumns.LONGITUDE)
                    val gpsArray = ArrayList<GpsImg>()
                    while (fileCursor.moveToNext()) {
                        val gpsKey = fileCursor.getLong(indexGpsKey)
                        val gpsImg = GpsImg(
                            gpsKey,
                            fileCursor.getDouble(indexLatitude),
                            fileCursor.getDouble(indexLongitude),
                            fileCursor.getInt(indexId)
                        )
                        gpsArray.add(gpsImg)
                    }
                    return gpsArray
                }
            }
        } catch (exp: Throwable) {
            e(TAG, "Collecting unresolved gps failed during querying Files table! ", exp)
        }
        return null
    }

    /**
     * 获取poi_route表 name
     * @return
     */
    fun getAllPoiAddress(): List<String> {
        return mPoiDBHelper.getAllAddress()
    }

    /**
     * 获取poi_route表：
     * LEVEL_ID，LEVEL_POI_NAME，LEVEL_LAT，LEVEL_LNG，LEVEL_RADIUS，LEVEL_GPS_KEY，LEVEL_POI_HASH
     * @return
     */
    fun getAllPoiInfo(): List<PoiDbInfo> {
        return mPoiDBHelper.getAllPoiAddress()
    }

    fun getAllPoiIds(poiHash: List<String?>?): List<Int> {
        return mPoiDBHelper.getPoiIds(poiHash)
    }

    /**
     * 更新poi_route元信息表
     * @param poiHash
     * @param lat
     * @param lng
     * @param radius
     */
    fun updatePoiBatch(poiHash: Long, lat: Double, lng: Double, radius: Int) {
        mPoiDBHelper.updatePoiDirectly(poiHash, lat, lng, radius)
    }

    /**
     * 批量写入Poi_route表
     * @param resolvedLocations
     */
    fun writePoiBatch(resolvedLocations: List<PoiRouteInfo>) {
        val startTime = System.currentTimeMillis()
        for (location in resolvedLocations) {
            val latitude = location.latitude
            val longitude = location.longitude
            val gpsKey = location.gpsKey
            val address = location.fullAddress
            val radius = location.radius
            val alias = location.alias
            val poiName = location.poiName
            try {
                var aliasStr = if (alias.isNullOrEmpty()) {
                    null
                } else {
                    alias.joinToString(",").trim()
                }
                if (aliasStr?.isEmpty() == true) {
                    aliasStr = null
                }
                mPoiDBHelper.insertPoiDirectly(latitude, longitude, gpsKey, poiName, address, radius, aliasStr)
            } catch (exp: SQLException) {
                e(TAG, "failed to writePoiBatch, SQLException is  ", exp)
            }
        }
        v(
            TAG, String.format(
                Locale.ENGLISH, "[writePoiBatch] Batch write %d records to PoiCache, duration: %dms",
                resolvedLocations.size, (System.currentTimeMillis() - startTime)
            )
        )
    }

    /**
     * 批量写入poi_grid_route表
     * @param resolvedPoiGridInfos
     */
    fun writePoiGridBatch(resolvedPoiGridInfos: List<PoiGridInfo>) {
        for (info in resolvedPoiGridInfos) {
            val galleryId = info.galleryId
            val gpsKey = info.gpsKey
            val poiIds = info.poiIdList
            poiIds.forEach(Consumer { poiId: Int ->
                try {
                    mPoiDBHelper.insetPoiGridDirectly(galleryId, gpsKey, poiId)
                } catch (exp: SQLException) {
                    e(TAG, "failed to writePoiGridBatch, SQLException is  ", exp)
                }
            })
        }
    }
}