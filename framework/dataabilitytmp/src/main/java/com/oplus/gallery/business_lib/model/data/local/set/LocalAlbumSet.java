/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocalAlbumSet.java
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/4
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2020/8/4      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.data.local.set;

import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_CAMERA;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_CARD_CASE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ONLY_LOCAL_FILE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_IMAGE;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_MEMORIES;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_SELF_ALBUM;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_VIDEO;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_VIDEO_CAMERA;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_WIDGET;

import android.content.Context;
import android.net.Uri;

import com.oplus.gallery.business_lib.model.data.base.IMediaInputEntry;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketHelper;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketHelper.BucketEntry;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.set.MediaAlbumSet;
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local;
import com.oplus.gallery.business_lib.model.data.base.utils.MediaSetUtils;
import com.oplus.gallery.business_lib.model.data.local.utils.DBCoverUtils;
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils;
import com.oplus.gallery.foundation.database.album.entry.CoverItem;
import com.oplus.gallery.foundation.database.store.CacheStore;
import com.oplus.gallery.foundation.database.store.ConfigStore;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.taskscheduling.ext.CoroutineScopeExtKt;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.framework.datatmp.R;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.thread.Future;
import com.oplus.gallery.standard_lib.thread.FutureListener;
import com.oplus.gallery.standard_lib.thread.ThreadPool;
import com.oplus.gallery.standard_lib.thread.ThreadPool.JobContext;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;

import kotlin.Pair;
import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Deferred;
import kotlinx.coroutines.Dispatchers;

public class LocalAlbumSet extends MediaAlbumSet implements FutureListener<List<MediaSet>> {

    private static final String TAG = "LocalAlbumSet";
    private static final Uri[] WATCH_URI = new Uri[]{
            GalleryStore.GalleryMedia.getLocalContentUri(),
            GalleryStore.OtherAlbumSet.getContentUri(),
            DataManager.ALBUMS_SET_RELOAD_URI,
            DataManager.TIME_NODE_ALBUM_RELOAD_URI,
            DataManager.CARD_CASE_RELOAD_URI,
            ConfigStore.AlbumNameMultiLang.getContentUri(),
            CacheStore.Album.getContentUri()
    };
    private static final IMediaInputEntry DEFAULT_ENTRY = new LocalAlbumSetEntry(MEDIA_TYPE_SUPPORT_ALL);
    private static final Comparator<BucketEntry> BUCKET_ENTRY_COMPARATOR = new DateComparator();
    private final String mName;
    private final boolean mIsAbilityOpen;
    private volatile List<MediaSet> mNormalSets = new ArrayList<>();
    private Deferred<List<MediaSet>> mLoadTask;
    private LocalAlbumSetEntry mEntry;

    public static class DateComparator implements Comparator<BucketEntry>, Serializable {
        public int compare(BucketEntry item1, BucketEntry item2) {
            if ((item1.mOrderTime < 0) && (item2.mOrderTime < 0)) {
                return 0;
            }
            if (item2.mOrderTime < 0) {
                return -1;
            }
            if (item1.mOrderTime < 0) {
                return 1;
            }
            return Long.compare(item2.mOrderTime, item1.mOrderTime);
        }
    }

    public LocalAlbumSet(Path path, Context application) {
        this(path, application, false);
    }

    public LocalAlbumSet(Path path, Context application, boolean isAbilityOpen) {
        super(nextVersionNumber());
        mApplication = application;
        registerNotifier(WATCH_URI, true);
        mName = application.getResources().getString(R.string.common_set_label_local_albums);
        setPath(path);
        setInputEntry(DEFAULT_ENTRY);
        mIsAbilityOpen = isAbilityOpen;
    }

    @Override
    public void setInputEntry(IMediaInputEntry inputEntry) {
        mEntry = (LocalAlbumSetEntry) inputEntry;
    }

    @Override
    public MediaSet getSubMediaSet(int index) {
        final List<MediaSet> mediaSets = mNormalSets;
        if (index >= mediaSets.size()) {
            return null;
        }
        return mediaSets.get(index);
    }

    @Override
    public List<MediaSet> getSubMediaSet(int start, int count) {
        final List<MediaSet> mediaSets = mNormalSets;
        final int size = mediaSets.size();
        if ((start < 0) || (count < 1) || ((start + count) > size)) {
            GLog.w(TAG, "getSubMediaSet exception, start:" + start + "count:" + count);
            return null;
        }
        return new ArrayList<>(mediaSets.subList(start, start + count));
    }

    @Override
    public int getSubMediaSetCount() {
        return mNormalSets.size();
    }

    @Override
    public String getName() {
        return mName;
    }

    // synchronized on this function for
    // 1. Prevent calling reload() concurrently.
    // 2. Prevent calling onFutureDone() and reload() concurrently
    @Override
    public synchronized long reload() {
        // "|" is used instead of "||" because we want to clear both flags.
        boolean isDirty = isDirty();
        GLog.d(TAG, "reload isDirty: " + isDirty);
        if (isDirty) {
            if (mLoadTask != null) {
                mLoadTask.cancel(null);
            }
            setLoading(true);
            mLoadTask = CoroutineScopeExtKt.async(AppScope.INSTANCE, (CoroutineContext) Dispatchers.getIO(),
                    CoroutineStart.DEFAULT, new AlbumsLoader(), this);
        }
        return mDataVersion;
    }

    private boolean addCommonAlbums(JobContext jc, BucketEntry[] entries, List<MediaSet> albums) {
        for (BucketEntry entry : entries) {
            if (jc.isCancelled()) {
                return false;
            }
            final MediaSet mediaSet = getLocalAlbum(entry);
            if (mediaSet == null) {
                continue;
            }
            albums.add(mediaSet);
        }
        return true;
    }

    private void addOtherAlbumSet(int supportMediaType, List<MediaSet> sets, List<MediaSet> hideAlbums) {
        if (hideAlbums.isEmpty()) {
            return;
        }
        List<String> pathList = new ArrayList<>(hideAlbums.size());
        for (MediaSet mediaSet : hideAlbums) {
            pathList.add(mediaSet.getPath().toString());
        }
        Path path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, Local.PATH_SET_OTHER_ANY)
                .appendParams(pathList);
        MediaSet set = DataManager.getMediaSet(path);
        if (set == null) {
            GLog.w(TAG, "addOtherAlbumSet mediaSet is null");
            return;
        }
        set.reload();
        sets.add(set);
    }

    private MediaSet getLocalAlbum(BucketEntry entry) {
        final int type = entry.mType;
        final int supportMediaType = mEntry.getMediaTypeSupport();
        MediaSet mediaSet = null;
        switch (type) {
            case MEDIA_TYPE_SUPPORT_CAMERA:
                if ((supportMediaType & MEDIA_TYPE_SUPPORT_ALL) == MEDIA_TYPE_SUPPORT_ALL) {
                    Path path = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_CAMERA_ANY : Local.PATH_ALBUM_CAMERA_ANY;
                    path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, path);
                    mediaSet = createLocalAlbum(path);
                } else if ((supportMediaType & MEDIA_TYPE_SUPPORT_IMAGE) == MEDIA_TYPE_SUPPORT_IMAGE) {
                    Path path = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_CAMERA_IMAGE : Local.PATH_ALBUM_CAMERA_IMAGE;
                    path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, path);
                    mediaSet = createLocalAlbum(path);
                } else if ((supportMediaType & (MEDIA_TYPE_SUPPORT_VIDEO | MEDIA_TYPE_SUPPORT_VIDEO_CAMERA)) != 0) {
                    Path path = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_CAMERA_VIDEO : Local.PATH_ALBUM_CAMERA_VIDEO;
                    path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, path);
                    mediaSet = createLocalAlbum(path);
                } else if (supportMediaType == MEDIA_TYPE_SUPPORT_MEMORIES) {
                    Path path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, Local.PATH_ALBUM_CAMERA_MEMORIES);
                    mediaSet = createLocalAlbum(path);
                } else if (supportMediaType == MEDIA_TYPE_SUPPORT_WIDGET) {
                    Path path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, Local.PATH_ALBUM_CAMERA_WIDGET);
                    mediaSet = createLocalAlbum(path);
                } else {
                    GLog.e(TAG, "getLocalAlbum type error:" + type);
                    Path path = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_CAMERA_ANY : Local.PATH_ALBUM_CAMERA_ANY;
                    path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, path);
                    mediaSet = createLocalAlbum(path);
                }
                break;
            case MEDIA_TYPE_SUPPORT_CARD_CASE:
                if ((supportMediaType & MEDIA_TYPE_SUPPORT_VIDEO) == MEDIA_TYPE_SUPPORT_VIDEO) {
                    Path path = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_CARD_CASE_ANY : Local.PATH_ALBUM_CARD_CASE_ANY;
                    path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, path);
                    mediaSet = createLocalAlbum(path);
                } else {
                    Path path = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_CARD_CASE_IMAGE : Local.PATH_ALBUM_CARD_CASE_IMAGE;
                    path = TypeFilterUtils.checkPathByMediaTypeSupport(supportMediaType, path);
                    mediaSet = createLocalAlbum(path);
                }
                break;
            case MEDIA_TYPE_SUPPORT_SELF_ALBUM:
            case MEDIA_TYPE_SUPPORT_ALL:
                mediaSet = createOrUpdateLocalAlbum(supportMediaType, entry);
                break;
            case MEDIA_TYPE_SUPPORT_IMAGE:
                mediaSet = createOrUpdateLocalAlbum(buildNewMediaType(MEDIA_TYPE_SUPPORT_IMAGE, supportMediaType), entry);
                break;
            case MEDIA_TYPE_SUPPORT_VIDEO:
            case MEDIA_TYPE_SUPPORT_VIDEO_CAMERA:
                mediaSet = createOrUpdateLocalAlbum(buildNewMediaType(MEDIA_TYPE_SUPPORT_VIDEO, supportMediaType), entry);
                break;
            default:
                throw new IllegalArgumentException("getLocalAlbum exception, type:" + type);
        }
        return mediaSet;
    }

    private int buildNewMediaType(int mediaType, int mediaTypeSupport) {
        // 后面可以继续添加其它条件
        return mediaType | (mediaTypeSupport & MEDIA_TYPE_SUPPORT_ONLY_LOCAL_FILE);
    }

    private LocalAlbum createOrUpdateLocalAlbum(int mediaTypeSupport, BucketEntry entry) {
        Path parentPath = null;
        if ((mediaTypeSupport & MEDIA_TYPE_SUPPORT_ALL) == MEDIA_TYPE_SUPPORT_IMAGE) {
            parentPath = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_IMAGE : Local.PATH_ALBUM_ANY_IMAGE;
            parentPath = TypeFilterUtils.checkPathByMediaTypeSupport(mediaTypeSupport, parentPath);
        } else if ((mediaTypeSupport & MEDIA_TYPE_SUPPORT_ALL) == MEDIA_TYPE_SUPPORT_VIDEO) {
            parentPath = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_VIDEO : Local.PATH_ALBUM_ANY_VIDEO;
            parentPath = TypeFilterUtils.checkPathByMediaTypeSupport(mediaTypeSupport, parentPath);
        } else if ((mediaTypeSupport & MEDIA_TYPE_SUPPORT_MEMORIES) != 0) {
            // 能力开放、创建回忆业务不相关，这里只有创建回忆才能走到
            parentPath = TypeFilterUtils.checkPathByMediaTypeSupport(mediaTypeSupport, Local.PATH_ALBUM_CREATE_MEMORIES_ANY);
        } else if ((mediaTypeSupport & MEDIA_TYPE_SUPPORT_WIDGET) != 0) {
            // 能力开放、创建回忆业务不相关，这里只有桌面卡片选择才能走到
            parentPath = TypeFilterUtils.checkPathByMediaTypeSupport(mediaTypeSupport, Local.PATH_ALBUM_CREATE_WIDGET_ANY);
        } else {
            parentPath = mIsAbilityOpen ? Local.PATH_ALBUM_OPEN_ALL : Local.PATH_ALBUM_ANY_ALL;
            parentPath = TypeFilterUtils.checkPathByMediaTypeSupport(mediaTypeSupport, parentPath);
        }
        return createOrUpdateLocalAlbumImpl(parentPath.getChild(entry.mBucketId), mediaTypeSupport, entry);
    }

    private LocalAlbum createOrUpdateLocalAlbumImpl(Path path, int mediaTypeSupport, BucketEntry entry) {
        final LocalAlbum localAlbum = (LocalAlbum) DataManager.getMediaSet(path);
        if (localAlbum == null) {
            GLog.e(TAG, "createLocalAlbum is null !");
            return null;
        }
        localAlbum.setName(entry.mBucketName);
        final List<Integer> bucketIdList = new ArrayList<>(entry.mBucketIdList);
        IMediaInputEntry albumEntry = localAlbum.getInputEntry();

        Pair<Integer, CoverItem> pair = DBCoverUtils.queryAlbumOrderAndCoverFromCache(localAlbum.getAlbumId());
        if (albumEntry instanceof LocalAlbum.LocalAlbumEntry) {
            final LocalAlbum.LocalAlbumEntry localAlbumEntry = (LocalAlbum.LocalAlbumEntry) albumEntry;
            localAlbumEntry.setSupportMediaType(mediaTypeSupport);
            localAlbumEntry.updateBucketIds(bucketIdList);
            localAlbumEntry.setBucketPath(entry.mBucketPath);
            //侧边栏场景图集排序
            localAlbumEntry.setAlbumId(localAlbum.getAlbumId());
            localAlbumEntry.setItemSortType(pair.getFirst());
            localAlbumEntry.setOrder(localAlbum.getDefaultOrder(!mIsAbilityOpen && mIsSubMediaSetPositiveOrder));
            //侧边栏场景: 自定义封面后，需要设置IsCustomCover标志，防止reload时，mCoverItem缓存clear,导致封面刷新成默认封面
            localAlbumEntry.setCoverInfo(pair.getSecond());
        }
        localAlbum.reload();
        localAlbum.updateCount(entry.mCount);
        /**
         * 侧边栏场景从图集表查询封面信息
         *
         * 侧边栏上的图集数据加载走的是原来的LocalAlbumSet-createLocalAlbum流程，而原来的流程中的封面数据是来源于localmedia表的，
         * 所以侧边栏图集的封面mCoverItems永远都是默认封面，并且选中的也是默认封面
         *
         * 方案：
         * 1.在LocalAlbumSet-createLocalAlbum中，根据当前的localAlbum的albumId去图集表中查询cover info
         * 2.然后根据coverpath去localmedia中查询并构建mediaItem ,重新设置localAlbum.setCoverItem()，更新mCoverItems
         *
         * Marked by xiaxudong:后续会进行侧边栏数据加载整改
         */
        MediaItem mediaItem = DBCoverUtils.queryCoverMediaItemFromLocal(pair.getSecond(), entry.mCoverItem);
        ArrayList<MediaItem> tempCovers = new ArrayList<>();
        tempCovers.add(mediaItem);
        localAlbum.setCovers(tempCovers);
        return localAlbum;
    }

    private MediaSet createLocalAlbum(Path path) {
        final MediaSet mediaSet = DataManager.getMediaSet(path);
        assert mediaSet != null;
        mediaSet.reload();
        return mediaSet;
    }

    private class AlbumsLoader implements ThreadPool.Job<List<MediaSet>> {

        @Override
        public List<MediaSet> run(JobContext jc) {
            GLog.d(TAG, "AlbumsLoader.run");
            final long startTime = System.currentTimeMillis();
            long time = System.currentTimeMillis();
            long loadEntriesTime = 0;
            long sortHideTime = 0;
            long sortNormalTime = 0;
            long getNormalTime = 0;
            long getHideTime = 0;
            final ArrayList<MediaSet> normalSets = new ArrayList<>();
            final ArrayList<MediaSet> hideSets = new ArrayList<>();
            try {
                GTrace.traceBegin("LocalAlbumSet:AlbumsLoader");
                if (jc.isCancelled()) {
                    return null;
                }
                final int mediaTypeSupport = mEntry.getMediaTypeSupport();
                final BucketEntry[][] result = BucketHelper.loadBucketEntries(mApplication, mediaTypeSupport);
                loadEntriesTime = GLog.getTime(time);
                time = System.currentTimeMillis();
                if (jc.isCancelled()) {
                    return null;
                }
                final BucketEntry[] normalEntries = result[0];
                final BucketEntry[] hideEntries = result[1];
                if (jc.isCancelled()) {
                    return null;
                }
                Arrays.sort(hideEntries, BUCKET_ENTRY_COMPARATOR);
                sortHideTime = GLog.getTime(time);
                time = System.currentTimeMillis();
                if (jc.isCancelled()) {
                    return null;
                }
                if (!addCommonAlbums(jc, normalEntries, normalSets)) {
                    return null;
                }

                sortNormalTime = GLog.getTime(time);
                time = System.currentTimeMillis();
                if (jc.isCancelled()) {
                    return null;
                }
                //add hide album
                if (!addCommonAlbums(jc, hideEntries, hideSets)) {
                    return null;
                }

                addOtherAlbumSet(mediaTypeSupport, normalSets, hideSets);
                getHideTime = GLog.getTime(time);
            } catch (Exception e) {
                GLog.e(TAG, "AlbumsLoader.run error:", e);
            } finally {
                if (GProperty.getLOGPERF()) {
                    GLog.w(TAG,
                            String.format(Locale.ENGLISH,
                                    "LocalAlbumSet-AlbumsLoader: "
                                            + "loadEntriesTime=%d, sortHideTime=%d,"
                                            + " getNormalTime=%d, sortNormalTime=%d,"
                                            + " getHideTime=%d, allTime=%d",
                                    loadEntriesTime, sortHideTime, getNormalTime, sortNormalTime,
                                    getHideTime, GLog.getTime(startTime)));
                }
                GTrace.traceEnd();
            }
            mNormalSets = normalSets;
            mDataVersion = nextVersionNumber();
            return null;
        }
    }

    @Override
    public synchronized void onFutureDone(Future<List<MediaSet>> future) {
        if (mLoadTask != future.getJob()) {
            GLog.e(TAG, "onFutureDone ! mLoadTask=" + mLoadTask + " future.getJob()=" + future.getJob());
            return; // ignore, wait for the latest task
        }
        BuildersKt.launch(AppScope.INSTANCE, Dispatchers.getIO(), CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    setLoading(false);
                    notifyContentChanged();
                    return null;
                });
    }

    @Override
    public @NotNull String getTag() {
        return TAG;
    }

    public static class LocalAlbumSetEntry implements IMediaInputEntry {

        private int mMediaTypeSupport;

        private LocalAlbumSetEntry() {
        }

        public LocalAlbumSetEntry(int mediaTypeSupport) {
            mMediaTypeSupport = mediaTypeSupport;
        }

        @Override
        public final String[] getProjection() {
            return new String[0];
        }

        @Override
        public final String getWhere() {
            return MediaSetUtils.constraintWhere(mMediaTypeSupport, null);
        }

        @Override
        public final String getOrder() {
            return null;
        }

        @Override
        public int getMediaTypeSupport() {
            return mMediaTypeSupport;
        }

        @Override
        public void setOrder(String order) {
        }

        @Override
        public IMediaInputEntry copy(IMediaInputEntry targetEntry) {
            LocalAlbumSetEntry resultEntry = null;
            if (targetEntry instanceof LocalAlbumSetEntry) {
                resultEntry = (LocalAlbumSetEntry) targetEntry;
            } else {
                resultEntry = new LocalAlbumSetEntry();
            }
            resultEntry.mMediaTypeSupport = mMediaTypeSupport;
            return resultEntry;
        }
    }
}