/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaDetails.java
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/4
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2020/8/4      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/


package com.oplus.gallery.business_lib.model.data.base;

import android.graphics.BitmapFactory.Options;
import androidx.exifinterface.media.ExifInterface;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;
import android.util.Rational;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils;
import com.oplus.gallery.foundation.exif.raw.FlashData;
import com.oplus.gallery.foundation.exif.raw.ImageInfoUtils;
import com.oplus.gallery.foundation.exif.raw.PhotoMetadata;
import com.oplus.gallery.foundation.fileaccess.FileAccessManager;
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.frameextractor_lib.IMediaMetadataRetriever;
import com.oplus.gallery.framework.datatmp.R;
import com.oplus.gallery.standard_lib.codec.DecodeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.text.TextUtil;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MediaDetails implements Iterable<Entry<Integer, Object>> {
    public static final int ROTATION_180 = 180;
    public static final int INDEX_TITLE = 1;
    public static final int INDEX_DESCRIPTION = 2;
    public static final int INDEX_DATETIME = 3;
    public static final int INDEX_LOCATION = 4;
    public static final int INDEX_DURATION = 8;
    public static final int INDEX_MIMETYPE = 9;
    public static final int INDEX_WIDTH_HEIGHT = 10;
    public static final int INDEX_FPS = 11;
    public static final int INDEX_SIZE = 12;
    public static final int INDEX_FILE_NUMBER = 13;
    public static final int INDEX_HDR_FORMAT = 14;
    //视频metaData的镜头信息在details的Index
    public static final int INDEX_VIDEO_USER_DATA = 15;
    //视频metaData获取镜头信息用
    public static final int METADATA_KEY_MPEG_USER_DATA = 1001;
    // for EXIF
    public static final int INDEX_PATH = 100;
    public static final int INDEX_MODEL = 101;
    public static final int INDEX_FOCAL_LENGTH = 102;
    public static final int INDEX_APERTURE = 103;
    public static final int INDEX_ISO = 104;
    public static final int INDEX_FLASH = 105;
    public static final int INDEX_WHITE_BALANCE = 106;
    public static final int INDEX_SHUTTER_SPEED = 107;
    public static final int INDEX_EXPOSURE_TIME = 108;
    public static final int INDEX_EXPOSURE_BIAS_VALUE = 109;
    public static final int INDEX_LENS_MODEL = 110;
    public static final int INDEX_MAX_APERTURE_VALUE = 111;
    public static final int INDEX_FOCAL_LENGTH_IN_35MM_FILM = 112;
    public static final int INDEX_DIGITAL_ZOOM_RATIO = 113;
    public static final int INVALID_RATION_VALUE = -1;
    public static final int FIRST_POSITION = 1;
    public static final String MULTIPLICATION_SIGN = Character.toString((char) 0x00D7);

    /**
     * Pattern to check fps from slowmotion title:
     */
    public static final Pattern FPS_PATTERN_FOR_SLOW_MOTION_TITLE = Pattern.compile("_([0-9]+):");
    @SuppressWarnings("unused")
    private static final String TAG = "MediaDetails";
    private TreeMap<Integer, Object> mDetails = new TreeMap<>();
    private HashMap<Integer, Integer> mUnits = new HashMap<>();

    public static class FlashState {
        public static final int FLASH_FIRED_MASK = 1;
        private static final int FLASH_RETURN_MASK = 2 | 4;
        private static final int FLASH_MODE_MASK = 8 | 16;
        private static final int FLASH_FUNCTION_MASK = 32;
        private static final int FLASH_RED_EYE_MASK = 64;
        private int mState;

        public FlashState(int state) {
            mState = state;
        }

        public boolean isFlashFired() {
            return (mState & FLASH_FIRED_MASK) != 0;
        }
    }

    public void addDetail(int index, Object value) {
        mDetails.put(index, value);
    }

    public void removeDetail(int index) {
        mDetails.remove(index);
    }

    public Object getDetail(int index) {
        return mDetails.get(index);
    }

    public int size() {
        return mDetails.size();
    }

    public Iterator<Entry<Integer, Object>> iterator() {
        return mDetails.entrySet().iterator();
    }

    public void setUnit(int index, int unit) {
        mUnits.put(index, unit);
    }

    public boolean hasUnit(int index) {
        return mUnits.containsKey(index);
    }

    public int getUnit(int index) {
        return mUnits.get(index);
    }

    private static void setExifData(MediaDetails details, ExifInterface exif, String tag, int key) {
        String value = exif.getAttribute(tag);
        try {
            if (value != null) {
                if (key == MediaDetails.INDEX_FLASH) {
                    MediaDetails.FlashState state = new MediaDetails.FlashState(Integer.parseInt(value));
                    details.addDetail(key, state);
                } else {
                    details.addDetail(key, value);
                }
            }
        } catch (NumberFormatException e) {
            GLog.e(TAG, "setExifData", e);
        }
    }

    /**
     * get image.width & image.height from filePath, then set to details. add by
     * LiuJunChao
     *
     * @param details  the details to hold image.width & image.height
     * @param filePath the image file
     */
    public static void setWidthAndHeight(MediaDetails details, String filePath, Uri uri, int rotate) {
        InputStream tmpInputStream = null;
        try {
            OpenFileRequest openFileRequest = new OpenFileRequest.Builder().setFile(filePath).setUri(uri).builder();
            tmpInputStream = FileAccessManager.getInstance().getInputStream(ContextGetter.context, openFileRequest);
            Options options = DecodeUtils.decodeBounds(tmpInputStream, null);
            int width = options.outWidth;
            int height = options.outHeight;
            if (rotate != INVALID_RATION_VALUE) {
                width = getRotated(rotate, options.outWidth, options.outHeight);
                height = getRotated(rotate, options.outHeight, options.outWidth);
            }
            // add for bug:1153780
            details.addDetail(MediaDetails.INDEX_WIDTH_HEIGHT, String.format("%d %s %d", width,
                    MULTIPLICATION_SIGN, height));
        } catch (Exception e) {
            GLog.e(TAG, "setWidthAndHeight", e);
        } finally {
            IOUtils.closeQuietly(tmpInputStream);
        }
    }

    /**
     * 设置长和宽到details中去，会根据旋转角度来校正宽高
     */
    public static void setWidthAndHeight(MediaDetails details, int rotate, int width, int height) {
        if ((width <= 0) || (height <= 0)) {
            GLog.w(TAG, "setWidthAndHeight, width or height is less than zero");
            return;
        }
        int widthInternal = width;
        int heightInternal = height;
        if (rotate != INVALID_RATION_VALUE) {
            widthInternal = getRotated(rotate, width, height);
            heightInternal = getRotated(rotate, height, width);
        }
        details.addDetail(MediaDetails.INDEX_WIDTH_HEIGHT, String.format("%d %s %d", widthInternal,
                MULTIPLICATION_SIGN, heightInternal));
    }

    /**
     * 设置视频文件的帧率元数据
     *
     * @param details  媒体详情实例
     * @param filePath 视频文件原始路径
     * @param caption  慢动作标记
     */
    public static void setVideoFps(MediaDetails details, String filePath, String caption) {
        long startTime = System.currentTimeMillis();
        IMediaMetadataRetriever retriever = null;
        try (FileInputStream fileInputStream = new FileInputStream(filePath)) {
            String fps = TextUtil.EMPTY_STRING;
            if (VideoTypeUtils.isSlowMotion(caption)) {
                /**
                 * 对于慢动作视频，媒体库元数据取出的帧率不可信(bugid 8900690 ),因此采用解析慢动作标记来提取fps信息
                 */
                fps = getSlowMotionFpsFromCaption(caption);
            } else {
                retriever = IMediaMetadataRetriever.create(IMediaMetadataRetriever.MediaMetadataRetrieverVendor.TBL);
                retriever.setDataSource(fileInputStream.getFD());
                fps = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE);
            }

            GLog.d(TAG, LogFlag.DL, "setVideoFps, fps = " + fps + ", time = " + (System.currentTimeMillis() - startTime));
            if (!TextUtils.isEmpty(fps)) {
                details.addDetail(MediaDetails.INDEX_FPS, fps);
            } else {
                GLog.w(TAG, LogFlag.DL, "setVideoFps, fps is empty.");
                details.removeDetail(MediaDetails.INDEX_FPS);
            }
        } catch (IOException | IllegalArgumentException e) {
            /**
             *  捕获IllegalArgumentException的原因：
             * 1.setDataSource接口内部只要发生异常都会抛出IllegalArgumentException.
             * 2.无法提前判断是哪种损坏，损坏可能是多种类型的，无法通过某一个判断，或者某几个判断来保证判断的完整性，
             *   只能通过setDatasource解析元数据的方式，来判断.
             */
            GLog.e(TAG, LogFlag.DL, "setVideoFps, exception: " + e);
        } finally {
            if (retriever != null) {
                retriever.release();
            }
        }
    }

    /**
     * 获取各厂家中视频文件的定制信息
     *
     * @param details  媒体详情实例
     * @param filePath 视频文件原始路径
     */
    public static void setVideoUserData(MediaDetails details, String filePath) {
        //多媒体组将新增代码写入到原生的MediaMetadataRetriever，IMediaMetadataRetriever暂时无法兼容
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            // 设置数据源
            retriever.setDataSource(filePath);
            // 获取定制信息title
            String userData = retriever.extractMetadata(METADATA_KEY_MPEG_USER_DATA);
            // 获取定制信息
            details.addDetail(MediaDetails.INDEX_VIDEO_USER_DATA, userData);
        } catch (IllegalArgumentException e) {
            /**
             *  捕获IllegalArgumentException的原因：
             * 1.setDataSource接口内部只要发生异常都会抛出IllegalArgumentException.
             * 2.无法提前判断是哪种损坏，损坏可能是多种类型的，无法通过某一个判断，或者某几个判断来保证判断的完整性，
             *   只能通过setDatasource解析元数据的方式，来判断.
             */
            //获取定制信息失败，空字符串
            details.addDetail(MediaDetails.INDEX_VIDEO_USER_DATA, TextUtil.EMPTY_STRING);
            GLog.e(TAG, LogFlag.DL, "setVideoUserData, exception: " + e);
        } finally {
            try {
                //释放资源，避免内存泄漏
                retriever.release();
            } catch (IOException e) {
                GLog.e(TAG, LogFlag.DL, "setVideoUserData, exception: " + e);
            }
        }
    }

    /**
     * 从慢动作标记中解析出帧率
     *
     * @param caption 慢动作视频标记，形如：Oplus_0slow_motion_hfr_120:0,0,0,0
     * @return 提取之后的fps
     */
    private static String getSlowMotionFpsFromCaption(String caption) {
        Matcher matcher = FPS_PATTERN_FOR_SLOW_MOTION_TITLE.matcher(caption);
        return matcher.find() ? matcher.group(FIRST_POSITION) : TextUtil.EMPTY_STRING;
    }

    public static void extractExifInfo(MediaDetails details, String filePath, Uri uri) {
        if (TextUtils.isEmpty(filePath)) {
            return;
        }
        ParcelFileDescriptor parcelFileDescriptor = null;
        try {
            ExifInterface exif = null;
            parcelFileDescriptor = FileAccessManager.getInstance().openFile(ContextGetter.context, uri);
            if (parcelFileDescriptor != null) {
                exif = new ExifInterface(parcelFileDescriptor.getFileDescriptor());
            }
            if (exif != null) {
                setExifData(details, exif, ExifInterface.TAG_FLASH, MediaDetails.INDEX_FLASH);
                setExifData(details, exif, ExifInterface.TAG_MODEL, MediaDetails.INDEX_MODEL);
                setExifData(details, exif, ExifInterface.TAG_F_NUMBER, MediaDetails.INDEX_APERTURE);
                setExifData(details, exif, ExifInterface.TAG_ISO_SPEED_RATINGS, MediaDetails.INDEX_ISO);
                setExifData(details, exif, ExifInterface.TAG_WHITE_BALANCE,
                        MediaDetails.INDEX_WHITE_BALANCE);
                setExifData(details, exif, ExifInterface.TAG_EXPOSURE_TIME,
                        MediaDetails.INDEX_EXPOSURE_TIME);
                //曝光值ev
                setExifData(details, exif, ExifInterface.TAG_EXPOSURE_BIAS_VALUE, MediaDetails.INDEX_EXPOSURE_BIAS_VALUE);
                //镜头信息
                setExifData(details, exif, ExifInterface.TAG_LENS_MODEL, MediaDetails.INDEX_LENS_MODEL);
                //固定焦距
                setExifData(details, exif, ExifInterface.TAG_FOCAL_LENGTH, MediaDetails.INDEX_FOCAL_LENGTH);
                //最大光圈
                setExifData(details, exif, ExifInterface.TAG_MAX_APERTURE_VALUE, MediaDetails.INDEX_MAX_APERTURE_VALUE);
                //35mm等效焦距
                setExifData(details, exif, ExifInterface.TAG_FOCAL_LENGTH_IN_35MM_FILM, MediaDetails.INDEX_FOCAL_LENGTH_IN_35MM_FILM);
                //数码变焦倍率
                setExifData(details, exif, ExifInterface.TAG_DIGITAL_ZOOM_RATIO, MediaDetails.INDEX_DIGITAL_ZOOM_RATIO);
            }
        } catch (Exception ex) {
            // ignore it.
            GLog.w(TAG, "extractExifInfo, IOException: ", ex);
        } finally {
            setWidthAndHeight(details, filePath, uri, MediaDetails.INVALID_RATION_VALUE);
            IOUtils.closeQuietly(parcelFileDescriptor);
        }
    }

    public static void extractRawFileExifInfo(MediaDetails details, String filePath, Uri uri, int rotation) {
        if (TextUtils.isEmpty(filePath)) {
            GLog.e(TAG, "extractRawFileExifInfo filePath isEmpty");
            return;
        }

        try {
            PhotoMetadata metadata = ImageInfoUtils.readPhotoMetadata(filePath);
            if (metadata == null) {
                GLog.w(TAG, "extractRawFileExifInfo, readPhotoMetadata error uri = " + uri);
                return;
            }

            String model = metadata.get(PhotoMetadata.PROP_MODEL);
            if (!TextUtils.isEmpty(model)) {
                details.addDetail(MediaDetails.INDEX_MODEL, model);
            }

            Double apertureValue = metadata.get(PhotoMetadata.PROP_APERTURE_VALUE);
            if (apertureValue != null) {
                details.addDetail(MediaDetails.INDEX_APERTURE, String.valueOf(apertureValue));
            }

            Integer isoValue = metadata.get(PhotoMetadata.PROP_ISO);
            if (isoValue != null) {
                details.addDetail(MediaDetails.INDEX_ISO, String.valueOf(isoValue));
            }

            FlashData flashValue = metadata.get(PhotoMetadata.PROP_FLASH_DATA);
            if (flashValue != null) {
                MediaDetails.FlashState state = new MediaDetails.FlashState(flashValue.getFlashValueMask());
                details.addDetail(MediaDetails.INDEX_FLASH, state);
            }

            PhotoMetadata.WhiteBalance wb = metadata.get(PhotoMetadata.PROP_WHITE_BALANCE);
            if (wb != null) {
                details.addDetail(MediaDetails.INDEX_WHITE_BALANCE,
                        (wb == PhotoMetadata.WhiteBalance.MANUAL)
                                ? ExifInterface.WHITEBALANCE_MANUAL
                                : ExifInterface.WHITEBALANCE_AUTO);
            }

            Rational exposureTime = metadata.get(PhotoMetadata.PROP_EXPOSURE_TIME);
            if (exposureTime != null) {
                details.addDetail(MediaDetails.INDEX_EXPOSURE_TIME, String.valueOf(exposureTime.floatValue()));
            }

            // fov焦距
            Integer fovFocalLengthIn35MM = metadata.get(PhotoMetadata.PROP_FOCAL_LENGTH_IN_35MM);
            details.addDetail(MediaDetails.INDEX_FOCAL_LENGTH_IN_35MM_FILM, fovFocalLengthIn35MM);
            //原始焦距
            Rational focalLength = metadata.get(PhotoMetadata.PROP_FOCAL_LENGTH);
            details.addDetail(MediaDetails.INDEX_FOCAL_LENGTH, focalLength);
        } catch (Exception ex) {
            GLog.w(TAG, "extractExifInfo, Exception: ", ex);
        } finally {
            MediaDetails.setWidthAndHeight(details, filePath, uri, rotation);
        }
    }

    /**
     * HDR 信息格式字段
     *
     * @param details   MediaDetails
     * @param mediaItem MediaItem
     */
    public static void extractHDRInfoIfNeed(MediaDetails details, MediaItem mediaItem) {
        if (VideoTypeUtils.isDolbyVideo(mediaItem)) {
            details.addDetail(MediaDetails.INDEX_HDR_FORMAT, ContextGetter.context.getString(R.string.model_title_mediatype_dolby));
        }
    }

    private static int getRotated(int degree, int original, int theother) {
        return (degree % ROTATION_180 == 0) ? original : theother;
    }
}
