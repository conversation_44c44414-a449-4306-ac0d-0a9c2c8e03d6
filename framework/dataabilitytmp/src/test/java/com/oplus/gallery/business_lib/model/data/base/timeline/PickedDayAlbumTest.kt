/********************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: PickedDayAlbumTest.kt
 * Description: PickedDayAlbum单元测试类
 *
 * Version: 1.0
 * Date: 2023/03/27
 * Author: Frank<PERSON><EMAIL>
 * TAG:
 * ------------------------------- Revision History: ----------------------------
 * <author>                       <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON>@Apps.Gallery        2023/03/27    1.0              OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.business_lib.model.data.base.timeline

import android.content.Context
import android.hardware.usb.UsbManager
import android.net.Uri
import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.MtpSource
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.seniorpicked.layout.data.PickedDayTypesettingConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * PickedDayAlbum单元测试类
 */
class PickedDayAlbumTest {
    private val oneDayTimeMillis = 24 * 60 * 60 * 1000
    private val mediaItemList3 = mutableListOf<MediaItem>()

    @MockK
    private lateinit var config: PickedDayTypesettingConfig

    @MockK
    private lateinit var mediaItem0: MediaItem

    @MockK
    private lateinit var mediaItem1: MediaItem

    @MockK
    private lateinit var mediaItem2: MediaItem

    @Before
    fun setUp() {
        ContextGetter.context = mockk()
        every { ContextGetter.context.mainLooper } returns mockk()
        every { ContextGetter.context.getPackageName() } returns "abc.abc.abc"
        every { ContextGetter.context.registerReceiver(any(), any()) } returns null
        every { ContextGetter.context.registerReceiver(any(), any(), any()) } returns null
        every { ContextGetter.context.getSystemService(Context.USB_SERVICE) } returns mockk<UsbManager>(relaxed = true)
        mockkStatic(Uri::class)
        every { Uri.parse(any()) } returns mockkClass(Uri::class)
        mockkConstructor(MtpSource::class)
        mockkObject(DataManager)
        mockkObject(CloudParamConfig)
        mockkStatic(CloudParamConfig::class)
        mockkStatic(DataManager::class)
        every { CloudParamConfig.getDefaultGroupValue(any(), any(), any()) } returns Unit
        every { CloudParamConfig.getDefaultGroupValue(any(), any()) } returns Unit
        every { DataManager.registerChangeNotifier(any(), any()) } answers {}

        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        mediaItemList3.add(mediaItem0)
        mediaItemList3.add(mediaItem1)
        mediaItemList3.add(mediaItem2)
        every { mediaItem0.mediaId } returns 123
        every { mediaItem0.day } returns 20220401
        every { mediaItem1.mediaId } returns 321
        every { mediaItem1.day } returns 20220401
        every { mediaItem2.mediaId } returns 333
        every { mediaItem2.day } returns 20220402
        every { config.hasHighScoreGrid(any()) } returns true
        every { config.getGridCountOfLoopTemplate() } returns 12
        every { config.findIndexOfHighScoreGrid(any(), any()) } returns 3
    }

    @Test
    fun `should return correct when mergeNodesFirstTime with k is 3`() {
        // give
        val groupThreshold = 25
        val path = mockk<Path>()
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)
        val nodes = ArrayList<TimeNode>()
        val time = System.currentTimeMillis() + oneDayTimeMillis
        for (i in 1..26) {
            val node = TimeNode(
                "2023030$i", i - 1, time,
                LongRange(time + i * 1000, time + i * 1000),
                IntRange(i, i), IntRange.EMPTY
            )
            nodes.add(node)
        }

        // when
        album.setGroupThreshold(groupThreshold)
        album.mergeNodesFirstTime(nodes)

        // then
        Assert.assertEquals(nodes.size, 2)
        Assert.assertEquals(nodes.first().timeRange, LongRange(time + 1000, time + groupThreshold * 1000))
    }

    @Test
    fun `should return correct when mergeNodesFirstTime with k is 4`() {
        // give
        val groupThreshold = 33
        val path = mockk<Path>()
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)
        val nodes = ArrayList<TimeNode>()
        val time = System.currentTimeMillis() + oneDayTimeMillis
        for (i in 1..34) {
            val node = TimeNode(
                "2023030$i", i - 1, time,
                LongRange(time + i * 1000, time + i * 1000),
                IntRange(i, i), IntRange.EMPTY
            )
            nodes.add(node)
        }

        // when
        album.setGroupThreshold(groupThreshold)
        album.mergeNodesFirstTime(nodes)

        // then
        Assert.assertEquals(nodes.size, 2)
        Assert.assertEquals(nodes.first().timeRange, LongRange(time + 1000, time + groupThreshold * 1000))
    }

    @Test
    fun `should return correct when mergeNodesFirstTime with k is 6`() {
        // give
        val groupThreshold = 37
        val path = mockk<Path>()
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)
        val nodes = ArrayList<TimeNode>()
        val time = System.currentTimeMillis() + oneDayTimeMillis
        for (i in 1..38) {
            val node = TimeNode(
                "2023030$i", i - 1, time,
                LongRange(time + i * 1000, time + i * 1000),
                IntRange(i, i), IntRange.EMPTY
            )
            nodes.add(node)
        }

        // when
        album.setGroupThreshold(groupThreshold)
        album.mergeNodesFirstTime(nodes)

        // then
        Assert.assertEquals(nodes.size, 2)
        Assert.assertEquals(nodes.first().timeRange, LongRange(time + 1000, time + groupThreshold * 1000))
    }

    @Test
    fun `should return correct when mergeNodesFirstTime with node list`() {
        // give
        val path = mockk<Path>()
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)
        val time = System.currentTimeMillis() + oneDayTimeMillis
        val nodes = ArrayList<TimeNode>()
        nodes.add(
            TimeNode(
                "20230201", 0, time,
                LongRange(time, time + 1000),
                IntRange(0, 1), IntRange.EMPTY
            )
        )
        nodes.add(
            TimeNode(
                "20230202", 1, time + 2000,
                LongRange(time + 2000, time + 3000),
                IntRange(2, 30), IntRange.EMPTY
            )
        )
        nodes.add(
            TimeNode(
                "20230301", 2, time + 4000,
                LongRange(time + 4000, time + 5000),
                IntRange(31, 31), IntRange.EMPTY
            )
        )
        nodes.add(
            TimeNode(
                "20230302", 3, time + 6000,
                LongRange(time + 6000, time + 7000),
                IntRange(32, 32), IntRange.EMPTY
            )
        )
        nodes.add(
            TimeNode(
                "20230310", 4, time + 8000,
                LongRange(time + 8000, time + 9000),
                IntRange(33, 36), IntRange.EMPTY
            )
        )

        // when
        album.setGroupThreshold(50)
        album.mergeNodesFirstTime(nodes)

        // then
        Assert.assertEquals(nodes.size, 3)
        Assert.assertEquals(nodes[0].itemRange, IntRange(0, 1))
        Assert.assertEquals(nodes[1].itemRange, IntRange(2, 30))
        Assert.assertEquals(nodes[2].itemRange, IntRange(31, 36))
    }

    @Test
    fun `should return true when sortAndMakeIndex with mediaItem size is 1`() {
        val mediaItemList = mutableListOf<MediaItem>()
        mediaItemList.add(mediaItem0)
        every { mediaItem0.mediaId } returns 123
        val path = mockk<Path>()
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)
        val result = album.sortAndMakeIndex(mediaItemList)
        Assert.assertTrue(result.size == 1)
        Assert.assertTrue(result[0].mediaId == 123)
    }

    @Test
    fun `should return true when sortAndMakeIndex with timeNode is null`() {
        val path = mockk<Path>()
        val nodes = ArrayList<TimeNode>()
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = spyk(PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)) {
            every { getTimeNodeList(TimeNodeType.PICKED_DAY) } returns nodes
            every { timeNodeOfKey(any(), any()) } returns null
        }
        val result = album.sortAndMakeIndex(mediaItemList3)
        Assert.assertTrue(result.size == 3)
        Assert.assertTrue(result[2].mediaId == 333)
    }

    @Test
    fun `should return true when sortAndMakeIndex with mediaItem exception`() {
        /**
         * 设定倒序，即item 日期从大到小
         * 接着故意设置mediaItemList出现一个变大的日期，测试异常情况
         */
        val path = mockk<Path>()
        mockkStatic(PickedDayConfig::class)
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = spyk(PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)) {
            every { isAscOrder } returns true
            val timeNode = TimeNode(
                "20220401",
                0,
                123456,
                LongRange(123456, 123456),
                IntRange(0, 10),
                IntRange(0, 0)
            )
            every { timeNodeOfKey(any(), any()) } returns timeNode
            val timeNodeList = mutableListOf<TimeNode>()
            timeNodeList.add(timeNode)
            every { getTimeNodeList(TimeNodeType.PICKED_DAY) } returns timeNodeList
        }
        val mediaItemList = mutableListOf<MediaItem>()
        every { mediaItem0.day } returns 20220401
        every { mediaItem1.day } returns 20220401
        every { mediaItem2.day } returns 20220501
        every { mediaItem0.dateTakenInMs } returns 123456
        every { mediaItem1.dateTakenInMs } returns 123456
        every { mediaItem2.dateTakenInMs } returns 234567
        mediaItemList.add(mediaItem0)
        mediaItemList.add(mediaItem1)
        mediaItemList.add(mediaItem2)
        every { PickedDayConfig.getPickedDayTypesettingConfig() } returns config
        val result = album.sortAndMakeIndex(mediaItemList)
        /**
         * 期望flow
         * 1，前两个item 成功加到准备排序的List里面
         * 2，第三个因日期异常而continue，导致循环跳出，因此结果为isEmpty
         */
        Assert.assertTrue(result.isEmpty())
    }

    @Test
    fun `should return true when sortInNode with originalList size is 1`() {
        val path = mockk<Path>()
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)
        val mediaItemList = mutableListOf<MediaItem>()
        mediaItemList.add(mediaItem0)
        every { mediaItem0.mediaId } returns 123
        val result = album.sortInNode(mediaItemList)
        Assert.assertTrue(result.size == 1)
        Assert.assertTrue(result[0].mediaId == 123)
    }

    @Test
    fun `should no sort when sortInNode with no score`() {
        val path = mockk<Path>()
        mockkStatic(PickedDayConfig::class)
        every { path.setObject(any()) } answers {}
        every { path.split() } returns emptyArray<String>()
        val album = PickedDayAlbum(path, ContextGetter.context, Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL)
        val mediaItemList = mutableListOf<MediaItem>()
        mediaItemList.add(mediaItem0)
        mediaItemList.add(mediaItem1)
        mediaItemList.add(mediaItem2)
        mediaItemList.add(mediaItem0)
        mediaItemList.add(mediaItem1)
        mediaItemList.add(mediaItem2)
        every { PickedDayConfig.getPickedDayTypesettingConfig() } returns config
        val result = album.sortInNode(mediaItemList)
        Assert.assertTrue(result.size == 6)
        Assert.assertTrue(result[5].mediaId == 333)
    }
}