/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FastCaptureDetectorTest.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/4/27
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2022/4/27    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.data.fastcapture

import android.content.Context
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito

class FastCaptureDetectorTest {

    @Before
    fun setUp() {
        ContextGetter.context = Mockito.mock(Context::class.java)
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 人像超清模式拍摄的 tmp模糊图 的路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_super_clear_and_temp_type_is_tmp() {
        val filePath = "/storage/emulated/0/DCIM/Camera/IMG20201224051605_sc_tmp.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertTrue(isTemporaryTmp())
            Assert.assertFalse(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertFalse(isNormal())
            Assert.assertFalse(isCShot())
            Assert.assertFalse(isSuperResolution())
            Assert.assertTrue(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 人像超清模式拍摄的 quick图 的文件路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_super_clear_and_temp_type_is_quick() {
        val filePath = "/storage/emulated/0/DCIM/Camera/IMG20201224051605_sc_quick.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertFalse(isTemporaryTmp())
            Assert.assertTrue(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertFalse(isNormal())
            Assert.assertFalse(isCShot())
            Assert.assertFalse(isSuperResolution())
            Assert.assertTrue(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 高像素模式拍摄的 tmp模糊图 的路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_super_resolution_and_temp_type_is_tmp() {
        val filePath = "/storage/emulated/0/DCIM/Camera/IMG20201224051605_sr_tmp.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertTrue(isTemporaryTmp())
            Assert.assertFalse(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertFalse(isNormal())
            Assert.assertFalse(isCShot())
            Assert.assertTrue(isSuperResolution())
            Assert.assertFalse(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 高像素模式拍摄的 quick图 的文件路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_super_resolution_and_temp_type_is_quick() {
        val filePath = "/storage/emulated/0/DCIM/Camera/IMG20201224051605_sr_quick.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertFalse(isTemporaryTmp())
            Assert.assertTrue(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertFalse(isNormal())
            Assert.assertFalse(isCShot())
            Assert.assertTrue(isSuperResolution())
            Assert.assertFalse(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 连拍模式拍摄的 tmp模糊图 的路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_cshot_and_temp_type_is_tmp() {
        val filePath = "/storage/emulated/0/DCIM/Camera/cshot/1608786965461/IMG20201224051605_BURST000_COVER_00_tmp.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath, cShotId = 1608786965461).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertTrue(isTemporaryTmp())
            Assert.assertFalse(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertFalse(isNormal())
            Assert.assertTrue(isCShot())
            Assert.assertFalse(isSuperResolution())
            Assert.assertFalse(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 连拍模式拍摄的 quick图 的文件路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_cshot_and_temp_type_is_quick() {
        val filePath = "/storage/emulated/0/DCIM/Camera/cshot/1608786965461/IMG20201224051605_BURST000_COVER_00_quick.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath, cShotId = 1608786965461).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertFalse(isTemporaryTmp())
            Assert.assertTrue(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertFalse(isNormal())
            Assert.assertTrue(isCShot())
            Assert.assertFalse(isSuperResolution())
            Assert.assertFalse(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 普通模式拍摄的 tmp模糊图 的路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_normal_and_temp_type_is_tmp() {
        val filePath = "/storage/emulated/0/DCIM/Camera/IMG20201224051605_tmp.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertTrue(isTemporaryTmp())
            Assert.assertFalse(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertTrue(isNormal())
            Assert.assertFalse(isCShot())
            Assert.assertFalse(isSuperResolution())
            Assert.assertFalse(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 普通模式拍摄的 quick图 的文件路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_normal_and_temp_type_is_quick() {
        val filePath = "/storage/emulated/0/DCIM/Camera/IMG20201224051605_quick.jpg"
        com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = filePath).detector().apply {
            Assert.assertTrue(isFastCapture())
            Assert.assertFalse(isTemporaryTmp())
            Assert.assertTrue(isTemporaryQuick())
            Assert.assertFalse(isOriginal())

            Assert.assertTrue(isNormal())
            Assert.assertFalse(isCShot())
            Assert.assertFalse(isSuperResolution())
            Assert.assertFalse(isSuperClear())
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] Camera目录下的原图文件路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_capture_mode_is_normal_and_temp_type_is_original() {
        ArrayList<String>().apply {
            add("/storage/emulated/0/DCIM/Camera/IMG20201224051605.jpg")
            add("/storage/emulated/0/DCIM/Camera/IMG20201224051605_1.jpg")
            add("/storage/emulated/0/DCIM/Camera/IMG20201224051605_1_1.jpg")
            add("/storage/emulated/0/DCIM/Camera/IMG20201224051605_tmp_1.jpg")
            add("/storage/emulated/0/DCIM/Camera/IMG20201224051605_quick_1.jpg")
        }.forEach {
            com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = it).detector().apply {
                Assert.assertFalse(isFastCapture())
                Assert.assertFalse(isTemporaryTmp())
                Assert.assertFalse(isTemporaryQuick())
                Assert.assertTrue(isOriginal())

                Assert.assertTrue(isNormal())
                Assert.assertFalse(isCShot())
                Assert.assertFalse(isSuperResolution())
                Assert.assertFalse(isSuperClear())
            }
        }
    }

    /**
     * 测试SDK解析出的拍摄模式和缩图类型
     * ```
     * [Given] 非Camera目录的原图文件路径
     * [When] 构建FastCaptureDetector对象
     * [Then] SDK的缩图类型和拍摄模式判断接口结果正确
     * ```
     */
    @Test
    fun should_return_correct_when_create_item_by_non_camera_directory() {
        ArrayList<String>().apply {
            add("/storage/emulated/0/DCIM/IMG20201224051605.jpg")
            add("/storage/emulated/0/IMG20201224051605_tmp.jpg")
            add("/storage/emulated/0/IMG20201224051605_quick.jpg")
        }.forEach {
            com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureDetector(filePath = it).detector().apply {
                Assert.assertFalse(isFastCapture())
                Assert.assertFalse(isTemporaryTmp())
                Assert.assertFalse(isTemporaryQuick())
                Assert.assertTrue(isOriginal())

                Assert.assertTrue(isNormal())
                Assert.assertFalse(isCShot())
                Assert.assertFalse(isSuperResolution())
                Assert.assertFalse(isSuperClear())
            }
        }
    }
}