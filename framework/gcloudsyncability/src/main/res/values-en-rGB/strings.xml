<?xml version="1.0" encoding="UTF-8"?>

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="google_cloud_wifi_off_stop_download">No Wi-Fi connection. Loading paused.</string>
    <string name="google_cloud_update_button">Update</string>
    <string name="google_cloud_network_disable_stop_upload">No internet connection. Backup paused.</string>
    <string name="google_cloud_network_disable_stop_download">No internet connection. Loading paused.</string>
    <string name="google_cloud_high_temperature_upload">Phone too hot. Backup paused.</string>
    <string name="google_cloud_mobile_data_reached_limit">Mobile data limit reached</string>
    <string name="google_cloud_high_temperature_download">Phone too hot. <PERSON><PERSON> paused.</string>
    <string name="google_cloud_mobile_data_reset">To continue backup, change the mobile data usage settings in \"Google Photos\".</string>
    <string name="google_cloud_not_allow_mobile_data">Backup not allowed on mobile data</string>
    <string name="google_cloud_not_allow_mobile_data_and_reset">You are using a mobile network. Change mobile data usage settings in \"Google Photos\" to continue.</string>
    <string name="google_cloud_low_version">Backup has been turned off because \"Google Photos\" is out of date.</string>
    <string name="google_cloud_not_install">\"Google Photos\" not installed. Backup turned off for \"Photos\".</string>
    <string name="google_cloud_permission_disable">\"Google Photos\" authorisation expired. Backup turned off.</string>
    <string name="google_cloud_account_changed">You are now signed in as \"%1$s\". Your \"Photos\" data will be backed up to your new Google Account.</string>
    <plurals name="google_cloud_upload_files">
        <item quantity="other">Backing up %1$s items to \"Google Photos\"…</item>
        <item quantity="one">Backing up %1$s item to \"Google Photos\"…</item>
    </plurals>
    <string name="google_cloud_save_power_download">Saving battery power. Loading paused.</string>
    <string name="google_cloud_high_temperature_download_pad">Tablet too hot. Loading paused.</string>
    <string name="google_cloud_syncing">Querying \"Google Photos\"…</string>
    <string name="google_cloud_save_power_upload">Saving battery power. Backup paused.</string>
    <string name="google_cloud_backup_done">Photos and videos are backed up to \"Google Photos\"</string>
    <string name="google_cloud_local_no_space">Insufficient storage on your phone. Loading paused.</string>
    <string name="google_cloud_disabled">Your photos are not being backed up because \"Google Photos\" has been disabled. Open the Settings app and go to \"Apps - App management - Google Photos\" to enable it.</string>
    <string name="google_cloud_continue">Resume</string>
    <string name="google_cloud_sync_state_clean">Free up space</string>
    <string name="google_cloud_grant_permission_button">Settings</string>
    <string name="google_cloud_local_no_space_pad">Insufficient storage on your tablet. Loading paused.</string>
    <plurals name="google_cloud_download_files">
        <item quantity="other">Loading %1$s items from \"Google Photos\"…</item>
        <item quantity="one">Loading %1$s item from \"Google Photos\"…</item>
    </plurals>
    <string name="google_cloud_wifi_off_stop_upload">No Wi-Fi connection. Backup paused.</string>
    <string name="google_cloud_high_temperature_upload_pad">Tablet too hot. Backup paused.</string>
    <string name="google_cloud_mobile_data_limit_reached_stop_upload">Daily mobile data usage limit has been reached. Backup has been paused.</string>
    <string name="google_cloud_open_backup_guide_button">Turn on</string>
    <string name="google_cloud_open_backup_guide">Turn on Cloud backup to automatically sync your photos and videos to \"Google Photos\", ensuring the safety of your data.</string>
    <string name="google_cloud_storage_space_low">You have less than %1$s of storage space remaining in \"Google Photos\". To continue backing up data, manage your storage.</string>
    <string name="google_cloud_storage_space_full">Google Photos storage is full and backup has been paused. To continue backing up data, manage your storage.</string>
    <string name="google_cloud_clean_storage_space">Clean up space</string>
    <string name="google_cloud_delete_when_move_to_safebox_message">You can view private photos in \"Private Safe\".</string>
    <string name="google_cloud_delete_when_move_to_safebox_title">Delete private photos from \"Google Photos\"?</string>
    <string name="google_cloud_disabled_title">Unable to turn on \"Back up to Google Photos\" because \"Google Photos\" has been disabled. Open the Settings app and go to \"Apps - App management - Google Photos\" to enable it.</string>
    <string name="google_cloud_go_to_turn_off_backup_message">Photos and videos on this device will no longer be backed up to \"Google Photos\", but you can still load existing data from \"Google Photos\".</string>
    <string name="google_cloud_go_to_turn_off_backup_title">Turn backup off in \"Google Photos\"</string>
    <string name="google_cloud_install_title">Install the latest \"Google Photos\" to continue</string>
    <string name="google_cloud_turn_off_backup_button">Turn off</string>
    <string name="google_cloud_turn_on_backup_after_install_title">\"Google Photos\" installed. Turn on backup?</string>
    <string name="google_cloud_turn_on_backup_button">Turn on</string>
    <string name="google_cloud_turn_on_backup_title">Turn on backup in \"Google Photos\"</string>
    <string name="google_cloud_update_title">Update to the latest \"Google Photos\" to continue</string>
    <string name="google_cloud_download_original_photos_toast">Downloading the original photos…</string>
    <string name="google_cloud_turn_off_album_licensing">Turn the \"Google Photos\" permission off?</string>
    <string name="google_cloud_turn_off_album_licensing_tips">Photos cached by \"Google Photos\" on this device will be removed. It\'s recommended to download the original photos before turning off this permission.</string>
    <string name="google_cloud_turn_off_album_licensing_tips2">The original photos are being downloaded. Turning the \"Google Photos\" permission off now will cancel the downloads and delete the compressed photos from this device.</string>
</resources>
