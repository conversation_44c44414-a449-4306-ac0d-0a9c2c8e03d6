/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : GDataOperator.kt
 ** Description : 数据操作
 ** Version     : 1.0
 ** Date        : 2023/06/16
 ** Author      : zhongxuechang@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D       2023/06/16     1.0       build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.gcloudsync

import android.content.ContentProviderOperation
import android.content.ContentProviderResult
import android.content.ContentValues
import android.database.Cursor
import android.os.Bundle
import android.provider.MediaStore
import androidx.annotation.WorkerThread
import androidx.core.database.getIntOrNull
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.CloudSyncStore
import com.oplus.gallery.foundation.database.store.CloudSyncStore.FileUploadStatus.FILE_UPLOADED
import com.oplus.gallery.foundation.database.store.CloudSyncStore.LocalFileStatus.LOCAL_FILE_STATUS_ORIGINAL
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.CLOUD_DATA_STATUS_NORMAL
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.CLOUD_DATA_STATUS_RECYCLE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.IS_TRASHED_FALSE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.IS_TRASHED_TRUE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.DatabaseUtils.SQLITE_MAX_VARIABLE_NUMBER
import com.oplus.gallery.foundation.database.util.SQLGrammar
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.dbaccess.convert.CountAllConvert
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.dao.MediaStoreDbDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.util.BuildConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.basecloudability.utils.RecyclePathAdapter
import com.oplus.gallery.framework.abilities.gcloudsync.metadata.GFileMetaData
import com.oplus.gallery.framework.abilities.gcloudsync.sdk.GCloudSyncApi
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.scheduler.BatchProcess
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 数据操作
 */
internal object GDataOperator {

    /** 回收站照片保留时间 */
    const val TRASHED_MAX_RETENTION_TIME = 60 * 24 * 3600000L
    private const val TAG = "GDataOperator"
    private val LOCAL_COVERT = object : IConvert<Cursor, List<GFileMetaData>> {
        val LOCAL_PROJECTION = arrayOf(
            CloudColumns._ID,
            CloudColumns.MEDIA_ID,
            CloudColumns.GLOBAL_ID,
            CloudColumns.DATE_TAKEN,
            CloudColumns.DISPLAY_NAME,
            CloudColumns.FILE_ID,
            CloudColumns.DURATION,
            CloudColumns.CLOUD_WIDTH,
            CloudColumns.CLOUD_HEIGHT,
            CloudColumns.MIME_TYPE,
            CloudColumns.CLOUD_SIZE,
            CloudColumns.IS_TRASHED,
            CloudColumns.DATE_RECYCLED,
            CloudColumns.DATA,
            CloudColumns.CLOUD_DATA_STATUS,
            CloudColumns.LOCAL_FILE_STATUS,
            LocalColumns.INVALID,
            LocalColumns.MEDIA_TYPE,
            CloudColumns.FILE_SYNC_COUNT,
            CloudColumns.FILE_SYNC_TIME,
            LocalColumns.SIZE,
            LocalColumns.IS_FAVORITE,
            CloudColumns.FILE_UPLOAD_STATUS
        )
        private val INDEX_ID                   = indexOf(CloudColumns._ID)
        private val INDEX_MEDIA_ID             = indexOf(CloudColumns.MEDIA_ID)
        private val INDEX_METADATA_ID          = indexOf(CloudColumns.GLOBAL_ID)
        private val INDEX_DATE_TAKEN           = indexOf(CloudColumns.DATE_TAKEN)
        private val INDEX_DISPLAY_NAME         = indexOf(CloudColumns.DISPLAY_NAME)
        private val INDEX_FILE_ID              = indexOf(CloudColumns.FILE_ID)
        private val INDEX_DURATION             = indexOf(CloudColumns.DURATION)
        private val INDEX_CLOUD_WIDTH          = indexOf(CloudColumns.CLOUD_WIDTH)
        private val INDEX_CLOUD_HEIGHT         = indexOf(CloudColumns.CLOUD_HEIGHT)
        private val INDEX_MIME_TYPE            = indexOf(CloudColumns.MIME_TYPE)
        private val INDEX_CLOUD_SIZE           = indexOf(CloudColumns.CLOUD_SIZE)
        private val INDEX_IS_TRASHED           = indexOf(CloudColumns.IS_TRASHED)
        private val INDEX_DATE_RECYCLED        = indexOf(CloudColumns.DATE_RECYCLED)
        private val INDEX_DATA                 = indexOf(CloudColumns.DATA)
        private val INDEX_CLOUD_DATA_STATUS    = indexOf(CloudColumns.CLOUD_DATA_STATUS)
        private val INDEX_LOCAL_FILE_STATUS    = indexOf(CloudColumns.LOCAL_FILE_STATUS)
        private val INDEX_INVALID              = indexOf(LocalColumns.INVALID)
        private val INDEX_MEDIA_TYPE           = indexOf(LocalColumns.MEDIA_TYPE)
        private val INDEX_FILE_SYNC_COUNT      = indexOf(CloudColumns.FILE_SYNC_COUNT)
        private val INDEX_FILE_SYNC_TIME       = indexOf(CloudColumns.FILE_SYNC_TIME)
        private val INDEX_SIZE                 = indexOf(LocalColumns.SIZE)
        private val INDEX_IS_FAVORITE          = indexOf(LocalColumns.IS_FAVORITE)
        private val INDEX_FILE_UPLOAD_STATUS   = indexOf(CloudColumns.FILE_UPLOAD_STATUS)

        private fun indexOf(column: String) = LOCAL_PROJECTION.indexOf(column)

        override fun convert(cursor: Cursor?): List<GFileMetaData> {
            if ((cursor == null) || (cursor.count <= 0)) return emptyList()
            val list = mutableListOf<GFileMetaData>()
            while (cursor.moveToNext()) {
                list.add(GFileMetaData().apply {
                    mediaId = cursor.getLong(INDEX_MEDIA_ID)
                    id = cursor.getString(INDEX_METADATA_ID) ?: TextUtil.EMPTY_STRING
                    datetaken = cursor.getLong(INDEX_DATE_TAKEN)
                    displayName = cursor.getString(INDEX_DISPLAY_NAME)
                    uniqueKey = cursor.getString(INDEX_FILE_ID) ?: TextUtil.EMPTY_STRING
                    duration = cursor.getInt(INDEX_DURATION)
                    width = cursor.getInt(INDEX_CLOUD_WIDTH)
                    height = cursor.getInt(INDEX_CLOUD_HEIGHT)
                    mimeType = cursor.getString(INDEX_MIME_TYPE)
                    size = cursor.getLong(INDEX_CLOUD_SIZE)
                    trashStatus = cursor.getInt(INDEX_IS_TRASHED)
                    dateRecycled = cursor.getLong(INDEX_DATE_RECYCLED)
                    filePath = cursor.getString(INDEX_DATA)
                    localId = cursor.getInt(INDEX_ID)
                    cloudDataStatus = cursor.getIntOrNull(INDEX_CLOUD_DATA_STATUS)
                    localFileStatus = cursor.getInt(INDEX_LOCAL_FILE_STATUS)
                    invalid = cursor.getInt(INDEX_INVALID)
                    mediaType = cursor.getInt(INDEX_MEDIA_TYPE)
                    fileSyncCount = cursor.getInt(INDEX_FILE_SYNC_COUNT)
                    fileSyncTime = cursor.getLong(INDEX_FILE_SYNC_TIME)
                    localFileSize = cursor.getLong(INDEX_SIZE)
                    favoriteStatus = cursor.getInt(INDEX_IS_FAVORITE)
                    uploadStatus = cursor.getInt(INDEX_FILE_UPLOAD_STATUS)
                })
            }
            return list
        }
    }
    /** 重命名的重试次数 */
    private const val RENAME_MAX_RETRY_COUNT = 10

    /**
     * 文件元数据存入数据库
     * @param metaDataList 文件元数据
     */
    fun saveMetadata(metaDataList: List<GFileMetaData>) {
        if (metaDataList.isEmpty()) return

        // 转换数据结构为：Map<MetadataId, GFileMetaData>
        val metadataIdMap = toMetadataIdMap(metaDataList)
        GLog.d(TAG, "saveMetadata, pending count: ${metadataIdMap.size}")
        val insertList = mutableListOf<GFileMetaData>()
        val updateList = mutableListOf<Pair<GFileMetaData, GFileMetaData>>()
        val deleteMediaIdList = mutableListOf<Long>()

        // 1 处理云端下发的删除数据
        deleteMediaIdList.addAll(filterDeletedMetadata(metadataIdMap))

        // 2.1 处理文件没有下载到本地的记录
        processMetadataWithoutMediaId(metadataIdMap = filterMetadataWithoutMediaId(metadataIdMap), insertList, updateList)
        // 2.2 处理文件已经下载到本地的记录
        processMetadataWithMediaId(metadataIdMap = metadataIdMap, insertList, updateList)

        // 3 更新数据到数据库
        deleteMediaStoreAndLocalMedia(deleteMediaIdList)
        updateFileMetadata(updateList)
        insertFileMetadata(insertList)

        // 4 清理冗余数据
        clearInvalidDataWithoutMediaId(metadataIdMap.values.toList())
    }

    /**
     * 处理文件还没有下载到本地的数据。
     *
     * 此流程不需要去关心本地文件的因素，只需要区分是更新还是插入即可。
     */
    private fun processMetadataWithoutMediaId(
        metadataIdMap: MutableMap<String, GFileMetaData>,
        insertList: MutableList<GFileMetaData>,
        updateList: MutableList<Pair<GFileMetaData, GFileMetaData>>
    ) {
        // 无媒体库文件的记录，使用元数据id找到待更新记录，并从 [metadataIdMap] 中移除
        updateList.addAll(filterUpdateByMetadataId(metadataIdMap))
        // 现在 [metadataIdMap] 里都是本地没有重复id的，可以直接insert
        insertList.addAll(metadataIdMap.values)
    }

    /**
     * 处理文件已经下载到本地的数据。此流程需要去处理本地文件变更的因素，包括：出入回收站、媒体数据同步等。
     * 1. 处理待更新数据：先保证媒体数据同步完成，然后再更新
     * 2. 处理待插入数据：更新完后，剩余的就是待插入的回收站数据(非回收站数据理论上再媒体数据同步后就会变成待更新数据)
     * 3. 剩余的数据，就是非法数据了，google给的数据源异常导致。
     * @param metadataIdMap 从google返回的元数据
     * @param insertList 待插入数据列表
     * @param updateList 待更新数据列表
     */
    private fun processMetadataWithMediaId(
        metadataIdMap: MutableMap<String, GFileMetaData>,
        insertList: MutableList<GFileMetaData>,
        updateList: MutableList<Pair<GFileMetaData, GFileMetaData>>
    ) {
        val mediaIdMap = convertToMediaIdMap(metadataIdMap.values)
        val pendingMediaIdMap = mutableMapOf<String, GFileMetaData>().apply { putAll(mediaIdMap) }
        val pendingUpdateList: MutableList<Pair<GFileMetaData, GFileMetaData>> = filterUpdateByMediaId(pendingMediaIdMap)

        // 特殊处理：针对回收站恢复的数据，先删除，然后媒体数据同步插入，然后再更新云端数据状态
        val hasRestoredData = processRestoredLocalMetaDataList(pendingUpdateList)
        if (hasRestoredData) {
            // 先触发媒体数据同步，将本地数据同步到相册数据库，然后再更新云端数据
            GLog.d(TAG) { "processMetadataWithMediaId, need execute media sync when mediaId is still not found" }
            ApiDmManager.getMediaDBSyncDM().executeIncrementSync()
            pendingMediaIdMap.clear()
            pendingMediaIdMap.putAll(mediaIdMap)
            updateList.addAll(filterUpdateByMediaId(pendingMediaIdMap))
        } else {
            updateList.addAll(pendingUpdateList)
        }

        // 处理待插入的回收站状态的数据
        if (pendingMediaIdMap.isNotEmpty()) {
            filterForMediaStoreTrashed(pendingMediaIdMap, insertList, updateList)
        }
        // 触发媒体数据同步，将本地数据同步到相册数据库，然后再更新云端数据
        if (pendingMediaIdMap.isNotEmpty() && hasRestoredData.not()) {
            GLog.d(TAG) { "processMetadataWithMediaId, need execute media sync when mediaId is still not found" }
            ApiDmManager.getMediaDBSyncDM().executeIncrementSync()
            updateList.addAll(filterUpdateByMediaId(pendingMediaIdMap))
        }

        // 处理遗留的异常数据，此数据的mediaid在媒体库中找不到。google返回的元数据有异常，没有解绑掉冗余的mediaid。
        if (pendingMediaIdMap.isNotEmpty()) {
            GLog.e(TAG) {
                "processMetadataWithMediaId, has mediaId metadata match fail:${pendingMediaIdMap.values.toTypedArray().contentToString()}"
            }
        }
    }

    private fun processRestoredLocalMetaDataList(pendingUpdateList: MutableList<Pair<GFileMetaData, GFileMetaData>>): Boolean {
        val restoredLocalMetaDataList = getRestoredLocalMetaDataList(pendingUpdateList)
        val hasRestoredData = restoredLocalMetaDataList.isNotEmpty()
        if (hasRestoredData) {
            GLog.d(TAG) { "processRestoredLocalMetaDataList, need execute media sync when some meta data restored" }
            /*
            处理场景：
            已存在相册数据库的记录处于回收站状态被恢复后，会出现两条重复记录。
            原因：
            恢复时，媒体同步同步和云同步任务很可能同时跑，由于目前媒体数据同步不同步媒体库回收站的记录，
            在云同步将这条记录的is_trashed改为0前，媒体同步是查不到这条记录的，文件路径也不会重复，就无
            法索引到，会插入一条新记录，这时就会存在重复的两条。
            方案：
            先识别出这些记录进行删除，然后让数据同步同步后再作更新
            */
            deleteMetaData(restoredLocalMetaDataList.map { it.id })
        }
        return hasRestoredData
    }

    /**
     * 过滤出没有媒体库id的元数据。没有媒体库id说明还没有下载公开文件。
     * @return 返回移除的数据
     */
    private fun filterMetadataWithoutMediaId(metadataMap: MutableMap<String, GFileMetaData>): MutableMap<String, GFileMetaData> {
        val noMediaIdMap = mutableMapOf<String, GFileMetaData>()
        val iterator = metadataMap.iterator()
        var next: GFileMetaData
        while (iterator.hasNext()) {
            next = iterator.next().value
            if ((next.mediaIds?.isNotEmpty() == true).not()) {
                iterator.remove()
                noMediaIdMap[next.id] = next
            }
        }
        return noMediaIdMap
    }

    /**
     * 从元数据中过滤出云端下发的已删除数据。
     *
     * google云下发删除数据后，相册端直接删除本地文件，无需和google需求一致。(谷歌是需要用户同意才能彻底删除本地文件)
     *
     * @return 媒体库的media ids ：MutableList<MediaId>
     */
    private fun filterDeletedMetadata(metadataMap: MutableMap<String, GFileMetaData>): MutableList<Long> {
        val deleteMediaIdList = mutableListOf<Long>()
        val iterator = metadataMap.iterator()
        var next: GFileMetaData
        while (iterator.hasNext()) {
            next = iterator.next().value
            if (next.cloudDataStatus == CloudColumns.CLOUD_DATA_STATUS_DELETE) {
                iterator.remove()
                next.mediaIds?.forEach { deleteMediaIdList.add(it) }
            }
        }
        return deleteMediaIdList
    }

    private fun convertToMediaIdMap(metadatas: Collection<GFileMetaData>): MutableMap<String, GFileMetaData> {
        val mediaIdMap = mutableMapOf<String, GFileMetaData>()
        metadatas.forEach { metadata ->
            metadata.mediaIds?.forEach { mediaId ->
                mediaIdMap[mediaId.toString()]?.let {
                    // 理论上不同的元数据不可能指向相同的媒体库记录
                    GLog.e(TAG) { "convertToMediaIdMap, metadata has duplicated mediaIds, prev:$mediaId, cur:$metadata" }
                }
                mediaIdMap[mediaId.toString()] = metadata
            }
        }
        return mediaIdMap
    }

    /**
     * 检查绝对路径的唯一性，不唯一则进行纠正并更新至数据库
     * @param metaDatas 元数据数组
     */
    fun checkAndCorrectUniqueFilePath(metaDatas: Collection<GFileMetaData>) {
        val updateReqList = mutableListOf<UpdateReq>()
        val filePathSet = mutableSetOf<String>()
        metaDatas.forEach { metaData ->
            val oldFilePath = metaData.filePath
            // 未下载前的路径是虚拟的，先生成默认原路径判断是否有重复的
            var filePath = GFileMetaData.createDefaultFilePath(FilePathUtils.getDisplayName(oldFilePath))
            if (oldFilePath == filePath) {
                // 如果老路径已经是原图预计存储的位置，就不需要再继续生成
                return@forEach
            }
            val dotPosition = filePath.lastIndexOf(".")
            var postfix = TextUtil.EMPTY_STRING
            var prefix = filePath
            if (dotPosition > 0) {
                postfix = filePath.substring(dotPosition)
                prefix = filePath.substring(0, dotPosition)
            }
            var isLastItem: Boolean
            for (i in 0 until RENAME_MAX_RETRY_COUNT + 1) {
                isLastItem = i == RENAME_MAX_RETRY_COUNT
                if (!isDuplicateFilePath(filePath, filePathSet)) break

                filePathSet.add(filePath.lowercase())
                // 最后一次还有冲突，那就取当前的时间作标记
                if (isLastItem) {
                    filePath = "${prefix}_${System.currentTimeMillis()}$postfix"
                    break
                }
                filePath = "${prefix}_${i + 1}$postfix"
            }

            if (updateFilePathToMetaData(metaData, filePath)) {
                updateReqList.add(UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere("${CloudColumns.DATA}=?")
                    .setWhareArgs(arrayOf(oldFilePath))
                    .setConvert {
                        ContentValues().apply {
                            put(LocalColumns.DATA, metaData.filePath)
                            put(LocalColumns.DISPLAY_NAME, metaData.displayName)
                        }
                    }
                    .build())
            }
            filePathSet.add(filePath.lowercase())
        }

        if (updateReqList.isEmpty()) return
        val results = BatchReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .addDataReqs(updateReqList)
            .build()
            .exec()
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        GLog.d(TAG) { "checkAndCorrectUniqueFilePath count:${results.size}" }
    }

    private fun isDuplicateFilePath(filePath: String, filePathSet: Set<String>): Boolean {
        if (filePathSet.contains(filePath.lowercase())) {
            return true
        }
        if (isDataExistInDb(filePath)) {
            return true
        }
        return false
    }

    private fun updateFilePathToMetaData(metaData: GFileMetaData, newFilePath: String): Boolean {
        if (metaData.filePath == newFilePath) return false
        GLog.d(TAG) {
            if (BuildConfig.DEBUG) {
                "updateFilePath old:${metaData.filePath}, new:$newFilePath"
            } else {
                "updateFilePath old:${PathMask.mask(metaData.filePath)}, new:${PathMask.mask(newFilePath)}"
            }
        }
        metaData.filePath = newFilePath
        metaData.displayName = if (metaData.isRecycler()) {
            FilePathUtils.getDisplayName(RecyclePathAdapter.generateDataR(newFilePath) ?: newFilePath)
        } else {
            FilePathUtils.getDisplayName(newFilePath)
        }
        return true
    }

    private fun isDataExistInDb(originalData: String): Boolean {
        return QueryReq.Builder<Int>().setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(arrayOf(SQLGrammar.COUNT_ALL))
            .setWhere("${LocalColumns.DATA}=? and ${LocalColumns.INVALID} is not null and ${LocalColumns.IS_TRASHED} is not null")
            .setWhereArgs(arrayOf(originalData))
            .setConvert(CountAllConvert())
            .build()
            .exec() > 0
    }

    private fun toMetadataIdMap(metadataList: List<GFileMetaData>): MutableMap<String, GFileMetaData> {
        val metadataMap = mutableMapOf<String, GFileMetaData>()
        val notUploadedMetadataMap = mutableMapOf<String, GFileMetaData>()
        val trashedExpiredMetadataMap = mutableMapOf<String, GFileMetaData>()
        val duplicatedMetadataMap = mutableMapOf<String, GFileMetaData>()
        metadataList.forEach { metadata ->
            GLog.d(TAG) { "toMetadataIdMap file:$metadata" }
            // 这里有彻底删除只会出现本地有原图，但在云端彻底删了这张照片，在谷歌相册内用户同意彻底删之前一直都是-1状态，需要跳过
            val isDelete = metadata.cloudDataStatus == CloudColumns.CLOUD_DATA_STATUS_DELETE
            // 回收站超过30天的不导入
            if (isTrashedExpired(metadata) && !isDelete) {
                trashedExpiredMetadataMap[metadata.id] = metadata
                return@forEach
            }

            // 过滤media_id重复的
            val duplicatedMetaData = metadataMap[metadata.id]
            if ((duplicatedMetaData == null) || (duplicatedMetaData.version <= metadata.version)) {
                metadataMap[metadata.id] = metadata
            } else {
                duplicatedMetadataMap[metadata.id] = metadata
            }
        }
        GLog.d(TAG) {
            "toMetadataIdMap total:${metadataList.size} pending:${metadataMap.size} " +
                    "notUploaded:${notUploadedMetadataMap.size} " +
                    "trashedExpired:${trashedExpiredMetadataMap.size} " +
                    "duplicated:${duplicatedMetadataMap.size}"
        }
        return metadataMap
    }

    private fun isTrashedExpired(metaData: GFileMetaData): Boolean {
        return metaData.isRecycler() &&
                ((metaData.dateRecycled + TRASHED_MAX_RETENTION_TIME) < System.currentTimeMillis())
    }

    /**
     * 收集本地元数据id与之相同的记录，用于数据库更新
     * @param metadataIdMap 元数据集合，当本地找到有与之相同元数据id的记录，会将其从集合中删除放入updateList
     * @return 待更新的数据
     */
    private fun filterUpdateByMetadataId(metadataIdMap: MutableMap<String, GFileMetaData>): MutableList<Pair<GFileMetaData, GFileMetaData>> {
        val updateList: MutableList<Pair<GFileMetaData, GFileMetaData>> = mutableListOf()
        object : BatchProcess<String>(metadataIdMap.keys.toList(), SQLITE_MAX_VARIABLE_NUMBER) {
            override fun processOneBatch(items: List<String>): List<String> {
                queryLocalMetadata(
                    DatabaseUtils.getWhereQueryIn(CloudColumns.GLOBAL_ID, items.size) +
                            " and ${CloudColumns.IS_TRASHED} is not null" +
                            " and ${LocalColumns.INVALID} is not null",
                    items.toTypedArray()
                ).forEach { localData ->
                    metadataIdMap.remove(localData.id)?.let { cloudData ->
                        updateList.add(Pair(cloudData, localData))
                        if (isLocalDataNotMatchCloudData(localData, cloudData)) {
                            GLog.d(TAG) {
                                "filterUpdateByMetadataId, local not match cloud, will insert cloud data, " +
                                        "local:${localData.localFileSize}, cloud:${cloudData.size}, metadataId:${localData.id}"
                            }
                            return@forEach
                        }
                    } ?: GLog.e(TAG) { "filterUpdateByMetadataId, find same metadata id fail, id:${localData.id}" }
                }
                return emptyList()
            }
        }.process()
        return updateList
    }

    /**
     * 媒体数据同步不同步媒体库回收站的记录，需过滤出回收站的记录，用于插入或插入本地数据库
     * @param mediaIdMap 含媒体库id的元数据集合，当本地找到有与之相同媒体库id或元数据id的记录，会将其从集合中删除放入updateList和insertList
     * @param insertList 需要插入的数据集
     * @param updateList 需要更新的数据集
     */
    private fun filterForMediaStoreTrashed(
        mediaIdMap: MutableMap<String, GFileMetaData>,
        insertList: MutableList<GFileMetaData>,
        updateList: MutableList<Pair<GFileMetaData, GFileMetaData>>
    ) {
        val recycleMap = mutableMapOf<String, GFileMetaData>()
        val iterator = mediaIdMap.iterator()
        var metaData: GFileMetaData
        while (iterator.hasNext()) {
            metaData = iterator.next().value
            if (metaData.isRecycler()) {
                iterator.remove()
                recycleMap[metaData.id] = metaData
            }
        }
        var insertCount = 0
        var updateCount = 0
        object : BatchProcess<String>(recycleMap.keys.toList(), SQLITE_MAX_VARIABLE_NUMBER) {
            override fun processOneBatch(items: List<String>): List<String> {
                queryLocalMetadata(
                    DatabaseUtils.getWhereQueryIn(CloudColumns.GLOBAL_ID, items.size) +
                            " and ${CloudColumns.IS_TRASHED} is not null" +
                            " and ${LocalColumns.INVALID} is not null",
                    items.toTypedArray()
                ).forEach { localData ->
                    recycleMap.remove(localData.id)?.let { cloudData ->
                        if ((localData.mediaId <= 0) && (cloudData.mediaIds?.size == 1)) {
                            cloudData.mediaId = cloudData.mediaIds?.let { it[0] } ?: 0
                        }
                        updateList.add(Pair(cloudData, localData))
                        updateCount++
                    } ?: GLog.e(TAG) { "filterForMediaStoreTrashed find same metadata id fail, id:${localData.id}" }
                }
                return emptyList()
            }
        }.process()
        if (recycleMap.isNotEmpty()) {
            val remainList = recycleMap.values
            insertList.addAll(remainList)
            insertCount = remainList.size
        }
        GLog.d(TAG, "filterForMediaStoreTrashed insertCount:$insertCount, updateCount:$updateCount")
    }

    /**
     * 对于含媒体库id的元数据，收集本地媒体库记录id与之相同的记录，用于数据库更新
     * @param mediaIdMap 含媒体库id的元数据集合，当本地找到有与之相同媒体库id的记录，会将其从集合中删除放入updateList
     * @return 需要更新的数据集
     */
    private fun filterUpdateByMediaId(mediaIdMap: MutableMap<String, GFileMetaData>): MutableList<Pair<GFileMetaData, GFileMetaData>> {
        val updateList: MutableList<Pair<GFileMetaData, GFileMetaData>> = mutableListOf()
        object : BatchProcess<String>(mediaIdMap.keys.toList(), SQLITE_MAX_VARIABLE_NUMBER) {
            override fun processOneBatch(items: List<String>): List<String> {
                queryLocalMetadata(
                    DatabaseUtils.getWhereQueryIn(CloudColumns.MEDIA_ID, items.size) +
                            " and ${CloudColumns.IS_TRASHED} is not null" +
                            " and ${LocalColumns.INVALID} is not null",
                    items.toTypedArray()
                ).forEach { localData ->
                    mediaIdMap.remove(localData.mediaId.toString())?.let { cloudData ->
                        updateList.add(Pair(cloudData, localData))
                    } ?: GLog.e(TAG) { "filterUpdateByMediaId find same media id fail, id:${localData.id}" }
                }
                return emptyList()
            }
        }.process()
        return updateList
    }

    private fun isLocalDataNotMatchCloudData(localMetadata: GFileMetaData, cloudMetadata: GFileMetaData): Boolean {
        return (localMetadata.uploadStatus == FILE_UPLOADED) &&
                (localMetadata.localFileStatus == LOCAL_FILE_STATUS_ORIGINAL) &&
                (localMetadata.localFileSize != cloudMetadata.size)
    }

    /**
     * 对比谷歌和本地元数据，获取本地那些被触发媒体库恢复的记录
     */
    private fun getRestoredLocalMetaDataList(
        updateList: MutableList<Pair<GFileMetaData, GFileMetaData>>
    ): List<GFileMetaData> {
        val result = mutableListOf<GFileMetaData>()
        var cloudMetaData: GFileMetaData
        var localMetaData: GFileMetaData
        updateList.forEach {
            cloudMetaData = it.first
            localMetaData = it.second
            if (cloudMetaData.isInMediaStore() &&
                cloudMetaData.isRecycler().not() &&
                localMetaData.cloudDataStatus == CLOUD_DATA_STATUS_RECYCLE &&
                localMetaData.isRecycler()
            ) {
                result.add(localMetaData)
            }
        }
        return result
    }

    private fun queryLocalMetadata(where: String, whereArgs: Array<String>): List<GFileMetaData> {
        return QueryReq.Builder<List<GFileMetaData>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(LOCAL_COVERT.LOCAL_PROJECTION)
            .setWhere(where)
            .setWhereArgs(whereArgs)
            .setConvert(LOCAL_COVERT)
            .build()
            .exec()
    }

    /**
     * 文件元数据存入数据库
     * @param metadataList 文件元数据
     */
    private fun insertFileMetadata(metadataList: MutableList<GFileMetaData>) {
        if (metadataList.isEmpty()) return
        val count = BulkInsertReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setConvert {
                toInsertValuesList(metadataList).toTypedArray()
            }
            .build()
            .exec()
        GLog.d(TAG) { "insertFileMetadata count:$count" }
    }

    /**
     * 将识别为新增的元数据记录，转换成插入本地数据库的数据集
     * @param metadataList 元数据集合
     */
    private fun toInsertValuesList(metadataList: MutableList<GFileMetaData>): List<ContentValues> {
        val valuesList = mutableListOf<ContentValues>()
        // 收集在媒体库的回收站记录，需从媒体库查询，赋值其真实文件路径
        val mediaStoreTrashedMap = mutableMapOf<Long, ContentValues>()
        metadataList.forEach { metadata ->
            if (metadata.isRecycler() && metadata.isInMediaStore()) {
                metadata.mediaIds?.forEach {
                    // 必须在toInsertContentValues前重新赋值mediaId
                    metadata.mediaId = it
                    mediaStoreTrashedMap[it] = createInsertValues(metadata)
                }
            } else {
                valuesList.add(createInsertValues(metadata))
            }
        }
        val mediaStoreTrashedCount = mediaStoreTrashedMap.size
        if (mediaStoreTrashedCount > 0) {
            queryTrashedDataFromMediaStore(
                DatabaseUtils.getWhereQueryIn(MediaStore.Files.FileColumns._ID, mediaStoreTrashedMap.size),
                DatabaseUtils.getWhereArgs(mediaStoreTrashedMap.keys)
            ).forEach {
                mediaStoreTrashedMap.remove(it.first)?.let { values ->
                    // 赋值真实的回收站文件路径
                    values.put(LocalColumns.DATA, it.second)
                    valuesList.add(values)
                }
            }
            if (mediaStoreTrashedMap.isNotEmpty()) {
                GLog.e(TAG, "toInsertValuesList has invalid trashed metadata:${mediaStoreTrashedMap.values}")
            }
        }
        GLog.d(TAG) { "toInsertValuesList totalCount:${valuesList.size}, mediaStoreTrashedCount:$mediaStoreTrashedCount" }
        return valuesList
    }

    private fun createInsertValues(metadata: GFileMetaData): ContentValues {
        return ContentValues().apply {
            put(CloudColumns.GLOBAL_ID, metadata.id)
            put(CloudColumns.DATE_TAKEN, metadata.datetaken)
            // google的元数据没有date_modified字段，用date_taken代替
            put(CloudColumns.DATE_MODIFIED, metadata.datetaken / TimeUtils.MILLISECOND_IN_SECOND)
            put(CloudColumns.DISPLAY_NAME, metadata.displayName)
            put(CloudColumns.FILE_ID, metadata.uniqueKey)
            put(CloudColumns.DURATION, metadata.duration)
            put(CloudColumns.CLOUD_WIDTH, metadata.width)
            put(CloudColumns.CLOUD_HEIGHT, metadata.height)
            put(CloudColumns.CLOUD_SIZE, metadata.size)
            put(CloudColumns.MIME_TYPE, metadata.mimeType)
            put(CloudColumns.MEDIA_TYPE, metadata.mediaType)
            put(CloudColumns.IS_TRASHED, metadata.trashStatus)
            put(CloudColumns.DATE_RECYCLED, metadata.dateRecycled)
            put(CloudColumns.DATA, metadata.filePath)
            put(CloudColumns.RELATIVE_PATH, GFileMetaData.DEFAULT_FILE_RELATIVE_PATH)
            put(CloudColumns.BUCKET_NAME, GFileMetaData.DEFAULT_FILE_DIR_NAME)
            put(CloudColumns.BUCKET_ID, GFileMetaData.DEFAULT_BUCKET_ID)
            put(CloudColumns.CLOUD_DATA_STATUS, metadata.cloudDataStatus)
            // mark by liumingfu 临时将cloud_key填入MD5
            put(CloudColumns.MD5, metadata.uniqueKey)
            put(CloudColumns.LOCAL_FILE_STATUS, metadata.localFileStatus)
            put(LocalColumns.IS_FAVORITE, metadata.favoriteStatus)
            if (metadata.mediaId > 0) {
                put(LocalColumns.MEDIA_ID, metadata.mediaId)
            }
        }
    }

    /**
     * 更新元数据到本地数据库
     * @param updateList 需要更新的数据集合，第一个是云端元数据，第二个是本地认为与之匹配的元数据
     */
    private fun updateFileMetadata(updateList: List<Pair<GFileMetaData, GFileMetaData>>) {
        if (updateList.isEmpty()) return

        val localUpdateReqList = ArrayList<UpdateReq>()
        val mediaStoreUpdateReqList = ArrayList<ContentProviderOperation>()
        val favoriteUpdateList = mutableListOf<String>()
        // 将updateList分组：更新本地数据库、更新媒体库、同步收藏到云端
        groupUpdateData(updateList, localUpdateReqList, mediaStoreUpdateReqList, favoriteUpdateList)
        // 更新本地数据库
        if (localUpdateReqList.isEmpty()) return
        val resultsForGallery = BatchReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .addDataReqs(localUpdateReqList)
            .build()
            .exec()
        // 更新媒体库
        var resultsForMediaStore: Array<ContentProviderResult>? = null
        runCatching {
            resultsForMediaStore = ContextGetter.context.contentResolver.applyBatch(MediaStore.AUTHORITY, mediaStoreUpdateReqList)
        }.onFailure {
            GLog.e(TAG, "updateFileMetadata update MediaStore fail, error", it)
        }
        GLog.d(TAG) {
            "updateFileMetadata galleryStoreCount:${resultsForGallery.size}, mediaStoreCount:${resultsForMediaStore?.size ?: 0}"
        }
        // 云端数据同步到本地后，本地收藏状态同步到云端
        if (favoriteUpdateList.isNotEmpty()) GCloudSyncApi.favorite(favoriteUpdateList)
    }

    private fun groupUpdateData(
        updateList: List<Pair<GFileMetaData, GFileMetaData>>,
        localUpdateReqList: MutableList<UpdateReq>,
        mediaStoreUpdateReqList: MutableList<ContentProviderOperation>,
        favoriteUpdateList: MutableList<String>
    ) {
        val trashedMap = mutableMapOf<Long, ContentValues>()
        updateList.forEach {
            val cloudMetadata = it.first
            val localMetadata = it.second
            val values = createUpdateValues(cloudMetadata, localMetadata, favoriteUpdateList)
            if (values.size() <= 0) return@forEach

            localUpdateReqList.add(
                UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .apply {
                        if (localMetadata.mediaId > 0) {
                            // 已进入媒体库的，本地该媒体库id必然只有一条，但不同的媒体库记录如果对应的文件相同，元数据id则指向同一个
                            setWhere("${CloudColumns.MEDIA_ID}=${localMetadata.mediaId}")
                        } else {
                            // 还没有进入媒体库的，本地该元数据id必然只有一条
                            setWhere("${CloudColumns.GLOBAL_ID}=?")
                            setWhareArgs(arrayOf(localMetadata.id))
                        }
                    }
                    .setConvert { values }
                    .build()
            )
            // 收集需要更新trashed字段的媒体库id，用来更新媒体库，触发文件移动
            if (values.containsKey(LocalColumns.IS_TRASHED)) {
                cloudMetadata.mediaIds?.toList()?.forEach { mediaId ->
                    val isTrashed = values.getAsInteger(LocalColumns.IS_TRASHED)
                    if (isTrashed == LocalColumns.IS_TRASHED_TRUE) {
                        trashedMap[mediaId] = values
                    }
                    mediaStoreUpdateReqList.add(
                        ContentProviderOperation.newUpdate(
                            MediaStoreUriHelper.getUri(mediaId.toString(), MediaStore.VOLUME_EXTERNAL, localMetadata.mediaType)
                        ).withValues(
                            ContentValues().apply { put(LocalColumns.IS_TRASHED, isTrashed) }
                        ).build()
                    )
                }
            } else if (cloudMetadata.isInMediaStore() && localMetadata.isRecycler()) {
                // 本地已在回收站的，可能也会发生缩图到原图，也要更新文件路径
                cloudMetadata.mediaIds?.forEach { mediaId ->
                    trashedMap[mediaId] = values
                }
            }
        }

        val trashedCount = trashedMap.size
        if (trashedCount > 0) {
            queryTrashedDataFromMediaStore(
                DatabaseUtils.getWhereQueryIn(MediaStore.Files.FileColumns._ID, trashedMap.size),
                DatabaseUtils.getWhereArgs(trashedMap.keys)
            ).forEach { updateData ->
                trashedMap.remove(updateData.first)?.put(LocalColumns.DATA, updateData.second)
            }
            if (trashedMap.isNotEmpty()) {
                GLog.e(TAG, "groupUpdateData error trashed data:${trashedMap.values}")
            }
        }
        GLog.d(TAG) { "groupUpdateData has trashed count:$trashedCount" }
    }

    /**
     * 清理本地记录中mediaid为空的冗余记录。
     *
     * 1. google元数据下发时，文件没有下载到本地，故没有绑定mediaid
     * 2. 文件下载后，媒体数据同步会插入本地记录到数据库，此时数据库就会有两条记录
     * 3. google元数据绑定好mediaid后，相册同步过来，优先覆盖有mediaid的记录，此时第1步插入的记录就是冗余记录，需要清理掉
     */
    private fun clearInvalidDataWithoutMediaId(metadataList: List<GFileMetaData>) {
        object : BatchProcess<GFileMetaData>(metadataList, SQLITE_MAX_VARIABLE_NUMBER) {
            override fun processOneBatch(items: List<GFileMetaData>): List<GFileMetaData> {
                val count = DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(
                        "${LocalColumns.MEDIA_ID}<=0" +
                                " and ${LocalColumns.IS_TRASHED}=${LocalColumns.IS_TRASHED_FALSE}" +
                                " and ${DatabaseUtils.getWhereQueryIn(CloudColumns.GLOBAL_ID, items.size)}"
                    )
                    .setWhereArgs(items.map { it.id }.toTypedArray())
                    .build().exec()
                GLog.d(TAG) { "clearInvalidDataWithoutMediaId, expectDeleteCount:${items.size}, actualDeleteCount:$count" }
                return emptyList()
            }
        }.process()
        if (metadataList.isNotEmpty()) {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        }
    }

    private fun createUpdateValues(
        cloudMetadata: GFileMetaData,
        localMetadata: GFileMetaData,
        localFavoriteList: MutableList<String>
    ): ContentValues {
        if (isLocalDataNotMatchCloudData(localMetadata, cloudMetadata)) {
            GLog.d(TAG) {
                "createUpdateValues, local data not match cloud data, do reset. " +
                        "local size:${localMetadata.size}, cloud size:${cloudMetadata.size}, " +
                        "local id:${localMetadata.id}, cloud id:${cloudMetadata.id}"
            }
            return getClearCloudColumnsUpdateValues()
        }

        val values = ContentValues()
        if (cloudMetadata.id != localMetadata.id) {
            values.put(CloudColumns.GLOBAL_ID, cloudMetadata.id)
        }
        if (cloudMetadata.uniqueKey != localMetadata.uniqueKey) {
            values.put(CloudColumns.FILE_ID, cloudMetadata.uniqueKey)
        }
        if (cloudMetadata.size != localMetadata.size) {
            values.put(CloudColumns.CLOUD_SIZE, cloudMetadata.size)
        }
        if (cloudMetadata.uploadStatus != localMetadata.uploadStatus) {
            values.put(CloudColumns.FILE_UPLOAD_STATUS, cloudMetadata.uploadStatus)
        }

        updateValueWithTrashed(values, cloudMetadata, localMetadata)

        if ((localMetadata.invalid != LocalColumns.INVALID_NORMAL) &&
            GCloudSyncApi.getConfirmedDeleteMetaDataIds().contains(cloudMetadata.id).not() &&
            localMetadata.isSafeBoxFile().not()
        ) {
            values.put(LocalColumns.INVALID, LocalColumns.INVALID_NORMAL)
        }

        // mark by liumingfu 当前谷歌流程不需要MD5，但DownloadData和CloudMediaBitmapRequester都不允许其为null，临时将cloud_key填入MD5
        values.put(CloudColumns.MD5, cloudMetadata.uniqueKey)

        if ((cloudMetadata.localFileStatus != localMetadata.localFileStatus) &&
            (cloudMetadata.localFileStatus == LOCAL_FILE_STATUS_ORIGINAL)) {
            values.put(CloudColumns.LOCAL_FILE_STATUS, cloudMetadata.localFileStatus)
        }

        updateValuesWithFavorite(values, cloudMetadata, localMetadata, localFavoriteList)

        if ((localMetadata.mediaId <= 0) && (cloudMetadata.mediaId > 0)) {
            values.put(LocalColumns.MEDIA_ID, cloudMetadata.mediaId)
        }

        // 本地有媒体库记录的代表文件已下载，以媒体库为准
        if (cloudMetadata.isInMediaStore()) return values

        updateValuesWhenOnlyInCloud(values, cloudMetadata, localMetadata)
        return values
    }

    private fun updateValuesWithFavorite(
        values: ContentValues,
        cloudMetadata: GFileMetaData,
        localMetadata: GFileMetaData,
        localFavoriteList: MutableList<String>
    ) {
        if (cloudMetadata.favoriteStatus != localMetadata.favoriteStatus) {
            // 本地数据已经同步过，则用云端覆盖本地，否则取云端和本地的并集，尽量保存收藏状态
            if (localMetadata.isCloudData()) {
                values.put(LocalColumns.IS_FAVORITE, cloudMetadata.favoriteStatus)
            } else if (cloudMetadata.isFavorite()) {
                values.put(LocalColumns.IS_FAVORITE, LocalColumns.STATE_IN_FAVORITED)
            } else if (localMetadata.isFavorite()) {
                values.put(LocalColumns.IS_FAVORITE, LocalColumns.STATE_IN_FAVORITED)
                localFavoriteList.add(cloudMetadata.id)
            } else {
                values.put(LocalColumns.IS_FAVORITE, LocalColumns.STATE_IN_UNFAVORITED)
            }
        }
    }

    private fun updateValuesWhenOnlyInCloud(
        values: ContentValues,
        cloudMetadata: GFileMetaData,
        localMetadata: GFileMetaData
    ) {
        if (localMetadata.isSafeBoxFile()) {
            // 云端变成缩图，但此前本地是在私密保险箱，则不能显示出来
            GLog.w(TAG, "updateValuesWhenOnlyInCloud, file was moved into the safe box by the file manager:${PathMask.mask(localMetadata.filePath)}")
            if (DatabaseUtils.isDataValid(localMetadata.invalid)) {
                values.put(LocalColumns.INVALID, LocalColumns.INVALID_EXTERNAL_MOVE_TO_SAFE_BOX)
            }
            if (localMetadata.mediaId != 0L) {
                values.put(CloudColumns.MEDIA_ID, 0)
            }
        } else if (localMetadata.isInMediaStore()) {
            // 云端认为不是媒体库的记录了，则将media_id=0和data=含随机文件的路径，防止数据同步查到又将其命中进行隐藏或更新
            values.put(CloudColumns.MEDIA_ID, 0)
            values.put(CloudColumns.DATA, cloudMetadata.filePath)
        }

        if (cloudMetadata.datetaken != localMetadata.datetaken) {
            values.put(CloudColumns.DATE_TAKEN, cloudMetadata.datetaken)
        }
        if (cloudMetadata.displayName != localMetadata.displayName) {
            values.put(CloudColumns.DISPLAY_NAME, cloudMetadata.displayName)
        }
        if (cloudMetadata.duration != localMetadata.duration) {
            values.put(CloudColumns.DURATION, cloudMetadata.duration)
        }
        if (cloudMetadata.width != localMetadata.width) {
            values.put(CloudColumns.CLOUD_WIDTH, cloudMetadata.width)
        }
        if (cloudMetadata.height != localMetadata.height) {
            values.put(CloudColumns.CLOUD_HEIGHT, cloudMetadata.height)
        }
        if (cloudMetadata.mimeType != localMetadata.mimeType) {
            values.put(CloudColumns.MIME_TYPE, cloudMetadata.mimeType)
        }
        if (cloudMetadata.mediaType != localMetadata.mediaType) {
            values.put(CloudColumns.MEDIA_TYPE, cloudMetadata.mediaType)
        }
        updateValuesWithLocalFileStatus(values, cloudMetadata, localMetadata)
    }

    private fun updateValuesWithLocalFileStatus(
        values: ContentValues,
        cloudMetadata: GFileMetaData,
        localMetadata: GFileMetaData
    ) {
        /*
        cloudMetadata.localFileStatus只能判断是否是原图，如果是原图则取LOCAL_FILE_STATUS_ORIGINAL，否则取LOCAL_FILE_STATUS_METADATA，
        所以只有在是否原图有变化的情况下才更新数据库LOCAL_FILE_STATUS字段
         */
        if (((cloudMetadata.localFileStatus == LOCAL_FILE_STATUS_ORIGINAL) && (localMetadata.localFileStatus != LOCAL_FILE_STATUS_ORIGINAL))
            || ((cloudMetadata.localFileStatus != LOCAL_FILE_STATUS_ORIGINAL) && (localMetadata.localFileStatus == LOCAL_FILE_STATUS_ORIGINAL))) {
            values.put(CloudColumns.LOCAL_FILE_STATUS, cloudMetadata.localFileStatus)
        }
    }

    private fun getClearCloudColumnsUpdateValues(): ContentValues {
        return ContentValues().apply {
            putNull(CloudColumns.GLOBAL_ID)
            putNull(CloudColumns.FILE_ID)
            putNull(CloudColumns.MD5)
            putNull(CloudColumns.CLOUD_SIZE)
            putNull(CloudColumns.CLOUD_WIDTH)
            putNull(CloudColumns.CLOUD_HEIGHT)
            putNull(CloudColumns.CLOUD_DATA_STATUS)
            put(CloudColumns.FILE_DOWNLOAD_STATUS, CloudSyncStore.FileDownloadStatus.FILE_DOWNLOAD_WAITING)
            put(CloudColumns.FILE_UPLOAD_STATUS, CloudSyncStore.FileUploadStatus.FILE_UPLOAD_WAITING)
            putNull(CloudColumns.FILE_SYNC_COUNT)
            putNull(CloudColumns.FILE_SYNC_TIME)
            put(CloudColumns.OPERATION, CloudSyncStore.Operation.OPERATION_NONE)
            put(CloudColumns.SYNC_PERCENT, 0)
            put(CloudColumns.LATEST_FILE_USAGE_TIME, 0)
            put(CloudColumns.LOCAL_FILE_STATUS, LOCAL_FILE_STATUS_ORIGINAL)
            put(CloudColumns.IS_SUPPORT_COMPRESSED, CloudSyncStore.IsSupportCompressed.NO)
            put(CloudColumns.AUTO_DOWNLOAD_ORIGIN, CloudSyncStore.IsAutoDownload.FILE_AUTO_DOWNLOAD_NO)
            putNull(CloudColumns.CLOUD_DATA_STATUS)
        }
    }

    /**
     * [CloudColumns.CLOUD_DATA_STATUS]
     * 代表元数据在云端的位置状态（在回收站、彻底删除、在正常路径），不随用户操作变化，以云端为准
     * [CloudColumns.IS_TRASHED]
     * 代表元数据在本地的位置状态（在回收站、在正常路径）初始值根据CLOUD_DATA_STATUS，后续以用户操作为准
     *
     * 如果CLOUD_DATA_STATUS和IS_TRASHED都代表在回收站或都是非回收站，说明代表用户的移入/恢复操作已同步，
     * 就可以同时更新两个字段，否则只能更新CLOUD_DATA_STATUS。
     * 因为后续备份流程需要从IS_TRASHED和CLOUD_DATA_STATUS不一致，识别这条记录的回收状态需同步给谷歌，同步完，再从谷歌拉元数据时，
     * CLOUD_DATA_STATUS就会和它一致。
     */
    private fun updateValueWithTrashed(
        values: ContentValues,
        cloudMetadata: GFileMetaData,
        localMetadata: GFileMetaData
    ) {
        if (cloudMetadata.isRecycler()) {
            updateValueWhenCloudInTrashed(values, cloudMetadata, localMetadata)
        } else {
            updateValueWhenCloudNotInTrashed(values, localMetadata)
        }
    }

    private fun updateValueWhenCloudInTrashed(
        values: ContentValues,
        cloudMetadata: GFileMetaData,
        localMetadata: GFileMetaData
    ) {
        // 云端要移入回收站
        if (localMetadata.cloudDataStatus != CLOUD_DATA_STATUS_RECYCLE) {
            values.put(CloudColumns.CLOUD_DATA_STATUS, CLOUD_DATA_STATUS_RECYCLE)
            // 如果原云端回收站状态和本地回收站状态都是已恢复，说明用户操作都已同步，可以直接更新
            if (localMetadata.isRecycler().not()) {
                values.put(CloudColumns.IS_TRASHED, IS_TRASHED_TRUE)
                if (cloudMetadata.dateRecycled != localMetadata.dateRecycled) {
                    values.put(CloudColumns.DATE_RECYCLED, cloudMetadata.dateRecycled)
                }
            }
            return
        }

        if (localMetadata.isRecycler().not() && localMetadata.isInMediaStore() && localMetadata.isFileExists().not()) {
            /* 解决场景：当一张照片存在重复副本且云端处于回收站状态，此时oppo相册恢复了其中一张，
            被恢复的照片又被三方删入回收站，此时需要将其更新成回收站状态 */
            values.put(CloudColumns.IS_TRASHED, IS_TRASHED_TRUE)
            if (cloudMetadata.dateRecycled != localMetadata.dateRecycled) {
                values.put(CloudColumns.DATE_RECYCLED, cloudMetadata.dateRecycled)
            }
            GLog.w(TAG, "updateValueWhenCloudInTrashed error data recycle, localMetadata:$localMetadata")
        }
    }

    private fun updateValueWhenCloudNotInTrashed(
        values: ContentValues,
        localMetadata: GFileMetaData
    ) {
        // 云端要从回收站恢复。cloudDataStatus是可null的，需要检查
        if (localMetadata.cloudDataStatus != CLOUD_DATA_STATUS_NORMAL) {
            values.put(CloudColumns.CLOUD_DATA_STATUS, CLOUD_DATA_STATUS_NORMAL)
        }
        // 本地记录的原云端回收站状态是否为恢复状态
        val isNormalInCloudFromLocal = (localMetadata.cloudDataStatus == null) ||
                (localMetadata.cloudDataStatus == CLOUD_DATA_STATUS_NORMAL)
        // 如果原云端回收站状态和本地回收站状态都是已回收，说明用户操作都已同步，可以直接更新
        if (isNormalInCloudFromLocal.not() && localMetadata.isRecycler()) {
            values.put(CloudColumns.IS_TRASHED, IS_TRASHED_FALSE)
            if (localMetadata.dateRecycled != 0L) {
                values.put(CloudColumns.DATE_RECYCLED, 0)
            }
            // 不在媒体库的记录被恢复时，需要更新下绝对路径
            updateFilePathNotInMediaStoreWhenRestored(values, localMetadata)
            return
        }

        if (localMetadata.isRecycler() && localMetadata.isInMediaStore() && localMetadata.isFileExists().not()) {
            /* 解决场景：当一张照片存在重复副本且云端处于非回收站状态，此时oppo相册删除了其中一张，
            被删除的照片又被三方恢复，此时需要将其彻底删除，等媒体库数据同步恢复它 */
            val count = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.CLOUD_MEDIA)
                .setWhere("${LocalColumns._ID}=${localMetadata.localId}")
                .build()
                .exec()
            GLog.w(TAG) { "updateValueWhenCloudNotInTrashed error data delete, deleteCount:$count, localMetadata:$localMetadata" }
        }
    }

    private fun updateFilePathNotInMediaStoreWhenRestored(values: ContentValues, localMetadata: GFileMetaData) {
        // 在媒体库的这里不处理，以媒体库为准，媒体数据同步会处理
        if (localMetadata.isInMediaStore()) return
        val newFilePath = RecyclePathAdapter.generateDataR(localMetadata.filePath)
        if (newFilePath.isNullOrEmpty() || localMetadata.filePath.equals(newFilePath, true)) return
        values.put(LocalColumns.DATA, newFilePath)
        GLog.d(TAG) {
            "updateFilePathNotInMediaStoreWhenRestored oldFilePath:${PathMask.mask(localMetadata.filePath)}, " +
                    "newFilePath:${PathMask.mask(newFilePath)}"
        }
    }

    /**
     * 从媒体库查询回收站文件的路径
     * @param where 查询条件
     * @param whereArgs 查询条件的参数
     * @return 返回装填media_id和回收站绝对路径的[Pair]数组
     */
    private fun queryTrashedDataFromMediaStore(where: String, whereArgs: Array<String>): List<Pair<Long, String>> {
        val bundle = Bundle()
        bundle.putInt(DatabaseUtils.QUERY_ARG_MATCH_TRASHED, LocalColumns.IS_TRASHED_TRUE)
        bundle.putString(DatabaseUtils.QUERY_ARG_SQL_SELECTION, where)
        bundle.putStringArray(DatabaseUtils.QUERY_ARG_SQL_SELECTION_ARGS, whereArgs)
        // 媒体库要求，需要这个key才能查询到错峰图，方案来源于********
        bundle.putInt(DatabaseUtils.QUERY_ARG_MATCH_PENDING, MediaStore.MATCH_INCLUDE)
        kotlin.runCatching {
            ContextGetter.context.contentResolver.query(
                MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL),
                arrayOf(MediaStore.Files.FileColumns._ID, MediaStore.Files.FileColumns.DATA),
                bundle,
                null
            )?.use {
                val list = mutableListOf<Pair<Long, String>>()
                while (it.moveToNext()) {
                    list.add(Pair(it.getLong(0), it.getString(1)))
                }
                return list
            } ?: GLog.e(TAG, "queryTrashedDataFromMediaStore cursor is null")
        }
        return emptyList()
    }

    /**
     * 删除媒体库记录，也删除本地的
     * @param mediaIds 要删除的媒体库id集合
     */
    private fun deleteMediaStoreAndLocalMedia(mediaIds: List<Long>) {
        if (mediaIds.isEmpty()) return
        val mediaStoreCount = DeleteReq.Builder()
            .setDaoType(IDao.DaoType.MEDIA_STORE)
            .setTableType(MediaStoreDbDao.TableType.FILES)
            .setWhere(DatabaseUtils.getExternalWhereIn(MediaStore.Files.FileColumns._ID, mediaIds))
            .build()
            .exec()
        val localCount = DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.CLOUD_MEDIA)
            .setWhere(DatabaseUtils.getExternalWhereIn(LocalColumns.MEDIA_ID, mediaIds))
            .build()
            .exec()
        GLog.d(TAG) {
            "deleteMediaStoreAndLocalMedia mediaIds:${mediaIds.joinToString(TextUtil.SPLIT_COMMA_SEPARATOR)}, " +
                    "mediaStoreCount:$mediaStoreCount, " +
                    "localCount:$localCount, "
        }
    }

    /**
     * 删除元数据
     * @param metadataIds 要删除的元数据的id集合
     */
    fun deleteMetaData(metadataIds: List<String>) {
        object : BatchProcess<String>(metadataIds, SQLITE_MAX_VARIABLE_NUMBER) {
            override fun processOneBatch(items: List<String>): List<String> {
                val count = DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(DatabaseUtils.getWhereQueryIn(CloudColumns.GLOBAL_ID, items.size))
                    .setWhereArgs(items.toTypedArray())
                    .build().exec()
                GLog.d(TAG) { "deleteMetaData deleteCount:${items.size}, realDeleteCount:$count" }
                return emptyList()
            }
        }.process()
        if (metadataIds.isNotEmpty()) {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        }
    }

    /**
     * 清空所有元数据
     */
    fun clearMetaData() {
        val deleteCount = DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setWhere("${CloudColumns.GLOBAL_ID} is not null and ${CloudColumns.MEDIA_ID}<=0")
            .build()
            .exec()
        val updateCount = UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setWhere("${CloudColumns.GLOBAL_ID} is not null and ${CloudColumns.MEDIA_ID}>0")
            .setConvert {
                ContentValues().apply {
                    putNull(CloudColumns.GLOBAL_ID)
                    putNull(CloudColumns.FILE_ID)
                }
            }
            .build()
            .exec()
        // 两次通知合并成一次发
        if (deleteCount + updateCount > 0) {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        }
        GLog.d(TAG) { "clearMetaData deleteCount:$deleteCount, updateCount:$updateCount" }
    }

    /**
     * 更新文件上传状态
     * @param cloudGlobalIdList 云端文件id集合
     * @param oldFileUploadStatus 更新前的文件上传状态
     * @param newFileUploadStatus 更新后的文件上传状态
     */
    @WorkerThread
    private fun updateFileUploadStatus(cloudGlobalIdList: Collection<String>, oldFileUploadStatus: Int, newFileUploadStatus: Int) {
        UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setWhere(
                DatabaseUtils.getWhereIn(CloudColumns.GLOBAL_ID, cloudGlobalIdList) +
                        AND +
                        "${CloudColumns.FILE_UPLOAD_STATUS}=$oldFileUploadStatus"
            )
            .setWhareArgs(null)
            .setConvert {
                ContentValues().apply {
                    put(CloudColumns.FILE_UPLOAD_STATUS, newFileUploadStatus)
                }
            }
            .build()
            .exec()
    }

    /**
     * 更新文件上传状态从待上传到上传中
     * @param cloudGlobalIdList 云端文件id集合
     */
    @WorkerThread
    fun updateFromFileUploadWaitingToFileUploading(cloudGlobalIdList: Collection<String>) {
        updateFileUploadStatus(
            cloudGlobalIdList = cloudGlobalIdList,
            oldFileUploadStatus = CloudSyncStore.FileUploadStatus.FILE_UPLOAD_WAITING,
            newFileUploadStatus = CloudSyncStore.FileUploadStatus.FILE_UPLOADING
        )
    }

    /**
     * 更新文件上传状态从上传中到待上传
     * @param cloudGlobalIdList 云端文件id集合
     */
    @WorkerThread
    fun updateFromFileUploadingToFileUploadWaiting(cloudGlobalIdList: Collection<String>) {
        updateFileUploadStatus(
            cloudGlobalIdList = cloudGlobalIdList,
            oldFileUploadStatus = CloudSyncStore.FileUploadStatus.FILE_UPLOADING,
            newFileUploadStatus = CloudSyncStore.FileUploadStatus.FILE_UPLOAD_WAITING
        )
    }
}