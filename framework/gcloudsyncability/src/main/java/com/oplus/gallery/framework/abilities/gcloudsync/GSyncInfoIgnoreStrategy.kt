/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : GSyncInfoIgnoreStrategy.kt
 ** Description : 谷歌云的同步信息忽略策略
 ** Version     : 1.0
 ** Date        : 2023/11/22
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>        <version>         <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              22023/11/22      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.gcloudsync

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.cloudsync.SyncStateInfo
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 谷歌云的同步信息忽略策略，可配置忽略次数对应的不再出现时间，未配置则使用默认值
 */
class GSyncInfoIgnoreStrategy(
    private val configID: String,
    /**
     * 第一次忽略的有效时间，默认1天
     */
    private val onceIgnoreEffectiveTimeInMs: Long = DEFAULT_ONCE_IGNORE_EFFECTIVE_TIME_IN_MS,
    /**
     * 第二次忽略的有效时间，默认3天
     */
    private val twiceIgnoreEffectiveTimeInMs: Long = DEFAULT_TWICE_IGNORE_EFFECTIVE_TIME_IN_MS,
    /**
     * 第三次及以上忽略的有效时间，默认永久
     */
    private val moreThanTwiceIgnoreEffectiveTimeInMs: Long = DEFAULT_MORE_THAN_TWICE_IGNORE_EFFECTIVE_TIME_IN_MS
) : SyncStateInfo.ISyncInfoIgnoreStrategy {
    override fun needIgnoreSyncInfo(): Boolean {
        val currentTime = System.currentTimeMillis()
        val times = getLastIgnoreTime()
        return when {
            times == null -> false // 未忽略过，立即出现
            (times[0] == COUNT_ONE) && ((currentTime - times[1]) > onceIgnoreEffectiveTimeInMs) -> false
            (times[0] == COUNT_TWO) && ((currentTime - times[1]) > twiceIgnoreEffectiveTimeInMs) -> false
            (times[0] > COUNT_TWO) && ((currentTime - times[1]) > moreThanTwiceIgnoreEffectiveTimeInMs) -> false
            else -> true
        }.also {
            GLog.d(TAG, "needIgnoreSyncInfo, $it, config id = $configID")
        }
    }

    override fun markUserIgnoreTime() {
        val currentTime = System.currentTimeMillis()
        val times = getLastIgnoreTime()
        var value: String? = null
        when {
            times == null -> value = "1${SEPARATOR}$currentTime"  // 第1次点击忽略
            times[0] == 1L -> value = "2${SEPARATOR}$currentTime" // 第2次点击忽略
            times[0] == 2L -> value = "3${SEPARATOR}$currentTime" // 第3次点击忽略
        }
        ContextGetter.context.getAppAbility<ISettingsAbility>()?.use { it.markIgnoreTimes(configID, value) }
    }

    /**
     * 获取上次点击忽略的时间
     */
    private fun getLastIgnoreTime(): LongArray? {
        return ConfigAbilityWrapper.getString(configID)?.run {
            val values = this.split(SEPARATOR)
            longArrayOf(values[0].toLong(), values[1].toLong())
        }
    }

    companion object {
        private const val TAG = "GSyncInfoIgnoreStrategy"
        private const val DEFAULT_ONCE_IGNORE_EFFECTIVE_TIME_IN_MS = TimeUtils.TIME_1_DAY_IN_MS
        private const val DEFAULT_TWICE_IGNORE_EFFECTIVE_TIME_IN_MS = TimeUtils.TIME_3_DAY_IN_MS
        private const val DEFAULT_MORE_THAN_TWICE_IGNORE_EFFECTIVE_TIME_IN_MS = Long.MAX_VALUE
        private const val SEPARATOR = TextUtil.STRIKE
        private const val COUNT_ONE = 1L
        private const val COUNT_TWO = 2L
    }
}