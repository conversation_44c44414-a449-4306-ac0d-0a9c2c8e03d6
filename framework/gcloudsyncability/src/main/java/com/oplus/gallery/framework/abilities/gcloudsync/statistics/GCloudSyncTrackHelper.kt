/***********************************************************
** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
** All rights reserved.

** File         : GCloudSyncTrackHelper.kt
** Description  : Google云服务相关埋点帮助类
** Version      : 1.0
** Date         : add for 2023/10/1 0001
** Author       : liumingfu@Apps.Gallery3D
** TAG          : OPLUS_ARCH_EXTENDS
**
** ------------------------------- Revision History: -------------------------------
**    <author>          <data>       <version >       <desc>
**   liumingfu       2023/10/1 0001         1.0     build this module
****************************************************************/
package com.oplus.gallery.framework.abilities.gcloudsync.statistics

import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.EventId.GCLOUD_DIALOG_CLICK
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.EventId.GCLOUD_MAIN_TIP_CLICK
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.EventId.SYNC_RESULT
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.EventId.USER_ACTION_GOOGLE_SYNC_SWITCH_STATE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.EventId.USER_ACTION_SYNC_CLICK_OPEN_CLOUD_EVENT_ID
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Key.ACTION
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Key.CLOUD_SERVICE_PROVIDER
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Key.OPERATION
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Key.RESULT
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Key.TIP
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Key.USER_ACTION_CLICK_SYNC_TYPE_KEY
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.TYPE_CLOUD_UPLOAD_WITH_DOWNLOAD
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.CLOSE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.OPEN
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.ENUM_FALSE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.ENUM_TRUE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.basecloudability.statistics.BaseCloudSyncTrackHelper

object GCloudSyncTrackHelper : BaseCloudSyncTrackHelper() {

    private const val TAG = "GCloudSyncTrackHelper"

    /**
     * 启动Google页面的入口来源埋点
     *
     * @see com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.SETTING_PAGE
     * @see com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.BLANK_PAGE
     * @see com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.DASHBOARD_PAGE
     *
     * @param from 入口埋点value值
     */
    @JvmStatic
    fun trackGoogleRedirectionEntry(from: String): Unit = track(USER_ACTION_SYNC_CLICK_OPEN_CLOUD_EVENT_ID) {
        GLog.d(TAG, "trackGoogleRedirectionEntry: from = $from")
        it.putProperty(USER_ACTION_CLICK_SYNC_TYPE_KEY, from)
        it.putProperty(CLOUD_SERVICE_PROVIDER, CloudSyncTrackConstant.Value.GOOGLE)
        it.save()
    }

    /**
     * 备份开关状态记录埋点
     *
     * @param isOpen 开启/关闭
     * @param isSuccess 开启成功/关闭成功
     * @param provider 业务来源，目前只有Google
     */
    @JvmStatic
    fun trackCloudSyncSwitchState(isOpen: Boolean, isSuccess: Boolean, provider: String): Unit =
        track(USER_ACTION_GOOGLE_SYNC_SWITCH_STATE) {
            GLog.d(TAG, "trackCloudSyncSwitchState: isOpen = $isOpen, $isSuccess = isSuccess, $provider = $provider")
            it.putProperty(OPERATION, if (isOpen) OPEN else CLOSE)
            it.putProperty(RESULT, if (isSuccess) ENUM_TRUE else ENUM_FALSE)
            it.save()
        }

    /**
     * GooglePhoto App 安装状态埋点上报
     *
     * @param result
     */
    @JvmStatic
    fun trackGoogleAppState(result: Int): Unit  = track(SYNC_RESULT) {
        GLog.d(TAG, "trackGoogleAppState: result = $result")
        it.putProperty(RESULT, result)
        it.putProperty(CLOUD_SERVICE_PROVIDER, CloudSyncTrackConstant.Value.GOOGLE)
        it.save()
    }

    @JvmStatic
    fun trackDialogClick(action: String) {
        Tracker.trackStore.createSingleTrack(
            event = GCLOUD_DIALOG_CLICK,
            type = TYPE_CLOUD_UPLOAD_WITH_DOWNLOAD,
            func = {
                it.putProperty(ACTION, action)
                it.save()
            }
        )
    }

    @JvmStatic
    fun trackMainTipClick(tipType: String, action: String) {
        Tracker.trackStore.createSingleTrack(
            event = GCLOUD_MAIN_TIP_CLICK,
            type = TYPE_CLOUD_UPLOAD_WITH_DOWNLOAD,
            func = {
                it.putProperty(TIP, tipType)
                it.putProperty(ACTION, action)
                it.save()
            }
        )
    }
}