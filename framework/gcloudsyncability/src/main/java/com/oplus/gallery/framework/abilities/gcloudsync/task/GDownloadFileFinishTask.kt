/***********************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.

 ** File         : GDownloadFileFinishTask.kt
 ** Description  : 下载任务结束之后的数据处理任务
 ** Version      : 1.0
 ** Date         : add for 2023/8/3 0003
 ** Author       : liumingfu@Apps.Gallery3D
 ** TAG          : OPLUS_ARCH_EXTENDS
 **
 ** ------------------------------- Revision History: -------------------------------
 **    <author>          <data>       <version >       <desc>
 **   liumingfu       2023/8/3 0003         1.0     build this module
 ****************************************************************/
package com.oplus.gallery.framework.abilities.gcloudsync.task

import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.provider.BaseColumns
import android.text.TextUtils
import com.oplus.gallery.business_lib.cloud.DownloadSpec
import com.oplus.gallery.foundation.database.store.CloudSyncStore
import com.oplus.gallery.foundation.database.store.CloudSyncStore.FileDownloadStatus
import com.oplus.gallery.foundation.database.store.CloudSyncStore.FileUploadStatus
import com.oplus.gallery.foundation.database.store.CloudSyncStore.LocalFileStatus
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.FILE_DOWNLOAD_STATUS
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.FILE_SYNC_COUNT
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.FILE_SYNC_TIME
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.FILE_UPLOAD_STATUS
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.MEDIA_ID
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.util.SQLGrammar
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.data.DeleteFileRequest
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.basecloudability.db.MediaDataScanner
import com.oplus.gallery.framework.abilities.basecloudability.sync.FileDownloadNotifier
import com.oplus.gallery.framework.abilities.basecloudability.sync.FileSyncDelayStrategy
import com.oplus.gallery.framework.abilities.basecloudability.utils.CloudCacheHelper
import com.oplus.gallery.framework.abilities.basecloudability.file.ThumbScaleUtil
import com.oplus.gallery.framework.abilities.gcloudsync.base.BaseTask
import com.oplus.gallery.framework.abilities.gcloudsync.base.TaskResult
import com.oplus.gallery.framework.abilities.gcloudsync.base.TaskResultCode
import com.oplus.gallery.framework.abilities.gcloudsync.base.TaskState
import com.oplus.gallery.framework.abilities.gcloudsync.db.LocalSyncDataVisitor
import com.oplus.gallery.framework.abilities.gcloudsync.isCache
import com.oplus.gallery.framework.abilities.gcloudsync.metadata.GFileMetaData
import com.oplus.gallery.framework.abilities.gcloudsync.task.filetransfer.GFileTransferResult
import com.oplus.gallery.framework.abilities.gcloudsync.utils.CloudFileOperation
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 下载任务结束之后的数据处理
 *
 * @property taskSuccess 下载任务成功与否
 *
 * @param appContext
 * @param state
 */
class GDownloadFileFinishTask(appContext: Context, private val taskSuccess: Boolean, state: TaskState = TaskState.PENDING) :
    BaseTask(appContext, state) {

    /**
     * 下载之后的文件信息，以 fileMetaData 保存在 preResult
     *
     * @param downloadResult
     * @return
     */
    override fun execute(downloadResult: TaskResult<out Any>?): TaskResult<out Any> {
        //安全性检查
        if (downloadResult !is GFileTransferResult) {
            throw IllegalArgumentException("not a GFileTransferResult")
        }

        val fileMetaData = downloadResult.data
        if (fileMetaData !is GFileMetaData) {
            throw IllegalArgumentException("GFileTransferResult data not a GFileMetaData")
        }

        if (fileMetaData.downloadSpec == null) {
            throw NullPointerException("downloadSpec null")
        }

        return if (taskSuccess) {
            onDownloadSuccessResult(fileMetaData)
        } else {
            onDownloadFailResult(downloadResult, fileMetaData)
        }
    }

    /**
     * 下载失败处理
     *
     * @param downloadResult
     * @param fileMetaData
     * @return
     */
    private fun onDownloadFailResult(downloadResult: TaskResult<*>?, fileMetaData: GFileMetaData): TaskResult<Any> {
        val localRecord: GFileMetaData? =
            LocalSyncDataVisitor.getLocalImageFile(
                BaseColumns._ID + SQLGrammar.EQUAL + fileMetaData.localId,
                null
            )
        if (localRecord == null) {
            GLog.w(TAG, "[FileTransfer][onDownloadFailResult] not found image in db.")
            return TaskResult<Any>(false)
        }
        var deltaSyncCount = 0
        if (needRecordSyncFail(downloadResult?.code)) {
            deltaSyncCount =
                if (!TextUtils.isEmpty(fileMetaData.filePath)
                    && fileMetaData.filePath != File(fileMetaData.filePath).absolutePath) {
                    // 异常路径导致的下载失败，直接屏蔽，不再下载。例如："/storage//emulated/0/DCIM/Camera///test.jpg"
                    GLog.e(TAG, "[FileTransfer][onDownloadFailResult] invalid file path. itemId = " + fileMetaData.localId)
                    FileSyncDelayStrategy.FILE_SYNC_INVALID_COUNT
                } else {
                    FileSyncDelayStrategy.FILE_SYNC_BASE_COUNT
                }
        }
        updateDbWhenDownloadFailed(fileMetaData, localRecord, deltaSyncCount)
        notifyDownloadFailed(fileMetaData)
        GLog.e(TAG) {
            "[FileTransfer][onDownloadFailResult] notify " +
                    PathMask.mask(fileMetaData.filePath) + " download state = " +
                    FileDownloadStatus.FILE_DOWNLOAD_WAITING
        }
        return TaskResult(false)
    }

    /**
     * 下载成功处理
     *
     * @param downloadedFileData
     * @return
     */
    private fun onDownloadSuccessResult(downloadedFileData: GFileMetaData): TaskResult<Any> {
        //查询本地数据库记录
        val localRecord: GFileMetaData? =
            LocalSyncDataVisitor.getLocalImageFile(
                BaseColumns._ID + SQLGrammar.EQUAL + downloadedFileData.localId,
                null
            )
        // 当前数据记录被删除，则不再继续搬迁文件到媒体库，并删除已下载文件
        if (localRecord == null) {
            deleteCacheFile(downloadedFileData)
            GLog.w(TAG) { "[FileTransfer][onDownloadSuccessResult] not found image in db." +
                    " delete file ${PathMask.mask(downloadedFileData.filePath)}" }
            notifyDownloadFailed(downloadedFileData)
            return TaskResult(true)
        }

        // 删除缓存中比当前缓存尺寸小的缩图
        downloadedFileData.downloadSpec?.let {
            if (!it.isOrigin) {
                CloudCacheHelper().deleteSmallThumb(localRecord.id, it.thumbSize)
            }
        }

        /*
        文件被加入私密保险箱，则不再继续搬迁文件到媒体库，并删除已下载文件
        Marked by mingfu 2023/8/3 后续适配
        if (localRecord.isOperationSafeBox()) {
            syncFilesParams.deleteCacheFile()
            val selection =
                BaseColumns._ID + "=" + syncFilesParams.getItemId()
            val values =
                ContentValues()
            values.put(CloudColumns.SYNC_PERCENT, 0)
            values.put(CloudColumns.OPERATION, CloudSyncStore.Operation.OPERATION_SAFE_BOX_FILE)
            val updateCount: Int = LocalSyncDataVisitor.update(values, selection, null)
            GLog.d(TAG,
                "[processDownloadedFile] image in safe box. updateCount=$updateCount"
            )
            notifyDownloadFailed(syncFilesParams)
            return
        }
        */

        /*
        处理缩图和原图下载后的文件缓存和数据状态
        已下载文件路径
        */
        val downloadedFile = File(downloadedFileData.cachePath)
        val isDownloadOriginal = downloadedFileData.downloadSpec == DownloadSpec.ORIGIN
        //云上的文件的size和已下载文件size比较
        if (isDownloadOriginal && (localRecord.size != downloadedFile.length())) {
            GLog.e(TAG, "[FileTransfer] onDownloadSuccessResult exception case：" +
                    "localFileStatus = ${downloadedFileData.localFileStatus}， but localRecord size = ${localRecord.size}" +
                    " actual file length = ${downloadedFile.length()}")
        }
        val downloadSuccess: Boolean
        val processFileResult: Pair<Boolean, Uri?> =
            if (isDownloadOriginal) {
                //下载原图流程处理
                processDownloadedOriginalFile(downloadedFileData, localRecord)
            } else {
                //下载缩图流程处理
                processDownloadedThumbFile(downloadedFileData, localRecord)
            }
        //处理成功
        downloadSuccess = processFileResult.first
        // 可以在这里更新Q版本媒体库，先使用扫描方式更新，如果有bug，在考虑直接插入或者更新媒体库
        if (downloadSuccess) {
            MediaDataScanner.getInstance().add(downloadedFileData.filePath)
        }
        //回调业务注册的监听
        FileDownloadNotifier.notifyDownloadFinish(
            downloadedFileData.localId,
            if (downloadSuccess) FileDownloadStatus.FILE_DOWNLOAD_DOWNLOADED else FileDownloadStatus.FILE_DOWNLOAD_WAITING,
            isDownloadOriginal,
            processFileResult.second
        )
        GLog.d(TAG, "[FileTransfer][onDownloadSuccessResult] end. downloadSuccess=$downloadSuccess")
        return TaskResult<Any>(true)
    }

    /**
     * 处理原图下载之后的流程
     *
     * @param downloadedFileData
     * @param localRecord
     * @return
     */
    private fun processDownloadedOriginalFile(downloadedFileData: GFileMetaData, localRecord: GFileMetaData): Pair<Boolean, Uri?> {
        GLog.d(TAG, "[FileTransfer][processDownloadedOriginalFile] start")
        val result: Pair<Boolean, Uri?> = replaceThumbByOriginalFile(localRecord, downloadedFileData)
        if (result.first) {
            updateDbWhenDownloadSuccess(downloadedFileData, localRecord, isCache = false, isOriginal = true, result.second)
            // 删除缓存中比原图尺寸小的缩图，主要就是删除大缩图，GCloud大缩图在 private目录
            CloudCacheHelper().deleteSmallThumb(localRecord.id, (DownloadSpec.COMPRESSED.thumbSize + 1))
        } else {
            updateDbWhenDownloadFailed(downloadedFileData, localRecord, FileSyncDelayStrategy.FILE_SYNC_BASE_COUNT)
        }
        GLog.d(TAG) {
            "[FileTransfer][processDownloadedOriginalFile] end. notify " +
                    PathMask.mask(downloadedFileData.filePath) + " download success=" + result.first
        }
        return result
    }

    /**
     * 使用下载的原图替换本地的缩图
     */
    private fun replaceThumbByOriginalFile(localRecord: GFileMetaData, downloadedFileData: GFileMetaData): Pair<Boolean, Uri?> {
        // 删除大缩图
        File(CloudCacheHelper.getThumbPath(downloadedFileData.id, DownloadSpec.COMPRESSED)).delete()
        val originFile = File(downloadedFileData.cachePath)
        // 需要触发下才会立马进入媒体库
        val mediaUri = MediaStoreUriHelper.getMediaUri(
            appContext,
            File(downloadedFileData.cachePath),
            null,
            localRecord.isImage()
        ) ?: MediaStoreScannerHelper.scanFileByMediaStoreSingle(appContext, originFile.absolutePath)
        if (mediaUri == null) {
            GLog.e(TAG, "replaceThumbByOriginalFile mediaUri is null, filePath:${originFile.absolutePath}")
            return Pair(false, null)
        }
        // 反写dateModified，并且可能在下载前被移入了回收站，也需要再检查下
        val success = CloudFileOperation.renameTo(
            originFile,
            originFile,
            localRecord.isImage(),
            localRecord.isRecycler(),
            localRecord.dateModified
        )
        GLog.d(TAG) {
            "replaceThumbByOriginalFile file:${PathMask.mask(originFile.absolutePath)}, " +
                    "isRecycler:${localRecord.isRecycler()}, success:$success"
        }
        return Pair(success, mediaUri)
    }

    /**
     * 大小缩图下载完成的处理
     *
     * @param fileMetaData
     * @param localRecord
     * @return
     */
    private fun processDownloadedThumbFile(fileMetaData: GFileMetaData, localRecord: GFileMetaData): Pair<Boolean, Uri?> {
        GLog.d(TAG, "[FileTransfer][processDownloadedThumbFile] start")
        val downloadedFilePath: String = fileMetaData.cachePath
        val downloadSpec: DownloadSpec? = fileMetaData.downloadSpec
        if (downloadSpec == null) {
            GLog.e(TAG, "[FileTransfer][processDownloadedThumbFile] downloadSpec in file is NULL")
            return Pair(false, null)
        }
        var success = false
        //缩图都是cache
        if (downloadSpec.isCache) {
            val destFilePath: String = CloudCacheHelper.getThumbPath(localRecord.id, downloadSpec) ?: TextUtil.EMPTY_STRING
            // 私有路径到私有路径，直接rename
            success =
                if (destFilePath != downloadedFilePath) {
                    FileOperationUtils.renameTo(File(downloadedFilePath), File(destFilePath))
                } else {
                    true
                }
            GLog.d(TAG) { "[FileTransfer][processDownloadedThumbFile] " +
                    "downloadedFilePath = ${PathMask.mask(downloadedFilePath)}, success = $success" }
        }
        if (success) {
            updateDbWhenDownloadSuccess(fileMetaData, localRecord, downloadSpec.isCache, false)
        } else {
            updateDbWhenDownloadFailed(fileMetaData, localRecord, FileSyncDelayStrategy.FILE_SYNC_BASE_COUNT)
        }
        GLog.d(TAG, "[FileTransfer][processDownloadedThumbFile] end")
        return Pair(success, null)
    }

    private fun needRecordSyncFail(resultCode: TaskResultCode?): Boolean {
        return resultCode == TaskResultCode.RESULT_FAIL
                || resultCode == TaskResultCode.RESULT_NETWORK_ERROR
                || resultCode == TaskResultCode.RESULT_IO_ERROR
                || resultCode == TaskResultCode.RESULT_METADATA_ERROR
    }

    /**
     * 文件处理成功，更新数据库
     *
     * @param fileMetaData
     * @param localRecord
     * @param isCache
     * @param isOriginal
     */
    private fun updateDbWhenDownloadSuccess(
        fileMetaData: GFileMetaData,
        localRecord: GFileMetaData,
        isCache: Boolean,
        isOriginal: Boolean,
        uri: Uri? = null
    ) {
        val currentTimeMillis = System.currentTimeMillis()
        val values = ContentValues()
        if (isCache) {
            values.put(FILE_DOWNLOAD_STATUS, FileDownloadStatus.FILE_DOWNLOAD_WAITING)
        } else {
            values.put(FILE_DOWNLOAD_STATUS, FileDownloadStatus.FILE_DOWNLOAD_DOWNLOADED)
        }
        uri?.let {
            val mediaId = ContentUris.parseId(it)
            if (mediaId > 0) {
                values.put(MEDIA_ID, mediaId)
            }
        }
        values.put(FILE_UPLOAD_STATUS, FileUploadStatus.FILE_UPLOADED)
        // 文件上传/下载成功后，重置时报次数，特别是下载，保证失败次数不会累加到下一个分辨率
        values.put(FILE_SYNC_COUNT, 0)
        values.put(FILE_SYNC_TIME, currentTimeMillis)
        values.put(CloudColumns.LATEST_FILE_USAGE_TIME, currentTimeMillis)
        values.put(CloudColumns.SYNC_PERCENT, 0)
        // 下载后，更新本地文件状态
        if (isOriginal) {
            values.put(LocalColumns.SIZE, localRecord.size)
            values.put(LocalColumns.WIDTH, localRecord.width)
            values.put(LocalColumns.HEIGHT, localRecord.height)
            values.put(CloudColumns.LOCAL_FILE_STATUS, LocalFileStatus.LOCAL_FILE_STATUS_ORIGINAL)
            if (localRecord.filePath != fileMetaData.filePath) {
                values.put(CloudColumns.DATA, fileMetaData.filePath)
                values.put(CloudColumns.DISPLAY_NAME, fileMetaData.displayName)
            }
        } else {
            //计算出缩图宽高
            val thumbResult = ThumbScaleUtil.getThumbResult(fileMetaData.width, fileMetaData.height)
            values.put(LocalColumns.WIDTH, thumbResult.width)
            values.put(LocalColumns.HEIGHT, thumbResult.height)
            values.put(CloudColumns.LOCAL_FILE_STATUS, fileMetaData.localFileStatus)
        }
        // 文件下载完成后，将云端operation设置为none，代表云端下发的操作已经处理完成
        values.put(CloudColumns.OPERATION, CloudSyncStore.Operation.OPERATION_NONE)
        LocalSyncDataVisitor.updateById(values, fileMetaData.localId)
    }

    /**
     * 下载失败后，更新数据库状态
     *
     * @param deltaSyncCount 失败次数增量，如果为0，则不更新失败次数
     */
    private fun updateDbWhenDownloadFailed(fileMetaData: GFileMetaData, localRecord: GFileMetaData, deltaSyncCount: Int) {
        val values = ContentValues()
        // 失败次数增量无效，不更新
        if (deltaSyncCount > 0) {
            val syncCount = FileSyncDelayStrategy.getSyncCount(fileMetaData.fileSyncCount, fileMetaData.fileSyncTime, deltaSyncCount)
            values.put(FILE_SYNC_COUNT, syncCount)
            values.put(FILE_SYNC_TIME, System.currentTimeMillis())
        }
        values.put(CloudColumns.LATEST_FILE_USAGE_TIME, System.currentTimeMillis())
        values.put(CloudColumns.SYNC_PERCENT, 0)
        // 更新下载状态，本地文件状态是元数据状态，更新为0，否则更新为2
        val fileDownloadState =
            if (localRecord.localFileStatus == LocalFileStatus.LOCAL_FILE_STATUS_METADATA) {
                FileDownloadStatus.FILE_DOWNLOAD_WAITING
            } else FileDownloadStatus.FILE_DOWNLOAD_DOWNLOADED
        values.put(FILE_DOWNLOAD_STATUS, fileDownloadState)
        values.put(FILE_UPLOAD_STATUS, FileUploadStatus.FILE_UPLOADED)
        LocalSyncDataVisitor.updateById(values, fileMetaData.localId)
    }

    /**
     * 删除下载的缓存文件
     */
    private fun deleteCacheFile(fileMetaData: GFileMetaData): Boolean {
        val request: DeleteFileRequest = DeleteFileRequest.Builder()
            .setImage(fileMetaData.mediaType).builder()
        val fileManager = FileAccessManager.getInstance()
        val filePath: String = fileMetaData.cachePath
        if (!TextUtils.isEmpty(filePath)) {
            val failedFile = File(filePath)
            request.file = failedFile
            return !failedFile.exists() || fileManager.delete(ContextGetter.context, request)
        }
        return true
    }

    /**
     * 通知业务下载失败
     *
     * @param downloadData
     */
    private fun notifyDownloadFailed(fileData: GFileMetaData) {
        fileData.downloadSpec?.run {
            FileDownloadNotifier.notifyDownloadFinish(
                fileData.localId,
                FileDownloadStatus.FILE_DOWNLOAD_WAITING,
                isOrigin,
                null
            )
        }
    }

    override fun stop() {
        //do nothing
    }

    companion object {
        private const val TAG = "GDownloadFileFinishTask"
    }
}