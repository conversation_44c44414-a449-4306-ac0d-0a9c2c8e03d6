/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : GCloudBackupWorkFlow.kt
 ** Description : 谷歌备份工作流，即将元数据信息同步至谷歌
 ** Version     : 1.0
 ** Date        : 2023/08/22
 ** Author      : zhongxuechang@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D       2023/08/22     1.0       build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.gcloudsync.flow

import android.content.Context
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.gcloudsync.ConditionManager
import com.oplus.gallery.framework.abilities.gcloudsync.base.BaseTask
import com.oplus.gallery.framework.abilities.gcloudsync.base.BaseWorkFlow
import com.oplus.gallery.framework.abilities.gcloudsync.base.FlowPriority
import com.oplus.gallery.framework.abilities.gcloudsync.base.TaskResult
import com.oplus.gallery.framework.abilities.gcloudsync.base.TaskResultCode
import com.oplus.gallery.framework.abilities.gcloudsync.task.GDeleteTask
import com.oplus.gallery.framework.abilities.gcloudsync.task.GRecycleTask
import com.oplus.gallery.framework.abilities.gcloudsync.task.GRestoreTask
import com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 谷歌备份工作流，即将元数据信息同步至谷歌
 */
class GCloudBackupWorkFlow(
    private val syncMode: String,
    private val syncType: String,
    appContext: Context,
    priority: FlowPriority,
    conditionManager: ConditionManager
) : BaseWorkFlow(appContext, priority, conditionManager) {

    private val executeInfoConfig: List<ExecuteInfo> by lazy {
        arrayListOf(
            ExecuteInfo(
                CloudSyncConstants.SYNC_MODE_RECYCLE,
                arrayOf(CloudSyncConstants.SYNC_TYPE_DELETE_PHOTO),
                GRecycleTask(ContextGetter.context)
            ),
            ExecuteInfo(
                CloudSyncConstants.SYNC_MODE_RECYCLE,
                arrayOf(CloudSyncConstants.SYNC_TYPE_RESTORE_PHOTO, CloudSyncConstants.SYNC_TYPE_RESTORE_ALL_PHOTO),
                GRestoreTask(ContextGetter.context)
            ),
            ExecuteInfo(
                CloudSyncConstants.SYNC_MODE_RECYCLE,
                arrayOf(CloudSyncConstants.SYNC_TYPE_DELETE_RECYCLE_PHOTO, CloudSyncConstants.SYNC_TYPE_DELETE_RECYCLE_ALL),
                GDeleteTask(ContextGetter.context)
            )
        )
    }

    override fun canStart(): TaskResult<out Any> {
        return matchToExecute("canStart") { it.task.canStart() }
    }

    override fun onRunning(): TaskResult<out Any> {
        return matchToExecute("onRunning") { it.task.start() }
    }

    private fun matchToExecute(logTag: String, action: (info: ExecuteInfo) -> TaskResult<out Any>): TaskResult<out Any> {
        val incrementInfoList = executeInfoConfig.filter { it.supportIncrementSyncMode == syncMode }
        if (incrementInfoList.isNotEmpty()) {
            // 如果指定跑增量模式，则只执行该模式下可支持的对应任务即可，例如指定只跑回收站场景下的同步彻底删除操作
            incrementInfoList.firstOrNull {
                it.syncTypes.contains(syncType)
            }?.let {
                return action.invoke(it)
            }
            GLog.e(TAG, "matchToExecute $logTag syncMode:$syncMode, error syncType:$syncType")
        }
        // 非增量模式，则执行所有任务
        var result: TaskResult<out Any> = TaskResult(true, TaskResultCode.META_DATA_SYNC_SUCCESS)
        executeInfoConfig.forEach {
            result = action.invoke(it)
            if (!result.isSuccess) return result
        }
        return result
    }

    /**
     * 任务执行信息
     * @param supportIncrementSyncMode 支持增量执行的同步模式，如果不为空，代表遇到这个模式的情况下，可以只执行它对应的任务即可
     * @param syncTypes 支持增量的同步类型
     * @param task 同步任务
     */
    data class ExecuteInfo(val supportIncrementSyncMode: String = TextUtil.EMPTY_STRING, val syncTypes: Array<String>, val task: BaseTask) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as ExecuteInfo

            if (supportIncrementSyncMode != other.supportIncrementSyncMode) return false
            if (!syncTypes.contentEquals(other.syncTypes)) return false
            if (task != other.task) return false

            return true
        }

        override fun hashCode(): Int {
            var result = supportIncrementSyncMode.hashCode()
            result = 31 * result + syncTypes.contentHashCode()
            result = 31 * result + task.hashCode()
            return result
        }
    }

    companion object {
        private const val TAG = "GCloudBackupWorkFlow"
    }
}