/***********************************************************
** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
** All rights reserved.

** File         : CloudSyncSupplier.kt
** Description  : 云端功能提供器，负责创建channelSyncChannel
** Version      : 1.0
** Date         : add for 2023/12/15 0015
** Author       : liumingfu@Apps.Gallery3D
** TAG          : OPLUS_ARCH_EXTENDS
**
** ------------------------------- Revision History: -------------------------------
**    <author>          <data>       <version >       <desc>
**   liumingfu       2023/12/15 0015         1.0     build this module
****************************************************************/
package com.oplus.gallery.framework.abilities.cloudsync

import com.oplus.gallery.business_lib.api.CloudSyncChannelType
import com.oplus.gallery.business_lib.api.ICloudSyncDM
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.text.TextUtil
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Method
import java.lang.reflect.Proxy

class CloudSyncSupplier(private val channelType: CloudSyncChannelType) {

    private val tag = "CloudSyncSupplier$$channelType"

    /**
     * 获取通道对应的DM
     *
     * 如果是[CloudSyncChannelType.NONE_CHANNEL]或者通道相应依赖未集成，则返回空DM
     */
    val cloudSyncDM: ICloudSyncDM by lazy {
        createInstanceFromType()
    }

    private fun createInstanceFromType(): ICloudSyncDM {
        GTrace.traceBegin("CloudSyncSupplier.createInstanceFromType")
        return if (channelType == CloudSyncChannelType.NONE_CHANNEL) {
            //动态代理空DM
            createNoneSyncDM()
        } else {
            runCatching {
                Class.forName(channelType.classPath).newInstance() as ICloudSyncDM
            }.onFailure {
                GLog.e(TAG) { "createInstanceFromType $tag failed, but may not a bug, error: $it" }
            }.getOrNull() ?: createNoneSyncDM()
        }.apply {
            GTrace.traceEnd()
        }
    }

    private fun createNoneSyncDM(): ICloudSyncDM {
        val clazz: Class<*> = ICloudSyncDM::class.java
        return Proxy.newProxyInstance(clazz.classLoader, arrayOf(clazz), DefaultIH()) as ICloudSyncDM
    }

    companion object {
        private const val TAG = "CloudSyncSupplier"
    }

    private class DefaultIH : InvocationHandler {
        override fun invoke(proxy: Any?, method: Method, args: Array<out Any>?): Any? {
            GLog.w(TAG, "invoke: call method <$method> in DefaultIH. It does not work!!!")
            val returnType = method.returnType
            return when {
                Int::class.java.isAssignableFrom(returnType) -> 0
                Float::class.java.isAssignableFrom(returnType) -> 0.0f
                Byte::class.java.isAssignableFrom(returnType) -> 0
                Double::class.java.isAssignableFrom(returnType) -> 0.0f
                Long::class.java.isAssignableFrom(returnType) -> 0L
                Boolean::class.java.isAssignableFrom(returnType) -> false
                String::class.java.isAssignableFrom(returnType) -> TextUtil.EMPTY_STRING
                else -> null
            }
        }

        companion object {
            private const val TAG = "CloudSyncSupplier.DefaultIH"
        }
    }
}