/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GpuFrameManager
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/6/4 10:39
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/6/4		  1.0		 GpuFrameManager
 *********************************************************************************/
package com.oplus.gallery.framework.editingability.pipeline.gl

import android.hardware.HardwareBuffer
import android.opengl.GLES30
import android.util.Size
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.editingability.pipeline.frame.BaseFrame
import com.oplus.gallery.framework.editingability.pipeline.frame.GpuFrame
import com.oplus.gallery.framework.editingability.pipeline.pool.FrameCacheStrategy
import com.oplus.gallery.framework.editingability.pipeline.pool.TexturePool
import com.oplus.gallery.framework.editingability.util.error

/**
 * GPU纹理缓存管理器
 * @param texturePool 纹理复用缓存池
 */
internal class GpuFrameManager(
    private val texturePool: TexturePool = TexturePool(FrameCacheStrategy())
) {
    /**
     * 分配一个GPU帧
     */
    fun allocateGpuFrame(size: Size, colorFormat: BaseFrame.ColorFormat): GpuFrame? {
        if (size.width <= 0 || size.height <= 0 || colorFormat == BaseFrame.ColorFormat.None) {
            GLog.error(TAG) { "[allocateGpuFrame] data is inValid, width = ${size.width} Height = ${size.height} colorFormat = $colorFormat" }
            return null
        }

        val texturePair: Pair<Int, HardwareBuffer?> = texturePool.acquireTexture(size.width, size.height, colorFormat)
        GLES30.glBindTexture(GLES30.GL_TEXTURE_2D, texturePair.first)
        GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_MIN_FILTER, GLES30.GL_LINEAR)
        GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_MAG_FILTER, GLES30.GL_LINEAR)
        GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_WRAP_S, GLES30.GL_CLAMP_TO_EDGE)
        GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_WRAP_T, GLES30.GL_CLAMP_TO_EDGE)

        return GpuFrame(this, texturePair.first, size, colorFormat).apply {
            this.hardwareBuffer = texturePair.second
        }
    }

    fun releaseGpuFrame(gpuFrame: GpuFrame) {
        if (!gpuFrame.isValid()) {
            return
        }
        if (GProperty.DEBUG_EDITING_ABILITY) {
            GLog.d(TAG, LogFlag.DL) { "[releaseGpuFrame] $gpuFrame" }
        }
        texturePool.releaseTexture(gpuFrame.textureId)
    }

    fun releaseAllGpuFrames() {
        texturePool.cleanup()
    }

    companion object {
        private const val TAG = "GpuFrameManager"
    }
}