/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : AvAssetMetadata.kt
 ** Description: 资源元信息读写实现类
 ** Version    : 1.0
 ** Date       : 2024/6/4
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2024/6/4    1.0              init
 *************************************************************************************************/

package com.oplus.gallery.framework.editingability.codec.metadata

import android.app.Application
import android.net.Uri
import com.oplus.gallery.foundation.codec.extend.COLOR_SPACE
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_IMAGE_QUALITY_ENHANCED
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_OBJ
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_ULTRA_HDR_INFO
import com.oplus.gallery.foundation.codec.extend.ExtendStruct
import com.oplus.gallery.foundation.exif.R
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag.EXIF_TAG_OPLUS_UHDR
import com.oplus.gallery.foundation.exif.utils.ExifTagParser
import com.oplus.gallery.foundation.exif.utils.hasFlag
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.editing.asset.ExifDataKey
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata
import com.oplus.gallery.framework.abilities.editing.asset.ExifDataKey.TAG_IMAGE_QUALITY_ENHANCED
import com.oplus.gallery.framework.abilities.editing.asset.ExifDataKey.TAG_ORIENTATION
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata.Companion.EXCLUDED_ASSOCIATE_METADATA_KEYS
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType
import com.oplus.gallery.framework.editingability.codec.image.eixf.ImageExifDataReader
import com.oplus.gallery.framework.editingability.codec.image.extend.ImageExtendedDataReader
import com.oplus.gallery.framework.editingability.codec.image.extend.OplusUhdrExtendedDataReader
import com.oplus.gallery.framework.editingability.codec.image.extend.UHdrImageExtendedDataReader
import com.oplus.gallery.framework.editingability.util.error
import com.oplus.gallery.framework.editingability.util.warn

/**
 * 资源元信息读写实现类。
 */
internal class AvAssetMetadata(
    val application: Application,
    val source: Uri,
    val mimeType: String,
) : IAvAssetMetadata {

    private val imageExifReader: ImageExifDataReader by lazy {
        ImageExifDataReader(application, source, mimeType)
    }

    private val imageExtendedReader: ImageExtendedDataReader by lazy {
        ImageExtendedDataReader(application, source)
    }

    private val uHdrImageExtendedReader: UHdrImageExtendedDataReader by lazy {
        UHdrImageExtendedDataReader(application, source)
    }

    private val oplusUhdrExtendedReader: OplusUhdrExtendedDataReader by lazy {
        OplusUhdrExtendedDataReader(application, source)
    }

    /**
     * 关联的Avasset的AvAssetMetadata
     * 1、如果从当前AvAssetMetadata没有获取到元信息，则尝试使用关联的associateMetadata获取
     * 2、associateMetadata目前对应的是原图的元信息
     * 3、人像图在处理参数化特效如  调节，裁剪旋转时不会写入人像信息，
     *   避免图发送到另外同型号手机上，还原出现问题，src图无法还原出效果图。
     *   这种状态下，效果图没有写入人像信息，项目化还原时，需要借助原图获取对应的人像信息
     */
    internal var associateMetadata: IAvAssetMetadata? = null

    /**
     * 图片的exif中的tagFlag字段值
     * 有的图片可能没有这个值。所以在使用这个值的时候，应该以白名单的形式严格判断自己需要的值。
     * ！没有期望值的逻辑应该是兜底的方案！
     */
    private var tagFlag: Long = OplusExifTag.INVALID
        get() {
            if (field != OplusExifTag.INVALID) return field
            val userComment = imageExifReader.getExifData(ExifDataKey.TAG_USER_COMMENT) as? String
            val tagFlagPrefixes = application.resources.getStringArray(R.array.base_tagflags_prefixes)
            val tagFlagFromExif = ExifTagParser.parseTagFlags(userComment, tagFlagPrefixes)
            if (tagFlagFromExif != OplusExifTag.INVALID) {
                field = tagFlagFromExif
            }
            return field
        }

    /**
     * 由使用方注入的元信息字段。资源的某些字段可能存在解析异常或者无法编辑写入的情况，此时需要业务注入进来。
     *
     * 拿Exif来举个例子：
     * 不支持Exif的图片旋转后，角度存储在业务，需要业务注入进来，此时就优先使用业务注入的数据。
     * - 只有Jpg才支持Exif读和写
     * - Heif图片只支持读Exif，不支持写Exif
     * - 其他没有Exif信息图片
     */
    private val injectedMetadata = mutableMapOf<String, Any?>()

    /**
     * 外部注入的扩展是新 key 对应 struct
     */
    private val injectedMetadataStructs = mutableMapOf<String, ExtendStruct<*>>()

    override fun getMetadata(type: MetadataType, dataKey: String): Any? {
        return when {
            (type == MetadataType.DATABASE) -> injectedMetadata[dataKey]

            (type == MetadataType.EXIF) -> {
                when (dataKey) {
                    TAG_ORIENTATION -> injectedMetadata[dataKey] ?: imageExifReader.getExifData(dataKey)
                    else -> imageExifReader.getExifData(dataKey)
                }
            }

            (type == MetadataType.EXTENDED) -> {
                when (dataKey) {
                    // ultra hdr 比较特殊，其元信息需要[OplusImageHdrImpl.decodeGainmapAndMetadata]去解析
                    EXTEND_KEY_ULTRA_HDR_INFO -> {
                        if (tagFlag.hasFlag(EXIF_TAG_OPLUS_UHDR)) {
                            // tagFlag中明确表示是oplus格式的，使用自定义的方式解析增益信息
                            oplusUhdrExtendedReader.getExtendedData(dataKey)
                        } else {
                            // tagFlag无效（可能图片根本就没有这个字段）或不是oplus，尝试通用的uhdr解析增益信息
                            uHdrImageExtendedReader.getExtendedData(dataKey)
                        }
                    }

                    COLOR_SPACE -> injectedMetadata[COLOR_SPACE]
                    TAG_IMAGE_QUALITY_ENHANCED -> imageExtendedReader.getExtendedData(EXTEND_KEY_IMAGE_QUALITY_ENHANCED)
                    else -> injectedMetadata[dataKey] ?: imageExtendedReader.getExtendedData(dataKey)
                }
            }

            else -> {
                GLog.error(TAG) { "getMetadata, not support $type#$dataKey yet" }
                null
            }
        }  ?: associateMetadata.takeUnless { dataKey in EXCLUDED_ASSOCIATE_METADATA_KEYS }?.getMetadata(type, dataKey)
    }

    override fun getMetadata(dataKeys: Map<MetadataType, List<String>>): Map<String, Any?>? {
        val result = mutableMapOf<String, Any?>()
        dataKeys.keys.forEach { type ->
            when {
                (type == MetadataType.EXIF) -> {
                    dataKeys[type]?.let { exifDataKeys ->
                        getExifMetadata(exifDataKeys, result)
                    }
                }

                (type == MetadataType.EXTENDED) -> {
                    dataKeys[type]?.let { extendedDataKeys ->
                        getExtendedMetadata(extendedDataKeys, result)
                    }
                }

                else -> GLog.error(TAG) { "getMetadata, not support type: $type yet" }
            }
        }

        return result.ifEmpty { associateMetadata?.getMetadata(dataKeys) }
    }

    override fun getMetadataStruct(type: MetadataType, dataKey: String): ExtendStruct<*>? {
        if (type != MetadataType.EXTENDED) {
            GLog.error(TAG) { "getMetadataStruct, Currently only EXTENDED acquisition is supported. type = $type, dataKey = $dataKey" }
            return null
        }

        return injectedMetadataStructs[dataKey] ?: EXTEND_KEY_OBJ[dataKey].let { clazz ->
            if (clazz == null) {
                GLog.error(TAG) { "getMetadataStruct error, Please configure parsing information in EXTEND_KEY_OBJ. dataKey = $dataKey" }
                null
            } else {
                imageExtendedReader.getExtendedDataStruct(dataKey, clazz) ?: run {
                    GLog.warn(TAG) { "getMetadataStruct error, No data returned for dataKey = $dataKey" }
                    null
                }
            }
        } ?: associateMetadata.takeUnless { dataKey in EXCLUDED_ASSOCIATE_METADATA_KEYS }?.getMetadataStruct(type, dataKey)
    }

    override fun setMetadataStruct(type: MetadataType, dataKey: String, struct: ExtendStruct<*>) {
        injectedMetadataStructs[dataKey] = struct
    }

    private fun getExifMetadata(exifDataKeys: List<String>, result: MutableMap<String, Any?>) {
        val list = mutableListOf<String>()
        exifDataKeys.forEach { key ->
            injectedMetadata[key]?.let { metadata ->
                // 获取业务注入的Exif内容
                result[key] = metadata
            } ?: list.add(key)
        }
        // 获取剩余的Exif字段
        if (list.isNotEmpty()) {
            result.putAll(imageExifReader.getExifData(list))
        }
    }

    private fun getExtendedMetadata(extendedDataKeys: List<String>, result: MutableMap<String, Any?>) {
        val list = mutableListOf<String>()
        extendedDataKeys.forEach { key ->
            if (EXTEND_KEY_ULTRA_HDR_INFO == key) {
                // 获取uhdr格式字段，处理完后释放fd
                result[EXTEND_KEY_ULTRA_HDR_INFO] = if (tagFlag.hasFlag(EXIF_TAG_OPLUS_UHDR)) {
                    // tagFlag中明确表示是oplus格式的，使用自定义的方式解析增益信息
                    oplusUhdrExtendedReader.getExtendedData(EXTEND_KEY_ULTRA_HDR_INFO)
                } else {
                    // tagFlag无效（可能图片根本就没有这个字段）或不是oplus，尝试通用的uhdr解析增益信息
                    uHdrImageExtendedReader.getExtendedData(EXTEND_KEY_ULTRA_HDR_INFO)
                }
            } else if (injectedMetadata[key] != null) {
                // 获取业务注入的Extended内容
                result[key] = injectedMetadata[key]
            } else if (TAG_IMAGE_QUALITY_ENHANCED == key) {
                result[key] = imageExtendedReader.getExtendedData(EXTEND_KEY_IMAGE_QUALITY_ENHANCED)
            } else {
                list.add(key)
            }
        }

        // 获取剩余的Entended字段
        if (list.isNotEmpty()) {
            result.putAll(imageExtendedReader.getExtendedData(list))
        }
    }

    override fun setMetadata(type: MetadataType, dataKey: String, value: Any) {
        injectedMetadata[dataKey] = value
    }

    /**
     * 释放内存缓存
     */
    override fun releaseCache() {
        imageExifReader.releaseCache()
        imageExtendedReader.releaseCache()
        uHdrImageExtendedReader.releaseCache()
        oplusUhdrExtendedReader.releaseCache()
        injectedMetadata.clear()
        injectedMetadataStructs.clear()
        associateMetadata?.releaseCache()
    }

    companion object {
        private const val TAG = "Codec#AvAssetMetadata"
    }
}