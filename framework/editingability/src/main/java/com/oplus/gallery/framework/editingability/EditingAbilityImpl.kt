/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditingAbility
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/1/31 21:58
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/1/31		  1.0		 EditingAbility
 *********************************************************************************/
package com.oplus.gallery.framework.editingability

import android.content.Context
import com.oplus.appability.AbsAppAbility
import com.oplus.gallery.framework.abilities.editing.IEditingAbility
import com.oplus.gallery.framework.editingability.session.SessionManager

/**
 * 编辑能力实现类
 * @param context 应用上下文
 */
class EditingAbilityImpl(context: Context) : AbsAppAbility(), IEditingAbility by SessionManager(context) {
    override val domainInstance: IEditingAbility = this
}