/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : CodecConfig.kt
 ** Description: 解码器配置类，设定解码器缓存的数量和使用偏好。
 ** Version    : 1.0
 ** Date       : 2024/5/8
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2024/5/8    1.0              init
 *************************************************************************************************/
package com.oplus.gallery.framework.editingability.codec

/**
 * 解码器配置类。设定解码器缓存的数量和使用偏好。
 * @param maxImageDecoderCount 图片解码器最大数量
 * @param maxImageEncoderCount 图片编码器最大数量
 * @param maxVideoDecoderCount 视频解码器最大数量
 * @param maxVideoEncoderCount 视频编码器最大数量
 * @param isHardwarePreferred 是否优先使用硬件编解码器？
 * @param longPhotoMinRatio 认定是长图的最小宽高比
 * @param longPhotoMemoryLimit 长图的最大内存限制
 * @param photoMaxLength 支持编辑的最大边长，受设备限制，如：Texture支持的最大长度
 */
internal data class CodecConfig(
    val maxImageDecoderCount: Int,
    val maxImageEncoderCount: Int,
    val maxVideoDecoderCount: Int,
    val maxVideoEncoderCount: Int,
    val isHardwarePreferred: Boolean,
    val longPhotoMinRatio: Float,
    val longPhotoMemoryLimit: Int,
    val photoMaxLength: Int,
)