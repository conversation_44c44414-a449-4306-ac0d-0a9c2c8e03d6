/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SessionManager
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/2/20 15:10
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/2/20		  1.0		 SessionManager
 *********************************************************************************/
package com.oplus.gallery.framework.editingability.session

import android.app.Application
import android.content.Context
import android.opengl.EGLContext
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.generator.IntIncrementGenerator
import com.oplus.gallery.framework.abilities.editing.IEditingAbility
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoBootstrapConfig
import com.oplus.gallery.framework.abilities.editing.editor.EditorResultState
import com.oplus.gallery.framework.abilities.editing.session.IEditingSession
import com.oplus.gallery.framework.abilities.editing.session.SessionConfigScope
import com.oplus.gallery.framework.editingability.bootstrap.GlobalAlgoBootstrapConfig
import com.oplus.gallery.framework.editingability.util.warn
import com.oplus.tbluniformeditor.common.LogUtil
import com.oplus.tbluniformeditor.common.constvalue.LogLevel
import java.util.concurrent.ConcurrentHashMap

/**
 * 操作会话管理模块
 * 它实现了IEditingAbility接口。SessionManager用于管理编辑会话，提供了创建、查找和关闭编辑会话的方法
 * @param context 应用上下文
 * @param globalAlgoBootstrapConfig 全局特效配置
 */
internal class SessionManager internal constructor(
    private val context: Context,
    private val globalAlgoBootstrapConfig: IAlgoBootstrapConfig = GlobalAlgoBootstrapConfig()
) : IEditingAbility {
    /**
     * 编辑会话列表
     */
    private val aliveSession = ConcurrentHashMap<Int, IEditingSessionCloseable>()

    /**
     * 编辑会话id生成器
     */
    private val sessionIdGenerator = IntIncrementGenerator()

    /**
     * 管理所有操作对话的优先级分配
     */
    private val priorityDispatching: PriorityDispatching by lazy { PriorityDispatching() }

    /**
     * 控制session的硬件和软件的资源分配
     */
    private val resourcingStrategy: ResourcingStrategy by lazy { ResourcingStrategy() }

    init {
        if (GProperty.DEBUG) {
            LogUtil.setLogLevel(LogLevel.DEBUG)
        }
    }

    override fun createSession(
        eglContext: EGLContext,
        algoBootstrapConfig: IAlgoBootstrapConfig,
        sessionEditingState: (EditorResultState) -> Unit,
        configScope: SessionConfigScope.() -> Unit
    ): IEditingSession {
        val session = EditingSession(
            application = context as Application,
            sessionEditingState = sessionEditingState,
            localAlgoBootstrapConfig = algoBootstrapConfig,
            globalAlgoBootstrapConfig = globalAlgoBootstrapConfig,
            configScope = configScope,
            sessionId = sessionIdGenerator.generatorId(),
            eglContext = eglContext
        )
        aliveSession[session.sessionId] = session
        return session
    }

    override fun findSession(sessionId: Int): IEditingSession? {
        return aliveSession[sessionId]
    }

    override fun closeSession(sessionId: Int) {
        aliveSession[sessionId]?.let {
            it.close()
            aliveSession.remove(sessionId)
        } ?: GLog.warn(TAG) { "[closeSession] , close session error, invalid sessionId !" }
    }

    override fun close() = Unit

    companion object {
        private const val TAG = "SessionManager"
    }
}