/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : ImageHardwareDecoderImpl.kt
 ** Description: 图片硬解码器
 ** Version    : 1.0
 ** Date       : 2024/5/28
 ** Author     : <PERSON><PERSON><PERSON>@Apps.Gallery
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <PERSON><PERSON><PERSON>@Apps.Gallery        2024/5/28    1.0              init
 *************************************************************************************************/

package com.oplus.gallery.framework.editingability.codec.image

import android.graphics.Bitmap
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.editingability.codec.CodecType
import com.oplus.gallery.framework.editingability.codec.inter.ImageDecoder
import com.oplus.gallery.framework.editingability.util.error

/**
 * 图片硬解码器实现。这也将取决于特定的库或API。
 *
 * Marked by Johnny 2024/5/28 待后续实现具体格式的解码，也可能不需要去实现硬解码，只保留图片软解码
 */
internal class ImageHardwareDecoderImpl(
    mimetype: String,
    photoMaxRatio: Float,
    photoMemoryLimit: Int,
    textureLengthLimit: Int
) : ImageDecoder(mimetype, photoMaxRatio, photoMemoryLimit, textureLengthLimit) {
    override val tag: String = TAG
    override val codecType: CodecType = CodecType.HARDWARE

    override fun assembleOptions() {
        GLog.error(TAG) { "assembleOptions, Not yet implemented" }
    }

    override fun decodeBonds() {
        GLog.error(TAG) { "decodeBonds, Not yet implemented" }
    }

    override fun assembleSampleSize() {
        GLog.error(TAG) { "assembleSampleSize, Not yet implemented" }
    }

    override fun decodeBitmap(decodeBonds: Boolean): Bitmap? {
        GLog.error(TAG) { "decodeBitmap, Not yet implemented" }
        return null
    }

    override fun resizeBitmap(bitmap: Bitmap): Bitmap? {
        GLog.error(TAG) { "resizeBitmap, Not yet implemented" }
        return null
    }

    companion object {
        private const val TAG = "Codec#ImageHardwareDecoderImpl"
    }
}