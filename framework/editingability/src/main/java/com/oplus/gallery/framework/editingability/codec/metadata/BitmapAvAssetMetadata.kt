/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : BitmapAvAssetMetadata.kt
 ** Description: Bitmap资源元信息读写实现类
 ** Version    : 1.0
 ** Date       : 2024/10/11
 ** Author     : <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                        <date>        <version>        <desc>
 ** <EMAIL>        2024/10/11    1.0              init
 *************************************************************************************************/
package com.oplus.gallery.framework.editingability.codec.metadata

import android.annotation.TargetApi
import android.graphics.Bitmap
import android.os.Build
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.addon.graphics.toUltraHdrInfo
import com.oplus.gallery.addon.utils.VersionHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata.ExtendedDataKey.ULTRA_HDR_INFO

internal class BitmapAvAssetMetadata(
    private val bitmap: Bitmap
) : IAvAssetMetadata {

    /**
     * 由使用方注入的元信息字段。资源的某些字段可能存在解析异常或者无法编辑写入的情况，此时需要业务注入进来。
     *
     * 拿Exif来举个例子：
     * 不支持Exif的图片旋转后，角度存储在业务，需要业务注入进来，此时就优先使用业务注入的数据。
     * - 只有Jpg才支持Exif读和写
     * - Heif图片只支持读Exif，不支持写Exif
     * - 其他没有Exif信息图片
     *
     * 需要主动释放这个map 否则有内存问题
     */
    private val injectedMetadata = mutableMapOf<String, Any?>()

    override fun getMetadata(type: IAvAssetMetadata.MetadataType, dataKey: String): Any? {
        return injectedMetadata[dataKey] ?: run {
            when (dataKey) {
                ULTRA_HDR_INFO -> getUHdrInfo(bitmap)
                else -> {
                    GLog.w(TAG, LogFlag.DL) { "<getMetadata> dataKey=$dataKey is not exist! return null!" }
                    null
                }
            }
        }
    }

    override fun getMetadata(dataKeys: Map<IAvAssetMetadata.MetadataType, List<String>>): Map<String, Any?> {
        val resultMap = mutableMapOf<String, Any?>()
        dataKeys.keys.forEach { type ->
            dataKeys[type]?.forEach { dataKey ->
                val result = getMetadata(type, dataKey)
                resultMap[dataKey] = result
            }
        }
        return resultMap
    }

    override fun setMetadata(type: IAvAssetMetadata.MetadataType, dataKey: String, value: Any) {
        injectedMetadata[dataKey] = value
    }

    /**
     * 获取当前Bitmap所对应的mask、hdrInfo
     *
     * @param bitmap
     * @return
     */
    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun getUHdrInfo(bitmap: Bitmap): Pair<Bitmap, UltraHdrInfo>? {
        if (bitmap.isRecycled) return null
        if (VersionHelper.isAtLeastAndroidU()) {
            val gainmap = bitmap.gainmap ?: return null
            return Pair(gainmap.gainmapContents, gainmap.toUltraHdrInfo())
        } else {
            return null
        }
    }

    /**
     * 释放内存缓存
     */
    override fun releaseCache() {
        injectedMetadata.clear()
    }

    companion object {
        private const val TAG = "Codec#BitmapAvAssetMetadata"
    }
}