/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File       : BitmapAvAssetMetadata.kt
 ** Description: Bitmap资源元信息读写实现类
 ** Version    : 1.0
 ** Date       : 2024/10/11
 ** Author     : <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                        <date>        <version>        <desc>
 ** <EMAIL>        2024/10/11    1.0              init
 *************************************************************************************************/
package com.oplus.gallery.framework.editingability.codec.metadata

import android.annotation.TargetApi
import android.graphics.Bitmap
import android.os.Build
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.addon.graphics.toUltraHdrInfo
import com.oplus.gallery.addon.utils.VersionHelper
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_ULTRA_HDR_INFO
import com.oplus.gallery.foundation.codec.extend.ExtendStruct
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata.Companion.EXCLUDED_ASSOCIATE_METADATA_KEYS
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType

internal class BitmapAvAssetMetadata(
    private val bitmap: Bitmap
) : IAvAssetMetadata {

    /**
     * 由使用方注入的元信息字段。资源的某些字段可能存在解析异常或者无法编辑写入的情况，此时需要业务注入进来。
     *
     * 拿Exif来举个例子：
     * 不支持Exif的图片旋转后，角度存储在业务，需要业务注入进来，此时就优先使用业务注入的数据。
     * - 只有Jpg才支持Exif读和写
     * - Heif图片只支持读Exif，不支持写Exif
     * - 其他没有Exif信息图片
     *
     * 需要主动释放这个map 否则有内存问题
     */
    private val injectedMetadata = mutableMapOf<String, Any?>()

    /**
     * 关联的Avasset的AvAssetMetadata
     * 1、如果从当前AvAssetMetadata没有获取到元信息，则尝试使用关联的associateMetadata获取
     * 2、associateMetadata目前对应的是原图的元信息
     * 3、人像图在处理参数化特效如  调节，裁剪旋转时不会写入人像信息，
     *   避免图发送到另外同型号手机上，还原出现问题，src图无法还原出效果图。
     *   这种状态下，效果图没有写入人像信息，项目化还原时，需要借助原图获取对应的人像信息
     */
    internal var associateMetadata: IAvAssetMetadata? = null

    /**
     * 外部注入的扩展是新 key 对应 struct
     */
    private val injectedMetadataStructs = mutableMapOf<String, ExtendStruct<*>>()

    override fun getMetadata(type: MetadataType, dataKey: String): Any? {
        return injectedMetadata[dataKey] ?: run {
            when (dataKey) {
                EXTEND_KEY_ULTRA_HDR_INFO -> getUHdrInfo(bitmap)
                else -> {
                    GLog.w(TAG, LogFlag.DL) { "<getMetadata> dataKey=$dataKey is not exist! return null!" }
                    null
                }
            }
        } ?: associateMetadata.takeUnless { dataKey in EXCLUDED_ASSOCIATE_METADATA_KEYS }?.getMetadata(type, dataKey)
    }

    override fun getMetadata(dataKeys: Map<MetadataType, List<String>>): Map<String, Any?>? {
        val resultMap = mutableMapOf<String, Any?>()
        dataKeys.keys.forEach { type ->
            dataKeys[type]?.forEach { dataKey ->
                val result = getMetadata(type, dataKey)
                resultMap[dataKey] = result
            }
        }
        return resultMap.ifEmpty { associateMetadata?.getMetadata(dataKeys) }
    }

    override fun getMetadataStruct(type: MetadataType, dataKey: String): ExtendStruct<*>? {
        return injectedMetadataStructs[dataKey] ?: run {
            GLog.e(TAG, LogFlag.DL) { "<getMetadataStruct> dataKey=$dataKey is not exist! return null!" }
            null
        } ?: associateMetadata.takeUnless { dataKey in EXCLUDED_ASSOCIATE_METADATA_KEYS }?.getMetadataStruct(type, dataKey)
    }

    override fun setMetadata(type: MetadataType, dataKey: String, value: Any) {
        injectedMetadata[dataKey] = value
    }

    override fun setMetadataStruct(type: MetadataType, dataKey: String, struct: ExtendStruct<*>) {
        injectedMetadataStructs[dataKey] = struct
    }

    /**
     * 获取当前Bitmap所对应的mask、hdrInfo
     *
     * @param bitmap
     * @return
     */
    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun getUHdrInfo(bitmap: Bitmap): Pair<Bitmap, UltraHdrInfo>? {
        if (bitmap.isRecycled) return null
        if (VersionHelper.isAtLeastAndroidU()) {
            val gainmap = bitmap.gainmap ?: return null
            return Pair(gainmap.gainmapContents, gainmap.toUltraHdrInfo())
        } else {
            return null
        }
    }

    /**
     * 释放内存缓存
     */
    override fun releaseCache() {
        injectedMetadata.clear()
        injectedMetadataStructs.clear()
    }

    companion object {
        private const val TAG = "Codec#BitmapAvAssetMetadata"
    }
}