/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvAlgoManager
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/2/1 10:26
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/2/1		  1.0		 AvAlgoManager
 *********************************************************************************/
package com.oplus.gallery.framework.editingability.algo

import android.app.Application
import com.oplus.gallery.framework.abilities.editing.algo.IAvAlgoInfo
import com.oplus.gallery.framework.abilities.editing.algo.IAvAlgoManager
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import java.util.concurrent.ConcurrentHashMap

/**
 * 算法管理器，提供特效和算法的映射表，算法的实例管理器
 */
internal class AvAlgoManager(application: Application) : IAvAlgoManager {

    internal val pluginSdkProxy = PluginSDKProxy(application)

    /**
     * 算法table映射表
     */
    override val algoRegistry: Map<AvEffect, IAvAlgoInfo> get() = _algoRegistry
    private val _algoRegistry: ConcurrentHashMap<AvEffect, IAvAlgoInfo> = ConcurrentHashMap()

    /**
     * 算法实例管理器
     */
    val algoInstanceManager: AlgoInstanceManager = AlgoInstanceManager(application, algoRegistry, pluginSdkProxy)

    /**
     * 安装算法，注册算法
     * @param effect 编辑特效
     * @param algoInfo 对应的算法
     * @param config 安装的相关信息
     */
    override fun installAlgo(effect: AvEffect, algoInfo: IAvAlgoInfo, config: Map<String, *>): Boolean {
        val supported = pluginSdkProxy.registerPlugin(algoInfo.algoName)
        if (supported) {
            _algoRegistry[effect] = algoInfo
        }
        return supported
    }

    /**
     * 算法卸载
     * @param effect 需要卸载的特效算法
     */
    override fun uninstallAlgo(effect: AvEffect) {
        _algoRegistry.remove(effect)?.let { algoInfo ->
            algoInstanceManager.releaseAlgo(algoInfo)
        }
    }

    /**
     * 配置算法资源
     * 如so库和模型库
     * @param effect 配置的特效
     * @param config 配置的相关参数
     */
    override fun configureAlgo(effect: AvEffect, config: Map<String, *>) = Unit

    /**
     * 判断算法是否支持
     * TODO("目前算法没有提供相关的接口，现有算法是否支持都是由业务层判断，后期算法sdk中，如果有相应的是否支持判断，只是要求提供静态接口")
     * @param effect 特效
     * @return 算法在当前手机平台是否支持
     */
    internal fun detectAlgoRequirement(effect: AvEffect): Boolean {
        return true
    }

    /**
     * 算法是否支持HDR
     */
    internal fun supportedHDR(effect: AvEffect): Boolean {
        return algoRegistry[effect]?.supportHDR ?: false
    }

    /**
     * 算法支持的hdr类型
     * String SCHEME_LOCAL_HDR = "local_hdr"
     * String SCHEME_ULTRA_HDR = "ultra_hdr"
     * String SCHEME_HDR_HLG = "hdr_hlg"
     * String SCHEME_HDR_PQ = "hdr_pq"
     * String SCHEME_HDR_DOLBY = "hdr_dolby"
     * String SCHEME_HDR_NONE = "hdr_none"
     */
    internal fun getHDRSchemes(effect: AvEffect): List<String> {
        return algoRegistry[effect]?.hdrSchemes ?: emptyList()
    }

    /**
     * 算法管理器资源回收
     */
    override fun recycle() {
        _algoRegistry.clear()
        algoInstanceManager.destroy()
    }
}