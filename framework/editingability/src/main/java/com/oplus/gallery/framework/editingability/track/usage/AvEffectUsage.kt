/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AvEffectUsage
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/4/3 14:17
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/4/3		  1.0		 AvEffectUsage
 *********************************************************************************/
package com.oplus.gallery.framework.editingability.track.usage

import android.util.ArrayMap
import com.oplus.gallery.foundation.util.collections.SpareMap
import com.oplus.gallery.foundation.util.debug.GLog.e
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_V
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.editing.editor.EditorResultState
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArraySet

/**
 * 特效及使用参数
 * @param effect 使用的特效类型
 */
internal class AvEffectUsage(override val effect: AvEffect) : IAvEffectUsage, IArgumentAccessor {
    // 特效最后修改时间
    internal var lastModified: Long = System.currentTimeMillis()
    private val uid = UUID.randomUUID().toString()
    private val lockObject = Any()

    /**
     * 对编辑参数中key-value的变化观测者集合
     */
    override val observers: Map<String, Set<IAvEffectUsage.ValueObserver>> get() = _observers
    private val _observers: MutableMap<String, MutableSet<IAvEffectUsage.ValueObserver>> = ConcurrentHashMap()

    /**
     * 特效的参数集
     */
    val arguments: SpareMap<String, Any?> get() = _arguments
    private val _arguments: SpareMap<String, Any?> = SpareMap()

    /**
     * 下一帧算法需要用到的UI层参数
     */
    val uiOverlayArg: SpareMap<String, Any?> get() = _uiOverlayArg
    private val _uiOverlayArg: SpareMap<String, Any?> = SpareMap()

    /**
     * 是否需要进行缓存
     * - should_cached_for_user：业务要求需要缓存
     * - should_cached_for_algo：算法要求需要缓存
     */
    var shouldCached: Boolean = true

    /**
     * 下一帧算法需要用到的上下文信息
     */
    private val algoStoreableOverlayArg: SpareMap<String, Any?> = SpareMap()

    val resultListeners: Set<(EditorResultState) -> Unit> get() = mutableSetOf<(EditorResultState) -> Unit>().apply { addAll(_resultListeners) }
    private val _resultListeners: MutableSet<(EditorResultState) -> Unit> = mutableSetOf()

    internal fun modifyArg(key: String, value: Any?) {
        synchronized(lockObject) {
            _uiOverlayArg[key] = value
            lastModified = System.currentTimeMillis()
        }
    }

    override fun <T> getArg(key: String): T? {
        synchronized(lockObject) {
            return _arguments[key] as? T? ?: let {
                _uiOverlayArg[key] as? T? ?: let {
                    algoStoreableOverlayArg[key] as? T?
                }
            }
        }
    }

    fun setArg(key: String, value: Any?) {
        synchronized(lockObject) {
            _uiOverlayArg[key] = value
            lastModified = System.currentTimeMillis()
        }
    }

    override fun addValueObserver(key: String, observer: IAvEffectUsage.ValueObserver) {
        _observers[key] ?: let { _observers[key] = CopyOnWriteArraySet() }
        _observers[key]?.add(observer)
    }

    override fun addValueObservers(vararg key: String, observer: IAvEffectUsage.ValueObserver) {
        key.forEach {
            addValueObserver(it, observer)
        }
    }

    override fun removeValueObserver(key: String, observer: IAvEffectUsage.ValueObserver) {
        _observers[key]?.remove(observer)
    }

    override fun removeValueObservers(vararg key: String, observer: IAvEffectUsage.ValueObserver) {
        key.forEach {
            removeValueObserver(it, observer)
        }
    }

    override fun addResultListener(listener: (EditorResultState) -> Unit) {
        _resultListeners.add(listener)
    }

    override fun removeResultListener(listener: (EditorResultState) -> Unit) {
        _resultListeners.remove(listener)
    }

    override fun removeAllResultListener() {
        _resultListeners.clear()
    }

    /**
     * requestCode 特效或者修改特效的请求码，一次效果执行的唯一标识
     */
    override val requestCode: Int get() = requestCodeNum
    internal var requestCodeNum: Int = 0

    /**
     * 当前效果的缓存key。
     * - 每次[mergeArg]后都需要进行重新构建
     * - Debug详细信息，请打开[GProperty.DEBUG_EDITING_ABILITY]和[GProperty.DEBUG_V]开关
     *
     * 生成规则：
     * 当前usage在第i个，effectCacheKey = "$prefixKey&uiOverlayKeys[i-1]&uiOverlayKeys[i-2]|...&uiOverlayKeys[0]",
     * prefixKey由Layer指定。
     */
    var effectCacheKey: String = TextUtil.EMPTY_STRING

    /**
     * 当前效果的gpu缓存key
     */
    val effectGpuCacheKey: String get() = "$effectCacheKey$GPU_KEY"

    /**
     * 特效的结果缓存，如果有hdr的 HDR增益图 cacheKey
     * ${effectCacheKey}&Gainmap
     */
    val gainBitmapCacheKey: String get() = "$effectCacheKey$GAINMAP_KEY"

    /**
     * 特效的结果gpu缓存，如果有hdr的 HDR增益图 cacheKey
     * ${gainBitmapCacheKey}&GPU
     */
    val gainGpuCacheKey: String get() = "$gainBitmapCacheKey$GPU_KEY"

    /**
     * 特效的增益信息缓存key
     * ${effectCacheKey}&Metadata
     */
    val metadataCacheKey: String get() = "$effectCacheKey$METADATA"

    /**
     * 使用[uiOverlayArg]数据生成一个cacheKey，用来进行后续的[effectCacheKey]生成。
     *
     * 生成规则：[mergeArg]前，将[_uiOverlayArg]所有参数按照key，value方式连接起来。
     */
    internal var uiOverlayKey: String = TextUtil.EMPTY_STRING

    private fun generateUiOverlayCacheKey() {
        val argumentsStr = _uiOverlayArg.toMap()
            .map { "${it.key}=${it.value}" }
            .joinToString(separator = "&")
        uiOverlayKey = if (GProperty.DEBUG_EDITING_ABILITY && DEBUG_V) {
            argumentsStr
        } else {
            argumentsStr.hashCode().toString()
        }
    }

    /**
     * FrameRenderingRequest 一帧消耗的绘制  onRenderingSuccessRely
     * 触发轨道结构中 AVEffectUsage mergeArg （再下一帧响应参数的变化）
     * 轨道合成器在 执行对所有的 AVEffectUsage mergeArg之后应该在触发一帧渲染，也就是这一次轨道结构的变化触发的渲染，不是通过观测触发的
     * TODO (Marked by wanglian  for wanglian 这里需要修改)
     */
    override fun mergeArg() {
        if (_uiOverlayArg.isEmpty && algoStoreableOverlayArg.isEmpty) {
            return
        }
        synchronized(lockObject) {
            if (_uiOverlayArg.isNotEmpty()) {
                // merge前，用 uiOverlayArg 生成一个用来标记此时usage状态的key
                generateUiOverlayCacheKey()
                // 将 uiOverlayArg merge到参数集中
                _arguments.putAll(_uiOverlayArg.toMap())
                _uiOverlayArg.clear()
            }

            shouldCached = _arguments[KEY_SHOULD_CACHED_FOR_USER] as? Boolean ?: true

            // 将 算法可存储参数 merge到参数集中
            if (algoStoreableOverlayArg.isNotEmpty()) {
                _arguments.putAll(algoStoreableOverlayArg.toMap())

                /**
                 * 这里处理StoreableOverly的数据观测回调
                 */
                _observers.forEach { (key, setValueObserver) ->
                    algoStoreableOverlayArg[key]?.let { value ->
                        setValueObserver.forEach { observer ->
                            observer.onValueChanged(key, value)
                        }
                    }
                }
                algoStoreableOverlayArg.clear()
            }
        }
    }

    override fun storeableArgCallBack(map: ArrayMap<String, Any?>) {
        algoStoreableOverlayArg.putAll(map)
    }


    override fun toString(): String {
        return kotlin.runCatching {
            return@runCatching "AvEffectUsage(effect=$effect, effectCacheKey='$effectCacheKey', arguments=${
                arguments.entries.joinToString(prefix = "{ ", postfix = " }") { entry ->
                    val key = entry.key.toString()
                    val value = when (val entryValue = entry.value) {
                        is Array<*> -> entryValue.contentToString()
                        else -> entryValue.toString()
                    }
                    "[ key = $key, value = $value ]"
                }
            })"
        }.onFailure {
            e(TAG, LogFlag.DL, "[toString] runCatching onFailure ${it.message}")
        }.getOrDefault(TextUtil.EMPTY_STRING)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as AvEffectUsage

        if (uid != other.uid) return false

        return true
    }

    override fun hashCode(): Int {
        return uid.hashCode()
    }

    internal fun destroy() {
        synchronized(lockObject) {
            arguments.clear()
            uiOverlayArg.clear()
            algoStoreableOverlayArg.clear()
        }
    }

    companion object {
        private const val TAG = "AvEffectUsage"
        private const val GAINMAP_KEY = "&Gainmap"
        private const val GPU_KEY = "&GPU"
        private const val METADATA = "&Metadata"
        private const val KEY_SHOULD_CACHED_FOR_USER = "should_cached_for_user"
    }
}