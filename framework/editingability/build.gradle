plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}
apply from: "${rootDir}/gradle/libCommon.gradle"

ext {
    mavenDescription = "编辑能力模块"
    mavenGroupId = mavenGroupName
}

android {
    namespace 'com.oplus.gallery.framework.editingability'
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            matchingFallbacks = ['release', 'debug']
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            matchingFallbacks = ['release']
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementationProject(':foundation:libcache')
    implementationProject(':foundation:libcodec')
    implementationProject(':foundation:libfileaccess')
    // 解析uhdr信息需要依赖 OplusImageHdrWrapper
    implementationProject(':foundation:libsysapi')
    implementationProject(':foundation:libutil')
    implementationProject(':foundation:libutiltmp')
    implementationProject(':foundation:libexif')
    implementationProject(':framework:abilityapi')
    implementationProject(':foundation:libopengl')
    implementation "androidx.core:core-ktx:$coreKtxVersion"
    implementation "androidx.graphics:graphics-core:$graphicsCore"
    implementation "androidx.appcompat:appcompat:$appcompatVersion"
    implementation "com.google.android.material:material:$materialVersion"
    implementation "com.oplus.appability:appabilitylib:$abilityapiLibVersion"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinxCoroutinesCore"
    implementation "com.oplus.camera:IPUapi:$ipuVersion"
    // 编辑能力算法插件池sdk
    implementation "com.oplus.tbluniformeditor:libpluginpool:$editorPlugin_pool"

    implementation("com.oplus.tbluniformeditor:foundation-ipu:$editorPlugin_foundationIpu")
    implementation("com.oplus.tbluniformeditor:foundation-basictone:$editorPlugin_foundationBasicTone")

    implementation "com.oplus.tbluniformeditor:libaieliminateplugin:$editorPlugin_aiEliminate"
    implementation "com.oplus.tbluniformeditor:libmagiceliminateplugin:$editorPlugin_magicEliminate"
    implementation "com.oplus.tbluniformeditor:libblurplugin:$editorPlugin_blur"
    implementation "com.oplus.tbluniformeditor:foundation-meicam:$editorPlugin_foundationMeicam"
    implementation "com.oplus.tbluniformeditor:libfilterplugin:$editorPlugin_filter"

    implementation("com.oplus.tbluniformeditor:libwatermarkplugin:$editorPlugin_watermark") {
        // MarkBy TangHui：exclude COUISupport 旧版本结构的所有子包
        exclude group: 'com.oplus.appcompat', module: 'base'
        exclude group: 'com.oplus.appcompat', module: 'controls'
        exclude group: 'com.oplus.appcompat', module: 'lists'
        exclude group: 'com.oplus.appcompat', module: 'bars'
        exclude group: 'com.oplus.appcompat', module: 'component'
        exclude group: 'com.oplus.appcompat', module: 'panel'
        exclude group: 'com.oplus.appcompat', module: 'responsiveui'
        exclude group: 'com.oplus.appcompat', module: 'nearx'
    }

    implementation("com.oplus.tbluniformeditor:libipufilterplugin:$editorPlugin_ipuFilter")

    implementation "com.oplus.tbluniformeditor:libtransformplugin:$editorPlugin_transform"
    implementation("com.oplus.tbluniformeditor:libairepairplugin:$editorPlugin_aiRepair") {
        exclude(group: 'com.oplus.aiunit.open', module: 'eraser')
    }
    implementation("com.oplus.tbluniformeditor:libimagequalityenhanceplugin:$editorPlugin_imageQualityEnhance") {
        exclude(group: 'com.oplus.aiunit.open', module: 'eraser')
    }
    implementation("com.oplus.tbluniformeditor:libaicompositionplugin:$editorPlugin_aiComposition")
    implementation ("com.oplus.tbluniformeditor:libadjustmentplugin:$editorPlugin_adjustment")
    implementation "com.oplus.tbluniformeditor:libbeautyplugin:$editorPlugin_beauty"
    implementation "com.oplus.tbluniformeditor:libipubeautyplugin:$editorPlugin_ipubeauty"
    implementation "com.oplus.tbluniformeditor:libphotoframeplugin:$editorPlugin_photoFrame"
    implementation("com.oplus.tbluniformeditor:libtextureblendplugin:$editorPlugin_textureBlend") {
        exclude(group: 'com.oplus.tbluniformeditor', module: 'libpluginpool')
    }
    implementation "com.oplus.tbluniformeditor:libaiwatermarkplugin:$editorPlugin_aiWatermark"
    implementation "com.oplus.tbluniformeditor:libemptyplugin:$editorPlugin_repair"
    implementation "com.oplus.tbluniformeditor:libmosaicplugin:$editorPlugin_mosaic"
    implementation "com.oplus.tbluniformeditor:libaihdplugin:$editorPlugin_aihd"
    implementation "com.oplus.tbluniformeditor:libimagereplaceplugin:$editorPlugin_imageReplace"
    implementation ("com.oplus.tbluniformeditor:libaigraffitiplugin:$editorPlugin_aigraffiti") {
        exclude(group: 'com.oplus.aiunit', module: 'meowai')
    }
    implementation("com.oplus.tbluniformeditor:libaibesttakeplugin:$editorPlugin_aibesttake") {
        exclude(group: 'com.oplus.tbluniformeditor', module: 'foundation-common')
        exclude(group: 'com.oplus.tbluniformeditor', module: 'libpluginpool')
    }
    implementation("com.oplus.tbluniformeditor:libaibesttakeplugin:$editorPlugin_aibesttake") {
        exclude(group: 'com.oplus.tbluniformeditor', module: 'foundation-common')
        exclude(group: 'com.oplus.tbluniformeditor', module: 'libpluginpool')
    }
    /**
     * 背景 & 原因：
     * gradle 依赖冲突的默认解决策略是：根据标准版本号（x.x.x）判定，一般会根据场景自动使用最新版本；
     * 目前 editorPlugin_aiEliminate、editorPlugin_mosaic 等库会间接使用 common，并且使用的版本不同。
     * 但比较坑的是，这些库的版本都是一样的（master2.0.0），不同点仅仅是后缀 hash，
     * 导致 gradle 无法有效解决依赖冲突，随机选择了一个较旧的版本，使依赖 common 的 mosaic 插件库功能异常。
     *
     * 方案：
     * 临时强制指定 foundation-common 的版本号，待 tbluniformeditor 相关库按照正式版本发布后即可移除
     * Marked By zhangjisong
     */
    implementation("com.oplus.tbluniformeditor:foundation-common:$editorPlugin_common") {
        exclude(group: 'androidx.graphics', module: 'graphics-core')
    }

    implementationProject(':foundation:libhdrtransform')
    implementation("com.oplus.tbluniformeditor:libailightingplugin:$editorPlugin_aiLighting") {
        exclude(group:'com.oplus.tbluniformeditor', module: 'libpluginpool')
        exclude(group: 'com.squareup.okhttp3', module: 'okhttp')
    }
}
