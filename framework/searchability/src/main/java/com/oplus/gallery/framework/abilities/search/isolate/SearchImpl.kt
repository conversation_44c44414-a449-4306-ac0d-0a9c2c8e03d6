/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SearchImpl.kt
 ** Description:搜索接口实现类，负责集成实际使用的SDK
 ** Version: 1.0
 ** Date: 2023/10/11
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/10/11     1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.isolate

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.Cursor.FIELD_TYPE_BLOB
import android.database.Cursor.FIELD_TYPE_FLOAT
import android.database.Cursor.FIELD_TYPE_INTEGER
import android.database.Cursor.FIELD_TYPE_NULL
import android.database.Cursor.FIELD_TYPE_STRING
import android.database.MatrixCursor
import android.os.Bundle
import androidx.core.database.getBlobOrNull
import androidx.core.database.getFloatOrNull
import androidx.core.database.getIntOrNull
import androidx.core.database.getStringOrNull
import com.oplus.andes.photos.kit.AndesPhotosKit
import com.oplus.andes.photos.kit.search.ability.ICompletionAbility
import com.oplus.andes.photos.kit.search.common.DataObserver
import com.oplus.andes.photos.kit.search.component.IConfigProvider
import com.oplus.andes.photos.kit.search.data.AndesAlbumEntry
import com.oplus.andes.photos.kit.search.data.AndesDateTimeEntry
import com.oplus.andes.photos.kit.search.data.AndesKeywordCache
import com.oplus.andes.photos.kit.search.data.AndesKeywordEntry
import com.oplus.andes.photos.kit.search.data.AndesMediaIdEntry
import com.oplus.andes.photos.kit.search.data.AndesMemoriesAlbumEntry
import com.oplus.andes.photos.kit.search.data.AndesMultiQueryResult
import com.oplus.andes.photos.kit.search.data.AndesMultiQueryResult.SEARCH_COMPOSITE_RESULT_ENTRIES
import com.oplus.andes.photos.kit.search.data.AndesMultiQueryResult.SEARCH_RESULT_KEYWORD_ENTRIES
import com.oplus.andes.photos.kit.search.data.CompositeData
import com.oplus.andes.photos.kit.search.strategy.CompositeStrategyType
import com.oplus.andes.photos.kit.utils.LogLevel.LOG_LEVEL_DEBUG
import com.oplus.andes.photos.kit.utils.LogUtil
import com.oplus.gallery.business_lib.model.config.allowlist.AndesSearchRusConfig
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry
import com.oplus.gallery.business_lib.model.data.base.entry.DateTimeEntry
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.business_lib.model.data.location.utils.PoiDBHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ANDES_SEARCH
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_CAPTION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_MULTIMODAL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OCREMBEDDING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.ABILITY_IS_SUPPORT_CAPTION_EMBEDDING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.MultiModal.ABILITY_IS_SUPPORT_MULTIMODAL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.OcrEmbedding.ABILITY_IS_SUPPORT_OCR_EMBEDDING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.ANDES_SEARCH_SYNC_ALL_COMPLETED
import com.oplus.gallery.framework.abilities.search.SearchAbilityImpl
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.completion.AlbumCompletion
import com.oplus.gallery.framework.abilities.search.completion.DateTimeCompletion
import com.oplus.gallery.framework.abilities.search.completion.FestivalCompletion
import com.oplus.gallery.framework.abilities.search.completion.LabelCompletion
import com.oplus.gallery.framework.abilities.search.completion.LocationCompletion
import com.oplus.gallery.framework.abilities.search.completion.MemoryCompletion
import com.oplus.gallery.framework.abilities.search.completion.PersonCompletion
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.framework.abilities.search.dmp.DmpAbility
import com.oplus.gallery.framework.abilities.search.dmpsync.DmpSearchSyncHelper
import com.oplus.gallery.framework.abilities.search.entry.KeywordEntry
import com.oplus.gallery.framework.abilities.search.result.MultiQueryResult
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.sqlite.SqliteAbility
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 搜索接口实现类，负责集成实际使用的SDK
 */
internal class SearchImpl(
    private val context: Context,
    private val festivalSelector: FestivalSelector,
    private val keywordCacheManager: KeywordCacheManager
) : ISearch {

    private val keywordCache = keywordCacheManager.keywordCache

    private val dmpAbility = DmpAbility(context, keywordCacheManager)

    private val isAndesSearchSupport: Boolean
        get() {
            return SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_ANDES_SEARCH) ?: false
        }

    private val isIndexSearchEnable: Boolean
        get() {
            return isAndesSearchSupport && (SearchConst.INDEX_SEARCH && DEBUG_SEARCH)
        }

    private val currentThreadInfo = Thread.currentThread().id.toString() + TextUtil.STRIKE + Thread.currentThread().name

    private var isAndesPhotosKitInit = false

    private var isAndesSearchQueryAllMediaDataCompleted: Boolean =
        SearchAbilityImpl.configAbility?.getBooleanConfig(ANDES_SEARCH_SYNC_ALL_COMPLETED) ?: false

    private val andesSearchKitLog by lazy {
        object : LogUtil.ILog {
            override fun v(tag: String?, msg: String?) {
                GLog.v(tag, msg)
            }

            override fun d(tag: String?, msg: String?) {
                GLog.d(tag, msg)
            }

            override fun i(tag: String?, msg: String?) {
                GLog.i(tag, msg)
            }

            override fun w(tag: String?, msg: String?) {
                GLog.w(tag, msg)
            }

            override fun w(tag: String?, msg: String?, throwable: Throwable?) {
                GLog.w(tag, msg, throwable)
            }

            override fun e(tag: String?, msg: String?) {
                GLog.e(tag, msg)
            }

            override fun e(tag: String?, msg: String?, throwable: Throwable?) {
                GLog.e(tag, msg, throwable)
            }
        }
    }

    override fun init() {
        AndesSearchRusConfig.setAndesRusListener(andesSearchRusListener)
        // 开启新的线程初始化提高速度，与DMP初始化并行
        AppScope.launch(Dispatchers.IO) {
            if (isAndesSearchSupport.not()) {
                GLog.w(TAG, "[init] AndesSearch feature not support")
                return@launch
            }
            if (!isAndesPhotosKitInit) {
                initAndeSearchSdk()
            }
        }
        if (isAndesSearchSupport) {
            DmpSearchSyncHelper.init(context)
            if (isIndexSearchEnable) {
                DmpSearchSyncHelper.setSearchQueryListener(searchQueryListener)
            }
            // kit内部会用到中子SDK的切词能力，即使索引配置开关关闭，也需要初始化dmp sdk
            val canInjectDict = initDmpSdk() && keywordCache.isKeywordCacheLoaded
            GLog.d(TAG, LogFlag.DL) { "init, canInjectDict=$canInjectDict, isKeywordCacheLoaded=${keywordCache.isKeywordCacheLoaded}" }
            if (canInjectDict) {
                injectDmpDict()
            }
        }
    }

    private fun initAndeSearchSdk() {
        isAndesPhotosKitInit = true
        val startTime = System.currentTimeMillis()
        AndesPhotosKit.INSTANCE.setContext(context)
        AndesPhotosKit.INSTANCE.registerMediaDataObserver()
        if (DEBUG) {
            AndesPhotosKit.INSTANCE.initLog(andesSearchKitLog, LOG_LEVEL_DEBUG)
            registerSearchInfoListener()
        }
        registerKeywordCacheListener()
        setConfigProvider(AndesSearchRusConfig.andesSearchConfig)
        AndesPhotosKit.INSTANCE.setSqliteRetrieval(SqliteAbility())
        if (((SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_MULTIMODAL, false) == true) &&
                    (SearchAbilityImpl.configAbility?.getBooleanConfig(ABILITY_IS_SUPPORT_MULTIMODAL, false) == true))
            || ((SearchConst.MULTI_MODAL_SWITCH && DEBUG_SEARCH))
        ) {
            AndesPhotosKit.INSTANCE.setMultiModalRetrieval(SearchAbilityImpl.multiModalAbility)
        }
        if (((SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_OCREMBEDDING, false) == true) &&
                    (SearchAbilityImpl.configAbility?.getBooleanConfig(ABILITY_IS_SUPPORT_OCR_EMBEDDING, false) == true))
            || ((SearchConst.OCR_EMBEDDING_SWITCH && DEBUG_SEARCH))
        ) {
            AndesPhotosKit.INSTANCE.setOcrEmbeddingRetrieval(SearchAbilityImpl.ocrEmbeddingAbility)
        }
        if (((SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_CAPTION, false) == true) &&
                    (SearchAbilityImpl.configAbility?.getBooleanConfig(ABILITY_IS_SUPPORT_CAPTION_EMBEDDING, false) == true))
            || ((SearchConst.CAPTION_SWITCH && DEBUG_SEARCH))
        ) {
            AndesPhotosKit.INSTANCE.setCaptionRetrieval(SearchAbilityImpl.captionAbility)
        }

        AndesPhotosKit.INSTANCE.init(context)
        GLog.d(TAG, "[initAndeSearchSdk] costTime:${GLog.getTime(startTime)}, thread:$currentThreadInfo")
        injectCompletionAbility()
    }

    private fun initDmpSdk(): Boolean {
        val startTimeDmp = System.currentTimeMillis()
        val dmpInitResult = dmpAbility.init()
        if (dmpInitResult.not()) {
            GLog.e(TAG, "[init] dmp init failed, skip AndesPhotoKit init")
            return false
        } else {
            AndesPhotosKit.INSTANCE.setDmpRetrieval(dmpAbility)
            GLog.d(TAG, "[init] dmp init costTime:${GLog.getTime(startTimeDmp)}")
        }
        return true
    }

    private fun injectDmpDict() {
        dmpAbility.addCustomDict()
    }

    private fun injectCompletionAbility() {
        val completionAbilities = mutableListOf<ICompletionAbility>()
        completionAbilities.add(LabelCompletion(context, keywordCacheManager))
        completionAbilities.add(AlbumCompletion(context, keywordCacheManager))
        completionAbilities.add(DateTimeCompletion(context, keywordCacheManager))
        completionAbilities.add(FestivalCompletion(context, festivalSelector, keywordCacheManager))
        completionAbilities.add(LocationCompletion(context, keywordCacheManager))
        completionAbilities.add(MemoryCompletion(context, keywordCacheManager))
        completionAbilities.add(PersonCompletion(context, keywordCacheManager))
        AndesPhotosKit.INSTANCE.setCompletionAbilities(completionAbilities)
    }

    private fun registerSearchInfoListener() {
        AndesPhotosKit.INSTANCE.registerSearchInfoListener { searchInfo ->
            GLog.d(
                TAG, "[onSearch] info:${searchInfo.info}, step:${searchInfo.searchStep}, stepStatus:${searchInfo.stepStatus}, " +
                        "statusInner:${searchInfo.statusInner}, costTime:${searchInfo.costTime}"
            )
        }
    }

    private val keywordCacheListener = object : KeywordCacheManager.CacheListener {
        override fun cacheReady() {
            GLog.d(TAG, "[cacheReady] keywordCache")
            if (isAndesSearchSupport) {
                injectDmpDict()
                // 更新后的keywordCache数据需要同步给kit
                syncKeywordDataToKit()
            }
        }
    }

    override fun syncAllMediaDataToDmp() {
        if (isAndesSearchSupport && isIndexSearchEnable) {
            if (LabelDictionary.isDictionaryLoaded().not()) {
                GLog.w(TAG, "[onQueryAllMediaData] labelDictionary is not loaded")
            }
            if (isAndesSearchQueryAllMediaDataCompleted) {
                GLog.w(TAG, "[onQueryAllMediaData] Query all mediaData is completed, ignore")
                return
            }
            AndesPhotosKit.INSTANCE.setTemporaryDmpEnable(false)
            val allQueryEntries = MediaDataEntryHelper.queryMediaDataToMediaDataEntry(DataObserver.Operation.SYNCHRONIZE_ALL)
            AndesPhotosKit.INSTANCE.notifyMediaDataSetChanged(
                allQueryEntries,
                DataObserver.Operation.SYNCHRONIZE_ALL
            ) { isSuccess, failedCount ->
                if (allQueryEntries.isNotEmpty() && isSuccess) {
                    AndesPhotosKit.INSTANCE.setTemporaryDmpEnable(true)
                    SearchAbilityImpl.configAbility?.setBooleanConfig(ANDES_SEARCH_SYNC_ALL_COMPLETED, true)
                    isAndesSearchQueryAllMediaDataCompleted = true
                }
                GLog.d(TAG, "[onQueryAllMediaData] isSuccess: $isSuccess, failedCount:$failedCount")
            }
        }
    }

    private val searchQueryListener = object : DmpSearchSyncHelper.OnSearchQueryListener {

        override fun onMediaDataDelete(table: String, selection: String?, selectionArgs: Array<String>?, values: ContentValues?) {
            AndesPhotosKit.INSTANCE.setTemporaryDmpEnable(false)
            if (!isAndesSearchQueryAllMediaDataCompleted) {
                GLog.w(TAG, "[onMediaDataDelete] Query all mediaData not completed, ignore")
                return
            }
            MediaDataEntryHelper.queryReqBuild(table, selection, selectionArgs, values, DataObserver.Operation.DELETE)
        }

        override fun onMediaDataUpdate(table: String, selection: String?, selectionArgs: Array<String>?, values: ContentValues?) {
            AndesPhotosKit.INSTANCE.setTemporaryDmpEnable(false)
            if (!isAndesSearchQueryAllMediaDataCompleted) {
                GLog.w(TAG, "[onMediaDataUpdate] Query all mediaData not completed, ignore")
                return
            }
            MediaDataEntryHelper.queryReqBuild(table, selection, selectionArgs, values, DataObserver.Operation.UPDATE)
        }

        override fun onQueryDeleteMediaData() {
            if (!isAndesSearchQueryAllMediaDataCompleted) {
                GLog.w(TAG, "[onQueryDeleteMediaData] Query all mediaData not completed, ignore")
                return
            }
            AndesPhotosKit.INSTANCE.notifyMediaDataSetChanged(
                MediaDataEntryHelper.queryMediaDataToMediaDataEntry(DataObserver.Operation.DELETE), DataObserver.Operation.DELETE
            ) { _, _ ->
                AndesPhotosKit.INSTANCE.setTemporaryDmpEnable(true)
            }
        }

        override fun onQueryUpdateMediaData() {
            if (!isAndesSearchQueryAllMediaDataCompleted) {
                GLog.w(TAG, "[onQueryUpdateMediaData] Query all mediaData not completed, skip")
                return
            }
            AndesPhotosKit.INSTANCE.notifyMediaDataSetChanged(
                MediaDataEntryHelper.queryMediaDataToMediaDataEntry(DataObserver.Operation.UPDATE), DataObserver.Operation.UPDATE
            ) { _, _ ->
                AndesPhotosKit.INSTANCE.setTemporaryDmpEnable(true)
            }
        }
    }

    private val andesSearchRusListener = object : AndesSearchRusConfig.OnAndesRusConfigLoadListener {
        override fun onLoadFinished() {
            GLog.d(TAG, "[andesSearchRusListener] Andes RUS update")
            init()
        }
    }

    private fun registerKeywordCacheListener() {
        keywordCacheManager.registerCacheListener(keywordCacheListener)
    }

    private fun unregisterKeywordCacheListener() {
        keywordCacheManager.unregisterCacheListener(keywordCacheListener)
    }

    override fun release() {
        AndesSearchRusConfig.setAndesRusListener(null)
        if (isAndesSearchSupport) {
            AndesPhotosKit.INSTANCE.release()
            unregisterKeywordCacheListener()
            DmpSearchSyncHelper.setSearchQueryListener(null)
            DmpSearchSyncHelper.release()
        }
    }

    override fun search(
        queryWord: String,
        searchType: Int,
        labelId: Int?,
        groupId: String?,
        bucketId: String?,
        memoriesId: String?,
        locationType: Int?
    ): Cursor? {
        if (checkSearchCondition().not()) return null
        val startTime = System.currentTimeMillis()
        var cursor: Cursor? = null
        var finalLabelId: Int = LabelSearchEngine.INVALID_ID
        cursor = when (searchType) {
            SearchType.TYPE_LABEL, SearchType.TYPE_GUIDE_LABEL -> {
                finalLabelId = labelId ?: LabelSearchEngine.INVALID_ID
                if (finalLabelId == LabelSearchEngine.INVALID_ID) {
                    finalLabelId = getLabelIdByLabelName(queryWord)
                }
                if (finalLabelId == LabelSearchEngine.INVALID_ID) {
                    GLog.w(TAG, "[search] finalLabelId is invalid id.")
                }
                if (searchType == SearchType.TYPE_GUIDE_LABEL && finalLabelId == LabelSearchEngine.INVALID_ID) {
                    return getLabelResultFromLabelGraph(context, queryWord)
                } else {
                    AndesPhotosKit.INSTANCE.smartSearchByLabelId(queryWord, finalLabelId.toString())
                }
            }
            SearchType.TYPE_PERSON -> AndesPhotosKit.INSTANCE.smartSearchByPersonId(queryWord, groupId)
            SearchType.TYPE_MEMORIES_ALBUM -> AndesPhotosKit.INSTANCE.smartSearchByMemoryId(queryWord, memoriesId)
            SearchType.TYPE_ALBUM -> AndesPhotosKit.INSTANCE.smartSearchByBucketId(queryWord, bucketId)
            SearchType.TYPE_LOCATION -> {
                AndesPhotosKit.INSTANCE.smartSqlSearchByEntityType(queryWord,
                    getLocationEntityType(locationType, queryWord))
            }
            else -> AndesPhotosKit.INSTANCE.smartSearch(queryWord, searchType)
        }
        cursor = buildCursor(cursor)
        GLog.d(
            TAG, "[search] finished query:$queryWord, searchType:$searchType, groupId:$groupId, labelId:$finalLabelId, " +
                    "bucketId:$bucketId, memoriesId:$memoriesId, locationEntityType:${getLocationEntityType(locationType, queryWord)}, " +
                    "result dimension size:${cursor?.count}, costTime:${GLog.getTime(startTime)}"
        )
        return cursor
    }

    /**
     * 知识图谱推荐的一定是有结果的，此处作为知识图谱标签推荐的兜底策略，通常用不上
     *
     * @param context 上下文对象
     * @param singleKeyword 单一的查询词
     * @return cursor
     */
    private fun getLabelResultFromLabelGraph(
        context: Context,
        singleKeyword: String,
    ): MatrixCursor? {
        if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(context)) {
            val multiQueryResult = MultiQueryResult()
            val singleQueryResult = SingleQueryResult(singleKeyword)
            val searchResult = LabelGraphSearchAbility.accurateSearch(context, singleKeyword, false)
            searchResult.forEach { (key, value) ->
                singleQueryResult.appendQueryResult(value, key, SearchType.TYPE_LABEL)
            }
            multiQueryResult.addKeywordResult(singleQueryResult)
            return multiQueryResult.buildCursorForQueryResult()
        }
        return null
    }

    private fun getLocationEntityType(locationType: Int?, queryWord: String): Int {
        if (locationType == null) {
            return LocationCompletion.getEntityType(queryWord)
        }
        return LocationCompletion.covertLocationTypeToEntityType(locationType)
    }

    private fun getLabelIdByLabelName(labelName: String): Int {
        var finalLabelId = LabelCompletion.getLabelId(labelName)
        if (finalLabelId == LabelSearchEngine.INVALID_ID && LabelGraphSearchAbility.checkAvailabilityForLabelGraph(context)) {
            finalLabelId = LabelGraphSearchAbility.queryLabelBeanByLabelName(labelName)
        } else {
            if (finalLabelId == LabelSearchEngine.INVALID_ID) {
                finalLabelId = LabelSearchEngine.getInstance().getLabelIdByName(labelName)
            }
            if (finalLabelId == LabelSearchEngine.INVALID_ID) {
                finalLabelId = LabelSearchEngine.getInstance().getLabelSynonymId(labelName)
            }
        }
        return finalLabelId
    }

    private fun checkSearchCondition(): Boolean {
        if (AndesPhotosKit.INSTANCE.isInit.not()) {
            GLog.w(TAG, "[checkSearchCondition] AndesPhotoKit has not initialized")
            return false
        }
        if (dmpAbility.initialized.get().not()) {
            GLog.d(TAG, "[checkSearchCondition] dmpAbility has not initialized")
        }
        if (keywordCache.isKeywordCacheLoaded.not()) {
            GLog.d(TAG, "[checkSearchCondition] keywordCache not loaded yet")
        }
        return true
    }

    override fun searchByLabelId(queryWord: String?, labelId: String): Cursor? {
        val cursor = queryWord?.let { AndesPhotosKit.INSTANCE.smartSearchByLabelId(it, labelId) }
        return buildCursor(cursor)
    }

    override fun searchByPersonId(queryWord: String?, personId: String): Cursor? {
        val cursor = queryWord?.let { AndesPhotosKit.INSTANCE.smartSearchByPersonId(it, personId) }
        return buildCursor(cursor)
    }

    override fun searchByBucketId(queryWord: String?, bucketId: String): Cursor? {
        val cursor = queryWord?.let { AndesPhotosKit.INSTANCE.smartSearchByBucketId(it, bucketId) }
        return buildCursor(cursor)
    }

    private fun buildCursor(cursor: Cursor?): Cursor? {
        GTrace.traceBegin("SearchImpl.buildCursor")
        val startTime = System.currentTimeMillis()
        return cursor?.use {
            if (cursor.count <= 0) {
                GLog.d(TAG, LogFlag.DL) { "[buildCursor] cursor is null" }
                return null
            }
            val resultCursor = MatrixCursor(AndesMultiQueryResult.COLUMN_NAME)
            val andesKeywordEntries: List<AndesKeywordEntry>? = cursor.extras.getParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES)
            val andesGridEntries: ArrayList<CompositeData>? = cursor.extras.getParcelableArrayList(SEARCH_COMPOSITE_RESULT_ENTRIES)
            val keywordEntries = andesKeywordEntries?.map {
                KeywordEntry(it.mType, it.mName, it.mGroupId, it.mAlbumType, it.mIsMultiSearch)
            }
            while (cursor.moveToNext()) {
                inflateMatrixCursorFromCursor(cursor, resultCursor)
            }
            val bundle = Bundle()
            bundle.putParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES, keywordEntries as ArrayList<KeywordEntry>)
            bundle.putParcelableArrayList(SEARCH_COMPOSITE_RESULT_ENTRIES, andesGridEntries)
            resultCursor.extras = bundle
            GTrace.traceEnd()
            GLog.d(TAG, LogFlag.DL) { "[buildCursor] resultCursor=${resultCursor.count} costTime=${GLog.getTime(startTime)}" }
            return resultCursor
        }
    }

    private fun getColumnValue(cursor: Cursor, columnIndex: Int): Any? {
        val columnValue: Any? = when (cursor.getType(columnIndex)) {
            FIELD_TYPE_NULL -> null
            FIELD_TYPE_BLOB -> cursor.getBlobOrNull(columnIndex)
            FIELD_TYPE_FLOAT -> cursor.getFloatOrNull(columnIndex)
            FIELD_TYPE_INTEGER -> cursor.getIntOrNull(columnIndex)
            FIELD_TYPE_STRING -> cursor.getStringOrNull(columnIndex)
            else -> {}
        }
        return columnValue
    }

    private fun inflateMatrixCursorFromCursor(cursor: Cursor, matrixCursor: MatrixCursor) {
        val columnValues = arrayListOf<Any?>()
        AndesMultiQueryResult.COLUMN_NAME.forEach {
            val columnIndex = cursor.getColumnIndexOrThrow(it)
            val columnValue: Any? = getColumnValue(cursor, columnIndex)
            columnValues.add(columnValue)
        }
        matrixCursor.addRow(columnValues)
    }

    /**
     * 同步KeywordCache数据到搜索Kit
     */
    private fun syncKeywordDataToKit() {
        val galleryKeywordCache = AndesKeywordCache()
        keywordCache.yearList?.let { galleryKeywordCache.yearList = it }
        keywordCache.monthList?.let { galleryKeywordCache.monthList = it }
        keywordCache.addressKeywordSet?.let { galleryKeywordCache.addressKeywordSet = it }
        keywordCache.addressSet?.let { galleryKeywordCache.addressSet = it }

        val albumEntryList = mutableListOf<AndesAlbumEntry>()
        keywordCache.albumEntrySet?.let {
            it.forEach { albumEntry ->
                val newAlbum = convertAlbumEntry(albumEntry)
                albumEntryList.add(newAlbum)
            }
        }
        galleryKeywordCache.albumEntrySet = albumEntryList

        keywordCache.mediaIdToLabelIdsMap?.let { galleryKeywordCache.mediaIdToLabelIdsMap = it }
        keywordCache.labelIdToNameMap?.let { galleryKeywordCache.labelIdToNameMap = it }

        keywordCache.personKeywordMap?.let { galleryKeywordCache.personKeywordMap = it }

        val memoriesAlbumEntryList: MutableList<AndesMemoriesAlbumEntry> = ArrayList()
        keywordCache.memoriesAlbumSet?.let {
            for (albumEntry in it) {
                val newAlbum = AndesMemoriesAlbumEntry()
                newAlbum.count = albumEntry.count
                newAlbum.memoriesId = albumEntry.memoriesId
                newAlbum.memoriesName = albumEntry.memoriesName
                newAlbum.idList = albumEntry.idList.toString()
                memoriesAlbumEntryList.add(newAlbum)
            }
        }
        galleryKeywordCache.memoriesAlbumSet = memoriesAlbumEntryList

        val dateTimeEntryMap: HashMap<String, AndesDateTimeEntry> = HashMap()
        keywordCache.festivalMap?.let {
            for (mapKey in it.keys) {
                val dateTimeEntry = it[mapKey]
                dateTimeEntry?.let { timeEntry ->
                    val newDateTimeEntry = convertDateTimeEntry(timeEntry)
                    dateTimeEntryMap[mapKey] = newDateTimeEntry
                }
            }
        }
        galleryKeywordCache.festivalMap = dateTimeEntryMap

        galleryKeywordCache.setIsKeywordLoaded(keywordCache.isKeywordCacheLoaded)
        AndesPhotosKit.INSTANCE.notifyKeywordCacheDataChanged(galleryKeywordCache)

        if (AndesSearchRusConfig.andesSearchConfig?.isPoiSearchEnabled == true) {
            val map = mutableMapOf<String, String>()
            for (poiAddress in PoiDBHelper.getInstance().allPoiAddress) {
                val poiName = poiAddress.poiName
                val poiId = poiAddress.id
                val alias = poiAddress.alias
                alias?.forEach {
                    if (it.isNotEmpty()) {
                        map[it] = poiId.toString()
                    }
                }
                map[poiName] = poiId.toString()
            }
            AndesPhotosKit.INSTANCE.notifyConfigPoiRouteDataChanged(map)
        }
    }

    private fun convertAlbumEntry(albumEntry: AlbumEntry): AndesAlbumEntry {
        val newAlbum = AndesAlbumEntry()
        newAlbum.mType = albumEntry.type
        newAlbum.mBucketId = albumEntry.bucketId ?: 0
        newAlbum.mBucketName = albumEntry.bucketName
        newAlbum.mSupportCShot = albumEntry.supportCShot
        newAlbum.mOriginalBucketName = albumEntry.originalBucketName
        newAlbum.mBucketIdList = ArrayList()
        newAlbum.mBucketIdList.addAll(albumEntry.bucketIdList)
        newAlbum.mMediaIdEntries = ArrayList()
        for (mediaIdEntry in albumEntry.mediaIdEntries) {
            val newMediaIdEntry = AndesMediaIdEntry()
            newMediaIdEntry.galleryId = mediaIdEntry.galleryId
            newMediaIdEntry.mediaId = mediaIdEntry.mediaId
            newMediaIdEntry.mediaType = mediaIdEntry.mediaType
            newMediaIdEntry.dateTaken = mediaIdEntry.dateTaken
            newMediaIdEntry.year = mediaIdEntry.year
            newMediaIdEntry.month = mediaIdEntry.month
            newAlbum.mMediaIdEntries.add(newMediaIdEntry)
        }
        newAlbum.mParentList = ArrayList()
        newAlbum.mParentList.addAll(albumEntry.parentList)
        newAlbum.mBucketName = albumEntry.bucketName
        return newAlbum
    }

    private fun convertDateTimeEntry(dateTimeEntry: DateTimeEntry): AndesDateTimeEntry {
        val newDateTimeEntry = AndesDateTimeEntry()
        newDateTimeEntry.mDateTaken = dateTimeEntry.dateTaken
        newDateTimeEntry.mLevel = dateTimeEntry.level
        newDateTimeEntry.mDate = dateTimeEntry.date
        newDateTimeEntry.mMediaIdList = ArrayList()
        for (mediaIdEntry in dateTimeEntry.mediaIdList) {
            val newMediaIdEntry = AndesMediaIdEntry()
            newMediaIdEntry.galleryId = mediaIdEntry.galleryId
            newMediaIdEntry.mediaId = mediaIdEntry.mediaId
            newMediaIdEntry.mediaType = mediaIdEntry.mediaType
            newMediaIdEntry.dateTaken = mediaIdEntry.dateTaken
            newMediaIdEntry.year = mediaIdEntry.year
            newMediaIdEntry.month = mediaIdEntry.month
            newDateTimeEntry.mMediaIdList.add(newMediaIdEntry)
        }
        return newDateTimeEntry
    }

    private fun setConfigProvider(andesSearchConfig: AndesSearchRusConfig.RemoteJsonContent?) {
        AndesPhotosKit.INSTANCE.setConfigProvider(object : IConfigProvider {
            /**
             * 多模态搜索使用的阈值，分数低于该阈值的结果会被过滤掉
             */
            override fun getMMRecallThreshold(): Float {
                if (DEBUG_SEARCH && GProperty.DEBUG_MULTI_MODAL_THRESHOLD.isNotEmpty()) {
                    GLog.d(TAG, LogFlag.DL) { "getMMRecallThreshold. debug search: ${GProperty.DEBUG_MULTI_MODAL_THRESHOLD}" }
                    return GProperty.DEBUG_MULTI_MODAL_THRESHOLD.toFloat()
                }
                val threshold = SearchAbilityImpl.multiModalAbility?.getThreshold()
                GLog.d(TAG, LogFlag.DL) { "getMMRecallThreshold, threshold = $threshold" }
                return threshold ?: super.getMMRecallThreshold()
            }

            /**
             * Caption embedding 搜索结果阈值，分数低于该阈值的结果会被过滤掉。
             */
            override fun getCaptionEmbeddingThreshold(): Float {
                return SearchAbilityImpl.captionAbility?.embeddingAbilityImpl?.getThreshold() ?: super.getCaptionEmbeddingThreshold()
            }

            /**
             * ocr embedding 搜索结果阈值，分数低于该阈值的结果会被过滤掉。
             */
            override fun getOcrEmbeddingThreshold(): Float {
                return SearchAbilityImpl.ocrEmbeddingAbility?.getThreshold() ?: super.getOcrEmbeddingThreshold()
            }

            override fun isAndesSearchEnabled(): Boolean {
                return if (DEBUG_SEARCH && (SearchConst.SEARCH_MODE == SearchConst.SEARCH_MODE_FORCE_ANDES_SEARCH)) {
                    true
                } else {
                    SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_ANDES_SEARCH, false) ?: false
                }
            }

            override fun isMultiModalSearchEnabled(): Boolean {
                var featureIsSupport = false
                var abilityIsSupport = false
                val result = if (DEBUG_SEARCH
                    && (SearchConst.SEARCH_MODE == SearchConst.SEARCH_MODE_FORCE_ANDES_SEARCH)
                    && SearchConst.MULTI_MODAL_SWITCH
                ) {
                    true
                } else {
                    featureIsSupport = SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_MULTIMODAL, false) ?: false
                    abilityIsSupport = SearchAbilityImpl.configAbility?.getBooleanConfig(ABILITY_IS_SUPPORT_MULTIMODAL, false) ?: false
                    (featureIsSupport && abilityIsSupport)
                }
                GLog.d(TAG, LogFlag.DL) { "isMultiModalSearchEnabled, result = $result, " +
                        "featureIsSupport = $featureIsSupport, abilityIsSupport = $abilityIsSupport" }
                return result
            }

            override fun isIndexSearchEnabled(): Boolean {
                return (DEBUG_SEARCH
                        && (SearchConst.SEARCH_MODE == SearchConst.SEARCH_MODE_FORCE_ANDES_SEARCH)
                        && SearchConst.INDEX_SEARCH)
            }

            override fun isSqliteSearchEnabled(): Boolean {
                return true
            }

            override fun isOcrSemanticSearchEnabled(): Boolean {
                var featureIsSupport = false
                var abilityIsSupport = false
                val result = if (DEBUG_SEARCH
                    && (SearchConst.SEARCH_MODE == SearchConst.SEARCH_MODE_FORCE_ANDES_SEARCH)
                    && SearchConst.OCR_EMBEDDING_SWITCH
                ) {
                    true
                } else {
                    featureIsSupport = SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_OCREMBEDDING, false) ?: false
                    abilityIsSupport = SearchAbilityImpl.configAbility?.getBooleanConfig(ABILITY_IS_SUPPORT_OCR_EMBEDDING, false) ?: false
                    (featureIsSupport && abilityIsSupport)
                }
                GLog.d(TAG, LogFlag.DL) { "isOcrSemanticSearchEnabled, result = $result, " +
                        "featureIsSupport = $featureIsSupport, abilityIsSupport = $abilityIsSupport" }
                return result
            }

            override fun isCaptionSearchEnabled(): Boolean {
                var featureIsSupport = false
                var abilityIsSupport = false
                val result = if (DEBUG_SEARCH
                    && (SearchConst.SEARCH_MODE == SearchConst.SEARCH_MODE_FORCE_ANDES_SEARCH)
                    && SearchConst.CAPTION_SWITCH
                ) {
                    true
                } else {
                    featureIsSupport = SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_CAPTION, false) ?: false
                    abilityIsSupport = SearchAbilityImpl.configAbility?.getBooleanConfig(ABILITY_IS_SUPPORT_CAPTION_EMBEDDING, false) ?: false
                    (featureIsSupport && abilityIsSupport)
                }
                GLog.d(TAG, LogFlag.DL) { "isCaptionSearchEnabled, result = $result, " +
                        "featureIsSupport = $featureIsSupport, abilityIsSupport = $abilityIsSupport" }
                return result
            }

            override fun getSearchTimeout(): Long {
                return andesSearchConfig?.searchTimeout ?: DEFAULT_SEARCH_TIMEOUT
            }

            override fun getMMModelVersion(): String {
                val version = SearchAbilityImpl.multiModalAbility?.currentModelVersion ?: super.getMMModelVersion()
                GLog.d(TAG, LogFlag.DL) { "getMMModelVersion. version: $version" }
                return version
            }

            override fun getCorrectionDictDir(): String {
                return andesSearchConfig?.correctionDictDir ?: TextUtil.EMPTY_STRING
            }

            override fun getStopWordDictDir(): String {
                return andesSearchConfig?.stopWordDictDir ?: TextUtil.EMPTY_STRING
            }

            override fun getSensitiveDictDir(): String {
                return andesSearchConfig?.sensitiveDictDir ?: TextUtil.EMPTY_STRING
            }

            override fun getCustomLabelDictDir(): String {
                return TextUtil.EMPTY_STRING
            }

            override fun getMMLibVersion(): String {
                return TextUtil.EMPTY_STRING
            }

            override fun getCustomEntityDict(): String {
                return andesSearchConfig?.customEntityDict ?: TextUtil.EMPTY_STRING
            }

            override fun getIncompatibleLabelDictFile(): String {
                return andesSearchConfig?.incompatibleLabelDict ?: TextUtil.EMPTY_STRING
            }

            /**
             * 配置多模态是否支持单关键词搜索
             * mark by chenzengxin
             * 默认返回 true，如果多模态badcase率不达标，可能再修改
             */
            override fun isSingleKeywordMMSearchEnabled(): Boolean {
                return true
            }

            /**
             * 配置多模态返回结果的策略
             * ALL_RESULT 表明 多关键字搜索结果 和 多模态搜索结果 同时保留
             */
            override fun getCompositeStrategyType(): Int {
                return CompositeStrategyType.ALL_RESULT
            }

            /**
             * 配置搜索场景词典，用于kit搜索排序等
             */
            override fun getQuerySceneTypeDir(): String {
                return andesSearchConfig?.sceneWordDict ?: TextUtil.EMPTY_STRING
            }
        })
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}SearchImpl"

        private const val DEFAULT_SEARCH_TIMEOUT = 2000L
    }
}

