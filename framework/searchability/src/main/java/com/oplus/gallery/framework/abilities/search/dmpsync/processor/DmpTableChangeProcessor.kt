/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  DmpTableChangeProcessor.kt
 * * Description:  处理表的数据变化
 * * Version: 1.0
 * * Date : 2025/01/11
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/01/11     1.0        create
 * *  pusong        2025/07/14     1.1        move
 ************************************************************/
package com.oplus.gallery.framework.abilities.search.dmpsync.processor

import android.content.ContentValues
import android.os.Bundle
import com.oplus.gallery.framework.abilities.search.dmpsync.DmpSearchSyncHelper.KEY_MEDIA_ID
import com.oplus.gallery.framework.abilities.search.dmpsync.DmpSearchSyncHelper.KEY_PATH
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import kotlin.reflect.KClass

/**
 * observer接收到数据表变化后进行数据处理
 * 每一个表对应一个数据处理类
 */
abstract class DmpTableChangeProcessor {
    protected val tableChangeListeners = mutableListOf<IDmpTableChangeListener>()

    /**
     * 获取tag
     */
    abstract fun getTag(): String

    /**
     * 插入数据处理
     * @param contentValuePairList 变化的数据，first为变化后的，second为变化前的，insert中second理应为null
     */
    abstract fun processInsert(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>)

    /**
     * 删除数据处理
     * @param contentValuePairList 变化的数据，first为变化后的，second为变化后的，first不一定为null，有可能是先置标记位
     */
    abstract fun processDelete(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>)

    /**
     * 更新数据处理
     * @param contentValuePairList 变化的数据，first为变化后的，second为变化后的
     */
    abstract fun processUpdate(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>)

    /**
     * 设置监听，回调处理变化后的结果
     */
    fun addTableChangeListener(listener: IDmpTableChangeListener) {
        tableChangeListeners.add(listener)
    }

    /**
     * 移除监听
     */
    fun removeListener(listener: IDmpTableChangeListener) {
        tableChangeListeners.remove(listener)
    }

    /**
     * 回调数据表变化信息给DmpSearchSyncHelper
     * @param dataSet media_id数据的集合
     * @param type 变化类型--增删改
     */
    protected inline fun <reified T : Any> onTableChange(dataSet: HashSet<T>, type: Int) {
        if (dataSet.isEmpty() || tableChangeListeners.isEmpty()) {
            return
        }
        GLog.d(getTag(), LogFlag.DL) { "onTableChange size:${dataSet.size}, type:$type" }
        val bundle = Bundle()
        when (typeOf<T>()) {
            typeOf<String>() -> bundle.putStringArray(KEY_PATH, dataSet.map { it.toString() }.toTypedArray())
            typeOf<Long>() -> bundle.putLongArray(KEY_MEDIA_ID, dataSet.map { it as Long }.toLongArray())
        }
        tableChangeListeners.forEach {
            it.onChanged(bundle, type)
        }
    }

    protected inline fun <reified T : Any> typeOf(): KClass<T> = T::class
}


/**
 * ITableChangeProcessor将数据处理后通过该回调将变化的数据回调到DmpSearchSyncHelper
 */
interface IDmpTableChangeListener {

    /**
     * 回调表的数据变化情况到业务
     */
    fun onChanged(bundle: Bundle, type: Int)
}