/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelQueryEntry.kt
 ** Description: 视觉知识图谱查询类
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/2/9
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery          2023/2/9     1.0         init
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph.bean

import com.oplus.gallery.framework.abilities.search.cvgraph.interceptor.LabelGraphInterceptor

/**
 * 标签查询条目，里面包含了用户输入的词，以及分别根据图谱查询后得到的标签集合
 * @property word 用户输入的词，或者被视觉知识图谱纠错后的词
 */
class LabelQueryEntry(var word: String) {

    /**
     * 根据word分词转换后的标签及（同义词）或纠错词
     */
    val labelBeans = mutableListOf<LabelBean>()

    /**
     * 根据word找到的补全词，也可能是纠错词的补全词
     */
    val completionLabelBeans = mutableListOf<LabelBean>()

    /**
     * 根据word找到的可能想搜词, 近义词, 上位词
     */
    val otherLabelBeans = mutableListOf<LabelBean>()

    /**
     * 添加对应标签到对应的容器中，拦截器在此工作
     */
    fun addQueryLabel(queryType: QueryType, beans: MutableList<LabelBean>) {
        if (beans.isEmpty()) {
            return
        }
        val interceptorResult = LabelGraphInterceptor().interceptor(queryType, beans)
        if (interceptorResult.isNotEmpty()) {
            when (queryType) {
                QueryType.LABEL_WORD -> labelBeans.addAll(interceptorResult)
                QueryType.COMPLETION_WORD -> completionLabelBeans.addAll(interceptorResult)
                QueryType.OTHER_WORD -> otherLabelBeans.addAll(interceptorResult)
            }
        }
    }

    enum class QueryType {
        /**
         * 标签、标签同义词、纠错词
         */
        LABEL_WORD,

        /**
         * 补全词
         */
        COMPLETION_WORD,

        /**
         * 其它，包括可能想搜、近义词、上位词
         */
        OTHER_WORD
    }
}