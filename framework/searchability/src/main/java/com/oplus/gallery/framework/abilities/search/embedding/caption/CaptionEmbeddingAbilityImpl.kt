/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : CaptionEmbeddingAbilityImpl.kt
 ** Description : Caption 语义扫描搜索实现类
 ** Version     : 1.0
 ** Date        : 2025/6/4
 ** Author      : W9058271
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9058271                            2025/6/4     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.search.embedding.caption

import android.content.Context
import com.ai.slp.engine.EmbeddingSceneType
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.caption.ICaptionAbility.Companion.TYPE_CAPTION_EMBEDDING_ABILITY
import com.oplus.gallery.framework.abilities.search.embedding.BaseEmbeddingAbilityImpl

/**
 * Caption 语义扫描搜索实现类
 */
class CaptionEmbeddingAbilityImpl(context: Context) : BaseEmbeddingAbilityImpl(context), ICaptionEmbeddingAbility {

    override val domainInstance: AutoCloseable = this

    override var type: String = TYPE_CAPTION_EMBEDDING_ABILITY

    override fun getEmbeddingSceneType(): Int {
        return EmbeddingSceneType.TYPE_CAPTION
    }

    /**
     * 获取日志 TAG
     */
    override fun getTag(): String {
        return TAG
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}CaptionEmbeddingAbilityImpl"
    }
}