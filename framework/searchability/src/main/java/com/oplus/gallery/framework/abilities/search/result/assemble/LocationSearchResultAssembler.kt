/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocationSearchResultAssembler.kt
 ** Description:地点搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import android.database.Cursor
import android.provider.BaseColumns
import android.util.Pair
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.business_lib.model.data.location.utils.GeoDBHelper
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCache
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.completion.LocationCompletion
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.entry.ShootLocation
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.LinkedList
import java.util.regex.Pattern

/**
 * 地点搜索结果拼装器
 */
internal class LocationSearchResultAssembler(
    private val keywordCacheManager: KeywordCacheManager,
    private val geoCacheService: GeoCacheService
) : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_LOCATION

    @Suppress("LongMethod")
    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val searchClassifier = searchEntry.searchClassifier
        val searchType = searchEntry.searchType
        GLog.d(TAG, "[assemble] singleKeyword:$singleKeyword, searchType:$searchType")
        val startTime = System.currentTimeMillis()
        // 1.缓存还未创建完, 则查询并更新缓存
        var keywordCache = keywordCacheManager.keywordCache
        if (keywordCache.addressSet.isNullOrEmpty()) {
            GLog.d(TAG, "[assemble] address cache is null, updateLocationCache")
            keywordCacheManager.updateLocationCache()
            keywordCache = keywordCacheManager.keywordCache
        }

        val pattern = SearchCommonUtils.REGION_SUFFIX_PATTERN
        val locationCompletion = LocationCompletion(ContextGetter.context, keywordCacheManager)
        var cursor: Cursor? = null
        // 特定搜索，只搜索地点; 特定查询
        if (this.resultType == searchType) {
            var addressList: List<Pair<Int, String>>? = null
            // 1.地址全匹配结果
            val fullMatchLocationList = fullMatch(locationCompletion, keywordCache, singleKeyword, pattern)
            fullMatchLocationList.forEach { shootLocation ->
                if (shootLocation.shootKeyword.isNullOrEmpty()) {
                    return@forEach
                }
                shootLocation.shootKeyword?.let {
                    val simpleAddress = pattern.matcher(it).replaceAll(TextUtil.EMPTY_STRING).trim()
                    val simpleSingleKeyword = pattern.matcher(singleKeyword).replaceAll(TextUtil.EMPTY_STRING).trim()
                    if (simpleSingleKeyword.equals(simpleAddress.lowercase(), true)) {
                        addressList = shootLocation.shootKeywordList
                        return@forEach
                    }
                }
            }
            if (addressList.isNullOrEmpty().not()) {
                cursor = geoCacheService.queryGpsCursorFromMediaDB(ContextGetter.context)
                val gpsEntryLinkedList: LinkedList<MediaIdEntry> = getAddressList(cursor)
                val addressIds: MutableList<MediaIdEntry> =
                    geoCacheService.queryMediaIdByAddress(ContextGetter.context, addressList, gpsEntryLinkedList)
                if (addressIds.isNotEmpty()) {
                    singleQueryResult.appendQueryResult(addressIds, singleKeyword, SearchType.TYPE_LOCATION)
                }
            }
        } else {
            val completionLocations: MutableList<ShootLocation> = locationCompletion.complete(searchClassifier, singleKeyword)
            if (GProperty.DEBUG) {
                GLog.d(TAG, "[assemble] singleKeyword:$singleKeyword, completion over:$completionLocations")
            }
            cursor = geoCacheService.queryGpsCursorFromMediaDB(ContextGetter.context)
            val gpsEntryLinkedList = getAddressList(cursor)
            completionLocations.forEach { completionLocation ->
                val addressList: List<Pair<Int, String>>? = completionLocation.shootKeywordList
                if (addressList.isNullOrEmpty().not()) {
                    val addressIds: MutableList<MediaIdEntry> =
                        geoCacheService.queryMediaIdByAddress(ContextGetter.context, addressList, gpsEntryLinkedList)
                    if (addressIds.isNotEmpty()) {
                        completionLocation.shootKeyword?.let {
                            singleQueryResult.appendQueryResult(
                                addressIds,
                                it, SearchType.TYPE_LOCATION
                            )
                        }
                    } else {
                        GLog.d(TAG, "failed to search addressList")
                    }
                }
            }
        }
        GLog.d(TAG, "[assemble] costTime:${GLog.getTime(startTime)}")
        return singleQueryResult
    }

    @Suppress("LoopWithTooManyJumpStatements")
    private fun fullMatch(
        locationCompletion: LocationCompletion,
        keywordCache: KeywordCache,
        singleKeyword: String,
        pattern: Pattern
    ): List<ShootLocation> {
        val shootLocationList = mutableListOf<ShootLocation>()
        // 根据内外销情况确定地址level
        val startIndex = locationCompletion.getAddressLevel()
        // 每个地址信息包含几段，如国家、省、城市等，存放在一个数组中的
        keywordCache.addressSet?.forEach { address ->
            var matchedKeyword: String? = null
            val matchedKeywordList: MutableList<Pair<Int, String>> = mutableListOf()
            // 对地址的每个分段地址进行遍历匹配
            for (index in startIndex until address.size) {
                if (index == GeoDBHelper.LEVEL_PROVINCE) {
                    continue
                }
                val addr = address[index]
                if (addr == null || addr.trim().isEmpty()) {
                    continue
                }
                val simpleAddress = pattern.matcher(addr).replaceAll(TextUtil.EMPTY_STRING).trim()
                // 搜索词也确实是地址信息
                val simpleRecommendKeyword = pattern.matcher(singleKeyword).replaceAll(TextUtil.EMPTY_STRING).trim()
                // 地址与搜索词完全匹配
                if (simpleAddress.equals(simpleRecommendKeyword, true)) {
                    matchedKeyword = addr
                    matchedKeywordList.add(Pair(index, addr))
                    break
                }
            }

            // 包装成ShootLocation对象
            matchedKeyword?.let {
                val matchedLocation = ShootLocation()
                matchedLocation.shootKeyword = it.trim()
                matchedLocation.shootKeywordList = matchedKeywordList
                if (shootLocationList.contains(matchedLocation).not()) {
                    shootLocationList.add(matchedLocation)
                }
            }
        }
        return shootLocationList
    }

    private fun getAddressList(gpsCursor: Cursor?): LinkedList<MediaIdEntry> {
        val gpsEntryLinkedList = LinkedList<MediaIdEntry>()
        if (gpsCursor != null && gpsCursor.moveToFirst()) {
            kotlin.runCatching {
                gpsCursor.use {
                    val indexID = gpsCursor.getColumnIndex(BaseColumns._ID)
                    val indexMediaID = gpsCursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID)
                    val indexType = gpsCursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE)
                    val indexDateTaken = gpsCursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN)
                    val indexLatitude = gpsCursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.LATITUDE)
                    val indexLongitude = gpsCursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.LONGITUDE)
                    val indexGpsKey = gpsCursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.GPS_KEY)
                    do {
                        val entry = MediaIdEntry()
                        entry.galleryId = gpsCursor.getInt(indexID)
                        entry.mediaId = gpsCursor.getInt(indexMediaID)
                        entry.dateTaken = gpsCursor.getLong(indexDateTaken)
                        entry.mediaType = gpsCursor.getInt(indexType)
                        entry.latitude = gpsCursor.getDouble(indexLatitude)
                        entry.longitude = gpsCursor.getDouble(indexLongitude)
                        entry.gpsKey = gpsCursor.getLong(indexGpsKey)
                        gpsEntryLinkedList.add(entry)
                    } while (gpsCursor.moveToNext())
                }
            }.onFailure {
                GLog.e(TAG, "[getAddressList] Query media ID with gps failed!", it)
            }
        }
        return gpsEntryLinkedList
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}LocationSearchResultAssembler"
    }
}