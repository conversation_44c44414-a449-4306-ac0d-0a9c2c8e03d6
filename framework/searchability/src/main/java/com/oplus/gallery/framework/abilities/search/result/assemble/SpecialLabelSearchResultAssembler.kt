/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SpecialLabelSearchResultAssembler.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.entry.KeywordString
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.Locale

/**
 * 特殊标签查询结果拼装器
 */
internal class SpecialLabelSearchResultAssembler : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_SPECIAL_LABEL

    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val searchClassifier = searchEntry.searchClassifier
        GLog.d(TAG, "[build] singleKeyword:$singleKeyword")
        val startTime = System.currentTimeMillis()
        var title: String? = null
        var isMatched = false
        if (searchClassifier.contains(SPECIAL_KEYWORD_GIF, singleKeyword)) {
            isMatched = true
            title = SPECIAL_KEYWORD_GIF
        } else if (searchClassifier.contains(KeywordString.gifKeyWord.lowercase(Locale.getDefault()), singleKeyword)) {
            isMatched = true
            title = KeywordString.gifKeyWord
        }
        if (isMatched) {
            val dateIdsList = DBSearchUtils.queryMediaIdEntryBySpecialKeywordGif(ContextGetter.context)
            if (dateIdsList.isNullOrEmpty().not()) {
                singleQueryResult.appendQueryResult(dateIdsList, title, resultType)
            }
        }
        GLog.d(TAG, "[build] costTime:${GLog.getTime(startTime)}")

        return singleQueryResult
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}SpecialLabelSearchResultAssembler"

        private const val SPECIAL_KEYWORD_GIF = "gif"
    }
}