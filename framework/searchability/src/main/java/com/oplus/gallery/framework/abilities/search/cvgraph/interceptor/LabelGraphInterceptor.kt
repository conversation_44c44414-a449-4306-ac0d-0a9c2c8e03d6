/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelGraphInterceptor.kt
 ** Description: 标签查询拦截器实现类，以处理对应的类型标签的拦截过程
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/4/13
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery          2023/4/13     1.0         init
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph.interceptor

import com.oplus.gallery.business_lib.model.data.label.LabelSelector
import com.oplus.gallery.business_lib.model.data.label.LabelSelector.CountryLabel
import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelBean
import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelQueryEntry
import com.oplus.gallery.foundation.util.text.TextUtil

/**
 * 标签查询拦截器实现类，以处理对应的类型标签的拦截过程
 */
class LabelGraphInterceptor : IGraphInterceptor {
    private var currentLabel: CountryLabel? = null

    init {
        currentLabel = LabelSelector.loadCurrentCountryLabel()
    }

    override fun interceptor(queryType: LabelQueryEntry.QueryType, labels: MutableList<LabelBean>): List<LabelBean> {
        // 兼容default_white_label_lists_config.xml配置文件的搜索词的拦截内容
        val resultTags = mutableListOf<LabelBean>()
        resultTags.addAll(labels)
        currentLabel?.rejectLabels?.forEach { rejectLabel ->
            labels.forEach { label ->
                if (label.labelId == rejectLabel.labelId) {
                    resultTags.remove(label)
                }
            }
        }
        // 拦截unknown标签的情况
        resultTags.remove(LabelBean(UNKNOWN_LABEL, TextUtil.EMPTY_STRING))
        return resultTags
    }

    companion object {
        /**
         * 不存在的标签id，在图谱的tag表中存在，需要拦截器过滤
         */
        private const val UNKNOWN_LABEL = 0
    }
}