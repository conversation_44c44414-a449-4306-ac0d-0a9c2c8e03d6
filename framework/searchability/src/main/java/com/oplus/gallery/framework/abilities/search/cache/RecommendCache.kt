/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendCache.kt
 ** Description: 搜索推荐缓存
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cache

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.standard_lib.util.io.IOUtils

/**
 * 搜索推荐缓存实体
 */
internal class RecommendCache {
    private var yearRecommend: Cursor? = null
    private var monthRecommend: Cursor? = null
    private var festivalRecommend: Cursor? = null
    private var locationRecommend: Cursor? = null
    private var personRecommend: Cursor? = null
    private var labelRecommend: Cursor? = null
    private var recentlyAddedRecommend: Cursor? = null
    private var memoriesRecommend: Cursor? = null

    /**
     * 年缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasYearRecommend(): Boolean {
        return yearRecommend != null
    }

    /**
     * 月缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasMonthRecommend(): Boolean {
        return monthRecommend != null
    }

    /**
     * 地点缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasLocationRecommend(): Boolean {
        return locationRecommend != null
    }

    /**
     * 节日缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasFestivalRecommend(): Boolean {
        return festivalRecommend != null
    }

    /**
     * 人物缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasPersonRecommend(): Boolean {
        return personRecommend != null
    }

    /**
     * 标签缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasLabelRecommend(): Boolean {
        return labelRecommend != null
    }

    /**
     * 最近添加缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasRecentlyAddedRecommend(): Boolean {
        return recentlyAddedRecommend != null
    }

    /**
     * 回忆缓存是否存在
     *
     * @return true：存在，false：不存在
     */
    fun hasMemoriesRecommend(): Boolean {
        return memoriesRecommend != null
    }

    /**
     * 获取标签缓存
     *
     * @return cursor
     */
    fun obtainLabelRecommend(): Cursor? {
        return obtainRecommend(labelRecommend)
    }

    /**
     * 获取年缓存
     *
     * @return cursor
     */
    fun obtainYearRecommend(): Cursor? {
        return obtainRecommend(yearRecommend)
    }

    /**
     * 获取节日缓存
     *
     * @return cursor
     */
    fun obtainFestivalRecommend(): Cursor? {
        return obtainRecommend(festivalRecommend)
    }

    /**
     * 获取月缓存
     *
     * @return cursor
     */
    fun obtainMonthRecommend(): Cursor? {
        return obtainRecommend(monthRecommend)
    }

    /**
     * 获取地点缓存
     *
     * @return cursor
     */
    fun obtainLocationRecommend(): Cursor? {
        return obtainRecommend(locationRecommend)
    }

    /**
     * 获取人物缓存
     *
     * @return cursor
     */
    fun obtainPersonRecommend(): Cursor? {
        return obtainRecommend(personRecommend)
    }

    /**
     * 获取最近添加缓存
     *
     * @return cursor
     */
    fun obtainRecentlyAddedRecommend(): Cursor? {
        return obtainRecommend(recentlyAddedRecommend)
    }

    /**
     * 获取回忆缓存
     *
     * @return cursor
     */
    fun obtainMemoriesRecommend(): Cursor? {
        return obtainRecommend(memoriesRecommend)
    }

    /**
     * 更新年缓存
     */
    fun updateToYearRecommend(cursor: Cursor?) {
        yearRecommend = updateToRecommend(cursor, yearRecommend)
    }

    /**
     * 更新月缓存
     */
    fun updateToMonthRecommend(cursor: Cursor?) {
        monthRecommend = updateToRecommend(cursor, monthRecommend)
    }

    /**
     * 更新节日缓存
     */
    fun updateToFestivalRecommend(cursor: Cursor?) {
        festivalRecommend = updateToRecommend(cursor, festivalRecommend)
    }

    /**
     * 更新标签缓存
     */
    fun updateToLabelRecommend(cursor: Cursor?) {
        labelRecommend = updateToRecommend(cursor, labelRecommend)
    }

    /**
     * 更新地点缓存
     */
    fun updateToLocationRecommend(cursor: Cursor?) {
        locationRecommend = updateToRecommend(cursor, locationRecommend)
    }

    /**
     * 更新人物缓存
     */
    fun updateToPersonRecommend(cursor: Cursor?) {
        personRecommend = updateToRecommend(cursor, personRecommend)
    }

    /**
     * 更新最近添加缓存
     */
    fun updateToRecentlyAddedRecommend(cursor: Cursor?) {
        recentlyAddedRecommend = updateToRecommend(cursor, recentlyAddedRecommend)
    }

    /**
     * 更新回忆缓存
     *
     * @return cursor
     */
    fun updateToMemoriesRecommend(cursor: Cursor?) {
        memoriesRecommend = updateToRecommend(cursor, memoriesRecommend)
    }

    private fun obtainRecommend(cursor: Cursor?): Cursor? {
        if (cursor == null || cursor.count == 0) {
            return null
        }
        synchronized(cursor) {
            val columnNames = cursor.columnNames
            val rowCount = cursor.count
            val columnCount = cursor.columnCount
            val cloneCursor = MatrixCursor(columnNames, rowCount)
            cursor.moveToFirst()
            do {
                val builder = cloneCursor.newRow()
                for (col in 0 until columnCount) {
                    builder.add(cursor.getString(col))
                }
            } while (cursor.moveToNext())
            return cloneCursor
        }
    }

    private fun updateToRecommend(inCursor: Cursor?, out: Cursor?): Cursor? {
        var outCursor = out
        val cloneCursor = obtainRecommend(inCursor)
        if (outCursor != null) {
            synchronized(outCursor) {
                outCursor?.close()
                outCursor = cloneCursor
            }
        } else {
            outCursor = cloneCursor
        }
        return outCursor
    }

    fun release() {
        IOUtils.closeQuietly(
            yearRecommend, monthRecommend, festivalRecommend, locationRecommend,
            personRecommend, labelRecommend, memoriesRecommend, recentlyAddedRecommend
        )
    }
}