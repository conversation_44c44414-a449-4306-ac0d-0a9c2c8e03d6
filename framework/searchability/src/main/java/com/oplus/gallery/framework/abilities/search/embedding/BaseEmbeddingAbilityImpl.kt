/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BaseEmbeddingAbilityImpl.kt
 ** Description : 文本 Embedding 扫描搜索实现基类
 ** Version     : 1.0
 ** Date        : 2025/6/4
 ** Author      : W9058271
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9058271                            2025/6/4     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.search.embedding

import android.content.Context
import com.ai.slp.engine.BaseEngine
import com.ai.slp.engine.SearchResult
import com.ai.slp.engine.TextEmbeddingEngine
import com.ai.slp.listener.IErrorListener
import com.ai.slp.listener.IStateChangeListener
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.business_lib.BusinessLibHelper.PREFERENCE_USE_OPEN_NETWORK
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Download.OCR_EMBEDDING_CURRENT_VERSION_PATH
import com.oplus.gallery.framework.abilities.search.caption.ICaptionAbility.Companion.MAX_GALLERY_ID
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility.Companion.MIN_OCR_TEXT_LENGTH
import com.oplus.gallery.framework.abilities.search.ocrembedding.OcrEmbeddingImageInfo
import com.oplus.gallery.framework.abilities.search.ocrembedding.OcrEmbeddingScanProviderHelper
import com.oplus.gallery.framework.abilities.search.utils.AIUnitUtils

/**
 * 文本 Embedding 扫描搜索实现基类
 */
abstract class BaseEmbeddingAbilityImpl(private val context: Context) : AbsAppAbility(), IEmbeddingAbility {

    private var configAbility: IConfigSetterAbility? = null

    private var clipEngine: TextEmbeddingEngine? = null

    private val currentThreadInfo = Thread.currentThread().id.toString() + TextUtil.STRIKE + Thread.currentThread().name

    override var currentModelVersion: String
        get() = configAbility?.getStringConfig(OCR_EMBEDDING_CURRENT_VERSION_PATH) ?: TextUtil.EMPTY_STRING
        set(value) {
            configAbility?.setStringConfig(OCR_EMBEDDING_CURRENT_VERSION_PATH, value)
        }

    /**
     * 是否在扫描中
     */
    override var isScanning: Boolean = false

    /**
     * 是否在下载模型中
     */
    override var isDownloading: Boolean = false

    /**
     * 初始化引擎
     *
     * @return 是否初始化成功。true成功，false失败
     */
    @Synchronized
    override fun init(): Boolean {
        GLog.d(getTag(), LogFlag.DL) { "initSlpSdk, thread = $currentThreadInfo, clipEngine = $clipEngine, this: $this" }
        if (MultiProcessSpUtil.getBoolean(PREFERENCE_USE_OPEN_NETWORK, false).not()) {
            GLog.w(getTag(), LogFlag.DL) { "initSlpSdk, no network" }
            return false
        }
        val startTime = System.currentTimeMillis()

        // 能力是否可用
        if (!AIUnitUtils.isDetectAvailable(context, TextEmbeddingEngine.DETECT_NAME)) {
            GLog.w(getTag(), LogFlag.DL, "initSlpSdk, TextEmbeddingEngine isDetectAvailable: false")
            return false
        }
        // 如果已经初始化，则无需要重新初始化
        if ((clipEngine?.getState() == BaseEngine.STATE_STARTED)) {
            GLog.w(getTag(), LogFlag.DL,  "initSlpSdk, engine already started!")
            return true
        }

        // 模型变更了，则需要释放之前的引擎，然后重新创建新的引擎实例并启动
        reInitEngine()
        GLog.d(getTag(), LogFlag.DL) { "initSlpSdk, thread = $currentThreadInfo, " +
                "clipEngine = $clipEngine, costTime = ${GLog.getTime(startTime)}" }

        return clipEngine != null
    }

    /**
     * 若状态不为STATE_START，调用clipEngine.start()
     */
    private fun startEngine(): Boolean {
        if ((clipEngine == null)) {
            GLog.e(getTag(), LogFlag.DL) { "startEngine, error, clipEngine = $clipEngine" }
            return false
        }
        var result = true
        if (clipEngine?.getState() != BaseEngine.STATE_STARTED) {
            result = clipEngine?.start() ?: false
            currentModelVersion = clipEngine?.getModelVersion().orEmpty()
        }
        GLog.d(getTag(), LogFlag.DL) { "startEngine, clipEngine = $clipEngine, result = $result" }
        return result
    }

    private fun reInitEngine() {
        clipEngine?.release()
        createEngine()
        startEngine()
    }

    private fun createEngine() {
        // 创建引擎实例
        clipEngine = TextEmbeddingEngine(context, type, getEmbeddingSceneType())
        clipEngine?.errorListener = object : IErrorListener {
            override fun onError(code: Int, msg: String?) {
                GLog.e(getTag(), LogFlag.DL, "createEngine. code: $code, msg: $msg")
            }
        }
        // 配置数据缓存路径
        clipEngine?.setParamsValue(TextEmbeddingEngine.EMBEDDING_DIR_CACHE, context.filesDir.absolutePath)
        GLog.d(getTag(), LogFlag.DL) { "createEngine, clipEngine:$clipEngine" }
    }

    /**
     * 获取文本 Embedding 场景类型: OCR/Caption. EmbeddingSceneType 中定有常量
     */
    abstract fun getEmbeddingSceneType(): Int

    @Synchronized
    override fun processImageText(localMediaTableId: Long, text: String): Boolean {
        val startTime = System.currentTimeMillis()
        if (startEngine().not()) {
            GLog.e(getTag(), LogFlag.DL) { "processImageText, startEngine fail, return" }
            return false
        }
        GLog.d(getTag(), LogFlag.DL) { "processImage " +
                "thread = $currentThreadInfo, clipEngine = $clipEngine" }
        var ret = false
        kotlin.runCatching {
            ret = clipEngine?.processText(localMediaTableId, text) ?: false
        }.onFailure {
            GLog.e(getTag(), LogFlag.DL, "processImage, fail", it)
        }
        if (DEBUG_SEARCH) {
            GLog.d(getTag(), LogFlag.DL) { "processImage, costTime = ${GLog.getTime(startTime)}, text = $text" }
        }
        return ret
    }

    /**
     * OCR语义搜索
     * @param keyword 搜索关键字
     * @return OcrEmbeddingImageInfo
     */
    override fun search(keyword: String): List<OcrEmbeddingImageInfo> {
        kotlin.runCatching {
            if (!init()) {
                // 初始化引擎失败，直接return
                GLog.e(getTag(), LogFlag.DL) { "searchImgByText, init multi modal engine fail, return" }
                return mutableListOf()
            }
            // 校验数据是否有效
            val dataValid = this.checkDataValid()
            if (dataValid.not()) {
                GLog.e(getTag(), LogFlag.DL) { "searchImgByText, data is Invalid, return" }
                return mutableListOf()
            }

            val imgIds = clipEngine?.searchByText(keyword)?.idArray
            val scanData = OcrEmbeddingScanProviderHelper.getScanDataByLocalMediaTableIds(imgIds)
            val result = scanData.filter {
                it.mInvalid.not() && it.mIsRecycled.not()
            }
            GLog.d(getTag(), LogFlag.DL) { "searchImgByText, scanData.size = ${scanData.size}, result.size = ${result.size}" }
            return result
        }.onFailure {
            GLog.e(getTag(), LogFlag.DL, "searchImgByText error ", it)
        }
        this.release()
        return mutableListOf()
    }

    /**
     * 内存数据输出到文件
     * processImage之后、deleteImage之后需要调用此方法
     * @return 成功true，失败false
     */
    @Synchronized
    override fun flushData(): Boolean {
        val startTime = System.currentTimeMillis()
        val ret = clipEngine?.flushData() ?: false
        if (DEBUG_SEARCH) {
            GLog.d(getTag(), LogFlag.DL,  "flushData costTime = ${GLog.getTime(startTime)}")
        }
        return ret
    }

    /**
     * 释放
     */
    @Synchronized
    override fun release() {
        val startTime = System.currentTimeMillis()
        GLog.d(getTag(), LogFlag.DL, "release, clipEngine = $clipEngine")
        clipEngine?.stop()
        clipEngine?.release()
        clipEngine = null
        if (DEBUG_SEARCH) {
            GLog.d(getTag(), LogFlag.DL) { "release costTime = ${GLog.getTime(startTime)}" }
        }
    }

    /**
     * 是否有新版本
     * 如果newVersion不为空，且记录的版本不等于newVersion。说明有新版本
     *
     * @param versionPath 模型路径
     * @return 是true，否false
     */
    @Synchronized
    override fun hasNewVersion(versionPath: String): Boolean {
        val newModelVersion = clipEngine?.getModelVersion().orEmpty()
        return (newModelVersion.isEmpty().not()) && (versionPath != newModelVersion)
    }

    /**
     * 删除图片向量
     * @param needDeleteList 需要删除的图片列表
     * @return 成功true，失败false
     */
    @Synchronized
    override fun deleteDataByLocalMediaTableIds(
        needDeleteList: MutableList<OcrEmbeddingImageInfo>
    ): MutableList<Long> {
        // 版本和List<Id> 组成map
        val map = needDeleteList.groupBy(
            OcrEmbeddingImageInfo::versionModelPath,
            OcrEmbeddingImageInfo::mGalleryId
        )

        return deleteDataFromEmbeddingTable(map)
    }

    /**
     * ocrEmbedding模型库里删除指定版本号的数据
     * @param map string:模型的版本path, List<Long> GalleryId的list
     * @return 删除成功图片的_id的list
     */
    override fun deleteDataFromEmbeddingTable(map: Map<String, List<Long>>): MutableList<Long> {
        val result = mutableListOf<Long>()
        map.forEach { (_, idList) ->
                reInitEngine()
            val deletedList = mutableListOf<Long>()
            idList.forEach {
                if (clipEngine?.deleteTextVectorDataById(it) == true) {
                    deletedList.add(it)
                }
            }
            if (clipEngine?.flushData() == true) {
                result.addAll(deletedList)
            }
            this.release()
        }
        return result
    }

    /**
     * 启动引擎
     */
    @Synchronized
    private fun launchEngine() {
        if ((clipEngine?.getState() != BaseEngine.STATE_STARTED) || (clipEngine?.getDataTag() != type)) {
            GLog.d(getTag(),  LogFlag.DL, "launchEngine, type = $type, clipEngine tag = ${clipEngine?.getDataTag()}, thread = $currentThreadInfo")
            init()
        }
    }

    /**
     * 检查是否具有老的模型的数据
     * @return  true 表示存在， false 表示不存在
     */
    override fun hasOldModelData(): Boolean {
        return clipEngine?.hasOldModelData() ?: false
    }

    /**
     * 清除老模型的数据
     * @return  true 表示执行成功，false 表示执行失败
     */
    override fun removeOldModelData(): Boolean {
        return clipEngine?.removeOldModelData() ?: false
    }

    /**
     * ---------------实现com.oplus.andes.photos.kit.search.ability.IMultiModalAbility开始--------------
     */
    /**
     * 强制停止引擎
     */
    @Synchronized
    override fun cancel() {
        clipEngine?.stop()
    }

    /**
     * 检查数据是否可以用
     */
    override fun checkDataValid(): Boolean {
        launchEngine()
        GLog.d(getTag(), LogFlag.DL) { "checkDataValid, clipEngine = $clipEngine" }
        return clipEngine?.checkDataValid() ?: false
    }

    /**
     * 是否允许做embedding，需要满足以下条件
     * 条件1：连续中文长度大于5
     * 条件2：英文、数字和标点符号长度小于总长度一半
     */
    override fun isAllowEmbedding(text: String): Boolean {
        val length = text.length
        if (length < MIN_OCR_TEXT_LENGTH) {
            GLog.d(getTag(), LogFlag.DL) { "isAllowEmbedding, text length = $length is not allowed" }
            return false
        }
        val halfLength = length / 2.0
        var cnCount = 0
        var hasContinuousCn = false
        var invalidCharCount = 0
        for (char in text) {
            // 对连续的中文字符进行计数，如果有连续的超过 MIN_OCR_TEXT_LENGTH 则不再计数
            if (!hasContinuousCn && (char.code in CN_CHAR_START..CN_CHAR_END)) {
                cnCount++
            } else {
                cnCount = 0
            }

            if (cnCount >= MIN_OCR_TEXT_LENGTH) {
                hasContinuousCn = true
            }

            // 对数字、字母和标点符号进行计数
            val isInvalid = (char in 'a'..'z') || (char in 'A'..'Z') || (char in '0'..'9') ||
                    (char.code in BASIC_LATIN_CHAR_START..BASIC_LATIN_CHAR_END) ||
                    (char.code in COM_PUNCT_CHAR_START..COM_PUNCT_CHAR_END) ||
                    (char.code in EXTENDED_PUNCT_CHAR_START..EXTENDED_PUNCT_CHAR_END)
            if (isInvalid) {
                invalidCharCount++
            }

            // 不满足做embedding条件退出遍历
            if (invalidCharCount >= halfLength) {
                GLog.d(getTag(), LogFlag.DL) { "isAllowEmbedding, invalidCharCount:$invalidCharCount over text half length:$halfLength" }
                return false
            }
        }
        GLog.d(getTag(), LogFlag.DL) { "isAllowEmbedding, hasContinuousCn:$hasContinuousCn, invalidCharCount:$invalidCharCount, " +
                "halfLength:$halfLength" }
        return hasContinuousCn && (invalidCharCount < halfLength)
    }

    /**
     * 获取主版号
     */
    override fun getMajorVersion(): Int {
        val versions = currentModelVersion.split('.').map { it.toInt() }
        return versions[0]
    }

    /**
     * 获取阈值
     */
    override fun getThreshold(): Float? {
        launchEngine()
        return clipEngine?.getRecommendThreshold().also {
            GLog.d(getTag(), LogFlag.DL) { "getThreshold. $it" }
        }
    }

    /**
     * 检查id是否存在
     */
    override fun checkIdExist(id: Long): Boolean {
        launchEngine()
        return clipEngine?.checkIdExist(id) ?: false
    }

    /**
     * 获取状态
     */
    override fun getStatus(): Int {
        launchEngine()
        return clipEngine?.getState() ?: BaseEngine.STATE_STOPPED
    }

    /**
     * 搜索
     */
    @Synchronized
    override fun searchImage(query: String?): SearchResult? {
        launchEngine()
        val result = query?.let { clipEngine?.searchByText(it, (MAX_GALLERY_ID - 1), 1) }
        GLog.d(getTag(), LogFlag.DL) { "searchImage, query = $query, " +
                "thread = $currentThreadInfo, clipEngine = $clipEngine, result = $result" }
        return result
    }

    /**
     * 设置错误监听
     */
    override fun setErrorListener(listener: IErrorListener) {
        launchEngine()
        clipEngine?.errorListener = listener
    }

    /**
     * 设置状态变化监听
     */
    override fun setStateChangeListener(listener: IStateChangeListener) {
        launchEngine()
        clipEngine?.stateChangeListener = listener
    }

    /**
     * 停止引擎
     */
    @Synchronized
    override fun stop() {
        this.release()
    }

    /**
     * ---------------实现com.oplus.andes.photos.kit.search.ability.IMultiModalAbility结束-------------
     */

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        configAbility = abilityBus.requireAbility(IConfigSetterAbility::class.java)
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        configAbility?.close()
        configAbility = null
        this.release()
    }

    companion object {

        /**
         * 常用基本汉字编码的起始位
         */
        private const val CN_CHAR_START = 0x4E00
        /**
         * 常用基本汉字编码的结束位
         */
        private const val CN_CHAR_END = 0x9FA5
        /**
         * 基本拉丁字符编码的起始位
         */
        private const val BASIC_LATIN_CHAR_START = 0x0020
        /**
         * 基本拉丁字符编码的结束位
         */
        private const val BASIC_LATIN_CHAR_END = 0x007E
        /**
         * 常用标点符号编码的起始位
         */
        private const val COM_PUNCT_CHAR_START = 0x2000
        /**
         * 常用标点符号编码的结束位
         */
        private const val COM_PUNCT_CHAR_END = 0x206F
        /**
         * 各种标点符号的扩展编码的起始位
         */
        private const val EXTENDED_PUNCT_CHAR_START = 0x2E00
        /**
         * 各种标点符号的扩展编码的结束位
         */
        private const val EXTENDED_PUNCT_CHAR_END = 0x2E7F
    }
}