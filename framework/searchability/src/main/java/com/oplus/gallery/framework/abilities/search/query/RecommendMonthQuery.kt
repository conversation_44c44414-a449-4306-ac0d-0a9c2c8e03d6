/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendMonthQuery.kt
 ** Description: 推荐月查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import android.util.Log
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 推荐月查询
 *
 * @property forceQuery 是否强制从数据库查询
 * @property recommendCache 推荐缓存
 */
internal class RecommendMonthQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache
) : Query {
    override fun query(): Cursor? {
        return if (!forceQuery && recommendCache.hasMonthRecommend()) {
            recommendCache.obtainMonthRecommend()
        } else {
            GTrace.traceBegin("RecommendMonthQuery")
            val startTime = System.currentTimeMillis()
            val cursor = MatrixCursor(RecommendFestivalQuery.COLUMN_RECOMMEND_DATE)
            val queryTime = System.currentTimeMillis()
            val coverEntry = DBSearchUtils.queryDateTimeEntryForMonths()
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] queryDateTimeEntryForMonths costTime:${GLog.getTime(queryTime)}")
            }
            if (coverEntry != null && coverEntry.count > 0) {
                var dateName = TextUtil.EMPTY_STRING
                kotlin.runCatching {
                    coverEntry.year?.let { coverYear ->
                        coverEntry.month?.let { coverMonth ->
                            val year = coverYear.toInt()
                            val month = coverMonth.replace(coverYear, TextUtil.EMPTY_STRING).toInt()
                            dateName = SearchCommonUtils.getDateYYMM(ContextGetter.context, year, month)
                        }
                    }
                }.onFailure {
                    GLog.w(TAG, "[query], Exception = $it")
                }
                cursor.addRow(arrayOf<Any>(dateName, coverEntry.mediaId, coverEntry.count, coverEntry.mediaType, coverEntry.galleryId))
            }
            recommendCache.updateToMonthRecommend(cursor)
            cursor.moveToPosition(-1)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] costTime:${GLog.getTime(startTime)}")
            }
            GTrace.traceEnd()
            cursor
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendMonthQuery"
    }
}