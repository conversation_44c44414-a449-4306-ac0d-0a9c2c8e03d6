/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OcrSearchResultAssembler.kt
 ** Description:OCR搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import android.database.Cursor
import android.provider.BaseColumns
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.ASTERISK
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.INNER_JOIN
import com.oplus.gallery.foundation.database.util.SQLGrammar.MATCH
import com.oplus.gallery.foundation.database.util.SQLGrammar.ON
import com.oplus.gallery.foundation.database.util.SQLGrammar.QUOTE
import com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult

/**
 * OCR搜索结果拼装器
 */
internal class OcrSearchResultAssembler : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_OCR

    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val startTime = System.currentTimeMillis()
        val ocrIdsList = queryMediaIdEntryFromOcrDB(singleKeyword)
        if (ocrIdsList.isEmpty().not()) {
            singleQueryResult.appendQueryResult(ocrIdsList, singleKeyword, resultType)
        }
        GLog.v(TAG, "[build], costTime:${GLog.getTime(startTime)}")

        return singleQueryResult
    }

    private fun queryMediaIdEntryFromOcrDB(keyword: String?): MutableList<MediaIdEntry> {
        val mediaIdList: MutableList<MediaIdEntry> = mutableListOf()
        val projection = arrayOf(
            BaseColumns._ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN,
            GalleryStore.OcrPages.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.INVALID, GalleryStore.OcrPagesColumns.IS_RECYCLED
        )
        kotlin.runCatching {
            matchOcrKeyword(projection, keyword).use { cursor ->
                if (cursor != null && cursor.count > 0) {
                    val idIndex = cursor.getColumnIndex(BaseColumns._ID)
                    val mediaIdIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID)
                    val dateTakenIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN)
                    val recycleIndex = cursor.getColumnIndex(GalleryStore.OcrPagesColumns.IS_RECYCLED)
                    val invalidIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.INVALID)
                    while (cursor.moveToNext()) {
                        val id = cursor.getString(idIndex)
                        val mediaId = cursor.getString(mediaIdIndex)
                        val dateTaken = cursor.getString(dateTakenIndex)
                        val isRecycle = cursor.getInt(recycleIndex)
                        val invalid = cursor.getInt(invalidIndex)
                        if (isRecycle != 1 && DatabaseUtils.isDataValid(invalid)) {
                            val entry = MediaIdEntry()
                            entry.galleryId = id.toInt()
                            entry.mediaId = mediaId.toInt()
                            entry.dateTaken = dateTaken.toLong()
                            mediaIdList.add(entry)
                        }
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, "queryMediaIdEntryFromOcrDB Exception:", it)
        }
        return mediaIdList
    }

    private fun matchOcrKeyword(columns: Array<String>?, keyword: String?): Cursor? {
        if (keyword.isNullOrEmpty()) {
            return null
        }
        val columnsBuilder = StringBuilder()
        if (columns.isNullOrEmpty()) {
            columnsBuilder.append(ASTERISK)
        } else {
            var i = 0
            val n = columns.size
            while (i < n) {
                columnsBuilder.append(columns[i])
                if (i != n - 1) {
                    columnsBuilder.append(SQLGrammar.COMMA)
                }
                i++
            }
        }
        val sqlBuilder = StringBuilder()
        sqlBuilder.append(SELECT)
        sqlBuilder.append(columnsBuilder.toString())
        sqlBuilder.append(FROM)
        sqlBuilder.append(GalleryStore.OcrPages.TAB + INNER_JOIN + GalleryStore.GalleryMedia.TAB + ON)
        sqlBuilder.append(GalleryStore.GalleryMedia.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.DATA + EQUAL
                + GalleryStore.OcrPages.TAB + DOT + GalleryStore.GalleryColumns.LocalColumns.DATA)
        sqlBuilder.append(WHERE)
        sqlBuilder.append(GalleryStore.OcrPagesColumns.CONTENT)
        sqlBuilder.append(MATCH)
        sqlBuilder.append(QUOTE).append(keyword).append(QUOTE)
        sqlBuilder.append(AND).append(DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB))
        val rawQueryReq = RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(CursorConvert())
            .setQuerySql(sqlBuilder.toString())
            .setSqlArgs(null)
            .build()
        kotlin.runCatching {
            return DataAccess.getAccess().rawQuery(rawQueryReq)
        }.onFailure {
            GLog.e(TAG, "[matchOcrKeyword], failed to match!", it)
        }
        return null
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}OcrSearchResultAssembler"
    }
}