/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonSearchResultAssembler.kt
 ** Description:人物搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import android.database.Cursor
import com.oplus.gallery.framework.abilities.search.entry.ShootPerson
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.OR
import com.oplus.gallery.foundation.database.util.SQLGrammar.QUOTE
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.completion.PersonCompletion
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.datatmp.R
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 人物搜索结果拼装器
 */
internal class PersonSearchResultAssembler(
    private val keywordCacheManager: KeywordCacheManager
) : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_PERSON

    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val searchGroupId = searchEntry.searchGroupId
        GLog.d(TAG, "[build] singleKeyword:$singleKeyword, searchGroupId:$searchGroupId")
        val startTime = System.currentTimeMillis()
        val keywordCache = keywordCacheManager.keywordCache
        if (keywordCache.personKeywordMap == null) {
            keywordCacheManager.updatePersonNameMapCache()
        }
        val personCompletion = PersonCompletion(ContextGetter.context, keywordCacheManager)
        val completePersonList = personCompletion.complete(singleKeyword)
        val cursor = getGroupIdByShootPerson(completePersonList)
        cursor?.let {
            val groupIdIndex = it.getColumnIndex(ScanFaceColumns.GROUP_ID)
            val groupNameIndex = it.getColumnIndex(ScanFaceColumns.GROUP_NAME)
            val mediaTypeIndex = it.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE)
            while (it.moveToNext()) {
                val gId = it.getInt(groupIdIndex)
                val groupName = it.getString(groupNameIndex)
                val mediaType = it.getInt(mediaTypeIndex)
                val list = GroupHelper.getMediaIdEntryByGroupId(gId.toLong())
                singleQueryResult.appendPersonQueryResult(list, gId, groupName, mediaType, SearchType.TYPE_PERSON)
            }
            IOUtils.closeQuietly(cursor)
        } ?: let {
            kotlin.runCatching {
                if (searchGroupId.isNullOrEmpty() || singleKeyword.isEmpty()) {
                    return singleQueryResult
                }
                val gId: Int = searchGroupId.toInt()
                val noName: String = ContextGetter.context.resources.getString(R.string.model_back_title_with_no_name)
                val groupName = GroupHelper.getGroupNameByGroupId(gId.toLong())
                if (gId > 0 && singleKeyword.equals(noName, ignoreCase = true) && groupName == null) {
                    val list = GroupHelper.getMediaIdEntryByGroupId(gId.toLong())
                    if (list.isNullOrEmpty().not()) {
                        singleQueryResult.appendPersonQueryResult(
                            list, gId,
                            ContextGetter.context.resources.getString(R.string.model_back_title_with_no_name),
                            list[0].mediaType,
                            SearchType.TYPE_PERSON
                        )
                    }
                }
            }.onFailure {
                GLog.w(TAG, "[build], NumberFormatException, groupId = $searchGroupId, e:$it")
            }
        }
        GLog.d(TAG, "[build] costTime:${GLog.getTime(startTime)}")

        return singleQueryResult
    }

    private fun getGroupIdByShootPerson(shootPersons: List<ShootPerson>?): Cursor? {
        if (shootPersons == null || shootPersons.isEmpty()) {
            return null
        }
        var cursor: Cursor? = null
        kotlin.runCatching {
            val projection = arrayOf(ScanFaceColumns.GROUP_NAME, ScanFaceColumns.GROUP_ID, ScanFaceColumns.MEDIA_TYPE)
            val stringBuffer = StringBuilder()
            for (person in shootPersons) {
                stringBuffer.append(ScanFaceColumns.GROUP_NAME).append(EQUAL).append(QUOTE).append(person.shootKeyword).append(QUOTE)
                stringBuffer.append(OR)
            }
            val lastIndex = stringBuffer.lastIndexOf("OR")
            if (lastIndex != -1) {
                stringBuffer.delete(lastIndex, stringBuffer.length)
            }
            val req = QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SCAN_FACE_GROUP_VIEW)
                .setProjection(projection)
                .setWhere(stringBuffer.toString())
                .setConvert(CursorConvert())
                .build()
            cursor = DataAccess.getAccess().query(req)
        }.onFailure {
            GLog.e(TAG, "getGroupIdByShootPerson query Exception:", it)
        }
        return cursor
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}PersonSearchResultAssembler"
    }
}