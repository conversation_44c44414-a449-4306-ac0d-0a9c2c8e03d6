/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SearchUtils.kt
 ** Description:搜索的工具类
 ** Version: 1.0
 ** Date: 2023/9/15
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/15      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.utils

import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.foundation.util.ext.isNullOrEmpty
import com.oplus.gallery.foundation.util.text.TextUtil
import java.util.stream.Collectors

/**
 * 搜索的工具类
 */
internal object SearchUtils {

    /**
     * 从数据源中取出galleryId  返回拼接结果
     *
     * @param mediaIdEntryList 数据源
     * @return 返回拼接结果
     */
    @JvmStatic
    fun mediaIdListToStr(mediaIdEntryList: List<MediaIdEntry>): String {
        return if (mediaIdEntryList.isNullOrEmpty()) {
            TextUtil.EMPTY_STRING
        } else {
            mediaIdEntryList.stream()
                .filter { mediaIdEntry: MediaIdEntry -> mediaIdEntry.mediaId > 0 }
                .map { mediaIdEntry: MediaIdEntry -> mediaIdEntry.mediaId.toString() }
                .collect(Collectors.joining(TextUtil.SPLIT_COMMA_SEPARATOR))
        }
    }

    /**
     * 从数据源中取出galleryId  返回拼接结果
     *
     * @param mediaIdEntryList 数据源
     * @return 返回拼接结果
     */
    @JvmStatic
    fun galleryIdListToStr(mediaIdEntryList: List<MediaIdEntry>): String {
        return if (mediaIdEntryList.isNullOrEmpty()) {
            TextUtil.EMPTY_STRING
        } else {
            mediaIdEntryList.stream()
                .map { mediaIdEntry: MediaIdEntry -> mediaIdEntry.galleryId.toString() }
                .collect(Collectors.joining(TextUtil.SPLIT_COMMA_SEPARATOR))
        }
    }

    /**
     * 从数据源中筛选出mediaId!=0的list数据  返回list数量（本地图片）
     *
     * @param mediaIdEntryList 数据源
     * @return 返回本地图片数量
     */
    @JvmStatic
    fun getLocalCountFromEntryList(mediaIdEntryList: List<MediaIdEntry>): Int {
        return mediaIdEntryList.stream().filter { mediaIdEntry: MediaIdEntry -> mediaIdEntry.mediaId > 0 }.count().toInt()
    }
}