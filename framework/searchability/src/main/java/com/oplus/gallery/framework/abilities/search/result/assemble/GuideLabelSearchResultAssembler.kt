/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GuideLabelSearchResultAssembler.kt
 ** Description:引导标签搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import android.content.Context
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 引导标签搜索结果拼装器
 */
internal class GuideLabelSearchResultAssembler(
    private val keywordCacheManager: KeywordCacheManager
) : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_GUIDE_LABEL

    @Suppress("LongMethod", "NestedBlockDepth")
    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val searchType = searchEntry.searchType
        val forceQuery = searchEntry.searchForceQuery
        GLog.d(TAG, "[assemble] singleKeyword:$singleKeyword, searchType:$searchType, forceQuery:$forceQuery")
        val startTime = System.currentTimeMillis()
        val keywordCache = keywordCacheManager.keywordCache
        // 知识图谱能力支持
        if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(ContextGetter.context)) {
            if (this.resultType == searchType) {
                accurateSearchByTagGraph(ContextGetter.context, singleKeyword, singleQueryResult)
            } else {
                fuzzySearchByTagGraph(ContextGetter.context, singleKeyword, singleQueryResult)
            }
        } else {
            if (this.resultType == searchType) {
                val labelIdsList = LabelSearchEngine.getInstance().getLabelIdsByName(singleKeyword)
                if (labelIdsList != null && labelIdsList.isNotEmpty()) {
                    val labelIdsArr = labelIdsList.toTypedArray()
                    val mediaIds: MutableList<MediaIdEntry>? = SearchDBHelper.queryFilterMediaIdEntryByLabels(
                        ContextGetter.context, labelIdsArr)
                    if (mediaIds != null && mediaIds.isNotEmpty()) {
                        singleQueryResult.appendQueryResult(mediaIds, singleKeyword, SearchType.TYPE_LABEL)
                    }
                } else {
                    GLog.d(TAG, "[assemble] getLabelIdsByName is empty")
                }
            } else {
                if (forceQuery) {
                    keywordCache.mediaIdToLabelIdsMap = DBSearchUtils.queryAllLabelsMappingMediaId()
                }
                var parentIds: List<Int?>? = null
                var partIds: List<Int?>? = null
                if (keywordCache.mediaIdToLabelIdsMap != null) {
                    val searchResult = LabelSearchEngine.getInstance().process(
                        singleKeyword, keywordCache.mediaIdToLabelIdsMap
                    )
                    if (searchResult != null) {
                        parentIds = searchResult.patentIds
                        partIds = searchResult.partMatchIds
                    }
                }
                if (parentIds.isNullOrEmpty().not()) {
                    val parentLabelIds = SearchDBHelper.queryLabelIdByLabels(parentIds?.toTypedArray())
                    if (parentLabelIds.isNullOrEmpty().not()) {
                        val parentLabelNames: MutableSet<String?> = HashSet()
                        for (item in parentLabelIds) {
                            val title = LabelDictionary.getLabelName(item)
                            if (!TextUtils.isEmpty(title)) {
                                parentLabelNames.add(title)
                            }
                        }
                        singleQueryResult.appendGuideQueryResult(parentLabelNames, null, SearchType.TYPE_GUIDE_LABEL)
                    }
                } else if (partIds.isNullOrEmpty().not()) {
                    val partLabelIds = SearchDBHelper.queryLabelIdByLabels(partIds?.toTypedArray())
                    if (partLabelIds.isNullOrEmpty().not()) {
                        val partLabelNames: MutableSet<String?> = HashSet()
                        for (item in partLabelIds) {
                            val title = LabelSearchEngine.getTitleById(item)
                            if (!TextUtils.isEmpty(title)) {
                                partLabelNames.add(title)
                            }
                        }
                        singleQueryResult.appendGuideQueryResult(partLabelNames, null, SearchType.TYPE_GUIDE_LABEL)
                    }
                }
            }
        }
        GLog.v(TAG, "[assemble] costTime:${GLog.getTime(startTime)}")
        return singleQueryResult
    }

    /**
     * 视觉知识图谱1.0精确查询
     */
    private fun accurateSearchByTagGraph(
        context: Context,
        singleKeyword: String,
        result: SingleQueryResult,
    ) {
        GLog.d(TAG, "[accurateSearchByTagGraph] singleKeyword:$singleKeyword")
        val startTime = System.currentTimeMillis()
        SearchTrackHelper.clear()
        val searchResult = LabelGraphSearchAbility.accurateSearch(
            context,
            singleKeyword,
            false
        )
        searchResult.forEach { (key, value) ->
            result.appendQueryResult(value, key, SearchType.TYPE_LABEL)
        }
        GLog.d(TAG, "[accurateSearchByTagGraph] costTime:${GLog.getTime(startTime)}")
    }

    /**
     * 视觉知识图谱1.0模糊查询
     */
    private fun fuzzySearchByTagGraph(
        context: Context,
        singleKeyword: String,
        result: SingleQueryResult
    ) {
        GLog.d(TAG, "[fuzzySearchByTagGraph] singleKeyword:$singleKeyword")
        val startTime = System.currentTimeMillis()
        SearchTrackHelper.clear()
        val labelNames = LabelGraphSearchAbility.fuzzySearch(context, singleKeyword)
        if (labelNames.isNotEmpty()) {
            result.appendGuideQueryResult(labelNames, null, SearchType.TYPE_GUIDE_LABEL)
        }
        GLog.d(TAG, "[fuzzySearchByTagGraph] costTime:${GLog.getTime(startTime)}")
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}GuideLabelSearchResultAssembler"
    }
}