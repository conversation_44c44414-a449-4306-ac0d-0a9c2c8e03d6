/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DmpAbility.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2023/8/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/8/31      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.dmp

import android.content.Context
import androidx.annotation.WorkerThread
import com.oplus.andes.photos.kit.search.ability.IDmpAbility
import com.oplus.dmp.sdk.SearchManager
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.util.AssetHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 中子DMP能力
 * 注意：必须要等DMPAnalyzer.init完成后才能注入到融合搜索框架中
 */
internal class DmpAbility(
    private val context: Context,
    private val keywordCacheManager: KeywordCacheManager
) : IDmpAbility {

    /**
     * dmp是否已经完成初始化
     */
    internal var initialized: AtomicBoolean = AtomicBoolean(false)

    private var cnGeneralHighFreqKeywordSet: HashSet<String>? = null
    private var cnTimeHighFreqKeywordSet: HashSet<String>? = null
    private var cnLocationHighFreqKeywordSet: HashSet<String>? = null
    private var cnSceneHighFreqKeywordSet: HashSet<String>? = null

    override fun dataSyncThreshold(): Float {
        return DATA_SYNC_THRESHOLD
    }

    override fun getSearchManager(): SearchManager {
        return DMPAnalyzer.searchManager
    }

    @WorkerThread
    fun init(): Boolean {
        if (initialized.compareAndSet(false, true)) {
            DMPAnalyzer.init(context)
        }
        return initialized.get()
    }

    /**
     * 向DMP添加相册自定义词典
     */
    fun addCustomDict(): Boolean {
        val cnCustomDict = getCnCustomDict()
        val cnHighFreqCustomDict = getCnHighFreqCustomDict()
        val englishCustomDict = getEnglishCustomDict()
        GLog.d(TAG, LogFlag.DL) {
            "addCustomDict, region=${FeatureUtils.region}, " +
                    "isMainland=${LocaleUtils.isChinaMainland(context)}, " +
                    "isEnglish=${LocaleUtils.isEnglish(context)}, " +
                    "cnCustomDict=${getDataDesc(cnCustomDict)}, " +
                    "cnHighFreqCustomDict=${getDataDesc(cnHighFreqCustomDict)}, " +
                    "englishCustomDict=${getDataDesc(englishCustomDict)}"
        }
        DMPAnalyzer.updateCustomDict(listOfNotNull(cnCustomDict, cnHighFreqCustomDict, englishCustomDict))
        return true
    }

    private fun getCnCustomDict(): HashMap<String, HashSet<String>>? {
        if (LocaleUtils.isChinaMainland(context).not()) {
            return null
        }
        val startTime = System.currentTimeMillis()
        val searchClassifier = SearchClassifier(context)
        val keywordCache = keywordCacheManager.keywordCache
        val customDictMap = HashMap<String, HashSet<String>>()

        // 时间
        val timeKeywordSet = HashSet<String>()
        customDictMap[DMPAnalyzer.Category.Time.value] = timeKeywordSet
        val chineseDateTimeKeywords = searchClassifier.dateTimeSelector.allChineseDateTimeKeywords
        timeKeywordSet.addAll(chineseDateTimeKeywords)

        // 节日
        val festivalKeywordSet = HashSet<String>()
        customDictMap[DMPAnalyzer.Category.Festival.value] = festivalKeywordSet
        keywordCache.festivalMap?.let { festivalKeywordSet.addAll(it.keys) }

        // 位置
        val locationKeywordSet = HashSet<String>()
        customDictMap[DMPAnalyzer.Category.Location.value] = locationKeywordSet
        keywordCache.addressKeywordSet?.let { locationKeywordSet.addAll(it) }

        // 标签
        val tagsKeywordSet = HashSet<String>()
        customDictMap[DMPAnalyzer.Category.Tags.value] = tagsKeywordSet
        keywordCache.labelIdToNameMap?.let { labelIdNameMap ->
            if (DEBUG_SEARCH) {
                GLog.d(TAG, "[getCnCustomDict] labelIdToNameMap:$labelIdNameMap")
                GLog.d(TAG, "[getCnCustomDict] key:${labelIdNameMap.keys} value:${labelIdNameMap.values}")
            }
            for (labels in labelIdNameMap.values) {
                labels?.let { tagsKeywordSet.addAll(it) }
            }
        }
        // 敏感词，此处用来屏蔽标签
        val rejectTagsKeywordSet = HashSet<String>()
        customDictMap[DMPAnalyzer.Category.Sensitive.value] = rejectTagsKeywordSet
        keywordCache.labelIdToNameMap?.let { labelIdNameMap ->
            labelIdNameMap.forEach { (key, value) ->
                value?.let {
                    if (it.any { LabelDictionary.isRejectLabel(it, key, false) }) {
                        rejectTagsKeywordSet.addAll(it)
                    }
                }
            }
        }
        GLog.d(TAG, "[getCnCustomDict] costTime:${GLog.getTime(startTime)}")
        return customDictMap
    }

    /**
     * 按照不同属性获取中文高频自定义词典
     * 向DMP添加相册自定义词典时调用
     */
    private fun getCnHighFreqCustomDict(): HashMap<String, HashSet<String>>? {
        if (LocaleUtils.isChinaMainland(context).not()) {
            return null
        }
        val startTime = System.currentTimeMillis()
        val customDictMap = HashMap<String, HashSet<String>>()

        // 一般普通词性
        val wordKeywordSet = HashSet<String>()
        if (cnGeneralHighFreqKeywordSet == null) {
            cnGeneralHighFreqKeywordSet = AssetHelper.getAssetCustomDictData(context, HIGH_FREQ_WORD_CUSTOM_DICT)
        }
        cnGeneralHighFreqKeywordSet?.let { wordKeywordSet.addAll(it) }
        customDictMap[DMPAnalyzer.Category.Word.value] = wordKeywordSet

        // 时间
        val timeKeywordSet = HashSet<String>()
        if (cnTimeHighFreqKeywordSet == null) {
            cnTimeHighFreqKeywordSet = AssetHelper.getAssetCustomDictData(context, HIGH_FREQ_TIME_CUSTOM_DICT)
        }
        cnTimeHighFreqKeywordSet?.let { timeKeywordSet.addAll(it) }
        customDictMap[DMPAnalyzer.Category.Time.value] = timeKeywordSet

        // 位置
        val locationKeywordSet = HashSet<String>()
        if (cnLocationHighFreqKeywordSet == null) {
            cnLocationHighFreqKeywordSet = AssetHelper.getAssetCustomDictData(context, HIGH_FREQ_LOCATION_CUSTOM_DICT)
        }
        cnLocationHighFreqKeywordSet?.let { locationKeywordSet.addAll(it) }
        customDictMap[DMPAnalyzer.Category.Location.value] = locationKeywordSet

        //场景词
        val sceneKeywordSet = HashSet<String>()
        if (cnSceneHighFreqKeywordSet == null) {
            cnSceneHighFreqKeywordSet = loadQuerySceneDic(context, HIGH_FREQ_SCENE_CUSTOM_DICT)
        }
        cnSceneHighFreqKeywordSet?.let { sceneKeywordSet.addAll(it) }
        customDictMap[DMPAnalyzer.Category.Scene.value] = sceneKeywordSet
        GLog.d(TAG, "[getCnHighFreqCustomDict] costTime: ${GLog.getTime(startTime)}")
        return customDictMap
    }

    /**
     * 获取传入词典中的各业务数据情况的描述
     * 返回如 Time=[20,Oct]，第一个是词语数量，第二个是首个元素的内容，用于检查内容是什么语言
     */
    private fun getDataDesc(map: HashMap<String, HashSet<String>>?): String? {
        map ?: return null
        fun getSingleDataDesc(key: String, map: HashMap<String, HashSet<String>>): String {
            return "[${map[key]?.size},${map[key]?.firstOrNull()}]"
        }
        return "Word=${getSingleDataDesc(DMPAnalyzer.Category.Word.value, map)}, " +
                "Time=${getSingleDataDesc(DMPAnalyzer.Category.Time.value, map)}, " +
                "Festival=${getSingleDataDesc(DMPAnalyzer.Category.Festival.value, map)}, " +
                "Location=${getSingleDataDesc(DMPAnalyzer.Category.Location.value, map)}, " +
                "Tags=${getSingleDataDesc(DMPAnalyzer.Category.Tags.value, map)}, " +
                "Sensitive=${getSingleDataDesc(DMPAnalyzer.Category.Sensitive.value, map)}"
    }

    /**
     * 获取英文自定义词典
     */
    private fun getEnglishCustomDict(): HashMap<String, HashSet<String>>? {
        if (LocaleUtils.isEnglish(context).not()) {
            return null
        }
        val startTime = System.currentTimeMillis()
        val searchClassifier = SearchClassifier(context)
        val keywordCache = keywordCacheManager.keywordCache
        val customDictMap = HashMap<String, HashSet<String>>()

        // 时间
        customDictMap[DMPAnalyzer.Category.Time.value] = HashSet<String>().apply {
            addAll(searchClassifier.dateTimeSelector.allForeignDateTimeKeywords)
        }
        // 节日
        customDictMap[DMPAnalyzer.Category.Festival.value] = HashSet<String>().apply {
            keywordCache.festivalMap?.let { addAll(it.keys) }
        }
        // 位置
        customDictMap[DMPAnalyzer.Category.Location.value] = HashSet<String>().apply {
            keywordCache.addressKeywordSet?.let { addAll(it) }
        }
        // 标签
        customDictMap[DMPAnalyzer.Category.Tags.value] = HashSet<String>().apply {
            if (DEBUG_SEARCH) {
                GLog.d(TAG, "[getEnglishCustomDict] labelIdToNameMap:${keywordCache.labelIdToNameMap?.size}")
                GLog.d(TAG, "[getEnglishCustomDict] key:${keywordCache.labelIdToNameMap?.keys} value:${keywordCache.labelIdToNameMap?.values}")
            }
            keywordCache.labelIdToNameMap?.values?.forEach { nameList -> nameList?.let { addAll(it) } }
        }
        // 拒识
        customDictMap[DMPAnalyzer.Category.Sensitive.value] = HashSet<String>().apply {
            keywordCache.labelIdToNameMap?.forEach { (labelId, labelNameList) ->
                if (labelNameList?.any { LabelDictionary.isRejectLabel(it, labelId, false) } == true) {
                    addAll(labelNameList)
                }
            }
        }
        GLog.d(TAG, "[getEnglishCustomDict] costTime:${GLog.getTime(startTime)}")
        return customDictMap
    }

    /**
     * 加载检索意图识别词典
     *
     */
    private fun loadQuerySceneDic(context: Context, fileName: String): HashSet<String> {
        val sceneWords = HashSet<String>()
        val allLines = AssetHelper.getAssetCustomDictData(context, fileName)
        for (line in allLines) {
            val split = line.split(TextUtil.SEMICOLON.toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (split.size != 2 || split[0].startsWith(TextUtil.LEFT_SLASH)) {
                continue
            }
            val intentKeyword = split[0]
            if (!intentKeyword.isEmpty()) {
                sceneWords.add(intentKeyword)
            }
        }
        return sceneWords
    }
    companion object {
        private const val TAG = "${SearchConst.TAG}DmpAbility"

        private const val DATA_SYNC_THRESHOLD = 0.95f
        private const val HIGH_FREQ_WORD_CUSTOM_DICT = "high_freq_word_custom_cn_dict.txt"
        private const val HIGH_FREQ_TIME_CUSTOM_DICT = "high_freq_time_custom_cn_dict.txt"
        private const val HIGH_FREQ_LOCATION_CUSTOM_DICT = "high_freq_location_custom_cn_dict.txt"
        private const val HIGH_FREQ_SCENE_CUSTOM_DICT = "scene_words.txt"
    }
}