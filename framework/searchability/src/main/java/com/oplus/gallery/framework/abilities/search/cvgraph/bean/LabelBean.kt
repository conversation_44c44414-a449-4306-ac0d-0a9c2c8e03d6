/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelBean.kt
 ** Description: 视觉知识图谱标签类
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/2/9
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** 80371959                        2023/2/9     1.0         *new
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph.bean

import com.oplus.gallery.foundation.tracing.constant.LabelTrackTypeConstant
import java.util.Objects

/**
 * 对应CV的图谱的标签数据类型
 * @property label 标签名称
 * @property labelId 标签id
 * @property labelOriginValue 标签类型，补全词，纠错词等
 */
data class LabelBean(
    val labelId: Int,
    val label: String,
    /**
     * 对上层显示的搜索词，默认值和label字段保持一致
     * 目前有2种情况和label字段的值不一致
     * 1.可能是同义词：比如用户输入词为入场券，对应查询生成的LabelBean为
     * {labelId:232,label:门票,displayName:入场券}
     * 2.可能是同义词补全词：比如用户输入词为入场，对应生成的LabelBean为
     * {labelId:232,label:门票,displayName:入场券}
     *
     * 存在一种特殊情况：
     * 可能是纠错词的补全词：比如用户输入的词为银亻，纠错后为银行，在tag_gra表中能补全同义词生成对应生成的LabelBean为
     * {labelId:186,label:支票,displayName:银行支票}
     * 但是纠错词属于模糊查询，模糊查询推荐给用户的一定是一个标签，不会用到displayName字段
     */
    var displayName: String = label
) {
    /**
     * 标签搜索来源, 补全词, 纠错词等, 搜索埋点使用
     */
    var labelOriginValue: Int = LabelTrackTypeConstant.LABEL_VALUE

    /**
     * 此处的equals逻辑主要是为了去重使用，请勿随意改动
     * 在图谱中，tagId是唯一的，所以如果tagId相同，即为同一个标签。
     */
    override fun equals(other: Any?): Boolean {
        if (other == null) return false
        return (other is LabelBean) && (labelId == other.labelId)
    }

    /**
     * 重写了equals之后，会需要对应的重写hashCode方法，否则代码质量编译不过关。
     * <pre>
     *     Effective Java 中对采用31做了说明：
     *     之所以使用 31， 是因为他是一个奇素数。如果乘数是偶数，并且乘法溢出的话，信息就会丢失，因为与2相乘等价于移位运算（低位补0）。
     *     使用素数的好处并不很明显，但是习惯上使用素数来计算散列结果。
     *     31 有个很好的性能，即用移位和减法来代替乘法，可以得到更好的性能： 31 * i == (i << 5）- i， 现代的 VM 可以自动完成这种优化
     * </pre>
     */
    override fun hashCode(): Int {
        var result = Objects.hashCode(labelId)
        result = 31 * result + Objects.hashCode(label)
        return result
    }
}