/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - QueryDispatcher.kt
 ** Description: Query分发器
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.content.Context
import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_CACHE
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FESTIVAL
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_ALBUM
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_FILENAME
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_GUIDE_LABEL
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_LABEL
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_LOCATION
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_MEMORIES
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_MULTI_LABEL
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_OCR
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_PERSON
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_TIME
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_LABEL
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_LOCATION
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_MEMORIES
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_MONTH
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_PERSON
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_RECENTLY_ADDED
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_RECOMMEND_YEAR
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SUGGESTION_SELECTION_SLOT_FILTER
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector
import com.oplus.gallery.business_lib.model.data.label.LabelSelector
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ANDES_SEARCH
import com.oplus.gallery.framework.abilities.search.QueryRequest
import com.oplus.gallery.framework.abilities.search.SearchAbilityImpl
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchConst.URI_MATCHER
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.cache.RecommendCacheManager
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.isolate.ISearch
import com.oplus.gallery.framework.abilities.search.result.MultiQueryResult
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryBucketId
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryForce
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryGroupId
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryLabelId
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryLocationType
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryMemoryId
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryOcr
import com.oplus.gallery.framework.abilities.search.utils.QueryWordParseUtils.getQueryWord
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.concurrent.Future

/**
 * Query分发器
 */
internal class QueryDispatcher(
    private val keywordCacheManager: KeywordCacheManager,
    private val searchRecommendCacheManager: RecommendCacheManager,
    private val geoCacheService: GeoCacheService,
    private val labelSelector: LabelSelector,
    private val festivalSelector: FestivalSelector,
    private val iSearch: ISearch
) {

    private val isAndesSearchSupport: Boolean
        get() = SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_ANDES_SEARCH) ?: false

    /**
     * 分发执行query请求
     *
     * @param queryRequest query请求
     * @param labelDataTaskFuture 标签数据任务加载结果
     * @return
     */
    fun dispatch(queryRequest: QueryRequest, labelDataTaskFuture: Future<Boolean>?): Cursor? {
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[dispatch] queryRequest:$queryRequest")
        }
        val matchCode = URI_MATCHER.match(queryRequest.uri)
        if (matchCode == SearchConst.MATCH_CODE_SEARCH_INFO) {
            return execute(DmpQuery(queryRequest.projection, queryRequest.queryArgs))
        }
        if (queryRequest.selection.isNullOrEmpty().not() && (queryRequest.selection == SUGGESTION_SELECTION_SLOT_FILTER)) {
            return if (isAndesSearchSupport) {
                andesBreenoSearch(queryRequest)
            } else {
                execute(createBreenoQuery(queryRequest))
            }
        }
        val selectionBuilder = StringBuilder()
        queryRequest.selectionArgs?.let {
            it.forEach { selectionArg ->
                selectionBuilder.append(selectionArg)
            }
        }
        val queryParam = selectionBuilder.toString().trim()
        if (queryParam.isEmpty()) {
            GLog.d(TAG, "[dispatch] queryParam is Empty")
            return null
        }
        return dispatchQuery(queryParam)
    }

    /**
     * 执行特定查询
     *
     * @param querySpec 查询规格
     * @return cursor
     */
    fun execute(querySpec: Query): Cursor? {
        return querySpec.query()
    }

    /**
     * 根据queryWord执行查询分发, 路由到特定查询
     *
     * @param queryParam 传入Provider的查询参数字符串
     */
    private fun dispatchQuery(queryParam: String): Cursor? {
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[dispatchQuery] queryParam:$queryParam")
        }
        val querySpecMatch = buildQuery(queryParam)
        return querySpecMatch?.query() ?: andesSearch(queryParam)
    }

    private fun andesSearch(queryParam: String): Cursor? {
        val inputKeyword: String? = getQueryWord(queryParam, true)
        val lowercaseQueryParam = queryParam.lowercase()
        val searchType = getSearchType(lowercaseQueryParam)
        val labelId = getQueryLabelId(queryParam)
        val groupId = getQueryGroupId(queryParam)
        val bucketId = getQueryBucketId(queryParam)
        val memoryId = getQueryMemoryId(queryParam)
        val locationType = getQueryLocationType(queryParam)
        var cursor: Cursor? = null
        inputKeyword?.let {
            kotlin.runCatching {
                cursor = iSearch.search(it, searchType, labelId, groupId, bucketId, memoryId, locationType)
            }.onFailure {
                GLog.e(TAG, "[andesSearch] andes search failed:", it)
                /**
                 * 融合搜索特定类型的检索与原相册的搜索差距较大，特定类型搜索逻辑是不兼容的，先关闭这个
                 * cursor = createComposeResultQuery(queryParam, searchType)?.query()
                 */
            }
        }
        // 当融合搜索结果无召回时，再走标签的模糊搜索，去前3个有结果的推荐词给到应用端显示在引导词中：你可能想搜
        if (cursor == null) {
            inputKeyword?.let {
                return getSuggestionWordResult(ContextGetter.context, it)
            }
        }
        return cursor
    }

    private fun getSuggestionWordResult(
        context: Context,
        singleKeyword: String,
    ): MatrixCursor {
        val multiQueryResult = MultiQueryResult()
        val startTime = System.currentTimeMillis()
        val result = SingleQueryResult(singleKeyword)
        val labelNames = mutableSetOf<String>()
        if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(context)) {
            labelNames.addAll(LabelGraphSearchAbility.fuzzySearchForKit(context, singleKeyword))
        }
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[getSuggestionWordResult] singleKeyword:$singleKeyword, labelNames:$labelNames")
        }
        // 取前面3个有搜索结果的推荐词
        val validLabelNames = mutableSetOf<String>()
        labelNames.forEachIndexed { index, labelName ->
            if (index < SUGGESTION_WORD_LIMIT) {
                validLabelNames.add(labelName)
            }
        }
        result.appendGuideQueryResult(validLabelNames, null, SearchType.TYPE_GUIDE_LABEL)
        multiQueryResult.addKeywordResult(result)
        GLog.d(TAG, "[getSuggestionWordResult] costTime:${GLog.getTime(startTime)}")
        return multiQueryResult.buildCursorForQueryResult()
    }

    private fun createComposeResultQuery(queryParam: String, type: Int): Query? {
        val searchEntry = SearchEntry(ContextGetter.context)
        val keywordCache = keywordCacheManager.keywordCache
        searchEntry.searchType = type
        val inputKeyword: String? = getQueryWord(queryParam, true)
        inputKeyword?.let {
            searchEntry.searchKeyword = it
            searchEntry.searchLabelId = getQueryLabelId(queryParam)
            searchEntry.searchForceQuery = getQueryForce(queryParam)
            searchEntry.searchOcrQuery = getQueryOcr(queryParam)
            searchEntry.searchGroupId = getQueryGroupId(queryParam)
            val classifier = SearchClassifier(ContextGetter.context)
            classifier.classify(it, searchEntry.searchType)
            classifier.setKeywordCache(keywordCache)
            searchEntry.searchKeywords = classifier.normalizedKeywords
            searchEntry.searchClassifier = classifier
            return ComposeResultQuery(searchEntry, keywordCacheManager, geoCacheService, festivalSelector)
        } ?: also {
            GLog.w(TAG, "[createSpecTypeQuery] lowerKeyword is null")
        }

        return null
    }

    private fun buildQuery(queryParam: String): Query? {
        val lowercaseQueryParam = queryParam.lowercase()
        val recommendCache = searchRecommendCacheManager.recommendCache
        val forceQuery = getQueryForce(lowercaseQueryParam)
        when {
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_CACHE) -> return RecommendSuggestionCacheQuery(recommendCache)
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_YEAR) -> return RecommendYearQuery(forceQuery, recommendCache)
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_MONTH) -> return RecommendMonthQuery(forceQuery, recommendCache)
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_FESTIVAL) -> return RecommendFestivalQuery(forceQuery, recommendCache)
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_LOCATION) -> {
                return RecommendLocationQuery(
                    forceQuery, recommendCache, geoCacheService
                )
            }
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_PERSON) -> return RecommendPersonQuery(forceQuery, recommendCache)
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_LABEL) -> {
                return RecommendLabelQuery(
                    forceQuery, recommendCache, labelSelector
                )
            }
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_MEMORIES) -> return RecommendMemoriesQuery(forceQuery, recommendCache)
            lowercaseQueryParam.startsWith(QUERY_RECOMMEND_RECENTLY_ADDED) -> return RecommendRecentlyAddedQuery(forceQuery, recommendCache)
            else -> {
                // 融合搜索不需要创建相册端的Query，直接走Kit的search流程, 返回null即可
                return if (isAndesSearchSupport) {
                    null
                } else {
                    createComposeResultQuery(queryParam, getSearchType(lowercaseQueryParam))
                }
            }
        }
    }

    private fun getSearchType(lowercaseQueryWord: String): Int {
        return when {
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_TIME) -> SearchType.TYPE_DATETIME
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_LOCATION) -> SearchType.TYPE_LOCATION
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_LABEL) -> SearchType.TYPE_LABEL
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_GUIDE_LABEL) -> SearchType.TYPE_GUIDE_LABEL
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_OCR) -> SearchType.TYPE_OCR
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_PERSON) -> SearchType.TYPE_PERSON
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_ALBUM) -> SearchType.TYPE_ALBUM
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_MEMORIES) -> SearchType.TYPE_MEMORIES_ALBUM
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_FILENAME) -> SearchType.TYPE_FILE_NAME
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_RECENTLY_ADDED) -> SearchType.TYPE_RECENT_TIME
            lowercaseQueryWord.startsWith(QUERY_RECOMMEND_FILTER_MULTI_LABEL) -> SearchType.TYPE_MULTI_LABEL
            else -> SearchType.TYPE_MASK_TYPE
        }
    }

    /**
     * "slot-filter"类型查询：仅用于Breeno小布语音, 为Breeno搜索内容
     * 可搜索配置.文件：res-biz/xml/model_accurate_searchable.xml
     *
     * 直接从查询参数selectionArgs中过滤出关键字，而后用关键字执行查询，类型使用TYPE_MASK_TYPE
     * 此种场景下不需要对关键字进行分类。
     *
     * @param queryRequest 查询请求，selectionArgs 比如 { "time?input=2019"，"location?input=深圳"}
     * @return Cursor对象
     */
    private fun createBreenoQuery(queryRequest: QueryRequest): ComposeResultQuery {
        val selectionArgs = queryRequest.selectionArgs
        val queryWordList = ArrayList<String>()
        val keywordBuffer = StringBuffer()
        var forceQuery = false
        selectionArgs?.forEach { selectionArg ->
            forceQuery = forceQuery or getQueryForce(selectionArg)
            val queryWord: String? = getQueryWord(selectionArg, true)
            queryWord?.let {
                queryWordList.add(it)
                keywordBuffer.append(it).append(TextUtil.BLANK_STRING)
            }
            GLog.d(TAG, "[createBreenoQuery] selectionArg = $selectionArg")
        }
        val searchEntry = SearchEntry(ContextGetter.context)
        searchEntry.searchClassifier = SearchClassifier(ContextGetter.context)
        searchEntry.searchClassifier.setKeywordCache(keywordCacheManager.keywordCache)
        searchEntry.searchType = SearchType.TYPE_MASK_TYPE
        searchEntry.searchKeywords = queryWordList.toTypedArray()
        searchEntry.searchKeyword = keywordBuffer.toString().trim()
        searchEntry.searchForceQuery = forceQuery
        searchEntry.searchOcrQuery = false
        searchEntry.searchGroupId = null
        GLog.d(TAG, "[createBreenoQuery] searchEntry:$searchEntry")

        return ComposeResultQuery(searchEntry, keywordCacheManager, geoCacheService, festivalSelector)
    }

    private fun andesBreenoSearch(queryRequest: QueryRequest): Cursor? {
        val selectionArgs = queryRequest.selectionArgs
        val queryWordList = ArrayList<String>()
        val keywordBuffer = StringBuffer()
        var forceQuery = false
        selectionArgs?.forEach { selectionArg ->
            forceQuery = forceQuery or getQueryForce(selectionArg)
            val queryWord: String? = getQueryWord(selectionArg, true)
            queryWord?.let {
                queryWordList.add(it)
                keywordBuffer.append(it).append(TextUtil.BLANK_STRING)
            }
            GLog.d(TAG, "[andesBreenoSearch] selectionArg = $selectionArg")
        }
        var cursor: Cursor? = null
        val inputKeyword = keywordBuffer.toString().trim()
        kotlin.runCatching {
            cursor = iSearch.search(inputKeyword, SearchType.TYPE_MASK_TYPE)
        }.onFailure {
            GLog.e(TAG, "[andesBreenoSearch] failed:", it)
        }
        // 当融合搜索结果无召回时，再走标签的模糊搜索，去前3个有结果的推荐词给到应用端显示在引导词中：你可能想搜
        if (cursor == null) {
            return getSuggestionWordResult(ContextGetter.context, inputKeyword)
        }
        return cursor
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}QueryDispatcher"

        /**
         * 产品定义：融合搜索结果为空时，返回前3个标签推荐词(想搜词、近义词、上位词)，并显示在“您可以尝试搜索”页
         */
        private const val SUGGESTION_WORD_LIMIT = 3
    }
}