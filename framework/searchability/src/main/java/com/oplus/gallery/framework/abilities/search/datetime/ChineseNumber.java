/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - .java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date: 2017/02/07
 ** Author:shu<PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** shu<PERSON><PERSON>@Apps.Gallery3D           2017/02/07     1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.search.datetime;

import android.content.res.Resources;
import android.util.SparseArray;

import com.oplus.gallery.framework.datatmp.R;

public class ChineseNumber {
    public static final int NUMBER_0 = 0;
    public static final int NUMBER_1 = 1;
    public static final int NUMBER_2 = 2;
    public static final int NUMBER_3 = 3;
    public static final int NUMBER_4 = 4;
    public static final int NUMBER_5 = 5;
    public static final int NUMBER_6 = 6;
    public static final int NUMBER_7 = 7;
    public static final int NUMBER_8 = 8;
    public static final int NUMBER_9 = 9;
    public static final int NUMBER_10 = 10;
    public static final int NUMBER_100 = 100;
    public static final int NUMBER_1000 = 1000;
    private SparseArray<String[]> mChineseNumberArray = new SparseArray<>();

    public ChineseNumber(Resources resources) {
        mChineseNumberArray.put(NUMBER_0, resources.getStringArray(R.array.model_str_cn_num_0));
        mChineseNumberArray.put(NUMBER_1, resources.getStringArray(R.array.model_str_cn_num_1));
        mChineseNumberArray.put(NUMBER_2, resources.getStringArray(R.array.model_str_cn_num_2));
        mChineseNumberArray.put(NUMBER_3, resources.getStringArray(R.array.model_str_cn_num_3));
        mChineseNumberArray.put(NUMBER_4, resources.getStringArray(R.array.model_str_cn_num_4));
        mChineseNumberArray.put(NUMBER_5, resources.getStringArray(R.array.model_str_cn_num_5));
        mChineseNumberArray.put(NUMBER_6, resources.getStringArray(R.array.model_str_cn_num_6));
        mChineseNumberArray.put(NUMBER_7, resources.getStringArray(R.array.model_str_cn_num_7));
        mChineseNumberArray.put(NUMBER_8, resources.getStringArray(R.array.model_str_cn_num_8));
        mChineseNumberArray.put(NUMBER_9, resources.getStringArray(R.array.model_str_cn_num_9));
        mChineseNumberArray.put(NUMBER_10, resources.getStringArray(R.array.model_str_cn_num_10));
        mChineseNumberArray.put(NUMBER_100, resources.getStringArray(R.array.model_str_cn_num_100));
        mChineseNumberArray.put(NUMBER_1000, resources.getStringArray(R.array.model_str_cn_num_1000));
    }

    public int decode(String text) {
        int result = 0;
        int textLength = (text != null) ? text.length() : 0;

        if (textLength == 0) {
            return -1;
        }
        for (int i = 0; i < textLength; i++) {
            int number = findNumber(text.charAt(i) + "");
            if (number >= 0) {
                if (number >= NUMBER_10) {
                    return decodeNumerical(text);
                }
                result *= NUMBER_10;
                result += number;
            } else {
                return -1;
            }
        }

        return result;
    }

    public String encode(int number) {
        String[] array = mChineseNumberArray.get(number);
        return (array != null) ? array[0] : null;
    }

    private int decodeNumerical(String text) {
        /*
        1. 两千零一
        2. 两千三
        */
        int result = 0;
        int textLength = text.length();
        int operateNumber = 0;
        int valueNumber = 1;
        for (int i = 0; i < textLength; i++) {
            int number = findNumber(text.charAt(i) + "");
            if (number >= 0) {
                if (number >= NUMBER_10) {
                    // Operate number
                    operateNumber = number;
                    result += valueNumber * operateNumber;
                    valueNumber = 0;
                } else {
                    // Value number
                    valueNumber = number;
                    if (number == 0) { // ?
                        operateNumber = NUMBER_10;
                    }
                }

            } else {
                return -1;
            }
        }

        if (valueNumber > 0) {
            result += valueNumber * operateNumber / NUMBER_10;
        }
        return result;
    }

    private int findNumber(String word) {
        int mapSize = mChineseNumberArray.size();
        for (int index = 0; index < mapSize; index++) {
            int key = mChineseNumberArray.keyAt(index);
            String[] value = mChineseNumberArray.get(key);
            if (value != null) {
                for (String aValue : value) {
                    if (aValue.equals(word)) {
                        return key;
                    } else if (word.equals(key + "")) {
                        return key;
                    }
                }
            }
        }
        return -1;
    }

}