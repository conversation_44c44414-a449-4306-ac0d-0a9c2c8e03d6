/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendRecentlyAddedQuery.kt
 ** Description: 是否有推荐缓存查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.RECOMMEND_HAS_CACHE
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.RECOMMEND_NO_CACHE
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache

/**
 * 是否有推荐缓存查询
 *
 * @property recommendCache 推荐缓存
 */
internal class RecommendSuggestionCacheQuery(private val recommendCache: RecommendCache) : Query {
    override fun query(): Cursor {
        val cursor = MatrixCursor(COLUMN_CACHE)
        cursor.addRow(
            arrayOf<Any>(
                if (recommendCache.hasYearRecommend()) {
                    RECOMMEND_HAS_CACHE
                } else {
                    RECOMMEND_NO_CACHE
                }
            )
        )
        return cursor
    }

    companion object {
        private val COLUMN_CACHE = arrayOf(
            SearchSuggestionProviderUtil.COLUMNCACHE
        )
    }
}
