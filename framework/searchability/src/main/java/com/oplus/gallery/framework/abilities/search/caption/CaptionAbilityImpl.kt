/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  CaptionAbilityImpl.kt
 * * Description:  caption扫描的原子能力
 * * Version: 1.0
 * * Date : 2025/02/19
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/02/19     1.0        create
 ************************************************************/
package com.oplus.gallery.framework.abilities.search.caption

import android.content.Context
import android.database.Cursor
import androidx.annotation.WorkerThread
import com.ai.slp.engine.SearchResult
import com.ai.slp.listener.IErrorListener
import com.oplus.aiunit.aicaption.AICaptionClient
import com.oplus.aiunit.core.callback.AICallback
import com.oplus.aiunit.core.data.AIConfig
import com.oplus.aiunit.core.data.DetectData
import com.oplus.andes.photos.kit.search.ability.Result
import com.oplus.appability.AbsAppAbility
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.embedding.caption.ICaptionEmbeddingAbility
import com.oplus.gallery.standard_lib.app.AppConstants
import org.json.JSONObject

/**
 * caption扫描的原子能力，提供caption扫描
 */
class CaptionAbilityImpl(private val context: Context) : AbsAppAbility(), ICaptionAbility {
    override var isDownloading: Boolean = false
    override val domainInstance: AutoCloseable = this
    override val captionClient: AICaptionClient by lazy {
        AICaptionClient(context)
    }
    override val captionVersion: String? by lazy {
        getVersion()
    }
    override var embeddingAbilityImpl: ICaptionEmbeddingAbility? = null
    override var embeddingVersion = TextUtil.EMPTY_STRING

    // processCaptions的状态回调
    private val callback = object : AICallback {
        override fun onAllDestroy(detectName: String) {
            GLog.d(TAG, DL) { "CaptionAbilityImpl onAllDestroy detectName:$detectName" }
        }

        override fun onAllFail(
            detectName: String,
            err: Int,
            msg: String?
        ) {
            GLog.d(TAG, DL) { "CaptionAbilityImpl onAllFail detectName:$detectName, err:$err, msg:$msg" }
        }

        override fun onOneDestroy(config: AIConfig) {
            GLog.d(TAG, DL) { "CaptionAbilityImpl onOneDestroy config:$config" }
        }

        override fun onOneFail(
            config: AIConfig,
            err: Int,
            msg: String?
        ) {
            GLog.d(TAG, DL) { "CaptionAbilityImpl onOneFail config:$config, err:$err, msg:$msg" }
        }

        override fun onStart(config: AIConfig) {
            GLog.d(TAG, DL) { "CaptionAbilityImpl onStart config:$config" }
        }
    }

    // 当前线程信息
    private val currentThreadInfo = Thread.currentThread().id.toString() + TextUtil.STRIKE + Thread.currentThread().name

    @WorkerThread
    @Synchronized
    override fun init(): Boolean {
        runCatching {
            // 0.检查是否支持caption能力
            if (checkSupported().not()) {
                GLog.e(TAG, DL) { "initCaption, caption is not supported, return" }
                return false
            }

            // 1.检查caption版本号
            if (captionVersion == null) {
                GLog.e(TAG, DL) { "initCaption, captionVersion is null, return" }
                return false
            }

            // 2.设置回调
            add(callback)

            GLog.d(TAG, DL) { "initCaption init successful, captionSdk:$captionVersion" }
            return true
        }.onFailure {
            GLog.e(TAG, DL) { "initCaption exception, $it" }
        }
        return false
    }

    @Synchronized
    @WorkerThread
    override fun release() {
        embeddingAbilityImpl?.release()
        stopAndClear()
    }

    /**
     * caption sdk 版本号
     */
    override fun getVersion(): String? {
        return  captionClient.version
    }

    /**
     * 是否支持，同AISettings.getDetectData
     */
    override fun checkSupported(): Boolean {
        return captionClient.checkSupported()
    }

    /**
     * 同步初始化：绑定服务和算法加载，需要在子线程执行
     */
    override fun initSync() {
        captionClient.initSync()
    }

    /**
     * 设置共享内存池大小
     */
    override fun setSharedMemoryPool(pool: IntArray) {
        captionClient.setSharedMemoryPool(pool)
    }

    /**
     * 算法加载，需要在子线程执行
     * @return 异常码，详见CaptionErrorCode
     */
    override fun start(): Int {
        GLog.d(TAG, DL) { "start, thread = $currentThreadInfo, embeddingAbilityImpl:$embeddingAbilityImpl" }
        captionClient.start()
        val result = embeddingAbilityImpl?.init()
        return if (result == true) {
            ERROR_NONE
        } else {
            ERROR_UNKNOWN
        }
    }

    /**
     * 停止算法，主要释放跨进程通信的共享内存，需要在子线程执行
     */
    override fun stop() {
        captionClient.stop()
    }

    /**
     * 等价于先调用stop，再调用clear
     */
    override fun stopAndClear() {
        captionClient.stopAndClear()
    }

    /**
     * 异步停止算法
     */
    override fun stopAsync() {
        captionClient.stopAsync()
    }

    /**
     * 添加状态监听，1.4.3以后支持
     */
    override fun add(callback: AICallback) {
        captionClient.add(callback)
    }

    /**
     * 设置异常回调
     */
    override fun setErrorListener(listener: IErrorListener) {
        embeddingAbilityImpl?.setErrorListener(listener)
    }

    /**
     * 移除指定状态监听
     */
    override fun remove(callback: AICallback) {
        captionClient.remove(callback)
    }

    /**
     * 移除所有状态监听
     */
    override fun clear() {
        captionClient.clear()
    }

    /**
     * 销毁，但注意不会清除回调，注意在子线程调用
     */
    override fun destroy() {
        captionClient.destroy()
    }

    /**
     * 图片转文本
     * 由于远程IPC调用是耗时操作，此方法需在子线程调用
     * @param data:图片的绝对路径
     * @return 图片描述文本
     */
    @Synchronized
    @WorkerThread
    override fun processCaptions(data: String): Result<Array<String>> {
        GLog.d(TAG, DL) { "processCaptions, thread = $currentThreadInfo, captionClient = $captionClient" }
        runCatching {
            val jsonObject = JSONObject()
            val extJson = jsonObject.put(CAPTION_RE_PREDICT, AppConstants.Number.NUMBER_0).toString()
            val results = captionClient.process(data, extJson)
            val code = results.code
            val msg = results.msg
            val result = results.data
            return Result(code, msg, result)
        }.onFailure {
            GLog.e(TAG, DL, it) { "processCaptions exception, $it" }
        }
        return Result(ERROR_UNKNOWN, ERROR_UNKNOWN_MSG, emptyArray())
    }

    /**
     * 批量图片转文本
     * 由于远程IPC调用是耗时操作，此方法需在子线程调用
     * @param datas: 图片的绝对路径,建议每次不超过1000
     * @return: 图片描述文本
     */
    @Synchronized
    @WorkerThread
    override fun processCaptions(datas: Array<String>): Result<Array<Array<String>>> {
        // ignore
        return Result(ERROR_UNKNOWN, ERROR_PROCESS_CAPTION_ARRAY_MSG, emptyArray())
    }

    /**
     * 删除caption的embedding数据
     * @param list 需要删除的图片列表
     * @return 删除图片的_id的list
     */
    @Synchronized
    override fun deleteEmbeddingDataByLocalMediaTableIds(list: List<CaptionImageInfo>): MutableList<Long> {
        val map = list.groupBy(
            CaptionImageInfo::embeddingVersion,
            CaptionImageInfo::mGalleryId
        )
        return embeddingAbilityImpl?.deleteDataFromEmbeddingTable(map) ?: mutableListOf()
    }

    /**
     * 算法接口版本，一般是算法sdk的版本号，用于与算法插件版本做兼容
     */
    override fun getApi(): Int {
        return getApi()
    }

    /**
     * 获取接口信息
     */
    override fun getDetectData(): DetectData {
        return captionClient.getDetectData()
    }

    /**
     * 获取接口名称
     */
    override fun getDetectName(): String {
        return captionClient.getDetectName()
    }

    /**
     * 客户端协议，等价于core sdk的版本号，用于与AIUnit版本做兼容
     */
    override fun getProtocol(): Int {
        return captionClient.getProtocol()
    }

    /**
     * 算法类型：0-端侧，1-云侧
     */
    override fun getRunningType(): Int {
        return captionClient.getRunningType()
    }

    /**
     * 对caption embedding内容的向量化召回
     */
    override fun searchByEmbedding(query: String?): SearchResult? {
        if (embeddingAbilityImpl == null) {
            GLog.e(TAG, DL) { "searchByEmbedding, embeddingAbilityImpl is null" }
            return null
        }
        val result = query?.let {
            embeddingAbilityImpl?.searchImage(query)
        }
        if (result == null) {
            GLog.d(TAG, DL) { "searchByEmbedding, result is null" }
            return null
        }
        GLog.d(TAG, DL) { "searchByEmbedding, query:$query, thread:$currentThreadInfo, original result:$result" }
        return result
    }

    /**
     * 对caption内容的关键字召回
     */
    override fun searchByText(query: String?): Cursor? {
        // ignore
        return null
    }

    companion object {
        const val TAG = "${SearchConst.TAG}CaptionAbilityImpl"

        /**
         * caption 推理次数
         */
        private const val CAPTION_RE_PREDICT = "re_predict"
        private const val ERROR_NONE = 0
        private const val ERROR_UNKNOWN = -1
        private const val ERROR_UNKNOWN_MSG = "processCaptions exceptions"
        private const val ERROR_PROCESS_CAPTION_ARRAY_MSG = "not use processCaptions array function"
    }
}