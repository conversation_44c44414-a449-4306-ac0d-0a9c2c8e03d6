/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  DmpTableChangeObserver.kt
 * * Description:  监听数据表变化
 * * Version: 1.0
 * * Date : 2025/01/09
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/01/09     1.0        create
 * *  pusong        2025/07/14     1.1        move
 ************************************************************/
package com.oplus.gallery.framework.abilities.search.dmpsync.observer

import android.content.ContentValues
import com.oplus.gallery.framework.abilities.search.dmpsync.processor.DmpTableChangeProcessor
import com.oplus.gallery.foundation.database.trigger.operator.IDataChangeObserver
import com.oplus.gallery.foundation.database.trigger.operator.OperationType
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 数据表变化监听者，用于在数据库变化时给DMP提供数据
 */
class DmpTableChangeObserver(private val processor: DmpTableChangeProcessor) : IDataChangeObserver {
    override fun onChanged(
        diffDataMap: MutableMap<OperationType, MutableList<Pair<ContentValues, ContentValues?>>>,
        immediately: Boolean
    ) {
        val isTemperatureAllow = TemperatureUtil.isTemperatureAllow(TEMPERATE_ALLOW_SYNC_DMP)
        val isBatteryAllow = isBatteryAllow()
        if (isTemperatureAllow && isBatteryAllow) {
            diffDataMap.forEach { (operationType, contentValuePairList) ->
                when (operationType) {
                    OperationType.INSERT_LOCAL -> processor.processInsert(contentValuePairList)
                    OperationType.DELETE_LOCAL -> processor.processDelete(contentValuePairList)
                    OperationType.UPDATE_LOCAL -> processor.processUpdate(contentValuePairList)
                }
            }
        } else {
            GLog.w(TAG, LogFlag.DL) { "onChanged, isTemperatureAllow: $isTemperatureAllow, isBatteryAllow:$isBatteryAllow" }
        }
    }

    /**
     * 电量是否允许同步，大于30%电量可同步
     */
    private fun isBatteryAllow(): Boolean {
        val batteryPercent = BatteryStatusUtil.getCurrentBatteryPercent(ContextGetter.context)
        GLog.d(TAG, LogFlag.DL) { "isBatteryAllow, current battery percent: $batteryPercent" }
        return batteryPercent >= BATTERY_ALLOW_SYNC_DMP
    }

    companion object {
        private const val TAG = "DmpTableChangeObserver"

        /**
         * 温度超过38度则不允许同步
         */
        const val TEMPERATE_ALLOW_SYNC_DMP = 38.0f

        /**
         * 电量低于30%则不允许同步
         */
        const val BATTERY_ALLOW_SYNC_DMP = 0.3f
    }
}