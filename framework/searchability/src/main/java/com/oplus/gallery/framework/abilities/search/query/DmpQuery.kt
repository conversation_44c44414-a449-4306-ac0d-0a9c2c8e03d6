/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DmpQuery.kt
 ** Description: DMP中子查询
 ** Version: 1.0
 ** Date: 2023/9/22
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/22      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import android.os.Bundle
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.config.allowlist.FolderNoteConfig
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns
import com.oplus.gallery.foundation.database.util.SQLGrammar
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.text.TextUtil.BLANK_STRING
import com.oplus.gallery.foundation.util.text.TextUtil.LEFT_SQUARE_BRACKETS
import com.oplus.gallery.foundation.util.text.TextUtil.RIGHT_SQUARE_BRACKETS
import com.oplus.gallery.foundation.util.text.TextUtil.SPLIT_COMMA_SEPARATOR
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 提供给DMP中子查询相册数据
 *
 * @property labelDataTaskFuture 标签数据加载任务的结果
 * @property projection projection 查询的列
 * @property queryArgs queryArgs 查询参数
 */
class DmpQuery(
    private val projection: Array<out String>?,
    private val queryArgs: Bundle?
) : Query {

    /**
     * 获取搜索信息（中子调用）
     *
     * Uri: `content://com.open.gallery.smart.provider/searchInfo`
     * project : null
     * selection : null
     * args 中的字段描述:
     *  | 字段名        |    类型     |     描述     |
     *  | ------------- | :---------: | :----------: |
     *  | selection     |    TEXT     |   查询条件   |
     *  | selectionArgs | STRINGARRAY | 查询条件参数 |
     *  | limit         |    TEXT     |   限制条目   |
     *
     * 返回 cursor 中的字段描述：
     *
     * | 字段名        |  类型   |        描述        |       备注        |
     * | ------------- | :-----: | :----------------: | :---------------: |
     * | _id           | Integer |    相册数据库id    |                   |
     * | media_id      | Integer |       媒体id       |                   |
     * | path          | String  |    文件绝对路径    |                   |
     * | datetaken     |  Long   |    文件创建时间    |                   |
     * | date_modified |  Long   |  文件最新修改时间  |                   |
     * | face_name     | String  |      人物名称      | (splited by "\|") |
     * | address       | String  |        地点        | (splited by "\|") |
     * | tags          | String  |      标签名字符串   | (splited by "\|") |
     * | english_tags  | String  |   英文标签名字符串   | (splited by "\|") |
     * | ocr           | String  |     OCR字符串      |                   |
     * | bucket_name   | String  |      图集名称      |                   |
     * | bucket_id     |  Long   |      文件夹id      |                   |
     * | _display_name | String  | 文件名称（带后缀） |                   |
     * | title         | String  |      文件名称      |                   |
     * | _size         |  Long   |      文件大小      |                   |
     * | media_type    | Integer |      媒体类型      |                   |
     * | mime_type     | String  |      文件类型      |                   |
     * | duration      | Integer |      视频时长      |                   |
     * | longitude     | Double  |        经度        |                   |
     * | latitude      | Double  |        纬度        |                   |
     * | festival_name | String  |      节日名称      | (splited by "\|") |
     * | memory_name   | String  |      回忆名称      | (splited by "\|") |
     * | sex           | String |      性别          | (splited by "\|") |
     * | age           | String |      年龄          | (splited by "\|") |
     * | tagflags      | Integer |      拓展字段      |                   |
     *
     */
    @Suppress("LongMethod")
    override fun query(): Cursor? {
        var selection: String? = null
        var selectionArgs: Array<String?>? = null
        var limit: String? = null
        var order: String? = null
        var lastSyncTime = 0L
        queryArgs?.let {
            selection = it.getString(SELECTION)
            selectionArgs = it.getStringArray(SELECTION_ARGS)
            limit = it.getString(LIMIT)
            order = it.getString(ORDER)
            lastSyncTime = it.getLong(LAST_SYNC_TIME)
        }
        GLog.d(TAG, LogFlag.DL) { "dmp query parm: lastSyncTime:$lastSyncTime, limit:$limit, order:$order, " +
                "selection:$selection, selectionArgs:$selectionArgs" }
        if ((lastSyncTime != 0L) && (selection != null)) {
            GLog.e(TAG, LogFlag.DL) { "dmp query parm is illegal" }
            return null
        }
        // 1.数据变化同步，此时以select为准，limit不参与限制
        if (selection != null) {
            return querySearchInfoForDmp(selection, selectionArgs, limit, order)
        }
        // 2.每日任务，需要limit限制数量
        val limitNum = limit?.split(SPLIT_COMMA_SEPARATOR)?.last() ?: DEFAULT_QUERY_SIZE
        val limitStart = limit?.split(SPLIT_COMMA_SEPARATOR)?.first()?.toInt() ?: 0
        if (limitStart == 0) {
            DBSearchUtils.setCursorId(0L)
        }
        // 新逻辑，使用最近同步时间戳增量查询
        if (lastSyncTime != 0L) {
            selection = queryNeedSyncData(lastSyncTime, limit, order)
            if ((selection == null) || (selection?.isEmpty() == true)) {
                GLog.d(TAG, LogFlag.DL) { "dmp query result is empty" }
                return MatrixCursor(emptyArray())
            }
            return querySearchInfoForDmp(selection, selectionArgs, limitNum, order)
        }
        // 兼容旧逻辑的每日任务
        return querySearchInfoForDmp(selection, selectionArgs, limitNum, order)
    }

    /**
     * dmp增量查询
     */
    private fun queryNeedSyncData(lastSyncTime: Long, limit: String?, order: String?): String? {
        val startTime = System.currentTimeMillis()
        var size = 0
        val result = StringBuilder()
        val whereClause = GalleryColumns.LocalColumns.GENERATION_MODIFIED + SQLGrammar.GREATER_THAN_OR_EQUAL_TO + lastSyncTime +
                SQLGrammar.AND + GalleryColumns.LocalColumns.INVALID + SQLGrammar.EQUAL_ZERO +
                SQLGrammar.AND + GalleryColumns.LocalColumns.IS_TRASHED + SQLGrammar.EQUAL_ZERO
        runCatching {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(arrayOf(GalleryColumns.LocalColumns.MEDIA_ID))
                .setWhere(whereClause)
                .setConvert(CursorConvert())
                .setLimit(limit)
                .setOrderBy(order)
                .build().exec()?.use {
                    size = it.count
                    if (size <= 0) {
                        GLog.d(TAG, LogFlag.DL) { "queryNeedSyncData, cost time:${GLog.getTime(startTime)}, size:$size" }
                        return null
                    }
                    result.append((GalleryColumns.LocalColumns.MEDIA_ID + SQLGrammar.IN + SQLGrammar.LEFT_BRACKETS))
                    val mediaIdIdx = it.getColumnIndex(GalleryColumns.LocalColumns.MEDIA_ID)
                    while (it.moveToNext()) {
                        result.append(("${it.getLong(mediaIdIdx)}${SQLGrammar.COMMA}"))
                    }
                    if (-1 != result.lastIndexOf(SQLGrammar.COMMA)) {
                        result.deleteCharAt(result.lastIndexOf(SQLGrammar.COMMA))
                    }
                    result.append(SQLGrammar.RIGHT_BRACKETS)
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "queryNeedSyncData onFailure" }
        }
        GLog.d(TAG, LogFlag.DL) { "queryNeedSyncData, cost time:${GLog.getTime(startTime)}, size:$size" }
        return result.toString()
    }

    /**
     * 查询dmp所需信息
     */
    private fun querySearchInfoForDmp(selection: String?, selectionArgs: Array<String?>?, limit: String?, order: String?): Cursor? {
        val startTime = System.currentTimeMillis()
        kotlin.runCatching {
            AppScope.launch(Dispatchers.IO) {
                LabelSearchEngine.getInstance().loadEnglishDicIfNeed(ContextGetter.context)
            }
            DBSearchUtils.getSearchInfoRawQueryReq(selection, selectionArgs, limit, order).use { cursor ->
                if ((cursor == null) || (cursor.columnNames == null)) {
                    GLog.w(TAG, LogFlag.DL) { "querySearchInfoForDmp, cursor or columnNames is null" }
                    return null
                }
                val columnNames = cursor.columnNames
                val matrixCursor = MatrixCursor(columnNames)
                val tagsIndex = cursor.getColumnIndex(GalleryStore.SearchInfoColumns.TAGS)
                val englishTagsIndex = cursor.getColumnIndex(GalleryStore.SearchInfoColumns.ENGLISH_TAGS)
                val bucketIdIndex = cursor.getColumnIndex(GalleryStore.SearchInfoColumns.BUCKET_ID)
                val bucketNameIndex = cursor.getColumnIndex(GalleryStore.SearchInfoColumns.BUCKET_NAME)
                val latitudeIndex = cursor.getColumnIndex(GalleryStore.SearchInfoColumns.LATITUDE)
                val longitudeIndex = cursor.getColumnIndex(GalleryStore.SearchInfoColumns.LONGITUDE)
                val dataIndex = cursor.getColumnIndex(GalleryStore.SearchInfoColumns.DATA)
                while (cursor.moveToNext()) {
                    val objects = arrayOfNulls<Any>(columnNames.size)
                    for (projectionIndex in columnNames.indices) {
                        when (projectionIndex) {
                            tagsIndex -> {
                                val tags = cursor.getString(projectionIndex)
                                mappingTags(objects, projectionIndex, tags)
                            }
                            englishTagsIndex -> {
                                val tags = cursor.getString(projectionIndex)
                                mappingEnglishTags(objects, projectionIndex, tags)
                            }
                            bucketNameIndex -> {
                                val bucketId = cursor.getInt(bucketIdIndex)
                                val bucketName = cursor.getString(projectionIndex)
                                mappingNoteName(bucketName, objects, projectionIndex, bucketId)
                            }
                            latitudeIndex, longitudeIndex -> objects[projectionIndex] = cursor.getDouble(projectionIndex)
                            else -> objects[projectionIndex] = cursor.getString(projectionIndex)
                        }
                    }
                    if (DEBUG_SEARCH) {
                        GLog.d(TAG, LogFlag.DL) { "querySearchInfoForDmp, objects: " +
                                toStringWithoutIndex(objects, dataIndex, latitudeIndex, longitudeIndex, bucketNameIndex)
                        }
                    }
                    matrixCursor.addRow(objects)
                }
                GLog.d(TAG, LogFlag.DL) { "querySearchInfoForDmp, cost time:${GLog.getTime(startTime)}, size:${matrixCursor.count}" }
                return matrixCursor
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "querySearchInfoForDmp onFailure $it" }
        }
        return null
    }

    private fun mappingNoteName(bucketName: String, objects: Array<Any?>, projectionIndex: Int, bucketId: Int) {
        val startTime = System.currentTimeMillis()
        val noteName = FolderNoteConfig.getInstance().getNoteName(bucketId.toLong())
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[mappingNoteName], getNoteName costTime:${GLog.getTime(startTime)}")
        }
        if (noteName != null) {
            objects[projectionIndex] = noteName
        } else {
            objects[projectionIndex] = bucketName
        }
    }

    private fun mappingTags(objects: Array<Any?>, projectionIndex: Int, tags: String?) {
        val startTime = System.currentTimeMillis()
        // 标签数据加载有结果
        if (tags != null) {
            val tagsSplit = tags.split("\\|".toRegex()).toTypedArray()
            val stringBuilder = StringBuilder()
            for (sceneId in tagsSplit) {
                val labelName = LabelDictionary.getLabelName(sceneId.toInt())
                if (!TextUtils.isEmpty(labelName)) {
                    stringBuilder.append(labelName)
                    stringBuilder.append(VERTICAL_LINE)
                }
            }
            val lastIndex = stringBuilder.lastIndexOf(VERTICAL_LINE)
            if (lastIndex != -1) {
                stringBuilder.deleteCharAt(lastIndex)
            }
            objects[projectionIndex] = stringBuilder.toString()
        }
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[mappingTags], getTags cost time:" + GLog.getTime(startTime))
        }
    }

    /**
     * tags转换成英文标签名字符串
     * @param objects 结果填充到此数组中
     * @param projectionIndex 结果填充到此下标中
     * @param tags 标签id字符串
     */
    private fun mappingEnglishTags(objects: Array<Any?>, projectionIndex: Int, tags: String?) {
        val startTime = System.currentTimeMillis()
        // 标签数据加载有结果
        if (tags != null) {
            val tagsSplit = tags.split("\\|".toRegex()).toTypedArray()
            val stringBuilder = StringBuilder()
            for (sceneId in tagsSplit) {
                val labelName = LabelDictionary.getEnglishLabelName(sceneId.toInt())
                if (!TextUtils.isEmpty(labelName)) {
                    stringBuilder.append(labelName)
                    stringBuilder.append(VERTICAL_LINE)
                }
            }
            val lastIndex = stringBuilder.lastIndexOf(VERTICAL_LINE)
            if (lastIndex != -1) {
                stringBuilder.deleteCharAt(lastIndex)
            }
            objects[projectionIndex] = stringBuilder.toString()
        }
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[mappingEnglishTags], getTags cost time:" + GLog.getTime(startTime))
        }
    }

    private fun toStringWithoutIndex(
        array: Array<Any?>?,
        dataIndex: Int,
        latitudeIndex: Int,
        longitudeIndex: Int,
        bucketNameIndex: Int
    ): String {
        if (array == null) {
            return TextUtil.NULL_STRING
        }
        val iMax = array.size - 1
        if (iMax == -1) {
            return LEFT_SQUARE_BRACKETS + RIGHT_SQUARE_BRACKETS
        }
        val stringBuilder = StringBuilder()
        stringBuilder.append(LEFT_SQUARE_BRACKETS)
        var index = 0
        while (true) {
            if ((index == dataIndex) || (index == latitudeIndex) || (index == longitudeIndex) || (index == bucketNameIndex)) {
                index++
                continue
            }
            stringBuilder.append(array[index])
            if (index == iMax) {
                return stringBuilder.append(RIGHT_SQUARE_BRACKETS).toString()
            }
            stringBuilder.append(SPLIT_COMMA_SEPARATOR).append(BLANK_STRING)
            index++
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}DmpQuery"

        private const val SELECTION = "selection"
        private const val SELECTION_ARGS = "selectionArgs"
        private const val LIMIT = "limit"
        private const val ORDER = "order"
        private const val LAST_SYNC_TIME = "lastSyncTime"
        private const val DEFAULT_QUERY_SIZE = "500"
        private const val VERTICAL_LINE = "|"
    }
}