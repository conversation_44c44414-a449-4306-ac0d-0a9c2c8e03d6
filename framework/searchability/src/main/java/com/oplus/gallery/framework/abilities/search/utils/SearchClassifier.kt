/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SearchClassifier.kt
 * Description: SearchClassifier
 * Version: 1.0
 * Date: 2023/8/7
 * Author: W9009912
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * W9009912      2023/8/7      1.0              create file
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.search.utils

import android.content.Context
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchType.TYPE_DATETIME
import com.oplus.gallery.framework.abilities.search.SearchType.TYPE_GUIDE_LABEL
import com.oplus.gallery.framework.abilities.search.SearchType.TYPE_LABEL
import com.oplus.gallery.framework.abilities.search.SearchType.TYPE_LOCATION
import com.oplus.gallery.framework.abilities.search.cache.KeywordCache
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 搜索分类
 */
class SearchClassifier(context: Context?) {
    private val isChinese: Boolean

    var normalizedKeywords: Array<String>? = null
        get() = field?.clone() ?: arrayOf()

    val locationFilter: LocationFilter

    @JvmField
    val dateTimeSelector: DateTimeSelector

    init {
        locationFilter = LocationFilter(context)
        dateTimeSelector = DateTimeSelector(context)
        isChinese = LocaleUtils.isChinese(ContextGetter.context)
    }

    fun setKeywordCache(keywordCache: KeywordCache?) {
        dateTimeSelector.setKeywordCache(keywordCache)
    }

    fun classify(keywords: String, type: Int) {
        normalizedKeywords =
            when (type) {
                TYPE_DATETIME, TYPE_LOCATION, TYPE_LABEL, TYPE_GUIDE_LABEL -> arrayOf(keywords)
                else -> {
                    if (LocaleUtils.isChinaMainland(ContextGetter.context)) {
                        normalize(keywords)
                    } else {
                        arrayOf(keywords)
                    }
                }
            }
    }

    fun getShootDateTime(keyword: String?): ShootDateTime? {
        return dateTimeSelector.getShootDateTimeFromSelector(keyword)
    }

    /**
     * 模糊匹配日期
     */
    fun completeDateTime(keyword: String?): Set<String> {
        return dateTimeSelector.completeDateTimeFromSelector(keyword)
    }

    fun contains(original: String, target: String?): Boolean {
        return if (isChinese) {
            original.contains(target!!)
        } else {
            original.startsWith(target!!)
        }
    }

    fun equals(original: String, target: String?): Boolean {
        return original.equals(target, ignoreCase = true)
    }

    private fun normalize(keywords: String): Array<String> {
        // classify keyword.
        val normKeywordArray = keywords.split(SearchCommonUtils.SPACE_REGEX.toRegex()).toTypedArray()
        val uniqueNormKeywords: MutableList<String> = ArrayList()
        if (normKeywordArray.isNotEmpty()) {
            for (normKeyword in normKeywordArray) {
                if (!uniqueNormKeywords.contains(normKeyword)) {
                    uniqueNormKeywords.add(normKeyword)
                }
            }
            if (!uniqueNormKeywords.contains(keywords) && !keywords.contains(" ")) {
                uniqueNormKeywords.add(keywords)
            }
        }
        // get first keyword for search
        val fKeywordBuffer = StringBuffer()
        if (uniqueNormKeywords.size > 0) {
            fKeywordBuffer.append(uniqueNormKeywords[0])
            uniqueNormKeywords.removeAt(0)
        }
        // get second keyword for search
        val sKeywordBuffer = StringBuffer()
        var i = 0
        val n = uniqueNormKeywords.size
        while (i < n) {
            sKeywordBuffer.append(uniqueNormKeywords[i])
            sKeywordBuffer.append(TextUtil.BLANK_STRING)
            i++
        }
        val lastIndex = sKeywordBuffer.lastIndexOf(TextUtil.BLANK_STRING)
        if (lastIndex != -1) {
            sKeywordBuffer.deleteCharAt(lastIndex)
        }
        // combination keyword for search
        uniqueNormKeywords.clear()
        val fKeyword = fKeywordBuffer.toString()
        if (!TextUtils.isEmpty(fKeyword)) {
            uniqueNormKeywords.add(fKeyword)
        }
        val sKeyword = sKeywordBuffer.toString()
        if (!TextUtils.isEmpty(sKeyword)) {
            uniqueNormKeywords.add(sKeyword)
        }
        return uniqueNormKeywords.toTypedArray()
    }
}