/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelGraphSearchUtil.kt
 ** Description: 视觉知识图谱标签查询工具类
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/2/9
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery          2023/2/9     1.0         init
 ** <EMAIL>          2023/7/10    2.0        将业务代码拆分出去，只保留图谱查询的能力
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.search.cvgraph.db

import android.annotation.SuppressLint
import net.sqlcipher.database.SQLiteDatabase
import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelBean
import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelGraphBean
import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelQueryEntry
import com.oplus.gallery.framework.abilities.search.cvgraph.enum.RelationType
import com.oplus.gallery.foundation.tracing.constant.LabelTrackTypeConstant
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 视觉知识图谱标签查询工具类
 */
object LabelGraphSearchUtil {
    private const val TAG: String = "LabelGraphSearchUtil"

    /**
     * tag表名
     * 标签存储在数据库tag表
     * 用一个二元组（tag_id,tag）表示
     */
    private const val TABLE_NAME_TAG = "tag"

    /**
     * tag_graph表名
     * 标签关系存储在数据表tag_graph
     * 用一个三元组(word_a，word_b，relation_type)表示一条边（关系）
     * relation_type代表两个节点的关系类型
     */
    private const val TABLE_NAME_TAG_GRAPH = "tag_graph"

    /**
     * tag表中的tag字段
     */
    private const val VALUE_TAG = "tag"

    /**
     * tag表中的tag_id字段
     */
    private const val VALUE_TAG_ID = "tag_id"

    /**
     * tag_graph表中的word_a字段，实质上是标签
     */
    private const val VALUE_WORD_A = "word_a"

    /**
     * tag_graph表中的word_b字段，对应标签（word_a）的关系词
     */
    private const val VALUE_WORD_B = "word_b"

    /**
     * tag_graph表中的relation_type字段，表明word_b是word_a的对应关系
     */
    private const val VALUE_RELATION_TYPE = "relation_type"


    /**
     * 查询对应标签，如果标签查询不到再通过图谱查询同义词，再转换为标签
     * 用户输入的文本查询后的标签、同义词、非正式同义词最终指向的只能是唯一的一个标签
     * */
    @JvmStatic
    fun queryLabelOrSynonymWord(database: SQLiteDatabase?, labelQueryEntry: LabelQueryEntry) {
        if (labelQueryEntry.word.isEmpty()) {
            GLog.e(TAG, LogFlag.DL, "queryLabelOrSynonymWord, LabelQueryEntry.word is empty")
            return
        }
        /**
         * 1.先查询tab表中的标签
         * 2.然后标签无法直接命中再查询同义词
         * 3.同义词无法直接命中再查询非正式同义词
         * 注意：在查询同义词或非正式同义词有结果后，需要将用户输入的word赋值给displayName字段，
         * 上层展示的时候需要用到，而且一个文本的同义词指向只能存在一个标签，所以同义词查询其实只会命中一次
         * exp：用户输入入场券，查询到同义词是门票，显示给用户的还得是入场券，
         * 但是我们实际查询图片是用的门票的labelId
         */
        val labelBean = queryLabelBeanByLabelName(database, labelQueryEntry.word) ?: let {
            GLog.d(TAG, LogFlag.DL, "queryLabelOrSynonymWord, queryLabelBeanByLabelName is empty")
            // 如果查询不到，再尝试查询tag_graph表中的同义词标签
            queryLabelBeansByRelationType(database, labelQueryEntry.word, RelationType.SYNONYM_TYPE).firstOrNull()?.apply {
                this.displayName = labelQueryEntry.word
            }
        } ?: let {
            GLog.d(TAG, LogFlag.DL, "queryLabelOrSynonymWord, queryLabelBeanBy SYNONYM_TYPE is empty")
            // 如果还查询不到，再尝试查询查询tag_graph表中的非正式同义词
            queryLabelBeansByRelationType(database, labelQueryEntry.word, RelationType.INFORMALITY_SYNONYM_TYPE).firstOrNull()?.apply {
                this.displayName = labelQueryEntry.word
            }
        }
        labelBean?.apply {
            labelQueryEntry.addQueryLabel(LabelQueryEntry.QueryType.LABEL_WORD, mutableListOf(this))
            GLog.d(TAG, LogFlag.DL, "queryLabelOrSynonymWord, query success. label: $this")
        }
        GLog.d(TAG, LogFlag.DL, "queryLabelOrSynonymWord, query end.")
    }

    /**
     * 查询补全词，注意tag_graph表中并没有type为2的补全词，是通过%占位符号去查询SYNONYM_TYPE类型的
     * 查到的是同义词，需要用a去查id，但是显示还是要显示b
     * 注意，补全是用用户输入的文本去补全的，不能用匹配到的标签或同义词去补全
     * */
    @JvmStatic
    fun queryCompletionWord(database: SQLiteDatabase?, labelQueryEntry: LabelQueryEntry, type: RelationType) {
        if (labelQueryEntry.word.isEmpty()) {
            GLog.e(TAG, LogFlag.DL, "queryCompletionWord, LabelQueryEntry.word is empty")
            return
        }
        GLog.d(TAG, LogFlag.DL, "queryCompletionWord Start query completion label. queryWord：${labelQueryEntry.word}")
        // 先模糊查询tag表
        val labels = queryCompletionLabelBeansByFuzzy(database, labelQueryEntry.word)
        GLog.d(TAG, LogFlag.DL, "queryCompletionWord End of query completion label. size：${labels.size}")
        // 然后模糊查询tag_graph表
        val labelGraphBeans = queryCompletionLabelGraphBeansByFuzzy(database, labelQueryEntry.word)
        GLog.d(TAG, LogFlag.DL, "queryCompletionWord End of query completion label graph. size：${labelGraphBeans.size}")
        // 将查询到的labelGraphBeans添加到labels
        labelGraphBeans.forEach { labelGraph ->
            //再将wordA转换成LabelBean
            queryLabelBeanByLabelName(database, labelGraph.wordA)?.also { label ->
                label.displayName = labelGraph.wordB
                if (type == RelationType.CORRECTING_TYPE) {
                    label.labelOriginValue = LabelTrackTypeConstant.CORRECTING_COMPLETION_VALUE
                } else if (type == RelationType.SYNONYM_TYPE) {
                    label.labelOriginValue = LabelTrackTypeConstant.SYNONYM_COMPLETION_VALUE
                }
                labels.add(label)
            }
        }
        if (labels.isNotEmpty()) {
            // labels先自身去重
            val resultList = labels.distinctBy {
                it.labelId
            }.toMutableList()
            GLog.d(TAG, LogFlag.DL, "queryCompletionWord End of distinct of labels. size：${labels.size}")
            // 如果标签和同义词能查到，没必要再次补全
            labelQueryEntry.labelBeans.firstOrNull()?.apply {
                resultList.remove(LabelBean(labelId, label))
            }
            GLog.d(TAG, LogFlag.DL, "queryCompletionWord End of distinct of labelBeans. size：${labels.size}")
            labelQueryEntry.addQueryLabel(LabelQueryEntry.QueryType.COMPLETION_WORD, resultList)
        }
        GLog.d(TAG, LogFlag.DL, "queryCompletionWord End of query all completion label. completionLabelBeans：${labelQueryEntry.completionLabelBeans}")
    }

    /**
     * 查询纠错词，后面需要用纠错后的词去查询补全、近义等等后续操作
     * */
    @JvmStatic
    fun queryCorrectingWord(database: SQLiteDatabase?, labelQueryEntry: LabelQueryEntry) {
        if (labelQueryEntry.word.isEmpty()) {
            GLog.e(TAG, LogFlag.DL, "queryCorrectingWord, LabelQueryEntry.word is empty")
            return
        }
        GLog.d(TAG, LogFlag.DL, "Start correcting label. query word：${labelQueryEntry.word}")
        val labels = queryLabelBeansByRelationType(database, labelQueryEntry.word, RelationType.CORRECTING_TYPE)
        labels.firstOrNull()?.apply {
            // 纠错词也只有一个
            labelOriginValue = getTrackLabelTypeByRelationType(RelationType.CORRECTING_TYPE)
            labelQueryEntry.addQueryLabel(LabelQueryEntry.QueryType.LABEL_WORD, mutableListOf(this))
            // 纠错词查询成功后，需要变更用户输入的文本内容，方便后期做补全、猜你想搜、近义词、上位词的查询
            labelQueryEntry.word = this.label
        }
        GLog.d(TAG, LogFlag.DL, "queryCorrectingWord End of query correcting label. labelQuery：$labelQueryEntry")
    }

    /**
     * 查询可能想搜词, 近义词, 上位词，下位词
     * 这边查询会把图谱中可能覆盖的关系都查出来，不做数量限制
     * */
    @JvmStatic
    fun queryOtherLabelBeans(database: SQLiteDatabase?, query: LabelQueryEntry) {
        if (query.word.isEmpty()) {
            GLog.e(TAG, LogFlag.DL, "queryOtherLabelBeans, LabelQueryEntry.word is empty")
            return
        }
        val labels = mutableListOf<LabelBean>()
        // 可能想搜 type 6
        queryLabelBeansByRelationType(database, query.word, RelationType.POSSIBLE_TYPE).also { queryLabels ->
            if (queryLabels.isNotEmpty()) {
                labels.addAll(queryLabels)
                GLog.d(TAG, LogFlag.DL, "queryOtherLabelBeans Query possible label success. $queryLabels")
            }
        }
        // 近义词 type 3
        queryLabelBeansByRelationType(database, query.word, RelationType.NEAR_TYPE).also { queryLabels ->
            if (queryLabels.isNotEmpty()) {
                labels.addAll(queryLabels)
                GLog.d(TAG, LogFlag.DL, "queryOtherLabelBeans Query near label success. $queryLabels")
            }
        }
        // 查询上位词，为了统一查询方式（查b出a），所以直接查询下位词，即b是a的下位词， type = 5，查出来的a就是我们需要的上位词
        queryLabelBeansByRelationType(database, query.word, RelationType.DOWN_TYPE).also { queryLabels ->
            if (queryLabels.isNotEmpty()) {
                labels.addAll(queryLabels)
                GLog.d(TAG, LogFlag.DL, "queryOtherLabelBeans Query up label success. $queryLabels")
            }
        }
        // 包装数据
        query.addQueryLabel(
            LabelQueryEntry.QueryType.OTHER_WORD,
            // 去重
            labels.distinctBy { it.labelId } as MutableList<LabelBean>
        )
    }

    /**
     * 通过labelName查询label表的所有Label
     * @param labelName 标签名
     * */
    @JvmStatic
    fun queryLabelBeanByLabelName(database: SQLiteDatabase?, labelName: String): LabelBean? {
        var labelBean: LabelBean? = null
        kotlin.runCatching {
            val selectionArgs = arrayOf(labelName)
            val rawQuerySql = "SELECT $VALUE_TAG_ID, $VALUE_TAG FROM $TABLE_NAME_TAG WHERE $VALUE_TAG = ? COLLATE NOCASE"
            database?.rawQuery(rawQuerySql, selectionArgs)?.use { cursor ->
                if ((cursor.count > 0) && cursor.moveToFirst()) {
                    @SuppressLint("Range") val labelId =
                        cursor.getInt(cursor.getColumnIndex(VALUE_TAG_ID))
                    @SuppressLint("Range") val labelTag =
                        cursor.getString(cursor.getColumnIndex(VALUE_TAG))
                    labelBean = LabelBean(labelId, labelTag)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryLabelBeanByLabelName, error ", it)
        }
        if (DEBUG_SEARCH) {
            GLog.d(TAG, LogFlag.DL, "queryLabelBeanByLabelName tag table query result: $labelBean")
        }
        return labelBean
    }

    /**
     * 通过标签id查询label
     * @param labelId 标签id
     * */
    @JvmStatic
    fun queryLabelBeanByLabelId(database: SQLiteDatabase?, labelId: Int): LabelBean? {
        var labelBean: LabelBean? = null
        kotlin.runCatching {
            val selectionArgs = arrayOf("$labelId")
            val rawQuerySql = "SELECT $VALUE_TAG FROM $TABLE_NAME_TAG WHERE $VALUE_TAG_ID = ?"
            database?.rawQuery(rawQuerySql, selectionArgs)?.use { cursor ->
                if ((cursor.count > 0) && cursor.moveToFirst()) {
                    @SuppressLint("Range") val labelName =
                        cursor.getString(cursor.getColumnIndex(VALUE_TAG))
                    labelBean = LabelBean(labelId, labelName)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryLabelBeanByLabelId, error ", it)
        }
        if (DEBUG_SEARCH) {
            GLog.d(TAG, LogFlag.DL, "queryLabelBeanByLabelId tag table query result: $labelBean")
        }
        return labelBean
    }

    /**
     * 通过word_b 精确查询到所有word_a
     * @param wordB 图谱中对应word_b
     * @param type 类型见RelationType
     * */
    @JvmStatic
    private fun queryLabelGraph(database: SQLiteDatabase?, wordB: String, type: RelationType): MutableList<LabelGraphBean> {
        val labelGraphBeans = mutableListOf<LabelGraphBean>()
        kotlin.runCatching {
            val selectionArgs = arrayOf(wordB, type.value)
            val rawQuerySql =
                "SELECT $VALUE_WORD_A FROM $TABLE_NAME_TAG_GRAPH WHERE $VALUE_WORD_B = ? AND $VALUE_RELATION_TYPE = ?"
            database?.rawQuery(rawQuerySql, selectionArgs)?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val wordAIndex = it.getColumnIndex(VALUE_WORD_A)
                    do {
                        @SuppressLint("Range") val wordA =
                            it.getString(wordAIndex)
                        val labelGraph = LabelGraphBean(wordA, wordB, type.value)
                        if (!labelGraphBeans.contains(labelGraph)) {
                            labelGraphBeans.add(labelGraph)
                        }
                    } while (it.moveToNext())
                    GLog.d(
                        TAG, "queryLabelGraph tag_graph table query result word_b: $wordB relation_type: ${type.value} "
                                + "labelGraphBeans: $labelGraphBeans"
                    )
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryLabelGraph, error ", it)
        }
        return labelGraphBeans
    }

    /**
     * 通过keyword以及词性的关系，查询tag_graph表，然后再通过tag表转换成id
     * @param keyword 查询的词
     * @param type 查询的类型
     * */
    @JvmStatic
    private fun queryLabelBeansByRelationType(database: SQLiteDatabase?, keyword: String, type: RelationType): MutableList<LabelBean> {
        val labels = mutableListOf<LabelBean>()
        val labelGraphBeans = queryLabelGraph(database, keyword, type)
        // 然后去tag表查询id
        if (labelGraphBeans.isNotEmpty()) {
            labelGraphBeans.forEach { labelGraphBean ->
                queryLabelBeanByLabelName(database, labelGraphBean.wordA)?.also { label ->
                    label.labelOriginValue = getTrackLabelTypeByRelationType(type)
                    labels.add(label)
                }
            }
        }
        return labels
    }

    /**
     * 获取标签词类型
     */
    @JvmStatic
    private fun getTrackLabelTypeByRelationType(type: RelationType): Int {
        return when (type) {
            RelationType.POSSIBLE_TYPE -> LabelTrackTypeConstant.POSSIBLE_VALUE
            RelationType.NEAR_TYPE -> LabelTrackTypeConstant.NEAR_VALUE
            RelationType.UP_TYPE -> LabelTrackTypeConstant.UP_VALUE
            RelationType.DOWN_TYPE -> LabelTrackTypeConstant.DOWN_VALUE
            RelationType.CORRECTING_TYPE -> LabelTrackTypeConstant.CORRECTING_VALUE
            RelationType.COMPLETION_TYPE -> LabelTrackTypeConstant.COMPLETION_VALUE
            RelationType.SYNONYM_TYPE -> LabelTrackTypeConstant.SYNONYM_VALUE
            RelationType.INFORMALITY_SYNONYM_TYPE -> LabelTrackTypeConstant.INFORMALITY_SYNONYM_VALUE
            else -> LabelTrackTypeConstant.LABEL_VALUE
        }
    }

    /**
     * 模糊查询tag表补全词
     * */
    @JvmStatic
    private fun queryCompletionLabelBeansByFuzzy(database: SQLiteDatabase?, queryWord: String): MutableList<LabelBean> {
        val labelBeanList = mutableListOf<LabelBean>()
        kotlin.runCatching {
            val selectionArgs = arrayOf("%${escapeStr(queryWord)}%", escapeStr(queryWord))
            val rawQuerySqlForLabel = "SELECT $VALUE_TAG_ID, $VALUE_TAG FROM $TABLE_NAME_TAG WHERE $VALUE_TAG " +
                    "LIKE ? AND $VALUE_TAG != ?"
            database?.rawQuery(rawQuerySqlForLabel, selectionArgs)?.use { cursor ->
                if (cursor.count > 0 && cursor.moveToFirst()) {
                    val tagIdIndex = cursor.getColumnIndex(VALUE_TAG_ID)
                    val tagIndex = cursor.getColumnIndex(VALUE_TAG)
                    do {
                        @SuppressLint("Range") val labelId =
                            cursor.getInt(tagIdIndex)
                        @SuppressLint("Range") val label =
                            cursor.getString(tagIndex)
                        val labelBean = LabelBean(labelId, label)
                        labelBean.labelOriginValue = LabelTrackTypeConstant.COMPLETION_VALUE
                        labelBeanList.add(labelBean)
                    } while (cursor.moveToNext())
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryLabelBeansByFuzzy, error", it)
        }
        return labelBeanList
    }

    /**
     * 模糊查询tag_graph表同义词的补全词
     * 注意：用户输入的词本身可能是标签，在补全词查询的时候需要添加WORD_A字段的过滤
     * */
    @JvmStatic
    private fun queryCompletionLabelGraphBeansByFuzzy(database: SQLiteDatabase?, queryWord: String): MutableList<LabelGraphBean> {
        val labelGraphList = mutableListOf<LabelGraphBean>()
        kotlin.runCatching {
            val selectionArgs = arrayOf("%${escapeStr(queryWord)}%", escapeStr(queryWord))
            val rawQuerySqlForLabelGraph = "SELECT $VALUE_WORD_A, $VALUE_WORD_B, $VALUE_RELATION_TYPE " +
                    "FROM $TABLE_NAME_TAG_GRAPH WHERE $VALUE_WORD_B LIKE ? " +
                    "AND $VALUE_WORD_A != ? " +
                    "AND $VALUE_RELATION_TYPE = ${RelationType.SYNONYM_TYPE.value}"
            database?.rawQuery(rawQuerySqlForLabelGraph, selectionArgs)?.use { cursor ->
                if (cursor.count > 0 && cursor.moveToFirst()) {
                    val wordAIndex = cursor.getColumnIndex(VALUE_WORD_A)
                    val wordBIndex = cursor.getColumnIndex(VALUE_WORD_B)
                    val relationTypeIndex = cursor.getColumnIndex(VALUE_RELATION_TYPE)
                    do {
                        @SuppressLint("Range") val wordA = cursor.getString(wordAIndex)
                        @SuppressLint("Range") val wordB = cursor.getString(wordBIndex)
                        @SuppressLint("Range") val relationType = cursor.getInt(relationTypeIndex)
                        val labelGraph = LabelGraphBean(wordA, wordB, relationType)
                        if (!labelGraphList.contains(labelGraph)) {
                            labelGraphList.add(labelGraph)
                        }
                    } while (cursor.moveToNext())
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryLabelGraphBeansByFuzzy, error ", it)
        }
        return labelGraphList
    }

    /**
     * 查询tab表中的所有的标签以及tag_graph表中的所有同义词
     * 注意在tag_graph表中，所有关系为b是a的XX关系，其中a为标签，所以同义词应该查询出所有word_b
     * @return String标签数组字符串
     * */
    @JvmStatic
    fun queryAllLabel(database: SQLiteDatabase?): MutableSet<String> {
        val tags = mutableSetOf<String>()
        kotlin.runCatching {
            database?.rawQuery(
                "SELECT $VALUE_TAG FROM $TABLE_NAME_TAG" +
                        " WHERE $VALUE_TAG_ID > 0",
                null
            )?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val tagIndex = it.getColumnIndex(VALUE_TAG)
                    do {
                        @SuppressLint("Range") val tag =
                            it.getString(tagIndex)
                        tags.add(tag)
                    } while (it.moveToNext())
                }
            }
            database?.rawQuery(
                "SELECT $VALUE_WORD_B FROM $TABLE_NAME_TAG_GRAPH" +
                        " WHERE $VALUE_RELATION_TYPE = ${RelationType.SYNONYM_TYPE.value}",
                null
            )?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val tagIndex = it.getColumnIndex(VALUE_WORD_B)
                    do {
                        @SuppressLint("Range") val tag =
                            it.getString(tagIndex)
                        tags.add(tag)
                    } while (it.moveToNext())
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryAllLabel, error ", it)
        }
        return tags
    }

    /**
     * 查询tab表中的所有的标签以及tag_graph表中的所有同义词
     * @return 标签id及对应的标签和同义词Map
     * */
    @JvmStatic
    fun queryAllLabelMap(database: SQLiteDatabase?): MutableMap<Int, MutableList<String>> {
        val tags = mutableMapOf<Int, MutableList<String>>()
        kotlin.runCatching {
            database?.rawQuery(
                "SELECT tag.$VALUE_TAG, tag.$VALUE_TAG_ID, graph.$VALUE_WORD_B" +
                        " FROM $TABLE_NAME_TAG tag" +
                        " LEFT JOIN $TABLE_NAME_TAG_GRAPH graph" +
                        " ON tag.$VALUE_TAG = graph.$VALUE_WORD_A" +
                        " AND graph.$VALUE_RELATION_TYPE = ${RelationType.SYNONYM_TYPE.value}" +
                        " WHERE tag.$VALUE_TAG_ID > 0",
                null
            )?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val tagIndex = it.getColumnIndex(VALUE_TAG)
                    val tagIdIndex = it.getColumnIndex(VALUE_TAG_ID)
                    val synonymIndex = it.getColumnIndex(VALUE_WORD_B)
                    do {
                        @SuppressLint("Range") val tagId =
                            it.getInt(tagIdIndex)
                        @SuppressLint("Range") val tag =
                            it.getString(tagIndex)
                        @SuppressLint("Range") val synonym =
                            it.getString(synonymIndex)
                        if (tags[tagId].isNullOrEmpty()) {
                            tags[tagId] = mutableListOf(tag)
                        }
                        if (!synonym.isNullOrEmpty()) {
                            tags[tagId]?.add(synonym)
                        }
                    } while (it.moveToNext())
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryAllLabelMap, error ", it)
        }
        return tags
    }

    /**
     * 根据标签名查询tag_graph表中的同义词（包含同义词和非正式同义词）
     * @return 标签id
     * */
    @JvmStatic
    fun queryLabelFromLabelGraphBySynonym(database: SQLiteDatabase?, labelName: String): LabelBean? {
        var labelBean: LabelBean? = null
        kotlin.runCatching {
            val selectionArgs = arrayOf(labelName)
            database?.rawQuery(
                "SELECT tag.$VALUE_TAG, tag.$VALUE_TAG_ID, graph.$VALUE_WORD_B" +
                        " FROM $TABLE_NAME_TAG tag" +
                        " LEFT JOIN $TABLE_NAME_TAG_GRAPH graph" +
                        " ON tag.$VALUE_TAG = graph.$VALUE_WORD_A" +
                        " AND (graph.$VALUE_RELATION_TYPE = ${RelationType.SYNONYM_TYPE.value}" +
                        " OR graph.$VALUE_RELATION_TYPE = ${RelationType.INFORMALITY_SYNONYM_TYPE.value})" +
                        " WHERE graph.$VALUE_WORD_B = ? COLLATE NOCASE",
                selectionArgs
            )?.use {
                if (it.count > 0 && it.moveToFirst()) {
                    val tagIndex = it.getColumnIndex(VALUE_TAG)
                    val tagIdIndex = it.getColumnIndex(VALUE_TAG_ID)
                    @SuppressLint("Range") val tagId =
                        it.getInt(tagIdIndex)
                    @SuppressLint("Range") val tag =
                        it.getString(tagIndex)
                    labelBean = LabelBean(tagId, tag, labelName)
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "queryLabelFromLabelGraphBySynonym, error ", it)
        }
        if (DEBUG_SEARCH) {
            GLog.d(TAG, LogFlag.DL, "queryLabelFromLabelGraphBySynonym tag table query result: $labelBean")
        }
        return labelBean
    }

    /**
     * 转义补全查询时用户输入的%和_
     */
    @JvmStatic
    private fun escapeStr(fieldValue: String): String {
        return fieldValue
            .replace("%", "\\%")
            .replace("_", "\\_")
    }
}