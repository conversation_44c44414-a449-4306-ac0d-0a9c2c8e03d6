/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - KeywordString.kt
 ** Description: 几个特殊的关键字
 ** Version: 1.0
 ** Date: 2023/9/15
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/15      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.entry

import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.framework.datatmp.R as DatatmpR

/**
 * 几个特殊的关键字
 */
internal object KeywordString {

    val gifKeyWord: String
        get() = ContextGetter.context.resources.getString(DatatmpR.string.model_search_special_keyword_gif)
    val thisYearKeyword: String
        get() = ContextGetter.context.resources.getString(DatatmpR.string.model_search_recommend_keyword_this_year)
    val lastYearKeyword: String
        get() = ContextGetter.context.resources.getString(DatatmpR.string.model_search_recommend_keyword_last_year)
    val preYearKeyword: String
        get() = ContextGetter.context.resources.getString(DatatmpR.string.model_search_recommend_keyword_previous_year)
    var recentAddedKeyword: String = ContextGetter.context.resources.getString(DatatmpR.string.model_search_recommend_recently_added)

    fun reload() {
        recentAddedKeyword = ContextGetter.context.resources.getString(DatatmpR.string.model_search_recommend_recently_added)
        if (LocaleUtils.isChinese(ContextGetter.context)) {
            recentAddedKeyword = recentAddedKeyword.replace(SearchCommonUtils.SPACE_REGEX.toRegex(), TextUtil.EMPTY_STRING)
        }
    }
}