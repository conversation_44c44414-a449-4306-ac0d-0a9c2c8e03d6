/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IGraphInterceptor.kt
 ** Description: 视觉知识图谱标签查询拦截器
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/4/13
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery          2023/4/13     1.0         init
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph.interceptor

import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelBean
import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelQueryEntry

/**
 * 视觉知识图谱标签查询拦截器
 */
interface IGraphInterceptor {
    /**
     * @param queryType 搜索词性的类型
     * @param labels 处理前的标签词组
     * @return 处理后的标签词组
     */
    fun interceptor(queryType: LabelQueryEntry.QueryType, labels: MutableList<LabelBean>): List<LabelBean>
}