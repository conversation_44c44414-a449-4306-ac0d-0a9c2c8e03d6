/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RelationType.kt
 ** Description: 视觉知识图谱枚举关系
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/2/9
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery          2023/2/9     1.0         init
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph.enum

/**
 * 视觉知识图谱枚举关系
 * @property value 对应枚举类型的值
 */
enum class RelationType(val value: Int) {
    /**
     * 同义词，精确搜索-用于直接匹配用户输入的词和同义词的补全
     */
    SYNONYM_TYPE(RelationTypeValue.SYNONYM_VALUE),

    /**
     * 纠错词，模糊搜索-用于纠错用户输入的词，体现在“您可以尝试搜索”场景
     */
    CORRECTING_TYPE(RelationTypeValue.CORRECTING_VALUE),

    /**
     * 补全词，实际是模糊查询同义词
     */
    COMPLETION_TYPE(RelationTypeValue.COMPLETION_VALUE),

    /**
     * 近义词，模糊搜索，体现在“您可以尝试搜索”场景
     */
    NEAR_TYPE(RelationTypeValue.NEAR_VALUE),

    /**
     * 上位词，模糊搜索，体现在“您可以尝试搜索”场景
     */
    UP_TYPE(RelationTypeValue.UP_VALUE),

    /**
     * 下位词，模糊搜索，体现在“您可以尝试搜索”场景
     */
    DOWN_TYPE(RelationTypeValue.DOWN_VALUE),

    /**
     * 可能想搜，模糊搜索，体现在“您可以尝试搜索”场景
     */
    POSSIBLE_TYPE(RelationTypeValue.POSSIBLE_VALUE),

    /**
     * 非正式同义词，可以被直接搜索命中，等同于同义词，但是不能被补全搜索
     * exp：搓麻，用户直接搜索“搓麻”时可以被搜索到，对应为“打麻将”。但是用户输入“搓”，不会补全成“搓麻”
     */
    INFORMALITY_SYNONYM_TYPE(RelationTypeValue.INFORMALITY_SYNONYM_VALUE),
}

/**
 * 视觉知识图谱枚举关系的值
 */
object RelationTypeValue {
    /**
     * 同义词Value
     */
    const val SYNONYM_VALUE = 0

    /**
     * 纠错词Value
     */
    const val CORRECTING_VALUE = 1

    /**
     * 补全词Value
     */
    const val COMPLETION_VALUE = 2

    /**
     * 近义词Value
     */
    const val NEAR_VALUE = 3

    /**
     * 上位词Value
     */
    const val UP_VALUE = 4

    /**
     * 下位词Value
     */
    const val DOWN_VALUE = 5

    /**
     * 可能想搜Value
     */
    const val POSSIBLE_VALUE = 6

    /**
     * 非正式同义词
     */
    const val INFORMALITY_SYNONYM_VALUE = 7
}