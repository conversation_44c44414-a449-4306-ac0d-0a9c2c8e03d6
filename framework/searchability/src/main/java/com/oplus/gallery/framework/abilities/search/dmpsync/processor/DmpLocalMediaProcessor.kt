/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  DmpLocalMediaProcessor.kt
 * * Description:  处理local_media表的数据变化
 * * Version: 1.1
 * * Date : 2025/01/11
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/01/11     1.0        create
 * *  pusong        2025/07/14     1.1        move
 ************************************************************/
package com.oplus.gallery.framework.abilities.search.dmpsync.processor

import android.content.ContentValues
import com.oplus.gallery.framework.abilities.search.dmpsync.DmpSearchSyncHelper.TASK_ENQUEUE_DATA_DELETE_QUEUE
import com.oplus.gallery.framework.abilities.search.dmpsync.DmpSearchSyncHelper.TASK_ENQUEUE_DATA_UPDATE_QUEUE
import com.oplus.gallery.foundation.database.migrate.store.MigrateMediaStore.RecycleMediaColumns.IS_TRASHED_TRUE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_NORMAL
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_NORMAL_NOT_IN_MEDIA_STORE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 处理local_media表的数据变化信息，回调给DmpSearchSyncHelper
 */
class DmpLocalMediaProcessor : DmpTableChangeProcessor() {
    override fun getTag(): String {
        return TAG
    }

    override fun processInsert(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processInsert size:${contentValuePairList.size}" }
        val insertSet = HashSet<Long>()
        contentValuePairList.forEach {
            val mediaId = it.first.getAsLong(LocalColumns.MEDIA_ID)
            if (mediaId != 0L) {
                insertSet.add(mediaId)
            }
        }
        onTableChange(insertSet, TASK_ENQUEUE_DATA_UPDATE_QUEUE)
    }

    override fun processDelete(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processDelete size:${contentValuePairList.size}" }
        contentValuePairList.mapNotNull {
            it.first.getAsLong(LocalColumns.MEDIA_ID).takeIf { it != 0L }
        }.toHashSet().let { onTableChange(it, TASK_ENQUEUE_DATA_DELETE_QUEUE) }
    }

    override fun processUpdate(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processUpdate size:${contentValuePairList.size}" }
        val insertSet = HashSet<Long>()
        val deleteSet = HashSet<Long>()
        val updateSet = HashSet<Long>()
        contentValuePairList.forEach {
            val newMediaId = it.first.getAsLong(LocalColumns.MEDIA_ID)
            val oldMediaId = it.second?.getAsLong(LocalColumns.MEDIA_ID)
            val newInvalid = it.first.getAsInteger(LocalColumns.INVALID)
            val newIsTrashed = it.first.getAsInteger(LocalColumns.IS_TRASHED)
            val newPath = it.first.getAsString(LocalColumns.DATA)
            val oldPath = it.second?.getAsString(LocalColumns.DATA)

            if ((newMediaId == 0L) || (oldMediaId == null) || (oldPath == null)) {
                return@forEach
            }

            if (newMediaId != oldMediaId) {
                // media_id不同，旧的属于delete，新的属于insert
                insertSet.add(newMediaId)
                deleteSet.add(oldMediaId)
            } else {
                // media_id相同不代表就是更新，删除会先置标记位，数据库里依然存在
                if ((newInvalid !in invalidUpdateRange) || (newIsTrashed == IS_TRASHED_TRUE)) {
                    // 相册里删除和文管里删除
                    deleteSet.add(newMediaId)
                } else if (newPath != oldPath) {
                    // Local_Media表仅当_data变更才update
                    updateSet.add(newMediaId)
                }
            }
        }
        onTableChange(insertSet, TASK_ENQUEUE_DATA_UPDATE_QUEUE)
        onTableChange(deleteSet, TASK_ENQUEUE_DATA_DELETE_QUEUE)
        onTableChange(updateSet, TASK_ENQUEUE_DATA_UPDATE_QUEUE)
    }

    companion object {
        private const val TAG = "DmpLocalMediaProcessor"
        private val invalidUpdateRange = listOf(INVALID_NORMAL, INVALID_NORMAL_NOT_IN_MEDIA_STORE)
    }
}