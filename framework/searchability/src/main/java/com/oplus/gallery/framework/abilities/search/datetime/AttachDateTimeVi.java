/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AttachDateTimeVi.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2016/11/25
 ** Author:yanchao.<PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yanchao.<PERSON>@Apps.Gallery3D    2016/11/25       1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.search.datetime;

import android.content.Context;
import android.content.res.Resources;

import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateInfo;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateRange;
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime;
import com.oplus.gallery.framework.datatmp.R;

import java.util.Locale;
import java.util.regex.Matcher;

public class AttachDateTimeVi {
    private static DateInfo sDicViDay;
    private static DateInfo sDicViMonth;
    private static DateInfo sDicViYear;


    public AttachDateTimeVi(Context context) {
        Resources resources = context.getResources();
        if (sDicViDay == null) {
            sDicViDay = new DateInfo(resources, R.array.model_vi_day);
        }
        if (sDicViMonth == null) {
            sDicViMonth = new DateInfo(resources, R.array.model_vi_month);
        }
        if (sDicViYear == null) {
            sDicViYear = new DateInfo(resources, R.array.model_vi_year);
        }
    }

    public ShootDateTime getShootDateTime(String keyword) {
        DateRange range = parseNumericalDateRangeForVietnam(keyword);
        ShootDateTime shootDateTime = null;
        if (range != null) {
            shootDateTime = new ShootDateTime();
            shootDateTime.dateRange = range;
            shootDateTime.shootKeyword = keyword;
            String condition = range.condition;
            String[] fmtArr = (condition != null) ? condition.split(",") : null;
            if ((fmtArr != null) && (fmtArr.length >= 2)) {
                shootDateTime.format = fmtArr[0];
                shootDateTime.conditions = (fmtArr.length > 2) ? condition.substring(condition.indexOf(",") + 1) : fmtArr[1];
            }
        }
        return shootDateTime;
    }

    /**
     * năm + 年份
     * tháng +月份
     * ngày + 日期
     * <p>
     * Ngày 24 tháng 12 năm 2017
     *
     * @param keyword
     * @return
     */
    private DateRange parseNumericalDateRangeForVietnam(String keyword) {
        Matcher matcher = SearchCommonUtils.VIETNAM_DATE_REGEX_PATTERN.matcher(keyword.replaceAll(
                SearchCommonUtils.SPACE_REGEX, ""));
        if (!matcher.matches()) {
            return null;
        }
        String[] keywords = keyword.split(SearchCommonUtils.SPACE_REGEX);
        final int dateMinLen = 2;
        final int dateMaxLen = 6;
        int year = -1;
        int month = -1;
        int day = -1;
        if (keywords.length == dateMinLen) {
            String dateUnit = keywords[0];
            String dateNum = keywords[1];
            // năm YYYY
            if (sDicViYear.equalsKeyword(dateUnit)) {
                year = Integer.parseInt(dateNum);
                if (!DateTimeSelector.isValidYear(year)) {
                    // check whether this year is valid or not
                    return null;
                }
            } else if (sDicViMonth.equalsKeyword(dateUnit)) {
                month = Integer.parseInt(dateNum);
                if (!DateTimeSelector.isValidMonth(month)) {
                    // check whether this month is valid or not
                    return null;
                }
            } else if (sDicViDay.equalsKeyword(dateUnit)) {
                day = Integer.parseInt(dateNum);
                if (!DateTimeSelector.isValidDay(year, month, day)) {
                    // check whether this day is valid or not
                    return null;
                }
            }
        } else if (keywords.length == dateMaxLen) {
            final int dayIndex = 1;
            final int monthIndex = 3;
            final int yearIndex = 5;

            year = Integer.parseInt(keywords[yearIndex]);
            if (!DateTimeSelector.isValidYear(year)) {
                // check whether this year is valid or not
                return null;
            }
            month = Integer.parseInt(keywords[monthIndex]);
            if (!DateTimeSelector.isValidMonth(month)) {
                // check whether this month is valid or not
                return null;
            }
            day = Integer.parseInt(keywords[dayIndex]);
            if (!DateTimeSelector.isValidDay(year, month, day)) {
                // check whether this day is valid or not
                return null;
            }
        }
        if ((year > 0) || (month > 0) || (day > 0)) {
            // YYYY-mm-dd
            DateRange range = new DateRange();
            StringBuffer preSuffixBuffer = new StringBuffer();
            StringBuffer postSuffixBuffer = new StringBuffer();
            if (year > 0) {
                preSuffixBuffer.append("-%Y");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%04d", year));
            }
            if (month > 0) {
                preSuffixBuffer.append("-%m");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", month));
            }
            if (day > 0) {
                preSuffixBuffer.append("-%d");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", day));
            }

            if (preSuffixBuffer.length() > 1) {
                preSuffixBuffer.delete(0, 1);
            }
            if (postSuffixBuffer.length() > 1) {
                postSuffixBuffer.delete(0, 1);
            }
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

}
