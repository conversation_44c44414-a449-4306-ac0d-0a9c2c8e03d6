/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SearchAbilityImpl.kt
 ** Description:搜索能力实现类
 ** Version: 1.0
 ** Date: 2023/9/25
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/25      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.database.Cursor
import android.net.Uri
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.business_lib.constants.StartupConstant
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.BACKGROUND_COLLECT_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCALE_CHANGE_RECOMMEND_UPDATING_URI
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.business_lib.model.data.label.LabelSelector
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.foundation.gstartup.GStartup.doOnCPUIdle
import com.oplus.gallery.foundation.gstartup.GStartup.doOnLaunchActivityCreated
import com.oplus.gallery.foundation.gstartup.task.ExecutePriority
import com.oplus.gallery.foundation.gstartup.task.Task
import com.oplus.gallery.foundation.gstartup.task.TaskCollector
import com.oplus.gallery.foundation.gstartup.task.ThreadPriority
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.cache.RecommendCacheManager
import com.oplus.gallery.framework.abilities.search.cache.SearchContentObserver
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.framework.abilities.search.datetime.ChineseDateTime
import com.oplus.gallery.framework.abilities.search.datetime.ForeignDateTime
import com.oplus.gallery.framework.abilities.search.entry.KeywordString
import com.oplus.gallery.framework.abilities.search.isolate.ISearch
import com.oplus.gallery.framework.abilities.search.isolate.SearchImpl
import com.oplus.gallery.framework.abilities.search.multimodal.IMultiModalAbility
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility
import com.oplus.gallery.framework.abilities.search.permission.AccessPackageManager
import com.oplus.gallery.framework.abilities.search.query.QueryDispatcher
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.scheduler.JobContext
import com.oplus.gallery.standard_lib.scheduler.PriorityScheduler
import com.oplus.gallery.standard_lib.scheduler.ThreadPoolConfig
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.Future

/**
 * 融合搜索能力实现类
 */
class SearchAbilityImpl(private var context: Context) : AbsAppAbility(), ISearchAbility {

    override val domainInstance: AutoCloseable = this

    private val labelSelector: LabelSelector = LabelSelector()
    private val festivalSelector: FestivalSelector = FestivalSelector()
    private val geoCacheService: GeoCacheService by lazy {
        GeoCacheService(context)
    }

    private val keywordCacheManager: KeywordCacheManager by lazy {
        KeywordCacheManager(context, geoCacheService, festivalSelector)
    }

    private val recommendCacheManager: RecommendCacheManager by lazy {
        RecommendCacheManager(ContextGetter.context, geoCacheService, labelSelector)
    }

    private val suggestionCacheDataObserver: SearchContentObserver by lazy  {
        SearchContentObserver(
            keywordCacheManager, recommendCacheManager
        )
    }

    private val iSearch: ISearch by lazy {
        SearchImpl(context, festivalSelector, keywordCacheManager)
    }

    private val queryDispatcher: QueryDispatcher by lazy {
        QueryDispatcher(
            keywordCacheManager, recommendCacheManager, geoCacheService,
            labelSelector, festivalSelector, iSearch
        )
    }

    // 是否需要init，避免多次执行GStartupTask.run()
    @Volatile
    private var isNeedInit = true

    // 并发锁
    private val lock = Any()

    private var isReleased: Boolean = false

    private var oldLocale: String? = null

    private var labelDataTaskFuture: Future<Boolean>? = null

    private val workerSession: WorkerSession by lazy {
        PriorityScheduler(ThreadPoolConfig(threadName = SEARCH_THREAD_NAME)).createDeferredSession(SESSION_LIMIT_SIZE, SESSION_NAME)
    }

    /**
     * 广播是否已经注册
     */
    private var isLocaleBroadcastReceiverRegistered: Boolean = false

    /**
     * 1、相册前台启动时，即context.packageName等于topPackageName时，执行到SearchAbilityImpl#init()，
     * 执行doOnLaunchActivityCreated、doOnCPUIdle，SuggestionCacheHandler延时4秒
     *
     * 2、相册进程未启动，查询app搜索相册数据时，执行到SearchAbilityImpl#executeQuery()，
     * 调用initData()初始化缓存，且SuggestionCacheHandler不延时
     */
    override fun init() {
        submitGStartupTask()
    }

    override fun updateConfig(newConfig: Configuration) {
        // 冷启动时，这里不需要执行，我们会在[init()]进行初始化
        if (isNeedInit) {
            GLog.w(TAG, "updateConfig, skip in cold launch")
            return
        }
        AppScope.launch(Dispatchers.IO) {
            iSearch.init()
            if (geoCacheService.isCurrentLocaleDirty) {
                geoCacheService.cleanDBLocaleDirty()
                keywordCacheManager.clearLocationCache()
                suggestionCacheDataObserver.setChangedImmediately(LOCALE_CHANGE_RECOMMEND_UPDATING_URI)
                geoCacheService.scheduleJob()
                ForeignDateTime.updateDicRelArrays(context)
                ChineseDateTime.updateDicRelArrays(context)
            }
            recommendCacheManager.clearLocationCache()
            KeywordString.reload()
            // 延迟1秒加载标签词典，避免影响应用启动速度
            delay(DELAY_LOAD_DICTIONARY_1S)
            reloadLabelDictionaryIfNeed()
        }
    }

    override fun executeQuery(queryRequest: QueryRequest): Cursor? {
        initData(0)
        return queryDispatcher.dispatch(queryRequest, labelDataTaskFuture)
    }

    /**
     * 若Locale变更，则重新加载标签词典
     * 为解决收到广播后Locale获取不对的情况下，将下列更新标签词典的代码移动到onConfigurationChanged中
     */
    private fun reloadLabelDictionaryIfNeed() {
        val locale = LocaleUtils.getLocale(context).toString()
        if ((locale == oldLocale).not()) {
            oldLocale = locale
            LabelSearchEngine.getInstance().loadDictionary(context, true)
        }
    }

    override fun release() {
        clearFestivalData()
        clearCache()
        unregisterLocaleBroadcastReceiver()
        unregisterCacheObserver()
        LabelGraphSearchAbility.closeCVGraph()
        AccessPackageManager.clearAccessPackages()
        iSearch.release()
        isReleased = true
    }

    private fun clearFestivalData() {
        festivalSelector.destroyFestivals()
    }

    private fun clearCache() {
        recommendCacheManager.clearCache()
        keywordCacheManager.clearCache()
    }

    private fun registerCacheObserver() {
        if (isReleased.not()) {
            suggestionCacheDataObserver.register(context)
        }
    }

    private fun unregisterCacheObserver() {
        suggestionCacheDataObserver.unregister(context)
    }

    private fun submitGStartupTask() {
        // doOnLaunchActivityCreated doOnCPUIdle方法声明都需要在主线程执行添加监听，防止多线程安全问题
        runOnUiThread {
            doOnLaunchActivityCreated { _: Activity?, taskCollector: TaskCollector, entrance: Int ->
                if (entrance == StartupConstant.SEARCH) {
                    taskCollector.add(getGStartupTask(0))
                }
            }

            doOnCPUIdle { taskCollector: TaskCollector, entrance: Int ->
                if (entrance != StartupConstant.SEARCH) {
                    taskCollector.add(getGStartupTask(DELAY_START_TASK_500MS))
                }
            }
        }
    }

    private fun getGStartupTask(delayTime: Long): Task {
        return object : Task(TAG, ThreadPriority.LOW, ExecutePriority.LOW) {
            override fun run() {
                initData(delayTime)
            }
        }
    }

    private fun initData(delayTime: Long) {
        synchronized(lock) {
            if (isNeedInit.not()) {
                GLog.w(TAG, "initData, data is initialized, return")
                return@synchronized
            }
            if (delayTime > 0) {
                try {
                    Thread.sleep(delayTime)
                } catch (e: InterruptedException) {
                    GLog.e(TAG, "[initData] ", e)
                }
            }
            GTrace.traceBegin("$TAG-$delayTime.init")
            iSearch.init()
            SearchCommonUtils.initSearchFeature()
            KeywordString.reload()
            registerCacheObserver()
            triggerCacheUpdate()
            loadData()
            registerLocaleBroadcastReceiver()
            GTrace.traceEnd()
            isNeedInit = false
        }
    }

    private fun loadData() {
        loadLocationData()
        loadFestivalData()
        loadLabelData()
    }

    /**
     * 通知数据脏掉，以触发缓存更新
     */
    private fun triggerCacheUpdate() {
        notifyDataDirty(BACKGROUND_COLLECT_URI)
    }

    private fun loadLocationData() {
        geoCacheService.scheduleJob()
    }

    /**
     * 解析并加载节日数据，存放到FestivalSelector
     */
    private fun loadFestivalData() {
        workerSession.submit { jc: JobContext ->
            if (jc.isCancelled().not()) {
                GTrace.traceBegin("suggestionProvider.loadFestivalData")
                val startTime = System.currentTimeMillis()
                festivalSelector.addCountryFestivals()
                GLog.d(TAG, "[loadFestivalData] costTime:${GLog.getTime(startTime)}")
                GTrace.traceEnd()
            }
        }
    }

    private fun loadLabelData() {
        labelDataTaskFuture = workerSession.submit { jc: JobContext ->
            if (jc.isCancelled().not()) {
                GTrace.traceBegin("suggestionProvider.loadLabelDictionary")
                val startTime = System.currentTimeMillis()
                // 加载国家标签
                labelSelector.addCountryLabel()
                // 增加拒识标签
                LabelDictionary.addRejectLabels(labelSelector.rejectLabels)
                // 词典加载
                LabelSearchEngine.getInstance().loadDictionary(context, true)
                GLog.d(TAG, "[loadLabelData] costTime:${GLog.getTime(startTime)}")
                GTrace.traceEnd()
            }
            true
        }
    }

    private fun registerLocaleBroadcastReceiver() {
        val filter = IntentFilter()
        filter.addAction(AppBrandConstants.Action.ACTION_REGION_CHANGED)
        filter.addAction(Intent.ACTION_LOCALE_CHANGED)
        BroadcastDispatcher.registerExportedReceiver(context, localeChangeBroadcastReceiver, filter)
        isLocaleBroadcastReceiverRegistered = true
    }

    private fun unregisterLocaleBroadcastReceiver() {
        if (isLocaleBroadcastReceiverRegistered) {
            BroadcastDispatcher.unregisterReceiver(context, localeChangeBroadcastReceiver)
        }
    }

    private val localeChangeBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val action = intent?.action
            if (AppBrandConstants.Action.ACTION_REGION_CHANGED == action) {
                GTrace.traceBegin("suggestionProvider.localeChangeBroadcastReceiver")
                val startTime = System.currentTimeMillis()
                festivalSelector.destroyFestivals()
                festivalSelector.addCountryFestivals()
                labelSelector.addCountryLabel()
                LabelDictionary.addRejectLabels(labelSelector.rejectLabels)
                GLog.d(TAG, "[localeChangeBroadcastReceiver] costTime:${GLog.getTime(startTime)}")
                GTrace.traceEnd()
            }
        }
    }

    override fun notifyDataDirty(uri: Uri) {
        AppScope.launch(Dispatchers.IO) {
            val resolver = context.contentResolver
            resolver.notifyChange(uri, null)
        }
    }

    override fun fullSyncDataToDmp() {
        iSearch.syncAllMediaDataToDmp()
    }

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[onLoad]")
        }
        configAbility = abilityBus.requireAbility(IConfigSetterAbility::class.java)
        multiModalAbility = abilityBus.requireAbility(IMultiModalAbility::class.java)
        ocrEmbeddingAbility = abilityBus.requireAbility(IOcrEmbeddingAbility::class.java)
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onUnload(abilityBus, abilityConfig)
        if (DEBUG_SEARCH) {
            GLog.d(TAG, "[onUnload]")
        }
        configAbility?.close()
        configAbility = null
        multiModalAbility?.close()
        multiModalAbility = null
        ocrEmbeddingAbility?.close()
        ocrEmbeddingAbility = null
    }

    override fun removeAccessPackage(index: Int): String {
        return AccessPackageManager.removeAccessPackage(index)
    }

    override fun addAccessPackage(packageName: String) {
        return AccessPackageManager.addAccessPackage(packageName)
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}SearchAbilityImpl"

        private const val SESSION_NAME = "search"
        private const val SEARCH_THREAD_NAME = "searchThr"
        private const val SESSION_LIMIT_SIZE = 2

        private const val DELAY_START_TASK_500MS = 500L
        private const val DELAY_LOAD_DICTIONARY_1S = 1000L

        internal var configAbility: IConfigSetterAbility? = null
        internal var multiModalAbility: IMultiModalAbility? = null
        internal var ocrEmbeddingAbility: IOcrEmbeddingAbility? = null
    }
}