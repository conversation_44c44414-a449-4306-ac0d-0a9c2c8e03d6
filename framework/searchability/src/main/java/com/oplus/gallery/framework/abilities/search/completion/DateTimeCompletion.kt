/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DateTimeCompletion.kt
 ** Description:日期时间补全
 ** Version: 1.0
 ** Date: 2023/9/29
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/29      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import android.os.Bundle
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.andes.photos.kit.search.data.QueryCompletionEntry
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.foundation.util.text.TextUtil.SINGLE_QUOTE
import com.oplus.gallery.foundation.util.text.TextUtil.SPLIT_COMMA_SEPARATOR
import com.oplus.gallery.foundation.util.text.TextUtil.STRIKE
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager

/**
 * 日期时间补全
 */
internal class DateTimeCompletion(context: Context, keywordCacheManager: KeywordCacheManager) :
    BaseCompletionAbility(context, keywordCacheManager) {

    override fun getCompletionType(): Int = CompletionType.TYPE_DATETIME

    override fun queryComplete(query: String): List<QueryCompletionEntry> {
        val completeDateTimes = complete(query)
        GLog.d(TAG, "[queryComplete] query:$query, completeDateTimes:${completeDateTimes.map { it.shootKeyword }}")
        return completeDateTimes.map {
            QueryCompletionEntry(it.shootKeyword, getTimeType(it), getTimeValue(it), query)
        }
    }

    /**
     * 日期时间搜索词补全
     *
     * @param query 搜索词
     * @return 日期时间信息列表
     */
    fun complete(query: String): MutableList<ShootDateTime> {
        GTrace.traceBegin("DateTime.complete")
        val startTime = System.currentTimeMillis()
        val completionDatetime: MutableList<ShootDateTime> = ArrayList()
        if (query.isNotEmpty()) {
            val searchClassifier = buildClassifier(query)
            val completeKeyword = searchClassifier.completeDateTime(query)
            for (keyword in completeKeyword) {
                val shootDateTime = searchClassifier.getShootDateTime(keyword)
                shootDateTime?.let {
                    completionDatetime.add(it)
                }
            }
        }
        GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
        GTrace.traceEnd()
        return completionDatetime
    }

    override fun queryComplete(query: String, extra: Bundle?): List<QueryCompletionEntry> { return queryComplete(query) }

    private fun getTimeValue(shootDateTime: ShootDateTime): String? = when (getTimeType(shootDateTime)) {
        EntityType.ENTITY_TIME_RANGE -> "${shootDateTime.dateRange?.start},${shootDateTime.dateRange?.end}"
        EntityType.ENTITY_YEAR -> shootDateTime.conditions?.trim()?.replace(SINGLE_QUOTE, EMPTY_STRING)?.replace(STRIKE, EMPTY_STRING)
        EntityType.ENTITY_MONTH -> shootDateTime.conditions?.trim()?.replace(SINGLE_QUOTE, EMPTY_STRING)?.replace(STRIKE, EMPTY_STRING)
        EntityType.ENTITY_DAY -> shootDateTime.conditions?.trim()?.replace(SINGLE_QUOTE, EMPTY_STRING)?.replace(STRIKE, EMPTY_STRING)
        EntityType.ENTITY_HOUR_RANGE -> {
            val condition = shootDateTime.dateRange?.condition.orEmpty().trim().replace(SINGLE_QUOTE, EMPTY_STRING).replace(STRIKE, EMPTY_STRING)
            val fmtArr: Array<String> = condition.split(SPLIT_COMMA_SEPARATOR.toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            // fmtArr 是 condition 拆分的结果。首位是 format，后面是时间区间，取时间区间首位和末尾给到 kit。
            val result = if (fmtArr.size >= 2) {
                "${fmtArr[1].trim()}${SPLIT_COMMA_SEPARATOR}${fmtArr.last().trim()}"
            } else {
                EMPTY_STRING
            }
            result
        }
        else -> shootDateTime.shootKeyword
    }

    private fun getTimeType(shootDateTime: ShootDateTime): Int = shootDateTime.dateRange?.condition?.let {
        when (shootDateTime.format) {
            YEAR_FORMAT -> EntityType.ENTITY_YEAR
            MONTH_FORMAT, YEAR_MONTH_FORMAT, PURE_MONTH_FORMAT -> EntityType.ENTITY_MONTH
            DAY_FORMAT, MONTH_DAY_FORMAT, YEAR_MONTH_DAY_FORMAT -> EntityType.ENTITY_DAY
            HOUR_FORMAT -> EntityType.ENTITY_HOUR_RANGE
            else -> EntityType.ENTITY_DATE_MASK
        }
    } ?: EntityType.ENTITY_TIME_RANGE

    companion object {
        private const val TAG = "${SearchConst.TAG}DateTimeCompletion"

        private const val YEAR_FORMAT = "%Y"
        private const val MONTH_FORMAT = "-%m"
        private const val DAY_FORMAT = "%d"
        private const val HOUR_FORMAT = "%H"
        private const val YEAR_MONTH_FORMAT = "%Y-%m"
        private const val PURE_MONTH_FORMAT = "%m"
        private const val YEAR_MONTH_DAY_FORMAT = "%Y-%m-%d"
        private const val MONTH_DAY_FORMAT = "%m-%d"
    }
}