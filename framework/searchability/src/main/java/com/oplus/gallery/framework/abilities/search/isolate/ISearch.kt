/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ISearch.kt
 ** Description: 搜索SDK相册端标准接口，隔离SDK实现
 ** Version: 1.0
 ** Date: 2023/9/15
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/9/15      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.isolate

import android.database.Cursor

/**
 * 搜索SDK相册端标准接口，隔离三方SDK
 */
internal interface ISearch {

    /**
     * 初始化
     */
    fun init()

    /**
     * 资源释放
     */
    fun release()

    /**
     * 特定类型的搜索
     *
     * @param queryWord 查询关键词
     * @param searchType 搜索类型
     * @param labelId 标签ID:当搜索类型为标签类型时才需要传入
     * @param groupId 人物分组ID：当搜索类型为人物时才需要传入
     * @param bucketId 图集ID：当搜索类型为图集时才需要传入
     * @param memoriesId 回忆ID:当搜索类型为回忆时才需要传入
     * @param locationType 地点类型：当指定地点搜索时才需要传入
     * @return Cursor对象
     */
    @Suppress("LongParameterList")
    fun search(
        queryWord: String,
        searchType: Int,
        labelId: Int? = null,
        groupId: String? = null,
        bucketId: String? = null,
        memoriesId: String? = null,
        locationType: Int? = null
    ): Cursor?

    /**
     * 通过标签ID搜索
     *
     * @param queryWord 查询关键词
     * @param labelId 标签ID
     */
    fun searchByLabelId(queryWord: String?, labelId: String): Cursor?

    /**
     * 通过人物ID搜索
     *
     * @param queryWord 查询关键词
     * @param personId 人物ID
     */
    fun searchByPersonId(queryWord: String?, personId: String): Cursor?

    /**
     * 通过人物ID搜索
     *
     * @param queryWord 查询关键词
     * @param bucketId 图集ID
     */
    fun searchByBucketId(queryWord: String?, bucketId: String): Cursor?

    /**
     * 全量同步相册媒体数据到dmp中子
     */
    fun syncAllMediaDataToDmp()
}