/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SearchResultAssembler.kt
 ** Description:搜索结果拼装器接口
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult

/**
 * 搜索结果拼装器接口
 */
internal interface SearchResultAssembler {

    /**
     * 搜索结果类型
     */
    val resultType: Int

    /**
     * 查询结果封装为ResultEntry，保存在SingleQueryResult对象
     *
     * @param singleQueryResult 单个query的所有维度搜索结果
     * @param searchEntry 搜索信息对象
     * @return 拼装新的搜索类型结果后的结果对象
     */
    fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult
}