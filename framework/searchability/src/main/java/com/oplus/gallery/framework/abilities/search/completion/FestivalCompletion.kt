/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FestivalCompletion.kt
 ** Description:节日补全
 ** Version: 1.0
 ** Date: 2023/9/29
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/29      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import android.os.Bundle
import android.util.Pair
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.andes.photos.kit.search.data.QueryCompletionEntry
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.business_lib.model.data.base.entry.DateTimeEntry
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager

/**
 * 节日补全
 */
internal class FestivalCompletion(
    context: Context,
    private val festivalSelector: FestivalSelector,
    private val keywordCacheManager: KeywordCacheManager
) : BaseCompletionAbility(context, keywordCacheManager) {

    override fun getCompletionType(): Int = CompletionType.TYPE_FESTIVAL

    override fun queryComplete(query: String): List<QueryCompletionEntry> {
        val completeFestivals = complete(query)
        GLog.d(TAG, "[queryComplete] query:$query, completeFestivals:$completeFestivals")
        return completeFestivals.map {
            QueryCompletionEntry(it?.first, EntityType.ENTITY_FESTIVAL, it?.second, query)
        }
    }

    /**
     * 搜索词尝试按节日名称补全
     *
     * @param query 搜索词
     * @return 节日信息映射表
     */
    private fun complete(query: String): List<Pair<String?, String?>?> {
        GTrace.traceBegin("Festival.complete")
        val startTime = System.currentTimeMillis()
        val festivalResult: MutableList<Pair<String?, String?>?> = mutableListOf()
        // 依据关键词在节日字典中找到所有匹配的节日
        val festivalPairList: MutableList<Pair<String?, String?>>? = festivalSelector.convert(query)
        if (festivalPairList.isNullOrEmpty()) {
            return festivalResult
        }

        val festivalMap: HashMap<String, DateTimeEntry>? = keywordCache.festivalMap
        // 缓存中节日表是空，那么从数据库中查询
        if (festivalMap.isNullOrEmpty()) {
            keywordCacheManager.updateFestivalCache()
        }
        // 数据库也是空的, 直接返回
        if (festivalMap == null) {
            return festivalResult
        }
        // 数据库中能匹配到，才是有效的节日信息
        for (festivalPair in festivalPairList) {
            val festival: DateTimeEntry? = festivalMap[festivalPair.first]
            if (festival != null) {
                festivalResult.add(festivalPair)
            }
        }
        GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
        GTrace.traceEnd()
        return festivalResult
    }

    override fun queryComplete(query: String, extra: Bundle?): List<QueryCompletionEntry> { return queryComplete(query) }

    companion object {
        private const val TAG = "${SearchConst.TAG}FestivalCompletion"
    }
}