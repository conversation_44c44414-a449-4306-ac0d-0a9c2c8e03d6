/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelGraphBean.kt
 ** Description: 视觉知识图谱标签图谱
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/2/9
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery         2023/2/9     1.0         init
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph.bean

import java.util.Objects

/**
 * 对应CV的图谱数据类型
 * @property wordA 视觉知识图谱中的标签名称
 * @property wordB 视觉知识图谱中对应wordA的关系词
 * @property relationType 视觉知识图谱中 wordB是wordA的对应关系
 */
data class LabelGraphBean(
    val wordA: String,
    val wordB: String,
    private val relationType: Int
) {
    /**
     * 此处的equals逻辑主要是为了去重使用，请勿随意改动
     * 在图谱中，wordA即为标签，所以如果wordA相同，即可。
     */
    override fun equals(other: Any?): Boolean {
        if ((other == null) || (other !is LabelGraphBean)) {
            return false
        }
        return other.wordA == wordA
    }

    /**
     * 重写了equals之后，会需要对应的重写hashCode方法，否则代码质量编译不过关。
     * <pre>
     *     Effective Java 中对采用31做了说明：
     *     之所以使用 31， 是因为他是一个奇素数。如果乘数是偶数，并且乘法溢出的话，信息就会丢失，因为与2相乘等价于移位运算（低位补0）。
     *     使用素数的好处并不很明显，但是习惯上使用素数来计算散列结果。
     *     31 有个很好的性能，即用移位和减法来代替乘法，可以得到更好的性能： 31 * i == (i << 5）- i， 现代的 VM 可以自动完成这种优化
     * </pre>
     */
    override fun hashCode(): Int {
        var result = Objects.hashCode(wordA)
        result = 31 * result + Objects.hashCode(wordB)
        result = 31 * result + Objects.hashCode(relationType)
        return result
    }
}