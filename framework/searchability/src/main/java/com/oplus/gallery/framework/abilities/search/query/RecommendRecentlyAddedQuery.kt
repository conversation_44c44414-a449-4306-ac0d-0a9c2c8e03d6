/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendRecentlyAddedQuery.kt
 ** Description: 最近添加推荐查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.framework.abilities.search.entry.KeywordString
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 最近添加推荐查询
 *
 * @property forceQuery 是否强制从数据库查询
 * @property recommendCache 推荐缓存
 */
internal class RecommendRecentlyAddedQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache,
) : Query {
    override fun query(): Cursor? {
        return if (!forceQuery && recommendCache.hasRecentlyAddedRecommend()) {
            recommendCache.obtainRecentlyAddedRecommend()
        } else {
            val startTime = System.currentTimeMillis()
            val cursor = MatrixCursor(ITEM_PROJECTION)
            val queryTime = System.currentTimeMillis()
            val recentlyAddedList = DBSearchUtils.queryMediaIdEntryByRecentlyAdded(ContextGetter.context)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] queryMediaIdEntryByRecentlyAdded costTime:${GLog.getTime(queryTime)}")
            }
            if (recentlyAddedList != null && recentlyAddedList.isNotEmpty()) {
                recentlyAddedList.sort()
                for (entry in recentlyAddedList) {
                    if (entry != null) {
                        cursor.addRow(arrayOf<Any>(KeywordString.recentAddedKeyword, entry.mediaId, entry.dateTaken, entry.galleryId))
                    }
                }
            }
            recommendCache.updateToRecentlyAddedRecommend(cursor)
            cursor.moveToPosition(-1)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] costTime:${GLog.getTime(startTime)}")
            }
            cursor
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendRecentlyAddedQuery"

        private val ITEM_PROJECTION = arrayOf(
            SearchSuggestionProviderUtil.COLUMNNAME,
            SearchSuggestionProviderUtil.COLUMN_ID,
            SearchSuggestionProviderUtil.COLUMN_DATETAKEN,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_GALLERY_ID
        )
    }
}