/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelCompletion.kt
 ** Description:标签搜索关键字的补全
 ** Version: 1.0
 ** Date: 2023/10/7
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/7      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import android.os.Bundle
import com.oplus.andes.photos.kit.search.ability.ICompletionAbility
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.andes.photos.kit.search.data.QueryCompletionEntry
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier
import java.util.Locale

/**
 * 标签搜索关键字的补全
 */
internal class LabelCompletion(
    private val context: Context,
    keywordCacheManager: KeywordCacheManager
) :
    BaseCompletionAbility(context, keywordCacheManager) {

    override fun getCompletionType(): Int = CompletionType.TYPE_LABEL

    override fun queryComplete(query: String): List<QueryCompletionEntry> {
        val completeLabels = complete(query)
        GLog.d(TAG, "[queryComplete] query:$query, completeLabels:$completeLabels")
        return labelMapToCompletionEntries(query, completeLabels)
    }

    /**
     * 可扩展标签补全能力
     *
     * @param query 整句query或分词
     * @param extra 可为空。若extra为空，默认使用queryComplete(query: String)。否则根据extra.getString(ICompletionAbility.BUNDLE_COMPLETE_TYPE, "")
     *        获取调用函数
     * @return 补全后的词
     */
    override fun queryComplete(query: String, extra: Bundle?): List<QueryCompletionEntry> {
        if (extra == null) {
            return queryComplete(query)
        }
        GTrace.traceBegin("Label.complete")
        val startTime = System.currentTimeMillis()
        val completeType = extra.getString(ICompletionAbility.BUNDLE_COMPLETE_TYPE, TextUtil.EMPTY_STRING)
        val isLabelGraphEnable = LabelGraphSearchAbility.checkAvailabilityForLabelGraph(context)
                || (DEBUG_SEARCH && SearchConst.LABEL_GRAPH)
        val labelIdToNameMap =
            when (completeType) {
                LABEL_METHOD_CORRECTION -> {
                    if (isLabelGraphEnable) {
                        LabelGraphSearchAbility.wordCorrectionForKit(query)
                    } else {
                        wordCompleteLocalLabelDict(query)
                    }
                }

                LABEL_METHOD_COMPLETION_WITH_SYN_COR -> {
                    if (isLabelGraphEnable) {
                        wordCompleteLabelGraph(query)
                    } else {
                        wordCompleteLocalLabelDict(query)
                    }
                }

                else -> {
                    if (isLabelGraphEnable) {
                        completeLabelGraph(query)
                    } else {
                        completeLocalLabelDict(query)
                    }
                }
            }
        latestLabelCompleteMap.clear()
        latestLabelCompleteMap.putAll(labelIdToNameMap)
        GLog.d(TAG, "[queryComplete] costTime:${GLog.getTime(startTime)}, query:$query, completeLabels:$latestLabelCompleteMap")
        GTrace.traceEnd()
        return labelMapToCompletionEntries(query, latestLabelCompleteMap)
    }

    fun complete(query: String): MutableMap<Int, String> {
        GTrace.traceBegin("Label.complete")
        val startTime = System.currentTimeMillis()
        latestLabelCompleteMap.clear()
        val labelIdToNameMap = if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(context)
            || (DEBUG_SEARCH && SearchConst.LABEL_GRAPH)) {
            completeLabelGraph(query)
        } else {
            completeLocalLabelDict(query)
        }
        latestLabelCompleteMap.putAll(labelIdToNameMap)
        GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
        GTrace.traceEnd()
        return labelIdToNameMap
    }

    /**
     * 图谱标签补全
     *
     * @param query 查询词
     * @return 标签ID到标签名的映射表
     */
    private fun completeLabelGraph(query: String): MutableMap<Int, String> {
        return LabelGraphSearchAbility.queryCompletionForKit(query)
    }

    /**
     * 图谱标签补全
     * 不查询补全词
     * @param word 查询词
     * @return 标签ID到标签名的映射表
     */
    private fun wordCompleteLabelGraph(word: String): MutableMap<Int, String> {
        return LabelGraphSearchAbility.wordCompletionForKit(word)
    }

    /**
     * 标签补全
     *
     * @param query 查询词
     * @return 标签ID到标签名的映射表
     */
    private fun completeLocalLabelDict(query: String): MutableMap<Int, String> {
        val searchClassifier = buildClassifier(query)
        val finalCompletionQueryEntities = mutableMapOf<Int, String>()
        finalCompletionQueryEntities.putAll(getCompleteLabelIdToNameMap(query, searchClassifier))
        finalCompletionQueryEntities.putAll(getMatchLabelIdToLabelNameMap(query))
        return finalCompletionQueryEntities
    }

    private fun wordCompleteLocalLabelDict(query: String): MutableMap<Int, String> {
        val finalCompletionQueryEntities = mutableMapOf<Int, String>()
        finalCompletionQueryEntities.putAll(getMatchLabelIdToLabelNameMap(query))
        return finalCompletionQueryEntities
    }

    private fun getMatchLabelIdToLabelNameMap(query: String): MutableMap<Int, String> {
        // 无缓存，直接返回
        if (keywordCache.mediaIdToLabelIdsMap == null) {
            return mutableMapOf()
        }
        // 依据搜索关键词以及标签缓存表，尽可能解析和匹配每一个可能的结果; 若一个没有匹配到，返回空列表
        val searchResult = LabelSearchEngine.getInstance().process(query, keywordCache.mediaIdToLabelIdsMap) ?: return mutableMapOf()
        // 全匹配的标签ID到标签名称的映射表
        val fullMatchLabelIdToLabelNameMap = mutableMapOf<Int, String>()
        searchResult.fullMatchMaps?.let {
            it.forEach { (label, labelIds) ->
                labelIds.forEach { labelId ->
                    kotlin.runCatching {
                        fullMatchLabelIdToLabelNameMap[labelId.toInt()] = label
                    }.onFailure { fail ->
                        GLog.e(TAG, "[getMatchLabelIdToLabelNameMap] labelId.toInt() error: ", fail)
                    }
                }
            }
        }

        return fullMatchLabelIdToLabelNameMap
    }

    private fun getCompleteLabelIdToNameMap(query: String, searchClassifier: SearchClassifier): MutableMap<Int, String> {
        // 缓存中labelId到LabelName的表
        val labelIdToLabelNameMap = mutableMapOf<Int, String>()
        keywordCache.labelIdToNameMap?.let {
            it.entries.forEach { (labelId, labelNameList) ->
                if (labelNameList == null || labelNameList.isEmpty()) return@forEach
                for (label in labelNameList) {
                    // 标签名称包含搜索词，且长度不一样
                    if (searchClassifier.contains(label.lowercase(Locale.getDefault()), query) && label.length != query.length) {
                        labelIdToLabelNameMap[labelId] = label
                        break
                    }
                }
            }
        }
        return labelIdToLabelNameMap
    }

    private fun labelMapToCompletionEntries(
        singleKeyword: String,
        labelIdToLabelMap: Map<Int, String>
    ): List<QueryCompletionEntry> {
        return labelIdToLabelMap.toList().map {
            QueryCompletionEntry(
                it.second, EntityType.ENTITY_LABEL, it.first.toString(), singleKeyword
            )
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}LabelCompletion"

        /**
         * 标签补全提供图谱纠错能力
         */
        private const val LABEL_METHOD_CORRECTION = "correct"

        /**
         * 标签补全提供图片近义词，纠错词映射标签能力
         */
        private const val LABEL_METHOD_COMPLETION_WITH_SYN_COR = "complete_syn_cor"

        private val latestLabelCompleteMap = mutableMapOf<Int, String>()

        fun getLabelId(labelName: String): Int {
            latestLabelCompleteMap.forEach { (labelId, label) ->
                if (labelName == label) {
                    // 用完之后应该要立即清理
                    latestLabelCompleteMap.clear()
                    return labelId
                }
            }
            latestLabelCompleteMap.clear()
            return LabelSearchEngine.INVALID_ID
        }
    }
}