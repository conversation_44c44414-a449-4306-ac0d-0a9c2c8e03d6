/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SuggestionCacheHandler.kt
 ** Description: 负责更新各种搜索类型的缓存
 ** Version: 1.0
 ** Date: 2023/10/12
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/12     1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cache

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.SearchContentObserver.Companion.CODE_ALBUM_NAME
import com.oplus.gallery.framework.abilities.search.cache.SearchContentObserver.Companion.CODE_BACKGROUND_COLLECT
import com.oplus.gallery.framework.abilities.search.cache.SearchContentObserver.Companion.CODE_FACE
import com.oplus.gallery.framework.abilities.search.cache.SearchContentObserver.Companion.CODE_LABEL
import com.oplus.gallery.framework.abilities.search.cache.SearchContentObserver.Companion.CODE_MEMORY

/**
 * 负责更新各种搜索类型的缓存
 *
 * @param looper Looper对象
 */
internal class SuggestionCacheHandler internal constructor(
    private val keywordCacheManager: KeywordCacheManager,
    private val searchRecommendCacheManager: RecommendCacheManager,
    looper: Looper
) : Handler(looper) {

    override fun handleMessage(message: Message) {
        when (message.what) {
            CODE_FACE -> updateCache(TASK_PERSON)
            CODE_LABEL -> updateCache(TASK_LABEL)
            CODE_BACKGROUND_COLLECT -> updateCache(TASK_ALL)
            CODE_MEMORY -> updateCache(TASK_MEMORIES)
            CODE_ALBUM_NAME -> updateCache(TASK_ALBUM)
            else -> updateCache(TASK_ALL)
        }
    }

    private fun updateCache(taskMask: Int) {
        GLog.d(TAG, "[updateCache] taskMask:$taskMask")
        keywordCacheManager.updateCache(taskMask)
        searchRecommendCacheManager.updateCache(taskMask)
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}SuggestionCacheHandler"

        internal const val TASK_ALL = -0x1
        internal const val TASK_DATE = 0x0001
        internal const val TASK_LABEL = 0x0002
        internal const val TASK_ALBUM = 0x0004
        internal const val TASK_PERSON = 0x0008
        internal const val TASK_LOCATION = 0x0010
        internal const val TASK_MEMORIES = 0x0020
    }
}