/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DBSearchUtils.kt
 ** Description:搜索数据库查询工具类
 ** Version: 1.0
 ** Date: 2023/9/25
 ** Author: <EMAIL>
 ** OPLUS Java File Skip Rule:FileLength,MethodLength,MethodComplexity,ParameterNumber,IllegalCatch,NestedBranchDepth
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/9/25      1.0         INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.utils;

import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.ArrayMap;
import android.util.ArraySet;

import androidx.annotation.VisibleForTesting;

import com.oplus.gallery.business_lib.model.config.allowlist.FolderNoteConfig;
import com.oplus.gallery.business_lib.model.data.base.MediaObject;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketHelper;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketHelper.BucketEntry;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketIdsCacheHelper;
import com.oplus.gallery.business_lib.model.data.base.buckets.LocaleNoteNameHelper;
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry;
import com.oplus.gallery.business_lib.model.data.base.entry.CoverMediaEntry;
import com.oplus.gallery.business_lib.model.data.base.entry.DateTimeEntry;
import com.oplus.gallery.business_lib.model.data.base.entry.FestivalEntry;
import com.oplus.gallery.business_lib.model.data.base.entry.LabelEntry;
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry;
import com.oplus.gallery.business_lib.model.data.base.entry.MemoriesAlbumEntry;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Recycle;
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaTypeSupport;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper;
import com.oplus.gallery.business_lib.model.data.common.dbaccess.convert.MediaIdEntryListConvert;
import com.oplus.gallery.business_lib.model.data.cshot.CShotUtils;
import com.oplus.gallery.business_lib.model.data.festival.GeneralFestivals;
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary;
import com.oplus.gallery.business_lib.model.data.local.set.ClassifiedPortraitBlurAlbum;
import com.oplus.gallery.business_lib.model.data.local.set.EnhanceTextAlbum;
import com.oplus.gallery.business_lib.model.data.local.set.LocalAlbum;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesHelper.MemoriesEntry;
import com.oplus.gallery.business_lib.model.data.person.set.FaceItem;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns;
import com.oplus.gallery.foundation.database.util.DatabaseUtils;
import com.oplus.gallery.foundation.database.util.SQLGrammar;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateRange;
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import static android.provider.BaseColumns._ID;
import static com.oplus.gallery.business_lib.model.data.base.buckets.BucketIdsCacheHelper.getCameraBucketIds;
import static com.oplus.gallery.business_lib.model.data.base.buckets.BucketIdsCacheHelper.getCardCaseBucketIds;
import static com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry.TYPE_CAMERA;
import static com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry.TYPE_CARD_CASE;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper.COUNT;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper.DEFAULT_LABEL_DATA_WHERE;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper.ORDER_CLAUSE_DATE_TAKEN;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper.addAllMediaIdEntry;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper.queryScreenShotsFromMediaDB;
import static com.oplus.gallery.business_lib.model.data.festival.FestivalSelector.FestivalInfo.FILTER_TYPE_COMMON;
import static com.oplus.gallery.business_lib.model.data.festival.FestivalSelector.FestivalInfo.getFilterType;
import static com.oplus.gallery.foundation.database.store.GalleryStore.FestivalColumns.DATE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.FestivalColumns.FESTIVAL_NAME;
import static com.oplus.gallery.foundation.database.store.GalleryStore.FestivalColumns.FILTER_TYPE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.FestivalColumns.LEVEL;
import static com.oplus.gallery.foundation.database.store.GalleryStore.FestivalColumns.REGION;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns.IS_RECYCLED;
import static com.oplus.gallery.foundation.database.store.GalleryStore.ScanLabelColumns.SCENE_ID;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.getSubQueryOrder;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.AND;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.AS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.BIT_AND;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA_SPACE;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.COUNT_ALL;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.DESC;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.DISTINCT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.DOT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL_ZERO;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.FROM;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.GREATER_THAN_ZERO;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.GROUP_BY;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.GROUP_CONCAT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.IN;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.INNER_JOIN;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_OUTER_JOIN;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LIKE;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LIKE_PERCENT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.LIMIT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.NOT_EQUAL;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.NOT_IN;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ON;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.OR;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ORDER_BY;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.ORDER_DESC;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.QUOTE;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.REPLACE;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.SPACE;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.TRIM_EQUAL;
import static com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE;

public class DBSearchUtils {

    private static final String TAG = "DBSearchUtils";

    private static final String QUERY_LABELS_WHERE_ORDER_CLAUSE =
            LocalColumns.DATE_TAKEN + getSubQueryOrder() + COMMA_SPACE + LocalColumns.MEDIA_ID + getSubQueryOrder();
    private static final String QUERY_LABELS_WHERE = DEFAULT_LABEL_DATA_WHERE + AND + SCENE_ID + NOT_EQUAL + LabelDictionary.getOtherId();
    private static final String QUERY_ALL_LABELS_WHERE = QUERY_LABELS_WHERE;
    private static final String QUERY_ALL_LABELS_MEDIA_ID_WHERE = QUERY_LABELS_WHERE;

    private static final String QUERY_FOR_FESTIVALS_ORDER_CLAUSE = ORDER_CLAUSE_DATE_TAKEN;
    private static final String QUERY_FOR_YEAR_ORDER_CLAUSE =
            LocalColumns.DATE_TAKEN + getSubQueryOrder() + COMMA + SPACE + LocalColumns.MEDIA_ID + getSubQueryOrder();
    private static final String QUERY_FOR_MONTH_ORDER_CLAUSE = QUERY_FOR_YEAR_ORDER_CLAUSE;

    private static final int BATCH_COUNT_QUERY_ALBUM = 100;
    private static final String STRFTIME_SEARCH_FORMAT = "strftime('%1$s', datetaken / 1000, 'unixepoch', 'localtime')";

    private static final String[] DATE_RANGE_PROJECTIONS = new String[]{_ID, LocalColumns.MEDIA_ID, LocalColumns.DATE_TAKEN, LocalColumns.MEDIA_TYPE};

    private static final long INVALID_DATA_VERSION = -1L;

    // dmpquery查询时设置分页查询起始值，提高每日任务或ai搜索的查询效率
    private static Long sCursorId = 0L;

    /**
     * 查询所有标签的映射表
     *
     * @return key是标签ID，value是标签名列表
     */
    public static HashMap<Integer, List<String>> queryAllLabelsMappingSceneId() {
        HashMap<Integer, List<String>> result = new HashMap<>();
        String[] projection = new String[]{DISTINCT + SPACE + SCENE_ID};
        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
            .setProjection(projection)
            .setWhere(QUERY_ALL_LABELS_WHERE)
            .setConvert(new CursorConvert())
            .build();

        try (Cursor cursor = DataAccess.getAccess().query(req)) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    int sceneId = cursor.getInt(0);
                    List<String> sceneNames = LabelDictionary.getLabelNames(sceneId);
                    result.put(sceneId, sceneNames);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "[queryAllLabelsMappingSceneId] Exception:", e);
        }
        return result;
    }

    /**
     * 查询所有标签的映射表
     *
     * @return key是媒体ID，value是标签ID列表
     */
    public static HashMap<Integer, HashSet<Integer>> queryAllLabelsMappingMediaId() {
        HashMap<Integer, HashSet<Integer>> result = new HashMap<>();
        String stringBuilder =
            SELECT + LocalColumns.MEDIA_ID + COMMA + SCENE_ID + FROM + GalleryStore.ScanLabel.TAB + INNER_JOIN + GalleryStore.GalleryMedia.TAB
                    + ON + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA + TRIM_EQUAL + GalleryStore.ScanLabel.TAB + DOT
                    + LocalColumns.DATA + WHERE + QUERY_ALL_LABELS_MEDIA_ID_WHERE;
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(new CursorConvert())
            .setQuerySql(stringBuilder)
            .setSqlArgs(null)
            .build();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int indexMedia = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexScene = cursor.getColumnIndex(SCENE_ID);
                while (cursor.moveToNext()) {
                    int mediaId = cursor.getInt(indexMedia);
                    int sceneId = cursor.getInt(indexScene);
                    HashSet<Integer> sceneIds = result.get(mediaId);
                    if (sceneIds == null) {
                        sceneIds = new HashSet<>();
                        sceneIds.add(sceneId);
                        result.put(mediaId, sceneIds);
                    } else {
                        sceneIds.add(sceneId);
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "[queryAllLabelsMappingMediaId] Exception:", e);
        }
        return result;
    }

    /**
     * 为keyword缓存查询所有的图集信息
     * 会过滤掉相同的图集
     *
     * @param context 上下文
     * @return List<AlbumEntry>
     */
    public static List<AlbumEntry> queryAllAlbumEntry(Context context) {
        long startTime = System.currentTimeMillis();
        BucketEntry[][] result = BucketHelper.loadBucketEntries(context, IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL);
        BucketEntry[] normalAlbums = result[0];
        BucketEntry[] hideAlbums = result[1];
        GLog.d(TAG, "[queryAllAlbumEntry] loadBucketEntries costTime :" + GLog.getTime(startTime));
        GLog.d(TAG, "[queryAllAlbumEntry] loadBucketEntries normalAlbums size :" + normalAlbums.length + ", hideAlbums size : "
                + hideAlbums.length);

        List<AlbumEntry> addAlbumEntry = new ArrayList<>();
        Map<String, AlbumEntry> albumEntryMap = new HashMap<>();
        List<Integer> cameraBucketIds = getCameraBucketIds();
        List<Integer> cardCaseBucketIds = getCardCaseBucketIds();
        Context application = context.getApplicationContext();

        // 只遍历正常图集
        for (BucketEntry bucketEntry : normalAlbums) {
            // 确定bucketName,若为空则跳过此图集
            String bucketName = bucketEntry.mBucketName;
            List<Integer> bucketIdList = bucketEntry.mBucketIdList;
            if (!bucketIdList.isEmpty()) {
                int bucketId = bucketIdList.get(0);
                String name = FolderNoteConfig.getInstance().getNoteName(bucketId);
                if (!TextUtils.isEmpty(name)) {
                    bucketName = name;
                } else if (BucketIdsCacheHelper.isRootBucketIds(bucketId)) {
                    bucketName = LocaleNoteNameHelper.getRootPathName(application.getResources());
                }
            }
            if (TextUtils.isEmpty(bucketName) || (bucketIdList.isEmpty())) {
                continue;
            }

            AlbumEntry oldAlbumEntry = albumEntryMap.get(bucketName);
            boolean isCardCaseAlbum = cardCaseBucketIds.contains(bucketEntry.mBucketId);
            if (oldAlbumEntry == null) {
                AlbumEntry albumEntry = new AlbumEntry();
                albumEntry.bucketIdList = bucketIdList;
                // 依据bucketId确定图集类型，对相机和随身卡包特殊处理
                if (cameraBucketIds.contains(bucketEntry.mBucketId)) {
                    albumEntry.type = TYPE_CAMERA;
                } else {
                    if (isCardCaseAlbum) {
                        albumEntry.type = TYPE_CARD_CASE;
                    } else {
                        albumEntry.type = AlbumEntry.TYPE_NORMAL_ALBUM;
                    }
                }
                // 卡证档案图集名称固定
                if (isCardCaseAlbum) {
                    albumEntry.bucketName = context.getResources().getString(com.oplus.gallery.framework.datatmp.R.string.main_title_card_case);
                } else {
                    // 其他通过第一个bucketId来获取本地文件夹名称
                    albumEntry.bucketName = LocaleNoteNameHelper.getLocaleNoteName(bucketIdList.get(0), bucketName);
                }
                // 记录原来的图集名
                albumEntry.originalBucketName = bucketEntry.mBucketName;
                albumEntry.bucketId = bucketEntry.mBucketId;
                if ((bucketEntry.mType == IMediaTypeSupport.MEDIA_TYPE_SUPPORT_SELF_ALBUM)
                        || ((bucketEntry.mType == IMediaTypeSupport.MEDIA_TYPE_SUPPORT_CAMERA) && cameraBucketIds.contains(bucketEntry.mBucketId))
                        || ((bucketEntry.mType == IMediaTypeSupport.MEDIA_TYPE_SUPPORT_CARD_CASE) && isCardCaseAlbum)) {
                    albumEntry.supportCShot = true;
                }
                albumEntryMap.put(bucketName, albumEntry);
            } else {
                Set<Integer> lastBucketIdSet = new ArraySet<>();
                List<Integer> oldBucketIdList = oldAlbumEntry.bucketIdList;
                List<Integer> newBucketIdList = bucketEntry.mBucketIdList;
                if (oldBucketIdList.containsAll(newBucketIdList) || newBucketIdList.containsAll(oldBucketIdList)) {
                    lastBucketIdSet.addAll(oldBucketIdList);
                    lastBucketIdSet.addAll(newBucketIdList);
                    oldAlbumEntry.bucketIdList = new ArrayList<>(lastBucketIdSet);
                } else {
                    AlbumEntry albumEntry = new AlbumEntry();
                    albumEntry.bucketName = bucketName;
                    albumEntry.bucketId = bucketEntry.mBucketId;
                    albumEntry.bucketIdList = bucketEntry.mBucketIdList;
                    if (cameraBucketIds.contains(bucketEntry.mBucketId)) {
                        albumEntry.type = TYPE_CAMERA;
                    } else {
                        if (isCardCaseAlbum) {
                            albumEntry.type = TYPE_CARD_CASE;
                        } else {
                            albumEntry.type = AlbumEntry.TYPE_NORMAL_ALBUM;
                        }
                    }
                    albumEntry.originalBucketName = bucketEntry.mBucketName;
                    if ((bucketEntry.mType == IMediaTypeSupport.MEDIA_TYPE_SUPPORT_SELF_ALBUM)
                            || ((bucketEntry.mType == IMediaTypeSupport.MEDIA_TYPE_SUPPORT_CAMERA) && cameraBucketIds.contains(bucketEntry.mBucketId))
                            || ((bucketEntry.mType == IMediaTypeSupport.MEDIA_TYPE_SUPPORT_CARD_CASE) && isCardCaseAlbum)) {
                        albumEntry.supportCShot = true;
                    }
                    addAlbumEntry.add(albumEntry);
                }
            }
        }
        List<AlbumEntry> albumEntries = new ArrayList<>();
        albumEntries.addAll(addAlbumEntry);
        albumEntries.addAll(albumEntryMap.values());
        // 再添加虚拟图集信息
        albumEntries.addAll(getVirtualAlbumEntry(context));
        GLog.d(TAG, "[queryAllAlbumEntry] albumEntries size :" + albumEntries.size());
        return albumEntries;
    }

    private static List<AlbumEntry> getVirtualAlbumEntry(Context context) {
        final Resources resources = context.getResources();
        List<AlbumEntry> albumEntries = new ArrayList<>();

        // 视频图集
        AlbumEntry videoAlbum = new AlbumEntry();
        videoAlbum.type = AlbumEntry.TYPE_VIDEO_ALBUM;
        videoAlbum.bucketName = resources.getString(com.oplus.gallery.framework.datatmp.R.string.model_camera_video_name);
        albumEntries.add(videoAlbum);

        // 超级文本图集
        AlbumEntry enhanceTextAlbum = new AlbumEntry();
        enhanceTextAlbum.type = AlbumEntry.TYPE_ENHANCE_TEXT_ALBUM;
        enhanceTextAlbum.bucketName = resources.getString(com.oplus.gallery.framework.datatmp.R.string.model_enhance_text);
        albumEntries.add(enhanceTextAlbum);

        // 删除图集
        AlbumEntry recycleAlbum = new AlbumEntry();
        recycleAlbum.type = AlbumEntry.TYPE_RECYCLE_ALBUM;
        recycleAlbum.bucketName = resources.getString(com.oplus.gallery.framework.datatmp.R.string.model_recycle_bin);
        albumEntries.add(recycleAlbum);

        // 人像景深图集
        AlbumEntry portraitBlurAlbum = new AlbumEntry();
        portraitBlurAlbum.type = AlbumEntry.TYPE_PORTRAIT_BLUR;
        portraitBlurAlbum.bucketName = resources.getString(com.oplus.gallery.framework.datatmp.R.string.model_title_portrait_blur);
        albumEntries.add(portraitBlurAlbum);

        // 实况图集
        AlbumEntry oliveAlbum = new AlbumEntry();
        oliveAlbum.type = AlbumEntry.TYPE_OLIVE;
        oliveAlbum.bucketName = resources.getString(com.oplus.gallery.framework.datatmp.R.string.model_title_olive);
        albumEntries.add(oliveAlbum);

        return albumEntries;
    }

    /**
     * 查询所有的会议图集
     *
     * @param context 上下文
     * @return 回忆图集列表
     */
    public static List<MemoriesAlbumEntry> queryAllMemoriesAlbumEntry(Context context) {
        long startTime = System.currentTimeMillis();
        List<MemoriesEntry> memoriesEntries = MemoriesHelper.loadAllMemoriesEntry(context);
        if ((memoriesEntries == null) || memoriesEntries.isEmpty()) {
            GLog.d(TAG, "[queryAllMemoriesAlbumEntry] failed to collect memoriesEntries!");
            return null;
        }
        List<MemoriesAlbumEntry> albumEntries = new ArrayList<>();
        for (MemoriesEntry memoriesEntry : memoriesEntries) {
            MemoriesAlbumEntry albumEntry = new MemoriesAlbumEntry();
            albumEntry.count = memoriesEntry.mCount;
            albumEntry.memoriesId = memoriesEntry.mMemoriesId;
            albumEntry.memoriesName = memoriesEntry.mMemoriesName;
            albumEntry.idList = memoriesEntry.mIdList;
            albumEntry.galleryIdList = memoriesEntry.mGalleryIdList;
            albumEntries.add(albumEntry);
        }
        GLog.d(TAG, "[queryAllMemoriesAlbumEntry] costTime :" + GLog.getTime(startTime));
        return albumEntries;
    }

    /**
     * 查询格式为YYYYMMDD的节日信息，保存到DateTimeEntry对象
     *
     * @return List<DateTimeEntry>
     */
    public static List<DateTimeEntry> queryDateTimeEntryForFestivals() {
        Map<String, DateTimeEntry> festivalMaps = new ArrayMap<>();

        String[] projection = new String[]{_ID, LocalColumns.MEDIA_ID, LocalColumns.DATE_TAKEN, LocalColumns.MEDIA_TYPE};
        String whereClause = SearchDBHelper.getCShotCoverAndFilterHeifWhere();
        GLog.d(TAG, "[queryDateTimeEntryForFestivals] whereClause:" + whereClause);

        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(projection).setWhere(whereClause)
            .setWhereArgs(null)
            .setOrderBy(QUERY_FOR_FESTIVALS_ORDER_CLAUSE)
            .setConvert(new CursorConvert())
            .build();

        long queryTime = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().query(req)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[queryDateTimeEntryForFestivals] queryTime :" + GLog.getTime(queryTime));
            }
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int indexId = cursor.getColumnIndex(_ID);
                int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                while (cursor.moveToNext()) {
                    int id = cursor.getInt(indexId);
                    int mediaId = cursor.getInt(indexMediaId);
                    long dateTaken = cursor.getLong(indexDateTaken);
                    int mediaType = cursor.getInt(indexMediaType);
                    MediaIdEntry entry = new MediaIdEntry();
                    entry.galleryId = id;
                    entry.mediaId = mediaId;
                    entry.dateTaken = dateTaken;
                    entry.mediaType = mediaType;
                    String date = TimeUtils.getYearMonthDayDate(dateTaken, TimeUtils.FORMAT_YYYYMMDD, Locale.ENGLISH);
                    DateTimeEntry dateTimeEntry = festivalMaps.get(date);
                    if (dateTimeEntry == null) {
                        dateTimeEntry = new DateTimeEntry();
                        dateTimeEntry.dateTaken = dateTaken;
                        dateTimeEntry.date = date;
                        dateTimeEntry.mediaIdList = new ArrayList<>();
                        dateTimeEntry.mediaIdList.add(entry);
                        festivalMaps.put(date, dateTimeEntry);
                    } else {
                        dateTimeEntry.mediaIdList.add(entry);
                    }
                }
            }
        } catch (Throwable thr) {
            GLog.e(TAG, "[queryMediaIdEntryForFestivals] query Exception:", thr);
        }

        return new ArrayList<>(festivalMaps.values());
    }

    /**
     * 查询格式位Y%的日期时间信息
     *
     * @param yearNumerical 年数字
     * @return CoverMediaEntry
     */
    public static CoverMediaEntry queryDateTimeEntryForYears(int yearNumerical) {
        String stringBuilder =
                SELECT + LocalColumns.MEDIA_ID + COMMA_SPACE + _ID + COMMA_SPACE + LocalColumns.DATE_TAKEN + COMMA_SPACE + LocalColumns.MEDIA_TYPE
                        + COMMA_SPACE + COUNT_ALL + AS + COUNT + FROM + LEFT_BRACKETS + SELECT + LocalColumns.MEDIA_ID + COMMA_SPACE
                        + GalleryStore.GalleryMedia.TAB + DOT + _ID + COMMA_SPACE + LocalColumns.DATE_TAKEN + COMMA_SPACE + LocalColumns.MEDIA_TYPE
                        + FROM + GalleryStore.GalleryMedia.TAB + WHERE + LocalColumns.YEAR + EQUAL + yearNumerical + AND
                        + DatabaseUtils.getDataValidWhere() + AND + DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB)
                        + AND + SearchDBHelper.getCShotCoverAndFilterHeifWhere() + ORDER_BY + QUERY_FOR_YEAR_ORDER_CLAUSE + RIGHT_BRACKETS;
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                        .setConvert(new CursorConvert())
                        .setQuerySql(stringBuilder)
                        .setSqlArgs(null)
                        .build();

        CoverMediaEntry entry = new CoverMediaEntry();
        long queryTime = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[queryDateTimeEntryForYears] queryTime :" + GLog.getTime(queryTime));
            }
            if (cursor != null) {
                queryTime = System.currentTimeMillis();
                int dateTakenIndex = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int mediaIdIndex = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int idIndex = cursor.getColumnIndex(_ID);
                int mediaTypeIndex = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                int countIndex = cursor.getColumnIndex(COUNT);
                if (cursor.moveToNext()) {
                    int mediaType = cursor.getInt(mediaTypeIndex);
                    int mediaId = cursor.getInt(mediaIdIndex);
                    int id = cursor.getInt(idIndex);
                    long dateTaken = cursor.getLong(dateTakenIndex);
                    int count = cursor.getInt(countIndex);
                    entry.mediaId = mediaId;
                    entry.galleryId = id;
                    entry.dateTaken = dateTaken;
                    entry.mediaType = mediaType;
                    entry.count = count;
                }
                if (GProperty.getLOG_GALLERY_OPEN()) {
                    GLog.v(TAG, "[queryDateTimeEntryForYears] moveToNextTime :" + GLog.getTime(queryTime));
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, "[queryDateTimeEntryForYears] Exception: ", e);
        }

        return entry;
    }

    /**
     * 查询格式为Y%-M%的日期时间信息
     *
     * @return CoverMediaEntry
     */
    public static CoverMediaEntry queryDateTimeEntryForMonths() {
        String stringBuilder = SELECT + LocalColumns.MEDIA_ID + COMMA_SPACE + _ID + COMMA_SPACE + LocalColumns.DATE_TAKEN + COMMA_SPACE
            + LocalColumns.MEDIA_TYPE
            + COMMA_SPACE + LocalColumns.YEAR + COMMA_SPACE + LocalColumns.MONTH + COMMA_SPACE + COUNT_ALL + AS + COUNT + FROM
            + LEFT_BRACKETS + SELECT + LocalColumns.MEDIA_ID + COMMA_SPACE + GalleryStore.GalleryMedia.TAB + DOT + _ID + COMMA_SPACE
            + LocalColumns.DATE_TAKEN + COMMA_SPACE + LocalColumns.MEDIA_TYPE + COMMA_SPACE + LocalColumns.YEAR + COMMA_SPACE
            + LocalColumns.MONTH + FROM + GalleryStore.GalleryMedia.TAB + WHERE + DatabaseUtils.getDataValidWhere() + AND
            + DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB) + AND
            + SearchDBHelper.getCShotCoverAndFilterHeifWhere() + ORDER_BY + QUERY_FOR_MONTH_ORDER_CLAUSE
            + RIGHT_BRACKETS + GROUP_BY + LocalColumns.MONTH + ORDER_BY + COUNT + ORDER_DESC + LIMIT + "1";
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(new CursorConvert())
            .setQuerySql(stringBuilder)
            .setSqlArgs(null)
            .build();
        CoverMediaEntry entry = new CoverMediaEntry();
        long queryTime = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[queryDateTimeEntryForMonths] queryTime :" + GLog.getTime(queryTime));
            }
            if (cursor != null) {
                int mediaIdIndex = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int idIndex = cursor.getColumnIndex(_ID);
                int dateTakenIndex = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int mediaTypeIndex = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                int countIndex = cursor.getColumnIndex(COUNT);
                int monthIndex = cursor.getColumnIndex(LocalColumns.MONTH);
                int yearIndex = cursor.getColumnIndex(LocalColumns.YEAR);
                queryTime = System.currentTimeMillis();
                if (cursor.moveToNext()) {
                    int mediaId = cursor.getInt(mediaIdIndex);
                    int id = cursor.getInt(idIndex);
                    long dateTaken = cursor.getLong(dateTakenIndex);
                    int mediaType = cursor.getInt(mediaTypeIndex);
                    int count = cursor.getInt(countIndex);
                    String year = cursor.getString(yearIndex);
                    String month = cursor.getString(monthIndex);
                    entry.mediaId = mediaId;
                    entry.galleryId = id;
                    entry.dateTaken = dateTaken;
                    entry.mediaType = mediaType;
                    entry.count = count;
                    entry.year = year;
                    entry.month = month;
                }
                if (GProperty.getLOG_GALLERY_OPEN()) {
                    GLog.v(TAG, "[queryDateTimeEntryForMonths] moveToNextTime :" + GLog.getTime(queryTime));
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, "[queryDateTimeEntryForMonths] query Exception: ", e);
        }

        return entry;
    }

    /**
     * 从LocalMedia表查询节日有关的媒体信息
     *
     * @return List<FestivalEntry>
     */
    public static List<FestivalEntry> queryFestivalsFromLocalMedia() {
        List<FestivalEntry> festivalEntries = new ArrayList<>();
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setQuerySql(getFestivalWhereClause())
            .setSqlArgs(null)
            .setConvert(new CursorConvert())
            .build();
        long queryTime = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[queryFestivalsFromLocalMedia] queryTime :" + GLog.getTime(queryTime));
            }
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexId = cursor.getColumnIndex(_ID);
                int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                int indexCount = cursor.getColumnIndex(COUNT);
                int indexFestivalName = cursor.getColumnIndex(FESTIVAL_NAME);
                queryTime = System.currentTimeMillis();
                while (cursor.moveToNext()) {
                    int mediaId = cursor.getInt(indexMediaId);
                    int id = cursor.getInt(indexId);
                    long dateTaken = cursor.getLong(indexDateTaken);
                    int mediaType = cursor.getInt(indexMediaType);
                    int count = cursor.getInt(indexCount);
                    String festivalName = cursor.getString(indexFestivalName);
                    CoverMediaEntry entry = new CoverMediaEntry();
                    entry.mediaId = mediaId;
                    entry.galleryId = id;
                    entry.dateTaken = dateTaken;
                    entry.mediaType = mediaType;
                    FestivalEntry festivalEntry = new FestivalEntry();
                    festivalEntry.count = count;
                    festivalEntry.festivalName = festivalName;
                    festivalEntry.coverMediaEntry = entry;
                    festivalEntries.add(festivalEntry);
                }
                if (GProperty.getLOG_GALLERY_OPEN()) {
                    GLog.v(TAG, "[queryFestivalsFromLocalMedia] moveToNextTime :" + GLog.getTime(queryTime));
                }
            }
        } catch (Throwable thr) {
            GLog.e(TAG, "[queryFestivalsFromLocalMedia] query Exception:", thr);
        }

        return festivalEntries;
    }

    private static String getFestivalWhereClause() {
        return SELECT + LocalColumns.MEDIA_ID + COMMA_SPACE + _ID + COMMA_SPACE + LocalColumns.DATE_TAKEN + COMMA_SPACE + LocalColumns.MEDIA_TYPE
                + COMMA_SPACE + FESTIVAL_NAME + COMMA_SPACE + LEVEL + COMMA_SPACE + COUNT_ALL + AS + COUNT + FROM + LEFT_BRACKETS + SELECT
                + LocalColumns.MEDIA_ID + COMMA_SPACE + GalleryStore.GalleryMedia.TAB + DOT + _ID + COMMA_SPACE + LocalColumns.DATE_TAKEN
                + COMMA_SPACE + FESTIVAL_NAME + COMMA_SPACE + LEVEL + COMMA_SPACE + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.MEDIA_TYPE
                + FROM + GalleryStore.Festival.TAB + INNER_JOIN + GalleryStore.GalleryMedia.TAB + ON + GalleryStore.GalleryMedia.TAB + DOT
                + LocalColumns.DAY + EQUAL + GalleryStore.Festival.TAB + DOT + DATE + WHERE + LEVEL + ">="
                + GeneralFestivals.MIN_LEVEL_MATCH_RECOMMEND_FESTIVAL + AND + FILTER_TYPE + BIT_AND + getFilterType(FILTER_TYPE_COMMON)
                + EQUAL + getFilterType(FILTER_TYPE_COMMON) + AND + REGION + LIKE + "'" + SearchCommonUtils.getSearchRegion().toLowerCase()
                + LIKE_PERCENT + "'" + AND + DatabaseUtils.getDataValidWhere() + AND
                + DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB) + AND + SearchDBHelper.getCShotCoverAndFilterHeifWhere()
                + ORDER_BY + QUERY_FOR_YEAR_ORDER_CLAUSE + RIGHT_BRACKETS + GROUP_BY + FESTIVAL_NAME + ORDER_BY + LEVEL + DESC;
    }

    /**
     * 标签信息查询
     *
     * @return List<LabelEntry>
     */
    public static List<LabelEntry> queryLabelEntryForLabels(List<String> labelNames) {
        Map<Integer, LabelEntry> labelMaps = new ArrayMap<>();
        List<Integer> labelIds = transferLabelNamesToLabelIds(labelNames);
        String[] selectionArgs = DatabaseUtils.getWhereArgs(labelIds);
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setQuerySql(getScanLabelWhereClause(labelIds.size()))
            .setSqlArgs(selectionArgs)
            .setConvert(new CursorConvert())
            .build();
        long queryTime = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[queryLabelEntryForLabels], queryTime :" + GLog.getTime(queryTime));
            }
            if (cursor != null) {
                int indexSceneId = cursor.getColumnIndex(SCENE_ID);
                int indexGalleryId = cursor.getColumnIndex(_ID);
                int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                int indexCount = cursor.getColumnIndex(COUNT);
                queryTime = System.currentTimeMillis();
                while (cursor.moveToNext()) {
                    int mediaId = cursor.getInt(indexMediaId);
                    int galleryId = cursor.getInt(indexGalleryId);
                    long dateTaken = cursor.getLong(indexDateTaken);
                    int count = cursor.getInt(indexCount);
                    int mediaType = cursor.getInt(indexMediaType);
                    CoverMediaEntry entry = new CoverMediaEntry();
                    entry.mediaId = mediaId;
                    entry.galleryId = galleryId;
                    entry.dateTaken = dateTaken;
                    entry.mediaType = mediaType;
                    int sceneId = cursor.getInt(indexSceneId);
                    String labelName = LabelDictionary.getLabelName(sceneId);
                    if (TextUtils.isEmpty(labelName)) {
                        if (GProperty.getLOG_GALLERY_OPEN()) {
                            GLog.d(TAG, "[queryLabelEntryForLabels], one empty labelName, sceneId is " + sceneId);
                        }
                        continue;
                    }
                    LabelEntry labelEntry = new LabelEntry();
                    labelEntry.sceneId = sceneId;
                    labelEntry.labelName = labelName;
                    labelEntry.labelKey = LabelDictionary.getLabelKey(sceneId);
                    labelEntry.coverMediaEntry = entry;
                    labelEntry.count = count;
                    labelMaps.put(sceneId, labelEntry);
                    if (GProperty.getDEBUG_LABEL()) {
                        GLog.d(TAG, "[queryLabelEntryForLabels] " + labelEntry);
                    }
                }
                if (GProperty.getLOG_GALLERY_OPEN()) {
                    GLog.v(TAG, "[queryLabelEntryForLabels], moveToNextTime :" + GLog.getTime(queryTime));
                }
                queryTime = System.currentTimeMillis();
                // filter screenshot label from slide label
                int slideLabelId = LabelDictionary.getSlideLabelId();
                LabelEntry slideLabelEntry = labelMaps.get(slideLabelId);
                if (slideLabelEntry != null) {
                    getSlideLabelFromScanLabel(slideLabelEntry);
                }
                if (GProperty.getLOG_GALLERY_OPEN()) {
                    GLog.v(TAG, "[queryLabelEntryForLabels], filter screenshot label Time :" + GLog.getTime(queryTime));
                }

                queryTime = System.currentTimeMillis();
                // add screenshot folder picture into screenshot label
                LabelEntry screenShotEntry = labelMaps.get(LabelDictionary.getScreenShotLabelId());
                if (screenShotEntry != null) {
                    addScreenShotIntoScreenShotLabel(screenShotEntry);
                }
                if (GProperty.getLOG_GALLERY_OPEN()) {
                    GLog.v(TAG, "[queryLabelEntryForLabels], add screenshot Time :" + GLog.getTime(queryTime));
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, "[queryLabelEntryForLabels], Exception = " + e);
        }
        return new ArrayList<>(labelMaps.values());
    }

    /**
     * getLabelEntries From ScanLabel with sql which is
     * <p>
     * SELECT scene_id, media_id,_id, datetaken,media_type,count(*) AS count
     * FROM
     * (
     * SELECT scene_id, media_id,datetaken,local_media._id,media_type
     * from scan_label inner join local_media on
     * scan_label._data = local_media._data
     * where
     * scan_label.invalid = 0 and is_recycled = 0 and scene_id != 93
     * and scene_id in (?,?..)
     * order by
     * datetaken ASC
     * )
     * GROUP BY scene_id
     */
    private static String getScanLabelWhereClause(int labelSize) {
        String whereQueryIn = DatabaseUtils.getWhereQueryIn(SCENE_ID, labelSize);

        return SELECT + SCENE_ID + COMMA_SPACE + LocalColumns.MEDIA_ID + COMMA_SPACE + _ID + COMMA_SPACE + LocalColumns.DATE_TAKEN + COMMA_SPACE
                + LocalColumns.MEDIA_TYPE + COMMA_SPACE + COUNT_ALL + AS + COUNT + FROM + LEFT_BRACKETS + SELECT + SCENE_ID + COMMA_SPACE
                + LocalColumns.MEDIA_ID + COMMA_SPACE + GalleryStore.GalleryMedia.TAB + DOT + _ID + COMMA_SPACE + LocalColumns.DATE_TAKEN
                + COMMA_SPACE + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.MEDIA_TYPE + FROM + GalleryStore.ScanLabel.TAB + INNER_JOIN
                + GalleryStore.GalleryMedia.TAB + ON + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA + EQUAL + GalleryStore.ScanLabel.TAB
                + DOT + LocalColumns.DATA + WHERE + DEFAULT_LABEL_DATA_WHERE + AND + SCENE_ID + NOT_EQUAL + LabelDictionary.getOtherId()
                + ((TextUtils.isEmpty(whereQueryIn)) ? TextUtil.EMPTY_STRING : (AND + whereQueryIn))
                + ORDER_BY + QUERY_LABELS_WHERE_ORDER_CLAUSE + RIGHT_BRACKETS + GROUP_BY + SCENE_ID;
    }

    private static List<Integer> transferLabelNamesToLabelIds(List<String> labelNames) {
        List<Integer> labelIds = new ArrayList<>();
        labelNames.forEach(name -> {
            int labelId = LabelDictionary.getLabelId(name);
            if (labelId > 0) {
                labelIds.add(labelId);
            }
        });
        return labelIds;
    }

    private static void getSlideLabelFromScanLabel(LabelEntry slideLabelEntry) {
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setQuerySql(getSlideLabelWhereClause())
            .setSqlArgs(null)
            .setConvert(new CursorConvert()).build();
        long start = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[getSlideLabelFromScanLabel] queryTime:" + GLog.getTime(start));
            }
            if ((cursor == null) || (cursor.getCount() == 0)) {
                GLog.d(TAG, "[getSlideLabelFromScanLabel], cursor is null!");
                return;
            }
            int indexGalleryId = cursor.getColumnIndex(_ID);
            int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
            int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
            int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
            int indexCount = cursor.getColumnIndex(COUNT);
            if (cursor.moveToNext()) {
                CoverMediaEntry coverMediaEntry = new CoverMediaEntry();
                coverMediaEntry.mediaId = cursor.getInt(indexMediaId);
                coverMediaEntry.galleryId = cursor.getInt(indexGalleryId);
                coverMediaEntry.dateTaken = cursor.getLong(indexDateTaken);
                coverMediaEntry.mediaType = cursor.getInt(indexMediaType);
                slideLabelEntry.count = cursor.getInt(indexCount);
                slideLabelEntry.coverMediaEntry = coverMediaEntry;
            }
        } catch (Exception e) {
            GLog.e(TAG, "[getSlideLabelFromScanLabel] Exception:", e);
        }
    }

    /**
     * filter screenshot label from slide label which sql is
     * <p>
     * SELECT scene_id, _id, media_id,datetaken,media_type,count(*) AS count
     * FROM
     * (
     * SELECT scene_id, media_id,datetaken,media_type
     * from scan_label inner join local_media on
     * scan_label._data = local_media._data
     * where
     * scene_id = 42 and media_id not in
     * (
     * SELECT media_id
     * from scan_label inner join local_media on
     * scan_label._data = local_media._data
     * where
     * scene_id = 43
     * )
     * order by
     * datetaken ASC
     * )
     * GROUP BY scene_id
     */
    private static String getSlideLabelWhereClause() {
        return SELECT + SCENE_ID + COMMA_SPACE + _ID + COMMA_SPACE + LocalColumns.MEDIA_ID + COMMA_SPACE + LocalColumns.DATE_TAKEN + COMMA_SPACE
                + LocalColumns.MEDIA_TYPE + COMMA_SPACE + COUNT_ALL + AS + COUNT + FROM + LEFT_BRACKETS + SELECT + SCENE_ID + COMMA_SPACE
                + GalleryStore.GalleryMedia.TAB + DOT + _ID + COMMA_SPACE + LocalColumns.MEDIA_ID + COMMA_SPACE + LocalColumns.DATE_TAKEN
                + COMMA_SPACE + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.MEDIA_TYPE + FROM + GalleryStore.ScanLabel.TAB + INNER_JOIN
                + GalleryStore.GalleryMedia.TAB + ON + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA + EQUAL + GalleryStore.ScanLabel.TAB
                + DOT + LocalColumns.DATA + WHERE + SCENE_ID + EQUAL + LabelDictionary.getSlideLabelId() + AND
                + DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB) + AND + LocalColumns.MEDIA_ID + NOT_IN + LEFT_BRACKETS
                + SELECT + LocalColumns.MEDIA_ID + FROM + GalleryStore.ScanLabel.TAB + INNER_JOIN + GalleryStore.GalleryMedia.TAB + ON
                + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA + EQUAL + GalleryStore.ScanLabel.TAB + DOT + LocalColumns.DATA
                + WHERE + SCENE_ID + " = " + LabelDictionary.getScreenShotLabelId() + RIGHT_BRACKETS + ORDER_BY + QUERY_LABELS_WHERE_ORDER_CLAUSE
                + RIGHT_BRACKETS + GROUP_BY + SCENE_ID;
    }

    private static void addScreenShotIntoScreenShotLabel(LabelEntry screenShotLabelEntry) {
        List<MediaIdEntry> screenShotEntryList = queryScreenShotsFromScanLabel();
        if (!screenShotEntryList.isEmpty()) {
            addAllMediaIdEntry(screenShotEntryList, queryScreenShotsFromMediaDB());

            Collections.sort(screenShotEntryList);

            MediaIdEntry mediaIdEntry = screenShotEntryList.get(0);
            CoverMediaEntry coverMediaEntry = new CoverMediaEntry();
            coverMediaEntry.galleryId = mediaIdEntry.galleryId;
            coverMediaEntry.mediaId = mediaIdEntry.mediaId;
            coverMediaEntry.dateTaken = mediaIdEntry.dateTaken;
            coverMediaEntry.mediaType = mediaIdEntry.mediaType;
            screenShotLabelEntry.coverMediaEntry = coverMediaEntry;
            screenShotLabelEntry.count = screenShotEntryList.size();
        }
    }

    private static List<MediaIdEntry> queryScreenShotsFromScanLabel() {
        ArrayList<MediaIdEntry> mediaIdList = new ArrayList<>();
        RawQueryReq<Cursor> rawQueryReq = new RawQueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setConvert(new CursorConvert())
            .setQuerySql(queryScreenShotLabelWhereClause())
            .setSqlArgs(null)
            .build();
        long start = System.currentTimeMillis();
        try (Cursor cursor = DataAccess.getAccess().rawQuery(rawQueryReq)) {
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[queryScreenShotsFromScanLabel] queryTime:" + GLog.getTime(start));
            }
            if ((cursor == null) || (cursor.getCount() == 0)) {
                GLog.d(TAG, "[queryScreenShotsFromScanLabel], cursor is null!");
                return mediaIdList;
            }
            int indexId = cursor.getColumnIndex(_ID);
            int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
            int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
            int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
            start = System.currentTimeMillis();
            while (cursor.moveToNext()) {
                MediaIdEntry entry = new MediaIdEntry();
                entry.galleryId = cursor.getInt(indexId);
                entry.mediaId = cursor.getInt(indexMediaId);
                entry.dateTaken = cursor.getLong(indexDateTaken);
                entry.mediaType = cursor.getInt(indexMediaType);
                mediaIdList.add(entry);
            }
            if (GProperty.getLOG_GALLERY_OPEN()) {
                GLog.v(TAG, "[queryScreenShotsFromScanLabel] whileTime:" + GLog.getTime(start));
            }
        } catch (Exception e) {
            GLog.e(TAG, "[queryScreenShotsFromScanLabel] Exception:", e);
        }
        return mediaIdList;
    }

    /**
     * queryScreenShotsFromScanLabel which sql is
     * <p>
     * SELECT local_media._id,media_id,datetaken,media_type
     * from scan_label inner join local_media on
     * scan_label._data = local_media._data
     * where
     * scene_id = 43 and scan_label.invalid = 0 and is_recycled = 0
     */
    private static String queryScreenShotLabelWhereClause() {
        return SELECT + GalleryStore.GalleryMedia.TAB + DOT + _ID + COMMA_SPACE + LocalColumns.MEDIA_ID + COMMA_SPACE + GalleryStore.GalleryMedia.TAB
                + DOT + LocalColumns.MEDIA_TYPE + COMMA_SPACE + LocalColumns.DATE_TAKEN + FROM + GalleryStore.ScanLabel.TAB + INNER_JOIN
                + GalleryStore.GalleryMedia.TAB + ON + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA + EQUAL + GalleryStore.ScanLabel.TAB
                + DOT + LocalColumns.DATA + WHERE + LEFT_BRACKETS + SCENE_ID + EQUAL + LabelDictionary.getScreenShotLabelId() + RIGHT_BRACKETS
                + AND + DatabaseUtils.getDataValidWhere(GalleryStore.ScanLabel.TAB) + AND + IS_RECYCLED + EQUAL_ZERO + ORDER_BY
                + LocalColumns.DATE_TAKEN + DESC;
    }

    /**
     * 查询最近添加的媒体列表
     *
     * @param context 上下文
     * @return List<MediaIdEntry>
     */
    public static List<MediaIdEntry> queryMediaIdEntryByRecentlyAdded(Context context) {
        ArrayList<MediaIdEntry> mediaIdList = new ArrayList<>();
        String[] projection = new String[]{_ID, LocalColumns.MEDIA_ID, LocalColumns.DATE_MODIFIED, LocalColumns.MEDIA_TYPE};
        String orderClause = LocalColumns.DATE_MODIFIED + ORDER_DESC;
        String whereClause = SearchDBHelper.getRecentlyAddedWhereClause(context);
        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(projection)
                .setWhere(whereClause)
                .setWhereArgs(null)
                .setOrderBy(orderClause)
                .setConvert(new CursorConvert())
                .build();
        GLog.d(TAG, "[queryMediaIdEntryByRecentlyAdded], whereClause:" + whereClause);
        try (Cursor cursor = DataAccess.getAccess().query(req)) {
            if ((cursor == null) || (cursor.getCount() == 0)) {
                GLog.d(TAG, "[queryMediaIdEntryByRecentlyAdded], cursor is null!");
                return mediaIdList;
            }
            int indexId = cursor.getColumnIndex(_ID);
            int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
            int indexDateModified = cursor.getColumnIndex(LocalColumns.DATE_MODIFIED);
            int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
            GLog.d(TAG, "[queryMediaIdEntryByRecentlyAdded], cursor.count:" + cursor.getCount());
            while (cursor.moveToNext()) {
                MediaIdEntry entry = new MediaIdEntry();
                entry.galleryId = cursor.getInt(indexId);
                entry.mediaId = cursor.getInt(indexMediaId);
                entry.dateTaken = cursor.getLong(indexDateModified);
                entry.mediaType = cursor.getInt(indexMediaType);
                mediaIdList.add(entry);
            }
        } catch (Exception e) {
            GLog.e(TAG, "[queryMediaIdEntryByRecentlyAdded] Exception:", e);
        }
        return mediaIdList;
    }

    public static List<AlbumEntry> queryMediaIdEntryByLocalAlbum(List<AlbumEntry> normalAlbumList) {
        if ((normalAlbumList == null) || normalAlbumList.isEmpty()) {
            return null;
        }
        for (AlbumEntry albumEntry : normalAlbumList) {
            if (albumEntry.supportCShot) {
                synchronized (albumEntry.parentList) {
                    if (albumEntry.parentList.isEmpty()
                            && (albumEntry.bucketIdList.size() > 0)
                            && !TextUtils.isEmpty(albumEntry.originalBucketName)) {
                        ArrayList<String> parentList = null;
                        if (albumEntry.type == TYPE_CAMERA) {
                            parentList = CShotUtils.getCShotParentPath();
                        } else if (albumEntry.type == TYPE_CARD_CASE) {
                            parentList = CShotUtils.getCardCaseCshotParentList();
                        } else {
                            parentList = CShotUtils.getSelfAlbumCshotParentList(albumEntry.originalBucketName);
                        }
                        if (!parentList.isEmpty()) {
                            albumEntry.parentList.addAll(parentList);
                        }
                    }
                }
            }
        }
        String[] projection = new String[]{_ID, LocalColumns.MEDIA_ID, LocalColumns.DATE_TAKEN, LocalColumns.BUCKET_ID, LocalColumns.DATA,
                LocalColumns.CSHOT_ID, LocalColumns.MEDIA_TYPE};
        String orderClause = LocalColumns.DATE_TAKEN + ORDER_DESC;
        int size = normalAlbumList.size();
        int offset = 0;
        while (size > 0) {
            List<AlbumEntry> list = null;
            if (size > BATCH_COUNT_QUERY_ALBUM) {
                list = normalAlbumList.subList(offset, offset + BATCH_COUNT_QUERY_ALBUM);
                offset += BATCH_COUNT_QUERY_ALBUM;
                size -= BATCH_COUNT_QUERY_ALBUM;
            } else {
                list = normalAlbumList.subList(offset, offset + size);
                offset += size;
                size -= size;
            }
            String whereClause = getLocalAlbumWhereClause(list);
            QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(projection)
                .setWhere(whereClause)
                .setOrderBy(orderClause)
                .setConvert(new CursorConvert())
                .build();
            try (Cursor cursor = DataAccess.getAccess().query(req)) {
                if ((cursor == null) || (cursor.getCount() == 0)) {
                    GLog.d(TAG, "[queryMediaIdEntryByLocalAlbum], cursor is null!");
                    continue;
                }
                int indexId = cursor.getColumnIndex(_ID);
                int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexBucketId = cursor.getColumnIndex(LocalColumns.BUCKET_ID);
                int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int indexData = cursor.getColumnIndex(LocalColumns.DATA);
                int indexCShotId = cursor.getColumnIndex(LocalColumns.CSHOT_ID);
                int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                while (cursor.moveToNext()) {
                    MediaIdEntry entry = new MediaIdEntry();
                    entry.galleryId = cursor.getInt(indexId);
                    entry.mediaId = cursor.getInt(indexMediaId);
                    entry.dateTaken = cursor.getLong(indexDateTaken);
                    entry.mediaType = cursor.getInt(indexMediaType);
                    int bucketId = cursor.getInt(indexBucketId);
                    long cShotId = cursor.getLong(indexCShotId);
                    if (DatabaseUtils.isCShotIdValid(cShotId)) {
                        String filePath = cursor.getString(indexData);
                        if (filePath == null) {
                            continue;
                        }
                        filePath = filePath.toLowerCase();
                        for (AlbumEntry albumEntry : list) {
                            if (albumEntry.supportCShot && !albumEntry.parentList.isEmpty()) {
                                for (String parentPath : albumEntry.parentList) {
                                    if (filePath.startsWith(parentPath.toLowerCase())) {
                                        if (!albumEntry.mediaIdEntries.contains(entry)) {
                                            albumEntry.mediaIdEntries.add(entry);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        for (AlbumEntry albumEntry : list) {
                            if (albumEntry.isWrappedBucketIdList(bucketId)) {
                                if (!albumEntry.mediaIdEntries.contains(entry)) {
                                    albumEntry.mediaIdEntries.add(entry);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                GLog.e(TAG, "[queryMediaIdEntryByLocalAlbum] Exception:", e);
            }
        }
        return normalAlbumList;
    }

    private static String getLocalAlbumWhereClause(List<AlbumEntry> albumEntries) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(LEFT_BRACKETS);
        int size = albumEntries.size();
        for (int i = 0; i < size; i++) {
            AlbumEntry albumEntry = albumEntries.get(i);
            buffer.append("(bucket_id IN (");
            if (albumEntry.bucketIdList.size() > 0) {
                for (int id : albumEntry.bucketIdList) {
                    buffer.append(id).append(COMMA);
                }
                int lastIndex = buffer.length() - 1;
                if (buffer.lastIndexOf(COMMA) == lastIndex) {
                    buffer.deleteCharAt(lastIndex);
                }
            }
            buffer.append(RIGHT_BRACKETS);
            if (albumEntry.supportCShot && albumEntry.bucketIdList.size() > 0 && !TextUtils.isEmpty(albumEntry.originalBucketName)) {
                ArrayList<String> parentList = albumEntry.parentList;
                buffer.append(" OR (_id in (");
                buffer.append(DatabaseUtils.getCShotBestPicWhereClauseById());
                buffer.append(RIGHT_BRACKETS);
                if (!parentList.isEmpty()) {
                    buffer.append(BucketHelper.addParent(parentList));
                }
                buffer.append(RIGHT_BRACKETS);
            }
            buffer.append(RIGHT_BRACKETS);
            if (i < (size - 1)) {
                buffer.append(OR);
            }
        }
        buffer.append(RIGHT_BRACKETS);
        buffer.append(" AND media_type IN (1, 3)");
        return buffer.toString();
    }

    public static List<MediaIdEntry> queryMediaIdEntryBySpecialKeywordGif(Context context) {
        ArrayList<MediaIdEntry> mediaIdList = new ArrayList<>();
        String[] projection = new String[]{_ID, LocalColumns.MEDIA_ID, LocalColumns.DATE_TAKEN};
        String orderClause = LocalColumns.DATE_TAKEN + ORDER_DESC;
        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(projection)
            .setWhere(getSpecialKeywordGifWhereClause())
            .setOrderBy(orderClause)
            .setConvert(new CursorConvert())
            .build();
        try (Cursor cursor = DataAccess.getAccess().query(req)) {
            if ((cursor == null) || (cursor.getCount() == 0)) {
                GLog.d(TAG, "[queryMediaIdEntryBySpecialKeywordGif], cursor is null!");
                return mediaIdList;
            }
            int indexMediaId = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
            int indexId = cursor.getColumnIndex(_ID);
            int indexDateTaken = cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATE_TAKEN);
            while (cursor.moveToNext()) {
                MediaIdEntry entry = new MediaIdEntry();
                entry.mediaId = cursor.getInt(indexMediaId);
                entry.galleryId = cursor.getInt(indexId);
                entry.dateTaken = cursor.getLong(indexDateTaken);
                mediaIdList.add(entry);
            }
        } catch (Exception e) {
            GLog.e(TAG, "[queryMediaIdEntryBySpecialKeywordGif] Exception:", e);
        }
        return mediaIdList;
    }

    private static String getSpecialKeywordGifWhereClause() {
        return LEFT_BRACKETS + DatabaseUtils.getWhereIsNotCShot() + " OR _id IN (" + DatabaseUtils.getCShotBestPicWhereClauseById() + "))" + AND
                + "media_type IN (1)" + " AND mime_type = 'image/gif'";
    }

    /**
     * select media_id,datetaken from local_media
     * where
     * ((cshot_id = 0 )
     * OR media_id IN ( SELECT media_id FROM local_media WHERE (cshot_id != 0 ) GROUP BY cshot_id HAVING _data =  MIN (_data))
     * )
     * AND _display_name LIKE '%b%.%'
     */
    public static List<MediaIdEntry> matchFileNameFromLocalMedia(String keyword) {
        if (TextUtils.isEmpty(keyword)) {
            GLog.w(TAG, "[matchFileNameFromLocalMedia], keyword:" + keyword);
            return null;
        }
        String whereClause =
                LEFT_BRACKETS + DatabaseUtils.getWhereIsNotCShot() + OR + _ID + IN + LEFT_BRACKETS + DatabaseUtils.getCShotBestPicWhereClauseById()
                        + RIGHT_BRACKETS + RIGHT_BRACKETS + AND + LocalColumns.DISPLAY_NAME + LIKE + QUOTE + LIKE_PERCENT + keyword
                        + LIKE_PERCENT + DOT + LIKE_PERCENT + QUOTE;

        QueryReq<List<MediaIdEntry>> req = new QueryReq.Builder<List<MediaIdEntry>>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(MediaIdEntryListConvert.getPROJECTION())
                .setWhere(whereClause)
                .setConvert(new MediaIdEntryListConvert())
                .build();
        long startTime = System.currentTimeMillis();
        try {
            return DataAccess.getAccess().query(req);
        } catch (Exception e) {
            GLog.e(TAG, "[matchFileNameFromLocalMedia], failed to match!", e);
        } finally {
            GLog.d(TAG, "[matchFileNameFromLocalMedia], query cost time:" + GLog.getTime(startTime));
        }
        return null;
    }

    /**
     * query mediaId entry by ShootDateTime list.
     * <p>
     * DateRange {
     * public long start;
     * public long end;
     * public String condition;
     * }
     * condition: unit + divider + value
     * unit: %w = weekday, this value should match format %d
     * %Y = year, this value should match format %04d
     * %m = month, this value should match format %02d
     * %d = day, this value should match format %02d
     * divider: ,
     * example: %w, '6', '0'    = every each Saturday & Sunday, that's every Weekend
     * %m, '05'                 = every each May
     * %Y-%m-%d, '2016-02-21'   = a whole day of 2016-02-21
     *
     * @return ShootDateTime list
     */
    public static List<ShootDateTime> queryMediaIdByDateRanges(Context context, List<ShootDateTime> completionDatetime) {
        if ((completionDatetime == null) || (completionDatetime.size() == 0)) {
            return null;
        }
        List<ShootDateTime> result = new ArrayList<>();
        List<ShootDateTime> dateRangeList = new ArrayList<>();
        List<ShootDateTime> conditionList = new ArrayList<>();

        for (ShootDateTime dateTime : completionDatetime) {
            if (dateTime.dateRange != null && TextUtils.isEmpty(dateTime.dateRange.condition)) {
                dateRangeList.add(dateTime);
            } else {
                conditionList.add(dateTime);
            }
        }
        if (!conditionList.isEmpty()) {
            queryMediaIdByConditionList(context, conditionList);
        }
        if (!dateRangeList.isEmpty()) {
            queryMediaIdByDateRangeList(dateRangeList);
        }
        if (!conditionList.isEmpty()) {
            result.addAll(conditionList);
        }
        if (!dateRangeList.isEmpty()) {
            result.addAll(dateRangeList);
        }
        return result;
    }

    /**
     * query media id list by shootDateTime list, there are some date ranges in the list.
     * according to range, to build the where clause using condition in ranges.
     * <p>
     * DateRange {
     * public long start;
     * public long end;
     * public String condition;
     * }
     * condition: unit + divider + value
     * unit: %w = weekday, this value should match format %d
     * %Y = year, this value should match format %04d
     * %m = month, this value should match format %02d
     * %d = day, this value should match format %02d
     * divider: ,
     * example: %w, '6', '0'    = every each Saturday & Sunday, that's every Weekend
     * %m, '05'                 = every each May
     * %Y-%m-%d, '2016-02-21'   = a whole day of 2016-02-21
     */
    private static void queryMediaIdByConditionList(Context context, List<ShootDateTime> conditionList) {
        Map<String, List<String>> formatConditionMaps = new ArrayMap<>();
        for (int i = 0, n = conditionList.size(); i < n; i++) {
            ShootDateTime dateTime = conditionList.get(i);
            List<String> conditionMaps = formatConditionMaps.get(dateTime.format);
            if (conditionMaps == null) {
                conditionMaps = new ArrayList<>();
                conditionMaps.add(dateTime.conditions);
                formatConditionMaps.put(dateTime.format, conditionMaps);
            } else {
                conditionMaps.add(dateTime.conditions);
            }
        }
        GLog.d(TAG, "[queryMediaIdByConditionList], formatConditionMaps is " + formatConditionMaps);

        for (Map.Entry<String, List<String>> entry : formatConditionMaps.entrySet()) {
            String format = entry.getKey();
            if (TextUtils.isEmpty(format)) {
                continue;
            }
            List<String> formatConditions = entry.getValue();
            if ((formatConditions == null) || formatConditions.isEmpty()) {
                continue;
            }
            queryMediaIdByDateFormat(context, format, conditionList);
        }
    }

    /**
     * query mediaId entry by dateRange using condition and format.
     * <p>
     * DateRange {
     * public long start;
     * public long end;
     * public String condition;
     * }
     * condition: unit + divider + value
     * unit: %w = weekday, this value should match format %d
     * %Y = year, this value should match format %04d
     * %m = month, this value should match format %02d
     * %d = day, this value should match format %02d
     * divider: ,
     * example: %w, '6', '0'    = every each Saturday & Sunday, that's every Weekend
     * %m, '05'                 = every each May
     * %Y-%m-%d, '2016-02-21'   = a whole day of 2016-02-21
     */
    private static void queryMediaIdByDateFormat(Context context, String format, List<ShootDateTime> conditionList) {

        Map<String, List<MediaIdEntry>> mediaIdConditionMap = new ArrayMap<>();
        String whereClause = getDateRangeQueryConditionWhereClause(conditionList);
        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(DATE_RANGE_PROJECTIONS)
            .setWhere(whereClause)
            .setWhereArgs(null)
            .setOrderBy(ORDER_CLAUSE_DATE_TAKEN)
            .setConvert(new CursorConvert())
            .build();
        GLog.d(TAG, "[queryMediaIdByDateFormat], whereClause:" + whereClause);
        try (Cursor cursor = DataAccess.getAccess().query(req)) {
            if (cursor != null) {
                int indexID = cursor.getColumnIndex(_ID);
                int indexMediaID = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                GLog.d(TAG, "[queryMediaIdByDateFormat], cursor.count:" + cursor.getCount());
                while (cursor.moveToNext()) {
                    MediaIdEntry entry = new MediaIdEntry();
                    entry.galleryId = cursor.getInt(indexID);
                    entry.mediaId = cursor.getInt(indexMediaID);
                    entry.dateTaken = cursor.getLong(indexDateTaken);
                    entry.mediaType = cursor.getInt(indexMediaType);
                    String date = TimeUtils.getStrfTimeDate(entry.dateTaken, format);
                    if (TextUtils.isEmpty(date)) {
                        continue;
                    }
                    List<MediaIdEntry> mediaIdEntries = mediaIdConditionMap.get(date);
                    if (mediaIdEntries == null) {
                        mediaIdEntries = new ArrayList<>();
                        mediaIdEntries.add(entry);
                        mediaIdConditionMap.put(date, mediaIdEntries);
                    } else {
                        mediaIdEntries.add(entry);
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "[queryMediaIdByDateFormat] query Exception:", e);
        }

        for (Map.Entry<String, List<MediaIdEntry>> entry : mediaIdConditionMap.entrySet()) {
            String condition = entry.getKey();
            if (TextUtils.isEmpty(condition)) {
                continue;
            }
            for (ShootDateTime shootDateTime : conditionList) {
                if (shootDateTime.isWrappedCondition(condition)) {
                    List<MediaIdEntry> mediaIdEntries = mediaIdConditionMap.get(condition);
                    if (mediaIdEntries != null) {
                        shootDateTime.mediaIdEntries.addAll(mediaIdEntries);
                    }
                    break;
                }
            }
        }
    }

    private static String getDateRangeQueryConditionWhereClause(List<ShootDateTime> conditionList) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(LEFT_BRACKETS).append(DatabaseUtils.getWhereIsNotCShot());
        buffer.append(OR).append(_ID).append(SQLGrammar.IN).append(SQLGrammar.LEFT_BRACKETS);
        buffer.append(DatabaseUtils.getCShotBestPicWhereClauseById());
        buffer.append("))");
        buffer.append(" AND (");
        for (ShootDateTime shootDateTime : conditionList) {
            if (shootDateTime != null) {
                String format = shootDateTime.format;
                String condition = shootDateTime.conditions;
                String strftime = String.format(STRFTIME_SEARCH_FORMAT, format);
                buffer.append(strftime);
                buffer.append("IN (");
                buffer.append(condition);
                buffer.append(" )");
                buffer.append(OR);
            }
        }
        int lastIndex = buffer.lastIndexOf("OR");
        if (lastIndex != -1) {
            buffer.delete(lastIndex, buffer.length());
        }
        buffer.append(RIGHT_BRACKETS);
        return buffer.toString();
    }

    /**
     * query mediaId entry by dateRange using start and end.
     * <p>
     * DateRange {
     * public long start;
     * public long end;
     * public String condition;
     * }
     * condition: unit + divider + value
     * unit: %w = weekday, this value should match format %d
     * %Y = year, this value should match format %04d
     * %m = month, this value should match format %02d
     * %d = day, this value should match format %02d
     * divider: ,
     * example: %w, '6', '0'    = every each Saturday & Sunday, that's every Weekend
     * %m, '05'                 = every each May
     * %Y-%m-%d, '2016-02-21'   = a whole day of 2016-02-21
     */
    private static void queryMediaIdByDateRangeList(List<ShootDateTime> dateRangeList) {
        String whereClause = getDateRangeQueryWhereClause(dateRangeList);
        QueryReq<Cursor> req = new QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(DATE_RANGE_PROJECTIONS)
            .setWhere(whereClause)
            .setWhereArgs(null)
            .setConvert(new CursorConvert()).setOrderBy(ORDER_CLAUSE_DATE_TAKEN)
            .build();
        GLog.d(TAG, "[queryMediaIdByDateRangeList], whereClause:" + whereClause);
        try (Cursor cursor = DataAccess.getAccess().query(req)) {
            if (cursor != null) {
                int indexID = cursor.getColumnIndex(_ID);
                int indexMediaID = cursor.getColumnIndex(LocalColumns.MEDIA_ID);
                int indexDateTaken = cursor.getColumnIndex(LocalColumns.DATE_TAKEN);
                int indexMediaType = cursor.getColumnIndex(LocalColumns.MEDIA_TYPE);
                GLog.d(TAG, "[queryMediaIdByDateRangeList], cursor.count:" + cursor.getCount());
                while (cursor.moveToNext()) {
                    MediaIdEntry entry = new MediaIdEntry();
                    entry.galleryId = cursor.getInt(indexID);
                    entry.mediaId = cursor.getInt(indexMediaID);
                    entry.dateTaken = cursor.getLong(indexDateTaken);
                    entry.mediaType = cursor.getInt(indexMediaType);
                    for (ShootDateTime shootDateTime : dateRangeList) {
                        if (shootDateTime.isWrappedDateRange(entry.dateTaken)) {
                            shootDateTime.mediaIdEntries.add(entry);
                        }
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "[queryMediaIdByDateRangeList] query Exception:", e);
        }
    }

    private static String getDateRangeQueryWhereClause(List<ShootDateTime> shootDateTimes) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(SearchDBHelper.getCShotCoverAndFilterHeifWhere());
        buffer.append(" AND (");
        for (ShootDateTime shootDateTime : shootDateTimes) {
            DateRange dateRange = shootDateTime.dateRange;
            buffer.append("datetaken >= ").append(dateRange.start).append(" AND datetaken <= ").append(dateRange.end);
            buffer.append(OR);
        }
        int lastIndex = buffer.lastIndexOf("OR");
        if (lastIndex != -1) {
            buffer.delete(lastIndex, buffer.length());
        }
        buffer.append(RIGHT_BRACKETS);
        return buffer.toString();
    }

    public static Cursor getSearchInfoRawQueryReq(String selection, String[] selectionArgs, String limit, String order) {
        Cursor cursor = new RawQueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                .setQuerySql(getSearchInfoViewSql(selection, limit, order))
                .setSqlArgs(selectionArgs)
                .setConvert(new CursorConvert())
                .build()
                .exec();
        // 只有每日任务即selection == null时才走此处更新cursorId，防止数据同步和每日任务同时造成的混乱
        if ((selection == null) && (cursor != null) && (cursor.getCount() > 0)) {
            cursor.moveToFirst();
            int idIdx = cursor.getColumnIndex(_ID);
            if (idIdx >= 0) {
                setCursorId(cursor.getLong(idIdx));
            }
            GLog.d(TAG, LogFlag.DL, () -> "getSearchInfoRawQueryReq, cursorId = " + sCursorId);
            cursor.moveToPosition(-1);
        }
        return cursor;
    }

    /**
     * 设置DmpQuery分页查询的值
     * @param cursorId 分页查询的起始值
     */
    public static void setCursorId(Long cursorId) {
        sCursorId = cursorId;
    }

    /**
     * 给中子搜索中台提供的查询接口的查询语句
     * SELECT
     * local_media._id as _id, media_id, local_media._data as path, datetaken, date_modified, face_name,
     * replace(address,',','|') as address, tags , english_tags , ocr, bucket_display_name as bucket_name, bucket_id,
     * _display_name, title, _size, local_media.media_type, mime_type, duration, longitude, latitude,
     * group_concat(festival_name,'|') as festival_name, memory_name, sex, age, tagflags "
     * from local_media"
     * left outer join (select group_concat(scene_id,'|') as tags,
     * group_concat (scene_id,'|') AS english_tags, _data from scan_label where scene_id > 0 group by _data)scan_label on
     * local_media._data = scan_label._data"
     * left outer join (select group_concat(group_name,'|') as face_name,
     * group_concat(sex,'|') as sex, group_concat(age,'|') as age, _data from scan_face group by _data) scan_face on local_media._data =
     * scan_face._data"
     * left outer join (select address, gps_key from geo_route)geo_route on local_media.gps_key = geo_route.gps_key"
     * left outer join (select festival_name, date, region from festival where region = 'cn,oc' 、
     * and filterType & FILTER_TYPE_COMMON = FILTER_TYPE_COMMON) festival on local_media.day = festival.date"
     * left outer join (select content as ocr, _data from ocr_pages group by _data) ocr_pages on local_media._data = ocr_pages._data"
     * left outer join (select _data, group_concat(name,'|') as memory_name from memories_setmap inner join memories_set on memories_setmap.set_id
     * = memories_set._id group by _data) memories_setmap on local_media._data = memories_setmap._data"
     * group by local_media._data;";
     *
     * @param selection 选择条件
     * @param limit     数量限制
     * @param order     排序方式
     */
    @VisibleForTesting
    public static String getSearchInfoViewSql(String selection, String limit, String order) {
        GLog.d(TAG, LogFlag.DL, () -> "getSearchInfoViewSql, cursorId = " + sCursorId + ", limit = " + limit
                + ", order = " + order + ", selection is null = " + (selection == null));
        String commaOrRightBracket = ",'|')";
        String localWhere =
                LEFT_BRACKETS + SELECT + LocalColumns._ID + COMMA + LocalColumns.MEDIA_ID + COMMA + LocalColumns.DATA + COMMA
                        + LocalColumns.DATE_TAKEN + COMMA + LocalColumns.DATE_MODIFIED + COMMA + LocalColumns.BUCKET_NAME + COMMA
                        + LocalColumns.BUCKET_ID + COMMA + LocalColumns.DISPLAY_NAME + COMMA + LocalColumns.TITLE + COMMA
                        + LocalColumns.SIZE + COMMA + LocalColumns.MEDIA_TYPE + COMMA + LocalColumns.MIME_TYPE + COMMA
                        + LocalColumns.DURATION + COMMA + LocalColumns.LONGITUDE + COMMA + LocalColumns.LATITUDE + COMMA
                        + LocalColumns.TAGFLAGS + COMMA + LocalColumns.GPS_KEY + COMMA + LocalColumns.DAY + FROM + GalleryStore.GalleryMedia.TAB
                        + WHERE + DatabaseUtils.getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB)
                        + ((selection == null) ? (AND + LocalColumns._ID + GREATER_THAN + sCursorId + LIMIT + limit) : (AND + selection))
                        + RIGHT_BRACKETS;
        String orderClause = (selection == null)
                ? (ORDER_BY + GalleryStore.GalleryMedia.TAB + DOT + _ID + DESC)
                : ((TextUtils.isEmpty(order)
                    ? TextUtil.EMPTY_STRING
                    : ORDER_BY + order));
        return SELECT + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns._ID + AS + GalleryStore.SearchInfoColumns._ID
                + COMMA + LocalColumns.MEDIA_ID + AS + GalleryStore.SearchInfoColumns.MEDIA_ID + COMMA + GalleryStore.GalleryMedia.TAB
                + DOT + LocalColumns.DATA + AS + GalleryStore.SearchInfoColumns.DATA + COMMA + LocalColumns.DATE_TAKEN + AS
                + GalleryStore.SearchInfoColumns.DATE_TAKEN + COMMA + LocalColumns.DATE_MODIFIED + AS + GalleryStore.SearchInfoColumns.DATE_MODIFIED
                + COMMA + GalleryStore.SearchInfoColumns.FACE_NAME + COMMA + REPLACE + LEFT_BRACKETS + GalleryStore.GeoRoute.ADDRESS + ",',','|')"
                + AS + GalleryStore.SearchInfoColumns.ADDRESS + COMMA + GalleryStore.SearchInfoColumns.TAGS + COMMA
                + GalleryStore.SearchInfoColumns.ENGLISH_TAGS + COMMA
                + GalleryStore.SearchInfoColumns.OCR + COMMA + LocalColumns.BUCKET_NAME + AS + GalleryStore.SearchInfoColumns.BUCKET_NAME
                + COMMA + LocalColumns.BUCKET_ID + AS + GalleryStore.SearchInfoColumns.BUCKET_ID + COMMA + LocalColumns.DISPLAY_NAME
                + AS + GalleryStore.SearchInfoColumns.DISPLAY_NAME + COMMA + LocalColumns.TITLE + AS + GalleryStore.SearchInfoColumns.TITLE
                + COMMA + LocalColumns.SIZE + AS + GalleryStore.SearchInfoColumns.SIZE + COMMA + GalleryStore.GalleryMedia.TAB + DOT
                + LocalColumns.MEDIA_TYPE + AS + GalleryStore.SearchInfoColumns.MEDIA_TYPE + COMMA + LocalColumns.MIME_TYPE + AS
                + GalleryStore.SearchInfoColumns.MIME_TYPE + COMMA + LocalColumns.DURATION + AS + GalleryStore.SearchInfoColumns.DURATION
                + COMMA + LocalColumns.LONGITUDE + AS + GalleryStore.SearchInfoColumns.LONGITUDE + COMMA + LocalColumns.LATITUDE
                + AS + GalleryStore.SearchInfoColumns.LATITUDE + COMMA + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.Festival.FESTIVAL_NAME
                + commaOrRightBracket + AS + GalleryStore.SearchInfoColumns.FESTIVAL_NAME + COMMA + GalleryStore.SearchInfoColumns.MEMORY_NAME + COMMA
                + GalleryStore.SearchInfoColumns.SEX + COMMA + GalleryStore.SearchInfoColumns.AGE + COMMA
                + GalleryStore.SearchInfoColumns.TAG_FLAGS + FROM + localWhere + GalleryStore.GalleryMedia.TAB + LEFT_OUTER_JOIN + LEFT_BRACKETS
                + SELECT + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.ScanLabel.SCENE_ID + commaOrRightBracket + AS
                + GalleryStore.SearchInfoColumns.TAGS + COMMA
                + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.ScanLabel.SCENE_ID + commaOrRightBracket + AS
                + GalleryStore.SearchInfoColumns.ENGLISH_TAGS + COMMA
                + GalleryStore.ScanLabel.DATA + FROM + GalleryStore.ScanLabel.TAB + WHERE + GalleryStore.ScanLabel.SCENE_ID
                + GREATER_THAN_ZERO + GROUP_BY + GalleryStore.ScanLabel.DATA + RIGHT_BRACKETS + GalleryStore.ScanLabel.TAB + ON
                + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA + EQUAL + GalleryStore.ScanLabel.TAB + DOT + GalleryStore.ScanLabel.DATA
                + LEFT_OUTER_JOIN + LEFT_BRACKETS + SELECT + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.ScanFace.GROUP_NAME + commaOrRightBracket
                + AS + GalleryStore.SearchInfoColumns.FACE_NAME + COMMA
                + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.ScanFace.SEX + commaOrRightBracket + AS + GalleryStore.SearchInfoColumns.SEX + COMMA
                + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.ScanFace.AGE + commaOrRightBracket + AS + GalleryStore.SearchInfoColumns.AGE + COMMA
                + GalleryStore.ScanFace.DATA + FROM + GalleryStore.ScanFace.TAB
                + GROUP_BY + GalleryStore.ScanFace.DATA + RIGHT_BRACKETS + GalleryStore.ScanFace.TAB + ON + GalleryStore.GalleryMedia.TAB
                + DOT + LocalColumns.DATA + EQUAL + GalleryStore.ScanFace.TAB + DOT + GalleryStore.ScanFace.DATA + LEFT_OUTER_JOIN
                + LEFT_BRACKETS + SELECT + GalleryStore.GeoRoute.ADDRESS + COMMA + GalleryStore.GeoRoute.GPS_KEY + FROM + GalleryStore.GeoRoute.TAB
                + RIGHT_BRACKETS + GalleryStore.GeoRoute.TAB + ON + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.GPS_KEY + EQUAL
                + GalleryStore.GeoRoute.TAB + DOT + GalleryStore.GeoRoute.GPS_KEY + LEFT_OUTER_JOIN + LEFT_BRACKETS + SELECT
                + GalleryStore.Festival.FESTIVAL_NAME + COMMA + GalleryStore.Festival.DATE + COMMA + GalleryStore.Festival.REGION + FROM
                + GalleryStore.Festival.TAB + WHERE + GalleryStore.Festival.REGION + EQUAL + "'cn,oc'" + AND + FILTER_TYPE + BIT_AND
                + getFilterType(FILTER_TYPE_COMMON) + EQUAL + getFilterType(FILTER_TYPE_COMMON) + RIGHT_BRACKETS + GalleryStore.Festival.TAB
                + ON + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DAY + EQUAL + GalleryStore.Festival.TAB + DOT
                + GalleryStore.Festival.DATE + LEFT_OUTER_JOIN + LEFT_BRACKETS + SELECT + GalleryStore.OcrPages.CONTENT + AS
                + GalleryStore.SearchInfoColumns.OCR + COMMA + GalleryStore.OcrPages.DATA + FROM + GalleryStore.OcrPages.TAB + GROUP_BY
                + GalleryStore.OcrPages.DATA + RIGHT_BRACKETS + GalleryStore.OcrPages.TAB + ON + GalleryStore.GalleryMedia.TAB + DOT
                + LocalColumns.DATA + EQUAL + GalleryStore.OcrPages.TAB + DOT + GalleryStore.OcrPages.DATA + LEFT_OUTER_JOIN + LEFT_BRACKETS
                + SELECT + GalleryStore.MemoriesSetmap.DATA + COMMA + GROUP_CONCAT + LEFT_BRACKETS + GalleryStore.MemoriesSet.NAME
                + commaOrRightBracket
                + AS + GalleryStore.SearchInfoColumns.MEMORY_NAME + FROM + GalleryStore.MemoriesSetmap.TAB + INNER_JOIN
                + GalleryStore.MemoriesSet.TAB + ON + GalleryStore.MemoriesSetmap.SET_ID + EQUAL + GalleryStore.MemoriesSet._ID + GROUP_BY
                + GalleryStore.MemoriesSetmap.DATA + RIGHT_BRACKETS + GalleryStore.MemoriesSetmap.TAB + ON + GalleryStore.GalleryMedia.TAB + DOT
                + LocalColumns.DATA + EQUAL + GalleryStore.MemoriesSetmap.TAB + DOT + GalleryStore.MemoriesSetmap.DATA
                + GROUP_BY + GalleryStore.GalleryMedia.TAB + DOT + LocalColumns.DATA
                + orderClause;
    }

    public static List<MediaIdEntry> queryMediaIdEntryByVirtualAlbum(AlbumEntry albumEntry, boolean forceQuery) {
        List<MediaItem> mediaItemList = new ArrayList<>();
        List<MediaIdEntry> mediaIdEntries = new ArrayList<>();
        Path path = null;
        if (albumEntry.type == AlbumEntry.TYPE_VIDEO_ALBUM) {
            // for all local video album
            path = Local.PATH_ALBUM_ANY_VIDEO;
            MediaObject object = DataManager.getMediaObject(path);
            if (object == null) {
                return mediaIdEntries;
            }
            LocalAlbum localAlbum = ((LocalAlbum) object);
            int count = localAlbum.getCount();
            mediaItemList = localAlbum.getSubMediaItem(0, count);
        } else if (albumEntry.type == AlbumEntry.TYPE_ENHANCE_TEXT_ALBUM) {
            path = Local.PATH_ALBUM_ANY_ENHANCE_TEXT;
            MediaObject object = DataManager.getMediaObject(path);
            if ((object instanceof EnhanceTextAlbum)) {
                EnhanceTextAlbum enhanceTextAlbum = ((EnhanceTextAlbum) object);
                int count = enhanceTextAlbum.getCount();
                mediaItemList = enhanceTextAlbum.getSubMediaItem(0, count);
            }
        } else if (albumEntry.type == AlbumEntry.TYPE_RECYCLE_ALBUM) {
            // all recycle album
            path = Recycle.PATH_ALBUM_ALL_ANY;
            MediaSet recycleAlbum = DataManager.getMediaSet(path);
            if (recycleAlbum == null) {
                return mediaIdEntries;
            }
            if (forceQuery || (recycleAlbum.getDataVersion() == INVALID_DATA_VERSION)) {
                recycleAlbum.reload();
            }
            mediaItemList = recycleAlbum.getSubMediaItem(0, recycleAlbum.getCount());
        } else if (albumEntry.type == AlbumEntry.TYPE_PORTRAIT_BLUR) {
            path = Local.PATH_ALBUM_ANY_PORTRAIT_BLUR;
            MediaObject object = DataManager.getMediaObject(path);
            if (object instanceof ClassifiedPortraitBlurAlbum) {
                ClassifiedPortraitBlurAlbum portraitBlurAlbum = ((ClassifiedPortraitBlurAlbum) object);
                int count = portraitBlurAlbum.getCount();
                mediaItemList = portraitBlurAlbum.getSubMediaItem(0, count);
            }
        }
        addEntries(mediaItemList, mediaIdEntries);
        return mediaIdEntries;
    }

    private static void addEntries(List<MediaItem> mediaItemList, List<MediaIdEntry> entries) {
        if ((mediaItemList != null) && !mediaItemList.isEmpty()) {
            for (int i = 0, n = mediaItemList.size(); i < n; i++) {
                MediaItem mediaItem = mediaItemList.get(i);
                if (mediaItem == null) {
                    GLog.d(TAG, "[addEntries], mediaItem is null!");
                    continue;
                }
                if (mediaItem instanceof FaceItem) {
                    FaceItem faceItem = (FaceItem) mediaItem;
                    MediaIdEntry entry = new MediaIdEntry();
                    if (faceItem.getRefItem() == null) {
                        GLog.d(TAG, "[addEntries], faceItem.getRefItem() is null");
                        continue;
                    } else {
                        entry.galleryId = faceItem.getRefItem().getId();
                    }
                    entry.mediaId = faceItem.getLocalId();
                    entry.dateTaken = faceItem.getDateTakenInMs();
                    entry.mediaType = faceItem.getMediaType();
                    entries.add(entry);
                } else if (mediaItem instanceof LocalMediaItem) {
                    LocalMediaItem localMediaItem = (LocalMediaItem) mediaItem;
                    MediaIdEntry entry = new MediaIdEntry();
                    entry.galleryId = localMediaItem.getId();
                    entry.mediaId = localMediaItem.getMediaId();
                    entry.dateTaken = localMediaItem.getDateTakenInMs();
                    entry.mediaType = localMediaItem.getMediaType();
                    entries.add(entry);
                }
            }
        }
    }
}
