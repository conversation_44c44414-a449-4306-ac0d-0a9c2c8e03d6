/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SmartLabelSearchAbility.kt
 ** Description: 视觉知识图谱标签模糊查询和精确查询业务能力接口
 ** about app info
 **
 ** Version: 1.0
 ** Date: 2023/7/10
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery          2023/7/10     1.0         init
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph

import android.content.Context
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ENGLISH_LABEL_GRAPH
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_LABEL_GRAPH
import com.oplus.gallery.framework.abilities.extraction.labelgraph.BaseLabelGraphModelDownloadConfig
import com.oplus.gallery.framework.abilities.extraction.labelgraph.EnglishLabelGraphModelDownloadConfig
import com.oplus.gallery.framework.abilities.extraction.labelgraph.LabelGraphModelDownloadConfig
import com.oplus.gallery.framework.abilities.search.SearchAbilityImpl
import com.oplus.gallery.framework.abilities.search.cvgraph.bean.LabelQueryEntry
import com.oplus.gallery.framework.abilities.search.cvgraph.db.LabelGraphDatabaseHelper
import com.oplus.gallery.framework.abilities.search.cvgraph.db.LabelGraphSearchUtil
import com.oplus.gallery.framework.abilities.search.cvgraph.enum.RelationType
import net.sqlcipher.database.SQLiteDatabase

/**
 * 视觉知识图谱标签模糊查询和精确查询能力接口
 */
object LabelGraphSearchAbility {
    private const val TAG: String = "LabelGraphSearchAbility"

    /**
     * 视觉知识图谱精确匹配最大查询数量
     */
    private const val MAX_ACCURATE_MATCH_COUNT = 5

    /**
     * 视觉知识图谱模糊匹配最大查询数量
     */
    private const val MAX_FUZZY_MATCH_COUNT = 3

    /**
     * label_graph视觉知识图谱数据库对象
     */
    @Volatile
    private var cvDatabase: SQLiteDatabase? = null

    /**
     * label_graph视觉知识图谱数据库db文件名
     * 用于记录当前db文件名，以便切换语言后重新加载so和db
     */
    @Volatile
    private var cvDbName: String? = null

    /**
     * 正则表达式，英文知识图谱下，将字符串开头结尾的英文逗号句号替换成""
     */
    private val removePunctuationRegex = Regex("^[.,]+|[.,]+$")

    /**
     * 获取过滤后的搜索词
     * @param singleKeyword 搜索关键字
     *
     * 英文知识图谱下，将字符串开头结尾的英文逗号句号替换成""
     */
    private fun getFilteredKeyword(singleKeyword: String): String {
        val isSupportEnglishLabelGraph = SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_ENGLISH_LABEL_GRAPH, false) ?: false
        if (isSupportEnglishLabelGraph) {
            return singleKeyword.replace(removePunctuationRegex, TextUtil.EMPTY_STRING)
        }
        return singleKeyword
    }

    /**
     * 视觉知识图谱v1.0精确查询
     */
    @JvmStatic
    fun accurateSearch(
        context: Context,
        singleKeyword: String,
        isNeedQueryCompletionWord: Boolean
    ): Map<String, MutableList<MediaIdEntry>> {
        GLog.d(TAG, LogFlag.DL, "accurateSearch Start accurateSearch. $singleKeyword")
        val searchResult = HashMap<String, MutableList<MediaIdEntry>>()
        if (cvDatabase == null) {
            GLog.e(TAG, LogFlag.DL, "accurateSearch cvDatabase is null ")
            return searchResult
        }

        val singleKeyword = getFilteredKeyword(singleKeyword)

        val time = System.currentTimeMillis()
        val query = LabelQueryEntry(singleKeyword)
        var canFindResultByLabelCount = 0
        // 一.先查询标签及同义词
        LabelGraphSearchUtil.queryLabelOrSynonymWord(cvDatabase, query)
        GLog.d(TAG, LogFlag.DL, "accurateSearch queryLabelOrSynonymWord end. " + query.labelBeans)
        kotlin.run outSide@{
            query.labelBeans.forEach { labelBean ->
                if (canFindResultByLabelCount < MAX_ACCURATE_MATCH_COUNT) {
                    val mediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(context, labelBean.labelId)
                    if ((mediaIds != null) && mediaIds.isNotEmpty()) {
                        searchResult[labelBean.displayName] = mediaIds
                        SearchTrackHelper.putTrackLabel(labelBean.displayName, labelBean.labelOriginValue)
                        canFindResultByLabelCount++
                    }
                } else {
                    return@outSide
                }
            }
        }
        if (!isNeedQueryCompletionWord || (canFindResultByLabelCount >= MAX_ACCURATE_MATCH_COUNT)) {
            GLog.v(TAG, "accurateSearch, cost time is " + (System.currentTimeMillis() - time))
            return searchResult
        }
        // 二.然后查询补全词，注意补全词是取并集
        LabelGraphSearchUtil.queryCompletionWord(cvDatabase, query, RelationType.SYNONYM_TYPE)
        GLog.d(TAG, LogFlag.DL, "accurateSearch queryCompletionWord end. " + query.completionLabelBeans)
        kotlin.run outSide@{
            query.completionLabelBeans.forEach { labelBean ->
                if (canFindResultByLabelCount < MAX_ACCURATE_MATCH_COUNT) {
                    val mediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(context, labelBean.labelId)
                    if (mediaIds != null && mediaIds.isNotEmpty()) {
                        searchResult[labelBean.displayName] = mediaIds
                        SearchTrackHelper.putTrackLabel(labelBean.displayName, labelBean.labelOriginValue)
                        canFindResultByLabelCount++
                    }
                } else {
                    return@outSide
                }
            }
        }
        GLog.d(TAG) {
            "accurateSearch, End accurateSearch. $singleKeyword cost time is " + (System.currentTimeMillis() - time)
        }
        return searchResult
    }

    /**
     * 视觉知识图谱v1.0模糊查询
     */
    @JvmStatic
    fun fuzzySearch(context: Context, singleKeyword: String): MutableSet<String> {
        // 推荐搜索词
        val labelNames = mutableSetOf<String>()
        if (cvDatabase == null) {
            GLog.e(TAG, LogFlag.DL, "fuzzySearch cvDatabase is null ")
            return labelNames
        }

        val singleKeyword = getFilteredKeyword(singleKeyword)
        GLog.d(TAG, LogFlag.DL, "fuzzySearch Start fuzzySearch. $singleKeyword")
        val time = System.currentTimeMillis()
        val query = LabelQueryEntry(singleKeyword)
        // 一.先查询纠错词
        fuzzyQueryCorrectingWord(context, query, labelNames)
        // 二.如果查询不到结果，则尝试用可能想搜词, 近义词, 上位词
        if (labelNames.isEmpty()) {
            fuzzyQueryOtherLabelWord(context, query, labelNames)
        }
        GLog.d(TAG) {
            "fuzzySearch End fuzzySearch. $singleKeyword cost time is " + (System.currentTimeMillis() - time)
        }
        return labelNames
    }


    /**
     * 模糊查询图谱中的纠错词以及纠错词的补全词
     * @param context 上下文
     * @param query LabelQueryEntry对象
     * @param labelNames 模糊查询结果MutableSet容器
     */
    private fun fuzzyQueryCorrectingWord(context: Context, query: LabelQueryEntry, labelNames: MutableSet<String>) {
        LabelGraphSearchUtil.queryCorrectingWord(cvDatabase, query)
        GLog.d(TAG, LogFlag.DL, "fuzzyQueryCorrectingWord  queryCorrectingWord end. " + query.labelBeans)
        // 纠错词因为只会有一个，这边直接取0的位置
        if (query.labelBeans.isNotEmpty()) {
            val labelBean = query.labelBeans[0]
            val mediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(context, labelBean.labelId)
            if ((mediaIds != null) && mediaIds.isNotEmpty()) {
                labelNames.add(labelBean.label)
                SearchTrackHelper.putTrackLabel(labelBean.label, labelBean.labelOriginValue)
            }
            // 然后用纠错词查询补全词
            LabelGraphSearchUtil.queryCompletionWord(cvDatabase, query, RelationType.CORRECTING_TYPE)
            GLog.d(TAG, LogFlag.DL, "fuzzyQueryCorrectingWord  queryCompletionWord end. " + query.completionLabelBeans)
            kotlin.run outSide@{
                query.completionLabelBeans.forEach { completionLabel ->
                    if (labelNames.size < MAX_FUZZY_MATCH_COUNT) {
                        val completionMediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(context, completionLabel.labelId)
                        if (completionMediaIds != null && completionMediaIds.isNotEmpty()) {
                            // 说明这个补全词能查到
                            labelNames.add(completionLabel.label)
                            SearchTrackHelper.putTrackLabel(completionLabel.label, completionLabel.labelOriginValue)
                        }
                    } else {
                        return@outSide
                    }
                }
            }
        }
    }

    /**
     * 模糊查询图谱中的可能想搜词, 近义词, 上位词
     * @param context 上下文
     * @param query LabelQueryEntry对象
     * @param labelNames 模糊查询结果MutableSet容器
     */
    private fun fuzzyQueryOtherLabelWord(context: Context, query: LabelQueryEntry, labelNames: MutableSet<String>) {
        LabelGraphSearchUtil.queryOtherLabelBeans(cvDatabase, query)
        GLog.d(TAG, LogFlag.DL, "fuzzyQueryOtherLabelWord  queryOtherLabelBeans end. " + query.otherLabelBeans)
        kotlin.run outSide@{
            query.otherLabelBeans.forEach { otherLabel ->
                if (labelNames.size < MAX_FUZZY_MATCH_COUNT) {
                    val mediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(context, otherLabel.labelId)
                    if (mediaIds != null && mediaIds.isNotEmpty()) {
                        SearchTrackHelper.putTrackLabel(otherLabel.label, otherLabel.labelOriginValue)
                        labelNames.add(otherLabel.label)
                    }
                } else {
                    return@outSide
                }
            }
        }
    }

    /**
     * 通过标签Id查询label名
     */
    @JvmStatic
    fun queryLabelNameByLabelId(labelId: Int): String {
        return LabelGraphSearchUtil.queryLabelBeanByLabelId(cvDatabase, labelId)?.label ?: TextUtil.EMPTY_STRING
    }

    /**
     * 通过查询当前语种和获取dataBase结果，检查是否使用图谱查询
     *
     * 内销 + 中文，支持中文知识图谱
     * 内销/外销 + 英文，支持英文知识图谱
     * */
    @JvmStatic
    fun checkAvailabilityForLabelGraph(context: Context): Boolean {
        val isSupportLabelGraph = SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_LABEL_GRAPH, false) ?: false
        val isSupportEnglishLabelGraph = SearchAbilityImpl.configAbility?.getBooleanConfig(FEATURE_IS_SUPPORT_ENGLISH_LABEL_GRAPH, false) ?: false
        GLog.d(TAG, LogFlag.DL, "checkAvailabilityForLabelGraph, isSupportLabelGraph = $isSupportLabelGraph, " +
                "isSupportEnglishLabelGraph = $isSupportEnglishLabelGraph")
        return if (isSupportLabelGraph) {
            getCVDatabase(context, LabelGraphModelDownloadConfig)
        } else if (isSupportEnglishLabelGraph) {
            getCVDatabase(context, EnglishLabelGraphModelDownloadConfig)
        } else {
            false
        }
    }

    /**
     * 获取CVDatabase
     * 并记录labelGraphDbName，以便切换语言后重新加载so和db
     */
    private fun getCVDatabase(context: Context, curConfig: BaseLabelGraphModelDownloadConfig): Boolean {
        if ((cvDatabase == null) || (cvDbName != curConfig.labelGraphDbName)) {
            synchronized(this) {
                if ((cvDatabase == null) || (cvDbName != curConfig.labelGraphDbName)) {
                    cvDatabase = LabelGraphDatabaseHelper.openCVGraph(context, curConfig)
                    cvDbName = curConfig.labelGraphDbName
                }
            }
        }
        return (cvDatabase != null)
    }

    @JvmStatic
    fun closeCVGraph() {
        cvDatabase?.close()
        cvDatabase = null
        SearchTrackHelper.clear()
        GLog.d(TAG, LogFlag.DL, "closeCVGraph")
    }

    /**
     * 为融合搜索提供的补全能力查询接口
     * 1.先查询标签及其同义词
     * 2.在查询补全词
     * 3.如果没有，则查询纠错词
     * 4.如果还没有，则查询纠错词补全词
     * */
    @JvmStatic
    fun queryCompletionForKit(queryString: String): MutableMap<Int, String> {
        if (queryString.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "queryCompletionForKit  queryString is empty!")
            return mutableMapOf()
        }
        GLog.d(TAG, LogFlag.DL, "queryCompletionForKit  queryString: $queryString")
        val queryResultMap = mutableMapOf<Int, String>()
        val query = LabelQueryEntry(queryString)

        // 先查询标签及同义词
        LabelGraphSearchUtil.queryLabelOrSynonymWord(cvDatabase, query)
        query.labelBeans.forEach { label ->
            queryResultMap.putIfAbsent(label.labelId, label.displayName.ifEmpty { label.label })
            SearchTrackHelper.putTrackLabel(label.displayName.ifEmpty { label.label }, label.labelOriginValue)
        }
        GLog.d(TAG, LogFlag.DL, "queryCompletionForKit queryLabelOrSynonymWord end. " + query.labelBeans)

        // 再查询补全词
        LabelGraphSearchUtil.queryCompletionWord(cvDatabase, query, RelationType.SYNONYM_TYPE)
        query.completionLabelBeans.forEach { label ->
            // displayName和label相等的LabelBean，可以优先被添加至map，其他displayName和label不相等的将被continue
            val sameNameLabelBean = query.completionLabelBeans.find {
                it.displayName == label.label
            }
            if ((sameNameLabelBean != null) && (sameNameLabelBean != label)) {
                return@forEach
            }

            val value = label.displayName.ifEmpty { label.label }
            if (queryResultMap.containsValue(value).not()) {
                queryResultMap.putIfAbsent(label.labelId, value)
                SearchTrackHelper.putTrackLabel(label.displayName.ifEmpty { label.label }, label.labelOriginValue)
            }
        }
        GLog.d(TAG, LogFlag.DL, "queryCompletionForKit queryCompletionWord end. " + query.completionLabelBeans)

        // 如果没有的话，再查询纠错词
        if (queryResultMap.isEmpty()) {
            LabelGraphSearchUtil.queryCorrectingWord(cvDatabase, query)
            query.labelBeans.forEach { label ->
                queryResultMap.putIfAbsent(label.labelId, label.displayName.ifEmpty { label.label })
                SearchTrackHelper.putTrackLabel(label.displayName.ifEmpty { label.label }, label.labelOriginValue)
            }
            GLog.d(TAG, LogFlag.DL, "queryCompletionForKit  queryCorrectingWord end. " + query.labelBeans)

            // 能查到纠错词的话再查询纠错词补全词
            if (queryResultMap.isNotEmpty()) {
                LabelGraphSearchUtil.queryCompletionWord(cvDatabase, query, RelationType.CORRECTING_TYPE)
                query.completionLabelBeans.forEach { label ->
                    queryResultMap.putIfAbsent(label.labelId, label.displayName.ifEmpty { label.label })
                    SearchTrackHelper.putTrackLabel(label.displayName.ifEmpty { label.label }, label.labelOriginValue)
                }
                GLog.d(TAG, LogFlag.DL, "queryCompletionForKit  queryCompletionWord end. " + query.completionLabelBeans)
            }
        }

        return queryResultMap
    }

    /**
     * 为融合搜索提供的标签补全能力查询接口
     * 1.先查询标签及其同义词
     * 2.如果没有，则查询纠错词
     * */
    @JvmStatic
    fun wordCompletionForKit(word: String): MutableMap<Int, String> {
        if (word.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "wordCompletionForKit  word is empty!")
            return mutableMapOf()
        }
        GLog.d(TAG, LogFlag.DL, "wordCompletionForKit  queryString: $word")
        val queryResultMap = mutableMapOf<Int, String>()
        val query = LabelQueryEntry(word)
        // 先查询标签及同义词
        LabelGraphSearchUtil.queryLabelOrSynonymWord(cvDatabase, query)
        query.labelBeans.forEach { label ->
            queryResultMap.putIfAbsent(label.labelId, label.displayName.ifEmpty { label.label })
            SearchTrackHelper.putTrackLabel(label.displayName.ifEmpty { label.label }, label.labelOriginValue)
        }
        GLog.d(TAG, LogFlag.DL, "wordCompletionForKit queryLabelOrSynonymWord end. " + query.labelBeans)
        // 如果没有的话，再查询纠错词
        if (queryResultMap.isEmpty()) {
            LabelGraphSearchUtil.queryCorrectingWord(cvDatabase, query)
            query.labelBeans.forEach { label ->
                queryResultMap.putIfAbsent(label.labelId, label.displayName.ifEmpty { label.label })
                SearchTrackHelper.putTrackLabel(label.displayName.ifEmpty { label.label }, label.labelOriginValue)
            }
            GLog.d(TAG, LogFlag.DL, "wordCompletionForKit  queryCorrectingWord end. " + query.labelBeans)
        }
        return queryResultMap
    }


    /**
     * 为融合搜索提供的标签纠错能力查询接口
     * 仅查询纠错词
     * @param word
     * @return
     */
    fun wordCorrectionForKit(word: String): MutableMap<Int, String> {
        if (word.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "wordCorrectionForKit  word is empty!")
            return mutableMapOf()
        }
        val queryResultMap = mutableMapOf<Int, String>()
        val query = LabelQueryEntry(word)
        LabelGraphSearchUtil.queryCorrectingWord(cvDatabase, query)
        query.labelBeans.forEach { label ->
            queryResultMap.putIfAbsent(label.labelId, label.displayName.ifEmpty { label.label })
            SearchTrackHelper.putTrackLabel(label.displayName.ifEmpty { label.label }, label.labelOriginValue)
        }
        GLog.d(TAG, LogFlag.DL, "wordCorrectionForKit  queryCorrectingWord end. " + query.labelBeans)
        return queryResultMap
    }

    /**
     * 针对融合搜索提供的图谱模糊查询接口
     * 相比谱图差异在于不查询纠错和纠错的补全
     */
    @JvmStatic
    fun fuzzySearchForKit(context: Context, singleKeyword: String): Set<String> {
        // 推荐搜索词
        val labelNames = mutableSetOf<String>()
        if (cvDatabase == null) {
            GLog.e(TAG, LogFlag.DL, "fuzzySearchForKit cvDatabase is null ")
            return labelNames
        }
        GLog.d(TAG, LogFlag.DL, "fuzzySearchForKit Start fuzzySearch. $singleKeyword")
        val time = System.currentTimeMillis()
        val query = LabelQueryEntry(singleKeyword)
        fuzzyQueryOtherLabelWord(context, query, labelNames)
        GLog.d(TAG) {
            "fuzzySearchForKit End fuzzySearch. $singleKeyword cost time is " + (System.currentTimeMillis() - time)
        }
        return labelNames
    }

    /**
     * 为融合搜索提供的查询图谱中所有的标签（包括同义词）
     * @return String标签数组字符串
     * */
    @JvmStatic
    fun queryAllLabel(): MutableSet<String> = LabelGraphSearchUtil.queryAllLabel(cvDatabase)

    /**
     * 通过label名查询标签Id
     */
    @JvmStatic
    fun queryLabelBeanByLabelName(labelName: String): Int  {
        val labelName = getFilteredKeyword(labelName)
        return LabelGraphSearchUtil.queryLabelBeanByLabelName(cvDatabase, labelName)?.labelId
            ?: LabelSearchEngine.INVALID_ID
    }

    /**
     * 为融合搜索提供的查询图谱中所有的标签（包括同义词）
     * @return 标签id及对应的标签和同义词Map
     * */
    @JvmStatic
    fun queryAllLabelMap(): MutableMap<Int, MutableList<String>> {
        return LabelGraphSearchUtil.queryAllLabelMap(cvDatabase)
    }

    /**
     * 为融合搜索提供的查询图谱中的标签id
     * 查询顺序：
     * 1.先查询queryLabelBeanByLabelName()函数
     * 2.查询不到再查询tag_graph表（同义词和非正式同义词）
     * 3.最终查询不到则返回 LabelSearchEngine.INVALID_ID
     * @return 标签id
     * */
    @JvmStatic
    fun queryLabelId(labelName: String): Int {
        val labelName = getFilteredKeyword(labelName)
        return LabelGraphSearchUtil.queryLabelBeanByLabelName(cvDatabase, labelName)?.labelId
            ?: LabelGraphSearchUtil.queryLabelFromLabelGraphBySynonym(cvDatabase, labelName)?.labelId
            ?: LabelSearchEngine.INVALID_ID
    }
}