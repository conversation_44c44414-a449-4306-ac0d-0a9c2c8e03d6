/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendMemoriesQuery.kt
 ** Description: 推荐回忆查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 推荐回忆查询
 *
 * @property forceQuery 是否强制从数据库查询
 * @property recommendCache 推荐缓存
 */
internal class RecommendMemoriesQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache
) : Query {
    override fun query(): Cursor? {
        return if (!forceQuery && recommendCache.hasMemoriesRecommend()) {
            recommendCache.obtainMemoriesRecommend()
        } else {
            GTrace.traceBegin("RecommendMemoriesQuery")
            val startTime = System.currentTimeMillis()
            val cursor = MatrixCursor(COLUMN_RECOMMEND_MEMORIES)
            val queryTime = System.currentTimeMillis()
            val memories = MemoriesHelper.loadAllMemoriesEntries(ContextGetter.context)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] loadAllMemoriesEntries costTime:${GLog.getTime(queryTime)}")
            }
            for (memoriesEntry in memories) {
                val mMemoriesId = memoriesEntry.mMemoriesId
                cursor.addRow(
                    arrayOf<Any>(
                        memoriesEntry.mMemoriesName, memoriesEntry.mCoverMediaId, mMemoriesId,
                        memoriesEntry.mCount, memoriesEntry.mSubTitle, mMemoriesId
                    )
                )
            }
            recommendCache.updateToMemoriesRecommend(cursor)
            cursor.moveToPosition(-1)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] costTime:${GLog.getTime(startTime)}")
            }
            GTrace.traceEnd()
            cursor
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendMemoriesQuery"

        /**
         * 推荐回忆图集数据列
         */
        private val COLUMN_RECOMMEND_MEMORIES = arrayOf(
            SearchSuggestionProviderUtil.COLUMNNAME,
            SearchSuggestionProviderUtil.COLUMN_ID,
            SearchSuggestionProviderUtil.COLUMN_MEMORIES_ID,
            SearchSuggestionProviderUtil.COLUMN_COUNT,
            SearchSuggestionProviderUtil.COLUMN_MEMORIES_SUB_TITLE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_MEMORIES_ID
        )
    }
}
