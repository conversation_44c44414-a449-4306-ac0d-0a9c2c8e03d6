/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MemoryCompletion.kt
 ** Description:精彩回忆搜索关键字的补全
 ** Version: 1.0
 ** Date: 2023/9/29
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/9/29      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import android.os.Bundle
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.andes.photos.kit.search.data.QueryCompletionEntry
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.business_lib.model.data.base.entry.MemoriesAlbumEntry
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager

/**
 * 精彩回忆搜索关键字的补全
 */
internal class MemoryCompletion(context: Context, keywordCacheManager: KeywordCacheManager) :
    BaseCompletionAbility(context, keywordCacheManager) {

    override fun getCompletionType(): Int = CompletionType.TYPE_MEMORIES_ALBUM

    override fun queryComplete(query: String): List<QueryCompletionEntry> {
        val completeMemories = complete(query)
        GLog.d(TAG, "[queryComplete] query:$query, completeMemories:${completeMemories.map { it.memoriesName }}")
        return completeMemories.map { memoryAlbumEntry ->
            QueryCompletionEntry(
                memoryAlbumEntry.memoriesName,
                EntityType.ENTITY_MEMORIES,
                memoryAlbumEntry.memoriesId.toString(),
                query
            )
        }
    }

    /**
     * 回忆图集名补全
     *
     * @param query 搜索词
     * @return 回忆图集信息列表
     */
    fun complete(query: String): MutableList<MemoriesAlbumEntry> {
        GTrace.traceBegin("Memory.complete")
        val startTime = System.currentTimeMillis()
        val completionMemoriesAlbum: MutableList<MemoriesAlbumEntry> = mutableListOf()
        keywordCache.memoriesAlbumSet?.let {
            val searchClassifier = buildClassifier(query)
            val memoryAlbumEntryList = it
            memoryAlbumEntryList.forEach { memoriesAlbumEntry ->
                val memoriesName = memoriesAlbumEntry.memoriesName
                if (memoriesName.isNullOrEmpty()) return@forEach
                if (searchClassifier.contains(memoriesName.lowercase(), query)) {
                    completionMemoriesAlbum.add(memoriesAlbumEntry)
                }
            }
        }
        GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
        GTrace.traceEnd()
        return completionMemoriesAlbum
    }

    override fun queryComplete(query: String, extra: Bundle?): List<QueryCompletionEntry> { return queryComplete(query) }

    companion object {
        private const val TAG = "${SearchConst.TAG}MemoryCompletion"
    }
}