/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AttachDateTimeInd.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2016/11/25
 ** Author:yanchao.<PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yanchao.<PERSON>@Apps.Gallery3D     2016/11/25      1.0      build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.search.datetime;

import android.content.Context;
import android.content.res.Resources;

import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateInfo;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateRange;
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime;
import com.oplus.gallery.framework.datatmp.R;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.regex.Pattern;

public class AttachDateTimeInd {
    private static final String TAG = "AttachDateTimeInd";
    private static DateInfo sDicIndJanuary;
    private static DateInfo sDicIndFebruary;
    private static DateInfo sDicIndMarch;
    private static DateInfo sDicIndApril;
    private static DateInfo sDicIndMay;
    private static DateInfo sDicIndJune;
    private static DateInfo sDicIndJuly;
    private static DateInfo sDicIndAugust;
    private static DateInfo sDicIndSeptember;
    private static DateInfo sDicIndOctober;
    private static DateInfo sDicIndNovember;
    private static DateInfo sDicIndDecember;

    private static DateInfo sDicDayNumber1;
    private static DateInfo sDicDayNumber2;
    private static DateInfo sDicDayNumber3;
    private static DateInfo sDicDayNumber4;
    private static DateInfo sDicDayNumber5;
    private static DateInfo sDicDayNumber6;
    private static DateInfo sDicDayNumber7;
    private static DateInfo sDicDayNumber8;
    private static DateInfo sDicDayNumber9;
    private static DateInfo sDicDayNumber10;
    private static DateInfo sDicDayNumber11;
    private static DateInfo sDicDayNumber12;
    private static DateInfo sDicDayNumber13;
    private static DateInfo sDicDayNumber14;
    private static DateInfo sDicDayNumber15;
    private static DateInfo sDicDayNumber16;
    private static DateInfo sDicDayNumber17;
    private static DateInfo sDicDayNumber18;
    private static DateInfo sDicDayNumber19;
    private static DateInfo sDicDayNumber20;
    private static DateInfo sDicDayNumber21;
    private static DateInfo sDicDayNumber22;
    private static DateInfo sDicDayNumber23;
    private static DateInfo sDicDayNumber24;
    private static DateInfo sDicDayNumber25;
    private static DateInfo sDicDayNumber26;
    private static DateInfo sDicDayNumber27;
    private static DateInfo sDicDayNumber28;
    private static DateInfo sDicDayNumber29;
    private static DateInfo sDicDayNumber30;
    private static DateInfo sDicDayNumber31;

    private static DateInfo[] sDicIndMonthArrays;
    private static DateInfo[] sDicIndDayArrays;
    private Context mContext;

    public AttachDateTimeInd(Context context) {
        Resources resources = context.getResources();

        if (sDicIndJanuary == null) {
            sDicIndJanuary = new DateInfo(resources, R.array.model_ind_january);
        }
        if (sDicIndFebruary == null) {
            sDicIndFebruary = new DateInfo(resources, R.array.model_ind_february);
        }
        if (sDicIndMarch == null) {
            sDicIndMarch = new DateInfo(resources, R.array.model_ind_march);
        }
        if (sDicIndApril == null) {
            sDicIndApril = new DateInfo(resources, R.array.model_ind_april);
        }
        if (sDicIndMay == null) {
            sDicIndMay = new DateInfo(resources, R.array.model_ind_may);
        }
        if (sDicIndJune == null) {
            sDicIndJune = new DateInfo(resources, R.array.model_ind_june);
        }
        if (sDicIndJuly == null) {
            sDicIndJuly = new DateInfo(resources, R.array.model_ind_july);
        }
        if (sDicIndAugust == null) {
            sDicIndAugust = new DateInfo(resources, R.array.model_ind_august);
        }
        if (sDicIndSeptember == null) {
            sDicIndSeptember = new DateInfo(resources, R.array.model_ind_september);
        }
        if (sDicIndOctober == null) {
            sDicIndOctober = new DateInfo(resources, R.array.model_ind_october);
        }
        if (sDicIndNovember == null) {
            sDicIndNovember = new DateInfo(resources, R.array.model_ind_november);
        }
        if (sDicIndDecember == null) {
            sDicIndDecember = new DateInfo(resources, R.array.model_ind_december);
        }

        if (sDicDayNumber1 == null) {
            sDicDayNumber1 = new DateInfo(resources, R.array.model_en_day_1);
        }
        if (sDicDayNumber2 == null) {
            sDicDayNumber2 = new DateInfo(resources, R.array.model_en_day_2);
        }
        if (sDicDayNumber3 == null) {
            sDicDayNumber3 = new DateInfo(resources, R.array.model_en_day_3);
        }
        if (sDicDayNumber4 == null) {
            sDicDayNumber4 = new DateInfo(resources, R.array.model_en_day_4);
        }
        if (sDicDayNumber5 == null) {
            sDicDayNumber5 = new DateInfo(resources, R.array.model_en_day_5);
        }
        if (sDicDayNumber6 == null) {
            sDicDayNumber6 = new DateInfo(resources, R.array.model_en_day_6);
        }
        if (sDicDayNumber7 == null) {
            sDicDayNumber7 = new DateInfo(resources, R.array.model_en_day_7);
        }
        if (sDicDayNumber8 == null) {
            sDicDayNumber8 = new DateInfo(resources, R.array.model_en_day_8);
        }
        if (sDicDayNumber9 == null) {
            sDicDayNumber9 = new DateInfo(resources, R.array.model_en_day_9);
        }
        if (sDicDayNumber10 == null) {
            sDicDayNumber10 = new DateInfo(resources, R.array.model_en_day_10);
        }
        if (sDicDayNumber11 == null) {
            sDicDayNumber11 = new DateInfo(resources, R.array.model_en_day_11);
        }
        if (sDicDayNumber12 == null) {
            sDicDayNumber12 = new DateInfo(resources, R.array.model_en_day_12);
        }
        if (sDicDayNumber13 == null) {
            sDicDayNumber13 = new DateInfo(resources, R.array.model_en_day_13);
        }
        if (sDicDayNumber14 == null) {
            sDicDayNumber14 = new DateInfo(resources, R.array.model_en_day_14);
        }
        if (sDicDayNumber15 == null) {
            sDicDayNumber15 = new DateInfo(resources, R.array.model_en_day_15);
        }
        if (sDicDayNumber16 == null) {
            sDicDayNumber16 = new DateInfo(resources, R.array.model_en_day_16);
        }
        if (sDicDayNumber17 == null) {
            sDicDayNumber17 = new DateInfo(resources, R.array.model_en_day_17);
        }
        if (sDicDayNumber18 == null) {
            sDicDayNumber18 = new DateInfo(resources, R.array.model_en_day_18);
        }
        if (sDicDayNumber19 == null) {
            sDicDayNumber19 = new DateInfo(resources, R.array.model_en_day_19);
        }
        if (sDicDayNumber20 == null) {
            sDicDayNumber20 = new DateInfo(resources, R.array.model_en_day_20);
        }
        if (sDicDayNumber21 == null) {
            sDicDayNumber21 = new DateInfo(resources, R.array.model_en_day_21);
        }
        if (sDicDayNumber22 == null) {
            sDicDayNumber22 = new DateInfo(resources, R.array.model_en_day_22);
        }
        if (sDicDayNumber23 == null) {
            sDicDayNumber23 = new DateInfo(resources, R.array.model_en_day_23);
        }
        if (sDicDayNumber24 == null) {
            sDicDayNumber24 = new DateInfo(resources, R.array.model_en_day_24);
        }
        if (sDicDayNumber25 == null) {
            sDicDayNumber25 = new DateInfo(resources, R.array.model_en_day_25);
        }
        if (sDicDayNumber26 == null) {
            sDicDayNumber26 = new DateInfo(resources, R.array.model_en_day_26);
        }
        if (sDicDayNumber27 == null) {
            sDicDayNumber27 = new DateInfo(resources, R.array.model_en_day_27);
        }
        if (sDicDayNumber28 == null) {
            sDicDayNumber28 = new DateInfo(resources, R.array.model_en_day_28);
        }
        if (sDicDayNumber29 == null) {
            sDicDayNumber29 = new DateInfo(resources, R.array.model_en_day_29);
        }
        if (sDicDayNumber30 == null) {
            sDicDayNumber30 = new DateInfo(resources, R.array.model_en_day_30);
        }
        if (sDicDayNumber31 == null) {
            sDicDayNumber31 = new DateInfo(resources, R.array.model_en_day_31);
        }

        if (sDicIndMonthArrays == null) {
            sDicIndMonthArrays = new DateInfo[]{
                    sDicIndJanuary,
                    sDicIndFebruary,
                    sDicIndMarch,
                    sDicIndApril,
                    sDicIndMay,
                    sDicIndJune,
                    sDicIndJuly,
                    sDicIndAugust,
                    sDicIndSeptember,
                    sDicIndOctober,
                    sDicIndNovember,
                    sDicIndDecember
            };
        }
        if (sDicIndDayArrays == null) {
            sDicIndDayArrays = new DateInfo[]{
                    sDicDayNumber1,
                    sDicDayNumber2,
                    sDicDayNumber3,
                    sDicDayNumber4,
                    sDicDayNumber5,
                    sDicDayNumber6,
                    sDicDayNumber7,
                    sDicDayNumber8,
                    sDicDayNumber9,
                    sDicDayNumber10,
                    sDicDayNumber11,
                    sDicDayNumber12,
                    sDicDayNumber13,
                    sDicDayNumber14,
                    sDicDayNumber15,
                    sDicDayNumber16,
                    sDicDayNumber17,
                    sDicDayNumber18,
                    sDicDayNumber19,
                    sDicDayNumber20,
                    sDicDayNumber21,
                    sDicDayNumber22,
                    sDicDayNumber23,
                    sDicDayNumber24,
                    sDicDayNumber25,
                    sDicDayNumber26,
                    sDicDayNumber27,
                    sDicDayNumber28,
                    sDicDayNumber29,
                    sDicDayNumber30,
                    sDicDayNumber31
            };
        }
        mContext = context;
    }

    public List<String> completeDateTime(String keyword) {
        List<String> keywordList = new ArrayList<>();
        for (DateInfo info : sDicIndMonthArrays) {
            if (info.indexOfKeyword(keyword)) {
                keywordList.add(info.mShootKeyword);
            }
        }
        GLog.d(TAG, "completeDateTime, keywordList is " + keywordList);
        return keywordList;
    }

    public ShootDateTime getShootDateTime(String keyword) {
        DateInfo info = null;
        DateRange range = null;
        if ((info = getAbsoluteMonth(keyword)) != null) {
            range = parseAbsoluteMonthRange(info);
        } else {
            range = parseNumericalDateRange(keyword);
        }
        ShootDateTime shootDateTime = null;
        if (range != null) {
            shootDateTime = new ShootDateTime();
            shootDateTime.dateRange = range;
            shootDateTime.shootKeyword = (info != null) ? info.mShootKeyword : keyword;
            String condition = range.condition;
            String[] fmtArr = (condition != null) ? condition.split(",") : null;
            if ((fmtArr != null) && (fmtArr.length >= 2)) {
                shootDateTime.format = fmtArr[0];
                shootDateTime.conditions = (fmtArr.length > 2) ? condition.substring(condition.indexOf(",") + 1) : fmtArr[1];
            }
        }
        return shootDateTime;
    }

    private DateInfo getAbsoluteMonth(String keyword) {
        if (LocaleUtils.isIndonesia(ContextGetter.context)) {
            for (DateInfo info : sDicIndMonthArrays) {
                if (info.equalsKeyword(keyword)) {
                    return info;
                }
            }
        }
        return null;
    }

    /**
     * 如：一月
     *
     * @param info
     * @return
     */
    private DateRange parseAbsoluteMonthRange(DateInfo info) {
        int monthNumber = 0;
        // Found out the month of year
        for (int i = 0; i < sDicIndMonthArrays.length; i++) {
            if (sDicIndMonthArrays[i] == info) {
                monthNumber = i + 1;
                break;
            }
        }
        StringBuffer preSuffixBuffer = new StringBuffer();
        StringBuffer postSuffixBuffer = new StringBuffer();
        if (monthNumber > 0) {
            DateRange range = new DateRange();
            preSuffixBuffer.append("-%m");
            postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", monthNumber));
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

    private DateRange parseNumericalDateRange(String keyword) {
        int minLength = 1;
        int midLength = 2;
        int maxLength = 3;
        String[] keywords = keyword.split(SearchCommonUtils.SPACE_REGEX);
        int length = keywords.length;
        if ((length < minLength) || (length > maxLength)) {
            GLog.d(TAG, "parseNumericalDateRange, not find legal date : " + keyword);
            return null;
        }
        final Pattern patternYear = Pattern.compile("^\\d{4}$");

        int day = -1;
        int year = -1;
        int month = -1;
        if (length == minLength) {
            // yyyy
            if (patternYear.matcher(keywords[0]).find()) {
                year = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidYear(year)) {
                    // check whether this year is valid or not
                    return null;
                }
            }
        } else if (length == midLength) {
            // mm-yyyy
            if (patternYear.matcher(keywords[1]).find()) {
                year = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidYear(year)) {
                    // check whether this year is valid or not
                    return null;
                }
                month = findAbsoluteMonthNumber(keywords[0]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    // check whether this month is valid or not
                    return null;
                }
            } else {
                // mm-dd, dd-mm
                day = findAbsoluteDayNumber(keywords[0]);
                month = findAbsoluteMonthNumber(keywords[1]);
                if ((day == -1) || (month == -1)) {
                    month = findAbsoluteMonthNumber(keywords[0]);
                    if (!DateTimeSelector.isValidMonth(month)) {
                        // check whether this month is valid or not
                        return null;
                    }
                    day = findAbsoluteDayNumber(keywords[1]);
                    if (!DateTimeSelector.isValidDay(year, month, day)) {
                        // check whether this day is valid or not
                        return null;
                    }
                }
            }
        } else if (length == maxLength) {
            // dd-mm-yyyy,
            if (patternYear.matcher(keywords[2]).find()) {
                year = Integer.parseInt(keywords[2]);
                if (!DateTimeSelector.isValidYear(year)) {
                    // check whether this year is valid or not
                    return null;
                }
                day = findAbsoluteDayNumber(keywords[0]);
                month = findAbsoluteMonthNumber(keywords[1]);
                if ((day == -1) || (month == -1)) {
                    month = findAbsoluteMonthNumber(keywords[0]);
                    if (!DateTimeSelector.isValidMonth(month)) {
                        // check whether this month is valid or not
                        return null;
                    }
                    day = findAbsoluteDayNumber(keywords[1]);
                    if (!DateTimeSelector.isValidDay(year, month, day)) {
                        // check whether this day is valid or not
                        return null;
                    }
                }
            }
        }

        if ((year > 0) || (month > 0) || (day > 0)) {
            DateRange range = new DateRange();
            StringBuffer preSuffixBuffer = new StringBuffer();
            StringBuffer postSuffixBuffer = new StringBuffer();
            if (year > 0) {
                preSuffixBuffer.append("-%Y");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%04d", year));
            }
            if (month > 0) {
                preSuffixBuffer.append("-%m");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", month));
            }
            if (day > 0) {
                preSuffixBuffer.append("-%d");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", day));
            }
            if (preSuffixBuffer.length() > 1) {
                preSuffixBuffer.delete(0, 1);
            }
            if (postSuffixBuffer.length() > 1) {
                postSuffixBuffer.delete(0, 1);
            }
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

    public int findAbsoluteMonthNumber(String keyword) {
        DateInfo info = getAbsoluteMonth(keyword);
        if (info == null) {
            return -1;
        }
        int monthNumber = 0;
        // Found out the month of year
        for (int i = 0; i < sDicIndMonthArrays.length; i++) {
            if (sDicIndMonthArrays[i] == info) {
                monthNumber = i + 1;
                break;
            }
        }
        return monthNumber;
    }

    private int findAbsoluteDayNumber(String keyword) {
        DateInfo info = getAbsoluteDay(keyword);
        if (info == null) {
            return -1;
        }
        int dayNumber = 0;
        // Found out the day of month
        for (int i = 0; i < sDicIndDayArrays.length; i++) {
            if (sDicIndDayArrays[i] == info) {
                dayNumber = i + 1;
                break;
            }
        }
        return dayNumber;
    }

    private DateInfo getAbsoluteDay(String keyword) {
        for (DateInfo info : sDicIndDayArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

}
