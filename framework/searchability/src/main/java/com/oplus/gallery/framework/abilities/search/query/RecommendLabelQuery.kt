/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendLabelQuery.kt
 ** Description: 推荐标签查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.business_lib.model.data.label.LabelSelector
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils

/**
 * 推荐标签查询
 *
 * @property forceQuery 是否强制从数据库查询
 * @property recommendCache 推荐缓存
 * @property labelSelector 标签选择器
 */
@Suppress("NestedBlockDepth")
internal class RecommendLabelQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache,
    private val labelSelector: LabelSelector
) : Query {
    override fun query(): Cursor? {
        return if (!forceQuery && recommendCache.hasLabelRecommend()) {
            recommendCache.obtainLabelRecommend()
        } else {
            GTrace.traceBegin("RecommendLabelQuery")
            val startTime = System.currentTimeMillis()
            val cursor = MatrixCursor(COLUMN_RECOMMEND_LABEL)
            val displayLabels: List<String>? = labelSelector.displayLabels
            if (GProperty.DEBUG_LABEL) {
                GLog.d(TAG, "[query] displayLabels=$displayLabels")
            }
            if (displayLabels?.isNotEmpty() == true) {
                val queryTime = System.currentTimeMillis()
                val labelEntries = DBSearchUtils.queryLabelEntryForLabels(displayLabels)
                GLog.d(TAG, "[query] costTime:${GLog.getTime(queryTime)}, query count = ${labelEntries.size}")

                displayLabels.forEach { displayLabel ->
                    for (labelEntry in labelEntries) {
                        val labelKey = labelEntry.labelKey
                        if (labelKey.isNullOrEmpty()) continue
                        if (labelKey.equals(displayLabel, ignoreCase = true)) {
                            val entry = labelEntry.coverMediaEntry
                            entry?.let {
                                if (GProperty.DEBUG_LABEL) {
                                    GLog.d(TAG, "[query] displayLabel=$displayLabel $labelEntry")
                                }
                                cursor.addRow(
                                    arrayOf<Any?>(
                                        labelEntry.labelName,
                                        it.mediaId, labelEntry.count, labelEntry.sceneId, it.mediaType, it.galleryId
                                    )
                                )
                            }
                            break
                        }
                    }
                }
            } else {
                GLog.d(TAG, "[query], no display label!")
            }
            recommendCache.updateToLabelRecommend(cursor)
            cursor.moveToPosition(-1)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] costTime:${GLog.getTime(startTime)}")
            }
            GTrace.traceEnd()
            cursor
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendLabelQuery"

        /**
         * 推荐标签数据列
         */
        private val COLUMN_RECOMMEND_LABEL = arrayOf(
            SearchSuggestionProviderUtil.COLUMNNAME,
            SearchSuggestionProviderUtil.COLUMN_ID,
            SearchSuggestionProviderUtil.COLUMN_COUNT,
            SearchSuggestionProviderUtil.COLUMN_SCENE_ID,
            SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_GALLERY_ID
        )
    }
}