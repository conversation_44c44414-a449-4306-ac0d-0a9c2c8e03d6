/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OcrEmbeddingAbilityImpl.kt
 ** Description : Ocr语义扫描搜索实现类
 ** Version     : 1.0
 ** Date        : 2024/11/21
 ** Author      : 80377872
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80377872                            2024/11/21     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.search.embedding.ocrembedding

import android.content.Context
import com.ai.slp.engine.EmbeddingSceneType
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.embedding.BaseEmbeddingAbilityImpl
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility.Companion.TYPE_OCR_EMBEDDING_ABILITY

/**
 * Ocr 语义扫描搜索实现类
 */
class OcrEmbeddingAbilityImpl(context: Context) : BaseEmbeddingAbilityImpl(context), IOcrEmbeddingAbility {

    override val domainInstance: AutoCloseable = this

    override var type: String = TYPE_OCR_EMBEDDING_ABILITY

    /**
     * 获取文本 Embedding 场景类型: OCR/Caption. EmbeddingSceneType 中定有常量
     */
    override fun getEmbeddingSceneType(): Int {
        return EmbeddingSceneType.TYPE_OCR
    }

    /**
     * 获取日志 TAG
     */
    override fun getTag(): String {
        return TAG
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}OcrEmbeddingAbilityImpl"
    }
}