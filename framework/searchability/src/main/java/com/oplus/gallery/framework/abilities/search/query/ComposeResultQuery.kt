/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ComposeResultQuery.kt
 ** Description: 组合多种类型检索结果的查询
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.MultiQueryResult
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.result.assemble.AlbumSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.DataTimeSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.FileNameSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.GuideLabelSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.LabelSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.LocationSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.MemoriesSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.OcrSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.PersonSearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.SearchResultAssembler
import com.oplus.gallery.framework.abilities.search.result.assemble.SpecialLabelSearchResultAssembler

/**
 * 组合多种类型检索结果的查询
 *
 * @property searchEntry 搜索相关信息封装对象
 * @property keywordCacheManager keyword缓存管理器
 * @property geoCacheService 地点更新服务
 * @property festivalSelector 节日选择器
 */
internal class ComposeResultQuery(
    private val searchEntry: SearchEntry,
    private val keywordCacheManager: KeywordCacheManager,
    private val geoCacheService: GeoCacheService,
    private val festivalSelector: FestivalSelector,
) : Query {

    override fun query(): Cursor {
        return queryMultiResult(searchEntry)
    }

    private fun queryMultiResult(searchEntry: SearchEntry): Cursor {
        val startTime = System.currentTimeMillis()
        val multiQueryResult = MultiQueryResult()
        // 目前相册仅支持将输入词按空格分割为两个搜索词
        searchEntry.searchKeywords?.let {
            GLog.d(TAG, "[queryMultiResult] searchEntry:$searchEntry")
            it.forEach { singleKeyword ->
                if (singleKeyword.isEmpty()) {
                    return@forEach
                }
                val singleQueryResult = SingleQueryResult(singleKeyword)
                getTargetResultTypes().forEach { resultType ->
                    createResultAssembler(resultType)?.assemble(singleQueryResult, searchEntry)
                }

                // 如果经过上面的查询后，所有维度的结果都是空的，那么后面其他的搜索关键词也不再执行查询了，直接丢弃，返回一个空的结果。
                if (singleQueryResult.isEmpty) {
                    multiQueryResult.clear()
                    return@let
                }
                multiQueryResult.addKeywordResult(singleQueryResult)
            }
        }

        // OCR不支持将搜索关键词拆分为多个关键词，不支持特殊字符，不支持外语，不支持补全
        if (searchEntry.searchOcrQuery && (searchEntry.searchType and SearchType.TYPE_OCR != 0)) {
            val ocrSearchResult = SingleQueryResult(searchEntry.searchKeyword)
            createResultAssembler(SearchType.TYPE_OCR)?.assemble(ocrSearchResult, searchEntry)
            multiQueryResult.addOcrQueryResult(ocrSearchResult)
        }

        // 只有当所有类型检索都没有内容时，才执行标签引导词的检索与推荐
        if (multiQueryResult.isEmpty && (searchEntry.searchType and SearchType.TYPE_GUIDE_LABEL != 0)) {
            val singleKeyword = searchEntry.searchKeyword?.replace(SearchCommonUtils.SPACE_REGEX, TextUtil.EMPTY_STRING)
            val result = SingleQueryResult(singleKeyword)
            createResultAssembler(SearchType.TYPE_GUIDE_LABEL)?.assemble(result, searchEntry)
            multiQueryResult.addKeywordResult(result)
        }

        GLog.v(TAG, "[queryMultiResult] costTime:${GLog.getTime(startTime)}")
        return multiQueryResult.buildCursorForQueryResult()
    }

    private fun createResultAssembler(resultType: Int): SearchResultAssembler? {
        return when (resultType) {
            SearchType.TYPE_DATETIME -> DataTimeSearchResultAssembler(keywordCacheManager, festivalSelector)
            SearchType.TYPE_LOCATION -> LocationSearchResultAssembler(keywordCacheManager, geoCacheService)
            SearchType.TYPE_OCR -> OcrSearchResultAssembler()
            SearchType.TYPE_PERSON -> PersonSearchResultAssembler(keywordCacheManager)
            SearchType.TYPE_FILE_NAME -> FileNameSearchResultAssembler()
            SearchType.TYPE_SPECIAL_LABEL -> SpecialLabelSearchResultAssembler()
            SearchType.TYPE_LABEL -> LabelSearchResultAssembler(keywordCacheManager)
            SearchType.TYPE_GUIDE_LABEL -> GuideLabelSearchResultAssembler(keywordCacheManager)
            SearchType.TYPE_ALBUM -> AlbumSearchResultAssembler(keywordCacheManager)
            SearchType.TYPE_MEMORIES_ALBUM -> MemoriesSearchResultAssembler(keywordCacheManager)
            else -> null
        }
    }

    private fun getTargetResultTypes(): MutableList<Int> {
        val targetResultTypes = mutableListOf<Int>()
        if (searchEntry.searchType and SearchType.TYPE_DATETIME != 0) {
            targetResultTypes.add(SearchType.TYPE_DATETIME)
        }
        if (searchEntry.searchType and SearchType.TYPE_LOCATION != 0) {
            targetResultTypes.add(SearchType.TYPE_LOCATION)
        }
        if (searchEntry.searchType and SearchType.TYPE_PERSON != 0) {
            targetResultTypes.add(SearchType.TYPE_PERSON)
        }
        if (searchEntry.searchType and SearchType.TYPE_ALBUM != 0) {
            targetResultTypes.add(SearchType.TYPE_ALBUM)
        }
        if (searchEntry.searchType and SearchType.TYPE_FILE_NAME != 0) {
            targetResultTypes.add(SearchType.TYPE_FILE_NAME)
        }
        if (searchEntry.searchType and SearchType.TYPE_MEMORIES_ALBUM != 0) {
            targetResultTypes.add(SearchType.TYPE_MEMORIES_ALBUM)
        }
        if (searchEntry.searchType and SearchType.TYPE_SPECIAL_LABEL != 0) {
            targetResultTypes.add(SearchType.TYPE_SPECIAL_LABEL)
        }
        if (searchEntry.searchType and SearchType.TYPE_LABEL != 0) {
            targetResultTypes.add(SearchType.TYPE_LABEL)
        }
        return targetResultTypes
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}ComposeResultQuery"
    }
}