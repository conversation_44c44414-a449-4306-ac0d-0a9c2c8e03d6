/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumSearchResultAssembler.kt
 ** Description: 图集搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.completion.AlbumCompletion
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 图集搜索结果拼装器
 */
internal class AlbumSearchResultAssembler(
    private val keywordCacheManager: KeywordCacheManager
) : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_ALBUM

    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val forceQuery = searchEntry.searchForceQuery
        GLog.d(TAG, "[assemble] singleKeyword:$singleKeyword")
        val startTime = System.currentTimeMillis()
        val keywordCache = keywordCacheManager.keywordCache
        keywordCache.albumEntrySet?.let {
            keywordCacheManager.updateAlbumSetCache()
        }
        keywordCache.albumEntrySet?.let {
            val normalAlbumList: MutableList<AlbumEntry> = mutableListOf()
            val albumCompletion = AlbumCompletion(ContextGetter.context, keywordCacheManager)
            // 图集补全词
            val completionAlbumList = albumCompletion.complete(singleKeyword)
            completionAlbumList.forEach { completionAlbum ->
                if ((completionAlbum.type != AlbumEntry.TYPE_NORMAL_ALBUM) && (completionAlbum.type != AlbumEntry.TYPE_CAMERA)
                    && (completionAlbum.type != AlbumEntry.TYPE_CARD_CASE)
                ) {
                    val albumMediaIds = DBSearchUtils.queryMediaIdEntryByVirtualAlbum(completionAlbum, forceQuery)
                    if (albumMediaIds.isNotEmpty()) {
                        singleQueryResult.appendAlbumQueryResult(
                            albumMediaIds,
                            completionAlbum.bucketName,
                            completionAlbum.type,
                            SearchType.TYPE_ALBUM
                        )
                    } else {
                        GLog.w(TAG, "[assemble] failed to search album")
                    }
                } else {
                    completionAlbum.mediaIdEntries.clear()
                    normalAlbumList.add(completionAlbum)
                }
            }

            val normalAlbumEntries = DBSearchUtils.queryMediaIdEntryByLocalAlbum(normalAlbumList)
            normalAlbumEntries?.forEach { albumEntry ->
                val albumMediaIds: MutableList<MediaIdEntry> = albumEntry.mediaIdEntries
                if (albumMediaIds.isNotEmpty()) {
                    GLog.d(TAG, "[assemble] albumMediaIds:" + albumMediaIds.size)
                    singleQueryResult.appendAlbumQueryResult(albumMediaIds, albumEntry.bucketName, albumEntry.type, SearchType.TYPE_ALBUM)
                } else {
                    GLog.d(TAG, "[assemble] failed to search album")
                }
            }
        }
        GLog.d(TAG, "[assemble] costTime:${GLog.getTime(startTime)}")

        return singleQueryResult
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}AlbumSearchResultAssembler"
    }
}