/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MemoriesSearchResultAssembler.kt
 ** Description:回忆搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import com.oplus.gallery.business_lib.model.data.base.entry.MemoriesAlbumEntry
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.completion.MemoryCompletion
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 回忆搜索结果拼装器
 */
internal class MemoriesSearchResultAssembler(
    private val keywordCacheManager: KeywordCacheManager
) : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_MEMORIES_ALBUM

    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val forceQuery = searchEntry.searchForceQuery
        GLog.d(TAG, "[assemble] singleKeyword:$singleKeyword")
        val startTime = System.currentTimeMillis()
        val keywordCache = keywordCacheManager.keywordCache
        if (forceQuery) {
            keywordCache.memoriesAlbumSet = DBSearchUtils.queryAllMemoriesAlbumEntry(ContextGetter.context)
        }
        val albumEntrySet: List<MemoriesAlbumEntry>? = keywordCache.memoriesAlbumSet
        albumEntrySet?.let {
            val memoryCompletion = MemoryCompletion(ContextGetter.context, keywordCacheManager)
            val completionMemoriesAlbum: List<MemoriesAlbumEntry> = memoryCompletion.complete(singleKeyword)
            for (albumEntry in completionMemoriesAlbum) {
                singleQueryResult.appendMemoriesAlbumQueryResult(
                    albumEntry.idList, albumEntry.galleryIdList,
                    albumEntry.memoriesId, albumEntry.memoriesName, albumEntry.count,
                    resultType
                )
            }
        }
        GLog.d(TAG, "[assemble] costTime:${GLog.getTime(startTime)}")

        return singleQueryResult
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}MemoriesSearchResultAssembler"
    }
}