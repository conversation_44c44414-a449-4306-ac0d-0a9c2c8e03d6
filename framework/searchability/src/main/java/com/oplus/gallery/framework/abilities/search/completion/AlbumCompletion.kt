/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumCompletion.kt
 ** Description:图集补全
 ** Version: 1.0
 ** Date: 2023/9/28
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/28      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import android.os.Bundle
import com.oplus.andes.photos.kit.search.data.AndesAlbumEntry.TYPE_ENHANCE_TEXT_ALBUM
import com.oplus.andes.photos.kit.search.data.AndesAlbumEntry.TYPE_PORTRAIT_BLUR
import com.oplus.andes.photos.kit.search.data.AndesAlbumEntry.TYPE_VIDEO_ALBUM
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.andes.photos.kit.search.data.QueryCompletionEntry
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry.Companion.TYPE_RECYCLE_ALBUM
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier

/**
 * 图集补全
 */
internal class AlbumCompletion(
    context: Context,
    private val keywordCacheManager: KeywordCacheManager,
) : BaseCompletionAbility(context, keywordCacheManager) {

    override fun getCompletionType(): Int = CompletionType.TYPE_ALBUM

    override fun queryComplete(query: String): List<QueryCompletionEntry> {
        val completeAlbums = complete(query)
        GLog.d(TAG, "[queryComplete] query:$query, completeAlbums:${completeAlbums.map { it.bucketName }}")
        return completeAlbums.map {
            QueryCompletionEntry(it.bucketName, getAlbumType(it.type), bucketIdsToString(it.bucketIdList), query)
        }
    }

    override fun queryComplete(query: String, extra: Bundle?): List<QueryCompletionEntry> { return queryComplete(query) }

    private fun bucketIdsToString(bucketIds: List<Int>): String {
        val sb = StringBuilder()
        bucketIds.forEachIndexed { index, bucketId ->
            if (index == bucketIds.size - 1) {
                sb.append(bucketId)
            } else {
                sb.append(bucketId).append(TextUtil.SPLIT_COMMA_SEPARATOR)
            }
        }
        return sb.toString()
    }

    /**
     * 搜索词尝试按图集名称补全
     *
     * @param query 搜索词
     * @return 图集信息列表
     */
    fun complete(query: String): List<AlbumEntry> {
        GTrace.traceBegin("Album.complete")
        val startTime = System.currentTimeMillis()
        // 缓存还未建立，则直接查询图集数据
        var albumEntrySet = keywordCache.albumEntrySet
        if (albumEntrySet.isNullOrEmpty()) {
            // 查询本地所有图集信息
            keywordCacheManager.updateAlbumSetCache()
            albumEntrySet = keywordCache.albumEntrySet
        }

        if (albumEntrySet == null) {
            GLog.d(TAG, "[complete] query:$query, albumEntrySet is null")
            return emptyList()
        }

        val completionAlbum = mutableListOf<AlbumEntry>()
        val classifier: SearchClassifier = buildClassifier(query)
        albumEntrySet.forEach { albumEntry ->
            // 图集名称不为空
            if (albumEntry.bucketName.isNullOrEmpty().not()) {
                val albumName = albumEntry.bucketName?.lowercase()
                albumName?.let {
                    // 中文采取包含的判定策略(图集名包含query)；英文采取startWith的判定策略(图集名以query开始)
                    if (classifier.contains(it, query)) {
                        completionAlbum.add(albumEntry)
                    }
                }
            }
        }
        GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
        GTrace.traceEnd()
        return completionAlbum
    }

    private fun getAlbumType(alumType: Int): Int = when (alumType) {
        TYPE_VIDEO_ALBUM -> EntityType.ENTITY_ALBUM_VIDEO
        TYPE_RECYCLE_ALBUM -> EntityType.ENTITY_ALBUM_RECYCLE
        TYPE_ENHANCE_TEXT_ALBUM -> EntityType.ENTITY_ALBUM_ENHANCE_TEXT
        TYPE_PORTRAIT_BLUR -> EntityType.ENTITY_PORTRAIT_BLUR
        else -> EntityType.ENTITY_ALBUM_NORMAL
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}AlbumCompletion"
    }
}