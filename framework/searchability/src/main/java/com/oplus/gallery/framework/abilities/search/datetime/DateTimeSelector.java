/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DateTimeSelector.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2017/02/07
 ** Author:shujian@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** shu<PERSON><PERSON>@A<PERSON>.Gallery3D          2017/02/07    1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.search.datetime;

import android.content.Context;
import android.content.res.Resources;
import android.util.ArraySet;

import com.oplus.gallery.framework.abilities.search.cache.KeywordCache;
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Set;

public class DateTimeSelector {
    public static final int YEAR_START_NUM = 1970;
    public static final int YEAR_END_NUM = 2099;
    public static final int MONTH_TOTAL_NUM = 12;
    public static final int YEAR_START_TWO_NUM = 19;
    public static final int YEAR_END_THREE_NUM = 209;

    private static final int[] DAYS_IN_MONTH = new int[]{
            31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31
    };
    private AttachDateTimeRu mAttachDateTimeRu;
    private AttachDateTimeVi mAttachDateTimeVi;
    private AttachDateTimeInd mAttachDateTimeInd;
    private AttachDateTimeJap mAttachDateTimeJap;
    private ForeignDateTime mForeignDateTime;
    private ChineseDateTime mChineseDateTime;

    private Context mContext;

    public DateTimeSelector(Context context) {
        mContext = context;
        mForeignDateTime = new ForeignDateTime(context);
        mChineseDateTime = new ChineseDateTime(context);
        mAttachDateTimeVi = new AttachDateTimeVi(context);
        mAttachDateTimeInd = new AttachDateTimeInd(context);
        mAttachDateTimeRu = new AttachDateTimeRu(context);
        mAttachDateTimeJap = new AttachDateTimeJap(context);
    }

    /**
     * 获取所有内建的中文时间关键词
     * @return 内建的中文时间关键词列表
     */
    public List<String> getAllChineseDateTimeKeywords() {
        return mChineseDateTime.getAllDateTimeKeywords();
    }

    /**
     * 获取所有内建的英文时间关键词
     *
     * @return 内建的英文时间关键词列表
     */
    public List<String> getAllForeignDateTimeKeywords() {
        return mForeignDateTime.getAllDateTimeKeywords();
    }

    public void setKeywordCache(KeywordCache keywordCache) {
        mForeignDateTime.setKeywordCache(keywordCache);
        mChineseDateTime.setKeywordCache(keywordCache);
    }

    public Set<String> completeDateTimeFromSelector(String keyword) {
        Set<String> keywordSet = new ArraySet<>();
        if (LocaleUtils.isChinese(ContextGetter.context)) {
            keywordSet.addAll(mChineseDateTime.completeDateTime(keyword));
            if (keywordSet.isEmpty()) {
                if (LocaleUtils.isTraditionalChinese(ContextGetter.context)) {
                    keywordSet.addAll(mForeignDateTime.completeDateTime(keyword));
                }
            }
        } else {
            List<String> keywordList = mForeignDateTime.completeDateTime(keyword);
            if (keywordList.isEmpty()) {
                if (LocaleUtils.isIndonesia(ContextGetter.context)) {
                    keywordList = mAttachDateTimeInd.completeDateTime(keyword);
                } else if (LocaleUtils.isRussia(ContextGetter.context)) {
                    keywordList = mAttachDateTimeRu.completeDateTime(keyword);
                }
            }
            if (keywordList.size() > 0) {
                keywordSet.addAll(keywordList);
            }
        }
        if (keywordSet.isEmpty()) {
            keywordSet.add(keyword);
        }
        return keywordSet;
    }

    public ShootDateTime getShootDateTimeFromSelector(String keyword) {
        ShootDateTime shootDateTime = null;
        if (LocaleUtils.isChinese(ContextGetter.context)) {
            shootDateTime = mChineseDateTime.getShootDateTime(keyword);
        } else {
            shootDateTime = mForeignDateTime.getShootDateTime(keyword);
        }
        if ((shootDateTime == null) || shootDateTime.isEmpty()) {
            // HK and TW not only support chinese, but also support english.
            if (LocaleUtils.isTraditionalChinese(ContextGetter.context)) {
                shootDateTime = mForeignDateTime.getShootDateTime(keyword);
            } else if (LocaleUtils.isIndonesia(ContextGetter.context)) {
                shootDateTime = mAttachDateTimeInd.getShootDateTime(keyword);
            } else if (LocaleUtils.isVietnam(ContextGetter.context)) {
                shootDateTime = mAttachDateTimeVi.getShootDateTime(keyword);
            } else if (LocaleUtils.isRussia(ContextGetter.context)) {
                shootDateTime = mAttachDateTimeRu.getShootDateTime(keyword);
            } else if (LocaleUtils.isJapan(ContextGetter.context)) {
                shootDateTime = mAttachDateTimeJap.getShootDateTime(keyword);
            }
        }
        return shootDateTime;
    }

    public static boolean isValidYear(int year) {
        return (year >= YEAR_START_NUM) && (year <= YEAR_END_NUM);
    }

    public static boolean isValidMonth(int month) {
        return (month >= 1) && (month <= MONTH_TOTAL_NUM);
    }

    public static boolean isValidDay(int year, int month, int day) {
        int maxDay = 31;
        int modeFour = 4;
        if (isValidMonth(month)) {
            maxDay = DAYS_IN_MONTH[month - 1];
            if (isValidYear(year) && (year % modeFour == 0) && ((month - 1) == Calendar.FEBRUARY)) {
                maxDay = DAYS_IN_MONTH[month - 1] + 1;
            }
        }
        return (day >= 1) && (day <= maxDay);
    }

    public static boolean containMonth(int singleNumerical) {
        return (singleNumerical >= 1) && (singleNumerical <= MONTH_TOTAL_NUM);
    }

    public static boolean containYear(int singleNumerical) {
        return (singleNumerical >= YEAR_START_NUM) && (singleNumerical <= YEAR_END_NUM);
    }

    public static boolean matchYear(int singleNumerical) {
        return (singleNumerical >= YEAR_START_TWO_NUM) && (singleNumerical <= YEAR_END_THREE_NUM);
    }

    public static int daysInMouth(Calendar calendar) {
        GregorianCalendar gCalender = (GregorianCalendar) calendar;
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        return (gCalender.isLeapYear(year) && (month == Calendar.FEBRUARY)) ? DAYS_IN_MONTH[month] + 1 : DAYS_IN_MONTH[month];
    }

    public static class DateRange {
        public long start;
        public long end;

        /**
         * condition: unit + divider + value
         * unit: %w = weekday, this value should match format %d
         * %Y = year, this value should match format %04d
         * %m = month, this value should match format %02d
         * %d = day, this value should match format %02d
         * %H = hour, this value should match format %02d
         * divider: ,
         * example: %w, '6', '0'             = every each Saturday & Sunday, that's every Weekend
         * %m, '05'                 = every each May
         * %H, '05', '06'                = every each 5`clock & 6`s clock
         * %Y-%m-%d, '2016-02-21'   = a whole day of 2016-02-21
         */
        public String condition;
    }

    public static class DateInfo {
        public String mShootKeyword;
        public String[] mKeywords;

        public DateInfo(Resources resources, int resId) {
            mKeywords = resources.getStringArray(resId);
        }

        public int indexOfKeyword(String keyword, int start) {
            int index = -1;
            for (String reference : mKeywords) {
                index = keyword.indexOf(reference, start);
                if (index >= 0) {
                    break;
                }
            }
            return index;
        }

        public boolean containsKeyword(String keyword) {
            for (String reference : mKeywords) {
                String ignoreCaseRef = reference.toLowerCase();
                if (ignoreCaseRef.contains(keyword.toLowerCase())) {
                    mShootKeyword = reference;
                    return true;
                }
            }
            return false;
        }

        public boolean containedKeyword(String keyword) {
            for (String reference : mKeywords) {
                String ignoreCaseRef = reference.toLowerCase();
                if (keyword.toLowerCase().contains(ignoreCaseRef)) {
                    mShootKeyword = keyword;
                    return true;
                }
            }
            return false;
        }


        public boolean equalsKeyword(String keyword) {
            for (String reference : mKeywords) {
                String ignoreCaseRef = reference.toLowerCase();
                if (ignoreCaseRef.equalsIgnoreCase(keyword.toLowerCase())) {
                    mShootKeyword = reference;
                    return true;
                }
            }
            return false;
        }

        public boolean indexOfKeyword(String keyword) {
            for (String reference : mKeywords) {
                String ignoreCaseRef = reference.toLowerCase();
                if (ignoreCaseRef.startsWith(keyword.toLowerCase())) {
                    mShootKeyword = reference;
                    return true;
                }
            }
            return false;
        }
    }

}

