/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SearchEntry.kt
 ** Description:存储搜索各种参数信息的对象
 ** Version: 1.0
 ** Date: 2023/8/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/8/31      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.entry

import android.content.Context
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier

/**
 * 存储搜索各种参数信息的对象
 *
 * @property context 上下文对象
 */
internal data class SearchEntry(private val context: Context) {
    var searchType: Int = SearchType.TYPE_MASK_TYPE
    var searchOcrQuery = false
    var searchForceQuery = false
    var searchKeywords: Array<String>? = null
    var searchKeyword: String? = null
    var searchGroupId: String? = null
    var searchLabelId = LabelSearchEngine.INVALID_ID
    var searchClassifier: SearchClassifier = SearchClassifier(context)

    override fun toString(): String {
        return "SearchEntry(searchType=$searchType, searchOcrQuery=$searchOcrQuery, " + "searchForceQuery=$searchForceQuery, " +
                "searchKeywords=${searchKeywords?.contentToString()}, searchKeyword=$searchKeyword, searchGroupId=$searchGroupId, " +
                "searchLabelId=$searchLabelId)"
    }
}