/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendYearQuery.kt
 ** Description: 年推荐查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.framework.abilities.search.entry.KeywordString
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.io.IOUtils
import java.util.Calendar

/**
 * 年推荐查询
 *
 * @property forceQuery 是否强制从数据库查询
 * @property recommendCache 推荐缓存
 */
internal class RecommendYearQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache,
) : Query {
    override fun query(): Cursor? {
        return if (!forceQuery && recommendCache.hasYearRecommend()) {
            recommendCache.obtainYearRecommend()
        } else {
            val startTime = System.currentTimeMillis()
            // 今年
            val thisYearNumerical = Calendar.getInstance()[Calendar.YEAR]
            // 去年
            val lastYearNumerical = thisYearNumerical - 1
            // 前年
            val previousYearNumerical = thisYearNumerical - 2
            var cursor: MatrixCursor? = getRecommendYearCursor(lastYearNumerical, KeywordString.lastYearKeyword)
            if (isCursorHaveData(startTime, cursor)) {
                return cursor
            }
            cursor = getRecommendYearCursor(previousYearNumerical, KeywordString.preYearKeyword)
            if (isCursorHaveData(startTime, cursor)) {
                return cursor
            }
            cursor = getRecommendYearCursor(thisYearNumerical, KeywordString.thisYearKeyword)
            if (isCursorHaveData(startTime, cursor)) {
                return cursor
            }
            return null
        }
    }

    private fun isCursorHaveData(startTime: Long, cursor: MatrixCursor?): Boolean {
        cursor?.let {
            if (it.count > 0) {
                if (GProperty.LOG_GALLERY_OPEN) {
                    GLog.v(TAG, "[isCursorHaveData] costTime:${GLog.getTime(startTime)}")
                }
                return true
            } else {
                IOUtils.closeQuietly(cursor)
            }
        }
        return false
    }

    private fun getRecommendYearCursor(yearNumerical: Int, year: String): MatrixCursor? {
        return try {
            GTrace.traceBegin("RecommendYearQuery")
            val cursor = MatrixCursor(RecommendFestivalQuery.COLUMN_RECOMMEND_DATE)
            val queryTime = System.currentTimeMillis()
            val coverEntry = DBSearchUtils.queryDateTimeEntryForYears(yearNumerical)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[getRecommendYearCursor] costTime:${GLog.getTime(queryTime)}")
            }
            if (coverEntry != null && coverEntry.count > 0) {
                cursor.addRow(arrayOf<Any>(year, coverEntry.mediaId, coverEntry.count, coverEntry.mediaType, coverEntry.galleryId))
                recommendCache.updateToYearRecommend(cursor)
                cursor.moveToPosition(-1)
            }
            cursor
        } finally {
            GTrace.traceEnd()
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendYearQuery"
    }
}