/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SqliteAbility.kt
 ** Description: sqlite搜索能力
 ** Version: 1.0
 ** Date: 2023/9/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/31      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.sqlite

import android.database.Cursor
import com.oplus.andes.photos.kit.search.ability.ISqliteAbility
import com.oplus.andes.photos.kit.search.data.SQLiteFieldInfo
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq

/**
 * SQLite搜索能力
 */
internal class SqliteAbility : ISqliteAbility {

    @Suppress("LongMethod")
    override fun getFieldInfoMap(): MutableMap<Int, SQLiteFieldInfo> {
        val fieldInfoMap = mutableMapOf<Int, SQLiteFieldInfo>()
        fieldInfoMap[EntityType.ENTITY_YEAR] = SQLiteFieldInfo(
            EntityType.ENTITY_YEAR,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.YEAR,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_MONTH] = SQLiteFieldInfo(
            EntityType.ENTITY_MONTH,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.MONTH,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_DAY] = SQLiteFieldInfo(
            EntityType.ENTITY_DAY,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.DAY,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_TIME_RANGE] = SQLiteFieldInfo(
            EntityType.ENTITY_TIME_RANGE,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_HOUR_RANGE] = SQLiteFieldInfo(
            EntityType.ENTITY_HOUR_RANGE,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_MONTH_RANGE] = SQLiteFieldInfo(
            EntityType.ENTITY_MONTH_RANGE,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.MONTH,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_FESTIVAL] = SQLiteFieldInfo(
            EntityType.ENTITY_FESTIVAL,
            GalleryStore.Festival.TAB,
            GalleryStore.FestivalColumns.FESTIVAL_NAME,
            false,
            GalleryStore.FestivalColumns.DATE,
            GalleryStore.GalleryColumns.LocalColumns.DAY,
            null
        )
        fieldInfoMap[EntityType.ENTITY_ALBUM_NORMAL] = SQLiteFieldInfo(
            EntityType.ENTITY_ALBUM_MASK,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_PERSON] = SQLiteFieldInfo(
            EntityType.ENTITY_PERSON,
            GalleryStore.ScanFace.TAB,
            GalleryStore.ScanFaceColumns.GROUP_ID,
            false,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            null
        )
        fieldInfoMap[EntityType.ENTITY_LABEL] = SQLiteFieldInfo(
            EntityType.ENTITY_LABEL,
            GalleryStore.ScanLabel.TAB,
            GalleryStore.ScanLabelColumns.SCENE_ID,
            false,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            null
        )
        fieldInfoMap[EntityType.ENTITY_MEMORIES] = SQLiteFieldInfo(
            EntityType.ENTITY_MEMORIES,
            GalleryStore.MemoriesSetmap.TAB,
            GalleryStore.MemoriesSetmapColumns.SET_ID,
            false,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            null
        )
        fieldInfoMap[EntityType.ENTITY_COUNTRY] = SQLiteFieldInfo(
            EntityType.ENTITY_COUNTRY,
            GalleryStore.GeoRoute.TAB,
            GalleryStore.GeoColumns.COUNTRY,
            false,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            null
        )
        fieldInfoMap[EntityType.ENTITY_PROVINCE] = SQLiteFieldInfo(
            EntityType.ENTITY_PROVINCE,
            GalleryStore.GeoRoute.TAB,
            GalleryStore.GeoColumns.PROVINCE,
            false,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            null
        )
        fieldInfoMap[EntityType.ENTITY_CITY] = SQLiteFieldInfo(
            EntityType.ENTITY_CITY,
            GalleryStore.GeoRoute.TAB,
            GalleryStore.GeoColumns.CITY,
            false,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            null
        )
        fieldInfoMap[EntityType.ENTITY_DISTRICT] = SQLiteFieldInfo(
            EntityType.ENTITY_DISTRICT,
            GalleryStore.GeoRoute.TAB,
            GalleryStore.GeoColumns.DISTRICT,
            false,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            null
        )
        fieldInfoMap[EntityType.ENTITY_STREET] = SQLiteFieldInfo(
            EntityType.ENTITY_STREET,
            GalleryStore.GeoRoute.TAB,
            GalleryStore.GeoColumns.STREET,
            false,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            null
        )
        fieldInfoMap[EntityType.ENTITY_OCR] = SQLiteFieldInfo(
            EntityType.ENTITY_OCR,
            GalleryStore.OcrPages.TAB,
            GalleryStore.OcrPagesColumns.CONTENT,
            false,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            null
        )
        fieldInfoMap[EntityType.ENTITY_FILE_NAME] = SQLiteFieldInfo(
            EntityType.ENTITY_FILE_NAME,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_SPECIAL_LABEL] = SQLiteFieldInfo(
            EntityType.ENTITY_FILE_NAME,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_MODIFY_TIME_RANGE] = SQLiteFieldInfo(
            EntityType.ENTITY_MODIFY_TIME_RANGE,
            GalleryStore.GalleryMedia.TAB,
            GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED,
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_POI] = SQLiteFieldInfo(
            EntityType.ENTITY_POI,
            GalleryStore.PoiGridRoute.TAB,
            GalleryStore.PoiGridColumns.POI_ID,
            false,
            GalleryStore.PoiGridColumns.GPS_KEY,
            GalleryStore.GalleryColumns.LocalColumns.GPS_KEY,
            null
        )
        fieldInfoMap[EntityType.ENTITY_CAPTION_SEARCH] = SQLiteFieldInfo(
            EntityType.ENTITY_CAPTION_SEARCH,
            GalleryStore.ScanCaption.TAB,
            listOf(GalleryStore.ScanCaption.EMBEDDING_CONTENT0,
                GalleryStore.ScanCaption.EMBEDDING_CONTENT1,
                GalleryStore.ScanCaption.EMBEDDING_CONTENT2,
                GalleryStore.ScanCaption.EMBEDDING_CONTENT3),
            false,
            GalleryStore.ScanCaption.LOCAL_MEDIA_TABLE_ID,
            GalleryStore.GalleryColumns.LocalColumns._ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_MEDIA_TYPE] = SQLiteFieldInfo(
            EntityType.ENTITY_MEDIA_TYPE,
            GalleryStore.GalleryMedia.TAB,
            listOf(
                GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE,
                GalleryStore.GalleryColumns.LocalColumns.TAGFLAGS
            ),
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        fieldInfoMap[EntityType.ENTITY_RESOLUTION] = SQLiteFieldInfo(
            EntityType.ENTITY_RESOLUTION,
            GalleryStore.GalleryMedia.TAB,
            listOf(
                GalleryStore.GalleryColumns.LocalColumns.WIDTH,
                GalleryStore.GalleryColumns.LocalColumns.HEIGHT
            ),
            true,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            null
        )
        return fieldInfoMap
    }

    override fun queryBySqlString(sqlString: String?): Cursor? {
        return RawQueryReq.Builder<Cursor?>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.RAW_QUERY)
            .setQuerySql(sqlString)
            .setConvert(CursorConvert())
            .build()
            .exec()
    }
}