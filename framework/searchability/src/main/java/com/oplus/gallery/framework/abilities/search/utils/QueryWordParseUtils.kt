/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - QueryWordParseUtils.kt
 ** Description: 搜索词的解析工具类
 ** Version: 1.0
 ** Date: 2023/9/5
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/9/5      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.utils

import android.net.Uri
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.util.debug.GLog
import java.util.Locale

/**
 * 搜索词的解析工具类
 */
internal object QueryWordParseUtils {

    private const val TAG = "QueryParseUtils"

    /**
     * 从keyword中解析出input（用户输入的搜索词）
     *
     * @param keyword uri中keyword字符串
     * @return 用户输入的搜索词
     */
    @JvmStatic
    fun getQueryWord(keyword: String?, toLowerCase: Boolean): String? {
        if (keyword.isNullOrEmpty()) {
            return null
        }
        val queryUri = Uri.parse(keyword)
        val word = queryUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_INPUT)
        GLog.v(TAG, "[getQueryWord] word = $word, keyword = $keyword")
        return if (word != null && toLowerCase) {
            word.lowercase(Locale.getDefault())
        } else word
    }

    /**
     * 从keyword中解析出forceQuery
     *
     * @param keyword uri中keyword字符串
     * @return 是否强制从数据库查询
     */
    @JvmStatic
    fun getQueryForce(keyword: String?): Boolean {
        if (keyword.isNullOrEmpty()) {
            return false
        }
        val queryUri = Uri.parse(keyword)
        val queryValue = queryUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_FORCE)
        return "true".equals(queryValue, ignoreCase = true)
    }

    /**
     * 从keyword中解析出label_id
     *
     * @param keyword uri中keyword字符串
     * @return label ID
     */
    @JvmStatic
    fun getQueryLabelId(keyword: String?): Int {
        if (keyword.isNullOrEmpty()) {
            GLog.w(TAG, "getQueryLabelId keyword is null ")
            return LabelSearchEngine.INVALID_ID
        }
        val labelId = Uri.parse(keyword).getQueryParameter(SearchSuggestionProviderUtil.QUERY_LABEL_ID)
        if (TextUtils.isEmpty(labelId)) {
            return LabelSearchEngine.INVALID_ID
        }
        GLog.d(TAG, "getQueryLabelId labelId = $labelId, keyword = $keyword")
        try {
            return labelId!!.toInt()
        } catch (e: NumberFormatException) {
            GLog.w(TAG, "getQueryLabelId err w: $labelId")
        }
        return LabelSearchEngine.INVALID_ID
    }

    /**
     * 从keyword中解析出ocr
     *
     * @param keyword uri中keyword字符串
     * @return 是否是ocr query
     */
    @JvmStatic
    fun getQueryOcr(keyword: String?): Boolean {
        if (keyword.isNullOrEmpty()) {
            return false
        }
        val queryUri = Uri.parse(keyword)
        val value = queryUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_OCR)
        GLog.v(TAG, "[getQueryOcr] value = $value, keyword = $keyword")
        return value == null || "true".equals(value, ignoreCase = true)
    }

    /**
     * 从keyword中解析出groupId
     *
     * @param keyword uri中keyword字符串
     * @return groupId
     */
    @JvmStatic
    fun getQueryGroupId(keyword: String?): String? {
        if (keyword.isNullOrEmpty()) {
            return null
        }
        val queryUri = Uri.parse(keyword)
        val groupId = queryUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_GROUPID)
        GLog.v(TAG, "[getQueryGroupId] groupId = $groupId, keyword = $keyword")
        return groupId
    }

    /**
     * 从keyword中解析出bucketId
     *
     * @param keyword uri中keyword字符串
     * @return bucketId 当bucket id<=0时，走全量搜索
     */
    @JvmStatic
    fun getQueryBucketId(keyword: String?): String? {
        if (keyword.isNullOrEmpty()) {
            return null
        }
        val queryUri = Uri.parse(keyword)
        val bucketId = queryUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_BUCKETID)
        GLog.v(TAG, "[getQueryBucketId] bucketId = $bucketId, keyword = $keyword")
        if ((bucketId != null) && (bucketId.toInt() <= 0)) {
            return null
        }
        return bucketId
    }

    /**
     * 从keyword中解析出bucketId
     *
     * @param keyword uri中keyword字符串
     * @return bucketId
     */
    @JvmStatic
    fun getQueryMemoryId(keyword: String?): String? {
        if (keyword.isNullOrEmpty()) {
            return null
        }
        val queryUri = Uri.parse(keyword)
        val memoryId = queryUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_MEMORYID)
        GLog.v(TAG, "[getQueryBucketId] memoryId = $memoryId, keyword = $keyword")
        return memoryId
    }

    /**
     * 从keyword中解析出bucketId
     *
     * @param keyword uri中keyword字符串
     * @return bucketId
     */
    @JvmStatic
    fun getQueryLocationType(keyword: String?): Int? {
        if (keyword.isNullOrEmpty()) {
            return null
        }
        val queryUri = Uri.parse(keyword)
        val locationType = queryUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_LOCATIONTYPE)
        GLog.v(TAG, "[getQueryBucketId] locationType= $locationType, keyword = $keyword")
        kotlin.runCatching {
            return locationType?.toInt()
        }.onFailure {
            GLog.e(TAG, "[getQueryLocationType] error:", it)
        }
        return null
    }
}