/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MultiModalAbilityImpl.kt
 ** Description:多模态-实现类
 ** Version: 1.0
 ** Date: 2023/8/29
 ** Author: W9009912
 ** TAG: MultiModalAbilityImpl
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                     <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** W9009912        2023/8/29        1.0         first created
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.search.multimodal

import android.content.Context
import android.graphics.Bitmap
import com.ai.slp.engine.BaseEngine
import com.ai.slp.engine.ImageClipEngine
import com.ai.slp.engine.SearchResult
import com.ai.slp.listener.IErrorListener
import com.ai.slp.listener.IStateChangeListener
import com.oplus.appability.AbsAppAbility
import com.oplus.appability.IAbilityBus
import com.oplus.appability.IAbilityConfig
import com.oplus.gallery.business_lib.BusinessLibHelper.PREFERENCE_USE_OPEN_NETWORK
import com.oplus.gallery.business_lib.util.AssetHelper
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value.RESULT_MULTIMODAL_FAIL
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value.RESULT_MULTIMODAL_SUCCESS
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Download.MULTI_MODAL_CURRENT_VERSION_PATH
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.Download.MULTI_MODAL_DICT_VULGARISMS_PATH
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.utils.AIUnitUtils
import java.io.File

/**
 * 多模态
 */
class MultiModalAbilityImpl(private val context: Context) : AbsAppAbility(), IMultiModalAbility {
    override val domainInstance: AutoCloseable = this

    private var configAbility: IConfigSetterAbility? = null

    private var clipEngine: ImageClipEngine? = null

    private val currentThreadInfo = Thread.currentThread().id.toString() + TextUtil.STRIKE + Thread.currentThread().name

    override var currentModelVersion: String
        get() = configAbility?.getStringConfig(MULTI_MODAL_CURRENT_VERSION_PATH) ?: TextUtil.EMPTY_STRING
        set(value) {
            configAbility?.setStringConfig(MULTI_MODAL_CURRENT_VERSION_PATH, value)
        }

    /**
     * 不雅词词典的路径，例如/data/data/包名/files/component/Dict_Vulgarisms/Dict_Vulgarisms.txt
     */
    override var dictVulgarismsPath: String
        get() {
            return configAbility?.getStringConfig(MULTI_MODAL_DICT_VULGARISMS_PATH) ?: TextUtil.EMPTY_STRING
        }
        set(value) {
            configAbility?.setStringConfig(MULTI_MODAL_DICT_VULGARISMS_PATH, value)
        }

    /**
     * 是否在扫描中
     */
    override var isScanning: Boolean = false

    /**
     * 是否在下载模型中
     */
    override var isDownloading: Boolean = false

    /**
     * 初始化引擎
     *
     *  @return 是否初始化成功。true成功，false失败
     */
    @Synchronized
    override fun initSlpSdk(): Boolean {
        GLog.d(TAG, LogFlag.DL, "initSlpSdk, thread = $currentThreadInfo")
        if (MultiProcessSpUtil.getBoolean(PREFERENCE_USE_OPEN_NETWORK, false).not()) {
            GLog.w(TAG, LogFlag.DL) { "initSlpSdk, no network" }
            return false
        }
        val startTime = System.currentTimeMillis()

        // 能力是否可用
        if (AIUnitUtils.isDetectAvailable(context, ImageClipEngine.DETECT_NAME).not()) {
            GLog.w(TAG, LogFlag.DL, "initSlpSdk, ImageClipEngine isDetectAvailable: false")
            return false
        }
        // 如果已经初始化且模型没有变，则无需要重新初始化
        if (clipEngine?.getState() == BaseEngine.STATE_STARTED) {
            GLog.w(TAG, LogFlag.DL,  "initSlpSdk, engine already started!")
            return true
        }

        // 模型变更了，则需要释放之前的引擎，然后重新创建新的引擎实例并启动
        reInitEngine()
        GLog.d(TAG, LogFlag.DL, "initSlpSdk, thread = $currentThreadInfo, " +
                "clipEngine = $clipEngine, costTime = ${GLog.getTime(startTime)}")

        return clipEngine != null
    }

    private fun reInitEngine() {
        clipEngine?.release()
        createEngine()
        startEngine()
    }

    private fun createEngine() {
        // 创建引擎实例
        clipEngine = ImageClipEngine(context)
        clipEngine?.errorListener = object : IErrorListener {
            override fun onError(code: Int, msg: String?) {
                GLog.e(TAG, LogFlag.DL, "createEngine. code: $code, msg: $msg")
            }
        }
        // 配置数据缓存路径
        clipEngine?.setParamsValue(ImageClipEngine.CLIP_DIR_CACHE, context.filesDir.absolutePath)
    }

    /**
     * 若状态不为STATE_START，调用clipEngine.start()
     */
    private fun startEngine() {
        GLog.d(TAG, LogFlag.DL, "startEngine, clipEngine = $clipEngine")
        if (clipEngine?.getState() != BaseEngine.STATE_STARTED) {
            clipEngine?.start()
            currentModelVersion = clipEngine?.getImageModelVersion().orEmpty()
            setDictVulgarisms()
        }
    }

    /**
     * 配置不雅词词典
     */
    private fun setDictVulgarisms() {
        clipEngine?.let {
            val data = if (dictVulgarismsPath.isEmpty()) {
                GLog.d(TAG, LogFlag.DL, "setDictVulgarisms, load dict_vulgarisms from asset")
                AssetHelper.getAssetData(context, FILE_NAME_DICT_VULGARISMS)
            } else {
                GLog.d(TAG, LogFlag.DL, "setDictVulgarisms, load dict_vulgarisms from component")
                File(dictVulgarismsPath).readText()
            }
            it.loadReject(data)
        }
    }

    /**
     * 调用引擎生成向量
     * @param localMediaTableId local_media表的_id
     * @param bitmap 图片bitmap
     * @param hasFace 是否有人脸。拒识需要用到人脸信心
     * @return 成功true，失败false
     */
    @Synchronized
    override fun processImage(
        localMediaTableId: Long,
        bitmap: Bitmap,
        hasFace: Boolean
    ): Boolean {
        val startTime = System.currentTimeMillis()
        startEngine()
        GLog.d(TAG, LogFlag.DL, "processImage, thread = $currentThreadInfo, clipEngine = $clipEngine")
        var ret = false
        kotlin.runCatching {
            ret = clipEngine?.processImage(localMediaTableId, bitmap, hasFace) ?: false
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "processImage, fail", it)
        }
        if (DEBUG_SEARCH) {
            GLog.d(TAG, LogFlag.DL, "processImage, costTime = ${GLog.getTime(startTime)}")
        }
        return ret
    }

    /**
     * 内存数据输出到文件
     * processImage之后、deleteImage之后需要调用此方法
     * @return 成功true，失败false
     */
    @Synchronized
    override fun flushData(): Boolean {
        val startTime = System.currentTimeMillis()
        val ret = clipEngine?.flushData() ?: false
        if (DEBUG_SEARCH) {
            GLog.d(TAG, LogFlag.DL, "flushData costTime = ${GLog.getTime(startTime)}")
        }
        return ret
    }

    /**
     * 释放
     */
    @Synchronized
    override fun release() {
        val startTime = System.currentTimeMillis()
        GLog.d(TAG, LogFlag.DL, "release, clipEngine = $clipEngine")
        clipEngine?.stop()
        clipEngine?.release()
        clipEngine = null
        if (DEBUG_SEARCH) {
            GLog.d(TAG, LogFlag.DL, "release costTime = ${GLog.getTime(startTime)}")
        }
    }

    /**
     * 是否有新版本
     * 如果newVersion不为空，且记录的版本不等于newVersion。说明有新版本
     *
     * @param versionPath 模型路径
     * @return 是true，否false
     */
    @Synchronized
    override fun hasNewVersion(versionPath: String): Boolean {
        val newModelVersion = clipEngine?.getImageModelVersion().orEmpty()
        return (newModelVersion.isEmpty().not()) && (versionPath != newModelVersion)
    }

    /**
     * 删除图片向量
     * @param needDeleteList 需要删除的图片列表
     * @return 成功true，失败false
     */
    @Synchronized
    override fun deleteDataByLocalMediaTableIds(
        needDeleteList: MutableList<MultiModalImageInfo>
    ): MutableList<Long> {
        GLog.d(TAG, LogFlag.DL, "deleteDataByLocalMediaTableIds. ${needDeleteList.getOrNull(0)?.versionModelPath}")
        val result = mutableListOf<Long>()

        // 版本和List<Id> 组成map
        val map = needDeleteList.groupBy(
            MultiModalImageInfo::versionModelPath,
            MultiModalImageInfo::mGalleryId
        )

        map.forEach { (_, idList) ->
            val deletedList = mutableListOf<Long>()
            idList.forEach {
                if (clipEngine?.deleteImageVectorDataById(it) == true) {
                    deletedList.add(it)
                }
            }
            if (clipEngine?.flushData() == true) {
                result.addAll(deletedList)
            }
            this.release()
        }
        return result
    }

    /**
     * 文字搜索图片信息
     * @param keyword 搜索关键字
     * @return MultiModalImageInfo集合
     */
    @Synchronized
    override fun searchImgByText(
        keyword: String
    ): List<MultiModalImageInfo> {
        kotlin.runCatching {
            if (!initSlpSdk()) {
                // 初始化引擎失败，直接return
                GLog.e(TAG, LogFlag.DL,  "searchImgByText, init multi modal engine fail, return")
                return mutableListOf()
            }
            // 校验数据是否有效
            val dataValid = this.checkDataValid()
            if (dataValid.not()) {
                GLog.e(TAG,  LogFlag.DL, "searchImgByText, data is Invalid, return")
                return mutableListOf()
            }

            val imgIds = clipEngine?.searchImage(keyword)?.idArray
            val scanData = MultiModalScanProviderHelper.getScanDataByLocalMediaTableIds(imgIds)
            val result = scanData.filter {
                it.mInvalid.not() && it.mIsRecycled.not()
            }
            GLog.d(TAG,  LogFlag.DL, "searchImgByText, scanData.size = ${scanData.size}, result.size = ${result.size}")
            return result
        }.onFailure {
            GLog.e(TAG,  LogFlag.DL, "searchImgByText error ", it)
        }
        this.release()
        return mutableListOf()
    }

    /**
     * 获取多模阈值
     */
    override fun getThreshold(): Float? {
        // 多模阈值获取前必须初始化且调用了start
        launchEngine()
        val threshold = clipEngine?.getRecommendThreshold()
        GLog.d(TAG, LogFlag.DL) { "getThreshold, threshold = $threshold" }
        return threshold
    }

    /**
     * 检查是否具有老的模型的数据
     * @return  true 表示存在， false 表示不存在
     */
    override fun hasOldModelData(): Boolean {
        return clipEngine?.hasOldModelData() ?: false
    }

    /**
     * 清除老模型的数据
     * @return  true 表示执行成功，false 表示执行失败
     */
    override fun removeOldModelData(): Boolean {
        return clipEngine?.removeOldModelData() ?: false
    }

    /**
     * ---------------实现com.oplus.andes.photos.kit.search.ability.IMultiModalAbility开始--------------
     */
    /**
     * 强制停止引擎
     */
    @Synchronized
    override fun cancel() {
        clipEngine?.stop()
    }

    /**
     * 检查数据是否可以用
     */
    override fun checkDataValid(): Boolean {
        launchEngine()
        GLog.d(TAG, LogFlag.DL, "checkDataValid, clipEngine = $clipEngine")
        return clipEngine?.checkDataValid() ?: false
    }

    /**
     * 检查id是否存在
     */
    override fun checkIdExist(id: Long): Boolean {
        launchEngine()
        return clipEngine?.checkIdExist(id) ?: false
    }

    /**
     * 获取状态
     */
    override fun getStatus(): Int {
        launchEngine()
        return clipEngine?.getState() ?: BaseEngine.STATE_STOPPED
    }

    /**
     * 搜索
     */
    @Synchronized
    override fun searchImage(query: String?): SearchResult? {
        launchEngine()
        GLog.d(TAG, LogFlag.DL, "searchImage, thread = $currentThreadInfo, clipEngine = $clipEngine")
        val result = clipEngine?.searchImage(query.orEmpty())
        GLog.d(TAG, LogFlag.DL) { "searchImage, result = ${result?.toString()}" }
        return result?.apply {
            if (code == 1) {
                SearchTrackHelper.trackSearchMultimodalResult(RESULT_MULTIMODAL_SUCCESS)
            } else {
                SearchTrackHelper.trackSearchMultimodalResult(RESULT_MULTIMODAL_FAIL)
            }
        }
    }

    /**
     * 设置错误监听
     */
    override fun setErrorListener(listener: IErrorListener) {
        launchEngine()
        clipEngine?.errorListener = listener
    }

    /**
     * 设置状态变化监听
     */
    override fun setStateChangeListener(listener: IStateChangeListener) {
        launchEngine()
        clipEngine?.stateChangeListener = listener
    }

    /**
     * 启动引擎
     */
    @Synchronized
    private fun launchEngine() {
        if (clipEngine?.getState() != BaseEngine.STATE_STARTED) {
            GLog.d(TAG,  LogFlag.DL, "start, thread = $currentThreadInfo")
            initSlpSdk()
        }
    }

    /**
     * 停止引擎
     */
    @Synchronized
    override fun stop() {
        GLog.d(TAG, LogFlag.DL, "stop")
        this.release()
    }

    /**
     * ---------------实现com.oplus.andes.photos.kit.search.ability.IMultiModalAbility结束-------------
     */

    override fun onLoad(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        super.onLoad(abilityBus, abilityConfig)
        configAbility = abilityBus.requireAbility(IConfigSetterAbility::class.java)
    }

    override fun onUnload(abilityBus: IAbilityBus, abilityConfig: IAbilityConfig) {
        GLog.d(TAG, LogFlag.DL, "onUnload")
        super.onUnload(abilityBus, abilityConfig)
        configAbility?.close()
        configAbility = null
        this.release()
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}MultiModalAbilityImpl"

        /**
         * 不雅词词典文件名
         */
        private const val FILE_NAME_DICT_VULGARISMS = "Dict_Vulgarisms.txt"
    }
}