/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  DmpOcrPagesContentProcessor.kt
 * * Description:  处理ocr_pages_v2_content表的数据变化
 * * Version: 1.0
 * * Date : 2025/01/11
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/01/11     1.0        create
 * *  pusong        2025/07/14     1.1        move
 ************************************************************/
package com.oplus.gallery.framework.abilities.search.dmpsync.processor

import android.content.ContentValues
import com.oplus.gallery.framework.abilities.search.dmpsync.DmpSearchSyncHelper.TASK_ENQUEUE_DATA_UPDATE_QUEUE
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_NORMAL
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag

/**
 * 处理ocr_pages_v2_content表的数据变化信息，回调给DmpSearchSyncHelper
 */
class DmpOcrPagesContentProcessor : DmpTableChangeProcessor() {
    override fun getTag(): String {
        return TAG
    }

    override fun processInsert(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processInsert size:${contentValuePairList.size}" }
        val pathSet = HashSet<String>()
        contentValuePairList.forEach {
            val path = it.first.getAsString(GalleryStore.OcrPagesContent.C0_DATA)
            val invalid = it.first.getAsInteger(GalleryStore.OcrPagesContent.C1_INVALID)
            val content = it.first.getAsString(GalleryStore.OcrPagesContent.C3_CONTENT)
            if ((invalid == INVALID_NORMAL) && !content.isNullOrEmpty()) {
                pathSet.add(path)
            }
        }
        onTableChange(pathSet, TASK_ENQUEUE_DATA_UPDATE_QUEUE)
    }

    override fun processDelete(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processDelete size:${contentValuePairList.size}" }
        contentValuePairList.mapNotNull {
            it.first.getAsString(GalleryStore.OcrPagesContent.C0_DATA).takeIf { !it.isNullOrEmpty() }
        }.toHashSet().let { onTableChange(it, TASK_ENQUEUE_DATA_UPDATE_QUEUE) }
    }

    override fun processUpdate(contentValuePairList: MutableList<Pair<ContentValues, ContentValues?>>) {
        if (contentValuePairList.isEmpty()) return
        GLog.d(TAG, LogFlag.DL) { "processUpdate size:${contentValuePairList.size}" }
        val pathSet = HashSet<String>()
        contentValuePairList.forEach {
            val newPath = it.first.getAsString(GalleryStore.OcrPagesContent.C0_DATA)
            val oldPath = it.second?.getAsString(GalleryStore.OcrPagesContent.C0_DATA)
            val newContent = it.first.getAsString(GalleryStore.OcrPagesContent.C3_CONTENT)
            val oldContent = it.second?.getAsString(GalleryStore.OcrPagesContent.C3_CONTENT)

            if (oldPath == null) {
                return@forEach
            }

            if (newContent != oldContent) {
                pathSet.add(newPath)
            }
        }
        onTableChange(pathSet, TASK_ENQUEUE_DATA_UPDATE_QUEUE)
    }

    companion object {
        private const val TAG = "DmpOcrPagesContentProcessor"
    }
}