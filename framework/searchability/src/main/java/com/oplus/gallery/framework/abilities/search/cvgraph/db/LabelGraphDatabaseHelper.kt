/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SmartLabelGraphDatabaseHelper.kt
 ** Description: 对知识图谱数据库的读取、拷贝及文件校验管理
 ** about app info
 **
 ** Version: 1.1
 ** Date: 2023/08/09
 ** Author: <PERSON><PERSON>@Apps.Gallery
 ** TAG: SH_INSTITUTE_SMART_SEARCH
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/2/9     1.0         init
 ** <EMAIL>           2023/08/09   1.1         rebase
 ** <EMAIL>              2023/2/9     1.2         新增so动态加载
 ** <EMAIL>              2023/12/14   1.3         通过数字信封下发图谱秘钥
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cvgraph.db

import android.content.Context
import com.cv.labelgraph.LabelGraphSQLiteDatabase
import net.sqlcipher.database.SQLiteDatabase
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.extraction.labelgraph.BaseLabelGraphModelDownloadConfig
import java.io.File
import java.nio.file.NoSuchFileException

/**
 * 对知识图谱数据库的读取、拷贝及文件校验管理
 */
object LabelGraphDatabaseHelper {

    private const val TAG = "LabelGraphDatabaseHelper"

    /**
     * 因为图谱数据下载和拷贝不是每次都会执行的，所以只能在使用图谱能力的时候做一次校验，确保数据库是成功拷贝的
     * 1.检查拷贝状态，如果是false，则需要看下本地是否有已经下载好的图谱资源文件并执行一次拷贝
     * 2.然后再次检查拷贝状态和so的加载状态
     * 3.用LabelGraphSQLiteDatabase解密库解密图谱数据库
     */
    @JvmStatic
    fun openCVGraph(context: Context, curConfig: BaseLabelGraphModelDownloadConfig): SQLiteDatabase? {
        // 如果拷贝状态是失败的，并且本地是否有已经下载好的图谱数据资源
        if (!curConfig.getLabelGraphSourceCopiedState(context) && curConfig.getModelVersion(context) > 0) {
            // 再次拷贝
            curConfig.copyCVDatabase(context)
        }

        // 然后再次校验拷贝状态和so加载状态
        return if (curConfig.getLabelGraphSourceCopiedState(context) && loadLabelGraphEngineSo(context, curConfig)) {
            // 如果通过就打开图谱数据库
            val localDBFile = context.getDatabasePath(curConfig.labelGraphDbName)
            openLabelGraphDatabase(context, localDBFile, curConfig)
        } else {
            // 不然就关闭图谱功能
            null
        }
    }

    /**
     * 打开视觉知识图谱数据库
     */
    @JvmStatic
    private fun openLabelGraphDatabase(context: Context, dbFile: File, curConfig: BaseLabelGraphModelDownloadConfig): SQLiteDatabase? {
        var database: SQLiteDatabase? = null
        kotlin.runCatching {
            GLog.d(TAG, LogFlag.DL, "openLabelGraphDatabase modelVersion: ${curConfig.getModelVersion(context)}")
            database =
                if (curConfig.getModelVersion(context) > BaseLabelGraphModelDownloadConfig.LABEL_GRAPH_SHANGHAI_LAST_VERSION) {
                    // 如果图谱模型版本号大于5，则采用安盾方案
                    curConfig.getLabelGraphKey(context).run {
                        SQLiteDatabase.openOrCreateDatabase(dbFile.path, this, null)
                    }
                } else {
                    // 如果图谱模型版本号小于等于5，则用老的上研so解密方案，否则在老版本的相册中如果不升级模型，则会导致无法解密
                    LabelGraphSQLiteDatabase.openDatabase(context, dbFile.path, null)
                }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "openLabelGraphDatabase $it")
        }
        return database
    }

    /**
     * 动态加载图谱加解密so
     * /data/data/com.coloros.gallery3d/files/component/LabelGraphSource/libcv_label_graph.so
     * /data/data/com.coloros.gallery3d/files/component/LabelGraphSource/libsqlcipher.so
     */
    private fun loadLabelGraphEngineSo(context: Context, curConfig: BaseLabelGraphModelDownloadConfig): Boolean {
        kotlin.runCatching {
            loadEngineSo(curConfig.getSoFilePathList(context))
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "loadLabelGraphEngineSo", it)
            return false
        }
        return true
    }


    /**
     * 加载已经下载好的so
     * @param  soFileList 路径集合
     * @throws NoSuchFileException so文件不存在时会抛出异常
     */
    @Throws(NoSuchFileException::class)
    private fun loadEngineSo(soFileList: LinkedHashSet<String>) {
        if (soFileList.isEmpty()) {
            throw NoSuchFileException("soFileList is empty: please check!")
        }
        val time = System.currentTimeMillis()
        GLog.v(TAG, "loadEngineSo start")
        soFileList.forEach {
            val file = File(it)
            if (file.exists()) {
                System.load(it)
            } else {
                throw NoSuchFileException("${file.path} does not exist!")
            }
        }
        GLog.v(TAG, "loadEngineSo end cost time = ${System.currentTimeMillis() - time}")
    }
}