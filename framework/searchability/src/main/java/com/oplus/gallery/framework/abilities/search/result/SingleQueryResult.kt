/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SingleQueryResult.kt
 ** Description: 单个搜索的各类搜索结果组合
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result

import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.entry.SearchResultEntry
import com.oplus.gallery.framework.abilities.search.utils.SearchUtils.galleryIdListToStr
import com.oplus.gallery.framework.abilities.search.utils.SearchUtils.getLocalCountFromEntryList
import com.oplus.gallery.framework.abilities.search.utils.SearchUtils.mediaIdListToStr

/**
 * 单个搜索的各类搜索结果组合
 * 保存一个查询词的所有类型的搜索结果，每个类型的结果以SearchResultEntry列表保存
 *
 * @property keyword 搜索关键词
 */
internal class SingleQueryResult(var keyword: String?) {

    /**
     * 按ocr文字识别搜索匹配的结果
     */
    @JvmField
    var ocrResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 日期搜索匹配结果
     */
    @JvmField
    var dateResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 标签搜索匹配结果
     */
    @JvmField
    var labelResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 图集名搜索匹配结果
     */
    @JvmField
    var albumResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 人物图集名搜索匹配结果
     */
    @JvmField
    var personResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 地点名搜索匹配结果
     */
    @JvmField
    var locationResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 引导标签搜索匹配结果
     */
    @JvmField
    var labelGuideResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 回忆图集名搜索匹配结果
     */
    @JvmField
    var memoriesAlbumResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 特殊标签搜索匹配结果
     */
    @JvmField
    var specialKeywordResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 文件名搜索匹配结果
     */
    @JvmField
    var fileNameKeywordResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 多模态搜索匹配结果
     */
    @JvmField
    var multiModalSearchResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * caption搜索结果
     */
    @JvmField
    var captionSearchResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 补充添加某个搜索维度的结果
     *
     * @param mediaIdEntryList 搜索匹配的媒体信息列表
     * @param resultName 结果名称
     * @param searchType 搜索类型
     */
    internal fun appendQueryResult(mediaIdEntryList: MutableList<MediaIdEntry>?, resultName: String?, searchType: Int) {
        if (mediaIdEntryList.isNullOrEmpty()) {
            GLog.d(TAG, "[appendQueryResult], mediaIdEntryList is empty!")
            return
        }
        if (searchType and SearchType.TYPE_OCR != 0) {
            ocrResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_DATETIME != 0) {
            dateResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_LOCATION != 0) {
            locationResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_LABEL != 0) {
            labelResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_CHILD_LABEL != 0) {
            labelResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_SPECIAL_LABEL != 0) {
            specialKeywordResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_FILE_NAME != 0) {
            fileNameKeywordResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_MULTI_MODAL_SEARCH != 0) {
            multiModalSearchResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
        if (searchType and SearchType.TYPE_CAPTION != 0) {
            captionSearchResult.add(buildSearchResult(mediaIdEntryList, resultName, searchType))
        }
    }

    private fun buildSearchResult(mediaIdEntryList: MutableList<MediaIdEntry>, name: String?, type: Int): SearchResultEntry {
        return SearchResultEntry.Builder()
            .setName(name)
            .setIdStr(mediaIdListToStr(mediaIdEntryList))
            .setGalleryIdStr(galleryIdListToStr(mediaIdEntryList))
            .setMediaIdEntryList(mediaIdEntryList)
            .setCount(mediaIdEntryList.size)
            .setLocalCount(getLocalCountFromEntryList(mediaIdEntryList))
            .setType(type)
            .build()
    }

    fun appendMemoriesAlbumQueryResult(idList: String?, galleryIdList: String?, memoriesId: Int, name: String?, count: Int, type: Int) {
        if (type and SearchType.TYPE_MEMORIES_ALBUM != 0) {
            memoriesAlbumResult.add(
                SearchResultEntry.Builder()
                    .setName(name)
                    .setIdStr(idList)
                    .setGalleryIdStr(galleryIdList)
                    .setCount(count)
                    .setLocalCount(count)
                    .setType(type)
                    .setGroupId(memoriesId)
                    .setCoverId(memoriesId)
                    .setCoverMemoriesId(memoriesId)
                    .build()
            )
        }
    }

    fun appendAlbumQueryResult(mediaIdEntryList: MutableList<MediaIdEntry>, name: String?, albumType: Int, type: Int) {
        if (type and SearchType.TYPE_ALBUM != 0) {
            albumResult.add(
                SearchResultEntry.Builder()
                    .setName(name)
                    .setIdStr(mediaIdListToStr(mediaIdEntryList))
                    .setGalleryIdStr(galleryIdListToStr(mediaIdEntryList))
                    .setMediaIdEntryList(mediaIdEntryList)
                    .setCount(mediaIdEntryList.size)
                    .setLocalCount(getLocalCountFromEntryList(mediaIdEntryList))
                    .setAlbumType(albumType)
                    .setType(type)
                    .build()
            )
        }
    }

    fun appendPersonQueryResult(mediaIdEntryList: MutableList<MediaIdEntry>, groupId: Int, name: String, mediaType: Int, type: Int) {
        if (type and SearchType.TYPE_PERSON != 0) {
            personResult.add(
                SearchResultEntry.Builder()
                    .setMediaIdEntryList(mediaIdEntryList)
                    .setIdStr(mediaIdListToStr(mediaIdEntryList))
                    .setGalleryIdStr(galleryIdListToStr(mediaIdEntryList))
                    .setName(name)
                    .setGroupId(groupId)
                    .setType(type)
                    .setCoverMediaType(mediaType)
                    .build()
            )
        }
    }

    fun appendGuideQueryResult(labelNames: Set<String?>?, name: String?, type: Int) {
        if (labelNames.isNullOrEmpty()) {
            GLog.d(TAG, "[appendGuideQueryResult] labelNames is empty!")
            return
        }
        if (type and SearchType.TYPE_GUIDE_LABEL != 0) {
            labelGuideResult.add(
                SearchResultEntry.Builder()
                    .setName(name)
                    .setCount(labelNames.size)
                    .setLocalCount(labelNames.size)
                    .setIdStr(java.lang.String.join(COMMA, labelNames))
                    .setGalleryIdStr(java.lang.String.join(COMMA, labelNames))
                    .setType(type)
                    .build()
            )
        }
    }

    val isEmpty: Boolean
        get() = (dateResult.isEmpty() && personResult.isEmpty() && locationResult.isEmpty()
                && labelResult.isEmpty() && specialKeywordResult.isEmpty()
                && albumResult.isEmpty() && ocrResult.isEmpty() && memoriesAlbumResult.isEmpty()
                && fileNameKeywordResult.isEmpty() && multiModalSearchResult.isEmpty() && captionSearchResult.isEmpty())

    companion object {
        private const val TAG = "${SearchConst.TAG}SingleQueryResult"
    }
}