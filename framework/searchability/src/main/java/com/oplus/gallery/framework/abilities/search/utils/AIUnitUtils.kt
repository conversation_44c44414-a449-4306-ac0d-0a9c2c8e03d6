/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIUnitUtils.kt
 ** Description:aiunit 工具类
 ** Version: 1.0
 ** Date: 2025/3/4
 ** Author: ********
 ** TAG: AIUnitUtils
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                     <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** ********        2025/3/4        1.0         first created
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.search.utils

import android.content.Context
import android.os.Bundle
import com.oplus.aiunit.toolkits.AISettings

object AIUnitUtils {

    /**
     * 查询能力是否可用，受网络、内存、开关、账号等因素影响
     * @param detectName 接口名称
     * @param extras 自定义参数，能力的可用性等结果与自定义参数存在关联，例如当指定ocr_type为静态识文，则一般返回可用
     */
    @JvmStatic
    fun isDetectAvailable(context: Context, detectName: String, extras: Bundle? = null): Boolean {
        return AISettings.isDetectAvailable(context, detectName, extras)
    }
}