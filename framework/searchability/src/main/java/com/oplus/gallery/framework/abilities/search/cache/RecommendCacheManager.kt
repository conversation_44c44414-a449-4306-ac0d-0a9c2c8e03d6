/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendCacheManager.kt
 ** Description: 搜索推荐的缓存管理
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cache

import android.content.Context
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.RECOMMEND_CACHE_UPDATE_COMPLETE_URI
import com.oplus.gallery.business_lib.model.data.label.LabelSelector
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.foundation.taskscheduling.ext.launch
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_DATE
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_LABEL
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_LOCATION
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_MEMORIES
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_PERSON
import com.oplus.gallery.framework.abilities.search.query.QueryDispatcher
import com.oplus.gallery.framework.abilities.search.query.RecommendFestivalQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendLabelQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendLocationQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendMemoriesQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendMonthQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendPersonQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendRecentlyAddedQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendSuggestionCacheQuery
import com.oplus.gallery.framework.abilities.search.query.RecommendYearQuery
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.thread.ThreadPool
import com.oplus.gallery.standard_lib.util.io.IOUtils
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job

/**
 * 搜索推荐的缓存管理
 *
 * @property context 上下文对象
 */
internal class RecommendCacheManager(
    private val context: Context,
    private val geoCacheService: GeoCacheService,
    private val labelSelector: LabelSelector
) {

    internal var recommendCache: RecommendCache = RecommendCache()

    private var recommendCachingTask: Job? = null

    private var queryDispatcher: QueryDispatcher? = null

    /**
     * 更新缓存
     *
     * @param taskMask 任务的Mask类型
     */
    fun updateCache(taskMask: Int) {
        if ((recommendCachingTask != null) && (recommendCachingTask?.isCancelled == false)) {
            return
        }
        val startTime = System.currentTimeMillis()
        recommendCachingTask = AppScope.launch(
            Dispatchers.IO,
            CoroutineStart.DEFAULT,
            SearchRecommendTask(taskMask, queryDispatcher, recommendCache, geoCacheService, labelSelector)
        ) {
            recommendCachingTask = null
            context.contentResolver.notifyChange(RECOMMEND_CACHE_UPDATE_COMPLETE_URI, null)
            GLog.d(TAG, "[updateCache] SearchRecommendTask costTime:${GLog.getTime(startTime)}")
        }
    }

    /**
     * 清理地点缓存
     */
    fun clearLocationCache() {
        recommendCache.updateToLocationRecommend(null)
    }

    /**
     * 清理所有缓存
     */
    fun clearCache() {
        recommendCache.release()
    }

    private class SearchRecommendTask(
        private val taskMask: Int,
        private val queryDispatcher: QueryDispatcher?,
        private var recommendCache: RecommendCache,
        private val geoCacheService: GeoCacheService,
        private val labelSelector: LabelSelector
    ) : ThreadPool.Job<Void?> {

        private fun isJobAbort(jc: ThreadPool.JobContext?): Boolean {
            jc?.let {
                return it.isCancelled
            }
            return false
        }

        override fun run(jc: ThreadPool.JobContext?): Void? {
            if (isJobAbort(jc)) {
                return null
            }
            IOUtils.closeQuietly(queryDispatcher?.execute(RecommendSuggestionCacheQuery(recommendCache)))
            if (taskMask and TASK_DATE != 0 && !isJobAbort(jc)) {
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendRecentlyAddedQuery(true, recommendCache)))
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendYearQuery(true, recommendCache)))
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendMonthQuery(true, recommendCache)))
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendFestivalQuery(true, recommendCache)))
            }
            if ((taskMask and TASK_LOCATION != 0) && (!isJobAbort(jc))) {
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendLocationQuery(true, recommendCache, geoCacheService)))
            }
            if ((taskMask and TASK_PERSON != 0) && (!isJobAbort(jc))) {
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendPersonQuery(true, recommendCache)))
            }
            if ((taskMask and TASK_LABEL != 0) && (!isJobAbort(jc))) {
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendLabelQuery(true, recommendCache, labelSelector)))
            }
            if ((taskMask and TASK_MEMORIES != 0) && (!isJobAbort(jc))) {
                IOUtils.closeQuietly(queryDispatcher?.execute(RecommendMemoriesQuery(true, recommendCache)))
            }
            return null
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendCacheManager"
    }
}