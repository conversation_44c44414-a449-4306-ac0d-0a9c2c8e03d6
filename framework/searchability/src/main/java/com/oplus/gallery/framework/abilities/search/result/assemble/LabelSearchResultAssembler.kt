/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelSearchResultAssembler.kt
 ** Description:标签搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.search.result.assemble

import android.content.Context
import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.SpecialSplitter
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCache
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.Locale

/**
 * 标签搜索结果拼装器
 */
internal class LabelSearchResultAssembler(
    private val keywordCacheManager: KeywordCacheManager
) : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_LABEL

    /**
     * query label. include accurate query and fuzzy query. support multi keyword query.
     *
     * fix:4764777 by add param labelId
     * 点击智能场景的图集时传入Keyword,根据keyword查找到的 labelId 错误,以至于根据此labelId无法从scan_label表中找到记录
     * 没有记录,导致点击无法跳转
     * 比如英文下 票据收据图集的keyWord="Receipts" labelId=92,收据图集的keyWord="Receipts" labelId=187
     * 由于keyWord都一样,以至于根据sceneId=187从scan_label表中无法查找到相应的"票据收据图集",返回null
     */
    @Suppress("LongMethod")
    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val searchType = searchEntry.searchType
        val searchLabelId = searchEntry.searchLabelId
        GLog.d(TAG, "[assemble] singleKeyword:$singleKeyword, searchType:$searchType, searchLabelId:$searchLabelId")
        var startTime = System.currentTimeMillis()
        if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(ContextGetter.context) && (SearchType.TYPE_LABEL != searchType)) {
            startTime = System.currentTimeMillis()
            SearchTrackHelper.clear()
            val searchResult = LabelGraphSearchAbility.accurateSearch(
                ContextGetter.context, singleKeyword, true
            )
            searchResult.forEach { (key, value) ->
                singleQueryResult.appendQueryResult(value, key, SearchType.TYPE_LABEL)
            }
            GLog.d(TAG, "[assemble] accurateSearchByTagGraph costTime:${GLog.getTime(startTime)}")
        } else {
            /*
            * 字典解析太过耗时，高端机型上也需要500ms左右，不加载就不查询了智能场景了，后续需优化
            * 影响：相册被杀死，三方通过provider拉起相册，第一次可能搜索不到标签结果，bugid:4611196
            */
            if (LabelDictionary.isDictionaryLoaded().not()) {
                GLog.d(TAG, "[assemble], label dictionary not loaded")
                return singleQueryResult
            }

            val keywordCache = keywordCacheManager.keywordCache
            if (keywordCache.labelIdToNameMap == null) {
                keywordCacheManager.updateLabelIdToNameCache()
            }
            if (keywordCache.mediaIdToLabelIdsMap == null) {
                keywordCacheManager.updateMediaIdToLabelIdsCache()
            }
            // 指定类型搜索
            if (SearchType.TYPE_LABEL == searchType) {
                when {
                    searchLabelId != LabelSearchEngine.INVALID_ID -> queryMediaIdEntryByLabelId(singleQueryResult, searchLabelId, singleKeyword)

                    LocaleUtils.isEnglish(ContextGetter.context)
                            && LabelGraphSearchAbility.checkAvailabilityForLabelGraph(ContextGetter.context) -> {
                        // 支持英文知识图谱，直接调用accurateSearch()，然后过滤。英文db中存在标签名一对多标签id的情况，getLabelIdByLabelName()不适用英文图谱
                        val searchResult = LabelGraphSearchAbility.accurateSearch(
                            ContextGetter.context, singleKeyword, true
                        )
                        searchResult.forEach { (key, value) ->
                            if (key.equals(singleKeyword, true)) {
                                singleQueryResult.appendQueryResult(value, key, SearchType.TYPE_LABEL)
                            }
                        }
                    }

                    else -> {
                        val tmpLabelId = getLabelIdByLabelName(singleKeyword)
                        if (tmpLabelId != LabelSearchEngine.INVALID_ID) {
                            GLog.d(TAG, "[assemble] labelId:$tmpLabelId")
                            queryMediaIdEntryByLabelId(singleQueryResult, tmpLabelId, singleKeyword)
                        } else {
                            GLog.d(TAG, "[assemble] getLabelIdByName return -1")
                        }
                    }
                }
            } else {
                val singleMatchedLabelIdSet: MutableSet<Int> = mutableSetOf()
                // 若不是指定标签查询，则需要先查出所有的标签信息，放到Map中保存
                var searchResult: LabelSearchEngine.Result? = null
                keywordCache.mediaIdToLabelIdsMap?.let {
                    searchResult = LabelSearchEngine.getInstance().process(
                        singleKeyword, keywordCache.mediaIdToLabelIdsMap
                    )
                    searchResult?.let { result ->
                        val fullGroupLabels = result.fullMatchMaps
                        fullGroupLabels?.let {
                            // 查询并构建标签结果;包含父标签和子标签对应的媒体列表
                            buildLabelResult(
                                it, result, ContextGetter.context, singleMatchedLabelIdSet, singleQueryResult
                            )
                        }
                    }
                }
                keywordCache.labelIdToNameMap?.let {
                    val completionLabel: MutableMap<Int, String> = completionLabel(keywordCache, singleMatchedLabelIdSet, singleKeyword)
                    val iterator: Iterator<Map.Entry<Int, String>> = completionLabel.entries.iterator()
                    var labelMediaIds: List<MediaIdEntry?>? = null
                    while (iterator.hasNext()) {
                        val (key, value) = iterator.next()
                        labelMediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(ContextGetter.context, key)
                        if (labelMediaIds.isNullOrEmpty().not()) {
                            singleQueryResult.appendQueryResult(labelMediaIds, value, SearchType.TYPE_LABEL)
                        }
                    }
                }
            }
        }
        GLog.d(TAG, "[assemble] costTime:${GLog.getTime(startTime)}")
        return singleQueryResult
    }

    /**
     * 依据标签ID查询所有对应的媒体列表，并构建结果
     * @param singleQueryResult 待构建结果
     * @param searchLabelId 智能场景labelId
     * @param singleKeyword 搜索关键字
     */
    private fun queryMediaIdEntryByLabelId(
        singleQueryResult: SingleQueryResult,
        searchLabelId: Int,
        singleKeyword: String
    ) {
        val mediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(ContextGetter.context, searchLabelId)
        if (mediaIds.isNullOrEmpty().not()) {
            singleQueryResult.appendQueryResult(mediaIds, singleKeyword, SearchType.TYPE_LABEL)
        }
    }


    private fun completionLabel(
        keywordCache: KeywordCache?,
        matchedLabelIdSet: Set<Int>,
        singleKeyword: String
    ): HashMap<Int, String> {
        val classifier = SearchClassifier(ContextGetter.context)
        val completionLabel = HashMap<Int, String>()
        keywordCache?.labelIdToNameMap?.forEach { (labelId, labelNames) ->
            if (!matchedLabelIdSet.contains(labelId)
                && !labelNames.isNullOrEmpty()
                && labelNames[0].length != singleKeyword.length
                && classifier.contains(labelNames[0].lowercase(Locale.getDefault()), singleKeyword)
            ) {
                completionLabel[labelId] = labelNames[0]
            }
        }
        return completionLabel
    }

    private fun getLabelIdByLabelName(labelName: String): Int {
        var tmpLabelId = LabelSearchEngine.INVALID_ID
        if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(ContextGetter.context)) {
            tmpLabelId = LabelGraphSearchAbility.queryLabelBeanByLabelName(labelName)
            if (tmpLabelId == LabelSearchEngine.INVALID_ID) {
                // 知识图谱补全
                tmpLabelId = LabelGraphSearchAbility.queryLabelId(labelName)
            }
        } else {
            if (tmpLabelId == LabelSearchEngine.INVALID_ID) {
                tmpLabelId = LabelSearchEngine.getInstance().getLabelIdByName(labelName)
            }
            if (tmpLabelId == LabelSearchEngine.INVALID_ID) {
                tmpLabelId = LabelSearchEngine.getInstance().getLabelSynonymId(labelName)
            }
        }
        return tmpLabelId
    }

    private fun buildLabelResult(
        fullGroupLabels: java.util.HashMap<String, List<String>>,
        searchResult: LabelSearchEngine.Result,
        context: Context,
        singleMatchedLabelIdSet: MutableSet<Int>,
        result: SingleQueryResult
    ) {
        var existFullMatch = false
        val iterator: Iterator<Map.Entry<String, List<String>>> = fullGroupLabels.entries.iterator()
        var allChildLabelMediaIds: List<MediaIdEntry>? = null
        val childIds = searchResult.childIds
        if (childIds != null) {
            val childIdsArr = childIds.toTypedArray()
            allChildLabelMediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabels(context, childIdsArr)
        }
        existFullMatch = buildParentLabelResult(
            iterator, context, singleMatchedLabelIdSet, allChildLabelMediaIds, result
        )
        if (existFullMatch && childIds != null) {
            buildChildLabelResult(childIds, context, singleMatchedLabelIdSet, result)
        }
    }

    private fun buildParentLabelResult(
        iterator: Iterator<Map.Entry<String, List<String>>>,
        context: Context,
        singleMatchedLabelIdSet: MutableSet<Int>,
        allChildLabelMediaIds: List<MediaIdEntry>?,
        result: SingleQueryResult
    ): Boolean {
        var existFullMatch = false
        var singleMatchedLabelId = LabelSearchEngine.INVALID_ID
        while (iterator.hasNext()) {
            val (key, labelIdsList) = iterator.next()
            val labelMediaIds: MutableSet<MediaIdEntry> = java.util.HashSet()
            for (labelIds in labelIdsList) {
                val labelIdsArr = SpecialSplitter.fastSplitInt(labelIds, LabelSearchEngine.ITEM_SPLIT_CHAR)
                if (labelIdsArr != null) {
                    val labelsArr = labelIdsArr.toTypedArray()
                    val queryMediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabels(context, labelsArr)
                    if (queryMediaIds.isNullOrEmpty().not()) {
                        if (labelIdsList.size == 1 && labelIdsArr.size == 1) {
                            singleMatchedLabelId = labelIdsArr[0]
                        }
                        labelMediaIds.addAll(queryMediaIds)
                    }
                }
            }
            if (labelMediaIds.isNotEmpty()) {
                val labelMediaList: MutableList<MediaIdEntry> = ArrayList()
                labelMediaList.addAll(labelMediaIds)
                if (singleMatchedLabelId != LabelSearchEngine.INVALID_ID) {
                    singleMatchedLabelIdSet.add(singleMatchedLabelId)
                }
                existFullMatch = true
                // label mediaIds should include all child labels mediaIds
                if (allChildLabelMediaIds != null && allChildLabelMediaIds.isNotEmpty()) {
                    val tmpList: MutableList<MediaIdEntry> = ArrayList(allChildLabelMediaIds)
                    tmpList.removeAll(labelMediaList)
                    labelMediaList.addAll(tmpList)
                }
                result.appendQueryResult(labelMediaList, key, SearchType.TYPE_LABEL)
            }
        }
        return existFullMatch
    }

    private fun buildChildLabelResult(
        childIds: List<Int>,
        context: Context,
        singleMatchedLabelIdSet: MutableSet<Int>,
        result: SingleQueryResult
    ) {
        for (childId in childIds) {
            val childLabelMediaIds = SearchDBHelper.queryFilterMediaIdEntryByLabel(context, childId)
            if (childLabelMediaIds.isNullOrEmpty().not()) {
                val childName = LabelDictionary.getLabelName(childId)
                if (!TextUtils.isEmpty(childName)) {
                    singleMatchedLabelIdSet.add(childId)
                    result.appendQueryResult(childLabelMediaIds, childName, SearchType.TYPE_CHILD_LABEL)
                }
            }
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}LabelSearchResultAssembler"
    }
}