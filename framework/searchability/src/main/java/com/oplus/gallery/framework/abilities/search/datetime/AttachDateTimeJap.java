/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AttachDateTimeJap.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2016/11/25
 ** Author:yanchao.<PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yanchao.<PERSON>@Apps.Gallery3D      2016/11/25     1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.search.datetime;

import android.content.Context;
import android.content.res.Resources;

import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateInfo;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateRange;
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime;
import com.oplus.gallery.framework.datatmp.R;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AttachDateTimeJap {
    private static final String TAG = "AttachDateTimeJap";
    private static DateInfo sDicJapDay;
    private static DateInfo sDicJapMonth;
    private static DateInfo sDicJapYear;


    public AttachDateTimeJap(Context context) {
        Resources resources = context.getResources();
        if (sDicJapDay == null) {
            sDicJapDay = new DateInfo(resources, R.array.model_cn_day);
        }
        if (sDicJapMonth == null) {
            sDicJapMonth = new DateInfo(resources, R.array.model_cn_month);
        }
        if (sDicJapYear == null) {
            sDicJapYear = new DateInfo(resources, R.array.model_cn_year);
        }
    }

    public ShootDateTime getShootDateTime(String keyword) {
        DateRange range = parseNumericalDateRangeForJapan(keyword);
        if (range == null) {
            range = parseSpecialFormatDateRange(keyword);
        }
        ShootDateTime shootDateTime = null;
        if (range != null) {
            shootDateTime = new ShootDateTime();
            shootDateTime.dateRange = range;
            shootDateTime.shootKeyword = keyword;
            String condition = range.condition;
            String[] fmtArr = (condition != null) ? condition.split(",") : null;
            if ((fmtArr != null) && (fmtArr.length >= 2)) {
                shootDateTime.format = fmtArr[0];
                shootDateTime.conditions = (fmtArr.length > 2) ? condition.substring(condition.indexOf(",") + 1) : fmtArr[1];
            }
        }
        return shootDateTime;
    }

    /**
     * <p>
     * 2018年1月22日
     *
     * @param keyword
     * @return
     */
    private DateRange parseNumericalDateRangeForJapan(String keyword) {
        Matcher matcher = SearchCommonUtils.JAPAN_DATE_REGEX_PATTERN.matcher(keyword);
        if (!matcher.matches()) {
            return null;
        }
        // Each date separator word could at most appear once in keyword
        int yearIndex = -1;
        int monthIndex = -1;
        int dayIndex = -1;
        do {
            int index = sDicJapYear.indexOfKeyword(keyword, (yearIndex < 0) ? 0 : yearIndex + 1);
            if (yearIndex != -1) {
                if (index != -1) {
                    return null;
                } else {
                    break;
                }
            } else {
                if (index != -1) {
                    yearIndex = index;
                } else {
                    break;
                }
            }
        } while (true);
        do {
            int index = sDicJapMonth.indexOfKeyword(keyword, (monthIndex < 0) ? 0 : monthIndex + 1);
            if (monthIndex != -1) {
                if (index != -1) {
                    return null;
                } else {
                    break;
                }
            } else {
                if (index != -1) {
                    monthIndex = index;
                } else {
                    break;
                }
            }
        } while (true);
        do {
            int index = sDicJapDay.indexOfKeyword(keyword, (dayIndex < 0) ? 0 : dayIndex + 1);
            if (dayIndex != -1) {
                if (index != -1) {
                    return null;
                } else {
                    break;
                }
            } else {
                if (index != -1) {
                    dayIndex = index;
                } else {
                    break;
                }
            }
        } while (true);

        int start = 0;
        int year = -1;
        int month = -1;
        int day = -1;

        // Year, if year == 0, that means 2000 A.D.
        if (yearIndex > 0) {
            start = findFloor(yearIndex, -1, monthIndex, dayIndex);
            if (start < yearIndex) {
                year = Integer.parseInt(keyword.substring(start + 1, yearIndex));
                /*
                Don't correct this input error, just return null to make
                this input invalid.
                year = year < 1970 ? -1 : year;
                */
                if (!DateTimeSelector.isValidYear(year)) {
                    return null;
                }
            }
        }

        // Month
        if (monthIndex > 0) {
            //start = Math.max(Math.min(yearIndex, monthIndex), Math.min(dayIndex, monthIndex));
            start = findFloor(monthIndex, -1, yearIndex, dayIndex);
            if (start < monthIndex) {
                month = Integer.parseInt(keyword.substring(start + 1, monthIndex));
                /*
                Don't correct this input error, just return null to make
                this input invalid.
                month = month < 1 ? -1 : month > 12 ? -1 : month;
                */
                if (!DateTimeSelector.isValidMonth(month)) {
                    return null;
                }
            }
        }

        // Day of month
        if (dayIndex > 0) {
            start = findFloor(dayIndex, -1, yearIndex, monthIndex);
            if (start < dayIndex) {
                day = Integer.parseInt(keyword.substring(start + 1, dayIndex));
                /*
                Don't correct this input error, just return null to make
                this input invalid.
                day = day < 1 ? -1 : day > maxDay ? -1 : day;
                */
                if (!DateTimeSelector.isValidDay(year, month, day)) {
                    return null;
                }
            }
        }

        if ((year > 0) || (month > 0) || (day > 0)) {
            // YYYY-mm-dd
            DateRange range = new DateRange();
            StringBuffer preSuffixBuffer = new StringBuffer();
            StringBuffer postSuffixBuffer = new StringBuffer();
            if (year > -1) {
                preSuffixBuffer.append("-%Y");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%04d", year));
            }
            if (month > 0) {
                preSuffixBuffer.append("-%m");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", month));
            }
            if (day > 0) {
                preSuffixBuffer.append("-%d");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", day));
            }

            if (preSuffixBuffer.length() > 1) {
                preSuffixBuffer.delete(0, 1);
            }
            if (postSuffixBuffer.length() > 1) {
                postSuffixBuffer.delete(0, 1);
            }
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

    private int findFloor(int ref, int a, int b, int c) {
        int result = ref;
        result = (a < ref) ? a : ref;
        result = ((b < ref) && (b > result)) ? b : result;
        result = ((c < ref) && (c > result)) ? c : result;
        return result;
    }

    /**
     *  parse special date format
     *
     * @param keyword
     * @return
     */
    private DateRange parseSpecialFormatDateRange(String keyword) {
        boolean existDifferentConnector = (keyword.contains("-") && keyword.contains("/"))
                || (keyword.contains("-") && keyword.contains("."))
                || (keyword.contains("/") && keyword.contains("."));
        if (existDifferentConnector) {
            GLog.d(TAG, "parseSpecialFormatDateRange, exist different connector!");
            return null;
        }
        keyword = keyword.replaceAll("[./]", "-");
        if (keyword.contains("-")) {
            return parseSpecialFormatYYMMDD(keyword);
        }
        return null;
    }

    /**
     * parse YY-MM-DD
     *
     * @param keyword
     * @return
     */
    private DateRange parseSpecialFormatYYMMDD(String keyword) {
        Pattern patternYearMonthDay = Pattern.compile("^\\d{4}-\\d{1,2}-\\d{1,2}$");
        Matcher matcher = patternYearMonthDay.matcher(keyword);
        if (matcher.matches()) {
            try {
                String[] keywords = keyword.split("-");
                int year = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidYear(year)) {
                    GLog.d(TAG, "parseSpecialFormatYYMMDD, invalid year " + year);
                    return null;
                }
                int month = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    GLog.d(TAG, "parseSpecialFormatYYMMDD, invalid month " + month);
                    return null;
                }
                int day = Integer.parseInt(keywords[2]);
                if (!DateTimeSelector.isValidDay(year, month, day)) {
                    GLog.d(TAG, "parseSpecialFormatYYMMDD, invalid day " + day);
                    return null;
                }

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String postSuffix = dateFormat.format(dateFormat.parse(keyword));
                DateRange range = new DateRange();
                range.condition = "%Y-%m-%d, '" + postSuffix + "'";
                return range;
            } catch (Exception e) {
                GLog.w(TAG, "parseSpecialFormatYYMMDD, Exception = " + e);
            }
        }
        return null;
    }

}
