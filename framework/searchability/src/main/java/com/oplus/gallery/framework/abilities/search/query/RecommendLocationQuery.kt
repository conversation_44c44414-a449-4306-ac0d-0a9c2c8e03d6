/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendLocationQuery.kt
 ** Description: 推荐地点查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.content.Context
import android.database.Cursor
import android.database.MatrixCursor
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.model.data.base.entry.LocationEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.business_lib.model.data.location.utils.LocationHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.search.SearchAbilityImpl
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 推荐地点查询
 *
 * @property forceQuery 是否强制从数据库查询
 * @property recommendCache 推荐缓存
 * @property geoCacheService 地点获取服务
 */
internal class RecommendLocationQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache,
    private val geoCacheService: GeoCacheService
) : Query {
    override fun query(): Cursor? {
        return if (!forceQuery && recommendCache.hasLocationRecommend()) {
            recommendCache.obtainLocationRecommend()
        } else {
            GTrace.traceBegin("RecommendLocationQuery")
            val startTime = System.currentTimeMillis()
            val cursor = MatrixCursor(COLUMN_RECOMMEND_LOCATION)
            val locationEntries: MutableList<LocationEntry> = ArrayList()
            var cityEntries: List<LocationEntry>? = null
            val isRegionCN = SearchAbilityImpl.configAbility?.getBooleanConfig(IS_REGION_CN, false) ?: false
            cityEntries = if (isRegionCN) {
                getCustomizedEntries(ContextGetter.context, geoCacheService.allLocationByCity)
            } else {
                // 外销区域展示所有城市
                geoCacheService.allLocationByCity
            }
            if (cityEntries != null && cityEntries.isNotEmpty()) {
                locationEntries.addAll(cityEntries)
            }
            locationEntries.sort()
            for (entry in locationEntries) {
                val coverMediaEntry = entry.coverMediaEntry ?: continue
                cursor.addRow(
                    arrayOf<Any?>(
                        entry.city, coverMediaEntry.mediaId, entry.extraNames,
                        entry.count, coverMediaEntry.mediaType, coverMediaEntry.galleryId
                    )
                )
            }
            recommendCache.updateToLocationRecommend(cursor)
            cursor.moveToPosition(-1)
            val end = System.currentTimeMillis()
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] costTime:${GLog.getTime(startTime)}")
            }
            GTrace.traceEnd()
            cursor
        }
    }

    /**
     * 合并特殊的城市到省，如台湾的所有城市需要聚合为台湾；澳门的在内销英文下，city字段不是Macau，需要转换
     *
     * @param cityEntries 城市列表
     * @return 处理后的城市列表
     */
    @VisibleForTesting
    fun getCustomizedEntries(context: Context?, cityEntries: List<LocationEntry>?): List<LocationEntry>? {
        if (cityEntries == null || cityEntries.isEmpty()) {
            return null
        }
        val entryMap: MutableMap<String?, LocationEntry> = HashMap()
        val extraNameMap: MutableMap<String?, MutableSet<String?>> = HashMap()
        for (cityEntry in cityEntries) {
            if (LocationHelper.isTaiwan(context, cityEntry.province) || LocationHelper.isMacau(context, cityEntry.province)) {
                extraNameMap.computeIfAbsent(cityEntry.province) { HashSet() }.add(cityEntry.city)
                entryMap.computeIfAbsent(cityEntry.province) {
                    val newEntry = LocationEntry()
                    newEntry.country = cityEntry.country
                    newEntry.coverMediaEntry = cityEntry.coverMediaEntry
                    newEntry.city = cityEntry.province
                    newEntry
                }.count += cityEntry.count
            } else {
                entryMap[cityEntry.city] = cityEntry
            }
        }
        extraNameMap.forEach { (key, value) ->
            val locationEntry = entryMap[key]
            locationEntry?.let { entry ->
                entry.extraNames = convertListToString(value)
            }
        }
        return ArrayList(entryMap.values)
    }

    private fun convertListToString(stringList: Collection<String?>?): String? {
        if (stringList == null || stringList.isEmpty()) {
            return null
        }
        val sb = StringBuilder()
        for (str in stringList) {
            sb.append(str)
            sb.append(TextUtil.SPLIT_COMMA_SEPARATOR)
        }
        sb.deleteCharAt(sb.length - 1)
        return sb.toString()
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendLocationQuery"

        /**
         * 推荐地点数据列
         */
        private val COLUMN_RECOMMEND_LOCATION = arrayOf(
            SearchSuggestionProviderUtil.COLUMNNAME,
            SearchSuggestionProviderUtil.COLUMN_ID,
            SearchSuggestionProviderUtil.COLUMN_EXTRANAMES,
            SearchSuggestionProviderUtil.COLUMN_COUNT,
            SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_GALLERY_ID
        )
    }
}