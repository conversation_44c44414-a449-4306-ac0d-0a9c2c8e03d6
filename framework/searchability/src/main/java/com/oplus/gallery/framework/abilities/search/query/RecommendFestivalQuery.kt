/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendFestivalQuery.kt
 ** Description: 节日推荐查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils

/**
 * 节日推荐查询
 *
 * @property forceQuery 是否强制查询
 * @property recommendCache 推荐缓存
 */
internal class RecommendFestivalQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache
) : Query {
    override fun query(): Cursor? {
        return try {
            GTrace.traceBegin("RecommendFestivalQuery")
            // 有缓存时直接从缓存中获取
            if (!forceQuery && recommendCache.hasFestivalRecommend()) {
                recommendCache.obtainFestivalRecommend()
            } else {
                val startTime = System.currentTimeMillis()
                val festivalEntries = DBSearchUtils.queryFestivalsFromLocalMedia()
                if (GProperty.LOG_GALLERY_OPEN) {
                    GLog.v(TAG, "[query] queryFestivalsFromLocalMedia costTime:${GLog.getTime(startTime)}")
                }
                val cursor = MatrixCursor(COLUMN_RECOMMEND_DATE)
                if (festivalEntries == null || festivalEntries.isEmpty()) {
                    GLog.d(TAG, "[query] festivalEntries isEmpty!")
                    return cursor
                }
                for (festivalEntry in festivalEntries) {
                    val mediaIdEntry = festivalEntry.coverMediaEntry
                    mediaIdEntry?.let {
                        cursor.addRow(
                            arrayOf<Any?>(
                                festivalEntry.festivalName, it.mediaId,
                                festivalEntry.count, it.mediaType, it.galleryId
                            )
                        )
                    }
                }
                // 更新缓存
                recommendCache.updateToFestivalRecommend(cursor)
                cursor.moveToPosition(-1)
                if (GProperty.LOG_GALLERY_OPEN) {
                    GLog.v(TAG, "[query] costTime:${GLog.getTime(startTime)}")
                }
                cursor
            }
        } finally {
            GTrace.traceEnd()
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendFestivalQuery"

        /**
         * 推荐日期时间数据列
         */
        internal val COLUMN_RECOMMEND_DATE = arrayOf(
            SearchSuggestionProviderUtil.COLUMNNAME,
            SearchSuggestionProviderUtil.COLUMN_ID,
            SearchSuggestionProviderUtil.COLUMN_COUNT,
            SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_GALLERY_ID
        )
    }
}