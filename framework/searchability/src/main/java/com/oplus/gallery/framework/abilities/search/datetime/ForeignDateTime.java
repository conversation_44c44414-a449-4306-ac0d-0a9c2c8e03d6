/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ForeignDateTime.java
 * * Description: for English dateTime.
 * * Version: 1.0
 * * Date : 2017/11/30
 * * Author: yanchao.Chen@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  yanchao.Chen@Apps.Gallery3D       2017/11/30    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/
package com.oplus.gallery.framework.abilities.search.datetime;

import android.content.Context;
import android.content.res.Resources;

import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.framework.abilities.search.cache.KeywordCache;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateInfo;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateRange;
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime;
import com.oplus.gallery.framework.datatmp.R;
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ForeignDateTime {
    private static final String TAG = "ForeignDateTime";
    private static final long DAY_MS = TimeUtils.TIME_1_DAY_IN_MS;
    private static final long LEAP_YEAR_MS = DAY_MS * 366L;
    private static final long YEAR_MS = DAY_MS * 365L;

    private static DateInfo sDicAbsJanuary;
    private static DateInfo sDicAbsFebruary;
    private static DateInfo sDicAbsMarch;
    private static DateInfo sDicAbsApril;
    private static DateInfo sDicAbsMay;
    private static DateInfo sDicAbsJune;
    private static DateInfo sDicAbsJuly;
    private static DateInfo sDicAbsAugust;
    private static DateInfo sDicAbsSeptember;
    private static DateInfo sDicAbsOctober;
    private static DateInfo sDicAbsNovember;
    private static DateInfo sDicAbsDecember;

    private static DateInfo sDicDayNumber1;
    private static DateInfo sDicDayNumber2;
    private static DateInfo sDicDayNumber3;
    private static DateInfo sDicDayNumber4;
    private static DateInfo sDicDayNumber5;
    private static DateInfo sDicDayNumber6;
    private static DateInfo sDicDayNumber7;
    private static DateInfo sDicDayNumber8;
    private static DateInfo sDicDayNumber9;
    private static DateInfo sDicDayNumber10;
    private static DateInfo sDicDayNumber11;
    private static DateInfo sDicDayNumber12;
    private static DateInfo sDicDayNumber13;
    private static DateInfo sDicDayNumber14;
    private static DateInfo sDicDayNumber15;
    private static DateInfo sDicDayNumber16;
    private static DateInfo sDicDayNumber17;
    private static DateInfo sDicDayNumber18;
    private static DateInfo sDicDayNumber19;
    private static DateInfo sDicDayNumber20;
    private static DateInfo sDicDayNumber21;
    private static DateInfo sDicDayNumber22;
    private static DateInfo sDicDayNumber23;
    private static DateInfo sDicDayNumber24;
    private static DateInfo sDicDayNumber25;
    private static DateInfo sDicDayNumber26;
    private static DateInfo sDicDayNumber27;
    private static DateInfo sDicDayNumber28;
    private static DateInfo sDicDayNumber29;
    private static DateInfo sDicDayNumber30;
    private static DateInfo sDicDayNumber31;
    private static DateInfo sDicRelPreviousYear;
    private static DateInfo sDicRelLastYear;
    private static DateInfo sDicRelThisYear;
    private volatile static DateInfo sDicRelThisDay;
    private static DateInfo sDicRelRecent;

    private static DateInfo sDicRelMorningSixToNine;
    private static DateInfo sDicRelMorningNineToTwelve;
    private static DateInfo sDicRelNoon;
    private static DateInfo sDicRelAfternoon;
    private static DateInfo sDicRelNight;
    private static DateInfo sDicRelAtNight;
    private static DateInfo sDicRelEarlyMorning;

    private static DateInfo[] sDicVocabularyArrays;
    private static DateInfo[] sDicAbsMonthArrays;
    private static DateInfo[] sDicAbsDayArrays;
    private static DateInfo[] sDicRelArrays;

    private KeywordCache mKeywordCache;
    private Context mContext;

    public void setKeywordCache(KeywordCache keywordCache) {
        mKeywordCache = keywordCache;
    }

    public ForeignDateTime(Context context) {
        Resources resources = context.getResources();
        if (sDicAbsJanuary == null) {
            sDicAbsJanuary = new DateInfo(resources, R.array.model_en_january);
        }
        if (sDicAbsFebruary == null) {
            sDicAbsFebruary = new DateInfo(resources, R.array.model_en_february);
        }
        if (sDicAbsMarch == null) {
            sDicAbsMarch = new DateInfo(resources, R.array.model_en_march);
        }
        if (sDicAbsApril == null) {
            sDicAbsApril = new DateInfo(resources, R.array.model_en_april);
        }
        if (sDicAbsMay == null) {
            sDicAbsMay = new DateInfo(resources, R.array.model_en_may);
        }
        if (sDicAbsJune == null) {
            sDicAbsJune = new DateInfo(resources, R.array.model_en_june);
        }
        if (sDicAbsJuly == null) {
            sDicAbsJuly = new DateInfo(resources, R.array.model_en_july);
        }
        if (sDicAbsAugust == null) {
            sDicAbsAugust = new DateInfo(resources, R.array.model_en_august);
        }
        if (sDicAbsSeptember == null) {
            sDicAbsSeptember = new DateInfo(resources, R.array.model_en_september);
        }
        if (sDicAbsOctober == null) {
            sDicAbsOctober = new DateInfo(resources, R.array.model_en_october);
        }
        if (sDicAbsNovember == null) {
            sDicAbsNovember = new DateInfo(resources, R.array.model_en_november);
        }
        if (sDicAbsDecember == null) {
            sDicAbsDecember = new DateInfo(resources, R.array.model_en_december);
        }
        if (sDicDayNumber1 == null) {
            sDicDayNumber1 = new DateInfo(resources, R.array.model_en_day_1);
        }
        if (sDicDayNumber2 == null) {
            sDicDayNumber2 = new DateInfo(resources, R.array.model_en_day_2);
        }
        if (sDicDayNumber3 == null) {
            sDicDayNumber3 = new DateInfo(resources, R.array.model_en_day_3);
        }
        if (sDicDayNumber4 == null) {
            sDicDayNumber4 = new DateInfo(resources, R.array.model_en_day_4);
        }
        if (sDicDayNumber5 == null) {
            sDicDayNumber5 = new DateInfo(resources, R.array.model_en_day_5);
        }
        if (sDicDayNumber6 == null) {
            sDicDayNumber6 = new DateInfo(resources, R.array.model_en_day_6);
        }
        if (sDicDayNumber7 == null) {
            sDicDayNumber7 = new DateInfo(resources, R.array.model_en_day_7);
        }
        if (sDicDayNumber8 == null) {
            sDicDayNumber8 = new DateInfo(resources, R.array.model_en_day_8);
        }
        if (sDicDayNumber9 == null) {
            sDicDayNumber9 = new DateInfo(resources, R.array.model_en_day_9);
        }
        if (sDicDayNumber10 == null) {
            sDicDayNumber10 = new DateInfo(resources, R.array.model_en_day_10);
        }
        if (sDicDayNumber11 == null) {
            sDicDayNumber11 = new DateInfo(resources, R.array.model_en_day_11);
        }
        if (sDicDayNumber12 == null) {
            sDicDayNumber12 = new DateInfo(resources, R.array.model_en_day_12);
        }
        if (sDicDayNumber13 == null) {
            sDicDayNumber13 = new DateInfo(resources, R.array.model_en_day_13);
        }
        if (sDicDayNumber14 == null) {
            sDicDayNumber14 = new DateInfo(resources, R.array.model_en_day_14);
        }
        if (sDicDayNumber15 == null) {
            sDicDayNumber15 = new DateInfo(resources, R.array.model_en_day_15);
        }
        if (sDicDayNumber16 == null) {
            sDicDayNumber16 = new DateInfo(resources, R.array.model_en_day_16);
        }
        if (sDicDayNumber17 == null) {
            sDicDayNumber17 = new DateInfo(resources, R.array.model_en_day_17);
        }
        if (sDicDayNumber18 == null) {
            sDicDayNumber18 = new DateInfo(resources, R.array.model_en_day_18);
        }
        if (sDicDayNumber19 == null) {
            sDicDayNumber19 = new DateInfo(resources, R.array.model_en_day_19);
        }
        if (sDicDayNumber20 == null) {
            sDicDayNumber20 = new DateInfo(resources, R.array.model_en_day_20);
        }
        if (sDicDayNumber21 == null) {
            sDicDayNumber21 = new DateInfo(resources, R.array.model_en_day_21);
        }
        if (sDicDayNumber22 == null) {
            sDicDayNumber22 = new DateInfo(resources, R.array.model_en_day_22);
        }
        if (sDicDayNumber23 == null) {
            sDicDayNumber23 = new DateInfo(resources, R.array.model_en_day_23);
        }
        if (sDicDayNumber24 == null) {
            sDicDayNumber24 = new DateInfo(resources, R.array.model_en_day_24);
        }
        if (sDicDayNumber25 == null) {
            sDicDayNumber25 = new DateInfo(resources, R.array.model_en_day_25);
        }
        if (sDicDayNumber26 == null) {
            sDicDayNumber26 = new DateInfo(resources, R.array.model_en_day_26);
        }
        if (sDicDayNumber27 == null) {
            sDicDayNumber27 = new DateInfo(resources, R.array.model_en_day_27);
        }
        if (sDicDayNumber28 == null) {
            sDicDayNumber28 = new DateInfo(resources, R.array.model_en_day_28);
        }
        if (sDicDayNumber29 == null) {
            sDicDayNumber29 = new DateInfo(resources, R.array.model_en_day_29);
        }
        if (sDicDayNumber30 == null) {
            sDicDayNumber30 = new DateInfo(resources, R.array.model_en_day_30);
        }
        if (sDicDayNumber31 == null) {
            sDicDayNumber31 = new DateInfo(resources, R.array.model_en_day_31);
        }
        if (sDicRelThisYear == null) {
            sDicRelThisYear = new DateInfo(resources, R.array.model_this_year);
        }
        if (sDicRelLastYear == null) {
            sDicRelLastYear = new DateInfo(resources, R.array.model_last_year);
        }
        if (sDicRelPreviousYear == null) {
            sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        }
        if (sDicRelThisDay == null) {
            sDicRelThisDay = new DateInfo(resources, R.array.model_this_day);
        }
        if (sDicRelMorningSixToNine == null) {
            sDicRelMorningSixToNine = new DateInfo(resources, R.array.model_morning_six_to_nine);
        }
        if (sDicRelMorningNineToTwelve == null) {
            sDicRelMorningNineToTwelve = new DateInfo(resources, R.array.model_morning_nine_to_twelve);
        }
        if (sDicRelPreviousYear == null) {
            sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        }
        if (sDicRelNoon == null) {
            sDicRelNoon = new DateInfo(resources, R.array.model_noon);
        }
        if (sDicRelAfternoon == null) {
            sDicRelAfternoon = new DateInfo(resources, R.array.model_afternoon);
        }
        if (sDicRelNight == null) {
            sDicRelNight = new DateInfo(resources, R.array.model_night);
        }
        if (sDicRelAtNight == null) {
            sDicRelAtNight = new DateInfo(resources, R.array.model_at_night);
        }
        if (sDicRelEarlyMorning == null) {
            sDicRelEarlyMorning = new DateInfo(resources, R.array.model_early_morning);
        }
        if (sDicRelRecent == null) {
            sDicRelRecent = new DateInfo(resources, R.array.model_recent);
        }
        if (sDicVocabularyArrays == null) {
            sDicVocabularyArrays = new DateInfo[]{
                    sDicRelMorningSixToNine,
                    sDicRelMorningNineToTwelve,
                    sDicRelNoon,
                    sDicRelAfternoon,
                    sDicRelNight,
                    sDicRelAtNight,
                    sDicRelEarlyMorning
            };
        }
        if (sDicAbsMonthArrays == null) {
            sDicAbsMonthArrays = new DateInfo[]{
                    sDicAbsJanuary,
                    sDicAbsFebruary,
                    sDicAbsMarch,
                    sDicAbsApril,
                    sDicAbsMay,
                    sDicAbsJune,
                    sDicAbsJuly,
                    sDicAbsAugust,
                    sDicAbsSeptember,
                    sDicAbsOctober,
                    sDicAbsNovember,
                    sDicAbsDecember
            };
        }

        if (sDicAbsDayArrays == null) {
            sDicAbsDayArrays = new DateInfo[]{
                    sDicDayNumber1,
                    sDicDayNumber2,
                    sDicDayNumber3,
                    sDicDayNumber4,
                    sDicDayNumber5,
                    sDicDayNumber6,
                    sDicDayNumber7,
                    sDicDayNumber8,
                    sDicDayNumber9,
                    sDicDayNumber10,
                    sDicDayNumber11,
                    sDicDayNumber12,
                    sDicDayNumber13,
                    sDicDayNumber14,
                    sDicDayNumber15,
                    sDicDayNumber16,
                    sDicDayNumber17,
                    sDicDayNumber18,
                    sDicDayNumber19,
                    sDicDayNumber20,
                    sDicDayNumber21,
                    sDicDayNumber22,
                    sDicDayNumber23,
                    sDicDayNumber24,
                    sDicDayNumber25,
                    sDicDayNumber26,
                    sDicDayNumber27,
                    sDicDayNumber28,
                    sDicDayNumber29,
                    sDicDayNumber30,
                    sDicDayNumber31
            };
        }
        if (sDicRelArrays == null) {
            sDicRelArrays = new DateInfo[]{
                    sDicRelPreviousYear,
                    sDicRelLastYear,
                    sDicRelThisYear,
                    sDicRelThisDay,
                    sDicRelRecent
            };
        }
        mContext = context;
    }

    /**
     * 获取所有内建的时间关键词
     * @return 内建的时间关键词列表
     */
    public List<String> getAllDateTimeKeywords() {
        ArrayList<String> list = new ArrayList<>();
        addKeywords(list, sDicAbsJanuary);
        addKeywords(list, sDicAbsFebruary);
        addKeywords(list, sDicAbsMarch);
        addKeywords(list, sDicAbsApril);
        addKeywords(list, sDicAbsMay);
        addKeywords(list, sDicAbsJune);
        addKeywords(list, sDicAbsJuly);
        addKeywords(list, sDicAbsAugust);
        addKeywords(list, sDicAbsSeptember);
        addKeywords(list, sDicAbsOctober);
        addKeywords(list, sDicAbsNovember);
        addKeywords(list, sDicAbsDecember);
        addKeywords(list, sDicDayNumber1);
        addKeywords(list, sDicDayNumber2);
        addKeywords(list, sDicDayNumber3);
        addKeywords(list, sDicDayNumber4);
        addKeywords(list, sDicDayNumber5);
        addKeywords(list, sDicDayNumber6);
        addKeywords(list, sDicDayNumber7);
        addKeywords(list, sDicDayNumber8);
        addKeywords(list, sDicDayNumber9);
        addKeywords(list, sDicDayNumber10);
        addKeywords(list, sDicDayNumber11);
        addKeywords(list, sDicDayNumber12);
        addKeywords(list, sDicDayNumber13);
        addKeywords(list, sDicDayNumber14);
        addKeywords(list, sDicDayNumber15);
        addKeywords(list, sDicDayNumber16);
        addKeywords(list, sDicDayNumber17);
        addKeywords(list, sDicDayNumber18);
        addKeywords(list, sDicDayNumber19);
        addKeywords(list, sDicDayNumber20);
        addKeywords(list, sDicDayNumber21);
        addKeywords(list, sDicDayNumber22);
        addKeywords(list, sDicDayNumber23);
        addKeywords(list, sDicDayNumber24);
        addKeywords(list, sDicDayNumber25);
        addKeywords(list, sDicDayNumber26);
        addKeywords(list, sDicDayNumber27);
        addKeywords(list, sDicDayNumber28);
        addKeywords(list, sDicDayNumber29);
        addKeywords(list, sDicDayNumber30);
        addKeywords(list, sDicDayNumber31);
        addKeywords(list, sDicRelPreviousYear);
        addKeywords(list, sDicRelLastYear);
        addKeywords(list, sDicRelThisYear);
        addKeywords(list, sDicRelThisDay);
        addKeywords(list, sDicRelMorningSixToNine);
        addKeywords(list, sDicRelMorningNineToTwelve);
        addKeywords(list, sDicRelNoon);
        addKeywords(list, sDicRelAfternoon);
        addKeywords(list, sDicRelNight);
        addKeywords(list, sDicRelAtNight);
        addKeywords(list, sDicRelEarlyMorning);
        addKeywords(list, sDicRelRecent);
        return list;
    }

    /**
     * 词语添加辅助函数
     * @param list 目标列表
     * @param dateInfo 待添加的日期关键词数据
     */
    private void addKeywords(ArrayList<String> list, DateInfo dateInfo) {
        String[] keywords = dateInfo.mKeywords;
        if ((keywords == null) || (keywords.length == 0)) {
            return;
        }
        Collections.addAll(list, keywords);
    }

    public static void updateDicRelArrays(Context context) {
        if (context == null) {
            return;
        }
        Resources resources = context.getResources();
        sDicRelThisYear = new DateInfo(resources, R.array.model_this_year);
        sDicRelLastYear = new DateInfo(resources, R.array.model_last_year);
        sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        sDicRelThisDay = new DateInfo(resources, R.array.model_this_day);
        sDicRelRecent = new DateInfo(resources, R.array.model_recent);
        sDicRelArrays = new DateInfo[]{
                sDicRelPreviousYear,
                sDicRelLastYear,
                sDicRelThisYear,
                sDicRelThisDay,
                sDicRelRecent
        };
        sDicRelMorningSixToNine = new DateInfo(resources, R.array.model_morning_six_to_nine);
        sDicRelMorningNineToTwelve = new DateInfo(resources, R.array.model_morning_nine_to_twelve);
        sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        sDicRelNoon = new DateInfo(resources, R.array.model_noon);
        sDicRelAfternoon = new DateInfo(resources, R.array.model_afternoon);
        sDicRelNight = new DateInfo(resources, R.array.model_night);
        sDicRelAtNight = new DateInfo(resources, R.array.model_at_night);
        sDicRelEarlyMorning = new DateInfo(resources, R.array.model_early_morning);
        sDicVocabularyArrays = new DateInfo[]{
                sDicRelMorningSixToNine,
                sDicRelMorningNineToTwelve,
                sDicRelNoon,
                sDicRelAfternoon,
                sDicRelNight,
                sDicRelAtNight,
                sDicRelEarlyMorning
        };
    }

    public List<String> completeDateTime(String keyword) {
        List<String> keywordList = new ArrayList<>();
        for (DateInfo info : sDicAbsMonthArrays) {
            if (info.indexOfKeyword(keyword)) {
                keywordList.add(info.mShootKeyword);
            }
        }
        for (DateInfo info : sDicVocabularyArrays) {
            if (info.containsKeyword(keyword)) {
                keywordList.add(info.mShootKeyword);
            }
        }
        // match Numerical dateTime
        int singleNumerical = 0;
        try {
            singleNumerical = Integer.parseInt(keyword);
        } catch (NumberFormatException e) {
            //ignore
            singleNumerical = 0;
        }
        if (singleNumerical > 0) {
            if (DateTimeSelector.containMonth(singleNumerical)) {
                // In exp language, this numerical could be year.
                List<Integer> matchedYears = mKeywordCache.searchYearMatch(singleNumerical);
                if (matchedYears != null) {
                    for (Integer matchedYear : matchedYears) {
                        keywordList.add(String.valueOf(matchedYear));
                    }
                }
            } else if (DateTimeSelector.containYear(singleNumerical)) {
                // this numerical is between 1970 - 2099, could be year.
                if (mKeywordCache.hasYear(singleNumerical)) {
                    keywordList.add(String.valueOf(singleNumerical));
                }
            } else if (DateTimeSelector.matchYear(singleNumerical)) {
                // this numerical is between 19-209, could be year.
                List<Integer> matchedYears = mKeywordCache.searchYearMatch(singleNumerical);
                if (matchedYears != null) {
                    for (Integer matchedYear : matchedYears) {
                        keywordList.add(String.valueOf(matchedYear));
                    }
                }
            }
        }
        return keywordList;
    }

    public ShootDateTime getShootDateTime(String keyword) {
        DateInfo info = null;
        DateRange range = null;
        if ((info = getAbsoluteMonth(keyword)) != null) {
            range = parseAbsoluteMonthRange(info);
        } else if ((info = getRelativeDate(keyword)) != null) {
            range = parseRelativeDateRange(info);
        } else if ((info = getVocabulary(keyword)) != null) {
            range = parseVocabularyRange(info);
        } else {
            range = parseNumericalDateRange(keyword);
        }
        if (range == null) {
            range = parseSpecialFormatDateRange(keyword);
        }

        ShootDateTime shootDateTime = null;
        if (range != null) {
            shootDateTime = new ShootDateTime();
            shootDateTime.dateRange = range;
            shootDateTime.shootKeyword = (info != null) ? info.mShootKeyword : upperCase(keyword);
            String condition = range.condition;
            String[] fmtArr = (condition != null) ? condition.split(",") : null;
            if ((fmtArr != null) && (fmtArr.length >= 2)) {
                shootDateTime.format = fmtArr[0];
                shootDateTime.conditions = (fmtArr.length > 2) ? condition.substring(condition.indexOf(",") + 1) : fmtArr[1];
            }
        }
        return shootDateTime;
    }

    /**
     * 如：一月
     *
     * @param info
     * @return
     */
    private DateRange parseAbsoluteMonthRange(DateInfo info) {
        int monthNumber = 0;
        // Found out the month of year
        for (int i = 0; i < sDicAbsMonthArrays.length; i++) {
            if (sDicAbsMonthArrays[i] == info) {
                monthNumber = i + 1;
                break;
            }
        }
        StringBuffer preSuffixBuffer = new StringBuffer();
        StringBuffer postSuffixBuffer = new StringBuffer();
        if (monthNumber > 0) {
            DateRange range = new DateRange();
            preSuffixBuffer.append("-%m");
            postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", monthNumber));
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

    private DateInfo getAbsoluteMonth(String keyword) {
        for (DateInfo info : sDicAbsMonthArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    private DateInfo getAbsoluteDay(String keyword) {
        for (DateInfo info : sDicAbsDayArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    private int findAbsoluteMonthNumber(String keyword) {
        DateInfo info = getAbsoluteMonth(keyword);
        if (info == null) {
            return -1;
        }
        int monthNumber = 0;
        // Found out the month of year
        for (int i = 0; i < sDicAbsMonthArrays.length; i++) {
            if (sDicAbsMonthArrays[i] == info) {
                monthNumber = i + 1;
                break;
            }
        }
        return monthNumber;
    }

    private int findAbsoluteDayNumber(String keyword) {
        DateInfo info = getAbsoluteDay(keyword);
        if (info == null) {
            return -1;
        }
        int dayNumber = 0;
        // Found out the day of month
        for (int i = 0; i < sDicAbsDayArrays.length; i++) {
            if (sDicAbsDayArrays[i] == info) {
                dayNumber = i + 1;
                break;
            }
        }
        return dayNumber;
    }

    private DateRange parseNumericalDateRange(String keyword) {
        int minLength = 1; // for yyyy format
        int midLength = 2; // for mm-dd, dd-mm, mm-yyyy,  mm-dd, dd-mm
        int maxLength = 3; // for dd-mm-yyyy
        String[] keywords = keyword.split(SearchCommonUtils.SPACE_REGEX);
        int length = keywords.length;
        if ((length < minLength) || (length > maxLength)) {
            GLog.d(TAG, "parseNumericalDateRange, not find legal date : " + keyword);
            return null;
        }
        int day = -1;
        int year = -1;
        int month = -1;

        final Pattern patternYear = Pattern.compile("^\\d{4}$");

        if (length == minLength) {
            // yyyy
            if (patternYear.matcher(keywords[0]).find()) {
                year = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidYear(year)) {
                    // check whether this year is valid or not
                    return null;
                }
            }
        } else if (length == midLength) {
            // mm-yyyy
            if (patternYear.matcher(keywords[1]).find()) {
                year = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidYear(year)) {
                    // check whether this year is valid or not
                    return null;
                }
                month = findAbsoluteMonthNumber(keywords[0]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    // check whether this month is valid or not
                    return null;
                }
            } else {
                // mm-dd, dd-mm
                day = findAbsoluteDayNumber(keywords[0]);
                month = findAbsoluteMonthNumber(keywords[1]);
                if ((day == -1) || (month == -1)) {
                    month = findAbsoluteMonthNumber(keywords[0]);
                    if (!DateTimeSelector.isValidMonth(month)) {
                        // check whether this month is valid or not
                        return null;
                    }
                    day = findAbsoluteDayNumber(keywords[1]);
                    if (!DateTimeSelector.isValidDay(year, month, day)) {
                        // check whether this day is valid or not
                        return null;
                    }
                }
            }
        } else if (length == maxLength) {
            // dd-mm-yyyy,
            if (patternYear.matcher(keywords[2]).find()) {
                year = Integer.parseInt(keywords[2]);
                if (!DateTimeSelector.isValidYear(year)) {
                    // check whether this year is valid or not
                    return null;
                }
                day = findAbsoluteDayNumber(keywords[0]);
                month = findAbsoluteMonthNumber(keywords[1]);
                if ((day == -1) || (month == -1)) {
                    month = findAbsoluteMonthNumber(keywords[0]);
                    if (!DateTimeSelector.isValidMonth(month)) {
                        // check whether this month is valid or not
                        return null;
                    }
                    day = findAbsoluteDayNumber(keywords[1]);
                    if (!DateTimeSelector.isValidDay(year, month, day)) {
                        // check whether this day is valid or not
                        return null;
                    }
                }
            }
        }

        if ((year > 0) || (month > 0) || (day > 0)) {
            DateRange range = new DateRange();
            StringBuffer preSuffixBuffer = new StringBuffer();
            StringBuffer postSuffixBuffer = new StringBuffer();
            if (year > 0) {
                preSuffixBuffer.append("-%Y");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%04d", year));
            }
            if (month > 0) {
                preSuffixBuffer.append("-%m");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", month));
            }
            if (day > 0) {
                preSuffixBuffer.append("-%d");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", day));
            }
            if (preSuffixBuffer.length() > 1) {
                preSuffixBuffer.delete(0, 1);
            }
            if (postSuffixBuffer.length() > 1) {
                postSuffixBuffer.delete(0, 1);
            }
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

    private DateInfo getVocabulary(String keyword) {
        for (DateInfo info : sDicVocabularyArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    private DateInfo getRelativeDate(String keyword) {
        for (DateInfo info : sDicRelArrays) {
            if (info.indexOfKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    /**
     * 解析模糊词时间范围
     *模糊词汇
     * 早上 - 6:00 至 9:00
     * 上午 - 9:00 至 12:00
     * 中午 - 12:00 至 13:00
     * 下午 - 13:00 至 18:00
     * 晚上 - 18:00 至 21:00
     * 夜里 - 21:00 至 24:00
     * 凌晨 - 0:00 至 6:00
     * 最近 - 过去一个月（30天）
     */
    private DateRange parseVocabularyRange(DateInfo info) {
        if (info == sDicRelMorningSixToNine) {
            DateRange range = new DateRange();
            range.condition = "%H, '06', '07', '08'";
            return range;
        } else if (info == sDicRelMorningNineToTwelve) {
            DateRange range = new DateRange();
            range.condition = "%H, '09', '10', '11'";
            return range;
        } else if (info == sDicRelNoon) {
            DateRange range = new DateRange();
            range.condition = "%H, '12'";
            return range;
        } else if (info == sDicRelAfternoon) {
            DateRange range = new DateRange();
            range.condition = "%H, '13', '14', '15', '16', '17'";
            return range;
        } else if (info == sDicRelNight) {
            DateRange range = new DateRange();
            range.condition = "%H, '18', '19', '20'";
            return range;
        } else if (info == sDicRelAtNight) {
            DateRange range = new DateRange();
            range.condition = "%H, '21', '22', '23'";
            return range;
        } else if (info == sDicRelEarlyMorning) {
            DateRange range = new DateRange();
            range.condition = "%H, '00', '01', '02', '03', '04', '05'";
            return range;
        } else {
            return null;
        }
    }

    /**
     * 如：去年，前年
     *
     * @param info
     * @return
     */
    private DateRange parseRelativeDateRange(DateInfo info) {
        GregorianCalendar calender = (GregorianCalendar) Calendar.getInstance();
        calender.setFirstDayOfWeek(Calendar.MONDAY);

        if (info == sDicRelPreviousYear) {
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.YEAR, -2);
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int year = calender.get(Calendar.YEAR);
            range.start = calender.getTimeInMillis();
            range.end = range.start + (calender.isLeapYear(year) ? LEAP_YEAR_MS : YEAR_MS) - 1;
            return range;
        } else if (info == sDicRelLastYear) {
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.YEAR, -1);
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int year = calender.get(Calendar.YEAR);
            range.start = calender.getTimeInMillis();
            range.end = range.start + (calender.isLeapYear(year) ? LEAP_YEAR_MS : YEAR_MS) - 1;
            return range;
        } else if (info == sDicRelThisYear) {
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int year = calender.get(Calendar.YEAR);
            range.start = calender.getTimeInMillis();
            range.end = range.start + (calender.isLeapYear(year) ? LEAP_YEAR_MS : YEAR_MS) - 1;
            return range;
        } else if (info == sDicRelThisDay) {
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + DAY_MS - 1;
            return range;
        } else if (info == sDicRelRecent) {
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.DAY_OF_YEAR, -TimeUtils.DAY_THIRTY);
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + TimeUtils.TIME_1_MONTH_IN_MS - 1;
            return range;
        }
        return null;
    }

    private void clearCalender(Calendar calendar, int... columns) {
        if (columns == null) {
            calendar.set(Calendar.YEAR, 0);
            calendar.set(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
        } else {
            for (int column : columns) {
                if ((column == Calendar.DAY_OF_MONTH) || (column == Calendar.DAY_OF_YEAR)) {
                    calendar.set(column, 1);
                } else {
                    calendar.set(column, 0);
                }
            }
        }
    }

    /**
     * DD-MM-YYYY
     *
     * @param keyword
     * @return
     */
    private DateRange parseSpecialFormatDateRange(String keyword) {
        boolean existDifferentConnector = (keyword.contains("-") && keyword.contains("/"))
                || (keyword.contains("-") && keyword.contains("."))
                || (keyword.contains("/") && keyword.contains("."));
        if (existDifferentConnector) {
            GLog.d(TAG, "parseSpecialFormatDateRange, exist different connector!");
            return null;
        }
        keyword = keyword.replaceAll("[./]", "-");
        if (!keyword.contains("-")) {
            GLog.d(TAG, "parseSpecialFormatDateRange, not exist valid connector!");
            return null;
        }
        // yyyy-mm
        Pattern patternYearMonth = Pattern.compile("^\\d{4}-\\d{1,2}$");
        Matcher matcher = patternYearMonth.matcher(keyword);
        if (matcher.matches()) {
            try {
                String[] keywords = keyword.split("-");
                int year = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidYear(year)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid year " + year);
                    return null;
                }
                int month = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid month " + month);
                    return null;
                }

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
                String postSuffix = dateFormat.format(dateFormat.parse(keyword));
                DateRange range = new DateRange();
                range.condition = "%Y-%m, '" + postSuffix + "'";
                return range;
            } catch (Exception e) {
                GLog.w(TAG, "parseSpecialFormatDateRange, Exception = " + e);
            }
        }
        // mm-yyyy
        Pattern patternMonthYear = Pattern.compile("^\\d{1,2}-\\d{4}$");
        matcher = patternMonthYear.matcher(keyword);
        if (matcher.matches()) {
            try {
                String[] keywords = keyword.split("-");
                int year = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidYear(year)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid year " + year);
                    return null;
                }
                int month = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid month " + month);
                    return null;
                }
                StringBuffer dateBuffer = new StringBuffer();
                dateBuffer.append(year);
                dateBuffer.append("-");
                dateBuffer.append(month);

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
                String postSuffix = dateFormat.format(dateFormat.parse(dateBuffer.toString()));
                DateRange range = new DateRange();
                range.condition = "%Y-%m, '" + postSuffix + "'";
                return range;
            } catch (Exception e) {
                GLog.w(TAG, "parseSpecialFormatDateRange, Exception = " + e);
            }
        }
        // dd-mm-yyyy or mm-dd-yyyy
        if (LocaleUtils.isNepal(ContextGetter.context) || LocaleUtils.isPhilippines(ContextGetter.context)) {
            return parseSpecialFormatMMDDYY(keyword);
        }
        return parseSpecialFormatDDMMYY(keyword);
    }

    /**
     * parse DD-MM-YY
     *
     * @param keyword
     * @return
     */
    private DateRange parseSpecialFormatDDMMYY(String keyword) {
        Pattern patternDayMonthYear = Pattern.compile("^\\d{1,2}-\\d{1,2}-\\d{4}$");
        Matcher matcher = patternDayMonthYear.matcher(keyword);
        if (matcher.matches()) {
            try {
                String[] keywords = keyword.split("-");
                int year = Integer.parseInt(keywords[2]);
                if (!DateTimeSelector.isValidYear(year)) {
                    GLog.d(TAG, "parseSpecialFormatDDMMYY, invalid year " + year);
                    return null;
                }
                int month = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    GLog.d(TAG, "parseSpecialFormatDDMMYY, invalid month " + month);
                    return null;
                }
                int day = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidDay(year, month, month)) {
                    GLog.d(TAG, "parseSpecialFormatDDMMYY, invalid day " + day);
                    return null;
                }
                StringBuffer dateBuffer = new StringBuffer();
                dateBuffer.append(year);
                dateBuffer.append("-");
                dateBuffer.append(month);
                dateBuffer.append("-");
                dateBuffer.append(day);

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
                String postSuffix = dateFormat.format(dateFormat.parse(dateBuffer.toString()));
                DateRange range = new DateRange();
                range.condition = "%Y-%m-%d, '" + postSuffix + "'";
                return range;
            } catch (Exception e) {
                // ignore
                GLog.w(TAG, "parseSpecialFormatDDMMYY, Exception is " + e);
            }
        }
        return null;
    }

    /**
     * parse MM-DD-YY
     *
     * @param keyword
     * @return
     */
    private DateRange parseSpecialFormatMMDDYY(String keyword) {
        Pattern patternDayMonthYear = Pattern.compile("^\\d{1,2}-\\d{1,2}-\\d{4}$");
        Matcher matcher = patternDayMonthYear.matcher(keyword);
        if (matcher.matches()) {
            try {
                String[] keywords = keyword.split("-");
                int year = Integer.parseInt(keywords[2]);
                if (!DateTimeSelector.isValidYear(year)) {
                    GLog.d(TAG, "parseSpecialFormatMMDDYY, invalid year " + year);
                    return null;
                }
                int month = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    GLog.d(TAG, "parseSpecialFormatMMDDYY, invalid month " + month);
                    return null;
                }
                int day = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidDay(year, month, month)) {
                    GLog.d(TAG, "parseSpecialFormatMMDDYY, invalid day " + day);
                    return null;
                }
                StringBuffer dateBuffer = new StringBuffer();
                dateBuffer.append(year);
                dateBuffer.append("-");
                dateBuffer.append(month);
                dateBuffer.append("-");
                dateBuffer.append(day);

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
                String postSuffix = dateFormat.format(dateFormat.parse(dateBuffer.toString()));
                DateRange range = new DateRange();
                range.condition = "%Y-%m-%d, '" + postSuffix + "'";
                return range;
            } catch (Exception e) {
                GLog.w(TAG, "parseSpecialFormatMMDDYY, Exception is " + e);
            }
        }
        return null;
    }

    /**
     * upperCase the first letter of each word
     *
     * @param str
     * @return
     */
    private String upperCase(String str) {
        final int charNum = 32;
        char[] ch = str.toCharArray();
        if ((ch[0] >= 'a') && (ch[0] <= 'z')) {
            ch[0] = (char) (ch[0] - charNum);
        }
        return new String(ch);
    }

}