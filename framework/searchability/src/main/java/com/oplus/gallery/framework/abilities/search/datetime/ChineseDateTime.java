/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ChineseDateTime.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2017/11/30
 ** Author:yanchao.Chen@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  yanch<PERSON>.<PERSON>@Apps.Gallery3D    2017/11/30      1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.framework.abilities.search.datetime;

import android.content.Context;
import android.content.res.Resources;

import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.framework.abilities.search.cache.KeywordCache;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateInfo;
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector.DateRange;
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime;
import com.oplus.gallery.framework.datatmp.R;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ChineseDateTime {
    private static final String TAG = "ChineseDateTime";

    private static final int WEEKEND = 8;
    private static final long DAY_MS = TimeUtils.TIME_1_DAY_IN_MS;
    private static final long WEEK_MS = DAY_MS * 7L;
    private static final long LEAP_YEAR_MS = DAY_MS * 366L;
    private static final long YEAR_MS = DAY_MS * 365L;

    private static DateInfo sDicCnDay;
    private static DateInfo sDicCnMonth;
    private static DateInfo sDicCnYear;

    private static DateInfo sDicCnMonday;
    private static DateInfo sDicCnTuesday;
    private static DateInfo sDicCnWednesday;
    private static DateInfo sDicCnThursday;
    private static DateInfo sDicCnFriday;
    private static DateInfo sDicCnSaturday;
    private static DateInfo sDicCnSunday;
    private static DateInfo sDicCnWeekend;

    private static DateInfo sDicCnJanuary;
    private static DateInfo sDicCnFebruary;
    private static DateInfo sDicCnMarch;
    private static DateInfo sDicCnApril;
    private static DateInfo sDicCnMay;
    private static DateInfo sDicCnJune;
    private static DateInfo sDicCnJuly;
    private static DateInfo sDicCnAugust;
    private static DateInfo sDicCnSeptember;
    private static DateInfo sDicCnOctober;
    private static DateInfo sDicCnNovember;
    private static DateInfo sDicCnDecember;

    private static DateInfo sDicRelThisYear;
    private static DateInfo sDicRelLastYear;
    private static DateInfo sDicRelPreviousYear;
    private static DateInfo sDicRelThisMonth;
    private static DateInfo sDicRelLastMonth;
    private static DateInfo sDicRelThisWeek;
    private static DateInfo sDicRelLastWeek;
    private static DateInfo sDicRelThisDay;
    private static DateInfo sDicRelLastDay;
    private static DateInfo sDicRelPreviousDay;
    private static DateInfo sDicRelMorningSixToNine;
    private static DateInfo sDicRelMorningNineToTwelve;
    private static DateInfo sDicRelNoon;
    private static DateInfo sDicRelAfternoon;
    private static DateInfo sDicRelNight;
    private static DateInfo sDicRelAtNight;
    private static DateInfo sDicRelEarlyMorning;
    private static DateInfo sDicRelRecent;

    private static DateInfo[] sDicCnVocabularyArrays;
    private static DateInfo[] sDicCnMonthArrays;
    private static DateInfo[] sDicCnWeekArrays;
    private static DateInfo[] sDicCnNumArrays;
    private static DateInfo[] sDicRelArrays;

    private ChineseNumber mChineseNumber;
    private KeywordCache mKeywordCache;

    public ChineseDateTime(Context context) {
        Resources resources = context.getResources();
        if (sDicCnDay == null) {
            sDicCnDay = new DateInfo(resources, R.array.model_cn_day);
        }
        if (sDicCnMonth == null) {
            sDicCnMonth = new DateInfo(resources, R.array.model_cn_month);
        }
        if (sDicCnYear == null) {
            sDicCnYear = new DateInfo(resources, R.array.model_cn_year);
        }
        if (sDicCnMonday == null) {
            sDicCnMonday = new DateInfo(resources, R.array.model_cn_monday);
        }
        if (sDicCnTuesday == null) {
            sDicCnTuesday = new DateInfo(resources, R.array.model_cn_tuesday);
        }
        if (sDicCnWednesday == null) {
            sDicCnWednesday = new DateInfo(resources, R.array.model_cn_wednesday);
        }
        if (sDicCnThursday == null) {
            sDicCnThursday = new DateInfo(resources, R.array.model_cn_thursday);
        }
        if (sDicCnFriday == null) {
            sDicCnFriday = new DateInfo(resources, R.array.model_cn_friday);
        }
        if (sDicCnSaturday == null) {
            sDicCnSaturday = new DateInfo(resources, R.array.model_cn_saturday);
        }
        if (sDicCnSunday == null) {
            sDicCnSunday = new DateInfo(resources, R.array.model_cn_sunday);
        }
        if (sDicCnWeekend == null) {
            sDicCnWeekend = new DateInfo(resources, R.array.model_cn_weekend);
        }
        if (sDicCnJanuary == null) {
            sDicCnJanuary = new DateInfo(resources, R.array.model_cn_january);
        }
        if (sDicCnFebruary == null) {
            sDicCnFebruary = new DateInfo(resources, R.array.model_cn_february);
        }
        if (sDicCnMarch == null) {
            sDicCnMarch = new DateInfo(resources, R.array.model_cn_march);
        }
        if (sDicCnApril == null) {
            sDicCnApril = new DateInfo(resources, R.array.model_cn_april);
        }
        if (sDicCnMay == null) {
            sDicCnMay = new DateInfo(resources, R.array.model_cn_may);
        }
        if (sDicCnJune == null) {
            sDicCnJune = new DateInfo(resources, R.array.model_cn_june);
        }
        if (sDicCnJuly == null) {
            sDicCnJuly = new DateInfo(resources, R.array.model_cn_july);
        }
        if (sDicCnAugust == null) {
            sDicCnAugust = new DateInfo(resources, R.array.model_cn_august);
        }
        if (sDicCnSeptember == null) {
            sDicCnSeptember = new DateInfo(resources, R.array.model_cn_september);
        }
        if (sDicCnOctober == null) {
            sDicCnOctober = new DateInfo(resources, R.array.model_cn_october);
        }
        if (sDicCnNovember == null) {
            sDicCnNovember = new DateInfo(resources, R.array.model_cn_november);
        }
        if (sDicCnDecember == null) {
            sDicCnDecember = new DateInfo(resources, R.array.model_cn_december);
        }
        if (sDicRelLastMonth == null) {
            sDicRelLastMonth = new DateInfo(resources, R.array.model_cn_last_month);
        }
        if (sDicRelThisMonth == null) {
            sDicRelThisMonth = new DateInfo(resources, R.array.model_cn_this_month);
        }
        if (sDicRelLastWeek == null) {
            sDicRelLastWeek = new DateInfo(resources, R.array.model_cn_last_week);
        }
        if (sDicRelThisWeek == null) {
            sDicRelThisWeek = new DateInfo(resources, R.array.model_cn_this_week);
        }
        if (sDicRelPreviousDay == null) {
            sDicRelPreviousDay = new DateInfo(resources, R.array.model_cn_previous_day);
        }
        if (sDicRelLastDay == null) {
            sDicRelLastDay = new DateInfo(resources, R.array.model_cn_last_day);
        }
        if (sDicRelThisDay == null) {
            sDicRelThisDay = new DateInfo(resources, R.array.model_cn_this_day);
        }
        if (sDicRelThisYear == null) {
            sDicRelThisYear = new DateInfo(resources, R.array.model_this_year);
        }
        if (sDicRelLastYear == null) {
            sDicRelLastYear = new DateInfo(resources, R.array.model_last_year);
        }
        if (sDicRelPreviousYear == null) {
            sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        }
        if (sDicRelMorningSixToNine == null) {
            sDicRelMorningSixToNine = new DateInfo(resources, R.array.model_morning_six_to_nine);
        }
        if (sDicRelMorningNineToTwelve == null) {
            sDicRelMorningNineToTwelve = new DateInfo(resources, R.array.model_morning_nine_to_twelve);
        }
        if (sDicRelPreviousYear == null) {
            sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        }
        if (sDicRelNoon == null) {
            sDicRelNoon = new DateInfo(resources, R.array.model_noon);
        }
        if (sDicRelAfternoon == null) {
            sDicRelAfternoon = new DateInfo(resources, R.array.model_afternoon);
        }
        if (sDicRelNight == null) {
            sDicRelNight = new DateInfo(resources, R.array.model_night);
        }
        if (sDicRelAtNight == null) {
            sDicRelAtNight = new DateInfo(resources, R.array.model_at_night);
        }
        if (sDicRelEarlyMorning == null) {
            sDicRelEarlyMorning = new DateInfo(resources, R.array.model_early_morning);
        }
        if (sDicRelRecent == null) {
            sDicRelRecent = new DateInfo(resources, R.array.model_recent);
        }

        if (sDicCnNumArrays == null) {
            sDicCnNumArrays = new DateInfo[]{
                    sDicCnDay,
                    sDicCnMonth,
                    sDicCnYear
            };
        }
        if (sDicCnWeekArrays == null) {
            sDicCnWeekArrays = new DateInfo[]{
                    sDicCnSunday,
                    sDicCnMonday,
                    sDicCnTuesday,
                    sDicCnWednesday,
                    sDicCnThursday,
                    sDicCnFriday,
                    sDicCnSaturday,
                    sDicCnWeekend
            };
        }
        if (sDicCnVocabularyArrays == null) {
            sDicCnVocabularyArrays = new DateInfo[]{
                    sDicRelMorningSixToNine,
                    sDicRelMorningNineToTwelve,
                    sDicRelNoon,
                    sDicRelAfternoon,
                    sDicRelNight,
                    sDicRelAtNight,
                    sDicRelEarlyMorning
            };
        }
        if (sDicCnMonthArrays == null) {
            sDicCnMonthArrays = new DateInfo[]{
                    sDicCnJanuary,
                    sDicCnFebruary,
                    sDicCnMarch,
                    sDicCnApril,
                    sDicCnMay,
                    sDicCnJune,
                    sDicCnJuly,
                    sDicCnAugust,
                    sDicCnSeptember,
                    sDicCnOctober,
                    sDicCnNovember,
                    sDicCnDecember
            };
        }
        if (sDicRelArrays == null) {
            sDicRelArrays = new DateInfo[]{
                    sDicRelPreviousYear,
                    sDicRelLastYear,
                    sDicRelThisYear,
                    sDicRelLastMonth,
                    sDicRelThisMonth,
                    sDicRelLastWeek,
                    sDicRelThisWeek,
                    sDicRelPreviousDay,
                    sDicRelLastDay,
                    sDicRelThisDay,
                    sDicRelRecent
            };
        }
        mChineseNumber = new ChineseNumber(resources);
    }

    /**
     * 词语添加辅助函数
     * @param list 目标列表
     * @param dateInfo 待添加的中文日期关键词数据
     */
    private void addKeywords(ArrayList<String> list, DateInfo dateInfo) {
        String[] keywords = dateInfo.mKeywords;
        if ((keywords == null) || (keywords.length == 0)) {
            return;
        }
        Collections.addAll(list, keywords);
    }

    /**
     * 获取所有内建的中文时间关键词
     * @return 内建的中文时间关键词列表
     */
    public List<String> getAllDateTimeKeywords() {
        ArrayList<String> list = new ArrayList<>();
        addKeywords(list, sDicCnDay);
        addKeywords(list, sDicCnMonth);
        addKeywords(list, sDicCnYear);
        addKeywords(list, sDicCnMonday);
        addKeywords(list, sDicCnTuesday);
        addKeywords(list, sDicCnWednesday);
        addKeywords(list, sDicCnThursday);
        addKeywords(list, sDicCnFriday);
        addKeywords(list, sDicCnSaturday);
        addKeywords(list, sDicCnSunday);
        addKeywords(list, sDicCnWeekend);
        addKeywords(list, sDicCnJanuary);
        addKeywords(list, sDicCnFebruary);
        addKeywords(list, sDicCnMarch);
        addKeywords(list, sDicCnApril);
        addKeywords(list, sDicCnMay);
        addKeywords(list, sDicCnJune);
        addKeywords(list, sDicCnJuly);
        addKeywords(list, sDicCnAugust);
        addKeywords(list, sDicCnSeptember);
        addKeywords(list, sDicCnOctober);
        addKeywords(list, sDicCnNovember);
        addKeywords(list, sDicCnDecember);
        addKeywords(list, sDicRelPreviousYear);
        addKeywords(list, sDicRelLastYear);
        addKeywords(list, sDicRelThisYear);
        addKeywords(list, sDicRelLastMonth);
        addKeywords(list, sDicRelThisMonth);
        addKeywords(list, sDicRelLastWeek);
        addKeywords(list, sDicRelThisWeek);
        addKeywords(list, sDicRelPreviousDay);
        addKeywords(list, sDicRelLastDay);
        addKeywords(list, sDicRelThisDay);
        addKeywords(list, sDicRelMorningSixToNine);
        addKeywords(list, sDicRelMorningNineToTwelve);
        addKeywords(list, sDicRelNoon);
        addKeywords(list, sDicRelAfternoon);
        addKeywords(list, sDicRelNight);
        addKeywords(list, sDicRelAtNight);
        addKeywords(list, sDicRelEarlyMorning);
        addKeywords(list, sDicRelRecent);
        return list;
    }

    public static void updateDicRelArrays(Context context) {
        if (context == null) {
            return;
        }
        Resources resources = context.getResources();
        sDicRelThisYear = new DateInfo(resources, R.array.model_this_year);
        sDicRelLastYear = new DateInfo(resources, R.array.model_last_year);
        sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        sDicRelLastMonth = new DateInfo(resources, R.array.model_cn_last_month);
        sDicRelThisMonth = new DateInfo(resources, R.array.model_cn_this_month);
        sDicRelLastWeek = new DateInfo(resources, R.array.model_cn_last_week);
        sDicRelThisWeek = new DateInfo(resources, R.array.model_cn_this_week);
        sDicRelPreviousDay = new DateInfo(resources, R.array.model_cn_previous_day);
        sDicRelLastDay = new DateInfo(resources, R.array.model_cn_last_day);
        sDicRelThisDay = new DateInfo(resources, R.array.model_cn_this_day);
        sDicRelRecent = new DateInfo(resources, R.array.model_recent);
        sDicRelArrays = new DateInfo[]{
                sDicRelPreviousYear,
                sDicRelLastYear,
                sDicRelThisYear,
                sDicRelLastMonth,
                sDicRelThisMonth,
                sDicRelLastWeek,
                sDicRelThisWeek,
                sDicRelPreviousDay,
                sDicRelLastDay,
                sDicRelThisDay,
                sDicRelRecent
        };
        sDicRelMorningSixToNine = new DateInfo(resources, R.array.model_morning_six_to_nine);
        sDicRelMorningNineToTwelve = new DateInfo(resources, R.array.model_morning_nine_to_twelve);
        sDicRelPreviousYear = new DateInfo(resources, R.array.model_previous_year);
        sDicRelNoon = new DateInfo(resources, R.array.model_noon);
        sDicRelAfternoon = new DateInfo(resources, R.array.model_afternoon);
        sDicRelNight = new DateInfo(resources, R.array.model_night);
        sDicRelAtNight = new DateInfo(resources, R.array.model_at_night);
        sDicRelEarlyMorning = new DateInfo(resources, R.array.model_early_morning);
        sDicCnVocabularyArrays = new DateInfo[]{
                sDicRelMorningSixToNine,
                sDicRelMorningNineToTwelve,
                sDicRelNoon,
                sDicRelAfternoon,
                sDicRelNight,
                sDicRelAtNight,
                sDicRelEarlyMorning
        };
    }

    public void setKeywordCache(KeywordCache keywordCache) {
        mKeywordCache = keywordCache;
    }

    /**
     * get shoot datetime from keyword, include week, month, relative date and
     * numerical date
     *
     * @param keyword
     * @return
     */
    public ShootDateTime getShootDateTime(String keyword) {
        DateInfo info = null;
        DateRange range = null;
        if ((info = getAbsoluteWeek(keyword)) != null) {
            range = parseAbsoluteWeekRange(info);
        } else if ((info = getAbsoluteMonth(keyword)) != null) {
            range = parseAbsoluteMonthRange(info);
        } else if ((info = getRelativeDate(keyword)) != null) {
            range = parseRelativeDateRange(info);
        } else if ((info = getNumericalDateKeyword(keyword)) != null) {
            range = parseNumericalDateRange(keyword);
        } else if ((info = getVocabulary(keyword)) != null) {
            range = parseVocabularyRange(info);
        } else {
            range = parseSpecialFormatDateRange(keyword);
        }
        ShootDateTime shootDateTime = null;
        if (range != null) {
            shootDateTime = new ShootDateTime();
            shootDateTime.dateRange = range;
            shootDateTime.shootKeyword = (info != null) ? info.mShootKeyword : keyword;
            String condition = range.condition;
            String[] fmtArr = (condition != null) ? condition.split(",") : null;
            if ((fmtArr != null) && (fmtArr.length >= 2)) {
                shootDateTime.format = fmtArr[0];
                shootDateTime.conditions = (fmtArr.length > 2) ? condition.substring(condition.indexOf(",") + 1) : fmtArr[1];
            }
        }
        return shootDateTime;
    }

    /**
     * fuzzy match date time, include relative date, week, and month
     * 1970-2099
     *
     * @param keyword
     * @return
     */
    public List<String> completeDateTime(String keyword) {
        List<String> keywordList = new ArrayList<>();
        for (DateInfo info : sDicRelArrays) {
            if (info.containsKeyword(keyword)) {
                keywordList.add(info.mShootKeyword);
            }
        }
        for (DateInfo info : sDicCnWeekArrays) {
            if (info == sDicCnWeekend) {
                //weekend support input association
                if (info.containsKeyword(keyword)) {
                    keywordList.add(info.mShootKeyword);
                }
            } else {
                if (info.equalsKeyword(keyword)) {
                    keywordList.add(info.mShootKeyword);
                }
            }
        }
        for (DateInfo info : sDicCnVocabularyArrays) {
            if (info.containsKeyword(keyword)) {
                keywordList.add(info.mShootKeyword);
            }
        }
        // match numerical month 1-12, year 1970-2099
        int singleNumerical = mChineseNumber.decode(keyword);
        if (singleNumerical < 0) {
            try {
                singleNumerical = Integer.parseInt(keyword);
            } catch (NumberFormatException e) {
                //ignore
                singleNumerical = 0;
            }
        }
        if (singleNumerical > 0) {
            if (DateTimeSelector.containMonth(singleNumerical)) {
                // in chinese language, this numerical is month
                if (mKeywordCache.hasMonth(singleNumerical)) {
                    keywordList.add(keyword + ChineseDateTime.sDicCnMonth.mKeywords[0]);
                }
            } else if (DateTimeSelector.containYear(singleNumerical)) {
                // this numerical is between 1970 - 2099, could be year.
                if (mKeywordCache.hasYear(singleNumerical)) {
                    keywordList.add(singleNumerical + ChineseDateTime.sDicCnYear.mKeywords[0]);
                }
            } else if (DateTimeSelector.matchYear(singleNumerical)) {
                // this numerical is between 19-209, could be year.
                List<Integer> matchedYears = mKeywordCache.searchYearMatch(singleNumerical);
                if (matchedYears != null) {
                    for (Integer matchedYear : matchedYears) {
                        keywordList.add(matchedYear + ChineseDateTime.sDicCnYear.mKeywords[0]);
                    }
                }
            }
        }
        return keywordList;
    }

    /**
     *  DD-MM-YYYY
     * @param keyword
     * @return
     */
    private DateRange parseSpecialFormatDateRange(String keyword) {
        boolean existDifferentConnector = (keyword.contains("-") && keyword.contains("/"))
                || (keyword.contains("-") && keyword.contains("."))
                || (keyword.contains("/") && keyword.contains("."));
        if (existDifferentConnector) {
            GLog.d(TAG, "parseSpecialFormatDateRange, exist different connector!");
            return null;
        }
        keyword = keyword.replaceAll("[./]", "-");
        if (!keyword.contains("-")) {
            GLog.d(TAG, "parseSpecialFormatDateRange, not exist - connector!");
            return null;
        }
        // yyyy-mm-dd
        Pattern patternYearMonthDay = Pattern.compile("^\\d{4}-\\d{1,2}-\\d{1,2}$");
        Matcher matcher = patternYearMonthDay.matcher(keyword);
        if (matcher.matches()) {
            try {
                String[] keywords = keyword.split("-");
                int year = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidYear(year)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid year " + year);
                    return null;
                }
                int month = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid month " + month);
                    return null;
                }
                int day = Integer.parseInt(keywords[2]);
                if (!DateTimeSelector.isValidDay(year, month, day)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid day " + day);
                    return null;
                }

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String postSuffix = dateFormat.format(dateFormat.parse(keyword));
                DateRange range = new DateRange();
                range.condition = "%Y-%m-%d, '" + postSuffix + "'";
                return range;
            } catch (Exception e) {
                GLog.w(TAG, "parseSpecialFormatDateRange, Exception = " + e);
            }
        }
        // yyyy-mm
        Pattern patternYearMonth = Pattern.compile("^\\d{4}-\\d{1,2}$");
        matcher = patternYearMonth.matcher(keyword);
        if (matcher.matches()) {
            try {
                String[] keywords = keyword.split("-");
                int year = Integer.parseInt(keywords[0]);
                if (!DateTimeSelector.isValidYear(year)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid year " + year);
                    return null;
                }
                int month = Integer.parseInt(keywords[1]);
                if (!DateTimeSelector.isValidMonth(month)) {
                    GLog.d(TAG, "parseSpecialFormatDateRange, invalid month " + month);
                    return null;
                }

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
                String postSuffix = dateFormat.format(dateFormat.parse(keyword));
                DateRange range = new DateRange();
                range.condition = "%Y-%m, '" + postSuffix + "'";
                return range;
            } catch (Exception e) {
                GLog.w(TAG, "parseSpecialFormatDateRange, Exception = " + e);
            }
        }
        return null;
    }

    /**
     * 如：2016年10月, 10月
     *
     * @param keyword
     * @return
     */
    private DateRange parseNumericalDateRange(String keyword) {
        Matcher matcher = SearchCommonUtils.CHINESE_DATE_REGEX_PATTERN.matcher(keyword);
        if (!matcher.matches()) {
            return null;
        }
        // Each date separator word could at most appear once in keyword
        int yearIndex = -1;
        int monthIndex = -1;
        int dayIndex = -1;
        do {
            int index = sDicCnYear.indexOfKeyword(keyword, (yearIndex < 0) ? 0 : yearIndex + 1);
            if (yearIndex != -1) {
                if (index != -1) {
                    return null;
                } else {
                    break;
                }
            } else {
                if (index != -1) {
                    yearIndex = index;
                } else {
                    break;
                }
            }
        } while (true);
        do {
            int index = sDicCnMonth.indexOfKeyword(keyword, (monthIndex < 0) ? 0 : monthIndex + 1);
            if (monthIndex != -1) {
                if (index != -1) {
                    return null;
                } else {
                    break;
                }
            } else {
                if (index != -1) {
                    monthIndex = index;
                } else {
                    break;
                }
            }
        } while (true);
        do {
            int index = sDicCnDay.indexOfKeyword(keyword, (dayIndex < 0) ? 0 : dayIndex + 1);
            if (dayIndex != -1) {
                if (index != -1) {
                    return null;
                } else {
                    break;
                }
            } else {
                if (index != -1) {
                    dayIndex = index;
                } else {
                    break;
                }
            }
        } while (true);

        int start = 0;
        int year = -1;
        int month = -1;
        int day = -1;

        // Year, if year == 0, that means 2000 A.D.
        if (yearIndex > 0) {
            start = findFloor(yearIndex, -1, monthIndex, dayIndex);
            if (start < yearIndex) {
                year = mChineseNumber.decode(keyword.substring(start + 1, yearIndex));
                /*
                Don't correct this input error, just return null to make
                this input invalid.
                year = year < 1970 ? -1 : year;
                */
                if (!DateTimeSelector.isValidYear(year)) {
                    return null;
                }
            }
        }

        // Month
        if (monthIndex > 0) {
            //start = Math.max(Math.min(yearIndex, monthIndex), Math.min(dayIndex, monthIndex));
            start = findFloor(monthIndex, -1, yearIndex, dayIndex);
            if (start < monthIndex) {
                month = mChineseNumber.decode(keyword.substring(start + 1, monthIndex));
                /*
                Don't correct this input error, just return null to make
                this input invalid.
                month = month < 1 ? -1 : month > 12 ? -1 : month;
                */
                if (!DateTimeSelector.isValidMonth(month)) {
                    return null;
                }
            }
        }

        // Day of month
        if (dayIndex > 0) {
            start = findFloor(dayIndex, -1, yearIndex, monthIndex);
            if (start < dayIndex) {
                day = mChineseNumber.decode(keyword.substring(start + 1, dayIndex));
                /*
                Don't correct this input error, just return null to make
                this input invalid.
                day = day < 1 ? -1 : day > maxDay ? -1 : day;
                */
                if (!DateTimeSelector.isValidDay(year, month, day)) {
                    return null;
                }
            }
        }

        if ((year > 0) || (month > 0) || (day > 0)) {
            // YYYY-mm-dd
            DateRange range = new DateRange();
            StringBuffer preSuffixBuffer = new StringBuffer();
            StringBuffer postSuffixBuffer = new StringBuffer();
            if (year > -1) {
                preSuffixBuffer.append("-%Y");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%04d", year));
            }
            if (month > 0) {
                preSuffixBuffer.append("-%m");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", month));
            }
            if (day > 0) {
                preSuffixBuffer.append("-%d");
                postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", day));
            }

            if (preSuffixBuffer.length() > 1) {
                preSuffixBuffer.delete(0, 1);
            }
            if (postSuffixBuffer.length() > 1) {
                postSuffixBuffer.delete(0, 1);
            }
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

    /**
     * 如：周一
     *
     * @param info
     * @return
     */
    private DateRange parseAbsoluteWeekRange(DateInfo info) {
        int weekOfDay = 0;
        /*
        Found out the day of the week
        If weekOfDay is 1, that means Sunday
        weekOfDay is 2, that means Monday
        */
        for (int i = 0; i < sDicCnWeekArrays.length; i++) {
            if (sDicCnWeekArrays[i] == info) {
                weekOfDay = i + 1;
                break;
            }
        }
        if (weekOfDay == 0) {
            return null;
        }
        if (weekOfDay == WEEKEND) {
            DateRange range = new DateRange();
            range.condition = "%w, '6', '0'"; // 6 for Saturday, 0 for Sunday
            return range;
        } else {
            DateRange range = new DateRange();
            range.condition = "%w, '" + (weekOfDay - 1) + "'";
            return range;
        }
    }

    /**
     * 解析模糊词时间范围
     *模糊词汇
     * 早上 - 6:00 至 9:00
     * 上午 - 9:00 至 12:00
     * 中午 - 12:00 至 13:00
     * 下午 - 13:00 至 18:00
     * 晚上 - 18:00 至 21:00
     * 夜里 - 21:00 至 24:00
     * 凌晨 - 0:00 至 6:00
     * 最近 - 过去一个月（30天）
     * @param info 模糊词
     * @return 模糊此对应的时间范围
     */
    private DateRange parseVocabularyRange(DateInfo info) {
        DateRange range = new DateRange();
        if (info == sDicRelMorningSixToNine) {
            range.condition = "%H, '06', '07', '08'";
        } else if (info == sDicRelMorningNineToTwelve) {
            range.condition = "%H, '09', '10', '11'";
        } else if (info == sDicRelNoon) {
            range.condition = "%H, '12'";
        } else if (info == sDicRelAfternoon) {
            range.condition = "%H, '13', '14', '15', '16', '17'";
        } else if (info == sDicRelNight) {
            range.condition = "%H, '18', '19', '20'";
        } else if (info == sDicRelAtNight) {
            range.condition = "%H, '21', '22', '23'";
        } else if (info == sDicRelEarlyMorning) {
            range.condition = "%H, '00', '01', '02', '03', '04', '05'";
        }
        return range;
    }

    /**
     * 如：一月
     *
     * @param info
     * @return
     */
    private DateRange parseAbsoluteMonthRange(DateInfo info) {
        int monthNumber = 0;
        // Found out the month of year
        for (int i = 0; i < sDicCnMonthArrays.length; i++) {
            if (sDicCnMonthArrays[i] == info) {
                monthNumber = i + 1;
                break;
            }
        }
        StringBuffer preSuffixBuffer = new StringBuffer();
        StringBuffer postSuffixBuffer = new StringBuffer();
        if (monthNumber > 0) {
            DateRange range = new DateRange();
            preSuffixBuffer.append("-%m");
            postSuffixBuffer.append(String.format(Locale.ENGLISH, "-%02d", monthNumber));
            String preSuffix = preSuffixBuffer.toString();
            String postSuffix = postSuffixBuffer.toString();
            range.condition = preSuffix + ", '" + postSuffix + "'";
            return range;
        }
        return null;
    }

    /**
     * 如：上周，前年
     *
     * @param info
     * @return
     */
    private DateRange parseRelativeDateRange(DateInfo info) {
        GregorianCalendar calender = (GregorianCalendar) Calendar.getInstance();
        calender.setFirstDayOfWeek(Calendar.MONDAY);

        if (info == sDicRelPreviousYear) {
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.YEAR, -2);
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int year = calender.get(Calendar.YEAR);
            range.start = calender.getTimeInMillis();
            range.end = range.start + (calender.isLeapYear(year) ? LEAP_YEAR_MS : YEAR_MS) - 1;
            return range;
        } else if (info == sDicRelLastYear) {
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_MONTH,
                    Calendar.HOUR, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.YEAR, -1);
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int year = calender.get(Calendar.YEAR);
            range.start = calender.getTimeInMillis();
            range.end = range.start + (calender.isLeapYear(year) ? LEAP_YEAR_MS : YEAR_MS) - 1;
            return range;
        } else if (info == sDicRelThisYear) {
            clearCalender(calender, Calendar.MONTH, Calendar.DAY_OF_YEAR,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int year = calender.get(Calendar.YEAR);
            range.start = calender.getTimeInMillis();
            range.end = range.start + (calender.isLeapYear(year) ? LEAP_YEAR_MS : YEAR_MS) - 1;
            return range;
        } else if (info == sDicRelLastMonth) {
            clearCalender(calender, Calendar.DAY_OF_MONTH,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.MONTH, -1);
            clearCalender(calender, Calendar.DAY_OF_MONTH,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int days = DateTimeSelector.daysInMouth(calender);
            range.start = calender.getTimeInMillis();
            range.end = range.start + days * DAY_MS - 1;
            return range;
        } else if (info == sDicRelThisMonth) {
            clearCalender(calender, Calendar.DAY_OF_MONTH,
                    Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            int days = DateTimeSelector.daysInMouth(calender);
            range.start = calender.getTimeInMillis();
            range.end = range.start + days * DAY_MS - 1;
            return range;
        } else if (info == sDicRelLastWeek) {
            setToMonday(calender);
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.WEEK_OF_YEAR, -1);
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + WEEK_MS - 1;
            return range;
        } else if (info == sDicRelThisWeek) {
            setToMonday(calender);
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + WEEK_MS - 1;
            return range;
        } else if (info == sDicRelPreviousDay) {
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.DAY_OF_YEAR, -2);
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + DAY_MS - 1;
            return range;
        } else if (info == sDicRelLastDay) {
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.DAY_OF_YEAR, -1);
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + DAY_MS - 1;
            return range;
        } else if (info == sDicRelThisDay) {
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + DAY_MS - 1;
            return range;
        } else if (info == sDicRelRecent) {
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            calender.add(Calendar.DAY_OF_YEAR, -TimeUtils.DAY_THIRTY);
            clearCalender(calender, Calendar.HOUR_OF_DAY, Calendar.MINUTE, Calendar.SECOND, Calendar.MILLISECOND);
            DateRange range = new DateRange();
            range.start = calender.getTimeInMillis();
            range.end = range.start + TimeUtils.TIME_1_MONTH_IN_MS - 1;
            return range;
        }
        return null;
    }

    private void clearCalender(Calendar calendar, int... columns) {
        if (columns == null) {
            calendar.set(Calendar.YEAR, 0);
            calendar.set(Calendar.MONTH, 0);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
        } else {
            for (int column : columns) {
                if ((column == Calendar.DAY_OF_MONTH) || (column == Calendar.DAY_OF_YEAR)) {
                    calendar.set(column, 1);
                } else {
                    calendar.set(column, 0);
                }
            }
        }
    }

    private void setToMonday(Calendar calendar) {
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
    }

    private int findFloor(int ref, int a, int b, int c) {
        int result = ref;
        result = (a < ref) ? a : ref;
        result = ((b < ref) && (b > result)) ? b : result;
        result = ((c < ref) && (c > result)) ? c : result;
        return result;
    }

    /**
     * 通过 query 中的关键词查询对应的模糊此 DateInfo
     * @param keyword query 中的关键词
     * @return 返回匹配到的模糊词
     */
    private DateInfo getVocabulary(String keyword) {
        for (DateInfo info : sDicCnVocabularyArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    private DateInfo getAbsoluteWeek(String keyword) {
        for (DateInfo info : sDicCnWeekArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    private DateInfo getAbsoluteMonth(String keyword) {
        for (DateInfo info : sDicCnMonthArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    private DateInfo getRelativeDate(String keyword) {
        for (DateInfo info : sDicRelArrays) {
            if (info.equalsKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

    private DateInfo getNumericalDateKeyword(String keyword) {
        for (DateInfo info : sDicCnNumArrays) {
            if (info.containedKeyword(keyword)) {
                return info;
            }
        }
        return null;
    }

}
