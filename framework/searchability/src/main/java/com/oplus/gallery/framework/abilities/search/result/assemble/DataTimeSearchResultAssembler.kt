/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DataTimeSearchResultAssembler.kt
 ** Description: 日期时间搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import android.util.Pair
import com.oplus.gallery.business_lib.model.data.base.entry.DateTimeEntry
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.completion.DateTimeCompletion
import com.oplus.gallery.framework.abilities.search.entry.KeywordString
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.entry.ShootDateTime
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.Locale

/**
 * 日期时间搜索结果拼装器
 */
internal class DataTimeSearchResultAssembler(
    private val keywordCacheManager: KeywordCacheManager,
    private val festivalSelector: FestivalSelector
) : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_DATETIME

    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        val searchClassifier = searchEntry.searchClassifier
        val forceQuery = searchEntry.searchForceQuery
        GLog.d(TAG, "[assemble] singleKeyword=$singleKeyword,forceQuery = $forceQuery")
        val startTime = System.currentTimeMillis()
        val keywordCache = keywordCacheManager.keywordCache

        // 确保在使用前资源字符串已更新,增加健壮性
        KeywordString.reload()
        // 1.最近30天包含了搜索关键词
        if (searchClassifier.contains(KeywordString.recentAddedKeyword.lowercase(Locale.getDefault()), singleKeyword)) {
            // 数据库查询最近30天的媒体列表
            val recentMediaIdList = DBSearchUtils.queryMediaIdEntryByRecentlyAdded(ContextGetter.context)
            GLog.d(TAG, "[assemble] queryMediaIdEntryByRecentlyAdded recentMediaIdList:$recentMediaIdList")
            if (recentMediaIdList.isNullOrEmpty().not()) {
                // 结果保存到SingleQueryResult中
                singleQueryResult.appendQueryResult(recentMediaIdList, KeywordString.recentAddedKeyword, SearchType.TYPE_DATETIME)
            }
        }

        // 2.对搜索关键词进行补全, 按照日期范围检索
        val dateTimeCompletion = DateTimeCompletion(ContextGetter.context, keywordCacheManager)
        val completionDatetime: List<ShootDateTime> = dateTimeCompletion.complete(singleKeyword)
        // 补全后按照日期范围查询媒体列表
        val dateTimeList = DBSearchUtils.queryMediaIdByDateRanges(ContextGetter.context, completionDatetime)
        dateTimeList?.forEach { dateTime ->
            dateTime.mediaIdEntries.let { dateIdsList ->
                dateTime.shootKeyword?.let { singleQueryResult.appendQueryResult(dateIdsList, it, SearchType.TYPE_DATETIME) }
            }
        }

        // 3.将搜索关键字映射为节日，再来搜索节日, 节日与时间
        val festivalPairList: MutableList<Pair<String, String>>? = festivalSelector.convert(singleKeyword)
        if (festivalPairList.isNullOrEmpty().not()) {
            var festivalMap: HashMap<String, DateTimeEntry>? = keywordCache.festivalMap
            // 强制查询或者节日信息缓存为空时，则从数据库查
            if (forceQuery || festivalMap.isNullOrEmpty()) {
                val dateTimeEntriesForFestivals = DBSearchUtils.queryDateTimeEntryForFestivals()
                festivalMap = festivalSelector.getMatchedFestival(dateTimeEntriesForFestivals)
                keywordCache.festivalMap = festivalMap
            }
            festivalMap?.let {
                festivalPairList?.forEach { festivalPair ->
                    val festival = it[festivalPair.first]
                    festival?.let {
                        val dateIdsList: MutableList<MediaIdEntry> = it.mediaIdList
                        if (dateIdsList.isNotEmpty()) {
                            singleQueryResult.appendQueryResult(dateIdsList, festivalPair.second, SearchType.TYPE_DATETIME)
                        }
                    }
                }
            }
        }
        GLog.v(TAG, "[assemble], costTime:${GLog.getTime(startTime)}, forceQuery : " + forceQuery)
        return singleQueryResult
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}DataTimeSearchResultAssembler"
    }
}