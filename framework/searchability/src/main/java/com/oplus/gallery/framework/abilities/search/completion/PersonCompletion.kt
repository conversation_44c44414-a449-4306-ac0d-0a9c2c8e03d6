/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonCompletion.kt
 ** Description:人物图集补全
 ** Version: 1.0
 ** Date: 2023/9/28
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/28      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import android.os.Bundle
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.andes.photos.kit.search.data.QueryCompletionEntry
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.framework.abilities.search.entry.ShootPerson
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager

/**
 * 人物图集补全
 */
internal class PersonCompletion(context: Context, private val keywordCacheManager: KeywordCacheManager) :
    BaseCompletionAbility(context, keywordCacheManager) {

    override fun getCompletionType(): Int = CompletionType.TYPE_PERSON

    override fun queryComplete(query: String): List<QueryCompletionEntry> {
        val completePersons = complete(query)
        GLog.d(TAG, "[queryComplete] query:$query, completePersons:${completePersons.map { it.shootKeyword }}")
        return completePersons.map { shootPerson ->
            QueryCompletionEntry(
                shootPerson.shootKeyword,
                EntityType.ENTITY_PERSON,
                "${shootPerson.groupId}",
                query
            )
        }
    }

    /**
     * 人物名补全
     *
     * @param query 搜索词
     * @return 人物信息列表
     */
    fun complete(query: String): List<ShootPerson> {
        GTrace.traceBegin("Person.complete")
        val startTime = System.currentTimeMillis()
        if (keywordCache.personKeywordMap.isNullOrEmpty()) {
            keywordCacheManager.updatePersonNameMapCache()
        }
        val completionPersonList = mutableListOf<ShootPerson>()
        if (query.isNotEmpty() && query.trim().isNotEmpty()) {
            val trimKeyword = query.lowercase().trim()
            val searchClassifier = buildClassifier(query)
            keywordCache.personKeywordMap?.mapValues { (groupId, personName) ->
                if (searchClassifier.contains(personName.lowercase(), trimKeyword)) {
                    val shootPerson = ShootPerson()
                    shootPerson.groupId = groupId
                    shootPerson.shootKeyword = personName
                    completionPersonList.add(shootPerson)
                }
            }
        }
        GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
        GTrace.traceEnd()
        return completionPersonList
    }

    override fun queryComplete(query: String, extra: Bundle?): List<QueryCompletionEntry> { return queryComplete(query) }

    companion object {
        private const val TAG = "${SearchConst.TAG}PersonCompletion"
    }
}