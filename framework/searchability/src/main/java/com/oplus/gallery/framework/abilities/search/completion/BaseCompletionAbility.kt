/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseCompletionAbility.kt
 ** Description: 补全能力抽象基类
 ** Version: 1.0
 ** Date: 2023/9/29
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/9/29      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import com.oplus.andes.photos.kit.search.ability.ICompletionAbility
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.cache.KeywordCache
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier

/**
 * 单个关键词补全能力抽象基类
 *
 * @property context 上下文对象
 * @property keywordCache 关键词Cache对象
 */
internal abstract class BaseCompletionAbility constructor(
    private val context: Context,
    keywordCacheManager: KeywordCacheManager
) : ICompletionAbility {

    val keywordCache: KeywordCache = keywordCacheManager.keywordCache

    open fun buildClassifier(query: String): SearchClassifier {
        val searchClassifier = SearchClassifier(context)
        searchClassifier.classify(query, getSearchType())
        searchClassifier.setKeywordCache(keywordCache)
        return searchClassifier
    }

    private fun completionTypeToSearchType(type: Int): Int =
        when (type) {
            CompletionType.TYPE_DATETIME, CompletionType.TYPE_FESTIVAL -> SearchType.TYPE_DATETIME
            CompletionType.TYPE_LOCATION -> SearchType.TYPE_LOCATION
            CompletionType.TYPE_LABEL -> SearchType.TYPE_LABEL
            CompletionType.TYPE_PERSON -> SearchType.TYPE_PERSON
            CompletionType.TYPE_ALBUM -> SearchType.TYPE_ALBUM
            CompletionType.TYPE_MEMORIES_ALBUM -> SearchType.TYPE_MEMORIES_ALBUM
            else -> SearchType.TYPE_MASK_TYPE
        }


    protected fun getSearchType(): Int {
        return completionTypeToSearchType(getCompletionType())
    }

    override fun equals(other: Any?): Boolean {
        if ((other is BaseCompletionAbility).not()) {
            return false
        }
        return getCompletionType() == (other as? BaseCompletionAbility)?.getCompletionType()
    }

    override fun hashCode(): Int {
        var result = context.hashCode()
        result = 31 * result + keywordCache.hashCode()
        return result
    }
}