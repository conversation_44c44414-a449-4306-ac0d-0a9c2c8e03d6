/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LocationCompletion.kt
 ** Description:地点搜索关键字的补全
 ** Version: 1.0
 ** Date: 2023/9/29
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/9/29      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.completion

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.util.Pair
import com.oplus.andes.photos.kit.search.data.CompletionType
import com.oplus.andes.photos.kit.search.data.QueryCompletionEntry
import com.oplus.andes.photos.kit.search.query.EntityType
import com.oplus.gallery.business_lib.model.config.allowlist.AndesSearchRusConfig
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.business_lib.model.data.location.LocationType
import com.oplus.gallery.business_lib.model.data.location.utils.GeoDBHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.ext.getBooleanSafe
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.KeywordCacheManager
import com.oplus.gallery.framework.abilities.search.entry.ShootLocation
import com.oplus.gallery.framework.abilities.search.utils.SearchClassifier
import java.util.Locale
import java.util.regex.Pattern
import com.oplus.gallery.framework.datatmp.R as DatatmpR

/**
 * 地点搜索关键字的补全
 */
internal class LocationCompletion(
    private val context: Context,
    private val keywordCacheManager: KeywordCacheManager
) : BaseCompletionAbility(context, keywordCacheManager) {

    /**
     * 内外销差异：是否需要过滤国家和省份的匹配：外销不匹配国家和省份
     */
    private val isNeedFilterCountryAndProvince: Boolean by lazy { context.getBooleanSafe(DatatmpR.bool.search_filter_country_and_province) }

    override fun getCompletionType(): Int = CompletionType.TYPE_LOCATION

    override fun queryComplete(query: String): List<QueryCompletionEntry> {
        val searchClassifier = buildClassifier(query)
        val completeLocations = complete(searchClassifier, query)
        GLog.d(TAG, "[queryComplete] query:$query, completeLocations:${completeLocations.map { it.shootKeyword }}")
        latestCompleteLocations.clear()
        val completeLocationEntry = completeLocations.map {
            QueryCompletionEntry(it.shootKeyword, getEntityType(it), it.shootKeyword, query)
        }
        latestCompleteLocations.addAll(completeLocationEntry)
        return completeLocationEntry
    }

    private fun getEntityType(shootLocation: ShootLocation): Int {
        val shootList = shootLocation.shootKeywordList
        if (shootList.isNullOrEmpty()) {
            return EntityType.ENTITY_STREET
        }
        shootList[0].first?.let { idx ->
            return when (idx) {
                COUNTRY_IDX -> EntityType.ENTITY_COUNTRY
                PROVINCE_IDX -> EntityType.ENTITY_PROVINCE
                CITY_IDX -> EntityType.ENTITY_CITY
                DISTRICT_IDX -> EntityType.ENTITY_DISTRICT
                STREET_IDX -> EntityType.ENTITY_STREET
                POI_IDX -> EntityType.ENTITY_POI
                else -> EntityType.ENTITY_STREET
            }
        } ?: EntityType.ENTITY_STREET
        return EntityType.ENTITY_STREET
    }

    override fun queryComplete(query: String, extra: Bundle?): List<QueryCompletionEntry> { return queryComplete(query) }

    /**
     * 地点搜索词补全
     *
     * @param classifier SearchClassifier
     * @param query 搜索词
     * @return
     */
    fun complete(classifier: SearchClassifier, query: String): MutableList<ShootLocation> {
        GTrace.traceBegin("Location.complete")
        val startTime = System.currentTimeMillis()
        queryAllAddressIfNeed()
        val completeLocations = if (LocaleUtils.checkAllChinese(query)) {
            completionLocationCN(classifier, query, keywordCache.addressKeywordSet, keywordCache.addressSet)
        } else {
            completionLocationExp(classifier, query, keywordCache.addressKeywordSet, keywordCache.addressSet)
        }
        if (AndesSearchRusConfig.andesSearchConfig?.isPoiSearchEnabled != true || !GProperty.DEBUG_POI_SEARCH_COMPLETION_ENABLE) {
            GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
            return completeLocations
        }
        val poiCompleteLocations = if (LocaleUtils.checkAllChinese(query)) {
            completionLocationCN(classifier, query, keywordCache.poiKeywordSet, keywordCache.poiLocationSet)
        } else {
            completionLocationExp(classifier, query, keywordCache.poiKeywordSet, keywordCache.poiLocationSet)
        }
        completeLocations.addAll(poiCompleteLocations)
        GLog.d(TAG, "[complete] costTime:${GLog.getTime(startTime)}")
        GTrace.traceEnd()

        return completeLocations
    }

    /**
     * 中文补全
     *
     * @param classifier 分类器
     * @param query 单个搜索词
     * @return 补全列表
     */
    @Suppress("LongMethod", "LoopWithTooManyJumpStatements", "NestedBlockDepth")
    private fun completionLocationCN(
        classifier: SearchClassifier,
        query: String,
        keywordSet: MutableSet<String>?,
        addressList: MutableList<Array<String?>>?
    ):
            MutableList<ShootLocation> {

        var isSingleWord = false
        keywordSet?.let {
            for (address in it) {
                if (address.contains(query)) {
                    isSingleWord = true
                    break
                }
            }
        }

        addressList?.let { addressSet ->
            val completionLocations: MutableList<ShootLocation> = mutableListOf()

            // 地点正则匹配
            val locationSuffixPattern = SearchCommonUtils.REGION_SUFFIX_PATTERN
            // 简化后的关键词
            val simpledKeyword = locationSuffixPattern.matcher(query).replaceAll(TextUtil.EMPTY_STRING).trim()
            if (LOCATION_DEBUG) {
                GLog.d(TAG, "[completionLocationCN] origin query:$query, simpledKeyword:$simpledKeyword")
            }

            // 地址遍历
            var isMatched = false
            val startTime = System.currentTimeMillis()
            for (address in addressSet) {
                if (checkFullAddress(address, isSingleWord, query).not()) {
                    continue
                }

                val startIndex = getAddressLevel()
                for (index in startIndex until address.size) {
                    // 剩下的就是需要处理的关键词
                    var processingKeyword = simpledKeyword
                    val modKeyword = StringBuffer()
                    val matchedKeyword = StringBuffer()
                    val matchedKeywordList: MutableList<Pair<Int, String>> = ArrayList()
                    // 地址去掉空格还是空的，无效地址，跳过
                    if (LOCATION_DEBUG) {
                        GLog.d(TAG, "[completionLocationCN] address[index]:${address[index]}")
                    }
                    val addr = address[index]
                    if (addr == null || addr.trim().isEmpty()) {
                        continue
                    }
                    if (isSingleWord) {
                        // 地址包含查询词， 表示匹配到
                        isMatched = addr.lowercase(Locale.getDefault()).contains(query)
                        if (isMatched) {
                            // 地点中省的名称与市的名称相同
                            matchedKeyword.append(addr + TextUtil.BLANK_STRING)
                            matchedKeywordList.add(Pair(index, addr))
                        }
                    } else {
                        if (LOCATION_DEBUG) {
                            GLog.d(
                                TAG, "[completionLocationCN] simpledAddress:${
                                    locationSuffixPattern.matcher(addr)
                                        .replaceAll(TextUtil.EMPTY_STRING)
                                }"
                            )
                        }
                        val simpledAddress = locationSuffixPattern.matcher(addr)
                            .replaceAll(TextUtil.EMPTY_STRING).trim().lowercase(Locale.getDefault())
                        if (processingKeyword.contains(simpledAddress)) {
                            modKeyword.append("$simpledAddress${TextUtil.BLANK_STRING}")
                            matchedKeyword.append(address[index] + TextUtil.BLANK_STRING)
                            matchedKeywordList.add(Pair(index, address[index]))
                            if (LOCATION_DEBUG) {
                                GLog.d(
                                    TAG,
                                    "[completionLocationCN] processingKeyword:${
                                        processingKeyword.replace(
                                            simpledAddress,
                                            TextUtil.EMPTY_STRING
                                        )
                                    }"
                                )
                            }
                            processingKeyword = processingKeyword.replace(simpledAddress, TextUtil.EMPTY_STRING).trim()

                            if (processingKeyword.isEmpty()) {
                                break
                            }
                        }
                    }
                    isMatched = matchedKeyword.isNotEmpty()
                    if (!isSingleWord && matchedKeyword.isNotEmpty()) {
                        if (LOCATION_DEBUG) {
                            GLog.d(TAG, "[completionLocationCN] query:$query")
                        }
                        var inputKeyword: String = query.trim()
                        inputKeyword = removeKeywords(
                            inputKeyword,
                            matchedKeyword.toString().split(SearchCommonUtils.SPACE_REGEX.toRegex()).toTypedArray()
                        )
                        inputKeyword =
                            removeKeywords(inputKeyword, modKeyword.toString().split(SearchCommonUtils.SPACE_REGEX.toRegex()).toTypedArray())
                        isMatched = inputKeyword.isEmpty()
                    }
                    if (isMatched) {
                        val matchedLocation = ShootLocation()
                        matchedLocation.shootKeywordList = matchedKeywordList
                        matchedLocation.shootKeyword = matchedKeyword.toString().replace(TextUtil.BLANK_STRING.toRegex(), TextUtil.EMPTY_STRING)

                        if (completionLocations.contains(matchedLocation)) {
                            // 省市区完全同名以更小一级为结果。例如province和city都是莫斯科市，返回city一级。
                            val index = completionLocations.indexOf(matchedLocation)
                            completionLocations[index].shootKeywordList = matchedLocation.shootKeywordList
                        } else {
                            completionLocations.add(matchedLocation)
                        }
                    }
                }
            }
            GLog.d(TAG, "[completionLocationCN] foreach albumSet costTime:${GLog.getTime(startTime)}")

            // 针对香港澳门搜索的特殊处理
            classifier.locationFilter.addSpecialKeyword(query, completionLocations, keywordCache.addressKeywordSet)

            return completionLocations
        } ?: return mutableListOf()
    }

    /**
     * 外语补全
     *
     * @param classifier 分类器
     * @param query 单个搜索词
     * @return 补全列表
     */
    @Suppress("LongMethod", "NestedBlockDepth")
    private fun completionLocationExp(
        classifier: SearchClassifier,
        query: String,
        keywordSet: MutableSet<String>?,
        addressList: MutableList<Array<String?>>?
    ): MutableList<ShootLocation> {

        var isSingleWord = false
        keywordSet?.let {
            for (address in it) {
                if (classifier.contains(address, query)) {
                    isSingleWord = true
                }
            }
        }
        addressList?.let { addressSet ->
            var isMatched = false
            val completionLocations: MutableList<ShootLocation> = ArrayList()
            val startTime = System.currentTimeMillis()
            for (address in addressSet) {
                if (checkFullAddress(address, isSingleWord, query).not()) {
                    continue
                }

                val matchedKeyword = StringBuffer()
                val matchedKeywordList: MutableList<Pair<Int, String>> = ArrayList()

                val startIndex = getAddressLevel()
                for (index in startIndex until address.size) {
                    val addr = address[index]
                    if (addr == null || addr.trim().isEmpty()) {
                        continue
                    }
                    isMatched = classifier.contains(addr.lowercase(Locale.getDefault()), query)
                    if (isMatched) {
                        if (matchedKeyword.indexOf(addr) < 0) {
                            matchedKeyword.append(address[index] + TextUtil.BLANK_STRING)
                            matchedKeywordList.add(Pair(index, address[index]))
                        }
                        break
                    }
                }
                if (isMatched) {
                    val matchedLocation = ShootLocation()
                    matchedLocation.shootKeywordList = matchedKeywordList
                    matchedLocation.shootKeyword = matchedKeyword.toString().trim()
                    if (!completionLocations.contains(matchedLocation)) {
                        completionLocations.add(matchedLocation)
                    }
                }
            }
            GLog.d(TAG, "[completionLocationExp] foreach albumSet costTime:${GLog.getTime(startTime)}")
            return completionLocations
        } ?: return mutableListOf()
    }

    private fun isSingleWord(classifier: SearchClassifier, query: String): Boolean {
        // 在地址缓存中的某个地址已经包含此搜索词
        keywordCache.addressKeywordSet?.let {
            for (address in it) {
                if (classifier.contains(address, query)) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 如果有必要的话从数据库查询所有地点信息
     */
    private fun queryAllAddressIfNeed() {
        val startTime = System.currentTimeMillis()
        // 缓存中地址列表为空，则从数据库中执行查询来获取
        if (keywordCache.addressSet.isNullOrEmpty()) {
            GLog.d(TAG, "[queryAllAddressIfNeed], address cache is null, now update location cache!")
            keywordCacheManager.updateLocationCache(false)
        }
        if (keywordCache.poiLocationSet.isNullOrEmpty()) {
            keywordCacheManager.updatePoiCache()
        }
        GLog.d(TAG, "[queryAllAddressIfNeed] costTime:${GLog.getTime(startTime)}")
    }

    /**
     * 全称地址检查
     *
     * @param address 多级地址列表
     * @param isSingleWord
     * @param query 分词后的单个查询词
     * @return
     */
    private fun checkFullAddress(address: Array<String?>, isSingleWord: Boolean, query: String): Boolean {
        // 先过滤掉不包含关键词的地址，优化搜索效率
        val fullAddress = address[GeoDBHelper.LEVEL_ADDRESS]
        // 不合法的地址，跳过
        if (fullAddress.isNullOrEmpty()) {
            return false
        }
        // fullAddress不包含这个搜索关键词, 跳过
        if (isSingleWord && fullAddress.lowercase(Locale.getDefault()).contains(query).not()) {
            return false
        }
        return true
    }

    fun getAddressLevel(): Int {
        var addressLevel = GeoDBHelper.LEVEL_COUNTRY
        if (isNeedFilterCountryAndProvince) {
            addressLevel = GeoDBHelper.LEVEL_CITY
        }
        return addressLevel
    }

    private fun removeKeywords(src: String, keywords: Array<String>?): String {
        if (keywords.isNullOrEmpty()) return src
        var input = src
        keywords.forEach { keyword ->
            if (TextUtils.isEmpty(keyword) || keyword.trim().isEmpty()) {
                return@forEach
            }
            input = Pattern.compile(Pattern.quote(keyword.lowercase(Locale.getDefault()))).matcher(
                input.lowercase(
                    Locale.getDefault()
                )
            ).replaceAll(TextUtil.EMPTY_STRING).trim()
        }
        return input.trim()
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}LocationCompletion"
        private const val LOCATION_DEBUG = false

        private const val COUNTRY_IDX = 1
        private const val PROVINCE_IDX = 2
        private const val CITY_IDX = 3
        private const val DISTRICT_IDX = 4
        private const val STREET_IDX = 5
        const val POI_IDX = 6

        private val latestCompleteLocations: MutableList<QueryCompletionEntry> = mutableListOf()

        fun getEntityType(locationString: String): Int {
            latestCompleteLocations.forEach {
                if (it.mCompletedQuery.contains(locationString)) {
                    return it.mEntityType
                }
            }
            return EntityType.ENTITY_LOCATION_MASK
        }

        fun covertLocationTypeToEntityType(locationType: Int?): Int {
            if (locationType == null) return EntityType.ENTITY_LOCATION_MASK
            return when (locationType) {
                LocationType.ENTITY_ADDRESS -> EntityType.ENTITY_ADDRESS
                LocationType.ENTITY_COUNTRY -> EntityType.ENTITY_COUNTRY
                LocationType.ENTITY_PROVINCE -> EntityType.ENTITY_PROVINCE
                LocationType.ENTITY_CITY -> EntityType.ENTITY_CITY
                LocationType.ENTITY_DISTRICT -> EntityType.ENTITY_DISTRICT
                LocationType.ENTITY_STREET -> EntityType.ENTITY_STREET
                LocationType.ENTITY_LOCATION_MASK -> EntityType.ENTITY_LOCATION_MASK
                LocationType.ENTITY_POI -> EntityType.ENTITY_POI
                else -> EntityType.ENTITY_LOCATION_MASK
            }
        }

        private fun covertEntityTypeToLocationType(entityType: Int): Int {
            return when (entityType) {
                EntityType.ENTITY_ADDRESS -> LocationType.ENTITY_ADDRESS
                EntityType.ENTITY_COUNTRY -> LocationType.ENTITY_COUNTRY
                EntityType.ENTITY_PROVINCE -> LocationType.ENTITY_PROVINCE
                EntityType.ENTITY_CITY -> LocationType.ENTITY_CITY
                EntityType.ENTITY_DISTRICT -> LocationType.ENTITY_DISTRICT
                EntityType.ENTITY_STREET -> LocationType.ENTITY_STREET
                EntityType.ENTITY_LOCATION_MASK -> LocationType.ENTITY_LOCATION_MASK
                EntityType.ENTITY_POI -> LocationType.ENTITY_POI
                else -> LocationType.ENTITY_STREET
            }
        }
    }
}