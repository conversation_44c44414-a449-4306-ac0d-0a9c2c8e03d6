/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ShootPerson.kt
 * Description: ShootPerson
 * Version: 1.0
 * Date: 2023/8/7
 * Author: W9009912
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * W9009912      2023/8/7      1.0              create file
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.search.entry

/**
 * 拍摄人物
 */
class ShootPerson : Comparable<ShootPerson> {
    @JvmField
    var groupId = 0

    @JvmField
    var shootKeyword: String? = null

    override operator fun compareTo(entry: ShootPerson): Int {
        return -Math.signum((groupId - entry.groupId).toFloat()).toInt()
    }

    override fun equals(obj: Any?): Boolean {
        return super.equals(obj)
    }

    override fun hashCode(): Int {
        return super.hashCode()
    }
}