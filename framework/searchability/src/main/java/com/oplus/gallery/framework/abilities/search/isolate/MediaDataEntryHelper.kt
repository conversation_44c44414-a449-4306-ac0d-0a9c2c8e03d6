/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - MediaDataEntryHelper
 ** Description: 查询融合搜索kit所需MediaDataEntry的构造类.
 ** Version: 1.0
 ** Date : 2023/10/9
 ** Author: Wen<PERSON>.<EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wenhao.Deng   2023/10/9      1.0        created
 ***************************************************************/
package com.oplus.gallery.framework.abilities.search.isolate

import android.content.ContentValues
import android.database.Cursor
import android.provider.BaseColumns._ID
import android.util.ArraySet
import com.oplus.andes.photos.kit.search.common.DataObserver
import com.oplus.andes.photos.kit.search.data.AndesMediaDataEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.COLUMN_SCENE_ID
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine
import com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.GEO_ROUTE
import com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.LOCAL_MEDIA
import com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.MEM_SET
import com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.MEM_SETMAP
import com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.SCAN_FACE
import com.oplus.gallery.foundation.database.statistics.StatisticsConstant.TableName.SCAN_LABEL
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.FestivalColumns.DATE
import com.oplus.gallery.foundation.database.store.GalleryStore.FestivalColumns.FESTIVAL_NAME
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns.MD5
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DATA
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DAY
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.GPS_KEY
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.BUCKET_NAME
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_UNKNOW
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MONTH
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.YEAR
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.CSHOT_ID
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.IS_TRASHED
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.IS_FAVORITE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.CARD_CASE_TYPE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.ORIGINAL_DATA
import com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.ADDRESS
import com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.CITY
import com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.DISTRICT
import com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.PROVINCE
import com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.COUNTRY
import com.oplus.gallery.foundation.database.store.GalleryStore.GeoColumns.STREET
import com.oplus.gallery.foundation.database.store.GalleryStore.MemoriesSetColumns.NAME
import com.oplus.gallery.foundation.database.store.GalleryStore.MemoriesSetColumns.THEME
import com.oplus.gallery.foundation.database.store.GalleryStore.MemoriesSetmapColumns.SET_ID
import com.oplus.gallery.foundation.database.store.GalleryStore.OcrPagesColumns.CONTENT
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.GROUP_ID
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.GROUP_NAME
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.HAS_BIG_FACE
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.SCORE
import com.oplus.gallery.foundation.database.util.SQLGrammar.AND
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.DOT
import com.oplus.gallery.foundation.database.util.SQLGrammar.EQUAL
import com.oplus.gallery.foundation.database.util.SQLGrammar.FROM
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS_WITH_BLANK
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_JOIN
import com.oplus.gallery.foundation.database.util.SQLGrammar.LIMIT
import com.oplus.gallery.foundation.database.util.SQLGrammar.ON
import com.oplus.gallery.foundation.database.util.SQLGrammar.OR
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.SELECT
import com.oplus.gallery.foundation.database.util.SQLGrammar.WHERE
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.RawQueryReq
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value.FESTIVAL
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_SEARCH
import com.oplus.gallery.foundation.util.ext.isNullOrEmpty
import com.oplus.gallery.foundation.util.ext.safeUse
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_1
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.Arrays
import java.util.regex.Pattern
import java.util.stream.Collectors

/**
 * 查询融合搜索kit所需MediaDataEntry的构造类.
 */
object MediaDataEntryHelper {

    private const val TAG = "${SearchConst.TAG}MediaDataEntryHelper"

    /**
     * 同步是个后台任务，比较占资源，所以取200分页降低优先级
     */
    private const val PAGE_SIZE = 200

    private var mediaTableQueryCollection = TableQueryCollection()
    private var geoRouteTableQueryCollection = TableQueryCollection()
    private var scanFaceTableQueryCollection = TableQueryCollection()
    private var scanLabelTableQueryCollection = TableQueryCollection()
    private val memoriesSetmapTabQueryCollection = TableQueryCollection()

    /**
     * local_media表查询语句构建
     * 根据selection构建 WHERE 默认为 media_id = ?
     * selectionArgs和values构建 替换占位符的参数
     * 删除{media_id,_data,year,month,day,bucket_id,bucket_display_name,media_type,datetaken,gps_key}
     */
    private fun mediaTabQuerySetBuild(
        selection: String?,
        selectionArgs: Array<String>?,
        values: ContentValues?,
        operation: DataObserver.Operation
    ) {
        val projection: Array<String> = arrayOf(
            _ID, MEDIA_ID, DATA, YEAR, MONTH, DAY, BUCKET_ID, BUCKET_NAME, MEDIA_TYPE, DATE_TAKEN, INVALID,
            GPS_KEY, DISPLAY_NAME, CSHOT_ID, IS_TRASHED, IS_FAVORITE, CARD_CASE_TYPE, MD5, ORIGINAL_DATA
        )
        var where = selection
        var whereArgs = selectionArgs
        val columnKeys = arrayOf(MEDIA_ID, DATA, ORIGINAL_DATA)
        // 如果更新条件不包含 media_id ,则需要从values中获取对应的数据
        if ((selection?.contains(MEDIA_ID) != true)) {
            values?.apply {
                columnKeys.forEach { key ->
                    getAsString(key)?.let {
                        where = "$key = ?"
                        whereArgs = arrayOf(it)
                        return@forEach
                    }
                }
            }
        }
        where?.let {
            mediaTableQueryCollection.offAllCollection(it, whereArgs, projection, operation)
        } ?: run {
            GLog.w(TAG, "[mediaTabQuerySetBuild] selection is null")
        }
    }

    /**
     * geo_route表查询语句构建
     * 根据selection构建 WHERE 默认为 gps_key = ?
     * selectionArgs和values构建 替换占位符的参数
     * projection为返回的列 {gps_key,country,province,city,district,street,address}
     */
    private fun geoRouteTabQuerySetBuild(
        selection: String?,
        selectionArgs: Array<String>?,
        values: ContentValues?,
        operation: DataObserver.Operation
    ) {
        val projection: Array<String> = arrayOf(GPS_KEY, COUNTRY, PROVINCE, CITY, DISTRICT, STREET, ADDRESS)
        var where = selection
        var whereArgs = selectionArgs
        if (selection.isNullOrEmpty()) {
            values?.apply {
                getAsString(GPS_KEY)?.let {
                    where = "$GPS_KEY = ?"
                    whereArgs = arrayOf(it)
                }
            }
        }
        where?.let {
            geoRouteTableQueryCollection.offAllCollection(it, whereArgs, projection, operation)
        } ?: run {
            GLog.w(TAG, "[geoRouteTabQuerySetBuild] selection is null")
        }
    }

    /**
     * 回忆图集查询语句构建
     * SELECT    _data, set_id, name
     * FROM      memories_setmap
     * LEFT JOIN memories_set ON (memories_setmap.set_id = memories_set._id)
     * WHERE     data = ?
     */
    private fun memoriesSetmapTabQuerySetBuild(
        selection: String?,
        selectionArgs: Array<String>?,
        values: ContentValues?,
        operation: DataObserver.Operation
    ) {
        var where = selection
        val querySql = StringBuilder()
        querySql.append(
            (((SELECT + DATA) + COMMA + SET_ID) + COMMA + NAME)
        )
        querySql.append(
            (((FROM + MEM_SETMAP + LEFT_JOIN + MEM_SET + ON +
                    LEFT_BRACKETS_WITH_BLANK + MEM_SETMAP + DOT + SET_ID +
                    EQUAL) + MEM_SET + DOT + _ID) + RIGHT_BRACKETS)
        )
        where?.let {
            if (selectionArgs != null) {
                for (i in selectionArgs.indices) {
                    where = String.format(it.replaceFirst("?", "'%s'"), selectionArgs[i])
                }
            }
            querySql.append(WHERE).append(where)
        } ?: {
            val setId = values?.getAsString(SET_ID)
            querySql.append(AND + MEM_SETMAP + DOT + SET_ID + EQUAL).append(setId)
        }
        memoriesSetmapTabQueryCollection.offAllCollection(querySql.toString(), null, null, operation)
    }

    /**
     * scan_face表查询语句构建
     * 根据selection构建 WHERE 默认为 _data = ?
     * selectionArgs和values构建 替换占位符的参数
     * projection为返回的列 {_data,group_id,group_name,has_big_face,score}
     */
    private fun scanFaceTabQuerySetBuild(
        selection: String?,
        selectionArgs: Array<String>?,
        values: ContentValues?,
        operation: DataObserver.Operation
    ) {
        val projection = arrayOf(DATA, GROUP_ID, GROUP_NAME, HAS_BIG_FACE, SCORE)
        var where = selection
        var whereArgs = selectionArgs
        if (selection.isNullOrEmpty()) {
            values?.apply {
                getAsString(DATA)?.let {
                    where = "$DATA= ?"
                    whereArgs = arrayOf(it)
                }
            }
        }
        where?.let {
            scanFaceTableQueryCollection.offAllCollection(it, whereArgs, projection, operation)
        } ?: run {
            GLog.w(TAG, "[scanFaceTabQuerySetBuild] selection is null")
        }
    }

    /**
     * scan_label表查询语句构建
     * 根据selection构建 WHERE 默认为 _data = ?
     * selectionArgs和values构建 替换占位符的参数
     * projection为返回的列 {_data,scene_id,score}
     */
    private fun scanLabelTabQuerySetBuild(
        selection: String?,
        selectionArgs: Array<String>?,
        values: ContentValues?,
        operation: DataObserver.Operation
    ) {
        val projection = arrayOf(DATA, COLUMN_SCENE_ID, SCORE)
        var where = selection
        var whereArgs = selectionArgs
        if (selection.isNullOrEmpty()) {
            values?.apply {
                getAsString(DATA)?.let {
                    where = "$DATA= ?"
                    whereArgs = arrayOf(it)
                }
            }
        }
        where?.let {
            scanLabelTableQueryCollection.offAllCollection(it, whereArgs, projection, operation)
        } ?: run {
            GLog.w(TAG, "[scanLabelTabQuerySetBuild] selection is null")
        }
    }

    @Suppress("LongMethod")
    private fun cursorBuildByQueryReq(
        table: String,
        tableType: Int,
        tableQueryCollection: TableQueryCollection,
        operation: DataObserver.Operation
    ): List<AndesMediaDataEntry> {
        val mediaDataEntry = mutableListOf<AndesMediaDataEntry>()
        val startTime = System.currentTimeMillis()
        runCatching {
            if (operation == DataObserver.Operation.DELETE) {
                tableQueryCollection.tableDeleteIDList.forEach {
                    if (DEBUG) {
                        GLog.d(TAG, "[cursorBuildByQueryReq] DELETE _id:$it")
                    }
                    mediaDataEntry.add(
                        AndesMediaDataEntry(
                            it.toInt(),
                            -1,
                            -1,
                            TextUtil.EMPTY_STRING,
                            TextUtil.EMPTY_STRING,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            -1,
                            -1,
                            -1,
                            -1,
                            -1,
                            null,
                            null
                        )
                    )
                }
                tableQueryCollection.tableDeleteIDList.clear()
                return mediaDataEntry
            } else {
                tableQueryCollection.getQueryReqByOperation(operation) { selection, whereArgs, projection ->
                    if (DEBUG) {
                        GLog.d(TAG, "cursorBuildByQueryReq ${operation.name} TABLE $table WHERE:[$selection] ")
                    }
                    QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(tableType)
                        .setProjection(Arrays.copyOf(projection.toArray(), projection.size, Array<String>::class.java))
                        .setWhere(selection.toString())
                        .setWhereArgs(Arrays.copyOf(whereArgs.toArray(), whereArgs.size, Array<String>::class.java))
                        .setConvert(CursorConvert())
                        .build().exec()?.safeUse {
                            mediaDataEntry.addAll(queryReqToMediaDataEntry(table, it))
                        } ?: run {
                        GLog.w(TAG, "[cursorBuildByQueryReq] Cursor is null")
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, "[cursorBuildByQueryReq] tableType:$table cursor build failed!", it)
        }
        if (DEBUG) {
            GLog.d(TAG, "[cursorBuildByQueryReq] cost:${GLog.getTime(startTime)},size:${mediaDataEntry.size}")
        }
        return mediaDataEntry
    }

    private fun cursorBuildByRawQueryReq(): List<AndesMediaDataEntry> {
        val mediaDataEntry = mutableListOf<AndesMediaDataEntry>()
        val startTime = System.currentTimeMillis()
        runCatching {
            memoriesSetmapTabQueryCollection.tableUpdateSelectionList.forEach { querySql ->
                if (DEBUG_SEARCH) {
                    GLog.d(TAG, "[cursorBuildByRawQueryReq] querySql:$querySql")
                }
                RawQueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setQuerySql(querySql)
                    .setConvert(CursorConvert())
                    .build().exec()?.safeUse {
                        mediaDataEntry.addAll(queryReqToMediaDataEntry(GalleryStore.MemoriesSetmap.TAB, it))
                    } ?: run {
                    GLog.e(TAG, "[cursorBuildByRawQueryReq] cursor is null")
                }
            }
        }.onFailure {
            GLog.e(TAG, "[cursorBuildByRawQueryReq] cursor build failed!", it)
        }
        memoriesSetmapTabQueryCollection.tableUpdateSelectionList.clear()
        if (DEBUG) {
            GLog.d(TAG, "[cursorBuildByRawQueryReq] costTime:${GLog.getTime(startTime)}")
        }
        return mergeMediaDataEntries(mediaDataEntry, AndesMediaDataEntry::data)
    }

    private fun queryReqToMediaDataEntry(
        table: String,
        cursor: Cursor
    ): List<AndesMediaDataEntry> {
        val mediaDataEntryList: MutableList<AndesMediaDataEntry> = mutableListOf()
        runCatching {
            if (cursor.count == 0) {
                GLog.w(TAG, "[queryReqToMediaDataEntry] Query $table no result!")
                return mediaDataEntryList
            }
            mediaDataEntryList.addAll(createEntryListFromCursor(cursor))
            return if (table == GalleryStore.GeoRoute.TAB) {
                mergeMediaDataEntries(mediaDataEntryList, AndesMediaDataEntry::gpsKey)
            } else {
                mergeMediaDataEntries(mediaDataEntryList, AndesMediaDataEntry::data)
            }
        }.onFailure {
            GLog.e(TAG, "[queryReqToMediaDataEntry] Query $table failed! ", it)
        }
        return mediaDataEntryList
    }

    /**
     * 分表查询数据库，构建MediaDataEntry列表，提供给融合搜索kit更新索引
     */
    private fun queryMediaChangeDataToMediaDataEntry(operation: DataObserver.Operation): List<AndesMediaDataEntry?> {
        val startTime = System.currentTimeMillis()
        val mediaDataEntryList: MutableList<AndesMediaDataEntry> = mutableListOf()
        cursorBuildByQueryReq(GalleryStore.GalleryMedia.TAB, GalleryDbDao.TableType.LOCAL_MEDIA, mediaTableQueryCollection, operation).let {
            mediaDataEntryList.addAll(it)
        }
        cursorBuildByQueryReq(GalleryStore.GeoRoute.TAB, GalleryDbDao.TableType.GEO_ROUTE, geoRouteTableQueryCollection, operation).let {
            mediaDataEntryList.addAll(it)
        }
        cursorBuildByRawQueryReq().let {
            mediaDataEntryList.addAll(it)
        }
        cursorBuildByQueryReq(GalleryStore.ScanFace.TAB, GalleryDbDao.TableType.SCAN_FACE, scanFaceTableQueryCollection, operation).let {
            mediaDataEntryList.addAll(it)
        }
        cursorBuildByQueryReq(GalleryStore.ScanLabel.TAB, GalleryDbDao.TableType.SCAN_LABEL, scanLabelTableQueryCollection, operation).let {
            mediaDataEntryList.addAll(it)
        }
        GLog.d(
            TAG,
            "[queryMediaChangeDataToMediaDataEntry] " +
                    "operation:${operation.name}, " +
                    "costTime:${GLog.getTime(startTime)}, " +
                    "mediaDataEntryList:${mediaDataEntryList.size}"
        )
        return mediaDataEntryList
    }

    /**
     * 构建querySql，查询所有MediaData
     *
     * SELECT _id,media_id,local_media._data,bucket_id,bucket_display_name,year,month,day,local_media.media_type,datetaken,
     *        local_media.gps_key,geo_route.country,province,geo_route.city,district,street,address,name,theme,
     *        scan_face.group_id,group_name,scan_face.score AS face_score,scene_id,scan_label.score AS label_score
     * FROM local_media
     *      LEFT JOIN geo_route ON local_media.gps_key = geo_route.gps_key
     *      LEFT JOIN memories_setmap ON local_media._data = memories_setmap._data
     *      LEFT JOIN memories_set ON memories_set._id = memories_setmap.set_id
     *      LEFT JOIN scan_face ON local_media._data = scan_face._data
     *      LEFT JOIN scan_label ON local_media._data = scan_label._data
     *      LEFT JOIN festival ON local_media.day = festival.date
     */
    @Suppress("LongMethod")
    private fun queryAllMediaDataToMediaDataEntry(): List<AndesMediaDataEntry?> {
        val startTime = System.currentTimeMillis()
        val querySql = StringBuilder()
        querySql.append(
            SELECT + LOCAL_MEDIA + DOT + _ID +
                    COMMA + MEDIA_ID +
                    COMMA + LOCAL_MEDIA + DOT + DATA +
                    COMMA + BUCKET_ID +
                    COMMA + BUCKET_NAME +
                    COMMA + YEAR +
                    COMMA + MONTH +
                    COMMA + DAY +
                    COMMA + DISPLAY_NAME +
                    COMMA + CSHOT_ID +
                    COMMA + IS_TRASHED +
                    COMMA + IS_FAVORITE +
                    COMMA + LOCAL_MEDIA + DOT + INVALID +
                    COMMA + CARD_CASE_TYPE +
                    COMMA + MD5 +
                    COMMA + ORIGINAL_DATA +
                    COMMA + LOCAL_MEDIA + DOT + MEDIA_TYPE +
                    COMMA + DATE_TAKEN +
                    COMMA + LOCAL_MEDIA + DOT + GPS_KEY +
                    COMMA + GEO_ROUTE + DOT + COUNTRY +
                    COMMA + PROVINCE +
                    COMMA + GEO_ROUTE + DOT + CITY +
                    COMMA + DISTRICT +
                    COMMA + STREET +
                    COMMA + ADDRESS +
                    COMMA + NAME +
                    COMMA + THEME +
                    COMMA + SCAN_FACE + DOT + GROUP_ID +
                    COMMA + GROUP_NAME +
                    COMMA + COLUMN_SCENE_ID +
                    COMMA + FESTIVAL + DOT + FESTIVAL_NAME +
                    COMMA + LOCAL_MEDIA + DOT + MIME_TYPE +
                    COMMA + MEM_SETMAP + DOT + SET_ID
        )
        querySql.append(
            (((((((((((((FROM + LOCAL_MEDIA) +
                    LEFT_JOIN + GEO_ROUTE) + ON + LOCAL_MEDIA + DOT + GPS_KEY + EQUAL + GEO_ROUTE + DOT + GPS_KEY) +
                    LEFT_JOIN + MEM_SETMAP + ON + LOCAL_MEDIA + DOT + ORIGINAL_DATA + EQUAL) + MEM_SETMAP + DOT + DATA) +
                    LEFT_JOIN + MEM_SET + ON + MEM_SET + DOT + _ID + EQUAL) + MEM_SETMAP + DOT + SET_ID) +
                    LEFT_JOIN + SCAN_FACE) + ON + LOCAL_MEDIA + DOT + ORIGINAL_DATA + EQUAL + SCAN_FACE + DOT + DATA) +
                    LEFT_JOIN + SCAN_LABEL) + ON + LOCAL_MEDIA + DOT + ORIGINAL_DATA + EQUAL + SCAN_LABEL + DOT + DATA) +
                    LEFT_JOIN + FESTIVAL + ON + LOCAL_MEDIA + DOT + DAY + EQUAL) + FESTIVAL + DOT + DATE)
        )
        GLog.d(TAG, "[queryAllMediaDataToMediaDataEntry] querySql: $querySql")
        val mediaDataEntryList: MutableList<AndesMediaDataEntry> = ArrayList()
        var cursor: Cursor? = null
        var offset = 0
        var continueQuery = true
        while (continueQuery) {
            // 温度检查
            if (TemperatureUtil.isTemperatureAllowContinueScan().not()) {
                GLog.w(TAG, "[queryAllMediaDataToMediaDataEntry] temperature is not allow to sync")
                // 清理掉下次需要重新同步才行，目前中子那边需要做一次全量同步才行，后续看看能否进一步优化
                mediaDataEntryList.clear()
                break
            }
            runCatching {
                val start = System.currentTimeMillis()
                val limit = LIMIT + offset + COMMA + PAGE_SIZE
                cursor = RawQueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.RAW_QUERY)
                    .setQuerySql(querySql.toString() + limit)
                    .setConvert(CursorConvert())
                    .build().exec()
                cursor?.let {
                    if (it.count == 0) {
                        GLog.w(TAG, "[queryAllMediaDataToMediaDataEntry] cursor count is 0!")
                        continueQuery = false
                    } else {
                        offset += it.count
                        mediaDataEntryList.addAll(createEntryListFromCursor(it))
                        if (DEBUG) {
                            GLog.i(TAG) {
                                "[queryAllMediaDataToMediaDataEntry] Query cost time: ${GLog.getTime(start)}, " +
                                    "offset: $offset, entries.size: ${mediaDataEntryList.size}, PAGE_SIZE: $PAGE_SIZE"
                            }
                        }
                    }
                    it.close()
                } ?: run {
                    continueQuery = false
                    GLog.w(TAG, "[queryAllMediaDataToMediaDataEntry] cursor is null!")
                }
            }.onFailure {
                continueQuery = false
                GLog.e(TAG, "[queryAllMediaDataToMediaDataEntry] Query MediaProvider failed! $it")
            }
        }
        IOUtils.closeQuietly(cursor)

        if (mediaDataEntryList.isNullOrEmpty()) {
            return emptyList()
        }

        // 合并,将多个mediaDataEntry合并为一个
        val mergedList = mergeMediaDataEntries(mediaDataEntryList, AndesMediaDataEntry::data)
        GLog.v(
            TAG, "[queryAllMediaDataToMediaDataEntry] costTime:${GLog.getTime(startTime)}, " +
                    "mediaDataEntryList:${mediaDataEntryList.size}, " +
                    "mergedList:${mergedList.size}"
        )
        return mergedList
    }

    private fun <T> mergeMediaDataEntries(
        mediaDataEntryList: MutableList<AndesMediaDataEntry>,
        paramSelector: (AndesMediaDataEntry) -> T
    ): List<AndesMediaDataEntry> {
        val startTime = System.currentTimeMillis()
        val mergeMediaDataEntryList = mutableListOf<AndesMediaDataEntry>()
        // 按照mediaId进行分组
        val groupMapByMediaId: MutableMap<T, List<AndesMediaDataEntry>> =
            mediaDataEntryList.stream().collect(Collectors.groupingBy(paramSelector))
        groupMapByMediaId.forEach { (_, mediaDataEntries) ->
            if (mediaDataEntries.size == NUMBER_1) {
                mergeMediaDataEntryList.addAll(mediaDataEntries)
            } else {
                // 将多个合并为一个
                val personIdList = mutableListOf<String>()
                val personNameList = mutableListOf<String>()
                val memoriesIdList = mutableListOf<String>()
                val memoriesNameList = mutableListOf<String>()
                val labelIdList = mutableListOf<String>()
                val labelList = mutableListOf<String>()
                val festivalNameSet = mutableSetOf<String>()

                // data相同，随便取一个就行
                val mergedAndesMediaDataEntry: AndesMediaDataEntry = mediaDataEntries[0]
                mediaDataEntries.forEach { mediaDataEntry ->
                    mediaDataEntry.personId?.let { personIdList.addAll(it) }
                    mediaDataEntry.personName?.let { personNameList.addAll(it) }
                    mediaDataEntry.memoriesId?.let { memoriesIdList.addAll(it) }
                    mediaDataEntry.memoriesName?.let { memoriesNameList.addAll(it) }
                    mediaDataEntry.labelId?.let { labelIdList.addAll(it) }
                    mediaDataEntry.label?.let { labelList.addAll(it) }
                    mediaDataEntry.festival?.let { festivalNameSet.addAll(it) }
                }
                // personName和personId需要一一对应, 压缩为一个map处理
                personIdList.zip(personNameList).associate { it.first to it.second }.let { map ->
                    mergedAndesMediaDataEntry.personId = map.keys.toMutableList()
                    mergedAndesMediaDataEntry.personName = map.values.toMutableList()
                }
                labelIdList.zip(labelList).associate { it.first to it.second }.let { map ->
                    mergedAndesMediaDataEntry.labelId = map.keys.toMutableList()
                    mergedAndesMediaDataEntry.label = map.values.toMutableList()
                }
                memoriesIdList.zip(memoriesNameList).associate { it.first to it.second }.let { map ->
                    mergedAndesMediaDataEntry.memoriesId = map.keys.toMutableList()
                    mergedAndesMediaDataEntry.memoriesName = map.values.toMutableList()
                }
                mergedAndesMediaDataEntry.festival = festivalNameSet.toMutableList()
                mergeMediaDataEntryList.add(mergedAndesMediaDataEntry)
            }
        }
        if (DEBUG) {
            GLog.v(TAG, "[mergeMediaDataEntries] costTime:${GLog.getTime(startTime)}")
        }
        return mergeMediaDataEntryList
    }

    private fun createEntryListFromCursor(cursor: Cursor): MutableList<AndesMediaDataEntry> {
        val mediaDataEntryList = mutableListOf<AndesMediaDataEntry>()
        while (cursor.moveToNext()) {
            val sceneId = cursor.safeGet(COLUMN_SCENE_ID, LabelSearchEngine.INVALID_ID)
            val labelNames = mutableListOf<String>()
            if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(ContextGetter.context)) {
                labelNames.add(LabelGraphSearchAbility.queryLabelNameByLabelId(sceneId))
            } else {
                val labelList = LabelDictionary.getLabelNames(sceneId)
                labelNames.addAll(if (labelList.isNullOrEmpty()) listOf(TextUtil.EMPTY_STRING) else labelList)
            }
            if (labelNames.isEmpty()) {
                labelNames.add(TextUtil.EMPTY_STRING)
            }
            var sceneIdList: List<String>? = null
            if (sceneId != LabelSearchEngine.INVALID_ID) {
                sceneIdList = listOf(sceneId.toString())
            }
            val mediaDataEntry = AndesMediaDataEntry(
                cursor.safeGet(_ID, -1),
                cursor.safeGet(MEDIA_ID, -1),
                cursor.safeGet(MEDIA_TYPE, MEDIA_TYPE_UNKNOW),
                cursor.safeGet(MIME_TYPE, TextUtil.EMPTY_STRING),
                cursor.safeGet(DATA, TextUtil.EMPTY_STRING),
                cursor.safeGet(DISPLAY_NAME, TextUtil.EMPTY_STRING),
                cursor.safeGet(YEAR, TextUtil.EMPTY_STRING),
                cursor.safeGet(MONTH, TextUtil.EMPTY_STRING),
                cursor.safeGet(DAY, TextUtil.EMPTY_STRING),
                cursor.safeGet(DATE_TAKEN, 0L),
                listOf(cursor.safeGet(FESTIVAL_NAME, TextUtil.EMPTY_STRING)),
                cursor.safeGet(BUCKET_ID, TextUtil.EMPTY_STRING),
                cursor.safeGet(BUCKET_NAME, TextUtil.EMPTY_STRING),
                listOf(cursor.safeGet(GROUP_ID, TextUtil.EMPTY_STRING)),
                listOf(cursor.safeGet(GROUP_NAME, TextUtil.EMPTY_STRING)),
                cursor.safeGet(GPS_KEY, 0L),
                cursor.safeGet(COUNTRY, TextUtil.EMPTY_STRING),
                cursor.safeGet(PROVINCE, TextUtil.EMPTY_STRING),
                cursor.safeGet(CITY, TextUtil.EMPTY_STRING),
                cursor.safeGet(DISTRICT, TextUtil.EMPTY_STRING),
                cursor.safeGet(STREET, TextUtil.EMPTY_STRING),
                cursor.safeGet(ADDRESS, TextUtil.EMPTY_STRING),
                listOf(cursor.safeGet(CONTENT, TextUtil.EMPTY_STRING)),
                listOf(cursor.safeGet(SET_ID, TextUtil.EMPTY_STRING)),
                listOf(cursor.safeGet(NAME, TextUtil.EMPTY_STRING)),
                sceneIdList,
                labelNames,
                cursor.safeGet(CSHOT_ID, -1),
                cursor.safeGet(IS_TRASHED, -1),
                cursor.safeGet(INVALID, -1),
                cursor.safeGet(IS_FAVORITE, -1),
                cursor.safeGet(CARD_CASE_TYPE, -1),
                cursor.safeGet(MD5, TextUtil.EMPTY_STRING),
                cursor.safeGet(ORIGINAL_DATA, TextUtil.EMPTY_STRING),
            )
            mediaDataEntryList.add(mediaDataEntry)
        }
        cursor.close()
        return mediaDataEntryList
    }

    private inline fun <reified T> Cursor.safeGet(columnName: String, defaultValue: T): T {
        val index = getColumnIndex(columnName)
        if (index < 0) {
            return defaultValue
        }
        return when (T::class) {
            Int::class -> (getInt(index) as? T) ?: defaultValue
            String::class -> (getString(index) as? T) ?: defaultValue
            Long::class -> (getLong(index) as? T) ?: defaultValue
            Float::class -> (getFloat(index) as? T) ?: defaultValue
            else -> defaultValue
        }
    }

    /**
     * 执行查表返回同步给融合搜索Kit所需MediaDataEntry列表
     *
     * @param operation 查询操作类型
     * @return 融合搜索Kit所需MediaDataEntry列表
     */
    @JvmStatic
    fun queryMediaDataToMediaDataEntry(operation: DataObserver.Operation): List<AndesMediaDataEntry?> {
        return if (operation == DataObserver.Operation.SYNCHRONIZE_ALL) {
            queryAllMediaDataToMediaDataEntry()
        } else {
            queryMediaChangeDataToMediaDataEntry(operation)
        }
    }

    /**
     * 查询语句构建：
     * 通过表名区分，构建相应的查询语句，保留在查询语句列表中
     */
    @JvmStatic
    fun queryReqBuild(
        table: String,
        selection: String?,
        selectionArgs: Array<String>?,
        values: ContentValues?,
        operation: DataObserver.Operation
    ) {
        when (table) {
            GalleryStore.GalleryMedia.TAB -> mediaTabQuerySetBuild(selection, selectionArgs, values, operation)
            GalleryStore.GeoRoute.TAB -> geoRouteTabQuerySetBuild(selection, selectionArgs, values, operation)
            GalleryStore.MemoriesSetmap.TAB -> memoriesSetmapTabQuerySetBuild(selection, selectionArgs, values, operation)
            GalleryStore.ScanFace.TAB -> scanFaceTabQuerySetBuild(selection, selectionArgs, values, operation)
            GalleryStore.ScanLabel.TAB -> scanLabelTabQuerySetBuild(selection, selectionArgs, values, operation)
        }
    }

    /**
     * 根据不同tabType,不同数据库操作构建不同查询条件类，有3个list
     * @param tableUpdateSelectionList where语句集合
     * @param tableUpdateSelectionArgsList 占位符所需参数集合
     * @param tableUpdateProjectionSet 查询返回列的集合(返回列不应该重复，使用set去重)
     */
    private class TableQueryCollection(
        val tableUpdateSelectionList: ArrayList<String> = arrayListOf(),
        val tableUpdateSelectionArgsList: ArrayList<String> = arrayListOf(),
        val tableUpdateProjectionSet: ArraySet<String> = ArraySet(),
        val tableDeleteIDList: ArrayList<String> = arrayListOf()
    ) {
        private fun clearAllUpdateList() {
            tableUpdateSelectionList.clear()
            tableUpdateSelectionArgsList.clear()
            tableUpdateProjectionSet.clear()
        }

        /**
         * 删除数据时，传递的查询条件为 _id IN ("2","3") AND _id IN ("47")。
         * 先匹配所有的_id IN的子句，
         * 每个子句中再匹配所有数字，累计所有id到tableDeleteIDList中
         */
        private fun offerDeleteCollection(where: String) {
            val idInMatcher = idInValuesPattern.matcher(where)
            while (idInMatcher.find()) {
                idInMatcher.group(NUMBER_1)?.let { ids ->
                    val idPattern = quotedNumberPattern
                    val idMatcher = idPattern.matcher(ids)
                    while (idMatcher.find()) {
                        idMatcher.group(NUMBER_1)?.let {
                            tableDeleteIDList.add(it)
                        }
                    }
                }
            }
        }

        private fun offerUpdateCollection(where: String, whereArgs: Array<String>?, projection: Array<String>?) {
            tableUpdateSelectionList.add(where)
            tableUpdateSelectionArgsList.addAll(whereArgs.orEmpty())
            tableUpdateProjectionSet.addAll(projection.orEmpty())
        }

        private fun collectListToSelection(tableSelectionList: ArrayList<String>): String {
            val selection = StringBuilder()
            if (tableSelectionList.isEmpty().not()) {
                tableSelectionList.forEach {
                    selection.append(it).append(OR)
                }
                if (selection.endsWith(OR)) {
                    selection.deleteRange(selection.lastIndexOf(OR), selection.length)
                }
            }
            return selection.toString()
        }

        /**
         * 单次查询条件根据操作类型，累计在对应list中
         */
        fun offAllCollection(where: String, whereArgs: Array<String>?, projection: Array<String>?, operation: DataObserver.Operation) {
            when (operation) {
                DataObserver.Operation.DELETE -> offerDeleteCollection(where)
                DataObserver.Operation.UPDATE -> offerUpdateCollection(where, whereArgs, projection)
                else -> GLog.w(TAG, "[offAllCollection] Unexpected operation")
            }
        }

        /**
         * 拼接对应操作list的查询条件
         */
        fun getQueryReqByOperation(
            operation: DataObserver.Operation,
            queryReq: (selection: String?, whereArgs: ArrayList<String>, projection: ArraySet<String>) -> Unit
        ) {
            when (operation) {
                DataObserver.Operation.UPDATE -> {
                    collectListToSelection(tableUpdateSelectionList).takeIf { it.isNotBlank() }?.let {
                        queryReq(it, tableUpdateSelectionArgsList, tableUpdateProjectionSet)
                        clearAllUpdateList()
                    }
                }

                else -> GLog.w(TAG, "[collectQueryByOperation] Unexpected operation")
            }
        }

        companion object {
            private val idInValuesPattern = Pattern.compile("_id IN \\((.*?)\\)")
            private val quotedNumberPattern = Pattern.compile("\"(\\d+)\"")
        }
    }
}