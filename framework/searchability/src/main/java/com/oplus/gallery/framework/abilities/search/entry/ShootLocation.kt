/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ShootLocation.kt
 * Description: ShootLocation
 * Version: 1.0
 * Date: 2023/8/7
 * Author: ********
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * ********      2023/8/7      1.0              create file
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.search.entry

import android.util.Pair

/**
 * 补全命中的地点信息
 */
class ShootLocation {
    @JvmField
    var shootKeyword: String? = null

    @JvmField
    var shootKeywordList: MutableList<Pair<Int, String>>? = arrayListOf()

    override fun equals(other: Any?): Boolean {
        if (other != null && other is ShootLocation) {
            return shootKeyword == other.shootKeyword
        }
        return false
    }

    override fun hashCode(): Int {
        return shootKeyword.hashCode()
    }

    override fun toString(): String {
        return shootKeyword!!
    }
}