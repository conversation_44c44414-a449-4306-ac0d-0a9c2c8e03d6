/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - KeywordCacheManager.kt
 ** Description: Keyword缓存管理
 ** Version: 1.0
 ** Date: 2023/9/20
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/9/20      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cache

import android.content.Context
import android.database.Cursor
import android.text.TextUtils
import com.oplus.andes.photos.kit.search.data.AndesAlbumEntry
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.BACKGROUND_COLLECT_COMPLETE_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.KEYWORD_CACHE_OPEN_URI
import com.oplus.gallery.business_lib.model.data.festival.FestivalSelector
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService
import com.oplus.gallery.business_lib.model.data.location.utils.GeoDBHelper
import com.oplus.gallery.business_lib.model.data.location.utils.PoiCacheService
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.taskscheduling.ext.launch
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.GTrace.traceEnd
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_ALBUM
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_ALL
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_DATE
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_LABEL
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_LOCATION
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_MEMORIES
import com.oplus.gallery.framework.abilities.search.cache.SuggestionCacheHandler.Companion.TASK_PERSON
import com.oplus.gallery.framework.abilities.search.completion.LocationCompletion
import com.oplus.gallery.framework.abilities.search.cvgraph.LabelGraphSearchAbility
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.thread.ThreadPool
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Job
import java.util.Locale
import java.util.concurrent.CopyOnWriteArraySet

/**
 * Keyword缓存管理
 *
 * @property context 上下文对象
 */
internal class KeywordCacheManager(
    private val context: Context,
    private val geoCacheService: GeoCacheService,
    private val festivalSelector: FestivalSelector
) {

    /**
     * keyword缓存
     */
    var keywordCache: KeywordCache = KeywordCache()
        private set

    /**
     * keyword缓存任务
     */
    private var keywordCachingTask: Job? = null

    /**
     * keyword缓存监听器集合
     */
    private var cacheListeners: CopyOnWriteArraySet<CacheListener>? = null

    /**
     * 更新缓存
     *
     * @param taskMask 更新缓存类型Mask
     */
    fun updateCache(taskMask: Int) {
        // 避免启动重复任务
        if ((keywordCachingTask != null) && (keywordCachingTask?.isCancelled == false)) {
            GLog.w(TAG, "[updateCache] keywordCachingTask is already running")
            return
        }

        keywordCachingTask = AppScope.launch(
            IO, CoroutineStart.DEFAULT, KeywordCacheUpdateTask(keywordCache, taskMask)
        ) {
            keywordCache.setIsKeywordLoaded(true)
            notifyCacheReady()
            context.contentResolver.notifyChange(BACKGROUND_COLLECT_COMPLETE_URI, null)
            context.contentResolver.notifyChange(KEYWORD_CACHE_OPEN_URI, null)
            keywordCachingTask = null
            GLog.d(TAG, "[updateCache] KeywordCacheUpdateTask taskMask = $taskMask future done")
        }
    }

    private fun notifyCacheReady() {
        if (LocaleUtils.isChinaMainland(context) || LocaleUtils.isEnglish(context)) {
            cacheListeners?.forEach {
                it.cacheReady()
            }
        }
    }

    /**
     * 清楚keyword cache缓存
     */
    fun clearCache() {
        keywordCache.release()
    }

    /**
     * 更新人物缓存
     */
    fun updatePersonNameMapCache(needNotify: Boolean = true) {
        val oldMap = keywordCache.personKeywordMap
        keywordCache.personKeywordMap = GroupHelper.queryAllPersonIdNameMap()
        if (needNotify && (oldMap != keywordCache.personKeywordMap)) {
            notifyCacheReady()
        }
    }

    /**
     * 更新图集缓存
     */
    fun updateAlbumSetCache(needNotify: Boolean = true) {
        val albumEntries = DBSearchUtils.queryAllAlbumEntry(context)
        for (albumEntry in albumEntries) {
            when (albumEntry.type) {
                AndesAlbumEntry.TYPE_VIDEO_ALBUM,
                AndesAlbumEntry.TYPE_ENHANCE_TEXT_ALBUM,
                AlbumEntry.TYPE_RECYCLE_ALBUM,
                AndesAlbumEntry.TYPE_PORTRAIT_BLUR ->
                    albumEntry.mediaIdEntries = DBSearchUtils.queryMediaIdEntryByVirtualAlbum(albumEntry, false)

                else -> {}
            }
        }
        keywordCache.albumEntrySet = albumEntries
        if (needNotify) {
            notifyCacheReady()
        }
    }

    /**
     * 更新回忆图集缓存
     */
    fun updateMemoriesAlbumCache(needNotify: Boolean = true) {
        keywordCache.memoriesAlbumSet = DBSearchUtils.queryAllMemoriesAlbumEntry(context)
        if (needNotify) {
            notifyCacheReady()
        }
    }

    /**
     * 更新标签ID到标签名的缓存
     */
    fun updateLabelIdToNameCache(needNotify: Boolean = true) {
        if (keywordCache.labelIdToNameMap == null) {
            keywordCache.labelIdToNameMap = mutableMapOf()
        }
        keywordCache.labelIdToNameMap?.clear()
        if (LabelGraphSearchAbility.checkAvailabilityForLabelGraph(context)) {
            LabelGraphSearchAbility.queryAllLabelMap().forEach { (labelId, labelNames) ->
                keywordCache.labelIdToNameMap?.put(labelId, labelNames)
            }
        } else {
            keywordCache.labelIdToNameMap = DBSearchUtils.queryAllLabelsMappingSceneId()
        }
        if (needNotify) {
            notifyCacheReady()
        }
    }

    /**
     * 更新媒体ID到标签ID列表的缓存
     */
    fun updateMediaIdToLabelIdsCache(needNotify: Boolean = true) {
        keywordCache.mediaIdToLabelIdsMap = DBSearchUtils.queryAllLabelsMappingMediaId()
        if (needNotify) {
            notifyCacheReady()
        }
    }

    /**
     * 更新地点缓存
     */
    fun updateLocationCache(needNotify: Boolean = true) {
        keywordCache.addressSet = geoCacheService.allAddress
        keywordCache.addressSet?.let { addressSet ->
            keywordCache.addressKeywordSet = mutableSetOf()
            addressSet.forEach { address ->
                for (index in GeoDBHelper.LEVEL_COUNTRY until address.size) {
                    val addressTrim = address[index]?.trim()
                    if (addressTrim != null && addressTrim.isNotEmpty()) {
                        keywordCache.addressKeywordSet?.add(addressTrim.lowercase(Locale.getDefault()))
                    }
                }
            }
        }
        if (needNotify) {
            notifyCacheReady()
        }
    }

    /**
     * 更新POI地址缓存
     *
     */
    fun updatePoiCache() {
        val poiAddresses = PoiCacheService.getAllPoiAddress()
        keywordCache.poiKeywordSet = mutableSetOf()
        keywordCache.poiLocationSet = mutableListOf()
        poiAddresses.forEach { address ->
            val addressTrim = address.trim()
            if (addressTrim.isNotEmpty()) {
                keywordCache.poiKeywordSet?.add(addressTrim.lowercase(Locale.getDefault()))
                val addressArray = ArrayList<String>()
                addressArray.add(addressTrim.lowercase(Locale.getDefault()))
                for (i in 1 until LocationCompletion.POI_IDX) {
                    addressArray.add(TextUtil.EMPTY_STRING)
                }
                addressArray.add(addressTrim.lowercase(Locale.getDefault()))
                keywordCache.poiLocationSet?.add(addressArray.toTypedArray())
            }
        }
    }

    /**
     * 清空地点缓存
     */
    fun clearLocationCache() {
        keywordCache.addressSet?.clear()
    }

    /**
     * 更新节日缓存
     */
    fun updateFestivalCache(needNotify: Boolean = true) {
        val dateTimeEntries = DBSearchUtils.queryDateTimeEntryForFestivals()
        keywordCache.festivalMap = festivalSelector.getMatchedFestival(dateTimeEntries)
        if (needNotify) {
            notifyCacheReady()
        }
    }

    /**
     * 注册缓存监听器
     *
     * @param listener 监听器
     */
    fun registerCacheListener(listener: CacheListener) {
        if (cacheListeners == null) {
            cacheListeners = CopyOnWriteArraySet()
        }
        cacheListeners?.add(listener)
    }

    /**
     * 注销缓存监听器
     *
     * @param listener 监听器
     */
    fun unregisterCacheListener(listener: CacheListener) {
        cacheListeners?.remove(listener)
        if (cacheListeners?.isEmpty() == true) {
            cacheListeners = null
        }
    }

    /**
     * 缓存监听器
     */
    internal interface CacheListener {
        /**
         * 缓存已经加载好 ready
         */
        fun cacheReady()
    }

    private inner class KeywordCacheUpdateTask(
        private var keywordCache: KeywordCache,
        private var taskMask: Int = TASK_ALL
    ) : ThreadPool.Job<Void?> {

        private fun isJobAbort(jc: ThreadPool.JobContext?): Boolean {
            jc?.let {
                return it.isCancelled
            }
            return false
        }

        override fun run(jc: ThreadPool.JobContext?): Void? {
            if (isJobAbort(jc)) {
                return null
            }
            val startTime = System.currentTimeMillis()
            GTrace.traceBegin("KeywordCacheUpdateTask.run")
            updateDateTimeAndFestivalCache(taskMask, jc)
            updateLocationCache(taskMask, jc)
            updatePersonCache(taskMask, jc)
            updateLabelCache(taskMask, jc)
            updateAlbumCache(taskMask, jc)
            updateMemoriesCache(taskMask, jc)
            GLog.d(TAG, "[run] KeywordCacheUpdateTask costTime:${GLog.getTime(startTime)}")
            traceEnd()
            return null
        }

        private fun updateMemoriesCache(taskMask: Int, jc: ThreadPool.JobContext?) {
            if (taskMask and TASK_MEMORIES != 0) {
                val isAbort: Boolean = isJobAbort(jc)
                if (isAbort) keywordCache.memoriesAlbumSet = null else updateMemoriesAlbumCache(false)
            }
        }

        private fun updateAlbumCache(taskMask: Int, jc: ThreadPool.JobContext?) {
            if (taskMask and TASK_ALBUM != 0) {
                val isAbort: Boolean = isJobAbort(jc)
                if (isAbort) keywordCache.albumEntrySet = null else updateAlbumSetCache(false)
            }
        }

        private fun updateLabelCache(taskMask: Int, jc: ThreadPool.JobContext?) {
            if (taskMask and TASK_LABEL != 0) {
                val isAbort: Boolean = isJobAbort(jc)
                if (isAbort) keywordCache.labelIdToNameMap = null else updateLabelIdToNameCache(false)
                if (isAbort) keywordCache.mediaIdToLabelIdsMap = null else updateMediaIdToLabelIdsCache(false)
            }
        }

        private fun updatePersonCache(taskMask: Int, jc: ThreadPool.JobContext?) {
            if (taskMask and TASK_PERSON != 0) {
                val isAbort: Boolean = isJobAbort(jc)
                if (isAbort) keywordCache.personKeywordMap = null else updatePersonNameMapCache(false)
            }
        }

        private fun updateLocationCache(taskMask: Int, jc: ThreadPool.JobContext?) {
            if (taskMask and TASK_LOCATION != 0) {
                val isAbort: Boolean = isJobAbort(jc)
                if (isAbort) {
                    keywordCache.addressSet?.clear()
                    keywordCache.addressKeywordSet?.clear()
                }
                updateLocationCache(false)
                updatePoiCache()
            }
        }

        private fun updateDateTimeAndFestivalCache(taskMask: Int, jc: ThreadPool.JobContext?) {
            if (taskMask and TASK_DATE != 0) {
                val isAbort: Boolean = isJobAbort(jc)
                // 查询并返回相册里所有媒体文件的年和月
                val yearAndMonthMap: Map<String, MutableList<Int>> = queryYearsAndMonth()
                val yearList = yearAndMonthMap[KEY_YEAR]
                val monthList = yearAndMonthMap[KEY_MONTH]
                keywordCache.yearList = if (isAbort) null else yearList
                keywordCache.monthList = if (isAbort) null else monthList
                if (isAbort) {
                    keywordCache.festivalMap = null
                } else {
                    updateFestivalCache(false)
                }
            }
        }

        /**
         * 查询并返回相册里所有媒体文件的年和月。
         */
        @Suppress("TooGenericExceptionCaught")
        private fun queryYearsAndMonth(): Map<String, MutableList<Int>> {
            val map: MutableMap<String, MutableList<Int>> = HashMap()
            val resultYear: MutableList<Int> = ArrayList()
            val resultMonth: MutableList<Int> = ArrayList()
            var cursor: Cursor? = null
            val projection = arrayOf(LocalColumns.DATE_TAKEN)
            val whereClause = SearchDBHelper.getCShotCoverAndFilterHeifWhere()
            val orderClause = LocalColumns.DATE_TAKEN + " DESC"
            kotlin.runCatching {
                cursor = QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(projection)
                    .setWhere(whereClause)
                    .setOrderBy(orderClause)
                    .setConvert(CursorConvert())
                    .addUriParameter(DISTINCT, LocalColumns.DATE_TAKEN)
                    .build()
                    .exec()
                cursor?.use {
                    while (it.moveToNext()) {
                        val index = it.getColumnIndex(LocalColumns.DATE_TAKEN)
                        val dateTaken = it.getLong(index)
                        val yearText = TimeUtils.getYearDate(dateTaken)
                        val monthText = TimeUtils.getMonthDate(dateTaken)
                        if (!TextUtils.isEmpty(yearText)) {
                            var yearInt = 0
                            try {
                                yearInt = yearText.toInt()
                            } catch (e: NumberFormatException) {
                                GLog.e(TAG, "[queryYearsAndMonth] getYear：NumberFormatException= $e")
                            }
                            if (!resultYear.contains(yearInt)) {
                                resultYear.add(yearInt)
                            }
                        }
                        if (monthText.isNotEmpty()) {
                            var monthInt = 0
                            try {
                                monthInt = monthText.toInt()
                            } catch (e: NumberFormatException) {
                                GLog.e(TAG, "[queryYearsAndMonth] getMonth：NumberFormatException= $e")
                            }
                            if (!resultMonth.contains(monthInt)) {
                                resultMonth.add(monthInt)
                            }
                        }
                    }
                }
            }.onFailure {
                GLog.e(TAG, "queryYearsAndMonthOfPhotoing Query valid years in MediaProvider failed!", it)
            }
            map[KEY_MONTH] = resultMonth
            map[KEY_YEAR] = resultYear
            return map
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}KeywordCacheManager"

        private const val DISTINCT = "DISTINCT"
        private const val KEY_MONTH = "key_month"
        private const val KEY_YEAR = "key_year"
    }
}