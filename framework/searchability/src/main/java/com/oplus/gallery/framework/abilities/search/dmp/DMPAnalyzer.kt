/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DMPAnalyzer
 ** Description: dmp sdk helper for word segmentation
 **
 ** Version: 1.0
 ** Date: 2023/8/2
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2023/8/2     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.dmp

import android.content.Context
import com.oplus.dmp.sdk.InitConfig
import com.oplus.dmp.sdk.SearchManager
import com.oplus.dmp.sdk.analyzer.local.dict.entity.LocalAnalyzerConfigure
import com.oplus.dmp.sdk.analyzer.local.dict.manager.DictManager
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * 中子检索分词sdk的封装
 * 用于初始化sdk、对sdk内部词典注入自定义词典，对自然语句进行词性拆分，根据不同词性（如时间、地点、标签等）将一句话中的各种关键词提取出来
 * 如，一句话“我上个礼拜在深圳市的前海公园露营”，注入了自定义词典后，这句话应当会被分词为：
 * "time"->["上个礼拜"]
 * "location"->["深圳市","前海"】
 * “tags"->["公园","露营"]
 **/
internal object DMPAnalyzer {
    private const val TAG = "DMPAnalyzer"
    private const val VERSION_LOCAL_ANALYZER = 0
    private const val VERSION_DMP_ANALYZER = 1
    private var init = false
    internal val searchManager
        get() = SearchManager.getInstance()

    fun init(context: Context): Boolean {
        init = true
        val versions = intArrayOf(VERSION_DMP_ANALYZER)
        val configure = LocalAnalyzerConfigure.Builder()
            .setAsTermDictUseNerDict(true)
            .setFilterStopWord(false)
            .setCutAll(false)
            .build()
        searchManager.init(context, InitConfig(versions, configure))
        //阻塞以等待DMP初始化完成
        searchManager.analyzer
        return true
    }


    /**
     * 同时更新DMP APP与SDK内部的词典
     *
     * @param customDictMapList 自定义词典列表
     * 映射表的key是词性分类 见[DMPAnalyzer.Category]
     * 映射表的value是词语列表
     */
    internal fun updateCustomDict(customDictMapList: List<Map<String, HashSet<String>>>) {
        val dictNames = mutableListOf<String>()
        val dictContents = mutableListOf<HashSet<String>>()
        customDictMapList.forEach { map ->
            map.forEach { (dictName, dictContent) ->
                dictNames.add(dictName)
                dictContents.add(dictContent)
            }
        }
        searchManager.dmpDictManager.updateDict(dictNames, dictContents)
    }

    /**
     * 融合自定义词典
     * @param customDictMap 自定义词典
     * key: 词语类型，如"time","festival","location"等
     * value: 词语列表
     */
    internal fun mergeCustomDictMap(customDictMap: Map<String, HashSet<String>>) {
        synchronized(DictManager::class.java) {
            val dictManager = DictManager.getInstance()
            val sourceNerMap = dictManager.nonTimeNerDict ?: return
            val sourceTermEntity = dictManager.simpleTermDictEntity ?: return
            val sourceTermDicts = sourceTermEntity.termDict ?: return
            var sourceTermDictMaxLength = sourceTermEntity.maxLength
            val cacheMap = HashMap<String, HashSet<String>>()

            for ((word, dictNames) in sourceNerMap) {
                val existDictNames = ensureDictNames(cacheMap, word)
                existDictNames += dictNames
            }
            for ((dictName, words) in customDictMap) {
                for (word in words) {
                    val existDictNames = ensureDictNames(cacheMap, word)
                    existDictNames += dictName
                }
            }
            sourceNerMap.clear()
            for ((word, dictNames) in cacheMap) {
                sourceNerMap[word] = dictNames.toList()
                sourceTermDicts += word
                sourceTermDictMaxLength = word.length.coerceAtLeast(sourceTermDictMaxLength)
            }
            sourceTermEntity.maxLength = sourceTermDictMaxLength
        }
    }

    /**
     * 确保待嵌入的词语有对应的非空set
     * @param cacheMap 缓存集合
     * @param word 待嵌入的词语
     * @return 词语对应的词典名称set
     */
    private fun ensureDictNames(cacheMap: HashMap<String, HashSet<String>>, word: String): HashSet<String> {
        var existDictNames = cacheMap[word]
        if (existDictNames == null) {
            existDictNames = HashSet()
            cacheMap[word] = existDictNames
        }
        return existDictNames
    }

    /**
     * 拆分词语，将一句自然语句中的关键词语，根据词典拆分成分词列表集合
     * @param query 自然语句
     * 如：“我上个礼拜在深圳的前海公园露营
     * @return 分词词典列表集合
     * 如，上述自然语句会被拆分出：
     * ”time"->["上个礼拜],"location"->["深圳","前海"],"word"->["公园"."露营"]
     */
    fun analyze(query: String?): Map<Category, Set<String>>? {
        if (!init) {
            GLog.w(TAG, "analyze DMP sdk need init!")
            return null
        }
        if (query.isNullOrEmpty()) return null
        val resultSetMap = mutableMapOf<Category, MutableSet<String>>()
        val result = searchManager.analyzer.analyze(query) ?: return null
        val originalResult = result.originalQueryAnalyzeResult
        val analyzedTerms = originalResult.analyzedTerms
        val timerNerInfos = originalResult.timeNerInfos
        fun ensureList(category: Category): MutableSet<String> {
            var set = resultSetMap[category]
            if (set == null) {
                set = mutableSetOf()
                resultSetMap[category] = set
            }
            return set
        }
        if (!analyzedTerms.isNullOrEmpty()) {
            for (resultTerm in analyzedTerms) {
                val word = resultTerm.word
                val tags = resultTerm.tags
                val category = when (tags.firstOrNull()) {
                    Category.Time.value -> Category.Time
                    Category.Festival.value -> Category.Festival
                    Category.Location.value -> Category.Location
                    Category.Tags.value -> Category.Tags
                    else -> Category.Word
                }
                ensureList(category) += word
            }
        }
        if (!timerNerInfos.isNullOrEmpty()) {
            val timeList = ensureList(Category.Time)
            for (timerNerInfo in timerNerInfos) {
                for (item in timerNerInfo.timeNerInfos) {
                    item.entity.let {
                        if (!it.isNullOrEmpty()) {
                            timeList += it
                        }
                    }
                    item.yearRange?.let {
                        timeList += it.first.toString()
                    }
                    item.monthRange?.let {
                        timeList += it.first.toString()
                    }
                    item.dayRange?.let {
                        timeList += it.first.toString()
                    }
                }
            }
        }
        return resultSetMap
    }

    /**
     * 词性分类
     */
    enum class Category(val value: String) {
        /**
         * 普通/未知词性
         */
        Word("word"),

        /**
         * 日期词语
         */
        Time("time"),

        /**
         * 节日词语
         */
        Festival("festival"),

        /**
         * 地点词语
         */
        Location("location"),

        /**
         * 标签/特殊词语
         */
        Tags("tags"),

        /**
         * 敏感词
         */
        Sensitive("sensitive"),

        /**
         *  场景
         */
        Scene("scene")
    }
}