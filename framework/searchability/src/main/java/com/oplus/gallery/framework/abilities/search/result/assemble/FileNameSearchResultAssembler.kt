/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FileNameSearchResultAssembler.kt
 ** Description:文件名搜索结果拼装器
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result.assemble

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.entry.SearchEntry
import com.oplus.gallery.framework.abilities.search.result.SingleQueryResult
import com.oplus.gallery.framework.abilities.search.utils.DBSearchUtils

/**
 * 文件名搜索结果拼装器
 */
internal class FileNameSearchResultAssembler : SearchResultAssembler {

    override val resultType: Int = SearchType.TYPE_FILE_NAME

    override fun assemble(
        singleQueryResult: SingleQueryResult,
        searchEntry: SearchEntry
    ): SingleQueryResult {
        val singleKeyword = singleQueryResult.keyword ?: return singleQueryResult
        GLog.d(TAG, "[assemble] singleKeyword:$singleKeyword")
        val startTime = System.currentTimeMillis()
        val mediaIdEntryList = DBSearchUtils.matchFileNameFromLocalMedia(singleKeyword)
        if (mediaIdEntryList.isNullOrEmpty().not()) {
            singleQueryResult.appendQueryResult(mediaIdEntryList, singleKeyword, resultType)
        }
        GLog.d(TAG, "[assemble] costTime:${GLog.getTime(startTime)}")
        return singleQueryResult
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}FileNameSearchResultAssembler"
    }
}