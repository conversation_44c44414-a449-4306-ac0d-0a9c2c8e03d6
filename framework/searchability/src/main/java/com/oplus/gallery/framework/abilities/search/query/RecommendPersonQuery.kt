/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RecommendPersonQuery.kt
 ** Description: 推荐月查询
 ** Version: 1.0
 ** Date: 2023/10/9
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/9      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.query

import android.database.Cursor
import android.database.MatrixCursor
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.cache.RecommendCache
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 推荐人物查询
 *
 * @property forceQuery 是否强制从数据库查询
 * @property recommendCache 推荐缓存
 */
internal class RecommendPersonQuery(
    private val forceQuery: Boolean,
    private val recommendCache: RecommendCache
) : Query {
    override fun query(): Cursor? {
        return if (!forceQuery && recommendCache.hasPersonRecommend()) {
            recommendCache.obtainPersonRecommend()
        } else {
            GTrace.traceBegin("RecommendPersonQuery")
            val startTime = System.currentTimeMillis()
            val cursor = MatrixCursor(COLUMN_RECOMMEND_PERSON)
            val queryTime = System.currentTimeMillis()
            val faceEntryList = GroupHelper.queryFaceEntryFroRecommend(ContextGetter.context)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] queryFaceEntryFroRecommend costTime:${GLog.getTime(queryTime)}")
                GLog.v(TAG, "[query] queryFaceEntryFroRecommend faceSize:${faceEntryList.size}")
            }
            for (entry in faceEntryList) {
                cursor.addRow(
                    arrayOf<Any?>(
                        entry.personId, entry.mediaId, entry.groupId, entry.faceName, entry.faceCount,
                        entry.left, entry.top, entry.right, entry.bottom, entry.width, entry.height, entry.mediaType, entry.personId
                    )
                )
            }
            recommendCache.updateToPersonRecommend(cursor)
            cursor.moveToPosition(-1)
            if (GProperty.LOG_GALLERY_OPEN) {
                GLog.v(TAG, "[query] costTime:${GLog.getTime(startTime)}")
            }
            GTrace.traceEnd()
            cursor
        }
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}RecommendPersonQuery"

        /**
         * 推荐人物数据列
         */
        private val COLUMN_RECOMMEND_PERSON = arrayOf(
            GalleryStore.ScanFaceColumns._ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.ScanFaceColumns.GROUP_ID,
            GalleryStore.ScanFaceColumns.GROUP_NAME,
            SearchSuggestionProviderUtil.COLUMN_COUNT,
            GalleryStore.ScanFaceColumns.LEFT,
            GalleryStore.ScanFaceColumns.TOP,
            GalleryStore.ScanFaceColumns.RIGHT,
            GalleryStore.ScanFaceColumns.BOTTOM,
            GalleryStore.ScanFaceColumns.THUMB_W,
            GalleryStore.ScanFaceColumns.THUMB_H,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_PERSON_ID
        )
    }
}