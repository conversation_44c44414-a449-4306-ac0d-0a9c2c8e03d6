/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: -LocationFilter.java
 ** Description: a helper for special location
 ** Version: 1.0
 ** Date :  2017/07/27
 ** Author: yanchao.<PERSON>@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 ** yanchao.<PERSON>@Apps.Gallery3D       2017/09/29    1.0     build this module
****************************************************************/
package com.oplus.gallery.framework.abilities.search.utils;

import android.content.Context;
import android.content.res.Resources;
import android.util.Pair;

import com.oplus.gallery.framework.abilities.search.entry.ShootLocation;
import com.oplus.gallery.business_lib.model.data.location.utils.GeoDBHelper;
import com.oplus.gallery.framework.datatmp.R;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class LocationFilter {
    private static LocationInfo sDicMo;
    private static LocationInfo sDicHk;
    private LocationInfo[] mDicFilters;

    public LocationFilter(Context context) {
        Resources resources = context.getResources();
        if (sDicMo == null) {
            sDicMo = new LocationInfo(resources, R.array.model_location_mo);
        }
        if (sDicHk == null) {
            sDicHk = new LocationInfo(resources, R.array.model_location_hk);
        }
        mDicFilters = new LocationInfo[]{
                sDicMo,
                sDicHk
        };
    }

    /**
     * 在搜索"香港,澳门"简体的时候,匹配上繁体的地点,并返回结果;因为高德的SDK不支持简体中文，需要特殊处理，引入该函数，修改链接已备注到Commit Message
     */
    public void addSpecialKeyword(String keyword, List<ShootLocation> completionLocations, Set<String> addressKeywordSet) {
        if ((addressKeywordSet == null) || addressKeywordSet.isEmpty()) {
            return;
        }
        for (LocationInfo location : mDicFilters) {
            if (location.containsKeyword(keyword)) {
                location.addKeyword(keyword, completionLocations, addressKeywordSet);
            }
        }
    }

    public static class LocationInfo {
        public int mResId;
        public String[] mKeywords;

        public LocationInfo(Resources resources, int resId) {
            this.mResId = resId;
            this.mKeywords = resources.getStringArray(resId);
        }

        public boolean containsKeyword(String keyword) {
            for (String reference : mKeywords) {
                if (reference.contains(keyword)) {
                    return true;
                }
            }
            return false;
        }

        public String matchKeyword(String singleKeyword) {
            for (String keyword : mKeywords) {
                if (keyword.contains(singleKeyword)) {
                    return keyword;
                }
            }
            return singleKeyword;
        }

        public void addKeyword(String singleKeyword, List<ShootLocation> completionLocations, Set<String> addressKeywordSet) {
            for (String keyword : mKeywords) {
                boolean isWrappedLocation = false;
                for (ShootLocation shootLocation : completionLocations) {
                    if (shootLocation.shootKeyword.contains(keyword)) {
                        isWrappedLocation = true;
                        break;
                    }
                }
                if (!isWrappedLocation && addressKeywordSet.contains(keyword)) {
                    ShootLocation shootLocation = new ShootLocation();
                    shootLocation.shootKeyword = matchKeyword(singleKeyword);
                    shootLocation.shootKeywordList = new ArrayList<>();
                    shootLocation.shootKeywordList.add(new Pair<>(GeoDBHelper.LEVEL_PROVINCE, keyword));
                    completionLocations.add(shootLocation);
                }
            }
        }

        @Override
        public String toString() {
            StringBuilder builder = new StringBuilder();
            builder.append("LocationInfo = ");
            if (mKeywords != null) {
                for (String keyword : mKeywords) {
                    builder.append(keyword);
                    builder.append(",");
                }
                builder.deleteCharAt(builder.length() - 1);
            } else {
                builder.append("[");
                builder.append(mResId);
                builder.append("]");
            }
            return builder.toString();
        }
    }

}

