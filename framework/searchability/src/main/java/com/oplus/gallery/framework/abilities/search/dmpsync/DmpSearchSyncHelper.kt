/***********************************************************
 * * Copyright (C), 2018-2028, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File       :  DmpSearchSyncHelper.kt
 * * Description:  dmp同步器，收集相册数据变化，同步到dmp
 * * Version: 1.0
 * * Date : 2025/01/09
 * * Author: pusong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>         <version>  <desc>
 * *  pusong        2025/01/09     1.0        create
 * *  pusong        2025/07/14     1.1         move
 ************************************************************/
package com.oplus.gallery.framework.abilities.search.dmpsync

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import android.os.Process
import android.provider.Settings
import android.text.TextUtils
import com.oplus.gallery.framework.abilities.search.dmpsync.observer.DmpTableChangeObserver
import com.oplus.gallery.framework.abilities.search.dmpsync.observer.DmpTableChangeObserver.Companion.BATTERY_ALLOW_SYNC_DMP
import com.oplus.gallery.framework.abilities.search.dmpsync.observer.DmpTableChangeObserver.Companion.TEMPERATE_ALLOW_SYNC_DMP
import com.oplus.gallery.framework.abilities.search.dmpsync.processor.DmpLocalMediaProcessor
import com.oplus.gallery.framework.abilities.search.dmpsync.processor.DmpOcrPagesContentProcessor
import com.oplus.gallery.framework.abilities.search.dmpsync.processor.DmpScanLabelProcessor
import com.oplus.gallery.framework.abilities.search.dmpsync.processor.IDmpTableChangeListener
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.INVALID_NORMAL
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.IS_TRASHED_FALSE
import com.oplus.gallery.foundation.database.trigger.DataDiffOperationManager
import com.oplus.gallery.foundation.database.trigger.DataOperationProcessorType
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.database.util.SQLGrammar.COMMA
import com.oplus.gallery.foundation.database.util.SQLGrammar.IN
import com.oplus.gallery.foundation.database.util.SQLGrammar.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.SQLGrammar.RIGHT_BRACKETS
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.cpu.CPUCoreBindUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.BatteryStatusUtil
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager.registerContentObserver
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager.unregisterContentObserver
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil
import com.oplus.gallery.standard_lib.scheduler.BatchProcess
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.util.LinkedList
import java.util.Queue

/**
 * 同步数据到dmp
 * 1.接收processor变化的数据
 * 2.15s内缓存变化数据，当超过限制立即同步dmp，未做限制则15s计时时间到再同步
 * 3.格式统一采用media_id IN (123,456,789)格式
 */
object DmpSearchSyncHelper {
    /*供中台知晓相册支持的版本列表，中台根据当前的版本列表，及自身支持的版本决定是否重建索引（默认版本为100）
    仅 SearchDBHelper.getSearchInfoRawQueryReq 接口提供的字段发生变更时，版本号才会更新*/
    const val RESOURCE_VERSION = 100
    private const val TAG = "DmpSearchSyncHelper"
    private const val VALUE_DEBUG_GAME_MODE = "debug_gamemode_value"

    // 同步dmp相关常量
    private const val KEY_URI = "uri"
    private const val KEY_RESOURCE_VERSION = "resourceVersion"
    const val KEY_MEDIA_ID = "media_id"
    const val KEY_PATH = "path"
    private const val GALLERY = "gallery"
    private const val KEY_SELECTION = "selection"
    private const val METHOD_DELETE = "deleteFiles"
    private const val METHOD_UPDATE = "updateFiles"
    private const val OPEN_URI = "content://com.open.gallery.smart.provider/searchInfo"
    private val DMP_URI = Uri.parse("content://com.oplus.dmp.index")

    // 数据变化状态
    private const val TASK_DELETE_DATA = 1
    private const val TASK_UPDATE_DATA = 2
    const val TASK_ENQUEUE_DATA_DELETE_QUEUE = 3
    const val TASK_ENQUEUE_DATA_UPDATE_QUEUE = 4

    // 通知dmp时间限制，有变化15s通知一次
    private const val DELAY_TIME_15_S = TimeUtils.TIME_15_SEC_IN_MS.toLong()

    // 批量通知dmp数量限制，15s内数量太多，每4999条数据更新一次
    const val MAX_MESSAGE_SIZE = 4999

    // 游戏模式状态
    private var isGameMode = false

    private var searchQueryListener: OnSearchQueryListener? = null
    @SuppressLint("StaticFieldLeak")
    private var gameModeObserver: GameModeObserver? = null
    @SuppressLint("StaticFieldLeak")
    private lateinit var context: Context

    private val dmpLocalMediaProcessor = DmpLocalMediaProcessor()
    private val dmpOcrPagesContentProcessor = DmpOcrPagesContentProcessor()
    private val dmpScanLabelProcessor = DmpScanLabelProcessor()
    private val dmpLocalMediaObserver = DmpTableChangeObserver(dmpLocalMediaProcessor)
    private val dmpOcrPagesContentObserver = DmpTableChangeObserver(dmpOcrPagesContentProcessor)
    private val dmpScanLabelObserver = DmpTableChangeObserver(dmpScanLabelProcessor)

    private val localMediaListener = object : IDmpTableChangeListener {
        override fun onChanged(bundle: Bundle, type: Int) {
            GLog.d(TAG, LogFlag.DL) { "localMediaListener onChanged" }
            val message = Message()
            message.what = type
            message.obj = bundle
            dmpSyncHandler.sendMessage(message)
            if (type == TASK_ENQUEUE_DATA_DELETE_QUEUE) {
                searchQueryListener?.onQueryDeleteMediaData()
            } else {
                searchQueryListener?.onQueryUpdateMediaData()
            }
        }
    }

    private val ocrPagesContentListener = object : IDmpTableChangeListener {
        override fun onChanged(bundle: Bundle, type: Int) {
            GLog.d(TAG, LogFlag.DL) { "ocrPagesContentListener onChanged" }
            val message = Message()
            message.what = type
            message.obj = bundle
            dmpSyncHandler.sendMessage(message)
            searchQueryListener?.onQueryUpdateMediaData()
        }
    }

    private val scanLabelListener = object : IDmpTableChangeListener {
        override fun onChanged(bundle: Bundle, type: Int) {
            GLog.d(TAG, LogFlag.DL) { "scanLabelListener onChanged" }
            val message = Message()
            message.what = type
            message.obj = bundle
            dmpSyncHandler.sendMessage(message)
            searchQueryListener?.onQueryUpdateMediaData()
        }
    }

    @JvmStatic
    fun init(context: Context) {
        runCatching {
            DmpSearchSyncHelper.context = context.applicationContext
            // 游戏模式监听
            gameModeObserver = GameModeObserver(context).apply {
                context.registerContentObserver(
                    Settings.Global.getUriFor(VALUE_DEBUG_GAME_MODE), false, this
                )
            }
            isGameMode = TextUtils.equals(Settings.Global.getString(context.contentResolver, VALUE_DEBUG_GAME_MODE), "1")
            GLog.d(TAG, LogFlag.DL) { "init, isGameMode:$isGameMode" }

            // 收到数据表变化监听信息并处理后的回调
            dmpLocalMediaProcessor.addTableChangeListener(localMediaListener)
            dmpOcrPagesContentProcessor.addTableChangeListener(ocrPagesContentListener)
            dmpScanLabelProcessor.addTableChangeListener(scanLabelListener)

            // Local_media，ocr_pages_v2_content，scan_label表变化监听
            DataDiffOperationManager.registerObserver(DataOperationProcessorType.LOCAL_MEDIA_DIFF, dmpLocalMediaObserver, false)
            DataDiffOperationManager.registerObserver(DataOperationProcessorType.OCR_PAGES_DIFF, dmpOcrPagesContentObserver, false)
            DataDiffOperationManager.registerObserver(DataOperationProcessorType.SCAN_LABEL_DIFF, dmpScanLabelObserver, false)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, it) { "DmpSearchSyncHelper init error" }
        }
    }

    @JvmStatic
    fun release() {
        gameModeObserver?.let { context.unregisterContentObserver(it) }
        DataDiffOperationManager.unRegisterObserver(DataOperationProcessorType.LOCAL_MEDIA_DIFF, dmpLocalMediaObserver, false)
        DataDiffOperationManager.unRegisterObserver(DataOperationProcessorType.OCR_PAGES_DIFF, dmpOcrPagesContentObserver, false)
        DataDiffOperationManager.unRegisterObserver(DataOperationProcessorType.SCAN_LABEL_DIFF, dmpScanLabelObserver, false)
        dmpLocalMediaProcessor.removeListener(localMediaListener)
        dmpOcrPagesContentProcessor.removeListener(ocrPagesContentListener)
        dmpScanLabelProcessor.removeListener(scanLabelListener)
        handlerThread.quitSafely()
    }

    fun setSearchQueryListener(listener: OnSearchQueryListener?) {
        searchQueryListener = listener
    }

    private class DmpSyncHandlerThread(
        name: String,
        priority: Int
    ) : HandlerThread(name, priority) {
        override fun run() {
            super.run()
            CPUCoreBindUtils.bindBgCore(threadId)
        }

        override fun onLooperPrepared() {
            super.onLooperPrepared()
            CPUCoreBindUtils.bindBgCore(threadId)
        }
    }

    private val handlerThread = DmpSyncHandlerThread(
        "DmpSyncHandlerThread",
        Process.THREAD_PRIORITY_BACKGROUND
    )

    private val dmpSyncHandler: Handler by lazy {
        handlerThread.start()
        DmpSyncHandler(handlerThread.looper)
    }

    private class DmpSyncHandler(looper: Looper) : Handler(looper) {
        private val deleteDataQueue: Queue<Bundle> = LinkedList()
        private val updateDataQueue: Queue<Bundle> = LinkedList()
        private val handleDeleteDataRunnable = DeleteRunnable()
        private val handleUpdateDataRunnable = UpdateRunnable()
        private var markCountOfDeleteQueue = 0
        private var markCountOfUpdateQueue = 0

        override fun handleMessage(msg: Message) {
            runCatching {
                if (isNotAllowToCallIndexProvider()) {
                    GLog.w(TAG, LogFlag.DL) {  "isNotAllowToCallIndexProvider in is game mode" }
                    return
                }
                if (isAllowToSyncDmp().not()) {
                    GLog.w(TAG, LogFlag.DL) {  "handleMessage, isAllowToSyncDmp is false" }
                    return
                }
                CPUCoreBindUtils.bindBgCore(Process.myTid())
                val bundle = msg.obj as Bundle
                when (msg.what) {
                    TASK_ENQUEUE_DATA_DELETE_QUEUE -> handleDataQueue(bundle, handleDeleteDataRunnable, deleteDataQueue, TASK_DELETE_DATA)
                    TASK_ENQUEUE_DATA_UPDATE_QUEUE -> handleDataQueue(bundle, handleUpdateDataRunnable, updateDataQueue, TASK_UPDATE_DATA)
                    TASK_DELETE_DATA, TASK_UPDATE_DATA -> syncDataToDmp(bundle, msg.what)
                    else -> GLog.w(TAG, LogFlag.DL) { "handleMessage no type" }
                }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, it) { "handleMessage error" }
            }
        }

        /**
         * 1.温度低于38
         * 2.电量高于30%
         */
        private fun isAllowToSyncDmp(): Boolean {
            val isTemperatureAllow = TemperatureUtil.isTemperatureAllow(TEMPERATE_ALLOW_SYNC_DMP)
            val batteryPercent = BatteryStatusUtil.getCurrentBatteryPercent(ContextGetter.context)
            GLog.d(TAG, LogFlag.DL) { "isAllowToSyncDmp, isTemperatureAllow: $isTemperatureAllow, batteryPercent: $batteryPercent" }
            return (isTemperatureAllow && (batteryPercent >= BATTERY_ALLOW_SYNC_DMP))
        }

        /**
         * 收到processor的回调会后走到此处，开启15s计时，缓存数据
         * @param bundle processor传递的变化数据
         * @param runnable 执行任务的runnable
         * @param queue 缓存15s内的数据
         * @param type 1-delete, 2-update(包括数据插入和变化)
         */
        private fun handleDataQueue(bundle: Bundle, runnable: Runnable, queue: Queue<Bundle>, type: Int) {
            if (bundle.isEmpty) {
                GLog.w(TAG, LogFlag.DL) { "handleDataQueue bundle is empty, type:$type" }
                return
            }
            queue.offer(bundle)
            if (hasCallbacks(runnable)) {
                GLog.d(TAG, LogFlag.DL) { "handleDataQueue, message queen already has this runnable, type:$type" }
                return
            }
            postDelayed(runnable, DELAY_TIME_15_S)
            GLog.d(TAG, LogFlag.DL) { "handleDataQueue, postDelayed 15s to handle runnable, type:$type" }
        }

        /**
         * 同步数据到dmp
         * @param bundle 需要同步的数据
         * @param type 1-delete, 2-update(包括数据插入和变化)
         */
        private fun syncDataToDmp(bundle: Bundle, type: Int) {
            bundle.putString(KEY_URI, OPEN_URI)
            bundle.putInt(KEY_RESOURCE_VERSION, RESOURCE_VERSION)
            context.contentResolver?.acquireUnstableContentProviderClient(DMP_URI)?.use {
                when (type) {
                    TASK_DELETE_DATA -> {
                        searchQueryListener?.onQueryDeleteMediaData()
                        it.call(METHOD_DELETE, GALLERY, bundle)
                    }
                    TASK_UPDATE_DATA -> {
                        searchQueryListener?.onQueryUpdateMediaData()
                        it.call(METHOD_UPDATE, GALLERY, bundle)
                    }
                }
                GLog.d(TAG, LogFlag.DL) { "handleMessage type:$type" }
            }
        }

        private fun isNotAllowToCallIndexProvider(): Boolean {
            return isGameMode
        }

        private inner class DeleteRunnable : Runnable {
            override fun run() {
                dispatchMessage(handleDeleteDataRunnable, deleteDataQueue, TASK_DELETE_DATA)
            }
        }

        private inner class UpdateRunnable : Runnable {
            override fun run() {
                dispatchMessage(handleUpdateDataRunnable, updateDataQueue, TASK_UPDATE_DATA)
            }
        }

        /**
         * 处理、分发processor发送的数据
         * @param runnable 具体处理的runnable
         * @param queue 15s缓存的数据
         * @param type 1-delete, 2-update(包括数据插入和变化)
         */
        private fun dispatchMessage(runnable: Runnable, queue: Queue<Bundle>, type: Int) {
            kotlin.runCatching {
                GLog.d(TAG, LogFlag.DL) { "run start, type:$type" }
                if (queue.isEmpty()) {
                    GLog.w(TAG, LogFlag.DL) { "run, bundleQueue isEmpty, type:$type" }
                    return
                }
                var selection = HashSet<Long>()
                when (type) {
                    TASK_DELETE_DATA -> {
                        selection = parseBundle(queue, type)
                        markCountOfDeleteQueue = 0
                    }
                    TASK_UPDATE_DATA -> {
                        selection = parseBundle(queue, type)
                        markCountOfUpdateQueue = 0
                    }
                }
                GLog.d(TAG, LogFlag.DL) { "parseBundle size:${selection.size}, type:$type" }
                if (selection.isEmpty()) {
                    return
                }
                val dataChunks = selection.chunked(MAX_MESSAGE_SIZE)
                dataChunks.forEachIndexed { index, chunk ->
                    val result = chunk.joinToString(COMMA, LEFT_BRACKETS, RIGHT_BRACKETS)
                    val realBundle = Bundle().apply {
                        putString(KEY_SELECTION, "${LocalColumns.MEDIA_ID}$IN$result")
                    }
                    val message = Message().apply {
                        what = type
                        obj = realBundle
                    }
                    dmpSyncHandler.sendMessage(message)
                    GLog.d(TAG, LogFlag.DL) { "run end, size:${chunk.size}, type:$type, index:$index" }
                }
                if (dmpSyncHandler.hasCallbacks(runnable)) {
                    dmpSyncHandler.removeCallbacks(runnable)
                }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, it) { "dispatchMessage error" }
            }
        }

        /**
         * 解析将要同步的数据
         * @param queue 15s缓存的数据
         * @param type 1-delete, 2-update(包括数据插入和变化)
         */
        private fun parseBundle(queue: Queue<Bundle>, type: Int): HashSet<Long> {
            val mediaIdSet = HashSet<Long>()
            if (type == TASK_UPDATE_DATA) {
                // update时可能是因为ocr，label扫描传递的数据，此时数据是path，需要通过path查找出media_id
                val pathSet = HashSet<String>()
                queue.forEach {
                    it.getLongArray(KEY_MEDIA_ID)?.let {
                        mediaIdSet.addAll(it.toList())
                    }
                    it.getStringArray(KEY_PATH)?.let {
                        pathSet.addAll(it.toList())
                    }
                }
                if (pathSet.isNotEmpty()) {
                    mediaIdSet.addAll(getMediaIdFromLocalMedia(pathSet))
                }
            } else {
                queue.forEach {
                    it.getLongArray(KEY_MEDIA_ID)?.let {
                        mediaIdSet.addAll(it.toList())
                    }
                }
            }
            queue.clear()
            return mediaIdSet
        }

        /**
         * 根据path从local_media获取media_id
         * @param dataSet path的集合
         */
        private fun getMediaIdFromLocalMedia(dataSet: HashSet<String>): HashSet<Long> {
            val mediaIdSet = HashSet<Long>()
            if (dataSet.isEmpty()) {
                return mediaIdSet
            }
            runCatching {
                BatchProcess.doBatch(dataSet.toList(), BatchProcess.PAGE_SIZE_999) { batchFile ->
                    QueryReq.Builder<Cursor>()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                        .setProjection(MEDIA_ID_ITEM_COLUMN)
                        .setWhere(DatabaseUtils.getWhereQueryIn(LocalColumns.DATA, batchFile.size))
                        .setWhereArgs(batchFile.toTypedArray())
                        .setConvert(CursorConvert())
                        .build().exec()?.use { cursor ->
                            val size = cursor.count
                            if (size > 0) {
                                GLog.d(TAG, LogFlag.DL) { "getMediaIdFromLocalMedia size: $size" }
                                val mediaIdIdx = cursor.getColumnIndex(LocalColumns.MEDIA_ID)
                                val isTrashedIdx = cursor.getColumnIndex(LocalColumns.IS_TRASHED)
                                val invalidIdx = cursor.getColumnIndex(GalleryStore.OcrPages.INVALID)
                                while (cursor.moveToNext()) {
                                    val mediaId = if ((mediaIdIdx >= 0)) cursor.getLong(mediaIdIdx) else null
                                    val isTrashed = if ((isTrashedIdx >= 0)) cursor.getInt(isTrashedIdx) else null
                                    val invalid = if ((invalidIdx >= 0)) cursor.getInt(invalidIdx) else null
                                    if (mediaId != null && isTrashed == IS_TRASHED_FALSE && invalid == INVALID_NORMAL) {
                                        mediaIdSet.add(mediaId)
                                    }
                                }
                            }
                        }
                    emptyList()
                }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, it) { "getMediaIdFromLocalMedia error" }
            }
            return mediaIdSet
        }

        companion object {
            private const val TAG = "DmpSyncHandler"
            private val MEDIA_ID_ITEM_COLUMN: Array<String> = arrayOf(
                LocalColumns.MEDIA_ID,
                LocalColumns.IS_TRASHED,
                LocalColumns.INVALID
            )
        }
    }

    /**
     * 处理搜索查询语句并查询数据库中媒体数据
     */
    interface OnSearchQueryListener {
        /**
         * 当媒体数据发生Delete时调用。
         *
         * @param table 发生变化的表名。
         * @param selection 用于 SQL WHERE 子句格式的选择条件
         * @param selectionArgs 替换 `selection` 中 '?' 占位符的参数数组
         * @param values 包含更新数据的 ContentValues
         */
        fun onMediaDataDelete(table: String, selection: String?, selectionArgs: Array<String>?, values: ContentValues?)

        /**
         * 当媒体数据发生Update时调用。
         *
         * @param table 发生变化的表名。
         * @param selection 用于 SQL WHERE 子句格式的选择条件
         * @param selectionArgs 替换 `selection` 中 '?' 占位符的参数数组
         * @param values 包含更新数据的 ContentValues
         */
        fun onMediaDataUpdate(table: String, selection: String?, selectionArgs: Array<String>?, values: ContentValues?)

        /**
         * 查询Delete媒体数据。
         */
        fun onQueryDeleteMediaData()

        /**
         * 查询Update媒体数据。
         */
        fun onQueryUpdateMediaData()
    }

    private class GameModeObserver(context: Context?) : HandlerContentObserver(null) {
        private var context: Context? = null

        init {
            this.context = context?.applicationContext
        }

        override fun onChange(selfChange: Boolean, uri: Uri?) {
            super.onChange(selfChange, uri)
            runCatching {
                isGameMode = TextUtils.equals(Settings.Global.getString(context?.contentResolver, VALUE_DEBUG_GAME_MODE), "1")
            }.onFailure {
                GLog.e(TAG, LogFlag.DL, it) { "onChange error" }
            }
            GLog.d(TAG, LogFlag.DL) { "onChange game mode $isGameMode" }
        }

        companion object {
            private const val TAG = "GameModeObserver"
        }
    }
}

