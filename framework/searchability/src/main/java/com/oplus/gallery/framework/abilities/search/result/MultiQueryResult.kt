/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MultiQueryResult.kt
 ** Description: 保存多种搜索结果
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.result

import android.database.MatrixCursor
import android.os.Bundle
import android.util.Pair
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SEARCH_RESULT_KEYWORD_ENTRIES
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SEARCH_RESULT_LIST
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GLog.getTime
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.SearchType
import com.oplus.gallery.framework.abilities.search.entry.KeywordEntry
import com.oplus.gallery.framework.abilities.search.entry.SearchResultEntry
import com.oplus.gallery.framework.abilities.search.utils.SearchUtils.galleryIdListToStr
import com.oplus.gallery.framework.abilities.search.utils.SearchUtils.getLocalCountFromEntryList
import com.oplus.gallery.framework.abilities.search.utils.SearchUtils.mediaIdListToStr

/**
 * 保存多关键词的所有搜索结果
 */
internal class MultiQueryResult {

    /**
     * 目前仅支持2个关键词的搜索，最终结果是取2个搜索关键词结果的交集
     */
    private val intersectionResult: MutableList<SearchResultEntry> = ArrayList()

    /**
     * 记录每个关键词的搜索结果集
     */
    private val multiKeywordResult: MutableList<Pair<String, SingleQueryResult>> = ArrayList()

    /**
     * OCR搜索结果
     * 多关键词输入时，多个关键词会同时当做一个整体来执行ocr查询结果
     */
    private var ocrQueryResult: SingleQueryResult? = null

    fun clear() {
        multiKeywordResult.clear()
    }

    private val isOcrEmpty: Boolean
        get() = (ocrQueryResult == null) || (ocrQueryResult?.isEmpty == true)

    val isEmpty: Boolean
        get() = multiKeywordResult.isEmpty() && isOcrEmpty

    fun addOcrQueryResult(result: SingleQueryResult?) {
        ocrQueryResult = result
    }

    fun addKeywordResult(result: SingleQueryResult) {
        multiKeywordResult.add(Pair(result.keyword, result))
    }

    fun buildCursorForQueryResult(): MatrixCursor {
        val time = System.currentTimeMillis()
        val cursor = MatrixCursor(COLUMN_NAME)
        var firstQueryResult: SingleQueryResult? = null
        var secondQueryResult: SingleQueryResult? = null
        // 遍历所有结果，并获取和记录第一个SingleQueryResult和最后一个SingleQueryResult
        for (resultPair in multiKeywordResult) {
            val singleQueryResult = resultPair.second

            singleQueryResult?.let {
                if (firstQueryResult == null) {
                    firstQueryResult = it
                } else {
                    secondQueryResult = it
                }
            }
        }
        if (firstQueryResult == null) {
            GLog.d(TAG, "[buildQueryResultForCursor] firstQueryResult is null")
        } else if (secondQueryResult == null) {
            firstQueryResult?.let {
                addRowToCursorFromQueryResult(cursor, it)
            }
        } else {
            takeIntersectionFromResult(cursor, firstQueryResult, secondQueryResult)
        }
        // OCR搜索结果添加到Cursor
        if (!isOcrEmpty) {
            ocrQueryResult?.let { addRowToCursorFromQueryResult(cursor, it) }
        }
        GLog.d(TAG, "[buildQueryResultForCursor] costTime:" + (System.currentTimeMillis() - time))
        return cursor
    }

    private fun takeIntersectionFromResult(
        cursor: MatrixCursor,
        firstQueryResult: SingleQueryResult?,
        secondQueryResult: SingleQueryResult?
    ) {
        val firstResultList: MutableList<SearchResultEntry> = ArrayList()
        firstQueryResult?.let {
            firstResultList.addAll(it.dateResult)
            firstResultList.addAll(it.personResult)
            firstResultList.addAll(it.locationResult)
            firstResultList.addAll(it.labelResult)
            firstResultList.addAll(it.albumResult)
            firstResultList.addAll(it.labelGuideResult)
            firstResultList.addAll(it.specialKeywordResult)
            firstResultList.addAll(it.fileNameKeywordResult)
            firstResultList.addAll(it.multiModalSearchResult)
            firstResultList.addAll(it.captionSearchResult)
        }
        GLog.d(TAG, "[takeIntersectionFromResult] firstResultList size:" + firstResultList.size)
        val secondResultList: MutableList<SearchResultEntry> = ArrayList()
        secondQueryResult?.let {
            secondResultList.addAll(it.dateResult)
            secondResultList.addAll(it.personResult)
            secondResultList.addAll(it.locationResult)
            secondResultList.addAll(it.labelResult)
            secondResultList.addAll(it.albumResult)
            secondResultList.addAll(it.labelGuideResult)
            secondResultList.addAll(it.specialKeywordResult)
            secondResultList.addAll(it.fileNameKeywordResult)
            secondResultList.addAll(it.multiModalSearchResult)
            secondResultList.addAll(it.captionSearchResult)
        }
        GLog.d(TAG, "[takeIntersectionFromResult], secondResultList size:" + secondResultList.size)

        intersectionResult.clear()
        firstResultList.forEach { firstResultEntry ->
            secondResultList.forEach { secondResultEntry ->
                if (canTakeIntersection(firstResultEntry, secondResultEntry)) {
                    takeIntersectionFromEntry(cursor, firstResultEntry, secondResultEntry)
                }
            }
        }
    }

    @Suppress("CollapsibleIfStatements")
    private fun takeIntersectionFromEntry(
        cursor: MatrixCursor,
        firstResultEntry: SearchResultEntry,
        secondResultEntry: SearchResultEntry
    ) {
        // 都是人物图集并且分组ID相同,取第一个结果
        if (firstResultEntry.isFaceEntry() && secondResultEntry.isFaceEntry()) {
            if (firstResultEntry.groupId == secondResultEntry.groupId) {
                addRowToCursorFromPersonQueryEntryForMultiSearch(cursor, firstResultEntry)
                return
            }
        }

        if (isNeedTaskIntersection(firstResultEntry, secondResultEntry).not()) {
            GLog.d(TAG, "[takeIntersectionFromEntry] isNeedTaskIntersection false")
            return
        }

        val firstResultEntryList: MutableList<MediaIdEntry> = firstResultEntry.mediaIdEntryList?.let { ArrayList(it) } ?: ArrayList()
        val secondResultEntryList: MutableList<MediaIdEntry> = secondResultEntry.mediaIdEntryList?.let { ArrayList(it) } ?: ArrayList()
        val startTime = System.currentTimeMillis()
        // 取两个结果的交集
        secondResultEntryList.retainAll(firstResultEntryList)
        GLog.d(TAG, "[takeIntersectionFromEntry] retainAll cost time:" + getTime(startTime))
        if (secondResultEntryList.isEmpty()) {
            return
        }
        GLog.d(
            TAG, "[takeIntersectionFromEntry] find one combination : "
                    + "firstResultEntry.name  = " + firstResultEntry.name
                    + ";secondResultEntry.name = " + secondResultEntry.name
                    + ";firstResultEntryList.size = " + firstResultEntryList.size
                    + ";secondResultEntryList.size = " + secondResultEntryList.size
        )
        secondResultEntryList.sort()

        // 确定组合结果类型
        val combinationType = getCombinationType(firstResultEntry, secondResultEntry)
        val keywordEntry = KeywordEntry(
            firstResultEntry.type, firstResultEntry.name,
            firstResultEntry.groupId, firstResultEntry.albumType, true
        )
        // 构建组合结果
        val combinationName = getCombinationName(firstResultEntry, secondResultEntry)
        val combinationEntry = SearchResultEntry.Builder()
            .setName(combinationName)
            .setIdStr(mediaIdListToStr(secondResultEntryList))
            .setGalleryIdStr(galleryIdListToStr(secondResultEntryList))
            .setMediaIdEntryList(secondResultEntryList)
            .setCount(secondResultEntryList.size)
            .setLocalCount(getLocalCountFromEntryList(secondResultEntryList))
            .setType(combinationType)
            .build()

        // 若结果已经被包含在交集结果intersectionResult中，则不用重复添加
        if (!isIntersectionWrapped(combinationEntry)) {
            intersectionResult.add(combinationEntry)
            val resultEntries = ArrayList<SearchResultEntry>()
            resultEntries.add(firstResultEntry)
            resultEntries.add(secondResultEntry)
            addRowToCursorFromResultEntryForMultiSearch(cursor, combinationEntry, keywordEntry, resultEntries)
        }
    }

    private fun isNeedTaskIntersection(firstResultEntry: SearchResultEntry, secondResultEntry: SearchResultEntry): Boolean {
        if (firstResultEntry.mediaIdEntryList == null || secondResultEntry.mediaIdEntryList == null) {
            GLog.d(TAG, "[takeIntersectionFromEntry] firstResultEntryList or secondResultEntryList is null")
            return false
        }

        // 名称组合： eg. name1+ " " + name2
        val combinationName = getCombinationName(firstResultEntry, secondResultEntry)

        /**
         * 当两个结果都是位置或是人物图集时，如果它们的组合名称间隔超过2个空格，那么就不取交集
         */
        if (firstResultEntry.isLocationEntry() && secondResultEntry.isFaceEntry()
            || firstResultEntry.isFaceEntry() && secondResultEntry.isLocationEntry()
        ) {
            val separatedNames = combinationName.split(SearchCommonUtils.SPACE_REGEX.toRegex()).toTypedArray()
            if (separatedNames.size > MAX_BLANK_SPACE_COUNT) {
                return false
            }
        }

        return true
    }

    /**
     * 交集结果是否已经包含
     *
     * @param combinationEntry 组合结果
     * @return 布尔类型
     */
    private fun isIntersectionWrapped(combinationEntry: SearchResultEntry): Boolean {
        for (entry in intersectionResult) {
            val equalSize = entry.mediaIdEntryList?.size == combinationEntry.mediaIdEntryList?.size
            if (equalSize && combinationEntry.mediaIdEntryList?.let { entry.mediaIdEntryList?.containsAll(it) } == true) {
                return true
            }
        }
        return false
    }

    private fun addRowToCursorFromResultEntry(cursor: MatrixCursor, entry: SearchResultEntry) {
        if (entry.coverId != -1) {
            cursor.addRow(
                arrayOf<Any?>(
                    entry.groupId, entry.coverId,
                    entry.name, entry.localCount, entry.idStr,
                    entry.type, entry.albumType, entry.coverDateTaken, entry.coverMediaType,
                    DEFAULT_COVER_ID, DEFAULT_COVER_ID, entry.coverGalleryId, entry.galleryIdStr, entry.count
                )
            )
            val bundle = Bundle(cursor.extras)
            var keywordEntries = cursor.extras.getParcelableArrayList<KeywordEntry?>(SEARCH_RESULT_KEYWORD_ENTRIES)
            if (keywordEntries == null) {
                keywordEntries = ArrayList()
            }
            keywordEntries.add(KeywordEntry(entry.type, entry.name, entry.groupId, entry.albumType, false))
            bundle.putParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES, keywordEntries)
            cursor.extras = bundle
        }
    }

    private fun addRowToCursorFromMemoriesAlbumResultEntry(cursor: MatrixCursor, entry: SearchResultEntry) {
        if (entry.coverId != -1) {
            cursor.addRow(
                arrayOf<Any?>(
                    entry.groupId, entry.coverId,
                    entry.name, entry.localCount, entry.idStr,
                    entry.type, entry.albumType, entry.coverDateTaken, entry.coverMediaType,
                    DEFAULT_COVER_ID, entry.coverMemoriesId, DEFAULT_COVER_ID, entry.galleryIdStr, entry.count
                )
            )
            val bundle = Bundle(cursor.extras)
            var keywordEntries = cursor.extras.getParcelableArrayList<KeywordEntry?>(SEARCH_RESULT_KEYWORD_ENTRIES)
            if (keywordEntries == null) {
                keywordEntries = ArrayList()
            }
            keywordEntries.add(KeywordEntry(entry.type, entry.name, entry.groupId, entry.albumType, false))
            bundle.putParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES, keywordEntries)
            cursor.extras = bundle
        }
    }

    private fun addRowToCursorFromResultEntryForMultiSearch(
        cursor: MatrixCursor,
        entry: SearchResultEntry,
        keywordEntry: KeywordEntry,
        resultEntries: ArrayList<SearchResultEntry>
    ) {
        if (entry.coverId != -1) {
            cursor.addRow(
                arrayOf<Any?>(
                    entry.groupId, entry.coverId, entry.name, entry.localCount, entry.idStr,
                    entry.type, entry.albumType, entry.coverDateTaken,
                    entry.coverMediaType, DEFAULT_COVER_ID, DEFAULT_COVER_ID, entry.coverGalleryId, entry.galleryIdStr, entry.count
                )
            )
            val bundle = Bundle(cursor.extras)
            var keywordEntries = cursor.extras.getParcelableArrayList<KeywordEntry?>(SEARCH_RESULT_KEYWORD_ENTRIES)
            if (keywordEntries == null) {
                keywordEntries = ArrayList()
            }
            keywordEntries.add(keywordEntry)
            bundle.putParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES, keywordEntries)
            if (resultEntries.isNotEmpty()) {
                val listId: String = SEARCH_RESULT_LIST + entry.hashCode()
                GLog.d(
                    TAG, "[addRowToCursorFromQueryEntry] name = " + entry.name
                            + ", hashCode = " + entry.hashCode()
                            + ", resultEntries = " + resultEntries
                            + ", count = " + entry.count
                            + ", localCount = " + entry.localCount
                            + ", idStr = " + entry.idStr
                )
                bundle.putParcelableArrayList(listId, resultEntries)
            }
            cursor.extras = bundle
        }
    }

    private fun addRowToCursorFromPersonQueryEntry(cursor: MatrixCursor, entry: SearchResultEntry) {
        val coverCount = GroupHelper.getCoverCountByGroupId(entry.groupId.toLong())
        if (coverCount.first != -1) {
            val personId = coverCount.first
            entry.coverPersonId = personId
            cursor.addRow(
                arrayOf<Any?>(
                    entry.groupId, personId,
                    entry.name, coverCount.second, entry.idStr,
                    entry.type, entry.albumType, entry.coverDateTaken, entry.coverMediaType,
                    entry.coverPersonId, DEFAULT_COVER_ID, DEFAULT_COVER_ID, entry.galleryIdStr, coverCount.second
                )
            )
            val bundle = Bundle(cursor.extras)
            var keywordEntries = cursor.extras.getParcelableArrayList<KeywordEntry?>(SEARCH_RESULT_KEYWORD_ENTRIES)
            if (keywordEntries == null) {
                keywordEntries = ArrayList()
            }
            keywordEntries.add(KeywordEntry(entry.type, entry.name, entry.groupId, entry.albumType, false))
            bundle.putParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES, keywordEntries)
            cursor.extras = bundle
        }
    }

    private fun addRowToCursorFromPersonQueryEntryForMultiSearch(cursor: MatrixCursor, entry: SearchResultEntry) {
        val coverCount = GroupHelper.getCoverCountByGroupId(entry.groupId.toLong())
        if (coverCount.first != -1) {
            val personId = coverCount.first
            cursor.addRow(
                arrayOf<Any?>(
                    entry.groupId, personId,
                    entry.name, coverCount.second, entry.idStr,
                    entry.type, entry.albumType, entry.coverDateTaken, entry.coverMediaType,
                    personId, DEFAULT_COVER_ID, DEFAULT_COVER_ID, entry.galleryIdStr, coverCount.second
                )
            )
            val bundle = Bundle(cursor.extras)
            var keywordEntries = cursor.extras.getParcelableArrayList<KeywordEntry?>(SEARCH_RESULT_KEYWORD_ENTRIES)
            if (keywordEntries == null) {
                keywordEntries = ArrayList()
            }
            keywordEntries.add(KeywordEntry(entry.type, entry.name, entry.groupId, entry.albumType, true))
            bundle.putParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES, keywordEntries)
            cursor.extras = bundle
        }
    }

    private fun addRowToCursorFromQueryResult(cursor: MatrixCursor, result: SingleQueryResult) {
        for (entry in result.ocrResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.dateResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.locationResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.labelResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.albumResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.personResult) {
            addRowToCursorFromPersonQueryEntry(cursor, entry)
        }
        for (entry in result.specialKeywordResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.labelGuideResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.memoriesAlbumResult) {
            addRowToCursorFromMemoriesAlbumResultEntry(cursor, entry)
        }
        for (entry in result.fileNameKeywordResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.multiModalSearchResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
        for (entry in result.captionSearchResult) {
            addRowToCursorFromResultEntry(cursor, entry)
        }
    }

    /**
     * 组合名称
     * 规则：
     * 1.first名 + 空格 + second名
     * 2.first或second有一个为空，则直接取另外一个名字
     *
     * @param firstResultEntry 第一个结果
     * @param secondResultEntry 第二个结果
     * @return
     */
    private fun getCombinationName(firstResultEntry: SearchResultEntry, secondResultEntry: SearchResultEntry): String {
        val nameBuffer = StringBuffer()
        if (firstResultEntry.name == null && secondResultEntry.name == null) {
            return nameBuffer.toString()
        }
        if (firstResultEntry.name == null) {
            nameBuffer.append(secondResultEntry.name)
        } else if (secondResultEntry.name == null) {
            nameBuffer.append(firstResultEntry.name)
        } else {
            nameBuffer.append(firstResultEntry.name + TextUtil.BLANK_STRING + secondResultEntry.name)
        }
        return nameBuffer.toString()
    }

    /**
     * 获取组合类型
     * 规则：
     * 1. 当两个结果都是Face的，并且其groupId不相同，那么类型就是人物
     * 2. 其他情况都作为TYPE_MULTI_LABEL类型
     *
     * @param firstResultEntry 第一个结果
     * @param secondResultEntry 第二个结果
     * @return
     */
    @Suppress("CollapsibleIfStatements")
    private fun getCombinationType(firstResultEntry: SearchResultEntry, secondResultEntry: SearchResultEntry): Int {
        return if (firstResultEntry.isFaceEntry() && secondResultEntry.isFaceEntry()) {
            if (firstResultEntry.groupId != secondResultEntry.groupId) {
                SearchType.TYPE_MULTI_LABEL
            } else {
                SearchType.TYPE_PERSON
            }
        } else {
            SearchType.TYPE_MULTI_LABEL
        }
    }

    private fun canTakeIntersection(firstResultEntry: SearchResultEntry, secondResultEntry: SearchResultEntry): Boolean {
        return (!firstResultEntry.isChildLabelEntry()
                && !secondResultEntry.isChildLabelEntry()
                && !firstResultEntry.isGuideLabelEntry()
                && !secondResultEntry.isGuideLabelEntry()
                && !firstResultEntry.isRecycleAlbum()
                && !secondResultEntry.isRecycleAlbum()
                && !firstResultEntry.isMemoriesAlbumEntry()
                && !secondResultEntry.isMemoriesAlbumEntry())
    }

    override fun toString(): String {
        return "MultiQueryResult(intersectionResult.size=${intersectionResult.size}, multiKeywordResult.size=${multiKeywordResult.size}, " +
                "ocrQueryResult.isEmpty=${ocrQueryResult?.isEmpty}, isOcrEmpty=$isOcrEmpty, MultiQueryResult.isEmpty=$isEmpty)"
    }

    companion object {
        private const val TAG = "${SearchConst.TAG}MultiQueryResult"

        private const val MAX_BLANK_SPACE_COUNT = 3

        /**
         * 搜索结果关注的列
         */
        private val COLUMN_NAME = arrayOf(
            SearchSuggestionProviderUtil.SEARCH_RESULT_ID,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_ID,
            SearchSuggestionProviderUtil.SEARCH_RESULT_NAME,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COUNT,
            SearchSuggestionProviderUtil.SEARCH_RESULT_ID_LIST,
            SearchSuggestionProviderUtil.SEARCH_RESULT_TYPE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_ALBUM_TYPE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_DATE_TAKEN,
            SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_PERSON_ID,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_MEMORIES_ID,
            SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_GALLERY_ID,
            SearchSuggestionProviderUtil.SEARCH_RESULT_GALLERY_ID_LIST,
            SearchSuggestionProviderUtil.SEARCH_RESULT_GALLERY_COUNT
        )

        private const val DEFAULT_COVER_ID = 0
    }
}