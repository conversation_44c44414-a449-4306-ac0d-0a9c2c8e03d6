/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - KeywordCache.kt
 * Description: KeywordCache
 * Version: 1.0
 * Date: 2023/8/7
 * Author: W9009912
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * W9009912      2023/8/7      1.0              create file
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.search.cache

import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry
import com.oplus.gallery.business_lib.model.data.base.entry.DateTimeEntry
import com.oplus.gallery.business_lib.model.data.base.entry.MemoriesAlbumEntry
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_10
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_100
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_1000

/**
 * 关键字缓存
 */
class KeywordCache {

    /**
     * 年信息缓存
     */
    @JvmField
    var yearList: MutableList<Int>? = null

    /**
     * 月信息缓存
     */
    @JvmField
    var monthList: MutableList<Int>? = null

    /**
     * 地址列表缓存
     */
    @JvmField
    var addressSet: MutableList<Array<String?>>? = null

    /**
     * 地址名关键字缓存
     */
    @JvmField
    var addressKeywordSet: MutableSet<String>? = null

    /**
     * poi关键字缓存
     */
    @JvmField
    var poiKeywordSet: MutableSet<String>? = null

    /**
     * poi地址名关键字缓存
     */
    @JvmField
    var poiLocationSet: MutableList<Array<String?>>? = null
    /**
     * 人物图集分组名缓存
     * 后面移除此字段，用personKeywordMap替代
     */
    @JvmField
    var personKeywordSet: MutableSet<String>? = null

    /**
     * 人物图集分组ID到分组名的映射表缓存
     */
    @JvmField
    var personKeywordMap: MutableMap<Int, String>? = null

    /**
     * 标签ID到标签名称列表的映射表缓存
     */
    @JvmField
    var labelIdToNameMap: MutableMap<Int, List<String>?>? = null

    /**
     * 媒体ID到标签ID列表的映射表信息缓存, 用于全匹配搜索
     * key: 媒体ID
     * value: sceneIds场景ID列表
     */
    @JvmField
    var mediaIdToLabelIdsMap: HashMap<Int, HashSet<Int>>? = null

    /**
     * 节日表信息缓存
     * key：节日名
     * value：DateTimeEntry 日期时间对象, 包含媒体ID列表
     */
    @JvmField
    var festivalMap: HashMap<String, DateTimeEntry>? = null

    /**
     * 本地图集信息缓存
     */
    @JvmField
    var albumEntrySet: MutableList<AlbumEntry>? = null

    /**
     * 精彩回忆图集信息缓存
     */
    @JvmField
    var memoriesAlbumSet: MutableList<MemoriesAlbumEntry>? = null

    var isKeywordCacheLoaded = false
        private set

    /**
     * 是否存在该年
     */
    fun hasYear(yearNumerical: Int): Boolean {
        yearList?.let {
            for (year in it) {
                if (year == yearNumerical) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 是否存在该月
     */
    fun hasMonth(monthNumerical: Int): Boolean {
        monthList?.let {
            for (month in it) {
                if (month == monthNumerical) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 搜索匹配的年
     */
    fun searchYearMatch(numerical: Int): List<Int>? {
        yearList?.let {
            val result: MutableList<Int> = ArrayList()
            for (year in it) {
                if (year == numerical || year / NUMBER_10 == numerical
                    || year / NUMBER_100 == numerical || year / NUMBER_1000 == numerical
                ) {
                    result.add(year)
                }
            }
            return result
        }
        return null
    }

    fun setIsKeywordLoaded(isKeywordLoaded: Boolean) {
        isKeywordCacheLoaded = isKeywordLoaded
    }

    fun release() {
        yearList?.clear()
        monthList?.clear()
        addressSet?.clear()
        addressKeywordSet?.clear()
        personKeywordSet?.clear()
        personKeywordMap?.clear()
        labelIdToNameMap?.clear()
        mediaIdToLabelIdsMap?.clear()
        festivalMap?.clear()
        albumEntrySet?.clear()
        memoriesAlbumSet?.clear()
        poiLocationSet?.clear()
        poiKeywordSet?.clear()
    }
}