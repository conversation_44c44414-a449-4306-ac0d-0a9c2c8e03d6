/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ShootDateTime.kt
 * Description: ShootDateTime
 * Version: 1.0
 * Date: 2023/8/7
 * Author: W9009912
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * W9009912      2023/8/7      1.0              create file
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.search.entry

import android.text.TextUtils
import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.framework.abilities.search.datetime.DateTimeSelector
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * 补全命中的日期时间对象
 */
class ShootDateTime {
    @JvmField
    var format: String? = null

    @JvmField
    var conditions: String? = null

    @JvmField
    var shootKeyword: String? = null

    @JvmField
    var dateRange: DateTimeSelector.DateRange? = null

    @JvmField
    var mediaIdEntries: MutableList<MediaIdEntry> = arrayListOf()

    override fun equals(other: Any?): Boolean {
        if (other != null && other is ShootDateTime) {
            return !TextUtils.isEmpty(shootKeyword) && shootKeyword.equals(other.shootKeyword, ignoreCase = true)
        }
        return false
    }

    override fun hashCode(): Int {
        val prime: Int = AppConstants.MagicCode.HASH_CODE_MAGIC
        var result = 1
        result = prime * result + if (TextUtils.isEmpty(format)) 0 else format.hashCode()
        result = prime * result + if (TextUtils.isEmpty(conditions)) 0 else conditions.hashCode()
        result = prime * result + if (TextUtils.isEmpty(shootKeyword)) 0 else shootKeyword.hashCode()
        result = prime * result + if (dateRange == null) 0 else dateRange.hashCode()
        if (mediaIdEntries.size > 0) {
            for (entry in mediaIdEntries) {
                result = prime * result + (entry.hashCode())
            }
        } else {
            result *= prime
        }
        return result
    }

    /**
     * 是否包含condition
     */
    fun isWrappedCondition(condition: String?): Boolean {
        return !TextUtils.isEmpty(conditions) && conditions!!.contains(condition!!)
    }

    /**
     * 是否处于日期范围之中
     */
    fun isWrappedDateRange(dateTaken: Long): Boolean {
        return if (dateRange == null) {
            false
        } else dateTaken >= dateRange!!.start && dateTaken <= dateRange!!.end
    }

    override fun toString(): String {
        return "ShootDateTime(format=$format, conditions=$conditions, shootKeyword=$shootKeyword, dateRange=$dateRange, " +
                "mediaIdEntries=$mediaIdEntries)"
    }

    val isEmpty: Boolean
        get() = dateRange == null
}