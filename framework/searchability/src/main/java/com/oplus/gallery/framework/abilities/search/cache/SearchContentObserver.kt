/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SearchSuggestionContentObserver.kt
 ** Description: 搜索关注的数据库表内容变更观察器
 ** Version: 1.0
 ** Date: 2023/8/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/8/31      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.cache

import android.content.Context
import android.content.UriMatcher
import android.net.Uri
import android.os.Looper
import android.text.TextUtils
import com.oplus.gallery.bus_lib.Bus
import com.oplus.gallery.bus_lib.annotations.FinderType
import com.oplus.gallery.bus_lib.annotations.Subscribe
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.BACKGROUND_COLLECT_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCALE_CHANGE_RECOMMEND_UPDATING_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCAL_ALBUM_NAME_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCAL_FACE_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCAL_IMAGE_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCAL_LABEL_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCAL_MEMORIES_URI
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCAL_VIDEO_URI
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle.isRunningForeground
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager.registerContentObserver
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager.unregisterContentObserver
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver
import com.oplus.gallery.framework.abilities.search.SearchConst
import com.oplus.gallery.framework.abilities.search.permission.AccessPackageManager
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 搜索关注的数据库表内容变更观察器
 */
internal class SearchContentObserver(
    val keywordCacheManager: KeywordCacheManager,
    searchRecommendCacheManager: RecommendCacheManager,
) : HandlerContentObserver(null) {

    private val uriMatcher = UriMatcher(UriMatcher.NO_MATCH)

    private val cacheHandler: SuggestionCacheHandler = SuggestionCacheHandler(
        keywordCacheManager, searchRecommendCacheManager, Looper.getMainLooper()
    )

    private var enabledObserver = false

    // 变化立刻刷新的集合，处理完后立刻移出集合。用于处理一些特殊情况下，收到了变化通知，用于立刻执行，不做延迟处理
    private val changedImmediatelySet = mutableSetOf<Uri>()

    init {
        uriMatcher.addURI(LOCAL_FACE_URI.authority, LOCAL_FACE_URI.path, CODE_FACE)
        uriMatcher.addURI(LOCAL_IMAGE_URI.authority, LOCAL_IMAGE_URI.path, CODE_BACKGROUND_COLLECT)
        uriMatcher.addURI(LOCAL_VIDEO_URI.authority, LOCAL_VIDEO_URI.path, CODE_BACKGROUND_COLLECT)
        uriMatcher.addURI(LOCAL_LABEL_URI.authority, LOCAL_LABEL_URI.path, CODE_LABEL)
        uriMatcher.addURI(BACKGROUND_COLLECT_URI.authority, BACKGROUND_COLLECT_URI.path, CODE_BACKGROUND_COLLECT)
        uriMatcher.addURI(LOCAL_MEMORIES_URI.authority, LOCAL_MEMORIES_URI.path, CODE_MEMORY)
        uriMatcher.addURI(LOCAL_ALBUM_NAME_URI.authority, LOCAL_ALBUM_NAME_URI.path, CODE_ALBUM_NAME)
        uriMatcher.addURI(LOCALE_CHANGE_RECOMMEND_UPDATING_URI.authority, LOCALE_CHANGE_RECOMMEND_UPDATING_URI.path, CODE_ALBUM_LOCALE)
    }

    /**
     * 注册内容观察者
     *
     * @param resolver
     */
    fun register(context: Context) {
        if (!enabledObserver) {
            GLog.v(TAG, "[register] register media provider content observer")
            enabledObserver = true
            context.registerContentObserver(LOCAL_IMAGE_URI, true, this)
            context.registerContentObserver(LOCAL_VIDEO_URI, true, this)
            context.registerContentObserver(LOCAL_FACE_URI, true, this)
            context.registerContentObserver(LOCAL_LABEL_URI, true, this)
            context.registerContentObserver(BACKGROUND_COLLECT_URI, true, this)
            context.registerContentObserver(LOCAL_MEMORIES_URI, true, this)
            context.registerContentObserver(LOCAL_ALBUM_NAME_URI, true, this)
            context.registerContentObserver(LOCALE_CHANGE_RECOMMEND_UPDATING_URI, true, this)
        }
    }

    /**
     * 设置uri的变化为立刻执行
     */
    fun setChangedImmediately(uri: Uri) {
        changedImmediatelySet.add(uri)
    }

    /**
     * 注销观察者
     *
     * @param resolver
     */
    fun unregister(context: Context) {
        if (enabledObserver) {
            GLog.v(TAG, "[unregister] unregister media provider content observer")
            enabledObserver = false
            context.unregisterContentObserver(this)
        }
    }

    override fun onChange(selfChange: Boolean, uri: Uri?) {
        super.onChange(selfChange, uri)
        GLog.v(TAG, "[onChange], uri is " + uri + "; isRunningForeground : " + isRunningForeground())
        if (!enabledObserver) {
            GLog.v(TAG, "[onChange], enabledObserver false")
            return
        }
        val msgWhat = uriMatcher.match(uri)
        val msgDelay = when {
            changedImmediatelySet.contains(uri) -> {
                changedImmediatelySet.remove(uri)
                0
            }
            isRunningForeground() -> DELAY_TO_TRIGGER_CACHING_IN_FOREGROUND
            backgroundEnable() -> {
                if (keywordCacheManager.keywordCache.isKeywordCacheLoaded.not()) {
                    /*
                     * 相册进程未启动，被拉起，需要注册标签词典加载完成监听，否则AndesPhotosKit.INSTANCE.notifyKeywordCacheDataChanged(galleryKeywordCache)
                     * 中的labelIdToNameMap的value全为null，导致开启融合搜索 + 断网扫描相册数据后 + 杀掉相册进程 + 进入微信搜索相册标签，搜索不到标签数据
                     *
                     * 且给msgDelay设值为0，即0延时初始化缓存
                     */
                    Bus.register(this, FinderType.CLASS_METHOD_FINDER)
                    0
                } else {
                    DELAY_TO_TRIGGER_CACHING_IN_BACKGROUND
                }
            }
            else -> -1
        }
        if (msgDelay >= 0) {
            cacheHandler.removeMessages(msgWhat)
            cacheHandler.sendEmptyMessageDelayed(msgWhat, msgDelay.toLong())
        }
    }

    private fun backgroundEnable(): Boolean {
        val size = AccessPackageManager.getAccessPackagesSize()
        if (size <= 0) {
            return false
        }
        val packageName = AccessPackageManager.removeAccessPackage(size - 1)
        val foreground = isRunningForeground()
        GLog.d(
            TAG,
            "[backgroundEnable] packageName = $packageName; foreground = $foreground"
        )
        return !TextUtils.isEmpty(packageName) && !foreground
    }

    /**
     * 标签词典加载完成回调
     */
    @Subscribe
    fun onDictionaryLoaded(msg: DataManager.ReloadMessage) {
        // 在io中updateLabelIdToNameCache()，避免阻塞回调线程
        AppScope.launch(Dispatchers.IO) {
            keywordCacheManager.updateLabelIdToNameCache()
        }

        // 取消注册标签词典加载完成监听
        Bus.unregister(this)
    }

    companion object {

        private const val TAG = "${SearchConst.TAG}SearchContentObserver"

        private const val DELAY_TO_TRIGGER_CACHING_IN_BACKGROUND = TimeUtils.TIME_4_SEC_IN_MS

        private const val DELAY_TO_TRIGGER_CACHING_IN_FOREGROUND = TimeUtils.TIME_4_SEC_IN_MS

        internal const val CODE_FACE = 1
        internal const val CODE_LABEL = 2
        internal const val CODE_BACKGROUND_COLLECT = 3
        internal const val CODE_MEMORY = 4
        internal const val CODE_ALBUM_NAME = 5
        internal const val CODE_ALBUM_LOCALE = 6
    }
}