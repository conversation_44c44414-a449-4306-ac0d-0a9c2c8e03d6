<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 多模态算法访问so的权限设置:MTK芯片 -->
    <uses-permission android:name="mediatek.permission.ACCESS_APU_SYS" />

    <application
        tools:replace="android:allowBackup"
        android:allowBackup="false">
        <!-- 多模态搜索[图文检索SDK] 开始 -->
        <meta-data
            android:name="SLP_APPKEY"
            android:value="jAkUqZgciL"/>
        <meta-data
            android:name="SLP_SECRETKEY"
            android:value="zUfGGiiolAQXjRfWpZAyPWiAPAIQlQMF"/>
        <!--当授权CODE更新时，请在此处替换更新-->
        <meta-data
            android:name="SLP_CODE"
            android:value="kjFwAAAFlBMQ8AAAAAAAAAAFwAAACA8xhkAAAAAPtr4mUAAAAAOGFlNmM5ZjIxZjFlZGFlZTE1YTU0NjllYmQ5YTM1ZDI4ODJiNTQwZTQxZmYxNGU1AAAAAAH/AP8wRAIgEHmMOAxhbTDEi1IpWIcdOuoHjtXeAFySlZYkp3wAbEcCIG8ile3kU6tRccoP+WoU3pYme5QCwCcLzC0wIdITYgp7AAAAAC5G"/>
        <!--MTK平台-->
        <uses-library
            android:name="libneuron_runtime.7.so"
            android:required="false"/>
        <!--高通平台-->
        <uses-native-library
            android:name="libcdsprpc.so"
            android:required="false" />
        <!-- 多模态搜索[图文检索SDK] 结束 -->
    </application>
</manifest>