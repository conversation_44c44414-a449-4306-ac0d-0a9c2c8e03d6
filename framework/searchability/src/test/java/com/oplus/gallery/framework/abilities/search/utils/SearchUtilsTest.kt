/*********************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - .kt
 * Description:
 * Version: 1.0
 * Date: 2023/10/12
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>              2023/10/12      1.0		   INIT
 ********************************************************************************/

package com.oplus.gallery.framework.abilities.search.utils

import com.oplus.gallery.business_lib.model.data.base.entry.MediaIdEntry
import com.oplus.gallery.framework.abilities.search.utils.SearchUtils.mediaIdListToStr
import org.junit.Assert
import org.junit.Test

class SearchUtilsTest  {
    @Test
    fun should_return_right_string_when_mediaIdEntries_media_id_is_normal() {
        val entry1 = MediaIdEntry()
        val entry2 = MediaIdEntry()
        val entry3 = MediaIdEntry()
        entry1.mediaId = 1
        entry2.mediaId = 2
        entry3.mediaId = 3
        val mediaIdEntries = mutableListOf<MediaIdEntry>()
        mediaIdEntries.add(entry1)
        mediaIdEntries.add(entry2)
        mediaIdEntries.add(entry3)
        Assert.assertEquals("1,2,3", mediaIdListToStr(mediaIdEntries))
    }
}