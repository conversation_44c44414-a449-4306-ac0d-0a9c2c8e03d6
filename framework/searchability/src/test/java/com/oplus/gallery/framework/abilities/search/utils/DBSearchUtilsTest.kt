/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DBSearchUtilsTest.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2023/10/8
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/10/8      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.framework.abilities.search.utils

import io.mockk.clearStaticMockk
import io.mockk.mockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class DBSearchUtilsTest {

    @Before
    fun setUp() {
        mockkStatic(DBSearchUtils::class)
    }

    @After
    fun after() {
        clearStaticMockk(DBSearchUtils::class)
    }

    /********************************************** getSearchInfoViewSql start ****************************/

    @Test
    fun should_get_correct_result_when_getSearchInfoViewSql_with_null_input() {
        // given
        val selection: String? = null
        val limit: String = "500"
        val order: String? = null

        // when
        val result = DBSearchUtils.getSearchInfoViewSql(selection, limit, order)

        // then
        val expectResult = " SELECT local_media._id AS _id,media_id AS media_id,local_media._data AS _data," +
                "datetaken AS datetaken,date_modified AS date_modified,face_name, replace (address,',','|') AS address," +
                "tags,english_tags,ocr,bucket_display_name AS bucket_name,bucket_id AS bucket_id,_display_name AS _display_name,title AS title," +
                "_size AS _size,local_media.media_type AS media_type,mime_type AS mime_type,duration AS duration," +
                "longitude AS longitude,latitude AS latitude, group_concat (festival_name,'|') AS festival_name," +
                "memory_name,sex,age,tagflags FROM ( SELECT _id,media_id,_data,datetaken,date_modified,bucket_display_name," +
                "bucket_id,_display_name,title,_size,media_type,mime_type,duration,longitude,latitude,tagflags,gps_key," +
                "day FROM local_media WHERE (local_media.is_trashed=0) AND _id > 0 LIMIT 500)local_media " +
                "LEFT OUTER JOIN ( SELECT  group_concat (scene_id,'|') AS tags, group_concat (scene_id,'|') AS english_tags,_data FROM scan_label " +
                "WHERE scene_id > 0  GROUP BY _data)scan_label ON local_media._data = scan_label._data " +
                "LEFT OUTER JOIN ( SELECT  group_concat (group_name,'|') AS face_name, " +
                "group_concat (sex,'|') AS sex, group_concat (age,'|') AS age,_data FROM scan_face " +
                "GROUP BY _data)scan_face ON local_media._data = scan_face._data " +
                "LEFT OUTER JOIN ( SELECT address,gps_key FROM geo_route)geo_route ON local_media.gps_key = geo_route.gps_key " +
                "LEFT OUTER JOIN ( SELECT festival_name,date,region FROM festival " +
                "WHERE region = 'cn,oc' AND filterType & 1 = 1)festival ON local_media.day = festival.date " +
                "LEFT OUTER JOIN ( SELECT content AS ocr,_data FROM ocr_pages_v2 GROUP BY _data)ocr_pages_v2 " +
                "ON local_media._data = ocr_pages_v2._data LEFT OUTER JOIN ( SELECT _data, group_concat (name,'|') AS memory_name " +
                "FROM memories_setmap INNER JOIN memories_set ON set_id = _id GROUP BY _data)memories_setmap " +
                "ON local_media._data = memories_setmap._data GROUP BY local_media._data ORDER BY local_media._id DESC "
        assertEquals(expectResult, result)
    }

    @Test
    fun should_get_correct_result_when_getSearchInfoViewSql_with_not_null_input() {
        // given
        val selection = "local_media.media_id IN(1,2)"
        val limit = "1000"
        val order = "_id ASC"

        // when
        val result = DBSearchUtils.getSearchInfoViewSql(selection, limit, order)

        // then
        val expectResult = " SELECT local_media._id AS _id,media_id AS media_id,local_media._data AS _data,datetaken AS datetaken," +
                "date_modified AS date_modified,face_name, replace (address,',','|') AS address,tags,english_tags,ocr," +
                "bucket_display_name AS bucket_name,bucket_id AS bucket_id,_display_name AS _display_name,title AS title," +
                "_size AS _size,local_media.media_type AS media_type,mime_type AS mime_type,duration AS duration," +
                "longitude AS longitude,latitude AS latitude, group_concat (festival_name,'|') AS festival_name," +
                "memory_name,sex,age,tagflags FROM ( SELECT _id,media_id,_data,datetaken,date_modified,bucket_display_name,bucket_id," +
                "_display_name,title,_size,media_type,mime_type,duration,longitude,latitude,tagflags,gps_key,day " +
                "FROM local_media WHERE (local_media.is_trashed=0) AND local_media.media_id IN(1,2))local_media " +
                "LEFT OUTER JOIN ( SELECT  group_concat (scene_id,'|') AS tags, group_concat (scene_id,'|') AS english_tags,_data FROM scan_label " +
                "WHERE scene_id > 0  GROUP BY _data)scan_label ON local_media._data = scan_label._data " +
                "LEFT OUTER JOIN ( SELECT  group_concat (group_name,'|') AS face_name, " +
                "group_concat (sex,'|') AS sex, group_concat (age,'|') AS age,_data FROM scan_face " +
                "GROUP BY _data)scan_face ON local_media._data = scan_face._data LEFT OUTER JOIN " +
                "( SELECT address,gps_key FROM geo_route)geo_route ON local_media.gps_key = geo_route.gps_key " +
                "LEFT OUTER JOIN ( SELECT festival_name,date,region FROM festival WHERE region = 'cn,oc' AND filterType & 1 = 1)festival " +
                "ON local_media.day = festival.date LEFT OUTER JOIN ( SELECT content AS ocr,_data FROM ocr_pages_v2 GROUP BY _data)ocr_pages_v2 " +
                "ON local_media._data = ocr_pages_v2._data LEFT OUTER JOIN ( SELECT _data, group_concat (name,'|') AS memory_name " +
                "FROM memories_setmap INNER JOIN memories_set ON set_id = _id GROUP BY _data)memories_setmap " +
                "ON local_media._data = memories_setmap._data GROUP BY local_media._data ORDER BY _id ASC"
        assertEquals(expectResult, result)
    }
    /********************************************** getSearchInfoViewSql end ****************************/
}