<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/common_app_name">
    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="pref_key_select_sync_album"
        app:isPreferenceVisible="false" >

        <com.coui.appcompat.preference.COUIMarkPreference
            android:key="pref_key_sync_all_album"
            android:title="@string/cloudsync_sync_albums_all"
            app:couiShowDivider="false" />

        <com.coui.appcompat.preference.COUIMarkPreference
            android:key="pref_key_custom_sync_album"
            android:title="@string/cloudsync_sync_albums_customize"
            app:couiShowDivider="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPagerHeaderPreference
        android:key="pref_key_sync_select_summary"
        android:selectable="false"
        android:summary="@string/cloudsync_sync_album_without_sd"
        app:isPreferenceVisible="false"/>

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="pref_key_local_album_list" />

    <com.coui.appcompat.preference.COUIPagerFooterPreference
        android:key="pref_key_head_summary"
        android:selectable="false"
        android:summary="@string/cloudsync_sync_select_album_except_sdcard"
        app:isPreferenceVisible="false"/>

    <com.coui.appcompat.preference.COUIBottomPreference />
</androidx.preference.PreferenceScreen>
