<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Access cloud for cloud ocr and cloud label and cloud gallery. -->
    <uses-permission android:name="heytap.permission.cloud.ACCESS_SYNC_SERVICE" />
    <uses-permission android:name="heytap.permission.cloud.ACCESS_CLOUD" />
    <!--    请求后台服务需要网络权限，lint检查需要在此模块声明此权限-->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <!-- Required only if your app targets Android 13. -->
    <!-- Declare one or more the following permissions only if your app needs
    to access data that's protected by them. -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <!-- Required to maintain app compatibility. -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <!-- 退出账号广播权限   -->
    <uses-permission android:name="com.heytap.usercenter.permission.RECIEVE_MESSAGE_ACCOUNT"/>

    <!-- 此权限为clientId针对低版本Android系统读取IMEI所需，Q以上使用stdId无需该权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" tools:node="remove"/>

    <!-- 老机器未内置支付apk需要下载安装所需，Q以上不需要 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" tools:node="remove"/>

    <application
        tools:replace="android:allowBackup"
        android:allowBackup="false">

        <service
            android:name="com.heytap.cloudkit.libsync.service.CloudSyncService"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.heytap.cloudkit.libsync.service" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
        <provider
            android:name="com.heytap.cloudkit.libcommon.provider.CloudAcrossProcDataProvider"
            android:enabled="true"
            android:exported="false"
            android:multiprocess="false"
            android:authorities="${applicationId}.CloudAcrossProcDataProvider"/>

        <provider
            android:name="com.oplus.gallery.framework.abilities.ocloudsync.account.CloudAccountInitProvider"
            android:enabled="true"
            android:exported="false"
            android:multiprocess="true"
            android:authorities="${applicationId}.CloudAccountInitProvider" />
        <activity
            android:name="com.oplus.gallery.framework.abilities.ocloudsync.ui.ShareToCloudAlbumActivity"
            android:label="@string/model_cloud_share_album_set"
            android:enabled="false"
            android:exported="false"
            android:theme="@style/TransparentTheme">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
                <data android:mimeType="video/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
                <data android:mimeType="video/*" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.gallery.framework.abilities.ocloudsync.ui.CloudSyncAlbumActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize|mcc|mnc"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:icon="@drawable/app_icon_system_application_gallery3d"
            android:resizeableActivity="true"
            android:screenOrientation="user"
            android:theme="@style/cloudsync_Theme.CloudSyncAlbum"
            tools:ignore="Instantiatable" >

            <intent-filter>
                <action android:name="coloros.intent.action.view.sync_dir" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.view.sync_dir" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <service
            android:name="com.oplus.gallery.framework.abilities.ocloudsync.compatible.GallerySyncCompatible"
            android:exported="true"
            android:permission="heytap.permission.cloud.ACCESS_SYNC_SERVICE"
            tools:ignore="Instantiatable">
            <meta-data
                android:name="module"
                android:value="album" />
            <meta-data
                android:name="hasSmallBinaryFiles"
                android:value="false" />
            <meta-data
                android:name="isMainModule"
                android:value="true" />

            <intent-filter>
                <action android:name="com.heytap.cloud.action.SYNC_MODULE" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <service
            android:name="com.oplus.gallery.framework.abilities.ocloudsync.compatible.GallerySyncAlbumCompatible"
            android:exported="true"
            android:permission="heytap.permission.cloud.ACCESS_SYNC_SERVICE"
            tools:ignore="Instantiatable">
            <meta-data
                android:name="module"
                android:value="album_dir" />
            <meta-data
                android:name="hasFile"
                android:value="false" />
            <meta-data
                android:name="isMainModule"
                android:value="false" />

            <intent-filter>
                <action android:name="com.heytap.cloud.action.SYNC_MODULE" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>
        <receiver
            android:name=".account.AccountManager$LoginReceiver"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="com.heytap.usercenter.account_logout"/>
            </intent-filter>
        </receiver>
        <service
            android:name="com.oplus.gallery.framework.abilities.basecloudability.sync.SyncConditionService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
    </application>
</manifest>