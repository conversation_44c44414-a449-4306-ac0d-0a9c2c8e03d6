/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CloudDebugUtil.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON><PERSON><PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.util

import android.content.Context
import com.heytap.cloudkit.libcommon.netrequest.CloudHttpStatusCode
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError
import com.oplus.cloudkitlib.base.SyncLock
import com.oplus.gallery.framework.abilities.basecloudability.base.EnvConfig
import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.gallery.business_lib.cloudsync.SyncResultConstants
import com.oplus.cloudkitlib.metadata.AbsMetaData
import com.oplus.cloudkitlib.metadata.BackupDataResult
import com.oplus.cloudkitlib.metadata.IMetaDataListener
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_GALLERY_CLOUD_SYNC_ALBUM_META_DATA_INVALID_TRACK
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.ocloudsync.OCloudSyncApi
import com.oplus.gallery.framework.abilities.ocloudsync.statistics.CloudSyncTrackHelper

object CloudDebugUtil {

    const val TAG = "CloudDebugUtil"

    private const val DEBUG_PROP = "debug.cloudkit.types"
    private const val DEBUG_NEED_FETCH = "debug.cloudkit.needfetch"
    private const val DEBUG_THREAD_SLEEP = 2000L

    private const val LIMIT_PERCENT_DEBUG = 0.1
    private const val MIN_TRANSFER_SPEED_DEBUG = 10.0

    const val RECOVERY_ON_ERROR = 101 //recovery返回失败的结果
    const val RECOVERY_SKIP = 102 //跳过恢复数据，用于制造数据冲突
    private const val RECOVERY_NETWORK_ERROR = 103 // 恢复时网络异常
    private const val RECOVERY_REQUEST_TOO_FREQUENT = 104 // 限流

    const val BACKUP_ON_ERROR = 201 //备份返回失败的结果
    const val BACKUP_SKIP = 202 //调过备份数据，用于制造异常场景
    const val BACKUP_ON_ERROR_PART = 203 //备份返回部分数据成功的结果

    const val TRANSFER_ERROR_BEFORE = 301 //传输文件服务器直接返回错误
    const val TRANSFER_ERROR_DOING = 302 //传输文件过程中服务器返回错误
    const val TRANSFER_INTERRUPT_DOING = 305//传输文件直接中断
    const val TRANSFER_INTERRUPT_BATCH = 306//传输文件返回批量结果前中断
    const val TRANSFER_SPEED_LIMIT = 307 //限制传输速度
    const val TRANSFER_ERROR_NETWORK = 308 // 传输文件时网络异常
    /* debug使用的模拟的错误码，无对应场景，只会中断不会处理 */
    const val DEBUG_RESULT_CODE = 1000

    enum class DebugStage(val code: Int) {
        MEDIA_FILE_RECOVERY(1),
        MEDIA_FILE_BACKUP(MEDIA_FILE_RECOVERY.code shl 1),
        ALBUM_RECOVERY(MEDIA_FILE_BACKUP.code shl 1),
        ALBUM_BACKUP(ALBUM_RECOVERY.code shl 1),
        FILE_UPLOAD(ALBUM_BACKUP.code shl 1),
        FILE_DOWNLOAD_THUMB(FILE_UPLOAD.code shl 1),
        FILE_DOWNLOAD_COMPRESS(FILE_DOWNLOAD_THUMB.code shl 1),
        FILE_DOWNLOAD_ORIGIN(FILE_DOWNLOAD_COMPRESS.code shl 1)
    }

    enum class DebugInterruptStage(val code: Int) {
        MEDIA_FILE_RECOVERY(1),
        MEDIA_FILE_BACKUP(MEDIA_FILE_RECOVERY.code shl 1),
        ALBUM_RECOVERY(MEDIA_FILE_BACKUP.code shl 1),
        ALBUM_BACKUP(ALBUM_RECOVERY.code shl 1),
        FILE_UPLOAD(ALBUM_BACKUP.code shl 1),
        FILE_DOWNLOAD_THUMB(FILE_UPLOAD.code shl 1),
        FILE_DOWNLOAD_COMPRESS(FILE_DOWNLOAD_THUMB.code shl 1),
        FILE_DOWNLOAD_ORIGIN(FILE_DOWNLOAD_COMPRESS.code shl 1)
    }

    private const val TEST_SYS_RECORD_ID = "test_record_id"

    /**
     * 检查是否debug当前阶段，当前行为为将同步结果设置为异常
     */
    @JvmStatic
    fun checkDebugResult(stage: DebugStage, result: SyncResult) {
        GLog.d(TAG, "checkDebugResult, stage=$stage, result=$result")
        if (isDebugCurrentStage(stage)) {
            GLog.d(TAG, "checkDebugResult, isDebugCurrentStage stage=$stage")
            result.isSuccess = false
            result.resultCode = DEBUG_RESULT_CODE
        }
    }

    @JvmStatic
    fun debugRun(runnable: Runnable) {
        Thread {
            try {
                Thread.sleep(DEBUG_THREAD_SLEEP)
            } catch (e: InterruptedException) {
                //Do nothing
            }
            runnable.run()
        }.start()
    }

    @JvmStatic
    fun isDebug(envConfig: EnvConfig, type: Int): Boolean {
        return if (envConfig == EnvConfig.PROCESS_TEST) {
            getDebugType(envConfig) == type
        } else {
            false
        }
    }

    @JvmStatic
    fun getDebugType(envConfig: EnvConfig): Int? {
        if (envConfig != EnvConfig.PROCESS_TEST) {
            return null
        }
        val debugTypeProp = GallerySystemProperties.get(DEBUG_PROP)
        GLog.d(TAG, "getDebugType $debugTypeProp")
        var result: Int? = null
        debugTypeProp?.apply {
            result = try {
                this.toInt()
            } catch (e: NumberFormatException) {
                null
            }
        }
        return result
    }

    @JvmStatic
    fun <T : AbsMetaData<*>> recoveryMetaData(
        envConfig: EnvConfig,
        metaDataListener: IMetaDataListener<T>
    ): SyncResult? {
        when {
            isDebug(envConfig, RECOVERY_ON_ERROR) -> {
                val syncLock = SyncLock()
                debugRun {
                    GLog.d(TAG, "recoveryMetaData debug on RECOVERY_ON_ERROR")
                    val error = CloudKitError.ERROR_RECOVERY_RESPONSE
                    metaDataListener.onMetaDataBatchRecoveryError(error)
                    syncLock.notifyFinish(SyncResult(error))
                }
                return syncLock.waitReturn()
            }
            isDebug(envConfig, RECOVERY_SKIP) -> {
                val syncLock = SyncLock()
                debugRun {
                    GLog.d(TAG, "recoveryMetaData debug on RECOVERY_SKIP")
                    metaDataListener.onMetaDataBatchRecoverySuccess(emptyList(), false, 0, 0)
                    syncLock.notifyFinish(SyncResult(true, SyncResultConstants.RESULT_SUCCESS))
                }
                return syncLock.waitReturn()
            }
            isDebug(envConfig, RECOVERY_NETWORK_ERROR) -> {
                val syncLock = SyncLock()
                debugRun {
                    GLog.d(TAG, "recoveryMetaData debug on RECOVERY_NETWORK_ERROR")
                    val error = CloudKitError(-1, "debug error", CloudBizError.NETWORK)
                    metaDataListener.onMetaDataBatchRecoveryError(error)
                    syncLock.notifyFinish(SyncResult(error))
                }
                return syncLock.waitReturn()
            }
            isDebug(envConfig, RECOVERY_REQUEST_TOO_FREQUENT) -> {
                val syncLock = SyncLock()
                debugRun {
                    GLog.d(TAG, "recoveryMetaData debug on RECOVERY_REQUEST_TOO_FREQUENT")
                    val error = CloudKitError(-1, "debug error", CloudBizError.FAIL)
                    error.subServerErrorCode = CloudHttpStatusCode.BizFocusServerCode.HTTP_REQUEST_TOO_FREQUENT
                    metaDataListener.onMetaDataBatchRecoveryError(error)
                    syncLock.notifyFinish(SyncResult(error))
                }
                return syncLock.waitReturn()
            }
            else -> return null
        }
    }

    @JvmStatic
    fun <T : AbsMetaData<*>> backupMetaData(
        envConfig: EnvConfig,
        backupList: List<T>,
        iMetaDataListener: IMetaDataListener<T>
    ): SyncResult? {
        when {
            isDebug(envConfig, BACKUP_ON_ERROR) -> {
                val syncLock = SyncLock()
                debugRun {
                    GLog.d(TAG, "backupMetaData debug on error")
                    val error = CloudKitError.ERROR_BACKUP_RESPONSE
                    iMetaDataListener.onMetaDataBatchBackupError(error)
                    syncLock.notifyFinish(SyncResult(error))
                }
                return syncLock.waitReturn()
            }
            isDebug(envConfig, BACKUP_SKIP) -> {
                val syncLock = SyncLock()
                debugRun {
                    GLog.d(TAG, "backupMetaData debug on BACKUP_SKIP")
                    val backupResult = BackupDataResult<T>(emptyList(), emptyList(), emptyList())
                    iMetaDataListener.onMetaDataBatchBackupServerResult(emptyList())
                    syncLock.notifyFinish(SyncResult(backupResult))
                }
                return syncLock.waitReturn()
            }
            isDebug(envConfig, BACKUP_ON_ERROR_PART) -> {
                val syncLock = SyncLock()
                debugRun {
                    GLog.d(TAG, "backupMetaData debug on error")
                    val errorList = mutableListOf<CloudBackupResponseError>()
                    backupList.forEach {
                        val error = CloudBackupResponseError()
                        error.sysRecordId = it.sysRecordId
                        error.cloudKitError = CloudKitError.ERROR_BACKUP_RESPONSE_NULL
                        errorList.add(error)
                    }
                    val backupResult = BackupDataResult(backupList, emptyList(), errorList)
                    iMetaDataListener.onMetaDataBatchBackupServerResult(backupList)
                    syncLock.notifyFinish(SyncResult(backupResult))
                }
                return syncLock.waitReturn()
            }
            else -> return null
        }
    }

    @JvmStatic
    fun speedLimit(envConfig: EnvConfig) {
        if (isDebug(envConfig, TRANSFER_SPEED_LIMIT)) {
            GLog.d(TAG, "execute debug on TRANSFER_SPEED_LIMIT")
            OCloudSyncApi.apiClient.setIOSpeedLimiter(LIMIT_PERCENT_DEBUG, MIN_TRANSFER_SPEED_DEBUG, MIN_TRANSFER_SPEED_DEBUG)
        } else if (envConfig != EnvConfig.RELEASE) {
            OCloudSyncApi.apiClient.cancelIOSpeedLimiter()
        }
    }

    @JvmStatic
    fun speedLimitWait(envConfig: EnvConfig) {
        if (isDebug(envConfig, TRANSFER_SPEED_LIMIT)) {
            GLog.d(TAG, "execute debug on speedLimitWait")
            try {
                Thread.sleep(DEBUG_THREAD_SLEEP)
            } catch (e: InterruptedException) {
                //Do nothing
            }
        }
    }

    @JvmStatic
    fun isNeedFetch(): Boolean = GallerySystemProperties.getBoolean(DEBUG_NEED_FETCH, false)

    @JvmStatic
    private fun isDebugCurrentStage(stage: DebugStage): Boolean {
        if (GProperty.DEBUG.not()) return false
        GLog.d(TAG, "isDebugCurrentStage, stage=$stage")
        val debugStage = GProperty.DEBUG_GALLERY_CLOUD_SYNC_TRACK_LINK_STAGE
        return stage.code and debugStage > 0
    }

    @JvmStatic
    private fun isDebugInterruptStage(stage: DebugStage): Boolean {
        if (GProperty.DEBUG.not()) return false
        GLog.d(TAG, "isDebugCurrentStage, stage=$stage")
        val debugStage = GProperty.DEBUG_GALLERY_CLOUD_SYNC_TRACK_LINK_STAGE
        return stage.code and debugStage > 0
    }

    /**
     * 用于测试云服务下发异常数据的埋点上报，因为目前无法以常规操作模拟出来云端下发异常数据，所以直接模拟上报异常，保证接口通道可行可测
     */
    @JvmStatic
    fun trackAlbumMetadataInvalidIfTestMode(context: Context) {
        if (GProperty.DEBUG.not()) return
        if (DEBUG_GALLERY_CLOUD_SYNC_ALBUM_META_DATA_INVALID_TRACK) {
            GLog.d(TAG, "trackSysVersionInvalidIfTestMode")
            CloudSyncTrackHelper.saveInvalidSysVersion(
                CloudSyncTrackConstant.Value.ErrorScene.InvalidSysVersion.REASON_CLOUD_BACKUP,
                0,
                TEST_SYS_RECORD_ID
            )
            CloudSyncTrackHelper.saveInvalidSysVersion(
                CloudSyncTrackConstant.Value.ErrorScene.InvalidSysVersion.REASON_CLOUD_RECOVERY,
                0,
                TEST_SYS_RECORD_ID
            )
            CloudSyncTrackHelper.saveInvalidSysRecordId(
                CloudSyncTrackConstant.Value.ErrorScene.InvalidSysRecordId.REASON_RESET_FOR_SYS_VERSION_INVALID,
                TextUtil.EMPTY_STRING,
                1
            )
            CloudSyncTrackHelper.saveInvalidSysRecordId(
                CloudSyncTrackConstant.Value.ErrorScene.InvalidSysRecordId.REASON_CLOUD_RECOVERY,
                TextUtil.EMPTY_STRING
            )
            CloudSyncTrackHelper.trackAlbumMetadataInvalid(context)
        }
    }
}