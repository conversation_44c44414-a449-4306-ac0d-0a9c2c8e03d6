/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FileTransferTaskManager.kt
 ** Description: 文件传输类，实现了任务优先级功能
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>alin @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import android.os.Process
import android.os.SystemClock
import com.heytap.cloudkit.libcommon.bean.io.CloudIOType
import com.heytap.cloudkit.libcommon.bean.io.CloudStopType
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.io.CloudIOFileListener
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.cloudkitlib.base.CustomCloudKitError
import com.oplus.cloudkitlib.base.SyncLock
import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.cloudkitlib.metadata.ISyncAllow
import com.oplus.cloudkitlib.strategy.ParallelDownloadCountStrategy
import com.oplus.cloudkitlib.util.CloudDebugUtil
import com.oplus.gallery.addon.osense.LongBgTaskQuotaManager
import com.oplus.gallery.business_lib.cloud.DownloadSpec
import com.oplus.gallery.business_lib.cloud.TransferPriority
import com.oplus.gallery.business_lib.cloudsync.SyncResultConstants
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.thread.PriorityThreadFactory
import com.oplus.gallery.framework.abilities.basecloudability.base.EnvConfig
import com.oplus.gallery.framework.abilities.ocloudsync.OCloudSyncApi
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import java.net.URI
import java.util.PriorityQueue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors

/**
 * 提交的任务会进行优先级排序，优先级高的先执行
 * 插入优先级高的任务，会把优先级低的任务暂停，重新插入排队队列
 * 大文件会分块多线程并行上传/下载，所以同时进行的大文件任务（上传和下载合起来）只能有一个
 * 不会同时上传和下载大文件，除此之外，上传和下载互不影响
 * 可根据任务id取消执行的任务
 * @param envConfig 环境
 * @param parallelFileCount 并发数量，上传/下载各自独立计算
 * @param syncAllow 快稳省策略
 */
class FileTransferTaskManager(
    val envConfig: EnvConfig,
    private val parallelFileCount: Int,
    private val syncAllow: ISyncAllow
) {
    private val bgParallelDownloadCountStrategy = ParallelDownloadCountStrategy()
    private val lowPriorityParallelDownCount
        get() = bgParallelDownloadCountStrategy.parallelCount


    interface ICallBack<T : CloudIOFile> {
        /**
         * @param file 传输的文件
         * @param cloudDateType cloudkit数据类型
         * @param progress 0-1 double值
         */
        fun onProgress(file: T, cloudDateType: CloudDataType, progress: Double)
        fun onFinish(file: T, cloudDateType: CloudDataType, customCloudKitError: CustomCloudKitError)
        fun batchResult(
            successFileList: List<T>,
            errorFileList: List<CloudIOErrorFile<T>>,
            cloudDateType: CloudDataType
        )
    }

    /**
     * 正在执行的任务
     * key:cloudIOFile的key,具有唯一性
     * value:任务对象,一个cloudIOFile 对应一个任务
     */
    private val runningMap: MutableMap<String, TransferTask> = HashMap()

    /**
     * 排队的任务队列
     */
    private val waitQueue = PriorityQueue<TransferTask>()

    /**
     * 排队的任务，1，因为queue队列查找速度慢，额外增加map去加快查找， 2，priorityQueue 没有去重逻辑，用map补充去重逻辑
     *  key:cloudIOFile的key,具有唯一性
     *  value:任务对象，一个cloudIOFile 对应一个任务
     */
    private val waitMap: MutableMap<String, TransferTask> = HashMap()

    /**
     * 存储任务id和文件的对应关系，用于取消任务时，查找到对应的任务 ,多线程操作，不使用concurrent可能会发生死锁问题
     * key:任务的回调Id
     * value:cloudIOFile 监听的文件对象
     */
    private val callbackIdToFileMap: MutableMap<Int, CloudIOFile> = ConcurrentHashMap()

    /**
     * 存储下载记录，用于提前拦截相同路径尺寸没有递进的下载请求
     * key: 文件路径
     * value: 下载记录
     */
    private val downloadedRecordMap: MutableMap<String, DownloadedRecord> = mutableMapOf()

    /**
     * 用于waitQueue waitMap runningMap 的锁，防止数据不同步
     */
    private val lock = Any()

    /**
     * 用于异步调用sdk接口，避免sdk接口出现耗时甚至阻塞时卡住主线程或者相册逻辑
     */
    private val Dispatchers.singleInvokeSDK by lazy {
        Executors.newSingleThreadExecutor(
            PriorityThreadFactory(
                DISPATCHER_NAME_SINGLE_INVOKE_SDK,
                Process.THREAD_PRIORITY_DEFAULT
            )
        ).asCoroutineDispatcher()
    }

    /**
     * 用于记录CK的服务是否启动
     */
    @Volatile
    private var isServiceStarted: Boolean = false

    /**
     * 批量上传/下载文件 兼容原有的调用，前台任务优先级设为最高
     * @param source 同步类型，查看SourceFlag
     * @param frontTask 前台任务还是后台任务
     * @param files 需要传输的文件列表
     * @param callback
     */
    fun <T : CloudIOFile> execute(
        source: Int,
        frontTask: Boolean,
        files: List<T>,
        callback: ICallBack<T>
    ): SyncResult {
        var priority = TransferPriority.DEFAULT
        if (frontTask) {
            priority = TransferPriority.MAX
        }
        return execute(source, priority, files, callback)
    }

    /**
     * 批量上传/下载文件
     * @param source 同步类型，查看SourceFlag
     * @param priority 任务优先级
     * @param files 需要传输的文件列表
     * @param callback
     */
    fun <T : CloudIOFile> execute(
        source: Int,
        priority: TransferPriority,
        files: List<T>,
        callback: ICallBack<T>
    ): SyncResult {
        if (files.isEmpty()) {
            // 因为 cloudDataType 放在 file 中，如果 files 为空，无法获取到 cloudDataType，只能传回默认的 CloudDataType.PRIVATE.
            callback.batchResult(files, ArrayList(), CloudDataType.PRIVATE)
            return SyncResult(true, SyncResultConstants.RESULT_SUCCESS)
        }
        val syncLock = SyncLock()
        val fileResults: MutableMap<String, CloudKitError?> = HashMap()
        files.forEach {
            fileResults[it.getKey(it.getCloudDataType())] = null
        }
        GLog.d(TAG, LogFlag.DL, "[execute] insert ${files.size} data priority:$priority ")
        val successFileList: MutableList<T> = ArrayList()
        val errorFileList: MutableList<CloudIOErrorFile<T>> = ArrayList()
        var needPauseTasks: List<TransferTask>? = null
        var addTaskCount = 0
        synchronized(lock) {
            files.forEach { cloudIOFile ->
                val transferCallBack = TransferCallback(
                    callback,
                    fileResults,
                    cloudIOFile,
                    successFileList,
                    errorFileList
                )
                if (addTask(source, priority, cloudIOFile, transferCallBack, syncLock)) {
                    addTaskCount++
                } else {
                    fileResults.remove(cloudIOFile.getKey(cloudIOFile.getCloudDataType()))
                }
            }
            if (addTaskCount > 0) {
                needPauseTasks = getNeedPauseTask(priority, files.size, files)
            }
        }
        if (addTaskCount == 0) {
            return SyncResult(true, SyncResultConstants.RESULT_SUCCESS)
        }
        needPauseTasks?.forEach { transferTask ->
            GLog.d(TAG, LogFlag.DL, "[execute] need pause task file path: ${PathMask.mask(transferTask.cloudIOFile.filePath)}")
            stopTransferFile(transferTask, transferTask.cloudIOFile.getCloudDataType(), CloudStopType.LIMIT, LIMIT_PAUSE_ERROR_CODE)
        }
        trigger()
        return syncLock.waitReturn()
    }

    /**
     * 插入单个任务
     * @param source 环境
     * @param priority 优先级
     * @param file 传输的文件
     * @param callback 回调
     * @return 任务id ,可通过这个id取消任务
     */
    fun <T : CloudIOFile> enqueue(
        source: Int,
        priority: TransferPriority,
        file: T,
        callback: ICallBack<T>
    ): Int {
        GLog.d(TAG, LogFlag.DL) {
            "[enqueue] insert data priority:$priority syncType:${file.type.toSyncTypeString()} path: ${PathMask.mask(file.filePath)}"
        }
        val fileResults: MutableMap<String, CloudKitError?> = HashMap(1)
        fileResults[file.getKey(file.getCloudDataType())] = null
        val successFileList: MutableList<T> = ArrayList()
        val errorFileList: MutableList<CloudIOErrorFile<T>> = ArrayList()
        val files: MutableList<T> = ArrayList()
        files.add(file)
        val transferCallBack = TransferCallback(
            callback,
            fileResults,
            file,
            successFileList,
            errorFileList,
        )
        var needPauseTasks: List<TransferTask>? = null
        synchronized(lock) {
            if (addTask(source, priority, file, transferCallBack, null)) {
                needPauseTasks = getNeedPauseTask(priority, files.size, listOf(file))
            } else {
                fileResults.remove(file.getKey(file.getCloudDataType()))
            }
        }
        //放在锁外面,stopTransferFile为跨进程，可能存在耗时操作
        needPauseTasks?.forEach { transferTask ->
            GLog.d(
                TAG, LogFlag.DL, "[enqueue] need pause task file path: ${PathMask.mask(transferTask.cloudIOFile.filePath)} " +
                        "priority=${transferTask.priority}"
            )
            stopTransferFile(transferTask, transferTask.cloudIOFile.getCloudDataType(), CloudStopType.LIMIT, LIMIT_PAUSE_ERROR_CODE)
        }
        trigger()
        return transferCallBack.id
    }

    /**
     * 初始化并行下载策略
     */
    fun initParallelDownloadCountStrategy(downloadSpec: DownloadSpec) {
        bgParallelDownloadCountStrategy.downloadSpec = downloadSpec
    }

    /**
     * 尝试调节并行下载线程数据
     */
    fun tryAdjustParallelCountBySpeed(internetSpeed: Long) {
        bgParallelDownloadCountStrategy.tryAdjustParallelCountBySpeed(internetSpeed)
    }

    /**
     * 添加任务
     * @param source 环境
     * @param priority 任务优先级
     * @param cloudIOFile cloudkit 标准传输文件
     * @param transferCallBack 回调
     * @param syncLock 等待锁，批量传输需要用到
     * @return 是否成功添加任务
     */
    @Suppress("LongMethod")
    private fun <T : CloudIOFile> addTask(
        source: Int,
        priority: TransferPriority,
        cloudIOFile: T,
        transferCallBack: TransferCallback<T>,
        syncLock: SyncLock?
    ): Boolean {
        transferCallBack.syncLock = syncLock
        val key = cloudIOFile.getKey(cloudIOFile.getCloudDataType())
        //去重逻辑，如果这个文件已经在排队队列中，那么就要找到正在下这个文件的task，然后添加回调
        if (waitMap.containsKey(key)) {
            GLog.d(TAG, LogFlag.DL) {
                "[addTask] waitMap contains key file syncType:${cloudIOFile.type.toSyncTypeString()} " +
                        "priority:$priority " +
                        "path: ${PathMask.mask(cloudIOFile.filePath)}"
            }
            val task = waitMap[key]
            task?.addCallback(transferCallBack)
            task?.let {
                //如果新插入的任务是MAX等级，或者插入的任务比之前的任务优先级更高，将那么更新task的等级和时间，并重新插入排序。
                if ((priority.value > it.priority.value) || (priority == TransferPriority.MAX)) {
                    it.priority = priority
                    it.createTime = System.currentTimeMillis()
                    waitQueue.remove(it)
                    waitQueue.add(it)
                }
            }
        } else if (runningMap.containsKey(key)) {
            GLog.d(TAG, LogFlag.DL, "[addTask] runningMap contains key file path :${PathMask.mask(cloudIOFile.filePath)}")
            val task = runningMap[key]
            task?.let {
                //如果新插入的任务比排队队列中的大，那么将此文件的task设置成更高级别,并更新时间
                if (priority.value > it.priority.value) {
                    it.priority = priority
                    it.createTime = System.currentTimeMillis()
                }
                it.addCallback(transferCallBack)
                it.onStart()
            }
        } else {
            if (cloudIOFile.type == CloudIOType.DOWNLOAD.type) {
                downloadedRecordMap.entries.filter {
                    (SystemClock.elapsedRealtime() - it.value.downloadFinishTime) > INTERVAL_DOWNLOAD
                }.forEach {
                    downloadedRecordMap.remove(it.key)
                }
                downloadedRecordMap[cloudIOFile.filePath]
                    ?.takeIf {
                        ((SystemClock.elapsedRealtime() - it.downloadFinishTime) <= INTERVAL_DOWNLOAD) &&
                                (DownloadFileSizeComparator().compare(it.file, cloudIOFile) >= 0)
                    }?.also {
                        GLog.e(
                            TAG, LogFlag.DL, "[addTask] skip, path:${PathMask.mask(cloudIOFile.filePath)}, " +
                                    "same or bigger size file just been downloaded, please check request size"
                        )
                        return false
                    }
            }
            addNewTaskToWaitQueue(cloudIOFile, key, priority, transferCallBack, source)
        }
        callbackIdToFileMap[transferCallBack.id] = cloudIOFile
        return true
    }

    private fun <T : CloudIOFile> addNewTaskToWaitQueue(
        cloudIOFile: T,
        key: String,
        priority: TransferPriority,
        transferCallBack: TransferCallback<T>,
        source: Int
    ) {
        val task = object : TransferTask(
            key,
            cloudIOFile,
            priority,
            System.currentTimeMillis(),
            transferCallBack,
            source
        ) {
            override fun onFinish(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                synchronized(lock) {
                    // 返回的 file 是原生的 CloudIOFile，并非 CloudIOFile 的子类，不能用来获取 CloudDataType.
                    runningMap.remove(file.getKey(cloudDateType))
                    if (cloudKitError.isSuccess && (cloudIOFile.type == CloudIOType.DOWNLOAD.type)) {
                        downloadedRecordMap[file.filePath] = DownloadedRecord(file, SystemClock.elapsedRealtime())
                        stopSmallerSizeDownloadTask(file)
                    }
                }

                var waitAgain = false
                when {
                    // 如果是因为任务优先级主动暂停，那么将任务重新放到任务队列中
                    cloudKitError.bizSubErrorCode == LIMIT_PAUSE_ERROR_CODE -> {
                        GLog.d(
                            TAG, LogFlag.DL, "[addTask] onFinish: error code is LIMIT_PAUSE_ERROR_CODE refresh in queue. " +
                                    "filePath: ${PathMask.mask(file.filePath)} cloudKitError:$cloudKitError"
                        )
                        waitAgain = true
                    }
                    // 如果是手动取消后又再次外部请求，那么将任务重新放到任务队列中
                    (cloudKitError.bizErrorCode == CloudBizError.MANUAL_STOP.code) && (taskState == TaskState.RUNNING) -> {
                        GLog.d(
                            TAG, LogFlag.DL, "[addTask] onFinish: error code is MANUAL_STOP refresh in queue. " +
                                    "filePath: ${PathMask.mask(file.filePath)} cloudKitError:$cloudKitError"
                        )
                        waitAgain = true
                    }
                }
                if (waitAgain) {
                    synchronized(lock) {
                        waitQueue.add(this)
                        waitMap[this.id] = this
                    }
                } else {
                    GLog.d(TAG, LogFlag.DL, "[addTask] onFinish: filePath :${PathMask.mask(file.filePath)} cloudKitError:$cloudKitError")
                    super.onFinish(file, cloudDateType, cloudKitError)
                }
                //重新开始下一个任务
                trigger()
            }
        }
        waitQueue.add(task)
        waitMap[task.id] = task
        GLog.d(TAG, LogFlag.DL, "[addNewTaskToWaitQueue] add to waitQueue size=${waitQueue.size}")
    }

    /**
     * 停止相同路径更小尺寸的下载任务，注意此方法需要在锁内调用
     */
    private fun stopSmallerSizeDownloadTask(file: CloudIOFile) {
        val stopTaskList = mutableListOf<TransferTask>()
        waitMap.filterValues {
            (it.cloudIOFile.filePath == file.filePath) &&
                    (DownloadFileSizeComparator().compare(it.cloudIOFile, file) < 0)
        }.forEach {
            GLog.d(TAG, LogFlag.DL, "[stopSmallerSizeDownloadTask] ${PathMask.mask(file.filePath)}")
            waitMap.remove(it.key)
            waitQueue.remove(it.value)
            stopTaskList.add(it.value)
        }
        stopTaskList.forEach {
            stopFromWaiting(it)
        }
    }

    /**
     * 获取需要调整为暂停的任务
     *
     * （1）暂停已有相同路径的更低优先级任务
     * （2）若新任务中有大文件，则：暂停所有同类型或者大文件的更低优先级任务 （避免并发IO抢占）
     * （3）若新任务中无大文件，则：
     *      正在执行的任务有同类型的相同优先级或更低优先级的大文件任务，则：暂停该任务（大文件任务不与其他任务并行）
     *      否则：剩余的可用并行数量不足时，暂停优先顺序靠后的相应数量的更低优先级任务
     *
     * @param transferPriority  插入的任务等级
     * @param count             插入的任务数量
     * @param files             插入的任务的文件，已有相同路径的更低优先级任务会被取消并重新排队
     * @return
     */
    private fun getNeedPauseTask(
        transferPriority: TransferPriority,
        count: Int,
        files: List<CloudIOFile>
    ): List<TransferTask> {
        val result = mutableSetOf<TransferTask>()
        runningMap.values.filter { runningTask ->
            for (newFile in files) {
                if (runningTask.cloudIOFile.filePath == newFile.filePath) {
                    if (runningTask.priority.value < transferPriority.value) {
                        return@filter true
                    }
                    break
                }
            }
            false
        }.takeIf { it.isNotEmpty() }?.also {
            it.forEach {
                GLog.d(TAG, LogFlag.DL, "[getNeedPauseTask] lower priority same path task ${PathMask.mask(it.cloudIOFile.filePath)}")
            }
            result.addAll(it)
        }
        val newFileKeys = files.map { it.getKey(it.getCloudDataType()) }
        val syncType = files.first().type
        if (files.find { it.isBigFile() } != null) {
            // 当新任务中有大文件时，暂停正在执行的所有同类型或者大文件的更低优先级任务
            getAllRunningSameTypeOrBigFileLowerPriorityTasks(syncType, transferPriority, newFileKeys).takeIf { it.isNotEmpty() }?.also {
                it.forEach {
                    GLog.d(TAG, LogFlag.DL, "[getNeedPauseTask] lower priority task ${PathMask.mask(it.cloudIOFile.filePath)}")
                }
                result.addAll(it)
            }
        } else {
            // 有正在执行的同类型的相同优先级或更低优先级的大文件任务，因为大文件任务不与其他任务并行，所以不论剩余任务空间如何都必须暂停才能让新任务执行
            getRunningBigFileLowerPriorityTask(syncType, transferPriority, newFileKeys)?.also {
                GLog.d(TAG, LogFlag.DL, "[getNeedPauseTask] lower priority big file task ${PathMask.mask(it.cloudIOFile.filePath)}")
                result.add(it)
            } ?: also {
                //剩余的任务空间，准备取消的任务不计入占用
                val vacancyCount = parallelFileCount - runningMap.filterValues { it.type == syncType }.size + result.size
                //如果插入的任务数量小于剩余空间
                if (vacancyCount >= count) {
                    return result.toList()
                }
                result.addAll(getNeedPauseLowerPriorityTask(syncType, transferPriority, count))
            }
        }
        return result.toList()
    }

    /**
     * 获取正在执行的所有同类型或者大文件的更低优先级任务
     */
    private fun getAllRunningSameTypeOrBigFileLowerPriorityTasks(
        syncType: Int,
        transferPriority: TransferPriority,
        newFileKeys: List<String>
    ): List<TransferTask> {
        return runningMap.mapNotNull { (_, task) ->
            val isSameTypeOrBigFile = (task.type == syncType) || task.cloudIOFile.isBigFile()
            val isLowerPriority = (task.priority.value < transferPriority.value) || (transferPriority == TransferPriority.MAX)
            if (isSameTypeOrBigFile
                && isLowerPriority
                && (task.cloudIOFile.getKey(task.cloudIOFile.getCloudDataType()) in newFileKeys).not()
            ) {
                task
            } else null
        }
    }

    /**
     * 获取正在执行的同类型的相同优先级或更低优先级的大文件任务
     */
    private fun getRunningBigFileLowerPriorityTask(
        syncType: Int,
        transferPriority: TransferPriority,
        newFileKeys: List<String>
    ): TransferTask? {
        return runningMap.values.find {
            (it.type == syncType) && it.cloudIOFile.isBigFile() &&
                    ((it.priority.value <= transferPriority.value) || (transferPriority == TransferPriority.MAX)) &&
                    (it.cloudIOFile.getKey(it.cloudIOFile.getCloudDataType()) in newFileKeys).not()
        }
    }

    private fun getNeedPauseLowerPriorityTask(
        syncType: Int,
        transferPriority: TransferPriority,
        count: Int
    ): List<TransferTask> {
        //遍历正在执行的任务,根据传入的优先级，获取队列中所有可能需要暂停的任务
        var transferTasks = runningMap.mapNotNull { (_, task) ->
            if ((task.type == syncType) &&
                ((task.priority.value < transferPriority.value) || (transferPriority == TransferPriority.MAX))) {
                task
            } else null
        }
        //如果没有比传入等级更低的任务，那么返回空
        if (transferTasks.isEmpty()) {
            return emptyList()
        }
        //复用任务的排序方法，排序完成以后任务低的放在了前面。
        transferTasks.toMutableList()
            .sortWith { transferTask: TransferTask, other: TransferTask -> -TransferTaskComparator.compare(transferTask, other) }
        if (transferTasks.size > count) {
            transferTasks = transferTasks.subList(0, count)
        }
        return transferTasks
    }

    /**
     * 快稳省等限制停止任务
     */
    fun stopByLimitIfNeed() {
        if (envConfig == EnvConfig.PROCESS_TEST) {
            GLog.d(TAG, LogFlag.DL, "[stopTransferIfNeed]")
        }
        synchronized(lock) {
            runningMap.forEach { (_, transferTask) ->
                val allowResult = syncAllow.onCheckAllowToSync(transferTask.source)
                if (!allowResult.isSuccess) {
                    if (envConfig == EnvConfig.PROCESS_TEST) {
                        GLog.d(TAG, LogFlag.DL, "[stopTransferIfNeed] stopTransferFile  ${PathMask.mask(transferTask.cloudIOFile.filePath)} ")
                    }
                    stopTransferFile(transferTask, transferTask.cloudIOFile.getCloudDataType(), CloudStopType.LIMIT, allowResult.resultCode)
                }
            }
        }
    }

    /**
     * 根据传入的文件停止
     * @param files 文件
     */
    fun stop(files: List<CloudIOFile>) {
        if (files.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "[stopFiles] file list is empty")
            return
        }
        synchronized(lock) {
            files.forEach { file ->
                val key = file.getKey(file.getCloudDataType())
                //如果是在运行队列中，那么就调用cloudkit 取消调此任务
                if (runningMap.containsKey(key)) {
                    val transferTask = runningMap[key]
                    GLog.d(TAG, LogFlag.DL, "[stopFiles] runningMap remove ${PathMask.mask(file.filePath)}")
                    transferTask?.let {
                        stopTransferFile(it, file.getCloudDataType(), CloudStopType.MANUAL)
                    }
                } else if (waitMap.containsKey(key)) {
                    val transferTask = waitMap[key]
                    transferTask?.let {
                        GLog.d(TAG, LogFlag.DL, "[stopFiles] waitMap remove  ${PathMask.mask(file.filePath)}")
                        stopFromWaiting(it)
                    }
                    waitQueue.remove(transferTask)
                    waitMap.remove(key)
                }
            }
        }
    }

    /**
     * 停止所有任务
     */
    fun stopAll() {
        GLog.d(TAG, LogFlag.DL, "[stopAll] stopAll")
        synchronized(lock) {
            waitMap.forEach {
                stopFromWaiting(it.value)
            }
            waitQueue.clear()
            waitMap.clear()
            GLog.d(TAG, LogFlag.DL, "[stopAll] wait queue cleared")
            runningMap.forEach { (_, transferTask) ->
                GLog.d(TAG, LogFlag.DL, "[stopAll] runningMap stopTransferFile path:${transferTask.cloudIOFile.filePath}")
                stopTransferFile(transferTask, transferTask.cloudIOFile.getCloudDataType(), CloudStopType.MANUAL)
            }
        }
    }

    /**
     * 根据任务id停止任务
     * @param taskId
     */
    fun stop(taskId: Int) {
        GLog.d(TAG, LogFlag.DL, "[stop] $taskId")
        synchronized(lock) {
            val cloudIOFile = callbackIdToFileMap[taskId]
            if (cloudIOFile == null) {
                //如果Id不存在则直接返回
                GLog.d(TAG, LogFlag.DL, "[stop] task already cancel or not in queue or cloudIOFile is null")
                return
            }
            val key = cloudIOFile.getKey(cloudIOFile.getCloudDataType())
            //如果是在运行队列中，那么就调用cloudkit 取消此任务
            if (runningMap.containsKey(key)) {
                val transferTask = runningMap[key]
                transferTask?.let {
                    val cancelable = it.cancelCallback(taskId, it.cloudIOFile.getCloudDataType(), getCloudkitErrorByType(cloudIOFile))
                    if (cancelable) {
                        GLog.d(TAG, LogFlag.DL, "[stop] runningMap cancelable stop task file path : ${PathMask.mask(it.cloudIOFile.filePath)}")
                        stopTransferFile(it, it.cloudIOFile.getCloudDataType(), CloudStopType.MANUAL)
                        // 取消的时候文件可能已经下载完成了，但是还没有搬到正式目录中，取消的时候需要把文件也删掉
                        it.cloudIOFile.cacheUri?.takeIf { it.isEmpty().not() }?.let {
                            File(URI(it)).delete()
                        }
                    }
                }
            }
            //如果是在等待队列中，则在队列中删除此任务
            else if (waitMap.containsKey(key)) {
                val transferTask = waitMap[key]
                transferTask?.let {
                    val cancelable = it.cancelCallback(taskId, it.cloudIOFile.getCloudDataType(), getCloudkitErrorByType(cloudIOFile))
                    if (cancelable) {
                        GLog.d(TAG, LogFlag.DL, "[stop] waitMap cancelable stop task file path : ${PathMask.mask(it.cloudIOFile.filePath)}")
                        stopFromWaiting(it)
                        waitQueue.remove(it)
                        waitMap.remove(key)
                    }
                }
            } else {
                //如果在队列中都不存在，则打印日志
                GLog.d(TAG, LogFlag.DL, "[stop] waitMap and runningMap not contain the taskId")
            }
        }
    }

    /**
     * 是否快速同步模式，界面会显示正在快速同步
     */
    fun isFastDownloadMode(): Boolean {
        return bgParallelDownloadCountStrategy.isFastDownloadMode()
    }

    /**
     * 停止等待执行队列中的任务
     * @param transferTask 任务
     * @param customLimitCode cloudkit透传的错误码
     */
    private fun stopFromWaiting(transferTask: TransferTask, customLimitCode: Int? = null) {
        val cloudKitError = getCloudkitErrorByType(transferTask.cloudIOFile, customLimitCode)
        transferTask.onFinishFromWait(transferTask.cloudIOFile, transferTask.cloudIOFile.getCloudDataType(), cloudKitError)
    }

    /**
     * @param cloudIOFile cloudkit 标准文件
     * @param customLimitCode cloudkit 透传的错误码
     */
    private fun getCloudkitErrorByType(cloudIOFile: CloudIOFile, customLimitCode: Int? = null): CustomCloudKitError {
        return if (cloudIOFile.type == CloudIOType.UPLOAD.type) {
            if (customLimitCode == null) {
                CustomCloudKitError(CloudKitError.STOP_UPLOAD_MANUAL)
            } else {
                CustomCloudKitError(CloudKitError.STOP_UPLOAD_LIMIT, customLimitCode)
            }
        } else {
            if (customLimitCode == null) {
                CustomCloudKitError(CloudKitError.STOP_DOWNLOAD_MANUAL)
            } else {
                CustomCloudKitError(CloudKitError.STOP_DOWNLOAD_LIMIT, customLimitCode)
            }
        }
    }

    /**
     * 触发开始任务
     */
    private fun trigger() {
        trigger(CloudIOType.DOWNLOAD.type)
        trigger(CloudIOType.UPLOAD.type)
    }

    private fun trigger(type: Int) {
        var startTask: TransferTask?
        var startNext = true
        do {
            GLog.d(TAG, LogFlag.DL, "[trigger] type: ${type.toSyncTypeString()} waitQueue size:${waitQueue.size}")
            synchronized(lock) {
                // 从等待队列找出第一个不存在相同路径任务正在执行的任务，然后判断是否满足开始任务的条件，满足则移出等待队列
                startTask = waitQueue.find { waitingTask ->
                    (waitingTask.type == type) && (runningMap.values.find { it.cloudIOFile.filePath == waitingTask.cloudIOFile.filePath } == null)
                }?.takeIf { canStart(it) }?.also {
                    waitQueue.remove(it)
                }
            }
            startTask?.also {
                start(it)
            } ?: also {
                startNext = false
            }
        } while (startNext)
    }

    /**
     * 是否满足开始任务的条件，用于控制并行数量
     *
     * 1.后台上传是单线程进行：没有正在上传的低优先级任务时，才可以开启新的上传任务（较低优先级上传任务互相不并行，减少后台同步功耗）
     * 2.后台下载是多线程进行：后台正在下载的任务数量 <= [lowPriorityParallelDownCount]
     * 3.正在执行的任务的数量小于并行数
     *
     * 注意要在 synchronized(lock) 里调用
     */
    private fun canStart(nextTask: TransferTask): Boolean {
        var canParallel = true
        for (runningTask in runningMap.values) {
            // 上传时，低优先级任务不并行，仍是单线程上传
            if ((nextTask.type == CloudIOType.UPLOAD.type) &&
                (runningTask.type == CloudIOType.UPLOAD.type) &&
                (nextTask.priority.value <= TransferPriority.LOW.value) &&
                (runningTask.priority.value <= TransferPriority.LOW.value)) {
                GLog.d(TAG, LogFlag.DL, "[canStart] running low priority task, not parallel for new low priority task")
                canParallel = false
                break
            }
        }
        val runningCount = runningMap.filterValues { it.type == nextTask.type }.size

        var runningDownloadLowCount = 0
        val canStartDownloadLowFile = if ((nextTask.type == CloudIOType.DOWNLOAD.type)
            && (nextTask.priority.value <= TransferPriority.LOW.value)
        ) {
            runningDownloadLowCount = runningMap.filterValues {
                (it.type == CloudIOType.DOWNLOAD.type) && (it.priority.value <= TransferPriority.LOW.value)
            }.size
            runningDownloadLowCount <= lowPriorityParallelDownCount
        } else {
            true
        }
        val canStart = canParallel && (runningCount < parallelFileCount) && canStartDownloadLowFile
        GLog.d(TAG, LogFlag.DL) {
            "[canStart] canParallel=$canParallel " +
                    "runningCount:${runningMap.size} parallelFileCount:$parallelFileCount " +
                    "canStart:$canStart " + " runningDownLowCount: $runningDownloadLowCount"
        }
        return canStart
    }

    /**
     * 开始执行任务
     * @param transferTask 执行的任务
     */
    private fun start(transferTask: TransferTask) {
        synchronized(lock) {
            //从等待队列中移除任务
            waitMap.remove(transferTask.id)
        }
        GLog.d(TAG, LogFlag.DL, "[start] path: ${PathMask.mask(transferTask.cloudIOFile.filePath)}  priority:${transferTask.priority.name}")
        val allowResult = syncAllow.onCheckAllowToSync(transferTask.source)
        if (allowResult.isSuccess) {
            GLog.d(
                TAG, LogFlag.DL, "[start] allowResult isSuccess syncType:${transferTask.type.toSyncTypeString()} " +
                        "path: ${PathMask.mask(transferTask.cloudIOFile.filePath)} priority:${transferTask.priority.name}"
            )
            synchronized(lock) {
                runningMap[transferTask.cloudIOFile.getKey(transferTask.cloudIOFile.getCloudDataType())] = transferTask
                startTransferFile(transferTask)
            }
        } else {
            //如果不允许任务执行，那么结束这个任务继续下一个任务
            val cloudKitError = getCloudkitErrorByType(transferTask.cloudIOFile, allowResult.resultCode)
            GLog.d(TAG, LogFlag.DL, "[start] allowResult fail. path: ${PathMask.mask(transferTask.cloudIOFile.filePath)}")
            transferTask.onFinishFromWait(transferTask.cloudIOFile, transferTask.cloudIOFile.getCloudDataType(), cloudKitError)
            trigger()
        }
    }

    private fun startTransferFile(transferTask: TransferTask) {
        GLog.d(
            TAG, LogFlag.DL, "[startTransferFile] type: ${transferTask.type.toSyncTypeString()} " +
                    "path: ${PathMask.mask(transferTask.cloudIOFile.filePath)} " +
                    "priority: ${transferTask.priority} " +
                    "cloudThumbInfo: ${transferTask.cloudIOFile.cloudThumbInfo} " +
                    "isServiceStarted: $isServiceStarted"
        )
        transferTask.onStart()
        if (!isServiceStarted) {
            isServiceStarted = OCloudSyncApi.startCloudSyncService()
        }
        // 最高优先级任务是用户前台发起的下载，可以跳过限流，强制下载
        if (transferTask.priority == TransferPriority.MAX) {
            OCloudSyncApi.apiClient.setForceSyncMark(true)
            OCloudSyncApi.apiClient.cancelIOSpeedLimiter()
        }
        AppScope.launch(Dispatchers.singleInvokeSDK) {
            LongBgTaskQuotaManager.add(ContextGetter.context, "$TAG.startTransferFile")
            OCloudSyncApi.apiClient.transferFile(transferTask.cloudIOFile, transferTask.cloudIOFile.getCloudDataType(), transferTask)
            LongBgTaskQuotaManager.remove(ContextGetter.context, "$TAG.startTransferFile")
        }
    }

    private fun stopTransferFile(
        transferTask: TransferTask,
        cloudDataType: CloudDataType,
        stopType: Int,
        limitErrorCode: Int = 0
    ) {
        GLog.d(
            TAG, LogFlag.DL, "[stopTransferFile] path: ${PathMask.mask(transferTask.cloudIOFile.filePath)} " +
                    "stopType: $stopType  " +
                    "priority: ${transferTask.priority} " +
                    "cloudThumbInfo: ${transferTask.cloudIOFile.cloudThumbInfo}"
        )
        transferTask.onCancel()
        AppScope.launch(Dispatchers.singleInvokeSDK) {
            LongBgTaskQuotaManager.add(ContextGetter.context, "$TAG.stopTransferFile")
            OCloudSyncApi.apiClient.stopTransferFile(transferTask.cloudIOFile, cloudDataType, stopType, limitErrorCode)
            LongBgTaskQuotaManager.remove(ContextGetter.context, "$TAG.stopTransferFile")
        }
    }

    private fun Int.toSyncTypeString(): String {
        return when (this) {
            CloudIOType.UPLOAD.type -> "upload"
            CloudIOType.DOWNLOAD.type -> "download"
            else -> "unknown"
        }
    }

    /**
     *  文件传输任务类，一个文件对应一个任务，一个任务包含多个监听回调
     *  @param id 任务的唯一标识
     *  @param cloudIOFile 传输的文件
     *  @param priority 优先级 优先级不同时，高优先级先执行；优先级相同时，MAX优先级提交时间晚先执行，其他优先级提交时间早先执行。
     *  @param createTime 创建的时间
     *  @param callback 回调
     *  @param source 同步类型，查看SourceFlag
     */
    @Suppress("LongParameterList")
    open inner class TransferTask(
        val id: String,
        val cloudIOFile: CloudIOFile,
        var priority: TransferPriority,
        var createTime: Long,
        callback: TransferCallback<*>,
        val source: Int,
        var taskState: TaskState = TaskState.INIT
    ) : CloudIOFileListener, Comparable<TransferTask> {

        /**
         * 回调集合，一个任务可能有多个回调
         */
        private val callbacks: MutableList<TransferCallback<*>> = CopyOnWriteArrayList()

        val type = cloudIOFile.type

        init {
            callbacks.add(callback)
        }

        fun addCallback(transferCallBack: TransferCallback<*>) {
            callbacks.add(transferCallBack)
        }

        fun onStart() {
            taskState = TaskState.RUNNING
        }

        override fun onProgress(file: CloudIOFile, cloudDateType: CloudDataType, progress: Double) {
            callbacks.forEachIndexed { index, callback ->
                callback.onProgress(file, cloudDateType, progress, invokeOuterCallback = index == 0)
            }
        }

        override fun onFinish(
            file: CloudIOFile,
            cloudDateType: CloudDataType,
            cloudKitError: CloudKitError
        ) {
            GLog.d(TAG, LogFlag.DL, "[onFinish] file=${PathMask.mask(file.filePath)} cloudKitError=$cloudKitError")
            val mockServerErrorCode = GProperty.DEBUG_CLOUD_MOCK_FILE_SERVER_ERROR_CODE
            val result = if ((mockServerErrorCode != 0) && file.filePath.contains("test")) {
                CloudKitError(TEST_INNER_ERROR_CODE, TEST_ERROR_MSG, CloudBizError.FAIL).apply {
                    subServerErrorCode = mockServerErrorCode
                }
            } else cloudKitError
            callbacks.forEachIndexed { index, callback ->
                callback.onFinish(
                    file,
                    cloudDateType,
                    CustomCloudKitError(result, result.bizSubErrorCode),
                    invokeOuterCallback = index == 0
                )
            }
            taskState = TaskState.FINISH
        }

        fun onCancel() {
            taskState = TaskState.CANCELED
        }

        /**
         * 根据传入的id,取消callback 回调
         * @param callbackId
         * @return  是否取消掉任务
         */
        fun cancelCallback(callbackId: Int, dataType: CloudDataType, cloudKitError: CustomCloudKitError): Boolean {
            var cancel = false
            //如果回调为空，可能是在刚好执行到这一步的时候就取消掉了，不用做任何处理
            if (callbacks.isEmpty()) {
                GLog.d(TAG, LogFlag.DL, "[cancelCallback] callbacks is empty")
                return cancel
            }
            //如果监听的回调大于1，证明有多个场景需要，不能取消这个任务，根据传入的callbackId移除掉对应的callback
            if (callbacks.size > 1) {
                val callback = callbacks.find {
                    (it.id == callbackId)
                }
                GLog.d(TAG, LogFlag.DL, "[cancelCallback] callbackId:$callbackId path: ${PathMask.mask(cloudIOFile.filePath)} cloudKitError:$cloudKitError")
                callback?.onFinish(cloudIOFile, dataType, cloudKitError, true)
                callbacks.removeIf {
                    it.id == callbackId
                }
            } else {
                cancel = true
            }
            return cancel
        }

        /**
         * 直接从Task等待队列中回调callbacks,和cloudkit 回调onFinish区分开
         * @param file 文件
         * @param cloudDateType cloudkit 规定的类型
         * @param cloudKitError cloudkit 错误
         */
        fun onFinishFromWait(
            file: CloudIOFile,
            cloudDateType: CloudDataType,
            cloudKitError: CustomCloudKitError
        ) {
            GLog.d(TAG, LogFlag.DL, "[onFinishFromWait] syncType:${file.type.toSyncTypeString()} path: ${PathMask.mask(cloudIOFile.filePath)}")
            callbacks.forEachIndexed { index, callback ->
                callback.onFinish(file, cloudDateType, cloudKitError, invokeOuterCallback = index == 0)
            }
        }

        override fun compareTo(other: TransferTask): Int {
            return TransferTaskComparator.compare(this, other)
        }

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false
            other as TransferTask
            if (id != other.id) return false
            return true
        }

        override fun hashCode(): Int {
            return id.hashCode()
        }
    }
    /**
     * 文件传输回调类，处理批量回调逻辑
     * @param callback 回调
     * @param batchResultCheckMap 批量传输文件时，根据此map验证是否所有文件都传输完毕
     * @param file 文件
     * @param successList 用于统计传输成功的文件
     * @param failedList 用于统计传输失败的文件
     */
    inner class TransferCallback<T : CloudIOFile>(
        private val callback: ICallBack<T>,
        private val batchResultCheckMap: MutableMap<String, CloudKitError?>,
        private val file: T,
        private val successList: MutableList<T>,
        private val failedList: MutableList<CloudIOErrorFile<T>>
    )  {

        val id = hashCode()
        var syncLock: SyncLock? = null

        fun onProgress(
            cloudIOFile: CloudIOFile,
            cloudDateType: CloudDataType,
            progress: Double,
            invokeOuterCallback: Boolean
        ) {
            if (invokeOuterCallback) {
                callback.onProgress(file, cloudDateType, progress)
            }
        }

        fun onFinish(
            cloudIOFile: CloudIOFile,
            cloudDateType: CloudDataType,
            customCloudKitError: CustomCloudKitError,
            invokeOuterCallback: Boolean
        ) {
            GLog.d(TAG, LogFlag.DL, "[onFinish] path:${PathMask.mask(cloudIOFile.filePath)} cacheUri:${cloudIOFile.cacheUri}")
            file.cacheUri = cloudIOFile.cacheUri
            file.cloudId = cloudIOFile.cloudId
            file.checkPayload = cloudIOFile.checkPayload
            if (invokeOuterCallback) {
                callback.onFinish(file, cloudDateType, customCloudKitError)
            }
            callBatchList(file, cloudDateType, customCloudKitError, invokeOuterCallback)
            callbackIdToFileMap.remove(id)
        }

        /**
         * 处理文件批量回调逻辑
         * @param cloudIOFile 文件
         * @param cloudDataType cloudkit 的数据类型
         * @param customCloudKitError cloudkit 回传的错误码
         */
        private fun callBatchList(
            cloudIOFile: T,
            cloudDataType: CloudDataType,
            customCloudKitError: CustomCloudKitError,
            invokeOuterCallback: Boolean
        ) {
            if (customCloudKitError.cloudKitError.isSuccess) {
                successList.add(cloudIOFile)
            } else {
                failedList.add(CloudIOErrorFile(cloudIOFile, customCloudKitError))
            }
            batchResultCheckMap[cloudIOFile.getKey(cloudDataType)] = customCloudKitError.cloudKitError
            batchResultCheckMap.map { (_, value) ->
                //当checkMap中的value还有空值时，证明一整批数据中，还有文件没有传输完毕，继续等待，直到value不为空为止
                if (value == null) {
                    GLog.d(TAG, LogFlag.DL, "[callBatchList] callBatchCallBack wait batch callback")
                    return
                }
            }
            if (CloudDebugUtil.isDebug(envConfig, CloudDebugUtil.TRANSFER_INTERRUPT_BATCH)) {
                GLog.d(TAG, LogFlag.DL, "[callBatchList] execute debug on DOWNLOAD_INTERRUPT_BATCH")
                throw InterruptedException("DOWNLOAD_INTERRUPT_BEFORE")
            } else {
                GLog.d(TAG, LogFlag.DL, "[callBatchList] successList size:${successList.size} failedList size:${failedList.size}")
                if (invokeOuterCallback) {
                    callback.batchResult(successList, failedList, cloudDataType)
                }
                val syncResult: SyncResult = if (failedList.isEmpty()) {
                    SyncResult(true, SyncResultConstants.RESULT_SUCCESS)
                } else {
                    SyncResult(failedList)
                }
                GLog.d(TAG, LogFlag.DL, "[callBatchList] syncLock.notifyFinish:${syncResult.resultCode}")
                syncLock?.notifyFinish(syncResult)
            }
        }
    }

    /**
     * 任务状态，可能有以下几种变化过程：
     * 1.正常请求： INIT -> RUNNING -> FINISH
     * 2.执行前取消： INIT -> FINISH
     * 3.执行中取消： INIT -> RUNNING -> CANCELED -> FINISH
     * 4.外部请求后执行中取消中再次外部请求：INIT -> RUNNING -> CANCELED -> RUNNING -> FINISH
     */
    enum class TaskState {
        INIT,
        RUNNING,
        CANCELED,
        FINISH
    }

    private data class DownloadedRecord(
        val file: CloudIOFile,
        /**
         * 下载完成时间，以SystemClock.elapsedRealtime()计，单位是ms
         */
        var downloadFinishTime: Long
    )

    companion object {
        const val TAG = "FileTransferTaskManager"
        private const val DISPATCHER_NAME_SINGLE_INVOKE_SDK = "singleInvokeSDK"
        private const val DISPATCHER_NAME_SINGLE_TRANSFER_CALLBACK = "singleTransferCallback"
        private const val LIMIT_PAUSE_ERROR_CODE = 100

        /**
         * 限制在此间隔时间内相同路径的下载请求必须是尺寸递进的，单位是ms
         */
        private const val INTERVAL_DOWNLOAD = 5000L

        private const val TEST_INNER_ERROR_CODE = -1015
        private const val TEST_ERROR_MSG = "test error"
    }
}
