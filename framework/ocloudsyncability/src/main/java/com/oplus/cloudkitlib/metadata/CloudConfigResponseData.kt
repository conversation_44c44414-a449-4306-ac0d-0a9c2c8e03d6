/*************************************************************************************************
 * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CloudConfigResponseData.kt
 * Description: 云端配置下发的数据结构
 *
 * Version: 1.0
 * Date: 2023/11/16
 * Author: <PERSON><PERSON>Wu@Apps.Gallery3D
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <PERSON>.Wu@Apps.Gallery3D    2023/11/16    1.0              build this module
 ************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import android.net.Uri
import com.google.gson.annotations.SerializedName

/**
 * 云端配置下发的数据结构
 */
data class CloudConfigResponseData(
    @SerializedName("resourceInfos") val configDataList: List<ConfigData>
) {

    /**
     * 云端配置下发的全部数据
     */
    data class ConfigData(
        /**
         * 每个提醒配置的唯一ID
         */
        @SerializedName("remindConfigId") val remindConfigId: Int,
        /**
         * 配置文案
         */
        @SerializedName("content") val content: String,
        /**
         * 配置类型
         */
        @SerializedName("remindCategory") val remindCategory: RemindCategory,
        /**
         * 配置生效日期，ms
         */
        @SerializedName("startTime") val startTime: Long,
        /**
         * 配置过期日期，ms
         */
        @SerializedName("endTime") val endTime: Long,
        /**
         * 按钮列表，配置1到2个
         */
        @SerializedName("buttons") var buttons: List<ButtonInfo>,
        /**
         * 资源投放追踪标识
         */
        @SerializedName("trackId") val trackId: String
    ) {

        /**
         * 按钮的全部数据
         */
        data class ButtonInfo(
            /**
             * 位置，0-左边，1-右边
             */
            @SerializedName("index") val index: Int,
            /**
             * 按鈕文案
             */
            @SerializedName("content") val content: String,
            /**
             * 按钮点击操作
             */
            @SerializedName("linkInfo") val linkInfo: LinkInfo
        ) {
            fun getButtonAction(): ButtonAction? {
                val uri = Uri.parse(linkInfo.jumpLink)
                return uri.takeIf {
                    it.toString().startsWith(SCHEMA_OF_GALLERY)
                }?.let {
                    when (it.getQueryParameter(TOKEN)) {
                        TOKEN_IGNORE -> ButtonAction.IGNORE
                        TOKEN_PAGE_SETTING -> ButtonAction.PAGE_SETTING
                        TOKEN_PAGE_CLOUD_SETTING -> ButtonAction.PAGE_CLOUD_SETTING
                        TOKEN_PAGE_ALBUM -> ButtonAction.PAGE_ALBUM
                        TOKEN_SW_AUTO_SYNC -> ButtonAction.OPEN_CLOUD_SYNC
                        TOKEN_SW_AUTO_SYNC_AND_OPTIMIZE_STORAGE -> ButtonAction.OPEN_AUTO_SYNC_AND_OPTIMIZE_STORAGE
                        TOKEN_SW_OPTIMIZE_STORAGE -> ButtonAction.OPEN_OPTIMIZE_STORAGE
                        TOKEN_SW_MOBILE_DATA -> ButtonAction.OPEN_MOBILE_SWITCH
                        TOKEN_SW_ALL_ALBUM -> ButtonAction.OPEN_ALL_ALBUM_SWITCH
                        TOKEN_PAGE_HALF_SCREEN_PAYMENT -> ButtonAction.UPGRADE
                        else -> null
                    }
                }
            }
        }

        /**
         * 按钮点击行为
         */
        data class LinkInfo(
            /**
             * 链接类型
             */
            @SerializedName("linkType") val linkType: LinkType,
            /**
             * 链接内容
             */
            @SerializedName("jumpLink") val jumpLink: String
        )

        /**
         * 配置类型
         */
        enum class RemindCategory {
            /**
             * 开关类型
             */
            SWITCH,

            /**
             * 付费类型
             */
            PAY
        }

        /**
         * 链接类型
         */
        enum class LinkType {
            /**
             * 账号web view，对应后台的web，h5页面
             */
            WEB_VIEW,

            /**
             * 浏览器，跳转到外部浏览器
             */
            BROWSER,

            /**
             * native页面，客户端原生页面，deeplink链接
             */
            NATIVE,

            /**
             * 快应用
             */
            FAST_APP,

            /**
             * 下载地址
             */
            DOWNLOAD
        }
    }

    enum class ButtonAction {
        IGNORE,
        PAGE_SETTING,
        PAGE_CLOUD_SETTING,
        PAGE_ALBUM,
        OPEN_CLOUD_SYNC,
        OPEN_AUTO_SYNC_AND_OPTIMIZE_STORAGE,
        OPEN_OPTIMIZE_STORAGE,
        OPEN_MOBILE_SWITCH,
        OPEN_ALL_ALBUM_SWITCH,
        UPGRADE
    }

    companion object {
        const val KEY_SW_AUTO_SYNC = "albumSWAutoSync"
        const val KEY_SW_SYNC_ALL_ALBUM = "albumSWSyncAllAlbum"
        const val KEY_SW_MOBILE_DATA_ALLOW = "albumSWMobileDataAllow"
        const val KEY_SW_OPTIMIZE_STORAGE = "albumSWOptimizeStorage"
        const val KEY_IMAGE_STORAGE = "localImagesStorageSpace"
        const val KEY_VIDEO_STORAGE = "localVideosStorageSpace"
        const val KEY_REMAIN_STORAGE = "localDeviceRemainStorageSpace"

        /**
         * 操作：开启同步所有图集 前提：云同步开启&同步所有图集关闭
         */
        const val TOKEN_SW_ALL_ALBUM = "sw_all_album"

        /**
         * 操作：开启允许移动数据下同步 前提：云同步开启&允许移动数据下同步关闭lou
         */
        const val TOKEN_SW_MOBILE_DATA = "sw_mobile_data"

        /**
         * 操作：开启优化存储空间 前提：云同步开启&优化存储空间关闭
         */
        const val TOKEN_SW_OPTIMIZE_STORAGE = "sw_optimize_storage"

        /**
         * 操作：开启云同步和优化存储空间 前提：云同步关闭
         */
        const val TOKEN_SW_AUTO_SYNC_AND_OPTIMIZE_STORAGE = "sw_auto_sync_and_optimize_storage"

        /**
         * 操作：开启云同步 前提：云同步关闭
         */
        const val TOKEN_SW_AUTO_SYNC = "sw_auto_sync"

        /**
         * 操作：跳转图集选择页 前提：云同步开启
         */
        const val TOKEN_PAGE_ALBUM = "page_album"

        /**
         * 操作：跳转云同步设置页 前提：无
         */
        const val TOKEN_PAGE_CLOUD_SETTING = "page_cloud_setting"

        /**
         * 操作：跳转存储空间优化页 前提：无
         */
        const val TOKEN_PAGE_SETTING = "page_setting"

        /**
         * 操作：忽略 前提：无
         */
        const val TOKEN_IGNORE = "ignore"
        /**
         * 操作：打开支付半屏 前提：无
         */
        const val TOKEN_PAGE_HALF_SCREEN_PAYMENT = "page_half_screen_payment"

        /**
         * 表示跳转到相册的schema
         * 相册的schema样式如：oplus://gallery/router?token=page_cloud_setting
         */
        private const val SCHEMA_OF_GALLERY = "oplus://gallery/router"
        private const val TOKEN = "token"
    }
}