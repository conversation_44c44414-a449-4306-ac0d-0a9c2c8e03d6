/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SyncResult.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.base

import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.oplus.cloudkitlib.metadata.BackupDataResult
import com.oplus.cloudkitlib.metadata.RecoveryDataResult
import com.oplus.gallery.business_lib.cloudsync.SyncResultConstants
import com.oplus.gallery.framework.abilities.ocloudsync.sync.CloudKitServerErrorCode
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor

class SyncResult {

    var trackInfo: TrackInfo = TrackInfo()
    var isSuccess: Boolean
    var resultCode: Int

    /**
     * 有别于正常流程里的 resultCode，此变量记录真实的 result 用于埋点上报。
     * 元数据备份异常时，为了让同步流程不被阻塞，resultCode使用的是success，埋点需要上报真实的结果。
     */
    var trackMetadataBackupErrorCode: Int? = null

    var backupDataResult: BackupDataResult<*>? = null
    var recoveryDataResult: RecoveryDataResult<*>? = null
    var error: CloudKitError? = null
    var customErrorCode: Int = 0

    constructor(success: Boolean, value: Int) {
        isSuccess = success
        resultCode = value
    }

    constructor(error: CloudKitError) {
        this.error = error
        resultCode = getResultValue(error)
        isSuccess = (resultCode == SyncResultConstants.RESULT_SUCCESS)
    }

    constructor(error: CloudKitError, customErrorCode: Int) {
        this.error = error
        this.customErrorCode = customErrorCode
        resultCode = getResultValue(error)
        isSuccess = (resultCode == SyncResultConstants.RESULT_SUCCESS)
    }

    constructor(errorList: List<CloudIOErrorFile<*>>) {
        var error: CustomCloudKitError? = null
        for (errorFile in errorList) {
            // 在收到cannot found local file异常时直接过滤掉（在下次上传的时候会重新上传），不直接展示给用户。
            if (errorFile.customCloudKitError.cloudKitError.bizErrorCode == CloudBizError.NO_FIND_LOCAL_FILE.code) continue

            if (errorFile.customCloudKitError.cloudKitError.innerErrorCode == CloudKitError.STOP_DOWNLOAD_LIMIT.innerErrorCode
                || errorFile.customCloudKitError.cloudKitError.innerErrorCode == CloudKitError.STOP_UPLOAD_LIMIT.innerErrorCode
            ) {
                error = errorFile.customCloudKitError
                break
            }

            if (error == null) {
                error = errorFile.customCloudKitError
            }
        }
        if (error == null) {
            resultCode = SyncResultConstants.RESULT_SUCCESS
            isSuccess = true
        } else {
            this.error = error.cloudKitError
            this.customErrorCode = error.bizErrorCode
            resultCode = getResultValue(error.cloudKitError)
            isSuccess = (resultCode == SyncResultConstants.RESULT_SUCCESS)
        }
    }

    constructor(backupData: BackupDataResult<*>) {
        isSuccess = true
        resultCode = SyncResultConstants.RESULT_SUCCESS
        backupDataResult = backupData
    }

    constructor(recoveryData: RecoveryDataResult<*>) {
        isSuccess = true
        resultCode = SyncResultConstants.RESULT_SUCCESS
        recoveryDataResult = recoveryData
    }

    private fun getResultValue(error: CloudKitError): Int {
        val result = when (CloudBizError.getByCode(error.bizErrorCode)) {
            // 主动调用取消，也算作成功
            CloudBizError.MANUAL_STOP,
            // 在收到cannot found local file异常时直接过滤掉（在下次上传的时候会重新上传），不直接展示给用户。
            CloudBizError.NO_FIND_LOCAL_FILE -> SyncResultConstants.RESULT_SUCCESS
            CloudBizError.FAIL -> {
                when (error.subServerErrorCode) {
                    CloudKitServerErrorCode.CLIENT_NOT_LOGIN_IN
                        -> SyncResultConstants.RESULT_NOT_LOGIN_OR_SWITCH_CLOSE
                    CloudKitServerErrorCode.ACCOUNT_AUTH_FAIL_INVALID_USER_TOKEN,
                    CloudKitServerErrorCode.ACCOUNT_AUTH_FAIL_INVALID_ENV,
                    CloudKitServerErrorCode.INVALID_ACCOUNT_TOKEN
                        -> SyncResultConstants.RESULT_AUTH_ERROR
                    CloudKitServerErrorCode.NO_CLOUD_SPACE
                        -> SyncResultConstants.RESULT_INSUFFICIENT_SPACE
                    CloudKitServerErrorCode.SERVER_LIMIT_QPS,
                    CloudKitServerErrorCode.REQUEST_TOO_FREQUENT,
                    CloudKitServerErrorCode.SERVER_LIMIT_DURATION,
                    CloudKitServerErrorCode.SERVER_LIMIT_IN_DELAY
                        -> SyncResultConstants.RESULT_REQUEST_TOO_FREQUENT
                    CloudKitServerErrorCode.DATA_COLD_STANDBY
                        -> SyncResultConstants.RESULT_DATA_COLD_STANDBY
                    CloudKitServerErrorCode.FORBID_CLOUD
                        -> SyncResultConstants.RESULT_SERVER_CLEARED_DATA
                    else -> SyncResultConstants.RESULT_SERVICE_ERROR
                }
            }
            CloudBizError.NETWORK -> {
                if (NetworkMonitor.isNetworkValidated()) {
                    SyncResultConstants.RESULT_NETWORK_ERROR
                } else {
                    SyncResultConstants.RESULT_NETWORK_NO_CONNECT
                }
            }
            CloudBizError.NO_LOCAL_SPACE -> SyncResultConstants.RESULT_LOCAL_INSUFFICIENT_SPACE
            CloudBizError.SUCCESS -> SyncResultConstants.RESULT_SUCCESS
            CloudBizError.LIMIT_STOP -> {
                if ((customErrorCode >= SyncResultConstants.RESULT_LIMIT)
                    && (customErrorCode <= SyncResultConstants.RESULT_THIRD_APP_FULL_FRONT)
                ) {
                    customErrorCode
                } else {
                    SyncResultConstants.RESULT_LIMIT
                }
            }
            CloudBizError.SERVER_SHUTDOWN -> SyncResultConstants.RESULT_SERVICE_OFFLINE
            else -> SyncResultConstants.RESULT_FAIL
        }
        return result
    }

    /**
     * 获取trackErrorCode
     * RESULT_FAIL的情况取cloudkit的错误码
     */
    fun getTrackErrorCode(): Int {
        return if (resultCode == SyncResultConstants.RESULT_FAIL) {
            error?.let {
                if (it.subServerErrorCode != 0) {
                    it.subServerErrorCode
                } else {
                    it.innerErrorCode
                }
            } ?: SyncResultConstants.RESULT_FAIL
        } else {
            resultCode
        }
    }

    override fun toString(): String {
        return "SyncResult[isSuccess=$isSuccess,resultCode=$resultCode,backupDataResult=$backupDataResult,error=$error]"
    }

    companion object {
        /**
         * 合并同步结果
         *
         * 如果全部结果都是成功，则返回第一个结果（也就是成功）；否则返回第一个不是成功的结果
         */
        fun mergeSyncResult(vararg results: SyncResult): SyncResult {
            return results.find { it.isSuccess.not() } ?: results[0]
        }
    }
}