/*************************************************************************************************
 * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - RecycleConfigResponseData.kt
 * Description: 云端配置下发的数据结构，用于最近删除页面的底部引导文案提示。
 *
 * Version: 1.0
 * Date: 2024/01/05
 * Author: <PERSON><PERSON>@Apps.Gallery3D
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Frank.Wu@Apps.Gallery3D    2024/01/05    1.0              build this module
 ************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import com.google.gson.annotations.SerializedName

/**
 * 云端配置下发的数据结构，用于最近删除页面的底部引导文案提示。
 */
data class RecycleConfigResponseData(
    /**
     * 配置文案
     */
    @SerializedName("content") val content: String,
    /**
     * 配置生效日期，ms
     */
    @SerializedName("startTime") val startTime: Long,
    /**
     * 配置过期日期，ms
     */
    @SerializedName("endTime") val endTime: Long,
)
