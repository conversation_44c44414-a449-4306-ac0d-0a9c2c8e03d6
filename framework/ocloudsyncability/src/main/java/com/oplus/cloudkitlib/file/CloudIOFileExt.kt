/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CloudIOFileExt.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/6/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2023/6/28        1.0           Add
 **************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libcommon.bean.io.CloudIOType
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.CloudTypeIOFile
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.app.AppConstants

/**
 * 大小达到该值的文件认为是大文件
 */
private const val BIG_FILE_THRESHOLD = 1 * AppConstants.Capacity.UNIT_MB2B
private const val TAG = "CloudIOFileExt"

fun CloudIOFile.isBigFile(): Boolean {
    return when (type) {
        CloudIOType.UPLOAD.type -> fileSize >= BIG_FILE_THRESHOLD
        CloudIOType.DOWNLOAD.type -> if (cloudThumbInfo.isNullOrEmpty()) fileSize >= BIG_FILE_THRESHOLD else false
        else -> false
    }
}

fun <T : CloudIOFile> T.getCloudDataType(): CloudDataType {
    return if (this is CloudTypeIOFile) {
        this.cloudDataType
    } else {
        GLog.w(TAG, LogFlag.DF) { "getCloudDataType: file should inherit from CloudTypeIOFile." }
        CloudDataType.PRIVATE
    }
}