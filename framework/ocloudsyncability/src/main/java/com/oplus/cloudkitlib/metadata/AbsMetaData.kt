/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsMetaData.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON><PERSON><PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord

abstract class AbsMetaData<T> : CloudMetaDataRecord {
    //自定义字段
    var customFields: T? = null

    constructor(cloudMetaDataRecord: CloudMetaDataRecord) {
        set(cloudMetaDataRecord)
    }

    constructor(cloudMetaDataRecord: CloudMetaDataRecord, fields: T) {
        set(cloudMetaDataRecord)
        customFields = fields
    }

    private fun set(cloudMetaDataRecord: CloudMetaDataRecord) {
        sysRecordId = cloudMetaDataRecord.sysRecordId
        sysVersion = cloudMetaDataRecord.sysVersion
        sysUniqueId = cloudMetaDataRecord.sysUniqueId
        sysProtocolVersion = cloudMetaDataRecord.sysProtocolVersion
        sysRecordType = cloudMetaDataRecord.sysRecordType
        sysDataType = cloudMetaDataRecord.sysDataType
        operatorType = cloudMetaDataRecord.operatorType
        sysStatus = cloudMetaDataRecord.sysStatus
        sysCreateTime = cloudMetaDataRecord.sysCreateTime
        sysUpdateTime = cloudMetaDataRecord.sysUpdateTime
        fields = cloudMetaDataRecord.fields
        fileInfos = cloudMetaDataRecord.fileInfos
    }
}