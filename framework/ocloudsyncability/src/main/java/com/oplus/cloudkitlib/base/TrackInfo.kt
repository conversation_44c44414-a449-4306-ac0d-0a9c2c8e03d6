/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TrackInfo.kt
 ** Description: 同步过程中的埋点信息
 ** Version: 1.0
 ** Date : 2023/12/11
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG: TrackInfo
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  zhongxuechang@Apps.Gallery3D      2023/12/11    1.0         first created
 ********************************************************************************/
package com.oplus.cloudkitlib.base

import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.framework.abilities.ocloudsync.cloud.DownloadFileInfo
import com.oplus.gallery.framework.abilities.ocloudsync.cloud.UploadFileInfo
import com.oplus.gallery.framework.abilities.ocloudsync.sync.MediaIOFile

class TrackInfo {
    /** 成功处理的图片数量 */
    var successImageCount: Int = 0
    /** 成功处理的视频数量 */
    var successVideoCount: Int = 0
    /** 成功处理的图片文件大小 */
    var successImageSize: Long = 0
    /** 成功处理的视频文件大小 */
    var successVideoSize: Long = 0

    /**
     * 追加留一个TrackInfo的数据
     */
    fun append(info: TrackInfo): TrackInfo {
        successImageCount += info.successImageCount
        successVideoCount += info.successVideoCount
        successImageSize += info.successImageSize
        successVideoSize += info.successVideoSize
        return this
    }

    /**
     * 跟踪记录被成功下载的信息
     */
    fun trackDownloadSuccessResult(list: List<MediaIOFile<DownloadFileInfo>>) {
        list.forEach {
            it.fileParam.let { params ->
                if (params.mediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE) {
                    successImageCount++
                    successImageSize += params.downloadedFileInfo.size
                } else {
                    successVideoCount++
                    successVideoSize += params.downloadedFileInfo.size
                }
            }
        }
    }

    /**
     * 跟踪记录被成功上传的信息
     */
    fun trackUploadSuccessResult(list: List<MediaIOFile<UploadFileInfo>>) {
        list.forEach {
            it.fileParam.let { params ->
                if (params.imageFile.isImage()) {
                    successImageCount++
                    successImageSize += params.imageFile.mLocalSize
                } else {
                    successVideoCount++
                    successVideoSize += params.imageFile.mLocalSize
                }
            }
        }
    }
}