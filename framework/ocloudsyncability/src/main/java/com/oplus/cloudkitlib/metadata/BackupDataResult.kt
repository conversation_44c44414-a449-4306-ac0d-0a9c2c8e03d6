/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BackupDataResult.kt
 ** Description: 元数据备份完成后返回的结果
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>alin @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.oplus.cloudkitlib.base.CloudKitConstants
import com.oplus.gallery.foundation.util.text.JsonUtil

/**
 * 元数据备份完成后返回的结果
 */
class BackupDataResult<T : AbsMetaData<*>>(
    backupList: List<T>,
    successList: List<CloudBackupResponseRecord>,
    errorList: List<CloudBackupResponseError>
) {
    var isAllSuccess: Boolean = true

    /**
     * 成功的数据
     */
    var successList = mutableListOf<T>()

    /**
     * 失败的数据，错误码->元数据
     */
    val errors = mutableMapOf<Int, MutableList<T>>()

    init {
        isAllSuccess = errorList.isEmpty()
        initSuccessList(backupList, successList)
        initErrorList(backupList, errorList)
    }

    private fun getBackupSysStatus(oldSysStatus: Int?, operatorType: String): Int? {
        return when (operatorType) {
            CloudKitConstants.OPERATOR_TYPE_CREATE -> CloudKitConstants.SYS_STATUS_NORMAL
            CloudKitConstants.OPERATOR_TYPE_CREATE_AND_RECYCLE -> CloudKitConstants.SYS_STATUS_RECYCLE
            CloudKitConstants.OPERATOR_TYPE_DELETE -> CloudKitConstants.SYS_STATUS_DELETED
            CloudKitConstants.OPERATOR_TYPE_REPLACE -> oldSysStatus
            CloudKitConstants.OPERATOR_TYPE_RECYCLE -> CloudKitConstants.SYS_STATUS_RECYCLE
            CloudKitConstants.OPERATOR_TYPE_RESUME -> CloudKitConstants.SYS_STATUS_NORMAL
            else -> oldSysStatus
        }
    }

    private fun initSuccessList(
        backupList: List<T>,
        successList: List<CloudBackupResponseRecord>,
    ) {
        for (success in successList) {
            for (backupData in backupList) {
                if (backupData.sysRecordId == success.sysRecordId) {
                    backupData.sysVersion = success.sysVersion
                    if (success.sysCreateTime != 0L) {
                        backupData.sysCreateTime = success.sysCreateTime
                    }
                    if (success.sysUpdateTime != 0L) {
                        backupData.sysUpdateTime = success.sysUpdateTime
                    }
                    getBackupSysStatus(backupData.sysStatus, backupData.operatorType)?.apply {
                        backupData.sysStatus = this
                    }
                    this.successList.add(backupData)
                    break
                }
            }
        }
    }

    @Suppress("NestedBlockDepth")
    private fun initErrorList(
        backupList: List<T>,
        errorList: List<CloudBackupResponseError>
    ) {
        for (error in errorList) {
            for (backupData in backupList) {
                if (backupData.sysRecordId == error.sysRecordId) {
                    // 1200和1204会返回sysRecordInfo
                    error.sysRecordInfo?.run {
                        JsonUtil.fromJson(this, CloudMetaDataRecord::class.java).run {
                            backupData.sysVersion = sysVersion
                            if (sysCreateTime != 0L) {
                                backupData.sysCreateTime = sysCreateTime
                            }
                            if (sysUpdateTime != 0L) {
                                backupData.sysUpdateTime = sysUpdateTime
                            }
                            // 本地的sysRecordId可能和云端不一致，以云端为准
                            if (sysRecordId != null) {
                                backupData.sysRecordId = sysRecordId
                            }
                            getBackupSysStatus(backupData.sysStatus, backupData.operatorType)?.run {
                                backupData.sysStatus = this
                            }
                        }
                    }

                    errors.computeIfAbsent(error.cloudKitError.subServerErrorCode) {
                        mutableListOf()
                    }.add(backupData)

                    break
                }
            }
        }
    }

    override fun toString(): String {
        return "BackupDataResult(isAllSuccess=$isAllSuccess, successList=${successList.size}, " +
                "errors=${errors.keys})"
    }

    /**
     * 需要处理的错误
     */
    enum class BackupError(val code: Int) {
        /** 文件上传过期，即文件上传很久后才上传对应的元数据，此时云端文件会被删除，需重新上传文件（该过期时间由后台控制） */
        FILE_UPLOAD_EXPIRED(CODE_FILE_UPLOAD_EXPIRED),
        /** 文件上传数据不匹配，上传元数据时云端解密fileCheckPayload字段拿到的ocloudId和上传的ocloudId不匹配 */
        FILE_UPLOAD_NOT_MATCH(CODE_FILE_UPLOAD_NOT_MATCH),
        /** 更新时sysRecordId在云端不存在(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace) */
        SYS_RECORD_ID_NOT_FOUND(CODE_SYS_RECORD_ID_NOT_FOUND),
        /**上传的sysVersion小于云端的数据, 需要先fetch(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace) */
        NEED_FETCH(CODE_NEED_FETCH),
        /** 创建时UNIQUE_ID在云端已经存在，需要先拉取数据合并重冲突 */
        UNIQUE_ID_EXIST(CODE_UNIQUE_ID_EXIST);

        override fun toString(): String {
            return "$name(code=$code)"
        }
    }

    companion object {

        /////////////////// 以下为需要处理的错误类型定义 //////////////////////

        /** 文件上传过期，即文件上传很久后才上传对应的元数据，此时云端文件会被删除，需重新上传文件（该过期时间由后台控制） */
        private const val CODE_FILE_UPLOAD_EXPIRED = 1108

        /** 文件上传数据不匹配，上传元数据时云端解密fileCheckPayload字段拿到的ocloudId和上传的ocloudId不匹配 */
        private const val CODE_FILE_UPLOAD_NOT_MATCH = 1109

        /** 更新时sysRecordId在云端不存在(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace) */
        private const val CODE_SYS_RECORD_ID_NOT_FOUND = 1201

        /**上传的sysVersion小于云端的数据, 需要先fetch(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace) */
        private const val CODE_NEED_FETCH = 1202

        /** 创建时UNIQUE_ID在云端已经存在，需要先拉取数据合并重冲突 */
        private const val CODE_UNIQUE_ID_EXIST = 1204
    }
}