/*************************************************************************************************
 * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CloudConfigRequestData.kt
 * Description: 请求云端配置的数据结构
 *
 * Version: 1.0
 * Date: 2023/11/16
 * Author: <PERSON><PERSON>@Apps.Gallery3D
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <PERSON>.Wu@Apps.Gallery3D    2023/11/16    1.0              build this module
 ************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import com.google.gson.annotations.SerializedName

/**
 * 请求云端配置的数据结构
 */
data class CloudConfigRequestData(
    /**
     * 云同步开关
     */
    @SerializedName("albumSWAutoSync") private val isAutoSync: Boolean,

    /**
     * 全部图集开关
     */
    @SerializedName("albumSWSyncAllAlbum") private val isSyncAllAlbum: Boolean,

    /**
     * 允许使用移动数据开关
     */
    @SerializedName("albumSWMobileDataAllow") private val isMobileDataAllow: Boolean,

    /**
     * 优化存储空间开关
     */
    @SerializedName("albumSWOptimizeStorage") private val isOptimizeStorage: Boolean,

    /**
     * 全部图片占用存储空间大小
     */
    @SerializedName("localImagesStorageSpace") private val imagesStorage: Long,

    /**
     * 全部视频占用存储空间大小
     */
    @SerializedName("localVideosStorageSpace") private val videosStorage: Long,

    /**
     * 剩余存储空间大小
     */
    @SerializedName("localDeviceRemainStorageSpace") private val remainStorage: Long
)