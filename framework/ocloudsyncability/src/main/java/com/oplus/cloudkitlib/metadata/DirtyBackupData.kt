/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - DirtyBackupData.kt
 * Description: 待备份的脏数据，normal 为正常数据，其余 key 为分好类的异常数据 list
 * Version: 1.0
 * Date: 2023/6/13
 * Author: chenzengxin@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_CLOUD_SYNC
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * chenzengxin@Apps.Gallery3D 2023/6/13     1.0              create
 **************************************************************************************************/
package com.oplus.cloudkitlib.metadata

/**
 * 待备份的脏数据，normal 为正常数据，其余 key 为分好类的异常数据 list
 */
class DirtyBackupData<T> {
    val normalList = mutableListOf<T>()
    val errorMap = mutableMapOf<BackupDataErrorType, MutableList<T>>()

    fun hasError(): Boolean {
        return errorMap.values.any { it.isNotEmpty() }
    }

    override fun toString(): String {
        var msg = "DirtyBackup[normal=${normalList.size}"
        errorMap.forEach { (key, value) ->
            if (value.isNotEmpty()) {
                msg += ",${key.name}=${value.size}"
            }
        }
        msg += "]"
        return msg
    }

    enum class BackupDataErrorType {
        /**
         * 操作类型为update，无锚点
         */
        UPDATE_NO_SYS_VERSION,

        /**
         * 操作类型为delete，无锚点
         */
        DELETE_NO_SYS_VERSION,

        /**
         * 操作类型为delete，无gid
         */

        DELETE_NO_SYS_RECORD_ID,

        /**
         * NEED_FETCH，原定方式为等待下次拉取，发现存在下次拉取也无法拉取的异常情况
         * 怀疑是有些记录的锚点写入失败，但是总锚点设置成功
         * -> 备份上云失败，恢复过程由于总锚点大，也不恢复写入失败的记录
         */
        NEED_FETCH
    }
}