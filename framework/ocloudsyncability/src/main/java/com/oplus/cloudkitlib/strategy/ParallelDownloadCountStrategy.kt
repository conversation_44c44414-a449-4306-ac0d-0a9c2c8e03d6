/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DownloadStrategyParamManager.kt
 ** Description: 下载并行数策略类
 ** Version: 1.0
 ** Date: 2024/4/17
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** <EMAIL>         2024/4/17        1.0           build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.strategy

import com.oplus.gallery.business_lib.cloud.DownloadSpec
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_CLOUD_SYNC_DOWNLOAD
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.foundation.util.systemcore.TemperatureUtil

/**
 * 下载并行数策略类，会根据网速调整下载线程
 *
 * - 当下载速度较快时，我们会开启更多的线程，更快地完成下载
 * - 但是网速较慢时，则会较少线程数，避免相册长期在后台运行
 */
internal class ParallelDownloadCountStrategy {

    /**
     * 并行数量
     *
     * 会根据网速进行调整
     * @see [tryAdjustParallelCountBySpeed]
     */
    var parallelCount = PARALLEL_COUNT_THREE
        private set

    /**
     * 当前的下载阶段
     */
    var downloadSpec: DownloadSpec = DownloadSpec.COMPRESSED
    private var lastAdjustTime: Long = 0

    /**
     * 尝试去调整并行下载数量，30分钟调整一次
     *
     * 下载速度 > 1024kb/s --- 3线程
     * 下载速度 > 512kb/s  --- 2线程
     * 下载速度 < 512kb/s  --- 1线程
     * 速度阈值与下载阶段和并发线程数有关，可见[getSpeedThresholdWeightFactor]
     *
     * 再在下载速度的基础上，根据温度进行调整
     * 37度以下 最大 3线程
     * 37-40度 最大 2线程
     * 40度以上 最大 1线程
     */
    fun tryAdjustParallelCountBySpeed(networkSpeed: Long): Int {
        if (canReAdjustParallelCount().not() || (networkSpeed <= 0)) {
            return parallelCount
        }
        var threeParallelCountSpeedThreshold = THREE_PARALLEL_COUNT_INTERNET_SPEED_THRESHOLD
        var twoParallelCountSpeedThreshold = TWO_PARALLEL_COUNT_INTERNET_SPEED_THRESHOLD
        if (DEBUG_CLOUD_SYNC_DOWNLOAD) {
            threeParallelCountSpeedThreshold = GallerySystemProperties.getInt(
                DEBUG_CLOUD_SYNC_DOWNLOAD_THREE_THREAD_SPEED,
                threeParallelCountSpeedThreshold
            )
            twoParallelCountSpeedThreshold = GallerySystemProperties.getInt(
                DEBUG_CLOUD_SYNC_DOWNLOAD_TWO_THREAD_SPEED,
                twoParallelCountSpeedThreshold
            )
        }
        val weightFactor = getSpeedThresholdWeightFactor()
        val networkSpeedParallelCount = when {
            networkSpeed >= threeParallelCountSpeedThreshold * weightFactor -> PARALLEL_COUNT_THREE
            networkSpeed >= twoParallelCountSpeedThreshold * weightFactor -> PARALLEL_COUNT_TWO
            else -> PARALLEL_COUNT_ONE
        }
        // 当前温度
        val temperature = TemperatureUtil.getCurrentTemperature()
        // 当前温度最大线程数
        val temperatureParallelCount = when {
            // 37度以下 --- 3线程
            temperature < LOW_TEMPERATURE_THRESHOLD -> PARALLEL_COUNT_THREE
            //  37-40度 --- 2线程
            temperature < HIGH_TEMPERATURE_THRESHOLD -> PARALLEL_COUNT_TWO
            // 40度以上 1线程
            else -> PARALLEL_COUNT_ONE
        }
        // 调整的线程数不能大于当前温度最大线程数
        parallelCount = networkSpeedParallelCount.coerceAtMost(temperatureParallelCount).also {
            lastAdjustTime = System.currentTimeMillis()
            GLog.d(TAG, LogFlag.DL) {
                "tryAdjustParallelCountBySpeed, parallelCount = $it," +
                        " weightFactor = $weightFactor," +
                        " temperature = $temperature"
            }
        }
        return parallelCount
    }

    /**
     * 获取网速阈值权重因子
     *
     * 当前的速度阈值是在开启三线程下载大缩图的情况下得出的，不适宜用来衡量1线程的或者其他线程的下载速度
     * 或者下缩图，原图的下载速度
     * 因此
     * 使用下载阶段获取一个下载阶段的速度阈值权重因子
     * 使用当前线程数/3，获取一个线程速度阈值的权重因子
     * 两个因子叠加得到一个总的速度阈值权重因子
     */
    private fun getSpeedThresholdWeightFactor(): Float {
        val downloadSpecWeight = when (downloadSpec) {
            DownloadSpec.ORIGIN -> ORIGIN_DOWNLOAD_SPEED_WEIGHT
            DownloadSpec.COMPRESSED -> COMPRESS_DOWNLOAD_SPEED_WEIGHT
            else -> {
                GLog.w(TAG) { "getSpeedThresholdWeightFactor, not support $downloadSpec" }
                COMPRESS_DOWNLOAD_SPEED_WEIGHT
            }
        }
        val threadWeight = parallelCount.toFloat() / PARALLEL_COUNT_THREE
        return downloadSpecWeight * threadWeight
    }

    /**
     * 是否快速同步模式，界面会显示正在快速同步
     */
    fun isFastDownloadMode(): Boolean {
        return parallelCount >= PARALLEL_COUNT_TWO
    }

    private fun canReAdjustParallelCount(): Boolean {
        return System.currentTimeMillis() - lastAdjustTime >= getAdjustParallelCountTimeThreshold()
    }

    companion object {
        private const val TAG = "ParallelDownloadCountStrategy"

        /**
         * 三线程的下载速度阈值，当下载速度大于该值时开启三线程下载
         *
         * 这个是在开启三线程下载大缩图的情况下得出的，单位kb/s
         */
        private const val THREE_PARALLEL_COUNT_INTERNET_SPEED_THRESHOLD = 1024
        /**
         * 二线程的下载速度阈值，当下载速度大于该值时开启二线程下载
         *
         * 这个是在开启二线程下载大缩图的情况下得出的，单位kb/s
         */
        private const val TWO_PARALLEL_COUNT_INTERNET_SPEED_THRESHOLD = 512
        private const val PARALLEL_COUNT_THREE = 3
        private const val PARALLEL_COUNT_TWO = 2
        private const val PARALLEL_COUNT_ONE = 1

        /**
         * 原图下载速度阈值权重，经验值，原图下载大概是大缩图下载的1.2倍
         */
        private const val ORIGIN_DOWNLOAD_SPEED_WEIGHT = 1.2f
        /**
         * 大缩图下载速度阈值权重
         */
        private const val COMPRESS_DOWNLOAD_SPEED_WEIGHT = 1f

        /**
         * 小缩图下载速度阈值权重，经验值，小缩图下载大概是大缩图下载的0.2倍
         */
        private const val THUMB_DOWNLOAD_SPEED_WEIGHT = 0.2f

        private const val DEBUG_CLOUD_SYNC_DOWNLOAD_THREE_THREAD_SPEED = "debug.three.thread.speed"
        private const val DEBUG_CLOUD_SYNC_DOWNLOAD_TWO_THREAD_SPEED = "debug.two.thread.speed"
        private const val DEBUG_ADJUST_PARALLEL_COUNT_TIME_THRESHOLD = "debug.adjust.parallel.count.time.threshold"

        private const val LOW_TEMPERATURE_THRESHOLD = 37f
        private const val HIGH_TEMPERATURE_THRESHOLD = 40f

        /**
         * 获取调节并行数量的时间阈值
         *
         * 默认30min调整一次
         */
        @JvmStatic
        fun getAdjustParallelCountTimeThreshold(): Int {
            var adjustParallelCountTimeThreshold = DownloadStrategyParamManager.adjustParallelCountTimeThreshold
            if (DEBUG_CLOUD_SYNC_DOWNLOAD) {
                adjustParallelCountTimeThreshold =
                    GallerySystemProperties.getInt(DEBUG_ADJUST_PARALLEL_COUNT_TIME_THRESHOLD, adjustParallelCountTimeThreshold)
            }
            return adjustParallelCountTimeThreshold
        }
    }
}