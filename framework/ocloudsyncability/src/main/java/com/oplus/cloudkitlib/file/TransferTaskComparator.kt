/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TransferTaskComparator.kt
 ** Description:任务排序算法，由于多处用到，抽出来做一个公共类
 ** Version: 1.0
 ** Date: 2023/3/29
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  <EMAIL>    2023/3/29    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.oplus.gallery.business_lib.cloud.TransferPriority

/**
 * 任务排序算法，由于多处用到，抽出来做一个公共类
 * 优先级不同：高优先级先执行；
 * 优先级相同：
 *      MAX优先级：提交时间晚先执行；
 *      其他优先级：小文件比大文件先执行，都是小文件或者大文件则提交时间早先执行。
 */
object TransferTaskComparator : Comparator<FileTransferTaskManager.TransferTask> {

    override fun compare(transferTask: FileTransferTaskManager.TransferTask, other: FileTransferTaskManager.TransferTask): Int {
        //如果两个优先级相同
        if (transferTask.priority == other.priority) {
            //如果是最大优先级，那么时间越大，先执行
            if (transferTask.priority == TransferPriority.MAX) {
                return other.createTime.compareTo(transferTask.createTime)
            } else {
                val thisIsBigFile = transferTask.cloudIOFile.isBigFile()
                val otherIsBigFile = other.cloudIOFile.isBigFile()
                if (thisIsBigFile != otherIsBigFile) {
                    return if (thisIsBigFile) 1 else -1
                }
                return transferTask.createTime.compareTo(other.createTime)
            }
        } else {
            return other.priority.value.compareTo(transferTask.priority.value)
        }
    }
}