/**************************************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - FilePathErrorProcessor.kt
 ** Description: 文件路径错误处理
 ** Version: 1.0
 ** Date : 2023/11/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: --------------------------------------------------------
 **  <author>                       <data>         <version >           <desc>
 **  <EMAIL>    2023/11/15       1.0            build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import android.content.ContentValues
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.CloudColumns
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.framework.abilities.ocloudsync.db.LocalSyncDataVisitor
import com.oplus.gallery.framework.abilities.ocloudsync.metadatasync.ImageFile
import com.oplus.gallery.framework.abilities.ocloudsync.metadatasync.MetadataConflictStrategy.rename
import java.util.regex.Pattern

/**
 * 文件路径错误处理
 * 1.处理文件路径过长
 * 由于相册进行了N次重命名导致文件名过长，如：
 * /storage/emulated/0/DCIM/2023_1_1_1_..._1.jpg
 * 需要重命名到更短的路径
 */
class FilePathErrorProcessor {
    /**
     * 错误处理
     * 1.处理文件路径过长
     */
    fun processError(errorDataList: List<ImageFile>) {
        if (errorDataList.isEmpty()) return
        GLog.d(TAG, "processError")
        errorDataList.forEach { imageFile ->
            imageFile.mOriginalData.let {
                if (isRenameTooManyTimes(it)) {
                    GLog.d(TAG, "processError, filePath = ${PathMask.mask(it)}")
                    renameToShortName(imageFile)
                }
            }
        }
    }

    /**
     * 重命名到更短路径
     *
     * 把路径重置（把云服务重命名的后缀_1_1去掉），再重新命名
     */
    private fun renameToShortName(imageFile: ImageFile): Boolean {
        imageFile.mOriginalData = resetFilePath(imageFile.mOriginalData)
        return imageFile.rename { originalData, _ ->
            if (imageFile.isRecycle()) {
                LocalSyncDataVisitor.updateById(
                    ContentValues().apply { put(CloudColumns.ORIGINAL_DATA, originalData) },
                    imageFile.mLocalId
                )
            }
        }
    }

    /**
     * 重置filePath
     *
     * 把云同步重命名时加的后缀_x去掉
     * in:  /storage/emulated/0/DCIM/Camera/IMG20231115171839_1_1_1.jpg
     * out: /storage/emulated/0/DCIM/Camera/IMG20231115171839.jpg
     */
    private fun resetFilePath(filePath: String): String {
        val suffix = filePath.substring(filePath.lastIndexOf("."))
        return PATTERN_CLOUD_NAME.matcher(filePath).replaceAll(suffix)
    }

    /**
     * 是否重命名过多
     *
     * @return 重命名大于等于10次返回true/重命名小于十次返回false
     */
    private fun isRenameTooManyTimes(filePath: String): Boolean {
        return PATTERN_MAX_RENAME.matcher(filePath).find()
    }

    companion object {
        private const val TAG = "FilePathErrorProcessor"

        private val PATTERN_MAX_RENAME = Pattern.compile("(_\\d){10}")
        private val PATTERN_CLOUD_NAME = Pattern.compile("(?i)(_\\d)+\\.\\S+$")
    }
}