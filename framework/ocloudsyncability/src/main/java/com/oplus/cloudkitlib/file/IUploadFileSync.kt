/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IUploadFileSync.kt
 ** Description: 上传文件控制接口
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.Jialin @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.gallery.business_lib.cloud.TransferPriority

/**
 * 上传文件控制接口
 */
interface IUploadFileSync<T : CloudIOFile> {
    /**
     * 上传所有文件
     */
    fun uploadAllFiles(source: Int): SyncResult

    /**
     * 指定上传批量文件
     */
    fun uploadBatchFiles(source: Int, priority: TransferPriority, fileList: List<T>, callBack: FileTransferTaskManager.ICallBack<T>?): SyncResult
}