/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - DownloadFileSizeComparator.kt
 ** Description: 比较同一个原始文件的下载文件的尺寸
 ** Version: 1.0
 ** Date : 2023/5/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  z<PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2023/5/15        1.0           Add
 **************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libsync.service.CloudIOFile

/**
 * 比较同一个原始文件的下载文件的尺寸
 *
 * 暂时通过比较CloudIOFile.cloudThumbInfo字符串（格式见[com.oplus.gallery.framework.abilities.ocloudsync.file.CloudThumbInfo]）来实现，待重构，更合理的方式是在剥离依赖的类结构里定义变量
 */
class DownloadFileSizeComparator : Comparator<CloudIOFile> {
    override fun compare(a: CloudIOFile, b: CloudIOFile): Int {
        val cloudThumbInfoA = a.cloudThumbInfo
        val cloudThumbInfoB = b.cloudThumbInfo
        if (cloudThumbInfoA == cloudThumbInfoB) return 0
        // 某一边没有thumbInfo，即是原图，则这边大
        if (cloudThumbInfoA.isNullOrEmpty()) return 1
        if (cloudThumbInfoB.isNullOrEmpty()) return -1
        // 某一边thumbInfo更长，即宽高数字的数量级更大，则这边大
        if (cloudThumbInfoA.length != cloudThumbInfoB.length) {
            return cloudThumbInfoA.length - cloudThumbInfoB.length
        }
        // 宽高数字的数量级相等的情况下，使用string的compareTo，即比较第一个差异字符的ASCII码差值
        return cloudThumbInfoA.compareTo(cloudThumbInfoB)
    }
}