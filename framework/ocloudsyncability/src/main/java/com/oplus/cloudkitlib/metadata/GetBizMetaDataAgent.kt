/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - GetBizMetaDataAgent.kt
 * Description:
 * Version: 1.0
 * Date: 2025/4/23
 * Author: linhaihong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_CLOUD_SYNC
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * linhaihong@Apps.Gallery3D 2025/4/23     1.0              create
 **************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import android.content.Context
import com.heytap.cloudkit.libsync.metadata.IGetBizMetaDataDbAgent
import com.oplus.gallery.framework.abilities.basecloudability.base.SourceFlag
import java.io.File

/**
 * Get biz meta data agent
 * 用于cloudkitsdk获取相册元数据
 * @property syncAllow
 * @constructor Create empty Get biz meta data agent
 */
class GetBizMetaDataAgent(private val context: Context, private val syncAllow: ISyncAllow) : IGetBizMetaDataDbAgent {

    override fun getAllDbFileList(): MutableList<File> {
        val fileNameList = arrayOf("", "-shm", "-wal")
        val dbFileList: MutableList<File> = ArrayList()
        for (fileNameEx in fileNameList) {
            dbFileList.add(context.getDatabasePath("gallery.db$fileNameEx"))
        }
        return dbFileList
    }

    override fun onCheckAllowToSync(): Int {
        return syncAllow.onCheckAllowToSync(SourceFlag.SYNC_DEFAULT).resultCode
    }
}
