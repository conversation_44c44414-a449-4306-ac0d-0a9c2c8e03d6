/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - IBackupErrorProcessor.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/9/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/9/23        1.0           Add
 **************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.ErrorProcessFailReason

/**
 * 元数据备份的特定错误码的处理逻辑
 */
interface IBackupErrorProcessor<T : AbsMetaData<*>> {
    /**
     * 处理错误
     * @param errorDataList 出现错误的元数据
     * @return 是否处理成功和处理失败的原因
     */
    fun processError(errorDataList: List<T>): ProcessResult

    /**
     * 是否触发重试
     *
     * @return 是否触发重试
     */
    fun shouldPostRetry(): Boolean = false
}

/**
 * 处理的结果
 * @param isSuccess 是否被成功处理
 * @param failReason 处理失败的可能原因，参照[ErrorProcessFailReason]
 */
data class ProcessResult(val isSuccess: Boolean, val failReason: Int = 0)