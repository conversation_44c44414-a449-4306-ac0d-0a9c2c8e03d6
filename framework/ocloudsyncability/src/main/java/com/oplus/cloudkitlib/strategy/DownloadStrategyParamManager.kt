/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DownloadStrategyParamManager.kt
 ** Description: 下载策略参数云端配置类
 ** Version: 1.0
 ** Date: 2024/4/17
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** <EMAIL>         2024/4/17        1.0           build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.strategy

import android.text.TextUtils
import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils

/**
 * 下载策略参数云端配置类
 */
object DownloadStrategyParamManager {
    private const val TAG = "DownloadStrategyParamManager"
    private const val MODULE_NAME = "down_strategy_parameter"
    private const val KEY_MAX_PARALLEL_DOWNLOAD_COUNT = "max_parallel_download_count"
    private const val KEY_READJUST_PARALLEL_COUNT_TIME_THRESHOLD = "readjust_parallel_count_time_threshold"
    private const val DEFAULT_MAX_PARALLEL_DOWNLOAD_COUNT = 3
    private const val DEFAULT_READJUST_PARALLEL_COUNT_TIME_THRESHOLD = TimeUtils.TIME_30_MIN_IN_MS

    /**
     * 最大允许并行的数量
     */
    @JvmStatic
    val maxParallelCount: Int by lazy {
        CloudParamConfig.getDefaultGroupValue(
            MODULE_NAME,
            KEY_MAX_PARALLEL_DOWNLOAD_COUNT,
            "",
            CloudParamConfig.GROUP_CLOUD_SERVICE
        ).let {
            if ((it is String) && !TextUtils.isEmpty(it)) {
                GLog.d(TAG, " init, maxParallelCount is $it")
                it.toFloatOrNull()?.toInt() ?: DEFAULT_MAX_PARALLEL_DOWNLOAD_COUNT
            } else {
                DEFAULT_MAX_PARALLEL_DOWNLOAD_COUNT
            }
        }
    }

    /**
     * 调整并行数量的时间阈值
     *
     * 默认30分钟调整一次并行数量
     */
    @JvmStatic
    val adjustParallelCountTimeThreshold: Int by lazy {
        CloudParamConfig.getDefaultGroupValue(
            MODULE_NAME,
            KEY_READJUST_PARALLEL_COUNT_TIME_THRESHOLD,
            "",
            CloudParamConfig.GROUP_CLOUD_SERVICE
        ).let {
            if ((it is String) && !TextUtils.isEmpty(it)) {
                GLog.d(TAG, " init, adjustParallelCountTimeThreshold is $it")
                it.toFloatOrNull()?.toInt() ?: DEFAULT_READJUST_PARALLEL_COUNT_TIME_THRESHOLD
            } else {
                DEFAULT_READJUST_PARALLEL_COUNT_TIME_THRESHOLD
            }
        }
    }
}