/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsMetadataFlow.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import android.content.Context
import com.heytap.cloudkit.libcommon.bean.io.CloudStopType
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.ICloudBackupMetaData
import com.heytap.cloudkit.libsync.ext.ICloudRecoveryMetaData
import com.heytap.cloudkit.libsync.metadata.helper.CloudBackupRequestSource
import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.oplus.cloudkitlib.base.CloudKitConstants.BACKUP_MAX_NUMBER
import com.oplus.gallery.framework.abilities.basecloudability.base.EnvConfig
import com.oplus.gallery.framework.abilities.basecloudability.base.SourceFlag
import com.oplus.cloudkitlib.base.SyncLock
import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.gallery.business_lib.cloudsync.SyncResultConstants
import com.oplus.cloudkitlib.util.CloudDebugUtil
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.ErrorProcessFailReason
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_CLOUD_ERROR_PROCESS
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.ocloudsync.BuildConfig
import com.oplus.gallery.framework.abilities.ocloudsync.GallerySyncAgent
import com.oplus.gallery.framework.abilities.ocloudsync.OCloudSyncApi
import com.oplus.gallery.framework.abilities.ocloudsync.statistics.CloudSyncTrackHelper
import com.oplus.gallery.framework.abilities.ocloudsync.statistics.details.SyncLinkTrackManager
import com.oplus.gallery.framework.abilities.ocloudsync.utils.CloudStatusPref
import com.oplus.gallery.standard_lib.scheduler.BatchProcess
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlin.random.Random

abstract class AbsMetadataFlow<T : AbsMetaData<*>>(
    val context: Context,
    val syncAllow: ISyncAllow,
) : IMetaDataSync,
    IMetaDataListener<T> {

    abstract val module: String
    abstract val zone: String
    open val requestSource: CloudRecoveryRequestSource = CloudRecoveryRequestSource.MANUAL
    abstract val cloudDataType: CloudDataType
    abstract val envConfig: EnvConfig
    abstract val metadataBackupErrorMap: MutableMap<DirtyBackupData.BackupDataErrorType, MutableList<T>>
    abstract val tag: String

    /**
     * 业务方recordType的版本，用于接入CloudKit后业务方field字段内容变更的兼容处理
     * （例如版本为1时field里共2个字段，后续业务方在field中加了一个字段，则recordTypeVersion可升级为2，这样服务端可根据不同版本返回不同的field内容从而进行兼容处理）
     */
    open val recordTypeVersion = 0

    @Volatile
    protected var stopAll = false

    private val backupErrorProcessors = mutableMapOf<BackupDataResult.BackupError, IBackupErrorProcessor<T>>()

    /**
     * 获取已同步的元数据数量，调用completeRecoveryMetaData时作为参数传给cloudkit，为了解决客户端与云端数据不一致问题
     * 云端会判断如果客户端上报的totalCount不等于云端的totalCount，则会发送push通知客户端，客户端收到此类型的push后需要做全量recovery
     * 公共配置不需要覆写，其他需要覆写
     */
    protected abstract fun getBackedUpMetadataTotalCount(): Long

    /**
     * 获取本地媒体表同步有关数据的个数，调用completeRecoveryMetaData时作为参数传给cloudkit，为了解决客户端与云端数据不一致问题
     * 目前只有媒体库元数据复写，其他类型元数据暂不上报
     */
    protected open fun getCloudMetadataReportJsonInfo(): String? {
        return null
    }

    protected fun addBackupErrorProcessor(error: BackupDataResult.BackupError, backupErrorProcessor: IBackupErrorProcessor<T>) {
        backupErrorProcessors[error] = backupErrorProcessor
    }

    protected open fun postMetadataSync(source: Int) {}

    private fun startRecoveryMetaData(source: Int): SyncResult {
        val syncAllowResult = syncAllow.onCheckAllowToSync(source)
        if (syncAllowResult.isSuccess.not()) {
            return syncAllowResult
        }
        val debugResult = CloudDebugUtil.recoveryMetaData(envConfig, this)
        if (debugResult != null) {
            return debugResult
        }
        val syncLock = SyncLock()
        val callback = object : ICloudRecoveryMetaData {
            override fun onSuccess(
                cloudDataType: CloudDataType,
                cloudMetaDataRecords: List<CloudMetaDataRecord>,
                hasMoreData: Boolean,
                totalCount: Long,
                remainCount: Long
            ) {
                if (envConfig == EnvConfig.PROCESS_TEST) {
                    cloudMetaDataRecords.forEach {
                        GLog.d(tag, "[startRecoveryMetaData] onSuccess $it")
                    }
                }
                runCatching {
                    val list = mutableListOf<T>()
                    cloudMetaDataRecords.forEach { onMetaDataConvert(it)?.let { record -> list.add(record) } }
                    onMetaDataBatchRecoverySuccess(list, hasMoreData, totalCount, remainCount)
                    OCloudSyncApi.apiClient.completeRecoveryMetaData(
                        module,
                        zone,
                        true,
                        cloudDataType,
                        getBackedUpMetadataTotalCount(),
                        getCloudMetadataReportJsonInfo(),
                        GetBizMetaDataAgent(context, syncAllow)
                    ) { isNeedFullSync, isNeedRepairData ->
                        if (isNeedFullSync) {
                           //端云不一致的时候，在push不可达的情况下，服务端下发全量同步标识
                            CloudStatusPref.setNeedClearAndFullSync(context, true)
                        }
                        if (isNeedRepairData) {
                            //端云不一致的时候，服务端下发修复异常数据标识
                            CloudStatusPref.setNeedRepairData(context, true)
                        }
                    }
                    val result = if (hasMoreData) {
                        SyncResult(true, SyncResultConstants.RESULT_RECOVERY_HAS_MORE)
                    } else {
                        SyncResult(true, SyncResultConstants.RESULT_SUCCESS)
                    }
                    syncLock.notifyFinish(result)
                }.onFailure {
                    if (BuildConfig.DEBUG) {
                        throw it
                    }
                    GLog.e(tag, "[startRecoveryMetaData] onSuccess error during process ", it)
                    syncLock.notifyFinish(SyncResult(false, SyncResultConstants.RESULT_FAIL))
                }
            }

            override fun onError(cloudDataType: CloudDataType, error: CloudKitError) {
                onMetaDataBatchRecoveryError(error)
                syncLock.notifyFinish(SyncResult(error))
            }
        }
        if (SourceFlag.match(source, SourceFlag.SYNC_FULL)) {
            OCloudSyncApi.apiClient.startFullRecoveryMetaData(
                module,
                zone,
                requestSource,
                cloudDataType,
                recordTypeVersion,
                callback
            )
        } else {
            OCloudSyncApi.apiClient.startRecoveryMetaData(
                module,
                zone,
                requestSource,
                cloudDataType,
                recordTypeVersion,
                callback
            )
        }
        return syncLock.waitReturn(SyncLock.DEFAULT_WAIT_TIME)
    }

    private fun startBackupMetaData(source: Int, backupList: List<T>): SyncResult {
        if (backupList.isEmpty()) {
            return SyncResult(true, SyncResultConstants.RESULT_SUCCESS)
        }
        val result = syncAllow.onCheckAllowToSync(source)
        if (!result.isSuccess) {
            return result
        }
        if (envConfig == EnvConfig.PROCESS_TEST) {
            backupList.forEach {
                GLog.d(tag, "startBackupMetaData $it")
            }
        }
        val syncLock = SyncLock()
        val debugResult = CloudDebugUtil.backupMetaData(envConfig, backupList, this)
        if (debugResult != null) {
            return debugResult
        }

        /*
         调试用，备份前延迟一段时间，方便模拟 1202-需要拉取 错误
         操作步骤如下：
         1.测试机执行命令：adb shell setprop debug.gallery.delay.backup 20
         2.测试机移动某个已上传文件
         3.等待5s
         4.辅助机删除同一个文件
         */
        val delay = GProperty.DEBUG_GALLERY_CLOUD_SYNC_DELAY_BACKUP * TimeUtils.TIME_1_SEC_IN_MS
        if (delay > 0) {
            GLog.d(tag, "startBackupMetaData: delay=$delay")
            Thread.sleep(delay)
        }

        OCloudSyncApi.apiClient.startBackupMetaData(
            module,
            zone,
            CloudBackupRequestSource.DATA_CHANGE,
            cloudDataType,
            recordTypeVersion,
            backupList,
            object : ICloudBackupMetaData {
                override fun onSuccess(
                    cloudDataType: CloudDataType,
                    successData: List<CloudBackupResponseRecord>,
                    errorData: List<CloudBackupResponseError>
                ) {
                    if (envConfig == EnvConfig.PROCESS_TEST) {
                        successData.forEach {
                            GLog.d(tag, "backup successData $it")
                        }
                        errorData.forEach {
                            GLog.d(tag, "backup errorData $it")
                        }
                    }
                    syncLock.notifyFinish(SyncResult(BackupDataResult(backupList, successData, errorData)))
                }

                override fun onError(cloudDataType: CloudDataType, error: CloudKitError) {
                    if (envConfig == EnvConfig.PROCESS_TEST) {
                        GLog.d(tag, "onError $error")
                    }
                    syncLock.notifyFinish(SyncResult(error))
                }
            })
        return syncLock.waitReturn(SyncLock.DEFAULT_WAIT_TIME)
    }

    override fun sync(source: Int): SyncResult {
        GLog.d(tag, "sync source=$source")
        val startTime = System.currentTimeMillis()
        stopAll = false
        var result = recovery(source)
        if (result.isSuccess) {
            result = backup(source)
        }
        CloudSyncTrackHelper.trackAlbumMetadataInvalid(context)
        GLog.d(tag, "sync recovery_backup cloud sync cost time=" + (System.currentTimeMillis() - startTime))
        return result
    }

    override fun recovery(source: Int): SyncResult {
        GLog.d(tag, "recovery, start source=${SourceFlag.getSourceDescription(source)}")
        var result = syncAllow.onCheckAllowToSync(source)
        onTriggerResult(result)

        if (!result.isSuccess) {
            return result
        }
        onMetaDataRecoveryStart()
        var adjustSource = source

        onMetadataErrorProcess(metadataBackupErrorMap).let { needRecoveryDataList ->
            if (needRecoveryDataList.isNotEmpty()) {
                // 非正常恢复流程，remainCount 为 0，不显示特殊同步提示语
                onMetaDataBatchRecoverySuccess(successList = needRecoveryDataList, hasMoreData = false, totalCount = 0, remainCount = 0)
            }
        }
        metadataBackupErrorMap.clear()

        do {
            result = startRecoveryMetaData(adjustSource)
            if (envConfig == EnvConfig.PROCESS_TEST) {
                GLog.d(tag, "recovery startRecoveryMetaData result ${result.isSuccess}")
            }
            if (result.isSuccess.not() || (result.resultCode != SyncResultConstants.RESULT_RECOVERY_HAS_MORE)) break
            // 外部触发全量恢复后，会清除恢复锚点，故只需要调一次，后续都按照默认恢复执行
            if (SourceFlag.match(source, SourceFlag.SYNC_FULL)) adjustSource = SourceFlag.SYNC_DEFAULT
        } while (!stopAll)

        onMetaDataRecoveryEnd(result)
        GLog.d(tag, "recovery, end")
        return result
    }

    /**
     * 同步触发结果，整个同步开始的地方，当前为元数据恢复，标识是否开始执行和云端有关的逻辑。
     */
    protected open fun onTriggerResult(result: SyncResult) {}

    override fun backup(source: Int): SyncResult {
        GLog.d(tag, "backup, start, source=${SourceFlag.getSourceDescription(source)}")
        var result = syncAllow.onCheckAllowToSync(source)
        if (!result.isSuccess) {
            return result
        }

        onMetaDataBackupStart()
        val prepareSuccess = GallerySyncAgent.INSTANCE.prepareBackupData(
            context,
            false,
            { syncAllow.onCheckAllowToSync(source).isSuccess }
        )
        /*
         准备备份不成功，不一定是syncAllow.onCheckAllowToSync条件不满足，需要再校验一下
         */
        if (!prepareSuccess) {
            result = syncAllow.onCheckAllowToSync(source)
            if (!result.isSuccess) {
                return result
            }
        }
        val isRetry = SourceFlag.match(source, SourceFlag.SYNC_RETRY)
        var isContainFailed = false

        // 1.获取所有需要备份数据
        val backupData = onGetBackupData()
        GLog.d(tag, "backup, backupData=$backupData")
        // 1.1 暂存异常 backup 数据，等待下次恢复过程处理
        if (backupData.hasError()) {
            metadataBackupErrorMap.putAll(backupData.errorMap)
            isContainFailed = true
        }

        val normalBackupList = backupData.normalList
        if (normalBackupList.isEmpty()) {
            GLog.d(tag, "backup, normalBackupList is empty, return")
            checkIfNeedPostMetaDataSync(isContainFailed, isRetry)
            onMetaDataBackupEnd(result)
            return result
        }

        // 2.分批进行备份
        object : BatchProcess<T>(normalBackupList, BACKUP_MAX_NUMBER) {
            override fun processOneBatch(items: List<T>): List<T> {
                if (stopAll) {
                    GLog.d(tag, "backup, processOneBatch, stopAll")
                    stop()
                    return emptyList()
                }
                GLog.d(tag, "backup, processOneBatch items.size=${items.size}")
                result = startBackupMetaData(source, items).let { syncResult ->
                    val backupResult = syncResult.backupDataResult as? BackupDataResult<T>
                    if (backupResult != null) {
                        GLog.d(tag, "backup: backupResult=$backupResult")
                        // 处理成功
                        onMetaDataBatchBackupServerResult(backupResult.successList)

                        // 记录处理结果存在失败的情况
                        if (backupResult.errors.isNotEmpty()) {
                            GLog.w(tag, LogFlag.DL) { "backup: backupResult=${backupResult.errors}" }
                            isContainFailed = true
                        }

                        // 针对错误码进行处理
                        val remainUnprocessedError = processBackupFailed(backupResult, isRetry)
                        GLog.d(tag, "backup, remainUnprocessedError=$remainUnprocessedError")

                        // 即使存在错误码，并且处理不了，备份结果也返回成功，这样 sync() 里就不会阻塞文件上传下载流程
                        SyncResult(true, SyncResultConstants.RESULT_SUCCESS).also {
                            if (backupResult.errors.isNotEmpty()) {
                                it.trackMetadataBackupErrorCode = backupResult.errors.keys.first()
                                GLog.d(tag, "processOneBatch, trackResultCode=${it.trackMetadataBackupErrorCode}")
                            }
                        }
                    } else {
                        // 环境问题导致，非元数据问题，startBackupMetaData 中 onError 接口的情况，此时直接结束，因为环境出问题了，下一批也注定失败
                        stop()
                        syncResult.error?.let { onMetaDataBatchBackupError(it) } ?: GLog.e(tag, "backup timeout!")
                        syncResult
                    }
                }
                return emptyList()
            }
        }.process()

        // 3.集中处理失败结果，如果存在失败的批次，仅发起一次重试，不论结果如何，本轮同步结束，避免无限重试。
        checkIfNeedPostMetaDataSync(isContainFailed, isRetry)

        onMetaDataBackupEnd(result)
        GLog.d(tag, "backup, end, syncResult=$result")
        return result
    }

    private fun checkIfNeedPostMetaDataSync(isContainFailed: Boolean, isRetry: Boolean) {
        GLog.d(tag, "checkIfNeedPostMetaDataSync, isContainFailed=$isContainFailed, isRetry=$isRetry")
        if (isContainFailed && isRetry.not()) {
            postMetadataSync(SourceFlag.SYNC_RETRY)
        }
    }

    /**
     * 尝试修复 backup 过程中遇到的问题。
     * @return remainUnprocessedError 是否遗留无法处理的错误，true 代表遗留
     */
    private fun processBackupFailed(backupResult: BackupDataResult<T>, isRetry: Boolean): Boolean {
        var remainUnprocessedError = false
        debugTrackBackupError()
        backupResult.errors.forEach { (errorCode, errorDataList) ->
            val error = BackupDataResult.BackupError.values().find {
                it.code == errorCode
            }
            val errorInfo = error?.toString() ?: "UNKNOWN($errorCode)"
            GLog.d(tag, "processBackupFailed, error=$errorInfo, size=${errorDataList.size}")

            var result = ProcessResult(false)
            error?.let {
                backupErrorProcessors[it]
            }?.run {
                result = processError(errorDataList)
                if (!result.isSuccess) {
                    remainUnprocessedError = true
                }
            } ?: run {
                GLog.w(tag, "processBackupFailed, no errorProcessor for this error")
                remainUnprocessedError = true
            }
            CloudSyncTrackHelper.trackBackupError(
                module,
                isRetry,
                errorCode,
                errorDataList.size,
                result.isSuccess,
                result.failReason
            )
            SyncLinkTrackManager.saveCountForStage(
                CloudSyncTrackConstant.Key.SyncLink.CURRENT_MEDIA_FILE_METADATA_BACKUP_COUNT,
                errorDataList.size
            )
        }
        return remainUnprocessedError
    }

    private fun debugTrackBackupError() {
        if (DEBUG_CLOUD_ERROR_PROCESS.not()) return
        CloudSyncTrackHelper.trackBackupError(
            module,
            false,
            BackupDataResult.BackupError.NEED_FETCH.code,
            Random.nextInt(),
            false,
            ErrorProcessFailReason.INVALID_ID or ErrorProcessFailReason.FILE_NOT_EXISTS
        )
    }

    override fun stopSyncMetaData(limitBizSubErrorCode: Int?) {
        stopAll = true
        if (limitBizSubErrorCode == null) {
            OCloudSyncApi.apiClient.stopRecoveryMetaData(module, zone, cloudDataType)
            OCloudSyncApi.apiClient.stopBackupMetaData(module, zone, cloudDataType)
        } else {
            OCloudSyncApi.apiClient.stopRecoveryMetaData(module, zone, cloudDataType, CloudStopType.LIMIT, limitBizSubErrorCode)
            OCloudSyncApi.apiClient.stopBackupMetaData(module, zone, cloudDataType, CloudStopType.LIMIT, limitBizSubErrorCode)
        }
    }

    /**
     * 清除SDK内部的锚点，从而请求接口会返回全量数据
     * */
    open fun clearSysVersion(): CloudKitError {
        return OCloudSyncApi.apiClient.clearSysVersion(module, zone, cloudDataType)
    }
}