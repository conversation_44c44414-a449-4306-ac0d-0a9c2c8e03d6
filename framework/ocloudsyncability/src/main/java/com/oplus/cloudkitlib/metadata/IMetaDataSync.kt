/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IMetaDataSync.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import com.oplus.cloudkitlib.base.SyncResult

interface IMetaDataSync {

    /**
     * 同步数据
     */
    fun sync(source: Int): SyncResult

    /**
     * 恢复
     */
    fun recovery(source: Int): SyncResult

    /**
     * 备份
     */
    fun backup(source: Int): SyncResult

    /**
     * 终止
     */
    fun stopSyncMetaData(limitBizSubErrorCode: Int?)
}