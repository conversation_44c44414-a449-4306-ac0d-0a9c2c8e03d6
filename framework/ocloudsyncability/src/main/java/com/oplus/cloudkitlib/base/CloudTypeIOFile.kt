/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: CloudTypeIOFile.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/9/10
 ** Author: maliang
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version>     <desc>
 **  maliang                        2024/9/10        1.0          Add
 **************************************************************************************************/
package com.oplus.cloudkitlib.base

import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile

open class CloudTypeIOFile(val cloudDataType: CloudDataType = CloudDataType.PRIVATE) : CloudIOFile() {

    companion object {
        private const val TAG = "CloudTypeIOFile"

        /**
         * 通过 CloudIOFile 来创建 CloudTypeIOFile，可以使用以下方法获取 CloudIOFile 对象：
         * [CloudIOFile.createUploadFile]
         * [CloudIOFile.createDownloadFile]
         * [CloudIOFile.createDownloadThumbFile]
         * [CloudIOFile.createDownloadShareFile]
         * [CloudIOFile.createUploadShareFile]
         * [CloudIOFile.createDownloadShareThumbFile]
         */
        fun createFile(file: CloudIOFile, cloudDataType: CloudDataType = CloudDataType.PRIVATE): CloudTypeIOFile {
            return CloudTypeIOFile(cloudDataType).apply {
                set(file)
            }
        }
    }
}