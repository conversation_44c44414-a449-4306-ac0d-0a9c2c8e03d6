/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CustomCloudKitError.kt
 ** Description: 相册业务自定义的错误类
 ** Version: 1.0
 ** Date: 2023/4/25
 ** Author: linhaihong@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** linhaihong@Apps.Gallery3D      2023/4/25    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.cloudkitlib.base

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError

/**
 * Custom cloud kit error
 *
 * @property cloudKitError ck定义的错误码
 * @property bizErrorCode 相册业务方自定义的错误码
 * @constructor Create empty Custom cloud kit error
 */
data class CustomCloudKitError(val cloudKitError: CloudKitError, val bizErrorCode: Int = 0)