/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CloudIOErrorFile.kt
 ** Description: CloudIOErrorFile
 **
 **
 ** Version: 1.0
 ** Date:2022/12/26
 ** Author:<EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** gao<PERSON><PERSON><PERSON>@Apps.Gallery        2022/12/26	   1.0         build this module
 ********************************************************************************/
package com.oplus.cloudkitlib.base

import com.heytap.cloudkit.libsync.service.CloudIOFile

data class CloudIOErrorFile<T : CloudIOFile>(
    val cloudIOFile: T,
    val customCloudKitError: CustomCloudKitError
)