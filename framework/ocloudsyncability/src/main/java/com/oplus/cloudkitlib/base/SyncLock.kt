/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SyncLock.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON><PERSON><PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.base

import android.os.SystemClock
import android.util.Log
import com.oplus.gallery.business_lib.cloudsync.SyncResultConstants

class SyncLock : Object {

    companion object {
        const val TAG = "SyncLock"
        const val DEFAULT_WAIT_TIME = 3 * 60 * 1000L //3分钟
    }

    @Volatile
    private var finished: Boolean

    @Volatile
    var syncResult: SyncResult

    constructor() : super() {
        finished = false
        syncResult = SyncResult(false, SyncResultConstants.RESULT_FAIL)
    }

    fun notifyFinish(result: SyncResult) {
        synchronized(this) {
            finished = true
            syncResult = result
            notifyAll()
        }
    }

    @JvmOverloads
    fun waitReturn(timeoutMs: Long = 0L): SyncResult {
        synchronized(this) {
            if (timeoutMs == 0L) {
                while (!finished) {
                    try {
                        this.wait()
                    } catch (e: InterruptedException) {
                        Log.d(TAG, "waitReturn $e")
                    }
                }
            } else {
                var now = SystemClock.elapsedRealtime()
                val end = now + timeoutMs
                while (!finished && now < end) {
                    try {
                        this.wait(end - now)
                    } catch (e: InterruptedException) {
                        Log.d(TAG, "waitReturn $e")
                    }
                    now = SystemClock.elapsedRealtime()
                }
            }
            return syncResult
        }
    }
}