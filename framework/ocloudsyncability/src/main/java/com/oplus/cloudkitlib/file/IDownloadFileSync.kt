/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IDownloadFileSync.kt
 ** Description: 下载文件控制接口
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>alin @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.gallery.business_lib.cloud.TransferPriority

/**
 * 下载文件控制接口
 */
interface IDownloadFileSync<T : CloudIOFile> {
    /**
     * 下载所有文件
     */
    fun downloadAllFiles(source: Int): SyncResult

    /**
     * 指定下载文件
     */
    fun downloadFile(source: Int, priority: TransferPriority, file: T, callBack: FileTransferTaskManager.ICallBack<T>?): Int

    /**
     * 指定下载批量文件
     */
    fun downloadBatchFiles(source: Int, priority: TransferPriority, fileList: List<T>, callBack: FileTransferTaskManager.ICallBack<T>?): SyncResult
}