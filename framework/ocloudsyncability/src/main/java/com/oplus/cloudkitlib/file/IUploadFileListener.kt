/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IUploadFileListener.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.cloudkitlib.base.CustomCloudKitError
import com.oplus.cloudkitlib.base.SyncResult

interface IUploadFileListener<T : CloudIOFile> {
    /**
     * 上传文件流程开始
     */
    fun onUploadAllFilesStart()

    /**
     * 获取需要上传的文件，会被多次调用，直到返回到的list为空
     */
    fun onUploadFilesGet(): List<T>

    /**
     * 更新上传文件进度
     */
    fun onUploadFileProgress(cloudIOFile: T, progress: Double)

    /**
     * 单个文件上传结束
     */
    fun onUploadFileFinish(success: Boolean, cloudIOFile: T, customCloudKitError: CustomCloudKitError)

    /**
     * 返回批量文件上传结果
     */
    fun onUploadFilesBatchResult(result: SyncResult, successFileList: List<T>, errorFileList: List<CloudIOErrorFile<T>>)

    /**
     * 上传文件流程结束
     */
    fun onUploadAllFilesEnd(result: SyncResult)
}