/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbsFileFlow.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.cloudkitlib.base.CustomCloudKitError
import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.cloudkitlib.metadata.ISyncAllow
import com.oplus.cloudkitlib.metadata.SyncHandler
import com.oplus.gallery.foundation.util.debug.GLog

abstract class AbsFileFlow<T : CloudIOFile>(
    protected val fileSyncAllow: ISyncAllow,
    protected val metadataSyncHandler: SyncHandler,
    protected val fileTransferTaskManager: FileTransferTaskManager,
) : IFileSync<T> {

    abstract val tag: String

    @Volatile
    protected var stopAll = false
    @Volatile
    // 标记本次文件同步是否被中断
    private var stopSyncFlag = false
    /**
     * 不允许不同线程同时调用，在fileSyncHandler中控制单线程调用此方法
     */
    final override fun syncFiles(source: Int): SyncResult {
        stopAll = false
        return onSyncFiles(source)
    }

    final override fun stopAllSyncFiles() {
        stopAll = true
        fileTransferTaskManager.stopAll()
    }

    override fun setStopSyncFlag(isStopSync: Boolean) {
        GLog.d(tag, "setStopSyncFlag, isStopSync=$isStopSync")
        this.stopSyncFlag = isStopSync
    }

    override fun isStopSync(): Boolean {
        return this.stopSyncFlag
    }

    /**
     * 同步文件
     */
    abstract fun onSyncFiles(source: Int): SyncResult

    /**
     * syncAllow方法返回false，构造错误对象列表
     */
    protected fun syncDisallowResultList(fileList: List<T>, syncResult: SyncResult): List<CloudIOErrorFile<T>> {
        val error = CustomCloudKitError(CloudKitError(0, "syncAllow()=false", CloudBizError.LIMIT_STOP), syncResult.resultCode)
        return fileList.map { CloudIOErrorFile(it, error) }
    }
}