/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ITransferFilesCallBack
 ** Description:ITransferFilesCallBack
 ** Version: 1.0
 ** Date : 2022/4/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/29        1.0          Add
 *******************************************************************/
package com.oplus.cloudkitlib.base

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile

interface ITransferFilesCallBack {
    /**
     * 批量结果回调
     */
    fun batchResult(
        successFileList: List<CloudIOFile>,
        errorFileList: List<CloudIOErrorFile<CloudIOFile>>,
        cloudDateType: CloudDataType
    )

    /**
     * 单个文件结果回调
     */
    fun onIOResult(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError)

    /**
     * 单个文件进度回调
     */
    fun onProgress(file: CloudIOFile, cloudDataType: CloudDataType, progress: Double)
}