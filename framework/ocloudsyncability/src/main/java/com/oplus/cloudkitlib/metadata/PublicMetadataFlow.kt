/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PublicMetadataFlow.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/9/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/9/23        1.0           Add
 **************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import android.content.Context
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.oplus.cloudkitlib.base.SyncResult

/**
 * 公共元数据同步，只有恢复，没有备份
 */
abstract class PublicMetadataFlow<T : AbsMetaData<*>>(
    context: Context,
    syncAllow: ISyncAllow,
) : AbsMetadataFlow<T>(context, syncAllow) {
    final override fun getBackedUpMetadataTotalCount() = DEFAULT_BACKED_UP_METADATA_TOTAL_COUNT

    override fun onGetBackupData(): DirtyBackupData<T> = DirtyBackupData()

    final override fun onMetaDataBackupStart() {
        // do nothing
    }

    final override fun onMetaDataBatchBackupServerResult(successList: List<T>) {
        // do nothing
    }

    final override fun onMetaDataBatchBackupError(error: CloudKitError) {
        // do nothing
    }

    final override fun onMetaDataBackupEnd(result: SyncResult) {
        // do nothing
    }

    final override fun onMetadataErrorProcess(errorMap: Map<DirtyBackupData.BackupDataErrorType, MutableList<T>>): List<T> {
        return emptyList()
    }

    companion object {
        private const val DEFAULT_BACKED_UP_METADATA_TOTAL_COUNT = -1L
    }
}
