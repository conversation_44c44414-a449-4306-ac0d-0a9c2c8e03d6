/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IMetaDataListener.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.oplus.cloudkitlib.base.SyncResult

interface IMetaDataListener<T : AbsMetaData<*>> {

    /**
     *  开始恢复所有元数据
     */
    fun onMetaDataRecoveryStart()

    /**
     *  数据解析，服务端返回的CloudMetaDataRecord转换成本地可识别的元数据
     */
    fun onMetaDataConvert(cloudMetaDataRecord: CloudMetaDataRecord): T?

    /**
     *  恢复数据成功返回的结果
     *  @param successList 当前批次恢复的数据
     *  @param hasMoreData 是否还有数据可以继续恢复
     *  @param totalCount 云端总数据
     *  @param remainCount 云端剩余待恢复数据
     */
    fun onMetaDataBatchRecoverySuccess(successList: List<T>, hasMoreData: Boolean, totalCount: Long, remainCount: Long)

    /**
     *  恢复流程中处理异常的元数据
     */
    fun onMetadataErrorProcess(errorMap: Map<DirtyBackupData.BackupDataErrorType, MutableList<T>>): List<T>

    /**
     *  恢复数据失败返回的结果
     */
    fun onMetaDataBatchRecoveryError(error: CloudKitError)

    /**
     *  恢复元数据流程结束
     */
    fun onMetaDataRecoveryEnd(result: SyncResult)

    /**
     * 开始备份数据
     */
    fun onMetaDataBackupStart()

    /**
     * 获取需要备份的元数据
     */
    fun onGetBackupData(): DirtyBackupData<T>

    /**
     *  备份数据云端返回结果，包括有数据备份错误的情况
     *  @param successList 备份成功的数据
     */
    fun onMetaDataBatchBackupServerResult(successList: List<T>)

    /**
     *  备份数据失败返回的结果
     */
    fun onMetaDataBatchBackupError(error: CloudKitError)

    /**
     *  备份数据流程结束
     */
    fun onMetaDataBackupEnd(result: SyncResult)
}