/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IDownloadFileListener.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.cloudkitlib.base.CustomCloudKitError
import com.oplus.cloudkitlib.base.SyncResult

interface IDownloadFileListener<T : CloudIOFile> {
    /**
     * 下载文件流程开始
     */
    fun onDownloadAllFilesStart()

    /**
     * 获取需要下载的文件，获取会被多次调用
     */
    fun onDownloadFilesGet(source: Int): List<T>

    /**
     * 更新文件下载进度
     */
    fun onDownloadFileProgress(cloudIOFile: T, progress: Double)

    /**
     * 单个文件下载结束
     */
    fun onDownloadFileFinish(success: Boolean, cloudIOFile: T, customCloudKitError: CustomCloudKitError)

    /**
     * 返回批量文件下载结果
     */
    fun onDownloadFilesBatchResult(result: SyncResult, successFileList: List<T>, errorFileList: List<CloudIOErrorFile<T>>)

    /**
     * 下载文件流程结束
     */
    fun onDownloadAllFilesEnd(result: SyncResult)
}