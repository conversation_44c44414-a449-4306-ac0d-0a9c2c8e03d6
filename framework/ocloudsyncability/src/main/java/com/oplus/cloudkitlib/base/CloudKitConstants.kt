/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CloudKitConstants.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.base

object CloudKitConstants {

    const val OPERATOR_TYPE_CREATE = "create" //新建元数据
    const val OPERATOR_TYPE_CREATE_AND_RECYCLE = "createAndRecycle" //创建在回收站中的文件
    const val OPERATOR_TYPE_MODIFY = "modify" //修改元数据字段
    const val OPERATOR_TYPE_DELETE = "deleteAndReplace" //彻底删除元数据
    const val OPERATOR_TYPE_REPLACE = "replace" //替换整个元数据（确保元数据是新的）
    const val OPERATOR_TYPE_RECYCLE = "recycleAndReplace" //把元数据挪到回收站
    const val OPERATOR_TYPE_RESUME = "resumeAndReplace" //把元数据从回收站 恢复出来

    const val METADATA_TYPE_TEXT = 1 //1 纯文本普通数据
    const val METADATA_TYPE_FILE = 2 //2 文件数据

    const val SYS_STATUS_NORMAL = 0
    const val SYS_STATUS_DELETED = 1
    const val SYS_STATUS_RECYCLE = 2

    const val ONE_BATCH_TRANSFER_DEFAULT_MAX_SIZE = (25 * 1024 * 1024).toLong()
    const val ONE_BATCH_TRANSFER_DEFAULT_MAX_NUMBER = 20

    const val BACKUP_MAX_NUMBER = 500
}