/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IFileSync.kt
 ** Description: 文件同步控制接口
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>alin @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.cloudkitlib.file

import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.SyncResult

/**
 * 文件同步控制接口
 */
interface IFileSync<T : CloudIOFile> {

    /**
     * 同步文件
     */
    fun syncFiles(source: Int): SyncResult

    /**
     * 终止同步文件
     */
    fun stopAllSyncFiles()

    /**
     * 当前批次的同步任务完成后停止同步，用于高优先级任务插入
     */
    fun setStopSyncFlag(isStopSync: Boolean)

    /** 返回当前任务是否被中断 */
    fun isStopSync(): Boolean
}