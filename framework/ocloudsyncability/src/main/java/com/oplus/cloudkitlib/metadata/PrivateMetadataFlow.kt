/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PrivateMetadataFlow.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/9/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/9/23        1.0           Add
 **************************************************************************************************/
package com.oplus.cloudkitlib.metadata

import android.content.Context
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.ErrorProcessFailReason

/**
 * 私有元数据同步，有恢复和备份
 */
abstract class PrivateMetadataFlow<T : AbsMetaData<*>>(
    context: Context,
    syncAllow: ISyncAllow,
    private val metadataSyncHandler: SyncHandler
) : AbsMetadataFlow<T>(context, syncAllow) {

    init {
        addBackupErrorProcessor(BackupDataResult.BackupError.SYS_RECORD_ID_NOT_FOUND, object : IBackupErrorProcessor<T> {
            override fun processError(errorDataList: List<T>): ProcessResult {
                return resetMetadata(errorDataList)
            }
        })
        addBackupErrorProcessor(BackupDataResult.BackupError.UNIQUE_ID_EXIST, object : IBackupErrorProcessor<T> {
            override fun processError(errorDataList: List<T>): ProcessResult {
                /* 上次备份可能因为异常或者进程被杀导致备份到云端后没有更新数据库，
                    再次备份会返回[BackupDataResult.BackupError.UNIQUE_ID_EXIST]，此时需要更新数据库，再把本地数据更新到云端避免遗漏变更 */
                return setOperationUpdate(errorDataList)
            }
        })
        addBackupErrorProcessor(BackupDataResult.BackupError.NEED_FETCH, object : IBackupErrorProcessor<T> {
            /**
             * 本错误码无法通过本地修改数据处理，故返回false
             */
            override fun processError(errorDataList: List<T>): ProcessResult {
                return ProcessResult(false, ErrorProcessFailReason.RETRY)
            }

            /**
             * 同步流程是先恢复再备份，备份的时候已经恢复过了，要重新恢复就必须发起一次新的同步
             */
            override fun shouldPostRetry(): Boolean = true
        })
    }

    /**
     * 是否有脏数据
     */
    abstract fun hasDirtyData(): Boolean

    /**
     * 某些备份错误码处理需要把数据库的元数据重置回添加状态（如果元数据有关联文件，则也清除文件id重新上传文件）
     */
    protected abstract fun resetMetadata(metadataList: List<T>): ProcessResult

    /**
     * 某些备份错误码处理需要按照备份成功更新数据库，并且把operation设置为update重新备份到云端避免遗漏变更
     */
    protected abstract fun setOperationUpdate(metadataList: List<T>): ProcessResult

    override fun postMetadataSync(source: Int) {
        metadataSyncHandler.sendSyncMessage(source)
    }
}
