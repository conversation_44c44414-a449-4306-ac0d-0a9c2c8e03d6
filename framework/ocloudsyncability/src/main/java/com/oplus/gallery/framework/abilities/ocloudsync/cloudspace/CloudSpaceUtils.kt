/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CloudSpaceUtils
 ** Description: 云服务空间相关业务工具类
 **
 ** Version: 1.0
 ** Date: 2023/02/02
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wangshengwei@Apps.Gallery3D  2023/02/02  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync.cloudspace

import androidx.appcompat.app.AppCompatActivity
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_CAMERA
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_FROM
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.CloudSync.CloudSpace.AVAILABLE_AMOUNT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.CloudSync.CloudSpace.LAST_SHOW_CLOUD_SPACE_NOT_ENOUGH_TIPS_TIME
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.ocloudsync.ui.CloudPageHelper
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils

object CloudSpaceUtils {
    private const val TAG = "CloudSpaceUtils"
    private const val INVALID_AVAILABLE_AMOUNT = Integer.MIN_VALUE

    /**
     * 欢太云在外销上升级云空间的跳转链接
     */
    const val OCLOUD_UPGRADE_SPACE_URL: String = "https://cloud.oppo.com/upgradespace.html"

    @JvmStatic
    fun gotoCloudSpaceNotEnoughRemind(activity: AppCompatActivity) {
        if (KEY_FROM_CAMERA.equals(IntentUtils.getStringExtra(activity.intent, KEY_MEDIA_FROM), ignoreCase = true)) {
            GLog.d(TAG, "no need show cloud space not enough tips from camera.")
            return
        }
        if (ApiDmManager.getCloudSyncDM().isAlbumSyncSwitchOpen().not()) {
            GLog.d(TAG, "album sync switch not open.")
            return
        }

        if (needShowCloudSpaceNotEnoughTips()) {
            CloudPageHelper.showCloudSpaceNotEnoughDialog(activity)
        }
    }

    /**
     * 是否需要显示云空间不足弹框
     * 显示条件：
     * 1、内销版本
     * 2、云同步开关已打开
     * 3、当前时间 距离 上次弹框时间 大于 提示间隔
     * 4、当前云空间大小 小于 阈值
     */
    @JvmStatic
    private fun needShowCloudSpaceNotEnoughTips(): Boolean {
        val cloudSpaceParamManager = CloudSpaceParamManager()
        val hintInterval = cloudSpaceParamManager.getHintInterval()
        val spaceThreshold = cloudSpaceParamManager.getSpaceThreshold()
        if ((hintInterval == null) || (spaceThreshold == null)) {
            GLog.d(TAG, "request cloud space param is null.")
            return false
        }

        val timeDistance = (System.currentTimeMillis() - ConfigAbilityWrapper
                .getLong(LAST_SHOW_CLOUD_SPACE_NOT_ENOUGH_TIPS_TIME)) / TimeUtils.TIME_1_DAY_IN_MS

        val availableAmount = ConfigAbilityWrapper.getInt(AVAILABLE_AMOUNT, INVALID_AVAILABLE_AMOUNT)
        val isCloudSpaceSizeNotEnough = (availableAmount != INVALID_AVAILABLE_AMOUNT) && (availableAmount < spaceThreshold)

        GLog.d(TAG, "needShowCloudSpaceNotEnoughTips: timeDistance=$timeDistance hintInterval=$hintInterval " +
                "isCloudSpaceSizeNotEnough=$isCloudSpaceSizeNotEnough availableAmount=$availableAmount spaceThreshold=$spaceThreshold")
        val isRegionCN = ConfigAbilityWrapper.getBoolean(IS_REGION_CN, false)
        return isRegionCN && (timeDistance > hintInterval) && isCloudSpaceSizeNotEnough
    }
}