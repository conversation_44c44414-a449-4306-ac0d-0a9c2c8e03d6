/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CloudPageHelper.kt
 * Description:与页面表现相关的代码，如页面跳转、弹框提示，还有偏向页面统一表现的逻辑，如关闭同步开关需要重置其他开关等
 * Version: 1.0
 * Date: 2020/10/9
 * Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Dajian@Apps.Gallery3D      2020/10/9    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync.ui

import android.app.Activity
import android.app.ActivityOptions
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.theme.COUIThemeOverlay
import com.google.gson.JsonObject
import com.heytap.cloudkit.libpay.upgrade.CloudUpgradeDialogBuilder
import com.oplus.gallery.addon.view.FlexibleWindowManagerWrapper
import com.oplus.gallery.addon.view.FlexibleWindowManagerWrapper.KEY_FLEXIBLE_NEWTASK_FULLSCREEN
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_FROM
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.StorageTipsHelper
import com.oplus.gallery.basebiz.permission.helper.PermissionHelper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.api.CloudSyncChannelType
import com.oplus.gallery.business_lib.cloudsync.CloudSyncOpenEntrance
import com.oplus.gallery.business_lib.cloudsync.CloudUpgradeDialogFromType
import com.oplus.gallery.business_lib.cloudsync.SyncResultConstants
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils
import com.oplus.gallery.business_lib.model.data.memories.userprofile.protocol.HttpClientHelper
import com.oplus.gallery.foundation.database.helper.PrivacyPersonalInfoTableHelper
import com.oplus.gallery.foundation.taskscheduling.ext.launch
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.BLANK_PAGE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.CLICK_CANCEL
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.CLICK_DELETE_AND_CLOSE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.CLICK_DOWNLOAD_ORIGINAL_FILE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.CLICK_STILL_CLOSE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.CLOSE_DIALOG_TIPS_DOWNLOAD_ORIGINAL_FILE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.CLOSE_DIALOG_TIPS_REMOVE_COMPRESSED_FILE
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.MAIN_TIP
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.OP_OLD_HIDDEN_COLLECTION
import com.oplus.gallery.foundation.tracing.constant.CloudSyncTrackConstant.Value.SETTING_PAGE
import com.oplus.gallery.foundation.ui.dialog.AppDisabledDialog
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.ui.dialog.ListDialog
import com.oplus.gallery.foundation.ui.dialog.LoadingDialog
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_NAME_WIRELESS_SETTINGS
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.isActivityInvalid
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.version.AppVersionUtils
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.CloudSync.OCloudState.GLOBAL_ENABLED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.CloudSync.OCloudState.GOOGLE_DOWNLOADING_ORIGIN_MANUALLY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.ocloudsync.CloudSyncFileManager
import com.oplus.gallery.framework.abilities.ocloudsync.CloudSyncPermissionHelper
import com.oplus.gallery.framework.abilities.ocloudsync.CloudSyncStatusManager
import com.oplus.gallery.framework.abilities.ocloudsync.R
import com.oplus.gallery.framework.abilities.ocloudsync.StatusType
import com.oplus.gallery.framework.abilities.ocloudsync.account.AccountManager
import com.oplus.gallery.framework.abilities.ocloudsync.account.acverify.AcVerifyHelper
import com.oplus.gallery.framework.abilities.ocloudsync.agent.CloudJumpHelperAgent
import com.oplus.gallery.framework.abilities.ocloudsync.agent.CloudStatusHelperAgent
import com.oplus.gallery.framework.abilities.ocloudsync.cloud.SyncAgentContants
import com.oplus.gallery.framework.abilities.ocloudsync.cloudspace.CloudSpaceUtils
import com.oplus.gallery.framework.abilities.ocloudsync.db.CloudSyncFileUtils
import com.oplus.gallery.framework.abilities.ocloudsync.db.LocalSyncDataVisitor
import com.oplus.gallery.framework.abilities.ocloudsync.statistics.CloudSyncTrackHelper.trackDialogBehavior
import com.oplus.gallery.framework.abilities.ocloudsync.statistics.CloudSyncTrackHelper.trackOpenCloudSync
import com.oplus.gallery.framework.abilities.ocloudsync.sync.GallerySyncManager
import com.oplus.gallery.framework.abilities.ocloudsync.sync.SwitchStateManager
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter.ActivityStarter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.Action.ACTION_ANDROID_NET_WIFI_PICK_WIFI_NETWORK
import com.oplus.gallery.standard_lib.app.AppConstants.Package.PACKAGE_NAME_OCLOUD
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.thread.ThreadPool
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.standard_lib.util.os.ContextGetter.context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStream
import java.lang.ref.WeakReference
import java.net.HttpURLConnection
import java.net.URL
import java.nio.charset.StandardCharsets
import java.util.concurrent.atomic.AtomicInteger
import com.oplus.gallery.basebiz.R as BasebizR
import com.oplus.gallery.framework.datatmp.R as DatatmpR
import com.support.appcompat.R as AppcompatR
import com.support.dialog.R as DialogR

object CloudPageHelper {
    private const val TAG = "CloudPageHelper"

    /**
     * helper default action
     */
    private const val ACTION_OPEN_CLOUD_INSTRUCTION_PAGE = CloudJumpHelperAgent.Action.INSTRUCTION_PAGE

    /**
     * view cloud data default url
     */
    private const val PREFERENCE_CLOUD_HELP_URL = "cloud_help_url"
    private const val PREFERENCE_CLOUD_WEB_URL = "cloud_web_url"
    private const val PREFERENCE_CLOUD_URL_LAST_UPDATE_TIME = "cloud_url_last_update_time"

    private const val CLOUD_DNS_API = "/httpdns/v1/get_ocloud_album_dns"
    private const val DEFAULT_REGION_CN = "CN"
    private const val ACTION_SYNC_DIR = "oplus.intent.action.view.sync_dir"
    private const val SEPARATOR = "_"

    /**
     * 跳转云同步设置页时是否需要自动开启/关闭同步开关
     */
    internal const val JUMP_SETTING_PAGE_KEY_NEED_SWITCH = "needSwitch"
    /**
     * 云服务metadata 是否支持云相册h5
     */
    private const val META_DATA_SUPPORT_CLOUD_ALBUM_H5 = "cloud_data_album"

    /**
     * 是否正在显示dialog
     */
    internal var isDialogShowing: Boolean = false

    @Volatile
    private var cloudHelpURL: String? = null

    @Volatile
    private var cloudWebURL: String? = null

    private var mLoadingDialog: LoadingDialog? = null

    private const val KEY_HAS_AUTH_USE_NET_PERMISSION = "hasAuthUseNetPermission"

    @JvmStatic
    fun gotoWifiSettings() {
        val context = ContextGetter.context
        val intent = Intent()
        intent.setPackage(PACKAGE_NAME_WIRELESS_SETTINGS)
        intent.action = ACTION_ANDROID_NET_WIFI_PICK_WIFI_NETWORK
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        intent.putExtra(IntentUtils.NAVIGATE_UP_PACKAGE, context.packageName)
        intent.putExtra(
            IntentUtils.NAVIGATE_UP_TITLE_TEXT,
            context.getString(R.string.cloudsync_app_name)
        )
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            GLog.e(TAG, "gotoWifiSettings, failed!", it)
        }
    }

    /**
     * 跳转云服务设置页
     * @param newTask 以new_task方式跳转activity。用于外部三方应用跳转
     */
    @JvmStatic
    fun gotoCloudSyncSettings(context: Context, bundle: Bundle? = null, newTask: Boolean = false) {
        if (SwitchStateManager.isSupportAppSettingSwitch()) {
            GLog.d(TAG, "gotoCloudSyncSettings, jumpCloudSettings")
            CloudJumpHelperAgent.jumpCloudSettings(context, bundle, newTask)
        } else {
            if ((newTask) || ((context is Activity).not())) {
                // 外部跳转设置页，暂不支持jumpCloudSettingsOld
                GLog.w(TAG, "gotoCloudSyncSettings newTask=$newTask, not support jumpCloudSettingsOld.")
                return
            }
            val activity = context as Activity
            /* 开关在云服务的情况下跳老云服务页面，同步开关开且未同意隐私，相册内显示关，云同步设置页显示开，
            检查隐私权限并提醒用户是为了减少两边开关不一致的情况 */
            CloudSyncPermissionHelper.checkAndTipNetStatus(activity = activity, checkNetworkConnected = false) {
                // 云服务可用
                if (CloudSyncStatusManager.getInstance().isCloudServiceEnabled) {
                    runCatching {
                        GLog.d(TAG, "gotoCloudSyncSettings, jumpCloudSettingsOld")
                        CloudJumpHelperAgent.jumpCloudSettingsOld(activity)
                    }.onFailure {
                        GLog.e(TAG, "gotoCloudSyncSettings, failed!", it)
                    }
                } else {
                    AppScope.launch(Dispatchers.UI) {
                        GLog.d(TAG, "gotoCloudSyncSettings, jumpCloudSettings show AppDisabledDialog")
                        AppDisabledDialog.showDialog(activity, CloudSyncStatusManager.getInstance().cloudPackageName)
                    }
                }
            }
        }
    }

    /**
     * 跳转同步图集开关页
     */
    fun gotoSyncAlbumDirPage(activity: Activity) {
        runCatching {
            CloudSyncPermissionHelper.checkAndTipNetStatus(activity) {
                val intent = Intent(ACTION_SYNC_DIR)
                intent.setPackage(activity.applicationContext.packageName)
                activity.startActivity(intent)
            }
        }.onFailure {
            GLog.e(TAG, "gotoSyncAlbumDirPage, failed! error=", it)
        }
    }

    /**
     * 响应界面点击云相册同步开关的按钮，开启或关闭云相册同步功能，如果云服务不支持appSettings，则跳云相册设置页
     */
    @JvmStatic
    fun setAlbumSyncSwitch(
        activity: Activity,
        open: Boolean,
        entrance: CloudSyncOpenEntrance,
        showCloseConfirmDialog: Boolean = true,
        stopLoadingCallback: (() -> Unit)? = null,
        isSuccessCallback: (() -> Unit)? = null
    ) {
        GLog.d(TAG) { "setAlbumSyncSwitch, isOpen=$open, entrance=$entrance, showCloseConfirmDialog=$showCloseConfirmDialog" }
        // 关闭开关的时候无需检查网络状态 直接关闭
        CloudSyncPermissionHelper.checkAndTipNetStatus(activity, checkNetworkConnected = open, {
            AppScope.launch(Dispatchers.Main) {
                stopLoadingCallback?.invoke()
            }
        }) {
            if (SwitchStateManager.isSupportAppSettingSwitch()) {
                CloudSyncPermissionHelper.checkFunctionEnable(activity) {
                    if (activity !is ComponentActivity) {
                        GLog.e(TAG, "setAlbumSyncSwitch, activity is not ComponentActivity")
                        return@checkFunctionEnable
                    }
                    val activityRef: WeakReference<Activity> = WeakReference(activity)
                    activity.lifecycleScope.launch(Dispatchers.IO) {
                        val isLogin = AccountManager.isLogin
                        withContext(Dispatchers.UI) {
                            setAppSettingAlbumSyncSwitch(
                                activityRef,
                                isLogin,
                                open,
                                entrance,
                                showCloseConfirmDialog,
                                isSuccessCallback,
                                stopLoadingCallback
                            )
                        }
                    }
                }
            } else {
                if (!open) {
                    GLog.e(TAG, "setAlbumSyncSwitch, old cloud app not support shutdown")
                    return@checkAndTipNetStatus
                }

                if (CloudSyncStatusManager.getInstance().isAlbumSyncEnable(true, true)) {
                    GLog.d(TAG, "setAlbumSyncSwitch, isAlbumSyncEnable")
                    return@checkAndTipNetStatus
                }

                if (CloudSyncStatusManager.getInstance().isCloudServiceEnabled) {
                    if (CloudStatusHelperAgent.querySupportModuleSwitchGuide(SyncAgentContants.DataType.GALLERY)) {
                        CloudStatusHelperAgent.openCloudModuleSwitch(activity, SyncAgentContants.DataType.GALLERY)
                    } else {
                        gotoCloudSyncSettings(activity)
                    }
                    val syncType: String =
                        when (entrance) {
                            CloudSyncOpenEntrance.MAIN_TIP -> MAIN_TIP
                            CloudSyncOpenEntrance.EMPTY -> BLANK_PAGE
                            CloudSyncOpenEntrance.SETTING -> SETTING_PAGE
                            CloudSyncOpenEntrance.OP_OLD_HIDDEN_COLLECTION -> OP_OLD_HIDDEN_COLLECTION
                            else -> TextUtil.EMPTY_STRING
                        }
                    trackOpenCloudSync(syncType)
                } else {
                    AppScope.launch(Dispatchers.UI) {
                        AppDisabledDialog.showDialog(activity, CloudSyncStatusManager.getInstance().cloudPackageName)
                    }
                }
            }
        }
    }

    private fun setAppSettingAlbumSyncSwitch(
        activityRef: WeakReference<Activity>,
        isLogin: Boolean,
        open: Boolean,
        entrance: CloudSyncOpenEntrance,
        showCloseConfirmDialog: Boolean,
        isSuccessCallback: (() -> Unit)? = null,
        stopLoadingCallback: (() -> Unit)? = null
    ) {
        val activity = activityRef.get() ?: let {
            GLog.w(TAG, LogFlag.DL, "setAlbumSyncSwitch, activity is null")
            return
        }
        if (activity.isActivityInvalid()) {
            GLog.w(TAG, LogFlag.DL, "setAlbumSyncSwitch, activity is invalid")
            return
        }

        if (isLogin) {
            AcVerifyHelper.acVerifyIfNeed(activity, open) {
                activityRef.get()?.let { activity ->
                    setAlbumSyncSwitchAfterAcVerify(
                        open,
                        activity,
                        entrance,
                        showCloseConfirmDialog,
                        isSuccessCallback,
                        stopLoadingCallback
                    ) {
                        AppScope.launch(Dispatchers.Main) {
                            closeLoadingDialog()
                        }
                    }
                }
            }
        } else {
            AccountManager.login {
                if (it?.isLogin == true) {
                    activityRef.get()?.let { activity ->
                        AcVerifyHelper.acVerifyIfNeed(activity, open) {
                            activityRef.get()?.let { activity ->
                                setAlbumSyncSwitchAfterAcVerify(open,
                                    activity,
                                    entrance,
                                    showCloseConfirmDialog,
                                    isSuccessCallback,
                                    stopLoadingCallback) {
                                    AppScope.launch(Dispatchers.Main) {
                                        closeLoadingDialog()
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @JvmStatic
    private fun setAlbumSyncSwitchAfterAcVerify(
        open: Boolean,
        activity: Activity,
        entrance: CloudSyncOpenEntrance,
        showCloseConfirmDialog: Boolean,
        isSuccessCallback: (() -> Unit)? = null,
        closeDialogCallback: (() -> Unit)? = null,
        stopLoadingCallback: (() -> Unit)? = null
    ) {
        if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not()) {
            when (entrance) {
                CloudSyncOpenEntrance.MAIN_TIP -> {
                    showLoadingDialog(activity, R.string.cloudsync_open_loading_tips)
                    openCloudSyncRequestCallBack(closeDialogCallback, activity, open, showCloseConfirmDialog, isSuccessCallback, stopLoadingCallback)
                }
                CloudSyncOpenEntrance.SETTING -> {
                    openCloudSyncRequestCallBack(closeDialogCallback, activity, open, showCloseConfirmDialog, isSuccessCallback, stopLoadingCallback)
                }
                else -> setAppSettingAlbumSyncSwitch(activity, open, showCloseConfirmDialog, isSuccessCallback)
            }
        } else {
            setAppSettingAlbumSyncSwitch(activity, open, showCloseConfirmDialog, isSuccessCallback)
        }
    }

    private fun openCloudSyncRequestCallBack(
        closeDialogCallback: (() -> Unit)? = null,
        activity: Activity,
        open: Boolean,
        showCloseConfirmDialog: Boolean,
        isSuccessCallback: (() -> Unit)? = null,
        stopLoadingCallback: (() -> Unit)? = null
    ) {
        CloudSyncStatusManager.getInstance()
            .openCloudSyncRequest(closeDialogCallback, {
                stopLoadingCallback?.invoke()
                AppScope.launch(Dispatchers.Main) {
                    activity.let {
                        ConfirmDialog.Builder(it)
                            .setNegativeButton(activity.getString(R.string.cloudsync_dialog_button_exit), null)
                            .setCancelable(false)
                            .setTitle(activity.getString(R.string.cloudsync_dialog_not_support_tips))
                            .build()
                            .show()
                    }
                }
            }, {
                AppScope.launch(Dispatchers.Main) {
                    stopLoadingCallback?.invoke()
                    ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.common_network_disconnected)
                }
            }, {
                stopLoadingCallback?.invoke()
                setAppSettingAlbumSyncSwitch(activity, open, showCloseConfirmDialog, isSuccessCallback)
            })
    }

    @JvmStatic
    fun setAppSettingAlbumSyncSwitch(
        activity: Activity,
        open: Boolean,
        showCloseConfirmDialog: Boolean,
        isSuccessCallback: (() -> Unit)? = null
    ) {
        if (DoubleClickUtils.isFastDoubleClick()) return
        if (open) {
            if (CloudSyncStatusManager.getInstance().isAlbumSyncEnable(true, true)) {
                return
            }
            openSyncSwitch(isSuccessCallback)
        } else {
            AppScope.launch(Dispatchers.IO) {
                val size = LocalSyncDataVisitor.getOriginFilesSizeNeedToDownload().second
                GLog.d(TAG, "setAppSettingAlbumSyncSwitch, size is $size")
                withContext(Dispatchers.UI) {
                    if (size > 0) {
                        //关闭总开关前 如果需要下载原图 且优化存储空间开关打开就弹三选一弹窗，否则弹缩图删除提示弹窗
                        if (SwitchStateManager.slimmingStatus == StatusType.SwitchValue.CLOSED) {
                            showDeleteCompressedFileDialog(activity)
                        } else {
                            showDownloadOriginDialog(activity, size)
                        }
                    } else {
                        if (showCloseConfirmDialog) {
                            showSyncCloseDialog(activity)
                        } else {
                            resetSwitch()
                        }
                    }
                }
            }
        }
    }

    /**
     * 提示删除大缩图的dialog
     *
     * 这是个二选一弹窗，提示 关闭相册自动同步
     * 按钮1：仍要关闭  直接关闭云同步
     * 按钮2：取消     取消操作
     */
    @JvmStatic
    private fun showDeleteCompressedFileDialog(activity: Activity) {
        ConfirmDialog.Builder(activity)
            .setNeutralButton(BasebizR.string.cloud_setting_still_close) { _: DialogInterface?, _: Int ->
                resetSwitch()
                setIsHeyTapDownloadingOriginManually(false)
                trackDialogBehavior(CLOSE_DIALOG_TIPS_REMOVE_COMPRESSED_FILE, CLICK_STILL_CLOSE)
            }
            .setNegativeButton(BasebizR.string.common_cancel) { _: DialogInterface?, _: Int ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_REMOVE_COMPRESSED_FILE, CLICK_CANCEL)
            }
            .setMessage(R.string.cloud_setting_downloading_original_files_tips)
            .setTitle(R.string.cloud_setting_auto_sync_close_dialog_title)
            .setOnDismissListener { isDialogShowing = false }
            .setCancelable(false)
            .build()
            .show()
        isDialogShowing = true
    }

    @JvmStatic
    private fun showSyncCloseDialog(activity: Activity) {
        ConfirmDialog.Builder(activity)
            .setPositiveButton(BasebizR.string.common_close) { _: DialogInterface?, _: Int ->
                resetSwitch()
            }
            .setNegativeButton(BasebizR.string.common_cancel, null)
            .setMessage(R.string.cloud_setting_auto_sync_close_dialog_msg)
            .setCancelable(false)
            .setTitle(R.string.cloud_setting_auto_sync_close_dialog_title)
            .build()
            .show()
    }

    /**
     * 这是一个三选一弹窗 提示 关闭相册同步开关
     *
     * 按钮1：下载原始照片（5.1GB）  点击触发原图下载
     * 按钮2：移除并关闭            点击关闭云同步开关并移除本地缩图
     * 按钮3：取消                 点击取消操作
     *
     */
    @JvmStatic
    private fun showDownloadOriginDialog(activity: Activity, size: Long) {
        val listDialog = ListDialog.Builder(activity).apply {
            setTitle(R.string.cloud_sync_setting_close_sync_title)
            setMessage(R.string.cloud_sync_setting_close_sync_tip_2)
            setNeutralButton(
                activity.getString(
                    BasebizR.string.cloud_setting_download_original_files,
                    FilePathUtils.getUnitValue(activity, size)
                )
            ) { _, _ ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_DOWNLOAD_ORIGINAL_FILE, CLICK_DOWNLOAD_ORIGINAL_FILE)
                checkEnoughDiskSpaceToDownload(activity, size) {
                    AppScope.launch(Dispatchers.UI) {
                        ToastUtil.showShortToast(R.string.cloudsync_downlaod_origin_wlan_toast)
                    }

                    SwitchStateManager.setOptimizeSpace(false)
                    CloudSyncFileManager.getInstance(activity.applicationContext).backupOrRecovery()
                    setIsHeyTapDownloadingOriginManually(true)
                }
            }
            setPositiveButton(DatatmpR.string.cloud_setting_remove_and_close_button) { _, _ ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_DOWNLOAD_ORIGINAL_FILE, CLICK_DELETE_AND_CLOSE)
                resetSwitch()
            }
            setNegativeButton(BasebizR.string.base_cancel) { _, _ ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_DOWNLOAD_ORIGINAL_FILE, CLICK_CANCEL)
            }
            setWindowAnimStyle(DialogR.style.Animation_COUI_Dialog_Alpha)
            setCancelable(false)
            setOnDismissListener { isDialogShowing = false }
        }.build().show()
        isDialogShowing = true
        listDialog.apply {
            setButtonTextColor(DialogInterface.BUTTON_NEUTRAL, COUIContextUtil.getAttrColor(activity, AppcompatR.attr.couiColorPrimaryText))
            setButtonTextColor(DialogInterface.BUTTON_POSITIVE, COUIContextUtil.getAttrColor(activity, AppcompatR.attr.couiColorError))
        }
    }

    @JvmStatic
    private fun checkEnoughDiskSpaceToDownload(activity: Activity, needDownloadSize: Long, onEnough: () -> Unit) {
        AppScope.launch(Dispatchers.IO) {
            val isSpaceAvailable = StorageLimitHelper.hasEnoughStorageSpace(OplusEnvironment.StorageType.PHONE_STORAGE, needDownloadSize)
            if (isSpaceAvailable.not()) {
                GLog.d(TAG, "checkEnoughDiskSpaceToDownload, the disk space is not enough")
                withContext(Dispatchers.UI) {
                    if (activity.isActivityInvalid()) {
                        GLog.d(TAG, "checkEnoughDiskSpaceToDownload, activity is invalid")
                        return@withContext
                    }
                    ConfirmDialog.Builder(activity)
                        .setPositiveButton(BasebizR.string.cloudsync_sync_state_clean) { _: DialogInterface?, _: Int ->
                            StorageTipsHelper.startCleanUpActivity(activity)
                        }
                        .setNegativeButton(BasebizR.string.common_cancel, null)
                        .setMessage(BasebizR.string.cloud_setting_no_space_for_download_tips)
                        .setCancelable(false)
                        .setTitle(BasebizR.string.cloud_setting_no_space_for_download_button)
                        .build()
                        .show()
                }
            } else {
                onEnough.invoke()
            }
        }
    }

    @JvmStatic
    private fun resetSwitch() {
        SwitchStateManager.setAlbumSync(false)
    }

    @JvmStatic
    private fun openSyncSwitch(isSuccessCallback: (() -> Unit)? = null) {
        SwitchStateManager.setAlbumSync(true) {
            isSuccessCallback?.invoke()
        }
    }

    @JvmStatic
    fun gotoCloudDataPage(activity: Activity) {
        if (!CloudSyncStatusManager.getInstance().isCloudServiceEnabled) {
            AppDisabledDialog.showDialog(activity, CloudSyncStatusManager.getInstance().cloudPackageName)
            return
        }
        val supportCloudAlbumH5 = isSupportCloudAlbumH5()
        if (supportCloudAlbumH5) {
            openWeb(activity, getCloudAlbumH5Url())
            return
        }

        val url = getCloudWebUrl(activity)
        if (url.isNullOrEmpty()) {
            return
        }
        if (!openWeb(activity, url)) {
            openActivity(activity, url)
        }
    }

    fun gotoManagerCloudPage(activity: Activity) {
        val securityUrl = ApiDmManager.getAppDM().getSecurityUrl()
        val jumpUrl = securityUrl?.oCloudSpaceManagerUrl ?: ""
        openWeb(activity, jumpUrl)
    }

    @JvmStatic
    private fun openActivity(activity: Activity, action: String?): Boolean {
        runCatching {
            val intent = Intent(action)
            activity.startActivity(intent)
            return true
        }.onFailure {
            GLog.e(TAG, "openActivity", it)
        }
        return false
    }

    @JvmStatic
    private fun openWeb(activity: Activity, uriString: String?): Boolean {
        if (!uriString.isNullOrEmpty()) {
            val uri = Uri.parse(uriString)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            runCatching {
                activity.startActivity(intent)
                return true
            }.onFailure {
                GLog.e(TAG, "openWeb", it)
            }
        }
        return false
    }

    @JvmStatic
    private fun getCloudDns(): String {
        val securityUrl = ApiDmManager.getAppDM().getSecurityUrl()
        return if (securityUrl != null) {
            securityUrl.cloudDns + CLOUD_DNS_API
        } else TextUtil.EMPTY_STRING
    }

    @JvmStatic
    private fun getCloudHostName(): String? {
        val securityUrl = ApiDmManager.getAppDM().getSecurityUrl()
        return if (securityUrl != null) {
            securityUrl.cloudHostName
        } else TextUtil.EMPTY_STRING
    }

    @JvmStatic
    fun refreshCloudUrl(context: Context) {
        if (PermissionHelper.isPermissionUnavailable()) {
            GLog.d(TAG, "refreshCloudUrl, permission unavailable.")
            return
        }
        if (!CloudSyncStatusManager.getInstance().isSupportCloudSync) {
            GLog.d(TAG, "refreshCloudUrl, cloudSync unavailable.")
            return
        }
        val lastTime = ComponentPrefUtils.getLongPref(
            context,
            PREFERENCE_CLOUD_URL_LAST_UPDATE_TIME,
            0
        )
        val time = System.currentTimeMillis()
        if (time - lastTime < TimeUtils.TIME_1_DAY_IN_MS) {
            return
        }
        AppScope.launch(Dispatchers.IO, block = CloudUrlRefreshTask(context))
    }

    @JvmStatic
    fun setCloudHelpUrl(context: Context?, url: String?) {
        if (!TextUtils.isEmpty(url)) {
            cloudHelpURL = url
        } else {
            GLog.w(TAG, "setCloudHelpUrl, url = null")
        }
        ComponentPrefUtils.setStringPref(context, PREFERENCE_CLOUD_HELP_URL, url)
    }

    @JvmStatic
    fun getCloudWebUrl(context: Context?): String? {
        if (cloudWebURL == null) { // Use null here as a condition. If null, use default url.
            cloudWebURL = ComponentPrefUtils.getStringPref(
                context,
                PREFERENCE_CLOUD_WEB_URL,
                getCloudHostName()
            )
        }
        return cloudWebURL
    }

    @JvmStatic
    private fun setCloudWebUrl(context: Context?, url: String?) {
        if (!TextUtils.isEmpty(url)) {
            cloudWebURL = url
        } else {
            GLog.w(TAG, "setCloudWebUrl, cloud url = null")
        }
        ComponentPrefUtils.setStringPref(context, PREFERENCE_CLOUD_WEB_URL, url)
    }

    /**
     * Cloud url refresh task
     */
    private class CloudUrlRefreshTask(context: Context) :
        ThreadPool.Job<Void?> {
        private val refContext: WeakReference<Context> = WeakReference(context)
        override fun run(jc: ThreadPool.JobContext): Void? {
            // Ensure only single task is running at same time
            synchronized(sSingleInstanceLock) {
                val lock = sSingleInstanceLock.getAndIncrement()
                if (lock > 0) {
                    sSingleInstanceLock.getAndDecrement()
                    GLog.d(TAG, "refreshCloudUrl, already has CloudUrlRefreshTask running, task rejected")
                    return null
                }
            }
            return try {
                val context = refContext.get() ?: return null
                if (jc.isCancelled) {
                    return null
                }
                GLog.d(TAG, "refreshCloudUrl, update url.")
                val responseContent: String?
                try {
                    responseContent = getCloudAlbumDns(context)
                    if (TextUtils.isEmpty(responseContent)) {
                        GLog.e(TAG, "refreshCloudUrl, responseContent is empty.")
                        return null
                    }
                    parseContent(responseContent!!)
                } catch (e: java.lang.Exception) {
                    GLog.e(TAG, "refreshCloudUrl, Exception: ", e)
                }
                ComponentPrefUtils.setLongPref(
                    context,
                    PREFERENCE_CLOUD_URL_LAST_UPDATE_TIME,
                    System.currentTimeMillis()
                )
                GLog.d(TAG, "refreshCloudUrl, end.")
                null
            } finally {
                synchronized(sSingleInstanceLock) { sSingleInstanceLock.getAndDecrement() }
            }
        }

        companion object {
            private val sSingleInstanceLock = AtomicInteger(0)
        }
    }

    @JvmStatic
    private fun parseContent(content: String) {
        val jo = JSONObject(content)
        val joData = jo["data"] as JSONObject
        // udpate helper url
        val ocloudExplainShow = joData.getBoolean("ocloudExplainShow")
        if (ocloudExplainShow) {
            val ocloudExplainDns = joData.getString("ocloudExplainDns")
            if (TextUtils.isEmpty(ocloudExplainDns) || ocloudExplainDns.equals(
                    "null",
                    ignoreCase = true
                )
            ) {
                setCloudHelpUrl(
                    context,
                    ACTION_OPEN_CLOUD_INSTRUCTION_PAGE
                )
            } else {
                setCloudHelpUrl(context, ocloudExplainDns)
            }
        } else {
            setCloudHelpUrl(context, "")
        }
        // update view cloud data url
        val albumPcShow = joData.getBoolean("albumPcShow")
        if (albumPcShow) {
            val albumPcDNS = joData.getString("albumPcDNS")
            if (TextUtils.isEmpty(albumPcDNS) || albumPcDNS.equals(
                    "null",
                    ignoreCase = true
                )
            ) {
                setCloudWebUrl(context, getCloudHostName())
            } else {
                setCloudWebUrl(context, albumPcDNS)
            }
        } else {
            setCloudWebUrl(context, "")
        }
    }

    @JvmStatic
    fun getCloudAlbumDns(context: Context): String? {
        val json = getCloudAlbumDnsHeader(context)
        val result = StringBuilder()
        var bufferedReader: BufferedReader? = null
        var outPutStream: OutputStream? = null
        var connection: HttpURLConnection? = null
        try {
            val url = URL(getCloudDns())
            connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.doOutput = true
            connection.doInput = true
            connection.useCaches = false
            connection.setRequestProperty("Connection", "Keep-Alive")
            connection.setRequestProperty("Charset", "UTF-8")
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8")
            connection.setRequestProperty("accept", "application/json")
            connection.connectTimeout = HttpClientHelper.DEFAULT_HTTP_CONNECT_TIMEOUT
            connection.readTimeout = HttpClientHelper.DEFAULT_HTTP_READ_TIMEOUT
            if (!TextUtils.isEmpty(json)) {
                val writebytes = json.toByteArray(StandardCharsets.UTF_8)
                connection.setRequestProperty("Content-Length", writebytes.size.toString())
                outPutStream = connection.outputStream
                outPutStream.write(json.toByteArray(StandardCharsets.UTF_8))
                outPutStream.flush()
                outPutStream = null
            }
            connection.connect()
            val responseCode = connection.responseCode
            GLog.d(TAG) { "getCloudAlbumDns, responseCode=$responseCode" }
            if (responseCode == HttpURLConnection.HTTP_OK) {
                bufferedReader = BufferedReader(
                    InputStreamReader(
                        connection.inputStream,
                        StandardCharsets.UTF_8
                    )
                )
                var line: String? = null
                while (bufferedReader.readLine().also { line = it } != null) {
                    result.append(line)
                }
            }
        } catch (e: java.lang.Exception) {
            GLog.e(TAG, "getCloudAlbumDns", e)
        } finally {
            IOUtils.closeQuietly(bufferedReader)
            IOUtils.closeQuietly(outPutStream)
            connection?.disconnect()
        }
        return result.toString()
    }

    @JvmStatic
    private fun getCloudAlbumDnsHeader(context: Context): String {
        val jsonObject = JsonObject()
        jsonObject.addProperty(
            "ocloudVersion",
            AppVersionUtils.getVersionName(
                context,
                CloudSyncStatusManager.getInstance().cloudPackageName
            )
        )
        jsonObject.addProperty("oplusOsVersion", FeatureUtils.osVersion)
        jsonObject.addProperty(
            "albumVersion",
            AppVersionUtils.getVersionName(context, context.packageName)
        )
        jsonObject.addProperty("deviceModel", FeatureUtils.phoneModel)
        jsonObject.addProperty("regionCode", getOplusRegionMark())

        PrivacyPersonalInfoTableHelper.insertCloudAlbumDnsHeaderData()
        val json = jsonObject.toString()
        return json
    }

    @JvmStatic
    private fun getOplusRegionMark(): String? {
        if (FeatureUtils.isRegionCN) {
            return DEFAULT_REGION_CN
        }
        var region: String? = FeatureUtils.regionMark
        if (TextUtils.isEmpty(region)) {
            region = FeatureUtils.afterSaleRegion
        }
        if ("OC".equals(region, ignoreCase = true)) {
            region = DEFAULT_REGION_CN
        }
        return region
    }

    @JvmStatic
    fun showCloudSpaceNotEnoughDialog(activity: AppCompatActivity, uploadingCount: Int) {
        GLog.d(TAG, "showCloudSpaceNotEnoughDialog")
        // lifecycleScope 是Dispatchers.Main运行在主线程
        activity.lifecycleScope.launch {
            ConfirmDialog.Builder(activity)
                .setPositiveButton(R.string.cloudspace_dialog_tip_no_space_positive) { _: DialogInterface?, _: Int ->
                    showStorageUpgradeDialog(activity, CloudUpgradeDialogFromType.FROM_DIALOG, uploadingCount = uploadingCount)
                }
                .setNegativeButton(R.string.cloudspace_dialog_tip_no_space_negative, null)
                .setMessage(
                    activity.getString(
                        R.string.cloudspace_dialog_tip_no_space_content_new,
                        uploadingCount
                    )
                )
                .setCancelable(false)
                .setTitle(R.string.cloudspace_dialog_tip_no_space_title_new)
                .build()
                .show()
            context.getAppAbility<ISettingsAbility>()?.use { settingAbility ->
                settingAbility.setLastShowCloudSpaceNotEnoughTipsTime(System.currentTimeMillis())
            }
        }
    }

    /**
     * 弹出欢太云空间升级弹窗
     * @param activity: 弹出弹窗的Activity对象
     * @param fromType: CloudUpgradeDialogFromType 埋点相关，是什么场景来的
     * @param trackId: 埋点相关
     * @param onSuccess: 升级成功后回调
     */
    @JvmStatic
    fun showStorageUpgradeDialog(
        activity: FragmentActivity,
        fromType: CloudUpgradeDialogFromType,
        trackId: String? = null,
        uploadingCount: Int? = null,
        onSuccess: (() -> Unit)? = null
    ) {
        if (DoubleClickUtils.isFastDoubleClick(TimeUtils.TIME_500_MS_IN_MS)) {
            return
        }
        if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not()) {
            openWeb(activity, CloudSpaceUtils.OCLOUD_UPGRADE_SPACE_URL)
            return
        }
        val fromTrack = trackId?.let {
            fromType.from + SEPARATOR + it
        } ?: fromType.from
        GLog.d(TAG, LogFlag.DL, "showStorageUpgradeDialog fromTrack：$fromTrack")
        // lifecycleScope 是Dispatchers.Main运行在主线程
        activity.lifecycleScope.launch {
            var headTips: String? = null
            if (fromType.showTips) {
                val uploadCount = withContext(Dispatchers.IO) {
                    val uploadResult = GallerySyncManager.instance.getUploadResult()
                    // 获取上传状态，如果为云空间不足，再获取数量，只有云空间不足时才需要再升级弹窗中提示上传个数
                    return@withContext if (uploadResult == SyncResultConstants.RESULT_INSUFFICIENT_SPACE) {
                        var count = uploadingCount
                        if (count == null) {
                            val info = CloudSyncFileUtils.getUploadInfo(activity)
                            count = info.mUploadingImageCnt + info.mUploadingVideoCnt
                        }
                        count
                    } else 0
                }

                if (uploadCount > 0) {
                    headTips = activity.getString(
                        R.string.cloudsync_space_not_enough_with_upload_file_count_text,
                        LocaleUtils.getLocaleFormattedNumber(uploadCount)
                    )
                }
                GLog.d(TAG, LogFlag.DL, "showStorageUpgradeDialog uploadCount：$uploadCount")
            }
            CloudUpgradeDialogBuilder()
                .setThemeColorAttr(com.oplus.gallery.foundation.ui.R.attr.gColorPrimary)
                .from(fromTrack)
                .setHeadTips(headTips)
                .setRecommend(fromType.isRecommend)
                .setCallback {
                    GLog.d(TAG) { "showStorageUpgradeDialog result:$it" }
                    if (it) {
                        onSuccess?.invoke()
                        GallerySyncManager.instance.triggerSync()
                    }
                }
                .build(activity)
                .show(activity.supportFragmentManager, null)
        }
    }

    /**
     *  "Google相册备份"切欢太云同步
     */
    fun gCloudSwitchToOCloudSync(
        activity: Activity
    ) {
        if (DoubleClickUtils.isFastDoubleClick()) return
        AppScope.launch(Dispatchers.IO) {
            val size = LocalSyncDataVisitor.getOriginFilesSizeNeedToDownload().second
            GLog.d(TAG, LogFlag.DL, "setAppSettingAlbumSyncSwitch, size is $size")
            withContext(Dispatchers.UI) {
                ApiDmManager.getCloudSyncDM().getCloudSyncChannel(CloudSyncChannelType.GOOGLE_CHANNEL).apply {
                    if (size > 0) {
                        if (ConfigAbilityWrapper.getBoolean(GOOGLE_DOWNLOADING_ORIGIN_MANUALLY, false)) {
                            showDeleteFileDialog(activity)
                        } else {
                            showDownloadOriginDialog(activity, size)
                        }
                    } else {
                        showTurnOffWhenSyncingDialog(activity)
                    }
                }
            }
        }
    }

    /**
     * 提示删除大缩图的dialog
     *
     * 这是个二选一弹窗，提示 关闭相册自动同步
     * 按钮1：仍要关闭  直接关闭云同步
     * 按钮2：取消     取消操作
     */
    @JvmStatic
    fun showHeytapDeleteCompressedFileDialog(activity: Activity) {
        ConfirmDialog.Builder(activity)
            .setNeutralButton(BasebizR.string.cloud_setting_still_close) { _: DialogInterface?, _: Int ->
                resetSwitchAndShowTurnOffToast(activity)
                trackDialogBehavior(CLOSE_DIALOG_TIPS_REMOVE_COMPRESSED_FILE, CLICK_STILL_CLOSE)
            }
            .setNegativeButton(com.oplus.gallery.basebiz.R.string.common_cancel) { _: DialogInterface?, _: Int ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_REMOVE_COMPRESSED_FILE, CLICK_CANCEL)
            }
            .setMessage(
                if (ConfigAbilityWrapper.getBoolean(GLOBAL_ENABLED, false) && ConfigAbilityWrapper.getBoolean(IS_REGION_CN, false).not()) {
                    R.string.heytap_cloud_sync_turn_off_tips4
                } else {
                    R.string.heytap_cloud_sync_turn_off_tips2
                }
            )
            .setTitle(R.string.heytap_cloud_sync_turn_off)
            .setOnDismissListener { isDialogShowing = false }
            .setCancelable(false)
            .build()
            .show()
        isDialogShowing = true
    }

    /**
     * 这是一个三选一弹窗 提示 关闭相册同步开关
     *
     * 按钮1：下载原始照片（5.1GB）  点击触发原图下载
     * 按钮2：移除并关闭            点击关闭云同步开关并移除本地缩图
     * 按钮3：取消                 点击取消操作
     *
     */
    @JvmStatic
    fun showHeyTapDownloadOriginDialog(activity: Activity, size: Long) {
        val listDialog = ListDialog.Builder(activity).apply {
            setTitle(R.string.cloud_sync_setting_close_sync_title)
            setMessage(R.string.cloud_sync_setting_close_sync_tip_2)
            setNeutralButton(
                activity.getString(
                    BasebizR.string.cloud_setting_download_original_files,
                    FilePathUtils.getUnitValue(activity, size)
                )
            ) { _, _ ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_DOWNLOAD_ORIGINAL_FILE, CLICK_DOWNLOAD_ORIGINAL_FILE)
                checkEnoughDiskSpaceToDownload(activity, size) {
                    AppScope.launch(Dispatchers.UI) {
                        ToastUtil.showShortToast(R.string.heytap_cloud_sync_turn_off_download_origin_toast)
                    }
                    ApiDmManager.getCloudSyncDM().getCloudSyncChannel(CloudSyncChannelType.OPLUS_CHANNEL).setOptimizeSpace(false)
                    CloudSyncFileManager.getInstance(activity.applicationContext).backupOrRecovery()
                    setIsHeyTapDownloadingOriginManually(true)
                }
            }
            setPositiveButton(com.oplus.gallery.framework.datatmp.R.string.cloud_setting_remove_and_close_button) { _, _ ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_DOWNLOAD_ORIGINAL_FILE, CLICK_DELETE_AND_CLOSE)
                resetSwitchAndShowTurnOffToast(activity)
            }
            setNegativeButton(com.oplus.gallery.basebiz.R.string.base_cancel) { _, _ ->
                trackDialogBehavior(CLOSE_DIALOG_TIPS_DOWNLOAD_ORIGINAL_FILE, CLICK_CANCEL)
            }
            setWindowAnimStyle(com.support.dialog.R.style.Animation_COUI_Dialog_Alpha)
            setCancelable(false)
            setOnDismissListener { isDialogShowing = false }
        }.build().show()
        isDialogShowing = true
        listDialog.apply {
            setButtonTextColor(
                DialogInterface.BUTTON_NEUTRAL,
                COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorPrimaryText)
            )
            setButtonTextColor(DialogInterface.BUTTON_POSITIVE, COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorError))
        }
    }

    private fun setIsHeyTapDownloadingOriginManually(isDownLoadOrigin: Boolean) {
        context.getAppAbility<ISettingsAbility>()?.use { settingAbility ->
            settingAbility.setHeyTapDownloadingOriginManually(isDownLoadOrigin)
        }
    }

    @JvmStatic
    fun showHeyTapSyncCloseDialog(activity: Activity) {
        ConfirmDialog.Builder(activity)
            .setPositiveButton(R.string.heytap_cloud_sync_close_now) { _: DialogInterface?, _: Int ->
                resetSwitchAndShowTurnOffToast(activity)
            }
            .setNegativeButton(com.oplus.gallery.basebiz.R.string.common_cancel, null)
            .setMessage(R.string.heytap_cloud_sync_turn_off_tips)
            .setCancelable(false)
            .setTitle(R.string.heytap_cloud_sync_turn_off)
            .build()
            .show()
    }

    /**
     * 欢太云同步切谷歌备份,欢太云正在同步中,提示dialog
     *
     * 这是个二选一弹窗，提示 关闭相册自动同步
     * 按钮1：仍要关闭  直接关闭云同步
     * 按钮2：取消     取消操作
     */
    @JvmStatic
    fun showHeyTapCloudSyncTurnOffWhenSyncingDialog(activity: Activity) {
        val count: Int = CloudSyncFileUtils.getUploadInfo(activity).mUploadingImageCnt
        val size = LocalSyncDataVisitor.getOriginFilesSizeNeedToDownload().second
        ConfirmDialog.Builder(activity)
            .setPositiveButton(R.string.heytap_cloud_sync_close_now) { _: DialogInterface?, _: Int ->
                if (size > 0) {
                    ApiDmManager.getCloudSyncDM().getCloudSyncChannel(CloudSyncChannelType.OPLUS_CHANNEL).showDownloadOriginDialog(activity, size)
                } else {
                    resetSwitchAndShowTurnOffToast(activity)
                }
            }
            .setNegativeButton(R.string.heytap_cloud_view_sync_progress) { _: DialogInterface?, _: Int ->
                launchGalleryHome(activity)
            }
            .setMessage(activity.resources.getQuantityString(R.plurals.heytap_cloud_sync_files_count, count, count))
            .setTitle(R.string.heytap_cloud_sync_turn_off)
            .setOnDismissListener { isDialogShowing = false }
            .setCancelable(false)
            .build()
            .show()
        isDialogShowing = true
    }

    private fun launchGalleryHome(activity: Activity) {
        ActivityStarter(
            startContext = activity,
            bundle = Bundle().apply {
                putBoolean(KEY_HAS_AUTH_USE_NET_PERMISSION, true)
                putString(KEY_MEDIA_FROM, IntentUtils.getStringExtra(activity.intent, KEY_MEDIA_FROM))
            },
            postCard = PostCard(RouterConstants.RouterName.MAIN_ACTIVITY),
            activityOptions = createActivityOptionsIfNeeded()
        ) { intent ->
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        }.start()
    }

    private fun createActivityOptionsIfNeeded(): Bundle? {
        val extraBundle = Bundle().apply {
            putBoolean(KEY_FLEXIBLE_NEWTASK_FULLSCREEN, true)
        }
        return FlexibleWindowManagerWrapper.setExtraBundle(ActivityOptions.makeBasic(), extraBundle)
    }

    /**
     * 关闭欢太云同步开关并Toast提示已关闭
     */
    @JvmStatic
    fun resetSwitchAndShowTurnOffToast(activity: Activity) {
        if (DoubleClickUtils.isFastDoubleClick()) return
        if (ApiDmManager.getCloudSyncDM().getCloudSyncChannel(CloudSyncChannelType.OPLUS_CHANNEL).isSupportAlbumSyncSwitch().not()
            && ApiDmManager.getCloudSyncDM().getCloudSyncChannel(CloudSyncChannelType.OPLUS_CHANNEL).isAlbumSyncSwitchOpen()) {
            ApiDmManager.getCloudSyncDM().getCloudSyncChannel(CloudSyncChannelType.OPLUS_CHANNEL)
                .gotoCloudSyncSettings(activity, null, false)
            return
        }
        SwitchStateManager.setAlbumSync(false) {
            setIsHeyTapDownloadingOriginManually(false)
            AppScope.launch(Dispatchers.Main) {
                ToastUtil.showShortToast(R.string.heytap_cloud_sync_turn_off_toast)
            }
        }
    }

    /**
     * 开启"loading"
     */
    @JvmStatic
    fun showLoadingDialog(context: Context?, titleId: Int) {
        if (context == null) {
            GLog.d(TAG, LogFlag.DL, "showLoadingDialog, context is null")
            return
        }
        if (mLoadingDialog == null) {
            val builder = LoadingDialog.Builder(context, false)
            builder.setTips(titleId)
            builder.setCancelable(false)
            mLoadingDialog = builder.build().show()
            COUIThemeOverlay.getInstance().applyThemeOverlays(mLoadingDialog?.getContext())
            ScreenUtils.ignoreHomeMenuKey(mLoadingDialog?.getWindow())
        } else if (mLoadingDialog?.isShowing() == true) {
            mLoadingDialog?.show()
        }
    }

    /**
     * 关闭"loading"
     */
    @JvmStatic
    fun closeLoadingDialog() {
        if (mLoadingDialog != null) {
            mLoadingDialog!!.dismiss()
            mLoadingDialog = null
        }
    }

    /**
     * 云服务是否支持  云相册h5(而非快应用)
     */
    fun isSupportCloudAlbumH5(): Boolean {
        return PackageInfoUtils.getBooleanFromMetaData(packageName = PACKAGE_NAME_OCLOUD, name = META_DATA_SUPPORT_CLOUD_ALBUM_H5)
    }

    /**
     *  获取跳转云相册h5
     */
    private fun getCloudAlbumH5Url(): String? {
        val webUrl = ApiDmManager.getAppDM().getSecurityUrl()?.cloudAlbumH5Url ?: return null
        return getCloudWebDeepLink(webUrl, true)
    }

    /**
     * 获取可以跳转云服务web界面的 deeplink
     */
    private fun getCloudWebDeepLink(webUrl: String, needLogin: Boolean): String {
        return "htcloud://cloud.heytap.com/route?pageType=1&pageId=1&pageUrl=$webUrl&needLogin=$needLogin"
    }
}