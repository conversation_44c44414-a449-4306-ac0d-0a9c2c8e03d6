/**************************************************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SyncAlbumConfigUtil.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/5/22
 ** Author: <PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 **
 ** --------------------- --------------- Revision History ----------------------------------------
 **  <author>                   <data>         <version >     <desc>
 **  <PERSON><PERSON><PERSON>@Rom.Apps.Gallery    2019/5/22      1.0            Add file head
 *************************************************************************************************/

package com.oplus.gallery.framework.abilities.ocloudsync.album;

import static com.oplus.gallery.foundation.database.store.CloudSyncStore.DirType.DIRTYPE_OTHER;
import static com.oplus.gallery.foundation.database.store.CloudSyncStore.DirType.DIRTYPE_SYSTEM;
import static com.oplus.gallery.foundation.database.store.CloudSyncStore.SwitchStatusType.DIR_STATUS_CLOSE;
import static com.oplus.gallery.foundation.database.store.CloudSyncStore.SwitchStatusType.DIR_STATUS_OPEN;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.AND;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.COMMA;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.DOUBLE_QUOTE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.IN;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.LEFT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.LIKE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.LIKE_PERCENT;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.RIGHT_BRACKETS;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.VARIABLE_PLACEHOLDER;
import static com.oplus.gallery.foundation.database.util.DatabaseUtils.getFilterOutTrashedWhere;
import static com.oplus.gallery.foundation.database.util.DatabaseUtils.getOnlyTrashedWhere;
import static com.oplus.gallery.foundation.database.util.DatabaseUtils.getWhereQueryIn;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteConstraintException;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.oplus.gallery.business_lib.menuoperation.helper.RenameAlbumHelper;
import com.oplus.gallery.business_lib.model.config.allowlist.FolderNoteConfig;
import com.oplus.gallery.business_lib.model.config.allowlist.TimerShaftConfig;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketHelper;
import com.oplus.gallery.foundation.database.store.CloudSyncStore;
import com.oplus.gallery.foundation.database.store.CloudSyncStore.DbType;
import com.oplus.gallery.foundation.database.store.CloudSyncStore.FilterDirsType;
import com.oplus.gallery.foundation.database.store.CloudSyncStore.Operation;
import com.oplus.gallery.foundation.database.store.ConfigStore;
import com.oplus.gallery.foundation.database.store.ConfigStore.CloudAllowList;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.DataAccess;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.ConfigDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.framework.abilities.ocloudsync.db.CloudSyncProviderUtils;
import com.oplus.gallery.framework.abilities.ocloudsync.db.LocalSyncDataVisitor;
import com.oplus.gallery.framework.abilities.ocloudsync.metadata.CustomAlbumFields;
import com.oplus.gallery.framework.abilities.ocloudsync.metadata.CustomAlbumMetaData;
import com.oplus.gallery.framework.abilities.ocloudsync.metadata.DefaultAlbumMetaData;
import com.oplus.gallery.framework.abilities.ocloudsync.sync.GallerySyncManager;
import com.oplus.gallery.framework.abilities.ocloudsync.utils.CloudStatusPref;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class SyncAlbumConfigUtil {
    private static final String TAG = "SyncAlbumConfigUtil";
    public static final String ALBUM_PATH_SEPERATE = ",";
    public static final String PATH_SLASHES_SEPARATION = "/";
    public static final String PATH_START_SEPARATION = "*";
    public static final String PATH_PERCENT_SEPARATION = "%";
    private static final String WILDCARD = "*";
    private static List<Integer> sExcludeBucketIds;
    private static List<Integer> sIncludeImageBucketIds;
    private static List<Integer> sIncludeVideoBucketIds;
    private static List<Integer> sSwitchOffBucketIds;
    private static List<Integer> sDefaultSyncBucketIds;
    private static Map<String, String> sAlbumNamePathMap;
    private static List<String> sDefaultWildCardDirs;

    public static synchronized List<Integer> getExcludeBucketIds(boolean forceUpdate) {
        if (forceUpdate || (sExcludeBucketIds == null)) {
            String[] excludeArray = getFilterDirsDB(FilterDirsType.TYPE_EXCLUDE);
            getAllExcludeBucketIds(excludeArray);
        }
        return sExcludeBucketIds;
    }

    public static synchronized List<Integer> getSwitchOffBucketIds(Context context, boolean forceUpdate) {
        if (forceUpdate || (sSwitchOffBucketIds == null)) {
            String[] switchArrayDirs = getFilterDirsDB(FilterDirsType.TYPE_SWITCH);
            getAllSwitchOffBucketIds(context, switchArrayDirs);
        }
        return sSwitchOffBucketIds;
    }

    public static synchronized List<Integer> getIncludeImageBucketIds(boolean forceUpdate) {
        if (forceUpdate || (sIncludeImageBucketIds == null) || sIncludeImageBucketIds.isEmpty()) {
            ArrayList<String> roots = checkRoots(null);
            String[] images = getFilterDirs(FilterDirsType.TYPE_IMAGE);
            sIncludeImageBucketIds = getAllBucketIdsIncludeChildDirs(images, roots, DbType.DBType_Image);
        }
        return sIncludeImageBucketIds;
    }

    public static synchronized List<Integer> getIncludeVideoBucketIds(boolean forceUpdate) {
        if (forceUpdate || (sIncludeVideoBucketIds == null) || sIncludeVideoBucketIds.isEmpty()) {
            ArrayList<String> roots = checkRoots(null);
            String[] images = getFilterDirs(FilterDirsType.TYPE_VIDEO);
            sIncludeVideoBucketIds = getAllBucketIdsIncludeChildDirs(images, roots, DbType.DBType_Video);
        }
        return sIncludeVideoBucketIds;
    }

    /**
     * 获取 default 名单里通配符路径的list，路径是单条的。
     * 如 DCIM/*
     *
     * @return 相对路径的 list
     */
    public static List<String> getDefaultWildCardDirs(boolean forceUpdate) {
        if (forceUpdate || (sDefaultWildCardDirs == null) || sDefaultWildCardDirs.isEmpty()) {
            sDefaultWildCardDirs = getDefaultWildCardDirs();
        }
        return sDefaultWildCardDirs;
    }

    private static List<String> getDefaultWildCardDirs() {
        // dir_type in (?,?) and dir_path like "%*%"
        String builder = ConfigStore.CloudAllowList.DIR_TYPE + IN
                + LEFT_BRACKETS + VARIABLE_PLACEHOLDER + COMMA + VARIABLE_PLACEHOLDER + RIGHT_BRACKETS
                + AND
                + ConfigStore.CloudAllowList.DIR_PATH + LIKE
                + DOUBLE_QUOTE + LIKE_PERCENT + WILDCARD + LIKE_PERCENT + DOUBLE_QUOTE;

        String[] projection = new String[]{ConfigStore.CloudAllowList.DIR_PATH};
        String[] selectionArgs = new String[]{String.valueOf(DIRTYPE_SYSTEM), String.valueOf(DIRTYPE_OTHER)};
        List<String> wildcardDirs = new ArrayList<>();
        try (Cursor cursor = CloudSyncProviderUtils.queryDir(projection, builder, selectionArgs, null)) {
            if ((cursor == null) || (cursor.getCount() == 0)) {
                GLog.e(TAG, "getDefaultWildCardDirs, cursor = null");
                return wildcardDirs;
            }
            int dirPathIndex = cursor.getColumnIndex(ConfigStore.CloudAllowList.DIR_PATH);
            while (cursor.moveToNext()) {
                // 如 DCIM/Camera,DCIM/*
                String dirPathStr = cursor.getString(dirPathIndex);
                List<String> dirPaths = convertStrToArray(dirPathStr);
                for (String dirPath : dirPaths) {
                    // 如 DCIM/*
                    if (dirPath.contains(WILDCARD)) {
                        wildcardDirs.add(dirPath);
                        if (GProperty.getDEBUG_CLOUD_ALBUM_SYNC()) {
                            GLog.d(TAG, null, () -> "getDefaultWildCardDirs, add dir=" + dirPath);
                        }
                    }
                }
            }
        }
        return wildcardDirs;
    }

    public static List<Integer> getDefaultSyncBucketIds() {
        return sDefaultSyncBucketIds;
    }

    public static void updateBucketIdsCache(Context context) {
        getExcludeBucketIds(true);
        getSwitchOffBucketIds(context, true);
        getIncludeImageBucketIds(true);
        getIncludeVideoBucketIds(true);
    }

    /**
     * 获取同步图集配置信息的 map
     * @param context context

     * @param forceUpdate 是否需要强制刷新一次内容
     * @return map key 为 dirPath， value 为 dirName
     */
    public static Map<String, String> updateAlbumDirPath(Context context, boolean forceUpdate) {
        if (forceUpdate || (sAlbumNamePathMap == null)) {
            sAlbumNamePathMap = convertFilterDirsMap(context, FilterDirsType.TYPE_ALL);
        }
        return sAlbumNamePathMap;
    }

    private static void getAllSwitchOffBucketIds(Context context, String[] excludeDirName) {
        if ((excludeDirName == null) || (excludeDirName.length <= 0)) {
            if (sSwitchOffBucketIds != null) {
                sSwitchOffBucketIds.clear();
            }
            //目录同步出现异常，相册使用本地配置xml文件作为同步范围
            if (CloudSyncProviderUtils.getAllDirCount() < SyncAlbumDataManager.LOAD_ALBUM_MIN_RETURN_SIZE) {
                GLog.d(TAG, "getAllSwitchOffBucketIds, dir is empty");
                String[] albumsSyncDir = getDefaultFilterDirs(FilterDirsType.TYPE_ALL);
                ArrayList<String> roots = checkRoots(null);
                sDefaultSyncBucketIds = getAllBucketIdsIncludeChildDirs(albumsSyncDir, roots, DbType.DBType_Media);
            }
            return;
        }
        if (sDefaultSyncBucketIds != null) {
            sDefaultSyncBucketIds.clear();
        }
        ArrayList<String> roots = checkRoots(null);
        sSwitchOffBucketIds = getAllBucketIdsIncludeChildDirs(excludeDirName, roots, DbType.DBType_Media);
        GLog.d(TAG, "getAllSwitchOffBucketIds, has close dir");
    }

    private static void getAllExcludeBucketIds(String[] excludeDirName) {
        if ((excludeDirName == null) || (excludeDirName.length <= 0)) {
            if (sExcludeBucketIds != null) {
                sExcludeBucketIds.clear();
            }
            return;
        }
        ArrayList<String> roots = checkRoots(null);
        sExcludeBucketIds = getAllBucketIdsIncludeChildDirs(excludeDirName, roots, DbType.DBType_Media);
    }

    public static List<Integer> getAllBucketIdsIncludeChildDirs(String[] dirs, List<String> roots, int dbType) {
        ArrayList<Integer> normalDirs = new ArrayList<>();
        ArrayList<String> parentDirs = new ArrayList<>();
        for (String root : roots) {
            for (String dirName : dirs) {
                if (!TextUtils.isEmpty(dirName)) {
                    List<String> strArr = convertStrToArray(dirName);
                    for (String dirStr : strArr) {
                        if (!TextUtils.isEmpty(dirStr)) {
                            if (dirStr.endsWith(PATH_START_SEPARATION)) {
                                String fullPath = null;
                                if (dirStr.startsWith(PATH_SLASHES_SEPARATION)) {
                                    fullPath = root + dirStr;
                                } else {
                                    fullPath = root + PATH_SLASHES_SEPARATION + dirStr;
                                }
                                parentDirs.add(fullPath.replace(PATH_START_SEPARATION, PATH_PERCENT_SEPARATION));
                            } else {
                                String fullPath = null;
                                if (dirStr.startsWith(PATH_SLASHES_SEPARATION)) {
                                    fullPath = root + dirStr;
                                } else {
                                    fullPath = root + PATH_SLASHES_SEPARATION + dirStr;
                                }
                                normalDirs.add(fullPath.toLowerCase(Locale.US).hashCode());
                            }
                        }
                    }
                }
            }
        }
        List<Integer> childDirs = null;
        if (parentDirs.size() > 0) {
            if ((dbType == DbType.DBType_Media)
                    || (dbType == DbType.DBType_Image)
                    || (dbType == DbType.DBType_Video)) {
                childDirs = LocalSyncDataVisitor.INSTANCE.getChildBucketIdsByParent(TextUtil.EMPTY_STRING, parentDirs);
            } else if (dbType == DbType.DBType_GallerySync) {
                childDirs = LocalSyncDataVisitor.INSTANCE.getChildBucketIdsByParent(
                        getFilterOutTrashedWhere(GalleryStore.GalleryMedia.TAB), parentDirs);
            } else if (dbType == DbType.DBType_Recycle) {
                childDirs = LocalSyncDataVisitor.INSTANCE.getChildBucketIdsByParent(
                        getOnlyTrashedWhere(GalleryStore.GalleryMedia.TAB), parentDirs);
            }
        }
        if ((childDirs != null) && !childDirs.isEmpty()) {
            normalDirs.addAll(childDirs);
        }
        return normalDirs;
    }

    public static ArrayList<String> checkRoots(ArrayList<String> roots) {
        if (roots == null) {
            roots = new ArrayList<>();
        }
        if (roots.size() == 0) {
            roots.add(OplusEnvironment.getInternalPath());
            String multia = OplusEnvironment.getLocalMultiAppPath();
            if (!TextUtils.isEmpty(multia)) {
                roots.add(multia);
            }
        }
        return roots;
    }

    public static String[] getFilterDirs(int type) {
        String[] dirs = getFilterDirsDB(type);
        if ((dirs != null) && (dirs.length > 0)) {
            return dirs;
        }
        return getDefaultFilterDirs(type);
    }

    /**
     * Marked by limao:本地同步目录名单配置已经不再使用了，后续应该重构相关代码
     */
    private static String[] getDefaultFilterDirs(int type) {
        // Marked by limao:本地同步目录配置res xml资源已经删除，此段代码也需要删除。与端云组确认该方法可以返回空数组。后续应该重构相关代码.
        return new String[0];
    }

    private static String[] getFilterDirsDB(int type) {
        String where = null;
        if (type == FilterDirsType.TYPE_IMAGE) {
            where = CloudAllowList.DIR_CONTENT_TYPE + "!=" + FilterDirsType.TYPE_VIDEO;
        } else if (type == FilterDirsType.TYPE_VIDEO) {
            where = CloudAllowList.DIR_CONTENT_TYPE + "!=" + FilterDirsType.TYPE_IMAGE;
        } else if (type == FilterDirsType.TYPE_EXCLUDE) {
            where = CloudAllowList.DIR_CONTENT_TYPE + "==" + FilterDirsType.TYPE_EXCLUDE;
        } else if (type == FilterDirsType.TYPE_SWITCH) {
            where = CloudAllowList.DIR_CONTENT_TYPE + "==" + FilterDirsType.TYPE_EXCLUDE
                    + " OR " + CloudAllowList.SWITCH_STATE + "=0";
        }
        String[] projection = new String[]{CloudAllowList.DIR_PATH};
        try (Cursor cursor = CloudSyncProviderUtils.queryDir(projection, where, null, null)) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                String[] dirs = new String[cursor.getCount()];
                int index = 0;
                int dirIndex = cursor.getColumnIndex(CloudAllowList.DIR_PATH);
                while (cursor.moveToNext()) {
                    dirs[index++] = cursor.getString(dirIndex);
                }
                return dirs;
            }
        } catch (Exception e) {
            GLog.e(TAG, "getFilterDirsDB, e = " + e);
        }
        return null;
    }

    public static synchronized boolean updateSyncAlbumDirConfig(List<DefaultAlbumMetaData> defaultAlbumMetaData, Context context) {
        int localConfigVersion = CloudStatusPref.getSyncDirConfigVersion(context, 0);
        int dirSize = CloudSyncProviderUtils.getAllDirCount();
        try {
            if ((defaultAlbumMetaData != null) && (defaultAlbumMetaData.size() > 0)) {
                DefaultAlbumMetaData metaData = defaultAlbumMetaData.get(0);

                int version = (int) metaData.getSysProtocolVersion();
                GLog.d(TAG, null, () -> "updateSyncAlbumDirConfig, localConfigVersion: "
                        + localConfigVersion + " version: " + version + " size : " + dirSize);

                if ((localConfigVersion != version) || (dirSize < SyncAlbumDataManager.LOAD_ALBUM_MIN_RETURN_SIZE)) {
                    // update and insert
                    dealConfigAlbumDir(defaultAlbumMetaData);
                    CloudStatusPref.setSyncDirConfigVersion(context, version);
                    return true;
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "updateSyncAlbumDirConfig, found exception : " + e.toString());
        }
        return false;
    }

    public static boolean updateSyncAlbumConfig(Context context) {
        if (context == null) {
            GLog.e(TAG, "updateSyncAlbumConfig, context is null");
            return false;
        }
        updateBucketIdsCache(context);
        return true;
    }

    private static void dealConfigAlbumDir(List<DefaultAlbumMetaData> defaultAlbumMetaDatas) {
        List<String> dirPaths = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        // delete
        for (DefaultAlbumMetaData defaultAlbumMetaData : defaultAlbumMetaDatas) {
            if ((defaultAlbumMetaData == null) || (defaultAlbumMetaData.getCustomAlbumMetaData() == null)) {
                if (GallerySyncManager.Companion.getTEST_ENABLE()) {
                    GLog.d(TAG, null, () -> "dealConfigAlbumDir continue " + defaultAlbumMetaData);
                }
                continue;
            }
            CustomAlbumMetaData customAlbumMetaData = defaultAlbumMetaData.getCustomAlbumMetaData();
            builder.append("?,");
            dirPaths.add(customAlbumMetaData.getCustomFields().getDirPaths());
        }
        builder.delete(builder.length() - 1, builder.length());
        String dirArray[] = new String[defaultAlbumMetaDatas.size()];
        dirPaths.toArray(dirArray);
        try {
            DeleteReq req = new DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.CONFIG)
                    .setTableType(ConfigDbDao.TableType.CLOUD_ALLOW_LIST)
                    .setWhere(CloudAllowList.DIR_PATH + " NOT IN ( " + builder.toString() + " )")
                    .setWhereArgs(dirArray)
                    .build();
            int delete = DataAccess.getAccess().delete(req);
            GLog.d(TAG, null, () -> "dealConfigAlbumDir, delete=" + delete);
        } catch (Exception e) {
            GLog.e(TAG, "dealConfigAlbumDir, e = " + e);
        }
        //insert or update
        for (DefaultAlbumMetaData defaultAlbumMetaData : defaultAlbumMetaDatas) {
            if (defaultAlbumMetaData == null) {
                continue;
            }
            CustomAlbumMetaData customAlbumMetaData = defaultAlbumMetaData.getCustomAlbumMetaData();
            if (customAlbumMetaData == null) {
                continue;
            }
            Integer configStatus = customAlbumMetaData.getConfigStatus();
            if (GallerySyncManager.Companion.getTEST_ENABLE()) {
                GLog.d(TAG, null, () -> "dealConfigAlbumDir for dirInfoMetaData " + configStatus + ":" + customAlbumMetaData);
            }
            updateAlbumFilterDirs(configStatus, customAlbumMetaData);
        }
        GLog.d(TAG, "dealConfigAlbumDir end");
    }

    public static void updateAlbumFilterDirs(Integer configStatus, CustomAlbumMetaData customAlbumMetaData) {
        if (customAlbumMetaData == null) {
            return;
        }
        ContentValues values = new ContentValues();
        if (configStatus != null) {
            values.put(CloudAllowList.CONFIG_STATUS, configStatus.intValue());
        }
        values.put(CloudAllowList.GLOBAL_ID, customAlbumMetaData.getSysRecordId());
        values.put(CloudAllowList.DIR_NAME, customAlbumMetaData.getCustomFields().getDirName());
        values.put(CloudAllowList.DIR_TYPE, customAlbumMetaData.getCustomFields().getDirType());
        values.put(CloudAllowList.SWITCH_STATE, customAlbumMetaData.getCustomFields().getSwitchStatus());
        values.put(CloudAllowList.DIR_CONTENT_TYPE, customAlbumMetaData.getCustomFields().getDirContentType());
        values.put(CloudAllowList.DIR_DISPLAY_NAME, customAlbumMetaData.getCustomFields().getDirDisplayName());
        values.put(CloudAllowList.OPERATION, Operation.DOWNLOAD_ADD);

        int count = CloudSyncProviderUtils.updateDir(values, CloudAllowList.DIR_PATH + "=?",
                new String[]{customAlbumMetaData.getCustomFields().getDirPaths()});
        GLog.d(TAG, null, () -> "updateAlbumFilterDirs " + PathMask.INSTANCE.mask(customAlbumMetaData.getCustomFields().getDirPaths()) + "," + count);
        if (count == 0) {
            values.put(CloudAllowList.DIR_PATH, customAlbumMetaData.getCustomFields().getDirPaths());
            CloudSyncProviderUtils.insertDir(values);
        }
    }

    public static void processRecoveryAlbumDirs(Context context, ArrayList<CustomAlbumMetaData> customAlbumMetaDatas,
                                                ArrayList<String> updatePathList) {


        List<String> cloudCoverLocalDirPaths = new ArrayList<>();
        List<String> localCoverCloudDirPaths = new ArrayList<>();

        HashMap<String, Long> pathModifyTimeMap = new HashMap<>();
        HashMap<String, Integer> pathSwitchStatusMap = new HashMap<>();
        HashMap<String, String> pathDirDisplayNameMap = new HashMap<>();
        HashMap<String, Long> pathLocalModifyTimeMap = new HashMap<>();
        List<String> dirPathList = new ArrayList<>();

        for (CustomAlbumMetaData customAlbumMetaData : customAlbumMetaDatas) {
            if (customAlbumMetaData != null) {
                CustomAlbumFields customAlbumFields = customAlbumMetaData.getCustomFields();
                String dirPath = customAlbumFields.getDirPaths();
                long modifyTime = customAlbumFields.getModifyTime();
                int switchStatus = customAlbumFields.getSwitchStatus();
                String dirDisplayName = customAlbumFields.getDirDisplayName();
                dirPathList.add(dirPath);
                pathModifyTimeMap.put(dirPath, modifyTime);
                pathSwitchStatusMap.put(dirPath, switchStatus);
                pathDirDisplayNameMap.put(dirPath, dirDisplayName);
            }
        }

        StringBuilder whereSb = CloudSyncProviderUtils.getWhereForInKeyword(customAlbumMetaDatas.size(), CloudAllowList.DIR_PATH);
        String[] selectionArgs = new String[customAlbumMetaDatas.size()];
        dirPathList.toArray(selectionArgs);
        String[] projection = {
                CloudAllowList.DIR_PATH,
                CloudAllowList.DIR_MODIFY_TIME,
                CloudAllowList.SWITCH_STATE,
                CloudAllowList.DIR_DISPLAY_NAME
        };

        //if recovery updateTime more than local updateTime, update
        try (Cursor cursor = CloudSyncProviderUtils.queryDir(projection, whereSb.toString(), selectionArgs, null)) {
            prepareUpdateDirPaths(cursor,

                    pathModifyTimeMap,
                    pathLocalModifyTimeMap,
                    pathSwitchStatusMap,
                    pathDirDisplayNameMap,
                    cloudCoverLocalDirPaths,
                    localCoverCloudDirPaths);
            updateLocalDir(context,


                    customAlbumMetaDatas,
                    localCoverCloudDirPaths,
                    cloudCoverLocalDirPaths,
                    updatePathList,
                    pathLocalModifyTimeMap);
        } catch (Exception e) {
            GLog.w(TAG, "processRecoveryAlbumDirs, e= " + e.toString());

        }
    }

    /**
     * 获取当前云端恢复的数据中是否存在非“全部同步”且开关关闭的项

     * @param customAlbumMetaDatas 从云端恢复的自定义图集数据
     * @return true当存在非“全部同步”且开关关闭的项时. 否则false
     */
    public static boolean getHasAlbumDirChangeToClose(ArrayList<CustomAlbumMetaData> customAlbumMetaDatas) {
        boolean hasAlbumDirChangeToClose = false;
        for (CustomAlbumMetaData customAlbumMetaData : customAlbumMetaDatas) {
            if (customAlbumMetaData != null) {
                CustomAlbumFields customAlbumFields = customAlbumMetaData.getCustomFields();
                String dirPath = customAlbumFields.getDirPaths();
                int switchStatus = customAlbumFields.getSwitchStatus();
                /**
                 * 当满足条件：
                 * 1.当前项不是“全部同步”开关对应项
                 * 2.当前项开关关闭
                 * 时，hasAlbumDirChangeToClose为true
                 */
                if ((dirPath != null)
                        && (!dirPath.equals(CloudSyncStore.SyncAllAlbumType.SYNC_ALL_ALBUM))
                        && (switchStatus != CloudSyncStore.SwitchStatusType.DIR_STATUS_OPEN)) {
                    hasAlbumDirChangeToClose = true;
                    break;
                }
            }
        }
        return hasAlbumDirChangeToClose;
    }

    /**
     * 准备需要更新的 dir paths
     * @param cursor
     * @param pathModifyTimeMap
     * @param pathLocalModifyTimeMap
     * @param pathSwitchStatusMap
     * @param pathDirDisplayNameMap
     * @param cloudCoverLocalDirPaths
     * @param localCoverCloudDirPaths
     *
     */
    private static void prepareUpdateDirPaths(Cursor cursor,
                                              HashMap<String, Long> pathModifyTimeMap,
                                              HashMap<String, Long> pathLocalModifyTimeMap,
                                              HashMap<String, Integer> pathSwitchStatusMap,
                                              HashMap<String, String> pathDirDisplayNameMap,
                                              List<String> cloudCoverLocalDirPaths,
                                              List<String> localCoverCloudDirPaths) {
        if ((cursor != null) && (cursor.getCount() > 0)) {
            int dirPathIndex = cursor.getColumnIndex(CloudAllowList.DIR_PATH);
            int modifyTimeIndex = cursor.getColumnIndex(CloudAllowList.DIR_MODIFY_TIME);
            int switchStatusIndex = cursor.getColumnIndex(CloudAllowList.SWITCH_STATE);
            int dirDisplayNameIndex = cursor.getColumnIndex(CloudAllowList.DIR_DISPLAY_NAME);
            while (cursor.moveToNext()) {
                String dirPath = cursor.getString(dirPathIndex);
                if (TextUtils.isEmpty(dirPath) || (!pathModifyTimeMap.containsKey(dirPath))) {
                    GLog.d(TAG, null, () -> "prepareUpdateDirPaths, dirPath: continue " + PathMask.INSTANCE.mask(dirPath));
                    continue;
                }
                long modifyTime = cursor.getLong(modifyTimeIndex);
                int switchStatus = cursor.getInt(switchStatusIndex);
                String dirDisplayName = cursor.getString(dirDisplayNameIndex);
                pathLocalModifyTimeMap.put(dirPath, modifyTime);
                Long time = pathModifyTimeMap.get(dirPath);
                long cloudModifyTime = (time == null) ? 0 : time;
                Integer status = pathSwitchStatusMap.get(dirPath);
                int cloudSwitchStatus = (status == null) ? 0 : status;
                String cloudDirDisplayName = pathDirDisplayNameMap.get(dirPath);
                GLog.d(TAG, null, () -> "prepareUpdateDirPaths, dirPath: " + PathMask.INSTANCE.mask(dirPath)
                        + " modifyTime: " + modifyTime + " switchStatus: " + switchStatus
                        + " compareUpdateTime: " + cloudModifyTime + " cloudSwitchStatus: "
                        + cloudSwitchStatus + " cloudDirDisplayName: " + cloudDirDisplayName);

                /*
                 * 本地覆盖云端的条件
                 * 本地数据更新，同时满足以下条件中的任何一个：
                 *      a. 同步开关状态不同
                 *      b. 图集的显示名称不同
                 */
                if ((modifyTime > cloudModifyTime)
                        && ((cloudSwitchStatus != switchStatus)
                        || ((dirDisplayName != null) && !dirDisplayName.equals(cloudDirDisplayName)
                        || ((cloudDirDisplayName != null) && !cloudDirDisplayName.equals(dirDisplayName))))) {
                    localCoverCloudDirPaths.add(dirPath);
                } else {
                    cloudCoverLocalDirPaths.add(dirPath);
                }
            }
        }
    }

    // 根据 dir paths 更新本地数据库
    private static void updateLocalDir(Context context,
                                       ArrayList<CustomAlbumMetaData> customAlbumMetaDatas,
                                       List<String> localCoverCloudDirPaths,
                                       List<String> cloudCoverLocalDirPaths,
                                       ArrayList<String> updatePathList,
                                       HashMap<String, Long> pathLocalModifyTimeMap) {


        for (CustomAlbumMetaData customAlbumMetaData : customAlbumMetaDatas) {
            if (customAlbumMetaData == null) {
                continue;
            }
            CustomAlbumFields customAlbumFields = customAlbumMetaData.getCustomFields();
            if (customAlbumFields == null) {
                continue;
            }
            String dirPath = customAlbumFields.getDirPaths();
            if (localCoverCloudDirPaths.contains(dirPath)) {
                //本地存在对应数据，并且本地覆盖云端，更新本地global_id和sysVersion
                ContentValues values = new ContentValues();
                String globalId = customAlbumMetaData.getSysRecordId();
                values.put(CloudAllowList.GLOBAL_ID, globalId);
                values.put(CloudAllowList.SYS_VERSION, customAlbumMetaData.getSysVersion());
                values.put(CloudAllowList.OPERATION, CloudSyncStore.Operation.OPERATION_UPDATE);
                CloudSyncProviderUtils.updateDir(values, CloudAllowList.DIR_PATH + "=?", new String[]{dirPath});
                GLog.d(TAG, null, () -> "updateLocalDir, invalidateUpdate: " + PathMask.INSTANCE.mask(dirPath));
                continue;
            }
            if (cloudCoverLocalDirPaths.contains(dirPath)) {
                updatePathList.add(dirPath);
                ContentValues values = new ContentValues();
                int switchStatus = customAlbumFields.getSwitchStatus();
                long modifyTime = customAlbumFields.getModifyTime();
                int dirType = customAlbumFields.getDirType();
                String dirDisplayName = customAlbumFields.getDirDisplayName();
                String globalId = customAlbumMetaData.getSysRecordId();

                boolean renameSuccess = false;
                Long time = pathLocalModifyTimeMap.get(dirPath);
                long localModifyTime = (time == null) ? 0 : time;
                /*
                云端同步开关关闭后，cloudAllowedListTable 被删除， 云端同步开关重新打开后，从云端恢复数据时，本地的 modifyTime 为 0，但
                在关闭开关期间，图集可能被重命名了，仅以 modifyTime 判断是否从云端恢复数据，会改写了本地的图集名称数据
                */
                if (localModifyTime == 0) {
                    List<String> dirArray = convertStrToArray(dirPath);
                    String bucketPath = File.separator + dirArray.get(0);
                    List<Integer> bucketIds = FilePathUtils.getBucketIds(bucketPath);
                    long localRenameTime = FolderNoteConfig.getInstance().getRenameTime(bucketIds.get(0));

                    if (localRenameTime > modifyTime) { // 从本地恢复数据
                        renameSuccess = true;
                        dirDisplayName = FolderNoteConfig.getInstance().getCustomName(bucketIds.get(0));
                        modifyTime = localRenameTime;
                        values.put(CloudAllowList.OPERATION, Operation.OPERATION_UPDATE);
                    }
                }

                if (!renameSuccess) { // 从云端恢复数据
                    renameSuccess = updateAlbumCustomNameTable(customAlbumMetaData);
                    values.put(CloudAllowList.OPERATION, Operation.OPERATION_NONE);
                }

                if (renameSuccess) { // 从云端恢复数据，并使用本地的目录名
                    values.put(CloudAllowList.DIR_DISPLAY_NAME, dirDisplayName);
                }

                values.put(CloudAllowList.DIR_TYPE, dirType);
                values.put(CloudAllowList.SWITCH_STATE, switchStatus);
                values.put(CloudAllowList.DIR_MODIFY_TIME, modifyTime);
                values.put(CloudAllowList.GLOBAL_ID, globalId);
                values.put(CloudAllowList.SYS_VERSION, customAlbumMetaData.getSysVersion());
                CloudSyncProviderUtils.updateDir(values, CloudAllowList.DIR_PATH + "=?", new String[]{dirPath});
                GLog.d(TAG, null, () -> "updateLocalDir, contains update dirPath=" + PathMask.INSTANCE.mask(dirPath)
                        + ", dirDisplayName=" + PathMask.INSTANCE.mask(values.getAsString(CloudAllowList.DIR_DISPLAY_NAME))
                        + ", switchStatus=" + values.getAsString(CloudAllowList.SWITCH_STATE)
                        + ", operation=" + values.getAsString(CloudAllowList.OPERATION));
            } else {
                //云端有图集配置，但是本地不存在对应图集，直接插入数据库
                ContentValues values = new ContentValues();
                values.put(CloudAllowList.DIR_NAME, customAlbumFields.getDirName());
                values.put(CloudAllowList.DIR_TYPE, customAlbumFields.getDirType());
                values.put(CloudAllowList.SWITCH_STATE, customAlbumFields.getSwitchStatus());
                values.put(CloudAllowList.DIR_CONTENT_TYPE, customAlbumFields.getDirContentType());
                values.put(CloudAllowList.DIR_MODIFY_TIME, customAlbumFields.getModifyTime());
                values.put(CloudAllowList.DIR_DISPLAY_NAME, customAlbumFields.getDirDisplayName());
                values.put(CloudAllowList.GLOBAL_ID, customAlbumMetaData.getSysRecordId());
                values.put(CloudAllowList.SYS_VERSION, customAlbumMetaData.getSysVersion());
                values.put(CloudAllowList.DIR_PATH, customAlbumFields.getDirPaths());
                if (!TextUtils.isEmpty(customAlbumFields.getDirDisplayName())) {
                    updateAlbumCustomNameTable(customAlbumMetaData);
                }

                //insertDir时如果本地又产生了相同路径的数据，会导致插入失败，则updateDir更新数据
                boolean insertResult = false;
                try {
                    CloudSyncProviderUtils.insertDir(values);
                    insertResult = true;
                } catch (SQLiteConstraintException e) {
                    GLog.w(TAG, "updateLocalDir insertDir " + e.toString());
                    insertResult = CloudSyncProviderUtils.updateDir(values, CloudAllowList.DIR_PATH + "=?", new String[]{dirPath}) == 1;
                }
                boolean finalInsertResult = insertResult;
                GLog.d(TAG, null, () -> "updateLocalDir not contains update dirPath: " + PathMask.INSTANCE.mask(dirPath) + ","
                        + values.getAsString(CloudAllowList.DIR_DISPLAY_NAME) + " result: " + finalInsertResult);
            }
        }
    }

    private static boolean updateAlbumCustomNameTable(@NonNull CustomAlbumMetaData customAlbumMetaData) {
        boolean retValue = false;
        String dirPath = customAlbumMetaData.getCustomFields().getDirPaths();
        List<String> dirArray = convertStrToArray(dirPath);

        try {
            boolean success = true;

            for (String folderPath : dirArray) {
                if (TextUtils.isEmpty(folderPath)) {
                    continue;
                }

                String bucketPath = File.separator + folderPath;
                List<Integer> bucketIds = FilePathUtils.getBucketIds(bucketPath);
                success &= RenameAlbumHelper.doRename(bucketIds, bucketPath, customAlbumMetaData.getCustomFields().getDirDisplayName(), customAlbumMetaData.getCustomFields().getModifyTime());
            }
            retValue = success;
        } catch (Exception e) {
            GLog.e(TAG, "updateAlbumCustomNameTable: " + e.toString());
        }

        return retValue;
    }

    private static HashMap<String, String> convertFilterDirsMap(Context context, int type) {
        HashMap<String, String> pathNameMap = new HashMap<>();
        if (context == null) {
            GLog.e(TAG, "convertFilterDirsMap--context is null");
            return pathNameMap;
        }

        String where = null;
        //todo
        if (type == FilterDirsType.TYPE_IMAGE) {
            where = CloudAllowList.DIR_CONTENT_TYPE + "!=" + FilterDirsType.TYPE_VIDEO;
        } else if (type == FilterDirsType.TYPE_VIDEO) {
            where = CloudAllowList.DIR_CONTENT_TYPE + "!=" + FilterDirsType.TYPE_IMAGE;
        } else if (type == FilterDirsType.TYPE_EXCLUDE) {
            where = CloudAllowList.DIR_CONTENT_TYPE + "==" + FilterDirsType.TYPE_EXCLUDE;
        } else if (type == FilterDirsType.TYPE_ALL) {
            where = null;
        }
        String[] projection = new String[]{CloudAllowList.DIR_NAME, CloudAllowList.DIR_PATH};

        try (Cursor cursor = CloudSyncProviderUtils.queryDir(projection, where, null, null)) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int dirPathIndex = cursor.getColumnIndex(CloudAllowList.DIR_PATH);
                int dirNameIndex = cursor.getColumnIndex(CloudAllowList.DIR_NAME);
                while (cursor.moveToNext()) {
                    String albumPath = cursor.getString(dirPathIndex);
                    String albumName = cursor.getString(dirNameIndex);
                    pathNameMap.put(albumPath, albumName);
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getFilterDirsDB, e = " + e);
        }
        return pathNameMap;
    }

    public static List<String> convertStrToArray(String str) {
        List<String> arrayList = new ArrayList<>();
        String[] strArray = str.split(ALBUM_PATH_SEPERATE);
        if (strArray != null) {
            arrayList = Arrays.asList(strArray);
        }
        return arrayList;
    }

    /**
     * 查询是否存在时间轴范围内开关值为关的同步图集
     *
     * @return true 为存在，false 为不存在或查询过程出现异常
     */
    public static boolean isAnyAlbumDirSwitchStateClose() {
        List<String> localAlbumRelativePathList = getLocalExistAlbumRelativePaths();
        GLog.d(TAG, null, () -> "isAnyAlbumDirSwitchStateClose, localAlbumRelativePathList.size=" + localAlbumRelativePathList.size());
        if (localAlbumRelativePathList.isEmpty()) {
            return false;
        }

        List<String> localExistTimerShaftRelativePathList = new ArrayList<>();
        for (String localAlbumRelativePath : localAlbumRelativePathList) {
            boolean isAlbumBelongToTimerShaft = TimerShaftConfig.INSTANCE.isContain(localAlbumRelativePath);
            if (isAlbumBelongToTimerShaft) {
                localExistTimerShaftRelativePathList.add(localAlbumRelativePath);
            }
        }
        GLog.d(TAG, null, () -> "isAnyAlbumDirSwitchStateClose, localExistTimerShaftRelativePathList.size="
                + localExistTimerShaftRelativePathList.size());
        if (localExistTimerShaftRelativePathList.isEmpty()) {
            return false;
        }

        // 判断是否存在本地时间轴范围内的图集的同步开关为关的图集
        List<String> closeDirList = getSwitchStateCloseSyncAlbumRelativePaths();
        for (String localTimeShaftAlbumDir : localExistTimerShaftRelativePathList) {
            for (String closeDir : closeDirList) {
                boolean isLocalTimerShaftAlbumClose = FilePathUtils.isEquals(localTimeShaftAlbumDir, closeDir);
                if (GProperty.getDEBUG_CLOUD_ALBUM_SYNC()) {
                    GLog.d(TAG, null, () -> "isAnyAlbumDirSwitchStateClose, result=" + isLocalTimerShaftAlbumClose
                            + ", localTimeShaftAlbumDir=" + localTimeShaftAlbumDir
                            + ", closeDir=" + closeDir);
                }
                if (isLocalTimerShaftAlbumClose) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取本地存在的图集的相对路径
     *
     * @return 本地存在图集的相对路径的list，不包含图集里图片全是非法状态和回收站状态的图集
     * 单条的格式为 DCIM/MyAlbums/c1
     */
    private static List<String> getLocalExistAlbumRelativePaths() {
        List<String> relativePathList = new ArrayList<>();
        String[] projection = new String[]{GalleryStore.GalleryColumns.LocalColumns.RELATIVE_PATH};
        try (Cursor cursor = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(projection)
                .setConvert(new CursorConvert())
                .setGroupBy(GalleryStore.GalleryColumns.LocalColumns.RELATIVE_PATH)
                .build().exec()) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    String relativePath = cursor.getString(0);
                    if (!TextUtils.isEmpty(relativePath)) {
                        // 查询出来的结果为 DCIM/MyAlbums/c1/，需要去掉最后一个/
                        String noEndWithSeparatorRelativePath = relativePath.substring(0, relativePath.lastIndexOf("/"));
                        relativePathList.add(noEndWithSeparatorRelativePath);
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getLocalExistAlbumRelativePaths, e=", e);
        }
        return relativePathList;
    }

    /**
     * 获取目录为关的同步图集的相对路径
     *
     * @return 单条的相对路径的 list，单条的格式为 DCIM/MyAlbums/c1
     */
    private static List<String> getSwitchStateCloseSyncAlbumRelativePaths() {
        List<String> relativePathList = new ArrayList<>();
        String[] projection = new String[]{CloudAllowList.DIR_PATH};
        String whereClause = CloudAllowList.SWITCH_STATE + EQUAL + DIR_STATUS_CLOSE;
        try (Cursor cursor = CloudSyncProviderUtils.queryDir(projection, whereClause, null, null)) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    String dirPaths = cursor.getString(0);
                    for (String path : convertStrToArray(dirPaths)) {
                        if (!TextUtils.isEmpty(path)) {
                            relativePathList.add(path);
                        }
                    }
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "getSwitchStateCloseSyncAlbumRelativePaths, e=", e);
        }
        return relativePathList;
    }

    /**
     * 用于新图集加入同步图集表的时候判断开关状态
     *
     * @param albumBucketPath 新图集的bucketPath
     * @return true 为开， false 为关
     */
    public static int decideSwitchStateOfNewAlbumConfig(String albumBucketPath) {
        if (CloudSyncProviderUtils.isSyncAllSwitchOpen()) {
            // "全部同步”开关开启时，新增图集开关为开；
            return DIR_STATUS_OPEN;
        }
        // 判断图集是否属于默认图集，固定的默认图集都在 DefaultAlbumSync 阶段就存储在本地配置表了，此处主要是判断是否属于默认图集的通配路径
        return isAlbumBelongToDefaultWildCardDirs(albumBucketPath) ? DIR_STATUS_OPEN : DIR_STATUS_CLOSE;
    }

    /**
     * 判断图集是否属于 default 名单下的通配图集
     *
     * @param albumBucketPath 待判断的图集的 bucketPath ，是相对路径，如 DCIM/MyAlbums/1
     * @return true 为包含，false 为不包含
     */
    private static boolean isAlbumBelongToDefaultWildCardDirs(String albumBucketPath) {
        if (TextUtils.isEmpty(albumBucketPath)) {
            return false;
        }
        List<String> wildcardDirs = getDefaultWildCardDirs(false);
        for (String wildcardDir : wildcardDirs) {
            if (BucketHelper.isBucketPathMatchWildCardPath(albumBucketPath, wildcardDir)) {
                if (GProperty.getDEBUG_CLOUD_ALBUM_SYNC()) {
                    GLog.d(TAG, null, () -> "isAlbumBelongToDefaultWildCardDirs, belong to default, inputBucketPath=" + albumBucketPath
                            + " wildcardDir=" + wildcardDir);
                }
                return true;
            }
        }
        if (GProperty.getDEBUG_CLOUD_ALBUM_SYNC()) {
            GLog.d(TAG, null, () -> "isAlbumBelongToDefaultWildCardDirs, not belong to default, inputBucketPath=" + albumBucketPath);
        }
        return false;
    }

    /**
     * 通过 dirPath 查询同步开关状态
     *
     * @param dirPathList dirPath的列表
     * @return Map<dirPath,switchState>，其中dirPath为小写
     */
    public static Map<String, Integer> getSwitchStateByDirPath(String[] dirPathList) {
        Map<String, Integer> dirPathSwitchStateMap = new HashMap<>();
        if ((dirPathList == null) || (dirPathList.length == 0)) {
            return dirPathSwitchStateMap;
        }
        String selection = getWhereQueryIn(ConfigStore.CloudAllowListColumns.DIR_PATH, dirPathList.length);
        String[] projections = new String[]{
                ConfigStore.CloudAllowListColumns.DIR_PATH,
                ConfigStore.CloudAllowListColumns.SWITCH_STATE
        };
        try (Cursor cursor = CloudSyncProviderUtils.queryDir(projections, selection, dirPathList, null)) {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                int dirPathIndex = cursor.getColumnIndex(ConfigStore.CloudAllowListColumns.DIR_PATH);
                int switchStateIndex = cursor.getColumnIndex(ConfigStore.CloudAllowListColumns.SWITCH_STATE);
                while (cursor.moveToNext()) {
                    String dirPath = cursor.getString(dirPathIndex).toLowerCase();
                    int switchState = cursor.getInt(switchStateIndex);
                    dirPathSwitchStateMap.put(dirPath, switchState);
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, "getSwitchStateByDirPath, failed! error=", e);
        }
        return dirPathSwitchStateMap;
    }
}