/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AcVerifyEnvironment.kt
 ** Description : 核身校验环境参数
 ** Version     : 1.0
 ** Date        : 2024/09/26
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@Apps.Gallery            2024/09/26  1.0          create
 *********************************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync.account.acverify

import com.oplus.gallery.framework.abilities.ocloudsync.account.AccountManager

/**
 * 核身校验环境参数
 *
 * @Note
 * 1. 核身校验SDK及相关业务不存在环境切换，只需与账号环境保持一致即可;
 * 2. 在代码内使用校验系统的场景ID，账号会自动识别并拉起核身引擎的能力，如果使用核身引擎的场景ID，可能遇到超时无法拉起校验页面的问题；
 * 3. 相关文档：https://odocs.myoas.com/docs/zdkyBY9jV5iJaKA6。
 */
object AcVerifyEnvironment {
    /**
     * OPPO开放平台核身校验系统测试环境中申请或配置的参数
     *
     * 依次为：AppID、AppKey、AppSecret、校验系统（核身）场景ID、核身引擎场景ID
     */
    private const val TEST_APP_ID = "********"
    private const val TEST_APP_KEY = "e7da81322178423aa250a858f6d75104"
    private const val TEST_APP_SECRET_KEY = "04be36d43c244d4d8e1845238afb2c5c"
    private const val TEST_BUSINESS_ID = "b07644c513e2425084e0f88252b71191"
    private const val TEST_ENGINE_BUSINESS_ID = "PBg9EZbm2icM1Dp1bShR"

    /**
     * OPPO开放平台核身校验系统正式环境中申请或配置的参数
     *
     * 依次为：AppID、AppKey、AppSecret、校验系统（核身）场景ID、核身引擎场景ID
     */
    private const val RELEASE_APP_ID = "********"
    private const val RELEASE_APP_KEY = "4f3cf53492e7439d94b2cb50e2b77e4e"
    private const val RELEASE_APP_SECRET_KEY = "b55679b1401847af8370678be814bfbb"
    private const val RELEASE_BUSINESS_ID = "e747e197b63546ccbc88f7841f52deeb"
    private const val RELEASE_ENGINE_BUSINESS_ID = "CFiD6y9JgMC7bAmG8sWt"

    /**
     * 核身校验环境参数，与账号环境保持一致 [AccountManager.AccountSDKAccess]
     */
    val environmentParams = if (AccountManager.isTestEnable) EnvironmentParams.TEST else EnvironmentParams.RELEASE

    /**
     *
     * OPPO开放平台核身校验系统中申请或配置的参数
     *
     * @param appId AppID
     * @param appKey AppKey
     * @param appSecretKey AppSecret
     * @param businessId 校验系统（核身）场景ID
     * @param engineBusinessId 核身引擎场景ID
     */
    enum class EnvironmentParams(
        val appId: String,
        val appKey: String,
        val appSecretKey: String,
        val businessId: String,
        val engineBusinessId: String
    ) {
        /** 测试环境 */
        TEST(
            appId = TEST_APP_ID,
            appKey = TEST_APP_KEY,
            appSecretKey = TEST_APP_SECRET_KEY,
            businessId = TEST_BUSINESS_ID,
            engineBusinessId = TEST_ENGINE_BUSINESS_ID
        ),

        /** 正式环境 */
        RELEASE(
            appId = RELEASE_APP_ID,
            appKey = RELEASE_APP_KEY,
            appSecretKey = RELEASE_APP_SECRET_KEY,
            businessId = RELEASE_BUSINESS_ID,
            engineBusinessId = RELEASE_ENGINE_BUSINESS_ID
        )
    }
}