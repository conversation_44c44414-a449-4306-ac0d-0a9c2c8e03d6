/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaFileMetaData.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync.metadata

import android.os.SystemClock
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.oplus.cloudkitlib.base.CloudKitConstants
import com.oplus.cloudkitlib.metadata.AbsMetaData
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.framework.abilities.ocloudsync.OCloudSyncApi
import java.util.Locale

class MediaFileMetaData : AbsMetaData<MediaFileFields> {

    companion object {
        const val DATA_TYPE = CloudKitConstants.METADATA_TYPE_FILE
        const val MODULE = "albumMetadata"
        const val ZONE = "albumMetadata"
        const val RECORD_TYPE = "item_info"

        @JvmStatic
        fun createSysRecordId(filePath: String, md5: String): String {
            return OCloudSyncApi.getMD5(filePath + md5 + SystemClock.currentThreadTimeMillis())
        }

        @JvmStatic
        fun createUniqueId(filePath: String, md5: String): String {
            //新增的文件元数据生成的UniqueId忽略大小写
            val filePathLowercase = filePath.lowercase(Locale.getDefault())
            return OCloudSyncApi.getMD5(filePathLowercase + md5)
        }
    }

    var itemId: Int? = null
    var operation: Int? = null
    var recycle: Boolean? = null
    var oCloudId: String? = null
    var fileCheckPayload: String? = null

    constructor(record: CloudMetaDataRecord) : super(record) {
        customFields = JsonUtil.fromJson(this.fields, MediaFileFields::class.java)
        if ((this.fileInfos != null) && this.fileInfos.isNotEmpty()) {
            oCloudId = this.fileInfos[0].ocloudId
            fileCheckPayload = this.fileInfos[0].fileCheckPayload
        }
    }

    constructor(record: CloudMetaDataRecord, mediaFileFields: MediaFileFields) : super(record, mediaFileFields) {
        fields = JsonUtil.toJson(mediaFileFields)
    }

    override fun toString(): String {
        return "MediaFileMetaData[ itemId: $itemId operation: $operation recycle: $recycle " +
                "oCloudId: $oCloudId fileCheckPayload: $fileCheckPayload ${super.toString()}]"
    }


    /**
     * 获取新旧版本兼容的最后操作时间
     *
     * 服务端：
     * - 1、创建元数据产生sysCreateTime,更新元数据产生sysUpdateTime，原有数据迁移不会产生operationTime数据
     * - 2、operationTime，旧版本：跟sysUpdateTime相同，新版本：客户端上传
     *
     * 返回值：
     * - 1、operationTime数据为空，使用sysCreateTime或者sysUpdateTime
     * - 2、operationTime比sysUpdateTime大（云端是标准的utc时间，本地时间往后调，本地就比云端大了），
     * 则返回sysCreateTime或者sysUpdateTime
     */
    fun getOperationTimeFromCloud(): Long {
        val operationTime = customFields?.operationTime ?: 0
        val lastUpdateTime = if (sysUpdateTime == 0L) {
            sysCreateTime
        } else {
            sysUpdateTime
        }
        return if ((operationTime <= 0L) || (operationTime > lastUpdateTime)) {
            lastUpdateTime
        } else {
            operationTime
        }
    }
}