/***************************************************************************************************
 ** Copyright (C), 2023-2024, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SlimmingCloudParamManager.kt
 ** Description:Obtain slimming configuration thresholds from the cloud.
 ** Version: 1.0
 ** Date : 2023/11/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ------------------------------------
 **  <author>                   <data>         <version >     <desc>
 **  <EMAIL>    2023/11/29     1.0            Add file head
 **
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.cloudsync.slimming

import android.text.TextUtils
import android.text.format.DateUtils
import androidx.annotation.WorkerThread
import com.oplus.gallery.business_lib.http.cloudparameter.CloudParamConfig
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * 云服务瘦身优化：网络后台配置
 */
class SlimmingCloudParamManager {

    /**
     * 使用文件最近使用时间判定是否可以瘦身的时间间隔阈值
     * 当前时间 -_latest_file_usage_time 小于该阈值的图片不会瘦身，也就是"最近"使用的的图片不会瘦身
     */
    private val latestFileUsageThresholdForSlimming: Long by lazy {
        CloudParamConfig.getDefaultGroupValue(
            MODULE_NAME,
            SLIMMING_LAST_FILE_USAGE_TIME_THRESHOLD,
            "",
            CloudParamConfig.GROUP_CLOUD_SERVICE
        ).let {
            if ((it is String) && !TextUtils.isEmpty(it)) {
                GLog.d(TAG, " init, file usage is $it")
                it.toLongOrNull()?.times(DateUtils.DAY_IN_MILLIS) ?: DEFAULT_LATEST_FILE_USAGE
            } else {
                DEFAULT_LATEST_FILE_USAGE
            }
        }
    }

    /**
     * 获取文件最近使用时间瘦身阈值，小于此时间可以进行瘦身
     *
     * @return latestFileUsage阈值 单位ms
     */
    @WorkerThread
    fun getSlimmingLastUsageTimeThreshold(): Long {
        return System.currentTimeMillis() - latestFileUsageThresholdForSlimming
    }

    companion object {
        private const val TAG = "SlimmingCloudParamManager"
        private const val DEFAULT_LATEST_FILE_USAGE = 30 * DateUtils.DAY_IN_MILLIS
        private const val MODULE_NAME = "cloud_slimming"
        private const val SLIMMING_LAST_FILE_USAGE_TIME_THRESHOLD = "last_file_usage_time_threshold"
    }
}