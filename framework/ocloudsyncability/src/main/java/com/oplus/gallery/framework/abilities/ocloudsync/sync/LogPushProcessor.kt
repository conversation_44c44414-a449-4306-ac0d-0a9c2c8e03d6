/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - LogPushProcessor.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/6/16
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/6/16        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync.sync

import android.content.Context
import com.google.gson.Gson
import com.heytap.cloudkit.libcommon.log.CloudKitLogUtil
import com.heytap.cloudkit.libcommon.log.CloudPushLogMsg
import com.heytap.cloudkit.libsync.push.CloudPushMessage
import com.oplus.gallery.business_lib.api.IPushDM
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * cloudkit日志打捞
 */
class LogPushProcessor : IPushDM.IPushDataMessageProcessor {
    override fun process(context: Context, pushMessage: IPushDM.PushMessage): Boolean {
        val content = pushMessage.content
        return if (isLogPushMessage(content)) {
            processLogPushMessage(ContextGetter.context, content)
            true
        } else false
    }

    /**
     * "action":"enable_log_upload"
     */
    private fun isLogPushMessage(content: String): Boolean {
        return content.contains("\"action\":\"" + CloudPushMessage.ACTION_LOG_UPLOAD)
    }

    private fun processLogPushMessage(context: Context, messageContent: String) {
        GLog.d(TAG, "processLogPushMessage")
        val gson = Gson()
        val cloudLogConfigMsg = gson.fromJson(
            messageContent,
            CloudPushLogMsg::class.java
        ).content
        if (CloudKitLogUtil.getCloudKitLogPkg() == cloudLogConfigMsg.tracePkg) {
            AppScope.launch(Dispatchers.IO) {
                CloudKitLogUtil.pushReport(context)
            }
        }
    }

    companion object {
        private const val TAG = "LogPushProcessor"
    }
}