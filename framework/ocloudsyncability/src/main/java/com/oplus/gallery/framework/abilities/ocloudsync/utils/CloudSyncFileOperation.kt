/**************************************************************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * OPLUS_EDIT
 * File: - CloudSyncFileOperation.java
 * Description:
 * Version: 1.0
 * Date : 2023/07/09
 * Author: <EMAIL>
 *
 * ---------------------Revision History: --------------------------------------------------------
 * <author>                      <data>         <version>          <desc>
 * <EMAIL>    2023/07/09        1.0               create
 *
*********************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync.utils

import android.content.ContentValues
import android.content.Context
import com.oplus.gallery.business_lib.api.ApiDmManager.getRecycleBinDM
import com.oplus.gallery.foundation.database.store.CloudSyncStore
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.ocloudsync.CloudSyncStatusManager
import com.oplus.gallery.framework.abilities.ocloudsync.db.CloudSyncFileUtils
import com.oplus.gallery.framework.abilities.ocloudsync.db.LocalSyncDataVisitor
import com.oplus.gallery.framework.abilities.ocloudsync.db.MediaStoreDataVisitor
import com.oplus.gallery.framework.abilities.ocloudsync.metadatasync.ImageFile
import com.oplus.gallery.framework.abilities.ocloudsync.metadatasync.MetadataConflictStrategy.isLocalFileExist
import com.oplus.gallery.standard_lib.scheduler.BatchProcess
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 云同步操作
 * 同步来自云端的操作，包括：
 * 回收站删除，回收站恢复，删除到回收站，正常文件彻底删除
 */
object CloudSyncFileOperation {
    private const val TAG = "CloudSyncFileOperation"
    private const val SYNC_GALLERY_RECYCLE_PAGE_SIZE = 100

    /**
     * 同步来自云端的操作，
     * 包括删除到回收站，回收站恢复，回收站删除，正常文件删除
     * @param context Context
     * @param updateMap 需要同步更新的数据，key-云端数据， value - 本地数据
     * 更新包括：回收站删除，回收站恢复，删除到回收站
     * @param deleteData 需要同步删除的数据，包括：正常文件彻底删除
     */
    @JvmStatic
    internal fun syncOperationFromCloud(
        context: Context,
        updateMap: Map<ImageFile, ImageFile>,
        deleteData: List<ImageFile>
    ) {
        val incrementStartTime = System.currentTimeMillis()
        val recycleMap: MutableMap<String, ImageFile> = mutableMapOf()
        val restoreMap: MutableMap<String, ImageFile> = mutableMapOf()
        val deleteRecycleList: MutableList<ImageFile> = mutableListOf()
        val deleteList: MutableList<ImageFile> = mutableListOf()
        updateMap.forEach { (imageNet, imageDB) ->
            if (imageDB.isLocalFileExist().not()) {
                return@forEach
            }
            val cloudIsRecycle = imageNet.isRecycle()
            val localIsRecycle = imageDB.isRecycle()
            if (localIsRecycle && !cloudIsRecycle) {
                restoreMap[imageNet.mOriginalData] = imageNet
            } else if (!localIsRecycle && cloudIsRecycle) {
                recycleMap[imageNet.mOriginalData] = imageNet
            }
        }
        deleteData.forEach {
            if (it.isRecycle()) {
                deleteRecycleList.add(it)
            } else {
                deleteList.add(it)
            }
        }
        // 同步回收站操作
        syncRecycleOperationRecycle(context, recycleMap, SYNC_GALLERY_RECYCLE_PAGE_SIZE)
        syncRecycleOperationRestore(context, restoreMap, SYNC_GALLERY_RECYCLE_PAGE_SIZE)
        syncRecycleOperationDelete(context, deleteRecycleList, SYNC_GALLERY_RECYCLE_PAGE_SIZE)
        // 同步正常文件彻底删除操作
        MediaStoreDataVisitor.syncDownloadDelete(context, deleteList)
        CloudFileDeletionTrace.getInstance(context).flush()
        GLog.d(TAG, "[syncRecycleOperation], increment sync cost time=" + (System.currentTimeMillis() - incrementStartTime))
    }

    /**
     * 查询数据库获取需要云端同步到本地的操作，并且执行文件同步
     *
     * 一般来说在恢复流程我们已经将云端操作同步到本地，但是我们不能保证所有的操作都已经同步，
     * 如果有文件没有同步，_opration状态没有置为0，那么这个文件在本地的所有操作将不会备份上云，
     * 因此需要在合适的时机去执行一下同步
     */
    @JvmStatic
    fun syncOperationIfNeed(context: Context) {
        val fullSyncStartTime = System.currentTimeMillis()
        // Cancel downloading first
        CloudSyncFileUtils.cancelDownloadingFilesIfDeleted(context)
        val filesNeedToSync = CloudSyncFileUtils.getNeedToSyncOperationFile()
        syncRecycleOperationRecycle(context, filesNeedToSync.mRecycleMap, SYNC_GALLERY_RECYCLE_PAGE_SIZE)
        syncRecycleOperationRestore(context, filesNeedToSync.mRestoreMap, SYNC_GALLERY_RECYCLE_PAGE_SIZE)
        syncRecycleOperationDelete(context, filesNeedToSync.mDeleteRecycleList, SYNC_GALLERY_RECYCLE_PAGE_SIZE)

        // 同步正常文件彻底删除操作
        MediaStoreDataVisitor.syncDownloadDelete(context, filesNeedToSync.mDeleteList)
        updateOperationToNone(filesNeedToSync.mUpdateOperationNoneList)
        CloudFileDeletionTrace.getInstance(context).flush()
        GLog.d(TAG, "[syncRecycleOperation], full sync cost time=" + (System.currentTimeMillis() - fullSyncStartTime))
    }

    @JvmStatic
    private fun recycleByData(dataList: List<String>, packageName: String): List<String>? {
        return getRecycleBinDM().recycleByData(dataList, packageName)
    }

    @JvmStatic
    private fun restoreByData(dataList: List<String>, packageName: String): Map<String, Long>? {
        return getRecycleBinDM().restoreByData(ContextGetter.context, dataList, packageName)
    }

    @JvmStatic
    private fun deleteRecycledByData(dataList: List<String>, packageName: String): List<String>? {
        return getRecycleBinDM().deleteRecycledByData(dataList, packageName)
    }

    @JvmStatic
    private fun syncRecycleOperationRecycle(
        context: Context,
        recycleMap: Map<String, ImageFile>?,
        pageSize: Int
    ) {
        if (recycleMap.isNullOrEmpty()) {
            return
        }
        val failedList = mutableListOf<String>()
        val pathList = recycleMap.keys.toList()
        runCatching {
            GLog.d(TAG, "[syncRecycleOperationRecycle] recycle size=" + pathList.size)
            var count = 0
            var pageNob = 0
            var items = BatchProcess.getPageItems(pathList, pageNob, pageSize)
            while (items.isNotEmpty()) {
                count += items.size
                val resultList = recycleByData(
                    items,
                    CloudSyncStatusManager.getInstance().cloudPackageName
                )
                syncRecycleOperationCheckFailed(resultList, items, failedList)
                pageNob++
                items = BatchProcess.getPageItems(pathList, pageNob, pageSize)
            }
            GLog.d(TAG, "[syncRecycleOperationRecycle] refactor_db_batch count=$count  success=${count == pathList.size}")
        }.onFailure {
            GLog.e(TAG, "[syncRecycleOperationRecycle] Exception=$it")
        }
        updateAfterRecycle(recycleMap, pathList, failedList)
        CloudFileDeletionTrace.getInstance(context).submitTraceRequest(
            pathList,
            CloudFileDeletionTrace.TYPE_RECYCLE_INSERT,
            CloudFileDeletionTrace.DESCRIBE_RECYCLE_INSERT_RECOVERY_RECYCLE, true
        )
    }

    @JvmStatic
    private fun updateAfterRecycle(
        recycleMap: Map<String, ImageFile>,
        updateKeys: List<String>,
        failedList: List<String>
    ) {
        updateKeys.forEach {
            if (!failedList.contains(it)) {
                val imageFile = recycleMap[it] ?: return@forEach
                val values = imageFile.makeUpdateGalleryRecycleValues()
                values.put(GalleryStore.GalleryColumns.CloudColumns.OPERATION, CloudSyncStore.Operation.OPERATION_NONE)
                values.put(
                    GalleryStore.GalleryColumns.CloudColumns.FILE_DOWNLOAD_STATUS,
                    CloudSyncStore.FileDownloadStatus.FILE_DOWNLOAD_DOWNLOADED
                )
                LocalSyncDataVisitor.updateByOriginalData(values, imageFile.mOriginalData).also {
                    GLog.d(TAG, "[updateAfterRecycle] update $it")
                }
            }
        }
    }

    @JvmStatic
    private fun syncRecycleOperationRestore(
        context: Context,
        restoreMap: Map<String, ImageFile>?,
        pageSize: Int
    ) {
        if (restoreMap.isNullOrEmpty()) {
            return
        }
        val failedList = mutableListOf<String>()
        val pathList = restoreMap.keys.toList()
        GLog.d(TAG, "[syncRecycleOperationRestore] restore size=" + pathList.size)
        val resultMap = mutableMapOf<String, Long>()
        runCatching {
            var count = 0
            var pageNob = 0
            var items: List<String> = BatchProcess.getPageItems(pathList, pageNob, pageSize)
            while (items.isNotEmpty()) {
                count += items.size
                val map = restoreByData(
                    items,
                    CloudSyncStatusManager.getInstance().cloudPackageName
                )
                if (map != null && map.isNotEmpty()) {
                    resultMap.putAll(map)
                }
                pageNob++
                items = BatchProcess.getPageItems(pathList, pageNob, pageSize)
            }
            GLog.d(TAG) {
                "[syncRecycleOperationRestore] refactor_db_batch count=$count success=${count == pathList.size}"
            }
        }.onFailure {
            GLog.e(TAG, "[syncRecycleOperationRestore] Exception=$it")
        }
        updateAfterRestore(restoreMap, resultMap, failedList)
        CloudFileDeletionTrace.getInstance(context).submitTraceRequest(
            pathList,
            CloudFileDeletionTrace.TYPE_RECYCLE_DELETE,
            CloudFileDeletionTrace.DESCRIBE_RECYCLE_DELETE_RECOVERY_RESTORE, true
        )
    }

    @JvmStatic
    private fun updateAfterRestore(
        restoreMap: Map<String, ImageFile>,
        resultMap: MutableMap<String, Long>,
        failedList: MutableList<String>
    ) {
        for (file in restoreMap.values) {
            //Update operation after sync end.
            val id = resultMap[file.mOriginalData]
            if (id != null) {
                val values = ContentValues()
                values.put(GalleryStore.GalleryColumns.CloudColumns.OPERATION, CloudSyncStore.Operation.OPERATION_NONE)
                values.put(
                    GalleryStore.GalleryColumns.CloudColumns.FILE_DOWNLOAD_STATUS,
                    CloudSyncStore.FileDownloadStatus.FILE_DOWNLOAD_DOWNLOADED
                )
                values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, id)
                LocalSyncDataVisitor.updateByOriginalData(values, file.mOriginalData).also {
                    GLog.d(TAG, "[updateAfterRestore] update $it")
                }
                continue
            }
            failedList.add(file.mOriginalData)
        }
    }

    @JvmStatic
    private fun syncRecycleOperationDelete(
        context: Context,
        deleteImageFiles: List<ImageFile>?,
        pageSize: Int
    ) {
        if (deleteImageFiles.isNullOrEmpty()) {
            return
        }
        val failedList = mutableListOf<String>()
        runCatching {
            val allCount = deleteImageFiles.size
            GLog.d(TAG, "[syncRecycleOperationDelete] delete size=$allCount")
            var count = 0
            var pageNob = 0
            val deleteFilePaths = deleteImageFiles.map { it.mOriginalData }.toList()
            var items = BatchProcess.getPageItems(deleteFilePaths, pageNob, pageSize)
            while (items.isNotEmpty()) {
                count += items.size
                val resultList = deleteRecycledByData(
                    items,
                    CloudSyncStatusManager.getInstance().cloudPackageName
                )
                syncRecycleOperationCheckFailed(resultList, items, failedList)
                pageNob++
                items = BatchProcess.getPageItems(deleteFilePaths, pageNob, pageSize)
            }
            GLog.d(TAG, "[syncRecycleOperationDelete] refactor_db_batch count=$count success=${count == allCount}")
        }.onFailure {
            GLog.e(TAG, "[syncRecycleOperationDelete] Exception=$it")
        }
        val successImageFiles = mutableListOf<ImageFile>()
        deleteImageFiles.forEach {
            if (it.isLocalFileExist().not() || failedList.contains(it.mOriginalData).not()) {
                successImageFiles.add(it)
            }
        }
        LocalSyncDataVisitor.batchDeleteByImageFiles(successImageFiles)
        CloudFileDeletionTrace.getInstance(context).submitTraceRequestByImage(
            deleteImageFiles,
            CloudFileDeletionTrace.TYPE_RECYCLE_DELETE,
            CloudFileDeletionTrace.DESCRIBE_RECYCLE_DELETE_RECOVERY_DELETE, true
        )
    }

    @JvmStatic
    private fun syncRecycleOperationCheckFailed(
        resultList: List<String>?,
        items: List<String>?,
        failedList: MutableList<String>
    ) {
        if (!resultList.isNullOrEmpty() && (items != null) && (resultList.size != items.size)) {
            for (data in items) {
                if (!resultList.contains(data)) {
                    failedList.add(data)
                }
            }
        }
    }

    @JvmStatic
    private fun updateOperationToNone(updateList: List<ImageFile>) {
        val contentValues = ContentValues().apply {
            put(GalleryStore.GalleryColumns.CloudColumns.OPERATION, CloudSyncStore.Operation.OPERATION_NONE)
        }
        updateList.forEach {
            LocalSyncDataVisitor.updateById(contentValues, it.mLocalId)
        }
    }
}