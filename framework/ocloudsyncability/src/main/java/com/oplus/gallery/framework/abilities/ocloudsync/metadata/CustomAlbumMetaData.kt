/**************************************************************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CustomAlbumMetaData.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2021/12/21
 ** Author: PI.<PERSON>ali<PERSON> @Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  PI.Jialin@Apps.Gallery3D    2021/12/21    1.0              build this module
 *************************************************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync.metadata

import android.text.TextUtils
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.heytap.usercenter.accountsdk.utils.GsonUtil
import com.oplus.cloudkitlib.metadata.AbsMetaData
import com.oplus.cloudkitlib.base.CloudKitConstants
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.platform.usercenter.tools.algorithm.MD5Util

class CustomAlbumMetaData : AbsMetaData<CustomAlbumFields> {

    companion object {
        const val DATA_TYPE = CloudKitConstants.METADATA_TYPE_TEXT
        const val MODULE = "albumDirInfo"
        const val ZONE = "albumDirInfo"
        const val RECORD_TYPE = "dir_info"
    }

    var configStatus: Int? = null

    var itemId: Int? = null
    var operation: Int? = null

    constructor(record: CloudMetaDataRecord) : super(record) {
        customFields = JsonUtil.fromJson(this.fields, CustomAlbumFields::class.java)
    }

    constructor(record: CloudMetaDataRecord, customAlbumFields: CustomAlbumFields) : super(record, customAlbumFields) {
        customFields?.apply {
            when {
                TextUtils.isEmpty(sysRecordId) -> {
                    sysRecordId = MD5Util.md5Hex(dirPaths + dirName + System.currentTimeMillis())
                    operatorType = CloudKitConstants.OPERATOR_TYPE_CREATE
                }
                sysVersion == 0L -> operatorType = CloudKitConstants.OPERATOR_TYPE_CREATE
                else -> operatorType = CloudKitConstants.OPERATOR_TYPE_REPLACE
            }
            sysDataType = DATA_TYPE
            sysRecordType = RECORD_TYPE
            sysProtocolVersion = 0
            fields = GsonUtil.toJson(customFields)
        }
    }
}