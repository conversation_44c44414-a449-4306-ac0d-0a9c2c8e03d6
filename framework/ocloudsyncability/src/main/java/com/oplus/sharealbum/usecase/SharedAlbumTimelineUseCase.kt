/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumTimelineUseCase
 ** Description:
 ** Version: 1.0
 ** Date : 2022/7/25
 ** Author: chenjie@A<PERSON>.KeKeCloud
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                          <data>         <version >     <desc>
 **  <EMAIL>            2022/7/25        1.0             Add
 **************************************************************************************************/

package com.oplus.sharealbum.usecase

import com.oplus.sharealbum.bean.QueryItemRequest
import com.oplus.sharealbum.bean.QueryItemResponse
import com.oplus.sharealbum.net.ApiResponse
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.net.callExt
import com.oplus.sharealbum.service.ISharedAlbumService

/**
 * 共享图集列表网络数据加载
 */
class SharedAlbumTimelineUseCase {

    fun fetch(albumId: String, index: Int, pageSession: String): ApiResponse<QueryItemResponse> {
        val request = QueryItemRequest(albumId, pageSession, index)
        val service = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
        return service.queryItem(request).callExt()
    }
}