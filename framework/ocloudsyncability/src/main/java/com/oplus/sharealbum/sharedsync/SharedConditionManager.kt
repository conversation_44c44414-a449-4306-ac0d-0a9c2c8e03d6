/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedConditionManager
 ** Description:SharedConditionManager
 ** Version: 1.0
 ** Date : 2022/7/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/7/30        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.sharedsync

import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.gallery.business_lib.cloudsync.SyncResultConstants
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.permission.RuntimePermissionUtils
import com.oplus.gallery.framework.abilities.ocloudsync.CloudSyncStatusManager
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 共享图集检测是否可以调用同步相关
 */
object SharedConditionManager {
    private const val TAG = "SharedConditionManager"

    @JvmStatic
    fun checkState(): SyncResult {
        if (permissionCheck().not()) {
            GLog.w(TAG, "checkState, permissionCheck=false")
            return SyncResult(false, SyncResultConstants.RESULT_PERMISSION_DENIED)
        }
        if (!CloudSyncStatusManager.getInstance().isSharedAlbumEnable) {
            GLog.w(TAG, "checkState, isSharedAlbumEnable=false")
            return SyncResult(false, SyncResultConstants.RESULT_NOT_LOGIN_OR_SWITCH_CLOSE)
        }

        if (NetworkPermissionManager.isUseOpenNetwork.not()) {
            GLog.w(TAG, "checkState, isUseOpenNetwork=false")
            return SyncResult(false, SyncResultConstants.RESULT_NO_NETWORK_PERMISSION)
        }
        return SyncResult(true, SyncResultConstants.RESULT_SUCCESS)
    }

    @JvmStatic
    private fun permissionCheck(): Boolean {
        if (!CloudSyncStatusManager.getInstance().isShareAlbumAuthorized) {
            GLog.d(TAG, "permissionCheck, shared sync not authorized, return false")
            return false
        }

        if (!RuntimePermissionUtils.isNecessaryPermissionGranted(ContextGetter.context)) {
            GLog.d(TAG, "permissionCheck, don't have storage permission, return false")
            return false
        }
        if (RuntimePermissionUtils.isAndroidRAndNoManageExternalStorageCanNotUserGallery()) {
            GLog.d(TAG, "permissionCheck, don't have hasManageExternalStorage, return false")
            return false
        }
        return true
    }
}