/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ChangeSharedAlbumAttributeRequest
 ** Description:
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/30        1.0             Add
 **************************************************************************************************/

package com.oplus.sharealbum.bean

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.util.text.TextUtil

@Keep
data class ChangeSharedAlbumAttributeRequest(
    @SerializedName("atlasId") var albumId: String = TextUtil.EMPTY_STRING,
    @SerializedName("atlasName") var albumName: String = TextUtil.EMPTY_STRING
) : SharedAlbumRequest()