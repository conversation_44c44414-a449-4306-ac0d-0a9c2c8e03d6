/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ISharedAlbumService
 ** Description:ISharedAlbumService
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/30        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.service

import io.reactivex.Observable
import com.oplus.sharealbum.bean.SharedAlbumRequest
import com.oplus.sharealbum.bean.AcceptInviteRequest
import com.oplus.sharealbum.bean.AcceptInviteResponse
import com.oplus.sharealbum.bean.AddInviteeRequest
import com.oplus.sharealbum.bean.AddInviteeResponse
import com.oplus.sharealbum.bean.CancelInviteeRequest
import com.oplus.sharealbum.bean.CancelInviteeResponse
import com.oplus.sharealbum.bean.DeleteInviteeRequest
import com.oplus.sharealbum.bean.DeleteInviteeResponse
import com.oplus.sharealbum.bean.QueryInviteMsgRequest
import com.oplus.sharealbum.bean.QueryInviteMsgResponse
import com.oplus.sharealbum.bean.QueryMemberRequest
import com.oplus.sharealbum.bean.QueryMemberResponse
import com.oplus.sharealbum.bean.RefuseInviteRequest
import com.oplus.sharealbum.bean.RefuseInviteResponse
import com.oplus.sharealbum.bean.SharedAlbumAddItemRequest
import com.oplus.sharealbum.bean.SharedAlbumAddItemResponse
import com.oplus.sharealbum.bean.SharedAlbumDeleteItemRequest
import com.oplus.sharealbum.bean.SharedAlbumDeleteItemResponse
import com.oplus.sharealbum.bean.NewMsgResponse
import com.oplus.sharealbum.bean.QuerySharedAlbumListRequest
import com.oplus.sharealbum.bean.QuerySharedAlbumListResponse
import com.oplus.sharealbum.bean.SharedAlbumConfigResponse
import com.oplus.sharealbum.bean.SharedAlbumOnlineResponse
import com.oplus.sharealbum.bean.CreateSharedAlbumRequest
import com.oplus.sharealbum.bean.CreateSharedAlbumResponse
import com.oplus.sharealbum.bean.ChangeSharedAlbumAttributeRequest
import com.oplus.sharealbum.bean.ChangeSharedAlbumAttributeResponse
import com.oplus.sharealbum.bean.DeleteSharedAlbumRequest
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.sharealbum.bean.QuitSharedAlbumRequest
import com.oplus.sharealbum.bean.ReportActivityReadLastRequest
import com.oplus.sharealbum.bean.ReportActivityReadLastResponse
import com.oplus.sharealbum.bean.QueryActivityResponse
import com.oplus.sharealbum.bean.SharedToWxRequest
import com.oplus.sharealbum.bean.ShareToWxResponse
import com.oplus.sharealbum.net.ApiResponse
import retrofit2.Call
import com.oplus.sharealbum.bean.QueryItemRequest
import com.oplus.sharealbum.bean.QueryItemResponse
import retrofit2.http.Body
import retrofit2.http.POST

interface ISharedAlbumService {

    /**
     * 图集元数据上传
     */
    @POST("shared-atlas/v1/addItem")
    fun addItem(@Body reqSharedAlbum: SharedAlbumAddItemRequest?): Observable<ApiResponse<SharedAlbumAddItemResponse>>

    /**
     * 图集元数据删除
     */
    @POST("shared-atlas/v1/deleteItem")
    fun deleteItem(@Body reqSharedAlbum: SharedAlbumDeleteItemRequest?): Observable<ApiResponse<SharedAlbumDeleteItemResponse>>

    /**
     * 根据用户查询所有图集(创建的和参与的)
     */
    @POST("shared-atlas/v1/queryAtlas")
    fun querySharedAlbumList(@Body req: QuerySharedAlbumListRequest?): Call<ApiResponse<QuerySharedAlbumListResponse>>

    /**
     * 获取静态配置
     */
    @POST("shared-atlas/v1/getConfig")
    fun getConfig(@Body req: SharedAlbumRequest): Call<ApiResponse<SharedAlbumConfigResponse>>

    /**
     * 共享图集功能是否在线
     */
    @POST("shared-atlas/v1/isOnLine")
    fun isOnline(@Body req: SharedAlbumRequest): Call<ApiResponse<SharedAlbumOnlineResponse>>

    /**
     * 创建图集
     */
    @POST("shared-atlas/v1/createAtlas")
    fun createSharedAlbum(@Body req: CreateSharedAlbumRequest): Call<ApiResponse<CreateSharedAlbumResponse>>

    /**
     * 变更图集属性，现仅支持变更图集名称(重命名)
     */
    @POST("shared-atlas/v1/changeAttribute")
    fun changeSharedAlbumAttribute(@Body req: ChangeSharedAlbumAttributeRequest): Call<ApiResponse<ChangeSharedAlbumAttributeResponse>>

    /**
     * 删除指定共享图集
     */
    @POST("shared-atlas/v1/deleteAtlas")
    fun deleteSharedAlbum(@Body req: DeleteSharedAlbumRequest): Call<ApiResponse<SharedAlbumResponse>>

    /**
     * 退出共享图集
     */
    @POST("shared-atlas/v1/quit")
    fun exitSharedAlbum(@Body sharedAlbumReq: QuitSharedAlbumRequest): Call<ApiResponse<ChangeSharedAlbumAttributeResponse>>

    /**
     * 是否有新消息
     */
    @POST("shared-atlas/v1/hasNewMsg")
    fun hasNewMsg(@Body req: SharedAlbumRequest): Call<ApiResponse<NewMsgResponse>>

    /**
     * 消息上报已读
     */
    @POST("shared-atlas/v1/activity/reportActivityReadLast")
    fun reportActivityReadLast(@Body req: ReportActivityReadLastRequest): Call<ApiResponse<ReportActivityReadLastResponse>>

    /**
     * 获取用户所属图集的所有未读动态(不足n条用已读补足)
     */
    @POST("shared-atlas/v1/activity/queryActivity")
    fun queryAllActivity(@Body req: SharedAlbumRequest): Call<ApiResponse<QueryActivityResponse>>

    /**
     * 查询指定用户未处理的邀请信息
     */
    @POST("shared-atlas/v1/queryUnprocessInviteMsg")
    fun queryUnprocessedInviteMsg(@Body req: SharedAlbumRequest): Call<ApiResponse<QueryActivityResponse>>

    /**
     * 获取成员列表
     */
    @POST("shared-atlas/v1/queryMember")
    fun queryMember(@Body req: QueryMemberRequest): Call<ApiResponse<QueryMemberResponse>>

    /**
     * 获取邀请信息
     */
    @POST("shared-atlas/v1/queryInviteMsg")
    fun queryInviteMsg(@Body req: QueryInviteMsgRequest): Call<ApiResponse<QueryInviteMsgResponse>>

    /**
     * 邀请成员
     */
    @POST("shared-atlas/v1/addInvitee")
    fun addInvitee(@Body req: AddInviteeRequest): Call<ApiResponse<AddInviteeResponse>>

    /**
     * 踢出成员
     */
    @POST("shared-atlas/v1/deleteInvitee")
    fun deleteInvitee(@Body req: DeleteInviteeRequest): Call<ApiResponse<DeleteInviteeResponse>>

    /**
     * 取消邀请
     */
    @POST("shared-atlas/v1/cancelInvitee")
    fun cancelInvitee(@Body req: CancelInviteeRequest): Call<ApiResponse<CancelInviteeResponse>>

    /**
     * 成员接受邀请
     */
    @POST("shared-atlas/v1/acceptInvite")
    fun acceptInvite(@Body req: AcceptInviteRequest): Call<ApiResponse<AcceptInviteResponse>>

    /**
     * 成员拒绝邀请
     */
    @POST("shared-atlas/v1/refuseInvite")
    fun refuseInvite(@Body req: RefuseInviteRequest): Call<ApiResponse<RefuseInviteResponse>>

    /**
     * 获取微信邀请链接
     */
    @POST("shared-atlas/v1/shareToWx")
    fun shareToWx(@Body req: SharedToWxRequest): Call<ApiResponse<ShareToWxResponse>>

    /**
     * 查询图集元数据
     */
    @POST("shared-atlas/v1/queryItem")
    fun queryItem(@Body req: QueryItemRequest): Call<ApiResponse<QueryItemResponse>>
}