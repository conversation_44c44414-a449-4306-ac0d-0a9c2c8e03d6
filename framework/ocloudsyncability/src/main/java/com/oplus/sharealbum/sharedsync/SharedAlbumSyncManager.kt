/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumSyncManager
 ** Description:SharedAlbumSyncManager
 ** Version: 1.0
 ** Date : 2022/3/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/3/24        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.sharedsync

import android.net.Uri
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import com.heytap.cloudkit.libcommon.netrequest.CloudHttpStatusCode
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.cloudkitlib.base.ITransferFilesCallBack
import com.oplus.gallery.addon.osense.LongBgTaskQuotaManager
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.sharealbum.bean.SharedAlbumExtraInfo
import com.oplus.sharealbum.bean.SharedInfo
import com.oplus.sharealbum.helper.SharedDataProviderUtils
import com.oplus.sharealbum.state.CloudKitSharedState
import com.oplus.sharealbum.constant.SharedSyncConstant.MAX_ERROR_COUNT
import com.oplus.sharealbum.sharedsync.file.SharedFileIOManager
import com.oplus.sharealbum.sharedsync.metadata.SharedAlbumMetaDataSync

class SharedAlbumSyncManager : SharedAlbumMetaDataSync() {

    companion object {
        private const val TAG = "SharedAlbumSyncManager"

        @Volatile
        var isStop = true
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            SharedAlbumSyncManager()
        }
    }

    private val metaDataHandler: Handler
    private var needUploadIOData: List<SharedAlbumEntity> = emptyList()

    init {
        val ht = HandlerThread(TAG)
        ht.start()
        metaDataHandler = SharedMetaDataHandler(ht.looper)
    }


    fun sync() {
        GLog.d(TAG, "sync")
        val checkState = SharedConditionManager.checkState()
        if (!checkState.isSuccess) {
            GLog.w(TAG, "sync checkCondition failed")
            return
        }
        metaDataHandler.sendEmptyMessage(0)
    }

    private fun backup() {
        if (!SharedDataProviderUtils.hasSharedDirtyData()) {
            GLog.d(TAG, "backup hasNoDataUnUpload")
            return
        }
        try {
            isStop = false
            /*db按照时间和图集id获取待上传的元数据
            获取所有的图集列表db遍历*/
            val allSharedAlbums = onGetAllAlbumGroup()
            if (isStop) {
                GLog.d(TAG, "backup after onGetAllAlbumGroup isStop")
                return
            }

            allSharedAlbums.forEach {
                if (isStop) {
                    GLog.d(TAG, "backup allSharedAlbums forEach isStop")
                    return
                }
                updateSyncState(it, true, CloudKitSharedState.METADATA_START)
                // db获取待删除的数据
                val needDeleteData = onGetBackupDeleteList(it)
                // 元数据删除
                if (needDeleteData.isNotEmpty()) {
                    onMetaDataBackupDelete(it, needDeleteData)
                }
                if (isStop) {
                    updateSyncState(it, false, CloudKitSharedState.METADATA_END)
                    GLog.d(TAG, "backup after onGetBackupDeleteList isStop")
                    return
                }
                val onGetBackupAddList = onGetBackupAddList(it)
                metaDataAdd(it, onGetBackupAddList)
                updateSyncState(it, false, CloudKitSharedState.METADATA_END)
            }
        } finally {
            onBackUpEnd(!isStop)
        }
    }

    private fun onBackUpEnd(isSuccess: Boolean) {
        if (!isSuccess) {
            // 停止同步
            GLog.d(TAG, "onBackUpEnd isStop")
            return
        }
        uploadFiles()
    }

    private fun uploadFiles() {
        // 获取待上传的文件
        needUploadIOData = SharedDataProviderUtils.getNeedUploadIOData()
        if (needUploadIOData.isEmpty()) {
            GLog.d(TAG, "uploadFiles getNeedUploadIOData is empty")
            return
        }
        if (isStop) {
            // 停止同步
            GLog.d(TAG, "uploadFiles after getNeedUploadIOData isStop")
            return
        }
        updateSyncState(needUploadIOData[0].albumId, true, CloudKitSharedState.IO_UPLOAD)
        // 进行IO上传
        SharedFileIOManager.instance.uploadSharedFiles(needUploadIOData, object :
            ITransferFilesCallBack {
            override fun batchResult(
                successFileList: List<CloudIOFile>,
                errorFileList: List<CloudIOErrorFile<CloudIOFile>>,
                cloudDateType: CloudDataType
            ) {
                /*一批结束 处理
                1.检测是否空间已满 空间已满不需要触发下一次*/
                if (errorFileList.isNotEmpty()) {
                    errorFileList.forEach {
                        if (it.customCloudKitError.cloudKitError.subServerErrorCode == CloudHttpStatusCode.BizFocusServerCode.HTTP_NO_CLOUD_SPACE) {
                            val info = JsonUtil.fromJson(it.cloudIOFile.shareInfo, SharedInfo::class.java)
                            val ioData = JsonUtil.fromJson(info?.ioData, SharedInfo.IOData::class.java)
                            SharedDataProviderUtils.decreasePriority(ioData?.groupId)
                            updateSyncState(needUploadIOData[0].albumId, false, CloudKitSharedState.SPACE_NOT_ENOUGH)
                            return
                        }
                        if (it.customCloudKitError.cloudKitError.bizErrorCode == CloudBizError.NETWORK.code) {
                            updateSyncState(needUploadIOData[0].albumId, false, CloudKitSharedState.SHARED_NETWORK_ERROR)
                            return
                        }
                        if (it.customCloudKitError.cloudKitError.bizErrorCode == CloudBizError.NO_FIND_LOCAL_FILE.code) {
                            Uri.parse(it.cloudIOFile.fileUri).path?.let { filePath ->
                                SharedDataProviderUtils.clearHasDeleteData(filePath)
                            }
                        }
                    }
                }

                updateSyncState(needUploadIOData[0].albumId, false, CloudKitSharedState.IO_UPLOAD)
                if (isStop) {
                    // 停止同步
                    GLog.d(TAG, "uploadFiles after uploadSharedFiles isStop")
                    return
                }
                // 2.否则触发检测是否有数据未上传，进行下一次同步
                if (successFileList.isNotEmpty()) {
                    GLog.d(TAG, "uploadIOFiles batchResult next sync")
                    sync()
                }
            }

            override fun onIOResult(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                // 单个处理
                if (cloudKitError.subServerErrorCode == CloudHttpStatusCode.BizFocusServerCode.HTTP_NO_CLOUD_SPACE) {
                    GLog.w(TAG, "onIOResult file ${PathMask.mask(file.filePath)} cloud space is not enough")
                    return
                }
                val extra = JsonUtil.fromJson(file.extra, SharedAlbumExtraInfo::class.java)
                if (extra == null) {
                    GLog.e(TAG, "onIOResult file ${PathMask.mask(file.filePath)} extra is null")
                    return
                }
                if (cloudKitError.isSuccess) {
                    // 如果成功，插入fileId和io空间校验字段
                    SharedDataProviderUtils.updateFileIdAndExtra(extra.itemId, file.cloudId, file.checkPayload)
                } else {
                    // 如果是失败，记录失败次数，达到3次就变成审核没有通过样式
                    val failCount = SharedDataProviderUtils.getFailCountByItem(extra.itemId)
                    if (failCount < MAX_ERROR_COUNT) {
                        // 更新失败次数
                        SharedDataProviderUtils.updateFailCount(extra.itemId, failCount + 1)
                    } else {
                        // 失败次数超过3次 保存审核未通过状态
                        SharedDataProviderUtils.updateCheckStatus(extra.itemId, GalleryStore.SharedMediaColumns.FILE_OTHER_ABNORMAL)
                    }
                }
            }

            override fun onProgress(file: CloudIOFile, cloudDataType: CloudDataType, progress: Double) {
                // do nothing
            }
        })
    }

    private fun metaDataAdd(albumId: String, data: MutableMap<Long, List<SharedAlbumEntity>>) {
        data.forEach { (time, uploadData) ->
            onMetaDataBackupAdd(albumId, time, uploadData)
        }
    }


    fun stopSync() {
        if (!isStop) {
            isStop = true
            SharedFileIOManager.instance.stopUploadSharedFiles(needUploadIOData)
            metaDataHandler.removeCallbacksAndMessages(null)
        }
    }

    inner class SharedMetaDataHandler(looper: Looper) : Handler(looper) {
        override fun handleMessage(msg: Message) {
            LongBgTaskQuotaManager.add(ContextGetter.context, "$TAG->SharedMetaDataHandler.handleMessage")
            backup()
            LongBgTaskQuotaManager.remove(ContextGetter.context, "$TAG->SharedMetaDataHandler.handleMessage")
        }
    }
}