/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedDataProviderUtils
 ** Description:SharedDataProviderUtils
 ** Version: 1.0
 ** Date : 2022/3/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/3/26        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.helper

import android.content.ContentValues
import android.database.Cursor
import com.oplus.gallery.business_lib.model.data.utils.BaseProviderUtils
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.SharedAlbumColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.SharedMediaColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.SharedMediaColumns.FILE_OTHER_ABNORMAL
import com.oplus.gallery.foundation.database.store.GalleryStore.SharedMediaColumns.FILE_SIZE_BEYOND
import com.oplus.gallery.foundation.database.util.ConstantUtils.AND
import com.oplus.gallery.foundation.database.util.ConstantUtils.ASC
import com.oplus.gallery.foundation.database.util.ConstantUtils.COMMA
import com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL
import com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_TO
import com.oplus.gallery.foundation.database.util.ConstantUtils.FROM
import com.oplus.gallery.foundation.database.util.ConstantUtils.IS_NOT_NULL
import com.oplus.gallery.foundation.database.util.ConstantUtils.IS_NULL
import com.oplus.gallery.foundation.database.util.ConstantUtils.LEFT_BRACKETS
import com.oplus.gallery.foundation.database.util.ConstantUtils.LIMIT
import com.oplus.gallery.foundation.database.util.ConstantUtils.NOT_EQUAL
import com.oplus.gallery.foundation.database.util.ConstantUtils.OR
import com.oplus.gallery.foundation.database.util.ConstantUtils.ORDER_ASC
import com.oplus.gallery.foundation.database.util.ConstantUtils.ORDER_BY
import com.oplus.gallery.foundation.database.util.ConstantUtils.RIGHT_BRACKETS
import com.oplus.gallery.foundation.database.util.ConstantUtils.SELECT
import com.oplus.gallery.foundation.database.util.ConstantUtils.WHERE
import com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_ONE
import com.oplus.gallery.foundation.dbaccess.convert.ContentValuesConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.util.DatabaseUtils

object SharedDataProviderUtils : BaseProviderUtils() {
    private const val TAG = "SharedDataProviderUtils"

    /**
     * 是否有脏数据
     */
    @JvmStatic
    fun hasSharedDirtyData(): Boolean {
        val needDeleteData = getAllNeedDeleteData()
        if (needDeleteData.isEmpty()) {
            val needUploadData = getNeedUploadData()
            return needUploadData.isNotEmpty()
        }

        return true
    }

    /**
     * 是否有待删除的数据
     */
    @JvmStatic
    private fun getAllNeedDeleteData(): List<SharedAlbumEntity> {
        val selection =
            SharedMediaColumns.DELETE + EQUAL_ONE + AND +
                    SharedMediaColumns.FILE_ID + IS_NOT_NULL + AND +
                    SharedMediaColumns.FILE_CHECK + NOT_EQUAL + FILE_SIZE_BEYOND
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                null, selection, null, null
            )
        return SharedAlbumEntity.getSharedAlbumEntities(cursor)
    }

    /**
     * 获取待删除的数据
     */
    @JvmStatic
    fun getNeedDeleteDataById(albumId: String): List<SharedAlbumEntity> {
        val selection =
            SharedMediaColumns.ALBUM_ID + EQUAL_TO + AND +
                    SharedMediaColumns.GLOBAL_ID + IS_NOT_NULL + AND +
                    SharedMediaColumns.DELETE + EQUAL_ONE
        val selectionArgs = arrayOf(albumId)
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                null, selection, selectionArgs, null
            )
        return SharedAlbumEntity.getSharedAlbumEntities(cursor)
    }

    /**
     * 获取待上传的文件
     */
    @JvmStatic
    private fun getNeedUploadData(): List<SharedAlbumEntity> {
        val selection =
            SharedMediaColumns.ALBUM_ID + IS_NULL + OR +
                    SharedMediaColumns.GLOBAL_ID + IS_NULL + AND +
                    SharedMediaColumns.FILE_CHECK + NOT_EQUAL + FILE_SIZE_BEYOND
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                null, selection, null, null
            )
        return SharedAlbumEntity.getSharedAlbumEntities(cursor)
    }

    /**
     * 通过遍历获取的图集id 获取当前创建时间的数据
     */
    @JvmStatic
    fun getNeedUploadMetaData(albumId: String, time: Long): List<SharedAlbumEntity> {
        val selection =
            SharedMediaColumns.ALBUM_ID + EQUAL_TO + AND +
                    SharedMediaColumns.CREATE_TIME + EQUAL_TO + AND +
                    SharedMediaColumns.GLOBAL_ID + IS_NULL + AND +
                    SharedMediaColumns.FILE_CHECK + NOT_EQUAL + FILE_SIZE_BEYOND + AND +
                    SharedMediaColumns.FILE_CHECK + NOT_EQUAL + FILE_OTHER_ABNORMAL
        val selectionArgs = arrayOf(albumId, time.toString())
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                null, selection, selectionArgs, null
            )
        return SharedAlbumEntity.getSharedAlbumEntities(cursor)
    }

    /**
     * 获取当前优先级最高的一批IO数据
     */
    @JvmStatic
    fun getNeedUploadIOData(): List<SharedAlbumEntity> {
        val allSharedAlbums = getAllSharedAlbumsByPriority()
        allSharedAlbums.forEach {
            val ioDataById = getIODataById(it)
            if (ioDataById.isNotEmpty()) {
                return ioDataById
            }
        }
        return emptyList()
    }

    /**
     * 获取该图集优先级最高的一批IO上传数据
     */
    @JvmStatic
    private fun getIODataById(albumId: String): List<SharedAlbumEntity> {
        val ioUploadSelection =
            SharedMediaColumns.FILE_ID + IS_NULL + AND +
                    SharedMediaColumns.FILE_CHECK + NOT_EQUAL + FILE_SIZE_BEYOND + AND +
                    SharedMediaColumns.FILE_CHECK + NOT_EQUAL + FILE_OTHER_ABNORMAL
        /* 这里需要获取最早的一批数据，获取数据是从图集里按创建时间，且数据必须要是fileId不为空，且文件审核状态不为超过大小限制和不为网络、服务
        异常上传失败，而且有且只需要一批。*/
        val selection =
            SharedMediaColumns.ALBUM_ID + EQUAL_TO + AND +
                    SharedMediaColumns.CREATE_TIME + EQUAL + LEFT_BRACKETS + SELECT +
                    SharedMediaColumns.CREATE_TIME + FROM + GalleryStore.SharedMedia.TAB + WHERE +
                    SharedMediaColumns.ALBUM_ID + EQUAL_TO + AND +
                    ioUploadSelection + ORDER_BY +
                    SharedMediaColumns.CREATE_TIME + ORDER_ASC + LIMIT + 1 + RIGHT_BRACKETS + AND +
                    ioUploadSelection
        val selectionArgs = arrayOf(albumId, albumId)
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                null, selection, selectionArgs, null
            )
        return SharedAlbumEntity.getSharedAlbumEntities(cursor)
    }

    /**
     * 获取所有的图集，图集id，按照时间升序排列
     */
    @JvmStatic
    fun getAllSharedAlbums(): List<String> {
        val projection = arrayOf(SharedAlbumColumns.ALBUM_ID)
        val order = SharedAlbumColumns.SAVE_TIME + ASC
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_ALBUM,
                projection, null, null, order
            )
        return getAlbumIdListByCursor(cursor)
    }

    /**
     * 获取所有的图集，图集id，按照优先级排列
     */
    @JvmStatic
    fun getAllSharedAlbumsByPriority(): List<String> {
        val projection = arrayOf(SharedAlbumColumns.ALBUM_ID)
        val order = SharedAlbumColumns.PRIORITY + COMMA + SharedAlbumColumns.SAVE_TIME + ASC
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_ALBUM,
                projection, null, null, order
            )
        return getAlbumIdListByCursor(cursor)
    }

    @JvmStatic
    private fun getAlbumIdListByCursor(cursor: Cursor?): List<String> {
        try {
            if ((cursor == null) || cursor.count <= 0) {
                return emptyList()
            }
            val albumIdList: MutableList<String> = mutableListOf()
            while (cursor.moveToNext()) {
                albumIdList.add(cursor.getString(0))
            }
            return albumIdList
        } catch (e: UnsupportedOperationException) {
            GLog.e(TAG, "getAlbumIdListByCursor", e)
        } finally {
            IOUtils.closeQuietly(cursor)
        }
        return emptyList()
    }

    /**
     * 获取该图集里面的所有文件创建时间，按照升序排列
     */
    @JvmStatic
    fun getCreateTimeById(albumId: String): List<Long> {
        val projection = arrayOf(SharedMediaColumns.CREATE_TIME)
        val selection = SharedMediaColumns.ALBUM_ID + EQUAL_TO
        val selectionArgs = arrayOf(albumId)
        val order = SharedMediaColumns.CREATE_TIME + ASC
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                projection, selection, selectionArgs, order
            )
        return getCreateTimeByCursor(cursor)
    }

    @JvmStatic
    private fun getCreateTimeByCursor(cursor: Cursor?): List<Long> {
        try {
            if ((cursor == null) || cursor.count <= 0) {
                GLog.e(TAG, "getCreateTimeByCursor cursor is null")
                return emptyList()
            }
            val createTimeList: MutableList<Long> = mutableListOf()
            while (cursor.moveToNext()) {
                val createTime = cursor.getLong(0)
                if (!createTimeList.contains(createTime)) {
                    createTimeList.add(createTime)
                }
            }
            return createTimeList
        } catch (e: UnsupportedOperationException) {
            GLog.e(TAG, "getCreateTimeByCursor", e)
        } finally {
            IOUtils.closeQuietly(cursor)
        }
        return emptyList()
    }

    /**
     * 更新globalId
     */
    @JvmStatic
    fun updateGlobalId(matchIds: Map<String, String>) {
        val updateReqList = mutableListOf<UpdateReq>()
        if (matchIds.isEmpty()) {
            return
        }
        matchIds.forEach { (itemId, globalId) ->
            val uploadTime = System.currentTimeMillis()
            val values = ContentValues()
            values.put(SharedMediaColumns.GLOBAL_ID, globalId)
            values.put(SharedMediaColumns.UPLOAD_TIME, uploadTime)
            val selection = SharedMediaColumns._ID + EQUAL_TO
            val selectionArgs = arrayOf(itemId)
            val updateReq =
                UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SHARED_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(selection)
                    .setWhareArgs(selectionArgs)
                    .setConvert(ContentValuesConvert(values))
                    .build()
            updateReqList.add(updateReq)
        }
        BatchReq.Builder().addDataReqs(updateReqList).build().exec()
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SHARED_MEDIA, IDao.DaoType.GALLERY)
    }

    /**
     * 更新文件fileId和io空间校验字段
     */
    @JvmStatic
    fun updateFileIdAndExtra(itemId: String, fileId: String, checkPayload: String) {
        val values = ContentValues()
        values.put(SharedMediaColumns.FILE_ID, fileId)
        values.put(SharedMediaColumns.CHECK_PAYLOAD, checkPayload)
        val selection = SharedMediaColumns._ID + EQUAL_TO
        val selectionArgs = arrayOf(itemId)
        update(
            IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
            values, selection, selectionArgs
        )
    }

    /**
     * 清理过期的文件fileId
     */
    @JvmStatic
    fun clearFileId(itemIds: List<String>) {
        if (itemIds.isEmpty()) {
            return
        }
        itemIds.forEach { itemId ->
            val values = ContentValues()
            values.put(SharedMediaColumns.FILE_ID, "")
            val selection = SharedMediaColumns._ID + EQUAL_TO
            val selectionArgs = arrayOf(itemId)
            update(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                values, selection, selectionArgs
            )
        }
    }


    /**
     * 降低图集优先级
     */
    @JvmStatic
    fun decreasePriority(albumId: String?) {
        if (albumId.isNullOrEmpty()) {
            return
        }
        val values = ContentValues()
        values.put(SharedAlbumColumns.PRIORITY, getPriorityByAlbumId(albumId) + 1)
        val selection = SharedAlbumColumns.ALBUM_ID + EQUAL_TO
        val selectionArgs = arrayOf(albumId)
        update(
            IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_ALBUM,
            values, selection, selectionArgs
        )
    }

    /**
     * 获取图集优先级
     */
    @JvmStatic
    fun getPriorityByAlbumId(albumId: String): Int {
        val projection = arrayOf(SharedAlbumColumns.PRIORITY)
        val selection = SharedAlbumColumns.ALBUM_ID + EQUAL_TO
        val selectionArgs = arrayOf(albumId)
        var priority = 1
        var cursor: Cursor? = null
        try {
            cursor =
                query(
                    IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_ALBUM,
                    projection, selection, selectionArgs, null
                )
            if (cursor != null && cursor.moveToFirst()) {
                priority = cursor.getInt(0)
            }
        } catch (e: UnsupportedOperationException) {
            GLog.e(TAG, "getPriorityByAlbumId", e)
        } finally {
            IOUtils.closeQuietly(cursor)
        }
        return priority
    }

    /**
     * 获取文件失败次数
     */
    @JvmStatic
    fun getFailCountByItem(itemId: String): Int {
        val projection = arrayOf(SharedMediaColumns.RETRY_COUNT)
        val selection = SharedMediaColumns._ID + EQUAL_TO
        val selectionArgs = arrayOf(itemId)
        var count = 0
        var cursor: Cursor? = null
        try {
            cursor =
                query(
                    IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                    projection, selection, selectionArgs, null
                )
            if (cursor != null && cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
        } catch (e: UnsupportedOperationException) {
            GLog.e(TAG, "getFailCountByItem", e)
        } finally {
            IOUtils.closeQuietly(cursor)
        }
        return count
    }

    /**
     * 更新文件失败次数
     */
    @JvmStatic
    fun updateFailCount(itemId: String, failCount: Int) {
        val values = ContentValues()
        values.put(SharedMediaColumns.RETRY_COUNT, failCount)
        val selection = SharedMediaColumns._ID + EQUAL_TO
        val selectionArgs = arrayOf(itemId)
        update(
            IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
            values, selection, selectionArgs
        )
    }

    /**
     * 更新文件审核状态
     */
    @JvmStatic
    fun updateCheckStatus(itemId: String, check: Int) {
        val values = ContentValues()
        values.put(SharedMediaColumns.FILE_CHECK, check)
        val selection = SharedMediaColumns._ID + EQUAL_TO
        val selectionArgs = arrayOf(itemId)
        update(
            IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
            values, selection, selectionArgs
        )
    }

    /**
     * 取消待删除的任务
     */
    @JvmStatic
    fun cancelToBeDel(albumId: String, globalIdList: List<String>) {
        if (globalIdList.isEmpty()) {
            return
        }
        val updateReqList = mutableListOf<UpdateReq>()
        globalIdList.forEach {
            val values = ContentValues()
            values.put(SharedMediaColumns.DELETE, false)
            val selection = SharedMediaColumns.ALBUM_ID + EQUAL_TO + AND + SharedMediaColumns.GLOBAL_ID + EQUAL_TO
            val selectionArgs = arrayOf(albumId, it)
            val updateReq =
                UpdateReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SHARED_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(selection)
                    .setWhareArgs(selectionArgs)
                    .setConvert(ContentValuesConvert(values))
                    .build()
            updateReqList.add(updateReq)
        }
        BatchReq.Builder().addDataReqs(updateReqList).build().exec()
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SHARED_MEDIA, IDao.DaoType.GALLERY)
    }

    @JvmStatic
    fun deleteLocalData(albumId: String, globalIdList: MutableSet<String>) {
        if (globalIdList.isEmpty()) {
            return
        }
        val deleteReqList = mutableListOf<DeleteReq>()
        globalIdList.forEach {
            val selection = SharedMediaColumns.ALBUM_ID + EQUAL_TO + AND + SharedMediaColumns.GLOBAL_ID + EQUAL_TO
            val selectionArgs = arrayOf(albumId, it)
            val deleteReq =
                DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SHARED_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(selection)
                    .setWhereArgs(selectionArgs)
                    .build()
            deleteReqList.add(deleteReq)
        }
        BatchReq.Builder().addDataReqs(deleteReqList).build().exec()
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SHARED_MEDIA, IDao.DaoType.GALLERY)
    }

    /**
     * 删除已上传过的数据
     */
    @JvmStatic
    fun deleteHasUploadData(albumId: String, list: List<SharedAlbumEntity>) {
        val deleteReqList = mutableListOf<DeleteReq>()
        if (list.isEmpty()) {
            return
        }
        list.forEach {
            val selection = SharedMediaColumns.ALBUM_ID + EQUAL_TO + AND + SharedMediaColumns._ID + EQUAL_TO
            val selectionArgs = arrayOf(albumId, it.itemId)
            val deleteReq =
                DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SHARED_MEDIA)
                    .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                    .setWhere(selection)
                    .setWhereArgs(selectionArgs)
                    .build()
            deleteReqList.add(deleteReq)
        }
        BatchReq.Builder().addDataReqs(deleteReqList).build().exec()
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SHARED_MEDIA, IDao.DaoType.GALLERY)
    }

    /**
     * 是否是家庭图集
     */
    @JvmStatic
    fun isFamilyAtlas(albumId: String): Boolean {
        val projection = arrayOf(SharedAlbumColumns.ALBUM_TYPE)
        val selection = SharedAlbumColumns.ALBUM_ID + EQUAL_TO
        val selectionArgs = arrayOf(albumId)
        var cursor: Cursor? = null
        var type = SharedAlbumColumns.TYPE_NORMAL
        try {
            cursor =
                query(
                    IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_ALBUM,
                    projection, selection, selectionArgs, null
                )
            if (cursor != null && cursor.moveToFirst()) {
                type = cursor.getInt(0)
            }
        } catch (e: UnsupportedOperationException) {
            GLog.e(TAG, "isFamilyAtlas", e)
        } finally {
            IOUtils.closeQuietly(cursor)
        }
        return type == SharedAlbumColumns.TYPE_FAMILY
    }

    /**
     * 清理未上传成功但本地文件已经删除的数据
     */
    @JvmStatic
    fun clearHasDeleteData(filePath: String) {
        val selection =
            SharedMediaColumns.FILE_PATH + EQUAL_TO +
                    AND + SharedMediaColumns.GLOBAL_ID + IS_NULL +
                    AND + SharedMediaColumns.FILE_ID + IS_NULL
        val selectionArgs = arrayOf(filePath)
        delete(
            IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
            selection, selectionArgs
        )
    }

    /**
     * 共享图集文件重置审核状态和重试次数,上传重试
     */
    @JvmStatic
    fun resetSharedMediaUploadState(ids: List<String>) {
        val selection = getWhereForInKeyword(ids.size, SharedMediaColumns._ID).toString()
        val selectionArgs = ids.toTypedArray()
        val values = ContentValues().apply {
            put(SharedMediaColumns.FILE_CHECK, SharedMediaColumns.FILE_CHECKING)
            put(SharedMediaColumns.RETRY_COUNT, 0)
        }
        update(
            IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA, values, selection, selectionArgs
        )
    }

    /**
     * 删除共享图集详情页本地数据
     */
    @JvmStatic
    fun delete(ids: List<String>) {
        val where = getWhereForInKeyword(ids.size, SharedMediaColumns._ID).toString()
        val contentValues = ContentValues()
        contentValues.put(SharedMediaColumns.DELETE, true)
        UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SHARED_MEDIA)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setConvert { contentValues }
            .setWhere(where)
            .setWhareArgs(ids.toTypedArray())
            .build()
            .exec()
        // 删除本地未上传但标记删除的文件
        DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SHARED_MEDIA)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
            .setWhere(
                SharedMediaColumns.GLOBAL_ID + IS_NULL +
                        AND + SharedMediaColumns.DELETE + EQUAL_ONE
            )
            .build()
            .exec()
        // 两次合并发一次通知
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SHARED_MEDIA, IDao.DaoType.GALLERY)
    }

    /**
     * 获取下载文件详细数据列表
     */
    @JvmStatic
    fun getShareAlbumEntityList(ids: List<String>): List<SharedAlbumEntity> {
        val builder: StringBuilder = getWhereForInKeyword(ids.size, SharedMediaColumns._ID)
        val selectionArgs = ids.toTypedArray()
        val cursor =
            query(
                IDao.DaoType.GALLERY, GalleryDbDao.TableType.SHARED_MEDIA,
                null, builder.toString(), selectionArgs, null
            )
        return SharedAlbumEntity.getSharedAlbumEntities(cursor)
    }

    /**
     * 获取共享图集详细数据
     */
    @JvmStatic
    fun queryAlbum(id: String): SharedAlbumResponse? {
        val exec =
            QueryReq.Builder<List<SharedAlbumResponse>>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SHARED_ALBUM)
                .setWhere("${GalleryStore.SharedAlbumColumns.ALBUM_ID} = '$id'")
                .setConvert(Cursor2SharedAlbumListConvert())
                .build().exec()
        return if (exec.isNullOrEmpty()) null else exec[0]
    }
}