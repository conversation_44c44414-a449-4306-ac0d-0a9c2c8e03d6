/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedSyncConstant
 ** Description:SharedSyncConstant
 ** Version: 1.0
 ** Date : 2022/4/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/29        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.constant

object SharedSyncConstant {
    const val SHARED_MODULE = "shareMetaData"
    const val SHARED_ID = "atlasId"
    const val USER_ID = "userId"

    /**
     * 共享图集网络请求 source字段默认传5
     */
    const val REQUEST_SOURCE = 5

    /**
     * 元数据上传服务器失败的最大次数
     */
    const val MAX_ERROR_COUNT = 3

    /**
     * 图集列表每页默认大小
     */
    const val DEFAULT_PAGE_SIZE = 20
}