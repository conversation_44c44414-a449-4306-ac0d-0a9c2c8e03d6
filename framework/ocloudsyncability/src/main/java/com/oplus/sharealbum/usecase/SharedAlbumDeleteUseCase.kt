/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumDeleteUseCase
 ** Description:
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: chenjie@A<PERSON>.KeKeCloud
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                          <data>         <version >     <desc>
 **  <EMAIL>            2022/7/26        1.0             Add
 **************************************************************************************************/

package com.oplus.sharealbum.usecase

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.sharealbum.bean.DeleteSharedAlbumRequest
import com.oplus.sharealbum.bean.QuitSharedAlbumRequest
import com.oplus.sharealbum.bean.SharedAlbumResult
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.net.callExt
import com.oplus.sharealbum.service.ISharedAlbumService

/**
 * 删除或者退出共享图集网络请求
 */
class SharedAlbumDeleteUseCase {

    /**
     * 删除共享图集
     */
    fun deleteSharedAlbum(albumId: String): Int {
        val albumService = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
        val call = albumService.deleteSharedAlbum(DeleteSharedAlbumRequest(albumId))
        val code = call.callExt().code
        GLog.d(TAG, "deleteSharedAlbum code = $code")
        return code
    }

    /**
     * 退出共享图集
     */
    fun quitSharedAlbum(albumId: String): Int {
        val albumService = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
        val call = albumService.exitSharedAlbum(QuitSharedAlbumRequest(albumId))
        val response = call.callExt()
        var code = response.code
        if (response.data?.flag == true) {
            code = SharedAlbumResult.SUCCESS
        }
        GLog.d(TAG, "quitSharedAlbum code = $code")
        return code
    }

    companion object {
        private const val TAG = "SharedAlbumDeleteUseCase"
    }
}