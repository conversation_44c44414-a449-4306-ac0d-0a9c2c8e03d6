/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumFaqUserCase
 ** Description:
 ** Version: 1.0
 ** Date : 2022/7/4
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>            2022/7/4        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.usecase

import com.oplus.sharealbum.bean.FetchDocResponse
import com.oplus.sharealbum.net.FaqAndDnsRequestManager

/**
 * 共享图集设置页faq网络请求
 */
class SharedAlbumFaqUserCase {

    fun reqFaq(): List<FetchDocResponse.ChildrenBean> {
        val response = FaqAndDnsRequestManager.requestFAQ()
        if (response?.isSuccess == true) {
            val data = response.data
            if (null != data) {
                return data.children ?: emptyList()
            }
        }
        return emptyList()
    }
}