/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - OperateDataUtils
 ** Description:OperateDataUtils
 ** Version: 1.0
 ** Date : 2022/3/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/3/28        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.helper

import androidx.annotation.WorkerThread
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.sharealbum.bean.SharedAlbumAddItemRequest
import com.oplus.sharealbum.bean.SharedAlbumAddItemResponse
import com.oplus.sharealbum.callback.ISharedAlbumReqCallback
import com.oplus.sharealbum.net.BaseObserver
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.service.ISharedAlbumService
import com.oplus.sharealbum.bean.SharedAlbumDeleteItemResponse
import com.oplus.sharealbum.bean.SharedAlbumDeleteItemRequest
import com.oplus.sharealbum.bean.QueryItemResponse
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.sharealbum.util.PushInitCheckUtils.checkPushInit

object OperateDataUtils {

    /**
     * 删除图片
     */
    @WorkerThread
    @JvmStatic
    fun backupOnDelete(
        albumId: String,
        delGids: MutableSet<String>,
        sharedAlbumReqCallback: ISharedAlbumReqCallback<SharedAlbumDeleteItemResponse>
    ) {
        checkPushInit()
        val deleteItemReq = SharedAlbumDeleteItemRequest(albumId, delGids)
        val reqDelItem = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java).deleteItem(deleteItemReq)
        reqDelItem.subscribe(object : BaseObserver<SharedAlbumDeleteItemResponse>() {
            override fun success(data: SharedAlbumDeleteItemResponse?) {
                sharedAlbumReqCallback.sharedAlbumReqResult(data)
            }

            override fun error(code: Int, msg: String?) {
                sharedAlbumReqCallback.error(code, msg)
            }
        })
    }

    /**
     * 元数据上传文件
     */
    @JvmStatic
    @WorkerThread
    fun backupOnAdd(
        albumId: String,
        shareTime: Long,
        addItems: MutableList<QueryItemResponse.Item>,
        sharedAlbumReqCallback: ISharedAlbumReqCallback<SharedAlbumAddItemResponse>
    ) {
        checkPushInit()
        val spaceId = ""
        val addItemReq = SharedAlbumAddItemRequest(albumId, spaceId, addItems)
        addItemReq.setUniqueId(albumId + shareTime)  // uniqueid采取图集id+sharetime 来避免重复上传
        val reqAddItem = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java).addItem(addItemReq)
        reqAddItem.subscribe(object : BaseObserver<SharedAlbumAddItemResponse>() {
            override fun success(data: SharedAlbumAddItemResponse?) {
                sharedAlbumReqCallback.sharedAlbumReqResult(data)
            }

            override fun error(code: Int, msg: String?) {
                sharedAlbumReqCallback.error(code, msg)
            }
        })
    }

    /**
     * 本地数据转换服务端 上传
     */
    @JvmStatic
    fun localTransferServerUp(sharedAlbumList: List<SharedAlbumEntity>): MutableList<QueryItemResponse.Item> {
        val queryItemResponse: MutableList<QueryItemResponse.Item> = ArrayList()
        if (sharedAlbumList.isNotEmpty()) {
            val uploadTime = System.currentTimeMillis()
            sharedAlbumList.forEach { album ->
                queryItemResponse.add(
                    QueryItemResponse.Item(
                        album.itemId,
                        album.globalId,
                        album.albumId,
                        album.userId,
                        album.md5,
                        album.fileId ?: TextUtil.EMPTY_STRING,
                        album.filePath,
                        album.type,
                        album.duration,
                        album.height,
                        album.width,
                        album.size,
                        album.check,
                        album.createTime,
                        uploadTime,
                        album.orientation,
                        checkPayload = album.checkPayload
                    )
                )
            }
        }
        return queryItemResponse
    }
}