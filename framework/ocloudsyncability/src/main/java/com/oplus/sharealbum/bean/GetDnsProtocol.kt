/***************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * OPLUS_EDIT
 * File: - GetDnsProtocol
 * Description:GetDnsProtocol
 * Version: 1.0
 * Date : 2022/3/30
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------------------------------------------
 * <author>                       <data>         <version>     <desc>
 * <EMAIL>             2022/3/30        1.0             Add
 ****************************************************************/
package com.oplus.sharealbum.bean

import com.google.gson.annotations.SerializedName
import com.oplus.sharealbum.bean.GetDnsProtocol.GetDnsResult

class GetDnsProtocol : CommonGalleryResponse<GetDnsResult?>() {
    data class GetDnsRequest(
        @SerializedName("regionCode") var regionCode: String?
    )

    data class GetDnsResult(
        @SerializedName("atlasUrlDNS") var atlasUrlDNS: String?
    )
}