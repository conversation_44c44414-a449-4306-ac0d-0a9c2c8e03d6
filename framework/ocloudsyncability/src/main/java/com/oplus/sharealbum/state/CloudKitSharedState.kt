/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CloudKitShareState
 ** Description:CloudKitShareState
 ** Version: 1.0
 ** Date : 2022/4/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/19        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.state

import com.oplus.sharealbum.bean.SharedAlbumResult.NETWORK_ERROR
import com.oplus.sharealbum.bean.SharedAlbumResult.NOT_MEMBER
import com.oplus.sharealbum.bean.SharedAlbumResult.NO_PERMISSION_PERFORM_OPERATION
import com.oplus.sharealbum.bean.SharedAlbumResult.SHARED_SYNC_FAIL
import com.oplus.sharealbum.bean.SharedAlbumResult.SHARED_SYNC_IO_UPLOAD
import com.oplus.sharealbum.bean.SharedAlbumResult.SHARED_SYNC_METADATA_END
import com.oplus.sharealbum.bean.SharedAlbumResult.SHARED_SYNC_METADATA_START
import com.oplus.sharealbum.bean.SharedAlbumResult.SHARED_SYNC_SPACE_NOT_ENOUGH
import com.oplus.sharealbum.bean.SharedAlbumResult.TOO_MANY_FILES_IN_THE_GALLERY

enum class CloudKitSharedState(private val code: Int) {
    SUCCESS(0),
    FAIL(SHARED_SYNC_FAIL),
    METADATA_START(SHARED_SYNC_METADATA_START),
    METADATA_END(SHARED_SYNC_METADATA_END),
    IO_UPLOAD(SHARED_SYNC_IO_UPLOAD),
    SPACE_NOT_ENOUGH(SHARED_SYNC_SPACE_NOT_ENOUGH),
    SHARED_FILE_TOO_MANY(TOO_MANY_FILES_IN_THE_GALLERY),
    NO_PERMISSION(NO_PERMISSION_PERFORM_OPERATION),
    NOT_ATLAS_MEMBER(NOT_MEMBER),
    SHARED_NETWORK_ERROR(NETWORK_ERROR);


    companion object {
        fun getByCode(code: Int): CloudKitSharedState {
            for (cloudKitShareState in values()) {
                if (cloudKitShareState.code == code) {
                    return cloudKitShareState
                }
            }
            return FAIL
        }
    }
}