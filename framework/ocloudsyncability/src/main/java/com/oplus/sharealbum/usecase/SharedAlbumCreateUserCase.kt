/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumCreateUserCase
 ** Description:
 ** Version: 1.0
 ** Date : 2022/6/28
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>            2022/6/28        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.usecase

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.sharealbum.bean.SharedAlbumResult
import com.oplus.sharealbum.bean.CreateSharedAlbumRequest
import com.oplus.sharealbum.bean.ChangeSharedAlbumAttributeRequest
import com.oplus.sharealbum.helper.SharedAlbumListRepository
import com.oplus.sharealbum.net.ApiResponse
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.net.callExt
import com.oplus.sharealbum.service.ISharedAlbumService

/**
 * 创建或者更新共享图集网络请求
 */
class SharedAlbumCreateUserCase(val callback: (ApiResponse<SharedAlbumResponse>) -> Unit) {

    /**
     * 请求是否超时
     */
    @Volatile
    private var isTimeout = false

    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            if (isTimeout) {
                return
            }
            isTimeout = true
            callback(ApiResponse<SharedAlbumResponse>().apply {
                code = SharedAlbumResult.TIME_OUT_ERROR
                errMsg = "request is time out"
            })
        }
    }

    /**
     * 创建共享图集
     */
    fun create(sharedAlbumTitle: String) {
        isTimeout = false
        handler.sendEmptyMessageDelayed(MSG_TIME_OUT, TIME_OUT)
        val call =
            SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
                .createSharedAlbum(CreateSharedAlbumRequest(sharedAlbumTitle))
        val response = call.callExt()
        if (isTimeout) {
            return
        }
        handler.removeMessages(MSG_TIME_OUT)
        isTimeout = true
        val result = ApiResponse<SharedAlbumResponse>().apply {
            code = response.code
            data = response.data?.album
            errMsg = response.errMsg
        }
        if (result.isSuccess) {
            result.data?.let {
                SharedAlbumListRepository().insertAlbumList(arrayListOf(it))
            }
        }
        callback(result)
    }

    /**
     * 更新共享图集名称
     */
    fun update(res: SharedAlbumResponse, albumName: String) {
        isTimeout = false
        handler.sendEmptyMessageDelayed(MSG_TIME_OUT, TIME_OUT)
        val req = ChangeSharedAlbumAttributeRequest(res.albumId ?: TextUtil.EMPTY_STRING, albumName)
        val call = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java).changeSharedAlbumAttribute(req)
        val response = call.callExt()
        if (isTimeout) {
            return
        }
        handler.removeMessages(MSG_TIME_OUT)
        isTimeout = true
        val result = ApiResponse<SharedAlbumResponse>().apply {
            code = response.code
            errMsg = response.errMsg
            res.albumName = albumName
            data = res
        }
        if (result.isSuccess && null != result.data) {
            res.albumId?.let {
                SharedAlbumListRepository().updateAlbumTitle(it, albumName)
            }
        }
        callback(result)
    }

    companion object {
        private const val TIME_OUT = 3L * TimeUtils.TIME_1_SEC_IN_MS
        private const val MSG_TIME_OUT = 3000
    }
}