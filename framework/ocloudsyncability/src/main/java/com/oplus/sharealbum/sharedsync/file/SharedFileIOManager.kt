/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedFileIOManager
 ** Description:SharedFileIOManager
 ** Version: 1.0
 ** Date : 2022/3/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/3/24        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.sharedsync.file

import android.net.Uri
import android.text.TextUtils
import androidx.core.net.toFile
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.cloudkitlib.base.CloudTypeIOFile
import com.oplus.cloudkitlib.base.CustomCloudKitError
import com.oplus.cloudkitlib.base.ITransferFilesCallBack
import com.oplus.cloudkitlib.base.SyncResult
import com.oplus.cloudkitlib.file.FileTransferTaskManager
import com.oplus.cloudkitlib.metadata.ISyncAllow
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cloud.DownloadInfo
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.basecloudability.base.EnvConfig
import com.oplus.gallery.framework.abilities.basecloudability.base.SourceFlag.SYNC_DEFAULT
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.key.CacheKeyFactory
import com.oplus.gallery.framework.abilities.ocloudsync.sync.GallerySyncManager.Companion.TEST_ENABLE
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.sharealbum.bean.SharedAlbumExtraInfo
import com.oplus.sharealbum.bean.SharedInfo
import com.oplus.sharealbum.constant.SharedSyncConstant.SHARED_MODULE
import com.oplus.sharealbum.sharedsync.SharedConditionManager
import com.oplus.sharealbum.util.SharedAlbumThumbInfoUtil
import com.oplus.sharealbum.util.SharedFilePathUtil
import java.net.URI
import java.util.Base64

class SharedFileIOManager : ISharedFileSync {
    companion object {
        private const val TAG = "SharedFileIOManager"

        /**
         * 最大并行传输文件数
         */
        private const val PARALLEL_FILE_COUNT = 2

        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            SharedFileIOManager()
        }
    }

    var fileTransferTaskManager: FileTransferTaskManager

    init {
        val syncModel = if (TEST_ENABLE) {
            EnvConfig.PROCESS_TEST
        } else {
            EnvConfig.RELEASE
        }
        val syncAllow = object : ISyncAllow {
            override fun onCheckAllowToSync(source: Int): SyncResult {
                return SharedConditionManager.checkState()
            }
        }
        fileTransferTaskManager = FileTransferTaskManager(syncModel, PARALLEL_FILE_COUNT, syncAllow = syncAllow)
    }


    private fun getSharedInfo(entity: SharedAlbumEntity): String? {
        val ioData = JsonUtil.toJson(SharedInfo.IOData(entity.albumId, entity.globalId, entity.userId))
        if (TextUtils.isEmpty(ioData)) {
            GLog.e(TAG, "getSharedInfo ioData isEmpty")
            return null
        }
        if (TextUtils.isEmpty(entity.albumOwnerUserId)) {
            GLog.e(TAG, "getSharedInfo albumOwnerUserId isEmpty")
            return null
        }
        val spaceData = JsonUtil.toJson(SharedInfo.SpaceData(entity.albumOwnerUserId ?: TextUtil.EMPTY_STRING, entity.albumId))
        if (TextUtils.isEmpty(spaceData)) {
            GLog.e(TAG, "getSharedInfo spaceData isEmpty")
            return null
        }
        val sharedInfo = SharedInfo(ioData, spaceData)
        return JsonUtil.toJson(sharedInfo)
    }

    override fun uploadSharedFiles(ioFiles: List<SharedAlbumEntity>, iTransferFilesCallBack: ITransferFilesCallBack) {
        GLog.d(TAG, "uploadSharedFiles")
        val files = ioFiles.mapNotNull {
            createUploadSharedFile(it)
        }
        executeTask(frontTask = false, files, iTransferFilesCallBack)
    }

    override fun downloadSharedOriginFiles(
        downloadInfo: DownloadInfo,
        ioFiles: List<SharedAlbumEntity>,
        iTransferFilesCallBack: ITransferFilesCallBack
    ) {
        GLog.d(TAG, "downloadSharedOriginFiles")
        val files = ioFiles.mapNotNull {
            createDownloadSharedOriginFile(it)
        }
        if (files.isEmpty()) return
        executeTask(frontTask = true, files, object : ITransferFilesCallBack {
            override fun batchResult(
                successFileList: List<CloudIOFile>,
                errorFileList: List<CloudIOErrorFile<CloudIOFile>>,
                cloudDateType: CloudDataType
            ) {
                iTransferFilesCallBack.batchResult(successFileList, errorFileList, cloudDateType)
            }

            override fun onIOResult(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                GLog.d(TAG, "downloadSharedOriginFiles onIOResult error = $cloudKitError")
                val extra = JsonUtil.fromJson(file.extra, SharedAlbumExtraInfo::class.java)
                if (null == extra || extra.itemId.isEmpty()) {
                    GLog.e(TAG, "downloadSharedOriginFiles onIOResult itemId is null , extra = ${file.extra}")
                    return
                }
                val itemId = extra.itemId
                if (cloudKitError.isSuccess && file.cacheUri.isNullOrEmpty().not()) {
                    downloadInfo.success.add(itemId)
                    val cacheFile = Uri.parse(file.cacheUri).toFile().absolutePath
                    val filePath = SharedFilePathUtil.createOriginalSavePath(file)
                    if (!filePath.isNullOrEmpty()) {
                        FileOperationUtils.moveFile(cacheFile, filePath)
                        ApiDmManager.getMediaDBSyncDM().executeFilePathSync(arrayOf(filePath))
                        // 下载完原图，通知上层刷新UI
                        ContextGetter.context.contentResolver.notifyChange(GalleryStore.SharedMedia.getContentUri(), null)
                    }
                } else {
                    if (cloudKitError.bizErrorCode != CloudBizError.MANUAL_STOP.code) {
                        downloadInfo.error[itemId] = cloudKitError
                    }
                }
                iTransferFilesCallBack.onIOResult(file, cloudDateType, cloudKitError)
            }

            override fun onProgress(file: CloudIOFile, cloudDataType: CloudDataType, progress: Double) {
                iTransferFilesCallBack.onProgress(file, cloudDataType, progress)
            }
        })
    }

    override fun downloadSharedThumbFiles(
        frontTask: Boolean,
        ioFiles: List<SharedAlbumEntity>,
        iTransferFilesCallBack: ITransferFilesCallBack
    ) {
        val cachingAbility = ContextGetter.context.getAppAbility<ICachingAbility>() ?: return
        val files = ioFiles.filter {
            if (it.fileId.isNullOrEmpty()) return@filter false
            cachingAbility.thumbnailCache?.exists(
                CacheKeyFactory.createSharedMediaCacheKey(it.fileId!!),
                CacheOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL)
            ) == false
        }.mapNotNull {
            createDownloadSharedThumbFile(it)
        }
        GLog.d(TAG, "downloadSharedThumbFiles size:${files.size}")
        if (files.isEmpty()) return

        executeTask(frontTask, files, object : ITransferFilesCallBack {
            override fun batchResult(
                successFileList: List<CloudIOFile>,
                errorFileList: List<CloudIOErrorFile<CloudIOFile>>,
                cloudDateType: CloudDataType
            ) {
                cachingAbility.close()
                iTransferFilesCallBack.batchResult(successFileList, errorFileList, cloudDateType)
            }

            override fun onIOResult(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                if (cloudKitError.isSuccess && file.cacheUri.isNullOrEmpty().not()) {
                    cachingAbility.thumbnailCache?.putBitmapData(
                        CacheKeyFactory.createSharedMediaCacheKey(file.cloudId),
                        FileOperationUtils.fileToByte(File(URI(file.cacheUri))),
                        CacheOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL)
                    )
                }
                iTransferFilesCallBack.onIOResult(file, cloudDateType, cloudKitError)
            }

            override fun onProgress(file: CloudIOFile, cloudDataType: CloudDataType, progress: Double) {
                iTransferFilesCallBack.onProgress(file, cloudDataType, progress)
            }
        })
    }

    override fun stopUploadSharedFiles(ioFiles: List<SharedAlbumEntity>) {
        GLog.d(TAG, "stopUploadSharedFiles")
        val list = ioFiles.mapNotNull {
            createUploadSharedFile(it)
        }
        fileTransferTaskManager.stop(list)
    }

    override fun stopDownloadSharedThumbFiles(ioFiles: List<SharedAlbumEntity>) {
        GLog.d(TAG, "stopDownloadSharedThumbFiles")
        val list = ioFiles.mapNotNull {
            createDownloadSharedThumbFile(it)
        }
        fileTransferTaskManager.stop(list)
    }

    override fun stopDownloadSharedOriginFiles(ioFiles: List<SharedAlbumEntity>) {
        GLog.d(TAG, "stopDownloadSharedOriginFiles")
        val list = ioFiles.mapNotNull {
            createDownloadSharedOriginFile(it)
        }
        fileTransferTaskManager.stop(list)
    }

    override fun createUploadSharedFile(entity: SharedAlbumEntity): CloudTypeIOFile? {
        val shareInfo = getSharedInfo(entity)
        if (shareInfo.isNullOrEmpty()) {
            GLog.e(TAG, "createUploadSharedFile shareInfo is null sharedAlbumEntity $entity")
            return null
        }
        GLog.d(TAG, "createUploadSharedFile shareInfo is $shareInfo")
        return CloudTypeIOFile.createFile(
            CloudIOFile.createUploadShareFile(
                SHARED_MODULE,
                SHARED_MODULE,
                File(entity.filePath).toUri().toString(),
                entity.md5,
                shareInfo
            ), CloudDataType.SHARE
        ).apply {
            extra = JsonUtil.toJson(SharedAlbumExtraInfo(entity.itemId))
        }
    }

    override fun createDownloadSharedThumbFile(entity: SharedAlbumEntity): CloudTypeIOFile? {
        val cloudThumbInfo = SharedAlbumThumbInfoUtil.getSharedThumbInfo(entity)
        val sharedInfo = getSharedInfo(entity)
        if (sharedInfo.isNullOrEmpty()) {
            GLog.e(TAG, "createDownloadSharedThumbFile sharedInfo is null sharedAlbumEntity $entity")
            return null
        }
        // 下载时shareInfo是放到下载接口的header头里，需要传入时base64加密
        val shareInfoWithBase64 = Base64.getUrlEncoder().encodeToString(sharedInfo.toByteArray())
        GLog.d(TAG, "createDownloadSharedThumbFile shareInfo : $sharedInfo encodeShareInfo : $shareInfoWithBase64")
        return CloudTypeIOFile.createFile(
            CloudIOFile.createDownloadShareThumbFile(
                entity.itemId,
                SHARED_MODULE,
                SHARED_MODULE,
                entity.md5,
                entity.fileId,
                // 其实目前指定文件路径并没有作用，Cloudkit只会下到他们内置缓存路径，需要业务自行迁移
                SharedFilePathUtil.getShareThumbFilePath(entity.filePath, entity.fileId ?: TextUtil.EMPTY_STRING),
                shareInfoWithBase64,
                cloudThumbInfo
            ), CloudDataType.SHARE
        ).apply {
            extra = JsonUtil.toJson(SharedAlbumExtraInfo(entity.itemId))
        }
    }

    override fun createDownloadSharedOriginFile(entity: SharedAlbumEntity): CloudTypeIOFile? {
        val sharedInfo = getSharedInfo(entity)
        if (sharedInfo.isNullOrEmpty()) {
            GLog.e(TAG, "createDownloadSharedOriginFile sharedInfo is null sharedAlbumEntity $entity")
            return null
        }
        // 下载时shareInfo是放到下载接口的header头里，需要传入时base64加密
        val shareInfoWithBase64 = Base64.getUrlEncoder().encodeToString(sharedInfo.toByteArray())
        return CloudTypeIOFile.createFile(
            CloudIOFile.createDownloadShareFile(
                entity.itemId, SHARED_MODULE, SHARED_MODULE,
                entity.md5, entity.fileId, entity.filePath, shareInfoWithBase64
            ), CloudDataType.SHARE
        ).apply {
            extra = JsonUtil.toJson(SharedAlbumExtraInfo(entity.itemId))
        }
    }

    override fun stopAll(clearData: Boolean) {
        fileTransferTaskManager.stopAll()
    }


    private fun executeTask(
        frontTask: Boolean,
        fileList: List<CloudIOFile>,
        iTransferFilesCallBack: ITransferFilesCallBack
    ) {
        fileTransferTaskManager.execute(SYNC_DEFAULT, frontTask, fileList, object : FileTransferTaskManager.ICallBack<CloudIOFile> {

            override fun onProgress(file: CloudIOFile, cloudDateType: CloudDataType, progress: Double) {
                iTransferFilesCallBack.onProgress(file, cloudDateType, progress)
            }

            override fun onFinish(file: CloudIOFile, cloudDateType: CloudDataType, customCloudKitError: CustomCloudKitError) {
                iTransferFilesCallBack.onIOResult(file, cloudDateType, customCloudKitError.cloudKitError)
            }

            override fun batchResult(
                successFileList: List<CloudIOFile>,
                errorFileList: List<CloudIOErrorFile<CloudIOFile>>,
                cloudDateType: CloudDataType
            ) {
                iTransferFilesCallBack.batchResult(successFileList, errorFileList, cloudDateType)
            }
        })
    }
}