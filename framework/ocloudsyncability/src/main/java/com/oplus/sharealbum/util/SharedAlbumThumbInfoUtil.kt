/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumThumbInfoUtil
 ** Description:SharedAlbumThumbInfoUtil
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/3/30        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.util

import com.oplus.gallery.framework.abilities.ocloudsync.file.CloudThumbInfo
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumFileResponse
import com.oplus.gallery.framework.abilities.basecloudability.file.ThumbScaleUtil

object SharedAlbumThumbInfoUtil {
    /**
     * 视频缩图的默认宽度
     */
    const val DEFAULT_WIDTH = 300L

    /**
     * 视频缩图的默认高度
     */
    const val DEFAULT_HEIGHT = 400L

    @JvmStatic
    fun getSharedThumbInfo(sharedAlbumEntity: SharedAlbumEntity): String {
        return if (sharedAlbumEntity.type == SharedAlbumFileResponse.TYPE_VIDEO) {
            val cloudThumbInfo =
                CloudThumbInfo(
                    DEFAULT_WIDTH.toInt(), DEFAULT_HEIGHT.toInt(), CloudThumbInfo.QualityType.Q_90, CloudThumbInfo.FormatType.JPG
                )
            cloudThumbInfo.getVideoUrlThumbInfoParam()
        } else {
            val thumbResult = ThumbScaleUtil.getThumbResult(sharedAlbumEntity.width.toInt(), sharedAlbumEntity.height.toInt())
            val cloudThumbInfo =
                CloudThumbInfo(thumbResult.width, thumbResult.height, CloudThumbInfo.QualityType.Q_90, CloudThumbInfo.FormatType.JPG)
            cloudThumbInfo.getUrlThumbInfoParam()
        }
    }
}