/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumDeleteItemRequest
 ** Description:SharedAlbumDeleteItemRequest
 ** Version: 1.0
 ** Date : 2022/4/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/29        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.bean

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class SharedAlbumDeleteItemRequest(

    @SerializedName("atlasId") val albumId: String,
    /**
     * 删除的数据(globalId)
     */
    val delGids: Set<String>
) : SharedAlbumRequest()
