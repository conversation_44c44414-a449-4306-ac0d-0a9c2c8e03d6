/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ShareSyncStatusManager
 ** Description:ShareSyncStatusManager
 ** Version: 1.0
 ** Date : 2022/4/6
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/6        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.state

import com.oplus.sharealbum.bean.SharedAlbumSyncState
import java.util.concurrent.locks.ReentrantLock

class SharedSyncStatusManager {

    private val statusCache: HashMap<String, SharedAlbumSyncState> = HashMap()
    private val lock = ReentrantLock()

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            SharedSyncStatusManager()
        }
    }


    fun putShareStateCache(albumId: String, isUploading: Boolean, state: CloudKitSharedState) {
        lock.lock()
        val nowState = statusCache[albumId]?.uploadState ?: CloudKitSharedState.SUCCESS
        if (state == CloudKitSharedState.METADATA_END && nowState != CloudKitSharedState.METADATA_START) {
            statusCache[albumId] = SharedAlbumSyncState(isUploading, nowState)
        } else {
            statusCache[albumId] = SharedAlbumSyncState(isUploading, state)
        }
        lock.unlock()
    }

    fun getSharedState(albumId: String): SharedAlbumSyncState? {
        return statusCache[albumId]
    }

    fun refresh() {
        lock.lock()
        statusCache.clear()
        lock.unlock()
    }
}