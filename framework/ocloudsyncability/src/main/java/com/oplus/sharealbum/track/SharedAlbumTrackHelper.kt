/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumTrackHelper
 ** Description:SharedAlbumTrackHelper
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/30        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.track

import androidx.annotation.WorkerThread
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.track.SingleTrack
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId.CLICK_CREATE_ALBUM_BTN
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId.CLICK_CREATE_ALBUM_PAGER
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId.CLICK_SETTING_PAGER
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.ALBUM_ID
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.ALBUM_TYPE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.BUTTON
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.DOUBLE_CHECK
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.FAQ_ID
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.FAQ_QUESTION
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.OPER_RESULT
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.OPER_TYPE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.PAGE_ID
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.TYPE_SHARED_ALBUM
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId.SHARED_TIMELINE_PAGE_BUTTON_CLICK
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId.SHARED_TIMELINE_PAGE_SELECT_MODE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId.SHARED_TIMELINE_PAGE_TOP_TIPS
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.EventId.SHARED_TIMELINE_UPLOAD_FILE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.ACCOUNT_COUNT
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.ENTER_ID
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.INVITATION_CHANNEL
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.INVITATION_ID
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.IS_BLANK
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.PAGE_STAY_TIME
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.PANEL
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.PHOTO_COUNT
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.TAB
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.TOAST
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.UPLOAD_WAY
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Key.VIDEO_COUNT
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.ALBUMS_PAGE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.ERROR_NONE

object SharedAlbumTrackHelper {

    /**
     * 图集列表页曝光
     */
    fun trackAlbumsPage(enterId: String, isBlank: Boolean, pageStayTime: Long) {
        track(EventId.SHARED_ALBUM_PAGER) {
            it.putProperty(PAGE_ID, ALBUMS_PAGE)
            it.putProperty(ENTER_ID, enterId)
            it.putProperty(IS_BLANK, isBlank)
            it.putProperty(PAGE_STAY_TIME, pageStayTime)
        }
    }

    /**
     * 邀请方式选择
     */
    fun trackInviteChannel(pageId: String, invitationChannel: String) {
        track(EventId.INVITE_CHANNEL) {
            it.putProperty(PAGE_ID, pageId)
            it.putProperty(INVITATION_CHANNEL, invitationChannel)
        }
    }

    /**
     * 微信邀请结果
     */
    fun trackWechatInviteResult(operResult: String) {
        track(EventId.WECHAT_INVITE_RESULT) {
            it.putProperty(OPER_RESULT, operResult)
        }
    }

    /**
     * 账号邀请界面点击
     */
    fun trackAccountInvitePagerClick(button: String, accountCount: Int? = null, operResult: String? = null) {
        track(EventId.CLICK_ACCOUNT_INVITE_PAGER) {
            it.putProperty(BUTTON, button)
            it.putProperty(ACCOUNT_COUNT, accountCount)
            it.putProperty(OPER_RESULT, operResult)
        }
    }

    /**
     * 邀请信息处理
     */
    fun trackInviteMsgOper(pageId: String, invitationId: String, operType: Int, operResult: String) {
        track(EventId.INVITE_MSG_OPER) {
            it.putProperty(PAGE_ID, pageId)
            it.putProperty(INVITATION_ID, invitationId)
            it.putProperty(OPER_TYPE, operType)
            it.putProperty(OPER_RESULT, operResult)
        }
    }

    /**
     *  成员页面操作
     *  @param tab 所在tab(member_management: 图集成员invitation_management：邀请管理)
     *  @param panel 展示的面板(member_delete：删除成员，invite_expired：已过期邀请的操作面板，
     *  invite_unexpire：未过期邀请的操作面板，invite_invalid：无效账号的操作面板)
     *  @param operType 操作类型(cancel：取消,delete：删除,withdraw：撤销邀请,invite_again：再次邀请)
     *  @param operResult 操作结果(success：操作成功,fail_01：无网络,fail_02：网络异常,fail_03:邀请已失效)
     */
    fun trackSharedMemberPagerClick(tab: String, panel: String, operType: String, operResult: String?) {
        track(EventId.CLICK_SHARED_MEMBER_PAGER) {
            it.putProperty(TAB, tab)
            it.putProperty(PANEL, panel)
            it.putProperty(OPER_TYPE, operType)
            it.putProperty(OPER_RESULT, operResult)
        }
    }

    /**
     *  Setting pager
     */
    fun trackSettingPagerBtnClick(btnName: String?, albumType: String?, albumId: String?, doubleCheck: String?) {
        trackSendSettingClick(
            btnName = btnName,
            albumType = albumType,
            albumId = albumId,
            doubleCheck = doubleCheck
        )
    }

    fun trackSettingPagerFaqClick(faqQuestion: String?, faqId: String?, albumType: String?, albumId: String?) {
        trackSendSettingClick(
            faqQuestion = faqQuestion,
            faqId = faqId,
            albumId = albumId,
            albumType = albumType
        )
    }

    @JvmStatic
    private fun trackSendSettingClick(
        btnName: String? = null,
        faqQuestion: String? = null,
        faqId: String? = null,
        doubleCheck: String? = null,
        albumType: String? = null,
        albumId: String? = null
    ) {
        track(CLICK_SETTING_PAGER) { track ->
            btnName?.let { track.putProperty(BUTTON, it) }
            faqQuestion?.let { track.putProperty(FAQ_QUESTION, it) }
            faqId?.let { track.putProperty(FAQ_ID, it) }
            doubleCheck?.let { track.putProperty(DOUBLE_CHECK, it) }
            albumType?.let { track.putProperty(ALBUM_TYPE, it) }
            albumId?.let { track.putProperty(ALBUM_ID, it) }
            track.save()
        }
    }

    /**
     *  Create pager
     */
    @JvmStatic
    fun trackCreateAlbumBtnClick(pagerId: String?) {
        track(CLICK_CREATE_ALBUM_BTN) { track ->
            pagerId?.let { track.putProperty(PAGE_ID, it) }
            track.save()
        }
    }


    @JvmStatic
    fun trackCreateAlbumPagerClick(pagerId: String? = null, operType: String? = null, operResult: String? = null) {
        track(CLICK_CREATE_ALBUM_PAGER) { track ->
            pagerId?.let { track.putProperty(PAGE_ID, it) }
            operType?.let { track.putProperty(OPER_TYPE, it) }
            operResult?.let { track.putProperty(OPER_RESULT, it) }
            track.save()
        }
    }


    /**
     *  用户选择文件上传时上报
     */
    fun trackSelectFileUpload(albumId: String, albumType: Int, uploadWay: String, photoCount: Int, videoCount: Int) {
        track(SHARED_TIMELINE_UPLOAD_FILE) {
            it.putProperty(ALBUM_ID, albumId)
            it.putProperty(ALBUM_TYPE, SharedAlbumTrackConstant.getAlbumType(albumType))
            it.putProperty(UPLOAD_WAY, uploadWay)
            it.putProperty(PHOTO_COUNT, photoCount)
            it.putProperty(VIDEO_COUNT, videoCount)
        }
    }

    /**
     *  点击图集内多选状态页按钮时上报
     */
    fun trackSelectModePage(albumId: String, albumType: Int, button: String, photoCount: Int, videoCount: Int) {
        track(SHARED_TIMELINE_PAGE_SELECT_MODE) {
            it.putProperty(ALBUM_ID, albumId)
            it.putProperty(ALBUM_TYPE, SharedAlbumTrackConstant.getAlbumType(albumType))
            it.putProperty(BUTTON, button)
            it.putProperty(PHOTO_COUNT, photoCount)
            it.putProperty(VIDEO_COUNT, videoCount)
        }
    }

    /**
     * 点击图集内页按钮时上报
     */
    fun trackSharedTimelineButton(albumId: String, albumType: Int, button: String, toast: Int) {
        track(SHARED_TIMELINE_PAGE_BUTTON_CLICK) {
            it.putProperty(ALBUM_ID, albumId)
            it.putProperty(ALBUM_TYPE, SharedAlbumTrackConstant.getAlbumType(albumType))
            it.putProperty(BUTTON, button)
            if (toast != ERROR_NONE) {
                it.putProperty(TOAST, toast)
            }
        }
    }

    /**
     * 共享图集内页顶部文案曝光
     */
    fun trackSharedTimelineTopTips(albumId: String, albumType: Int, trackMap: Map<String, Any?>) {
        track(SHARED_TIMELINE_PAGE_TOP_TIPS) {
            it.putProperty(ALBUM_ID, albumId)
            it.putProperty(ALBUM_TYPE, SharedAlbumTrackConstant.getAlbumType(albumType))
            trackMap.forEach { map ->
                it.putProperty(map.key, map.value)
            }
        }
    }


    private fun track(eventId: String, @WorkerThread func: ((SingleTrack) -> Unit)? = null) {
        Tracker.trackStore.createSingleTrack(
            event = eventId,
            type = TYPE_SHARED_ALBUM,
            func = func
        )
    }
}