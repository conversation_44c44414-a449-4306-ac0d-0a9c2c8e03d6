/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedFeatureUtils
 ** Description:SharedFeatureUtils
 ** Version: 1.0
 ** Date : 2022/8/16
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/8/16        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.util

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.REGION_MARK

/**
 * 共享图集专用
 */
object SharedFeatureUtils {
    private const val VALUE_REGION_CN = "CN"

    /**
     * regionmark在Q上面获取不到,共享图集不上外销，可以默认设置 "CN"
     */
    @JvmStatic
    val regionMark: String by lazy {
        ConfigAbilityWrapper.getString(REGION_MARK, VALUE_REGION_CN) ?: VALUE_REGION_CN
    }
}