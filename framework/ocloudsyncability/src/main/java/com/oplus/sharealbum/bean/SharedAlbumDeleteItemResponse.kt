/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumDeleteItemResponse
 ** Description:SharedAlbumDeleteItemResponse
 ** Version: 1.0
 ** Date : 2022/4/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/29        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.bean

import androidx.annotation.Keep

@Keep
data class SharedAlbumDeleteItemResponse(
    /**
     * 操作失败的globalId
     */
    val failIds: List<String>,
    /**
     * 无操作权限的文件的globalId
     */
    val authFails: List<String>
)
