/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumConfigHelper
 ** Description:
 ** Version: 1.0
 ** Date : 2022/5/6
 ** Author: chenjie@A<PERSON>.KeKeCloud
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  chenjie@A<PERSON>.KeKeCloud            2022/5/6        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.helper

import androidx.annotation.WorkerThread
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.util.io.IOUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.sharealbum.bean.SharedAlbumConfigResponse
import com.oplus.sharealbum.bean.SharedAlbumRequest
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.net.callExt
import com.oplus.sharealbum.service.ISharedAlbumService

object SharedAlbumConfigHelper {

    private var sharedAlbumConfig: SharedAlbumConfigResponse? = null

    @WorkerThread
    fun init() {
        val service = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
        val apiResponse = service.getConfig(SharedAlbumRequest()).callExt()
        if (apiResponse.isSuccess) {
            sharedAlbumConfig = apiResponse.data ?: SharedAlbumConfigResponse()
        }
    }

    /**
     * 查询共享图集是否下架
     * true-未下架，false-下架
     */
    @WorkerThread
    fun checkIsOnline(): Boolean {
        val service = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
        val response = service.isOnline(SharedAlbumRequest()).callExt()
        if (response.isSuccess) {
            return response.data?.flag ?: true
        }
        return true
    }

    /**
     * 获取共享图集配置
     */
    fun getConfig(): SharedAlbumConfigResponse {
        return sharedAlbumConfig ?: SharedAlbumConfigResponse().apply {
            sharedAlbumConfig = this
        }
    }

    /**
     * 判断共享图集功能是否授权
     */
    @JvmStatic
    fun isPrivacyAuthorized(): Boolean {
        val ability = (ContextGetter.context as GalleryApplication).getAppAbility<IPrivacyAuthorizingAbility>()
        ability?.let {
            val isPrivacyAuthorized = it.isPrivacyAuthorized(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_SHARE_ALBUM)
            IOUtils.closeQuietly(it)
            return isPrivacyAuthorized == true
        }
        return false
    }
}