/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ISharedAlbumMetaDataListener
 ** Description:ISharedAlbumMetaDataListener
 ** Version: 1.0
 ** Date : 2022/4/27
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/27        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.sharedsync.metadata

import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.sharealbum.state.CloudKitSharedState

interface ISharedAlbumMetaDataListener {

    /**
     * 获取所有的图集id
     */
    fun onGetAllAlbumGroup(): List<String>

    /**
     * 获取当前图集里待删除的元数据
     */
    fun onGetBackupDeleteList(albumId: String): List<SharedAlbumEntity>

    /**
     * 获取当前图集里待上云的元数据（根据createTime分组）
     */
    fun onGetBackupAddList(albumId: String): MutableMap<Long, List<SharedAlbumEntity>>

    /**
     * 元数据新增上传
     */
    fun onMetaDataBackupAdd(albumId: String, createTime: Long, data: List<SharedAlbumEntity>)

    /**
     * 元数据删除
     */
    fun onMetaDataBackupDelete(albumId: String, data: List<SharedAlbumEntity>)

    /**
     * 更新同步状态
     */
    fun updateSyncState(albumId: String, isUploading: Boolean, result: CloudKitSharedState)
}