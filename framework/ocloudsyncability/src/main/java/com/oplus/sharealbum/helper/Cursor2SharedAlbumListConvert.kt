/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - Cursor2SharedAlbumListConvert
 ** Description:
 ** Version: 1.0
 ** Date : 2022/4/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/4/15        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.helper

import android.database.Cursor
import com.google.gson.reflect.TypeToken
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumFileResponse
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumUserResponse
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.key.SharedMediaCacheKey
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.os.ContextGetter

class Cursor2SharedAlbumListConvert : IConvert<Cursor, List<SharedAlbumResponse>> {

    override fun convert(cursor: Cursor?): List<SharedAlbumResponse> {
        val result = ArrayList<SharedAlbumResponse>()
        if ((cursor == null) || (cursor.count <= 0)) {
            return result
        }
        val cachingAbility = ContextGetter.context.getAppAbility<ICachingAbility>() ?: return result
        runCatching {
            var item: SharedAlbumResponse
            val albumIdIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.ALBUM_ID)
            val albumNameIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.ALBUM_NAME)
            val ownerIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.ALBUM_CREATOR)
            val isCreatorIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.IS_CREATOR)
            val typeIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.ALBUM_TYPE)
            val coverFileIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.ALBUM_COVER_FILE)
            val countMapIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.ALBUM_COUNT_MAP)
            val fileCountIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.ALBUM_FILE_COUNT)
            val changeNameIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.PERMISSION_CHANGE_NAME)
            val changeCoverIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.PERMISSION_CHANGE_COVER)
            val inviteOtherIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.PERMISSION_INVITE_OTHER)
            val uploadFileIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.PERMISSION_UPLOAD_FILE)
            val saveTimeIndex = cursor.getColumnIndex(GalleryStore.SharedAlbumColumns.SAVE_TIME)
            while (cursor.moveToNext()) {
                item = SharedAlbumResponse()
                item.albumId = cursor.getString(albumIdIndex)
                item.albumName = cursor.getString(albumNameIndex)
                item.owner = JsonUtil.fromJson(cursor.getString(ownerIndex), SharedAlbumUserResponse::class.java)
                //0和1表示false或true
                item.master = cursor.getInt(isCreatorIndex) == 1
                item.type = cursor.getInt(typeIndex)
                item.coverFile = JsonUtil.fromJson(cursor.getString(coverFileIndex), SharedAlbumFileResponse::class.java)
                item.counts = JsonUtil.fromJson(cursor.getString(countMapIndex), object : TypeToken<Map<Int, Int>>() {}.type)
                item.allCount = cursor.getInt(fileCountIndex)
                item.changeName = cursor.getInt(changeNameIndex)
                item.changeCover = cursor.getInt(changeCoverIndex)
                item.inviteOther = cursor.getInt(inviteOtherIndex)
                item.uploadFile = cursor.getInt(uploadFileIndex)
                item.extSaveTime = cursor.getLong(saveTimeIndex)
                item.isCoverThumbnailDownloaded = item.coverFile?.fileId?.let {
                    cachingAbility.thumbnailCache?.exists(SharedMediaCacheKey(it), CacheOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL))
                } == true
                result.add(item)
            }
        }.onFailure {
            GLog.e(TAG, "convert, error: ${it.message}")
        }
        cachingAbility.close()
        return result
    }

    companion object {
        const val TAG = "Cursor2SharedAlbumListConvert"
    }
}