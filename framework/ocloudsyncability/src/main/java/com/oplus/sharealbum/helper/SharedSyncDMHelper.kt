/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedSyncDMHelper
 ** Description:
 ** Version: 1.0
 ** Date : 2022/7/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/7/29        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.helper

import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.source.DataManager.getMediaSet
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.share.SharedAlbum
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.mask.DisplayNameMask
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object SharedSyncDMHelper {

    private const val TAG = "SharedSyncDMHelper"

    fun getSharedAlbums(count: Int): List<SharedAlbum> {
        val repository = SharedAlbumListRepository()
        val responseList = repository.queryAllAlbumResponse()

        if (responseList.isEmpty()) {
            AppScope.launch(Dispatchers.IO) {
                repository.reqSharedAlbumList(count, TextUtil.EMPTY_STRING, 0, {
                    val album = it?.album ?: ArrayList()
                    val size = album.size
                    GLog.d(TAG, "getSharedAlbums, reqSharedAlbumList success: size = $size")
                    if (size > 0) {
                        repository.addThumbTask(album)
                    }
                }, { code, msg ->
                    GLog.e(TAG, "getSharedAlbums, reqSharedAlbumList fail: code = $code, msg = $msg")
                })
            }
        } else {
            AppScope.launch(Dispatchers.IO) {
                repository.addThumbTask(responseList)
            }
        }
        return responseList.map {
            GLog.d(TAG, "getSharedAlbums name:${DisplayNameMask.mask(it.albumName)}, path:${PathMask.mask(it.coverFile?.filePath)}")
            val path: Path = SourceConstants.Shared.PATH_ALBUM_ALL_ANY.getChild(it.albumId)
            (getMediaSet(path) as SharedAlbum).apply { albumResponse = it }
        }
    }
}