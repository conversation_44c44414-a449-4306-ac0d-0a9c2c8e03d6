/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedTabHttpHeaderUtils
 ** Description:SharedTabHttpHeaderUtils
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  yang<PERSON>@Apps.KeKeCloud             2022/3/30        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.util

import android.os.Build
import android.os.Process
import android.text.TextUtils
import com.oplus.gallery.foundation.database.helper.PrivacyPersonalInfoTableHelper
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.stdid.StdIdManager
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.marketName
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.osVersion
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.otaVersion
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.phoneModel
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils.region
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.version.AppVersionUtils.getVersionCode
import com.oplus.gallery.framework.abilities.ocloudsync.account.AccountManager
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.sharealbum.net.HttpConstants
import com.oplus.sharealbum.util.SharedFeatureUtils.regionMark
import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.util.Locale
import kotlin.collections.HashMap

object SharedTabHttpHeaderUtils {
    private const val TAG = "SharedTabHttpHeaderUtils"
    private const val CLOUD_PER_USER_RANGE = 100000 // 此值等于UserHandle#PER_USER_RANGE值
    private const val CLOUD_OPEN_ID_PRE = "OPENID-"
    private const val SHOW_TYPE_NO_BRADN = "1" // H5中不显示品牌
    private const val SHOW_TYPE_HETYAP = "2" // H5中显示HeyTap品牌

    @JvmStatic
    fun addCommonHeader(headers: HashMap<String, String>) {
        runCatching {
            val context = ContextGetter.context
            val timestamp = System.currentTimeMillis().toString()
            val location = getLocation()
            headers[HttpConstants.HEADER_CONTENT_TYPE] = "application/json"
            headers[HttpConstants.HEADER_TOKEN] = AccountManager.getAccountInfo().token
            headers[HttpConstants.HEADER_TIMESTAMP] = timestamp
            headers[HttpConstants.HEADER_OCLOUD_LOCATION] = location
            headers[HttpConstants.HEADER_OCLOUD_OCLOUD_LANGUAGE] = location
            headers[HttpConstants.HEADER_VERSION] = getVersionCode(context).toString()
            headers[HttpConstants.HEADER_MODEL] = getDeviceMarketNameByURLEncoder() ?: ""
            headers[HttpConstants.HEADER_BRAND] = Build.BRAND
            headers[HttpConstants.HEADER_BUILD_MODEL] = phoneModel
            headers[HttpConstants.HEADER_OS_VERSION] = osVersion
            headers[HttpConstants.HEADER_OCLOUD_GRAY_VERSION] = HttpConstants.HEADER_OCLOUD_GRAY_VERSION_2
            headers[HttpConstants.HEADER_OCLOUD_REGION_MARK] = regionMark
            headers[HttpConstants.HEADER_OCLOUD_REGION] = region ?: ""
            headers[HttpConstants.HEADER_CLOUD_PACKAGE_NAME] = context.packageName ?: ""
            headers[HttpConstants.HEADER_APP_VERSION] = getVersionCode(context).toString()
            headers[HttpConstants.HEADER_H5_BRAND_SHOW_TYPE] = getH5BrandShowType()
            headers[HttpConstants.HEADER_IMEI] = getTabHttpImei()
            headers[HttpConstants.HEADER_DUID] = StdIdManager.INSTANCE.duId
            headers[HttpConstants.HEADER_OUID] = StdIdManager.INSTANCE.ouid
            headers[HttpConstants.HEADER_OCLOUD_SYSTEM_OS] = HttpConstants.SHOW_TYPE_OP
            PrivacyPersonalInfoTableHelper.insertCommonHeaderData(
                token = headers[HttpConstants.HEADER_TOKEN],
                udid = StdIdManager.INSTANCE.guId,
                oaid = StdIdManager.INSTANCE.ouid
            )
            val phoneOtaVersion = otaVersion
            if (!TextUtils.isEmpty(phoneOtaVersion)) {
                try {
                    headers[HttpConstants.HEADER_OCLOUD_OTA_VERSION] = URLEncoder.encode(phoneOtaVersion, "UTF-8")
                } catch (e: UnsupportedEncodingException) {
                    GLog.e(TAG, "addCommonHeader encode e = ${e.message}")
                }
            }
        }.onFailure {
            GLog.e(TAG, "addCommonHeader e = ${it.message}")
        }
    }

    @JvmStatic
    fun buildHeadersNoEncryption(): HashMap<String, String> {
        val headers = HashMap<String, String>()
        headers[HttpConstants.HEADER_CONTENT_TYPE] =
            "application/json"
        headers[HttpConstants.HEADER_BRAND] = Build.BRAND
        headers[HttpConstants.HEADER_OCLOUD_SYSTEM_OS] =
            HttpConstants.SHOW_TYPE_OP
        return headers
    }

    @JvmStatic
    private fun getTabHttpImei(): String {
        return if (TextUtils.isEmpty(StdIdManager.INSTANCE.guId)) {
            TextUtil.EMPTY_STRING
        } else {
            val systemUserId: Int = Process.myUid() / CLOUD_PER_USER_RANGE
            CLOUD_OPEN_ID_PRE + StdIdManager.INSTANCE.guId + systemUserId
        }
    }

    @JvmStatic
    private fun getLocation(): String {
        return Locale.getDefault().language + "_" + Locale.getDefault().country
    }

    /**
     * called in http interface
     * @return
     */
    @JvmStatic
    private fun getDeviceMarketNameByURLEncoder(): String? {
        var deviceMarketName = marketName
        if (!TextUtils.isEmpty(deviceMarketName)) {
            try {
                deviceMarketName = URLEncoder.encode(deviceMarketName, "UTF-8")
            } catch (e: UnsupportedEncodingException) {
                GLog.e(TAG, "getDeviceMarketNameByURLEncoder UnsupportedEncodingException e = ${e.message}")
            }
        }
        GLog.d(TAG, "getDeviceMarketNameByURLEncoder marketName = $deviceMarketName")
        return deviceMarketName
    }

    /**
     * 获取H5显示品牌的风格
     * @return
     */
    @JvmStatic
    private fun getH5BrandShowType(): String {
        return if (PROPERTY_IS_OP_BRAND) {
            SHOW_TYPE_NO_BRADN
        } else {
            SHOW_TYPE_HETYAP
        }
    }
}