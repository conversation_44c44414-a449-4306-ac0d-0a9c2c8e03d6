/***************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * OPLUS_EDIT
 * File: - ApiResponse
 * Description:ApiResponse
 * Version: 1.0
 * Date : 2022/3/30
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------------------------------------------
 * <author>                       <data>         <version>     <desc>
 * <EMAIL>             2022/3/30        1.0             Add
 ****************************************************************/
package com.oplus.sharealbum.net

import com.google.gson.annotations.SerializedName
import com.oplus.sharealbum.bean.SharedAlbumResult
import java.io.Serializable

class ApiResponse<T> : Serializable {
    @SerializedName("code")
    var code = 0

    @SerializedName("errmsg")
    var errMsg: String? = null

    @SerializedName("data")
    var data: T? = null


    override fun toString(): String {
        return "ApiResponse(code=$code, errMsg=$errMsg, data=$data)"
    }

    val isSuccess by lazy { code == SharedAlbumResult.SUCCESS }

    companion object {
        private const val serialVersionUID = 1L
        fun <M> error(throwable: Throwable): ApiResponse<M> = ApiResponse<M>().apply {
            code = -1
            errMsg = throwable.toString()
        }

        fun <M> createSuccessResponse(data: M): ApiResponse<M> = ApiResponse<M>().apply {
            code = SharedAlbumResult.SUCCESS
            this.data = data
        }
    }
}