/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PushInitCheckUtils
 ** Description:PushInitCheckUtils
 ** Version: 1.0
 ** Date : 2022/5/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/5/19         1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.util

import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.framework.abilities.ocloudsync.utils.ShareAlbumHelper

object PushInitCheckUtils {
    /**
     * 需要push操作时需要检测push init操作是否完成
     */
    @JvmStatic
    @Synchronized
    fun checkPushInit() {
        ApiDmManager.getPushDM().getRegisterId()?.run {
            ShareAlbumHelper.onPushRegister(this)
        }
    }
}