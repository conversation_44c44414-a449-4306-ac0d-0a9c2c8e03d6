/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumTrackConstant
 ** Description:SharedAlbumTrackConstant
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/30        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.track

import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.ALBUMS_CHOOSING_PAGE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.ALBUMS_PAGE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.ALBUMS_TAB
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.CREATE_PAGER_FROM_ALBUMS_TAB_PAGE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.CREATE_PAGER_FROM_CHOOSING_PAGE
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.FAMILY
import com.oplus.sharealbum.track.SharedAlbumTrackConstant.Value.OTHERS

object SharedAlbumTrackConstant {

    // 埋点文档地址：https://odocs.myoas.com/sheets/KrkEVzYmdDcbP7AJ/MODOC?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI4MDI5OTgwNiIsImlzcyI6IlRHVC04MjYwNjItSWJVYWlPTE81djMyclZQZTM5ek5pVHJkMWhrbTFZbUlGZmwyWHozNVR4SVlOQUY2N08tU0lBTSIsIm5hbWUiOiJTSUFNVEdUIiwidHlwZSI6IlRHVCIsImlhdCI6MTY1MTA0NDg1M30.UG8VRXbY9rSHTrId64K7G-XX9q617ZzBX8G5sVMEvGs&tt_lang=zh-CN&tt_zone=%2B8&new_client=1&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI4MDI5OTgwNiIsImlzcyI6IlRHVC04MjYwNjItSWJVYWlPTE81djMyclZQZTM5ek5pVHJkMWhrbTFZbUlGZmwyWHozNVR4SVlOQUY2N08tU0lBTSIsIm5hbWUiOiJTSUFNVEdUIiwidHlwZSI6IlRHVCIsImlhdCI6MTY1MTA0NDcyNn0.YBYXWI1_QpUDLUBrkYPNEpDDd3Xzf_3sTDRhlLffdw4&tt_lang=zh-CN&tt_zone=%2B8&new_client=1&client_id=100097&expire_time=1651048325996&msgShowStyle=31&ticket=APPURLWITHTICKET81d7612a70c3983d5754f810667741d0&client_id=100097&expire_time=1651048453832&msgShowStyle=31

    /**
     * 大类ID
     */
    const val TYPE_SHARED_ALBUM = "2006021"


    /**
     * 事件的id，对应埋点的事件名称
     */
    object EventId {
        /**
         * 设置页
         */
        const val CLICK_SETTING_PAGER = "2006021012"

        /**
         *  新建按钮点击
         */
        const val CLICK_CREATE_ALBUM_BTN = "2006021003"

        /**
         * 新建共享图集面板点击
         */
        const val CLICK_CREATE_ALBUM_PAGER = "**********"

        /**
         * 共享图集页曝光
         */
        const val SHARED_ALBUM_PAGER = "**********"

        /**
         * 邀请方式选择
         */
        const val INVITE_CHANNEL = "**********"

        /**
         * 微信邀请结果
         */
        const val WECHAT_INVITE_RESULT = "**********"

        /**
         * 账号邀请页面点击
         */
        const val CLICK_ACCOUNT_INVITE_PAGER = "**********"

        /**
         * 共享邀请处理
         */
        const val INVITE_MSG_OPER = "**********"

        /**
         * 共享成员页操作
         */
        const val CLICK_SHARED_MEMBER_PAGER = "**********"

        /**
         * 文件上传数量
         */
        const val SHARED_TIMELINE_UPLOAD_FILE = "**********"

        /**
         *  图集内多选状态页点击操作
         */
        const val SHARED_TIMELINE_PAGE_SELECT_MODE = "**********"

        /**
         * 图集内页操作
         */
        const val SHARED_TIMELINE_PAGE_BUTTON_CLICK = "**********"

        /**
         * 共享图集内页顶部文案曝光
         */
        const val SHARED_TIMELINE_PAGE_TOP_TIPS = "**********"
    }

    object Key {
        const val BUTTON = "button"
        const val FAQ_QUESTION = "faq_question"
        const val FAQ_ID = "faq_id"
        const val DOUBLE_CHECK = "double_check"
        const val ALBUM_TYPE = "album_type"
        const val ALBUM_ID = "album_id"

        const val PAGE_ID = "page_id"
        const val OPER_TYPE = "oper_type"
        const val OPER_RESULT = "oper_result"
        const val ENTER_ID = "enter_id"
        const val IS_BLANK = "is_blank"
        const val PAGE_STAY_TIME = "page_stay_time"
        const val INVITATION_CHANNEL = "invitation_channel"
        const val ACCOUNT_COUNT = "account_count"
        const val INVITATION_ID = "invitation_id"
        const val TAB = "tab"
        const val PANEL = "panel"

        const val UPLOAD_WAY = "upload_way"
        const val PHOTO_COUNT = "photo_count"
        const val VIDEO_COUNT = "video_count"
        const val TOAST = "abnormal_toast"
        const val TEXT = "text"
        const val DURATION = "duration"
        const val END_TYPE = "end_type"
    }

    object Value {
        const val RENAME = "rename"
        const val DECLARATION = "declaration"
        const val FAQ = "faq"
        const val DELETE = "delete"
        const val QUITE = "quit"
        const val CANCEL = "cancel"
        const val CONFIRM = "confirm"
        const val FAMILY = "family"
        const val OTHERS = "others"
        const val CONTACTS = "contacts"
        const val RECENT = "recent"
        const val RECOMMEND = "recommend"
        const val WITHDRAW = "withdraw"
        const val INVITE_AGAIN = "invite_again"

        const val ALBUMS_CHOOSING_PAGE = "albums_choosing_page"
        const val ALBUMS_TAB = "albums_tab"
        const val ALBUMS_PAGE = "albums_page"
        const val SAVE = "save"
        const val SUCCESS = "success"
        const val FAIL_ = "fail_"
        const val FAIL_01 = "fail_01"
        const val FAIL_02 = "fail_02"
        const val FAIL_03 = "fail_03"
        const val FAIL_04 = "fail_04"
        const val FAIL_05 = "fail_05"

        /**
         * 分享时候的创建
         */
        const val CREATE_PAGER_FROM_CHOOSING_PAGE = 1

        /**
         * 相册页的创建
         */
        const val CREATE_PAGER_FROM_ALBUMS_TAB_PAGE = 2

        /**
         * 共享图集内的tab创建
         */
        const val CREATE_PAGER_FROM_ALBUMS_PAGE = 3

        const val INVITATION_NOTIC = "invitation_notic"
        const val ALBUM_TAB_SHARED_ALBUM = "album_tab_shared_album"
        const val ALBUM_TAB_CREATE = "album_tab_create"
        const val ALBUM_SEND_TO = "album_send_to"
        const val CLOUD_SERVICE_QUICK_APP = "cloud_service_quick_app"

        const val PHOTOS_PAGE = "photos_page"
        const val MEMBERS_PAGE = "member_page"
        const val REINVITE = "reinvite"

        const val ACCOUNT = "account"
        const val WECHAT = "WeChat"

        const val NEWS_PAGE = "news_page"
        const val INVITATION_NOTICE = "invitation_notice"

        /**
         * 忽略邀请
         */
        const val INVITE_MSG_OPER_REFUSE = 0

        /**
         * 接受邀请
         */
        const val INVITE_MSG_OPER_ACCEPT = 1

        const val MEMBER_MANAGEMENT = "member_management"
        const val INVITATION_MANAGEMENT = "invitation_management"
        const val MEMBER_DELETE = "member_delete"
        const val INVITE_EXPIRED = "invite_expired"
        const val INVITE_UNEXPIRE = "invite_unexpire"
        const val INVITE_INVALID = "invite_invalid"


        /**
         * 通过相册发送面板上传
         */
        const val UPLOAD_WAY_SEND_TO = "send_to"

        /**
         * 在共享图集文件列表页内上传
         */
        const val UPLOAD_WAY_PHOTOS_PAGE = "photos_page"

        /**
         * 在创建共享图集流程中上传
         */
        const val UPLOAD_WAY_NEW_CREATED = "new_created"

        /**
         * 下载
         */
        const val DOWNLOAD = "download"


        /**
         * 邀请成员按钮
         */
        const val BUTTON_INVITE = "invite"

        /**
         * 选择（包括点击按钮或长按文件）
         */
        const val BUTTON_SELECT = "select"

        /**
         * 共享成员
         */
        const val BUTTON_MEMBERS = "members"

        /**
         * 设置
         */
        const val BUTTON_SETTING = "setting"

        /**
         * 文件上传按钮
         */
        const val BUTTON_UPLOAD = "upload"

        const val ERROR_NONE = 0

        /**
         * 无网络
         */
        const val ERROR_NO_NETWORK = 1

        /**
         * 网络异常
         */
        const val ERROR_NETWORK = 2

        /**
         * 服务异常
         */
        const val ERROR_SERVICE = 3

        /**
         * 图集成员已达上限
         */
        const val ERROR_MEMBER_LIMIT = 4

        /**
         * 图集文件数量已达上限
         */
        const val ERROR_FILE_LIMIT = 5

        /**
         * 家庭图集不支持在此操作
         */
        const val ERROR_FAMILY = 6

        /**
         * 离开时间轴页面
         */
        const val END_TYPE_LEAVE = 0

        /**
         * 用户划动照片列表
         */
        const val END_TYPE_TOUCH = 1

        /**
         * 文案按钮被点击
         */
        const val END_TYPE_CLICK = 2
    }

    fun getAlbumType(type: Int): String {
        return if (type == GalleryStore.SharedAlbumColumns.TYPE_FAMILY) {
            FAMILY
        } else {
            OTHERS
        }
    }

    fun getCreateAlbumType(type: Int): String {
        return when (type) {
            CREATE_PAGER_FROM_CHOOSING_PAGE -> ALBUMS_CHOOSING_PAGE
            CREATE_PAGER_FROM_ALBUMS_TAB_PAGE -> ALBUMS_TAB
            else -> ALBUMS_PAGE
        }
    }
}