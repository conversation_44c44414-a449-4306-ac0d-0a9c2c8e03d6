/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedFilePathUtil
 ** Description:SharedFilePathUtil
 ** Version: 1.0
 ** Date : 2022/4/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/19        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.util

import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.DATA
import com.oplus.gallery.foundation.database.util.ConstantUtils.ORDER_DESC
import com.oplus.gallery.foundation.database.util.ConstantUtils.LIKE
import com.oplus.gallery.foundation.database.util.ConstantUtils.LIKE_PERCENT
import com.oplus.gallery.foundation.database.util.ConstantUtils.VARIABLE_PLACEHOLDER
import com.oplus.gallery.foundation.dbaccess.convert.StringConverter
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Path.PATH_SHARED_ALBUM_FILE_THUMB_CACHE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils

object SharedFilePathUtil {
    private const val TAG = "SharedFilePathUtil"
    private val DOWNLOAD_ORIGIN_FOLDER = OplusEnvironment.getInternalPath() + Dir.SHARED_ALBUM.bucketPath
    private val DOWNLOAD_THUMB_FOLDER = OplusEnvironment.getInternalPath() + PATH_SHARED_ALBUM_FILE_THUMB_CACHE
    private const val THUMB_EXTENSION = ".jpg"
    private const val SEPARATOR = "_"
    private const val MAX_CREATE_NAME_COUNT = 10 // 最多尝试创建名字的次数

    /**
     * 创建一个本地还不存在文件的全新的文件名
     * 文件名拼上时间戳(毫秒级)+index，最多尝试[MAX_CREATE_NAME_COUNT]次，理论上即使是列表下载还能出现文件已存在的概率很低了，
     * 即使发生也只是重复文件不再保存，业务可通过[findNewestOriginalSavePath]找到已存的内容相同的文件
     */
    @JvmStatic
    fun createOriginalSavePath(file: CloudIOFile): String? {
        val filePath = file.filePath ?: return TextUtil.EMPTY_STRING
        if (!FileOperationUtils.ensureMakeDirectory(DOWNLOAD_ORIGIN_FOLDER)) {
            GLog.e(TAG, "getSavePath ensureMakeDirectory failed")
            return null
        }
        var result: String? = null
        for (i in 0 until MAX_CREATE_NAME_COUNT) {
            result = DOWNLOAD_ORIGIN_FOLDER + File.separator +
                    "Shared$SEPARATOR${file.md5}$SEPARATOR${System.currentTimeMillis()}$SEPARATOR$i" +
                    ".${FilePathUtils.getFilePathSuffix(filePath)}"
            if (!File(result).exists()) break
        }
        if (result == null) {
            GLog.e(TAG, "createOriginalSavePath fail!")
        }
        return result
    }

    /**
     * 找到md5相同前提下，最新下载的原文件保存路径
     */
    @JvmStatic
    fun findNewestOriginalSavePath(md5: String): String? {
        if (md5.isEmpty()) return null
        val prefixPath = "$DOWNLOAD_ORIGIN_FOLDER${File.separator}Shared$SEPARATOR${md5}$SEPARATOR"
        return QueryReq.Builder<String>().setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(arrayOf(DATA))
            .setWhere("$DATA$LIKE$VARIABLE_PLACEHOLDER")
            .setWhereArgs(arrayOf("$prefixPath$LIKE_PERCENT"))
            .setOrderBy("$DATA$ORDER_DESC")
            .setLimit("1")
            .setConvert(StringConverter(DATA))
            .build()
            .exec()
    }

    @JvmStatic
    fun getShareThumbFilePath(filePath: String, fileId: String): String {
        if (!FileOperationUtils.ensureMakeDirectory(DOWNLOAD_THUMB_FOLDER)) {
            GLog.e(TAG, "getShareThumbFilePath ensureMakeDirectory failed")
            return TextUtil.EMPTY_STRING
        }
        return DOWNLOAD_THUMB_FOLDER + File.separator + fileId + SEPARATOR + FilePathUtils.getTitle(filePath) + THUMB_EXTENSION
    }
}