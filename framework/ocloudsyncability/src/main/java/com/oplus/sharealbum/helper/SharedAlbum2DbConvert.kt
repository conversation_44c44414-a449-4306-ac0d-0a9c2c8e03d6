/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbum2DbConvert
 ** Description:
 ** Version: 1.0
 ** Date : 2022/4/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/4/15        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.helper

import android.content.ContentValues
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse

class SharedAlbum2DbConvert : IConvert<SharedAlbumResponse, ContentValues> {

    override fun convert(original: SharedAlbumResponse): ContentValues {
        return ContentValues().apply {
            put(GalleryStore.SharedAlbumColumns.ALBUM_ID, original.albumId)
            put(GalleryStore.SharedAlbumColumns.ALBUM_NAME, original.albumName)
            put(GalleryStore.SharedAlbumColumns.ALBUM_CREATOR, JsonUtil.toJson(original.owner))
            put(GalleryStore.SharedAlbumColumns.IS_CREATOR, if (original.master) 1 else 0)
            put(GalleryStore.SharedAlbumColumns.ALBUM_TYPE, original.type)
            put(GalleryStore.SharedAlbumColumns.ALBUM_COVER_FILE, JsonUtil.toJson(original.coverFile))
            put(GalleryStore.SharedAlbumColumns.ALBUM_COUNT_MAP, JsonUtil.toJson(original.counts))
            put(GalleryStore.SharedAlbumColumns.ALBUM_FILE_COUNT, original.allCount)
            put(GalleryStore.SharedAlbumColumns.PERMISSION_CHANGE_NAME, original.changeName)
            put(GalleryStore.SharedAlbumColumns.PERMISSION_CHANGE_COVER, original.changeCover)
            put(GalleryStore.SharedAlbumColumns.PERMISSION_INVITE_OTHER, original.inviteOther)
            put(GalleryStore.SharedAlbumColumns.PERMISSION_UPLOAD_FILE, original.uploadFile)
            put(GalleryStore.SharedAlbumColumns.SAVE_TIME, original.extSaveTime)
            put(GalleryStore.SharedAlbumColumns.PRIORITY, 1)
        }
    }

    companion object {
        const val TAG = "SharedAlbum2DbConvert"
    }
}