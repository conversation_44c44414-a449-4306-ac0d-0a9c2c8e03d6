/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumMetaDataSync
 ** Description:SharedAlbumMetaDataSync
 ** Version: 1.0
 ** Date : 2022/3/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/3/24        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.sharedsync.metadata

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.sharealbum.bean.SharedAlbumAddItemResponse
import com.oplus.sharealbum.bean.SharedAlbumDeleteItemResponse
import com.oplus.sharealbum.bean.SharedAlbumResult
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.sharealbum.callback.ISharedAlbumReqCallback
import com.oplus.sharealbum.constant.SharedSyncConstant
import com.oplus.sharealbum.helper.OperateDataUtils
import com.oplus.sharealbum.helper.SharedAlbumListRepository
import com.oplus.sharealbum.helper.SharedDataProviderUtils
import com.oplus.sharealbum.state.CloudKitSharedState
import com.oplus.sharealbum.state.SharedSyncStatusManager

open class SharedAlbumMetaDataSync : ISharedAlbumMetaDataListener {

    companion object {
        private const val TAG = "SharedAlbumMetaDataSync"
    }

    override fun onGetAllAlbumGroup(): List<String> {
        GLog.d(TAG, "onGetAllAlbumGroup")
        return SharedDataProviderUtils.getAllSharedAlbums()
    }

    override fun onGetBackupDeleteList(albumId: String): List<SharedAlbumEntity> {
        GLog.d(TAG, "onGetBackupDeleteList")
        return SharedDataProviderUtils.getNeedDeleteDataById(albumId)
    }

    override fun onGetBackupAddList(albumId: String): MutableMap<Long, List<SharedAlbumEntity>> {
        GLog.d(TAG, "onGetBackupAddList")
        val data: MutableMap<Long, List<SharedAlbumEntity>> = mutableMapOf()
        val createTimes = SharedDataProviderUtils.getCreateTimeById(albumId)
        createTimes.forEach {
            val needUploadMetaData = SharedDataProviderUtils.getNeedUploadMetaData(albumId, it)
            if (needUploadMetaData.isNotEmpty() && checkDataIsComplete(needUploadMetaData)) {
                data[it] = needUploadMetaData
            }
        }
        return data
    }

    private fun checkDataIsComplete(list: List<SharedAlbumEntity>): Boolean {
        list.forEach {
            if (it.fileId.isNullOrEmpty()) {
                return false
            }
        }
        return true
    }

    override fun onMetaDataBackupAdd(albumId: String, createTime: Long, data: List<SharedAlbumEntity>) {
        GLog.d(TAG, "onMetaDataBackupAdd albumId:$albumId, createTime:$createTime, data.size:${data.size}")
        val localTransferServerUp = OperateDataUtils.localTransferServerUp(data)
        val repository = SharedAlbumListRepository()
        OperateDataUtils.backupOnAdd(
            albumId,
            createTime,
            localTransferServerUp,
            object : ISharedAlbumReqCallback<SharedAlbumAddItemResponse> {
                override fun sharedAlbumReqResult(data: SharedAlbumAddItemResponse?) {
                    if (data == null) {
                        GLog.e(TAG, "onMetaDataBackupAdd data == null")
                        return
                    }
                    // 上传成功后更新globalId
                    if (data.matchIds != null) {
                        GLog.d(TAG, "onMetaDataBackupAdd success size:${data.matchIds.size}")
                        SharedDataProviderUtils.updateGlobalId(data.matchIds)
                        // 元数据新增成功后，需要重新拉一下图集列表，刷新图集tab页共享图集展示
                        repository.reqSharedAlbumList(SharedSyncConstant.DEFAULT_PAGE_SIZE, TextUtil.EMPTY_STRING, 0, {
                            GLog.d(TAG, "onMetaDataBackupAdd reqSharedAlbumList success")
                        }, { code, msg ->
                            GLog.e(TAG, "onMetaDataBackupAdd reqSharedAlbumList fail: code = $code, msg = $msg")
                        })
                    }

                    if (data.expireIds != null) {
                        GLog.e(TAG, "onMetaDataBackupAdd expireIds size:${data.expireIds.size}")
                        // 上传的元数据包含io文件已经过期了的数据，需要清理fileId，重新走IO上传
                        SharedDataProviderUtils.clearFileId(data.expireIds)
                    }
                }

                override fun error(code: Int, mes: String?) {
                    GLog.e(TAG, "onMetaDataBackupAdd code : $code  message: $mes")
                    if (SharedAlbumResult.REQUEST_BEEN_PROCESSED == code) {
                        GLog.e(TAG, "onMetaDataBackupAdd error code : ${SharedAlbumResult.REQUEST_BEEN_PROCESSED}")
                        SharedDataProviderUtils.deleteHasUploadData(albumId, data)
                        return
                    }
                    // 家庭图集用户无权限操作
                    if (code == SharedAlbumResult.NO_PERMISSION_PERFORM_OPERATION && SharedDataProviderUtils.isFamilyAtlas(albumId)) {
                        updateSyncState(albumId, true, CloudKitSharedState.NO_PERMISSION)
                        return
                    }
                    updateSyncState(albumId, true, CloudKitSharedState.getByCode(code))
                }
            })
    }

    override fun onMetaDataBackupDelete(albumId: String, data: List<SharedAlbumEntity>) {
        GLog.d(TAG, "onMetaDataBackupDelete albumId = $albumId, data.size = ${data.size}")
        // 元数据删除
        val gidList: MutableSet<String> = HashSet()
        data.forEach {
            gidList.add(it.globalId ?: TextUtil.EMPTY_STRING)
        }
        OperateDataUtils.backupOnDelete(albumId, gidList, object : ISharedAlbumReqCallback<SharedAlbumDeleteItemResponse> {
            override fun sharedAlbumReqResult(data: SharedAlbumDeleteItemResponse?) {
                if (data != null) {
                    if (data.authFails.isNotEmpty()) {
                        GLog.e(TAG, "backupOnDelete authFails size:${data.authFails.size}")
                        gidList.removeAll(data.authFails.toSet())
                        // 取消待删除的数据
                        SharedDataProviderUtils.cancelToBeDel(albumId, data.failIds)
                    }
                    if (data.failIds.isNotEmpty()) {
                        GLog.e(TAG, "backupOnDelete failIds size:${data.failIds.size}")
                        gidList.removeAll(data.failIds.toSet())
                    }
                }
                if (gidList.isNotEmpty()) {
                    // 数据库删除成功删除的数据
                    GLog.d(TAG, "backupOnDelete success size:${gidList.size}")
                    SharedDataProviderUtils.deleteLocalData(albumId, gidList)
                }
            }

            override fun error(code: Int, mes: String?) {
                GLog.e(TAG, "backupOnDelete code : $code  message: $mes")
                if (code == SharedAlbumResult.NO_PERMISSION_PERFORM_OPERATION) {
                    SharedDataProviderUtils.cancelToBeDel(albumId, gidList.toList())
                }
                updateSyncState(albumId, true, CloudKitSharedState.getByCode(code))
            }
        })
    }

    override fun updateSyncState(albumId: String, isUploading: Boolean, result: CloudKitSharedState) {
        GLog.d(TAG, "updateSyncState albumId: $albumId result: $result")
        SharedSyncStatusManager.instance.putShareStateCache(albumId, isUploading, result)
    }
}