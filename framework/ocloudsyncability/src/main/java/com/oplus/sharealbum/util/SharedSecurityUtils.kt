/***************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * OPLUS_EDIT
 * File: - SharedSecurityUtils
 * Description:共享图集加密工具
 * Version: 1.0
 * Date : 2022/3/30
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------------------------------------------
 * <author>                       <data>         <version>     <desc>
 * <EMAIL>             2022/3/30        1.0             Add
 ****************************************************************/
package com.oplus.sharealbum.util

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import com.oplus.gallery.foundation.security.SecurityUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.ocloudsync.utils.CloudStatusPref
import java.io.IOException
import java.lang.IllegalStateException
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets
import java.security.Key
import java.security.KeyFactory
import java.security.KeyStore
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object SharedSecurityUtils {
    private const val SESSION_KEY_SIZE = 128
    private const val PUBLIC_KEY =
        "305c300d06092a864886f70d0101010500034b003048024100a323a0d84a9e9aade3b78145d69c7203a5098aa404" +
                "8bea0732e44536a463cff7073efd118726a5ddcb27ad4aa28ba0a64604193359fe97c48ac5cd2a81beb82f0203010001"
    private const val TAG = "SharedSecurityUtils"
    private const val STORE_TYPE_ANDROID = "AndroidKeyStore"
    private val SESSION_KEY = AES.genKey(SESSION_KEY_SIZE)
    private val iv = "1234567812345678".toByteArray(Charset.defaultCharset())

    @JvmStatic
    fun getEncryptSessionKey(context: Context): ByteArray? {
        return RSA.encryptByPublicKey(
            SESSION_KEY, CloudStatusPref.getTabHttpPublicKey(context) ?: PUBLIC_KEY
        )
    }

    @JvmStatic
    fun updatePublicKey(context: Context, publicKeyBytes: ByteArray?) {
        val publicKey: String
        if (publicKeyBytes != null) {
            publicKey = SecurityUtils.binToHex(publicKeyBytes)
            if (publicKey.length == PUBLIC_KEY.length) {
                GLog.d(TAG, "updatePublicKey= sucess")
                CloudStatusPref.setTabHttpPublicKey(context, publicKey)
            } else {
                GLog.d(TAG, "updatePublicKey= false")
            }
        }
    }

    @JvmStatic
    fun encryptBody(body: String?): ByteArray? {
        if (body == null) {
            GLog.w(TAG, "encryptBody body is null")
            return null
        }
        val bodyBytes: ByteArray = body.toByteArray(StandardCharsets.UTF_8)
        return AES.CBC.encrypt(bodyBytes, SESSION_KEY, iv)
    }

    @JvmStatic
    fun decryptBody(bodyBytes: ByteArray?): String? {
        if (bodyBytes != null) {
            val responseBytes = AES.CBC.decrypt(bodyBytes, SESSION_KEY, iv)
            return if (responseBytes != null) {
                String(responseBytes, StandardCharsets.UTF_8)
            } else {
                null
            }
        }
        return null
    }

    /**
     * 通过别名方式从KeyStore获取秘钥为数据加密，这种方式能较大程度保证秘钥的安全性，当前默认使用[AES.CTR]
     * @param data 需要加密的数据
     * @param keyAlias 秘钥的别名
     */
    @JvmStatic
    fun encryptWithKeyStore(data: String, keyAlias: String): String? = encryptWithKeyStore(data.toByteArray(), keyAlias)?.let {
        SecurityUtils.binToHex(it)
    }

    /**
     * 通过别名方式从KeyStore获取秘钥为数据加密，这种方式能较大程度保证秘钥的安全性，当前默认使用[AES.CTR]
     * @param data 需要加密的数据
     * @param keyAlias 秘钥的别名
     */
    @JvmStatic
    fun encryptWithKeyStore(data: ByteArray, keyAlias: String): ByteArray? = AES.CTR.encryptWithKeyStore(data, keyAlias)

    /**
     * 通过别名方式从KeyStore获取秘钥为数据解密，这种方式能较大程度保证秘钥的安全性，当前默认使用[AES.CTR]
     * @param data 需要解密的数据
     * @param keyAlias 秘钥的别名
     * @return 解密后的字符串
     */
    @JvmStatic
    fun decryptWithKeyStore(data: String, keyAlias: String): String? = decryptWithKeyStore(SecurityUtils.hexToBin(data), keyAlias)?.let {
        String(it)
    }

    /**
     * 通过别名方式从KeyStore获取秘钥为数据解密，这种方式能较大程度保证秘钥的安全性，当前默认使用[AES.CTR]
     * @param data 需要解密的数据
     * @param keyAlias 秘钥的别名
     * @return 解密后的字节数组
     */
    @JvmStatic
    fun decryptWithKeyStore(data: ByteArray, keyAlias: String): ByteArray? = AES.CTR.decryptWithKeyStore(data, keyAlias)

    object AES {
        private const val KEY_AES = "AES"

        @JvmStatic
        fun genKey(size: Int): ByteArray? {
            return runCatching {
                val e = KeyGenerator.getInstance(KEY_AES)
                e.init(size)
                val skey = e.generateKey()
                skey.encoded
            }.onFailure {
                throw IOException(it)
            }.getOrNull()
        }

        object CBC {
            private const val KEY_CBC = "AES/CBC/PKCS5Padding"

            @JvmStatic
            fun encrypt(data: ByteArray?, key: ByteArray?, iv: ByteArray?): ByteArray? {
                return runCatching {
                    val e = SecretKeySpec(key, KEY_AES)
                    val cipher = Cipher.getInstance(KEY_CBC)
                    val params = IvParameterSpec(iv)
                    cipher.init(Cipher.ENCRYPT_MODE, e, params)
                    cipher.doFinal(data)
                }.onFailure {
                    throw IOException(it)
                }.getOrNull()
            }

            @JvmStatic
            fun decrypt(encryptedData: ByteArray?, key: ByteArray?, iv: ByteArray?): ByteArray? {
                return runCatching {
                    val e = SecretKeySpec(key, KEY_AES)
                    val cipher = Cipher.getInstance(KEY_CBC)
                    val params = IvParameterSpec(iv)
                    cipher.init(Cipher.DECRYPT_MODE, e, params)
                    cipher.doFinal(encryptedData)
                }.onFailure {
                    GLog.e(TAG, "decrypt error e $it")
                }.getOrNull()
            }
        }

        object CTR {
            private const val SAFE_KEY_SIZE = 256
            private const val CIPHER_ALGORITHM = "${KeyProperties.KEY_ALGORITHM_AES}" +
                    "/${KeyProperties.BLOCK_MODE_CTR}" +
                    "/${KeyProperties.ENCRYPTION_PADDING_NONE}"
            private const val IV_LEN = 16

            /**
             * 根据秘钥别名从KeyStore获取秘钥，如果没有则会自动生成并以别名标记保存在KeyStore，
             * 可较大程度保证秘钥安全性
             * @param keyAlias 秘钥别名
             */
            @JvmStatic
            private fun obtainKeyFromKeyStore(keyAlias: String): Key? {
                return runCatching {
                    val keyStore = KeyStore.getInstance(STORE_TYPE_ANDROID)
                    keyStore.load(null)
                    val key = if (keyStore.containsAlias(keyAlias)) {
                        (keyStore.getEntry(keyAlias, null) as KeyStore.SecretKeyEntry).secretKey
                    } else {
                        val generator: KeyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, STORE_TYPE_ANDROID)
                        generator.init(
                            KeyGenParameterSpec.Builder(keyAlias, KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT)
                                .setKeySize(SAFE_KEY_SIZE)
                                .setBlockModes(KeyProperties.BLOCK_MODE_CTR)
                                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                                .build()
                        )
                        generator.generateKey()
                    }
                    if (key == null) {
                        GLog.e(TAG, "obtainKeyFromKeyStore key is null!")
                    }
                    key
                }.onFailure {
                    GLog.e(TAG, "obtainKeyFromKeyStore error:", it)
                }.getOrNull()
            }

            /**
             * 通过别名方式从KeyStore获取秘钥为数据加密，这种方式能较大程度保证秘钥的安全性
             * @param data 需要加密的数据
             * @param keyAlias 秘钥的别名
             */
            @JvmStatic
            fun encryptWithKeyStore(data: ByteArray, keyAlias: String): ByteArray? {
                return runCatching {
                    val cipher = Cipher.getInstance(CIPHER_ALGORITHM)
                    cipher.init(Cipher.ENCRYPT_MODE, obtainKeyFromKeyStore(keyAlias))
                    val iv: ByteArray = cipher.iv ?: throw IllegalStateException("Empty iv found.")
                    if (iv.size != IV_LEN) {
                        throw IllegalStateException("Not except iv len " + iv.size)
                    }
                    val enData: ByteArray = cipher.doFinal(data)
                    val result = iv.copyOf(IV_LEN + enData.size)
                    System.arraycopy(enData, 0, result, IV_LEN, enData.size)
                    result
                }.onFailure {
                    GLog.e(TAG, "encryptWithKeyStore error:", it)
                }.getOrNull()
            }

            /**
             * 通过别名方式从KeyStore获取秘钥为数据解密，这种方式能较大程度保证秘钥的安全性
             * @param data 需要解密的数据
             * @param keyAlias 秘钥的别名
             */
            @JvmStatic
            fun decryptWithKeyStore(data: ByteArray, keyAlias: String): ByteArray? {
                return runCatching {
                    val iv = data.copyOf(IV_LEN)
                    val enData = data.copyOfRange(IV_LEN, data.size)
                    val cipher = Cipher.getInstance(CIPHER_ALGORITHM)
                    val ivParameterSpec = IvParameterSpec(iv)
                    cipher.init(Cipher.DECRYPT_MODE, obtainKeyFromKeyStore(keyAlias), ivParameterSpec)
                    cipher.doFinal(enData)
                }.onFailure {
                    GLog.e(TAG, "decryptWithKeyStore error:", it)
                }.getOrNull()
            }
        }
    }

    object RSA {
        private const val KEY_ALGORITHM = "RSA"
        private const val TRANSFORMATION_RSA = "RSA/None/PKCS1Padding"

        @JvmStatic
        private fun encryptByPublicKey(data: ByteArray?, key: ByteArray?): ByteArray? {
            return runCatching {
                val e = X509EncodedKeySpec(key)
                val keyFactory = KeyFactory.getInstance(KEY_ALGORITHM)
                val publicKey = keyFactory.generatePublic(e)
                val cipher = Cipher.getInstance(TRANSFORMATION_RSA)
                cipher.init(Cipher.ENCRYPT_MODE, publicKey)
                cipher.doFinal(data)
            }.onFailure {
                throw IOException(it)
            }.getOrNull()
        }

        @JvmStatic
        fun encryptByPublicKey(data: ByteArray?, key: String): ByteArray? {
            val keyBytes = SecurityUtils.hexToBin(key)
            return encryptByPublicKey(data, keyBytes)
        }
    }
}