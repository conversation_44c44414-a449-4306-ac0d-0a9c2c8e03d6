/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedSyncDM
 ** Description:SharedSyncDM
 ** Version: 1.0
 ** Date : 2022/4/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/29        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum

import android.content.pm.ResolveInfo
import android.net.Uri
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.gallery.business_lib.api.ISharedSyncDM
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.gallery.business_lib.model.data.share.SharedAlbum
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.business_lib.cloud.DownloadInfo
import com.oplus.gallery.business_lib.cloud.IPhotoPageTransferFilesCallBack
import com.oplus.cloudkitlib.base.ITransferFilesCallBack
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.ocloudsync.CloudSyncStatusManager
import com.oplus.gallery.framework.abilities.ocloudsync.cloud.SyncAgentContants.URI_SHARE_ATLAS
import com.oplus.gallery.framework.abilities.ocloudsync.utils.CloudFileUtil
import com.oplus.gallery.framework.abilities.ocloudsync.utils.ShareAlbumHelper
import com.oplus.gallery.router_lib.annotations.Component
import com.oplus.sharealbum.bean.SharedAlbumRequest
import com.oplus.sharealbum.helper.SharedSyncDMHelper
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.service.ISharedAlbumService
import com.oplus.sharealbum.sharedsync.file.SharedFileIOManager
import com.oplus.sharealbum.track.SharedAlbumTrackHelper
import com.oplus.sharealbum.util.SharedFilePathUtil
import java.io.IOException

@Component(interfaceName = "com.oplus.gallery.business_lib.api.ISharedSyncDM")
class SharedSyncDM : ISharedSyncDM {

    override fun getNotificationCount(): Int {
        try {
            val response =
                SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
                    .hasNewMsg(SharedAlbumRequest()).execute()
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.isSuccess == true && (body.data?.hasNew == true || (body.data?.inviteMsgCount ?: 0) > 0)) {
                    return 1
                }
            }
        } catch (e: IOException) {
            GLog.e(TAG, "execute, Exception:$e")
        }
        return 0
    }

    override fun getSharedAlbums(count: Int): List<SharedAlbum> {
        val originDataList = SharedSyncDMHelper.getSharedAlbums(count)
        GLog.d(TAG, "getSharedAlbums count = ${originDataList.size}")
        return originDataList
    }

    override fun isSharedAlbumSwitchOpen(): Boolean {
        return ConfigAbilityWrapper.getBoolean(IS_REGION_CN) && CloudSyncStatusManager.getInstance().isSharedAlbumSwitchOpen
    }

    override fun getSharedAlbumUri(): Uri = URI_SHARE_ATLAS

    override fun isSupportSharedAlbum(): Boolean {
        return ConfigAbilityWrapper.getBoolean(IS_REGION_CN) && CloudSyncStatusManager.getInstance().isSupportSharedAlbum
    }

    override fun onInterceptShared(
        resolveInfo: ResolveInfo,
        filePaths: List<String>
    ): Boolean {
        return ShareAlbumHelper.onInterceptShare(resolveInfo, filePaths)
    }

    override fun setSharedAlbumSwitchDirectly(open: Boolean) {
        CloudSyncStatusManager.getInstance().isSharedAlbumEnable = open
    }

    override fun findNewestOriginalSavePath(md5: String): String? = SharedFilePathUtil.findNewestOriginalSavePath(md5)

    override fun hasEnoughStorageSpaceForDownload(size: Long): Boolean {
        return CloudFileUtil.hasEnoughStorageSpaceForDownload(size)
    }

    override fun trackSelectModePage(albumId: String, albumType: Int, button: String, photoCount: Int, videoCount: Int) {
        SharedAlbumTrackHelper.trackSelectModePage(albumId, albumType, button, photoCount, videoCount)
    }

    override fun stopDownloadSharedOriginFiles(ioFiles: List<SharedAlbumEntity>) {
        SharedFileIOManager.instance.stopDownloadSharedOriginFiles(ioFiles)
    }

    override fun downloadSharedOriginFiles(
        downloadInfo: DownloadInfo,
        ioFiles: List<SharedAlbumEntity>,
        iPhotoPageTransferFilesCallBack: IPhotoPageTransferFilesCallBack
    ) {
        SharedFileIOManager.instance.downloadSharedOriginFiles(downloadInfo, ioFiles, object : ITransferFilesCallBack {
            override fun batchResult(
                successFileList: List<CloudIOFile>,
                errorFileList: List<CloudIOErrorFile<CloudIOFile>>,
                cloudDateType: CloudDataType
            ) {
                iPhotoPageTransferFilesCallBack.batchResult()
            }

            override fun onIOResult(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                iPhotoPageTransferFilesCallBack.onIOResult()
            }

            override fun onProgress(file: CloudIOFile, cloudDataType: CloudDataType, progress: Double) {
                iPhotoPageTransferFilesCallBack.onProgress(progress)
            }
        })
    }

    companion object {
        private const val TAG = "SharedSyncDM"
    }
}