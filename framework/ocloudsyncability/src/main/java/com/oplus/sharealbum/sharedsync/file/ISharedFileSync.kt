/*******************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ISharedFileSync
 ** Description:ISharedFileSync
 ** Version: 1.0
 ** Date : 2022/4/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  <EMAIL>          2022/4/29        1.0          Add
 *******************************************************************/
package com.oplus.sharealbum.sharedsync.file

import com.oplus.cloudkitlib.base.CloudTypeIOFile
import com.oplus.cloudkitlib.base.ITransferFilesCallBack
import com.oplus.gallery.business_lib.cloud.DownloadInfo
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity

interface ISharedFileSync {
    /**
     * 上传共享文件
     */
    fun uploadSharedFiles(ioFiles: List<SharedAlbumEntity>, iTransferFilesCallBack: ITransferFilesCallBack)

    /**
     * 下载共享原文件
     */
    fun downloadSharedOriginFiles(downloadInfo: DownloadInfo, ioFiles: List<SharedAlbumEntity>, iTransferFilesCallBack: ITransferFilesCallBack)

    /**
     * 下载共享缩图
     */
    fun downloadSharedThumbFiles(frontTask: Boolean, ioFiles: List<SharedAlbumEntity>, iTransferFilesCallBack: ITransferFilesCallBack)

    /**
     * 停止上传共享
     */
    fun stopUploadSharedFiles(ioFiles: List<SharedAlbumEntity>)

    /**
     * 停止下载共享缩图
     */
    fun stopDownloadSharedThumbFiles(ioFiles: List<SharedAlbumEntity>)

    /**
     * 停止下载共享原文件
     */
    fun stopDownloadSharedOriginFiles(ioFiles: List<SharedAlbumEntity>)

    /**
     * 创建共享上传的IO文件
     */
    fun createUploadSharedFile(entity: SharedAlbumEntity): CloudTypeIOFile?

    /**
     * 创建共享缩图下载的IO文件
     */
    fun createDownloadSharedThumbFile(entity: SharedAlbumEntity): CloudTypeIOFile?

    /**
     * 创建共享原文件下载的IO文件
     */
    fun createDownloadSharedOriginFile(entity: SharedAlbumEntity): CloudTypeIOFile?

    /**
     * 停止所有任务
     */
    fun stopAll(clearData: Boolean)
}