/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ISharedAlbumReqCallback
 ** Description:ISharedAlbumReqCallback
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/30        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.callback

interface ISharedAlbumReqCallback<T> {
    fun sharedAlbumReqResult(data: T?)
    fun error(code: Int, msg: String?)
}