/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumListRepository
 ** Description:
 ** Version: 1.0
 ** Date : 2022/4/14
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/4/14        1.0             Add
 **************************************************************************************************/
package com.oplus.sharealbum.helper

import android.content.ContentValues
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.gallery.bus_lib.Bus
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BatchReq
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.dbaccess.req.UpdateReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.cloudkitlib.base.CloudIOErrorFile
import com.oplus.cloudkitlib.base.ITransferFilesCallBack
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.service.ISharedAlbumService
import com.oplus.sharealbum.bean.QuerySharedAlbumListRequest
import com.oplus.sharealbum.bean.QuerySharedAlbumListResponse
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumEntity
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.framework.abilities.caching.CacheOptions
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.caching.key.SharedMediaCacheKey
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.sharealbum.net.executeExt
import com.oplus.sharealbum.sharedsync.file.SharedFileIOManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue

class SharedAlbumListRepository {

    @Volatile
    private var thumbTaskRunning = false
    private val thumbTaskQueue = ConcurrentLinkedQueue<List<SharedAlbumResponse>>()
    private var currentThumbTask: List<SharedAlbumResponse>? = null

    /**
     * 获取图集列表
     */
    fun reqSharedAlbumList(
        pageSize: Int,
        pageSession: String,
        index: Int,
        successCallback: (QuerySharedAlbumListResponse?) -> Unit,
        errorCallback: (Int, String?) -> Unit
    ) {

        GLog.d(TAG, "reqSharedAlbumList call， index = $index")
        val req = QuerySharedAlbumListRequest(pageSize, pageSession, index)
        val service = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)

        service.querySharedAlbumList(req).executeExt({
            if (pageSession.isEmpty() && index == 0) {
                //首页刷新
                deleteAllAlbum()
            }
            it?.album?.let { albums ->
                if (!fillThumbnailStatus(albums)) return@executeExt
                insertAlbumList(albums)
            }
            successCallback.invoke(it)
        }, errorCallback)
    }

    /**
     * 填充缩图状态信息
     */
    private fun fillThumbnailStatus(albums: List<SharedAlbumResponse>): Boolean {
        if (albums.isEmpty()) return true
        val cachingAbility = ContextGetter.context.getAppAbility<ICachingAbility>() ?: return false
        val cache = cachingAbility.thumbnailCache ?: return false
        albums.forEach { data ->
            val fileId = data.coverFile?.fileId ?: return@forEach
            data.isCoverThumbnailDownloaded = cache.exists(
                SharedMediaCacheKey(fileId),
                CacheOptions(ThumbnailSizeUtils.TYPE_THUMBNAIL)
            ) == true
        }
        cachingAbility.close()
        return true
    }

    fun clearThumbTaskQueue() {
        GLog.d(TAG, "clearThumbTaskQueue call.")
        thumbTaskQueue.clear()

        val task = currentThumbTask
        if (thumbTaskRunning && task != null) {
            stopDownloadThumbImage(task)
            thumbTaskRunning = false
            currentThumbTask = null
        }
    }

    fun addThumbTask(albumList: List<SharedAlbumResponse>) {
        GLog.d(TAG, "addThumbTask call.")
        thumbTaskQueue.add(albumList)
        if (thumbTaskRunning.not()) {
            AppScope.launch(Dispatchers.IO) {
                loopThumbTask()
            }
        }
    }

    private fun loopThumbTask() {
        val task = thumbTaskQueue.poll()
        currentThumbTask = task
        if (task == null) {
            thumbTaskRunning = false
            GLog.d(TAG, "loopThumbTask  taskRunning end")
        } else {
            thumbTaskRunning = true
            startDownloadThumbImage(task)
            loopThumbTask()
        }
    }

    private fun startDownloadThumbImage(albumList: List<SharedAlbumResponse>) {
        val dataSet = albumList
            .filter { !it.isCoverThumbnailDownloaded }
            .map { SharedAlbumEntity.getSharedEntityByResponse(it) }
        GLog.d(TAG, "startDownloadThumbImage, need download size = ${dataSet.size}")
        if (dataSet.isEmpty()) {
            return
        }

        SharedFileIOManager.instance.downloadSharedThumbFiles(true, dataSet, object :
            ITransferFilesCallBack {
            override fun batchResult(
                successFileList: List<CloudIOFile>,
                errorFileList: List<CloudIOErrorFile<CloudIOFile>>,
                cloudDateType: CloudDataType
            ) {
                GLog.d(TAG, "startDownloadThumbImage.batchResult, success size = ${successFileList.size}, errorSize = ${errorFileList.size}")
            }

            override fun onIOResult(file: CloudIOFile, cloudDateType: CloudDataType, cloudKitError: CloudKitError) {
                GLog.d(TAG, "startDownloadThumbImage.onIOResult success, path:${file.filePath}, success:$cloudKitError")
                if (cloudKitError.isSuccess) {
                    // 封面下载完成需通知图集列表刷新
                    Bus.post(CoverThumbnailDownloadedMessage())
                    ContextGetter.context.contentResolver.notifyChange(GalleryStore.SharedAlbum.getContentUri(), null)
                }
            }

            override fun onProgress(file: CloudIOFile, cloudDataType: CloudDataType, progress: Double) {
                //do nothing
            }
        })
    }

    private fun stopDownloadThumbImage(albumList: List<SharedAlbumResponse>) {
        GLog.d(TAG, "stopDownloadThumbImage call")
        val dataSet = albumList.filter {
            !(it.coverFile?.fileId.isNullOrEmpty() || it.coverFile?.filePath.isNullOrEmpty()) || !it.isCoverThumbnailDownloaded
        }.map {
            SharedAlbumEntity.getSharedEntityByResponse(it)
        }
        SharedFileIOManager.instance.stopDownloadSharedThumbFiles(dataSet)
    }

    fun insertAlbumList(elements: Collection<SharedAlbumResponse>?) {
        if (elements.isNullOrEmpty()) {
            return
        }

        //记录保存的时间并保证顺序
        val convert = SharedAlbum2DbConvert()
        var saveTime = System.currentTimeMillis()
        val list = elements.map { item ->
            item.extSaveTime = saveTime++
            InsertReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.SHARED_ALBUM)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert { convert.convert(item) }
                .build()
        }
        BatchReq.Builder().addDataReqs(list).build().exec()
        UriNotifier.notifyTableChange(GalleryDbDao.TableType.SHARED_ALBUM, IDao.DaoType.GALLERY)
    }

    fun deleteAllAlbum() {
        DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SHARED_ALBUM)
            .build()
            .exec()
    }


    fun deleteSharedAlbumById(albumId: String, noNotify: Boolean) {
        DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SHARED_ALBUM)
            .addUriParameter(DatabaseUtils.NO_NOTIFY, if (noNotify) DatabaseUtils.VALUE_TRUE else DatabaseUtils.VALUE_FALSE)
            .setWhere("${GalleryStore.SharedAlbumColumns.ALBUM_ID} = '$albumId'")
            .build().exec()
    }

    fun queryAllAlbumResponse(): List<SharedAlbumResponse> {
        return QueryReq.Builder<List<SharedAlbumResponse>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SHARED_ALBUM)
            .setOrderBy("${GalleryStore.SharedAlbumColumns.SAVE_TIME} ASC")
            .setConvert(Cursor2SharedAlbumListConvert())
            .build().exec()
    }

    fun updateAlbumTitle(albumId: String, title: String) {
        UpdateReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SHARED_ALBUM)
            .setConvert { ContentValues().apply { put(GalleryStore.SharedAlbumColumns.ALBUM_NAME, title) } }
            .setWhere("${GalleryStore.SharedAlbumColumns.ALBUM_ID} = '$albumId'")
            .build().exec()
    }

    /**
     * 图集封面缩图下载完成的消息通知
     */
    class CoverThumbnailDownloadedMessage

    companion object {
        const val TAG = "SharedAlbumListRepository"
    }
}

