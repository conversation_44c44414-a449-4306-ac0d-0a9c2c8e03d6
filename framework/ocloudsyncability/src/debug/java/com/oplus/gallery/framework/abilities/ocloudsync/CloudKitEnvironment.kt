/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - CloudKitEnvironment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/6/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/6/24        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.ocloudsync

import com.heytap.cloudkit.libcommon.config.CloudEnv
import com.heytap.cloudkit.libcommon.config.CloudLogLevel
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.ocloudsync.sync.GallerySyncManager

object CloudKitEnvironment {
    val environmentParams = when (GallerySyncManager.TEST_ENVIR) {
        GallerySyncManager.ENVIR_RELEASE -> EnvironmentParams.RELEASE
        GallerySyncManager.ENVIR_DEBUG -> EnvironmentParams.DEBUG
        else -> EnvironmentParams.PRE
    }
    enum class EnvironmentParams(
        val appKey: String,
        val appSecretKey: String,
        val host: String,
        val cloudEnv: CloudEnv,
        val cloudLogLevel: CloudLogLevel
    ) {
        DEBUG(
            appKey = "5814d5310de3429797c2eb0e2a6a9208",
            appSecretKey = "f432e82483481d621a5ac4a18a954c1d5e6cb68b69bffe096ad14db4232cda82",
            host = "wanyol.com",
            CloudEnv.TEST1,
            CloudLogLevel.LEVEL_DEBUG
        ),
        RELEASE(
            appKey = "64d5a49504ce4c8e90e92856aefceec2",
            appSecretKey = "8ff5bdbebc28ddd8f4a04cdef7503a97a3b1167f301a55736f10762ed22d188d",
            ApiDmManager.getAppDM().getSecurityUrl()?.cloudKitApiHost ?: TextUtil.EMPTY_STRING,
            CloudEnv.RELEASE,
            CloudLogLevel.LEVEL_NONE
        ),
        PRE(
            appKey = "64d5a49504ce4c8e90e92856aefceec2",
            appSecretKey = "8ff5bdbebc28ddd8f4a04cdef7503a97a3b1167f301a55736f10762ed22d188d",
            host = "oppomobile.com",
            CloudEnv.PRE,
            CloudLogLevel.LEVEL_DEBUG
        )
    }
}