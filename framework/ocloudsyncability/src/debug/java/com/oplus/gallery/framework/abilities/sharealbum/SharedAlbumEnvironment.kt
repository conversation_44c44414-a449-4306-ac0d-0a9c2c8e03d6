/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumEnvironment
 ** Description:SharedAlbumEnvironment
 ** Version: 1.0
 ** Date : 2022/7/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>                        2022/7/11        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.framework.abilities.sharealbum

import com.oplus.gallery.framework.abilities.ocloudsync.sync.GallerySyncManager

object SharedAlbumEnvironment {
    val environmentParams = when (GallerySyncManager.TEST_ENVIR) {
        GallerySyncManager.ENVIR_RELEASE -> EnvironmentParams.RELEASE
        GallerySyncManager.ENVIR_DEBUG -> EnvironmentParams.DEBUG
        else -> EnvironmentParams.PRE
    }

    enum class EnvironmentParams(
        val dnsHost: String,
        val shareDefaultHost: String,
        val faqHost: String
    ) {
        DEBUG(
            dnsHost = "http://httpdns.ocloud-test.wanyol.com",
            shareDefaultHost = "http://ocloud-zx.ocloud-test.wanyol.com",
            faqHost = "http://ocloud-zx.ocloud-test.wanyol.com"
        ),
        RELEASE(
            dnsHost = "https://httpdns.ocloud.heytapmobi.com",
            shareDefaultHost = "https://share-cn01a.ocloud.heytapmobi.com",
            faqHost = "https://i-cn01a.ocloud.heytapmobi.com"
        ),
        PRE(
            dnsHost = "https://ocloud-htprehttpdns-cn.heytapmobi.com",
            shareDefaultHost = "http://ocloud-htpreshare-cn.heytapmobi.com",
            faqHost = "http://ocloud-htpreshare-cn.heytapmobi.com"
        )
    }
}