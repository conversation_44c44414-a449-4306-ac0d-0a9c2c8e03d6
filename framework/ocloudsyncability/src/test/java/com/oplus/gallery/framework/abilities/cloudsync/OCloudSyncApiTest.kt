/*****************************************************************
 * Copyright (C), 2022-2022, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: OCloudSyncApiTest.kt
 * Description:
 * Version: 1.0
 * Date: 2023/10/7
 * Author: Kun.Qin
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>         <date>          <version>       <desc>
 * ------------------------------------------------------------------------------
 * Kun.Qin          2023/10/7         1.0             ---
 ****************************************************************/

package com.oplus.gallery.framework.abilities.cloudsync

import com.oplus.gallery.framework.abilities.ocloudsync.OCloudSyncApi
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class OCloudSyncApiTest {
    @MockK(relaxed = true)
    lateinit var mockApp: GalleryApplication

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        ContextGetter.context = mockApp
        mockkObject(OCloudSyncApi)
    }

    @After
    fun after() {
        unmockkAll()
    }

    @Test
    fun should_return_not_empty_region_list_when_getAcceptRegion() {
        // given
        mockkObject(OCloudSyncApi)
        // when
        val acceptRegionList = OCloudSyncApi.getAcceptRegion()
        // then
        Assert.assertTrue(acceptRegionList.isNotEmpty())
    }
}