/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SafeBoxService.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/02/12
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/02/12      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.encryption

import android.graphics.Bitmap
import android.os.IBinder
import com.oplus.gallery.business_lib.safebox.BaseSafeBox
import com.oplus.gallery.business_lib.safebox.DeSafeBoxTaskInfo
import com.oplus.gallery.business_lib.safebox.ISafeBoxImageInfo
import com.oplus.gallery.business_lib.safebox.ISafeBoxProgressListener

internal class OplusSafeBox : BaseSafeBox<EncyptionInterface>() {

    override fun onServiceInterfaceInit(binder: IBinder): EncyptionInterface? = EncyptionInterface.Stub.asInterface(binder)

    override fun startMoveToSafeBox(source: String, imageType: Int, scanState: Boolean): Int {
        return safeBoxInterface?.encryptionTask(source, imageType, scanState) ?: 0
    }

    override fun startMoveToSafeBox(
        sources: List<String>,
        imageTypes: IntArray,
        scanState: Boolean,
        listener: ISafeBoxProgressListener?
    ): Int = safeBoxInterface?.let {
        /*
         * 重置私密保险箱那边的 cancel 标记，如果之前调用过 setStopEncryption(true) ，那么再次调用 safeBoxTasks
         * 会直接返回 ERROR_OTHER_REASON ，原因就是私密那边的 cancel 标记没有重置，需要相册这边手动重置
         */
        it.setStopEncryption(false)
        return it.encryptionTasks(sources, imageTypes, scanState, object : IEncryptProgressListener.Stub() {

            override fun onStarted() {
                listener?.onStarted()
            }

            override fun onProgress(progress: Int) {
                listener?.onProgress(progress)
            }

            override fun onFinished(result: Int, failedCount: Int) {
                listener?.onFinished(result, failedCount)
            }
        })
    } ?: 0

    override fun startMoveAlbumToSafeBox(
        albumInfoList: List<EncryptionFolderInfo>,
        scanState: Boolean,
        listener: ISafeBoxProgressListener?
    ): Int = safeBoxInterface?.let {
        /*
         * 重置私密保险箱那边的 cancel 标记，如果之前调用过 setStopEncryption(true) ，那么再次调用 safeBoxTasks
         * 会直接返回 ERROR_OTHER_REASON ，原因就是私密那边的 cancel 标记没有重置，需要相册这边手动重置
         */
        it.setStopEncryption(false)
        return it.encryptionFoldersTasks(albumInfoList, scanState, object : IEncryptProgressListener.Stub() {

            override fun onStarted() {
                listener?.onStarted()
            }

            override fun onProgress(progress: Int) {
                listener?.onProgress(progress)
            }

            override fun onFinished(result: Int, failedCount: Int) {
                listener?.onFinished(result, failedCount)
            }
        })
    } ?: 0

    override fun cancelMoveToSafeBox() {
        // 停止加密，setStopSafeBox 的接口设计不合理，私密保险箱会读取 state 值赋给一个是否停止加密的标记. 取消时设置为true, 开启任务时记得设置为false
        safeBoxInterface?.setStopEncryption(true)
    }

    override fun deSafeBoxTask(taskInfo: DeSafeBoxTaskInfo): Int = safeBoxInterface?.dencryptionTask(
        taskInfo.rootPath,
        taskInfo.targetRootPath,
        taskInfo.targetPath,
        taskInfo.encryptName,
        taskInfo.originalPath,
        taskInfo.originalType,
        taskInfo.password,
        taskInfo.replaced
    ) ?: 0

    override fun deleteTask(source: String?) {
        safeBoxInterface?.deleteTask(source)
    }

    override fun viewTask(source: String?): String? = safeBoxInterface?.viewTask(source)

    override fun getBitmap(source: String?, password: String?): Bitmap? = safeBoxInterface?.getBitmap(source, password)

    override fun savePasswordAndMode(mode: Int, password: String?) {
        safeBoxInterface?.savePasswordAndMode(mode, password)
    }

    override fun getSafeFileWithoutRetry(
        filePath: String?,
        fileSize: Long,
        thumbSize: Long,
        md5: String?,
        gid: String?
    ): ISafeBoxImageInfo? = safeBoxInterface?.getSafeFile(filePath, fileSize, thumbSize, md5, gid)

    override fun mergeBackupRestoreData(): Boolean = safeBoxInterface?.mergeBackupRestoreData() ?: false

    override fun deleteBackupRestoreData(): Boolean = safeBoxInterface?.deleteBackupRestoreData() ?: false

    override fun getBitmapForExternal(
        source: String?,
        originalType: Int,
        width: Int,
        height: Int
    ): Bitmap? = safeBoxInterface?.getBitmapForExternal(source, originalType, width, height)

    override fun isConfigurationComplete(): Boolean = safeBoxInterface?.isConfigurationComplete() ?: false
}