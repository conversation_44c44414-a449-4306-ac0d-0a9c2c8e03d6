/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AlbumSetViewDataBinding.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2020/8/13 10:21
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2020/8/13      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.ui.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_IS_CREATE_NEW_ALBUM
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.AlbumViewData.Companion.SUPPORT_SHOW_RED_DOT
import com.oplus.gallery.basebiz.uikit.isOtherAlbumSet
import com.oplus.gallery.basebiz.uikit.isSelected
import com.oplus.gallery.basebiz.uikit.isSupportDelete
import com.oplus.gallery.basebiz.widget.SlotView
import com.oplus.gallery.business_lib.helper.TalkBackHelper
import com.oplus.gallery.business_lib.viewmodel.style.IStylePool
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.ui.helper.enablePressFeedback
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_ALBUM
import com.oplus.gallery.foundation.util.ext.DrawType
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.baselist.view.ListViewDataBinding
import com.oplus.gallery.standard_lib.baselist.view.canShowSelectionMode
import com.oplus.gallery.standard_lib.baselist.view.isSelectMode
import com.oplus.gallery.standard_lib.bean.ViewData
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.graphics.StyleDrawableMaker
import com.oplus.gallery.standard_lib.graphics.drawable.GridDrawable
import com.oplus.gallery.standard_lib.graphics.drawable.toRoundDrawable
import com.oplus.gallery.basebiz.R as BasebizR

open class AlbumSetViewDataBinding(
    context: Context,
    protected val canShowSelectionCheckBox: Boolean = true,
    stylePool: IStylePool?,
    protected val isMaskVisibleGet: (() -> Boolean),
    protected val checkboxAnimEnableGet: (() -> Boolean)
) : AlbumLoadViewDataBinding<AlbumViewData>(context, stylePool) {
    /**
     * 默认的标题
     * */
    open val defaultTitle: String
        get() = context.resources.getString(BasebizR.string.base_back_title_with_no_name)

    protected open val placeholder: Drawable? = stylePool?.let {
        StyleDrawableMaker(it.getStyle(StyleType.TYPE_THUMB_STYLE))
            .generateDrawable(ColorDrawable(context.getColor(BasebizR.color.base_placeholder_color)))
    }
    var slotView: SlotView? = null
    var titleTxt: TextView? = null
    var detailTxt: TextView? = null

    // Debug的时候打开动态开关，可以在每个图集item上加上一层view,目前是只有TextView用于显示position
    private val infoDebugView by lazy {
        TextView(context).apply {
            setTextColor(Color.RED)
            textSize = DEBUG_TEXT_SIZE
            (view as? ViewGroup)?.addView(this)
        }
    }

    private val isMaskVisible get() = isMaskVisibleGet.invoke()

    protected val useCheckboxAnim get() = checkboxAnimEnableGet.invoke()

    @SuppressLint("UseCompatLoadingForDrawables")
    override fun onCreateView(parent: ViewGroup, viewType: Int): View {
        return LayoutInflater.from(parent.context).inflate(BasebizR.layout.base_albumset_item, parent, false).apply {
            slotView = findViewById(BasebizR.id.base_album_set_item)
            titleTxt = findViewById(BasebizR.id.albumset_title)
            detailTxt = findViewById(BasebizR.id.albumset_detail)
            setContentDrawable(null)
            COUIPressFeedbackHelper(slotView, COUIPressFeedbackHelper.CARD_PRESS_FEEDBACK).enablePressFeedback(this)
        }
    }

    @Suppress("LongMethod")
    override fun onBindViewHolder(
        itemViewHolder: BaseListViewHolder<AlbumViewData>,
        position: Int,
        viewData: AlbumViewData?
    ) {
        if (DEBUG_ALBUM) {
            GLog.d(TAG, "onBindViewHolder position: $position")
        }
        val view = itemViewHolder.view
        val albumViewData = if (viewData != null) {
            view.visibility = View.VISIBLE
            viewData
        } else {
            // 未加载出mediaSet数据时，不显示
            view.visibility = View.INVISIBLE
            return
        }

        view.tag = albumViewData
        showDebugInfo(albumViewData)
        titleTxt?.setTextColor(COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPrimaryNeutral))
        detailTxt?.setTextColor(COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorSecondNeutral))
        if (albumViewData.supportedAbilities?.getBoolean(SUPPORT_IS_CREATE_NEW_ALBUM, false) == true) {
            titleTxt?.text = view.resources.getString(BasebizR.string.base_create_local_album)
            detailTxt?.text = TextUtil.EMPTY_STRING
            TalkBackHelper.setItemDescriptionForAlbumSet(
                view,
                titleTxt,
                detailTxt,
                isEditMode = false,
                isChecked = false,
                needUnit = true,
                isEnable = true
            )

            val createNewAlbumDrawable = processDrawable(
                ResourcesCompat.getDrawable(view.resources, BasebizR.drawable.base_button_add_to, view.context.theme),
                albumViewData
            )
            setContentDrawable(createNewAlbumDrawable)
        } else {
            onSetRedDotShow(albumViewData)

            titleTxt?.text = albumViewData.title.let {
                if (it.isNullOrEmpty()) {
                    defaultTitle
                } else {
                    it
                }
            }
            detailTxt?.text = getDetailText(albumViewData)
            slotView?.isCheckboxShow = isSelectMode(selectModeSpec) &&
                (canShowSelectionMode(selectModeSpec) || canShowSelectionCheckBox) &&
                albumViewData.isSupportDelete()
            if (!isSelectMode(selectModeSpec)) {
                slotView?.clearCheckBoxState()
            }
            setChecked(albumViewData.isSelected(), useCheckboxAnim)
            onSetMarkIconShow(albumViewData)

            val itemIsOtherAlbumSet = albumViewData.isOtherAlbumSet()
            TalkBackHelper.setItemDescriptionForAlbumSet(
                view,
                titleTxt,
                detailTxt,
                isEditMode = isSelectMode(selectModeSpec),
                isChecked = slotView?.isChecked ?: false,
                needUnit = !itemIsOtherAlbumSet,
                isEnable = !itemIsOtherAlbumSet
            )

            val resultDrawable = processDrawable(
                albumViewData.thumbnail?.run {
                    processThumbnailLoaded(this, albumViewData, view)
                    content
                },
                albumViewData
            )
            setContentDrawable(resultDrawable)
            val itemIsSelectionEnable =
                albumViewData.supportedAbilities?.getBoolean(SlotOverlayHelper.SUPPORT_SELECTION_ENABLE, true) ?: true
            //添加蒙层功能
            slotView?.isMaskVisible =
                (<EMAIL> && (slotView?.isChecked) ?: false) || !itemIsSelectionEnable
        }
    }

    open fun onSetRedDotShow(viewData: AlbumViewData) {
        titleTxt?.apply {
            if (viewData.supportedAbilities?.get(SUPPORT_SHOW_RED_DOT) == true) {
                compoundDrawablePadding = context.resources.getDimensionPixelOffset(BasebizR.dimen.base_album_set_item_red_dot_padding)
                setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, BasebizR.drawable.base_red_dot, 0)
            } else {
                compoundDrawablePadding = 0
                setCompoundDrawables(null, null, null, null)
            }
            titleTxt?.setPaddingRelative(0, 0, compoundDrawablePadding, 0)
        }
    }

    protected open fun onSetMarkIconShow(viewData: AlbumViewData): Unit = Unit

    protected fun showDebugInfo(viewData: AlbumViewData?) {
        if (!DEBUG_ALBUM) return
        infoDebugView.text = viewData?.position?.toString() ?: TextUtil.EMPTY_STRING
    }

    override fun setChecked(checked: Boolean, animated: Boolean) {
        slotView?.isChecked = checked
        TalkBackHelper.setItemDescriptionForAlbumSet(
            slotView?.parent as View,
            titleTxt,
            detailTxt,
            isEditMode = true,
            isChecked = checked,
            needUnit = true,
            isEnable = true
        )
        if (!animated) {
            slotView?.jumpDrawablesToCurrentState()
        }
    }

    override fun setContentDrawable(drawable: Drawable?) {
        slotView?.apply {
            (drawable ?: placeholder)?.also(this::setImageDrawable)
        }
    }

    override fun updateCount(viewData: ViewData?) {
        (viewData as? AlbumViewData)?.apply {
            detailTxt?.text = getDetailText(this)
        }
    }

    open fun getDetailText(viewData: AlbumViewData): String? = LocaleUtils.getLocaleFormattedNumber(viewData.totalCount)

    protected fun getGridRowCol(): Pair<Int, Int> {
        var rowCount = StyleDrawableMaker.DEFAULT_ROW_COUNT
        var colCount = StyleDrawableMaker.DEFAULT_COLUMN_COUNT
        stylePool?.let {
            rowCount = it.getStyle(StyleType.TYPE_GRID_THUMB_STYLE)
                .getInt(StyleData.KEY_THUMB_LAYOUT_ROW_COUNT, StyleDrawableMaker.DEFAULT_ROW_COUNT)
            colCount = it.getStyle(StyleType.TYPE_GRID_THUMB_STYLE)
                .getInt(StyleData.KEY_THUMB_LAYOUT_COLUMN_COUNT, StyleDrawableMaker.DEFAULT_COLUMN_COUNT)
        }
        return Pair(rowCount, colCount)
    }

    protected fun generateCoverGridDrawable(coverDrawable: Drawable, rowCount: Int, colCount: Int, showIndex: Int): GridDrawable {
        val grids = Array<Array<Drawable?>>(rowCount) { rowIndex ->
            Array(colCount) { colIndex ->
                if (rowCount * rowIndex + colIndex == showIndex) {
                    coverDrawable.toRoundDrawable(
                        background = stylePool?.getStyle(StyleType.TYPE_GRID_ITEM_MASK_THUMB_STYLE)
                            ?.getDrawable(StyleData.KEY_THUMB_BACKGROUND),
                        radius = stylePool?.getStyle(StyleType.TYPE_GRID_THUMB_STYLE)?.getStyleData(StyleData.KEY_SUB_STYLE)
                            ?.getFloat(StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS) ?: 0f,
                        drawType = DrawType.CENTER
                    )
                } else null
            }
        }
        return GridDrawable(
            grids,
            edge = stylePool?.getStyle(StyleType.TYPE_GRID_THUMB_STYLE)?.getFloat(StyleData.KEY_THUMB_LAYOUT_PADDING_BORDER) ?: 0f,
            gap = stylePool?.getStyle(StyleType.TYPE_GRID_THUMB_STYLE)?.getFloat(StyleData.KEY_THUMB_LAYOUT_GAP_BETWEEN_CHILD) ?: 0f
        )
    }

    override fun copyLayout(context: Context): ListViewDataBinding<AlbumViewData> {
        return AlbumSetViewDataBinding(context, canShowSelectionCheckBox, stylePool, isMaskVisibleGet, checkboxAnimEnableGet)
    }

    fun setTitleVisibility(view: View, visibility: Int) {
        view.findViewById<TextView>(BasebizR.id.albumset_title)?.visibility = visibility
    }

    fun setDetailVisibility(view: View, visibility: Int) {
        view.findViewById<TextView>(BasebizR.id.albumset_detail)?.visibility = visibility
    }

    companion object {
        private const val TAG = "AlbumSetViewDataBinding"
        private const val DEBUG_TEXT_SIZE = 30F
    }
}