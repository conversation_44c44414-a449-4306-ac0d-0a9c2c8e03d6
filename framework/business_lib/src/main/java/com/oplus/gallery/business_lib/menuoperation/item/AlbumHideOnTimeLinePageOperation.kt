/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AlbumHideOnTimeLinePageOperation.kt
 ** Description : 不显示在照片页的图集保存操作类
 ** Version     : 1.0
 ** Date        : 2025/3/13
 ** Author      : W9077760@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9077760@Apps.Gallery3D         2025/3/13      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.business_lib.menuoperation.HideAlbumOnTimeLinePageAction
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RESULT_ERROR_NO_DATA
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RESULT_FAILED
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RESULT_SUCCESS
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.business_lib.menuoperation.helper.AlbumHideOnTimeLinePageHelper
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_ALBUM_HIDE_ON_TIME_LINE_PAGE
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_FAILED_PARAM_NO_DATA
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils.getRelativePathMapFromBucketPaths

class AlbumHideOnTimeLinePageOperation(
    isRunOnMain: Boolean,
    paramMap: Map<String, Any?>,
    onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)?
) : MenuOperation<Unit>(isRunOnMain, paramMap, onCompleted) {
    /** 菜单操作时的图集数据 适用于首页长按 单选场景*/
    private var albumId: Int?  = null

    /** 菜单操作时选中的的图集项id 适用于多选场景（我的图集）*/
    private var albumIdList: List<Int>?  = null

    override fun onCheckAndPrepare(): Boolean {
        var isAlbumInfoPrepared = false
        (paramMap[HideAlbumOnTimeLinePageAction.KEY_ALBUM_ID] as? Int?)?.let {
            this.albumId = it
            isAlbumInfoPrepared = true
        } ?: run {
            GLog.d(TAG, LogFlag.DL) { "onCheckAndPrepare, albumViewData = null" }
        }
        (paramMap[HideAlbumOnTimeLinePageAction.KEY_ALBUM_IDS] as? List<Int>?)?.let {
            this.albumIdList = it
            isAlbumInfoPrepared =  true
        } ?: run {
            GLog.d(TAG, LogFlag.DL) { "onCheckAndPrepare, albumIdList = null" }
        }
        if (isAlbumInfoPrepared.not()) {
            setResult(RESULT_ERROR_NO_DATA, trackResult = TrackResult(VALUE_FAILED_PARAM_NO_DATA))
        }
        return isAlbumInfoPrepared
    }

    override fun onRun() {
        var albumIds = listOf<Int>()
        albumId?.let {
            albumIds = listOf(it)
        } ?: run {
            albumIds = this.albumIdList ?: listOf()
        }
        if (albumIds.isEmpty().not()) {
            val albumBucketPaths = AlbumHideOnTimeLinePageHelper.getAlbumBucketPathsByAlbumId(albumIds)
            val bucketPathMap = albumIds.zip(albumBucketPaths).toMap()
            val relativePathMap: Map<Int, List<String>> = getRelativePathMapFromBucketPaths(bucketPathMap)
            val insertAlbumHideOnTimeLinePageSuccess: Boolean = AlbumHideOnTimeLinePageHelper.insertAlbumHideOnTimeLinePage(relativePathMap)
            if (insertAlbumHideOnTimeLinePageSuccess) {
                setResult(RESULT_SUCCESS)
                AlbumsActionTrackHelper.trackAndSendAlbumHideOnTimeLinePage(true)
            } else {
                setResult(RESULT_FAILED)
            }
        }
    }

    override fun getMenuTypeForTrack(): String = VALUE_ALBUM_HIDE_ON_TIME_LINE_PAGE

    override fun getCustomParamForTrack(): Map<String, String?>? = null

    override fun getImageAndVideoCountForTrack(): Pair<String, String> = defaultTrackCountPair

    override fun cancel() {
        // do nothing
    }

    companion object {
        const val TAG = "AlbumHideOnTimeLinePageOperation"
    }
}