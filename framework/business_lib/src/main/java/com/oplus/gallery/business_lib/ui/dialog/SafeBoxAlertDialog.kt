/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SafeBoxAlertDialog.kt
 ** Description:带有文件进度的进度条弹框，目前是私密保险箱（设为私密）使用
 **
 ** 如果支持加密芯片则加密过程动画如下
 ** 1、成功：进度动画（弹框进度展示展示时间最少700ms，弹框显示时间大于700ms不播放动画，直接设置进度）-> 播放位移动画（300ms）结束 后-> 再播放锁头动画，结束后 -> 延迟400ms 关闭弹窗
 ** 2、加密过程中点击取消：播放位移动画（300ms）结束后 -> 延迟750ms 关闭弹窗
 ** 3、失败：不播放位移，取消按钮不消失，等待用户点击取消关闭 ->  用户点击取消直接关闭弹窗，不播放任何动画
 **
 ** 如果不支持加密芯片，则是普通弹框
 **
 ** 交互视觉稿：https://www.figma.com/file/1Tl9GBJM5nx2bS5dx2wQqZ/%E7%A7%81%E5%AF%86%E4%BF%9D%E9%99%A9%E7%AE%B1---%E8%8A%AF%E7%89%87%E7%BA%A7%E6%96%87%E4%BB%B6%E5%8A%A0%E5%AF%86?type=design&node-id=0-1&mode=design&t=WLshhFTCpcjpTYnP-0
 ** 动效文档：https://odocs.myoas.com/docs/erAdP6ErOlCxRKAG?mobileMsgShowStyle=1&pcMsgShowStyle=1
 **
 ** Version: 1.0
 ** Date: 2023/7/10
 ** Author: xingyafan@Apps.Gallery3D
 ** TAG: SafeBoxAlertDialog
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xingyafan@Apps.Gallery3D       2023/7/10     1.0         first created
 ********************************************************************************/
package com.oplus.gallery.business_lib.ui.dialog

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ObjectAnimator
import android.animation.AnimatorSet
import android.app.AlertDialog
import android.content.Context
import android.content.DialogInterface
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.oplus.anim.EffectiveAnimationView
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.foundation.ui.dialog.base.BaseAlertDialog
import com.oplus.gallery.foundation.ui.dialog.base.BaseBuilder
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * 带有文件进度的进度条弹框，目前是私密保险箱（设为私密）使用
 *
 * 如果支持加密芯片则加密过程动画如下
 * 1、成功：进度动画（弹框进度展示展示时间最少700ms，弹框显示时间大于700ms不播放动画，直接设置进度）-> 播放位移动画（300ms）结束 后-> 再播放锁头动画，结束后 -> 延迟400ms 关闭弹窗
 * 2、加密过程中点击取消：播放位移动画（300ms）结束后 -> 延迟750ms 关闭弹窗
 * 3、失败：不播放位移，取消按钮不消失，等待用户点击取消关闭 ->  用户点击取消直接关闭弹窗，不播放任何动画
 *
 * 如果不支持加密芯片，则是普通弹框
 *
 * 交互视觉稿：https://www.figma.com/file/1Tl9GBJM5nx2bS5dx2wQqZ/%E7%A7%81%E5%AF%86%E4%BF%9D%E9%99%A9%E7%AE%B1---%E8%8A%AF%E7%89%87%E7%BA%A7%E6%96%87%E4%BB%B6%E5%8A%A0%E5%AF%86?type=design&node-id=0-1&mode=design&t=WLshhFTCpcjpTYnP-0
 * 动效文档：https://odocs.myoas.com/docs/erAdP6ErOlCxRKAG?mobileMsgShowStyle=1&pcMsgShowStyle=1
 */
class SafeBoxAlertDialog private constructor(
    builder: Builder
) : BaseAlertDialog<SafeBoxAlertDialog>(builder) {
    private val mainHandler = Handler(Looper.getMainLooper())

    private var title: String? = null
    private var onClickListener: DialogInterface.OnClickListener? = null

    private var maxProgress: Int = 0
    private var maxSize = 0
    private var progress: Int = 0
    private var aniProgress: Int = 0
    private var progressAnimator: ValueAnimator? = null

    private var dialogTitle: TextView? = null
    private var animationLockView: EffectiveAnimationView? = null
    private var dialogSubTitle: TextView? = null
    private var dialogCancel: TextView? = null
    private var contentLayout: LinearLayout? = null
    private var bodyLayout: LinearLayout? = null

    private var isUserCancel: Boolean = false
    private var failedCount = 0
    private var startShowTime = 0L

    private var negativeButtonText: CharSequence? = null

    private var isTaskFinished: Boolean = false

    override fun show(): SafeBoxAlertDialog {
        super.show()
        bodyLayout = findViewById(com.oplus.gallery.basebiz.R.id.dialog_body)
        contentLayout = findViewById(com.oplus.gallery.basebiz.R.id.dialog_content)
        dialogTitle = findViewById(com.oplus.gallery.basebiz.R.id.dialog_title)
        dialogSubTitle = findViewById(com.oplus.gallery.basebiz.R.id.dialog_sub_title)
        animationLockView = findViewById(com.oplus.gallery.basebiz.R.id.progress_anim)
        animationLockView?.apply {
            setAnimation(com.oplus.gallery.basebiz.R.raw.safe_chip_encryption_loading)
            setMinAndMaxFrame(0, PROGRESS_ANIM_FRAME_MAX)
            progress = 0f
        }
        dialogCancel = findViewById(com.oplus.gallery.basebiz.R.id.dialog_cancel)
        dialogCancel?.setOnClickListener { v ->
            if (isTaskFinished) {
                if (isTaskFailed()) {
                    //加密任务已完成或者被中断，此时有失败文件，私密保险箱的回调进度progress为已加密的文件进度，如果存在加密失败，进度不会到100%
                    progressAnimator?.end()
                    dismiss()
                }
            } else {
                isUserCancel = true
                onClickListener?.onClick(realDialog, AlertDialog.BUTTON_NEGATIVE)
            }
        }
        dialogTitle?.text = title
        dialogSubTitle?.text = getContext().getString(
            com.oplus.gallery.basebiz.R.string.base_encrypt_file_process, (maxSize * progress / maxProgress), maxSize
        )
        dialogCancel?.text = negativeButtonText

        startShowTime = SystemClock.uptimeMillis()

        return this
    }

    /**
     * 更新进度
     */
    fun setProgress(progress: Int) {
        this.progress = progress
        val startProgress = aniProgress
        val showedTime = SystemClock.uptimeMillis() - startShowTime
        val totalProgressTime = TIME_PROGRESS_MIN_SHOW / maxProgress * progress
        if (showedTime < totalProgressTime) {
            // 如果 弹框已显示的时长 < 当前进度动画的总时长 ，需要播放动画，动画时间 = 当前进度动画的总时长 - 弹框已显示的时长
            playProgressAnimator(startProgress, progress, totalProgressTime - showedTime)
        } else {
            // 如果 弹框已显示的时长 >= 当前进度动画的总时长，直接设置进度，无需动画
            updateProgress(progress)
        }
    }

    /**
     * 播放进度动画，例如上一次调用setProgress进度是80%，然后调用setProgress进度是90%，使用动画平稳过渡到90%
     */
    private fun playProgressAnimator(startProgress: Int, endProgress: Int, duration: Long) {
        progressAnimator?.cancel()
        progressAnimator = ValueAnimator.ofInt(startProgress, endProgress).apply {
            interpolator = LinearInterpolator()
            this.duration = duration
            addUpdateListener {
                updateProgress(it.animatedValue as Int)
            }
            doOnCancel {
                progressAnimator = null
            }
            doOnEnd {
                progressAnimator = null
            }
        }
        progressAnimator?.start()
    }

    private fun updateProgress(progress: Int) {
        if (aniProgress < progress) {
            aniProgress = progress
            /**
             * 规避IllegalArgumentException，在初始化动画时，调用 setMinAndMaxFrame(0, PROGRESS_ANIM_FRAME_MAX) 设置了最大帧数，
             * PROGRESS_ANIM_FRAME_MAX 是lottie动画资源的帧数，此处设置无问题，但是在 COUI EffectiveAnimationView 控件内部此数据会发生变化，
             * 具体原因未知, 根据COUI建议，setMinFrame 方法调用前，最好增加合法性检验，避免crash，详见 6730286 .
             */
            val maxFrame = animationLockView?.maxFrame
            val minFrame = if (maxFrame != null) progress.coerceAtMost(maxFrame.toInt()) else progress
            animationLockView?.setMinFrame(minFrame)
            dialogSubTitle?.text = getContext().getString(
                com.oplus.gallery.basebiz.R.string.base_encrypt_file_process, (maxSize * progress / maxProgress), maxSize
            )
        }
    }

    /**
     * 移入私密保险箱的任务结束
     * @param failedCount 失败的文件数量
     */
    fun taskFinished(failedCount: Int = 0) {
        if (isTaskFinished) {
            GLog.w(TAG, "[taskFinished] failed:$isTaskFinished")
            return
        }
        isTaskFinished = true
        this.failedCount = failedCount

        if (isTaskFailed()) {
            if (isUserCancel) {
                progressAnimator?.end()
                startAnimator(false)
            } else {
                // 失败：不播放位移，取消按钮不消失，显示失败数量，等待用户点击取消关闭 ->  用户点击取消直接关闭弹窗，不播放任何动画
                progressAnimator?.end()
                dialogTitle?.let {
                    getAlphaAnimator(it, ALPHA_SHOW, ALPHA_HIDE) {
                        it.text = getTitleText()
                        getAlphaAnimator(it, ALPHA_HIDE, ALPHA_SHOW).start()
                    }.start()
                }
            }
        } else {
            // 成功： 如果弹框显示时间大于700ms，就会直接播放成功动画；如果小于700ms，需要等待延迟播放进度动画，保证播放成功动画之前，用户看到最少看了700ms弹框
            doActionAfterAnimatorEnd(progressAnimator) {
                startAnimator(true)
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface?) {
        super.onDismiss(dialog)
        mainHandler.removeCallbacksAndMessages(null)
        startShowTime = 0
        failedCount = 0
        isTaskFinished = false
        isUserCancel = false
        onClickListener = null
    }

    /**
     * 跟据safebox返回的任务失败的文件数量判断成功还是失败
     */
    private fun isTaskFailed(): Boolean {
        return failedCount > 0
    }

    private fun getTitleText(): String {
        return if (isTaskFailed()) {
            getContext().resources.getQuantityString(
                com.oplus.gallery.basebiz.R.plurals.base_encrypt_fail, failedCount, failedCount
            )
        } else {
            getContext().getString(com.oplus.gallery.basebiz.R.string.base_encrypt_done)
        }
    }

    private fun startAnimator(isPlayLockAnimator: Boolean) {
        val animatorList = ArrayList<Animator>()

        dialogTitle?.let {
            if (it.text != getTitleText()) {
                animatorList.add(getAlphaAnimator(it, ALPHA_SHOW, ALPHA_HIDE) {
                    it.text = getTitleText()
                    getAlphaAnimator(it, ALPHA_HIDE, ALPHA_SHOW).start()
                })
            }
        }

        if (isTaskFailed()) {
            dialogSubTitle?.let {
                animatorList.add(getAlphaAnimator(it, ALPHA_SHOW, ALPHA_HIDE))
            }
        }

        dialogCancel?.let {
            animatorList.add(getAlphaAnimator(it, ALPHA_HIDE_SHOW_BUTTON, ALPHA_HIDE))
        }

        getMoveAnimator {
            if (isPlayLockAnimator) {
                animationLockView?.let {
                    playLockAnimation(it) {
                        mainHandler.postDelayed({ dismiss() }, DELAY_DISMISS_SUCCESS)
                    }
                } ?: kotlin.run {
                    dismiss()
                }
            } else {
                mainHandler.postDelayed({ dismiss() }, DELAY_DISMISS_FAIL)
            }
        }?.let { animatorList.add(it) } ?: kotlin.run {
            dismiss()
        }

        AnimatorSet().apply {
            interpolator = COUIMoveEaseInterpolator()
            playTogether(
                animatorList
            )
            start()
        }
    }

    private fun getAlphaAnimator(
        view: View,
        fromAlpha: Float,
        toAlpha: Float,
        onEnd: (() -> Unit)? = null
    ): Animator {
        return ObjectAnimator.ofFloat(view, ANIMATOR_NAME_ALPHA, fromAlpha, toAlpha).apply {
            duration = DURATION_ANIMATOR_ALPHA
            interpolator = COUIMoveEaseInterpolator()
            doOnEnd {
                onEnd?.invoke()
            }
        }
    }

    private fun getMoveAnimator(onEnd: (() -> Unit)? = null): Animator? {
        val bodyViewHeight = bodyLayout?.height ?: -1
        val animViewHeight = contentLayout?.height ?: -1
        val topMargin = (contentLayout?.layoutParams as? LinearLayout.LayoutParams)?.topMargin ?: -1

        if ((bodyViewHeight < 0) || (animViewHeight < 0) || (topMargin < 0) || contentLayout == null) {
            return null
        }

        return contentLayout?.let { view ->
            ObjectAnimator.ofFloat(view, ANIMATOR_NAME_TRANSLATION_Y, 0f, ((bodyViewHeight - animViewHeight) / 2 - topMargin).toFloat())
                .apply {
                    duration = DURATION_ANIMATOR_MOVE
                    interpolator = COUIMoveEaseInterpolator()
                    doOnEnd {
                        onEnd?.invoke()
                    }
                }
        } ?: kotlin.run {
            null
        }
    }

    private fun doActionAfterAnimatorEnd(viewAnimator: ValueAnimator? = null, action: () -> Unit) {
        if (viewAnimator?.isRunning == true) {
            viewAnimator.doOnEnd {
                action.invoke()
            }
        } else {
            action.invoke()
        }
    }

    private fun playLockAnimation(animationView: EffectiveAnimationView, onEnd: (() -> Unit)? = null) {
        val animatorListener: Animator.AnimatorListener = object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                animationView.removeAllAnimatorListeners()
                onEnd?.invoke()
            }
        }
        animationView.addAnimatorListener(animatorListener)
        animationView.playAnimation()
    }

    class Builder(context: Context) :
        BaseBuilder<SafeBoxAlertDialog>(
            context,
            com.oplus.gallery.basebiz.R.style.SecurityChip_AlertDialog_Progress_Cancelable
        ) {

        private var title: String? = null
        private var maxProgress: Int = MAX_PROGRESS
        private var maxSize = 0
        private var onClickListener: DialogInterface.OnClickListener? = null
        private var negativeButtonText: CharSequence? = null

        fun setProgressTitle(titleResId: Int): Builder {
            this.title = context.getString(titleResId)
            return this
        }

        fun setMaxProgress(max: Int): Builder {
            this.maxProgress = max
            return this
        }

        fun setMaxSize(maxSize: Int): Builder {
            this.maxSize = maxSize
            return this
        }

        fun setNegativeButton(
            textId: Int,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            negativeButtonText = context.getString(textId)
            onClickListener = listener
            return this
        }

        fun setNegativeButton(
            text: CharSequence?,
            listener: DialogInterface.OnClickListener? = null
        ): Builder {
            negativeButtonText = text
            onClickListener = listener
            return this
        }

        override fun build(): SafeBoxAlertDialog {
            return SafeBoxAlertDialog(this).apply {
                this.title = <EMAIL>
                this.maxProgress = <EMAIL>
                this.maxSize = <EMAIL>
                this.negativeButtonText = <EMAIL>
                this.onClickListener = <EMAIL>
            }
        }
    }

    companion object {
        private const val TAG = "SafeBoxAlertDialog"
        private const val MAX_PROGRESS = 100

        private const val TIME_PROGRESS_MIN_SHOW = 700L

        private const val DELAY_DISMISS_SUCCESS = 400L
        private const val DELAY_DISMISS_FAIL = 750L

        private const val PROGRESS_ANIM_FRAME_MAX = 146
        private const val ALPHA_SHOW = 1F
        private const val ALPHA_HIDE = 0F
        private const val ALPHA_HIDE_SHOW_BUTTON = 0.84F
        private const val DURATION_ANIMATOR_ALPHA = 150L
        private const val DURATION_ANIMATOR_MOVE = 300L

        private const val ANIMATOR_NAME_ALPHA = "alpha"
        private const val ANIMATOR_NAME_TRANSLATION_Y = "translationY"
    }
}