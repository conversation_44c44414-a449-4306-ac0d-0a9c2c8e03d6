/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SelectionModelCache.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/07/08
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2022/07/08		1.0			OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.business_lib.model.selection

import android.os.Bundle
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.LabelModelGetter.Companion.TYPE_LABEL_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_ALL_PICTURE
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_ALL_SELECTABLE_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_OTHER_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_RECYCLE_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocationModelGetter.Companion.TYPE_LOCATION_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.MemoriesModelGetter.Companion.TYPE_MEMORIES_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonModelGetter.Companion.TYPE_OTHER_PERSON_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonModelGetter.Companion.TYPE_PERSON_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import java.lang.ref.WeakReference

object SelectionModelCache {
    private val cache = HashMap<String, WeakReference<BaseModel<*>>>()
    private val FIX_ALBUM_CACHE_KEY = arrayOf(
        TYPE_ALL_PICTURE,
        TYPE_RECYCLE_ALBUM
    )
    private val FIX_ALBUM_SET_CACHE_KEY = arrayOf(
        TYPE_ALL_SELECTABLE_ALBUM_SET,
        TYPE_OTHER_ALBUM_SET,
        TYPE_LABEL_ALBUM_SET,
        TYPE_LOCATION_ALBUM_SET,
        TYPE_MEMORIES_ALBUM_SET,
        TYPE_PERSON_ALBUM_SET,
        TYPE_OTHER_PERSON_ALBUM_SET
    )

    fun getAlbumCache(key: String, bundle: Bundle = Bundle()): BaseModel<*> {
        return cache[key]?.get() ?: DataRepository.getAlbumModel(key, bundle).also {
            if (key in FIX_ALBUM_CACHE_KEY) {
                cache[key] = WeakReference(it)
            }
        }
    }

    fun getAlbumSetCache(key: String, bundle: Bundle = Bundle()): BaseModel<*> {
        return cache[key]?.get() ?: DataRepository.getAlbumSetModel(key, bundle).also {
            if (key in FIX_ALBUM_SET_CACHE_KEY) {
                cache[key] = WeakReference(it)
            }
        }
    }
}