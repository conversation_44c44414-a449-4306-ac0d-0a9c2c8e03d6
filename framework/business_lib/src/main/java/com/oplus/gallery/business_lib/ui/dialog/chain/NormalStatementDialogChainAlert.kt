/********************************************************************************
 ** Copyright (C), 2022-2032, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - NormalStatementDialogChainAlert.kt
 ** Description:
 ** OptionsParcelable
 **
 ** Version: 1.0
 ** Date: 2022/6/27
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/6/27     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.business_lib.ui.dialog.chain

import android.content.Context
import android.content.res.Resources.ID_NULL
import androidx.appcompat.app.AlertDialog
import com.oplus.gallery.foundation.authorizing.ui.NormalStatementBuilder
import com.oplus.gallery.foundation.authorizing.ui.text.style.SpanParams

class NormalStatementDialogChainAlert private constructor(
    private val context: Context,
    private val alertType: Int = IAlert.ALERT_DEFAULT_TYPE,
) : IAlert {
    private var titleResId: Int = ID_NULL
    private var negativeResId: Int = ID_NULL
    private var positiveResId: Int = ID_NULL
    private var contentSpan: SpanParams = SpanParams(ID_NULL)
    private var linkSpan: SpanParams = SpanParams(ID_NULL)
    private var alertNegativeListener: IAlertNegativeListener<NormalStatementDialogChainAlert>? = null
    private var alertNextListener: IAlertNextListener<NormalStatementDialogChainAlert>? = null
    private var keyListener: IKeyListener<NormalStatementDialogChainAlert>? = null
    private var dialog: AlertDialog? = null

    override fun show() {
        dialog = NormalStatementBuilder(context)
            .setDescribeMessage(contentSpan.getText(context))
            .setDetailsLink(linkSpan.getText(context))
            .setTitle(titleResId)
            .setPositiveButton(positiveResId) { _, _ ->
                alertNextListener?.onNext(this@NormalStatementDialogChainAlert)
            }
            .setNegativeButton(negativeResId) { _, _ ->
                alertNegativeListener?.onNegative(this@NormalStatementDialogChainAlert)
            }
            .setOnKeyListener { _, keyCode, event ->
                keyListener?.onKey(this, keyCode, event) ?: false
            }
            .show()
    }

    override fun dismiss() {
        if (dialog?.isShowing == true) {
            dialog?.dismiss()
        }
    }

    override fun type() = alertType

    class Builder(context: Context) : IAlert.AbsBuilder<NormalStatementDialogChainAlert>(context) {
        private var titleResId: Int = ID_NULL
        private var negativeResId: Int = ID_NULL
        private var positiveResId: Int = ID_NULL
        private var contentSpan: SpanParams = SpanParams(ID_NULL)
        private var linkSpan: SpanParams = SpanParams(ID_NULL)

        fun buildTitle(resId: Int): Builder {
            titleResId = resId
            return this
        }

        fun buildNegative(resId: Int): Builder {
            negativeResId = resId
            return this
        }

        fun buildPositive(resId: Int): Builder {
            positiveResId = resId
            return this
        }

        fun buildContentSpan(spanParams: SpanParams): Builder {
            contentSpan = spanParams
            return this
        }

        fun buildLinkSpan(spanParams: SpanParams): Builder {
            linkSpan = spanParams
            return this
        }

        override fun create(): NormalStatementDialogChainAlert {
            return NormalStatementDialogChainAlert(
                context,
                alertType
            ).apply {
                titleResId = <EMAIL>
                negativeResId = <EMAIL>
                positiveResId = <EMAIL>
                contentSpan = <EMAIL>
                linkSpan = <EMAIL>
                alertNegativeListener = iAlertNegativeListener
                alertNextListener = iAlertNextListener
                keyListener = iKeyListener
            }
        }
    }
}