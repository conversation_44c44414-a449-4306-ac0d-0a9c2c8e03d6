/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TrackCallerEntry.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/12/21
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/12/21	   1.0         build this module
 ********************************************************************************/

package com.oplus.gallery.business_lib.menuoperation.track

import android.os.Parcel
import android.os.Parcelable

data class TrackCallerEntry(
    val page: String?,
    val path: String? = null,
    val trackAlbumPageInfo: String? = null,
    val currentAlbumName: String? = null,
    val albumsActionCurrentPage: String? = null
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(page)
        parcel.writeString(path)
        parcel.writeString(trackAlbumPageInfo)
        parcel.writeString(currentAlbumName)
        parcel.writeString(albumsActionCurrentPage)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<TrackCallerEntry> {
        override fun createFromParcel(parcel: Parcel): TrackCallerEntry {
            return TrackCallerEntry(parcel)
        }

        override fun newArray(size: Int): Array<TrackCallerEntry?> {
            return arrayOfNulls(size)
        }
    }
}
