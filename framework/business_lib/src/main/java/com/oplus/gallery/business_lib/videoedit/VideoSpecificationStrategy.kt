/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoSpecificationStrategy.kt
 * Description:
 * Version:
 * Date: 2022/5/19
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/19     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.videoedit

import android.util.Size
import com.oplus.gallery.business_lib.model.data.base.utils.VideoResolutionType
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtil.closestMultipleOfTwo
import com.oplus.gallery.videoeditor.data.VideoOptions
import com.oplus.gallery.videoeditor.data.VideoSpecification
import com.oplus.gallery.videoeditor.utils.CodecSupport
import kotlin.math.max
import kotlin.math.min

/**
 * 视频编辑业务通用的策略，最大支持到4k120fps编辑，根据视频格式和机器性能会调整到最低1080p30fps
 * 具体详见策略实现[processSpecification]
 */
class VideoSpecificationStrategy : ISpecificationStrategy {
    override fun processSpecification(mimeType: String, spec: VideoSpecification): Boolean {
        var localSpec = spec
        // 1 异常尺寸返回不支持编辑
        if ((localSpec.height <= 0) || (localSpec.width <= 0) || (localSpec.fps <= 0f)) {
            GLog.e(TAG, LogFlag.DL, "[processSpecification] error: video specification is invalid. specification = $localSpec")
            return false
        }

        // 2 解析硬件解码器支持的最大分辨率
        val dumpedCapabilities = CodecSupport.dumpVideoCapabilities(VideoOptions(mimeType, false, false))
        GLog.d(TAG, LogFlag.DL) { "[processSpecification] capability: {$dumpedCapabilities}" }

        // 先判断是否支持
        if (CodecSupport.isSupportCodec(mimeType, localSpec)) {
            return true
        }
        // 降分辨率
        val resolutionType = VideoResolutionType.getType(Size(localSpec.width, localSpec.height))
        var tmpSpec = VideoSpecification(localSpec.width, localSpec.height, localSpec.fps)
        val resultType = VideoResolutionType.entries.subList(0, resolutionType.ordinal).asReversed().find {
            tmpSpec = tmpSpec.copy(fps = localSpec.fps)
            resolutionLimit(tmpSpec, it)

            var isSupport = CodecSupport.isSupportCodec(mimeType, tmpSpec)
            if (!isSupport) {
                while (tmpSpec.fps / 2 > 28.0) {
                    tmpSpec = tmpSpec.copy(fps = tmpSpec.fps / 2)
                    isSupport = CodecSupport.isSupportCodec(mimeType, tmpSpec)
                    if (isSupport) break
                }
            }
            isSupport
        } ?: VideoResolutionType.R_1080P

        localSpec = localSpec.copy(fps = tmpSpec.fps)
        localSpec = resolutionLimit(spec, resultType)

        GLog.d(TAG, LogFlag.DL, "[processSpecification] the final edit specification is $spec")
        return CodecSupport.isSupportCodec(mimeType, spec)
    }

    private fun resolutionLimit(spec: VideoSpecification, type: VideoResolutionType): VideoSpecification {
        val resolution = spec.width * spec.height
        if (type.resolution in 1 until resolution) {
            val ratio = min(
                min(type.width, type.height).toDouble() / min(spec.width, spec.height),
                max(type.width, type.height).toDouble() / max(spec.width, spec.height)
            )
            GLog.d(TAG, LogFlag.DL, "[resolutionLimit] too large resolution, shrink percent:$ratio")
            return spec.copy((spec.width * ratio).closestMultipleOfTwo(), (spec.height * ratio).closestMultipleOfTwo())
        } else {
            return spec
        }
    }

    companion object {
        // 偏移指定fps 例如平台规格1920x1080@30fps的机型，
        // 允许1920x1080@32fps及以下的视频按1920x1080@30fps进视频编辑
        private const val SUPPORTED_FRAME_RATE_OFFSET = 2f

        private const val TAG = "VideoSpecificationStrategy"

        /**
         * 视频编辑最大支持的视频帧率：120
         */
        private const val MAX_SUPPORTED_FRAME_RATE = 120f

        /**
         * MediaCodec接口读出异常帧率时，用于兜底的帧率：30
         */
        private const val NORMAL_SUPPORTED_FRAME_RATE = 30f
    }
}