/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LightweightOcrResult.kt
 ** Description:
 ** 对应SDK中的OcrResultWrap，但是矩形都是经过压缩的
 ** Version: 1.0
 ** Date: 2022/05/16
 ** Author: wuhengze@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wuhengze@Apps.Gallery3D	       2022/05/16		1.0		create
 *********************************************************************************/
package com.oplus.gallery.business_lib.supertext.data

import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.util.text.JsonUtil

/**
 * 轻量化的OcrResult,对应SDK中的OCRResult
 * SDK中提供的rect四个点的坐标按顺序存到DoubleArray中
 * 第一个点为文字方向的左上角，按照顺时针的顺序，先x后y存储如下
 * (x0, y0, x1, y1, x2, y2, x3, y3, x4, y4, x5, y5, x6, y6, x7, y7)
 * 轻量后按照（中心点X,中心点Y,宽,高，矩形倾角）存储。执行压缩的逻辑在[LightweightOcrItem]
 */
class LightweightOcrResult() {

    @SerializedName("items")
    var items: Array<LightweightOcrItem?>? = null

    @SerializedName("is_editable")
    var isEditable: Boolean? = null

    constructor(ocrResultWrap: AiUnitOcrResultWrap) : this() {
        if (ocrResultWrap.containsText()) {
            items = ocrResultWrap.aiUnitOCRResult?.items?.map { ocrItem ->
                ocrItem?.let { LightweightOcrItem(it) }
            }?.toTypedArray()
            isEditable = ocrResultWrap.isEditable
        }
    }

    /**
     * 转换为jsonString
     */
    fun toJsonString(): String {
        return JsonUtil.toJson(this)
    }

    /**
     * 转化成AiUnitOCRResult，会将rect还原成如下形式
     * (x0, y0, x1, y1, x2, y2, x3, y3, x4, y4, x5, y5, x6, y6, x7, y7)
     */
    fun toAiUnitOcrResult(): AiUnitOCRResult {
        return AiUnitOCRResult(
            0,
            <EMAIL>?.map { it?.toOCRItem() }?.toTypedArray()
        )
    }
}