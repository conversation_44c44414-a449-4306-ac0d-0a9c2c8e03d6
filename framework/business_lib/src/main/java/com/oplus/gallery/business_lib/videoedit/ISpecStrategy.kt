/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - ISpecificationStrategy.kt
 * Description:
 * Version:
 * Date: 2022/5/19
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/19     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.videoedit

import com.oplus.gallery.videoeditor.data.VideoSpec

/**
 * 视频编辑的规格处理策略接口，用于对编辑视频的保存规格（宽高帧率）进行调整
 * 一般策略的处理结果取决于业务设计及机器性能
 */
interface ISpecStrategy {

    /**
     * 处理视频规格
     *
     * @param mimeType 视频的mime类型
     * @param spec 视频规格
     * @return 是否支持编辑
     */
    fun processSpec(mimeType: String, spec: VideoSpec): VideoSpec?
}