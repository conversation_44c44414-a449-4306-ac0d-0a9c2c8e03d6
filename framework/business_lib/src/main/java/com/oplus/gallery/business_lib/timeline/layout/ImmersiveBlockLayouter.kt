/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BlockLayouter.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/6/1 19:22
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2021/6/1      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.timeline.layout

import android.graphics.Rect
import androidx.annotation.CheckResult
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNodeType
import com.oplus.gallery.business_lib.timeline.draw.TimelineImmersiveDrawer.Companion.isTimeTileShow
import com.oplus.gallery.business_lib.util.TimelineUtils.INVALID_INDEX
import com.oplus.gallery.business_lib.util.TimelineUtils.TIMELINE_TAG
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.length
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.roundToInt

/**
 * 沉浸式视图布局实现，用于沉浸式年月日视图
 */
open class ImmersiveBlockLayouter(
    config: LayoutConfig,
    outStateGetter: OutStateGetter? = null,
    coroutineScope: CoroutineScope = AppScope,
    dispatcher: CoroutineDispatcher = Dispatchers.SINGLE,
) : BlockLayouter(config, outStateGetter, coroutineScope, dispatcher) {

    override var tag: String = "$TAG.$config"

    private val subNodeDataParams = mutableListOf<NodeLayoutParam>()
    private val subNodeDataParamsRWLock = ReentrantReadWriteLock()
    private var itemTotalCount = 0

    /**
     * Slot槽位偏移量，与布局方向无关，>0时，向右下方向偏移，<0时向左上方向偏移
     * 当前限定所有视图偏移范围为[-config.columnOfBlock+1， 0]
     */
    @Volatile
    var slotOffset: Int = 0
        private set(value) {
            field = (value % config.columnOfBlock - config.columnOfBlock) % config.columnOfBlock
        }

    /**
     * TimeNode布局节点的Y偏移，布局完成后TimeNode位置不随slotOffset变化，所有根据TimeNode位置算出的Slot、Block rect都要根据该值进行偏移
     */
    private var nodeOffsetY = 0

    /**
     * 全局行偏移量，在slotOffset<0时，可能存在上面多一行，下面少一行情形，这时需要整体向下偏移一行
     */
    private var globalRowOffset = 0
    // 最后一行隐藏时需要的整体需要偏移的Y值
    private var lastRowOffsetY = 0

    override fun layoutInternal(width: Int, scope: CoroutineScope?) {
        val tempNodeLayoutParams = mutableListOf<NodeLayoutParam>()
        timeNodesRWLock.read {
            initializeTimeNodes(tempNodeLayoutParams, width, scope)
            calculateLayoutParameters(width, tempNodeLayoutParams)
        }

        // 统一数据处理避免2个锁管理异常死锁
        subNodeDataParamsRWLock.write {
            subNodeDataParams.clear()
            subNodeDataParams.addAll(tempNodeLayoutParams)
        }
    }

    private fun initializeTimeNodes(list: MutableList<NodeLayoutParam>, width: Int, scope: CoroutineScope?) {
        slotWidth = calculateSlotWidth(width)
        slotHeight = slotWidth
        var rowIndex = 0
        nodeLayoutParamsAsync.clear()

        itemTotalCount = 0
        val columnOfBlock = config.columnOfBlock.coerceAtLeast(1)
        for (timeNode in timeNodes) {
            if (scope?.isActive == false) {
                GLog.d(tag, "layoutInternal. layout cancelled.")
                return
            }
            itemTotalCount += timeNode.itemRange.length()
            // 按照时间点分组的布局结构，用于绘制时间气泡标题显示
            list.add(NodeLayoutParam().apply {
                nodeDecoration = TimelineNodeDecoration(timeNode.position).apply {
                    config = <EMAIL>
                }
                index = timeNode.position
                firstItemIndex = timeNode.itemRange.first
                itemCount = timeNode.itemRange.length()

                firstRowIndex = rowIndex
                rowCount = ceil(itemCount / columnOfBlock.toFloat()).toInt()
                rowIndex += rowCount
            })
        }
    }

    private fun calculateLayoutParameters(width: Int, tempNodeLayoutParams: MutableList<NodeLayoutParam>) {
        var blockIndex = 0
        val columnOfBlock = config.columnOfBlock.coerceAtLeast(1)
        nodeLayoutParamsAsync.add(NodeLayoutParam().apply {
            itemCount = itemTotalCount
            firstBlockIndex = blockIndex
            val maxSlotCount = config.maxSlotCount.coerceAtLeast(1).toFloat()
            blockCount = ceil(itemCount / maxSlotCount).toInt()
            blockIndex += blockCount
            rowCount = ceil(itemCount / columnOfBlock.toFloat()).toInt()

            left = 0
            top = 0
            right = width
            contentBottom = heightOfRows(slotHeight, rowCount) + config.vGap
            bottom = contentBottom + config.timeNodeBottomMargin
        })

        val slotHeightWithGap = slotHeight + config.vGap
        val totalRowIndex = nodeLayoutParamsAsync.first().lastRowIndex
        for (index in tempNodeLayoutParams.indices) {
            /**
             *    TimeNode节点首尾相接布局，记录上下左右边界最大值
             *    ————————————————————————  contentTop
             *    |   Block  |          |
             *    |——————————|  Block   |
             *    |                     |
             *    |——————————|——————————|
             *    |    ...   |   ...    |
             *    |——————————|——————————  contentBottom
             *    ——————————————————————  bottom
             */
            tempNodeLayoutParams[index].apply {
                this.index = index
                left = 0
                top = if (revertLayoutDirection) {
                    (totalRowIndex - lastItemIndex / columnOfBlock) * slotHeightWithGap
                } else {
                    (lastItemIndex / columnOfBlock) * slotHeightWithGap
                }
                right = width
                contentTop = top
                contentBottom = contentTop + config.timeNodeTitleHeight
                bottom = contentBottom
                nodeDecoration?.nodeRect?.set(left, top, right, bottom)
            }
        }
        printTimeNodeListInfo(nodeLayoutParamsAsync)
    }

    override fun onLayoutCompleted() {
        super.onLayoutCompleted()
        if (slotOffset == 0) {
            slotOffset = getDefaultOffset()
            calculateLayoutHeight()
        }
    }

    @VisibleForTesting
    @Suppress("LongMethod")
    override fun updateVisibleRange(
        scrollPosition: Int,
        nodeParams: List<NodeLayoutParam>,
        force: Boolean
    ): Boolean {
        if (nodeParams.isEmpty()) {
            return false
        }
        val edgeSize: Int = (slotHeight * getVisibleRangeDetectThreshold()).roundToInt()
        // 上下各增加edgeSize缓冲区，降低触发频率
        val bufferDistance = if (isYearMonthPresentation()) {
            edgeSize
        } else {
            VISIBLE_RANGE_UPDATE_BUFFER_DISTANCE
        }
        if (!force && (abs(scrollPositionForVisibleRange - scrollPosition) < bufferDistance)) return false
        scrollPositionForVisibleRange = scrollPosition
        val top = scrollPosition - edgeSize
        val contentHeight = viewHeight + edgeSize * 2
        val bottom = top + contentHeight

        val startEdge = if (revertLayoutDirection) bottom else top
        val startItemIndex = getFirstItemIndexInLastRow(startEdge, nodeParams)

        /**
         *  endItemIndex等于startItemIndex加整数行-1,以保证从起点到终点是行*列个，不-1会多一个；
         *  当手机横屏3列时，一屏只能显示一行多点，缓冲区*2+本身的一屏超过了3屏，endItemIndex多出的这一个正好导致SlotSlidingWindow去
         *  setVisibleRange时itemRange.length() > swConfig.activeCacheSize，从而添加visibleRange失败
         */
        val endItemIndex = (startItemIndex + getItemCountByHeight(contentHeight) + config.columnOfBlock - 1)

        val newVisibleNodeRange: IntRange
        val newVisibleBlockRange: IntRange
        val newVisibleItemRange: IntRange

        val itemOffset = if (revertLayoutDirection) -slotOffset else slotOffset + config.columnOfBlock
        val offsetTotalCount = itemTotalCount + itemOffset
        if ((endItemIndex < 0) || (startItemIndex + 1 > offsetTotalCount)) {
            newVisibleNodeRange = IntRange.EMPTY
            newVisibleBlockRange = IntRange.EMPTY
            newVisibleItemRange = IntRange.EMPTY
        } else {
            val newStartIndex = startItemIndex.coerceAtLeast(0)
            // 偏移后的可见范围，与实际数据位置对应
            newVisibleItemRange = newStartIndex..endItemIndex.coerceAtMost(itemTotalCount - 1)
            val firstNodeIndex = findSubTimeNodeIndexByItemIndex(newVisibleItemRange.first)
            val lastNodeIndex = findSubTimeNodeIndexByItemIndex(newVisibleItemRange.last)
            newVisibleNodeRange = firstNodeIndex..lastNodeIndex
            // block索引从左上角0,0开始布局的范围
            val blockCount = nodeParams.firstOrNull()?.blockCount ?: 1
            val maxSlotCount = config.maxSlotCount.coerceAtLeast(1)
            val originVisibleItemRange = newStartIndex..endItemIndex.coerceAtMost(offsetTotalCount - 1)
            val firstBlockIndex = (originVisibleItemRange.first / maxSlotCount).coerceAtLeast(0)
            val lastBlockIndex = (originVisibleItemRange.last / maxSlotCount).coerceAtMost(blockCount - 1)
            newVisibleBlockRange = firstBlockIndex..lastBlockIndex
        }
        var rangeChanged = false
        if (visibleNodeRange != newVisibleNodeRange) {
            visibleNodeRange = newVisibleNodeRange
            rangeChanged = true
        }
        if (visibleBlockRange != newVisibleBlockRange) {
            visibleBlockRange = newVisibleBlockRange
            rangeChanged = true
        }
        if (visibleItemRange != newVisibleItemRange) {
            visibleItemRange = newVisibleItemRange
            rangeChanged = true
        }
        return rangeChanged || force
    }

    /**
     * 返回最后一行第一个Item的索引，正向布局在最左边，反向布局在最右边
     */
    private fun getFirstItemIndexInLastRow(targetPosition: Int, nodeParams: List<NodeLayoutParam>): Int {
        return if (revertLayoutDirection) {
            val lastRowIndex = nodeParams.firstOrNull()?.lastRowIndex ?: (targetPosition / (slotHeight + config.vGap).coerceAtLeast(1))
            lastRowIndex * config.columnOfBlock - getItemCountByHeight(targetPosition)
        } else {
            getItemCountByHeight(targetPosition)
        }
    }

    private fun getItemCountByHeight(height: Int): Int {
        val rowIndex = height / (slotHeight + config.vGap).coerceAtLeast(1)
        return rowIndex * config.columnOfBlock
    }

    override fun dispatchClickEvent(
        x: Int,
        y: Int,
        ignoreTitle: Boolean,
        alternateLast: Boolean,
        selectModeSpec: Int,
        clickListener: OnElementClickListener?
    ) {
        if ((x < 0) || (x > viewWidth)) {
            return
        }
        val layoutX = x
        val layoutY = y + scrollPosition
        val offsetY = nodeOffsetY + globalRowOffset * slotHeightWithGap
        subNodeDataParamsRWLock.read {
            for (nodeIndex in visibleNodeRange) {
                val nodeLayoutParam = subNodeDataParams.getOrNull(nodeIndex) ?: continue
                val timeNode = getTimeNode(nodeIndex) ?: continue
                val contentTop = nodeLayoutParam.contentTop + offsetY
                val contentBottom = nodeLayoutParam.contentBottom + offsetY
                val nodeDecoration = nodeLayoutParam.nodeDecoration as? TimelineNodeDecoration
                if (layoutY in contentTop until contentBottom && ignoreTitle.not()
                    && isTimeTileShow(timeNode, config, nodeDecoration?.forceShow == true)) {
                    nodeDecoration?.let {
                        rectOfTimeNode(nodeIndex, tmpRect)
                        val rX = layoutX - tmpRect.left
                        val rY = layoutY - tmpRect.top - offsetY
                        if (it.titleContentRect.contains(rX, rY)) {
                            // 找当前时间第一张，时间节点降序，应该取最后一个值
                            val resultIndex = timeNode.itemRange.last
                            clickListener?.onElementClick(nodeIndex, resultIndex, ELEMENT_TYPE_TITLE)
                            return
                        }
                    }
                }
            }
        }

        val itemIndex = findItemIndexByLayoutPosition(layoutX, layoutY)
        val subTimeNodeIndex = findSubTimeNodeIndexByItemIndex(itemIndex)
        if (itemIndex != INVALID_INDEX) {
            clickListener?.onElementClick(subTimeNodeIndex, itemIndex, ELEMENT_TYPE_SLOT_ITEM)
            return
        }
    }

    private fun findItemIndexByLayoutPosition(
        layoutX: Int,
        layoutY: Int,
    ): Int {
        val timeNode = nodeLayoutParams.firstOrNull() ?: return INVALID_INDEX
        val rx = if (isRTLMode) {
            timeNode.width() - (layoutX - timeNode.left)
        } else {
            layoutX - timeNode.left
        }
        val ry = layoutY - timeNode.contentTop - nodeOffsetY - globalRowOffset * slotHeightWithGap
        val col = (rx / slotWidthWithGap).coerceAtMost(config.columnOfBlock - 1)
        val row = (ry + slotHeightWithGap) / slotHeightWithGap - 1

        return if (revertLayoutDirection) {
            val itemIndex = (timeNode.rowCount * config.columnOfBlock - 1) - (row * config.columnOfBlock + col) + slotOffset
            when {
                (itemIndex in timeNode.firstItemIndex..timeNode.lastItemIndex) -> itemIndex
                else -> INVALID_INDEX
            }
        } else {
            val itemIndex = timeNode.firstItemIndex + row * config.columnOfBlock + col - slotOffset
            when {
                (itemIndex <= timeNode.lastItemIndex) -> itemIndex
                else -> INVALID_INDEX
            }
        }
    }

    override fun findVisibleItemUnder(
        x: Int,
        y: Int,
        outRect: Rect?,
        ignoreTitle: Boolean,
        ignoreCheckBox: Boolean,
        alternateLast: Boolean
    ): Pair<Int, Int> {
        val nodeLayoutParam = nodeLayoutParams.firstOrNull()
        if ((x < 0) || (x > viewWidth) || (nodeLayoutParam == null)) {
            outRect?.setInvalid()
            return Pair(INVALID_INDEX, INVALID_INDEX)
        }
        val layoutX = x
        val layoutY = y + scrollPosition
        val itemIndex = findItemIndexByLayoutPosition(layoutX, layoutY)
        if (visibleItemRange.contains(itemIndex).not()) {
            outRect?.setInvalid()
            return Pair(INVALID_INDEX, INVALID_INDEX)
        }
        val timeNodeIndex = findSubTimeNodeIndexByItemIndex(itemIndex)
        if (timeNodeIndex == INVALID_INDEX) {
            outRect?.setInvalid()
            return Pair(INVALID_INDEX, INVALID_INDEX)
        }
        outRect?.let {
            rectOfItemInTimeNode(itemIndex, nodeLayoutParam, it)
        }
        return Pair(timeNodeIndex, itemIndex)
    }

    private fun findSubTimeNodeIndexByItemIndex(itemIndex: Int): Int = timeNodesRWLock.read {
        findTimeNodeByItemIndex(0, timeNodes.size - 1, itemIndex).apply {
            if (this == INVALID_INDEX) {
                GLog.w(TAG, LogFlag.DL) { "itemIndex=$itemIndex, NodeIndex is invalid" }
            }
        }
    }

    @Suppress("ComplexCondition")
    private fun findTimeNodeByItemIndex(left: Int, right: Int, targetItemIndex: Int): Int {
        if ((left > right)
            || (left < 0)
            || (right >= timeNodes.size)
            || (targetItemIndex < timeNodes[left].itemRange.first)
            || (targetItemIndex > timeNodes[right].itemRange.last)
        ) {
            return INVALID_INDEX
        }
        if (left == right) return left

        val mid = left + (right - left) * (targetItemIndex - timeNodes[left].itemRange.first) /
            (timeNodes[right].itemRange.last - timeNodes[left].itemRange.first)
        if (mid !in timeNodes.indices) {
            return INVALID_INDEX
        }
        return when {
            targetItemIndex > timeNodes[mid].itemRange.last -> findTimeNodeByItemIndex(mid + 1, right, targetItemIndex)
            targetItemIndex < timeNodes[mid].itemRange.first -> findTimeNodeByItemIndex(left, mid - 1, targetItemIndex)
            else -> mid
        }
    }

    override fun centerItemIndex(): Int {
        return findVisibleItemUnder(viewWidth / 2, viewHeight / 2).second
    }

    private fun heightOfRows(slotSize: Int, rowCount: Int) =
        ((slotSize + config.vGap) * rowCount - config.vGap).coerceAtLeast(0)

    @CheckResult
    override fun firstRowIndexOfTimeNode(nodeIndex: Int): Int {
        subNodeDataParamsRWLock.read {
            return if (nodeIndex in 0 until subNodeDataParams.size) {
                rowIndexOfVisibleItem(subNodeDataParams[nodeIndex].firstItemIndex)
            } else {
                INVALID_INDEX
            }
        }
    }

    @CheckResult
    override fun lastRowIndexOfTimeNode(nodeIndex: Int): Int {
        subNodeDataParamsRWLock.read {
            return if (nodeIndex in 0 until subNodeDataParams.size) {
                rowIndexOfVisibleItem(subNodeDataParams[nodeIndex].lastItemIndex)
            } else {
                INVALID_INDEX
            }
        }
    }

    override fun rectOfBlock(blockIndex: Int, outRect: Rect, isRelative: Boolean) {
        val rowHeight = slotHeightWithGap
        val node = nodeLayoutParams.firstOrNull() ?: run {
            outRect.setInvalid()
            return@rectOfBlock
        }

        val lastBlockIndex = node.blockCount - 1
        val rowCountOfBlock = if (blockIndex == lastBlockIndex) {
            (node.rowCount - config.rowOfBlock * blockIndex).coerceAtMost(config.rowOfBlock)
        } else {
            config.rowOfBlock
        }
        val blockHeightMax = heightOfRows(slotHeight, config.rowOfBlock)

        outRect.left = 0
        val offsetY = nodeOffsetY + globalRowOffset * slotHeightWithGap
        if (revertLayoutDirection) {
            outRect.bottom = node.height() - config.timeNodeBottomMargin - config.vGap - (blockHeightMax + config.vGap) * blockIndex + offsetY
            outRect.top = outRect.bottom - rowCountOfBlock * rowHeight + config.vGap
        } else {
            outRect.top = (blockHeightMax + config.vGap) * blockIndex + offsetY
            outRect.bottom = outRect.top + rowCountOfBlock * rowHeight - config.vGap
        }
        outRect.right = blockWidth
        if (isRelative) {
            outRect.offset(0, -scrollPosition)
        }
    }

    override fun rectOfItem(itemIndex: Int, outRect: Rect, isRelative: Boolean) {
        nodeLayoutParams.firstOrNull()?.apply {
            if (rectOfItemInTimeNode(itemIndex, this, outRect)) {
                if (isRelative) outRect.offset(0, -scrollPosition)
                return@rectOfItem
            }
        }
        outRect.setInvalid()
    }

    override fun rectOfVisibleItem(itemIndex: Int, outRect: Rect, isRelative: Boolean) {
        rectOfItem(itemIndex, outRect, isRelative)
    }

    override fun rowRectListInRange(rowRange: IntRange): List<Rect> {
        return super.rowRectListInRange(rowRange).apply {
            this.forEach { it.offset(0, nodeOffsetY) }
        }
    }

    @CheckResult
    override fun rowIndexOfVisibleItem(itemIndex: Int): Int {
        return itemIndex / config.columnOfBlock + getGlobalRowOffset(itemIndex)
    }

    override fun rowRangeOfBlock(blockIndex: Int): IntRange {
        nodeLayoutParams.firstOrNull()?.apply {
            val first = firstRowIndex + (blockIndex - firstBlockIndex) * config.rowOfBlock
            val last = (first + config.rowOfBlock - 1).coerceAtMost(lastRowIndex)
            return@rowRangeOfBlock (first + globalRowOffset)..(last + globalRowOffset)
        }
        return IntRange.EMPTY
    }

    private fun rectOfItemInTimeNode(itemIndex: Int, node: NodeLayoutParam, outRect: Rect): Boolean {
        return if (itemIndex in node.firstItemIndex..node.lastItemIndex) {
            val col = if (revertLayoutDirection) {
                config.columnOfBlock - 1 - (itemIndex - slotOffset) % config.columnOfBlock
            } else {
                (itemIndex + slotOffset + config.columnOfBlock) % config.columnOfBlock
            }
            val row = if (revertLayoutDirection) {
                (node.rowCount - 1) - (itemIndex / config.columnOfBlock) - getGlobalRowOffset(itemIndex)
            } else {
                itemIndex / config.columnOfBlock + getGlobalRowOffset(itemIndex)
            }

            outRect.apply {
                left = if (isRTLMode) {
                    node.width() - col * slotWidthWithGap - slotWidth
                } else {
                    col * slotWidthWithGap
                }
                top = node.contentTop + nodeOffsetY + row * slotHeightWithGap
                right = left + slotWidth
                bottom = top + slotHeight
            }
            true
        } else false
    }

    @CheckResult
    override fun firstItemIndexOfBlock(blockIndex: Int): Int = itemRangeOfBlock(blockIndex).first

    @CheckResult
    override fun lastItemIndexOfBlock(blockIndex: Int): Int = itemRangeOfBlock(blockIndex).last

    @CheckResult
    override fun itemCountOfBlock(blockIndex: Int): Int {
        return (allItemSize() - config.maxSlotCount * blockIndex).coerceAtMost(config.maxSlotCount)
    }

    private fun itemRangeOfBlock(blockIndex: Int): IntRange {
        val first = blockIndex * config.maxSlotCount
        val last = ((blockIndex + 1) * config.maxSlotCount - 1).coerceAtMost(itemTotalCount - 1)
        return first..last
    }

    override fun <T : BaseNodeDecoration> decorationOfTimeNode(nodeIndex: Int): T? = subNodeDataParamsRWLock.read {
        if (nodeIndex in subNodeDataParams.indices) {
            subNodeDataParams[nodeIndex].nodeDecoration as? T
        } else null
    }

    override fun rectOfTimeNode(nodeIndex: Int, outRect: Rect, isRelative: Boolean) {
        subNodeDataParamsRWLock.read {
            subNodeDataParams.getOrNull(nodeIndex)?.let { param ->  // 使用 getOrNull 避免越界
                outRect.set(param.left, param.top + nodeOffsetY, param.right, param.bottom + nodeOffsetY)
                // 确保标签位置在 [0, layoutHeight] 范围内
                if (outRect.top < 0) {
                    outRect.offset(0, -outRect.top)
                } else if (outRect.top >= layoutHeight) {
                    outRect.offset(0, -slotHeightWithGap.coerceAtLeast(outRect.top - layoutHeight))
                }
                if (isRelative) {
                    outRect.offset(0, -scrollPosition)
                }
            } ?: outRect.setInvalid()  // 如果 nodeIndex 越界，返回无效 Rect
        }
    }

    /**
     * 指定焦点位置作为中心来布局，原先的布局需要参考此动态调整偏移量将其布局到中间
     * @param offset 需要移动到中心点的索引
     */
    fun updateItemOffset(offset: Int) {
        slotOffset = offset
        // 视图偏移后，需要更新可见性范围
        updateVisibleRange(force = true)
        calculateLayoutHeight()
    }

    private fun calculateLayoutHeight() {
        val firstRowOffset = getRelativeRowOffset(0)
        val lastRowOffset = getRelativeRowOffset(itemTotalCount - 1)
        val (firstItemIndex, lastItemIndex) = if (revertLayoutDirection) {
            itemTotalCount - 1 to 0
        } else {
            0 to itemTotalCount - 1
        }
        val rowOffset = if (needHideFirstRow(firstItemIndex)) -1 else 0
        nodeOffsetY = (abs(firstRowOffset - lastRowOffset) + rowOffset) * slotHeightWithGap
        globalRowOffset = if ((abs(firstRowOffset) == 1) && (abs(lastRowOffset) == 1)) 1 else 0
        lastRowOffsetY = if (needHideLastRow(lastItemIndex)) -slotHeightWithGap else 0
        layoutHeight = (nodeLayoutParams.firstOrNull()?.contentBottom ?: 0) + nodeOffsetY + config.timeNodeBottomMargin + lastRowOffsetY
    }

    fun needHideFirstRow(firstItemIndex: Int): Boolean {
        return (getColIndexByItemIndex(firstItemIndex) != 0) && isYearMonthPresentation() && (itemTotalCount > OFFSET_ROW * config.columnOfBlock)
    }

    fun needHideLastRow(lastItemIndex: Int): Boolean {
        return (getColIndexByItemIndex(lastItemIndex) != (config.columnOfBlock - 1))
                && isYearMonthPresentation()
                && (itemTotalCount > OFFSET_ROW * config.columnOfBlock)
    }

    fun calculateColumnOffset(): Int {
        return if (slotOffset < 0) {
            (config.columnOfBlock + slotOffset % config.columnOfBlock) % config.columnOfBlock
        } else 0
    }

    private fun getColIndexByItemIndex(itemIndex: Int): Int {
        return if (revertLayoutDirection) {
            config.columnOfBlock - 1 - (itemIndex - slotOffset) % config.columnOfBlock
        } else {
            (itemIndex + slotOffset + config.columnOfBlock) % config.columnOfBlock
        }
    }

    /**
     * 重置偏移默认值
     */
    fun restOffset() {
        slotOffset = 0
    }

    /**
     * 默认偏移量，首页会重写该方法
     */
    open fun getDefaultOffset(): Int = 0

    /**
     * 获取item的行偏移
     */
    private fun getRelativeRowOffset(itemIndex: Int): Int {
        if (slotOffset < 0) {
            if (revertLayoutDirection) {
                val colIndex = config.columnOfBlock - 1 - itemIndex % config.columnOfBlock
                return if (colIndex + slotOffset < 0) 1 else 0
            } else {
                val colIndex = itemIndex % config.columnOfBlock
                return if (colIndex + slotOffset < 0) -1 else 0
            }
        }
        return 0
    }

    private fun getGlobalRowOffset(itemIndex: Int): Int = getRelativeRowOffset(itemIndex) + globalRowOffset

    private fun isYearMonthPresentation() = ((config.nodeType == TimeNodeType.YEAR) || (config.nodeType == TimeNodeType.MONTH))

    override fun allNodeSize(): Int = subNodeDataParamsRWLock.read {
        subNodeDataParams.size
    }

    companion object {
        private const val TAG = "${TIMELINE_TAG}ImmersiveBlockLayouter"

        private const val VISIBLE_RANGE_UPDATE_BUFFER_DISTANCE = 10
        // 年月视图首尾两行在动态捏合过程中会动态隐藏空缺的行，所以不满4行的数据，不偏移隐藏，保证显示效果
        const val OFFSET_ROW = 4
    }
}
