/********************************************************************************
 ** Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File: - TimelineImmersiveDrawer.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2025-03-27
 ** Author: <PERSON>ona<PERSON>.<PERSON><PERSON>@Android.Apps.Gallery
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>    <date>    <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON><PERSON>@Android.Apps.Gallery    2025/03/27    1.0   Create
 ********************************************************************************/
package com.oplus.gallery.business_lib.timeline.draw

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.style.ForegroundColorSpan
import android.text.style.TypefaceSpan
import android.util.Size
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.Companion.EXTRA_KEY_TIME_TITLE
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNodeType
import com.oplus.gallery.business_lib.timeline.layout.LayoutConfig
import com.oplus.gallery.business_lib.timeline.layout.TimelineNodeDecoration
import com.oplus.gallery.business_lib.util.TimelineUtils.ALPHA_OPAQUE
import com.oplus.gallery.foundation.util.collections.ConcurrentExtraMap
import com.oplus.gallery.foundation.util.math.isInvalid
import com.oplus.gallery.foundation.util.math.length
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.typeface.TypefaceUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.FORMAT_MONTH_CH
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.FORMAT_YEAR_CH
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.getChinaTimeString
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.isChineseLocale
import java.util.Date
import java.util.Locale
import com.support.appcompat.R as CouiR


/**
 * 沉浸式视图下装饰视图绘制，比如年月视图的时间标签
 */
class TimelineImmersiveDrawer(
    context: Context,
    private var layoutConfig: LayoutConfig
) : BaseDecorationDrawer<TimelineNodeDecoration>(context, layoutConfig) {
    private val titleRegionPaddingStart by lazy { context.resources.getDimensionPixelSize(R.dimen.base_timeline_title_region_padding_start) }
    private val timeTitleTypeface by lazy { TypefaceUtil.SANS_SERIF_MEDIUM }
    private val timeTitleBoldTypeface by lazy { TypefaceUtil.SANS_SERIF_SEMIBOLD }
    private val timeTitleTextSize by lazy { context.resources.getDimension(R.dimen.base_timeline_time_title_text_size) }
    private val timeTitleTextColor by lazy {
        if (COUIDarkModeUtil.isNightMode(context)) {
            context.getColor(CouiR.color.coui_color_primary_neutral_dark)
        } else {
            context.getColor(CouiR.color.coui_color_primary_neutral)
        }
    }
    private val timeTitleHeight by lazy { layoutConfig.timeNodeTitleHeight }
    private var timePaint: Paint = Paint().apply {
        color = context.resources.getColor(R.color.base_timeline_title_backgound, null)
        style = Paint.Style.FILL
    }
    private val timeTitleBgRadius by lazy { context.resources.getDimensionPixelOffset(R.dimen.base_timeline_immersive_time_title_radius) }
    private var timeTitleWidthMax = 0
    private val timePaddingHorizon
            by lazy { context.resources.getDimensionPixelOffset(R.dimen.base_timeline_immersive_time_title_padding_start_end) }
    private val subTimeTileColor by lazy {
        if (COUIDarkModeUtil.isNightMode(context)) {
            context.getColor(CouiR.color.coui_color_label_secondary_dark)
        } else {
            context.getColor(CouiR.color.coui_color_label_secondary)
        }
    }

    override fun onDraw(
        canvas: Canvas,
        timeNode: TimeNode,
        nodeDecoration: TimelineNodeDecoration,
        alpha: Float
    ) {
        val title = timeNode.extraInfo.getCharSequence(EXTRA_KEY_TIME_TITLE)
        if (isTimeTileShow(timeNode, layoutConfig, nodeDecoration.forceShow).not() || title.isNullOrEmpty()) return
        // 绘制背景drawable
        drawTextBackground(canvas, nodeDecoration, alpha * nodeDecoration.titleAlpha)
        // 设置时间标题透明度
        changeSpanAlpha(timeNode, alpha * nodeDecoration.titleAlpha)
        // 绘制时间标题
        drawText(
            canvas = canvas,
            rect = nodeDecoration.subTitleContentRect,
            textDrawable = findTextDrawable(
                title,
                timeTitleWidthMax,
                timeTitleTextSize,
                timeTitleTypeface,
                timeTitleTextColor
            ),
            alpha = alpha * nodeDecoration.titleAlpha,
            offsetY = nodeDecoration.titleOffsetY,
        )
    }

    private fun drawTextBackground(canvas: Canvas, nodeDecoration: TimelineNodeDecoration, alpha: Float) {
        val titleContentRect = nodeDecoration.titleContentRect
        if (titleContentRect.isEmpty  || isValid(titleContentRect).not()) return
        val cornerRadius = timeTitleBgRadius.toFloat()
        val roundRect = RectF(
            titleContentRect.left.toFloat(),
            titleContentRect.top.toFloat(),
            titleContentRect.right.toFloat(),
            titleContentRect.bottom.toFloat()
        )
        roundRect.offset(0f, nodeDecoration.titleOffsetY.toFloat())
        timePaint.alpha = (alpha * ALPHA_OPAQUE).toInt()
        canvas.drawRoundRect(
            roundRect,
            cornerRadius,
            cornerRadius,
            timePaint
        )
    }

    private fun changeSpanAlpha(timeNode: TimeNode, alpha: Float) {
        val colorSpanList = timeNode.extraInfo[EXTRA_KEY_TIME_TITLE_COLOR_SPAN] as? List<AlphaForegroundColorSpan> ?: return
        colorSpanList.takeIf { it.isNotEmpty() }?.let {
            it.forEach { span ->
                span.setAlpha(alpha)
            }
        }
    }

    private fun isValid(rect: Rect): Boolean {
        return rect.left <= rect.right && rect.top <= rect.bottom
    }

    /**
     * 测量Title区域内容
     */
    override fun measureIfNeed(timeNode: TimeNode, nodeDecoration: TimelineNodeDecoration) {
        nodeDecoration.apply {
            val needMeasure = (isDirty || (isRTL != isRTLMode)
                    || (timeNode.extraInfo.getCharSequence(EXTRA_KEY_TIME_TITLE) == null))
                    && isTimeTileShow(timeNode, layoutConfig, nodeDecoration.forceShow)
            if (needMeasure) {
                isDirty = false
                isRTL = isRTLMode
                val nodeWidth = nodeDecoration.nodeRect.width()
                val availableWidth = nodeWidth - titleRegionPaddingStart
                measureTitleRect(timeNode, this, availableWidth)
            }
        }
    }

    private fun measureTitleRect(
        timeNode: TimeNode,
        nodeDecoration: TimelineNodeDecoration,
        availableWidth: Int
    ) {
        val timeTitle = transformTimeTitle(timeNode).apply {
            timeNode.extraInfo.putCharSequence(EXTRA_KEY_TIME_TITLE, this)
        }.toString()
        textPaint.typeface = timeTitleBoldTypeface
        textPaint.textSize = timeTitleTextSize
        timeTitleWidthMax = availableWidth
        val titleWidth = nodeDecoration.nodeRect.width()
        val titleCenterY = timeTitleHeight / 2

        val size = getCacheTextSize(timeTitle)
            ?: (textPaint.measureText(timeTitle).toInt()).let { width ->
                val size = Size(width, (textPaint.descent() - textPaint.ascent()).toInt())
                putCacheTextSize(timeTitle, size)
                size
            }

        val timeTextWidth = size.width
        val timeTextHalfHeight = size.height / 2

        nodeDecoration.titleContentRect.apply {
            if (nodeDecoration.isRTL) {
                right = titleWidth - titleRegionPaddingStart
                left = right - timeTextWidth - timePaddingHorizon * 2
            } else {
                left = titleRegionPaddingStart
                right = left + timeTextWidth + timePaddingHorizon * 2
            }

            top = 0
            bottom = timeTitleHeight
        }
        nodeDecoration.subTitleContentRect.apply {
            if (nodeDecoration.isRTL) {
                right = titleWidth - titleRegionPaddingStart - timePaddingHorizon
                left = right - timeTextWidth
            } else {
                left = titleRegionPaddingStart + timePaddingHorizon
                right = left + timeTextWidth
            }

            top = titleCenterY - timeTextHalfHeight
            bottom = titleCenterY + timeTextHalfHeight
        }
    }

    /**
     * 横竖屏切换或者大小屏切换场景更新显示的列数等配置
     * @param config 布局配置
     */
    fun updateLayoutConfig(config: LayoutConfig) {
        layoutConfig = config
    }

    /**
     * 时间戳转换成按年、月分组的富文本字符串，实现分段不同样式的颜色字重
     */
    private fun transformTimeTitle(timeNode: TimeNode): CharSequence {
        val timestamp = timeNode.timestamp
        val extraInfo = timeNode.extraInfo
        return when (layoutConfig.nodeType) {
            TimeNodeType.YEAR -> {
                val date = Date(timestamp)
                val yearDate: String = if (isChineseLocale(Locale.getDefault())) {
                    getChinaTimeString(date, FORMAT_YEAR_CH)
                } else {
                    TimeUtils.getYearDate(date)
                }
                createYearStyleText(yearDate, extraInfo)
            }
            TimeNodeType.MONTH -> {
                val date = Date(timestamp)
                val yearMonthDate: String = TimeUtils.getYearMonthDate(date)
                val yearDate: String
                val monthDate: String
                if (isChineseLocale(Locale.getDefault())) {
                    yearDate = getChinaTimeString(date, FORMAT_YEAR_CH)
                    monthDate = getChinaTimeString(date, FORMAT_MONTH_CH)
                } else {
                    yearDate = TimeUtils.getYearDate(date)
                    monthDate = TimeUtils.getMDate(date)
                }
                createMonthStyleText(yearMonthDate, yearDate, monthDate, extraInfo)
            }
            else -> TextUtil.EMPTY_STRING
        }
    }

    private fun createMonthStyleText(yearMonth: String, year: String, month: String, extraInfo: ConcurrentExtraMap): SpannableString {
        val fullText = if (isChineseLocale(Locale.getDefault())) {
            "$year $month"
        } else {
            yearMonth
        }

        val yearStart = fullText.indexOf(year)
        val yearEnd = yearStart + year.length
        val monthRanges = listOf(IntRange(0, yearStart), IntRange(yearEnd, fullText.length)).filterNot { it.isInvalid() }

        val spannable = SpannableString(fullText)
        if (yearStart == INDEX_INVALID) return spannable

        val foregroundColorSpans = mutableListOf<AlphaForegroundColorSpan>()

        // 设置年份样式
        AlphaForegroundColorSpan(subTimeTileColor).let {
            spannable.setSpan(it, yearStart, yearEnd, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            foregroundColorSpans.add(it)
        }

        // 设置月份样式
        monthRanges.forEach {
            if (it.isInvalid()) return@forEach
            AlphaForegroundColorSpan(timeTitleTextColor).let { span ->
                spannable.setSpan(span, it.first, it.last, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
                spannable.setSpan(TypefaceSpan(timeTitleBoldTypeface), it.first, it.last, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
                foregroundColorSpans.add(span)
            }
        }
        extraInfo[EXTRA_KEY_TIME_TITLE_COLOR_SPAN] = foregroundColorSpans.toList()

        return spannable
    }

    private fun createYearStyleText(year: String, extraInfo: ConcurrentExtraMap): SpannableString {
        val spannable = SpannableString(year)
        val timeTitleTextColorSpan = AlphaForegroundColorSpan(timeTitleTextColor)
        spannable.setSpan(timeTitleTextColorSpan, 0, year.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        spannable.setSpan(TypefaceSpan(timeTitleBoldTypeface), 0, year.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        extraInfo[EXTRA_KEY_TIME_TITLE_COLOR_SPAN] = listOf(timeTitleTextColorSpan)
        return spannable
    }

    private inner class AlphaForegroundColorSpan(private val color: Int) : ForegroundColorSpan(color) {
        private var alpha: Int = Color.alpha(color)
        private var changeColor = color

        /**
         * 颜色透明度变化，0=0, 1=color的透明度
         * @param alphaProcess 透明度比例[0..1]
         */
        fun setAlpha(alphaProcess: Float) {
            changeColor = Color.argb((alpha * alphaProcess).toInt(), Color.red(color), Color.green(color), Color.blue(color))
        }

        override fun updateDrawState(textPaint: TextPaint) {
            // 动态设置颜色，包含当前Alpha值
            textPaint.color = changeColor
        }
    }

    companion object {
        private const val INDEX_INVALID = -1

        const val MONTH_NUM_PAGE = 2
        const val YEAR_NUM_PAGE = 3
        private const val EXTRA_KEY_TIME_TITLE_COLOR_SPAN = "timeTitleColorSpan"

        /**
         * 时间标签显示条件判断，根据交互定义，年视图至少3列数据 月视图至少2列数据才显示
         * @param timeNode 时间节点数据
         * @param config 视图布局配置
         * @param forceShow 标签不重叠的时候强制显示
         */
        @JvmStatic
        fun isTimeTileShow(timeNode: TimeNode, config: LayoutConfig, forceShow: Boolean = false): Boolean {
            return when (config.nodeType) {
                TimeNodeType.YEAR -> timeNode.itemRange.length() > YEAR_NUM_PAGE * config.columnOfBlock
                TimeNodeType.MONTH -> timeNode.itemRange.length() > MONTH_NUM_PAGE * config.columnOfBlock
                else -> false
            } || forceShow
        }
    }
}