/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoCleanHelper.kt
 ** Description: 照片清理相关
 ** Version: 1.0
 ** Date: 2023/11/22
 ** Author: <EMAIL>
 *
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** <EMAIL>     2023/11/22        1.0            Add new file.
 *************************************************************************************************/
package com.oplus.gallery.business_lib.photoclean

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Key.ALBUMS_ACTION_PHOTO_CLEAN_ID_KEY
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Action.ACTION_PHONE_CLEAN
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_NAME_PHONE_MANAGER
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_NAME_PHONE_MANAGER_ONEPLUS
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Property.PROPERTY_IS_OP_BRAND
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.app.getAppAbility

private const val TAG = "PhotoCleanHelper"

object PhotoCleanHelper {
    private const val PHONE_MANAGER_PHOTO_CLEAN_META = "com.coloros.phonemanager.gallery_clear"
    private const val PHOTO_CLEAN_META_DATA_VALUE = "phonemanager_gallery_clear"
    private const val PHONE_MANAGER_PHOTO_CLEAN_META_ONEPLUS = "com.oplus.phonemanager.gallery_clear"
    private val isOnePlusExport by lazy {
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN, defValue = false, expDefValue = true).not()
                && PROPERTY_IS_OP_BRAND
    }

    /**
     * 启动【照片清理】应用
     */
    fun startPhotoClean(activity: Activity) {
        activity.getAppAbility<ISettingsAbility>()?.use {
            it.setPhotoCleanIsSupportShowRedDotLocal(false)
        }
        if (isPhotoCleanEnabled(activity).getOrDefault(false)) {
            if (isExistPhotoCleanAction(activity)) {
                trackAndSendEnterPhotoClean(ALBUMS_ACTION_PHOTO_CLEAN_ID_KEY)
                jumpPhotoCleanAPPByAction(activity)
            } else {
                GLog.w(TAG, LogFlag.DL) { "photoClean action is null" }
            }
        } else {
            showDialog(activity)
        }
    }

    /**
     * 显示dialog提示
     */
    fun showDialog(activity: Activity) {
        if (activity.isFinishing || activity.isDestroyed) {
            GLog.d(TAG, LogFlag.DL) { "activity has finished" }
            return
        }
        ConfirmDialog.Builder(activity).apply {
            setTitle(com.oplus.gallery.basebiz.R.string.main_enable_photo_clean_title)
            setMessage(com.oplus.gallery.basebiz.R.string.main_enable_photo_clean_message)
            setNegativeButton(com.oplus.gallery.basebiz.R.string.base_cancel, null)
            setCancelable(false)
            setPositiveButton(com.oplus.gallery.basebiz.R.string.main_enable) { _: DialogInterface, _: Int ->
                jumpPhotoCleanAPPDetailSettings(activity)
            }
            build().show()
        }
    }

    /**
     * 进入【照片清理】应用详情
     */
    private fun jumpPhotoCleanAPPDetailSettings(activity: Activity) {
        Intent().apply {
            action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
            data = Uri.fromParts("package", if (isOnePlusExport) PACKAGE_NAME_PHONE_MANAGER_ONEPLUS else PACKAGE_NAME_PHONE_MANAGER, null)
            activity.startActivity(this)
        }
    }



    /**
     * 根据action进入【清理建议】页面
     */
    private fun jumpPhotoCleanAPPByAction(activity: Activity) {
        Intent().apply {
            action = ACTION_PHONE_CLEAN
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            activity.startActivity(this)
        }
    }

    /**
     * 是否支持【照片清理】
     * @return
     */
    fun isSupportPhotoClean(context: Context): Boolean {
        if (!OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_11_3)) {
            GLog.d(TAG, LogFlag.DL) { "isSupportPhotoClean, colorOs version is less than 11.3" }
            return false
        }
        if (!PackageInfoUtils.isPackageExist(
                context,
                if (isOnePlusExport) PACKAGE_NAME_PHONE_MANAGER_ONEPLUS else PACKAGE_NAME_PHONE_MANAGER
            )
        ) {
            GLog.d(TAG, LogFlag.DL) { "isSupportPhotoClean, package name is not exist" }
            return false
        }
        if (!isPhoneManagerSupportPhotoClean().getOrDefault(false)) {
            GLog.d(TAG, LogFlag.DL) { "isSupportPhotoClean, meta-data is not exist" }
            return false
        }
        return true
    }

    /**
     * 进入【清理相册】埋点
     */
    private fun trackAndSendEnterPhotoClean(title: String) {
        //do something
        AlbumsActionTrackHelper.trackAndSendAlbumsClick(
            currentPage = AlbumsActionTackConstant.Value.ALBUMS_ACTION_MAIN_ALBUM_SET_PAGE_VALUE,
            path = "",
            albumName = title,
            albumType = AlbumsActionTackConstant.Value.ALBUMS_ACTION_PHOTO_CLEAN_VALUE
        )
    }

    /**
     * 获取手管meta-data 信息,检查手管是否支持照片清理
     * @return true:支持，false:不支持
     */
    private fun isPhoneManagerSupportPhotoClean(): Result<Boolean> {
        val packageName = if (isOnePlusExport) PACKAGE_NAME_PHONE_MANAGER_ONEPLUS else PACKAGE_NAME_PHONE_MANAGER
        return runCatching {
            val applicationInfo = PackageInfoUtils.getMetaData(packageName)
            val metaValue = applicationInfo?.getString(
                if (isOnePlusExport) PHONE_MANAGER_PHOTO_CLEAN_META_ONEPLUS else PHONE_MANAGER_PHOTO_CLEAN_META
            )
            metaValue == PHOTO_CLEAN_META_DATA_VALUE
        }.onFailure {
            GLog.w(TAG, LogFlag.DL) { "package name not exist" }
        }
    }

    /**
     * 判断action是否存在
     */
    private fun isExistPhotoCleanAction(context: Context): Boolean {
        val intent = Intent(ACTION_PHONE_CLEAN)
        val resolveInfo = context.packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
        if (resolveInfo != null && resolveInfo.size > 0) {
            return true
        }
        GLog.d(TAG, LogFlag.DL) { "isExistPhotoCleanAction, the action does not exist" }
        return false
    }

    /**
     * 检查【照片清理】是否启用
     * @return true 为启用
     */
    private fun isPhotoCleanEnabled(context: Context): Result<Boolean> {
        return runCatching {
            val packageName = if (isOnePlusExport) PACKAGE_NAME_PHONE_MANAGER_ONEPLUS else PACKAGE_NAME_PHONE_MANAGER
            context.packageManager.getApplicationEnabledSetting(packageName) == PackageManager.COMPONENT_ENABLED_STATE_DEFAULT
                    || context.packageManager.getApplicationEnabledSetting(packageName) == PackageManager.COMPONENT_ENABLED_STATE_ENABLED
        }.onFailure {
            GLog.w(TAG, LogFlag.DL) { "isPhotoCleanEnabled, the named package does not exist" }
        }
    }

    /**
     * 【清理建议】入口小红点是否需要显示 (图集 Tab)
     */
    fun isPhotoCleanMenuEntryTipsRedDotShowing(): Boolean {
        val isShowRedDot = ConfigAbilityWrapper.getBoolean(
            ConfigID.Business.Classification.PhotoClean.PHOTO_CLEAN_IS_SUPPORT_SHOW_RED_DOT_LOCAL,
            defValue = true
        )
        GLog.d(TAG, LogFlag.DL) {
            "isPhotoCleanMenuEntryTipsRedDotShowing isShowRedDot:$isShowRedDot"
        }
        return isShowRedDot
    }
}