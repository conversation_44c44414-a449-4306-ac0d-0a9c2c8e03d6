/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FileProcessTaskBuilder.kt
 ** Description: 文件处理任务构造器，传入待处理的文件Path列表以及处理结束的监听。
 ** 并配置好需要构造的任务类型、是否要前置弹窗、进度条弹窗等信息。待任务执行结束后就能够收到回调。
 ** 当然，有需要也可以设置进度监听，实时监听任务的执行进度。
 ** Version: 1.0
 ** Date: 2022/5/17
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** <EMAIL>     2022/5/17   1.0              build this module
 **
 *************************************************************************************************/
package com.oplus.gallery.business_lib.fileprocessor.builder

import android.app.Activity
import androidx.annotation.UiThread
import com.oplus.gallery.business_lib.fileprocessor.ITaskCreator
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.CancelListener
import com.oplus.gallery.business_lib.fileprocessor.ProgressUpdateListener
import com.oplus.gallery.business_lib.fileprocessor.origdownload.OrigDownloadTaskCreator
import com.oplus.gallery.business_lib.fileprocessor.view.SmoothProgressDialog
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import java.lang.ref.WeakReference

class FileProcessTaskBuilder(
    activity: Activity,
    private var paths: List<Path>,
    private var finishListener: FinishListener?,
    private var scene: FileProcessScene
) {

    /**
     * 这里使用弱引用，避免在该类的匿名内部类对其造成引用
     */
    private val activityRef: WeakReference<Activity> = WeakReference(activity)

    /**
     * 标记当前需要构造的任务类型（多个task会按照任务添加顺序连接起来）
     */
    private var taskTypes: MutableList<Int> = ArrayList()

    /**
     * 是否需要进度条弹窗，以及进度更新通知
     */
    private var needShowProgressDialog: Boolean = true
    private var progressUpdateListener: ProgressUpdateListener? = null

    private var cancelListener: CancelListener? = null

    fun needProgressDialog(needProgressDialog: Boolean): FileProcessTaskBuilder = apply {
        this.needShowProgressDialog = needProgressDialog
    }

    fun addTaskType(type: Int): FileProcessTaskBuilder = apply {
        this.taskTypes.add(type)
    }

    fun setProgressUpdateListener(progressUpdateListener: ProgressUpdateListener?): FileProcessTaskBuilder = apply {
        this.progressUpdateListener = progressUpdateListener
    }

    fun setCancelListener(cancelListener: CancelListener?): FileProcessTaskBuilder = apply {
        this.cancelListener = cancelListener
    }

    @UiThread
    fun build(): BaseFileProcessTask? {
        if (taskTypes.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "build taskTypes is null or empty, return null" }
            return null
        }
        val activity = activityRef.get() ?: run {
            GLog.w(TAG, LogFlag.DL) { "activityRef get null , return null" }
            return null
        }
        val progressDialog = if (needShowProgressDialog) SmoothProgressDialog(activity) else null
        var firstTask: BaseFileProcessTask? = null
        var lastTask: BaseFileProcessTask? = null
        taskTypes.forEach {
            TASK_CREATORS[it]?.create(
                activity,
                paths,
                finishListener,
                scene,
                progressDialog,
                cancelListener
            )?.apply {
                progressUpdateListener?.let {
                    this.progressListeners.add(it)
                }

                lastTask?.let { lastInner ->
                    lastInner.nextTask = this
                    lastTask = this
                } ?: let {
                    lastTask = this
                    firstTask = this
                }
            } ?: return@forEach
        }
        return firstTask
    }


    companion object {

        private const val TAG = "FileProcessTaskBuilder"

        /**
         * 定义文件处理任务类型
         */
        const val TASK_TYPE_DOWNLOAD_ORIG = 1
        const val TASK_TYPE_FORMAT_TRANSFER = 2
        const val TASK_TYPE_REMOVE_PRIVACY = 3

        private val TASK_CREATORS = HashMap<Int, ITaskCreator>()
        init {
            TASK_CREATORS[TASK_TYPE_DOWNLOAD_ORIG] = OrigDownloadTaskCreator()
            // 在这里增加其他task的creator
        }
    }
}


