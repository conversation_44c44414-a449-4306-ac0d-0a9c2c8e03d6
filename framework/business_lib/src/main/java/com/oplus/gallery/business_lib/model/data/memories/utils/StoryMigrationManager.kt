/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: StoryMigrationManager.kt
 ** Description:
 ** Adapt OP Story.
 **
 ** Version: 1.0
 ** Date: 2021/08/30
 ** Author: <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2021/08/30      1.0		Add new file.
 *********************************************************************************/

package com.oplus.gallery.business_lib.model.data.memories.utils

import android.content.Context
import android.database.Cursor
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.permission.AppDataPermissionUtils
import com.oplus.gallery.foundation.database.util.ConstantUtils
import com.oplus.gallery.foundation.database.util.ConstantUtils.ASC
import com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.convert.IntListConverter
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.ext.getStringSafe
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import java.io.File
import java.io.InputStream
import com.oplus.gallery.basebiz.R as BasebizR

object StoryMigrationManager {
    private const val TAG = "StoryMigrationManager"
    private const val OP_STORY_PATH = "stories"
    private const val FLAG_APP_DATA_CE = 2
    private const val RETRY_THRESHOLD = 30

    // OP MediaStoreMedia id prefixes
    private const val OP_BURST_MEDIA_STORE_MEDIA_ID_PREFIX = "MediaStore:Burst/"
    private const val OP_MEDIA_STORE_MEDIA_ID_PREFIX = "MediaStore/"

    private const val INVALID_MEDIA_ID = -1

    private const val PREF_NAME = "pref_story_migrate"
    private const val KEY_ALL_STORIES_MIGRATED = "all_stories_migrated"
    private const val KEY_RETRY_TIMES = "retry_times"

    private val gson: Gson by lazy { GsonBuilder().create() }
    private val storyRootDirectory: File by lazy { File(ContextGetter.context.dataDir, OP_STORY_PATH) }

    init {
        if (ApiLevelUtil.isAtLeastAndroidS() && ContextGetter.context.getStringSafe(BasebizR.string.op_change_permission).toBoolean()) {
            AppDataPermissionUtils.modifyDataPermission(OP_STORY_PATH, FLAG_APP_DATA_CE)
        }
    }

    @JvmStatic
    fun checkMigrate(context: Context) {
        if (allStoriesMigrated(context)) {
            GLog.d(TAG, "checkMigrate, all stories has been migrated")
            return
        }
        if (isReachRetryLimit(context)) {
            GLog.d(TAG, "checkMigrate, album migration reach retry limit")
            return
        }
        recordRetryTimes(context)
        val allStoriesMigrated = migrateAllStories(context)
        if (allStoriesMigrated) {
            setAllStoriesMigrated(context)
            removeStoryDirectory()
        }
    }

    private fun allStoriesMigrated(context: Context) =
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            .getBoolean(KEY_ALL_STORIES_MIGRATED, false)

    private fun setAllStoriesMigrated(context: Context) {
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE).also {
            it.edit().putBoolean(KEY_ALL_STORIES_MIGRATED, true).apply()
            it.edit().putInt(KEY_RETRY_TIMES, RETRY_THRESHOLD).apply()
        }
    }

    private fun isReachRetryLimit(context: Context): Boolean {
        val count = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE).getInt(KEY_RETRY_TIMES, 0)
        return count >= RETRY_THRESHOLD
    }

    private fun recordRetryTimes(context: Context) {
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE).also {
            var count = it.getInt(KEY_RETRY_TIMES, 0)
            it.edit().putInt(KEY_RETRY_TIMES, ++count).apply()
        }
    }

    private fun migrateAllStories(context: Context): Boolean {
        val stories = loadStories(context)
        var successCount = 0
        for (story in stories) {
            if (migrateStory(context, story)) {
                successCount = successCount.inc()
            }
        }
        GLog.i(TAG, "migrateAllStories, story count: ${stories.size}, success count: $successCount")
        return successCount == stories.size
    }

    private fun removeStoryDirectory() {
        // Delete stories folder, if story JSON file not exist.
        if (storyRootDirectory.list()?.isEmpty() == true) {
            storyRootDirectory.delete()
        }
    }

    private fun loadStories(context: Context): List<Story> {
        // setup directories
        if (!storyRootDirectory.exists()) {
            GLog.e(TAG, "loadStories, stories folder \"$storyRootDirectory\" does not exist.")
            return emptyList()
        }

        // load stories JSON file.
        val stories = mutableListOf<Story>()
        storyRootDirectory.listFiles()?.forEach { file ->
            val inputStream: InputStream = file.inputStream()
            val storyString = inputStream.bufferedReader().use { it.readText() }
            try {
                stories.add(gson.fromJson(storyString, Story::class.java))
            } catch (e: Exception) {
                GLog.e(TAG, "loadStories, exception: $e")
            }
        } ?: run {
            GLog.d(TAG, "loadStories, stories folder contains no file")
        }
        return stories
    }

    /**
     * Migrate data through manualCreateMemories function to create Memory.
     * @param context
     * @param story
     * @return boolean
     */
    private fun migrateStory(context: Context, story: Story): Boolean {
        val paths = getPathsInStory(story)
        val validPathCount = getValidPathCount(paths)
        val pathCount = paths.size
        if (validPathCount != paths.size) {
            GLog.w(TAG, "migrateStory, some path are not valid, path count: $pathCount, valid path count: $validPathCount")
            if (isReachRetryLimit(context)) {
                GLog.w(TAG, "migrateStory, some path are not valid, final")
            } else {
                GLog.w(TAG, "migrateStory, some path are not valid, retry")
                return false
            }
        }
        val dataRange = MemoriesScannerHelper.getDateRangeOfPathList(paths)
        val successCount = MemoriesHelper.manualCreateMemories(context, paths, story.name, dataRange)
        deleteStory(storyRootDirectory, story.id)
        if (successCount != paths.size) {
            GLog.d(
                TAG, "Story data insert DB not complete. Success count = $successCount" +
                        " and Story data count = ${story.mediaStoreMediaIds.size}"
            )
        }
        return true
    }

    /**
     * Convert media store media id ("MediaStore/<media id>" or "MediaStore:Burst/<media path>") to
     * path ("/<media id>")
     * @param story
     * @return
     */
    private fun getPathsInStory(story: Story): ArrayList<Path> {
        val paths = mutableListOf<Path>()
        for (mediaStoreMediaId in story.mediaStoreMediaIds) {
            var galleryId = -1
            if (mediaStoreMediaId.startsWith(OP_BURST_MEDIA_STORE_MEDIA_ID_PREFIX)) { // handle burst image
                // remove burst header and used the first image of burst
                val data = mediaStoreMediaId.replace(OP_BURST_MEDIA_STORE_MEDIA_ID_PREFIX, "") + "_"
                galleryId = getGalleryIdFromDBByData(data)
                if (galleryId == INVALID_MEDIA_ID) {
                    GLog.e(TAG, "getPathsInStory, cannot find media id for burst media: $mediaStoreMediaId")
                    continue
                }
                GLog.v(TAG, "getPathsInStory, mediaStoreMediaId: $mediaStoreMediaId, mediaId: $galleryId")
            } else {
                val mediaId = mediaStoreMediaId.replace(OP_MEDIA_STORE_MEDIA_ID_PREFIX, TextUtil.EMPTY_STRING)
                galleryId = getGalleryIdFromDBByMediaId(mediaId)
            }
            paths.add(Path.fromString("/$galleryId"))
        }
        return ArrayList(paths)
    }

    private fun getValidPathCount(paths: List<Path>): Int {
        val mediaIds = paths.map { e -> e.suffix }
        try {
            QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(arrayOf("count(1)"))
                .setWhere("${GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID} IN (${mediaIds.joinToString(",")})")
                .setConvert(CursorConvert())
                .build().exec().use { cursor ->
                    if ((cursor != null) && cursor.moveToFirst()) {
                        return cursor.getInt(0)
                    }
                }
        } catch (e: Exception) {
            GLog.w(TAG, "getValidPathCount, error: ", e)
        }
        return 0
    }

    private fun getGalleryIdFromDBByData(data: String): Int {
        QueryReq.Builder<ArrayList<Int>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(arrayOf(GalleryStore.GalleryColumns.LocalColumns._ID))
            .setWhere(GalleryStore.GalleryColumns.LocalColumns.DATA + ConstantUtils.LIKE + "'" + data + "%'")
            .setConvert(IntListConverter(GalleryStore.GalleryColumns.LocalColumns._ID))
            .setOrderBy(GalleryStore.GalleryColumns.LocalColumns.DATA + ASC + " LIMIT 1")
            .build().exec().let { list ->
                if (list.size > 0) {
                    return list[0]
                }
            }
        return INVALID_MEDIA_ID
    }

    private fun getGalleryIdFromDBByMediaId(mediaId: String): Int {
        QueryReq.Builder<ArrayList<Int>>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(arrayOf(GalleryStore.GalleryColumns.LocalColumns._ID))
            .setWhere(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + EQUAL + mediaId)
            .setConvert(IntListConverter(GalleryStore.GalleryColumns.LocalColumns._ID))
            .build().exec().let { list ->
                if (list.size > 0) {
                    return list[0]
                }
            }
        return INVALID_MEDIA_ID
    }

    private fun deleteStory(rootDir: File, id: String) {
        File(rootDir, id).also {
            if (it.exists()) {
                it.delete().also { result ->
                    GLog.d(TAG, "deleteStory, story $id delete result = $result")
                }
            }
        }
    }

    data class Story(
        @SerializedName("Id") val id: String,
        @SerializedName("Name") val name: String,
        @SerializedName("MediaStoreMediaId") val mediaStoreMediaIds: List<String>
    )
}