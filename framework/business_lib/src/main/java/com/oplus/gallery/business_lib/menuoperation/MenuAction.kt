/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - Action.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/09/16
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/09/16	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation

import com.oplus.gallery.business_lib.menuoperation.builder.AddFavoritesActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.AddToShareAlbumActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.AlbumCreateMemoryActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.AlbumHideOnTimeLinePageActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.AlbumShowOnTimeLinePageActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.CloseCardCaseActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.CollageActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.CreateAlbumActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.CreateShareAlbumActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.DeleteRecycledActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.DetailsActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.EditPhotoActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.EditVideoActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ExportCShotActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ExportVideoActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.FreeFromGroupActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.GifSynthesisActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.GoToSettingActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.HDRVideoTransformActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.HEIFImageTransformActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ImportActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.LnSViewSaveResultActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.LnsShareActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ManualCreateMemoryActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.MergeGroupActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.MoveAlbumActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.MoveToActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.MoveToCardCaseActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.MoveToSafeBoxActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.OpenInSystemPlayerActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.PhotoToOcrScannerActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.PhotoToPdfActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ProjectionActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RecycleActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ReleaseCShotActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RemoveFavoritesActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RemoveFromLabelActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RemoveFromWidgetListBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RenameAlbumActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RenameFileActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RestoreRecycledActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.RotateActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.SaveToStickerActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.SetAlbumCoverActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.SetAsContactPhotoActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.SetOliveStatusActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.SetWallpaperActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ShareActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.SharedAlbumSelectFileActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.SharedMediaUploadFailedTipActionBuilder
import com.oplus.gallery.business_lib.menuoperation.builder.ShowGroupActionBuilder

sealed class MenuAction<Builder> {

    companion object {

        /**
         * 菜单类型
         */

        /** 菜单操作：设为私密 */
        const val MOVE_TO_SAFE_BOX = 0

        /** 菜单操作：拼图 */
        const val COLLAGE = 1

        /** 菜单操作：收藏 */
        const val ADD_FAVORITES = 3

        /** 菜单操作：取消收藏 */
        const val REMOVE_FAVORITES = 4

        /** 菜单操作：不是此人 */
        const val FREE_FROM_GROUP = 5

        /** 合并、合并并移出其他人物图集共用 */
        const val MERGE_GROUP = 6

        /** 移入、移出其他人物图集共用 */
        const val SHOW_GROUP = 7

        /** 菜单操作：旋转 */
        const val ROTATE = 8

        /** 菜单操作：手动生成回忆 */
        const val MANUAL_CREATE_MEMORY = 9

        /** 菜单操作：删除 */
        const val RECYCLE = 10

        /** 菜单操作：恢复删除 */
        const val RESTORE_RECYCLED = 11

        /** 菜单操作：彻底删除 */
        const val DELETE_RECYCLED = 12

        /** 菜单操作：跳转设置 */
        const val GO_TO_SETTING = 13

        /** 菜单操作：投屏 */
        const val PROJECTION = 14

        /** 菜单操作：设为联系人头像 */
        const val SET_AS_CONTACT_PHOTO = 15

        /** 菜单操作：设为壁纸 */
        const val SET_WALLPAPER = 16

        /** 菜单操作：一键生成回忆 */
        const val ALBUM_CREATE_MEMORY = 17

        /** 菜单操作：分享 */
        const val SHARE = 18

        /** 菜单操作：解除连拍 */
        const val RELEASE_CSHOT = 19

        /** 菜单操作：移入、移出其他图集 */
        const val MOVE_ALBUM = 20

        /** 菜单操作：详情 */
        const val DETAILS = 21

        /** 菜单操作：图片编辑 */
        const val EDIT_PHOTO = 22

        /** 菜单操作：视频编辑 */
        const val EDIT_VIDEO = 23

        /** 菜单操作：新建图集 */
        const val CREATE_ALBUM = 24

        /** 菜单操作：移动到 */
        const val MOVE_TO = 25

        /** 菜单操作：新建共享图集 */
        const val CREATE_SHARE_ALBUM = 26

        /** 菜单操作：添加到共享图集 */
        const val ADD_TO_SHARE_ALBUM = 27

        /** 菜单操作：导入
         * 两台手机用 OTG 线连接，一台手机能看到另一台手机的全部图片，在以手机名为图集名的图集里，
         * 这个图集里的图片长按选择或者点击进入大图，都只有一个导入的菜单，导入后会导入到新手机里
         * 的根目录的 Import 文件夹下，并且直接跳转 Import 图集页
         * */
        const val IMPORT = 28

        /** 菜单操作：转为JPEG格式 */
        const val TRANSFORM_HEIF_TO_JPEG = 29

        /** 菜单操作 ： HLG视频转为SDR **/
        const val TRANSFORM_HDR_TO_SDR = 30

        /** 菜单操作：不是此类 */
        const val REMOVE_FROM_LABEL = 31

        /** 弹窗-移动到，如随身卡包：设置页-关闭-弹窗移动到 */
        const val CLOSE_CARD_CASE = 32

        /** 菜单操作: 移入随身卡包 **/
        const val MOVE_TO_CARD_CASE = 33

        /** 菜单操作: 文件重命名 **/
        const val RENAME_FILE = 34

        /** 菜单操作: 图集重命名 **/
        const val RENAME_ALBUM = 35

        /** 菜单操作: 移出桌面卡片轮播列表（移出精选/自选列表） **/
        const val REMOVE_FROM_WIDGET_LIST = 36

        /** 菜单操作: 共享图集选择文件 **/
        const val SHARED_ALBUM_SELECT_FILE = 38

        /** 菜单操作: 文件上传到共享图集失败原因提示 **/
        const val SHARED_MEDIA_UPLOAD_FAILED_TIP = 39

        /** 菜单操作：导出连拍、定时连拍中的照片 **/
        const val EXPORT_CSHOT = 40

        /** 菜单操作: GIF合成 **/
        const val GIF_SYNTHESIS = 41

        /** 菜单操作：查看抠图结果 **/
        const val LNS_VIEW_SAVE_RESULT = 42

        /**
         * 菜单操作：抠图结果分享
         */
        const val LNS_RESULT_SHARE = 43

        /** 菜单操作：复制到图集 **/
        const val COPY_TO = 44

        /** 菜单操作：导出实况照片中的视频 **/
        const val EXPORT_VIDEO = 45

        /** 菜单操作：保存为贴纸 **/
        const val SAVE_TO_STICKER = 46

        /** 菜单操作：设置为封面 **/
        const val SET_AS_COVER = 48

        /** 菜单操作: 图片转PDF **/
        const val PIC_TO_PDF = 49

        /** 菜单操作：文件夹设为私密 */
        const val MOVE_ALBUM_TO_SAFE_BOX = 50

        /** 菜单操作：开启实况 */
        const val SET_OLIVE_ENABLE = 51

        /** 菜单操作：在系统播放器中打开 */
        const val OPEN_IN_SYSTEM_PLAYER = 52

        /** 菜单操作: 图片转扫一扫 **/
        const val PIC_TO_OCR_SCANNER = 53

        /** 菜单操作: 不显示在照片页 **/
        const val ALBUM_HIDE_ON_TIME_LINE_PAGE = 54

        /** 菜单操作: 显示在照片页 **/
        const val ALBUM_SHOW_ON_TIME_LINE_PAGE = 55

        /** 菜单操作: 导出实况 **/
        const val VIDEO_EXPORT_OLIVE = 56

        /** 通用的菜单入参 */
        const val KEY_TRACK_ID = "track_id"
        const val KEY_TRACK_CALL_TIME = "track_call_time"
        const val KEY_TRACK_CALLER_ENTRY = "track_caller_entry"
        const val KEY_IS_FROM_TIMELINE = "is_from_timeline"

        /** 事件变化通知器：比如：横竖屏事件变化*/
        const val KEY_EVENT_NOTIFIER_REF = "menu_event_notifier"

        /** 通用的 resultCode，其他业务特殊的在下方定义 */
        /** 返回值非法 */
        const val RESULT_INVALID = "result_invalid"
        const val RESULT_CANCEL = "result_cancel"
        const val RESULT_SUCCESS = "result_success"
        const val RESULT_FAILED = "result_failed"

        const val RESULT_TRACK_SOLOOP = "result_track_soloop"

        /** 入参无数据 */
        const val RESULT_ERROR_NO_DATA = "result_error_no_data"

        /** 入参无上下文 */
        const val RESULT_ERROR_NO_CONTEXT = "result_error_no_context"

        /** 相关功能开关未开启 */
        const val RESULT_FAILED_FEATURE_SWITCHER_CLOSED = "result_failed_feature_switcher_closed"

        /** 未知错误返回 */
        const val RESULT_FAILED_UNKNOWN = "result_failed_unknown"

        /** 返回携带的intent*/
        const val KEY_INTENT = "intent"

        /**
         * lifecycle的弱引用
         */
        const val KEY_LIFECYCLE_REF = "key_lifecycle_ref"
    }

    abstract val builder: Builder
}

/** 菜单操作：拼图 */
class CollageAction : MenuAction<CollageActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_FRAGMENT_REF = "fragment_ref"
        const val KEY_CURRENT_PAGE = "current_page"

        const val RESULT_ERROR_HAS_VIDEO = "result_error_has_video"
        const val RESULT_ERROR_COUNT_TOO_LESS = "result_error_count_too_less"
        const val RESULT_ERROR_COUNT_TOO_MUCH = "result_error_count_too_much"
        const val RESULT_ERROR_NOT_SUPPORT_RATIO = "result_error_not_support_ratio"
    }

    override val builder = CollageActionBuilder()
}

/** 菜单操作：设为私密 */
class MoveToSafeBoxAction : MenuAction<MoveToSafeBoxActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_FAILED_COUNT = "failed_count"
        const val KEY_FILTERED_COUNT = "filtered_count"

        const val RESULT_ERROR_ALL_FILE_LOADING = "result_error_all_file_loading"
        const val RESULT_ERROR_GET_SERVICE_NULL = "result_error_get_service_null"
    }

    override val builder = MoveToSafeBoxActionBuilder()
}

/** 菜单操作：转为JPEG **/
class HEIFImageTransformAction : MenuAction<HEIFImageTransformActionBuilder>() {

    companion object {
        const val KEY_ACTIVITY = "activity"
        const val KEY_IMG_ITEM = "img_item"
        const val KEY_SESSION = "session"
    }

    override val builder = HEIFImageTransformActionBuilder()
}

/** 菜单操作 ： hlg转为sdr **/
class HDRVideoTransformAction : MenuAction<HDRVideoTransformActionBuilder>() {
    companion object {
        const val KEY_ACTIVITY = "activity"
        const val KEY_VIDEO_ITEM = "video_item"
        const val KEY_SESSION = "session"
    }

    override val builder = HDRVideoTransformActionBuilder()
}

/** 菜单操作：添加收藏 */
class AddFavoritesAction : MenuAction<AddFavoritesActionBuilder>() {

    companion object {
        const val KEY_MEDIA_ITEM = "media_item"
    }

    override val builder = AddFavoritesActionBuilder()
}

/** 菜单操作：在系统播放器中打开  */
class OpenInSystemPlayerAction : MenuAction<OpenInSystemPlayerActionBuilder>() {
    companion object {
        const val KEY_ACTIVITY = "activity"
        const val KEY_VIDEO_URI = "video_uri"
        const val KEY_CURRENT_PLAY_TIME = "current_play_time"
    }

    override val builder = OpenInSystemPlayerActionBuilder()
}

/** 菜单操作：移除收藏 */
class RemoveFavoritesAction : MenuAction<RemoveFavoritesActionBuilder>() {

    companion object {
        const val KEY_FILE_PATH_LIST = "file_path_list"
    }

    override val builder = RemoveFavoritesActionBuilder()
}

/** 菜单操作：开启实况 */
class SetOLiveStatusAction : MenuAction<SetOliveStatusActionBuilder>() {

    companion object {
        const val KEY_MEDIA_ITEM = "media_item"
        const val KEY_OLIVE_ENABLE = "olive_enable"
        const val KEY_FRAGMENT_REF = "fragment_ref"
    }

    override val builder = SetOliveStatusActionBuilder()
}

/** 菜单操作：不是此人（把某张图片从某个人物中移除） */
class FreeFromGroupAction : MenuAction<FreeFromGroupActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_GROUP_ID = "group_id"
        const val KEY_ALBUM_SET_TYPE = "album_set_type"
    }

    override val builder = FreeFromGroupActionBuilder()
}

/** 菜单操作：合并，合并并移入其他人物图集共用 */
class MergeGroupAction : MenuAction<MergeGroupActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_SHOW_ENABLE = "show_enable"
    }

    override val builder = MergeGroupActionBuilder()
}

/** 菜单操作：移入、移出其他人物图集共用 */
class ShowGroupAction : MenuAction<ShowGroupActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_SHOW_ENABLE = "show_enable"
    }

    override val builder = ShowGroupActionBuilder()
}

/** 菜单操作：标签移除（把某张图片从某个标签中移除） */
class RemoveFromLabelAction : MenuAction<RemoveFromLabelActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_LABEL_ID = "label_id"
    }

    override val builder = RemoveFromLabelActionBuilder()
}

/** 手势操作：旋转 */
class RotateAction : MenuAction<RotateActionBuilder>() {

    companion object {
        const val KEY_DEGREES = "degress"
        const val KEY_MEDIA_ITEM = "media_item"
    }

    override val builder = RotateActionBuilder()
}

/** 菜单操作：手动生成回忆 */
class ManualCreateMemoryAction : MenuAction<ManualCreateMemoryActionBuilder>() {

    companion object {
        const val KEY_FRAGMENT = "fragment"
        const val KEY_SET_NAME = "set_name"
    }

    override val builder = ManualCreateMemoryActionBuilder()
}

/** 菜单操作：移入回收站 */
class RecycleAction : MenuAction<RecycleActionBuilder>() {

    companion object {
        const val KEY_FILE_PATH_LIST = "file_path_list"
        const val KEY_RECYCLE_CSHOT_COLLECTION = "recycle_cshot_collection"
        const val KEY_RECYCLE_ALBUMS = "recycle_albums"

        const val RESULT_FILE_PATH_LIST_PARAMETER_ERROR = "result_file_path_list_parameter_error"
        const val RESULT_RECYCLE_CSHOT_COLLECTION_PARAMETER_ERROR = "result_recycle_cshot_collection_parameter_error"
        const val RESULT_RECYCLE_ALBUMS_PARAMETER_ERROR = "result_recycle_albums_parameter_error"
    }

    override val builder = RecycleActionBuilder()
}

/** 菜单操作：从回收站恢复 */
class RestoreRecycledAction : MenuAction<RestoreRecycledActionBuilder>() {

    companion object {
        const val KEY_FILE_PATH_LIST = "file_path_list"
        const val KEY_DELETE_CSHOT_COLLECTION = "restore_cshot_collection"
        const val KEY_ALL_RESTORED = "restore_all_recycled"
        const val KEY_ACTIVITY = "activity"

        const val RESULT_FILE_PATH_LIST_PARAMETER_ERROR = "RESULT_FILE_PATH_LIST_PARAMETER_ERROR"
        const val RESULT_DELETE_CSHOT_COLLECTION_PARAMETER_ERROR = "RESULT_DELETE_CSHOT_COLLECTION_PARAMETER_ERROR"
        const val RESULT_ALL_RESTORED_PARAMETER_ERROR = "RESULT_ALL_RESTORED_PARAMETER_ERROR"
    }

    override val builder = RestoreRecycledActionBuilder()
}

/** 菜单操作：彻底删除 */
class DeleteRecycledAction : MenuAction<DeleteRecycledActionBuilder>() {

    companion object {
        const val KEY_FILE_PATH_LIST = "file_path_list"
        const val KEY_ALL_RECYCLED = "delete_all_recycled"
        const val KEY_DELETE_CSHOT_COLLECTION = "delete_cshot_collection"
        const val RESULT_FILE_PATH_LIST_PARAMETER_ERROR = "RESULT_FILE_PATH_LIST_PARAMETER_ERROR"
        const val RESULT_ALL_RECYCLED_PARAMETER_ERROR = "RESULT_ALL_RECYCLED_PARAMETER_ERROR"
        const val RESULT_DELETE_CSHOT_COLLECTION_PARAMETER_ERROR = "RESULT_DELETE_CSHOT_COLLECTION_PARAMETER_ERROR"
    }

    override val builder = DeleteRecycledActionBuilder()
}

/** 菜单操作：跳转设置 */
class GoToSettingAction : MenuAction<GoToSettingActionBuilder>() {

    companion object {
        const val KEY_OPEN_SETTING_FROM = "key_open_setting_from"
        const val KEY_ACTIVITY = "activity"

        // 相册内部打开设置标识(区分从哪里打开设置页面 内部/外部)
    }

    override val builder = GoToSettingActionBuilder()
}

/** 菜单操作：投屏播放 */
class ProjectionAction : MenuAction<ProjectionActionBuilder>() {

    companion object {
        const val KEY_ACTIVITY_REF = "activity_ref"
    }

    override val builder = ProjectionActionBuilder()
}

/** 菜单操作：设置为联系人头像 */
class SetAsContactPhotoAction : MenuAction<SetAsContactPhotoActionBuilder>() {

    companion object {
        const val KEY_IMAGE_PATH = "image_path"
        const val KEY_ACTIVITY_REF = "activity"
    }

    override val builder = SetAsContactPhotoActionBuilder()
}

/** 菜单操作：设为壁纸 */
class SetWallpaperAction : MenuAction<SetWallpaperActionBuilder>() {

    companion object {
        const val KEY_IMG_PATH = "img_path"
        const val KEY_ACTIVITY = "activity"
    }

    override val builder = SetWallpaperActionBuilder()
}

/** 菜单操作：人物图集、自建图集一键生成回忆 */
class AlbumCreateMemoryAction : MenuAction<AlbumCreateMemoryActionBuilder>() {

    companion object {
        const val KEY_ALBUM_MODEL = "album_model"
        const val KEY_ACTIVITY = "activity"
        const val SUPPORT_CREATE_MEMORY = "isSupportCreateMemory"

        const val RESULT_ERROR_FAIL = "RESULT_ERROR_FAIL"
    }

    override val builder = AlbumCreateMemoryActionBuilder()
}

/** 菜单操作：跳转分享 */
class ShareAction : MenuAction<ShareActionBuilder>() {

    companion object {
        const val KEY_SHARE_ACTIVITY = "activity"
        const val KEY_BUNDLE = "bundle"
        const val KEY_VIEWDATA_ID = "view_data_id"
        const val KEY_MODEL_TYPE = "model_type"
        const val KEY_IS_FROM_EXTERNAL = "is_from_external"
        const val KEY_IS_SHOW_SHARE_DELETE = "is_show_share_delete"
        const val KEY_SHARE_INTENT_KEY = "intent"
        const val KEY_SHARE_SELECTION_UPDATED_CALLBACK = "share_selection_updated_callback"
        const val KEY_ORDER_REVERT = "order_revert"
        const val KEY_FROM_PHOTO_PAGE = "from_photo_page"
    }

    override val builder = ShareActionBuilder()
}

/** 菜单操作：解除连拍 */
class ReleaseCShotAction : MenuAction<ReleaseCShotActionBuilder>() {

    companion object {
        const val KEY_BEST_PIC_MEDIA_ID = "id"
        const val KEY_CSHOT_ID = "cshot_id"
        const val KEY_PATHS = "paths"
        const val KEY_IN_LOCKED_MODE = "in_locked_mode"
        const val KEY_ORIGIN_SET_PATH = "origin_set_path"
    }

    override val builder = ReleaseCShotActionBuilder()
}

/** 菜单操作：导出连拍、定时连拍中的图片 */
class ExportCShotAction : MenuAction<ExportCShotActionBuilder>() {
    companion object {
        const val KEY_BEST_PIC_MEDIA_ID = "id"
        const val KEY_CSHOT_ID = "cshot_id"
        const val KEY_PATHS = "paths"
        const val KEY_IN_LOCKED_MODE = "in_locked_mode"
        const val KEY_ORIGIN_SET_PATH = "origin_set_path"
    }

    override val builder = ExportCShotActionBuilder()
}

/** 菜单操作：移动图集到其他图集，从其他图集移出，移入侧边导航栏 */
class MoveAlbumAction : MenuAction<MoveAlbumActionBuilder>() {

    companion object {
        const val KEY_MOVE_ALBUM_INDEX_LIST = "album_index_list"
        const val KEY_MOVE_ALBUM_MOVABLE = "album_movable"
        const val KEY_MOVE_ALBUM_OPERATION_TYPE = "operation_type"
    }

    enum class OperationType {
        /**
         * 移入侧边导航栏
         */
        MOVE_TO_SIDEPANE,

        /**
         * 移出常用图集
         */
        MOVE_OUT_SHORTCUT_ALBUMSET,

        /**
         * 移入常用图集
         */
        MOVE_IN_SHORTCUT_ALBUM_SET
    }

    override val builder = MoveAlbumActionBuilder()
}

/** 菜单操作：显示详细信息 */
class DetailsAction : MenuAction<DetailsActionBuilder>() {

    companion object {
        const val KEY_DETAILS_ACTIVITY = "activity"
        const val KEY_DETAILS_MEDIA_ITEM = "media_item"
        const val KEY_DETAILS_OPERATION_REGISTER = "details_operation_register"
    }

    interface IDetailsOperation {
        fun close()
    }

    override val builder = DetailsActionBuilder()
}

/** 菜单操作：编辑图片 */
class EditPhotoAction : MenuAction<EditPhotoActionBuilder>() {
    companion object {
        const val KEY_FRAGMENT = "fragment"
        const val KEY_IS_CSHOT = "is_cshot"
        const val KEY_FROM_PHOTOPAGE = "from_photopage"
        const val KEY_MEDIA_SET_PATH = "media_set_path"
        const val KEY_INVOKER = "invoker"
        const val KEY_INVOKER_TOKEN = "invoker_token"
        const val KEY_RESULT_SUCCESS_DATA = "key_data"

        const val KEY_EDIT_FROM_PHOTO = "editor_from_photo"
        const val KEY_SHOW_BACK_TITLE = "show_back_title"
        const val KEY_SUPPORT_LOSS_LESS_CACHE = "support_loss_less_cache"
        const val KEY_MEDIA_ITEM = "mediaItem"
        const val KEY_THUMBNAIL = "thumbnail"
        const val KEY_COLOR_SPACE = "color_space"
        const val KEY_EDIT_SKILL = "edit_skill"
        const val KEY_MEDIA_MODEL_TYPE = "media_model_type"
        const val KEY_EXTRAS = "extras"
        const val KEY_HDR_IMAGE_CONTENT = "hdr_image_content"
        const val KEY_START_EDITOR_TIME = "start_editor_time"
    }

    override val builder = EditPhotoActionBuilder()
}

/** 菜单操作：视频编辑 */
class EditVideoAction : MenuAction<EditVideoActionBuilder>() {

    companion object {
        const val KEY_FRAGMENT = "fragment"
        const val KEY_VIDEO_URI = "video_uri"
        const val KEY_INVOKER = "invoker"
        const val KEY_INVOKER_TOKEN = "invoker_token"
        const val VALUE_INVOKER_SEARCH_PAGE = "search"
        const val VALUE_INVOKER_GALLERY_PAGE = "gallery"
        const val VALUE_INVOKER_GALLERY_PAGE_FROM_CAMERA = "gallery_from_camera"

        /**
         * 从壁纸应用去剪辑和设置壁纸视频
         */
        const val VALUE_INVOKER_WALLPAPER_PAGE = "wallpaper"

        /**
         * 从相册-大图-更多-设为壁纸，去剪辑和设置壁纸视频
         */
        const val VALUE_INVOKER_GALLEY_WALLPAPER_PAGE = "gallery_wallpaper"
        /**
         * 从相册-大图-更多-导出实况
         */
        const val VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE = "gallery_export_olive"
        const val KEY_MEDIA_MODEL_TYPE = "media_model_type"
        const val KEY_MEDIA_ITEM_PATH = "media_item_path"
        const val KEY_THUMBNAIL = "thumbnail"
        const val KEY_COLOR_SPACE = "color_space"
    }

    override val builder = EditVideoActionBuilder()
}

/** 菜单操作：新建图集 */
class CreateAlbumAction : MenuAction<CreateAlbumActionBuilder>() {

    companion object {
        const val KEY_FRAGMENT_REF = "fragment_ref"
        const val KEY_TARGET_ALBUM_NAME = "target_album_name"
        const val KEY_MIME_TYPE = "mime_type"
        const val KEY_CURRENT_BUCKETID = "current_bucketid"
    }

    override val builder = CreateAlbumActionBuilder()
}

/** 菜单操作：移动到 */
class MoveToAction : MenuAction<MoveToActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_FRAGMENT_REF = "fragment_ref"
        const val KEY_ALBUM_SET_PATH = "album_set_path"
        const val KEY_ITEMS_MEDIA_TYPE = "items_media_type"
        const val KEY_HIDE_INTERNAL_TOOLBAR = "hide_internal_toolbar"
        const val KEY_IMAGE_COUNT = "image_count"
        const val KEY_VIDEO_COUNT = "video_count"
        const val KEY_CURRENT_PAGE = "current_page"
        const val KEY_PATH = "path"
        const val KEY_CURRENT_ALBUM_NAME = "current_album_name"
        const val KEY_MENU_ACTION_ID = "menu_action_id"
        const val KEY_IS_START_ACTIVITY = "is_start_activity"
    }

    override val builder = MoveToActionBuilder()
}

/** 菜单操作：移动到 */
class ExportVideoAction : MenuAction<ExportVideoActionBuilder>() {

    companion object {
        const val KEY_FRAGMENT_REF = "fragment_ref"
        const val KEY_ALBUM_SET_PATH = "album_set_path"
        const val KEY_PATH = "path"
        const val KEY_PATH_LIST = "path_list"
        const val KEY_IS_FROM_PHOTO_PAGE = "is_from_photo_page"
    }

    override val builder = ExportVideoActionBuilder()
}

/** 菜单操作：移入随身卡包 */
class MoveToCardCaseAction : MenuAction<MoveToCardCaseActionBuilder>() {
    companion object {
        const val KEY_FRAGMENT_REF = "fragment_ref"
        const val KEY_MOVABLE_COUNT_SET = "movable_count_set"
        const val KEY_MOVABLE_PATHS = "movable_paths"
    }

    override val builder = MoveToCardCaseActionBuilder()
}

/** 移动文件前，需要弹窗确认、 */
class CloseCardCaseAction : MenuAction<CloseCardCaseActionBuilder>() {

    companion object {
        const val KEY_FRAGMENT_REF = "fragment_ref"
        const val KEY_DIALOG_TITLE = "dialog_title"
    }

    override val builder = CloseCardCaseActionBuilder()
}

/** 菜单操作：新建共享图集 */
class CreateShareAlbumAction : MenuAction<CreateShareAlbumActionBuilder>() {

    companion object {
        const val KEY_ACTIVITY_REF = "activity_ref"
        const val RESULT_SHARE_ALBUM_NOT_OPEN = "RESULT_SHARE_ALBUM_NOT_OPEN"
        const val RESULT_NETWORK_NOT_CONNECTED = "RESULT_NETWORK_NOT_CONNECTED"
    }

    override val builder = CreateShareAlbumActionBuilder()
}

/** 菜单操作：添加到共享图集 */
class AddToShareAlbumAction : MenuAction<AddToShareAlbumActionBuilder>() {

    companion object {
        const val KEY_ACTIVITY_REF = "activity_ref"
        const val KEY_GROUP_ID = "group_id"
    }

    override val builder = AddToShareAlbumActionBuilder()
}

/** 菜单操作：添加到共享图集 */
class ImportAction : MenuAction<ImportActionBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
    }

    override val builder = ImportActionBuilder()
}

/** 菜单操作：图集重命名 */
class RenameAlbumAction : MenuAction<RenameAlbumActionBuilder>() {
    companion object {
        const val KEY_CONTEXT_REF = "context_ref"
        const val KEY_ALBUM_NAME = "album_name"
        const val KEY_BUCKET_ID = "bucket_id"
        const val KEY_FOLDER_PATH = "folder_path"
        const val KEY_ANCHOR_VIEW = "bottom_anchor_view"
    }

    override val builder = RenameAlbumActionBuilder()
}

/** 菜单操作：重命名 */
class RenameFileAction : MenuAction<RenameFileActionBuilder>() {

    companion object {
        const val KEY_CONTEXT = "context"
        const val KEY_CURRENT_PATH = "current_path"
        const val KEY_IS_FLOATING_WINDOW_MODE = "is_floating_window_mode"
    }

    override val builder = RenameFileActionBuilder()
}

/** 菜单操作：转PDF */
class PhotoToPdfAction : MenuAction<PhotoToPdfActionBuilder>() {

    companion object {
        const val KEY_CONTEXT = "context"
        const val KEY_CURRENT_PATH_LIST = "current_path_list"
        const val KEY_PATH_LIST = "path_list"
    }

    override val builder = PhotoToPdfActionBuilder()
}


/** 菜单操作：转扫一扫 */
class PhotoToOcrScannerAction : MenuAction<PhotoToOcrScannerActionBuilder>() {

    companion object {
        const val KEY_ACTIVITY = "activity"
        const val KEY_OCR_SCANNER_TYPE_PDF = "type_pdf"
        const val KEY_PATH_LIST = "path_list"
        const val KEY_OCR_SCANNER_TYPE_SUPER_TEXT = "type_super_text"
    }

    override val builder = PhotoToOcrScannerActionBuilder()
}

/** 菜单操作：移出桌面卡片轮播列表（移出精选/自选列表） */
class RemoveFromWidgetListAction : MenuAction<RemoveFromWidgetListBuilder>() {

    companion object {
        const val KEY_PATH_LIST = "path_list"
        const val KEY_DISPLAY_LIST_ID = "display_list_id"
    }

    override val builder = RemoveFromWidgetListBuilder()
}


/** 菜单操作：设置为封面  */
class SetAsCoverAction : MenuAction<SetAlbumCoverActionBuilder>() {
    companion object {
        const val KEY_COVER_PATH = "cover_path"
        const val KEY_ALBUM_COVER_CHANGEABLE = "album_cover_changeable"
        const val KEY_FRAGMENT_REF = "fragment_ref"
    }

    override val builder: SetAlbumCoverActionBuilder
        get() = SetAlbumCoverActionBuilder()
}

/** 菜单操作：共享图集选择文件 */
class SharedAlbumSelectFileAction : MenuAction<SharedAlbumSelectFileActionBuilder>() {

    companion object {
        const val KEY_ACTIVITY_REF = "activity_ref"

        /** 最多选择数量限制提示语 */
        const val KEY_COUNT_LIMIT_HINT_MAXIMUM = "count_limit_hint_maximum"

        /** fragment 选择完成之后返回的选择列表 */
        const val KEY_RESULT_PATH_LIST = "result_path_list"
    }

    override val builder = SharedAlbumSelectFileActionBuilder()
}

/** 菜单操作：共享图集文件上传失败提示 */
class SharedMediaUploadFailedTipAction : MenuAction<SharedMediaUploadFailedTipActionBuilder>() {

    companion object {
        const val KEY_UPLOAD_FAILED_ACTIVITY = "activity"
        const val KEY_UPLOAD_FAILED_MEDIA_ITEM = "media_item"
    }

    override val builder = SharedMediaUploadFailedTipActionBuilder()
}

class GifSynthesisAction : MenuAction<GifSynthesisActionBuilder>() {

    companion object {
        const val KEY_ACTION_GIF_IMAGE_URI = "action_gif_image_uri"
        const val KEY_ACTION_GIF_IMAGE_PATH = "action_gif_image_path"
        const val KEY_ACTION_GIF_EDIT_FROM_PHOTO = "action_gif_editor_from_photo"
        const val KEY_ACTION_GIF_FORM_CSHOT = "action_gif_form_cshot"
        const val KEY_ACTION_GIF_FORM_OLIVE = "action_gif_from_olive"
        const val KEY_ACTION_GIF_PATH_LIST = "action_gif_path_list"
        const val KEY_ACTION_GIF_FRAGMENT_REF = "action_gif_fragment_ref"
        const val KEY_ACTION_GIF_CURRENT_PAGE = "action_gif_current_page"
        const val KEY_ACTION_GIF_THUMBNAIL = "action_gif_thumbnail"
        const val KEY_ACTION_GIF_CONTENT_DISPLAY_RECT = "action_gif_content_display_rect"
        const val KEY_ACTION_GIF_CONSTRAINT_DISPLAY_RECT = "action_gif_constraint_display_rect"
        const val KEY_ACTION_GIF_INVALID_COUNT = "action_gif_invalid_image_count"
        const val KEY_ACTION_GIF_REQUEST_CODE = "action_gif_request_code"
        const val KEY_ACTION_GIF_INVOKER_TOKEN = "action_gif_invoker_token"
        const val KEY_ACTION_GIF_BEST_PIC_MEDIA_ID = "action_gif_best_pic_media_id"
        const val KEY_ACTION_GIF_CSHOT_ID = "action_gif_best_cshot_Id"
        const val KEY_ACTION_GIF_IS_LOCK_MODE = "action_gif_is_lock_mode"
        const val KEY_ACTION_GIF_ORIGIN_PATH = "action_gif_origin_set_path"
        const val KEY_ACTION_GIF_JUMP_TYPE = "action_gif_jump_type"
    }

    override val builder = GifSynthesisActionBuilder()
}

/** 菜单操作：抠图保存完成后，拉起新大图查看保存结果 */
class LnSViewSaveResultAction : MenuAction<LnSViewSaveResultActionBuilder>() {

    companion object {
        /** 菜单操作时所在的fragment */
        const val KEY_ACTION_LNS_FRAGMENT_REF = "action_lns_fragment_ref"

        /** 菜单操作请求码 */
        const val KEY_ACTION_LNS_REQUEST_CODE = "action_lns_request_code"

        /** 用于配置拉起新大图时的包名参数 */
        const val KEY_ACTION_LNS_PACKAGE_NAME = "action_lns_package_name"

        /** 抠图保存结果的uri */
        const val KEY_ACTION_LNS_SAVE_RESULT_URI = "action_lns_save_result_uri"
    }

    override val builder: LnSViewSaveResultActionBuilder = LnSViewSaveResultActionBuilder()
}

/**
 * 抠图结果分享的菜单操作
 */
class LnsResultShareAction : MenuAction<LnsShareActionBuilder>() {

    override val builder = LnsShareActionBuilder()

    companion object {
        /** 抠图保存结果的uri */
        const val KEY_ACTION_LNS_SHARE_FILE_URI = "action_lns_share_file_uri"

        /** 菜单操作时所在的fragment */
        const val KEY_ACTION_LNS_SHARE_FRAGMENT_REF = "action_lns_share_fragment_ref"

        const val KEY_ACTION_LNS_SHARE_MINE_TYPE = "action_lns_share_mine_type"
    }
}

class SaveToStickerAction : MenuAction<SaveToStickerActionBuilder>() {

    override val builder = SaveToStickerActionBuilder()

    companion object {
        /** 保存为贴纸的原图filePath */
        const val KEY_ACTION_SAVE_TO_STICKER_FILE_PATH = "action_save_to_sticker_file_path"

        /** 保存为贴纸的原图 */
        const val KEY_ACTION_SAVE_TO_STICKER_BITMAP = "action_save_to_sticker_bitmap"

        /** 贴纸在大图的旋转角度 */
        const val KEY_ACTION_SAVE_TO_STICKER_BITMAP_ROTATE = "action_save_to_sticker_bitmap_rotate"

        /** 拖拽图片存贴纸时间 */
        const val KEY_ACTION_DRAG_SAVE_TO_STICKER_REQUEST_TIME = "action_save_drag_to_sticker_request_time"
    }
}

/** 菜单操作：图集不显示在照片页 */
class HideAlbumOnTimeLinePageAction : MenuAction<AlbumHideOnTimeLinePageActionBuilder>() {

    companion object {
        /** 菜单操作时的图集数据 */
        const val KEY_ALBUM_ID = "album_id"
        const val KEY_ALBUM_IDS = "album_ids"
    }

    override val builder = AlbumHideOnTimeLinePageActionBuilder()
}

/** 菜单操作：图集显示在照片页 */
class ShowAlbumOnTimeLinePageAction : MenuAction<AlbumShowOnTimeLinePageActionBuilder>() {

    companion object {
        /** 菜单操作时的图集数据 */
        const val KEY_ALBUM_ID = "album_id"
        const val KEY_ALBUM_IDS = "album_ids"
    }

    override val builder = AlbumShowOnTimeLinePageActionBuilder()
}

object MenuActionGetter {

    @JvmStatic
    val collage
        get() = CollageAction()

    @JvmStatic
    val safeBox
        get() = MoveToSafeBoxAction()

    @JvmStatic
    val addFavorites
        get() = AddFavoritesAction()

    @JvmStatic
    val removeFavorites
        get() = RemoveFavoritesAction()

    @JvmStatic
    val freeFromGroup
        get() = FreeFromGroupAction()

    @JvmStatic
    val mergeGroup
        get() = MergeGroupAction()

    @JvmStatic
    val showGroup
        get() = ShowGroupAction()

    @JvmStatic
    val rotate
        get() = RotateAction()

    @JvmStatic
    val recycle
        get() = RecycleAction()

    @JvmStatic
    val restoreRecycled
        get() = RestoreRecycledAction()

    @JvmStatic
    val deleteRecycled
        get() = DeleteRecycledAction()

    @JvmStatic
    val goToSetting
        get() = GoToSettingAction()

    val manualCreateMemory
        get() = ManualCreateMemoryAction()

    @JvmStatic
    val projection
        get() = ProjectionAction()

    @JvmStatic
    val setAsContactPhoto
        get() = SetAsContactPhotoAction()

    @JvmStatic
    val setWallpaperAction
        get() = SetWallpaperAction()

    @JvmStatic
    val albumCreateMemory
        get() = AlbumCreateMemoryAction()

    @JvmStatic
    val share
        get() = ShareAction()

    @JvmStatic
    val releaseCShot
        get() = ReleaseCShotAction()

    @JvmStatic
    val exportCShot
        get() = ExportCShotAction()

    @JvmStatic
    val moveAlbum
        get() = MoveAlbumAction()

    @JvmStatic
    val details
        get() = DetailsAction()

    @JvmStatic
    val editPhoto
        get() = EditPhotoAction()

    @JvmStatic
    val editVideo
        get() = EditVideoAction()

    @JvmStatic
    val createAlbum
        get() = CreateAlbumAction()

    @JvmStatic
    val moveTo
        get() = MoveToAction()

    @JvmStatic
    val copyTo
        get() = MoveToAction()

    @JvmStatic
    val exportVideo
        get() = ExportVideoAction()

    @JvmStatic
    val moveToCardCase
        get() = MoveToCardCaseAction()

    @JvmStatic
    val createShareAlbum
        get() = CreateShareAlbumAction()

    @JvmStatic
    val addToShareAlbum
        get() = AddToShareAlbumAction()

    @JvmStatic
    val import
        get() = ImportAction()

    @JvmStatic
    val transformFormat
        get() = HEIFImageTransformAction()

    @JvmStatic
    val transformToSdr
        get() = HDRVideoTransformAction()

    @JvmStatic
    val removeFromLabel
        get() = RemoveFromLabelAction()

    @JvmStatic
    val closeCardCase
        get() = CloseCardCaseAction()

    @JvmStatic
    val renameAlbum
        get() = RenameAlbumAction()

    @JvmStatic
    val renameFile
        get() = RenameFileAction()

    @JvmStatic
    val photoToPdf
        get() = PhotoToPdfAction()

    @JvmStatic
    val photoToOcrScanner
        get() = PhotoToOcrScannerAction()

    @JvmStatic
    val removeFromWidgetList
        get() = RemoveFromWidgetListAction()

    @JvmStatic
    val setAsCover
        get() = SetAsCoverAction()

    @JvmStatic
    val sharedAlbumSelectFile
        get() = SharedAlbumSelectFileAction()

    @JvmStatic
    val sharedMediaUploadFailedTip
        get() = SharedMediaUploadFailedTipAction()

    @JvmStatic
    val gifSynthesis
        get() = GifSynthesisAction()

    @JvmStatic
    val lnsViewSaveResult
        get() = LnSViewSaveResultAction()

    @JvmStatic
    val lnsShare
        get() = LnsResultShareAction()

    @JvmStatic
    val saveToSticker
        get() = SaveToStickerAction()

    @JvmStatic
    val setOliveStatus
        get() = SetOLiveStatusAction()

    @JvmStatic
    val openInSystemPlayer
        get() = OpenInSystemPlayerAction()

    @JvmStatic
    val hideAlbumOnTimeLinePage
        get() = HideAlbumOnTimeLinePageAction()

    @JvmStatic
    val showAlbumOnTimeLinePage
        get() = ShowAlbumOnTimeLinePageAction()
}
