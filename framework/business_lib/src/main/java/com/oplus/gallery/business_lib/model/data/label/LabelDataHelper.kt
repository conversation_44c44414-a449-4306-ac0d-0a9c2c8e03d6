/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelDataHelper.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/09/10
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/09/10	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.model.data.label

import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.scheduler.BatchProcess

object LabelDataHelper {

    private const val TAG = "LabelDataHelper"

    fun deleteLabelImageByMediaIdList(filePaths: List<String>) {
        if (filePaths.isNullOrEmpty()) {
            GLog.w(TAG, "deleteLabelImageByMediaIdList, filePaths is empty!")
            return
        }
        try {
            BatchProcess.doBatch(filePaths, BatchProcess.PAGE_SIZE_999) { batchFilePaths ->
                val deleteCount = DeleteReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
                        .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                        .setWhere(DatabaseUtils.getWhereQueryIn(GalleryStore.ScanLabel.DATA, batchFilePaths.size))
                        .setWhereArgs(batchFilePaths.toTypedArray())
                        .build()
                        .exec()
                GLog.d(TAG, "deleteLabelImageByMediaIdList, delete count = $deleteCount")
                emptyList()
            }
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_LABEL, IDao.DaoType.GALLERY)
        } catch (e: Exception) {
            GLog.e(TAG, "deleteLabelImageByMediaIdList, e = ", e)
        }
    }
}