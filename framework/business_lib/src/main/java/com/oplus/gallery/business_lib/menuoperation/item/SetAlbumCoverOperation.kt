/********************************************************************************
 ** Copyright (C), 2024-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SetAlbumCoverOperation.kt
 ** Description: 菜单操作：设为封面
 **
 **
 ** Version: 1.0
 ** Date: 2024/04/03
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/04/03     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.SetAsCoverAction
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_SET_AS_COVER
import com.oplus.gallery.framework.abilities.data.sortablemodel.sort.AlbumCoverChangeable
import java.lang.ref.WeakReference

class SetAlbumCoverOperation(
    isRunOnMain: Boolean,
    paramMap: Map<String, Any?>,
    onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)?,
) : MenuOperation<Unit>(isRunOnMain, paramMap, onCompleted) {
    private var coverPath: String? = null
    private var albumCoverChangeable: AlbumCoverChangeable? = null
    private var currentFragmentRef: WeakReference<BaseFragment>? = null

    override fun onCheckAndPrepare(): Boolean {
        paramMap.let {
            coverPath = it[SetAsCoverAction.KEY_COVER_PATH] as? String
            albumCoverChangeable = it[SetAsCoverAction.KEY_ALBUM_COVER_CHANGEABLE] as? AlbumCoverChangeable
            currentFragmentRef = it[SetAsCoverAction.KEY_FRAGMENT_REF] as? WeakReference<BaseFragment>
        }
        return true
    }

    override fun onRun() {
        coverPath?.let { albumCoverChangeable?.setAlbumCover(it) }
        setResult(MenuAction.RESULT_SUCCESS)
    }

    override fun getMenuTypeForTrack(): String = VALUE_SET_AS_COVER

    override fun getCustomParamForTrack(): Map<String, String?>? = null

    override fun getImageAndVideoCountForTrack(): Pair<String, String> = defaultTrackCountPair

    override fun cancel() {
        // do nothing
    }

    companion object {
        private const val TAG = "SetAlbumCoverOperation"
    }
}
