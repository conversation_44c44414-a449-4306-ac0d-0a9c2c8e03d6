/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MapPageJumper.kt
 ** Description : 地图页面跳转封装工具类
 **
 ** Version     : 1.0
 ** Date        : 2025/4/7
 ** Author      : huangyuanwang
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80243592 Huangyuanwang      2024/6/20     1.0        Add KFile header describe
 *********************************************************************************/
package com.oplus.gallery.business_lib.helper

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.View
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.activity.findMapContainerId
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_MAP
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_MAP_OF_MY_ALBUM_SET
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.business_lib.BusinessLibHelper.isPositiveOrder
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.location.set.MapLocationAlbum
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.map.IMapAbility
import com.oplus.gallery.framework.abilities.map.MapConstants
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.standard_lib.util.os.ContextGetter.context

class MapPageJumper {

    companion object {
        const val TAG = "MapPageJumper"

        /**
         * 检测地图功能是否授权
         */
        fun checkMapAgreed(context: Context): Boolean {
            val isDomestic = ConfigAbilityWrapper.getBoolean(IS_REGION_CN)
            val authorizingAbility: IPrivacyAuthorizingAbility? = context.getAppAbility()
            val mapAuthorited = authorizingAbility?.use { ability ->
                ability.isPrivacyAuthorized(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_GALLERY_MAP)
            } ?: false
            val result = if (isDomestic) {
                mapAuthorited
            } else {
                true
            }
            GLog.d(TAG, LogFlag.DL, "checkMapAgreed mapAuthorited $mapAuthorited, result: $result")
            return result
        }
    }

    /**
     * 进入地图和地图详情页面, 常用图集，我的图集，我的图集Fragment调用
     */
    fun enterMapLocationAlbum(
        fragment: BaseFragment?,
        fragmentContainerId: Int? = null,
        itemView: View?,
        radius: Float,
        toolBarBottomY: Float = 0f
    ) {
        fragment ?: return
        val activity = fragment.activity ?: return
        if (!NetworkMonitor.isNetworkValidated()) {
            ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.main_network_not_contected_msg)
            return
        }
        val mapAbility: IMapAbility? = context?.getAppAbility<IMapAbility>()
        val isMapPageSupport = mapAbility?.use {
            it.isMapSupported()
        } ?: false
        if (!isMapPageSupport) {
            GLog.w(TAG, LogFlag.DL) { "dispatchLocationClick not support map page!" }
            return
        }
        //val path = SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(timeRange)
        val path = SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(Path.SEGMENT_ALL)
        val mapLocationAlbum: MapLocationAlbum =
            DataManager.getMediaObject(path) as MapLocationAlbum
        mapLocationAlbum.setOrder(mapLocationAlbum.getDefaultOrder(isPositiveOrder()))
        val data = Bundle()
        data.putString(
            IntentConstant.AlbumConstant.KEY_MEDIA_PATH,
            mapLocationAlbum.path.toString()
        )
        data.putBoolean(IntentConstant.AlbumConstant.KEY_TARGET_MAP, true)
        //设置进入mapView时的默认缩放比例
        data.putFloat(IntentConstant.AlbumConstant.KEY_MAP_DEFAULT_ZOOM, MapConstants.ZOOM_LEVEL_5KM)

        val floatingWindowOffset = IntArray(2)
        if (fragment.isFloatingWindowMode()) fragment.view?.rootView?.getLocationOnScreen(floatingWindowOffset)
        itemView?.let {
            val entrance = if (it.width < it.height) ENTRANCE_MAP else ENTRANCE_MAP_OF_MY_ALBUM_SET
            FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                fragment.context,
                data,
                it,
                TriggerViewRectGetter(it, floatingWindowOffset, toolBarBottomY),
                entrance,
                radius
            )
        }

        /**这里是模拟从详情页点击进入MapView页面
        simulateIntoMapFromDetailPage(nodeIndex, data)*/
        if (!checkMapAgreed(activity)) {
            showGalleryMapAuthorizingDialog(fragment, activity, data)
            return
        }
        if (!NetworkPermissionManager.isUseOpenNetwork) {
            showPermissionAlertDialog(activity, data)
            return
        }
        if (GProperty.DEBUG_DISABLE_MAP.not()) {
            GLog.d(TAG, LogFlag.DL, "dispatchLocationClick, path $path, bundle path ${mapLocationAlbum.path}")
            // 2006002027 埋点，这里是模拟从详情页点击进入MapView页面，修改埋点类型
            AlbumsActionTrackHelper.trackAndSendEnterMapPage(AlbumsActionTackConstant.Value.ALBUM_ACTION_ENTER_MAP_FROM_MAP_ALBUM_VALUE)
            val containerId = fragmentContainerId ?: activity.findMapContainerId()
            fragment.startByStack<TemplateFragment>(
                resId = containerId,
                postCard = PostCard(RouterConstants.RouterName.MAP_TRAVEL_FRAGMENT),
                anim = SeamlessTransitionAnimation.SEAMLESS_ANIM_ARRAY,
                data = data,
            )
        }
    }


    fun enterMapLocationAlbumActivity(activity: Activity?, mediaItemPath: Path, defaultZoom: Float) {
        activity ?: return
        if (!NetworkMonitor.isNetworkValidated()) {
            ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.main_network_not_contected_msg)
            return
        }
        val mapAbility: IMapAbility? = context.getAppAbility<IMapAbility>()
        val isMapPageSupport = mapAbility?.use {
            it.isMapSupported()
        } ?: false
        if (!isMapPageSupport) {
            GLog.w(TAG, LogFlag.DL) { "dispatchLocationClick not support map page!" }
            return
        }
        //val path = SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(timeRange)
        val path = SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(Path.SEGMENT_ALL)
        val mapLocationAlbum: MapLocationAlbum =
            DataManager.getMediaObject(path) as MapLocationAlbum
        mapLocationAlbum.setOrder(mapLocationAlbum.getDefaultOrder(isPositiveOrder()))
        val data = Bundle()
        data.putString(
            IntentConstant.AlbumConstant.KEY_MEDIA_PATH,
            mapLocationAlbum.path.toString()
        )
        data.putBoolean(IntentConstant.AlbumConstant.KEY_TARGET_MAP, true)
        //设置进入mapView时的默认缩放比例
        data.putFloat(IntentConstant.AlbumConstant.KEY_MAP_DEFAULT_ZOOM, defaultZoom)
        simulateIntoMapFromDetailPage(mediaItemPath.toString(), data)
        if (!checkMapAgreed(activity)) {
            showGalleryMapAuthorizingDialog(null, activity, data)
            return
        }
        if (!NetworkPermissionManager.isUseOpenNetwork) {
            showPermissionAlertDialog(activity, data)
            return
        }
        if (GProperty.DEBUG_DISABLE_MAP.not()) {
            GLog.d(TAG, LogFlag.DL, "dispatchLocationClick  path $path, bundle path ${mapLocationAlbum.path}")
            // 2006002027 埋点，这里是模拟从详情页点击进入MapView页面，修改埋点类型
            AlbumsActionTrackHelper.trackAndSendEnterMapPage(AlbumsActionTackConstant.Value.ALBUM_ACTION_ENTER_MAP_FROM_PHOTO_DETAIL_VALUE)
            Starter.ActivityStarter(activity, data, PostCard(RouterConstants.RouterName.MAP_ACTIVITY)).start()
        }
    }

    private fun simulateIntoMapFromDetailPage(mediaItemPath: String, data: Bundle) {
        if (mediaItemPath.isNotEmpty()) {
            data.putBoolean(IntentConstant.AlbumConstant.KEY_FROM_IMAGE_DETAIL, true)
            data.putString(IntentConstant.AlbumConstant.KEY_IMAGE_DETAIL_PATH, mediaItemPath)
        }
    }

    private fun showGalleryMapAuthorizingDialog(baseFragment: BaseFragment?, activity: Activity, data: Bundle) {
        PermissionDialogHelper.showGalleryMapPrivacyDialog(
            activity,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    activity.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
                        it.authorizePrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_GALLERY_MAP)
                    }
                    NetworkPermissionManager.openNetwork(activity)
                    if (GProperty.DEBUG_DISABLE_MAP.not()) {
                        val launchActivity = baseFragment == null
                        if (launchActivity) {
                            Starter.ActivityStarter(activity, data, PostCard(RouterConstants.RouterName.MAP_ACTIVITY)).start()
                        } else {
                            val containerId = activity.findMapContainerId()
                            baseFragment?.startByStack<TemplateFragment>(
                                resId = containerId,
                                postCard = PostCard(RouterConstants.RouterName.MAP_TRAVEL_FRAGMENT),
                                anim = BaseFragment.DEFAULT_ANIM_ARRAY,
                                data = data,
                            )
                        }
                    }
                }
            })
    }

    private fun showPermissionAlertDialog(context: Context, data: Bundle) {
        val dialog = ConfirmDialog.Builder(context)
            .setPositiveButton(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_option_allow) { _, _ ->
                NetworkPermissionManager.openNetwork(context)
                //onPermissionDialogOKClick()
                if (GProperty.DEBUG_DISABLE_MAP.not()) {
                    Starter.ActivityStarter(context, data, PostCard(RouterConstants.RouterName.MAP_ACTIVITY)).start()
                }
            }
            .setNegativeButton(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_option_refuse) { _, _ ->
                //onPermissionDialogCancelClick()
            }
            .setMessage(context.getString(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_gallery_map))
            .setTitle(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_title)
            .setCancelable(false)
            .build()

        if (context.isValidActivity()) {
            dialog.show()
        }
    }


    private fun Context.isValidActivity(): Boolean {
        return if (this is Activity) {
            !isFinishing && !isDestroyed
        } else {
            false
        }
    }
}