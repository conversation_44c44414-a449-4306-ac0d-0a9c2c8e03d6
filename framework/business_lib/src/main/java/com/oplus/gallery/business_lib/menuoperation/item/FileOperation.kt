/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - FileOperation.kt
 ** Description: 文件操作接口.
 ** Version: 1.0
 ** Date : 2023/11/7
 ** Author: Fonan.<PERSON><PERSON>@Android.Apps.Gallery
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <data>   <version >    <desc>
 **  Fonan.Zhong    2023/11/7     1.0     build this module
 ****************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import androidx.annotation.WorkerThread
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper

/**
 * 文件操作接口
 * 创建图集菜单中需要同时支持移动和复制到文件不同方式操作，提供接口给对应实际业务类代理实现
 */
interface FileOperation {
    /**
     * 添加文件到图集
     * @param paths 选中的文件列表路径
     * @param targetAlbumName 从图集tab新建图集、从自建图集里点+添加到自检图集、从复制到里点新建图集，复制到自建图集，都传此参数
     * @param targetAlbumPath 复制到非自建图集
     * @param bucketId 存储桶唯一标识
     * @param onProgressChanged 操作进度回调
     */
    @WorkerThread
    fun addToAlbum(
        paths: List<Path>,
        targetAlbumName: String? = null,
        targetAlbumPath: Path? = null,
        bucketId: String?,
        onProgressChanged: ((progress: Int) -> Unit)? = null
    ): ResultEntry?


    /**
     * 设置操作取消
     */
    fun operationCancel() {
        //do nothing
    }
}

/**
 * resultCode: 结果标识，包含INVALID、SUCCESS、ALL_FILE_FILTERED、SOME_FILE_FILTERED、SOME_FILE_FAILED、STORAGE_NOT_ENOUGH、IS_ALL_SAME、
 *            USER_CANCEL等
 * succeedCount: 处理文件成功的数量
 * filterCount: 过滤文件数量
 * failedCount: 处理失败文件数量
 * storageType: 存储类型，内置或者外置存储
 * state: 空间存储状态，详细见OplusEnvironment.StorageType
 * isAllTheSame: 复制过程中判断文件是否全部相同，用于生成resultCode#IS_ALL_SAME
 * sameCount: 重复文件数量，用于提示英文复数词条判断
 * storageLimitCnt: 空间不足的文件数量
 * storageLimitSize：空间不足大小
 */
data class ResultEntry(
    var resultCode: Int = INVALID,
    var succeedCount: Int = 0,
    var filterCount: Int = 0,
    var failedCount: Int = 0,
    var storageType: OplusEnvironment.StorageType = OplusEnvironment.StorageType.PHONE_STORAGE,
    var state: StorageLimitHelper.State = StorageLimitHelper.State.OK,
    var isAllTheSame: Boolean = false,
    var sameCount: Int = 0,
    var storageLimitCnt: Int = 0,
    var storageLimitSize: Long = 0L
) {
    companion object {
        const val INVALID = -1
        const val SUCCESS = 0
        const val ALL_FILE_FILTERED = 1
        const val SOME_FILE_FILTERED = 2
        const val SOME_FILE_FAILED = 3
        const val STORAGE_NOT_ENOUGH = 4
        const val IS_ALL_SAME = 5
        const val USER_CANCEL = 6
        const val ALL_FILE_FAILED = 7
    }
}