/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FileProcessManager.kt
 ** Description: 文件处理任务管理器，
 ** 1、负责任务的执行
 ** 2、任务前后台切换（后续扩展）
 ** 3、对后台任务支持功耗、发热等策略管控（后续扩展）
 ** Version: 1.0
 ** Date: 2022/5/17
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** yaob<PERSON><PERSON>@Apps.Gallery      2022/5/17   1.0              build this module
 **
 *************************************************************************************************/
package com.oplus.gallery.business_lib.fileprocessor

import android.os.Process
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.foundation.util.thread.PriorityThreadFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors

object FileProcessManager {

    private const val TAG = "FileProcessManager"
    private const val DISPATCHER_NAME_SINGLE_FILE_PROCESS = "singleFileProcess"

    /**
     * 用于文件处理、回调通知的单线程池协程。
     * 因为处理的主要是涉及UI交互的前台任务，所以线程优先级使用默认的，而不是后台优先级
     */
    val Dispatchers.SINGLE_FILE_PROCESS by lazy {
        Executors.newSingleThreadExecutor(
            PriorityThreadFactory(
                DISPATCHER_NAME_SINGLE_FILE_PROCESS,
                Process.THREAD_PRIORITY_DEFAULT
            )
        ).asCoroutineDispatcher()
    }

    /**
     * 未执行完成的任务列表
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    val runningTasks: CopyOnWriteArrayList<BaseFileProcessTask> = CopyOnWriteArrayList()


    /**
     * 查找执行类型、指定文件路径的 运行中 的任务
     * @param taskType 任务类型
     * @param galleryId 文件对应的相册数据库_id。该方法只支持查找匹配处理单个文件的任务。目前没有处理多文件任务需要匹配的场景。
     * @return 返回查找到的允许中的任务，找不到就返回null
     */
    @JvmStatic
    fun findRunningTask(taskType: Int, galleryId: Int): BaseFileProcessTask? {
        runningTasks.iterator().forEach {
            if ((taskType == it.taskType) && (it.galleryIds.size == 1) && it.galleryIds[0] == galleryId) {
                return it
            }
        }
        return null
    }

    /**
     * 当前是否有前台任务在执行
     */
    @JvmStatic
    fun hasForegroundTaskRunning(): Boolean {
        runningTasks.iterator().forEach {
            if (!it.isDone && it.isForeground) return true
        }
        return false
    }

    @JvmStatic
    internal fun removeTask(task: BaseFileProcessTask?) {
        task?.let {
            runningTasks.remove(task)
        }
    }

    /**
     * 执行一个文件处理任务
     */
    @JvmStatic
    fun post(task: BaseFileProcessTask?) {
        task?.let {
            runningTasks.add(it)
            runInner(it)
        }
    }

    @JvmStatic
    private fun runInner(task: BaseFileProcessTask) {
        AppScope.launch(if (task.runOnMainThread) Dispatchers.UI else Dispatchers.SINGLE_FILE_PROCESS) {
            task.run()
        }
    }
}