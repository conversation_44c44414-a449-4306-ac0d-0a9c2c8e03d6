/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BasePreferenceWithAppbarFragment.kt
 ** Description : 自定义顶部带有AppbarLayout的Preference样式的Fragment基类
 ** Version     : 1.0
 ** Date        : 2023/04/14
 ** Author      : <EMAIL>
 ** TAG         : BasePreferenceWithAppbarFragment
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/04/14  1.0         BasePreferenceWithAppbarFragment
 *********************************************************************************/
package com.oplus.gallery.business_lib.ui.fragment

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.preference.PreferenceGroupAdapter
import androidx.preference.PreferenceScreen
import androidx.preference.PreferenceViewHolder
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout
import com.oplus.gallery.addon.view.FlexibleWindowManagerWrapper
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.foundation.uikit.systembar.WindowInsetsUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil

/**
 * 顶部带有AppbarLayout的Preference样式的Fragment基类
 */
open class BasePreferenceWithAppbarFragment : COUIPreferenceWithAppbarFragment() {
    var appBarLayout: AppBarLayout? = null
    private var recyclerView: RecyclerView? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        appBarLayout = view.findViewById(com.oplus.gallery.basebiz.R.id.appbar_layout)
        appBarLayout?.findViewById<COUIToolbar>(com.oplus.supertext.ostatic.R.id.toolbar)?.titleView?.textDirection = View.TEXT_DIRECTION_ANY_RTL
        recyclerView = listView
        refreshAppbarLayout()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        refreshRecycleViewTopPadding()
        refreshAppbarLayout()
    }

    override fun onCreateAdapter(preferenceScreen: PreferenceScreen?): RecyclerView.Adapter<*> {
        return LocalPreferenceGroupAdapter(preferenceScreen)
    }

    /**
     * 以跟手面板显示时，要求减小顶部间距，需要移除顶部占位的ImageView
     * 全屏显示时，恢复原有的顶部间距，如果已经移除过顶部占位ImageView，需要重新添加
     */
    private fun refreshAppbarLayout() {
        /**
         * 以跟手面板形式显示，且顶部是ImageView时，移除顶部ImageView
         * 非跟手面板显示，且顶部不是ImageView时，添加顶部ImageView
         */
        appBarLayout?.let {
            val isChildViewIndex0ImageView = it.getChildAt(INDEX_ZERO)?.let { childViewIndex0 ->
                childViewIndex0 is ImageView
            } ?: false
            val isFlexibleActivitySuitable: Boolean =
                FlexibleWindowManagerWrapper.isFlexibleActivitySuitable(requireActivity().resources.configuration)
            if (isFlexibleActivitySuitable && isChildViewIndex0ImageView) {
                it.removeViewAt(INDEX_ZERO)
            } else if (!isFlexibleActivitySuitable && !isChildViewIndex0ImageView) {
                val statusBarView = getStatusBarView()
                it.addView(statusBarView, 0, statusBarView.layoutParams)
            }
        }
    }

    /**
     * 为recyclerView添加OnGlobalLayoutListener，在顶部AppbarLayout高度变化时，更新recyclerView顶部占位FrameLayout的高度
     */
    private fun refreshRecycleViewTopPadding() {
        recyclerView?.let { recyclerView ->
            recyclerView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    val header: View? = recyclerView.getChildAt(0)
                    if ((header != null) && (header is FrameLayout)) {
                        val topPadding = appBarLayout?.let {
                            it.measuredHeight - resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_divider_height)
                        }
                        if ((topPadding != null) && (topPadding > 0)) {
                            val params = header.layoutParams as RecyclerView.LayoutParams
                            if (params.height != topPadding) {
                                params.height = topPadding
                                header.layoutParams = params
                                GLog.d(TAG, "refreshRecycleViewTopPadding and set header height, topPadding =$topPadding")
                            }
                        }
                        recyclerView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                }
            })
        }
    }

    override fun getTitle(): String {
        return TextUtil.EMPTY_STRING
    }

    /**
     * 获取高度为getStatusBarHeight()的占位ImageView
     */
    private fun getStatusBarView(): View {
        val view = ImageView(this.activity)
        view.scaleType = ImageView.ScaleType.FIT_XY
        val layoutParams = ViewGroup.LayoutParams(DEFAULT_WIDTH, WindowInsetsUtils.getStatusBarHeight(view.context))
        view.layoutParams = layoutParams
        return view
    }

    @SuppressLint("RestrictedApi")
    inner class LocalPreferenceGroupAdapter(preferenceScreen: PreferenceScreen?) : PreferenceGroupAdapter(preferenceScreen) {
        override fun onBindViewHolder(holder: PreferenceViewHolder, position: Int) {
            super.onBindViewHolder(holder, position)
            val itemView = holder.itemView
            /**
             * 页面顶部滑出屏幕时切换中大屏/小屏，然后滑动屏幕，顶部重新划入屏幕时需要更新占位FrameLayout高度
             */
            if (itemView is FrameLayout) {
                val topPadding = appBarLayout?.let {
                    it.measuredHeight - resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_divider_height)
                }
                val itemLayoutParams = itemView.layoutParams as RecyclerView.LayoutParams
                if ((topPadding != null) && (itemLayoutParams.height != topPadding)) {
                    itemLayoutParams.height = topPadding
                    itemView.layoutParams = itemLayoutParams
                }
            }
        }
    }

    companion object {
        private const val TAG = "BasePreferenceWithAppbarFragment"
        private const val INDEX_ZERO = 0
        private const val DEFAULT_WIDTH = -1
    }
}