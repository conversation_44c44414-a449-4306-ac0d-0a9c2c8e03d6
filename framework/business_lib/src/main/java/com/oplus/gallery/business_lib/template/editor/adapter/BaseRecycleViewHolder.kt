/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - BaseRecycleViewHolder.kt
 * Description:
 * Version:
 * Date:
 * Author: lixing@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * xinyang.Hu@Apps.Gallery3D       2017/11/04    1.0     build this module
 * duchengsong@Apps.Gallery3D      2021/4/22     2.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.template.editor.adapter

import android.graphics.drawable.Drawable
import android.util.SparseArray
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.foundation.util.debug.GLog

open class BaseRecycleViewHolder(
    itemView: View
) : RecyclerView.ViewHolder(itemView) {

    private val viewMap: SparseArray<View?> = SparseArray()

    fun <T : View?> findViewById(viewId: Int): T? {
        var view = viewMap[viewId]
        view ?: let {
            view = itemView.findViewById(viewId)
        }
        view?.let {
            viewMap.put(viewId, it)
        }
        return view as? T
    }

    fun setClickListener(viewId: Int, listener: View.OnClickListener): BaseRecycleViewHolder {
        findViewById<View>(viewId)?.setOnClickListener(listener)
        return this
    }
}

private const val TAG: String = "BaseRecycleViewHolder"

interface Selectable {
    companion object {
        const val CLEAR = 0
        const val SELECTED = 1
        //未选中状态
        const val UNSELECTED = 2
        //禁用态
        const val DISABLE = 3
        //特殊背景选中状态
        const val SELECTEDSPE = 4
        //可点击不可选中状态
        const val SELECTABLE = 5
    }

    fun setSelectedState(selectedState: Int)

    /***
     * 设置状态背景图片
     * @param selectedState 按钮状态
     * @param drawable 背景drawable
     * */
    fun setSelectedState(selectedState: Int, drawable: Drawable)

    fun isSelected(): Boolean
}

interface HolderViewRecycled {
    fun onViewRecycled(holder: RecyclerView.ViewHolder?)
}

fun RecyclerView.ViewHolder.setViewSelectedState(selectedState: Int) {
    if (this is Selectable) {
        setSelectedState(selectedState)
    } else {
        GLog.e(TAG, "${this.javaClass.simpleName} need impl interface Checkable")
    }
}

fun RecyclerView.ViewHolder.isViewSelected(): Boolean {
    return if (this is Selectable) {
        isSelected()
    } else {
        GLog.e(TAG, "${this.javaClass.simpleName} need impl interface Checkable")
        false
    }
}

fun RecyclerView.ViewHolder.onViewRecycled() {
    if (this is HolderViewRecycled) {
        onViewRecycled(this)
    } else {
        GLog.e(TAG, "${this.javaClass.simpleName} need impl interface HolderViewRecycled")
    }
}