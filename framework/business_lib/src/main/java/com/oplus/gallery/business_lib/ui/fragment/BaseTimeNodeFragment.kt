/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BaseTimeNodeFragment.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/6/1 9:18
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2021/6/1      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.ui.fragment

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.widget.Toast
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.addon.utils.OSVersionUtils.OPLUS_OS_12_1
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_CURRENT_PAGE
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_ENTER_PHOTO_ANIMATE
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_INDEX_HINT
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_POSITION_CONTROLLER
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_SHARED_ID
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_VIEW_REVERT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_ENABLE_DRAG_OUT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_ENABLE_ROTATE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_MODEL_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_PREVIEW_SELECT_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE_PREVIEW
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_SELECTION_DATA_ID
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_ENABLE_DRAG_OUT_DISABLE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_ENABLE_ROTATE_DISABLE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_PHOTO_PREVIEW_SELECT_MULTI
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_PHOTO_PREVIEW_SELECT_SINGLE
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.PageLayoutDisabledAdjust
import com.oplus.gallery.basebiz.sidepane.RESTORE_DELAY_TIME
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.transition.widget.PhotoClipBoundTransitionView
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity.Companion.FINISH_FRAGMENT_STACK_SIZE
import com.oplus.gallery.basebiz.uikit.activity.findFullScreenContainerId
import com.oplus.gallery.basebiz.uikit.controller.TintTranslucentElement
import com.oplus.gallery.basebiz.uikit.controller.filter.FilterPanelTintExpandElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintBackElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintFilterElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintMenuButtonDrawableElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintMenuButtonTextElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintStatusElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintSubtitleElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintTitleElement
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.ISelectionModeCallback
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.basebiz.widget.filter.PersonalFilterPanelView
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.controller.PersonalFilterViewWrapper
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisToolsHelper
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.helper.PermissionDialogHelper
import com.oplus.gallery.business_lib.helper.ShareHelper
import com.oplus.gallery.business_lib.menuoperation.ShareAction
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.Companion.EXTRA_KEY_SELECTION
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.location.set.MapLocationAlbum
import com.oplus.gallery.business_lib.model.selection.NotifyState
import com.oplus.gallery.business_lib.model.selection.OnSelectionListener
import com.oplus.gallery.business_lib.model.selection.SegmentSelectedItem
import com.oplus.gallery.business_lib.model.selection.SelectionData
import com.oplus.gallery.business_lib.onetouchshare.OneTouchShareController
import com.oplus.gallery.business_lib.synergy.SynergyTouchInterceptor
import com.oplus.gallery.business_lib.timeline.data.TimeViewData
import com.oplus.gallery.business_lib.timeline.diff.DiffResult
import com.oplus.gallery.business_lib.timeline.diff.ElementAdapter
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_LOCATION_TITLE
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_NODE_CHECKBOX
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_PREVIEW_CHECKBOX
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_PREVIEW_PICTURE
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_SLOT_ITEM
import com.oplus.gallery.business_lib.timeline.layout.BaseLayouter.Companion.ELEMENT_TYPE_SLOT_ITEM_EXPAND
import com.oplus.gallery.business_lib.timeline.layout.OnElementClickListener
import com.oplus.gallery.business_lib.timeline.presentation.Presentation
import com.oplus.gallery.business_lib.timeline.view.TimelineView
import com.oplus.gallery.business_lib.timeline.view.TimelineView.PlaceHolder
import com.oplus.gallery.business_lib.timeline.viewmodel.BaseSlidingWindow
import com.oplus.gallery.business_lib.timeline.viewmodel.TimelinePreLoader
import com.oplus.gallery.business_lib.timeline.viewmodel.TimelineViewModel
import com.oplus.gallery.business_lib.track.FloatingWindowTrackHelper
import com.oplus.gallery.business_lib.transition.IPhotoPagePreTransitionController
import com.oplus.gallery.business_lib.transition.PhotoPagePreTransitionController
import com.oplus.gallery.business_lib.transition.PreTransitionStartInfo
import com.oplus.gallery.business_lib.transition.TransitionHelper
import com.oplus.gallery.business_lib.ui.IPicturePositionController
import com.oplus.gallery.business_lib.util.FastScrollerMessageFormater
import com.oplus.gallery.business_lib.util.TimelineUtils.TIMELINE_TAG
import com.oplus.gallery.business_lib.util.draganddrop.DragData
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.util.draganddrop.DragOptions
import com.oplus.gallery.business_lib.util.draganddrop.animation.DragDropAnimationHelper
import com.oplus.gallery.business_lib.util.draganddrop.listener.OnDragEventListener
import com.oplus.gallery.foundation.fileaccess.FileConstants
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUM_ACTION_ENTER_MAP_FROM_TIME_LINE_VALUE
import com.oplus.gallery.foundation.tracing.constant.FloatingWindowTrackConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.helper.LaunchExitPopupTrackHelper
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.ui.systembar.toolbar.DirectionAnimationElement
import com.oplus.gallery.foundation.ui.systembar.toolbar.IProgressChanger
import com.oplus.gallery.foundation.ui.systembar.toolbar.TintDrawableAnimationElement
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.onWidthChanged
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.ext.safeUse
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_MAP_PAGE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_GALLERY_MAP
import com.oplus.gallery.framework.abilities.data.DataRepository.KEY_IS_SUPPORT_REVERT_ORDER
import com.oplus.gallery.framework.abilities.data.DataRepository.TimelineModelGetter.Companion.TYPE_TIMELINE_ALBUM
import com.oplus.gallery.framework.abilities.data.IDataAbility
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.framework.abilities.map.MapConstants
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.TrackScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.popupwindow.MenuListPopupItem
import com.oplus.gallery.standard_lib.ui.scroller.FullScreenFastScroller
import com.oplus.gallery.standard_lib.ui.toolbar.actionview.MenuButton
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import kotlin.math.abs
import com.oplus.gallery.basebiz.R as BasebizR
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizingR

@Suppress("LargeClass")
abstract class BaseTimeNodeFragment : TemplateFragment(), OnElementClickListener,
    OnSelectionListener<Path>, OnDragEventListener, IPicturePositionController,
    TimelineView.OnItemLongClickListener, ISidePaneListener, ISelectionModeCallback,
    IPhotoPagePreTransitionController by PhotoPagePreTransitionController() {
    protected val logTag = "$TIMELINE_TAG${this.javaClass.simpleName}"

    /**
     * 是否保持系统栏可见。
     */
    private var isKeepSystemBarVisible: Boolean = true
        set(value) {
            if (value == field) return
            field = value
            setupSystemBarVisibility(value)
        }

    protected lateinit var timelineView: TimelineView
    protected lateinit var bottomMenuHelper: BottomMenuHelper
    val timelineViewModel: TimelineViewModel by lazy {
        onCreateViewModel()
    }

    protected var fastScroller: FullScreenFastScroller? = null

    protected val fastScrollerMessageFormater: FastScrollerMessageFormater by lazy {
        FastScrollerMessageFormater()
    }

    /**
     * pre transition的容器控件
     */
    private var preTransitionPageContainer: View? = null

    /**
     * 大图过渡动画控件；图片
     */
    private var preTransitionView: PhotoClipBoundTransitionView? = null

    /**
     * 当前点击查看图片的drawable
     */
    private var drawable: Drawable? = null

    /**
     * 当前点击查看图片的mediaItem
     */
    private var mediaItem: MediaItem? = null

    /**
     * 获取入场动画完成时，大图内容的显示区域
     */
    private val onGetPreTransitionConstraintRect = {
        val rect = Rect().apply {
            val thumbnail = drawable ?: return@apply
            val activity = activity ?: return@apply
            val mediaItem = mediaItem ?: return@apply
            set(getPreTransitionConstraintRect(activity, thumbnail, mediaItem))
        }
        rect
    }

    /**
     * 转场动画管理类实例
     */
    private val transitionManager by lazy {
        val container = preTransitionPageContainer ?: return@lazy null
        val preTransition = preTransitionView ?: return@lazy null

        val isSupportedPreTransition = TransitionHelper.isSupportedPreTransition()
        if (isSupportedPreTransition.not()) {
            return@lazy null
        }

        createPhotoPageTransitionManager(this, preTransition, container, onGetPreTransitionConstraintRect)
    }

    private var lastFastScrollerTouchY = 0f
    private var galleryIdList: String? = null
    private var isNeedShowCreateMenuFlag: Boolean? = null

    /**
     * 当前是否显示顶部半透明背景
     */
    private var currentShowTopTranslucent: Boolean = false

    // 适配多个页面拉起多个大图页情况
    // 1、用hashcode作为当前fragment的key传给大图页
    // 2、大图页通过该key获取对应的positionController从而得到对应的缩图位置
    protected val positionControllerKey = hashCode().toString()

    override var trackAlbumPageInfo: String? = LaunchExitPopupConstant.Value.CUR_PAGE_TIMELINE_PAGE

    private var dragDropAnimationHelper: DragDropAnimationHelper? = null

    // 当前页面是否收到drop事件
    private var receiveDrop: Boolean = false

    private val positiveOrderObserver = Observer<Boolean> { isPositiveOrder ->
        timelineView.revertLayoutDirection = if (isSupportViewRevertLayoutDirection) isPositiveOrder() else false
        timelineViewModel.updateOrder(isPositiveOrder)
        if (isSupportViewRevertLayoutDirection && isImmersivePresentation && (isCurPositiveOrder != isPositiveOrder)) {
            timelineView.restPresentLayoutOffset()
            timelineView.updateAllHolders()
        }
        this.isCurPositiveOrder = isPositiveOrder
    }

    /**
     * 是否支持PC互联，图集页面默认为true
     */
    open var supportSynergy = true

    /**
     * 是否支持View反转布局方向（正倒序），如果是model反转顺序或者不支持正倒序，则需要覆写为false
     */
    protected open val isSupportViewRevertLayoutDirection: Boolean = true

    /**
     * 该页面是否支持正倒序
     */
    protected open val isSupportRevertOrder: Boolean = true

    /**
     * 是否属于多选状态，
     * 属于多选状态，大图预览的右上角menu为 checkBox；
     * 属于单选状态，大图预览的右上角menu为 “√”
     */
    protected open val isSelectedMulti: Boolean = true

    /**
     * 是否支持一碰分享(时间轴页，进入选择模式，存在分享按钮的页面，都应该支持，应该设置为true)
     */
    protected open val supportOneTouchShare: Boolean = false

    /**
     * 一碰分享能力
     */
    private var oneTouchShareController: OneTouchShareController? = null

    private val synergyTouchInterceptor by lazy {
        SynergyTouchInterceptor { x, y ->
            val position = timelineView.findVisibleItemUnder(x.toInt(), y.toInt())
            val path = timelineViewModel.getPath(position)
            val isSelectionMode = timelineView.isSelectionMode
            GLog.d(logTag, "synergyTouch. pos:$position,selectionMode=$isSelectionMode,path=$path")

            val result = mutableSetOf<Path>()
            if (isSelectionMode) {
                timelineViewModel.selectItem(position)
                result.addAll(timelineViewModel.getSelectedItems())
            } else {
                path?.let { result.add(it) }
            }
            result
        }
    }

    private val elementAnimationListener by lazy {
        object : ElementAdapter.OnAnimationListener {
            override fun onStart() {
                timelineViewModel.needSkipRefreshData = timelineView.curPresentation().isAnimating()
            }

            override fun onFinish() {
                timelineViewModel.needSkipRefreshData = false
            }
        }
    }

    /**
     * 是否支持个性化筛选
     */
    protected open val isSupportedPersonalFilter: Boolean = false

    /**
     * 当前是否倒序
     */
    protected var isCurPositiveOrder: Boolean = false

    // 顶部半透明背景View
    protected var topTranslucentView: View? = null
    protected var createMemoryMenuItem: MenuItem? = null
    protected var searchMenuItem: MenuItem? = null
    protected var cancelButton: MenuButton? = null
    protected var personalFilterPanelView: PersonalFilterPanelView? = null

    protected var filterMenuItem: MenuItem? = null
        set(value) {
            if (field == value) return
            field = value
            field?.isVisible = isSupportedPersonalFilter
        }
    protected open val personalFilterViewWrapper: PersonalFilterViewWrapper by lazy {
        object : PersonalFilterViewWrapper({ timelineViewModel }) {
            override fun onUpdateFilterMenuItem() {
                checkToolbarTint(timelineView.scrollPositionExtend(), true, isNeedAnimation = false)
            }
        }
    }

    /**
     * 是否是在沉浸视图
     * 可以在设置页-照片页切换
     * 是-沉浸视图，时间轴的图片会紧密罗列，不显示日期信息
     * 否-日期视图，时间轴的常规视图，显示日期信息
     */
    protected var isImmersivePresentation = false
    protected open val isSupportedTimelineImmersive = true

    /**
     * 页面初始化时，创建所有SlidingWindow
     */
    abstract fun onCreateSlidingWindows(): List<BaseSlidingWindow>

    /**
     * 页面初始化时，创建所有Presentation
     */
    abstract fun onCreatePresentations(): List<Presentation>

    /**
     * 页面初始化时，创建ViewModel
     */
    abstract fun onCreateViewModel(): TimelineViewModel

    /**
     * 获取视图Model
     */
    abstract fun getModel(modelType: String = TYPE_TIMELINE_ALBUM, bundle: Bundle = Bundle()): BaseModel<MediaItem>

    /**
     * 视图item数量发生变化时触发
     */
    abstract fun onTotalCountChanged(type: String, totalCount: Int)

    /**
     * 获取快速滑动条的布局ID
     */
    abstract fun getFastScrollerId(): Int

    open fun onSpecialCountChange(type: String, bundle: Bundle) {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isSupportImmersive = true
        galleryIdList = arguments?.getString(IntentConstant.ViewGalleryConstant.KEY_GALLERY_ID_LIST)

        savedInstanceState?.apply {
            restoreFragmentState(savedInstanceState)
        }

        registerPositionController()
        loadImmersivePresentationConfig()
        val slidingWindows = onCreateSlidingWindows()
        timelineViewModel.init(slidingWindows, getDefaultPresentationType())
        bottomMenuHelper = BottomMenuHelper(activity as? BaseActivity, timelineViewModel)
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        initView(view)
        scrollPositionToImmersive = resources.getDimensionPixelSize(BasebizR.dimen.common_toolbar_padding_top)
        observeTimelineViewModel()
        initSidePaneAccessor()
        lifecycleScope.launch(Dispatchers.CPU) {
            isNeedShowCreateMenuFlag = GifSynthesisToolsHelper.isNeedShowCreateMenu()
        }
    }

    private fun initSidePaneAccessor() = GTrace.trace("$TAG.initSidePaneAccessor") {
        sidePaneAccessor?.takeIf { isSidePaneEnabled() }?.let { accessor ->
            // 时间轴同步侧边栏展开收起动画
            SidePaneAnimationController(this, timelineView, accessor)

            contentView?.let {
                // 侧边栏动画过程中,需要禁掉非resume的fragment刷新
                PageLayoutDisabledAdjust(this, accessor) { RESTORE_DELAY_TIME }.bindView(it)
            }
        }
    }

    private fun observeTimelineViewModel() = GTrace.trace("$TAG.observeTimelineViewModel") {
        timelineViewModel.isSelectionMode.observe(this) { isSelectionMode ->
            if (isSelectionMode) {
                onEnterSelectionMode()
            } else {
                onExitSelectionMode()
            }
            val thisFragment = this
            (activity as? BaseActivity)?.apply {
                if (isNeedPredictiveBack()
                    && (fragmentStackSize() <= FINISH_FRAGMENT_STACK_SIZE)) {
                    setBackInterception(isSelectionMode)
                    fragmentSelectStateMap[thisFragment] = isSelectionMode
                    GLog.d(TAG, DL) { "Fragment $this isSelectionMode: $isSelectionMode" }
                }
            }
        }
        timelineViewModel.isPositiveOrder.observeForever(positiveOrderObserver)
        timelineViewModel.specifiedCountMap.forEach { (type, liveData) ->
            liveData.observe(this) { specifiedCount ->
                onSpecialCountChange(type, specifiedCount)
            }
        }
        timelineViewModel.immersivePresentationLiveData.observe(this) {
            if (isSupportedTimelineImmersive && (isImmersivePresentation != it)) {
                isImmersivePresentation = it
                updatePresentations()
            }
        }
    }

    protected fun isNeedShowCreateMenuFlag(): Boolean {
        if (isNeedShowCreateMenuFlag == null) {
            isNeedShowCreateMenuFlag = GifSynthesisToolsHelper.isNeedShowCreateMenu()
        }
        return isNeedShowCreateMenuFlag == true
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initView(view: View) = GTrace.trace("$TAG.initView") {
        initPreTransitionView()
        timelineView = view.findViewById(R.id.timeline_view)
        timelineView.timelineViewModel = timelineViewModel
        timelineView.addElementAnimationListener(elementAnimationListener)
        timelineView.setOnItemLongClickListener(this)
        timelineView.setOnElementClickListener(this)
        timelineView.addOnScrollListener(object : TimelineView.OnScrollListener {
            override fun onScrolled(scrollPosition: Int) {
                toolbarSetter?.setListScrollPosition(scrollPosition)
            }

            override fun onScrollStateChanged(newState: Int, scrollPosition: Int) {
                if (newState == TimelineView.SCROLL_STATE_IDLE) {
                    toolbarSetter?.setListScrollStateIdle(timelineView, scrollPosition)
                }
            }
        })
        topTranslucentView = view.findViewById(R.id.timeline_top_translucent_view)
        val presentations = onCreatePresentations()
        if (presentations.size != timelineViewModel.windowCount()) {
            throw IllegalStateException("Presentations size should equals to slidingWindows size.")
        }
        presentations.forEach {
            timelineViewModel.timelineInfo(it.type)?.let { timelineInfo ->
                it.timelineInfo = timelineInfo
            }
        }
        val isPositiveOrder = isPositiveOrder()
        timelineView.init(this, presentations, getDefaultPresentationType())
        timelineView.revertLayoutDirection = if (isSupportViewRevertLayoutDirection) isPositiveOrder else false
        if (isSupportRevertOrder.not()) {
            timelineView.curPresentation().needResetScrollPosition = false
        }
        timelineViewModel.updateOrder(isPositiveOrder)
        initReLoadListener()
        if (supportMapTitle()) {
            timelineViewModel.initTimelineMapTitleMaker(galleryIdList)
        }
        timelineView.timelineViewModel.registerOnSelectionListener(this)
        initFastScroller(view)
    }

    /**
     * 初始化Pre Transition动画相关View视图
     */
    private fun initPreTransitionView() {
        val activity = activity ?: return
        preTransitionPageContainer = activity.findViewById<View>(
            BasebizR.id.pre_transition_view_container
        )?.apply {
            preTransitionView = findViewById(BasebizR.id.pre_transition_view)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    protected open fun initFastScroller(view: View) {
        fastScroller = view.findViewById(getFastScrollerId())
        fastScroller?.let {
            timelineView.fastScroller = it
            timelineView.setOnScrollChangeListener { _, _, y, _, _ ->
                if (it.isVisible) {
                    val scrollRange = fastScrollerScrollRange()
                    val scrollLimitExtend = timelineView.scrollLimitExtend().toFloat()
                    if (scrollLimitExtend != 0.0f) {
                        val scrollRatio = timelineView.scrollPositionExtend() / scrollLimitExtend
                        it.translationY = scrollRange * scrollRatio
                    }
                }
            }
            it.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        it.parent.requestDisallowInterceptTouchEvent(true)
                        timelineView.stopScroll()
                        lastFastScrollerTouchY = event.rawY
                        onFastScrollerTouchDown()
                    }
                    MotionEvent.ACTION_MOVE -> {
                        val dy = event.rawY - lastFastScrollerTouchY
                        lastFastScrollerTouchY = event.rawY
                        val scrollRange = fastScrollerScrollRange()
                        timelineView.scrollBy(
                            0,
                            (dy * timelineView.scrollLimitExtend() / scrollRange).toInt()
                        )
                        onFastScrollerTouchMove(it.translationY == 0F, it.translationY >= scrollRange)
                    }
                    MotionEvent.ACTION_UP -> {
                        it.parent.requestDisallowInterceptTouchEvent(false)
                        onFastScrollerTouchUp()
                    }
                }
                true
            }
        }
    }

    /**
     * FastScroller Touch ACTION_DOWN
     */
    protected open fun onFastScrollerTouchDown() {
        showFastScrollerMessageView()
        updateFastScrollerMessage()
    }

    /**
     * FastScroller Touch ACTION_MOVE
     */
    protected open fun onFastScrollerTouchMove(isToTop: Boolean, isToBottom: Boolean) {
        if (fastScroller?.isMessageVisible() == false) showFastScrollerMessageView()
        updateFastScrollerMessage()
    }

    /**
     * FastScroller Touch ACTION_UP
     */
    protected open fun onFastScrollerTouchUp() {
        hideFastScrollerMessageView()
    }

    /**
     * 显示滑块日期
     */
    protected open fun showFastScrollerMessageView() {
        if (fastScroller?.isMessageVisible() == true) return
        fastScroller?.setMessageVisibility(VISIBLE)
    }

    /**
     * 隐藏滑块日期
     */
    protected open fun hideFastScrollerMessageView() {
        if (fastScroller?.isMessageVisible() == false) return
        fastScroller?.setMessageVisibility(GONE)
    }

    /**
     * 更新滑块日期
     */
    protected fun updateFastScrollerMessage() {
        val yGap = timelineView.curPresentation().layoutConfig.vGap
        fastScroller?.let {
            if (it.isVisible.not() || it.isMessageVisible().not()) return

            /** 计算滑块的坐标，注意X坐标要考虑气泡的位置 */
            val fastScrollerX = if (ResourceUtils.isRTL(context)) {
                it.left + it.translationX
            } else {
                it.right - it.getScrollerImgAreaWidth() + it.translationX
            }

            val fastScrollerY = it.top + it.height + it.translationY

            /** 找到滑块所在位置的 Pair(NodeIndex, ItemIndex) */
            var pair = timelineView.curPresentation().layouter.findVisibleItemUnder(fastScrollerX.toInt(), fastScrollerY.toInt())

            /** 二次查找（坐标可能落在item之间的gap区域）*/
            if (pair.first == INVALID_INDEX) {
                pair = timelineView.curPresentation().layouter.findVisibleItemUnder(fastScrollerX.toInt(), fastScrollerY.toInt() - yGap)
            }

            /** 根据 NodeIndex 获取对应的 TimeNode */
            val timeNode = timelineView.curPresentation().timelineInfo.getTimeNode(pair.first)
            if (timeNode == null) {
                GLog.w(
                    logTag,
                    LogFlag.DF
                ) { "[updateFastScrollerMessage] getTimeNode of index ${pair.first} is null , return" }
                return
            }
            val type = timelineView.curPresentation().type

            /** 从 TimeNode 中获取 timestamp（此时间节点的日期时间戳）并根据当前的 type 转换成对应的日期显示格式 */
            val date = formatDateMessage(type, timeNode.timestamp)
            if (date.isNotEmpty()) it.setMessage(date)
        }
    }

    /**
     * 格式化滑块日期
     *
     * @param type 年/月/日 视图
     * @param timestamp 时间戳
     * @return 日期格式化文本
     */
    protected open fun formatDateMessage(type: String, timestamp: Long): String {
        return if (timestamp == 0L) EMPTY_STRING else fastScrollerMessageFormater.format(timestamp)
    }

    private fun initReLoadListener() {
        GLog.d(logTag, "initReLoadListener")
        timelineViewModel.reloadListener = object : TimelineViewModel.ReloadListener {
            override fun onReloadStart(type: String) {
                onDataReloadStart(type)
            }

            override fun onReloadFinish(type: String) {
                onDataReloadFinish(type)
            }

            override fun onUpdateTimeNodes(
                type: String,
                totalCount: Int,
                timeNodes: List<TimeNode>,
                diffResult: DiffResult?
            ) {
                val presentation = timelineView.getPresentation(type) ?: return
                val isChanged = timelineView.updateTotalCount(presentation, totalCount)
                GLog.d(logTag, DL) {
                    "onUpdateTimeNodes, totalCount=$totalCount, " +
                            "presentation=$presentation, changed=$isChanged"
                }
                timelineView.updateTimeNodes(type, timeNodes, diffResult)

                lifecycleScope.launch(Dispatchers.UI) {
                    // 避免VM数据过来时，Fragment被detach，引发异常
                    context ?: let {
                        GLog.e(logTag, "onUpdateTimeNodes. error state. added=$isAdded, detached=$isDetached")
                        return@launch
                    }
                    GLog.d(logTag, DL) {
                        "onUpdateTimeNodes, launch onTotalCountChanged, " +
                                "isTotalCountChanged=$isChanged"
                    }
                    if (isChanged) {
                        onTotalCountChanged(type, totalCount)
                    }
                    // quick图删除菜单置灰，原图写完后刷新使删除菜单恢复
                    if (timelineView.isSelectionMode) {
                        onSelectItemChange(INVALID_INDEX, selected = false)
                    }

                    onUpdateTimeNodes(type)
                }
            }
        }
    }

    protected open fun onUpdateTimeNodes(type: String): Unit = Unit

    /**
     * 在reload 线程中，非主线程
     */
    protected open fun onDataReloadStart(type: String): Unit = Unit

    /**
     * 在reload 线程中，非主线程
     */
    protected open fun onDataReloadFinish(type: String): Unit = Unit

    override fun onResume() {
        super.onResume()
        timelineView.onResume()

        // 跳转其它页面返回后，保证状态栏和系统导航条的可见性正确
        setupSystemBarVisibility(isKeepSystemBarVisible)

        if (timelineView.isSelectionMode) {
            onSelectItemChange(INVALID_INDEX, selected = false)

            /** 处于选择模式，启用一碰分享，如跳转预览大图后再返回的场景 */
            enableOneTouchShare()
        }
        toolbarSetter?.setListScrollPosition(timelineView.scrollPositionExtend())

        if (supportSynergy) {
            timelineView.synergyTouchInterceptor = synergyTouchInterceptor
        }
    }

    override fun onPause() {
        super.onPause()
        timelineView.onPause()
        timelineView.synergyTouchInterceptor = null
    }

    override fun onStart() {
        super.onStart()
        timelineView.onStart()
    }

    override fun onStop() {
        super.onStop()
        timelineView.onStop()
    }

    override fun enterSelectionMode() =
        if (isTimelineViewInit() && !timelineView.isSelectionMode) {
            timelineViewModel.enterSelectionMode()
        } else {
            SelectionData.INVALID_ID
        }

    override fun exitSelectionMode() {
        if (isTimelineViewInit() && timelineView.isSelectionMode) {
            timelineViewModel.exitSelectionMode()
        }
    }

    override fun isInSelectionMode() = timelineView.isSelectionMode

    override fun onBackPressed(): Boolean = when {
        isTimelineViewInit() && timelineView.isSelectionMode && isResumed -> {
            exitSelectionMode()
            false
        }
        personalFilterViewWrapper.isVisible() -> {
            personalFilterViewWrapper.dismiss()
            false
        }
        else -> true
    }

    override fun onExitCurrentFragment() {
        /* activity 的 onBackPressed 在这个 Fragment 会被选择模式消费，如果在选择模式，只会退出选择模式，而不是退出页面。
        但是 exitCurrentFragment 的目的是退出页面，所以需要在 onBackPressed 前手动退出选择模式。*/
        if (timelineView.isSelectionMode) {
            timelineViewModel.exitSelectionMode()
            /*exitSelectionMode 里通过 postValues 的方式设置 isSelectionMode 为 false，但是耗时，导致 onBackPressed 传递到本界面的时候，
            isSelectionMode 还没变成false，还是被选择模式消费，没有退出界面，需要直接设置为false */
            timelineView.isSelectionMode = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        transitionManager?.release()
        unregisterPositionController()
        timelineViewModel.isPositiveOrder.removeObserver(positiveOrderObserver)
        if (::bottomMenuHelper.isInitialized) {
            bottomMenuHelper.release()
        }
        if (isTimelineViewInit()) {
            timelineView.removeElementAnimationListener(elementAnimationListener)
        }
        personalFilterViewWrapper.removeListener()
        timelineViewModel.registerOnSelectionListener(null)
        timelineViewModel.reloadListener = null
        /** fragment被回收时，关闭一碰分享 ，避免泄漏*/
        disableOneTouchShare()
    }

    fun isUiConfigChanged(config: AppUiResponder.AppUiConfig): Boolean {
        /*
        屏幕旋转不使用 config.orientation.isChanged() 来判断，而是使用宽高变化来判断
        因为如果使用 config.orientation.isChanged() 来判断会出现横屏下进入一个只支持竖屏的界面，强制竖屏后时间轴界面宽高没变只变了横竖屏，
        此时如果执行屏幕切换操作会导致回到时间轴时白屏，因为回到时间轴并不会走onLayout重新布局
        去掉了windowHeight变化的判断，由于浮窗会更改windowHeight，导致时间轴重刷 bugId：1903896
         */
        return config.windowWidth.isChanged() || config.screenMode.isChanged()
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        val isConfigChanged = isUiConfigChanged(uiConfig)
        GLog.d(logTag, "onAppUiStateChanged, isConfigChanged = $isConfigChanged, config = $uiConfig")
        if (isConfigChanged) {
            context ?: return
            val slidingWindows = onCreateSlidingWindows()
            timelineViewModel.updateSlidingWindows(slidingWindows)
            val presentations = onCreatePresentations()
            if (presentations.size != timelineViewModel.windowCount()) {
                throw IllegalStateException("Presentations size should equals to slidingWindows size.")
            }
            timelineView.updatePresentations(presentations, updateLayouter = shouldUpdateLayouter(uiConfig))
            timelineViewModel.updateOrder(isPositiveOrder())
            /*
            概率性onLayout在收到屏幕切换消息之前就执行了，导致屏幕切换后没有执行onLayout，然后就显示白屏
            收到屏幕切换消息时post一个消息去走onLayout逻辑，如果View的onLayout来了就把这个消息移除
            */
            timelineView.postLayoutDelay(POST_LAYOUT_DELAY)
        }
        uiConfig.onWidthChanged {
            if (this::bottomMenuHelper.isInitialized) {
                bottomMenuHelper.updateDialogIfNeed()
            }
        }
    }

    override fun onBottomMenuLayoutChanged() {
        updateBottomMenuDialogGravity()
    }

    private fun updateBottomMenuDialogGravity() {
        if (this::bottomMenuHelper.isInitialized && (bottomMenuBar?.isVisible == true)) {
            bottomMenuHelper.updateDialogIfNeed()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        bottomMenuHelper.handleOperationResult(
            requestCode,
            resultCode,
            data?.apply {
                putExtra(IntentUtils.NAVIGATE_UP_TITLE_TEXT, timelineView.curPresentationType())
            }
        )
    }

    override fun supportClickStatusBar(): Boolean = true

    override fun onStatusBarClicked() {
        timelineView.snapToTop()
    }

    fun isCurPresentationDataEmpty(): Boolean = timelineView.curPresentation().totalCount == 0

    open fun supportMapTitle(): Boolean = true

    protected open fun shouldUpdateLayouter(uiConfig: AppUiResponder.AppUiConfig): Boolean = false

    private fun updateBottomBar(count: Int = Int.MIN_VALUE): SegmentSelectedItem<Path> {
        val cnt = if (count < 0) timelineViewModel.getSelectedItemCount() else count
        val pathSet = timelineViewModel.getSegmentSelectedItems()
        bottomMenuBar?.apply {
            GTrace.trace({ "BaseTimeNodeFragment updateBottomBar cnt:$cnt ${pathSet.getSubSegmentCount()} ${pathSet.getItemCount()}" }) {
                bottomMenuHelper.updateBottomMenu(cnt, null, this)
                pathSet.forEachTriple { item ->
                    bottomMenuHelper.updateBarEnable(item.list, this)
                }
            }

            if (isNeedShowCreateMenuFlag()) {
                bottomMenuHelper.setBottomMenuItemRedDotVisible(
                    BasebizR.id.action_photo_create,
                    isRedDotVisible = false,
                    this
                )
            }
        }
        return pathSet
    }

    @Suppress("LongMethod")
    protected open fun startPhotoPage(position: Int, isPreview: Boolean = false) { // 我的照片/日视图

        // 共享数据
        var sharedId: String? = null
        context?.getAppAbility<IDataAbility>()?.safeUse {
            val snapshot = timelineViewModel.getSnapshot(position, isSupportRevertOrder).also { items ->
                if (items.data.isNotEmpty()) {
                    mediaItem = items.data[0]
                }
            }
            val shareSession = it.share?.createSession()
            shareSession?.uploadPayload(snapshot)
            sharedId = shareSession?.id
        }

        // 跳转大图
        GLog.d(TAG, "timelineView.isSelectionMode:${timelineView.isSelectionMode} isPreview:$isPreview")
        if (timelineView.isSelectionMode && isPreview.not()) {
            GLog.d(TAG, "startPhotoPage. Current is selection mode, skip.")
            return
        }
        GLog.d(logTag, "startPicture: position: $position")
        timelineView.curPresentation().timelineInfo.getActiveData(position)?.let {
            val pathInThumbnailTask = it.timeThumbnail?.item?.path?.toString()
            if (pathInThumbnailTask != it.id) {
                // 出现了缩略图和大图不一致的问题，先加一下日志确定是不是thumbnail错了，后续删掉
                GLog.w(logTag, DL, "startPicture, thumbnail task is wrong! pathInThumbnailTask = $pathInThumbnailTask")
            }
            Bundle().apply {
                if (isPreview) {
                    val selectTypeValue = if (isSelectedMulti) {
                        VALUE_PHOTO_PREVIEW_SELECT_MULTI
                    } else {
                        VALUE_PHOTO_PREVIEW_SELECT_SINGLE
                    }
                    putInt(KEY_PHOTO_TYPE, KEY_PHOTO_TYPE_PREVIEW)
                    putInt(KEY_PHOTO_PREVIEW_SELECT_TYPE, selectTypeValue)
                    putBoolean(KEY_ENABLE_DRAG_OUT, VALUE_ENABLE_DRAG_OUT_DISABLE)
                    putBoolean(KEY_ENABLE_ROTATE, VALUE_ENABLE_ROTATE_DISABLE)
                    putInt(KEY_SELECTION_DATA_ID, timelineViewModel.selectionDataId)
                }

                putString(KEY_MEDIA_ITEM_PATH, it.id)

                val focusSlot = convertToPictureIndex(it.position)
                transitionManager?.updateFocusSlot(focusSlot)
                putInt(KEY_INDEX_HINT, focusSlot)
                putString(KEY_SHARED_ID, sharedId)
                putBoolean(KEY_ENTER_PHOTO_ANIMATE, true)
                putBoolean(KEY_VIEW_REVERT, timelineView.curPresentation().revertLayoutDirection)
                putString(KEY_MEDIA_MODEL_TYPE, timelineViewModel.modelType)
                putString(KEY_MEDIA_SET_PATH, timelineViewModel.modelPath)
                putString(KEY_POSITION_CONTROLLER, positionControllerKey)
                putString(KEY_CURRENT_PAGE, trackAlbumPageInfo)
                getSelectedViewTypes(it.id)?.apply {
                    putString(IntentConstant.PicturePageConstant.KEY_VIEW_TYPE, this)
                }
                getSelectedCrops(it.id)?.apply {
                    putString(IntentConstant.PicturePageConstant.KEY_CROP, this)
                }
                putParcelable(IntentConstant.PicturePageConstant.KEY_OPEN_ANIMATION_RECT, timelineView.getAbsoluteItemRect(position))
                putMoreInfoToPictureBundle(this, position)
                putBoolean(IntentConstant.PicturePageConstant.KEY_STATUSBAR_TINT, currentShouldToolbarTint())
                putBoolean(KEY_IS_SUPPORT_REVERT_ORDER, isSupportRevertOrder)
                startPicture(this@apply, isPreview)
            }
        } ?: GLog.e(logTag, "startPicture, invalid pos, position: $position")
    }

    /**
     * 计算好bundle数据后，最终启动大图页面
     * 这里以fragment的形式启动大图，在某些情况下，可能需要以activity启动大图，重写这个方法即可
     */
    protected open fun startPicture(bundle: Bundle, isPreview: Boolean) {
        GLog.d(TAG, "startPicture: bundle: $bundle, isPreview: $isPreview")

        //如果支持将启动动画提前到点击缩略图，则在启动大图Fragment前启动入场动画
        startEnterTransitionIfNeeded(bundle)

        startByStack<BaseFragment>(
            resId = activity.findFullScreenContainerId(),
            postCard = PostCard(RouterConstants.RouterName.PICTURE_FRAGMENT),
            data = bundle
        )
    }

    /**
     * 如果支持将启动动画提前到点击缩略图，则在启动大图Fragment前启动入场动画
     * @param bundle 启动大图Fragment时传递的bundle信息，入场动画初始化时按需从bundle中获取所需要的数据
     */
    private fun startEnterTransitionIfNeeded(bundle: Bundle) {
        transitionManager?.let {
            val context = context ?: return
            val intent = activity?.intent ?: Intent()
            val thumbnail = getDrawableFromPath(
                context,
                Path.fromString(bundle.getString(KEY_MEDIA_ITEM_PATH))
            ) ?: return
            drawable = thumbnail

            startEnterTransition(
                context,
                PreTransitionStartInfo(intent, bundle, mediaItem, thumbnail),
                onGetPreTransitionConstraintRect,
                it
            )
        }
    }

    /** 用于子类跳转大图时增加传参接口 **/
    protected open fun putMoreInfoToPictureBundle(bundle: Bundle, position: Int) {}

    private fun registerPositionController() {
        ApiDmManager.getMainDM().registerPhotoPageAnimatorController(positionControllerKey, this)
    }

    private fun unregisterPositionController() {
        ApiDmManager.getMainDM().unregisterPhotoPageAnimatorController(positionControllerKey)
    }

    override fun showBottomMenuBar() {
        super.showBottomMenuBar()
        if (this::bottomMenuHelper.isInitialized) {
            bottomMenuHelper.bindMenuView(bottomMenuBar)
        }
    }

    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        bottomMenuHelper.setClickedMenuItemId(menuItem.itemId)
        submitSelection {
            getSelectedItemSpecifiedCount { bundle ->
                val imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                val videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                val menuItemId = menuItem.itemId
                onBottomNavBarItemClicked(menuItemId, imageCount, videoCount)
            }
        }
    }

    @Suppress("LongMethod")
    open fun onBottomNavBarItemClicked(menuItemId: Int, imageCount: Int, videoCount: Int) {
        if (DoubleClickUtils.isFastDoubleClick()) return
        val trackCallerEntry =
            TrackCallerEntry(trackPage, timelineViewModel.modelPath, this.trackAlbumPageInfo)
        when (menuItemId) {
            BasebizR.id.action_share -> {
                val (focusPosition, focusPath) = findSortInFrontSelectedItem()
                val bundle = Bundle().apply {
                    putInt(KEY_SELECTION_DATA_ID, timelineViewModel.selectionDataId)
                    putString(ShareAction.KEY_VIEWDATA_ID, timelineViewModel.modelPath)
                    putString(ShareAction.KEY_MODEL_TYPE, timelineViewModel.modelType)
                }
                bottomMenuHelper.doShareAction(
                    context,
                    bundle,
                    focusPath = focusPath,
                    focusPosition = focusPosition,
                    onCompleteCallback = fun(op: Int) {
                        if (isDetached || isAdded.not() || (context == null)) return
                        when (op) {
                            ShareHelper.STATE_DISMISS_AND_EXIT_EDIT_MODE -> {
                                // 退出选择模式
                                exitSelectionMode()
                            }
                            ShareHelper.STATE_DISMISS_AND_UPDATE_SELECTED_SHOW -> {
                                timelineViewModel.refreshSelectedShow()
                                val count = timelineViewModel.getSelectedItemCount()
                                updateBottomBar(count)
                                updateToolbarSelectedTitle(count)
                                oneTouchShareController?.setOrUpdateSharePaths(timelineViewModel.getSelectedItems())
                            }
                            else -> Unit
                        }
                    },
                    trackCallerEntry = trackCallerEntry,
                )
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_SHARE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                ) {
                    getTrackExtraMap(menuItemId)
                }
            }
            BasebizR.id.action_encrypt -> {
                bottomMenuHelper.doSafeBoxAction(
                    context,
                    this.lifecycle,
                    trackCallerEntry = trackCallerEntry,
                    callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onActionCallback)
                )
                //埋点2006000008：时间轴或图集内等图片列表页进入选择状态后在底部菜单栏点击操作
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_ENCRYPT, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
            }
            BasebizR.id.action_photo_creation -> {
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_CREATE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
                bottomMenuHelper.doPhotoCreationAction(
                    this, bottomMenuBar, getUserActionCurPage(), trackCallerEntry,
                    trackAlbumPageInfo
                )
            }
            BasebizR.id.action_photo_jigsaw -> {
                bottomMenuHelper.doJigsawAction(this, getUserActionCurPage(), trackCallerEntry)
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_COLLAGE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
            }
            BasebizR.id.action_photo_create -> {
                bottomMenuHelper.doCreateAction(
                    this,
                    bottomMenuBar,
                    getUserActionCurPage(),
                    trackCallerEntry,
                    imageCount,
                    videoCount
                )
            }
            BasebizR.id.action_recycle -> {
                bottomMenuHelper.doSelectionRecycleAction(
                    actionId = BottomMenuHelper.ACTION_RECYCLE,
                    trackCallerEntry = trackCallerEntry,
                    isFromTimeline = isTimelineAlbum(),
                    callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onActionCallback)
                )
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_DELETE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                ) {
                    getTrackExtraMap(menuItemId)
                }
            }
            BasebizR.id.action_move_to -> {
                timelineViewModel.getCurrentModelPath()?.let {
                    bottomMenuHelper.doAppendToAction(
                        this, it, trackCallerEntry = trackCallerEntry,
                        // 用于时间轴发送埋点2006001002，为什么不在回调里埋点？因为新建图集的取消新建事件回调不回来
                        isFromTimeline = isTimelineAlbum(),
                        callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onActionCallback)
                    )
                } ?: GLog.e(logTag, "onBottomNavBarItemClicked, action_append_to path = null")
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_ADD_TO, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
            }
            BasebizR.id.action_more -> {
                val moreItemList = ArrayList<MenuListPopupItem>()
                moreItemList.add(MenuListPopupItem(BasebizR.string.base_export_video, isEnabled = true))
                moreItemList.add(MenuListPopupItem(BasebizR.string.base_move_to_gallery))
                moreItemList.add(MenuListPopupItem(BasebizR.string.base_copy_to))
                bottomMenuHelper.doMoreMenuAction(
                    this,
                    bottomMenuBar,
                    timelineView.timelineViewModel.getCurrentModelPath(),
                    BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onActionCallback),
                    trackCallerEntry,
                    BasebizR.id.action_more,
                    moreItemList
                )
            }
        }
    }

    /**
     * 在选中的items中，找到排在最前面的item，并返回其位置和路径
     */
    private fun findSortInFrontSelectedItem(): Pair<Int, Path?> {
        val selectedItems = timelineViewModel.getSelectedItems()
        var focusPath: Path? = null
        var focusPosition = Int.MAX_VALUE
        for (item in selectedItems) {
            val position = timelineViewModel.getItemIndexByPath(timelineView.curPresentation().type, item.toString())
            if (position < focusPosition) {
                focusPosition = position
                focusPath = item
            }
        }

        if (timelineViewModel.isPositiveOrder.value == true) {
            focusPosition = timelineView.curPresentation().totalCount - focusPosition
        }
        return Pair(focusPosition, focusPath)
    }

    /**
     * 获取所有选中的item的ViewType集合，转成string
     */
    protected open fun getSelectedViewTypes(path: String): String? = null

    /**
     * 获取所有选中的item的Crop集合，转成string
     */
    protected open fun getSelectedCrops(path: String): String? = null

    /**
     * 获取额外的埋点信息
     */
    protected open fun getTrackExtraMap(menuItemId: Int): Map<String, String>? = null

    protected open fun onActionCallback(path: List<Path>?, state: Int) {
        GLog.d(logTag, "onActionCallback, state: $state")
        timelineViewModel.needSkipRefreshData = (state == BottomMenuHelper.ACTION_STATE_START)
    }

    protected fun submitSelection(completedHandle: (Set<Path>) -> Unit): Unit = timelineViewModel.submitSelection(completedHandle)

    protected fun getSelectedItemSpecifiedCount(getSpecifiedCount: (Bundle) -> Unit) {
        timelineViewModel.getSelectedItemImageAndVideoCount(getSpecifiedCount)
    }

    protected open fun updateToolbarSelectedTitle(count: Int = Int.MIN_VALUE) {
        if (timelineViewModel.isSelectionMode.value != true) {
            GLog.w(TAG, "updateToolbarSelectedTitle return isSelectionMode.vale:${timelineViewModel.isSelectionMode.value}")
            return
        }
        toolbarSetter?.setTitle(getToolbarSelectedTitle(count))
    }

    /**
     * 获取选择模式标题栏文案
     * @param count 当前选中的照片数量
     */
    protected fun getToolbarSelectedTitle(count: Int? = null): String {
        val cnt = if ((count == null) || (count < 0)) timelineViewModel.getSelectedItemCount() else count
        val title = when (cnt) {
            0 -> resources.getString(BasebizR.string.base_title_select_image)
            else -> resources.getQuantityString(BasebizR.plurals.base_title_has_select, cnt, cnt)
        }
        return title
    }

    private fun isTimelineAlbum() =
        trackAlbumPageInfo == LaunchExitPopupConstant.Value.CUR_PAGE_TIMELINE_PAGE

    open fun getDefaultPresentationType(): String? = null
    open fun restoreFragmentState(savedInstanceState: Bundle) {}

    private fun showPermissionAlertDialog(context: Context, data: Bundle) {
        ConfirmDialog.Builder(context)
            .setPositiveButton(AuthorizingR.string.authorizing_option_allow) { _, _ ->
                NetworkPermissionManager.openNetwork(context)
                onPermissionDialogOKClick()
                if (GProperty.DEBUG_DISABLE_MAP.not()) {
                    Starter.ActivityStarter(context, data, PostCard(RouterConstants.RouterName.MAP_ACTIVITY)).start()
                }
            }
            .setNegativeButton(AuthorizingR.string.authorizing_option_refuse) { _, _ ->
                onPermissionDialogCancelClick()
            }
            .setMessage(getString(AuthorizingR.string.authorizing_request_network_gallery_map))
            .setTitle(AuthorizingR.string.authorizing_request_network_title)
            .setCancelable(false)
            .build()
            .show()
    }

    open fun onPermissionDialogOKClick() {}
    open fun onPermissionDialogCancelClick() {}

    private fun fastScrollerScrollRange() = fastScroller?.let {
        timelineView.height - it.marginBottom - it.marginTop - it.height
    } ?: 0

    override fun onElementClick(nodeIndex: Int, itemIndex: Int, elementType: String, extra: Bundle) {
        GLog.d(TAG, DL, "onElementClick nodeIndex $nodeIndex, itemIndex $itemIndex, " +
                "elementType $elementType, extra: $extra")
        when (elementType) {
            ELEMENT_TYPE_SLOT_ITEM_EXPAND,
            ELEMENT_TYPE_SLOT_ITEM -> startPhotoPage(itemIndex)
            ELEMENT_TYPE_LOCATION_TITLE -> {
                /*val timeRange = extra.getString(CLICK_EXTRA_KEY_TIME_RANGE)
                val location = extra.getString(CLICK_EXTRA_KEY_LOCATION)
                dispatchLocationClick(nodeIndex, timeRange, location)*/
            }
            ELEMENT_TYPE_NODE_CHECKBOX -> dispatchNodeCheckBoxClick(nodeIndex)
            ELEMENT_TYPE_PREVIEW_PICTURE -> startPhotoPage(itemIndex, true)
            ELEMENT_TYPE_PREVIEW_CHECKBOX -> timelineViewModel.toggleItemSelection(itemIndex)
            else -> onElementClick(nodeIndex, itemIndex, ELEMENT_TYPE_SLOT_ITEM, extra)
        }
    }

    private fun dispatchNodeCheckBoxClick(nodeIndex: Int) {
        GLog.d(logTag, "dispatchNodeCheckBoxClick. nodeIndex=$nodeIndex")

        val isNodeSelected = timelineView.curPresentation().timelineInfo.getTimeNode(nodeIndex)?.let {
            it.extraInfo.getBoolean(EXTRA_KEY_SELECTION)
        } ?: false

        if (!isNodeSelected) {
            timelineViewModel.selectAllInNode(nodeIndex, timelineView.curPresentation().layouter.itemRangeOfNode(nodeIndex))
        } else {
            timelineViewModel.unselectAllInNode(nodeIndex, timelineView.curPresentation().layouter.itemRangeOfNode(nodeIndex))
        }
    }

    private fun dispatchLocationClick(nodeIndex: Int, timeRange: String?, location: String?) {
        val isMapPageSupport = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_MAP_PAGE)
        if (!isMapPageSupport) {
            GLog.w(TAG) { "dispatchLocationClick not support map page!" }
            return
        }
        val regionCn = ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN)
        if (regionCn && !OSVersionUtils.isAtLeast(OPLUS_OS_12_1)) {
            GLog.w(TAG, DL) { "dispatchLocationClick, domestic and below OS12.1, not allow into Map" }
            return
        }
        activity?.let {
            if (!NetworkMonitor.isNetworkValidated()) {
                Toast.makeText(
                    it, BasebizR.string.main_network_not_contected_msg,
                    Toast.LENGTH_SHORT
                ).show()
                return
            }
            val path = SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(timeRange)
            /**这里将输入的时间轴的输入抹掉，用全部时间，进入MapFragment之后加载全部时间段的数据
            val path = SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(SEGMENT_ALL)*/
            val mapLocationAlbum: MapLocationAlbum =
                DataManager.getMediaObject(path) as MapLocationAlbum
            val count = mapLocationAlbum.count
            mapLocationAlbum.setOrder(mapLocationAlbum.getDefaultOrder(isPositiveOrder()))
            val data = Bundle()
            data.putString(
                IntentConstant.AlbumConstant.KEY_MEDIA_PATH,
                mapLocationAlbum.path.toString()
            )
            data.putString(MAP_PAGE_TITLE, getString(BasebizR.string.main_map))
            //设置入口类型
            data.putBoolean(IntentConstant.AlbumConstant.KEY_FROM_TIME_LINE, true)
            //设置进入mapView时的默认缩放比例
            data.putFloat(IntentConstant.AlbumConstant.KEY_MAP_DEFAULT_ZOOM, MapConstants.ZOOM_LEVEL_5KM)
            /**这里是模拟从详情页点击进入MapView页面
            simulateIntoMapFromDetailPage(nodeIndex, data)*/
            val authorizingAbility: IPrivacyAuthorizingAbility? = activity?.getAppAbility()
            authorizingAbility?.use { ability ->
                if (ability.isPrivacyAuthorized(AUTHORIZE_GALLERY_MAP) != true) {
                    showGalleryMapAuthorizingDialog(it, data)
                    return
                }
            }
            if (!NetworkPermissionManager.isUseOpenNetwork) {
                showPermissionAlertDialog(it, data)
                return
            }
            if (GProperty.DEBUG_DISABLE_MAP.not()) {
                GLog.d(TAG, DL, "dispatchLocationClick timeRange $timeRange, path $path, bundle path ${mapLocationAlbum.path}")
                // 2006002027 埋点，这里是模拟从详情页点击进入MapView页面，修改埋点类型
                AlbumsActionTrackHelper.trackAndSendEnterMapPage(ALBUM_ACTION_ENTER_MAP_FROM_TIME_LINE_VALUE)
                Starter.ActivityStarter(it, data, PostCard(RouterConstants.RouterName.MAP_ACTIVITY)).start()
            }
        }
    }

    private fun simulateIntoMapFromDetailPage(nodeIndex: Int, data: Bundle) {
        val timeNode = timelineView.curPresentation().timelineInfo.getTimeNode(nodeIndex)
        if (timeNode != null) {
            val itemRangeStart = timeNode.itemRange.first
            val itemRangeEnd = timeNode.itemRange.last
            var timeViewData: TimeViewData? = null
            GLog.d(TAG, DL, "dispatchLocationClick nodeIndex: $nodeIndex, timeNode $timeNode")
            for (itemIndex in itemRangeStart..itemRangeEnd) {
                timeViewData = timelineView.curPresentation().timelineInfo.getActiveData(itemIndex)
                if (timeViewData != null) {
                    val isImage = timeViewData.mediaType == FileConstants.MediaType.MEDIA_TYPE_IMAGE
                    if (isImage) {
                        break
                    }
                }
            }
            GLog.d(TAG, DL, "dispatchLocationClick nodeIndex: $nodeIndex, timeViewData $timeViewData")
            if (timeViewData != null) {
                data.putBoolean(IntentConstant.AlbumConstant.KEY_FROM_IMAGE_DETAIL, true)
                data.putString(IntentConstant.AlbumConstant.KEY_IMAGE_DETAIL_PATH, timeViewData.id)
                data.putFloat(IntentConstant.AlbumConstant.KEY_MAP_DEFAULT_ZOOM, MapConstants.ZOOM_LEVEL_5KM)
            }
        }
    }

    private fun showGalleryMapAuthorizingDialog(activity: Activity, data: Bundle) {
        PermissionDialogHelper.showGalleryMapPrivacyDialog(
            activity,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    activity.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
                        it.authorizePrivacy(AUTHORIZE_GALLERY_MAP)
                    }
                    NetworkPermissionManager.openNetwork(activity)
                    if (GProperty.DEBUG_DISABLE_MAP.not()) {
                        Starter.ActivityStarter(activity, data, PostCard(RouterConstants.RouterName.MAP_ACTIVITY)).start()
                    }
                }
            })
    }

    /**
     * Item长按事件
     */
    override fun onItemLongClick(index: Int): Boolean {
        if (
            !timelineView.isSelectionMode ||
            !timelineViewModel.isItemSelected(index)
        ) {
            GLog.d(logTag, "onItemLongClick, Does not meet the drag sharing conditions!")
            return false
        }
        val isSynergyEnable = ApiDmManager.getSynergyDM().isSynergyEnable()
        // 当处于选中状态并且手机处于互联连接情况下，返回
        if (isSynergyEnable) {
            return false
        }
        val selectedCount = timelineViewModel.getSelectedItemCount()
        if (!DragHelper.checkMediaItemLimit(activity, selectedCount)) {
            GLog.d(logTag, "onItemLongClick, Exceeded the limit of number of shares!")
            return true
        }
        context?.let {
            VibratorUtils.vibrate(it, VibratorUtils.EffectType.VIBRATE_TYPE_WEAK)
        }
        AppScope.launch(Dispatchers.IO) {
            val dragData = DragData().apply {
                pathList = timelineViewModel.getSelectedItems().toList()
                bitmapList = timelineViewModel.loadSelectedBitmap(index)
            }
            DragHelper.downloadOriginalPhoto(activity, <EMAIL>, dragData.pathList) {
                /**
                 * 浮窗，分屏获取和图集详情四列的时候的item一样的高度（相当于固定高），否则是slot的高度
                 */
                context?.let {
                    val dragItemWidth = if (isFloatingWindowMode() || getCurrentAppUiConfig().isInMultiWindow.current) {
                        DragHelper.getDragItemDefaultWidth(it)
                    } else {
                        timelineView.getSlotWidth(timelineViewModel.getCurrentWindowType())
                    }
                    DragOptions.isSupportDropToGallery = false
                    if (supportDragAnimation()) {
                        activity?.let { activity ->
                            dragDropAnimationHelper = DragDropAnimationHelper(
                                activity,
                                dragData,
                                dragItemWidth,
                                getRectByIndexCallback
                            )
                            dragDropAnimationHelper?.isSupportDropAnimation = true
                        }
                    }
                    activity?.window?.let { window ->
                        DragOptions.startMediaDrag(window, isFloatingWindowMode(), dragData, dragItemWidth, this@BaseTimeNodeFragment)
                    }
                }
            }
        }
        return true
    }

    /**
     * 启用一碰分享
     */
    private fun enableOneTouchShare() {
        /** 页面或系统不支持则不激活一碰分享 */
        if (supportOneTouchShare.not() || ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE).not()) {
            GLog.w(logTag, "[enableOneTouchShare] Failed : OneTouchShare is not supported!")
            return
        }

        if (oneTouchShareController == null) {
            oneTouchShareController = OneTouchShareController(activity = activity, lifecycleOwner = this, workSession = session)
            oneTouchShareController?.enable()
        }

        GLog.d(logTag, "[enableOneTouchShare] registerShareEvent : selectedItems = ${timelineViewModel.getSelectedItems()} ")
        oneTouchShareController?.setOrUpdateSharePaths(timelineViewModel.getSelectedItems())
    }

    /**
     * 关闭一碰分享
     */
    private fun disableOneTouchShare() {
        oneTouchShareController?.disable()
        oneTouchShareController = null
    }

    /**
     * 选择
     * SelectionModel.OnSelectionListener implementation
     */
    protected open fun onEnterSelectionMode() {
        if (context == null) {
            GLog.e(logTag, "onEnterSelectionMode. $this not attached to a context.")
            return
        }
        GLog.d(logTag, "onEnterSelectionMode.")

        /** 进入选择模式时，启用一碰分享 */
        enableOneTouchShare()

        timelineView.isSelectionMode = true
        showBottomMenuBar()
        updateBottomBar()
        timelineView.setMaskVisible(true)
    }

    protected open fun onExitSelectionMode() {
        if (context == null) {
            GLog.e(logTag, "onExitSelectionMode. $this not attached to a context.")
            return
        }
        GLog.d(logTag, "onExitSelectionMode.")

        /** 退出选择模式时，关闭一碰分享 */
        disableOneTouchShare()

        timelineView.isSelectionMode = false
        updateBottomBar(0)
        hideBottomMenuBar()
        timelineView.setMaskVisible(false)
    }

    private val handler by lazy { Handler(Looper.getMainLooper()) }
    private val updateFragmentBarTask by lazy {
        Runnable {
            if (context == null) {
                GLog.e(logTag, DL, "onSelectionChange. Fragment $this not attached to a context.")
            } else {
                val count = timelineViewModel.getSelectedItemCount()
                val pathSet = updateBottomBar(count)

                /** 选择发生变时，更新NFC事件注册 */
                GLog.d(logTag, "[onSelectItemChange] count = $count")
                oneTouchShareController?.setOrUpdateSharePaths(pathSet)
            }
        }
    }

    override fun onSelectItemChange(position: Int, selected: Boolean) {
        if (context == null) {
            GLog.e(logTag,  DL, "onSelectItemChange: Fragment $this not attached to a context.")
        } else {
            updateToolbarSelectedTitle(timelineViewModel.getSelectedItemCount())
        }

        handler.removeCallbacks(updateFragmentBarTask)
        handler.postDelayed(updateFragmentBarTask, UPDATE_FRAGMENT_BAR_TASK_DELAY)
    }

    override fun onSelectItemsChange(positions: Set<Int>, selected: Boolean) {
        if (context == null) {
            GLog.e(logTag, DL, "onSelectItemsChange: Fragment $this not attached to a context.")
        } else {
            updateToolbarSelectedTitle(timelineViewModel.getSelectedItemCount())
        }

        handler.removeCallbacks(updateFragmentBarTask)
        handler.postDelayed(updateFragmentBarTask, UPDATE_FRAGMENT_BAR_TASK_DELAY)
    }

    override fun onSelectionChange(item: Path?, state: NotifyState) {
        if (item == null) {
            onSelectItemChange(INVALID_INDEX, selected = false)
        }
    }

    /**
     * 浮窗拖拽
     * OnDragEventListener implementation
     */
    override fun onDragStart(dragStartX: Float, dragStartY: Float) {
        if (this.isDetached) {
            GLog.w(TAG, "onDragStart fragment isDetached")
            return
        }
        receiveDrop = false
        dragDropAnimationHelper?.takeIf { supportDragAnimation() && it.isSupportDropAnimation }?.apply {
            lifecycleScope.launch(Dispatchers.IO) {
                val selectAllBitmapList = timelineViewModel.loadAllSelectedBitmapFromCache(dragData.bitmapList).map {
                    Pair(timelineViewModel.getItemIndexByPath(timelineView.curPresentation().type, it.first), it)
                }
                withContext(Dispatchers.UI) {
                    setDragSourceList(selectAllBitmapList)
                    startTogetherAnim(dragStartX, dragStartY)
                }
            }
        }
    }

    override fun onDragEnd(result: Boolean, dragStartX: Float, dragStartY: Float) {
        if (this.isDetached || (activity?.isDestroyed != false) || (activity?.isFinishing != false)) {
            GLog.w(TAG, "onDragEnd fragment isDetached")
            return
        }
        GLog.d(logTag, "onDragEnd. $result")
        // 当前页面没有收到drop并且结果为false，提示拖动到的页面不支持
        DragOptions.clearDragListener(activity?.window)
        if (!receiveDrop && !result) {
            ToastUtil.showShortToast(BasebizR.string.drag_currentpage_not_support)
        }
        DragOptions.isSupportDropToGallery = true
        if (supportDragAnimation()) {
            dragDropAnimationHelper?.isSupportDropAnimation = false
        }
        val timelineVmRef = WeakReference(timelineViewModel)
        TrackScope.launch {
            val bundle = timelineVmRef.get()?.getSelectedItemImageAndVideoCountAsync()
            FloatingWindowTrackHelper.trackDragShare(
                bundle?.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT),
                bundle?.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT),
                FloatingWindowTrackConstant.Value.DRAG_PAGE_LIST_PAGE,
                FloatingWindowTrackConstant.Value.DRAG_SOURCE_TIMELINE,
                if (result) FloatingWindowTrackConstant.Value.SHARE_RESULT_SUCCESS else FloatingWindowTrackConstant.Value.SHARE_RESULT_FAILED
            )
        }
    }

    override fun onDrop(dragStartX: Float, dragStartY: Float) {
        if (this.isDetached) {
            GLog.w(TAG, "onDrop fragment isDetached")
            return
        }
        receiveDrop = true
        GLog.d(logTag, "onDrop.dragStartX= $dragStartX,dragStartY=$dragStartY")
        dragDropAnimationHelper?.takeIf {
            supportDragAnimation() && it.isSupportDropAnimation
        }?.apply { startTogetherReverseAnim(dragStartX, dragStartY) }
    }

    override fun onDragLocation(locationX: Float, locationY: Float) {
        if (this.isDetached) {
            GLog.w(TAG, "onDragLocation fragment isDetached")
            return
        }
        dragDropAnimationHelper?.takeIf { supportDragAnimation() && it.isSupportDropAnimation }?.apply { setDragLocation(locationX, locationY) }
    }

    override fun onDragExit() {
        dragDropAnimationHelper?.stopAnim()
    }

    private fun supportDragAnimation(): Boolean {
        return DragHelper.supportDragAnimation(isFloatingWindowMode())
    }

    private val getRectByIndexCallback: (Context, List<Int>) -> List<Rect> = { context, indexList ->
        val resultList = ArrayList<Rect>()
        val needAdjustRect = !ResourceUtils.isRTL(context) && sidePaneAccessor.isOpen()
        for (i in indexList.indices) {
            val rect = Rect()
            timelineView.curPresentation().layouter.rectOfVisibleItem(indexList[i], rect, true)
            if (needAdjustRect) {
                /** 时间轴的rect的left实际坐标为left+侧边栏宽度 */
                rect.apply {
                    val width = width()
                    left += sidePaneAccessor.getSlideWidth()
                    right = left + width
                }
            }

            /**时间轴头部出现 ”开启相册自动同步“的提示时，布局向下移动的偏移量，拿到偏移量更新图片坐标*/
            val offSet = timelineView.scrollY
            if (offSet != 0) {
                rect.apply {
                    top += abs(offSet)
                    bottom += abs(offSet)
                }
            }

            /** rectOfVisibleItem 获取的可见部分还是超出了屏幕，这和图集不一致了，这里修改为超过屏幕rect设为非法，不做动画 */
            dragDropAnimationHelper?.animationViewParent?.apply {
                val contentViewRect = Rect()
                getGlobalVisibleRect(contentViewRect)
                if ((rect.bottom < contentViewRect.top) || (rect.top > contentViewRect.bottom)) {
                    rect.set(0, 0, 0, 0)
                }
            }

            resultList.add(rect)
        }
        resultList
    }

    private fun convertToTimelineIndex(pictureIndex: Int): Int {
        timelineView.curPresentation().apply {
            return if (revertLayoutDirection) {
                totalCount - pictureIndex - 1
            } else {
                pictureIndex
            }
        }
    }

    private fun convertToPictureIndex(timelineIndex: Int): Int {
        timelineView.curPresentation().apply {
            return if (revertLayoutDirection) {
                totalCount - timelineIndex - 1
            } else {
                timelineIndex
            }
        }
    }

    /**
     * 大图下拉,找到时间轴上实际的位置
     * IPicturePositionController implementation
     */
    override fun getItemRect(index: Int, ignoreInvisibility: Boolean): Rect {
        if (index != INVALID_INDEX) {
            val timelineIndex = convertToTimelineIndex(index)
            GLog.d(logTag, "getItemRect. index=$index, timelineIndex=$timelineIndex")
            timelineView.scrollItemToVisibleRange(timelineIndex)
            return timelineView.getAbsoluteItemRect(timelineIndex)
        }
        return Rect()
    }

    override fun getItemIndex(path: Path?): Int {
        return INVALID_INDEX
    }

    /**
     * 大图滑动时，找到实际的时间轴index通知时间轴滚动
     */
    override fun onPictureChanged(index: Int) {
        transitionManager?.updateFocusSlot(index)
        val timelineIndex = convertToTimelineIndex(index)
        GLog.d(logTag, "onPictureChanged. index=$index, timelineIndex=$timelineIndex")
        if (timelineIndex != INVALID_INDEX) {
            timelineView.scrollItemToVisibleRange(timelineIndex)
        }
    }

    /**
     * 开始大图下拉时，找到实际的时间轴index通知时间轴滚动
     */
    override fun onPullDownStart(index: Int) {
        // 下拉时刷新一次时间轴可见区域
        val timelineIndex = convertToTimelineIndex(index)
        GLog.d(logTag, "onPullDownStart. index=$index, timelineIndex=$timelineIndex")
        timelineView.setPlaceHolder(PlaceHolder(timelineIndex))
        timelineView.updateHolders()
    }

    override fun onPullDownFinish(index: Int, complete: Boolean) {
        GLog.d(logTag, "onPullDownFinish. index=$index, complete=$complete")
        timelineView.setPlaceHolder(null)
    }

    /**
     * 设置系统栏可见性。
     * @param isVisible 系统栏的可见性
     */
    private fun setupSystemBarVisibility(isVisible: Boolean) {
        if (isVisible) {
            showStatusBar()
            showNaviBar()
        } else {
            hideStatusBar()
            hideNaviBar()
        }
    }

    protected fun isTimelineViewInit(): Boolean {
        return this::timelineView.isInitialized
    }

    /**
     * 滚动到最新内容区域
     */
    protected fun scrollToLatestContent() {
        if (isTimelineViewInit().not()) {
            GLog.d(TAG, DL) { "scrollToLatestContent. timelineView is not initialized" }
            return
        }
        if (isPositiveOrder()) {
            timelineView.snapToBottom()
        } else {
            timelineView.snapToTop()
        }
    }

    override fun createSidePaneListener(): ISidePaneListener = this

    override fun isSidePaneEnabled(): Boolean = sidePaneAccessor.isSupportSidePane()

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        super.onSystemBarChanged(windowInsets)
        /* 虚拟按键状态下，contentView会添加底部padding，菜单控件无需处理
        非虚拟按键有导航条情况下，contentView底部padding会被设为0，NavigationView会跟底部导航条重叠，为避免重叠将NavigationView的marginBottom的高度设为导航条的高度
        原来是使用的padding，padding是在自身限定高度做内部间距调整，设计是希望菜单栏在导航条之上不与导航条重叠，改为margin*/
        bottomMenuBar?.updateMargin(bottom = getCurrentGestureBarHeight())
    }

    protected fun isPersonalFiltered(type: String = timelineView.curPresentationType()): Boolean {
        return isSupportedPersonalFilter && timelineViewModel.isPersonalFiltered(type)
    }

    override fun getEmptyPageIconAnimRes(): Int {
        return if (isPersonalFiltered()) {
            BasebizR.raw.base_empty_view_search_result
        } else {
            super.getEmptyPageIconAnimRes()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        cancelButton = null
        searchMenuItem = null
        filterMenuItem = null
        createMemoryMenuItem = null
    }

    override fun onCreateTintElementsIfNeed(
        lightNightDriver: IProgressChanger<Boolean>,
        directionAnimDriver: IProgressChanger<Boolean>,
        elementList: MutableList<DirectionAnimationElement>
    ) {
        super.onCreateTintElementsIfNeed(lightNightDriver, directionAnimDriver, elementList)
        val context = context ?: return
        onCreateTintElementsIfNeedImpl(context, lightNightDriver, elementList)
    }

    protected fun onCreateTintElementsIfNeedImpl(
        context: Context,
        changer: IProgressChanger<Boolean>,
        elementList: MutableList<DirectionAnimationElement>,
        isEarlyFinishAnimation: Boolean = false
    ) {
        val owner = this
        activity?.let {
            elementList.add(TintStatusElement(it, changer, isEarlyFinishAnimation).apply { register(owner) })
            if (isInSelectionMode().not()) {
                elementList.add(TintBackElement(context, it, changer, isEarlyFinishAnimation).apply { register(owner) })
            }
        }
        toolbar?.let {
            elementList.add(TintTitleElement(it, changer, isEarlyFinishAnimation).apply { register(owner) })
            elementList.add(TintSubtitleElement(context, it, changer, isEarlyFinishAnimation).apply { register(owner) })
            if (isInSelectionMode().not()) {
                it.overflowIcon?.let { icon ->
                    elementList.add(TintDrawableAnimationElement(icon, changer, isEarlyFinishAnimation).apply { register(owner) })
                }
            }
        }
        cancelButton?.takeIf { it.isVisible }?.let {
            elementList.add(TintMenuButtonDrawableElement(context, it, changer) { currentShouldToolbarTint() }.apply { register(owner) })
            elementList.add(TintMenuButtonTextElement(context, it, changer) { currentShouldToolbarTint() }.apply { register(owner) })
        }
        createMemoryMenuItem?.takeIf { it.isVisible }?.icon?.let {
            elementList.add(TintDrawableAnimationElement(it, changer, isEarlyFinishAnimation).apply { register(owner) })
        }
        topTranslucentView?.let {
            val showTranslucent = currentShouldToolbarTint()
            val isNeedAnim = (currentShowTopTranslucent != showTranslucent)
            currentShowTopTranslucent = showTranslucent
            elementList.add(
                object : TintTranslucentElement(
                    translucentView = it,
                    changer = changer,
                    isNeedAnimation = isNeedAnim,
                    shouldTranslucentVisible = showTranslucent
                ) {
                    override fun updateAlpha(progress: Float) {
                        // 顶部蒙层 View 可能带有模糊效果，不能通过 View.setAlpha 改变透明度，否则会导致模糊视图变黑
                        topTranslucentView?.background?.alpha = (progress * ALPHA_255.toFloat()).toInt()
                    }
                }.apply {
                    register(owner)
                }
            )
        }
        sidePaneAccessor?.takeIf { it.getSidePaneState().isOpen().not() }?.let {
            elementList.add(FilterPanelTintExpandElement(context, it, changer).apply { register(owner) })
        }
        onCreateAdditionalTintElementsIfNeed(context, changer, elementList, isEarlyFinishAnimation)
    }

    protected open fun onCreateAdditionalTintElementsIfNeed(
        context: Context,
        changer: IProgressChanger<Boolean>,
        elementList: MutableList<DirectionAnimationElement>,
        isEarlyFinishAnimation: Boolean = false
    ) {
        val owner = this
        searchMenuItem?.takeIf { it.isVisible && (it.icon != null) }?.icon?.let {
            elementList.add(object : TintDrawableAnimationElement(it, changer, isEarlyFinishAnimation) {
                override val startColor: Int = tintColorWhite
                override val endColor: Int = context.getColor(com.support.appcompat.R.color.coui_color_primary_neutral)
            }.apply { register(owner) })
        }
        filterMenuItem?.takeIf { it.isVisible }?.let {
            val toolbar = toolbar ?: return
            elementList.add(TintFilterElement(context, it, toolbar, changer, isEarlyFinishAnimation) {
                isPersonalFiltered() }.apply { register(owner) })
        }
    }

    private fun updatePresentations() {
        // 调用地方有可能是子线程，抛到主线程执行视图切换
        runOnUiThread post@{
            val slidingWindows = onCreateSlidingWindows()
            timelineViewModel.updateSlidingWindows(slidingWindows)
            val presentations = onCreatePresentations()
            if (presentations.size != timelineViewModel.windowCount()) {
                GLog.w(TAG, DL) { "Presentations size should equals to slidingWindows size." }
                return@post
            }
            timelineView.updatePresentations(presentations, true)
            if (timelineView.curPresentation().isForeground) {
                refreshTimelineViewPadding()
            }
            timelineView.layoutRunnable.run()
        }
    }

    private fun loadImmersivePresentationConfig() {
        isImmersivePresentation = TimelinePreLoader.isImmersivePresentation.get() ?: timelineViewModel.loadImmersivePresentationConfig()
    }

    /**
     * 更新时间轴padding，不同业务可以设置不同的padding
     */
    open fun refreshTimelineViewPadding(): Unit = Unit

    companion object {
        private const val TAG = "BaseTimeNodeFragment"
        const val INVALID_INDEX = -1
        const val POST_LAYOUT_DELAY = 500L
        // 单位：毫秒
        private const val UPDATE_FRAGMENT_BAR_TASK_DELAY = 200L

        const val MAP_PAGE_TITLE = "map_page_title"

        private const val ALPHA_255 = 255
    }
}