/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MenuRequestCode.kt
 ** Description: 大图执行菜单操作的请求码封装
 ** Version: 1.0
 ** Date : 2022/10/26
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG: MenuRequestCode
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xie<PERSON><PERSON>e@Apps.Gallery3D      2022/10/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation

import androidx.annotation.IntDef

/**
 * 大图执行具体菜单操作对应的请求码,此请求码用于给编辑页或其他页面的activity保存,
 * 当activity退出时作为结果返回,返回后大图会根据此requestCode匹配对应的PhotoMenuActionRule,
 * 以作相应的处理
 */
object MenuRequestCode {

    /**
     * 作为请求码的key使用
     */
    const val KEY_REQUEST_CODE = "request_code"

    /**
     * 点击"T"图标进入超级文本1.0编辑的请求码
     */
    const val PHOTO_EDIT_ENHANCE_TEXT = 1

    /**
     * 点击识文图标进入超级文本2.0编辑的请求码
     */
    const val PHOTO_EDIT_SUPER_TEXT = PHOTO_EDIT_ENHANCE_TEXT + 1

    /**
     * 进入图片编辑的默认的请求码
     */
    const val PHOTO_EDIT_DEFAULT = PHOTO_EDIT_SUPER_TEXT + 1

    /**
     * 进入连拍图集的请求码
     */
    const val RELEASE_CSHOT = PHOTO_EDIT_DEFAULT + 1

    /**
     * 进入视频编辑的请求码
     */
    const val VIDEO_EDIT = RELEASE_CSHOT + 1

    /**
     * 进入人像景深编辑的请求码
     */
    const val PHOTO_EDIT_PORTRAIT_BLUR = VIDEO_EDIT + 1

    /**
     * 进入GIF合成页的请求码
     */
    const val GIF_SYNTHESIS_REQUEST_CODE = PHOTO_EDIT_PORTRAIT_BLUR + 1

    /**
     * gif合成页进入解锁连拍编辑页请求码
     */
    const val GIF_CSHOT_EDITOR_REQUEST_CODE = GIF_SYNTHESIS_REQUEST_CODE + 1

    /**
     * 进入隐私水印编辑的请求码
     */
    const val PHOTO_EDIT_PRIVACY_WATERMARK = GIF_CSHOT_EDITOR_REQUEST_CODE + 1

    /**
     * 查看抠图保存结果，拉起新大图的请求码
     */
    const val LNS_VIEW_SAVED_RESULT_REQUEST_CODE = PHOTO_EDIT_PRIVACY_WATERMARK + 1

    /**
     * 请求进入合影优化编辑页的请求码
     */
    const val GROUP_PHOTO_REQUEST_CODE = LNS_VIEW_SAVED_RESULT_REQUEST_CODE + 1

    /**
     * 进入贴图编辑的请求码
     */
    const val DROP_PICTURE_REQUEST_CODE = GROUP_PHOTO_REQUEST_CODE + 1

    /**
     * 请求进入AI画质增强编辑页的请求码
     */
    const val PHOTO_EDIT_IMAGE_QUALITY_ENHANCE = DROP_PICTURE_REQUEST_CODE + 1

    /**
     * 请求进入AI修复页的请求码
     */
    const val AI_REPAIR_REQUEST_CODE = PHOTO_EDIT_IMAGE_QUALITY_ENHANCE + 1

    /**
     * 请求进入AI消除页的请求码
     */
    const val AI_ELIMINATE_REQUEST_CODE = AI_REPAIR_REQUEST_CODE + 1

    /**
     * 请求进入选择图片页的请求码
     */
    const val PICK_PHOTO_REQUEST_CODE = AI_ELIMINATE_REQUEST_CODE + 1

    /**
     * 请求进入拍摄图片页的请求码
     */
    const val TAKE_PHOTO_REQUEST_CODE = PICK_PHOTO_REQUEST_CODE + 1

    /**
     * 请求进入裁剪图片页的请求码
     */
    const val CROP_PHOTO_REQUEST_CODE = TAKE_PHOTO_REQUEST_CODE + 1

    /**
     * 请求进入内部分享页的请求码
     */
    const val SHARE_INNER_REQUEST_CODE = CROP_PHOTO_REQUEST_CODE + 1

    /**
     * 请求进入AI灵感成片请求码
     */
    const val AI_COMPOSITION_REQUEST_CODE = SHARE_INNER_REQUEST_CODE + 1

    /**
     * 请求进入最佳表情编辑页的请求码
     */
    const val AI_BESTTAKE_REQUEST_CODE = AI_COMPOSITION_REQUEST_CODE + 1

    /**
     * 请求进入补光页面的请求码
     */
    const val AI_LIGHTING_CODE = AI_BESTTAKE_REQUEST_CODE + 1

    /**
     * 请求进入AI 风光的请求码
     */
    const val AI_SCENERY_REQUEST_CODE = AI_LIGHTING_CODE + 1

    /**
     * 请求进入导出实况的请求码
     */
    const val EXPORT_OLIVE_REQUEST_CODE = AI_SCENERY_REQUEST_CODE + 1
}

/**
 * 图片编辑的请求码,包括默认的图片编辑,超级文本1.0和超级文本2.0
 */
@Retention(AnnotationRetention.SOURCE)
@IntDef(
    MenuRequestCode.PHOTO_EDIT_SUPER_TEXT,
    MenuRequestCode.PHOTO_EDIT_ENHANCE_TEXT,
    MenuRequestCode.PHOTO_EDIT_DEFAULT
)
internal annotation class PhotoEditRequestCode