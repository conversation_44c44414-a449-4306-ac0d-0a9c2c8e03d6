/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorViewAlphaAnimViewHolder.kt
 ** Description: 对itemView做alpha动画的viewHolder
 ** OptionsParcelable
 **
 ** Version: 1.0
 ** Date: 2022/7/27
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2022/7/27     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.business_lib.template.editor.adapter

import android.view.View
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation
import com.oplus.gallery.business_lib.template.editor.anim.EditorViewAlphaAnimation
import com.oplus.gallery.business_lib.template.editor.anim.IPressAnimation
import com.oplus.gallery.business_lib.template.editor.anim.ISelectableAlphaAnim

open class EditorViewAlphaAnimViewHolder(
    itemView: View,
    private val selectableAnimation: ISelectableAlphaAnim? = EditorViewAlphaAnimation(),
    pressAnimation: IPressAnimation? = EditorPressAnimation()
) : EditorAnimViewHolder(itemView, selectableAnimation, pressAnimation), ISelectableAlphaAnim {

    override fun setDisableAlpha(alpha: Float) {
        selectableAnimation?.setDisableAlpha(alpha)
    }

    override fun setSelectedAlpha(alpha: Float) {
        selectableAnimation?.setSelectedAlpha(alpha)
    }

    override fun setUnselectedAlpha(alpha: Float) {
        selectableAnimation?.setUnselectedAlpha(alpha)
    }

    override fun isSelectAnimationRunning(): Boolean {
        return selectableAnimation?.isSelectAnimationRunning() ?: false
    }

    override fun updateAnimValue(value: Any) {
        (value as? Float)?.let {
            itemView.alpha = it
        }
    }
}