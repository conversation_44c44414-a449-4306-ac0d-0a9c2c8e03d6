/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CollageModel.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/09/10
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/09/22	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.model.data.collage

import android.database.Cursor
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import kotlin.math.max
import kotlin.math.min

object CollageDataHelper {
    private const val TAG = "CollageDataHelper"
    private const val COLLAGE_LIMIT_PHOTO_RATIO = 400
    private const val ERROR_NOT_SUPPORT_RATIO = 0

    fun getFilePathsFromPath(paths: List<Path>, onError: ((Int) -> Unit)?): ArrayList<String> {
        val filePaths = ArrayList<String>()
        getItemsInfo(paths.map { path ->
            (path.`object` as? MediaItem)?.toOriginalItem()?.path?.suffix ?: let {
                GLog.e(TAG, LogFlag.DL) { "getFilePathsFromPath, error path:$path" }
                return filePaths
            }
        })?.use { cursor ->
            val indexFilePath = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DATA)
            val indexWidth = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.WIDTH)
            val indexHeight = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.HEIGHT)
            while (cursor.moveToNext()) {
                val filePath = cursor.getString(indexFilePath)
                val width = cursor.getInt(indexWidth).toFloat()
                val height = cursor.getInt(indexHeight).toFloat()

                val minLength: Float = min(width, height)
                val maxLength: Float = max(width, height)
                val itemRatio: Float = if (minLength > 0) maxLength / minLength else 0f

                if (itemRatio > COLLAGE_LIMIT_PHOTO_RATIO) {
                    onError?.invoke(ERROR_NOT_SUPPORT_RATIO)
                    filePaths.clear()
                    break
                }
                filePaths.add(filePath)
            }
        }
        return filePaths
    }

    /**
     * 获取指定id列表的信息
     *
     * @param ids _id的列表
     */
    private fun getItemsInfo(ids: List<String>): Cursor? {
        if (ids.isEmpty()) {
            return null
        }
        return QueryReq.Builder<Cursor>()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .setProjection(arrayOf(
                    GalleryStore.GalleryColumns.LocalColumns.DATA,
                    GalleryStore.GalleryColumns.LocalColumns.WIDTH,
                    GalleryStore.GalleryColumns.LocalColumns.HEIGHT
                ))
                .setWhere(DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns._ID, ids.size))
                .setWhereArgs(ids.toTypedArray())
                .setConvert(CursorConvert())
                .build()
                .exec()
    }
}