/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BottomMenuHelper.kt
 ** Description: Helps list fragment to do menu operations.
 **
 **
 ** Version: 1.0
 ** Date: 2020/10/23
 ** Author: wang<PERSON><EMAIL>
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wang<PERSON><PERSON>@Apps.Gallery		2020/10/23		1.0		OPLUS_FEATURE_TAB
 *********************************************************************************/
package com.oplus.gallery.business_lib.helper

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.res.Resources
import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.theme.COUIThemeOverlay
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterKey.KEY_ID
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterKey.KEY_MODEL_TYPE
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterKey.KEY_TITLE
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.StorageTipsHelper
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.api.ApiDmManager.getCloudSyncDM
import com.oplus.gallery.business_lib.cardcase.data.CardCaseCountSet
import com.oplus.gallery.business_lib.cardcase.utils.CardCaseUtils
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask.Companion.autoCancelWhenOnDestroy
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.builder.FileProcessTaskBuilder
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisSpecificationChecker
import com.oplus.gallery.business_lib.menuoperation.CollageAction.Companion.RESULT_ERROR_NOT_SUPPORT_RATIO
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter.freeFromGroup
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter.mergeGroup
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter.removeFromLabel
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter.showGroup
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.MenuRequestCode
import com.oplus.gallery.business_lib.menuoperation.MoveAlbumAction
import com.oplus.gallery.business_lib.menuoperation.MoveToSafeBoxAction
import com.oplus.gallery.business_lib.menuoperation.RenameAlbumAction
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.business_lib.menuoperation.base.MenuResult
import com.oplus.gallery.business_lib.menuoperation.item.CollageOperation
import com.oplus.gallery.business_lib.menuoperation.item.GifSynthesisTipOperation
import com.oplus.gallery.business_lib.menuoperation.item.RecycleOperation
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.config.allowlist.AlbumAllowListConfig
import com.oplus.gallery.business_lib.model.config.allowlist.GalleryCommonListConfig
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isTemporaryCacheItem
import com.oplus.gallery.business_lib.model.data.base.item.isVideo
import com.oplus.gallery.business_lib.model.data.base.set.MediaAlbum
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local.PATH_ALBUM_ANY_ALL
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local.PATH_ALBUM_CARD_CASE_ANY
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.base.utils.MediaSetUtils
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.NORMAL_INVALID_CODE
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PERSON_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PET_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.personpet.utils.PersonPetDataHelper
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.business_lib.model.selection.Selection
import com.oplus.gallery.business_lib.recycleBin.RecycleHelper.Companion.EXECUTION_RESULT_SUCCESS
import com.oplus.gallery.business_lib.recycleBin.RecycleHelper.Companion.RECYCLE_PHONE_BROKEN_FAIL
import com.oplus.gallery.business_lib.recycleBin.RecycleHelper.Companion.RECYCLE_PHONE_NO_SPACE_FAIL
import com.oplus.gallery.business_lib.recycleBin.RecycleHelper.Companion.RECYCLE_SD_BROKEN_FAIL
import com.oplus.gallery.business_lib.recycleBin.RecycleHelper.Companion.RECYCLE_SD_NO_SPACE_FAIL
import com.oplus.gallery.business_lib.safebox.SafeBoxHelper
import com.oplus.gallery.business_lib.ui.dialog.GuideDialogBuilder
import com.oplus.gallery.business_lib.ui.dialog.SafeBoxProgressDialog
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment
import com.oplus.gallery.business_lib.ui.fragment.BaseTimeNodeFragment
import com.oplus.gallery.business_lib.util.addBackCallback
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_MOVE_IN_SHORTCUT
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_MOVE_IN_SIDE_PANE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_MOVE_OUT_SHORTCUT
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_DIALOG_CLICK_CANCEL
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_DIALOG_CLICK_OK
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_RENAME_ALBUM_ALBUM_TYPE_OTHER
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_RENAME_ALBUM_ALBUM_TYPE_USER_CREATE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_RENAME_ALBUM_OPERATION_TYPE_CANCEL
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_RENAME_ALBUM_OPERATION_TYPE_SAVE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.NUM_ONE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.BATCH_DELETE_WARNING_OPER_CANCEL
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.BATCH_DELETE_WARNING_OPER_DELETE
import com.oplus.gallery.foundation.tracing.constant.RecycleTrackConstant.Value.RECYCLE_ACTION_RESULT_CANCEL
import com.oplus.gallery.foundation.tracing.constant.RecycleTrackConstant.Value.RECYCLE_ACTION_RESULT_SUCCESS
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant
import com.oplus.gallery.foundation.tracing.helper.CardCaseTrackHelper
import com.oplus.gallery.foundation.tracing.helper.CreationClickTrackHelper
import com.oplus.gallery.foundation.tracing.helper.LaunchExitPopupTrackHelper
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.ui.dialog.DialogHelper
import com.oplus.gallery.foundation.ui.dialog.DialogHelper.DIALOG_DELAY_0
import com.oplus.gallery.foundation.ui.dialog.DialogHelper.DIALOG_DELAY_500
import com.oplus.gallery.foundation.ui.dialog.ListDialog
import com.oplus.gallery.foundation.ui.dialog.LoadingDialog
import com.oplus.gallery.foundation.ui.dialog.SecurityDialog
import com.oplus.gallery.foundation.ui.dialog.base.BaseAlertDialog
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.storage.OplusEnvironment.StorageType
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.foundation.util.systemcore.WakeLockUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.SuperText.GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAVE_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_FOLD
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_TABLET
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.sortablemodel.sort.AlbumMovable
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_PAGE_UNKNOWN
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.bottomnavigation.BottomNavigationView
import com.oplus.gallery.standard_lib.ui.popupwindow.MenuListPopupItem
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import com.oplus.gallery.basebiz.R as BasebizR

@Suppress("LargeClass")
class BottomMenuHelper(activity: BaseActivity?, selection: Selection? = null) {

    private var activity: WeakReference<BaseActivity>? = WeakReference(activity)
    private var selection: WeakReference<Selection>? = WeakReference(selection)
    private var selectedItemId: Int = 0
    private var selectCount: Int = 0
    private var menuViewRef: WeakReference<View?>? = null
    private var menuResult: MenuResult? = null
    private var dialogHelper: DialogHelper? = null
    private var fromBottomDialog: ListDialog? = null
    private var fromCenterDialog: ConfirmDialog? = null
    private var confirmDialogListener: ConfirmDialogListener? = null
    private var batchDialogMessage: String? = null
    private var menuEventNotifier: MenuOperation.EventNotifier? = null
    private var safeBoxProgressDialog: SafeBoxProgressDialog? = null
    private var countDownTimer: CountDownTimer? = null
    private var quickLoadingHelper: QuickLoadingHelper = QuickLoadingHelper()

    private val isNeedShowSixtyDaysDeleteCopywrite: Boolean by lazy {
        getCloudSyncDM().isGoogleChannel() == true
    }

    @Suppress("LongMethod")
    fun updateBottomMenu(selectedCount: Int, selectedItems: Set<Path>?, navigationBar: BottomNavigationView) {
        if (selectedCount < 1) {
            setItemEnable(BasebizR.id.action_recycle, false, navigationBar)
            setItemEnable(BasebizR.id.action_share, false, navigationBar)
            setItemEnable(BasebizR.id.action_encrypt, false, navigationBar)
            setItemEnable(BasebizR.id.action_move_to, false, navigationBar)
            setItemEnable(BasebizR.id.action_photo_creation, false, navigationBar)
            setItemEnable(BasebizR.id.action_photo_jigsaw, false, navigationBar)
            setItemEnable(BasebizR.id.action_free_face_from_group, false, navigationBar)
            setItemEnable(BasebizR.id.action_delete_all_recycled, true, navigationBar)
            setItemEnable(BasebizR.id.action_restore_all_recycled, true, navigationBar)
            setItemEnable(BasebizR.id.action_remove_from_label, false, navigationBar)
            setItemEnable(BasebizR.id.action_more_info, false, navigationBar)
            setItemEnable(BasebizR.id.action_import, false, navigationBar)
            setItemEnable(BasebizR.id.action_photo_create, false, navigationBar)
            setItemEnable(BasebizR.id.action_more, false, navigationBar)
        } else {
            setItemEnable(BasebizR.id.action_recycle, true, navigationBar)
            setItemEnable(BasebizR.id.action_share, true, navigationBar)
            setItemEnable(BasebizR.id.action_move_to, true, navigationBar)
            setItemEnable(BasebizR.id.action_free_face_from_group, true, navigationBar)
            setItemEnable(BasebizR.id.action_delete_all_recycled, false, navigationBar)
            setItemEnable(BasebizR.id.action_restore_all_recycled, false, navigationBar)
            setItemEnable(BasebizR.id.action_remove_from_label, true, navigationBar)
            setItemEnable(BasebizR.id.action_more_info, true, navigationBar)
            setItemEnable(BasebizR.id.action_import, true, navigationBar)
            setItemEnable(BasebizR.id.action_more, true, navigationBar)
            setItemEnable(
                BasebizR.id.action_photo_creation,
                selectedCount in MINIMUM_ITEM_FOR_FILM_CREATION..MAXIMUM_ITEM_FOR_FILM_CREATION,
                navigationBar
            )
            setItemEnable(
                BasebizR.id.action_photo_jigsaw,
                selectedCount in MINIMUM_ITEM_FOR_FILM_CREATION..MAXIMUM_ITEM_FOR_FILM_CREATION,
                navigationBar
            )
            setItemEnable(
                BasebizR.id.action_photo_create,
                true,
                navigationBar
            )
            setItemEnable(BasebizR.id.action_encrypt, true, navigationBar)
        }

        // quick图删除功能目前在 os 14.0 及以上，若不支持quick图删除，且存在quick图，则禁用删除
        if (isSupportQuickPhotoDelete.not() && selectedItems != null) {
            updateBarEnable(selectedItems, navigationBar)
        }
        setBottomMenuItemRedDotVisible(BasebizR.id.action_more, false, navigationBar)
    }

    /**
     * 更新导航栏的状态
     */
    fun updateBarEnable(selectedItems: Set<Path>, navigationBar: BottomNavigationView) {
        // quick图删除功能目前在 os 14.0 及以上，若不支持quick图删除，且存在quick图，则禁用删除
        if (isSupportQuickPhotoDelete.not()) {
            for (path in selectedItems) {
                val mediaItem = DataManager.getMediaObject(path) as? MediaItem
                if ((mediaItem?.isLoaded == true) && mediaItem.isTemporaryCacheItem()) {
                    GLog.d(TAG, LogFlag.DL, "updateBottomMenu: $path is quick, disable delete")
                    setItemEnable(BasebizR.id.action_recycle, false, navigationBar)
                    break
                }
            }
        }
    }

    fun setItemEnable(id: Int, enable: Boolean, navigationBar: BottomNavigationView) {
        navigationBar.menu.findItem(id)?.let {
            if (it.isEnabled != enable) it.isEnabled = enable
        }
    }

    /**
     * 设置对应的menuItem action的红点显隐状态
     */
    fun setBottomMenuItemRedDotVisible(id: Int, isRedDotVisible: Boolean = false, navigationBar: BottomNavigationView) {
        if (isRedDotVisible) {
            navigationBar.showRedDot(id)
        } else {
            navigationBar.clearRedDot(id)
        }
    }

    /**
     *  大图模式下的删除/恢复/彻底删除操作统一使用这个接口
     *  @param hint 点击删除按钮之后弹窗上的灰色提示语
     *  @param confirmMsg 点击删除按钮之后确定键的提示语“确定、确认”
     *  @param actionId 具体执行的操作（删除、恢复、彻底删除）
     *  @param setPathString 所属图集的Path,可以用来判断是从那个图集进入到大图的
     *  @param cShotCount  不为0时，则删除一组连拍图片， 当为0时，则删除单张照片（不管是不是连拍都只删除一张：如文件管理器-进入大图-删除连拍时，是删除单张照片）
     *  @param neutralButtonTextColor  按钮颜色
     *  @return 显示的弹窗
     */
    @Suppress("LongMethod")
    fun doSingleItemRecycleAction(
        lifecycle: Lifecycle,
        actionId: String,
        itemPath: Path,
        hint: String? = null,
        confirmMsg: String,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL,
        confirmProgressListener: ProgressListener? = null,
        setPathString: String? = null,
        trackCallerEntry: TrackCallerEntry,
        cShotCount: Int = 0,
        @ColorInt neutralButtonTextColor: Int? = null,
        showCallback: ((DialogDelegate?) -> Unit)? = null
    ) {
        val resources = activity?.get()?.resources
        if (resources == null) {
            showCallback?.invoke(null)
            GLog.e(TAG, LogFlag.DL, "doSingleItemRecycleAction resources is null")
            return
        }
        val title = makeDialogActionRecycleTitle(resources, actionId, mediaType, true, cShotCount, null)
        val clickCallback = clickCallback@{ which: Int ->
            when (actionId) {
                ACTION_DELETE_RECYCLED -> {
                    //首次删除状态修改
                    changeFirstDeleteState(actionId)
                }
            }
            if (which != DialogInterface.BUTTON_NEUTRAL) return@clickCallback
            confirmProgressListener?.onClickPositive()
            when (actionId) {
                ACTION_RECYCLE -> {
                    recycle(
                        listOf(itemPath),
                        trackCallerEntry = trackCallerEntry,
                        isDeleteCShotCollection = cShotCount > 0,
                        callbackWrapper = LifecycleCallbackWrapper(lifecycle) { paths, state ->
                            if (state == ACTION_STATE_SUCCESS) {
                                confirmProgressListener?.onProgressComplete(EXECUTION_RESULT_SUCCESS, paths)
                                // 产品埋点：随身卡包内图片被删除，200600220   从随身卡包图集详情页进入大图页并删除成功
                                val isFromCardCase = setPathString?.equals(PATH_ALBUM_CARD_CASE_ANY.toString()) ?: false
                                if (isFromCardCase) {
                                    CardCaseTrackHelper.trackCardCaseDeleteCountFromPhotoPage()
                                }
                            }
                        }
                    )
                }

                ACTION_DELETE_RECYCLED -> {
                    deleteRecycled(
                        listOf(itemPath),
                        trackCallerEntry,
                        callbackWrapper = LifecycleCallbackWrapper(lifecycle) { paths, state ->
                            if (state == ACTION_STATE_SUCCESS) {
                                confirmProgressListener?.onProgressComplete(EXECUTION_RESULT_SUCCESS, paths)
                            }
                        }
                    )
                }

                ACTION_RESTORE -> {
                    restore(
                        listOf(itemPath),
                        trackCallerEntry = trackCallerEntry,
                        isRestoreCShotCollection = cShotCount > 0,
                        callbackWrapper = LifecycleCallbackWrapper(lifecycle) { paths, state ->
                            if (state == ACTION_STATE_SUCCESS) {
                                confirmProgressListener?.onProgressComplete(EXECUTION_RESULT_SUCCESS, paths)
                            }
                        }
                    )
                }
            }
        }
        if (actionId == ACTION_DELETE_RECYCLED) {
            doRecycleBinDeleteAction(
                listOf(itemPath),
                hint = hint,
                title = title,
                confirmMsg = confirmMsg,
                actionId = actionId,
                clickCallback = clickCallback,
                showCallback = showCallback
            )
        } else {
            val dialog = showActionDialog(
                confirmMsg = confirmMsg,
                hint = hint,
                title = title,
                actionId = actionId,
                newNeutralButtonTextColor = neutralButtonTextColor,
                clickCallback = clickCallback
            )?.let {
                BaseAlertDialogDelegate(it)
            }
            showCallback?.invoke(dialog)
        }
    }

    /**
     *  选择模式下的删除/恢复/彻底删除/删除图集等操作统一使用这个接口
     *  @param actionId 具体执行的操作（删除、恢复、彻底删除）
     *  @param totalItemCount 此集合中的所有图片，可为0（如时间轴。。。）
     *  @param supportSelectAll 此集合是否支持全部选择功能
     */
    @Suppress("LongMethod")
    fun doSelectionRecycleAction(
        actionId: String,
        totalItemCount: Int = 0,
        specialCount: Bundle = Bundle(),
        supportSelectAll: Boolean = false,
        trackCallerEntry: TrackCallerEntry,
        isFromTimeline: Boolean = false,
        selectedAlbumName: String? = null,
        @ColorInt neutralButtonTextColor: Int? = null,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        var isContainCShot = false
        var imageCount = 0
        var videoCount = 0
        val resource = activity?.get()?.resources ?: return
        //回收站--非选择模式-直接点击“全部删除”，所以此时getSelectedItemCount()返回值为0,需要totalItemCount来区别弹窗的按钮显示删除或者全部删除文案
        val totalSelectedCount =
            selection?.get()?.let { if (actionId == ACTION_DELETE_ALL_RECYCLED) totalItemCount else it.getSelectedItemCount() } ?: return
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                when (actionId) {
                    ACTION_RESTORE_ALL_RECYCLED,
                    ACTION_DELETE_ALL_RECYCLED -> {
                        imageCount = specialCount.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                        videoCount = specialCount.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                    }

                    else -> {
                        imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                        videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                        //由于需求只对非回收站图集的选择页面做了连拍提示语区分，选择页面每一个连拍数据当作一个项目；备注：后面使用该变量时需谨慎，确认需求一致
                        if ((actionId == ACTION_RECYCLE) && (imageCount > 0)) {
                            isContainCShot = isContainCShot(set.toList())
                        }
                    }
                }
                getRecycleHint(
                    actionId = actionId,
                    imageCount = imageCount,
                    videoCount = videoCount,
                    isContainCShot = isContainCShot,
                    resources = resource,
                    selectedCount = totalSelectedCount,
                    totalItemCount = totalItemCount,
                    albumName = selectedAlbumName,
                    resultCallback = { hint, confirmMsg, title, showBatchCloudDeleteDialog ->
                        val clickCallback = clickCallback@{ which: Int ->
                            //首次删除状态修改
                            changeFirstDeleteState(actionId)
                            // 删除-弹窗点击取消，并且是来自时间轴
                            if (isFromTimeline && (which == DialogInterface.BUTTON_NEGATIVE) && (actionId == ACTION_RECYCLE)) {
                                //云批量删除弹框，点击取消上报
                                if (showBatchCloudDeleteDialog) {
                                    LaunchExitPopupTrackHelper.trackSendCloudBatchDelete(
                                        batchDialogMessage,
                                        BATCH_DELETE_WARNING_OPER_CANCEL
                                    )
                                }
                            }
                            //当为清空相册事件，且操作删除并关闭按钮时，侧先关闭云同步
                            if ((actionId == ACTION_RECYCLE_ALL) && (which == DialogInterface.BUTTON_NEUTRAL)) {
                                // 关闭云同步
                                getCloudSyncDM().setAlbumSyncSwitchDirectly(false)
                            }
                            doAlbumsActionTrackOnRecycle(
                                actionId = actionId,
                                which = which,
                                set = set,
                                trackCallerEntry = trackCallerEntry,
                                imageCount = imageCount,
                                videoCount = videoCount,
                                totalItemCount = totalItemCount
                            )
                            if (which != DialogInterface.BUTTON_NEUTRAL) {
                                callbackWrapper?.invoke(null, ACTION_STATE_CANCEL)
                                return@clickCallback
                            }
                            when (actionId) {
                                ACTION_RECYCLE_ALL,
                                ACTION_RECYCLE -> {
                                    recycle(
                                        set.toList(),
                                        isDeleteCShotCollection = true,
                                        trackCallerEntry = trackCallerEntry,
                                        callbackWrapper = callbackWrapper
                                    )
                                    trackSendClick(imageCount, videoCount)
                                }

                                ACTION_RECYCLE_ALBUM -> {
                                    recycleAlbum(
                                        set.toList(),
                                        trackCallerEntry,
                                        callbackWrapper = callbackWrapper
                                    )
                                }

                                ACTION_DELETE_RECYCLED -> {
                                    deleteRecycled(
                                        set.toList(),
                                        trackCallerEntry,
                                        callbackWrapper = callbackWrapper
                                    )
                                }

                                ACTION_DELETE_ALL_RECYCLED -> {
                                    deleteAllRecycled(
                                        set.toList(),
                                        trackCallerEntry,
                                        callbackWrapper = callbackWrapper
                                    )
                                }

                                ACTION_RESTORE -> {
                                    restore(
                                        set.toList(),
                                        true,
                                        trackCallerEntry,
                                        callbackWrapper = callbackWrapper
                                    )
                                }

                                ACTION_RESTORE_ALL_RECYCLED -> {
                                    restoreAll(
                                        set.toList(),
                                        trackCallerEntry,
                                        callbackWrapper = callbackWrapper
                                    )
                                }
                            }
                        }
                        if ((actionId == ACTION_DELETE_RECYCLED) || (actionId == ACTION_DELETE_ALL_RECYCLED)) {
                            doRecycleBinDeleteAction(
                                set.toList(),
                                hint = hint,
                                title = title,
                                confirmMsg = confirmMsg,
                                actionId = if (totalSelectedCount == totalItemCount) ACTION_DELETE_ALL_RECYCLED else actionId,
                                clickCallback = clickCallback
                            )
                        } else {
                            showActionDialog(
                                confirmMsg = confirmMsg,
                                hint = hint,
                                title = title,
                                newNeutralButtonTextColor = neutralButtonTextColor,
                                actionId = actionId,
                                clickCallback = clickCallback
                            )
                        }
                    }
                )
            }
        }
    }

    /**
     * 判断是否包含连拍数据
     */
    private fun isContainCShot(pathList: List<Path>): Boolean {
        return pathList.any { ImageTypeUtils.isCShot(it) }
    }

    /**
     * 回收站执行彻底删除的动作，先提示弹窗再根据用户操作作处理
     */
    private fun doRecycleBinDeleteAction(
        itemPathList: List<Path>,
        hint: String? = null,
        title: String? = null,
        confirmMsg: String,
        actionId: String,
        clickCallback: (which: Int) -> Unit,
        showCallback: ((DialogDelegate?) -> Unit)? = null
    ) {
        if (ApiDmManager.getCloudSyncPageDM().needShowCloudDeleteConfirmDialog() == true) {
            // 尝试显示云服务定义的彻底删除dialog
            tryToShowCloudDeleteConfirmDialog(
                pathList = itemPathList,
                actionId = actionId,
                confirmedCallback = { clickCallback(DialogInterface.BUTTON_NEUTRAL) },
                cancelCallback = { clickCallback(DialogInterface.BUTTON_NEGATIVE) },
                showCallback = {
                    runOnUiThread {
                        if (it == null) {
                            // 条件不符合，显示业务自定义的彻底删除dialog
                            showCommonDeleteDialog(
                                title = title,
                                hint = hint,
                                confirmMsg = confirmMsg,
                                clickCallback = clickCallback,
                                showCallback = showCallback
                            )
                        } else {
                            showCallback?.invoke(it)
                        }
                    }
                }
            )
        } else {
            showCommonDeleteDialog(
                title = title,
                hint = hint,
                confirmMsg = confirmMsg,
                clickCallback = clickCallback,
                showCallback = showCallback
            )
        }
    }

    /**
     * 显示业务自定义的彻底删除dialog
     */
    private fun showCommonDeleteDialog(
        title: String? = null,
        hint: String? = null,
        confirmMsg: String,
        clickCallback: (which: Int) -> Unit,
        showCallback: ((DialogDelegate?) -> Unit)? = null
    ) {
        confirmDialogListener = ConfirmDialogListener(clickCallback)
        val dialog = if (getCloudSyncDM().isAlbumSyncSwitchOpen() && !isCloudSyncRecycledDeleteTipsFirstShow()) {
            showConfirmTimerDialog(title = title, confirmMsg = confirmMsg, hint = hint, view = null)
        } else {
            showConfirmDialog(title = title, confirmMsg = confirmMsg, hint = hint, view = null)
        }?.let {
            BaseAlertDialogDelegate(it)
        }
        showCallback?.invoke(dialog)
    }

    /**
     * 删除弹窗标题
     * @param isBigPictureMode 大图模式
     * @param selectedCount 选择的数量
     */
    private fun makeDialogActionRecycleTitle(
        resources: Resources,
        actionId: String,
        mediaType: Int,
        isBigPictureMode: Boolean,
        selectedCount: Int,
        albumName: String?,
        totalItemCount: Int = 0,
        isSyncSwitchOpen: Boolean = false
    ): String? {
        return when (actionId) {
            ACTION_DELETE_RECYCLED,
            ACTION_DELETE_ALL_RECYCLED -> {
                makeRecyledDialogTitle(
                    resources,
                    mediaType = mediaType,
                    isBigPictureMode = isBigPictureMode,
                    selectedCount,
                    totalItemCount
                )
            }

            ACTION_RESTORE,
            ACTION_RESTORE_ALL_RECYCLED -> {
                makeRestoreDialogTitle(
                    mediaType = mediaType,
                    isBigPictureMode = isBigPictureMode,
                    selectedCount = selectedCount,
                )
            }

            ACTION_RECYCLE_ALBUM,
            ACTION_RECYCLE -> {
                makeRecycleDialogTitle(
                    actionId = actionId,
                    mediaType = mediaType,
                    isBigPictureMode = isBigPictureMode,
                    albumName = albumName,
                    selectedCount = selectedCount,
                    totalItemCount
                )
            }

            else -> null
        }
    }

    private fun changeFirstDeleteState(actionId: String) {
        when (actionId) {
            ACTION_RECYCLE,
            ACTION_RECYCLE_ALBUM -> changeRecycleFirstDeleteState(actionId)

            ACTION_DELETE_RECYCLED,
            ACTION_DELETE_ALL_RECYCLED -> changeRecycledFirstDeleteState(actionId)
        }
    }

    /**
     *  更改首次删除状态
     *  @param actionId 具体执行的操作（删除、恢复、彻底删除）
     */
    private fun changeRecycleFirstDeleteState(actionId: String) {
        if (isCloudSyncRecycleDeleteTipsFirstShow()) {
            return
        }
        if (!getCloudSyncDM().isAlbumSyncSwitchOpen()) {
            return
        }
        activity?.get()?.getAppAbility<ISettingsAbility>()?.use {
            it.setRecycleTipsAsFirstShow(true)
        }
    }

    /**
     *  更改最近删除-首次删除状态
     *  @param actionId 具体执行的操作（删除、恢复、彻底删除）
     */
    private fun changeRecycledFirstDeleteState(actionId: String) {
        if (isCloudSyncRecycledDeleteTipsFirstShow()) {
            return
        }
        if (!getCloudSyncDM().isAlbumSyncSwitchOpen()) {
            return
        }
        activity?.get()?.getAppAbility<ISettingsAbility>()?.use {
            it.setRecycledDeleteTipsAsFirstShow(true)
        }
    }

    /**
     *  图集的删除操作：
     *  @param actionId 具体执行的操作（删除）
     *  @param totalItemCount 此集合中的所有图片
     *  @param isClickSelectAll 是否点击全选按钮
     *  @param albumViewDataPath 图集 path
     */
    fun doSelectionRecycleActionForAlbum(
        actionId: String,
        totalItemCount: Int = 0,
        isClickSelectAll: Boolean = false,
        albumViewDataPath: String?,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        val totalSelectedCount = selection?.get()?.getSelectedItemCount() ?: return
        // 所有项目中：1. 点击全选按钮或者选择的数量与总数量相等；2. 且开启云同步；则走清空相册逻辑 (albumViewDataPath == PATH_ALBUM_ANY_ALL.toString()) 表示【所有项目图集】
        if (isSupportClearAlbumAllFunction() && isClearAlbumAll(albumViewDataPath, isClickSelectAll, totalSelectedCount, totalItemCount)) {
            doSelectionRecycleActionForAlbumAll(
                actionId = actionId,
                totalItemCount = totalItemCount,
                trackCallerEntry = trackCallerEntry,
                callbackWrapper = callbackWrapper
            )
        } else {
            doSelectionRecycleAction(
                actionId = actionId,
                totalItemCount = totalItemCount,
                supportSelectAll = true,
                trackCallerEntry = trackCallerEntry,
                callbackWrapper = callbackWrapper
            )
        }
    }

    /**
     *  “所有项目”图集的删除操作：
     *  场景1: 勾选全选或划选全选-》删除
     *  场景2：勾选或划选全选之后，取消部分选中，--》删除
     *  @param totalItemCount 此集合中的所有图片
     *  @param isClickSelectAll 是否点击全选按钮
     */
    private fun doSelectionRecycleActionForAlbumAll(
        actionId: String,
        totalItemCount: Int = 0,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        val resource = activity?.get()?.resources ?: return
        var isContainCShot = false
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                val imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                val videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                val (newPositiveButtonTextColor, newNeutralButtonTextColor) = clearAlbumDialogNewButtonTextColor()
                if ((actionId == ACTION_RECYCLE) && (imageCount > 0)) {
                    isContainCShot = isContainCShot(set.toList())
                }
                val mediaType = if (isContainCShot) MEDIA_TYPE_CONTAIN_ALL else getMediaType(videoCount, imageCount)
                showClearAlbumDialog(
                    resource,
                    totalItemCount,
                    imageCount,
                    videoCount,
                    newNeutralButtonTextColor,
                    newPositiveButtonTextColor,
                    mediaType = mediaType
                ) { which ->
                    if (which == DialogInterface.BUTTON_NEUTRAL) {
                        //选择【仅从本设备删除】进行二次弹窗
                        doSelectionRecycleAction(
                            actionId = ACTION_RECYCLE_ALL,
                            totalItemCount = totalItemCount,
                            supportSelectAll = true,
                            trackCallerEntry = trackCallerEntry,
                            callbackWrapper = callbackWrapper
                        )
                        return@showClearAlbumDialog
                    }
                    doAlbumsActionTrackOnRecycle(
                        actionId = ACTION_RECYCLE_ALL,
                        which = which,
                        set = set,
                        trackCallerEntry = trackCallerEntry,
                        imageCount = imageCount,
                        videoCount = videoCount,
                        totalItemCount = totalItemCount
                    )
                    if (which == DialogInterface.BUTTON_NEGATIVE) {
                        callbackWrapper?.invoke(null, ACTION_STATE_CANCEL)
                        return@showClearAlbumDialog
                    }
                    recycle(
                        set.toList(),
                        isDeleteCShotCollection = true,
                        trackCallerEntry = trackCallerEntry,
                        callbackWrapper = callbackWrapper
                    )
                    trackSendClick(imageCount, videoCount)
                }
            }
        }
    }

    /**
     * 回收站-恢复
     * @param mediaType 数据类型：1：图片；2：包含视频和图片；3：视频
     * @param isBigPictureMode 是否是大图页进入 true：大图页；false：选择模式；
     * @param selectedCount 选择恢复数量
     * @return  标题
     */
    private fun makeRestoreDialogTitle(
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL,
        isBigPictureMode: Boolean = false,
        selectedCount: Int
    ): String? {
        return if (isBigPictureMode) {
            singleChoiceMakeRestoreTitle(mediaType)
        } else {
            multipleChoiceMakeRestoreTitle(mediaType, selectedCount)
        }
    }

    /**
     * 回收站-大图页，恢复弹窗标题
     */
    private fun singleChoiceMakeRestoreTitle(mediaType: Int): String? {
        return when (mediaType) {
            MEDIA_TYPE_ONLY_VIDEO -> activity?.get()?.resources?.getString(BasebizR.string.base_restore_selected_this_video_message)
            else -> activity?.get()?.resources?.getString(BasebizR.string.base_restore_selected_this_photo_message)
        }
    }

    /**
     * 回收站-xuanze页，恢复弹窗标题
     */
    private fun multipleChoiceMakeRestoreTitle(mediaType: Int, selectedCount: Int): String? {
        return when (mediaType) {
            MEDIA_TYPE_ONLY_VIDEO -> {
                activity?.get()?.resources?.getQuantityString(
                    BasebizR.plurals.base_restore_selected_these_video_message,
                    selectedCount,
                    selectedCount
                )
            }

            MEDIA_TYPE_ONLY_IMAGE -> {
                activity?.get()?.resources?.getQuantityString(
                    BasebizR.plurals.base_restore_selected_these_photo_message,
                    selectedCount,
                    selectedCount
                )
            }

            else -> {
                activity?.get()?.resources?.getQuantityString(
                    BasebizR.plurals.base_restore_selected_these_project_message,
                    selectedCount,
                    selectedCount
                )
            }
        }
    }

    /**
     * 是否清空相册
     */
    private fun isClearAlbumAll(
        albumViewDataPath: String?,
        isClickSelectAll: Boolean,
        totalSelectedCount: Int,
        totalItemCount: Int
    ): Boolean {
        //是否开启云同步
        val isSyncSwitchOpen = getCloudSyncDM().isAlbumSyncSwitchOpen()
        return (isSyncSwitchOpen
                && (albumViewDataPath == PATH_ALBUM_ANY_ALL.toString())
                && (isClickSelectAll || (totalSelectedCount == totalItemCount)))
    }

    /**由于该关闭云同步（getCloudSyncDM().setAlbumSyncSwitchDirectly(false)）函数只支持有appSetting的版本，因此根据产品要求对于不支持的版本不开放清空相册功能；
     * 判断是否支持清空相册功能
     */
    private fun isSupportClearAlbumAllFunction(): Boolean {
        return getCloudSyncDM().isSupportAlbumSyncSwitch()
    }

    /**
     * 清空相册 dialog 按钮的颜色
     */
    private fun clearAlbumDialogNewButtonTextColor(): Pair<Int?, Int?> {
        val newPositiveButtonTextColor = activity?.get()?.let {
            COUIContextUtil.getAttrColor(activity?.get(), com.support.appcompat.R.attr.couiColorError)
        }
        val newNeutralButtonTextColor = activity?.get()?.let {
            COUIContextUtil.getAttrColor(activity?.get(), com.support.appcompat.R.attr.couiColorPrimaryTextOnPopup)
        }
        return Pair(newPositiveButtonTextColor, newNeutralButtonTextColor)
    }

    private fun trackSendClick(imageCount: Int, videoCount: Int) {
        //云批量删除弹框，点击删除上报
        LaunchExitPopupTrackHelper.trackSendCloudBatchDelete(
            batchDialogMessage,
            BATCH_DELETE_WARNING_OPER_DELETE
        )
    }

    private fun doAlbumsActionTrackOnRecycle(
        actionId: String,
        which: Int,
        set: Set<Path>,
        trackCallerEntry: TrackCallerEntry,
        imageCount: Int,
        videoCount: Int,
        totalItemCount: Int
    ) {
        when (actionId) {
            ACTION_RECYCLE_ALL,
            ACTION_RECYCLE -> {
                AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                    currentPage = trackCallerEntry.albumsActionCurrentPage,
                    imageCount = imageCount.toString(),
                    videoCount = videoCount.toString(),
                    itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_DELETE_VALUE,
                    albumName = trackCallerEntry.currentAlbumName,
                    path = trackCallerEntry.path,
                    operResult = if (which == DialogInterface.BUTTON_NEUTRAL) RECYCLE_ACTION_RESULT_SUCCESS else RECYCLE_ACTION_RESULT_CANCEL
                )
            }

            ACTION_RECYCLE_ALBUM -> {
                AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                    currentPage = trackCallerEntry.albumsActionCurrentPage,
                    itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_DELETE_VALUE,
                    albumName = trackCallerEntry.currentAlbumName,
                    path = trackCallerEntry.path,
                    operResult = if (which == DialogInterface.BUTTON_NEUTRAL) RECYCLE_ACTION_RESULT_SUCCESS else RECYCLE_ACTION_RESULT_CANCEL,
                    albumList = set.toList()
                )
            }

            ACTION_RESTORE, ACTION_RESTORE_ALL_RECYCLED -> {
                AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                    currentPage = trackCallerEntry.albumsActionCurrentPage,
                    imageCount = imageCount.toString(),
                    videoCount = videoCount.toString(),
                    itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_RESTORE_VALUE,
                    albumName = trackCallerEntry.currentAlbumName,
                    path = trackCallerEntry.path,
                    operResult = if (which == DialogInterface.BUTTON_NEUTRAL) RECYCLE_ACTION_RESULT_SUCCESS else RECYCLE_ACTION_RESULT_CANCEL
                )
            }
        }
    }

    private fun recycle(
        pathList: List<Path>,
        isDeleteCShotCollection: Boolean,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        RealShowTimeInstrument.startRecord(RealShowTimeInstrument.PHOTO_RECYCLE_TAG)
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        if (pathList.size > 1) {
            showLoadingDialog(BasebizR.string.base_deleting, pathList.size)
        }
        val time = System.currentTimeMillis()
        GLog.d(TAG, LogFlag.DL, "recycle action start")
        MenuOperationManager.doAction(
            MenuAction.RECYCLE,
            MenuActionGetter.recycle.builder
                .setRecycleList(pathList)
                .setRecycleCShotCollection(isDeleteCShotCollection)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            createOnComplete(MenuAction.RECYCLE, actionCallback = { state ->
                callbackWrapper?.invoke(pathList, state)
                GLog.d(TAG, LogFlag.DL, "recycle action complete, time: ${GLog.getTime(time)}, state: $state, deleteSize: ${pathList.size}")
            })
        )
    }

    private fun recycleAlbum(
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        showLoadingDialog(BasebizR.string.base_deleting, pathList.size)
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.RECYCLE,
            MenuActionGetter.recycle.builder
                .setRecycleList(pathList)
                .setIsRecycleAlbum(true)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            createOnComplete(MenuAction.RECYCLE, actionCallback = { state ->
                RealShowTimeInstrument.startRecord(RealShowTimeInstrument.ALBUM_RECYCLE_TAG)
                callbackWrapper?.invoke(pathList, state)
                CardCaseUtils.deleteCardCaseAlbum(pathList)
            })
        )
    }

    private fun restore(
        pathList: List<Path>,
        isRestoreCShotCollection: Boolean,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        if (pathList.size > 1) {
            showLoadingDialog(BasebizR.string.base_restoring, pathList.size)
            callbackWrapper?.invoke(null, ACTION_STATE_START)
        }
        MenuOperationManager.doAction(
            MenuAction.RESTORE_RECYCLED,
            MenuActionGetter.restoreRecycled.builder
                .setRestoreList(pathList)
                .setRestoreCShotCollection(isRestoreCShotCollection)
                .setActivity(activity)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            createOnComplete(MenuAction.RESTORE_RECYCLED, actionCallback = { state ->
                callbackWrapper?.invoke(pathList, state)
                CardCaseUtils.restoreCardCaseAlbum()
            })
        )
    }

    private fun restoreAll(
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        showLoadingDialog(BasebizR.string.base_restoring, pathList.size)
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.RESTORE_RECYCLED,
            MenuActionGetter.restoreRecycled.builder
                .setRestoreAllRecycled(true)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            createOnComplete(MenuAction.RESTORE_RECYCLED, actionCallback = { state ->
                callbackWrapper?.invoke(pathList, state)
                CardCaseUtils.restoreCardCaseAlbum()
            })
        )
    }

    private fun deleteRecycled(
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        if (pathList.size > 1) {
            showLoadingDialog(BasebizR.string.base_deleting, pathList.size)
        }
        MenuOperationManager.doAction(
            MenuAction.DELETE_RECYCLED,
            MenuActionGetter.deleteRecycled.builder
                .setDeleteList(pathList)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            createOnComplete(MenuAction.DELETE_RECYCLED, actionCallback = { state ->
                callbackWrapper?.invoke(pathList, state)
            })
        )
    }

    private fun deleteAllRecycled(
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        showLoadingDialog(BasebizR.string.base_deleting, pathList.size)
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.DELETE_RECYCLED,
            MenuActionGetter.deleteRecycled.builder
                .setDeleteAllRecycled(true)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            createOnComplete(MenuAction.DELETE_RECYCLED, actionCallback = { state ->
                callbackWrapper?.invoke(pathList, state)
            })
        )
    }

    fun doPhotoCreationAction(
        fragment: BaseFragment,
        bottomNavigationBar: BottomNavigationView?,
        currentPage: String?,
        trackCallerEntry: TrackCallerEntry,
        albumNavigationTrackPageInfo: String?
    ) {
        var imageCount = 0
        var videoCount = 0
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                photoCreation(
                    fragment,
                    imageCount,
                    videoCount,
                    bottomNavigationBar,
                    set,
                    currentPage,
                    trackCallerEntry,
                    albumNavigationTrackPageInfo
                )
            }
        }
    }

    /**
     * 响应更多菜单点击
     * @param anchorItemId 更多菜单资源id, 用于更多菜单锚点
     * @param menuList 菜单项列表
     */
    fun doMoreMenuAction(
        fragment: BaseFragment,
        bottomNavigationBar: BottomNavigationView?,
        albumPath: Path?,
        callbackWrapper: LifecycleCallbackWrapper? = null,
        trackCallerEntry: TrackCallerEntry,
        anchorItemId: Int,
        menuList: ArrayList<MenuListPopupItem>
    ) {
        var imageCount = 0
        var videoCount = 0
        var oliveCount = 0
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                oliveCount = bundle.getInt(Constants.SpecifiedCount.KEY_OLIVE_COUNT, 0)
                val isSupportOliveSave = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false)
                if (isSupportOliveSave.not() || (videoCount > 0) || (oliveCount == 0) || (imageCount > oliveCount)) {
                    val menuListPopupItem = menuList.find { it == MenuListPopupItem(BasebizR.string.base_export_video) }
                    if (menuListPopupItem != null) menuList.remove(menuListPopupItem)
                }
                doMoreMenu(
                    fragment,
                    imageCount,
                    videoCount,
                    bottomNavigationBar,
                    set,
                    albumPath,
                    callbackWrapper,
                    trackCallerEntry,
                    anchorItemId,
                    menuList,
                    oliveCount
                )
            }
        }
    }

    fun doCreateAction(
        fragment: BaseFragment,
        bottomNavigationBar: BottomNavigationView?,
        currentPage: String?,
        trackCallerEntry: TrackCallerEntry,
        picCount: Int,
        videoCount: Int
    ) {
        bottomNavigationBar?.showSubMenuPopWindow(
            BasebizR.id.action_photo_create,
            { itemList ->
                itemList.add(MenuListPopupItem(BasebizR.string.gif_synthesis_item_create_name, false, true))
                itemList.add(MenuListPopupItem(BasebizR.string.base_jigsaw_item, false, true))
            }) { _, item ->
            when (item.titleId) {
                BasebizR.string.gif_synthesis_item_create_name -> {
                    doGifAction(fragment, currentPage, trackCallerEntry)
                    LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                        LaunchExitPopupConstant.Value.NAV_BAR_ACTION_GIF_SYNTHESIS, fragment
                            .trackAlbumPageInfo, picCount, videoCount
                    )
                }

                BasebizR.string.base_jigsaw_item -> {
                    doJigsawAction(fragment, currentPage, trackCallerEntry)
                    LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                        LaunchExitPopupConstant.Value.NAV_BAR_ACTION_COLLAGE, fragment
                            .trackAlbumPageInfo, picCount, videoCount
                    )
                }
            }
            CreationClickTrackHelper.trackCreationClickUserAction(
                getCreationItemId(item.titleId),
                VideoEditorTrackConstant.Value.ITEM_ENTRANCE_CREATION,
                getCreationEntrance(fragment)
            )
        }
    }

    /**
     * 返回点击创作后选择的itemID （拼图或GIF）
     * @param titleID 用户点击项
     */
    private fun getCreationItemId(titleId: Int): Int {
        return when (titleId) {
            BasebizR.string.gif_synthesis_item_create_name -> VideoEditorTrackConstant.Value.GENERATE_GIF
            BasebizR.string.base_jigsaw_item -> VideoEditorTrackConstant.Value.PUZZLE
            else -> -1
        }
    }

    /**
     * 获取点击创作的入口
     * @param fragment 当前属于哪个Fragment
     */
    private fun getCreationEntrance(fragment: Fragment): Int {
        return when (fragment) {
            is BaseAlbumFragment -> VideoEditorTrackConstant.Value.CREATION_ENTRANCE_ATLAS
            is BaseTimeNodeFragment -> VideoEditorTrackConstant.Value.CREATION_ENTRANCE_TIMELINE
            else -> VideoEditorTrackConstant.Value.CREATION_ENTRANCE_NONE
        }
    }

    private fun doPhotoToPdfAction(fragment: BaseFragment, trackCallerEntry: TrackCallerEntry) {
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItems()?.let {
                quickLoadingHelper.checkIfQuick(fragment.context, it) {
                    onPhotoToPdfClick(set, trackCallerEntry) { resultCode: String, resultMap: Map<String, Any>? ->
                        GLog.d(TAG, LogFlag.DL, "onPhotoToPdfClick on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
                        if (resultCode == MenuAction.RESULT_SUCCESS) {
                            selection?.get()?.exitSelectionMode()
                        }
                    }
                }
            }
        }
    }

    private fun onPhotoToPdfClick(
        set: Set<Path>,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: (String, Map<String, Any>?) -> Unit = { _, _ -> }
    ) {
        if (set.size > LIMIT_PIC_COUNT) {
            Toast.makeText(
                activity?.get(), activity?.get()?.resources?.getQuantityString(
                    BasebizR.plurals.base_convert_photo_to_pdf_exceed_limit,
                    LIMIT_PIC_COUNT,
                    LIMIT_PIC_COUNT
                ), Toast.LENGTH_SHORT
            ).show()
            GLog.e(TAG, LogFlag.DL, "onPhotoToPdfClick, outof limit count")
            return
        }

        val currentPathList: MutableList<String> = mutableListOf()
        var isContainVideo = false
        run loop@{
            set.forEach {
                val mediaItem = it.`object` as? MediaItem
                mediaItem?.let { item ->
                    item.filePath?.let { path ->
                        currentPathList.add(path)
                        isContainVideo = isContainVideo || item.isVideo
                        // 包含视频，提前退出循环
                        if (isContainVideo) return@loop
                    } ?: GLog.w(TAG, LogFlag.DF) { "onPhotoToPdfClick: filePath is null, mediaId = ${item.mediaId}" }
                }
            }
        }

        if (isContainVideo) {
            Toast.makeText(activity?.get(), activity?.get()?.getString(BasebizR.string.enter_error_has_video_tip), Toast.LENGTH_SHORT).show()
            GLog.e(TAG, LogFlag.DL, "onPhotoToPdfClick, Video cannot to pdf")
            return
        }
        val isSupportOcrScanner = ConfigAbilityWrapper.getBoolean(GET_SUPER_TEXT_V3_JUMP_OCR_SCANNER_SUPPORTED)
        GLog.d(TAG, LogFlag.DL) { "onPhotoToPdfClick, support OcrScanner is $isSupportOcrScanner" }
        if (isSupportOcrScanner) {
            menuResult = MenuOperationManager.doAction(
                action = MenuAction.PIC_TO_OCR_SCANNER,
                paramMap = activity?.get()?.let { activity ->
                    MenuActionGetter.photoToOcrScanner.builder
                        .setActivity(activity)
                        .isPDF(true)
                        .setPathList(set.toList())
                        .setTrackCallerEntry(trackCallerEntry)
                        .build()
                },
                onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                    onCompleted(resultCode, resultMap)
                }
            )
            return
        }
        menuResult = MenuOperationManager.doAction(
            action = MenuAction.PIC_TO_PDF,
            paramMap = activity?.get()?.let {
                MenuActionGetter.photoToPdf.builder
                    .setContext(it)
                    .setCurrentPathList(currentPathList)
                    .setPathList(set.toList())
                    .setTrackCallerEntry(trackCallerEntry)
                    .build()
            },
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                onCompleted(resultCode, resultMap)
            }
        )
    }

    private fun doGifAction(fragment: BaseFragment, currentPage: String?, trackCallerEntry: TrackCallerEntry) {
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItems()?.let {
                quickLoadingHelper.checkIfQuick(fragment.context, it) {
                    onGifClick(fragment, set, currentPage, trackCallerEntry) { resultCode: String, resultMap: Map<String, Any>? ->
                        GLog.d(TAG, LogFlag.DL, "onGifClick on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
                        if (resultCode == MenuAction.RESULT_INVALID) {
                            val checkResult =
                                resultMap?.get(GifSynthesisTipOperation.TAG_RESULT_MAP) as? GifSynthesisSpecificationChecker.CheckResult
                            checkResult?.let {
                                getGifActionErrorString(checkResult)?.let { strToastMessage ->
                                    ToastUtil.showShortBidirectionalToast(strToastMessage)
                                } ?: GLog.d(TAG, LogFlag.DL, "getGifActionErrorString is null")
                            } ?: GLog.e(TAG, LogFlag.DL, "doGifAction checkResult is null , resultMap = $resultMap")
                        } else {
                            selection?.get()?.exitSelectionMode()
                        }
                    }
                }
            }
        }
    }

    private fun getGifActionErrorString(result: GifSynthesisSpecificationChecker.CheckResult): String? = when (result.resultCode) {
        GifSynthesisSpecificationChecker.ResultCode.FAILED_NOT_ENOUGH_SPACE ->
            ContextGetter.context.resources.getString(BasebizR.string.base_send_no_storage_space)

        GifSynthesisSpecificationChecker.ResultCode.FAILED_PICTURE_COUNT_INVALID -> {
            when (result.validImagePaths.size) {
                //一张有效
                GifSynthesisTipOperation.RESULT_INVALID_COUNT_1 -> {
                    ContextGetter.context.resources.getQuantityString(
                        BasebizR.plurals.gif_synthesis_enter_error_min_support_tip,
                        GifSynthesisTipOperation.RESULT_INVALID_COUNT_1,
                        GifSynthesisTipOperation.RESULT_INVALID_COUNT_1
                    )
                }
                //大于50张有效
                else -> {
                    ContextGetter.context.resources.getQuantityString(
                        BasebizR.plurals.gif_synthesis_enter_error_max_support_tip,
                        GifSynthesisTipOperation.RESULT_MAX_COUNT_50,
                        GifSynthesisTipOperation.RESULT_MAX_COUNT_50
                    )
                }
            }
        }

        GifSynthesisSpecificationChecker.ResultCode.FAILED_IMG_NUMBER_NOT_ENOUGH -> {
            ContextGetter.context.resources.getQuantityString(
                BasebizR.plurals.gif_synthesis_delete_action_waring_tip,
                GIF_SYNTHESIS_MIN_COUNT,
                GIF_SYNTHESIS_MIN_COUNT
            )
        }
        //含有视频
        GifSynthesisSpecificationChecker.ResultCode.FAILED_HAS_VIDEO ->
            ContextGetter.context.resources.getString(BasebizR.string.base_synthesis_enter_error_has_video_tip)
        // 含有超过像素限制的图片
        GifSynthesisSpecificationChecker.ResultCode.FAILED_PICTURE_PIXEL_INVALID ->
            ContextGetter.context.resources.getString(BasebizR.string.gif_synthesis_over_resolution_error_tip)
        // 不符合要求的图片中只有格式不支持
        GifSynthesisSpecificationChecker.ResultCode.FAILED_PICTURE_FORMAT_INVALID ->
            ContextGetter.context.resources.getString(BasebizR.string.gif_synthesis_enter_error_not_support_tip)

        else -> null
    }

    private fun onGifClick(
        fragment: BaseFragment,
        set: Set<Path>,
        currentPage: String?,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: (String, Map<String, Any>?) -> Unit = { _, _ -> }
    ) {
        menuResult = MenuOperationManager.doAction(
            action = MenuAction.GIF_SYNTHESIS,
            paramMap = MenuActionGetter.gifSynthesis.builder
                .setPathList(set.toList())
                .setFragment(fragment)
                .setCurrentPage(currentPage)
                .setTrackCallerEntry(trackCallerEntry)
                .setLifecycle(fragment.lifecycle)
                .build(),
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                onCompleted(resultCode, resultMap)
            }
        )
    }

    fun doJigsawAction(fragment: BaseFragment, currentPage: String?, trackCallerEntry: TrackCallerEntry) {
        selection?.get()?.submitSelection { set ->
            //判断是否需要loading quick图，loading结束执行进入拼图
            selection?.get()?.getSelectedItems()?.let {
                quickLoadingHelper.checkIfQuick(fragment.context, it) {
                    selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                        val imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                        val videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                        if (canCollegeImage(imageCount, videoCount)) {
                            onCollageClick(fragment, set, currentPage, trackCallerEntry) { resultCode: String, _: Map<String, Any>? ->
                                when (resultCode) {
                                    RESULT_ERROR_NOT_SUPPORT_RATIO -> {
                                        ToastUtil.showShortToast(
                                            ContextGetter.context.resources.getString(BasebizR.string.common_collage_unsupported_photo)
                                        )
                                    }

                                    else ->
                                        selection?.get()?.exitSelectionMode()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun moveToSafeBox(
        lifecycle: Lifecycle,
        pathList: List<Path>,
        imageCount: Int,
        videoCount: Int,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        showSafeBoxProgressDialog(pathList)
        callbackWrapper?.invoke(null, ACTION_STATE_START)

        doMoveToSafeBox(
            lifecycle,
            pathList,
            MenuAction.MOVE_TO_SAFE_BOX,
            trackCallerEntry,
            createSafeBoxCompleteAction(pathList, actionCallback = { state ->
                callbackWrapper?.invoke(null, state)
            })
        )

        AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
            currentPage = trackCallerEntry.albumsActionCurrentPage,
            imageCount = imageCount.toString(),
            videoCount = videoCount.toString(),
            itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_SET_ENCRYPT_VALUE,
            albumName = trackCallerEntry.currentAlbumName,
            path = trackCallerEntry.path
        )
    }

    /**
     * 将选中的图集集合移到私密保险箱中
     */
    private fun moveAlbumSetToSafeBox(
        lifecycle: Lifecycle,
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        showSafeBoxProgressDialog(pathList)
        callbackWrapper?.invoke(null, ACTION_STATE_START)

        doMoveToSafeBox(
            lifecycle,
            pathList,
            MenuAction.MOVE_ALBUM_TO_SAFE_BOX,
            trackCallerEntry,
            createSafeBoxCompleteAction(pathList, actionCallback = { state ->
                callbackWrapper?.invoke(null, state)
            })
        )
    }

    /**
     * 将选中的图集移到私密保险箱中
     */
    fun doAlbumSafeBoxAction(
        context: Context?,
        lifecycle: Lifecycle,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        activity?.get()?.let {
            if (!isSupportUserCustomSafeBox()) {
                ToastUtil.showShortToast(BasebizR.string.base_feature_is_disabled_tip)
                return
            }
            //图集加密先判断是否需要下载原图
            downloadAndSubmitSelection(context, lifecycle, null, trackCallerEntry, callbackWrapper)
        }
    }

    fun doSafeBoxAction(
        context: Context?,
        lifecycle: Lifecycle,
        itemPath: Path? = null,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        activity?.get()?.let {
            if (!isSupportUserCustomSafeBox()) {
                ToastUtil.showShortToast(BasebizR.string.base_feature_is_disabled_tip)
                return
            }
            itemPath?.apply {
                // 如果itemPath不为null，表示是从大图-更多-设为私密，大图也会下载原图，这里就不需要再下载原图
                moveToSafeBoxPrepareData(context, lifecycle, itemPath, trackCallerEntry, callbackWrapper)
            } ?: run {
                // 从时间轴和图集界面"设为私密"的时候先判断是否需要下载原图，大图页"设为私密"在PhotoPage中处理
                downloadAndSubmitSelection(context, lifecycle, itemPath, trackCallerEntry, callbackWrapper)
            }
        }
    }

    private fun moveToSafeBoxPrepareData(
        context: Context?,
        lifecycle: Lifecycle,
        itemPath: Path? = null,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        var imageCount = 0
        var videoCount = 0
        val pathList = ArrayList<Path>()
        itemPath?.apply {
            pathList.add(this)
            if (((this.`object` as MediaItem).mediaType == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO)) {
                videoCount = NUM_ONE
            } else {
                imageCount = NUM_ONE
            }
            showSafeBoxConfirmDialog(pathList, lifecycle) {
                moveToSafeBox(lifecycle, pathList, imageCount, videoCount, trackCallerEntry, callbackWrapper)
            }
        } ?: run {
            selection?.get()?.let {
                it.submitSelection { set ->
                    //判断是否需要loading quick图，loading结束执行设为私密
                    quickLoadingHelper.checkIfQuick(context, set) {
                        pathList.addAll(set)
                        if (isSelectAlbum(pathList)) {
                            showSafeBoxConfirmDialog(pathList, lifecycle) {
                                moveAlbumSetToSafeBox(lifecycle, pathList, trackCallerEntry, callbackWrapper)
                            }
                        } else {
                            it.getSelectedItemImageAndVideoCount { bundle ->
                                imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                                videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                                showSafeBoxConfirmDialog(pathList, lifecycle) {
                                    moveToSafeBox(lifecycle, pathList, imageCount, videoCount, trackCallerEntry, callbackWrapper)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun showSafeBoxConfirmDialog(
        pathList: List<Path>,
        lifecycle: Lifecycle,
        onConfirmAction: () -> Unit
    ) {
        // 优先显示云服务的私密确认弹框，无需则弹普通的私密确认弹框
        if (ApiDmManager.getCloudSyncPageDM().needShowCloudDeleteConfirmDialog() != true) {
            tryShowCommonSafeBoxConfirmDialog(pathList, onConfirmAction)
            return
        }
        val intentSenderNotifier = activity?.get()?.intentSenderNotifier ?: let {
            GLog.e(TAG, LogFlag.DL, "showSafeBoxConfirmDialog activity:${activity?.get()}")
            tryShowCommonSafeBoxConfirmDialog(pathList, onConfirmAction)
            return
        }

        lifecycle.coroutineScope.launch(Dispatchers.IO) {
            val itemPathList = getSelectItemPathList(pathList)
            ApiDmManager.getCloudSyncPageDM().tryShowMoveToSafeBoxConfirmDialog(
                intentSenderNotifier,
                toLocalIdList(itemPathList),
                confirmedCallback = { onConfirmAction.invoke() },
                canceledCallback = null,
                showCallback = {
                    if (it.not()) {
                        runOnUiThread {
                            // 云弹框没有弹出来则弹普通弹框
                            tryShowCommonSafeBoxConfirmDialog(pathList, onConfirmAction)
                        }
                    }
                })
        }
    }

    /**
     * 显示通用的加入私密提示框
     */
    @Suppress("LongMethod")
    private fun tryShowCommonSafeBoxConfirmDialog(
        pathList: List<Path>,
        onConfirmAction: () -> Unit
    ) {
        if (SafeBoxHelper.isSafeBoxNotRemindAgain()) {
            GLog.d(TAG, LogFlag.DL) { "tryShowCommonSafeBoxConfirmDialog isSafeBoxNotRemindAgain" }
            onConfirmAction.invoke()
            return
        }
        //如果是coloros版本是15.0以上，使用新的message提示
        val messageId = if (OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)) {
            if (ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN)) {
                BasebizR.string.base_encrypt_confirm_content_v
            } else {
                BasebizR.string.base_encrypt_confirm_content_v_export
            }
        } else {
            BasebizR.string.base_encrypt_confirm_content
        }
        runWhenActivityRunning({
            val contextNonNull = activity?.get() ?: return@runWhenActivityRunning
            val isAlbum = isSelectAlbum(pathList)
            val title = if (isAlbum) {
                BasebizR.plurals.base_encrypt_folder_confirm_title
            } else {
                BasebizR.plurals.base_encrypt_confirm_title
            }
            val message = if (isAlbum) {
                BasebizR.string.base_encrypt_filder_confirm_content
            } else {
                BasebizR.string.base_encrypt_confirm_content
            }
            SecurityDialog.Builder(contextNonNull)
                .setMessage(message)
                .setHasCheckBox(true)
                .setNegativeString(BasebizR.string.common_cancel)
                .setPositiveString(BasebizR.string.common_confirm)
                .setOnSelectedListener { whichButton, isCheck ->
                    when (whichButton) {
                        DialogInterface.BUTTON_NEGATIVE -> menuResult?.cancel()
                        DialogInterface.BUTTON_POSITIVE -> {
                            SafeBoxHelper.setSafeBoxNotRemindAgain(isCheck)
                            onConfirmAction()
                        }
                    }
                }
                .setCancelable(false)
                .setTitle(
                    ContextGetter.context.resources.getQuantityString(
                        title,
                        pathList.size,
                        pathList.size
                    )
                )
                .setOnKeyListener { dialog, keyCode, event ->
                    if ((keyCode == KeyEvent.KEYCODE_BACK) && (event.action == KeyEvent.ACTION_UP)) {
                        menuResult?.cancel()
                        dialog.dismiss()
                        return@setOnKeyListener true
                    }
                    return@setOnKeyListener false
                }
                .build()
                .show().apply {
                    this.realDialog.addBackCallback {
                        menuResult?.cancel()
                        dismiss()
                    }
                }
        }, {
            GLog.w(TAG, LogFlag.DL, "tryShowCommonSafeBoxConfirmDialog can not show")
            menuResult?.cancel()
        })
    }

    /**
     * 尝试显示云服务的删除确认提示框
     * @param pathList 要被删除的item path
     * @param actionId 执行动作
     * @param confirmedCallback 点击确认的回调
     * @param cancelCallback 点击取消或dismiss的回调
     * @param showCallback 显示结果回调，DialogDelegate为空代表显示失败
     */
    @Suppress("LongParameterList")
    private fun tryToShowCloudDeleteConfirmDialog(
        pathList: List<Path>,
        actionId: String = TextUtil.EMPTY_STRING,
        confirmedCallback: (() -> Unit)? = null,
        cancelCallback: (() -> Unit)? = null,
        showCallback: ((DialogDelegate?) -> Unit)? = null
    ) {
        val intentSenderNotifier = activity?.get()?.intentSenderNotifier ?: let {
            GLog.e(TAG, LogFlag.DL, "tryToShowCloudDeleteConfirmDialog activity:${activity?.get()}")
            showCallback?.invoke(null)
            return
        }
        val dialogDelegate = object : DialogDelegate {

            var dismissListener: (() -> Unit)? = null

            override fun cancel() = Unit

            override fun setOnDismissListener(listener: () -> Unit) {
                dismissListener = listener
            }
        }
        val innerConfirmedCallback: () -> Unit = {
            dialogDelegate.dismissListener?.invoke()
            confirmedCallback?.invoke()
        }
        val innerCanceledCallback: () -> Unit = {
            dialogDelegate.dismissListener?.invoke()
            cancelCallback?.invoke()
        }
        val innerShowCallback: (Boolean) -> Unit = { isShowing: Boolean ->
            if (isShowing) {
                showCallback?.invoke(dialogDelegate)
            } else {
                showCallback?.invoke(null)
            }
        }
        if (actionId == ACTION_DELETE_ALL_RECYCLED) {
            ApiDmManager.getCloudSyncPageDM().tryShowDeleteAllConfirmDialog(
                intentSenderNotifier = intentSenderNotifier,
                confirmedCallback = innerConfirmedCallback,
                canceledCallback = innerCanceledCallback,
                showCallback = innerShowCallback
            )
        } else {
            val loadingDialogCallback: (Boolean) -> Unit = { showDialog: Boolean ->
                if (showDialog) {
                    showLoadingDialog(R.string.common_msg_loading, 0, DIALOG_DELAY_500)
                } else {
                    releaseDialogHelper()
                }
            }
            ApiDmManager.getCloudSyncPageDM().tryShowDeleteConfirmDialog(
                intentSenderNotifier = intentSenderNotifier,
                localIdList = toLocalIdList(pathList),
                confirmedCallback = innerConfirmedCallback,
                canceledCallback = innerCanceledCallback,
                showCallback = innerShowCallback,
                loadingDialogCallback = loadingDialogCallback
            )
        }
    }

    private fun toLocalIdList(pathList: List<Path>): List<Int> {
        var list: List<Int> = emptyList()
        runCatching {
            list = pathList.map { it.suffix.toInt() }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "toLocalIdList fail, pathList:$pathList")
        }
        return list
    }

    private fun downloadAndSubmitSelection(
        context: Context?,
        lifecycle: Lifecycle,
        itemPath: Path? = null,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        selection?.get()?.submitSelection { set ->
            activity?.get()?.let {
                lifecycle.coroutineScope.launch(Dispatchers.IO) {
                    val dataList = getSelectItemPathList(set.toList())
                    FileProcessTaskBuilder(
                        it,
                        dataList,
                        object : FinishListener {
                            override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                                if (!success) return
                                moveToSafeBoxPrepareData(context, lifecycle, itemPath, trackCallerEntry, callbackWrapper)
                            }
                        },
                        scene = FileProcessScene.DOWNLOAD_ORIGIN_SCENE_MOVE_TO_SAFE_BOX
                    )
                        .addTaskType(FileProcessTaskBuilder.TASK_TYPE_DOWNLOAD_ORIG)
                        .build()?.let {
                            it.execute()
                            it.autoCancelWhenOnDestroy(lifecycle)
                        }
                }
            }
        }
    }

    /**
     * 1、在照片页选择的文件，直接返回照片集合
     * 2、在图集或其他图集页，选择图集，获取图集里所有文件的path路径后，返回文件path集合
     *  @param  selectList: 选择的文件或图集
     *  @return 所有选中文件的Path集合
     */
    private fun getSelectItemPathList(selectList: List<Path>): List<Path> {
        val itemPathList = ArrayList<Path>()
        if (isSelectAlbum(selectList)) {
            selectList.forEach {
                /* 此处 path.object 为空不会是该 object 持有的 MediaSet 的弱引用被 GC 后为空（此函数是由正在使用此 MediaSet 的页面调
                   用的，所以当前 MediaSet 被强引用持有，不会被 GC），所以此处如果 object 为空，则只可能是 MediaSet 对应的图集被删除，无
                   需再通过 DataManager 重新获取 MediaSet 对象 */
                val mediaAlbum = it.`object` as? MediaAlbum
                mediaAlbum?.count?.let { size -> mediaAlbum.getSubMediaItem(0, size) }?.forEach { mediaItem ->
                    itemPathList.add(mediaItem.path)
                }
            }
        } else {
            itemPathList.addAll(selectList)
        }
        return itemPathList
    }

    /**
     * 判断选择的集合是照片集合，还是图集集合
     */
    private fun isSelectAlbum(selectList: List<Path>): Boolean {
        return selectList.isNotEmpty() && (selectList[0].`object` is MediaAlbum)
    }

    fun doShareAction(
        context: Context?,
        bundle: Bundle,
        onCompleteCallback: ((state: Int) -> Unit)? = null,
        focusPath: Path? = null,
        focusPosition: Int = -1,
        trackCallerEntry: TrackCallerEntry
    ) {
        val activity = activity?.get()
        if (activity == null) {
            GLog.e(TAG, LogFlag.DL, "doShareAction: activity is $activity")
            return
        }
        selection?.get()?.getSelectedItems()?.let {
            //判断是否需要loading quick图，loading结束执行进入分享
            quickLoadingHelper.checkIfQuick(context, it) {
                GLog.d(TAG, LogFlag.DL, "[doShareAction] is not quick")
                ShareHelper.showShareDialog(
                    activity = activity as BaseActivity,
                    bundle = bundle,
                    paths = it,
                    itemPath = focusPath,
                    focusPosition = focusPosition,
                    updateShowCallback = onCompleteCallback,
                    trackCallerEntry = trackCallerEntry,
                )
            }
        }
    }

    private fun onSafeBoxResult(result: String, resultMap: Map<String, Any>?, showToast: Boolean = false) {
        if ((result == MenuAction.RESULT_FAILED_UNKNOWN) || (result == MenuAction.RESULT_FAILED)) {
            return
        }
        if (result == MoveToSafeBoxAction.RESULT_ERROR_GET_SERVICE_NULL) {
            GLog.w(TAG, LogFlag.DL, "onSafeBoxResult get service fail")
            if (showToast) {
                ToastUtil.showLongToast(R.string.main_safe_box_service_initializing)
            }
            return
        }

        val failedCount: Int = (resultMap?.get(MoveToSafeBoxAction.KEY_FAILED_COUNT) as? Int) ?: 0
        val filterCount: Int = (resultMap?.get(MoveToSafeBoxAction.KEY_FILTERED_COUNT) as? Int) ?: 0
        val succeed = (failedCount == 0)
        val noPicturesLoading = (filterCount == 0)
        when {
            succeed && noPicturesLoading -> {
                if (SafeBoxHelper.isSafeBoxFirstUsed()) {
                    showSafeBoxEntryTipsDialog()
                } else {
                    if (showToast) {
                        ToastUtil.showShortToast(
                            activity?.get()?.resources?.getString(BasebizR.string.common_encrypt_success_dialog_title)
                        )
                    }
                    SafeBoxHelper.updateSafeBoxTips()
                }
            }

            showToast && succeed && !noPicturesLoading -> {
                ToastUtil.showShortToast(
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.common_encrypt_image_filter_failed_tip,
                        filterCount, filterCount
                    )
                )
            }

            showToast && !succeed -> {
                val totalFailedCount: Int = failedCount + filterCount
                ToastUtil.showShortToast(
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.common_encrypt_failed_hint_mix,
                        totalFailedCount, totalFailedCount
                    )
                )
            }
        }
    }

    private fun createSafeBoxCompleteAction(list: List<Path>, actionCallback: ((state: Int) -> Unit)? = null) =
        { result: String, resultMap: Map<String, Any>? ->
            runWhenActivityRunning(onDo = {
                GLog.d(TAG, LogFlag.DL) { "[createSafeBoxCompleteAction] onDo result: $result" }
                selectCount = selection?.get()?.getSelectedItemCount() ?: list.size

                if (SafeBoxHelper.isSupportSuperEncryption()) {
                    safeBoxProgressDialog?.let { dialog ->
                        if (dialog.isShowing()) {
                            dialog.setOnDismissListener(object : SafeBoxProgressDialog.OnDismissListener {
                                override fun onDismiss() {
                                    runWhenActivityRunning({
                                        onSafeBoxResult(result, resultMap)
                                    })
                                }
                            })
                        }
                    }
                } else {
                    onSafeBoxResult(result, resultMap, true)
                }

                val failedCount: Int = (resultMap?.get(MoveToSafeBoxAction.KEY_FAILED_COUNT) as? Int) ?: 0
                actionCallback?.invoke(ACTION_STATE_COMPLETED)
                /**
                 * 移入私密保险箱result有两种类型：RESULT_FAILED和RESULT_SUCCESS
                 * RESULT_FAILED场景：
                 * 1、引导弹窗取消，包括开启私密保险箱弹窗和设置隐私密码弹窗
                 * RESULT_SUCCESS场景:
                 * 1、图片已全部移入图集
                 * 2、图片部分移入图集：手动取消移入私密图集进度弹窗
                 */
                if (result == MenuAction.RESULT_FAILED) {
                    // 如果私密保险箱没设置密码，就会返回失败，按照之前逻辑直接dismiss
                    safeBoxProgressDialog?.dismiss()
                } else {
                    selection?.get()?.exitSelectionMode()
                    safeBoxProgressDialog?.taskFinished(failedCount)
                }
            }, onCancel = {
                GLog.d(TAG, LogFlag.DL) { "[createSafeBoxCompleteAction] onCancel" }
            })
            safeBoxProgressDialog = null
        }

    private fun photoCreation(
        fragment: BaseFragment,
        imageCount: Int,
        videoCount: Int,
        bottomNavigationBar: BottomNavigationView?,
        set: Set<Path>,
        currentPage: String?,
        trackCallerEntry: TrackCallerEntry,
        albumNavigationTrackPageInfo: String?
    ) {
        GLog.d(TAG, LogFlag.DL, "photoCreation: imageCount: $imageCount, videoCount: $videoCount, path set: $set")
        bottomNavigationBar?.showSubMenuPopWindow(
            BasebizR.id.action_photo_creation,
            { itemList ->
                itemList.add(MenuListPopupItem(BasebizR.string.base_jigsaw_item, false, true))
            }) { _, item ->
            when (item.titleId) {
                BasebizR.string.base_jigsaw_item -> {
                    if (canCollegeImage(imageCount, videoCount)) {
                        LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                            LaunchExitPopupConstant.Value.NAV_BAR_ACTION_COLLAGE,
                            albumNavigationTrackPageInfo, imageCount, videoCount
                        )
                        CreationClickTrackHelper.trackCreationClickUserAction(
                            VideoEditorTrackConstant.Value.PUZZLE,
                            VideoEditorTrackConstant.Value.ITEM_ENTRANCE_TIMELINE,
                            VideoEditorTrackConstant.Value.CREATION_ENTRANCE_NONE
                        )
                        onCollageClick(fragment, set, currentPage, trackCallerEntry) { resultCode: String, _: Map<String, Any>? ->
                            when (resultCode) {
                                RESULT_ERROR_NOT_SUPPORT_RATIO -> {
                                    ToastUtil.showShortToast(
                                        ContextGetter.context.resources.getString(
                                            BasebizR.string.common_collage_unsupported_photo
                                        )
                                    )
                                }

                                else ->
                                    selection?.get()?.exitSelectionMode()
                            }
                        }
                    }
                }
            }
        }
    }

    private fun canCollegeImage(imageCount: Int, videoCount: Int): Boolean {
        if (videoCount > 0) {
            ToastUtil.showShortToast(BasebizR.string.base_synthesis_enter_error_has_video_tip)
            return false
        }
        if (imageCount !in MINIMUM_ITEM_FOR_JIGSAW..MAXIMUM_ITEM_FOR_JIGSAW) {
            ToastUtil.showShortToast(
                String.format(
                    ContextGetter.context.resources.getString(BasebizR.string.base_collage_image_tips),
                    MINIMUM_ITEM_FOR_JIGSAW,
                    MAXIMUM_ITEM_FOR_JIGSAW
                )
            )
            return false
        }
        return true
    }

    private fun doMoreMenu(
        fragment: BaseFragment,
        imageCount: Int,
        videoCount: Int,
        bottomNavigationBar: BottomNavigationView?,
        set: Set<Path>,
        albumPath: Path?,
        callbackWrapper: LifecycleCallbackWrapper?,
        trackCallerEntry: TrackCallerEntry,
        anchorItemId: Int,
        menuList: ArrayList<MenuListPopupItem>,
        oliveCount: Int = 0
    ) {
        GLog.d(TAG, LogFlag.DL, "doMoreMenu: imageCount: $imageCount, videoCount: $videoCount, path set: $set")
        if (anchorItemId == 0) {
            GLog.e(TAG, LogFlag.DL, "doMoreMenu, moreMenuId is invalid")
            return
        }

        bottomNavigationBar?.showSubMenuPopWindow(
            anchorItemId,
            { itemList ->
                itemList.addAll(menuList)
                itemList.add(MenuListPopupItem(BasebizR.string.base_convert_photo_to_pdf_photopage, false, true))
            }) { _, item ->
            onMoreSubMenuItemClick(item, albumPath, trackCallerEntry, Triple(imageCount, videoCount, oliveCount), fragment, callbackWrapper)
        }
    }

    private fun onMoreSubMenuItemClick(
        item: MenuListPopupItem,
        albumPath: Path?,
        trackCallerEntry: TrackCallerEntry,
        countTriple: Triple<Int, Int, Int>,
        fragment: BaseFragment,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        when (item.titleId) {
            BasebizR.string.base_convert_photo_to_pdf_photopage -> {
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_PIC_TO_PDF,
                    trackCallerEntry.trackAlbumPageInfo, countTriple.first, countTriple.second
                )
                doPhotoToPdfAction(fragment, trackCallerEntry)
            }

            BasebizR.string.base_move_to_gallery -> {
                albumPath?.let {
                    LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                        LaunchExitPopupConstant.Value.NAV_BAR_ACTION_ADD_TO,
                        trackCallerEntry.trackAlbumPageInfo, countTriple.first, countTriple.second
                    )
                    doAppendToAction(fragment, it, trackCallerEntry = trackCallerEntry, isFromTimeline = false, callbackWrapper)
                } ?: GLog.e(TAG, LogFlag.DL, "doMoreMenu, action_append_to path = null")
            }

            BasebizR.string.base_safe_encryption_nemu -> {
                doSafeBoxAction(
                    fragment.context,
                    fragment.lifecycle,
                    trackCallerEntry = trackCallerEntry,
                    callbackWrapper = callbackWrapper
                )
                //埋点2006000008：时间轴或图集内等图片列表页进入选择状态后在底部菜单栏点击操作
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_ENCRYPT,
                    trackCallerEntry.trackAlbumPageInfo,
                    countTriple.first,
                    countTriple.second
                )
            }

            BasebizR.string.base_copy_to -> {
                albumPath?.let {
                    LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                        LaunchExitPopupConstant.Value.NAV_BAR_ACTION_COPY_TO,
                        trackCallerEntry.trackAlbumPageInfo, countTriple.first, countTriple.second
                    )
                    doCopyToAction(fragment, it, trackCallerEntry = trackCallerEntry, callbackWrapper)
                } ?: GLog.e(TAG, LogFlag.DL, "doMoreMenu, action_copy_to path = null")
            }

            BasebizR.string.base_export_video -> {
                albumPath?.let {
                    LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                        LaunchExitPopupConstant.Value.NAV_BAR_ACTION_EXPORT_TO_VIDEO,
                        trackCallerEntry.trackAlbumPageInfo, countTriple.first, countTriple.second
                    )
                    if (checkOliveCountValidity(countTriple.third).not()) return
                    doExportToVideoAction(fragment, it, trackCallerEntry = trackCallerEntry, callbackWrapper)
                } ?: GLog.e(TAG, LogFlag.DL, "doMoreMenu, action_export_to_video path = null")
            }
        }
    }

    /**
     * 另存为视频菜单操作
     * @param fragment 启动界面的fragment，用于后续启动弹窗界面选择
     * @param albumPath 选中的文件都隶属于的 albumSet 的 path
     * @param trackCallerEntry 埋点统计
     * @param callbackWrapper 菜单状态回调
     */
    private fun doExportToVideoAction(
        fragment: Fragment,
        albumPath: Path? = null,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        menuEventNotifier = MenuOperation.EventNotifier()
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        albumPath?.let {
            selection?.get()?.submitSelection { set ->
                val paths = ArrayList(set)
                MenuOperationManager.doAction(
                    MenuAction.EXPORT_VIDEO,
                    MenuActionGetter.exportVideo.builder
                        .setPaths(paths)
                        .setFragment(fragment)
                        .setAlbumSetPath(albumPath.toString())
                        .setTrackCallerEntry(trackCallerEntry)
                        .setEventNotifier(menuEventNotifier)
                        .setLifecycle(fragment.lifecycle)
                        .build(),
                    onCompleted = { result, resultMap: Map<String, Any>? ->
                        GLog.w(TAG, LogFlag.DL, "result=$result")
                        if (result == MenuAction.RESULT_CANCEL) {
                            callbackWrapper?.invoke(paths, ACTION_STATE_CANCEL)
                            return@doAction
                        }
                        selection?.get()?.exitSelectionMode()
                        callbackWrapper?.invoke(paths, ACTION_STATE_COMPLETED)
                    }
                )
            }
        } ?: GLog.e(TAG, LogFlag.DL, "doExportToVideoAction, album path = null")
    }

    private fun checkOliveCountValidity(oliveCount: Int): Boolean {
        if (oliveCount > MAXIMUM_ITEM_OLIVE) {
            ToastUtil.showShortToast(
                activity?.get()?.resources?.getQuantityString(
                    BasebizR.plurals.base_single_saved_video_max_limit_toast,
                    MAXIMUM_ITEM_OLIVE,
                    MAXIMUM_ITEM_OLIVE
                )
            )
            return false
        }
        return true
    }

    private fun onCollageClick(
        fragment: BaseFragment,
        set: Set<Path>,
        currentPage: String?,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: (String, Map<String, Any>?) -> Unit
    ) {
        menuResult = MenuOperationManager.doAction(
            MenuAction.COLLAGE, MenuActionGetter.collage.builder
                .setPathList(set.toList())
                .setFragment(fragment)
                .setCurrentPage(currentPage)
                .setTrackCallerEntry(trackCallerEntry)
                .setLifecycle(fragment.lifecycle)
                .build(),
            onCompleted
        )
        selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
            AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                currentPage = trackCallerEntry.albumsActionCurrentPage,
                imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0).toString(),
                videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0).toString(),
                itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_COLLAGE_VALUE,
                albumName = trackCallerEntry.currentAlbumName,
                path = trackCallerEntry.path
            )
        }
    }

    private fun doMoveToSafeBox(
        lifecycle: Lifecycle,
        list: List<Path>,
        menuAction: Int = MenuAction.MOVE_TO_SAFE_BOX,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: (String, Map<String, Any>?) -> Unit
    ) {
        GLog.e(TAG, LogFlag.DL, "doMoveToSafeBox: path list$list")
        val paramMap = MenuActionGetter.safeBox.builder
            .setPathList(list)
            .setTrackCallerEntry(trackCallerEntry)
            .setLifecycle(lifecycle)
            .build()
        // Start SafeBox action
        menuResult = MenuOperationManager.doAction(
            menuAction, paramMap, onCompleted
        ) { progress ->
            safeBoxProgressDialog?.setProgress(progress)
        }
    }

    private fun showSafeBoxProgressDialog(paths: List<Path>) {
        runWhenActivityRunning({
            val contextNonNull = activity?.get() ?: return@runWhenActivityRunning
            safeBoxProgressDialog = SafeBoxProgressDialog().build(paths.size, contextNonNull) {
                menuResult?.cancel()
                // 点击取消按钮后，退出选择模式
                selection?.get()?.exitSelectionMode()
            }
            safeBoxProgressDialog?.show()
        }, {
            GLog.w(TAG, LogFlag.DL, "[showSafeBoxProgressDialog] can not show")
            menuResult?.cancel()
        })
    }

    private fun showSafeBoxEntryTipsDialog() {
        activity?.get()?.let {
            GuideDialogBuilder(it).apply {
                /*
                根据平板侧边栏、折叠屏侧边栏和无侧边栏场景设置示意图和提示语
                 */
                if (ConfigAbilityWrapper.getBoolean(IS_TABLET) && (ScreenUtils.isLargeOrMediumScreenStyle(it))) {
                    setCustomImage(R.drawable.base_safe_box_menu_entry_tips_pad)
                    setCustomMessage(BasebizR.string.base_safe_box_menu_side_encrypt_entry_tips_dialog_msg)
                } else if (ConfigAbilityWrapper.getBoolean(IS_FOLD) && ScreenUtils.isLargeOrMediumScreenStyle(it)) {
                    setCustomImage(R.drawable.base_safe_box_menu_entry_tips_fold)
                    setCustomMessage(BasebizR.string.base_safe_box_menu_side_encrypt_entry_tips_dialog_msg)
                } else {
                    setCustomImage(R.drawable.base_safe_box_menu_entry_tips)
                    setCustomMessage(BasebizR.string.base_safe_box_menu_encrypt_entry_tips_dialog_msg)
                }

                setCustomTitle(BasebizR.string.base_safe_box_menu_entry_tips_dialog_title)
                setPositiveButton(BasebizR.string.common_ok) { _, _ ->
                    SafeBoxHelper.setSafeBoxFirstUsed(false)
                    SafeBoxHelper.updateSafeBoxTips()
                }
                setOnKeyListener { _, keyCode, event ->
                    if ((keyCode == KeyEvent.KEYCODE_BACK) && (event.action == KeyEvent.ACTION_UP)) {
                        SafeBoxHelper.setSafeBoxFirstUsed(false)
                        SafeBoxHelper.updateSafeBoxTips()
                    }
                    true
                }
            }.show().apply {
                addBackCallback {
                    SafeBoxHelper.setSafeBoxFirstUsed(false)
                    SafeBoxHelper.updateSafeBoxTips()
                }
            }
        }
    }

    /**
     * 大图下的“不是此人”,“不是TA”,"不是TA们"按钮 - 需要单独的一个path
     *  @return 显示的弹窗
     */
    @JvmOverloads
    fun doFreeSingleFaceAction(
        itemPath: Path,
        isImage: Boolean,
        groupId: Long,
        albumSetType: Int = TYPE_PERSON_ALBUM_SET,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: (String, Map<String, Any>?) -> Unit = { _, _ -> }
    ): BaseAlertDialog<out Any>? = activity?.get()?.resources?.run {
        val title = if (isImage) {
            getString(BasebizR.string.base_characters_album_remove_selection_this_photo)
        } else {
            getString(BasebizR.string.base_characters_album_remove_selection_this_video)
        }
        showActionDialog(
            confirmMsg = getString(BasebizR.string.base_side_pane_remove),
            title = title,
            actionId = ACTION_NOT_THIS_PERSON
        ) { which ->
            if (which != DialogInterface.BUTTON_NEUTRAL) {
                onCompleted(MenuAction.RESULT_CANCEL, null)
                return@showActionDialog
            }
            freeFaceFromGroup(listOf(itemPath), groupId, trackCallerEntry, albumSetType, onCompleted)
        }
    } ?: let {
        onCompleted(MenuAction.RESULT_FAILED, null)
        GLog.e(TAG, LogFlag.DL, "doFreeSingleFaceAction: Activity get is ${activity?.get()}")
        null
    }

    /**
     * 人宠详情选择模式下的“不是TA” 和 合照详情选择模式下的“不是TA们” - 可以从selection中获取所有path
     */
    fun doFreeFaceFromSelectionAction(groupId: Long, trackCallerEntry: TrackCallerEntry, albumSetType: Int = TYPE_PERSON_ALBUM_SET) {
        var imageCount = 0
        var videoCount = 0
        val resource: Resources? = activity?.get()?.resources
        if (resource == null) {
            GLog.e(TAG, LogFlag.DL, "doFreeFaceFromSelectionAction: Activity get is ${activity?.get()}")
            return
        }
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                val title = when {
                    imageCount == 0 -> resource.getQuantityString(BasebizR.plurals.base_characters_move_selection_video, videoCount, videoCount)
                    videoCount == 0 -> resource.getQuantityString(BasebizR.plurals.base_characters_merge_selection_photo, imageCount, imageCount)
                    else -> {
                        resource.getQuantityString(
                            BasebizR.plurals.base_characters_move_selection_project,
                            (imageCount + videoCount),
                            (imageCount + videoCount)
                        )
                    }
                }
                showActionDialog(
                    confirmMsg = resource.getString(BasebizR.string.base_side_pane_remove),
                    title = title,
                    actionId = ACTION_NOT_THIS_PERSON
                ) { which ->
                    AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                        currentPage = trackCallerEntry.albumsActionCurrentPage,
                        imageCount = imageCount.toString(),
                        videoCount = videoCount.toString(),
                        itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_REMOVE_FACE_ALBUM_VALUE,
                        albumName = trackCallerEntry.currentAlbumName,
                        path = trackCallerEntry.path,
                        operResult = if (which == DialogInterface.BUTTON_NEUTRAL) ALBUMS_ACTION_DIALOG_CLICK_OK else ALBUMS_ACTION_DIALOG_CLICK_CANCEL
                    )
                    if (which != DialogInterface.BUTTON_NEUTRAL) return@showActionDialog
                    freeFaceFromGroup(set.toList(), groupId, trackCallerEntry, albumSetType)
                }
            }
        }
    }

    /**
     * 选择模式下的“不是此类” - 可以从selection中获取所有path
     */
    fun doRemoveLabelFromSelectionAction(labelId: Int, labelName: String, trackCallerEntry: TrackCallerEntry) {
        var imageCount = 0
        var videoCount = 0
        val resource: Resources? = activity?.get()?.resources
        if (resource == null) {
            GLog.e(TAG, LogFlag.DL, "doRemoveLabelFromSelectionAction: Activity get is ${activity?.get()}")
            return
        }
        selection?.get()?.submitSelection { set ->
            selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                showActionDialog(
                    actionId = ACTION_NOT_THIS_LABEL,
                    confirmMsg = resource.getString(BasebizR.string.base_side_pane_remove),
                    title = when {
                        imageCount == 0 -> {
                            resource.getQuantityString(BasebizR.plurals.base_characters_move_selection_video, videoCount, videoCount)
                        }

                        videoCount == 0 -> {
                            resource.getQuantityString(BasebizR.plurals.base_characters_merge_selection_photo, imageCount, imageCount)
                        }

                        else -> {
                            resource.getQuantityString(
                                BasebizR.plurals.base_characters_move_selection_project,
                                (imageCount + videoCount),
                                (imageCount + videoCount)
                            )
                        }
                    }
                ) { which ->
                    AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                        currentPage = trackCallerEntry.albumsActionCurrentPage,
                        imageCount = imageCount.toString(),
                        videoCount = videoCount.toString(),
                        itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_REMOVE_FROM_LABEL_ALBUM_VALUE,
                        albumName = trackCallerEntry.currentAlbumName,
                        path = trackCallerEntry.path,
                        operResult = if (which == DialogInterface.BUTTON_NEUTRAL) ALBUMS_ACTION_DIALOG_CLICK_OK else ALBUMS_ACTION_DIALOG_CLICK_CANCEL
                    )
                    if (which != DialogInterface.BUTTON_NEUTRAL) return@showActionDialog
                    removeFromLabel(set.toList(), labelId, trackCallerEntry)
                }
            }
        }
    }

    /**
     * 大图下的“不是此类”按钮 - 需要单独的一个path
     */
    @JvmOverloads
    fun doRemoveFromLabelAction(
        itemPath: Path,
        isImage: Boolean,
        labelId: Int,
        labelName: String,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit) = { _, _ -> }
    ): BaseAlertDialog<out Any>? = activity?.get()?.resources?.run {
        showActionDialog(
            actionId = ACTION_NOT_THIS_LABEL,
            confirmMsg = getString(BasebizR.string.base_side_pane_remove),
            title = if (isImage) {
                getString(BasebizR.string.base_characters_album_remove_selection_this_photo)
            } else {
                getString(BasebizR.string.base_characters_album_remove_selection_this_video)
            }
        ) { which ->
            if (which != DialogInterface.BUTTON_NEUTRAL) {
                onCompleted(MenuAction.RESULT_CANCEL, null)
                return@showActionDialog
            }
            removeFromLabel(listOf(itemPath), labelId, trackCallerEntry, onCompleted)
        }
    } ?: let {
        onCompleted(MenuAction.RESULT_FAILED, null)
        GLog.e(TAG, LogFlag.DL, "doRemoveFromLabelAction: Activity get is ${activity?.get()}")
        null
    }

    private fun freeFaceFromGroup(
        pathList: List<Path>,
        groupId: Long,
        trackCallerEntry: TrackCallerEntry,
        albumSetType: Int = TYPE_PERSON_ALBUM_SET,
        onCompleted: (String, Map<String, Any>?) -> Unit = { _, _ -> }
    ) {
        GLog.d(TAG, LogFlag.DL, "freeFaceFromGroup: path list: $pathList")
        if (pathList.size > 1) {
            showLoadingDialog(BasebizR.string.base_regrouping, pathList.size)
        }
        MenuOperationManager.doAction(
            action = MenuAction.FREE_FROM_GROUP,
            paramMap = freeFromGroup.builder
                .setPaths(pathList)
                .setGroupId(groupId)
                .setAlbumSetType(albumSetType)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL, "freeFaceFromGroup on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
                releaseDialogHelper()
                selection?.get()?.exitSelectionMode()
                if (resultCode == MenuAction.RESULT_FAILED) {
                    ToastUtil.showShortToast(BasebizR.string.base_free_failed_desp)
                }
                onCompleted(resultCode, resultMap)
            }
        )
    }

    /**
     * 人物图集的“移入其他人物”和“移出其他人物”
     * @param isMoveToPersonAlbum 是否移动到正常人物图集 true: 移出其他人物, false: 移入其他图集
     */
    fun doMovePersonAlbumAction(
        isMoveToPersonAlbum: Boolean,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        dispatchPersonAlbumAction(
            actionId = ACTION_MOVE_TO_PERSON_ALBUM,
            enable = isMoveToPersonAlbum,
            trackCallerEntry = trackCallerEntry,
            callbackWrapper = callbackWrapper
        )
    }

    /**
     * 移除选中的角色图集
     */
    fun doRemovePersonPetAlbumAction(
        trackCallerEntry: TrackCallerEntry,
        isSupportPetClassify: Boolean,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        val resources: Resources? = activity?.get()?.resources
        if (resources == null) {
            GLog.e(TAG, LogFlag.DL) { "doRemovePersonPetAlbumAction: Activity get is ${activity?.get()}" }
            return
        }
        val selectionCount = selection?.get()?.getSelectedItemCount() ?: 0
        resources.apply {
            dispatchPersonPetAlbumAction(
                actionId = ACTION_REMOVE_PERSON_PET_ALBUM,
                title = getQuantityString(BasebizR.plurals.remove_person_pet_dialog_title, selectionCount, selectionCount),
                confirmMsg = getString(BasebizR.string.common_confirmation),
                hint = if (isSupportPetClassify) getTipContent(this) else null,
                trackCallerEntry = trackCallerEntry,
                callbackWrapper = callbackWrapper
            )
        }
    }

    /**
     * 获取移除角色提示信息
     */
    private fun getTipContent(resources: Resources): String {
        return resources.getString(
            when {
                PersonPetDataHelper.isOnlyPerson() -> BasebizR.string.remove_member_person_desc
                PersonPetDataHelper.isOnlyPet() -> BasebizR.string.remove_member_pet_desc
                else -> BasebizR.string.remove_member_desc
            }
        )
    }

    /**
     * 人物图集或者宠物图集的“合并”
     * @param albumSetType 要合并的图集的类型 0:人物图集，1:宠物图集
     */
    fun doMergePersonPetAlbumAction(
        albumSetType: Int = 0,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        val resources: Resources? = activity?.get()?.resources
        if (resources == null) {
            GLog.e(TAG, LogFlag.DL) { "doMergePersonPetAlbumAction: Activity get is ${activity?.get()}" }
            return
        }
        val selectionCount = selection?.get()?.getSelectedItemCount() ?: 0
        resources.apply {
            dispatchPersonPetAlbumAction(
                actionId = ACTION_MERGE_PERSON_PET_ALBUM,
                title = getString(
                    when (albumSetType) {
                        TYPE_PERSON_ALBUM_SET -> BasebizR.string.base_merge_selected_these_characters_message
                        TYPE_PET_ALBUM_SET -> BasebizR.string.merge_pets_title
                        else -> NORMAL_INVALID_CODE
                    },
                    selectionCount
                ),
                hint = getString(R.string.merge_person_pet_desc),
                confirmMsg = getString(BasebizR.string.base_merge_face_album),
                trackCallerEntry = trackCallerEntry,
                callbackWrapper = callbackWrapper
            )
        }
    }

    /**
     *  通过selection创建path list并将list当做参数传给对应action
     *  合并需要通过点击底部弹窗的确认按钮来执行操作
     */
    private fun dispatchPersonPetAlbumAction(
        actionId: String,
        title: String? = null,
        confirmMsg: String = TextUtil.EMPTY_STRING,
        hint: String? = null,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        selection?.get()?.submitSelection { set: Set<Path> ->
            showActionDialog(
                confirmMsg = confirmMsg,
                hint = hint,
                title = title,
                actionId = actionId
            ) { which ->
                AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                    currentPage = trackCallerEntry.albumsActionCurrentPage,
                    itemId = if (actionId == ACTION_MERGE_PERSON_PET_ALBUM) {
                        AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_MERGE_FACE_ALBUM_VALUE
                    } else {
                        AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_HIDE_FACE_ALBUM_VALUE
                    },
                    albumName = trackCallerEntry.currentAlbumName,
                    path = trackCallerEntry.path,
                    albumList = set.toList(),
                    operResult = if (which == DialogInterface.BUTTON_NEUTRAL) {
                        ALBUMS_ACTION_DIALOG_CLICK_OK
                    } else {
                        ALBUMS_ACTION_DIALOG_CLICK_CANCEL
                    }
                )
                if (which != DialogInterface.BUTTON_NEUTRAL) {
                    callbackWrapper?.invoke(null, ACTION_STATE_CANCEL)
                    return@showActionDialog
                }
                if (actionId == ACTION_MERGE_PERSON_PET_ALBUM) {
                    mergePersonPetAlbum(set.toList(), trackCallerEntry, callbackWrapper = callbackWrapper)
                } else if (actionId == ACTION_REMOVE_PERSON_PET_ALBUM) {
                    removePersonPetAlbum(false, set.toList(), trackCallerEntry, callbackWrapper = callbackWrapper)
                    AlbumsActionTrackHelper.trackAndSendMenuOperationClickForPet(removeActor = TextUtil.EMPTY_STRING)
                }
            }
        }
    }

    /**
     * 人物图集的“合并”和“合并并移出”
     * @param isOtherAlbumSet 是否在其他图集中执行此操作
     */
    fun doMergePersonAlbumAction(
        isOtherAlbumSet: Boolean,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        val resources: Resources? = activity?.get()?.resources
        if (resources == null) {
            GLog.e(TAG, LogFlag.DL, "dispatchPersonAlbumAction: Activity get is ${activity?.get()}")
            return
        }
        val selectionCount = selection?.get()?.getSelectedItemCount() ?: 0
        resources.apply {
            if (isOtherAlbumSet) {
                val albumName = getString(BasebizR.string.base_hide_face_album_set)
                dispatchPersonAlbumAction(
                    actionId = ACTION_MERGE_AND_MOVE_TO_PERSON_ALBUM,
                    title = getString(BasebizR.string.base_merge_selected_these_characters_message, selectionCount),
                    confirmMsg = getString(BasebizR.string.base_merge_face_album),
                    hint = getQuantityString(
                        BasebizR.plurals.base_merge_and_remove_other_album,
                        selectionCount,
                        selectionCount,
                        albumName
                    ),
                    trackCallerEntry = trackCallerEntry,
                    callbackWrapper = callbackWrapper
                )
            } else {
                dispatchPersonAlbumAction(
                    actionId = ACTION_MERGE_PERSON_PET_ALBUM,
                    title = getString(BasebizR.string.base_merge_selected_these_characters_message, selectionCount),
                    confirmMsg = getString(BasebizR.string.base_merge_face_album),
                    trackCallerEntry = trackCallerEntry,
                    callbackWrapper = callbackWrapper
                )
            }
        }
    }

    private fun removeFromLabel(
        pathList: List<Path>,
        labelId: Int,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit) = { _, _ -> }
    ) {
        GLog.d(TAG, "removeFromLabel: path list: $pathList")
        if (pathList.size > 1) {
            showLoadingDialog(BasebizR.string.base_regrouping, pathList.size, DIALOG_DELAY_500)
        }
        MenuOperationManager.doAction(
            action = MenuAction.REMOVE_FROM_LABEL,
            paramMap = removeFromLabel.builder
                .setPaths(pathList)
                .setLabelId(labelId)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL, "removeFromLabel on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
                releaseDialogHelper()
                selection?.get()?.exitSelectionMode()
                AppScope.launch(Dispatchers.UI) {
                    if (resultCode == MenuAction.RESULT_FAILED) {
                        ToastUtil.showShortToast(BasebizR.string.base_free_failed_desp)
                    } else if (resultCode == MenuAction.RESULT_SUCCESS) {
                        ToastUtil.showShortToast(BasebizR.string.base_remove_success)
                    }
                }
                onCompleted(resultCode, resultMap)
            }
        )
    }

    /**
     *  通过selection创建path list并将list当做参数传给对应action
     *  移入/移出其他人物图集不需要显示底部的确认弹窗，直接doAction
     *  合并/合并并移出需要通过点击底部弹窗的确认按钮来执行操作
     */
    private fun dispatchPersonAlbumAction(
        actionId: String,
        title: String? = null,
        confirmMsg: String = TextUtil.EMPTY_STRING,
        hint: String? = null,
        enable: Boolean = false,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        selection?.get()?.submitSelection { set: Set<Path> ->
            when (actionId) {
                ACTION_MOVE_TO_OTHER_PERSON_ALBUM, ACTION_MOVE_TO_PERSON_ALBUM -> {
                    movePersonAlbum(enable, set.toList(), trackCallerEntry, callbackWrapper = callbackWrapper)
                    AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                        currentPage = trackCallerEntry.albumsActionCurrentPage,
                        itemId = if (enable) {
                            AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_SHOW_FACE_ALBUM_VALUE
                        } else {
                            AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_HIDE_FACE_ALBUM_VALUE
                        },
                        albumName = trackCallerEntry.currentAlbumName,
                        path = trackCallerEntry.path,
                        albumList = set.toList()
                    )
                }

                else -> {
                    showActionDialog(
                        confirmMsg = confirmMsg,
                        hint = hint,
                        title = title,
                        actionId = actionId
                    ) { which ->
                        AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                            currentPage = trackCallerEntry.albumsActionCurrentPage,
                            itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_MERGE_FACE_ALBUM_VALUE,
                            albumName = trackCallerEntry.currentAlbumName,
                            path = trackCallerEntry.path,
                            albumList = set.toList(),
                            operResult = if (which == DialogInterface.BUTTON_NEUTRAL) {
                                ALBUMS_ACTION_DIALOG_CLICK_OK
                            } else {
                                ALBUMS_ACTION_DIALOG_CLICK_CANCEL
                            }
                        )
                        if (which != DialogInterface.BUTTON_NEUTRAL) {
                            callbackWrapper?.invoke(null, ACTION_STATE_CANCEL)
                            return@showActionDialog
                        }
                        if (actionId == ACTION_MERGE_AND_MOVE_TO_PERSON_ALBUM) {
                            mergeOtherPersonAlbum(set.toList(), trackCallerEntry, callbackWrapper = callbackWrapper)
                        } else if (actionId == ACTION_MERGE_PERSON_PET_ALBUM) {
                            mergePersonAlbum(set.toList(), trackCallerEntry, callbackWrapper = callbackWrapper)
                        }
                    }
                }
            }
        }
    }

    private fun mergePersonPetAlbum(
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        GLog.d(TAG, LogFlag.DL) { "mergePersonPetAlbum: path list: $pathList, enable: true" }
        if (pathList.size > 1) {
            showLoadingDialog(BasebizR.string.base_merging, pathList.size)
        }
        callbackWrapper?.invoke(pathList, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.MERGE_GROUP,
            mergeGroup.builder
                .setPaths(pathList)
                .setShowEnable(false)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL) { "Action : MERGE_GROUP on complete: resultCode: $resultCode, resultMap: $resultMap" }
                onMergeProgressComplete(isOtherPersonAlbumSet = false, isSuccess = resultCode == MenuAction.RESULT_SUCCESS)
                selection?.get()?.exitSelectionMode()
                releaseDialogHelper()
                callbackWrapper?.invoke(pathList, ACTION_STATE_COMPLETED)
            }
        )
    }

    private fun mergePersonAlbum(
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        GLog.d(TAG, LogFlag.DL, "mergePersonAlbum: path list: $pathList, enable: true")
        if ((pathList.size > 1)) {
            showLoadingDialog(BasebizR.string.base_merging, pathList.size)
        }
        callbackWrapper?.invoke(pathList, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.MERGE_GROUP,
            mergeGroup.builder
                .setPaths(pathList)
                .setShowEnable(false)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL, "Action : MERGE_GROUP on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
                onMergeProgressComplete(isOtherPersonAlbumSet = false, isSuccess = resultCode == MenuAction.RESULT_SUCCESS)
                selection?.get()?.exitSelectionMode()
                releaseDialogHelper()
                callbackWrapper?.invoke(pathList, ACTION_STATE_COMPLETED)
            }
        )
    }

    private fun mergeOtherPersonAlbum(
        pathList: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        GLog.d(TAG, LogFlag.DL, "mergeOtherPersonAlbum: path list: $pathList, enable: true")
        if ((pathList.size > 1)) {
            showLoadingDialog(BasebizR.string.base_merge_and_showing, pathList.size)
        }
        callbackWrapper?.invoke(pathList, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.MERGE_GROUP,
            mergeGroup.builder
                .setPaths(pathList)
                .setShowEnable(true)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL, "mergeOtherPersonAlbum, Action : MERGE_GROUP on complete: resultCode:"
                        + resultCode + "resultMap:" + resultMap)
                onMergeProgressComplete(isOtherPersonAlbumSet = true, isSuccess = resultCode == MenuAction.RESULT_SUCCESS)
                selection?.get()?.exitSelectionMode()
                releaseDialogHelper()
                callbackWrapper?.invoke(pathList, ACTION_STATE_COMPLETED)
            }
        )
    }

    private fun onMergeProgressComplete(isOtherPersonAlbumSet: Boolean, isSuccess: Boolean) {
        ToastUtil.showShortToast(
            if (isOtherPersonAlbumSet) {
                if (isSuccess) {
                    BasebizR.string.base_merge_and_show_success
                } else {
                    BasebizR.string.base_merge_and_show_fail
                }
            } else {
                if (isSuccess) {
                    BasebizR.string.base_merge_success
                } else {
                    BasebizR.string.base_merge_fail
                }
            }
        )
    }

    private fun createOnComplete(actionId: Int, actionCallback: ((state: Int) -> Unit)? = null, isRecycleAlbum: Boolean = false) =
        { resultCode: String, resultMap: Map<String, Any>? ->
            GLog.d(TAG, LogFlag.DL, "Action : $actionId on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
            selection?.get()?.exitSelectionMode()
            // 图集删除的对话框dismiss，放在图集删除完成且页面数据刷新完成以后, 以改善图集删除的用户体验
            if (isRecycleAlbum.not()) releaseDialogHelper()
            /**
             * 图片被成功删除后需要回调成功的状态
             */
            when (actionId) {
                MenuAction.RECYCLE -> {
                    activity?.get()?.apply {
                        when (resultMap?.get(RecycleOperation.TAG_RESULT_MAP)) {
                            RECYCLE_PHONE_NO_SPACE_FAIL, RECYCLE_SD_NO_SPACE_FAIL -> {
                                actionCallback?.invoke(ACTION_STATE_FAILED)
                                StorageTipsHelper.show(
                                    this,
                                    StorageType.PHONE_STORAGE,
                                    StorageLimitHelper.State.NO_SPACE, BasebizR.string.base_recyle_nospace
                                )
                            }

                            RECYCLE_PHONE_BROKEN_FAIL, RECYCLE_SD_BROKEN_FAIL -> {
                                actionCallback?.invoke(ACTION_STATE_FAILED)
                                StorageTipsHelper.show(
                                    this,
                                    StorageType.PHONE_STORAGE,
                                    StorageLimitHelper.State.BROKEN
                                )
                            }

                            EXECUTION_RESULT_SUCCESS -> {
                                //删除成功后调用震动
                                vibrateShock()
                                actionCallback?.invoke(ACTION_STATE_SUCCESS)
                            }
                        }
                    }
                }

                MenuAction.RESTORE_RECYCLED, MenuAction.DELETE_RECYCLED -> {
                    activity?.get()?.apply {
                        when (resultMap?.get(RecycleOperation.TAG_RESULT_MAP)) {
                            EXECUTION_RESULT_SUCCESS -> {
                                if (actionId == MenuAction.DELETE_RECYCLED) {
                                    //删除成功并且是 彻底删除（全部删除）才调用震动
                                    vibrateShock()
                                }
                                actionCallback?.invoke(ACTION_STATE_SUCCESS)
                            }

                            else -> actionCallback?.invoke(ACTION_STATE_FAILED)
                        }
                    }
                }
            }

            actionCallback?.invoke(ACTION_STATE_COMPLETED)

            dialogHelper = null
        }

    /**
     *  震动。
     *  type : 描述震动波形图
     *  优化单:使用新feature判断，满足条件删除文件震动反馈使用47号波形图
     */
    private fun vibrateShock() {
        VibratorUtils.luXunVibrator(ContextGetter.context)
    }

    /**
     * 展示底部弹窗，用户通过点击弹窗上的“确认”，“取消”来进行下一步操作。
     * @param confirmMsg 确认按钮的文字
     * @param hint 确认按钮上方的灰色提示语 - 删除弹窗中执行彻底删除提示语为空，其他删除弹窗提示语不能为空
     */
    private fun showActionDialog(
        confirmMsg: String,
        hint: String? = null,
        title: String? = null,
        @ColorInt newNeutralButtonTextColor: Int? = null,
        actionId: String? = null,
        clickCallback: (which: Int) -> Unit
    ): BaseAlertDialog<out Any>? {
        confirmDialogListener = ConfirmDialogListener(clickCallback)
        return when (actionId) {
            //回收站-恢复
            ACTION_RESTORE,
            ACTION_MERGE_AND_MOVE_TO_PERSON_ALBUM,
            ACTION_MERGE_PERSON_PET_ALBUM,
            ACTION_REMOVE_PERSON_PET_ALBUM,
            ACTION_RESTORE_ALL_RECYCLED -> showDialogAndNotWarning(title, hint, confirmMsg)

            ACTION_DELETE_MEMORIES,
            ACTION_RECYCLE_ALL,
            ACTION_RECYCLE_ALBUM,
            ACTION_NOT_THIS_PERSON,
            ACTION_NOT_THIS_LABEL,
            ACTION_MERGE_PERSON_PET_ALBUM,
            ACTION_REMOVE_PERSON_PET_ALBUM,
            ACTION_RECYCLE -> showConfirmDialog(title = title, confirmMsg = confirmMsg, hint = hint, view = null)

            else -> showActionDialogOtherOptionalScene(hint, confirmMsg, newNeutralButtonTextColor)
        }
    }

    /**
     * 其他场景（指非首次删除和清空相册以外的场景）
     *  @param hint 提示信息
     *  @param confirmMsg 提交按钮信息
     *  @param newNeutralButtonTextColor NeutralButton 字体颜色
     */
    private fun showActionDialogOtherOptionalScene(
        hint: String?,
        confirmMsg: String,
        newNeutralButtonTextColor: Int?
    ): BaseAlertDialog<out Any>? {
        return showListDialog(confirmMsg = confirmMsg, hint = hint, view = null, newNeutralButtonTextColor = newNeutralButtonTextColor)
    }

    /**
     *  显示三个button的弹窗 - 底部弹出
     *  @param resources
     *  @param newNeutralButtonTextColor NeutralButton 字体颜色
     *  @param newPositiveButtonTextColor PositiveButton 字体颜色
     */
    private fun showClearAlbumDialog(
        resources: Resources,
        totalItemCount: Int = 0,
        imageCount: Int = 0,
        videoCount: Int = 0,
        @ColorInt newNeutralButtonTextColor: Int? = null,
        @ColorInt newPositiveButtonTextColor: Int? = null,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL,
        resultCallback: (which: Int) -> Unit
    ): BaseAlertDialog<out Any>? {
        confirmDialogListener = ConfirmDialogListener(resultCallback)
        val triple = makeClearAlbumHint(resources, false, mediaType)
        val title = makeClearAlbumTitle(resources, totalItemCount, imageCount, videoCount, mediaType)
        return showListDialog(
            title = title,
            positiveMsg = triple.third,
            confirmMsg = triple.first,
            hint = triple.second,
            view = null,
            newNeutralButtonTextColor = newNeutralButtonTextColor,
            newPositiveButtonTextColor = newPositiveButtonTextColor
        )
    }

    /**
     *  警示弹出 Dialog
     *  @param title setTitle 文案
     *  @param confirmMsg Neutral Button 文案
     *  @param hint setMessage 文案
     *  @param view 自定义样式
     */
    private fun showConfirmDialog(
        title: String? = null,
        confirmMsg: String?,
        hint: String?,
        view: View?
    ): BaseAlertDialog<out Any>? {
        fromCenterDialog?.apply {
            if (isShowing()) {
                GLog.w(TAG, LogFlag.DL, "showActionDialog: center dialog is showing")
                return fromCenterDialog
            }
        }
        val resources: Resources? = activity?.get()?.resources
        fromCenterDialog = activity?.get()?.let {
            if (confirmMsg.isNullOrEmpty()) {
                return null
            }
            if (it.isFinishing || it.isDestroyed) {
                return null
            }
            ConfirmDialog.Builder(it).apply {
                setTitle(title)
                setMessage(hint)
                setButtonLayoutDynamicLayout(false)
                setOnDismissListener(confirmDialogListener)
                setNeutralButton(confirmMsg, confirmDialogListener)
                setNegativeButton(BasebizR.string.common_cancel, confirmDialogListener)
                setOnCancelListener(confirmDialogListener)
                setCanceledOnTouchOutside(true)
                if (view != null) setView(view)
            }
                .build()
                .show()
        }
        return fromCenterDialog?.apply {
            GLog.d(TAG, LogFlag.DL) { "showConfirmDialog: confirmMsg: $confirmMsg, hint: $hint, fromCenterDialog: $fromCenterDialog" }
        }
    }

    /**
     *  非警示弹窗 Dialog, comfirm文字按钮为蓝色
     *  @param title setTitle 文案
     *  @param confirmMsg Neutral Button 文案
     *  @param hint setMessage 文案
     */
    private fun showDialogAndNotWarning(
        title: String? = null,
        hint: String?,
        confirmMsg: String?,
    ): BaseAlertDialog<out Any>? {
        fromCenterDialog?.apply {
            if (isShowing()) {
                GLog.w(TAG, LogFlag.DL, "showDialogAndNotWarning: center dialog is showing")
                return fromCenterDialog
            }
        }
        val resources: Resources? = activity?.get()?.resources
        fromCenterDialog = activity?.get()?.let {
            if (confirmMsg.isNullOrEmpty()) {
                return null
            }
            if (it.isFinishing || it.isDestroyed) {
                return null
            }
            ConfirmDialog.Builder(it).apply {
                setTitle(title)
                setMessage(hint)
                setButtonLayoutDynamicLayout(false)
                setOnDismissListener(confirmDialogListener)
                setNeutralButton(confirmMsg, confirmDialogListener)
                setNegativeButton(BasebizR.string.common_cancel, confirmDialogListener)
                setOnCancelListener(confirmDialogListener)
                setCanceledOnTouchOutside(false)
            }
                .build()
                .show()
        }
        return fromCenterDialog?.apply {
            getButton(DialogInterface.BUTTON_NEUTRAL)?.let { neutralButton ->
                resources?.let { resources ->
                    neutralButton.setTextColor(resources.getColor(com.support.appcompat.R.color.coui_color_primary_on_popup_blue, null))
                }
            }
            GLog.d(TAG, LogFlag.DL) { "showDialogAndNotWarning: confirmMsg: $confirmMsg, hint: $hint, fromCenterDialog: $fromCenterDialog" }
        }
    }

    /**
     *  最近删除-首次删除弹窗-居中弹出 Dialog
     *  @param title setTitle 文案
     *  @param confirmMsg Neutral Button 文案
     *  @param hint setMessage 文案
     *  @param view 自定义样式
     */
    private fun showConfirmTimerDialog(
        title: String? = null,
        confirmMsg: String?,
        hint: String?,
        view: View?
    ): BaseAlertDialog<out Any>? {
        fromCenterDialog?.apply {
            if (isShowing()) {
                GLog.w(TAG, LogFlag.DL, "showActionDialog: center dialog is showing")
                return fromCenterDialog
            }
        }
        val resources: Resources? = activity?.get()?.resources
        fromCenterDialog = activity?.get()?.let {
            if (confirmMsg.isNullOrEmpty()) {
                return null
            }
            if (it.isFinishing || it.isDestroyed) {
                return null
            }
            ConfirmDialog.Builder(it).apply {
                setTitle(title)
                setMessage(hint)
                setButtonLayoutDynamicLayout(false)
                setOnDismissListener(confirmDialogListener)
                setNeutralButton(confirmMsg, confirmDialogListener)
                setNegativeButton(BasebizR.string.common_cancel, confirmDialogListener)
                setOnCancelListener(confirmDialogListener)
                setCanceledOnTouchOutside(false)
                if (view != null) setView(view)
            }
                .build()
                .show()
        }

        return fromCenterDialog?.apply {
            val lp: LinearLayout.LayoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f)
            getButton(DialogInterface.BUTTON_NEGATIVE)?.let { negativeButton ->
                negativeButton.layoutParams = lp
            }
            getButton(DialogInterface.BUTTON_NEUTRAL)?.let { neutralButton ->
                neutralButton.layoutParams = lp
                neutralButton.isSingleLine = false
                neutralButton.isEnabled = false
                resources?.let { resources ->
                    neutralButton.setTextColor(resources.getColor(com.support.appcompat.R.color.coui_borderless_button_text_color_disabled, null))
                    startButtonClickableCountDowntimer(neutralButton, resources, confirmMsg)
                }
            }
            GLog.d(TAG, LogFlag.DL) { "showConfirmTimerDialog: confirmMsg: $confirmMsg, hint: $hint, fromCenterDialog: $fromCenterDialog" }
        }
    }

    /**
     *  列表型-底部弹出 Dialog
     *  @param positiveMsg positive button 文案
     *  @param confirmMsg Neutral Button 文案
     *  @param hint setMessage 文案
     *  @param view 自定义样式
     *  @param newNeutralButtonTextColor NeutralButton 字体颜色
     *  @param newPositiveButtonTextColor PositiveButton 字体颜色
     */
    private fun showListDialog(
        title: String? = null,
        positiveMsg: String? = null,
        confirmMsg: String?,
        hint: String?,
        view: View?,
        @ColorInt newNeutralButtonTextColor: Int? = null,
        @ColorInt newPositiveButtonTextColor: Int? = null
    ): BaseAlertDialog<out Any>? {
        fromCenterDialog?.apply {
            if (isShowing()) {
                GLog.w(TAG, LogFlag.DL, "showActionDialog: center dialog is showing")
                return fromCenterDialog
            }
        }
        fromBottomDialog = activity?.get()?.let {
            if (it.isFinishing || it.isDestroyed) {
                return null
            }
            ListDialog.Builder(it).apply {
                setOnDismissListener(confirmDialogListener)
                setTitle(title)
                setPositiveButton(positiveMsg, confirmDialogListener)
                setNeutralButton(confirmMsg, confirmDialogListener)
                setNegativeButton(BasebizR.string.common_cancel, confirmDialogListener)
                setOnCancelListener(confirmDialogListener)
                if (view != null) setView(view)
                if (!hint.isNullOrEmpty() && (view == null)) setMessage(hint)
            }
                .setAnchorView(getSelectedMenuItemView())
                .build()
                .show()
        }

        return fromBottomDialog?.apply {
            newNeutralButtonTextColor?.let {
                setButtonTextColor(DialogInterface.BUTTON_NEUTRAL, newNeutralButtonTextColor)
            }
            newPositiveButtonTextColor?.let {
                setButtonTextColor(DialogInterface.BUTTON_POSITIVE, newPositiveButtonTextColor)
            }
            GLog.d(TAG, LogFlag.DL) { "showListDialog: confirmMsg: $confirmMsg, hint: $hint, fromBottomDialog: $fromBottomDialog" }
        }
    }

    /**
     *  最近删除-首次删除-倒计时文案
     *  @param neutralButton 右边button
     *  @param resources
     *  @param confirmMsg button 的文案
     */
    private fun startButtonClickableCountDowntimer(neutralButton: Button, resources: Resources, confirmMsg: String?) {
        countDownTimer = object : CountDownTimer(RECYCLED_COUNTDOWN_MILLISECOND, RECYCLED_COUNTDOWN_INTERVAL) {
            override fun onFinish() {
                neutralButton.text = confirmMsg
                neutralButton.setTextColor(resources.getColor(com.support.dialog.R.color.coui_bottom_alert_dialog_button_warning_color, null))
                neutralButton.isEnabled = true
            }

            override fun onTick(millisecond: Long) {
                val msg = StringBuilder(confirmMsg)
                    .append(" (${(millisecond / RECYCLED_COUNTDOWN_INTERVAL) + 1})")
                    .toString()
                neutralButton.text = msg
            }
        }.start()
    }

    private fun getCloudDialogView(): View? {
        val activity = activity?.get()
        if (activity == null) {
            GLog.e(TAG, LogFlag.DL, "getCloudDialogView: Activity get is null")
            return null
        }
        val resources: Resources? = activity.resources
        if (resources == null) {
            GLog.e(TAG, LogFlag.DL, "getCloudDialogView: resources is null")
            return null
        }
        return LayoutInflater.from(activity).inflate(BasebizR.layout.delete_dialog_old_device_view, null)
    }

    private fun movePersonAlbum(
        moveToNormal: Boolean,
        list: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        if (list.isNotEmpty()) {
            showLoadingDialog(if (moveToNormal) BasebizR.string.base_showing else BasebizR.string.base_hiding, list.size)
        }
        callbackWrapper?.invoke(list, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.SHOW_GROUP,
            showGroup.builder
                .setPaths(list)
                .setShowEnable(moveToNormal)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL) { "movePersonAlbum complete: resultCode: $resultCode, resultMap: $resultMap" }
                releaseDialogHelper()
                selection?.get()?.exitSelectionMode()
                callbackWrapper?.invoke(list, ACTION_STATE_COMPLETED)
                if (resultCode == MenuAction.RESULT_FAILED) {
                    ToastUtil.showShortToast(BasebizR.string.base_free_failed_desp)
                }
            }
        )
    }

    private fun removePersonPetAlbum(
        moveToNormal: Boolean,
        list: List<Path>,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        if (list.isNotEmpty()) {
            showLoadingDialog(if (moveToNormal) BasebizR.string.base_showing else BasebizR.string.base_hiding, list.size)
        }
        callbackWrapper?.invoke(list, ACTION_STATE_START)
        MenuOperationManager.doAction(
            MenuAction.SHOW_GROUP,
            showGroup.builder
                .setPaths(list)
                .setShowEnable(moveToNormal)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL, "movePersonAlbum complete: resultCode:" + resultCode + "resultMap:" + resultMap)
                releaseDialogHelper()
                selection?.get()?.exitSelectionMode()
                callbackWrapper?.invoke(list, ACTION_STATE_COMPLETED)
                if (resultCode == MenuAction.RESULT_FAILED) {
                    ToastUtil.showShortToast(BasebizR.string.base_free_failed_desp)
                }
            }
        )
    }

    /**
     * 添加到
     */
    fun doAppendToAction(
        fragment: BaseFragment,
        albumPath: Path? = null,
        trackCallerEntry: TrackCallerEntry,
        isFromTimeline: Boolean = false,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        albumPath?.let {
            selection?.get()?.submitSelection { set ->
                selection?.get()?.getSelectedItems()?.let {
                    val paths = ArrayList(set)
                    val itemsMediaType = TypeFilterUtils.getMediaTypeByPathString(paths)
                    MenuOperationManager.doAction(
                        MenuAction.MOVE_TO,
                        MenuActionGetter.moveTo.builder
                            .setPaths(paths)
                            .setIsFromTimeline(isFromTimeline)
                            .setFragment(fragment)
                            .setItemsMediaType(itemsMediaType)
                            .setAlbumSetPath(albumPath.toString())
                            .setTrackCallerEntry(trackCallerEntry)
                            .setLifecycle(fragment.lifecycle)
                            .build(),
                        onCompleted = { result, resultMap: Map<String, Any>? ->
                            if (result == MenuAction.RESULT_CANCEL) {
                                callbackWrapper?.invoke(paths, ACTION_STATE_CANCEL)
                                return@doAction
                            }
                            selection?.get()?.exitSelectionMode()
                            if (result == MenuAction.RESULT_SUCCESS) {
                                selection?.get()?.getSelectedItemImageAndVideoCount { bundle ->
                                    AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                                        currentPage = trackCallerEntry.albumsActionCurrentPage,
                                        imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0).toString(),
                                        videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0).toString(),
                                        itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_APPEND_TO_VALUE,
                                        albumName = trackCallerEntry.currentAlbumName,
                                        path = trackCallerEntry.path,
                                        targetAlbum = resultMap?.get(AlbumsActionTackConstant.Key.ALBUMS_ACTION_ALBUM_NAME_KEY).toString(),
                                        operResult = ALBUMS_ACTION_DIALOG_CLICK_OK
                                    )
                                }
                            }
                            callbackWrapper?.invoke(paths, ACTION_STATE_COMPLETED)
                        }
                    )
                }
            }
        } ?: GLog.e(TAG, LogFlag.DL, "doAppendToAction, album path = null")
    }

    /**
     * 复制到操作响应
     * @param fragment 启动界面的fragment，用于后续启动弹窗界面选择
     * @param albumPath 选中的文件都隶属于的 albumSet 的 path
     * @param trackCallerEntry 埋点统计
     * @param callbackWrapper 菜单状态回调
     */
    private fun doCopyToAction(
        fragment: BaseFragment,
        albumPath: Path? = null,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        menuEventNotifier = MenuOperation.EventNotifier()
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        albumPath?.let {
            selection?.get()?.submitSelection { set ->
                selection?.get()?.getSelectedItems()?.let {
                    val paths = ArrayList(set)
                    val itemsMediaType = TypeFilterUtils.getMediaTypeByPathString(paths)
                    MenuOperationManager.doAction(
                        MenuAction.COPY_TO,
                        MenuActionGetter.copyTo.builder
                            .setPaths(paths)
                            .setFragment(fragment)
                            .setItemsMediaType(itemsMediaType)
                            .setAlbumSetPath(albumPath.toString())
                            .setTrackCallerEntry(trackCallerEntry)
                            .setEventNotifier(menuEventNotifier)
                            .setLifecycle(fragment.lifecycle)
                            .build(),
                        onCompleted = { result, resultMap: Map<String, Any>? ->
                            if (result == MenuAction.RESULT_CANCEL) {
                                callbackWrapper?.invoke(paths, ACTION_STATE_CANCEL)
                                return@doAction
                            }
                            selection?.get()?.exitSelectionMode()
                            callbackWrapper?.invoke(paths, ACTION_STATE_COMPLETED)
                        }
                    )
                }
            }
        } ?: GLog.e(TAG, LogFlag.DL, "doCopyToAction, album path = null")
    }

    /**
     * 移动到随身卡包
     */
    fun doMoveToCardCaseAction(fragment: BaseFragment, movableCountSet: CardCaseCountSet, paths: List<Path>) {
        MenuOperationManager.doAction(
            MenuAction.MOVE_TO_CARD_CASE,
            MenuActionGetter.moveToCardCase.builder
                .setFragment(fragment)
                .setMovableCardCaseCountSet(movableCountSet)
                .setMovablePaths(paths)
                .build()
        )
    }

    /**
     * 新建图集
     */
    fun doCreateAlbumAction(fragment: BaseFragment, trackCallerEntry: TrackCallerEntry) {
        menuEventNotifier = MenuOperation.EventNotifier()
        MenuOperationManager.doAction(
            MenuAction.CREATE_ALBUM,
            MenuActionGetter.createAlbum.builder
                .setFragment(fragment)
                .setEventNotifier(menuEventNotifier)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            { result, resultMap: Map<String, Any>? ->

            }
        )
    }

    /**
     * 新建共享图集
     */
    fun doCreateShareAlbum(fragment: BaseFragment, trackCallerEntry: TrackCallerEntry) {
        MenuOperationManager.doAction(
            MenuAction.CREATE_SHARE_ALBUM,
            MenuActionGetter.createShareAlbum.builder
                .setActivity(fragment.activity as? BaseActivity)
                .setTrackCallerEntry(trackCallerEntry)
                .build()
        )
    }

    fun doRenameAlbumAction(
        context: Context,
        trackCallerEntry: TrackCallerEntry,
        actionCallback: ((success: Boolean, customName: String?) -> Unit)? = null,
        albumPath: Path? = null
    ) {
        /* 此处 path.object 为空不会是该 object 持有的 MediaSet 的弱引用被 GC 后为空（此函数是由正在使用此 MediaSet 的页面调
           用的，所以当前 MediaSet 被强引用持有，不会被 GC），所以此处如果 object 为空，则只可能是 MediaSet 对应的图集被删除，无
           需再通过 DataManager 重新获取 MediaSet 对象 */
        val mediaAlbum = (albumPath?.`object`) as? MediaAlbum
        val callbackRef = WeakReference(actionCallback)
        mediaAlbum?.also {
            if (it.bucketIds.isEmpty()) return

            var bucketIds: List<Int> = AlbumAllowListConfig.getInstance().getMergeAlbumsBucketIds(it.bucketIds[0])

            if (bucketIds.isEmpty()) {
                bucketIds = it.bucketIds
            }
            menuEventNotifier = MenuOperation.EventNotifier()
            MenuOperationManager.doAction(
                MenuAction.RENAME_ALBUM,
                MenuActionGetter.renameAlbum.builder
                    .setContext(context)
                    .setAlbumName(it.noteName ?: it.name)
                    .setFolderPath(it.bucketPath)
                    .setBucketId(bucketIds)
                    .setEventNotifier(menuEventNotifier)
                    .setTrackCallerEntry(trackCallerEntry)
                    .build(),
                { result, resultMap: Map<String, Any>? ->
                    selection?.get()?.exitSelectionMode()
                    val callback = callbackRef.get()
                    val albumType = if (it.isSelfAlbum) {
                        ALBUMS_ACTION_RENAME_ALBUM_ALBUM_TYPE_USER_CREATE
                    } else {
                        ALBUMS_ACTION_RENAME_ALBUM_ALBUM_TYPE_OTHER
                    }

                    when (result) {
                        MenuAction.RESULT_SUCCESS -> {
                            it.customName = resultMap?.get(RenameAlbumAction.KEY_ALBUM_NAME) as? String
                            callback?.invoke(true, it.customName)
                            AlbumsActionTrackHelper.trackAndSendRenameAlbumsResult(
                                albumType,
                                ALBUMS_ACTION_RENAME_ALBUM_OPERATION_TYPE_SAVE
                            )
                            AppScope.launch(Dispatchers.IO, CoroutineStart.DEFAULT) {
                                getCloudSyncDM().updateAlbumDisplayName(it)
                            }
                        }

                        MenuAction.RESULT_CANCEL -> {
                            callback?.invoke(false, null)
                            AlbumsActionTrackHelper.trackAndSendRenameAlbumsResult(
                                albumType,
                                ALBUMS_ACTION_RENAME_ALBUM_OPERATION_TYPE_CANCEL
                            )
                        }

                        MenuAction.RESULT_ERROR_NO_DATA -> {
                            callback?.invoke(false, null)
                            GLog.e(TAG, LogFlag.DL, "doRenameAlbumAction, rename album action param no data")
                        }
                    }
                    menuEventNotifier = null
                }
            )
        } ?: GLog.e(TAG, LogFlag.DL, "doRenameAlbumAction, album is null, do nothing")
    }

    @Suppress("LongMethod")
    fun doRecycleMemoryAlbumAction(
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        val resources: Resources? = activity?.get()?.resources
        if (resources == null) {
            GLog.e(TAG, LogFlag.DL, "doRecycleMemoryAlbumAction: Activity get is ${activity?.get()}")
            return
        }
        selection?.get()?.submitSelection { set: Set<Path> ->
            val selectedCount = selection?.get()?.getSelectedItemCount() ?: return@submitSelection
            val title = if (selectedCount > 1) {
                resources.getQuantityString(
                    BasebizR.plurals.base_delete_selected_these_memories_title,
                    selectedCount,
                    selectedCount
                )
            } else {
                resources.getString(BasebizR.string.base_delete_selected_this_memories_title)
            }
            val hint = if (selectedCount > 1) {
                resources.getQuantityString(
                    BasebizR.plurals.base_delete_selected_these_memories_message,
                    selectedCount,
                    selectedCount
                )
            } else {
                resources.getString(BasebizR.string.base_delete_selected_this_memories_message)
            }
            showActionDialog(
                confirmMsg = resources.getString(BasebizR.string.base_comfirm_delete),
                hint = hint,
                title = title,
                actionId = ACTION_DELETE_MEMORIES
            ) { which ->
                AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
                    currentPage = trackCallerEntry.albumsActionCurrentPage,
                    itemId = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_DELETE_VALUE,
                    albumName = trackCallerEntry.currentAlbumName,
                    path = trackCallerEntry.path,
                    operResult = if (which == DialogInterface.BUTTON_NEUTRAL) RECYCLE_ACTION_RESULT_SUCCESS else RECYCLE_ACTION_RESULT_CANCEL,
                    albumList = set.toList()
                )
                if (which != DialogInterface.BUTTON_NEUTRAL) {
                    callbackWrapper?.invoke(null, ACTION_STATE_CANCEL)
                    return@showActionDialog
                }
                callbackWrapper?.invoke(null, ACTION_STATE_START)
                showLoadingDialog(BasebizR.string.base_deleting, set.size)
                MenuOperationManager.doAction(
                    action = MenuAction.RECYCLE,
                    paramMap = MenuActionGetter.recycle
                        .builder
                        .setRecycleList(set.toList())
                        .setTrackCallerEntry(trackCallerEntry)
                        .build(),
                    onCompleted = { resultCode, resultMap ->
                        GLog.d(TAG, LogFlag.DL, "Action : ${MenuAction.RECYCLE} on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
                        selection?.get()?.exitSelectionMode()
                        releaseDialogHelper()
                        callbackWrapper?.invoke(set.toList(), ACTION_STATE_COMPLETED)
                    }
                )
            }
        }
    }

    private fun showLoadingDialog(title: Int, size: Int, delayTime: Int = DIALOG_DELAY_0) {
        val activity = activity?.get()
        if (activity == null) {
            GLog.e(TAG, LogFlag.DL, "showLoadingDialog: activity is $activity")
            return
        }
        dialogHelper = (dialogHelper ?: DialogHelper(activity, createLoadingDialogBuilder(activity, title)))
        GLog.d(TAG, LogFlag.DL, "show dialog title: $title total size: $size delayTime:$delayTime")
        runCatching {
            dialogHelper?.showDialog(PROGRESS_START, delayTime)
            WakeLockUtil.acquireWakeLock(ContextGetter.context, TAG)
        }.onFailure {
            GLog.d(MODULE_NAME, TAG, LogFlag.DL, "showDialog == error", it)
            WakeLockUtil.releaseWakeLock()
        }
    }

    private fun createLoadingDialogBuilder(context: Context, title: Int): LoadingDialog.Builder =
        LoadingDialog.Builder(context, false).apply {
            setTips(title)
            setCancelable(false)
        }

    /**
     *  移入其他图集，移出其他图集，移入侧边导航栏公用
     *  @param operationType 当前操作的类型，具体见OperationType定义
     *  @param selectedItems 被点击的item在列表中position的值， 之后会用来记录排序
     */
    fun doMoveAlbumAction(
        operationType: MoveAlbumAction.OperationType,
        selectedItems: Set<Path>,
        albumMovable: AlbumMovable? = null,
        trackCallerEntry: TrackCallerEntry,
        callbackWrapper: LifecycleCallbackWrapper? = null
    ) {
        callbackWrapper?.invoke(null, ACTION_STATE_START)
        MenuOperationManager.doAction(
            action = MenuAction.MOVE_ALBUM,
            paramMap = MenuActionGetter.moveAlbum.builder
                .setHideAlbumPathList(selectedItems.toList())
                .setAlbumMovable(albumMovable)
                .setOperationType(operationType)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { _: String, _: Map<String, Any>? ->
                selection?.get()?.exitSelectionMode()
                callbackWrapper?.invoke(null, ACTION_STATE_COMPLETED)
            }
        )
        AlbumsActionTrackHelper.trackAndSendMenuOperationClick(
            currentPage = trackCallerEntry.albumsActionCurrentPage,
            itemId =
            when (operationType) {
                MoveAlbumAction.OperationType.MOVE_TO_SIDEPANE -> ALBUMS_ACTION_CLICK_MENU_ITEM_MOVE_IN_SIDE_PANE
                MoveAlbumAction.OperationType.MOVE_OUT_SHORTCUT_ALBUMSET -> ALBUMS_ACTION_CLICK_MENU_ITEM_MOVE_OUT_SHORTCUT
                MoveAlbumAction.OperationType.MOVE_IN_SHORTCUT_ALBUM_SET -> ALBUMS_ACTION_CLICK_MENU_ITEM_MOVE_IN_SHORTCUT
            },
            albumName = trackCallerEntry.currentAlbumName,
            path = trackCallerEntry.path,
            albumList = selection?.get()?.getSelectedItems()?.toList()
        )
    }

    /**
     *  将图集显示在照片页
     *  @param actionType 当前操作的类型，具体见OperationType定义
     *  @param albumIdList 显示的图集albumId
     */
    fun doHideOnTimelinePageAction(
        actionType: Int,
        albumIdList: List<Int>,
        trackCallerEntry: TrackCallerEntry,
        actionCallback: ((path: List<Path>?, state: Int) -> Unit)? = null
    ) {
        actionCallback?.invoke(null, ACTION_STATE_START)
        MenuOperationManager.doAction(
            action = actionType,
            paramMap = MenuActionGetter.hideAlbumOnTimeLinePage.builder
                .setAlbumIdList(albumIdList)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { _: String, _: Map<String, Any>? ->
                selection?.get()?.exitSelectionMode()
                actionCallback?.invoke(null, ACTION_STATE_COMPLETED)
            }
        )
    }

    /**
     *  将图集隐藏在照片页
     *  @param actionType 当前操作的类型，具体见OperationType定义
     *  @param albumIdList 隐藏的图集albumId
     */
    fun doShowOnTimelinePageAction(
        actionType: Int,
        albumIdList: List<Int>,
        trackCallerEntry: TrackCallerEntry,
        actionCallback: ((path: List<Path>?, state: Int) -> Unit)? = null
    ) {
        actionCallback?.invoke(null, ACTION_STATE_START)
        MenuOperationManager.doAction(
            action = actionType,
            paramMap = MenuActionGetter.showAlbumOnTimeLinePage.builder
                .setAlbumIdList(albumIdList)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { _: String, _: Map<String, Any>? ->
                selection?.get()?.exitSelectionMode()
                actionCallback?.invoke(null, ACTION_STATE_COMPLETED)
            }
        )
    }

    fun doImportAction(path: Path?, activity: FragmentActivity? = null, trackCallerEntry: TrackCallerEntry) {
        path?.let { // 大图
            import(arrayListOf(path), activity, trackCallerEntry)
        } ?: run { // 图集页
            selection?.get()?.submitSelection { set ->
                val pathList = set.toList()
                if (pathList.size > 1) {
                    showLoadingDialog(BasebizR.string.base_importing, pathList.size)
                }
                import(pathList, activity, trackCallerEntry)
            }
        }
    }

    private fun import(pathList: List<Path>, activity: FragmentActivity? = null, trackCallerEntry: TrackCallerEntry) {
        val activityRef = WeakReference<FragmentActivity>(activity)
        MenuOperationManager.doAction(
            MenuAction.IMPORT,
            MenuActionGetter.import.builder
                .setPathList(pathList)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
                GLog.d(TAG, LogFlag.DL, "doImportAction: resultCode:" + resultCode + "resultMap:" + resultMap)
                selection?.get()?.exitSelectionMode()
                releaseDialogHelper()
                when (resultCode) {
                    MenuAction.RESULT_SUCCESS -> {
                        ToastUtil.showLongToast(ContextGetter.context.getString(BasebizR.string.base_import_complete))
                        activityRef.get()?.let {
                            Starter.ActivityStarter(
                                activity,
                                Bundle().apply {
                                    putString(
                                        KEY_ID, SourceConstants.Local.PATH_ALBUM_ANY_ALL.getChild(MediaSetUtils.IMPORTED_BUCKET_ID)
                                            .toString()
                                    )
                                    putString(KEY_TITLE, ContextGetter.context.getString(BasebizR.string.model_folder_imported))
                                    putString(KEY_MODEL_TYPE, DataRepository.LocalAlbumModelGetter.TYPE_LOCAL_ALBUM)
                                },
                                PostCard(RouterConstants.RouterName.PICTURE_EXTEND_ACTIVITY)
                            ).start()
                        }
                    }

                    MenuAction.RESULT_FAILED, MenuAction.RESULT_CANCEL ->
                        ToastUtil.showLongToast(ContextGetter.context.getString(BasebizR.string.base_import_fail))
                }
            }
        )
    }

    fun releaseDialogHelper() {
        dialogHelper?.let {
            it.dismissDialog()
            dialogHelper = null
            WakeLockUtil.releaseWakeLock()
        }
    }

    fun release() {
        safeBoxProgressDialog?.dismiss()
        safeBoxProgressDialog = null
        menuResult?.cancel()
        dialogHelper?.removeCallbacksAndMessages(null)
    }

    /**
     *  底部确认弹窗的点击事件监听：删除、恢复、人脸等操作需要等待用户点击确认之后再执行action
     */
    private inner class ConfirmDialogListener(resultCallback: ((which: Int) -> Unit)) : DialogInterface.OnClickListener,
        DialogInterface.OnDismissListener, DialogInterface.OnCancelListener {
        private var resultCallback: ((which: Int) -> Unit)? = resultCallback

        override fun onClick(dialog: DialogInterface, which: Int) {
            resultCallback?.invoke(which)
        }

        override fun onDismiss(dialog: DialogInterface) {
            fromBottomDialog = null
            confirmDialogListener = null
            countDownTimer?.cancel()
        }

        override fun onCancel(dialog: DialogInterface?) {
            resultCallback?.invoke(DialogInterface.BUTTON_NEGATIVE)
            countDownTimer?.cancel()
        }
    }

    interface ProgressListener {
        fun onConfirmDialogShown()
        fun onConfirmDialogDismissed(confirmed: Boolean)
        fun onProgressUpdate(index: Int)
        fun onProgressComplete(result: Int, list: List<Path>?)
        fun onProgressComplete(result: Int, filterCount: Int?)
        fun onClickPositive()
    }

    open class SimpleProgressListener : ProgressListener {
        override fun onConfirmDialogShown() {}
        override fun onConfirmDialogDismissed(confirmed: kotlin.Boolean) {}
        override fun onProgressUpdate(index: Int) {}
        override fun onProgressComplete(result: Int, list: List<Path>?) {}
        override fun onProgressComplete(result: Int, filterCount: Int?) {}
        override fun onClickPositive() {}
    }

    /**
     * isContainCShot 说明： 仅图集页-编辑模式-选择照片时，提示语的区别：有连拍照片：
     * 提示语为：xx项目...仅照片时，xx照片...
     * 仅视频时：xx视频...
     * 两个媒体类型以上师，xx项目
     * @param isContainCShot
     */
    private fun getRecycleHint(
        actionId: String,
        videoCount: Int,
        imageCount: Int,
        selectedCount: Int,
        totalItemCount: Int,
        isContainCShot: Boolean = false,
        isBigPictureMode: Boolean = false,
        resources: Resources,
        albumName: String? = null,
        resultCallback: (hint: String?, confirmMsg: String, title: String?, showBatchCloudDeleteDialog: Boolean) -> Unit
    ) {

        val isRecycle = (actionId == ACTION_RECYCLE)
        val isSyncSwitchOpen = getCloudSyncDM().isAlbumSyncSwitchOpen()
        val tipCount = if ((actionId == ACTION_DELETE_ALL_RECYCLED) || (actionId == ACTION_RESTORE_ALL_RECYCLED)) {
            totalItemCount
        } else {
            selectedCount
        }
        //数据类型
        val mediaType = if (isContainCShot) MEDIA_TYPE_CONTAIN_ALL else getMediaType(videoCount, imageCount)
        var showBatchCloudDeleteDialog = false
        //清空相册二次弹窗
        if (isSyncSwitchOpen && (actionId == ACTION_RECYCLE_ALL)) {
            val triple = makeClearAlbumHint(resources, true)
            resultCallback.invoke(triple.second, triple.third, triple.first, showBatchCloudDeleteDialog)
            return
        }
        val title = makeDialogActionRecycleTitle(
            resources, actionId, mediaType,
            isBigPictureMode, tipCount, albumName, totalItemCount, isSyncSwitchOpen
        )
        val hint: String? = makeTipsMessage(actionId, isSyncSwitchOpen, mediaType, albumName, false, tipCount, totalItemCount)
        val confirmMsg: String = makeConfirmMsg(actionId, resources, tipCount, totalItemCount)
        if (isRecycle && isSyncSwitchOpen && GalleryCommonListConfig.isNeedShowCountDownTime(tipCount)) {
            showBatchCloudDeleteDialog = true
        }
        resultCallback.invoke(hint, confirmMsg, title, showBatchCloudDeleteDialog)
    }

    /**
     * 依据选择图片和视频数量返回对应的数据类型，用以确定提示语
     */
    private fun getMediaType(videoCount: Int, imageCount: Int): Int {
        return if (videoCount == 0) {
            MEDIA_TYPE_ONLY_IMAGE
        } else if (imageCount == 0) {
            MEDIA_TYPE_ONLY_VIDEO
        } else {
            MEDIA_TYPE_CONTAIN_ALL
        }
    }

    /**
     *  获取tips confirMsg 文案
     *  @param actionId 操作事件ID
     *  @param selectedCount 选中的数量
     *  @param totalItemCount 总数量
     */
    private fun makeConfirmMsg(
        actionId: String,
        resources: Resources,
        selectedCount: Int,
        totalItemCount: Int
    ): String {
        val isDelete = ((actionId != ACTION_RESTORE) && (actionId != ACTION_RESTORE_ALL_RECYCLED))
        return if (actionId == ACTION_RECYCLE_ALBUM) {
            //图集
            resources.getString(BasebizR.string.base_comfirm_delete)
        } else if ((actionId == ACTION_DELETE_ALL_RECYCLED) || (actionId == ACTION_DELETE_RECYCLED)) {
            //最近删除-删除
            resources.getString(BasebizR.string.base_delete_single_permanently)
        } else {
            //其它
            makeRecycleConfirmMsg(selectedCount, totalItemCount, isDelete, resources)
        }
    }

    /**
     *  获取 confirmMsg 信息
     */
    private fun makeRecycleConfirmMsg(
        selectedCount: Int,
        totalItemCount: Int,
        isDelete: Boolean,
        resources: Resources
    ): String {
        return if (selectedCount == totalItemCount) {
            if (isDelete) {
                resources.getString(BasebizR.string.base_comfirm_delete_all)
            } else {
                resources.getString(BasebizR.string.base_restore)
            }
        } else {
            if (isDelete) {
                resources.getString(BasebizR.string.base_comfirm_delete)
            } else {
                resources.getString(BasebizR.string.base_restore)
            }
        }
    }

    /**
     *  获取tips message说明文案
     *  @param actionId 操作事件ID
     *  @param isSyncCloud 云同步状态
     *  @param mediaType 数据类型：1：图片；2：包含视频和图片；3：视频
     *  @param albumName albumName 图集名称
     *  @param selectedCount 数据选中的数量
     */
    fun makeTipsMessage(
        actionId: String,
        isSyncCloud: Boolean,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL,
        albumName: String? = null,
        isBigPictureMode: Boolean = false,
        selectedCount: Int,
        totalItemCount: Int = 0
    ): String? {
        //回收站 message 说明
        if ((actionId == ACTION_DELETE_ALL_RECYCLED) || (actionId == ACTION_DELETE_RECYCLED)) {
            return makeRecyledDialogMessage(
                isSyncCloud,
                mediaType = mediaType,
                isBigPictureMode = isBigPictureMode,
                selectedCount,
                totalItemCount
            )
        }
        if (!isSyncCloud) { //未开启云同步
            //未开启云同步，图集，大图，多选 message 提示语
            return when (actionId) {
                //图片
                ACTION_RECYCLE -> makeActionRecycleMessageNotCloudSync(isBigPictureMode, mediaType, selectedCount, totalItemCount)
                //图集
                ACTION_RECYCLE_ALBUM -> makeActionRecycleAlbumMessageNotCloudSync(albumName, selectedCount)
                else -> null
            }
        } else {
            return when (actionId) {
                //图片
                ACTION_RECYCLE -> makeActionRecycleMessage(isBigPictureMode, mediaType, selectedCount, totalItemCount)
                //图集
                ACTION_RECYCLE_ALBUM -> makeActionRecycleAlbumMessage(albumName, selectedCount)
                else -> null
            }
        }
    }

    /**
     *  获取 actionID == ACTION_RECYCLE_ALBUM 开启同步图集的message说明文案
     *  @param albumName 图集名称， 由于只有在单个图集时才显示名称，所以单个图集才有数据，多个图集时数据为空
     */
    private fun makeActionRecycleAlbumMessage(albumName: String?, selectedCount: Int): String? {
        return if (!isCloudSyncRecycleDeleteTipsFirstShow()) {
            //开启云同步第一次显示删除弹窗
            getDeleteString(
                BasebizR.string.base_export_recycle_delete_selected_album_hint,
                BasebizR.string.base_recycle_delete_selected_other_hint
            )
        } else {
            if (albumName.isNullOrEmpty()) {
                getQuantityDeleteString(
                    BasebizR.plurals.base_export_recycle_delete_selected_album_hint,
                    BasebizR.plurals.base_recycle_delete_selected_album_hint,
                    selectedCount
                )
            } else {
                activity?.get()?.resources?.getString(
                    if (isNeedShowSixtyDaysDeleteCopywrite) {
                        BasebizR.string.base_export_recycle_delete_selected_single_album_hint
                    } else {
                        BasebizR.string.base_recycle_delete_selected_single_album_hint
                    }, albumName
                )
            }
        }
    }

    /**
     *  获取 actionID == ACTION_RECYCLE_ALBUM 未开启同步图集的message说明文案
     *  @param albumName 图集名称， 由于只有在单个图集时才显示名称，所以单个图集才有数据，多个图集时数据为空
     */
    private fun makeActionRecycleAlbumMessageNotCloudSync(albumName: String?, selectedCount: Int): String? {
        return if (albumName.isNullOrEmpty()) {
            getQuantityDeleteString(
                BasebizR.plurals.base_export_recycle_delete_local_selected_album_hint,
                BasebizR.plurals.base_recycle_delete_local_selected_album_hint,
                selectedCount
            )
        } else {
            activity?.get()?.resources?.getString(
                if (isNeedShowSixtyDaysDeleteCopywrite) {
                    BasebizR.string.base_export_recycle_delete_local_selected_single_album_hint
                } else {
                    BasebizR.string.base_recycle_delete_local_selected_single_album_hint
                }, albumName
            )
        }
    }

    /**
     *  获取 actionID == ACTION_RECYCLE 选择模式下的message说明文案
     */
    private fun makeActionRecycleMessage(isBigPictureMode: Boolean, mediaType: Int, selectedCount: Int, totalItemCount: Int): String? {
        val resources = activity?.get()?.resources
        return when {
            isBigPictureMode -> singleChoiceMakeActionRecycleMessage(mediaType, selectedCount)
            //开启云同步第一次显示删除弹窗
            !isCloudSyncRecycleDeleteTipsFirstShow() -> {
                if (isNeedShowSixtyDaysDeleteCopywrite) {
                    when (mediaType) {
                        MEDIA_TYPE_ONLY_IMAGE -> resources?.getString(BasebizR.string.base_export_recycle_delete_selected_photo_hint)
                        MEDIA_TYPE_ONLY_VIDEO -> resources?.getString(BasebizR.string.base_export_recycle_delete_selected_video_hint)
                        else -> resources?.getString(BasebizR.string.base_export_recycle_delete_selected_project_hint)
                    }
                } else {
                    resources?.getString(BasebizR.string.base_recycle_delete_selected_other_hint)
                }
            }

            else -> multipleChoiceMakeActionRecycleMessage(mediaType, selectedCount, totalItemCount)
        }
    }

    private fun getDeleteString(exportDeleteStringId: Int, defaultDeletedStringId: Int): String? {
        return activity?.get()?.resources?.getString(if (isNeedShowSixtyDaysDeleteCopywrite) exportDeleteStringId else defaultDeletedStringId)
    }

    private fun getQuantityDeleteString(exportDeleteStringId: Int, defaultDeletedStringId: Int, selectedCount: Int): String? {
        return activity?.get()?.resources?.getQuantityString(
            if (isNeedShowSixtyDaysDeleteCopywrite) exportDeleteStringId else defaultDeletedStringId,
            selectedCount,
            selectedCount
        )
    }

    /**
     *  获取 actionID == ACTION_RECYCLE 多个选择模式下获取提示信息
     */
    private fun multipleChoiceMakeActionRecycleMessage(mediaType: Int, selectedCount: Int, totalItemCount: Int): String? {
        return if (selectedCount == totalItemCount) {
            when (mediaType) {
                MEDIA_TYPE_ONLY_IMAGE -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_selected_all_photo_hint,
                        BasebizR.string.base_recycle_delete_selected_all_photo_hint
                    )
                }

                MEDIA_TYPE_ONLY_VIDEO -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_selected_all_video_hint,
                        BasebizR.string.base_recycle_delete_selected_all_video_hint
                    )
                }

                else -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_selected_all_project_hint,
                        BasebizR.string.base_recycle_delete_selected_all_project_hint
                    )
                }
            }
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_IMAGE -> {
                    getQuantityDeleteString(
                        BasebizR.plurals.base_export_recycle_delete_selected_photo_hint,
                        BasebizR.plurals.base_recycle_delete_selected_photo_hint,
                        selectedCount
                    )
                }

                MEDIA_TYPE_ONLY_VIDEO -> {
                    getQuantityDeleteString(
                        BasebizR.plurals.base_export_recycle_delete_selected_video_hint,
                        BasebizR.plurals.base_recycle_delete_selected_video_hint,
                        selectedCount
                    )
                }

                else -> {
                    getQuantityDeleteString(
                        BasebizR.plurals.base_export_recycle_delete_selected_project_hint,
                        BasebizR.plurals.base_recycle_delete_selected_project_hint,
                        selectedCount
                    )
                }
            }
        }
    }

    /**
     *  获取 actionID == ACTION_RECYCLE 多个选择模式下获取提示信息
     */
    private fun multipleChoiceMakeActionRecycleMessageNotCloudSync(mediaType: Int, selectedCount: Int, totalItemCount: Int): String? {
        return if (selectedCount == totalItemCount) {
            when (mediaType) {
                MEDIA_TYPE_ONLY_IMAGE -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_local_selected_all_photo_hint,
                        BasebizR.string.base_recycle_delete_local_selected_all_photo_hint
                    )
                }

                MEDIA_TYPE_ONLY_VIDEO -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_local_selected_all_video_hint,
                        BasebizR.string.base_recycle_delete_local_selected_all_video_hint
                    )
                }

                else -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_local_selected_all_project_hint,
                        BasebizR.string.base_recycle_delete_local_selected_all_project_hint
                    )
                }
            }
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_IMAGE -> {
                    getQuantityDeleteString(
                        BasebizR.plurals.base_export_recycle_delete_local_selected_photo_hint,
                        BasebizR.plurals.base_recycle_delete_local_selected_photo_hint,
                        selectedCount
                    )
                }

                MEDIA_TYPE_ONLY_VIDEO -> {
                    getQuantityDeleteString(
                        BasebizR.plurals.base_export_recycle_delete_local_selected_video_hint,
                        BasebizR.plurals.base_recycle_delete_local_selected_video_hint,
                        selectedCount
                    )
                }

                else -> {
                    getQuantityDeleteString(
                        BasebizR.plurals.base_export_recycle_delete_local_selected_project_hint,
                        BasebizR.plurals.base_recycle_delete_local_selected_project_hint,
                        selectedCount
                    )
                }
            }
        }
    }

    /**
     *  获取 actionID == ACTION_RECYCLE 大图页-单个选择下获取提示信息
     *  @param selectedCount 大图页表示连拍照片数量，大于 1 表示时连拍照片，显示连拍提示语
     */
    private fun singleChoiceMakeActionRecycleMessage(mediaType: Int, selectedCount: Int): String? {
        return if (selectedCount > 1) {
            getQuantityDeleteString(
                BasebizR.plurals.base_export_delete_selected_schot_project_hint,
                BasebizR.plurals.base_delete_selected_schot_project_hint,
                selectedCount
            )
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_VIDEO -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_this_video_hint,
                        BasebizR.string.base_recycle_delete_this_video_hint
                    )
                }

                else -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_this_photo_hint,
                        BasebizR.string.base_recycle_delete_this_photo_hint
                    )
                }
            }
        }
    }

    /**
     *  获取 actionID == ACTION_RECYCLE 大图页-单个选择下获取提示信息
     *  @param selectedCount 大图页表示连拍照片数量，大于 1 表示时连拍照片，显示连拍提示语
     */
    private fun singleChoiceMakeActionRecycleMessageNotCloudSync(mediaType: Int, selectedCount: Int): String? {
        return if (selectedCount > 1) {
            getQuantityDeleteString(
                BasebizR.plurals.base_export_delete_local_selected_schot_project_hint,
                BasebizR.plurals.base_delete_local_selected_schot_project_hint,
                selectedCount
            )
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_VIDEO -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_local_selected_this_video_hint,
                        BasebizR.string.base_recycle_delete_local_selected_this_video_hint
                    )
                }

                else -> {
                    getDeleteString(
                        BasebizR.string.base_export_recycle_delete_local_selected_this_photo_hint,
                        BasebizR.string.base_recycle_delete_local_selected_this_photo_hint
                    )
                }
            }
        }
    }

    /**
     *  未云同步：获取 actionID == ACTION_RECYCLE 的message说明文案
     *  @param selectedCount 表示选中数据数量； 大图页连拍数据表示连拍的数量
     */
    private fun makeActionRecycleMessageNotCloudSync(isBigPictureMode: Boolean, mediaType: Int, selectedCount: Int, totalItemCount: Int): String? {
        return if (isBigPictureMode) {
            singleChoiceMakeActionRecycleMessageNotCloudSync(mediaType, selectedCount)
        } else {
            multipleChoiceMakeActionRecycleMessageNotCloudSync(mediaType, selectedCount, totalItemCount)
        }
    }

    /**
     * 删除操作-dialog的标题
     * @param isSyncCloud 是否开启云同步
     * @param mediaType 数据类型：1：图片；2：包含视频和图片；3：视频
     * @param isBigPictureMode 是否是大图页进入 true：大图页；false：选择模式；
     * @return  标题
     */
    private fun makeRecycleDialogTitle(
        actionId: String,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL,
        isBigPictureMode: Boolean = false,
        albumName: String? = null,
        selectedCount: Int,
        totalItemCount: Int
    ): String? {
        val isSyncSwitchOpen = getCloudSyncDM().isAlbumSyncSwitchOpen()
        return if (isBigPictureMode) {
            return singleChoiceMakeActionRecycleTitle(mediaType, selectedCount)
        } else {
            when (actionId) {
                //图集
                ACTION_RECYCLE_ALBUM -> multipleChoiceAlbumMakeActionRecycleTitle(albumName, selectedCount, isSyncSwitchOpen)
                ACTION_RECYCLE -> multipleChoiceMakeActionRecycleTitle(mediaType, selectedCount, totalItemCount, isSyncSwitchOpen)
                else -> null
            }
        }
    }

    /**
     * 获取 actionID == ACTION_RECYCLE 大图页-单个选择的标题信息
     * @param selectedCount 表示连拍照片数量，大于 1 表示时连拍照片，显示连拍提示语
     */
    private fun singleChoiceMakeActionRecycleTitle(mediaType: Int, selectedCount: Int): String? {
        return if (selectedCount > 1) {
            activity?.get()?.resources?.getQuantityString(
                BasebizR.plurals.base_delete_selected_these_cshot_title,
                selectedCount,
                selectedCount
            )
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_VIDEO -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_seleted_this_video_title)
                else -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_seleted_this_photo_title)
            }
        }
    }

    /**
     * 获取 actionID == ACTION_RECYCLE 选择多个时的标题信息
     */
    private fun multipleChoiceMakeActionRecycleTitle(
        mediaType: Int,
        selectedCount: Int,
        totalItemCount: Int,
        isSyncSwitchOpen: Boolean
    ): String? {
        val isRecycleDeleteTipsFirstShow = isCloudSyncRecycleDeleteTipsFirstShow()
        val isSelectedAll = (selectedCount == totalItemCount)
        val resources = activity?.get()?.resources ?: return null
        return when (mediaType) {
            MEDIA_TYPE_ONLY_IMAGE -> {
                when {
                    (isSyncSwitchOpen && !isRecycleDeleteTipsFirstShow) && isSelectedAll -> {
                        resources.getString(BasebizR.string.base_recycle_delete_selected_all_photo_title)
                    }

                    (isSyncSwitchOpen && !isRecycleDeleteTipsFirstShow) && !isSelectedAll -> {
                        resources.getQuantityString(BasebizR.plurals.base_recycle_delete_selected_photo_title, selectedCount, selectedCount)
                    }

                    isSelectedAll -> resources.getString(BasebizR.string.base_delete_seleted_all_photo_title)
                    else -> resources.getQuantityString(BasebizR.plurals.base_delete_seleted_these_photo_title, selectedCount, selectedCount)
                }
            }

            MEDIA_TYPE_ONLY_VIDEO -> {
                when {
                    (isSyncSwitchOpen && !isRecycleDeleteTipsFirstShow) && isSelectedAll -> {
                        resources.getString(BasebizR.string.base_recycle_delete_selected_all_video_title)
                    }

                    (isSyncSwitchOpen && !isRecycleDeleteTipsFirstShow) && !isSelectedAll -> {
                        resources.getQuantityString(BasebizR.plurals.base_recycle_delete_selected_video_title, selectedCount, selectedCount)
                    }

                    isSelectedAll -> resources.getString(BasebizR.string.base_delete_seleted_all_video_title)
                    else -> resources.getQuantityString(BasebizR.plurals.base_delete_seleted_these_video_title, selectedCount, selectedCount)
                }
            }

            else -> {
                when {
                    (isSyncSwitchOpen && !isRecycleDeleteTipsFirstShow) && isSelectedAll -> {
                        resources.getString(BasebizR.string.base_recycle_delete_selected_all_project_title)
                    }

                    (isSyncSwitchOpen && !isRecycleDeleteTipsFirstShow) && !isSelectedAll -> {
                        resources.getQuantityString(BasebizR.plurals.base_recycle_delete_selected_project_title, selectedCount, selectedCount)
                    }

                    isSelectedAll -> resources.getString(BasebizR.string.base_delete_seleted_all_project_title)
                    else -> resources.getQuantityString(BasebizR.plurals.base_delete_seleted_these_project_title, selectedCount, selectedCount)
                }
            }
        }
    }

    /**
     * 获取 actionID == ACTION_RECYCLE_ALBUM 图集的标题信息
     */
    private fun multipleChoiceAlbumMakeActionRecycleTitle(albumName: String?, selectedCount: Int, isSyncSwitchOpen: Boolean): String? {
        return if (isSyncSwitchOpen && !isCloudSyncRecycleDeleteTipsFirstShow()) {
            if (albumName.isNullOrEmpty()) {
                activity?.get()?.resources?.getQuantityString(
                    BasebizR.plurals.base_recycle_delete_selected_album_title,
                    selectedCount,
                    selectedCount
                )
            } else {
                activity?.get()?.resources?.getString(BasebizR.string.base_recycle_delete_single_album_title, albumName)
            }
        } else {
            if (albumName.isNullOrEmpty()) {
                activity?.get()?.resources?.getQuantityString(
                    BasebizR.plurals.base_delete_selected_these_album_title,
                    selectedCount,
                    selectedCount
                )
            } else {
                activity?.get()?.resources?.getString(BasebizR.string.base_delete_selected_album_title, albumName)
            }
        }
    }

    /**
     * 最近删除图集
     * @param isSyncCloud 是否开启云同步
     * @param mediaType 数据类型：1：图片；2：包含视频和图片；3：视频
     * @param isBigPictureMode 是否是大图页进入 true：大图页；false：选择模式；
     * @return  标题
     */
    private fun makeRecyledDialogTitle(
        resources: Resources,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL,
        isBigPictureMode: Boolean = false,
        selectedCount: Int,
        totalItemCount: Int
    ): String {
        return if (isBigPictureMode) {
            singleChoiceMakeRecycledTitle(mediaType, resources)
        } else {
            multipleChoiceMakeRecycledTitle(mediaType, resources, selectedCount, totalItemCount)
        }
    }

    /**
     * 最近删除图集
     * @param isSyncCloud 是否开启云同步
     * @param mediaType 数据类型：1：图片；2：包含视频和图片；3：视频
     * @param isBigPictureMode 是否是大图页进入 true：大图页；false：选择模式；
     * @return  message
     */
    private fun makeRecyledDialogMessage(
        isSyncCloud: Boolean,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL,
        isBigPictureMode: Boolean = false,
        selectedCount: Int,
        totalItemCount: Int
    ): String? {
        return if (!isSyncCloud) {
            if (isBigPictureMode) {
                singleChoiceMakeRecycledMessageWhenLocal(mediaType)
            } else {
                multipleChoiceMakeRecycledMessageWhenLocal(mediaType, selectedCount, totalItemCount)
            }
        } else {
            if (isBigPictureMode) {
                singleChoiceMakeRecycledMessageWhenSync(mediaType)
            } else {
                multipleChoiceMakeRecycledMessageWhenSync(mediaType, selectedCount, totalItemCount)
            }
        }
    }

    /**
     * 回收站-大图页，警示弹窗标题
     */
    private fun singleChoiceMakeRecycledTitle(mediaType: Int, resources: Resources): String {
        return when (mediaType) {
            MEDIA_TYPE_ONLY_VIDEO -> resources.getString(BasebizR.string.base_delete_seleted_this_video_title)
            else -> resources.getString(BasebizR.string.base_delete_seleted_this_photo_title)
        }
    }

    /**
     * 回收站-选择页，警示弹窗标题
     */
    private fun multipleChoiceMakeRecycledTitle(mediaType: Int, resources: Resources, selectedCount: Int, totalItemCount: Int): String {
        return if (selectedCount == totalItemCount) {
            when (mediaType) {
                MEDIA_TYPE_ONLY_VIDEO -> resources.getString(BasebizR.string.base_delete_seleted_all_video_title)
                MEDIA_TYPE_ONLY_IMAGE -> resources.getString(BasebizR.string.base_delete_seleted_all_photo_title)
                else -> resources.getString(BasebizR.string.base_delete_seleted_all_project_title)
            }
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_VIDEO -> {
                    resources.getQuantityString(
                        BasebizR.plurals.base_delete_seleted_these_video_title,
                        selectedCount,
                        selectedCount
                    )
                }

                MEDIA_TYPE_ONLY_IMAGE -> {
                    resources.getQuantityString(
                        BasebizR.plurals.base_delete_seleted_these_photo_title,
                        selectedCount,
                        selectedCount
                    )
                }

                else -> {
                    resources.getQuantityString(
                        BasebizR.plurals.base_delete_seleted_these_project_title,
                        selectedCount,
                        selectedCount
                    )
                }
            }
        }
    }

    /**
     * 回收站-大图页，未开启云同步-警示弹窗删除提示message
     */
    private fun singleChoiceMakeRecycledMessageWhenLocal(mediaType: Int): String? {
        return when (mediaType) {
            MEDIA_TYPE_ONLY_VIDEO -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_local_selected_permanently_this_video_title)
            else -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_local_selected_permanently_this_photo_title)
        }
    }

    /**
     * 回收站-选择页，未开启云同步-警示弹窗删除提示message
     */
    private fun multipleChoiceMakeRecycledMessageWhenLocal(mediaType: Int, selectedCount: Int, totalItemCount: Int): String? {
        return if (selectedCount == totalItemCount) {
            when (mediaType) {
                MEDIA_TYPE_ONLY_VIDEO -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_local_selected_permanently_all_video_title)
                MEDIA_TYPE_ONLY_IMAGE -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_local_selected_permanently_all_photo_title)
                else -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_local_selected_permanently_all_project_title)
            }
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_VIDEO -> {
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.base_delete_local_selected_permanently_video_title,
                        selectedCount,
                        selectedCount
                    )
                }

                MEDIA_TYPE_ONLY_IMAGE -> {
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.base_delete_local_selected_permanently_photo_title,
                        selectedCount,
                        selectedCount
                    )
                }

                else -> {
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.base_delete_local_selected_permanently_project_title,
                        selectedCount,
                        selectedCount
                    )
                }
            }
        }
    }

    /**
     * 回收站-大图页，开启云同步-警示弹窗删除提示message
     */
    private fun singleChoiceMakeRecycledMessageWhenSync(mediaType: Int): String? {
        return when (mediaType) {
            MEDIA_TYPE_ONLY_VIDEO -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_selected_permanently_this_video_title)
            else -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_selected_permanently_this_photo_title)
        }
    }

    /**
     * 回收站-选择页，开启云同步-警示弹窗删除提示message
     */
    private fun multipleChoiceMakeRecycledMessageWhenSync(mediaType: Int, selectedCount: Int, totalItemCount: Int): String? {
        return if (selectedCount == totalItemCount) {
            when (mediaType) {
                MEDIA_TYPE_ONLY_IMAGE -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_selected_permanently_all_photo_title)
                MEDIA_TYPE_ONLY_VIDEO -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_selected_permanently_all_video_title)
                else -> activity?.get()?.resources?.getString(BasebizR.string.base_delete_selected_permanently_all_project_title)
            }
        } else {
            when (mediaType) {
                MEDIA_TYPE_ONLY_IMAGE -> {
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.base_delete_selected_permanently_photo_title,
                        selectedCount,
                        selectedCount
                    )
                }

                MEDIA_TYPE_ONLY_VIDEO -> {
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.base_delete_selected_permanently_video_title,
                        selectedCount,
                        selectedCount
                    )
                }

                else -> {
                    activity?.get()?.resources?.getQuantityString(
                        BasebizR.plurals.base_delete_selected_permanently_project_title,
                        selectedCount,
                        selectedCount
                    )
                }
            }
        }
    }

    /**
     * 清空相册
     * @param resources
     * @param totalItemCount 总的数量
     * @param imageCount 选择照片的数量
     * @param videoCount 选择视频的数量
     * @return 返回title
     */
    private fun makeClearAlbumTitle(
        resources: Resources,
        totalItemCount: Int = 0,
        imageCount: Int = 0,
        videoCount: Int = 0,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL
    ): String {
        return if (imageCount == 0) {
            if (totalItemCount == videoCount) {
                resources.getString(BasebizR.string.base_delete_seleted_all_video_title)
            } else {
                resources.getQuantityString(BasebizR.plurals.base_delete_seleted_these_video_title, videoCount, videoCount)
            }
        } else if (videoCount == 0) {
            if (totalItemCount == imageCount) {
                resources.getString(
                    if ((isNeedShowSixtyDaysDeleteCopywrite) && (mediaType == MEDIA_TYPE_CONTAIN_ALL)) {
                        BasebizR.string.base_delete_seleted_all_project_title
                    } else {
                        BasebizR.string.base_delete_seleted_all_photo_title
                    }
                )
            } else {
                resources.getQuantityString(
                    if ((isNeedShowSixtyDaysDeleteCopywrite) && (mediaType == MEDIA_TYPE_CONTAIN_ALL)) {
                        BasebizR.plurals.base_delete_seleted_these_project_title
                    } else {
                        BasebizR.plurals.base_delete_seleted_these_photo_title
                    }, imageCount, imageCount
                )
            }
        } else {
            if (totalItemCount == (imageCount + videoCount)) {
                resources.getString(BasebizR.string.base_delete_seleted_all_project_title)
            } else {
                resources.getQuantityString(
                    BasebizR.plurals.base_delete_seleted_these_project_title,
                    (videoCount + imageCount),
                    (videoCount + imageCount)
                )
            }
        }
    }

    /**
     * 清空相册
     * @param isSecond 是否是二次弹窗
     * @param actionId 事件ID
     * @return first 标题 或 其他新增内容， second： 提示语msg， third： confirm 信息
     */
    private fun makeClearAlbumHint(
        resources: Resources,
        isSecond: Boolean,
        mediaType: Int = MEDIA_TYPE_CONTAIN_ALL
    ): Triple<String?, String?, String> {
        return if (isSecond) {
            Triple(
                resources.getString(BasebizR.string.base_recycle_delete_close_cloud_and_delete_title),
                resources.getString(BasebizR.string.base_recycle_delete_close_cloud_and_delete_hint),
                resources.getString(BasebizR.string.base_recycle_delete_local_all)
            )
        } else {
            var deleteStringHintId = BasebizR.string.base_recycle_delete_only_local_all
            if (isNeedShowSixtyDaysDeleteCopywrite) {
                deleteStringHintId = when (mediaType) {
                    MEDIA_TYPE_ONLY_IMAGE -> BasebizR.string.base_export_recycle_delete_selected_photo_hint
                    MEDIA_TYPE_ONLY_VIDEO -> BasebizR.string.base_export_recycle_delete_selected_video_hint
                    else -> BasebizR.string.base_export_recycle_delete_selected_project_hint
                }
            }
            Triple(
                resources.getString(BasebizR.string.base_recycle_delete_only_local_all),
                resources.getString(deleteStringHintId),
                resources.getString(BasebizR.string.base_recycle_delete_all_devices)
            )
        }
    }

    fun handleOperationResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            CollageOperation.REQUEST_PHOTO_EFFECT,
            MenuRequestCode.GIF_SYNTHESIS_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    menuResult?.handleResult(HashMap<String, Any?>().apply { put(MenuAction.KEY_INTENT, data) })
                }
            }
        }
    }

    /**
     * 移出桌面卡片轮播列表（移出精选/自选列表）
     */
    fun removeFromWidgetDisplayList(
        itemPathList: List<Path>,
        displayListId: String,
        trackCallerEntry: TrackCallerEntry,
        onCompleted: (() -> Unit)?
    ): MenuResult = MenuOperationManager.doAction(
        action = MenuAction.REMOVE_FROM_WIDGET_LIST,
        paramMap = MenuActionGetter.removeFromWidgetList.builder
            .setPathList(itemPathList)
            .setDisplayListId(displayListId)
            .setTrackCallerEntry(trackCallerEntry)
            .build(),
        onCompleted = { resultCode: String, resultMap: Map<String, Any>? ->
            GLog.d(MODULE_NAME, TAG, LogFlag.DL, "removeFromWidgetDisplayList on complete: resultCode:" + resultCode + "resultMap:" + resultMap)
            onCompleted?.invoke()
        }
    )

    fun updateDialogIfNeed() {
        menuViewRef?.get()?.post {
            val selectedMenuItemView = getSelectedMenuItemView()
            fromBottomDialog?.update(selectedMenuItemView)
        }
        // 新建图集、重命名图集编辑弹窗
        menuEventNotifier?.sendEvent(MenuOperation.EventNotifier.EVENT_ID_SCREEN_CHANGED)
    }

    fun bindMenuView(menuView: View?) {
        if (menuView != menuViewRef?.get()) {
            menuViewRef = WeakReference(menuView)
        }
    }

    fun setClickedMenuItemId(selectedItemId: Int) {
        this.selectedItemId = selectedItemId
    }

    private fun getSelectedMenuItemView(): View? {
        return menuViewRef?.get()?.findViewById(selectedItemId)
    }

    private fun isSupportUserCustomSafeBox(): Boolean = activity?.get()?.getAppAbility<IConfigAbility>()?.use {
        it.getBooleanConfig(FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX) ?: false
    } ?: false

    private fun runWhenActivityRunning(onDo: (() -> Unit)? = null, onCancel: (() -> Unit)? = null) {
        activity?.get()?.let { activity ->
            if (!activity.isFinishing && !activity.isDestroyed) {
                onDo?.invoke()
            } else {
                onCancel?.invoke()
            }
        } ?: kotlin.run {
            onCancel?.invoke()
        }
    }

    private fun isCloudSyncRecycleDeleteTipsFirstShow() =
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.CloudSync.AlbumDelete.RECYCLE_TIPS_AS_FIRST_SHOW, false)

    private fun isCloudSyncRecycledDeleteTipsFirstShow() =
        ConfigAbilityWrapper.getBoolean(ConfigID.Business.CloudSync.AlbumDelete.RECYCLED_DELETE_TIPS_AS_FIRST_SHOW, false)

    /**
     * 对业务层的弹窗事务代理
     */
    interface DialogDelegate {

        /**
         * 取消dialog
         */
        fun cancel()

        /**
         * 设置dialog消失的监听器
         */
        fun setOnDismissListener(listener: () -> Unit)
    }

    private class BaseAlertDialogDelegate(val dialog: BaseAlertDialog<out Any>) : DialogDelegate {
        override fun cancel() {
            dialog.cancel()
        }

        override fun setOnDismissListener(listener: () -> Unit) {
            dialog.setOnDismissListener {
                listener()
            }
        }
    }

    companion object {
        /** action回调状态*/
        const val ACTION_STATE_SUCCESS = 0
        const val ACTION_STATE_START = 1
        const val ACTION_STATE_COMPLETED = 2
        const val ACTION_STATE_CANCEL = 3
        const val ACTION_STATE_FAILED = 4
        const val GIF_SYNTHESIS_MIN_COUNT = 2

        const val MINIMUM_ITEM_FOR_JIGSAW = 2
        const val MAXIMUM_ITEM_FOR_JIGSAW = 9
        const val MINIMUM_ITEM_FOR_FILM_CREATION = 1
        const val MAXIMUM_ITEM_FOR_FILM_CREATION = 50
        const val MAXIMUM_ITEM_OLIVE = 50
        const val ACTION_RECYCLE = "action_recycle"

        //专用于 【所有项目】图集 全部删除照片的场景（清空相册）
        const val ACTION_RECYCLE_ALL = "action_recycle_all"
        const val ACTION_DELETE_RECYCLED = "action_delete_recycled"
        const val ACTION_RESTORE = "action_restore"
        const val ACTION_RESTORE_ALL_RECYCLED = "action_restore_all"
        const val ACTION_DELETE_ALL_RECYCLED = "action_delete_all"
        const val ACTION_RECYCLE_ALBUM = "action_recycle_album"
        const val ACTION_SHARED_MEDIA_DELETE = "action_shared_media_delete"
        const val ACTION_SHARED_MEDIA_SAVE = "action_shared_media_save"
        const val ACTION_DELETE_MEMORIES = "action_delete_memories"
        const val ACTION_NOT_THIS_PERSON = "action_not_this_person"
        const val ACTION_MERGE_PERSON_PET_ALBUM = "action_merge_person_pet_album"
        const val ACTION_REMOVE_PERSON_PET_ALBUM = "action_remove_person_pet_album"
        const val ACTION_MERGE_AND_MOVE_TO_PERSON_ALBUM = "action_merge_and_move_to_person_album"
        const val ACTION_MOVE_TO_OTHER_PERSON_ALBUM = "action_move_to_other_person_album"
        const val ACTION_MOVE_TO_PERSON_ALBUM = "action_move_to_person_album"
        const val ACTION_NOT_THIS_LABEL = "action_not_this_label"

        // 整个图集加密
        const val ACTION_ALBUM_SAFE_BOX = "safeBoxAlbum"
        // 移出常用图集
        const val ACTION_MOVE_OUT_SHORTCUT_ALBUM = "moveOutShortcut"
        // 移入常用图集
        const val ACTION_MOVE_IN_SHORTCUT_ALBUM = "moveInShortCut"

        private const val TAG = "BottomMenuHelper"
        private const val PROGRESS_START = 0
        private const val INVALID_PROGRESS_MAX = 0
        private const val RECYCLED_COUNTDOWN_MILLISECOND = 5000L
        private const val RECYCLED_COUNTDOWN_INTERVAL = 1000L
        private const val MEDIA_TYPE_CONTAIN_ALL = 2
        private const val MEDIA_TYPE_ONLY_VIDEO = 3
        private const val MEDIA_TYPE_ONLY_IMAGE = 1
        private const val LIMIT_PIC_COUNT = 99
        private const val MODULE_NAME = "business_lib"

        private val isSupportQuickPhotoDelete: Boolean by lazy {
            ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_QUICK_PHOTO_DELETE)
        }

        fun startMovingAnimation(context: Context, view: ImageView) {
            val rotate = AnimationUtils.loadAnimation(context, R.anim.quick_loading)
            view.startAnimation(rotate)
        }

        fun removeMovingAnimation(view: ImageView) {
            view.clearAnimation()
        }

        fun updateSnackBar(context: Context, imageView: ImageView) {
            val drawable = context.getDrawable(R.drawable.base_loading_view)
            val layoutParams = imageView.layoutParams
            layoutParams.height = context.resources.getDimensionPixelSize(com.support.picker.R.dimen.coui_selected_background_horizontal_padding)
            layoutParams.width = context.resources.getDimensionPixelSize(com.support.picker.R.dimen.coui_selected_background_horizontal_padding)
            imageView.setImageDrawable(drawable)
            imageView.visibility = View.VISIBLE
        }

        fun generateLoadingDialog(
            activity: Activity,
            titleId: Int,
        ): DialogHelper = DialogHelper(
            activity,
            LoadingDialog.Builder(activity, false).apply {
                COUIThemeOverlay.getInstance().applyThemeOverlays(context)
                setTips(titleId)
                setCancelable(false)
            }
        )

        fun doMoveToAction(
            fragment: Fragment,
            srcAlbumPath: Path,
            paths: List<Path>,
            fromPage: Int = FROM_PAGE_UNKNOWN,
            onHandleResult: ((path: List<Path>?, result: String) -> Unit)? = null
        ): MenuResult {
            val itemsMediaType = TypeFilterUtils.getMediaTypeByPathString(paths)
            return MenuOperationManager.doAction(
                MenuAction.MOVE_TO,
                MenuActionGetter.moveTo.builder
                    .setPaths(paths)
                    .setIsStartActivity(true)
                    .setFragment(fragment)
                    .setItemsMediaType(itemsMediaType)
                    .setFromPage(fromPage)
                    .setAlbumSetPath(srcAlbumPath.toString())
                    .setLifecycle(fragment.lifecycle)
                    .build(),
                onCompleted = { result, resultMap: Map<String, Any>? ->
                    if (result != MenuAction.RESULT_SUCCESS) {
                        GLog.e(MODULE_NAME, TAG, LogFlag.DL, "doMoveToAction:onCompleted,result:$result")
                    }
                    onHandleResult?.invoke(paths, result)
                }
            )
        }
    }

    /**
     *  生命周期感知的 Callback 包装器,因为外部引入过多无法一一控制callback的引用
     *  故通过lifecycle自动管理，避免callback里面强持有activity/fragment，导致内存泄漏
     */
    class LifecycleCallbackWrapper(
        lifecycle: Lifecycle,
        callback: ((List<Path>?, Int) -> Unit)
    ) : DefaultLifecycleObserver {

        private var _callback: ((List<Path>?, Int) -> Unit)? = null

        init {
            _callback = callback
            lifecycle.addObserver(this) // 注册生命周期监听
        }

        fun invoke(path: List<Path>?, state: Int) {
            _callback?.invoke(path, state)
        }

        /**
         * 当 Lifecycle 销毁时自动清理 callback
         */
        override fun onDestroy(owner: LifecycleOwner) {
            _callback = null
            owner.lifecycle.removeObserver(this) // 移除监听
        }
    }
}