/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumThumbDispatcher.kt
 ** Description: 基础列表缩图分发器，只加载当前页面的素材，会优先加载可视区域
 **
 ** Version: 1.0
 ** Date: 2020/07/22
 ** Author: <EMAIL>
 ** TAG: OPLUS_THUMBNAILDISPATCHER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/07/22		1.0		OPLUS_THUMBNAILDISPATCHER
 *********************************************************************************/
package com.oplus.gallery.business_lib.viewmodel.loader

import android.content.Context
import android.graphics.drawable.Drawable
import com.oplus.gallery.basebiz.task.MediaItemThumbnailTask
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.basebiz.task.ThumbTaskDispatcher
import com.oplus.gallery.basebiz.task.BaseThumbnailTask
import com.oplus.gallery.standard_lib.graphics.GridDrawableType
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession

/**
 * 基础列表图集任务分发器
 */
class AlbumThumbDispatcher(
    private val context: Context,
    session: WorkerSession,
    private val taskManager: ThumbTaskMapManager<BaseThumbnailTask>,
    tag: String = "AlbumThumbDispatcher",
) : ThumbTaskDispatcher<BaseThumbnailTask>(tag, taskManager, session) {

    fun createThumbnailTask(mediaItem: MediaItem, thumbStyle: StyleData): MediaItemThumbnailTask {
        return MediaItemThumbnailTask(context, mediaItem, thumbStyle)
    }

    fun createDrawableThumbnailTask(drawable: Drawable, thumbStyle: StyleData): DrawableThumbnailTask {
        return DrawableThumbnailTask(context, drawable, thumbStyle)
    }

    fun createGridThumbnailTask(mediaItemList: List<MediaItem>, replaceThumbs: Map<Int, Drawable>, thumbStyle: StyleData): BaseThumbnailTask {
        return MultiThumbnailTask(context, mediaItemList, replaceThumbs, thumbStyle)
    }

    /**
     * 创建封面为5张缩图的task
     */
    fun createFiveGridThumbnailTask(mediaItemList: List<MediaItem>, thumbStyle: StyleData, scaleBitmapSize: Int): BaseThumbnailTask {
        return MultiThumbnailTask(context, mediaItemList, emptyMap(), thumbStyle, GridDrawableType.FIVE_GRID).apply {
            needBlurBackground = true
            blurBitmapScaleSize = scaleBitmapSize
        }
    }

    fun createRoundThumbnailTask(mediaItem: MediaItem, thumbStyle: StyleData): BaseThumbnailTask {
        return MediaItemThumbnailTask(context, mediaItem, thumbStyle)
    }

    override fun submitTask(task: BaseThumbnailTask) {
        session.submit(task.thumbnailLoaderJob) { future ->
            requestNext()
            taskManager.takeIf { it.isEmpty() }?.printTaskLog()
            // 执行结束了,thumbnail.futureListener.onFutureDone如果异常则会导致size个数异常，所以catch住
            future.get()?.let {
                task.onLoadComplete(it)
            }
        }
    }
}