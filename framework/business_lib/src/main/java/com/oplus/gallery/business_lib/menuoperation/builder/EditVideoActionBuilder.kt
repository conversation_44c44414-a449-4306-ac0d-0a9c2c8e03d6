/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditVideoActionBuilder.kt
 ** Description:编辑（视频）菜单参数建造器
 ** Version: 1.0
 ** Date: 2020/11/3
 ** Author: luyao.Tan@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_SETTING_ACTION_BUILDER
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** luyao.Tan@Apps.Gallery3D      2020/11/3    1.0           build this module
 *************************************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.builder

import android.graphics.ColorSpace
import android.graphics.drawable.Drawable
import android.net.Uri
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_TYPE_TAG
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_PHOTO_POSITION_FOR_EDITOR
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CHAIN_FROM
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.menuoperation.EditPhotoAction
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction
import com.oplus.gallery.foundation.ui.transition.ITransitionBoundsProvider
import java.lang.ref.WeakReference

class EditVideoActionBuilder : ActionBuilder() {
    private var fragmentRef: WeakReference<BaseFragment>? = null
    private var invoker: String? = null
    private var invokerToken: String? = null
    private var chainFrom: String? = null
    private var videoUri: Uri? = null
    private var modelType: String? = null
    private var screenOrientation: Int? = null
    private var mediaItemPath: String? = null
    private var thumbnail: Drawable? = null
    private var colorSpace: ColorSpace? = null
    private var positionStrategy: ITransitionBoundsProvider? = null
    private var editTypeTag: String? = null

    fun setInvoker(invoker: String): EditVideoActionBuilder = apply { this.invoker = invoker }
    fun setInvokerToken(token: String): EditVideoActionBuilder = apply { this.invokerToken = token }
    fun setModelType(modelType: String): EditVideoActionBuilder = apply { this.modelType = modelType }
    fun setChainFrom(chainFrom: String?): EditVideoActionBuilder = apply { this.chainFrom = chainFrom }
    fun setVideoUri(videoUri: Uri): EditVideoActionBuilder = apply { this.videoUri = videoUri }
    fun setFragment(fragment: BaseFragment): EditVideoActionBuilder = apply { this.fragmentRef = WeakReference<BaseFragment>(fragment) }
    fun setScreenOrientation(orientation: Int?): EditVideoActionBuilder = apply { this.screenOrientation = orientation }
    fun setMediaItemPath(path: String?): EditVideoActionBuilder = apply { this.mediaItemPath = path }
    fun setThumbnail(thumbnail: Drawable?): EditVideoActionBuilder = apply { this.thumbnail = thumbnail }
    fun setColorSpace(colorSpace: ColorSpace?): EditVideoActionBuilder = apply { this.colorSpace = colorSpace }

    /**
     * 设置大图位置策略：将进编辑前、回大图后的大图位置信息通知给编辑页。
     * @param positionStrategy 位置策略类
     */
    fun setPositionStrategy(positionStrategy: ITransitionBoundsProvider): EditVideoActionBuilder = apply {
        this.positionStrategy = positionStrategy
    }

    /**
     * 设置编辑类型
     */
    fun setEditTypeTag(editTypeTag: String?): EditVideoActionBuilder = apply { this.editTypeTag = editTypeTag }

    override fun innerBuild() = HashMap<String, Any?>().apply {
        put(EditVideoAction.KEY_FRAGMENT, fragmentRef)
        put(EditVideoAction.KEY_INVOKER, invoker)
        put(EditVideoAction.KEY_INVOKER_TOKEN, invokerToken)
        put(EditPhotoAction.KEY_MEDIA_MODEL_TYPE, modelType)
        put(EditVideoAction.KEY_VIDEO_URI, videoUri)
        put(EditVideoAction.KEY_MEDIA_ITEM_PATH, mediaItemPath)
        put(EditVideoAction.KEY_THUMBNAIL, thumbnail)
        put(EditVideoAction.KEY_COLOR_SPACE, colorSpace)
        put(IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION, screenOrientation)
        put(KEY_CHAIN_FROM, chainFrom)
        put(KEY_PHOTO_POSITION_FOR_EDITOR, positionStrategy)
        put(KEY_EDIT_TYPE_TAG, editTypeTag)
    }
}