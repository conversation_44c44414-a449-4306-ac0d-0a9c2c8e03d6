/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description: 横向列表中的卡片视图设置
 **
 ** Version: 1.0
 ** Date: 2025/5/23
 ** Author: 80407954@OppoGallery3D
 ** TAG: xx
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80407954@OppoGallery3D  2025/5/23  1.0        横向列表中的卡片视图设置
 *********************************************************************************/
package com.oplus.gallery.business_lib.ui.view

import android.content.Context
import androidx.core.view.isGone
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.business_lib.cardcase.utils.CardCaseUtils
import com.oplus.gallery.business_lib.viewmodel.style.IStylePool
import com.oplus.gallery.foundation.util.ext.updateSize
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.baselist.view.ListViewDataBinding

class ResponsiveCardAlbumSetViewDataBinding(
    private val layoutDetail: LayoutDetail,
    context: Context,
    stylePool: IStylePool?
) : CardAlbumSetViewDataBinding(context, stylePool) {

    override fun onBindViewHolder(itemViewHolder: BaseListViewHolder<AlbumViewData>, position: Int, viewData: AlbumViewData?) {
        super.onBindViewHolder(itemViewHolder, position, viewData)
        imageCover?.updateSize(width = context.resources.getDimensionPixelOffset(R.dimen.label_album_set_item_cover_width))
        cardItemLayout?.updateSize(width = layoutDetail.itemWidth)
    }

    override fun copyLayout(context: Context): ListViewDataBinding<AlbumViewData> {
        return ResponsiveCardAlbumSetViewDataBinding(layoutDetail, context, stylePool)
    }

    override fun updateTitleText(viewData: AlbumViewData) {
        super.updateTitleText(viewData)
        textSubtitle?.isGone = CardCaseUtils.isCardIDAlbum(viewData.albumId)
    }
}