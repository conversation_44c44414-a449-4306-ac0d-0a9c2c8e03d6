/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ShareHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/10/14
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/10/14		1.0		OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.helper

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.ArraySet
import androidx.collection.arraySetOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_SELECTION_DATA_ID
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask.Companion.autoCancelWhenOnDestroy
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.builder.FileProcessTaskBuilder
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SHARE
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.business_lib.model.selection.PathSelectionManager
import com.oplus.gallery.business_lib.model.selection.SelectionData
import com.oplus.gallery.business_lib.securityshare.SecurityShareHelper
import com.oplus.gallery.business_lib.securityshare.data.ShareConfig
import com.oplus.gallery.business_lib.sharetransform.TransformManager
import com.oplus.gallery.business_lib.util.ShareUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.dbaccess.convert.IConvert
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Package.PACKAGE_NAME_OSHARE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object ShareHelper {
    private const val TAG = "ShareHelper"

    /**
     * 分享弹窗消失，并应该退出编辑模式
     */
    const val STATE_DISMISS_AND_EXIT_EDIT_MODE = 0

    /**
     * 分享弹窗消失，并应该更新选择状态
     */
    const val STATE_DISMISS_AND_UPDATE_SELECTED_SHOW = 1

    /**
     * 分享操作失败，图片数量太多导致的无法分享。
     */
    const val STATE_SHARE_FAILED_BY_LARGE_COUNT = 2

    /**
     * 没有调用相册的分享页，调用了系统的分享页进行分享。
     */
    const val STATE_SHARE_BY_SYSTEM_SHARE = 3

    /**
     * 最大分享图片数量
     */
    const val SEND_MAX_LIMIT = 1000

    /**
     * 指定的ItemPath
     */
    const val KEY_ITEM_PATH = "key-media-path"

    /**
     * 分享页的focusPosition
     */
    const val KEY_FOCUS = "key-focus"

    /**
     * 默认分享index
     *
     * @see SecurityShareHelper.sharedAppIndex
     */
    const val DEFAULT_SHARED_APP_INDEX = -1

    /**
     * 适用于有选择态的界面
     * 如：图集页面
     */
    @JvmStatic
    fun showShareDialog(
        activity: BaseActivity,
        bundle: Bundle,
        itemPath: Path? = null,
        focusPosition: Int = -1,
        paths: Set<Path>,
        updateShowCallback: ((state: Int) -> Unit)? = null,
        trackCallerEntry: TrackCallerEntry,
    ) {
        val isSupportUserCustomGalleryShare: Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE)
        if (!isSupportUserCustomGalleryShare) {
            ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.base_feature_is_disabled_tip)
            return
        }
        if (!FeatureUtils.isContainerUser) {
            MenuOperationManager.doAction(
                SHARE,
                MenuActionGetter.share.builder
                    .setActivity(activity)
                    .setBundle(bundle.apply {
                        itemPath?.let {
                            putString(KEY_ITEM_PATH, itemPath.toString())
                            if (focusPosition != -1) {
                                putInt(KEY_FOCUS, focusPosition)
                            }
                        }
                    })
                    .setIntent(Intent())
                    .setUpdateShowCallback(updateShowCallback)
                    .setTrackCallerEntry(trackCallerEntry)
                    .build()
            )
        } else {
            if (showLimitToast(bundle)) {
                updateShowCallback?.invoke(STATE_SHARE_FAILED_BY_LARGE_COUNT)
                return
            }
            activity.lifecycleScope.launch {
                val intent = Intent().addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
                if (paths.size == 1) {
                    intent.action = Intent.ACTION_SEND
                    val convert = withContext(Dispatchers.CPU) {
                        PathToUriConvert.convert(paths.first())
                    }
                    intent.type = convert.second
                    intent.putExtra(Intent.EXTRA_STREAM, convert.first)
                } else if (paths.size > 1) {
                    intent.action = Intent.ACTION_SEND_MULTIPLE
                    val convert = withContext(Dispatchers.CPU) {
                        PathsToUrisConvert.convert(paths)
                    }
                    intent.type = convert?.second
                    intent.putParcelableArrayListExtra(
                        Intent.EXTRA_STREAM,
                        ArrayList<Uri>().also {
                            convert?.first?.also(it::addAll)
                        }
                    )
                } else {
                    intent.action = Intent.ACTION_SEND
                    intent.type = MimeTypeUtils.MIME_TYPE_IMAGE_ANY
                    GLog.w(TAG, "showShareDialog: paths is 0.")
                }
                showSysShareDialog(intent, activity)
                updateShowCallback?.invoke(STATE_SHARE_BY_SYSTEM_SHARE)
            }
        }
        ApiDmManager.getCloudSyncDM().updateUsageTime(paths.toMutableList())
    }

    /**
     * 适用于没有选择态的界面
     * 如：大图页面
     * @param focusPath:用于分享页计算居中图片
     * @param selectionPath:用于创建SelectionData
     */
    @JvmStatic
    fun showShareDialog(
        activity: BaseActivity,
        bundle: Bundle,
        focusPath: Path,
        focusPosition: Int = -1,
        selectionPath: Path,
        updateShowCallback: ((state: Int) -> Unit)? = null,
        trackCallerEntry: TrackCallerEntry,
    ) {
        val selectionData = PathSelectionManager.createSelectionData().apply {
            selectItem(selectionPath)
        }
        bundle.putInt(KEY_SELECTION_DATA_ID, selectionData.selectionDataId)
        showShareDialog(
            activity = activity,
            bundle = bundle,
            itemPath = focusPath,
            focusPosition = focusPosition,
            paths = arraySetOf(focusPath),
            trackCallerEntry = trackCallerEntry,
            updateShowCallback = fun(state: Int) {
                PathSelectionManager.destroyData(selectionData.selectionDataId)
                updateShowCallback?.invoke(state)
            },
        )
    }

    /**
     * 图片分享时清理缓存
     * @see 迁移修改参考自 com.oplus.gallery.sharepage.SendMediaFragment.cleanUpCache
     *
     * @param localActivity Activity实例
     * @param session 任务的session
     */
    fun cleanUpCache(localActivity: Activity, session: WorkerSession) {
        TransformManager(
            localActivity,
            session,
            TransformStorage.APP_PRIV_CACHE
        ).apply {
            submitCacheCleanUpTask(TransformType.HEIF)
            submitCacheCleanUpTask(TransformType.HDR_VIDEO)
            submitCacheCleanUpTask(TransformType.OLIVE)
        }
        SecurityShareHelper.submitCacheCleanUpTask()
    }


    /**
     * 下载原图
     *
     * 参考自
     * @see com.oplus.gallery.sharepage.SendMediaFragment.downloadOriginalPhoto
     *
     * @param successCallback 下载和准备成功的回调
     */
    @JvmStatic
    fun downloadOriginalPhoto(
        activity: BaseActivity?,
        lifecycle: Lifecycle,
        session: WorkerSession,
        selectedItems: List<Path>,
        successCallback: ((List<Path>) -> Unit)?
    ) {
        activity ?: return

        GLog.d(TAG, "[downloadOriginalPhoto] Execute file process task")
        activity.lifecycleScope.launch(Dispatchers.UI) {
            FileProcessTaskBuilder(
                activity,
                selectedItems,
                object : FinishListener {
                    override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                        GLog.d(TAG, "[downloadOriginalPhoto-onFinished] success: $success , errCode: $errCode , errMsg: $errMsg")
                        if (!success) return
                        if (activity.isFinishing || activity.isDestroyed) {
                            GLog.w(TAG, "[downloadOriginalPhoto-onFinished] activity is invalid!")
                            return
                        }
                        cleanUpCache(activity, session)
                        activity.lifecycleScope.launch(Dispatchers.UI) {
                            successCallback?.invoke(selectedItems)
                        }
                    }
                },
                FileProcessScene.DOWNLOAD_ORIGIN_SCENE_SHARE
            ).addTaskType(FileProcessTaskBuilder.TASK_TYPE_DOWNLOAD_ORIG)
                .build()?.let {
                    it.execute()
                    it.autoCancelWhenOnDestroy(lifecycle)
                }
        }
    }

    /**
     * 下载原图 && 分享
     *
     * 参考自
     * @see com.oplus.gallery.sharepage.sendbyoshare.SendByOShareFragment.downloadOrigAndShare
     */
    @JvmStatic
    fun downloadOrigAndShare(
        activity: BaseActivity,
        lifecycle: Lifecycle,
        session: WorkerSession,
        intent: Intent?,
        selectedItems: List<Path>,
        shareConfig: ShareConfig?,
        handleShareAction: (Intent?) -> Unit
    ) {
        intent ?: return

        val securityShareHelper = SecurityShareHelper(
            activity = activity,
            workerSession = session,
            shareConfig = shareConfig
        )

        GLog.d(TAG) {
            "[downloadOrigAndShare] Config [" +
                    "isEraseLocationInfo = ${securityShareHelper.isEraseLocationInfo} , " +
                    "isEraseShotInfo = ${securityShareHelper.isEraseShotInfo} , " +
                    "isNeedConvertImage = ${securityShareHelper.isNeedConvertImage} , " +
                    "isNeedConvertVideo = ${securityShareHelper.isNeedConvertVideo}]"
        }

        downloadOriginalPhoto(activity, lifecycle, session, selectedItems) {
            activity.lifecycleScope.launch(Dispatchers.IO) {
                GLog.d(TAG) { "[downloadOrigAndShare] pathList size = ${it.size}" }
                ShareUtils.updateShareAddScore(ArraySet(it))
                securityShareHelper.apply {
                    // 安全分享
                    if (isEraseAnyPrivacyInfo()) {
                        /**
                         * 因为这里进行隐私信息擦除的时候，它会对传递的Intent进行修改它里面的extra参数，
                         * 所以这里为了保证在擦除成功之后修改的intent对外部其他分享不影响，
                         * 就在传递给安全发送的时候由直接传递过来引用变为new出来一个Intent进行包装和外部隔离
                         */
                        val secureIntent = Intent(intent)
                        this.handleShareAction = {
                            handleShareAction.invoke(secureIntent)
                        }
                        startSecurityShare(secureIntent, DEFAULT_SHARED_APP_INDEX, selectedItems, PACKAGE_NAME_OSHARE)
                        return@launch
                    }
                }

                if (securityShareHelper.isConvertAnyFormat() && ShareUtils.isSupportShareTranscode(PACKAGE_NAME_OSHARE)) {
                    // 转码以及分享
                    ShareConvertHelper(activity, session, ArraySet(selectedItems)) { _, convertedMap ->
                        val secureIntent = Intent(intent)
                        ShareUtils.updateShareIntent(secureIntent, ArraySet(selectedItems), convertedMap)
                        handleShareAction.invoke(secureIntent)
                    }.checkConvertAndShare(
                        DEFAULT_SHARED_APP_INDEX,
                        securityShareHelper.isNeedConvertImage,
                        securityShareHelper.isNeedConvertVideo,
                        PACKAGE_NAME_OSHARE
                    )
                } else {
                    // 非安全分享
                    handleShareAction.invoke(intent)
                }
            }
        }
    }

    fun showLimitToast(
        bundle: Bundle? = null,
        selectedCount: Int? = null,
        selectionData: SelectionData<Path>? = null,
        selectedLimit: Int = SEND_MAX_LIMIT
    ): Boolean {
        val count = selectedCount ?: getSelectedCount(bundle, selectionData)
        if (isSelectedCountOverLimit(count, selectedLimit)) {
            showOverLimitToast()
            return true
        }
        return false
    }

    fun showLimitToast(selectedCount: Int, selectedLimit: Int = SEND_MAX_LIMIT): Boolean {
        if (isSelectedCountOverLimit(selectedCount, selectedLimit)) {
            showOverLimitToast()
            return true
        }
        return false
    }

    fun isSelectedCountOverLimit(selectedCount: Int, selectedLimit: Int = SEND_MAX_LIMIT): Boolean {
        GLog.d(TAG, "selectedCountOverLimit. count=$selectedCount, limit=$selectedLimit")
        return (selectedCount > selectedLimit)
    }

    private fun getSelectedCount(bundle: Bundle? = null, selectionData: SelectionData<Path>? = null): Int {
        val session = selectionData ?: bundle?.getInt(KEY_SELECTION_DATA_ID)?.let(PathSelectionManager::getSelectionData)
        return session?.getSelectedItemCount() ?: 0
    }

    private fun showOverLimitToast() {
        AppScope.launch(Dispatchers.UI) {
            ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.base_send_limit)
        }
    }

    private fun showSysShareDialog(shareIntent: Intent, context: Context) {
        try {
            context.startActivity(Intent.createChooser(shareIntent, context.resources.getString(com.oplus.gallery.basebiz.R.string.base_share)))
        } catch (e: ActivityNotFoundException) {
            GLog.e(TAG, "showSysShareDialog, handleMsg oShare:", e)
            ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.base_oshare_state_transit_failed)
        }
    }

    private fun Path?.getMimeType(): String {
        return if (TypeFilterUtils.isImage(this)) {
            MimeTypeUtils.MIME_TYPE_IMAGE_ANY
        } else if (TypeFilterUtils.isVideo(this)) {
            MimeTypeUtils.MIME_TYPE_VIDEO_ANY
        } else {
            MimeTypeUtils.MIME_TYPE_ANY
        }
    }

    fun Path.getMediaUri(): Uri? {
        return if (TypeFilterUtils.isImage(this)) {
            MediaStoreUriHelper.getUri(this.suffix, MediaStore.VOLUME_EXTERNAL_PRIMARY,
                MEDIA_TYPE_IMAGE
            )
        } else if (TypeFilterUtils.isVideo(this)) {
            MediaStoreUriHelper.getUri(this.suffix, MediaStore.VOLUME_EXTERNAL_PRIMARY,
                MEDIA_TYPE_VIDEO
            )
        } else null
    }

    object PathToUriConvert : IConvert<Path, Pair<Uri?, String>> {
        override fun convert(original: Path?): Pair<Uri?, String> {
            return original?.getMediaUri() to original.getMimeType()
        }
    }

    object PathsToUrisConvert : IConvert<Collection<Path>, Pair<Collection<Uri>?, String>> {
        override fun convert(original: Collection<Path>?): Pair<Collection<Uri>?, String> {
            var mimeType: String? = null
            val uris = original?.mapNotNull { path ->
                path.getMediaUri()?.also {
                    mimeType = chooseMimeType(mimeType, path.getMimeType())
                }
            }
            return uris to (mimeType ?: MimeTypeUtils.MIME_TYPE_ANY)
        }

        private fun chooseMimeType(original: String?, target: String): String {
            return when (original) {
                null -> target
                target -> original
                else -> MimeTypeUtils.MIME_TYPE_ANY
            }
        }
    }
}