/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SafeBoxDataHelper.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/09/22
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/09/22    1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.model.data.safebox

import android.database.Cursor
import com.oplus.gallery.business_lib.seniorpicked.SeniorMediaDBOperation
import com.oplus.gallery.business_lib.model.data.cshot.CShotDataHelper
import com.oplus.gallery.business_lib.model.data.fastcapture.FastCaptureUtils
import com.oplus.gallery.business_lib.model.data.label.LabelDataHelper
import com.oplus.gallery.business_lib.model.data.person.utils.FaceDataHelper
import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.standard_lib.app.multiapp.MultiAppUtils.convertToSystemPath
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.standard_lib.scheduler.BatchProcess
import com.oplus.gallery.foundation.util.debug.GLog

object SafeBoxDataHelper {

    private const val TAG = "SafeBoxDataHelper"
    private const val BATCH_COUNT_GET_MEDIA_ID = 50
    private const val BATCH_COUNT_DELETE_RELATIVE_DATA = 200

    /**
     * Values of encrypt db column "media_type".
     */
    private const val VALUE_VIDEOS_POSITION = 1
    private const val VALUE_PHOTOS_POSITION = 2

    class SafeBoxTaskInfo {
        val filePathList = ArrayList<String>()
        var imageTypes = IntArray(0)
        var allFilterCount = 0

        val filePathTypeMap = HashMap<String, Int>()
        val galleryIds = ArrayList<Int>()
    }

    fun setUpSafeBoxTaskInfo(galleryIds: ArrayList<Int>, safeBoxTaskInfo: SafeBoxTaskInfo) {
        safeBoxTaskInfo.apply {
            getFilePathsFromIds(galleryIds, this)
            getMediaType(this)
        }
    }

    /**
     * 把被设为私密的文件的数据库记录从 local_media 表里删除
     */
    fun deleteDBRecordInLocalMedia(galleryIds: ArrayList<Int>) {
        BatchProcess.doArgsQueryMaxBatch(galleryIds) { batchIds ->
            val deleteCount = DeleteReq.Builder()
                .setDaoType(IDao.DaoType.GALLERY)
                .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setWhere(DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns._ID, batchIds.size))
                .setWhereArgs(DatabaseUtils.getWhereArgs(batchIds))
                .build()
                .exec()
            GLog.d(TAG, "deleteDBRecordInLocalMedia, delete count = $deleteCount")
            emptyList()
        }
        if (galleryIds.isNotEmpty()) {
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.LOCAL_MEDIA, IDao.DaoType.GALLERY)
        }
    }

    /**
     * 把被设为私密的文件的数据库记录从相关的表里删除
     */
    fun deleteDBRecordInRelativeTable(filePathList: ArrayList<String>) {
        val filePaths = ArrayList<String>().apply { addAll(filePathList) }
        BatchProcess.doBatch(filePaths, BATCH_COUNT_DELETE_RELATIVE_DATA) { batchFilePaths ->
            LabelDataHelper.deleteLabelImageByMediaIdList(batchFilePaths)
            FaceDataHelper.deleteFaceImageByMediaIdList(batchFilePaths)
            SeniorMediaDBOperation.deleteSeniorMediaByFilePathList(batchFilePaths)
            emptyList()
        }
    }

    private fun getFilePathsFromIds(galleryIds: ArrayList<Int>, safeBoxTaskInfo: SafeBoxTaskInfo) {
        // step1:收集 ids 里的 cshotId，过滤 连拍temp文件 的 cshotId
        val cshotIds = ArrayList<Long>()
        safeBoxTaskInfo.allFilterCount = CShotDataHelper.getCShotIdsFilterTemp(galleryIds, cshotIds)

        // step2:把 cshotIds 里的 cshotId 对应的 id 都查出来，加入到 ids里
        getMediaIdsFromCShotIds(galleryIds, cshotIds)
        safeBoxTaskInfo.galleryIds.addAll(galleryIds)

        // step3:把 ids 里的 filePath 都查出来
        getFilePathFromMediaIds(safeBoxTaskInfo)
    }

    private fun getMediaIdsFromCShotIds(galleryIds: ArrayList<Int>, cshotIds: ArrayList<Long>) {
        if (cshotIds.isNullOrEmpty()) {
            return
        }
        try {
            BatchProcess.doArgsQueryMaxBatch(cshotIds) { batchCShotIds ->
                QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(arrayOf(GalleryStore.GalleryColumns.LocalColumns._ID))
                    .setWhere(DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns.CSHOT_ID, batchCShotIds.size))
                    .setWhereArgs(DatabaseUtils.getWhereArgs(batchCShotIds))
                    .setConvert(CursorConvert())
                    .build()
                    .exec()
                    ?.use { cursor ->
                        if (cursor.count > 0) {
                            GLog.d(TAG, "getMediaIdsFromCShotIds, cursor.count: ${cursor.count}")
                            while (cursor.moveToNext()) {
                                val galleyIdIndex = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns._ID)
                                val id = cursor.getInt(galleyIdIndex)
                                if (!galleryIds.contains(id)) {
                                    galleryIds.add(id)
                                }
                            }
                        }
                    }
                emptyList()
            }
        } catch (e: Exception) {
            GLog.e(TAG, "getMediaIdsFromCShotIds, e = ", e)
        }
        GLog.d(TAG, "getMediaIdsFromCShotIds, after add media id from cshotId, ids size = ${galleryIds.size}")
    }

    private fun getFilePathFromMediaIds(safeBoxTaskInfo: SafeBoxTaskInfo) {
        BatchProcess.doBatch(safeBoxTaskInfo.galleryIds, BATCH_COUNT_GET_MEDIA_ID) { batchIds ->
            runCatching {
                getBatchCursorWihtGalleryIds(batchIds)?.use { cursor ->
                    getBatchFilePathsFromCursor(cursor, safeBoxTaskInfo)
                }
            }.onFailure {
                GLog.w(TAG, "getBatchFilePathFromMediaIds, thr = $it")
            }
            emptyList()
        }
        GLog.d(TAG, "getFilePathFromMediaIds, allFilterCount = $safeBoxTaskInfo.allFilterCount")
    }

    private fun getBatchCursorWihtGalleryIds(items: List<Int>): Cursor? {
        val projection = arrayOf(
            GalleryStore.GalleryColumns.LocalColumns.DATA,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE,
            GalleryStore.GalleryColumns.LocalColumns.WIDTH,
            GalleryStore.GalleryColumns.LocalColumns.HEIGHT,
            GalleryStore.GalleryColumns.LocalColumns.CSHOT_ID
        )
        return QueryReq.Builder<Cursor>()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(projection)
            .setWhere(DatabaseUtils.getWhereQueryIn(GalleryStore.GalleryColumns.LocalColumns._ID, items.size))
            .setWhereArgs(DatabaseUtils.getWhereArgs(items))
            .setConvert(CursorConvert())
            .build()
            .exec()
    }

    private fun getBatchFilePathsFromCursor(cursor: Cursor, safeBoxTaskInfo: SafeBoxTaskInfo) {
        if (cursor.count > 0) {
            val indexFilePath = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DATA)
            val indexType = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE)
            val indexWidth = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.WIDTH)
            val indexHeight = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.HEIGHT)
            val indexCshotId = cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.CSHOT_ID)
            var filePath: String? = null
            var width = CShotDataHelper.WIDTH
            var height = CShotDataHelper.HEIGHT
            var cshotId = CShotDataHelper.DEFAULT_CSHOT_ID
            while (cursor.moveToNext()) {
                filePath = convertToSystemPath(cursor.getString(indexFilePath))
                width = cursor.getInt(indexWidth)
                height = cursor.getInt(indexHeight)
                cshotId = cursor.getLong(indexCshotId)
                if (FastCaptureUtils.isFastCaptureTemporaryFile(filePath, cshotId, width, height)) {
                    safeBoxTaskInfo.allFilterCount++
                    continue
                }
                filePath?.let {
                    safeBoxTaskInfo.filePathList.add(it)
                    safeBoxTaskInfo.filePathTypeMap[it] = cursor.getInt(indexType)
                }
            }
        }
    }

    private fun getMediaType(safeBoxTaskInfo: SafeBoxTaskInfo) {
        safeBoxTaskInfo.apply {
            val size = filePathList.size
            imageTypes = IntArray(size)
            for (i in 0 until size) {
                imageTypes[i] = getMediaType(filePathTypeMap, filePathList[i])
            }
        }
    }

    private fun getMediaType(filePathTypeMap: HashMap<String, Int>, path: String): Int {
        return if (filePathTypeMap[path] == GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO) {
            VALUE_VIDEOS_POSITION
        } else {
            VALUE_PHOTOS_POSITION
        }
    }
}