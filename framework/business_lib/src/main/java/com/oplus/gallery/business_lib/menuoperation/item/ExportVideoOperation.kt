/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ExportVideoOperation.kt
 ** Description:菜单操作：另存为视频
 **
 **
 ** Version: 1.0
 ** Date: 2024/02/27
 ** Author: W9070689@Apps.Gallery3D
 ** TAG: ExportVideoOperation
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** W9070689@Apps.Gallery3D   	    2024/02/27	 1.0          build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item


import android.content.ContentProviderOperation
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Rect
import android.graphics.RectF
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.provider.MediaStore
import android.view.KeyEvent
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import com.meicam.sdk.NvsStreamingContext
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.helper.StorageTipsHelper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask.Companion.autoCancelWhenOnDestroy
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.builder.FileProcessTaskBuilder
import com.oplus.gallery.business_lib.menuoperation.ExportVideoAction
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.helper.LoadingProgressDialogHelper
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.ExportVideoEngineFactory
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.ExportVideoEngineType
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.ExportVideoOptions
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.IExportVideoCallback
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.toLocalVideo
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeUtils
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper.getLocalMediaItem
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.getFullThumbnailKey
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.data.DeleteFileRequest
import com.oplus.gallery.foundation.fileaccess.data.FileResponse
import com.oplus.gallery.foundation.fileaccess.data.NewFileRequest
import com.oplus.gallery.foundation.fileaccess.data.RenameToFileRequest
import com.oplus.gallery.foundation.fileaccess.helper.ContentValuesHelper
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_FAILED_PARAM_NO_CONTEXT
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_FAILED_PARAM_NO_DATA
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_VIDEO_MP4
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.foundation.util.systemcore.WakeLockUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.frameextractor_lib.IMediaMetadataRetriever
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory.createResourceKey
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.olive_decoder.livephoto.MicroVideo
import com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_MODE_INCR
import com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_TYPE_COPY_FILE
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_270
import com.oplus.gallery.standard_lib.app.AppConstants.Degree.DEGREE_90
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.codec.player.effect.EffectUtil
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditor.data.VideoSpec
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.FileInputStream
import java.lang.ref.WeakReference
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.roundToInt
import kotlin.system.measureTimeMillis
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 菜单操作：导出实况照片中的视频
 */
open class ExportVideoOperation(
    isRunOnMain: Boolean,
    paramMap: Map<String, Any?>,
    onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)?
) : CopyFileOperation(isRunOnMain, paramMap, onCompleted), MeicamEngineLimiter.LimitAble {
    private lateinit var itemPathList: List<Path>
    private lateinit var currentFragmentRef: WeakReference<Fragment>
    private lateinit var albumPath: String
    private var exportFileItemPath: Path? = null
    private var exportFileSetPath: Path? = null
    private var totalFileSize: Long = 0L

    /**表示有多少个文件无法导出*/
    private var storageExportLimitCnt: Int = 0

    /**需要释放的空间大小*/
    private var storageExportLimitSize: Long = 0L

    /**已导出的文件uri*/
    private var exportedUriList: CopyOnWriteArrayList<Uri> = CopyOnWriteArrayList()

    /**要导出的文件总数量*/
    private var filesCount: Int = 0

    /**是否取消*/
    private var isCancel: Boolean = false

    /**存储每个文件的进度*/
    private var fileProgress: MutableList<Int> = mutableListOf()
    private val storageDialog: StorageTipsHelper by lazy { StorageTipsHelper() }

    /**裁剪进度条*/
    private var loadingDialog: LoadingProgressDialogHelper? = null

    /**olive微视频*/
    private var microVideo: MicroVideo? = null

    /**olive解码后的video参数*/
    private var videoInfo: VideoInfo? = null

    private var nvsContext: NvsStreamingContext? = null

    override fun onCheckAndPrepare(): Boolean {
        val paths = paramMap[ExportVideoAction.KEY_PATH_LIST] as? List<Path>
        this.itemPathList = checkParam(paths, failedDescription = "path list is null") ?: return false

        val fragment = paramMap[ExportVideoAction.KEY_FRAGMENT_REF] as? WeakReference<Fragment>
        this.currentFragmentRef = checkParam(fragment, VALUE_FAILED_PARAM_NO_CONTEXT, "fragment is null") ?: return false

        val albumPathParam = paramMap[ExportVideoAction.KEY_ALBUM_SET_PATH] as? String
        this.albumPath = checkParam(albumPathParam, failedDescription = "albumPath is null") ?: return false

        return true
    }

    private fun <T> checkParam(
        param: T? = null,
        result: String = VALUE_FAILED_PARAM_NO_DATA,
        failedDescription: String,
    ): T? {
        if (param == null) {
            GLog.e(TAG, LogFlag.DF) { "checkParam: $failedDescription" }
            setResult(MenuAction.RESULT_ERROR_NO_DATA, trackResult = TrackResult(result, failedDescription))
        }
        return param
    }

    private val mActivity get() = currentFragmentRef.get()?.activity as? AppCompatActivity
    private val appContext get() = ContextGetter.context

    override fun onRun() {
        MeicamEngineLimiter.getInstance().register(this)
        checkParam(mActivity, VALUE_FAILED_PARAM_NO_CONTEXT, "onRun, activity = null")?.let { activity ->
            val finishListener = object : FinishListener {
                override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                    if (success) {
                        exportTo(activity)
                    } else {
                        GLog.w(TAG, LogFlag.DF) { "downloadOrigAndExportVideo, download fail , errCode is $errCode, errMsg is $errMsg" }
                        setResult(MenuAction.RESULT_FAILED, skipTrackResult = true)
                    }
                }
            }

            FileProcessTaskBuilder(activity, itemPathList, finishListener, FileProcessScene.DOWNLOAD_ORIGIN_SCENE_OLIVE)
                .addTaskType(FileProcessTaskBuilder.TASK_TYPE_DOWNLOAD_ORIG)
                .setCancelListener { finishListener.onFinished(false) }
                .build()?.let {
                    it.execute()
                    refLifecycle?.get()?.let { lifecycle -> it.autoCancelWhenOnDestroy(lifecycle) }
                }
        }
    }

    override fun notifyCloudSync() {
        ApiDmManager.getCloudSyncDM().triggerCloudSync(SYNC_MODE_INCR, SYNC_TYPE_COPY_FILE)
    }

    override fun limitNow() {
        requestCancel()
    }

    override fun isActive(): Boolean {
        return mActivity?.lifecycle?.currentState?.isAtLeast(Lifecycle.State.RESUMED) ?: false
    }

    private suspend fun exportVideoFile(activity: AppCompatActivity, callback: OnExportVideoCallback): ResultEntry {
        mActivity?.let { context -> createLoadingAndShowDialog(context, com.oplus.gallery.basebiz.R.string.base_exporting) }
        val exportedUri = exportVideoToPendingFile(activity, callback)
        if (exportedUri.isNullOrEmpty()) {
            return ResultEntry(resultCode = ResultEntry.ALL_FILE_FAILED, succeedCount = 0)
        }
        unlockPendingFile(exportedUri)
        preLoadData(exportedUri)
        return ResultEntry(resultCode = ResultEntry.SUCCESS, succeedCount = exportedUri.size)
    }

    private suspend fun exportVideoToPendingFile(activity: AppCompatActivity, callback: OnExportVideoCallback): List<Uri>? {
        itemPathList.forEachIndexed { index, itemPath ->
            if (isCancel) {
                deleteExportedFiles()
                GLog.d(TAG, LogFlag.DF) { "exportVideoFile, delete exported files when cancel dialog." }
                return null
            }
            getLocalMediaItem(itemPath)?.let { mediaItem ->
                WakeLockUtil.acquireWakeLock(activity, TAG)
                val start = System.currentTimeMillis()
                runCatching {
                    val filePath = mediaItem.filePath
                    OLiveDecode.create(filePath).run {
                        microVideo = decode()?.microVideo
                        microVideo?.let { initVideoInfo(it) }
                        /**需要先执行decode，才能执行exportVideo，否则会失败*/
                        val fType: String = microVideo?.mime.let {
                            if (it == null) {
                                GLog.w(TAG, LogFlag.DL) { "exportVideoFile, mime is null, use default:video/mp4" }
                                MimeTypeUtils.getSuffixByMimeType(MIME_TYPE_VIDEO_MP4)
                            } else {
                                MimeTypeUtils.getSuffixByMimeType(it)
                            }
                        }
                        GLog.d(TAG, LogFlag.DF) { "exportVideoFile: video's type=$fType, length=${getLengthFromVideoStream(this)}" }
                        val pendingFileInfo = createPendingFile(activity, filePath, fType) ?: run {
                            GLog.e(TAG, LogFlag.DL, "exportVideoFile, createPendingFile fail, skip ${PathMask.mask(filePath)}")
                            return@forEachIndexed
                        }
                        val tmpFile = File(pendingFileInfo.filePath)
                        val videoCodecType = mediaItem.toLocalVideo()?.codecType
                        val result = exportVideo(mediaItem.contentUri, filePath, tmpFile, mediaItem.dateTakenInMs, videoCodecType) {
                            callback.onProgressChange(it, index)
                        }
                        if (result) {
                            exportedUriList.add(pendingFileInfo.uri)
                        }
                    }
                }.onSuccess {
                    val cost = System.currentTimeMillis() - start
                    GLog.d(TAG, LogFlag.DF) { "exportVideoFile: cost=$cost ms." }
                    WakeLockUtil.releaseWakeLock()
                }.onFailure {
                    GLog.e(TAG, LogFlag.DF) { "exportVideoFile, $it" }
                    WakeLockUtil.releaseWakeLock()
                }
            } ?: run {
                GLog.e(TAG, LogFlag.DF) { "exportVideoFile, No right MediaItem." }
            }
        }
        return exportedUriList
    }

    /**
     * 从VideoStream中获取length
     */
    private fun getLengthFromVideoStream(oLiveDecode: OLiveDecode): Int {
        val stream = oLiveDecode.getVideoStream()
        val length = stream?.available() ?: -1
        stream?.close()
        return length
    }

    private fun unlockPendingFile(exportedUri: List<Uri>): Boolean {
        if (isCancel) return false
        val operations = ArrayList<ContentProviderOperation>()
        exportedUri.forEach {
            val updateValues = ContentValues().apply {
                put(GalleryStore.GalleryColumns.LocalColumns.IS_PENDING, ContentValuesHelper.IS_PENDING_FALSE)
            }
            operations.add(ContentProviderOperation.newUpdate(it).withValues(updateValues).build())
        }
        runCatching {
            appContext.contentResolver.applyBatch(MediaStore.AUTHORITY, operations)
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "unlockFile, fail: $it" }
            return false
        }
        return true
    }

    private fun preLoadData(exportedUri: List<Uri>) {
        if (isCancel) return
        // 从大图执行导出时需要预加载数据，回到大图就直接跳转到目标视频了
        if (paramMap[ExportVideoAction.KEY_IS_FROM_PHOTO_PAGE] as Boolean) {
            exportedUri.forEach {
                // 跳转大图需要传参
                exportFileItemPath = DataManager.findPathByUri(it)
                exportFileSetPath = exportFileItemPath?.let { path -> DataManager.getDefaultSetOf(path) }
                loadThumbnail(exportFileItemPath)
            }
        }
    }

    private fun createPendingFile(context: Context, srcFilePath: String, mimeTypeSuffix: String): PendingFileInfo? {
        val newFileRequest = NewFileRequest.Builder()
            .setFile(getTargetFilepath(srcFilePath, mimeTypeSuffix))
            .setImage(false)
            .builder()
        FileAccessManager.getInstance().newCreateFile(context, newFileRequest)?.uri?.let {
            MediaStoreUriHelper.getFilePathByUri(it)?.let { pendingPath ->
                return PendingFileInfo(it, pendingPath)
            }
        }
        return null
    }

    private data class PendingFileInfo(
        val uri: Uri,
        val filePath: String
    )


    private fun deleteExportedFiles() {
        if (exportedUriList.isNotEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "deleteExportedFiles: size = ${exportedUriList.size}" }
            val operations = java.util.ArrayList<ContentProviderOperation>()
            exportedUriList.forEach { uri ->
                operations.add(ContentProviderOperation.newDelete(uri).build())
            }
            runCatching {
                appContext.contentResolver.applyBatch(MediaStore.AUTHORITY, operations)
            }.onSuccess {
                exportedUriList.clear()
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "deleteExportedFiles, fail: $it" }
            }
        }
    }

    /**
     * 视频参数初始化
     */
    private fun initVideoInfo(microVideo: MicroVideo) {
        microVideo?.let {
            val offset = it.offset
            val length = it.length
            val videoStartUs = it.videoStartUs ?: 0L
            val videoEndUs = it.videoEndUs ?: 0L
            videoInfo = VideoInfo(offset, length, videoStartUs, videoEndUs)
        }
    }

    private fun handleProgressData(progress: Int, index: Int) {
        if (isCancel) return
        if (filesCount == 0) return
        fileProgress[index] = progress
        val targetProgress = (fileProgress.sum() / filesCount).coerceIn(MIN_PROGRESS, MAX_PROGRESS)
        loadingDialog?.updateDialogProgress(targetProgress)
    }

    private fun exportTo(activity: AppCompatActivity) {
        if (isSpaceEnough(activity).not()) {
            GLog.e(TAG, LogFlag.DF) { "exportTo, not enough space." }
            setResult(MenuAction.RESULT_FAILED, skipTrackResult = true)
            return
        }
        AppScope.launch(Dispatchers.UI + CoroutineName(TAG)) {
            val callback = object : OnExportVideoCallback {
                override fun onProgressChange(progress: Int, index: Int) {
                    handleProgressData(progress, index)
                }
            }
            val resultEntry: ResultEntry = withContext(Dispatchers.IO + CoroutineName(TAG)) {
                initExportVideoInfo()
                exportVideoFile(activity, callback)
            }
            handleExportVideoResult(resultEntry)
        }
    }

    private fun isSpaceEnough(activity: AppCompatActivity): Boolean {
        val filePath = (DataManager.peekMediaObject(itemPathList.first()) as? MediaItem)?.filePath
        val available = StorageLimitHelper.getStorageAvailableByPath(filePath)
        itemPathList.forEach { itemPath ->
            totalFileSize += File((DataManager.peekMediaObject(itemPath) as? MediaItem)?.filePath).length()
            if (available < totalFileSize) {
                storageExportLimitCnt++
                storageExportLimitSize = totalFileSize - available
            }
        }
        if (storageExportLimitCnt > 0) {
            AppScope.launch(Dispatchers.UI + CoroutineName("isSpaceEnough")) {
                storageDialog.show(activity, storageExportLimitCnt, storageExportLimitSize, StorageLimitHelper.Scene.EXPORT_VIDEO)
            }
            return false
        }
        return true
    }

    private fun handleExportVideoResult(resultEntry: ResultEntry) {
        nvsContext?.let {
            MeicamContextAliveCounter.tryCloseContext(TAG)
            nvsContext = null
        }
        if (itemPathList.size == 1) {
            loadingDialog?.dismissDialog(delayDismiss = true)
        } else {
            loadingDialog?.dismissDialog()
        }
        if (isCancel) return
        var resultCode = MenuAction.RESULT_SUCCESS
        when {
            resultEntry.succeedCount == 0 -> {
                if (filesCount == 1) {
                    // 单选失败，提示 “文件已损坏，无法另存为视频”
                    ToastUtil.showShortToast(BasebizR.string.base_export_video_failed)
                } else {
                    // 多选全部导出失败，则提示“保存视频失败”
                    ToastUtil.showShortToast(BasebizR.string.base_saved_video_failed_toast)
                }
                resultCode = MenuAction.RESULT_FAILED
            }
            // 部分失败，则只提示“xx 个视频保存失败”
            resultEntry.succeedCount < filesCount -> {
                ToastUtil.showShortToast(
                    mActivity?.resources?.getQuantityString(
                        BasebizR.plurals.base_saved_video_part_failed_toast,
                        filesCount - resultEntry.succeedCount,
                        filesCount - resultEntry.succeedCount
                    )
                )
                resultCode = MenuAction.RESULT_FAILED
            }
            // 全部成功，则提示"已保存 xx 个视频"
            resultEntry.succeedCount >= filesCount -> {
                if ((paramMap[ExportVideoAction.KEY_IS_FROM_PHOTO_PAGE] as Boolean).not()) {
                    ToastUtil.showShortToast(
                        mActivity?.resources?.getQuantityString(
                            BasebizR.plurals.base_saved_video_toast,
                            resultEntry.succeedCount,
                            resultEntry.succeedCount
                        )
                    )
                }
            }
        }
        doOnEnd(resultCode)
    }

    private fun initExportVideoInfo() {
        filesCount = itemPathList.count()
        fileProgress = MutableList(filesCount) { 0 }
    }

    private suspend fun exportVideo(
        uri: Uri,
        srcFilePath: String,
        tempFile: File,
        videoCreateTime: Long,
        videoCodecType: String?,
        onProgressChange: (Int) -> Unit
    ): Boolean {
        val finalVideoInfo = videoInfo ?: let {
            GLog.e(TAG, LogFlag.DF) { "exportVideoPartClip, video info is null, return." }
            return false
        }
        val specification = getVideoSpecification(srcFilePath) ?: return false
        ExportVideoEngineFactory.create(ExportVideoEngineType.MEICAM).apply {
            if (initEngine(appContext, uri, finalVideoInfo.offset, finalVideoInfo.length, specification).not()) {
                return false
            }

            // 添加视频片段，区分视频长度是否剪辑过
            if (isNeedPartClip()) {
                appendVideoClip(uri, finalVideoInfo.offset, finalVideoInfo.length, finalVideoInfo.videoStartUs, finalVideoInfo.videoEndUs)
            } else {
                appendVideoClip(uri, finalVideoInfo.offset, finalVideoInfo.length)
            }

            // 视频裁切，返回 null或是1.0时， 则无需裁切
            getVideoEisFactor(srcFilePath)?.takeIf { it != DEFAULT_CROP_FRACTION }?.let { factor ->
                //获取视频是否有画框水印
                var videoDisplayRect: RectF? = null
                appContext.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
                    ability.newWatermarkFileOperator(uri)?.use {
                        videoDisplayRect = it.getAiMasterWatermarkExtInfo()?.videoDisplayRect
                    }
                }

                /**
                 * 根据视频是否有画框水印使用不同的裁剪方案去模糊
                 * 1.带画框水印情况：是视频内容模糊，因此需要对视频内容进行裁剪而非裁剪画框
                 * 2.不带画框水印情况：裁剪视频四周模糊边缘
                 */
                videoDisplayRect?.takeIf { it.isEmpty.not() }?.let {
                    //视频有画框水印时，只裁剪视频内容的模糊边缘
                    cropVideoContent(it, factor)
                } ?: calculateVideoRect(specification, videoCodecType, factor)?.let { displayRect ->
                    //视频没有画框水印时，裁剪整个视频模糊边缘
                    cropVideo(displayRect)
                }
            }

            val completableDeferred = CompletableDeferred<Boolean>()
            var isExportSuccess = false
            registerExportVideoCallback(object : IExportVideoCallback {
                override fun onProgress(position: Int) {
                    onProgressChange.invoke(position)
                }

                override fun onComplete(isSuccess: Boolean) {
                    GLog.d(TAG, LogFlag.DF) { "exportVideoPartClip, onComplete success:$isSuccess" }
                    if (isSuccess) {
                        MediaStoreScannerHelper.scanFileByMediaStoreSingle(appContext, tempFile.absolutePath, true)
                    }
                    isExportSuccess = isSuccess
                    close()
                    completableDeferred.complete(isSuccess)
                }

                override fun onFailed(isSuccess: Boolean) {
                    GLog.e(TAG, LogFlag.DF) { "onFailed isFailed:$isSuccess" }
                    isExportSuccess = isSuccess
                    close()
                    completableDeferred.complete(isSuccess)
                }
            })

            if (saveVideo(tempFile.absolutePath, ExportVideoOptions(videoCreateTime, specification.height))) {
                completableDeferred.await()
            } else {
                close()
            }
            return isExportSuccess
        }
    }

    /**
     * 获取视频内容无模糊的区域，用于后面裁切视频的无模糊区域
     *
     * @param specification 视频解析信息
     * @param videoCodecType 视频编码类型
     * @param eisFactor 视频内容放大系数
     */
    private fun calculateVideoRect(specification: VideoSpec, videoCodecType: String?, eisFactor: Float): Rect? {
        if (videoInfo == null) {
            GLog.e(TAG, LogFlag.DL) { "[calculateCropRect] videoInfo is null" }
            return null
        }

        // 拆解宽高裁切比，给的比例会大于 1，所以需要进行转换
        val cropFraction = if (eisFactor > 0) {
            1 / eisFactor
        } else {
            DEFAULT_CROP_FRACTION
        }

        // 计算裁切后的宽高
        val requestWidth = (specification.width  * cropFraction).roundToInt()
        val requestHeight = (specification.height * cropFraction).roundToInt()
        // 因编辑 MeicamVideoCutRotate.cutVideo 中在使用相同 api 的时候对宽高有额外限制，所以此处做对齐。
        val useLimit = VideoTypeUtils.isDolbyVideo(videoCodecType).not()
        val size = VideoRatio.correctSize(requestWidth, requestHeight, useLimit)

        // 计算裁切后的位置
        val left = (specification.width - size.width) / NUMBER_2
        val top = (specification.height - size.height) / NUMBER_2
        return Rect(left, top, left + size.width, top + size.height)
    }

    /**
     * 获取视频文件中的模糊放大系数，即对视频内容放大指定系数播放不会显示模糊边缘
     *
     * @param videoPath 文件路径
     */
    private fun getVideoEisFactor(videoPath: String): Float? {
        if (videoInfo == null) {
            GLog.e(TAG, LogFlag.DL) { "[calculateCropRect] videoInfo is null" }
            return null
        }

        val eisInfo = videoInfo?.offset?.let { EffectUtil.getEisInfo(filePath = videoPath, offset = it) }
        val eisFactorList = eisInfo?.eisCropFactor ?: let {
            // 获取裁切比失败
            GLog.e(TAG, LogFlag.DL, "[calculateCropRect] Failed to get video crop scale")
            return null
        }
        if (eisFactorList.size < NUMBER_2) {
            // 裁切比不合法
            GLog.e(TAG, LogFlag.DL, "[calculateCropRect] cropScaleList size is invalid, cropScaleList: $eisFactorList")
            return null
        }

        GLog.d(TAG, LogFlag.DL, "[calculateCropRect] cropScaleList: $eisFactorList")

        return eisFactorList[0]
    }

    private fun createLoadingAndShowDialog(context: Context, titleId: Int) {
        if (loadingDialog == null) {
            loadingDialog = LoadingProgressDialogHelper(context).apply {
                setTitle(titleId)
                setCancelListener {
                    requestCancel()
                }
                setOnKeyListener { dialog, keyCode, event ->
                    if ((keyCode == KeyEvent.KEYCODE_BACK) && (event.action == KeyEvent.ACTION_UP)) {
                        return@setOnKeyListener true
                    }
                    return@setOnKeyListener false
                }
            }
        }
        loadingDialog?.showDialog(TRIM_VIDEO_SHOW_TIMEOUT)
    }

    /**
     * 是否需要裁剪视频时长
     *
     * 视频经过时长裁剪前视频参数videoStartUs，videoEndUs默认是0
     * 编辑完成后videoStartUs,videoEndUs不可能同时为0
     * 且videoStartUs<videoEndUS合法
     */
    private fun isNeedPartClip(): Boolean {
        return videoInfo?.let { it.videoStartUs < it.videoEndUs } ?: false
    }

    private fun getVideoSpecification(filePath: String?): VideoSpec? {
        runCatching {
            FileInputStream(filePath).use { inputStream ->
                IMediaMetadataRetriever.create(IMediaMetadataRetriever.MediaMetadataRetrieverVendor.TBL).use { mediaMetadataRetriever ->
                    videoInfo?.let {
                        mediaMetadataRetriever.setDataSource(inputStream.fd, it.offset, it.length)
                    }
                    val width = mediaMetadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toInt() ?: 0
                    val height = mediaMetadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toInt() ?: 0
                    val rotation = mediaMetadataRetriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)?.toInt() ?: 0
                    if (rotation == DEGREE_90 || rotation == DEGREE_270) {
                        return VideoSpec(height, width, ENGINE_VIDEO_FPS)
                    } else {
                        return VideoSpec(width, height, ENGINE_VIDEO_FPS)
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DF) { "<getVideoSpecification> retrieve video size got error: ${it.message}" }
        }
        return null
    }

    private fun moveFile(context: Context, srcFile: File, path: String, targetName: String): FileResponse? {
        val destFile = File(File(path).parent, targetName).run {
            /** 目标文件，如果不存在就可使用，如果存在，就按序号顺序创建文件*/
            takeIf { exists().not() } ?: FilePathUtils.createAscFile(this)
        }
        val renameRequest: RenameToFileRequest =
            RenameToFileRequest.Builder()
                .setSrcFile(srcFile)
                .setTargetFile(destFile)
                .setCoverFile(true)
                .setIsDeleteSrcFile(true)
                // 因为是视频文件
                .setImage(false).builder()
        return FileAccessManager.getInstance().renameTo(context, renameRequest)
    }

    private fun getTargetFilepath(srcFilePath: String, mimeType: String): String {
        return File(File(srcFilePath).parent, FilePathUtils.getTitle(srcFilePath) + TextUtil.DOT + mimeType).run {
            /** 目标文件，如果不存在就可使用，如果存在，就按序号顺序创建文件*/
            takeIf { exists().not() } ?: FilePathUtils.createAscFile(this)
        }.absolutePath
    }

    override fun operationCancel() {
        this.isCancel = true
    }

    private fun doOnEnd(resultCode: String = MenuAction.RESULT_SUCCESS) {
        if ((resultCode == MenuAction.RESULT_SUCCESS)) {
            if ((paramMap[ExportVideoAction.KEY_IS_FROM_PHOTO_PAGE] as Boolean) && (exportFileItemPath != null) && (exportFileSetPath != null)) {
                setResult(
                    resultCode = resultCode, resultMap = mapOf(
                        IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH to exportFileItemPath.toString(),
                        IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH to exportFileSetPath.toString()
                    )
                )
            } else {
                setResult(resultCode = resultCode)
            }
            notifyCloudSync()
        } else {
            setResult(resultCode = resultCode)
        }
        MeicamEngineLimiter.getInstance().unregister(this)
    }

    private fun loadThumbnail(itemPath: Path?) {
        GTrace.trace(LOAD_THUMBNAIL_TRACE) {
            val loadThumbnailCost = measureTimeMillis {
                if (itemPath == null) {
                    GLog.w(TAG, LogFlag.DF) { "[loadThumbnail] can't save bitmap because item path is null" }
                    return@measureTimeMillis
                }
                getLocalMediaItem(itemPath)?.let { mediaItem ->
                    val resourceKey = createResourceKey(mediaItem) ?: return@let
                    val getOptions = ResourceGetOptions()
                    getOptions.inThumbnailType = getFullThumbnailKey()
                    getOptions.inCacheOperation = CacheOperation.ReadWriteAllCache
                    mActivity?.getAppAbility<IResourcingAbility>()?.use { resourcingAbility ->
                        val result = resourcingAbility.requestBitmap(resourceKey, getOptions, null, null)
                        result?.let {
                            val savedBitmap = result.result as? Bitmap
                            // 加载出来的图片是为了提前缓存缩图，此处并不会使用，直接回收
                            savedBitmap?.recycle()
                        }
                    }
                } ?: let {
                    GLog.w(TAG, LogFlag.DF) { "[loadThumbnail] save thumbnail error because mediaItem is invalid" }
                }
            }
            GLog.d(TAG, LogFlag.DF) { "[loadThumbnail] cost time $loadThumbnailCost ms" }
        }
    }

    /**
     * 删除已导出的视频文件
     *
     * @param videoFile 视频文件
     */
    private fun doDeleteVideoFile(videoFile: File): Boolean {
        if (!videoFile.exists()) {
            GLog.d(TAG, LogFlag.DF) { "[doDeleteFile] video file not exist" }
            return false
        }
        val deleteFileRequest = DeleteFileRequest.Builder().setFile(videoFile).builder()
        return FileAccessManager.getInstance().delete(ContextGetter.context, deleteFileRequest)
    }

    private fun requestCancel() {
        operationCancel()
        doOnEnd(MenuAction.RESULT_CANCEL)
        loadingDialog?.dismissDialog()
        AppScope.launch(Dispatchers.IO + CoroutineName("deleteVideo")) {
            deleteExportedFiles()
        }
    }

    /**
     * 图片参数
     */
    private data class VideoInfo(val offset: Long = 0L, val length: Long = 0L, val videoStartUs: Long = 0L, val videoEndUs: Long = 0L)

    /**
     * 导出视频回调接口
     */
    interface OnExportVideoCallback {

        /**
         *导出过程进度回调
         * @param progress 计算的进度
         * @param index 文件索引，表示当前导出的是第几个文件
         */
        fun onProgressChange(progress: Int, index: Int)
    }

    companion object {
        const val TAG = "ExportVideoOperation"
        const val ENGINE_VIDEO_FPS = 30F
        const val ANIM_DURATION = 1000L

        /**
         * 延迟显示时间300ms
         */
        const val TRIM_VIDEO_SHOW_TIMEOUT = 300L
        private const val LOAD_THUMBNAIL_TRACE = TAG + "_loadThumbnail"
        private const val MIN_PROGRESS = 0
        private const val MAX_PROGRESS = 100

        private const val DEFAULT_CROP_FRACTION = 1.0f
    }
}