/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumFragment.kt
 ** Description: A standard fragment which will be used to display an normal album with
 **              many pictures.
 **
 **
 ** Version: 1.0
 ** Date: 2020/08/29
 ** Author: wang<PERSON><EMAIL>
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wang<PERSON><EMAIL>		2020/08/29		1.0		OPLUS_FEATURE_TAB
 ** <EMAIL>		2020/10/23		1.1		OPLUS_FEATURE_TAB
 *********************************************************************************/
package com.oplus.gallery.business_lib.ui.fragment

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.TextUtils
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import android.view.animation.Animation
import android.widget.AdapterView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_DRAGGING
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE
import com.coui.appcompat.checkbox.COUICheckBox.SELECT_ALL
import com.coui.appcompat.checkbox.COUICheckBox.SELECT_NONE
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.oplus.gallery.addon.app.OplusJankWrapper
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_RECYCLERVIEW_SCROLL
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_RECYCLERVIEW_SCROLL
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_ALBUM_TITLE
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_MEDIA_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_MODEL_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_CURRENT_PAGE
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_FROM_FAVORITES_ALBUM
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_INDEX_HINT
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_OPEN_ANIMATION_RECT
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_POSITION_CONTROLLER
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_SHARED_ID
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_STATUSBAR_TINT
import com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_THUMB_SIZE_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.TrackConstant.ALBUM_NAVIGATION_TRACK_PAGE_ID
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_FROM
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_MODEL_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_PHOTO_PREVIEW_SELECT_TYPE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_SELECTION_DATA_ID
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_ENABLE_DRAG_OUT_DEFAULT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_ENABLE_DRAG_OUT_DISABLE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_ENABLE_ROTATE_DEFAULT
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_ENABLE_ROTATE_DISABLE
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_PHOTO_PREVIEW_SELECT_MULTI
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_PHOTO_PREVIEW_SELECT_SINGLE
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_SELECTED
import com.oplus.gallery.basebiz.sidepane.ISidePaneAnimation
import com.oplus.gallery.basebiz.sidepane.PageLayoutDisabledAdjust
import com.oplus.gallery.basebiz.sidepane.SidePaneAnimationConfig
import com.oplus.gallery.basebiz.sidepane.SidePaneDependencyAnimation
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.SidePaneWithAlbumAnimation
import com.oplus.gallery.basebiz.sidepane.adjustControlIconColor
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isVisible
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.transition.widget.PhotoClipBoundTransitionView
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.findFullScreenContainerId
import com.oplus.gallery.basebiz.uikit.animation.ToolbarFadeInFadeOutAnimator
import com.oplus.gallery.basebiz.uikit.controller.TintTranslucentElement
import com.oplus.gallery.basebiz.uikit.controller.filter.FilterPanelTintExpandElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintBackElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintFilterElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintMenuButtonDrawableElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintMenuButtonTextElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintStatusElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintSubtitleElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintTitleElement
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.isSupportRename
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.basebiz.widget.AlbumRecycleView
import com.oplus.gallery.basebiz.widget.filter.PersonalFilterPanelDataConfig
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.controller.PersonalFilterViewWrapper
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisToolsHelper
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.helper.ContentDescriptionSetter
import com.oplus.gallery.business_lib.helper.ShareHelper
import com.oplus.gallery.business_lib.menuoperation.AlbumCreateMemoryAction
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.ShareAction
import com.oplus.gallery.business_lib.menuoperation.helper.AlbumHideOnTimeLinePageHelper
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.set.MediaAlbum.INVALID_ALBUM_ID
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.local.set.AllPictureAlbum
import com.oplus.gallery.business_lib.model.data.local.set.FavoritesAlbum
import com.oplus.gallery.business_lib.model.data.memories.utils.OptimalConfigInterface.DEFAULT_VALUE_MEMORIES_PIC_COUNT_MIN
import com.oplus.gallery.business_lib.onetouchshare.OneTouchShareController
import com.oplus.gallery.business_lib.selectionpage.SelectionTrackHelper
import com.oplus.gallery.business_lib.synergy.SynergyTouchInterceptor
import com.oplus.gallery.business_lib.timeline.Level
import com.oplus.gallery.business_lib.timeline.TimelineConfig.getTimelineConfigColumn
import com.oplus.gallery.business_lib.track.FloatingWindowTrackHelper
import com.oplus.gallery.business_lib.transition.IPhotoPagePreTransitionController
import com.oplus.gallery.business_lib.transition.PhotoPagePreTransitionController
import com.oplus.gallery.business_lib.transition.PreTransitionStartInfo
import com.oplus.gallery.business_lib.transition.TransitionHelper
import com.oplus.gallery.business_lib.ui.IPicturePositionController
import com.oplus.gallery.business_lib.ui.view.AlbumViewDataBinding
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.ui.view.SlotFooterViewDataBinding
import com.oplus.gallery.business_lib.util.FastScrollerMessageFormater
import com.oplus.gallery.business_lib.util.draganddrop.DragData
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.util.draganddrop.DragOptions
import com.oplus.gallery.business_lib.util.draganddrop.DropAlbumManager
import com.oplus.gallery.business_lib.util.draganddrop.animation.DragDropAnimationHelper
import com.oplus.gallery.business_lib.util.draganddrop.listener.OnDragEventListener
import com.oplus.gallery.business_lib.viewmodel.AlbumViewModel
import com.oplus.gallery.business_lib.viewmodel.AlbumViewModel.ThumbReuseType
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.helper.Constants.AlbumOrderType
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.TYPE_ALBUMS_ACTION
import com.oplus.gallery.foundation.tracing.constant.CollageTrackConstant
import com.oplus.gallery.foundation.tracing.constant.FloatingWindowTrackConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.TYPE_LAUNCH_EXIT_POPUP
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.ALBUM_MAIN_SET_PAGE
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.tracing.helper.LaunchExitPopupTrackHelper
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.toolbar.DirectionAnimationElement
import com.oplus.gallery.foundation.ui.systembar.toolbar.IProgressChanger
import com.oplus.gallery.foundation.ui.systembar.toolbar.TintDrawableAnimationElement
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.onWidthChanged
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.PerformanceLevel.LOWER
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.cpuLevel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.DisplayUtils
import com.oplus.gallery.foundation.util.ext.mutateDrawable
import com.oplus.gallery.foundation.util.ext.safeUse
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.caching.ICachingAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.KEY_PATH_STR
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_CARD_CASE_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_CSHOT_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_DOLBY_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_DOUBLE_EXPOSURE_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_ENHANCE_TEXT_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_FAST_VIDEO_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_FAVORITES_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_GIF_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_LOCAL_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_LOG_VIDEO_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_OLIVE_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_PANORAMA_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_PORTRAIT_BLUR_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_RAW_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_RECYCLE_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_SLOW_MOTION_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_VIDEO_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_WIDGET_DISPLAY_LIST_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.MemoriesModelGetter.Companion.TYPE_MEMORIES_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.MtpModelGetter.Companion.TYPE_MTP_ALBUM
import com.oplus.gallery.framework.abilities.data.IDataAbility
import com.oplus.gallery.framework.abilities.data.model.AlbumModel
import com.oplus.gallery.framework.abilities.data.model.BaseModel.Companion.COUNT_UNINITIALIZED
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_MAP_PAGE
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_PAGE_UNKNOWN
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_SIDE_PANE_PAGE
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.KEY_FROM_PAGE
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.TrackScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.baselist.adapter.config.DEFAULT_FIRST_ITEM_TYPE
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.BaseListAdapter
import com.oplus.gallery.standard_lib.baselist.view.BaseListItemAnimator
import com.oplus.gallery.standard_lib.baselist.view.ItemClickListener.Companion.CLICK_AREA_ITEM_VIEW
import com.oplus.gallery.standard_lib.baselist.view.OnAnimationListener
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.popupwindow.MenuListPopupItem
import com.oplus.gallery.standard_lib.ui.toolbar.actionview.MenuButton
import com.oplus.gallery.standard_lib.ui.toolbar.actionview.MenuCheckButton
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.io.IOUtils
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import kotlin.math.abs
import com.oplus.gallery.basebiz.R as BasebizR
import com.oplus.gallery.foundation.ui.R as LibUIR
import com.support.appcompat.R as couiR

@Suppress("LargeClass")
abstract class BaseAlbumFragment : BaseListFragment<MediaItem, ItemViewData>(),
    IPicturePositionController, OnDragEventListener,
    IPhotoPagePreTransitionController by PhotoPagePreTransitionController() {

    /**
     * 是否保持系统栏可见。
     */
    private var keepSystemBarVisible: Boolean = true
        set(value) {
            if (value == field) return
            field = value
            setupSystemBar(value)
        }

    /**
     * 点击更多菜单弹出的窗口
     */
    private var subMenuPopup: COUIPopupListWindow? = null

    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_ALBUM_FRAGMENT
    override val recyclerViewId: Int = BasebizR.id.recycler_view
    override val recyclerViewPaddingBottom by lazy { resources.getDimensionPixelSize(BasebizR.dimen.base_album_recyclerview_padding_bottom) }
    protected var albumViewData: AlbumViewData? = null
    protected var modelBundle: Bundle = bundleOf()
    protected lateinit var bottomMenuHelper: BottomMenuHelper
    protected val trackCallerEntry: TrackCallerEntry by lazy {
        TrackCallerEntry(
            page = trackPage,
            path = baseListViewModel?.trackPath,
            trackAlbumPageInfo = this.trackAlbumPageInfo,
            currentAlbumName = albumViewData?.title,
            albumsActionCurrentPage = getUserActionCurPage(TYPE_ALBUMS_ACTION)
        )
    }

    /**
     * 图集中照片视频的数量
     */
    private var albumCountViewData: AlbumViewData? = null

    protected var slotFooterViewDataBinding: SlotFooterViewDataBinding? = null
        private set
    protected var isSupportCreateMemory: Boolean = false
    protected var isCountEnoughCreateMemory: Boolean = false
    protected var needScrollToBottom: Boolean = true

    private var selectAllCheckButton: MenuCheckButton? = null
    private var cancelButton: MenuButton? = null

    /**
     * 标记已经点击了全选按钮,但不一定是全选图片,如点击全选后,手动反选了几张照片
     */
    private var markClickedSelectAllButton = false

    private var albumOrderBinding: AlbumOrderBinding? = null
    private var albumCoverBinding: AlbumCoverBinding? = null

    // 适配多个页面拉起多个大图页情况
    // 1、用hashcode作为当前fragment的key传给大图页
    // 2、大图页通过该key获取对应的positionController从而得到对应的缩图位置
    private val positionControllerKey = hashCode().toString()

    private var isShowAddToAlbumIcon: Boolean = false
    private var disableAccessibilityViews = mutableListOf<View>()

    /**
     * 记录从哪个页面跳转至这个页面：来源
     */
    private var fromPage: Int = FROM_PAGE_UNKNOWN

    /**
     * 当前RV的ClipToPadding属性
     */
    private var tempOriginClipToPadding: Boolean? = null

    // 当前是否从OverScroll进入沉浸式
    private var currentOverScrollImmersive: Boolean = false
    // 过滑的y值，处于底部继续上滑时>0，处于顶部继续下滑时<0
    private var overScrollingY: Int = 0

    private var placeHolderView: WeakReference<View>? = null

    private var isNeedShowCreateMenuFlag = true

    private var dragDropAnimationHelper: DragDropAnimationHelper? = null

    // 当前页面是否收到drop事件
    private var receiveDrop: Boolean = false

    // 由于onResume-refreshToolbar()-setSubTitle,所以需要在onResume中设置默认的subTitle,固使用此标记，标记subTitle已初始化
    private var isToolbarSubTitleInited = false

    // 首次加载totalCount:adapter默认使用totalCount,vm的visibleRange不会更新一直是-1,导致viewdata不会构建,最终列表灰图
    private var isFirstLoadTotalCount = true

    private var dropManager: DropAlbumManager? = null

    private var albumName: String? = null

    /**
     * 当前是否显示顶部半透明背景
     */
    private var currentShowTopTranslucent: Boolean = false

    private val fastScrollerMessageFormater: FastScrollerMessageFormater by lazy {
        FastScrollerMessageFormater()
    }

    /**
     * 是否属于多选状态，
     * 属于多选状态，大图预览的右上角menu为 checkBox；
     * 属于单选状态，大图预览的右上角menu为 “√”
     */
    protected open val isSelectedMulti: Boolean = true

    /**
     * 是否支持PC互联，图集页面默认为true
     */
    open var supportSynergy = true
    private val synergyItemTouchListener = object : RecyclerView.OnItemTouchListener {
        val touchInterceptor = SynergyTouchInterceptor { x, y ->
            var position = recyclerView?.findChildViewUnder(x, y)?.let {
                recyclerView?.getChildAdapterPosition(it)
            } ?: INVALID_INDEX
            position -= recyclerAdapter.headerCount
            val path = baseListViewModel?.getCacheData(position)?.path
            GLog.d(TAG, "synergyTouch. pos:$position,selectionMode=${isInSelectionMode()},path=$path")

            val result = mutableSetOf<Path>()
            if (isInSelectionMode()) {
                selectItem(position)
                baseListViewModel?.let { result.addAll(it.getSelectedItems()) }
            } else {
                path?.let { result.add(it) }
            }
            result
        }

        override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
            return touchInterceptor.onInterceptTouchEvent(e).apply {
                if (this) slidingSelectProcessor?.cancelLongClickEvent()
            }
        }

        override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
        }
        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}
    }

    /**
     * 侧边栏展开收起动画联动基础列表动画管理
     */
    protected var sidePaneAnim: SidePaneWithAlbumAnimation? = null
    private var sidePaneWithFooterAnim: ISidePaneAnimation? = null

    private val onFooterViewCreated: ((view: View) -> Unit) = { footerView ->
        if (showSlotFooter()) {
            val footerAnim = recyclerView?.let { SidePaneDependencyAnimation(footerView, it) }
            sidePaneAnim?.removeDependencyAnimation(sidePaneWithFooterAnim)
            sidePaneAnim?.addDependencyAnimation(footerAnim)
            sidePaneWithFooterAnim = footerAnim
        } else {
            GLog.w(TAG, "onFooterViewCreated: not support footer")
        }
    }

    protected var firstTotalCount = COUNT_UNINITIALIZED
        private set
    private val recyclerViewPaddingTop by lazy { resources.getDimensionPixelSize(LibUIR.dimen.common_toolbar_category_top_padding) }

    /**是否点击过个性化筛选：用于过滤首次打开图集进场动画过程中多次layout,
     * 由于数据分批加载,多次onReloadFinish-refreshEmptyView,触发多次layout
     * 每帧都耗时约1ms,低端机更加明显明显
     */
    private var isClickedFilter: Boolean = false

    private val contentDescriptionSetter: ContentDescriptionSetter = ContentDescriptionSetter()

    /**
     * pre transition的容器控件
     */
    private var preTransitionPageContainer: View? = null

    /**
     * 大图过渡动画控件；图片
     */
    private var preTransitionView: PhotoClipBoundTransitionView? = null

    /**
     * 当前点击查看图片的缩图
     */
    private var focusThumbnailDrawable: Drawable? = null

    /**
     * 当前点击查看图片的mediaItem
     */
    private var mediaItem: MediaItem? = null

    /**
     * 获取入场动画完成时，大图内容的显示区域
     */
    private val onGetPreTransitionConstraintRect = {
        val rect = Rect().apply {
            val thumbnail = focusThumbnailDrawable ?: return@apply
            val activity = activity ?: return@apply
            val mediaItem = mediaItem ?: return@apply
            set(getPreTransitionConstraintRect(activity, thumbnail, mediaItem))
        }
        rect
    }

    /**
     * 转场动画管理类实例
     */
    private val transitionManager by lazy {
        val container = preTransitionPageContainer ?: return@lazy null
        val preTransition = preTransitionView ?: return@lazy null

        val isSupportedPreTransition = TransitionHelper.isSupportedPreTransition()
        if (isSupportedPreTransition.not()) {
            return@lazy null
        }

        createPhotoPageTransitionManager(this, preTransition, container, onGetPreTransitionConstraintRect)
    }

    override fun isSupportEmptyPage(): Boolean = true

    override fun getEmptyPageTitleRes(): Int {
        return if (isPersonalFiltered()) {
            BasebizR.string.main_filter_empty_result
        } else {
            super.getEmptyPageTitleRes()
        }
    }

    override fun getEmptyPageIconAnimRes(): Int {
        return if (isPersonalFiltered()) {
            BasebizR.raw.base_empty_view_search_result
        } else {
            super.getEmptyPageIconAnimRes()
        }
    }

    /**
     * 是否支持一碰分享(图集详情页，进入选择模式，存在分享按钮的页面，都应该支持，应该设置为true)
     */
    protected open val supportOneTouchShare: Boolean = false

    /**
     * 一碰分享能力
     */
    private var oneTouchShareController: OneTouchShareController? = null

    /**
     * 是否支持个性化筛选
     */
    protected open val isSupportedPersonalFilter: Boolean = false
    override val needExitWhenEmpty: Boolean
        get() = super.needExitWhenEmpty && isPersonalFiltered().not()

    // 顶部半透明背景View
    private var topTranslucentView: View? = null
    private var addToAlbumMenuItem: MenuItem? = null
    private var createMemoryMenuItem: MenuItem? = null
    private var searchMenuItem: MenuItem? = null

    private var filterMenuItem: MenuItem? = null
        set(value) {
            if (field == value) return
            field = value
            field?.isVisible = isSupportedPersonalFilter
        }
    protected val personalFilterViewWrapper: PersonalFilterViewWrapper by lazy {
        object : PersonalFilterViewWrapper({ baseListViewModel as? AlbumViewModel }) {
            override fun onUpdateFilterMenuItem() {
                refreshCreateMemoryIcon()
                checkToolbarTint(recyclerView?.computeVerticalScrollOffset() ?: 0, true, isNeedAnimation = false)
            }
        }
    }

    /**
     * 顶部提示的容器
     */
    protected var headerTipsContainer: ViewGroup? = null

    /**
     * 将浏览寻顺序缓存，talkback播报图片的位置依赖此值
     * 照片浏览顺序每次都从sp中读取,而且是在onBindViewHolder中调用,会比较耗时。
     */
    private var isPositiveOrder: Boolean? = null

    /**
     * 缓存ability,避免频繁获取和创建,此过程有锁,会耗时,尤其是列表刷新表现更加明显
     */
    private val abilityCache by lazy { mutableMapOf<Int, AutoCloseable?>() }

    /**
     * 不显示在照片页菜单项
     */
    private var albumHideOnTimeLinePageMenuItem: MenuItem? = null

    protected fun setViewData(viewData: AlbumViewData, modelBundle: Bundle = bundleOf()) {
        albumViewData = viewData
        albumViewData?.apply {
            isSupportCreateMemory = viewData.supportedAbilities?.getBoolean(AlbumCreateMemoryAction.SUPPORT_CREATE_MEMORY) ?: false
            isShowAddToAlbumIcon = viewData.supportedAbilities?.getBoolean(AlbumViewData.SUPPORT_IS_SELF_ALBUM) ?: false
        }
        this.modelBundle = modelBundle
    }

    override fun getListAdapter(): BaseListAdapter<ItemViewData> {
        return super.getListAdapter().apply {
            if (isDefaultTotalCountEnabled()) {
                // 创建时给默认值，避免从空白页面->灰图
                totalCount = albumViewData?.totalCount ?: 0
            }
        }
    }

    override fun initLayoutDetail(context: Context): LayoutDetail {
        return GridLayoutDetail.HorizontalGapsBuilder().apply {
            val itemGap = context.resources.getDimension(BasebizR.dimen.base_album_fragment_item_view_horizontal_gap)
            // 默认边距为0，当后续需求需要更改边距时，可修改此处
            val edge = 0f
            parentWidth = getContentWidth()
            edgeWidth = edge.toInt()
            gapWidth = itemGap.toInt()
            spanCount = getTimelineConfigColumn(Level.DAY, parentWidth)
        }.build().apply {
            footerCount = if (showSlotFooter()) 1 else 0
            itemDecorationGapPx.top = context.resources.getDimension(BasebizR.dimen.base_normal_album_fragment_item_padding_top)
        }
    }

    override fun getAdapterItemConfigs(): List<ItemConfig<ItemViewData>> {
        return mutableListOf<ItemConfig<ItemViewData>>().apply {
            context?.let { context ->
                add(
                    ItemConfig(
                        ItemViewData::class.java,
                        AlbumViewDataBinding(
                            context,
                            stylePool = baseListViewModel,
                            onGetTalkBackPosition = getTalkBackPosition(),
                            onGetAppAbility = getCacheAppAbility(),
                            contentDescriptionSetter = contentDescriptionSetter,
                            onGetXqipUniqueId = { baseListViewModel?.uniqueInstanceId },
                            isMaskVisibleGet = { isMaskVisible() },
                            checkboxAnimEnableGet = { checkboxAnimEnable() }
                        )
                    )
                )
            }
        }
    }

    private fun isMaskVisible(): Boolean = if (isRecyclerAdapterInitialized()) recyclerAdapter.isMaskVisible else false

    private fun checkboxAnimEnable(): Boolean = if (isRecyclerAdapterInitialized()) !recyclerAdapter.isAutoScrolling else false

    private fun getCacheAppAbility(): (type: Int) -> AutoCloseable? = { type ->
        abilityCache.computeIfAbsent(type) {
            when (type) {
                CACHE_ABILITY -> context?.getAppAbility<ICachingAbility>()
                RESOURCE_ABILITY -> context?.getAppAbility<IResourcingAbility>()
                else -> null
            }
        }
    }

    protected open fun getTalkBackPosition(): (position: Int) -> Int = {
        if (isPositiveOrder == null) {
            isPositiveOrder = baseListViewModel?.isPositiveOrderFromSetting()
        }
        if (isPositiveOrder == true) recyclerAdapter.totalCount - it else it + 1
    }

    override fun onCreateViewModel(): ListViewModel<MediaItem, ItemViewData> {
        return ViewModelProvider(this).get(AlbumViewModel::class.java).apply {
            isSupportedPersonalFilter = <EMAIL>
        }
    }

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaItem, ItemViewData>) {
        super.onSetUpViewModelStyle(viewModel)
        context?.let {
            val rectThumbStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_SIZE_TYPE, getThumbSizeType(layoutDetail.itemWidth))
                put(StyleData.KEY_THUMB_STROKE_WIDTH, resources.getDimension(LibUIR.dimen.common_round_drawable_frame_stroke_width))
                put(
                    StyleData.KEY_THUMB_STROKE_PAINT_COLOR,
                    resources.getColor(BasebizR.color.common_round_drawable_frame_stroke_color, null)
                )
                put(
                    StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR, DisplayUtils.getDarkColorWithNightMode(
                        it,
                        resources.getColor(BasebizR.color.standard_default_bg_color_for_transparent, null)
                    )
                )
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
            }
            viewModel.addStyle(StyleType.TYPE_RECT_THUMB_STYLE, rectThumbStyleData)
        }
    }

    override fun getThumbSizeType(itemWidth: Int): Int {
        return ThumbnailSizeUtils.getMicroThumbnailKey(itemWidth)
    }

    override fun needReleaseViewDataOnSizeChange(): Boolean {
        val oldRectThumbSizeType = baseListViewModel?.getStyle(StyleType.TYPE_RECT_THUMB_STYLE)?.get(StyleData.KEY_THUMB_SIZE_TYPE)
        val newRectThumbSizeType = getThumbSizeType(layoutDetail.itemWidth)
        return oldRectThumbSizeType?.equals(newRectThumbSizeType) != true
    }

    override fun getBottomBarMenuId(): Int {
        return when {
            baseListViewModel?.model?.getModelType() == TYPE_MTP_ALBUM -> BasebizR.menu.base_selection_mtp
            else -> {
                if (isNeedShowCreateMenuFlag) {
                    BasebizR.menu.base_selection_album_with_creation
                } else {
                    BasebizR.menu.base_selection_album_without_creation
                }
            }
        }
    }

    override fun getLayoutId(): Int {
        return BasebizR.layout.base_fragment_normal_album
    }

    override fun onItemClick(position: Int, viewData: ItemViewData?, clickType: Int, itemView: View?) {
        GLog.d(TAG, "onItemClick: viewData: $viewData, position:$position")
        if (DoubleClickUtils.isFastDoubleClick()) return
        viewData ?: return
        if (isInSelectionMode()) {
            //选择态下，点击footer或者header返回，不处理点击事件
            if (recyclerAdapter.isHeaderOrFooter(position)) return
            val isSelected = viewData.supportedAbilities.getBoolean(SUPPORT_SELECTED)
            when {
                (((CLICK_AREA_ITEM_VIEW == clickType) && isSelectedMulti) || isSelectedMulti.not()) -> {
                    //多选点击item非checkbox区域，进大图预览; 单选点击item任何区域，直接进大图预览
                    startPhotoPage(viewData, true)
                    SelectionTrackHelper.trackClickSelection(true)
                }

                isSelected -> {
                    unselectItem(viewData.position)
                    SelectionTrackHelper.trackClickSelection(false)
                }

                else -> {
                    selectItem(viewData.position)
                    SelectionTrackHelper.trackClickSelection(false)
                }
            }
        } else {
            // 跳转大图
            startPhotoPage(viewData)
        }
    }

    private fun startPhotoPage(viewData: ItemViewData, isPreview: Boolean = false) {
        GLog.d(TAG, "startPhotoPage: viewData: $viewData, albumViewData: $albumViewData")

        // 共享数据
        var sharedId: String? = null
        context?.getAppAbility<IDataAbility>()?.safeUse {
            val snapshot = baseListViewModel?.getSnapshot(viewData.position)?.also { items ->
                if (items.data.isNotEmpty()) {
                    mediaItem = items.data[0]
                }
            }
            val shareSession = it.share?.createSession()
            shareSession?.uploadPayload(snapshot)
            sharedId = shareSession?.id
        }

        viewData.let {
            // modelBundle中可能含有获取Model的关键信息，需要传递给大图
            Bundle(modelBundle).apply {
                // 点击跳进大图时，需要传正确的path，人物图集的对应有face path
                if (isPreview) {
                    val selectTypeValue = if (isSelectedMulti) {
                        VALUE_PHOTO_PREVIEW_SELECT_MULTI
                    } else {
                        VALUE_PHOTO_PREVIEW_SELECT_SINGLE
                    }
                    putInt(IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE, IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE_PREVIEW)
                    putInt(KEY_PHOTO_PREVIEW_SELECT_TYPE, selectTypeValue)
                    putBoolean(IntentConstant.ViewGalleryConstant.KEY_ENABLE_DRAG_OUT, VALUE_ENABLE_DRAG_OUT_DISABLE)
                    putBoolean(IntentConstant.ViewGalleryConstant.KEY_ENABLE_ROTATE, VALUE_ENABLE_ROTATE_DISABLE)
                    baseListViewModel?.let { viewModel -> putInt(KEY_SELECTION_DATA_ID, viewModel.selectionDataId) }
                } else {
                    putInt(IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE, IntentConstant.ViewGalleryConstant.KEY_PHOTO_TYPE_DEFAULT)
                    putBoolean(IntentConstant.ViewGalleryConstant.KEY_ENABLE_DRAG_OUT, VALUE_ENABLE_DRAG_OUT_DEFAULT)
                    putBoolean(IntentConstant.ViewGalleryConstant.KEY_ENABLE_ROTATE, VALUE_ENABLE_ROTATE_DEFAULT)
                }

                transitionManager?.updateFocusSlot(it.position)

                val path = viewData.id
                putString(KEY_MEDIA_ITEM_PATH, path)
                putString(KEY_MEDIA_SET_PATH, albumViewData?.id)
                putString(KEY_POSITION_CONTROLLER, positionControllerKey)
                putString(KEY_MEDIA_MODEL_TYPE, albumViewData?.modelType)
                putInt(KEY_INDEX_HINT, viewData.position)
                putString(KEY_SHARED_ID, sharedId)
                putBoolean(KEY_FROM_FAVORITES_ALBUM, albumViewData?.modelType.equals(TYPE_FAVORITES_ALBUM))
                putBoolean(IntentConstant.PicturePageConstant.KEY_ENTER_PHOTO_ANIMATE, true)
                putParcelable(KEY_OPEN_ANIMATION_RECT, recyclerAdapter.getItemRectByIndex(viewData.position))
                putString(KEY_CURRENT_PAGE, trackAlbumPageInfo)
                putString(KEY_MEDIA_FROM, ALBUM_MAIN_SET_PAGE)
                putBoolean(KEY_STATUSBAR_TINT, currentShouldToolbarTint())
                (baseListViewModel as? AlbumViewModel)?.modelPath?.let {
                    putString(KEY_MEDIA_SET_PATH, it)
                    putString(KEY_PATH_STR, it)
                }
                (baseListViewModel?.getStyle(StyleType.TYPE_RECT_THUMB_STYLE)?.get(StyleData.KEY_THUMB_SIZE_TYPE) as? Int)?.let {
                    // 不使用 getInt，如无此字段或非 Int，则 null
                    putInt(KEY_THUMB_SIZE_TYPE, it)
                }
                startPicture(this@apply, isPreview)
            }
        }
    }

    protected open fun getCurrentPageForTrack(): String = LaunchExitPopupConstant.Value
        .PHOTO_ENTER_CUR_PAGE_ALBUM_PAGE_VALUE

    override fun getUserActionCurPage(trackType: String?): String? = when (trackType) {
        TYPE_LAUNCH_EXIT_POPUP -> LaunchExitPopupConstant.Value.ALBUM_PAGE
        CollageTrackConstant.TYPE_COLLAGE -> {
            when ((baseListViewModel?.model as? AlbumModel)?.getPackedMediaSet()) {
                is FavoritesAlbum -> CollageTrackConstant.Value.CURRENT_PAGE_FAVORITE
                is AllPictureAlbum -> CollageTrackConstant.Value.CURRENT_PAGE_ALL_PICTURE
                else -> LaunchExitPopupConstant.Value.ALBUM_PAGE
            }
        }

        TYPE_ALBUMS_ACTION -> AlbumsActionTackConstant.Value.ALBUMS_ACTION_ALBUM_PAGE_VALUE
        else -> LaunchExitPopupConstant.Value.ALBUM_PAGE
    }

    /**
     * 子类的列表页有跳转到大图的页面，需实现该方法提供当前图片的坐标
     * @param index
     * @param ignoreInvisibility
     */
    override fun getItemRect(index: Int, ignoreInvisibility: Boolean): Rect {
        if (index != INVALID_INDEX) {
            return recyclerAdapter.getItemRectByIndex(index)
        }
        return Rect()
    }

    override fun getItemIndex(path: Path?): Int {
        return INVALID_INDEX
    }

    override fun onPictureChanged(index: Int) {
        transitionManager?.updateFocusSlot(index)
        if (index != INVALID_INDEX) {
            recyclerAdapter.scrollByItemIndex(index)
        }
    }

    override fun onPullDownStart(index: Int) {
        setPlaceHolder(index)
    }

    override fun onPullDownFinish(index: Int, complete: Boolean) {
        setPlaceHolder(INVALID_INDEX)
    }

    private fun setPlaceHolder(index: Int) {
        lifecycleScope.launch(Dispatchers.UI) {
            placeHolderView?.get()?.visibility = View.VISIBLE
            if (index != INVALID_INDEX) {
                recyclerAdapter.getItemByIndex(index)?.let {
                    it.visibility = View.INVISIBLE
                    placeHolderView = WeakReference(it)
                }
            }
        }
    }

    /**
     * 计算好bundle数据后，最终启动大图页面
     * 这里以fragment的形式启动大图，在某些情况下，可能需要以activity启动大图，重写这个方法即可
     */
    protected open fun startPicture(bundle: Bundle = Bundle(), isPreview: Boolean, requestCode: Int? = null) {
        GLog.d(TAG, "startPicture: bundle: $bundle, resultCode: $requestCode， positionControllerKey: $positionControllerKey")

        //如果支持将启动动画提前到点击缩略图，则在启动大图Fragment前启动入场动画
        startEnterTransitionIfNeeded(bundle)

        startByStack<BaseFragment>(
            resId = activity.findFullScreenContainerId(),
            postCard = PostCard(RouterConstants.RouterName.PICTURE_FRAGMENT),
            data = bundle
        )
    }

    /**
     * 如果支持将启动动画提前到点击缩略图，则在启动大图Fragment前启动入场动画
     * @param bundle 启动大图Fragment时传递的bundle信息，入场动画初始化时按需从bundle中获取所需要的数据
     */
    private fun startEnterTransitionIfNeeded(bundle: Bundle) {
        transitionManager?.let {
            val context = context ?: return
            val intent = activity?.intent ?: Intent()
            val thumbnail = getDrawableFromPath(
                context,
                Path.fromString(bundle.getString(KEY_MEDIA_ITEM_PATH))
            ) ?: return
            focusThumbnailDrawable = thumbnail

            startEnterTransition(
                context,
                PreTransitionStartInfo(intent, bundle, mediaItem, thumbnail),
                onGetPreTransitionConstraintRect,
                it
            )
        }
    }

    override fun onPause() {
        super.onPause()
        recyclerView?.removeOnItemTouchListener(synergyItemTouchListener)
        disableAccessibility((view as? ViewGroup), disableAccessibilityViews)
    }

    override fun onResume() {
        super.onResume()
        // 跳转其它页面返回后，保证状态栏和系统导航条可见
        setupSystemBar(keepSystemBarVisible)

        if (supportSynergy) {
            recyclerView?.addOnItemTouchListener(synergyItemTouchListener)
        }
        restoreAccessibility(disableAccessibilityViews)
        initToolbarSubTitle()

        /** 处于选择模式，启用一碰分享，如跳转预览大图后再返回的场景 */
        if (isInSelectionMode()) enableOneTouchShare()
    }

    override fun onStop() {
        super.onStop()
        subMenuPopup?.dismiss()
    }

    private fun initToolbarSubTitle() {
        if (showToolbarSubtitle() && !isToolbarSubTitleInited) {
            // 只初始化一次，避免侧边导航栏图集切图集时,副标题从无到有会闪动,因父类onResume()->refreshToolbar()->setSubTitle("")，所以在此覆盖设置
            toolbarSetter?.setSubtitle(getDefaultSubTitle())
            isToolbarSubTitleInited = true
        }
    }

    /**
     * 获取首次赋予toolbar默认子标题的内容
     * 避免侧边导航栏进入图集详情时，子标题从无到有导致标题跳动
     */
    private fun getDefaultSubTitle(): String {
        val count = albumViewData?.totalCount ?: 0
        if ((count == 0) && !needExitWhenEmpty) {
            // 图集空不退出时,是不需要显示subTitle的
            return EMPTY_STRING
        }
        return context?.resources?.getQuantityString(BasebizR.plurals.common_item_count, count, count) ?: EMPTY_STRING
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isPositiveOrder = null
    }

    override fun onDestroy() {
        super.onDestroy()
        transitionManager?.release()
        unregisterPositionController()
        getSafeBottomMenuHelper()?.release()
        personalFilterViewWrapper.removeListener()
        GTrace.trace("clearAbilityCache") {
            clearAbilityCache()
        }
        /** fragment被回收时，关闭一碰分享 ，避免泄漏*/
        disableOneTouchShare()
    }

    private fun clearAbilityCache() {
        val iterator = abilityCache.iterator()
        while (iterator.hasNext()) {
            IOUtils.closeQuietly(iterator.next().value)
            iterator.remove()
        }
    }

    @Suppress("ComplexCondition")
    override fun onBackPressed(): Boolean {
        if (!super.onBackPressed()) {
            return false
        }
        /**
         * 从侧边栏进入的，侧边栏可见时，页面内容为空时退出并回到 tabfragment 的图集页，此处需要拦截退出事件，否则会进入mainactivity中退到后台的逻辑，
         * 真正的退出逻辑已经在SidePanefragment中通过数据驱动实现，具体在onTotalCountChanged中。
         * 从图集页进入的，或者侧边栏不可见的时候走原有逻辑，退出fragment
         */
        return when {
            isRecyclerAdapterInitialized() &&
                isFromSidePanePage() &&
                sidePaneAccessor.isVisible() &&
                needExitWhenEmpty &&
                (recyclerAdapter.totalCount == NO_ITEMS) -> false
            personalFilterViewWrapper.isVisible() -> {
                personalFilterViewWrapper.dismiss()
                false
            }
            else -> true
        }
    }

    private fun registerPositionController() {
        ApiDmManager.getMainDM().registerPhotoPageAnimatorController(positionControllerKey, this@BaseAlbumFragment)
    }

    private fun unregisterPositionController() {
        ApiDmManager.getMainDM().unregisterPhotoPageAnimatorController(positionControllerKey)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isSupportImmersive = true
        needScrollToBottom = isPositiveOrder()
        registerPositionController()
        arguments?.also {
            it.getString(ALBUM_NAVIGATION_TRACK_PAGE_ID)?.let { pageId ->
                // 默认是系统图集或子Fragment所自由的图集值， 但也有可能是其他的传进来的值
                trackAlbumPageInfo = pageId
            }
            fromPage = it.getInt(KEY_FROM_PAGE, FROM_PAGE_UNKNOWN)
            setViewDataInOnCreate(it)
            it.getParcelable<AlbumViewData>(KEY_ALBUM_VIEW_DATA)?.also { viewData ->
                setViewData(viewData, it.getBundle(KEY_ALBUM_MODEL_BUNDLE) ?: Bundle())
            }
        }
        isNeedShowCreateMenuFlag = GifSynthesisToolsHelper.isNeedShowCreateMenu()
    }

    protected fun isNeedShowCreateMenuFlag(): Boolean {
        return isNeedShowCreateMenuFlag
    }

    protected open fun setViewDataInOnCreate(arguments: Bundle) {
        val mediaPathStr = arguments.getString(KEY_MEDIA_PATH)
        if (mediaPathStr.isNullOrEmpty()) {
            return
        }
        GLog.d(TAG, LogFlag.DL) { "setViewDataInOnCreate, mediaPath=$mediaPathStr" }
        val modelBundle = arguments.getBundle(KEY_ALBUM_MODEL_BUNDLE) ?: Bundle()
        setViewData(
            AlbumViewData(
                id = mediaPathStr,
                position = 0,
                modelType = arguments.getString(KEY_MODEL_TYPE, TYPE_LOCAL_ALBUM),
                isMediaAlbum = true,
                version = 0,
                totalCount = 0,
                title = arguments.getString(KEY_ALBUM_TITLE),
                supportedAbilities = Bundle().apply {
                    putBoolean(
                        AlbumViewData.SUPPORT_IS_SELF_ALBUM,
                        arguments.getBoolean(AlbumViewData.SUPPORT_IS_SELF_ALBUM, false)
                    )
                }
            ),
            modelBundle
        )
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        initPreTransitionView()
        canShowZoomInMode = isSelectedMulti
        baseListViewModel?.apply {
            albumViewData?.let { <EMAIL>(it, modelBundle) }
            refreshCallback = { isPositiveOrder ->
                if (isPositiveOrder) {
                    if (needScrollToBottom) {
                        recyclerView?.scrollToPosition(recyclerAdapter.itemCount - 1)
                    }
                } else {
                    recyclerView?.scrollToPosition(0)
                }
            }
        }

        bottomMenuHelper = BottomMenuHelper(activity as? BaseActivity, baseListViewModel)

        if (showSlotFooter()) {
            slotFooterViewDataBinding = SlotFooterViewDataBinding(view.context).also { footerBinding ->
                footerBinding.onViewCreated = onFooterViewCreated
                recyclerAdapter.addFooterView(footerBinding)
            }
        }

        internalAppbarLayout = view.findViewById(BasebizR.id.appbar_layout)
        fastScroller = view.findViewById(BasebizR.id.fastScroller)
        initFastScroller()

        topTranslucentView = view.findViewById(BasebizR.id.album_top_translucent_view)

        initImmersiveView()
        sidePaneAccessor?.takeIf { isSidePaneEnabled() }?.let { accessor ->
            sidePaneAnim = recyclerView?.let { SidePaneWithAlbumAnimation(this, it) }
            /*
              从侧边栏跳转至该页面,则该页面处于一级页面不需要返回键
              initToolbar->setupToolbarWithView会将置为true,导致会闪一下箭头
            */
            val isShowBackArrow = isNeedHomeAsUp()
            setDisplayHomeAsUpEnabled(isShowBackArrow)
            /*
             * 判定Toolbar没有向上一级返回键时候，左间距在大屏上不够24dp
            * 增加间距24dp
             */
            if (!isShowBackArrow) {
                toolbar?.titleMarginStart = resources.getDimensionPixelSize(R.dimen.appbar_title_margin_start)
            }
            contentView?.let {
                // 侧边栏动画过程中,需要禁掉非resume的fragment刷新
                PageLayoutDisabledAdjust(this, accessor).bindView(it)
            }
        }
        if (isSupportDrop()) {
            contentView?.let { contentView ->
                dropManager = DropAlbumManager(this)
                dropManager?.bindDropView(contentView, albumViewData)
            }
        }

        headerTipsContainer = internalAppbarLayout?.findViewById(LibUIR.id.header_content)

        // 解决 8502124 编辑-长按-自动滚动划选-卡顿
        recyclerView?.apply {
            setItemViewCacheSize(RV_CACHE_VIEW_SIZE)
            recycledViewPool.setMaxRecycledViews(DEFAULT_FIRST_ITEM_TYPE, RV_CACHE_POOL_SIZE)
            (itemAnimator as? BaseListItemAnimator)?.addAnimationListener(onAnimationListener)
        }
    }

    /**
     * 列表diff动画开始结束时的回调
     */
    private val onAnimationListener = object : OnAnimationListener {
        override fun onAnimationStart() {
            recyclerAdapter?.apply { if (totalCount == 0) setRVClipPadding() }
        }

        override fun onAnimationFinished() {
            restoreRVClipPadding()
        }
    }

    override fun onSlidingSelectEnd() {
        super.onSlidingSelectEnd()
        recyclerView?.setHasFixedSize(false)
    }

    override fun onSlidingSelectRangeChanged(start: Int, end: Int, lastEnd: Int, isTargetSelected: Boolean) {
        super.onSlidingSelectRangeChanged(start, end, lastEnd, isTargetSelected)
        // 8502124 进入自动滚动划选状态时,RV大小不会发生变化,可设置为固定大小,减少RV measure/layout耗时
        recyclerView?.setHasFixedSize(recyclerAdapter.isAutoScrolling)
    }

    override fun onFragmentTransitionEnd(animation: Animation, animId: Int) {
        super.onFragmentTransitionEnd(animation, animId)
        GLog.d(TAG, LogFlag.DL) { "onTransitionEnd: $clazzSimpleName" }
        //bug:8368860 低端机,需要等动画结束后,才注册数据观察者,避免机场动画过程中，刷新RV,导致进场卡顿
        super.registerLiveDataObserver()
    }

    override fun registerLiveDataObserver() {
        if (cpuLevel == LOWER) {
            // 低端机:进场动画结束之后才进行注册，否则动画过程中，数据加载刷新RV，会导致进场动画卡顿
            view?.post {
                // post 是因为进场动画在下一帧才开始 也就是下一帧isTransitionAnimationRunning才为true
                super.registerLiveDataObserver()
            }
        } else {
            super.registerLiveDataObserver()
        }
    }

    /**
     * bug:8368860
     * 需要等registerLiveDataObserver之后再注册,避免baseListViewModel?.activeInfoLiveData还未注册,其它先注册,导致数据刷新异常
     * 如 onReloadFinish?.observe先注册,若activeInfoLiveData还未注册,但数据已经加载，通知到onReloadFinish->refreshEmptyPageView,
     * 由于原先使用的是adapter.count=0判断，导致直接退出fragment
     */
    override fun onPostRegisterLiveDataObserver() {
        addSpecifiedCountObserve()
        (baseListViewModel as? AlbumViewModel)?.isSetCoverSuccess?.observe(this) { isSuccess ->
            ToastUtil.showShortToast(
                if (isSuccess) {
                    BasebizR.string.base_album_modify_cover_succeed
                } else {
                    BasebizR.string.base_album_modify_cover_failed
                }
            )
        }
        (baseListViewModel as? AlbumViewModel)?.onReloadFinish?.observe(this) { totalCount ->
            if (isClickedFilter && exitCurrentFragmentIfNeed(totalCount).not()) {
                refreshEmptyPageView(totalCount)
            }
        }
        (baseListViewModel as? AlbumViewModel)?.isSetOrderSuccess?.observe(this) {
            fastScroller?.scrollerWithMessageEnable = isFastScrollerWithMessageEnable()
        }
    }

    /**
     * bug:8368860
     * 是否延期注册数据观察者
     * 低端机:进场动画结束之后才进行注册，否则动画过程中，数据加载刷新RV，会导致图集进场动画卡顿
     */
    override fun isDelayRegisterObserver() =
        (cpuLevel == LOWER) &&
            isTransitionAnimationRunning &&
            ((albumViewData?.totalCount ?: 0) >= DELAY_REGISTER_OVER_COUNT)

    private fun initFastScroller() {
        fastScroller?.scrollerWithMessageEnable = isFastScrollerWithMessageEnable()
        fastScroller?.scrollerMessageObtain = this::obtainScrollerMessage
    }

    /**
     * 快滑条是否显示MessageView
     *
     * 图集排序为：按拍摄时间、修改事件排序时，快滑条显示MessageView
     */
    private fun isFastScrollerWithMessageEnable(): Boolean {
        val currentAlbumOrder = (baseListViewModel as? AlbumViewModel)?.getAlbumOrder()
        return listOf(
            AlbumOrderType.DATE_TAKEN_ASC,
            AlbumOrderType.DATE_TAKEN_DESC,
            AlbumOrderType.DATE_MODIFY_ASC,
            AlbumOrderType.DATE_MODIFY_DESC
        ).contains(currentAlbumOrder)
    }

    /**
     * 获取FastScroller 落点位置Item的日期
     *
     * @param currentX FastScroller X坐标
     * @param currentY FastScroller Y坐标
     *
     * @return 坐标落点位置Item的Date
     */
    private fun obtainScrollerMessage(currentX: Float, currentY: Float): String {
        /** 查找到坐标点所在位置的item在 adapter中的 position */
        val position = findVisibleItemUnderPosition(currentX, currentY)

        /** 根据 position 获取 data 并返回日期(根据排序类型取创建时间或修改时间) */
        val data = baseListViewModel?.getCacheData(position) ?: return EMPTY_STRING
        val albumOrder = (baseListViewModel as? AlbumViewModel)?.getAlbumOrder()

        return when (albumOrder) {
            AlbumOrderType.DATE_TAKEN_ASC, AlbumOrderType.DATE_TAKEN_DESC -> fastScrollerMessageFormater.format(data.dateTakenInMs)

            AlbumOrderType.DATE_MODIFY_ASC, AlbumOrderType.DATE_MODIFY_DESC ->
                fastScrollerMessageFormater.format(data.dateModifiedInSec.times(TimeUtils.TIME_1_SEC_IN_MS))

            else -> EMPTY_STRING
        }
    }


    /**
     * 根据坐标，查找到坐标点所在位置的item在 adapter中的 position
     *
     * @Note
     * 1. RecyclerView API 正常查找：目标坐标点落在item范围
     * 2. RecyclerView API 二次查找：目标坐标点落在item之间的gap区域，交互确定，此种情况下，取gap上方item
     * 3. RecyclerView API 两次查找均未找，即目标坐标点不在item范围也不在item之间的gap区域，那么目标坐标点就只能在空白区域，此时的场景就是坐标点落在了末行且
     * 末行未占满，此时直接返回 VisibleRange 最后一个即可，也可以使用 BaseListFragment.findNearbyChildViewUnder 进行查找，但是这里没必要进行NearBy查找，
     * 原因是：图集页末行未占满，那么距离滑块水平方向最近的肯定是VisibleRange最后一个，即便是RTL布局模式，也是如此。
     */
    private fun findVisibleItemUnderPosition(x: Float, y: Float): Int {
        /** 正常查找 */
        var position: Int? = recyclerView?.findChildViewUnder(x, y)?.let {
            recyclerView?.getChildLayoutPosition(it)
        }
        if ((position != null) && (position != RecyclerView.NO_POSITION)) return position

        /** 二次查找(GAP区域，取GAP上方item)*/
        val itemGapPx = abs(layoutDetail.itemDecorationGapPx.height())
        position = recyclerView?.findChildViewUnder(x, y - itemGapPx)?.let {
            recyclerView?.getChildLayoutPosition(it)
        }
        if ((position != null) && (position != RecyclerView.NO_POSITION)) return position

        /** 空白区域（末行未占满）*/
        val range = recyclerAdapter.visibleRange
        return if (range.isInvalid().not()) range.end else RecyclerView.NO_POSITION
    }

    private fun addSpecifiedCountObserve() {
        (baseListViewModel as? AlbumViewModel)?.specifiedCount?.observe(this) {
            val memoriesCount = it?.specifiedCount?.getInt(Constants.SpecifiedCount.KEY_CREATE_MEMORIES_COUNT) ?: 0
            if ((memoriesCount > DEFAULT_VALUE_MEMORIES_PIC_COUNT_MIN && isSupportCreateMemory) != isCountEnoughCreateMemory) {
                isCountEnoughCreateMemory = !isCountEnoughCreateMemory
                createMemoryMenuItem?.takeIf { shouldRefreshMenu() }?.let { refreshCreateMemoryIcon() }
            }
            albumCountViewData = it
            if (showSlotFooter()) {
                slotFooterViewDataBinding?.albumViewData = it
                if (showToolbarSubtitle()) {
                    toolbarSetter?.setSubtitle(getItemCountText())
                    slotFooterViewDataBinding?.shouldShowCount = false
                }
                if (recyclerView?.isComputingLayout?.not() == true) {
                    recyclerAdapter.notifyItemChanged(recyclerAdapter.itemCount - 1)
                }
            } else if (showToolbarSubtitle()) {
                toolbarSetter?.setSubtitle(getItemCountText())
            }
        }
    }

    private fun getItemCountText(): String? {
        val imageCount = albumCountViewData?.specifiedCount?.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT) ?: 0
        val videoCount = albumCountViewData?.specifiedCount?.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT) ?: 0
        return if ((videoCount <= 0) && (imageCount <= 0)) {
            TextUtil.EMPTY_STRING
        } else {
            context?.resources?.getQuantityString(
                BasebizR.plurals.common_item_count, (imageCount + videoCount), (imageCount + videoCount)
            )
        }
    }

    /**
     * 初始化Pre Transition动画相关View视图
     */
    private fun initPreTransitionView() {
        val activity = activity ?: return
        preTransitionPageContainer = activity.findViewById<View>(BasebizR.id.pre_transition_view_container)?.apply {
            preTransitionView = findViewById(BasebizR.id.pre_transition_view)
        }
    }

    private fun initImmersiveView() {
        if (isSupportImmersive.not()) return

        /*
         * 支持上滑全屏半透明才把背景设置为透明，新建图集选图页是不支持上滑全屏半透明的，不能设置背景透明
         * 此处 background 只能设置为透明颜色，不能设置为 null，否则会出现标题栏 icon 无点按效果
         */
        internalAppbarLayout?.background = requireContext().getDrawable(android.R.color.transparent)
        scrollPositionToImmersive = recyclerView?.paddingTop ?: 0
        (recyclerView as? AlbumRecycleView)?.apply {
            onOverScrollListener = object : AlbumRecycleView.OnOverScrollListener {
                override fun onOverScrolled(scrollX: Int, scrollY: Int, clampedX: Boolean, clampedY: Boolean) {
                    val isCurrentShouldToolbarTint = currentShouldToolbarTint()
                    overScrollingY = scrollY
                    val isTotalCountGreaterThanZero = (baseListViewModel?.totalSize ?: 0) > 0
                    if (isCurrentShouldToolbarTint.not() && isTotalCountGreaterThanZero && isHeaderVisible().not()) {
                        val currentShouldToolbarTint = scrollY > scrollPositionToImmersive
                        if (currentOverScrollImmersive != currentShouldToolbarTint) {
                            currentOverScrollImmersive = currentShouldToolbarTint
                            sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint() || currentOverScrollImmersive)
                            checkToolbarTint(scrollY, true)
                        }
                    }
                }
            }
        }

        recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                if (needScrollToBottom && (recyclerView.scrollState == SCROLL_STATE_DRAGGING) && (abs(dy) > 0)) {
                    needScrollToBottom = false
                }
                onScrollCheckToolbarTint(recyclerView)
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    SCROLL_STATE_DRAGGING -> {
                        OplusJankWrapper.gfxScrollState(
                            recyclerView.context, JANK_SCENE_ID_RECYCLERVIEW_SCROLL,
                            JANK_SCENE_DES_RECYCLERVIEW_SCROLL, OplusJankWrapper.ScrollState.Drag
                        )
                    }

                    SCROLL_STATE_IDLE -> {
                        contentDescriptionSetter.forceFlush()
                        OplusJankWrapper.gfxScrollState(
                            recyclerView.context, JANK_SCENE_ID_RECYCLERVIEW_SCROLL,
                            JANK_SCENE_DES_RECYCLERVIEW_SCROLL, OplusJankWrapper.ScrollState.Idle
                        )
                    }
                }
            }
        })

        recyclerView?.let {
            it.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                GLog.d(TAG, "onLayoutChange")
                // post的原因参考[isFirstScrollRecyclerView]
                checkToolbarTint(it.computeVerticalScrollOffset())
            }
        }
    }

    override fun currentShouldToolbarTint(): Boolean {
        return if (personalFilterViewWrapper.isShowed()) {
            context?.let { COUIDarkModeUtil.isNightMode(it) } ?: false
        } else {
            isSupportImmersive
                    && (isInBottomOverScrolling()
                    || ((recyclerView?.computeVerticalScrollOffset() ?: 0) > scrollPositionToImmersive))
        }
    }

    open fun currentShouldShowTranslucent(): Boolean {
        return isSupportImmersive
                    && (isInBottomOverScrolling()
                    || ((recyclerView?.computeVerticalScrollOffset() ?: 0) > scrollPositionToImmersive))
    }

    override fun generateToolbarSetter(): ToolbarSetter {
        return InternalToolbarSetter()
    }

    override fun refreshToolbar() {
        setHasOptionsMenu(true)
        toolbarSetter?.rebindActivity()
        toolbarSetter?.setSelectionMode(isInSelectionMode())
    }

    override fun supportClickStatusBar(): Boolean = true

    override fun onStatusBarClicked() {
        super.onStatusBarClicked()
        needScrollToBottom = false
    }

    override fun onTotalCountChanged(totalCount: Int) {
        super.onTotalCountChanged(totalCount)
        if (exitCurrentFragmentIfNeed(totalCount)) {
            if (isFirstLoadTotalCount) {
                // 图集列表显示已删除图集-点击进入又立即退出,需要触发全量同步纠正
                GLog.d(TAG, LogFlag.DL) { "onTotalCountChanged: totalCount:$firstTotalCount->$totalCount" }
                firstTotalCount = totalCount
            }
            return
        }
        // bugid:1863967 totalCount=-1的情况是默认状态，还未加载完成，不能显示空页面，否则当加载完成时会闪空页面
        refreshEmptyPageView(totalCount)
        if ((totalCount > 0) && needScrollToBottom) {
            GLog.d(TAG, LogFlag.DL) { "onTotalCountChanged. scroll to bottom, totalCount=$totalCount" }
            recyclerAdapter.scrollByItemIndex(totalCount - 1 + recyclerAdapter.footerCount)
        }
        invalidateOptionsMenu()
        // quick图删除菜单置灰，原图写完后刷新使删除菜单恢复
        if (isInSelectionMode()) {
            onSelectionStateChange()
        }
        // onTotalCountChanged触发较快，需要等待下一帧通知设置contentDescription
        view?.post {
            contentDescriptionSetter.updateBatchSize(totalCount)
        }
        if (isFirstLoadTotalCount) {
            refreshVisibleRangeIfNeed()
            isFirstLoadTotalCount = false
            firstTotalCount = totalCount
        }
    }

    /**
     * 是否处于底部向上的过滑动
     */
    protected fun isInBottomOverScrolling(): Boolean = overScrollingY > scrollPositionToImmersive

    private fun onScrollCheckToolbarTint(recyclerView: RecyclerView) {
        // 滑动过程中，如果个性化筛选面板正在展示且未退出，就不要更新toolbar了，否则会造成标题闪烁
        if (personalFilterViewWrapper.isShowed()) return
        checkToolbarTint(recyclerView.computeVerticalScrollOffset(), isNeedAnimation = true)
    }

    fun isHeaderVisible(): Boolean {
        return headerTipsContainer?.let {
            (it.height > 0) && it.isVisible
                // tip提示滑动隐藏后，切换横屏后重新布局后有高度，列表的顶部位置是小于toolBar的位置，此时也算tip不可见
                && isRecyclerViewBelowToolBar()
        } ?: false
    }

    /**
     * 列表的顶部位置是不是大于toolBar的位置
     */
    private fun isRecyclerViewBelowToolBar(): Boolean {
        return recyclerView?.let { recyclerView -> recyclerView.paddingTop > getToolBarLocationY() } ?: false
    }

    /**
     * 获取ToolBar在屏幕上实际位置(包含padding)，用于判断和列表的相对位置关系
     */
    protected fun getToolBarLocationY(): Int {
        return toolbar?.takeIf { it.height > 0 }?.let { it.bottom + recyclerViewPaddingTop } ?: 0
    }

    private fun refreshEmptyPageView(totalCount: Int) {
        if (totalCount == NO_ITEMS) {
            updateRVHeightIfNeed(LayoutParams.WRAP_CONTENT)
            showEmptyPageView()
        } else {
            updateRVHeightIfNeed(LayoutParams.MATCH_PARENT)
            hideEmptyPageView()
        }
        personalFilterViewWrapper.cancelUnnecessaryAnimation()
        // 数据刷新后，也需要一次着色检查，因为有可能筛选前后数据没有变化，那么由于筛选面板的着色处理可能不够准确，这里需要补充一次
        checkToolbarTint(recyclerView?.computeVerticalScrollOffset() ?: 0, isForceStart = true, isNeedAnimation = false)
    }

    /**
     * bugId:9378215
     * 图集详情页点击筛选，点击收藏，从有数据的界面进入空页面，会闪图
     * 设置RV的clipToPadding为true，不绘制padding区域内,等待diff动画完成，再设置回去
     */
    private fun setRVClipPadding() {
        recyclerView?.let { rv ->
            if (rv.clipToPadding) return
            tempOriginClipToPadding ?: run {
                tempOriginClipToPadding = rv.clipToPadding
                rv.clipToPadding = true
            }
        }
    }

    /**
     * 恢复RV的clipToPadding属性
     */
    private fun restoreRVClipPadding() {
        tempOriginClipToPadding?.let { clip -> recyclerView?.clipToPadding = clip }
        tempOriginClipToPadding = null
    }

    private fun exitCurrentFragmentIfNeed(totalCount: Int): Boolean {
        if ((totalCount == NO_ITEMS) && isResumed && needExitWhenEmpty) {
            exitCurrentFragment()
            return true
        }
        return false
    }

    /**
     * 1.由于adapter默认设置为传入参数viewData.totalCount,adapter首次刷新列表时，数据还未加载，
     * 导致vm.setVisibleRange没更新(vm.totalCount==-1,调用vm.setVisibleRange直接返回了)
     *
     * 2.当数据加载回来之后，由于可见区域没变化，所以不会onScroll不会执行，也就不会再次执行vm.setVisibleRange，
     * 导致vm的range一直没刷新，最终viewData没有刷新，界面一直灰图
     *
     * 3.所以需要处理adapter设置默认totalCount时，首次数据加载完成后,主动触发resetAndJudgeVisibleRange
     *
     * 场景如下：
     *  侧边导航栏-》选中任一图集A-》切换到 共享图集（加载过程中）-》立即切换回 图集A
     */
    private fun refreshVisibleRangeIfNeed() {
        if (isDefaultTotalCountEnabled()) {
            recyclerView?.post {
                recyclerAdapter.resetAndJudgeVisibleRange()
            }
        }
    }

    /**
     * 进入图集详情页时，是否需要默认显示列表（此时列表都是默认灰图），通过设置默认totalCount来显示
     * 侧边导航栏进入图集详情页时需要默认显示列表,避免闪一帧空白页面
     * 使用详见 {@link BaseAlbumFragment#getListAdapter()},
     * @return true adapter默认使用传入参数的totalCount ,false:默认adapter.totalCount = 0
     */
    private fun isDefaultTotalCountEnabled(): Boolean {
        return isFromSidePanePage()
    }

    private fun refreshCreateMemoryIcon() {
        createMemoryMenuItem?.isVisible = isCountEnoughCreateMemory && isPersonalFiltered().not()
    }

    @Suppress("LongMethod")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (!isResumed) return
        menu.clear()
        cancelButton = null
        selectAllCheckButton = null
        filterMenuItem = null
        searchMenuItem = null
        addToAlbumMenuItem = null
        createMemoryMenuItem = null
        albumHideOnTimeLinePageMenuItem = null
        val isTotalCountGreaterThanZero = baseListViewModel?.totalSize ?: 0 > 0
        when {
            isInSelectionMode() -> {
                toolbar?.isTitleCenterStyle = true
                setDisplayHomeAsUpEnabled(false)
                inflater.inflate(BasebizR.menu.base_opt_album_selection, menu)
                menu.findItem(BasebizR.id.action_select_all)?.let { menuItem ->
                    selectAllCheckButton = menuItem.actionView as? MenuCheckButton
                    selectAllCheckButton?.let { checkButton ->
                        checkButton.state = if (isSelectAll()) SELECT_ALL else SELECT_NONE
                        checkButton.setOnClickListener {
                            ClickUtil.clickable()
                            onOptionsItemSelected(menuItem)
                        }
                    }
                }
                menu.findItem(BasebizR.id.action_cancel)?.let { menuItem ->
                    cancelButton = menuItem.actionView as? MenuButton
                    cancelButton?.let { cancelButton ->
                        cancelButton.setOnClickListener {
                            ClickUtil.clickable()
                            onOptionsItemSelected(menuItem)
                        }
                    }
                }
            }

            shouldRefreshMenu() -> {
                toolbar?.isTitleCenterStyle = false
                inflater.inflate(BasebizR.menu.base_opt_album_unselection, menu)
                albumHideOnTimeLinePageMenuItem = menu.findItem(BasebizR.id.action_album_hide_on_time_line_page)
                filterMenuItem = menu.findItem(BasebizR.id.action_filter)
                searchMenuItem = menu.findItem(BasebizR.id.action_search)
                createMemoryMenuItem = menu.findItem(BasebizR.id.action_create_memories)
                addToAlbumMenuItem = menu.findItem(BasebizR.id.action_add_to_album)
                if (isShowAlbumHideOnTimeLinePageMenuItem()) {
                    albumHideOnTimeLinePageMenuItem?.isVisible = true
                    lifecycleScope.launch(Dispatchers.IO) {
                        val isAlbumHide = isAlbumHideOnTimeLinePage()
                        withContext(Main) {
                            albumHideOnTimeLinePageMenuItem?.setChecked(isAlbumHide)
                        }
                    }
                } else {
                    albumHideOnTimeLinePageMenuItem?.isVisible = false
                }
                // 设置个纯色icon即可，后续通过着色改变
                if (isPersonalFiltered()) {
                    filterMenuItem?.setIcon(BasebizR.drawable.main_ic_filter_high_light.mutateDrawable(context))
                } else {
                    filterMenuItem?.setIcon(BasebizR.drawable.main_ic_filter_unselect_dark.mutateDrawable(context))
                }
                createMemoryMenuItem?.setIcon(BasebizR.drawable.base_menu_ic_create_memories.mutateDrawable(context))
                refreshCreateMemoryIcon()
                addToAlbumMenuItem?.setIcon(BasebizR.drawable.base_menu_ic_add.mutateDrawable(context))
                addToAlbumMenuItem?.isVisible = isShowAddToAlbumIcon
                if (isSupportedPersonalFilter) {
                    menu.findItem(BasebizR.id.action_select)?.apply {
                        isVisible = true
                        isEnabled = isTotalCountGreaterThanZero
                    }
                } else {
                    menu.findItem(BasebizR.id.action_select)?.isVisible = isTotalCountGreaterThanZero
                }
                menu.findItem(BasebizR.id.action_set_album_cover)?.isEnabled = isTotalCountGreaterThanZero
                searchMenuItem?.isVisible = showSearchMenu()
                updateCoverMenuView()
                menu.findItem(BasebizR.id.action_rename_album)?.isVisible = albumViewData?.isSupportRename() ?: false

                updateOrderMenuView(isTotalCountGreaterThanZero)
                isSupportCreateMemory
            }

            else -> {
                /*
                 * do nothing
                 */
            }
        }
        // 解决BUG 1772132 图集内照片不足一屏---缓缓上滑至刚出发半透明---打开一张图片图片---返回后箭头和搜索按钮未反色
        checkToolbarTint(recyclerView?.computeVerticalScrollOffset() ?: 0, true, isNeedAnimation = false)
        toolbar?.menuView?.apply {
            setOverflowMenuListener { popup ->
                subMenuPopup = popup
                subMenuPopup?.let {
                    albumOrderBinding?.setSubMenuItemList(it)
                    albumCoverBinding?.setSubMenuItemListIfNeed(it)
                }
            }
        }
    }

    private fun shouldRefreshMenu() = showInternalToolbar && !isInSelectionMode()

    protected fun isPersonalFiltered(): Boolean = isSupportedPersonalFilter &&
        ((baseListViewModel as? AlbumViewModel)?.isPersonalFiltered() == true)

    override fun onCreateTintElementsIfNeed(
        lightNightDriver: IProgressChanger<Boolean>,
        directionAnimDriver: IProgressChanger<Boolean>,
        elementList: MutableList<DirectionAnimationElement>
    ) {
        super.onCreateTintElementsIfNeed(lightNightDriver, directionAnimDriver, elementList)
        val context = context ?: return
        onCreateTintElementsIfNeedImpl(context, lightNightDriver, elementList)
    }

    private fun onCreateTintElementsIfNeedImpl(
        context: Context,
        changer: IProgressChanger<Boolean>,
        elementList: MutableList<DirectionAnimationElement>,
        isEarlyFinishAnimation: Boolean = false
    ) {
        val owner = this

        /*
         不支持沉浸式 + 亮色模式，不需要改变任何元素的颜色
         此时的标题栏是固定的白色背景，所以下面的元素都不需要改变颜色。
         */
        if (isSupportImmersive.not() && !COUIDarkModeUtil.isNightMode(context)) return

        cancelButton?.takeIf { it.isVisible }?.let {
            elementList.add(TintMenuButtonDrawableElement(context, it, changer) { currentShouldToolbarTint() }.apply { register(owner) })
            elementList.add(TintMenuButtonTextElement(context, it, changer) { currentShouldToolbarTint() }.apply { register(owner) })
        }
        selectAllCheckButton?.takeIf { it.isVisible }?.let {
            elementList.add(TintMenuButtonDrawableElement(context, it, changer) { currentShouldToolbarTint() }.apply { register(owner) })
            elementList.add(TintMenuButtonTextElement(context, it, changer)  { currentShouldToolbarTint() }.apply { register(owner) })
        }

        /*
         不支持沉浸式，不需要改变下面的元素的颜色
         此时的标题栏是固定的背景色，所以下面的元素都不需要改变颜色。
        */
        if (isSupportImmersive.not()) return

        activity?.let {
            elementList.add(TintStatusElement(it, changer, isEarlyFinishAnimation).apply { register(owner) })
            if (isInSelectionMode().not()) {
                elementList.add(TintBackElement(context, it, changer, isEarlyFinishAnimation).apply { register(owner) })
            }
        }
        toolbar?.let {
            elementList.add(TintTitleElement(it, changer, isEarlyFinishAnimation).apply { register(owner) })
            elementList.add(TintSubtitleElement(context, it, changer, isEarlyFinishAnimation).apply { register(owner) })
            if (isInSelectionMode().not()) {
                it.overflowIcon?.let { icon ->
                    elementList.add(TintDrawableAnimationElement(icon, changer, isEarlyFinishAnimation).apply { register(owner) })
                }
            }
        }
        filterMenuItem?.takeIf { it.isVisible }?.let {
            val toolbar = toolbar ?: return
            elementList.add(TintFilterElement(context, it, toolbar, changer, isEarlyFinishAnimation) {
                isPersonalFiltered() }.apply { register(owner) })
        }
        addToAlbumMenuItem?.takeIf { it.isVisible }?.icon?.let {
            elementList.add(TintDrawableAnimationElement(it, changer, isEarlyFinishAnimation).apply { register(owner) })
        }
        createMemoryMenuItem?.takeIf { it.isVisible && it.isEnabled }?.icon?.let {
            elementList.add(TintDrawableAnimationElement(it, changer, isEarlyFinishAnimation).apply { register(owner) })
        }
        topTranslucentView?.let {
            elementList.add(
                object : TintTranslucentElement(
                    it,
                    changer,
                    (currentShouldShowTranslucent()).let { shouldTint ->
                        (currentShowTopTranslucent != shouldTint).apply {
                            currentShowTopTranslucent = shouldTint
                        }
                    },
                    currentShouldShowTranslucent()
                ) {
                    override fun updateAlpha(progress: Float) {
                        // 顶部蒙层 View 可能带有模糊效果，不能通过 View.setAlpha 改变透明度，否则会导致模糊视图变黑
                        topTranslucentView?.background?.alpha = (progress * ALPHA_255.toFloat()).toInt()
                    }
                }.apply { register(owner) }
            )
        }
        sidePaneAccessor?.takeIf { it.getSidePaneState().isOpen().not() }?.let {
            elementList.add(FilterPanelTintExpandElement(context, it, changer).apply { register(owner) })
        }
    }

    private fun updateOrderMenuView(isTotalCountGreaterThanZero: Boolean) {
        val toolbarTmp = toolbar ?: return
        (baseListViewModel as? AlbumViewModel)?.let { albumViewModel ->
            if (albumOrderBinding == null) {
                albumOrderBinding = AlbumOrderBinding(viewLifecycleOwner, toolbarTmp, albumViewModel)
            }
            albumOrderBinding?.updateMenuView(isTotalCountGreaterThanZero)
        }
    }

    private fun updateCoverMenuView() {
        val toolbarTmp = toolbar ?: return
        (baseListViewModel as? AlbumViewModel)?.let { albumViewModel ->
            if (albumCoverBinding == null) {
                albumCoverBinding = AlbumCoverBinding(this, toolbarTmp, albumViewModel) {
                    albumViewData
                }
            }
            albumCoverBinding?.updateMenuView()
        }
    }

    @Suppress("LongMethod")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!isResumed) return false
        if (DoubleClickUtils.isFastDoubleClick()) return false
        val trackCallerEntry = TrackCallerEntry(
            page = trackPage,
            path = baseListViewModel?.trackPath,
            albumsActionCurrentPage = getUserActionCurPage(TYPE_ALBUMS_ACTION)
        )

        when (item.itemId) {
            BasebizR.id.action_search -> {
                Starter.ActivityStarter(
                    this,
                    postCard = PostCard(RouterConstants.RouterName.SEARCH_ACTIVITY),
                    requestCode = SEARCH_REQUEST_CODE
                ).start()
                if (isFromMapPage()) {
                    // 进入搜索页时，去除alpha动画，避免闪现地图
                    activity?.overridePendingTransition(
                        couiR.anim.coui_open_slide_enter,
                        BasebizR.anim.business_lib_open_slide_exit_no_alpha
                    )
                }
            }

            BasebizR.id.action_select -> {
                AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                    enterSelect = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE,
                )
                enterSelectionMode()
            }

            BasebizR.id.action_cancel -> exitSelectionMode()
            BasebizR.id.action_select_all -> {
                markClickedSelectAllButton = if (isSelectAll()) {
                    unselectAll()
                    false
                } else {
                    selectAll()
                    true
                }
            }

            BasebizR.id.action_create_memories -> {
                (baseListViewModel?.model as? AlbumModel)?.let {
                    GLog.d(TAG, "onOptionsItemSelected: ID: action_create_memories")
                    MenuOperationManager.doAction(
                        action = MenuAction.ALBUM_CREATE_MEMORY,
                        paramMap = MenuActionGetter.albumCreateMemory.builder
                            .setAlbumModel(it)
                            .setActivity(activity as BaseActivity)
                            .setLifecycle(this.lifecycle)
                            .setTrackCallerEntry(trackCallerEntry)
                            .build()
                    )
                }
            }

            BasebizR.id.action_add_to_album -> {
                baseListViewModel?.model?.let { model ->
                    model.getDirName().let {
                        val currentBucketId: Int? = if (model.getCoverItems().isNotEmpty()) {
                            model.getCoverItems()[0].bucketId
                        } else {
                            null
                        }
                        MenuOperationManager.doAction(
                            MenuAction.CREATE_ALBUM,
                            MenuActionGetter.createAlbum.builder
                                .setFragment(this@BaseAlbumFragment)
                                .setTargetAlbumName(it)
                                .setCurrentBucketId(currentBucketId)
                                .setTrackCallerEntry(trackCallerEntry)
                                .build()
                        )
                    }
                }
            }

            BasebizR.id.action_set_album_cover -> albumCoverBinding?.clickCoverMenuItem()

            BasebizR.id.action_rename_album -> {
                renameAlbum(albumViewData, trackCallerEntry)
            }

            BasebizR.id.action_filter -> {
                val activity = activity ?: return true
                val appBarLayout = (toolbar?.parent as? View) ?: return true
                isClickedFilter = true
                val personalFilters = (baseListViewModel as? AlbumViewModel)?.getAllPersonalFilters() ?: return true
                personalFilterViewWrapper.showFilterPanel(
                    activity = activity,
                    owner = this@BaseAlbumFragment,
                    anchorView = appBarLayout,
                    dataConfig = PersonalFilterPanelDataConfig(
                        getFilterTypes(),
                        personalFilters,
                        appBarLayout.height,
                        if (isSupportImmersive) 0 else statusBarHeight()
                    )
                ) { changer, list, isChanged ->
                    if (currentShouldToolbarTint().not()) return@showFilterPanel
                    if (isChanged) {
                        sidePaneAccessor?.takeIf { it.getSidePaneState().isOpen().not() }?.let {
                            list.add(FilterPanelTintExpandElement(activity, it, changer).apply { register(this@BaseAlbumFragment) })
                        }
                        return@showFilterPanel
                    }
                    onCreateTintElementsIfNeedImpl(activity, changer, list)
                }
            }

            BasebizR.id.action_sort -> {
                subMenuPopup?.apply {
                    setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
                        albumOrderBinding?.setAlbumOrder(position)
                        dismiss()
                    })
                }
            }

            BasebizR.id.action_album_hide_on_time_line_page -> {
                albumViewData?.let { data ->
                    albumHideOnTimeLinePageMenuItem?.let {
                        if (it.isChecked) {
                            MenuOperationManager.doAction(
                                action = MenuAction.ALBUM_SHOW_ON_TIME_LINE_PAGE,
                                paramMap = MenuActionGetter.showAlbumOnTimeLinePage.builder
                                    .setAlbumId(data.albumId)
                                    .setTrackCallerEntry(trackCallerEntry)
                                    .build(),
                                onCompleted = ::onReceiveAlbumShowOnTimeLinePageResult
                            )
                        } else {
                            MenuOperationManager.doAction(
                                action = MenuAction.ALBUM_HIDE_ON_TIME_LINE_PAGE,
                                paramMap = MenuActionGetter.hideAlbumOnTimeLinePage.builder
                                    .setAlbumId(data.albumId)
                                    .setTrackCallerEntry(trackCallerEntry)
                                    .build(),
                                onCompleted = ::onReceiveAlbumHideOnTimeLinePageResult
                            )
                        }
                    }
                }
            }
        }
        return true
    }

    /**
     * 重命名图集
     */
    protected open fun renameAlbum(viewData: AlbumViewData?, trackCallerEntry: TrackCallerEntry) {
        context?.let { ctx ->
            bottomMenuHelper.doRenameAlbumAction(
                context = ctx,
                trackCallerEntry = trackCallerEntry,
                actionCallback = ::onReceiveAlbumRenameResult,
                albumPath = Path.fromString(albumViewData?.id)
            )
        }
    }

    private fun getFilterTypes(): Int {
        return when (albumViewData?.modelType) {
            TYPE_FAVORITES_ALBUM,
            TYPE_RECYCLE_ALBUM -> BasebizR.array.filter_types_image_video

            TYPE_VIDEO_ALBUM,
            TYPE_CSHOT_ALBUM,
            TYPE_FAST_VIDEO_ALBUM,
            TYPE_WIDGET_DISPLAY_LIST_ALBUM,
            TYPE_OLIVE_ALBUM,
            TYPE_GIF_ALBUM,
            TYPE_PANORAMA_ALBUM,
            TYPE_CARD_CASE_ALBUM,
            TYPE_MEMORIES_ALBUM,
            TYPE_RAW_ALBUM,
            TYPE_PORTRAIT_BLUR_ALBUM,
            TYPE_SLOW_MOTION_ALBUM,
            TYPE_DOUBLE_EXPOSURE_ALBUM,
            TYPE_DOLBY_ALBUM,
            TYPE_LOG_VIDEO_ALBUM,
            TYPE_ENHANCE_TEXT_ALBUM -> BasebizR.array.filter_types_favorites_only

            else -> BasebizR.array.filter_types
        }
    }

    private fun onReceiveAlbumRenameResult(success: Boolean, customName: String?) {
        if (success) {
            lifecycleScope.launch(Dispatchers.UI) {
                // 当图集内重命名成功后，及时刷新tittle为新的名称
                toolbar?.title = customName
                albumName = customName
            }
        } else {
            GLog.e(TAG, "onReceiveAlbumRenameResult, album rename fail.")
        }
    }

    protected open fun getAlbumName(): String? = if (TextUtils.isEmpty(albumName)) albumViewData?.title else albumName

    override fun onEnterSelection() {
        GTrace.trace("$TAG.onEnterSelection") {
            super.onEnterSelection()

            /** 进入选择模式时，启用一碰分享 */
            enableOneTouchShare()

            showBottomMenuBar()
            if (!showInternalToolbar) {
                toolbarFadeAnimator?.updateSelectionTitleLater = false
                updateToolbarEditTitle(
                    defaultTitleResId = BasebizR.string.base_title_select_image,
                    quantityTitleResId = BasebizR.plurals.base_title_has_select,
                    count = baseListViewModel?.getSelectedItemCount() ?: 0
                )
            }
            toolbarSetter?.setSelectionMode(true)
            val count = baseListViewModel?.getSelectedItemCount() ?: 0
            updateBottomBar(count)
            recyclerAdapter.isMaskVisible = true
        }
    }

    override fun onExitSelection() {
        GTrace.trace("$TAG.onExitSelection") {
            super.onExitSelection()
            updateBottomBar(0)
            hideBottomMenuBar()
            toolbarSetter?.setSelectionMode(false)
            recyclerAdapter.isMaskVisible = false
            //退出时重置点击全选按钮标记
            markClickedSelectAllButton = false

            /** 退出选择模式时，关闭一碰分享 */
            disableOneTouchShare()
        }
    }

    /**
     * 启用一碰分享
     */
    private fun enableOneTouchShare() {
        /** 页面或系统不支持则不激活一碰分享 */
        if (supportOneTouchShare.not() || ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE).not()) {
            GLog.w(TAG, "[enableOneTouchShare] Failed : OneTouchShare is not supported!")
            return
        }

        if (oneTouchShareController == null) {
            oneTouchShareController = OneTouchShareController(activity = activity, lifecycleOwner = this, workSession = session)
            oneTouchShareController?.enable()
        }

        GLog.d(TAG, "[enableOneTouchShare] registerShareEvent : selectedItems = ${baseListViewModel?.getSelectedItems()} ")
        baseListViewModel?.let { oneTouchShareController?.setOrUpdateSharePaths(it.getSelectedItems()) }
    }

    /**
     * 关闭一碰分享
     */
    private fun disableOneTouchShare() {
        oneTouchShareController?.disable()
        oneTouchShareController = null
    }

    override fun onSelectionStateChange() {
        val count = baseListViewModel?.getSelectedItemCount() ?: 0
        updateToolbarText(count)
        updateBottomBar(count)
        //当选中数量为0时，即使点击了全选按钮也不走清空相册强弹窗逻辑。正常情况下,最近项目图集中,只要点击了全选按钮(可能手动反选了几张),且开启云服务,则依旧强弹窗提醒
        if (count == 0) {
            markClickedSelectAllButton = false
        }
        /** 选择发生变时，更新NFC事件注册 */
        GLog.d(logTag, LogFlag.DL) { "onSelectionStateChange count:$count==${baseListViewModel?.totalSize}, isSelectAll=${isSelectAll()}" }
        baseListViewModel?.let { oneTouchShareController?.setOrUpdateSharePaths(it.getSelectedItems()) }
    }

    protected open fun updateBottomBar(count: Int) {
        bottomMenuBar?.apply {
            baseListViewModel?.let { bottomMenuHelper.updateBottomMenu(count, it.getSelectedItems(), this) }
            if (isFromMapPage()) {
                bottomMenuHelper.setItemEnable(BasebizR.id.action_move_to, false, this)
                bottomMenuHelper.setItemEnable(BasebizR.id.action_encrypt, false, this)
            }
            if (isNeedShowCreateMenuFlag) {
                bottomMenuHelper.setBottomMenuItemRedDotVisible(
                    BasebizR.id.action_photo_create,
                    isRedDotVisible = false,
                    this
                )
            }
        }
    }

    private fun updateToolbarText(count: Int) {
        updateToolbarEditTitle(
            defaultTitleResId = BasebizR.string.base_title_select_image,
            quantityTitleResId = BasebizR.plurals.base_title_has_select,
            count = count
        )
        updateSelectAllButton()
    }

    /**
     * 更新全选按钮状态
     */
    private fun updateSelectAllButton() {
        val lastSelectedStatus = selectAllCheckButton?.state
        selectAllCheckButton?.state = if (isSelectAll()) SELECT_ALL else SELECT_NONE
        if ((lastSelectedStatus != selectAllCheckButton?.state)) {
            // 8655295 按钮由全选<->取消反选变化时，由于文本长度不一致，导致显示不全，所以需要重新触发更新
            invalidateOptionsMenu()
        }
    }

    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        bottomMenuHelper.setClickedMenuItemId(menuItem.itemId)
        submitSelection {
            getSelectedItemSpecifiedCount { bundle ->
                val imageCount = bundle.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT, 0)
                val videoCount = bundle.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT, 0)
                val menuItemId = menuItem.itemId
                onBottomNavBarItemClicked(menuItemId, imageCount, videoCount)
            }
        }
    }

    @Suppress("LongMethod")
    open fun onBottomNavBarItemClicked(menuItemId: Int, imageCount: Int, videoCount: Int) {
        if (DoubleClickUtils.isFastDoubleClick()) return
        val totalItemsCount = baseListViewModel?.totalSize

        when (menuItemId) {
            BasebizR.id.action_share -> {
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_SHARE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
                val bundle = Bundle().apply {
                    baseListViewModel?.let { putInt(KEY_SELECTION_DATA_ID, it.selectionDataId) }
                    putString(ShareAction.KEY_VIEWDATA_ID, albumViewData?.id ?: EMPTY_STRING)
                    putString(ShareAction.KEY_MODEL_TYPE, albumViewData?.modelType ?: EMPTY_STRING)
                }
                val (focusPosition, focusPath) = findSortInFrontSelectedItem()
                bottomMenuHelper.doShareAction(
                    context,
                    bundle,
                    focusPath = focusPath,
                    focusPosition = focusPosition,
                    onCompleteCallback = fun(op: Int) {
                        if (isDetached || isAdded.not() || (context == null)) return
                        when (op) {
                            ShareHelper.STATE_DISMISS_AND_EXIT_EDIT_MODE -> {
                                if (isInSelectionMode()) {
                                    exitSelectionMode()
                                }
                            }

                            ShareHelper.STATE_DISMISS_AND_UPDATE_SELECTED_SHOW -> {
                                (baseListViewModel as? AlbumViewModel)?.refreshSelectedShow()
                                baseListViewModel?.getSelectedItemCount()?.let { updateBottomBar(it) }
                                updateToolbarEditTitle(
                                    defaultTitleResId = BasebizR.string.base_title_select_image,
                                    quantityTitleResId = BasebizR.plurals.base_title_has_select,
                                    count = baseListViewModel?.getSelectedItemCount() ?: 0
                                )
                                selectAllCheckButton?.state = if (isSelectAll()) SELECT_ALL else SELECT_NONE
                            }
                            else -> Unit
                        }
                    },
                    trackCallerEntry = trackCallerEntry
                )
            }

            BasebizR.id.action_encrypt -> {
                bottomMenuHelper.doSafeBoxAction(
                    context,
                    this.lifecycle,
                    trackCallerEntry = trackCallerEntry,
                    callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(this.lifecycle, ::onRemoveActionCallback)
                )
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_ENCRYPT, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
            }

            BasebizR.id.action_photo_creation -> {
                // 先发送数据，否则在doPhotoCreationAction中又会有一个类似的操作将会把map中的事件ID覆盖
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_CREATE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
                bottomMenuHelper.doPhotoCreationAction(
                    this,
                    bottomMenuBar,
                    currentPage = getUserActionCurPage(CollageTrackConstant.TYPE_COLLAGE),
                    trackCallerEntry = trackCallerEntry,
                    albumNavigationTrackPageInfo = trackAlbumPageInfo
                )
            }

            BasebizR.id.action_photo_jigsaw -> {
                bottomMenuHelper.doJigsawAction(this, getUserActionCurPage(CollageTrackConstant.TYPE_COLLAGE), trackCallerEntry)
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_COLLAGE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
            }

            BasebizR.id.action_photo_create -> {
                // 先发送数据，否则在doCreateAction中又会有一个类似的操作（GIF和拼图的埋点）将会把map中的事件ID覆盖
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_CREATE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
                bottomMenuHelper.doCreateAction(
                    this,
                    bottomMenuBar,
                    getUserActionCurPage(),
                    trackCallerEntry,
                    imageCount,
                    videoCount
                )
            }

            BasebizR.id.action_recycle -> {
                bottomMenuHelper.doSelectionRecycleActionForAlbum(
                    actionId = BottomMenuHelper.ACTION_RECYCLE,
                    totalItemCount = totalItemsCount ?: 0,
                    isClickSelectAll = markClickedSelectAllButton,
                    albumViewDataPath = albumViewData?.id,
                    trackCallerEntry = trackCallerEntry,
                    callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                )
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_DELETE, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
            }

            BasebizR.id.action_move_to -> {
                bottomMenuHelper.doAppendToAction(
                    this@BaseAlbumFragment,
                    (baseListViewModel?.model as? AlbumModel)?.getPath(),
                    trackCallerEntry = trackCallerEntry,
                    callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                )
                LaunchExitPopupTrackHelper.trackSendAlbumNavigationClick(
                    LaunchExitPopupConstant.Value.NAV_BAR_ACTION_ADD_TO, this
                        .trackAlbumPageInfo, imageCount, videoCount
                )
            }

            BasebizR.id.action_import -> activity?.let { bottomMenuHelper.doImportAction(null, it, trackCallerEntry) }

            BasebizR.id.action_more -> {
                val moreItemList = ArrayList<MenuListPopupItem>()
                val isFromMap = isFromMapPage()
                moreItemList.add(MenuListPopupItem(BasebizR.string.base_export_video, isEnabled = true))
                moreItemList.add(MenuListPopupItem(BasebizR.string.base_move_to_gallery, false, !isFromMap))
                moreItemList.add(MenuListPopupItem(BasebizR.string.base_copy_to, false, !isFromMap))
                if (isShowEncryptionMenuToMore()) {
                    moreItemList.add(MenuListPopupItem(BasebizR.string.base_safe_encryption_nemu, false, true))
                }
                bottomMenuHelper.doMoreMenuAction(
                    this,
                    bottomMenuBar,
                    (baseListViewModel?.model as? AlbumModel)?.getPath(),
                    BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback),
                    trackCallerEntry,
                    BasebizR.id.action_more,
                    moreItemList
                )
            }
        }
    }

    /**
     * 在选中的items中，找到排在最前面的item，并返回其位置和路径
     */
    private fun findSortInFrontSelectedItem(): Pair<Int, Path?> {
        var focusPath: Path? = null
        var focusPosition = -1
        getSelectedItems()?.let { selectItems ->
            (baseListViewModel as? AlbumViewModel)?.apply {
                val isPositiveOrder = this.isPositiveOrderFromSetting()
                for (item in selectItems) {
                    val position = getItemIndexByPath(item.toString())
                    // 正序时，最新图片在下方，position大的图片是最新的
                    val frontPicInPositive = isPositiveOrder && (position > focusPosition)
                    // 倒序时，最新图片在上方，position小的图片是最新的
                    val frontPicInNegative = isPositiveOrder.not() && (position < focusPosition)
                    if ((focusPosition == -1)
                        || frontPicInPositive
                        || frontPicInNegative
                    ) {
                        focusPosition = position
                        focusPath = item
                    }
                }
            }
        }
        return Pair(focusPosition, focusPath)
    }

    protected open fun showSlotFooter(): Boolean = false

    protected open fun showSearchMenu(): Boolean {
        if (isOpenedFromSearchActivity) {
            return false
        }
        return true
    }

    /**
     * 人宠合照详情从继承原来TimeNodeFragment改造成继承BaseAlbumFragment后，
     * BaseAlbumFragment设为私密菜单是直接定义在xml中默认固定显示
     * 原人物详情设为私密菜单是显示在自定义的更多菜单中
     *
     * 为了与原来保持一致，将人宠详情页的isShowEncryptionMenuToMore重载为true,将此菜单展示在自定义的更多菜单中
     */
    protected open fun isShowEncryptionMenuToMore(): Boolean = false

    override fun showBottomMenuBar() {
        super.showBottomMenuBar()
        getSafeBottomMenuHelper()?.bindMenuView(bottomMenuBar)
        val isSupportSafeBoxAlbum = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM)
        bottomMenuBar?.let {
            it.menu.findItem(BasebizR.id.action_encrypt)?.isVisible = isSupportSafeBoxAlbum
        }
        onBottomMenuHeightChanged(bottomMenuHeight)
    }

    /**
     * 如果所有item都完全可见（即图片不满一屏），且最底部的图片与recyclerView的距离大于底部导航栏的高度，
     * 则图片不会被导航栏遮挡，不需要添加底部padding
     */
    protected fun canDisplayInOneScreen(): Boolean {
        recyclerView?.layoutManager?.apply {
            if (this is LinearLayoutManager) {
                val lastItemPosition = recyclerAdapter.itemCount - 1
                if ((findFirstCompletelyVisibleItemPosition() != 0) ||
                    (findLastCompletelyVisibleItemPosition() != lastItemPosition)
                ) {
                    return false
                }
                val viewBottom = findViewByPosition(lastItemPosition - recyclerAdapter.footerCount)?.bottom ?: 0
                return recyclerView!!.bottom - viewBottom - getCurrentGestureBarHeight() > bottomMenuHeight
            }
        }
        return false
    }

    override fun hideBottomMenuBar() {
        super.hideBottomMenuBar()
        recyclerView?.updatePadding(bottom = recyclerViewPaddingBottom)
    }

    override fun onBottomMenuHeightChanged(height: Int) = GTrace.trace("$TAG.onUpdateBottomNavBarHeight") {
        updateRecyclerViewBottomPadding(height)
        onUpdateFastScrollerMarginBottom(height)
    }

    private fun updateRecyclerViewBottomPadding(bottomMenuHeight: Int) {
        bottomMenuBar?.takeIf {
            it.isVisible()
        }?.let {
            var bottomPadding = if (!canDisplayInOneScreen()) bottomMenuHeight else recyclerViewPaddingBottom
            /* 虚拟按键状态下，contentView会添加底部padding，菜单控件无需处理
             非虚拟按键有导航条情况下，contentView底部padding会被设为0，NavigationView会跟底部导航条重叠，为避免重叠将NavigationView的marginBottom的高度设为导航条的高度
             原来是使用的padding，padding是在自身限定高度做内部间距调整，设计是希望菜单栏在导航条之上不与导航条重叠，改为margin*/
            bottomPadding += getCurrentGestureBarHeight()
            recyclerView?.updatePadding(bottom = bottomPadding)
        }
    }

    override fun onRemoveActionCallback(path: List<Path>?, state: Int) {
        super.onRemoveActionCallback(path, state)
        if (state == BottomMenuHelper.ACTION_STATE_COMPLETED) {
            updateThumbReuseType()
        }
    }

    /**
     * 临时更改占位缩图复用规则(同bug7586304)。
     * 占位图原复用规则是新旧数据在同一index位置，且path相等时才进行复用,而删除场景复用不到故取了默认灰色占位图。
     */
    private fun updateThumbReuseType() {
        lifecycleScope.launch(Dispatchers.IO) {
            (baseListViewModel as? AlbumViewModel)?.let {
                it.thumbReuseType = ThumbReuseType.REUSE_BY_POSITION
                delay(DELETE_PHOTO_FLAG_CLEAR_DELAY)
                it.thumbReuseType = ThumbReuseType.REUSE_BY_ID
            }
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        config.onWidthChanged {
            //编辑模式为文字按钮需要刷新图标
            if (isResumed && !isInSelectionMode()) {
                val isShowBackArrow = isNeedHomeAsUp()
                setDisplayHomeAsUpEnabled(isShowBackArrow)
                adjustToolbarTitleMargin(isShowBackArrow)
            }
            if (this::bottomMenuHelper.isInitialized) {
                bottomMenuHelper.updateDialogIfNeed()
            }
        }
    }

    private fun adjustToolbarTitleMargin(isShowBackArrow: Boolean) {
        toolbar?.let { toolbar ->
            val marginRes = if (isShowBackArrow) {
                R.dimen.appbar_show_back_arrow_title_margin_start
            } else {
                R.dimen.appbar_title_margin_start
            }
            toolbar.titleMarginStart = resources.getDimensionPixelSize(marginRes)
        }
    }

    open fun onUpdateFastScrollerMarginBottom(bottomToolMenuHeight: Int) {
        fastScroller?.trackMarginBottom = bottomToolMenuHeight
    }

    private fun updateToolbarEditTitle(defaultTitleResId: Int, quantityTitleResId: Int, count: Int) {
        toolbarFadeAnimator?.let {
            if (it.updateSelectionTitleLater) return
        }
        val title = if (count == 0) {
            context?.resources?.getString(defaultTitleResId)
        } else {
            context?.resources?.getQuantityString(quantityTitleResId, count, count)
        }
        toolbarSetter?.setTitle(title)
    }

    private inner class InternalToolbarSetter : ToolbarSetter() {
        var isSelectionModeToolbar: Boolean = isInSelectionMode()

        override fun rebindActivity() {
            setSupportActionBar(toolbar)
            // 从侧边栏跳转至该页面,则该页面处于一级页面不需要返回键
            setDisplayHomeAsUpEnabled(isNeedHomeAsUp())
        }

        override fun setSelectionMode(selectionMode: Boolean) {
            if (!isResumed) return
            if (isSelectionModeToolbar == selectionMode) {
                refreshToolbar(selectionMode)
            } else {
                toolbarFadeAnimator?.apply {
                    cancelAnimation()
                    setToolbarAnimationListener(object : ToolbarFadeInFadeOutAnimator.ToolbarAnimationListener {

                        override fun onSwitch() {
                            updateSelectionTitleLater = !selectionMode
                            refreshToolbar(selectionMode)
                            invalidateOptionsMenu()
                        }
                    })
                    start()
                }
            }
            isSelectionModeToolbar = selectionMode
        }

        override fun setIsTitleCenterStyle(isTitleCenterStyle: Boolean) {
            toolbar?.isTitleCenterStyle = isTitleCenterStyle
        }

        override fun setTitle(charSequence: CharSequence?) {
            toolbar?.title = charSequence
        }

        override fun setSubtitle(subtitle: CharSequence?) {
            if (isSelectionModeToolbar) {
                toolbar?.subtitle = EMPTY_STRING
            } else {
                toolbar?.subtitle = subtitle
            }
        }

        private fun refreshToolbar(selectionMode: Boolean) {
            if (selectionMode) {
                // 解决重命名图集名称后，进入选择模式后再退出选择模式，toolbar tittle不刷新的问题
                val count = baseListViewModel?.getSelectedItemCount() ?: 0
                toolbarSetter?.setSubtitle(EMPTY_STRING)
                updateToolbarText(count)
                updateBottomBar(count)
            } else {
                // 从侧边栏跳转至该页面,则该页面处于一级页面不需要返回键
                setDisplayHomeAsUpEnabled(isNeedHomeAsUp())
                setIsTitleCenterStyle(false)
                (activity as? AppCompatActivity)?.supportActionBar?.let {
                    it.setHomeAsUpIndicator(
                        if (currentShouldToolbarTint()) BasebizR.drawable.base_menu_ic_back_arrow_sliding_up else couiR.drawable.coui_back_arrow
                    )
                    it.setHomeActionContentDescription(if (isNeedHomeAsUp()) BasebizR.string.base_description_home_as_up else 0)
                }
                // 地图页跳转过来时，title会异步加载后存在intent中
                setTitle(getAlbumName())
                if (showToolbarSubtitle()) {
                    toolbarSetter?.setSubtitle(getItemCountText())
                }
            }
        }
    }

    /**
     * 根据需要调用刷新,不能无脑刷,若是在滑动列表时不断invalid,则是会有性能卡顿问题的
     * 需要的场景
     * 1.全选/取消全选
     * 2.进出编辑模式
     * 3.totalCount为何要invalid，还未清楚
     * 内容发生变化,view的size没变,若不刷新,会导致显示不全
     */
    private fun invalidateOptionsMenu() {
        activity?.invalidateOptionsMenu()
    }

    /**
     * 是否需要返回或取消按钮:
     * 需要：不是从侧边栏进入该页面或者侧边栏不显示(分屏时,需要显示返回键)
     * 不需要：从侧边栏进入该页面并且侧边栏是显示以及处于非编辑模式时才不需要
     */
    private fun isNeedHomeAsUp(): Boolean {
        return !(!isInSelectionMode() && isFromSidePanePage() && sidePaneAccessor.isVisible())
    }

    override fun dispatchLongClick(position: Int): Boolean {
        if (!isInSelectionMode()) return false
        // 当处于选中状态并且手机处于平板互联连接情况下，图集拖拽传输分享
        if (synergyItemTouchListener.touchInterceptor.onLongPress()) {
            return true
        }
        if (!DragHelper.isEnable(albumViewData?.id) ||
            !isItemSelected(position)
        ) {
            GLog.d(TAG, "dispatchLongClick, Does not meet the drag sharing conditions!")
            return false
        }

        val selectedCount = baseListViewModel?.getSelectedItemCount() ?: 0
        if (!DragHelper.checkMediaItemLimit(activity, selectedCount)) {
            GLog.d(TAG, "dispatchLongClick, Exceeded the limit of number of shares!")
            return true
        }
        context?.let { VibratorUtils.vibrate(it, VibratorUtils.EffectType.VIBRATE_TYPE_WEAK) }
        AppScope.launch(Dispatchers.IO) {
            val dragData = DragData().apply {
                (baseListViewModel as? AlbumViewModel)?.let { albumViewModel ->
                    pathList = albumViewModel.getSelectedItems().toList()
                    bitmapList = albumViewModel.loadSelectedBitmap(position)
                }
            }

            DragHelper.downloadOriginalPhoto(activity, <EMAIL>, dragData.pathList) {
                /**
                 * 浮窗，分屏获取和图集详情四列的时候的item一样的高度（相当于固定高），否则是slot的高度
                 */
                context?.let {
                    val dragItemWidth = if (isFloatingWindowMode() || getCurrentAppUiConfig().isInMultiWindow.current) {
                        DragHelper.getDragItemDefaultWidth(it)
                    } else {
                        layoutDetail.itemWidth
                    }
                    DragOptions.isSupportDropToGallery = false
                    if (supportDragAnimation()) {
                        activity?.let { activity ->
                            dragDropAnimationHelper = DragDropAnimationHelper(
                                activity,
                                dragData,
                                dragItemWidth,
                                getRectByIndexCallback
                            )
                            dragDropAnimationHelper?.isSupportDropAnimation = true
                        }
                    }
                    activity?.window?.let { window ->
                        DragOptions.startMediaDrag(window, isFloatingWindowMode(), dragData, dragItemWidth, this@BaseAlbumFragment)
                    }
                }
            }
        }
        return true
    }

    override fun onDragStart(dragStartX: Float, dragStartY: Float) {
        if (this.isDetached) {
            GLog.w(TAG, "onDragStart fragment isDetached")
            return
        }
        receiveDrop = false
        dragDropAnimationHelper?.takeIf { supportDragAnimation() && it.isSupportDropAnimation }?.apply {
            lifecycleScope.launch(Dispatchers.IO) {
                baseListViewModel?.let { viewModel ->
                    val selectAllBitmapList =
                        (viewModel as AlbumViewModel).loadAllSelectedBitmapFromCache(dragData.bitmapList).map {
                            Pair(viewModel.getItemIndexByPath(it.first), it)
                        }
                    withContext(Dispatchers.UI) {
                        setDragSourceList(selectAllBitmapList)
                        startTogetherAnim(dragStartX, dragStartY)
                    }
                }
            }
        }
    }

    override fun onDragEnd(result: Boolean, dragStartX: Float, dragStartY: Float) {
        if (this.isDetached || (activity?.isDestroyed != false) || (activity?.isFinishing != false)) {
            GLog.w(TAG, "onDragEnd fragment isDetached")
            return
        }
        GLog.i(TAG, "onDragEnd. $result")
        DragOptions.clearDragListener(activity?.window)
        // 当前页面没有收到drop并且结果为false，提示拖动到的页面不支持
        if (!receiveDrop && !result) {
            ToastUtil.showShortToast(BasebizR.string.drag_currentpage_not_support)
        }
        DragOptions.isSupportDropToGallery = true
        if (supportDragAnimation()) {
            dragDropAnimationHelper?.isSupportDropAnimation = false
        }
        val listViewVmRef = WeakReference(baseListViewModel)
        TrackScope.launch {
            val bundle = listViewVmRef.get()?.getSelectedItemImageAndVideoCountAsync()
            FloatingWindowTrackHelper.trackDragShare(
                bundle?.getInt(Constants.SpecifiedCount.KEY_IMAGE_COUNT),
                bundle?.getInt(Constants.SpecifiedCount.KEY_VIDEO_COUNT),
                FloatingWindowTrackConstant.Value.DRAG_PAGE_LIST_PAGE,
                FloatingWindowTrackConstant.Value.DRAG_SOURCE_ALBUM,
                if (result) FloatingWindowTrackConstant.Value.SHARE_RESULT_SUCCESS else FloatingWindowTrackConstant.Value.SHARE_RESULT_FAILED
            )
        }
    }

    override fun onDrop(dragStartX: Float, dragStartY: Float) {
        if (this.isDetached) {
            GLog.w(TAG, "onDrop fragment isDetached")
            return
        }
        receiveDrop = true
        GLog.d(TAG, "onDrop.dragStartX= $dragStartX,dragStartY=$dragStartY")
        dragDropAnimationHelper?.takeIf { supportDragAnimation() && it.isSupportDropAnimation }
            ?.startTogetherReverseAnim(dragStartX, dragStartY)
    }

    override fun onDragLocation(locationX: Float, locationY: Float) {
        if (this.isDetached) {
            GLog.w(TAG, "onDragLocation fragment isDetached")
            return
        }
        dragDropAnimationHelper?.takeIf { supportDragAnimation() && it.isSupportDropAnimation }?.apply { setDragLocation(locationX, locationY) }
    }

    override fun onDragExit() {
        dragDropAnimationHelper?.stopAnim()
    }

    private fun supportDragAnimation(): Boolean {
        return DragHelper.supportDragAnimation(isFloatingWindowMode())
    }

    private val getRectByIndexCallback: (Context, List<Int>) -> List<Rect> = { _, indexList ->
        val resultList = ArrayList<Rect>()
        for (i in indexList.indices) {
            val rect = getItemRect(indexList[i], false)
            resultList.add(rect)
        }
        resultList
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == SEARCH_REQUEST_CODE) {
            if (isFromMapPage()) {
                // 从搜索页退回时，去除alpha动画，避免闪现地图
                activity?.overridePendingTransition(
                    BasebizR.anim.business_lib_close_slide_enter_no_alpha,
                    couiR.anim.coui_close_slide_exit
                )
            }
            return
        }
        bottomMenuHelper.handleOperationResult(
            requestCode,
            resultCode,
            data?.apply {
                putExtra(IntentUtils.NAVIGATE_UP_TITLE_TEXT, toolbar?.title)
            }
        )
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        refreshFootShow()
    }

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        super.onSystemBarChanged(windowInsets)
        refreshFootShow()
        // 上下分屏，且相册在上方，此时进行横竖屏切换，虚拟手势条会发生从无到有的变化，需要更新 recyclerView 的 bottomPadding
        updateRecyclerViewBottomPadding(bottomMenuHeight)
    }

    protected fun refreshFootShow() {
        if (showSlotFooter()) {
            slotFooterViewDataBinding?.refreshLayout()
        }
    }

    /**
     * 设置系统栏。
     * @param isVisible 系统栏的可见性
     */
    protected fun setupSystemBar(isVisible: Boolean) {
        if (isVisible) {
            showStatusBar()
            showNaviBar()
        } else {
            hideStatusBar()
            hideNaviBar()
        }
    }

    private fun getSafeBottomMenuHelper(): BottomMenuHelper? = if (this::bottomMenuHelper.isInitialized) bottomMenuHelper else null

    /**
     * 是否从地图页跳转至该页面
     */
    private fun isFromMapPage(): Boolean = FROM_MAP_PAGE == fromPage

    /**
     *从侧边栏列表中点击,跳转至该页面,该页面作为一级页面
     */
    private fun isFromSidePanePage(): Boolean = FROM_SIDE_PANE_PAGE == fromPage

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        GLog.d(TAG, "onSidePaneSlideStart: isResumed:$isResumed newState=$newState $clazzSimpleName")
        if (isResumed && newState.isFlat()) {
            sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint() || currentOverScrollImmersive)
            val currentSpanCount = getListSpanCount()
            context?.let {
                refreshLayoutManager(it)
            }
            sidePaneAnim?.startAnimation(
                SidePaneAnimationConfig(
                    currentSpanCount = currentSpanCount,
                    nextSpanCount = getListSpanCount(),
                    currentItemWidth = getListItemWidth(),
                    nextItemWidth = layoutDetail.itemWidth,
                    slideWidth = sidePaneAccessor.getSlideWidth(),
                    gapWidth = resources.getDimensionPixelOffset(BasebizR.dimen.base_album_fragment_item_view_horizontal_gap),
                    newState = newState
                )
            )
        }
    }

    private fun getListSpanCount(): Int = (recyclerView?.layoutManager as? GridLayoutManager)?.spanCount ?: 1

    protected open fun getListItemWidth(): Int {
        return (recyclerView?.layoutManager as? GridLayoutManager)?.getChildAt(0)?.width ?: 0
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        sidePaneAnim?.animating(newState, slideProgress)
    }

    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        sidePaneAnim?.endAnimation(newState)
        if (!isResumed && newState.isFlat()) {
            context?.let {
                refreshLayoutManager(it)
            }
        }
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : BaseListSystemBarStyle(true) {
        override fun isCanUpdateStatusAndSideIcon(): Boolean = personalFilterViewWrapper.isVisible().not() && isInBottomOverScrolling()
    }

    /**
     * 查找当前图集是否在不显示在照片页表中
     */
    private fun isAlbumHideOnTimeLinePage(): Boolean {
        var isAlbumHide: Boolean = false
        albumViewData?.albumId?.let {
            isAlbumHide = AlbumHideOnTimeLinePageHelper.findAlbumHideOnTimeLinePageItem(it)
        }
        return isAlbumHide
    }

    /**
     * 不显示在照片页操作完成后回调，刷新不显示在照片页菜单项的选中状态
     */
    private fun onReceiveAlbumHideOnTimeLinePageResult(resultCode: String, resultMap: Map<String, Any>?) {
        if (MenuAction.RESULT_SUCCESS == resultCode) {
            val isAlbumHide = isAlbumHideOnTimeLinePage()
            lifecycleScope.launch(Main, CoroutineStart.DEFAULT) {
                albumHideOnTimeLinePageMenuItem?.setChecked(isAlbumHide)
                ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.base_album_hide_on_time_line_page_success)
            }
        }
        GLog.d(
            TAG, LogFlag.DL, "onReceiveAlbumHideOnTimeLinePageResult : ${MenuAction.ALBUM_HIDE_ON_TIME_LINE_PAGE} on complete, resultCode:"
                    + resultCode + ", resultMap:" + resultMap
        )
    }

    /**
     * 显示在照片页操作完成后回调，刷新不显示在照片页菜单项的选中状态
     */
    private fun onReceiveAlbumShowOnTimeLinePageResult(resultCode: String, resultMap: Map<String, Any>?) {
        if (MenuAction.RESULT_SUCCESS == resultCode) {
            val isAlbumHide = isAlbumHideOnTimeLinePage()
            lifecycleScope.launch(Main, CoroutineStart.DEFAULT) {
                albumHideOnTimeLinePageMenuItem?.setChecked(isAlbumHide)
                ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.base_album_hide_on_time_line_page_cancel)
            }
        }
        GLog.d(
            TAG, LogFlag.DL, "onReceiveAlbumShowOnTimeLinePageResult : ${MenuAction.ALBUM_SHOW_ON_TIME_LINE_PAGE} on complete, resultCode:"
                    + resultCode + ", resultMap:" + resultMap
        )
    }

    /**
     * 当前图集是否显示「不显示在照片页」菜单项
     * @return Boolean true:显示 false:不显示
     */
    private fun isShowAlbumHideOnTimeLinePageMenuItem(): Boolean {
        val bucketPath = albumViewData?.supportedAbilities?.getString(SlotOverlayHelper.SUPPORT_BUCKET_PATH)
        val haveBucketPath = !TextUtils.isEmpty(bucketPath)
        val isVirtualAlbum = albumViewData?.supportedAbilities?.getBoolean(AlbumViewData.SUPPORT_IS_VIRTUAL_ALBUM) ?: false
        val isCardCaseAlbum = TextUtils.equals(TYPE_CARD_CASE_ALBUM, albumViewData?.modelType)
        return haveBucketPath && (albumViewData?.albumId != INVALID_ALBUM_ID) && !isVirtualAlbum && !isCardCaseAlbum
    }

    companion object {
        const val INVALID_INDEX = -1

        const val KEY_ALBUM_MODEL_BUNDLE = "albumModelBundle.key"
        private const val TAG = "AlbumFragment"
        private const val SEARCH_REQUEST_CODE = 1234
        /**本地测试的经验值:当图集数量超过30时,低端机上进入图集会卡顿,故超过30时,延迟刷新RV,避免卡顿*/
        const val DELAY_REGISTER_OVER_COUNT = 30

        const val CACHE_ABILITY = 1
        const val RESOURCE_ABILITY = 2

        /**
         *  增加RV的缓存大小,避免自动滚动划选,预取item时inflate耗时
         *  RV_CACHE_POOL_SIZE: RecyclerView(简称RV)的通用缓存,按itemType存储,默认每种type最大存储5个
         *
         *  RV_CACHE_VIEW_SIZE: RV的itemView缓存,不区分itemType,统一存储,保存刚移除屏幕的viewholder,按position查找
         */
        private const val RV_CACHE_POOL_SIZE = 32
        private const val RV_CACHE_VIEW_SIZE = 12
        private const val DELETE_PHOTO_FLAG_CLEAR_DELAY = 1000L

        private const val ALPHA_255 = 255
    }
}