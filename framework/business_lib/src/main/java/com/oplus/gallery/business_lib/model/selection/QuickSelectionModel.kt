/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - QuickSelectionModel.kt
 * Description:
 * Version: 1.0
 * Date: 2020/09/28
 * Author: Wangrunxin@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Wangrunxin@Apps.Gallery3D      2020/09/28      1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.selection

import androidx.annotation.VisibleForTesting
import com.oplus.gallery.foundation.util.debug.GLog
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.concurrent.read
import kotlin.concurrent.write

abstract class QuickSelectionModel<Data>(selectionManager: SelectionManager<Data>) : SelectionModel<Data>(selectionManager) {

    @VisibleForTesting
    @Volatile
    var isSelectAlling: Boolean = false

    @VisibleForTesting
    val selectJobUnSelects = mutableSetOf<Data>()

    @VisibleForTesting
    @Volatile
    var isUnSelectAlling: Boolean = false

    @VisibleForTesting
    val unSelectJobSelects = mutableSetOf<Data>()

    @VisibleForTesting
    var selectJob: Job? = null

    private var submitJob: (() -> Unit)? = null

    override fun selectItem(position: Int, data: Data, needNotify: Boolean) {
        val result = lock.write {
            if (isSelectAlling && selectionData?.isItemSelected(data) != true) {
                selectJobUnSelects.remove(data)
            } else if (isUnSelectAlling && selectionData?.isItemSelected(data) != true) {
                unSelectJobSelects.add(data)
            } else {
                selectionData?.selectItem(data, needNotify) ?: false
            }
        }
        if (result) {
            onSelectionListeners.forEach { it.onSelectItemChange(position, true) }
        }
    }

    override fun unSelectItem(position: Int, data: Data, needNotify: Boolean) {
        val result = lock.write {
            if (isSelectAlling && selectionData?.isItemSelected(data) != true) {
                selectJobUnSelects.add(data)
            } else if (isUnSelectAlling && selectionData?.isItemSelected(data) != true) {
                unSelectJobSelects.remove(data)
            } else {
                selectionData?.unSelectItem(data, needNotify) ?: false
            }
        }
        if (result) {
            onSelectionListeners.forEach { it.onSelectItemChange(position, false) }
        }
    }

    override fun selectAll(getAll: suspend () -> Set<Data>) {
        lock.write {
            isSelectAlling = false
            isUnSelectAlling = false

            submitJob?.invoke()
            submitJob = null

            isSelectAlling = true

            selectJobUnSelects.clear()
        }
        onSelectionListeners.forEach { it.onSelectionChange(state = NotifyState.PREVIEW) }
        selectJob?.cancel()
        selectJob = launch {
            val allPaths = if (isActive) getAll() else emptySet()
            GLog.i(TAG, "getAllPath, $isActive, count:${allPaths.size}")
            lock.write {
                if (isActive) {
                    selectionData?.selectItems(allPaths - selectJobUnSelects)
                }
                isSelectAlling = false

                submitJob?.invoke()
                submitJob = null
            }
        }
    }

    override fun unSelectAll(getAll: suspend () -> Set<Data>) {
        lock.write {
            isUnSelectAlling = false
            isSelectAlling = false

            submitJob?.invoke()
            submitJob = null

            isUnSelectAlling = true

            unSelectJobSelects.clear()
        }
        onSelectionListeners.forEach { it.onSelectionChange(state = NotifyState.PREVIEW) }
        selectJob?.cancel()
        selectJob = launch {
            val allPaths = if (isActive) getAll() else emptySet()
            GLog.i(TAG, "getAllPath, count:${allPaths.size}")
            lock.write {
                if (isActive) {
                    selectionData?.unSelectItems(allPaths - unSelectJobSelects)
                }
                isUnSelectAlling = false

                submitJob?.invoke()
                submitJob = null
            }
        }
    }

    override fun isItemSelected(data: Data): Boolean {
        return lock.read {
            if (isSelectAlling) {
                !selectJobUnSelects.contains(data)
            } else if (isUnSelectAlling) {
                GLog.d(TAG, "isItemSelected: isUnSelectAlling")
                unSelectJobSelects.contains(data)
            } else {
                selectionData?.isItemSelected(data) ?: false
            }
        }
    }

    override fun getSelectedItemCount(totalCount: Int, immutable: Boolean): Int {
        return lock.read {
            if (isSelectAlling) {
                totalCount - selectJobUnSelects.size
            } else if (isUnSelectAlling) {
                totalCount - unSelectJobSelects.size
            } else {
                selectionData?.getSelectedItemCount(immutable) ?: 0
            }
        }.apply {
            if (this > 0) {
                GLog.i(TAG, "getSelectedItemCount, selectionData getSelectedItemCount: $this")
            }
        }
    }

    override fun submit(completedHandle: (Set<Data>) -> Unit) {
        GLog.d(TAG, "submit")
        lock.read {
            if (isSelectAlling || isUnSelectAlling) {
                submitJob = {
                    super.submit(completedHandle)
                }
            } else {
                super.submit(completedHandle)
            }
        }
    }

    companion object {
        private const val TAG = "QuickSelectionModel"
    }
}