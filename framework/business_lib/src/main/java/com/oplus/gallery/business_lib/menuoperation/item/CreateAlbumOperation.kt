/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CreateAlbumOperation.kt
 ** Description: 菜单操作：新建图集
 **
 **
 ** Version: 1.0
 ** Date:2020/11/06
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/11/06	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.snackbar.COUISnackBar
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PANEL_DIALOG
import com.oplus.gallery.basebiz.helper.StorageTipsHelper
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.menuoperation.CreateAlbumAction
import com.oplus.gallery.business_lib.menuoperation.CreateAlbumAction.Companion.KEY_CURRENT_BUCKETID
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.business_lib.menuoperation.helper.CreateAlbumHelper
import com.oplus.gallery.business_lib.menuoperation.helper.CreateAlbumHelper.CreateAlbumEditDialogListener
import com.oplus.gallery.business_lib.menuoperation.helper.LoadingProgressDialogHelper
import com.oplus.gallery.business_lib.menuoperation.helper.RenameAlbumHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaAlbum
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local
import com.oplus.gallery.business_lib.model.data.utils.TypeFilterUtils
import com.oplus.gallery.business_lib.selectionpage.KEY_CREATE_ALBUM_NAME
import com.oplus.gallery.business_lib.selectionpage.KEY_DOWNLOAD_ORIG_FAIL
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA_LIST
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_OP_TYPE
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.selectionpage.SelectType
import com.oplus.gallery.business_lib.selectionpage.SelectionFrom
import com.oplus.gallery.business_lib.selectionpage.TYPE_COPY
import com.oplus.gallery.business_lib.selectionpage.TYPE_MOVE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CREATE_SELF_ALBUM_COPY
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CREATE_SELF_ALBUM_MOVE
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.MenuParamKey.KEY_TARGET_ALBUM_SET_NAME
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_CANCEL
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_CREATE_ALBUM
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_FAILED_PARAM_NO_CONTEXT
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_FAILED_PARAM_NO_DATA
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_GIF
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_OLIVE
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_PIC
import com.oplus.gallery.foundation.tracing.constant.UserAssetsTrackConstant.Key.RES_TYPE_VIDEO
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper.Scene
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_MODE_INCR
import com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_TYPE_CREATE_ALBUM
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.LoggingExceptionHandler
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter.context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.Calendar
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 菜单操作：新建图集
 */
class CreateAlbumOperation(
    isRunOnMain: Boolean,
    paramMap: Map<String, Any?>,
    onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)?
) : MenuOperation<Unit>(isRunOnMain, paramMap, onCompleted) {

    companion object {
        const val TAG = "CreateAlbumOperation"
        const val KEY_CREATE_ALBUM_OPERATION = "createAlbumOperation.requestKey"
    }

    private lateinit var currentFragmentRef: WeakReference<Fragment>
    private var targetFolderName: String? = null
    private var targetCustomAlbumName: String? = null
    private var isSelfAlbumExisted = false
    private var pathList = ArrayList<Path>()
    private var createAlbumDialogHelper: CreateAlbumHelper? = null
    private var selectType = SelectType.ALL
    private var addOperation: FileOperation? = null
    private var copyLoadingDialog: LoadingProgressDialogHelper? = null
    private val storageDialog: StorageTipsHelper = StorageTipsHelper()
    private var snackBar: COUISnackBar? = null

    override fun onCheckAndPrepare(): Boolean {
        (paramMap[CreateAlbumAction.KEY_FRAGMENT_REF] as? WeakReference<Fragment>)?.let {
            currentFragmentRef = it
        } ?: run {
            GLog.e(TAG, "onCheckAndPrepare, fragmentRef = null")
            setResult(MenuAction.RESULT_ERROR_NO_DATA, trackResult = TrackResult(VALUE_FAILED_PARAM_NO_CONTEXT))
            return false
        }
        targetFolderName = paramMap[CreateAlbumAction.KEY_TARGET_ALBUM_NAME] as? String
        val mineType = paramMap[CreateAlbumAction.KEY_MIME_TYPE] as? String
        selectType = when (mineType) {
            MimeTypeUtils.MIME_TYPE_IMAGE_ANY -> SelectType.IMAGE
            MimeTypeUtils.MIME_TYPE_VIDEO_ANY -> SelectType.VIDEO
            else -> SelectType.ALL
        }
        return true
    }

    override fun onRun() {
        currentFragmentRef.get()?.activity?.let { activity ->
            targetFolderName?.let {
                isSelfAlbumExisted = true
                goSelectionPage(activity.supportFragmentManager)
            } ?: run {
                createAlbumDialogHelper = CreateAlbumHelper(
                    activity,
                    object : CreateAlbumEditDialogListener {
                        override fun editSuccess(folderName: String, customAlbumName: String?) {
                            if (TextUtils.isEmpty(folderName)) {
                                GLog.e(TAG, "editSuccess, text = null")
                                return
                            }
                            targetFolderName = folderName
                            targetCustomAlbumName = customAlbumName
                            goSelectionPage(activity.supportFragmentManager)
                            createAlbumDialogHelper = null
                        }

                        override fun editCancel() {
                            createAlbumDialogHelper = null
                        }
                    }
                )
                createAlbumDialogHelper?.show()
            }
        } ?: setResult(MenuAction.RESULT_ERROR_NO_DATA, trackResult = TrackResult(VALUE_FAILED_PARAM_NO_CONTEXT))
    }

    private fun goSelectionPage(fm: FragmentManager) {
        Starter.DialogFragmentStarter<PanelDialog>(
            fm = fm,
            bundle = SelectInputData(
                selectMulti = true,
                selectType = selectType,
                fromWhere = getFromWhere()
            ).createBundle().apply {
                putString(KEY_REQUEST_KEY, KEY_CREATE_ALBUM_OPERATION)
                putString(KEY_CREATE_ALBUM_NAME, targetCustomAlbumName)
            },
            postCard = PostCard(SELECTION_PANEL_DIALOG),
        ).start()?.apply {
            setFragmentResultListenerSafety(KEY_CREATE_ALBUM_OPERATION) { _, bundle ->
                onSelectionFinished(bundle)
            }
        }
    }

    private fun getFromWhere() = SelectionFrom.CREATE_ALUM

    override fun cancel() {
        // do nothing
    }

    private fun notifyCloudSync() {
        ApiDmManager.getCloudSyncDM().triggerCloudSync(SYNC_MODE_INCR, SYNC_TYPE_CREATE_ALBUM)
    }

    @Suppress("LongMethod")
    private fun onSelectionFinished(resultBundle: Bundle?) {
        resultBundle ?: run {
            setResult(
                MenuAction.RESULT_FAILED,
                trackResult = TrackResult(VALUE_FAILED_PARAM_NO_DATA, "cancel")
            )
            return
        }
        if (resultBundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) {
            val downloadOrigFail = resultBundle.getBoolean(KEY_DOWNLOAD_ORIG_FAIL, false)
            if (downloadOrigFail) {
                GLog.w(TAG, "onSelectionFinished: download fail")
                ToastUtil.showShortToast(BasebizR.string.base_copy_to_down_fail)
            }
            GLog.w(TAG, "onSelectionFinished: cancel select items.")
            setResult(
                MenuAction.RESULT_CANCEL,
                trackResult = TrackResult(VALUE_CANCEL, "cancel")
            )
            return
        }
        val stringPathList = resultBundle.getStringArray(KEY_RESULT_DATA_LIST) ?: emptyArray<String>()
        pathList.addAll(stringPathList.map(Path::fromString))
        val activity = currentFragmentRef.get()?.activity ?: run {
            GLog.w(TAG, "onSelectionFinished: activity is null")
            setResult(
                MenuAction.RESULT_CANCEL,
                trackResult = TrackResult(VALUE_CANCEL, "cancel")
            )
            return
        }
        val view = currentFragmentRef.get()?.view ?: run {
            GLog.w(TAG, "onSelectionFinished: view is null")
            setResult(
                MenuAction.RESULT_CANCEL,
                trackResult = TrackResult(VALUE_CANCEL, "cancel")
            )
            return
        }
        val bucketId = paramMap[KEY_CURRENT_BUCKETID]?.toString()
        val opType = resultBundle.getInt(KEY_RESULT_OP_TYPE, TYPE_MOVE)
        if (opType == TYPE_COPY) {
            addOperation = object : CopyFileOperation() {
                override fun notifyCloudSync() {
                    <EMAIL>()
                }
            }
            doOperation(activity, view, BasebizR.string.base_copying, {
                addOperation?.addToAlbum(pathList, targetFolderName, bucketId = bucketId) { progress ->
                    handleProgressData(progress)
                }
            }, { resultEntry ->
                handleCopyResult(activity, resultEntry)
                trackCreateSelfAlbumOperation(resultEntry, ALBUMS_ACTION_CREATE_SELF_ALBUM_COPY)
            }, opType)
        } else {
            addOperation = object : MoveFileOperation() {
                override fun notifyCloudSync() {
                    <EMAIL>()
                }
            }
            doOperation(activity, view, BasebizR.string.base_virtual_set_move, {
                addOperation?.addToAlbum(pathList, targetFolderName, bucketId = bucketId)
            }, { resultEntry ->
                handleMoveResult(activity, resultEntry)
                trackCreateSelfAlbumOperation(resultEntry, ALBUMS_ACTION_CREATE_SELF_ALBUM_MOVE)
            }, opType)
        }
        setResult(
            resultMap = mapOf<String, Any>(
                AlbumsActionTackConstant.Key.ALBUMS_ACTION_ALBUM_NAME_KEY to (targetFolderName ?: TextUtil.EMPTY_STRING)
            )
        )
    }

    private fun trackCreateSelfAlbumOperation(resultEntry: ResultEntry, addType: String) {
        if (resultEntry.resultCode == ResultEntry.SUCCESS) {
            val resCntMap = mutableMapOf<String, String>()
            val videoCount = TypeFilterUtils.getSelectedVideoCount(pathList)
            val imageCount = pathList.size - videoCount
            resCntMap[RES_TYPE_PIC] = imageCount.toString()
            resCntMap[RES_TYPE_VIDEO] = videoCount.toString()
            resCntMap[RES_TYPE_OLIVE] = TypeFilterUtils.getSelectedOliveImageCount(pathList).toString()
            resCntMap[RES_TYPE_GIF] = TypeFilterUtils.getSelectedGifImageCount(pathList).toString()
            targetCustomAlbumName?.let {
                AlbumsActionTrackHelper.trackCreateSelfAlbum(JsonUtil.toJson(resCntMap), addType, it)
            }
        }
    }

    private fun handleMoveResult(context: Context, resultEntry: ResultEntry) {
        when (resultEntry.resultCode) {
            ResultEntry.SUCCESS -> {
                if (isSelfAlbumExisted) {
                    ToastUtil.showShortToast(BasebizR.string.base_move_to_success)
                }
            }
            ResultEntry.ALL_FILE_FILTERED ->
                ToastUtil.showShortToast(BasebizR.string.base_move)
            ResultEntry.USER_CANCEL -> {
                //与交互确认用户取消移动不弹出任何提示就行
                GLog.d(TAG, LogFlag.DL, "[handleMoveResult] user cancel move")
            }
            ResultEntry.SOME_FILE_FILTERED -> {
                resultEntry.filterCount.apply {
                    val tip = context.resources.getQuantityString(BasebizR.plurals.base_move_image_filter_failed_tip, this, this)
                    ToastUtil.showShortToast(tip)
                }
            }
            ResultEntry.SOME_FILE_FAILED ->
                ToastUtil.showShortToast(BasebizR.string.base_move_to_fail)
            ResultEntry.STORAGE_NOT_ENOUGH ->
                StorageTipsHelper.show(context, resultEntry.storageType, resultEntry.state)
        }
    }

    override fun getMenuTypeForTrack(): String = VALUE_CREATE_ALBUM

    override fun getCustomParamForTrack() = HashMap<String, String?>().apply {
        put(KEY_TARGET_ALBUM_SET_NAME, targetFolderName)
    }

    override fun getImageAndVideoCountForTrack() = getImageAndVideoCount(pathList)

    override fun onReceiveEvent(eventId: Int) {
        when (eventId) {
            EventNotifier.EVENT_ID_SCREEN_CHANGED -> createAlbumDialogHelper?.updateDialogIfNeed()
        }
    }

    private fun doOperation(
        activity: FragmentActivity,
        view: View,
        titleId: Int,
        fileOperation: () -> ResultEntry?,
        handleResult: (ResultEntry) -> Unit,
        opType: Int
    ) {
        activity.lifecycleScope.launch(Dispatchers.UI + LoggingExceptionHandler) {
            createLoadingAndShowDialog(activity, view, titleId, opType)
            val resultEntry = withContext(Dispatchers.CPU) {
                fileOperation.invoke()
            }
            GLog.d(TAG, "doOperation, resultEntry:$resultEntry")
            dismissAndHandleResult(resultEntry, handleResult, opType)
            val currentTargetFolderName = targetFolderName
            if (!isSelfAlbumExisted && (currentTargetFolderName != null)) {
                val folderPath = Dir.MYALBUMS.makeBucketPath(currentTargetFolderName)
                val bucketIds = FilePathUtils.getBucketIds(folderPath)
                RenameAlbumHelper.doRename(bucketIds, folderPath, targetCustomAlbumName, Calendar.getInstance().timeInMillis)
                withContext(Dispatchers.IO) {
                    if (bucketIds.isNotEmpty()) {
                        val updateToCloudAlbumPath = Local.PATH_ALBUM_ANY_ALL.toString() + "/" + bucketIds[0]
                        val updateToCloudMediaSet = Path.fromString(updateToCloudAlbumPath).`object` as? MediaAlbum
                        updateToCloudMediaSet?.let {
                            updateToCloudMediaSet.reload()
                            ApiDmManager.getCloudSyncDM().updateAlbumDisplayName(updateToCloudMediaSet)
                        } ?:  GLog.e(TAG, "doOperation: cast exception ,updateToCloudMediaSet is null")
                    }
                }
            }
        }
    }

    private fun createLoadingAndShowDialog(activity: Activity, view: View, titleId: Int, opType: Int) {
        when (opType) {
            TYPE_COPY -> {
                if (copyLoadingDialog == null) {
                    copyLoadingDialog = LoadingProgressDialogHelper(activity).apply {
                        setTitle(titleId)
                        setCancelListener {
                            copyLoadingDialog?.dismissDialog()
                            addOperation?.operationCancel()
                        }
                    }
                }
                copyLoadingDialog?.showDialog(LoadingProgressDialogHelper.DIALOG_SHOW_TIMEOUT)
            }
            TYPE_MOVE -> {
                //make 前必须先dismiss , 防止添加多个
                snackBar?.dismiss()
                snackBar = COUISnackBar.make(
                    view,
                    context.resources.getString(BasebizR.string.base_virtual_set_move),
                    MoveToOperation.MOVE_TO_TIME
                ).apply {
                    val imageView = findViewById<ImageView>(com.support.snackbar.R.id.iv_snack_bar_icon)
                    val text = context.resources.getString(BasebizR.string.common_cancel)
                    val setButtonClickListener: View.OnClickListener = View.OnClickListener {
                        addOperation?.operationCancel()
                    }
                    setOnAction(text, setButtonClickListener)
                    BottomMenuHelper.updateSnackBar(context, imageView)
                    setOnStatusChangeListener(object : COUISnackBar.OnStatusChangeListener {
                        override fun onShown(couiSnackBar: COUISnackBar?) {
                            //把资源文件放进Animation的对象中开始转圈
                            BottomMenuHelper.startMovingAnimation(context, imageView)
                        }
                        override fun onDismissed(couiSnackBar: COUISnackBar?) {
                            snackBar?.dismiss()
                            BottomMenuHelper.removeMovingAnimation(imageView)
                            addOperation?.operationCancel()
                        }
                    })
                    show()
                }
            }
        }
    }

    private fun dismissAndHandleResult(resultEntry: ResultEntry?, handleResult: (ResultEntry) -> Unit, opType: Int) {
        when (opType) {
            TYPE_COPY -> {
                if (copyLoadingDialog?.isShowing() == true) {
                    delayDismissAndSetFinishLister {
                        resultEntry?.let(handleResult)
                    }
                } else {
                    copyLoadingDialog?.dismissDialog()
                    resultEntry?.let(handleResult)
                }
            }
            TYPE_MOVE -> {
                snackBar?.dismiss()
                snackBar?.setOnStatusChangeListener(null)
                resultEntry?.let(handleResult)
            }
        }
    }

    private fun delayDismissAndSetFinishLister(finishListener: (() -> Unit)?) {
        copyLoadingDialog?.setFinishListener(finishListener)
        copyLoadingDialog?.dismissDialog(true)
    }

    private fun handleProgressData(progress: Int) {
        copyLoadingDialog?.updateDialogProgress(progress)
    }

    private fun handleCopyResult(context: Context, resultEntry: ResultEntry, isVideoFiltered: Boolean = false) {
        when (resultEntry.resultCode) {
            ResultEntry.SUCCESS -> {
                if (isVideoFiltered) {
                    ToastUtil.showShortToast(
                        context.resources.getQuantityString(BasebizR.plurals.base_photo_copy_success_video_cannot_copy, resultEntry.succeedCount)
                    )
                } else if (isSelfAlbumExisted) {
                    ToastUtil.showShortToast(BasebizR.string.base_copy_success)
                }
            }
            ResultEntry.SOME_FILE_FILTERED -> {
                val tip = context.resources.getString(BasebizR.string.base_copy_image_filter_failed_tip)
                ToastUtil.showShortToast(tip)
            }
            ResultEntry.IS_ALL_SAME -> {
                val tip = context.resources.getQuantityString(BasebizR.plurals.base_item_already_exist, resultEntry.sameCount)
                ToastUtil.showShortToast(tip)
            }
            ResultEntry.STORAGE_NOT_ENOUGH -> {
                if (resultEntry.failedCount > 0) {
                    copyLoadingDialog?.dismissDialog()
                }
                if (resultEntry.state == StorageLimitHelper.State.NO_SPACE) {
                    storageDialog.show(context, resultEntry.storageLimitCnt, resultEntry.storageLimitSize, Scene.CREATE_ALBUM)
                }
            }
            ResultEntry.USER_CANCEL -> {
                val tip = context.resources.getQuantityString(BasebizR.plurals.base_copy_success_item,
                    resultEntry.succeedCount, resultEntry.succeedCount)
                ToastUtil.showShortToast(tip)
            }
        }
    }
}
