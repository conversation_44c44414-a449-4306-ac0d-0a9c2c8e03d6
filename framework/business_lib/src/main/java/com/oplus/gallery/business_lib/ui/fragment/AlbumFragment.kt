/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AlbumFragment.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/6/22
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery     2021/6/22      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.business_lib.ui.fragment

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_ALBUM_SUPPORT_PERSONAL_FILTER
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_NEED_SCROLL_TO_BOTTOM
import com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_PATH_CHILD_INDEX
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.util.SystemConfigs.isRegionCN
import com.oplus.gallery.business_lib.map.MapAlbumTitleMaker
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local
import com.oplus.gallery.business_lib.model.data.map.MapAlbum
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.AlbumViewModel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_MAP_PAGE
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_PAGE_UNKNOWN
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.KEY_FROM_PAGE
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

@RouterNormal(RouterConstants.RouterName.ALBUM_FRAGMENT)
class AlbumFragment : BaseAlbumFragment() {

    override val supportOneTouchShare = true
    override val isSupportedPersonalFilter: Boolean by lazy {
        arguments?.getBoolean(KEY_ALBUM_SUPPORT_PERSONAL_FILTER, true) ?: true
    }

    /**
     * 用于解析地理位置信息
     */
    private val titleMaker: MapAlbumTitleMaker? by lazy {
        initMapAlbumTitleMaker()
    }

    /**
     * 本次跳转是否源自于地图页
     */
    private val isFromMapPage: Boolean by lazy {
        arguments?.getInt(KEY_FROM_PAGE, FROM_PAGE_UNKNOWN) == FROM_MAP_PAGE
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        needScrollToBottom = arguments?.getBoolean(KEY_NEED_SCROLL_TO_BOTTOM, isPositiveOrder()) ?: isPositiveOrder()
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        reloadTitleIfNeed()
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    override fun onDestroy() {
        super.onDestroy()
        if (isSupportedPersonalFilter.not() || isPersonalFiltered().not()) {
            (baseListViewModel as? AlbumViewModel)?.checkAlbumSetDataSync(albumViewData, firstTotalCount, logTag)
        }
        if (isFromMapPage) {
            titleMaker?.onDestroy()
        }
    }

    /**
     * 如果该页面为地图图集详情页，需要加载地理位置作为图集的标题
     */
    private fun reloadTitleIfNeed() {
        if (isFromMapPage) {
            viewLifecycleOwner.lifecycleScope.launch {
                if (!isAdded) {
                    GLog.w(TAG, LogFlag.DL, "fragment is not added, don't reload title")
                    return@launch
                }
                titleMaker?.let {
                    val pathChildIndex = arguments?.getInt(KEY_PATH_CHILD_INDEX, 0) ?: 0
                    val mapAlbum = getMapAlbum(pathChildIndex)
                    it.setAlbum(mapAlbum)
                    // 跳转到album图集时，加载地点信息作为title
                    it.reload()
                }
            }
        }
    }

    private fun initMapAlbumTitleMaker(): MapAlbumTitleMaker? {
        val baseActivity  = activity as? BaseActivity
        val fragmentRef = WeakReference(this)
        return baseActivity?.let {
            MapAlbumTitleMaker(it).apply {
                setStateListener {
                    GLog.d(TAG, LogFlag.DL, "createTitle and update")
                    val fragment = fragmentRef.get()
                    if (fragment != null && fragment.isAdded) {
                        val title = createTitle(isRegionCN)
                        fragment.updateTitle(title)
                    } else {
                        GLog.w(TAG, LogFlag.DL, "fragment is not added")
                    }
                }
            }
        } ?: run {
            GLog.e(TAG, LogFlag.DL, "activity is null")
            null
        }
    }

    private fun getMapAlbum(pathChildIndex: Int): MapAlbum? {
        return DataManager.getMediaSet(Local.PATH_ALBUM_MAP_ADDRESS.getChild(pathChildIndex)) as? MapAlbum
    }

    /**
     * 刷新图集的标题
     */
    private fun updateTitle(title: String?) {
        GLog.d(TAG, LogFlag.DL, "updateTitle title:$title")
        runOnUiThread {
            title?.takeIf { it.isNotEmpty() }?.let { validTitle ->
                albumViewData?.let {
                    it.title = validTitle
                    refreshToolbar()
                } ?: run {
                    GLog.d(TAG, LogFlag.DL, "albumViewData is null, cannot set title: $validTitle")
                }
            }
        }
    }

    companion object {
        private const val TAG: String = "AlbumFragment"
    }
}
