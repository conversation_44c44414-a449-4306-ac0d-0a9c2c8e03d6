/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditVideoOperation.kt
 ** Description: 菜单操作：编辑（视频）跳转
 **
 ** Version: 1.0
 ** Date:2020/11/3
 ** Author:luyao.Tan@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** luyao.Tan@Apps.Gallery3D   	2020/11/3	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.ColorSpace
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_TYPE_TAG
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_PHOTO_POSITION_FOR_EDITOR
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CHAIN_FROM
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuRequestCode
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.MenuParamKey.KEY_CONTENT_URI
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_EDIT_VIDEO
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_FAILED_PARAM_NO_CONTEXT
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant.Value.VALUE_FAILED_PARAM_NO_DATA
import com.oplus.gallery.foundation.ui.transition.ITransitionBoundsProvider
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Action.ACTION_VIDEO_EDITOR
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_VIDEO_ANY
import com.oplus.gallery.foundation.util.multiprocess.TransBitmapBinder
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.lang.ref.WeakReference

/**
 * 菜单操作：编辑（视频）
 */
class EditVideoOperation(
    isRunOnMain: Boolean,
    paramMap: Map<String, Any?>,
    onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)?
) : MenuOperation<Unit>(isRunOnMain, paramMap, onCompleted) {

    companion object {
        private const val TAG = "EditVideoOperation"
    }

    private lateinit var fragmentRef: WeakReference<Fragment>
    private lateinit var invoker: String
    private lateinit var invokerToken: String
    private lateinit var videoUri: Uri
    private var chainFrom: String? = null
    private var modelType: String? = null
    private var screenOrientation: Int? = null
    private var mediaItemPath: String? = null
    private var thumbnail: Drawable? = null
    private var colorSpace: ColorSpace? = null
    private var photoPositionProvider: ITransitionBoundsProvider? = null
    private var editTypeTag: String? = null

    override fun onCheckAndPrepare(): Boolean = runCatching {
        paramMap.let { map ->
            fragmentRef = map[EditVideoAction.KEY_FRAGMENT] as WeakReference<Fragment>
            invoker = map[EditVideoAction.KEY_INVOKER] as String
            invokerToken = map[EditVideoAction.KEY_INVOKER_TOKEN] as String
            videoUri = map[EditVideoAction.KEY_VIDEO_URI] as Uri
            chainFrom = map[KEY_CHAIN_FROM] as? String
            modelType = map[EditVideoAction.KEY_MEDIA_MODEL_TYPE] as? String
            mediaItemPath = map[EditVideoAction.KEY_MEDIA_ITEM_PATH] as? String
            thumbnail = map[EditVideoAction.KEY_THUMBNAIL] as? Drawable
            screenOrientation = (map[IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION] as? Int)
                ?: ActivityInfo.SCREEN_ORIENTATION_USER
            photoPositionProvider = (map[KEY_PHOTO_POSITION_FOR_EDITOR] as? ITransitionBoundsProvider)
            editTypeTag = map[KEY_EDIT_TYPE_TAG] as? String
        }
    }.onFailure { error ->
        setResult(MenuAction.RESULT_ERROR_NO_DATA, trackResult = TrackResult(VALUE_FAILED_PARAM_NO_DATA))
        GLog.e(TAG, "onCheckAndPrepare :", error)
    }.isSuccess

    override fun onRun() {
        fragmentRef.get()?.let { fragment ->
            val bundle = Bundle().apply {
                thumbnail?.apply {
                    val bitmap = BitmapUtils.drawableToBitmap(this, colorSpace)
                    putBinder(IntentConstant.ViewGalleryConstant.KEY_EDIT_THUMBNAIL, TransBitmapBinder(bitmap))
                }
            }
            Starter.ActivityStarter(
                startContext = fragment,
                bundle = bundle,
                postCard = PostCard(RouterConstants.RouterName.VIDEO_EDITOR_ACTIVITY),
                requestCode = MenuRequestCode.VIDEO_EDIT,
                onIntentCreated = fun(intent: Intent) {
                    intent.apply {
                        action = ACTION_VIDEO_EDITOR
                        setPackage(ContextGetter.context.packageName)
                        putExtra(EditVideoAction.KEY_INVOKER, invoker)
                        putExtra(EditVideoAction.KEY_INVOKER_TOKEN, invokerToken)
                        putExtra(IntentConstant.ViewGalleryConstant.KEY_CHAIN_FROM, chainFrom)
                        putExtra(EditVideoAction.KEY_MEDIA_MODEL_TYPE, modelType)
                        putExtra(EditVideoAction.KEY_MEDIA_ITEM_PATH, mediaItemPath)
                        putExtra(KEY_EDIT_TYPE_TAG, editTypeTag)
                        screenOrientation?.apply {
                            putExtra(IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION, this)
                        }
                        setDataAndType(videoUri, MIME_TYPE_VIDEO_ANY)
                        photoPositionProvider?.apply {
                            putExtra(KEY_PHOTO_POSITION_FOR_EDITOR, this)
                        }
                    }
                }
            ).start()
            setResult()
        } ?: let {
            setResult(MenuAction.RESULT_ERROR_NO_DATA, trackResult = TrackResult(VALUE_FAILED_PARAM_NO_CONTEXT))
            GLog.e(TAG, "onRun :activity is null")
        }
    }

    override fun cancel() {
        // Do nothing
    }

    override fun getMenuTypeForTrack(): String = VALUE_EDIT_VIDEO

    override fun getCustomParamForTrack() = HashMap<String, String?>().apply {
        put(KEY_CONTENT_URI, (paramMap[EditVideoAction.KEY_VIDEO_URI] as? Uri)?.toString())
    }

    override fun getImageAndVideoCountForTrack(): Pair<String, String> = defaultTrackCountPair
}