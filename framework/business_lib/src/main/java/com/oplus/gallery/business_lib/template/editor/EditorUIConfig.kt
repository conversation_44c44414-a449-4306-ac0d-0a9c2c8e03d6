/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorUIConfig.kt
 * Description:
 * Version:
 * Date: 2021/9/28
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2021/9/28     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.template.editor

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.view.View
import android.view.animation.PathInterpolator
import android.widget.LinearLayout
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.business_lib.template.editor.data.ImageViewData
import com.oplus.gallery.business_lib.template.editor.data.TextViewData
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm
import com.oplus.gallery.foundation.ui.systembar.INaviBarController
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppConstants.Number
import com.oplus.gallery.standard_lib.ui.util.UIConfigUtils.getItemMaxConfig
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlin.math.min
import com.oplus.gallery.basebiz.R as BasebizR

object EditorUIConfig {

    private const val PRESS_ANIM_DURATION = 200L
    private const val UP_ANIM_DURATION = 340L
    private const val PRESS_ANIM_INTERPOLATOR_X1 = 0.4f
    private const val PRESS_ANIM_INTERPOLATOR_X2 = 0.2f
    private const val UP_ANIM_INTERPOLATOR_X2 = 0.2f

    private const val PRESS_ANIM_SCALE_NUM_K = 0.000000717772f
    private const val PRESS_ANIM_SCALE_NUM_B = 0.928206f
    private const val PRESS_ANIM_MAX_SCALE = 0.98f
    private const val PRESS_ANIM_MIN_SCALE = 0.93f

    private val pressInterpolator = PathInterpolator(PRESS_ANIM_INTERPOLATOR_X1, 0f, PRESS_ANIM_INTERPOLATOR_X2, 1f)
    private val upInterpolator = PathInterpolator(0f, 0f, UP_ANIM_INTERPOLATOR_X2, 1f)

    private const val TAG = "EditorUIConfig"
    const val DIVIDED_BY_6_NUM = 6
    const val DIVIDED_BY_8_NUM = 8
    const val DIVIDED_BY_12_NUM = 12
    const val TOOLBAR_SCALE_IN_MICRO_WINDOW = 0.9f

    const val CLEAR_COLOR = 0
    const val MAX_COLOR_VALUE = 255
    const val MAX_ALPHA_VALUE = 255
    const val MIN_ALPHA_VALUE = 0

    /**
     * 工具栏区域：toolBar默认的栅格断点值
     */
    private const val TOOLBAR_T1 = 540
    private const val TOOLBAR_T2 = 840
    /**
     * 栅格间距
     */
    private const val GAP = 8
    private const val EDGE_24DP = 24

    /**
     * 工具栏需要占用的栅格数量
     *  适用于：
     *      视频编辑（首页，剪辑，裁剪旋转，主题，滤镜，配乐，特效，倍数，文本）
     *      回忆编辑（首页，主题，音乐，照片）
     * 备注：这里的宽度/高度（最长边）不是窗口的，而是工具栏实际占位view的宽度/高度（最长边）
     * 300(包含)-540  分4行(列)    占4行(列)  间隔8     SPAN_TOOLBAR_SIZE_T0
     * 540(包含)-840  分8行(列)    占6行(列)  间隔8     SPAN_TOOLBAR_SIZE_T1
     * 540(包含)-840  分8行(列)    占8行(列)  间隔8     SPAN_TOOLBAR_SIZE_LAND_T1  针对直板横屏
     * 840及以上      分12行(列)   占8行(列)   间隔8     SPAN_TOOLBAR_SIZE_T2
     */
    private const val SPAN_TOOLBAR_SIZE_T0 = 4
    private const val SPAN_TOOLBAR_SIZE_T1 = 6
    private const val SPAN_TOOLBAR_SIZE_LAND_T1 = 8
    private const val SPAN_TOOLBAR_SIZE_T2 = 8

    /**
     * 栅格数量
     */
    private const val ROW_COLUMN_NUM_T0 = 4
    private const val ROW_COLUMN_NUM_T1 = 8
    private const val ROW_COLUMN_NUM_T2 = 12

    @VisibleForTesting
    val paddingParamPadding: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelOffset(BasebizR.dimen.business_lib_editor_menu_padding)
    }

    @VisibleForTesting
    val paddingParamGap: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelOffset(BasebizR.dimen.business_lib_editor_menu_gap)
    }

    @VisibleForTesting
    val paddingParamMinNoPaddingWidth: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelOffset(BasebizR.dimen.business_lib_editor_menu_need_padding_width)
    }

    /**
     * 编辑模块底部的基础边距
     * 避免没有导航栏和手势指示条时底部标题栏直接贴底
     * 这个边距就是一个手势指示条的高度
     */
    val editorDefaultBottomMargin: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelOffset(BasebizR.dimen.business_lib_editor_menu_default_margin_bottom)
    }

    val videoEditorDefaultImageItemWidth: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_memories_editor_photo_image_item_width)
    }

    val gridWindowDefaultPadding: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_default_padding)
    }

    val windowWidthDividedBy12: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_width_divided_by12)
    }

    val windowWidthDividedBy8: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_width_divided_by8)
    }
    val windowHeightDividedBy12: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_height_divided_by12)
    }

    val windowHeightDividedBy8: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_height_divided_by8)
    }

    /**工具栏栅格断点规则 T0/T1断点 */
    val toolbarGridDividedByT1: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_height_divided_by8)
    }

    /**工具栏栅格断点规则 T1/T2断点 */
    val toolbarGridDividedByT2: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_height_divided_by12)
    }

    val gridWindowGap: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_window_gap)
    }

    val landscapeToolbarMargin: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_landscape_toolbar_margin)
    }

    val gridWindowNoPaddingHeight: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_grid_window_no_padding_height)
    }

    val safeEdgeWidthDefaultLandscape: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_editor_safe_edge_width_default_landscape)
    }

    val safeEdgeWidthTableLandscape: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_editor_safe_edge_width_table_landscape)
    }

    val oldTitleBarHeightDefaultWindow: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_editor_title_bar_height_default_window_old)
    }

    val titleBarHeightDefaultWindow: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_editor_title_bar_height_default_window)
    }

    val titleBarHeightLandDefaultWindow: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_editor_title_bar_land_height_default_window)
    }

    val titleBarHeightMicroWindow: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_editor_title_bar_height_micro_window)
    }

    val titleBarHeightLandMicroWindow: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.business_lib_editor_title_bar_land_height_micro_window)
    }

    val titleBarTopPaddingDefault: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.base_editor_menu_item_icon_padding)
    }

    val titleBarTopPaddingDefaultLandscape: Int by lazy {
        ContextGetter.context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_title_region_padding_top)
    }

    fun getTitleBarHeightDefaultWindow(config: AppUiResponder.AppUiConfig): Int {
        return if (isEditorLandscape(config)) {
            titleBarHeightLandDefaultWindow
        } else {
            titleBarHeightDefaultWindow
        }
    }

    /**
     * 解bug8017218
     * 1.编辑AI功能菜单体验优化需求直接调整了business_lib_editor_title_bar_height_default_window值
     * 2.编辑重构，超级文本还是走的老框架的流程，布局里面的高度没有变化，会出现直接进相册查看图像调整和转文档显示正常
     * 切换了系统分辨率，会触发onUiConfigChanged，重新设置titlebar的高度，改成了52dp，图像调整和转文档显示不全
     *
     * 老框架还需要保持62dp
     */
    fun getTitleBarHeightDefaultWindowOld(config: AppUiResponder.AppUiConfig): Int {
        return if (isEditorLandscape(config)) {
            titleBarHeightLandDefaultWindow
        } else {
            oldTitleBarHeightDefaultWindow
        }
    }

    fun getTitleBarHeightMicroWindow(config: AppUiResponder.AppUiConfig): Int {
        return if (isEditorLandscape(config)) {
            titleBarHeightLandMicroWindow
        } else {
            titleBarHeightMicroWindow
        }
    }

    fun getTitleBarAdjustedHeight(sizeForm: WindowSizeForm, config: AppUiResponder.AppUiConfig): Int {
        return if (sizeForm == WindowSizeForm.MICRO_SIZE) {
            getTitleBarHeightMicroWindow(config)
        } else {
            getTitleBarHeightDefaultWindow(config)
        }
    }

    fun getTitleBarTopPadding(config: AppUiResponder.AppUiConfig): Int {
        return if (isEditorLandscape(config)) {
            titleBarTopPaddingDefaultLandscape
        } else {
            titleBarTopPaddingDefault
        }
    }

    /**
     * 调整边缘安全区域view的宽度以适配不同的窗口尺寸以及导航栏其他
     * 横屏时不同尺寸有不同安全区域尺寸，有导航栏时要用安全区域填充导航栏宽度
     * @param naviBarController 导航条控制器
     * @param leftEdge
     * @param rightEdge
     * @param sizeForm
     */
    fun adjustEdgeViewWidth(
        naviBarController: INaviBarController,
        leftEdge: View,
        rightEdge: View,
        rightMask: View? = null,
        sizeForm: WindowSizeForm
    ): Pair<Int, Int> {
        val safeWidth = if ((sizeForm == WindowSizeForm.STANDARD_LANDSCAPE) || (sizeForm == WindowSizeForm.STANDARD_PORTRAIT_MIDDLE)) {
            safeEdgeWidthDefaultLandscape
        } else {
            safeEdgeWidthTableLandscape
        }

        val newWidthLeft = maxOf(safeWidth, naviBarController.leftNaviBarHeight(false))
        val newWidthRight = maxOf(safeWidth, naviBarController.rightNaviBarHeight(false))
        leftEdge.layoutParams = leftEdge.layoutParams.apply {
            width = newWidthLeft
        }
        rightEdge.layoutParams = rightEdge.layoutParams.apply {
            width = newWidthRight
        }
        rightMask?.let {
            it.layoutParams = it.layoutParams.apply {
                width = newWidthRight
            }
        }
        return Pair(newWidthLeft, newWidthRight)
    }

    /**
     * 针对大尺寸屏幕，会对界面操作区域范围做缩窄
     * 注意，尽量保证在操作区域的根容器上做padding操作
     */
    fun getMenuPadding(viewWidth: Int): Int {
        return if (viewWidth > paddingParamMinNoPaddingWidth) {
            val column = ((viewWidth - Number.NUMBER_2 * paddingParamPadding + paddingParamGap) / Number.NUMBER_12f) - paddingParamGap
            (Number.NUMBER_2 * column + paddingParamGap + paddingParamPadding).toInt()
        } else {
            0
        }
    }

    /**
     * 对于需要分栅格做内容padding的view，根据窗口宽度返回水平padding值
     * 当窗口很宽时，边距为窗口宽 2/12
     * 当窗口较宽时，边距为窗口宽 1/8
     * 一般窗口大小用默认边距
     * 具体参考视觉稿
     * @param width
     */
    fun getGridWindowHorizontalPadding(width: Int): Int {
        var padding = gridWindowDefaultPadding
        if (width >= windowWidthDividedBy12) {
            // 窗口12等分取2分间隙所以是除6,边距还包含2个gap

            padding += (width - gridWindowDefaultPadding * Number.NUMBER_2 - gridWindowGap) / DIVIDED_BY_6_NUM + gridWindowGap * Number.NUMBER_2
        } else if (width >= windowWidthDividedBy8) {
            padding += (width - gridWindowDefaultPadding * Number.NUMBER_2 - gridWindowGap) / DIVIDED_BY_8_NUM + gridWindowGap
        }
        return padding
    }

    /**
     * 对于需要分栅格做内容padding的view，根据窗口高度返回竖直padding值
     * 当窗口较高时，有一个默认边距
     * 当窗口较矮时，无竖直边距
     * 具体参考视觉稿
     * @param height
     */
    fun getGridWindowVerticalPadding(height: Int): Int {
        var padding = gridWindowDefaultPadding
        if (height < gridWindowNoPaddingHeight) {
            padding = 0
        }
        return padding
    }

    /**
     * 参考重载方法 [calculateListSuitableVisibleSpacing]
     */
    fun calculateListSuitableVisibleSpacing(
        viewSize: Int,
        itemSize: Int,
        originSpacing: Int = 0,
        edgePadding: Int = 0,
        count: Int
    ): Int {
        return calculateListSuitableVisibleSpacing(
            viewSize,
            itemSize,
            originSpacing,
            edgePadding,
            count,
            overLongDivideEqually = false
        )
    }

    /**
     * 针对较长的列表，可能因为列表的最后刚好显示了一个完整的item
     * 导致用户以为列表已经显示完全，不知道滑动之后还有其他选项
     *
     * 此算法是根据列表宽，item宽，列表内间距，默认间距和可调整区间，计算一个合适的间距
     * 让列表最后刚好显示一个item的一部分，引导用户滑动列表
     *
     * 算法会计算默认间距下最后一个item露出多少
     * 不在最佳露出区间，就循环调整间距以匹配露出区间
     * 之所以使用循环，是因为在item较多的情况下，间距调整后每次的item显示个数可能有较大差异，都要独立检测
     * 循环的间距需要在可调整范围内
     * 如果在就用这个间距，如果不在，返回原始间距
     * 注意：返回的间距一定是偶数，方便平分
     *
     * @param viewSize 列表的滑动边长
     * @param itemSize item在列表滑动方向的长度
     * @param originSpacing 初始间距
     * @param edgePadding 列表内边距，算法默认起点终点间距相同
     * @param count item的个数，计算时会考虑刚好显示边距的情况，如果不需要考虑边距情况，可以传一个较大值
     * @param itemSuitableVisibleRange item的最佳露出度，默认是item的20%~80%
     * @param spacingAdjustmentRange 间距的可调整范围，默认是原始间距的70%~130%
     * @param overLongDivideEqually 当列表过长的时候，是否要调整间距以填充列表长度
     * @return 返回调整后的间距，必定是偶数
     */
    @Suppress("LongMethod")
    fun calculateListSuitableVisibleSpacing(
        viewSize: Int,
        itemSize: Int,
        originSpacing: Int,
        edgePadding: Int = 0,
        count: Int,
        itemSuitableVisibleRange: List<Int> =
            listOf((itemSize * 0.2f).toInt(), (itemSize * 0.8f).toInt()),
        spacingAdjustmentRange: List<Int> =
            listOf((originSpacing * 0.7f).toInt(), (originSpacing * 1.3f).toInt()),
        overLongDivideEqually: Boolean = false
    ): Int {

        fun isCutoffInSuitableVisibleRange(cutoff: Int): Boolean {
            return cutoff in itemSuitableVisibleRange[0]..itemSuitableVisibleRange[1]
        }

        fun isSpacingApplyInSuitableVisibleRange(spacing: Int): Boolean {
            // 计算开始时的截断位置
            val visibleCount = (viewSize - edgePadding) / (itemSize + spacing)
            val cutoff = (viewSize - edgePadding) - (itemSize + spacing) * visibleCount
            // 差异刚好在合适区域
            return ((visibleCount > 0) && (visibleCount < count) && isCutoffInSuitableVisibleRange(cutoff))
                    || ((visibleCount == count) && (cutoff <= edgePadding))
        }

        if ((spacingAdjustmentRange.size != 2) || (itemSuitableVisibleRange.size != 2)) {
            return originSpacing
        }
        if (count <= 1) {
            return originSpacing
        }
        if (viewSize < edgePadding * 2 + itemSize) {
            return originSpacing
        }

        // 间距会二分，必须是整数
        val minSpacing = spacingAdjustmentRange[0] + spacingAdjustmentRange[0] % 2
        val maxSpacing = spacingAdjustmentRange[1] - spacingAdjustmentRange[1] % 2
        val spacingCount = count - 1

        if (viewSize >= edgePadding * 2 + itemSize * count + originSpacing * spacingCount) {
            //超长时填充列表
            if (overLongDivideEqually) {
                var newSpacing = (viewSize - edgePadding * 2 - itemSize * count) / spacingCount
                newSpacing -= newSpacing % 2
                return min(maxSpacing, newSpacing)
            }
        } else {
            if (isSpacingApplyInSuitableVisibleRange(originSpacing)) {
                return originSpacing
            } else {
                // 默认值需要在合理范围内
                var smallerSpacing = 0
                var largerSpacing = viewSize

                var newSpacing = originSpacing
                while (true) {
                    newSpacing += 2
                    if (newSpacing > maxSpacing) {
                        break
                    }
                    if (isSpacingApplyInSuitableVisibleRange(newSpacing)) {
                        largerSpacing = newSpacing
                        break
                    }
                }

                newSpacing = originSpacing
                while (true) {
                    newSpacing -= 2
                    if (newSpacing < minSpacing) {
                        break
                    }
                    if (isSpacingApplyInSuitableVisibleRange(newSpacing)) {
                        smallerSpacing = newSpacing
                        break
                    }
                }
                return if ((largerSpacing > maxSpacing) && (smallerSpacing < minSpacing)) {
                    originSpacing
                } else {
                    if (largerSpacing - originSpacing <= originSpacing - smallerSpacing) {
                        largerSpacing
                    } else {
                        smallerSpacing
                    }
                }
            }
        }
        return originSpacing
    }

    @JvmStatic
    fun isEditorLandscape(config: AppUiResponder.AppUiConfig?): Boolean {
        config ?: return false
        val sizeForm = WindowSizeForm.getSizeForm(config)
        return (sizeForm == WindowSizeForm.STANDARD_LANDSCAPE) || (sizeForm == WindowSizeForm.TABLET_LANDSCAPE)
    }

    @JvmStatic
     fun isEnterMultiWindow(config: AppUiResponder.AppUiConfig?): Boolean {
        config ?: return false
        val sizeForm = WindowSizeForm.getSizeForm(config)
        return (sizeForm === WindowSizeForm.MULTI_WINDOW) || (sizeForm === WindowSizeForm.MICRO_SIZE)
    }

    @JvmStatic
    fun getListOrientation(config: AppUiResponder.AppUiConfig): Int {
        return if (isEditorLandscape(config)) {
            LinearLayout.VERTICAL
        } else {
            LinearLayout.HORIZONTAL
        }
    }

    @JvmStatic
    fun initEditorMenuAdapterData(
        context: Context?,
        idArrayResId: Int,
        iconArrayResId: Int,
        textArrayResId: Int
    ): ArrayList<EditorMenuItemViewData> {
        val data = ArrayList<EditorMenuItemViewData>()
        val ids = ResourceUtils.getResourceIdArrays(context, idArrayResId)
        val icons = ResourceUtils.getResourceIdArrays(context, iconArrayResId)
        val texts = ResourceUtils.getResourceIdArrays(context, textArrayResId)
        val size = Math.min(Math.min(ids.size, icons.size), texts.size)
        for (i in 0 until size) {
            data.add(
                EditorMenuItemViewData(
                    viewId = ids[i],
                    isEnable = true,
                    isSelectable = true,
                    isSelected = false,
                    iconResId = icons[i],
                    text = null,
                    textId = texts[i],
                    itemId = -1,
                    iconBitmap = null,
                    centerText = TextUtil.EMPTY_STRING,
                    textColor = 0,
                    isTopTipsShow = false,
                    isCornerTipsShow = false,
                    isDisableStyle = false
                )
            )
        }
        return data
    }

    @JvmStatic
    fun initEditorMenuAdapterData(
        context: Context?,
        idArrayResId: Int,
        iconArrayResId: Int,
        textArrayResId: Int,
        itemIdArrayResId: Int
    ): ArrayList<EditorMenuItemViewData> {
        val data = ArrayList<EditorMenuItemViewData>()
        val ids = ResourceUtils.getResourceIdArrays(context, idArrayResId)
        val icons = ResourceUtils.getResourceIdArrays(context, iconArrayResId)
        val texts = ResourceUtils.getResourceIdArrays(context, textArrayResId)
        var itemIds: IntArray? = null
        if (itemIdArrayResId != Resources.ID_NULL) {
            itemIds = ResourceUtils.getIntArrays(context, itemIdArrayResId)
        }
        val size = Math.min(Math.min(ids.size, icons.size), texts.size)
        var id = Resources.ID_NULL
        for (i in 0 until size) {
            if (itemIds != null) {
                id = itemIds[i]
            }
            data.add(
                EditorMenuItemViewData(
                    ids[i], true, false, false, icons[i], null,
                    texts[i], id, null, TextUtil.EMPTY_STRING, 0,
                    false, false, false
                )
            )
        }
        return data
    }

    @JvmStatic
    fun initEditorMenuAdapterData(
        idArray: List<Int?>,
        textArray: List<Int?>
    ): ArrayList<EditorMenuItemViewData> {
        val data = ArrayList<EditorMenuItemViewData>()
        val size = Math.min(idArray.size, textArray.size)
        for (i in 0 until size) {
            data.add(
                EditorMenuItemViewData(
                    idArray[i]!!, true, true, false, 0, null,
                    textArray[i]!!, -1, null, TextUtil.EMPTY_STRING, 0,
                    false, false, false
                )
            )
        }
        return data
    }

    /**
     * 初始化菜单按钮的视图数据集
     * @param idArray id数组
     * @param iconArray 图标数组
     * @param textArray 文本数组
     * @return 视图数据集
     */
    @JvmStatic
    fun initEditorMenuAdapterData(
        idArray: List<Int>,
        iconArray: List<Int>,
        textArray: List<Int>
    ): ArrayList<EditorMenuItemViewData> {
        val data = ArrayList<EditorMenuItemViewData>()
        val size = min(idArray.size, textArray.size)
        for (i in 0 until size) {
            data.add(
                EditorMenuItemViewData(
                    idArray[i], true, true, false, iconArray[i], null,
                    textArray[i], -1, null, TextUtil.EMPTY_STRING, 0,
                    false, false, false
                )
            )
        }
        return data
    }

    @JvmStatic
    fun initTextAdapterData(context: Context?, idArrayResId: Int, textArrayResId: Int): ArrayList<TextViewData> {
        val data = ArrayList<TextViewData>()
        val ids = ResourceUtils.getResourceIdArrays(context, idArrayResId)
        val texts = ResourceUtils.getResourceIdArrays(context, textArrayResId)
        val size = Math.min(ids.size, texts.size)
        for (i in 0 until size) {
            data.add(TextViewData(ids[i], true, true, false, Resources.ID_NULL, null, texts[i], -1, null))
        }
        return data
    }

    @JvmStatic
    fun initAdapterData(context: Context?, idArrayResId: Int, iconArrayResId: Int): ArrayList<ImageViewData>? {
        val data = ArrayList<ImageViewData>()
        val ids = ResourceUtils.getResourceIdArrays(context, idArrayResId)
        val icons = ResourceUtils.getResourceIdArrays(context, iconArrayResId)
        val size = Math.min(ids.size, icons.size)
        for (i in 0 until size) {
            data.add(ImageViewData(ids[i], true, true, false, icons[i]))
        }
        return data
    }

    @JvmStatic
    fun initAdapterDataDescription(context: Context?, idArrayResId: Int, iconArrayResId: Int, description: Int): ArrayList<ImageViewData>? {
        val data = ArrayList<ImageViewData>()
        val ids = ResourceUtils.getResourceIdArrays(context, idArrayResId)
        val icons = ResourceUtils.getResourceIdArrays(context, iconArrayResId)
        val description = ResourceUtils.getResourceIdArrays(context, description)
        val size = Math.min(ids.size, icons.size)
        for (i in 0 until size) {
            data.add(ImageViewData(ids[i], true, true, false, icons[i], description[i]))
        }
        return data
    }

    /**
     * 获取颜色不变,alpha加深的颜色值
     */
    @JvmStatic
    fun getAnimAlphaColor(progress: Float, srcColor: Int): Int {
        val r = MAX_COLOR_VALUE.coerceAtMost(Color.red(srcColor))
        val g = MAX_COLOR_VALUE.coerceAtMost(Color.green(srcColor))
        val b = MAX_COLOR_VALUE.coerceAtMost(Color.blue(srcColor))
        val a = getAnimAlpha(progress, Color.alpha(srcColor))
        return Color.argb(a, r, g, b)
    }

    /**
     * return 返回0-255的alpha值
     */
    @JvmStatic
    fun getAnimAlpha(progress: Float, srcAlpha: Int): Int =
        MAX_ALPHA_VALUE.coerceAtMost(
            MIN_ALPHA_VALUE.coerceAtLeast(
                (srcAlpha + (MAX_ALPHA_VALUE - srcAlpha) * (progress)).toInt()
            )
        )

    /**
     * 针对列表，在不同尺寸下配置item宽度 间距等ui参数
     *
     */
    data class ListUIConfig(
        var margin: Int = 0,
        var column: Int = 0,
        var itemWidth: Int = 0,
        var verticalSpacing: Int = 0,
        var horizontalSpacing: Int = 0
    ) {
        fun updateListUIConfig(
            viewWidth: Int,
            marginHorizontal: Int,
            designColumn: Int,
            minItemWidth: Int,
            maxItemWidth: Int,
            itemGap: Int
        ) {
            // gridView非两边空白区域
            val listConfig = getItemMaxConfig(
                viewWidth.toFloat(),
                (marginHorizontal * 2).toFloat(),
                itemGap.toFloat(),
                minItemWidth.toFloat()..maxItemWidth.toFloat()
            )
            var newColumn = listConfig.column
            if (newColumn <= 1) {
                newColumn = designColumn
            }
            if (newColumn <= 1) {
                GLog.e(TAG, "resetStickerGridViewConfig column num must > 1")
                return
            }
            val newItemWidth = listConfig.itemWidth
            // 由于float转int会有精度丢失，列数越多则误差越大，所以重新计算grid之间的间距
            val gap = ((viewWidth - marginHorizontal * 2 - newColumn * newItemWidth) * 1f / (newColumn - 1) + 0.5f).toInt()
            // 间距变化之后，gridView内容区域的总宽度也要跟着变化
            val newParentW = newColumn * (newItemWidth + gap) - gap
            // 总宽度变化之后，两边的空白大小也要变更
            val newMargin: Int = (viewWidth - newParentW) / 2

            margin = newMargin
            column = newColumn
            itemWidth = newItemWidth
            verticalSpacing = gap
            horizontalSpacing = gap
        }
    }

    /**
     * 计算的单位是dp
     */
    @JvmStatic
    fun getToolBarContentSize(toolBarSize: Int, isSmallLandscape: Boolean): Int {
        val gridLevel = getToolBarGridLevel(toolBarSize)
        val edge = if (gridLevel != GridLevel.T0) EDGE_24DP * Number.NUMBER_2 else 0
        return getToolBarContentSize(gridLevel, toolBarSize, edge, isSmallLandscape)
    }

    /**
     * 根据栅格的级别，及安全区域和边距，获取toobar区域的宽/高
     * 返回和计算单位都是dp
     */
    @JvmStatic
    fun getToolBarContentSize(toolBarSize: Int, margin: Int): Int {
        val gridLevel = getToolBarGridLevel(toolBarSize)
        val edge = if (gridLevel != GridLevel.T0) {
            if (margin > 0) EDGE_24DP * Number.NUMBER_2 + margin * Number.NUMBER_2 else EDGE_24DP * Number.NUMBER_2
        } else 0
        return getToolBarContentSize(gridLevel, toolBarSize, edge)
    }

    @JvmStatic
    private fun getToolBarGridLevel(size: Int) = when {
        size < TOOLBAR_T1 -> GridLevel.T0
        size < TOOLBAR_T2 -> GridLevel.T1
        else -> GridLevel.T2
    }

    /**
     * 根据栅格的级别，及安全区域，获取toobar区域的宽/高
     * @param level 栅格等级
     * @param viewSize 视图宽度
     * @param edge 边距
     * @param isSmallLandscape 是否直板横屏
     * @return 返回和计算单位都是dp
     */
    @JvmStatic
    private fun getToolBarContentSize(level: GridLevel, viewSize: Int, edge: Int = 0, isSmallLandscape: Boolean = false): Int {
        val spanSize = getToolBarContentSpanSize(level, isSmallLandscape)
        val rowColumnNum = getRowColumnNum(level)
        val rowColumnSize = getRowColumnSize(size = viewSize, rowColumnNum = rowColumnNum, edge = edge)
        return (rowColumnSize * spanSize + GAP * (spanSize - 1)).toInt()
    }

    @JvmStatic
    private fun getToolBarContentSpanSize(level: GridLevel, isSmallLandscape: Boolean = false) = when (level) {
        GridLevel.T0 -> SPAN_TOOLBAR_SIZE_T0
        GridLevel.T1 -> if (isSmallLandscape.not()) SPAN_TOOLBAR_SIZE_T1 else SPAN_TOOLBAR_SIZE_LAND_T1
        GridLevel.T2 -> SPAN_TOOLBAR_SIZE_T2
    }

    @JvmStatic
    private fun getRowColumnNum(level: GridLevel) = when (level) {
        GridLevel.T0 -> ROW_COLUMN_NUM_T0
        GridLevel.T1 -> ROW_COLUMN_NUM_T1
        GridLevel.T2 -> ROW_COLUMN_NUM_T2
    }

    /**
     * 获取栅格数量
     */
    @JvmStatic
    private fun getRowColumnSize(size: Int, rowColumnNum: Int, gap: Int = GAP, edge: Int): Float {
        return (size - edge - (gap.toFloat() * (rowColumnNum - 1))) / rowColumnNum
    }

    private enum class GridLevel {
        T0, T1, T2
    }
}