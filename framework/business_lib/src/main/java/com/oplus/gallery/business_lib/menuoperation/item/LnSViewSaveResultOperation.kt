/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : LnSViewSaveResultOperation.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/06/12
 ** Author      : <EMAIL>
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** gao<PERSON><EMAIL>           2023/06/12      1.0
 *********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.business_lib.menuoperation.LnSViewSaveResultAction
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_IMAGE_ANY
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.lang.ref.WeakReference

/**
 * 菜单操作：在新大图查看保存的抠图结果
 */
class LnSViewSaveResultOperation(
    isRunOnMain: Boolean,
    paramMap: Map<String, Any?>,
    onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)?
) : MenuOperation<Unit>(isRunOnMain, paramMap, onCompleted) {
    private var currentFragmentRef: WeakReference<BaseFragment>? = null
    private var requestCode: Int? = null
    private var packageName: String? = null
    private var saveResultUri: Uri? = null

    override fun onCheckAndPrepare(): Boolean = runCatching {
        currentFragmentRef = (paramMap[LnSViewSaveResultAction.KEY_ACTION_LNS_FRAGMENT_REF] as? WeakReference<BaseFragment>)
        if (currentFragmentRef == null) {
            GLog.e(TAG) { "[onCheckAndPrepare] fail, cause activity = null" }
            setResult(MenuAction.RESULT_FAILED, skipTrackResult = true)
            return@runCatching false
        }
        requestCode = (paramMap[LnSViewSaveResultAction.KEY_ACTION_LNS_REQUEST_CODE] as? Int)
        packageName = (paramMap[LnSViewSaveResultAction.KEY_ACTION_LNS_PACKAGE_NAME] as? String)
        saveResultUri = (paramMap[LnSViewSaveResultAction.KEY_ACTION_LNS_SAVE_RESULT_URI] as? Uri)
    }.onFailure { error ->
        setResult(MenuAction.RESULT_FAILED, skipTrackResult = true)
        GLog.e(TAG, error) { "[onCheckAndPrepare] fail, exception: " }
    }.onSuccess {
        setResult(MenuAction.RESULT_SUCCESS)
    }.isSuccess

    override fun onRun() {
        currentFragmentRef?.get()?.let { fragment ->
            // 拉起新大图
            launchNewPhotoPage(fragment)
            // 后续交互可能会要加动效
            fragment.activity?.overridePendingTransition(0, 0)
        } ?: let {
            GLog.e(TAG) { "[onRun] fail, cause fragment is null" }
            setResult(MenuAction.RESULT_ERROR_NO_DATA, skipTrackResult = true)
        }
    }

    private fun launchNewPhotoPage(fragment: BaseFragment) {
        Starter.ActivityStarter(
            startContext = fragment,
            postCard = PostCard(RouterConstants.RouterName.VIEW_GALLERY_ACTIVITY),
            requestCode = requestCode,
            onIntentCreated = { intent ->
                intent.apply {
                    setAction(Intent.ACTION_VIEW)
                    addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    setDataAndType(saveResultUri, MIME_TYPE_IMAGE_ANY)
                    if (!TextUtils.isEmpty(packageName)) {
                        setPackage(packageName)
                    } else {
                        setPackage(ContextGetter.context.packageName)
                    }
                    putExtra(IntentUtils.NAVIGATE_UP_TITLE_ID, com.oplus.gallery.basebiz.R.string.common_back)
                }
            }
        ).start()
    }

    override fun getMenuTypeForTrack(): String = MenuFrameworkTrackConstant.Value.VALUE_DEFAULT_NULL

    override fun getCustomParamForTrack(): Map<String, String?>? = null

    override fun getImageAndVideoCountForTrack(): Pair<String, String> = defaultTrackCountPair

    override fun cancel() {
        // do nothing
    }

    companion object {
        private const val TAG = "LnSViewSaveResultOperation"
    }
}