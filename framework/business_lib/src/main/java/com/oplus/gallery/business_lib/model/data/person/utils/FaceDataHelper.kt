/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - FaceDataHelper.java
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/09/10
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/09/10	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.model.data.person.utils

import com.oplus.gallery.foundation.database.notifier.UriNotifier
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.INVALID
import com.oplus.gallery.foundation.database.store.GalleryStore.ScanFaceColumns.IS_RECYCLED
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.scheduler.BatchProcess

object FaceDataHelper {

    private const val TAG = "FaceDataHelper"

    @JvmStatic
    fun deleteFaceImageByMediaIdList(filePaths: List<String>) {
        if (filePaths.isNullOrEmpty()) {
            GLog.w(TAG, "deleteFaceImageByMediaIdList, filePaths is empty!")
            return
        }
        try {
            BatchProcess.doBatch(filePaths, BatchProcess.PAGE_SIZE_999) { batchFilePaths ->
                val deleteCount = DeleteReq.Builder()
                        .setDaoType(IDao.DaoType.GALLERY)
                        .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                        .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                        .setWhere(DatabaseUtils.getWhereQueryIn(ScanFaceColumns.DATA, batchFilePaths.size))
                        .setWhereArgs(batchFilePaths.toTypedArray())
                        .build()
                        .exec()
                GLog.d(TAG, "deleteFaceImageByMediaIdList, delete count = $deleteCount")
                emptyList()
            }
            UriNotifier.notifyTableChange(GalleryDbDao.TableType.SCAN_FACE, IDao.DaoType.GALLERY)
        } catch (e: Exception) {
            GLog.w(TAG, "deleteFaceImageByMediaIdList, e = ", e)
        }
    }

    @JvmStatic
    fun deleteMediaFile(fileList: ArrayList<String>?) {
        if (fileList.isNullOrEmpty()) {
            GLog.w(TAG, "deleteMediaFile, fileList is empty!")
            return
        }
        val size = fileList.size
        GLog.d(TAG, "deleteMediaFile, fileList.mSize: $size")
        try {
            val sb = StringBuilder()
            sb.append(GalleryStore.ScanFace.DATA + " IN (")
            for (filePath in fileList) {
                sb.append("\'")
                sb.append(filePath)
                sb.append("\'")
                sb.append(",")
            }
            sb.deleteCharAt(sb.length - 1)
            sb.append(")")
            val req = DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setWhere(sb.toString())
                    .build()
            DataAccess.getAccess().delete(req)
        } catch (e: Exception) {
            GLog.e(TAG, "deleteMediaFile, error: " + e)
        }
    }

    @JvmStatic
    fun deleteAllRecycledFile() {
        GLog.d(TAG, "deleteAllRecycledFile")
        try {
            val whereClause = (IS_RECYCLED + " = 1  AND " + INVALID + " != 1")
            DeleteReq.Builder()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.SCAN_FACE)
                    .setWhere(whereClause)
                    .build().exec()
        } catch (e: java.lang.Exception) {
            GLog.e(TAG, "deleteAllRecycledFile, error: " + e)
        }
    }
}
