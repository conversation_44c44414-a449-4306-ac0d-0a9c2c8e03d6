/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamExportVideoEngine
 ** Description: 基于美摄SDK实现的导出视频的引擎。
 ** Version: 1.0
 ** Date : 2024/7/4
 ** Author: xiangwei
 ** email:<EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xiangwei                           2024/7/4  1.0          first created
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item.exportvideo

import android.content.Context
import android.graphics.Rect
import android.graphics.RectF
import android.net.Uri
import com.meicam.sdk.NvsAudioResolution
import com.meicam.sdk.NvsRational
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsStreamingContext.COMPILE_OPPO_SPACIAL_DATA
import com.meicam.sdk.NvsStreamingContext.COMPILE_USE_OPERATING_RATE
import com.meicam.sdk.NvsStreamingContext.COMPILE_VIDEO_ENCODER_NAME
import com.meicam.sdk.NvsStreamingContext.COMPILE_VIDEO_HDR_COLOR_TRANSFER
import com.meicam.sdk.NvsStreamingContext.CompileCallback
import com.meicam.sdk.NvsStreamingContext.CompileCallback2
import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsVideoClip
import com.meicam.sdk.NvsVideoFx
import com.meicam.sdk.NvsVideoResolution
import com.meicam.sdk.NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
import com.meicam.sdk.NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_AUTO
import com.meicam.sdk.NvsVideoStreamInfo
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_BT2020
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_DISPLAY_P3
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_HLG
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_SDR_VIDEO
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_ST2084
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_DOLBYVISION
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_HDR10
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_HDR10PLUS
import com.meicam.sdk.NvsVideoStreamInfo.HDR_TYPE_HLG
import com.meicam.sdk.NvsVideoTrack
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.menuoperation.item.ExportVideoOperation.Companion.ENGINE_VIDEO_FPS
import com.oplus.gallery.business_lib.util.formatNvmFilePath
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_HLG
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_PQ
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.SRGB
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.isHdrGamma
import com.oplus.gallery.foundation.util.math.MathUtil
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USE_OPERATE_RATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE_ACCELERATE
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_0
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_4
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter.tryCloseContext
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupport
import com.oplus.gallery.videoeditor.utils.CodecSupportParser
import java.util.Hashtable
import kotlin.math.roundToInt

/**
 * 基于美摄SDK实现的导出视频的引擎。
 * Marked by zhangwenming，未来视频编辑重构后，此处需要接入视频编辑的能力。
 *
 */
internal class MeicamExportVideoEngine : IExportVideoEngine {
    /**
     * 流媒体上下文
     */
    private var nvsContext: NvsStreamingContext? = null

    /**
     * 当前视频编辑引擎使用的时间线。
     */
    private var nvsTimeLine: NvsTimeline? = null

    /**
     * 导出视频时，创建的视频轨道
     */
    private var videoTracker: NvsVideoTrack? = null

    /**
     * 导出视频时，创建的视频片段
     */
    private var videoClip: NvsVideoClip? = null

    /**
     *导出视频状态回调
     */
    private var iExportVideoCallback: IExportVideoCallback? = null

    /**
     * 是否支持广色域
     */
    private val isWideColorGamut by lazy {
        ActivityLifecycle.getActivityList().lastOrNull()?.get()?.window?.isWideColorGamut ?: false
    }

    /**
     * 视频文件保存进度
     */
    private val compileCallback: CompileCallback = object : CompileCallback {
        override fun onCompileProgress(timeline: NvsTimeline, position: Int) {
            if (nvsTimeLine !== timeline) {
                return
            }
            iExportVideoCallback?.onProgress(position)
        }

        override fun onCompileFinished(timeline: NvsTimeline) {
            if (nvsTimeLine !== timeline) {
                return
            }
        }

        override fun onCompileFailed(timeline: NvsTimeline) {
            if (nvsTimeLine !== timeline) {
                return
            }
            iExportVideoCallback?.onFailed(true)
        }
    }

    /**
     * 视频文件保存完成状态
     */
    private val compileCallback2 = CompileCallback2 { timeline, isCanceled ->
        if (nvsTimeLine !== timeline) {
            return@CompileCallback2
        }
        iExportVideoCallback?.onComplete(true)
    }

    /**
     * 原始视频宽度
     */
    private var originalVideoWidth: Int? = null

    /**
     * 原始视频高度
     */
    private var originalVideoHeight: Int? = null

    /**
     * 裁切使用的视频特效
     */
    private var cropFx: NvsVideoFx? = null

    /**
     * 视频的hdr类型，后续会根据这个设置美摄的config参数
     */
    private var hdrType: Int? = null

    override fun initEngine(context: Context, uri: Uri, offset: Long, length: Long, specification: VideoSpec): Boolean {
        if (nvsTimeLine != null) {
            GLog.d(TAG, LogFlag.DL) { "[initEngine] ignore, for nvsTimeLine already exist." }
            return false
        }
        nvsContext = MeicamContextAliveCounter.requestContext(context.applicationContext, tag = TAG)
        if (nvsContext == null) {
            GLog.e(TAG, LogFlag.DL) { "[initEngine] nvsContext create failed." }
            return false
        }
        var colorPrimaries: Int? = null
        var colorTransfer: Int? = null
        nvsContext?.getAVFileInfo(formatNvmFilePath(uri, offset, length), NvsStreamingContext.AV_FILEINFO_EXTRA_INFO)?.let { videoInfo ->
            hdrType = if ((videoInfo.getVideoStreamCodecType(0) == NvsVideoStreamInfo.VIDEO_CODEC_TYPE_H265)
                && CodecSupport.isSupportCodec(MimeTypeUtils.MIME_TYPE_VIDEO_HEVC, specification).not()
            ) {
                // 当设备不支持用hevc的硬件编解码此规格的视频时，hdrType设为null，按sdr导出
                null
            } else {
                videoInfo.getVideoStreamHDRType(0)
            }
            colorPrimaries = videoInfo.getVideoStreamColorPrimaries(0)
            colorTransfer = videoInfo.getVideoStreamColorTranfer(0)

            // 视频规格使用实际获取到的帧率，修复当导出60fps的实况照片时，保存的视频为30fps的bug.
            videoInfo.getVideoStreamFrameRate(0)?.let { fps ->
                specification.fps = (fps.num / fps.den.toFloat()).coerceAtLeast(ENGINE_VIDEO_FPS)
                GLog.d(TAG, LogFlag.DL) { "initEngine videoFps=${specification.fps}" }
            }
        }
        GLog.d(TAG, LogFlag.DL) { "initEngine hdrType:$hdrType colorPrimaries:$colorPrimaries colorTranser:$colorTransfer" }
        nvsTimeLine =
            nvsContext!!.createTimeline(
                getVideoResolution(specification, colorPrimaries, colorTransfer), getFpsRate(specification.fps),
                getAudioResolution()
            ).also {
                videoTracker = it.appendVideoTrack()
            }
        nvsContext?.setCompileCallback(compileCallback)
        nvsContext?.setCompileCallback2(compileCallback2)
        originalVideoWidth = nvsTimeLine?.videoRes?.imageWidth
        originalVideoHeight = nvsTimeLine?.videoRes?.imageHeight
        return true
    }

    override fun appendVideoClip(uri: Uri, offset: Long, length: Long, trimIn: Long, trimOut: Long) {
        appendVideoClip(formatNvmFilePath(uri, offset, length), trimIn, trimOut)
    }

    override fun appendVideoClip(path: String, trimIn: Long, trimOut: Long) {
        videoClip = if (trimIn >= trimOut) {
            videoTracker?.appendClip(path)
        } else {
            videoTracker?.appendClip(path, trimIn, trimOut)
        }?.also {
            it.disableClipColorPrimariesConvert(true)
        }
    }

    override fun saveVideo(filePath: String, options: ExportVideoOptions): Boolean {
        val timeLine = nvsTimeLine ?: let {
            GLog.d(TAG, LogFlag.DL) { "[saveVideo] failed, for timeline is null." }
            return false
        }
        val context = nvsContext ?: let {
            GLog.d(TAG, LogFlag.DL) { "[saveVideo] failed, for nvsContext is null." }
            return false
        }
        context.customCompileVideoHeight = options.videoHeight
        val configuration = formatNvsConfiguration(options)

        if (isWideColorGamut) {
            configuration[NvsStreamingContext.COMPILE_VIDEO_COLOR_PRIMARIES] = COMPILE_VIDEO_COLOR_PRIMARIES_VALUE
        }
        context.compileConfigurations = configuration
        return context.compileTimeline(
            timeLine,
            0,
            timeLine.duration,
            filePath,
            NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
            NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
            // 别问我这两个参数为啥是false，我也不知道，其他地方也是这么写的！
            buildCompileFlag(false, false)
        ).also {
            context.compileConfigurations = null
        }
    }

    private fun buildCompileFlag(noUseInputSurface: Boolean, needDisableHardwareEncoder: Boolean): Int {
        //生成文件输出的特殊标志，如果没有特殊需求，请填写0
        var compileFlag = 0
        if (noUseInputSurface) {
            compileFlag = compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DONT_USE_INPUT_SURFACE
        }
        if (needDisableHardwareEncoder) {
            compileFlag = compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_HARDWARE_ENCODER
        }
        // 禁用字节对齐，否则去水印时坐标就不准确了
        compileFlag = compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE
        return compileFlag
    }

    /**
     * 获取回调接口
     */
    override fun registerExportVideoCallback(exportVieoCallback: IExportVideoCallback) {
        iExportVideoCallback = exportVieoCallback
    }

    /**
     * 释放回调资源
     */
    override fun unRegisterExportVideoCallback() {
        if (iExportVideoCallback != null) {
            iExportVideoCallback = null
        }
    }

    /**
     * 资源回收
     */
    override fun close() {
        nvsContext?.let {
            nvsContext?.stop(NvsStreamingContext.STREAMING_ENGINE_STOP_FLAG_FORCE_STOP_COMPILATION)
            nvsContext?.removeTimeline(nvsTimeLine)
            nvsContext = null
            tryCloseContext(tag = TAG)
        }
        if (iExportVideoCallback != null) {
            iExportVideoCallback = null
        }
    }

    override fun cropVideoContent(contentRect: RectF, scale: Float) {
        // 检查原始视频尺寸是否有效
        val videoWidth = originalVideoWidth ?: run {
            GLog.e(TAG, LogFlag.DL) { "originalVideoWidth is null" }
            return
        }
        val videoHeight = originalVideoHeight ?: run {
            GLog.e(TAG, LogFlag.DL) { "originalVideoHeight is null" }
            return
        }

        if ((videoWidth == 0) || (videoHeight == 0)) {
            GLog.e(TAG, LogFlag.DL) { "video's width or height is 0" }
            return
        }

        // 调整 contentRect，确保不超出边界
        with(contentRect) {
            left = (left - NUMBER_4).coerceAtLeast(0f)
            top = (top - NUMBER_4).coerceAtLeast(0f)
            right = (right + NUMBER_4)
            bottom = (bottom + NUMBER_4)
        }

        // 应用 2D 变换
        videoClip?.appendBuiltinFx(TRANSFORM_2D)?.let { transform2D ->
            transform2D.regional = true

            // 计算原始视频中心点
            val originalCenterX = videoWidth / NUMBER_2
            val originalCenterY = videoHeight / NUMBER_2

            // 计算归一化的区域坐标(NDC)
            val normalizedLeft = (contentRect.left - originalCenterX) / originalCenterX
            val normalizedRight = (contentRect.right - originalCenterX) / originalCenterX
            val normalizedTop = (originalCenterY - contentRect.top) / originalCenterY
            val normalizedBottom = (originalCenterY - contentRect.bottom) / originalCenterY

            // 设置变换区域
            transform2D.region = floatArrayOf(
                normalizedLeft, normalizedTop,      // 左上
                normalizedLeft, normalizedBottom,    // 左下
                normalizedRight, normalizedBottom,   // 右下
                normalizedRight, normalizedTop       // 右上
            )

            // 计算并设置锚点
            val anchorX = ((contentRect.centerX() - originalCenterX) / NUMBER_2).toDouble()
            val anchorY = ((contentRect.centerY() - originalCenterY) / NUMBER_2).toDouble()
            transform2D.setFloatVal(ANCHOR_X, anchorX)
            transform2D.setFloatVal(ANCHOR_Y, anchorY)

            // 应用缩放
            transform2D.setFloatVal(SCALE_X, scale.toDouble())
            transform2D.setFloatVal(SCALE_Y, scale.toDouble())
        } ?: GLog.e(TAG, LogFlag.DL) { "Failed to apply 2D transform: videoClip or transform2D is null" }
    }

    /**
     * 裁切视频到指定 rect 范围，需已初始化 nvsTimeLine 和 videoClip，
     *
     * 1. 美摄要求 width 需为 4 的倍数， height 需为 2 的倍数，
     *    详见：https://www.meishesdk.com/android/doc_ch/html/content/classcom_1_1meicam_1_1sdk_1_1NvsTimeline.html#a82ba434cf4089aa59bf93fdbd528e7b9
     * 2. rect 会由 View 坐标系转为美摄坐标系，
     *    详见：https://www.meishesdk.com/android/doc_ch/html/content/Coordinate_8md.html
     * 3. 反复调用时，依旧是根据原大小做裁切。
     *
     * @param rect 以左上角为原点的范围
     */
    override fun cropVideo(rect: Rect) {
        if (rect.isEmpty) {
            GLog.e(TAG, LogFlag.DL, "[cropVideo] given rect is not valid, $rect")
            return
        }
        val timeline = nvsTimeLine ?: run {
            GLog.e(TAG, LogFlag.DL, "[cropVideo] nvsTimeLine is not initialized before calling cropVideo()!")
            return
        }
        val videoClip = videoClip ?: run {
            GLog.e(TAG, LogFlag.DL, "[cropVideo] videoClip is not set (appendVideoClip) before calling cropVideo()!")
            return
        }
        val orgVideoWidth = originalVideoWidth ?: run {
            GLog.e(TAG, LogFlag.DL, "[cropVideo] originalVideoWidth is not set!")
            return
        }
        val orgVideoHeight = originalVideoHeight ?: run {
            GLog.e(TAG, LogFlag.DL, "[cropVideo] originalVideoHeight is not set!")
            return
        }

        val requestLeft = rect.left.coerceAtLeast(NUMBER_0)
        val requestRight = rect.right.coerceAtMost(orgVideoWidth)
        val requestTop = rect.top.coerceAtLeast(NUMBER_0)
        val requestBottom = rect.bottom.coerceAtMost(orgVideoHeight)

        // 美摄原点
        val centerX = orgVideoWidth / NUMBER_2
        val centerY = orgVideoHeight / NUMBER_2

        // 转为美摄的坐标
        val targetLeft = requestLeft - centerX
        val requestWidth = requestRight - requestLeft
        val targetWidth = MathUtil.getAlignedStrideDownToMultipleOf4(requestWidth).coerceAtLeast(NUMBER_4)
        val targetRight = targetLeft + targetWidth

        val targetTop = centerY - requestTop
        val requestHeight = requestBottom - requestTop
        val targetHeight = MathUtil.getAlignedStrideDoneToMultipleOf2(requestHeight).coerceAtLeast(NUMBER_2)
        val targetBottom = targetTop - targetHeight

        GLog.d(TAG, LogFlag.DL) { "[cropVideo] $rect -> ${Rect(targetLeft, targetTop, targetRight, targetBottom)}" }

        // 设置导出视频的宽高
        timeline.changeVideoSize(targetWidth, targetHeight)

        // 裁切到指定位置
        if (cropFx == null) {
            videoClip.setRawFilterProcessesMode(NvsVideoClip.RAW_FILTER_PROCESSES_MODE_VARIANT_IMAGE_WITH_FILL_MODE_USED)
            videoClip.enablePropertyVideoFx(true)
            cropFx = videoClip.appendRawBuiltinFx(FX_CROP)
        }
        cropFx?.setFloatVal(FX_PROP_BOUNDING_LEFT, targetLeft.toDouble())
        cropFx?.setFloatVal(FX_PROP_BOUNDING_TOP, targetTop.toDouble())
        cropFx?.setFloatVal(FX_PROP_BOUNDING_RIGHT, targetRight.toDouble())
        cropFx?.setFloatVal(FX_PROP_BOUNDING_BOTTOM, targetBottom.toDouble())

        val nvsVideoFx = videoClip.appendRawBuiltinFx(TRANSFORM_2D)
        val tranX = (orgVideoWidth - requestWidth) / 2 - requestLeft.toDouble()
        val tranY = -((orgVideoHeight - requestHeight) / 2 - requestTop.toDouble())
        nvsVideoFx?.setFloatVal(TRANS_X, tranX)
        nvsVideoFx?.setFloatVal(TRANS_Y, tranY)

        timeline.changeVideoSize(targetWidth, targetHeight)
    }

    private fun updateVideoResolution(videoWidth: Int, videoHeight: Int): IntArray {
        /** for meicam. video width % 4 must equals 0, and video height % 2 must equals 0*/
        var mVideoWidth = videoWidth
        var mVideoHeight = videoHeight
        var divCheck = videoWidth % VIDEO_WIDTH_DIV_ZERO_VALUE
        if (divCheck != NUM_ZERO) {
            mVideoWidth -= divCheck
            GLog.d(TAG, LogFlag.DL, "[updateVideoResolution] need change videoWidth:$videoWidth")
        }
        divCheck = videoHeight % VIDEO_HEIGHT_DIV_ZERO_VALUE
        if (divCheck != NUM_ZERO) {
            mVideoHeight -= divCheck
            GLog.d(TAG, LogFlag.DL, "[updateVideoResolution] need change mVideoHeight:$videoHeight")
        }
        return intArrayOf(mVideoWidth, mVideoHeight)
    }

    private fun getVideoResolution(specification: VideoSpec, colorPrimaries: Int?, colorTransfer: Int?): NvsVideoResolution {
        return NvsVideoResolution().apply {
            val newVideoResolution: IntArray = updateVideoResolution(specification.width, specification.height)
            val videoWidth = newVideoResolution[0]
            val videoHeight = newVideoResolution[1]
            imageWidth = videoWidth
            imageHeight = videoHeight
            imagePAR = NvsRational(1, 1)

            // 视频的原始色域
            val videoColorSpace = when {
                (colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTransfer == COLOR_TRANSFER_HLG) -> BT2020_HLG
                (colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTransfer == COLOR_TRANSFER_ST2084) -> BT2020_PQ
                (colorPrimaries == COLOR_PRIMARIES_DISPLAY_P3) && (colorTransfer == COLOR_TRANSFER_SDR_VIDEO) -> DISPLAY_P3
                else -> null
            } ?: SRGB
            // 如果视频显示的是HDR，并且能够显示HDR时，需要设置成VIDEO_RESOLUTION_BIT_DEPTH_AUTO
            bitDepth = if (videoColorSpace.isHdrGamma()) VIDEO_RESOLUTION_BIT_DEPTH_AUTO else VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
        }
    }

    private fun getAudioResolution(): NvsAudioResolution = NvsAudioResolution().apply {
        sampleRate = SAMPLE_RETE
        channelCount = CHANNEL_COUNT
    }

    private fun getFpsRate(fps: Float): NvsRational = NvsRational(fps.roundToInt(), 1)

    /**
     * 将给定的[options]，转换成NvsContext的configuration信息。
     *
     * @param options
     * @return
     */
    private fun formatNvsConfiguration(options: ExportVideoOptions): Hashtable<String, Any> {
        val config = Hashtable<String, Any>()

        if (hdrType == HDR_TYPE_HLG) {
            config[COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
            config[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_HLG_COLOR_TRANSFER_VALUE
        } else if (hdrType == HDR_TYPE_DOLBYVISION) {
            config[COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
            config[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_DOLBY_COLOR_TRANSFER_VALUE
            if (ConfigAbilityWrapper.getBoolean(IS_SUPPORT_DOLBY_ENCODE_ACCELERATE, true).not()) {
                //配置由MediaCodec自行选择处理速率；参照美摄SDK的信息，对编码时间的影响最大增加1/3
                config[COMPILE_USE_OPERATING_RATE] = false
            }
        } else if (hdrType == HDR_TYPE_HDR10) {
            config[COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
            config[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_HDR10_COLOR_TRANSFER_VALUE
        } else if (hdrType == HDR_TYPE_HDR10PLUS) {
            config[COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
            config[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_HDR10PLUS_COLOR_TRANSFER_VALUE
        } else if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_USE_OPERATE_RATE)) {
            config[COMPILE_USE_OPERATING_RATE] = true
        }
        options.eisData?.let {
            config[COMPILE_OPPO_SPACIAL_DATA] = it
        }
        config[NvsStreamingContext.COMPILE_CREATION_TIME] = TimeUtils.getFileDateTakenFormat(options.dateTaken)
        return config
    }

    companion object {
        private const val TAG = "MeicamExportVideoEngine"
        private const val SAMPLE_RETE = 44100
        private const val CHANNEL_COUNT = 2
        private const val NUM_ZERO = 0
        private const val VIDEO_WIDTH_DIV_ZERO_VALUE = 4
        private const val VIDEO_HEIGHT_DIV_ZERO_VALUE = 2

        private const val COMPILE_VIDEO_ENCODER_VALUE = "hevc"
        private const val COMPILE_VIDEO_DOLBY_COLOR_TRANSFER_VALUE = "hlg dolby vision"
        private const val COMPILE_VIDEO_HLG_COLOR_TRANSFER_VALUE = "hlg"
        private const val COMPILE_VIDEO_HDR10_COLOR_TRANSFER_VALUE = "st2084"
        private const val COMPILE_VIDEO_HDR10PLUS_COLOR_TRANSFER_VALUE = "hdr10plus"
        private const val COMPILE_VIDEO_COLOR_PRIMARIES_VALUE = "display p3"

        /**
         * 美摄特效名：裁切
         */
        private const val FX_CROP = "Crop"

        /**
         * 美摄裁切特效用属性名：左边界
         * 参见：OliveVideoWatermarkEditorImpl.removeWatermark() 和 OliveVideoEditEffectsProperty$TransformCropPropertyKey
         */
        private const val FX_PROP_BOUNDING_LEFT = "Bounding Left"

        /**
         * 美摄裁切特效用属性名：右边界
         * 参见：OliveVideoWatermarkEditorImpl.removeWatermark() 和 OliveVideoEditEffectsProperty$TransformCropPropertyKey
         */
        private const val FX_PROP_BOUNDING_RIGHT = "Bounding Right"

        /**
         * 美摄裁切特效用属性名：上边界
         * 参见：OliveVideoWatermarkEditorImpl.removeWatermark() 和 OliveVideoEditEffectsProperty$TransformCropPropertyKey
         */
        private const val FX_PROP_BOUNDING_TOP = "Bounding Top"

        /**
         * 美摄裁切特效用属性名：下边界
         * 参见：OliveVideoWatermarkEditorImpl.removeWatermark() 和 OliveVideoEditEffectsProperty$TransformCropPropertyKey
         */
        private const val FX_PROP_BOUNDING_BOTTOM = "Bounding Bottom"


        const val TRANSFORM_2D = "Transform 2D"

        /**
         * 横向缩放 类型:FLOAT 最大值：1000.0 最小值：-1000.0 默认值1.0
         */
        const val SCALE_X: String = "Scale X"

        /**
         * 纵向缩放  类型:FLOAT  最大值：1000.0  最小值：-1000.0 默认值1.0
         */
        const val SCALE_Y: String = "Scale Y"

        /**
         * 横向定位 类型:FLOAT 最大值：100000.0 最小值：-100000.0 默认值0
         */
        const val ANCHOR_X: String = "Anchor X"

        /**
         * 纵向定位 类型:FLOAT 最大值：100000.0 最小值：-100000.0 默认值0
         */
        const val ANCHOR_Y: String = "Anchor Y"

        /**
         * 横向平移 类型:FLOAT 最大值：100000.0 最小值：-100000.0 默认值0
         */
        const val TRANS_X: String = "Trans X"

        /**
         * 纵向平移 类型:FLOAT 最大值：100000.0 最小值：-100000.0 默认值0
         */
        const val TRANS_Y: String = "Trans Y"
    }
}
