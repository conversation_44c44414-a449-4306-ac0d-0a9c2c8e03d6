/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - MapAlbumTitleMaker.java
 * Description:
 * Version: 1.0
 * Date: 2020/11/2
 * Author: huang.linpeng@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * huang.linpeng@Apps.Gallery3D      2020/11/2     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.map;

import static com.oplus.gallery.business_lib.model.data.location.api.LocationReverseGeocode.READ_CACHE;
import static com.oplus.gallery.business_lib.model.data.location.utils.LocationHelper.PRECISION_CUT_OFF;

import android.text.TextUtils;

import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet;
import com.oplus.gallery.business_lib.model.data.location.LocationManager;
import com.oplus.gallery.business_lib.model.data.location.api.ConfigAddress;
import com.oplus.gallery.business_lib.model.data.location.api.Location;
import com.oplus.gallery.business_lib.model.data.location.api.LocationGeocoder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.geo.GPS;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.JobContext;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR> 2014-2-19
 * 用于解析地图图集的标题
 */
public class MapAlbumTitleMaker extends Location {

    private static final String TAG = "MapAlbumTitleMaker";

    private final WeakReference<BaseActivity> mActivityRef;
    private MediaSet mAlbum;
    private StateListener mStateListener;
    private Future<Void> mTask;

    public MapAlbumTitleMaker(BaseActivity activity) {
        mActivityRef = new WeakReference<>(activity);
    }

    private BaseActivity getActivity() {
        return mActivityRef.get();
    }

    public void setAlbum(MediaSet album) {
        GLog.d(TAG, LogFlag.DL, "setAlbum");
        mAlbum = album;
    }

    public void cancel() {
        GLog.d(TAG, LogFlag.DL, "cancel");
        if (mTask != null) {
            mTask.cancel(false);
        }
        LocationGeocoder.getInstance().remove(this);
    }

    public void onDestroy() {
        cancel();
        mStateListener = null;
    }

    public void reload() {
        if (mAlbum == null) {
            GLog.w(TAG, LogFlag.DL, "reLoad, mAlbum is null");
            return;
        }
        GLog.d(TAG, LogFlag.DL, "reLoad, mAddress is null:" + (mAddress == null)
                + ", mVersion: " + mVersion + ", getDataVersion:" + mAlbum.getDataVersion());
        if ((mVersion != mAlbum.getDataVersion()) || (mAddress == null)) {
            mAddress = null;
            mVersion = mAlbum.getDataVersion();
            BaseActivity activity = getActivity();
            if (activity == null) {
                GLog.w(TAG, LogFlag.DL, "reLoad, activity is null");
                return;
            } else {
                mTask = activity.getSession().submit(new Loader());
            }
        } else if (mStateListener != null) {
            mStateListener.onStateChange();
        }
    }

    public String createTitle(boolean isRegionCN) {
        String title = TextUtil.EMPTY_STRING;
        if (mAddress != null) {
            if (isRegionCN) {
                title = mAddress.getFeatureTitle();
            } else {
                title = mAddress.getTitle();
            }
            // 某些照片的地点信息获取不到，添加兜底策略，按照地点精细度优先级获取标题
            if (TextUtils.isEmpty(title)) {
                title = mAddress.getTitleByMatchPriority();
            }
        } else {
            GLog.w(TAG, LogFlag.DL, "mAddress is null");
        }
        GLog.d(TAG, LogFlag.DL,"createTitle: " + title);
        return title;
    }

    @Override
    protected void notifyStateChanged() {
        GLog.d(TAG, LogFlag.DL, "notifyStateChanged");
        if ((mStateListener != null) && (mState == Location.STATE_LOADED)) {
            mStateListener.onStateChange();
        }
    }

    public void setStateListener(StateListener listener) {
        mStateListener = listener;
    }

    private void reverseGeocode(double latitude, double longitude) {
        ConfigAddress address = LocationManager.INSTANCE.lookupAddress(latitude, longitude, READ_CACHE);
        GLog.d(TAG, LogFlag.DL, "reverseGeocode, getLatLng " + !GPS.isLatLngInvalid(latitude, longitude));
        if (address != null) {
            mAddress = address;
            setState(Location.STATE_LOADED);
        } else {
            mLat = latitude;
            mLng = longitude;
            setState(Location.STATE_LOADING);
            LocationGeocoder.getInstance().add(this);
        }
    }

    private class Loader implements Job<Void> {
        @Nullable
        @Override
        public Void call(@NotNull JobContext jc) {
            GLog.d(TAG, LogFlag.DL, "Loader--call, mState:" + mState + " loader:" + this);
            if (mState == STATE_RECYCLE) {
                return null;
            }
            List<MediaItem> items = mAlbum.getSubMediaItem(0, 1);
            if (items.isEmpty()) {
                GLog.w(TAG, LogFlag.DL, "Loader--call, setState STATE_NONSUPPORT");
                setState(Location.STATE_NONSUPPORT);
            } else {
                LocalMediaItem localMediaItem = (LocalMediaItem) items.get(0);
                double[] latLng = new double[2];
                localMediaItem.getLatLong(latLng);
                double latitude = ((int) (latLng[0] * PRECISION_CUT_OFF)) / PRECISION_CUT_OFF;
                double longitude = ((int) (latLng[1] * PRECISION_CUT_OFF)) / PRECISION_CUT_OFF;
                reverseGeocode(latitude, longitude);
            }
            return null;
        }
    }
}
