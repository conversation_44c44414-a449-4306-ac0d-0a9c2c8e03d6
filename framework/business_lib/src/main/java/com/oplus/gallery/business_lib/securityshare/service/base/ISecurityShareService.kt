/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - ISecurityShareService.kt
 ** Description: ISecurityShareService.kt
 ** Version: 1.0
 ** Date : 2021/04/27
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/04/27        1.0           ISecurityShareService
 **************************************************************************************************/

package com.oplus.gallery.business_lib.securityshare.service.base

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.securityshare.data.PrivacyInfoData
import com.oplus.gallery.business_lib.securityshare.data.SecurityShareRequest
import com.oplus.gallery.business_lib.securityshare.data.SecurityShareResponse

interface ISecurityShareService {

    /**
     * 校验是否有隐私信息
     */
    fun isHasPrivacyInfo(mediaItem: MediaItem): PrivacyInfoData

    /**
     * 执行安全分享
     * 步骤一、 校验是否有隐私信息（所选文件list大于等于2，本次分享抹除的开关状态不受文件是否真正有隐私信息控制，默认可以关闭或者打开）
     * 步骤二 、写缓存文件
     * 步骤三 、去掉隐私信息
     * @param securityShareRequest SecurityShareRequest
     * @return SecurityShareResult
     */
    fun executeSecurityShare(securityShareRequest: SecurityShareRequest): SecurityShareResponse

    /**
     *  构建需要保留OplusExifTag各标志位的数组，用于后续抹除信息时用于保留
     *  @param mediaItem 对应的媒体项目
     *  @return List<Int> 保留OplusExifTag标志位的数组
     */
    fun buildKeepOplusExifTagList(mediaItem: MediaItem): List<Int> = emptyList()

    /**
     * 取消安全分享
     */
    fun cancelSecurityShare(): Unit = Unit
}