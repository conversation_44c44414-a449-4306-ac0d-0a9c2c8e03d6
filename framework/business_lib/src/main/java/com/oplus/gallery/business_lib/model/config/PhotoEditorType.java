package com.oplus.gallery.business_lib.model.config;

public enum PhotoEditorType {
    NORMAL("oplus.normal"),
    ENHANCE_TEXT("oplus.enhance_text"),
    AI_ID_PHOTO("oplus.aiidphoto"),
    SUPER_TEXT("oplus.super_text"),
    PORTRAIT_BLUR("oplus.portrait_blur"),
    GIF_SYNTHESIS("oplus.gif_synthesis"),
    PRIVACY_WATERMARK("oplus.privacy_watermark"),
    IMAGE_QUALITY_ENHANCE("oplus.image_quality_enhance"),
    GROUP_PHOTO("oplus.group_photo"),
    AI_ELIMINATE("oplus.ai_eliminate"),
    PASSERBY_ELIMINATE("oplus_passerby_eliminate"),
    AI_REPAIR_DEBLUR("oplus.ai_repair.deblur"),
    AI_REPAIR_DEREFLECTION("oplus.ai_repair.dereflection"),
    AI_COMPOSITION("oplus.ai_composition"),
    DROP_PICTURE("oplus.drop_picture"),
    AI_SCENERY("oplus.ai_scenery"),
    WATERMARK_CAMERA("oplus.watermark.camera"),
    AI_BESTTAKE("oplus.ai_besttake"),
    AI_LIGHTING("oplus.ai_lighting"),
    EXPORT_OLIVE("oplus.export_olive");

    private final String mTag;

    PhotoEditorType(String tag) {
        mTag = tag;
    }

    public String getTag() {
        return mTag;
    }

    public static boolean isEnhanceTextEditor(String type) {
        return ENHANCE_TEXT.mTag.equals(type);
    }

    public static boolean isAiIDPhotoEditor(String type) {
        return AI_ID_PHOTO.mTag.equals(type);
    }

    public static boolean isSuperTextEditor(String type) {
        return SUPER_TEXT.mTag.equals(type);
    }

    public static boolean isPortraitBlurEditor(String type) {
        return PORTRAIT_BLUR.mTag.equals(type);
    }

    public static boolean isGifSynthesisEditor(String type) {
        return GIF_SYNTHESIS.mTag.equals(type);
    }

    public static boolean isPrivacyWatermarkEditor(String type) {
        return PRIVACY_WATERMARK.mTag.equals(type);
    }

    public static boolean isGroupPhotoEditor(String type) {
        return GROUP_PHOTO.mTag.equals(type);
    }

    public static boolean isDropPictureEditor(String type) {
        return DROP_PICTURE.mTag.equals(type);
    }
}