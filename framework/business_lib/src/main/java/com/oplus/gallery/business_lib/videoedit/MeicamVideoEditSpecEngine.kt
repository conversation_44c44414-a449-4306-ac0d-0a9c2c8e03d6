/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MeicamVideoEditSpecEngine.kt
 ** Description:
 **     {DESCRIPTION}
 **
 ** Version: 1.0
 ** Date: 2025-01-06
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>         2025/01/06  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.business_lib.videoedit

import android.content.Context
import android.os.SystemClock
import android.util.Size
import androidx.collection.LruCache
import com.meicam.sdk.NvsAVFileInfo
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsVideoStreamInfo
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter
import com.oplus.gallery.videoeditor.data.VideoSpec

/**
 * 美摄实现的视频编辑规格检查类
 */
class MeicamVideoEditSpecEngine : IVideoEditSpecEngine {
    /**
     * 缓存是否支持软解，<路径，是否支持软解>
     */
    private val supportSoftDecodeCache: LruCache<String, Boolean?> = LruCache(CACHE_SIZE)

    /**
     * 视频信息的缓存，<路径，视频信息>
     */
    private val nvsFileInfoCache: LruCache<String, NvsFileInfo> = LruCache(CACHE_SIZE)

    /**
     * Gop size 的缓存，<路径，Gop size>
     */
    private val gopSizeCache: LruCache<String, Int> = LruCache(CACHE_SIZE)

    /**
     * 检查视频是否被视频编辑支持
     * 1. 视频能被美摄解析且合法
     * 2. 视频支持美摄软解
     * 3. Gop size 小于等于 300
     */
    override fun checkSupportedByEditor(path: String, context: Context): Boolean {
        return MeicamContextAliveCounter.useContext(context, tag = TAG) { nvsContext ->
            val time = System.currentTimeMillis()

            val info: NvsFileInfo = getNvsFileInfo(nvsContext, path) ?: let {
                GLog.d(TAG, LogFlag.DL, "[checkVideoSupported] NvsFileInfo is null, timeConsuming: ${System.currentTimeMillis() - time}")
                return@useContext false
            }

            // 是否支持软解
            val softDecodeSupported = supportSoftDecodeCache[path] ?: checkSoftDecodeSupported(path, context)

            // Gop size 是否符合
            val gop: Int = getGopSize(nvsContext, path)
            val isGopSizeSupported: Boolean = (gop <= SUPPORT_EDITOR_MAX_GOP_HIGH)

            GLog.d(TAG, LogFlag.DL) {
                "[checkVideoSupported] time cost: ${System.currentTimeMillis() - time}, " +
                        "gop: $gop, " +
                        "width: ${info.size.width}, " +
                        "height: ${info.size.height}, " +
                        "softDecoded: $softDecodeSupported, " +
                        "isGopSizeValid = $isGopSizeSupported"
            }

            return@useContext softDecodeSupported && isGopSizeSupported
        } ?: false
    }

    /**
     * 判断视频是否支持美摄软解
     */
    override fun checkSoftDecodeSupported(path: String, context: Context): Boolean {
        supportSoftDecodeCache[path]?.let { return it }

        return MeicamContextAliveCounter.useContext(context, tag = TAG) { nvsContext ->
            val time = System.currentTimeMillis()
            val supportSoftDecode = nvsContext.canDecodeVideoStreamBySoftware(path)
            supportSoftDecodeCache.put(path, supportSoftDecode)

            GLog.d(TAG, LogFlag.DL) {
                "[checkVideoSoftSupported] mSupportSoftDecode = $supportSoftDecode, timeConsuming = ${System.currentTimeMillis() - time}"
            }
            return@useContext supportSoftDecode
        } ?: false
    }

    /**
     * 检查视频的规格是否被此平台支持
     * 1. 视频能被美摄解析且合法
     * 3. 视频的原始分辨率和帧率被平台支持且符合编辑条件（通过 [VideoSpecStrategy.processSpec] 判断）
     */
    override fun checkSpecValidOnPlatform(path: String, context: Context): Boolean {
        return MeicamContextAliveCounter.useContext(context, tag = TAG) { nvsContext ->
            val time = SystemClock.elapsedRealtime()

            val fileInfo = getNvsFileInfo(nvsContext, path) ?: return@useContext false
            GLog.d(TAG, LogFlag.DL) { "[checkVideoSpec] fileInfo: $fileInfo" }

            val specification = VideoSpec(fileInfo.size.width, fileInfo.size.height, fileInfo.fps)
            val processedSpecification = specification.copy()

            val videoSpecStrategy = VideoSpecStrategy()
            // processSpecification 内部会修改传入的 VideoSpecification 到合适的值。
            if (videoSpecStrategy.processSpec(fileInfo.codecMimeType, processedSpecification).not()) {
                // 超出平台能力
                GLog.w(TAG, LogFlag.DL) { "[checkVideoSpec] exceed platform abilities" }
                return@useContext false
            }
            // 检查是否被降规，如果 processedSpecification 变了则表示被将降规，相等则未被降规。
            val isSpecificationValid = processedSpecification == specification
            GLog.d(TAG, LogFlag.DL) {
                "[checkVideoSpec] isSpecificationValid: $isSpecificationValid, " + "time used: ${SystemClock.elapsedRealtime() - time}"
            }

            return@useContext isSpecificationValid
        } ?: false
    }

    /**
     * 通过美摄接口获取视频信息
     * @param path String 视频路径
     * @param context Context 上下文
     * @return NvsFileInfo 返回的信息
     */
    override fun getVideoInfo(path: String, context: Context): NvsFileInfo? {
        return MeicamContextAliveCounter.useContext(context, tag = TAG) { nvsContext ->
            getNvsFileInfo(nvsContext, path)
        }
    }

    /**
     * 通过美摄解析文件获取视频的编码 MimeType
     */
    override fun getCodecMimeType(path: String, context: Context): String? {
        return MeicamContextAliveCounter.useContext(context, tag = TAG) { nvsContext ->
            getNvsFileInfo(nvsContext, path)?.codecMimeType
        }
    }

    /**
     * 获取视频的帧率
     *
     * @param path 视频路径
     * @param context Context
     * @return 视频帧率
     */
    override fun getFps(path: String, context: Context): Float? {
        return MeicamContextAliveCounter.useContext(context, tag = TAG) { nvsContext ->
            getNvsFileInfo(nvsContext, path)?.fps
        }
    }

    /**
     * 获取视频的分辨率大小
     *
     * @param path 视频路径
     * @param context Context
     * @return 视频分辨率大小
     */
    override fun getSize(path: String, context: Context): Size? {
        return MeicamContextAliveCounter.useContext(context, tag = TAG) { nvsContext ->
            getNvsFileInfo(nvsContext, path)?.size
        }
    }

    /**
     * 获取视频信息，优先从缓存中查找，未找到则通过美摄获取并添加入缓存
     */
    private fun getNvsFileInfo(nvsContext: NvsStreamingContext, path: String): NvsFileInfo? {
        return nvsFileInfoCache[path] ?: let {
            val timeStart = SystemClock.elapsedRealtime()
            nvsContext.getAVFileInfo(path, NvsStreamingContext.AV_FILEINFO_EXTRA_INFO).toNvsFileInfo().also { nvsFileInfo ->
                if (nvsFileInfo == null) {
                    GLog.e(TAG, LogFlag.DL) { "[getNvsFileInfo] failed to get nvsFileInfo" }
                } else {
                    GLog.d(TAG, LogFlag.DL) {
                        "[getNvsFileInfo] get nvsFileInfo: $nvsFileInfo, time used: ${SystemClock.elapsedRealtime() - timeStart}"
                    }
                    nvsFileInfoCache.put(path, nvsFileInfo)
                }
            }
        }
    }

    /**
     * 将美摄的文件信息转为内部使用的文件信息数据类
     */
    private fun NvsAVFileInfo?.toNvsFileInfo(): NvsFileInfo? {
        if (this == null) {
            GLog.e(TAG, LogFlag.DL) { "[toNvsFileInfo] NvsAVFileInfo is null" }
            return null
        }
        if (videoStreamCount <= 0) {
            GLog.e(TAG, LogFlag.DL) { "[toNvsFileInfo] videoStreamCount is invalid: $videoStreamCount" }
            return null
        }
        val size = getVideoStreamDimension(0).run {
            val rotation = getVideoStreamRotation(0)
            if ((rotation == NvsVideoStreamInfo.VIDEO_ROTATION_90) || (rotation == NvsVideoStreamInfo.VIDEO_ROTATION_270)) {
                Size(height, width)
            } else {
                Size(width, height)
            }
        }
        val fps = getVideoStreamFrameRate(0).run { num / den.toFloat() }
        val mimeType = VideoSpecHelper.convertNvsCodecTypeToMimeType(getVideoStreamCodecType(0))
        return NvsFileInfo(size, fps, mimeType)
    }

    /**
     * 获取 Gop size，优先从缓存中查找，未找到则通过美摄获取并添加入缓存
     */
    private fun getGopSize(nvsContext: NvsStreamingContext, path: String): Int {
        return gopSizeCache[path] ?: let {
            val timeStart = SystemClock.elapsedRealtime()
            nvsContext.detectVideoFileKeyframeInterval(path).also { gopSize ->
                GLog.d(TAG, LogFlag.DL) { "[getGopSize] get gopSize: $gopSize, time used: ${SystemClock.elapsedRealtime() - timeStart}" }
                gopSizeCache.put(path, gopSize)
            }
        }
    }

    /**
     * 美摄解析的视频文件信息
     * ！此类会被打印
     *
     * @param size 视频的分辨率
     * @param fps 视频的帧率
     * @param codecMimeType 视频的编码 mimeType
     */
    data class NvsFileInfo(
        val size: Size,
        val fps: Float,
        val codecMimeType: String,
    )

    companion object {
        private const val TAG = "MeicamVideoEditSpecEngine"

        /**
         * 缓存的大小
         */
        private const val CACHE_SIZE = 3

        /**
         * 最高支持的 Gop size
         */
        private const val SUPPORT_EDITOR_MAX_GOP_HIGH = 300
    }
}