/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DropAlbumSetManager.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2023/05/26
 ** Author: zhuhongbo@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** zhuhongbo@Apps.Gallery3D   2023/05/26    1.0              build
 *************************************************************************************************/
package com.oplus.gallery.business_lib.util.draganddrop

import android.content.Context
import android.net.Uri
import android.view.View
import androidx.fragment.app.Fragment
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.business_lib.util.draganddrop.DropPresenter.DropCode
import com.oplus.gallery.business_lib.util.draganddrop.listener.DropEventProxy
import com.oplus.gallery.business_lib.util.draganddrop.listener.DropEventProxy.DropSupportType
import com.oplus.gallery.business_lib.util.draganddrop.listener.OnDropEventListener
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.util.debug.GLog

/**
 * drop图集选择管理类 用于处理drop接口调用，drop流程封装处理
 * 图集调用
 * @param fragment Fragment
 */
class DropAlbumSetManager(private val fragment: Fragment) : BaseDropManager(fragment) {

    init {
        fragment.lifecycle.addObserver(this)
    }
    /**
     * 设置dropListener
     * 调用时（图集tab，其他图集调用）弹出来图集选择弹框
     * @param view View
     */
    fun bindDropView(view: View) {
        if (fragment.isDetached) return
        val listener: OnDropEventListener? = object : OnDropEventListener {
            override fun onDrop(
                supportType: DropSupportType,
                clipDataList: ArrayList<Pair<String, Uri>>
            ) {
                if (fragmentIsInActive(fragment)) return
                if (supportType == DropSupportType.CODE_ALL_NOT_SUPPORT) {
                    fragment.activity?.let {
                        dropResultListener?.invoke(it, DropCode.CODE_ALL_FAIL, supportType, ArrayList())
                    }
                    return
                }
                AlbumsActionTrackHelper.trackDrop(AlbumsActionTackConstant.Value.ALBUMS_ACTION_DROP_TYPE_SELECTION)
                startDropPannel(supportType, clipDataList, onCompleted = dropResultListener)
            }
        }
        view.setOnDragListener(DropEventProxy(listener))
    }

    /**
     * drop 結果提示
     * 失败除了空间不足和媒体类型支持情况,其他暂时只有打印
     * @param context Context
     * @param code 回调结果
     * @param supportType 資源支持情況
     * @param successList drop成功的資源absolute path 列表
     */
    private val dropResultListener: ((
        context: Context,
        code: DropCode,
        supportType: DropSupportType,
        successList: ArrayList<String>
    ) -> Unit)? = { context, code, supportType, _: ArrayList<String> ->
        when {
            code == DropCode.CODE_NO_SAPCE -> showDropNoSpaceDialog(context)
            supportType == DropSupportType.CODE_ALL_NOT_SUPPORT ->
                showDropFailDialog(context, com.oplus.gallery.basebiz.R.string.drop_dialog_albumset_fail_all_desc)
            supportType == DropSupportType.CODE_PART_NOT_SUPPORT ->
                showDropFailDialog(context, com.oplus.gallery.basebiz.R.string.drop_dialog_albumset_fail_part_desc)
            else -> GLog.e(TAG, "dropResultListener-code=$code,supportType=$supportType")
        }
    }

    companion object {
        private const val TAG = "DropAlbumSetManager"
    }
}