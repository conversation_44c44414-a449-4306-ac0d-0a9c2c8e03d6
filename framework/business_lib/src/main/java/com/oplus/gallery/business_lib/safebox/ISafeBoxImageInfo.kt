/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ISafeBoxImageInfo.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/02/12
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/02/12      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.safebox

interface ISafeBoxImageInfo {
    fun isMatched(): Boolean
    fun isDeleted(): Boolean
    fun getPath(): String?
    fun getMd5(): String?
    fun getGid(): String?
    fun getFileLength(): Long
    fun getSource(): String?
}