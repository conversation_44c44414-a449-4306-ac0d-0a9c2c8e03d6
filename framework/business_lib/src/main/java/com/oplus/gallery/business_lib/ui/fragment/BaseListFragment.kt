/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : BaseListFragment.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2020/8/13 10:21
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2020/8/13      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.business_lib.ui.fragment

import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewTreeObserver
import androidx.annotation.ColorInt
import androidx.annotation.VisibleForTesting
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isInvisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GalleryGridLayoutManager
import androidx.recyclerview.widget.GalleryLinearLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.theme.COUIThemeUtils
import com.google.android.material.appbar.AppBarLayout
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.adjustControlIconColor
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity.Companion.FINISH_FRAGMENT_STACK_SIZE
import com.oplus.gallery.basebiz.uikit.fragment.ISelectionModeCallback
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.business_lib.drag.DraggableListAdapter
import com.oplus.gallery.business_lib.drag.IDraggableInterceptor
import com.oplus.gallery.business_lib.drag.ItemDragHelper
import com.oplus.gallery.business_lib.drag.OnDragCallback
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.helper.ToolbarHelper
import com.oplus.gallery.business_lib.helper.ToolbarHelper.setupToolbarWithView
import com.oplus.gallery.business_lib.model.data.base.MediaObject
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.selection.NotifyState
import com.oplus.gallery.business_lib.model.selection.OnSelectionListener
import com.oplus.gallery.business_lib.model.selection.SelectionData
import com.oplus.gallery.business_lib.viewmodel.base.DifferNotifyCallback
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.base.isNullOrEmpty
import com.oplus.gallery.business_lib.viewmodel.limit.SelectLimitType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils.getMicroThumbnailKey
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_NON_SYSTEM_ALBUM_VALUE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_SYSTEM_ALBUM_VALUE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_USER_CREATE_ALBUM_VALUE
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.cpu.PerformanceLevelUtils.cpuLevel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.findFirstVisiblePosition
import com.oplus.gallery.foundation.util.ext.findLastVisiblePosition
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.data.model.BaseModel.Companion.INVALID_INDEX
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.LoggingExceptionHandler
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.baselist.adapter.config.AbsAdapterConfigProvider
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.BaseListAdapter
import com.oplus.gallery.standard_lib.baselist.view.BaseListItemAnimator
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.baselist.view.FirstItemDrawCallback
import com.oplus.gallery.standard_lib.baselist.view.GridItemGapDecoration
import com.oplus.gallery.standard_lib.baselist.view.ISlidingSelector
import com.oplus.gallery.standard_lib.baselist.view.ItemClickListener
import com.oplus.gallery.standard_lib.baselist.view.ItemGapDecoration
import com.oplus.gallery.standard_lib.baselist.view.OnAnimationListener
import com.oplus.gallery.standard_lib.baselist.view.makeSelectModeSpec
import com.oplus.gallery.standard_lib.bean.ViewData
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.standard_lib.ui.scroller.RecyclerViewFastScroller
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.properties.Delegates

abstract class BaseListFragment<Data : MediaObject, TViewData : ViewData> :
    TemplateFragment(),
    ItemClickListener<TViewData>,
    OnSelectionListener<Path>,
    BaseListAdapter.OnSlidingSelectListener,
    ISidePaneListener,
    ISelectionModeCallback, OnDragCallback {

    companion object {
        private const val TAG = "BaseListFragment"

        /**
         * 低端机：指cpuLevel=Lower 如24281 Alpha L4
         * fragment进场动画时长为350,为了避免白屏时间太长,需要在进场动画结束前更新RV.
         * 290本地测试经验值:这个时间刚好是进场界面静止时间，小于这个时间，会感受到动画停止前会卡顿一下
         */
        private const val WAIT_SET_OBSERVER_TIMEOUT = 290L
        const val NO_ITEMS = 0
    }

    protected val logTag = "$TAG.$clazzSimpleName"
    protected var canShowSelectionMode: Boolean by Delegates.observable(true) { _, oldValue, newValue ->
        if (isSelectionMode && (oldValue != newValue)) {
            GLog.d(logTag, "canShowSelectionMode: new: $newValue")
            if (this::recyclerAdapter.isInitialized) {
                if (GProperty.DEBUG) {
                    GLog.d(
                        logTag, "canShowSelectionMode trigger notifyDataSetChanged, " +
                            "thread:${Thread.currentThread().id}:${Thread.currentThread().name}"
                    )
                }
                recyclerAdapter.notifyDataSetChanged()
            }
        }
    }

    protected var canShowCheckedMode: Boolean = true

    /**
     * 初始化canShowZoomInMode需要放置在canShowSelectionMode前
     */
    protected var canShowZoomInMode: Boolean = false

    abstract val recyclerViewId: Int

    lateinit var recyclerAdapter: BaseListAdapter<TViewData>
    lateinit var layoutDetail: LayoutDetail
    var recyclerView: RecyclerView? = null
    var baseListViewModel: ListViewModel<Data, TViewData>? = null

    //模式（外部传值）：当页面数据为空时，是否需要自动退出当前页面,默认为true
    open val needExitWhenEmpty: Boolean = true

    open val diffUpdateListener: DiffCallback<TViewData> by lazy { DiffCallback(recyclerAdapter) }

    open val supportDragItem = false

    private var isSelectionMode = false
    protected var slidingSelectProcessor: SimpleSlidingSelectProcessor? = null
    protected var itemTouchClickProcessor: SimpleItemClickProcessor? = null

    protected var fastScroller: RecyclerViewFastScroller? = null

    /**
     * 配置是否显示Fragment内部Toolbar，如果不显示，则会共用外部Toolbar
     */
    protected var internalAppbarLayout: AppBarLayout? = null
    protected val showInternalToolbar by lazy { arguments?.getBoolean(IntentConstant.AlbumConstant.KEY_HIDE_INTERNAL_TOOLBAR) != true }

    protected var itemDragHelper: ItemDragHelper? = null
    private var visibleStart = INVALID_INDEX
    private var visibleEnd = INVALID_INDEX

    protected abstract val recyclerViewPaddingBottom: Int

    /**
     * 由于打开BaseAlbumFragment(为了避免上一帧空白页面，尤其侧边导航栏切图集时尤其明显)时,
     * 默认将adapter.totalCount=viewData.totalCount,导致load完成后，totalCount和lastTotalCount相等，
     * 最后没走onTotalCountChanged流程,会有显示异常(如回收站底部菜单不显示问题)，所以首次加载需强制走刷新流程
     */
    private var isFirstLoadData = true

    /**
     * 是否已经注册liveData数据观察者
     */
    private var isRegisteredDataObserver = false

    private val viewTreeObserver by lazy { MyViewTreeObserver(clazzSimpleName) }

    private fun recordFirstDrawTime() {
        recyclerAdapter.setFirstDrawCallback(object : FirstItemDrawCallback {
            override fun firstItemBind(itemView: View) {
                if (!isFirstDraw) return
                isFirstDraw = false
                viewTreeObserver.init(itemView)
            }
        })
    }

    private class MyViewTreeObserver(val clazzSimpleName: String) : ViewTreeObserver.OnDrawListener {
        private var mView: View? = null

        fun init(view: View) {
            mView = view
            mView?.viewTreeObserver?.addOnDrawListener(this)
        }

        fun release() {
            mView?.viewTreeObserver?.removeOnDrawListener(this)
            mView = null
        }

        override fun onDraw() {
            RealShowTimeInstrument.endRecord(clazzSimpleName)
            AppScope.launch(Dispatchers.Main) { release() }
        }
    }

    @Suppress("LongMethod")
    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        recyclerView = view.findViewById(recyclerViewId)
        layoutDetail = initLayoutDetail(requireContext())
        baseListViewModel = onCreateViewModel().apply {
            spanCount = layoutDetail.spanCount
            onSetUpViewModelStyle(this)
        }
        recyclerAdapter = getListAdapter()
        recordFirstDrawTime()
        initRecyclerView()

        recyclerAdapter.onVisibleRangeChangedListener = { visibleStart, visibleEnd ->
            this.visibleStart = visibleStart
            this.visibleEnd = visibleEnd
            baseListViewModel?.setVisibleRange(visibleStart, visibleEnd)
        }
        recyclerAdapter.onRefreshStateChangedListener = { canRefresh ->
            baseListViewModel?.isActive = canRefresh
        }
        recyclerAdapter.itemClickListener = this
        recyclerAdapter.slidingSelectListener = this

        baseListViewModel?.registerOnSelectionListener(this)
        initToolbar(view)
        /*
         * 选择“所有图片”删除全部-最近删除-恢复全部-回到图集-“所有图片”图集不见了：图集TAB复用了View,所以RecycleView已经在fragment中-
         * 不会调用onScrolled-不调用judgeVisibleRange-range没有变更导致VM.reloadInfo.reloadStartPosition为-1，不能对更新后的全部mediaSet遍历
         */
        baseListViewModel?.setVisibleRange(visibleStart, visibleEnd)
        registerLiveDataObserver()
    }

    protected open fun registerLiveDataObserver() {
        setLiveDataObserver()
    }

    /**
     * 空页面时recyclerView全屏显示并覆盖在emptyView上面，导致emptyView触摸事件都无法消费
     * 空页面recyclerView可能会显示header，不能直接隐藏，高度改为wrap_content，不会覆盖到emptyView内容
     *
     * @param layoutHeight LayoutParams.MATCH_PARENT/WRAP_CONTENT
     */
    protected fun updateRVHeightIfNeed(layoutHeight: Int) {
        recyclerView?.apply {
            if (layoutParams.height != layoutHeight) {
                updateLayoutParams { height = layoutHeight }
            }
        }
    }

    /**
     * 主要处理低端机(指cpuLever=lower 如24281Aplha L4),进入图集时进场动画时RV刷新导致进入卡顿，
     * 所以需要延迟注册数据监听器observer，延迟RV刷新页面。直到进场动画（总时长350）快要结束（280）时,才设置监听执行RV刷新
     * @param force 是否立即注册liveData的observer
     */
    private fun setLiveDataObserver(force: Boolean = false) {
        if (isRegisteredDataObserver) return
        if (force.not() && isDelayRegisterObserver()) {
            GLog.w(logTag, DL) { "setLiveDataObserver: return $isTransitionAnimationRunning cpu=$cpuLevel" }
            // 目前低端机用超时机制加快RV显示，避免进场动画太长白屏时间太长；也用于防呆,避免动画异常结束,不执行回调isTransitionAnimationRunning一直为false
            view?.postDelayed({ setLiveDataObserver(true) }, WAIT_SET_OBSERVER_TIMEOUT)
            return
        }
        GLog.d(logTag, DL) { "setLiveDataObserver: force=$force" }
        isRegisteredDataObserver = true
        baseListViewModel?.activeInfoLiveData?.observe(this) { activeDataInfo ->
            val lastTotalCount = recyclerAdapter.totalCount
            val totalCount = baseListViewModel?.totalSize ?: 0
            GLog.d(logTag, DL) {
                "setLiveDataObserver.totalCount:$lastTotalCount->$totalCount first=$isFirstLoadData,cpu=$cpuLevel,${activeDataInfo.differ}"
            }

            // refresh可能触发多次，避免totalCount和lastTotalCount都为0的场景adapter不必要的刷新，可能会recycleView打断上一次的动画
            if (!isFirstLoadData && (totalCount == 0) && (lastTotalCount == 0)) {
                return@observe
            }
            var needNotifyDataSetChanged = false
            if ((totalCount == 0) || (lastTotalCount != totalCount) || isFirstLoadData) {
                isFirstLoadData = false
                recyclerAdapter.totalCount = totalCount
                /*
                 * 有diff时，后面通过动画更新ViewHolder
                 * 无diff时，直接notifyDataSetChanged更新ViewHolder，使holder与totalCount同步，避免crash
                 */
                if (activeDataInfo.differ.isNullOrEmpty()) {
                    needNotifyDataSetChanged = true
                }
                onTotalCountChanged(totalCount)
            }

            // 有diff时此处不刷新holder，而在动画结束时刷新
            recyclerAdapter.setDataSet(
                activeDataInfo.activeViewDataArray.asList(),
                refresh = activeDataInfo.differ.isNullOrEmpty()
            )

            if (needNotifyDataSetChanged) {
                recyclerAdapter.notifyDataSetChanged()
            } else {
                activeDataInfo.differ?.dispatchUpdateEventsTo(diffUpdateListener)
            }
            onViewDataChanged(activeDataInfo.activeViewDataArray)
        }

        setSelectLiveDataObserver()
        view?.post {
            // 避免子类重写此方法注册后,liveData立即回调,而view又没有初始化,导致刷新异常,所以需要post
            onPostRegisterLiveDataObserver()
        }
    }

    private fun setSelectLiveDataObserver() {
        baseListViewModel?.selectLimitLiveData?.observe(this) {
            onCheckSelectLimit(it)
        }

        baseListViewModel?.selectionMode?.observe(this) { selectionMode ->
            if (context == null) {
                GLog.e(logTag, "onEnterSelectionMode. Fragment $this not attached to a context.")
                return@observe
            }
            isSelectionMode = selectionMode
            itemDragHelper?.dragEnable = selectionMode
            itemTouchClickProcessor?.setCanShowZoomInMode(canShowZoomInMode)
            if (selectionMode) {
                itemTouchClickProcessor?.enterSelectionMode()
                slidingSelectProcessor?.enterSelectionMode(canShowSelectionMode, canShowSelectionMode)
                recyclerAdapter.setSelectionMode(makeSelectModeSpec(true, canShowSelectionMode, canShowCheckedMode, canShowZoomInMode))
                onEnterSelection()
            } else {
                itemTouchClickProcessor?.exitSelectionMode()
                slidingSelectProcessor?.exitSelectionMode()
                recyclerAdapter.setSelectionMode(makeSelectModeSpec(false, canShowSelectionMode, canShowCheckedMode, canShowZoomInMode))
                onExitSelection()
            }
            val thisFragment = this
            (activity as? BaseActivity)?.apply {
                if (isNeedPredictiveBack()
                    && (fragmentStackSize() <= FINISH_FRAGMENT_STACK_SIZE)) {
                    setBackInterception(isSelectionMode)
                    fragmentSelectStateMap[thisFragment] = isSelectionMode
                    GLog.d(TAG, DL) { "Fragment $this isSelectionMode: $isSelectionMode" }
                }
            }
        }
    }

    protected open fun onPostRegisterLiveDataObserver(): Unit = Unit

    /**
     * bug:8368860
     * 是否延期注册数据观察者
     * 低端机:进场动画结束之后才进行注册，否则动画过程中，数据加载刷新RV，会导致图集进场动画卡顿
     */
    protected open fun isDelayRegisterObserver(): Boolean = false

    protected open fun onSetUpViewModelStyle(viewModel: ListViewModel<Data, TViewData>) {
        //doNothing
    }

    protected fun isRecyclerAdapterInitialized(): Boolean = this::recyclerAdapter.isInitialized

    protected fun getAdapterTotalCount(): Int = if (isRecyclerAdapterInitialized()) recyclerAdapter.totalCount else 0

    /**
     * 获取缩图的尺寸类型
     *
     * @param itemWidth item的宽度
     */
    protected open fun getThumbSizeType(itemWidth: Int): Int {
        return getMicroThumbnailKey()
    }

    override fun onResume() {
        super.onResume()
        //处理界面在后台返回时，页面数据为空的场景
        if ((baseListViewModel?.totalSize == NO_ITEMS) && needExitWhenEmpty) {
            exitCurrentFragment()
        }
        refreshToolbar()
    }

    override fun onDestroy() {
        viewTreeObserver.release()
        super.onDestroy()
        itemDragHelper?.detach()
        itemDragHelper = null
    }

    /**
     * 初始化Toolbar，Fragment.view创建时调用
     *     主要进行设置及绑定Toolbar（有内部外部之分）操作
     */
    protected open fun initToolbar(view: View) {
        internalAppbarLayout = view.findViewById(com.oplus.gallery.basebiz.R.id.appbar_layout)
        if (showInternalToolbar) {
            internalAppbarLayout?.visibility = View.VISIBLE
            setupToolbarWithView(
                isBackEnabled = true,
                topPaddingType = ToolbarHelper.TYPE_TOOLBAR,
                targetView = recyclerView,
                isSupportImmersive = isSupportImmersive
            )
            bindToolbarSetter(generateToolbarSetter())
        } else {
            internalAppbarLayout?.visibility = View.GONE
        }
    }

    /**
     * 生成ToolbarSetter
     */
    abstract fun generateToolbarSetter(): ToolbarSetter

    /**
     * 刷新Toolbar，Fragment resume时调用
     *     主要用于进行重新绑定Toolbar操作及更新Title
     */
    protected open fun refreshToolbar() {}

    override fun onExitCurrentFragment() {
        if (isSelectionMode) {
            exitSelectionMode()
            /*
             * bug 955851
             * 当全选删除数据时，由于删除是异步操作，存在一定概率，数据回调onTotalCountChange比正常的exitSelection先，
             * 导致在执行exitSelection时直接退出了页面，而出现概率性来不及执行exitSelection，导致未刷新toolbar
             */
            onExitSelection()
            isSelectionMode = false
        }
    }

    open fun onTotalCountChanged(totalCount: Int) {}

    open fun onViewDataChanged(viewDataArray: Array<TViewData?>) {}

    open fun isPageItemSelectable(): Boolean = true

    /**
     * StaggeredGridLayoutManager conditions
     */
    private fun getLayoutManager(
        recyclerView: RecyclerView,
        context: Context,
        layoutDetail: LayoutDetail
    ): RecyclerView.LayoutManager = when {
        (layoutDetail is GridLayoutDetail) -> GalleryGridLayoutManager(recyclerView, layoutDetail)
        else -> GalleryLinearLayoutManager(context)
    }

    private fun initRecyclerView() {
        recyclerView?.apply {
            isMotionEventSplittingEnabled = false
            layoutManager = getLayoutManager(this, context, layoutDetail)
            addItemDecoration()
            adapter = recyclerAdapter
            itemAnimator = BaseListItemAnimator().also { animator ->
                animator.addAnimationListener(object : OnAnimationListener {
                    override fun onAnimationFinished() {
                        innerOnAnimationFinished()
                    }
                })
            }
            if (supportDragItem) {
                itemDragHelper ?: let {
                    (recyclerAdapter as? DraggableListAdapter)?.let { draggable ->
                        itemDragHelper = ItemDragHelper(draggable).apply {
                            attach(recyclerView!!)
                        }
                    }
                }
            }
            if (isPageItemSelectable()) {
                initSimpleSlidingSelectProcessor()
                /**
                 * addOnItemTouchListener的使用有风险，
                 * 除非在down的时候就直接拦截消费，否则每个gestureDetector内部都会在down的时候post一个400ms延迟的longPress事件，
                 * 如果后续不能正确的接收到up事件，那么longPress就会被触发
                 * 所以如果gestureDetector收到了down事件，那么最好也保证他能收到up事件，否则容易出bug
                 */
                addOnItemTouchListener(object : RecyclerView.OnItemTouchListener {
                    override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
                        slidingSelectProcessor?.processTouchEvent(e)
                        itemDragHelper?.onTouchEvent(rv, e)
                    }

                    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                        val touchClickHandled = itemTouchClickProcessor?.processInterceptTouchEvent(e) ?: false
                        val slidingSelectHandled = slidingSelectProcessor?.processInterceptTouchEvent(e) ?: false
                        val dragHelperHandled = itemDragHelper?.onInterceptTouchEvent(rv, e) ?: false
                        val isIntercept = touchClickHandled || slidingSelectHandled || dragHelperHandled
                        if (isIntercept) {
                            GLog.d(TAG, DL) {
                                "onInterceptTouchEvent event:$e touchClickHandled:$touchClickHandled " +
                                    "slidingSelectHandled:$slidingSelectHandled dragHelperHandled:$dragHelperHandled"
                            }
                        }
                        return isIntercept
                    }

                    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
                        // do nothing
                    }
                })
            } else {
                itemDragHelper?.let { addOnItemTouchListener(it) }
            }
        }
        refreshRecyclerPadding()
    }

    /**
     * 动画结束时的回调
     */
    private fun innerOnAnimationFinished() {
        GLog.d(logTag, DL) { "initRecyclerView onAnimationsFinished refresh=${recyclerAdapter.isNeedRefreshAfterAnimateFinished}" }
        /*动画过程中,正好加载数据回来后,diff=null,adapter.refreshViewHolder因为有diff会跳过数据刷新,所以动画结束后刷新数据,避免新增或变更的item不刷新
          场景:人宠图集重命名后，无法立刻显示新名称，需要退出刷新后显示
         */
        recyclerAdapter.setDataSet(refresh = recyclerAdapter.isNeedRefreshAfterAnimateFinished)
        recyclerAdapter.isNeedRefreshAfterAnimateFinished = false
    }

    open fun refreshRecyclerPadding() {
        recyclerView?.updatePadding(bottom = recyclerViewPaddingBottom)
    }

    open fun addItemDecoration() {
        recyclerView?.takeIf { it.isComputingLayout.not() }?.let {
            it.addItemDecoration(
                when {
                    (layoutDetail is GridLayoutDetail) -> GridItemGapDecoration(layoutDetail as GridLayoutDetail)
                    else -> ItemGapDecoration(layoutDetail)
                }
            )
        } ?: GLog.w(TAG, DL) {
            "addItemDecoration, isComputingLayout = ${recyclerView?.isComputingLayout}"
        }
    }

    /**
     * initSimpleSlidingSelectProcessor
     * param : isEnterSelectMode 是否进入选图模式
     */
    private fun initSimpleSlidingSelectProcessor() {
        slidingSelectProcessor = recyclerView?.let { recyclerView ->
            SimpleSlidingSelectProcessor(
                recyclerView, recyclerAdapter as ISlidingSelector,
                onItemLongClick = {
                    // 不需要当前recyclerView处理长按事件
                    if (!needHandleTouchEvent()) return@SimpleSlidingSelectProcessor
                    // 当在浮窗下，编辑模式下，当前长按图片被选中，拦截长按事件，进行拖拽分享
                    if (dispatchLongClick(it)) return@SimpleSlidingSelectProcessor

                    if ((it >= 0) && !recyclerAdapter.isHeaderOrFooter(it)) {
                        if (!<EMAIL>) {
                            AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                                enterSelect = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_ENTER_SELECT_LONG_CLICK_VALUE,
                            )
                            ClickUtil.clickable() //长按后会有概率执行ItemClick事件，所以这里更新click时间
                            <EMAIL>()
                        }
                        if (!<EMAIL>(it - recyclerAdapter.headerCount)) {
                            <EMAIL>(it - recyclerAdapter.headerCount)
                        }
                        context?.let { VibratorUtils.vibrate(it, VibratorUtils.EffectType.VIBRATE_TYPE_WEAK) }
                    } else {
                        return@SimpleSlidingSelectProcessor
                    }
                },
                findItemPositionAction = { x, y, _ ->
                    val position = recyclerView.findChildViewUnder(x, y)?.let {
                        recyclerView.getChildLayoutPosition(it)
                    } ?: findNearbyChildViewUnder(x, y)?.let {
                        recyclerView.getChildLayoutPosition(it)
                    } ?: RecyclerView.NO_POSITION

                    position
                },
                isItemSelected = { position ->
                    <EMAIL>(position)
                }
            )
        }
        itemTouchClickProcessor = recyclerView?.let { recyclerView ->
            SimpleItemClickProcessor(
                recyclerView,
                recyclerAdapter as ISlidingSelector,
                findItemPositionAction = { x, y, _ ->
                    val position = recyclerView.findChildViewUnder(x, y)?.let {
                        recyclerView.getChildLayoutPosition(it)
                    } ?: RecyclerView.NO_POSITION

                    position
                },
                onItemClickCallback = { position, clickType ->
                    if (needHandleTouchEvent() &&  ClickUtil.clickable()) { //长按后会有概率执行ItemClick事件，如果概率出现会很快执行到这里，所以判断是否能点击
                        <EMAIL>(position, recyclerAdapter.getNormalViewData(position), clickType, null)
                        true
                    } else {
                        false
                    }
                }
            )
        }
    }

    /**
     *  是否需要当前界面recyclerView处理触摸事件
     */
    protected open fun needHandleTouchEvent(): Boolean = true

    /**
     *  手指触摸的是空白区域：
     *  触摸空白区域是在照片最后一行（照片未占满一行）的空白区域
     */
    private fun findNearbyChildViewUnder(x: Float, y: Float): View? {
        val count: Int = recyclerView?.childCount ?: 0
        if (count <= 0) {
            return null
        }
        val isRtl = ResourceUtils.isRTL(context)
        for (i in count - 1 downTo 0) {
            val child: View = recyclerView?.getChildAt(i) ?: return null
            if (child.isInvisible) {
                continue
            }
            val translationX = child.translationX
            val translationY = child.translationY
            if (isRtl) {
                if ((x <= child.left + translationX) && (y >= child.top + translationY) && (y <= child.bottom + translationY)) {
                    return child
                }
            } else if ((x >= child.right + translationX) && (y >= child.top + translationY) && (y <= child.bottom + translationY)) {
                return child
            }
        }
        return null
    }

    /**
     * 单击
     * @param position
     * @param viewData
     * @param clickType
     */
    override fun onItemClick(position: Int, viewData: TViewData?, clickType: Int, itemView: View?) = Unit

    override fun onSlidingSelectStart(position: Int, needToggle: Boolean): Boolean {
        return baseListViewModel?.onSlidingSelectStart(position, needToggle) == true
    }

    override fun onSlidingSelectEnd() {
        baseListViewModel?.onSlidingSelectEnd()
    }

    override fun onSlidingSelectRangeChanged(start: Int, end: Int, lastEnd: Int, isTargetSelected: Boolean) {
        baseListViewModel?.onSlidingSelectRangeChanged(start, end, lastEnd, isTargetSelected)
    }

    open fun getListAdapter(): BaseListAdapter<TViewData> = if (supportDragItem) {
        DraggableListAdapter(
            layoutDetail,
            makeAdapterConfigProvider(),
            getDraggableInterceptor()
        ).also { adapter ->
            adapter.onDragCallback = this@BaseListFragment
        }
    } else {
        BaseListAdapter(
            layoutDetail,
            makeAdapterConfigProvider()
        )
    }

    private fun makeAdapterConfigProvider() = object : AbsAdapterConfigProvider() {
        override val configs: MutableList<ItemConfig<*>> = mutableListOf<ItemConfig<*>>().apply {
            addAll(getAdapterItemConfigs())
        }

        override fun getItemSpanSize(position: Int, viewData: ViewData?, rvSpanCount: Int): Int {
            val spanSize = getSpanSize(position, viewData, rvSpanCount)
            if (spanSize > 0) return spanSize
            return super.getItemSpanSize(position, viewData, rvSpanCount)
        }
    }

    /**
     * 是否支持拖拽
     */
    protected open fun getDraggableInterceptor(): IDraggableInterceptor<TViewData>? = null

    /**
     * 每个Item占用几列,默认占用1列
     * @param rvSpanCount RV的spanCount
     */
    protected open fun getSpanSize(position: Int, viewData: ViewData?, rvSpanCount: Int): Int = 1

    /**
     * 按item类型,配置对应的viewDataBinding
     * 如：AlbumViewData->AlbumViewDataBinding,
     *    CardCaseBannerViewData->CardCaseBannerViewDataBinding
     * @return List<ItemConfig<TViewData>>
     */
    abstract fun getAdapterItemConfigs(): List<ItemConfig<TViewData>>

    /**
     * 初始化Recycler的布局配置，如边距、间距、header、footer等
     */
    abstract fun initLayoutDetail(context: Context): LayoutDetail

    /**
     * 仅用于Fragment create时创建ViewModel
     * 在Fragment处于detach状态时调用会引发异常，业务代码不建议直接使用该方法获得ViewModel
     */
    abstract fun onCreateViewModel(): ListViewModel<Data, TViewData>

    open fun refreshLayoutManager(context: Context) {
        // grid更新网格列数
        (recyclerView?.layoutManager as? GridLayoutManager)?.let {
            val newLayoutDetail = initLayoutDetail(requireContext())
            layoutDetail.spanCount = newLayoutDetail.spanCount
            layoutDetail.itemWidth = newLayoutDetail.itemWidth
            (layoutDetail as? GridLayoutDetail)?.replaceGaps((newLayoutDetail as? GridLayoutDetail)?.itemHorizontalGaps)
            removeAllItemDecorations()
            addItemDecoration()
            /*
             * 如果页面支持拖拽且处于编辑模式，才去附着itemDragHelper（OnItemTouchListener）到RecycleView上
             * 如果不是编辑模式，那说明itemDragHelper没有附着在RecyclerView上，从而没有必要重新附着
             */
            if (supportDragItem && isSelectionMode) {
                itemDragHelper?.apply {
                    detach()
                    attach(recyclerView!!)
                }
            }
            it.spanCount = newLayoutDetail.spanCount
        }
        // 若需要COUILinearLayoutManager的更新
    }

    private fun removeAllItemDecorations() {
        recyclerView?.takeIf { (it.itemDecorationCount > 0) && (it.isComputingLayout.not()) }?.let {
            for (index in it.itemDecorationCount - 1 downTo 0) {
                it.removeItemDecoration(it.getItemDecorationAt(index))
            }
        } ?: GLog.w(logTag, DL) {
            "removeAllItemDecorations, itemDecorationCount = ${recyclerView?.itemDecorationCount}"
        }
    }

    protected fun setExtraFixHeadListCount(count: Int) {
        (recyclerAdapter as? DraggableListAdapter)?.extraFixHeadListCount = count
    }

    override fun onSelectItemChange(position: Int, selected: Boolean) {
        if (context == null) {
            GLog.e(logTag, "onSelectionChange. Fragment $this not attached to a context.")
            return
        }
        val fixPosition = position + recyclerAdapter.headerCount
        val viewHolder = recyclerView?.findViewHolderForAdapterPosition(fixPosition)
        val viewData = (viewHolder as? BaseListViewHolder<*>)?.viewDataBinding
        // 滑选自动滚动状态下，关闭checkBox动画
        viewData?.setChecked(selected, !recyclerAdapter.isAutoScrolling)
        if ((viewHolder == null) || (viewData == null)) {
            GLog.e(
                logTag, "onSelectionChange. position=$position, selected=$selected, viewHolder=$viewHolder, viewData=$viewData, " +
                    "thread:${Thread.currentThread().id}:${Thread.currentThread().name}"
            )
            /**
             * 解决BUG：6250600
             * 在recyclerview的dispatchLayoutStep2的阶段，是可能在滑动的过程中在主线程触发onSelectionStateChange回调进而触发数据更新，
             * 而此时dispatchLayoutStep2没有执行完，因此抛出导致抛出异常
             * 兜底策略：若仍然抛出异常，则捕获(LoggingExceptionHandler)，避免App Crash
             */
            lifecycleScope.launch(Dispatchers.UI + LoggingExceptionHandler) {
                recyclerAdapter.notifyItemChanged(fixPosition)
            }
        }
        onSelectionStateChange()
    }

    override fun onSelectItemsChange(positions: Set<Int>, selected: Boolean)  = Unit

    override fun onSelectionChange(item: Path?, state: NotifyState) {
        view?.post {
            if (isSelectionMode) {
                if (context == null) {
                    GLog.e(logTag, "onAllSelectionChange. Fragment $this not attached to a context.")
                    return@post
                }
                if (!recyclerAdapter.visibleRange.isInvalid()) {
                    if (GProperty.DEBUG) {
                        GLog.d(
                            logTag, "onSelectionChange trigger notifyItemRangeChanged, " +
                                "thread:${Thread.currentThread().id}:${Thread.currentThread().name}"
                        )
                    }
                    /**
                     * 解决BUG：6250600
                     * 在recyclerview的dispatchLayoutStep2的阶段，是可能在滑动的过程中在主线程触发onSelectionStateChange回调进而触发数据更新，
                     * 而此时dispatchLayoutStep2没有执行完，因此抛出导致抛出异常
                     * 兜底策略：若仍然抛出异常，则捕获(LoggingExceptionHandler)，避免App Crash
                     */
                    lifecycleScope.launch(Dispatchers.UI + LoggingExceptionHandler) {
                        /*
                         * 解决BUG：9441255
                         * 在全选态下选择或取消，存在部分可视范围外的图集项在滑动到可视范围后，没有走onBindViewHolder方法，其选择状态未更新
                         * 兜底策略：选择状态改变时更新全部图集项
                         */
                        recyclerAdapter.notifyItemRangeChanged(0, recyclerAdapter.totalCount)
                    }
                }
                onSelectionStateChange()
            }
        }
    }

    override fun onBackPressed(): Boolean {
        return if (isSelectionMode && isResumed) {
            if ((recyclerAdapter as? DraggableListAdapter)?.isDragging != true) {
                exitSelectionMode()
            }
            false
        } else {
            super.onBackPressed()
        }
    }

    protected open fun onEnterSelection() {
        // nothing to do
    }

    protected open fun onExitSelection() {
        // nothing to do
    }

    /**
     * 单选或全选触发，选择状态变化，可复写此方法，实现UI改动
     */
    protected open fun onSelectionStateChange() {}

    override fun enterSelectionMode() = baseListViewModel?.enterSelectionMode() ?: SelectionData.INVALID_ID

    protected fun enterSelectionMode(selectionDataId: Int): Unit? = baseListViewModel?.enterSelectionMode(selectionDataId)

    override fun exitSelectionMode() {
        baseListViewModel?.exitSelectionMode()
    }

    override fun isInSelectionMode() = isSelectionMode

    protected fun toggleItemSelection(position: Int): Boolean = baseListViewModel?.toggleItemSelection(position) == true

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    open fun selectItem(position: Int, needNotify: Boolean = true): Boolean = baseListViewModel?.selectItem(position, needNotify) == true

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    fun unselectItem(position: Int, needNotify: Boolean = true): Boolean = baseListViewModel?.unselectItem(position, needNotify) == true

    protected fun selectAll(): Unit? = baseListViewModel?.selectAll()

    protected fun unselectAll(): Unit? = baseListViewModel?.unselectAll()

    protected fun isItemSelected(position: Int): Boolean = baseListViewModel?.isItemSelected(position) == true

    protected fun getSelectedItems(): Set<Path>? = baseListViewModel?.getSelectedItems()

    protected fun getSelectedItemCount(): Int? = baseListViewModel?.getSelectedItemCount()

    protected fun isSelectAll(): Boolean = baseListViewModel?.isSelectAll() == true

    protected fun getSelectedItemSpecifiedCount(getSpecifiedCount: (Bundle) -> Unit) {
        baseListViewModel?.getSelectedItemImageAndVideoCount(getSpecifiedCount)
    }
    protected fun submitSelection(completedHandle: (Set<Path>) -> Unit): Unit? = baseListViewModel?.submitSelection(completedHandle)

    protected open fun onCheckSelectLimit(selectLimitType: SelectLimitType): Unit = Unit

    /**
     * 处理fragment间切换时viewmodel 任务切换
     */
    override fun supportClickStatusBar(): Boolean = true

    override fun onStatusBarClicked() {
        super.onStatusBarClicked()
        // 在划选过程中不支持回顶操作，否则当数据量过大时，会导致anr
        if (slidingSelectProcessor?.isSlidingSelecting == true) {
            return
        }
        recyclerView?.let { recyclerView ->
            val firstVisiblePosition = recyclerView.layoutManager?.findFirstVisiblePosition() ?: 0
            recyclerView.scrollToPosition(firstVisiblePosition.coerceAtMost(recyclerView.childCount))
            recyclerView.post {
                recyclerView.smoothScrollToPosition(0)
            }
        }
    }

    override fun onClickSelectedTabAgain() {
        if (isPositiveOrder()) {
            // 在划选过程中不支持回顶操作，否则当数据量过大时，会导致anr
            if (slidingSelectProcessor?.isSlidingSelecting == true) {
                return
            }
            val maxPosition = getMaxScrollPosition().coerceAtLeast(0)
            recyclerView?.let { recyclerView ->
                val lastVisiblePosition = recyclerView.layoutManager?.findLastVisiblePosition() ?: 0
                recyclerView.scrollToPosition(lastVisiblePosition.coerceAtLeast(maxPosition - recyclerView.childCount))
                recyclerView.post {
                    recyclerView.smoothScrollToPosition(maxPosition)
                }
            }
        } else {
            onStatusBarClicked()
        }
    }

    /**
     * 获取最大滚动下标
     * 点击回归最新位置，最新图片显示在下方时用到
     *
     * 若是回收站界面，因为有提示提示文本在最下方，返回recyclerAdapter.totalCount
     * 其他界面，无提示文本，返回recyclerAdapter.totalCount - 1
     *
     * (scrollToPosition允许传入除-1以外任何下标值，如-10、0、100、10000)
     */
    open fun getMaxScrollPosition(): Int {
        return (recyclerAdapter.totalCount - 1).coerceAtLeast(0)
    }

    override fun isRecycleViewAnimating(): Boolean {
        return recyclerView?.isAnimating ?: false
    }

    /**
     * 此方法主要用于图集列表移除当前列表的操作，进而控制在有loading框时不刷新数据，直到框消失后
     * （包含但不仅限于移入移除其他人物图集、设为私密、删除、恢复、添加到、合并等）
     */
    protected open fun onRemoveActionCallback(path: List<Path>?, state: Int) {
        GLog.d(logTag, "onRecycleActionCallback, state: $state")
        baseListViewModel?.needSkipRefreshData = (state == BottomMenuHelper.ACTION_STATE_START)
    }

    protected open fun dispatchLongClick(position: Int): Boolean {
        return false
    }

    protected fun updateAlbumType(viewData: AlbumViewData): String {
        val isSelfAlbum = viewData.supportedAbilities?.getBoolean(AlbumViewData.SUPPORT_IS_SELF_ALBUM, false) ?: false
        val bucketPath =
            viewData.supportedAbilities?.getString(SlotOverlayHelper.SUPPORT_BUCKET_PATH, TextUtil.EMPTY_STRING) ?: TextUtil.EMPTY_STRING
        return if (isSelfAlbum) {
            ALBUMS_ACTION_USER_CREATE_ALBUM_VALUE
        } else if (!bucketPath.isBlank() && !bucketPath.startsWith(Dir.DCIM.bucketPath, true)) {
            ALBUMS_ACTION_NON_SYSTEM_ALBUM_VALUE
        } else {
            ALBUMS_ACTION_SYSTEM_ALBUM_VALUE
        }
    }

    open class DiffCallback<TViewData : ViewData>(protected val recyclerAdapter: BaseListAdapter<TViewData>) :
        DifferNotifyCallback<BaseListViewHolder<TViewData>>(recyclerAdapter) {
        override fun getItemPosition(position: Int): Int {
            return position + recyclerAdapter.headerCount
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        //onAppUiStateChanged不响应某些系统UI变化，如不响应亮暗色变化，需要在此处更新style
        baseListViewModel?.let { onSetUpViewModelStyle(it) }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        GLog.d(logTag, "onAppUiStateChanged,windowWidth:${config.windowWidth} windowHeight:${config.windowHeight}")
        // 屏幕宽有变化都重置ViewData缓存，再重新加载缓存
        if (config.windowWidth.isChanged() || config.screenMode.isChanged()) {
            refreshLayoutManager(requireContext())
            baseListViewModel?.let {
                if (needReleaseViewDataOnSizeChange()) {
                    onSetUpViewModelStyle(it)
                    // 释放缓存的ViewData,并重置range,以便adapter重新判断visibleRange之后回调给VM去重新加载ViewData并更新数据给adapter来更新界面
                    it.releaseViewDataAndRange()
                }
            }
            recyclerAdapter.resetAndJudgeVisibleRange()
        }
    }

    /**
     * 是否需要释放viewData
     *
     * 目前是判断缩图size不变就不需要释放，在屏幕宽度或者大小变化的时候调用
     */
    protected open fun needReleaseViewDataOnSizeChange(): Boolean = true

    override fun createSidePaneListener(): ISidePaneListener? = this

    override fun isSidePaneEnabled(): Boolean = sidePaneAccessor.isSupportSidePane()

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        /* 虚拟按键状态下，contentView会添加底部padding，菜单控件无需处理
         非虚拟按键有导航条情况下，contentView底部padding会被设为0，NavigationView会跟底部导航条重叠，为避免重叠将NavigationView的marginBottom的高度设为导航条的高度
         原来是使用的padding，padding是在自身限定高度做内部间距调整，设计是希望菜单栏在导航条之上不与导航条重叠，改为margin*/
        bottomMenuBar?.updateMargin(bottom = getCurrentGestureBarHeight())
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = BaseListSystemBarStyle()

    open inner class BaseListSystemBarStyle(
        private val mockNaviEnable: Boolean = true
    ) : FragmentSystemBarStyle(this) {
        override fun onInit() {
            setMockNaviBarEnable(mockNaviEnable)
            setMockNaviBarColor(COUIThemeUtils.getThemeAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorBackgroundWithCard))
        }

        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            if (isForeground && isCanUpdateStatusAndSideIcon()) {
                setStatusBarAppearance(isStatusBarLightAppearance())
                sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint())
            }
            windowInsets.naviBarInsets().apply {
                val statusHeight = statusBarHeight()
                val topPadding = if (isSupportImmersive) 0 else statusHeight
                val bottomPadding: Int = if (hasVirtualKey()) bottom else 0
                setContentPadding(left, topPadding, right, bottomPadding)
            }

            windowInsets.naviBarInsets().apply {
                setNaviBarColor(Color.TRANSPARENT)
                if (bottomMenuBar?.isVisible() == true) {
                    setCurrentNaviBarColor(context?.getColor(com.support.appcompat.R.color.coui_color_bottom_bar) ?: 0)
                } else {
                    setCurrentNaviBarColor(Color.TRANSPARENT)
                }
            }
        }

        /**
         * 是否可以更新状态栏的颜色和侧边栏的icon
         */
        protected open fun isCanUpdateStatusAndSideIcon(): Boolean = true

        private fun setCurrentNaviBarColor(@ColorInt color: Int) {
            if (mockNaviEnable) {
                setMockNaviBarColor(color)
            } else {
                setNaviBarColor(color)
            }
        }
    }
}
