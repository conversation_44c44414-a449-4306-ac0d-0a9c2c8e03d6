/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RenameAlbumActionBuilder.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2021/10/15
 ** Author:xiweinan@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xiweinan@Apps.Gallery3D   	2021/10/15	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.builder

import android.content.Context
import com.oplus.gallery.business_lib.menuoperation.RenameAlbumAction
import com.oplus.gallery.foundation.util.text.TextUtil
import java.lang.ref.WeakReference

class RenameAlbumActionBuilder : ActionBuilder() {
    private var contextRef: WeakReference<Context>? = null
    private var srcAlbumName: String = TextUtil.EMPTY_STRING
    private var folderPath: String? = null
    private var bucketIds: List<Int> = emptyList()

    fun setContext(context: Context): RenameAlbumActionBuilder = apply { contextRef = WeakReference(context) }

    fun setAlbumName(albumName: String): RenameAlbumActionBuilder = apply { srcAlbumName = albumName }

    fun setFolderPath(path: String): RenameAlbumActionBuilder = apply { folderPath = path }

    fun setBucketId(ids: List<Int>): RenameAlbumActionBuilder = apply { bucketIds = ArrayList(ids) }

    override fun innerBuild(): HashMap<String, Any?> = HashMap<String, Any?>().apply {
        put(RenameAlbumAction.KEY_CONTEXT_REF, contextRef)
        put(RenameAlbumAction.KEY_ALBUM_NAME, srcAlbumName)
        put(RenameAlbumAction.KEY_FOLDER_PATH, folderPath)
        put(RenameAlbumAction.KEY_BUCKET_ID, bucketIds)
    }
}