/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description: 支持选择模式的卡片类图集项视图设置
 **
 ** Version: 1.0
 ** Date: 2025/4/24
 ** Author: 80407954@OppoGallery3D
 ** TAG: create this class
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80407954@OppoGallery3D  2025/4/24  1.0
 *********************************************************************************/
package com.oplus.gallery.business_lib.ui.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isGone
import com.coui.appcompat.checkbox.COUICheckBox
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.isSelected
import com.oplus.gallery.basebiz.uikit.isShowCount
import com.oplus.gallery.basebiz.uikit.isSupportDelete
import com.oplus.gallery.basebiz.widget.ViewStubExpand
import com.oplus.gallery.business_lib.helper.TalkBackHelper
import com.oplus.gallery.business_lib.viewmodel.style.IStylePool
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.FAVORITE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.MAP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SCREEN_SHOT_ALBUM_ID
import com.oplus.gallery.foundation.util.ext.updateSize
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.baselist.view.ListViewDataBinding
import com.oplus.gallery.standard_lib.baselist.view.isSelectMode

/**
 * 支持选择模式的卡片类图集项视图设置
 */
class EditableCardAlbumSetViewDataBinding(
    context: Context,
    stylePool: IStylePool?,
    private val isMaskVisibleGet: (() -> Boolean) = { false },
    private val checkboxAnimEnableGet: (() -> Boolean) = { false }
)  : CardAlbumSetViewDataBinding(context, stylePool) {
    /**
     * 是否可以显示遮罩
     */
    private val isMaskVisible get() = isMaskVisibleGet.invoke()

    /**
     * 是否使用复选框动画
     */
    private val useCheckboxAnim get() = checkboxAnimEnableGet.invoke()

    private var vbCheckbox: ViewStubExpand<COUICheckBox>? = null

    override fun onCreateView(parent: ViewGroup, viewType: Int): View {
        return super.onCreateView(parent, viewType).apply {
            vbCheckbox = ViewStubExpand(findViewById(R.id.cv_card_checkbox))
            imageCover?.updateSize(width = context.resources.getDimensionPixelOffset(R.dimen.label_album_set_item_cover_width))
        }
    }

    override fun copyLayout(context: Context): ListViewDataBinding<AlbumViewData> {
        return EditableCardAlbumSetViewDataBinding(
            context,
            stylePool,
            isMaskVisibleGet,
            checkboxAnimEnableGet
        )
    }

    override fun setChecked(checked: Boolean, animated: Boolean) {
        vbCheckbox?.requireView()?.isChecked = checked
    }

    override fun onBindViewHolder(itemViewHolder: BaseListViewHolder<AlbumViewData>, position: Int, viewData: AlbumViewData?) {
        val view = itemViewHolder.view
        // 未加载出mediaSet数据时，不显示
        if (viewData == null) {
            view.visibility = View.INVISIBLE
            return
        }
        view.apply {
            visibility = View.VISIBLE
            tag = viewData
        }
        imageCover?.updateSize(width = context.resources.getDimensionPixelOffset(R.dimen.label_album_set_item_cover_width))
        updateTitleText(viewData)
        TalkBackHelper.setItemDescriptionForAlbumSet(
            view,
            textTitle,
            textSubtitle,
            isEditMode = isSelectMode(selectModeSpec),
            isChecked = vbCheckbox?.getView()?.isChecked ?: false,
            needUnit = true,
            isEnable = true
        )
        if (needShowCheckbox(viewData) || (vbCheckbox?.getView() != null)) {
            changeCheckboxVisibility(needShowCheckbox(viewData))
            setChecked(viewData.isSelected(), useCheckboxAnim)
        }
        val resultDrawable = processDrawable(
            viewData.thumbnail?.run {
                processThumbnailLoaded(this, viewData, view)
                content
            },
            viewData
        )
        setContentDrawable(resultDrawable)
        val itemIsSelectionEnable =
            viewData.supportedAbilities?.getBoolean(SlotOverlayHelper.SUPPORT_SELECTION_ENABLE, true) ?: true
        //添加蒙层功能
        imageCover?.isMaskVisible =
            (<EMAIL> && (vbCheckbox?.getView()?.isChecked ?: false)) || !itemIsSelectionEnable
    }

    /**
     * 是否显示复选框
     */
    private fun needShowCheckbox(viewData: AlbumViewData): Boolean = isSelectMode(selectModeSpec) &&
            (viewData.isSupportDelete() || (viewData.albumId == SCREEN_SHOT_ALBUM_ID)
                    || (viewData.albumId == FAVORITE_ALBUM_ID) || (viewData.albumId == MAP_ALBUM_ID))

    /**
     * 更新图集信息
     */
    override fun updateTitleText(viewData: AlbumViewData) {
        super.updateTitleText(viewData)
        //地图和卡证档案数量为0不显示个数
        textSubtitle?.isGone = viewData.isShowCount(viewData).not()
    }

    /**
     * 刷新复选框显示状态
     */
    private fun changeCheckboxVisibility(isCheckboxShow: Boolean) {
        vbCheckbox?.requireView()?.visibility = if (isCheckboxShow) View.VISIBLE else View.GONE
    }
}