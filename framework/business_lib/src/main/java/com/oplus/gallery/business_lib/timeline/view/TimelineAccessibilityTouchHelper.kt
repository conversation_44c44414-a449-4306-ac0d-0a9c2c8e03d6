/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : TimelineAccessibilityTouchHelper.kt
 ** Description : 时间轴无障碍适配对象
 ** Version     : 1.0
 ** Date        : 2023/2/10 10:32
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2023/2/10  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.business_lib.timeline.view

import android.graphics.Rect
import android.os.Bundle
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import androidx.customview.widget.ExploreByTouchHelper
import androidx.customview.widget.ExploreByTouchHelper.INVALID_ID
import com.oplus.gallery.business_lib.timeline.layout.OnElementClickListener
import com.oplus.gallery.business_lib.timeline.view.PresentationAccessibilityTouchHelper.Companion.INVALID_VIRTUAL_ITEM_ID
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.baselist.view.makeSelectModeSpec

/**
 * 时间轴无障碍适配对象
 */
class TimelineAccessibilityTouchHelper(private val timelineView: TimelineView) : ExploreByTouchHelper(timelineView) {
    override fun getVirtualViewAt(x: Float, y: Float): Int {
        var virtualViewId = INVALID_VIRTUAL_ITEM_ID
        timelineView.curPresentation().layouter.dispatchClickEvent(
            x.toInt(),
            y.toInt() + timelineView.scrollY,
            ignoreTitle = false,
            alternateLast = false,
            selectModeSpec = makeSelectModeSpec(timelineView.isSelectionMode, timelineView.canShowSelectionMode),
            clickListener = object : OnElementClickListener {
                override fun onElementClick(nodeIndex: Int, itemIndex: Int, elementType: String, extra: Bundle) {
                    timelineView.curPresentation().accessibilityExplorer?.generateVirtualItemIfNeed(nodeIndex, itemIndex, elementType)?.apply {
                        virtualViewId = this.id
                    }
                }
            })

        return virtualViewId
    }

    override fun getVisibleVirtualViews(virtualViewIds: MutableList<Int>) {
        timelineView.curPresentation().accessibilityExplorer?.apply {
            // 更新可见虚拟Views前需要清除旧缓存，避免不断累积
            clear()

            getVisibleVirtualItems().forEach {
                virtualViewIds.add(it.id)
            }
        }
    }

    override fun onPerformActionForVirtualView(virtualViewId: Int, action: Int, arguments: Bundle?): Boolean {
        return false
    }

    @Suppress("deprecation")
    override fun onPopulateNodeForVirtualView(virtualViewId: Int, node: AccessibilityNodeInfoCompat) {
        node.className = TimelineView::class.java.name
        var description = TextUtil.EMPTY_STRING
        val rect = Rect()
        timelineView.curPresentation().accessibilityExplorer?.getVirtualItemWithProperties(virtualViewId)?.let { item ->
            description = item.description
            rect.set(item.rect)
            item.actions.forEach { action ->
                node.addAction(action)
            }
        }
        // 下列多项必须设置初始值，否则会crash
        node.contentDescription = description
        node.setBoundsInParent(rect)
    }
}

abstract class PresentationAccessibilityTouchHelper {
    private val virtualItemCache: MutableMap<Int, VirtualItem> = mutableMapOf()

    /**
     * 缓存中不存在的话生成新的虚拟Item
     *
     * @param nodeIndex 虚拟Item的节点索引
     * @param itemIndex 虚拟Item的全局索引
     * @param elementType 虚拟Item的类型
     * @return 生成的虚拟Item对象
     */
    fun generateVirtualItemIfNeed(nodeIndex: Int, itemIndex: Int, elementType: String): VirtualItem? {
        val virtualViewId = generateVirtualViewId(nodeIndex, itemIndex, elementType)
        if ((virtualViewId != INVALID_ID) && !virtualItemCache.containsKey(virtualViewId)) {
            virtualItemCache[virtualViewId] = VirtualItem(virtualViewId, nodeIndex, itemIndex, elementType)
        }
        return virtualItemCache[virtualViewId]
    }

    /**
     * 获取缓存中的虚拟Item
     *
     * @param virtualViewId 虚拟Item的ID
     * @return 虚拟Item对象
     */
    fun getVirtualItemWithProperties(virtualViewId: Int): VirtualItem? {
        return virtualItemCache[virtualViewId]?.apply {
            fillProperties(this)
        }
    }

    /**
     * 清空虚拟Item缓存
     */
    fun clear() {
        virtualItemCache.clear()
    }

    /**
     * 生成虚拟Item的唯一ID
     *
     * @param nodeIndex 虚拟Item的节点索引
     * @param itemIndex 虚拟Item的全局索引
     * @param elementType 虚拟Item的类型
     * @return 生成的唯一ID
     */
    abstract fun generateVirtualViewId(nodeIndex: Int, itemIndex: Int, elementType: String): Int

    /**
     * 为VirtualItem填充播报属性
     */
    abstract fun fillProperties(item: VirtualItem)

    /**
     * 获取所有可见的虚拟Item对象
     */
    abstract fun getVisibleVirtualItems(): List<VirtualItem>

    companion object {
        const val INVALID_VIRTUAL_ITEM_ID = INVALID_ID
    }
}

data class VirtualItem(
    val id: Int,
    val nodeIndex: Int,
    val itemIndex: Int,
    val elementType: String,
    var description: String = TextUtil.EMPTY_STRING,
    val rect: Rect = Rect(),
    val actions: MutableList<Int> = mutableListOf()
)