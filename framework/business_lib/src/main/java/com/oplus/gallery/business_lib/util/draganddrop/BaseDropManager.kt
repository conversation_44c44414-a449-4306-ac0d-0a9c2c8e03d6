/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseDropManager.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2023/05/26
 ** Author: zhuhongbo@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** zhuhongbo@Apps.Gallery3D   2023/05/26    1.0              build
 *************************************************************************************************/
package com.oplus.gallery.business_lib.util.draganddrop

import android.content.Context
import android.net.Uri
import androidx.annotation.WorkerThread
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.helper.StorageTipsHelper
import com.oplus.gallery.business_lib.fileoperation.BackupEntry
import com.oplus.gallery.business_lib.helper.SelfAlbumHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.favorite.FavoritesDataManager
import com.oplus.gallery.business_lib.model.data.favorite.FavoritesProviderHelper
import com.oplus.gallery.business_lib.util.draganddrop.DropPresenter.DropCode
import com.oplus.gallery.business_lib.util.draganddrop.listener.DropEventProxy.DropSupportType
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.file.Dir.Companion.CAMERA
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * drop管理类基类
 * @param fragment Fragment
 */
abstract class BaseDropManager(private val fragment: Fragment) : DefaultLifecycleObserver {
    private var progressDialog: DropProgressDialogController? = null
    private val dropPresenter = DropPresenter()
    private var dropPanelDialog: DropPanelDialogController? = null

    private val cancelCallBack = {
        dropPresenter.cancel()
    }

    /**
     * 初始化
     * @param owner LifecycleOwner
     */
    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        GLog.d(TAG, "onCreate")
        progressDialog = DropProgressDialogController(fragment,
            fragment.activity?.getString(BasebizR.string.drop_loading_dialog_tilte), cancelCallBack)
    }

    /**
     * 释放资源
     */
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        GLog.d(TAG, "onDestroy")
        progressDialog?.release()
        progressDialog = null
    }

    /**
     * 启动弹框选择图集, 点击图集进行保存
     * 图集列表和虚拟图集，要弹框选择图集
     * @param supportType 媒体类型支持情况
     * @param clipDataList 资源列表
     * @param isFavorite 是否收藏，默认否
     */
    protected fun startDropPannel(
        supportType: DropSupportType,
        clipDataList: ArrayList<Pair<String, Uri>>,
        isFavorite: Boolean = false,
        onCompleted: ((context: Context, code: DropCode, supportType: DropSupportType, successList: ArrayList<String>) -> Unit)?
    ) {
        val uris = clipDataList.map { it.second }
        val itemsMediaType = getMediaType(clipDataList)

        dropPanelDialog ?: let {
            dropPanelDialog = DropPanelDialogController(fragment)
        }
        dropPanelDialog?.startPanelDialog(uris, itemsMediaType) { targetAlbumPath, selfPath, targetAlbumName ->
            GLog.d(TAG, "DropAlbumSelectionManager targetAlbumName=$targetAlbumName,targetAlbumPath=$targetAlbumPath")
            if (fragmentIsInActive(fragment)) return@startPanelDialog
            val albumPath = targetAlbumPath?.let {
                pathToString(targetAlbumPath, targetAlbumName)
            } ?: let {
                selfPath
            }
            dropBatchFile(supportType, clipDataList, albumPath, isFavorite, onCompleted = onCompleted)
        }
    }

    /**
     * 批量drop文件
     * @param supportType drop的媒体类型支持情况
     * @param clipDataList drop的数据
     * @param targetAlbumPath 目标绝对路径
     * @param onCompleted 结果回调
     */
    protected fun dropBatchFile(
        supportType: DropSupportType,
        clipDataList: ArrayList<Pair<String, Uri>>,
        targetAlbumPath: String?,
        isFavorite: Boolean = false,
        onCompleted: ((context: Context, code: DropCode, supportType: DropSupportType, successList: ArrayList<String>) -> Unit)?
    ) {
        progressDialog?.setProgressMax(clipDataList.size)
        progressDialog?.showProgressDialog()
        fragment.lifecycleScope.launch(Dispatchers.IO) {
            dropPresenter.dropBatchFile(clipDataList, targetAlbumPath, { progress ->
                fragment.lifecycleScope.launch(Dispatchers.UI) {
                    if (fragmentIsInActive(fragment)) return@launch
                    GLog.d(TAG, "dropBatchFile-progress=$progress")
                    progressDialog?.updateProgress(progress ?: 0)
                }
            }) { dropCode, resultList ->
                if (dropCode == DropCode.CODE_SUCCESS || dropCode == DropCode.CODE_PART_FAIL) addToFavorites(isFavorite, resultList)
                fragment.lifecycleScope.launch(Dispatchers.UI) {
                    if (fragmentIsInActive(fragment)) return@launch
                    progressDialog?.hideProgressDialog()
                    fragment.activity?.let {
                        onCompleted?.invoke(it, dropCode, supportType, resultList)
                    }
                }
            }
        }
    }

    /**
     * 添加收藏
     * @param isFavorite 是否收藏
     * @param successList 收藏的资源
     */
    @WorkerThread
    private fun addToFavorites(isFavorite: Boolean, successList: ArrayList<String>) {
        if (isFavorite) {
            val entryList = ArrayList<FavoritesProviderHelper.FavoritesEntry>()
            for (data in successList) {
                val entry = FavoritesProviderHelper.FavoritesEntry()
                entry.mData = data
                entryList.add(entry)
            }
            val favoriteCount = FavoritesDataManager.getInstance().addToFavorites(entryList)
            GLog.d(TAG, "dropBatchFile favoriteCount=$favoriteCount")
        }
    }

    private fun pathToString(targetAlbumPath: Path, targetAlbumName: String?): String? {
        val isCameraAlbum = SourceConstants.Local.PATH_ALBUM_CAMERA_ANY == targetAlbumPath
        return if (isCameraAlbum) CAMERA.internalPath else {
            val albumPath = targetAlbumName?.let {
                val root = OplusEnvironment.getInternalPath()
                root ?: return null
                SelfAlbumHelper.getSelfAlbumDir(null, root, it)
            } ?: let {
                BackupEntry.getAlbumPath(targetAlbumPath)
            }
            return albumPath
        }
    }

    protected fun getMediaType(clipDataList: ArrayList<Pair<String, Uri>>): Int {
        val listMimeType = clipDataList.map { it.first }
        val hasImage = listMimeType.any { mimeType -> MimeTypeUtils.isImage(mimeType) }
        val hasVideo = listMimeType.any { mimeType -> MimeTypeUtils.isVideo(mimeType) }
        return when {
            hasImage && hasVideo -> Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_ALL
            hasImage -> Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_IMAGE
            hasVideo -> Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_VIDEO
            else -> Constants.IMediaTypeSupport.MEDIA_TYPE_SUPPORT_UNKNOWN
        }
    }

    /**
     * fragment和activity是否有效
     * @param fragment Fragment
     */
    protected fun fragmentIsInActive(fragment: Fragment): Boolean {
        if (fragment.isDetached) return true
        val activity = fragment.activity
        if ((activity?.isDestroyed != false) || (activity.isFinishing)) return true
        return false
    }

    /**
     * drop磁盘没有空间，引导清理
     * @param context Context
     */
    protected fun showDropNoSpaceDialog(context: Context) {
        StorageTipsHelper.show(
            context,
            OplusEnvironment.StorageType.PHONE_STORAGE,
            StorageLimitHelper.State.NO_SPACE, BasebizR.string.base_send_no_storage_space
        )
    }
    /**
     * drop错误提示
     * @param context Context
     * @param messageResId 提示的资源id
     */
    protected fun showDropFailDialog(context: Context, messageResId: Int) {
        ConfirmDialog.Builder(context)
            .setMessage(messageResId)
            .setNegativeButton(BasebizR.string.common_ok)
            .setCanceledOnTouchOutside(true)
            .setTitle(BasebizR.string.drop_dialog_fail_tilte)
            .build()
            .show()
    }

    companion object {
        private const val TAG = "BaseDropManager"
    }
}