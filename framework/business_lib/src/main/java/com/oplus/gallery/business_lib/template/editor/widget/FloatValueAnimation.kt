/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - FloatValueAnimation.kt
 * Description:
 * Version:
 * Date:
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2021/7/7     2.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.template.editor.widget

import android.view.animation.Animation
import android.view.animation.Transformation

class FloatValueAnimation(
    var start: Float,
    var end: Float,
    val updateCallback: (Float) -> Unit
) : Animation() {

    override fun applyTransformation(interpolatedTime: Float, t: Transformation?) {
        val current = start + (end - start) * interpolatedTime
        updateCallback(current)
    }
}