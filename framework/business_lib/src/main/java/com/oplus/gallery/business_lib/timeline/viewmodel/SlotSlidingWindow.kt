/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SlotSlidingWindow.kt
 * Description:
 * Version: 1.0
 * Date: 2020/8/18
 ** Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * biao.chen@Apps.Gallery3D      2020/8/18      1.0          build this module
 *************************************************************************************************/
package com.oplus.gallery.business_lib.timeline.viewmodel

import android.graphics.Bitmap
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.basebiz.task.TaskMapManager
import com.oplus.gallery.business_lib.R
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.KEY_IMAGE_COUNT
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.SpecifiedCount.KEY_VIDEO_COUNT
import com.oplus.gallery.business_lib.timeline.viewmodel.BaseSlidingWindow.FakeBitmapProvider
import com.oplus.gallery.business_lib.util.SlidingRangeHelper.calculateActiveRangeOfVisibleRange
import com.oplus.gallery.business_lib.util.TimelineUtils.TIMELINE_TAG
import com.oplus.gallery.business_lib.viewmodel.loader.ThumbTaskMapManager
import com.oplus.gallery.foundation.cvimageprocess.autocrop.CropRatio
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.isChanged
import com.oplus.gallery.foundation.util.math.isInvalid
import com.oplus.gallery.foundation.util.math.length
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import java.io.PrintWriter

open class SlotSlidingWindow(
    swConfig: SWConfig,
    onModelGetter: () -> BaseModel<MediaItem>,
    defaultOrder: Boolean = false
) : BaseSlidingWindow(swConfig, onModelGetter, defaultOrder), FakeBitmapProvider {
    override val tag = "${TIMELINE_TAG}SlotSlidingWindow#${swConfig.type}"

    override val slotBgColor: Int = ContextGetter.context.getColor(com.oplus.gallery.basebiz.R.color.common_transparent)

    override fun onCreateTaskManager(): TaskMapManager<TimeThumbnailTask> =
        ThumbTaskMapManager<TimeThumbnailTask>(swConfig.type).apply { debugLogEnable = true }

    override fun onForeground() {
        super.onForeground()
        // 后台timeNodes发生变化时，回到前台需要更新ViewData
        if (timelineInfo.isTimeNodeChanged) {
            timelineInfo.isTimeNodeChanged = false
            buildViewDataIfNeed(rangeInfo.visibleRange)
            handler.post {
                val buildCount = buildViewDataIfNeed(rangeInfo.activeRange)
                GLog.d(tag, "onForeground. timeNodeChanged. buildCount=$buildCount")
            }
        }
    }

    override fun setVisibleRange(itemRange: IntRange, blockRange: IntRange, nodeRange: IntRange) {
        if (itemRange.isInvalid() || (itemRange.length() > swConfig.activeCacheSize) || (itemRange.last > totalSize - 1)) {
            GLog.w(tag, "setVisibleRange. invalid visible,visible:$itemRange,totalSize:$totalSize")
            return
        }

        val oldVisibleRange = rangeInfo.visibleRange
        val oldActiveRange = rangeInfo.activeRange
        val oldCacheRange = rangeInfo.cacheRange

        rangeInfo.visibleRange = itemRange
        updateActiveRange(itemRange, blockRange, nodeRange)
        thumbTaskManager.updateRange(rangeInfo.visibleRange, rangeInfo.activeRange)

        if (isForeground || (slidingListener?.enlargeContent(this) == true)) {
            runInThreadWhenBackground {
                freeBuildViewData(rangeInfo.activeRange, oldActiveRange)
            }
        } else {
            freeBuildViewData(rangeInfo.visibleRange, oldVisibleRange)
        }

        // refresh view
        if (rangeInfo.visibleRange.isChanged(oldVisibleRange)) {
            requestInvalidateAction?.invoke(this)
        }
        restartErrorThumbTask()
        GLog.d(tag, LogFlag.DL) {
            "setVisibleRange. isForeground:$isForeground, newVisibleRange:${rangeInfo.visibleRange}, " +
                    "newActiveRange:${rangeInfo.activeRange}, newCacheRange:${rangeInfo.cacheRange}, " +
                    "oldVisibleRange:$oldVisibleRange, oldActiveRange:$oldActiveRange, oldCacheRange:$oldCacheRange"
        }
    }

    /**
     * newLayout是从reloadTask直接调到view去布局，跑setVisibleRange算各range的，
     * 此次还不能重建view data，动画的旧holder还需要旧的view data
     */
    override fun setPreVisibleRange(itemRange: IntRange, blockRange: IntRange, nodeRange: IntRange) {
        if (itemRange.isInvalid() || (itemRange.length() > swConfig.activeCacheSize) || (itemRange.last > totalSize - 1)) {
            GLog.w(tag, "setPreVisibleRange. invalid visible,visible:$itemRange,totalSize:$totalSize")
            return
        }

        GLog.d(
            tag, "setPreVisibleRange. newLayout, newVisibleRange:${rangeInfo.visibleRange}, " +
                "newActiveRange:${rangeInfo.activeRange}, newCacheRange:${rangeInfo.cacheRange}, "
        )

        rangeInfo.visibleRange = itemRange
        updateActiveRange(itemRange, blockRange, nodeRange)
        thumbTaskManager.updateRange(rangeInfo.visibleRange, rangeInfo.activeRange)
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    open fun updateActiveRange(itemRange: IntRange, blockRange: IntRange, nodeRange: IntRange) {
        val activeRange = calculateActiveRangeOfVisibleRange(itemRange, swConfig.activeCacheSize, totalSize)
        GLog.d(
            tag, "updateActiveRange. visibleRange:$itemRange, " +
                "oldActiveRange:${rangeInfo.activeRange}, newActiveRange:$activeRange"
        )

        // update active view data range
        val isChanged = rangeInfo.activeRange.isChanged(activeRange)
        if (isChanged) {
            rangeInfo.activeRange = activeRange
            updateCacheRange(activeRange)
        }
    }

    override fun buildViewDataIfNeed(index: Int): Boolean {
        val oldId = getViewData(index)?.id
        val oldCropRatio = getViewData(index)?.timeThumbnail?.cropRatio
        val oldBitmap = oldCropRatio?.let { getBitmapByIndex(index, it) }
        return super.buildViewDataIfNeed(index).apply {
            getViewData(index)?.takeIf {
                it.id == oldId
            }?.timeThumbnail?.apply {
                fakeBitmap = if (cropRatio == oldCropRatio) oldBitmap else null
            }
        }
    }

    override fun updateSpecifiedCount(): Boolean {
        val newSpecifiedCount = model.getSpecifiedCount()
        val changed = (specifiedCount.getInt(KEY_IMAGE_COUNT) != newSpecifiedCount.getInt(KEY_IMAGE_COUNT)) ||
            (specifiedCount.getInt(KEY_VIDEO_COUNT) != newSpecifiedCount.getInt(KEY_VIDEO_COUNT))
        if (changed) {
            specifiedCount = newSpecifiedCount
        }
        return changed
    }

    override fun getBitmapByIndex(index: Int, cropRatio: CropRatio): Bitmap? {
        getViewData(index)?.timeThumbnail?.let {
            if ((it.index == index) && (it.cropRatio == cropRatio)) return it.availableDrawable?.bitmap
        }
        return null
    }

    override fun getBitmapByPath(path: String, cropRatio: CropRatio): Bitmap? {
        getViewData(path)?.timeThumbnail?.let {
            if ((it.item.path.toString() == path) && (it.cropRatio == cropRatio)) return it.bitmap
        }
        return null
    }

    protected fun findFakeBitmap(types: List<String>, byPath: Boolean = false) {
        val bingoList = mutableListOf<Int>()
        rangeInfo.visibleRange.forEach { index ->
            val viewData = getViewData(index) ?: return@forEach
            val task = viewData.timeThumbnail ?: return@forEach
            if ((task.bitmap == null) && (task.fakeBitmap == null)) {
                task.fakeBitmap = if (byPath) {
                    fakeBitmapFinder?.findBitmapByPath(types, viewData.id, task.cropRatio)
                } else {
                    fakeBitmapFinder?.findBitmapByIndex(types, index, task.cropRatio)
                }
                if (task.fakeBitmap != null) {
                    bingoList.add(index)
                }
            }
        }
        GLog.d(tag, "findFakeBitmap. visibleRange=${rangeInfo.visibleRange}, bingoList=${bingoList.toIntArray().contentToString()}")
    }

    override fun dump(writer: PrintWriter) {
        writer.println("-------------- Dump Type.$swConfig start --------------")
    }
}
