/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SuperTextDocConvertor.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/05/10
 ** Author: XIEWUJIE
 ** TAG:SuperTextDocConvertor
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** XIEWUJIE                      2022/05/10		1.0		SuperTextDocConvertor
 *********************************************************************************/
package com.oplus.gallery.business_lib.supertext.convert

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.os.Environment
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.google.gson.reflect.TypeToken
import com.oplus.gallery.basebiz.helper.AiCloudRequester
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.supertext.DocConvertListener
import com.oplus.gallery.business_lib.supertext.DocConvertRequestBean
import com.oplus.gallery.business_lib.supertext.DocConvertResponseBean
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.networkaccess.convert.JsonResponseConvert
import com.oplus.gallery.foundation.security.AppsAESUtil
import com.oplus.gallery.foundation.security.SecurityUtils
import com.oplus.gallery.foundation.security.SecurityUtils.binToHex
import com.oplus.gallery.foundation.security.SecurityUtils.hexToBin
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.graphic.ImageUtils
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusNetServiceManager
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import java.io.BufferedInputStream
import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import java.util.Base64
import javax.crypto.SecretKey

/**
 * 超级文本2.0转文档的转换器
 */
internal class SuperTextDocConvertor(private val context: Context) : IDocConvertor {

    private var convertedFile: File? = null

    /**
     * 将图片转为文档
     * @param bitmap 需要转换的图片
     * @param type 需要转换的文件类型
     * @param imageFilePath 图片的存储路径,需要用来查询图片的标签和图片名
     * @param docConvertListener 转文档过程中的监听器
     */
    override fun convertDocument(
        activity: Activity,
        bitmap: Bitmap?,
        type: DocumentType,
        imageFilePath: String,
        docConvertListener: DocConvertListener?
    ) {
        bitmap ?: let {
            docConvertListener?.onError("convertDocument, bitmap can not be null")
            GLog.w(TAG, "convertDocument, bitmap can not be null")
            return
        }
        val fileSuffix = when (type) {
            DocumentType.WORD -> WORD_FILE_SUFFIX
            DocumentType.PPT -> PPT_FILE_SUFFIX
            DocumentType.EXCEL -> EXCEL_FILE_SUFFIX
        }
        // 缩放压缩
        val scaleBitmap = if (
            (bitmap.width > CONVERT_DOC_UPLOAD_IMAGE_MAX_WIDTH)
            || (bitmap.height > CONVERT_DOC_UPLOAD_IMAGE_MAX_HEIGHT)
        ) {
            BitmapUtils.scaleToSuggestSize(
                bitmap,
                CONVERT_DOC_UPLOAD_IMAGE_MAX_WIDTH,
                CONVERT_DOC_UPLOAD_IMAGE_MAX_HEIGHT,
                ImageUtils.SCALE_MODE_INSIDE
            )
        } else {
            bitmap
        }
        // 质量压缩，阈值==0.5MB
        val bitmapByteArray = BitmapUtils.compressToBytes(
            scaleBitmap,
            Bitmap.CompressFormat.JPEG,
            CONVERT_DOC_UPLOAD_IMAGE_MAX_SIZE,
            COMPRESS_QUALITY_INTERVAL
        )
        runCatching {
            convertDocumentWithByteArray(bitmapByteArray, type, imageFilePath, fileSuffix, docConvertListener)
        }.onFailure {
            GLog.w(TAG, "convertDocument error", it)
            docConvertListener?.onError("convert fail")
        }
    }

    @VisibleForTesting
    fun convertDocumentWithByteArray(
        bitmapByteArray: ByteArray,
        type: DocumentType,
        imageFilePath: String,
        fileSuffix: String,
        docConvertListener: DocConvertListener?
    ) {
        // 1. 删除上一次的转文档的文件
        deleteConvertedFile()
        // 2. 生成aesKey，共构建请求加密和请求结果解密使用
        val aesSecretKey = AppsAESUtil.generateKey(AppsAESUtil.AES_KEY_SIZE_128)
        // 3. 构建请求
        val requester = buildRequest(bitmapByteArray, aesSecretKey, imageFilePath, type)
        requester.execute(JsonResponseConvert(object : TypeToken<DocConvertResponseBean>() {}))?.let { result ->
            result.data?.fileContent?.let {
                convertedFile = saveDocFile(
                    Base64.getDecoder().decode(
                        AppsAESUtil.decrypt(
                            aesSecretKey, hexToBin(it),
                            AppsAESUtil.CIPHER_ALGORITHM_CFB_NO_PADDING
                        )
                    ),
                    FilePathUtils.getTitle(imageFilePath),
                    fileSuffix
                )
            } ?: let {
                GLog.w(TAG, "convertDocumentWithByteArray fail,result: $result")
            }
        } ?: let {
            GLog.w(TAG, "convertDocumentWithByteArray fail,result is null")
        }
        // 4.执行结束，结果回调
        convertedFile?.let {
            docConvertListener?.onSucceed(it)
        } ?: run {
            GLog.w(TAG, "convertDocumentWithByteArray: convertedFile = null")
            docConvertListener?.onError("convert fail")
        }
    }

    @VisibleForTesting
    fun buildRequest(
        bitmapByteArray: ByteArray,
        aesSecretKey: SecretKey,
        imageFilePath: String,
        type: DocumentType
    ): AiCloudRequester {
        // 1.图片转为base64
        val base64Image = JsonUtil.toJson(listOf(Base64.getEncoder().encodeToString(bitmapByteArray)))
        // 2.查询这张图片的标签
        val labelIdList = SearchDBHelper.querySceneIdsByFilePath(imageFilePath)
            ?: LocalMediaDataHelper.preloadMediaItem(imageFilePath)?.let {
                ApiDmManager.getScanDM().scanLabel(it)
            }
            ?: let {
                GLog.w(TAG, "[buildRequest], get path is null")
                emptyList()
            }
        val labelNameList = labelIdList.map { LabelDictionary.getLabelName(it) }
        // 3.加密
        val skAfterRsa = binToHex(
            SecurityUtils.RSA.encryptByPublicKeyInNONE(
                AppsAESUtil.convertKeyToString(aesSecretKey).toByteArray(StandardCharsets.UTF_8),
                OplusNetServiceManager.getInstance().aiCloudPubKey,
                SecurityUtils.RSA.ALGORITHM_RSA_NONE_PKCS1PADDING
            )
        )
        val encryptData = binToHex(
            AppsAESUtil.encrypt(
                aesSecretKey,
                base64Image.toByteArray(),
                AppsAESUtil.CIPHER_ALGORITHM_CFB_NO_PADDING
            )
        )
        // 4.构造body
        val body = JsonUtil.toJson(
            DocConvertRequestBean(
                type = type.name.lowercase(),
                imageBase64 = encryptData,
                options = DocConvertRequestBean.Options(labelNameList)
            )
        )
        // 5.构建请求
        return AiCloudRequester.Builder(context)
            .setBody(body)
            .setCryptoKey(skAfterRsa)
            .setSubUrl(IMAGE_TO_DOC_SUB_URL)
            .setRequestCryptoParam(DOC_CONVERT_REQUEST_CRYPTO_PARAM)
            .setResponseCryptoParam(DOC_CONVERT_RESPONSE_CRYPTO_PARAM)
            .setCryptoAlgorithm(AiCloudRequester.AI_CRYPTO_ALGORITHM_AES_RSA)
            .build()
    }

    /**
     * 将文件存在"/storage/emulated/0/Android/data/com.coloros.gallery3d/files/Documents/"目录下,
     * 清理目录时会清理掉,名字和原图片名字相同
     */
    @WorkerThread
    @VisibleForTesting
    fun saveDocFile(byteArray: ByteArray, fileName: String, fileSuffix: String): File? {
        var baseFileName = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath
        if (baseFileName == null) {
            baseFileName = "${context.filesDir.absolutePath}${File.separator}$DEFAULT_FOLDER_NAME"
        }
        val destPath = "$baseFileName${File.separator}$fileName.$fileSuffix"
        return runCatching {
            FileOperationUtils.saveInputStream(
                BufferedInputStream(ByteArrayInputStream(byteArray)),
                destPath,
                true
            ).takeIf { it }?.let {
                File(destPath)
            }
        }.onFailure {
            GLog.w(TAG, "saveDocFile: $it")
        }.getOrNull()
    }

    /**
     * 删除文件缓存
     */
    override fun close() {
        deleteConvertedFile()
    }

    private fun deleteConvertedFile() {
        convertedFile?.let {
            FileOperationUtils.deleteQuietly(it)
            convertedFile = null
        }
    }

    companion object {
        private const val TAG = "SuperTextDocConvertor"

        /**
         * 上传图片到服务器需要限制大小,阈值为0.5MB
         */
        private const val CONVERT_DOC_UPLOAD_IMAGE_MAX_SIZE = (0.5 * 1024 * 1024).toLong()
        private const val CONVERT_DOC_UPLOAD_IMAGE_MAX_WIDTH = 1080
        private const val CONVERT_DOC_UPLOAD_IMAGE_MAX_HEIGHT = 1920
        private const val COMPRESS_QUALITY_INTERVAL = 2
        private const val DOC_CONVERT_REQUEST_CRYPTO_PARAM = "image_base64"
        private const val DOC_CONVERT_RESPONSE_CRYPTO_PARAM = "file_content"
        private const val IMAGE_TO_DOC_SUB_URL = "/image-process/v2/multi-img2doc"

        private const val WORD_FILE_SUFFIX = "docx"
        private const val EXCEL_FILE_SUFFIX = "xlsx"
        private const val PPT_FILE_SUFFIX = "pptx"

        private const val DEFAULT_FOLDER_NAME = "convertDoc"
    }
}