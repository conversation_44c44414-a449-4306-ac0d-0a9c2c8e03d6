/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : TimelineDecorationDrawer.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2020/9/19 10:15
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2020/9/19      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.timeline.draw

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.Size
import androidx.core.content.ContextCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.Companion.EXTRA_KEY_LOCATION_TITLE
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.Companion.EXTRA_KEY_LOCATION_TITLE_DIRTY
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.Companion.EXTRA_KEY_SELECTION
import com.oplus.gallery.business_lib.model.data.base.timeline.TimeNode.Companion.EXTRA_KEY_TIME_TITLE
import com.oplus.gallery.business_lib.timeline.layout.LayoutConfig
import com.oplus.gallery.business_lib.timeline.layout.TimelineNodeDecoration
import com.oplus.gallery.business_lib.timeline.view.StateChangeAdapter
import com.oplus.gallery.business_lib.util.TimelineUtils
import com.oplus.gallery.business_lib.util.TimelineUtils.ALPHA_OPAQUE
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.typeface.TypefaceUtil
import com.oplus.gallery.standard_lib.baselist.view.canShowSelectionMode
import com.oplus.gallery.standard_lib.baselist.view.isSelectMode
import com.oplus.gallery.basebiz.R as BasebizR

class TimelineDecorationDrawer(
    context: Context,
    layoutConfig: LayoutConfig
) : BaseDecorationDrawer<TimelineNodeDecoration>(context, layoutConfig) {
    private val mapTitleComma by lazy { context.resources.getString(BasebizR.string.base_timeline_map_title_comma) }
    private val mapTitleMore by lazy { context.resources.getString(BasebizR.string.base_timeline_map_title_more) }
    private var locationTitleWidthMax = 0
    private var timeTitleWidthMax = 0
    private val timeTitleTypeface by lazy { TypefaceUtil.SANS_SERIF_MEDIUM }
    private val timeTextPaddingEnd by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_time_text_region_padding_end) }
    private val timeTitleTextSize by lazy { context.resources.getDimension(BasebizR.dimen.base_timeline_time_title_text_size) }
    private val timeTitleTextColor by lazy { COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPrimaryNeutral) }

    private val titleRegionPaddingStart by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_title_region_padding_start) }
    private val titleRegionPaddingEnd by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_title_region_padding_end) }
    private val titleRegionPaddingTop by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_title_region_padding_top) }
    private val titleRegionPaddingBottom by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_title_region_padding_bottom) }
    private val checkBoxWH by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_checkbox_wh) }
    private val checkBoxMarginEnd by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_checkbox_margin_end) }
    private val dotWidth by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_title_region_dot_width) }
    private val dotRadius by lazy { context.resources.getDimensionPixelSize(BasebizR.dimen.base_timeline_title_region_dot_radius) }
    private val dotColor by lazy { context.getColor(BasebizR.color.base_timeline_title_dot_color) }
    private var dotPaint: Paint = Paint().apply {
        color = dotColor
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val checkBoxCache by lazy {
        DrawableCache(CHECKBOX_CACHE_SIZE, CHECKBOX_POOL_SIZE) {
            ContextCompat.getDrawable(context, com.support.appcompat.R.drawable.coui_checkbox_state)
        }
    }

    init {
        registerStateChangeListener(object : StateChangeAdapter() {
            override fun onSelectionModeChanged(selectModeSpec: Int) {
                clearCheckBoxCache()
            }

            override fun onAccentColorChanged() {
                clearCheckBoxCache()
            }
        })
    }

    override fun onDraw(
        canvas: Canvas,
        timeNode: TimeNode,
        nodeDecoration: TimelineNodeDecoration,
        alpha: Float
    ) {
        nodeDecoration.let {
            // 绘制文字外框，用于UX对比，调试用
            if (TimelineUtils.DRAW_DEBUG != TimelineUtils.DRAW_DEBUG_NONE) {
                paintDebug.style = Paint.Style.STROKE
                paintDebug.strokeWidth = 1f
                canvas.drawRect(nodeDecoration.nodeRect, paintDebug)
                canvas.drawRect(nodeDecoration.titleContentRect, paintDebug)
                canvas.drawRect(nodeDecoration.locationTitleRect, paintDebug)
                paintDebug.style = Paint.Style.FILL
                canvas.drawText(
                    nodeDecoration.index.toString(),
                    nodeDecoration.nodeRect.width() / 2f,
                    nodeDecoration.titleContentRect.bottom.toFloat(),
                    paintDebug
                )
            }
            // 绘制时间标题
            drawText(
                canvas = canvas,
                rect = nodeDecoration.titleContentRect,
                textDrawable = findTextDrawable(
                    timeNode.extraInfo.getString(EXTRA_KEY_TIME_TITLE),
                    timeTitleWidthMax,
                    timeTitleTextSize,
                    timeTitleTypeface,
                    timeTitleTextColor
                ),
                alpha = alpha
            )

            // 绘制位置信息标题
            drawLocation(canvas, timeNode, nodeDecoration, alpha)

            // 绘制checkbox
            drawCheckbox(timeNode, nodeDecoration, canvas)
        }
    }

    private fun drawCenterDot(canvas: Canvas, nodeDecoration: TimelineNodeDecoration, alpha: Float) {
        val dotRect = nodeDecoration.dotRect
        val centerX: Float = dotRect.left + (dotRect.width() / 2f)
        val centerY: Float = dotRect.top + (dotRect.height() / 2f)
        dotPaint.alpha = (alpha * ALPHA_OPAQUE).toInt()
        canvas.drawCircle(centerX, centerY, dotRadius.toFloat(), dotPaint)
    }

    fun clearCheckBoxCache() {
        checkBoxCache.clear()
    }

    private fun drawCheckbox(
        timeNode: TimeNode,
        nodeDecoration: TimelineNodeDecoration,
        canvas: Canvas
    ) {
        if (isSelectMode(selectModeSpec) && canShowSelectionMode(selectModeSpec)) {
            val isAllSelected = timeNode.extraInfo.getBoolean(EXTRA_KEY_SELECTION)
            checkBoxCache.get(timeNode.id)?.apply {
                state = if (isAllSelected) {
                    CHECKED_STATE_SET
                } else {
                    UNCHECKED_STATE_SET
                }
                if (!isCheckBoxAnimEnable) jumpToCurrentState()
                bounds = nodeDecoration.checkBoxRect
                draw(canvas)
            }
        }
    }

    @Suppress("LongMethod")
    private fun drawLocation(
        canvas: Canvas,
        timeNode: TimeNode,
        nodeDecoration: TimelineNodeDecoration,
        alpha: Float
    ) {
        val locationTitle = timeNode.extraInfo.getString(EXTRA_KEY_LOCATION_TITLE) ?: return
        val locationTitleRect = nodeDecoration.locationTitleRect
        findTextDrawable(
            locationTitle,
            locationTitleWidthMax,
            timeTitleTextSize,
            timeTitleTypeface,
            timeTitleTextColor
        )?.let { locationDrawable ->
            // 地点显示后需要同时绘制圆点分隔符
            drawCenterDot(canvas, nodeDecoration, alpha)
            tmpRect.set(
                locationTitleRect.left,
                locationTitleRect.top,
                locationTitleRect.right,
                locationTitleRect.bottom
            )
            drawText(
                canvas,
                rect = tmpRect,
                textDrawable = locationDrawable,
                alpha = alpha
            )
        }
    }

    private fun getLocationTitle(locationList: List<String>): String {
        return if (locationList.isEmpty()) {
            TextUtil.EMPTY_STRING
        } else {
            val stringBuilder = StringBuilder(locationList[0])
            val size = locationList.size
            if (size > 1) {
                for (i in 1 until size) {
                    val next = mapTitleComma + locationList[i]
                    if (textPaint.measureText(stringBuilder.toString() + mapTitleMore + next) <= locationTitleWidthMax) {
                        stringBuilder.append(next)
                    } else {
                        stringBuilder.append(mapTitleMore)
                        break
                    }
                }
            }
            stringBuilder.toString()
        }
    }

    /**
     * 测量Title区域内容
     */
    override fun measureIfNeed(timeNode: TimeNode, nodeDecoration: TimelineNodeDecoration) {
        nodeDecoration.apply {
            val needMeasure = isDirty || (isRTL != isRTLMode)
                || (timeNode.extraInfo.getString(EXTRA_KEY_TIME_TITLE) == null)
                || (timeNode.extraInfo.getString(EXTRA_KEY_LOCATION_TITLE) == null) // null在内容失效后的一种特殊状态，需要重新测量，如果没有值是空字符串，也不会触发测量
            if (needMeasure) {
                isDirty = false
                isRTL = isRTLMode
                val nodeWidth = nodeDecoration.nodeRect.width()
                val availableWidth = nodeWidth - titleRegionPaddingStart - titleRegionPaddingEnd -
                    timeTextPaddingEnd - dotWidth
                val (titleCenterY, timeTextWidth) = measureTitleRect(timeNode, this, availableWidth)
                measureDotRect(nodeDecoration, titleCenterY)
                measureLocationRect(timeNode, this, availableWidth, timeTextWidth, titleCenterY)

                checkBoxRect.apply {
                    if (isRTL) {
                        left = checkBoxMarginEnd
                        right = left + checkBoxWH
                    } else {
                        right = nodeWidth - checkBoxMarginEnd
                        left = right - checkBoxWH
                    }
                    top = titleCenterY - checkBoxWH / 2
                    bottom = titleCenterY + checkBoxWH / 2
                }
            }
        }
    }

    private fun measureDotRect(nodeDecoration: TimelineNodeDecoration, titleCenterY: Int) {
        nodeDecoration.apply {
            val halfHeight = titleContentRect.height() / 2
            dotRect.apply {
                if (isRTL) {
                    right = titleContentRect.left
                    left = right - dotWidth
                } else {
                    left = titleContentRect.right
                    right = left + dotWidth
                }
                top = titleCenterY - halfHeight
                bottom = titleCenterY + halfHeight
            }
        }
    }

    private fun measureLocationRect(
        timeNode: TimeNode,
        nodeDecoration: TimelineNodeDecoration,
        availableWidth: Int,
        timeTextWidth: Int,
        titleCenterY: Int
    ) {
        val locationList = timeNode.locationList ?: let {
            // EXTRA_KEY_LOCATION_TITLE_DIRTY为true时。可能是地点解析过程中，locationList可能还会变化，所以先不为空
            if (timeNode.extraInfo.getBoolean(EXTRA_KEY_LOCATION_TITLE_DIRTY).not()) {
                timeNode.extraInfo.putString(EXTRA_KEY_LOCATION_TITLE, TextUtil.EMPTY_STRING)
            }
            return
        }

        nodeDecoration.apply {
            textPaint.typeface = timeTitleTypeface
            textPaint.textSize = timeTitleTextSize
            locationTitleWidthMax = availableWidth - timeTextWidth
            val locationTitle = getLocationTitle(locationList).apply {
                timeNode.extraInfo.putString(EXTRA_KEY_LOCATION_TITLE, this)
            }

            if (locationTitle.isBlank()) {
                locationTitleRect.setEmpty()
            } else {
                val size = getCacheTextSize(locationTitle) ?: measureAndCacheTextSize(locationTitle)
                /*
                 * 取出size之后再判断，保证缓存正常，locationTitleWidthMax再分屏情况数值可能异常为负数，
                 * 如果缓存正常后续等locationTitleWidthMax正常后还可以正常展示
                 */
                val locationTextWidth = size.width.coerceAtMost(locationTitleWidthMax)
                val locationTextHeight = size.height
                val locationTitleHalfHeight = locationTextHeight / 2
                locationTitleRect.apply {
                    if (isRTL) {
                        right = dotRect.left
                        left = right - locationTextWidth
                    } else {
                        left = dotRect.right
                        right = left + locationTextWidth
                    }
                    top = titleCenterY - locationTitleHalfHeight
                    bottom = titleCenterY + locationTitleHalfHeight
                }
            }
        }
    }

    private fun measureAndCacheTextSize(text: String): Size {
        return textPaint.measureText(text).toInt().let { width ->
            val size = Size(width, (textPaint.descent() - textPaint.ascent()).toInt())
            // 缓存size，避免重复计算，需保证此处的值不会异常。
            putCacheTextSize(text, size)
            size
        }
    }

    private fun measureTitleRect(
        timeNode: TimeNode,
        nodeDecoration: TimelineNodeDecoration,
        availableWidth: Int
    ): Pair<Int, Int> {
        val timeTitle = parseTimeTitle(timeNode.timestamp).apply {
            timeNode.extraInfo.putString(EXTRA_KEY_TIME_TITLE, this)
        }
        textPaint.typeface = timeTitleTypeface
        textPaint.textSize = timeTitleTextSize
        /*
         * timeText最大宽度不超过可用宽度的0.7倍，
         * 但没有地点的时候不作这个限制，最大长度不超过可用宽度且不挤占checkBox空间即可
         */
        timeTitleWidthMax = (availableWidth * TIME_TEXT_MAX_RATIO).toInt()
        if (timeNode.locationList.isNullOrEmpty()) {
            timeTitleWidthMax = availableWidth - checkBoxWH - checkBoxMarginEnd - timeTextPaddingEnd
        }
        val titleHeight = nodeDecoration.config.titleRegionHeight
        val titleWidth = nodeDecoration.nodeRect.width()
        val titleCenterY = (titleRegionPaddingTop + titleHeight - titleRegionPaddingBottom) / 2
        val size = getCacheTextSize(timeTitle) ?: measureAndCacheTextSize(timeTitle)
        /*
         * 取出size之后再判断，保证缓存正常，timeTitleWidthMax再分屏情况数值可能异常为负数，
         * 如果缓存正常后续等timeTitleWidthMax正常后还可以正常展示
         */
        val timeTextWidth = size.width.coerceAtMost(timeTitleWidthMax)
        val timeTextHalfHeight = size.height / 2
        nodeDecoration.titleContentRect.apply {
            if (nodeDecoration.isRTL) {
                right = titleWidth - titleRegionPaddingStart
                left = right - timeTextWidth
            } else {
                left = titleRegionPaddingStart
                right = left + timeTextWidth
            }

            top = titleCenterY - timeTextHalfHeight
            bottom = titleCenterY + timeTextHalfHeight
        }
        return Pair(titleCenterY, timeTextWidth)
    }

    companion object {
        private const val TIME_TEXT_MAX_RATIO = 0.7f

        const val CHECKBOX_CACHE_SIZE = 8 // checkbox一级缓存，缓存drawable对象、drawable状态、及position对应信息
        const val CHECKBOX_POOL_SIZE = 2 // checkbox二级缓存，仅缓存drawable对象

        val CHECKED_STATE_SET = intArrayOf(
            android.R.attr.state_enabled,
            com.support.appcompat.R.attr.coui_state_allSelect,
            -com.support.appcompat.R.attr.coui_state_partSelect
        )

        val UNCHECKED_STATE_SET = intArrayOf(
            android.R.attr.state_enabled,
            -com.support.appcompat.R.attr.coui_state_allSelect,
            -com.support.appcompat.R.attr.coui_state_partSelect
        )
    }
}