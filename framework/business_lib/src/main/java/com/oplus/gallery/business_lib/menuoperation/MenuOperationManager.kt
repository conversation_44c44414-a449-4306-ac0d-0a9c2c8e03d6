/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MenuOperationManager.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/09/10
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/09/10	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation

import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.ADD_FAVORITES
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.ALBUM_CREATE_MEMORY
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.ALBUM_HIDE_ON_TIME_LINE_PAGE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.ALBUM_SHOW_ON_TIME_LINE_PAGE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.CLOSE_CARD_CASE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.COLLAGE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.COPY_TO
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.CREATE_ALBUM
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.CREATE_SHARE_ALBUM
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.DELETE_RECYCLED
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.DETAILS
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.EDIT_PHOTO
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.EDIT_VIDEO
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.EXPORT_CSHOT
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.EXPORT_VIDEO
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.FREE_FROM_GROUP
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.GIF_SYNTHESIS
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.GO_TO_SETTING
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.IMPORT
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.LNS_RESULT_SHARE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.LNS_VIEW_SAVE_RESULT
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.MANUAL_CREATE_MEMORY
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.MERGE_GROUP
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.MOVE_ALBUM
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.MOVE_ALBUM_TO_SAFE_BOX
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.MOVE_TO
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.MOVE_TO_CARD_CASE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.MOVE_TO_SAFE_BOX
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.OPEN_IN_SYSTEM_PLAYER
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.PIC_TO_OCR_SCANNER
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.PIC_TO_PDF
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.PROJECTION
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RECYCLE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RELEASE_CSHOT
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.REMOVE_FAVORITES
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.REMOVE_FROM_LABEL
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.REMOVE_FROM_WIDGET_LIST
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RENAME_ALBUM
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RENAME_FILE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.RESTORE_RECYCLED
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.ROTATE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SAVE_TO_STICKER
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SET_AS_CONTACT_PHOTO
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SET_AS_COVER
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SET_OLIVE_ENABLE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SET_WALLPAPER
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SHARE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SHARED_ALBUM_SELECT_FILE
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SHARED_MEDIA_UPLOAD_FAILED_TIP
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.SHOW_GROUP
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.TRANSFORM_HDR_TO_SDR
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.TRANSFORM_HEIF_TO_JPEG
import com.oplus.gallery.business_lib.menuoperation.MenuAction.Companion.VIDEO_EXPORT_OLIVE
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.business_lib.menuoperation.base.MenuResult
import com.oplus.gallery.business_lib.menuoperation.item.AddFavoritesOperation
import com.oplus.gallery.business_lib.menuoperation.item.AlbumCreateMemoryOperation
import com.oplus.gallery.business_lib.menuoperation.item.AlbumHideOnTimeLinePageOperation
import com.oplus.gallery.business_lib.menuoperation.item.AlbumShowOnTimeLinePageOperation
import com.oplus.gallery.business_lib.menuoperation.item.CloseCardCaseOperation
import com.oplus.gallery.business_lib.menuoperation.item.CollageOperation
import com.oplus.gallery.business_lib.menuoperation.item.CopyToOperation
import com.oplus.gallery.business_lib.menuoperation.item.CreateAlbumOperation
import com.oplus.gallery.business_lib.menuoperation.item.CreateShareAlbumOperation
import com.oplus.gallery.business_lib.menuoperation.item.DeleteRecycledOperation
import com.oplus.gallery.business_lib.menuoperation.item.DetailsOperation
import com.oplus.gallery.business_lib.menuoperation.item.EditPhotoOperation
import com.oplus.gallery.business_lib.menuoperation.item.EditVideoOperation
import com.oplus.gallery.business_lib.menuoperation.item.ExportCShotOperation
import com.oplus.gallery.business_lib.menuoperation.item.ExportVideoOperation
import com.oplus.gallery.business_lib.menuoperation.item.FreeFromGroupOperation
import com.oplus.gallery.business_lib.menuoperation.item.GifSynthesisTipOperation
import com.oplus.gallery.business_lib.menuoperation.item.GoToSettingOperation
import com.oplus.gallery.business_lib.menuoperation.item.HDRVideoTransformOperation
import com.oplus.gallery.business_lib.menuoperation.item.HEIFImageTransformOperation
import com.oplus.gallery.business_lib.menuoperation.item.ImportOperation
import com.oplus.gallery.business_lib.menuoperation.item.LnSResultShareOperation
import com.oplus.gallery.business_lib.menuoperation.item.LnSViewSaveResultOperation
import com.oplus.gallery.business_lib.menuoperation.item.ManualCreateMemoryOperation
import com.oplus.gallery.business_lib.menuoperation.item.MergeGroupOperation
import com.oplus.gallery.business_lib.menuoperation.item.MoveAlbumOperation
import com.oplus.gallery.business_lib.menuoperation.item.MoveAlbumToSafeBoxOperation
import com.oplus.gallery.business_lib.menuoperation.item.MoveToCardCaseAlbumOperation
import com.oplus.gallery.business_lib.menuoperation.item.MoveToOperation
import com.oplus.gallery.business_lib.menuoperation.item.MoveToSafeBoxOperation
import com.oplus.gallery.business_lib.menuoperation.item.OpenInSystemPlayerOperation
import com.oplus.gallery.business_lib.menuoperation.item.PhotoToOcrScannerOperation
import com.oplus.gallery.business_lib.menuoperation.item.PhotoToPdfOperation
import com.oplus.gallery.business_lib.menuoperation.item.ProjectionOperation
import com.oplus.gallery.business_lib.menuoperation.item.RecycleOperation
import com.oplus.gallery.business_lib.menuoperation.item.ReleaseCShotOperation
import com.oplus.gallery.business_lib.menuoperation.item.RemoveFavoritesOperation
import com.oplus.gallery.business_lib.menuoperation.item.RemoveFromLabelOperation
import com.oplus.gallery.business_lib.menuoperation.item.RemoveFromWidgetListOperation
import com.oplus.gallery.business_lib.menuoperation.item.RenameAlbumOperation
import com.oplus.gallery.business_lib.menuoperation.item.RenameFileOperation
import com.oplus.gallery.business_lib.menuoperation.item.RestoreRecycledOperation
import com.oplus.gallery.business_lib.menuoperation.item.RotateOperation
import com.oplus.gallery.business_lib.menuoperation.item.SaveAsStickerOperation
import com.oplus.gallery.business_lib.menuoperation.item.SetAlbumCoverOperation
import com.oplus.gallery.business_lib.menuoperation.item.SetAsContactPhotoOperation
import com.oplus.gallery.business_lib.menuoperation.item.SetOliveStatusOperation
import com.oplus.gallery.business_lib.menuoperation.item.SetWallpaperOperation
import com.oplus.gallery.business_lib.menuoperation.item.ShareOperation
import com.oplus.gallery.business_lib.menuoperation.item.SharedAlbumSelectFileOperation
import com.oplus.gallery.business_lib.menuoperation.item.SharedMediaUploadFailedTipOperation
import com.oplus.gallery.business_lib.menuoperation.item.ShowGroupOperation
import com.oplus.gallery.standard_lib.app.AppScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

object MenuOperationManager {

    private val menuDispatcher: MenuDispatcher = MenuDispatcher()

    private const val MAIN_THREAD = true
    private const val WORKER_THREAD = false

    /**
     * 通过入参的菜单类型 key 生成对应的菜单操作类并放到调度器中，类型定义在 MenuOperationType 中
     * paramMap 存放入参中的数据
     * onCompleted 菜单操作完成后的回调，包括异常情况
     * onProgressChanged 菜单操作进度的回调
     */
    @JvmStatic
    fun doAction(
        action: Int,
        paramMap: Map<String, Any?>? = null,
        onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)? = null,
        onProgressChanged: ((progress: Int) -> Unit)? = null
    ): MenuResult {
        val newParamMap = paramMap?.let { HashMap(it) } ?: HashMap()
        newParamMap.apply {
            put(MenuAction.KEY_TRACK_CALL_TIME, System.currentTimeMillis().toString())
        }

        val operation = createOperation(action, newParamMap, onCompleted, onProgressChanged)
        return menuDispatcher.dispatcher(operation)
    }

    private fun createOperation(
        action: Int,
        paramMap: Map<String, Any?>,
        onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)? = null,
        onProgressChanged: ((progress: Int) -> Unit)? = null
    ) = when (action) {
        MOVE_TO_SAFE_BOX -> MoveToSafeBoxOperation(WORKER_THREAD, paramMap, onCompleted, onProgressChanged)
        MOVE_ALBUM_TO_SAFE_BOX -> MoveAlbumToSafeBoxOperation(WORKER_THREAD, paramMap, onCompleted, onProgressChanged)
        COLLAGE -> CollageOperation(WORKER_THREAD, paramMap, onCompleted)
        ADD_FAVORITES -> AddFavoritesOperation(WORKER_THREAD, paramMap, onCompleted)
        REMOVE_FAVORITES -> RemoveFavoritesOperation(WORKER_THREAD, paramMap, onCompleted)
        FREE_FROM_GROUP -> FreeFromGroupOperation(WORKER_THREAD, paramMap, onCompleted)
        MERGE_GROUP -> MergeGroupOperation(WORKER_THREAD, paramMap, onCompleted)
        SHOW_GROUP -> ShowGroupOperation(WORKER_THREAD, paramMap, onCompleted)
        ROTATE -> RotateOperation(WORKER_THREAD, paramMap, onCompleted)
        MANUAL_CREATE_MEMORY -> ManualCreateMemoryOperation(MAIN_THREAD, paramMap, onCompleted)
        RECYCLE -> RecycleOperation(WORKER_THREAD, paramMap, onCompleted)
        RESTORE_RECYCLED -> RestoreRecycledOperation(WORKER_THREAD, paramMap, onCompleted)
        DELETE_RECYCLED -> DeleteRecycledOperation(WORKER_THREAD, paramMap, onCompleted)
        GO_TO_SETTING -> GoToSettingOperation(MAIN_THREAD, paramMap, onCompleted)
        PROJECTION -> ProjectionOperation(MAIN_THREAD, paramMap, onCompleted)
        SET_AS_CONTACT_PHOTO -> SetAsContactPhotoOperation(WORKER_THREAD, paramMap, onCompleted)
        SET_WALLPAPER -> SetWallpaperOperation(MAIN_THREAD, paramMap, onCompleted)
        ALBUM_CREATE_MEMORY -> AlbumCreateMemoryOperation(WORKER_THREAD, paramMap, onCompleted)
        SHARE -> ShareOperation(MAIN_THREAD, paramMap, onCompleted)
        RELEASE_CSHOT -> ReleaseCShotOperation(WORKER_THREAD, paramMap, onCompleted)
        EXPORT_CSHOT -> ExportCShotOperation(WORKER_THREAD, paramMap, onCompleted)
        MOVE_ALBUM -> MoveAlbumOperation(WORKER_THREAD, paramMap, onCompleted)
        DETAILS -> DetailsOperation(WORKER_THREAD, paramMap, onCompleted)
        EDIT_PHOTO -> EditPhotoOperation(MAIN_THREAD, paramMap, onCompleted)
        EDIT_VIDEO -> EditVideoOperation(WORKER_THREAD, paramMap, onCompleted)
        CREATE_ALBUM -> CreateAlbumOperation(MAIN_THREAD, paramMap, onCompleted)
        MOVE_TO -> MoveToOperation(MAIN_THREAD, paramMap, onCompleted)
        MOVE_TO_CARD_CASE -> MoveToCardCaseAlbumOperation(WORKER_THREAD, paramMap, onCompleted)
        CREATE_SHARE_ALBUM -> CreateShareAlbumOperation(MAIN_THREAD, paramMap, onCompleted)
        IMPORT -> ImportOperation(WORKER_THREAD, paramMap, onCompleted)
        TRANSFORM_HEIF_TO_JPEG -> HEIFImageTransformOperation(MAIN_THREAD, paramMap, onCompleted)
        TRANSFORM_HDR_TO_SDR -> HDRVideoTransformOperation(MAIN_THREAD, paramMap, onCompleted)
        REMOVE_FROM_LABEL -> RemoveFromLabelOperation(WORKER_THREAD, paramMap, onCompleted)
        CLOSE_CARD_CASE -> CloseCardCaseOperation(WORKER_THREAD, paramMap, onCompleted)
        RENAME_ALBUM -> RenameAlbumOperation(MAIN_THREAD, paramMap, onCompleted)
        RENAME_FILE -> RenameFileOperation(WORKER_THREAD, paramMap, onCompleted)
        REMOVE_FROM_WIDGET_LIST -> RemoveFromWidgetListOperation(WORKER_THREAD, paramMap, onCompleted)
        SHARED_ALBUM_SELECT_FILE -> SharedAlbumSelectFileOperation(MAIN_THREAD, paramMap, onCompleted)
        SHARED_MEDIA_UPLOAD_FAILED_TIP -> SharedMediaUploadFailedTipOperation(MAIN_THREAD, paramMap, onCompleted)
        GIF_SYNTHESIS -> GifSynthesisTipOperation(MAIN_THREAD, paramMap, onCompleted)
        LNS_VIEW_SAVE_RESULT -> LnSViewSaveResultOperation(MAIN_THREAD, paramMap, onCompleted)
        LNS_RESULT_SHARE -> LnSResultShareOperation(MAIN_THREAD, paramMap, onCompleted)
        COPY_TO -> CopyToOperation(MAIN_THREAD, paramMap, onCompleted)
        EXPORT_VIDEO -> ExportVideoOperation(MAIN_THREAD, paramMap, onCompleted)
        SAVE_TO_STICKER -> SaveAsStickerOperation(WORKER_THREAD, paramMap, onCompleted)
        SET_AS_COVER -> SetAlbumCoverOperation(MAIN_THREAD, paramMap, onCompleted)
        PIC_TO_PDF -> PhotoToPdfOperation(WORKER_THREAD, paramMap, onCompleted)
        SET_OLIVE_ENABLE -> SetOliveStatusOperation(WORKER_THREAD, paramMap, onCompleted)
        OPEN_IN_SYSTEM_PLAYER -> OpenInSystemPlayerOperation(MAIN_THREAD, paramMap, onCompleted)
        ALBUM_HIDE_ON_TIME_LINE_PAGE -> AlbumHideOnTimeLinePageOperation(WORKER_THREAD, paramMap, onCompleted)
        ALBUM_SHOW_ON_TIME_LINE_PAGE -> AlbumShowOnTimeLinePageOperation(WORKER_THREAD, paramMap, onCompleted)
        PIC_TO_OCR_SCANNER -> PhotoToOcrScannerOperation(WORKER_THREAD, paramMap, onCompleted)
        VIDEO_EXPORT_OLIVE -> EditVideoOperation(WORKER_THREAD, paramMap, onCompleted)
        else -> throw UnsupportedOperationException()
    }
}

private class MenuDispatcher {

    /**
     * 在 MenuOperation 指定的线程里执行它，并返回一个可以取消该 MenuOperation 的对象。
     */
    fun dispatcher(menuOperation: MenuOperation<out Any>): MenuResult {
        var job: Job? = null
        if (menuOperation.isRunOnMain) {
            menuOperation.run()
        } else {
            job = AppScope.launch { menuOperation.run() }
        }
        return MenuResult(menuOperation, job)
    }
}