/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SlidingDataListener.kt
 * Description:
 * Version: 1.0
 * Date: 2020/09/09
 ** Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * biao.chen@Apps.Gallery3D      2020/09/09      1.0          build this module
 *************************************************************************************************/
package com.oplus.gallery.business_lib.timeline.viewmodel

interface SlidingDataListener {

    /**
     * is enlarge content
     *
     * @return true: need enlarge content
     */
    fun enlargeContent(sWindow: BaseSlidingWindow): Boolean

    /**
     * notify to load data
     */
    fun onCacheRangeChanged(sWindow: BaseSlidingWindow)
}