/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoSpecificationStrategy.kt
 * Description:
 * Version:
 * Date: 2022/5/19
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/19     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.videoedit

import android.util.Size
import com.oplus.gallery.business_lib.model.data.base.utils.VideoResolutionType
import com.oplus.gallery.business_lib.videoedit.VideoSpecStrategy.processSpec
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtil.alignment2
import com.oplus.gallery.videoeditor.data.VideoOptions
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupport
import kotlin.math.max
import kotlin.math.min

/**
 * 视频编辑业务通用的策略，最大支持到4k120fps编辑，根据视频格式和机器性能会调整到最低1080p30fps
 * 具体详见策略实现[processSpec]
 */
object VideoSpecStrategy : ISpecStrategy {
    private const val TAG = "VideoSpecificationStrategy"

    override fun processSpec(mimeType: String, spec: VideoSpec, options: SpecOptions): VideoSpec? {
        var localSpec = spec
        // 1 异常尺寸返回不支持编辑
        if ((localSpec.height <= 0) || (localSpec.width <= 0) || (localSpec.fps <= 0f)) {
            GLog.e(TAG, LogFlag.DL, "[processSpecification] error: video specification is invalid. specification = $localSpec")
            return null
        }

        // 2 解析硬件解码器支持的最大分辨率
        val dumpedCapabilities = CodecSupport.dumpVideoCapabilities(VideoOptions(mimeType, false, false))
        GLog.d(TAG, LogFlag.DL) { "[processSpecification] capability: {$dumpedCapabilities}" }

        // 先判断是否支持
        if (CodecSupport.isSupportCodec(mimeType, localSpec)) {
            return localSpec
        }
        // 降分辨率
        if (options.reduceResolution) {
            val resolutionType = VideoResolutionType.getType(Size(localSpec.width, localSpec.height))
            var tmpSpec = VideoSpec(localSpec.width, localSpec.height, localSpec.fps)
            val resultType = VideoResolutionType.entries.subList(0, resolutionType.ordinal).asReversed().find {
                tmpSpec = tmpSpec.copy(fps = localSpec.fps).resolutionLimit(it)

                var isSupport = CodecSupport.isSupportCodec(mimeType, tmpSpec)
                // 降帧率
                if (!isSupport && options.reduceFps) {
                    while (tmpSpec.fps / 2 > 28.0) {
                        tmpSpec = tmpSpec.copy(fps = tmpSpec.fps / 2)
                        isSupport = CodecSupport.isSupportCodec(mimeType, tmpSpec)
                        if (isSupport) break
                    }
                }
                isSupport
            } ?: VideoResolutionType.R_1080P

            // 如果上面执行了[R_1080P]，则可能fps是30fps的，这里暂时不修改了，理解这种情况降规格到1080p30fps即可
            localSpec = localSpec.copy(fps = tmpSpec.fps)
            localSpec = localSpec.resolutionLimit(resultType)
        }

        GLog.d(TAG, LogFlag.DL, "[processSpecification] the final edit specification is $localSpec")
        return if (CodecSupport.isSupportCodec(mimeType, localSpec)) localSpec else null
    }

    /**
     * 限制视频规格到指定分辨率
     * @param spec
     * @param type
     * @return 返回降规格后的视频规格，如果规格未超过限制分辨率则返回原规格
     */
    private fun VideoSpec.resolutionLimit(type: VideoResolutionType): VideoSpec {
        val ratio = min(
            min(type.width, type.height).toDouble() / min(width, height),
            max(type.width, type.height).toDouble() / max(width, height)
        )
        return if (ratio < 1.0) {
            GLog.d(TAG, LogFlag.DL, "[resolutionLimit] too large resolution, shrink percent:$ratio")
            copy((width * ratio).alignment2(), (height * ratio).alignment2())
        } else {
            this
        }
    }
}
