/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp, Ltd.
 ** VENDOR_EDIT
 ** File        : EmptySlidingWindow.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/4/22 9:31
 ** Author      : wanglongping
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  wanglongping     2021/4/22  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.business_lib.timeline.viewmodel

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import java.io.PrintWriter

class EmptySlidingWindow : BaseSlidingWindow(SWConfig(), { object : BaseModel<MediaItem>() {} }, false) {
    override fun setVisibleRange(itemRange: IntRange, blockRange: IntRange, nodeRange: IntRange) {}
    override fun dump(writer: PrintWriter) {}
}