/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IExportVideoEngine
 ** Description: 视频裁剪导出引擎
 ** Version: 1.0
 ** Date : 2024/7/4
 ** Author: xiangwei
 ** email:<EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** xiangwei                           2024/7/4  1.0          first created
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item.exportvideo

import android.content.Context
import android.graphics.Rect
import android.graphics.RectF
import android.net.Uri
import com.oplus.gallery.videoeditor.data.VideoSpec

/**
 * 视频导出引擎接口
 * Marked by zhangwenming，未来视频编辑重构后，此处需要接入视频编辑的能力。
 */
interface IExportVideoEngine : AutoCloseable {
    /**
     * 使用[VideoSpec]初始化引擎，以确定当前视频编辑器目标的参数
     *
     * @param specification，指定视频编辑器的视频参数
     * @return
     */
    fun initEngine(context: Context, uri: Uri, offset: Long, length: Long, specification: VideoSpec): Boolean

    /**
     * 向视频编辑器中添加一个视频片段，适用于视频位于 uri 资源中的某位置。
     *
     * @param uri 待添加视频的资源信息
     * @param offset 视频在给定[uri]资源的位置
     * @param trimIn 该视频片段的裁剪入点(毫秒)
     * @param trimOut 该视频片段的裁剪出点(毫秒)
     */
    fun appendVideoClip(uri: Uri, offset: Long, length: Long, trimIn: Long = 0L, trimOut: Long = 0L)

    /**
     * 向视频编辑器中添加一个视频片段
     *
     * @param path 视频文件路径
     * @param trimIn 该视频片段的裁剪入点(毫秒)，trimIn == trimOut 则无裁剪
     * @param trimOut 该视频片段的裁剪出点(毫秒)，trimIn == trimOut 则无裁剪
     */
    fun appendVideoClip(path: String, trimIn: Long = 0L, trimOut: Long = 0L)

    /**
     * 将最终的Video保存到指定的[filePath]
     *
     * @param filePath
     * @param options
     * @return
     */
    fun saveVideo(filePath: String, options: ExportVideoOptions): Boolean

    /**
     * 视频保存参数回调，获取progress和状态
     *
     * @param exportVideoCallback
     */
    fun registerExportVideoCallback(exportVideoCallback: IExportVideoCallback)

    /**
     * 释放视频保存接口
     */
    fun unRegisterExportVideoCallback()

    /**
     * 裁切视频到指定 rect 范围
     *
     * @param rect 以左上角为原点的范围
     */
    fun cropVideo(rect: Rect)

    /**
     * 裁切视频内容的模糊区域, 通过对视频局部进行放大scale实现去掉模糊边缘
     *
     * @param contentRect 视频内容范围
     * @param scale 局部放大系数
     */
    fun cropVideoContent(contentRect: RectF, scale: Float)
}

/**
 * 视频文件的导出进度和状态
 */
interface IExportVideoCallback {
    /**
     * 保存进度
     */
    fun onProgress(position: Int)

    /**
     * 保存完成状态
     *
     * @param isSuccess
     */
    fun onComplete(isSuccess: Boolean)

    /**
     * 导出失败
     *
     * @param isSuccess
     */
    fun onFailed(isSuccess: Boolean)
}

/**
 * 导出视频时的参数。
 *
 * @property dateTaken
 */
data class ExportVideoOptions(
    /**
     * 导出的视频，创建时间
     */
    val dateTaken: Long,

    /**
     * 视频的高度
     */
    val videoHeight: Int,

    /**
     * 视频eis数据
     */
    val eisData: String? = null
)
