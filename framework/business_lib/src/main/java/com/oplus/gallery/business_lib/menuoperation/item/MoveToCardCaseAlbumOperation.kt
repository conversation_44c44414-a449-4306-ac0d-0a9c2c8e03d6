/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MoveToCardCaseAlbumOperation.kt
 ** Description:MoveToCardCaseAlbumOperation
 **
 ** Version: 1.0
 ** Date: 2021/09/08
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		 2021/09/08		1.0		MoveToCardCaseAlbumOperation
 *********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import androidx.fragment.app.Fragment
import com.oplus.gallery.business_lib.cardcase.api.CardCaseDataOperationApi
import com.oplus.gallery.business_lib.cardcase.data.CardCaseCountSet
import com.oplus.gallery.business_lib.cardcase.utils.CardCaseUtils
import com.oplus.gallery.business_lib.menuoperation.base.MenuOperation
import com.oplus.gallery.business_lib.menuoperation.MoveToCardCaseAction
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

class MoveToCardCaseAlbumOperation(
    isRunOnMain: Boolean,
    paramMap: Map<String, Any?>,
    onCompleted: ((resultCode: String, resultMap: Map<String, Any>?) -> Unit)?
) : MenuOperation<Unit>(isRunOnMain, paramMap, onCompleted) {
    private var currentFragmentRef: WeakReference<Fragment>? = null
    private var movableCountSet: CardCaseCountSet? = null
    private var movablePaths: List<Path> = emptyList()

    override fun onCheckAndPrepare(): Boolean {
        (paramMap[MoveToCardCaseAction.KEY_FRAGMENT_REF] as? WeakReference<Fragment>)?.let {
            this.currentFragmentRef = it
        } ?: let {
            GLog.e(TAG, "onCheckAndPrepare, currentFragmentRef is null")
            return false
        }
        movableCountSet = paramMap[MoveToCardCaseAction.KEY_MOVABLE_COUNT_SET] as? CardCaseCountSet
        movableCountSet ?: let {
            GLog.e(TAG, "onCheckAndPrepare, movableCountSet is null")
            return false
        }
        (paramMap[MoveToCardCaseAction.KEY_MOVABLE_PATHS] as? List<Path>)?.let {
            this.movablePaths = it
        }
        return true
    }

    override fun onRun() {
        movableCountSet?.let {
            handleResult(CardCaseDataOperationApi.moveToCardCase(it, movablePaths))
        }
    }

    private fun handleResult(countSet: CardCaseCountSet) {
        AppScope.launch(Dispatchers.UI) {
            val activity = currentFragmentRef?.get()?.activity
            activity?.let {
                val tips = CardCaseUtils.generateMoveSuccessToastStr(
                    activity,
                    countSet.certificatesCount,
                    countSet.bankCardCount,
                    countSet.billCount
                )
                if (tips.isNotEmpty()) {
                    ToastUtil.showShortToast(tips)
                }
            }
        }
    }

    override fun getMenuTypeForTrack(): String = TextUtil.EMPTY_STRING

    override fun getCustomParamForTrack(): Map<String, String?>? = null

    override fun getImageAndVideoCountForTrack(): Pair<String, String> = defaultTrackCountPair

    override fun cancel() {
    }

    companion object {
        const val TAG = "MoveToCardCaseAlbumOperation"
    }
}