/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IEditorCustomUI
 ** Description: interface for editor ui scheme
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2022/07/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.business_lib.template.editor.ui

interface IEditorCustomUI : IEditorUIScheme {
    /**
     * 有的页面需要做一些定制化的UI变化，需要在统一的UI框架执行之后再进行触发
     */
    fun onCallAfterEditorScheme()

    /**
     * 有的页面需要做一些定制化的UI变化，需要是在统一的UI框架执行之前触发
     */
    fun onCallBeforeEditorScheme()
}