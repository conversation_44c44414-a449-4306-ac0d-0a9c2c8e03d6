/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoSpecStrategyIntegrationTest.kt
 * Description: VideoSpecStrategy 集成测试，测试分辨率限制等复杂场景
 * Version: 1.0
 * Date: 2025/08/05
 * Author: AI Assistant
 * TAG: INTEGRATION_TEST
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * AI Assistant                   2025/08/05     1.0        创建 VideoSpecStrategy 集成测试
 *************************************************************************************************/
package com.oplus.gallery.business_lib.videoedit

import android.util.Size
import com.oplus.gallery.business_lib.model.data.base.utils.VideoResolutionType
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtil
import com.oplus.gallery.videoeditor.data.VideoOptions
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupport
import io.mockk.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * VideoSpecStrategy 的集成测试类
 * 主要测试复杂的分辨率降级和帧率降级场景
 */
class VideoSpecStrategyIntegrationTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        
        // Mock 静态方法
        mockkStatic(GLog::class)
        mockkStatic(CodecSupport::class)
        mockkStatic(VideoResolutionType::class)
        mockkStatic(MathUtil::class)
        
        // 设置默认的 Mock 行为
        every { GLog.e(any(), any(), any<String>()) } just Runs
        every { GLog.d(any(), any(), any<() -> String>()) } just Runs
        every { GLog.d(any(), any(), any<String>()) } just Runs
        every { MathUtil.alignment2(any()) } answers { firstArg<Double>().toInt() }
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `processSpec should handle 4K to 1080P resolution reduction correctly`() {
        // Given - 4K视频需要降到1080P
        val originalSpec = VideoSpec(3840, 2160, 30f)
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = true, reduceFps = false)

        // Mock CodecSupport - 4K不支持，1080P支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, originalSpec) } returns false
        
        // Mock VideoResolutionType
        every { VideoResolutionType.getType(Size(3840, 2160)) } returns VideoResolutionType.R_4K
        every { VideoResolutionType.entries } returns listOf(
            VideoResolutionType.R_720P,
            VideoResolutionType.R_1080P,
            VideoResolutionType.R_2K,
            VideoResolutionType.R_4K
        )
        
        // Mock 1080P规格支持
        val expectedSpec = VideoSpec(1920, 1080, 30f)
        every { CodecSupport.isSupportCodec(mimeType, expectedSpec) } returns true
        every { CodecSupport.isSupportCodec(mimeType, any()) } returns false andThen true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, originalSpec, options)

        // Then
        assertNotNull("应该返回降规格后的结果", result)
        result?.let {
            assertTrue("宽度应该被降低", it.width < originalSpec.width)
            assertTrue("高度应该被降低", it.height < originalSpec.height)
            assertEquals("帧率应该保持不变", originalSpec.fps, it.fps, 0.1f)
        }
    }

    @Test
    fun `processSpec should handle 8K to multiple resolution reduction steps`() {
        // Given - 8K视频需要多步降级
        val originalSpec = VideoSpec(7680, 4320, 60f)
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = true, reduceFps = false)

        // Mock CodecSupport - 只有720P支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, originalSpec) } returns false
        
        // Mock VideoResolutionType
        every { VideoResolutionType.getType(Size(7680, 4320)) } returns VideoResolutionType.R_8K
        every { VideoResolutionType.entries } returns listOf(
            VideoResolutionType.R_720P,
            VideoResolutionType.R_1080P,
            VideoResolutionType.R_2K,
            VideoResolutionType.R_4K,
            VideoResolutionType.R_8K
        )
        
        // Mock 只有720P支持
        every { CodecSupport.isSupportCodec(mimeType, any()) } returns false andThen false andThen false andThen true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, originalSpec, options)

        // Then
        assertNotNull("应该返回降规格后的结果", result)
        result?.let {
            assertTrue("应该大幅降低分辨率", it.width <= 1280 && it.height <= 720)
            assertEquals("帧率应该保持不变", originalSpec.fps, it.fps, 0.1f)
        }
    }

    @Test
    fun `processSpec should handle fps reduction from 120fps to 30fps`() {
        // Given - 高帧率视频需要降帧率
        val originalSpec = VideoSpec(1920, 1080, 120f)
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = true, reduceFps = true)

        // Mock CodecSupport - 120fps不支持，30fps支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, originalSpec) } returns false
        
        // Mock VideoResolutionType
        every { VideoResolutionType.getType(Size(1920, 1080)) } returns VideoResolutionType.R_1080P
        every { VideoResolutionType.entries } returns listOf(VideoResolutionType.R_1080P)
        
        // Mock 降帧率过程：120fps -> 60fps -> 30fps
        every { CodecSupport.isSupportCodec(mimeType, any()) } returns false andThen false andThen true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, originalSpec, options)

        // Then
        assertNotNull("应该返回降帧率后的结果", result)
        result?.let {
            assertEquals("宽度应该保持不变", originalSpec.width, it.width)
            assertEquals("高度应该保持不变", originalSpec.height, it.height)
            assertTrue("帧率应该被降低", it.fps < originalSpec.fps)
            assertTrue("帧率应该在合理范围内", it.fps >= 30f)
        }
    }

    @Test
    fun `processSpec should handle combined resolution and fps reduction`() {
        // Given - 需要同时降分辨率和帧率
        val originalSpec = VideoSpec(3840, 2160, 120f)
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = true, reduceFps = true)

        // Mock CodecSupport
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, originalSpec) } returns false
        
        // Mock VideoResolutionType
        every { VideoResolutionType.getType(Size(3840, 2160)) } returns VideoResolutionType.R_4K
        every { VideoResolutionType.entries } returns listOf(
            VideoResolutionType.R_720P,
            VideoResolutionType.R_1080P,
            VideoResolutionType.R_2K,
            VideoResolutionType.R_4K
        )
        
        // Mock 复杂的降级过程：先降分辨率失败，再降帧率成功
        every { CodecSupport.isSupportCodec(mimeType, any()) } returns false andThen false andThen false andThen false andThen true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, originalSpec, options)

        // Then
        assertNotNull("应该返回降规格后的结果", result)
        result?.let {
            assertTrue("分辨率应该被降低", it.width < originalSpec.width || it.height < originalSpec.height)
            assertTrue("帧率应该被降低", it.fps < originalSpec.fps)
        }
    }

    @Test
    fun `processSpec should respect fps reduction boundary at 28fps`() {
        // Given - 测试帧率降级边界
        val originalSpec = VideoSpec(1920, 1080, 60f)
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = true, reduceFps = true)

        // Mock CodecSupport
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, originalSpec) } returns false
        
        // Mock VideoResolutionType
        every { VideoResolutionType.getType(Size(1920, 1080)) } returns VideoResolutionType.R_1080P
        every { VideoResolutionType.entries } returns listOf(VideoResolutionType.R_1080P)
        
        // Mock 降帧率过程，在30fps时支持（30fps > 28fps，所以会继续降到15fps，但15fps < 28fps所以停止）
        every { CodecSupport.isSupportCodec(mimeType, any()) } returns false andThen true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, originalSpec, options)

        // Then
        assertNotNull("应该返回降帧率后的结果", result)
        result?.let {
            assertTrue("帧率应该大于28fps", it.fps > 28f)
        }
    }

    @Test
    fun `processSpec should handle portrait 4K video correctly`() {
        // Given - 竖屏4K视频
        val originalSpec = VideoSpec(2160, 3840, 30f) // 竖屏4K
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = true, reduceFps = false)

        // Mock CodecSupport
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, originalSpec) } returns false
        
        // Mock VideoResolutionType - 注意竖屏的Size
        every { VideoResolutionType.getType(Size(2160, 3840)) } returns VideoResolutionType.R_4K
        every { VideoResolutionType.entries } returns listOf(
            VideoResolutionType.R_720P,
            VideoResolutionType.R_1080P,
            VideoResolutionType.R_2K,
            VideoResolutionType.R_4K
        )
        
        // Mock 1080P竖屏支持
        every { CodecSupport.isSupportCodec(mimeType, any()) } returns false andThen true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, originalSpec, options)

        // Then
        assertNotNull("应该返回降规格后的结果", result)
        result?.let {
            assertTrue("宽度应该被降低", it.width < originalSpec.width)
            assertTrue("高度应该被降低", it.height < originalSpec.height)
            assertTrue("应该保持竖屏比例", it.height > it.width)
        }
    }

    @Test
    fun `processSpec should handle square video correctly`() {
        // Given - 正方形视频
        val originalSpec = VideoSpec(2160, 2160, 30f) // 正方形4K
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = true, reduceFps = false)

        // Mock CodecSupport
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, originalSpec) } returns false
        
        // Mock VideoResolutionType
        every { VideoResolutionType.getType(Size(2160, 2160)) } returns VideoResolutionType.R_4K
        every { VideoResolutionType.entries } returns listOf(
            VideoResolutionType.R_720P,
            VideoResolutionType.R_1080P,
            VideoResolutionType.R_2K,
            VideoResolutionType.R_4K
        )
        
        // Mock 1080P正方形支持
        every { CodecSupport.isSupportCodec(mimeType, any()) } returns false andThen true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, originalSpec, options)

        // Then
        assertNotNull("应该返回降规格后的结果", result)
        result?.let {
            assertTrue("宽度应该被降低", it.width < originalSpec.width)
            assertTrue("高度应该被降低", it.height < originalSpec.height)
            assertEquals("应该保持正方形比例", it.width, it.height)
        }
    }
}
