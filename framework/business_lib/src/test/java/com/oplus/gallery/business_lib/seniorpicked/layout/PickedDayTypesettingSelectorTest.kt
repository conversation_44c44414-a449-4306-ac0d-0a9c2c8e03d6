/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PickedDayTypesettingSelectorTest
 ** Description: 排版器选择类的测试类
 **
 ** Version: 1.0
 ** Date: 2022/05/30
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/05/30  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.business_lib.seniorpicked.layout

import android.app.Application
import com.google.gson.reflect.TypeToken
import com.oplus.gallery.business_lib.seniorpicked.PickedDayTestHelper
import com.oplus.gallery.business_lib.seniorpicked.layout.data.GridConfigs
import com.oplus.gallery.business_lib.util.AssetHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class PickedDayTypesettingSelectorTest {

    @MockK
    private lateinit var app: Application

    @MockK
    private lateinit var configs: GridConfigs


    private lateinit var selector: PickedDayTypesettingSelector

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        mockkStatic(AssetHelper::class)
        ContextGetter.context = app
        every { configs.headTemplate } returns PickedDayTestHelper.headTemplate
        every { configs.bodyTemplates } returns PickedDayTestHelper.bodyTemplates
        every { configs.extraTemplates } returns PickedDayTestHelper.extraTemplates
        every {
            AssetHelper.parseJsonConfig<GridConfigs>(
                any(),
                any(),
                object : TypeToken<GridConfigs>() {}.type
            )
        } returns configs
        selector = spyk(PickedDayTypesettingSelector.instance)
    }

    @Test
    fun `should return null when select with aspectRatio is 0`() {
        selector.load()
        val config = selector.select(3, 0.0f)
        Assert.assertTrue(config == null)
        selector.clear()
    }

    @Test
    fun `should return config when select with aspectRatio is 1f`() {
        selector.load()
        val config = selector.select(3, 1.0f)
        Assert.assertTrue(config?.getGridCountOfLoopTemplate() == PickedDayTestHelper.bodyTemplates[0].size)
        // count为3 是扩展模板第二个
        Assert.assertTrue(config?.getGrid(0, 3)?.type == PickedDayTestHelper.extraTemplates[1][0].type)
        // count为6 是普通的头部模板 + 循环模板 + 尾部模板的定式，index为0，取的是头部模板
        Assert.assertTrue(config?.getGrid(0, 6)?.type == PickedDayTestHelper.headTemplate[0].type)
        selector.clear()
    }
}