/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - VideoSpecificationStrategyTest.kt
 * Description:
 * Version:
 * Date: 2022/5/20
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/20     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.business_lib.videoedit

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.utils.VideoResolutionType
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupportParser
import io.mockk.InternalPlatformDsl
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkStatic
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class VideoSpecStrategyTest {

    private lateinit var specificationStrategy: VideoSpecStrategy

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        specificationStrategy = spyk(recordPrivateCalls = true)
        mockkStatic(GLog::class)
        mockkStatic(ConfigAbilityWrapper::class)
        every { ConfigAbilityWrapper.getBoolean(any<String>()) } returns false
    }

    @Test
    fun should_return_false_when_processSpecification_with_invalid_specification() {
        // 异常尺寸返回不支持编辑

        // given
        val specification1 = VideoSpec(0, 1080, 60f)
        val specification2 = VideoSpec(1920, 0, 60f)
        val specification3 = VideoSpec(1920, 1080, 0f)

        // when
        val ans1 = specificationStrategy.processSpec("", specification1)
        val ans2 = specificationStrategy.processSpec("", specification2)
        val ans3 = specificationStrategy.processSpec("", specification3)

        // then
        Assert.assertEquals(ans1, false)
        Assert.assertEquals(ans2, false)
        Assert.assertEquals(ans3, false)
    }

    @Test
    fun should_return_true_when_processSpecification_with_normal_specification() {
        // 正常尺寸返回支持编辑
        mockkConstructor(CodecSupportParser::class)
        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { true }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers { 60f }

        // given
        val specification1 = VideoSpec(1920, 1080, 30f)
        val specification2 = VideoSpec(1080, 1920, 30f)

        // when
        val ans1 = specificationStrategy.processSpec("", specification1)
        val ans2 = specificationStrategy.processSpec("", specification2)

        // then
        Assert.assertEquals(ans1, true)
        Assert.assertEquals(ans2, true)
    }

    @Test
    fun should_return_false_when_processSpecification_with_unsupported_specification() {
        // 硬件解码器对应格式的支持能力达不到视频尺寸，返回不支持编辑

        // given
        val specification1 = VideoSpec(1920, 1080, 60f)
        val specification2 = VideoSpec(1080, 1920, 60f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { false }

        // when
        val ans1 = specificationStrategy.processSpec("", specification1)
        val ans2 = specificationStrategy.processSpec("", specification2)

        // then
        Assert.assertEquals(ans1, false)
        Assert.assertEquals(ans2, false)
    }

    @Test
    fun should_return_ture_when_processSpecification_with_supported_specification() {
        // 硬件解码器对应格式的支持能力达到视频尺寸，返回支持编辑

        // given
        val specification1 = VideoSpec(1920, 1080, 60f)
        val specification2 = VideoSpec(1080, 1920, 60f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { true }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers { 120f }

        // when
        val ans1 = specificationStrategy.processSpec("", specification1)
        val ans2 = specificationStrategy.processSpec("", specification2)

        // then
        Assert.assertEquals(ans1, true)
        Assert.assertEquals(ans2, true)
    }

    @Test
    fun should_return_original_when_processSpecification_with_supported_specification() {
        // 如果视频尺寸较小，按原尺寸返回

        // given
        val specification = VideoSpec(100, 100, 60f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { true }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers { 60f }

        // when
        val ans = specificationStrategy.processSpec("", specification)

        // then
        Assert.assertEquals(ans, true)
        Assert.assertEquals(specification, VideoSpec(100, 100, 60f))
    }

    @Test
    fun should_limit_to_1080p_when_processSpecification_with_unsupported_type() {
        // 硬件解码器没有找到对应的格式支持能力，限制到1080p尝试处理

        // given
        val specification = VideoSpec(3840, 2160, 60f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { null }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers {
            60f
        }

        // when
        val ans = specificationStrategy.processSpec("", specification)

        // then
        Assert.assertEquals(ans, true)
        Assert.assertEquals(specification, VideoSpec(1920, 1080, 60f))
    }

    @Test
    fun should_limit_to_4k_when_processSpecification_with_supported_size() {
        // 硬件解码器对应格式的支持能力达到视频的尺寸，还是限制尺寸到4k大小

        // given
        val specification = VideoSpec(38400, 21600, 60f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { true }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers { 60f }

        // when
        val ans = specificationStrategy.processSpec("", specification)

        // then
        Assert.assertEquals(ans, true)
        Assert.assertEquals(specification, VideoSpec(3840, 2160, 60f))
    }

    @Test
    fun should_limit_to_60fps_when_processSpecification_with_supported_fps() {
        //  硬件解码器对应格式的支持fps大于60fps，限制视频fps最大值为60fps

        // given
        val specification = VideoSpec(3840, 2160, 60f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { true }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers { 120f }

        // when
        val ans = specificationStrategy.processSpec("", specification)

        // then
        Assert.assertEquals(ans, true)
        Assert.assertEquals(specification, VideoSpec(3840, 2160, 60f))
    }

    @Test
    fun should_limit_to_30fps_when_processSpecification_with_unsupported_fps() {
        //  如果硬件不支持此格式视频，则返回不支持编辑

        // given
        val specification = VideoSpec(3840, 2160, 60f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { true }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers { 0f }

        // when
        val ans = specificationStrategy.processSpec("", specification)

        // then
        Assert.assertEquals(ans, false)
    }

    @Test
    fun should_keep_fps_when_processSpecification_with_normal_fps() {
        //   编辑视频帧率将修改为不超过限制最大值

        // given
        val specification = VideoSpec(3840, 2160, 24f)
        mockkConstructor(CodecSupportParser::class)

        every { anyConstructed<CodecSupportParser>().isSupportSize(any(), any()) } answers { true }
        every { anyConstructed<CodecSupportParser>().getSpecificationMaxFps(any(), any()) } answers { 30f }

        // when
        val ans = specificationStrategy.processSpec("", specification)

        // then
        Assert.assertEquals(ans, true)
        Assert.assertEquals(specification, VideoSpec(3840, 2160, 24f))
    }

    @Test
    fun should_keep_specification_when_resolutionLimit_with_invalid_limit() {
        // 限制输入视频规格尺寸到限制分辨率，异常分辨率不做处理

        // given
        val specification = VideoSpec(0, 1080, 60f)

        // when

        InternalPlatformDsl.dynamicCall(specificationStrategy, "resolutionLimit", arrayOf(specification, VideoResolutionType.R_1080P), mockk())

        // then
        Assert.assertEquals(specification, VideoSpec(0, 1080, 60f))
    }

    @Test
    fun should_keep_specification_when_resolutionLimit_with_no_limit() {
        // 限制输入视频规格尺寸到限制分辨率，限制分辨率内不做处理

        // given
        val specification = VideoSpec(1920, 1080, 60f)

        // when

        InternalPlatformDsl.dynamicCall(specificationStrategy, "resolutionLimit", arrayOf(specification, VideoResolutionType.R_1080P), mockk())

        // then
        Assert.assertEquals(specification, VideoSpec(1920, 1080, 60f))
    }

    @Test
    fun should_keep_specification_ratio_when_resolutionLimit_with_limit() {
        // 限制输入视频规格尺寸到限制分辨率，超过限制分辨率等比缩放到限制值

        // given
        val specification = VideoSpec(3840, 2160, 60f)

        // when

        InternalPlatformDsl.dynamicCall(specificationStrategy, "resolutionLimit", arrayOf(specification, VideoResolutionType.R_1080P), mockk())

        // then
        Assert.assertEquals(specification, VideoSpec(1920, 1080, 60f))
    }
}