/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AbsLabelParserTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/20
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2021/12/20      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.business_lib.model.data.label

import android.content.Context
import com.oplus.gallery.business_lib.model.data.label.bean.LabelWord
import com.oplus.gallery.business_lib.model.data.label.structure.GenericTrie
import com.oplus.gallery.foundation.util.text.TextUtil
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class AbsLabelParserTest {

    private lateinit var labelParser: AbsLabelParser

    @Before
    fun setUp() {
        labelParser = object : AbsLabelParser() {
            override fun getTag(): String {
                return "${PRE_TAG}AbsLabelParserTest"
            }

            override fun load(context: Context): GenericTrie<LabelWord>? {
                return null
            }
        }
    }

    /**
     * 如果key sceneIdStr在map中找不到，是不是应该不替换
     */
    @Test
    fun should_not_replace_when_replaceDiffPart_without_key_sceneIdStr() {
        // Given
        val sceneIdStr = "1000"
        val originArray = DIC_LINE_SPLIT_ARRAY
        val dicLineSplitArray: MutableList<String> = mutableListOf<String>().apply {
            addAll(originArray)
        }

        // When
        labelParser.replaceDiffPart(DIFFS_MAP, sceneIdStr, dicLineSplitArray)

        // Then 执行完replace之后内容没有变换
        assert(dicLineSplitArray.containsAll(originArray))
    }

    /**
     * 如果key sceneIdStr在map中能找到
     * 但是array.size<dicColumnIndex,是不是应该不替换
     */
    @Test
    fun should_not_replace_when_replaceDiffPart_with_array_size_smaller_than_dicColumnIndex() {
        // Given
        val sceneIdStr = DIFF_EXIST_SCENE_ID_STR
        val originArray = DIC_LINE_SPLIT_ARRAY.subList(0, 1)
        val dicLineSplitArray: MutableList<String> = mutableListOf<String>().apply {
            addAll(originArray)
        }

        // When
        labelParser.replaceDiffPart(DIFFS_MAP, sceneIdStr, dicLineSplitArray)

        // Then 执行完replace之后内容没有变换
        assert(dicLineSplitArray.containsAll(originArray))
    }

    /**
     * 如果key sceneIdStr在map中能找到
     * 且array.size>=dicColumnIndex
     * 是不是应该替换指定部分
     */
    @Test
    fun should_replace_when_replaceDiffPart_with_condition_okay() {
        // Given
        val sceneIdStr = DIFF_EXIST_SCENE_ID_STR
        val originArray = DIC_LINE_SPLIT_ARRAY
        val dicLineSplitArray: MutableList<String> = mutableListOf<String>().apply {
            addAll(originArray)
        }

        // When
        labelParser.replaceDiffPart(DIFFS_MAP, sceneIdStr, dicLineSplitArray)

        // Then 执行完replace之后内容已经变换
        assert(!dicLineSplitArray.containsAll(originArray))
    }

    /**
     * 如果key sceneIdStr在map中能找到
     * 且array.size>=dicColumnIndex
     * 是不是应该替换指定部分，且被替换之后的内容就是指定部分
     */
    @Test
    fun should_equal_when_replaceDiffPart_with_condition_okay() {
        // Given
        val sceneIdStr = DIFF_EXIST_SCENE_ID_STR
        val originArray = DIC_LINE_SPLIT_ARRAY
        val dicLineSplitArray: MutableList<String> = mutableListOf<String>().apply {
            addAll(originArray)
        }
        val expected = DIFFS_MAP[DIFF_EXIST_SCENE_ID_STR]?.dicColumnValue

        // When
        labelParser.replaceDiffPart(DIFFS_MAP, sceneIdStr, dicLineSplitArray)

        // Then 执行完replace之后内容已经变换
        val actual = dicLineSplitArray[DIFF_DIC_COLUMN_INDEX]
        assertEquals(expected, actual)
    }

    /**
     * 如果传参name是""
     * 那么回调的值还是""
     */
    @Test
    fun should_not_replace_when_replaceSpecialCharInName_with_name_empty() {
        // Given
        val name = TextUtil.EMPTY_STRING

        // When
        val actual = labelParser.replaceSpecialCharInName(name)

        // Then
        assert(actual.isEmpty())
    }

    /**
     * 如果传参name不是"",也不带有&amp;
     * 如果传参name是带有\\\\的字符串，那么应该将字符串中的\\\\去掉
     */
    @Test
    fun should_replace_when_replaceSpecialCharInName_with_name_contains_four_backslash() {
        // Given
        val name = "asdf\\\\ghjkl"
        val expected = "asdfghjkl"

        // When
        val actual = labelParser.replaceSpecialCharInName(name)

        // Then
        assertEquals(expected, actual)
    }

    /**
     * 如果传参name不是"",也不带有\\\\
     * 如果传参name是带有的转义字符&amp;，那么应该将字符串中的&amp;改成&
     */
    @Test
    fun should_replace_when_replaceSpecialCharInName_with_name_contains_escape_character_and() {
        // Given
        val name = "asdf&amp;ghjkl"
        val expected = "asdf&ghjkl"

        // When
        val actual = labelParser.replaceSpecialCharInName(name)

        // Then
        assertEquals(expected, actual)
    }

    /**
     * 如果传参name不是"",也不带有\\\\
     * 如果传参name是带有的转义字符&amp;或者\\\\，那么应该将字符串中的&amp;改成&，而\\\\直接去掉
     */
    @Test
    fun should_replace_when_replaceSpecialCharInName_with_name_contains_escape_character() {
        // Given
        val name = "asdf&amp;gh\\\\jkl"
        val expected = "asdf&ghjkl"

        // When
        val actual = labelParser.replaceSpecialCharInName(name)

        // Then
        assertEquals(expected, actual)
    }

    companion object {
        private const val DIFF_EXIST_SCENE_ID_STR = "1"
        private const val DIFF_DIC_COLUMN_INDEX = 3

        private val DIFFS_MAP: Map<String, AbsLabelParser.Diff> = HashMap<String, AbsLabelParser.Diff>().apply {
            put(
                DIFF_EXIST_SCENE_ID_STR,
                AbsLabelParser.Diff("2", DIFF_DIC_COLUMN_INDEX, "59,61,62,63,147,163,215,338").copy()
            )
        }

        // 词典中的某一行字符串被解析成了一个标签的各种属性字符串数组
        private val DIC_LINE_SPLIT_ARRAY = mutableListOf<String>().apply {
            add("Dot") // 名称
            add("3") // 标签id
            add("-1") //父类id
            add("4,102,103") // 子类id
            add("-1") // 近义词
            add("1") // 是否显示（这个值没啥用）
            add("狗") // 键
        }
    }
}