/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SecurityShareServiceHelperTest
 ** Description:
 ** Version: 1.0
 ** Date: 2022/1/17
 ** Author: dingyong@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>        <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** dingyong@Apps.Gallery3D      2022/1/17    1.0       SecurityShareServiceHelperTest
 ********************************************************************************/


package com.oplus.gallery.business_lib.securityshare.service

import com.oplus.gallery.business_lib.securityshare.service.base.ISecurityShareService
import com.oplus.gallery.business_lib.securityshare.service.image.SecurityShareHEIFService
import com.oplus.gallery.business_lib.securityshare.service.image.SecurityShareJpgPngService
import com.oplus.gallery.business_lib.securityshare.service.image.SecurityShareOtherService
import com.oplus.gallery.business_lib.securityshare.service.image.SecurityShareRawService
import com.oplus.gallery.business_lib.securityshare.service.video.SecurityShareVideoService
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.powermock.core.classloader.annotations.PrepareForTest

@PrepareForTest(
    ISecurityShareService::class,
    MimeTypeUtils::class
)
class SecurityShareServiceFactoryTest {

    @Before
    fun setUp() {
        mockkStatic(ISecurityShareService::class)
        mockkStatic(MimeTypeUtils::class)
    }

    @Test
    fun should_return_true_create_image_security_share_service_jpeg() {
        val resultJpeg = SecurityShareServiceFactory.createSecurityShareService(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE,
            MimeTypeUtils.MIME_TYPE_IMAGE_JPEG
        )
        Assert.assertTrue(resultJpeg is SecurityShareJpgPngService)
    }

    @Test
    fun should_return_true_create_image_security_share_service_png() {
        val resultJpeg = SecurityShareServiceFactory.createSecurityShareService(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE,
            MimeTypeUtils.MIME_TYPE_IMAGE_PNG
        )
        Assert.assertTrue(resultJpeg is SecurityShareJpgPngService)
    }

    @Test
    fun should_return_true_create_image_security_share_service_heic() {
        val resultJpeg = SecurityShareServiceFactory.createSecurityShareService(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE,
            MimeTypeUtils.MIME_TYPE_IMAGE_HEIC
        )
        Assert.assertTrue(resultJpeg is SecurityShareHEIFService)
    }

    @Test
    fun should_return_true_create_image_security_share_service_heif() {
        val resultJpeg = SecurityShareServiceFactory.createSecurityShareService(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE,
            MimeTypeUtils.MIME_TYPE_IMAGE_HEIF
        )
        Assert.assertTrue(resultJpeg is SecurityShareHEIFService)
    }


    @Test
    fun should_return_true_create_image_security_share_service_raw() {
        val resultJpeg = SecurityShareServiceFactory.createSecurityShareService(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE,
            MimeTypeUtils.MIME_TYPE_IMAGE_RAW
        )
        Assert.assertTrue(resultJpeg is SecurityShareRawService)
    }

    @Test
    fun should_return_true_create_image_security_share_service_other() {
        val resultJpeg = SecurityShareServiceFactory.createSecurityShareService(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE,
            MimeTypeUtils.MIME_TYPE_ANY
        )
        Assert.assertTrue(resultJpeg is SecurityShareOtherService)
    }

    @Test
    fun should_return_true_create_image_security_share_service_video() {
        val resultJpeg = SecurityShareServiceFactory.createSecurityShareService(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO,
            MimeTypeUtils.MIME_TYPE_ANY
        )
        Assert.assertTrue(resultJpeg is SecurityShareVideoService)
    }
}