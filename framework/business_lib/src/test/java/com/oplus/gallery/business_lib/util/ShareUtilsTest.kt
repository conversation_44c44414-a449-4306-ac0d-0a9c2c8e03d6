/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - ShareUtilsTest
 ** Description: Test for ShareUtils.
 ** Version: 1.0
 ** Date : 2022/12/16
 ** Author: <EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wenhao.Deng   2022/12/16      1.0        created
 ***************************************************************/
package com.oplus.gallery.business_lib.util

import com.oplus.gallery.business_lib.util.ShareUtils.OShareShowType
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class ShareUtilsTest {
    private lateinit var mockContext: GalleryApplication

    @Before
    fun setUp() {
        mockkObject(ContextGetter)
        mockContext = mockk()
        every { ContextGetter.context } returns mockContext
        every { mockContext.applicationContext } returns mockContext
    }

    @Test
    fun `should return true when has dolby decode and encode`() {
        // Given
        every { mockContext.getAppAbility<IConfigAbility>() } returns mockk {
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_DECODE) } returns true
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE) } returns true
            every { close() } returns Unit
        }

        // When
        val result = ShareUtils.isSupportDolbyCodec()

        // Then
        Assert.assertTrue(result)
    }

    @Test
    fun `should return false when only has dolby decode`() {
        // Given
        every { mockContext.getAppAbility<IConfigAbility>() } returns mockk {
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_DECODE) } returns true
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE) } returns false
            every { close() } returns Unit
        }

        // When
        val result = ShareUtils.isSupportDolbyCodec()

        // Then
        Assert.assertFalse(result)
    }

    @Test
    fun `should return false when only has dolby encode`() {
        // Given
        every { mockContext.getAppAbility<IConfigAbility>() } returns mockk {
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_DECODE) } returns false
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE) } returns true
            every { close() } returns Unit
        }

        // When
        val result = ShareUtils.isSupportDolbyCodec()

        // Then
        Assert.assertFalse(result)
    }

    @Test
    fun `should return false when has no dolby encode and no dolby decode`() {
        // Given
        every { mockContext.getAppAbility<IConfigAbility>() } returns mockk {
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_DECODE) } returns false
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE) } returns false
            every { close() } returns Unit
        }

        // When
        val result = ShareUtils.isSupportDolbyCodec()

        // Then
        Assert.assertFalse(result)
    }

    @Test
    fun `should return true when has hlg encode`() {
        // Given
        every { mockContext.getAppAbility<IConfigAbility>() } returns mockk {
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_HLG_ENCODE) } returns true
            every { close() } returns Unit
        }

        // When
        val result = ShareUtils.isSupportHlgCodec()

        // Then
        Assert.assertTrue(result)
    }

    @Test
    fun `should return false when has no hlg encode`() {
        // Given
        every { mockContext.getAppAbility<IConfigAbility>() } returns mockk {
            every { getBooleanConfig(ConfigID.Common.SystemInfo.IS_SUPPORT_HLG_ENCODE) } returns false
            every { close() } returns Unit
        }

        // When
        val result = ShareUtils.isSupportHlgCodec()

        // Then
        Assert.assertFalse(result)
    }

    @Test
    fun `should hide oshare when not support`() {
        // Given ：不支持OShare
        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isSupportOplusShare } returns false

        // When
        val result = ShareUtils.getOShareTypeInSharePage()

        // Then
        Assert.assertEquals(result, OShareShowType.HIDE)
    }
    @Test
    fun `should show oshare with old UI for domestic`() {
        // Given ：支持OShare && 不支持NewPanel && 内销
        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isSupportOplusShare } returns true

        mockkStatic(ShareUtils::class)
        every { ShareUtils.supportNewSharePanel() } returns false
        every { ShareUtils.isRegionCN } returns true

        // When
        val result = ShareUtils.getOShareTypeInSharePage()

        // Then
        Assert.assertEquals(result, OShareShowType.SHOW_WITH_OLD_UI)
    }

    @Test
    fun `should show oshare with old UI for export`() {
        // Given ：支持OShare && 不支持NewPanel && 外销 && 非一加
        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isSupportOplusShare } returns true

        mockkStatic(ShareUtils::class)
        every { ShareUtils.supportNewSharePanel() } returns false
        every { ShareUtils.isRegionCN } returns false
        every { ShareUtils.isOnePlusBrand() } returns false

        // When
        val result = ShareUtils.getOShareTypeInSharePage()

        // Then
        Assert.assertEquals(result, OShareShowType.SHOW_WITH_OLD_UI)
    }

    @Test
    fun `should show oshare with new UI for domestic`() {
        // Given ：支持OShare && 支持NewPanel && 内销
        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isSupportOplusShare } returns true

        mockkStatic(ShareUtils::class)
        every { ShareUtils.supportNewSharePanel() } returns true
        every { ShareUtils.isRegionCN } returns true

        // When
        val result = ShareUtils.getOShareTypeInSharePage()

        // Then
        Assert.assertEquals(result, OShareShowType.SHOW_WITH_NEW_UI)
    }

    @Test
    fun `should show oshare in app list for export oneplus`() {
        // Given ：支持OShare && 支持NewPanel && 外销(一加)  && 当前APILevel >= 35
        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isSupportOplusShare } returns true

        mockkStatic(ShareUtils::class)
        every { ShareUtils.supportNewSharePanel() } returns true
        every { ShareUtils.isRegionCN } returns false
        every { ShareUtils.isOnePlusBrand() } returns true

        mockkStatic(ApiLevelUtil::class)
        every { ApiLevelUtil.isApiLevelAtLeastV() } returns true

        // When
        val result = ShareUtils.getOShareTypeInSharePage()

        // Then
        Assert.assertEquals(result, OShareShowType.SHOW_IN_APP_LIST)
    }


    @Test
    fun `should show oshare in app list for export oppo`() {
        // Given ：支持OShare && 支持NewPanel && 外销(非一加)  && 当前APILevel >= 35
        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isSupportOplusShare } returns true

        mockkStatic(ShareUtils::class)
        every { ShareUtils.supportNewSharePanel() } returns true
        every { ShareUtils.isRegionCN } returns false
        every { ShareUtils.isOnePlusBrand() } returns false

        mockkStatic(ApiLevelUtil::class)
        every { ApiLevelUtil.isApiLevelAtLeastV() } returns true

        // When
        val result = ShareUtils.getOShareTypeInSharePage()

        // Then
        Assert.assertEquals(result, OShareShowType.SHOW_IN_APP_LIST)
    }

    @Test
    fun `should show oshare in app list for export oppo U`() {
        // Given ：支持OShare && 支持NewPanel && 外销 && 非一加 && 当前APILevel = 34 &&  FirstApiLevel < 34
        mockkStatic(FeatureUtils::class)
        every { FeatureUtils.isSupportOplusShare } returns true

        mockkStatic(ShareUtils::class)
        every { ShareUtils.supportNewSharePanel() } returns true
        every { ShareUtils.isRegionCN } returns false
        every { ShareUtils.isOnePlusBrand() } returns false

        mockkStatic(ApiLevelUtil::class)
        every { ApiLevelUtil.isAndroidU() } returns true
        every { ApiLevelUtil.isFirstApiLevelAtLeastU() } returns false

        // When
        val result = ShareUtils.getOShareTypeInSharePage()

        // Then
        Assert.assertEquals(result, OShareShowType.SHOW_IN_APP_LIST)
    }
}