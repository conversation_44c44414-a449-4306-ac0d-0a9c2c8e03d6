/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 * File: - VideoSpecStrategyMockKTest.kt
 * Description:
 * Version: 1.0
 * Date: 2025/08/05
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery        2025/08/05      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.business_lib.videoedit

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupport
import io.mockk.MockKAnnotations
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test

/**
 * VideoSpecStrategy 的单元测试类
 * 使用 MockK 框架进行依赖模拟
 */
class VideoSpecStrategyMockKTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        
        // Mock 静态方法
        mockkObject(GLog::class)
        mockkStatic(GLog::class)
        mockkObject(CodecSupport::class)
        
        // 设置默认的 Mock 行为
        every { GLog.e(any(), any(), any<String>()) } just Runs
        every { GLog.d(any(), any<Int>(), any<() -> String>()) } just Runs
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `processSpec should return null when video spec has invalid dimensions`() {
        // Given - 无效的视频规格（宽度为0）
        val invalidSpec = VideoSpec(0, 1080, 30f)
        val mimeType = "video/avc"
        val options = SpecOptions()

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, invalidSpec, options)

        // Then
        assertNull("应该返回null，因为视频规格无效", result)
        
        // 验证错误日志被调用
        verify { GLog.e("VideoSpecificationStrategy", LogFlag.DL, any<String>()) }
    }

    @Test
    fun `processSpec should return null when video spec has invalid height`() {
        // Given - 无效的视频规格（高度为负数）
        val invalidSpec = VideoSpec(1920, -1, 30f)
        val mimeType = "video/avc"
        val options = SpecOptions()

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, invalidSpec, options)

        // Then
        assertNull("应该返回null，因为视频规格无效", result)
    }

    @Test
    fun `processSpec should return null when video spec has invalid fps`() {
        // Given - 无效的视频规格（帧率为0）
        val invalidSpec = VideoSpec(1920, 1080, 0f)
        val mimeType = "video/avc"
        val options = SpecOptions()

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, invalidSpec, options)

        // Then
        assertNull("应该返回null，因为视频规格无效", result)
    }

    @Test
    fun `processSpec should return original spec when codec is supported`() {
        // Given - 支持的视频规格
        val supportedSpec = VideoSpec(1920, 1080, 30f)
        val mimeType = "video/avc"
        val options = SpecOptions()

        // Mock CodecSupport 返回支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, supportedSpec) } returns true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, supportedSpec, options)

        // Then
        assertEquals("应该返回原始规格", supportedSpec, result)
        
        // 验证调用了相关方法
        verify { CodecSupport.dumpVideoCapabilities(any()) }
        verify { CodecSupport.isSupportCodec(mimeType, supportedSpec) }
    }

    @Test
    fun `processSpec should return null when codec not supported and reduceResolution is false`() {
        // Given - 不支持的视频规格，且不允许降分辨率
        val unsupportedSpec = VideoSpec(3840, 2160, 60f)
        val mimeType = "video/avc"
        val options = SpecOptions(reduceResolution = false, reduceFps = false)

        // Mock CodecSupport 返回不支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, unsupportedSpec) } returns false

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, unsupportedSpec, options)

        // Then
        assertNull("应该返回null，因为不支持且不允许降规格", result)
    }

    @Test
    fun `processSpec should handle small video dimensions correctly`() {
        // Given - 小尺寸视频规格
        val smallSpec = VideoSpec(640, 480, 30f)
        val mimeType = "video/avc"
        val options = SpecOptions()

        // Mock CodecSupport 返回支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, smallSpec) } returns true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, smallSpec, options)

        // Then
        assertEquals("小尺寸视频应该直接支持", smallSpec, result)
    }

    @Test
    fun `processSpec should handle portrait video correctly`() {
        // Given - 竖屏视频规格
        val portraitSpec = VideoSpec(1080, 1920, 30f)
        val mimeType = "video/avc"
        val options = SpecOptions()

        // Mock CodecSupport 返回支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, portraitSpec) } returns true

        // When
        val result = VideoSpecStrategy.processSpec(mimeType, portraitSpec, options)

        // Then
        assertEquals("竖屏视频应该正确处理", portraitSpec, result)
    }

    @Test
    fun `processSpec should handle different mime types`() {
        // Given - 不同的MIME类型
        val spec = VideoSpec(1920, 1080, 30f)
        val hevcMimeType = "video/hevc"
        val options = SpecOptions()

        // Mock CodecSupport 返回支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(hevcMimeType, spec) } returns true

        // When
        val result = VideoSpecStrategy.processSpec(hevcMimeType, spec, options)

        // Then
        assertEquals("HEVC格式应该正确处理", spec, result)
        
        // 验证使用了正确的MIME类型
        verify { CodecSupport.isSupportCodec(hevcMimeType, spec) }
    }

    @Test
    fun `processSpec should log debug information`() {
        // Given
        val spec = VideoSpec(1920, 1080, 30f)
        val mimeType = "video/avc"
        val options = SpecOptions()
        val mockCapabilities = "mock video capabilities info"

        // Mock
        every { CodecSupport.dumpVideoCapabilities(any()) } returns mockCapabilities
        every { CodecSupport.isSupportCodec(mimeType, spec) } returns true

        // When
        VideoSpecStrategy.processSpec(mimeType, spec, options)

        // Then - 验证调试日志被正确调用
        verify { GLog.d("VideoSpecificationStrategy", LogFlag.DL, any<() -> String>()) }
        verify { CodecSupport.dumpVideoCapabilities(match { 
            it.mimeType == mimeType && !it.isEncoder && !it.isDesireHardwareAccelerated 
        }) }
    }

    @Test
    fun `processSpec should use default options when not provided`() {
        // Given - 不提供options参数
        val spec = VideoSpec(1920, 1080, 30f)
        val mimeType = "video/avc"

        // Mock CodecSupport 返回支持
        every { CodecSupport.dumpVideoCapabilities(any()) } returns "mock capabilities"
        every { CodecSupport.isSupportCodec(mimeType, spec) } returns true

        // When - 不传递options参数，应该使用默认值
        val result = VideoSpecStrategy.processSpec(mimeType, spec)

        // Then
        assertEquals("应该使用默认选项并返回原始规格", spec, result)
    }
}
