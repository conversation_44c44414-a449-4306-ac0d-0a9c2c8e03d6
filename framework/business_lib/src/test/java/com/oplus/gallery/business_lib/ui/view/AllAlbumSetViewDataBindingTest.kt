/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - AllAlbumSetViewDataBindingTest.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2022/03/28
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery		2022/03/28		1.0			OPLUS_ARCH_EXTENDS
 * W9002848                     2024/6/12       1.1         Add Suppress ArrayPrimitive
 ******************************************************************************/

package com.oplus.gallery.business_lib.ui.view

import android.content.Context
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.business_lib.viewmodel.style.StylePool
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized

private const val DRAWABLE_SIZE = 4

@RunWith(Parameterized::class)
class GetOtherAlbumShowCountTest(private val testCount: Int, private val expected: Int) {

    @Test
    fun should_return_true_when_getOtherAlbumShowCount() {
        val context = mockk<Context>()
        every { context.getColor(any()) } returns 0
        val stylePool = mockk<StylePool>()
        every { stylePool.getStyle(any()) } returns StyleData()
        every { context.resources.getString(any()) } returns "ABC"
        val allAlbumSetViewDataBinding =
            AllAlbumSetViewDataBinding(context, stylePool = stylePool, isMaskVisibleGet = { false }, checkboxAnimEnableGet = { false })

        val albumViewData = mockk<AlbumViewData>()
        every { albumViewData.totalCount } returns testCount
        val actual = allAlbumSetViewDataBinding.getOtherAlbumShowCount(albumViewData, DRAWABLE_SIZE)
        assertEquals(expected, actual)
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        @Suppress("ArrayPrimitive")
        fun parameters(): Collection<*> {
            return listOf(
                arrayOf(0, 0),
                arrayOf(1, 0),
                arrayOf(2, 0),
                arrayOf(3, 0),
                arrayOf(4, 0),
                arrayOf(5, 1),
                arrayOf(10, 6),
                arrayOf(-1, 0)
            )
        }
    }
}

@RunWith(Parameterized::class)
class ShareAlbumCoverIndexTest(private val testCount: Int, private val expected: Int) {

    @Test
    fun should_return_true_when_getShareAlbumCoverIndex() {
        val context = mockk<Context>()
        every { context.getColor(any()) } returns 0
        val stylePool = mockk<StylePool>()
        every { stylePool.getStyle(any()) } returns StyleData()
        every { context.resources.getString(any()) } returns "ABC"
        val allAlbumSetViewDataBinding =
            AllAlbumSetViewDataBinding(context, stylePool = stylePool, isMaskVisibleGet = { false }, checkboxAnimEnableGet = { false })

        val albumViewData = mockk<AlbumViewData>()
        every { albumViewData.totalCount } returns testCount
        val actual = allAlbumSetViewDataBinding.getShareAlbumCoverIndex(albumViewData, DRAWABLE_SIZE)
        assertEquals(expected, actual)
    }

    companion object {
        @Parameterized.Parameters
        @JvmStatic
        @Suppress("ArrayPrimitive")
        fun parameters(): Collection<*> {
            return listOf(
                arrayOf(0, 0),
                arrayOf(1, 0),
                arrayOf(2, 1),
                arrayOf(3, 2),
                arrayOf(4, 3),
                arrayOf(5, 3),
                arrayOf(-1, 0)
            )
        }
    }
}