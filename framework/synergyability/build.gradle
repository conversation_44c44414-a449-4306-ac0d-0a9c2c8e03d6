plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}

apply from: "${rootDir}/gradle/libCommon.gradle"

// 这部分一定要在android的前面
ext {
    mavenDescription = "相册多端互联能力模块"
    mavenGroupId = mavenGroupName
}

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion
    resourcePrefix project.name + "_"
    namespace 'com.oplus.gallery.framework.abilities.synergy'

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    //忽略，olint扫描项:代码混淆风险
    lintOptions {
        disable  "MinifyEnabled"
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation "com.oplus.gallery.router_lib:annotations:$routerAnnotationsVersion"
    kapt "com.oplus.gallery.router_lib:compiler:$routerCompilerVersion"

    //PC互联中控服务SDK
    api "com.oplus.synergysdk:synergysdkcompat:$synergysdkVersion"

    // 用于HeyCast CommonUtil.getUIBCDevice，
    implementation "com.oplus.cast.api:castsdk:${heycastVersion}"
    implementation "com.oplus.appprovider:settings-compat:${appproviderSettingsCompatVersion}"

    implementationProject(':basebiz')
    implementationProject(':framework')
    implementationProject(':framework:business_lib')
    implementationProject(':foundation:libcache')
    implementationProject(':foundation:libcodec')
    implementationProject(':foundation:librouter')
    implementationProject(':foundation:libtaskscheduling')
    implementationProject(':foundation:libutil')
    implementationProject(':foundation:libsysapi')
    implementationProject(':framework:abilityapi')
    //Marked by zhangwenming 将data部分及相关依赖临时迁移到framework层而添加，后面整改时，需要去掉。
    implementationProject(':framework:dataabilitytmp')
    implementation "androidx.appcompat:appcompat:$appcompatVersion"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinxCoroutinesCore"
}