<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!--PC互联需求中访问系统中控应用所需要的权限-->
    <uses-permission android:name="com.oplus.permission.safe.CONNECTIVITY"/>

    <queries>
        <!-- PC/PAD互联中拖拽传输，需要先判断是否有对应的包 -->
        <package android:name="com.oplus.synergy"/>
        <package android:name="com.heytap.synergy"/>
    </queries>

    <application
        tools:replace="android:allowBackup"
        android:allowBackup="false" />
</manifest>