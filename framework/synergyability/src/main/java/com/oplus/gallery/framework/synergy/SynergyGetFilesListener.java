/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - SynergyGetFileListener.java
 * * Description:
 * * Version: 1.0
 * * Date : 2020/06/04
 * * Author: huanglinpeng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  huanglinpeng@Apps.Gallery3D  2020/06/04        build this module
 ****************************************************************/
package com.oplus.gallery.framework.synergy;

import com.oplus.gallery.business_lib.model.data.base.Path;

import java.util.Set;

/**
 * PC互联文件点选接口
 */
public interface SynergyGetFilesListener {
    Set<Path> getSelectedItemPaths(float x, float y);
}
