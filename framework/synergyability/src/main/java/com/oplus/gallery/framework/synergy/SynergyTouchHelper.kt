/****************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - TabletSynergyHelper.kt
 ** Description: 只用于平板互联状态的查询
 **
 ** Version: 1.0
 ** Date : 2022/1/26
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery
 ** ------------------------------- Revision History: -----------
 **  <author>                 <data>       <version>   <desc>
 ** -------------------------------------------------------------
 **  Tan<PERSON><PERSON><PERSON>@Apps.Gallery    2022/1/26    1.0         create
 *****************************************************************/

package com.oplus.gallery.framework.synergy

import android.net.Uri
import android.text.TextUtils
import android.view.MotionEvent
import com.oplus.cast.service.sdk.util.CommonUtil
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.AppVersionCode.CAST_VERSION_CODE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager.registerContentObserver
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.providers.AppSettings

/**
 * 互联辅助类
 * 1、查询和监听 平板端相册 和跨屏互联服务的连接状态
 * 2、查询事件来源 是否为互联设备(平板端、PC电脑端)
 */
object SynergyTouchHelper {
    private const val TAG = "SynergyTouchHelper"
    private const val KEY_PAD_CONNECT_STATE = "pad_connect_state"
    private const val PAD_SYNERGY_CONNECTED = 1
    private const val PAD_SYNERGY_NOT_CONNECTED = 0
    private const val PAD_UIBC = "PAD_UIBC"

    private val tabletConnectStateObserver = object : HandlerContentObserver(null) {
        override fun onChange(selfChange: Boolean, uri: Uri?) {
            isSynergyEnable = isTabletSynergyEnable()
            GLog.d(TAG, "onChange : $isSynergyEnable")
        }
    }

    /**
     * 判断相册(平板端)是否与Synergy服务连接，若已连接，则可触发拖拽
     */
    var isSynergyEnable = false
        private set

    @Synchronized
    fun init() {
        if (FeatureUtils.isTablet && OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_12_0)) {
            isSynergyEnable = isTabletSynergyEnable()
            GLog.d(TAG, "init, isConnect = $isSynergyEnable")
            registerTabletConnectStateObserver()
        } else {
            GLog.d(TAG, "init, not support tablet connect")
        }
    }

    /**
     * 只用于平板判断当前是否处于互联已连接状态
     * @return
     */
    private fun isTabletSynergyEnable(): Boolean = runCatching {
       AppSettings.Secure.getInt(ContextGetter.context.contentResolver, KEY_PAD_CONNECT_STATE, PAD_SYNERGY_NOT_CONNECTED)
    }.onFailure {
        GLog.e(TAG, "isTabletSynergyEnable, e:", it)
    }.getOrNull() == PAD_SYNERGY_CONNECTED

    private fun registerTabletConnectStateObserver() {
        runCatching {
            ContextGetter.context.registerContentObserver(
                AppSettings.Secure.getUriFor(KEY_PAD_CONNECT_STATE),
                true,
                tabletConnectStateObserver
            )
        }.onFailure {
            GLog.e(TAG, "registerTabletConnectStateObserver e: ", it)
        }
    }

    @JvmStatic
    fun isTouchEventFromTablet(event: MotionEvent): Boolean {
        val isFromDevice = CommonUtil.getUIBCDevice(event)
        return TextUtils.equals(PAD_UIBC, isFromDevice)
    }

    /**
     * 事件来源是否为跨屏互联设备
     */
    @JvmStatic
    fun isTouchEventFromSynergyDevice(event: MotionEvent): Boolean {
        // 目前反控（在大屏控制小屏）的对象只有手机，所以这里只需要判断手机是否处于正常连接状态，后续如果有pc控制平板可以在这里添加
        val isSynergyEnable = ApiDmManager.getSynergyDM().isSynergyEnable()
        if (GProperty.DEBUG_SYNERGY_MOTION_EVENT) {
            GLog.d(TAG, LogFlag.DL) { "isTouchEventFromSynergyDevice. isSynergyEnable = $isSynergyEnable" }
        }
        if (isSynergyEnable.not()) {
            return false
        }

        val isFromSynergyDevice = CommonUtil.checkMotionEvent(CAST_VERSION_CODE.toInt(), event)
        if (GProperty.DEBUG_SYNERGY_MOTION_EVENT) {
            GLog.d(TAG, LogFlag.DL) {
                "isTouchEventFromSynergyDevice. isFromSynergyDevice=$isFromSynergyDevice,deviceId=${event.deviceId},castVersion=$CAST_VERSION_CODE"
            }
        }
        return isFromSynergyDevice
    }
}