/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * VENDOR_EDIT
 * * File:  - SynergyFileInfoCovert.java
 * * Description:
 * * Version: 1.0
 * * Date : 2020/06/04
 * * Author: huanglinpeng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  huanglinpeng@Apps.Gallery3D  2020/06/04        build this module
 ****************************************************************/
package com.oplus.gallery.framework.synergy;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Base64;

import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.foundation.util.graphic.ImageUtils;
import com.oplus.gallery.framework.abilities.caching.CacheOperation;
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility;
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey;
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory;
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions;
import com.oplus.gallery.framework.abilities.resourcing.options.SourceOperation;
import com.oplus.gallery.framework.abilities.resourcing.result.ImageResult;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.synergy.sdk.bean.SynergyFileInfo;

public class SynergyFileInfoCovert implements Covertable<SynergyFileInfo, MediaItem> {

    private static final String TAG = "SynergyFileInfoCovert";
    private static final int MAX_HEIGHT = 96;
    private static final int MAX_WIDTH = 96;
    private static final int HIGH_QUALITY = 100;

    private Context mContext;

    public SynergyFileInfoCovert(Context context) {
        mContext = context;
    }

    @Override
    public SynergyFileInfo covert(MediaItem item, boolean preview) {
        if (item == null) {
            GLog.e(TAG, "[covert] item == null !!!");
            return null;
        }
        long start = System.currentTimeMillis();
        SynergyFileInfo info = new SynergyFileInfo();
        try {
            if (preview) {
                info.setFilePreViewBase64(getPreViewBase64(item));
            }
            info.setFileName(FilePathUtils.getDisplayName(item.getFilePath()));
            info.setFileSize(item.getFileSize());
            info.setFileUri(item.getContentUri());
            info.setFileType(FilePathUtils.extractFileType(item.getFilePath()));
        } catch (Exception e) {
            GLog.e(TAG, "[covert] exception : " + e);
        }
        GLog.d(TAG, "[covert] info = " + (info.toString()) + "; time = " + GLog.getTime(start));
        return info;
    }

    private String getPreViewBase64(MediaItem item) {
        if (item != null) {
            Bitmap thumbnail = requestBitmap(item);
            if (thumbnail == null) {
                return null;
            }
            int sampleSize = ImageUtils.adviseSampleSize(thumbnail.getWidth(), thumbnail.getHeight(), MAX_WIDTH, MAX_HEIGHT);
            if (sampleSize == 0) {
                return null;
            }
            byte[] bytes = BitmapUtils.compressToBytes(thumbnail, Bitmap.CompressFormat.JPEG, HIGH_QUALITY / sampleSize);
            return Base64.encodeToString(bytes, Base64.DEFAULT);
        }
        return null;
    }

    private Bitmap requestBitmap(MediaItem mediaItem) {
        IResourcingAbility resourcingAbility = ((GalleryApplication) mContext.getApplicationContext())
                .getAppAbility(IResourcingAbility.class);
        ResourceKey resourceKey = ResourceKeyFactory.createResourceKey(mediaItem);
        ResourceGetOptions options = new ResourceGetOptions(
                ThumbnailSizeUtils.getMicroThumbnailKey(MAX_WIDTH),
                -1,
                -1,
                CropParams.centerRectCrop(),
                CacheOperation.ReadWriteAllCache.INSTANCE,
                SourceOperation.ReadAll.INSTANCE
        );
        if ((resourcingAbility != null) && (resourceKey != null)) {
            try {
                ImageResult<Bitmap> result = resourcingAbility.requestBitmap(resourceKey, options, null, null);
                if (result != null) {
                    return result.getResult();
                }
            } finally {
                resourcingAbility.close();
            }
        }
        return null;
    }
}
