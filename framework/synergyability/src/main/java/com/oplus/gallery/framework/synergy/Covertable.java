/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * VENDOR_EDIT
 * * File:  - Covertable.java
 * * Description:
 * * Version: 1.0
 * * Date : 2020/06/04
 * * Author: huanglinpeng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  huanglinpeng@Apps.Gallery3D  2020/06/04        build this module
 ****************************************************************/
package com.oplus.gallery.framework.synergy;

import com.oplus.gallery.business_lib.model.data.base.MediaObject;

public interface Covertable<T, E extends MediaObject> {
    T covert(E object, boolean preview);
}
