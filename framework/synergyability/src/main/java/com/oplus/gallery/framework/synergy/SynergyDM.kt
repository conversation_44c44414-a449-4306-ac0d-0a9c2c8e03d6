/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * VENDOR_EDIT
 * * File:  - SynergyDM.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2020/11/17
 * * Author: huanglinpeng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  huanglinpeng@Apps.Gallery3D  2020/06/04        build this module
 ****************************************************************/

package com.oplus.gallery.framework.synergy

import android.view.MotionEvent
import com.oplus.gallery.business_lib.api.ISynergyDM
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.router_lib.annotations.Component
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Component(interfaceName = "com.oplus.gallery.business_lib.api.ISynergyDM")
class SynergyDM : ISynergyDM {
    override fun isSynergyEnable(): Boolean =
        SynergyHelper.getInstance().isSynergyEnable

    override fun dragFileOnRemote(paths: Set<Path>) {
        AppScope.launch(Dispatchers.SINGLE_UN_BUSY) {
            SynergyHelper.getInstance().dragFileOnRemote(paths)
        }
    }

    override fun isTabletSynergyEnable(): Boolean =
        SynergyTouchHelper.isSynergyEnable

    override fun isTouchEventFromTablet(event: MotionEvent): Boolean =
        SynergyTouchHelper.isTouchEventFromTablet(event)

    override fun isTouchEventFromSynergyDevice(event: MotionEvent): Boolean =
        SynergyTouchHelper.isTouchEventFromSynergyDevice(event)
}