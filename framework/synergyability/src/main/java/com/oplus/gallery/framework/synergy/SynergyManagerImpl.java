/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * VENDOR_EDIT
 * * File:  - SynergyManagerImpl.java
 * * Description:
 * * Version: 1.0
 * * Date : 2020/06/04
 * * Author: huanglinpeng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  huanglinpeng@Apps.Gallery3D  2020/06/04        build this module
 ****************************************************************/
package com.oplus.gallery.framework.synergy;

import android.content.Context;

import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.synergy.api.FileInfo;
import com.oplus.synergy.sdk.SynergyClientManager;
import com.oplus.synergy.sdk.bean.SynergyFileInfo;
import com.oplus.synergy.sdk.listener.SynergyClientManagerDelegate;
import com.oplus.synergy.sdk.listener.SynergyFileTransferCallback;

import java.util.List;

class SynergyManagerImpl implements SynergyManager {

    private static final String TAG = "SynergyManagerImpl";
    public static final int STATE_UNKNOWN = -1;
    public static final int STATE_CONNECTED = 1;
    public static final int STATE_DICCONNECTED = 2;
    private SynergyClientManager mSynergyClientManager = null;

    public SynergyManagerImpl(Context context) {
        mSynergyClientManager = new SynergyClientManager(context);
    }

    @Override
    public void open() {
        try {
            GLog.d(TAG, "[open]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.open();
            }
        } catch (Exception e) {
            GLog.e(TAG, "[open]  Exception : ", e);
        }
    }

    @Override
    public void close() {
        try {
            GLog.d(TAG, "[close]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.close();
            }
        } catch (Exception e) {
            GLog.e(TAG, "[close]  Exception : ", e);
        }
    }

    @Override
    public int getDisplayState() {
        try {
            GLog.d(TAG, "[getDisplayState]");
            if (mSynergyClientManager != null) {
                return mSynergyClientManager.getDisplayState();
            }
        } catch (Exception e) {
            GLog.e(TAG, "[getDisplayState]  Exception : ", e);
        }
        return STATE_UNKNOWN;
    }

    @Override
    public void openFileOnRemote(SynergyFileInfo fileInfo) {
        try {
            GLog.d(TAG, "[openFileOnRemote]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.openFileOnRemote(fileInfo);
            }
        } catch (Exception e) {
            GLog.e(TAG, "[openFileOnRemote]  Exception : ", e);
        }
    }

    @Override
    public void dragFileOnRemote(List<FileInfo> filesList) {
        try {
            GLog.d(TAG, "[dragFileOnRemote]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.dragFileOnRemote(filesList);
            }
        } catch (Exception e) {
            GLog.e(TAG, "[dragFileOnRemote]  Exception : ", e);
        }
    }

    @Override
    public void registerClientManagerDelegate(SynergyClientManagerDelegate delegate) {
        try {
            GLog.d(TAG, "[registerClientManagerDelegate]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.registerClientManagerDelegate(delegate);
            }
        } catch (Exception e) {
            GLog.e(TAG, "[registerClientManagerDelegate]  Exception : ", e);
        }
    }

    @Override
    public void registerFileTransferCallback(SynergyFileTransferCallback callback) {
        try {
            GLog.d(TAG, "[registerFileTransferCallback]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.registerFileTransferCallback(callback);
            }
        } catch (Exception e) {
            GLog.e(TAG, "[registerFileTransferCallback]  Exception : ", e);
        }
    }

    @Override
    public void unRegisterClientManagerDelegate() {
        try {
            GLog.d(TAG, "[unRegisterClientManagerDelegate]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.unRegisterClientManagerDelegate();
            }
        } catch (Exception e) {
            GLog.e(TAG, "[unRegisterClientManagerDelegate]  Exception : ", e);
        }
    }

    @Override
    public void unRegisterFileTransferCallback() {
        try {
            GLog.d(TAG, "[unRegisterFileTransferCallback]");
            if (mSynergyClientManager != null) {
                mSynergyClientManager.unregisterFileTransferCallback(null);
            }
        } catch (Exception e) {
            GLog.e(TAG, "[unRegisterFileTransferCallback]  Exception : ", e);
        }
    }
}
