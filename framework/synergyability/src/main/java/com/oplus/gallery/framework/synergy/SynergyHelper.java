/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * VENDOR_EDIT
 * * File:  - SynergyHelper.java
 * * Description:
 * * Version: 1.0
 * * Date : 2020/06/04
 * * Author: huanglinpeng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  huanglinpeng@Apps.Gallery3D  2020/06/04        build this module
 ****************************************************************/
package com.oplus.gallery.framework.synergy;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;

import androidx.annotation.WorkerThread;

import com.oplus.gallery.business_lib.model.data.base.MediaObject;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager;
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver;
import com.oplus.gallery.foundation.util.thread.ThreadUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.synergy.sdk.bean.DisplayState;
import com.oplus.synergy.sdk.bean.SynergyFileInfo;
import com.oplus.synergy.sdk.bean.SynergyFilePosition;
import com.oplus.synergy.sdk.listener.SynergyClientManagerDelegate;
import com.oplus.synergy.sdk.listener.SynergyFileTransferCallback;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.WeakHashMap;

public class SynergyHelper implements SynergyClientManagerDelegate, SynergyFileTransferCallback {

    private static final String TAG = "SynergyHelper";
    private static final String KEY_PC_CONNECT_SUPPORT = "pc_connect_support";
    private static final String KEY_PC_CONNECT_STATE = "pc_connect_state";
    private static final int STATE_PC_CONNECT_SUPPORT = 1;
    private static final int STATE_CONNECTED = 1;
    private static final int STATE_CONNECTED_CASTING = 2;
    private static final int INVALID_VALUE = -1;
    private static final SynergyHelper sInstance = new SynergyHelper();
    private final SynergyManager mSynergyManager;
    private final Set<SynergyGetFilesListener> mGetFilesListeners =
        Collections.synchronizedSet(Collections.newSetFromMap(new WeakHashMap<>()));
    private final String mSynergyPackageName = AppBrandConstants.Package.getPACKAGE_NAME_SYNERGY();
    private final HashSet<Uri> mGrantPermissionSet = new HashSet<>();
    private SynergyConnectListener mSynergyConnectListener = null;
    private boolean mSynergyOpen = false;
    private HandlerContentObserver mPCConnectStateObserver;

    public static SynergyHelper getInstance() {
        return sInstance;
    }

    private SynergyHelper() {
        mSynergyManager = new SynergyManagerImpl(ContextGetter.context);
    }

    public void init() {
        int supportState = Settings.Secure.getInt(ContextGetter.context.getContentResolver(), KEY_PC_CONNECT_SUPPORT, INVALID_VALUE);
        if (supportState == STATE_PC_CONNECT_SUPPORT) {
            mPCConnectStateObserver = new HandlerContentObserver(null) {
                @Override
                public void onChange(boolean selfChange, Uri uri) {
                    GLog.d(TAG, "onChange");
                    int connectState = Settings.Secure.getInt(ContextGetter.context.getContentResolver(), KEY_PC_CONNECT_STATE, INVALID_VALUE);
                    if ((connectState == STATE_CONNECTED) || (connectState == STATE_CONNECTED_CASTING)) {
                        openSynergy();
                    } else {
                        closeSynergy();
                    }
                }
            };
            int connectState = Settings.Secure.getInt(ContextGetter.context.getContentResolver(), KEY_PC_CONNECT_STATE, INVALID_VALUE);
            GLog.d(TAG, "init, connectState = " + connectState);
            if ((connectState == STATE_CONNECTED) || (connectState == STATE_CONNECTED_CASTING)) {
                openSynergy();
            }
            registerPCConnectStateObserver();
        } else {
            GLog.d(TAG, "init, not support pc connect");
        }
    }

    private void openSynergy() {
        mSynergyManager.registerClientManagerDelegate(this);
        mSynergyManager.open();
        mSynergyManager.registerFileTransferCallback(this);
    }

    private void closeSynergy() {
        mSynergyManager.close();
        mSynergyManager.unRegisterClientManagerDelegate();
        mSynergyManager.unRegisterFileTransferCallback();
        recycleFilePermission();
    }

    private void recycleFilePermission() {
        synchronized (mGrantPermissionSet) {
            GLog.d(TAG, "recycleUriPermission, recycle permission, size = " + mGrantPermissionSet.size());
            Context context = ContextGetter.context;
            for (Uri uri : mGrantPermissionSet) {
                context.revokeUriPermission(mSynergyPackageName, uri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
            }
            mGrantPermissionSet.clear();
        }
    }

    public int getDisplayState() {
        return mSynergyManager.getDisplayState();
    }

    private boolean checkFileInfo(SynergyFileInfo info) {
        return false;
    }

    /**
     * 判断相册是否与Synergy服务连接，若已连接，则可触发拖拽
     */
    public boolean isSynergyEnable() {
        return mSynergyOpen;
    }

    public void setSynergyConnectListener(SynergyConnectListener listener) {
        mSynergyConnectListener = listener;
    }

    /**
     * 时间轴、图集详情页、大图页注册拖拽监听，发生拖拽时会回调
     */
    public void registerGetFilesListener(SynergyGetFilesListener listener) {
        mGetFilesListeners.add(listener);
    }

    /**
     * 时间轴、图集详情页、大图页注销拖拽监听
     */
    public void unregisterGetFilesListener(SynergyGetFilesListener listener) {
        mGetFilesListeners.remove(listener);
    }

    private void registerPCConnectStateObserver() {
        ContentObserverManager.registerContentObserver(ContextGetter.context, Settings.Secure.getUriFor(KEY_PC_CONNECT_STATE),
            true, mPCConnectStateObserver);
    }

    // SynergyClientManagerDelegate start
    @Override
    public void onOpen() {
        GLog.d(TAG, "[onOpen]");
        mSynergyOpen = true;
    }

    @Override
    public void onClose() {
        GLog.d(TAG, "[onClose]");
        mSynergyOpen = false;
    }
    // SynergyClientManagerDelegate end

    // SynergyFileTransferCallback start
    @Override
    public void onDisplayStateChange(DisplayState displayState) {
        GLog.d(TAG, "[onDisplayStateChange] displayState = " + displayState);
        if ((displayState == null) || (mSynergyConnectListener == null)) {
            return;
        }
        if (displayState == DisplayState.ENUM_DISPLAY_CONNECTED) {
            mSynergyConnectListener.onSynergyConnect();
        } else {
            mSynergyConnectListener.onSynergyDisConnect();
        }
    }

    @Override
    public boolean onFileOpenSaved(String currentPath, String targetPath) {
        GLog.d(TAG, "[onFileOpenSaved] currentPath=" + currentPath + ", targetPath=" + targetPath);
        return false;
    }

    @Override
    public void onFileOpen(SynergyFileInfo synergyFileInfo) {
        GLog.d(TAG, "[onFileOpen] displayState = " + synergyFileInfo);
    }

    @Override
    public void onFileOpenSuccess(SynergyFileInfo synergyFileInfo) {
        GLog.d(TAG, "[onFileOpenSuccess] displayState = " + synergyFileInfo);
    }

    @Override
    public void onFileOpenFail(SynergyFileInfo synergyFileInfo) {
        GLog.d(TAG, "[onFileOpenFail] displayState = " + synergyFileInfo);
    }

    @Override
    public List<SynergyFileInfo> onGetFileInfo(SynergyFilePosition position) {
        GLog.d(TAG, "[onGetFileInfo] : " + position + "; isMainThread == " + (ThreadUtils.isMainThread()));
        long start = System.currentTimeMillis();

        SynergyGetFilesListener listener = null;
        SynergyGetFilesListener[] localFilesListeners = mGetFilesListeners.toArray(new SynergyGetFilesListener[0]);
        int length = localFilesListeners.length;
        if (length > 0) {
            listener = localFilesListeners[length - 1];
        }

        if ((position == null) || (listener == null)) {
            GLog.d(TAG, "[onGetFileInfo] (position == null) || (mGetFileListener == null) ");
            return new ArrayList<>();
        }

        int x = position.getPositionX();
        int y = position.getPositionY();

        Set<Path> pathSet = listener.getSelectedItemPaths(x, y);
        if (pathSet == null) {
            return new ArrayList<>();
        }
        List<SynergyFileInfo> synergyFileInfos = grantPermissionAndConvert(pathSet, new SynergyFileInfoCovert(ContextGetter.context));
        GLog.d(TAG, "[onGetFileInfo] "
            + " x     = " + x
            + ";y     = " + y
            + ";size  = " + synergyFileInfos.size()
            + ";spend = " + GLog.getTime(start));
        return synergyFileInfos;
    }
    //SynergyFileTransferCallback end

    @WorkerThread
    public void dragFileOnRemote(Set<Path> paths) {
        GLog.d(TAG, "dragFileOnRemote. size=" + paths.size());
        mSynergyManager.dragFileOnRemote(grantPermissionAndConvert(paths, new FileInfoCovert(ContextGetter.context)));
    }

    private <T> List<T> grantPermissionAndConvert(Set<Path> paths, Covertable<T, MediaItem> converter) {
        List<T> files = new ArrayList<>();
        Iterator<Path> iterator = paths.iterator();
        Context context = ContextGetter.context;
        for (int i = 0; iterator.hasNext(); i++) {
            Path path = iterator.next();
            MediaObject mediaObject = DataManager.getMediaObject(path);
            if (mediaObject != null) {
                Uri fileUri = mediaObject.getContentUri();
                files.add(converter.covert((MediaItem) mediaObject, (i == 0)));
                synchronized (mGrantPermissionSet) {
                    context.grantUriPermission(mSynergyPackageName, fileUri, Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    mGrantPermissionSet.add(fileUri);
                }
            }
        }
        return files;
    }
}