/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * VENDOR_EDIT
 * * File:  - ISynergyManager.java
 * * Description:
 * * Version: 1.0
 * * Date : 2020/06/04
 * * Author: huanglinpeng@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  huanglinpeng@Apps.Gallery3D  2020/06/04        build this module
 ****************************************************************/
package com.oplus.gallery.framework.synergy;

import com.oplus.synergy.api.FileInfo;
import com.oplus.synergy.sdk.bean.SynergyFileInfo;
import com.oplus.synergy.sdk.listener.SynergyClientManagerDelegate;
import com.oplus.synergy.sdk.listener.SynergyFileTransferCallback;

import java.util.List;

interface SynergyManager {
    void open();
    void close();
    int getDisplayState();
    void openFileOnRemote(SynergyFileInfo fileInfo);
    void dragFileOnRemote(List<FileInfo> filesList);
    void registerClientManagerDelegate(SynergyClientManagerDelegate delegate);
    void registerFileTransferCallback(SynergyFileTransferCallback callback);
    void unRegisterClientManagerDelegate();
    void unRegisterFileTransferCallback();
}
