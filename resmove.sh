#!/bin/bash
if [[ $1 = "--help" ]] || [[ $1 = "-h" ]]
then
  echo ""
  echo -e "\033[31m----------------资源迁移和拆分自动化脚本使用帮助----------------\033[0m"
  echo ""
  echo -e "\033[1m获取帮助:./resmove.sh -h\033[0m"
  echo ""
  echo "1.chmod u+x resmove.sh"
  echo "2.source resmove.sh"
  echo "3.将要迁移的资源写入一个配置文件（命名自由指定，比如config.txt）"
  echo "4.执行迁移命令"
  echo -e "\033[1meg.多行资源类型的迁移\033[0m：move_multiline_resbatch 要迁移的资源所属的原目录(到res这一级) 要迁往的资源目录（到res这一级）配置文件名(config.txt)"
  echo "eg. move_multiline_resbatch photoeditor_page/src/main/res photo_page/src/main/res config.txt"
  echo "config.txt内容如下:"
  echo ""
  echo -e "\033[32mpicture3d_cshot_select_confirm:plurals"
  echo "picture3d_title_has_select:plurals"
  echo -e "picture3d_editor_array_screen_shot_state_text_array:array\033[0m]"
  echo ""
  echo -e "\033[1meg.单行资源类型的迁移\033[0m：move_singleline_resbatch 要迁移的资源所属的原目录(到res这一级) 要迁往的资源目录（到res这一级）配置文件名(config.txt)"
  echo "eg. move_singleline_resbatch photoeditor_page/src/main/res photo_page/src/main/res config.txt"
  echo "config.txt内容如下:"
  echo ""
  echo -e "\033[32mpicture3d_cshot_select_confirm:string"
  echo "picture3d_title_has_select:dimen"
  echo -e "picture3d_switch_to_debug_env:color\033[0m"
  echo ""
  echo -e "\033[31m----------------其他使用----------------------------------------\033[0m"
  echo ""
  echo "eg. 迁移单个string资源: move_single_singleline_res..."
  echo "eg. 迁移单个的多行文本资源: move_multilineres..."
  echo "eg. 迁移文件资源命令: moveres pictureeditorpage/src/main/res/ photo_page/src/main/res/ config.txt"
fi

# 迁移单个单行资源
# $1：参数1 源目录
# $2: 参数2 目标目录
# $3: 参数3 要迁移的string资源id
move_single_singleline_res(){
  listSrc=$(ls $1)
  echo $listSrc

  valueDirs=
  for dir in $listSrc
  do
    if [[ $dir =~ "value" ]]
    then
      echo 'dir:'${dir}
      valueDirs="$valueDirs $dir"
    fi
  done
  echo $valueDirs

  # find the resource content line
  for valdir in $valueDirs
  do
    valueFiles=$(ls $1/$valdir)
    for vfile in $valueFiles
    do
      srcPath="$1/$valdir/$vfile"
      echo "srcPath:$srcPath"
      if [ ! -f "$srcPath" ];then
        echo "srcPath file not exist"
        continue
      fi
      # get content (only single line content) eg.<string> <dimen>
      # pattern="sed -n -e '/<$4 name=\"$3\"/,/<\/$4>/p' $srcPath"
      # pattern="name=\""$3"\""
      pattern="grep '<$4 name=\"$3\"' $srcPath"
      eval $pattern >> content.txt
      echo "pattern:$pattern"
      # can't match pattern
      # content=`grep "$pattern" $srcPath | cat`
      content=`cat content.txt`
      echo "content:$content"
      if [ -z $content ];then
        continue
      fi
      # delete content from src file
      # sed -i '/'"${pattern}"'/d' $srcPath
      delpattern="sed -i '/<$4 name=\"$3\"/d' $srcPath"
      echo delpattern=$delpattern
      eval $delpattern
      # write content to dst file
      dstDir="$2/$valdir"
      echo "dstDir:$dstDir"
      if [ ! -d "$dstDir" ];then
        mkdir "$dstDir"
      fi
      dstPath="$2/$valdir/$vfile"
      echo "dstPath:"$dstPath
      if [ ! -f $dstPath ];then
        touch $dstPath
        echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" >> $dstPath
        echo "<resources>" >> $dstPath
        echo "</resources>" >> $dstPath
      fi
      echo "============================sed content:"$content
      sed -i '/<\/resources>/i'"\ ${content}"'' $dstPath
      rm content.txt
    done    
  done
}

# 批量迁移资源：支持单行文本的资源，比如string/dimen/color等
# 配置文件格式:id名:资源标签名
# $1：参数1 源目录
# $2: 参数2 目标目录
# $3: 参数3 要迁移的资源配置文件。格式是：资源id:资源类型，比如:
# -resource1:color
# -resource2:string
# -resource3:dimen
# 配置文件举例：
# picture3d_cshot_select_confirm:string
# picture3d_title_has_select:dimen
# picture3d_split_menu_text_color:color
# 命令执行举例：move_singleline_resbatch photoeditor_page/src/main/res photo_page/src/main/res config.txt
move_singleline_resbatch(){
  file=$3
  idlist=`cat $file`
  for id in $idlist
  do
    echo $id
    OLD_IFS="$IFS" 
    IFS=":"
    array=($id)
    IFS="$OLD_IFS"
    echo "${array[0]}"
    echo "${array[1]}"
    move_single_singleline_res $1 $2 ${array[0]} ${array[1]}
  done
}

# 迁移多行类型的资源
# $1：参数1 源目录
# $2: 参数2 目标目录
# $3: 参数3 要迁移的资源id
# $4: 参数4 要迁移的资源类型，比如array/string/plurals
move_multiline_res(){
  listSrc=$(ls $1)
  echo $listSrc

  valueDirs=
  for dir in $listSrc
  do
    if [[ $dir =~ "value" ]]
    then
      echo 'dir:'${dir}
      valueDirs="$valueDirs $dir"
    fi
  done
  echo $valueDirs

  # find the resource content line
  for valdir in $valueDirs
  do
    valueFiles=$(ls $1/$valdir)
    for vfile in $valueFiles
    do
      srcPath="$1/$valdir/$vfile"
      echo "srcPath:$srcPath"
      if [ ! -f "$srcPath" ];then
        echo "srcPath file not exist"
        continue
      fi
      #xml标签 多行文本匹配
      pattern="sed -n -e '/<$4 name=\"$3\"/,/<\/$4>/p' $srcPath"
      echo pattern=$pattern
      eval $pattern > content.txt
      #check content
      checkcontent=`cat content.txt`
      echo "============================checkcontent:$checkcontent"
      if [ -z $checkcontent ];then
        echo "-----------------not match content"
        continue
      fi
      # delete content from src file
      delpattern="sed -i -e '/<$4 name=\"$3\"/,/<\/$4>/d' $srcPath"
      echo delpattern=$delpattern
      eval $delpattern
      # write content to dst file
      dstDir="$2/$valdir"
      if [ ! -d "$dstDir" ];then
        mkdir "$dstDir"
      fi
      dstPath="$2/$valdir/$vfile"
      echo "dstPath:"$dstPath
      if [ ! -f $dstPath ];then
        touch $dstPath
        echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" >> $dstPath
        echo "<resources>" >> $dstPath
        echo "</resources>" >> $dstPath
      fi
      #必须一行行的处理,for line将以空格分割，而不是换行符
      #for line in `cat limao.txt` 
      cat content.txt | while read -r line
      do
         echo "line:$line"
         echo "dstPath:"$dstPath
         if [[ $line =~ "<item" ]];then
           echo "-------------<item line--------------"
           sed -i '/<\/resources>/i'"\        ${line}"'' $dstPath
         else
           echo "-------------non <item line----------"
           sed -i '/<\/resources>/i'"\    ${line}"'' $dstPath
         fi
      done
      rm content.txt
    done
  done
}

# 批量迁移资源：支持多行文本的资源，比如包含多个item的array或是plurals等资源
# 配置文件格式:id名:占据的行数
# $1：参数1 源目录
# $2: 参数2 目标目录
# $3: 参数3 要迁移的资源配置文件。格式是：资源id:资源类型，比如:
# -resource1:array
# -resource2:plurals
# 配置文件举例：
# picture3d_cshot_select_confirm:plurals
# picture3d_title_has_select:plurals
# picture3d_editor_array_screen_shot_state_text_array:array
# 命令执行举例：mover_multiline_resbatch photoeditor_page/src/main/res photo_page/src/main/res config.txt
move_multiline_resbatch(){
  file=$3
  idlist=`cat $file`
  for id in $idlist
  do
    echo $id
    OLD_IFS="$IFS" 
    IFS=":"
    array=($id)
    IFS="$OLD_IFS"
    echo "${array[0]}"
    echo "${array[1]}"
    move_multiline_res $1 $2 ${array[0]} ${array[1]}
  done
}

# 移动文件的脚本
movesinglefile() {
  echo "$1/$3"
  echo "$2"
  mv $1/$3 $2/
}

# 批量移动资源文件;目前只能批量移动同一个目录的资源文件
# 文件内容配置形式如下:
# ic_launcher.png
# picture_selector.xml
# 命令使用:moveres pictureeditorpage/src/main/res/ framework/basebiz/src/main/res-biz/ config.txt
# 命令使用:moveres pictureeditorpage/src/main/res/ photo_page/src/main/res/ config.txt
movefilesbatch() {
  file=$3
  filelist=`cat $file`
  for resfile in $filelist
  do
    echo "resfile:$resfile"
    movesinglefile $1 $2 $resfile
  done
}



