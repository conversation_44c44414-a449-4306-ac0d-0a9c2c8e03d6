<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/search_all_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/search_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rl_search_top_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_toolbar_height"
            android:orientation="vertical">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/toolbar_min_height"
                app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

            <View
                android:id="@+id/divider_line"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_gl_action_bar_bottom_divider_height"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="0dp"
                android:layout_marginEnd="0dp"
                android:alpha="100"
                android:background="@color/search_under_diver_line_color"
                android:visibility="gone"
                android:forceDarkAllowed="false" />
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/search_content_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:id="@+id/fragment_search_results"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <FrameLayout
                android:id="@+id/fragment_search_recommend"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </FrameLayout>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/base_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</FrameLayout>


