<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/layout_search_guide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/search_no_result_card_margin_top"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/search_card_padding_horizontal"
        android:visibility="gone">

        <TextView
            style="@style/ItemTextMediumFontStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/search_no_result_guide"
            android:textColor="@color/search_no_result_guide_text_color"
            android:textSize="@dimen/search_no_result_guide_text_size" />

        <LinearLayout
            android:id="@+id/layout_guide_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/search_card_guide_padding_top"
            android:orientation="horizontal" />
    </LinearLayout>
</FrameLayout>
