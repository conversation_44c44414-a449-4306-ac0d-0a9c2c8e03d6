/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: -SearchActivity.java
 ** Description:  A common searching page for Gallery
 ** Version: 1.0
 ** Date : 2017-02-14 16:05:28
 ** Author: shu<PERSON>an@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 ** shujian@Apps.Gallery3D       2017/02/14   1.0     build this module
 ****************************************************************/
package com.oplus.gallery.searchpage;

import static com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_ALBUM_TITLE;
import static com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_FROM_SEARCH_ACTIVITY;
import static com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_HIDE_INTERNAL_TOOLBAR;
import static com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_MEDIA_PATH;
import static com.oplus.gallery.basebiz.constants.IntentConstant.AlbumConstant.KEY_NEED_SCROLL_TO_BOTTOM;
import static com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_GALLERY_ID_LIST;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.ALBUM_FRAGMENT;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.MEMORIES_DETAIL_FRAGMENT;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.PERSON_PET_DETAIL_FRAGMENT;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.RECYCLE_FRAGMENT;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SEARCH_ACTIVITY;
import static com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptorKt.SCENE_NAME_AI_CAPTION;
import static com.oplus.gallery.basebiz.uikit.activity.PermissionsGuideActivity.REQUEST_CODE_PERMISSION_SETTING;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.BACKGROUND_COLLECT_COMPLETE_URI;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.BACKGROUND_COLLECT_URI;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.LOCALE_CHANGE_RECOMMEND_UPDATING_URI;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SD_MOUNT_RECOMMEND_UPDATING_URI;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SEARCH_WHITE_LIST_DOWNLOAD_URI;
import static com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment.KEY_ALBUM_MODEL_BUNDLE;
import static com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value.CLICK_ALL;
import static com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value.SEARCH_MULTIMODAL;
import static com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING;
import static com.oplus.gallery.framework.abilities.data.DataRepository.KEY_IS_POSITIVE_ORDER;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_UNIT_CONFIG_AVAILABLE;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Scanning.ImageCaption.CAPTION_MODEL_USER_CANCEL_DOWNLOAD;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.AI_CAPTION_PLUGIN_DOWNLOAD_STATE;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Search.SearchAbility.IS_FIRST_ANDES_SEARCH;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_ALBUM;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_ALL_PHOTO;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_CHILD_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_DATETIME;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_GUIDE_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_LOCATION;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_MASK_TYPE;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_MEMORIES_ALBUM;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_MULTI_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_OCR;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_PERSON;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_RECENT_TIME;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_SPECIAL_LABEL;
import static com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument.SPECIAL_TYPE_SEARCH;
import static com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument.SPECIAL_TYPE_SEARCH_FULL_LINK;
import static com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_MODE_INCR;
import static com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_TYPE_ON_NECESSARY_PERMISSION_OK;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.SharedElementCallback;
import android.content.ContentValues;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.transition.Transition;
import android.transition.TransitionInflater;
import android.transition.TransitionSet;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.core.graphics.Insets;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.LifecycleKt;
import androidx.recyclerview.widget.RecyclerView;

import com.coui.appcompat.animation.COUIMoveEaseInterpolator;
import com.coui.appcompat.theme.COUIThemeOverlay;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.oplus.aiunit.core.data.DownloadType;
import com.oplus.aiunit.core.data.SceneData;
import com.oplus.aiunit.toolkits.AISettings;
import com.oplus.aiunit.toolkits.callback.SettingsCallback;
import com.oplus.gallery.basebiz.constants.IntentConstant;
import com.oplus.gallery.basebiz.event.EnterGalleryEvent;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.basebiz.helper.FragmentHelperKt;
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.basebiz.permission.helper.CTAHelper;
import com.oplus.gallery.basebiz.uikit.activity.BasePermissionsActivity;
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment;
import com.oplus.gallery.basebiz.util.AlbumPasswordSettingsUtil;
import com.oplus.gallery.bus_lib.Bus;
import com.oplus.gallery.bus_lib.annotations.FinderType;
import com.oplus.gallery.bus_lib.annotations.Subscribe;
import com.oplus.gallery.bus_lib.annotations.ThreadType;
import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.helper.SearchHelper;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Person;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil;
import com.oplus.gallery.business_lib.model.data.location.utils.GeoCacheService;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.business_lib.model.data.picture.set.ExtOrderIdsPictureAlbum;
import com.oplus.gallery.business_lib.model.data.search.item.ItemInfo;
import com.oplus.gallery.business_lib.model.data.search.set.SearchMultiModalResultAlbum;
import com.oplus.gallery.business_lib.model.data.search.set.SearchResultAlbum;
import com.oplus.gallery.business_lib.util.TimelineUtils;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant;
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant;
import com.oplus.gallery.foundation.tracing.helper.LaunchExitPopupTrackHelper;
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper;
import com.oplus.gallery.foundation.ui.systembar.SystemBarControllerKt;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.foundation.util.permission.RuntimePermissionUtils;
import com.oplus.gallery.foundation.util.systemcore.ActivityManagerUtils;
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager;
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver;
import com.oplus.gallery.foundation.util.systemcore.IntentUtils;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.foundation.util.systemcore.StaticHandler;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.framework.abilities.config.IConfigSetterAbility;
import com.oplus.gallery.framework.abilities.config.ISettingsAbility;
import com.oplus.gallery.framework.abilities.config.keys.ConfigID;
import com.oplus.gallery.framework.abilities.download.IAIUnitDownloadAbility;
import com.oplus.gallery.framework.abilities.scan.modeldownload.AiCaptionPluginSupportCheckStrategy;
import com.oplus.gallery.framework.abilities.search.ISearchAbility;
import com.oplus.gallery.framework.abilities.search.caption.ICaptionAbility;
import com.oplus.gallery.framework.abilities.search.embedding.caption.ICaptionEmbeddingAbility;
import com.oplus.gallery.framework.abilities.search.multimodal.IMultiModalAbility;
import com.oplus.gallery.framework.abilities.search.ocrembedding.IOcrEmbeddingAbility;
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.router_lib.annotations.RouterNormal;
import com.oplus.gallery.searchpage.animate.TextResizeTransition;
import com.oplus.gallery.searchpage.animate.TransitionElementCallback;
import com.oplus.gallery.searchpage.animate.ViewGroupResizeTransition;
import com.oplus.gallery.searchpage.bean.SearchResultGuideItemEntry;
import com.oplus.gallery.searchpage.ui.GallerySearchLayout;
import com.oplus.gallery.searchpage.ui.SearchView;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.scheduler.DefaultScheduler;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.session.PrioritySession;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

@RouterNormal(path = SEARCH_ACTIVITY)
public class SearchActivity extends BasePermissionsActivity implements SearchActivityLinker,
        SearchView.OnQueryTextListener,
        GallerySearchLayout.OnCancelButtonClickListener,
        SearchView.OnSearchViewClickListener,
        TextView.OnEditorActionListener,
        SearchRecommendFragment.OnRecommendResultListener,
        View.OnLayoutChangeListener,
        SearchView.OnKeywordsSearchListener,
        TemplateFragment.IToolbarSetter {
    public static final String KEY_FILTER_KEYWORD = "filter-keyword";
    public static final String KEY_FILTER_SELECTION = "filter-selection";
    public static final String KEY_FILTER_SINGLE_JUMP = "filter-single-jump";
    public static final String KEY_SEARCH_KEYWORDS = "search_keywords";

    public static final int RESULT_BLANK = 201;
    public static final int RESULT_CLEAR = 202;
    public static final int RESULT_CANCEL = 203;

    public static final int ENTER_ANIMATE_DURATION = 450;

    private static final String HANDLE_THREAD_NAME = "SearchResultReloadObserver";
    private static final String EMOJI_STRING = "emoji";
    private static final String GET_KEYWORDS = ", getKeywords = ";
    private static final String TAG = "SearchActivity";
    private static final String DEFAULT_TAG = "default_tag";
    private static final int INT_ZERO = 0;

    /**
     * 模型下载状态 - 未下载
     */
    private static final int AIUNIT_MODEL_NOT_DOWNLOAD = 0;

    /**
     * 模型下载状态 - 默认，可能是未查询到
     */
    private static final int AIUNIT_MODEL_DOWNLOAD_STATE_DEFAULT = -1;

    /**
     * 模型下载状态 - 下载失败
     */
    private static final int AIUNIT_MODEL_DOWNLOAD_FAIL = 7;

    /**
     * 模型下载状态 - 下载取消
     */
    private static final int AIUNIT_MODEL_DOWNLOAD_CANCEL = 8;

    /**
     * add for permission
     */
    private static final long USER_ACTION_INTERVAL = 500;
    private static final long RESULT_REFRESH_INTERVAL = 100;
    private static final int MAX_QUERY_LENGTH = 50;
    private static final int[] DEFAULT_ANIM_ARRAY = {com.oplus.gallery.basebiz.R.anim.common_fade_in,
            com.oplus.gallery.basebiz.R.anim.common_fade_out, 0, 0};
    private static final int SEARCH_FRAGMENT_STACK_SIZE = 2;
    private static final int SESSION_MAX_LIMIT_SIZE = 8;

    private final boolean mIsUnfocusedOnSearch = false;
    private final Handler mHandler = new Handler();
    private final PrioritySession mSession = DefaultScheduler.INSTANCE.getScheduler().createPrioritySession(
            SESSION_MAX_LIMIT_SIZE,
            this.getClass().getSimpleName()
    );
    private ViewGroup mSearchContainer = null;
    private RelativeLayout mSearchTopBarLayout = null;
    private View mCustomView = null;
    private View mContentView = null;
    private GallerySearchLayout mSearchViewBar = null;
    private SearchView mSearchView = null;
    private COUIToolbar mToolbar;
    private SearchRecommendFragment mFragmentRecommend = null;
    private SearchResultFragment mFragmentResult = null;
    private SearchResultReloadObserver mSearchResultObserver = null;
    private long mUserActionTime = 0;
    private boolean mIsQueryRecommend = false;
    private boolean mIsIMEHidden = false;
    /**
     * Immediate value, just valid for a moment
     */
    private int mCurrentQueryEntry = SearchResultFragment.ENTRY_TYPE_UNKNOWN;
    private boolean mSingleJumpEnable = false;
    private int mSearchResultStatus;

    /**
     * add for permission
     */
    private boolean mIsDoPermissionOK = false;
    private boolean mIsPermissionOK = false;
    private boolean mShowGuideDetail = false;
    private ViewGroup mSearchViewAnimateRelativeLayout;
    private TextView mInputAnimateTextView;

    private boolean mIsStatusBarTint = false;
    private SearchPermissionHelper mPermissionHelper = new SearchPermissionHelper();
    private HandlerThread mHandlerThread = null;
    private Handler mResultRefreshHandler = null;
    private String mJumpFrom = SearchTrackConstant.Value.USER_FROM_OTHER;

    private boolean mIsSmallScreenWhenInit = false;

    private String mRecycleName = "";

    ActivityResultLauncher<Intent> mResultLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
        if ((result != null && result.getResultCode() == Activity.RESULT_OK) && !TextUtils.isEmpty(mRecycleName)) {
            goToRecycleAlbumPage(mRecycleName);
        }
    });

    private View.OnLayoutChangeListener mAdjustSearchViewListener = (view, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) -> {
        if ((left != oldLeft) || (right != oldRight)) {
            updateSearchView();
        }
    };
    /**
     * SuppressLint("ScreencaptureDetector") 忽略Olint敏感页面保护风险信息检查
     */
    @SuppressLint("ScreencaptureDetector")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Bus.INSTANCE.register(this, FinderType.CLASS_METHOD_FINDER); // 必须在执行父类 onCreate 方法前注册
        super.onCreate(savedInstanceState);
        SearchTrackHelper.viewCallPackage = ActivityManagerUtils.getCallPackage(this);
        //sync data before search
        doDataSync();
        COUIThemeOverlay.getInstance().applyThemeOverlays(this);
        setContentView(R.layout.search_activity_layout);
        Intent intent = getIntent();
        if (intent != null) {
            mIsStatusBarTint = intent.getBooleanExtra(IntentConstant.PicturePageConstant.KEY_STATUSBAR_TINT, false);
            mJumpFrom = intent.getStringExtra(IntentConstant.SearchConstant.KEY_JUMP_SOURCE);
        }
        initializeViews();
        initializeActionBar();
        initKeywords();
        onSearchResultChanged(SHOW_RECOMMEND);
        mSearchResultObserver = new SearchResultReloadObserver(this);
        registerContentObserver();
        setPermissionHelpDialog();

        LaunchExitPopupTrackHelper.trackSendEnterGallerySearch(getActivityRef());

        Transition inTransition = TransitionInflater.from(this).inflateTransition(R.transition.search_enter_fade_in_transition);
        Transition outTransition = TransitionInflater.from(this).inflateTransition(R.transition.search_enter_fade_out_transition);
        getWindow().setEnterTransition(inTransition);
        getWindow().setReturnTransition(outTransition);

        // 分屏或者RTL模式下动画有问题，去掉动画
        boolean needAnimation = !getCurrentAppUiConfig().isInMultiWindow().getCurrent() && !ResourceUtils.isRTL(this);
        if (needAnimation) {
            TransitionSet transitionSet = new TransitionSet();
            transitionSet.addTransition(new TextResizeTransition());
            transitionSet.addTransition(new ViewGroupResizeTransition());
            transitionSet.addTarget(mInputAnimateTextView);
            transitionSet.addTarget(mSearchViewAnimateRelativeLayout);
            transitionSet.setDuration(ENTER_ANIMATE_DURATION);
            transitionSet.setInterpolator(new COUIMoveEaseInterpolator());
            getWindow().setSharedElementEnterTransition(transitionSet);
            getWindow().setSharedElementExitTransition(transitionSet);
            mInputAnimateTextView.setTransitionName(SearchHelper.INPUT_TEXT_TRANSITION);
            mSearchViewAnimateRelativeLayout.setTransitionName(SearchHelper.SEARCH_TRANSITION);
            setEnterSharedElementCallback(new TransitionElementCallback(getIntent(), mInputAnimateTextView, mSearchViewAnimateRelativeLayout));
            mSearchViewBar.startCancelButtonEnterAnimation(false);
        } else {
            mSearchViewBar.startCancelButtonEnterAnimation(true);
        }
        mIsSmallScreenWhenInit = !ScreenUtils.isMiddleAndLargeScreen(this);
    }

    /**
     * 检查 AIUnit 用户协议
     * 因为融合搜索算法相关插件 Clip、TextEmbedding 放在了 AIUnit。
     * 后台扫描下载时，如果用户手动关闭了 AIUnit 的隐私协议，会导致插件下载失败，且在后台不能弹框。
     * 所以需要在进入搜索时，提示用户开启
     */
    private void checkAIUnitPrivacy() {
        if (!isAndesSearchEnable()) {
            return;
        }
        BuildersKt.launch(LifecycleKt.getCoroutineScope(getLifecycle()), Dispatchers.getIO(),
                CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
                    // 检查 AIUnit 用户协议
                    if (!AISettings.isPrivacyAvailable(this)) {
                        AISettings.startPrivacyGuide(this, new SettingsCallback() {
                            @Override
                            public void onSwitch(int status) {
                                GLog.d(TAG, LogFlag.DL, "checkAIUnitPrivacy. onSwitch status: " + status);
                            }

                            @Override
                            public void onUI(int status) {
                                GLog.d(TAG, LogFlag.DL, "checkAIUnitPrivacy. onUI status: " + status);
                            }

                            @Override
                            public void onError(int code) {
                                GLog.e(TAG, LogFlag.DL, "checkAIUnitPrivacy. onError code: " + code);
                            }
                        }, true);
                    } else {
                        initAndesSearchAbility();
                        checkAiCaptionCondition();
                    }
                    return null;
                });
    }

    private void initAndesSearchAbility() {
        GLog.d(TAG, LogFlag.DL, "init multi modal and text embedding ability");
        try (IOcrEmbeddingAbility ocrEmbeddingAbility = ((GalleryApplication) getApplicationContext()).getAppAbility(IOcrEmbeddingAbility.class);
             IMultiModalAbility multiModalAbility = ((GalleryApplication) getApplicationContext()).getAppAbility(IMultiModalAbility.class);
             ICaptionEmbeddingAbility captionEmbeddingAbility =
                     ((GalleryApplication) getApplicationContext()).getAppAbility(ICaptionEmbeddingAbility.class)) {
            if (multiModalAbility != null) {
                multiModalAbility.initSlpSdk();
            }
            if (ocrEmbeddingAbility != null) {
                ocrEmbeddingAbility.init();
            }
            if (captionEmbeddingAbility != null) {
                captionEmbeddingAbility.init();
            }
        }
    }

    /**
     * 检查aiCaption的环境
     * 1. 先检查是否在下载中
     * 2. 检查用户是否手动取消过
     * 3. 检查是否需要下载
     */
    private void checkAiCaptionCondition() {
        ICaptionAbility captionAbility = ((GalleryApplication) getApplicationContext()).getAppAbility(ICaptionAbility.class);
        if (captionAbility != null) {
            boolean isDownloading = captionAbility.isDownloading();
            captionAbility.close();
            GLog.d(TAG, LogFlag.DL, () -> "checkAiCaptionCondition, isDownloading:" + isDownloading);
            if (isDownloading) {
                return;
            }
        }
        IConfigSetterAbility configSetterAbility = ((GalleryApplication) getApplicationContext()).getAppAbility(IConfigSetterAbility.class);
        if (configSetterAbility != null) {
            boolean isUserCancelDownload = Boolean.TRUE.equals(configSetterAbility.getBooleanConfig(CAPTION_MODEL_USER_CANCEL_DOWNLOAD, false));
            configSetterAbility.close();
            if (isUserCancelDownload) {
                GLog.d(TAG, LogFlag.DL, () -> "checkAiCaptionCondition, isUserCancelDownload: " + true);
                return;
            }
        }

        ISettingsAbility settingsAbility = ((GalleryApplication) getApplicationContext()).getAppAbility(ISettingsAbility.class);
        if (settingsAbility != null) {
            settingsAbility.updateBlockingConfig(this, AI_CAPTION_PLUGIN_DOWNLOAD_STATE, IS_AGREE_AI_UNIT_PRIVACY, IS_AI_UNIT_CONFIG_AVAILABLE);
            settingsAbility.close();
        }
        SceneData sceneData = AISettings.getSceneData(this, SCENE_NAME_AI_CAPTION, null);
        GLog.d(TAG, LogFlag.DL, () -> "checkAiCaptionCondition, sceneData: " + sceneData);
        if (sceneData == null) {
            return;
        }
        if (DownloadType.DOWNLOAD_LOST.equals(sceneData.getDownloadType())) { // 文件缺失，必须下载才能用
            queryCaptionDownloadState();
        } else if (DownloadType.DOWNLOAD_NEW.equals(sceneData.getDownloadType())) { // 有新版本，建议下载
            downloadAiCaption();
        }
    }

    /**
     * 查询 Caption 下载状态
     */
    private void queryCaptionDownloadState() {
        IAIUnitDownloadAbility aiUnitDownloadAbility = ((GalleryApplication) getApplicationContext())
                .getAppAbility(IAIUnitDownloadAbility.class);
        if (aiUnitDownloadAbility != null) {
            aiUnitDownloadAbility.queryDownloadState(this, SCENE_NAME_AI_CAPTION, (groupName, state, fullSize, offsetSize) -> {
                GLog.d(TAG, LogFlag.DL, () -> "checkAiCaptionCondition, queryCaptionDownloadState, groupName:" + groupName + ", state:" + state
                        + ", fullSize: " + fullSize + ", offsetSize:" + offsetSize);
                if (state != AIUNIT_MODEL_NOT_DOWNLOAD && state != AIUNIT_MODEL_DOWNLOAD_STATE_DEFAULT
                        && state != AIUNIT_MODEL_DOWNLOAD_FAIL && state != AIUNIT_MODEL_DOWNLOAD_CANCEL) {
                    return null;
                }
                // 下载过部分插件，且下载进度小于100%，则继续下载
                if (offsetSize > 0 && offsetSize < fullSize) {
                    downloadAiCaption();
                } else {
                    // 是否是第一次触发搜索
                    boolean isFirst = ConfigAbilityWrapper.getBoolean(IS_FIRST_ANDES_SEARCH, true);
                    GLog.d(TAG, LogFlag.DL, () -> "checkAiCaptionCondition, isFirst:" + isFirst);
                    if (isFirst) {
                        IConfigSetterAbility configAbility = ((GalleryApplication) getApplicationContext())
                                .getAppAbility(IConfigSetterAbility.class);
                        if (configAbility != null) {
                            configAbility.setBooleanConfig(IS_FIRST_ANDES_SEARCH, false);
                            configAbility.close();
                            downloadAiCaption();
                        }
                    }
                }
                return null;
            });
            try {
                aiUnitDownloadAbility.close();
            } catch (Exception e) {
                GLog.e(TAG, LogFlag.DL, e, () -> "checkAiCaptionCondition, aiUnitDownloadAbility close error");
            }
        }
    }

    /**
     * 下载caption模型，AiUnit下载模型架构内部已经封装好当需要下载时才触发下载流程，此处只需要触发下载即可
     */
    private void downloadAiCaption() {
        AiCaptionPluginSupportCheckStrategy aiCaption = new AiCaptionPluginSupportCheckStrategy();
        aiCaption.setEnableUi(true);
        aiCaption.setUiWord();
        aiCaption.checkSupportability(this, isSupport -> Unit.INSTANCE, notificationAction -> Unit.INSTANCE);
    }

    public void setPermissionHelpDialog() {
        mPermissionHelper.setDialogClickListener(new SearchPermissionHelper.OnDialogClickListener() {
            @Override
            public void onPositiveBtnClicked(boolean isGrantNetworkPermission) {
                if (isGrantNetworkPermission) {
                    grantNetworkPermission();
                }
                mFragmentRecommend.loadRecommendData();
            }

            @Override
            public void onNegativeBtnClicked() {
                mFragmentRecommend.loadRecommendData();
            }
        });
    }

    @Override
    public void onAppUiStateChanged(@NonNull AppUiResponder.AppUiConfig uiConfig) {
        super.onAppUiStateChanged(uiConfig);
        boolean isCurrentSmallScreen = !ScreenUtils.isMiddleAndLargeScreen(this);
        GLog.d(TAG, "onAppUiStateChanged isSmallScreenWhenInit:" + mIsSmallScreenWhenInit
                + "  isCurrentSmallScreen:" + isCurrentSmallScreen);
        // 暂时没有找到更好办法可以去除共享元素的动画，各种移出都试了不行，后续有好方案可以替换
        if (mIsSmallScreenWhenInit != isCurrentSmallScreen) {
            if (mSearchViewAnimateRelativeLayout != null) {
                mSearchViewAnimateRelativeLayout.setTransitionName(SearchHelper.INVALID_TRANSITION);
            }
            if (mInputAnimateTextView != null) {
                mInputAnimateTextView.setTransitionName(SearchHelper.INVALID_TRANSITION);
            }
        }
    }

    private void updateSearchView() {
        if ((mSearchViewBar != null) && (mToolbar != null)) {
            Resources resource = mToolbar.getResources();
            int marginStart = resource.getDimensionPixelSize(R.dimen.search_header_height_margin_start);
            int marginEnd = resource.getDimensionPixelSize(R.dimen.search_header_height_margin_end);
            int height = resource.getDimensionPixelSize(R.dimen.search_header_height);
            COUIToolbar.LayoutParams lp = new COUIToolbar.LayoutParams(mToolbar.getMeasuredWidth() - marginStart - marginEnd, height);
            lp.setMargins(marginStart, 0, marginEnd, 0);
            mSearchViewBar.setLayoutParams(lp);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        GLog.d(TAG, LogFlag.DL, () -> "onNewIntent");
        setIntent(intent);
        initKeywords();
    }

    @Override
    protected void onStart() {
        super.onStart();
        GLog.d(TAG, LogFlag.DL, () -> "onStart");
        if (mResultRefreshHandler != null) {
            mResultRefreshHandler.sendEmptyMessage(ResultStaticHandler.MESSAGE_TYPE_NOTIFY_DIRTY);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        GLog.d(TAG, LogFlag.DL, () -> "onResume");
        if (!mSingleJumpEnable) {
            // 进入onStop再返回，搜索默认页fragment被设置为可见，挡住了搜索结果页，需要重新设置下可见性。bug id:5570595
            onSearchResultChanged(mSearchResultStatus);
        }
        loadRecommendData();
    }

    /**
     * 当前业务逻辑必须使用必要权限才可以的时候，重写它
     */
    @Override
    protected void onNecessaryPermissionOK(boolean firstGranted) {
        GLog.d(TAG, LogFlag.DL, () -> "onNecessaryPermissionOK. firstGranted=" + firstGranted);
        mIsPermissionOK = true;
        if (firstGranted) {
            onStoragePermissionOK();
        } else {
            mPermissionHelper.checkNetworkAndPrivacy(this);
        }
    }

    /**
     * 同意 cta 之后进入相册回调
     * @param event 进入相册事件
     */
    @Subscribe(supportSticky = false, threadType = ThreadType.MAIN)
    public void onEnterGallery(EnterGalleryEvent event) {
        GLog.d(TAG, LogFlag.DL, () -> "onEnterGallery. event=" + event.getFirstEnterGallery());
        checkAIUnitPrivacy();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Bus.INSTANCE.unregister(this);
        if (mResultLauncher != null) {
            mResultLauncher.unregister();
            mResultLauncher = null;
        }
        if ((mToolbar != null)) {
            mToolbar.removeOnLayoutChangeListener(mAdjustSearchViewListener);
        }
        // 虽然可能耗时，但是放子线程会导致内存泄露
        unregisterContentObserver();

        releaseAndesSearchAbility();
    }

    private void releaseAndesSearchAbility() {
        if (!isAndesSearchEnable()) {
            return;
        }
        BuildersKt.launch(AppScope.INSTANCE, Dispatchers.getIO(),
                CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
                    GLog.d(TAG, LogFlag.DL, "release multi modal and text embedding ability");
                    try (IOcrEmbeddingAbility ocrEmbeddingAbility =
                                 ((GalleryApplication) getApplicationContext()).getAppAbility(IOcrEmbeddingAbility.class);
                         IMultiModalAbility multiModalAbility =
                                 ((GalleryApplication) getApplicationContext()).getAppAbility(IMultiModalAbility.class);
                         ICaptionEmbeddingAbility captionEmbeddingAbility =
                                 ((GalleryApplication) getApplicationContext()).getAppAbility(ICaptionEmbeddingAbility.class)) {
                        if (multiModalAbility != null) {
                            multiModalAbility.release();
                        }
                        if (ocrEmbeddingAbility != null) {
                            ocrEmbeddingAbility.release();
                        }
                        if (captionEmbeddingAbility != null) {
                            captionEmbeddingAbility.release();
                        }
                    }
                    return null;
                });
    }

    private void loadRecommendData() {
        if (mIsPermissionOK && (mFragmentRecommend != null) && !mPermissionHelper.isShowPermissionDialog()) {
            mFragmentRecommend.loadRecommendData();
        }
    }

    private void initializeViews() {
        mSearchContainer = findViewById(R.id.search_container);
        mSearchTopBarLayout = findViewById(R.id.rl_search_top_bar);
        mContentView = findViewById(R.id.search_content_view);
        FragmentManager fragmentManager = getSupportFragmentManager();

        mFragmentRecommend = (SearchRecommendFragment) FragmentHelperKt.start(fragmentManager,
                null,
                R.id.fragment_search_recommend,
                SearchRecommendFragment.class,
                DEFAULT_TAG,
                null,
                null,
                this,
                true,
                new int[]{INT_ZERO, INT_ZERO, INT_ZERO, INT_ZERO},
                INT_ZERO
        );
        mFragmentResult = (SearchResultFragment) FragmentHelperKt.start(fragmentManager,
                null,
                R.id.fragment_search_results,
                SearchResultFragment.class,
                DEFAULT_TAG,
                null,
                null,
                this,
                true,
                new int[]{INT_ZERO, INT_ZERO, INT_ZERO, INT_ZERO},
                INT_ZERO
        );
        mFragmentRecommend.setSearchActivityLinker(this);
        mFragmentResult.setSearchActivityLinker(this);
        mContentView.addOnLayoutChangeListener(this);
    }

    private void initializeActionBar() {
        mToolbar = findViewById(R.id.toolbar);
        Menu menu = mToolbar.getMenu();
        if (null != menu) {
            menu.clear();
        }
        setSupportActionBar(mToolbar);
        if (mToolbar != null) {
            mToolbar.addOnLayoutChangeListener(mAdjustSearchViewListener);
        }

        mCustomView = getLayoutInflater().inflate(R.layout.search_bar_layout, new LinearLayout(SearchActivity.this), false);
        mSearchViewBar = mCustomView.findViewById(R.id.search_header);
        mSearchViewAnimateRelativeLayout = mSearchViewBar.findViewById(R.id.search_background_layout);
        mInputAnimateTextView = mSearchViewBar.findViewById(R.id.tv_input_animate);
        mSearchView = mSearchViewBar.getSearchView();
        mSearchViewBar.addOnCancelButtonClickListener(this);

        getSupportActionBar().setDisplayHomeAsUpEnabled(false);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
        mToolbar.setSearchView(mCustomView);
         /*
         COUIToolbar内部有对大屏中屏小屏做间距适配。自己设置的padding无效
         mUseResponsivePadding设为false才会取自己设置的padding
         */
        mToolbar.setUseResponsivePadding(false);
        mSearchView.setQueryHint("");
        mSearchView.setSaveEnabled(true);
        mSearchView.setOnQueryTextListener(this);
        mSearchView.setOnSearchViewClickListener(this);
        mSearchView.setImeOptions(EditorInfo.IME_ACTION_SEARCH | EditorInfo.IME_FLAG_NO_FULLSCREEN);
        mSearchView.getSearchAutoComplete().setOnEditorActionListener(this);
        mSearchView.setOnWordDeleteListener(new SearchActivity.OnWordDeleteListener() {
            @Override
            public void onWordDelete() {
                mFragmentResult.mCurrentSearchResultGuideItemEntry = null;
            }
        });
        TextUtil.addLengthInputFilter(mSearchView.getSearchAutoComplete(), MAX_QUERY_LENGTH,
                getString(com.oplus.gallery.basebiz.R.string.common_input_exceed_limit), true);
        if (mSearchView != null) {
            // 设计要求动画结束再拉起键盘
            mSearchView.postDelayed(new WeakSearchLayoutRunnable(mSearchViewBar), ENTER_ANIMATE_DURATION);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        GLog.d(TAG, "onActivityResult requestCode = " + requestCode
                + ", resultCode = " + resultCode
                + GET_KEYWORDS + SearchKeywordHelper.getKeywords()
                + ", getTextSize = " + mSearchView.getSearchAutoComplete().getTextSize());
        switch (resultCode) {
            case RESULT_BLANK:
                ArrayList<CharSequence> keywords = IntentUtils.getCharSequenceArrayListExtra(data,
                        KEY_SEARCH_KEYWORDS);
                if ((keywords == null) || keywords.isEmpty()) {
                    mSearchView.getSearchAutoComplete().getText().clear();
                    onSearchResultChanged(SHOW_RECOMMEND);
                    mSearchView.cleanSearchView();
                    break;
                }

                SearchKeywordHelper.setKeywords(keywords);
                CharSequence newText = SearchKeywordHelper.getKeywordText(keywords);
                mSearchView.getSearchAutoComplete().setText(newText);
                //build word package
                mSearchView.addWordLayout(keywords);

                onSearchResultChanged(SHOW_RESULT);
                onQueryTextChange(SearchKeywordHelper.getKeywordText(keywords));
                GLog.d(TAG, "onActivityResult.RESULT_BLANK keywords = " + keywords
                        + ", newText = " + newText
                        + ", getKeywordText = " + SearchKeywordHelper.getKeywordText(keywords)
                        + ", getTextSize = " + mSearchView.getSearchAutoComplete().getTextSize());
                break;
            case RESULT_CLEAR:
                onSearchResultChanged(SHOW_RECOMMEND);
                SearchKeywordHelper.clearKeywords();
                mSearchView.cleanSearchView();
                break;
            case RESULT_CANCEL:
                finish();
                break;
            case REQUEST_CODE_PERMISSION_SETTING:
                if (RuntimePermissionUtils.isNecessaryPermissionGranted(this)) {
                    GLog.d(TAG, LogFlag.DL, () -> "onActivityResult, get base permission.");
                    getContentResolver().notifyChange(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, null);
                    GeoCacheService.scheduleJob(SearchActivity.this);
//                    ApiDmManager.getCloudSyncDM().backupOrRecovery();
                    onResume();
                }
                break;
            default:
                break;
        }
    }

    /**
     * @param keywords { "time?input=2019", "location?input=深圳"};
     * @return String
     */
    private String filterKeywords(String[] keywords) {
        if ((keywords == null) || (keywords.length == 0)) {
            return null;
        }
        StringBuilder keywordBuffer = new StringBuilder();
        for (String singleWord : keywords) {
            try {
                Uri singleUri = Uri.parse(singleWord);
                String singleInput = singleUri.getQueryParameter(SearchSuggestionProviderUtil.QUERY_INPUT);
                keywordBuffer.append(singleInput).append(" ");
                GLog.d(TAG, "[filterKeywords] singleWord = " + singleWord);
            } catch (UnsupportedOperationException e) {
                GLog.w(TAG, "[filterKeywords] singleUri = " + Uri.parse(singleWord) + ", Unsupported URI form, " + e);
            }
        }
        return keywordBuffer.toString().trim();
    }

    private void grantNetworkPermission() {
        NetworkPermissionManager.setUseOpenNetwork(true);
        getWorkerSession().submit((Job<Object>) jc -> {
            getContentResolver().notifyChange(CTAHelper.getNetWorkPermissionNotifyUri(), null);
            GeoCacheService.scheduleJob(SearchActivity.this);
            return null;
        });
    }

    @Override
    public boolean onQueryTextSubmit(String query) {
        saveHistoricalSuggestion(query);
        return true;
    }

    @Override
    public void onKeywordsSearch(String query, String[] keywords) {
        if ((keywords == null) || (keywords.length == 0)) {
            mFragmentResult.cleanSearchResult();
            mInputAnimateTextView.setVisibility(View.VISIBLE);
            return;
        }
        mInputAnimateTextView.setVisibility(View.GONE);
        if (mFragmentResult != null) {
            mFragmentResult.updateSuggestionResult(query, keywords, mCurrentQueryEntry, false);
        }
        mCurrentQueryEntry = SearchResultFragment.ENTRY_TYPE_UNKNOWN;
    }

    @Override
    public boolean onQueryTextChange(String changedQuery) {
        String query = changedQuery;
        // 融合搜索Kit端会分词，搜索query不应该由相册内部根据关键字分词，所以融合搜索开启时应该由原query去构建Result
        if (!isAndesSearchEnable()) {
            ArrayList<CharSequence> newKeywords = SearchKeywordHelper.repackKeyword(query);
            query = SearchKeywordHelper.getKeywordText(newKeywords);
        } else {
            query = query.trim();
        }
        if (mShowGuideDetail && query.isEmpty()) { //从引导详情里退出
            mShowGuideDetail = false;
            goBackToRecommendAndRefresh();
        } else {
            String trimString = query.trim();
            if (query.isEmpty()) {
                mFragmentResult.cleanSearchResult();
                mInputAnimateTextView.setVisibility(changedQuery.isEmpty() ? View.VISIBLE : View.GONE);
            } else if (TextUtil.containsEmoji(query) || trimString.isEmpty()) {
                final String statisticsString;
                if (trimString.isEmpty()) {
                    statisticsString = query;
                    mInputAnimateTextView.setVisibility(View.VISIBLE);
                } else {
                    statisticsString = EMOJI_STRING;
                    mInputAnimateTextView.setVisibility(View.GONE);
                }
                SearchTrackHelper.trackSearchInputData(statisticsString,
                        String.valueOf(SearchTrackConstant.Value.NO_RESULT_VALUE), mJumpFrom);
                mFragmentResult.setCurrentQuery(query);
                onSearchResultChanged(NO_RESULT);
            } else {
                mInputAnimateTextView.setVisibility(View.GONE);
                GLog.d(TAG, LogFlag.DL, () -> "onQueryTextChange updateSuggestionResult");
                mFragmentResult.updateSuggestionResult(query, mCurrentQueryEntry, true, false,
                        TYPE_MASK_TYPE, mFragmentResult.mCurrentSearchResultGuideItemEntry, false);
                // 优化体验：进入相册，点搜索框，输入“关键词”，退出搜索页，重新点搜索，保留搜索历史
                if (!TextUtils.isEmpty(query) && mSearchView.isNormalFlat()) {
                    saveHistoricalSuggestion(query);
                }
                startAIAsk(changedQuery);
            }
        }
        mCurrentQueryEntry = SearchResultFragment.ENTRY_TYPE_UNKNOWN;
        return true;
    }

    /**
     * 发起 AI 控件问答
     */
    private void startAIAsk(String changedQuery) {
        mFragmentResult.startAIAsk(changedQuery);
    }

    private void goBackToRecommendAndRefresh() {
        mSearchView.onSearchClicked();
        loadRecommendData();
        mFragmentResult.cleanSearchResult();
        mInputAnimateTextView.setVisibility(View.VISIBLE);
    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onBackPressed() {
        /*
         * 当返回时，由于 mSearchContainer 可能被 startFragment 方法改成隐藏状态，因此需要将它还原成可显示的状态。
         */
        mSearchContainer.setVisibility(View.VISIBLE);
        //fix bug 8269270 此处之前有防止快速点击的逻辑，如果加上快速点击逻辑会导致问题单当中现象
        if ((fragmentStackSize() > SEARCH_FRAGMENT_STACK_SIZE)) {
            if (backPressed()) {
                pop();
                if (fragmentStackSize() == SEARCH_FRAGMENT_STACK_SIZE) { // 退出搜索结果详情
                    if (mSearchResultStatus == SHOW_RECOMMEND) { // 点推荐进详情返回后清除搜索
                        mSearchView.cleanSearchView();
                    } else { // 返回搜索结果刷新
                        mSearchView.setQuery(mSearchView.getQuery(), false);
                    }
                    // 刷新推荐（图集全选删除）
                    loadRecommendData();
                }
                // 跳转其他Fragment回到搜索页面后，重新绑定Toolbar
                rebindActivity();
            }
        } else if (mShowGuideDetail) {
            String query = mSearchView.getEditTextString();
            if (query.isEmpty()) {
                onQueryTextChange("");
            } else {
                mSearchView.cleanSearchView();
            }

        } else if (mSearchResultStatus == SHOW_RECOMMEND) {
            if (mIsStatusBarTint) {
                setStatusBarAppearance(false);
            }
            if (mSearchViewBar != null) {
                mSearchViewBar.changeStateImmediately(GallerySearchLayout.STATE_NORMAL);
                mSearchViewBar.startCancelButtonExitAnimation(false);
                // 需要把mSearchView的内容设为空，这样才能通过监听的逻辑把页面切换为搜索推荐页
                mSearchView.setQuery(EMPTY_STRING, false);
            }
            supportFinishAfterTransition();
        } else {
            onSearchResultChanged(SHOW_RESULT);
            String query = mSearchView.getEditTextString();
            if (query.isEmpty()) {
                onQueryTextChange("");
            } else {
                mSearchView.cleanSearchView();
            }
        }
    }

    @Override
    public boolean onClickCancel() {
        SearchTrackHelper.trackSearchCancel();
        if (mSearchViewBar != null) {
            mSearchViewBar.openSoftInput(false);
            mSearchViewBar.changeStateImmediately(GallerySearchLayout.STATE_NORMAL);
            mSearchViewBar.startCancelButtonExitAnimation(false);
        }
        if (mSearchView.hasInputWord() || mSearchView.hasWordLayout()) {
            setEnterSharedElementCallback(new SharedElementCallback() {
            });
        }
        if (mIsStatusBarTint) {
            setStatusBarAppearance(false);
        }
        if (mFragmentResult != null) {
            mFragmentResult.stopAIAsk();
        }
        supportFinishAfterTransition();
        SearchKeywordHelper.clearKeywords();
        return true;
    }

    @Override
    public void onSearchViewClick() {
        if (mSearchViewBar != null) {
            mSearchViewBar.openSoftInput(true);
        }
        if ((mSearchView != null) && (mSearchView.getEditTextString().isEmpty())) {
            onSearchResultChanged(SHOW_RECOMMEND);
        }
    }

    @Override
    public boolean singleJumpEnable() {
        return mSingleJumpEnable;
    }

    @Override
    public boolean onEditorAction(TextView textView, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_SEARCH) {
            String query = mSearchView.getEditTextString();
            String resultName = "0";
            if ((query == null) || query.trim().isEmpty()) {
                onSearchResultChanged(SHOW_RECOMMEND);
            } else if (TextUtil.containsEmoji(query)) {
                onSearchResultChanged(NO_RESULT);
            } else {
                mSearchView.clearFocus();
                mFragmentResult.scrollToGridResult();
                resultName = mFragmentResult.hasSearchResult() ? "1" : "0";
            }
            if (!mFragmentResult.hasSearchResult()) {
                String guideResult = mFragmentResult.getGuideResult();
                if (!TextUtils.isEmpty(guideResult)) {
                    resultName = guideResult;
                }
            }
            // statistics
            if (SearchKeywordHelper.getKeywords().isEmpty()) {
                SearchTrackHelper.trackSearchClickBtn(false, resultName, query);
            } else if (mSearchView.hasWordLayout() && mSearchView.hasInputWord()) {
                SearchTrackHelper.trackSearchClickBtn(true, resultName, query);
            }
            if (mSearchViewBar != null) {
                mSearchViewBar.openSoftInput(false);
            }
            TimelineUtils.setDebugForTimeline(query);
        }
        return true;
    }

    @Override
    public int getLayoutMaxHeight() {
        return (mContentView == null) ? 0 : mContentView.getHeight();
    }

    /**
     * Check out if comes from third part application, may be it will search with keywords.
     */
    private void initKeywords() {
        Intent intent = getIntent();
        if (intent == null) {
            onSearchResultChanged(SHOW_RECOMMEND);
            return;
        }
        //maybe enable if comes from Breeno so far;
        mSingleJumpEnable = IntentUtils.getBooleanExtra(intent, KEY_FILTER_SINGLE_JUMP, false);

        //get the keywords
        String[] keywords = IntentUtils.getStringArrayExtra(intent, KEY_FILTER_KEYWORD);
        if ((keywords == null) || (keywords.length == 0)) {
            return;
        }

        String selection = IntentUtils.getStringExtra(intent, KEY_FILTER_SELECTION);
        String singleKeyword = filterKeywords(keywords);
        if (SearchSuggestionProviderUtil.SUGGESTION_SELECTION_KEYWORD.equalsIgnoreCase(selection)) {
            mSearchView.setQuery(singleKeyword, true);
        } else {
            mSearchView.setOnKeywordsSearchListener(this);
            mSearchView.setKeywords(singleKeyword, keywords);
            mFragmentResult.setKeywords(keywords);
        }

        GLog.d(TAG, "[initKeywords] singleKeyword : " + singleKeyword
                + "; selection : " + ((selection == null) ? "slot-filter" : selection));
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public void cleanHistoricalRecords() {
        GLog.d(TAG, LogFlag.DL, () -> "cleanHistoricalRecords");
        BuildersKt.launch(
                LifecycleKt.getCoroutineScope(getLifecycle()),
                Dispatchers.getIO(),
                CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    new DeleteReq.Builder()
                            .setDaoType(IDao.DaoType.GALLERY)
                            .setTableType(GalleryDbDao.TableType.SEARCH_HISTORY)
                            .build().exec();
                    return null;
                });
        SearchTrackHelper.trackSearchHistoryData();
    }

    private void goToPersonAlbumPage(String title, long personId) {
        Bundle bundle = new Bundle();
        bundle.putString(KEY_MEDIA_PATH, Person.PATH_ALBUM_PERSON_ANY.getChild(personId).toString());
        bundle.putString(KEY_ALBUM_TITLE, title);
        bundle.putBoolean(KEY_HIDE_INTERNAL_TOOLBAR, false);
        bundle.putBoolean(KEY_FROM_SEARCH_ACTIVITY, true);
        startFragment(PERSON_PET_DETAIL_FRAGMENT, bundle);
    }

    private void goToAlbumPage(String title, String idList, int type) {
        boolean orderByDateModified = getString(com.oplus.gallery.basebiz.R.string.model_search_recommend_recently_added).equals(title);
        goToAlbumPage(title, idList, type, orderByDateModified);
    }

    private void startRecyclerAlbum(String name) {
        BuildersKt.launch(LifecycleKt.getCoroutineScope(getLifecycle()), Dispatchers.getIO(),
                CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
                    mRecycleName = name;
                    try {
                        Intent intent = AlbumPasswordSettingsUtil.makeDecryptIntentIfNeed(ContextGetter.context);
                        if (intent == null) {
                            runOnUiThread(() -> {
                                if (!TextUtils.isEmpty(name)) {
                                    goToRecycleAlbumPage(name);
                                }
                            });
                        } else {
                            mResultLauncher.launch(intent);
                        }
                    } catch (Exception e) {
                        GLog.e(TAG, LogFlag.DL, "goToAlbumPageWithPwd error:", e);
                    }
                    return null;
                });
    }

    private void goToAlbumPage(String title, String idList, int type, boolean orderByDateModified) {
        Path path = Local.PATH_ALBUM_SEARCH_RESULT.getChild(idList.hashCode());
        SearchResultAlbum album = (SearchResultAlbum) DataManager.getMediaSet(path);
        if (album == null) {
            GLog.e(TAG, "goToAlbumPage error, album is null, path = " + path);
            return;
        }
        //强制最新照片在上方
        boolean recentPhotosDisplayAtTheTop = needRecentPhotosAtTheTop(type);
        //这里album根据orderByDateModified来排序，isPositiveOrder设置为false
        SearchResultAlbum.SearchResultAlbumEntry albumEntry = new SearchResultAlbum.SearchResultAlbumEntry(
            idList,
            SearchKeywordHelper.getKeywords(),
            type,
            orderByDateModified,
            false
        );
        album.setInputEntry(albumEntry);
        Bundle bundle = new Bundle();
        bundle.putString(KEY_MEDIA_PATH, path.toString());
        bundle.putString(KEY_GALLERY_ID_LIST, idList);
        bundle.putString(KEY_ALBUM_TITLE, title);
        bundle.putBoolean(KEY_HIDE_INTERNAL_TOOLBAR, false);
        bundle.putBoolean(KEY_FROM_SEARCH_ACTIVITY, true);
        if (recentPhotosDisplayAtTheTop) {
            bundle.putBoolean(KEY_NEED_SCROLL_TO_BOTTOM, false);
            Bundle modelBundle = new Bundle();
            //指定排序方式为最新照片在上面
            modelBundle.putBoolean(KEY_IS_POSITIVE_ORDER, false);
            bundle.putBundle(KEY_ALBUM_MODEL_BUNDLE, modelBundle);
        }
        startFragment(ALBUM_FRAGMENT, bundle);
    }

    /**
     * 背景：bug 9446618,搜索页智能分类进图集详情的排序是跟随设置的，而图集页智能分类进详情的排序是固定最新照片显示在上方，
     * 两个入口进智能分类详情的排序不一样
     *
     * 返回是否最新照片显示在顶部
     * @param type 时间、人物、智能分类、地点...
     * @return true: 表示最新照片显示在列表顶部
     */
    private boolean needRecentPhotosAtTheTop(int type) {
        switch (type) {
            case TYPE_LABEL:
            case TYPE_GUIDE_LABEL:
                return true;
            default:
                return false;
        }
    }

    /**
     * 跳转到多模态搜索结果页
     */
    private void goToMultiModalResultPage(String title, String idList, String mediaIdList) {
        Path path = Local.PATH_ALBUM_SEARCH_MULTI_MODAL_RESULT.getChild(idList.hashCode());
        SearchMultiModalResultAlbum album = (SearchMultiModalResultAlbum) DataManager.getMediaSet(path);
        if (album == null) {
            GLog.e(TAG, "goToMultiModalResultPage error, album is null, path = " + path);
            return;
        }
        album.setInputEntry(new ExtOrderIdsPictureAlbum.OrderIdsPictureInputEntry(mediaIdList));
        Bundle bundle = new Bundle();
        bundle.putString(KEY_MEDIA_PATH, path.toString());
        bundle.putString(KEY_GALLERY_ID_LIST, idList);
        bundle.putString(KEY_ALBUM_TITLE, title);
        bundle.putBoolean(KEY_HIDE_INTERNAL_TOOLBAR, false);
        bundle.putBoolean(KEY_FROM_SEARCH_ACTIVITY, true);
        startFragment(ALBUM_FRAGMENT, bundle);
    }

    private void goToMemoriesAlbumPage(String title, String idList) {
        int memoriesId = -1;
        try {
            memoriesId = Integer.parseInt(idList);
        } catch (Exception e) {
            GLog.d(TAG, "goToMemoriesAlbumSetPage, Exception = " + e);
            return;
        }
        Path path = Local.PATH_ALBUM_MEMORIES_ANY.getChild(memoriesId);
        Bundle bundle = new Bundle();
        bundle.putString(KEY_MEDIA_PATH, path.toString());
        bundle.putString(KEY_ALBUM_TITLE, title);
        bundle.putBoolean(KEY_HIDE_INTERNAL_TOOLBAR, false);
        bundle.putBoolean(KEY_FROM_SEARCH_ACTIVITY, true);
        startFragment(MEMORIES_DETAIL_FRAGMENT, bundle);
    }

    private void goToRecycleAlbumPage(String title) {
        Bundle bundle = new Bundle();
        bundle.putString(KEY_MEDIA_PATH, SourceConstants.Recycle.PATH_ALBUM_ALL_ANY.toString());
        bundle.putString(KEY_ALBUM_TITLE, title);
        bundle.putBoolean(KEY_HIDE_INTERNAL_TOOLBAR, false);
        bundle.putBoolean(KEY_FROM_SEARCH_ACTIVITY, true);
        startFragment(RECYCLE_FRAGMENT, bundle);
    }

    @Override
    public PrioritySession getWorkerSession() {
        return mSession;
    }

    @Override
    public void setQuery(String query) {
        mSearchView.cleanWordLayout();
        mSearchView.setQuery(query, false);
    }

    @Override
    public void onHistoricalRecordClicked(String record) {
        GLog.d(TAG, "onHistoricalRecordClicked record = " + record);
        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - mUserActionTime) < USER_ACTION_INTERVAL) {
            return;
        }
        if (mSearchView != null) {
            SearchKeywordHelper.setKeyword(record, true);
            mCurrentQueryEntry = SearchResultFragment.ENTRY_TYPE_HISTORY;
            mSearchView.setQuery(record, true);
            //build word package
            mSearchView.addWordLayout(record);
            mShowGuideDetail = true;
        }
        mUserActionTime = currentTime;
        SearchTrackHelper.trackClickSearchRecord(record);
    }

    @Override
    public void onRecommendClicked(int type, String recommend, ItemInfo itemInfo) {
        if (mIsQueryRecommend) {
            GLog.v(TAG, "[onRecommendClicked] type:" + type + ", recommend:" + recommend
                    + ", canceled because the last one wasn't finished yet");
        } else {
            GLog.v(TAG, "[onRecommendClicked] type:" + type + ", recommend:" + recommend);
            if (mFragmentRecommend != null) {
                mIsQueryRecommend = mFragmentRecommend.getRecommendResultInfo(type, recommend, itemInfo, this);
            }
            // statistics
            searchRecommendTrack(type, recommend);
        }
    }

    private void searchRecommendTrack(int type, String recommend) {
        SearchTrackHelper.trackSearchClickRecommend(recommend);
        if (type == TYPE_GUIDE_LABEL) {
            String query = TextUtil.EMPTY_STRING;
            if (mSearchView != null) {
                query = mSearchView.getEditTextString();
            }
            SearchTrackHelper.trackSearchRecommendLabelClick(query, recommend,
                    SearchTrackHelper.mappingRecommendClickedLabelType(recommend));
        }
    }

    @Override
    public void onRecommendResulted(String recommend, int type, String result, int count, ItemInfo itemInfo) {
        if (result == null) {
            onSearchResultChanged(NO_RESULT);
            mIsQueryRecommend = false;
            return;
        }
        SearchKeywordHelper.setKeyword(recommend, true);
        switch (type) {
            case TYPE_PERSON:
                String keyword = itemInfo.mName;
                if (TextUtils.isEmpty(keyword)) {
                    keyword = getResources().getString(com.oplus.gallery.basebiz.R.string.base_back_title_with_no_name);
                    SearchKeywordHelper.setPersonGroupId(Integer.parseInt(recommend));
                }
                SearchKeywordHelper.setKeyword(keyword, true);
                goToPersonAlbumPage(itemInfo.mName, Integer.parseInt(result));

                // statistics
                SearchTrackHelper.trackClickSearchPerson(null);
                break;
            case TYPE_LOCATION:
                goToAlbumPage(recommend, result, type);

                // statistics
                SearchTrackHelper.trackClickSearchLocation(recommend);
                break;
            case TYPE_RECENT_TIME:
                goToAlbumPage(recommend, result, type);

                // statistics
                SearchTrackHelper.trackClickSearchRecent(null, true);
                break;
            case TYPE_DATETIME:
                goToAlbumPage(recommend, result, type);

                // statistics
                SearchTrackHelper.trackClickSearchDate(recommend);
                break;
            case TYPE_LABEL:
            case TYPE_GUIDE_LABEL:
                String labelIds = result;
                goToAlbumPage(recommend, labelIds, type);

                // statistics
                SearchTrackHelper.trackClickSearchScene(recommend);
                break;
            default:
                break;
        }
        if (mSearchViewBar != null) {
            mSearchViewBar.openSoftInput(false);
        }
        mSearchView.clearFocus();
        mIsQueryRecommend = false;
    }

    @Override
    public void onSearchResultChanged(int status) {
        GLog.d(TAG, "onSearchResultChanged status = " + status);
        switch (status) {
            case NO_RESULT:
                if (mFragmentRecommend != null) {
                    mFragmentRecommend.setContentVisible(false);
                }
                if (mFragmentResult != null) {
                    mFragmentResult.setContentVisibility(View.VISIBLE, false, false, mSearchView.isInSearching());
                }
                break;
            case GUIDE_RESULT:
                if (mFragmentRecommend != null) {
                    mFragmentRecommend.setContentVisible(false);
                }
                if (mFragmentResult != null) {
                    mFragmentResult.setContentVisibility(View.VISIBLE, false, true, mSearchView.isInSearching());
                }
                break;
            case SHOW_RESULT:
                if (mFragmentRecommend != null) {
                    mFragmentRecommend.setContentVisible(false);
                }
                if (mFragmentResult != null) {
                    mFragmentResult.setContentVisibility(View.GONE, true, false, mSearchView.isInSearching());
                    if (mSingleJumpEnable && mFragmentResult.hasSearchResult()) {
                        mSingleJumpEnable = false;
                        mFragmentResult.singleJump();
                    }
                }
                break;
            case SHOW_RECOMMEND:
                if (mFragmentRecommend != null) {
                    mFragmentRecommend.setContentVisible(true);
                }
                if (mFragmentResult != null) {
                    mFragmentResult.setContentVisibility(View.GONE, false, false, mSearchView.isInSearching());
                }
                break;
            default:
                break;
        }
        mSearchResultStatus = status;
    }

    private void saveHistoricalSuggestion(final String query) {
        GLog.d(TAG, "saveHistoricalSuggestion query = " + query);
        if (!TextUtils.isEmpty(query) && !TextUtils.equals(query, getResources()
                .getString(com.oplus.gallery.basebiz.R.string.base_back_title_with_no_name))) {
            // save historical
            BuildersKt.launch(
                    LifecycleKt.getCoroutineScope(getLifecycle()),
                    Dispatchers.getIO(),
                    CoroutineStart.DEFAULT,
                    (coroutineScope, continuation) -> {
                        new InsertReq.Builder().setDaoType(IDao.DaoType.GALLERY)
                                .setTableType(GalleryDbDao.TableType.SEARCH_HISTORY)
                                .setConvert(aVoid -> {
                                    ContentValues values = new ContentValues();
                                    values.put(GalleryStore.SearchHistoryColumns.DISPLAY1, query);
                                    values.put(GalleryStore.SearchHistoryColumns.QUERY, query);
                                    values.put(GalleryStore.SearchHistoryColumns.DATE, System.currentTimeMillis());
                                    return values;
                                }).build().exec();
                        return null;
                    });
        }
    }

    @Override
    public void onSearchResultClicked(String name, String bundle, String mediaIdList, int albumType, int type, ArrayList<String> nameList) {
        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - mUserActionTime) < USER_ACTION_INTERVAL) {
            return;
        }
        if (mSearchViewBar != null) {
            mSearchViewBar.openSoftInput(false);
        }
        // single keyword
        if ((nameList == null) || nameList.isEmpty()) {
            nameList = new ArrayList<>();
            nameList.add(name);
        }
        ArrayList<CharSequence> newKeywords = SearchKeywordHelper.repackKeywordList(nameList);
        GLog.d(TAG, "onSearchResultClicked type = " + type + ", albumType = " + albumType + ", name = " + name
                + ", nameList = " + nameList + ", newKeyword = " + newKeywords
                + GET_KEYWORDS + SearchKeywordHelper.getKeywords());
        SearchKeywordHelper.setKeywords(newKeywords);
        for (CharSequence keyword : newKeywords) {
            if (!TextUtils.isEmpty(keyword)) {
                saveHistoricalSuggestion(keyword.toString());
            }
        }
        mSearchView.clearFocus();

        switch (type) {
            case TYPE_PERSON:
                mUserActionTime = currentTime;
                goToPersonAlbumPage(name, Integer.parseInt(bundle));
                break;
            case TYPE_SPECIAL_LABEL:// same as TYPE_CHILD_LABEL, drop case down
            case TYPE_MULTI_LABEL:// same as TYPE_CHILD_LABEL, drop case down
            case TYPE_LOCATION: // same as TYPE_CHILD_LABEL, drop case down
            case TYPE_DATETIME: // same as TYPE_CHILD_LABEL, drop case down
            case TYPE_LABEL: // same as TYPE_CHILD_LABEL, drop case down
            case TYPE_OCR: // same as TYPE_CHILD_LABEL, drop case down
            case TYPE_CHILD_LABEL:
                mUserActionTime = currentTime;
                if (isAndesSearchEnable()) {
                    goToMultiModalResultPage(name, bundle, mediaIdList);
                } else {
                    goToAlbumPage(name, bundle, type);
                }
                break;
            case TYPE_ALBUM:
                mUserActionTime = currentTime;
                if (albumType == AlbumEntry.TYPE_RECYCLE_ALBUM) {
                    startRecyclerAlbum(name);
                } else {
                    if (isAndesSearchEnable()) {
                        goToMultiModalResultPage(name, bundle, mediaIdList);
                    } else {
                        goToAlbumPage(name, bundle, type, albumType != AlbumEntry.TYPE_CAMERA);
                    }
                }
                break;
            case TYPE_MEMORIES_ALBUM:
                mUserActionTime = currentTime;
                goToMemoriesAlbumPage(name, bundle);
                break;
            default:
                break;
        }
    }

    @Override
    public void onSearchGuideCLicked(SearchResultGuideItemEntry entry) {
        RealShowTimeInstrument.startRecord(SPECIAL_TYPE_SEARCH);
        RealShowTimeInstrument.startRecord(SPECIAL_TYPE_SEARCH_FULL_LINK);
        mFragmentResult.mCurrentSearchResultGuideItemEntry = entry;
        String name = entry.getName();
        int albumType = entry.getAlbumType();
        int searchType = entry.getType();
        ArrayList<String> nameList = entry.getCombinationNameList();
        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - mUserActionTime) < USER_ACTION_INTERVAL) {
            return;
        }
        if (mSearchViewBar != null) {
            mSearchViewBar.openSoftInput(false);
        }
        // single keyword
        if (nameList.isEmpty()) {
            nameList = new ArrayList<>();
            nameList.add(name);
        }
        ArrayList<CharSequence> newKeywords = SearchKeywordHelper.repackKeywordList(nameList);
        GLog.d(TAG, "onSearchGuideCLicked type = " + entry.getType() + ", albumType = " + albumType + ", name = " + name
                + ", nameList = " + nameList + ", new_Keywords = " + newKeywords
                + GET_KEYWORDS + SearchKeywordHelper.getKeywords());
        SearchKeywordHelper.setKeywords(newKeywords);
        for (CharSequence keyword : newKeywords) {
            if (!TextUtils.isEmpty(keyword)) {
                saveHistoricalSuggestion(keyword.toString());
            }
        }
        mShowGuideDetail = true;
        mSearchView.clearFocus();
        GLog.d(TAG, "onSearchGuideCLicked updateSuggestionResult");
        mFragmentResult.updateSuggestionResult(name, SearchResultFragment.ENTRY_TYPE_UNKNOWN, true,
                true, searchType, entry, false);
        mSearchView.addWordLayout(newKeywords);
    }

    @Override
    public void onLocationSuggestionClicked() {
        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - mUserActionTime) < USER_ACTION_INTERVAL) {
            return;
        }
        if (mSearchViewBar != null) {
            mSearchViewBar.openSoftInput(false);
        }

        mSearchView.clearFocus();
    }

    @Override
    public void onViewAllButtonClicked(String name, String bundle, String mediaIdList, int type,
                                       ArrayList<String> nameList, boolean isMultiModalResult) {
        long currentTime = System.currentTimeMillis();
        if (Math.abs(currentTime - mUserActionTime) < USER_ACTION_INTERVAL) {
            return;
        }
        if (mSearchViewBar != null) {
            mSearchViewBar.openSoftInput(false);
        }
        // single keyword
        if ((nameList == null) || nameList.isEmpty()) {
            nameList = new ArrayList<>();
            nameList.add(name);
        }

        mSearchView.clearFocus();

        if (type == TYPE_ALL_PHOTO) {
            String searchClickType = CLICK_ALL;
            String resultName = name;
            if (isMultiModalResult) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < nameList.size(); i++) {
                    sb.append(nameList.get(i));
                }
                resultName = sb.toString();
                goToMultiModalResultPage(resultName, bundle, mediaIdList);
                searchClickType = SEARCH_MULTIMODAL;
                saveHistoricalSuggestion(resultName);
            } else {
                //复合搜索时，将两个关键字用 和 连接作为图集的标题
                if (nameList.size() >= 2) {
                    resultName = getResources().getString(R.string.search_result_album_title, nameList.get(0), nameList.get(1));
                }
                goToAlbumPage(resultName, bundle, type);
            }

            mUserActionTime = currentTime;
            SearchTrackHelper.trackSearchClickAll(resultName, searchClickType);
        }
    }

    @Override
    public void onLayoutChange(View view, int left, int top, int right, int bottom,
                               int oldLeft, int oldTop, int oldRight, int oldBottom) {
        int diffHeight = (oldBottom - oldTop) - (bottom - top);
        mIsIMEHidden = diffHeight < 0;
        if (mIsIMEHidden && mIsUnfocusedOnSearch) {
            mHandler.post(() -> {
                SearchTrackHelper.trackSearchCancel();
                finish();
            });
        }
    }

    @Override
    public void onScrollStateChanged(int scrollState) {
        if ((mSearchViewBar != null) && (scrollState == RecyclerView.SCROLL_STATE_DRAGGING)) {
            mSearchViewBar.openSoftInput(false);
        }
    }

    @Override
    public void setIsJumpToPhotoPage(boolean isJumpToPhotoPage) {
        if (isJumpToPhotoPage) {
            // 记录搜索词汇, 取消焦点
            SearchView searchView = mSearchView;
            if (searchView != null) {
                searchView.clearFocus();
            }

            // 关闭键盘
            GallerySearchLayout searchViewBar = mSearchViewBar;
            if (searchViewBar != null) {
                searchViewBar.openSoftInput(false);
            }
        }
    }

    @Override
    public List<CharSequence> getWordPackage() {
        return mSearchView.getWords();
    }

    /**
     * 点击gridView的item
     * 若isMultiModalResult为true，将存入历史搜索词
     * @param queryWord 搜索词
     * @param isMultiModalResult 是否是多模态结果
     */
    @Override
    public void onClickGridItem(String queryWord, boolean isMultiModalResult) {
        if (isMultiModalResult) {
            saveHistoricalSuggestion(queryWord);
        }
    }

    @Override
    public boolean isInSearching() {
        return mSearchView.isInSearching();
    }

    @Override
    public void onSystemBarChanged(@NotNull WindowInsetsCompat windowInsets) {
        if (mSearchTopBarLayout != null) {
            int statusBarHeight = statusBarHeight(true);
            int paddingTop = statusBarHeight;
            Insets naviInsets = SystemBarControllerKt.naviBarInsets(windowInsets, true);
            int paddingBottom = 0;
            if (hasVirtualKey()) {
                paddingBottom = naviInsets.bottom;
            }
            mSearchContainer.setPadding(naviInsets.left, paddingTop, naviInsets.right, paddingBottom);
        }
    }

    private void doDataSync() {
        BuildersKt.launch(
                LifecycleKt.getCoroutineScope(getLifecycle()),
                Dispatchers.getIO(),
                CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    MemoriesUtils.syncMemories();
                    return null;
                });
    }

    @Override
    public void rebindActivity() {
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(false);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
    }

    @Override
    public void setSelectionMode(boolean selectionMode) {
        if (selectionMode) {
            mToolbar.setIsTitleCenterStyle(false);
            mSearchViewBar.setVisibility(View.GONE);
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel);
            getSupportActionBar().setHomeActionContentDescription(com.oplus.gallery.basebiz.R.string.base_close);
        } else {
            getSupportActionBar().setDisplayHomeAsUpEnabled(false);
            mToolbar.setIsTitleCenterStyle(false);
            mSearchViewBar.setVisibility(View.VISIBLE);
            mToolbar.setTitle(TextUtil.EMPTY_STRING);
        }
        invalidateOptionsMenu();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item == null) {
            return false;
        }

        switch (item.getItemId()) {
            case android.R.id.home:
                onBackPressed();
                return true;

            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override
    public void setTitle(CharSequence title) {
        super.setTitle(title);
        mToolbar.setTitle(title);
    }

    @Override
    public void setTitleAppearance(int id) {
        // do nothing
    }

    @Override
    public void setTitleTextColor(int color) {
        // do nothing
    }

    @Override
    public void setSubtitle(@NotNull CharSequence subtitle) {
    }

    @Override
    public void setListScrollPosition(int scrollPosition) {
        // do nothing
    }

    @Override
    public void setListScrollStateIdle(@NotNull View view, int scrollPosition) {
        // do nothing
    }

    @Override
    public void setDividerVisible(boolean isVisible) {
        // do nothing
    }

    @Override
    public void setIsTitleCenterStyle(boolean isTitleCenterStyle) {
        mToolbar.setIsTitleCenterStyle(isTitleCenterStyle);
    }

    @Override
    public void setMenuEnable(int menuId, boolean isEnabled) {
        // do nothing
    }

    @Override
    public void setRedDot(int menuId, int num) {
        // do nothing
    }

    /**
     * 监听点击事件, 如果点击在输入框区域外面,则隐藏键盘
     * @param ev
     * @return
     */
    @Override
    public boolean dispatchTouchEvent(@NonNull MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            View currentFocusView = getCurrentFocus();
            if (isSearchInputArea(currentFocusView, ev) && mSearchViewBar != null) {
                mSearchViewBar.openSoftInput(false);
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    /**
     * 判断是否在输入框区域
     * @param currentFocusView
     * @param event
     * @return
     */
    private boolean isSearchInputArea(View currentFocusView, MotionEvent event) {
        if ((currentFocusView != null) && (currentFocusView instanceof EditText)) {
            int[] leftTop = {0, 0};
            //获取输入框当前的location位置
            currentFocusView.getLocationInWindow(leftTop);
            int left = leftTop[0];
            int top = leftTop[1];
            int bottom = top + currentFocusView.getHeight();
            int right = left + currentFocusView.getWidth();
            if ((event.getX() > left) && (event.getX() < right)
                    && (event.getY() > top) && (event.getY() < bottom)) {
                // 点击的是输入框区域，保留点击EditText的事件
                return false;
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 删除词包后监听，用于清理上一次词包搜索结果的ResultGuideItemEntry
     */
    public interface OnWordDeleteListener {
        void onWordDelete();
    }

    /**
     * Search result reload observer
     */
    private static class SearchResultReloadObserver extends HandlerContentObserver {
        private WeakReference<SearchActivity> mWeakSearchActivity;

        SearchResultReloadObserver(SearchActivity activity) {
            super(null);
            mWeakSearchActivity = new WeakReference<>(activity);
        }

        @Override
        public void onChange(boolean selfChanged, Uri uri) {
            SearchActivity activity = mWeakSearchActivity.get();
            if ((activity == null) || activity.isDestroyed() || activity.isFinishing()) {
                return;
            }
            String query = activity.mSearchView.getEditTextString();
            GLog.d(TAG, "onChange, query = " + query + ", uri = " + uri);
            boolean isSearchUri = SD_MOUNT_RECOMMEND_UPDATING_URI.equals(uri)
                    || LOCALE_CHANGE_RECOMMEND_UPDATING_URI.equals(uri)
                    || SEARCH_WHITE_LIST_DOWNLOAD_URI.equals(uri);
            boolean isScanUri = GalleryStore.ScanFace.getContentUri().equals(uri)
                    || GalleryStore.ScanLabel.getContentUri().equals(uri);
            if ((isSearchUri || isScanUri) && (activity.mFragmentRecommend != null)) {
                activity.mFragmentRecommend.forceToUpdateRecommend();
            }
            // 限制非推荐页，否则从推荐页点击进入图集后全选删除，走到这里还没onBackPressed清除搜索栏，query非空进行刷新，就会变成结果页
            boolean isQueryValid = (query != null)
                    && !query.trim().isEmpty()
                    && !TextUtil.containsEmoji(query);
            if (isQueryValid && (activity.mSearchResultStatus != SHOW_RECOMMEND)
                    && (activity.mFragmentResult != null)
                    && (activity.mResultRefreshHandler != null)) {
                activity.mResultRefreshHandler.removeMessages(ResultStaticHandler.MESSAGE_TYPE_REFRESH);
                activity.mResultRefreshHandler.sendEmptyMessageDelayed(ResultStaticHandler.MESSAGE_TYPE_REFRESH, RESULT_REFRESH_INTERVAL);
            }
        }
    }

    private static class ResultStaticHandler extends StaticHandler<SearchActivity> {
        public static final int MESSAGE_TYPE_REGISTER = 1;
        public static final int MESSAGE_TYPE_UNREGISTER = 2;
        public static final int MESSAGE_TYPE_REFRESH = 3;
        public static final int MESSAGE_TYPE_NOTIFY_DIRTY = 4;

        public ResultStaticHandler(SearchActivity activity, Looper looper) {
            super(activity, looper);
        }

        @Override
        protected void handleMessage(Message msg, SearchActivity activity) {
            switch (msg.what) {
                case MESSAGE_TYPE_REGISTER:
                    registerContentObserverImpl(activity);
                    break;
                case MESSAGE_TYPE_UNREGISTER:
                    unregisterContentObserverImpl(activity);
                    break;
                case MESSAGE_TYPE_REFRESH:
                    refreshResult(activity);
                    break;
                case MESSAGE_TYPE_NOTIFY_DIRTY:
                    ISearchAbility searchAbility = ((GalleryApplication) ContextGetter.context).getAppAbility(ISearchAbility.class);
                    if (searchAbility != null) {
                        searchAbility.notifyDataDirty(BACKGROUND_COLLECT_URI);
                        IOUtils.closeQuietly(searchAbility);
                    }
                    break;
                default:
                    break;
            }
        }

        private void registerContentObserverImpl(SearchActivity activity) {
            SearchResultReloadObserver observer = activity.mSearchResultObserver;
            ContentObserverManager.registerContentObserver(activity, GalleryStore.ScanLabel.getContentUri(),
                    true, observer);
            ContentObserverManager.registerContentObserver(activity, GalleryStore.ScanFace.getContentUri(),
                    true, observer);
            ContentObserverManager.registerContentObserver(activity, MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    true, observer);
            ContentObserverManager.registerContentObserver(activity, SEARCH_WHITE_LIST_DOWNLOAD_URI,
                    true, observer);
            ContentObserverManager.registerContentObserver(activity, BACKGROUND_COLLECT_COMPLETE_URI,
                    true, observer);
            ContentObserverManager.registerContentObserver(activity, SD_MOUNT_RECOMMEND_UPDATING_URI,
                    false, observer);
            ContentObserverManager.registerContentObserver(activity, LOCALE_CHANGE_RECOMMEND_UPDATING_URI,
                    false, observer);
            ContentObserverManager.registerContentObserver(activity, GalleryStore.MemoriesSet.getContentUri(),
                    false, observer);
            GLog.d(TAG, "registerContentObserverImpl");
        }

        private void unregisterContentObserverImpl(SearchActivity activity) {
            SearchResultReloadObserver observer = activity.mSearchResultObserver;
            ContentObserverManager.unregisterContentObserver(activity, observer);
            GLog.d(TAG, "unregisterContentObserverImpl");
        }

        private void refreshResult(SearchActivity activity) {
            activity.doDataSync();
            String query = activity.mSearchView.getEditTextString();
            String[] keywords = activity.mFragmentResult.getKeywords();
            if ((keywords == null) || (keywords.length == 0)) {
                // 融合搜索Kit端会分词，搜索query不应该由相册内部分词，所以融合搜索开启时应该由原query去构建Result
                if (!isAndesSearchEnable()) {
                    ArrayList<CharSequence> newKeywords = SearchKeywordHelper.repackKeyword(query);
                    query = SearchKeywordHelper.getKeywordText(newKeywords);
                    GLog.d(TAG, "refreshResult onChange query = " + query + ", newKeywords = " + newKeywords
                            + GET_KEYWORDS + SearchKeywordHelper.getKeywords()
                            + ", getKeywordText = " + SearchKeywordHelper.getKeywordText(newKeywords));
                }
                activity.mFragmentResult.updateSuggestionResult(query, SearchResultFragment.ENTRY_TYPE_UNKNOWN, false,
                        false, TYPE_MASK_TYPE, activity.mFragmentResult.mCurrentSearchResultGuideItemEntry, false);
            } else {
                GLog.d(TAG, "refreshResult onChange query = " + query + "; keywords[0] : " + keywords[0]);
                activity.mFragmentResult.updateSuggestionResult(query, keywords, SearchResultFragment.ENTRY_TYPE_UNKNOWN, false);
            }
            GLog.v(TAG, String.format("[onChange] Trigger re-update suggestion result with \"%s\"", query));
        }
    }

    private static boolean isAndesSearchEnable() {
        return ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ANDES_SEARCH, false, false);
    }

    private void registerContentObserver() {
        createHandlerThread();
        if (mResultRefreshHandler != null) {
            mResultRefreshHandler.sendEmptyMessage(ResultStaticHandler.MESSAGE_TYPE_REGISTER);
        }
    }

    private void unregisterContentObserver() {
        if (mResultRefreshHandler != null) {
            mResultRefreshHandler.sendEmptyMessage(ResultStaticHandler.MESSAGE_TYPE_UNREGISTER);
        } else {
            ContentObserverManager.unregisterContentObserver(this, mSearchResultObserver);
            GLog.w(TAG, "unregisterContentObserver with mResultRefreshHandler is null");
        }
        destroyHandlerThread();
    }

    private void createHandlerThread() {
        destroyHandlerThread();
        mHandlerThread = new HandlerThread(HANDLE_THREAD_NAME);
        mHandlerThread.start();
        mResultRefreshHandler = new ResultStaticHandler(this, mHandlerThread.getLooper());
    }

    private void destroyHandlerThread() {
        if (mResultRefreshHandler != null) {
            mResultRefreshHandler.removeMessages(ResultStaticHandler.MESSAGE_TYPE_REFRESH);
            mResultRefreshHandler.removeMessages(ResultStaticHandler.MESSAGE_TYPE_REGISTER);
            mResultRefreshHandler = null;
        }
        if (mHandlerThread != null) {
            /**
             * 因为[unregisterContentObserver]放在了该mHandlerThread中执行，因此此处必须要使用quitSafely
             * 避免直接退出而导致反注册的操作没执行，出现内存泄漏。
             */
            GLog.d(TAG, "destroyHandlerThread quitSafely");
            mHandlerThread.quitSafely();
        }
    }

    private void onStoragePermissionOK() {
        if (!mIsDoPermissionOK) {
            GLog.d(TAG, "onStoragePermissionOK");
            mIsDoPermissionOK = true;
            getContentResolver().notifyChange(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, null);
            GeoCacheService.scheduleJob(SearchActivity.this);
            ApiDmManager.getCloudSyncDM().triggerCloudSync(SYNC_MODE_INCR, SYNC_TYPE_ON_NECESSARY_PERMISSION_OK);
            mFragmentRecommend.loadRecommendData();
            ApiDmManager.getMediaDBSyncDM().enqueueIncrementSync();
        }
    }

    private void startFragment(String routerName, Bundle data) {
        startFragment(R.id.base_fragment_container, routerName, data, this, null, true, DEFAULT_ANIM_ARRAY);
        /*
         * 开启 Talkback 时，search_container 和 base_fragment_container 是平级关系。
         * 其中 search_container 是推荐项和搜索结果的显示容器，而 base_fragment_container 是结果图集页面的显示容器。
         * 当打开结果图集页面时，base_fragment_container 实际上只是覆盖在 search_container 上面，search_container
         * 仍然是可见的，所以系统认为不需要焦点的变化。
         * 因此，需要将 mSearchContainer 设置成不可见的状态，从而取消焦点。
         */
        mHandler.post(() -> mSearchContainer.setVisibility(View.GONE));
    }

    @NotNull
    @Override
    protected String getCTAWindowType() {
        return LaunchExitPopupConstant.Value.ALLOW_DIALOG_OPEN_NETWORK_FOR_ENTER_SEARCH;
    }

    private static class WeakSearchLayoutRunnable implements Runnable {
        private WeakReference<GallerySearchLayout> mSsearchLayoutWeakReference;

        public WeakSearchLayoutRunnable(GallerySearchLayout searchLayout) {
            mSsearchLayoutWeakReference = new WeakReference<>(searchLayout);
        }

        @Override
        public void run() {
            GLog.d(TAG, LogFlag.DL, () -> "WeakSearchLayoutRunnable run");
            if (mSsearchLayoutWeakReference != null) {
                GallerySearchLayout searchLayout = mSsearchLayoutWeakReference.get();
                if (searchLayout != null) {
                    searchLayout.changeStateImmediately(GallerySearchLayout.STATE_INPUT);
                }
            }
        }
    }
}
