/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * COLOR_OS
 * * File        :  - SearchView.java
 * * Description : for Searching
 * * Version     : 1.0
 * * Date        : 2019/01/22
 * * Author      : yong<PERSON><PERSON>@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  yongzhan@Apps.Gallery3D       2019/01/22   1.0     build this module
 ****************************************************************/

package com.oplus.gallery.searchpage.ui;

import android.annotation.SuppressLint;
import android.app.PendingIntent;
import android.app.SearchManager;
import android.app.SearchableInfo;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.speech.RecognizerIntent;
import android.text.Editable;
import android.text.InputType;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.ArrayMap;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputMethodManager;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatAutoCompleteTextView;
import androidx.core.content.res.ResourcesCompat;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper;
import com.oplus.gallery.foundation.ui.helper.FeedbackAnimatorHelperKt;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.searchpage.R;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.searchpage.SearchActivity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SearchView extends LinearLayout {
    private static final String LOG_TAG = "SearchView";
    private static final String IME_OPTION_NO_MICROPHONE = "nm";
    private static final String SPACE = " ";
    private final SearchAutoComplete mSearchSrcTextView;
    private final View mSearchEditFrame;
    private final View mSearchPlate;
    private final View mSubmitArea;
    private final ImageView mGoButton;
    private final ImageView mCloseButton;
    private final ImageView mVoiceButton;
    private final LinearLayout mSearchWordLayout;
    /**
     * Intents used for voice searching.
     */
    private final Intent mVoiceWebSearchIntent;
    private final Intent mVoiceAppSearchIntent;
    private final Map<String, KeywordItem> mKeywordItemMap = new ArrayMap<>();

    private UpdatableTouchDelegate mTouchDelegate;
    private Rect mSearchSrcTextViewBounds = new Rect();
    private Rect mSearchSrtTextViewBoundsExpanded = new Rect();
    private int[] mTemp = new int[2];
    private int[] mTemp2 = new int[2];

    private CharSequence mDefaultQueryHint;

    private SearchView.OnQueryTextListener mOnQueryChangeListener;
    private SearchView.OnCloseListener mOnCloseListener;
    private OnKeywordsSearchListener mOnKeywordsSearchListener;
    private OnFocusChangeListener mOnQueryTextFocusChangeListener;
    private SearchView.OnSuggestionListener mOnSuggestionListener;
    private SearchView.OnSearchViewClickListener mOnSearchClickListener;
    private SearchActivity.OnWordDeleteListener mOnWordDeleteListener;

    private boolean mIconifiedByDefault;
    private boolean mIconified;
    private boolean mSubmitButtonEnabled;
    private CharSequence mQueryHint;
    private boolean mClearingFocus;
    private int mMaxWidth;
    private boolean mVoiceButtonEnabled;
    private CharSequence mOldQueryText = "";
    private CharSequence mUserQuery;
    private boolean mExpandedInActionView;
    private int mCollapsedImeOptions;
    private boolean mQueryRefinement;
    /**
     * 变量mInSearching表明是否处在搜索状态
     * 输入状态变化时会认为处于搜索状态，置为true
     * 生成词包时表明搜索结束，置为false
     */
    private boolean mInSearching = false;

    private SearchableInfo mSearchable;
    private Bundle mAppSearchData;

    private float mWordHintTextSize;
    private int mWordPaddingLR;
    private int mWordPaddingBT;
    private int mWordPaddingToClearButton;
    private int mWordMarginRight;
    private float mWordTextSize;
    private int mWordMaxWidth;
    private int mWordHeight;
    private boolean mIsNormalFlat = true;

    private String[] mKeywords;

    private Runnable mUpdateDrawableStateRunnable = new Runnable() {
        public void run() {
            updateFocusedState();
        }
    };

    private List<CharSequence> mWords;

    /**
     * Callbacks for changes to the query text.
     */
    public interface OnQueryTextListener {

        boolean onQueryTextSubmit(String query);

        boolean onQueryTextChange(String newText);
    }

    public interface OnKeywordsSearchListener {
        void onKeywordsSearch(String query, String[] keywords);
    }

    public interface OnCloseListener {

        /**
         * The user is attempting to close the SearchView.
         *
         * @return true if the listener wants to override the default behavior of clearing the
         * text field and dismissing it, false otherwise.
         */
        boolean onClose();
    }
    /**
     * Callback interface for selection events on suggestions. These callbacks
     * are only relevant when a SearchableInfo has been specified by {@link #setSearchableInfo}.
     */
    public interface OnSuggestionListener {
        boolean onSuggestionSelect(int position);

        boolean onSuggestionClick(int position);
    }

    public SearchView(Context context) {
        this(context, null);
    }

    public SearchView(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.searchViewStyle);
    }

    public SearchView(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public SearchView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        final TypedArray a = context.obtainStyledAttributes(
                attrs, androidx.appcompat.R.styleable.SearchView, defStyleAttr, defStyleRes);
        final LayoutInflater inflater = (LayoutInflater) context.getSystemService(
                Context.LAYOUT_INFLATER_SERVICE);
        inflater.inflate(R.layout.search_view, this, true);

        mSearchSrcTextView = (SearchAutoComplete) findViewById(R.id.search_src_text);
        mSearchSrcTextView.setSearchView(this);

        mSearchEditFrame = findViewById(R.id.search_edit_frame);
        mSearchPlate = findViewById(R.id.search_plate);
        mSubmitArea = findViewById(R.id.submit_area);
        mGoButton = (ImageView) findViewById(R.id.search_go_btn);
        mCloseButton = (ImageView) findViewById(R.id.search_close_btn);
        mVoiceButton = (ImageView) findViewById(R.id.search_voice_btn);
        mSearchWordLayout = (LinearLayout) findViewById(R.id.search_word_layout);

        mCloseButton.setOnClickListener(mOnClickListener);
        mSearchSrcTextView.setOnClickListener(mOnClickListener);

        mSearchSrcTextView.addTextChangedListener(mTextWatcher);
        mSearchSrcTextView.setOnEditorActionListener(mOnEditorActionListener);
        mSearchSrcTextView.setOnKeyListener(mKeyListener);
        // Inform any listener of focus changes
        mSearchSrcTextView.setOnFocusChangeListener(new OnFocusChangeListener() {

            public void onFocusChange(View v, boolean hasFocus) {
                if (mOnQueryTextFocusChangeListener != null) {
                    mOnQueryTextFocusChangeListener.onFocusChange(SearchView.this, hasFocus);
                }
            }
        });

        final int maxWidth = a.getDimensionPixelSize(androidx.appcompat.R.styleable.SearchView_android_maxWidth, -1);
        if (maxWidth != -1) {
            setMaxWidth(maxWidth);
        }

        final int imeOptions = a.getInt(androidx.appcompat.R.styleable.SearchView_android_imeOptions, -1);
        if (imeOptions != -1) {
            setImeOptions(imeOptions);
        }

        final int inputType = a.getInt(androidx.appcompat.R.styleable.SearchView_android_inputType, -1);
        if (inputType != -1) {
            setInputType(inputType);
        }

        if (getFocusable() == FOCUSABLE_AUTO) {
            setFocusable(FOCUSABLE);
        }

        a.recycle();
        mQueryHint = getResources().getText(com.oplus.gallery.basebiz.R.string.model_search_hint);
        mWordPaddingLR = getResources().getDimensionPixelSize(R.dimen.android_search_auto_recommend_text_padding_start);
        mWordHintTextSize = getResources().getDimension(R.dimen.android_search_auto_recommend_text_size);
        mWordPaddingBT = getResources().getDimensionPixelSize(R.dimen.android_search_auto_recommend_text_padding_top);
        mWordPaddingToClearButton = getResources().getDimensionPixelSize(R.dimen.android_search_auto_recommend_text_padding_to_clear_button);
        mWordTextSize = getResources().getDimension(R.dimen.search_record_group_text_size);
        mWordMarginRight = getResources().getDimensionPixelSize(R.dimen.search_view_word_mrgin_right);
        mWordMaxWidth = context.getResources().getDimensionPixelSize(R.dimen.gl_search_item_text_width_max);
        mWordHeight = context.getResources().getDimensionPixelSize(R.dimen.gl_search_item_text_height_max);
        // Save voice intent for later queries/launching
        mVoiceWebSearchIntent = new Intent(RecognizerIntent.ACTION_WEB_SEARCH);
        mVoiceWebSearchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mVoiceWebSearchIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL,
                RecognizerIntent.LANGUAGE_MODEL_WEB_SEARCH);

        mVoiceAppSearchIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        mVoiceAppSearchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        updateViewsVisibility(mIconifiedByDefault);
        updateQueryHint();
        setIconified(false);
    }

    /**
     * Sets the SearchableInfo for this SearchView. Properties in the SearchableInfo are used
     * to display labels, hints, suggestions, create intents for launching search results screens
     * and controlling other affordances such as a voice button.
     *
     * @param searchable a SearchableInfo can be retrieved from the SearchManager, for a specific
     * activity or a global search provider.
     */
    public void setSearchableInfo(SearchableInfo searchable) {
        mSearchable = searchable;
        if (mSearchable != null) {
            updateSearchAutoComplete();
            updateQueryHint();
        }
        // Cache the voice search capability
        mVoiceButtonEnabled = hasVoiceSearch();

        if (mVoiceButtonEnabled) {
            /*
            Disable the microphone on the keyboard, as a mic is displayed near the text box
            use imeOptions to disable voice input when the new API will be available
            */
            mSearchSrcTextView.setPrivateImeOptions(IME_OPTION_NO_MICROPHONE);
        }
        updateViewsVisibility(isIconified());
    }

    /**
     * Sets the APP_DATA for legacy SearchDialog use.
     * @param appSearchData bundle provided by the app when launching the search dialog
     * @hide
     */
    public void setAppSearchData(Bundle appSearchData) {
        mAppSearchData = appSearchData;
    }

    /**
     * Sets the IME options on the query text field.
     *
     * @see TextView#setImeOptions(int)
     * @param imeOptions the options to set on the query text field
     *
     * @attr ref android.R.styleable#SearchView_imeOptions
     */
    public void setImeOptions(int imeOptions) {
        mSearchSrcTextView.setImeOptions(imeOptions);
    }

    /**
     * Returns the IME options set on the query text field.
     * @return the ime options
     * @see TextView#setImeOptions(int)
     *
     * @attr ref android.R.styleable#SearchView_imeOptions
     */
    public int getImeOptions() {
        return mSearchSrcTextView.getImeOptions();
    }

    /**
     * Sets the input type on the query text field.
     *
     * @see TextView#setInputType(int)
     * @param inputType the input type to set on the query text field
     *
     * @attr ref android.R.styleable#SearchView_inputType
     */
    public void setInputType(int inputType) {
        mSearchSrcTextView.setInputType(inputType);
    }

    /**
     * Returns the input type set on the query text field.
     * @return the input type
     *
     * @attr ref android.R.styleable#SearchView_inputType
     */
    public int getInputType() {
        return mSearchSrcTextView.getInputType();
    }

    /** @hide */
    @Override
    public boolean requestFocus(int direction, Rect previouslyFocusedRect) {
        // Don't accept focus if in the middle of clearing focus
        if (mClearingFocus) {
            return false;
        }
        // Check if SearchView is focusable.
        if (!isFocusable()) {
            return false;
        }
        // If it is not iconified, then give the focus to the text field
        if (!isIconified()) {
            boolean result = mSearchSrcTextView.requestFocus(direction, previouslyFocusedRect);
            if (result) {
                updateViewsVisibility(false);
            }
            return result;
        } else {
            return super.requestFocus(direction, previouslyFocusedRect);
        }
    }

    /** @hide */
    @Override
    public void clearFocus() {
        mClearingFocus = true;
        super.clearFocus();
        mSearchSrcTextView.clearFocus();
        mSearchSrcTextView.setImeVisibility(false);
        mClearingFocus = false;
    }

    /**
     * Sets a listener for user actions within the SearchView.
     *
     * @param listener the listener object that receives callbacks when the user performs
     * actions in the SearchView such as clicking on buttons or typing a query.
     */
    public void setOnQueryTextListener(SearchView.OnQueryTextListener listener) {
        mOnQueryChangeListener = listener;
    }

    /**
     * Sets a listener to inform when the user closes the SearchView.
     *
     * @param listener the listener to call when the user closes the SearchView.
     */
    public void setOnCloseListener(SearchView.OnCloseListener listener) {
        mOnCloseListener = listener;
    }

    /**
     * Sets a listener to inform when the focus of the query text field changes.
     *
     * @param listener the listener to inform of focus changes.
     */
    public void setOnQueryTextFocusChangeListener(OnFocusChangeListener listener) {
        mOnQueryTextFocusChangeListener = listener;
    }

    /**
     * Sets a listener to inform when a suggestion is focused or clicked.
     *
     * @param listener the listener to inform of suggestion selection events.
     */
    public void setOnSuggestionListener(SearchView.OnSuggestionListener listener) {
        mOnSuggestionListener = listener;
    }

    /**
     * Sets a listener to inform when used the keywords to search.
     * @param keywordsSearchListener
     */
    public void setOnKeywordsSearchListener(OnKeywordsSearchListener keywordsSearchListener) {
        mOnKeywordsSearchListener = keywordsSearchListener;
    }
    /**
     * Returns the query string currently in the text field.
     *
     * @return the query string
     */
    public CharSequence getQuery() {
        return mSearchSrcTextView.getText();
    }

    /**
     * Sets a query string in the text field and optionally submits the query as well.
     *
     * @param query the query string. This replaces any query text already present in the
     * text field.
     * @param submit whether to submit the query right now or only update the contents of
     * text field.
     */
    public void setQuery(CharSequence query, boolean submit) {
        if (!TextUtils.equals(mSearchSrcTextView.getText(), query)) {
            mSearchSrcTextView.setText(query);
        }
        if (query != null) {
            mSearchSrcTextView.setSelection(mSearchSrcTextView.length());
            mUserQuery = query;
        }

        // If the query is not empty and submit is requested, submit the query
        if (submit && !TextUtils.isEmpty(query)) {
            onSubmitQuery();
        }
    }

    /**
     * Set the keywords the search instead of query
     * @param query the query string, but only used to show on text field.
     * @param keywords the keywords which used to search, maybe it has more than one
     */
    public void setKeywords(String query, String[] keywords) {
        if ((keywords == null) || (keywords.length <= 0)) {
            return;
        }
        cleanWordLayout();
        mKeywords = keywords;
        setQuery(query, false);
    }

    /**
     * Sets the hint text to display in the query text field. This overrides
     * any hint specified in the {@link SearchableInfo}.
     * <p>
     * This value may be specified as an empty string to prevent any query hint
     * from being displayed.
     *
     * @param hint the hint text to display or {@code null} to clear
     * @attr ref android.R.styleable#SearchView_queryHint
     */
    public void setQueryHint(@Nullable CharSequence hint) {
        mQueryHint = hint;
        updateQueryHint();
    }

    /**
     * Returns the hint text that will be displayed in the query text field.
     * <p>
     * The displayed query hint is chosen in the following order:
     * <ol>
     * <li>Non-null value set with {@link #setQueryHint(CharSequence)}
     * <li>Value specified in XML using
     *     {@link R.styleable#SearchView_queryHint android:queryHint}
     * <li>Valid string resource ID exposed by the {@link SearchableInfo} via
     *     {@link SearchableInfo#getHintId()}
     * <li>Default hint provided by the theme against which the view was
     *     inflated
     * </ol>
     *
     * @return the displayed query hint text, or {@code null} if none set
     * @attr ref android.R.styleable#SearchView_queryHint
     */
    @Nullable
    public CharSequence getQueryHint() {
        final CharSequence hint;
        if (mQueryHint != null) {
            hint = mQueryHint;
        } else if ((mSearchable != null) && (mSearchable.getHintId() != 0)) {
            hint = getContext().getText(mSearchable.getHintId());
        } else {
            hint = mDefaultQueryHint;
        }
        return hint;
    }

    /**
     * Sets the default or resting state of the search field. If true, a single search icon is
     * shown by default and expands to show the text field and other buttons when pressed. Also,
     * if the default state is iconified, then it collapses to that state when the close button
     * is pressed. Changes to this property will take effect immediately.
     *
     * <p>The default value is true.</p>
     *
     * @param iconified whether the search field should be iconified by default
     *
     * @attr ref android.R.styleable#SearchView_iconifiedByDefault
     */
    public void setIconifiedByDefault(boolean iconified) {
        if (mIconifiedByDefault == iconified) {
            return;
        }
        mIconifiedByDefault = iconified;
        updateViewsVisibility(iconified);
        updateQueryHint();
    }

    /**
     * Returns the default iconified state of the search field.
     * @return
     *
     * @attr ref android.R.styleable#SearchView_iconifiedByDefault
     */
    public boolean isIconfiedByDefault() {
        return mIconifiedByDefault;
    }

    /**
     * Iconifies or expands the SearchView. Any query text is cleared when iconified. This is
     * a temporary state and does not override the default iconified state set by
     * {@link #setIconifiedByDefault(boolean)}. If the default state is iconified, then
     * a false here will only be valid until the user closes the field. And if the default
     * state is expanded, then a true here will only clear the text field and not close it.
     *
     * @param iconify a true value will collapse the SearchView to an icon, while a false will
     * expand it.
     */
    public void setIconified(boolean iconify) {
        if (iconify) {
            onCloseClicked();
        } else {
            onSearchClicked();
        }
    }

    /**
     * Returns the current iconified state of the SearchView.
     *
     * @return true if the SearchView is currently iconified, false if the search field is
     * fully visible.
     */
    public boolean isIconified() {
        return mIconified;
    }

    /**
     * Makes the view at most this many pixels wide
     *
     * @attr ref android.R.styleable#SearchView_maxWidth
     */
    public void setMaxWidth(int maxpixels) {
        mMaxWidth = maxpixels;
        requestLayout();
    }

    /**
     * Gets the specified maximum width in pixels, if set. Returns zero if
     * no maximum width was specified.
     * @return the maximum width of the view
     *
     * @attr ref android.R.styleable#SearchView_maxWidth
     */
    public int getMaxWidth() {
        return mMaxWidth;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        // Let the standard measurements take effect in iconified state.
        if (isIconified()) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
            return;
        }

        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int width = MeasureSpec.getSize(widthMeasureSpec);

        switch (widthMode) {
            case MeasureSpec.AT_MOST:
                // If there is an upper limit, don't exceed maximum width (explicit or implicit)
                if (mMaxWidth > 0) {
                    width = Math.min(mMaxWidth, width);
                } else {
                    width = Math.min(getPreferredWidth(), width);
                }
                break;
            case MeasureSpec.EXACTLY:
                // If an exact width is specified, still don't exceed any specified maximum width
                if (mMaxWidth > 0) {
                    width = Math.min(mMaxWidth, width);
                }
                break;
            case MeasureSpec.UNSPECIFIED:
                // Use maximum width, if specified, else preferred width
                width = (mMaxWidth > 0) ? mMaxWidth : getPreferredWidth();
                break;
            default:
                break;
        }
        widthMode = MeasureSpec.EXACTLY;

        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);

        switch (heightMode) {
            case MeasureSpec.AT_MOST:
                height = Math.min(getPreferredHeight(), height);
                break;
            case MeasureSpec.UNSPECIFIED:
                height = getPreferredHeight();
                break;
            default:
                break;
        }
        heightMode = MeasureSpec.EXACTLY;

        super.onMeasure(MeasureSpec.makeMeasureSpec(width, widthMode),
                MeasureSpec.makeMeasureSpec(height, heightMode));
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);

        if (changed) {
            /*
            Expand mSearchSrcTextView touch target to be the height of the parent in order to
            allow it to be up to 48dp.
            */
            getChildBoundsWithinSearchView(mSearchSrcTextView, mSearchSrcTextViewBounds);
            mSearchSrtTextViewBoundsExpanded.set(
                    mSearchSrcTextViewBounds.left, 0, mSearchSrcTextViewBounds.right, bottom - top);
            if (mTouchDelegate == null) {
                mTouchDelegate = new UpdatableTouchDelegate(mSearchSrtTextViewBoundsExpanded,
                        mSearchSrcTextViewBounds, mSearchSrcTextView);
                setTouchDelegate(mTouchDelegate);
            } else {
                mTouchDelegate.setBounds(mSearchSrtTextViewBoundsExpanded, mSearchSrcTextViewBounds);
            }
        }
    }

    private void getChildBoundsWithinSearchView(View view, Rect rect) {
        view.getLocationInWindow(mTemp);
        getLocationInWindow(mTemp2);
        final int top = mTemp[1] - mTemp2[1];
        final int left = mTemp[0] - mTemp2[0];
        rect.set(left , top, left + view.getWidth(), top + view.getHeight());
    }

    private int getPreferredWidth() {
        return getContext().getResources()
                .getDimensionPixelSize(R.dimen.search_view_preferred_width);
    }

    private int getPreferredHeight() {
        return getContext().getResources()
                .getDimensionPixelSize(R.dimen.search_view_preferred_height);
    }

    private void updateViewsVisibility(final boolean collapsed) {
        mIconified = collapsed;
        // Visibility of views that are visible when collapsed
        final int visCollapsed = collapsed ? VISIBLE : GONE;
        // Is there text in the query
        final boolean hasText = !TextUtils.isEmpty(mSearchSrcTextView.getText());

        updateSubmitButton(hasText);
        mSearchEditFrame.setVisibility(collapsed ? GONE : VISIBLE);

        updateCloseButton();
        updateVoiceButton(!hasText);
        updateSubmitArea();
    }

    private boolean hasVoiceSearch() {
        if ((mSearchable != null) && mSearchable.getVoiceSearchEnabled()) {
            Intent testIntent = null;
            if (mSearchable.getVoiceSearchLaunchWebSearch()) {
                testIntent = mVoiceWebSearchIntent;
            } else if (mSearchable.getVoiceSearchLaunchRecognizer()) {
                testIntent = mVoiceAppSearchIntent;
            }
            if (testIntent != null) {
                ResolveInfo ri = getContext().getPackageManager().resolveActivity(testIntent,
                        PackageManager.MATCH_DEFAULT_ONLY);
                return ri != null;
            }
        }
        return false;
    }

    private boolean isSubmitAreaEnabled() {
        return (mSubmitButtonEnabled || mVoiceButtonEnabled) && !isIconified();
    }

    private void updateSubmitButton(boolean hasText) {
        int visibility = GONE;
        if (mSubmitButtonEnabled && isSubmitAreaEnabled() && hasFocus()
                && (hasText || !mVoiceButtonEnabled)) {
            visibility = VISIBLE;
        }
        mGoButton.setVisibility(visibility);
    }

    private void updateSubmitArea() {
        int visibility = GONE;
        if (isSubmitAreaEnabled()
                && ((mGoButton.getVisibility() == VISIBLE)
                || (mVoiceButton.getVisibility() == VISIBLE))) {
            visibility = VISIBLE;
        }
        mSubmitArea.setVisibility(visibility);
    }

    public void updateCloseButton() {
        final boolean hasText = !TextUtils.isEmpty(mSearchSrcTextView.getText());
        /*
        Should we show the close button? It is not shown if there's no focus,
        field is not iconified by default and there is no text in it.
        */
        final boolean showClose = (hasText || (mIconifiedByDefault && !mExpandedInActionView)
                || hasWordLayout()) && mInSearching;
        mCloseButton.setVisibility(showClose ? VISIBLE : GONE);
    }

    private void postUpdateFocusedState() {
        post(mUpdateDrawableStateRunnable);
    }

    public void updateFocusedState() {
        final boolean focused = mSearchSrcTextView.hasFocus();
        final int[] stateSet = focused ? FOCUSED_STATE_SET : EMPTY_STATE_SET;
        final Drawable searchPlateBg = mSearchPlate.getBackground();
        if (searchPlateBg != null) {
            searchPlateBg.setState(stateSet);
        }
        final Drawable submitAreaBg = mSubmitArea.getBackground();
        if (submitAreaBg != null) {
            submitAreaBg.setState(stateSet);
        }
        invalidate();
    }

    @Override
    protected void onDetachedFromWindow() {
        removeCallbacks(mUpdateDrawableStateRunnable);
        super.onDetachedFromWindow();
    }

    /**
     * Called by the SuggestionsAdapter
     * @hide
     */
    /* package */void onQueryRefine(CharSequence queryText) {
        setQuery(queryText);
    }

    private final OnClickListener mOnClickListener = new OnClickListener() {

        public void onClick(View v) {
            if (v == mCloseButton) {
                onCloseClicked();
            } else if (v == mGoButton) {
                onSubmitQuery();
            } else if (v == mVoiceButton) {
                onVoiceClicked();
            } else if (v == mSearchSrcTextView) {
                forceSuggestionQuery();
            }
        }
    };

    /**
     * Handles the key down event for dealing with action keys.
     *
     * @param keyCode This is the keycode of the typed key, and is the same value as
     *        found in the KeyEvent parameter.
     * @param event The complete event record for the typed key
     *
     * @return true if the event was handled here, or false if not.
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (mSearchable == null) {
            return false;
        }

        /*
        if it's an action specified by the searchable activity, launch the
        entered query with the action key

        Can not use hidden interface.
        SearchableInfo.ActionKeyInfo actionKey = mSearchable.findActionKey(keyCode);
        if ((actionKey != null) && (actionKey.getQueryActionMsg() != null)) {
            launchQuerySearch(keyCode, actionKey.getQueryActionMsg(), mSearchSrcTextView.getText()
                    .toString());
            return true;
        }
        */

        return super.onKeyDown(keyCode, event);
    }

    public CharSequence getDecoratedHint(CharSequence hintText) {
        /*
        If the field is always expanded or we don't have a search hint icon,
        then don't add the search icon to the hint.
        */
        if (!mIconifiedByDefault) {
            return hintText;
        }

        final SpannableStringBuilder ssb = new SpannableStringBuilder("");
        ssb.append(hintText);
        return ssb;
    }

    private void updateQueryHint() {
        final CharSequence hint = getQueryHint();
        mSearchSrcTextView.setHint(getDecoratedHint((hint == null) ? "" : hint));
    }

    /**
     * Updates the auto-complete text view.
     */
    private void updateSearchAutoComplete() {
        mSearchSrcTextView.setImeOptions(mSearchable.getImeOptions());
        int inputType = mSearchable.getInputType();
        /*
        We only touch this if the input type is set up for text (which it almost certainly
        should be, in the case of search!)
        */
        if ((inputType & InputType.TYPE_MASK_CLASS) == InputType.TYPE_CLASS_TEXT) {
            /*
            The existence of a suggestions authority is the proxy for "suggestions
            are available here"
            */
            inputType &= ~InputType.TYPE_TEXT_FLAG_AUTO_COMPLETE;
            if (mSearchable.getSuggestAuthority() != null) {
                inputType |= InputType.TYPE_TEXT_FLAG_AUTO_COMPLETE;
                /*
                TYPE_TEXT_FLAG_AUTO_COMPLETE means that the text editor is performing
                auto-completion based on its own semantics, which it will present to the user
                as they type. This generally means that the input method should not show its
                own candidates, and the spell checker should not be in action. The text editor
                supplies its candidates by calling InputMethodManager.displayCompletions(),
                which in turn will call InputMethodSession.displayCompletions().
                */
                inputType |= InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS;
            }
        }
        mSearchSrcTextView.setInputType(inputType);
    }

    /**
     * Update the visibility of the voice button.  There are actually two voice search modes,
     * either of which will activate the button.
     * @param empty whether the search query text field is empty. If it is, then the other
     * criteria apply to make the voice button visible.
     */
    private void updateVoiceButton(boolean empty) {
        int visibility = GONE;
        if (mVoiceButtonEnabled && !isIconified() && empty) {
            visibility = VISIBLE;
            mGoButton.setVisibility(GONE);
        }
        mVoiceButton.setVisibility(visibility);
    }

    private final TextView.OnEditorActionListener mOnEditorActionListener = new TextView.OnEditorActionListener() {

        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            onSubmitQuery();
            return true;
        }
    };

    public void onTextChanged(CharSequence s) {
        GLog.d(LOG_TAG, "onTextChanged() called with: s = [" + s + "]");
        if (mSearchSrcTextView == null) {
            GLog.e(LOG_TAG, "onTextChanged--mSearchSrcTextView is null");
            return;
        }
        CharSequence text = mSearchSrcTextView.getText();
        String newText = s.toString();
        mUserQuery = text;
        boolean hasText = !TextUtils.isEmpty(text);
        updateSubmitButton(hasText);
        updateVoiceButton(!hasText);
        updateCloseButton();
        updateSubmitArea();

        mInSearching = (!(mOldQueryText.toString().trim().equals(newText.trim())) || (mWords == null) || mWords.isEmpty());

        StringBuilder idStrBuilder = new StringBuilder();
        if (mWords != null) {
            mWords.forEach(item -> idStrBuilder.append(item));
            if (newText.trim().equals(idStrBuilder.toString())) {
                mInSearching = false;
            }
        }
        if ((mOnKeywordsSearchListener != null) && (mKeywords != null) && (mKeywords.length > 0)) {
            mOnKeywordsSearchListener.onKeywordsSearch(newText, mKeywords);
            //the keywords search only perform once, so we need to reset it here;
            mKeywords = null;
        } else if ((mOnQueryChangeListener != null) && (newText.isEmpty() || !TextUtils.equals(newText, mOldQueryText))) {
            mOnQueryChangeListener.onQueryTextChange(newText);
        }
        mOldQueryText = newText;
        if (TextUtils.isEmpty(newText)) {
            mSearchSrcTextView.setHint(mQueryHint);
        }
    }

    public boolean isInSearching() {
        return mInSearching;
    }

    public CharSequence getOldQueryText() {
        return mOldQueryText;
    }

    public void onSubmitQuery() {
        CharSequence query = mSearchSrcTextView.getText();
        if ((query != null) && (TextUtils.getTrimmedLength(query) > 0)) {
            if ((mOnQueryChangeListener == null)
                    || !mOnQueryChangeListener.onQueryTextSubmit(query.toString())) {
                if (mSearchable != null) {
                    launchQuerySearch(KeyEvent.KEYCODE_UNKNOWN, null, query.toString());
                }
                mSearchSrcTextView.setImeVisibility(false);
            }
        }
    }

    public void onCloseClicked() {
        CharSequence text = mSearchSrcTextView.getText();
        if (TextUtils.isEmpty(text)) {
            if (mIconifiedByDefault) {
                // If the app doesn't override the close behavior
                if ((mOnCloseListener == null) || !mOnCloseListener.onClose()) {
                    // hide the keyboard and remove focus
                    clearFocus();
                    // collapse the search field
                    updateViewsVisibility(true);
                }
            }
        } else {
            mSearchSrcTextView.setText("");
            mSearchSrcTextView.requestFocus();
            mSearchSrcTextView.setImeVisibility(true);
        }
        cleanSearchView();
    }

    public void onSearchClicked() {
        updateViewsVisibility(false);
        mSearchSrcTextView.requestFocus();
        mSearchSrcTextView.setImeVisibility(true);
        if (mOnSearchClickListener != null) {
            mOnSearchClickListener.onSearchViewClick();
        }
    }

    public void onVoiceClicked() {
        // guard against possible race conditions
        if (mSearchable == null) {
            return;
        }
        SearchableInfo searchable = mSearchable;
        try {
            if (searchable.getVoiceSearchLaunchWebSearch()) {
                Intent webSearchIntent = createVoiceWebSearchIntent(mVoiceWebSearchIntent,
                        searchable);
                getContext().startActivity(webSearchIntent);
            } else if (searchable.getVoiceSearchLaunchRecognizer()) {
                Intent appSearchIntent = createVoiceAppSearchIntent(mVoiceAppSearchIntent,
                        searchable);
                getContext().startActivity(appSearchIntent);
            }
        } catch (ActivityNotFoundException e) {
            /*
            Should not happen, since we check the availability of
            voice search before showing the button. But just in case...
            */
            GLog.e(LOG_TAG, "Could not find voice search activity");
        }
    }

    void onTextFocusChanged() {
        updateViewsVisibility(isIconified());
        /*
        Delayed update to make sure that the focus has settled down and window focus changes
        don't affect it. A synchronous update was not working.
        */
        postUpdateFocusedState();
    }

    @Override
    public void onWindowFocusChanged(boolean hasWindowFocus) {
        super.onWindowFocusChanged(hasWindowFocus);

        postUpdateFocusedState();
    }

    static class SavedState extends BaseSavedState {
        boolean mIsIconified;

        SavedState(Parcelable superState) {
            super(superState);
        }

        public SavedState(Parcel source) {
            super(source);
            mIsIconified = (Boolean) source.readValue(null);
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            super.writeToParcel(dest, flags);
            dest.writeValue(mIsIconified);
        }

        @Override
        public String toString() {
            return "SearchView.SavedState{"
                    + Integer.toHexString(System.identityHashCode(this))
                    + " isIconified=" + mIsIconified + "}";
        }

        public static final Creator<SavedState> CREATOR =
                new Creator<SavedState>() {
                    public SavedState createFromParcel(Parcel in) {
                        return new SavedState(in);
                    }

                    public SavedState[] newArray(int size) {
                        return new SavedState[size];
                    }
                };
    }

    @Override
    protected Parcelable onSaveInstanceState() {
        Parcelable superState = super.onSaveInstanceState();
        SavedState ss = new SavedState(superState);
        ss.mIsIconified = isIconified();
        return ss;
    }

    @Override
    protected void onRestoreInstanceState(Parcelable state) {
        SavedState ss = (SavedState) state;
        super.onRestoreInstanceState(ss.getSuperState());
        updateViewsVisibility(ss.mIsIconified);
        requestLayout();
    }

    @Override
    public CharSequence getAccessibilityClassName() {
        return SearchView.class.getName();
    }

    /**
     * Sets the text in the query box, without updating the suggestions.
     */
    private void setQuery(CharSequence query) {
        mSearchSrcTextView.setSelection(TextUtils.isEmpty(query) ? 0 : query.length());
    }

    private void launchQuerySearch(int actionKey, String actionMsg, String query) {
        String action = Intent.ACTION_SEARCH;
        Intent intent = createIntent(action, null, null, query, actionKey, actionMsg);
        getContext().startActivity(intent);
    }

    /**
     * Constructs an intent from the given information and the search dialog state.
     *
     * @param action Intent action.
     * @param data Intent data, or <code>null</code>.
     * @param extraData Data for {@link SearchManager#EXTRA_DATA_KEY} or <code>null</code>.
     * @param query Intent query, or <code>null</code>.
     * @param actionKey The key code of the action key that was pressed,
     *        or {@link KeyEvent#KEYCODE_UNKNOWN} if none.
     * @param actionMsg The message for the action key that was pressed,
     *        or <code>null</code> if none.
     * @param {@link SearchManager#SEARCH_MODE}, or {@code null}.
     * @return The intent.
     */
    private Intent createIntent(String action, Uri data, String extraData, String query,
                                int actionKey, String actionMsg) {
        // Now build the Intent
        Intent intent = new Intent(action);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        /*
        We need CLEAR_TOP to avoid reusing an old task that has other activities
        on top of the one we want. We don't want to do this in in-app search though,
        as it can be destructive to the activity stack.
        */
        if (data != null) {
            intent.setData(data);
        }
        intent.putExtra(SearchManager.USER_QUERY, mUserQuery);
        if (query != null) {
            intent.putExtra(SearchManager.QUERY, query);
        }
        if (extraData != null) {
            intent.putExtra(SearchManager.EXTRA_DATA_KEY, extraData);
        }
        if (mAppSearchData != null) {
            intent.putExtra(SearchManager.APP_DATA, mAppSearchData);
        }
        if (actionKey != KeyEvent.KEYCODE_UNKNOWN) {
            intent.putExtra(SearchManager.ACTION_KEY, actionKey);
            intent.putExtra(SearchManager.ACTION_MSG, actionMsg);
        }
        intent.setComponent(mSearchable.getSearchActivity());
        return intent;
    }

    /**
     * Create and return an Intent that can launch the voice search activity for web search.
     */
    private Intent createVoiceWebSearchIntent(Intent baseIntent, SearchableInfo searchable) {
        Intent voiceIntent = new Intent(baseIntent);
        ComponentName searchActivity = searchable.getSearchActivity();
        voiceIntent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, (searchActivity == null) ? null
                : searchActivity.flattenToShortString());
        return voiceIntent;
    }

    /**
     * Create and return an Intent that can launch the voice search activity, perform a specific
     * voice transcription, and forward the results to the searchable activity.
     *
     * @param baseIntent The voice app search intent to start from
     * @return A completely-configured intent ready to send to the voice search activity
     */
    @SuppressLint("PendingIntentDetector")
    private Intent createVoiceAppSearchIntent(Intent baseIntent, SearchableInfo searchable) {
        ComponentName searchActivity = searchable.getSearchActivity();
        /*
        create the necessary intent to set up a search-and-forward operation
        in the voice search system.   We have to keep the bundle separate,
        because it becomes immutable once it enters the PendingIntent
        */
        Intent queryIntent = new Intent(Intent.ACTION_SEARCH);
        queryIntent.setComponent(searchActivity);
        PendingIntent pending = PendingIntent.getActivity(getContext(), 0, queryIntent,
                PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE);
        /*
        Now set up the bundle that will be inserted into the pending intent
        when it's time to do the search.  We always build it here (even if empty)
        because the voice search activity will always need to insert "QUERY" into
        it anyway.
        */
        Bundle queryExtras = new Bundle();
        if (mAppSearchData != null) {
            queryExtras.putParcelable(SearchManager.APP_DATA, mAppSearchData);
        }
        /*
        Now build the intent to launch the voice search.  Add all necessary
        extras to launch the voice recognizer, and then all the necessary extras
        to forward the results to the searchable activity
        */
        Intent voiceIntent = new Intent(baseIntent);

        // Add all of the configuration options supplied by the searchable's metadata
        String languageModel = RecognizerIntent.LANGUAGE_MODEL_FREE_FORM;
        String prompt = null;
        String language = null;
        int maxResults = 1;

        Resources resources = getResources();
        if (searchable.getVoiceLanguageModeId() != 0) {
            languageModel = resources.getString(searchable.getVoiceLanguageModeId());
        }
        if (searchable.getVoicePromptTextId() != 0) {
            prompt = resources.getString(searchable.getVoicePromptTextId());
        }
        if (searchable.getVoiceLanguageId() != 0) {
            language = resources.getString(searchable.getVoiceLanguageId());
        }
        if (searchable.getVoiceMaxResults() != 0) {
            maxResults = searchable.getVoiceMaxResults();
        }
        voiceIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, languageModel);
        voiceIntent.putExtra(RecognizerIntent.EXTRA_PROMPT, prompt);
        voiceIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, language);
        voiceIntent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, maxResults);
        voiceIntent.putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, (searchActivity == null) ? null
                : searchActivity.flattenToShortString());

        // Add the values that configure forwarding the results
        voiceIntent.putExtra(RecognizerIntent.EXTRA_RESULTS_PENDINGINTENT, pending);
        voiceIntent.putExtra(RecognizerIntent.EXTRA_RESULTS_PENDINGINTENT_BUNDLE, queryExtras);

        return voiceIntent;
    }

    public void forceSuggestionQuery() {
        /*mSearchSrcTextView.doBeforeTextChanged();
        mSearchSrcTextView.doAfterTextChanged();*/
    }

    static boolean isLandscapeMode(Context context) {
        return context.getResources().getConfiguration().orientation
                == Configuration.ORIENTATION_LANDSCAPE;
    }
    /**
     * Callback to watch the text field for empty/non-empty
     */
    private TextWatcher mTextWatcher = new TextWatcher() {

        public void beforeTextChanged(CharSequence s, int start, int before, int after) {
        }

        public void onTextChanged(CharSequence s, int start,
                                  int before, int after) {
            GLog.d(LOG_TAG, "onTextChanged() called with: s = [" + s + "], start = [" + start + "], before = [" + before + "], after = [" + after + "]");
            if (mIsNormalFlat) {
                CharSequence text = getTextChange(s);
                SearchView.this.onTextChanged(text);
            }
            updateCloseButton();
        }

        public void afterTextChanged(Editable s) {
            mIsNormalFlat = true;
        }
    };

    private OnKeyListener mKeyListener = new OnKeyListener() {
        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if (mSearchSrcTextView != null) {
                int start = mSearchSrcTextView.getSelectionStart();
                int end = mSearchSrcTextView.getSelectionEnd();
                if ((keyCode == KeyEvent.KEYCODE_DEL)
                        && (event.getAction() == KeyEvent.ACTION_DOWN)
                        && (start == 0) && (end == 0)) {
                    removeSearchView();
                    return true;
                }
            }
            return false;
        }
    };

    private static class UpdatableTouchDelegate extends TouchDelegate {
        /**
         * View that should receive forwarded touch events
         */
        private final View mDelegateView;

        /**
         * Bounds in local coordinates of the containing view that should be mapped to the delegate
         * view. This rect is used for initial hit testing.
         */
        private final Rect mTargetBounds;

        /**
         * Bounds in local coordinates of the containing view that are actual bounds of the delegate
         * view. This rect is used for event coordinate mapping.
         */
        private final Rect mActualBounds;

        /**
         * mTargetBounds inflated to include some slop. This rect is to track whether the motion events
         * should be considered to be be within the delegate view.
         */
        private final Rect mSlopBounds;

        private final int mSlop;

        /**
         * True if the delegate had been targeted on a down event (intersected mTargetBounds).
         */
        private boolean mDelegateTargeted;

        public UpdatableTouchDelegate(Rect targetBounds, Rect actualBounds, View delegateView) {
            super(targetBounds, delegateView);
            mSlop = ViewConfiguration.get(delegateView.getContext()).getScaledTouchSlop();
            mTargetBounds = new Rect();
            mSlopBounds = new Rect();
            mActualBounds = new Rect();
            setBounds(targetBounds, actualBounds);
            mDelegateView = delegateView;
        }

        public void setBounds(Rect desiredBounds, Rect actualBounds) {
            mTargetBounds.set(desiredBounds);
            mSlopBounds.set(desiredBounds);
            mSlopBounds.inset(-mSlop, -mSlop);
            mActualBounds.set(actualBounds);
        }

        @Override
        public boolean onTouchEvent(MotionEvent event) {
            final int x = (int) event.getX();
            final int y = (int) event.getY();
            boolean sendToDelegate = false;
            boolean hit = true;
            boolean handled = false;

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    if (mTargetBounds.contains(x, y)) {
                        mDelegateTargeted = true;
                        sendToDelegate = true;
                    }
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_MOVE:
                    sendToDelegate = mDelegateTargeted;
                    if (sendToDelegate) {
                        if (!mSlopBounds.contains(x, y)) {
                            hit = false;
                        }
                    }
                    break;
                case MotionEvent.ACTION_CANCEL:
                    sendToDelegate = mDelegateTargeted;
                    mDelegateTargeted = false;
                    break;
                default:
                    break;
            }
            if (sendToDelegate) {
                if (hit && !mActualBounds.contains(x, y)) {
                    /*
                    Offset event coordinates to be in the center of the target view since we
                    are within the targetBounds, but not inside the actual bounds of
                    mDelegateView
                    */
                    event.setLocation(mDelegateView.getWidth() / 2,
                            mDelegateView.getHeight() / 2);
                } else {
                    // Offset event coordinates to the target view coordinates.
                    event.setLocation(x - mActualBounds.left, y - mActualBounds.top);
                }

                handled = mDelegateView.dispatchTouchEvent(event);
            }
            return handled;
        }
    }

    private void removeSearchView() {
        if (mSearchSrcTextView == null) {
            GLog.e(LOG_TAG, "removeSeachView--mSearchSrcTextView is null");
            return;
        }
        if (mOnWordDeleteListener != null) {
            mOnWordDeleteListener.onWordDelete();
        }
        if (mSearchWordLayout.getChildCount() > 0) {
            mSearchWordLayout.removeViewAt(mSearchWordLayout.getChildCount() - 1);
        }
        if ((mWords != null) && (mWords.size() > 0)) {
            mWords.remove(mWords.size() - 1);
        }
        CharSequence text = getTextChange(mSearchSrcTextView.getText());
        SearchView.this.onTextChanged(text);
        mIsNormalFlat = true;
    }

    public void cleanSearchView() {
        GLog.d(LOG_TAG, "cleanSearchView: ");
        if (mSearchSrcTextView != null) {
            mOnKeywordsSearchListener = null;
            mKeywords = null;
            cleanWordLayout();
            mSearchSrcTextView.setText("");
            mIsNormalFlat = true;
            updateCloseButton();
        }
    }

    public boolean hasWordLayout() {
        if (mSearchWordLayout == null) {
            GLog.e(LOG_TAG, "hasWordLayout--mSearchWordLayout is null ");
            return false;
        }
        return (mSearchWordLayout.getChildCount() > 0);
    }

    public boolean hasInputWord() {
        if (mSearchSrcTextView == null) {
            GLog.e(LOG_TAG, "hasInputWord--mSearchSrcTextView is null ");
            return false;
        }
        Editable editText = mSearchSrcTextView.getText();
        return ((editText != null) && (editText.length() > 0));
    }

    /**
     * @return  the contain in EditText
     */
    public String getEditTextString() {
        if (mSearchSrcTextView == null) {
            GLog.e(LOG_TAG, "getEditTextString--mSearchSrcTextView is null");
            return "";
        }

        return getTextChange(mSearchSrcTextView.getText()).toString();
    }

    public CharSequence getTextChange(CharSequence newText) {
        StringBuilder sb = new StringBuilder();
        if (mSearchWordLayout != null) {
            for (int i = 0; i < mSearchWordLayout.getChildCount(); i++) {
                TextView textView = (TextView) mSearchWordLayout.getChildAt(i);
                if (textView != null) {
                    CharSequence text = textView.getText();
                    sb.append(text);
                }
            }
        }

        if (!TextUtils.isEmpty(newText)) {
            if (sb.length() > 0) {
                sb.append(SPACE);
            }
            sb.append(newText);
        }
        return sb;
    }

    public void cleanWordLayout() {
        if (mSearchWordLayout != null) {
            GLog.e(LOG_TAG, "cleanWordLayout--record： "
                    + mSearchWordLayout.getChildCount());
            if (mSearchWordLayout.getChildCount() > 0) {
                if (mOnWordDeleteListener != null) {
                    mOnWordDeleteListener.onWordDelete();
                }
                mSearchWordLayout.removeAllViews();
                mIsNormalFlat = true;
                mSearchSrcTextView.setHint(mQueryHint);
                mWords = null;
            }
        }
        mKeywordItemMap.clear();
    }

    public void addWordLayout(List<CharSequence> words) {
        Context context = getContext();
        if ((context == null) || (words == null) || (words.isEmpty())) {
            GLog.e(LOG_TAG, "addWordLayout--words or context or layout is empty");
            return;
        }
        mInSearching = false;
        cleanWordLayout();
        mWords = new ArrayList<>(words);
        for (CharSequence word : words) {
            if (TextUtils.isEmpty(word)) {
                continue;
            }
            final TextView textView = new TextView(context);
            textView.setForceDarkAllowed(false);
            textView.setText(word);
            textView.setGravity(Gravity.CENTER);
            textView.setTextAppearance(com.oplus.gallery.foundation.ui.R.style.gTextAppearanceDescription);
            textView.setTextColor(COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPrimaryNeutral));
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, mWordHintTextSize);

            LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            params.setMarginEnd(mWordMarginRight);
            textView.setLayoutParams(params);

            textView.setMaxWidth(mWordMaxWidth);
            textView.setSingleLine();
            textView.setEllipsize(TextUtils.TruncateAt.END);
            textView.setHeight(mWordHeight);
            textView.setPaddingRelative(mWordPaddingLR, mWordPaddingBT, mWordPaddingLR, mWordPaddingBT);
            textView.setBackgroundResource(R.drawable.search_word_text_background);
            KeywordItem keywordItem = new KeywordItem(word);
            mKeywordItemMap.put(word.toString(), keywordItem);

            Drawable clearDrawable = ResourcesCompat.getDrawable(getResources(), R.drawable.search_ic_search_item_clear, context.getTheme());
            if (clearDrawable != null) {
                clearDrawable.setBounds(0, 0, clearDrawable.getIntrinsicWidth(), clearDrawable.getIntrinsicHeight());
                keywordItem.mClearDrawableIntrinsicWidth = clearDrawable.getIntrinsicWidth();
                textView.setCompoundDrawablePadding(mWordPaddingToClearButton);
                textView.setCompoundDrawablesRelative(null, null, clearDrawable, null);
            }
            setWordOnTouchListener(textView, word);

            mSearchWordLayout.addView(textView);
        }
        setWordLayoutState();
    }

    private void setWordOnTouchListener(TextView textView, CharSequence word) {
        COUIPressFeedbackHelper feedbackHelper = new COUIPressFeedbackHelper(textView, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK);
        textView.setOnTouchListener((v, event) -> {
            FeedbackAnimatorHelperKt.enablePressFeedback(feedbackHelper, event);
            if (event.getAction() == MotionEvent.ACTION_UP) {
                KeywordItem clickedKeywordItem = mKeywordItemMap.get(word.toString());
                if ((clickedKeywordItem != null)) {
                    boolean clickDelete = false;
                    int drawableWidth = clickedKeywordItem.mClearDrawableIntrinsicWidth + mWordPaddingLR + mWordPaddingToClearButton;
                    if (getResources().getConfiguration().getLayoutDirection() == LAYOUT_DIRECTION_RTL) {
                        if (event.getX() <= drawableWidth) {
                            clickDelete = true;
                        }
                    } else {
                        if (event.getX() >= (v.getWidth() - drawableWidth)) {
                            clickDelete = true;
                        }
                    }
                    if (clickDelete) {
                        mKeywordItemMap.remove(word.toString());
                        mSearchWordLayout.removeView(v);
                        mWords.remove(word);
                        if (mOnWordDeleteListener != null) {
                            mOnWordDeleteListener.onWordDelete();
                        }
                        SearchView.this.onTextChanged(getEditTextString());
                        return true;
                    }
                }
            }
            return true;
        });
    }

    private boolean hasSameWordInLayout(String word) {
        if (mSearchWordLayout != null) {
            for (int i = 0; i < mSearchWordLayout.getChildCount(); i++) {
                TextView textView = (TextView) mSearchWordLayout.getChildAt(i);
                if (textView != null) {
                    String text = textView.getText().toString();
                    if (word.equalsIgnoreCase(text)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public void addWordLayout(String word) {
        if ((mSearchWordLayout == null) || TextUtils.isEmpty(word.trim()) || hasSameWordInLayout(word)) {
            GLog.e(LOG_TAG, "addWordLayout--mSearchWordLayout: " + mSearchSrcTextView + " word: " + word);
            return;
        }
        ArrayList<CharSequence> words = new ArrayList<>();
        words.add(word);
        addWordLayout(words);
    }

    private void setWordLayoutState() {
        GLog.d(LOG_TAG, "setWordLayoutState: ");
        mIsNormalFlat = false;
        mSearchSrcTextView.setText("");
        mSearchSrcTextView.setHint("");
    }

    public static class SearchAutoComplete extends AppCompatAutoCompleteTextView {
        private static final int VALUE_INT_960 = 960;
        private static final int VALUE_INT_720 = 720;
        private static final int VALUE_INT_256 = 256;
        private static final int VALUE_INT_600 = 600;
        private static final int VALUE_INT_192 = 192;
        private static final int VALUE_INT_160 = 160;
        private int mThreshold;
        private SearchView mSearchView;

        private boolean mHasPendingShowSoftInputRequest;
        private final Runnable mRunShowSoftInputIfNecessary = new Runnable() {
            @Override
            public void run() {
                showSoftInputIfNecessary();
            }
        };

        public SearchAutoComplete(Context context) {
            super(context);
            mThreshold = getThreshold();
        }

        public SearchAutoComplete(Context context, AttributeSet attrs) {
            super(context, attrs);
            mThreshold = getThreshold();
        }

        public SearchAutoComplete(Context context, AttributeSet attrs, int defStyleAttrs) {
            super(context, attrs, defStyleAttrs);
            mThreshold = getThreshold();
        }

        @Override
        protected void onFinishInflate() {
            super.onFinishInflate();
            DisplayMetrics metrics = getResources().getDisplayMetrics();
            setMinWidth((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                    getSearchViewTextMinWidthDp(), metrics));
        }

        public void setSearchView(SearchView searchView) {
            mSearchView = searchView;
        }

        @Override
        public void setThreshold(int threshold) {
            super.setThreshold(threshold);
            mThreshold = threshold;
        }

        /**
         * Returns true if the text field is empty, or contains only whitespace.
         */
        private boolean isEmpty() {
            return TextUtils.getTrimmedLength(getText()) == 0;
        }

        /**
         * We override this method to avoid replacing the query box text when a
         * suggestion is clicked.
         */
        @Override
        protected void replaceText(CharSequence text) {
        }

        /**
         * We override this method to avoid an extra onItemClick being called on
         * the drop-down's OnItemClickListener by
         * {@link AutoCompleteTextView#onKeyUp(int, KeyEvent)} when an item is
         * clicked with the trackball.
         */
        @Override
        public void performCompletion() {
        }

        /**
         * We override this method to be sure and show the soft keyboard if
         * appropriate when the TextView has focus.
         */
        @Override
        public void onWindowFocusChanged(boolean hasWindowFocus) {
            super.onWindowFocusChanged(hasWindowFocus);

            if (hasWindowFocus && mSearchView.hasFocus() && (getVisibility() == VISIBLE)) {
                mHasPendingShowSoftInputRequest = true;

                // If in landscape mode, then make sure that the ime is in front of the dropdown.
                if (isLandscapeMode(getContext())) {
//                    ensureImeVisible(true);
                }
            }
        }

        @Override
        protected void onFocusChanged(boolean focused, int direction, Rect previouslyFocusedRect) {
            super.onFocusChanged(focused, direction, previouslyFocusedRect);
            mSearchView.onTextFocusChanged();
        }

        /**
         * We override this method so that we can allow a threshold of zero,
         * which ACTV does not.
         */
        @Override
        public boolean enoughToFilter() {
            return (mThreshold <= 0) || super.enoughToFilter();
        }

        @Override
        public boolean onKeyPreIme(int keyCode, KeyEvent event) {
            if (keyCode == KeyEvent.KEYCODE_BACK) {
                /*
                special case for the back key, we do not even try to send it
                to the drop down list but instead, consume it immediately
                */
                if ((event.getAction() == KeyEvent.ACTION_DOWN) && (event.getRepeatCount() == 0)) {
                    KeyEvent.DispatcherState state = getKeyDispatcherState();
                    if (state != null) {
                        state.startTracking(event, this);
                    }
                    return true;
                } else if (event.getAction() == KeyEvent.ACTION_UP) {
                    KeyEvent.DispatcherState state = getKeyDispatcherState();
                    if (state != null) {
                        state.handleUpEvent(event);
                    }
                    if (event.isTracking() && !event.isCanceled()) {
                        mSearchView.clearFocus();
                        setImeVisibility(false);
                        return true;
                    }
                }
            }
            return super.onKeyPreIme(keyCode, event);
        }

        /**
         * Get minimum width of the search view text entry area.
         */
        private int getSearchViewTextMinWidthDp() {
            final Configuration configuration = getResources().getConfiguration();
            final int width = ScreenUtils.getScreenWidthDp(getContext());
            final int height = ScreenUtils.getScreenHeightDp(getContext());
            final int orientation = configuration.orientation;
            if ((width >= VALUE_INT_960) && (height >= VALUE_INT_720)
                    && (orientation == Configuration.ORIENTATION_LANDSCAPE)) {
                return VALUE_INT_256;
            } else if (width >= VALUE_INT_600) {
                return VALUE_INT_192;
            }
            return VALUE_INT_160;
        }

        /**
         * We override {@link View#onCreateInputConnection(EditorInfo)} as a signal to schedule a
         * pending {@link InputMethodManager#showSoftInput(View, int)} request (if any).
         */
        @Override
        public InputConnection onCreateInputConnection(EditorInfo editorInfo) {
            final InputConnection ic = super.onCreateInputConnection(editorInfo);
            if (mHasPendingShowSoftInputRequest) {
                removeCallbacks(mRunShowSoftInputIfNecessary);
                post(mRunShowSoftInputIfNecessary);
            }
            return ic;
        }

        private void showSoftInputIfNecessary() {
            if (mHasPendingShowSoftInputRequest) {
                final InputMethodManager imm =
                        getContext().getSystemService(InputMethodManager.class);
                if (imm != null) {
                    imm.showSoftInput(this, 0);
                    mHasPendingShowSoftInputRequest = false;
                }
            }
        }

        private void setImeVisibility(final boolean visible) {
            final InputMethodManager imm = getContext().getSystemService(InputMethodManager.class);
            if (imm == null) {
                GLog.e(LOG_TAG, "setImeVisibility--getSystemService is null");
                return;
            }
            if (!visible) {
                mHasPendingShowSoftInputRequest = false;
                removeCallbacks(mRunShowSoftInputIfNecessary);
                imm.hideSoftInputFromWindow(getWindowToken(), 0);
                return;
            }

            if (imm.isActive(this)) {
                /*
                This means that SearchAutoComplete is already connected to the IME.
                InputMethodManager#showSoftInput() is guaranteed to pass client-side focus check.
                */
                mHasPendingShowSoftInputRequest = false;
                removeCallbacks(mRunShowSoftInputIfNecessary);
                imm.showSoftInput(this, 0);
                return;
            }
            mHasPendingShowSoftInputRequest = true;
        }
    }

    private boolean mIsHintTextSize = true;

    private OnSearchViewClickListener mOnSearchViewClickListener = null;

    public void setOnSearchViewClickListener(OnSearchViewClickListener listener) {
        mOnSearchViewClickListener = listener;
    }

    public AutoCompleteTextView getSearchAutoComplete() {
        return mSearchSrcTextView;
    }

    public void setOnWordDeleteListener(SearchActivity.OnWordDeleteListener onWordDeleteListener) {
        mOnWordDeleteListener = onWordDeleteListener;
    }

    public interface OnSearchViewClickListener {
        void onSearchViewClick();
    }

    public void setImeVisibility(boolean visible) {
        if (mSearchSrcTextView != null) {
            mSearchSrcTextView.setImeVisibility(false);
        }
    }

    public void updateSearchKeyword(String oldText, String newText) {
        if (mWords != null) {
            for (int i = 0; i < mWords.size(); i++) {
                if (mWords.get(i).equals(oldText)) {
                    mWords.set(i, newText);
                    break;
                }
            }
            addWordLayout(mWords);
        }
    }

    public List<CharSequence> getWords() {
        return mWords;
    }

    /**
     * 获取是否是普通模式搜索
     *
     * @return 是否是普通模式的搜索  true-是  false-多个搜索词模式
     */
    public boolean isNormalFlat() {
        if (mSearchWordLayout != null) {
            return mSearchWordLayout.getChildCount() == 0;
        }
        return false;
    }

    private class KeywordItem {
        public CharSequence mText;
        public int mClearDrawableIntrinsicWidth;

        KeywordItem(CharSequence text) {
            mText = text;
        }

        @Override
        public String toString() {
            return "KeywordItem: text = " + mText;
        }
    }
}
