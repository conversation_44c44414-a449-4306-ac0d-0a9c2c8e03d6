/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * COLOR_OS
 * * File        :  - GallerySearchLayout.java
 * * Description : for Searching
 * * Version     : 1.0
 * * Date        : 2019/01/22
 * * Author      : yo<PERSON><PERSON><PERSON>@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  yongzhan@Apps.Gallery3D       2019/01/22   1.0     build this module
 ****************************************************************/
package com.oplus.gallery.searchpage.ui;

import static com.oplus.gallery.searchpage.SearchActivity.ENTER_ANIMATE_DURATION;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.IntDef;

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.display.DisplayUtils;
import com.oplus.gallery.framework.abilities.config.keys.ConfigID;
import com.oplus.gallery.searchpage.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜索框控件，参考support，由于需要处理词包，所以只能自定义，删除了很多无用的代码
 */
public class GallerySearchLayout extends LinearLayout {
    private static final String TAG = "GallerySearchLayout";

    /**
     * 普通样式，未弹出输入框
     */
    public static final int STATE_NORMAL = 0;

    /**
     * 输入内容的样式，弹出了输入框
     */
    public static final int STATE_INPUT = 1;

    private static final float DURATION_DELAY_FACTOR = 0.5f;

    /**
     * current state {@link SearchViewState}
     */
    private int mState = STATE_NORMAL;
    /**
     * search icon in normal frame
     */
    private ImageView mSearchIcon;

    /**
     * real search view , in editable frame
     */
    private SearchView mSearchView;

    /**
     * cancel button to exit from editable frame
     */
    private TextView mCancelButton;

    /**
     * minimum height of this view
     */
    private int mMinHeightSize;

    private TextView mInputTextView;

    private List<OnCancelButtonClickListener> mOnCancelButtonClickListeners;

    private int mGravity = Gravity.CENTER_VERTICAL;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({STATE_NORMAL, STATE_INPUT})
    @interface SearchViewState {
    }

    /**
     * listener related to "cancel" text button on edit frame
     */
    public interface OnCancelButtonClickListener {
        /**
         * result means whether intercept click event to dispatch
         *
         * @return true will intercept event, well false won't do
         */
        boolean onClickCancel();
    }

    public GallerySearchLayout(Context context) {
        this(context, null);
    }

    public GallerySearchLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0, 0);
    }

    public GallerySearchLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public GallerySearchLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        inflateView(context, attrs);
        loadAttr(context, attrs, defStyleAttr, defStyleRes);
        mMinHeightSize = getResources().getDimensionPixelSize(R.dimen.search_min_height);
        mMinHeightSize = Math.max(getMinimumHeight(), mMinHeightSize);
    }

    private void inflateView(Context context, AttributeSet attrs) {
        inflate(context, R.layout.search_view_animate_layout, this);
        mSearchIcon = findViewById(R.id.animated_search_icon);
        mSearchView = findViewById(R.id.animated_search_view);
        mInputTextView = findViewById(R.id.tv_input_animate);
        mCancelButton = findViewById(R.id.animated_cancel_button);
        mCancelButton.setOnClickListener(view -> {
            notifyCancelButton();
        });
        setSearchAutoCompleteUnFocus();
    }

    private void loadAttr(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {

        TypedArray ta = context.obtainStyledAttributes(attrs, com.support.toolbar.R.styleable.COUISearchViewAnimate, defStyleAttr, defStyleRes);

        try {
            int textSize = getResources().getDimensionPixelSize(R.dimen.search_view_input_text_size);
            textSize = ta.getDimensionPixelSize(com.support.toolbar.R.styleable.COUISearchViewAnimate_inputTextSize, textSize);
            mSearchView.getSearchAutoComplete().setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);

            AutoCompleteTextView searchAutoComplete = mSearchView.getSearchAutoComplete();
            searchAutoComplete.setForceDarkAllowed(false);

            int textColor = getResources().getColor(R.color.search_view_input_text_color, null);
            textColor = ta.getColor(com.support.toolbar.R.styleable.COUISearchViewAnimate_inputTextColor, textColor);
            searchAutoComplete.setTextColor(DisplayUtils.getLightColorWithNightMode(context, textColor));

            int hintTextColor = getResources().getColor(com.support.appcompat.R.color.coui_color_hint_neutral, null);
            hintTextColor = ta.getColor(com.support.toolbar.R.styleable.COUISearchViewAnimate_inputHintTextColor, hintTextColor);
            searchAutoComplete.setHintTextColor(DisplayUtils.getLightColorWithNightMode(context, hintTextColor));
            searchAutoComplete.setTextCursorDrawable(R.drawable.search_text_cursor);

            int gravity = ta.getInt(com.support.toolbar.R.styleable.COUISearchViewAnimate_android_gravity, Gravity.CENTER_VERTICAL);
            GLog.i(TAG, null, () -> "loadAttr--gravity: " + gravity);
            setGravity(gravity);
        } catch (Resources.NotFoundException e) {
            GLog.e(TAG,null, () -> "[loadAttr] e = " + e);
        }

        ta.recycle();
    }

    @Override
    public void setGravity(int gravity) {
        int tmpGravity = gravity;
        if (mGravity != tmpGravity) {
            if ((tmpGravity & Gravity.RELATIVE_HORIZONTAL_GRAVITY_MASK) == 0) {
                tmpGravity |= Gravity.START;
            }

            if ((tmpGravity & Gravity.VERTICAL_GRAVITY_MASK) == 0) {
                tmpGravity |= Gravity.CENTER_VERTICAL;
            }

            mGravity = tmpGravity;
        }
    }

    @Override
    public int getGravity() {
        return mGravity;
    }

    public int getMinHeight() {
        return mMinHeightSize;
    }

    @Override
    public void addView(View child) {
        super.addView(child);
    }

    public void addOnCancelButtonClickListener(OnCancelButtonClickListener mOnCancelButtonClickListener) {
        if (mOnCancelButtonClickListeners == null) {
            mOnCancelButtonClickListeners = new ArrayList<>();
        }
        mOnCancelButtonClickListeners.add(mOnCancelButtonClickListener);
    }

    /**
     * 立即切换不同状态的view的效果，包括键盘的弹出和收起
     * 只处理view的效果，具体业务逻辑自行处理
     *
     * @param targetState
     */
    public void changeStateImmediately(@SearchViewState final int targetState) {
        GLog.d(TAG, null, () -> "changeStateImmediately--targetState: " + targetState + "  mState:" + mState);
        if (mState == targetState) {
            return;
        }
        mState = targetState;
        mCancelButton.setAlpha(1f);
        if (targetState == STATE_INPUT) {
            mSearchView.setVisibility(VISIBLE);
            /**
             * 背景bug：7305222，使用手写笔在相册搜索栏里写字无反应
             * 由于进入页面时，有动画，所以一开始不可见，此时attachToWindow时，mPrivateFlags3赋值有问题
             * 后来变成了可见，但是并没有更新mPrivateFlags3，导致手写笔在计算可见区域时有问题
             * 需要主动调一次。
             * 当前只有平板上用手写笔输入时有这个问题，所以做条件限制
             */
            if (ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_TABLET)) {
                mSearchView.getSearchAutoComplete().onVisibilityAggregated(true);
            }
            openSoftInput(true);
        } else {
            mSearchView.setVisibility(GONE);
            mInputTextView.setVisibility(VISIBLE);
            openSoftInput(false);
        }
    }

    /**
     * get the real search view
     */
    public SearchView getSearchView() {
        return mSearchView;
    }

    /**
     * open/close input keyboard
     *
     * @param open
     */
    public void openSoftInput(boolean open) {
        if ((mSearchView != null) && (mSearchView.getSearchAutoComplete() != null)) {
            InputMethodManager imm = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            if (open) {
                setSearchAutoCompleteFocus();
                if (imm != null) {
                    imm.showSoftInput(mSearchView.getSearchAutoComplete(), 0);
                }
            } else {
                if ((imm != null) && (imm.isActive())) {
                    imm.hideSoftInputFromWindow(mSearchView.getSearchAutoComplete().getWindowToken(), 0);
                }
            }
        }
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        if (mSearchIcon != null) {
            mSearchIcon.setEnabled(enabled);
        }
        if (mSearchView != null) {
            mSearchView.setEnabled(enabled);
        }
        if (mCancelButton != null) {
            mCancelButton.setEnabled(enabled);
        }
    }

    /**
     * 取消按钮的入场动画
     * 例如RTL时，搜索框的transition动画有问题，所以不需要动画
     * @param isNeedAnimation 是否需要指定动画
     */
    public void startCancelButtonEnterAnimation(boolean isNeedAnimation) {
        if (mCancelButton != null) {
            if (!isNeedAnimation) {
                mCancelButton.setAlpha(1);
                return;
            }
            mCancelButton.setAlpha(0);
            mCancelButton.animate()
                    .alpha(1)
                    .setDuration((long) (ENTER_ANIMATE_DURATION * (1 - DURATION_DELAY_FACTOR)))
                    .setStartDelay((long) (ENTER_ANIMATE_DURATION * DURATION_DELAY_FACTOR))
                    .start();
        }
    }

    /**
     * 取消按钮的退场动画
     * @param isNeedAnimation 是否需要指定动画
     */
    public void startCancelButtonExitAnimation(boolean isNeedAnimation) {
        if (mCancelButton != null) {
            if (!isNeedAnimation) {
                mCancelButton.setAlpha(0);
                return;
            }
            mCancelButton.setAlpha(1);
            mCancelButton.animate()
                    .alpha(0)
                    .setDuration((long) (ENTER_ANIMATE_DURATION * (1 - DURATION_DELAY_FACTOR)))
                    .start();
        }
    }

    private void setSearchAutoCompleteFocus() {
        if (mSearchView != null) {
            AutoCompleteTextView autoComplete = mSearchView.getSearchAutoComplete();
            if (autoComplete != null) {
                autoComplete.setFocusable(true);
                autoComplete.setFocusableInTouchMode(true);
                autoComplete.requestFocus();
            }
        }
    }

    private void setSearchAutoCompleteUnFocus() {
        if (mSearchView != null) {
            mSearchView.setImeVisibility(false);
            mSearchView.clearFocus();
            mSearchView.setFocusable(false);
            mSearchView.onWindowFocusChanged(false);
            AutoCompleteTextView autoComplete = mSearchView.getSearchAutoComplete();
            if (autoComplete != null) {
                autoComplete.setFocusable(false);
            }
        }
    }

    private boolean notifyCancelButton() {
        boolean result = false;
        if (mOnCancelButtonClickListeners != null) {
            for (OnCancelButtonClickListener ref : mOnCancelButtonClickListeners) {
                if (ref != null) {
                    result |= ref.onClickCancel();
                }
            }
        }
        return result;
    }
}
