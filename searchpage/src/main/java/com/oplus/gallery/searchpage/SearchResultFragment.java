/***********************************************************
 * * Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: - SearchResultFragment.java
 * * Description: Search result fragment
 * * Version: 1.0
 * * Date : 2017-02-07 14:11:42
 * * Author:  shujian@Apps.Gallery3D
 * * OPLUS Java File Skip Rule:Method<PERSON>ength,ParameterNumber,DeclarationOrder,MethodComplexity
 * * ---------------------Revision History: ---------------------
 * *  shujian@Apps.Gallery3D       2017/02/07   1.0     build this module
 ****************************************************************/
package com.oplus.gallery.searchpage;

import static android.provider.BaseColumns._ID;
import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;
import static com.oplus.andes.photos.kit.search.data.AndesMultiQueryResult.SEARCH_COMPOSITE_RESULT_ENTRIES;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.PICTURE_FRAGMENT;
import static com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry.TYPE_NORMAL_ALBUM;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.BASE_URI;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_BUCKETID;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_FORCE;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_GROUPID;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_INPUT;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_LABEL_ID;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_MEMORYID;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SEARCH_RESULT_KEYWORD_ENTRIES;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SUGGESTION_SELECTION_KEYWORD;
import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SUGGESTION_SELECTION_SLOT_FILTER;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE;
import static com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO;
import static com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value.RESULT_MULTIMODAL_MATCHED;
import static com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value.RESULT_MULTIMODAL_MISMATCH;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_ALBUM;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_ALL_PHOTO;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_CHILD_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_DATETIME;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_FILE_NAME;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_GUIDE_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_INDEX_SEARCH;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_LOCATION;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_MASK_TYPE;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_MEMORIES_ALBUM;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_MULTI_LABEL;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_MULTI_MODAL_SEARCH;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_OCR;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_OCR_EMBEDDING;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_OTHER;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_PERSON;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_RECENT_TIME;
import static com.oplus.gallery.framework.abilities.search.SearchType.TYPE_SPECIAL_LABEL;
import static com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument.SEARCH;
import static com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument.SEARCH_FULL_LINK;
import static com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument.SPECIAL_TYPE_SEARCH_FULL_LINK;
import static com.oplus.gallery.standard_lib.app.AppConstants.MagicCode.HASH_CODE_MAGIC;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.LifecycleKt;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.GalleryLinearLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ItemAnimator;

import com.coui.appcompat.cardlist.COUICardListHelper;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.emptyview.COUIEmptyStateView;
import com.oplus.andes.photos.kit.search.data.CompositeData;
import com.oplus.gallery.addon.utils.OSVersionUtils;
import com.oplus.gallery.basebiz.constants.IntentConstant;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper;
import com.oplus.gallery.basebiz.task.ThumbnailJob;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivityKt;
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment;
import com.oplus.gallery.basebiz.util.AlbumPasswordSettingsUtil;
import com.oplus.gallery.basebiz.widget.SlotView;
import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.model.data.base.entry.AlbumEntry;
import com.oplus.gallery.business_lib.model.data.label.LabelSearchEngine;
import com.oplus.gallery.business_lib.model.data.picture.set.ExtOrderIdsPictureAlbum;
import com.oplus.gallery.business_lib.model.data.search.set.SearchMultiModalResultAlbum;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties;
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch;
import com.oplus.gallery.framework.abilities.search.SearchType;
import com.oplus.gallery.framework.abilities.search.entry.KeywordEntry;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.framework.abilities.search.entry.SearchResultEntry;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItemExtKt;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchCommonUtils;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchDBHelper;
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesHelper;
import com.oplus.gallery.business_lib.model.data.person.set.FaceItem;
import com.oplus.gallery.business_lib.model.data.search.set.SearchResultAlbum;
import com.oplus.gallery.business_lib.ui.IPicturePositionController;
import com.oplus.gallery.business_lib.viewmodel.style.IStylePool;
import com.oplus.gallery.business_lib.viewmodel.style.StylePool;
import com.oplus.gallery.business_lib.viewmodel.style.StyleType;
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils;
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant;
import com.oplus.gallery.foundation.tracing.constant.LabelTrackTypeConstant;
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant;
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant.Value;
import com.oplus.gallery.foundation.tracing.helper.SearchTrackHelper;
import com.oplus.gallery.foundation.ui.systembar.SystemBarControllerKt;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.display.DisplayUtils;
import com.oplus.gallery.foundation.util.ext.ResourceExtKt;
import com.oplus.gallery.foundation.util.text.SpecialSplitter;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument;
import com.oplus.gallery.router_lib.meta.PostCard;
import com.oplus.gallery.searchpage.bean.SearchResultGridGroupEntry;
import com.oplus.gallery.searchpage.bean.SearchResultGridItemEntry;
import com.oplus.gallery.searchpage.bean.SearchResultGuideItemEntry;
import com.oplus.gallery.searchpage.bean.SearchResultRecommendGroupEntry;
import com.oplus.gallery.searchpage.bean.SearchResultRecommendItemEntry;
import com.oplus.gallery.searchpage.bean.UpdateAvatarMsgEntry;
import com.oplus.gallery.searchpage.locationsuggestion.LocationSuggestionFragment;
import com.oplus.gallery.searchpage.ui.ColorExpandableListView;
import com.oplus.gallery.searchpage.ui.CustomGridLayoutManager;
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail;
import com.oplus.gallery.standard_lib.baselist.view.GridItemGapDecoration;
import com.oplus.gallery.standard_lib.baselist.view.ItemGapDecoration;
import com.oplus.gallery.standard_lib.graphics.StyleData;
import com.oplus.gallery.standard_lib.graphics.StyleDrawableMaker;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.JobContext;
import com.oplus.gallery.standard_lib.thread.ThreadPool;
import com.oplus.gallery.standard_lib.ui.util.UIConfigUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.support.dmp.aiask.AIAskContainer;
import com.oplus.support.dmp.aiask.interfaces.AIAskInterface;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

import kotlin.coroutines.CoroutineContext;
import kotlin.ranges.RangesKt;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;
import static com.oplus.gallery.basebiz.R.drawable.base_icon_drm;
import static com.oplus.gallery.basebiz.R.drawable.base_icon_cloud;
import static com.oplus.gallery.basebiz.R.drawable.base_icon_albumfavorite_small;

public class SearchResultFragment extends BaseFragment implements IPicturePositionController {
    public static final int SORT_TYPE_DATE = 1;
    public static final int SORT_TYPE_ALBUM = 2;
    public static final int SORT_TYPE_SPECIAL_LABEL = 3;
    public static final int SORT_TYPE_OCR = 4;
    public static final int SORT_TYPE_PERSON = 5;
    public static final int SORT_TYPE_LOCATION = 6;
    public static final int SORT_TYPE_LABEL = 7;
    public static final int SORT_TYPE_MEMORIES_ALBUM = 8;
    public static final int SORT_TYPE_DEFAULT = 9;
    public static final int INT_ZERO = 0;

    public static final int ENTRY_TYPE_UNKNOWN = 0; // Might be re-query to refresh result page
    public static final int ENTRY_TYPE_HISTORY = 3;
    public static final int MSG_UPDATE_AVATAR = 2;
    private static final int NO_ROTATION = 0;
    private static final String TAG = "SearchResultFragment";
    private static final String DEFAULT_TAG = "default_tag";
    private static final String KEY_NEED_NEW_ADAPTER = "need_new_adapter";
    /**
     * 是否是模糊搜索结果
     */
    private static final String KEY_IS_ONLY_FUZZY_SEARCH_RESULT = "is_only_fuzzy_search_result";
    /**
     * 多模态结果数量
     */
    private static final String KEY_MULTI_MODAL_RESULT_COUNT = "multi_modal_result_count";
    /**
     * 是否有搜索结果
     * true：有（引导、网格、推荐任意一个有都行）。false：没有
     */
    private static final String KEY_HAS_SEARCH_RESULT = "has_search_result";
    /**
     * 推荐搜索结果列表数量
     */
    private static final String KEY_SUGGEST_LABELS_SIZE = "suggest_labels_size";
    private static final String SYMBOL_COMMA = ",";
    private static final int GRID_MAX_SIZE = 30;
    private static final int MSG_QUERY_TASK = 1;
    private static final int MSG_UPDATE_RESULT_LIST = 1;
    private static final int MSG_CLEAN_RESULT_LIST = 3;
    private static final int MSG_PERFORM_SINGLE_JUMP = 4;
    private static final int MSG_UPDATE_GRID_RESULT_LIST = 5;
    private static final int MSG_UPDATE_GUIDE_RESULT_LIST = 6;
    private static final int MSG_UPDATE_RECOMMEND_RESULT_LIST = 7;
    private static final int MSG_UPDATE_GRID_AVATAR = 8;
    private static final int MSG_SCROLL_TO_SEARCH_GRID = 9;
    private static final int MSG_UPDATE_ADAPTER_PWD = 10;
    /**
     * 更新页面显示状态
     */
    private static final int MSG_UPDATE_PAGE_STATUS = 11;
    private static final String TASK_QUEUE_THREAD_NAME = "TaskQueueHandlerThread";
    private static final int INVALID_INDEX = -1;
    private static final int GRID_LAYOUT_ROW_2 = 2;
    private static final int GRID_LAYOUT_ROW_3 = 3;
    /**
     * 搜索提示词的RecyclerView是ExpandListView的Header，导致这个RecyclerView只能显示最多一屏的Item，这里给RecyclerView额外空间
     */
    private static final int MAX_EXTRA_SPACE = 2000 * 10;
    private static final String DEBUG_MULTIMODAL_RESULT = "debug.multimodal.result.display";
    private static final String MULTIMODAL_RESULT_TEST_STRING = "(多模态结果)";

    private final String mPositionControllerKey = Integer.toString(hashCode());
    private final TaskQueueDispatcher mTaskQueueDispatcher = new TaskQueueDispatcher();
    private final StylePool mStylePool = new StylePool();
    SearchActivityLinker mSearchActivityLinker = null;

    /**
     * 点击的 AI 控件上面的追问 query
     */
    String mAIAskQuery = null;
    private View mRootView = null;
    private COUIEmptyStateView mEmptyPageView = null;
    private View mLayoutSearchGuide = null;
    private ViewGroup mLayoutGuideContent = null;
    private ViewGroup mLayoutGridContent = null;
    private LinearLayout mLayoutGuide = null;
    private RecyclerView mRecyclerViewSearchGuide = null;
    private RecyclerView mRecyclerViewSearchGrid = null;
    private CustomGridLayoutManager mRecyclerViewSearchGridLayoutManager = null;
    private ColorExpandableListView mListViewSearchRecommend = null;
    private TextView mPictureNumText;
    private TextView mViewAllText;
    private LinearLayout mSearchGridResultContent;
    private View mSearchGridResultContentChildView;

    private SearchResultStatistics mStatistics = null;
    private HandlerThread mTaskQueueThread = null;
    private Handler mTaskQueueHandler = null;
    private View.OnClickListener mOnGuideClickedListener = null;
    private List<SearchResult> mLastQueryResultList;
    private String mLastQuery = null;
    private String mCurrentQuery = null;

    /**
     * 记录上一次请求 AI ASk 的 query，用于判断当前 query 是否和上一次一样，一样则不请求。
     * 1. 进入结果照片大图，收藏一下，在返回来走 onResume
     * 2. 控件出来后，点击下面的推荐搜索词。（推荐搜索词和输入一致的情况）
     */
    private String mAIAskLastQuery = null;
    private String[] mKeywords = null;
    private SearchResultGridGroupEntry mSearchResultEntry = null;
    private SearchResultGridAdapter mSearchResultGridAdapter;
    private KeywordEntry mGuideClickedKeyWordEntry = null;
    private ItemGapDecoration mItemGapDecoration = null;
    private int mGridLayoutMaxRow = 3;
    private int mGridLayoutColumn = 4;
    private int mGridLayoutItemWidth = 0;

    private float mMinItemWidth;
    private float mMaxItemWidth;
    private float mGridItemGap;
    private int mMicroThumbnailType = ThumbnailSizeUtils.getMicroThumbnailKey();

    private int mStartPadding = 0;
    private int mEndPadding = 0;

    private LocationSuggestionFragment mLocationSuggestionFragment = null;

    /**
     * 是否保持系统栏可见。
     */
    private Boolean mIsKeepSystemBarVisible = true;

    /**
     * 是否需要验证隐私密码
     */
    private boolean mNeedVerifyPassword = false;

    /**
     * 记录当前的guide entry信息
     */
    protected SearchResultGuideItemEntry mCurrentSearchResultGuideItemEntry = null;

    /**
     * AI 控件，隐藏和显示由其内部控制，我们不控制
     */
    private AIAskContainer mAIAskContainer = null;

    /**
     * Ai控件的注入回调接口
     */
    private AIAskInterface mAiAskCallback = new AiAskCallback(this);

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {

        private SearchResultRecommendAdapter mExpandableListAdapter;
        private SearchResultGuideAdapter mSearchResultGuideAdapter;

        @Override
        public void handleMessage(Message msg) {
            if (getContext() == null) {
                return;
            }
            switch (msg.what) {
                case MSG_UPDATE_RESULT_LIST:
                    handleUpdateResultListMsg(msg);
                    break;
                case MSG_UPDATE_GRID_RESULT_LIST:
                    handleSearchGridResult(msg);
                    break;
                case MSG_UPDATE_GUIDE_RESULT_LIST:
                    handleSearchGuideResult(msg);
                    break;
                case MSG_UPDATE_RECOMMEND_RESULT_LIST:
                    handleSearchRecommendResult(msg);
                    break;
                case MSG_UPDATE_ADAPTER_PWD:
                    handleListAdapter();
                    break;
                case MSG_UPDATE_AVATAR:
                    handleUpdateAvatarMsg(msg);
                    break;
                case MSG_UPDATE_GRID_AVATAR:
                    handleUpdateGridAvatarMsg(msg);
                    break;
                case MSG_CLEAN_RESULT_LIST:
                    setCurrentQuery(null);
                    setKeywords(null);
                    clearSearchResultData();
                    mGuideClickedKeyWordEntry = null;
                    stopAIAsk();
                    if (mSearchActivityLinker != null) {
                        mSearchActivityLinker.onSearchResultChanged(SearchActivityLinker.SHOW_RECOMMEND);
                    }
                    break;
                case MSG_PERFORM_SINGLE_JUMP:
                    if (mRecyclerViewSearchGuide != null) {
                        SearchResultGuideAdapter adapter = (SearchResultGuideAdapter) mRecyclerViewSearchGuide.getAdapter();
                        if ((adapter != null) && (adapter.getItemCount() == 1)) {
                            onItemClicked(adapter.getSearchEntry(0));
                        }
                    }
                    break;
                case MSG_SCROLL_TO_SEARCH_GRID:
                    mListViewSearchRecommend.scrollToLayoutGridContent(mLayoutGuide, mLayoutGridContent);
                    break;
                case MSG_UPDATE_PAGE_STATUS:
                    handlePageShowStatus(msg);
                    break;
                default:
                    break;
            }
        }

        /**
         * 处理页面显示状态
         * 如果有推荐搜索，显示推荐搜索
         * 如果无推荐搜索，有搜索结果，显示搜索结果
         * 默认显示无搜索结果
         */
        private void handlePageShowStatus(Message msg) {
            int status = SearchActivityLinker.NO_RESULT;
            Bundle bundle = msg.getData();
            int labelSize = bundle.getInt(KEY_SUGGEST_LABELS_SIZE);
            // 检查是否有推荐搜索词
            if (labelSize > 0) {
                status = SearchActivityLinker.GUIDE_RESULT;
            } else {
                boolean hasSearchResult = bundle.getBoolean(KEY_HAS_SEARCH_RESULT, false);
                if (hasSearchResult) {
                    // 只有模糊搜索结果
                    status = SearchActivityLinker.SHOW_RESULT;
                }
            }

            if (TextUtils.isEmpty(mCurrentQuery)) {
                status = SearchActivityLinker.SHOW_RECOMMEND;
            }

            if (mSearchActivityLinker != null) {
                mSearchActivityLinker.onSearchResultChanged(status);
            }
        }

        /**
         * 处理推荐搜索词列表
         * @param msg 包含推荐搜索词
         */
         private void handleUpdateResultListMsg(Message msg) {
            if (msg.obj instanceof List) {
                clearSearchResultData();
                mLayoutGuideContent.removeAllViews();
                List<String> labels = (List<String>) msg.obj;
                if ((labels != null) && (labels.size() > 0)) {
                    for (String label : labels) {
                        if ((label != null) && !label.trim().isEmpty()) {
                            addGuide(label);
                        }
                    }
                }
            } else {
                clearSearchResultData();
            }
        }

        private void handleUpdateGridAvatarMsg(Message msg) {
            if (msg.obj instanceof UpdateAvatarMsgEntry) {
                UpdateAvatarMsgEntry entry = (UpdateAvatarMsgEntry) msg.obj;
                ImageView imageView = entry.getImageView();
                Bitmap bitmap = entry.getBitmap();
                StyleData rectThumbStyleData = mStylePool.getStyle(StyleType.TYPE_RECT_THUMB_STYLE);
                imageView.setImageDrawable(new StyleDrawableMaker(rectThumbStyleData).generateDrawable(bitmap, NO_ROTATION, null));
                imageView.setForceDarkAllowed(false);
            }
        }

        private void handleUpdateAvatarMsg(Message msg) {
            if (msg.obj instanceof UpdateAvatarMsgEntry) {
                UpdateAvatarMsgEntry entry = (UpdateAvatarMsgEntry) msg.obj;
                ImageView imageView = entry.getImageView();
                Bitmap bitmap = entry.getBitmap();
                int avatarType = entry.getType();
                Drawable drawable = null;
                StyleData thumbStyleData = null;
                if (avatarType == TYPE_PERSON) {
                    thumbStyleData = mStylePool.getStyle(StyleType.TYPE_CIRCLE_THUMB_STYLE);
                } else {
                    thumbStyleData = mStylePool.getStyle(StyleType.TYPE_THUMB_STYLE);
                }
                drawable = new StyleDrawableMaker(thumbStyleData).generateDrawable(bitmap, NO_ROTATION, null);
                imageView.setImageDrawable(drawable);
                imageView.setForceDarkAllowed(false);
            }
        }

        private void clearSearchResultData() {
            if ((mExpandableListAdapter != null) && (mSearchResultGuideAdapter != null)
                    && (mSearchResultGridAdapter != null)) {
                mExpandableListAdapter.clear();
                mSearchResultGuideAdapter.clear();
                mSearchResultGridAdapter.clear();
            }
        }

        private void handleSearchRecommendResult(Message msg) {
            if (msg.obj instanceof List) {
                List<SearchResultRecommendGroupEntry> recommendGroupEntries = (List<SearchResultRecommendGroupEntry>) msg.obj;
                Bundle bundle = msg.getData();
                boolean needNewAdapter = bundle.getBoolean(KEY_NEED_NEW_ADAPTER);
                boolean isFuzzySearchResult = bundle.getBoolean(KEY_IS_ONLY_FUZZY_SEARCH_RESULT);

                // 是模糊搜索结果，清空recommendGroupEntries，让mListViewSearchRecommend不显示
                if (isFuzzySearchResult) {
                    recommendGroupEntries.clear();
                }

                if (needNewAdapter || (mListViewSearchRecommend.getAdapter() == null)) {
                    if (mExpandableListAdapter != null) {
                        mExpandableListAdapter.release();
                        mExpandableListAdapter = null;
                    }
                    Context context = getContext();
                    if (context == null) {
                        GLog.w(TAG, "handleSearchRecommendResult, context is null");
                        return;
                    }
                    mExpandableListAdapter = new SearchResultRecommendAdapter(context,
                            mListViewSearchRecommend,
                            mSearchActivityLinker,
                            mHandler,
                            mStylePool,
                            mNeedVerifyPassword
                    );
                    mExpandableListAdapter.setGroupInfoList(recommendGroupEntries);
                    mListViewSearchRecommend.setAdapter(mExpandableListAdapter);
                } else {
                    mExpandableListAdapter.setNeedVerifyPassword(mNeedVerifyPassword);
                    mExpandableListAdapter.setGroupInfoList(recommendGroupEntries);
                    mExpandableListAdapter.notifyDataSetChanged();
                    GLog.d(TAG, LogFlag.DL, "handleSearchRecommendResult finish");
                }
            }
        }

        private void handleListAdapter() {
            if (mSearchResultGuideAdapter != null) {
                mSearchResultGuideAdapter.setNeedVerifyPassword(mNeedVerifyPassword);
                mSearchResultGuideAdapter.notifyDataSetChanged();
                GLog.d(TAG, LogFlag.DL, "mSearchResultGuideAdapter handleListAdapter");
            }
            if (mExpandableListAdapter != null) {
                mExpandableListAdapter.setNeedVerifyPassword(mNeedVerifyPassword);
                mExpandableListAdapter.notifyDataSetChanged();
                GLog.d(TAG, LogFlag.DL, "mExpandableListAdapter handleListAdapter");
            }
        }

        /**
         * 处理引导结果
         * @param msg 包含引导数据的 msg
         */
        private void handleSearchGuideResult(Message msg) {
            if (msg.obj instanceof List) {
                List<SearchResultGuideItemEntry> guideEntries = (List<SearchResultGuideItemEntry>) msg.obj;
                Bundle bundle = msg.getData();
                boolean isOnlyFuzzySearchResult = bundle.getBoolean(KEY_IS_ONLY_FUZZY_SEARCH_RESULT, false);
                int multiModalResultCount = bundle.getInt(KEY_MULTI_MODAL_RESULT_COUNT, 0);
                // 是模糊搜索结果，清空guideEntries，让mRecyclerViewSearchGuide不显示
                if (isOnlyFuzzySearchResult) {
                    guideEntries.clear();
                }

                if (mRecyclerViewSearchGuide.getAdapter() == null) {
                    mSearchResultGuideAdapter = new SearchResultGuideAdapter(getContext(), mNeedVerifyPassword);
                    mSearchResultGuideAdapter.setDataSource(guideEntries);
                    mRecyclerViewSearchGuide.setAdapter(mSearchResultGuideAdapter);
                } else {
                    mSearchResultGuideAdapter.setNeedVerifyPassword(mNeedVerifyPassword);
                    mSearchResultGuideAdapter.setDataSource(guideEntries);
                    mSearchResultGuideAdapter.notifyDataSetChanged();
                }

                int topMargin = getResources().getDimensionPixelOffset(R.dimen.search_result_guide_layout_margin_top);
                if (isOnlyFuzzySearchResult && (multiModalResultCount > 0)) {
                    // 只有模糊搜索结果
                    topMargin = 0;
                }
                GLog.d(TAG, "[handleSearchGuideResult] "
                        + ", isOnlyFuzzySearchResult=" + isOnlyFuzzySearchResult
                        + ", guideEntries=" + guideEntries.size()
                        + ", multiModalResultCount=" + multiModalResultCount
                );

                // (是多模态结果) && (多模态数量 > 0)，topMargin要赋为0，其余不做修改
                ViewGroup.LayoutParams layoutParams = mRecyclerViewSearchGuide.getLayoutParams();
                if ((layoutParams instanceof LinearLayout.LayoutParams)) {
                    ((LinearLayout.LayoutParams) layoutParams).topMargin = topMargin;
                    mRecyclerViewSearchGuide.setLayoutParams(layoutParams);
                }
            }
        }

        private void handleSearchGridResult(Message msg) {
            if (msg.obj instanceof SearchResultGridGroupEntry) {
                SearchResultGridGroupEntry gridEntry = (SearchResultGridGroupEntry) msg.obj;
                int pictureNum = gridEntry.getCount();
                int videoCount = gridEntry.getVideoCount();
                String pictureNumText = getResources().getQuantityString(R.plurals.search_result_image_count, pictureNum, pictureNum);
                if ((pictureNum == videoCount) && (pictureNum > 0)) {
                    pictureNumText = getResources().getQuantityString(
                            com.oplus.gallery.basebiz.R.plurals.base_timer_shaft_only_have_video, videoCount, videoCount);
                }
                if (gridEntry.isMultiModalResult()) {
                    if (GallerySystemProperties.getBoolean(DEBUG_MULTIMODAL_RESULT, false)) {
                        mPictureNumText.setText(pictureNumText + MULTIMODAL_RESULT_TEST_STRING);
                        SpannableStringBuilder builder = new SpannableStringBuilder(mPictureNumText.getText().toString());
                        ForegroundColorSpan redSpan = new ForegroundColorSpan(Color.RED);
                        builder.setSpan(redSpan, pictureNumText.length(), mPictureNumText.getText().length(),
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                        mPictureNumText.setText(builder);
                    } else {
                        mPictureNumText.setText(pictureNumText);
                    }
                } else {
                    mPictureNumText.setText(pictureNumText);
                }

                mSearchResultEntry = gridEntry;

                showSearchGridResultContent(pictureNum > 0);

                if (mRecyclerViewSearchGrid.getAdapter() == null) {
                    mSearchResultGridAdapter = new SearchResultGridAdapter(getContext(), mStylePool);
                    mSearchResultGridAdapter.setFirstItemDrawCallback(itemView -> {
                        RealShowTimeInstrument.endRecord(SEARCH_FULL_LINK);
                        RealShowTimeInstrument.endRecord(SPECIAL_TYPE_SEARCH_FULL_LINK);
                    });
                    /*
                    setHasStableIds(true)后notifyDataSetChanged时会缓存在mAttachedScrap，不会重新createViewHolder
                    如果没有setHasStableIds(true)会直接CreateViewHolder，重新inflate，itemView重新测量布局导致闪烁
                    */
                    mSearchResultGridAdapter.setHasStableIds(true);
                    mSearchResultGridAdapter.setDataSource(gridEntry.getItemEntries(), gridEntry.isMultiModalResult());
                    mRecyclerViewSearchGrid.setAdapter(mSearchResultGridAdapter);
                } else {
                    mSearchResultGridAdapter.setDataSource(gridEntry.getItemEntries(), gridEntry.isMultiModalResult());
                    mSearchResultGridAdapter.notifyDataSetChanged();
                }
            }
        }
    };

    private void showSearchGridResultContent(boolean show) {
        int paddingTop = show ? getResources().getDimensionPixelOffset(R.dimen.search_result_grid_margin_top) : 0;

        ViewGroup.LayoutParams layoutParams = mSearchGridResultContentChildView.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.height = show ? WRAP_CONTENT : 0;
            mSearchGridResultContentChildView.setLayoutParams(layoutParams);
        }
        mSearchGridResultContent.setPadding(mSearchGridResultContent.getPaddingLeft(),
                paddingTop,
                mSearchGridResultContent.getPaddingRight(),
                mSearchGridResultContent.getPaddingBottom());
        mSearchGridResultContent.setVisibility(show ? View.VISIBLE : View.GONE);
        mRecyclerViewSearchGrid.setVisibility(show ? View.VISIBLE : View.GONE);
        // 不显示图片页时，将adapter置空，避免复用到detached的view，导致事件不响应
        if (!show && (mSearchResultGridAdapter != null)) {
            mRecyclerViewSearchGrid.setAdapter(null);
            mSearchResultGridAdapter.clear();
            mSearchResultGridAdapter = null;
        }
    }

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        registerPositionController();
        onSetupViewStyle();
    }

    private void onSetupViewStyle() {
        Context context = getContext();
        if (context == null) {
            GLog.w(TAG, "onSetupViewStyle, context is null, do not refresh style");
            return;
        }
        Resources resources = context.getResources();
        StyleData rectThumbStyleData = new StyleData();
        rectThumbStyleData.put(StyleData.KEY_THUMB_STROKE_WIDTH,
                resources.getDimension(com.oplus.gallery.foundation.ui.R.dimen.common_round_drawable_frame_stroke_width));
        rectThumbStyleData.put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR,
                resources.getColor(com.oplus.gallery.basebiz.R.color.common_round_drawable_frame_stroke_color, null));
        rectThumbStyleData.put(
                StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR, DisplayUtils.getDarkColorWithNightMode(
                        context,
                        resources.getColor(com.oplus.gallery.basebiz.R.color.standard_default_bg_color_for_transparent, null)
                )
        );
        StyleData roundThumbStyleData = rectThumbStyleData.copy();
        roundThumbStyleData.put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND);
        roundThumbStyleData.put(StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS,
                resources.getDimension(com.oplus.gallery.basebiz.R.dimen.base_album_small_item_cover_image_corner_radius));

        StyleData circleThumbStyleData = roundThumbStyleData.copy();
        circleThumbStyleData.put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_CIRCLE);

        mStylePool.addStyle(StyleType.TYPE_RECT_THUMB_STYLE, rectThumbStyleData);
        mStylePool.addStyle(StyleType.TYPE_THUMB_STYLE, roundThumbStyleData);
        mStylePool.addStyle(StyleType.TYPE_CIRCLE_THUMB_STYLE, circleThumbStyleData);
    }

    @Override
    public void onResume() {
        super.onResume();

        // 跳转其它页面返回后，保证状态栏和系统导航条的可见性正确
        setStatusBarAppearance(!COUIDarkModeUtil.isNightMode(requireContext()));
        setupSystemBarVisibility(mIsKeepSystemBarVisible);
        BuildersKt.launch(LifecycleKt.getCoroutineScope(getLifecycle()), (CoroutineContext) Dispatchers.getIO(),
                CoroutineStart.DEFAULT, (coroutineScope, continuation) -> {
                    Boolean needVerifyPassword = AlbumPasswordSettingsUtil.needVerifyPrivacyPassword(getContext());
                    if (mNeedVerifyPassword == needVerifyPassword) {
                        GLog.d(TAG, LogFlag.DL, "onResume no need refresh adapter");
                    } else {
                        mNeedVerifyPassword = needVerifyPassword;
                        getActivity().runOnUiThread(() -> {
                            mHandler.obtainMessage(MSG_UPDATE_ADAPTER_PWD, null).sendToTarget();
                            GLog.d(TAG, LogFlag.DL, "onResume send MSG_UPDATE_ADAPTER msg");
                        });
                    }
                    return null;
                });
        if ((mCurrentQuery != null) && !mCurrentQuery.trim().isEmpty() && (mTaskQueueHandler != null)) {
            mTaskQueueHandler.removeMessages(MSG_QUERY_TASK);
            GLog.d(TAG, LogFlag.DL, "onResume SearchResultLoader send");
            mTaskQueueHandler.obtainMessage(MSG_QUERY_TASK, new SearchResultLoader(mCurrentSearchResultGuideItemEntry, mCurrentQuery,
                    mKeywords, SearchResultFragment.ENTRY_TYPE_UNKNOWN, true)).sendToTarget();
        }
    }

    @Override
    public void doViewCreated(@NotNull View view, @Nullable Bundle savedInstanceState) {
        super.doViewCreated(view, savedInstanceState);
        mTaskQueueThread = new HandlerThread(TASK_QUEUE_THREAD_NAME);
        mTaskQueueThread.start();
        mTaskQueueHandler = new Handler(mTaskQueueThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                mTaskQueueDispatcher.handle(msg);
            }
        };
        mRootView = getContentView();
        createListeners();
        findViews(mRootView);
    }

    private void createListeners() {
        mOnGuideClickedListener = new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mSearchActivityLinker != null) {
                    String guide = (String) view.getTag();
                    mSearchActivityLinker.onRecommendClicked(TYPE_GUIDE_LABEL, guide, null);
                }
            }
        };
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mTaskQueueThread.quitSafely();
        mTaskQueueThread = null;
        mTaskQueueHandler = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mSearchActivityLinker != null) {
            mSearchActivityLinker.getWorkerSession().removeTasks(SearchResultCoverLoader.class);
        }
        unregisterPositionController();
        mHandler.removeCallbacksAndMessages(null);
        mStylePool.clearAllStyle();
        mAiAskCallback = null;
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //onAppUiStateChanged不响应某些系统UI变化，如不响应亮暗色变化，需要在此处更新style
        onSetupViewStyle();
    }

    @Override
    public void onAppUiStateChanged(@NotNull AppUiResponder.AppUiConfig config) {
        super.onAppUiStateChanged(config);
        if (config.getWindowHeight().isChanged() || config.getWindowWidth().isChanged()) {
            refreshItemWidth();
            refreshRecyclerViewPadding(config);
            refreshGridLayout(config);
        }
    }

    private float getRequireGridWidth(@NotNull AppUiResponder.AppUiConfig config) {
        return UIConfigUtils.getRequireGridWidth(
                config.getWindowWidth().getCurrent(),
                getResources().getDimension(R.dimen.search_result_grid_edge),
                getResources().getDimension(R.dimen.search_result_grid_gap),
                getResources().getInteger(R.integer.search_result_all_grid),
                getResources().getInteger(R.integer.search_result_require_grid)
        ) + getResources().getDimension(R.dimen.search_result_grid_view_padding_horizontal) * 2;
    }

    private void refreshRecyclerViewPadding(@NotNull AppUiResponder.AppUiConfig config) {
        int horizontalPadding = (int) ((config.getWindowWidth().getCurrent() - getRequireGridWidth(config)) / 2);
        mListViewSearchRecommend.setPadding(horizontalPadding,
                mListViewSearchRecommend.getPaddingTop(),
                horizontalPadding,
                mListViewSearchRecommend.getPaddingBottom());
    }

    private void refreshGridLayout(@NotNull AppUiResponder.AppUiConfig config) {
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) mSearchGridResultContentChildView.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.setMarginStart(getResources().getDimensionPixelSize(R.dimen.search_result_grid_view_padding_horizontal));
            layoutParams.setMarginEnd(getResources().getDimensionPixelSize(R.dimen.search_result_grid_view_padding_horizontal));
            mSearchGridResultContentChildView.setLayoutParams(layoutParams);
        }
        mSearchGridResultContentChildView.setPaddingRelative(
                getResources().getDimensionPixelSize(R.dimen.search_result_grid_title_padding_start),
                mSearchGridResultContentChildView.getPaddingTop(),
                getResources().getDimensionPixelSize(R.dimen.search_result_grid_title_padding_end),
                mSearchGridResultContentChildView.getPaddingBottom()
        );
        float requireGridWidth = getRequireGridWidth(config);
        int windowHeight = config.getWindowHeight().getCurrent();
        if (requireGridWidth > windowHeight) {
            mGridLayoutMaxRow = GRID_LAYOUT_ROW_2;
        } else {
            mGridLayoutMaxRow = GRID_LAYOUT_ROW_3;
        }
        float edge = mStartPadding + mEndPadding + getResources().getDimension(R.dimen.search_result_grid_view_padding_horizontal) * 2;
        int oldGridLayoutColumn = mGridLayoutColumn;
        int oldGridLayoutItemWidth = mGridLayoutItemWidth;
        UIConfigUtils.ListConfig listConfig = UIConfigUtils.getItemMaxConfig(requireGridWidth,
                edge,
                mGridItemGap,
                RangesKt.rangeTo(mMinItemWidth, mMaxItemWidth));
        mGridLayoutColumn = listConfig.getColumn();
        mGridLayoutItemWidth = listConfig.getItemWidth();
        if ((oldGridLayoutColumn == mGridLayoutColumn) && (oldGridLayoutItemWidth == mGridLayoutItemWidth)) {
            return;
        }

        mMicroThumbnailType = ThumbnailSizeUtils.getMicroThumbnailKey(mGridLayoutItemWidth);

        /*
         * fix:9044230。Otest报错，IllegalArgumentException: Tmp detached view should be removed from RecyclerView before it can be recycled
         * onSystemBarChanged->refreshGridLayout->setLayoutManager->endAnimations->cancelAll->cancel->endAnimation->notifyEndListeners
         * ->notifyListeners->callOnList->call->onAnimationEnd->dispatchAddFinished->dispatchAnimationFinished->onAnimationFinished
         * ->removeAnimatingView->recycleViewHolderInternal->holder.isTmpDetached()->IllegalArgumentException
         *
         * 可能是因为频繁setLayoutManager()导致，改为停止动画停止滚动，然后setSpanCount()动态设置列数
         */
        ItemAnimator itemAnimator = mRecyclerViewSearchGrid.getItemAnimator();
        if (itemAnimator != null) {
            itemAnimator.endAnimations();
        }
        mRecyclerViewSearchGrid.stopScroll();
        if (mRecyclerViewSearchGrid.getLayoutManager() == null) {
            mRecyclerViewSearchGridLayoutManager = new CustomGridLayoutManager(getContext(), mGridLayoutColumn);
            mRecyclerViewSearchGridLayoutManager.setScrollVerticalEnabled(false);
            mRecyclerViewSearchGrid.setLayoutManager(mRecyclerViewSearchGridLayoutManager);
        } else {
            mRecyclerViewSearchGridLayoutManager.setSpanCount(mGridLayoutColumn);
        }

        if (mItemGapDecoration != null) {
            mRecyclerViewSearchGrid.removeItemDecoration(mItemGapDecoration);
        }
        mItemGapDecoration = getItemGapDecoration(mGridLayoutColumn, (int) mGridItemGap, (int) requireGridWidth);
        mRecyclerViewSearchGrid.addItemDecoration(mItemGapDecoration);
        mHandler.obtainMessage(MSG_UPDATE_GRID_RESULT_LIST, mSearchResultEntry).sendToTarget();
    }

    private void findViews(View contentView) {
        mEmptyPageView = contentView.findViewById(R.id.empty_page_view);

        mListViewSearchRecommend = contentView.findViewById(R.id.list_view_search_results);
        mListViewSearchRecommend.setGroupIndicator(null);
        mListViewSearchRecommend.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                if (mSearchActivityLinker != null) {
                    mSearchActivityLinker.onScrollStateChanged(scrollState);
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
            }
        });

        initAISearchLayout();
        initSuggestionLayout();
        initGuideLayout();
        initGridLayout();
        initSuggestSearchLayout();
    }

    /**
     * 初始化推荐搜索布局
     */
    private void initSuggestSearchLayout() {
        View suggestSearchLayout = LayoutInflater.from(getContext()).inflate(R.layout.searchpage_suggest_search, null, false);
        mLayoutSearchGuide = suggestSearchLayout.findViewById(R.id.layout_search_guide);
        mLayoutGuideContent = suggestSearchLayout.findViewById(R.id.layout_guide_content);
        mListViewSearchRecommend.addHeaderView(suggestSearchLayout);
    }

    private void initAISearchLayout() {
        if (!isAiAskEnable() || mAIAskContainer != null) {
            GLog.d(TAG, LogFlag.DL, () -> "initAISearchLayout, repeat");
            return;
        }
        FrameLayout aiAskContainerWrapper = (FrameLayout) LayoutInflater.from(getContext()).inflate(R.layout.search_ai_ask_container, null, false);
        mAIAskContainer = aiAskContainerWrapper.findViewById(R.id.ai_ask_container);
        mListViewSearchRecommend.addHeaderView(aiAskContainerWrapper);
        mAIAskContainer.register(mAiAskCallback);
    }

    void startAIAsk(String changedQuery) {
        if (!isAiAskEnable() || mAIAskContainer == null) {
            GLog.d(TAG, LogFlag.DL, () -> "startAIAsk, not support andes search or mAIAskContainer is null") ;
            return;
        }
        boolean queryNoChanged = (changedQuery == null) || changedQuery.equals(mAIAskLastQuery);
        if (queryNoChanged) {
            GLog.d(TAG, LogFlag.DL, () -> "startAIAsk, query no changed.");
            return;
        }
        // 如果是点击的控件的追问，也不需要去搜索
        if (changedQuery.equals(mAIAskQuery)) {
            GLog.d(TAG, LogFlag.DL, () -> "startAIAsk, click ai ask query no need search.");
            return;
        }
        if (!changedQuery.equals(mCurrentQuery)) {
            return;
        }
        mHandler.post(() -> {
            mAIAskContainer.startAIAsk(changedQuery);
            mAIAskLastQuery = changedQuery;
        });
        mAIAskQuery = null;
    }

    void stopAIAsk() {
        if (!isAiAskEnable() || mAIAskContainer == null) {
            GLog.d(TAG, LogFlag.DL, () -> "stopAIAsk, not support andes search or mAIAskContainer is null") ;
            return;
        }
        mHandler.post(() -> {
            mAIAskContainer.stopAiAsk();
            mAIAskLastQuery = null;
        });
        mAIAskQuery = null;
    }

    private void initSuggestionLayout() {
        // 仅外销显示城市名称搜索建议
        boolean needLocationSuggestion = ResourceExtKt.getBooleanSafe(getContext(),
                com.oplus.gallery.framework.datatmp.R.bool.search_filter_country_and_province);
        if (needLocationSuggestion) {
            View suggestionView = LayoutInflater.from(getContext()).inflate(
                    R.layout.search_location_suggestion_container, mListViewSearchRecommend, false
            );
            mListViewSearchRecommend.addHeaderView(suggestionView);

            mLocationSuggestionFragment = new LocationSuggestionFragment();
            mLocationSuggestionFragment.setSearchActivityLinker(mSearchActivityLinker);

            getChildFragmentManager().beginTransaction()
                    .replace(R.id.location_suggestion_container, mLocationSuggestionFragment)
                    .commitAllowingStateLoss();
        }
    }

    private void initGuideLayout() {
        mLayoutGuide = (LinearLayout) LayoutInflater.from(getContext()).inflate(R.layout.search_result_guide_view, mListViewSearchRecommend, false);
        mRecyclerViewSearchGuide = mLayoutGuide.findViewById(R.id.recyclerView_search_results);
        GalleryLinearLayoutManager layoutManager = new GalleryLinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false) {
            @Override
            protected void calculateExtraLayoutSpace(@NonNull RecyclerView.State state, @NonNull int[] extraLayoutSpace) {
                extraLayoutSpace[0] = MAX_EXTRA_SPACE;
                extraLayoutSpace[1] = MAX_EXTRA_SPACE;
            }
        };
        layoutManager.setScrollVerticalEnabled(false);
        mRecyclerViewSearchGuide.setLayoutManager(layoutManager);
        mListViewSearchRecommend.addHeaderView(mLayoutGuide);
    }

    private void initGridLayout() {
        refreshItemWidth();
        mSearchGridResultContent = (LinearLayout) LayoutInflater.from(getContext()).
                inflate(R.layout.search_list_item_result_grid, mListViewSearchRecommend, false);
        mSearchGridResultContentChildView = mSearchGridResultContent.findViewById(R.id.layout_grid_content_text_child_view);
        mPictureNumText = mSearchGridResultContent.findViewById(R.id.picture_number);
        mViewAllText = mSearchGridResultContent.findViewById(R.id.view_all_text);
        mViewAllText.setOnClickListener(itemView -> onItemClicked(mSearchResultEntry));
        mRecyclerViewSearchGrid = mSearchGridResultContent.findViewById(R.id.grid_recycler_view);
        mLayoutGridContent = mSearchGridResultContent.findViewById(R.id.layout_grid_content);
        mListViewSearchRecommend.addHeaderView(mSearchGridResultContent);

        refreshRecyclerViewPadding(getCurrentAppUiConfig());
        refreshGridLayout(getCurrentAppUiConfig());
    }

    private void refreshItemWidth() {
        if (getContext() != null) {
            mMinItemWidth = getContext().getResources().getDimension(com.oplus.gallery.basebiz.R.dimen.base_album_list_min_item_width);
            mMaxItemWidth = getContext().getResources().getDimension(com.oplus.gallery.basebiz.R.dimen.base_album_list_max_item_width);
            mGridItemGap = getContext().getResources().getDimension(R.dimen.search_result_grid_item_gap);
        }
    }

    private ItemGapDecoration getItemGapDecoration(int spanCount, int gapWidth, int parentWidth) {
        GridLayoutDetail.HorizontalGapsBuilder horizontalGapsBuilder = new GridLayoutDetail.HorizontalGapsBuilder();
        horizontalGapsBuilder.setSpanCount(spanCount);
        horizontalGapsBuilder.setGapWidth(gapWidth);
        horizontalGapsBuilder.setEdgeWidth(getResources().getDimensionPixelSize(R.dimen.search_result_grid_view_padding_horizontal));
        horizontalGapsBuilder.setParentWidth(parentWidth);
        GridLayoutDetail gridLayoutDetail = horizontalGapsBuilder.build();
        gridLayoutDetail.getItemDecorationGapPx().bottom = mGridItemGap;
        return new GridItemGapDecoration(gridLayoutDetail);
    }

    private void refreshPadding(View contentView, WindowInsetsCompat windowInsets) {
        if (contentView == null) {
            return;
        }

        Insets insets = SystemBarControllerKt.naviBarInsets(windowInsets, true);
        mStartPadding = insets.left;
        mEndPadding = insets.right;
    }

    public void setSearchActivityLinker(SearchActivityLinker linker) {
        mSearchActivityLinker = linker;
    }

    public void updateSuggestionResult(String query, int entryType, boolean needNewAdapter, boolean needForceUpdate, int type,
                                       SearchResultGuideItemEntry entry, boolean forceQuery) {
        if (!TextUtils.isEmpty(query)) {
            mCurrentQuery = query;
            mKeywords = null;
            if (mHandler != null) {
                mHandler.removeMessages(MSG_CLEAN_RESULT_LIST);
            }
            Handler handler = mTaskQueueHandler;
            if (handler != null) {
                RealShowTimeInstrument.startRecord(SEARCH);
                RealShowTimeInstrument.startRecord(SEARCH_FULL_LINK);
                handler.removeMessages(MSG_QUERY_TASK);
                handler.obtainMessage(MSG_QUERY_TASK, new SearchResultLoader(query, entryType, needNewAdapter, needForceUpdate, type,
                                entry, forceQuery))
                        .sendToTarget();
            }
            if (mLocationSuggestionFragment != null) {
                mLocationSuggestionFragment.updateSearchKeyword(query);
            }
        }
    }

    public void updateSuggestionResult(String query, String[] keywords, int entryType, boolean forceQuery) {
        if ((keywords != null) && (keywords.length > 0)) {
            mCurrentQuery = query;
            mKeywords = keywords;
            Handler handler = mTaskQueueHandler;
            if (handler != null) {
                handler.removeMessages(MSG_QUERY_TASK);
                GLog.d(TAG, "updateSuggestionResult SearchResultLoader send");
                handler.obtainMessage(MSG_QUERY_TASK,
                        new SearchResultLoader(null, query, keywords, entryType, forceQuery)).sendToTarget();
            }
        }
    }

    public void setCurrentQuery(String query) {
        mCurrentQuery = query;
    }

    public String getCurrentQuery() {
        return mCurrentQuery;
    }

    public void setKeywords(String[] keywords) {
        mKeywords = keywords;
    }

    public String[] getKeywords() {
        return mKeywords;
    }

    public static String getSearchResultType(int type) {
        switch (type) {
            case SearchType.TYPE_DATETIME:
                return Value.DATE;
            case SearchType.TYPE_LOCATION:
                return Value.LOCATION;
            case SearchType.TYPE_PERSON:
                return Value.PERSON;
            case SearchType.TYPE_LABEL:
                return Value.LABEL;
            case SearchType.TYPE_CHILD_LABEL:
                return Value.CHILD_LABEL;
            case SearchType.TYPE_GUIDE_LABEL:
                return Value.GUIDE_LABEL;
            case SearchType.TYPE_OCR:
                return Value.OCR;
            case SearchType.TYPE_ALBUM:
                return Value.ALBUM;
            case SearchType.TYPE_SPECIAL_LABEL:
                return Value.SPECIAL;
            case SearchType.TYPE_MEMORIES_ALBUM:
                return Value.MEMORIES;
            case SearchType.TYPE_FILE_NAME:
                return Value.FILE_NAME;
            default:
                return Value.UNKNOWN;
        }
    }

    public void setContentVisibility(int noResultVisibility, boolean needResultList, boolean needGuide, boolean needSearchGuide) {
        if (mEmptyPageView != null) {
            if (((mEmptyPageView.getVisibility() == View.GONE)
                    || (mEmptyPageView.getVisibility() == View.INVISIBLE))
                    && (noResultVisibility == View.VISIBLE)) {
                mEmptyPageView.playAnimation();
            }
            mEmptyPageView.setVisibility(noResultVisibility);
        }
        if (mLayoutSearchGuide != null) {
            mLayoutSearchGuide.setVisibility(needGuide ? View.VISIBLE : View.GONE);
        }

        if (!needResultList) {
            mLastQuery = null;
            mLastQueryResultList = null;
        }

        if (mListViewSearchRecommend != null) {
            mListViewSearchRecommend.setVisibility(View.VISIBLE);

            int paddingBottom = 0;
            if (getContext() != null) {
                paddingBottom = needResultList ? getResources().getDimensionPixelSize(R.dimen.search_result_recommend_padding_bottom) : 0;
            }
            mListViewSearchRecommend.setPadding(
                    mListViewSearchRecommend.getPaddingLeft(),
                    mListViewSearchRecommend.getPaddingTop(),
                    mListViewSearchRecommend.getPaddingRight(),
                    paddingBottom
            );
            ViewGroup.LayoutParams lp = mListViewSearchRecommend.getLayoutParams();
            lp.height = needResultList ? MATCH_PARENT : WRAP_CONTENT;
            mListViewSearchRecommend.setLayoutParams(lp);
        }

        if (mLayoutGuide != null) {
            if (needSearchGuide) {
                mRecyclerViewSearchGuide.setVisibility(View.VISIBLE);
                mLayoutGuide.setVisibility(View.VISIBLE);
            } else {
                mRecyclerViewSearchGuide.setVisibility(View.GONE);
                mLayoutGuide.setVisibility(View.GONE);
            }
        }
    }

    public void cleanSearchResult() {
        if (mHandler != null) {
            mHandler.obtainMessage(MSG_CLEAN_RESULT_LIST).sendToTarget();
        }
    }

    public void singleJump() {
        if (mHandler != null) {
            mHandler.obtainMessage(MSG_PERFORM_SINGLE_JUMP).sendToTarget();
        }
    }

    public void scrollToGridResult() {
        if (mHandler != null) {
            mHandler.obtainMessage(MSG_SCROLL_TO_SEARCH_GRID).sendToTarget();
        }
    }

    private boolean isNeedSingleJump() {
        return (mSearchActivityLinker != null) && (mSearchActivityLinker.singleJumpEnable());
    }

    public boolean hasSearchResult() {
        if (mRecyclerViewSearchGuide != null) {
            SearchResultGuideAdapter adapter = (SearchResultGuideAdapter) mRecyclerViewSearchGuide.getAdapter();
            return (adapter != null) && (adapter.getItemCount() > 0);
        }

        return false;
    }

    /**
     * 获取搜索结果推荐词
     *
     * @return 搜索推荐词
     */
    public String getGuideResult() {
        StringBuilder builder = new StringBuilder();
        if (mLayoutGuideContent != null) {
            for (int i = 0; i < mLayoutGuideContent.getChildCount(); i++) {
                String tag = mLayoutGuideContent.getChildAt(i).getTag().toString();
                int labelType = SearchTrackHelper.mappingRecommendGuideLabelType(tag);
                if (labelType == LabelTrackTypeConstant.LABEL_VALUE) {
                    continue;
                }
                builder.append(labelType);
                if (i != mLayoutGuideContent.getChildCount() - 1) {
                    builder.append(",");
                }
            }
        }
        return builder.toString();
    }

    private void addGuide(String label) {
        Context context = getContext();
        if ((context != null) && (mLayoutGuideContent != null)) {
            View guideView = createGuideView(context, label, mLayoutGuideContent);
            mLayoutGuideContent.addView(guideView);
        }
    }

    private View createGuideView(Context context, String guide, ViewGroup parentLayout) {
        View itemView = LayoutInflater.from(context).inflate(R.layout.search_auto_recommend_text, parentLayout, false);
        TextView textView = itemView.findViewById(R.id.button_recommend_item);
        textView.setTag(guide);
        textView.setText(guide);
        textView.setOnClickListener(mOnGuideClickedListener);
        itemView.setTag(guide);
        return itemView;
    }

    @Override
    public void onSystemBarChanged(@NotNull WindowInsetsCompat insets) {
        refreshPadding(mRootView, insets);
        refreshGridLayout(getCurrentAppUiConfig());
    }

    @Override
    public int getLayoutId() {
        return R.layout.search_result_fragment;
    }

    @NotNull
    @Override
    public Rect getItemRect(int index, boolean ignoreInvisibility) {
        Rect rect = new Rect();
        View itemView = mRecyclerViewSearchGridLayoutManager.findViewByPosition(index);
        if ((index != INVALID_INDEX) && (itemView != null)) {
            itemView.getGlobalVisibleRect(rect);
        }
        return rect;
    }

    @Override
    public int getItemIndex(@Nullable Path path) {
        return mSearchResultGridAdapter.getItemIndex(path);
    }

    @Override
    public void onPictureChanged(int index) {

    }

    @Override
    public void onPullDownStart(int index) {

    }

    @Override
    public void onPullDownFinish(int index, boolean complete) {
        /* do nothing */
    }

    private void registerPositionController() {
        ApiDmManager.getMainDM().registerPhotoPageAnimatorController(mPositionControllerKey, this);
    }

    private void unregisterPositionController() {
        ApiDmManager.getMainDM().unregisterPhotoPageAnimatorController(mPositionControllerKey);
    }

    @Override
    public boolean supportClickStatusBar() {
        return true;
    }

    @Override
    public void onStatusBarClicked() {
        mListViewSearchRecommend.post(new Runnable() {
            @Override
            public void run() {
                mListViewSearchRecommend.smoothScrollToPosition(0);
            }
        });
    }

    private static class SearchResult {
        public int mCount;
        public int mCoverPersonId;
        public int mCoverMemoriesId;
        public int mCoverGalleryId;
        public int mRotation;
        public int mAlbumType;

        @Override
        public boolean equals(Object o) {
            if (o instanceof SearchResult) {
                SearchResult result = (SearchResult) o;
                return (this.mCoverGalleryId == result.mCoverGalleryId)
                        && (this.mCoverPersonId == result.mCoverPersonId)
                        && (this.mCoverMemoriesId == result.mCoverMemoriesId)
                        && (this.mCount == result.mCount)
                        && (this.mRotation == result.mRotation)
                        && (mAlbumType == result.mAlbumType);
            }
            return false;
        }

        @Override
        public int hashCode() {
            final int prime = HASH_CODE_MAGIC;
            int result = 1;
            result = prime * result + mCoverPersonId;
            result = prime * result + mCoverMemoriesId;
            result = prime * result + mCoverGalleryId;
            result = prime * result + mCount;
            result = prime * result + mRotation;
            return result;
        }
    }

    public class SearchResultGuideAdapter extends COUIRecyclerView.Adapter<COUIRecyclerView.ViewHolder> {
        public static final String NAME = "Name";
        public static final String MEDIA_TYPE = "MediaType";

        private Context mContext = null;
        private List<SearchResultGuideItemEntry> mSearchEntries = null;

        private Boolean mNeedVerifyPassword = false;

        public SearchResultGuideAdapter(Context context, boolean needVerifyPassword) {
            mContext = context;
            mNeedVerifyPassword = needVerifyPassword;
        }

        /**
         * 给adapter设置是否需要验证隐私密码
         *
         * @param needVerifyPassword true,需要验证隐私密码，false，不需要验证隐私密码
         */
        public void setNeedVerifyPassword(boolean needVerifyPassword) {
            mNeedVerifyPassword = needVerifyPassword;
        }

        public void setDataSource(List<SearchResultGuideItemEntry> searchEntries) {
            mSearchEntries = searchEntries;
        }

        @NonNull
        @Override
        public COUIRecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
            View view = LayoutInflater.from(mContext).inflate(R.layout.search_list_item_result, viewGroup, false);
            return new GuideItem(view);
        }

        @Override
        public void onBindViewHolder(@NonNull COUIRecyclerView.ViewHolder viewHolder, int position) {
            SearchResultGuideItemEntry searchResultGuideItemEntry = getSearchEntry(position);
            if (searchResultGuideItemEntry == null) {
                return;
            }
            GuideItem item = (GuideItem) viewHolder;
            item.setCargo(searchResultGuideItemEntry);
            COUICardListHelper.setItemCardBackground(
                    item.itemView,
                    COUICardListHelper.getPositionInGroup(getItemCount(), position)
            );
            int type = searchResultGuideItemEntry.getType();
            if (type == TYPE_CHILD_LABEL) {
                item.setChildSize();
            } else {
                item.setNormalSize();
            }
            //显示和隐藏分割线
            if (position == (getItemCount() - 1)) {
                item.itemDivider.setVisibility(View.GONE);
            } else {
                item.itemDivider.setVisibility(View.VISIBLE);
            }
            if (searchResultGuideItemEntry != null) {
                int albumType = searchResultGuideItemEntry.getAlbumType();
                if ((type == TYPE_ALBUM) && (albumType == AlbumEntry.TYPE_RECYCLE_ALBUM)) {
                    if (mNeedVerifyPassword) {
                        item.mStatusIcon.setVisibility(View.VISIBLE);
                        item.mTxvCount.setVisibility(View.GONE);
                    } else {
                        item.mTxvCount.setVisibility(View.VISIBLE);
                        item.mStatusIcon.setVisibility(View.GONE);
                    }
                } else {
                    item.mTxvCount.setVisibility(View.VISIBLE);
                    item.mStatusIcon.setVisibility(View.GONE);
                }
            }
        }

        @Override
        public int getItemCount() {
            return (mSearchEntries == null) ? 0 : mSearchEntries.size();
        }

        public SearchResultGuideItemEntry getSearchEntry(int position) {
            return (mSearchEntries != null) ? mSearchEntries.get(position) : null;
        }

        private void clear() {
            if ((mSearchEntries != null) && (!mSearchEntries.isEmpty())) {
                mSearchEntries.clear();
                notifyDataSetChanged();
            }
        }
    }

    public interface FirstItemDrawCallback {
        void firstItemBind(View itemView);
    }

    public class SearchResultGridAdapter extends COUIRecyclerView.Adapter<SearchResultGridAdapter.GridItem> {
        private final boolean mIsAscOrder = isAscOrder();
        private final List<SearchResultGridItemEntry> mSearchResultGridItemEntries = new ArrayList<>();
        private String mIdList = null;
        private String mMediaIdList = null;

        private Drawable mPlaceholder;
        private boolean mIsFirstBind = true;

        /**
         * 是否是多模态搜索结果
         */
        private boolean mIsMultiModalResult = false;

        private FirstItemDrawCallback mFirstItemDrawCallback;

        public void setFirstItemDrawCallback(FirstItemDrawCallback firstItemDrawCallback) {
            this.mFirstItemDrawCallback = firstItemDrawCallback;
        }

        public SearchResultGridAdapter(Context context, IStylePool stylePool) {
            mPlaceholder = new StyleDrawableMaker(stylePool.getStyle(StyleType.TYPE_RECT_THUMB_STYLE))
                    .generateDrawable(new ColorDrawable(context.getColor(com.oplus.gallery.basebiz.R.color.base_placeholder_color)), 0, null);
        }

        public void setDataSource(
                List<SearchResultGridItemEntry> searchResultGridItemEntries,
                boolean isMultiModalResult
        ) {
            mSearchResultGridItemEntries.clear();
            StringBuilder idStrBuilder = new StringBuilder();
            StringBuilder mediaIdStrBuilder = new StringBuilder();
            int size = Math.min(searchResultGridItemEntries.size(), mGridLayoutMaxRow * mGridLayoutColumn);
            for (int i = 0; i < size; i++) {
                SearchResultGridItemEntry gridItemEntry = searchResultGridItemEntries.get(i);
                mSearchResultGridItemEntries.add(gridItemEntry);
                idStrBuilder.append(gridItemEntry.getId()).append(SYMBOL_COMMA);
                mediaIdStrBuilder.append(gridItemEntry.getMediaId()).append(SYMBOL_COMMA);
            }
            if (-1 != idStrBuilder.lastIndexOf(SYMBOL_COMMA)) {
                idStrBuilder.deleteCharAt(idStrBuilder.lastIndexOf(SYMBOL_COMMA));
            }
            if (-1 != mediaIdStrBuilder.lastIndexOf(SYMBOL_COMMA)) {
                mediaIdStrBuilder.deleteCharAt(mediaIdStrBuilder.lastIndexOf(SYMBOL_COMMA));
            }

            mIdList = idStrBuilder.toString().trim();
            mMediaIdList = mediaIdStrBuilder.toString().trim();
            mIsMultiModalResult = isMultiModalResult;
        }

        @Override
        public long getItemId(int position) {
            return mSearchResultGridItemEntries.get(getItemPosition(position)).hashCode();
        }

        /**
         * 根据path获取对应的index
         * path:/local/item/image/8035
         * @param path
         * @return
         */
        int getItemIndex(@Nullable Path path) {
            if (path == null) {
                return INVALID_INDEX;
            }
            String[] idStrArr = mIdList.split(SYMBOL_COMMA);
            String suffix = path.getSuffix();
            for (int i = 0; i < idStrArr.length; i++) {
                if (Objects.equals(suffix, idStrArr[i])) {
                    return getItemPosition(i);
                }
            }
            return INVALID_INDEX;
        }

        @NotNull
        @Override
        public GridItem onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.search_result_grid_item, parent, false);
            return new GridItem(view);
        }

        @Override
        public void onBindViewHolder(GridItem holder, int position) {
            Context context = holder.itemView.getContext();
            SearchResultGridItemEntry searchResultGridItemEntry = mSearchResultGridItemEntries.get(getItemPosition(position));

            SlotView slotView = holder.mSlotView;
            ViewGroup.LayoutParams layoutParams = slotView.getLayoutParams();
            layoutParams.height = mGridLayoutItemWidth;
            layoutParams.width = mGridLayoutItemWidth;
            slotView.setLayoutParams(layoutParams);

            prepareSlotView(context, slotView, searchResultGridItemEntry);

            slotView.setBackground(mPlaceholder);

            // 缩图加载
            mSearchActivityLinker.getWorkerSession().submit(new ThumbnailLoader(
                    searchResultGridItemEntry.getId(),
                    searchResultGridItemEntry.getIdType(),
                    slotView));

            if (position == 0 && mIsFirstBind) {
                if (mFirstItemDrawCallback != null) {
                    mFirstItemDrawCallback.firstItemBind(holder.itemView);
                    mIsFirstBind = false;
                }
            }

            holder.mSlotView.setOnClickListener(v -> {
                if ((mSearchActivityLinker != null) && (mSearchResultEntry != null)) {
                    mSearchActivityLinker.onClickGridItem(mSearchResultEntry.getFirstQuery(), mIsMultiModalResult);
                }
                startPicture(position,
                        mIdList,
                        mMediaIdList,
                        searchResultGridItemEntry.getId(),
                        searchResultGridItemEntry.getIdType(),
                        mIsMultiModalResult);
            });
        }

        private int getItemPosition(int position) {
            int itemPosition = position;
            if (mIsAscOrder) {
                itemPosition = (getItemCount() - 1 - position);
            }
            return itemPosition;
        }

        @Override
        public int getItemCount() {
            return mSearchResultGridItemEntries.size();
        }

        @Override
        public int getItemViewType(int position) {
            return mSearchResultGridItemEntries.get(getItemPosition(position)).getId();
        }

        private void clear() {
            if (!mSearchResultGridItemEntries.isEmpty()) {
                mSearchResultGridItemEntries.clear();
                notifyDataSetChanged();
            }
        }

        @Override
        public void onViewRecycled(@NonNull GridItem holder) {
            holder.mSlotView.setImageDrawable(null);
            super.onViewRecycled(holder);
        }

        public class GridItem extends COUIRecyclerView.ViewHolder {
            public final SlotView mSlotView;

            public GridItem(View itemView) {
                super(itemView);
                mSlotView = itemView.findViewById(R.id.search_grid_item_img);
            }
        }

        /**
         * 显示在图片上的各种标签
         * @param context
         * @param slotView
         * @param searchResultGridItemEntry
         */
        private void prepareSlotView(Context context, SlotView slotView, SearchResultGridItemEntry searchResultGridItemEntry) {
            boolean isShowTypeIcon = searchResultGridItemEntry.getIconType() != ResourcesCompat.ID_NULL;
            slotView.setShowTypeIcon(isShowTypeIcon);
            if (isShowTypeIcon) {
                slotView.setTypeIconDrawable(SlotOverlayHelper.INSTANCE.loadMediaIconDrawable(context, searchResultGridItemEntry.getIconType()));
            }

            boolean isFavorite = searchResultGridItemEntry.isFavorite();
            slotView.setShowRightTopMarkIcon(isFavorite);
            if (isFavorite) {
                slotView.setRightTopMarkIconDrawable(ContextCompat.getDrawable(context, base_icon_albumfavorite_small));
            }

            boolean isVideoType = (searchResultGridItemEntry.getIdType() == MEDIA_TYPE_VIDEO);
            slotView.setShowTimeText(isVideoType);
            if (isVideoType) {
                slotView.setDurationText(searchResultGridItemEntry.getDurationText());
            }

            int localFileStatus = searchResultGridItemEntry.getLocalFileStatus();
            boolean isCloud = SlotOverlayHelper.INSTANCE.needShowCloudIcon(localFileStatus);
            slotView.setCloudIconShow(isCloud);
            boolean isDrm = searchResultGridItemEntry.isDRM();
            slotView.setShowDrmIcon(isDrm && !isCloud);
            if (isDrm && !isCloud) {
                slotView.setDrmIconDrawable(ContextCompat.getDrawable(context, base_icon_drm));
            }
            if (isCloud) {
                slotView.setCloudIconDrawable(ContextCompat.getDrawable(context, base_icon_cloud));
            }
        }
    }

    /**
     * 跳转大图
     */
    private void startPicture(int position, String galleryIdList, String mediaIdList, int id, int mediaType, boolean isMultiModalResult) {
        GLog.d(TAG, "startPicture , position = " + position + "galleryIdList = " + galleryIdList + ", id = " + id);
        Path path = null;
        if (isMultiModalResult) {
            path = Local.PATH_ALBUM_SEARCH_MULTI_MODAL_RESULT.getChild(galleryIdList.hashCode());
            SearchMultiModalResultAlbum album = (SearchMultiModalResultAlbum) DataManager.getMediaSet(path);
            if (album == null) {
                GLog.e(TAG, "startPicture, goToMultiModalAlbumPage error, album is null, path = " + path);
                return;
            }
            album.setInputEntry(new ExtOrderIdsPictureAlbum.OrderIdsPictureInputEntry(mediaIdList));
        } else {
            path = Local.PATH_ALBUM_SEARCH_RESULT.getChild(galleryIdList.hashCode());
            SearchResultAlbum album = (SearchResultAlbum) DataManager.getMediaSet(path);
            if (album == null) {
                GLog.e(TAG, "startPicture, goToAlbumPage error, album is null, path = " + path);
                return;
            }
            album.setInputEntry(new SearchResultAlbum.SearchResultAlbumEntry(galleryIdList,
                    SearchKeywordHelper.getKeywords(), TYPE_ALL_PHOTO, false, isAscOrder()));
        }

        Path itemPath = null;
        if (mediaType == MEDIA_TYPE_VIDEO) {
            SearchTrackHelper.trackSearchClickVideo(mCurrentQuery);
            itemPath = LocalVideo.ITEM_PATH.getChild(id);
        } else {
            SearchTrackHelper.trackSearchClickPicture(mCurrentQuery);
            itemPath = LocalImage.ITEM_PATH.getChild(id);
        }
        Bundle bundle = new Bundle();
        bundle.putString(IntentConstant.ViewGalleryConstant.KEY_MEDIA_SET_PATH, path.toString());
        bundle.putString(IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH, itemPath.toString());
        bundle.putInt(IntentConstant.PicturePageConstant.KEY_INDEX_HINT, position);
        bundle.putBoolean(IntentConstant.PicturePageConstant.KEY_ENTER_PHOTO_ANIMATE, true);
        bundle.putString(IntentConstant.PicturePageConstant.KEY_POSITION_CONTROLLER, mPositionControllerKey);
        bundle.putParcelable(IntentConstant.PicturePageConstant.KEY_OPEN_ANIMATION_RECT, getItemRect(position, true));

        if (mSearchActivityLinker != null) {
            mSearchActivityLinker.setIsJumpToPhotoPage(true);
        }

        startByStack(
                null,
                BaseActivityKt.findFullScreenContainerId(getActivity()),
                new PostCard(PICTURE_FRAGMENT),
                DEFAULT_TAG,
                null,
                bundle,
                new int[]{INT_ZERO, INT_ZERO, INT_ZERO, INT_ZERO}
        );
    }

    private class TaskQueueDispatcher {
        public void handle(Message msg) {
            switch (msg.what) {
                case MSG_QUERY_TASK:
                    ThreadPool.Job job = (ThreadPool.Job) msg.obj;
                    if (job != null) {
                        job.run(ThreadPool.createJobContextStub());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private class ThumbnailLoader implements Job<Bitmap> {
        private WeakReference<ImageView> mRefImageView = null;
        private int mId = 0;
        private int mMediaType = 0;

        ThumbnailLoader(int id, int mediaType, ImageView imageView) {
            mId = id;
            mRefImageView = new WeakReference<>(imageView);
            mMediaType = mediaType;
        }

        @Nullable
        @Override
        public Bitmap call(@NotNull JobContext jc) {
            Context context = getContext();
            ImageView imageView = mRefImageView.get();
            if ((context == null) || (imageView == null)) {
                return null;
            }
            Path imagePath = LocalImage.ITEM_PATH.getChild(mId);
            Path videoPath = LocalVideo.ITEM_PATH.getChild(mId);

            Path path = imagePath;
            if (mMediaType == MEDIA_TYPE_VIDEO) {
                path = videoPath;
            }
            MediaItem mediaItem = LocalMediaDataHelper.getLocalMediaItem(path);

            if (mediaItem == null) {
                // This path is invalid, reset it in cause DataManager return an empty content MediaItem in next query
                imagePath.resetObjectFromFileManager();
                return null;
            }

            if (mSearchActivityLinker != null) {
                int thumbnailType = 0;
                if (FaceItem.class.getName().equals(mediaItem.getItemType())) {
                    thumbnailType = ThumbnailSizeUtils.TYPE_FACE_THUMBNAIL;
                } else {
                    thumbnailType = mMicroThumbnailType;
                }
                Bitmap bitmap = new ThumbnailJob(context, mediaItem, thumbnailType).call(jc);
                if (bitmap != null) {
                    int fullAngles = 360;
                    int rotation = MediaItemExtKt.getThumbnailRotation(mediaItem);
                    if (rotation % fullAngles != 0) {
                        bitmap = SearchCommonUtils.rotateBitmap(bitmap, rotation, true);
                    }
                    mHandler.obtainMessage(MSG_UPDATE_GRID_AVATAR, new UpdateAvatarMsgEntry(imageView, bitmap, 0)).sendToTarget();
                }
            }
            return null;
        }
    }

    private class GuideItem extends COUIRecyclerView.ViewHolder {
        private WeakReference<SearchResultGuideItemEntry> mRefSearchResultEntry = null;
        private ImageView mImgAvatar = null;
        private TextView mTxvName = null;
        private TextView mTxvCount = null;
        private View itemDivider = null;
        private ImageView mStatusIcon = null;
        private int mHintColor = 0;

        GuideItem(View view) {
            super(view);
            mImgAvatar = view.findViewById(R.id.image_avatar);
            mTxvName = view.findViewById(R.id.result_name);
            mTxvCount = view.findViewById(R.id.result_count);
            itemDivider = view.findViewById(R.id.item_divider);
            mHintColor = view.getContext().getResources().getColor(R.color.search_result_item_text_height_light_color, null);
            mStatusIcon = view.findViewById(R.id.status_icon);
            view.setOnClickListener(v -> {
                if (mRefSearchResultEntry == null) {
                    return;
                }
                onItemClicked(mRefSearchResultEntry.get());
            });
        }

        private void setChildSize() {
            setSize(R.dimen.search_result_guide_child_item_margin_start);
        }

        private void setNormalSize() {
            setSize(R.dimen.search_result_guide_item_margin_start);
        }

        private void setSize(int startMargin) {
            RelativeLayout.LayoutParams contentLayoutParams = (RelativeLayout.LayoutParams) mImgAvatar.getLayoutParams();
            contentLayoutParams.setMarginStart(itemView.getResources().getDimensionPixelSize(startMargin));
            mImgAvatar.setLayoutParams(contentLayoutParams);
        }

        @SuppressLint("UseCompatLoadingForDrawables")
        public void setCargo(SearchResultGuideItemEntry searchResultGuideItemEntry) {
            mRefSearchResultEntry = new WeakReference<>(searchResultGuideItemEntry);
            if (searchResultGuideItemEntry != null) {
                String count = String.format(Locale.getDefault(), "%d", searchResultGuideItemEntry.getCount());
                mTxvCount.setText(count);
                List<CharSequence> words = mSearchActivityLinker.getWordPackage();
                StringBuilder idStrBuilder = new StringBuilder();
                if ((words != null) && (words.size() > 0)) {
                    words.forEach(idStrBuilder::append);
                }
                //二次搜索时，前一个词包不显示
                String title = searchResultGuideItemEntry.getName();
                if (title.contains(idStrBuilder.toString()) && !title.equals(idStrBuilder.toString())) {
                    title = title.replace(idStrBuilder.toString(), "").trim();
                }
                if (title.isEmpty()) {
                    title = searchResultGuideItemEntry.getName();
                }
                setHighLightTitle(searchResultGuideItemEntry, title);
                setGuideImageIcon(searchResultGuideItemEntry);
            }
        }

        private void setGuideImageIcon(SearchResultGuideItemEntry searchResultGuideItemEntry) {
            int resultType = searchResultGuideItemEntry.getType();
            int resId = -1;
            switch (resultType) {
                case TYPE_LOCATION:
                    resId = R.drawable.search_result_location_icon;
                    break;
                case TYPE_ALBUM:
                    resId = R.drawable.search_result_album_icon;
                    if (searchResultGuideItemEntry.getAlbumType() == AlbumEntry.TYPE_RECYCLE_ALBUM) {
                        resId = R.drawable.search_recommend_icon_album_delete;
                    }
                    break;
                case TYPE_CHILD_LABEL:
                case TYPE_LABEL:
                    resId = R.drawable.search_result_label_icon;
                    break;
                case TYPE_MEMORIES_ALBUM:
                    resId = R.drawable.search_result_memory_icon;
                    break;
                case TYPE_PERSON:
                    resId = R.drawable.search_result_communication_person_icon;
                    break;
                case TYPE_DATETIME:
                    resId = R.drawable.search_result_date_icon;
                    break;
                case TYPE_MULTI_LABEL:
                    resId = R.drawable.search_default_icon;
                    break;
                case TYPE_OCR:
                    resId = R.drawable.search_result_ocr_icon;
                    break;
                case TYPE_SPECIAL_LABEL:
                    resId = R.drawable.search_result_special_type_icon;
                    break;
                case TYPE_FILE_NAME:
                    resId = R.drawable.search_result_file_name_icon;
                    break;
                default:
                    resId = R.drawable.search_default_icon;
                    GLog.e(TAG, "setCargo,don't know the type:" + resultType);
            }
            if (resId != -1) {
                mImgAvatar.setImageDrawable(ContextCompat.getDrawable(getContext(), resId));
            }
        }

        public void setHighLightTitle(SearchResultGuideItemEntry searchResultGuideItemEntry, String title) {
            String keyword = mCurrentQuery;
            if (keyword == null) {
                mTxvName.setText((title == null) ? "" : title);
                return;
            }
            if (searchResultGuideItemEntry.getType() == TYPE_OCR) {
                mTxvName.setText(getHighLightTextSpannable(title, getResources().getString(R.string.search_ocr_result)));
                return;
            }
            if ((searchResultGuideItemEntry.getType() == TYPE_FILE_NAME)
                    || (searchResultGuideItemEntry.getSecondResultEntryType() == TYPE_FILE_NAME)) {
                mTxvName.setText(getHighLightTextSpannable(title, getResources().getString(R.string.search_photo_name_result)));
                return;
            }
            Spannable spannableText = new SpannableString(title);
            int totalLength = spannableText.length();
            String[] normKeywordArray = splitKeyword(keyword);
            boolean isLowerLengthDifferent = isLowerLengthDifferent(title);
            String originalString = title;
            title = title.toLowerCase();
            for (int i = 0; i < normKeywordArray.length; i++) {
                String normKeyword = normKeywordArray[i].toLowerCase().trim();
                int len = normKeyword.length();
                int index = title.indexOf(normKeyword);
                int actualIndex = index;
                int actualEnd = index + len;
                while (index != -1) {
                    //bugId: 1831970, in some language like Turkey, toLowerCase will got different length
                    if (isLowerLengthDifferent) {
                        actualIndex = index + getLengthDiffFromLowerCase(originalString, 0, index);
                    } else {
                        actualIndex = index;
                    }
                    actualEnd = actualIndex + len;
                    if (actualEnd > totalLength) {
                        actualEnd = totalLength;
                    }

                    spannableText.setSpan(new ForegroundColorSpan(mHintColor), actualIndex, actualEnd, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    index = title.indexOf(normKeyword, index + 1);
                }
            }
            mTxvName.setText(spannableText);
        }

        /**
         * check if String.toLowerCase have changed length
         *
         * @param target
         * @return boolean
         */
        private boolean isLowerLengthDifferent(String target) {
            return target.length() != target.toLowerCase().length();
        }

        private int getLengthDiffFromLowerCase(String target, int start, int end) {
            if ((end < start) || (end > target.length())) {
                return 0;
            }
            String subString = target.substring(start, end);
            return subString.length() - subString.toLowerCase().length();
        }

        private Spannable getHighLightTextSpannable(String title, String format) {
            int len = title.length();
            int index = format.indexOf("%s");
            if (index == -1) {
                return new SpannableString(title);
            }
            String highLightTitle = String.format(format, title);
            Spannable spannableText = new SpannableString(highLightTitle);
            spannableText.setSpan(new ForegroundColorSpan(mHintColor), index, index + len, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            return spannableText;
        }

        private String[] splitKeyword(String keywords) {
            String[] normKeywordArray = null;
            try {
                normKeywordArray = keywords.split(SearchCommonUtils.SPACE_REGEX);
            } catch (PatternSyntaxException exp) {
                GLog.e(TAG, "splitKeyword, keywords split error:", exp);
                normKeywordArray = new String[]{keywords};
            }

            List<String> uniqueNormKeywords = new ArrayList<>();
            if ((normKeywordArray != null) && (normKeywordArray.length > 0)) {
                for (String normKeyword : normKeywordArray) {
                    if (!uniqueNormKeywords.contains(normKeyword) && !normKeyword.isEmpty()) {
                        uniqueNormKeywords.add(normKeyword);
                    }
                }
                if (!uniqueNormKeywords.contains(keywords) && !keywords.contains(" ")) {
                    uniqueNormKeywords.add(keywords);
                }
            }
            return uniqueNormKeywords.toArray(new String[0]);
        }
    }

    private static class SearchResultStatistics {
        private String mQuery;
        private int mEntryType;
        private String mResultJson;
    }

    private void onItemClicked(SearchResultGuideItemEntry entry) {
        if ((mSearchActivityLinker != null) && (entry != null)) {
            int type = entry.getType();
            // 用户点击联想词时，将对应的信息存储到GuideClickedKeyWordEntry中
            KeywordEntry keywordEntry = entry.getKeywordEntry();
            if ((keywordEntry != null) && !keywordEntry.isMultiSearch) {
                mGuideClickedKeyWordEntry = keywordEntry;
                mGuideClickedKeyWordEntry.type = (keywordEntry.type == TYPE_CHILD_LABEL) ? TYPE_LABEL : type;
            }
            mSearchActivityLinker.onSearchGuideCLicked(entry);
            if (mStatistics != null) {
                SearchTrackHelper.trackClickSearchRecommend(
                        mStatistics.mQuery,
                        mappingSearchResultType(type),
                        TextUtils.isEmpty(mStatistics.mResultJson) ? Value.RESULT_FAIL : Value.RESULT_SUCC,
                        mappingSearchResultWordType(entry.getName(), type));
            }
        }
    }

    private void onItemClicked(SearchResultGridGroupEntry entry) {
        if ((mSearchActivityLinker != null) && (entry != null)) {
            String firstQuery = entry.getFirstQuery();
            String bundle = entry.getIdList();
            String mediaIdsString = entry.getItemEntries().stream()
                    .map(curEntry -> String.valueOf(curEntry.getMediaId()))
                    .collect(Collectors.joining(SYMBOL_COMMA));
            int type = entry.getType();
            ArrayList<String> queryList = (ArrayList<String>) entry.getQueryList();
            mSearchActivityLinker.onViewAllButtonClicked(firstQuery, bundle, mediaIdsString, type, queryList, entry.isMultiModalResult());
        }
    }

    private boolean isAndesSearchEnable() {
        return ConfigAbilityWrapper.getBoolean(FeatureSwitch.FEATURE_IS_SUPPORT_ANDES_SEARCH, false, false);
    }


    /**
     * 是否允许拉起Ai控件
     * 支持融合搜索 && OS版本大于13
     * @return ture：支持拉起Ai控件 false：不支持拉起Ai控件
     */
    private boolean isAiAskEnable() {
        return isAndesSearchEnable() && OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_13_0);
    }

    /**
     * First, query the keywords what are user given
     * Second, if query is fail, query again with auto completion keywords
     */
    private class SearchResultLoader implements ThreadPool.Job<Void> {
        private int mEntryType = ENTRY_TYPE_UNKNOWN;
        private int mType = TYPE_MASK_TYPE;
        private boolean mForceQuery = false;
        private String mQuery = null;
        private String[] mKeywords = null;
        private int mGroupId = -1;
        private int mBucketId = -1;
        private int mMemoryId = -1;
        private int mLabelId = -1;
        private boolean mNeedNewAdapter = false;
        private boolean mNeedForceUpdate = false; // 是否一定要刷新数据

        private void setSpecTypeId(int searchType, SearchResultGuideItemEntry entry) {
            // 融合搜索特定类型查询，必须传递对应ID
            if (entry != null) {
                mType = entry.getType();
                if (searchType == TYPE_PERSON) {
                    mGroupId = entry.getId();
                } else if (searchType == TYPE_ALBUM) {
                    mBucketId = entry.getId();
                } else if (searchType == TYPE_MEMORIES_ALBUM) {
                    mMemoryId = entry.getCoverMemoriesId();
                } else if (searchType == TYPE_LABEL) {
                    if (entry.getId() > 0) {
                        mLabelId = entry.getId();
                    } else {
                        mLabelId = LabelSearchEngine.INVALID_ID;
                    }
                }
            }
        }

        SearchResultLoader(String query, int entryType, boolean needNewAdapter, boolean needForceUpdate, int type,
                           SearchResultGuideItemEntry entry, boolean forceQuery) {
            if (isAndesSearchEnable()) {
                if (entry != null) {
                    setSpecTypeId(entry.getType(), entry);
                }
            } else {
                mType = type;
            }
            mQuery = query;
            mEntryType = entryType;
            mForceQuery = forceQuery;
            mNeedNewAdapter = needNewAdapter;
            mNeedForceUpdate = needForceUpdate;
        }

        SearchResultLoader(SearchResultGuideItemEntry entry, String query, String[] keywords, int entryType, boolean forceQuery) {
            if (isAndesSearchEnable()) {
                if (entry != null) {
                    setSpecTypeId(entry.getType(), entry);
                }
            }
            mQuery = query;
            mKeywords = keywords;
            mEntryType = entryType;
            mForceQuery = forceQuery;
        }

        @Override
        public Void run(ThreadPool.JobContext jc) {
            Activity activity = getActivity();
            if (activity == null) {
                return null;
            }

            String selection = null;
            String[] selectionArgs = null;
            String[] keywords = mKeywords;
            if ((keywords == null) || (keywords.length <= 0)) {
                int groupId = mGroupId;
                if (SearchKeywordHelper.isUnknownPerson(activity, mQuery)) {
                    groupId = SearchKeywordHelper.getPersonGroupId();
                    GLog.d(TAG, "[SearchResultLoader] getPersonGroupId = " + SearchKeywordHelper.getPersonGroupId() + ", mQuery = " + mQuery);
                }
                StringBuilder searchParameter = new StringBuilder();
                // 当搜索特定type，点击引导词，记录当此KeyWordEntry，当再次搜索的Query没有构建新的Entry，沿用type会导致特定类型搜索无结果，这里要重置type
                if (mGuideClickedKeyWordEntry != null) {
                    mType = mQuery.equals(mGuideClickedKeyWordEntry.name) ? mType : TYPE_MASK_TYPE;
                }
                searchParameter.append(mappingType(mType));
                searchParameter.append("?");
                searchParameter.append(QUERY_INPUT + "=" + Uri.encode(mQuery));
                searchParameter.append("&");
                searchParameter.append(QUERY_GROUPID + "=" + groupId);
                searchParameter.append("&");
                searchParameter.append(QUERY_BUCKETID + "=" + mBucketId);
                searchParameter.append("&");
                searchParameter.append(QUERY_MEMORYID + "=" + mMemoryId);
                searchParameter.append("&");
                searchParameter.append(QUERY_LABEL_ID + "=" + mLabelId);
                searchParameter.append("&");
                searchParameter.append(QUERY_FORCE + "=" + mForceQuery);
                selection = SUGGESTION_SELECTION_KEYWORD;
                selectionArgs = new String[]{searchParameter.toString()};
            } else {
                selection = SUGGESTION_SELECTION_SLOT_FILTER;
                selectionArgs = keywords;
            }
            GLog.d(TAG, "[SearchResultLoader] mQuery = " + mQuery + "; selection : " + selection);
            generateResult(activity, selection, selectionArgs);
            return null;
        }

        /**
         * 查询、解析并发送搜索结果
         */
        private void generateResult(Activity activity, String selection, String[] selectionArgs) {
            GLog.d(TAG, LogFlag.DL, "[generateResult] start");
            Cursor cursor = null;
            try {
                // 1. 查询
                long startTime = System.currentTimeMillis();
                cursor = activity.getContentResolver().query(BASE_URI, null, selection, selectionArgs, null);
                RealShowTimeInstrument.endRecord(RealShowTimeInstrument.SPECIAL_TYPE_SEARCH);
                RealShowTimeInstrument.endRecord(RealShowTimeInstrument.SEARCH);
                GLog.d(TAG, "[generateResult] query costTime = " + GLog.getTime(startTime));
                if (cursor == null) {
                    GLog.d(TAG, "[generateResult] cursor is null!");
                    return;
                }
                List<String> labelGuide = null;
                List<SearchResult> resultList = new ArrayList<>();
                List<SearchResultGuideItemEntry> resultGuideEntries = new ArrayList<>();
                Map<Integer, List<SearchResultRecommendItemEntry>> recommendItemMap = new HashMap<>();

                int indexId = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_ID);
                int indexType = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_TYPE);
                int indexName = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_NAME);
                int indexCount = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_GALLERY_COUNT);
                int indexIdList = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_GALLERY_ID_LIST);
                int indexMediaIdList = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_ID_LIST);
                int indexCoverGalleryId = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_GALLERY_ID);
                int indexCoverMemoriesId = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_MEMORIES_ID);
                int indexCoverPersonId = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_COVER_PERSON_ID);
                int indexAlbumType = cursor.getColumnIndex(SearchSuggestionProviderUtil.SEARCH_RESULT_ALBUM_TYPE);
                int indexMediaType = cursor.getColumnIndex(SearchSuggestionProviderUtil.COLUMN_MEDIA_TYPE);
                StringBuilder resultJson = new StringBuilder();
                if ((indexName == -1) || (indexCount == -1) || (indexIdList == -1)
                        || (indexType == -1) || (indexAlbumType == -1)) {
                    GLog.d(TAG, "[generateResult] invalid index!");
                    return;
                }
                if (indexCoverGalleryId == -1 && indexCoverMemoriesId == -1 && indexCoverPersonId == -1) {
                    GLog.d(TAG, "[generateResult] invalid index! "
                            + "There must be at least one CoverGalleryId,CoverMemoriesId and CoverPersonId");
                    return;
                }
                StringBuilder builderAlbumType = new StringBuilder();
                StringBuilder idStrForGrid = new StringBuilder();
                // 是否是多关键字搜索结果
                boolean isMultiLabel = false;
                // 统计模糊搜索结果的数量
                int fuzzySearchResultCount = 0;
                // 获取融合搜索结果--显示在中间Grid区域
                if (isAndesSearchEnable()) {
                    ArrayList<CompositeData> compositeDataList = cursor.getExtras().getParcelableArrayList(SEARCH_COMPOSITE_RESULT_ENTRIES);
                    if (compositeDataList == null) {
                        GLog.w(TAG, LogFlag.DL, () -> "compositeDataList is null");
                    } else {
                        for (int i = 0; i < compositeDataList.size(); i++) {
                            idStrForGrid.append(compositeDataList.get(i).galleryId).append(SYMBOL_COMMA);
                            if (GProperty.getDEBUG_SEARCH()) {
                                CompositeData compositeData = compositeDataList.get(i);
                                GLog.d(TAG, LogFlag.DL, () -> "ID:" + compositeData.galleryId + ", score:" + compositeData.rrfScore);
                            }
                        }
                    }
                }
                // 2. 解析cursor，填充引导数据 guideItemEntry 和 推荐数据 recommendItemMap
                while (cursor.moveToNext()) {
                    String name = cursor.getString(indexName);
                    String idList = cursor.getString(indexIdList);
                    String mediaIdList = cursor.getString(indexMediaIdList);
                    // 当结果是人物的时候，此id就是group_id
                    int id = cursor.getInt(indexId);
                    int type = cursor.getInt(indexType);
                    int count = cursor.getInt(indexCount);
                    int coverPersonId = cursor.getInt(indexCoverPersonId);
                    int coverMemoriesId = cursor.getInt(indexCoverMemoriesId);
                    int coverGalleryId = cursor.getInt(indexCoverGalleryId);
                    int albumType = cursor.getInt(indexAlbumType);
                    int mediaType = cursor.getInt(indexMediaType);
                    GLog.d(TAG, "[generateResult] mQuery = " + mQuery + ", type = " + type
                            + ", count = " + count + ", name = " + name
                            + ", coverPersonId = " + coverPersonId
                            + ", coverMemoriesId = " + coverMemoriesId
                            + ", coverGalleryId = " + coverGalleryId
                            + ", albumType = " + albumType);

                    String strAlbumType = builderAlbumType.toString();
                    String tempAlbumType = getSearchResultType(type);
                    if (!strAlbumType.contains(tempAlbumType)) {
                        if (!TextUtils.isEmpty(strAlbumType)) {
                            builderAlbumType.append(CommonTrackConstant.SYMBOL_HORIZONTAL_LINE);
                        }
                        builderAlbumType.append(tempAlbumType);
                    }
                    if (isFuzzySearchResultType(type)) {
                        fuzzySearchResultCount++;
                    }
                    if (type == TYPE_MULTI_LABEL) {
                        isMultiLabel = true;
                    }

                    // 引导标签显示时，表示无搜索结果的类型
                    if (type == TYPE_GUIDE_LABEL) {
                        List<String> labelArray = SpecialSplitter.fastSplit(idList, ',');
                        labelGuide = new ArrayList<>(labelArray);
                    } else {
                        if (TextUtils.isEmpty(name)) {
                            GLog.d(TAG, "[generateResult] result name is empty");
                            continue;
                        }

                        SearchResultGuideItemEntry guideItemEntry = new SearchResultGuideItemEntry(id, name.trim(), idList,
                                type, count, coverPersonId, coverMemoriesId, coverGalleryId, albumType, mediaType);
                        SearchResultRecommendItemEntry recommendItemEntry = new SearchResultRecommendItemEntry(id, name.trim(),
                                idList, mediaIdList, type, count, coverPersonId, coverMemoriesId, coverGalleryId, albumType, mediaType);
                        if (cursor.getExtras() != null) {
                            if (filterSearchResult(cursor, name, id, type, albumType, guideItemEntry)) {
                                continue;
                            }
                            getResultEntriesFromCursorExtras(cursor, guideItemEntry, recommendItemEntry);
                        }

                        // 搜索结果图片展示区不显示“最近删除”中的图片数量 + 非融合搜索（融合搜索Grid区数据从extra取）
                        if ((albumType != AlbumEntry.TYPE_RECYCLE_ALBUM) && !isAndesSearchEnable()) {
                            idStrForGrid.append(idList).append(SYMBOL_COMMA);
                        }
                        // 模糊搜索的结果不加入 引导 和 推荐
                        if (isFuzzySearchResultType(type)) {
                            continue;
                        }
                        resultGuideEntries.add(guideItemEntry);
                        fillingRecommendMap(recommendItemMap, recommendItemEntry);
                        fillResultList(activity, resultList, recommendItemEntry);
                    }
                    resultJson.append(String.format("%s" + SearchTrackConstant.Value.PROPERTY_DIVIDER
                                    + "%s" + Value.PROPERTY_DIVIDER
                                    + "%s" + Value.RECORD_DIVIDER,
                            name, getSearchResultType(type), count));
                }

                SearchTrackHelper.trackSearchResult(mQuery, (resultList.size() > 0) ? "1" : "0", builderAlbumType.toString());
                if (isAndesSearchEnable()) {
                    SearchTrackHelper.trackSearchResultMultimodal(RESULT_MULTIMODAL_MATCHED);
                } else {
                    SearchTrackHelper.trackSearchResultMultimodal(RESULT_MULTIMODAL_MISMATCH);
                }

                // 3. 填充搜索结果（网格部分）
                long generateGridTime = System.currentTimeMillis();
                SearchResultGridGroupEntry searchResultGridGroupEntry = null;
                if ((idStrForGrid.length() > 0) && (-1 != idStrForGrid.lastIndexOf(SYMBOL_COMMA))) {
                    idStrForGrid.deleteCharAt(idStrForGrid.lastIndexOf(SYMBOL_COMMA));
                }
                searchResultGridGroupEntry = generateSearchGridResult(TYPE_NORMAL_ALBUM, TYPE_ALL_PHOTO,
                        idStrForGrid, getInputWords(isMultiLabel), isAndesSearchEnable());
                boolean isOnlyFuzzySearchResult = (fuzzySearchResultCount == cursor.getCount()) && (cursor.getCount() > 0);
                GLog.d(TAG, LogFlag.DL, () -> "generateResult, isOnlyFuzzySearchResult=" + isOnlyFuzzySearchResult
                        + ", generateGridTime, cost time:" + GLog.getTime(generateGridTime)
                );

                // 4. 处理 “引导” “推荐” 的结果
                if ((labelGuide == null) && !needForceUpdate() && isSameResultList(resultList, searchResultGridGroupEntry) && !isNeedSingleJump()) {
                    GLog.d(TAG, "[generateResult] SearchResultLoader is not needUpdate");
                    return;
                }
                mLastQueryResultList = resultList;
                long sortTime = System.currentTimeMillis();
                sortQueryResultByCount(resultGuideEntries);
                GLog.d(TAG, "[generateResult] sortQueryResultByCount, sortTime:" + GLog.getTime(sortTime));

                long generateRecommendTime = System.currentTimeMillis();
                List<SearchResultRecommendGroupEntry> resultRecommendGroupEntries = new ArrayList<>();
                if (!recommendItemMap.isEmpty()) {
                    generateSearchRecommendResult(recommendItemMap, resultRecommendGroupEntries);
                }
                GLog.d(TAG, "[generateResult] generateSearchRecommendResult, cost time:" + GLog.getTime(generateRecommendTime));

                // 刷新推荐搜索结果
                mHandler.obtainMessage(MSG_UPDATE_RESULT_LIST, labelGuide).sendToTarget();
                // 刷新引导、搜索结果和推荐
                sendMessageToUpdateResultList(resultGuideEntries, searchResultGridGroupEntry, resultRecommendGroupEntries, isOnlyFuzzySearchResult);
                // 将之前分散在推荐搜索和引导里面的页面 status 更新提取出来，之前他们两个是互斥的可以这样做。现在需要同时运行，会有冲突。
                Message updatePageStatusMsg = Message.obtain(mHandler, MSG_UPDATE_PAGE_STATUS);
                Bundle bundle = new Bundle();
                bundle.putInt(KEY_SUGGEST_LABELS_SIZE, (labelGuide == null ? 0 : labelGuide.size()));
                boolean hasSearchResult = !searchResultGridGroupEntry.getItemEntries().isEmpty()
                        || !resultGuideEntries.isEmpty() || !resultRecommendGroupEntries.isEmpty();
                bundle.putBoolean(KEY_HAS_SEARCH_RESULT, hasSearchResult);
                updatePageStatusMsg.setData(bundle);
                updatePageStatusMsg.sendToTarget();

                SearchResultStatistics statistics = new SearchResultStatistics();
                statistics.mQuery = mCurrentQuery;
                statistics.mEntryType = mEntryType;
                statistics.mResultJson = resultJson.toString();
                mStatistics = statistics;
            } catch (Exception e) {
                GLog.e(TAG, "[generateResult] an error occur during loading suggestion result cursor to dataSet", e);
            } finally {
                IOUtils.closeQuietly(cursor);
            }
        }

        /**
         * 判断是否模糊搜索类型的结果
         */
        private boolean isFuzzySearchResultType(int searchType) {
            return searchType == TYPE_MULTI_MODAL_SEARCH || searchType == TYPE_OCR_EMBEDDING;
        }

        private void sendMessageToUpdateResultList(
                List<SearchResultGuideItemEntry> resultGuideEntries,
                SearchResultGridGroupEntry searchResultGridGroupEntry,
                List<SearchResultRecommendGroupEntry> resultRecommendGroupEntries,
                boolean isOnlyFuzzySearchResult) {
            int multiModalResultCount = searchResultGridGroupEntry.getItemEntries().size();
            GLog.d(TAG, LogFlag.DL, () -> "sendMessageToUpdateResultList, guide=" + resultGuideEntries.size()
                    + ", search=" + multiModalResultCount + ", recommend=" + resultRecommendGroupEntries.size());

            Message updateGuideMessage = mHandler.obtainMessage(MSG_UPDATE_GUIDE_RESULT_LIST, resultGuideEntries);
            Bundle updateGuideBundle = new Bundle();
            updateGuideBundle.putBoolean(KEY_IS_ONLY_FUZZY_SEARCH_RESULT, isOnlyFuzzySearchResult);
            updateGuideBundle.putInt(KEY_MULTI_MODAL_RESULT_COUNT, multiModalResultCount);
            updateGuideMessage.setData(updateGuideBundle);
            updateGuideMessage.sendToTarget();

            mHandler.obtainMessage(MSG_UPDATE_GRID_RESULT_LIST, searchResultGridGroupEntry).sendToTarget();

            Message message = Message.obtain();
            message.what = MSG_UPDATE_RECOMMEND_RESULT_LIST;
            message.obj = resultRecommendGroupEntries;
            Bundle bundle = new Bundle();
            bundle.putBoolean(KEY_NEED_NEW_ADAPTER, mNeedNewAdapter);
            bundle.putBoolean(KEY_IS_ONLY_FUZZY_SEARCH_RESULT, isOnlyFuzzySearchResult);
            message.setData(bundle);
            mHandler.sendMessage(message);
        }

        private void fillResultList(Activity activity,
                                    List<SearchResult> resultList,
                                    SearchResultRecommendItemEntry recommendItemEntry) {
            SearchResult result = new SearchResult();
            result.mCoverPersonId = recommendItemEntry.getCoverPersonId();
            result.mCoverMemoriesId = recommendItemEntry.getCoverMemoriesId();
            result.mCoverGalleryId = recommendItemEntry.getCoverGalleryId();
            result.mCount = recommendItemEntry.getCount();
            result.mRotation = getRotationFromMediaId(activity, recommendItemEntry.getCoverMemoriesId(),
                    recommendItemEntry.getCoverGalleryId(), recommendItemEntry.getMediaType(),
                    recommendItemEntry.getType(), recommendItemEntry.getAlbumType());
            result.mAlbumType = recommendItemEntry.getAlbumType();
            resultList.add(result);
        }

        private void fillingRecommendMap(
                Map<Integer, List<SearchResultRecommendItemEntry>> recommendItemMap,
                SearchResultRecommendItemEntry recommendItemEntry) {
            int itemEntryType = recommendItemEntry.getType();
            if ((itemEntryType != TYPE_MULTI_LABEL)
                    && (itemEntryType != TYPE_FILE_NAME)
                    && (itemEntryType != TYPE_INDEX_SEARCH)
                    && (itemEntryType != TYPE_OTHER)) {
                List<SearchResultRecommendItemEntry> searchResultRecommendItemEntries = recommendItemMap.get(itemEntryType);
                if (searchResultRecommendItemEntries == null) {
                    ArrayList<SearchResultRecommendItemEntry> itemEntries = new ArrayList<>();
                    itemEntries.add(recommendItemEntry);
                    recommendItemMap.put(itemEntryType, itemEntries);
                } else {
                    searchResultRecommendItemEntries.add(recommendItemEntry);
                }
            }
        }

        /**
         * 过滤搜索结果
         * 当不处于搜索状态时，即用户点击了联想词生成词包或者二次搜索时，需要将与联想词不相关的搜索结果过滤掉
         */
        private boolean filterSearchResult(Cursor cursor, String name, int id, int type, int albumType, SearchResultGuideItemEntry guideItemEntry) {
            ArrayList<KeywordEntry> keywordEntries = cursor.getExtras().getParcelableArrayList(SEARCH_RESULT_KEYWORD_ENTRIES);
            if ((keywordEntries != null) && !keywordEntries.isEmpty()) {
                KeywordEntry keywordEntry = keywordEntries.get(cursor.getPosition());
                if (!mSearchActivityLinker.isInSearching() || keywordEntry.isMultiSearch
                        || (keywordEntry.type == SearchType.TYPE_OCR)) {
                    if (mGuideClickedKeyWordEntry != null) {
                        if (isAndesSearchEnable()) {
                            if (!keywordEntry.contains(mGuideClickedKeyWordEntry)) {
                                GLog.w(TAG, "filterSearchResult, generateResult, keywordEntry isNotSameTo guideClickedKeyWordEntry, name:"
                                        + name + ", type:" + type + ", id:" + id + ", albumType:" + albumType);
                                return true;
                            }
                        } else {
                            // 非融合搜索情况下，文件名等特定检索也是走的全维度检索，需要通过此条件来过滤掉其他结果
                            if (!keywordEntry.equals(mGuideClickedKeyWordEntry)) {
                                GLog.w(TAG, "filterSearchResult, generateResult, keywordEntry isNotSameTo guideClickedKeyWordEntry, name:"
                                        + name + ", type:" + type + ", id:" + id + ", albumType:" + albumType);
                                return true;
                            }
                        }
                    }
                }
                guideItemEntry.setKeywordEntry(keywordEntry);
            }
            return false;
        }

        /**
         * 通过 cursor extras 中的 result list 获取另一部分的搜索结果
         */
        private void getResultEntriesFromCursorExtras(Cursor cursor, SearchResultGuideItemEntry guideItemEntry,
                                                      SearchResultRecommendItemEntry recommendItemEntry) {
            int hashCode = recommendItemEntry.hashCode();
            String nameListId = SearchSuggestionProviderUtil.SEARCH_RESULT_LIST + hashCode;
            ArrayList<SearchResultEntry> resultEntries = cursor.getExtras().getParcelableArrayList(nameListId);
            GLog.d(TAG, "[getResultEntriesFromCursorExtras] resultEntries.size = " + ((resultEntries != null) ? resultEntries.size() : 0));
            if ((resultEntries != null) && (!resultEntries.isEmpty())) {
                guideItemEntry.setResultEntries(resultEntries);
                recommendItemEntry.setResultEntries(resultEntries);
                GLog.d(TAG, "[getResultEntriesFromCursorExtras] resultEntries = " + resultEntries);
            }
        }

        @NotNull
        private String[] getInputWords(boolean isMultiLabel) {
            String recentlyAdd = requireContext().getResources().getString(R.string.search_recommend_recently_added);
            String[] inputWords = null;
            if (recentlyAdd.contains(mQuery) || (!isMultiLabel)) {
                inputWords = new String[]{mQuery};
            } else {
                inputWords = mQuery.split(" ");
            }
            return inputWords;
        }

        /**
         * 构造推荐结果
         * 当前只有重排序逻辑，item name和搜索词相同的放前面，不同的放后面，内部通过 sortType 排序
         */
        private void generateSearchRecommendResult(Map<Integer, List<SearchResultRecommendItemEntry>> recommendItemMap,
                                                   List<SearchResultRecommendGroupEntry> resultRecommendGroupEntries) {
            // 所有搜索结果中（一个结果对应一个group），若group中所有item的名字都不等于搜索关键词，则添加到 nameNotEqualGroupEntries 中
            List<SearchResultRecommendGroupEntry> nameNotEqualGroupEntries = new ArrayList<>();
            for (Map.Entry<Integer, List<SearchResultRecommendItemEntry>> mapEntry : recommendItemMap.entrySet()) {
                if ((mapEntry.getKey() == SearchType.TYPE_CHILD_LABEL)) {
                    GLog.d(TAG, "generateSearchRecommendResult, type:" + mapEntry.getKey());
                    continue;
                }
                SearchResultRecommendGroupEntry groupEntry = new SearchResultRecommendGroupEntry(mapEntry.getKey(),
                        mappingGroupName(mapEntry.getKey()),
                        false, mapEntry.getValue(), mappingSortType(mapEntry.getKey()));

                boolean isRecommendNameEqualQuery = false;
                // 把当前group中所有item名字等于搜索关键词的item添加到nameEqualItemEntryList中
                List<SearchResultRecommendItemEntry> nameEqualItemEntryList = new ArrayList<>();
                Iterator<SearchResultRecommendItemEntry> iterator = groupEntry.getItemList().iterator();
                while (iterator.hasNext()) {
                    SearchResultRecommendItemEntry recommendItemEntry = iterator.next();
                    /*
                    若存在搜索结果名称与搜索关键词完全一致的情况，将搜索结果从groupEntry.getItemList()中删除
                    并添加到nameEqualItemEntryList中
                    */
                    if (!TextUtils.isEmpty(recommendItemEntry.getName()) && recommendItemEntry.getName().equalsIgnoreCase(mCurrentQuery)) {
                        isRecommendNameEqualQuery = true;
                        iterator.remove();
                        nameEqualItemEntryList.add(recommendItemEntry);
                    }
                }
                /*
                若isRecommendNameEqualQuery为true，说明存在搜索结果名称与搜索关键词完全一致的情况
                将nameEqualItemEntryList中的数据添加到groupEntry.getItemList()的最前面
                */
                if (isRecommendNameEqualQuery) {
                    groupEntry.getItemList().addAll(0, nameEqualItemEntryList);
                    resultRecommendGroupEntries.add(groupEntry);
                } else {
                    nameNotEqualGroupEntries.add(groupEntry);
                }
            }
            Collections.sort(resultRecommendGroupEntries, new RecommendSortComparator());
            Collections.sort(nameNotEqualGroupEntries, new RecommendSortComparator());
            resultRecommendGroupEntries.addAll(nameNotEqualGroupEntries);
        }

        private String mappingType(int type) {
            switch (type) {
                case TYPE_PERSON:
                    return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_PERSON;
                case TYPE_OCR:
                    return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_OCR;
                case TYPE_DATETIME:
                    return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_TIME;
                case TYPE_ALBUM:
                    return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_ALBUM;
                case TYPE_MEMORIES_ALBUM:
                    return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_MEMORIES;
                case TYPE_GUIDE_LABEL:
                    return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_GUIDE_LABEL;
                case TYPE_LABEL:
                    return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_LABEL;
                case TYPE_FILE_NAME:
                    if (isAndesSearchEnable()) {
                        return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_FILENAME;
                    } else {
                        return SearchSuggestionProviderUtil.QUERY_ALL_DIMENSIONS;
                    }
                case TYPE_LOCATION:
                    if (isAndesSearchEnable()) {
                        return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_LOCATION;
                    } else {
                        /*为避免查找地点的结果被滤去，地点类型进行了特殊处理，进行所有维度查询*/
                        return SearchSuggestionProviderUtil.QUERY_ALL_DIMENSIONS;
                    }
                case TYPE_RECENT_TIME:
                    if (isAndesSearchEnable()) {
                        return SearchSuggestionProviderUtil.QUERY_RECOMMEND_RECENTLY_ADDED;
                    } else {
                        /*为避免查找地点的结果被滤去，地点类型进行了特殊处理，进行所有维度查询*/
                        return SearchSuggestionProviderUtil.QUERY_ALL_DIMENSIONS;
                    }
                case TYPE_MULTI_LABEL:
                    if (isAndesSearchEnable()) {
                        return SearchSuggestionProviderUtil.QUERY_RECOMMEND_FILTER_MULTI_LABEL;
                    } else {
                        /*为避免查找地点的结果被滤去，地点类型进行了特殊处理，进行所有维度查询*/
                        return SearchSuggestionProviderUtil.QUERY_ALL_DIMENSIONS;
                    }
                default:
                    return SearchSuggestionProviderUtil.QUERY_ALL_DIMENSIONS;
            }
        }

        private String mappingGroupName(int groupType) {
            Context context = getContext();
            if (context == null) {
                GLog.d(TAG, "mappingGroupName, context is null");
                return TextUtil.EMPTY_STRING;
            }
            Resources resources = context.getResources();
            switch (groupType) {
                case SearchType.TYPE_DATETIME:
                    return resources.getString(R.string.search_result_type_time);
                case SearchType.TYPE_LOCATION:
                    return resources.getString(R.string.search_result_type_location);
                case SearchType.TYPE_PERSON:
                    return resources.getString(R.string.search_result_type_person);
                case SearchType.TYPE_LABEL:
                case SearchType.TYPE_CHILD_LABEL:
                    return resources.getString(R.string.search_result_type_label);
                case SearchType.TYPE_ALBUM:
                    return resources.getString(R.string.search_result_type_album);
                case SearchType.TYPE_MEMORIES_ALBUM:
                    return resources.getString(R.string.search_result_type_memories);
                case SearchType.TYPE_OCR:
                    return resources.getString(R.string.search_result_type_ocr);
                case SearchType.TYPE_SPECIAL_LABEL:
                    return resources.getString(R.string.search_special_keyword_gif);
                case SearchType.TYPE_MULTI_LABEL:
                    return resources.getString(R.string.search_multi_label);
                default:
                    return TextUtil.EMPTY_STRING;
            }
        }

        private int mappingSortType(int groupType) {
            switch (groupType) {
                case SearchType.TYPE_DATETIME:
                    return SORT_TYPE_DATE;
                case SearchType.TYPE_ALBUM:
                    return SORT_TYPE_ALBUM;
                case SearchType.TYPE_SPECIAL_LABEL:
                    return SORT_TYPE_SPECIAL_LABEL;
                case SearchType.TYPE_OCR:
                    return SORT_TYPE_OCR;
                case SearchType.TYPE_PERSON:
                    return SORT_TYPE_PERSON;
                case SearchType.TYPE_LOCATION:
                    return SORT_TYPE_LOCATION;
                case SearchType.TYPE_LABEL:
                    return SORT_TYPE_LABEL;
                case SearchType.TYPE_MEMORIES_ALBUM:
                    return SORT_TYPE_MEMORIES_ALBUM;
                default:
                    return SORT_TYPE_DEFAULT;
            }
        }

        /**
         * 构造中间的搜索结果
         */
        private SearchResultGridGroupEntry generateSearchGridResult(
                int albumType,
                int type,
                StringBuilder idStrBuilder,
                String[] inputWords,
                boolean isMultiModalResult
        ) {
            String idListStr = idStrBuilder.toString().trim();
            List<SearchResultGridItemEntry> resultGridItemEntries = new ArrayList<>();
            List<String> nameList = new ArrayList<>();

            if (idListStr.isEmpty()) {
                return new SearchResultGridGroupEntry(inputWords[0], albumType, type, idListStr, 0, 0,
                        resultGridItemEntries, nameList, isMultiModalResult);
            }

            // 过滤多个精确搜索之间重复的内容
            String[] idList = idListStr.split(SYMBOL_COMMA);
            Set<String> uniqueIdSet = new LinkedHashSet<>();
            Collections.addAll(uniqueIdSet, idList);
            String uniqueIdStr = String.join(SYMBOL_COMMA, uniqueIdSet);
            String[] uniqueIdList = uniqueIdSet.toArray(new String[0]);

            // 多模态搜索结果时，需要按照相关度排序，所以需要查询所有的数据，然后重新按照相关度排序
            int queryMaxCount = 0;
            if (isMultiModalResult) {
                queryMaxCount = uniqueIdList.length;
            } else {
                queryMaxCount = GRID_MAX_SIZE;
            }

            ArrayList<MediaItem> itemList = SearchDBHelper.getMediaItemListFromIdList(_ID, uniqueIdStr, 0, queryMaxCount);
            // 如果是多模态搜索结果，需要根据idList的顺序(即相关度)重新排序，而不按时间排序
            if (isMultiModalResult) {
                // 先将 itemList 转化为 Map<id, item>,方便后面排序取值，避免双层 for 嵌套。
                Map<Integer, MediaItem> idItemMap = itemList.stream()
                        .collect(Collectors.toMap(MediaItem::getId, item -> item));
                Arrays.stream(uniqueIdList)
                        .map(Integer::parseInt)
                        .forEach(id -> {
                            MediaItem mediaItem = idItemMap.get(id);
                            if (mediaItem != null) {
                                resultGridItemEntries.add(getSearchResultGridItemEntry(mediaItem));
                            }
                        });
            } else {
                for (MediaItem mediaItem : itemList) {
                    resultGridItemEntries.add(getSearchResultGridItemEntry(mediaItem));
                }
            }

            nameList.addAll(Arrays.asList(inputWords));
            Set<String> idSet = new HashSet<>(Arrays.asList(idList));
            int videoCount = SearchDBHelper.queryCountFromGalleryIdList(uniqueIdStr, MEDIA_TYPE_VIDEO);
            return new SearchResultGridGroupEntry(inputWords[0], albumType, type, uniqueIdStr, idSet.size(), videoCount, resultGridItemEntries,
                    nameList, isMultiModalResult);
        }

        private SearchResultGridItemEntry getSearchResultGridItemEntry(MediaItem mediaItem) {
            int mediaId = mediaItem.getMediaId();
            int id = mediaItem.getId();
            int idType = mediaItem.getMediaType();
            long dateTaken = mediaItem.getDateTakenInMs();
            String durationText = SlotOverlayHelper.INSTANCE.updateTimeText(mediaItem.getDurationInSec());
            int iconType = SlotOverlayHelper.INSTANCE.iconType(mediaItem);
            int rotation = MediaItemExtKt.getThumbnailRotation(mediaItem);
            boolean isFavorite = mediaItem.isFavorite();
            boolean isDRM = mediaItem.isDrm();
            int localStatus = mediaItem.getLocalFileStatus();
            long fileSize = mediaItem.getFileSize();
            return new SearchResultGridItemEntry(id, mediaId, idType, dateTaken, durationText, iconType, rotation,
                    isFavorite, isDRM, localStatus, fileSize);
        }

        private boolean needForceUpdate() {
            if (mNeedForceUpdate) {
                return true;
            }
            if (mCurrentQuery == null) {
                boolean lastQueryIsNull = (mLastQuery == null);
                if (!lastQueryIsNull) {
                    mLastQuery = null;
                }
                return !lastQueryIsNull;
            }
            if (mCurrentQuery.equals(mLastQuery)) {
                return false;
            }
            mLastQuery = mCurrentQuery;
            return true;
        }

        private int getRotationFromMediaId(Context context,
                                           int coverMemoriesId,
                                           int coverGalleryId, int mediaType, int type, int albumType) {
            if (type == TYPE_MEMORIES_ALBUM) {
                final Path memoriesPath = Local.PATH_ALBUM_MEMORIES_ANY.getChild(coverMemoriesId);
                List<MediaItem> coverItems = MemoriesHelper.getCoverItemsByMemoriesId(coverMemoriesId, memoriesPath);
                MediaItem mediaItem = ((coverItems != null) && !coverItems.isEmpty()) ? coverItems.get(0) : null;
                if (mediaItem != null) {
                    return mediaItem.getRotation();
                }
            } else {
                if (albumType == AlbumEntry.TYPE_RECYCLE_ALBUM) {
                    return 0;
                }
                if (mediaType == MEDIA_TYPE_IMAGE) {
                    final Path path = LocalImage.ITEM_PATH.getChild(coverGalleryId);
                    try {
                        LocalImage localImage = (LocalImage) DataManager.peekMediaObject(path);
                        if (localImage == null) {
                            localImage = new LocalImage(path, context, false);
                        }
                        return localImage.getRotation();
                    } catch (Exception e) {
                        GLog.w(TAG, "getRotationFromMediaId, Exception = " + e);
                    }
                } else if (mediaType == MEDIA_TYPE_VIDEO) {
                    return 0;
                }
            }
            return 0;
        }

        private boolean isSameResultList(List<SearchResult> resultList, SearchResultGridGroupEntry searchResultGridGroupEntry) {
            if ((mLastQueryResultList == null) || (mLastQueryResultList.size() != resultList.size())) {
                return false;
            }
            if ((mSearchResultEntry == null) || (mSearchResultEntry.getItemEntries().size()
                    != searchResultGridGroupEntry.getItemEntries().size())) {
                return false;
            }
            return resultList.containsAll(mLastQueryResultList) && searchResultGridGroupEntry.
                    getItemEntries().containsAll(mSearchResultEntry.getItemEntries());
        }

        private void sortQueryResultByCount(List<SearchResultGuideItemEntry> resultEntries) {
            if (resultEntries.isEmpty()) {
                return;
            }
            Collections.sort(resultEntries, new GuideComparator());
            String labelName = null;
            List<SearchResultGuideItemEntry> childLabelList = new ArrayList<>();
            List<SearchResultGuideItemEntry> nameEqualList = new ArrayList<>();
            Iterator<SearchResultGuideItemEntry> iterator = resultEntries.iterator();
            while (iterator.hasNext()) {
                SearchResultGuideItemEntry entry = iterator.next();
                int resultType = entry.getType();
                String resultName = entry.getName();
                if (!TextUtils.isEmpty(resultName) && resultName.equalsIgnoreCase(mCurrentQuery)) {
                    nameEqualList.add(entry);
                    iterator.remove();
                    if (resultType == TYPE_LABEL) {
                        labelName = resultName;
                    }
                }
                if (resultType == TYPE_CHILD_LABEL) {
                    childLabelList.add(entry);
                }
            }
            resultEntries.addAll(0, nameEqualList);
            if (TextUtils.isEmpty(labelName) || childLabelList.isEmpty()) {
                return;
            }
            resultEntries.removeAll(childLabelList);
            for (int i = 0, n = resultEntries.size(); i < n; i++) {
                SearchResultGuideItemEntry entry = resultEntries.get(i);
                String resultName = entry.getName();
                int resultType = entry.getType();
                if ((resultType == TYPE_LABEL) && labelName.equalsIgnoreCase(resultName)) {
                    resultEntries.addAll(i + 1, childLabelList);
                    break;
                }
            }
        }
    }

    private boolean isAscOrder() {
        return ApiDmManager.getSettingDM().isPositiveOrder();
    }

    /**
     * 设置系统栏是否一致保持可见。
     *
     * @param isKeepSystemBarVisible true 保持状态栏可见，会立即使系统栏可见，且会在 Fragment的onResume时再次确保状态栏可见。
     */
    protected void setKeepSystemBarVisible(Boolean isKeepSystemBarVisible) {
        // 去重
        if (mIsKeepSystemBarVisible == isKeepSystemBarVisible) {
            return;
        }
        mIsKeepSystemBarVisible = isKeepSystemBarVisible;
        setupSystemBarVisibility(mIsKeepSystemBarVisible);
    }

    /**
     * 设置系统栏可见性。
     *
     * @param isVisible 系统栏的可见性。
     */
    protected void setupSystemBarVisibility(Boolean isVisible) {
        if (isVisible) {
            showStatusBar();
            showNaviBar();
        } else {
            hideStatusBar();
            hideNaviBar();
        }
    }

    private static class GuideComparator implements Comparator<SearchResultGuideItemEntry> {
        @Override
        public int compare(SearchResultGuideItemEntry entry1, SearchResultGuideItemEntry entry2) {
            if ((entry1 == null) && (entry2 == null)) {
                return 0;
            }
            if (entry1 == null) {
                return 1;
            }
            if (entry2 == null) {
                return -1;
            }
            if (entry2.getCount() - entry1.getCount() > 0) {
                return 1;
            }
            if (entry1.getCount() - entry2.getCount() > 0) {
                return -1;
            }
            return 0;
        }
    }

    private static class RecommendSortComparator implements Comparator<SearchResultRecommendGroupEntry> {
        @Override
        public int compare(SearchResultRecommendGroupEntry entry1, SearchResultRecommendGroupEntry entry2) {
            if ((entry1 == null) && (entry2 == null)) {
                return 0;
            }
            if (entry1 == null) {
                return -1;
            }
            if (entry2 == null) {
                return 1;
            }
            if (entry2.getSortType() - entry1.getSortType() > 0) {
                return -1;
            }
            if (entry1.getSortType() - entry2.getSortType() > 0) {
                return 1;
            }
            return 0;
        }
    }

    private String mappingSearchResultType(int type) {
        switch (type) {
            case TYPE_DATETIME:
                return Value.SEARCH_DATE;
            case TYPE_LOCATION:
                return Value.SEARCH_LOCATION;
            case TYPE_PERSON:
                return Value.SEARCH_PERSON;
            case TYPE_OCR:
                return Value.SEARCH_OCR;
            case TYPE_MEMORIES_ALBUM:
                return Value.SEARCH_MEMORY;
            case TYPE_ALBUM:
                return Value.SEARCH_ALBUM;
            case TYPE_GUIDE_LABEL:
                return Value.SEARCH_GUIDE_LABEL;
            case TYPE_LABEL:
                return Value.SEARCH_LABEL;
            case TYPE_FILE_NAME:
                return Value.SEARCH_FILENAME;
            default:
                return Value.SEARCH_DEFAULT;
        }
    }

    private int mappingSearchResultWordType(String word, int type) {
        switch (type) {
            case TYPE_DATETIME:
                return Value.WORD_FROM_DATE;
            case TYPE_LOCATION:
                return Value.WORD_FROM_LOCATION;
            case TYPE_PERSON:
                return Value.WORD_FROM_PERSON;
            case TYPE_OCR:
                return Value.WORD_FROM_OCR;
            case TYPE_MEMORIES_ALBUM:
            case TYPE_ALBUM:
                return Value.WORD_FROM_ALBUM;
            case TYPE_GUIDE_LABEL:
            case TYPE_LABEL:
                return SearchTrackHelper.mappingSearchResultLabelType(word);
            case TYPE_FILE_NAME:
                return Value.WORD_FROM_FILE_NAME;
            default:
                return 0;
        }
    }
}
