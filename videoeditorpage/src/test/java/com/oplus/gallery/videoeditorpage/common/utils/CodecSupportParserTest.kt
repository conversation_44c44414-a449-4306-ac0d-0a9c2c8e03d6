/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CodecSupportParserTest.kt
 * Description:
 * Version:
 * Date: 2022/5/20
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/20     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.videoeditorpage.common.utils

import android.media.MediaCodecInfo
import android.util.Range
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditor.utils.CodecSupportParser
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.mockkClass
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CodecSupportParserTest {

    private lateinit var codecSupportParser: CodecSupportParser

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        codecSupportParser = spyk(recordPrivateCalls = true)
        mockkStatic(GLog::class)
    }

    @Test
    fun should_return_null_when_isSupportSize_with_null_videoCapabilities() {
        // 硬件解码器不支持此option配置的格式规格，返回null

        // given
        val option = CodecSupportParser.Options("test")
        val specification = VideoSpec(1920, 1080, 30f)
        every { codecSupportParser invoke "getVideoCapabilities" withArguments listOf(option) } answers { null }

        // when
        val ans = codecSupportParser.isSupportSize(specification, option)

        // then
        Assert.assertEquals(ans, null)
    }

    @Test
    fun should_return_supported_when_isSupportSize_with_normal_videoCapabilities() {
        // 获取此options规格下的视频编解码支持的分辨率

        // given
        val option = CodecSupportParser.Options("test")
        val specification1 = VideoSpec(1920, 1080, 30f)
        val specification2 = VideoSpec(1080, 1920, 30f)
        val capabilities = mockkClass(MediaCodecInfo.VideoCapabilities::class)
        every { codecSupportParser invoke "getVideoCapabilities" withArguments listOf(option) } answers { capabilities }
        every { capabilities.isSizeSupported(any(), any()) } answers { true }

        // when
        val ans1 = codecSupportParser.isSupportSize(specification1, option)
        val ans2 = codecSupportParser.isSupportSize(specification2, option)

        // then
        Assert.assertEquals(ans1, true)
        Assert.assertEquals(ans2, true)
    }

    @Test
    fun should_return_unsupported_when_isSupportSize_with_normal_videoCapabilities() {
        // 获取此options规格下的视频编解码不支持的分辨率

        // given
        val option = CodecSupportParser.Options("test")
        val specification1 = VideoSpec(1920, 1080, 30f)
        val specification2 = VideoSpec(1080, 1920, 30f)
        val capabilities = mockkClass(MediaCodecInfo.VideoCapabilities::class)
        every { codecSupportParser invoke "getVideoCapabilities" withArguments listOf(option) } answers { capabilities }
        every { capabilities.isSizeSupported(any(), any()) } answers { false }

        // when
        val ans1 = codecSupportParser.isSupportSize(specification1, option)
        val ans2 = codecSupportParser.isSupportSize(specification2, option)

        // then
        Assert.assertEquals(ans1, false)
        Assert.assertEquals(ans2, false)
    }

    @Test
    fun should_return_0fps_when_updateSpecificationSupportedFps_with_null_videoCapabilities() {
        // 获取option配置下specification宽高的视频所能支持到的最大fps,硬件解码器不支持此option配置的格式规格，返回0f

        // given
        val option = CodecSupportParser.Options("test")
        val specification = VideoSpec(1920, 1080, 30f)
        every { codecSupportParser invoke "getVideoCapabilities" withArguments listOf(option) } answers { null }

        // when
        val ans = codecSupportParser.getSpecificationMaxFps(specification, option)

        // then
        Assert.assertEquals(ans, 0f)
    }

    @Test
    fun should_return_limit_fps_when_updateSpecificationSupportedFps_with_normal_videoCapabilities() {
        // 获取option配置下specification宽高的视频所能支持到的最大fps

        // given
        val option = CodecSupportParser.Options("test")
        val specification = VideoSpec(1920, 1080, 60f)
        val capabilities = mockkClass(MediaCodecInfo.VideoCapabilities::class)
        every { codecSupportParser invoke "getVideoCapabilities" withArguments listOf(option) } answers { capabilities }
        every { capabilities.getSupportedFrameRatesFor(any(), any()).upper } answers { 30.0 }

        // when
        val ans = codecSupportParser.getSpecificationMaxFps(specification, option)

        // then
        Assert.assertEquals(ans, 30f)
    }

    @Test
    fun should_return_limit_fps_when_updateSpecificationSupportedFps_with_reverse_size() {
        // 获取option配置下specification宽高的视频所能支持到的最大fps, 要求getSupportedFrameRatesFor传参必须较大值在前

        // given
        val option = CodecSupportParser.Options("test")
        val specification = VideoSpec(1080, 1920, 60f)
        val capabilities = mockkClass(MediaCodecInfo.VideoCapabilities::class)
        val range = mockkClass(Range::class)
        every { codecSupportParser invoke "getVideoCapabilities" withArguments listOf(option) } answers { capabilities }
        every { capabilities.getSupportedFrameRatesFor(any(), any()) } answers {
            if (this.args[0] as Int > this.args[1] as Int) {
                range as Range<Double>
            } else {
                null
            }
        }
        every { range.upper } answers { 30.0 }

        // when
        val ans = codecSupportParser.getSpecificationMaxFps(specification, option)

        // then
        Assert.assertEquals(ans, 30f)
    }
}