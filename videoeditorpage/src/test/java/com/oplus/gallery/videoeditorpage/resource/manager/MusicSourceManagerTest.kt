/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MusicSourceManagerTest
 ** Description: 音乐资源管理测试类
 **
 ** Version: 1.0
 ** Date: 2022/03/17
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  <PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D  2022/03/17 1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.resource.manager

import android.app.Application
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem
import com.oplus.gallery.videoeditorpage.resource.room.dao.MusicDao
import com.oplus.gallery.videoeditorpage.resource.room.helper.ResourceDatabaseHelper
import com.oplus.gallery.videoeditorpage.resource.room.helper.ThemeTableHelper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.spyk
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class MusicSourceManagerTest {
    private var musicDao = TestMusicDao()

    private val items = mutableListOf<MusicItem>()

    @MockK
    private lateinit var mockApp: Application

    @MockK
    private lateinit var localBroadcastManager: LocalBroadcastManager

    private lateinit var musicSourceManager: MusicSourceManager

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(GLog::class)
        mockkStatic(ResourceDatabaseHelper::class)
        mockkStatic(LocalBroadcastManager::class)
        mockkStatic(LocalSourceManager::class)
        mockkStatic(VideoEditorHelper::class)
        mockkStatic(ThemeTableHelper::class)
        every { ResourceDatabaseHelper.getInstance().db.musicDao } returns musicDao
        every { LocalBroadcastManager.getInstance(any()) } returns localBroadcastManager
        every { VideoEditorHelper.setLongPref(any(), any(), any()) } returns Unit
        items.add(MusicItem())
        items.add(MusicItem())
        musicSourceManager = spyk(MusicSourceManager.getInstance())
    }

    @Test
    fun `should return true when checkBuiltinItem with parse success`() {
        ContextGetter.context = mockApp
        every { LocalSourceManager.parseConfig(any(), any(), any()) } returns items
        Assert.assertTrue(musicSourceManager.checkBuiltinItem(true))
    }

    @Test
    fun `should return false when checkBuiltinItem with parse failed`() {
        ContextGetter.context = mockApp
        every { LocalSourceManager.parseConfig(any(), any(), any()) } returns null
        Assert.assertFalse(musicSourceManager.checkBuiltinItem(true))
    }

    @Test
    fun `should return false when checkBuiltinItem with context null`() {
        Assert.assertFalse(musicSourceManager.checkBuiltinItem(true))
    }

    @Test
    fun `should return True when checkBuiltinItem with has builtin`() {
        ContextGetter.context = mockApp
        Assert.assertTrue(musicSourceManager.checkBuiltinItem())
    }

    class TestMusicDao : MusicDao {
        override fun insert(item: MusicItem?): Long {
            return -1
        }

        override fun insert(items: MutableList<MusicItem>?): MutableList<Long> {
            return mutableListOf()
        }

        override fun delete(item: MusicItem?) {
            //do nothing
        }

        override fun delete(items: MutableList<MusicItem>?) {
            //do nothing
        }

        override fun update(item: MusicItem?): Int {
            return -1
        }

        override fun updateAll(items: MutableList<MusicItem>?): Int {
            return -1
        }

        override fun clearBuiltin() {
            //do nothing
        }

        override fun getAllBuiltin(): MutableList<MusicItem> {
            val items = mutableListOf<MusicItem>()
            items.add(MusicItem())
            return items
        }

        override fun queryAll(): MutableList<MusicItem> {
            return mutableListOf()
        }

        override fun queryEnableMusic(): MutableList<MusicItem> {
            return mutableListOf()
        }

        override fun queryNotDownloadedItem(): MutableList<MusicItem> {
            return mutableListOf()
        }

        override fun queryIconExistedMusic(): MutableList<MusicItem> {
            return mutableListOf()
        }

        override fun getEntityByPosition(position: Int): MusicItem {
            return MusicItem()
        }

        override fun getEntityByMusicId(songId: Int): MusicItem {
            return MusicItem()
        }

        override fun getNoIconEntityList(): MutableList<MusicItem> {
            return mutableListOf()
        }

        override fun getInvalidEntityList(maxPosition: Int): MutableList<MusicItem> {
            return mutableListOf()
        }

        override fun deleteInvalidEntity(maxPosition: Int): Int {
            return -1
        }

        override fun getMusicBySourcePath(musicPath: String?): MusicItem {
            return MusicItem()
        }
    }
}