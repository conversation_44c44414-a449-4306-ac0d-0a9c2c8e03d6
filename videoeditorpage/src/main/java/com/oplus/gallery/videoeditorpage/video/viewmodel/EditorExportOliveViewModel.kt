/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorExportOliveState.kt
 ** Description: 视频导出实况照片编辑页的ViewModel
 ** Version: 1.0
 ** Date: 2025/6/6
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/6        1.0        created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.viewmodel

import android.net.Uri
import android.os.Build
import android.view.SurfaceView
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.meicam.sdk.NvsTimeline
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_IDLE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.setOrPostValue
import com.oplus.gallery.foundation.util.lifecycle.SingleLiveEvent
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.base.util.VideoTrackHelper.trackVideoEditorExportOLiveOrPhoto
import com.oplus.gallery.videoeditorpage.engine.meicam.MeicamVideoEngine.Companion.MILLIS_TIME_BASE
import com.oplus.gallery.videoeditorpage.output.ResultCode
import com.oplus.gallery.videoeditorpage.output.task.ExportFileInfo
import com.oplus.gallery.videoeditorpage.output.task.ExportOliveTask
import com.oplus.gallery.videoeditorpage.output.task.OutputData
import com.oplus.gallery.videoeditorpage.output.task.SaveType
import com.oplus.gallery.videoeditorpage.uhdr.engine.VideoUhdrEngine
import kotlinx.coroutines.launch
import java.util.concurrent.Executors
import java.util.concurrent.Semaphore
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import kotlin.math.floor

/**
 * 视频导出实况照片编辑页的ViewModel
 */
class EditorExportOliveViewModel() : BaseEditorViewModel() {

    /**
     * 初始化引擎完成
     */
    val initEngineFinish: LiveData<Boolean> get() = _initEngineFinish
    private val _initEngineFinish = SingleLiveEvent<Boolean>()

    /**
     * 显示导出进度弹窗
     */
    val isShowCompileDialog: LiveData<Boolean> get() = _isShowCompileDialog
    private val _isShowCompileDialog = SingleLiveEvent<Boolean>()

    /**
     * 导出进度
     */
    val exportProgress: LiveData<Int> get() = _exportProgress
    private val _exportProgress = SingleLiveEvent<Int>()

    /**
     * 导出成功
     */
    val exportSuccess: LiveData<ExportFileInfo> get() = _exportSuccess
    private val _exportSuccess = SingleLiveEvent<ExportFileInfo>()

    /**
     * 通知缩图轴滚动到指定时间
     */
    val scrollThumbToTime: LiveData<Long> get() = _scrollThumbToTime
    private val _scrollThumbToTime = SingleLiveEvent<Long>()

    /**
     * 通知缩图轴停止滚动
     */
    val thumbStopScroll: LiveData<Boolean> get() = _thumbStopScroll
    private val _thumbStopScroll = SingleLiveEvent<Boolean>()

    /**
     * 通知视图提亮
     */
    val editorVideoBright: LiveData<SurfaceView> get() = _editorVideoBright
    private val _editorVideoBright = SingleLiveEvent<SurfaceView>()

    /**
     * 原视频总长度
     */
    private var videoDuration: Long = DEFAULT_VALUE

    /**
     * 播放起始时间
     */
    private var startTime: Long = DEFAULT_VALUE

    /**
     * 可播放视频时长
     */
    private var playDuration: Long = DEFAULT_VALUE

    /**
     * 封面帧时间
     */
    private var coverTime: Long = DEFAULT_VALUE

    /**
     * video导出uhdr的engine
     */
    private var videoUhdrEngine: VideoUhdrEngine? = null

    /**
     * 是否需要执行自动播放
     */
    var shouldAutoPlayWhenScrollIdle: Boolean = false

    /**
     * 最后滚动到的位置（可能会超出视频长度或小于 0）
     * 用于判断目前滚动位置是否位于 Olive 导出开头时间点。
     */
    private var lastScrollTime: Long = 0L

    /**
     * 缩图轴当前的滚动状态
     */
    private var thumbScrollState = SCROLL_STATE_IDLE

    /**
     * 导出实况任务
     */
    private var exportOliveTask: ExportOliveTask? = null

    /**
     * 导出实况任务触发的时间
     */
    private var exportOliveStartTime: Long = 0

    /**
     * 导出任务的单线程执行器，
     * 导出的任务会放在此执行器中执行，如果先前任务未完成会进行排队 (使用 [countDownLatch] 阻塞)。
     * 用户取消导出时会清空排队队列。
     */
    private val executor by lazy {
        Executors.newFixedThreadPool(
            1, ThreadFactoryBuilder().setNameFormat("export_olive-%d").build()
        )
    }

    /**
     * 用于阻塞导出任务 runnable，
     * 初始没有 permit，需待导出回调时 +1。
     * “导出回调的+1” 需与 “runnable的-1” 相对应（两者的执行时机无关）
     */
    private var semaphore: Semaphore = Semaphore(0)

    /**
     * 导出实况任务监听器
     */
    private val exportOliveListener = object : ExportOliveTask.ExportOliveListener {
        override fun onProcess(process: Int) {
            _exportProgress.setOrPostValue(process)
        }

        override fun onExportComplete(outputData: OutputData<ExportFileInfo>) {
            if (outputData.resultCode == ResultCode.SAVE_SUCCESS) {
                GLog.d(TAG, LogFlag.DL, "done success, ExportOliveTask.run cost time: ${System.currentTimeMillis() - exportOliveStartTime}")
            } else if (isForceStopSave(outputData.resultCode)) {
                GLog.d(TAG, LogFlag.DL, "done result code: ${outputData.resultCode}, reason: ${outputData.resultMsg}")
                viewModelScope.launch {
                    ToastUtil.showShortToast(R.string.videoeditor_editor_export_fail)
                }
            }

            if (isForceStopSave(outputData.resultCode)) {
                // 非点击弹窗取消按键时才 dismiss；AppCompatDialog 在点击按钮后会自行 dismiss，此处再dismiss会导致：取消后再点导出时弹窗出现后马上消失的问题。
                _isShowCompileDialog.setOrPostValue(false)
            }

            // 埋点
            trackExportOLive(outputData)

            if (outputData.resultCode == ResultCode.SAVE_SUCCESS) {
                // 导出成功发送通知跳转大图
                outputData.resultData?.let {
                    _exportSuccess.setOrPostValue(it)
                } ?: GLog.e(TAG, LogFlag.DL) { "resultData is null" }
            } else {
                // 导出失败重置封面帧
                viewModelScope.launch {
                    seekTo(coverTime)
                }
            }

            videoEngineManager?.updateCustomCompileTaskStatus(false)
            videoEngineManager?.setCustomCompileTaskListener(null)

            if (semaphore.availablePermits() == 0) {
                // 提供一个 permit
                semaphore.release()
            }
        }
    }

    /**
     * 是否为强制保存
     * @param resultCode 结果码
     * @return true:强制保存 false:非强制保存
     */
    private fun isForceStopSave(resultCode: Int): Boolean {
        return (resultCode != ResultCode.MEICAM_FORCE_STOP_SAVE) && (resultCode != ResultCode.TASK_STOP_FORCE_STOP_SAVE)
    }

    /**
     * 通知初始化引擎完成
     * @param isFinished
     */
    fun notifyInitEngineFinish(isFinished: Boolean) {
        _initEngineFinish.setOrPostValue(isFinished)
        initVideoInfo()
    }

    /**
     * 通知编辑视频提亮
     */
    fun notifyEditorVideoBrighten(surfaceView: SurfaceView) {
        _editorVideoBright.setOrPostValue(surfaceView)
    }

    /**
     * 初始化视频信息
     */
    private fun initVideoInfo() {
        seekTo(0)
        videoDuration = getTotalTime()
        createUhdrEngine()
        updateTimeAndEngine(0)
    }

    /**
     * 获取可播放的总时长
     * 选帧框起始位置时间 + 3s 时长
     * 超出总时长则最大使用视频总时长
     * @return 播放时长
     */
    private fun getPlayDurationTime(): Long {
        val playTime = startTime.plus(MAX_EXPORT_TIME)
        return minOf(playTime, videoDuration)
    }

    /**
     * 获取当前播放时间
     * @return
     */
    fun getCurrentTime(): Long {
        return videoEngineManager?.getCurrentTime() ?: 0
    }

    /**
     * 获取封面帧时间
     * @return
     */
    fun getCoverTime(): Long {
        return coverTime
    }

    /**
     * 获取视频总时长
     * @return
     */
    fun getTotalTime(): Long {
        return videoEngineManager?.getTotalTime() ?: 0
    }

    /**
     * 设置进度
     * @param time 要设置的时间点，单位：ms
     */
    fun seekTo(time: Long) {
        videoEngineManager?.seekTo(time)
    }

    /**
     * 执行导出实况任务
     */
    fun exportOlive() {
        // 如果点导出时，选帧框不在正确位置则先滚动到正确的导出起始位置
        if (isScrollTimeEqualCoverTime().not()) {
           _scrollThumbToTime.setOrPostValue(coverTime)
        }
        // 导出实况时停止缩图轴滑动
        _thumbStopScroll.setOrPostValue(true)
        // 拉起实况导出进度对话框
        _isShowCompileDialog.setOrPostValue(true)

        videoEngineManager?.let {
            val newTask = ExportOliveTask(startTime, playDuration, coverTime, it, exportOliveListener, videoUhdrEngine)
            // 导出任务
            executor.submit {
                exportOliveTask = newTask
                exportOliveStartTime = System.currentTimeMillis()
                it.updateCustomCompileTaskStatus(true)
                exportOliveTask?.run()

                // 阻塞直到完成导出，或超时
                try {
                    // 等待一个 permit
                    val successRelease = semaphore.tryAcquire(TASK_MAX_WAITING_TIME_SEC, TimeUnit.SECONDS)
                    if (!successRelease) {
                        GLog.e(TAG, LogFlag.DL) { "[done] acquire timeout!" }
                    }
                } catch (e: InterruptedException) {
                    GLog.e(TAG, LogFlag.DL, e) { "[done] failed to acquire" }
                }
            }
        }
    }

    /**
     * 结束实况导出任务
     */
    fun stopExportOliveTask() {
        exportOliveTask?.stop()
        // 取消当前时，同时取消所有正在排队的任务。
        (executor as? ThreadPoolExecutor)?.queue?.clear()
    }

    /**
     * 设置缩图轴当前的滚动状态
     * @param state
     */
    fun setThumbScrollState(state: Int) {
        thumbScrollState = state
    }

    /**
     * 选帧框是否在正确位置
     * @return
     */
    fun isScrollTimeEqualCoverTime(): Boolean {
        return lastScrollTime == coverTime
    }

    /**
     * 通过传入时间获取对应的缩图轴位置
     * @param time 当前时间
     * @param pixelPerMicrosecond 每微妙所占用的像素数
     */
    fun getTimeConvertPosition(time: Long, pixelPerMicrosecond: Double): Int {
        var postPercent = 0f
        val totalTime = getTotalTime()
        if (totalTime > 0) {
            postPercent = time.coerceIn(0, totalTime) / totalTime.toFloat()
        }
        val x = Math.round(postPercent * getSequenceWidth(pixelPerMicrosecond))
        return x
    }

    /**
     * 获取缩图轴序列宽度
     * @param pixelPerMicrosecond 每微妙所占用的像素数
     * @return 缩图轴宽度
     */
    private fun getSequenceWidth(pixelPerMicrosecond: Double): Int {
        var sequenceWidth = 0
        videoEngineManager?.apply {
            sequenceWidth = floor(
                (getTotalTime() * getTimeBase() * pixelPerMicrosecond) + PIXEL_OFFSET
            ).toInt()
        }
        return sequenceWidth
    }

    /**
     * 通过传入位置获取对应时间
     */
    fun getPositionConvertTime(position: Int, pixelPerMicrosecond: Double): Long {
        var tmpTimeStamp = 0L
        // 通过当前坐标获取对应的时间线
        videoEngineManager?.let {
            tmpTimeStamp = (floor(position / pixelPerMicrosecond + PIXEL_OFFSET)).toLong() / it.getTimeBase()
            lastScrollTime = tmpTimeStamp
        }
        return tmpTimeStamp
    }

    /**
     * 更新时间和设置引擎封面帧位置
     * 导出实况的封面取选帧框开始时间 + [COVER_OFFSET_TIME]偏移时间的视频帧图片，
     * @param currentTime 当前时间
     */
     fun updateTimeAndEngine(currentTime: Long) {
        val maxStartTime = (videoDuration - MAX_EXPORT_TIME).coerceAtLeast(0)
        val totalTime = getTotalTime()
        coverTime = currentTime
        startTime = when {
            // 小于等于3秒的视频
            totalTime <= MAX_EXPORT_TIME -> 0
            // 封面帧时间小于等于偏移时间，则开始时间为0
            coverTime <= COVER_OFFSET_TIME -> 0
            // 封面帧时间大于等于总时长-偏移时间，则开始时间为maxStartTime
            coverTime >= (totalTime - COVER_OFFSET_TIME) -> maxStartTime
            else -> coverTime - COVER_OFFSET_TIME
        }
        playDuration = getPlayDurationTime()
        // 设置引擎到当前封面帧位置
        videoEngineManager?.seekTo(coverTime)
    }

    /**
     * 创建VideoUhdrEngine
     */
    private fun createUhdrEngine() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            videoEngineManager?.let {
                val videoUri = it.getVideoUri()
                val timeline = it.getMeicamVideoTimeline() as? NvsTimeline

                if (videoUri != null && timeline != null) {
                    val path = MediaStoreUriHelper.getFilePathByUri(Uri.parse(videoUri))
                    if (path != null) {
                        videoUhdrEngine =  VideoUhdrEngine(ContextGetter.context, it)
                    }
                }
            }
        }
    }

    /**
     * 执行点击预览按钮操作
     */
    fun clickPreviewBtn() {
        videoEngineManager?.let {
            if (allowPlayBack()) {
                // 如果在播放过程中，则不可在重置封面，这里主要解决极限操作情况下回调延迟得问题
                it.play(startTime, playDuration)
            } else {
                // 如果是在缩图轴滚动过程中点击播放，则需要记录点击播放行为，以便停止滚动后执行自动播放功能
                shouldAutoPlayWhenScrollIdle = true
            }
        }
    }

    /**
     * 是否允许播放
     * 当缩图轴当前未滚动时可播放
     * @return
     */
    private fun allowPlayBack(): Boolean {
        return thumbScrollState == SCROLL_STATE_IDLE
    }

    /**
     * 播放完成后判断播放时长在可播放总时长的差值范围内，则更新时长显示为可播放总时长
     */
    fun onPlayFinish() {
        if (getCurrentTime() >= (playDuration - VIDEO_PLAY_END_CHECK_GAP)) {
            seekTo(coverTime)
        }
    }

    fun onDestroy() {
        videoEngineManager?.setCustomCompileTaskListener(null)
        executor.shutdownNow()
    }

    /**
     * 导出实况埋点
     */
    fun trackExportOLive(outputData: OutputData<ExportFileInfo>) {
        val costTime = System.currentTimeMillis() - exportOliveStartTime
        val uri = if (outputData.resultCode == ResultCode.SAVE_SUCCESS) outputData.resultData?.resultUri.toString() else null
        trackVideoEditorExportOLiveOrPhoto(ContextGetter.context, uri, costTime, SaveType.Olive, outputData.resultCode)
    }

    companion object {
        private const val TAG = "EditorExportOliveViewModel"
        private const val DEFAULT_VALUE = 0L
        const val MAX_EXPORT_TIME = 3 * MILLIS_TIME_BASE
        private const val VIDEO_PLAY_END_CHECK_GAP = 100
        // 像素偏移，用于参与通过坐标得到映射的时间线
        private const val PIXEL_OFFSET = 0.5

        /**
         * 导出任务阻塞的最大等待时间，秒
         */
        private const val TASK_MAX_WAITING_TIME_SEC = 30L

        /**
         * 导出实况封面帧距离导出开始时刻的偏移时间
         */
        const val COVER_OFFSET_TIME = (0.5 * MAX_EXPORT_TIME).toLong()
    }
}