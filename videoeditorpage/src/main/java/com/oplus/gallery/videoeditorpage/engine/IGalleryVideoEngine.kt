/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - IGalleryVideoEngine.kt
 * * Description: GalleryVideoEngine interface.
 * * Version: 1.0
 * * Date : 2017/12/23
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version>	   <desc>
 * *  <EMAIL>       2017/12/23    1.0     build this module
</desc></version></data></author> */
package com.oplus.gallery.videoeditorpage.engine

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.util.Rational
import android.view.View
import android.view.ViewGroup
import com.meicam.sdk.NvsTimeline
import com.oplus.gallery.framework.abilities.videoedit.OnCustomCompileTaskStatusListener
import com.oplus.gallery.framework.abilities.videoedit.data.SaveVideoInfo
import java.io.PrintWriter

interface IGalleryVideoEngine {
    /**---------------- engine base start ----------------*/
    /**
     * 初始化engine预览时和保存时的帧率宽高，预览时还会受[IGalleryVideoEngine.setPreviewMaxHeight]影响
     * @param videoWidth 视频的宽度
     * @param videoHeight 视频的高度
     * @param fps 保存和预览的帧率
     * @return
     */
    fun initEngine(
        videoWidth: Int,
        videoHeight: Int,
        fps: Rational
    ): Boolean

    /**
     * 设置视频的编码类型
     * @param type 编码类型
     */
    fun setCodecType(type: Int)

    /**
     * 创建视频预览窗口
     * @param galleryEditorVideoView 视频窗口的载体view
     * @param shouldCrop 是否裁剪
     * @return 是否创建成功
     */
    fun createLiveWindow(galleryEditorVideoView: ViewGroup, shouldCrop: Boolean): Boolean

    /**
     * 限制预览的最大分辨率，若是视频的高度大于maxHeight，则等比缩小宽高
     * @param maxHeight 限制预览的最大高度
     */
    fun setPreviewMaxHeight(maxHeight: Int)

    fun getTimeBase(): Long

    fun getCurrentTime(): Long

    /**
     * 从美摄接口直接获取时间轴的原始时间位置（未经末尾偏移调整）
     * @return Long 微秒
     */
    fun getRealCurrentTime(): Long

    fun getTotalTime(): Long

    fun getCurrentFrame(): Bitmap?

    fun repaintFrame()

    fun isPlaying(): Boolean

    fun play()

    fun play(startTime: Long, endTime: Long)

    fun resume()

    fun pause()

    fun stop()

    fun stop(force: Boolean)

    fun seekTo(time: Long): Boolean

    fun reset()

    fun saveVideo(stringUri: String): Boolean

    fun saveVideo(stringUri: String, videoHeight: Int, videoDateTaken: Long): Boolean

    /**
     * 保存视频到指定路径，可指定起始时刻与帧率
     * @param filePath: 保存路径
     * @param startTime: 起始时刻
     * @param endTime: 结束时刻
     * @param fps: 帧率
     */
    fun saveVideo(filePath: String, startTime: Long, endTime: Long, fps: Int, videoUri: String?): Boolean

    /**
     * 获取导出的视频的 hdr 类型
     * @return type 参考 [VideoTypeParser.VideoType]
     */
    fun getExportVideoHdrType(): Int

    fun saveShareVideo(stringUri: String, start: Long, end: Long, videoHeight: Int): Boolean

    fun saveVideo(info: SaveVideoInfo): Boolean

    /**
     * 获取timeline指定帧时刻图片，并且管控是否继续播放
     * @param frameTime: 取图帧时刻
     * @param endTime: 播放停止时刻
     * @param isResumePlay: 播放暂停后是否继续播放
     */
    suspend fun grabImageFromTimelineAsync(
        frameTime: Long,
        endTime: Long,
        isResumePlay: Boolean
    ): Bitmap?

    /**
     * 基于GPU的方式从原视频中抽目标色域帧
     * 适用于视频抽帧
     * 由于美摄限制，需要在 MAIN 线程中调用（主线程 + 挂起函数）
     * @param timeUs 时间点，微妙
     * @param config 目标config
     * @param colorSpace 目标色域
     * @return 目标色域的 bitmap
     */
    suspend fun grabImageFromTimelineAsync(timeUs: Long, config: Bitmap.Config, colorSpace: ColorSpace): Bitmap?

    /**
     * 使用硬件解码器加载缩图轴
     * @param filePath String 资源文件
     * @param codecIconEnabled Boolean 是否开启硬件解码器加载缩图
     */
    fun setMediaCodecIconReaderEnabled(filePath: String, codecIconEnabled: Boolean)

    fun destroy(cleanAllContext: Boolean)

    fun hasVideoChanged(): Boolean

    fun createVideoView(parent: ViewGroup): View?

    fun getThumbnailView(parent: ViewGroup): View?

    fun dumpEngineInfo(writer: PrintWriter)

    fun getVideoHeight(): Int

    fun getVideoWidth(): Int

    /**
     * 获取单帧间隔
     *
     * @return
     */
    fun getFrameDuration(): Int

    fun getVideoScale(): Float

    /**
     * 注册监听自定义编译任务执行状态的回调接口
     */
    fun setCustomCompileTaskListener(videoListener: OnCustomCompileTaskStatusListener?)

    /**
     * 更新自定义编译任务执行状态
     * @param isRunning: 是否正在执行
     */
    fun updateCustomCompileTaskStatus(isRunning: Boolean)

    /**
     * 获取自定义编译任务执行状态
     */
    fun getCustomCompileTaskStatus(): Boolean

    /**
     * 获取美摄剪辑的时间线
     * @return NvsTimeline
     */
    fun getMeicamVideoTimeline(): NvsTimeline?

    /**---------------- engine base end ----------------*/

    /**---------------- sub engine interface ----------------*/
    fun getGalleryThemeHelper(): IGalleryThemeHelper

    fun getGalleryVideoClip(): IGalleryVideoClip

    fun getGalleryAudioClip(): IGalleryAudioClip

    fun getGalleryVideoFilter(): IGalleryVideoFilter

    fun getGalleryVideoSubTitle(): IGalleryVideoSubTitle

    fun getGalleryVideoThumbnail(): IGalleryVideoThumbnail

    fun getGalleryVideoFx(): IGalleryVideoFx

    fun getIGalleryVideoCutRotate(): IGalleryVideoCutRotate

    fun getGalleryVideoTemplate(): IGalleryVideoTemplate

    fun getGalleryVideoSticker(): IGalleryVideoSticker
}
