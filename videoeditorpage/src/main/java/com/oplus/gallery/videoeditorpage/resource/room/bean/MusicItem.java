/***********************************************************
 ** Copyright (C), 2008-2016, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: - MusicItem.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/7/17
 ** Author: liuyue
 **
 ** ---------------------Revision History: ---------------------
 **  <author>	 <data> 	  <version >	   <desc>
 **  liuyue      2019/7/17       1.0         build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.resource.room.bean;

import android.text.TextUtils;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.SerializedName;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;

@Entity(tableName = "source_music",
        indices = {@Index(value = "music_id", unique = true)})
public class MusicItem extends Item {
    @PrimaryKey
    @ColumnInfo(name = "position")
    private int mPosition;
    @ColumnInfo(name = "music_id")
    private int mMusicId;
    @ColumnInfo(name = "zh_name")
    private String mZhName;
    @ColumnInfo(name = "ch_name")
    private String mChName;
    @ColumnInfo(name = "en_name")
    private String mEnName;
    @ColumnInfo(name = "file_url")
    private String mFileUrl;
    @ColumnInfo(name = "source_path")
    private String mSourcePath;
    @ColumnInfo(name = "download_state")
    private int mDownloadState = 0;
    @ColumnInfo(name = "thumbnail_url")
    private String mThumbnailUrl;
    @ColumnInfo(name = "thumbnail_path")
    private String mThumbnailPath;
    @ColumnInfo(name = "version")
    private long mVersion;
    @SerializedName("isBuiltin")
    @ColumnInfo(name = "builtin")
    private boolean mIsBuiltin;
    @ColumnInfo(name = "update_time")
    private String mUpdateTime;

    public String getName() {
        if (ResourceUtils.isChineseSimp()) {
            if (TextUtils.isEmpty(getZhName())) {
                return getZhName();
            }
        } else if (ResourceUtils.isChineseTrad()) {
            if (TextUtils.isEmpty(getChName())) {
                return getChName();
            }
        } else {
            return getEnName();
        }
        return getEnName();
    }

    public String getSourcePath() {
        return mSourcePath;
    }

    public void setSourcePath(String sourcePath) {
        mSourcePath = sourcePath;
    }

    public String getThumbnailPath() {
        return mThumbnailPath;
    }

    public void setThumbnailPath(String thumbnailPath) {
        mThumbnailPath = thumbnailPath;
    }

    public int getMusicId() {
        return mMusicId;
    }

    public void setMusicId(int musicId) {
        mMusicId = musicId;
    }

    public String getZhName() {
        return mZhName;
    }

    public void setZhName(String zhName) {
        mZhName = zhName;
    }

    public String getChName() {
        return mChName;
    }

    public void setChName(String chName) {
        mChName = chName;
    }

    public String getEnName() {
        return mEnName;
    }

    public void setEnName(String enName) {
        mEnName = enName;
    }

    public String getFileUrl() {
        return mFileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.mFileUrl = fileUrl;
    }

    public String getThumbnailUrl() {
        return mThumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        mThumbnailUrl = thumbnailUrl;
    }

    public long getVersion() {
        return mVersion;
    }

    public void setVersion(long version) {
        mVersion = version;
    }

    public int getPosition() {
        return mPosition;
    }

    public void setPosition(int position) {
        mPosition = position;
    }

    public boolean isBuiltin() {
        return mIsBuiltin;
    }

    public void setIsBuiltin(boolean builtin) {
        mIsBuiltin = builtin;
    }

    public int getDownloadState() {
        return mDownloadState;
    }

    public void setDownloadState(int mDownloadState) {
        this.mDownloadState = mDownloadState;
    }

    public String getUpdateTime() {
        return mUpdateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.mUpdateTime = updateTime;
    }

    @Ignore
    public void updateDownloadState(int state) {
        this.mDownloadState = this.mDownloadState | state;
    }

    @Ignore
    @Override
    public boolean isDefaultIcon() {
        return (this.mDownloadState & (TYPE_DEFAULT_ICON | TYPE_DOWNLOAD_ICON)) == TYPE_DEFAULT_ICON;
    }

    @Ignore
    @Override
    public boolean isNeedDownloadIcon() {
        return (this.mDownloadState & TYPE_DOWNLOAD_ICON) != TYPE_DOWNLOAD_ICON;
    }

    @Ignore
    @Override
    public boolean isNeedDownloadFile() {
        return (this.mDownloadState & TYPE_DOWNLOAD_FILE) != TYPE_DOWNLOAD_FILE;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if ((o == null) || (getClass() != o.getClass())) {
            return false;
        }
        MusicItem item = (MusicItem) o;
        return (mPosition == item.mPosition) && (mMusicId == item.mMusicId);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public String toString() {
        return "MusicItem{"
                + "mPosition=" + mPosition
                + ", mMusicId=" + mMusicId
                + ", mZhName='" + mZhName + '\''
                + ", mChName='" + mChName + '\''
                + ", mEnName='" + mEnName + '\''
                + ", mDownloadState=" + mDownloadState
                + ", mVersion=" + mVersion
                + ", isBuiltin=" + mIsBuiltin
                + ", mUpdateTime='" + mUpdateTime + '\''
                + '}';
    }
}
