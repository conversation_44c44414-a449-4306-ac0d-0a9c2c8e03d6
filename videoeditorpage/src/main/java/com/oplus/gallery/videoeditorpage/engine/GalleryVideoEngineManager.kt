/***********************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - GalleryVideoEngineManager.java
 * Description: init GalleryVideoEngineManager
 * Version: 1.0
 * Date : 2017/11/10
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
 * <EMAIL>    2017/11/10    1.0    build this module
</desc></version></data></author> */
package com.oplus.gallery.videoeditorpage.engine

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.ColorSpace
import android.graphics.PointF
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.util.Rational
import android.view.ViewGroup
import com.meicam.sdk.NvsVideoStreamInfo
import com.oplus.gallery.business_lib.model.data.location.api.ConfigAddress
import com.oplus.gallery.business_lib.videoedit.VideoSpecStrategy
import com.oplus.gallery.foundation.util.debug.GLog.d
import com.oplus.gallery.foundation.util.debug.GLog.e
import com.oplus.gallery.framework.abilities.videoedit.ClipType
import com.oplus.gallery.framework.abilities.videoedit.IClip
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility
import com.oplus.gallery.framework.abilities.videoedit.OnCustomCompileTaskStatusListener
import com.oplus.gallery.framework.abilities.videoedit.OnVideoEditStateListener
import com.oplus.gallery.framework.abilities.videoedit.data.FilterInfo
import com.oplus.gallery.framework.abilities.videoedit.data.FxInfo
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo
import com.oplus.gallery.framework.abilities.videoedit.data.SaveVideoInfo
import com.oplus.gallery.framework.abilities.videoedit.data.SongInfo
import com.oplus.gallery.framework.abilities.videoedit.data.StickerInfo
import com.oplus.gallery.framework.abilities.videoedit.data.SubTitleInfo
import com.oplus.gallery.framework.abilities.videoedit.data.TemplateInfo
import com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio
import com.oplus.gallery.videoeditor.data.VideoSpec
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper
import com.oplus.gallery.videoeditorpage.engine.meicam.MeicamVideoEngine
import com.oplus.gallery.videoeditorpage.video.VideoEditorActivity
import java.io.PrintWriter
import java.util.function.Function

/**
 * 基于MeicamVideoEngine封装给外部视频编辑功能使用的封装类
 * @param context: 上下文
 * @param type: 使用的engine来源（当前默认美摄）
 * @param listener: 相册视频一级编辑页监听器
 */
class GalleryVideoEngineManager(
    context: Context,
    type: GalleryVideoEngineFactory.Engine?,
    listener: GalleryVideoEngineListener?
) : IVideoEditAbility {
    private val mGalleryVideoEngine: IGalleryVideoEngine =
        GalleryVideoEngineFactory().createVideoEngine(type, context, listener)
    private var mVideoUri: String? = null
    private var mVideoFilePath: String? = null

    private var mAudioManager: AudioManager? = null
    private val mAudioFocusRequest: AudioFocusRequest =
        AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
            .setAudioAttributes(
                AudioAttributes.Builder()
                    .setLegacyStreamType(AudioManager.STREAM_MUSIC)
                    .build()
            )
            .setOnAudioFocusChangeListener { focusChange: Int ->
                d(TAG, "onAudioFocusChange, focusChange= $focusChange")
                when (focusChange) {
                    AudioManager.AUDIOFOCUS_LOSS, AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> if (isPlaying()) pause()

                    else -> {}
                }
            }
            .build()

    constructor(context: Context, listener: GalleryVideoEngineListener?) : this(
        context,
        GalleryVideoEngineFactory.Engine.MEICAM,
        listener
    )

    init {
        mAudioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    override fun initVideoFileInfo(videoUri: String?, videoFilePath: String, isHfrSlowMotion: Boolean, videoType: Int): Boolean {
        mVideoUri = videoUri
        mVideoFilePath = videoFilePath
        mGalleryVideoEngine.setCodecType(videoType)
        val success: Boolean = mGalleryVideoEngine.getGalleryVideoClip().initVideoFileInfo(
            videoUri,
            isHfrSlowMotion,
            videoType
        )
        if (success && (mGalleryVideoEngine.getGalleryVideoClip().getVideoCodecType() === NvsVideoStreamInfo.VIDEO_CODEC_TYPE_H265)) {
            mGalleryVideoEngine.getGalleryVideoThumbnail().configOnlyDecodeKeyFrame(true)
        }
        return success
    }

    /**---------------- engine base start ----------------*/

    /**
     * 初始化engine
     *
     * @param videoSpec 视频规格尺寸
     * @return 是否初始化engine成功
     */
    override fun initEngine(mediaItemPath: String?, videoSpec: VideoSpec, checkVideoSpecification: Boolean): Boolean {
        var localSpec = videoSpec
        if (checkVideoSpecification) {
            val processSpec = VideoSpecStrategy.processSpec(videoMimeType, localSpec) ?: return false
            localSpec = processSpec
        }
        /***
         * 原始视频fps为30.000, specification.getFps()
         * 从sdk那边获取的NvsRational对像的num/den 值为float类型fps为29.99706
         * 强转会导致float类型转int精度丢失，和美摄sdk确认需要round处理
         */
        return mGalleryVideoEngine.initEngine(
            localSpec.width,
            localSpec.height,
            Rational(Math.round(localSpec.fps), 1)
        )
    }

    private val videoMimeType: String
        /**
         * 获取视频的mimeType
         *
         * @return
         */
        get() = VideoEditorHelper.getMimeType(mGalleryVideoEngine.getGalleryVideoClip().getVideoCodecType())

    /**
     * 限制预览的最大分辨率，若是视频的高度大于maxHeight，则等比缩小宽高
     *
     * @param maxHeight 限制预览的最大高度
     */
    override fun setPreviewMaxHeight(maxHeight: Int) {
        mGalleryVideoEngine.setPreviewMaxHeight(maxHeight)
    }

    /**
     * 创建视频预览窗口
     *
     * @param parent     视频窗口的载体view,[GalleryEditorVideoView]
     * @param shouldCrop 是否裁剪
     * @return 是否创建成功
     */
    override fun createLiveWindow(parent: ViewGroup, shouldCrop: Boolean): Boolean {
        return mGalleryVideoEngine.createLiveWindow(parent, shouldCrop)
    }

    override fun getTimeBase(): Long {
        return mGalleryVideoEngine.getTimeBase()
    }

    override fun getCurrentTime(): Long {
        return mGalleryVideoEngine.getCurrentTime()
    }

    /**
     * 从美摄接口直接获取时间轴的原始时间位置（未经末尾偏移调整）
     * @return Long 微秒
     */
    override fun getRealCurrentTime(): Long {
        return mGalleryVideoEngine.getRealCurrentTime()
    }

    override fun getTotalTime(): Long {
        return mGalleryVideoEngine.getTotalTime()
    }

    override fun getFrameDuration(): Int {
        return mGalleryVideoEngine.getFrameDuration()
    }

    override fun getCurrentFrame(): Bitmap? {
        return mGalleryVideoEngine.getCurrentFrame()
    }

    override fun repaintFrame() {
        mGalleryVideoEngine.repaintFrame()
    }

    override fun isPlaying(): Boolean {
        return mGalleryVideoEngine.isPlaying()
    }

    override fun play() {
        mAudioManager?.requestAudioFocus(mAudioFocusRequest)
        mGalleryVideoEngine.play()
    }

    override fun play(startTime: Long, endTime: Long) {
        mAudioManager?.requestAudioFocus(mAudioFocusRequest)
        mGalleryVideoEngine.play(startTime, endTime)
    }

    override fun resume() {
        // resume的工作是要重新播放音频，此处也需要重新获取音频焦点
        mAudioManager?.requestAudioFocus(mAudioFocusRequest)
        mGalleryVideoEngine.resume()
    }

    override fun pause() {
        mGalleryVideoEngine.pause()
    }

    override fun stop() {
        mGalleryVideoEngine.stop()
    }

    override fun stop(force: Boolean) {
        mGalleryVideoEngine.stop(force)
    }

    override fun seekTo(time: Long) {
        mGalleryVideoEngine.seekTo(time)
    }

    override fun reset() {
        mGalleryVideoEngine.reset()
    }

    override fun saveVideo(stringUri: String?): Boolean {
        return mGalleryVideoEngine.saveVideo(stringUri!!)
    }

    override fun saveVideo(stringUri: String?, videoHeight: Int, videoDateTaken: Long, videoPath: String): Boolean {
        return mGalleryVideoEngine.saveVideo(stringUri!!, videoHeight, videoDateTaken, videoPath)
    }

    override fun getExportVideoHdrType(): Int {
        return mGalleryVideoEngine.getExportVideoHdrType()
    }

    override fun saveShareVideo(stringUri: String?, start: Long, end: Long, videoHeight: Int): Boolean {
        return mGalleryVideoEngine.saveShareVideo(stringUri!!, start, end, videoHeight)
    }

    override fun saveVideo(info: SaveVideoInfo): Boolean {
        return mGalleryVideoEngine.saveVideo(info)
    }

    /**
     * 获取timeline指定帧时刻图片，并且管控是否继续播放
     * @param frameTime: 取图帧时刻
     * @param endTime: 播放停止时刻
     * @param isResumePlay: 播放暂停后是否继续播放
     */
    override suspend fun grabImageFromTimelineAsync(frameTime: Long, endTime: Long, isResumePlay: Boolean): Bitmap? {
        return mGalleryVideoEngine.grabImageFromTimelineAsync(frameTime, endTime, isResumePlay)
    }

    /**
     * 基于GPU的方式从原视频中抽目标色域帧
     * 适用于视频抽帧
     * 由于美摄限制，需要在 MAIN 线程中调用（主线程 + 挂起函数）
     * @param timeUs 时间点，微妙
     * @param config 目标config
     * @param colorSpace 目标色域
     * @return 目标色域的 bitmap
     */
    override suspend fun grabImageFromTimelineAsync(timeUs: Long, config: Bitmap.Config, colorSpace: ColorSpace): Bitmap? {
        return mGalleryVideoEngine.grabImageFromTimelineAsync(timeUs, config, colorSpace)
    }

    /**
     * 保存视频到指定路径，可指定起始时刻与帧率
     * @param filePath: 保存路径
     * @param startTime: 起始时刻
     * @param endTime: 结束时刻
     * @param fps: 帧率
     */
    override fun saveVideo(filePath: String, startTime: Long, endTime: Long, fps: Int): Boolean {
        return mGalleryVideoEngine.saveVideo(filePath, startTime, endTime, fps, getVideoUri(), getVideoFilePath())
    }

    override fun destroy(cleanAllContext: Boolean) {
        mAudioManager?.abandonAudioFocusRequest(mAudioFocusRequest)
        mGalleryVideoEngine.destroy(cleanAllContext)
    }

    override fun getVideoHeight(): Int {
        return mGalleryVideoEngine.getVideoHeight()
    }

    override fun getVideoWidth(): Int {
        return mGalleryVideoEngine.getVideoWidth()
    }

    override fun getVideoFileHeight(): Int {
        return mGalleryVideoEngine.getGalleryVideoClip().getVideoFileHeight()
    }

    override fun getVideoFps(): Number? {
        return mGalleryVideoEngine.getGalleryVideoClip().getFps()
    }

    override fun getSlowMotionPlayFps(): Int {
        return mGalleryVideoEngine.getGalleryVideoClip().getSlowMotionPlayFps()
    }

    override fun getVideoFileWidth(): Int {
        return mGalleryVideoEngine.getGalleryVideoClip().getVideoFileWidth()
    }

    override fun getVideoDuration(): Long {
        return mGalleryVideoEngine.getGalleryVideoClip().getDuration()
    }

    override fun getVideoHdrType(): Int {
        return mGalleryVideoEngine.getGalleryVideoClip().getEditVideoHdrType()
    }

    override fun getMeicamVideoTimeline(): Any? {
        return mGalleryVideoEngine.getMeicamVideoTimeline()
    }

    /**
     * 注册监听自定义编译任务的回调接口
     */
    override fun setCustomCompileTaskListener(videoListener: OnCustomCompileTaskStatusListener?) {
        mGalleryVideoEngine.setCustomCompileTaskListener(videoListener)
    }

    /**
     * 更新自定义编译任务执行状态
     * @param isRunning: 是否正在执行
     */
    override fun updateCustomCompileTaskStatus(isRunning: Boolean) {
        mGalleryVideoEngine.updateCustomCompileTaskStatus(isRunning)
    }

    /**
     * 当前是否允许Seek操作，默认为true
     * 目前只是添加了正在导出过程中不允许，后期如果还有其他限制条件亦可在此方法中添加
     * @return true/false
     */
    override fun isSeekEnable(): Boolean {
        if (mGalleryVideoEngine.getCustomCompileTaskStatus()) {
            return false
        }
        return true
    }
    /**---------------- engine base end ----------------*/

    /**---------------- rotate and cut ----------------*/
    override fun intoCutRotateMode() {
        mGalleryVideoEngine.getIGalleryVideoCutRotate().intoCutRotateMode()
    }

    override fun exitCutRotateMode() {
        mGalleryVideoEngine.getIGalleryVideoCutRotate().exitCutRotateMode()
    }

    override fun updateWaterMarkPosition(hideWaterMark: Boolean) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().updateWaterMarkPosition(hideWaterMark)
    }

    override fun hadHideWaterMark(): Boolean {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().hadHideWaterMark()
    }

    override fun setSelectRect(ratio: VideoRatio) {
        mGalleryVideoEngine.pause()
        mGalleryVideoEngine.getIGalleryVideoCutRotate().setSelectRect(ratio)
    }

    override fun updateRatio(ratio: VideoRatio) {
        mGalleryVideoEngine.getIGalleryVideoCutRotate().updateRatio(ratio)
    }

    override fun cutVideo(isAnimation: Boolean) {
        mGalleryVideoEngine.getIGalleryVideoCutRotate().cutVideo(isAnimation)
    }

    override fun rotateVideo(rotation: Int) {
        mGalleryVideoEngine.getIGalleryVideoCutRotate().rotateVideo(rotation)
    }

    override fun setSelectPosition(position: Int) {
        mGalleryVideoEngine.getIGalleryVideoCutRotate().setSelectPosition(position)
    }

    override fun getSelectPosition(): Int {
        return mGalleryVideoEngine.getIGalleryVideoCutRotate().getSelectPosition()
    }

    override fun getRotation(): Int {
        return mGalleryVideoEngine.getIGalleryVideoCutRotate().getRotation()
    }

    override fun getAspectRatio(): Int {
        return VideoEditorHelper.getAspectRatioFromVideoSize(getVideoWidth(), getVideoHeight())
    }

    override fun recover(ratio: VideoRatio, rotation: Int, isCut: Boolean, isAnimation: Boolean) {
        mGalleryVideoEngine.getIGalleryVideoCutRotate().recover(ratio, rotation, isCut, isAnimation)
    }

    override fun getRatio(): VideoRatio {
        return mGalleryVideoEngine.getIGalleryVideoCutRotate().getRatio()
    }

    /**---------------- rotate and end ----------------*/

    /**---------------- theme helper start ----------------*/
    override fun initThemeVideoClips(mediaInfos: ArrayList<MediaInfo?>?): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().initThemeVideoClips(mediaInfos)
    }

    override fun insertThemeVideoClip(infoList: ArrayList<MediaInfo?>?): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().insertThemeVideoClip(infoList)
    }

    override fun deleteThemeVideoClip(info: MediaInfo?): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().deleteThemeVideoClip(info)
    }

    override fun getThemeVideoClipList(): ArrayList<String?>? {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeVideoClipList()
    }

    override fun getThemeVideoClipCount(): Int {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeVideoClipCount()
    }

    override fun addThemeVideoCover(info: MediaInfo?): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().addThemeVideoCover(info)
    }

    override fun removeThemeVideoCover(): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().removeThemeVideoCover()
    }

    override fun getThemeVideoCover(): MediaInfo? {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeVideoCover()
    }

    override fun setThemeMusicMute(isMute: Boolean) {
        mGalleryVideoEngine.getGalleryThemeHelper().setThemeMusicMute(isMute)
    }

    override fun addThemeMusic(musicId: String?) {
        mGalleryVideoEngine.getGalleryThemeHelper().addThemeMusic(musicId)
    }

    override fun addThemeTrimMusic(musicId: String?, startTime: Long, endTime: Long) {
        mGalleryVideoEngine.getGalleryThemeHelper().addThemeTrimMusic(musicId, startTime, endTime)
    }

    override fun removeThemeMusic(force: Boolean) {
        mGalleryVideoEngine.getGalleryThemeHelper().removeThemeMusic(force)
    }

    override fun getCurrentThemeMusic(): String? {
        return mGalleryVideoEngine.getGalleryThemeHelper().getCurrentThemeMusic()
    }

    override fun getThemeCurrentMusicPos(): Int {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeCurrentMusicPos()
    }

    override fun addTheme(theme: ThemeInfo?): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().addTheme(theme)
    }

    override fun getCurrentTheme(): String? {
        return mGalleryVideoEngine.getGalleryThemeHelper().getCurrentTheme()
    }

    override fun getThemeCurrentThemePos(): Int {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeCurrentThemePos()
    }

    override fun addThemeCaption(title: String?, hint: String?): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().addThemeCaption(title, hint)
    }

    override fun updateThemeCaptionTitle(title: String?): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().updateThemeCaptionTitle(title)
    }

    override fun removeThemeCaption() {
        mGalleryVideoEngine.getGalleryThemeHelper().removeThemeCaption()
    }

    override fun reAddThemeCaption() {
        mGalleryVideoEngine.getGalleryThemeHelper().reAddThemeCaption()
    }

    override fun getThemeTitleText(): String? {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeTitleText()
    }

    override fun changeThemeDuration(time: Long): Boolean {
        return mGalleryVideoEngine.getGalleryThemeHelper().changeThemeDuration(time)
    }

    override fun getThemeMinTotalTime(): Long {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeMinTotalTime()
    }

    override fun getThemeMaxTotalTime(): Long {
        return mGalleryVideoEngine.getGalleryThemeHelper().getThemeMaxTotalTime()
    }

    override fun seekToThemePosition(index: Int) {
        mGalleryVideoEngine.getGalleryThemeHelper().seekToThemePosition(index)
    }

    override fun cleanBuiltinTransition() {
        mGalleryVideoEngine.getGalleryThemeHelper().cleanBuiltinTransition()
    }


    /**---------------- theme helper end ----------------*/

    /**---------------- video clip start ----------------*/
    override fun addVideoClip(uri: String?, filePath: String?, dataTaken: Long): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().addVideoClip(uri, filePath, dataTaken)
    }

    override fun addVideoClip(uri: String?, filePath: String?, dataTaken: Long, trimIn: Long, trimOut: Long): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().addVideoClip(
            uri,
            filePath,
            dataTaken,
            trimIn,
            trimOut
        )
    }

    override fun checkVideoSupported(path: String?, context: Context?): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().checkVideoSupported(path, context)
    }

    override fun checkVideoSoftSupported(path: String?, context: Context?): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().checkVideoSoftSupported(path, context)
    }

    override fun trimVideo(trimIn: Long, trimOut: Long) {
        mGalleryVideoEngine.getGalleryVideoClip().trimVideo(trimIn, trimOut)
    }

    override fun addSlowMotionVideoClip(
        fileUri: String?,
        filePath: String?,
        dataTaken: Long,
        slowfps: Int,
        slowtimelist: LongArray?,
        fullslowmotion: Boolean
    ): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().addSlowMotionVideoClip(
            fileUri, filePath, dataTaken, slowfps, VideoEditorActivity.SLOW_MOTION_DEFAULT_FPS, slowtimelist, fullslowmotion, false
        )
    }

    override fun addHfrFullSlowMotionVideoClip(
        fileUri: String?,
        filePath: String?,
        dataTaken: Long,
        slowfps: Int,
        playfps: Int
    ): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().addSlowMotionVideoClip(
            fileUri, filePath, dataTaken, slowfps, playfps, null, true, true
        )
    }

    override fun showVideoThumbLoader(trimView: ViewGroup?) {
        mGalleryVideoEngine.getGalleryVideoThumbnail().showVideoThumbnail(trimView)
    }

    override fun showTrimDetailThumbLoader(trimView: ViewGroup?, time: Long) {
        mGalleryVideoEngine.getGalleryVideoThumbnail().showTrimDetailThumbnail(trimView, time)
    }

    override fun showFxThumbLoader(trimView: ViewGroup?) {
        mGalleryVideoEngine.getGalleryVideoThumbnail().showFxThumbnail(trimView)
    }

    override fun showSubTitleThumbLoader(trimView: ViewGroup?) {
        mGalleryVideoEngine.getGalleryVideoThumbnail().showSubtitleThumbnail(trimView)
    }

    /**
     * 加载视频导出olive缩图轴组件
     * @param trimView 父容器
     */
    override fun showVideoOliveThumbLoader(trimView: ViewGroup?) {
        mGalleryVideoEngine.getGalleryVideoThumbnail().showVideoOliveThumbnail(trimView)
    }

    /**
     * 加载自定义剪辑缩图轴组件
     */
    override fun showCustomTrimThumbLoader(trimView: ViewGroup?, time: Long, padding: Int) {
        mGalleryVideoEngine.getGalleryVideoThumbnail().showCustomTrimThumbnail(trimView, time, padding)
    }

    override fun changeSlowMotion(enterA: Float, outA: Float, enterB: Float, outB: Float): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().changeSlowMotion(enterA, outA, enterB, outB)
    }

    override fun isSupportSlowMotionMode(): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().isSupportSlowMotionMode()
    }

    override fun isHfrSlowMotion(): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().isHfrSlowMotion()
    }

    override fun getSlowMotionList(): FloatArray? {
        return mGalleryVideoEngine.getGalleryVideoClip().getSlowMotionList()
    }

    override fun getSlowMotionOriginalList(): FloatArray? {
        return mGalleryVideoEngine.getGalleryVideoClip().getSlowMotionOriginalList()
    }

    override fun getTrimInTime(): Long {
        return mGalleryVideoEngine.getGalleryVideoClip().getTrimInTime()
    }

    override fun getTrimOutTime(): Long {
        return mGalleryVideoEngine.getGalleryVideoClip().getTrimOutTime()
    }

    override fun setOriginalMusicMute(isMute: Boolean) {
        mGalleryVideoEngine.getGalleryVideoClip().setOriginalMusicMute(isMute)
    }

    override fun getOriginalMusicMuted(): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().getOriginalMusicMuted()
    }

    override fun changeAudioSpeed(speed: Float): Boolean {
        return mGalleryVideoEngine.getGalleryAudioClip().changeAudioSpeed(speed)
    }

    override fun changeVideoSpeed(speed: Float): Boolean {
        return mGalleryVideoEngine.getGalleryVideoClip().changeVideoSpeed(speed)
    }

    override fun getVideoSpeed(): Float {
        return mGalleryVideoEngine.getGalleryVideoClip().getTrimVideoSpeed()
    }

    override fun setVideoSpeedChanged(changed: Boolean) {
        mGalleryVideoEngine.getGalleryVideoClip().setVideoSpeedChanged(changed)
    }

    override fun setVideoClipChanged(changed: Boolean) {
        mGalleryVideoEngine.getGalleryVideoClip().setVideoClipChanged(changed)
    }

    override fun setExtraVideoRotation(rotation: Int) {
        mGalleryVideoEngine.getGalleryVideoClip().setExtraVideoRotation(rotation)
    }

    override fun getExtralVideoRotation(): Int {
        return mGalleryVideoEngine.getGalleryVideoClip().getExtraVideoRotation()
    }

    /**---------------- video clip end ----------------*/

    /**---------------- audio clip start ----------------*/
    override fun applySong(songInfo: SongInfo?): Boolean {
        if (songInfo == null) {
            e(TAG, "applySong, songInfo is null.")
            return false
        }
        var ret = false
        if (songInfo.position == 0) {
            removeMusic()
            setCurrentSongInfo(songInfo)
            ret = true
        } else {
            ret = mGalleryVideoEngine.getGalleryAudioClip().addSong(songInfo)
        }
        return ret
    }

    override fun getCurrentSongInfo(): SongInfo? {
        return mGalleryVideoEngine.getGalleryAudioClip().getCurrentSongInfo()
    }

    override fun setCurrentSongInfo(songInfo: SongInfo?) {
        mGalleryVideoEngine.getGalleryAudioClip().setCurrentSongItem(songInfo)
    }

    override fun addTrimMusic(
        contentUri: String?,
        filePath: String?,
        start: Long,
        end: Long,
        duration: Long
    ): Boolean {
        return mGalleryVideoEngine.getGalleryAudioClip().addTrimMusic(contentUri, start, end, duration)
    }

    override fun reAlignMusic(startTime: Long): Boolean {
        return mGalleryVideoEngine.getGalleryAudioClip().reAlignMusic(startTime)
    }

    private fun removeMusic(): Boolean {
        return mGalleryVideoEngine.getGalleryAudioClip().removeMusic()
    }

    override fun setLocalMusicIndex(index: Int) {
        mGalleryVideoEngine.getGalleryAudioClip().setLocalMusicIndex(index)
    }

    override fun getLocalMusicPath(): String? {
        return null
    }

    override fun resetLocalMusicIndex() {
        mGalleryVideoEngine.getGalleryAudioClip().resetLocalMusicIndex()
    }

    override fun getLocalMusicUri(): String? {
        return mGalleryVideoEngine.getGalleryAudioClip().getLocalMusicUri()
    }

    override fun getLocalMusicTrimStart(): Long {
        return mGalleryVideoEngine.getGalleryAudioClip().getLocalMusicTrimStart()
    }

    override fun getLocalMusicTrimEnd(): Long {
        return mGalleryVideoEngine.getGalleryAudioClip().getLocalMusicTrimEnd()
    }

    override fun getLocalMusicDuration(): Long {
        return mGalleryVideoEngine.getGalleryAudioClip().getLocalMusicDuration()
    }

    override fun saveMusicState() {
        mGalleryVideoEngine.getGalleryAudioClip().saveMusicState()
    }

    override fun resetMusicState() {
        mGalleryVideoEngine.getGalleryAudioClip().resetMusicState()
    }

    /**---------------- audio clip end ----------------*/

    /**---------------- Video filter start ----------------*/
    override fun installSubTitleStyleRes() {
        mGalleryVideoEngine.getGalleryVideoSubTitle().installSubTitleStyleRes()
    }

    override fun applyVideoFilter(filterInfo: FilterInfo?, index: Int): Boolean {
        d(TAG, "applyVideoFilter, filterInfo = $filterInfo")
        return mGalleryVideoEngine.getGalleryVideoFilter().setFilter(filterInfo, index)
    }

    override fun applyVideoFilter(path: String): Boolean {
        return mGalleryVideoEngine.getGalleryVideoFilter().applyLutFx(path)
    }

    override fun getCurrentFilter(): FilterInfo? {
        return mGalleryVideoEngine.getGalleryVideoFilter().getCurrentInfo()
    }

    override fun getCurrentFilterIndex(): Int {
        return mGalleryVideoEngine.getGalleryVideoFilter().getCurrentFilterIndex()
    }

    override fun getCurrentFilterPath(): String? {
        return mGalleryVideoEngine.getGalleryVideoFilter().getFilterPath()
    }

    override fun setCurrentFilter(filterInfo: FilterInfo?) {
        mGalleryVideoEngine.getGalleryVideoFilter().setCurrentInfo(filterInfo)
    }

    private fun removeVideoFilter() {
        mGalleryVideoEngine.getGalleryVideoFilter().removeVideoFilter()
    }

    override fun setWaterMark(model: String?, timeAndAddress: String?): Boolean {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().setWaterMark(model, timeAndAddress)
    }

    override fun updateWaterMarkDuration() {
        mGalleryVideoEngine.getGalleryVideoSubTitle().updateWaterMarkDuration()
    }

    override fun hasWaterMark(): Boolean {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().hasWaterMark()
    }

    override fun addSubTitle(text: String?, context: Context?): Long {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().addSubTitle(text, context)
    }

    override fun addSubTitleInfo(info: SubTitleInfo?, context: Context?) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().addSubTitleInfo(info, context)
    }

    override fun renameSubTitle(subTitleIndex: Long, text: String?) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().renameSubTitle(subTitleIndex, text)
    }


    override fun removeSubTitle(subtitlePos: Long, needSeek: Boolean): SubTitleInfo? {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().removeSubTitle(subtitlePos, needSeek)
    }

    override fun moveSubTitle(subTitleIndex: Long, prePointF: PointF?, nowPointF: PointF?) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().moveSubTitle(subTitleIndex, prePointF, nowPointF)
    }

    override fun checkAndGetSubTitleEditPos(curPos: Long): List<SubTitleInfo?>? {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().checkAndGetSubTitleEditPos(curPos)
    }

    override fun getSubTitleList(): ArrayList<SubTitleInfo?>? {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().getSubTitleList()
    }

    override fun reAlignSubTitle(offsetUs: Long) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().moveAllSubTitle(offsetUs)
    }

    override fun reAlignSubTitle(offsetCalculateFunction: Function<Long?, Long?>?) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().moveAllSubTitle(offsetCalculateFunction)
    }

    override fun reAlignSubTitle(speed: Float) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().moveAllSubTitle(speed)
    }

    /**---------------- Video filter end ----------------*/

    /**---------------- Video template start ----------------*/
    override fun removeTemplateElements(keepBGM: Boolean) {
        d(TAG, "removeTemplateElements keepBGM = $keepBGM")
        mGalleryVideoEngine.getGalleryVideoSticker().removeVideoSticker()
        if (!keepBGM) {
            mGalleryVideoEngine.getGalleryAudioClip().removeMusic()
        }
        removeVideoFilter()
    }

    override fun applyVideoSticker(stickerInfo: StickerInfo) {
        d(TAG, "applyVideoSticker, stickerInfo = $stickerInfo")
        mGalleryVideoEngine.getGalleryVideoSticker().applyVideoSticker(stickerInfo)
    }

    override fun reAlignVideoSticker(inTimeNs: Long) {
        mGalleryVideoEngine.getGalleryVideoSticker().reAlignVideoSticker(inTimeNs)
    }

    override fun setCurrentTemplateInfo(templateInfo: TemplateInfo?) {
        mGalleryVideoEngine.getGalleryVideoTemplate().setCurrentTemplateInfo(templateInfo)
    }

    override fun getCurrentTemplateInfo(): TemplateInfo? {
        return mGalleryVideoEngine.getGalleryVideoTemplate().getCurrentTemplateInfo()
    }

    /**---------------- Video template end ----------------*/
    override fun setVideoBackgroundColor(color: Color?) {
        if (mGalleryVideoEngine is MeicamVideoEngine) {
            mGalleryVideoEngine.setBackgroundColor(color!!)
        }
    }

    override fun hasVideoChanged(): Boolean {
        return mGalleryVideoEngine.hasVideoChanged()
    }

    override fun dumpEngineInfo(writer: PrintWriter?) {
        mGalleryVideoEngine.dumpEngineInfo(writer!!)
    }

    /**---------------- Video fx start ----------------*/
    override fun getAppliedFxTime(): Long {
        return mGalleryVideoEngine.getGalleryVideoFx().getAppliedFxTime()
    }

    override fun setAppliedFxTime(time: Long) {
        mGalleryVideoEngine.getGalleryVideoFx().setAppliedFxTime(time)
    }

    override fun setCurrentFxInfo(fxInfo: FxInfo?) {
        mGalleryVideoEngine.getGalleryVideoFx().setCurrentFxInfo(fxInfo)
    }

    override fun getCurrentFxInfo(): FxInfo? {
        return mGalleryVideoEngine.getGalleryVideoFx().getCurrentFxInfo()
    }

    override fun removeVideoFx() {
        mGalleryVideoEngine.getGalleryVideoFx().removeVideoFx()
    }

    override fun installVideoFx(fxInfo: FxInfo) {
        mGalleryVideoEngine.getGalleryVideoFx().installVideoFx(fxInfo)
    }

    override fun applyVideoFx(fxInfo: FxInfo, startPos: Long) {
        mGalleryVideoEngine.getGalleryVideoFx().applyVideoFx(fxInfo, startPos)
        mGalleryVideoEngine.getGalleryVideoFx().setCurrentFxInfo(fxInfo)
    }

    override fun reAlignVideoFx(offsetUs: Long) {
        mGalleryVideoEngine.getGalleryVideoFx().moveVideoFx(offsetUs)
    }

    override fun reAlignVideoFx(speed: Float) {
        mGalleryVideoEngine.getGalleryVideoFx().moveVideoFx(speed)
    }

    override fun clearLastFx() {
        mGalleryVideoEngine.getGalleryVideoFx().clearLastFx()
    }

    override fun reAlignVideoFx(offsetCalculateFunction: Function<Long?, Long?>?) {
        mGalleryVideoEngine.getGalleryVideoFx().moveVideoFx(offsetCalculateFunction)
    }

    override fun computeLocationInfo(latitude: Double, longitude: Double) {
        mGalleryVideoEngine.getGalleryVideoSubTitle().computeLocationInfo(latitude, longitude)
    }

    override fun clearLocationInfo() {
        mGalleryVideoEngine.getGalleryVideoSubTitle().clearLocationInfo()
    }

    override fun getAddress(): ConfigAddress? {
        return mGalleryVideoEngine.getGalleryVideoSubTitle().getAddress()
    }

    /**---------------- engine fx  end----------------*/
    override fun registerStateListener(listener: OnVideoEditStateListener) {
        //do nothing
    }

    override fun unregisterStateListener(listener: OnVideoEditStateListener) {
        //do nothing
    }

    override fun setAspectRatio(rational: Rational) {
        //do nothing
    }

    override fun addClip(type: ClipType, filePath: String?, trimIn: Long, trimOut: Long): IClip? {
        return null
    }

    override fun getClip(type: ClipType, index: Int): IClip? {
        return null
    }

    override fun isClipPrePrepared(index: Int): Boolean {
        return true
    }

    @Throws(Exception::class)
    override fun close() {
        //do nothing
    }

    override fun appendTransition(transitionIndex: Int, clipIndex: Int, duration: Long): Boolean {
        return false
    }

    override fun appendTransition(transitionIndex: Int, clip: IClip, duration: Long): Boolean {
        return false
    }

    override fun removeTransition(clipIndex: Int, index: Int): Boolean {
        return false
    }

    override fun removeTransition(clip: IClip, index: Int): Boolean {
        return false
    }

    override fun resizeTransition(clipIndex: Int, duration: Long): Boolean {
        return false
    }

    override fun resizeTransition(clip: IClip, duration: Long): Boolean {
        return false
    }

    override fun initEngine(mediaItemPath: String?): Boolean {
        return false
    }

    override fun addClip(type: ClipType, bitmap: Bitmap, trimIn: Long, trimOut: Long): IClip? {
        return null
    }

    override fun prepare() {
        //do nothing
    }

    override fun dataReady() {
        //do nothing
    }

    override fun applyLocalSong(songInfo: SongInfo?): Boolean {
        return false
    }

    override fun getVideoUri(): String? {
        return mVideoUri
    }

    override fun getVideoFilePath(): String? {
        return mVideoFilePath
    }

    companion object {
        const val PLAY_STATUS_READY: Int = 1
        const val PLAY_STATUS_STOPPED: Int = 2
        const val PLAY_STATUS_FINISH: Int = 3
        const val PLAY_STATUS_PLAYING: Int = 4
        const val PLAY_STATUS_PAUSED: Int = 5

        const val EXPORT_STATUS_READY: Int = 1
        const val EXPORT_STATUS_FINISH: Int = 2
        const val EXPORT_STATUS_COMPLETE: Int = 3
        const val EXPORT_STATUS_CANCEL: Int = 4
        const val EXPORT_STATUS_ERROR: Int = 5

        private const val TAG = "GalleryVideoEngineManager"
    }
}