/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - ThumbScrollView.java
 ** Description: ThumbScrollView.
 ** Version: 1.0
 ** Date : 2017/11/01
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/01    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.ui;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.NinePatch;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;

import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoThumbnailView;
import com.oplus.gallery.framework.abilities.videoedit.data.SubTitleInfo;

import java.util.ArrayList;

public class GalleryVideoSubTitleCoverView extends View {

    private static final String TAG = "GalleryVideoSubTitleEditView";

    private static final int SUB_TITLE_COVER_ALPHA = 128;

    private ArrayList<SubTitleInnerPos> mSubTitleCoverList = new ArrayList<>();
    private int mHeight = 0;
    private int mWidth = 0;
    private NinePatch mMaskNinePatch;
    private int mBitmapPadding;

    public GalleryVideoSubTitleCoverView(Context context) {
        this(context, null);
        init();
    }

    public GalleryVideoSubTitleCoverView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
        init();
    }

    public GalleryVideoSubTitleCoverView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        Bitmap maskBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.videoeditor_video_editor_text_control);
        mMaskNinePatch = new NinePatch(maskBitmap, maskBitmap.getNinePatchChunk(), null);
        mBitmapPadding = getResources().getDimensionPixelSize(R.dimen.video_editor_subtitle_mask_padding);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if ((mHeight == 0) && (mWidth == 0)) {
            mWidth = getWidth();
            mHeight = getHeight();
        }
        if (mSubTitleCoverList.size() > 0) {
            for (SubTitleInnerPos coverpos : mSubTitleCoverList) {
                if (coverpos.mIsInDrawArea) {
                    Rect bg = new Rect(
                            coverpos.mDrawStartX + mBitmapPadding,
                            mBitmapPadding,
                            coverpos.mDrawEndX - mBitmapPadding,
                            mHeight - mBitmapPadding);
                    mMaskNinePatch.draw(canvas, bg);
                }
            }
        }
    }

    public void scrollSubTextCover(GalleryVideoThumbnailView thumbnailView) {
        long currentStartTime = thumbnailView.mapTimelinePosFromX(0);
        long currentEndTime = thumbnailView.mapTimelinePosFromX(thumbnailView.getWidth());
        for (SubTitleInnerPos coverpos : mSubTitleCoverList) {
            if ((coverpos.mEndTimePos < currentStartTime)
                    || (coverpos.mStartTimePos > currentEndTime)) {
                coverpos.mIsInDrawArea = false;
            } else {
                coverpos.mIsInDrawArea = true;
                coverpos.mDrawStartX = thumbnailView.mapXFromTimelinePos(coverpos.mStartTimePos);
                coverpos.mDrawEndX = thumbnailView.mapXFromTimelinePos(coverpos.mEndTimePos);
            }
        }
        invalidate();
    }

    public void updateSubText(ArrayList<SubTitleInfo> subTitleArrayList, GalleryVideoThumbnailView thumbnailView,
                              long trimInTime, long trimOutTime) {
        if (subTitleArrayList != null) {
            mSubTitleCoverList.clear();
            for (SubTitleInfo span : subTitleArrayList) {
                if (!span.getVisibility()) {
                    continue;
                }
                long inTime = span.getStartTimePos();
                long outTime = span.getEndTimePos();
                if ((inTime > trimOutTime) || (outTime < trimInTime)) {
                    continue;
                } else if ((inTime < trimInTime) && (outTime > trimInTime)) {
                    inTime = 0;
                    outTime -= trimInTime;
                } else if ((inTime < trimOutTime) && (outTime > trimOutTime)) {
                    inTime -= trimInTime;
                    outTime = (trimOutTime - trimInTime);
                } else {
                    inTime -= trimInTime;
                    outTime -= trimInTime;
                }
                mSubTitleCoverList.add(new SubTitleInnerPos(inTime, outTime));
            }
            scrollSubTextCover(thumbnailView);
            invalidate();
        }
    }

    private static class SubTitleInnerPos {

        boolean mIsInDrawArea = false;
        long mStartTimePos = 0;
        long mEndTimePos = 0;
        int mDrawStartX = 0;
        int mDrawEndX = 0;

        SubTitleInnerPos(long start, long end) {
            mStartTimePos = start;
            mEndTimePos = end;
        }
    }

}
