/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorThemeUIController.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/10
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/10    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.memories;

import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.MEMORIES_ACTIVITY;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE;
import static com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper.MINUTES_60;
import static com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper.SECONDS_1000;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.WorkerThread;
import androidx.core.view.WindowInsetsCompat;

import com.coui.appcompat.seekbar.COUISeekBar;
import com.coui.appcompat.theme.COUIThemeOverlay;
import com.oplus.gallery.addon.osense.CpuFrequencyManager;
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesUtils;
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter;
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog;
import com.oplus.gallery.foundation.ui.dialog.LoadingDialog;
import com.oplus.gallery.foundation.ui.helper.RippleHelper;
import com.oplus.gallery.foundation.ui.systembar.ActivitySystemBarStyle;
import com.oplus.gallery.foundation.ui.systembar.ImmersiveActivitySystemBarStyle;
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.foundation.util.version.AppVersionUtils;
import com.oplus.gallery.framework.abilities.config.IConfigAbility;
import com.oplus.gallery.framework.abilities.videoedit.data.MediaInfo;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.router_lib.annotations.RouterNormal;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.videoeditor.data.VideoSpec;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.VideoBaseActivity;
import com.oplus.gallery.videoeditorpage.base.app.EditorBaseState;
import com.oplus.gallery.videoeditorpage.base.ui.ControlBarView;
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.base.util.VideoTrackHelper;
import com.oplus.gallery.videoeditorpage.engine.GalleryEditorVideoView;
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoEngineListener;
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoEngineManager;
import com.oplus.gallery.videoeditorpage.memories.app.EditorPhotoState;
import com.oplus.gallery.videoeditorpage.memories.ui.MemoriesView;
import com.oplus.gallery.videoeditorpage.resource.manager.MusicSourceManager;
import com.oplus.gallery.videoeditorpage.resource.manager.ThemeSourceManager;
import com.oplus.gallery.videoeditorpage.resource.room.bean.ThemeItem;
import com.oplus.gallery.videoeditorpage.resource.util.ThemeAutoDownloadHelper;

import org.jetbrains.annotations.NotNull;

import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.ArrayList;

import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

@RouterNormal(path = MEMORIES_ACTIVITY)
public class MemoriesActivity extends VideoBaseActivity implements
        View.OnClickListener, ControlBarView.OnCancelDoneClickListener, MeicamEngineLimiter.LimitAble {
    // activity result, request code
    public static final int REQUEST_CODE_TAKE_MUSIC = 100;
    public static final int REQUEST_CODE_TAKE_COVER = 101;
    public static final int REQUEST_CODE_TAKE_PHOTO = 102;

    // handle message flag
    public static final int MSG_START_LOADING = 0;
    public static final int MSG_UPDATE_SEEKBAR = 1;
    public static final int MSG_UPDATE_PLAYBTN = 2;
    public static final int MSG_DATA_READY = 3;
    public static final int MSG_LOAD_FINISH = 4;
    public static final int MSG_VIDEO_READY = 5;
    public static final int MSG_SEND_DATA = 6;
    public static final int MSG_TAKE_COVER_RESULT = 8;
    public static final int MSG_EDIT_MODE = 9;
    public static final int MSG_REFRESH_VIDEO = 10;
    public static final int MSG_CLEAR_ENGINE_BG = 11;
    public static final int MSG_EXIT = 100;
    // delay duration
    public static final int TAKE_RESULT_DELAY = 100;
    public static final int LOAD_FINISH_DELAY = 600;
    public static final int EXPORT_FINISH_DELAY = TimeUtils.TIME_1_SEC_IN_MS;
    public static final int CLEAR_ENGINE_BG_DELAY = TimeUtils.TIME_1_SEC_IN_MS;
    private static final String TAG = "MemoriesActivity";
    private static final String VERSION_NAME = "version_name";
    private static final String DEFAULT_VERSION_NAME = "none";
    // engine config flag
    private static final int ENGINE_VIEW_WIDTH = 720;
    private static final int ENGINE_VIEW_HEIGHT = 1280;
    private static final int ENGINE_VIEW_FPS = 25;
    private static final int MSG_REFRESH_WINDOW = 26;
    private static final int REFRESH_NEED_SEEK = 2;
    private static final int REFRESH_WINDOW_DELAY = 100;
    private Object mEngineLock = new Object();

    // engine
    private GalleryEditorVideoView mGalleryEditorVideoView;
    private GalleryVideoEngineManager mGalleryEngineManager;
    //
    private MemoriesManager mMemoriesManager;
    // action bar / control bar
    private MemoriesView mMemoriesView;
    private View mActionBar;
    private View mBottomAreaView;
    private ControlBarView mControlBar;
    private ImageView mSendView;
    private ImageView mEditView;
    private ImageView mBackView;
    private FrameLayout mTimeSeekBar;
    private ImageButton mPlayBtn;
    private TextView mCurTimeView;
    private TextView mTotalTimeView;
    private COUISeekBar mSeekBar;
    private boolean mSeeking = false;
    private ConfirmDialog mCancelDialog;
    // first loading dialog
    private LoadingDialog mLoadingDialog;
    private LoadingDialog mSaveLoadingDialog;
    private boolean mShowBars = false;
    private boolean mStoped = false;
    private boolean mFirstGotoEditor = true;
    private boolean mIsGiveUpChange = false;

    private int mEditModeBgColor = 0;
    private int mPlayModeBgColor = Color.BLACK;

    private BroadcastReceiver mSDCardReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            GLog.d(TAG, "onReceive, action = " + action);
            if (Intent.ACTION_MEDIA_EJECT.equals(action)
                    || Intent.ACTION_MEDIA_UNMOUNTED.equals(action)
                    || Intent.ACTION_MEDIA_REMOVED.equals(action)) {
                if (MemoriesManager.isMediaInSDCard()) {
                    GLog.d(TAG, "onReceive media in sdcard, exit.");
                    finish();
                    return;
                }
                if (mMemoriesManager.isSavingInSDCard()) {
                    GLog.d(TAG, "onReceive saving video in sdcard, exit.");
                    finish();
                    return;
                }
            }
        }
    };
    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_START_LOADING:
                    GLog.d(TAG, "MSG_START_LOADING");
                    showLoadingDialog(MemoriesActivity.this, R.string.videoeditor_memories_dialog_loading);
                    break;
                case MSG_UPDATE_SEEKBAR:
                    updateTimeAndSeekBar();
                    break;
                case MSG_UPDATE_PLAYBTN:
                    updatePlayButton();
                    break;
                case MSG_DATA_READY:
                    GLog.d(TAG, "MSG_DATA_READY");
                    if (!isFinishing() && !isDestroyed()) {
                        updateBackTitle();
                        initEngineByThread();
                    } else {
                        GLog.w(TAG, "MSG_DATA_READY is finishing or destroyed. isFinishing = " + isFinishing());
                    }
                    break;
                case MSG_LOAD_FINISH:
                    handleMsgLoadFinish();
                    break;
                case MSG_VIDEO_READY:
                    handleMsgVideoReady();
                    break;
                case MSG_SEND_DATA:
                    GLog.d(TAG, "MSG_SEND_DATA");
                    if ((mMemoriesManager != null) && mMemoriesManager.isVideoSaving()) {
                        mMemoriesManager.destroyDialog();
                        mMemoriesManager.startShareActivity();
                    }
                    break;
                case MSG_TAKE_COVER_RESULT:
                    if (msg.obj instanceof Intent) {
                        Intent data = (Intent) msg.obj;
                        takeCoverResult(data);
                    }
                    break;
                case MSG_EDIT_MODE:
                    gotoEditMode();
                    break;
                case MSG_REFRESH_VIDEO:
                    handleMsgRefreshVideo(msg);
                    break;
                case MSG_CLEAR_ENGINE_BG:
                    mGalleryEditorVideoView.setBackground(null);
                    break;
                // error, exit memories view
                case MSG_EXIT:
                    GLog.d(TAG, "MSG_EXIT");
                    ToastUtil.showShortToast(R.string.videoeditor_memories_toast_loading_failed);
                    finish();
                    break;
                case MSG_REFRESH_WINDOW:
                    if ((mGalleryEngineManager != null) && (mControlBar.getCurrentEditor() != null)) {
                        if (msg.arg1 == REFRESH_NEED_SEEK) {
                            mGalleryEngineManager.seekTo(mGalleryEngineManager.getCurrentTime());
                        }
                        mControlBar.getCurrentEditor().onWindowChangeRefresh();
                    }
                    break;
                default:
                    break;
            }
        }
    };


    private GalleryVideoEngineListener mGalleryVideoEngineListener = new GalleryVideoEngineListener() {
        // play callback
        @Override
        public void onPlayStatusChange(int status) {
            GLog.d(TAG, "GalleryVideoEngineListener.onPlayStatusChange status = " + status);
            if (status == GalleryVideoEngineManager.PLAY_STATUS_FINISH) {
                synchronized (mEngineLock) {
                    if (mGalleryEngineManager != null) {
                        mGalleryEngineManager.reset();
                    }
                }
            } else if ((status == GalleryVideoEngineManager.PLAY_STATUS_READY)
                    || (status == GalleryVideoEngineManager.PLAY_STATUS_STOPPED)) {
                mHandler.removeMessages(MSG_UPDATE_PLAYBTN);
                mHandler.sendEmptyMessage(MSG_UPDATE_PLAYBTN);
            }
            mHandler.sendEmptyMessage(MSG_UPDATE_SEEKBAR);
        }

        @Override
        public void onPlayPositionChange(long position) {
            mHandler.sendEmptyMessage(MSG_UPDATE_SEEKBAR);
        }

        // save mp4 callback
        @Override
        public void onExportStatusChange(int state) {
            GLog.d(TAG, "GalleryVideoEngineListener.onExportStatusChange state = " + state);
            switch (state) {
                case GalleryVideoEngineManager.EXPORT_STATUS_COMPLETE:
                    BuildersKt.launch(AppScope.INSTANCE, Dispatchers.getIO(), CoroutineStart.DEFAULT,
                        (coroutineScope, continuation) -> {
                            onExportFinish();
                            return null;
                        });
                    break;

                case GalleryVideoEngineManager.EXPORT_STATUS_CANCEL:
                case GalleryVideoEngineManager.EXPORT_STATUS_ERROR:
                    if (mMemoriesManager != null) {
                        mMemoriesManager.onCanceExportVideo(true);
                    }
                    CpuFrequencyManager.setAction(MemoriesManager.SAVE_VIDEO_ACTION, CpuFrequencyManager.TIMEOUT_CLOSE);
                    break;

                default:
                    break;
            }
        }

        @Override
        public void onExportProgressChange(int progress) {
            if (mMemoriesManager != null) {
                mMemoriesManager.setDialogProgress(progress);
            }
        }

        // stream ready callback
        @Override
        public void onEngineStateChanged(int state) {
            GLog.d(TAG, "GalleryVideoEngineListener.onEngineStateChanged state = " + state);
            mHandler.sendEmptyMessage(MSG_UPDATE_SEEKBAR);
        }

        @Override
        public void onFirstVideoFrameReady() {
            GLog.d(TAG, "GalleryVideoEngineListener.onFirstVideoFrameReady");
            mHandler.sendEmptyMessage(MSG_UPDATE_SEEKBAR);
            mHandler.sendEmptyMessage(MSG_VIDEO_READY);
        }

        @Override
        public void onEngineException(int errCode, String msg) {
            GLog.d(TAG, "GalleryVideoEngineListener.onEngineException errCode = " + errCode + ", msg = " + msg);
        }
    };

    @WorkerThread
    private void onExportFinish() {
        if ((mMemoriesManager != null) && mMemoriesManager.saveVideoFinishSandbox()) {
            mHandler.sendEmptyMessageDelayed(MSG_SEND_DATA, EXPORT_FINISH_DELAY);
        } else {
            mHandler.sendEmptyMessage(MemoriesActivity.MSG_EXIT);
        }
        CpuFrequencyManager.setAction(MemoriesManager.SAVE_VIDEO_ACTION, CpuFrequencyManager.TIMEOUT_CLOSE);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        GLog.d(TAG, "onKeyUp() keyCode = " + keyCode);
        if (DoubleClickUtils.isFastDoubleClick()) {
            GLog.d(TAG, "onKeyUp() isFastDoubleClick  return");

            return super.onKeyUp(keyCode, event);
        }
        switch (keyCode) {
            case KeyEvent.KEYCODE_HEADSETHOOK:
            case KeyEvent.KEYCODE_MEDIA_PLAY:
            case KeyEvent.KEYCODE_MEDIA_PAUSE:
            case KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE:
                if (mGalleryEngineManager.isPlaying()) {
                    mGalleryEngineManager.pause();
                } else {
                    mGalleryEngineManager.resume();
                }
                return true;
            case KeyEvent.KEYCODE_MEDIA_STOP:
                if (mGalleryEngineManager.isPlaying()) {
                    mGalleryEngineManager.pause();
                }
                return true;
            default:
                break;
        }
        return super.onKeyUp(keyCode, event);
    }

    private void setWindowBackground(int color) {
        getWindow().setBackgroundDrawable(new ColorDrawable(color));
    }

    @Override
    public void loadMain() {
        GLog.d(TAG, "loadMain start");
        MeicamEngineLimiter.getInstance().register(this);
        registerSDCardBroadcast();
        //before setContentView
        mGalleryEngineManager = new GalleryVideoEngineManager(this, mGalleryVideoEngineListener);

        setContentView(R.layout.videoeditor_memories_editor_main);

        mMemoriesManager = new MemoriesManager(this, mHandler, mGalleryEngineManager);
        checkResource();
        GLog.d(TAG, "loadMain end");
    }

    private void checkResource() {
        BuildersKt.launch(
                AppScope.INSTANCE,
                (CoroutineContext) Dispatchers.getIO(),
                CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    doCheckResource();
                    return null;
                }
        );
    }

    private void doCheckResource() {
        if (isNeedRefreshBuiltin()) {
            boolean musicStatus = MusicSourceManager.getInstance().checkBuiltinItem(true);
            boolean themeStatus = ThemeSourceManager.getInstance().checkBuiltinItem(true);
            GLog.d(TAG, "doCheckResource : true");
            if (musicStatus && themeStatus) {
                updateVersionName();
            } else {
                GLog.e(TAG, "doCheckResource failed! musicStatus = "
                        + musicStatus + ", themeStatus = " + themeStatus);
            }
        }
        GLog.d(TAG, "doCheckResource. start download theme.");
        ThemeAutoDownloadHelper.autoDownloadInWifi((success, msg) -> {
            //Do nothing
        });
    }

    private boolean isNeedRefreshBuiltin() {
        String versionName = AppVersionUtils.getVersionName(this.getApplicationContext());
        String oldVersionName = VideoEditorHelper.getStringPref(
                this.getApplicationContext(),
                VERSION_NAME,
                DEFAULT_VERSION_NAME
        );
        GLog.d(TAG, "[isNeedRefreshBuiltin]  " + versionName + " : " + oldVersionName);
        return !TextUtils.equals(oldVersionName, versionName);
    }

    private void updateVersionName() {
        String versionName = AppVersionUtils.getVersionName(this.getApplicationContext());
        GLog.d(TAG, "[updateVersionName] : $versionName");
        VideoEditorHelper.setStringPref(
                this.getApplicationContext(),
                VERSION_NAME,
                versionName
        );
    }

    @Override
    public void onCreateCheck() {
        GLog.d(TAG, "onCreateCheck start");
        mMemoriesManager.initMemoriesSetData(getIntent());
        if (!MemoriesManager.isSetIdValid()) {
            GLog.w(TAG, "onCreateCheck sSetId is invalid, finish");
            mHandler.sendEmptyMessage(MSG_EXIT);
            return;
        }
        boolean success = initView();
        if (!success) {
            GLog.w(TAG, "onCreateCheck initView failed, finish");
            mHandler.sendEmptyMessage(MSG_EXIT);
            return;
        }
        initProgressView();
        mHandler.sendEmptyMessage(MSG_START_LOADING);
        GLog.d(TAG, "onCreateCheck end");
    }

    private boolean initView() {
        GLog.d(TAG, "initView initVideoEdit");
        mGalleryEditorVideoView = findViewById(R.id.engine_view);
        boolean success = mGalleryEngineManager.initEngine(null,
                new VideoSpec(
                        ENGINE_VIEW_WIDTH,
                        ENGINE_VIEW_HEIGHT,
                        ENGINE_VIEW_FPS
                ), false
        );
        if ((!success) || (!mGalleryEngineManager.createLiveWindow(mGalleryEditorVideoView, false))) {
            GLog.w(TAG, "initView initVideoEdit failed");
            return false;
        }

        mEditModeBgColor = getColor(com.oplus.gallery.basebiz.R.color.videoeditor_video_editor_background_color_edit);
        mMemoriesView = findViewById(R.id.main_view);
        mMemoriesView.dispatchNaviHeightChanged(bottomNaviBarHeight(true));
        mMemoriesView.setOnClickListener(view -> {
            if (mMemoriesView.isNormalState()) {
                toggleBars();
            }
        });
        mMemoriesView.setClickable(false);
        mActionBar = findViewById(R.id.action_bar);
        mBottomAreaView = findViewById(R.id.bottom_area_view);
        mSendView = findViewById(R.id.top_bar_send);
        RippleHelper.setRippleBackgroundBorderless(mSendView);
        mSendView.setOnClickListener(this);
        mEditView = findViewById(R.id.top_bar_edit);
        RippleHelper.setRippleBackgroundBorderless(mEditView);
        mEditView.setOnClickListener(this);
        mControlBar = findViewById(R.id.control_bar);
        mControlBar.setEngineManager(mGalleryEngineManager);
        mControlBar.setZoomWindowManager(getZoomWindowManager());
        mControlBar.setCancelDoneClickListener(this);
        /**
         * 在进入回忆-编辑时需要通过uiConfig计算宽度，来判断是否需要先相关动画
         * 需要初始化uiConfig
         */
        initAppUiConfig();
        mBackView = findViewById(R.id.top_bar_back);
        RippleHelper.setRippleBackgroundBorderless(mBackView);
        mBackView.setOnClickListener(view -> {
            GLog.d(TAG, "Back.onClick");
            //statistic go back by btn
            finish();
        });
        mTimeSeekBar = findViewById(R.id.time_seek_bar);
        return true;
    }

    /**
     * 初始化uiConfig
     */
    private void initAppUiConfig() {
        final AppUiResponder.AppUiConfig uiConfig = getCurrentAppUiConfig();
        GLog.d(TAG, "initAppUiConfig uiConfig = " + uiConfig);
        if (mControlBar != null) {
            mControlBar.updateAppUiConfig(uiConfig);
        }
    }

    private void initProgressView() {
        mPlayBtn = findViewById(R.id.btn_play);
        RippleHelper.setRippleBackgroundBorderless(mPlayBtn);
        mPlayBtn.setOnClickListener(view -> {
            GLog.d(TAG, "PlayBtn.onClick isPlaying = " + mGalleryEngineManager.isPlaying());
            if (mGalleryEngineManager.isPlaying()) {
                mGalleryEngineManager.pause();
                //statistic stop memories
            } else {
                mGalleryEngineManager.resume();
                //statistic play memories
            }
            updatePlayButton();
        });
        mCurTimeView = findViewById(R.id.current_time_text);
        mTotalTimeView = findViewById(R.id.total_time_text);
        mSeekBar = findViewById(R.id.seek_bar);
        mCurTimeView.setText(VideoEditorHelper.formatTimeWithMillis(this, 0));
        mTotalTimeView.setText(VideoEditorHelper.formatTimeWithMillis(this, mGalleryEngineManager.getTotalTime()));
        mSeekBar.setHapticFeedbackEnabled(false);
        mSeekBar.setMax((int) mGalleryEngineManager.getTotalTime());
        mSeekBar.setOnSeekBarChangeListener(new COUISeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(COUISeekBar seekBar, int i, boolean change) {
                if (change) {
                    mCurTimeView.setText(VideoEditorHelper.formatTimeWithMillis(MemoriesActivity.this, i));
                    mGalleryEngineManager.seekTo(i);
                }
            }

            @Override
            public void onStartTrackingTouch(COUISeekBar seekBar) {
                mSeeking = true;
                mCurTimeView.setText(VideoEditorHelper.formatTimeWithMillis(MemoriesActivity.this, seekBar.getProgress()));
                mGalleryEngineManager.seekTo(seekBar.getProgress());
                //statistic seek bar
            }

            @Override
            public void onStopTrackingTouch(COUISeekBar seekBar) {
                mSeeking = false;
                if (hasWindowFocus()) {
                    mGalleryEngineManager.resume();
                }
            }
        });
        setSeekBarAccessibility();
    }

    public void initEngineByThread() {
        Runnable runnable = () -> {
            synchronized (mEngineLock) {
                initEngine(true);
            }
        };
        mMemoriesManager.enqueueWorkTask(runnable);
    }

    public void initEngine(final boolean firstLoad) {
        GLog.d(TAG, "initEngine ");
        if (isFinishing() || isDestroyed()) {
            GLog.w(TAG, "initEngine is finishing or destroyed. isFinishing = " + isFinishing());
            mHandler.sendEmptyMessage(MSG_EXIT);
            return;
        }
        if ((mMemoriesManager == null) || (mGalleryEngineManager == null)) {
            GLog.w(TAG, "initEngine mMemoriesManager or mGalleryEngineManager is null, finish");
            mHandler.sendEmptyMessage(MSG_EXIT);
            return;
        }
        long time = System.currentTimeMillis();
        // add video clips
        boolean result = mGalleryEngineManager.initThemeVideoClips((ArrayList<MediaInfo>) MemoriesManager.getAllMediaInfoList());
        if (!result) {
            GLog.w(TAG, "initEngine.initThemeVideoClips failed. MSG_EXIT");
            mHandler.sendEmptyMessage(MSG_EXIT);
            return;
        }

        ThemeItem theme = mMemoriesManager.getCurrentTheme();
        if (theme == null) {
            GLog.w(TAG, "initEngine.addTheme theme is null.");
            return;
        }
        result = mGalleryEngineManager.addTheme(theme.convertToThemeInfo());
        GLog.d(TAG, "initEngine curTheme = " + theme + ", result = " + result);
        if (!result) {
            GLog.w(TAG, "initEngine.addTheme failed. MSG_EXIT");
            mHandler.sendEmptyMessage(MSG_EXIT);
            return;
        }

        MediaInfo curCover = MemoriesManager.getCurrentCoverId();
        mGalleryEngineManager.addThemeVideoCover(curCover);
        GLog.d(TAG, "initEngine cover = " + curCover);

        mGalleryEngineManager.addThemeCaption(MemoriesManager.getTitleText(), MemoriesManager.getTimeText());
        String music = MemoriesManager.getCurrentMusicId();
        if (TextUtils.equals(music, "none")) {
            mGalleryEngineManager.setThemeMusicMute(true);
        } else {
            mGalleryEngineManager.addThemeMusic(music);
        }
        GLog.d(TAG, "initEngine music = " + music);
        if (firstLoad) {
            // play from start
            mGalleryEngineManager.play();
            mHandler.sendEmptyMessageDelayed(MSG_LOAD_FINISH, LOAD_FINISH_DELAY);
        } else {
            mGalleryEngineManager.reset();
        }
        GLog.d(TAG, "initEngine time = " + (System.currentTimeMillis() - time));
    }

    private void setSeekBarAccessibility() {
        mSeekBar.setAccessibilityDelegate(new View.AccessibilityDelegate() {
            @Override
            public void sendAccessibilityEvent(View host, int eventType) {
                GLog.d(TAG, "sendAccessibilityEvent eventType = " + eventType);
                String durationStr = null;
                if (mGalleryEngineManager != null) {
                    long curTime = mGalleryEngineManager.getCurrentTime() / SECONDS_1000;
                    long totalTime = mGalleryEngineManager.getTotalTime() / SECONDS_1000;
                    durationStr = getResources().getString(R.string.videoeditor_talkback_seek_bar_duration,
                            curTime / MINUTES_60, curTime % MINUTES_60,
                            totalTime / MINUTES_60, totalTime % MINUTES_60);
                    GLog.d(TAG, "sendAccessibilityEvent durationStr = " + durationStr);
                }
                if (eventType == AccessibilityEvent.TYPE_VIEW_SELECTED) {
                    if (!TextUtils.isEmpty(durationStr)) {
                        mSeekBar.setContentDescription(durationStr);
                    }
                } else if ((eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED)
                        && !TextUtils.isEmpty(durationStr)) {
                    mSeekBar.setContentDescription(durationStr);
                }
                super.sendAccessibilityEvent(host, eventType);
            }
        });
    }

    private void updateBackTitle() {
        TextView backTitle = findViewById(R.id.top_bar_title);
        backTitle.setText(MemoriesManager.getTitleText());
    }

    private void updatePlayButton() {
        if ((mGalleryEngineManager != null) && (mPlayBtn != null)) {
            if (mSeeking) {
                return;
            }
            if (mGalleryEngineManager.isPlaying()) {
                // play video
                mPlayBtn.setImageResource(R.drawable.videoeditor_ic_memory_seekbar_pause);
                mPlayBtn.setContentDescription(getResources().getString(R.string.videoeditor_talkback_play));
                getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            } else {
                // paused or stop video
                mPlayBtn.setImageResource(R.drawable.videoeditor_ic_memory_seekbar_play);
                mPlayBtn.setContentDescription(getResources().getString(R.string.videoeditor_talkback_stop));
                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            }
        }
    }

    private void updateTimeAndSeekBar() {
        if (isFinishing() || isDestroyed()) {
            GLog.d(TAG, "updateTimeAndSeekBar is finishing or destroyed. isFinishing = " + isFinishing());
            return;
        }
        long curTime = mGalleryEngineManager.getCurrentTime();
        long totalTime = mGalleryEngineManager.getTotalTime();
        if (mCurTimeView != null) {
            mCurTimeView.setText(VideoEditorHelper.formatTimeWithMillis(this, curTime));
        }
        if (mTotalTimeView != null) {
            mTotalTimeView.setText(VideoEditorHelper.formatTimeWithMillis(this, totalTime));
        }
        if (mSeekBar != null) {
            mSeekBar.setProgress((int) curTime);
            mSeekBar.setMax((int) totalTime);
        }
    }

    private void takeCoverResult(Intent data) {
        if (data != null) {
            String coverUri = data.getStringExtra(MemoriesUtils.KEY_MEMORIES_CURRENT_COVER);
            MediaInfo curCover = MemoriesManager.getCurMemoriesInfo().mCover;
            MediaInfo newCover = MemoriesManager.getMediaInfoFromCurrent(coverUri);
            GLog.d(TAG, "takeCoverResult coverUri = " + coverUri + ", curCover = " + curCover + ", newCover = " + newCover);

            if (newCover != null) {
                if (curCover != null) {
                    MediaInfo oldCover = MemoriesManager.getMediaInfoFromCurrent(curCover.mPath);
                    if (newCover.equals(oldCover)) {
                        GLog.d(TAG, "takeCoverResult cover not change. oldCover = " + oldCover);
                        return;
                    }
                    if (oldCover != null) {
                        oldCover.mIsCover = false;
                    }
                    GLog.d(TAG, "takeCoverResult oldCover = " + oldCover);
                }
                if (mGalleryEngineManager.addThemeVideoCover(newCover)) {
                    newCover.mInVideo = true;
                    newCover.mIsCover = true;
                    GLog.d(TAG, "takeCoverResult newCover = " + newCover);
                    MemoriesManager.getCurMemoriesInfo().mCover = newCover;
                    mControlBar.setActionDoneEnable(!MemoriesManager.isMemoriesSame());
                    return;
                }
            }
        }
        ToastUtil.showShortToast(R.string.videoeditor_memories_toast_change_cover_failed);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        if (mMemoriesView != null) {
            mMemoriesView.setBackground(null);
            mMemoriesView.setVisibility(View.GONE);
        }
        setWindowBackground(Color.TRANSPARENT);
        this.overridePendingTransition(0, 0);
        GLog.d(TAG, "finish");
    }

    @Override
    public void onClick(View view) {
        if (DoubleClickUtils.isFastDoubleClick()) {
            GLog.w(TAG, "onClick() isFastDoubleClick");
            return;
        }
        GLog.d(TAG, "onClick getId = " + view.getId());
        // memories play mode
        int id = view.getId();
        if (id == R.id.top_bar_send) {
            GLog.d(TAG, "onClick top_bar_send");
            if (!isSupportUserCustomGalleryShare()) {
                ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.base_feature_is_disabled_tip);
                return;
            }
            if (!mMemoriesManager.isVideoSaving()) {
                mHandler.removeMessages(MSG_SEND_DATA);
                if (mGalleryEngineManager.isPlaying()) {
                    mGalleryEngineManager.pause();
                }
                mMemoriesManager.createVideoFileForSend();
                //statistic menu send
            }
        } else if (id == R.id.top_bar_edit) {
            GLog.d(TAG, "onClick top_bar_edit");
            mGalleryEngineManager.reset();
            updateTimeAndSeekBar();
            mHandler.removeMessages(MSG_SEND_DATA);
            mHandler.sendEmptyMessage(MSG_EDIT_MODE);
            //statistic menu edit
        }
    }

    private boolean isSupportUserCustomGalleryShare() {
        IConfigAbility configAbility = ((GalleryApplication) getApplicationContext()).getAppAbility(IConfigAbility.class);
        if (configAbility != null) {
            Boolean isSupportUserCustomGalleryShare = configAbility.getBooleanConfig(FEATURE_IS_SUPPORT_USER_CUSTOM_GALLERY_SHARE, false);
            IOUtils.closeQuietly(configAbility);
            if (isSupportUserCustomGalleryShare != null) {
                return isSupportUserCustomGalleryShare;
            }
        }
        return false;
    }

    private void changeButtonState(boolean isClickable) {
        mBackView.setEnabled(isClickable);
        mSendView.setEnabled(isClickable);
        mEditView.setEnabled(isClickable);
    }

    private void gotoEditMode() {
        // go to edit mode
        if ((mMemoriesView != null) && mMemoriesView.isNormalState()) {
            mControlBar.enterEditor(mGalleryEngineManager);
            //Enter edit status, return and share button is not clickable
            changeButtonState(false);
            updateBackgroundColor(true);
            hideStatusBar();
            // scale view
            mMemoriesView.changeState(MemoriesView.State.EDIT, true, 0);
            mControlBar.setActionDoneEnable(false);
            updateTimeAndSeekBar();
            if (mFirstGotoEditor) {
                mMemoriesManager.loadOptimalThumbnail();
                mFirstGotoEditor = false;
            }
        }
    }

    private boolean canShowCancelDialog() {
        if ((mMemoriesView != null) && !mMemoriesView.isNormalState() && mControlBar.isPreviewState()) {
            boolean isChange = !MemoriesManager.isMemoriesSame();
            GLog.d(TAG, LogFlag.DL, () -> "canShowCancelDialog isChange = " + isChange);
            return isChange;
        }
        return false;
    }

    private boolean gotoPlayMode(boolean done) {
        GLog.d(TAG, "onUpPressed done = " + done);
        // go back to play
        if ((mMemoriesView != null) && !mMemoriesView.isNormalState()) {
            // is on editor state
            if (mControlBar.onBackPressed()) {
                return true;
            }
            if (!done) {
                // onCancelClick or back to normal mode, must reset video
                MemoriesManager.setTitleText(mGalleryEngineManager.getThemeTitleText());
                boolean isChange = !MemoriesManager.isMemoriesSame();
                GLog.d(TAG, "onBackPressed isChange = " + isChange);
                if (isChange) {
                    MemoriesManager.resetCurrentMemoriesInfo();
                    mGalleryEngineManager.reset();
                    initEngine(false);
                    if (mIsGiveUpChange) {
                        mMemoriesManager.updateMemoriesData();
                        mIsGiveUpChange = false;
                    }
                } else if (MemoriesManager.hasEditCover()) {
                    mMemoriesManager.updateMemoriesData();
                }
            }
            mGalleryEngineManager.reset();
            //Go to normal, return and share button can be clicked
            changeButtonState(true);
            updateBackgroundColor(false);
            updateSystemBar(true);
            mMemoriesView.changeState(MemoriesView.State.NORMAL, true, 0);
            return true;
        }

        return false;
    }

    private void updateBackgroundColor(boolean isEditState) {
        int playerBgColor = mPlayModeBgColor;
        if (isEditState) {
            playerBgColor = mEditModeBgColor;
        }
        mGalleryEngineManager.setVideoBackgroundColor(Color.valueOf(playerBgColor));
        mMemoriesView.setBackgroundColor(playerBgColor);
    }

    @Override
    public void onAppUiStateChanged(@NotNull AppUiResponder.AppUiConfig uiConfig) {
        super.onAppUiStateChanged(uiConfig);
        if (!uiConfig.isChanged()) {
            return;
        }
        if (mMemoriesView != null) {
            if (mMemoriesView.isNormalState()) {
                updateSystemBar(mShowBars);
            }
            mMemoriesView.updateAppUiConfig(uiConfig);
        }
        /**
         * 接入新版美摄sdk NvsLiveWindow继承关系发生变化,
         * 窗口方向发生变化,编辑预览非播放场景图像也需要seek刷新
         */
        if ((uiConfig.getOrientation().isChanged()) && (mGalleryEngineManager != null) && (!mGalleryEngineManager.isPlaying())) {
            mGalleryEngineManager.pause();
            Message msg = mHandler.obtainMessage();
            msg.what = MSG_REFRESH_WINDOW;
            msg.arg1 = REFRESH_NEED_SEEK;
            mHandler.removeMessages(MSG_REFRESH_WINDOW);
            mHandler.sendMessageDelayed(msg, REFRESH_WINDOW_DELAY);
        }
    }

    @Override
    public void onSystemBarChanged(@NotNull WindowInsetsCompat windowInsets) {
        if (mMemoriesView != null) {
            mMemoriesView.dispatchNaviHeightChanged(bottomNaviBarHeight(false));
            if (mMemoriesView.isNormalState()) {
                boolean isLandscape = getCurrentAppUiConfig().getOrientation().getCurrent() == Configuration.ORIENTATION_LANDSCAPE;
                mMemoriesView.updateActionBarView(isLandscape);
                mMemoriesView.updateTimeSeekBarPaddingWhenAppUiConfigChanged();
            }
        }
    }

    @Override
    public void onBackPressed() {
        GLog.d(TAG, "onBackPressed isNormalState = " + ((mMemoriesView != null) ? mMemoriesView.isNormalState() : null));
        if (canShowCancelDialog()) {
            showCancelDialog(null);
            return;
        }
        if (gotoPlayMode(false)) {
            return;
        }
        if (mControlBar != null) {
            mControlBar.exitEditor();
        }
        super.onBackPressed();
    }

    @Override
    public void onCancelClick(View view) {
        GLog.d(TAG, "onCancelClick");
        if (canShowCancelDialog()) {
            showCancelDialog(view);
        } else {
            gotoPlayMode(false);
        }
        //statistic editor cancel
    }

    @Override
    public void onDoneClick() {
        GLog.d(TAG, "onDoneClick curTitle = " + mGalleryEngineManager.getThemeTitleText()
                + ", baseTitle = " + MemoriesManager.getTitleText());
        MemoriesManager.setTitleText(mGalleryEngineManager.getThemeTitleText());
        boolean isChange = !MemoriesManager.isMemoriesSame();
        GLog.d(TAG, LogFlag.DL, () -> "onDoneClick isChange = " + isChange);
        if (isChange) {
            updateMemoriesData();
        } else {
            gotoPlayMode(true);
            VideoTrackHelper.saveFinishOperation(true, null, true, -1);
        }
    }

    private void updateMemoriesData() {
        mMemoriesManager.updateMemoriesData(new UpdateMemoriesDataCallBack() {
            @Override
            public void updateStart() {
                showSaveLoadingDialog();
            }

            @Override
            public void updateSuccess() {
                updateMemoriesDataSuccess();
            }
        });
    }

    private void updateMemoriesDataSuccess() {
        runOnUiThread(() -> {
            dismissSaveLoadingDialog();
            updateBackTitle();
            gotoPlayMode(true);
            VideoTrackHelper.saveFinishOperation(true, null, true, -1);
        });
    }

    private void showSaveLoadingDialog() {
        if (isFinishing() || isDestroyed()) {
            return;
        }
        if (mSaveLoadingDialog == null) {
            LoadingDialog.Builder builder = new LoadingDialog.Builder(this, false);
            builder.setTips(R.string.videoeditor_editor_cancel_compiling);
            builder.setCancelable(false);
            mSaveLoadingDialog = builder.build().show();
        } else if (!mSaveLoadingDialog.isShowing()) {
            mSaveLoadingDialog.show();
        }
        if (mSaveLoadingDialog != null) {
            COUIThemeOverlay.getInstance().applyThemeOverlays(mSaveLoadingDialog.getContext());
        }
    }

    private void dismissSaveLoadingDialog() {
        if ((mSaveLoadingDialog != null) && (mSaveLoadingDialog.isShowing())) {
            mSaveLoadingDialog.dismiss();
        }
    }

    @Override
    public void onStartCheck() {
        GLog.d(TAG, "onStartCheck mStoped = " + mStoped);
        super.onStartCheck();
        if (mStoped) {
            mMemoriesManager.checkPhoto();
            mGalleryEngineManager.repaintFrame();
        }
        mStoped = false;
    }

    @Override
    public void onResumeCheck() {
        GLog.d(TAG, "onResumeCheck");
        super.onResumeCheck();
        mGalleryEngineManager.repaintFrame();
        mHandler.removeMessages(MSG_CLEAR_ENGINE_BG);
        mHandler.sendEmptyMessageDelayed(MSG_CLEAR_ENGINE_BG, CLEAR_ENGINE_BG_DELAY);
    }

    @Override
    public void onPauseCheck() {
        GLog.d(TAG, "onPauseCheck");
        super.onPauseCheck();
        mHandler.removeMessages(MSG_CLEAR_ENGINE_BG);
    }

    @Override
    public void onStopCheck() {
        GLog.d(TAG, "onStopCheck");
        super.onStopCheck();
        synchronized (mEngineLock) {
            if (mGalleryEngineManager.isPlaying()) {
                mGalleryEngineManager.pause();
            }
        }
        if ((mLoadingDialog != null) && mLoadingDialog.isShowing()) {
            GLog.d(TAG, "onStopCheck isShowing = true, finish activity.");
            finish();
        }
        mStoped = true;
    }

    @Override
    protected void onDestroy() {
        GLog.d(TAG, "onDestroy");
        super.onDestroy();
        mHandler.removeCallbacksAndMessages(null);
        synchronized (mEngineLock) {
            if (mGalleryEngineManager != null) {
                mGalleryEngineManager.destroy(!mIsLimited);
            }
        }
        if (mMemoriesManager != null) {
            mMemoriesManager.destroyDialog();
            mMemoriesManager.destroy(!mIsLimited);
        }
        if (mLoadingDialog != null) {
            mLoadingDialog.dismiss();
            mLoadingDialog = null;
        }
        unRegisterSDCardBroadcast();
        MeicamEngineLimiter.getInstance().unregister(this);
        ThemeAutoDownloadHelper.release();
    }

    private void toggleBars() {
        GLog.d(TAG, "toggleBars mShowBars = " + mShowBars + ", isNormalState = " + mMemoriesView.isNormalState());
        if (mShowBars) {
            if (mActionBar != null) {
                mActionBar.setVisibility(View.INVISIBLE);
            }
            if (mControlBar != null) {
                mControlBar.setVisibility(View.INVISIBLE);
            }
            if (mBottomAreaView != null) {
                mBottomAreaView.setVisibility(View.INVISIBLE);
            }
            updateSystemBar(false);
            mShowBars = false;
        } else {
            if (mActionBar != null) {
                mActionBar.setVisibility(View.VISIBLE);
            }
            if (mControlBar != null) {
                mControlBar.setVisibility(View.VISIBLE);
            }
            if (mBottomAreaView != null) {
                mBottomAreaView.setVisibility(View.VISIBLE);
            }
            updateSystemBar(true);
            mShowBars = true;
        }
    }

    /**
     * 加载完视频后,首次要更新view
     * 设置背景,设置视频宽高
     */
    private void refreshView() {
        if (mMemoriesView != null) {
            updateBackgroundColor(false);
            setWindowBackground(Color.BLACK);
        }
    }

    private void showCancelDialog(View view) {
        if (isFinishing() || isDestroyed()) {
            GLog.d(TAG, "showCancelDialog, activity is destroyed");
            return;
        }
        if ((mCancelDialog != null) && mCancelDialog.isShowing()) {
            return;
        }
        ConfirmDialog.Builder builder = new ConfirmDialog.Builder(this);
        builder.setTitle(R.string.videoeditor_editor_text_abandon_current_modify);
        builder.setNeutralButton(
                R.string.videoeditor_editor_text_abandon_amend,
                (dialog, which) -> {
                    mIsGiveUpChange = true;
                    gotoPlayMode(false);
                }
        );
        builder.setNegativeButton(
                R.string.videoeditor_cancel,
                (dialog, which) -> {
                    dialog.dismiss();
                }
        );
        mCancelDialog = builder.build().show();
    }

    private void showLoadingDialog(Context context, int titleId) {
        if (isFinishing() || isDestroyed()) {
            GLog.d(TAG, "showLoadingDialog, activity is destroyed");
            return;
        }
        if (mLoadingDialog == null) {
            LoadingDialog.Builder builder = new LoadingDialog.Builder(context, false);
            builder.setTips(titleId);
            builder.setCancelable(false);
            mLoadingDialog = builder.build().show();
            COUIThemeOverlay.getInstance().applyThemeOverlays(mLoadingDialog.getContext());
            ScreenUtils.ignoreHomeMenuKey(mLoadingDialog.getWindow());
        } else if (!mLoadingDialog.isShowing()) {
            mLoadingDialog.show();
        }
    }

    private void registerSDCardBroadcast() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_MEDIA_EJECT);
        filter.addAction(Intent.ACTION_MEDIA_UNMOUNTED);
        filter.addAction(Intent.ACTION_MEDIA_REMOVED);
        filter.addDataScheme("file");
        BroadcastDispatcher.INSTANCE.registerReceiver(this, mSDCardReceiver, filter);
    }

    private void unRegisterSDCardBroadcast() {
        try {
            BroadcastDispatcher.INSTANCE.unregisterReceiver(this, mSDCardReceiver);
        } catch (Exception e) {
            GLog.w(TAG, "unRegisterSDCardBroadcast, e:" + e);
        }
    }

    @Override
    public void dump(String prefix, FileDescriptor fd, PrintWriter writer, String[] args) {
        writer.println("----------------------- Dump MemoriesActivity: -----------------------");
        if (MemoriesManager.DUMP_DEBUG) {
            int opti = 0;
            while (opti < args.length) {
                String opt = args[opti];
                if ((opt == null) || (opt.length() <= 0) || (opt.charAt(0) != '-')) {
                    break;
                }
                opti++;
                if ("-memoriesinfo".equals(opt)) {
                    if (MemoriesManager.getBaseMemoriesInfo() != null) {
                        writer.println("Dump getBaseMemoriesInfo()");
                        MemoriesManager.getBaseMemoriesInfo().dump(writer);
                    }
                    if (MemoriesManager.getCurMemoriesInfo() != null) {
                        writer.println("Dump getCurMemoriesInfo()");
                        MemoriesManager.getCurMemoriesInfo().dump(writer);
                    }
                } else if ("-thememusicinfo".equals(opt)) {
                    writer.println("Dump dumpThemeAndMusicList");
                    MemoriesManager.dumpThemeAndMusicList(writer);
                } else if ("-engineinfo".equals(opt)) {
                    writer.println("Dump dumpEngineInfo");
                    if (mGalleryEngineManager != null) {
                        mGalleryEngineManager.dumpEngineInfo(writer);
                    }
                } else if ("-all".equals(opt)) {
                    if (MemoriesManager.getBaseMemoriesInfo() != null) {
                        writer.println("Dump getBaseMemoriesInfo()");
                        MemoriesManager.getBaseMemoriesInfo().dump(writer);
                    }
                    if (MemoriesManager.getCurMemoriesInfo() != null) {
                        writer.println("Dump getCurMemoriesInfo()");
                        MemoriesManager.getCurMemoriesInfo().dump(writer);
                    }
                    writer.println("Dump dumpThemeAndMusicList");
                    MemoriesManager.dumpThemeAndMusicList(writer);
                    writer.println("Dump dumpEngineInfo");
                    if (mGalleryEngineManager != null) {
                        mGalleryEngineManager.dumpEngineInfo(writer);
                    }
                }
            }
        }
    }

    private void handleMsgRefreshVideo(Message msg) {
        ArrayList<MediaInfo> removeList = (ArrayList<MediaInfo>) msg.obj;
        if ((removeList != null) && !removeList.isEmpty()) {
            MediaInfo cover = MemoriesManager.getCurrentCoverId();
            MediaInfo curCover = mGalleryEngineManager.getThemeVideoCover();
            if ((curCover != null) && !curCover.equals(cover)) {
                mGalleryEngineManager.addThemeVideoCover(cover);
            }
            for (MediaInfo info : removeList) {
                mGalleryEngineManager.deleteThemeVideoClip(info);
            }
            ArrayList<String> curVideoList = mGalleryEngineManager.getThemeVideoClipList();
            if ((curVideoList == null) || (curVideoList.size() < MemoriesUtils.MEMORIES_VIDEO_PHOTOS_MIN)) {
                String toast = getResources().getString(R.string.videoeditor_memories_toast_less_than_min_photos,
                        MemoriesUtils.MEMORIES_VIDEO_PHOTOS_MIN);
                ToastUtil.showShortToast(toast);
                finish();
                return;
            }
            mControlBar.setActionDoneEnable(!MemoriesManager.isMemoriesSame());

            EditorBaseState state = mControlBar.getCurrentEditor();
            if (state instanceof EditorPhotoState) {
                ((EditorPhotoState) state).updateAdapterPhoto();
            }
            mGalleryEngineManager.reset();
        }
    }

    private void handleMsgLoadFinish() {
        GLog.d(TAG, "handleMsgLoadFinish.");
        if (mLoadingDialog != null) {
            mLoadingDialog.dismiss();
            mLoadingDialog = null;
        }
        updateSystemBar(false);
        if (mMemoriesView != null) {
            mMemoriesView.setClickable(true);
        }
        MemoriesManager.setDuration(mGalleryEngineManager.getTotalTime());
    }

    private void updateSystemBar(boolean show) {
        boolean isLandscape = getCurrentAppUiConfig().getOrientation().getCurrent() == Configuration.ORIENTATION_LANDSCAPE;
        if (show) {
            if (isLandscape) {
                hideStatusBar();
            } else {
                showStatusBar();
            }
            showNaviBar();
        } else {
            hideStatusBar();
            hideNaviBar();
        }
    }

    private void handleMsgVideoReady() {
        GLog.d(TAG, "handleMsgVideoReady.");
        if (mGalleryEditorVideoView.getVisibility() != View.VISIBLE) {
            mGalleryEditorVideoView.setVisibility(View.VISIBLE);
            refreshView();
        }
        if (mLoadingDialog != null) {
            mLoadingDialog.dismiss();
            mLoadingDialog = null;
        }
    }

    @NotNull
    @Override
    public ActivitySystemBarStyle getSystemBarStyle() {
        return new ImmersiveActivitySystemBarStyle(this) {
            @Override
            public void onUpdate(@NotNull WindowInsetsCompat windowInsets) {
                setStatusBarColor(Color.TRANSPARENT);
                setNaviBarColor(Color.TRANSPARENT);
                setStatusBarAppearance(false);
            }
        };
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (mControlBar.getCurrentEditor() != null) {
            mControlBar.getCurrentEditor().onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }
}
