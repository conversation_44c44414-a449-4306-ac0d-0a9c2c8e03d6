/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DefaultInvoker.kt
 ** Description: 默认调用者
 ** Version: 1.0
 ** Date: 2025/6/6
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/23        1.0        created
 *********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.invoker

import android.view.ViewGroup
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R

/**
 * 默认调用者
 */
class DefaultInvoker(
    val activity: BaseActivity,
    val galleryVideoEngine: IVideoEditAbility
) : BaseInvoker() {
    override fun handleCreateCheckFail() {
        ToastUtil.showShortToast(R.string.videoeditor_notif_not_supported)
        activity.finish()
    }

    override fun createLiveWindow(parent: ViewGroup, shouldCrop: Boolean): Boolean {
        return galleryVideoEngine.createLiveWindow(parent, shouldCrop)
    }
}