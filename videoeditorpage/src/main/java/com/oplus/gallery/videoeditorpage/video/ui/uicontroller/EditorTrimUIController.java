/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - EditorTrimUIController.java
 ** Description: for video trim.
 ** Version: 1.0
 ** Date : 2017/11/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **  <EMAIL>    2017/11/24    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video.ui.uicontroller;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig.FORM_TRIM_PAGE;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.coui.appcompat.button.COUIButton;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.framework.abilities.config.keys.ConfigID;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.app.EditorBaseState;
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig;
import com.oplus.gallery.videoeditorpage.video.app.EditorTrimState;
import com.oplus.gallery.videoeditorpage.video.ui.GallerySlowMotionControlView;

public class EditorTrimUIController extends EditorVideoBaseUiController {
    private final static String TAG = "EditorTrimUIController";
    private final static float DEFAULT_SCALE = 1.0f;

    public EditorTrimUIController(Context context, ViewGroup rootView, EditorBaseState state,
                                  ShowAnimationEndListener listener) {
        super(context, rootView, state, listener);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_video_editor_sub_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_video_editor_trim_bottom_menu_layout;
    }

    @Override
    public int getContentLayoutId(@NonNull AppUiResponder.AppUiConfig config) {
        return isLandscapeLayout(config) ? R.layout.videoeditor_frame_trimvideo_layout_landscape : R.layout.videoeditor_frame_trimvideo_layout;
    }

    @Override
    public int getTitleId() {
        return R.string.videoeditor_editor_text_trim;
    }

    @Override
    public void adaptToolBarContainer(int layoutId,
                                      @NonNull View view,
                                      @NonNull AppUiResponder.AppUiConfig config,
                                      @NonNull BaseActivity context) {
        /**
         * 这里设计后期要求界面需要单独做特殊的缩放操作，so不需要父类逻辑
         */
    }

    @Override
    public void zoomToolBar(ViewGroup toolbarContainer, AppUiResponder.AppUiConfig config) {
        float scale = VideoEditorUIConfig.getViewScaleByWindowSize(config, FORM_TRIM_PAGE);
        int viewSize = ScreenUtils.pixelToDp(config.getWindowWidth().getCurrent());

        boolean isSupportExportImage = isSupportExportImage();
        boolean isSlowMotionMode = ((EditorTrimState) mEditorState).isSlowMotionMode();
        // 根据视频模式选择合适的toolbar高度
        int toolbarHeightResId = R.dimen.video_editor_toolbar_height;
        if (isSlowMotionMode && isSupportExportImage) {
            toolbarHeightResId = R.dimen.video_editor_trim_slow_toolbar_height;
        }
        // 根据scale调整工具栏高度
        RelativeLayout toolBarContainer = (RelativeLayout) mToolBarMenuContainer;
        int toolBarHeight = VideoEditorUIConfig.getViewSizeByScale(toolbarHeightResId, mContext, config, FORM_TRIM_PAGE);
        ViewGroup.LayoutParams layoutParams = toolBarContainer.getLayoutParams();
        layoutParams.width = MATCH_PARENT;
        layoutParams.height = toolBarHeight;

        // 根据是否支持导出照片调整播控按钮竖屏底部边距
        RelativeLayout toolBarCommonContainer = (RelativeLayout) mToolBarCommonContainer;
        RelativeLayout.LayoutParams commonParam = (RelativeLayout.LayoutParams) toolBarCommonContainer.getLayoutParams();
        if (isSupportExportImage && !isLandscapeLayout(config)) {
            commonParam.bottomMargin = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_trim_toolbar_margin_top);
        }

        adjustToolBarMenuHeight();
        // 设置工具栏内容区域宽度
        int toolBarContentSize = ScreenUtils.dpToPixel(VideoEditorUIConfig.getToolBarContentWidthForTrimSize(viewSize, scale));
        RelativeLayout toolBarContentView = toolBarContainer.findViewById(R.id.editor_trim_toolbar_menu_layout);
        if (toolBarContentView != null) {
            toolBarContentView.getLayoutParams().width = toolBarContentSize;
            //根据是否支持导出图片,调整ui布局规则
            int gravity = isSupportExportImage ? Gravity.CENTER_HORIZONTAL : Gravity.CENTER;
            toolBarContentView.setGravity(gravity);
        }

        // 根据scale，调整缩图列表及滑动条位置
        FrameLayout thumbLayout = toolBarContainer.findViewById(R.id.video_thumb_layout);
        if (thumbLayout != null) {
            thumbLayout.setScaleX(scale);
            thumbLayout.setScaleY(scale);
            int viewH = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_trim_touch_view_height);
            thumbLayout.setTranslationY(-viewH * (NO_SCALE - scale) / 2);
        }
        GallerySlowMotionControlView slowMotionControlView = null;
        if (isSlowMotionMode) {
            slowMotionControlView = toolbarContainer.findViewById(R.id.slow_motion_edit);
        }
        if ((slowMotionControlView != null)) {
            int touchViewMarginTop = VideoEditorUIConfig.getViewSizeByScale(R.dimen.video_editor_trim_bottom_menu_slow_motion_margin_top,
                    mContext, config, FORM_TRIM_PAGE);
            int thumbLayoutHeight = VideoEditorUIConfig.getViewSizeByScale(R.dimen.video_editor_trim_touch_view_height,
                    mContext, config, FORM_TRIM_PAGE);
            RelativeLayout.LayoutParams buttonParam = (RelativeLayout.LayoutParams) slowMotionControlView.getLayoutParams();
            buttonParam.setMargins(buttonParam.leftMargin, (touchViewMarginTop + thumbLayoutHeight), buttonParam.rightMargin, 0);
            slowMotionControlView.setScaleX(scale);
            slowMotionControlView.setScaleY(scale);
            int viewH = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_trim_bottom_menu_slow_motion_height);
            slowMotionControlView.setTranslationY(-viewH * (NO_SCALE - scale) / 2);
        }

        // 调整COUIButton位置
        COUIButton exportButton = toolbarContainer.findViewById(R.id.export_photo_button);
        if (isSupportExportImage) {
            exportButton.setVisibility(View.VISIBLE);
            adapterExportButtonPosition(exportButton, config, scale);
        } else {
            exportButton.setVisibility(View.GONE);
        }

        toolBarContainer.requestLayout();
    }

    /**
     * 根据Scale，调整COUIButton位置
     *
     * @param exportButton 导出按钮
     * @param config       uiconfig配置
     * @param scale        缩图轴缩放比例
     */
    private void adapterExportButtonPosition(COUIButton exportButton, AppUiResponder.AppUiConfig config, float scale) {
        boolean isSlowMotionMode = ((EditorTrimState) mEditorState).isSlowMotionMode();
        if (exportButton != null) {
            int exportButtonMarginTopResId = R.dimen.video_editor_trim_margin_top;
            if (isSlowMotionMode) {
                exportButtonMarginTopResId = R.dimen.video_editor_export_photo_button_slowmode_margin_top;
                // 缩放比例小于1时调整按钮与顶部间距
                if (scale < DEFAULT_SCALE) {
                    exportButtonMarginTopResId = R.dimen.video_editor_export_photo_button_slowmode_margin_top_land;
                }
            }
            int marginTop = VideoEditorUIConfig.getViewSizeByScale(exportButtonMarginTopResId, mContext, config, FORM_TRIM_PAGE);
            RelativeLayout.LayoutParams buttonParam = (RelativeLayout.LayoutParams) exportButton.getLayoutParams();
            buttonParam.setMargins(0, marginTop, 0, 0);
        }
    }

    /**
     * 调整toolBarMenu的高度,父类中会根据此高度来设置预览图区域距离底部的间距
     * 在父类使用时会对横竖屏做判断，这里直接赋值即可，无需在判断横竖屏
     */
    private void adjustToolBarMenuHeight() {
        boolean isSupportExportImage = isSupportExportImage();
        boolean isSlowMotionMode = ((EditorTrimState) mEditorState).isSlowMotionMode();
        if (!isSupportExportImage) {
            return;
        }
        mToolBarMenuHeight = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_toolbar_height)
                + mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_trim_toolbar_margin_top);
        if (isSlowMotionMode) {
            mToolBarMenuHeight = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_trim_slow_toolbar_height)
                    + mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_trim_toolbar_margin_top);
            mToolBarMenuHeightLandscape = mContext.getResources().getDimensionPixelSize(R.dimen.video_editor_trim_slow_toolbar_height_landscape);
        }
    }

    /**
     * 是否支持导出图片
     *
     * @return true 支持， false 不支持
     */
    private boolean isSupportExportImage() {
        return ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE_EXTRACT_UHDR_FRAME);
    }


    @Override
    public void adaptPreviewArea(int layoutId, @NonNull View view, @NonNull AppUiResponder.AppUiConfig config, @NonNull BaseActivity context) {
        adaptPreviewArea(layoutId, view, config, context, false);
    }

    @Override
    public boolean isLandscapeLayout(AppUiResponder.AppUiConfig config) {
        return ScreenUtils.pixelToDp(config.getWindowHeight().getCurrent()) <= VideoEditorUIConfig.WINDOW_HEIGHT_540;
    }
}
