/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - EditorSpeederUIController.java
 * Description:
 * Version: 1.0
 * Date: 2021/3/27
 * Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <EMAIL>   2021/3/27          1.0         OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.videoeditorpage.video.ui.uicontroller;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.widget.LinearLayout.HORIZONTAL;
import static android.widget.LinearLayout.VERTICAL;

import android.content.Context;
import android.content.res.Resources;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.oplus.gallery.business_lib.template.editor.widget.VerticalWrapperLayout;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.app.EditorBaseState;
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig;

public class EditorSpeederUIController extends EditorVideoBaseUiController {
    private final static String TAG = "EditorSpeederUIController";
    private VerticalWrapperLayout mSectionSeekbarWrapper;
    private LinearLayout mSectionSeekBarTextWrapper;
    private RelativeLayout mSpeederMenuLayout;

    public EditorSpeederUIController(Context context, ViewGroup rootView, EditorBaseState state,
                                     ShowAnimationEndListener listener) {
        super(context, rootView, state, listener);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_video_editor_sub_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_video_editor_speeder_bottom_menu_layout;
    }

    @Override
    public int getTitleId() {
        return R.string.videoeditor_editor_text_speeder;
    }

    @Override
    public void onShow() {
        mSpeederMenuLayout = mContainer.findViewById(R.id.speeder_menu_layout);
        mSectionSeekBarTextWrapper = mContainer.findViewById(R.id.section_seek_bar_text_wrapper);
        mSectionSeekbarWrapper = mContainer.findViewById(R.id.section_seekbar_wrapper);
        super.onShow();
    }

    @Override
    public void adaptToolBarContainer(int layoutId, @NonNull View view, @NonNull AppUiResponder.AppUiConfig config, @NonNull BaseActivity context) {
        super.adaptToolBarContainer(layoutId, view, config, context);
        boolean isLandscape = isLandscapeLayout(config);
        adaptSectionSeekBarHintText(isLandscape, context);
        adaptSectionSeekBar(isLandscape, context);
        adaptMenuLayout(isLandscape, context, config);
    }

    private void adaptSectionSeekBarHintText(boolean isLandscape, Context context) {
        if (mSectionSeekBarTextWrapper != null) {
            int startViewId = mSectionSeekBarTextWrapper.getChildAt(0).getId();
            if ((isLandscape && (startViewId == R.id.text_very_slow))
                    || (!isLandscape && (startViewId == R.id.text_very_fast))) {
                //倒叙处理,匹配seekBar 90度旋转的
                for (int i = (mSectionSeekBarTextWrapper.getChildCount() - 1); i > 0; i--) {
                    View child = mSectionSeekBarTextWrapper.getChildAt(0);
                    mSectionSeekBarTextWrapper.removeView(child);
                    mSectionSeekBarTextWrapper.addView(child, i);
                }
            }
            Resources resources = context.getResources();
            int width = MATCH_PARENT;
            int height = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_text_width);
            int orientation = HORIZONTAL;
            int verb = RelativeLayout.ABOVE;
            if (isLandscape) {
                width = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_text_width);
                height = MATCH_PARENT;
                orientation = VERTICAL;
                verb = RelativeLayout.LEFT_OF;
            }
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width, height);
            layoutParams.addRule(verb, R.id.section_seekbar_wrapper);
            mSectionSeekBarTextWrapper.setOrientation(orientation);
            mSectionSeekBarTextWrapper.setLayoutParams(layoutParams);
        }
    }

    private void adaptSectionSeekBar(boolean isLandscape, Context context) {
        if (mSectionSeekbarWrapper != null) {
            Resources resources = context.getResources();
            int width = MATCH_PARENT;
            int height = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_seek_bar_height);
            int verb = RelativeLayout.ALIGN_PARENT_BOTTOM;
            if (isLandscape) {
                width = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_seek_bar_height);
                height = MATCH_PARENT;
                verb = RelativeLayout.ALIGN_PARENT_RIGHT;
            }
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width, height);
            layoutParams.addRule(verb);
            mSectionSeekbarWrapper.setOrientation(isLandscape ? VERTICAL : HORIZONTAL);
            mSectionSeekbarWrapper.setLayoutParams(layoutParams);
        }
    }

    private void adaptMenuLayout(boolean isLandscape, BaseActivity context, AppUiResponder.AppUiConfig config) {
        if (mSpeederMenuLayout != null) {
            mSpeederMenuLayout.post(() -> {
                if (context.isFinishing() || context.isDestroyed()) {
                    GLog.d(TAG, "adaptMenuLayout, UI controller has been destroyed, cancel perform post runnable");
                    return;
                }
                int viewSize = config.getWindowWidth().getCurrent();
                int titleBarHeight = mBottomActionBarContainer.getLayoutParams().height;
                Resources resources = context.getResources();
                int margin = 0;
                if (isLandscape) {
                    viewSize = config.getWindowHeight().getCurrent() - titleBarHeight - ((BaseActivity) mContext).bottomNaviBarHeight(false);
                    margin = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_text_padding_vertical);
                } else {
                    margin = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_text_padding_horizontal);
                }
                viewSize = ScreenUtils.pixelToDp(viewSize);
                int toolBarContentSize = ScreenUtils.dpToPixel(VideoEditorUIConfig.getToolBarContentSize(viewSize, isLandscape));
                /* 由于公共控件 COUISectionSeekBar 限制了最大宽度480dp。所以当layout超出480dp后，倍速文字就无法和 COUISectionSeekBar 对齐了。
                 * 这里需要同步限制layout最大尺寸为480dp */
                int toolBarContentSizeLimit = context.getResources().getDimensionPixelOffset(R.dimen.video_editor_speeder_menu_layout_max_side);
                int width = Math.min(toolBarContentSize, toolBarContentSizeLimit);
                int height = context.getResources().getDimensionPixelOffset(R.dimen.video_editor_speeder_content_height);
                int marginLeft = margin;
                int marginTop = 0;
                int marginRight = margin;
                int marginBottom = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_content_margin);
                if (isLandscape) {
                    width = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_text_width)
                            + resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_seek_bar_height);
                    height = Math.min(toolBarContentSize, toolBarContentSizeLimit);
                    marginLeft = 0;
                    marginTop = margin;
                    marginRight = resources.getDimensionPixelOffset(R.dimen.video_editor_speeder_content_margin_right_landscape);
                    marginBottom = margin;
                }
                RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width, height);
                layoutParams.setMargins(marginLeft, marginTop, marginRight, marginBottom);
                mSpeederMenuLayout.setLayoutParams(layoutParams);
            });
        }
    }
}
