/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ExportOliveInvoker.kt
 ** Description: 导出实况业务调用者
 ** Version: 1.0
 ** Date: 2025/6/6
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/23        1.0        created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.invoker

import android.os.Bundle
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.EXPORT_OLIVE_FRAGMENT
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoEngineManager
import com.oplus.gallery.videoeditorpage.video.VideoEditorActivity
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorExportOliveViewModel

/**
 * 导出实况业务调用者
 */
class ExportOliveInvoker(
    val activity: VideoEditorActivity,
    val galleryVideoEngine: IVideoEditAbility
) : BaseInvoker() {
    /**
     * 导出实况ViewModel
     */
    private var exportOliveViewModel: EditorExportOliveViewModel = ViewModelProvider(activity)[EditorExportOliveViewModel::class.java]

    init {
        exportOliveViewModel.videoEngineManager = galleryVideoEngine
        subscribeFromViewModel()
    }

    /**
     * 订阅ViewModel中LiveData
     */
    private fun subscribeFromViewModel() {
        // 针对导出实况页面视频编辑进行提亮管理
        exportOliveViewModel.editorVideoBright.observe(activity) { surfaceView ->
            activity.setVideoBrightenManager(surfaceView)
        }
    }

    override fun showCustomPage(resId: Int, data: Bundle?) {
        activity.startFragment(resId, EXPORT_OLIVE_FRAGMENT, data, true)
    }

    override fun onInitEngineFinish() {
        exportOliveViewModel.notifyInitEngineFinish(true)
    }

    override fun onPlayStatus(status: Int) {
        if (status == GalleryVideoEngineManager.PLAY_STATUS_FINISH) {
            exportOliveViewModel.onPlayFinish()
        }
    }

    override fun createLiveWindow(parent: ViewGroup, shouldCrop: Boolean): Boolean {
        return true
    }

    companion object {
        /**
         * 从相册-大图-更多-导出实况
         */
        const val VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE = "gallery_export_olive"
    }
}