/********************************************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:  - MeicamVideoEngine.kt
 * Description: manager the whole video, audio, theme, overlay item, filter.
 * Version: 1.0
 * Date : 2017/11/11
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
 * <EMAIL>    2017/11/11    1.0    build this module
 ********************************************************************************/
package com.oplus.gallery.videoeditorpage.engine.meicam

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.ColorSpace
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import android.util.Rational
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import com.meicam.sdk.NvsLiveWindow
import com.meicam.sdk.NvsRational
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsStreamingContext.COMPILE_DELAY_TIME_IN_MS_PER_FRMAE
import com.meicam.sdk.NvsStreamingContext.COMPILE_VIDEO_COLOR_PRIMARIES
import com.meicam.sdk.NvsStreamingContext.COMPILE_VIDEO_HDR_COLOR_TRANSFER
import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsVideoResolution
import com.meicam.sdk.NvsVideoStreamInfo
import com.oplus.gallery.abilities.transform.channel.livephoto.OliveHdrVideoFx
import com.oplus.gallery.abilities.transform.channel.livephoto.TransformConfig
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper.getBoolean
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.DOLBY_VIDEO_TYPE
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.HDR10PLUS_VIDEO_TYPE
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.HDR10_VIDEO_TYPE
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.HLG_VIDEO_TYPE
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser.VideoType.SDR_VIDEO_TYPE
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA
import com.oplus.gallery.foundation.codec.extend.ExtendDataUtils
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct
import com.oplus.gallery.foundation.codec.extend.VideoTransformStruct
import com.oplus.gallery.foundation.uikit.lifecycle.ActivityLifecycle
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants.Permission.PERMISSION_COMPONENT_SAFE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GLog.d
import com.oplus.gallery.foundation.util.debug.GLog.w
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_HLG
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_PQ
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.getMeicamColorSpaceName
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.isHdrGamma
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils.chooseByPackageIfExist
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USE_OPERATE_RATE
import com.oplus.gallery.framework.abilities.videoedit.OnCustomCompileTaskStatusListener
import com.oplus.gallery.framework.abilities.videoedit.data.SaveVideoInfo
import com.oplus.gallery.framework.abilities.videoedit.data.VideoRatio
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter.Companion.FPS_120_MIN_AVERAGE_FPS_THRESHOLD
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.videoeditor.engine.meicam.MeicamContext
import com.oplus.gallery.videoeditor.engine.meicam.MeicamContext.NvsContextFlagBuilder
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper
import com.oplus.gallery.videoeditorpage.engine.GalleryEditorVideoView
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoEngineListener
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoEngineManager
import com.oplus.gallery.videoeditorpage.engine.IGalleryAudioClip
import com.oplus.gallery.videoeditorpage.engine.IGalleryThemeHelper
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoClip
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoCutRotate
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoEngine
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoFilter
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoFx
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoSticker
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoSubTitle
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoTemplate
import com.oplus.gallery.videoeditorpage.engine.IGalleryVideoThumbnail
import com.oplus.gallery.videoeditorpage.engine.meicam.SdkCallbackInterface.SDKCallback
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import java.io.PrintWriter
import java.util.Hashtable
import java.util.Locale
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.math.min

/**
 * 基于美摄sdk封装的视频操作类
 * @param context: 上下文
 * @param listener: 相册视频一级编辑页监听器
 */
class MeicamVideoEngine(context: Context, listener: GalleryVideoEngineListener) : IGalleryVideoEngine {
    private val meicamAudioClip: MeicamAudioClip
    private val meicamVideoClip: MeicamVideoClip
    private val meicamThemeHelper: MeicamThemeHelper
    private val meicamVideoSubTitle: MeicamVideoSubTitle
    private val meicamVideoFilter: MeicamVideoFilter
    private val meicamVideoTheme: MeicamVideoTheme
    private val meicamVideoTemplate: MeicamVideoTemplate
    private val meicamVideoSticker: MeicamVideoSticker
    private val meicamVideoFx: MeicamVideoFx
    private val meicamVideoThumbnail: MeicamVideoThumbnail
    private val meicamVideoCutRotate: MeicamVideoCutRotate

    private var galleryVideoEngineListener: GalleryVideoEngineListener? = null
    private var onCustomCompileTaskStatusListener: OnCustomCompileTaskStatusListener? = null
    private var sdkCallbackInterface: SdkCallbackInterface? = null

    private var meicamContext: MeicamContext?
    private var nvsRational: NvsRational? = null
    private var liveWindow: NvsLiveWindow? = null
    private var timeline: NvsTimeline? = null
    private var meicamTimeline: MeicamTimeline? = null
    private val context: Context
    private var frameDurationMs = 0
    private var resolutionWidth = 0
    private var resolutionHeight = 0

    private var videoCodecType: Int = SDR_VIDEO_TYPE

    private var lastGrabCoroutine: Continuation<*>? = null

    /**
     * 表示正在后台执行save video操作, 会控制其他逻辑的分发
     */
    private var customCompileTaskRunning = false

    /**
     * 是否在compile时，使用了自有的下变换
     */
    private var hanUseCustomFx = false

    /**
     * 是否编译回调失败
     */
    private var isCompileFailed = false

    /**
     * 添加进美摄timeline的特效索引 （如下变换的 fx）
     */
    private var fxIndex = -1

    /**
     * 是否支持广色域
     */
    private val isWideColorGamut by lazy {
        ActivityLifecycle.getActivityList().lastOrNull()?.get()?.window?.isWideColorGamut ?: false
    }

    /**
     * callback
     */
    private val sdkCallback: SDKCallback = object : SDKCallback {
        /**
         * NvsStreamingContext.CompileCallback
         */
        override fun onCompileProgress(nvsTimeline: NvsTimeline, progress: Int) {
            if (timeline !== nvsTimeline) {
                return
            }
            if ((galleryVideoEngineListener != null) && !customCompileTaskRunning) {
                galleryVideoEngineListener?.onExportProgressChange(progress)
            }
            if (onCustomCompileTaskStatusListener != null) {
                onCustomCompileTaskStatusListener?.onVideoSaveProcess(progress)
            }
        }

        override fun onCompileFinished(nvsTimeline: NvsTimeline) {
            if (timeline !== nvsTimeline) {
                return
            }
            if ((galleryVideoEngineListener != null) && !customCompileTaskRunning) {
                galleryVideoEngineListener?.onExportStatusChange(GalleryVideoEngineManager.EXPORT_STATUS_FINISH)
            }
        }

        override fun onCompileFailed(nvsTimeline: NvsTimeline) {
            if (timeline !== nvsTimeline) {
                return
            }
            if ((galleryVideoEngineListener != null) && !customCompileTaskRunning) {
                galleryVideoEngineListener?.onExportStatusChange(GalleryVideoEngineManager.EXPORT_STATUS_ERROR)
            }
            isCompileFailed = true
        }

        /**
         * NvsStreamingContext.CompileCallback2
         */
        override fun onCompileCompleted(nvsTimeline: NvsTimeline, isCanceled: Boolean) {
            if (timeline !== nvsTimeline) {
                return
            }
            GLog.d(
                TAG, LogFlag.DL, "customCompileTaskRunning:$customCompileTaskRunning, isCompileFailed:$isCompileFailed," +
                        " isCanceled:$isCanceled, onCustomCompileTaskStatusListener:$onCustomCompileTaskStatusListener"
            )
            if ((galleryVideoEngineListener != null) && !customCompileTaskRunning) {
                galleryVideoEngineListener?.onExportStatusChange(
                    if (isCanceled) GalleryVideoEngineManager.EXPORT_STATUS_CANCEL else GalleryVideoEngineManager.EXPORT_STATUS_COMPLETE
                )
            }
            if (onCustomCompileTaskStatusListener != null) {
                resetTimelineBitdepth()
                resetCustomFx()
                if (isCompileFailed) {
                    onCustomCompileTaskStatusListener?.onVideoSaveFail()
                } else {
                    onCustomCompileTaskStatusListener?.onVideoSaveCompleted(isCanceled)
                }
            }
        }

        /**
         * NvsStreamingContext.PlaybackCallback
         */
        override fun onPlaybackPreloadingCompletion(nvsTimeline: NvsTimeline) {
            if (timeline !== nvsTimeline) {
                return
            }
            if (galleryVideoEngineListener != null) {
                galleryVideoEngineListener?.onPlayStatusChange(GalleryVideoEngineManager.PLAY_STATUS_READY)
            }
        }

        override fun onPlaybackStopped(nvsTimeline: NvsTimeline) {
            if (timeline !== nvsTimeline) {
                return
            }
            if (galleryVideoEngineListener != null) {
                galleryVideoEngineListener?.onPlayStatusChange(GalleryVideoEngineManager.PLAY_STATUS_STOPPED)
            }
        }

        override fun onPlaybackEOF(nvsTimeline: NvsTimeline) {
            if (timeline !== nvsTimeline) {
                return
            }
            if (galleryVideoEngineListener != null) {
                galleryVideoEngineListener?.onPlayStatusChange(GalleryVideoEngineManager.PLAY_STATUS_FINISH)
            }
        }

        /**
         * NvsStreamingContext.PlaybackCallback2
         */
        override fun onPlaybackTimelinePosition(nvsTimeline: NvsTimeline, position: Long) {
            if (timeline !== nvsTimeline) {
                return
            }
            if (galleryVideoEngineListener != null) {
                galleryVideoEngineListener?.onPlayPositionChange(position / MILLIS_TIME_BASE)
            }
        }

        /**
         * NvsStreamingContext.StreamingEngineCallback1&2
         */
        override fun onStreamingEngineStateChanged(nvsTimeline: NvsTimeline, state: Int) {
            if (timeline !== nvsTimeline) {
                return
            }
            if (galleryVideoEngineListener != null) {
                galleryVideoEngineListener?.onEngineStateChanged(state)
            }
        }

        override fun onFirstVideoFramePresented(nvsTimeline: NvsTimeline) {
            if (timeline !== nvsTimeline) {
                return
            }
            if (galleryVideoEngineListener != null) {
                galleryVideoEngineListener?.onFirstVideoFrameReady()
            }
        }

        /**
         * NvsStreamingContext.PlaybackExceptionCallback
         */
        override fun onPlaybackException(nvsTimeline: NvsTimeline, errCode: Int, msg: String) {
            if (timeline !== nvsTimeline) {
                return
            }
            if (galleryVideoEngineListener != null) {
                galleryVideoEngineListener?.onEngineException(errCode, msg)
            }
        }
    }

    init {
        // for nvs sdk, NvsStreamingContext.init should be called before setContentView
        NvsStreamingContext.setLoadPluginFromAssets(true)
        val debugLevel = if ((GProperty.DEBUG)) {
            NvsStreamingContext.DEBUG_LEVEL_DEBUG
        } else {
            NvsStreamingContext.DEBUG_LEVEL_ERROR
        }
        NvsStreamingContext.setDebugLevel(debugLevel)

        /**
         * The original signature:
         * int flag = NvsStreamingContext.STREAMING_CONTEXT_FLAG_COMPACT_MEMORY_MODE | NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT;
         */
        val flag = NvsContextFlagBuilder()
            .minimumMemoryUsage()
            .support4K()
            .apiInvokeAsynchronized()
            .supportForceStop()
            .build()
        meicamContext = MeicamContext.newInstance(context, flag)
        this.context = context.applicationContext

        galleryVideoEngineListener = listener

        val nvsStreamContext = nvsContext
        meicamAudioClip = MeicamAudioClip(this)
        meicamVideoClip = MeicamVideoClip(this, galleryVideoEngineListener, nvsStreamContext)
        meicamVideoSubTitle = MeicamVideoSubTitle(context, nvsStreamContext, this)
        meicamVideoFilter = MeicamVideoFilter()
        meicamThemeHelper = MeicamThemeHelper(this.context, this, galleryVideoEngineListener)
        meicamVideoThumbnail = MeicamVideoThumbnail(this.context, this)
        meicamVideoTheme = MeicamVideoTheme(nvsStreamContext)
        meicamVideoFx = MeicamVideoFx(nvsStreamContext)
        meicamVideoCutRotate = MeicamVideoCutRotate()
        meicamVideoTemplate = MeicamVideoTemplate()
        meicamVideoSticker = MeicamVideoSticker()
    }

    private val nvsContext: NvsStreamingContext?
        get() = meicamContext?.nvsStreamingContext

    /*---------------- engine base start ----------------*/
    /**
     * 初始化engine预览时和保存时的帧率宽高，预览时还会受[IGalleryVideoEngine.setPreviewMaxHeight]影响
     *
     * @param videoWidth  保存或者预览时分辨率中的宽度
     * @param videoHeight 保存或者预览时分辨率中的高度
     * @param fps         保存和预览的帧率
     * @return
     */
    override fun initEngine(videoWidth: Int, videoHeight: Int, fps: Rational): Boolean {
        if ((videoWidth <= 0) || (videoHeight <= 0)) {
            GLog.e(TAG, LogFlag.DL, "initEngine error: the videoWidth and videoHeight must not less than 0")
            return false
        }
        val resolution = cropSize(videoWidth, videoHeight)
        resolutionWidth = resolution[0]
        resolutionHeight = resolution[1]
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.e(TAG, LogFlag.DL, "initEngine error: NvsStreaming context isn't initialized or has been recycled")
            return false
        }
        GLog.d(TAG, LogFlag.DL, "initEngine createTimeline w:" + videoWidth + ", h:" + videoHeight + ", fps:" + fps.toFloat())
        val nvsVideoResolution = meicamVideoClip.getVideoResolution(resolutionWidth, resolutionHeight)
        if (VideoEditorHelper.isTypeSupportHdrEdit(meicamVideoClip.videoType)) {
            nvsVideoResolution.bitDepth = NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_16_BIT_FLOAT
        }
        val timeline: NvsTimeline? = nvsStreamContext.createTimeline(
            nvsVideoResolution,
            meicamVideoClip.getFpsRate(fps), MeicamAudioClip.getAudioResolution()
        )
        if (timeline == null) {
            GLog.e(TAG, LogFlag.DL, "[initEngine] error: create timeline failed")
            return false
        }
        this.timeline = timeline

        if (fps.toFloat().compareTo(0f) > 0) {
            frameDurationMs = (MILLIS_TIME_BASE.toFloat() / fps.toFloat()).toInt()
        }
        val meicamTimeline = MeicamTimeline(timeline, videoWidth, videoHeight)
        this.meicamTimeline = meicamTimeline
        meicamTimeline.videoCodecType = videoCodecType
        sdkCallbackInterface = SdkCallbackInterface(sdkCallback)
        // set save mp4 callback
        nvsStreamContext.setCompileCallback(sdkCallbackInterface?.compileCallback)
        // set save mp4 callback2
        nvsStreamContext.setCompileCallback2(sdkCallbackInterface?.compileCallback2)
        // set playback callback
        nvsStreamContext.setPlaybackCallback(sdkCallbackInterface?.playbackCallback)
        // set playback callback2
        nvsStreamContext.setPlaybackCallback2(sdkCallbackInterface?.playbackCallback2)
        // set timeline strem ready callback
        nvsStreamContext.setStreamingEngineCallback(sdkCallbackInterface?.streamingEngineCallback)
        // set playback exception callback
        nvsStreamContext.setPlaybackExceptionCallback(sdkCallbackInterface?.exceptionCallback)
        meicamAudioClip.setTimeline(timeline)
        meicamVideoClip.setTimeline(timeline)
        meicamThemeHelper.setTimeline(timeline)
        meicamVideoSubTitle.setTimeline(meicamTimeline)
        meicamVideoFilter.setTimeline(meicamTimeline)
        meicamVideoTheme.setTimeline(timeline)
        meicamVideoTemplate.setTimeline(timeline)
        meicamVideoSticker.setTimeline(meicamTimeline)
        meicamVideoThumbnail.setTimeline(timeline)
        meicamVideoFx.setTimeline(meicamTimeline)
        meicamVideoCutRotate.setTimeline(meicamTimeline, resolutionWidth, resolutionHeight)
        return true
    }

    override fun setCodecType(type: Int) {
        videoCodecType = type
    }

    /**
     * 创建视频预览窗口
     *
     * @param viewGroup  视频窗口的载体view
     * @param shouldCrop 是否裁剪
     */
    override fun createLiveWindow(viewGroup: ViewGroup, shouldCrop: Boolean): Boolean {
        var galleryEditorVideoView: GalleryEditorVideoView? = null
        if (viewGroup is GalleryEditorVideoView) {
            galleryEditorVideoView = viewGroup
        }
        if (galleryEditorVideoView == null) {
            return false
        }
        if ((resolutionHeight <= 0) || (resolutionWidth <= 0)) {
            GLog.e(TAG, LogFlag.DL, "createLiveWindow error: because the width == $resolutionWidth ,the height == $resolutionHeight")
            return false
        }
        val videoRatio = VideoRatio(resolutionWidth, resolutionHeight)
        val view = createVideoView(galleryEditorVideoView)
        view?.fillMode = if (shouldCrop) NvsLiveWindow.FILLMODE_PRESERVEASPECTCROP else NvsLiveWindow.FILLMODE_PRESERVEASPECTFIT
        galleryEditorVideoView.addLiveWindow(view)
        liveWindow = view
        meicamVideoSubTitle.setLiveWindow(liveWindow)
        galleryEditorVideoView.updateVideoViewRatio(videoRatio)
        meicamVideoCutRotate.setGalleryVideoView(galleryEditorVideoView)
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.e(TAG, LogFlag.DL, "createLiveWindow error: NvsStreaming context isn't initialized or has been recycled")
            return false
        }
        if (VideoEditorHelper.isTypeSupportHdrEdit(meicamVideoClip.videoType)) {
            liveWindow?.setHDRDisplayMode(NvsLiveWindow.HDR_DISPLAY_MODE_DEPEND_DEVICE)
            /**
             * 设置在SDR向HDR转换时候色彩增益
             * 色彩增益 范围是【1.0，10.0】
             */
            nvsStreamContext.colorGainForSDRToHDR = COLOR_GAINT_FOR_SDR_TO_HDR_VALUE
        }
        val result = nvsStreamContext.connectTimelineWithLiveWindow(timeline, liveWindow)
        if (!result) {
            GLog.e(TAG, LogFlag.DL, "createLiveWindow(), connectTimelineWithLiveWindow failed.")
            return false
        }
        return true
    }

    /**
     * 裁剪视频的宽高，使长度符合编解码器的需要
     *
     * @param videoWidth  视频的宽
     * @param videoHeight 视频的高
     * @return 裁剪后视频的宽高，数组大小为2，下标0则是裁剪后的宽，1则是裁剪后的高度
     */
    private fun cropSize(videoWidth: Int, videoHeight: Int): IntArray {
        // for meicam. video width % 4 must equals 0, and video height % 2 must equals 0
        var videoWidth = videoWidth
        var videoHeight = videoHeight
        var divCheck = videoWidth % VIDEO_WIDTH_DIV_ZERO_VALUE
        if (divCheck != 0) {
            videoWidth -= divCheck
            GLog.d(TAG, LogFlag.DL, "cropSize, need change videoWidth:$videoWidth")
        }
        divCheck = videoHeight % VIDEO_HEIGHT_DIV_ZERO_VALUE
        if (divCheck != 0) {
            videoHeight -= divCheck
            GLog.d(TAG, LogFlag.DL, "cropSize, need change mVideoHeight:$videoHeight")
        }

        return intArrayOf(videoWidth, videoHeight)
    }

    /**
     * 限制预览的最大分辨率，若是视频的高度大于maxHeight，则等比缩小宽高
     *
     * @param maxHeight 限制预览的最大高度
     */
    override fun setPreviewMaxHeight(maxHeight: Int) {
        if ((resolutionWidth <= 0) || (resolutionHeight <= 0)) {
            return
        }
        if (resolutionWidth > resolutionHeight) {
            if (resolutionHeight > maxHeight) {
                GLog.d(TAG, LogFlag.DL, "setPreviewMaxHeight() adjust videoHeight:$resolutionHeight")
                nvsRational = NvsRational(maxHeight, resolutionHeight)
            } else {
                nvsRational = NvsRational(1, 1)
            }
        } else {
            if (resolutionWidth > maxHeight) {
                GLog.d(TAG, LogFlag.DL, "setPreviewMaxHeight() adjust resolution:$resolutionWidth")
                nvsRational = NvsRational(maxHeight, resolutionWidth)
            } else {
                nvsRational = NvsRational(1, 1)
            }
        }
    }

    override fun getTimeBase(): Long {
        return MILLIS_TIME_BASE.toLong()
    }

    override fun getCurrentTime(): Long {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "getCurrentTime: NvsStreaming context isn't initialized or has been recycled")
            return 0
        }
        var meicamCurrent = nvsStreamContext.getTimelineCurrentPosition(timeline)
        if ((duration != 0L) && ((duration - meicamCurrent) <= VIDEO_SEEK_OFFSET_FOR_DURATION)) {
            /*
            for some vidoe duration, such as 6000ms, we can not seek to 6000ms, just to 5999ms
            so we need to adjust it for showing 00:06
            */
            meicamCurrent = duration
        }
        return meicamCurrent / MILLIS_TIME_BASE
    }

    /**
     * 获取时间轴的原始时间位置（未经末尾偏移调整）。
     *
     * 与 [getCurrentTime] 不同，此方法直接返回美摄时间轴位置，不会处理视频末尾的偏移补偿。
     * 适用于需要精确时间同步（如导出图片）。
     *
     * @return Long 微秒
     *
     * 示例：
     * - 若视频总时长 duration=6000000μs，当美摄实际位置为 5999999μs 时：
     *   getRealCurrentTime() = 5999999（微秒）
     *   getCurrentTime()     = 6000（毫秒）
     */
    override fun getRealCurrentTime(): Long {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "getRealCurrentTime: NvsStreaming context isn't initialized or has been recycled")
            return 0
        }
        return nvsStreamContext.getTimelineCurrentPosition(timeline)
    }

    private val duration: Long
        get() = timeline?.duration ?: 0L

    override fun getTotalTime(): Long {
        return duration / MILLIS_TIME_BASE
    }

    override fun getCurrentFrame(): Bitmap? {
        val frame = liveWindow?.takeScreenshot()
        GLog.d(TAG, LogFlag.DL, "getCurrentFrame frame = $frame")
        return frame
    }

    override fun repaintFrame() {
        if (liveWindow == null) {
            GLog.w(TAG, LogFlag.DL, "repaintFrame mLiveWindow is null.")
            return
        }
        liveWindow?.repaintVideoFrame()
    }

    override fun isPlaying(): Boolean {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "isPlaying: NvsStreaming context isn't initialized or has been recycled")
            return false
        }
        return (nvsStreamContext.streamingEngineState == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK)
    }

    /**
     * play from start to end of stream
     */
    override fun play() {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "play: NvsStreaming context isn't initialized or has been recycled")
            return
        }

        var success = false
        success = if (nvsRational != null) {
            nvsStreamContext.playbackTimeline(
                timeline, 0, duration,
                nvsRational, true, 0
            )
        } else {
            nvsStreamContext.playbackTimeline(
                timeline, 0, duration,
                NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_FULLSIZE, true, 0
            )
        }
        GLog.d(TAG, LogFlag.DL, "play.playbackTimeline success = $success")
    }

    /**
     * play from startTime to endTime
     * @param startTime
     * @param endTime
     */
    override fun play(startTime: Long, endTime: Long) {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "play: nvsStreamContext isn't initialized or has been recycled!" +
                    " start time is $startTime, end time is $endTime")
            return
        }

        var success = false
        success = if (nvsRational != null) {
            nvsStreamContext.playbackTimeline(
                timeline,
                startTime * MILLIS_TIME_BASE,
                endTime * MILLIS_TIME_BASE,
                nvsRational,
                true,
                0
            )
        } else {
            nvsStreamContext.playbackTimeline(
                timeline,
                startTime * MILLIS_TIME_BASE,
                endTime * MILLIS_TIME_BASE,
                NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_FULLSIZE,
                true,
                0
            )
        }
        GLog.d(TAG, LogFlag.DL, "play.playbackTimeline startTime = $startTime, endTime = $endTime, getTotalTime = ${getTotalTime()}," +
                " success = $success")
    }

    override fun resume() {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "resume: NvsStreaming context isn't initialized or has been recycled")
            return
        }

        val state = nvsStreamContext.streamingEngineState
        GLog.d(TAG, LogFlag.DL, "resume() state:$state")
        if ((state == NvsStreamingContext.STREAMING_ENGINE_STATE_STOPPED)
            || (state == NvsStreamingContext.STREAMING_ENGINE_STATE_SEEKING)
        ) {
            if (nvsRational != null) {
                nvsStreamContext.playbackTimeline(
                    timeline, nvsStreamContext.getTimelineCurrentPosition(timeline),
                    -1, nvsRational, true, 0
                )
            } else {
                nvsStreamContext.playbackTimeline(
                    timeline, nvsStreamContext.getTimelineCurrentPosition(timeline),
                    -1, NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_FULLSIZE, true, 0
                )
            }
        }
    }

    override fun pause() {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "pause: NvsStreaming context isn't initialized or has been recycled")
            return
        }

        val state = nvsStreamContext.streamingEngineState
        GLog.d(TAG, LogFlag.DL, "pause() state:$state")
        if (state == NvsStreamingContext.STREAMING_ENGINE_STATE_PLAYBACK) {
            nvsStreamContext.stop(NvsStreamingContext.STREAMING_ENGINE_STOP_FLAG_ASYNC)
        }
    }

    override fun stop() {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "stop: NvsStreaming context isn't initialized or has been recycled")
            return
        }

        nvsStreamContext.stop(NvsStreamingContext.STREAMING_ENGINE_STOP_FLAG_ASYNC)
    }

    override fun stop(force: Boolean) {
        GLog.d(TAG, LogFlag.DL, "stop: force = $force")
        if (!force) {
            stop()
        } else {
            val nvsStreamContext = nvsContext
            if (nvsStreamContext == null) {
                GLog.w(TAG, LogFlag.DL, "stop: NvsStreaming context isn't initialized or has been recycled, is force stop: $force")
                return
            }
            nvsStreamContext.stop(NvsStreamingContext.STREAMING_ENGINE_STOP_FLAG_FORCE_STOP_COMPILATION)
        }
    }

    /**
     * time : milliseconds
     * @param time
     * @return
     */
    override fun seekTo(time: Long): Boolean {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "seekTo: NvsStreaming context isn't initialized or has been recycled")
            return false
        }

        var meicamTime = time * MILLIS_TIME_BASE
        if (meicamTime > duration - VIDEO_SEEK_OFFSET_FOR_DURATION) {
            // we can not seek to the duration time of the video, so it need to adjust
            meicamTime = duration - VIDEO_SEEK_OFFSET_FOR_DURATION
        }
        var result = false
        result = if (nvsRational != null) {
            nvsStreamContext.seekTimeline(
                timeline, meicamTime,
                nvsRational,
                NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
            )
        } else {
            nvsStreamContext.seekTimeline(
                timeline, meicamTime,
                NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_FULLSIZE,
                NvsStreamingContext.STREAMING_ENGINE_SEEK_FLAG_SHOW_CAPTION_POSTER
            )
        }
        GLog.d(TAG, LogFlag.DL, "seekTo: time = $time seekTimeline = $meicamTime  result = $result")
        return result
    }

    /**
     * reset to start position
     */
    override fun reset() {
        val result = seekTo(0)
        GLog.d(TAG, LogFlag.DL, "reset: result = $result")
        if (result && (galleryVideoEngineListener != null)) {
            galleryVideoEngineListener?.onPlayPositionChange(0)
        }
    }

    private val isDolbyType: Boolean
        get() = (videoCodecType and DOLBY_VIDEO_TYPE) == DOLBY_VIDEO_TYPE

    override fun saveVideo(stringUri: String): Boolean {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "saveVideo: NvsStreaming context isn't initialized or has been recycled")
            return false
        }

        nvsStreamContext.customCompileVideoHeight = COMPILE_VIDEO_HEIGHT_DEFAULT
        val success = nvsStreamContext.compileTimeline(
            timeline,
            0, duration,
            stringUri,
            NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
            NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
            buildCompileFlag(false, true)
        )
        GLog.d(TAG, LogFlag.DL, "saveVideo: success ? $success, file is $stringUri")
        if (success) {
            notifyMediaScan(context, stringUri)
        }
        return success
    }

    /**
     * 保存视频到指定路径，可指定起始时刻与帧率
     * @param filePath: 保存路径
     * @param startTime: 起始时刻
     * @param endTime: 结束时刻
     * @param fps: 帧率
     * @param videoUri: 源文件的uri
     * @param videoFilePath: 源文件的filePath
     */
    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    override fun saveVideo(filePath: String, startTime: Long, endTime: Long, fps: Int, videoUri: String?, videoFilePath: String?): Boolean {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "saveVideo: NvsStreaming context isn't initialized or has been recycled")
            return false
        }

        // 根据业务需求决定导出的格式
        val targetHdrTpe = getExportVideoHdrType()
        // 如果是 hdr 转 sdr，使用自有下变换算法
        if ((meicamVideoClip.editVideoHdrType != SDR_VIDEO_TYPE) && (targetHdrTpe == SDR_VIDEO_TYPE)) {
            appendVideoFx(videoUri)
        }
        val isWideColorGamutDisplayP3 = (isWideColorGamut && (getColorSpace(videoFilePath) == DISPLAY_P3))
        // 配置config
        nvsStreamContext.compileConfigurations = createSaveVideoConfigurations(fps, targetHdrTpe, videoFilePath, isWideColorGamutDisplayP3)
        nvsStreamContext.customCompileVideoHeight = meicamVideoClip.videoFileHeight
        // 配置BitDepth
        val isDeviceAndVideoSupportHdrEdit = VideoEditorHelper.isDeviceAndVideoSupportHdrEdit(targetHdrTpe)
        timeline?.changeVideoBitDepth(
            if (isDeviceAndVideoSupportHdrEdit) {
                NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_16_BIT_FLOAT
            } else {
                NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
            }
        )
        if (isWideColorGamut && getColorSpace(videoFilePath) == DISPLAY_P3) {
            // 由于美摄当是sdr视频时会转成sRGB，所以需要禁止色域转换
            meicamVideoClip.nvsVideoClip?.disableClipColorPrimariesConvert(true)
        }
        val success = nvsStreamContext.compileTimeline(
            timeline,
            startTime * MILLIS_TIME_BASE,
            endTime * MILLIS_TIME_BASE,
            filePath,
            NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
            NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
            buildCompileFlag(false, false)
        )
        nvsStreamContext.compileConfigurations = null
        if (success) {
            notifyMediaScan(context, filePath)
        }
        GLog.d(TAG, LogFlag.DL) {
            "saveVideo, success=$success, fps=$fps, originHdr=${meicamVideoClip.editVideoHdrType}, targetHdr=$targetHdrTpe, " +
                    "isDeviceAndVideoSupportHdrEdit=$isDeviceAndVideoSupportHdrEdit"
        }
        return success
    }

    /**
     * 做 hdr 转 sdr， 添加下变换特效到美摄中
     */
    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun appendVideoFx(videoUri: String?) {
        meicamVideoClip.nvsVideoClip?.apply {
            // hlg 视频优先使用相机写入的下变换信息，如果没有写，使用 dolby 的默认下变换信息
            val hdrTransformData = if (videoUri.isNullOrEmpty() || (meicamVideoClip.editVideoHdrType != HLG_VIDEO_TYPE)) {
                HdrTransformDataStruct.dolbyHdrTransformData()
            } else {
                var data = HdrTransformDataStruct.dolbyHdrTransformData()
                ExtendDataUtils.getExtendData(
                    Uri.parse(videoUri), EXTEND_KEY_VIDEO_HDR_TRANSFORM_DATA,
                    VideoTransformStruct::class.java
                )?.let {
                    data = data.copy(
                        gammaEnable = true,
                        hlgDstGammaTable = it.hlgGamma,
                        srgbDstGammaTable = it.srgbGamma
                    )
                }
                data
            }
            GLog.d(TAG, LogFlag.DL, "appendVideoFx, hdrTransformData=$hdrTransformData")
            appendCustomFx(OliveHdrVideoFx(hdrTransformData, TransformConfig(outputTextureIsSDR = true))).apply {
                fxIndex = index
            }
            disableHDRTonemappingToSDR(true)
            hanUseCustomFx = true
        }
    }

    override fun getExportVideoHdrType(): Int {
        return when (meicamVideoClip.editVideoHdrType) {
            // 杜比，HDR10，HDR10+ 转为 HLG
            DOLBY_VIDEO_TYPE, HDR10_VIDEO_TYPE, HDR10PLUS_VIDEO_TYPE -> HLG_VIDEO_TYPE
            else -> meicamVideoClip.editVideoHdrType
        }
    }

    override fun saveVideo(stringUri: String, videoHeight: Int, videoDateTaken: Long, videoPath: String): Boolean {
        var saveVideoHeight = videoHeight
        GLog.w(TAG, LogFlag.DL, "start saveVideo")
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "saveVideo: NvsStreaming context isn't initialized or has been recycled")
            return false
        }

        val divCheck = saveVideoHeight % VIDEO_HEIGHT_DIV_ZERO_VALUE
        if (divCheck != 0) {
            saveVideoHeight -= divCheck
            GLog.d(TAG, LogFlag.DL, "saveVideo: need change videoHeight is $saveVideoHeight, divCheck is $divCheck")
        }
        nvsStreamContext.customCompileVideoHeight = saveVideoHeight
        val isWideColorGamutDisplayP3 = (isWideColorGamut && (getColorSpace(videoPath) == DISPLAY_P3))
        if (videoDateTaken > 0) {
            setVideoDateTaken(nvsStreamContext, videoDateTaken, isWideColorGamutDisplayP3)
        }
        if (isWideColorGamutDisplayP3) {
            // 由于美摄当是sdr视频时会转成sRGB，所以需要禁止色域转换
            meicamVideoClip.nvsVideoClip?.disableClipColorPrimariesConvert(true)
        }

        val success = nvsStreamContext.compileTimeline(
            timeline,
            0,
            duration,
            stringUri,
            NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
            NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
            buildCompileFlag(false, false)
        )
        GLog.d(TAG, LogFlag.DL, "saveVideo: success ? $success, videoHeight is $saveVideoHeight, uri is $stringUri")
        if (success) {
            notifyMediaScan(context, stringUri)
        }
        return success
    }

    /**
     * 创建导出视频的configuration参数
     * 配置 fps，编码类型，颜色传输特性
     * @param fps 期望导出的帧率
     * @param targetHdrType 期望导出的 hdr 类型
     * @param filePath 源文件的 path
     * @return 用于传给美摄的 nvsStreamContext.compileConfigurations
     */
    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun createSaveVideoConfigurations(
        fps: Int,
        targetHdrType: Int,
        filePath: String?,
        isWideColorGamutDisplayP3: Boolean
    ): Hashtable<String, Any> {
        val configurations: Hashtable<String, Any> = Hashtable<String, Any>()
        // 配置视频帧率
        configurations[NvsStreamingContext.COMPILE_FPS] = NvsRational(fps, 1)
        // 支持广色域且是DISPLAY_P3 需要下发display p3给美摄
        if (isWideColorGamutDisplayP3) {
            configurations[COMPILE_VIDEO_COLOR_PRIMARIES] = COMPILE_VIDEO_COLOR_PRIMARIES_VALUE
        }
        if (!VideoEditorHelper.isDeviceAndVideoSupportHdrEdit(targetHdrType)) {
            /*
                1.目标是HDR，但是不支持HDR时需要做下变换
                2.需要判断文件是美摄能认出来的HDR类型，才能使用appendCustomFx接口，否则美摄只会解析出来sdr帧，无法作为影像下变换的入参，比如profile5格式
            */
            if ((targetHdrType != SDR_VIDEO_TYPE) && isHDRGamma(filePath)) {
                meicamVideoClip.nvsVideoClip?.apply {
                    appendCustomFx(
                        OliveHdrVideoFx(
                            // 该参数后续需要更换，目前暂时先使用默认的hdrData
                            HdrTransformDataStruct.defaultHdrTransformData(),
                            TransformConfig(outputTextureIsSDR = true)
                        )
                    ).apply {
                        fxIndex = index
                    }
                    disableHDRTonemappingToSDR(true)
                    hanUseCustomFx = true
                    GLog.d(TAG, LogFlag.DL, "createSaveVideoConfigurations, use append fx")
                }
            }
            GLog.d(TAG, LogFlag.DL, "createSaveVideoConfigurations, do sdr")
            return configurations
        }
        // 配置视频压缩格式，HDR配置成hevc(h.265)
        configurations[NvsStreamingContext.COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
        // 配置COLOR_TRANSFER
        when (targetHdrType) {
            HDR10_VIDEO_TYPE -> configurations[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_HDR10_COLOR_TRANSFER_VALUE
            HLG_VIDEO_TYPE -> configurations[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_HLG_COLOR_TRANSFER_VALUE
            // 目前无法识别HDR10+格式的视频，HDR10+的视频都用HDR10的方式进行处理
            HDR10PLUS_VIDEO_TYPE -> configurations[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_HDR10PLUS_COLOR_TRANSFER_VALUE
            DOLBY_VIDEO_TYPE -> configurations[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = COMPILE_VIDEO_DOLBY_COLOR_TRANSFER_VALUE
        }
        return configurations
    }

    /**
     * 通过美摄接口判断视频文件是否hdr类型
     */
    private fun isHDRGamma(filePath: String?): Boolean {
        return getColorSpace(filePath)?.isHdrGamma() == true
    }

    /**
     * 通过美摄接口获取视频色域
     */
    private fun getColorSpace(filePath: String?): ColorSpace? {
        var colorSpace: ColorSpace? = ColorSpaceExt.SRGB
        runCatching {
            val info = nvsContext?.getAVFileInfo(filePath, NvsStreamingContext.AV_FILEINFO_EXTRA_INFO)
            if (info != null) {
                val colorPrimaries: Int = info.getVideoStreamColorPrimaries(0)
                val colorTransfer: Int = info.getVideoStreamColorTranfer(0)
                if ((colorPrimaries == NvsVideoStreamInfo.COLOR_PRIMARIES_BT2020)
                    && (colorTransfer == NvsVideoStreamInfo.COLOR_TRANSFER_HLG)
                ) {
                    colorSpace = BT2020_HLG
                } else if ((colorPrimaries == NvsVideoStreamInfo.COLOR_PRIMARIES_BT2020)
                    && (colorTransfer == NvsVideoStreamInfo.COLOR_TRANSFER_ST2084)
                ) {
                    colorSpace = BT2020_PQ
                } else if ((colorPrimaries == NvsVideoStreamInfo.COLOR_PRIMARIES_DISPLAY_P3)
                    && (colorTransfer == NvsVideoStreamInfo.COLOR_TRANSFER_SDR_VIDEO)
                ) {
                    colorSpace = DISPLAY_P3
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "getColorSpace, failed! error=", it)
        }
        return colorSpace
    }

    private fun resetCustomFx() {
        if (hanUseCustomFx) {
            meicamVideoClip.nvsVideoClip?.apply {
                if (fxIndex != -1) {
                    removeFx(fxIndex)
                    fxIndex = -1
                }
                disableHDRTonemappingToSDR(false)
                hanUseCustomFx = false
            }
        }
    }

    override fun saveShareVideo(
        stringUri: String,
        start: Long,
        end: Long,
        videoHeight: Int
    ): Boolean {
        var height = videoHeight
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "saveShareVideo: NvsStreaming context isn't initialized or has been recycled")
            return false
        }

        val divCheck = height % VIDEO_HEIGHT_DIV_ZERO_VALUE
        if (divCheck != 0) {
            height -= divCheck
            GLog.d(TAG, LogFlag.DL, "saveShareVideo: need change videoHeight is $height, divCheck is $divCheck")
        }
        nvsStreamContext.customCompileVideoHeight = height
        var success = false
        var splitResult = true

        if (getGalleryVideoClip().isSupportSlowMotionMode) {
            val time = System.currentTimeMillis()
            splitResult = meicamVideoClip.splitSlowMotion()
            // splitting video will remove filter, we need refresh the video filter,
            meicamVideoFilter.resetFilter()
            GLog.d(TAG, LogFlag.DL, "saveShareVideo: splitResult is $splitResult, time costs is ${System.currentTimeMillis() - time}, " +
                    "slow video time is ${getTotalTime()}")
        }
        success = nvsStreamContext.compileTimeline(
            timeline,
            start * MILLIS_TIME_BASE,
            end * MILLIS_TIME_BASE, stringUri,
            NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
            NvsStreamingContext.COMPILE_BITRATE_GRADE_MEDIUM,
            buildCompileFlag(true, false)
        )
        success = success and splitResult
        GLog.d(TAG, LogFlag.DL, "saveShareVideo: success ? $success, videoHeight is $height, file is $stringUri")
        return success
    }

    /**
     * 定制化保存视频
     */
    override fun saveVideo(info: SaveVideoInfo): Boolean {
        val nvsStreamContext = nvsContext
        nvsStreamContext ?: let {
            w(TAG, LogFlag.DL, "saveVideo: NvsStreaming context isn't initialized or has been recycled")
            return false
        }

        var videoHeight = calculateHeight(info)
        if (videoHeight <= 0) {
            w(TAG, LogFlag.DL, "saveVideo: video videoHeight is zero")
            return false
        }
        val divCheck = videoHeight % VIDEO_HEIGHT_DIV_ZERO_VALUE
        if (divCheck != 0) {
            videoHeight -= divCheck
            d(TAG, LogFlag.DL) {
                String.format(Locale.ENGLISH, "saveVideo: need change videoHeight is %d, divCheck is %d", videoHeight, divCheck)
            }
        }
        nvsStreamContext.customCompileVideoHeight = videoHeight

        setCompileConfigurations(
            nvsStreamContext,
            info.creationTime,
            info.frameRate,
            info.bitRate.toLong(),
            info.codecFormat,
            info.isKeepHdr
        )

        val success = nvsStreamContext.compileTimeline(
            timeline,
            0,
            timeline?.duration ?: 0,
            info.stringUri,
            NvsStreamingContext.COMPILE_VIDEO_RESOLUTION_GRADE_CUSTOM,
            NvsStreamingContext.COMPILE_BITRATE_GRADE_HIGH,
            buildCompileFlag(noUseInputSurface = false, needDisableHardwareEncoder = false)
        )
        d(TAG, LogFlag.DL) {
            String.format(Locale.ENGLISH, "saveVideo: success ? %s, videoHeight is %d, uri is %s", success, videoHeight, info.stringUri)
        }
        if (success) {
            notifyMediaScan(context, info.stringUri)
        }
        return success
    }

    private fun calculateHeight(info: SaveVideoInfo): Int {
        val originalHeight: Int = meicamVideoClip.videoFileHeight
        val originalWidth: Int = meicamVideoClip.videoFileWidth
        var videoHeight = 0
        // 期望宽度、期望高度、期望边长壁纸只会传一个过来，这里用大于0来判断壁纸传入的是哪个
        when {
            info.width > 0 -> {
                videoHeight = originalHeight
                if (originalWidth > info.width) {
                    videoHeight = (1f * info.width * originalHeight / originalWidth).toInt()
                }
            }

            info.height > 0 -> {
                videoHeight = if (originalHeight < info.height) {
                    originalHeight
                } else {
                    info.height
                }
            }

            info.length > 0 -> {
                if (originalHeight > originalWidth) {
                    videoHeight = if (originalHeight < info.length) {
                        originalHeight
                    } else {
                        info.length
                    }
                } else {
                    videoHeight = originalHeight
                    if (originalWidth > info.length) {
                        videoHeight = (1f * info.length * originalHeight / originalWidth).toInt()
                    }
                }
            }
        }
        return videoHeight
    }

    /**
     * 重置timeline位深度，保证预览显示亮度正常
     */
    private fun resetTimelineBitdepth() {
        val bitDepth = if (VideoEditorHelper.isTypeSupportHdrEdit(meicamVideoClip.videoType)) {
            NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_16_BIT_FLOAT
        } else {
            NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
        }
        val changeStatus = timeline?.changeVideoBitDepth(bitDepth)
        nvsContext?.clearCachedResources(false)
        GLog.d(TAG, LogFlag.DL, "resetTimelineBitdepth to $bitDepth status: $changeStatus")
    }

    /**
     * 获取timeline指定帧时刻图片，并且管控是否继续播放（需要在主线程中调用）
     * @param frameTime: 取图帧时刻
     * @param endTime: 播放停止时刻
     * @param isResumePlay: 播放暂停后是否继续播放
     */
    override suspend fun grabImageFromTimelineAsync(frameTime: Long, endTime: Long, isResumePlay: Boolean): Bitmap? {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext != null) {
            // 此循环是为了避免出现grabImageFromTimelineAsync取图被覆盖场景
            while (lastGrabCoroutine != null) {
                GLog.d(TAG, LogFlag.DL) { "grabImageFromTimelineAsync: delay time." }
                delay(GRAB_DELAY_TIME_MS)
            }
            // 记录取图前的播放状态
            val isPlaying = isPlaying()
            val resultBitmap = suspendCancellableCoroutine { continuation ->
                lastGrabCoroutine = continuation
                // 添加取消监听
                continuation.invokeOnCancellation {
                    GLog.d(TAG, LogFlag.DL) { "grabImageFromTimelineAsync: cancelled." }
                    handleReleaseContinuation(continuation, nvsStreamContext)
                }
                nvsStreamContext.setImageGrabberCallback { bitmap, _ ->
                    GLog.d(TAG, LogFlag.DL) { "grabImageFromTimelineAsync: $bitmap, $continuation" }
                    lastGrabCoroutine = null
                    continuation.resume(bitmap)
                }
                // 防止grabImageFromTimelineAsync接口出现异常continuation没有resume导致anr
                kotlin.runCatching {
                    // NvsRational设置代理级别，此处设置1/1，不对取图做压缩
                    val success = nvsStreamContext.grabImageFromTimelineAsync(timeline, frameTime, NvsRational(1, 1), 0)
                    if (!success) {
                        GLog.e(TAG, LogFlag.DL) { "grabImageFromTimelineAsync: grab false." }
                        handleReleaseContinuation(continuation, nvsStreamContext)
                    }
                }.onFailure { e ->
                    GLog.e(TAG, LogFlag.DL, "grabImageFromTimelineAsync fail", e)
                    handleReleaseContinuation(continuation, nvsStreamContext)
                }
            }
            // 决定是否继续播放（需要保证play与grabImageFromTimelineAsync在同一线程中执行）
            if (isResumePlay && isPlaying) play(getCurrentTime(), endTime)
            return resultBitmap
        } else {
            GLog.e(TAG, LogFlag.DL, "grabImageFromTimelineAsync fail, timeline or nvsContext is null")
        }
        return null
    }

    /**
     * 释放资源
     * @param continuation CancellableContinuation<Bitmap?>
     * @param nvsStreamContext NvsStreamingContext
     */
    private fun handleReleaseContinuation(continuation: CancellableContinuation<Bitmap?>, nvsStreamContext: NvsStreamingContext) {
        lastGrabCoroutine = null
        continuation.resume(null)
        nvsStreamContext.setImageGrabberCallback(null)
    }

    @RequiresApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    override suspend fun grabImageFromTimelineAsync(timeUs: Long, config: Bitmap.Config, colorSpace: ColorSpace): Bitmap? {
        val nvsStreamContext = nvsContext ?: return null
        val nvsTimeline = timeline ?: return null
        var hasChangeBitDepth = false
        val time = System.currentTimeMillis()

        // 根据美摄描述，[grabImageFromTimelineAsync]方法调用需要等上次方法调用结束后才能调用，否则其结果的回调将会丢失
        while (lastGrabCoroutine != null) {
            GLog.d(TAG, LogFlag.DL) { "getSdrFrameFromSourceAsyncByGpu: delay time." }
            delay(GRAB_DELAY_TIME_MS)
        }

        val resultBitmap = suspendCancellableCoroutine { continuation ->
            lastGrabCoroutine = continuation
            // 添加取消监听
            continuation.invokeOnCancellation {
                GLog.d(TAG, LogFlag.DL) { "grabImageFromTimelineAsync: cancelled." }
                handleReleaseContinuation(continuation, nvsStreamContext)
            }
            nvsStreamContext.setImageGrabberCallback { bitmap, _ ->
                GLog.d(TAG, LogFlag.DL) {
                    "<grabImageFromTimelineAsync> timeUs=$timeUs bmpConfig=${bitmap?.config} " +
                            "colorSpace=${bitmap?.colorSpace} costTime=${GLog.getTime(time)} ms"
                }
                continuation.resume(bitmap)
            }

            if (colorSpace.isHdrGamma()
                && VideoEditorHelper.isSupportExportUhdrPhoto(meicamVideoClip.editVideoHdrType)
                && (nvsTimeline.videoRes.bitDepth != NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_16_BIT_FLOAT)) {
                // 抽取HDR帧时，如果是HLG/杜比/HDR10/HDR10+，需要在16 bit位深
                nvsTimeline.changeVideoBitDepth(NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_16_BIT_FLOAT)
                nvsStreamContext.clearCachedResources(false)
                hasChangeBitDepth = true
            } else if (!colorSpace.isHdrGamma()
                && VideoEditorHelper.isSupportExportUhdrPhoto(meicamVideoClip.editVideoHdrType)
                && (nvsTimeline.videoRes.bitDepth != NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT)) {
                // 抽取SDR帧时，如果是HLG/杜比/HDR10/HDR10+视频，需要将 timeline 切换到 8bit
                nvsTimeline.changeVideoBitDepth(NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT)
                nvsStreamContext.clearCachedResources(false)
                hasChangeBitDepth = true
            }
            GLog.d(TAG, LogFlag.DL) { "<grabImageFromTimelineAsync> bitDepth=${nvsTimeline.videoRes.bitDepth} isHdrGamma=${colorSpace.isHdrGamma()}" }

            kotlin.runCatching {
                // NvsRational设置代理级别，此处设置1/1，不对取图做压缩
                val success = nvsStreamContext.grabImageFromTimelineAsync(
                    nvsTimeline,
                    timeUs,
                    NvsRational(1, 1),
                    config,
                    colorSpace.getMeicamColorSpaceName(),
                    0
                )
                if (!success) {
                    GLog.e(TAG, LogFlag.DL) { "grabImageFromTimelineAsync: grab false." }
                    handleReleaseContinuation(continuation, nvsStreamContext)
                }
            }.onFailure { e ->
                // 防止grabImageFromTimelineAsync接口出现异常continuation没有resume导致anr
                GLog.e(TAG, LogFlag.DL, "grabImageFromTimelineAsync fail", e)
                handleReleaseContinuation(continuation, nvsStreamContext)
            }
        }

        lastGrabCoroutine = null
        // 重置位深需要同样在主线程完成
        if (hasChangeBitDepth) {
            resetTimelineBitdepth()
        }

        return resultBitmap
    }

    /**
     * 使用硬件解码器加载缩图轴
     * @param filePath String 资源文件
     * @param codecIconEnabled Boolean 是否开启硬件解码器加载缩图
     */
    override fun setMediaCodecIconReaderEnabled(filePath: String, codecIconEnabled: Boolean) {
        val nvsStreamContext = nvsContext
        if (nvsStreamContext == null) {
            GLog.w(TAG, LogFlag.DL, "setMediaCodecIconReaderEnabled: NvsStreaming context isn't initialized or has been recycled")
            return
        }
        nvsStreamContext.setMediaCodecIconReaderEnabled(filePath, codecIconEnabled)
    }

    /**
     * 设置指定compile flag
     * @param noUseInputSurface: 不使用inputsurface
     * @param needDisableHardwareEncoder: 禁用硬件编码器
     */
    private fun buildCompileFlag(
        noUseInputSurface: Boolean,
        needDisableHardwareEncoder: Boolean
    ): Int {
        //生成文件输出的特殊标志，如果没有特殊需求，请填写0
        var compileFlag = 0
        if (noUseInputSurface) {
            compileFlag =
                compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DONT_USE_INPUT_SURFACE
        }
        if (needDisableHardwareEncoder) {
            compileFlag =
                compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_HARDWARE_ENCODER
        }
        if (isDolbyType) {
            // dolby视频调用的杜比编码器，无需做字节对齐
            compileFlag =
                compileFlag or NvsStreamingContext.STREAMING_ENGINE_COMPILE_FLAG_DISABLE_ALIGN_VIDEO_SIZE
        }
        return compileFlag
    }

    override fun destroy(cleanAllContext: Boolean) {
        if (cleanAllContext) {
            if (meicamContext != null) {
                val nvsContext = meicamContext?.nvsStreamingContext
                if (nvsContext != null) {
                    nvsContext.stop(NvsStreamingContext.STREAMING_ENGINE_INTERRUPT_STOP)
                    nvsContext.removeTimeline(timeline)
                }
                meicamContext?.recycle()
                meicamContext = null
            }
        } else {
            val nvsStreamContext = nvsContext
            nvsStreamContext?.removeTimeline(timeline)
        }
        if (sdkCallbackInterface != null) {
            sdkCallbackInterface?.removeSdkCallback()
        }
        galleryVideoEngineListener = null
    }

    /**
     * engine base end
     */
    override fun getGalleryThemeHelper(): IGalleryThemeHelper {
        return meicamThemeHelper
    }

    override fun getGalleryVideoClip(): IGalleryVideoClip {
        return meicamVideoClip
    }

    override fun getGalleryAudioClip(): IGalleryAudioClip {
        return meicamAudioClip
    }

    override fun getGalleryVideoFilter(): IGalleryVideoFilter {
        return meicamVideoFilter
    }

    override fun getGalleryVideoSubTitle(): IGalleryVideoSubTitle {
        return meicamVideoSubTitle
    }

    override fun getGalleryVideoThumbnail(): IGalleryVideoThumbnail {
        return meicamVideoThumbnail
    }

    override fun getGalleryVideoFx(): IGalleryVideoFx {
        return meicamVideoFx
    }

    override fun getIGalleryVideoCutRotate(): IGalleryVideoCutRotate {
        return meicamVideoCutRotate
    }

    override fun getGalleryVideoTemplate(): IGalleryVideoTemplate {
        return meicamVideoTemplate
    }

    override fun getGalleryVideoSticker(): IGalleryVideoSticker {
        return meicamVideoSticker
    }

    private val inflater: LayoutInflater
        /*---------------- engine view start ----------------*/
        get() = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

    override fun createVideoView(parent: ViewGroup): MeicamVideoView? {
        kotlin.runCatching {
            val videoView = MeicamVideoView(context)
            val bgColor =
                context.getColor(R.color.videoeditor_video_editor_background_color) // Joshua Debug
            val r = ((bgColor and MeicamVideoSubTitle.HEX_COLOR_OXOOFFOOOO) shr RGB_R_OFFSET).toFloat() / RGB_BIT_WIDTH
            val g = ((bgColor and MeicamVideoSubTitle.HEX_COLOR_OXOOOOFFOO) shr RGB_G_OFFSET).toFloat() / RGB_BIT_WIDTH
            val b = ((bgColor) and MeicamVideoSubTitle.HEX_COLOR_OXOOOOOOFF).toFloat() / RGB_BIT_WIDTH
            videoView.setBackgroundColor(r, g, b)
            GLog.d(TAG, LogFlag.DL, "getVideoView parent.getChildCount = " + parent.childCount + ",  videoView = " + videoView)
            return videoView
        }.onFailure { e ->
            GLog.e(TAG, LogFlag.DL, "getVideoView Exception:", e)
        }
        return null
    }

    fun setBackgroundColor(color: Color) {
        if (liveWindow != null) {
            liveWindow?.setBackgroundColor(color.red(), color.green(), color.blue())
        }
    }

    override fun getThumbnailView(parent: ViewGroup): View? {
        val thumbnailView = inflater.inflate(R.layout.videoeditor_video_editor_meicam_thumbnail_view, parent, false)
        if (thumbnailView !is MeicamThumbnailView) {
            GLog.w(TAG, LogFlag.DL, "getThumbnailView thumbnailView is not instanceof MeicamThumbnailView")
            return null
        }
        GLog.d(TAG, LogFlag.DL, "getThumbnailView parent.getChildCount = " + parent.childCount + ",  thumbnailView = " + thumbnailView)
        return thumbnailView
    }

    override fun dumpEngineInfo(writer: PrintWriter) {
        meicamThemeHelper.dumpThemeInfo(writer)
    }

    override fun getVideoHeight(): Int {
        val height = timeline?.videoRes?.imageHeight
        if (height != null) {
            return height
        }
        GLog.d(TAG, LogFlag.DL, "getVideoHeight is null, so return 0")
        return 0
    }

    override fun getVideoWidth(): Int {
        val width = timeline?.videoRes?.imageWidth
        if (width != null) {
            return width
        }
        GLog.d(TAG, LogFlag.DL, "getVideoWidth is null, so return 0")
        return 0
    }

    override fun getFrameDuration(): Int {
        return frameDurationMs
    }

    override fun getVideoScale(): Float {
        val meicamWidth = meicamTimeline?.width ?: 0
        val meicamHeight = meicamTimeline?.height ?: 0
        val liveWindowWidth = liveWindow?.width ?: 0
        val liveWindowHeight = liveWindow?.height ?: 0
        if ((liveWindow == null) || (meicamWidth <= 0) || (meicamHeight <= 0)) {
            return 1f
        }
        val widthScale = liveWindowWidth.toFloat() / meicamWidth
        val heightScale = liveWindowHeight.toFloat() / meicamHeight
        return min(widthScale.toDouble(), heightScale.toDouble()).toFloat()
    }

    /**
     * 注册监听自定义编译任务状态的回调接口
     */
    override fun setCustomCompileTaskListener(videoListener: OnCustomCompileTaskStatusListener?) {
        this.onCustomCompileTaskStatusListener = videoListener
    }

    /**
     * 更新自定义编译任务执行状态
     * @param isRunning: 是否正在执行
     */
    override fun updateCustomCompileTaskStatus(isRunning: Boolean) {
        // 任务开始或结束时，重置编译失败状态
        isCompileFailed = false
        this.customCompileTaskRunning = isRunning
    }

    /**
     * 获取NvsTimeline
     * @return NvsTimeline 时间线
     */
    override fun getMeicamVideoTimeline(): NvsTimeline? {
        return timeline
    }

    /**
     * 获取自定义编译任务的执行状态
     * @return true/false 是否正在执行
     */
    override fun getCustomCompileTaskStatus(): Boolean {
        return customCompileTaskRunning
    }

    /**
     * engine view end
     */
    override fun hasVideoChanged(): Boolean {
        return (meicamVideoClip.isVideoClipChanged
                || meicamVideoClip.isVideoSpeedChanged
                || meicamAudioClip.isAudioChanged
                || meicamVideoSubTitle.isSubTitleChanged
                || meicamVideoFilter.isFilterChanged()
                || meicamVideoTheme.isThemeChanged
                || meicamVideoTemplate.isTemplateChanged()
                || meicamVideoFx.isVideoFxChanged
                || meicamVideoCutRotate.isCutRotateChanged)
    }

    @Suppress("deprecation")
    private fun notifyMediaScan(context: Context?, filePath: String) {
        if ((null == context) || TextUtils.isEmpty(filePath)) {
            return
        }

        val intent = Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
        intent.setData(Uri.fromFile(File(filePath)))
        intent.setPackage(getMediaProviderPackageName(context))
        context.sendBroadcast(intent, PERMISSION_COMPONENT_SAFE)
    }

    private fun setVideoDateTaken(nvsContext: NvsStreamingContext, videoDateTaken: Long, isWideColorGamutDisplayP3: Boolean) {
        if (videoDateTaken <= 0) {
            return
        }
        val compileConfigurations = buildCompileConfigurations(videoDateTaken)
        if (isWideColorGamutDisplayP3) {
            compileConfigurations[COMPILE_VIDEO_COLOR_PRIMARIES] = COMPILE_VIDEO_COLOR_PRIMARIES_VALUE
        }
        nvsContext.compileConfigurations = compileConfigurations
    }

    private fun setCompileConfigurations(
        nvsContext: NvsStreamingContext,
        videoDateTaken: Long,
        frameRate: Int,
        bitRate: Long,
        codecFormat: String?,
        isKeepHdr: Boolean
    ) {
        if (videoDateTaken <= 0) {
            return
        }

        val compileConfigurations = buildCompileConfigurations(videoDateTaken)

        var destFps = Rational(frameRate, 1)
        if (meicamVideoClip.fps < destFps) {
            destFps = meicamVideoClip.fps
        }
        compileConfigurations[NvsStreamingContext.COMPILE_FPS] = meicamVideoClip.getFpsRate(destFps)

        var destDataRate = bitRate
        if (meicamVideoClip.bitRate < destDataRate) {
            destDataRate = meicamVideoClip.bitRate
        }
        compileConfigurations[NvsStreamingContext.COMPILE_BITRATE] = destDataRate

        if (!codecFormat.isNullOrEmpty()) {
            compileConfigurations[NvsStreamingContext.COMPILE_VIDEO_ENCODER_NAME] = codecFormat
        }

        if (!isKeepHdr) {
            compileConfigurations.remove(COMPILE_VIDEO_HDR_COLOR_TRANSFER)
        }

        // 配置视频bit位数，10bit的需要设置编码器
        val videoStreamComponentBitDepth = meicamVideoClip.bitDepth
        if (videoStreamComponentBitDepth == COMPILE_ENCODER_BIT_DEPTH_10) {
            compileConfigurations[NvsStreamingContext.COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
        }
        compileConfigurations[NvsStreamingContext.COMPILE_ENCODER_BIT_DEPTH] = videoStreamComponentBitDepth.also {
            d(TAG, LogFlag.DL) { "[setCompileConfigurations] current file bit depth is $it" }
        }
        nvsContext.compileConfigurations = compileConfigurations
    }

    private fun getMediaProviderPackageName(context: Context): String {
        val time = System.currentTimeMillis()
        val packageNames = arrayOf(ANDROID_MEDIA_PROVIDER_NAME, GOOGLE_MEDIA_PROVIDER_NAME)
        var packageName = chooseByPackageIfExist(context, packageNames)
        if (TextUtils.isEmpty(packageName)) {
            packageName = GOOGLE_MEDIA_PROVIDER_NAME
        }
        GLog.d(TAG, LogFlag.DL, "getMediaProviderPackageName packageName =  $packageName, cost time = ${System.currentTimeMillis() - time}")
        return packageName
    }

    /**
     * 配置视频编码时使用的compile configuration参数，可选项：使用的视频编码器、颜色转换函数、media codec编码提速、
     * CREATION时间
     *
     * @param videoDateTaken 对应视频的CREATION_TIME信息
     * @return 包含各种compile configuration参数的Hashmap
     */
    private fun buildCompileConfigurations(videoDateTaken: Long): Hashtable<String?, Any?> {
        val compileConfigurations: Hashtable<String?, Any?> = Hashtable<String?, Any?>()
        val isTypeSupportDolbyEdit = VideoEditorHelper.isTypeSupportDolbyEdit(meicamVideoClip.videoType)
        val isTypeSupportHlgEdit = VideoEditorHelper.isTypeSupportHlgEdit(meicamVideoClip.videoType)
        if (isTypeSupportDolbyEdit || isTypeSupportHlgEdit) {
            compileConfigurations[NvsStreamingContext.COMPILE_VIDEO_ENCODER_NAME] = COMPILE_VIDEO_ENCODER_VALUE
        }
        var compileVideoColorTransferValue = ""
        if (isTypeSupportDolbyEdit) {
            compileVideoColorTransferValue = COMPILE_VIDEO_DOLBY_COLOR_TRANSFER_VALUE

            if (!isSupportDolbyEncodeAccelerate) {
                //配置由MediaCodec自行选择处理速率；参照美摄SDK的信息，对编码时间的影响最大增加1/3
                compileConfigurations[NvsStreamingContext.COMPILE_USE_OPERATING_RATE] = false
            }
        } else if (isTypeSupportHlgEdit) {
            compileVideoColorTransferValue = COMPILE_VIDEO_HLG_COLOR_TRANSFER_VALUE
        } else {
            val isSupportUseOperateRate = getBoolean(FEATURE_IS_SUPPORT_USE_OPERATE_RATE)
            if (isSupportUseOperateRate) {
                compileConfigurations[NvsStreamingContext.COMPILE_USE_OPERATING_RATE] = true
            }
            GLog.d(TAG, LogFlag.DL, "buildCompileConfigurations COMPILE_USE_OPERATING_RATE set $isSupportUseOperateRate")
        }
        if (!TextUtils.isEmpty(compileVideoColorTransferValue)) {
            compileConfigurations[COMPILE_VIDEO_HDR_COLOR_TRANSFER] = compileVideoColorTransferValue
        }
        compileConfigurations[NvsStreamingContext.COMPILE_CREATION_TIME] = TimeUtils.getFileDateTakenFormat(videoDateTaken)

        val videoFps = meicamVideoClip.fps.toFloat()
        if (videoFps > FPS_120_MIN_AVERAGE_FPS_THRESHOLD) {
            // 对于120fp的视频，每帧的编码要等待12ms。这样会降低导出功耗和温升幅度，当然也会延长导出的时间。
            GLog.d(TAG, LogFlag.DL) { "buildCompileConfigurations highFps video, fps=$videoFps. compile will be delay for each frame." }
            compileConfigurations[COMPILE_DELAY_TIME_IN_MS_PER_FRMAE] = HIGH_FPS_VIDEO_COMPILE_FRAME_DELAY_TIME_MS
        }

        return compileConfigurations
    }

    private val isSupportDolbyEncodeAccelerate: Boolean
        get() {
            val configAbility = (context as GalleryApplication).getAppAbility(
                IConfigAbility::class.java
            )
            if (configAbility != null) {
                //判断设备是否支持Dolby视频高速率编码；若设备MediaCodec性能不足以支持高速率的Dolby视频编码，让设备的MediaCodec自行选择处理速率
                val isSupportDolbyEncodeAccelerate = configAbility.getBooleanConfig(
                    ConfigID.Common.SystemInfo.IS_SUPPORT_DOLBY_ENCODE_ACCELERATE, true
                )
                configAbility.close()
                return isSupportDolbyEncodeAccelerate ?: false
            }
            return false
        }

    companion object {
        const val MILLIS_TIME_BASE: Int = 1000 //base on micro time
        private const val COMPILE_VIDEO_HEIGHT_DEFAULT = 1280 // means mp4 video is 720 * 1280
        private const val VIDEO_SEEK_OFFSET_FOR_DURATION = 1 // 0.001ms
        private const val VIDEO_WIDTH_DIV_ZERO_VALUE = 4
        private const val VIDEO_HEIGHT_DIV_ZERO_VALUE = 2


        private const val RGB_R_OFFSET = 16
        private const val RGB_G_OFFSET = 8
        private const val RGB_BIT_WIDTH = 0xFF

        private const val COMPILE_ENCODER_BIT_DEPTH_10 = 10

        /**
         * 设置在SDR向HDR转换时候色彩增益,色彩增益的范围是【1.0，10.0】
         */
        private const val COLOR_GAINT_FOR_SDR_TO_HDR_VALUE = 3.0f
        private const val TAG = "MeicamVideoEngine"
        private const val GOOGLE_MEDIA_PROVIDER_NAME = "com.google.android.providers.media.module"
        private const val ANDROID_MEDIA_PROVIDER_NAME = "com.android.providers.media.module"

        /**
         * mark by gaojinxian，目前美摄SDK没有提供相应常量，后续配合他们调整，应该由他们提供常量
         */
        private const val COMPILE_VIDEO_ENCODER_VALUE = "hevc"
        private const val COMPILE_VIDEO_DOLBY_COLOR_TRANSFER_VALUE = "hlg dolby vision"
        private const val COMPILE_VIDEO_HLG_COLOR_TRANSFER_VALUE = "hlg"
        private const val COMPILE_VIDEO_HDR10_COLOR_TRANSFER_VALUE = "st2084"
        private const val COMPILE_VIDEO_HDR10PLUS_COLOR_TRANSFER_VALUE = "hdr10plus"
        private const val COMPILE_VIDEO_COLOR_PRIMARIES_VALUE = "display p3"
        private const val GRAB_DELAY_TIME_MS = 20L
        private const val HIGH_FPS_VIDEO_COMPILE_FRAME_DELAY_TIME_MS = 12L
    }
}
