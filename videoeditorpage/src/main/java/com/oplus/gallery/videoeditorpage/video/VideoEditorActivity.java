/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - VideoEditorActivity.java
 * * Description: VideoEditor main activity.
 * * Version: 1.0
 * * Date : 2017/10/31
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *  <EMAIL>    2017/10/31    1.0    build this module
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.video;

import static android.provider.BaseColumns._ID;
import static com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_TYPE_TAG;
import static com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_IS_CANCEL_FROM_EDIT;
import static com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_PHOTO_POSITION_FOR_EDITOR;
import static com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_TRANSITION_THUMBNAIL;
import static com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CHAIN_FROM;
import static com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_EDIT_THUMBNAIL;
import static com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_RECT;
import static com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.VALUE_CHAIN_FROM_CAMERA;
import static com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.VIDEO_EDITOR_ACTIVITY;
import static com.oplus.gallery.business_lib.menuoperation.EditVideoAction.KEY_MEDIA_ITEM_PATH;
import static com.oplus.gallery.business_lib.menuoperation.EditVideoAction.VALUE_INVOKER_WALLPAPER_PAGE;
import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL;
import static com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper.SCAN_FILE_TIME_OUT;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_10BIT_DECODE;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SUPPORT_HLG_ENCODE;
import static com.oplus.gallery.framework.abilities.hardware.IScreen.DEFAULT_WINDOW_REFRESH_RATE;
import static com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_MODE_INCR;
import static com.oplus.gallery.standard_lib.app.AppConstants.CloudSyncConstants.SYNC_TYPE_EDIT_VIDEO;
import static com.oplus.gallery.videoeditorpage.video.helper.WallpaperHelper.ACTION_SET_VIDEO_WALLPAPER;
import static com.oplus.gallery.videoeditorpage.video.helper.WallpaperHelper.KEY_IS_VIDEO_WALLPAPER;
import static com.oplus.gallery.videoeditorpage.video.helper.WallpaperHelper.SET_VIDEO_WALLPAPER_REQUEST_CODE;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.FileObserver;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Rational;
import android.util.Size;
import android.view.KeyEvent;
import android.view.SurfaceView;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import androidx.core.graphics.Insets;
import androidx.core.view.WindowInsetsCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.meicam.sdk.NvsStreamingContext;
import com.oplus.gallery.basebiz.constants.IntentConstant;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.menuoperation.EditVideoAction;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.LocalVideo;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItemExtKt;
import com.oplus.gallery.business_lib.model.data.base.utils.VideoTypeParser;
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper;
import com.oplus.gallery.business_lib.seniorpicked.SeniorPickedScoreShare;
import com.oplus.gallery.business_lib.template.editor.MeicamEngineLimiter;
import com.oplus.gallery.business_lib.util.external.SoloopConfig;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.MediaStoreUtils;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.taskscheduling.ext.CoroutineScopeExtKt;
import com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value;
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog;
import com.oplus.gallery.foundation.ui.dialog.LoadingHelper;
import com.oplus.gallery.foundation.ui.dialog.NoTitleLoadingHelper;
import com.oplus.gallery.foundation.ui.dialog.ProgressDialog;
import com.oplus.gallery.foundation.ui.systembar.ActivitySystemBarStyle;
import com.oplus.gallery.foundation.ui.systembar.ImmersiveActivitySystemBarStyle;
import com.oplus.gallery.foundation.ui.systembar.SystemBarControllerKt;
import com.oplus.gallery.foundation.ui.transition.ITransitionBoundsProvider;
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.foundation.util.ext.ActivityInfoExtKt;
import com.oplus.gallery.foundation.util.ext.StringExtKt;
import com.oplus.gallery.foundation.util.ext.UriExtKt;
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper;
import com.oplus.gallery.foundation.util.multiprocess.ITransBitmap;
import com.oplus.gallery.foundation.util.multiprocess.TransBitmapBinder;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.foundation.util.systemcore.ContentObserverManager;
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver;
import com.oplus.gallery.foundation.util.systemcore.IntentUtils;
import com.oplus.gallery.foundation.util.thread.MainSwitchHandler;
import com.oplus.gallery.framework.abilities.hardware.IHardwareAbility;
import com.oplus.gallery.framework.abilities.hardware.IScreen;
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.router_lib.annotations.RouterNormal;
import com.oplus.gallery.standard_lib.app.AppConstants;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.codec.SlowMotionVideoUtils;
import com.oplus.gallery.standard_lib.codec.player.adapter.PlayerAdapter;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;
import com.oplus.gallery.videoeditor.data.VideoSpec;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.VideoBaseActivity;
import com.oplus.gallery.videoeditorpage.base.ui.ControlBarView;
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.base.util.VideoStorageHelper;
import com.oplus.gallery.videoeditorpage.base.util.VideoTrackHelper;
import com.oplus.gallery.videoeditorpage.engine.GalleryEditorVideoView;
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoEngineListener;
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoEngineManager;
import com.oplus.gallery.videoeditorpage.resource.manager.FilterResourceManager;
import com.oplus.gallery.videoeditorpage.resource.manager.FxResourceManager;
import com.oplus.gallery.videoeditorpage.resource.manager.SongResourceManager;
import com.oplus.gallery.videoeditorpage.resource.manager.TemplateResourceManager;
import com.oplus.gallery.videoeditorpage.video.app.EditorExportOliveState;
import com.oplus.gallery.videoeditorpage.video.app.EditorSubTitleState;
import com.oplus.gallery.videoeditorpage.video.data.WallPaperParam;
import com.oplus.gallery.videoeditorpage.video.helper.WallpaperHelper;
import com.oplus.gallery.videoeditorpage.video.invoker.ExportOliveInvoker;
import com.oplus.gallery.videoeditorpage.video.invoker.InvokerManager;
import com.oplus.gallery.videoeditorpage.video.ui.VideoSubTitleEditTextView;
import com.oplus.gallery.videoeditorpage.video.ui.brighten.EditorVideoBrightenManager;
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorFilterViewModel;
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorFxViewModel;
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorInitViewModel;
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorSongViewModel;
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorTemplateViewModel;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;

import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

@RouterNormal(path = VIDEO_EDITOR_ACTIVITY)
public class VideoEditorActivity extends VideoBaseActivity implements MeicamEngineLimiter.LimitAble {

    public static final String START_FROM_VIDEO_LIST = "start_from_video_list";
    public static final String KEY_VIDEO_EDITOR_TEMP_SAVE_NAME_PREF = "key_video_editor_temp_save_name_pref";
    public static final String KEY_VIDEO_EDITOR_TEMP_SAVE_DIR_PREF = "key_video_editor_temp_save_dir_pref";
    public static final String KEY_VIDEO_EDITOR_TEMP_SAVE_URI_PREF = "key_video_editor_temp_save_uri_pref";
    public static final String KEY_PHOTO_EDITOR_INVOKER = "invoker";
    public static final int REQUEST_CODE_TAKE_MUSIC = 100;
    public static final int VIDEO_PLAY_END_CHECK_GAP = 100;
    /**
     * in slow motion mode, we should use 30fps to maintain the exact division by fps 120 or 240
     */
    public static final int SLOW_MOTION_DEFAULT_FPS = 30;
    public static final int REQUEST_CODE_START_SOLOOP = 1001;
    private static final String TAG = "VideoEditorActivity";
    private static final String SETTING_KEY_MANUAL_HIDE_NAVIGATION_BAR = "manual_hide_navigationbar";
    private static final String KEY_NEW_SAVED_VIDEO_URI = "videoUri";
    private static final int MANUAL_CANCEL_FINISH = 0;
    private static final int MANUAL_CANCELING = 1;

    private static final int NUM_0 = 0;
    private static final int NUM_1 = 1;
    private static final int NUM_2 = 2;
    private static final int NUM_3 = 3;
    private static final int NUM_4 = 4;
    private static final int NUM_5 = 5;
    private static final int NUM_6 = 6;
    private static final int NUM_7 = 7;
    private static final int NUM_8 = 8;
    private static final int NUM_9 = 9;
    private static final int NUM_10 = 10;
    /**
     * slow motion title example: 0slow_motion_hsr_120:12,220,300,900 or op**_0slow_motion_hsr_120:12,220,300,900
     * 对标题使用":"分割，对分割后的前一个字符串使用"_"分割，有”op**_“前缀时得到5个字符串，没有”op**_“前缀时得到4个
     */
    private static final int SLOW_MOTION_FPS_FOUR_PARTS = 4;
    private static final int SLOW_MOTION_FPS_FIVE_PARTS = 5;
    private static final int COMPILE_VIDEO_MAX = 100;
    private static final int SUPPORT_SLOW_MOTION_FPS_120 = 120;
    private static final int SUPPORT_SLOW_MOTION_FPS_240 = 240;
    private static final int SUPPORT_SLOW_MOTION_FPS_480 = 480;
    private static final int VIDEO_PREVIEW_MAX_HEIGHT = 720;
    private static final long ENSURE_SPACE_MB = 100L * 1024 * 1024;
    private static final int VIDEO_FRAME_RATE_HIGH = 120;
    private static final String[] BASE_INFO_PROJECTION = new String[]{
        GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
        MediaStoreUtils.DATA,
        GalleryStore.GalleryColumns.LocalColumns.DURATION,
        GalleryStore.GalleryColumns.LocalColumns.TITLE,
        GalleryStore.GalleryColumns.LocalColumns.WIDTH,
        GalleryStore.GalleryColumns.LocalColumns.HEIGHT,
        GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE,
        GalleryStore.GalleryColumns.LocalColumns.SIZE,
        GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN,
        GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME,
        GalleryStore.GalleryColumns.LocalColumns.CODEC_TYPE
    };
    private final Handler mCompileDialogHandler = new Handler();
    private int mVideoWidth = 0;
    private int mVideoHeight = 0;
    private int mScreenHeightDp = 0;
    private float mVideoFps = 0.0f;
    private int mSlowMotionFps = SLOW_MOTION_DEFAULT_FPS;
    private long mVideoDuration;
    private long mSlowAEnter = 0;
    private long mSlowAOut = 0;
    private long mSlowBEnter = 0;
    private long mSlowBOut = 0;
    private double mLatitude = 0;
    private double mLongitude = 0;
    private boolean mIsFullSlow = false;
    private boolean mCheckSupported = false;
    private boolean mSupportSlowMotion = false;
    private boolean mIsHfrSlowMotion = false;
    private boolean mIsHsrSlowMotion = false;
    private boolean mIsInit = false;

    private File mCurSaveDir;
    private String mVideoPath;
    private String mVideoUri;
    private MediaItem mVideoMediaItem;
    private String mVideoCodecType;
    private String mVideoTitle;
    private String mDisplayName;
    private String mMimeType;
    private String mInvoker;
    private String mModelType;
    private String mColorTransfer;
    private int mVideoType;
    private long mSize;
    private long mDateTaken;
    private long mLastDateModified;
    private int mWidth;
    private int mHeight;
    private IVideoEditAbility mGalleryVideoEngine;
    private GalleryEditorVideoView mGalleryEditorVideoView;
    private ControlBarView mControlBar;
    private VideoSubTitleEditTextView mVideoSubTitleEditTextView;
    private ConfirmDialog mCancelDialog;
    private ConfirmDialog mNotSupportDialog;
    private ProgressDialog mCompileDialog;
    private String mVideoFileName;
    private EditorHandler mEditorHandler;
    private SubThreadHandler mEditorSubThreadHandler;
    private HandlerThread mEditorHandlerThread;
    private VideoFileObserver mVideoFileObserver;
    private String mStartExtra;
    private Uri mNavigationBarUri;
    private Uri mMediaUri = null;
    private String mMediaId;
    private long mStartSaveTime = 0;

    /**
     * 编辑类型
     */
    private String mEditorTypeTag;
    private InvokerManager mInvokerManager;

    /**
     * 来自哪条进相册的链路
     */
    private String mChainFrom = null;
    /**
     * 是否能操作亮度一致性
     * <p>
     * 当弹出“选择音频”等页面时，可以设置不能操作，以便页面保持亮度
     */
    private boolean mCanOperateBrightenUniformity = true;
    /**
     * 手动取消
     */
    private int mManualCancelState = MANUAL_CANCEL_FINISH;
    private WallPaperParam mWallPaperParam;

    /**
     * 大图进编辑位置规则封装
     */
    private ITransitionBoundsProvider mPhotoPositionForEditor = null;

    /**
     * 视频提亮
     */
    private EditorVideoBrightenManager mVideoBrightenManager;

    /**
     * 屏幕刷新率更新器
     */
    private IRefreshRateUpdater mRefreshRateUpdater;

    private VideoTypeParser mVideoTypeParser = new VideoTypeParser();
    private Bitmap mThumbnail;
    private LoadingHelper mLoadingHelper;
    private BaseActivity.OnUserInteractionListener mOnUserInteractionListener = new BaseActivity.OnUserInteractionListener() {
        @Override
        public void onUserInteraction(@NonNull InteractionType type) {
            if (mVideoBrightenManager != null) {
                mVideoBrightenManager.handlerUserInteraction(type);
            }
        }
    };
    private BroadcastReceiver mSDCardReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            GLog.d(TAG, "onReceive, action = " + action);
            if (Intent.ACTION_MEDIA_UNMOUNTED.equals(action)
                || Intent.ACTION_MEDIA_REMOVED.equals(action)) {
                if (isMediaInSDCard()) {
                    finish();
                    return;
                }
                if (isSavingInSDCard()) {
                    finish();
                }
            }
        }
    };
    private ControlBarView.OnCancelDoneClickListener mOnCancelDoneClickListener = new ControlBarView.OnCancelDoneClickListener() {

        @Override
        public void onCancelClick(View view) {
            if (mControlBar != null) {
                VideoTrackHelper.trackClickVideoCancelMenuUserAction(Value.CANCEL, null);
            }
            GLog.d(TAG, "onCancelClick()");
            if (mWallPaperParam != null) {
                VideoTrackHelper.cleanOperation();
                setResult(RESULT_CANCELED);
                finish();
                return;
            }
            if (!mGalleryVideoEngine.hasVideoChanged()) {
                VideoTrackHelper.cleanOperation();
                finish();
            } else {
                showCancelDialog(view);
            }
        }

        @Override
        public void onDoneClick() {
            if ((mWallPaperParam == null) && !mGalleryVideoEngine.hasVideoChanged()) {
                return;
            }
            if ((mCompileDialog != null) && mCompileDialog.isShowing()) {
                return;
            }
            GLog.d(TAG, "onDoneClick");
            mGalleryVideoEngine.pause();

            if (mWallPaperParam != null) {
                saveWallpaperVideo();
                return;
            }

            VideoStorageHelper.deleteOldVideoFile(VideoEditorActivity.this, VideoStorageHelper.VideoType.VIDEO_EDITOR);
            String relativePath = FilePathUtils.getRelativePath(mVideoPath);
            int tipRes = R.string.videoeditor_video_editor_disk_space_not_enough;
            mCurSaveDir = VideoStorageHelper.checkStorageEnough(VideoEditorActivity.this,
                VideoStorageHelper.VideoType.VIDEO_EDITOR, ENSURE_SPACE_MB, relativePath, tipRes);
            if (mCurSaveDir == null) {
                GLog.w(TAG, "onDoneClick storage not enough");
                return;
            }
            mStartSaveTime = System.currentTimeMillis();
            showCompileDialog();
            mVideoFileName = VideoEditorHelper.getDefaultVideoSaveName();
            VideoEditorHelper.setStringPref(VideoEditorActivity.this, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_NAME_PREF, mVideoFileName);
            File file = new File(mCurSaveDir, mVideoFileName);
            file = FilePathUtils.switchToPublicDir(file, false);
            VideoEditorHelper.setStringPref(VideoEditorActivity.this, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_DIR_PREF, file.getParent());
            GLog.d(TAG, "onDoneClick, mVideoFileName = " + mVideoFileName
                + ", mSupportSlowMotion:" + mSupportSlowMotion
                + ", mIsHfrSlowMotion:" + mIsHfrSlowMotion
                + ", file = " + file);
            //saveVideo方法将mDateTaken作为creation_time字段值写入视频的metadata里面
            mGalleryVideoEngine.saveVideo(file.getAbsolutePath(), mVideoHeight, mDateTaken, mVideoPath);
        }
    };

    private GalleryVideoEngineListener mGalleryVideoEngineListener = new GalleryVideoEngineListener() {

        @Override
        public void onPlayStatusChange(int status) {
            GLog.d(TAG, LogFlag.DL,
                    "GalleryVideoEngineListener onPlayStatusChange()  mControlBar.getCurrentEditor():"
                            + ((mControlBar.getCurrentEditor() != null) ? (mControlBar.getCurrentEditor().getCurrentTAG()) : "null")
                            + " status=" + status
            );
            mEditorHandler.removeMessages(EditorHandler.MSG_PLAY_STATUS_CHANGE);
            mEditorHandler.sendEmptyMessageDelayed(EditorHandler.MSG_PLAY_STATUS_CHANGE, EditorHandler.STATUS_CHANGE_REFRESH_DELAY);
            if (status == GalleryVideoEngineManager.PLAY_STATUS_FINISH) {
                if (mControlBar.getCurrentEditor() != null) {
                    mControlBar.getCurrentEditor().onPlayFinish();
                    mControlBar.getCurrentEditor().onResetPlay();
                }
            }
            if (mInvokerManager != null) {
                mInvokerManager.onPlayStatus(status);
            }
            if (mVideoBrightenManager != null) {
                mVideoBrightenManager.handlerPlayerStatus(status);
            }
        }

        @Override
        public void onPlayPositionChange(long position) {
            GLog.d(TAG, LogFlag.DL, "GalleryVideoEngineListener onPlayPositionChange position=" + position);
            if (mControlBar.getCurrentEditor() != null) {
                mControlBar.getCurrentEditor().onPlayPositionChange();
            }
        }

        @Override
        public void onExportStatusChange(int state) {
            GLog.d(TAG, LogFlag.DL, "GalleryVideoEngineListener onExportStatusChange state=" + state);
            switch (state) {
                case GalleryVideoEngineManager.EXPORT_STATUS_ERROR:
                    if (mCompileDialog.isShowing()) {
                        mCompileDialog.dismiss();
                    }
                    String relativePath = FilePathUtils.getRelativePath(mVideoPath);
                    int tipRes = R.string.videoeditor_video_editor_disk_space_not_enough;
                    if (null != VideoStorageHelper.checkStorageEnough(VideoEditorActivity.this,
                        VideoStorageHelper.VideoType.VIDEO_EDITOR, ENSURE_SPACE_MB, relativePath, tipRes)) {
                        ToastUtil.showShortToast(R.string.videoeditor_editor_cancel_compile_space_not_enough);
                    } else {
                        ToastUtil.showShortToast(R.string.videoeditor_editor_cancel_compile_error);
                    }
                    VideoTrackHelper.saveFinishOperation(false,
                        mCurSaveDir + mVideoFileName,
                        false,
                        System.currentTimeMillis() - mStartSaveTime);
                    break;
                case GalleryVideoEngineManager.EXPORT_STATUS_COMPLETE:
                    BuildersKt.launch(AppScope.INSTANCE, Dispatchers.getIO(), CoroutineStart.DEFAULT,
                        (coroutineScope, continuation) -> {
                            if (mManualCancelState == MANUAL_CANCELING) {
                                GLog.d(TAG, "onExportStatusChange manual cancel");
                                onExportCancel();
                                return null;
                            }
                            onExportFinish();
                            return null;
                        });
                    break;
                case GalleryVideoEngineManager.EXPORT_STATUS_CANCEL:
                    onExportCancel();
                    break;
                default:
                    break;
            }
        }

        @WorkerThread
        private void onExportFinish() {
            GLog.d(TAG, LogFlag.DL, "GalleryVideoEngineListener onExportFinish");
            Uri uri = null;
            Bitmap transBitmap = null;
            if (mWallPaperParam != null) {
                uri = GalleryFileProvider.fromFile(getApplicationContext(), new File(mCurSaveDir, mVideoFileName));
            } else {
                if (updateSavedFile()) {
                    uri = mMediaUri;
                    transBitmap = VideoStorageHelper.decodeTransitionBitmap(VideoEditorActivity.this, uri);
                    showShortToast(R.string.videoeditor_editor_cancel_compile_success);
                    /* AlbumSyncUtil.notifySyncGalleryDB(VideoEditorActivity.this,
                     * AlbumSyncUtil.SYNC_DATA_INCR, AlbumSyncUtil.SYNC_TYPE_EDIT_VIDEO);
                     */
                } else {
                    showShortToast(R.string.videoeditor_editor_cancel_compile_error);
                    return;
                }
            }

            if (uri != null) {
                new SeniorPickedScoreShare().asyncSetAsSeniorPickedData(mModelType, mVideoPath, uri);
                ApiDmManager.getCloudSyncDM().triggerCloudSync(SYNC_MODE_INCR, SYNC_TYPE_EDIT_VIDEO);
            }
            VideoTrackHelper.saveFinishOperation(false,
                mCurSaveDir + mVideoFileName,
                true,
                System.currentTimeMillis() - mStartSaveTime);
            Uri finalUri = uri;
            final Bitmap finalTransBitmap = transBitmap;
            MainSwitchHandler.getInstance().post(() -> setResultAndFinish(finalUri, finalTransBitmap, false));
        }

        private void showShortToast(int id) {
            MainSwitchHandler.getInstance().post(() -> ToastUtil.showShortToast(id));
        }

        private boolean updateSavedFile() {
            if (TextUtils.isEmpty(mVideoFileName)) {
                GLog.w(TAG, "updateSavedFile mVideoFileName is empty!");
                return false;
            }
            final File mediaFile = new File(mCurSaveDir, mVideoFileName);
            if (!mediaFile.exists()) {
                GLog.e(TAG, "updateSavedFile failed. the file does not exists, mediaFile = " + mediaFile);
                return false;
            }
            // 必须在update is_pending前修改，因为改is_pending能够触发媒体库重扫改记录，直接修改date_modified会无效
            mediaFile.setLastModified(mLastDateModified);
            // 美摄sdk写文件是通过fuse，先插入媒体库记录，再通过fuse写文件时会产生另外的新记录，之后如果再更新媒体库记录就会因为冲突把记录删除，需要媒体库扫描
            mMediaUri = MediaStoreScannerHelper.scanFileByMediaStoreSingle(
                getApplicationContext(),
                mediaFile.getAbsolutePath(),
                SCAN_FILE_TIME_OUT);
            if (mMediaUri != null) {
                VideoEditorHelper.insertVideo(mediaFile, mVideoFileName, mDateTaken, mMediaUri);
                VideoEditorHelper.setStringPref(VideoEditorActivity.this, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_NAME_PREF, null);
                VideoEditorHelper.setStringPref(VideoEditorActivity.this, VideoEditorHelper.KEY_VIDEO_EDITOR_TEMP_SAVE_DIR_PREF, null);
                return true;
            }
            GLog.e(TAG, "updateSavedFile failed");
            return false;
        }

        private void onExportCancel() {
            runOnUiThread(() -> {
                mManualCancelState = MANUAL_CANCEL_FINISH;
                ToastUtil.showShortToast(R.string.videoeditor_editor_cancel_compile_cancel);
                if (mCompileDialog != null) {
                    mCompileDialog.dismiss();
                }
            });
        }

        @Override
        public void onExportProgressChange(int progress) {
            GLog.d(TAG, LogFlag.DL, "GalleryVideoEngineListener onExportProgressChange progress=" + progress);
            setDialogProgress(progress);
        }

        @Override
        public void onEngineStateChanged(int state) {
            GLog.d(TAG, LogFlag.DL, "GalleryVideoEngineListener onEngineStateChanged");
        }

        @Override
        public void onFirstVideoFrameReady() {
            GLog.d(TAG, LogFlag.DL, "GalleryVideoEngineListener onFirstVideoFrameReady");
        }

        @Override
        public void onEngineException(int errCode, String msg) {
            GLog.e(TAG, LogFlag.DL, "GalleryVideoEngineListener onEngineException errorCode=" + errCode + " msg=" + msg);
            if (!TextUtils.isEmpty(mVideoPath) && (errCode != NvsStreamingContext.STREAMING_ENGINE_PLAYBACK_EXCEPTION_TYPE_HARDWARE_CODEC_ERROR)) {
                File file = new File(mVideoPath);
                if (file.exists()) {
                    mEditorHandler.removeMessages(EditorHandler.MSG_EDITOR_NOT_SUPPORTED);
                    mEditorHandler.sendEmptyMessageDelayed(EditorHandler.MSG_EDITOR_NOT_SUPPORTED,
                        EditorHandler.EDITOR_NOT_SUPPORTED_DELAY);
                }
            }
        }
    };


    private HandlerContentObserver mNavigationStatusObserver = new HandlerContentObserver(new Handler()) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            Message msg = mEditorHandler.obtainMessage();
            msg.what = EditorHandler.MSG_REFRESH_WINDOW;
            msg.arg1 = EditorHandler.REFRESH_NOT_NEED_SEEK;
            mEditorHandler.removeMessages(EditorHandler.MSG_REFRESH_WINDOW);
            mEditorHandler.sendMessageDelayed(msg, EditorHandler.REFRESH_WINDOW_DELAY);
        }
    };

    @Override
    public void onCreateCheck() {
        GLog.d(TAG, "onCreateCheck()");
        Intent intent = getIntent();
        setPreviewThumbnailFirst(intent);
        mStartExtra = IntentUtils.getStringExtra(intent, START_FROM_VIDEO_LIST);
        mChainFrom = IntentUtils.getStringExtra(intent, KEY_CHAIN_FROM);
        mEditorHandler = new EditorHandler();
        mEditorHandlerThread = new HandlerThread("EditorCheckHandlerThread");
        mEditorHandlerThread.start();
        mEditorSubThreadHandler = new SubThreadHandler(mEditorHandlerThread.getLooper(), this);
        // 如果美摄不支持软解视频,可能在appendClip消耗大量的时间阻塞主线程,所以软解提前判断
        mEditorSubThreadHandler.sendEmptyMessage(SubThreadHandler.MSG_CHECK_SOFT_DECODE);
        mInvoker = IntentUtils.getStringExtra(intent, KEY_PHOTO_EDITOR_INVOKER);
        mModelType = IntentUtils.getStringExtra(intent, EditVideoAction.KEY_MEDIA_MODEL_TYPE);
        mWallPaperParam = WallPaperParam.Companion.newInstance(intent, mInvoker);
        mPhotoPositionForEditor = IntentUtils.getParcelableExtra(intent, KEY_PHOTO_POSITION_FOR_EDITOR);
        mRefreshRateUpdater = new VideoEditorRefreshRateUpdater(getApplicationContext());
        mEditorTypeTag = IntentUtils.getStringExtra(intent, KEY_EDIT_TYPE_TAG);
        mInvokerManager = new InvokerManager(this, mGalleryVideoEngine, mInvoker);
        mInvokerManager.showCustomPage(R.id.base_fragment_container, getIntent().getExtras());
    }

    /**
     * 是否来自导出实况菜单
     * @return
     */
    private boolean isFromExportOliveMenu() {
        return mInvoker.equals(ExportOliveInvoker.VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE);
    }

    /**
     * 设置需要提亮的视频呈现视图
     * @param surfaceView
     */
    public void setVideoBrightenManager(SurfaceView surfaceView) {
        if (surfaceView != null && !VideoTypeParser.isSdrType(mVideoType)) {
            mVideoBrightenManager = new EditorVideoBrightenManager(surfaceView, mVideoType);
        }
    }

    /**
     *  进入页面时首先设置从大图传过来的bitmap给preview,防止子线程initEngine未及时执行导致的预览黑一下
     *
     * @param intent
     */
    private void setPreviewThumbnailFirst(Intent intent) {
        if (intent == null) {
            GLog.d(TAG, LogFlag.DL, "setPreviewThumbnailFirst, intent is null.");
            return;
        }
        Bundle extraData = intent.getExtras();
        if (extraData != null) {
            IBinder imageBinder = extraData.getBinder(KEY_EDIT_THUMBNAIL);
            try {
                ITransBitmap transBitmapBinder = ITransBitmap.Stub.asInterface(imageBinder);
                if (transBitmapBinder != null) {
                    mThumbnail = transBitmapBinder.getBitmap();
                    if ((mThumbnail != null) && (mGalleryEditorVideoView != null)) {
                        mGalleryEditorVideoView.setBackgroundDrawable(new BitmapDrawable(getResources(), mThumbnail));
                    }
                }
            } catch (RemoteException e) {
                GLog.e(TAG, LogFlag.DL, "setPreviewThumbnailFirst get thumbnail fail, exception: " + e);
            }
        }
    }

    private void init(String startExtra) {
        if (mIsInit) {
            return;
        }
        if (isFinishing()) {
            GLog.d(TAG, LogFlag.DL, "init, activity is finishing.");
            return;
        }
        if (mControlBar == null) {
            GLog.d(TAG, LogFlag.DL, "init, mControlBar is null.");
            return;
        }
        mLoadingHelper = new NoTitleLoadingHelper(this, null, Resources.ID_NULL);
        mLoadingHelper.setOnKeyListener((dialog, keyCode, event) -> {
            if ((keyCode == KeyEvent.KEYCODE_BACK) && (event.getAction() == KeyEvent.ACTION_UP)) {
                dialog.cancel();
                VideoEditorActivity.this.finish();
                return true;
            }
            return false; // 如果不是返回键，则不处理
        });
        mLoadingHelper.showDialog(LoadingHelper.PROGRESS_DIALOG_HIDE_DELAY_TIME_500_MS);
        CoroutineScopeExtKt.launch(AppScope.INSTANCE, Dispatchers.getIO(), CoroutineStart.DEFAULT, jc -> {
            handlerVideoTypeParser();
            final boolean initResult = tryInitVideoEngineInfo();
            mLoadingHelper.hideDialog(0); //返回初始化结果就取消加载框
            runOnUiThread(() -> {
                initVideoEditorInfo(startExtra, initResult);
                if (mGalleryEditorVideoView != null) {
                    mGalleryEditorVideoView.setBackgroundDrawable(null);
                }
            });
            return null;
        }, null);
    }

    private void initVideoEditorInfo(String startExtra, boolean initVideoEngineInfoSuccess) {
        if ((!isFinishing()) && initVideoEngineInfoSuccess && initEngine() && (mControlBar != null) && initVideoClip()) {
            // 通知实况导出fragment引擎初始化完成
            mInvokerManager.onInitEngineFinish();
            mIsInit = true;
            // enterInitEditor must run after updateVideo for checking slow motion video
            installSubTitleStyleRes();
            if (mWallPaperParam != null) {
                mControlBar.enterCustomTrimEditor(
                        mGalleryVideoEngine,
                        mWallPaperParam.getVideoTrimMinDuration(),
                        mWallPaperParam.getVideoTrimMaxDuration()
                );
            } else {
                mControlBar.enterInitEditor(mGalleryVideoEngine, mVideoSubTitleEditTextView);
            }
            initFileObserver();
            String from = TextUtils.isEmpty(startExtra) ? Value.FROM_GALLERY : Value.FROM_EXTERNAL;
            VideoTrackHelper.setVideoInfo(from,
                mDisplayName,
                mMediaId,
                mVideoPath,
                mVideoCodecType,
                mVideoDuration,
                mDateTaken,
                mSize,
                mMimeType,
                mWidth,
                mHeight,
                mVideoFps,
                mVideoUri,
                mVideoMediaItem
            );
            // 记录默认没有剪辑效果操作
            VideoTrackHelper.saveTrimOperation(Value.RESULT_CLOSE, String.valueOf(mGalleryVideoEngine.getTotalTime()));
            VideoTrackHelper.saveSpeederOperation(String.valueOf(mGalleryVideoEngine.getVideoSpeed()));
            mNavigationBarUri = Settings.Secure.getUriFor(SETTING_KEY_MANUAL_HIDE_NAVIGATION_BAR);
            if (mNavigationBarUri != null) {
                ContentObserverManager.registerContentObserver(this, mNavigationBarUri,
                        false, mNavigationStatusObserver);
            }
            SurfaceView surfaceView = mGalleryEditorVideoView.getSurfaceView();
            if (surfaceView != null && !VideoTypeParser.isSdrType(mVideoType) && !isFromExportOliveMenu()) {
                mVideoBrightenManager = new EditorVideoBrightenManager(surfaceView, mVideoType);
            }
            registerSDCardBroadcast();
        } else {
            notSupportEdit();
        }
    }

    private void notSupportEdit() {
        GLog.d(TAG, "notSupportEdit isFinishing() " + isFinishing());
        if (!isFinishing()) {
            if (mWallPaperParam != null) {
                showNotSupportDialog();
            } else {
                ToastUtil.showShortToast(R.string.videoeditor_not_support_error);
                finish();
            }
        }
    }

    private void installSubTitleStyleRes() {
        mGalleryVideoEngine.installSubTitleStyleRes();
    }

    private boolean initVideoClip() {
        boolean result = false;
        if (mSupportSlowMotion) {
            if (mIsHfrSlowMotion) {
                result = mGalleryVideoEngine.addHfrFullSlowMotionVideoClip(mVideoUri, mVideoPath, mDateTaken, mSlowMotionFps, SLOW_MOTION_DEFAULT_FPS);
            } else if (mIsHsrSlowMotion) {
                if (mSlowBEnter > 0) {
                    result = mGalleryVideoEngine.addSlowMotionVideoClip(mVideoUri, mVideoPath, mDateTaken, mSlowMotionFps,
                        new long[]{mSlowAEnter, mSlowAOut, mSlowBEnter, mSlowBOut}, mIsFullSlow);
                } else {
                    result = mGalleryVideoEngine.addSlowMotionVideoClip(mVideoUri, mVideoPath, mDateTaken, mSlowMotionFps,
                        new long[]{mSlowAEnter, mSlowAOut}, mIsFullSlow);
                }
            }
        } else {
            result = mGalleryVideoEngine.addVideoClip(mVideoUri, mVideoPath, mDateTaken);
        }
        return result;
    }

    @NotNull
    @Override
    public ActivitySystemBarStyle getSystemBarStyle() {
        return new ImmersiveActivitySystemBarStyle(this) {
            @Override
            public void onUpdate(@NotNull WindowInsetsCompat windowInsets) {
                super.onUpdate(windowInsets);
                setStatusBarColor(Color.TRANSPARENT);
                setNaviBarColor(Color.TRANSPARENT);
                setStatusBarAppearance(false);
                Insets naviInsets = SystemBarControllerKt.naviBarInsets(windowInsets, true);
                int bottomPadding = naviInsets.bottom;
                setContentPadding(naviInsets.left, naviInsets.top, naviInsets.right, bottomPadding);
            }
        };
    }

    @Override
    public void loadMain() {
        MeicamEngineLimiter.getInstance().register(this);
        mGalleryVideoEngine = new GalleryVideoEngineManager(VideoEditorActivity.this, mGalleryVideoEngineListener);
        setupScreenOrientationIfNeeded();
        setContentView(R.layout.videoeditor_video_editor_main_view);
        mControlBar = findViewById(R.id.main_control_view);
        mGalleryEditorVideoView = findViewById(R.id.video_engine_view);
        mVideoSubTitleEditTextView = findViewById(R.id.gallery_video_subtitle_text);
        mGalleryEditorVideoView.setGalleryVideoSubTitleEditTextView(mVideoSubTitleEditTextView);
        mControlBar.setCancelDoneClickListener(mOnCancelDoneClickListener);
        mControlBar.setZoomWindowManager(getZoomWindowManager());
        mControlBar.setEditorVideoView(mGalleryEditorVideoView);
        addUserInteractionListener(mOnUserInteractionListener);
        initAppUiConfig();
        initResource();
    }

    private void setupScreenOrientationIfNeeded() {
        int expectedScreenOrientation = ActivityInfoExtKt.getValidScreenOrientationOrDefault(
            IntentUtils.getIntExtra(
                getIntent(),
                IntentConstant.ViewGalleryConstant.KEY_INIT_SCREEN_ORIENTATION,
                ActivityInfo.SCREEN_ORIENTATION_USER
            ), ActivityInfo.SCREEN_ORIENTATION_USER
        );

        GLog.d(TAG, "[setupScreenOrientationIfNeeded] requestedOrientation = "
            + getRequestedOrientation()
            + " , expectedScreenOrientation = " + expectedScreenOrientation
        );

        if (getRequestedOrientation() == expectedScreenOrientation) {
            return;
        }

        setRequestedOrientation(expectedScreenOrientation);
    }

    private void initAppUiConfig() {
        final AppUiResponder.AppUiConfig uiConfig = getCurrentAppUiConfig();
        GLog.d(TAG, "initAppUiConfig uiConfig = " + uiConfig);
        if (mControlBar != null) {
            mControlBar.updateAppUiConfig(uiConfig);
        }
    }

    private void initResource() {
        GLog.d(TAG, "initResource");
        EditorInitViewModel editorInitViewModel = new ViewModelProvider(this).get(EditorInitViewModel.class);
        editorInitViewModel.setVideoEngineManager(mGalleryVideoEngine);

        SongResourceManager songResourceManager = new SongResourceManager(this);
        EditorSongViewModel editorSongViewModel = new ViewModelProvider(this).get(EditorSongViewModel.class);
        editorSongViewModel.setSongManager(songResourceManager);
        editorSongViewModel.setVideoEngineManager(mGalleryVideoEngine);
        editorSongViewModel.loadSongData();

        FilterResourceManager filterResourceManager = new FilterResourceManager(this);
        EditorFilterViewModel editorFilterViewModel = new ViewModelProvider(this).get(EditorFilterViewModel.class);
        editorFilterViewModel.setCurrentResourceManager(filterResourceManager);
        editorFilterViewModel.setVideoEngineManager(mGalleryVideoEngine);
        editorFilterViewModel.loadData();

        FxResourceManager fxResourceManager = new FxResourceManager(this);
        EditorFxViewModel editorFxViewModel = new ViewModelProvider(this).get(EditorFxViewModel.class);
        editorFxViewModel.setCurrentResourceManager(fxResourceManager);
        editorFxViewModel.setVideoEngineManager(mGalleryVideoEngine);
        editorFxViewModel.loadData();

        TemplateResourceManager templateResourceManager = new TemplateResourceManager(this);
        EditorTemplateViewModel editorTemplateViewModel = new ViewModelProvider(this).get(EditorTemplateViewModel.class);
        editorTemplateViewModel.setSongResourceManager(songResourceManager);
        editorTemplateViewModel.setCurrentResourceManager(templateResourceManager);
        editorTemplateViewModel.setVideoEngineManager(mGalleryVideoEngine);
        editorTemplateViewModel.loadTemplateData();
    }

    private void initFileObserver() {
        if (!TextUtils.isEmpty(mVideoPath)) {
            File file = new File(mVideoPath);
            String parentPath = file.getParent();
            String fileName = file.getName();
            if (!TextUtils.isEmpty(parentPath) && !TextUtils.isEmpty(fileName)) {
                mVideoFileObserver = new VideoFileObserver(this, parentPath, fileName, FileObserver.DELETE);
                mVideoFileObserver.startWatching();
            }
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if ((keyCode == KeyEvent.KEYCODE_BACK) && (event.getRepeatCount() == 0)) {
            if (findViewById(R.id.video_editor_guide_layout) != null) {
                return true;
            }
            if ((mControlBar.getCurrentEditor() != null)
                && (mControlBar.onVideoEditorBack())) {
                return true;
            }
            if (mWallPaperParam != null) {
                VideoTrackHelper.cleanOperation();
                setResult(RESULT_CANCELED);
                finish();
                return true;
            }
            if (mGalleryVideoEngine.hasVideoChanged()) {
                showCancelDialog(null);
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    private void registerSDCardBroadcast() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_MEDIA_UNMOUNTED);
        filter.addAction(Intent.ACTION_MEDIA_REMOVED);
        filter.addDataScheme("file");
        BroadcastDispatcher.INSTANCE.registerReceiver(this, mSDCardReceiver, filter,
            AppBrandConstants.Permission.getPERMISSION_COMPONENT_SAFE(), null, 0);
    }

    private void unRegisterSDCardBroadcast() {
        try {
            BroadcastDispatcher.INSTANCE.unregisterReceiver(this, mSDCardReceiver);
        } catch (Exception e) {
            GLog.w(TAG, "unRegisterSDCardBroadcast, e:" + e);
        }
    }

    private void showCancelDialog(View view) {
        if ((mCancelDialog != null) && mCancelDialog.isShowing()) {
            return;
        }
        if (isFinishing() || isDestroyed()) {
            GLog.d(TAG, "showCancelDialog, activity is destroyed");
            return;
        }
        ConfirmDialog.Builder builder = new ConfirmDialog.Builder(this);
        builder.setTitle(R.string.videoeditor_editor_text_abandon_current_modify);
        builder.setNeutralButton(R.string.videoeditor_editor_text_abandon_amend, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                VideoTrackHelper.trackClickVideoCancelMenuUserAction(Value.CANCEL, Value.OP_OK);
                VideoTrackHelper.cleanOperation();
                dialog.dismiss();
                finish();
            }
        });
        builder.setNegativeButton(R.string.videoeditor_cancel, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                VideoTrackHelper.trackClickVideoCancelMenuUserAction(Value.CANCEL, Value.OP_CANCEL);
                dialog.dismiss();
            }
        });
        mCancelDialog = builder.build().show();
    }

    private void showNotSupportDialog() {
        if ((mNotSupportDialog != null) && mNotSupportDialog.isShowing()) {
            return;
        }
        if (isFinishing() || isDestroyed()) {
            GLog.e(TAG, LogFlag.DL, "showNotSupportDialog, activity is destroyed");
            return;
        }
        ConfirmDialog.Builder builder = new ConfirmDialog.Builder(this);
        builder.setTitle(R.string.videoeditor_editor_not_support_set_wallpaper);
        builder.setCancelable(false);
        builder.setPositiveButton(com.oplus.gallery.basebiz.R.string.common_ok, (dialog, which) -> {
            VideoTrackHelper.cleanOperation();
            dialog.dismiss();
            finish();
        });
        mNotSupportDialog = builder.build().show();
    }

    private void showCompileDialog() {
        if (isFinishing() || isDestroyed()) {
            GLog.d(TAG, "showCompileDialog, activity is destroyed");
            return;
        }
        if (mCompileDialog == null) {
            ProgressDialog.Builder builder = new ProgressDialog.Builder(this, true);
            int msgResId = 0;
            if (mWallPaperParam != null) {
                msgResId = R.string.videoeditor_editor_msg_processing;
            } else {
                msgResId = R.string.videoeditor_editor_cancel_compiling;
            }
            builder.setTitle(msgResId);
            builder.setCancelable(false);
            builder.setPositiveButton(getResources().getString(android.R.string.cancel), (dialog, whichButton) -> {
                DoubleClickUtils.updateLastClickTime();
                mManualCancelState = MANUAL_CANCELING;
                cancelSaveVideo();
                if (mGalleryVideoEngine != null) {
                    mGalleryVideoEngine.stop(true);
                }
            });
            mCompileDialog = builder.build().show();
        } else if (!mCompileDialog.isShowing()) {
            mCompileDialog.setProgress(0);
            mCompileDialog.show();
        }
    }

    private void saveWallpaperVideo() {
        mCurSaveDir = WallpaperHelper.INSTANCE.getSaveDir();
        mVideoFileName = WallpaperHelper.INSTANCE.getFileName(mWallPaperParam.getVideoTrimTargetContainerFormat());
        File file = new File(mCurSaveDir, mVideoFileName);
        FileOperationUtils.deleteExistFile(file);
        String relativePath = FilePathUtils.getRelativePath(file.getAbsolutePath());
        int tipRes = R.string.videoeditor_video_editor_disk_space_not_enough;
        File tmpFile = VideoStorageHelper.checkStorageEnough(VideoEditorActivity.this,
                VideoStorageHelper.VideoType.VIDEO_EDITOR, ENSURE_SPACE_MB, relativePath, tipRes);
        if (tmpFile == null) {
            GLog.w(TAG, "onDoneClick storage not enough");
            return;
        }
        mMediaUri = Uri.fromFile(file.getFile());
        showCompileDialog();

        WallpaperHelper.INSTANCE.saveVideo(mGalleryVideoEngine, mWallPaperParam, file);
    }

    /**
     * 取消视频保存
     */
    public void cancelSaveVideo() {
        try {
            File mediaFile = null;
            if (!TextUtils.isEmpty(mVideoFileName)) {
                mediaFile = new File(mCurSaveDir, mVideoFileName);
            }
            GLog.d(TAG, "cancelSaveVideo, mediaFile = " + mediaFile);
            if ((mediaFile != null) && mediaFile.exists()) {
                GLog.d(TAG, "cancelSaveVideo, delete file.");
                if (mMediaUri != null) {
                    int result = getContentResolver().delete(mMediaUri, null, null);
                    if (result <= 0) {
                        GLog.w(TAG, "cancelSaveVideo, delete file failed. mMediaUri = " + mMediaUri);
                    }
                }
            }
            mCurSaveDir = null;
            mVideoFileName = null;
        } catch (Exception e) {
            GLog.e(TAG, "cancelSaveVideo, Exception:", e);
        }
    }

    private void setDialogProgress(int progress) {
        if (null != mCompileDialog) {
            mCompileDialog.setProgress(progress);
        }
    }

    public boolean initEngine() {
        mVideoWidth = mGalleryVideoEngine.getVideoFileWidth();
        mVideoHeight = mGalleryVideoEngine.getVideoFileHeight();

        Number fps = null;
        if (mSupportSlowMotion) {
            fps = new Rational(SLOW_MOTION_DEFAULT_FPS, 1);
        } else {
            //采用原视频的帧率
            fps = mGalleryVideoEngine.getVideoFps();
        }
        mVideoFps = fps.floatValue();
        boolean succeed = mGalleryVideoEngine.initEngine(null,
            new VideoSpec(
                mVideoWidth,
                mVideoHeight,
                mVideoFps
            ), true
        );
        if (succeed) {
            mGalleryVideoEngine.setPreviewMaxHeight(VIDEO_PREVIEW_MAX_HEIGHT);
            succeed = mInvokerManager.createLiveWindow(mGalleryEditorVideoView, true);
        }
        return succeed;
    }

    /**
     * 处理 VideoTypeParser 数据
     */
    private void handlerVideoTypeParser() {
        String videoUrl = StringExtKt.getNonEmptyString(mVideoUri);
        String videoPath = StringExtKt.getNonEmptyString(mVideoPath);
        String videoMediaId = StringExtKt.getNonEmptyString(mMediaId);
        String videoCodecType = StringExtKt.getNonEmptyString(mVideoCodecType);
        String colorTransfer = StringExtKt.getNonEmptyString(mColorTransfer);
        mVideoTypeParser.init(videoUrl, videoPath, videoMediaId, videoCodecType, colorTransfer);
        mVideoType = mVideoTypeParser.getVideoType();
    }

    /**
     * 是否 初始化 视频引擎信息
     *
     * @return true:是  false:否
     */
    private boolean isReadyInitVideoEngineInfo() {
        //这里HLG的编码环境是最基本的要求
        boolean isSupportHlgEncode = ConfigAbilityWrapper.getBoolean(IS_SUPPORT_HLG_ENCODE);
        GLog.d(TAG, LogFlag.DL, "[checkVideoType] isSupportHlg = " + isSupportHlgEncode);
        // 当是杜比视频,HDR10、HDR10+，且不支持HLG编码时，则不支持hdr 编辑, return
        if (!mVideoTypeParser.isSupportHdrEdit(isSupportHlgEncode)) {
            GLog.w(TAG, LogFlag.DL, "[checkVideoType] not support hdr edit. mVideoCodecType = " + mVideoCodecType);
            return false;
        }

        boolean isSupport10bitDecode = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_10BIT_DECODE);
        // 当是10bit视频，且不支持10bit Decode，return
        if (!isSupport10bitDecode && mVideoTypeParser.is10BitVideo()) {
            GLog.w(TAG, LogFlag.DL, "[checkVideoType] not support HDR10 or HDR10Plus or 10bit edit.");
            return false;
        }
        return true;
    }

    /**
     * 初始化 视频视频信息
     *
     * @return true:初始化成功  false:初始化失败
     */
    private boolean tryInitVideoEngineInfo() {
        boolean initResult = false;
        if (isReadyInitVideoEngineInfo()) {
            initResult = mGalleryVideoEngine.initVideoFileInfo(mVideoUri, mVideoPath, mIsHfrSlowMotion, mVideoType);
            if (!initResult) {
                GLog.e(TAG, LogFlag.DL, "[tryInitVideoEngineInfo] meicam init fail. videoType = " + mVideoType);
            }
        }
        return initResult;
    }

    private boolean isMediaInSDCard() {
        String internalDir = OplusEnvironment.getInternalPath();
        return (!TextUtils.isEmpty(mVideoPath) && !mVideoPath.startsWith(internalDir));
    }

    private boolean isSavingInSDCard() {
        if ((mCompileDialog != null) && mCompileDialog.isShowing()) {
            File saveVideoDir = VideoStorageHelper.getSaveVideoDir();
            GLog.d(TAG, "isSavingInSDCard, mCurSaveDir = " + mCurSaveDir
                + ", saveVideoDir = " + saveVideoDir);
            return (mCurSaveDir != null) && !mCurSaveDir.equals(saveVideoDir);
        }
        return false;
    }

    @Override
    public void onStartCheck() {
        super.onStartCheck();
        if (mGalleryVideoEngine != null) {
            mGalleryVideoEngine.repaintFrame();
        }
    }

    @Override
    public void onResumeCheck() {
        GLog.d(TAG, "onResumeCheck()");
        enableBrightenUniformity();
        if (!mCheckSupported) {
            mCheckSupported = true;
            mEditorSubThreadHandler.sendEmptyMessage(SubThreadHandler.MSG_CHECK_DECODER);
        }

        if ((mControlBar != null) && (mControlBar.getCurrentEditor() != null)) {
            mControlBar.getCurrentEditor().onPlayStatusChange();
        }
    }

    public boolean checkVideoSupport(Activity activity) {
        return (mGalleryVideoEngine != null)
            && !TextUtils.isEmpty(mVideoPath)
            && (activity != null)
            && (mGalleryVideoEngine.checkVideoSupported(mVideoUri, activity.getApplicationContext()));
    }

    @Override
    public void onPauseCheck() {
        GLog.d(TAG, "onPauseCheck()");
        recoverSystemBrightness();
        if (mGalleryVideoEngine.isPlaying()) {
            mGalleryVideoEngine.pause();
        }
        if (mControlBar.getCurrentEditor() != null) {
            mControlBar.getCurrentEditor().onPlayStatusChange();
        }
        if (mControlBar.getCurrentEditor() instanceof EditorSubTitleState) {
            ((EditorSubTitleState) mControlBar.getCurrentEditor()).exitEditorMode();
        }
        if (mControlBar.getCurrentEditor() instanceof EditorExportOliveState) {
            ((EditorExportOliveState) mControlBar.getCurrentEditor()).onPauseCheck();
        }
    }

    @Override
    public void onStopCheck() {
        mGalleryVideoEngine.repaintFrame();
    }

    @Override
    public void finish() {
        reportStatistics();
        super.finish();
        overridePendingTransition(
            com.oplus.gallery.basebiz.R.anim.business_lib_close_slide_enter_no_alpha,
            com.support.appcompat.R.anim.coui_close_slide_exit
        );
    }

    private void enableBrightenUniformity() {
        if (!mCanOperateBrightenUniformity) {
            GLog.w(TAG, "enableBrightenUniformity skip, since mCanOperateBrightenUniformity is false.");
            return;
        }
        IScreen.IBrightenUniformityOperator operator = getBrightenUniformityOperator();
        if (operator == null) {
            GLog.d(TAG, "enableBrightenUniformity skip, since operator is null.");
            return;
        }
        Window window = getWindow();
        if (window == null) {
            GLog.w(TAG, "enableBrightenUniformity skip, since window is null.");
            return;
        }
        operator.attachBrightenUniformity(window, VALUE_CHAIN_FROM_CAMERA.equals(mChainFrom));
    }

    private void recoverSystemBrightness() {
        if (!mCanOperateBrightenUniformity) {
            GLog.w(TAG, "recoverSystemBrightness skip, since mCanOperateBrightenUniformity is false.");
            return;
        }
        IScreen.IBrightenUniformityOperator operator = getBrightenUniformityOperator();
        if (operator == null) {
            GLog.d(TAG, "recoverSystemBrightness skip, since operator is null.");
            return;
        }
        operator.detachBrightenUniformity(IScreen.IBrightenUniformityOperator.DetachOperate.RESET);
    }

    public void setCanOperateBrightenUniformity(boolean canOperateBrightenUniformity) {
        mCanOperateBrightenUniformity = canOperateBrightenUniformity;
    }

    private IScreen.IBrightenUniformityOperator getBrightenUniformityOperator() {
        IHardwareAbility hardwareAbility = ((GalleryApplication) getApplicationContext()).getAppAbility(IHardwareAbility.class);
        if (hardwareAbility == null) {
            return null;
        }
        IScreen screen = hardwareAbility.getScreen();
        if (screen == null) {
            return null;
        }
        return screen.getBrightenUniformityOperator();
    }

    private void reportStatistics() {
        EditorInitViewModel editorInitViewModel = new ViewModelProvider(this).get(EditorInitViewModel.class);
        editorInitViewModel.reportStatistics();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (mControlBar.getCurrentEditor() != null) {
            mControlBar.getCurrentEditor().onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    @Override
    public void onDestroy() {
        GLog.d(TAG, "onDestroy()");
        if (mControlBar != null) {
            mControlBar.exitEditor();
        }
        if (mGalleryVideoEngine != null) {
            if (!mIsLimited) {
                mGalleryVideoEngine.clearLocationInfo();
            }
            mGalleryVideoEngine.destroy(!mIsLimited);
        }
        VideoStorageHelper.deleteOldVideoFile(this, VideoStorageHelper.VideoType.VIDEO_EDITOR);
        if (mNavigationBarUri != null) {
            ContentObserverManager.unregisterContentObserver(this, mNavigationStatusObserver);
        }
        if (mVideoFileObserver != null) {
            mVideoFileObserver.stopWatching();
        }
        if (mEditorHandler != null) {
            mEditorHandler.removeCallbacksAndMessages(null);
        }
        if (mEditorSubThreadHandler != null) {
            mEditorSubThreadHandler.removeCallbacksAndMessages(null);
            mEditorHandlerThread.quit();
        }
        if ((mCompileDialog != null) && (mCompileDialog.isShowing())) {
            mCompileDialog.dismiss();
        }
        if (mCompileDialogHandler != null) {
            mCompileDialogHandler.removeCallbacksAndMessages(null);
        }
        unRegisterSDCardBroadcast();
        MeicamEngineLimiter.getInstance().unregister(this);
        if (mVideoBrightenManager != null) {
            mVideoBrightenManager.close();
            mVideoBrightenManager = null;
        }
        removeUserInteractionListener(mOnUserInteractionListener);
        mRefreshRateUpdater.release();
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        handleResult(requestCode, resultCode, data);
    }

    private void handleResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == SET_VIDEO_WALLPAPER_REQUEST_CODE) {
            WallpaperHelper.INSTANCE.revokeUriPermission(this);
            if (resultCode == RESULT_OK) {
                if (VALUE_INVOKER_WALLPAPER_PAGE.equals(mInvoker)) {
                    setResult(RESULT_OK);
                } else {
                    setResult(RESULT_CANCELED);
                }
                finish();
            }
        }
    }

    private void startVideoPageByIntent(Intent intent) {
        GLog.d(TAG, "startVideoPageByIntent");
        if (intent == null) {
            return;
        }
        String uriStr = IntentUtils.getStringExtra(intent, SoloopConfig.EXTRA_RESULT_URI);
        if (TextUtils.isEmpty(uriStr)) {
            GLog.e(TAG, "startVideoPageByIntent uriStr is null.");
            return;
        }
        Uri uri = Uri.parse(uriStr);
        setResultAndFinish(uri, null,true);
    }

    private void startWallpaperPageByIntent(Uri uri) {
        GLog.d(TAG, LogFlag.DL, "startWallpaperPageByIntent uri=" + uri);
        try {
            Intent intent = new Intent(ACTION_SET_VIDEO_WALLPAPER);
            intent.setData(uri);
            intent.putExtra(KEY_IS_VIDEO_WALLPAPER, true);
            WallpaperHelper.INSTANCE.grantUriPermission(this, uri);
            startActivityForResult(intent, SET_VIDEO_WALLPAPER_REQUEST_CODE);
        } catch (Exception e) {
            GLog.e(TAG, LogFlag.DL, "startWallpaperPageByIntent", e);
        }
    }

    private void setResultAndFinish(Uri uri,  Bitmap transBitmap, boolean isFromSoloop) {
        if ((uri != null) && ((mWallPaperParam == null) || mWallPaperParam.isNeedToSetResult())) {
            sendMessageForVideoThumbnail(uri);
            Intent result = new Intent();
            result.setData(uri);
            if (!TextUtils.isEmpty(mInvoker)) {
                result.putExtra(KEY_PHOTO_EDITOR_INVOKER, mInvoker);
            }
            if (isFromSoloop) {
                result.putExtra(SoloopConfig.FROM_SOLOOP, true);
            }
            if (!TextUtils.isEmpty(mChainFrom)) {
                result.putExtra(KEY_CHAIN_FROM, mChainFrom);
            }
            if (mWallPaperParam != null) {
                result.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                result.putExtra(KEY_IS_VIDEO_WALLPAPER, true);
            }
            trySetTransitionInfoForResult(result, uri, transBitmap);
            setResult(RESULT_OK, result);
        }
        if (isFromSoloop) {
            finish();
            overridePendingTransition(0, 0);
        } else {
            mCompileDialogHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if ((mCompileDialog != null) && (mCompileDialog.isShowing())) {
                        mCompileDialog.dismiss();
                    }
                    if (mManualCancelState != MANUAL_CANCELING) {
                        if (!((mWallPaperParam == null) || mWallPaperParam.isFinishVideoWallPaperCut())) {
                            startWallpaperPageByIntent(uri);
                        } else {
                            finish();
                            overridePendingTransition(0, 0);
                        }
                    } else {
                        mManualCancelState = MANUAL_CANCEL_FINISH;
                        if (mWallPaperParam != null) {
                            ToastUtil.showShortToast(R.string.videoeditor_editor_cancel_compile_cancel);
                        }
                    }
                }
            }, (mWallPaperParam == null) ? VideoEditorHelper.WAIT_NEW_VIDEO_RELOAD_TIME : 0L);
        }
    }

    /**
     * 跳转到大图
     * @param uri 资源uri
     * @param transBitmap 过渡资源图片
     */
    public void setResultAndFinish(Uri uri, Bitmap transBitmap) {
        if (uri != null) {
            Intent result = new Intent();
            result.setData(uri);
            if (!TextUtils.isEmpty(mInvoker)) {
                result.putExtra(KEY_PHOTO_EDITOR_INVOKER, mInvoker);
            }
            if (!TextUtils.isEmpty(mChainFrom)) {
                result.putExtra(KEY_CHAIN_FROM, mChainFrom);
            }
            trySetTransitionInfoForResult(result, uri, transBitmap);
            setResult(RESULT_OK, result);
            finish();
            overridePendingTransition(0, 0);
        }
    }

    private void sendMessageForVideoThumbnail(Uri uri) {
        try {
            Intent intent = new Intent(AppConstants.Action.ACTION_REQUEST_VIDEO_THUMBNAIL);
            intent.putExtra(KEY_NEW_SAVED_VIDEO_URI, uri.toString());
            LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(intent);
        } catch (Exception e) {
            GLog.e(TAG, "sendMessageForVideoThumbnail", e);
        }
    }

    /**
     * 尝试在intent中放置过渡图信息。避免编辑回大图后，出现闪黑现象。
     * @param resultIntent intent
     * @param uri 新保存视频的uri
     * @param transBitmap 过渡图：新保存视频的封面图。
     */
    private void trySetTransitionInfoForResult(Intent resultIntent, Uri uri, Bitmap transBitmap) {
        if (transBitmap == null) {
            GLog.e(TAG, "[trySetTransitionInfoForResult] transBitmap is null. set failed. black flash may happen in photo page.");
            return;
        }

        ITransitionBoundsProvider positionProvider = mPhotoPositionForEditor;
        if (positionProvider == null) {
            GLog.e(TAG, "[trySetTransitionInfoForResult] positionProvider is null. set failed. black flash may happen in photo page.");
            return;
        }

        MediaItem mediaItem = LocalMediaDataHelper.getLocalMediaItem(uri);
        if (mediaItem == null) {
            GLog.e(TAG, "[trySetTransitionInfoForResult] mediaItem is null. set failed. black flash may happen in photo page.");
            return;
        }

        // 结合大图提供的位置信息，计算过渡bitmap应该显示在屏幕的哪个区域
        AppUiResponder.AppUiConfig uiConfig = getCurrentAppUiConfig();
        Size windowSize = new Size(uiConfig.getWindowWidth().getCurrent(), uiConfig.getWindowHeight().getCurrent());
        Size bitmapSize = new Size(transBitmap.getWidth(), transBitmap.getHeight());
        Rect finalRect = positionProvider.getExitBounds(bitmapSize, windowSize);

        // intent放置：通知大图是编辑保存回大图
        resultIntent.putExtra(KEY_IS_CANCEL_FROM_EDIT, false);
        // intent放置：大图过渡图显示位置
        resultIntent.putExtra(KEY_MEDIA_ITEM_RECT, finalRect);
        // intent放置：大图新跳转的视频标识id
        resultIntent.putExtra(IntentConstant.ViewGalleryConstant.KEY_MEDIA_ITEM_PATH, mediaItem.getPath().toString());
        // intent放置：过渡图
        IntentUtils.putBinderExtra(resultIntent, KEY_TRANSITION_THUMBNAIL, new TransBitmapBinder(transBitmap));
    }

    private boolean updateVideo() {
        GLog.d(TAG, "updateVideo");
        Intent data = getIntent();
        Uri uri = data.getData();
        if (!queryBaseInfo(uri)) {
            return false;
        }
        GLog.d(TAG, "updateVideo, queryBaseInfo true");

        if (TextUtils.isEmpty(mVideoPath)) {
            return false;
        }
        mLastDateModified = new File(mVideoPath).lastModified();
        if (!TextUtils.isEmpty(mVideoTitle)) {
            if (mVideoTitle.toLowerCase().contains(SlowMotionVideoUtils.HSR_SLOW_MOTION_SUFFIX)) {
                // slow motion title example: 0slow_motion_hsr_120:12,220,300,900
                mIsHsrSlowMotion = true;
            } else if (mVideoTitle.toLowerCase().contains(SlowMotionVideoUtils.HFR_SLOW_MOTION_SUFFIX)) {
                // new slow motion title example: 0slow_motion_hfr_120:0,0,0,0
                mIsHfrSlowMotion = true;
            }
            updateSlowMotionVideo();
        }
        if (mIsHfrSlowMotion) {
            GLog.d(TAG, "updateVideo() support hfr width:" + mWidth + " height:" + mHeight);
            if (!SlowMotionVideoUtils.isCameraSupportHFR(this, mWidth, mHeight, mSlowMotionFps)) {
                mIsHfrSlowMotion = false;
                mSupportSlowMotion = false;
            }
        }

        if (GProperty.DEBUG) {
            GLog.d(TAG, "updateVideo() uri:" + uri
                + " mVideoDuration:" + mVideoDuration
                + " mVideoTitle:" + mVideoTitle
                + " mSupportSlowMotion:" + mSupportSlowMotion
                + " mIsFullSlow:" + mIsFullSlow);
        }
        return true;
    }

    private boolean queryBaseInfo(Uri uri) {
        GLog.d(TAG, "queryBaseInfo uri = " + uri);
        if (uri == null) {
            return false;
        }
        mVideoUri = uri.toString();
        if (isFromExternal()) {
            int mediaId = -1;
            if (!"content".equalsIgnoreCase(uri.getScheme())
                || ((mediaId = (int) UriExtKt.parseId(uri)) <= 0)) {
                return false;
            }
            ApiDmManager.getMediaDBSyncDM().executeUrisSync(new Uri[]{uri});
            return queryBaseInfoFromDB(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + EQUAL + mediaId);
        }

        String pathStr = IntentUtils.getStringExtra(getIntent(), KEY_MEDIA_ITEM_PATH);
        if (TextUtils.isEmpty(pathStr)) {
            return false;
        }
        GLog.d(TAG, "queryBaseInfo pathStr:" + pathStr);
        Path path = Path.fromString(pathStr);
        MediaItem mediaItem = LocalMediaDataHelper.getLocalMediaItem(path);
        mVideoMediaItem = mediaItem;

        LocalVideo localVideo = null;
        if (mediaItem != null) {
            localVideo = MediaItemExtKt.toLocalVideo(mediaItem);
        }
        if (localVideo != null) {
            if (!TextUtils.isEmpty(localVideo.getFilePath())
                && (localVideo.getSyncStatus() == GalleryStore.GalleryColumns.LocalColumns.SYNC_STATUS_DEFAULT)) {
                mVideoPath = localVideo.getFilePath();
                mVideoCodecType = localVideo.getCodecType();
                mColorTransfer = MediaItemExtKt.getHlgHdrColorTransfer(localVideo);
                mVideoDuration = localVideo.getDuration();
                mVideoTitle = localVideo.getName();
                mWidth = localVideo.getWidth();
                mHeight = localVideo.getHeight();
                mMimeType = localVideo.getMimeType();
                mSize = localVideo.getFileSize();
                mDateTaken = localVideo.getDateTakenInMs();
                mDisplayName = FilePathUtils.getDisplayName(localVideo.getFilePath());
                mLatitude = localVideo.getLatitude();
                mLongitude = localVideo.getLongitude();
                mMediaId = String.valueOf(localVideo.getMediaId());
                GLog.d(TAG, LogFlag.DL, "[queryBaseInfo] mMediaId = " + mMediaId);
                return true;
            }
            if (GProperty.DEBUG) {
                GLog.d(TAG, "queryBaseInfo from item fail, localVideo :"
                    + localVideo + ", sync_status:" + localVideo.getSyncStatus());
            }
        } else {
            GLog.d(TAG, "queryBaseInfo localVideo is null");
            finish();
            return false;
        }
        // query from db
        String where = _ID + EQUAL + path.getSuffix();
        return queryBaseInfoFromDB(where);
    }

    private boolean isFromExternal() {
        Bundle extraData = getIntent().getExtras();
        if (extraData == null) {
            return true;
        }
        String invoker = extraData.getString(EditVideoAction.KEY_INVOKER);
        return (!EditVideoAction.VALUE_INVOKER_SEARCH_PAGE.equals(invoker)) && (!EditVideoAction.VALUE_INVOKER_GALLERY_PAGE.equals(invoker));
    }

    private boolean queryBaseInfoFromDB(String where) {
        try (Cursor cursor = new QueryReq.Builder<Cursor>().setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setProjection(BASE_INFO_PROJECTION)
            .setWhere(where)
            .setConvert(new CursorConvert())
            .build()
            .exec()) {
            if (VideoEditorHelper.checkCursorValid(cursor)) {
                cursor.moveToFirst();
                final int id = cursor.getInt(NUM_0);
                mMediaId = String.valueOf(id);
                mVideoPath = cursor.getString(NUM_1);
                mVideoDuration = cursor.getInt(NUM_2);
                mVideoTitle = cursor.getString(NUM_3);
                mWidth = cursor.getInt(NUM_4);
                mHeight = cursor.getInt(NUM_5);
                mMimeType = cursor.getString(NUM_6);
                mSize = cursor.getLong(NUM_7);
                mDateTaken = cursor.getLong(NUM_8);
                mDisplayName = cursor.getString(NUM_9);
                mVideoCodecType = cursor.getString(NUM_10);
                GLog.d(TAG, LogFlag.DL, "[queryBaseInfoFromDB] mMediaId = " + mMediaId);
                float[] latLng = ApiDmManager.getMediaDBSyncDM().parseLatlng(id, mVideoPath, MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO);
                if ((latLng != null) && (latLng.length >= 2)) {
                    mLatitude = latLng[0];
                    mLongitude = latLng[1];
                }
            }
        } catch (Exception e) {
            GLog.e(TAG, "queryBaseInfo from db fail:", e);
            return false;
        }
        return true;
    }

    private void updateSlowMotionVideo() {
        if (mIsHsrSlowMotion || mIsHfrSlowMotion) {
            GLog.d(TAG, "updateVideo(), slow motion mTitle:" + mVideoTitle + " mIsHsrSlowMotion:"
                + mIsHsrSlowMotion + "mIsHfrSlowMotion:" + mIsHfrSlowMotion);
            try {
                String[] motion = mVideoTitle.split(":");
                if (motion.length == NUM_2) {
                    String[] time = motion[NUM_1].split(",");
                    String[] fps = motion[NUM_0].split("_");
                    if ((time.length == NUM_4) && ((fps.length == SLOW_MOTION_FPS_FIVE_PARTS) || (fps.length == SLOW_MOTION_FPS_FOUR_PARTS))) {
                        mSlowAEnter = Long.parseLong(time[NUM_0]);
                        mSlowAOut = Long.parseLong(time[NUM_1]);
                        mSlowBEnter = Long.parseLong(time[NUM_2]);
                        mSlowBOut = Long.parseLong(time[NUM_3]);
                        mSlowMotionFps = Integer.parseInt(fps[fps.length - 1]);
                        GLog.d(TAG, "updateVideo()"
                            + " mFPS:" + mSlowMotionFps
                            + " mSlowAEnter:" + mSlowAEnter
                            + " mSlowAOut:" + mSlowAOut
                            + " mSlowBEnter:" + mSlowBEnter
                            + " mSlowBOut:" + mSlowBOut);
                        if ((mSlowMotionFps != SUPPORT_SLOW_MOTION_FPS_120)
                            && (mSlowMotionFps != SUPPORT_SLOW_MOTION_FPS_240)
                            && (mSlowMotionFps != SUPPORT_SLOW_MOTION_FPS_480)) {
                            mSupportSlowMotion = false;
                            mIsHfrSlowMotion = false;
                            mIsHsrSlowMotion = false;
                            mSlowMotionFps = SLOW_MOTION_DEFAULT_FPS;
                        } else {
                            mSupportSlowMotion = true;
                            /*
                            because the slow time  writeln by camera into database may be difference with
                            the time writeln by media coder into the file
                            we need adjust incorrect slow motion time
                            */
                            if (mSlowAEnter >= mVideoDuration) {
                                GLog.w(TAG, "updateVideo() mSlowAEnter >= mVideoDuration");
                                mSlowAEnter = 0;
                                mSlowAOut = 0;
                                mSlowBEnter = 0;
                                mSlowBOut = 0;
                            } else if (mSlowAOut >= mVideoDuration) {
                                GLog.w(TAG, "updateVideo() mSlowAOut >= mVideoDuration");
                                mSlowAOut = mVideoDuration;
                                mSlowBEnter = 0;
                                mSlowBOut = 0;
                            } else if (mSlowBEnter >= mVideoDuration) {
                                GLog.w(TAG, "updateVideo() mSlowBEnter >= mVideoDuration");
                                mSlowBEnter = 0;
                                mSlowBOut = 0;
                            } else if (mSlowBOut >= mVideoDuration) {
                                GLog.w(TAG, "updateVideo() mSlowBOut >= mVideoDuration");
                                mSlowBOut = mVideoDuration;
                            }
                            mIsFullSlow = (mSlowAEnter == 0L) && (mSlowAOut == 0L) && (mSlowBEnter == 0L) && (mSlowBOut == 0L);
                        }
                    }
                }
            } catch (Exception e) {
                GLog.e(TAG, "updateVideo() check slow motion error:" + e);
                mSupportSlowMotion = false;
                mIsHfrSlowMotion = false;
                mIsHsrSlowMotion = false;
                mSlowMotionFps = SLOW_MOTION_DEFAULT_FPS;
            }
        }
    }

    private void parseAddress(double latitude, double longitude) {
        if (mGalleryVideoEngine == null) {
            return;
        }
        // not support latitude(0,0),because cursor.getDouble() will return 0 for a null latitude
        if ((!Double.isNaN(latitude)) && (!Double.isNaN(longitude))
            && (Double.compare(latitude, 0) != 0) && (Double.compare(longitude, 0) != 0)) {
            mGalleryVideoEngine.computeLocationInfo(latitude, longitude);
        } else {
            mGalleryVideoEngine.clearLocationInfo();
        }
    }

    private class EditorHandler extends Handler {

        static final int MSG_PLAY_STATUS_CHANGE = 10;
        static final int MSG_REFRESH_WINDOW = 11;
        static final int MSG_EDITOR_NOT_SUPPORTED = 12;
        static final int MSG_EDITOR_INIT = 13;
        static final int STATUS_CHANGE_REFRESH_DELAY = 50;
        static final int REFRESH_WINDOW_DELAY = 100;
        static final int EDITOR_NOT_SUPPORTED_DELAY = 200;

        static final int REFRESH_NOT_NEED_SEEK = 1;
        static final int REFRESH_NEED_SEEK = 2;

        public EditorHandler() {
            super(Looper.myLooper());
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_PLAY_STATUS_CHANGE:
                    playStatusChange();
                    break;
                case MSG_REFRESH_WINDOW:
                    if ((mGalleryVideoEngine != null) && (mControlBar.getCurrentEditor() != null)) {
                        if (msg.arg1 == REFRESH_NEED_SEEK) {
                            mGalleryVideoEngine.seekTo(mGalleryVideoEngine.getCurrentTime());
                        }
                        mControlBar.getCurrentEditor().onWindowChangeRefresh();
                    }
                    break;

                case MSG_EDITOR_NOT_SUPPORTED:
                    notSupportEdit();
                    break;
                case MSG_EDITOR_INIT:
                    init(mStartExtra);
                    break;
                default:
                    break;
            }
        }

        private void playStatusChange() {
            if (mControlBar.getCurrentEditor() != null) {
                mControlBar.getCurrentEditor().onPlayStatusChange();
            }
            Window window = getWindow();
            if ((mGalleryVideoEngine != null) && (window != null)) {
                if (mGalleryVideoEngine.isPlaying()) {
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                    setWindowRefreshRateIfNeed(true);
                } else {
                    window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                    setWindowRefreshRateIfNeed(false);
                }
            }
        }

        private void setWindowRefreshRateIfNeed(boolean isPlaying) {
            // 不支持此功能feature，do nothing
            if (!ConfigAbilityWrapper.getBoolean(FEATURE_IS_SYSTEM_SUPPORT_120_FPS_PLAYBACK, false, false)) {
                return;
            }

            // 非120帧视频，无视。
            float fps = mVideoFps;
            if (fps < PlayerAdapter.Companion.FPS_120_MIN_AVERAGE_FPS_THRESHOLD) {
                GLog.d(TAG, LogFlag.DF, "[setWindowRefreshRateIfNeed] no need for refreshRate setting for fps: " + fps);
                return;
            }

            if (isDestroyed() || isFinishing() || isRestricted()) {
                GLog.w(TAG, LogFlag.DL, "[setWindowRefreshRateIfNeed] no need to set refreshRate, "
                        + "because current activity is destroyed or restricted. Screen touch idle refresh rate should recover to DEFAULT now. "
                        + "Otherwise, ask OPPO DisplayFramework Team for what happened.");
                return;
            }

            Window window = getWindow();
            if (window == null) {
                GLog.w(TAG, LogFlag.DL, "[setWindowRefreshRateIfNeed] no need to set refreshRate, "
                        + "because no window in activity. Screen touch idle refresh rate should recover to DEFAULT now. "
                        + "Otherwise, ask OPPO DisplayFramework Team for what happened.");
                return;
            }
            mRefreshRateUpdater.setRefreshRate(window, isPlaying ? VIDEO_FRAME_RATE_HIGH : DEFAULT_WINDOW_REFRESH_RATE);
        }
    }

    private class SubThreadHandler extends Handler {

        static final int MSG_CHECK_DECODER = 1;
        static final int MSG_GET_LOCATION = 2;
        static final int MSG_CHECK_SOFT_DECODE = 3;

        private final WeakReference<VideoEditorActivity> mActivity;

        SubThreadHandler(Looper looper, VideoEditorActivity activity) {
            super(looper);
            mActivity = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message message) {
            VideoEditorActivity activity = mActivity.get();
            switch (message.what) {
                case MSG_CHECK_SOFT_DECODE:
                    /*
                    软解判断在onCreateCheck触发,一般耗时3~15ms
                    下一个任务是MSG_CHECK_DECODER在onResumeCheck触发,相当于软解判断前置了
                    */
                    Uri uri = getIntent().getData();
                    if (uri == null) {
                        return;
                    }
                    boolean softDecodeSupport = (mGalleryVideoEngine != null)
                        && (activity != null)
                        && (mGalleryVideoEngine.checkVideoSoftSupported(uri.toString(), activity.getApplicationContext()));
                    if (!softDecodeSupport) {
                        mEditorHandler.removeMessages(EditorHandler.MSG_EDITOR_NOT_SUPPORTED);
                        mEditorHandler.sendEmptyMessage(EditorHandler.MSG_EDITOR_NOT_SUPPORTED);
                    }
                    break;
                case MSG_CHECK_DECODER:
                    if (isFinishing()) {
                        return;
                    }
                    if (updateVideo()) {
                        mEditorHandler.removeMessages(EditorHandler.MSG_EDITOR_INIT);
                        mEditorHandler.sendEmptyMessage(EditorHandler.MSG_EDITOR_INIT);
                    }
                    if (!checkVideoSupport(activity)) {
                        GLog.e(TAG, "handleMessage() checkVideoSupported false");
                        mEditorHandler.removeMessages(EditorHandler.MSG_EDITOR_NOT_SUPPORTED);
                        mEditorHandler.sendEmptyMessage(EditorHandler.MSG_EDITOR_NOT_SUPPORTED);
                    }
                    // 解析地址信息放在检查格式支持后面, 避免解析耗时影响功能
                    parseAddress(mLatitude, mLongitude);
                    break;
                case MSG_GET_LOCATION:
                    Bundle bundle = message.getData();
                    if (bundle != null) {
                        if (!Double.isNaN(mLatitude) && !Double.isNaN(mLongitude) && (mGalleryVideoEngine != null)) {
                            mGalleryVideoEngine.computeLocationInfo(mLatitude, mLongitude);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        GLog.d(TAG, "onKeyUp() keyCode = " + keyCode);
        if (DoubleClickUtils.isFastDoubleClick()) {
            GLog.d(TAG, "onKeyUp() isFastDoubleClick  return");
            return super.onKeyUp(keyCode, event);
        }
        switch (keyCode) {
            case KeyEvent.KEYCODE_HEADSETHOOK:
            case KeyEvent.KEYCODE_MEDIA_PLAY:
            case KeyEvent.KEYCODE_MEDIA_PAUSE:
            case KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE:
                if (mGalleryVideoEngine.isPlaying()) {
                    mGalleryVideoEngine.pause();
                } else {
                    if (mGalleryVideoEngine.getCurrentTime()
                        >= (mGalleryVideoEngine.getTotalTime() - VIDEO_PLAY_END_CHECK_GAP)) {
                        mGalleryVideoEngine.play();
                    } else {
                        mGalleryVideoEngine.play(mGalleryVideoEngine.getCurrentTime(), -1);
                    }
                }
                return true;
            case KeyEvent.KEYCODE_MEDIA_STOP:
                if (mGalleryVideoEngine.isPlaying()) {
                    mGalleryVideoEngine.pause();
                }
                return true;
            default:
                break;
        }
        return super.onKeyUp(keyCode, event);
    }

    private static final class VideoFileObserver extends FileObserver {
        private final WeakReference<Activity> mActivity;
        private final String mFileName;

        private VideoFileObserver(Activity activity, String dir, String fileName, int event) {
            super(new File(dir).getFile(), event);
            mActivity = new WeakReference<>(activity);
            mFileName = fileName;
        }

        @Override
        public void onEvent(int event, String path) {
            switch (event) {
                case FileObserver.DELETE:
                    Activity activity = mActivity.get();
                    if (activity != null) {
                        if (TextUtils.equals(mFileName, path)) {
                            activity.finish();
                        }
                    } else {
                        GLog.w(TAG, "VideoFileObserver  activity destroyed, stop watching");
                        stopWatching();
                    }
                    break;

                default:
                    break;
            }
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        /**
         * 接入新版美摄sdk NvsLiveWindow继承关系发生变化,
         * 窗口方向发生变化,编辑预览非播放场景图像也需要seek刷新
         */
        if (mGalleryVideoEngine == null) {
            GLog.e(TAG, "onConfigurationChanged, mGalleryVideoEngine is null");
            return;
        }
        if ((isInMultiWindowMode() || !mGalleryVideoEngine.isPlaying()) && ((mCompileDialog == null) || !mCompileDialog.isShowing())) {
            int newheight = ScreenUtils.getScreenHeightDp(getBaseContext());
            GLog.d(TAG, "onConfigurationChanged() newheight:" + newheight + " mScreenHeightDp:" + mScreenHeightDp);
            if (newheight != mScreenHeightDp) {
                mGalleryVideoEngine.pause();
                if (mControlBar.getCurrentEditor() instanceof EditorSubTitleState) {
                    ((EditorSubTitleState) mControlBar.getCurrentEditor()).exitEditorMode();
                }
                Message msg = mEditorHandler.obtainMessage();
                msg.what = EditorHandler.MSG_REFRESH_WINDOW;
                if (mGalleryVideoEngine.isSeekEnable()) {
                    msg.arg1 = EditorHandler.REFRESH_NEED_SEEK;
                }
                mEditorHandler.removeMessages(EditorHandler.MSG_REFRESH_WINDOW);
                mEditorHandler.sendMessageDelayed(msg, EditorHandler.REFRESH_WINDOW_DELAY);
                mScreenHeightDp = newheight;
            }
        }
    }

    @Override
    public void onSystemBarChanged(@NotNull WindowInsetsCompat windowInsets) {
        super.onSystemBarChanged(windowInsets);
    }

    @Override
    public void onAppUiStateChanged(@NotNull AppUiResponder.AppUiConfig uiConfig) {
        GLog.d(TAG, "onAppUiStateChanged uiConfig = " + uiConfig);
        if (mControlBar != null) {
            mControlBar.updateAppUiConfig(uiConfig);
        }
        mEditorHandler.removeMessages(EditorHandler.MSG_PLAY_STATUS_CHANGE);
        mEditorHandler.sendEmptyMessageDelayed(EditorHandler.MSG_PLAY_STATUS_CHANGE, EditorHandler.STATUS_CHANGE_REFRESH_DELAY);
    }
}