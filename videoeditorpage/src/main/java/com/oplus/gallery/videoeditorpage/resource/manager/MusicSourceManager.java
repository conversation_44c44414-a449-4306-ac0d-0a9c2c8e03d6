package com.oplus.gallery.videoeditorpage.resource.manager;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.oplus.gallery.basebiz.permission.NetSendPrivacyPermissionRule;
import com.oplus.gallery.foundation.database.helper.InsertFileNameContentType;
import com.oplus.gallery.foundation.database.helper.PrivacyPersonalInfoTableHelper;
import com.oplus.gallery.foundation.networkaccess.callback.AppResultCallback;
import com.oplus.gallery.framework.abilities.download.IDownloadAbility;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.framework.abilities.download.networkrequest.OplusResponseData;
import com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.security.EncryptUtils;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.resource.callback.DaoResultCallback;
import com.oplus.gallery.videoeditorpage.resource.data.MusicResponseBean;
import com.oplus.gallery.videoeditorpage.resource.listener.DownloadListener;
import com.oplus.gallery.videoeditorpage.resource.listener.OnLoadingListener;
import com.oplus.gallery.videoeditorpage.resource.listener.OnLoadingListenerProxy;
import com.oplus.gallery.videoeditorpage.resource.room.bean.MusicItem;
import com.oplus.gallery.videoeditorpage.resource.room.helper.MusicTableHelper;
import com.oplus.gallery.videoeditorpage.resource.room.helper.ResourceDatabaseHelper;
import com.oplus.gallery.videoeditorpage.resource.util.AsyncDbCommand;
import com.oplus.gallery.videoeditorpage.resource.util.ErrorCode;
import com.oplus.gallery.videoeditorpage.resource.util.RtnCode;

import java.util.ArrayList;
import java.util.List;

import static com.oplus.gallery.framework.abilities.videoedit.data.ThemeInfo.THEME_CATEGORY_MEMORY;
import static com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_DOWNLOAD_FILE;
import static com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_DOWNLOAD_ICON;
import static com.oplus.gallery.videoeditorpage.resource.room.bean.Item.TYPE_NOT_DOWNLOADED;
import static com.oplus.gallery.videoeditorpage.resource.util.RtnCode.DOWNLOAD_ABILITY_NOT_EXIST;

public class MusicSourceManager extends LocalSourceManager<MusicItem, List<MusicResponseBean.SongListBean>> {
    private static final String TAG = "Memories.MusicSourceManager";
    public static final String ASSETS_MUSIC_CONFIG = "videoeditor_music_config.json";
    public static final String ASSETS_MUSIC_PATH = "assets:/music/";
    public static final String MUSIC_SUFFIX = ".m4a";
    public static final String MUSIC_NONE = "None";
    public static final String MUSIC_LOCAL = "local";
    public static final String RESOURCE_MUSIC_DIRECTORY = "music";
    public static final String ACTION_MUSIC_DOWNLOAD_STATE = "com.oplus.gallery.videoeditorpage.musicDownloadState";
    public static final int MUSIC_SOURCE_FILE_NUM = 1;
    private static final String KEY_MUSIC_LAST_REQUEST_TIME = "key_music_last_request_time";
    private static volatile MusicSourceManager sMusicSourceManager;

    public static MusicSourceManager getInstance() {
        if (sMusicSourceManager == null) {
            synchronized (MusicSourceManager.class) {
                if (sMusicSourceManager == null) {
                    sMusicSourceManager = new MusicSourceManager();
                }
            }
        }
        return sMusicSourceManager;
    }

    private MusicSourceManager() {
        super(TYPE_UPDATE_BUILTIN | TYPE_UPDATE_DOWNLOAD,
                KEY_MUSIC_LAST_REQUEST_TIME, new MusicTableHelper(), ACTION_MUSIC_DOWNLOAD_STATE);
    }

    @Override
    public String requestNetworkResource(OnLoadingListener listener, boolean needDownloadIcon, boolean forceRequest) {
        OnLoadingListenerProxy listenerProxy = new OnLoadingListenerProxy(listener);
        GLog.d(TAG, "[requestNetworkResource] needDownloadIcon=" + needDownloadIcon + ", forceRequest=" + forceRequest
                + ", isInRequestInterval=" + isInRequestInterval());
        if (!permitUpdateFromDownload()) {
            listenerProxy.onLoadingError(ErrorCode.Music.NO_PERMISSION);
            return null;
        }
        if (!NetworkPermissionManager.isUseOpenNetwork()) {
            listenerProxy.onLoadingError(ErrorCode.Music.NO_ALLOW_OPEN_NETWORK);
            return null;
        }
        if (!(forceRequest || !isInRequestInterval())) {
            GLog.d(TAG, "[requestNetworkResource] at intervals");
            new AsyncDbCommand<List<MusicItem>>() {
                @Override
                protected List<MusicItem> doInBackground() {
                    return queryIconExistedMusic();
                }
            }.setResultCallback(new DaoResultCallback<List<MusicItem>>() {
                @Override
                public void onResult(List<MusicItem> entityList) {
                    listenerProxy.onLoadingFinish(RtnCode.Music.AT_INTERVALS, entityList);
                }
            }).execute();
            if (needDownloadIcon) {
                checkIcon(listener);
            }
            return null;
        }

        String requestTag = null;
        IDownloadAbility downloadAbility = ((GalleryApplication) ContextGetter.context).getAppAbility(IDownloadAbility.class);
        if (downloadAbility == null) {
            listenerProxy.onLoadingError(DOWNLOAD_ABILITY_NOT_EXIST);
            return null;
        }
        requestTag = downloadAbility.enqueue(ContextGetter.context,
                new GetSongRequest(ContextGetter.context, new NetSendPrivacyPermissionRule()),
                new AppResultCallback<OplusResponseData<MusicResponseBean>>() {
                    @Override
                    public void onSuccess(OplusResponseData<MusicResponseBean> responseBean) {
                        if ((responseBean == null) || (responseBean.getData() == null)) {
                            GLog.e(TAG, "[onSuccess] responseBean is null");
                            listenerProxy.onLoadingError(ErrorCode.Music.RESPONSE_DATA_ERROR);
                            return;
                        }
                        List<MusicResponseBean.SongListBean> songListBeanList = responseBean.getData().getSongList();
                        GLog.d(TAG, "[onSuccess] songListBeanList.size = " + (songListBeanList == null ? 0 : songListBeanList.size()));
                        if (songListBeanList == null) {
                            listenerProxy.onLoadingError(ErrorCode.Music.RESPONSE_DATA_ERROR);
                            return;
                        }
                        // 获取歌曲列表，提供回忆编辑使用:保存歌曲名称
                        songListBeanList.forEach(songListBean -> PrivacyPersonalInfoTableHelper.INSTANCE.
                                insertPersonalInfoCollectionFileNameContentInIO(songListBean.getZhName(), songListBean.getEnName(),
                                        InsertFileNameContentType.MUSIC_SOURCE));

                        int counter = mTableHelper.getAllBuiltinSize();
                        if (counter == 0) {
                            GLog.d(TAG, "[onSuccess] checkBuiltin maybe not yet called");
                        }
                        int newSize = counter + songListBeanList.size();
                        int oldSize = mTableHelper.getAllResourceSize();
                        GLog.d(TAG, "[onSuccess] getAllBuiltinSize = " + counter + ";newSize = " + newSize + " , (oldSize) = " + oldSize);
                        List<MusicItem> musicEntityList = localizeData(songListBeanList, counter);

                        if ((musicEntityList == null) || (musicEntityList.size() == 0)) {
                            GLog.d(TAG, "[onSuccess] There is no need to update.");
                            if (oldSize > newSize) {
                                removeInvalidEntity(null, newSize);
                            } else {
                                GLog.d(TAG, "[onSuccess] And size is no change.");
                            }
                            updateRequestInterval();
                            listenerProxy.onLoadingFinish(RtnCode.Music.SUCCESS, queryIconExistedMusic());
                            if (needDownloadIcon) {
                                checkIcon(listener);
                            }
                            return;
                        }
                        List<MusicItem> coveredEntityList = getCoveredEntity(musicEntityList, oldSize);
                        GLog.d(TAG, "[onSuccess] coveredEntityList = " + coveredEntityList);
                        List ret = mTableHelper.insertAll(musicEntityList);
                        if (ret == null) {
                            GLog.e(TAG, "[onSuccess] requestNetworkResource database insert failed!");
                            listenerProxy.onLoadingError(ErrorCode.Music.DATABASE_INSERT_ERROR);
                            return;
                        }
                        removeInvalidEntity(coveredEntityList, newSize);
                        updateRequestInterval();
                        listenerProxy.onLoadingFinish(RtnCode.Music.SUCCESS, queryIconExistedMusic());
                        if (needDownloadIcon) {
                            checkIcon(listener);
                        }
                    }

                    @Override
                    public void onFailed(int code, String msg) {
                        GLog.e(TAG, "[onFailed] code = " + code + " , msg = " + msg);
                        listenerProxy.onLoadingError(code);
                    }
                });
        downloadAbility.close();
        return requestTag;
    }

    @Override
    protected List<MusicItem> getCoveredEntity(List<MusicItem> nowEntityList, int oldSize) {
        if (nowEntityList == null) {
            return new ArrayList<>();
        }
        List<MusicItem> coveredEntityList = new ArrayList<>();
        for (MusicItem nowEntity : nowEntityList) {
            int currentPosition = nowEntity.getPosition();
            if (currentPosition <= oldSize) {
                MusicItem coveredEntity = ((MusicTableHelper) mTableHelper).getEntityByPosition(currentPosition);
                if ((coveredEntity != null) && (coveredEntity.getDownloadState() > TYPE_NOT_DOWNLOADED)) {
                    coveredEntityList.add(coveredEntity);
                }
            }
        }
        return coveredEntityList;
    }

    @Override
    protected ArrayList<MusicItem> removeInvalidEntity(List<MusicItem> coveredEntityList, int maxPosition) {

        List<MusicItem> musicEntityList = mTableHelper.getInvalidEntityList(maxPosition);
        ArrayList<MusicItem> rtnList = new ArrayList<>();
        if (musicEntityList != null) {
            for (MusicItem musicEntity : musicEntityList) {
                FileOperationUtils.deleteFolderFile(musicEntity.getSourcePath(), true);
                FileOperationUtils.deleteFolderFile(musicEntity.getThumbnailPath(), true);
                rtnList.add(musicEntity);
            }
            mTableHelper.deleteInvalidEntity(maxPosition);
        }

        if ((coveredEntityList == null) || (coveredEntityList.size() == 0)) {
            return rtnList;
        }
        for (MusicItem coveredEntity : coveredEntityList) {
            MusicItem nowMusicEntity =
                    ((MusicTableHelper) mTableHelper).getEntityByResourceId(coveredEntity.getMusicId());
            if (nowMusicEntity == null) {
                FileOperationUtils.deleteFolderFile(coveredEntity.getSourcePath(), true);
                FileOperationUtils.deleteFolderFile(coveredEntity.getThumbnailPath(), true);
                rtnList.add(coveredEntity);
            }
        }
        return rtnList;
    }

    @Override
    public void checkIcon(OnLoadingListener listener) {
        GLog.d(TAG, "checkIcon begin");
        OnLoadingListenerProxy listenerProxy = new OnLoadingListenerProxy(listener);

        List<MusicItem> noIconEntityList = mTableHelper.getNoIconEntityList();
        if (noIconEntityList == null) {
            GLog.e(TAG, "getNoIconEntityList failed!");
            listenerProxy.onLoadingError(ErrorCode.Music.DATABASE_QUERY_ERROR);
            return;
        }
        if (noIconEntityList.size() == 0) {
            GLog.d(TAG, "there is no entity without icon!");
            return;
        }
        GLog.d(TAG, "noIconEntityList = " + noIconEntityList);
        for (MusicItem entity : noIconEntityList) {
            final int resourceId = entity.getMusicId();
            downloadFile(entity, TYPE_DOWNLOAD_ICON, new DownloadListener() {

                @Override
                public void onProgress(int progress) {

                }

                @Override
                public void onFinish(int code, String destFilePath) {
                    MusicItem musicEntity = ((MusicTableHelper) mTableHelper).getEntityByResourceId(resourceId);
                    listenerProxy.onIconDownloadFinish(musicEntity);
                }

                @Override
                public void onError(int errCode) {
                    GLog.e(TAG, "downloadFile Icon errCode = " + errCode);
                    MusicItem musicEntity = ((MusicTableHelper) mTableHelper).getEntityByResourceId(resourceId);
                    listenerProxy.onIconDownloadError(errCode, musicEntity);
                }
            }, true);
        }
    }

    @Override
    public void retryDownload() {
        List<Integer> idList = getNeedRetryItem();
        if (idList == null) {
            return;
        }
        List<Integer> resourceIdList = new ArrayList<>(idList);
        for (int resourceId : resourceIdList) {
            MusicItem musicItem = getMusic(resourceId);
            if (musicItem == null) {
                continue;
            }
            retryDownloadMusic(musicItem);
        }
        GLog.d(TAG, "retryDownload");
    }

    @Override
    protected List<MusicItem> localizeData(List<MusicResponseBean.SongListBean> data, int builtinSize) {
        if (data == null) {
            GLog.e(TAG, "localizeData SongListBean is null");
            return null;
        }
        int counter = builtinSize;
        List<MusicItem> musicEntityList = new ArrayList<>();
        for (MusicResponseBean.SongListBean songListBean : data) {
            counter++;
            MusicItem musicEntity = new MusicItem();
            MusicItem oldMusicEntity =
                    ((MusicTableHelper) mTableHelper).getEntityByResourceId(songListBean.getSongId());
            if (oldMusicEntity != null) {
                GLog.d(TAG, "localizeData oldMusicEntity=" + oldMusicEntity);
                int oldDownloadState = oldMusicEntity.getDownloadState();
                String oldUpdateTime = oldMusicEntity.getUpdateTime();
                String newUpdateTime = songListBean.getUpdateTime();
                if (TextUtils.equals(newUpdateTime, oldUpdateTime)) {
                    if (counter == oldMusicEntity.getPosition()) {
                        continue;
                    }
                    musicEntity.setThumbnailPath(oldMusicEntity.getThumbnailPath());
                    musicEntity.setSourcePath(oldMusicEntity.getSourcePath());
                    musicEntity.setDownloadState(oldDownloadState);
                } else if (oldMusicEntity.isDefaultIcon()) {
                    musicEntity.setThumbnailPath(oldMusicEntity.getThumbnailPath());
                    musicEntity.setSourcePath(oldMusicEntity.getSourcePath());
                    musicEntity.setDownloadState(oldDownloadState);
                } else {
                    int downloadState = TYPE_NOT_DOWNLOADED;
                    if (TextUtils.equals(oldMusicEntity.getThumbnailUrl(), songListBean.getIconPath())
                            && !oldMusicEntity.isNeedDownloadIcon()) {
                        musicEntity.setThumbnailPath(oldMusicEntity.getThumbnailPath());
                        downloadState |= TYPE_DOWNLOAD_ICON;
                    }
                    if (TextUtils.equals(oldMusicEntity.getFileUrl(), songListBean.getSongFilePath())
                            && !oldMusicEntity.isNeedDownloadFile()) {
                        musicEntity.setSourcePath(oldMusicEntity.getSourcePath());
                        downloadState |= TYPE_DOWNLOAD_FILE;
                    }
                    musicEntity.setDownloadState(downloadState);
                }
            } else {
                musicEntity.setDownloadState(TYPE_NOT_DOWNLOADED);
            }

            musicEntity.setPosition(counter);
            musicEntity.setMusicId(songListBean.getSongId());
            musicEntity.setZhName(songListBean.getZhName());
            musicEntity.setChName(songListBean.getChName());
            musicEntity.setEnName(songListBean.getEnName());
            musicEntity.setThumbnailUrl(songListBean.getIconPath());
            musicEntity.setFileUrl(songListBean.getSongFilePath());
            musicEntity.setUpdateTime(songListBean.getUpdateTime());
            musicEntity.setIsBuiltin(false);
            GLog.d(TAG, "localizeData musicEntity=" + musicEntity);
            musicEntityList.add(musicEntity);
        }
        return musicEntityList;
    }

    public String downloadMusic(MusicItem musicEntity, int source,
                                boolean needDownloadTheme) {
        if (musicEntity == null) {
            sendBroadcast(DOWNLOAD_STATE_ERROR, ErrorCode.Music.ENTITY_IS_NULL, -1);
            return null;
        }
        final int resourceId = musicEntity.getMusicId();
        changeSource(resourceId, source);
        if ((source != DOWNLOAD_RETRY) && isDownloadingStateInQueue(resourceId)) {
            sendBroadcast(DOWNLOAD_STATE_DOWNLOADING, RtnCode.Music.DOWNLOADING, resourceId, needDownloadTheme);
            return null;
        }
        mProgressLock = false;
        sendDownloadProgress(resourceId, MIN_PROGRESS);
        return downloadFile(musicEntity, TYPE_DOWNLOAD_FILE, new DownloadListener() {
            @Override
            public void onProgress(int progress) {
                sendDownloadProgress(resourceId, progress);
            }

            @Override
            public void onFinish(int code, String destFilePath) {
                if (code == RtnCode.NETWORK_SUCCESS) {
                    code = RtnCode.Music.SUCCESS;
                }
                sendBroadcast(DOWNLOAD_STATE_FINISH, code, resourceId, needDownloadTheme);
            }

            @Override
            public void onError(int errCode) {
                sendBroadcast(DOWNLOAD_STATE_ERROR, errCode, resourceId);
            }
        }, false);
    }

    public String downloadMusicByTheme(MusicItem musicItem, int source) {
        if (musicItem == null) {
            sendBroadcast(DOWNLOAD_STATE_ERROR, ErrorCode.Music.ENTITY_IS_NULL, -1);
            return null;
        }
        return downloadMusic(musicItem, source, true);
    }

    public String retryDownloadMusic(MusicItem musicItem) {
        return downloadMusic(musicItem, DOWNLOAD_RETRY, false);
    }

    public String manualDownloadMusic(MusicItem musicEntity) {
        return downloadMusic(musicEntity, DOWNLOAD_MANUAL, false);
    }

    @Override
    public String downloadFile(MusicItem musicEntity, final int type, DownloadListener listener, boolean filterContentType) {
        if (musicEntity == null) {
            GLog.e(TAG, "musicEntity is null!");
            if (listener != null) {
                listener.onError(ErrorCode.Music.ENTITY_IS_NULL);
            }
            return null;
        }

        final int resourceId = musicEntity.getMusicId();
        final int downloadState =
                mTableHelper.getDownloadState(musicEntity.getMusicId());
        if (downloadState < 0) {
            if (listener != null) {
                listener.onError(ErrorCode.Music.ID_INVALID);
            }
            return null;
        }
        if (!musicEntity.isNeedDownload(downloadState, type)) {
            String path =
                    (type == TYPE_DOWNLOAD_ICON) ? musicEntity.getThumbnailPath() : musicEntity.getSourcePath();
            if (listener != null) {
                listener.onFinish(RtnCode.Music.ALREADY_DOWNLOADED, path);
            }
            return null;
        }
        String fileUrl;
        if (type == TYPE_DOWNLOAD_ICON) {
            fileUrl = musicEntity.getThumbnailUrl();
        } else if (type == TYPE_DOWNLOAD_FILE) {
            fileUrl = musicEntity.getFileUrl();
        } else {
            GLog.e(TAG, "Error downloadFile type = " + type);
            if (listener != null) {
                listener.onError(ErrorCode.Music.PARAM_INVALID);
            }
            return null;
        }

        String fileName = EncryptUtils.encryptMD5ToString(fileUrl);
        if (type == TYPE_DOWNLOAD_FILE) {
            fileName = fileName + TEMP_ZIP_SUFFIX;
        }
        File file = new File(getDownloadPath(RESOURCE_MUSIC_DIRECTORY), fileName);

        return downloadNormal(fileUrl, file.getAbsolutePath(), new DownloadListener() {

            @Override
            public void onProgress(int progress) {
                if (listener != null) {
                    listener.onProgress(progress);
                }
            }

            @Override
            public void onFinish(int code, String destFilePath) {
                if (!FileOperationUtils.isFileExist(destFilePath)) {
                    GLog.e(TAG, "downloadNormal destFilePath is invalid!");
                    if (listener != null) {
                        listener.onError(ErrorCode.Music.FILE_PATH_INVALID);
                    }
                }
                MusicItem entity = ((MusicTableHelper) mTableHelper).getEntityByResourceId(resourceId);
                if (entity == null) {
                    if (listener != null) {
                        listener.onError(ErrorCode.Music.ID_INVALID);
                    }
                    return;
                }
                if (type == TYPE_DOWNLOAD_ICON) {
                    if (!updateDownloadIconState(resourceId, destFilePath, listener)) {
                        return;
                    }
                } else if (type == TYPE_DOWNLOAD_FILE) {
                    if (!updateDownloadZipState(resourceId, destFilePath, listener)) {
                        return;
                    }
                }
                if (listener != null) {
                    listener.onFinish(code, destFilePath);
                }
            }

            @Override
            public void onError(int errCode) {
                GLog.e(TAG, "downloadNormal File errCode = " + errCode);
                if (listener != null) {
                    listener.onError(errCode);
                }
            }
        }, filterContentType);
    }

    private boolean updateDownloadIconState(int resourceId, String destFilePath, DownloadListener listener) {
        Boolean isUpdateSuccessful = ResourceDatabaseHelper.getInstance().runInTransaction(() -> {
            MusicItem entity = ((MusicTableHelper) mTableHelper).getEntityByResourceId(resourceId);
            if (entity == null) {
                if (listener != null) {
                    listener.onError(ErrorCode.Music.ID_INVALID);
                }
                return false;
            }
            entity.setThumbnailPath(destFilePath);
            entity.updateDownloadState(TYPE_DOWNLOAD_ICON);
            int ret = mTableHelper.update(entity);
            if (ret < 0) {
                GLog.e(TAG, "updateDownloadIconState: downloadNormal update data failed!");
                if (listener != null) {
                    listener.onError(ErrorCode.Theme.DATABASE_UPDATE_ERROR);
                }
                return false;
            }
            GLog.d(TAG, "updateDownloadIconState success!");
            return true;
        });
        if (isUpdateSuccessful != null) {
            return isUpdateSuccessful;
        } else {
            return false;
        }
    }

    private boolean updateDownloadZipState(int resourceId, String destFilePath, DownloadListener listener) {
        File zipFile = new File(destFilePath);
        String result = destFilePath.substring(0, destFilePath.length() - TEMP_ZIP_SUFFIX.length());
        try {
            FileOperationUtils.unzipFolder(destFilePath, result);
        } catch (Exception e) {
            if (listener != null) {
                listener.onError(ErrorCode.Music.UNZIP_ERROR);
            }
            GLog.e(TAG, "updateDownloadZipState: downloadNormal finish, unZipFolder exception", e);
            return false;
        } finally {
            zipFile.delete();
        }
        Boolean isUpdateSuccessful = ResourceDatabaseHelper.getInstance().runInTransaction(() -> {
            MusicItem entity = ((MusicTableHelper) mTableHelper).getEntityByResourceId(resourceId);
            if (entity == null) {
                if (listener != null) {
                    listener.onError(ErrorCode.Music.ID_INVALID);
                }
                return false;
            }
            entity.setSourcePath(result);
            entity.updateDownloadState(TYPE_DOWNLOAD_FILE);
            int ret = mTableHelper.update(entity);
            if (ret < 0) {
                GLog.e(TAG, "updateDownloadZipState: downloadNormal update data failed!");
                if (listener != null) {
                    listener.onError(ErrorCode.Theme.DATABASE_UPDATE_ERROR);
                }
                return false;
            }
            GLog.d(TAG, "updateDownloadZipState success! entity = " + entity);
            return true;
        });
        if (isUpdateSuccessful != null) {
            return isUpdateSuccessful;
        } else {
            return false;
        }
    }

    @Override
    public List<MusicItem> getResourceLists() {
        return ((MusicTableHelper) mTableHelper).getAll();
    }

    public List<MusicItem> queryIconExistedMusic() {
        List<MusicItem> list = ((MusicTableHelper) mTableHelper).queryIconExistedMusic();
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(0, getNoneMusicItem());
        list.add(list.size(), getLocalMusicItem());
        return list;
    }

    public MusicItem getMusicBySourcePath(String musicPath) {
        return ((MusicTableHelper) mTableHelper).getMusicBySourcePath(musicPath);
    }

    public MusicItem getMusic(int songId) {
        return ((MusicTableHelper) mTableHelper).getEntityByResourceId(songId);
    }

    @Override
    public boolean checkBuiltinItem(boolean forceUpdate) {
        GLog.d(TAG, "checkBuiltinItem");
        if (!permitUpdateFromBuiltin()) {
            return true;
        }
        if ((!forceUpdate) && (mTableHelper.getAllBuiltinSize() > 0)) {
            return true;
        }
        List<MusicItem> items = getBuildInItems();
        if ((items != null) && (items.size() > 0)) {
            mTableHelper.clearBuiltin();
            List ret = mTableHelper.insertAll(items);
            resetRequestInterval();
            if (ret != null) {
                return true;
            }
            GLog.e(TAG, "checkBuiltinItem insertAll failed!");
            return false;
        }
        GLog.e(TAG, "checkBuiltinItem parseConfig failed!");
        return false;
    }

    private static List<MusicItem> getBuildInItems() {
        Context context = ContextGetter.context;
        if (context == null) {
            GLog.e(TAG, "context is null");
            return null;
        }
        List<MusicItem> items = parseConfig(context, ASSETS_MUSIC_CONFIG,
                new TypeToken<List<MusicItem>>() {
                }.getType());
        return items;
    }

    private static String getDownloadM4aFile(String dir) {
        if (TextUtils.isEmpty(dir)) {
            return "";
        }
        File file = new File(dir);
        File[] list = file.listFiles();
        if ((list != null) && (list.length >= MUSIC_SOURCE_FILE_NUM)) {
            for (File f : list) {
                if (f.getName().contains(MUSIC_SUFFIX)) {
                    return f.getAbsolutePath();
                }
            }
        } else {
            GLog.w(TAG, "getVideoMusicPath, music source file is broken");
        }
        return "";
    }

    private static String getBuildM4aFile(String fileName) {
        String path = ASSETS_MUSIC_PATH + fileName + MusicSourceManager.MUSIC_SUFFIX;
        return path;
    }

    public static String getVideoMusicPath(MusicItem item) {
        if (item == null) {
            GLog.e(TAG, "getVideoMusicPath musicItem is null");
            return null;
        }
        if (item.isBuiltin()) {
            return getBuildM4aFile(item.getSourcePath());
        } else {
            return getDownloadM4aFile(item.getSourcePath());
        }
    }

    public static String getMemoriesMusicPath(MusicItem item) {
        if (item == null) {
            GLog.e(TAG, "getMemoriesMusicPath musicItem is null");
            return null;
        }
        if (item.isBuiltin()) {
            return item.getSourcePath();
        } else {
            return getDownloadM4aFile(item.getSourcePath());
        }
    }

    public static String getMemoriesMusicPath(ThemeInfo item) {
        if (item == null) {
            GLog.e(TAG, "themeItem is null");
            return null;
        }
        MusicItem musicItem = MusicSourceManager.getInstance().getMusic(item.getSongId());
        if (musicItem == null) {
            GLog.e(TAG, "musicItem is null");
            return null;
        }
        if (item.getCategory() == THEME_CATEGORY_MEMORY) {
            return getMemoriesMusicPath(musicItem);
        } else {
            return null;
        }
    }

    public int update(MusicItem item) {
        return ((MusicTableHelper) mTableHelper).update(item);
    }

    public MusicItem getNoneMusicItem() {
        MusicItem noneItem = new MusicItem();
        String name = ContextGetter.context.getResources().getString(R.string.videoeditor_editor_text_music_none);
        noneItem.setEnName(name);
        noneItem.setChName(name);
        noneItem.setZhName(name);
        noneItem.setThumbnailPath(NONE_ITEM_THUMBNAIL);
        noneItem.setIsBuiltin(true);
        noneItem.setDownloadState(TYPE_DOWNLOAD_ICON | TYPE_DOWNLOAD_FILE);
        noneItem.setSourcePath(MUSIC_NONE);
        return noneItem;
    }

    public MusicItem getLocalMusicItem() {
        MusicItem item = new MusicItem();
        String name = ContextGetter.context.getResources().getString(R.string.videoeditor_editor_text_music_local);
        item.setEnName(name);
        item.setChName(name);
        item.setZhName(name);
        item.setThumbnailPath(LOCAL_MUSIC_THUMBNAIL);
        item.setIsBuiltin(true);
        item.setDownloadState(TYPE_DOWNLOAD_ICON | TYPE_DOWNLOAD_FILE);
        item.setSourcePath(MUSIC_LOCAL);
        return item;
    }
}
