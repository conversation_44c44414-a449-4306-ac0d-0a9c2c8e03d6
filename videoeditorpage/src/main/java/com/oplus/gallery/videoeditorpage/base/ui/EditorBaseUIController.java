/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorBaseUIController.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/06
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/06    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/
package com.oplus.gallery.videoeditorpage.base.ui;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.PathInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import com.coui.appcompat.animation.COUIEaseInterpolator;
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter;
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData;
import com.oplus.gallery.business_lib.template.editor.data.TextViewData;
import com.oplus.gallery.business_lib.template.editor.ui.EditorUIExecutor;
import com.oplus.gallery.business_lib.template.editor.ui.IEditorUIScheme;
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.display.ScreenUtils;
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility;
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.app.EditorBaseState;
import com.oplus.gallery.videoeditorpage.base.ui.animation.AutoRemovedViewAnimator;
import com.oplus.gallery.videoeditorpage.base.ui.animation.EditorAnimationUtils;
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorHelper;
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig;

import java.util.ArrayList;
import java.util.List;

public abstract class EditorBaseUIController implements View.OnClickListener, BaseRecyclerAdapter.OnItemClickListener, IEditorUIScheme {
    private static final String TAG = "EditorBaseUIController";
    protected static final int ANIMATION_FADE_IN_DURATION = 300;
    protected static final int ANIMATION_FADE_OUT_DURATION = 50;
    protected static final int MONITOR_DEFAULT = 1 << 0;
    protected static final int MONITOR_DOWNLOAD = 1 << 1;

    /**
     * 用于从视频预览view的padding值列表中获取左上右下各个padding
     */
    protected static final int PADDING_LIST_LEFT = 0;
    protected static final int PADDING_LIST_TOP = 1;
    protected static final int PADDING_LIST_RIGHT = 2;
    protected static final int PADDING_LIST_BOTTOM = 3;
    protected static final int PADDINGS_LIST_SIZE = 4;

    /**
     * 视频预览view动画时长
     */
    private static final int VIDEO_EDITOR_VIEW_ANIMATION_DURATION = 417;

    /**
     * 时间显示view动画时长
     */
    private static final int PLAY_BUTTON_TIME_CONTAINER_ANIMATION_DURATION = 150;

    /**
     * 菜单栏动画时长
     */
    private static final int MENU_ANIMATION_DURATION = 300;

    /**
     * 菜单栏平移动画原始位置
     */
    private static final int MENU_ANIMATION_ORIGIN_POSITION = 0;

    /**
     * 视频预览view动画起始属性值和结束属性值
     */
    private static final float VIDEO_EDITOR_VIEW_ANIMATION_START_PROGRESS = 0f;
    private static final float VIDEO_EDITOR_VIEW_ANIMATION_END_PROGRESS = 1f;

    private static final PathInterpolator VIDEO_EDITOR_VIEW_ANIMATION_INTERPOLATOR =
            new PathInterpolator(0.17f, 0.17f, 0.1f, 1.0f);
    private final EditorAnimationUtils.OnEditorAnimationListener mListener = () -> onShowAnimationEnd();
    private final LayoutInflater mLayoutInflater;
    private final IVideoEditAbility mEngineManager;
    protected Context mContext;
    protected ViewGroup mContainer;
    protected BaseRecyclerAdapter mAdapter;
    protected EditorBaseState mEditorState;
    protected OnIconClickListener mOnIconClickListener;
    protected OnViewClickListener mOnViewClickListener;
    protected OnPlayBackClickListener mOnPlayBackClickListener;
    protected ViewGroup mRootView;
    protected ViewGroup mBottomActionBarContainer;
    /**
     * 工具栏区域 通用控件以外的区域
     */
    protected ViewGroup mToolBarMenuContainer;
    /**
     * 工具栏区域 通用控件放这里，播放按钮，旋转，静音等
     */
    protected ViewGroup mToolBarCommonContainer;
    protected EditorUIExecutor mEditorUIExecutor = new EditorUIExecutor();
    protected ViewGroup mPlayButtonTimeContainer;
    protected ImageView mPlayButtonTimeBorder;

    /**
     * state切换时，视频预览view和时间显示/进度条view是否需要做动画
     */
    protected boolean mIsNeedAnimator;

    /**
     * state切换时，菜单栏是否需要做动画
     */
    protected boolean mIsMenuNeedAnimator;

    private int mPermitMonitor = MONITOR_DEFAULT;
    private BottomActionBar mBottomActionBar;
    private ImageView mSoundMuteButton;
    private ImageView mPlayButton;
    private TextView mVideoDurationTextView;
    private TextView mCurrentTimeTextView;
    private OnBottomTitleClickListener mOnBottomTitleClickListener;
    private LocalReceiver mLocalReceiver;
    private BaseActivity mBaseActivity;
    private EditorAppUiConfig mEditorAppUiConfig;

    public EditorBaseUIController(Context context, ViewGroup rootView, EditorBaseState state) {
        this(context, rootView, state, MONITOR_DEFAULT);
    }

    public EditorBaseUIController(Context context, ViewGroup rootView, EditorBaseState state, int permitMonitor) {
        mContext = context;
        if (mContext instanceof BaseActivity) {
            mBaseActivity = (BaseActivity) mContext;
        }
        mRootView = rootView;
        mEditorState = state;
        mEngineManager = state.getEngineManager();
        mLayoutInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mPermitMonitor = permitMonitor;
        if (permitMonitorDownload()) {
            registerDownloadReceiver();
        }
    }

    private boolean permitMonitorDownload() {
        return ((mPermitMonitor & MONITOR_DOWNLOAD) == MONITOR_DOWNLOAD);
    }

    public void onShowAnimationEnd() {

    }

    public void onAppUiStateChanged(AppUiResponder.AppUiConfig uiConfig) {
        if (mEditorAppUiConfig != null) {
            mEditorAppUiConfig.updateConfig(uiConfig, false);
        } else {
            mEditorAppUiConfig = new EditorAppUiConfig(uiConfig, false);
        }
        mEditorUIExecutor.onAppUiConfigChanged(mEditorAppUiConfig);
    }

    public void cancel() {
        unRegisterDownloadReceiver();
    }

    /**
     * 显示对应Controller <p>
     * @param isNeedAnimator 对应state切换时，视频预览view和时间显示/进度条view是否需要做动画. <p>
     * 包括：<p>
     * 1.视频预览view的位置变动动画 <p>
     * 2.时间显示/进度条view的Alpha动画 <p>
     * @param isMenuNeedAnimator 对应state切换时，菜单栏是否需要做动画 <p>
     * 包括：<p>
     * 菜单栏的TranslateX动画
     */
    public void show(boolean isNeedAnimator, boolean isMenuNeedAnimator) {
        mIsNeedAnimator = isNeedAnimator;
        mIsMenuNeedAnimator = isMenuNeedAnimator;
        mContainer = mRootView.findViewById(R.id.videoeditor_ui_framework);
        if (mContainer == null) {
            mContainer = (ViewGroup) mLayoutInflater.inflate(getContentLayoutId(mBaseActivity.getCurrentAppUiConfig()), null);
            mEditorUIExecutor.init(mBaseActivity);
            mPlayButtonTimeContainer = mContainer.findViewById(R.id.editor_btn_play_and_time);
            if (mPlayButtonTimeContainer != null) {
                mPlayButtonTimeContainer.setOnClickListener(this);
            }
            mPlayButton = mContainer.findViewById(R.id.editor_btn_play);
            mSoundMuteButton = mContainer.findViewById(R.id.video_music_on_off);
            mVideoDurationTextView = mContainer.findViewById(R.id.video_duration);
            mCurrentTimeTextView = mContainer.findViewById(R.id.video_current_position);
            mPlayButtonTimeBorder = mContainer.findViewById(R.id.editor_btn_play_and_time_bg_border_shape);
        }
        if (mContainer.getParent() == null) {
            mRootView.addView(mContainer);
        }
        setPlayBtnViewInvisibleIfNeed();
        mContainer.setVisibility(View.VISIBLE);
        mRootView.setVisibility(View.VISIBLE);
        ArrayList<Animator> aniList = new ArrayList<>();
        mBottomActionBarContainer = mContainer.findViewById(R.id.title_bar_container);
        if (getBottomTitleLayoutId() != 0) {
            mBottomActionBarContainer.setVisibility(View.VISIBLE);
            mBottomActionBar = (BottomActionBar) mLayoutInflater.inflate(getBottomTitleLayoutId(), mContainer, false);
            mBottomActionBar.setIsDrawTips(false);
            if (getTitleId() != 0) {
                mBottomActionBar.setTitle(mContext.getString(getTitleId()));
            }
            setBottomTitleClickListener(mBottomActionBar);
            aniList.add(EditorAnimationUtils.createAlphaAnimator(mBottomActionBar, true, ANIMATION_FADE_IN_DURATION));
            mBottomActionBarContainer.addView(mBottomActionBar);
        } else {
            mBottomActionBarContainer.setVisibility(View.GONE);
        }
        mToolBarMenuContainer = mContainer.findViewById(R.id.toolbar_container);
        mToolBarCommonContainer = mContainer.findViewById(R.id.toolbar_common_container);
        if (getMenuLayoutId() != 0) {
            mToolBarMenuContainer.setVisibility(View.VISIBLE);
            View menuView = mLayoutInflater.inflate(getMenuLayoutId(), mToolBarMenuContainer, true);
            aniList.add(EditorAnimationUtils.createAlphaAnimator(menuView, true, ANIMATION_FADE_IN_DURATION));
            /**
             * 以下场景：
             * 视频-编辑-剪辑/特效/文本 返回 视频-编辑, 回忆-编辑-时长 返回 回忆-时长
             * 右侧菜单栏显示需要做TranslateX动画
             */
            if (mIsMenuNeedAnimator) {
                aniList.add(EditorAnimationUtils.createTranslationAnimator(menuView, false,
                        getMenuAnimationTranslateX(), MENU_ANIMATION_ORIGIN_POSITION, MENU_ANIMATION_DURATION));
            }
        } else {
            mToolBarMenuContainer.setVisibility(View.GONE);
        }
        EditorAnimationUtils.startAnimationSet(aniList, ANIMATION_FADE_IN_DURATION, 0, mListener);
        onShow();
        mEditorUIExecutor.setScheme(this, null);
    }

    public void onShow() {
        onAppUiStateChanged(mBaseActivity.getCurrentAppUiConfig());
    }

    public void hide(boolean animate) {
        hide(animate, true);
        mEditorState.setHasUpdateList(false);
    }

    public void hide(boolean animate, boolean removeContainer) {
        if (mContainer == null) {
            return;
        }
        ViewGroup titleLayout = mContainer.findViewById(R.id.title_bar_container);
        ViewGroup majorMenu = mContainer.findViewById(R.id.toolbar_container);
        if (animate) {
            final ArrayList<AutoRemovedViewAnimator> animatorList = new ArrayList<>();
            ArrayList<AutoRemovedViewAnimator> titleAniList = createHideAnimationForAllChildView(titleLayout);
            if (titleAniList != null) {
                animatorList.addAll(titleAniList);
            }

            ArrayList<AutoRemovedViewAnimator> majorMenuAniList = createHideAnimationForAllChildView(majorMenu);
            if (majorMenuAniList != null) {
                animatorList.addAll(majorMenuAniList);
            }

            EditorAnimationUtils.startAnimationSet(animatorList);
        } else {
            if (titleLayout != null) {
                titleLayout.removeAllViews();
            }
            if (majorMenu != null) {
                majorMenu.removeAllViews();
            }
        }
        if (removeContainer) {
            mContainer.setVisibility(View.INVISIBLE);
            mRootView.removeView(mContainer);
            mRootView.requestLayout();
        }
    }

    public ViewGroup getBottomBarContainer() {
        if (mBottomActionBarContainer == null) {
            mBottomActionBarContainer = mContainer.findViewById(R.id.title_bar_container);
        }
        return mBottomActionBarContainer;
    }

    public void switchPlayButtonStatus() {
        if (mPlayButton != null) {
            if (mEngineManager.isPlaying()) {
                GLog.d(TAG, "onPlayStatusChange() playing");
                mPlayButton.setSelected(true);
                mPlayButton.setContentDescription(mContext.getString(R.string.videoeditor_editor_pause_description));
            } else {
                GLog.d(TAG, "onPlayStatusChange() not playing");
                mPlayButton.setSelected(false);
                mPlayButton.setContentDescription(mContext.getString(R.string.videoeditor_editor_play_description));
            }
        }
    }

    public ImageView getSoundMuteButton() {
        return mSoundMuteButton;
    }

    public TextView getCurrentTimeTextView() {
        return mCurrentTimeTextView;
    }

    public TextView getVideoDurationTextView() {
        return mVideoDurationTextView;
    }

    public void hidePlayButtonTimeContainer() {
        if (mPlayButtonTimeContainer != null) {
            mPlayButtonTimeContainer.setVisibility(View.GONE);
        }
        hideSoundMuteButton();
    }

    public void hideSoundMuteButton() {
        if (mSoundMuteButton != null) {
            mSoundMuteButton.setVisibility(View.GONE);
        }
    }

    public BottomActionBar getBottomActionBar() {
        if (mBottomActionBar != null) {
            return mBottomActionBar;
        }
        return null;
    }

    public ViewGroup getToolBarContainer() {
        if (mToolBarMenuContainer == null) {
            mToolBarMenuContainer = mContainer.findViewById(R.id.toolbar_container);
        }
        return mToolBarMenuContainer;
    }

    public void updateCurrentTimeTextView() {
        if (mCurrentTimeTextView != null) {
            mCurrentTimeTextView.setText(VideoEditorHelper.formatTimeWithMillisByDuration(mContext, mEngineManager.getCurrentTime(),
                    mEngineManager.getTotalTime()));
        }
    }

    public void updateCurrentTimeTextView(long millis, long duration) {
        if (mCurrentTimeTextView != null) {
            mCurrentTimeTextView.setText(VideoEditorHelper.formatTimeWithMillisByDuration(mContext, millis, duration));
        }
    }

    public void updateVideoDurationTextView(long millis) {
        if (mVideoDurationTextView != null) {
            mVideoDurationTextView.setText(VideoEditorHelper.formatTimeWithMillis(mContext, VideoEditorHelper.getVideoCurrentUnitTime(millis)));
        }
    }

    public void addToMajorMenuContainer(int layoutId) {
        int count = getToolBarContainer().getChildCount();
        if (count > 0) {
            ArrayList<AutoRemovedViewAnimator> hideAnimateList = createHideAnimationForAllChildView(getToolBarContainer());
            EditorAnimationUtils.startAnimationSet(hideAnimateList);
        }
        ViewGroup subMenu = (ViewGroup) mLayoutInflater.inflate(layoutId, getToolBarContainer(), false);
        getToolBarContainer().addView(subMenu);
        Animator animator = EditorAnimationUtils.createAlphaAnimator(subMenu, true, ANIMATION_FADE_IN_DURATION);
        animator.start();
    }

    private ArrayList<AutoRemovedViewAnimator> createHideAnimationForAllChildView(ViewGroup parent) {
        if (parent == null) {
            return null;
        }

        int childCount = parent.getChildCount();
        if (childCount == 0) {
            return null;
        }
        ArrayList<AutoRemovedViewAnimator> animatorList = new ArrayList<>();
        for (int i = 0; i < childCount; i++) {
            final View child = parent.getChildAt(i);
            ObjectAnimator animator = EditorAnimationUtils.createAlphaAnimator(child, false, ANIMATION_FADE_OUT_DURATION);
            animatorList.add(new AutoRemovedViewAnimator(child, animator));
        }
        return animatorList;
    }


    public void onDestroy() {
        if (mContainer != null) {
            mContainer.removeAllViews();
            mRootView.removeView(mContainer);
        }
        cancel();
    }

    public void addExtraViewToRootView(View view, boolean animate) {
        if ((mRootView != null) && (view != null)) {
            mRootView.addView(view);
            if (animate) {
                EditorAnimationUtils.startAlphaAnimation(view, true, 0, ANIMATION_FADE_IN_DURATION, null);
            }
        }
    }

    public void removeExtraViewFromRootView(final View view, boolean animate) {
        if ((mRootView != null) && (view != null)) {
            if (animate) {
                EditorAnimationUtils.startAlphaAnimation(view, false, 0, ANIMATION_FADE_IN_DURATION,
                        new EditorAnimationUtils.OnEditorAnimationListener() {
                            @Override
                            public void onAnimationEnd() {
                                if (mRootView != null) {
                                    mRootView.removeView(view);
                                }
                            }
                        });
            } else {
                mRootView.removeView(view);
            }
        }
    }

    /**
     * 代替View.getGlobalVisibleRect(Rect r)使用，用于获取view区域。
     * 当view不可见，view.getGlobalVisibleRect会失败
     * 本方法当view不可见时，也能正常获取到区域。
     *
     * @param view 需要获取 区域的view
     * @param r    当返回false时，该参数可用
     * @return true：正常获取到了区域
     */
    protected boolean getGlobalRect(View view, Rect r) {
        if (view == null) {
            GLog.e(TAG, "getGlobalRect: view == null");
            return false;
        }
        if (view.getGlobalVisibleRect(r)) {
            return true;
        }
        if (view.isLaidOut()) {
            GLog.d(TAG, "getGlobalRect: getGlobalVisibleRect fail,use getXXX");
            r.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
            return true;
        } else {
            ViewGroup.LayoutParams lp = view.getLayoutParams();
            if (lp instanceof ViewGroup.MarginLayoutParams) {
                GLog.d(TAG, "getGlobalRect: getGlobalVisibleRect fail,use MarginLayoutParams");
                ViewGroup.MarginLayoutParams mlp = (ViewGroup.MarginLayoutParams) lp;
                r.set(mlp.leftMargin,
                        mlp.topMargin,
                        mlp.leftMargin + view.getMeasuredWidth(),
                        mlp.topMargin + view.getMeasuredHeight());
            } else {
                GLog.w(TAG, "getGlobalRect: getGlobalVisibleRect fail,use Measured size,it will lost top,left");
                r.set(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
            }
            return (view.getMeasuredWidth() > 0) && (view.getMeasuredHeight() > 0);
        }
    }

    public abstract int getBottomTitleLayoutId();

    public abstract int getMenuLayoutId();

    public abstract int getTitleId();

    protected int[] getResourceIdArrays(Context context, int resourceArrayResId) {
        TypedArray typedArray = context.getResources().obtainTypedArray(resourceArrayResId);
        int resourcesSize = typedArray.length();
        int[] resourceIds = new int[resourcesSize];
        for (int i = 0; i < resourcesSize; i++) {
            resourceIds[i] = typedArray.getResourceId(i, 0);
        }
        typedArray.recycle();
        return resourceIds;
    }

    private String[] getResourceStringArrays(Context context, int resourceArrayResId) {
        TypedArray typedArray = context.getResources().obtainTypedArray(resourceArrayResId);
        int resourcesSize = typedArray.length();
        String[] resourceIds = new String[resourcesSize];
        for (int i = 0; i < resourcesSize; i++) {
            resourceIds[i] = typedArray.getString(i);
        }
        typedArray.recycle();
        return resourceIds;
    }

    protected ArrayList<EditorMenuItemViewData> initAdapterData(Context context, int idArrayResId,
                                                                int iconArrayResId, int textArrayResId) {
        ArrayList<EditorMenuItemViewData> data = new ArrayList<>();
        int[] ids = getResourceIdArrays(context, idArrayResId);
        int[] icons = getResourceIdArrays(context, iconArrayResId);
        int[] texts = getResourceIdArrays(context, textArrayResId);
        int size = Math.min(Math.min(ids.length, icons.length), texts.length);
        for (int i = 0; i < size; i++) {
            data.add(new EditorMenuItemViewData(
                    ids[i], true, true, false, icons[i], null,
                    texts[i], -1, null, TextUtil.EMPTY_STRING, 0,
                    false, false, false));
        }
        return data;
    }

    protected ArrayList<TextViewData> initTextAdapterData(Context context, int idArrayResId, int iconArrayResId, int textArrayResId) {
        ArrayList<TextViewData> data = new ArrayList<>();
        int[] ids = getResourceIdArrays(context, idArrayResId);
        int[] icons = getResourceIdArrays(context, iconArrayResId);
        int[] texts = getResourceIdArrays(context, textArrayResId);
        int size = Math.min(Math.min(ids.length, icons.length), texts.length);
        for (int i = 0; i < size; i++) {
            data.add(new TextViewData(ids[i], true, true, false, icons[i], null, texts[i], -1, null, 0));
        }
        return data;
    }

    @Override
    public void onClick(View v) {
        if (DoubleClickUtils.isFastDoubleClick()) {
            GLog.w(TAG, "onClick() isFastDoubleClick v = " + v);
            return;
        }
        if (mOnBottomTitleClickListener != null) {
            mOnBottomTitleClickListener.onBottomTitleClick(v);
        }
    }

    @Override
    public void onItemClick(View v, int position, Object item) {
        if (mOnIconClickListener != null) {
            mOnIconClickListener.onIconClick(v, position, item);
        }
    }

    @Override
    public void onItemSelected(View v, int position, Object item) {

    }

    @Override
    public void onItemUnselected(View view, int position, Object item) {

    }

    public int getCurrentSelection() {
        if (mAdapter != null) {
            return mAdapter.getSelectedPosition();
        }
        return -1;
    }

    public BaseRecyclerAdapter getAdapter() {
        return mAdapter;
    }

    public void onDataSetChanged() {
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    public void notifyDataSetChanged() {
        if (mAdapter != null) {
            mAdapter.notifyDataSetChanged();
        }
    }

    public void setOnBottomTitleClickListener(OnBottomTitleClickListener listener) {
        mOnBottomTitleClickListener = listener;
    }

    public void setOnIconClickListener(OnIconClickListener listener) {
        mOnIconClickListener = listener;
    }

    public void setOnViewClickListener(OnViewClickListener listener) {
        mOnViewClickListener = listener;
    }

    /**
     * 设置预览播放回调监听器
     * @param listener 监听器
     */
    public void setOnPlayBackClickListener(@Nullable OnPlayBackClickListener listener) {
        mOnPlayBackClickListener = listener;
    }

    private void setBottomTitleClickListener(View view) {
        TextView tvCancel = view.findViewById(R.id.editor_id_action_cancel);
        TextView tvDone = view.findViewById(R.id.editor_id_action_done);
        TextView textBtnCancel = view.findViewById(R.id.editor_id_text_action_cancel);
        TextView textBtnDone = view.findViewById(R.id.editor_id_text_action_done);
        TextView tvTitle = view.findViewById(R.id.editor_id_title);
        if (tvCancel != null) {
            tvCancel.setOnClickListener(this);
        }
        if (tvDone != null) {
            tvDone.setOnClickListener(this);
        }
        if (textBtnCancel != null) {
            textBtnCancel.setOnClickListener(this);
        }
        if (textBtnDone != null) {
            textBtnDone.setOnClickListener(this);
        }
        if (tvTitle != null) {
            tvTitle.setOnClickListener(this);
        }
    }

    public interface OnBottomTitleClickListener {
        void onBottomTitleClick(View view);
    }

    public interface OnIconClickListener<T> {
        void onIconClick(View view, int position, T item);
    }

    public interface OnViewClickListener {
        void onClickView(View view);
    }

    /**
     * 预览播放回调监听
     */
    public interface OnPlayBackClickListener {
        void onClickView(View view);
    }

    public void onDownloadBroadcastReceive(Context context, Intent intent) {

    }

    public IntentFilter getDownloadIntentFilter() {
        return null;
    }

    private class LocalReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            onDownloadBroadcastReceive(context, intent);
        }
    }

    private void registerDownloadReceiver() {
        if (mLocalReceiver == null) {
            IntentFilter intentFilter = getDownloadIntentFilter();
            if (intentFilter != null) {
                GLog.d(TAG, "registerDownloadReceiver");
                mLocalReceiver = new LocalReceiver();
                LocalBroadcastManager.getInstance(mContext).registerReceiver(mLocalReceiver, intentFilter);
            }
        }
    }

    private void unRegisterDownloadReceiver() {
        if (mLocalReceiver != null) {
            GLog.d(TAG, "unRegisterDownloadReceiver");
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mLocalReceiver);
            mLocalReceiver = null;
        }
    }

    protected void closeDefaultMoveAnimator(RecyclerView recyclerView) {
        RecyclerView.ItemAnimator itemAnimator = recyclerView.getItemAnimator();
        if (itemAnimator != null) {
            itemAnimator.setMoveDuration(0);
        }
    }

    @NonNull
    @Override
    public ViewGroup getRootView() {
        return mRootView;
    }

    public boolean isLandscapeLayout(AppUiResponder.AppUiConfig config) {
        return EditorUIConfig.isEditorLandscape(config);
    }

    /**
     * 获取右侧菜单栏TranslateX动画距离
     */
    private int getMenuAnimationTranslateX() {
        return mContext.getResources().getDimensionPixelSize(R.dimen.videoeditor_menu_animation_translate_x);
    }

    /**
     * 判断高度是否小于540dp, 与剪辑/特效/文本/时长界面中计算padding时的横屏判断标准一致
     */
    protected boolean isLandscapeLayoutByHeight(AppUiResponder.AppUiConfig config) {
        return ScreenUtils.pixelToDp(config.getWindowHeight().getCurrent()) <= VideoEditorUIConfig.WINDOW_HEIGHT_540;
    }

    /**
     * 获取进出视频-编辑二级页面和回忆-编辑二级页面的动画
     */
    protected ValueAnimator getVideoEditorViewAnimator(View videoEditorView, List<Integer> startPaddings, List<Integer> endPaddings) {
        ValueAnimator videoEditorViewAnim = ValueAnimator.ofFloat(VIDEO_EDITOR_VIEW_ANIMATION_START_PROGRESS,
                VIDEO_EDITOR_VIEW_ANIMATION_END_PROGRESS);
        videoEditorViewAnim.setDuration(VIDEO_EDITOR_VIEW_ANIMATION_DURATION);
        videoEditorViewAnim.setInterpolator(VIDEO_EDITOR_VIEW_ANIMATION_INTERPOLATOR);
        videoEditorViewAnim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                updateVideoEditorViewPosition(videoEditorView,
                        VIDEO_EDITOR_VIEW_ANIMATION_START_PROGRESS,
                        startPaddings,
                        endPaddings);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                updateVideoEditorViewPosition(videoEditorView,
                        VIDEO_EDITOR_VIEW_ANIMATION_END_PROGRESS,
                        startPaddings,
                        endPaddings);
            }
        });

        videoEditorViewAnim.addUpdateListener(animation -> {
            Object value = animation.getAnimatedValue();
            if (value == null) {
                GLog.w(TAG, "getVideoEditorViewAnimator.onAnimationUpdate value is null");
                return;
            }
            updateVideoEditorViewPosition(videoEditorView,
                    (float)value,
                    startPaddings,
                    endPaddings);
        });

        return videoEditorViewAnim;
    }


    /**
     * 进出视频-编辑二级页面和回忆-编辑二级页面时，更新视频预览view的padding
     * @param progress 动画过程中的属性值
     * @param videoEditorView 视频预览View
     */
    private void updateVideoEditorViewPosition(View videoEditorView,
                                               float progress,
                                               List<Integer> startPaddings,
                                               List<Integer> endPaddings) {
        if ((startPaddings != null)
                && (startPaddings.size() >= PADDINGS_LIST_SIZE)
                && (endPaddings != null)
                && (endPaddings.size() >= PADDINGS_LIST_SIZE)) {
            ArrayList<Integer> paddings = new ArrayList<>();

            /**
             * 分别获取起始padding和结束padding, 计算实时padding
             * 按照顺序共有左上右下4个padding
             */
            for (int i = 0; i < PADDINGS_LIST_SIZE; i++) {
                paddings.add(getAnimatorPadding(startPaddings.get(i), endPaddings.get(i), progress));
            }
            videoEditorView.setPadding(paddings.get(PADDING_LIST_LEFT),
                    paddings.get(PADDING_LIST_TOP),
                    paddings.get(PADDING_LIST_RIGHT),
                    paddings.get(PADDING_LIST_BOTTOM));
        }
    }

    /**
     * 获取进入/退出视频-编辑二级页面和回忆-编辑二级页面动画过程中，视频预览view的实时padding值 <p>
     * @param startPadding 动画开始时视频预览view的padding <p>
     * @param endPadding 动画结束时视频预览view的padding <p>
     * @param progress 动画过程属性值 <p>
     * @return 动画过程中视频预览view实时padding, 等于：起始padding + (结束padding - 起始padding) * 动画过程属性值
     */
    private int getAnimatorPadding(int startPadding, int endPadding, float progress) {
        return (int)(startPadding + (endPadding - startPadding) * progress);
    }

    /**
     * 获取时间显示相关view的动画, 包括：<p>
     * 进出 视频-编辑的剪辑/特效/文本 时，时间显示view和背景border的Alpha动画 <p>
     * 进出 回忆-编辑的时长 时，播放进度条view的Alpha动画
     */
    protected ValueAnimator getTimeViewAnimator(View playBtnTimeView) {
        if (playBtnTimeView != null) {
            ValueAnimator animator = EditorAnimationUtils.createAlphaAnimator(playBtnTimeView, true, PLAY_BUTTON_TIME_CONTAINER_ANIMATION_DURATION);
            animator.setInterpolator(new COUIEaseInterpolator());
            animator.setStartDelay(VIDEO_EDITOR_VIEW_ANIMATION_DURATION - PLAY_BUTTON_TIME_CONTAINER_ANIMATION_DURATION);
            animator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    playBtnTimeView.setVisibility(View.VISIBLE);
                }
            });
            return animator;
        } else {
            return null;
        }
    }

    /**
     * 以下场景：<p>
     * 视频-编辑 进出 视频-编辑-剪辑 <p>
     * 视频-编辑 进出 视频-编辑-特效 <p>
     * 视频-编辑 进出 视频-编辑-文本 <p>
     * 时间显示view（包含显示按钮和背景border）需要做Alpha动画，需要先设置不可见
     */
    private void setPlayBtnViewInvisibleIfNeed() {
        if (mIsNeedAnimator) {
            if (mPlayButtonTimeContainer != null) {
                mPlayButtonTimeContainer.setVisibility(View.INVISIBLE);
            }

            if (mPlayButtonTimeBorder != null) {
                mPlayButtonTimeBorder.setVisibility(View.INVISIBLE);
            }
        }
    }
}
