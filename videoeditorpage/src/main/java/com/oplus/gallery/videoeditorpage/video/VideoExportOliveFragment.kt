/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorExportOliveState.kt
 ** Description: 视频导出实况照片
 ** Version: 1.0
 ** Date: 2025/6/6
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/6        1.0        created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video

import android.animation.Animator
import android.app.Activity
import android.content.DialogInterface
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.EXPORT_OLIVE_FRAGMENT
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.foundation.ui.dialog.ProgressDialog
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_DRAGGING
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_IDLE
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_SETTLING
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder.AppUiConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils.dpToPixel
import com.oplus.gallery.foundation.util.display.ScreenUtils.pixelToDp
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.base.ui.BottomActionBar
import com.oplus.gallery.videoeditorpage.base.ui.animation.EditorAnimationUtils
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig.FORM_COMMON_PAGE
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig.FORM_TRIM_PAGE
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig.getToolBarContentWidthForTrimSize
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig.getViewScaleByWindowSize
import com.oplus.gallery.videoeditorpage.base.util.VideoEditorUIConfig.getViewSizeByScale
import com.oplus.gallery.videoeditorpage.engine.GalleryEditorVideoView
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoThumbnailView
import com.oplus.gallery.videoeditorpage.engine.IGalleryThumbController.ThumbScrollerListener
import com.oplus.gallery.videoeditorpage.video.ui.ExportPlayBackView
import com.oplus.gallery.videoeditorpage.video.ui.VideoOliveCoverView
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorExportOliveViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 视频导出实况照片
 */
@RouterNormal(EXPORT_OLIVE_FRAGMENT)
class VideoExportOliveFragment : BaseFragment(), View.OnClickListener {

    private val activity: Activity by lazy { requireActivity() }

    /**
     * 与activity共享viewmadel
     */
    private val viewModel: EditorExportOliveViewModel by viewModels(ownerProducer = { requireActivity() })

    /**
     * 视频显示窗口
     */
    private var galleryEditorVideoView: GalleryEditorVideoView? = null
    private lateinit var previewArea: RelativeLayout
    private lateinit var leftEdgeView: View
    private lateinit var rightEdgeView: View

    /**
     * 预览播放控件
     */
    private lateinit var exportPlayBackView: ExportPlayBackView
    private lateinit var rootView: View
    private lateinit var toolbarContainer: RelativeLayout

    /**
     * 视频轴布局
     */
    private lateinit var thumbLayout: FrameLayout

    /**
     * 顶部actionbar
     */
    private lateinit var bottomActionBar: BottomActionBar
    private lateinit var tvCancel: TextView
    private lateinit var tvDone: TextView

    /**
     * 视频帧缩图轴
     */
    private lateinit var thumbScrollView: GalleryVideoThumbnailView

    /**
     * 时间线选帧框容器布局
     */
    private lateinit var thumbOliveCoverView: VideoOliveCoverView

    /**
     * 导出实况任务进度框弹窗
     */
    private var oliveProcessDialog: ProgressDialog? = null

    /**
     * 这里标识的是一个手势操作后，带来的一些列连带效应（跟手滚动、惯性滑动、极限阻尼），一直持续到禁止态结束
     * 即：当前可滚动view的内容位置变更（scroll、fling、overScroll）是否为用户触发。
     * <p>
     * true - 用户触发：
     * 1.当前状态处于SCROLL_STATE_DRAGGING
     * 2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
     * <p>
     * false - 非用户触发。
     */
    private var isThumbScrollViewScrollFromUserOp = false

    /**
     * 边距值
     */
    private var titleBarHeight: Int = 0
    private var previewAreaMarginTop: Int = 0
    private var previewAreaMarginTopLandscape: Int = 0
    private var toolBarMenuHeight: Int = 0
    private var toolBarMenuHeightLandscape = 0
    private var menuBarWidthLandscape = 0
    private var previewPlaybackHeight = 0
    private var previewPlaybackMarginTop = 0

    private var engineManager: IVideoEditAbility? = null

    /**
     * 记录滚动状态
     */
    private var thumbScrollViewLastScrollState = SCROLL_STATE_IDLE

    override fun getLayoutId(): Int {
        return R.layout.videoeditor_editor_exportolive_layout
    }

    override fun onAppUiStateChanged(uiConfig: AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        adaptViewArea(uiConfig)
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        subscribeFromViewModel()
        initView(view)
        initData()
    }

    /**
     * 订阅ViewModel中LiveData
     */
    private fun subscribeFromViewModel() {
        viewModel.initEngineFinish.observe(viewLifecycleOwner) { isFinish ->
            if (isFinish) {
                engineManager = viewModel.videoEngineManager
                galleryEditorVideoView?.apply {
                    engineManager?.createLiveWindow(this, true)
                    adaptViewArea(getCurrentAppUiConfig())
                    initThumbScrollView(thumbScrollView)
                    viewModel.notifyEditorVideoBrighten(surfaceView)
                }
            }
        }

        viewModel.scrollThumbToTime.observe(viewLifecycleOwner) { value ->
            scrollThumbViewToTime(value)
        }

        viewModel.thumbStopScroll.observe(viewLifecycleOwner) { isStop ->
            if (isStop) thumbScrollView.stopScroll()
        }

        viewModel.isShowCompileDialog.observe(viewLifecycleOwner) { isShow ->
            if (isShow) showCompileDialog() else oliveProcessDialog?.dismiss()
        }

        viewModel.exportProgress.observe(viewLifecycleOwner) { value ->
            oliveProcessDialog?.setProgress(value)
        }

        viewModel.exportSuccess.observe(viewLifecycleOwner) { data ->
            exportSuccess(data.resultUri, data.thumbBitmap)
        }
    }

    private fun initView(view: View) {
        titleBarHeight = resources.getDimensionPixelSize(R.dimen.video_editor_titlebar_height)
        previewAreaMarginTop = resources.getDimensionPixelSize(R.dimen.video_editor_preview_safe_area_size)
        previewAreaMarginTopLandscape = resources.getDimensionPixelSize(R.dimen.video_editor_preview_safe_area_size_landscape)
        toolBarMenuHeight = resources.getDimensionPixelSize(R.dimen.video_editor_toolbar_height)
        toolBarMenuHeightLandscape = resources.getDimensionPixelSize(R.dimen.video_editor_toolbar_height_landscape)
        menuBarWidthLandscape = resources.getDimensionPixelSize(R.dimen.video_editor_toolbar_width_landscape)
        previewPlaybackHeight = resources.getDimensionPixelSize(R.dimen.video_editor_toolbar_common_height)
        previewPlaybackMarginTop = resources.getDimensionPixelSize(R.dimen.video_editor_toolbar_common_area_margin_top)

        rootView = view.findViewById(R.id.videoeditor_ui_framework)
        galleryEditorVideoView = view.findViewById(R.id.video_engine_view)
        previewArea = view.findViewById(R.id.preview_area)
        leftEdgeView = view.findViewById(R.id.start_safe_area)
        rightEdgeView = view.findViewById(R.id.end_safe_area)
        exportPlayBackView = view.findViewById(R.id.preview_export_video)
        toolbarContainer = view.findViewById(R.id.toolbar_container)
        thumbLayout = view.findViewById(R.id.export_olive_thumb_layout)
        bottomActionBar = view.findViewById(R.id.export_title_bar)
        tvCancel = view.findViewById(R.id.editor_id_text_action_cancel)
        tvDone = view.findViewById(R.id.editor_id_text_action_done)
        thumbOliveCoverView = view.findViewById(R.id.video_cover_view)
        thumbScrollView = view.findViewById(R.id.video_thumb_view)
        thumbScrollView.addOnLayoutChangeListener(onLayoutChangeListener)


        exportPlayBackView.setOnClickListener(this)
        tvCancel.setOnClickListener(this)
        tvDone.setOnClickListener(this)

        initFadeInAnim()
    }

    private fun initData() {
        bottomActionBar.setIsDrawTips(false)
        bottomActionBar.setTitle(getString(R.string.videoeditor_editor_export_olive))
        bottomActionBar.setTitleViewLayout()
    }

    /**
     * 布局更改监听
     */
    private val onLayoutChangeListener =
        View.OnLayoutChangeListener { view, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            // 判断布局是否真正发生改变
            if ((left != oldLeft) || (right != oldRight) || (top != oldTop) || (bottom != oldBottom)) {
                engineManager?.let {
                    initThumbScrollView(view)
                } ?: return@OnLayoutChangeListener
            }
        }

    /**
     * 初始化缩图轴
     * @param view
     */
    private fun initThumbScrollView(view: View) {
        // 在view尚未附加到窗口时，会等待视图附加后在执行任务
        view.post {
            // 由于布局改变时需要更新缩图轴与选帧框位置，因此当布局发生改变时都需要设置一下缩图轴
            engineManager?.showVideoOliveThumbLoader(thumbScrollView)
            thumbScrollView.setScrollListener(scrollChangeListener)
            // 滚动缩图轴到目标位置
            scrollThumbViewToTime(viewModel.getCoverTime())
        }
    }

    /**
     * 滚动缩图轴到目标位置
     * @param time
     */
    private fun scrollThumbViewToTime(time: Long) {
        val totalTime = viewModel.getTotalTime()
        val xPosition = viewModel.getTimeConvertPosition(time, thumbScrollView.pixelPerMicrosecond)
        thumbOliveCoverView.changeSelectViewOffSet(time, totalTime, true, xPosition)
        thumbScrollView.scrollTo(xPosition, 0)
    }

    /**
     * 缩图轴滚动监听
     */
    private val scrollChangeListener: ThumbScrollerListener = object : ThumbScrollerListener {
        override fun onScrolled(pos: Int) {
            engineManager?.let {
                if (isThumbScrollViewScrollFromUserOp) {
                    refreshTimeAfterScroll(viewModel.getPositionConvertTime(pos, thumbScrollView.pixelPerMicrosecond))
                }
            }
        }

        override fun onScrollStateChanged(newState: Int) {
            // 更新当前缩图轴的滚动状态
            viewModel.setThumbScrollState(newState)
            /*
               此次操作为用户触发：
               1.当前状态处于SCROLL_STATE_DRAGGING
               2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
             */
            if ((newState == SCROLL_STATE_DRAGGING)
                || ((thumbScrollViewLastScrollState == SCROLL_STATE_DRAGGING) && (newState == SCROLL_STATE_SETTLING))
            ) {
                if (!isThumbScrollViewScrollFromUserOp) {
                    engineManager?.pause()
                    // 当为用户触发滚动操作时显示时间线，隐藏选帧框和圆点
                    thumbOliveCoverView.showTrimTime = true
                }

                isThumbScrollViewScrollFromUserOp = true
            }

            /*
              用户触发的操作停止：当前状态为SCROLL_STATE_IDLE 且 变更前处于用户触发态 mIsThumbScrollViewScrollFromUserOp = true
             */
            if (isThumbScrollViewScrollFromUserOp && (newState == SCROLL_STATE_IDLE)) {
                isThumbScrollViewScrollFromUserOp = false
            }
            thumbScrollViewLastScrollState = newState
            // 当缩图轴停止滚动时，隐藏时间线，显示选帧框和圆点
            if (thumbScrollViewLastScrollState == SCROLL_STATE_IDLE) {
                thumbOliveCoverView.showTrimTime = false
                if (viewModel.shouldAutoPlayWhenScrollIdle) exportPlayBackView.performClick()
                viewModel.shouldAutoPlayWhenScrollIdle = false
            }
        }
    }

    /**
     * 滚动后更新播放进度和播放时间
     * @param time 当前时间
     */
    private fun refreshTimeAfterScroll(time: Long) {
        val totalTime = viewModel.getTotalTime()
        val videoProgress = time.coerceIn(0, totalTime)
        val x = viewModel.getTimeConvertPosition(time, thumbScrollView.pixelPerMicrosecond)
        thumbOliveCoverView.changeSelectViewOffSet(videoProgress, totalTime, position = x)
        thumbOliveCoverView.updateTrimTime(activity, videoProgress, totalTime)
        viewModel.updateTimeAndEngine(videoProgress)
    }

    /**
     * 显示实况导出时的加载框
     */
    private fun showCompileDialog() {
        if (oliveProcessDialog == null) {
            val builder = ProgressDialog.Builder(activity, false)
            builder.setTitle(R.string.videoeditor_editor_exporting_olivephoto)
            builder.setCancelable(false)
            builder.setPositiveButton(
                activity.resources.getString(android.R.string.cancel)
            ) { _: DialogInterface?, _: Int ->
                viewModel.stopExportOliveTask()
            }
            oliveProcessDialog = builder.build().show()
        } else {
            if (oliveProcessDialog?.isShowing() == false) {
                oliveProcessDialog?.setProgress(0)
                oliveProcessDialog?.show()
            }
        }
    }

    /**
     * 预览按钮以及视频轴和底部ActionBar渐显动画
     */
    private fun initFadeInAnim() {
        val aniList = ArrayList<Animator>()
        val playAnim = EditorAnimationUtils.createAlphaAnimator(exportPlayBackView, true, ANIMATION_EXPORT_FADE_IN_DURATION)
        val thumbAnim = EditorAnimationUtils.createAlphaAnimator(thumbLayout, true, ANIMATION_EXPORT_FADE_IN_DURATION)
        val bottomActionBarAnim = EditorAnimationUtils.createAlphaAnimator(bottomActionBar, true, ANIMATION_EXPORT_FADE_IN_DURATION)
        aniList.add(playAnim)
        aniList.add(thumbAnim)
        aniList.add(bottomActionBarAnim)
        EditorAnimationUtils.startAnimationSet(aniList, ANIMATION_EXPORT_FADE_IN_DURATION, 0, null)
    }

    /**
     * 根据屏幕尺寸和横竖屏方向调整UI布局
     * @param config
     */
    private fun adaptViewArea(config: AppUiConfig) {
        (activity as? VideoEditorActivity)?.let {
            VideoEditorUIConfig.adaptSafeAreaInVideo(it, leftEdgeView, rightEdgeView, config)
        }
        zoomToolBar(config)
        getVideoEditorPadding(config)
    }

    /**
     * 根据横竖屏方向来设置预览控件边距
     * @param config
     */
    private fun getVideoEditorPadding(config: AppUiConfig) {
        val isLandscape = isLandscapeLayout(config)
        val paddingLeft = leftEdgeView.layoutParams?.width ?: 0
        var paddingTop = previewAreaMarginTop
        val paddingRight = rightEdgeView.layoutParams?.width ?: 0
        var paddingBottom = titleBarHeight + getViewSizeByScale(toolBarMenuHeight, config, FORM_COMMON_PAGE) +
                previewPlaybackHeight + previewPlaybackMarginTop
        if (isLandscape) {
            paddingTop = previewAreaMarginTopLandscape
            paddingBottom = titleBarHeight + getViewSizeByScale(toolBarMenuHeightLandscape, config, FORM_TRIM_PAGE)
        }
        /*
          当前位置的padding等于应该显示位置的padding时，说明位置已正确，直接返回
          取paddingBottom做判断
         */
        if (galleryEditorVideoView?.paddingBottom == paddingBottom) {
            return
        }
        galleryEditorVideoView?.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
    }

    /**
     * 根据横竖屏方向来调整工具栏高度和控件缩放比例
     * @param config
     */
    private fun zoomToolBar(config: AppUiConfig) {
        val viewSize = pixelToDp(config.windowWidth.current)
        val scale = getViewScaleByWindowSize(config, FORM_TRIM_PAGE)

        // 根据scale调整工具栏高度
        val toolBarHeight = getViewSizeByScale(R.dimen.video_editor_export_olive_toolbar_height, activity, config, FORM_TRIM_PAGE)
        val layoutParams = toolbarContainer.layoutParams
        layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
        layoutParams.height = toolBarHeight
        toolbarContainer.gravity = Gravity.CENTER_HORIZONTAL
        toolbarContainer.setLayoutParams(layoutParams)
        toolbarContainer.visibility = View.VISIBLE

        // 设置工具栏内容区域宽度
        val toolBarContentSize = dpToPixel(getToolBarContentWidthForTrimSize(viewSize))
        val toolBarContentView = toolbarContainer.findViewById<RelativeLayout>(R.id.export_olive_menu_layout)
        if (toolBarContentView != null) {
            toolBarContentView.layoutParams.width = toolBarContentSize
        }

        // 根据scale，调整缩图列表位置
        val thumbLayout = toolbarContainer.findViewById<FrameLayout>(R.id.export_olive_thumb_layout)
        if (thumbLayout != null) {
            thumbLayout.scaleX = scale
            thumbLayout.scaleY = scale
            val viewH = resources.getDimensionPixelSize(R.dimen.video_editor_export_olive_trim_view_height)
            thumbLayout.translationY = viewH * (NO_SCALE - scale) / 2
        }

        // 根据scale，调整预览按钮位置
        val playBackView = toolbarContainer.findViewById<ExportPlayBackView>(R.id.preview_export_video)
        if (playBackView != null) {
            playBackView.scaleX = scale
            playBackView.scaleY = scale
            val viewMarginTop = resources.getDimensionPixelSize(R.dimen.video_editor_export_olive_playback_view_margin_top)
            playBackView.translationY = viewMarginTop * (NO_SCALE - scale)
        }

        toolbarContainer.requestLayout()
    }

    override fun onClick(view: View) {
        if (DoubleClickUtils.isFastDoubleClick()) {
            return
        }
        val viewId = view.id
        when (viewId) {
            R.id.preview_export_video -> viewModel.clickPreviewBtn()
            R.id.editor_id_text_action_cancel -> activity.finish()
            R.id.editor_id_text_action_done -> viewModel.exportOlive()
            else -> GLog.d(TAG, LogFlag.DL) { "click nothing" }
        }
    }

    /**
     * 导出成功跳转大图
     * @param resultUri   资源uri
     * @param transBitmap 过渡资源图片
     */
    private fun exportSuccess(resultUri: Uri?, transBitmap: Bitmap?) {
        lifecycleScope.launch {
            delay(DELAY_TIME)
            (activity as? VideoEditorActivity)?.setResultAndFinish(resultUri, transBitmap)
        }
    }

    /**
     * 在onPause时进行相关状态检测
     * 1、选帧框不在正确位置则直接滚动到正确的起始位置
     * 2、更改自动播放状态为false，防止后台播放
     */
    override fun onPause() {
        super.onPause()
        if (thumbScrollView.scrollState != SCROLL_STATE_IDLE) {
            if (viewModel.isScrollTimeEqualCoverTime().not()) {
                scrollThumbViewToTime(viewModel.getCoverTime())
            }
            viewModel.shouldAutoPlayWhenScrollIdle = false
            thumbScrollView.stopScroll()
        }
    }

    /**
     * 通过窗口高度判断是否为横屏
     */
    private fun isLandscapeLayout(config: AppUiConfig): Boolean {
        return pixelToDp(config.windowHeight.current) <= VideoEditorUIConfig.WINDOW_HEIGHT_540
    }

    override fun onDestroyView() {
        super.onDestroyView()
        thumbScrollView.removeOnLayoutChangeListener(onLayoutChangeListener)
        thumbScrollView.setScrollListener(null)
        viewModel.onDestroy()
    }

    companion object {
        private const val TAG = "VideoExportOliveFragment"
        private const val ANIMATION_EXPORT_FADE_IN_DURATION = 200
        private const val NO_SCALE = 1.0f
        private const val DELAY_TIME = 300L
    }
}