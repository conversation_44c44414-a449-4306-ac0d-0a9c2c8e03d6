/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorInitUIController.java
 * * Description: editor ui controller for preview state.
 * * Version: 1.0
 * * Date : 2017/11/07
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/07    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.video.ui.uicontroller;

import static com.oplus.gallery.business_lib.template.editor.EditorUIConfig.CLEAR_COLOR;
import static com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolderKt.setViewSelectedState;
import static com.oplus.gallery.foundation.tracing.constant.VideoEditorTrackConstant.Value.CLICK_MENU_ITEM_TYPE_ICON;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.basebiz.widget.EditorMenuItemView;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorColorAnimViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuAdapter;
import com.oplus.gallery.business_lib.template.editor.adapter.Selectable;
import com.oplus.gallery.business_lib.template.editor.anim.EditorColorAnimation;
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation;
import com.oplus.gallery.business_lib.template.editor.anim.IPressAnimation;
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData;
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.framework.abilities.config.keys.ConfigID;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.app.EditorBaseState;
import com.oplus.gallery.videoeditorpage.video.viewmodel.EditorInitViewModel;

import java.util.ArrayList;
import java.util.Iterator;

public class EditorInitUIController extends EditorVideoBaseUiController {
    private static final String TAG = "EditorInitUIController";
    private static final int ITEM_TYPE_COMMON = 0;
    private static final int ITEM_TYPE_WATER_MARK = 1;

    private final boolean mSupportWaterMark;
    private final boolean mIsSupportSlowMotionMode;
    private final EditorInitViewModel mEditorInitViewModel;
    private boolean mHaveWaterMark;
    private OnIconClickListener mOnIconClickListener;

    public EditorInitUIController(
        Context context,
        ViewGroup rootView,
        EditorBaseState state,
        boolean supportWatermark,
        boolean isSupportSlowMotionMode,
        ShowAnimationEndListener listener
    ) {
        super(context, rootView, state, listener);
        mSupportWaterMark = supportWatermark;
        mIsSupportSlowMotionMode = isSupportSlowMotionMode;
        mEditorInitViewModel = new ViewModelProvider((AppCompatActivity) mContext).get(EditorInitViewModel.class);
    }

    @Override
    public int getBottomTitleLayoutId() {
        return R.layout.videoeditor_video_editor_init_bottom_action_bar_layout;
    }

    @Override
    public int getMenuLayoutId() {
        return R.layout.videoeditor_video_editor_menu_list_layout;
    }

    @Override
    public void onShow() {
        mMenuListView = mContainer.findViewById(R.id.horizontal_list);
        mMenuListView.keepLastFocusItem(true);
        mMenuListView.setHorizontalFlingFriction(EditorLinearListView.PREVIEW_FRICTION_COEFFICIENT);
        LinearLayoutManager layoutManager = new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false);
        mMenuListView.setLayoutManager(layoutManager);
        setMenuListViewMarginTop();
        ArrayList<EditorMenuItemViewData> data = initAdapterData(mContext,
            R.array.videoeditor_video_editor_init_id_array,
            R.array.videoeditor_video_editor_init_icon_array,
            R.array.videoeditor_video_editor_init_text_array);
        checkIconTextMenuData(data);
        mAdapter = new EditorMenuAdapter(mContext, data) {
            private int mDefaultBgColor = mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask);

            @Override
            public void bindData(BaseRecycleViewHolder viewHolder, int position, EditorMenuItemViewData item) {
                super.bindData(viewHolder, position, item);
                setNameTextViewMarginTop(viewHolder, com.oplus.gallery.basebiz.R.dimen.base_editor_menu_item_name_textview_margin_top);
                EditorMenuItemView menuItemView = getMenuItemView(viewHolder);
                menuItemView.setSelectedDrawFlag(EditorMenuItemView.DRAW_BACKGROUND_COLOR
                    | EditorMenuItemView.DRAW_CENTER_ICON);
                if ((item.getViewId() == R.id.videoeditor_id_video_editor_init_watermark)) {
                    setViewSelectedState(viewHolder, mHaveWaterMark ? Selectable.SELECTED : Selectable.UNSELECTED);
                } else {
                    menuItemView.setItemBackgroundColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask));
                }
            }

            @Override
            public BaseRecycleViewHolder createViewHolder(View itemView, int viewType) {
                BaseRecycleViewHolder holder = null;
                if (viewType == ITEM_TYPE_WATER_MARK) {
                    EditorColorAnimViewHolder animHolder = new EditorColorAnimViewHolder(
                        itemView,
                        new EditorColorAnimation(),
                        new EditorPressAnimation());
                    holder = animHolder;
                    setSupportPressAnim(animHolder);

                    animHolder.setSelectedAnimEnable(true);
                    animHolder.setSelectedAnimView(animHolder);
                    animHolder.setDisableColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask));
                    animHolder.setUnselectedColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask));
                    animHolder.setSelectedColor(COUIContextUtil.getAttrColor(mContext, com.oplus.gallery.foundation.ui.R.attr.gColorPrimary));
                } else {
                    holder = super.createViewHolder(itemView, viewType);
                    if (holder instanceof IPressAnimation) {
                        final EditorMenuItemView menuItemView = holder.findViewById(com.oplus.gallery.basebiz.R.id.base_editorMenuItem_item_icon);
                        ((IPressAnimation) holder).setPressAnimListener(progress -> {
                            int srcColor = mDefaultBgColor;
                            if ((menuItemView != null) && (srcColor != CLEAR_COLOR)) {
                                menuItemView.setItemBackgroundColor(EditorUIConfig.getAnimAlphaColor(progress, srcColor));
                            }
                        });
                    }
                }
                return holder;
            }

            @Override
            public int getItemViewType(int position) {
                EditorMenuItemViewData item = (EditorMenuItemViewData) getMenuData(position);
                if ((item != null) && (item.getViewId() == R.id.videoeditor_id_video_editor_init_watermark)) {
                    return ITEM_TYPE_WATER_MARK;
                }
                return ITEM_TYPE_COMMON;
            }
        };
        mAdapter.setCanUnselectCurrentPosition(true);
        mAdapter.setItemClickListener(this);
        mMenuListView.setAdapter(mAdapter);
        mEditorInitViewModel.initViewShowCount(data, CLICK_MENU_ITEM_TYPE_ICON);
        initScrollListener();
        super.onShow();
    }

    /**
     * 设置mMenuListView的marginTop属性，使视频的菜单列表显示与图片菜单列表对齐
     */
    private void setMenuListViewMarginTop() {
        int marginTop = mContext.getResources().getDimensionPixelOffset(com.oplus.gallery.basebiz.R.dimen.base_editor_title_bar_container_margin_top);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) mMenuListView.getLayoutParams();
        layoutParams.setMargins(0, marginTop, 0, 0);
        mMenuListView.setLayoutParams(layoutParams);
    }

    /**
     * 设置Container的背景色
     * 层级关系 从上到下：mPreviewArea  --> mContainer    --> surfaceView     --> activity
     *                  实际预览大小      透明，接近全屏      等同mContainer           灰黑色
     * 在adaptPreviewArea 会将surfaceView 调整到预览大小
     * 1，一般情况下，surfaceView的初始化 跟调整大小，以及出视频数据都很快，能看到预览大小的视频数据
     * 2，个别情况下，调整大小延后一点，视频数据出现比较快，可以看到视频数据接近全屏，下一帧才调整到预览大小
     * 3，QR等性能更差一点的系统上，调整大小靠后，视频数据出来得也慢，导致surfaceView按接近全屏大小透出
     * 解法：
     * 1，默认mContainer 设置同activity的背景色，完全遮挡surfaceView
     * 2，在surfaceView调整到预览大小时，还原mContainer颜色为透明
     *
     * @param color 颜色
     */
    @Override
    protected void updateContainerBackgroundColor(int color) {
        mRootView.setBackgroundColor(color);
    }

    private void initScrollListener() {
        mMenuListView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    startExposure(mEditorInitViewModel);
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                if (mEditorInitViewModel.getFirstVisibleState()) {
                    startExposure(mEditorInitViewModel);
                }
            }
        });
    }

    @Override
    public void onItemClick(View view, int position, Object item) {
        if (mOnIconClickListener != null) {
            mOnIconClickListener.onIconClick(view, position);
        }
    }

    @Override
    public void onItemSelected(View view, int position, Object item) {
    }

    @Override
    public void onItemUnselected(View view, int position, Object item) {
        super.onItemUnselected(view, position, item);
    }

    public void setOnIconClickListener(OnIconClickListener listener) {
        mOnIconClickListener = listener;
    }

    public interface OnIconClickListener {
        void onIconClick(View view, int position);
    }

    public void updateWaterMark(boolean haveWaterMark) {
        mHaveWaterMark = haveWaterMark;
    }

    /**
     * 校验并移除不支持的菜单项
     * @param dataList 所有菜单数据
     */
    private void checkIconTextMenuData(ArrayList<EditorMenuItemViewData> dataList) {
        Iterator<EditorMenuItemViewData> it = dataList.iterator();
        while (it.hasNext()) {
            EditorMenuItemViewData data = it.next();
            if (mIsSupportSlowMotionMode && (data.getTextId() == R.string.videoeditor_editor_text_speeder)) {
                GLog.d(TAG, "checkIconTextMenuData SlowMotionMode remove speeder.");
                it.remove();
            }
            if (data.getViewId() == R.id.videoeditor_id_video_editor_init_export_olive) {
                if (!ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_VIDEO_EXTRACT_OLIVE)) {
                    it.remove();
                }
            }
        }
    }
}
