/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorPreviewState.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  <EMAIL>       2017/11/20    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.videoeditorpage.memories.app;

import static com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponderKt.onWidthChanged;

import android.content.Context;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.oplus.gallery.foundation.ui.dialog.SingleEditDialog;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.videoeditorpage.R;
import com.oplus.gallery.videoeditorpage.base.app.EditorBaseState;
import com.oplus.gallery.videoeditorpage.base.ui.ControlBarView;
import com.oplus.gallery.videoeditorpage.base.ui.EditorBaseUIController;
import com.oplus.gallery.videoeditorpage.memories.MemoriesManager;
import com.oplus.gallery.videoeditorpage.memories.ui.uicontroller.EditorPreviewUIController;

public class EditorPreviewState extends EditorBaseState implements EditorBaseUIController.OnIconClickListener {
    private static final String TAG = "EditorPreviewState";

    private static final String TYPE_NAME = "Preview";
    // edit title dialog
    private SingleEditDialog mSingleEditDialog;

    public EditorPreviewState(Context context, ControlBarView controlBarView) {
        super(TYPE_NAME, context, controlBarView);
    }

    @Override
    protected EditorBaseUIController createUIController() {
        EditorPreviewUIController previewUIController = new EditorPreviewUIController(mContext, mControlBarView, this);
        previewUIController.setOnIconClickListener(this);
        return previewUIController;
    }

    @Override
    public boolean done() {
        GLog.d(TAG, "done");
        return super.done();
    }

    @Override
    public void cancel() {
        super.cancel();
        GLog.d(TAG, "cancel");
    }

    @Override
    public void show(boolean isNeedAnimator, boolean isMenuNeedAnimator) {
        super.show(isNeedAnimator, isMenuNeedAnimator);
        GLog.d(TAG, "show");
        setActionDoneEnable(!MemoriesManager.isMemoriesSame());
    }

    @Override
    public void onIconClick(View view, int position, Object item) {
        if (view == null) {
            return;
        }
        int id = view.getId();
        if (id == R.id.videoeditor_id_memories_editor_preview_theme) {
            mEngineManager.reset();
            GLog.d(TAG, "onIconClick videoeditor_id_memories_editor_preview_theme");
            EditorThemeState themeState = new EditorThemeState(mContext, mControlBarView);
            mControlBarView.changeState(themeState);
            //statistic edit them
        } else if (id == R.id.videoeditor_id_memories_editor_preview_music) {
            mEngineManager.reset();
            GLog.d(TAG, "onIconClick videoeditor_id_memories_editor_preview_music");
            EditorMusicState musicState = new EditorMusicState(mContext, mControlBarView);
            mControlBarView.changeState(musicState);
            //statistic edit music
        } else if (id == R.id.videoeditor_id_memories_editor_preview_cover) {
            mEngineManager.reset();
            GLog.d(TAG, "onIconClick videoeditor_id_memories_editor_preview_cover");
            if (mContext instanceof FragmentActivity) {
                MemoriesManager.takeCover((FragmentActivity) mContext, this);
            }
            //statistic edit cover
        } else if (id == R.id.videoeditor_id_memories_editor_preview_title) {
            GLog.d(TAG, "onIconClick videoeditor_id_memories_editor_preview_title");
            mEngineManager.reset();
            if (mSingleEditDialog == null) {
                mSingleEditDialog = new SingleEditDialog.Builder()
                    .setContext(mContext)
                    .setTitle(mContext.getResources().getString(R.string.videoeditor_rename_memories_title))
                    .setContent(mEngineManager.getThemeTitleText())
                    .setHintText(mContext.getResources().getString(com.oplus.gallery.basebiz.R.string.common_edit_text_hint))
                    .setInputErrorToastResId(R.string.videoeditor_create_title_invalid_string)
                    .setListener(mListener)
                    .create();
            }
            mSingleEditDialog.show();
            //statistic edit title
        } else if (id == R.id.videoeditor_id_memories_editor_preview_photo) {
            mEngineManager.reset();
            GLog.d(TAG, "onIconClick videoeditor_id_memories_editor_preview_photo");
            EditorPhotoState photoState = new EditorPhotoState(mContext, mControlBarView);
            mControlBarView.changeState(photoState);
            //statistic edit photo
        } else if (id == R.id.videoeditor_id_memories_editor_preview_timeset) {
            GLog.d(TAG, "onIconClick videoeditor_id_memories_editor_preview_timeset");
            mEngineManager.reset();
            EditorTimeSetState timeSetState = new EditorTimeSetState(mContext, mControlBarView);
            mControlBarView.changeState(timeSetState);
            //statistic edit timeset
        }
    }

    private SingleEditDialog.ConfirmListener mListener = new SingleEditDialog.ConfirmListener() {
        public void onSaved(String text) {
            MemoriesManager.setTitleText(text);
            mEngineManager.updateThemeCaptionTitle(text);
            mEngineManager.reset();
            setActionDoneEnable(!MemoriesManager.isMemoriesSame());
            if (mSingleEditDialog != null) {
                mSingleEditDialog.dismiss();
                mSingleEditDialog = null;
            }
        }

        public void onCancelled() {
            if (mSingleEditDialog != null) {
                mSingleEditDialog.dismiss();
                mSingleEditDialog = null;
            }
        }
    };

    @Override
    public void onAppUiStateChanged(AppUiResponder.AppUiConfig uiConfig) {
        super.onAppUiStateChanged(uiConfig);
        onWidthChanged(uiConfig, width -> {
            if (mSingleEditDialog != null) {
                mSingleEditDialog.updateIfNeed();
            }
            return null;
        });
    }
}
