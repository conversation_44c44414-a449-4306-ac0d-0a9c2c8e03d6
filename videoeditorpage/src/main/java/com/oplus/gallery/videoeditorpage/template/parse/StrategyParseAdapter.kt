/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - StrategyParseAdapter.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.template.parse

import com.oplus.gallery.videoeditorpage.template.strategy.ClipFxStrategy
import com.oplus.gallery.videoeditorpage.template.strategy.StickerStrategy
import com.oplus.gallery.framework.abilities.videoedit.strategy.TemplateStrategy
import com.oplus.gallery.videoeditorpage.template.strategy.UnsupportedStrategy
import com.google.gson.JsonSerializer
import com.google.gson.JsonDeserializer
import com.google.gson.JsonParseException
import com.google.gson.JsonElement
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonSerializationContext
import java.lang.reflect.Type
import kotlin.jvm.Throws

class StrategyParseAdapter() : JsonSerializer<TemplateStrategy>, JsonDeserializer<TemplateStrategy> {
    companion object {
        private const val STRATEGY_JSON_NAME = "class_name"
    }
    private lateinit var strategiesClassMap: HashMap<String, Class<*>>

    init {
        registerStrategies()
    }

    private fun registerStrategies() {
        // register all strategies
        strategiesClassMap = HashMap()
        strategiesClassMap[ClipFxStrategy.STRATEGY_JSON_NAME] = ClipFxStrategy::class.java
        strategiesClassMap[StickerStrategy.STRATEGY_JSON_NAME] = StickerStrategy::class.java
    }

    @Throws(JsonParseException::class)
    override fun deserialize(json: JsonElement, typeOfT: Type?, context: JsonDeserializationContext): TemplateStrategy? {
        var strategyClass: Class<*>? = UnsupportedStrategy::class.java
        val jsonObject = json.asJsonObject
        if (jsonObject != null) {
            val classNameElement = jsonObject[STRATEGY_JSON_NAME]
            if (classNameElement != null) {
                val classFound = strategiesClassMap[classNameElement.asString]
                if (classFound != null) {
                    strategyClass = classFound
                }
            }
        }
        return context.deserialize<TemplateStrategy>(json, strategyClass)
    }

    override fun serialize(src: TemplateStrategy?, typeOfSrc: Type?, context: JsonSerializationContext): JsonElement? {
        return context.serialize(src)
    }
}