/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEditorRouterRegister.kt
 ** Description: 视频编辑的路由动态注册器实现.
 ** Version: 1.0
 ** Date: 2023/3/30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2023/3/16      1.0		   INIT
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage

import com.oplus.gallery.basebiz.dfm.IVideoEditorRouterRegister
import com.oplus.gallery.router_lib.RouterManager
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.ComponentMeta
import com.oplus.gallery.router_lib.meta.InitMeta
import com.oplus.gallery.router_lib.meta.RouterMeta

/**
 * 视频编辑的路由动态注册器实现.
 *
 * 背景：librouter框架不支持dynamic feature module，在base启动插件中的路由组件会因为找不到而报错。因此通过serviceLoader提供支持。
 *
 * Marked by limao:后面有时间需要研究一套支撑多apk、支持AAB、支持Dynamic Feature Module的路由组件，统一相册的页面路由、服务路由等。
 */
class VideoEditorRouterRegister : IVideoEditorRouterRegister {

    override fun registerComponent() {
        RouterManager.componentCenter.add(
            ComponentMeta(
                "com.oplus.gallery.business_lib.api.IVideoEditorDM",
                "com.oplus.gallery.videoeditorpage.VideoEditorDM"
            )
        )
    }

    override fun registerRouter() {
        RouterManager.routerCenter.add(
            RouterNormal::class.java,
            RouterMeta(
                "router://videoeditor/memories_activity",
                "com.oplus.gallery.videoeditorpage.memories.MemoriesActivity"
            )
        )
        RouterManager.routerCenter.add(
            RouterNormal::class.java,
            RouterMeta(
                "router://videoeditor/video_editor_activity",
                "com.oplus.gallery.videoeditorpage.video.VideoEditorActivity"
            )
        )
        RouterManager.routerCenter.add(
            RouterNormal::class.java,
            RouterMeta(
                "router://videoeditor/video_editor_send_activity",
                "com.oplus.gallery.videoeditorpage.video.VideoEditorSendActivity"
            )
        )
        RouterManager.routerCenter.add(
            RouterNormal::class.java,
            RouterMeta(
                "router://picture/highlight_clips_fragment",
                "com.oplus.gallery.videoeditorpage.highlight.HighLightClipsFragment"
            )
        )
        RouterManager.routerCenter.add(
            RouterNormal::class.java,
            RouterMeta(
                "router://picture/highlight_vlog_play_fragment",
                "com.oplus.gallery.videoeditorpage.highlight.HighLightVLogPlayerFragment"
            )
        )
        RouterManager.routerCenter.add(
            RouterNormal::class.java,
            RouterMeta(
                "router://videoeditor/memories_inner_gallery_activity",
                "com.oplus.gallery.videoeditorpage.memories.MemoriesViewGalleryInnerActivity"
            )
        )
    }

    override fun registerAppInit() {
        RouterManager.appInitCenter.add(
            InitMeta(
                "com.oplus.gallery.videoeditorpage.VideoEditorModule",
                VIDEOEDITOR_SORT, arrayOf("com.coloros.gallery3d", "com.oneplus.gallery")
            )
        )
    }

    companion object {
        /**
         * 参考librouter规则，AppModule固定为0，其他的业务page的固定为100
         */
        private const val VIDEOEDITOR_SORT: Int = 100
    }
}