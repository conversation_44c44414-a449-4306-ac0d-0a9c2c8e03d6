/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorFxViewModel.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/27
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/27		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.viewmodel

import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.videoedit.data.FxInfo
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoThumbnailView
import com.oplus.gallery.videoeditorpage.resource.base.BaseResourceManager
import com.oplus.gallery.videoeditorpage.resource.listener.OnLoadDataListener
import com.oplus.gallery.videoeditorpage.resource.room.bean.FxItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import java.util.*
import kotlin.collections.ArrayList

class EditorFxViewModel : BaseResourceViewModel<FxItem, Objects>() {

    companion object {
        private const val TAG = "EditorFxViewModel"
    }

    var fxItemList: ArrayList<FxItem> = ArrayList()
        private set
    private val loadState = MutableLiveData<LoadDataState>()
    private var prevFxItem: FxItem? = null

    init {
        loadState.postValue(LoadDataState.IDLE)
    }

    fun getLoadState(): LiveData<LoadDataState> {
        return loadState
    }

    fun getCurrentItem(): FxItem? {
        val fxInfo = videoEngineManager?.getCurrentFxInfo() ?: return null
        return getItemByFilepath(fxInfo.filePath, fxItemList)
    }

    fun getIndex(): Int {
        val fxInfo = videoEngineManager?.getCurrentFxInfo() ?: return 0
        return getItemByFilepath(fxInfo.filePath, fxItemList)?.position ?: 0
    }

    fun initDefaultItem(fxItem: FxItem?) {
        videoEngineManager?.setCurrentFxInfo(fxItem?.convertFxInfo())
        prevFxItem = fxItem
    }

    fun loadData() {
        viewModelScope.async(Dispatchers.Default) {
            loadState.postValue(LoadDataState.LOADING)
            currentResourceManager?.loadData(BaseResourceManager.CATEGORY_VIDEO, object : OnLoadDataListener<FxItem> {
                override fun onFinish(code: Int, allItem: List<FxItem>?) {
                    GLog.d(TAG, "loadData.onFinish, allItem = $allItem")
                    val allList = allItem as? ArrayList<FxItem>
                    if (allList != null) {
                        fxItemList.clear()
                        fxItemList.addAll(allList)
                        for (fxItem in fxItemList) {
                            videoEngineManager?.installVideoFx(fxItem.convertFxInfo())
                        }
                        loadState.postValue(LoadDataState.COMPLETE)
                    } else {
                        loadState.postValue(LoadDataState.ERROR)
                    }
                }

                override fun onRefresh(code: Int, item: FxItem) {
                    GLog.d(TAG, "loadData.onRefresh, code = $code, item = $item")
                }

                override fun onError(errCode: Int) {
                    GLog.e(TAG, "loadData.onError, errCode = $errCode")
                    loadState.postValue(LoadDataState.ERROR)
                }
            })
        }
    }

    fun applyVideoFx(fxItem: FxItem?, startPos: Long) {
        fxItem?.convertFxInfo()?.let {
            videoEngineManager?.apply {
                removeVideoFx()
                applyVideoFx(it, startPos)
                if (!TextUtils.isEmpty(it.filePath)) {
                    play(startPos, getTotalTime() ?: 0)
                } else {
                    play()
                }
            }
        }
    }

    fun clickDoneButton(): Boolean {
        var changed = false
        videoEngineManager?.apply {
            val currentFxInfo: FxInfo = getCurrentFxInfo() ?: return false
            if (currentFxInfo.filePath != prevFxItem?.resourcePath) {
                changed = true
                clearLastFx()
                play()
            }
            prevFxItem = getItemByFilepath(currentFxInfo.filePath, fxItemList)
        }
        return changed
    }

    fun clickPlayButton(haveReset: Boolean, currentFxTime: Long) {
        videoEngineManager?.let {
            if (it.isPlaying()) {
                it.pause()
            } else {
                if (haveReset || (it.getCurrentTime()
                            >= (it.getTotalTime() - VIDEO_PLAY_END_CHECK_GAP))
                ) {
                    if (getIndex() > 0) {
                        it.play(currentFxTime, -1)
                    } else {
                        it.play()
                    }
                } else {
                    it.play(it.getCurrentTime(), -1)
                }
            }
        }
    }

    fun restoreFxState(): Boolean {
        var change = false
        val currentFxInfo: FxInfo = videoEngineManager?.getCurrentFxInfo() ?: return false
        prevFxItem?.let {
            if (currentFxInfo.filePath != it.resourcePath) {
                change = true
                applyVideoFx(it, videoEngineManager?.getAppliedFxTime() ?: -1)
            }
        }
        return change
    }

    fun getAppliedFxTime(): Long? {
        return videoEngineManager?.getAppliedFxTime()
    }

    fun setAppliedFxTime(fxTime: Long) {
        videoEngineManager?.setAppliedFxTime(fxTime)
    }

    fun showFxThumbLoader(fxThumbView: GalleryVideoThumbnailView) {
        videoEngineManager?.showFxThumbLoader(fxThumbView)
    }
}