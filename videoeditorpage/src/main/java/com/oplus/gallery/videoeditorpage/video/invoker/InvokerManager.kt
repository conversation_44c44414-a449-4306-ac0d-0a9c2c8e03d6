/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - InvokerManager.kt
 ** Description: 调用者管理类
 ** Version: 1.0
 ** Date: 2025/6/6
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/23        1.0        created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.invoker

import android.os.Bundle
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility
import com.oplus.gallery.videoeditorpage.video.VideoEditorActivity

/**
 * 调用者管理类
 */
class InvokerManager(
    val activity: VideoEditorActivity,
    val galleryVideoEngine: IVideoEditAbility,
    val invokerName: String?
) : BaseInvoker() {

    init {
        activity.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                when (event) {
                    Lifecycle.Event.ON_DESTROY -> invoker.release()
                    else -> Unit
                }
            }
        })
    }

    /**
     * 被代理的invoker对象
     */
    private val invoker: BaseInvoker = when (invokerName) {
        ExportOliveInvoker.VALUE_INVOKER_GALLEY_EXPORT_OLIVE_PAGE -> ExportOliveInvoker(activity, galleryVideoEngine)
        else -> DefaultInvoker(activity, galleryVideoEngine)
    }

    override fun showCustomPage(resId: Int, data: Bundle?) {
        invoker.showCustomPage(resId, data)
    }

    override fun createLiveWindow(parent: ViewGroup, shouldCrop: Boolean): Boolean {
        return invoker.createLiveWindow(parent, shouldCrop)
    }

    override fun handleCreateCheckFail() {
        invoker.handleCreateCheckFail()
    }

    override fun onInitEngineFinish() {
        invoker.onInitEngineFinish()
    }

    override fun onPlayStatus(status: Int) {
        invoker.onPlayStatus(status)
    }

    override fun release() {
        invoker.release()
    }
}