/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - UnsupportedStrategy.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/04
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/09/04		1.0		OPLUS_FEATURE_SENIOR_EDITOR
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.template.strategy

import com.oplus.gallery.framework.abilities.videoedit.IVideoEditAbility
import com.oplus.gallery.framework.abilities.videoedit.strategy.TemplateStrategy

class UnsupportedStrategy : TemplateStrategy() {

    override fun installEffects(videoEngineManager: IVideoEditAbility) {
        // do nothing
    }

    override fun applyTo(videoEngineManager: IVideoEditAbility, directory: String?) {
        // do nothing
    }
}