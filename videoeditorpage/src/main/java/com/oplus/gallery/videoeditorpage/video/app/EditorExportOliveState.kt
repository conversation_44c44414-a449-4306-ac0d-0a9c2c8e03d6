/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorExportOliveState.kt
 ** Description: 导出实况照片功能逻辑实现类
 ** Version: 1.0
 ** Date: 2025/1/24
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/1/24        1.0        created
 *********************************************************************************/
package com.oplus.gallery.videoeditorpage.video.app

import android.content.Context
import android.content.DialogInterface
import android.net.Uri
import android.os.Build
import android.view.View
import android.view.View.OnLayoutChangeListener
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.meicam.sdk.NvsTimeline
import com.oplus.gallery.foundation.fileaccess.helper.MediaStoreUriHelper
import com.oplus.gallery.foundation.ui.dialog.ProgressDialog
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_DRAGGING
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_IDLE
import com.oplus.gallery.foundation.ui.widget.GalleryHorizontalScrollView.SCROLL_STATE_SETTLING
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.videoeditorpage.R
import com.oplus.gallery.videoeditorpage.base.ui.ControlBarView
import com.oplus.gallery.videoeditorpage.base.ui.EditorBaseUIController
import com.oplus.gallery.videoeditorpage.base.util.VideoTrackHelper.trackVideoEditorExportOLiveOrPhoto
import com.oplus.gallery.videoeditorpage.engine.GalleryVideoThumbnailView
import com.oplus.gallery.videoeditorpage.engine.IGalleryThumbController.ThumbScrollerListener
import com.oplus.gallery.videoeditorpage.engine.meicam.MeicamVideoEngine.Companion.MILLIS_TIME_BASE
import com.oplus.gallery.videoeditorpage.output.ResultCode
import com.oplus.gallery.videoeditorpage.output.task.ExportFileInfo
import com.oplus.gallery.videoeditorpage.output.task.ExportOliveTask
import com.oplus.gallery.videoeditorpage.output.task.OutputData
import com.oplus.gallery.videoeditorpage.output.task.SaveType
import com.oplus.gallery.videoeditorpage.uhdr.engine.VideoUhdrEngine
import com.oplus.gallery.videoeditorpage.video.VideoEditorActivity
import com.oplus.gallery.videoeditorpage.video.ui.ExportPlayBackView
import com.oplus.gallery.videoeditorpage.video.ui.VideoOliveCoverView
import com.oplus.gallery.videoeditorpage.video.ui.uicontroller.EditorExportOliveUIController
import kotlinx.coroutines.launch
import java.util.concurrent.Executors
import java.util.concurrent.Semaphore
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import kotlin.math.floor

/**
 * 导出实况照片功能逻辑实现类
 */
class EditorExportOliveState(context: Context, controlBarView: ControlBarView) : VideoEditorBaseState(TAG, context, controlBarView),
    EditorBaseUIController.OnPlayBackClickListener {

    /**
     * 原视频总长度
     */
    private var videoDuration: Long = DEFAULT_VALUE

    /**
     * 播放起始时间
     */
    private var startTime: Long = DEFAULT_VALUE

    /**
     * 可播放视频时长
     */
    private var playDuration: Long = DEFAULT_VALUE

    /**
     * 封面帧时间
     */
    private var coverTime: Long = DEFAULT_VALUE

    /**
     * 记录滚动状态
     */
    private var thumbScrollViewLastScrollState = SCROLL_STATE_IDLE

    /**
     * 导出实况任务进度框弹窗
     */
    private var oliveProcessDialog: ProgressDialog? = null

    /**
     * 导出实况任务
     */
    private var exportOliveTask: ExportOliveTask? = null

    /**
     * 导出实况任务触发的时间
     */
    private var exportOliveStartTime: Long = 0

    /**
     * 视频帧缩图轴
     */
    private lateinit var thumbScrollView: GalleryVideoThumbnailView

    /**
     * 时间线选帧框容器布局
     */
    private lateinit var thumbOliveCoverView: VideoOliveCoverView

    /**
     * 预览播放控件
     */
    private lateinit var exportPlayBackView: ExportPlayBackView

    /**
     * 导出实况UI控制类
     */
    private lateinit var exportOliveUIController: EditorExportOliveUIController

    /**
     * 这里标识的是一个手势操作后，带来的一些列连带效应（跟手滚动、惯性滑动、极限阻尼），一直持续到禁止态结束
     * 即：当前可滚动view的内容位置变更（scroll、fling、overScroll）是否为用户触发。
     * <p>
     * true - 用户触发：
     * 1.当前状态处于SCROLL_STATE_DRAGGING
     * 2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
     * <p>
     * false - 非用户触发。
     */
    private var isThumbScrollViewScrollFromUserOp = false

    /**
     * 应用context
     */
    private val appContext = mContext as? AppCompatActivity

    /**
     * video导出uhdr的engine
     */
    private var videoUhdrEngine: VideoUhdrEngine? = null

    /**
     * 剪辑开始时间
     */
    private var initTrimInTime: Long = DEFAULT_VALUE

    /**
     * 剪辑结束时间
     */
    private var initTrimOutTime: Long = DEFAULT_VALUE

    /**
     * 视频变速倍速（适配通过倍速编辑过的场景）
     */
    private var videoSpeed: Float = DEFAULT_SPEED

    /**
     * 导出任务的单线程执行器，
     * 导出的任务会放在此执行器中执行，如果先前任务未完成会进行排队 (使用 [countDownLatch] 阻塞)。
     * 用户取消导出时会清空排队队列。
     */
    private val executor by lazy {
        Executors.newFixedThreadPool(
            1, ThreadFactoryBuilder().setNameFormat("export_olive-%d").build()
        )
    }

    /**
     * 用于阻塞导出任务 runnable，
     * 初始没有 permit，需待导出回调时 +1。
     * “导出回调的+1” 需与 “runnable的-1” 相对应（两者的执行时机无关）
     */
    private var semaphore: Semaphore = Semaphore(0)

    /**
     * 最后滚动到的位置（可能会超出视频长度或小于 0）
     * 用于判断目前滚动位置是否位于 Olive 导出开头时间点。
     */
    private var lastScrollTime: Long = 0L

    /**
     * 是否需要重置封面帧
     */
    private var shouldResetCover: Boolean = true

    /**
     * 是否需要执行自动播放
     */
    private var shouldAutoPlayWhenScrollIdle: Boolean = false

    init {
        val totalDuration = mEngineManager.getVideoDuration() / MILLIS_TIME_BASE
        initTrimInTime = mEngineManager.getTrimInTime()
        initTrimOutTime = mEngineManager.getTrimOutTime()
        videoSpeed = mEngineManager.getVideoSpeed()
        val trimVideoDuration = getRealPlayTimeIfNeed(initTrimOutTime - initTrimInTime)
        // 如果有剪辑记录则使用裁剪后的时长
        videoDuration = if (trimVideoDuration > 0) trimVideoDuration else totalDuration
    }

    /**
     * 布局更改监听
     */
    private val onLayoutChangeListener =
        OnLayoutChangeListener { view, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
            // 判断布局是否真正发生改变
            if ((left != oldLeft) || (right != oldRight) || (top != oldTop) || (bottom != oldBottom)) {
                mEngineManager?.let {
                    // 在view尚未附加到窗口时，会等待视图附加后在执行任务
                    view.post {
                        // 由于布局改变时需要更新缩图轴与选帧框位置，因此当布局发生改变时都需要设置一下缩图轴
                        mEngineManager.showVideoOliveThumbLoader(thumbScrollView)
                        thumbScrollView.setScrollListener(scrollChangeListener)
                        // 滚动缩图轴到目标位置
                        scrollThumbViewToTime(coverTime)
                    }
                } ?: return@OnLayoutChangeListener
            }
        }

    /**
     * 滚动缩图轴到目标位置
     * @param time
     */
    private fun scrollThumbViewToTime(time: Long) {
        val totalTime = mEngineManager.getTotalTime()
        val x = getTimeConvertPosition(time)
        thumbOliveCoverView.changeSelectViewOffSet(time, totalTime, true, x)
        thumbScrollView.scrollTo(x, 0)
    }

    /**
     * 通过传入时间获取对应的缩图轴位置
     * @param time
     * @return
     */
    private fun getTimeConvertPosition(time: Long): Int {
        val totalTime = mEngineManager.getTotalTime()
        var postPercent = 0f
        if (totalTime > 0) {
            postPercent = time.coerceIn(0, totalTime) / totalTime.toFloat()
        }
        return Math.round(postPercent * getSequenceWidth())
    }

    /**
     * 获取缩图轴序列宽度
     */
    private fun getSequenceWidth(): Int {
        val sWidth = floor(
            (mEngineManager.getTotalTime() * mEngineManager.getTimeBase() * thumbScrollView.getPixelPerMicrosecond()) + PIXEL_OFFSET
        ).toInt()
        return sWidth
    }

    /**
     * 缩图轴滚动监听
     */
    private val scrollChangeListener: ThumbScrollerListener = object : ThumbScrollerListener {
        override fun onScrolled(pos: Int) {
            // 通过当前坐标获取对应的时间线
            val tmpTimeStamp = (floor(pos / thumbScrollView.pixelPerMicrosecond + PIXEL_OFFSET)).toLong() / mEngineManager.getTimeBase()
            lastScrollTime = tmpTimeStamp
            if (isThumbScrollViewScrollFromUserOp) {
                refreshTimeAfterScroll(tmpTimeStamp)
            }
        }

        override fun onScrollStateChanged(newState: Int) {
            /*
             * 此次操作为用户触发：
             * 1.当前状态处于SCROLL_STATE_DRAGGING
             * 2.当前状态是SCROLL_STATE_SETTLING, 变更前的状态为SCROLL_STATE_DRAGGING时
             */
            if ((newState == SCROLL_STATE_DRAGGING)
                || ((thumbScrollViewLastScrollState == SCROLL_STATE_DRAGGING) && (newState == SCROLL_STATE_SETTLING))) {
                if (!isThumbScrollViewScrollFromUserOp) {
                    mEngineManager.pause()
                    // 当为用户触发滚动操作时显示时间线，隐藏选帧框和圆点
                    thumbOliveCoverView.showTrimTime = true
                }

                isThumbScrollViewScrollFromUserOp = true
            }

            /*
             * 用户触发的操作停止：当前状态为SCROLL_STATE_IDLE 且 变更前处于用户触发态 mIsThumbScrollViewScrollFromUserOp = true
             */
            if (isThumbScrollViewScrollFromUserOp && (newState == SCROLL_STATE_IDLE)) {
                isThumbScrollViewScrollFromUserOp = false
            }
            thumbScrollViewLastScrollState = newState
            // 当缩图轴停止滚动时，隐藏时间线，显示选帧框和圆点
            if (thumbScrollViewLastScrollState == SCROLL_STATE_IDLE) {
                thumbOliveCoverView.showTrimTime = false
                if (shouldAutoPlayWhenScrollIdle) exportPlayBackView.performClick()
                shouldAutoPlayWhenScrollIdle = false
            }
        }
    }

    /**
     * 滚动后更新播放进度和播放时间
     * @param time 当前时间
     */
    private fun refreshTimeAfterScroll(time: Long) {
        val totalTime = mEngineManager.getTotalTime()
        val videoProgress = time.coerceIn(0, totalTime)
        val x = getTimeConvertPosition(time)
        thumbOliveCoverView.changeSelectViewOffSet(videoProgress, totalTime, position = x)
        thumbOliveCoverView.updateTrimTime(mContext, videoProgress, totalTime)
        mHaveReset = false
        updateTimeAndEngine(videoProgress)
    }

    override fun createUIController(): EditorBaseUIController {
        exportOliveUIController = EditorExportOliveUIController(mContext, mControlBarView, this, this)
        exportOliveUIController.setOnPlayBackClickListener(this)
        return exportOliveUIController
    }

    /**
     * 是否为强制保存
     * @param resultCode 结果码
     * @return true:强制保存 false:非强制保存
     */
    private fun isForceStopSave(resultCode: Int): Boolean {
        return (resultCode != ResultCode.MEICAM_FORCE_STOP_SAVE) && (resultCode != ResultCode.TASK_STOP_FORCE_STOP_SAVE)
    }

    private val exportOliveListener = object : ExportOliveTask.ExportOliveListener {
        override fun onProcess(process: Int) {
            appContext?.lifecycleScope?.launch {
                oliveProcessDialog?.setProgress(process)
            }
        }

        override fun onExportComplete(outputData: OutputData<ExportFileInfo>) {
            if (outputData.resultCode == ResultCode.SAVE_SUCCESS) {
                GLog.d(TAG, LogFlag.DL, "done success, ExportOliveTask.run cost time: ${System.currentTimeMillis() - exportOliveStartTime}")
            } else if (isForceStopSave(outputData.resultCode)) {
                GLog.d(TAG, LogFlag.DL, "done result code: ${outputData.resultCode}, reason: ${outputData.resultMsg}")
                showToastOnMain(R.string.videoeditor_editor_export_fail)
            }

            if (isForceStopSave(outputData.resultCode)) {
                // 非点击弹窗取消按键时才 dismiss；AppCompatDialog 在点击按钮后会自行 dismiss，此处再dismiss会导致：取消后再点导出时弹窗出现后马上消失的问题。
                oliveProcessDialog?.dismiss()
            }

            if (outputData.resultCode == ResultCode.SAVE_SUCCESS) {
                // 导出成功，退出当前页面；退出会跑动画，需要使用有Looper的线程进行调用
                appContext?.lifecycleScope?.launch {
                    // 导出成功退出当前页面
                    controlBarView.changeToFirstEnterState()
                    // 导出成功显示查看tips
                    outputData.resultData?.let {
                        exportOliveUIController.showImmediateLookTips(it.resultUri, it.thumbBitmap)
                    } ?: GLog.e(TAG, LogFlag.DL) { "resultData is null" }
                }
                mEngineManager.reset()
            } else {
                // 导出失败后需重置预览按钮和重置封面帧位置
                if (mControlBarView.currentEditor is EditorExportOliveState) {
                    appContext?.lifecycleScope?.launch {
                        /*
                          1、默认这里是需要重置封面帧的
                          2、这里使用shouldResetCover主要是为了解决测试极限操作同时取消导出和按
                          系统返回键时，由于这里协程调度的问题导致可能触发先执行cancel()中的reset
                          回到上级页面后又执行了此处seekTo操作的问题
                         */
                        if (shouldResetCover) mEngineManager.seekTo(coverTime)
                    }
                }
            }

            mEngineManager.updateCustomCompileTaskStatus(false)
            mEngineManager.setCustomCompileTaskListener(null)

            if (semaphore.availablePermits() == 0) {
                // 提供一个 permit
                semaphore.release()
            }
            // 埋点
            trackExportOLive(outputData)
        }
    }

    /**
     * 导出实况埋点
     */
    fun trackExportOLive(outputData: OutputData<ExportFileInfo>) {
        val costTime = System.currentTimeMillis() - exportOliveStartTime
        val uri = if (outputData.resultCode == ResultCode.SAVE_SUCCESS) outputData.resultData?.resultUri.toString() else null
        trackVideoEditorExportOLiveOrPhoto(mContext, uri, costTime, SaveType.Olive, outputData.resultCode)
    }

    override fun done(): Boolean {
        // 如果点导出时，选帧框不在正确位置则先滚动到正确的导出起始位置
        if (lastScrollTime != coverTime) {
            scrollThumbViewToTime(coverTime)
        }
        // 导出实况时停止缩图轴滑动
        thumbScrollView.stopScroll()

        // 导出实况结束需要重置封面
        shouldResetCover = true

        // 拉起实况导出进度对话框
        showCompileDialog()

        val newTask = ExportOliveTask(startTime, playDuration, coverTime, mEngineManager, exportOliveListener, videoUhdrEngine)

        // 导出任务
        executor.submit {
            exportOliveTask = newTask
            exportOliveStartTime = System.currentTimeMillis()
            mEngineManager.updateCustomCompileTaskStatus(true)
            exportOliveTask?.run()

            // 阻塞直到完成导出，或超时
            try {
                // 等待一个 permit
                val successRelease = semaphore.tryAcquire(TASK_MAX_WAITING_TIME_SEC, TimeUnit.SECONDS)
                if (!successRelease) {
                    GLog.e(TAG, LogFlag.DL) { "[done] acquire timeout!" }
                }
            } catch (e: InterruptedException) {
                GLog.e(TAG, LogFlag.DL, e) { "[done] failed to acquire" }
            }
        }

        // 不直接退出页面，待导出成功后手动退出
        return false
    }

    override fun cancel() {
        super.cancel()
        shouldResetCover = false
        mEngineManager.reset()
    }

    override fun onDestroy() {
        super.onDestroy()
        thumbScrollView.removeOnLayoutChangeListener(onLayoutChangeListener)
        thumbScrollView.setScrollListener(null)
        mEngineManager.setCustomCompileTaskListener(null)
        executor.shutdownNow()
    }

    override fun show(isNeedAnimator: Boolean, isMenuNeedAnimator: Boolean) {
        super.show(isNeedAnimator, isMenuNeedAnimator)
        mEngineManager.seekTo(0)
        thumbScrollView = mControlBarView.findViewById(R.id.video_thumb_view)
        thumbScrollView.addOnLayoutChangeListener(onLayoutChangeListener)
        thumbOliveCoverView = mControlBarView.findViewById(R.id.video_cover_view)
        exportPlayBackView = mControlBarView.findViewById(R.id.preview_export_video)
        initShowTime()
        onPlayStatusChange()
        createUhdrEngine()
    }

    /**
     * 在onPause时进行相关状态检测
     * 1、选帧框不在正确位置则直接滚动到正确的起始位置
     * 2、更改自动播放状态为false，防止后台播放
     */
     fun onPauseCheck() {
        if (thumbScrollView.scrollState != SCROLL_STATE_IDLE) {
            // 选帧框不在正确位置则直接滚动到正确的起始位置
            if (lastScrollTime != coverTime) {
                scrollThumbViewToTime(coverTime)
            }
            shouldAutoPlayWhenScrollIdle = false
            thumbScrollView.stopScroll()
        }
    }

    /**
     * 创建VideoUhdrEngine
     */
    private fun createUhdrEngine() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val videoUri = mEngineManager.getVideoUri()
            val timeline = mEngineManager.getMeicamVideoTimeline() as? NvsTimeline

            if (videoUri != null && timeline != null) {
                val path = MediaStoreUriHelper.getFilePathByUri(Uri.parse(videoUri))
                if (path != null) {
                    videoUhdrEngine = VideoUhdrEngine(mContext, mEngineManager)
                }
            }
        }
    }

    /**
     * 主线程toast提示
     */
    private fun showToastOnMain(@StringRes resId: Int) {
        appContext?.lifecycleScope?.launch {
            ToastUtil.showShortToast(resId)
        }
    }

    /**
     * 初始化播放时间显示
     */
    private fun initShowTime() {
        updateTimeAndEngine(0)
        thumbOliveCoverView.updateTrimTime(mContext, 0, mEngineManager.getTotalTime())
    }

    /**
     * 播放完成后判断播放时长在可播放总时长的差值范围内，则更新时长显示为可播放总时长
     */
    override fun onPlayFinish() {
        if (mEngineManager.getCurrentTime() >= (playDuration - VideoEditorActivity.VIDEO_PLAY_END_CHECK_GAP)) {
            mEngineManager.seekTo(coverTime)
            uiController.updateCurrentTimeTextView(startTime, videoDuration)
            uiController.updateVideoDurationTextView(playDuration)
        }
    }

    override fun onClickView(view: View?) {
        if (view?.id == R.id.preview_export_video) {
            if (allowPlayBack()) {
                // 如果在播放过程中，则不可在重置封面，这里主要解决极限操作情况下回调延迟得问题
                shouldResetCover = false
                mEngineManager.play(startTime, playDuration)
            } else {
                // 如果是在缩图轴滚动过程中点击播放，则需要记录点击播放行为，以便停止滚动后执行自动播放功能
                shouldAutoPlayWhenScrollIdle = true
            }
        }
    }

    /**
     * 是否允许播放
     * 当缩图轴当前未滚动时可播放
     * @return
     */
    private fun allowPlayBack(): Boolean {
        return thumbScrollView.scrollState == SCROLL_STATE_IDLE
    }

    /**
     * 更新时间和设置引擎封面帧位置
     * 导出实况的封面取选帧框开始时间 + [COVER_OFFSET_TIME]偏移时间的视频帧图片，
     * @param currentTime 当前时间
     */
    private fun updateTimeAndEngine(currentTime: Long) {
        val maxStartTime = (videoDuration - MAX_EXPORT_TIME).coerceAtLeast(0)
        val totalTime = mEngineManager.getTotalTime()
        coverTime = currentTime
        startTime = when {
            // 小于等于3秒的视频
            totalTime <= MAX_EXPORT_TIME -> 0
            // 封面帧时间小于等于偏移时间，则开始时间为0
            coverTime <= COVER_OFFSET_TIME -> 0
            // 封面帧时间大于等于总时长-偏移时间，则开始时间为maxStartTime
            coverTime >= (totalTime - COVER_OFFSET_TIME) -> maxStartTime
            else -> coverTime - COVER_OFFSET_TIME
        }
        playDuration = getPlayDurationTime()
        // 设置引擎到当前封面帧位置
        mEngineManager.seekTo(coverTime)
    }

    /**
     * 获取可播放的总时长
     * 选帧框起始位置时间 + 3s 时长
     * 超出总时长则最大使用视频总时长
     * @return 播放时长
     */
    private fun getPlayDurationTime(): Long {
        val playTime = startTime.plus(MAX_EXPORT_TIME)
        return minOf(playTime, videoDuration)
    }

    override fun isNeedAnimator(): Boolean {
        return true
    }

    /**
     * 显示实况导出时的加载框
     */
    private fun showCompileDialog() {
        if (oliveProcessDialog == null) {
            val builder = ProgressDialog.Builder(mContext, false)
            builder.setTitle(R.string.videoeditor_editor_exporting_olivephoto)
            builder.setCancelable(false)
            builder.setPositiveButton(
                mContext.resources.getString(android.R.string.cancel)
            ) { _: DialogInterface?, _: Int ->
                stopExportOliveTask()
            }
            oliveProcessDialog = builder.build().show()
        } else {
            if (oliveProcessDialog?.isShowing() == false) {
                oliveProcessDialog?.setProgress(0)
                oliveProcessDialog?.show()
            }
        }
    }

    /**
     *  用实际时间转为播放时间
     *  @param time 实际时间
     *  @return 映射时间
     */
    private fun getRealPlayTimeIfNeed(time: Long): Long {
        if (videoSpeed <= 0) videoSpeed = DEFAULT_SPEED
        // 平台视频做变速映射
        return (time.toFloat() / videoSpeed).toLong()
    }

    /**
     * 结束实况导出任务
     */
    private fun stopExportOliveTask() {
        exportOliveTask?.stop()
        // 取消当前时，同时取消所有正在排队的任务。
        (executor as? ThreadPoolExecutor)?.queue?.clear()
    }

    companion object {
        private const val TAG = "EditorExportOliveState"
        private const val DEFAULT_VALUE = 0L
        private const val DEFAULT_SPEED = 1f
        const val MAX_EXPORT_TIME = 3 * MILLIS_TIME_BASE

        // 像素偏移，用于参与通过坐标得到映射的时间线
        private const val PIXEL_OFFSET = 0.5

        /**
         * 导出任务阻塞的最大等待时间，秒
         */
        private const val TASK_MAX_WAITING_TIME_SEC = 30L

        /**
         * 导出实况封面帧距离导出开始时刻的偏移时间
         */
        const val COVER_OFFSET_TIME = (0.5 * MAX_EXPORT_TIME).toLong()
    }
}