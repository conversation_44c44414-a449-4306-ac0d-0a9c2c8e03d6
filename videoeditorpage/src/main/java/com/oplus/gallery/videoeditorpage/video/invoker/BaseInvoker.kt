/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseInvoker.kt
 ** Description: 编辑调用者基类 处理一些通用的逻辑
 ** Version: 1.0
 ** Date: 2025/6/6
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>           2025/6/23        1.0        created
 *********************************************************************************/

package com.oplus.gallery.videoeditorpage.video.invoker

import android.os.Bundle
import android.view.ViewGroup

/**
 * 编辑调用者基类 处理一些通用的逻辑
 */
open class BaseInvoker {

    /**
     * 处理解析失败
     */
    open fun handleCreateCheckFail(): Unit = Unit

    /**
     * 引擎初始化完成
     */
    open fun onInitEngineFinish(): Unit = Unit

    /**
     * 播放状态
     * @param status
     */
    open fun onPlayStatus(status: Int): Unit = Unit

    /**
     * 创建视频预览窗口
     *
     * @param parent     视频窗口的载体view
     * @param shouldCrop 是否裁剪
     * @return 是否创建成功
     */
    open fun createLiveWindow(parent: ViewGroup, shouldCrop: Boolean): Boolean = false

    /**
     * 显示自定义页面
     * @param resId
     * @param data
     */
    open fun showCustomPage(resId: Int, data: Bundle? = null): Unit = Unit

    /**
     * 根据需要释放资源
     */
    open fun release(): Unit = Unit
}