<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:forceDarkAllowed="false">

    <com.oplus.gallery.videoeditorpage.engine.GalleryEditorVideoView
        android:id="@+id/video_engine_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">

        <com.oplus.gallery.videoeditorpage.video.ui.VideoSubTitleEditTextView
            android:id="@+id/gallery_video_subtitle_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true" />

    </com.oplus.gallery.videoeditorpage.engine.GalleryEditorVideoView>

    <com.oplus.gallery.videoeditorpage.base.ui.ControlBarView
        android:id="@+id/main_control_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/videoeditor_video_editor_background_color_edit"
        android:layout_alignParentBottom="true" />
</RelativeLayout>