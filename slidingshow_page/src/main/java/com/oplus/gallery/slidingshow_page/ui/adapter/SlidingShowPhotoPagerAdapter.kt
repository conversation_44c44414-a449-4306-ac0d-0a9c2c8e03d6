/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PhotoPagerAdapter.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2022/1/6
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_ROUTER
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		 2022/1/6		1.0		OPLUS_ARCH_ROUTER
 *********************************************************************************/
package com.oplus.gallery.slidingshow_page.ui.adapter

import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import androidx.lifecycle.LifecycleOwner
import com.oplus.gallery.photopager.PhotoPagerAdapter
import com.oplus.gallery.photopager.strategy.SlotLoadingProxy
import com.oplus.gallery.slidingshow_page.viewmodel.SlidingShowViewModel

/**
 * PhotoPager的适配器
 */
class SlidingShowPhotoPagerAdapter(
    private val viewModel: SlidingShowViewModel,
    viewLifecycleOwner: LifecycleOwner
) : PhotoPagerAdapter(),
    SlotLoadingProxy by viewModel.photoPagerViewModel.photoPagerLoadingProxy {

    companion object {
        private val NO_EFFECT_DRAWABLE = ColorDrawable()
    }

    init {
        // 图片加载完成时刷新item的回调
        viewModel.photoPagerViewModel.notifier = { position ->
            notifySlotInvalidate(position)
        }
        // count改变时刷新
        viewModel.count.observe(viewLifecycleOwner) {
            notifyDataSetChanged()
        }
    }

    override fun onSlotFocused(slot: Int, isFromUser: Boolean) {
        viewModel.notifyFocusItemChanged(slot)
    }

    override fun getSlotCount(): Int {
        return viewModel.count.value ?: 0
    }

    override fun getSlotOverlay(slot: Int, builder: Any): Drawable {
        return NO_EFFECT_DRAWABLE
    }

    override fun getSlotRendererType(slot: Int): Int = 0
}