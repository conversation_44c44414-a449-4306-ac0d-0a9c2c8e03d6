/*********************************************************************************
 * Copyright (C), 2008-2108, OPLUS Mobile Comm Corp., Ltd
 * OPLUS_FEATURE_RESOLVER_SHARE, All rights reserved.
 *
 * File: - OplusResolverInfoLoader.kt
 * Description:从系统接口加载支持三方分享的应用信息
 * Version: 1.0
 * Date: 2021-3-24
 * Author: <PERSON><PERSON><PERSON><PERSON>@Gallery.App
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>          <desc>
 * ------------------------------------------------------------------------------
 * Wu<PERSON><EMAIL>          2021-3-24        1.0         Create this module
 ******************************************************************************/
package com.oplus.gallery.sharepage.sendbyapp

import android.content.Context
import android.content.Intent
import android.content.pm.ResolveInfo
import android.os.Process
import android.os.Process.THREAD_PRIORITY_FOREGROUND
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.sharepage.ILoadPackageIconHelper
import com.oplus.gallery.sharepage.LoadPackageIconHelperImpl
import com.oplus.gallery.standard_lib.scheduler.DefaultScheduler
import com.oplus.gallery.standard_lib.scheduler.FutureListener
import com.oplus.gallery.standard_lib.scheduler.Job
import com.oplus.gallery.standard_lib.scheduler.JobContext

class ResolveInfoLoader(context: Context?) {
    private var packageIconHelper: ILoadPackageIconHelper? = LoadPackageIconHelperImpl(context)
    private val session = DefaultScheduler.scheduler.createPrioritySession(JOB_LIMIT)

    init {
        session.name = TAG
        session.bringToFront()
    }

    fun processResolverInfoLoaded(
        originIntent: Intent,
        resolveInfo: ResolveInfo,
        futureListener: FutureListener<ActionSendItemInfo?>
    ) {
        session.submit(ResolverInfoLoadTask(originIntent, resolveInfo), futureListener)
    }

    private inner class ResolverInfoLoadTask(
        private val originIntent: Intent,
        private val resolveInfo: ResolveInfo
    ) : Job<ActionSendItemInfo?> {
        override fun call(jc: JobContext): ActionSendItemInfo? {
            GTrace.traceBegin("getIconItem")
            val originalThreadPriority: Int = Process.getThreadPriority(Process.myTid())
            Process.setThreadPriority(THREAD_PRIORITY_FOREGROUND)
            val activityInfo = resolveInfo.activityInfo
            val tag = activityInfo.applicationInfo.packageName + activityInfo.name
            return packageIconHelper?.getIconItem(originIntent, resolveInfo, tag).apply {
                Process.setThreadPriority(originalThreadPriority)
                GTrace.traceEnd()
            }
        }
    }

    companion object {
        private const val TAG = "ResolverInfoLoader"
        private const val JOB_LIMIT = 3
    }
}