/*********************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp, Ltd.
 ** VENDOR_EDIT
 ** File        : SendMediaFragment.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2020/5/28 20:44
 ** Author      : wanglongping
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>        <desc>
 **  wanglongping     2020/5/28    1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.sharepage

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.SharedPreferences
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewStub
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.doOnPreDraw
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.checkbox.COUICheckBox
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.tooltips.COUIToolTips
import com.oplus.gallery.addon.app.OplusJankWrapper
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_ENTER_SHARE_ANIMATION
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_ENTER_SHARE_ANIMATION
import com.oplus.gallery.addon.osense.CpuFrequencyManager
import com.oplus.gallery.addon.utils.OSVersionUtils
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.fileprocessor.BaseFileProcessTask.Companion.autoCancelWhenOnDestroy
import com.oplus.gallery.business_lib.fileprocessor.FileProcessScene
import com.oplus.gallery.business_lib.fileprocessor.FinishListener
import com.oplus.gallery.business_lib.fileprocessor.builder.FileProcessTaskBuilder
import com.oplus.gallery.business_lib.helper.HDRVideoTransformHelper
import com.oplus.gallery.business_lib.helper.HDRVideoTransformHelper.PREFERENCE_VIDEO_AUTO_CONVERT_KEY
import com.oplus.gallery.business_lib.helper.HeifHelper
import com.oplus.gallery.business_lib.helper.HeifHelper.PREFERENCE_USE_ENABLE_HEIF_CONVERT
import com.oplus.gallery.business_lib.model.config.user.UserConfigUtils.KEY_SECURITY_SHARE_IS_ERASE_LOCATION
import com.oplus.gallery.business_lib.model.config.user.UserConfigUtils.KEY_SECURITY_SHARE_IS_ERASE_TAKE_PHOTO
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.getBooleanPref
import com.oplus.gallery.business_lib.model.data.config.ComponentPrefUtils.getSp
import com.oplus.gallery.business_lib.onetouchshare.OneTouchShareController
import com.oplus.gallery.business_lib.securityshare.SecurityShareHelper
import com.oplus.gallery.business_lib.securityshare.data.CompatibleFormatStatus
import com.oplus.gallery.business_lib.securityshare.data.PrivacyInfoStatus
import com.oplus.gallery.business_lib.securityshare.track.ShareTrackHelper
import com.oplus.gallery.business_lib.sharetransform.TransformManager
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.util.ShareUtils
import com.oplus.gallery.business_lib.util.ShareUtils.OShareShowType
import com.oplus.gallery.business_lib.viewmodel.base.DifferNotifyCallback
import com.oplus.gallery.business_lib.viewmodel.base.UpdateEventsCallback
import com.oplus.gallery.business_lib.viewmodel.base.isNullOrEmpty
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.store.GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.SHARE_DELETE_CLOSE
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant.Value.SHARE_DELETE_OPEN
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.DisplayUtils.getDarkColorWithNightMode
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil.TYPE_BOOLEAN
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.HandlerContentObserver
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.framework.abilities.sharing.nfc.Constants.NfcScene
import com.oplus.gallery.framework.abilities.transform.TransformStorage
import com.oplus.gallery.framework.abilities.transform.TransformType
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.sharepage.GalleryShareDialog.Companion.updateSystemBarStatus
import com.oplus.gallery.sharepage.securityshare.widget.OneTouchShareGuideDialog
import com.oplus.gallery.sharepage.securityshare.widget.SecurityShareSettingDialog
import com.oplus.gallery.sharepage.sendbyapp.ListSendByAppsFragment
import com.oplus.gallery.sharepage.sendbyapp.PagerSendByAppsFragment
import com.oplus.gallery.sharepage.sendbyapp.SendByAppsFragment
import com.oplus.gallery.sharepage.sendbynearby.SendByNearbyShare
import com.oplus.gallery.sharepage.sendbyoshare.SendByOShareFragment
import com.oplus.gallery.sharepage.viewmodel.ShareViewModel
import com.oplus.gallery.sharepage.widget.ShareGalleryRecyclerView
import com.oplus.gallery.sharepage.widget.ShareScrollView
import com.oplus.gallery.standard_lib.app.SHARE_PAGE_PRIORITY_FOREGROUND
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as BasebizR
import com.oplus.gallery.foundation.ui.R as LibUIR

/**
 * 分享页的Fragment，会被[GalleryShareDialog]持有w，进行分享操作。
 */

@Deprecated("废弃代码")
class SendMediaFragment : BaseFragment(), SharedPreferences.OnSharedPreferenceChangeListener {
    // 数据依赖
    private var selectionDataId: Int? = null
    var albumModel: BaseModel<MediaItem>? = null

    private var galleryShareDialog: GalleryShareDialog? = null

    private var galleryAdapter: ShareGalleryAdapter? = null
    private var sendAppsFragment: SendByAppsFragment? = null
    private var oShareFragment: SendByOShareFragment? = null
    private var nearbyShare: SendByNearbyShare? = null
    private var nestScrollView: ShareScrollView? = null
    private var galleryView: ShareGalleryRecyclerView? = null
    private var titleView: TextView? = null

    private var deleteTipView: ConstraintLayout? = null
    private var deleteTipCheck: COUICheckBox? = null
    private var deleteRestoreTipCheck: COUIToolTips? = null

    private var tvSecurityShareTitle: TextView? = null
    private var securityShareHelper: SecurityShareHelper? = null
    private var safeShareDialog: SecurityShareSettingDialog? = null
    private var lastRefreshTime = 0L

    private var isShareDeleteTipsAsFirstShow: Boolean = false

    private var oneTouchShareController: OneTouchShareController? = null

    /**
     * 分享页的VM，目前只是作为了ReloadTask来使用，后续应该重构，将业务逻辑迁移到ViewModel中。
     */
    private val viewModel by lazy { ViewModelProvider(this)[ShareViewModel::class.java] }

    /**
     * 分享页OShare的显示类型
     */
    private val oShareShowType: OShareShowType by lazy {
        ShareUtils.getOShareTypeInSharePage()
    }

    /**
     * 画廊数据变化时使用
     */
    private var diffCallback: UpdateEventsCallback? = null
    @Volatile
    private var hasInvalidSharedMedia: Boolean = false

    /**
     * 隐私安全的位置状态：包含 选中照片是否含有信息、是否启用抹除信息
     */
    @VisibleForTesting
    val locationStatus by lazy {
        PrivacyInfoStatus().apply {
            isPrivacySecurityEnabled = getBooleanPref(ContextGetter.context, KEY_SECURITY_SHARE_IS_ERASE_LOCATION, false)
        }
    }

    /**
     * 隐私安全的拍摄信息状态：包含 选中照片是否含有信息、是否启用抹除信息
     */
    @VisibleForTesting
    val shotInfoStatus by lazy {
        PrivacyInfoStatus().apply {
            isPrivacySecurityEnabled = getBooleanPref(ContextGetter.context, KEY_SECURITY_SHARE_IS_ERASE_TAKE_PHOTO, false)
        }
    }

    /**
     * 兼容模式状态：包含 是否含有需要转换格式的照片,是否需要转换格式
     */
    @VisibleForTesting
    val imageFormatStatus by lazy {
        CompatibleFormatStatus().apply {
            isConvertFormatEnabled = HeifHelper.isPrefHeifConvertEnable() && HeifHelper.isHeifConvertEnable()
        }
    }

    /**
     * 兼容模式状态：包含 是否含有需要转换格式的视频,是否需要转换格式
     */
    @VisibleForTesting
    val videoFormatStatus by lazy {
        CompatibleFormatStatus().apply {
            isConvertFormatEnabled = HDRVideoTransformHelper.getHdrConvertFunctionStatus()
        }
    }

    private val galleryLayoutChangeListener: View.OnLayoutChangeListener by lazy {
        View.OnLayoutChangeListener { v, _, top, _, bottom, _, oldTop, _, oldBottom ->
            if ((bottom - top) != (oldBottom - oldTop)) {
                refreshGalleryDelay()
            }
        }
    }

    /**
     * 延迟刷新画廊视图
     *
     * 画廊布局高度频繁变化或画廊数据频繁刷新时，第一次正常刷新，后面延迟150ms刷新
     */
    private fun refreshGalleryDelay() {
        galleryView?.also { v ->
            if (System.currentTimeMillis() - lastRefreshTime > DELAY_REFRESH_TIME) {
                if ((v.handler != null) && v.handler.hasCallbacks(notifyDataRunnable).not()) {
                    v.post(notifyDataRunnable)
                }
            } else {
                v.removeCallbacks(notifyDataRunnable)
                v.postDelayed(notifyDataRunnable, DELAY_REFRESH_TIME)
            }
        }
    }

    /**
     * 监听 设置--发送时转换图片格式 选项值的变化
     */
    private val convertImageObserver = object : HandlerContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean) {
            ApiDmManager.getSettingDM().getHeifStatus().let { enabled ->
                imageFormatStatus.isConvertFormatEnabled = enabled
                safeShareDialog?.setImageConvertEnabled(enabled)
                GLog.d(TAG, "onChange, imageFormatStatus.isConvertFormatEnabled=$enabled")
            }
            updateShareConvertFormatStatus()
        }
    }

    /**
     * 监听 设置--发送时转换视频格式 选项值的变化
     */
    private val convertVideoObserver = object : HandlerContentObserver(Handler(Looper.getMainLooper())) {
        override fun onChange(selfChange: Boolean) {
            ApiDmManager.getSettingDM().getHdrStatus().let { enabled ->
                videoFormatStatus.isConvertFormatEnabled = enabled
                safeShareDialog?.setVideoConvertEnabled(enabled)
                GLog.d(TAG, "onChange, videoFormatStatus.isConvertFormatEnabled=$enabled")
            }
            updateShareConvertFormatStatus()
        }
    }

    private val notifyDataRunnable = Runnable {
        lastRefreshTime = System.currentTimeMillis()
        galleryAdapter?.refreshGalleryView()
        GLog.d(TAG) { "[notifyDataRunnable] GalleryView layout, refresh GalleryView" }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        initializeViewModel()
        setupViewStyle()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        CpuFrequencyManager.setAction(CpuFrequencyManager.Action.GAME_BOOST_L3, CpuFrequencyManager.TIMEOUT_1S)
        super.onCreate(savedInstanceState)
        GLog.d(TAG, "onCreate.")
        galleryShareDialog ?: return

        /**
         * SendAppsFragment 展示方式
         *
         * 1. ListSendByAppsFragment - RecyclerView 列表（水平方向）形式展示，即单行显示
         * -- 需要同时满足如下条件：
         *    (1). Nearby单行显示（外销才会有Nearby）
         *    (2). 支持互传
         *    (3). 非外销 Android U 新项目 -- 因外销14.1新项目开始支持OShare，但是不显示入口
         *
         * 2. PagerSendByAppsFragment: ViewPager形式展示，即多行显示
         * -- 其他非列表展示情况，如内销项目
         */
        sendAppsFragment = if (ShareUtil.highlightNearbyShare(requireContext())
            && FeatureUtils.isSupportOplusShare
            && ShareUtils.isExportFirstApiLevelAtLeastU.not()
        ) {
            ListSendByAppsFragment.newInstance(galleryShareDialog!!)
        } else {
            PagerSendByAppsFragment.newInstance(galleryShareDialog!!)
        }.apply {
            downloadOriginalPhotoTrigger = this@SendMediaFragment::downloadOriginalPhoto
        }
        val fragmentTransaction = childFragmentManager.beginTransaction()
        fragmentTransaction.add(R.id.send_apps_fragment, sendAppsFragment!!)

        when (oShareShowType) {
            OShareShowType.SHOW_WITH_OLD_UI -> {
                initOShareFragment()
                oShareFragment?.let { sendByOShareFragment ->
                    fragmentTransaction.add(R.id.send_oshare_fragment, sendByOShareFragment)
                }
                GLog.d(TAG, "add SendOShareFragment")
            }

            OShareShowType.SHOW_WITH_NEW_UI -> {
                // 相册直接使用系统的应用图标分享面板（指分享面板针对OShare改版后的系统，内销OShare独立一个控件需要采取新的高度）
                initOShareFragment()
                oShareFragment?.let { sendByOShareFragment ->
                    fragmentTransaction.add(R.id.new_send_oshare_fragment, sendByOShareFragment)
                }
                GLog.d(TAG, "add newSendOShareFragment")
            }

            else -> GLog.d(TAG, "not add SendOShareFragment , OShareShowType = $oShareShowType")
        }

        fragmentTransaction.commitAllowingStateLoss()

        securityShareHelper = SecurityShareHelper(activity = requireActivity(), workerSession = session, selection = viewModel)

        MultiProcessSpUtil.registerSPObserver(
            requireContext(),
            PREFERENCE_USE_ENABLE_HEIF_CONVERT,
            TYPE_BOOLEAN,
            convertImageObserver
        )
        MultiProcessSpUtil.registerSPObserver(
            requireContext(),
            PREFERENCE_VIDEO_AUTO_CONVERT_KEY,
            TYPE_BOOLEAN,
            convertVideoObserver
        )
        getSp(requireContext()).registerOnSharedPreferenceChangeListener(this)
    }

    private fun initOShareFragment() {
        galleryShareDialog?.let { dialog ->
            oShareFragment = SendByOShareFragment.newInstance(dialog).apply {
                downloadOriginalPhotoTrigger = this@SendMediaFragment::downloadOriginalPhoto
                selection = viewModel
            }
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.main_resolver_dialog_send_media_fragment
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)

        initView(view)
        updateSelectTitle()
        updateShareIntentAndSendOptionInfo()

        nestScrollView?.addOnLayoutChangeListener { _, _, top, _, bottom, _, oldTop, _, oldBottom ->
            if ((bottom - top) != (oldBottom - oldTop)) {
                refreshLayout(bottom - top)
            }
        }
        setupJankMonitor()
    }

    private fun refreshLayout(contentHeight: Int) {
        // 窗口断点高度，小于此高度面板内容可滑动
        val breakpointWindowHeight = resources.getDimensionPixelSize(R.dimen.main_resolver_gallery_panel_min_content_height)
        // 可以滑动状态下，除把手，title高度外，画廊铺满剩余内容高度，底部加56dp
        val windowHeight = getCurrentAppUiConfig().windowHeight.current
        GLog.d(TAG, "refreshLayout. contentHeight=$contentHeight, windowHeight=$windowHeight, breakpoint=$breakpointWindowHeight")
        if (windowHeight < breakpointWindowHeight) {
            val galleryBottomHeight = resources.getDimensionPixelSize(R.dimen.main_resolver_gallery_bottom_height_in_smallLandscape)
            val containerHeight = contentHeight - (galleryView?.top ?: 0) - galleryBottomHeight
            galleryView?.updateLayoutParams<LinearLayout.LayoutParams> {
                height = containerHeight
            }
            nestScrollView?.measureSpec = View.MeasureSpec.UNSPECIFIED
            nestScrollView?.isFillViewport = false
        } else {
            galleryView?.updateLayoutParams<LinearLayout.LayoutParams> {
                height = 0
                weight = 1f
            }
            nestScrollView?.measureSpec = View.MeasureSpec.AT_MOST
            nestScrollView?.isFillViewport = true
        }
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        if (uiConfig.isInFloatingWindow.isChanged()) {
            if (uiConfig.isInFloatingWindow.current) {
                hideStatusBar()
            } else {
                showStatusBar()
            }
        }
        if (uiConfig.isChanged()) {
            safeShareDialog?.apply {
                if (isShowing()) {
                    updateSystemBarStatus(window, galleryShareDialog?.needImmersion(uiConfig) ?: false)
                }
                updateDialogIfNeed()
            }
        }
        setupViewStyle()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        //onAppUiStateChanged不响应某些系统UI变化，如不响应亮暗色变化，需要在此处更新style
        setupViewStyle()
    }

    override fun onStart() {
        super.onStart()
        enableOneTouchShare()
    }

    override fun onResume() {
        super.onResume()
        session.bringToFront()
    }

    override fun onPause() {
        super.onPause()
        session.bringToBack()
    }

    override fun onStop() {
        super.onStop()
        disableOneTouchShare()
    }

    override fun onDestroy() {
        super.onDestroy()
        GLog.d(TAG, "onDestroy.")
        activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED

        galleryView?.removeOnLayoutChangeListener(galleryLayoutChangeListener)
        galleryView?.removeCallbacks(notifyDataRunnable)
        session.terminal()
        getSp(ContextGetter.context).unregisterOnSharedPreferenceChangeListener(this)
        MultiProcessSpUtil.unregisterSPObserver(ContextGetter.context, convertImageObserver)
        MultiProcessSpUtil.unregisterSPObserver(ContextGetter.context, convertVideoObserver)
        safeShareDialog?.apply {
            securityShareSettingCallback = null
            takeIf { it.isShowing() }?.dismiss()
        }
        deleteRestoreTipCheck?.apply {
            takeIf { it.isShowing }?.dismissImmediately()
        }
        /** fragment被回收时，关闭一碰分享 ，避免泄漏*/
        disableOneTouchShare()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initView(view: View) {
        GTrace.trace("$TAG.initView") {
            titleView = view.findViewById(R.id.tv_title)
            view.findViewById<View>(R.id.iv_cancel).setOnClickListener {
                galleryShareDialog?.dismiss()
            }
            tvSecurityShareTitle = view.findViewById(R.id.tv_security_share_title)
            tvSecurityShareTitle?.setOnClickListener {
                showSecurityShareDialog()
            }
            galleryShareDialog?.onActionExitSelection = this::onActionExitSelection
            initOShareView(view)
            initNearbyShareView(view)
            initNestScrollView(view)
            initGalleryView(view)
            showOneTouchShareGuideDialogIfNeed()
        }
    }

    /**
     * 初始化ViewModel相关信息。
     */
    private fun initializeViewModel() {
        val albumModel = this.albumModel
        val selectionDataId = this.selectionDataId

        if ((selectionDataId == null) || (albumModel == null)) {
            GLog.w(TAG) { "[initializeViewModel] selectionDataId is null or albumModel is null" }
            onBackPressed()
            return
        }
        albumModel.setPositiveOrder(viewModel.isPositiveOrderFromSetting())
        albumModel.forceDirty()
        viewModel.selectionModel.setRealModel(albumModel)
        viewModel.setDataModel(albumModel)

        viewModel.activeInfoLiveData.observe(this) { activeDataInfo ->
            var needNotifyDataSetChanged = false
            val lastTotalCount = galleryAdapter?.data?.size
            val totalCount = viewModel.totalSize
            GLog.d(TAG) {
                "initializeViewModel. totalCount Changed.new=$totalCount,old=$lastTotalCount,differ=${activeDataInfo.differ}"
            }
            if ((totalCount == 0) || (lastTotalCount != totalCount)) {
                if (activeDataInfo.differ.isNullOrEmpty()) {
                    needNotifyDataSetChanged = true
                }
            }

            galleryAdapter?.data?.update(
                totalCount = viewModel.totalSize,
                activeRange = viewModel.reloadInfo.activeRange.toIntRange(),
                activeData = mutableListOf<ItemViewData?>().apply { addAll(activeDataInfo.activeViewDataArray) },
                activeSize = viewModel.reloadInfo.activeSize
            )

            // 刷新数据
            if (needNotifyDataSetChanged) {
                galleryAdapter?.notifyDataSetChanged()
            } else {
                activeDataInfo.differ?.let { differ ->
                    updatePageInfo()
                    updateOneTouchShareData()
                    diffCallback?.let(differ::dispatchUpdateEventsTo)
                }
                galleryAdapter?.refreshGalleryView()
            }
        }
    }

    private fun initOShareView(view: View) {
        when (oShareShowType) {
            OShareShowType.HIDE, OShareShowType.SHOW_IN_APP_LIST -> {
                view.findViewById<View>(R.id.send_oshare_fragment)?.visibility = View.GONE
                view.findViewById<View>(R.id.new_send_oshare_fragment)?.visibility = View.GONE
            }

            OShareShowType.SHOW_WITH_OLD_UI -> {
                view.findViewById<View>(R.id.send_oshare_fragment)?.visibility = View.VISIBLE
                view.findViewById<View>(R.id.new_send_oshare_fragment)?.visibility = View.GONE
            }

            OShareShowType.SHOW_WITH_NEW_UI -> {
                view.findViewById<View>(R.id.send_oshare_fragment)?.visibility = View.GONE
                view.findViewById<View>(R.id.new_send_oshare_fragment)?.visibility = View.VISIBLE
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initNestScrollView(view: View) {
        nestScrollView = view.findViewById(R.id.scroll_view)
        nestScrollView?.setSmartDrag(galleryShareDialog?.dialog)
    }

    private fun initNearbyShareView(view: View) {
        if (ShareUtil.highlightNearbyShare(context)) {
            view.findViewById<ViewStub>(R.id.nearby_share_stub)?.inflate()?.apply {
                nearbyShare = findViewById(R.id.send_nearbyshare_layout)
            }

            nearbyShare?.apply {
                downloadOriginalPhotoTrigger = this@SendMediaFragment::downloadOriginalPhoto
                initNearbySharing(activity as BaseActivity, galleryShareDialog!!)
            }
        }
    }

    private val deleteRunnable = Runnable {
        showDeleteTipView()
    }

    private fun ensureDeleteView(view: View) {
        //显示这个视图的条件是：由截图跳转过来
        view.findViewById<ViewStub>(R.id.share_tip_stub)?.inflate()?.apply {
            deleteTipView = findViewById(R.id.share_delete_tip_layout)
            deleteTipCheck = findViewById(R.id.checkbox)
            deleteTipView?.setOnClickListener {
                deleteTipCheck?.state =
                    if (deleteTipCheck?.state == COUICheckBox.SELECT_ALL) COUICheckBox.SELECT_NONE else COUICheckBox.SELECT_ALL
                onClickDeleteView()
            }
        }
    }

    private fun selectCheckBox(shareDeleteSelected: Boolean) {
        deleteTipCheck?.state = if (shareDeleteSelected) COUICheckBox.SELECT_ALL else COUICheckBox.SELECT_NONE
    }

    private fun showDeleteTipView() {
        val activity = activity ?: return
        if (!isShareDeleteTipsAsFirstShow && !activity.isFinishing && !activity.isDestroyed) {
            deleteRestoreTipCheck = COUIToolTips(context).apply {
                setDismissOnTouchOutside(false)
                setContent(resources.getString(R.string.share_page_delete_photos_find_tip))
                show(deleteTipView)
                activity?.getAppAbility<ISettingsAbility>()?.use {
                    it.setShareDeleteTipsAsFirstShow(true)
                }
                ShareTrackHelper.trackAndSendShareDeleteStatus(SHARE_DELETE_CLOSE)
            }
        }
    }

    private fun onClickDeleteView() {
        if (deleteTipCheck?.state == COUICheckBox.SELECT_ALL) {
            activity?.getAppAbility<ISettingsAbility>()?.use {
                it.setShareDeleteSelectedEnable(true)
            }
            ShareTrackHelper.trackAndSendShareDeleteStatus(SHARE_DELETE_OPEN)
        } else {
            activity?.getAppAbility<ISettingsAbility>()?.use {
                it.setShareDeleteSelectedEnable(false)
            }
            ShareTrackHelper.trackAndSendShareDeleteStatus(SHARE_DELETE_CLOSE)
        }
    }

    private fun showOneTouchShareGuideDialogIfNeed() {
        lifecycleScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
            if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE).not()) return@launch

            val isAtLeastOs15 = OSVersionUtils.isAtLeast(OSVersionUtils.OPLUS_OS_15_0_0)
            val isSupportNfcPhoneShare = ShareUtils.isSupportNfcPhoneShare(requireContext())
            val isAsFirstEnter = ConfigAbilityWrapper.getBoolean(ConfigID.Business.CloudSync.SharePage.SHARE_PAGE_AS_FIRST_ENTER, false)
            if (isAtLeastOs15.not() || isSupportNfcPhoneShare.not() || isAsFirstEnter) return@launch

            activity?.getAppAbility<ISettingsAbility>()?.use {
                it.setSharePageAsFirstEnter(true)
            }
            withContext(Dispatchers.UI) {
                if ((activity == null) || (activity?.isFinishing == true) || (activity?.isDestroyed == true)) return@withContext
                activity?.let {
                    OneTouchShareGuideDialog(it).show()
                }
            }
        }
    }

    private fun initGalleryView(view: View) {
        galleryView = view.findViewById<ShareGalleryRecyclerView?>(R.id.resolver_gallery_view).apply {
            setReverseLayout(galleryShareDialog?.isOrderRevert == true)
            isInvisible = true
            ShareGalleryAdapter(
                context = context,
                selection = viewModel,
                onVisibleRangeChanged = { start, end ->
                    viewModel.setVisibleRange(start, end)
                }
            ).let {
                galleryAdapter = it
                adapter = it
                diffCallback = DifferNotifyCallback(it)
                it.setOnItemClickListener { _, _ ->
                    updatePageInfo()
                    updateOneTouchShareData()
                }
                it.initCacheView(context)
            }

            // 设置居中图片
            setUpFocusPosition()

            // 监听画廊高度变化
            addOnLayoutChangeListener(galleryLayoutChangeListener)
        }
    }

    /**
     * 设置居中图片。
     */
    private fun ShareGalleryRecyclerView.setUpFocusPosition() {
        val albumModel = <EMAIL> ?: return
        selectionDataId?.let { viewModel.enterSelectionMode(it) }

        // 设置焦点的方法
        val setFocus = fun(index: Int) {
            viewModel.setVisibleRange(index, index)
            post {
                snapItemToCenter(index, false)
                isVisible = true
            }
            GLog.d(TAG) { "[setUpFocusPosition] focusPosition=$index" }
        }

        lifecycleScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
            // 1. 如果外部给的有path，使用外部给的
            galleryShareDialog?.itemPath?.let {
                // Marked: lichengli  从时间轴、图集页进来，考虑给一个指定的Path。
                albumModel.getIndexOfItem(it, 0).let(setFocus)
            } ?: let {
                //2. 如果没有，从图集中查选中图片中，在当前图集中最前的图片。
                val allPaths = albumModel.getAllPath()
                viewModel.getSelectedItems() // 获取选中图片
                    .map { path -> allPaths.indexOf(path) } // 获取在图集中的位置
                    .filter { index -> index != INVALID_INDEX } // 过滤无效index
                    .sorted() // 排序
                    .let {
                        if (viewModel.isPositiveOrderFromSetting()) {
                            // 如果是正序，获取最后一个
                            it.lastOrNull() ?: (viewModel.totalSize - 1)
                        } else {
                            // 否则获取第一个
                            it.firstOrNull() ?: 0
                        }
                    }.let(setFocus)
            }
        }
    }

    /**
     * 设置ViewStyle,会改变加载图片的样式。
     */
    private fun setupViewStyle() {
        StyleData().apply {
            put(StyleData.KEY_THUMB_SIZE_TYPE, ThumbnailSizeUtils.getFullThumbnailKey())
            put(StyleData.KEY_THUMB_STROKE_WIDTH, resources.getDimension(LibUIR.dimen.common_round_drawable_frame_stroke_width))
            put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, resources.getColor(BasebizR.color.common_round_drawable_frame_stroke_color, null))
            put(
                StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR,
                getDarkColorWithNightMode(context, resources.getColor(BasebizR.color.standard_default_bg_color_for_transparent, null))
            )
            put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
            viewModel.addStyle(StyleType.TYPE_RECT_THUMB_STYLE, this)
        }
    }

    /**
     * 更新界面相关信息
     * - 选中文本
     * - 隐私分享信息
     * - 分享应用面板
     */
    private fun updatePageInfo() {
        updateSelectTitle()
        updateShareIntentAndSendOptionInfo()
    }


    /**
     * 启用一碰分享
     */
    private fun enableOneTouchShare() {
        /** 系统不支持则不激活一碰分享 */
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ONE_TOUCH_SHARE).not()) {
            GLog.w(TAG, "[enableOneTouchShare] Failed : OneTouchShare is not supported!")
            return
        }

        if (oneTouchShareController == null) {
            val oShareServiceManager = oShareFragment?.oShareServiceManager
            GLog.d(TAG, "[enableOneTouchShare] oShareServiceManager = $oShareServiceManager")

            /** 复用 SendByOShareFragment 中的 oShareServiceManager */
            oneTouchShareController = OneTouchShareController(activity, this, session, oShareServiceManager)
            oneTouchShareController?.setScene(NfcScene.SCENE_SHARE)
            oneTouchShareController?.enable()
        }
        GLog.d(TAG, "[enableOneTouchShare] setOrUpdateSharePaths : selectedItems = ${viewModel.getSelectedItems().size} ")
        updateOneTouchShareData()
    }

    /**
     * 关闭一碰分享
     */
    private fun disableOneTouchShare() {
        oneTouchShareController?.disable()
        oneTouchShareController = null
    }

    /**
     * 时间轴/图集编辑模式将随GalleryShareDialog退出时 的回调
     */
    private fun onActionExitSelection() {
        oneTouchShareController?.terminate()
    }

    /**
     * 更新一碰分享待分享的数据
     */
    private fun updateOneTouchShareData() {
        viewModel.getSelectedItems().let { oneTouchShareController?.setOrUpdateSharePaths(it) }
    }

    /**
     * 更新一碰分享隐私保护配置参数
     */
    private fun updateSecurityShareErasedConfig() {
        oneTouchShareController?.setOrUpdateErasePrivacyConfig(
            securityShareHelper?.isEraseLocationInfo ?: false,
            securityShareHelper?.isEraseShotInfo ?: false
        )
    }

    /**
     * 更新一碰分享格式转换配置参数
     */
    private fun updateShareConvertFormatConfig() {
        oneTouchShareController?.setOrUpdateConvertFormatConfig(
            securityShareHelper?.isNeedConvertImage ?: false,
            securityShareHelper?.isNeedConvertVideo ?: false
        )
    }

    private fun updateSelectTitle() {
        val resources = context?.resources ?: return
        val selectedItems = viewModel.getSelectedItems()
        val selectedCount = selectedItems.size
        val videoCount = getSelectVideoCount(selectedItems)

        titleView?.text = when {
            (selectedCount == 0) -> resources.getString(BasebizR.string.base_title_select_image)

            (selectedCount > 0) && (videoCount == 0) -> {
                resources.getQuantityString(
                    BasebizR.plurals.base_title_has_select, selectedCount, selectedCount
                )
            }

            (selectedCount > 0) && (videoCount == selectedCount) -> {
                resources.getQuantityString(
                    BasebizR.plurals.base_title_has_select, selectedCount, selectedCount
                )
            }

            else -> {
                resources.getQuantityString(
                    BasebizR.plurals.base_title_has_select, selectedCount, selectedCount
                )
            }
        }
    }

    private fun getSelectVideoCount(selectedItems: Set<Path>): Int {
        var videoCount = 0
        selectedItems.forEach {
            val mediaType = DataManager.getMediaType(it)
            if (mediaType == MEDIA_TYPE_VIDEO) {
                videoCount++
            }
        }
        return videoCount
    }

    private fun updateShareIntentAndSendOptionInfo(
        forceReloadMediaObject: Boolean = false,
        isNeedSendOption: Boolean = true,
        endCallback: (() -> Unit)? = null
    ) {
        lifecycleScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
            val selectedItemsSet = viewModel.getSelectedItems()
            hasInvalidSharedMedia = hasInvalidSharedMedia(selectedItemsSet)
            val shareIntent = ShareUtils.makeShareIntent(selectedItemsSet, forceReloadMediaObject)
            withContext(Dispatchers.UI) {
                if (shareIntent != null) {
                    sendAppsFragment?.apply {
                        updateShareIntent(shareIntent)
                        setTouchable(true)
                    }
                    oShareFragment?.apply {
                        updateShareIntent(shareIntent)
                        setTouchable(true)
                    }
                    nearbyShare?.apply {
                        updateShareIntent(shareIntent)
                        updateIconAndText()
                        setTouchable(true)
                    }
                } else {
                    GLog.w(TAG, "setResolverGalleryAdapter originIntent is null!")
                    sendAppsFragment?.setTouchable(false)
                    oShareFragment?.setTouchable(false)
                    nearbyShare?.setTouchable(false)
                }
                if (isNeedSendOption) {
                    securityShareHelper?.getSendOptionInfo(selectedItemsSet)?.let {
                        locationStatus.hasPrivacyInfo = it.isHasLocation
                        shotInfoStatus.hasPrivacyInfo = it.isHasShotInfo
                        imageFormatStatus.hasConvertFormat = it.isHasImageConvertFormat
                        videoFormatStatus.hasConvertFormat = it.isHasVideoConvertFormat
                    }
                    if (selectedItemsSet.isNotEmpty()) {
                        initSecurityShareHelper()
                        updateSecurityShareErasedStatus()
                        updateShareConvertFormatStatus()
                        updateSecurityShareTitleView()
                    } else {
                        tvSecurityShareTitle?.visibility = View.GONE
                    }
                }
                endCallback?.invoke()
            }
        }
    }

    /**
     * 调用它会触发原图的下载（如果有瘦身图的话），并且如果有下载的话会下载弹框提示用户。
     * @param activityParam 给内部分享页ShareViewBinding传递Activity解耦用 其他用不到 mark:后续改造完成后统一在删除
     * @param successCallback 下载和准备成功的回调
     */
    private fun downloadOriginalPhoto(activityParam: Activity? = null, successCallback: ((Set<Path>?) -> Unit)?) {
        val localActivity = activity
        localActivity ?: return
        val selectedItems = galleryShareDialog?.selectionData?.getSelectedItems()
        if (selectedItems?.isEmpty() != false) {
            GLog.w(TAG, LogFlag.DL) { "[downloadOriginalPhoto] SelectedItems is no data , return!" }
            return
        }
        // 不管是移动网络还是wifi网络，如果有瘦身图，弹框提示用户是下载原图后发送还是直接发送
        lifecycleScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
            GLog.d(TAG, LogFlag.DL) { "[downloadOriginalPhoto] Execute file process task" }
            FileProcessTaskBuilder(
                localActivity,
                ArrayList(selectedItems),
                object : FinishListener {
                    override fun onFinished(success: Boolean, errCode: Int, errMsg: String?) {
                        GLog.d(TAG, LogFlag.DL) { "[onFinished] success: $success errCode: $errCode errMsg: $errMsg" }
                        if (!success) return
                        // 下载完之前可能已经退出，如果当前activity正在销毁或者已经被销毁，我们不应该继续去执行后续逻辑
                        if (localActivity.isFinishing || localActivity.isDestroyed) {
                            GLog.w(TAG, LogFlag.DL) { "[onFinished] activity is invalid , return!" }
                            return
                        }
                        cleanUpCache(localActivity)
                        /*
                         图片已下载完成，或者本地图片已存在，但是仍存在无效的分享数据（如mediaId仍为0），
                         则需要重新刷新一下mediaObject，否则继续分享会导致crash
                         */
                        val dataChanged = errCode == FinishListener.DATA_CHANGED_ERROR_CODE
                        val needReloadMediaObject = (errCode == FinishListener.NOT_MODIFIED_ERROR_CODE) && hasInvalidSharedMedia
                        if (dataChanged || needReloadMediaObject) {
                            updateShareIntentAndSendOptionInfo(needReloadMediaObject, isNeedSendOption = false) {
                                successCallback?.invoke(selectedItems)
                            }
                        } else {
                            runOnUiThread(lifecycleScope) {
                                successCallback?.invoke(selectedItems)
                            }
                        }
                    }
                },
                FileProcessScene.DOWNLOAD_ORIGIN_SCENE_SHARE
            ).addTaskType(FileProcessTaskBuilder.TASK_TYPE_DOWNLOAD_ORIG)
                .setCancelListener {
                    GLog.w(TAG, LogFlag.DL) {
                        "[downloadOriginalPhoto] canceled : lifecycle.currentState = ${<EMAIL>}"
                    }
                }
                .build()?.let {
                    it.execute()
                    it.autoCancelWhenOnDestroy(<EMAIL>)
                    GLog.d(TAG, LogFlag.DL) {
                        "[downloadOriginalPhoto] execute : lifecycle.currentState = ${<EMAIL>}"
                    }
                }
        }
    }

    private fun hasInvalidSharedMedia(selectedItems: Set<Path>?): Boolean {
        selectedItems ?: return true
        return selectedItems.find {
            val mediaItem = it.`object` as? LocalMediaItem
            mediaItem?.mediaId == 0
        } != null
    }

    private fun cleanUpCache(localActivity: Activity) {
        TransformManager(
            localActivity,
            session,
            TransformStorage.APP_PRIV_CACHE
        ).apply {
            submitCacheCleanUpTask(TransformType.HEIF)
            submitCacheCleanUpTask(TransformType.HDR_VIDEO)
            submitCacheCleanUpTask(TransformType.OLIVE)
        }
        SecurityShareHelper.submitCacheCleanUpTask()
    }

    private fun updateSecurityShareTitleView() {
        tvSecurityShareTitle?.apply {
            isEnabled = hasSendOptions()
            val arrow = if (isEnabled) resources.getDrawable(R.drawable.main_resover_security_share_arrow, context?.theme) else null
            val isRtl = ResourceUtils.isRTL(context)
            setCompoundDrawablesWithIntrinsicBounds(
                if (isRtl) arrow else null, null, if (isRtl) null else arrow, null
            )
            text = getSecurityShareTitle()
            visibility = if (text.isNotEmpty()) View.VISIBLE else View.GONE
        }
    }

    /**
     * 由于sendAppsFragment还没有add完成,以至于其adapter还未初始化，导致无法给其helper赋值，
     * 所以需要在withContext(UI)后，fragment已经add完成，调用此方法对各个fragment的helper进行赋值
     */
    private fun initSecurityShareHelper() {
        securityShareHelper?.apply {
            sendAppsFragment?.securityShareHelper = this
            oShareFragment?.securityShareHelper = this
            nearbyShare?.securityShareHelper = this
        }
    }

    /**
     * 更新文件是否需要抹除隐私信息
     */
    private fun updateSecurityShareErasedStatus() {
        // 应用分享照片或视频文件是否需要抹除隐私信息(含有的话)
        securityShareHelper?.apply {
            isEraseLocationInfo = locationStatus.isErasePrivacyInfo()
            isEraseShotInfo = shotInfoStatus.isErasePrivacyInfo()
        }
        updateSecurityShareErasedConfig()
    }

    /**
     * 更新文件是否需要转换格式
     */
    private fun updateShareConvertFormatStatus() {
        // 应用分享照片或视频文件是否需要转换格式(含有的话)
        securityShareHelper?.apply {
            isNeedConvertImage = imageFormatStatus.isConvertFormat()
            isNeedConvertVideo = videoFormatStatus.isConvertFormat()
        }
        updateShareConvertFormatConfig()
    }

    private fun showSecurityShareDialog() {
        val activity = activity ?: return

        if (safeShareDialog == null) {
            safeShareDialog = SecurityShareSettingDialog(activity)
        }
        safeShareDialog?.apply {
            if (!isShowing() && !activity.isFinishing && !activity.isDestroyed) {
                show(locationStatus.copy(), shotInfoStatus.copy(), imageFormatStatus.copy(), videoFormatStatus.copy())
            }
            securityShareSettingCallback = object : SecurityShareSettingDialog.SecurityShareDialogSettingCallback {
                override fun onComplete(
                    isLocationPrivacyEnabled: Boolean,
                    isShotPrivacyEnabled: Boolean,
                    isImageConvertEnabled: Boolean,
                    isVideoConvertEnabled: Boolean
                ) {
                    if (isDetached) {
                        GLog.d(TAG, "onComplete detached")
                        return
                    }
                    locationStatus.isPrivacySecurityEnabled = isLocationPrivacyEnabled
                    shotInfoStatus.isPrivacySecurityEnabled = isShotPrivacyEnabled
                    imageFormatStatus.isConvertFormatEnabled = isImageConvertEnabled
                    videoFormatStatus.isConvertFormatEnabled = isVideoConvertEnabled
                    tvSecurityShareTitle?.text = getSecurityShareTitle()
                    updateSecurityShareErasedStatus()
                    updateShareConvertFormatStatus()
                    GLog.d(TAG, "onComplete locationStatus=$locationStatus, shotInfoStatus=$shotInfoStatus")
                }

                override fun onCancel(isLocationPrivacyEnabled: Boolean, isShotPrivacyEnabled: Boolean) {
                    if (isDetached) {
                        GLog.d(TAG, "onCancel detached")
                        return
                    }
                    GLog.d(TAG, "onCancel locationStatus=$locationStatus, shotInfoStatus=$shotInfoStatus")
                }
            }
        }
    }

    @VisibleForTesting
    fun getSecurityShareTitle(): String {
        context ?: return TextUtil.EMPTY_STRING
        return when {
            // 不含地点信息、不含拍摄信息、不含有需要兼容格式的图片或视频 --result = ""(不展示文案)
            hasSendOptions().not() -> ""
            else ->
                // 其他情况展示发送选项
                getString(BasebizR.string.base_share_option_title)
        }
    }

    private fun hasSendOptions() = (locationStatus.hasPrivacyInfo) || (shotInfoStatus.hasPrivacyInfo)
            || (imageFormatStatus.hasConvertFormat) || (videoFormatStatus.hasConvertFormat)

    override fun onSharedPreferenceChanged(sp: SharedPreferences?, key: String?) {
        key ?: run {
            GLog.d(TAG, "onSharedPreferenceChanged: key is null")
            return
        }
        sp ?: run {
            GLog.d(TAG, "onSharedPreferenceChanged: sp is null")
            return
        }
        if (isDetached) {
            GLog.d(TAG, "onSharedPreferenceChanged: fragment detached:")
            return
        }
        if ((KEY_SECURITY_SHARE_IS_ERASE_LOCATION == key) || (KEY_SECURITY_SHARE_IS_ERASE_TAKE_PHOTO == key)) {
            // 一旦设置页修改了(只要修改了两个中的一个开关值),那么两个勾选状态都是以设置页的开关为准
            locationStatus.isPrivacySecurityEnabled = sp.getBoolean(KEY_SECURITY_SHARE_IS_ERASE_LOCATION, false)
            safeShareDialog?.setLocationPrivacyEnabled(locationStatus.isPrivacySecurityEnabled)

            shotInfoStatus.isPrivacySecurityEnabled = sp.getBoolean(KEY_SECURITY_SHARE_IS_ERASE_TAKE_PHOTO, false)
            safeShareDialog?.setShotPrivacyEnabled(shotInfoStatus.isPrivacySecurityEnabled)

            updateSecurityShareErasedStatus()
        }
        GLog.d(TAG, "onSharedPreferenceChanged: loc:$locationStatus,shot:$shotInfoStatus k=$key")
    }

    private fun setupJankMonitor() {
        // 分享对话框展开动画执行逻辑见控件COUIBottomSheetDialog#mOnPreDrawListener，在第一帧开始启动动画，卡顿监控也从第一帧开始
        nestScrollView?.doOnPreDraw {
            context?.let { OplusJankWrapper.gfxSceneBegin(it, JANK_SCENE_ID_ENTER_SHARE_ANIMATION, JANK_SCENE_DES_ENTER_SHARE_ANIMATION) }
        }
        galleryShareDialog?.setOnShowAnimationEndListener(object : COUIBottomSheetDialog.OnAnimationListener {
            override fun onShowAnimationEnd() {
                context?.let { OplusJankWrapper.gfxSceneEnd(it, JANK_SCENE_ID_ENTER_SHARE_ANIMATION) }
            }
        })
    }

    companion object {
        private const val TAG = "SendMediaFragment"
        private const val DELAY_REFRESH_TIME = 150L
        const val VIEWS_ENABLE_ALPHA = 1f
        const val VIEWS_DISABLE_ALPHA = 0.3f
        const val INVALID_INDEX = -1
        private const val SHOW_SHARE_DELETE_TIPS_DELAY = 500L

        @JvmStatic
        fun newInstance(
            panelDialog: GalleryShareDialog
        ): SendMediaFragment = SendMediaFragment().apply {
            galleryShareDialog = panelDialog
            selectionDataId = panelDialog.selectionData?.selectionDataId
            albumModel = panelDialog.albumModel
        }
    }
}