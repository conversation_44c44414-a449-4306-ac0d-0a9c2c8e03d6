/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ShareGalleryAdapter.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/14 14:11
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2022/5/14      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.sharepage

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.LayoutDirection
import android.util.Size
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.annotation.VisibleForTesting
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.animation.addListener
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.view.doOnLayout
import androidx.core.view.forEach
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.task.MediaItemThumbnailTask
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.constants.ThumbnailFadeInConstants
import com.oplus.gallery.business_lib.helper.ShareHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.selection.Selection
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.findFirstVisiblePosition
import com.oplus.gallery.foundation.util.ext.findLastVisiblePosition
import com.oplus.gallery.foundation.util.ext.isInvalid
import com.oplus.gallery.foundation.util.math.Math2DUtil
import com.oplus.gallery.foundation.util.math.isInvalid
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.typeface.TypefaceUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_PRODUCT_LIGHT
import com.oplus.gallery.sharepage.ShareGalleryAdapter.OnItemClickListener
import com.oplus.gallery.sharepage.widget.ShareGalleryRecyclerAdapter
import com.oplus.gallery.sharepage.widget.ShareGalleryRecyclerView
import com.oplus.gallery.standard_lib.graphics.drawable.RoundDrawable
import com.oplus.gallery.basebiz.R as BasebizR
import kotlin.math.abs

/**
 * 分享页的画廊适配器。
 */
class ShareGalleryAdapter(
    context: Context,
    private val selection: Selection,
    private val onVisibleRangeChanged: (Int, Int) -> Unit
) : ShareGalleryRecyclerAdapter<ShareGalleryAdapter.ItemViewHolder>() {

    /**
     * 占位图
     */
    private val placeHolderDrawable: Drawable? = AppCompatResources.getDrawable(context, R.drawable.main_default_resolver_pic)

    /**
     * 数据请求池，数据加载时，获取ViewData失败的请求会暂存此处。
     */
    private val bindDataRequests = mutableListOf<BindDataRequest>()

    /**
     * 点击事件回调
     */
    private var onItemClickListener: OnItemClickListener = OnItemClickListener { _, _ -> }

    // 用于保存加载AnimateVectorDrawable的资源，后续只需要复用，不用重复加载
    private var checkBtnDrawable: Drawable? = AppCompatResources.getDrawable(context, BasebizR.drawable.base_shape_btn_check)
    private var isFirstUserDrawable = true
    private val isLightOS by lazy { ConfigAbilityWrapper.getBoolean(IS_PRODUCT_LIGHT) }
    private val viewCache = mutableListOf<View>()
    private var viewIndex = 0

    /**
     * RecyclerView的滑动监听
     */
    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)
            val galleryView = <EMAIL> ?: return
            galleryView.layoutManager?.let {
                val maxPosition = (data.size - 1).coerceAtLeast(0)
                onVisibleRangeChanged(
                    it.findFirstVisiblePosition().coerceIn(0, maxPosition),
                    it.findLastVisiblePosition().coerceIn(0, maxPosition)
                )
            }
            recyclerView.forEach {
                (recyclerView.getChildViewHolder(it) as? ItemViewHolder)?.apply {
                    refreshCheckBoxPosition()
                    refreshMediaTypeIconPosition()
                    showOrHideMediaTypeIcon()
                }
            }
        }
    }

    /**
     * 当前数据
     */
    internal val data: SourceData = SourceData(0, IntRange.EMPTY, listOf(), 0)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
        val itemView = viewCache.getOrNull(viewIndex++)
        return ItemViewHolder(itemView ?: generateView(parent.context))
    }

    private fun generateView(context: Context): View {
        return RelativeLayout(context).apply {
            layoutParams = RecyclerView.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            addView(createPictureImageView(context))
            addView(createCheckBox(context))
            addView(createDurationTextView(context))
            addView(createGifImageView(context))
            addView(createFavoritesImageView(context))
        }
    }

    private fun createPictureImageView(context: Context): ImageView {
        return ImageView(context).apply {
            id = R.id.picture
            layoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            ).apply {
                addRule(RelativeLayout.CENTER_IN_PARENT)
            }
            isClickable = false
            contentDescription = null
            isFocusable = false
            isForceDarkAllowed = false
            scaleType = ImageView.ScaleType.CENTER_CROP
        }
    }

    private fun createCheckBox(context: Context): CheckBox {
        return CheckBox(context, null, 0).apply {
            id = R.id.checkbox
            layoutParams = RelativeLayout.LayoutParams(
                resources.getDimensionPixelSize(R.dimen.main_resolver_img_item_checkbox_wh),
                resources.getDimensionPixelSize(R.dimen.main_resolver_img_item_checkbox_wh)
            ).apply {
                addRule(RelativeLayout.ALIGN_PARENT_END)
                addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                setMargins(0, 0,
                    resources.getDimensionPixelSize(R.dimen.main_resolver_img_item_checkbox_margin),
                    resources.getDimensionPixelSize(R.dimen.main_resolver_img_item_checkbox_margin))
            }
            buttonDrawable = null
            isClickable = false
            isFocusable = false
            isForceDarkAllowed = false
            val drawable: Drawable?
            if (isFirstUserDrawable) {
                drawable = checkBtnDrawable
                isFirstUserDrawable = false
            } else {
                drawable = checkBtnDrawable?.constantState?.newDrawable()?.let { DrawableCompat.wrap(it).mutate() }
            }
            background = drawable
            visibility = View.INVISIBLE
        }
    }

    private fun createDurationTextView(context: Context): AppCompatTextView {
        return AppCompatTextView(context).apply {
            id = R.id.tv_duration
            layoutParams = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_tv_duration_height)
            ).apply {
                addRule(RelativeLayout.ALIGN_PARENT_START)
                addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                setMargins(
                    resources.getDimensionPixelSize(R.dimen.main_resolver_share_tv_duration_margin_start),
                    0,
                    0,
                    resources.getDimensionPixelSize(R.dimen.main_resolver_share_tv_duration_margin_bottom)
                )
            }
            gravity = android.view.Gravity.CENTER
            background = resources.getDrawable(BasebizR.drawable.base_video_duration_bg, null)
            setPadding(
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_tv_duration_padding_start),
                0,
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_tv_duration_padding_end),
                0
            )
            setTextColor(resources.getColor(BasebizR.color.base_white, null))
            textDirection = View.TEXT_DIRECTION_LOCALE
            typeface = TypefaceUtil.SANS_SERIF_MEDIUM
            setTextSize(TypedValue.COMPLEX_UNIT_PX, resources.getDimension(R.dimen.main_resolver_share_tv_duration_text_size))
            visibility = View.GONE
        }
    }

    private fun createGifImageView(context: Context): ImageView {
        return ImageView(context).apply {
            id = R.id.image_gif
            layoutParams = RelativeLayout.LayoutParams(
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_width),
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_height)
            ).apply {
                addRule(RelativeLayout.ALIGN_PARENT_START)
                addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
                setMargins(
                    resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_margin_start),
                    0,
                    0,
                    resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_margin_bottom)
                )
            }
            background = resources.getDrawable(BasebizR.drawable.base_video_duration_bg, null)
            contentDescription = null
            setPadding(
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_padding_start),
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_padding_top),
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_padding_end),
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_gif_padding_bottom)
            )
            setImageResource(BasebizR.drawable.base_ic_corner_gif)
            visibility = View.GONE
        }
    }

    private fun createFavoritesImageView(context: Context): ImageView {
        return ImageView(context).apply {
            id = R.id.image_favorites
            layoutParams = RelativeLayout.LayoutParams(
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_favorites_width),
                resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_favorites_height)
            ).apply {
                addRule(RelativeLayout.ALIGN_PARENT_TOP)
                addRule(RelativeLayout.ALIGN_PARENT_END)
                setMargins(
                    0,
                    resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_layout_margin_top),
                    resources.getDimensionPixelSize(R.dimen.main_resolver_share_image_layout_margin_End),
                    0
                )
            }
            contentDescription = null
            setImageResource(BasebizR.drawable.base_icon_albumfavorite_small)
            visibility = View.GONE
        }
    }

    /**
     * 初始化缓存view，用于createViewHolder时快速获取view
     */
    fun initCacheView(context: Context) {
        for (i in 0 until CACHE_VIEW) {
            viewCache.add(generateView(context))
        }
    }

    override fun onViewRecycled(holder: ItemViewHolder) {
        super.onViewRecycled(holder)
        holder.setImage(placeHolderDrawable, isRecycled = true)
        holder.setMediaIcon(null)
        holder.isChecked = false
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {

        // 1. 数据绑定请求
        val bindDataRequest = BindDataRequest(position, selection, holder)

        // 2. 获取当前ViewData。
        data[position]?.let { viewData ->
            // 2.1 获取成功，直接消费请求
            bindDataRequest.consume(viewData)
        } ?: let {
            // 2.2 获取失败，缓存请求，等待数据加载完毕
            bindDataRequests.add(bindDataRequest)
            galleryView?.let {
                // 没有加载到数据时，默认占满一屏，避免闪烁
                val aspectRatio = if (it.height == 0 || it.width == 0) {
                    DEFAULT_WIDTH_RATIO
                } else {
                    it.width.toFloat() / it.height
                }
                holder.setImage(placeHolderDrawable, aspectRatio)
                holder.setMediaIcon(null)
            }

            GLog.d(TAG) { "[onBindViewHolder] position: $position, not find viewData, cache request." }
        }
        // 3. 设置点击事件
        holder.itemView.setOnClickListener {
            dispatchOnItemClick(holder)
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        recyclerView.addOnScrollListener(scrollListener)
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        recyclerView.removeOnScrollListener(scrollListener)
    }

    override fun toString(): String {
        return super.toString() + "data.size = ${data.size}"
    }

    /**
     * 处理点击事件。
     */
    private fun dispatchOnItemClick(holder: ItemViewHolder) {
        // 0. 获取holder的位置
        val position = holder.layoutPosition

        // 1. 图片滑动到中心
        galleryView?.snapItemToCenter(position)

        // 2. 更新选中状态
        if (selection.isItemSelected(position)) {
            // 3.1 如果当前已选中，取消选中
            selection.unselectItem(position)
        } else {
            // 3.2 如果当前未选中，需要进行选中操作

            // 3.2.1 判断如果选中，是否超出最大分享图片限制
            ShareHelper.showLimitToast(
                selectedCount = selection.getSelectedItemCount() + 1, selectedLimit = ShareHelper.SEND_MAX_LIMIT
            ).let { isOverLimitCount ->
                // 超出显示，不做处理。
                if (isOverLimitCount) return
            }

            // 3.2.2 没超出限制，选中
            selection.selectItem(position)
        }

        // 3. 更新选中视图
        val isSelected = selection.isItemSelected(position)
        holder.isChecked = isSelected
        GLog.d(TAG) { "[dispatchOnItemClick] position:$position, isSelected:$isSelected." }

        // 4. 更新最新使用时间
        data[position]?.id?.let(Path::fromString)?.let {
            ApiDmManager.getCloudSyncDM().updateUsageTime(arrayListOf())
        }

        // 5. 向外分发点击事件
        onItemClickListener.onItemClick(holder.itemView, position)
    }

    override fun getItemCount(): Int = data.size

    /**
     * 设置Item点击事件监听
     */
    fun setOnItemClickListener(listener: OnItemClickListener) {
        onItemClickListener = listener
    }

    /**
     * 通知刷新画廊视图。
     * 在数据变化或横竖屏变化后，手动刷新一次视图，避免错位。
     */
    fun refreshGalleryView() {
        GLog.d(TAG, "refreshGalleryView:")
        galleryView?.refreshItemView()
        galleryView?.consumeCachedRequests()
    }

    /**
     * 刷新ItemView
     */
    private fun ShareGalleryRecyclerView.refreshItemView() {
        val manager = layoutManager ?: return
        post {
            (manager.findFirstVisiblePosition()..manager.findLastVisiblePosition())
                .sortedBy { position -> centerPosition - position } // 最靠近焦点的，最先刷新
                .forEach { (findViewHolderForAdapterPosition(it) as? ItemViewHolder)?.refreshView() }
        }
    }

    /**
     * 消费缓存的加载请求。
     */
    private fun ShareGalleryRecyclerView.consumeCachedRequests() {
        val center = centerPosition
        bindDataRequests.sortWith(compareBy { center - it.position }) // 根据
        val it = bindDataRequests.iterator()
        while (it.hasNext()) {
            val bindDataRequest = it.next()
            if (bindDataRequest.isValid.not()) {
                // 如果已经无效，弃用
                it.remove()
            } else {
                // 否则发起请求
                data[bindDataRequest.position]?.let { viewData ->
                    it.remove()
                    bindDataRequest.consume(viewData)
                }
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    inner class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val imageView: ImageView = itemView.findViewById(R.id.picture)
        private val checkBox: CheckBox = itemView.findViewById(R.id.checkbox)
        private val imageFavorites: ImageView = itemView.findViewById(R.id.image_favorites)
        private val imageGif: ImageView = itemView.findViewById(R.id.image_gif)
        private val tvVideoDuration: AppCompatTextView = itemView.findViewById(R.id.tv_duration)
        /**
         * 选中状态
         */
        var isChecked: Boolean
            get() = checkBox.isChecked
            set(value) {
                GLog.d(TAG) { "[isChecked] set isChecked. isChecked=$value" }
                checkBox.isChecked = value
            }

        /**
         * 刷新内部的View
         * - 选中器位置
         * - 图片显示
         */
        fun refreshView() {
            data[layoutPosition]?.let { viewData ->
                refreshCheckBoxPosition()
                refreshMediaTypeIconPosition()
                showOrHideMediaTypeIcon()
                BindDataRequest(layoutPosition, selection, this).consume(viewData)
            }
        }

        /**
         * 设置图片。
         * @param drawable 图片
         * @param aspectRatio 纵横比
         * @param isRecycled 是否回收
         */
        fun setImage(
            drawable: Drawable? = placeHolderDrawable,
            aspectRatio: Float = DEFAULT_WIDTH_RATIO,
            isRecycled: Boolean = false
        ) {
            val params = itemView.layoutParams
            val galleryHeight = galleryView?.height ?: 0
            val imageWith = if (galleryHeight == 0) DEFAULT_IMAGE_WIDTH else (aspectRatio * galleryHeight).toInt()
            if (imageWith != params.width) {
                params.width = imageWith
                itemView.layoutParams = params
            }
            itemView.doOnLayout {
                refreshCheckBoxPosition()
                refreshMediaTypeIconPosition()
                showOrHideMediaTypeIcon()
            }
            // 如果触发onViewRecycled设置默认图，需要重置imageView，防止下次设置图片有淡入动画导致闪屏
            if (isRecycled) {
                imageView.setImageDrawable(null)
                GLog.d(TAG, "[setImage] position: $layoutPosition isRecycle, reset drawable")
                return
            }

            if (imageView.drawable == drawable) return
            val previousDrawableIsPlaceHolder = imageView.drawable == placeHolderDrawable
            if (drawable?.isInvalid() == true) {
                GLog.e(TAG, LogFlag.DL) { "drawable is invalid and not set" }
                return
            }
            if (drawable != placeHolderDrawable) {
                // 提前通知到RT绘制，避免绘制时耗时
                ((drawable as? RoundDrawable)?.drawable as? BitmapDrawable)?.bitmap?.prepareToDraw()
                checkBox.visibility = View.VISIBLE
            }
            imageView.setImageDrawable(drawable)
            drawable.takeIf {
                previousDrawableIsPlaceHolder && (it != placeHolderDrawable)
            }?.let(::startFadeInAnimation)

            GLog.d(TAG) {
                "[setImage] position=$layoutPosition. ${if (drawable == placeHolderDrawable) "use placeHolder!" else ""}. " +
                        "galleryHeight=$galleryHeight, imageWith=$imageWith"
            }
        }

        /**
         * 设置媒体类型
         */
        fun setMediaIcon(mediaTypeData: MediaTypeData?) {
            if (mediaTypeData == null) {
                showFavoritesIcon(false)
                showGifIcon(false)
                showVideoIcon(false)
                return
            }
            mediaTypeData.apply {
                showFavoritesIcon(isFavorites)
                when (mediaType) {
                    GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE -> {
                        // 图片
                        showGifIcon(isGif)
                        showVideoIcon(false)
                    }
                    GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_VIDEO -> {
                        // 视频
                        showGifIcon(false)
                        if (durationInSec > 0) {
                            showVideoIcon(true)
                            setVideoDuration(durationInSec)
                        } else {
                            showVideoIcon(false)
                        }
                    }
                    else -> {
                        showVideoIcon(false)
                        showGifIcon(false)
                    }
                }
            }
        }

        /**
         * 是否显示收藏的图标
         */
        private fun showFavoritesIcon(isShow: Boolean) {
            imageFavorites.visibility = if (isShow) View.VISIBLE else View.GONE
        }

        /**
         * 是否显示Gif的图标
         */
        private fun showGifIcon(isShow: Boolean) {
            imageGif.visibility = if (isShow) View.VISIBLE else View.GONE
        }

        /**
         * 设置视频视频的
         */
        private fun setVideoDuration(durationSec: Long) {
            val text = SlotOverlayHelper.updateTimeText(durationSec)
            tvVideoDuration.text = text
        }

        /**
         * 是否显示视频图标
         */
        private fun showVideoIcon(isShow: Boolean) {
            tvVideoDuration.visibility = if (isShow) View.VISIBLE else View.GONE
        }

        /**
         * 设置图片时的渐变动画
         */
        private fun startFadeInAnimation(drawable: Drawable) {
            if (isLightOS) {
                return
            }
            val refreshAlpha = fun(alpha: Int) {
                imageView.background = if (alpha < ThumbnailFadeInConstants.END_ALPHA) placeHolderDrawable else null
                drawable.alpha = alpha
                imageView.invalidate()
            }
            ValueAnimator.ofInt(ThumbnailFadeInConstants.START_ALPHA, ThumbnailFadeInConstants.END_ALPHA).apply {
                duration = ThumbnailFadeInConstants.ANIMATION_DURATION
                interpolator = ThumbnailFadeInConstants.thumbnailFadeInInterpolator
                addUpdateListener { refreshAlpha((animatedValue as Int)) }
                addListener(onCancel = { refreshAlpha(ThumbnailFadeInConstants.END_ALPHA) })
                start()
            }
        }

        /**
         * 使列表横向滚动时，CheckBox始终在屏幕内
         */
         fun refreshCheckBoxPosition() {
            val galleryView = galleryView ?: return
            val param = checkBox.layoutParams as ViewGroup.MarginLayoutParams
            val scrollX = <EMAIL>?.scrollX ?: 0
            var offsetX = 0

            val absOffsetMax = itemView.width - checkBox.width - param.bottomMargin * 2
            val isRTL = checkBox.layoutDirection == LayoutDirection.RTL
            if (isRTL && (itemView.left < galleryView.left)) {
                offsetX = itemView.left - galleryView.left - scrollX
                offsetX = offsetX.coerceAtLeast(-absOffsetMax)
            } else if (!isRTL && (itemView.right > galleryView.right)) {
                offsetX = itemView.right - galleryView.right - scrollX
                offsetX = offsetX.coerceAtMost(absOffsetMax)
            }

            checkBox.translationX = -offsetX.toFloat()
        }

        /**
         * 刷新mediaType icon的位置
         */
        fun refreshMediaTypeIconPosition() {
            // 图标在左边的translationX 为正数 在右边为负数
            imageFavorites.translationX = -getRightTopItemIconViewTranslationX(imageFavorites)
            tvVideoDuration.translationX = getLeftBottomItemIconViewTranslationX(tvVideoDuration)
            imageGif.translationX = getLeftBottomItemIconViewTranslationX(imageGif)
        }

        /**
         * 显示或隐藏 MediaType Icon。
         * 如果 CheckBox 水平移动的距离和 MediaType Icon 水平移动的距离之和，大于可用的移动距离，则表示出现了重叠，此时需要隐藏 MediaType Icon，反之则显示。
         * 隐藏或显示 MediaType Icon 时设置 alpha 值，因为设置 MediaType Icon 的 Visibility 需要比较复杂的判断，容易出问题。
         */
        fun showOrHideMediaTypeIcon() {
            if (tvVideoDuration.visibility == View.VISIBLE) {
                val durationParams = tvVideoDuration.layoutParams as ViewGroup.MarginLayoutParams
                val cbParams = checkBox.layoutParams as ViewGroup.MarginLayoutParams
                val availableOffsetX = itemView.width - tvVideoDuration.width - durationParams.marginStart - checkBox.width - cbParams.marginEnd
                tvVideoDuration.alpha = if ((abs(checkBox.translationX) + abs(tvVideoDuration.translationX)) >= availableOffsetX) 0f else 1f
            }
            if (imageGif.visibility == View.VISIBLE) {
                val imageGifParams = imageGif.layoutParams as ViewGroup.MarginLayoutParams
                val cbParams = checkBox.layoutParams as ViewGroup.MarginLayoutParams
                val availableOffsetX = itemView.width - imageGif.width - imageGifParams.marginStart - checkBox.width - cbParams.marginEnd
                imageGif.alpha = if ((abs(checkBox.translationX) + abs(imageGif.translationX)) >= availableOffsetX) 0f else 1f
            }
        }

        private fun getRightTopItemIconViewTranslationX(iconView: View): Float {
            galleryView?.apply {
                val param = iconView.layoutParams as ViewGroup.MarginLayoutParams
                val scrollX = <EMAIL>?.scrollX ?: 0
                var offsetX = 0

                val absOffsetMax = itemView.width - iconView.width - param.topMargin * 2
                val isRTL = iconView.layoutDirection == LayoutDirection.RTL

                if (isRTL && (itemView.left < left)) {
                    offsetX = itemView.left - left - scrollX
                    offsetX = offsetX.coerceAtLeast(-absOffsetMax)
                } else if (!isRTL && (itemView.right > right)) {
                    offsetX = itemView.right - right - scrollX
                    offsetX = offsetX.coerceAtMost(absOffsetMax)
                }
                return offsetX.toFloat()
            }
            return 0.0f
        }

        private fun getLeftBottomItemIconViewTranslationX(iconView: View): Float {
            galleryView?.apply {
                val param = iconView.layoutParams as ViewGroup.MarginLayoutParams
                val scrollX = <EMAIL>?.scrollX ?: 0
                var offsetX = 0
                val absOffsetMax = itemView.width - iconView.width - param.bottomMargin * 2
                val isRTL = iconView.layoutDirection == LayoutDirection.RTL
                if (isRTL && (itemView.right > right)) {
                    offsetX = right - itemView.right - scrollX
                    offsetX = offsetX.coerceAtLeast(-absOffsetMax)
                } else if (!isRTL && (itemView.left < left)) {
                    offsetX = left - itemView.left - scrollX
                    offsetX = offsetX.coerceAtMost(absOffsetMax)
                }
                return offsetX.toFloat()
            }
            return 0.0f
        }
    }

    /**
     * 媒体类型的数据
     */
    data class MediaTypeData(
        /**
         * 是否威gif
         */
        val isGif: Boolean,
        /**
         * 是否威收藏
         */
        val isFavorites: Boolean,
        /**
         * 图片or视频
         */
        val mediaType: Int,
        /**
         * 时长
         */
        val durationInSec: Long
    )

    /**
     * 数据源
     */
    internal data class SourceData(
        private var totalCount: Int,
        private var activeRange: IntRange,
        private var activeData: List<ItemViewData?>,
        private var activeSize: Int
    ) {

        val size get() = totalCount

        /**
         * 更新数据
         */
        fun update(
            totalCount: Int,
            activeRange: IntRange,
            activeData: List<ItemViewData?>,
            activeSize: Int
        ) {
            this.totalCount = totalCount

            this.activeRange = activeRange

            this.activeData = activeData

            this.activeSize = activeSize
        }

        operator fun get(position: Int): ItemViewData? {
            if (activeRange.isInvalid()) {
                GLog.w(TAG, LogFlag.DL) { "[SourceData.get] activeRange is invalid" }
                return null
            }
            if (activeRange.contains(position)) {
                return activeData[position % activeSize.coerceAtLeast(1)]
            } else {
                GLog.w(TAG, LogFlag.DL) { "[SourceData.get] $position is not in $activeRange" }
                return null
            }
        }
    }

    /**
     * 数据绑定请求，当获取ViewData失败时，会缓存请求，并在数据加载完毕后消费
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    class BindDataRequest(
        val position: Int,
        private val selection: Selection,
        private val viewHolder: ItemViewHolder
    ) {

        /**
         * 当前请求是否有效
         */
        val isValid: Boolean get() = (position == viewHolder.layoutPosition)

        /**
         * 消费请求。
         */
        fun consume(viewData: ItemViewData) {
            // 1. 无效时，直接退出
            if (isValid.not()) {
                GLog.d(TAG, LogFlag.DL) { "consume: not valid, position=$position, layoutPosition=${viewHolder.layoutPosition}" }
                return
            }

            // 2. 给定一个标记，用来验证缩图加载完毕时，ViewHolder是否已经被复用
            viewHolder.itemView.tag = viewData.id
            val aspectRatio = viewData.findCorrectedItemSize()
                .run { width.toFloat() / height.coerceAtLeast(1) }
                .coerceIn(MIN_WIDTH_RATIO, MAX_WIDTH_RATIO)

            // 3. 处理缩图任务
            viewData.thumbnail?.let { thumbnailTask ->
                viewHolder.isChecked = selection.isItemSelected(position)
                val mediaTypeData = getMediaTypeData(thumbnailTask)

                thumbnailTask.onThumbnailLoaded = {
                    val currentViewDataId = viewData.id
                    val bindViewDataId = viewHolder.itemView.tag as? String
                    // 只有绑定的id和当前id相同时，才把图片给到viewHolder。
                    if (bindViewDataId == currentViewDataId) {
                        viewHolder.setImage(it, aspectRatio)
                        viewHolder.setMediaIcon(mediaTypeData)
                    } else {
                        GLog.d(TAG, LogFlag.DL) {
                            "consume: ids are different. currentViewDataId=$currentViewDataId, bindViewDataId=$bindViewDataId"
                        }
                    }
                }
                thumbnailTask.content?.let {
                    viewHolder.setImage(it, aspectRatio)
                    viewHolder.setMediaIcon(mediaTypeData)
                } ?: let {
                    // viewData有效时，thumbnailTask.content为空时,设置itemView宽高，设置为默认占位图
                    viewHolder.setImage(aspectRatio = aspectRatio)
                    viewHolder.setMediaIcon(null)
                    GLog.w(TAG) { "[BindDataRequest] consume fail, position: $position. thumbnailTask.content is null!" }
                }
            } ?: let {
                // viewData有效时，thumbnailTask为空时,设置itemView宽高，设置为默认占位图
                viewHolder.setImage(aspectRatio = aspectRatio)
                viewHolder.setMediaIcon(null)
                GLog.w(TAG) { "[BindDataRequest] consume fail, position: $position. thumbnailTask is null!" }
            }
        }

        private fun getMediaTypeData(thumbnailTask: MediaItemThumbnailTask): MediaTypeData {
            thumbnailTask.mediaItem.apply {
                return MediaTypeData(
                    isGif = MimeTypeUtils.isGif(mimeType),
                    isFavorites = isFavorite,
                    mediaType = mediaType,
                    durationInSec = durationInSec.toLong(),
                )
            }
        }


        /**
         * 获取根据旋转角度矫正过的图片宽高。
         *
         * ItemViewData直接获取的宽高是没有考虑旋转角度的，
         * 在方法内部会根据旋转角度对宽高进行校正。
         */
        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        fun ItemViewData.findCorrectedItemSize(): Size {
            val rotation = supportedAbilities.getInt(SlotOverlayHelper.SUPPORT_MEDIA_ROTATION)
            val shouldReversalWH = (rotation % Math2DUtil.DEG_180I) != 0
            val originalItemWidth = supportedAbilities.getInt(SlotOverlayHelper.SUPPORT_WIDTH)
            val originalItemHeight = supportedAbilities.getInt(SlotOverlayHelper.SUPPORT_HEIGHT)
            //如果原图尺寸大小为0，默认返回解析失败的默认图尺寸大小720*720
            if ((originalItemWidth <= 0) && (originalItemHeight <= 0)) {
                return Size(DECODE_FAILED_IMAGE_SIZE, DECODE_FAILED_IMAGE_SIZE)
            }
            val itemWidth = if (shouldReversalWH) originalItemHeight else originalItemWidth
            val itemHeight = if (shouldReversalWH) originalItemWidth else originalItemHeight
            return Size(itemWidth, itemHeight)
        }
    }

    /**
     * 点击事件监听
     */
    fun interface OnItemClickListener {
        fun onItemClick(view: View, position: Int)
    }

    companion object {
        private const val TAG = "ShareGalleryAdapter"
        const val MIN_WIDTH_RATIO = 36.0f / 235
        const val MAX_WIDTH_RATIO = 324.0f / 235
        //解析失败的占位图的尺寸大小
        const val DECODE_FAILED_IMAGE_SIZE = 720
        private const val DEFAULT_WIDTH_RATIO = 131.0f / 235
        private const val CACHE_VIEW = 4
        private const val DEFAULT_IMAGE_WIDTH = 360
    }
}