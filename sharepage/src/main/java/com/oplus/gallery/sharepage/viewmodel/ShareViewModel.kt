/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ShareViewModel.kt
 *          分享数据加载
 ** Description:
 ** OptionsParcelable
 **
 ** Version: 1.0
 ** Date: 2023/4/24
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><EMAIL>     2023/4/24     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.sharepage.viewmodel

import android.app.Application
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.person.set.FaceItem
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.viewmodel.AlbumViewModel
import com.oplus.gallery.business_lib.viewmodel.base.BaseReloadInfo
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.framework.abilities.data.model.BaseModel.Companion.COUNT_UNINITIALIZED
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst
import com.oplus.gallery.standard_lib.baselist.viewmodel.Range

/**
 * 分享页数据加载VM
 */
class ShareViewModel(application: Application) : AlbumViewModel(application) {
    override val id: String = TaskOwnerConst.SHARE

    override var reloadInfo = BaseReloadInfo<MediaItem, ItemViewData>(
        reloadVersion = BaseModel.INVALID_DATA_VERSION,
        cacheDataArray = arrayOfNulls(DATA_CACHE_SIZE),
        activeViewDataArray = arrayOfNulls(DATA_ACTIVE_SIZE),
        visibleRange = Range(),
        activeRange = Range(),
        cacheRange = Range(),
        reloadStartPosition = COUNT_UNINITIALIZED,
        reloadCount = COUNT_UNINITIALIZED,
        minLoadCount = MIN_LOAD_COUNT,
        maxLoadCount = MAX_LOAD_COUNT,
        visibleExpendSize = DATA_VISIBLE_SIZE,
        totalCount = COUNT_UNINITIALIZED
    )

    override fun createViewData(mediaItem: MediaItem, index: Int): ItemViewData {
        return super.createViewData(mediaItem, index).apply {
            // 分享页统一使用localMediaPath,不要使用personPath,避免SelectionMode和分享数据不统一
            (mediaItem as? FaceItem)?.refItem?.path?.let { localMediaPath ->
                id = localMediaPath.toString()
                supportedAbilities[SlotOverlayHelper.SUPPORT_SELECTED] = selectionModel.isItemSelected(localMediaPath)
            }
        }
    }

    override fun onSetDataModel() {
        // 置空，不执行父类的加载xqipCache的逻辑
    }

    override fun getTag() = TAG

    companion object {
        private const val TAG = "ShareViewModel"
        private const val DATA_CACHE_SIZE = 32
        private const val DATA_ACTIVE_SIZE = 16
        private const val DATA_VISIBLE_SIZE = 2
        private const val MIN_LOAD_COUNT = 4
        private const val MAX_LOAD_COUNT = 8
    }
}