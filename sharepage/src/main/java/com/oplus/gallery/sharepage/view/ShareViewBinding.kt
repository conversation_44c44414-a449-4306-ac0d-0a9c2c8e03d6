/********************************************************************************
 ** Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File: - ShareViewBinding.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2025-01-11
 ** Author: Fona<PERSON>.<PERSON><EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>    <date>    <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** F<PERSON><PERSON>.<PERSON><PERSON>@Android.Apps.Gallery    2025/01/11    1.0   Create
 ********************************************************************************/
package com.oplus.gallery.sharepage.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.app.ActionBar.LayoutParams
import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.ResolveInfo
import android.content.res.Configuration
import android.net.Uri
import android.provider.Settings
import android.text.TextUtils
import android.transition.TransitionSet
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.transition.doOnEnd
import androidx.core.transition.doOnStart
import androidx.core.view.doOnNextLayout
import androidx.core.view.doOnPreDraw
import androidx.core.view.isInvisible
import androidx.core.view.isNotEmpty
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.checkbox.COUICheckBox
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.coui.appcompat.indicator.COUIPageIndicator
import com.coui.appcompat.panel.COUIBottomSheetBehavior
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.panel.COUIViewMarginUtil
import com.coui.appcompat.tooltips.COUIToolTips
import com.oplus.gallery.addon.app.OplusJankWrapper
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_DES_ENTER_SHARE_ANIMATION
import com.oplus.gallery.addon.app.OplusJankWrapper.JANK_SCENE_ID_ENTER_SHARE_ANIMATION
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.selection.SelectionData
import com.oplus.gallery.business_lib.securityshare.data.CompatibleFormatStatus
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.util.ShareUtils
import com.oplus.gallery.business_lib.util.ShareUtils.OShareShowType
import com.oplus.gallery.business_lib.viewmodel.base.DifferNotifyCallback
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.base.UpdateEventsCallback
import com.oplus.gallery.business_lib.viewmodel.base.isNullOrEmpty
import com.oplus.gallery.foundation.ui.dialog.ListDialog
import com.oplus.gallery.foundation.ui.systembar.SystemBarController
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.isActivityInvalid
import com.oplus.gallery.foundation.util.ext.safeStartActivity
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_PRODUCT_LIGHT
import com.oplus.gallery.sharepage.ActionListener
import com.oplus.gallery.sharepage.R
import com.oplus.gallery.sharepage.SendMediaFragment.Companion.VIEWS_DISABLE_ALPHA
import com.oplus.gallery.sharepage.SendMediaFragment.Companion.VIEWS_ENABLE_ALPHA
import com.oplus.gallery.sharepage.ShareGalleryAdapter
import com.oplus.gallery.sharepage.ShareUtil
import com.oplus.gallery.sharepage.data.SecurityStatus
import com.oplus.gallery.sharepage.data.SelectionCountData
import com.oplus.gallery.sharepage.model.ResolveInfoData
import com.oplus.gallery.sharepage.securityshare.widget.OneTouchShareGuideDialog
import com.oplus.gallery.sharepage.securityshare.widget.SecurityShareSettingDialog
import com.oplus.gallery.sharepage.sendbyapp.SendByAppsFragment.Companion.GALLERY_PIN_LIST
import com.oplus.gallery.sharepage.sendbynearby.SendByNearbyShare
import com.oplus.gallery.sharepage.sendbyoshare.SendByOShareFragment
import com.oplus.gallery.sharepage.transition.PanelBackgroundFadeTransition
import com.oplus.gallery.sharepage.transition.ShareContentTransition
import com.oplus.gallery.sharepage.transition.ShareViewTranslationAnim
import com.oplus.gallery.sharepage.viewmodel.ShareInnerViewModel
import com.oplus.gallery.sharepage.widget.CustomDefaultItemAnimator
import com.oplus.gallery.sharepage.widget.GalleryFadeBackImageView
import com.oplus.gallery.sharepage.widget.HorizontalScrollPageAdapter
import com.oplus.gallery.sharepage.widget.ShareGalleryRecyclerView
import com.oplus.gallery.sharepage.widget.ShareScrollView
import com.oplus.gallery.standard_lib.app.SHARE_PAGE_PRIORITY_FOREGROUND
import com.oplus.gallery.standard_lib.app.UI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as BasebizR
import com.support.appcompat.R as COUIR

class ShareViewBinding private constructor(private val rootView: View) : IViewBinding, ActionListener {
    // 底部蒙层
    private var panelBackground: View? = null
    // 实际内容显示区域 不包含状态栏和导航栏
    var shareContentView: ViewGroup? = null
    // 工具栏区
    private var cancelView: TextView? = null
    private var titleView: TextView? = null
    private var tvSecurityShareTitle: TextView? = null
    private var safeShareDialog: SecurityShareSettingDialog? = null
    // 面板的整体的滚动View
    private var nestScrollView: ShareScrollView? = null
    // 画廊区
    private var galleryView: ShareGalleryRecyclerView? = null
    private var galleryAdapter: ShareGalleryAdapter? = null
    private var diffCallBack: UpdateEventsCallback? = null
    // 分享页OShare区域
    private val oShareShowType: OShareShowType by lazy {
        ShareUtils.getOShareTypeInSharePage()
    }
    private val highlightNearbyShare by lazy { ShareUtil.highlightNearbyShare(rootView.context) }
    private val hasFixOneRow by lazy { highlightNearbyShare && FeatureUtils.isSupportOplusShare && ShareUtils.isExportFirstApiLevelAtLeastU.not() }
    private var oShareFragment: SendByOShareFragment? = null
    private var nearbyShare: SendByNearbyShare? = null
    // app列表区域
    private var imageIndicator: COUIPageIndicator? = null
    private var viewPager: ViewPager2? = null
    private var pagerAdapter: HorizontalScrollPageAdapter? = null
    private val onPageChangeListener = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            imageIndicator?.onPageScrolled(position, positionOffset, positionOffsetPixels)
        }

        override fun onPageSelected(position: Int) {
            imageIndicator?.onPageSelected(position)
        }

        override fun onPageScrollStateChanged(state: Int) {
            imageIndicator?.onPageScrollStateChanged(state)
        }
    }
    private val itemHeight by lazy { rootView.context.resources.getDimensionPixelSize(R.dimen.main_resolver_share_recycleview_item_height) }
    private val iconWidth by lazy { rootView.context.resources.getDimensionPixelSize(R.dimen.main_oplus_resolver_icon_width) }
    private val iconHeight by lazy { rootView.context.resources.getDimensionPixelSize(R.dimen.main_oplus_resolver_icon_height) }
    private val itemTextColor by lazy { rootView.context.resources.getColor(BasebizR.color.common_resolve_dialog_item_textcolor) }
    private val itemLabelTextSize by lazy { rootView.context.resources.getDimension(R.dimen.main_resolver_share_item_label_text_size) }
    private val iconTopMargin by lazy {
        rootView.context.resources.getDimensionPixelSize(R.dimen.main_resolve_dialog_sharebyapp_item_icon_margin_top)
    }
    private val iconBottomMargin by lazy {
        rootView.context.resources.getDimensionPixelSize(R.dimen.main_resolve_dialog_sharebyapp_item_icon_margin_bottom)
    }
    private var viewCache: MutableList<ViewGroup>? = null
    // 实现具体分享业务的VM
    var viewModel: ShareInnerViewModel? = null
    // 负责面板整体的滑动处理
    private var behavior: COUIBottomSheetBehavior<View>? = null
    // 是否用户主动关闭面板，比如点击取消按钮，默认为true, 即没有执行分享操作
    var isCloseManual = true
    private var viewTranslationAnim: ShareViewTranslationAnim? = null
    private val isLightOS by lazy { ConfigAbilityWrapper.getBoolean(IS_PRODUCT_LIGHT) }
    private var deleteTipView: ConstraintLayout? = null
    private var deleteRestoreTipCheck: COUIToolTips? = null
    private val deleteRunnable = Runnable {
        showDeleteTipView()
    }

    init {
        initView()
    }

    override fun getRoot(): View {
        return rootView
    }

    private fun initView() {
        shareContentView = rootView.findViewById(R.id.share_content)
        nestScrollView = shareContentView?.findViewById(R.id.scroll_view)
        tvSecurityShareTitle = rootView.findViewById(R.id.tv_security_share_title)
        titleView = rootView.findViewById(R.id.tv_title)
        cancelView = rootView.findViewById(R.id.iv_cancel)
        galleryView = rootView.findViewById(R.id.resolver_gallery_view)
        viewPager = rootView.findViewById(R.id.apps_viewpager)
        imageIndicator = rootView.findViewById(R.id.resolver_dot_view)

        viewPager?.registerOnPageChangeCallback(onPageChangeListener)
        initPagerAdapter()
    }

    /**
     * 监听VM发送的liveData事件，刷新对应的UI
     * @param activity 需要监听的Activity，在其生命周期内
     */
    fun initVMObserve(activity: BaseActivity) {
        initShareUIEventObserve(activity)
        initSelectEventObserve(activity)
        initSecurityObserve(activity)
    }

    /**
     * 设置分享UI刷新事件liveData监听，比如刷新画廊、刷新应用列表、弹窗提示等事件
     */
    private fun initShareUIEventObserve(activity: BaseActivity) {
        viewModel?.apply {
            activeInfoLiveData.observe(activity) { activeDataInfo -> refreshGalleryUI(activeDataInfo) }
            isOrderRevert.observe(activity) { initGalleryView(it) }
            resolveInfoData.observe(activity) {
                updateShareUI(it.intent)
                setAppListData(it)
                executeDownCallback()
            }
            finishActivityEvent.observe(activity) { finish ->
                if (finish) {
                    activity.finish()
                }
            }
            shareCompleteEvent.observe(activity) { done ->
                if (done) {
                    completeShare(activity)
                }
            }
            resolvePinOrder.observe(activity) { onOrderChanged() }
            showOneTouchGuideDialog.observe(activity) { if (it) showOneTouchShareGuideDialog(activity) }
            showShareDelete.observe(activity) { showDeleteView(activity, it.isShareDeleteSelected, it.isShareDeleteTipsAsFirstShow) }
            startAppLiveData.observe(activity) {
                if (activity.isActivityInvalid()) return@observe
                /**
                 * startActivity 因系统侧会校验传递的uri，当uri数量比较多时，此方法比较耗时，可能导致ANR
                 * 但是放在子线程执行存在高负载情况不执行的风险，故只在自动化测试环境如此处理来避免提ANR问题单
                 */
                if (GProperty.PROPERTY_OTEST_RUNNING || GProperty.PROPERTY_MONKEY_RUNNING) {
                    activity.lifecycleScope.launch(Dispatchers.SHARE_PAGE_PRIORITY_FOREGROUND) {
                        activity.safeStartActivity(it.first, it.second)
                        withContext(Dispatchers.UI) {
                            completeShare(activity)
                        }
                    }
                } else {
                    activity.safeStartActivity(it.first, it.second)
                    completeShare(activity)
                }
            }
        }
    }

    private fun completeShare(activity: BaseActivity) {
        isCloseManual = false
        viewModel?.oneTouchShareViewModel?.terminate()
        activity.onBackPressedWithPredictive()
    }

    /**
     * 设置选中数据相关事件liveData监听，比如刷新选中标题，居中显示选中等
     */
    private fun initSelectEventObserve(activity: BaseActivity) {
        viewModel?.apply {
            selectionData.observe(activity) { updateSelectTitle(it) }
            selectionDataPath.observe(activity) { selectionDataPath ->
                if (selectionDataPath != null) {
                    loadOShareUI(activity.supportFragmentManager, selectionDataPath)
                    initNearbyShareView(activity, selectionDataPath)
                }
            }
            focusIndex.observe(activity) { index ->
                GLog.d(TAG, LogFlag.DL, "focusIndex: $index")
                setVisibleRange(index, index)
                snapItemToCenter(index)
            }
        }
    }

    /**
     * 设置安全分享相关事件liveData监听，比如安全分享
     */
    private fun initSecurityObserve(activity: BaseActivity) {
        viewModel?.apply {
            securityStatus.observe(activity) { showSecurityShareDialog(it) }
            hasSendOptions.observe(activity) { updateSecurityShareTitle(it) }
            securityStatusCheck.observe(activity) { updateSecurityCheck(it) }
            imageFormatStatus.observe(activity) { setImageConvertEnabled(it) }
            videoFormatStatus.observe(activity) { setVideoConvertEnabled(it) }
        }
    }

    /**
     * 初始化view相关操作
     * 1、滚动布局设置高度监听，为了动态修改画廊高度适配面板高度
     * 2、设置取消按钮点击返回
     * 3、初始化滑动behavior行为，用于面板拖拽
     * 4、根据中大屏动态设置面板宽高，更新app列表高度等布局行为
     * 5、更新状态栏显示或者隐藏
     * 6、安全分享选项弹窗
     * @param context 上下文
     * @param appUiConfig 窗口宽高等参数
     * @param contentView AppCompatActivity提供的根view
     */
    fun setupView(context: Context, appUiConfig: AppUiResponder.AppUiConfig, contentView: ViewGroup?) {
        nestScrollView?.addOnLayoutChangeListener { _, _, top, _, bottom, _, oldTop, _, oldBottom ->
            if ((bottom - top) != (oldBottom - oldTop)) {
                refreshGalleryHeight(context, bottom - top)
            }
        }
        cancelView?.setOnClickListener {
            goBack(context)
        }
        createPanelBackgroundView(contentView)
        setupBehavior(context)
        refreshShareContentLayout(context, appUiConfig)
        refreshViewpagerLayout(context, appUiConfig)
        updateSystemBarStatus(context, viewModel?.needImmersion(appUiConfig) ?: false)
        tvSecurityShareTitle?.setOnClickListener { showSecurityShareAction(context) }
        if (isLightOS) {
            createViewCache(context)
        }
    }

    private fun createViewCache(context: Context) {
        if (viewCache == null) {
            viewCache = mutableListOf()
            for (i in 0 until  DEFAULT_PAGE_COUNT) {
                viewCache?.add(createView(context))
            }
        }
    }

    private fun goBack(context: Context) {
        (context as? BaseActivity)?.onBackPressed()
    }

    private fun setupBehavior(context: Context) {
        shareContentView?.let { shareContentView ->
            behavior = COUIBottomSheetBehavior.from(shareContentView)
            behavior?.apply {
                setPanelState(COUIBottomSheetBehavior.STATE_EXPANDED)
                setIsHandlePanel(false)
                setPanelSkipCollapsed(true)
                applyPhysics(DEFAULT_PHYSICS_FREQUENCY, DEFAULT_PHYSICS_DAMPING_RATIO)
                setPanelPeekHeight(0)
                addBottomSheetCallback(object : COUIBottomSheetBehavior.COUIBottomSheetCallback() {
                    override fun onStateChanged(bottomSheet: View, newState: Int) {
                        if ((newState == COUIBottomSheetBehavior.STATE_COLLAPSED) || (newState == COUIBottomSheetBehavior.STATE_HIDDEN)) {
                            goBack(context)
                        }
                    }

                    override fun onSlide(bottomSheet: View, slideOffset: Float) = Unit
                })
                setGlobalDrag(false)
                setPanelDragListener { false }
            }
        }
    }

    private fun createPanelBackgroundView(contentView: ViewGroup?) {
        contentView?.let {
            panelBackground = View(it.context).apply {
                id = R.id.sharepage_panel_background
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                setBackgroundColor(ContextCompat.getColor(context, COUIR.color.coui_color_mask))
                isFocusable = false
                importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
                isSoundEffectsEnabled = false
                setOnTouchListener { _, event ->
                    if (event.action == MotionEvent.ACTION_UP) {
                        goBack(context)
                    }
                    true
                }
            }
            // 面板背景蒙层需要全屏 和rootView互相独立成兄弟节点关系 避免其因为适配系统状态栏而受到Margins或者padding影响
            it.addView(panelBackground, 0)
        }
    }

    /**
     * 动态修改面板宽度
     */
    private fun refreshShareContentLayout(context: Context, appUiConfig: AppUiResponder.AppUiConfig) {
        // 折叠屏副屏（合起）及普通手机竖屏时为小屏
        val isSmallScreen = COUIPanelMultiWindowUtils.isSmallScreen(context, null)
        var panelWith = LayoutParams.MATCH_PARENT
        if (isSmallScreen.not()) {
            val grid = context.resources.getInteger(com.support.grid.R.integer.grid_guide_column_bottom_sheet_dialog)
            val paddingNoneMode = 0
            val typeFlag = 0
            panelWith = COUIResponsiveUtils.calculateWidth(
                appUiConfig.windowWidth.current.toFloat(),
                appUiConfig.windowHeight.current.toFloat(),
                grid,
                typeFlag,
                paddingNoneMode,
                context
            ).toInt()
        }
        (shareContentView?.layoutParams as? CoordinatorLayout.LayoutParams)?.let { layoutParams ->
            if (panelWith != layoutParams.width) {
                shareContentView?.updateLayoutParams<CoordinatorLayout.LayoutParams> { width = panelWith }
            }
        }
    }

    /**
     * 动态修改画廊高度
     */
    private fun refreshGalleryHeight(context: Context, contentHeight: Int) {
        val activity = (context as? BaseActivity) ?: return
        // 窗口断点高度，小于此高度面板内容可滑动
        val breakpointWindowHeight = context.resources.getDimensionPixelSize(R.dimen.main_resolver_gallery_panel_min_content_height)
        // 可以滑动状态下，除把手，title高度外，画廊铺满剩余内容高度，底部加56dp
        val windowHeight = activity.getCurrentAppUiConfig().windowHeight.current
        GLog.d(TAG, LogFlag.DL) { "refreshGalleryHeight. cHeight=$contentHeight, wHeight=$windowHeight, breakpoint=$breakpointWindowHeight" }
        if (windowHeight < breakpointWindowHeight) {
            val galleryBottomHeight = context.resources.getDimensionPixelSize(R.dimen.main_resolver_gallery_bottom_height_in_smallLandscape)
            val containerHeight = contentHeight - (galleryView?.top ?: 0) - galleryBottomHeight
            galleryView?.updateLayoutParams<LinearLayout.LayoutParams> {
                height = containerHeight
            }
            nestScrollView?.measureSpec = View.MeasureSpec.UNSPECIFIED
            nestScrollView?.isFillViewport = false
        } else {
            galleryView?.updateLayoutParams<LinearLayout.LayoutParams> {
                height = 0
                weight = 1f
            }
            nestScrollView?.measureSpec = View.MeasureSpec.AT_MOST
            nestScrollView?.isFillViewport = true
        }
        galleryView?.doOnNextLayout {
            // 画廊图片宽度根据galleryView高度比例设置，在galleryView高度变化后，手动刷新一次视图，避免宽度显示异常
            galleryAdapter?.refreshGalleryView()
        }
    }

    /***
     * 进入窗口动效
     * 小屏：位移+透明度动画
     * 中大屏：中心缩放+透明度动画
     */
    fun getEnterTransition(context: Context): TransitionSet {
        val fade = PanelBackgroundFadeTransition(context)
        val shareContentTransition = ShareContentTransition(context)
        return TransitionSet().apply {
            addTransition(fade)
            addTransition(shareContentTransition)
            doOnStart {
                OplusJankWrapper.gfxSceneBegin(context, JANK_SCENE_ID_ENTER_SHARE_ANIMATION, JANK_SCENE_DES_ENTER_SHARE_ANIMATION)
            }
            doOnEnd {
                viewModel?.oneTouchShareViewModel?.showGuideDialogIfNeed()
                OplusJankWrapper.gfxSceneEnd(context, JANK_SCENE_ID_ENTER_SHARE_ANIMATION)
            }
        }
    }

    /**
     * 初始化画廊的列表
     * @param viewModel 分享VM 用于选中数据处理
     * @param reverseLayout 是否反向布局
     */
    private fun initGalleryView(reverseLayout: Boolean) {
        galleryView?.apply {
            // 轻量OS设置默认可见，减少布局和刷新
            isInvisible = isLightOS.not()
            viewModel?.let { viewModel ->
                ShareGalleryAdapter(
                    context = context,
                    selection = viewModel,
                    onVisibleRangeChanged = { start, end ->
                        viewModel.setVisibleRange(start, end)
                    }
                ).let {
                    galleryAdapter = it
                    adapter = it
                    diffCallBack = DifferNotifyCallback(it)
                    it.initCacheView(context)
                }
                setReverseLayout(reverseLayout)
                // 设置居中图片
                viewModel.setUpFocusPosition()
            }
            itemAnimator = CustomDefaultItemAnimator()
        }
    }

    /**
     * 设置app列表数据
     * @param resolveInfo 分享数据
     */
    private fun setAppListData(resolveInfo: ResolveInfoData) {
        if (resolveInfo.intent == null) {
            setTouchable(false)
            return
        }
        if (resolveInfo.dataList.isEmpty()) return
        setTouchable(true)
        pagerAdapter?.let { adapter ->
            adapter.updateIntent(resolveInfo.intent)
            val newList = mutableListOf<ResolveInfo>()
            newList.addAll(resolveInfo.dataList)
            resolveInfo.aiDataList?.takeIf { it.isNotEmpty() }?.let {
                /**
                 * 将智慧控件app,放到第二页
                 * 1.避免首次加载时,app的viewPager加载2-3页,布局耗时,最终导致启动分享dialog动画卡顿
                 * 2.避免首次加载完成后,app的viewPager刷新,导致闪一下app列表
                 */
                val pageSize = adapter.getPageNumber()
                val index = if (pageSize > 0) {
                    if (pageSize > newList.size) newList.size else pageSize
                } else 0
                newList.addAll(index, it)
            }
            adapter.setPagerDataList(resolveInfo.copy(dataList = newList, viewCache = viewCache?.toList()))
        }
        setImageIndicatorDotsCount(pagerAdapter?.itemCount ?: 0)
    }

    private fun initPagerAdapter() {
        if (pagerAdapter == null) {
            pagerAdapter = HorizontalScrollPageAdapter()
            pagerAdapter?.setActionListener(this)
            viewPager?.adapter = pagerAdapter
            if (hasFixOneRow) {
                pagerAdapter?.setPageRows(PAGE_ONE_ROWS)
                viewPager?.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = viewPager?.context?.resources?.getDimensionPixelSize(R.dimen.main_resolver_share_recycleview_item_height) ?: 0
                }
            }
        }
    }

    private fun setTouchable(enable: Boolean) {
        setViewEnable(viewPager, enable)
        setViewEnable(imageIndicator, enable)
        oShareFragment?.setTouchable(enable)
        nearbyShare?.setTouchable(enable)
    }

    private fun setViewEnable(view: View?, enable: Boolean) {
        view?.apply {
            isEnabled = enable
            alpha = if (enable) VIEWS_ENABLE_ALPHA else VIEWS_DISABLE_ALPHA
        }
    }

    /**
     * 设置 ImageIndicator 控件的数量
     */
    private fun setImageIndicatorDotsCount(pageSize: Int) {
        imageIndicator?.let {
            if (pageSize > 1) {
                it.dotsCount = pageSize
                it.isVisible = true
            } else {
                it.isVisible = false
            }
        }
    }

    /**
     * 刷新画廊显示
     * @param activeDataInfo 此次加载的数据结果
     */
    private fun refreshGalleryUI(activeDataInfo: ListViewModel<MediaItem, ItemViewData>.ActiveDataInfo) {
        var needNotifyDataSetChanged = false
        val lastTotalCount = galleryAdapter?.data?.size
        val totalCount = viewModel?.totalSize ?: 0
        GLog.d(TAG, LogFlag.DL) {
            "refreshGalleryUI. totalCount Changed.new=$totalCount,old=$lastTotalCount,differ=${activeDataInfo.differ}"
        }
        if (((totalCount == 0) || (lastTotalCount != totalCount)) && activeDataInfo.differ.isNullOrEmpty()) {
            needNotifyDataSetChanged = true
        }

        galleryAdapter?.data?.update(
            totalCount = totalCount,
            activeRange = viewModel?.reloadInfo?.activeRange?.toIntRange() ?: IntRange.EMPTY,
            activeData = mutableListOf<ItemViewData?>().apply { addAll(activeDataInfo.activeViewDataArray) },
            activeSize = viewModel?.reloadInfo?.activeSize ?: 0
        )

        // 刷新数据
        if (needNotifyDataSetChanged) {
            galleryAdapter?.notifyDataSetChanged()
        } else {
            activeDataInfo.differ?.let { differ ->
                diffCallBack?.let(differ::dispatchUpdateEventsTo)
            }
            galleryAdapter?.refreshGalleryView()
        }
    }

    /**
     * 更新选中的标题
     */
    private fun updateSelectTitle(selectionCountData: SelectionCountData) {
        val selectedCount = selectionCountData.selectedCount
        val videoCount = selectionCountData.videoCount
        titleView?.text = when {
            (selectedCount == 0) -> titleView?.resources?.getString(com.oplus.gallery.basebiz.R.string.base_title_select_image)
            (selectedCount > 0) && (videoCount == 0) -> {
                titleView?.resources?.getQuantityString(com.oplus.gallery.basebiz.R.plurals.base_title_has_select, selectedCount, selectedCount)
            }
            (selectedCount > 0) && (videoCount == selectedCount) -> {
                titleView?.resources?.getQuantityString(com.oplus.gallery.basebiz.R.plurals.base_title_has_select, selectedCount, selectedCount)
            }
            else -> titleView?.resources?.getQuantityString(com.oplus.gallery.basebiz.R.plurals.base_title_has_select, selectedCount, selectedCount)
        }
    }

    /**
     * 加载OShare分享组件
     * @param childFragmentManager Fragment管理
     * @param selectionData 选中数据
     */
    private fun loadOShareUI(childFragmentManager: FragmentManager, selectionData: SelectionData<Path>) {
        val fragmentContainerId = when (oShareShowType) {
            OShareShowType.SHOW_WITH_OLD_UI -> {
                GLog.d(TAG, LogFlag.DL) { "add SendOShareFragment" }
                rootView.findViewById<View>(R.id.send_oshare_fragment)?.visibility = View.VISIBLE
                R.id.send_oshare_fragment
            }

            OShareShowType.SHOW_WITH_NEW_UI -> {
                GLog.d(TAG, LogFlag.DL) { "add newSendOShareFragment" }
                rootView.findViewById<View>(R.id.new_send_oshare_fragment)?.visibility = View.VISIBLE
                R.id.new_send_oshare_fragment
            }

            else -> {
                GLog.d(TAG, LogFlag.DL) { "not add SendOShareFragment, OShareShowType = $oShareShowType" }
                return
            }
        }
        viewModel?.let { viewModel ->
            val fragmentTransaction = childFragmentManager.beginTransaction()
            oShareFragment = SendByOShareFragment().apply {
                downloadOriginalPhotoTrigger = viewModel::downloadOriginalPhoto
                this.selectionData = selectionData
                securityShareHelper = viewModel.securityViewModel.securityShareHelper
                this.viewModel = viewModel
                fragmentTransaction.add(fragmentContainerId, this)
            }
            fragmentTransaction.commitAllowingStateLoss()
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        viewModel?.onCreate()
    }

    override fun onStart(owner: LifecycleOwner) {
        viewModel?.onStart()
    }

    override fun onResume(owner: LifecycleOwner) {
        viewModel?.onResume()
        viewModel?.oneTouchShareViewModel?.onStateChanged(owner, Lifecycle.Event.ON_RESUME)
    }

    override fun onPause(owner: LifecycleOwner) {
        viewModel?.onPause()
        viewModel?.oneTouchShareViewModel?.onStateChanged(owner, Lifecycle.Event.ON_PAUSE)
    }

    override fun onStop(owner: LifecycleOwner) {
        viewModel?.onStop()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        viewModel?.onDestroy()
        viewPager?.unregisterOnPageChangeCallback(onPageChangeListener)
        panelBackground?.setOnTouchListener(null)
        safeShareDialog?.apply {
            securityShareSettingCallback = null
            takeIf { it.isShowing() }?.dismiss()
        }
        deleteRestoreTipCheck?.apply {
            takeIf { it.isShowing }?.dismissImmediately()
        }
        deleteTipView?.removeCallbacks(deleteRunnable)
    }

    override fun onOrderChanged() {
        viewModel?.resolveInfoVM?.updateResolveInfo()
    }

    override fun onItemClick(position: Int) {
        val activity = rootView.context as? BaseActivity ?: return
        viewModel?.downloadOriginalPhoto(activity) { selectedItems ->
            val resolveInfo = pagerAdapter?.getItemData()
            viewModel?.onHandleShare(activity, position, resolveInfo, selectedItems)
        }
    }

    override fun onItemLongClick(position: Int, resolveInfo: ResolveInfo) {
        val context = rootView.context ?: return
        val packageName = resolveInfo.activityInfo.packageName
        val className = resolveInfo.activityInfo.name
        val componentName = ComponentName(packageName, className).flattenToShortString()
        val pinPrefList: Set<String>?
        var pinned = false
        val galleryPinList = Settings.Secure.getString(context.contentResolver, GALLERY_PIN_LIST)
        GLog.d(TAG, LogFlag.DL) { "[onItemLongClick] galleryPinList = $galleryPinList" }

        if (!TextUtils.isEmpty(galleryPinList)) {
            pinPrefList = HashSet(galleryPinList.split(";"))
            pinned = pinPrefList.contains(componentName)
        }

        val itemsId = if (pinned) R.array.main_resolver_target_unpin else R.array.main_resolver_target_pin
        val clickListener = DialogInterface.OnClickListener { _: DialogInterface?, which: Int ->
            when (which) {
                TYPE_PIN_ORNOT -> ShareUtils.updatePinnedData(context, componentName)
                TYPE_APP_INFO -> {
                    context.safeStartActivity(
                        Intent()
                            .setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                            .setData(Uri.fromParts("package", packageName, null))
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_DOCUMENT)
                    )

                    (context as? Activity)?.overridePendingTransition(
                        COUIR.anim.coui_open_slide_enter,
                        COUIR.anim.coui_open_slide_exit
                    )
                }
                else -> GLog.d(TAG, LogFlag.DL) { "[onItemLongClick] onClick: do nothing" }
            }
        }

        ListDialog.Builder(context)
            .setItems(itemsId, clickListener)
            .setNegativeButton(BasebizR.string.common_cancel, null)
            .setWindowGravity(Gravity.BOTTOM)
            .setWindowAnimStyle(com.support.dialog.R.style.Animation_COUI_Dialog)
            .setOnDismissListener { onDetailDialogDismiss() }
            .build()
            .show()
        onDetailDialogShow()
    }

    override fun onDetailDialogShow() {
        behavior?.isDraggable = false
    }

    override fun onDetailDialogDismiss() {
        behavior?.isDraggable = true
    }

    /**
     * 更新OShare或者NearByShare相关UI，触发刷新安全分享选项
     * @param intent 分享intent
     */
    private fun updateShareUI(intent: Intent?) {
        oShareFragment?.apply {
            updateShareIntent(intent)
            setTouchable(intent != null)
        }
        nearbyShare?.apply {
            updateShareIntent(intent)
            updateIconAndText()
            setTouchable(intent != null)
        }
        // 安全分享选项数据在选中数据的加载之后更新，可以直接使用MediaItem的缓存
        viewModel?.securityViewModel?.updateSendOptionInfo()
    }

    /**
     * 初始化nearbyShare
     */
    private fun initNearbyShareView(activity: BaseActivity, selectionDataPath: SelectionData<Path>) {
        shareContentView?.let { view ->
            if (highlightNearbyShare) {
                view.findViewById<ViewStub>(R.id.nearby_share_stub)?.inflate()?.apply {
                    nearbyShare = findViewById<SendByNearbyShare?>(R.id.send_nearbyshare_layout).apply {
                        viewModel = <EMAIL>
                    }
                }
                viewModel?.let { viewModel ->
                    nearbyShare?.apply {
                        downloadOriginalPhotoTrigger = viewModel::downloadOriginalPhoto
                        securityShareHelper = viewModel.securityViewModel.securityShareHelper
                        initNearbySharing(activity, selectionDataPath)
                    }
                }
            }
        }
    }

    private fun showSecurityShareAction(context: Context) {
        val activity = context as? BaseActivity ?: return
        if (safeShareDialog == null) {
            safeShareDialog = SecurityShareSettingDialog(activity)
        }
        safeShareDialog?.apply {
            if (isShowing().not() && checkActivityInValid(activity).not()) {
                viewModel?.securityViewModel?.showSecurityShareDialog()
            }
            securityShareSettingCallback = object : SecurityShareSettingDialog.SecurityShareDialogSettingCallback {
                override fun onComplete(
                    isLocationPrivacyEnabled: Boolean,
                    isShotPrivacyEnabled: Boolean,
                    isImageConvertEnabled: Boolean,
                    isVideoConvertEnabled: Boolean
                ) {
                    if (checkActivityInValid(activity)) {
                        GLog.d(TAG, LogFlag.DL) { "onComplete detached" }
                        return
                    }
                    viewModel?.securityViewModel?.updateSecurityStatus(
                        isLocationPrivacyEnabled, isShotPrivacyEnabled, isImageConvertEnabled, isVideoConvertEnabled
                    )
                }

                override fun onCancel(isLocationPrivacyEnabled: Boolean, isShotPrivacyEnabled: Boolean) = Unit
            }
        }
    }

    private fun checkActivityInValid(activity: Activity): Boolean {
        return activity.isFinishing || activity.isDestroyed
    }

    /**
     * 显示安全分享对话框
     * @param data 包含安全状态信息的对象，包括位置状态、截图信息状态、图像格式状态和视频格式状态
     */
    private fun showSecurityShareDialog(data: SecurityStatus) {
        safeShareDialog?.show(data.locationStatus, data.shotInfoStatus, data.imageFormatStatus, data.videoFormatStatus)
    }

    /**
     * 更新安全分享标题的显示状态和内容
     * @param hasSendOptions 是否有存在可抹除隐私或者格式转换的媒体资源
     */
    private fun updateSecurityShareTitle(hasSendOptions: Boolean) {
        tvSecurityShareTitle?.apply {
            isEnabled = hasSendOptions
            val textString = when {
                // 不含地点信息、不含拍摄信息、不含有需要兼容格式的图片或视频 --result = ""(不展示文案)
                hasSendOptions.not() -> TextUtil.EMPTY_STRING
                // 其他情况展示发送选项
                else -> context.getString(BasebizR.string.base_share_option_title)
            }
            visibility = if (textString.isNotEmpty()) View.VISIBLE else View.GONE
            text = textString
            if (isVisible) {
                val arrow = if (isEnabled) resources.getDrawable(R.drawable.main_resover_security_share_arrow, context?.theme) else null
                val isRtl = ResourceUtils.isRTL(context)
                setCompoundDrawablesWithIntrinsicBounds(
                    if (isRtl) arrow else null, null, if (isRtl) null else arrow, null
                )
            }
        }
    }

    /**
     * 刷新发送选项的对话框里位置信息、拍摄信息的选中状态
     *
     * @param status 启用或者不启用的数据
     */
    private fun updateSecurityCheck(status: SecurityStatus) {
        safeShareDialog?.setLocationPrivacyEnabled(status.locationStatus.isPrivacySecurityEnabled)
        safeShareDialog?.setShotPrivacyEnabled(status.shotInfoStatus.isPrivacySecurityEnabled)
    }

    /**
     * 刷新发送选项的对话框里图片兼容格式选中状态
     */
    private fun setImageConvertEnabled(status: CompatibleFormatStatus) {
        safeShareDialog?.setImageConvertEnabled(status.isConvertFormatEnabled)
    }

    /**
     * 刷新发送选项的对话框里视频兼容格式选中状态
     */
    private fun setVideoConvertEnabled(status: CompatibleFormatStatus) {
        safeShareDialog?.setVideoConvertEnabled(status.isConvertFormatEnabled)
    }

    /**
     * 开启一碰分享
     */
    fun enableOneTouchShare(activity: BaseActivity) {
        viewModel?.oneTouchShareViewModel?.enableOneTouchShare(oShareFragment?.oShareServiceManager, activity)
    }

    /**
     * 关闭一碰分享
     */
    fun disableOneTouchShare() {
        viewModel?.oneTouchShareViewModel?.disableOneTouchShare()
    }

    /**
     * 一碰分享引导提示
     * @param activity 用于弹窗
     */
    private fun showOneTouchShareGuideDialog(activity: BaseActivity) {
        if (checkActivityInValid(activity).not()) {
            OneTouchShareGuideDialog(activity).show()
        }
    }

    /**
     * 将指定位置的项滚动居中，实际逻辑将在列表不为空时执行
     */
    private fun snapItemToCenter(index: Int) {
        galleryView?.apply {
            if (isNotEmpty()) {
                GLog.d(TAG, LogFlag.DL, "snapItemToCenter: directly")
                snapItemToCenter(index, this)
            } else {
                GLog.d(TAG, LogFlag.DL, "snapItemToCenter: addOnLayoutChangeListener")
                addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
                    override fun onLayoutChange(
                        view: View,
                        left: Int,
                        top: Int,
                        right: Int,
                        bottom: Int,
                        oldLeft: Int,
                        oldTop: Int,
                        oldRight: Int,
                        oldBottom: Int
                    ) {
                        if (isNotEmpty()) {
                            GLog.d(TAG, LogFlag.DL, "snapItemToCenter: onLayoutChange")
                            view.removeOnLayoutChangeListener(this)
                            snapItemToCenter(index, this@apply)
                        }
                    }
                })
            }
        }
    }

    private fun snapItemToCenter(index: Int, galleryView: ShareGalleryRecyclerView) {
        galleryView.apply {
            snapItemToCenter(index, false)
            isVisible = true
        }
    }

    /**
     * 刷新app列表的行列数
     * 小屏横屏，小屏分屏 1X4
     * 大屏分屏宽度< 360时，2x3
     * 其他情况，2x4
     */
    private fun refreshViewpagerLayout(context: Context, config: AppUiResponder.AppUiConfig) {
        if (hasFixOneRow) {
            // 固定单行显示的不需要动态修改行列数
            return
        }
        val oldPageNumber = pagerAdapter?.getPageNumber()
        if (ScreenUtils.isMiddleHeight(context)
            && config.isInMultiWindow.current
            && (config.windowWidthDp.current < LARGE_MULTI_WINDOW_WIDTH)) {
            viewPager?.updateLayoutParams<ViewGroup.LayoutParams> {
                height = context.resources.getDimensionPixelSize(R.dimen.main_resolver_share_recycleview_height)
            }
            pagerAdapter?.let {
                it.setPageRows(PAGE_ROWS)
                it.setPageColumns(LARGE_MULTI_WINDOW_PAGE_COLUMNS)
            }
        } else if (!ScreenUtils.isMiddleAndLargeScreen(context)) { //小屏幕设备
            //小屏横屏，小屏分屏 1X4
            if ((config.orientation.current == Configuration.ORIENTATION_LANDSCAPE)
                || config.isInMultiWindow.current) {
                viewPager?.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = context.resources.getDimensionPixelSize(R.dimen.main_resolver_share_recycleview_item_height)
                }
                pagerAdapter?.let {
                    it.setPageRows(SMALL_SCREEN_HORIZONTAL_PAGE_ROWS)
                    it.setPageColumns(PAGE_COLUMNS)
                }
            } else {
                //小屏竖屏，2x4
                viewPager?.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = context.resources.getDimensionPixelSize(R.dimen.main_resolver_share_recycleview_height)
                }
                pagerAdapter?.let {
                    it.setPageRows(PAGE_ROWS)
                    it.setPageColumns(PAGE_COLUMNS)
                }
            }
        } else { //其它
            viewPager?.updateLayoutParams<ViewGroup.LayoutParams> {
                height = context.resources.getDimensionPixelSize(R.dimen.main_resolver_share_recycleview_height)
            }
            pagerAdapter?.let {
                it.setPageRows(PAGE_ROWS)
                it.setPageColumns(PAGE_COLUMNS)
            }
        }
        if (oldPageNumber != pagerAdapter?.getPageNumber()) {
            pagerAdapter?.notifyDataSetChanged()
        }
        setImageIndicatorDotsCount(pagerAdapter?.itemCount ?: 0)
    }

    override fun onAppUiStateChanged(activity: BaseActivity, uiConfig: AppUiResponder.AppUiConfig) {
        if (uiConfig.windowWidth.isChanged()) {
            refreshShareContentLayout(activity, uiConfig)
            refreshViewpagerLayout(activity, uiConfig)
            updateSystemBarStatus(activity, viewModel?.needImmersion(uiConfig) ?: false)
        }
    }

    private fun updateSystemBarStatus(context: Context, immersive: Boolean) {
        val window = (context as? BaseActivity)?.window ?: return
        val controller = SystemBarController(window)
        if (immersive) {
            controller.hideStatusBar()
        } else {
            controller.showStatusBar()
        }
    }

    private fun showDeleteView(activity: BaseActivity, shareDeleteSelected: Boolean, shareDeleteTipsAsFirstShow: Boolean) {
        //显示这个视图的条件是：由截图跳转过来
        rootView.findViewById<ViewStub>(R.id.share_tip_stub)?.inflate()?.apply {
            deleteTipView = findViewById(R.id.share_delete_tip_layout)
            val deleteTipCheck: COUICheckBox? = findViewById(R.id.checkbox)
            deleteTipView?.setOnClickListener {
                deleteTipCheck?.state =
                    if (deleteTipCheck?.state == COUICheckBox.SELECT_ALL) COUICheckBox.SELECT_NONE else COUICheckBox.SELECT_ALL
                viewModel?.setShareDeleteSelectedEnable(deleteTipCheck?.state == COUICheckBox.SELECT_ALL)
            }
            deleteTipCheck?.state = if (shareDeleteSelected) COUICheckBox.SELECT_ALL else COUICheckBox.SELECT_NONE
            if (shareDeleteTipsAsFirstShow.not() && checkActivityInValid(activity).not()) {
                deleteRestoreTipCheck = COUIToolTips(activity).apply {
                    setDismissOnTouchOutside(false)
                    setContent(resources.getString(R.string.share_page_delete_photos_find_tip))
                }
                deleteTipView?.postDelayed(deleteRunnable, SHOW_SHARE_DELETE_TIPS_DELAY)
            }
        }
    }

    private fun showDeleteTipView() {
        val activity = deleteTipView?.context as? Activity
        if ((activity != null) && checkActivityInValid(activity).not()) {
            deleteRestoreTipCheck?.show(deleteTipView)
        }
    }

    /**
     * 使用View属性动画来实现进场，截屏拉起相册分享代码在外部，无法设置ActivityOptionsCompat启动参数配合Transition动画
     */
    fun doViewTranslationShowAnim() {
        panelBackground?.doOnPreDraw { view ->
            shareContentView?.let {
                val isSmallScreen = COUIPanelMultiWindowUtils.isSmallScreen(view.context, null)
                val startValue = it.measuredHeight + COUIViewMarginUtil.getMargin(it, COUIViewMarginUtil.DIRECTION_BOTTOM)
                if (isSmallScreen) {
                    it.translationY = startValue.toFloat()
                }
                viewTranslationAnim = ShareViewTranslationAnim()
                viewTranslationAnim?.doTranslationShowingAnim(isSmallScreen, startValue, it, view,
                    object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: Animator) {
                            viewModel?.oneTouchShareViewModel?.showGuideDialogIfNeed()
                        }
                    })
            }
        }
    }

    /**
     * 使用View动画来实现退场动画，和doViewTranslationShowAnim相反
     * @param listener 动画监听 用于结束后执行真正的Activity退出
     */
    fun doViewTranslationHideAnim(listener: Animator.AnimatorListener) {
        val contentView = shareContentView
        val backgroundView = panelBackground
        if ((contentView == null) || (backgroundView == null)) {
            listener.onAnimationEnd(ValueAnimator())
            return
        }
        val isSmallScreen = COUIPanelMultiWindowUtils.isSmallScreen(contentView.context, null)
        viewTranslationAnim?.doTranslationHideAnim(isSmallScreen, contentView, backgroundView, listener) ?:  listener.onAnimationEnd(ValueAnimator())
    }

    private fun createView(context: Context): ViewGroup {
        val linearLayout = LinearLayout(context)
        linearLayout.orientation = LinearLayout.VERTICAL
        linearLayout.gravity = Gravity.CENTER_HORIZONTAL
        linearLayout.layoutParams = LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            itemHeight
        )

        val imageView = GalleryFadeBackImageView(context)
        imageView.id = R.id.resolver_item_icon
        imageView.setImageDrawable(ResourcesCompat.getDrawable(context.resources, R.drawable.resolver_icon_placeholder, context.theme))
        imageView.scaleType = ImageView.ScaleType.FIT_XY
        imageView.layoutParams = LinearLayout.LayoutParams(iconWidth, iconHeight).apply {
            topMargin = iconTopMargin
            bottomMargin = iconBottomMargin
            gravity = Gravity.CENTER_HORIZONTAL
        }
        linearLayout.addView(imageView)

        val labelTextView = TextView(context)
        labelTextView.id = R.id.resolver_item_label
        labelTextView.ellipsize = TextUtils.TruncateAt.END
        labelTextView.gravity = Gravity.CENTER_HORIZONTAL
        labelTextView.isSingleLine = true
        labelTextView.setTextColor(itemTextColor)
        labelTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, itemLabelTextSize)
        labelTextView.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.CENTER_HORIZONTAL
        }
        linearLayout.addView(labelTextView)

        val nameTextView = TextView(context)
        nameTextView.id = R.id.resolver_item_name
        nameTextView.ellipsize = TextUtils.TruncateAt.END
        nameTextView.gravity = Gravity.CENTER_HORIZONTAL
        nameTextView.setLineSpacing(0f, LINE_SPACING_MULTIPLIER)
        nameTextView.maxLines = TEXT_MAX_LINE
        nameTextView.setTextColor(itemTextColor)
        nameTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, itemLabelTextSize)
        nameTextView.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.CENTER_HORIZONTAL
        }
        linearLayout.addView(nameTextView)
        return linearLayout
    }

    companion object {
        private const val TAG = "ShareViewBinding"
        private const val PAGE_ONE_ROWS = 1 //每页行数
        private const val DEFAULT_PHYSICS_FREQUENCY: Float = 16f
        private const val DEFAULT_PHYSICS_DAMPING_RATIO: Float = 0.6f
        private const val TYPE_PIN_ORNOT = 0
        private const val TYPE_APP_INFO = 1
        private const val DEFAULT_PAGE_COUNT = 8
        private const val PAGE_ROWS = 2 //每页行数
        private const val SMALL_SCREEN_HORIZONTAL_PAGE_ROWS = 1 //每页行数(小屏横屏)
        private const val PAGE_COLUMNS = 4 //每页列数
        private const val LARGE_MULTI_WINDOW_PAGE_COLUMNS = 3 //中大屏分屏(宽度<360)每页列数
        private const val LARGE_MULTI_WINDOW_WIDTH = 360 //中大屏分屏(宽度<360)
        private const val SHOW_SHARE_DELETE_TIPS_DELAY = 500L
        private const val LINE_SPACING_MULTIPLIER = 0.99f
        private const val TEXT_MAX_LINE = 2
        private const val LOADING_DIALOG_SHOW_DELAY_TIME: Long = 500

        @JvmStatic
        fun inflate(layoutInflater: LayoutInflater): ShareViewBinding {
            return ShareViewBinding(layoutInflater.inflate(R.layout.main_resolver_dialog_share_activity, null, false))
        }
    }
}