<resources>

    <style name="SecurityShareDialogStyle" parent="AlertDialogBuildStyle.Bottom.ListDialog">
        <item name="android:layout">@layout/main_security_share_dialog_layout</item>
    </style>

    <style name="SecurityShare" parent="COUIAlertDialog.Bottom">
        <item name="alertDialogStyle">@style/SecurityShareDialogStyle</item>
    </style>

    <style name="OneTouchShareGuideDialogScrollViewStyle" parent="COUIAlertDialogMessageScrollViewStyle">
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
    </style>

    <style name="ShareActivityStyle" parent="@style/CommonDefaultTheme">
        <item name="windowPreviewType">1</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowActivityTransitions">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
</resources>