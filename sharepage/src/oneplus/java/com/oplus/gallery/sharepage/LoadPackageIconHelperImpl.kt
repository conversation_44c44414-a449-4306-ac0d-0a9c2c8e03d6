/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LoadPackageIconHelperImpl.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/05/31
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2021/05/31		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.sharepage

import android.content.Context
import android.content.Intent
import android.content.pm.ResolveInfo
import android.graphics.drawable.BitmapDrawable
import androidx.annotation.WorkerThread
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.resolver.OplusGalleryLoadIconHelper
import com.oplus.gallery.sharepage.sendbyapp.ActionSendItemInfo

class LoadPackageIconHelperImpl(private val context: Context?) : ILoadPackageIconHelper {
    private var rVersionResolverInfoHelper: OplusGalleryLoadIconHelper? = OplusGalleryLoadIconHelper.getInstance(context)
    private val iconWidth by lazy {
        context?.resources?.getDimensionPixelSize(R.dimen.main_oplus_resolver_icon_width) ?: 0
    }
    private val iconHeight by lazy {
        context?.resources?.getDimensionPixelSize(R.dimen.main_oplus_resolver_icon_height) ?: 0
    }

    @WorkerThread
    override fun getIconItem(originIntent: Intent?, info: ResolveInfo, tag: String): ActionSendItemInfo? {
        val oplusItem = rVersionResolverInfoHelper?.getOplusItem(originIntent, info, context?.packageManager) ?: return null

        if (context == null) return null
        val bitmap = BitmapUtils.drawableToBitmap(oplusItem.icon, null, iconWidth, iconHeight)
        return bitmap?.let {
            ActionSendItemInfo(oplusItem.label, oplusItem.text, BitmapDrawable(context.resources, it), tag, info)
        } ?: ActionSendItemInfo(oplusItem.label, oplusItem.text, oplusItem.icon, tag, info)
    }

    override fun isSupportGetCustomIcon(): Boolean {
        return rVersionResolverInfoHelper != null
    }
}