/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OplusItem.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/06/08
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2021/06/08		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;

public class OplusItem {

    public static final int ITEM_FIRST = 0;
    public static final int ITEM_SECOND = 1;
    public static final int ITEM_THIRD = 2;
    public static final int ITEM_FOURTH = 3;
    public static final int ITEM_FIFTH = 4;

    private String mLabel;
    private String mText;
    private Drawable mIcon;
    private Context mContext;
    private Drawable mBackground;
    private OnItemClickListener mOnItemClickListener;

    private OplusItem() {
    }

    /**
     * Interface for MenuItem
     */
    public interface OnItemClickListener {

        public void onMenuItemClick(int position);
    }

    public static class Builder {

        private OplusItem mCi = new OplusItem();

        public Builder(Context context) {
            mCi.mContext = context;
        }

        public Builder setText(String text) {
            mCi.mText = text;
            return this;
        }

        public Builder setText(int textResId) {
            mCi.mText = mCi.getContext().getString(textResId);
            return this;
        }

        public Builder setLabel(int labelId) {
            mCi.mLabel = mCi.getContext().getString(labelId);
            return this;
        }

        public Builder setLabel(String label) {
            mCi.mLabel = label;
            return this;
        }

        public Builder setIcon(Drawable icon) {
            mCi.mIcon = icon;
            return this;
        }

        public Builder setIcon(int iconResId) {
            mCi.mIcon = mCi.getContext().getResources().getDrawable(iconResId, null);
            return this;
        }

        public Builder setBackground(Drawable background) {
            mCi.mBackground = background;
            return this;
        }

        public Builder setBackground(int bgResId) {
            mCi.mBackground = mCi.getContext().getResources().getDrawable(bgResId, null);
            return this;
        }

        public Builder setOnItemClickListener(OnItemClickListener e) {
            mCi.mOnItemClickListener = e;
            return this;
        }

        public OplusItem create() {
            return mCi;
        }
    }

    public String getText() {
        return mText;
    }

    public void setText(String mText) {
        this.mText = mText;
    }

    public String getLabel() {
        return mLabel;
    }

    public void setLabel(String subLabel) {
        this.mLabel = subLabel;
    }

    public Drawable getIcon() {
        return mIcon;
    }

    public void setIcon(Drawable mIcon) {
        this.mIcon = mIcon;
    }

    public Drawable getBackgroud() {
        return mBackground;
    }

    public void setBackgroud(Drawable backgroud) {
        mBackground = backgroud;
    }

    public Context getContext() {
        return mContext;
    }

    public void setContext(Context context) {
        mContext = context;
    }

    public OnItemClickListener getOnItemClickListener() {
        return mOnItemClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        mOnItemClickListener = onItemClickListener;
    }
}
