/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SecurityUrlConstant.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2023/3/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2023/3/16      1.0		   INIT
 *********************************************************************************/
package com.coloros.gallery3d.app

/**
 * 内销URL地址常量
 */
object SecurityUrlDomestic {

    const val CLOUD_HOST_NAME = "https://cloud.heytap.com"

    const val CLOUD_DNS_DOMESTIC = "https://ocloud-httpdns-cn.heytapmobi.com"
    // 欢太云管理云空间跳链
    const val OCLOUD_SPACE_MANAGE_URL = "htcloud://cloud.heytap.com/route?pageType=2&pageId=11&needLogin=true"

    const val VIDEO_EDITOR_HOST_NAME_DOMESTIC_TEST = "https://videoclip-romtest.wanyol.com"
    // 测试服务器的预发布测试地址
    const val VIDEO_EDITOR_HOST_NAME_PREPUBLISH_TEST = "https://videoclip-server-test.wanyol.com"
    // 预发布环境
    const val VIDEO_EDITOR_HOST_NAME_PREPUBLISH = "https://videoclip-pretest-cn.allawntech.com"
    const val VIDEO_EDITOR_HOST_NAME_DOMESTIC = "https://fourier-videoclip-cn.allawntech.com"

    const val USER_PROFILE_HOST_NAME_DOMESTIC = "https://proxy-apps-cn.allawntech.com"

    const val STICKER_HOST_DOMESTIC_TEST_CN = "http://stickerserver.store-test.wanyol.com"
    const val STICKER_HOST_DOMESTIC_TEST = "http://stickerserverwx.store-test.wanyol.com"
    const val STICKER_HOST_DOMESTIC_CN = "https://sticker-service-cn.allawntech.com"

    const val AI_REPAIR_HOST_DOMESTIC_TEST = "http://ai-repair-test.wanyol.com"
    const val AI_REPAIR_HOST_DOMESTIC = "https://ai-repair.ocloud.heytapmobi.com"

    const val AI_PHOTO_HOST_DOMESTIC_TEST = "http://idcard.romtest.wanyol.com"
    const val AI_PHOTO_HOST_DOMESTIC = "https://album-idcard.heytapmobi.com"

    const val CLOUD_KIT_HOST_DOMESTIC = "https://cloudkit-api-cn.heytapmobi.com"

    const val CLOUD_SHARED_DNS_HOST = "https://httpdns.ocloud.heytapmobi.com"

    const val CLOUD_SHARED_DEFAULT_HOST = "https://share-cn01a.ocloud.heytapmobi.com"

    const val CLOUD_SHARED_FAQ_HOST = "https://i-cn01a.ocloud.heytapmobi.com"

    const val FILING_INFO_HOST = "https://beian.miit.gov.cn"

    const val CLOUD_KIT_API_HOST_NAME = "ocloud.heytapmobi.com"
    const val AI_CLOUD_DEFAULT_URL = "https://aip-gw-cn.allawntech.com"

    const val RELEASE_HOST_CLOUD_ALBUM_H5: String = "https://ocloud-webdata-cn.heytapmobi.com"
    const val DEBUG_HOST_CLOUD_ALBUM_H5: String = "https://ocloud-album-cn-test.wanyol.com"
    const val PRE_HOST_CLOUD_ALBUM_H5: String = "https://ocloud-htprewebdata-cn.heytapmobi.com"

    const val URL_CLOUD_ALBUM_H5: String = "/cloud-album/?pageStyle=3&interceptBack=true&backPress=true&needAddTaskBarMargin=false"
}