/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SecurityUrlImpl.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/11/20
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                      <date>      <version>            <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery        2020/02/24      1.0          OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.coloros.gallery3d.app.model.http;

import static com.coloros.gallery3d.app.SecurityUrlExport.CLOUD_DNS_INDIA;
import static com.coloros.gallery3d.app.SecurityUrlExport.CLOUD_DNS_SG;
import static com.coloros.gallery3d.app.SecurityUrlExport.CLOUD_HOST_NAME;
import static com.coloros.gallery3d.app.SecurityUrlExport.CLOUD_HOST_NAME_ONEPLUS;
import static com.coloros.gallery3d.app.SecurityUrlExport.CLOUD_KIT_HOST_NAME;
import static com.coloros.gallery3d.app.SecurityUrlExport.OCLOUD_OFFLINE_HOST_NAME;
import static com.coloros.gallery3d.app.SecurityUrlExport.STICKER_HOST_NAME_INDIA;
import static com.coloros.gallery3d.app.SecurityUrlExport.STICKER_HOST_NAME_SG;
import static com.coloros.gallery3d.app.SecurityUrlExport.VIDEO_EDITOR_HOST_NAME_INDIA;
import static com.coloros.gallery3d.app.SecurityUrlExport.VIDEO_EDITOR_HOST_NAME_PREPUBLISH;
import static com.coloros.gallery3d.app.SecurityUrlExport.VIDEO_EDITOR_HOST_NAME_SG;
import static com.coloros.gallery3d.app.SecurityUrlExport.VIDEO_EDITOR_HOST_NAME_TEST;
import static com.coloros.gallery3d.app.SecurityUrlExport.VIDEO_EDITOR_HOST_NAME_TEST_PREPUBLISH;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_ONEPLUS_BRAND;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND;
import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_US;

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.business_lib.model.http.ISecurityUrl;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties;

/**
 * 空实现是因为外销没有该业务
 */
public class SecurityUrlImpl implements ISecurityUrl {

    public static final String TAG = "SecurityUrlImpl";
    /**
     * 预发布环境开关
     */
    private static final boolean PRE_PUBLISH_ENABLE = GallerySystemProperties.getBoolean(
            "debug.gallery.videoeditor.prepublish", false);
    // 测试服务器环境开关
    private static final boolean TEST_ENABLE = GallerySystemProperties.getBoolean("debug.gallery.videoeditor.test", false);
    // 测试服务器的预发布环境开关
    private static final boolean PRE_PUBLISH_TEST_ENABLE = GallerySystemProperties.getBoolean("debug.gallery.videoeditor.prepublish.test", false);

    private final ISecurityUrl mUrl;

    public SecurityUrlImpl() {
        GLog.d(TAG, "SecurityUrlImpl, export");
        if (FeatureUtils.isRegionIN()) {
            mUrl = new INSecurityUrlImpl();
        } else if (ConfigAbilityWrapper.getBoolean(IS_REGION_US)) {
            mUrl = new USSecurityUrlImpl();
        } else {
            mUrl = new DefaultSecurityUrlImpl();
        }
    }

    @Override
    public String getVideoEditorHostName() {
        return mUrl.getVideoEditorHostName();
    }

    @Override
    public String getUserProfileHostName() {
        return mUrl.getUserProfileHostName();
    }

    @Override
    public String getStickerHostName() {
        return mUrl.getStickerHostName();
    }

    @Override
    public String getCloudHostName() {
        return mUrl.getCloudHostName();
    }

    @Override
    public String getCloudDns() {
        return mUrl.getCloudDns();
    }

    @Override
    public String getOCloudSpaceManagerUrl() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getOCloudAiRepairHostName() {
        return mUrl.getOCloudAiRepairHostName();
    }

    @Override
    public String getOCloudAiPhotoHostName() {
        return mUrl.getOCloudAiPhotoHostName();
    }

    @Override
    public String getCloudKitHost() {
        return mUrl.getCloudKitHost();
    }

    @Override
    public String getCloudKitApiHost() {
        return mUrl.getCloudKitApiHost();
    }

    @Override
    public String getAiCloudHostName() {
        return mUrl.getAiCloudHostName();
    }

    @Override
    public String getOfflineCloudAnnounceDomainName() {
        return mUrl.getOfflineCloudAnnounceDomainName();
    }

    @Override
    public String getSharedDnsHost() {
        if (mUrl == null) {
            return TextUtil.EMPTY_STRING;
        }
        return mUrl.getSharedDnsHost();
    }

    @Override
    public String getSharedDefaultHost() {
        if (mUrl == null) {
            return TextUtil.EMPTY_STRING;
        }
        return mUrl.getSharedDefaultHost();
    }

    @Override
    public String getSharedFaqHost() {
        if (mUrl == null) {
            return TextUtil.EMPTY_STRING;
        }
        return mUrl.getSharedFaqHost();
    }

    @Override
    public String getFilingInfoHost() {
        if (mUrl == null) {
            return TextUtil.EMPTY_STRING;
        }
        return mUrl.getFilingInfoHost();
    }

    @Override
    public String getCloudAlbumH5Url() {
        return TextUtil.EMPTY_STRING;
    }

    private static boolean isOnePlusBrand() {
        return ConfigAbilityWrapper.getBoolean(IS_ONEPLUS_BRAND, false, false);
    }

    private static boolean isRealmeBrand() {
        return ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND, false, false);
    }

    private static class DefaultSecurityUrlImpl implements ISecurityUrl {

        @Override
        public String getVideoEditorHostName() {
            if (TEST_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_TEST;
            } else if (PRE_PUBLISH_TEST_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_TEST_PREPUBLISH;
            } else if (PRE_PUBLISH_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_PREPUBLISH;
            } else {
                return VIDEO_EDITOR_HOST_NAME_SG;
            }
        }

        @Override
        public String getUserProfileHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getStickerHostName() {
            return STICKER_HOST_NAME_SG;
        }

        @Override
        public String getCloudHostName() {
            return CLOUD_HOST_NAME;
        }

        @Override
        public String getCloudDns() {
            return CLOUD_DNS_SG;
        }

        @Override
        public String getOCloudSpaceManagerUrl() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOCloudAiRepairHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOCloudAiPhotoHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudKitHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudKitApiHost() {
            return CLOUD_KIT_HOST_NAME;
        }

        @Override
        public String getAiCloudHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOfflineCloudAnnounceDomainName() {
            return OCLOUD_OFFLINE_HOST_NAME;
        }

        @Override
        public String getSharedDnsHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getSharedDefaultHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getSharedFaqHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getFilingInfoHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudAlbumH5Url() {
            return TextUtil.EMPTY_STRING;
        }
    }

    private static class INSecurityUrlImpl implements ISecurityUrl {

        @Override
        public String getVideoEditorHostName() {
            if (TEST_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_TEST;
            } else if (PRE_PUBLISH_TEST_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_TEST_PREPUBLISH;
            } else if (PRE_PUBLISH_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_PREPUBLISH;
            } else {
                return VIDEO_EDITOR_HOST_NAME_INDIA;
            }
        }

        @Override
        public String getUserProfileHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getStickerHostName() {
            return STICKER_HOST_NAME_INDIA;
        }

        @Override
        public String getCloudHostName() {
            // 印度地区云主机地址在一加品牌有差异
            return isOnePlusBrand() ? CLOUD_HOST_NAME_ONEPLUS : CLOUD_HOST_NAME;
        }

        @Override
        public String getCloudDns() {
            return CLOUD_DNS_INDIA;
        }

        @Override
        public String getOCloudSpaceManagerUrl() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOCloudAiRepairHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOCloudAiPhotoHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudKitHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudKitApiHost() {
            return CLOUD_KIT_HOST_NAME;
        }

        @Override
        public String getAiCloudHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOfflineCloudAnnounceDomainName() {
            return OCLOUD_OFFLINE_HOST_NAME;
        }

        @Override
        public String getSharedDnsHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getSharedDefaultHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getSharedFaqHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getFilingInfoHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudAlbumH5Url() {
            return TextUtil.EMPTY_STRING;
        }
    }

    private static class USSecurityUrlImpl implements ISecurityUrl {

        @Override
        public String getVideoEditorHostName() {
            if (TEST_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_TEST;
            } else if (PRE_PUBLISH_TEST_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_TEST_PREPUBLISH;
            } else if (PRE_PUBLISH_ENABLE) {
                return VIDEO_EDITOR_HOST_NAME_PREPUBLISH;
            } else {
                return FeatureUtils.getVideoEditorUrl();
            }
        }

        @Override
        public String getUserProfileHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getStickerHostName() {
            return FeatureUtils.getStickerUrl();
        }

        @Override
        public String getCloudHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudDns() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOCloudSpaceManagerUrl() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOCloudAiRepairHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOCloudAiPhotoHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudKitHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudKitApiHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getAiCloudHostName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getOfflineCloudAnnounceDomainName() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getSharedDnsHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getSharedDefaultHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getSharedFaqHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getFilingInfoHost() {
            return TextUtil.EMPTY_STRING;
        }

        @Override
        public String getCloudAlbumH5Url() {
            return TextUtil.EMPTY_STRING;
        }
    }
}
