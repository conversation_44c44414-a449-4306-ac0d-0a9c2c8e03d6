// fixme: zhengyirui TFS单元测试mock报错，暂时屏蔽
///********************************************************************************
// ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
// ** All rights reserved.
// **
// ** File: - BitmapUtilsTest
// ** Description:
// ** Version: 1.0
// ** Date: 2020-07-09
// ** Author: zhongxuechang@Apps.Gallery3D
// ** TAG: OPLUS_ARCH_EXTENDS
// ** ------------------------------- Revision History: ----------------------------
// ** <author>                        <date>       <version>    <desc>
// ** ------------------------------------------------------------------------------
// ** zhongxuechang@Apps.Gallery3D    2020/07/09     1.0        OPLUS_ARCH_EXTENDS
// ********************************************************************************/
//package com.coloros.gallery3d.common.util.decode;
//
//import com.heytap.addon.media.MediaFile;
//
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.stubbing.Answer;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.powermock.api.mockito.PowerMockito.mockStatic;
//import static org.powermock.api.mockito.PowerMockito.when;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({MediaFile.class})
//public class BitmapUtilsTest {
//
//    @Before
//    public void setUp() {
//        mockStatic(MediaFile.class);
//        when(MediaFile.isRawImageMimeType(anyString())).thenAnswer((Answer<Boolean>) invocation -> {
//            String mimeType = invocation.getArgument(0);
//            switch (mimeType) {
//                case "image/x-adobe-dng":
//                case "image/tiff":
//                case "image/x-canon-cr2":
//                case "image/x-nikon-nrw":
//                case "image/x-sony-arw":
//                case "image/x-panasonic-rw2":
//                case "image/x-olympus-orf":
//                case "image/x-pentax-pef":
//                case "image/x-samsung-srw":
//                case "image/x-nikon-nef":
//                    return true;
//                default:
//                    return false;
//            }
//        });
//    }
//
//    @Test
//    public void should_ReturnFalse_when_isSupportedExif_with_empty_mime_type() {
//        Assert.assertFalse(BitmapUtils.isSupportedExif(null));
//        Assert.assertFalse(BitmapUtils.isSupportedExif(""));
//    }
//
//    @Test
//    public void should_ReturnTrue_when_isSupportedExif_with_supported_mime_type() {
//        Assert.assertTrue(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_HEIF));
//        Assert.assertTrue(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_HEIC));
//        Assert.assertTrue(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_RAW));
//        Assert.assertTrue(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_JPEG));
//    }
//
//    @Test
//    public void should_ReturnFalse_when_isSupportedExif_with_unsupported_mime_type() {
//        Assert.assertFalse(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_WEBP));
//        Assert.assertFalse(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_GIF));
//        Assert.assertFalse(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_ICON));
//        Assert.assertFalse(BitmapUtils.isSupportedExif(BitmapUtils.MIME_TYPE_PNG));
//    }
//}
