/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SecurityUrlImpl.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/11/20
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>						<date>			<version>		<desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery		2020/02/24		1.0			OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.coloros.gallery3d.app.model.http;

import static com.coloros.gallery3d.app.SecurityUrlDomestic.AI_CLOUD_DEFAULT_URL;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.AI_PHOTO_HOST_DOMESTIC;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.AI_REPAIR_HOST_DOMESTIC;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.CLOUD_DNS_DOMESTIC;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.CLOUD_HOST_NAME;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.CLOUD_KIT_API_HOST_NAME;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.CLOUD_KIT_HOST_DOMESTIC;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.CLOUD_SHARED_DEFAULT_HOST;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.CLOUD_SHARED_DNS_HOST;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.CLOUD_SHARED_FAQ_HOST;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.FILING_INFO_HOST;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.OCLOUD_SPACE_MANAGE_URL;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.STICKER_HOST_DOMESTIC_CN;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.USER_PROFILE_HOST_NAME_DOMESTIC;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.VIDEO_EDITOR_HOST_NAME_DOMESTIC;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.VIDEO_EDITOR_HOST_NAME_DOMESTIC_TEST;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.VIDEO_EDITOR_HOST_NAME_PREPUBLISH;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.VIDEO_EDITOR_HOST_NAME_PREPUBLISH_TEST;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.RELEASE_HOST_CLOUD_ALBUM_H5;
import static com.coloros.gallery3d.app.SecurityUrlDomestic.URL_CLOUD_ALBUM_H5;

import com.oplus.gallery.business_lib.model.http.ISecurityUrl;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.basebiz.helper.AiCloudRequester;
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties;

public class SecurityUrlImpl implements ISecurityUrl {

    public static final String TAG = "SecurityUrlImpl";
    private static final String OCLOUD_OFFLINE_HOST_NAME = "https://cloud.heytap.com";
    private static final boolean TEST_ENABLE = GallerySystemProperties.getBoolean("debug.gallery.videoeditor.test", false);
    // 测试服务器的预发布环境开关
    private static final boolean PRE_PUBLISH_TEST_ENABLE = GallerySystemProperties.getBoolean(
            "debug.gallery.videoeditor.prepublish.test", false);
    // 预发布环境
    private static final boolean PRE_PUBLISH_ENABLE = GallerySystemProperties.getBoolean(
            "debug.gallery.videoeditor.prepublish", false);

    public SecurityUrlImpl() {
        GLog.d(TAG, "SecurityUrlImpl, domesticRelease");
    }

    @Override
    public String getVideoEditorHostName() {
        if (TEST_ENABLE) {
            return VIDEO_EDITOR_HOST_NAME_DOMESTIC_TEST;
        } else if (PRE_PUBLISH_TEST_ENABLE) {
            return VIDEO_EDITOR_HOST_NAME_PREPUBLISH_TEST;
        } else if (PRE_PUBLISH_ENABLE) {
            return VIDEO_EDITOR_HOST_NAME_PREPUBLISH;
        } else {
            return VIDEO_EDITOR_HOST_NAME_DOMESTIC;
        }
    }

    @Override
    public String getUserProfileHostName() {
        return USER_PROFILE_HOST_NAME_DOMESTIC;
    }

    @Override
    public String getStickerHostName() {
        return STICKER_HOST_DOMESTIC_CN;
    }

    @Override
    public String getCloudHostName() {
        return CLOUD_HOST_NAME;
    }

    @Override
    public String getCloudDns() {
        return CLOUD_DNS_DOMESTIC;
    }

    @Override
    public String getOCloudSpaceManagerUrl() {
        return OCLOUD_SPACE_MANAGE_URL;
    }

    @Override
    public String getOCloudAiRepairHostName() {
        return AI_REPAIR_HOST_DOMESTIC;
    }

    @Override
    public String getOCloudAiPhotoHostName() {
        return AI_PHOTO_HOST_DOMESTIC;
    }

    @Override
    public String getCloudKitHost() {
        return CLOUD_KIT_HOST_DOMESTIC;
    }

    @Override
    public String getCloudKitApiHost() {
        return CLOUD_KIT_API_HOST_NAME;
    }

    @Override
    public String getAiCloudHostName() {
        return AI_CLOUD_DEFAULT_URL;
    }

    @Override
    public String getOfflineCloudAnnounceDomainName() {
        return OCLOUD_OFFLINE_HOST_NAME;
    }

    @Override
    public String getSharedDnsHost() {
        return CLOUD_SHARED_DNS_HOST;
    }

    @Override
    public String getSharedDefaultHost() {
        return CLOUD_SHARED_DEFAULT_HOST;
    }

    @Override
    public String getSharedFaqHost() {
        return CLOUD_SHARED_FAQ_HOST;
    }

    @Override
    public String getFilingInfoHost() {
        return FILING_INFO_HOST;
    }

    @Override
    public String getCloudAlbumH5Url() {
        return RELEASE_HOST_CLOUD_ALBUM_H5 + URL_CLOUD_ALBUM_H5;
    }
}