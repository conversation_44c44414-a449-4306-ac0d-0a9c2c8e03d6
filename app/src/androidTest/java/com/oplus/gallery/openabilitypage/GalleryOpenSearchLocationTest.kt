/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description:GalleryOpenProvider test
 ** Version: 1.0
 ** Date: 2020/11/25
 ** Author: 80264395 wujiafeng
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                   <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** 80264395 wujiafeng    2020/11/25      1.0
 ********************************************************************************/
package com.oplus.gallery.openabilitypage

import android.content.ContentResolver
import android.database.Cursor
import android.net.Uri
import androidx.test.rule.ActivityTestRule
import com.oplus.autotest.olt.testlib.annotations.CaseId
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.annotations.TestType
import com.oplus.autotest.olt.testlib.common.BasicData
import com.oplus.gallery.business_lib.data.helper.LocationHelper
import com.oplus.gallery.business_lib.data.helper.LocationHelper.addLocation
import com.oplus.gallery.business_lib.data.helper.LocationHelper.delete
import com.oplus.gallery.business_lib.data.helper.LocationHelper.locationList
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.QUERY_ALL_DIMENSIONS
import com.oplus.gallery.framework.abilities.mediadbsync.sync.MediaSyncManager
import com.oplus.gallery.framework.abilities.search.SearchType.TYPE_LOCATION
import com.oplus.gallery.openabilitypage.GalleryOpenProviderTestUtils.EXCEPTION_COUNT
import com.oplus.gallery.openabilitypage.GalleryOpenProviderTestUtils.EXCEPTION_COVERID
import com.oplus.gallery.openabilitypage.GalleryOpenProviderTestUtils.OPEN_SEARCH_ALBUMS_URI
import com.oplus.gallery.openabilitypage.GalleryOpenProviderTestUtils.adjustTheSearchCache
import com.oplus.gallery.searchpage.SearchActivity
import com.oplus.gallery.utils.TestConstants.LOCATION_CODE_ADDRESS
import com.oplus.gallery.utils.TestConstants.LOCATION_CODE_COUNTRY
import com.oplus.gallery.utils.TestConstants.LOCATION_CODE_DISTRICT
import com.oplus.gallery.utils.TestConstants.LOCATION_CODE_PROVINCE
import com.oplus.gallery.utils.TestConstants.LOCATION_CODE_STREET
import com.oplus.gallery.utils.TestConstants.NOEXTERNAL
import com.oplus.gallery.utils.TestPermissionUtils.setIsPrivacyPolicyAlert
import com.oplus.gallery.utils.TestAppUtils.openGallerySearchActivity
import com.oplus.gallery.utils.TestUiViewUtils.setScreenKeepAndUnlocks
import org.junit.After
import org.junit.AfterClass
import org.junit.Assert
import org.junit.Before
import org.junit.BeforeClass
import org.junit.Rule
import org.junit.Test

class GalleryOpenSearchLocationTest {

    @Rule
    @JvmField
    var searchActivityRule: ActivityTestRule<SearchActivity>? = null

    companion object {
        private val TAG = GalleryOpenSearchLocationTest::class.simpleName
        private val targetContext = BasicData.getTargetContext()
        private val sResolver: ContentResolver = targetContext.contentResolver

        @JvmStatic
        @BeforeClass
        fun classSetUp() {
            setIsPrivacyPolicyAlert(targetContext, false)
            setScreenKeepAndUnlocks()
            //Pause media library synchronization
            MediaSyncManager.getInstance().pause(true)
        }

        @JvmStatic
        @AfterClass
        fun classTearDown() {
            //Resume sync function
            MediaSyncManager.getInstance().resume()
        }
    }

    @Before
    fun setUp() {
        //清空测试资源
        delete()
        //预置测试资源
        addLocation()
        searchActivityRule = openGallerySearchActivity(null)
    }

    @After
    fun tearDown() {
        delete()
        searchActivityRule?.finishActivity()
    }

    /**
     * caseID: AT_AG_Photos_00278
     * 前提条件： 无
     * 【Given】 插入地点信息数据到数据库
     * 【When】 调用能力开放搜索接口搜索图片地理位置信息中的"国家"，如"中国"，查看结果匹配
     * 【Then】 搜索结果与预置测试数据相等,封面正确，文件数量正确
     */
    @Test
    @CaseId("AT_APP_Gallery_01628")
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    fun should_success_when_search_country_for_open_provider() {
        checkSearchLocationResult(OPEN_SEARCH_ALBUMS_URI, "中国", LOCATION_CODE_COUNTRY)
    }

    /**
     * caseID: AT_AG_Photos_00280
     * 前提条件： 无
     * 【Given】 插入地点信息数据到数据库
     * 【When】 调用能力开放搜索接口地理位置信息中的"省"，如"广东"，查看结果匹配
     * 【Then】 搜索结果与预置测试数据相等,封面正确，文件数量正确
     */
    @Test
    @CaseId("AT_APP_Gallery_01629")
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    fun should_success_when_search_province_for_open_provider() {
        checkSearchLocationResult(OPEN_SEARCH_ALBUMS_URI, "福建", LOCATION_CODE_PROVINCE)
    }

    /**
     * caseID: AT_AG_Photos_00284
     * 前提条件： 无
     * 【Given】 插入地点信息数据到数据库
     * 【When】 调用能力开放搜索接口地理位置信息中的"区"，如"南山"，查看结果匹配
     * 【Then】 搜索结果与预置测试数据相等,封面正确，文件数量正确
     */
    @Test
    @CaseId("AT_APP_Gallery_01630")
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    fun should_success_when_search_district_for_open_provider() {
        checkSearchLocationResult(OPEN_SEARCH_ALBUMS_URI, "武侯", LOCATION_CODE_DISTRICT)
    }

    /**
     * caseID: AT_AG_Photos_00286
     * 前提条件： 无
     * 【Given】 插入地点信息数据到数据库
     * 【When】 调用能力开放搜索接口地理位置信息中的"街道"，如"海德三道"，查看结果匹配
     * 【Then】 搜索结果与预置测试数据相等,封面正确，文件数量正确
     */
    @Test
    @CaseId("AT_APP_Gallery_01631")
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    fun should_success_when_search_street_for_open_provider() {
        checkSearchLocationResult(OPEN_SEARCH_ALBUMS_URI, "吉祥街", LOCATION_CODE_STREET)
    }


    /**
     * caseID: AT_AG_Photos_00297
     * 前提条件： 无
     * 【Given】 插入地点信息数据到数据库
     * 【When】 调用能力开放搜索接口地理位置信息中的"广东省深圳市南山区"关键字，查看搜索结果
     * 【Then】 搜索结果与预置测试数据相等,封面正确，文件数量正确
     */
    @Test
    @CaseId("AT_APP_Gallery_01632")
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    fun should_success_when_search_address_for_open_provide() {
        checkSearchLocationResult(OPEN_SEARCH_ALBUMS_URI, "广东深圳宝安西乡街道", LOCATION_CODE_ADDRESS)
    }

    /**
     * uri: OPEN_SEARCH_ALBUMS_URI or SMART_SEARCH_ALBUMS_URI
     * keyword: query location msg
     * locationCode
     */
    fun checkSearchLocationResult(uri: Uri, keyword: String, locationCode: String) {
        //1.搜索缓存
        adjustTheSearchCache()
        //2.传入查询参数
        val searchParameter = "$QUERY_ALL_DIMENSIONS?input=$keyword&force=true"
        //3.查询
        val cursor: Cursor? = sResolver.query(uri, null, keyword, arrayOf(searchParameter), null)
        //4.检测图集查询结果与实际是否一致
        cursor?.use {
            Assert.assertNotNull(cursor)
            Assert.assertEquals(1, cursor.count)
            while (cursor.moveToNext()) {
                var isExist = true
                //5.判断图集名称、类型、数量、封面图片是否正确
                val entry = GalleryOpenProviderTestUtils.SearchAlbumsEntry(cursor)
                for (locationInfo in locationList) {
                    val address = "${locationInfo.province}${locationInfo.city}${locationInfo.district}${locationInfo.street}"
                    val logMsg = "SearchAlbumsEntry: $entry, locationInfo: [address:$address, type:$TYPE_LOCATION, " +
                            "count:${locationInfo.count}, coverId:${locationInfo.coverId}]"
                    if ((locationCode == LOCATION_CODE_COUNTRY) && (entry.name == keyword)) {
                        //5.判断图集名称、类型、数量、封面图片是否正确
                        Assert.assertEquals(logMsg, TYPE_LOCATION, entry.type)
                        Assert.assertEquals(logMsg, EXCEPTION_COUNT, entry.count)
                        Assert.assertEquals(logMsg, EXCEPTION_COVERID, entry.coverId)
                        return
                    } else if ((locationCode == LOCATION_CODE_PROVINCE) && (entry.name == locationInfo.province)) {
                        assertLocationMsg(logMsg, entry, locationInfo)
                        return
                    } else if ((locationCode == LOCATION_CODE_DISTRICT) && (entry.name == locationInfo.district)) {
                        assertLocationMsg(logMsg, entry, locationInfo)
                        return
                    } else if ((locationCode == LOCATION_CODE_STREET) && (entry.name == locationInfo.street)) {
                        assertLocationMsg(logMsg, entry, locationInfo)
                        return
                    } else if ((locationCode == LOCATION_CODE_ADDRESS)) {
                        if (entry.name == address) {
                            //5.判断图集名称、类型、数量、封面图片是否正确
                            assertLocationMsg(logMsg, entry, locationInfo)
                            return
                        } else {
                            isExist = false
                        }
                    } else {
                        isExist = false
                    }
                }
                Assert.assertTrue(isExist)
            }
        }
    }

    private fun assertLocationMsg(
        logMsg: String,
        entry: GalleryOpenProviderTestUtils.SearchAlbumsEntry,
        locationInfo: LocationHelper.LocationInfo
    ) {
        Assert.assertEquals(logMsg, TYPE_LOCATION, entry.type)
        Assert.assertEquals(logMsg, locationInfo.count, entry.count)
        Assert.assertEquals(logMsg, locationInfo.coverId, entry.coverId)
    }
}