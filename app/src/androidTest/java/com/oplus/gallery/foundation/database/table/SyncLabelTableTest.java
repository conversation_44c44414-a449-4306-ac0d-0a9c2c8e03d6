/************************************************************
 * Copyright 2010-2019 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : SyncLabelTableTest.java
 * Version Number : 1.0
 * Description    : android test
 * Author         : 80264395
 * Date           : 2020-04-05
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2020-04-05, 80264395, create
 ************************************************************/
package com.oplus.gallery.foundation.database.table;

import static com.oplus.gallery.utils.TestConstants.NOEXTERNAL;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;

import com.oplus.autotest.olt.testlib.annotations.CaseId;
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization;
import com.oplus.autotest.olt.testlib.annotations.TestType;
import com.oplus.autotest.olt.testlib.common.BasicData;
import com.oplus.gallery.foundation.database.store.CloudStore;
import com.oplus.gallery.framework.abilities.mediadbsync.sync.MediaSyncManager;
import com.oplus.gallery.standard_lib.database.databaseAw.DataBaseUtils;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

public class SyncLabelTableTest {
    private static final String TAG = SyncLabelTableTest.class.getSimpleName();
    private static final String MD5 = "363e60f1d68ad2341a5217b966f0ed73";
    private static final String VERSION = "1";
    private static Context sContext = BasicData.getTargetContext();

    @BeforeClass
    public static void beforeClass() {
        //Pause media library synchronization
        MediaSyncManager.getInstance().pause(true);
    }

    @Before
    public void setUp() {
        //clear local media data
        //deleteSyncLabelData();
        DataBaseUtils.deleteDbData(sContext, CloudStore.SyncLabel.getContentUri(), null);
    }

    @After
    public void tearDown() {
    }

    @AfterClass
    public static void afterClass() {
        deleteDBTestData();
        sContext = null;
        //Resume sync function
        MediaSyncManager.getInstance().resume();
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01540")
    public void should_return_success_when_insert_sync_label_with_uri_values() {
        ContentValues values = new ContentValues();
        values.put(CloudStore.SyncLabel.MD5, MD5);
        Uri uri = sContext.getContentResolver().insert(CloudStore.SyncLabel.getContentUri(), values);
        Assert.assertNotNull(uri);
    }


    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01541")
    public void should_return_success_when_insert_sync_label_data_with_1000_values() {
        for (int i = 0; i < DataBaseUtils.MIDDLE_SCALE_DATA; i++) {
            ContentValues values = new ContentValues();
            values.put(CloudStore.SyncLabel.MD5, MD5);
            Uri uri1 = sContext.getContentResolver().insert(CloudStore.SyncLabel.getContentUri(), values);
            Assert.assertNotNull(uri1);
        }
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01542")
    public void should_return_success_when_insert_sync_label_data_with_1w_values() {
        for (int i = 0; i < DataBaseUtils.MUCH_MAX_SCALE_DATA; i++) {
            ContentValues values = new ContentValues();
            values.put(CloudStore.SyncLabel.MD5, MD5);
            Uri uri1 = sContext.getContentResolver().insert(CloudStore.SyncLabel.getContentUri(), values);
            Assert.assertNotNull(uri1);
        }
    }


    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01543")
    public void should_return_success_when_bulk_insert_with_1000_values() {
        ContentValues[] valuesArray = new ContentValues[DataBaseUtils.MAX_SCALE_DATA];
        for (int i = 0; i < DataBaseUtils.MAX_SCALE_DATA; i++) {
            ContentValues values = new ContentValues();
            values.put(CloudStore.SyncLabel.MD5, MD5);
            valuesArray[i] = values;
        }

        int bulkInsertResult = sContext.getContentResolver().bulkInsert(CloudStore.SyncLabel.getContentUri(), valuesArray);
        Assert.assertEquals(DataBaseUtils.MAX_SCALE_DATA, bulkInsertResult);
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01545")
    public void should_return_success_when_update_sync_label_with_selection() {
        //1.import 100 data
        insertSyncLabelData(DataBaseUtils.MIDDLE_SCALE_DATA);
        //2.update data
        String where = CloudStore.SyncLabel.MD5 + " like '%7b966f0ed73'";
        ContentValues values = new ContentValues();
        values.put(CloudStore.SyncLabel.VERSION, VERSION);
        int updateCount = sContext.getContentResolver().update(CloudStore.SyncLabel.getContentUri(), values, where, null);
        //3.verify update result
        int exceptedNum = 100;
        Assert.assertEquals(exceptedNum, updateCount);
        //Assert.assertTrue("Test true!", 100 == updateCount);
    }


    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01544")
    public void should_return_success_when_query_sync_label_with_selection_100() {
        //1.import 100 data
        insertSyncLabelData(DataBaseUtils.MIDDLE_SCALE_DATA);
        //2.query data
        String where = CloudStore.SyncLabel.MD5 + " like '%b966f0ed73'";
        Cursor cursor = sContext.getContentResolver().query(CloudStore.SyncLabel.getContentUri(),
                null, where, null, null);
        //3.verify query result
        if (cursor == null) {
            Assert.fail("query local media fail!");
        }
        int exceptedNum = 100;
        Assert.assertEquals(exceptedNum, cursor.getCount());
        //Assert.assertTrue("Test true!", 100 == cursor.getCount());
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01546")
    public void should_return_success_when_delete_sync_label_with_selection() {
        //1.import 100 data
        insertSyncLabelData(DataBaseUtils.MIDDLE_SCALE_DATA);
        //2.query data
        String where = CloudStore.SyncLabel.MD5 + " like '%b966f0ed73'";
        int deleteCount = sContext.getContentResolver().delete(CloudStore.SyncLabel.getContentUri(),
                where, null);
        //3.verify query result
        int exceptedNum = 100;
        Assert.assertEquals(exceptedNum, deleteCount);
        //Assert.assertTrue("Test true!", 100 == deleteCount);
    }


    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01547")
    public void should_return_success_when_delete_sync_label_with_selection_part() {
        //1.import 100 data
        insertSyncLabelData(DataBaseUtils.MAX_SCALE_DATA);
        //2.query data
        String where = CloudStore.SyncLabel._ID + " like '%0'";
        int deleteCount = sContext.getContentResolver().delete(CloudStore.SyncLabel.getContentUri(),
                where, null);
        //3.verify query result
        int exceptedNum = 100;
        Assert.assertEquals(exceptedNum, deleteCount);
        //Assert.assertTrue("Test true!", 100 == deleteCount);
    }

    private void insertSyncLabelData(int length) {
        ContentValues[] valuesArray = new ContentValues[length];
        for (int i = 0; i < length; i++) {
            ContentValues values = new ContentValues();
            values.put(CloudStore.SyncLabel.MD5, MD5);
            valuesArray[i] = values;
        }
        int i = sContext.getContentResolver().bulkInsert(CloudStore.SyncLabel.getContentUri(), valuesArray);

    }

    /**
     * elete sync_label table data
     */
    public void deleteSyncLabelData() {
        int deleteResult = sContext.getContentResolver().delete(CloudStore.SyncLabel.getContentUri(),
                null, null);
    }

    /**
     * delete table data
     */
    public static void deleteDBTestData() {
        int deleteResult1 = sContext.getContentResolver().delete(CloudStore.SyncLabel.getContentUri(),
                null, null);
    }

}
