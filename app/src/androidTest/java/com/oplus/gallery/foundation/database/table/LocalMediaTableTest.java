/************************************************************
 * Copyright 2010-2019 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : LocalMediaTableTest.java
 * Version Number : 1.0
 * Description    :android test
 * Author         : 80082786
 * Date           : 2020-03-19
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2020-03-19, 80082786, create
 ************************************************************/
package com.oplus.gallery.foundation.database.table;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;

import com.oplus.autotest.olt.testlib.annotations.CaseId;
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization;
import com.oplus.autotest.olt.testlib.annotations.TestType;
import com.oplus.autotest.olt.testlib.common.BasicData;
import com.oplus.gallery.framework.abilities.mediadbsync.sync.MediaSyncManager;
import com.oplus.gallery.standard_lib.database.databaseAw.DataBaseUtils;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.utils.TestDBHelpUtils;
import com.oplus.gallery.utils.TestLog;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import static com.oplus.gallery.utils.TestConstants.NOEXTERNAL;


public class LocalMediaTableTest {
    private static final String TAG = LocalMediaTableTest.class.getSimpleName();
    private static final String PATH = "/storage/emulated/0/DCIM/Camera/";
    private static final String NAME = "123";
    private static final String SUFFIX = ".jpg";
    private static final String DATA = PATH + NAME + SUFFIX;
    private static final Long DATE_TAKEN = 1585627094000l;//2020-03-31 11:58:14
    private static final int MEDIA_ID = 110322;
    private static Context sContext = BasicData.getTargetContext();

    @BeforeClass
    public static void beforeClass() {
        //Pause media library synchronization
        MediaSyncManager.getInstance().pause(true);
    }

    @Before
    public void setUp() {
        //clear local media data
        deleteLoclMediaData();
    }

    @After
    public void tearDown() {
    }

    @AfterClass
    public static void afterClass() {
        deleteDBTestData();
        sContext = null;
        //Resume sync function
        MediaSyncManager.getInstance().resume();
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01512")
    public void should_return_success_when_insert_local_media_with_uri_values() {
        ContentValues values = new ContentValues();
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, DATA);
        values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, MEDIA_ID);
        Uri uri = sContext.getContentResolver().insert(GalleryStore.GalleryMedia.getContentUri(), values);
        TestLog.d(TAG, "put data in local media with value: " + values);
        TestLog.d(TAG, "put data in local media with uri: " + uri);
        Assert.assertNotNull(uri);
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01513")
    public void should_return_fail_when_insert_local_media_with_same_uri() {
        ContentValues values = new ContentValues();
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, DATA);
        values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, MEDIA_ID);
        Uri uri1 = sContext.getContentResolver().insert(GalleryStore.GalleryMedia.getContentUri(), values);
        Assert.assertNotNull(uri1);
        //insert the same data
        Uri uri2 = sContext.getContentResolver().insert(GalleryStore.GalleryMedia.getContentUri(), values);
        Assert.assertNull(uri2);
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01514")
    public void should_return_success_when_insert_local_media_data_with_1w_values() {
        for (int i = 0; i < DataBaseUtils.MIDDLE_SCALE_DATA; i++) {
            ContentValues values = new ContentValues();
            values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, PATH + NAME + i + SUFFIX);
            values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, MEDIA_ID);
            Uri uri1 = sContext.getContentResolver().insert(GalleryStore.GalleryMedia.getContentUri(), values);
            Assert.assertNotNull(uri1);
        }
    }


    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01515")
    public void should_return_success_when_bulk_insert_with_10w_values() {
        ContentValues[] valuesArray = new ContentValues[DataBaseUtils.MAX_SCALE_DATA];
        for (int i = 0; i < DataBaseUtils.MAX_SCALE_DATA; i++) {
            ContentValues values = new ContentValues();
            values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, PATH + NAME + i + SUFFIX);
            values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, MEDIA_ID);
            valuesArray[i] = values;
        }

        int bulkInsertResult = sContext.getContentResolver().bulkInsert(GalleryStore.GalleryMedia.getContentUri(), valuesArray);
        Assert.assertEquals(DataBaseUtils.MAX_SCALE_DATA, bulkInsertResult);
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01518")
    public void should_return_success_when_update_local_media_with_selection() {
        //1.import 100 data
        insertLocalMediaData(DataBaseUtils.MIDDLE_SCALE_DATA);
        //2.update data
        String where = GalleryStore.GalleryColumns.LocalColumns.DATA + " like '%1231.jpg'";
        ContentValues values = new ContentValues();
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN, DATE_TAKEN);
        int updateCount = sContext.getContentResolver().update(GalleryStore.GalleryMedia.getContentUri(), values, where, null);
        //3.verify update result
        int exceptedNum = 1;
        Assert.assertEquals(exceptedNum, updateCount);
        //Assert.assertTrue("Test true!", 1 == updateCount);
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01516")
    public void should_return_success_when_insert_local_media_with_data_taken() {
        //1.insert data
        ContentValues values = new ContentValues();
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, DATA);
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN, DATE_TAKEN);
        values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, MEDIA_ID);
        Uri before = sContext.getContentResolver().insert(GalleryStore.GalleryMedia.getContentUri(), values);

        //2.query data
        String where = GalleryStore.GalleryColumns.LocalColumns.DATA + TestDBHelpUtils.WHERE_123JPG;
        Cursor cursor = sContext.getContentResolver().query(GalleryStore.GalleryMedia.getContentUri(),
                null, where, null, null);
        if (cursor == null) {
            Assert.fail("cursor should not be null");
            return;
        }
        //3.verify query data
        if (cursor != null && cursor.moveToFirst()) {
            String year = cursor.getString(cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.YEAR));
            String month = cursor.getString(cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.MONTH));
            String day = cursor.getString(cursor.getColumnIndex(GalleryStore.GalleryColumns.LocalColumns.DAY));
            Assert.assertEquals("2020", year);
            Assert.assertEquals("202003", month);
            Assert.assertEquals("20200331", day);
        }

    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01517")
    public void should_return_success_when_query_local_media_with_selection() {
        //1.import 100 data
        insertLocalMediaData(DataBaseUtils.MIDDLE_SCALE_DATA);
        //2.query data
        String where = GalleryStore.GalleryColumns.LocalColumns.DATA + " like '%1231.jpg'";
        try (Cursor cursor = sContext.getContentResolver().query(GalleryStore.GalleryMedia.getContentUri(),
                null, where, null, null)) {
            if (cursor == null) {
                Assert.fail("query local media fail!");
            }
            int exceptedNum = 1;
            Assert.assertEquals(exceptedNum, cursor.getCount());
        } catch (Exception e) {
            Assert.fail(TAG + "should_return_success_when_query_local_media_with_selection :" + e.getMessage() + e.getStackTrace());
        }
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01519")
    public void should_return_success_when_delete_local_media_with_selection() {
        //1.import 100 data
        insertLocalMediaData(DataBaseUtils.MIDDLE_SCALE_DATA);
        //2.query data
        String where = GalleryStore.GalleryColumns.LocalColumns.DATA + " like '%1231.jpg'";
        int deleteCount = sContext.getContentResolver().delete(GalleryStore.GalleryMedia.getContentUri(),
                where, null);
        //3.verify query result
        int exceptedNum = 1;
        Assert.assertEquals(exceptedNum, deleteCount);
        //Assert.assertTrue("Test true!", 1 == deleteCount);
    }

    private void insertLocalMediaData(int length) {
        ContentValues[] valuesArray = new ContentValues[length];
        for (int i = 0; i < length; i++) {
            ContentValues values = new ContentValues();
            values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, PATH + NAME + i + SUFFIX);
            values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, MEDIA_ID);
            valuesArray[i] = values;
        }
        int count = sContext.getContentResolver().bulkInsert(GalleryStore.GalleryMedia.getContentUri(), valuesArray);
        Assert.assertEquals(count, length);
    }

    /**
     * elete local_media table data
     */
    public void deleteLoclMediaData() {
        int deleteResult = sContext.getContentResolver().delete(GalleryStore.GalleryMedia.getContentUri(),
                null, null);
    }

    /**
     * delete table data
     */
    public static void deleteDBTestData() {
        int deleteResult2 = sContext.getContentResolver().delete(GalleryStore.GalleryMedia.getContentUri(),
                null, null);
    }

}
