/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DiskLruCacheUriTest.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/5/5
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** Dajian@Apps.Gallery3D      2022/5/5    1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/

package com.oplus.gallery.foundation.cache.diskcache

import android.net.Uri
import com.oplus.autotest.olt.testlib.annotations.CaseId
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.annotations.TestType
import com.oplus.gallery.utils.TestConstants
import org.junit.Assert
import org.junit.Test

class DiskLruCacheUriTest {

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入构造uri的正确入参
     * [When] 构建对象
     * [Then] getUri接口返回正确的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01575")
    fun should_return_correct_when_getUri_by_correct_params() {
        DiskLruCacheUri(
            scheme = DISK_LRU_CACHE_SCHEME,
            authority = GALLERY_AUTHORITY,
            path = CACHE_PATH,
            appVersion = 1,
            valueCount = 1,
            maxSize = 1024,
            cacheKey = "IMG_20220505",
        ).apply {
            Assert.assertFalse(isInvalidUri())
            Assert.assertEquals(EXPECTED_URI.toString(), getUri().toString())
        }
    }

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入错误的入参：scheme
     * [When] 构建对象
     * [Then] getUri接口返回空的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01576")
    fun should_return_false_when_isInvalidUri_by_invalid_scheme() {
        DiskLruCacheUri(
            scheme = CONTENT_SCHEME,
            authority = GALLERY_AUTHORITY,
            path = CACHE_PATH,
            appVersion = 1,
            valueCount = 1,
            maxSize = 1024,
            cacheKey = "IMG_20220505",
        ).apply {
            Assert.assertTrue(isInvalidUri())
            Assert.assertEquals(Uri.EMPTY, getUri())
        }
    }

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入错误的入参：scheme
     * [When] 构建对象
     * [Then] getUri接口返回空的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01577")
    fun should_return_false_when_isInvalidUri_by_invalid_authority() {
        DiskLruCacheUri(
            scheme = DISK_LRU_CACHE_SCHEME,
            authority = "",
            path = CACHE_PATH,
            appVersion = 1,
            valueCount = 1,
            maxSize = 1024,
            cacheKey = "IMG_20220505",
        ).apply {
            Assert.assertTrue(isInvalidUri())
            Assert.assertEquals(Uri.EMPTY, getUri())
        }
    }

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入错误的入参：authority
     * [When] 构建对象
     * [Then] getUri接口返回空的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01578")
    fun should_return_false_when_isInvalidUri_by_invalid_path() {
        DiskLruCacheUri(
            scheme = DISK_LRU_CACHE_SCHEME,
            authority = GALLERY_AUTHORITY,
            path = "",
            appVersion = 1,
            valueCount = 1,
            maxSize = 1024,
            cacheKey = "IMG_20220505",
        ).apply {
            Assert.assertTrue(isInvalidUri())
            Assert.assertEquals(Uri.EMPTY, getUri())
        }
    }

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入错误的入参：appVersion
     * [When] 构建对象
     * [Then] getUri接口返回空的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01579")
    fun should_return_false_when_isInvalidUri_by_invalid_appVersion() {
        DiskLruCacheUri(
            scheme = DISK_LRU_CACHE_SCHEME,
            authority = GALLERY_AUTHORITY,
            path = CACHE_PATH,
            appVersion = 0,
            valueCount = 1,
            maxSize = 1024,
            cacheKey = "IMG_20220505",
        ).apply {
            Assert.assertTrue(isInvalidUri())
            Assert.assertEquals(Uri.EMPTY, getUri())
        }
    }

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入错误的入参：valueCount
     * [When] 构建对象
     * [Then] getUri接口返回空的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01580")
    fun should_return_false_when_isInvalidUri_by_invalid_valueCount() {
        DiskLruCacheUri(
            scheme = DISK_LRU_CACHE_SCHEME,
            authority = GALLERY_AUTHORITY,
            path = CACHE_PATH,
            appVersion = 1,
            valueCount = 0,
            maxSize = 1024,
            cacheKey = "IMG_20220505",
        ).apply {
            Assert.assertTrue(isInvalidUri())
            Assert.assertEquals(Uri.EMPTY, getUri())
        }
    }

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入错误的入参：maxSize
     * [When] 构建对象
     * [Then] getUri接口返回空的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01581")
    fun should_return_false_when_isInvalidUri_by_invalid_maxSize() {
        DiskLruCacheUri(
            scheme = DISK_LRU_CACHE_SCHEME,
            authority = GALLERY_AUTHORITY,
            path = CACHE_PATH,
            appVersion = 1,
            valueCount = 1,
            maxSize = 0,
            cacheKey = "IMG_20220505",
        ).apply {
            Assert.assertTrue(isInvalidUri())
            Assert.assertEquals(Uri.EMPTY, getUri())
        }
    }

    /**
     * 测试SDK是否能够构建出正确的uri
     * ```
     * [Given] 输入错误的入参：cacheKey
     * [When] 构建对象
     * [Then] getUri接口返回空的uri
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01582")
    fun should_return_false_when_isInvalidUri_by_invalid_cacheKey() {
        DiskLruCacheUri(
            scheme = DISK_LRU_CACHE_SCHEME,
            authority = GALLERY_AUTHORITY,
            path = CACHE_PATH,
            appVersion = 1,
            valueCount = 1,
            maxSize = 1024,
            cacheKey = "",
        ).apply {
            Assert.assertTrue(isInvalidUri())
            Assert.assertEquals(Uri.EMPTY, getUri())
        }
    }

    /**
     * 测试SDK是否能够构正确的解析uri
     * ```
     * [Given] 输入正确的入参：符合格式的uri
     * [When] 构建对象
     * [Then] 解析后，各个接口返回的结果正确
     * ```
     */
    @Test
    @TestType(TestConstants.NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01583")
    fun should_return_correct_when_create_by_param_uri() {
        DiskLruCacheUri(EXPECTED_URI).apply {
            Assert.assertFalse(isInvalidUri())
            Assert.assertEquals(DISK_LRU_CACHE_SCHEME, scheme)
            Assert.assertEquals(GALLERY_AUTHORITY, authority)
            Assert.assertEquals(CACHE_PATH, path)
            Assert.assertEquals(1, appVersion)
            Assert.assertEquals(1, valueCount)
            Assert.assertEquals(1024, maxSize)
            Assert.assertEquals("IMG_20220505", cacheKey)
        }
    }

    companion object {
        private const val DISK_LRU_CACHE_SCHEME = "disklrucache"
        private const val CONTENT_SCHEME = "content"
        private const val GALLERY_AUTHORITY = "com.oplus.gallery"
        private const val CACHE_PATH = "/Dcim/Camera/.Cache"

        // query param key
        private const val PARAM_APP_VERSION = "app_version"
        private const val PARAM_VALUE_COUNT = "value_count"
        private const val PARAM_MAX_SIZE = "max_size"
        private const val PARAM_CACHE_KEY = "cache_key"
        private val EXPECTED_URI = Uri.Builder()
            .scheme(DISK_LRU_CACHE_SCHEME)
            .authority(GALLERY_AUTHORITY)
            .path(CACHE_PATH)
            .appendQueryParameter(PARAM_APP_VERSION, "1")
            .appendQueryParameter(PARAM_VALUE_COUNT, "1")
            .appendQueryParameter(PARAM_MAX_SIZE, "1024")
            .appendQueryParameter(PARAM_CACHE_KEY, "IMG_20220505")
            .build()
    }
}