/************************************************************
 * Copyright 2010-2019 OPLUS Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ScanLabelTableTest.java
 * Version Number : 1.0
 * Description    : android test
 * Author         : wujiafeng
 * Date           : 2020-03-26
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2020-03-26, wuji<PERSON>eng, create
 ************************************************************/
package com.oplus.gallery.foundation.database.table;

import static com.oplus.gallery.utils.TestConstants.NOEXTERNAL;

import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;

import com.oplus.autotest.olt.testlib.annotations.CaseId;
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization;
import com.oplus.autotest.olt.testlib.annotations.TestType;
import com.oplus.autotest.olt.testlib.common.BasicData;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.framework.abilities.mediadbsync.sync.MediaSyncManager;
import com.oplus.gallery.standard_lib.database.databaseAw.DataBaseUtils;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

public class ScanLabelTableTest {
    private static final String TAG = ScanLabelTableTest.class.getSimpleName();
    private static final String PATH = "/storage/emulated/0/DCIM/Camera/";
    private static final String NAME = "123";
    private static final String SUFFIX = ".jpg";
    private static final String DATA = PATH + NAME + SUFFIX;
    private static final String INVALID_0 = "0";
    private static final String INVALID_1 = "1";
    private static final String IS_RECYCLED_0 = "0";
    private static final String IS_RECYCLED_1 = "1";
    private static final String SIZE = "12";
    private static final int MEDIA_ID = 110322;
    private static Context sContext = BasicData.getTargetContext();

    @BeforeClass
    public static void beforeClass() {
        //Pause media library synchronization
        MediaSyncManager.getInstance().pause(true);
    }

    @Before
    public void setUp() {
        // 1.prepare action :  delete  the LocalMedia/RecycleMedia/ScanLabel  data
        DataBaseUtils.deleteDbData(sContext, GalleryStore.GalleryMedia.getContentUri(), null);
        DataBaseUtils.deleteDbData(sContext, GalleryStore.ScanLabel.getContentUri(), null);
    }

    @After
    public void tearDown() {

    }

    @AfterClass
    public static void afterClass() {
        deleteDBTestData();
        sContext = null;
        //Resume sync function
        MediaSyncManager.getInstance().resume();
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01469")
    public void should_return_success_when_insert_data_to_scan_label_with_uri_values() {
        //absolutePath
        ContentValues values = new ContentValues();
        values.put(GalleryStore.ScanLabel.DATA, DATA);
        //1.insert data
        Uri uri = sContext.getContentResolver().insert(GalleryStore.ScanLabel.getContentUri(), values);
        Assert.assertNotNull(uri);
    }

    @Test
    @TestType(NOEXTERNAL)
    @Ignore
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01470")
    public void should_return_fail_when_insert_data_to_scan_label_with_same_uri() {
        ContentValues values = new ContentValues();
        values.put(GalleryStore.ScanLabel.DATA, DATA);
        //1.insert data
        Uri uri1 = sContext.getContentResolver().insert(GalleryStore.ScanLabel.getContentUri(), values);
        Assert.assertNotNull(uri1);
        //2.insert the same data
        Uri uri2 = sContext.getContentResolver().insert(GalleryStore.ScanLabel.getContentUri(), values);
        Assert.assertNull(uri2);
    }


    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01471")
    public void should_scan_label_set_change_when_local_media_with_insert_or_delete() {
        // 1.prepare action :  delete  the local_media and scan_label  data
        DataBaseUtils.deleteDbData(sContext, GalleryStore.GalleryMedia.getContentUri(), null);
        DataBaseUtils.deleteDbData(sContext, GalleryStore.ScanLabel.getContentUri(), null);

        // 2.insert data to scan_label and local_media
        ContentValues values = new ContentValues();
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, DATA);
        DataBaseUtils.insertDbData(sContext, GalleryStore.ScanLabel.getContentUri(), GalleryStore.ScanLabel.DATA,
                DATA, GalleryStore.ScanLabel.INVALID, INVALID_0);
        DataBaseUtils.insertDbDataWithInt(sContext, GalleryStore.GalleryMedia.getContentUri(), GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID,
                MEDIA_ID, GalleryStore.GalleryColumns.LocalColumns.INVALID, INVALID_0);

        // 3.update "invalid" from local_media to 1 ,then query the "invalid" from local_media is 1
        DataBaseUtils.updataDbData(sContext, GalleryStore.GalleryMedia.getContentUri(), null, GalleryStore.GalleryColumns.LocalColumns.DATA,
                DATA, GalleryStore.GalleryColumns.LocalColumns.INVALID, INVALID_1);

        // 4.query the "invalid" from scan_label is 1
        DataBaseUtils.queryDbData(sContext, GalleryStore.ScanLabel.getContentUri(), null,
                GalleryStore.ScanLabel.INVALID, INVALID_1);
    }


    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01472")
    public void should_scan_label_set_change_when_recycle_media_with_insert_or_delete() {
        // 2.insert data to scan_label
        DataBaseUtils.insertDbData(sContext, GalleryStore.ScanLabel.getContentUri(),
                GalleryStore.ScanLabel.DATA, DATA, GalleryStore.ScanLabel.IS_RECYCLED, IS_RECYCLED_0);

        // 3.insert "_data" from recycle_media that same to the "_data" form scan_label
        DataBaseUtils.insertDbData(sContext, GalleryStore.GalleryMedia.getContentUri(),
                GalleryStore.GalleryColumns.LocalColumns.DATA, DATA, GalleryStore.GalleryColumns.LocalColumns.SIZE, SIZE);

        // 4.query the scan_label "is_recycle" data is 1
        String where = GalleryStore.ScanLabel.IS_RECYCLED + "=1";
        DataBaseUtils.queryDbData(sContext, GalleryStore.ScanLabel.getContentUri(), where,
                GalleryStore.ScanLabel.IS_RECYCLED, IS_RECYCLED_1);
    }

    /**
     * delete table data
     */
    public static void deleteDBTestData() {
        int deleteResult1 = sContext.getContentResolver().delete(GalleryStore.ScanLabel.getContentUri(),
                null, null);
        int deleteResult3 = sContext.getContentResolver().delete(GalleryStore.GalleryMedia.getContentUri(),
                null, null);
    }

}
