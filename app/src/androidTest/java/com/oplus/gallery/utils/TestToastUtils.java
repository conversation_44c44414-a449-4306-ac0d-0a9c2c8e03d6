/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description:
 ** Version: 1.0
 ** Date: 2021-08-16
 ** Author: W9002848 Pengcheng Lin
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** W9002848 Pengcheng Lin    2021-08-16    1.0
 ********************************************************************************/
package com.oplus.gallery.utils;

import android.content.res.Resources;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.oplus.gallery.standard_lib.ui.util.ToastUtil;

public class TestToastUtils {
    private TestToastUtils() { }
    
    /**
     * get toast content text
     * @param toast
     * @return toast msg string
     */
    public static String getToastContent(Toast toast) {
        Resources resources = Resources.getSystem();
        int id = resources.getIdentifier("message", "id", "android");
        View view = toast.getView();
        if (view == null) {
            return "";
        }
        TextView tv = view.findViewById(id);
        if ((tv == null) || (tv.getText() == null)) {
            return "";
        }
        return tv.getText().toString();
    }
    
    /**
     * check toast content when input image path
     * @param fieldName
     * @return toast
     */
    public static synchronized Toast reflectToast(String fieldName) {
        Class aClass = null;
        try {
            aClass = Class.forName(ToastUtil.class.getName());
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return (Toast) TestReflectUtils.getFieldByReflect(aClass, fieldName, TestReflectUtils.TargetType.OBJECT);
    }
}
