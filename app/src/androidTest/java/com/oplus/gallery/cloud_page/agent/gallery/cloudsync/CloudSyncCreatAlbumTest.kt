/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: CloudSyncCreatAlbumTest.kt
 ** Description: cloud sync test
 ** Version: 1.0
 ** Date: 2020-09-28
 ** Author: W9002848 Pengcheng Lin
 ** TAG: Otest#CloudSyncCreatAlbumTest
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                  <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** W9002848 Pengcheng Lin    2020/09/28    1.0
 ********************************************************************************/
package com.oplus.gallery.cloud_page.agent.gallery.cloudsync

import androidx.test.rule.ActivityTestRule
import com.coloros.gallery3d.app.MainActivity
import com.oplus.autotest.olt.testlib.annotations.CaseId
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.annotations.TestType
import com.oplus.autotest.olt.testlib.common.BasicData
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.EXPECT_COPY_COUNT_10
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.LOCAL_INVALID_NORMAL
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.PATH_CLOUD_ALLOW_DIR
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.SELF_ALBUM_PATH
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.checkDeleteWhenSyncDirectoryOpen
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.checkLocalDataAfterRestore
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.checkSyncAlbumDirStatus
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.checkSyncStateWhenSyncDirectoryOpen
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.copyTenFile
import com.oplus.gallery.cloud_page.agent.gallery.cloudsync.CloudSyncTestUtils.notRecycleAndSyncDirectoryOpen
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.framework.abilities.mediadbsync.sync.MediaSyncManagerTestUtils
import com.oplus.gallery.utils.TestConstants.EXTERNAL
import org.junit.After
import org.junit.AfterClass
import org.junit.Assert
import org.junit.Before
import org.junit.BeforeClass
import org.junit.Ignore
import org.junit.Rule
import org.junit.Test

class CloudSyncCreatAlbumTest {

    @Rule
    @JvmField
    val activity = ActivityTestRule(MainActivity::class.java)

    companion object {
        private val targetContext = BasicData.getTargetContext()

        @JvmStatic
        @BeforeClass
        fun classSetUp() {
            //Set Network enable
            /*setNetworkConnect(true)
            setScreenKeepAndUnlocks()
            setIsPrivacyPolicyAlert(targetContext, false)
            clearMediaStoreImages(targetContext)
            TestDBHelpUtils.deleteRecycledByFilePath()
            TestDBHelpUtils.testResultForDeleteRecycled(0)*/
        }

        @JvmStatic
        @AfterClass
        fun classTearDown() {
            /*setNetworkConnect(false)*/
        }
    }

    @Before
    fun setUp() {
        /*//Apply permission
        applyPermissions(targetContext)
        //clear data(gallery.db  media_store)
        clearDataAndOpenSync(targetContext)*/
    }

    @After
    fun tearDown() {
        //TearDown: End delete the files in the folder PATH_CAMERA
        /*clearTestResource(targetContext)*/
    }

    /**
     * 2.
     * 验证创建图集文件夹，且开关打开，图集的图片和视频都会同步至云端（7.3）
     * 前提条件： 相册云同步开关开启，连接wifi
     * 【given】拷贝图片是相册目录，触发媒体库扫描，验证local_media同步成功并获取mediaId
     * 【when】调用全量同步接口，触发云同步，验证cloud_allow_list存在记录，并同步开关为打开状态
     * 【then】云同步成功，并回收测试资源
     */
    @Test
    @Ignore
    @TestType(EXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01672")
    fun should_cloud_sync_success_when_create_file_album_with_switchState_open() {
        //1、创建图集文件夹/storage/emulated/0/DCIM/MyAlbums/Vikings!，并拷贝图片资源，触发系统媒体库同步
        val copyCount: Int = EXPECT_COPY_COUNT_10
        copyTenFile(targetContext, SELF_ALBUM_PATH)

        //2、验证local_media同步成功（存在数据记录且invalid=0），并获取图片mediaId集合
        val path = SELF_ALBUM_PATH + MediaSyncManagerTestUtils.ONE_NEW_SYNC
        val whereLocalMedia = GalleryStore.GalleryColumns.LocalColumns.DATA + " like '" + path + "%' and invalid=?"
        val mediaIdMap = MediaSyncManagerTestUtils.queryLocalStoreMediaId(targetContext,
            whereLocalMedia, arrayOf(LOCAL_INVALID_NORMAL))
        val isEmpty = (mediaIdMap == null) || (mediaIdMap.isEmpty())
        Assert.assertFalse("The mediaIds should be not null or empty!", isEmpty)

        //3.设置同步目录开关，并校验状态
        checkSyncAlbumDirStatus(targetContext, PATH_CLOUD_ALLOW_DIR, true)

        //4.开关打开后，校验云同步情况，并删除数据
        checkSyncStateWhenSyncDirectoryOpen(targetContext, copyCount, mediaIdMap, SELF_ALBUM_PATH)

        //5.恢复图片，校验本地同步情况
        checkLocalDataAfterRestore(targetContext, copyCount, mediaIdMap, SELF_ALBUM_PATH)

        //6.恢复图片，校验云端同步情况
        notRecycleAndSyncDirectoryOpen(targetContext, copyCount, SELF_ALBUM_PATH)

        //7.删除到回收站--彻底删除，校验云端同步情况
        checkDeleteWhenSyncDirectoryOpen(targetContext, copyCount, SELF_ALBUM_PATH)
    }
}