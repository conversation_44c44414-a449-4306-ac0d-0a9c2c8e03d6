/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MediaEntry
 ** Description:
 ** Version: 1.0
 ** Date: 2020-04-15
 ** Author: zhongxuechang@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_MEDIA_SYNC
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhongxuechang@Apps.Gallery3D    2020/04/15     1.0        OPLUS_FEATURE_MEDIA_SYNC
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item.utils;

import android.database.Cursor;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.gallery.framework.abilities.mediadbsync.parse.ValueCheckUtils;
import com.oplus.gallery.standard_lib.file.File;

class MediaEntry {

    static final String VOLUME_EXTERNAL = MediaStore.VOLUME_EXTERNAL;
    static final String VOLUME_EXTERNAL_PRIMARY = MediaStore.VOLUME_EXTERNAL_PRIMARY;

    static final String PATH_IMAGE = File.separator + "images" + File.separator + "media";
    static final String PATH_VIDEO = File.separator + "video" + File.separator + "media";

    //meida
    static final String[] MEDIA_PROJECTIONS = new String[] {
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DATA,
            MediaStore.Files.FileColumns.DATE_TAKEN,
            MediaStore.Files.FileColumns.WIDTH,
            MediaStore.Files.FileColumns.HEIGHT,
            MediaStore.Files.FileColumns.MEDIA_TYPE,
            MediaStore.Files.FileColumns.DISPLAY_NAME,
            MediaStore.Files.FileColumns.RELATIVE_PATH,
            MediaStore.Files.FileColumns.VOLUME_NAME,
            MediaStore.Files.FileColumns.DATE_MODIFIED,
            MediaStore.Files.FileColumns.MIME_TYPE,
            MediaStore.Files.FileColumns.DATE_ADDED,
            MediaStore.Files.FileColumns.IS_PENDING,
            MediaStore.Files.FileColumns.SIZE,
            MediaStore.Files.FileColumns.ORIENTATION,
            MediaStore.Files.FileColumns.BUCKET_ID,
            MediaStore.Files.FileColumns.BUCKET_DISPLAY_NAME,
            MediaStore.Files.FileColumns.DURATION,
            MediaStore.Files.FileColumns.TITLE,
    };
    static final int MEDIA_INDEX_ID = 0;
    static final int MEDIA_INDEX_DATA                   = MEDIA_INDEX_ID + 1;
    static final int MEDIA_INDEX_DATE_TAKEN             = MEDIA_INDEX_DATA + 1;
    static final int MEDIA_INDEX_WIDTH                  = MEDIA_INDEX_DATE_TAKEN + 1;
    static final int MEDIA_INDEX_HEIGHT                 = MEDIA_INDEX_WIDTH + 1;
    static final int MEDIA_INDEX_MEDIA_TYPE             = MEDIA_INDEX_HEIGHT + 1;
    static final int MEDIA_INDEX_DISPLAY_NAME           = MEDIA_INDEX_MEDIA_TYPE + 1;
    static final int MEDIA_INDEX_RELATIVE_PATH          = MEDIA_INDEX_DISPLAY_NAME + 1;
    static final int MEDIA_INDEX_VOLUME_NAME            = MEDIA_INDEX_RELATIVE_PATH + 1;
    static final int MEDIA_INDEX_DATE_MODIFIED          = MEDIA_INDEX_VOLUME_NAME + 1;
    static final int MEDIA_INDEX_MIME_TYPE              = MEDIA_INDEX_DATE_MODIFIED + 1;
    static final int MEDIA_INDEX_DATE_ADDED             = MEDIA_INDEX_MIME_TYPE + 1;
    static final int MEDIA_INDEX_IS_PENDING             = MEDIA_INDEX_DATE_ADDED + 1;
    static final int MEDIA_INDEX_SIZE                   = MEDIA_INDEX_IS_PENDING + 1;
    static final int MEDIA_INDEX_ORIENTATION            = MEDIA_INDEX_SIZE + 1;
    static final int MEDIA_INDEX_BUCKET_ID              = MEDIA_INDEX_ORIENTATION + 1;
    static final int MEDIA_INDEX_BUCKET_DISPLAY_NAME    = MEDIA_INDEX_BUCKET_ID + 1;
    static final int MEDIA_INDEX_DURATION               = MEDIA_INDEX_BUCKET_DISPLAY_NAME + 1;
    static final int MEDIA_INDEX_TITLE                  = MEDIA_INDEX_DURATION + 1;

    int mId;
    int mMediaId;
    String mData;
    long mDateTaken;
    int mWidth;
    int mHeight;
    String mRelativePath;
    String mVolumeName;
    int mMediaType;
    String mMimeType;
    String mDisplayName;
    long mDateModified;
    long mDateAdded;
    int mIsPending;
    long mSize;
    int mOrientation;
    String mBucketId;
    String mBucketDisplayName;
    int mDuration;
    String mTitle;

    /**
     * @param cursor must from MediaStore
     * @param onlyMediaId only get media_id from cursor
     * @return MediaEntry
     */
    static MediaEntry newEntry(Cursor cursor, boolean onlyMediaId) {
        MediaEntry entry = new MediaEntry();
        entry.mMediaId = cursor.getInt(MEDIA_INDEX_ID);
        if (!onlyMediaId) {
            entry.fillFromOnlyMediaIdToAll(cursor);
        }
        return entry;
    }

    void fillFromOnlyMediaIdToAll(Cursor cursor) {
        fillFromOnlyMediaIdToCompare(cursor);
        fillFromCompareToAll(cursor);
    }

    void fillFromOnlyMediaIdToCompare(Cursor mediaCursor) {
        mData = mediaCursor.getString(MEDIA_INDEX_DATA);
        mDateModified = mediaCursor.getLong(MEDIA_INDEX_DATE_MODIFIED);
        mDateAdded = mediaCursor.getLong(MEDIA_INDEX_DATE_ADDED);
        mSize = mediaCursor.getLong(MEDIA_INDEX_SIZE);
        mWidth = mediaCursor.getInt(MEDIA_INDEX_WIDTH);
        mHeight = mediaCursor.getInt(MEDIA_INDEX_HEIGHT);
        mOrientation = mediaCursor.getInt(MEDIA_INDEX_ORIENTATION);
    }

    void fillFromCompareToAll(Cursor mediaCursor) {
        mMediaType = mediaCursor.getInt(MEDIA_INDEX_MEDIA_TYPE);
        mRelativePath = mediaCursor.getString(MEDIA_INDEX_RELATIVE_PATH);
        mVolumeName = mediaCursor.getString(MEDIA_INDEX_VOLUME_NAME);
        mDisplayName = mediaCursor.getString(MEDIA_INDEX_DISPLAY_NAME);
        mMimeType = mediaCursor.getString(MEDIA_INDEX_MIME_TYPE);
        mIsPending = mediaCursor.getInt(MEDIA_INDEX_IS_PENDING);
        mBucketId = mediaCursor.getString(MEDIA_INDEX_BUCKET_ID);
        mBucketDisplayName = mediaCursor.getString(MEDIA_INDEX_BUCKET_DISPLAY_NAME);
        mDuration = mediaCursor.getInt(MEDIA_INDEX_DURATION);
        mTitle = mediaCursor.getString(MEDIA_INDEX_TITLE);
        mDateTaken = mediaCursor.getLong(MEDIA_INDEX_DATE_TAKEN);
    }

    boolean checkExifChange(MediaEntry entry) {
        return ((entry.mSize != mSize) && (entry.mSize > 0) && (mSize > 0)
                || ((entry.mDateModified != mDateModified)
                && ValueCheckUtils.validateSecondTime(entry.mDateModified)
                && ValueCheckUtils.validateSecondTime(mDateModified)));
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof MediaEntry) {
            MediaEntry entry = (MediaEntry) o;
            return ((mMediaId == entry.mMediaId)
                    && (mDateModified == entry.mDateModified)
                    && (mDateAdded == entry.mDateAdded)
                    && (mSize == entry.mSize)
                    && (mWidth == entry.mWidth)
                    && (mHeight == entry.mHeight)
                    && (mOrientation == entry.mOrientation)
                    && TextUtils.equals(mData, entry.mData));
        }
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    boolean isMaybeSameFile(MediaEntry entry) {
        if (entry == null) {
            return false;
        }
        return ((mDateModified > 0) && (mDateModified == entry.mDateModified))
                || ((mDateTaken > 0) && (mDateTaken == entry.mDateTaken))
                || ((mSize > 0) && (mSize == entry.mSize));
    }
}
