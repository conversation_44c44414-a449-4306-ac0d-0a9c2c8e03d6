/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OcrHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/01/19
 ** Author: wujiafeng 80264395
 ** TAG: OPLUS_FEATURE_DATA
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery		2020/09/14		1.0		create
 ** wujiafeng 80264395                  2021/01/19      2.0     overwrite
 *********************************************************************************/

package com.oplus.gallery.business_lib.data.helper

import android.content.ContentValues
import android.util.Log
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.DeleteReq
import com.oplus.gallery.foundation.dbaccess.req.InsertReq
import com.oplus.gallery.foundation.util.media.MimeTypeUtils.MIME_TYPE_VIDEO_MP4
import com.oplus.gallery.openabilitypage.GalleryOpenSearchInfoTest
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.utils.TestTimeUtils

object OcrHelper {
    private val TAG = GalleryOpenSearchInfoTest::class.simpleName
    const val CONTENT = "新春快乐！"

    fun addOcrData(count: Int) {
        for (i in 0 until count) {
            val data = insertLocalMedia(i)
            insertLabel(data, 1)
            addOcr(data, 0)
        }
    }

    private fun insertLabel(data: String, mediaType: Int) {
        val cv = ContentValues().apply {
            put(GalleryStore.ScanLabel.DATA, data)
            put(GalleryStore.ScanLabel.MEDIA_TYPE, mediaType)
            put(GalleryStore.ScanLabel.INVALID, 0)
            put(GalleryStore.ScanLabel.IS_RECYCLED, 1)
            put(GalleryStore.ScanLabel.SCENE_ID, 28)
            put(GalleryStore.ScanLabel.SCORE, 1)
            put(GalleryStore.ScanLabel.IS_SYNC, 0)
        }
        InsertReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.SCAN_LABEL)
            .setConvert {
                cv
            }
            .build().exec()
    }


    private fun insertLocalMedia(id: Int): String {
        val values = ContentValues()
        val date = TestTimeUtils.longToString(SearchInfoHelper.time)
        val name = "$date-$id"
        val type = ".mp4"
        val data = LocalMediaHelper.PATH_CAMERA + name + type
        values.put(GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE, MIME_TYPE_VIDEO_MP4)
        values.put(GalleryStore.GalleryColumns.LocalColumns.INVALID, 0)
        values.put(GalleryStore.GalleryColumns.LocalColumns.DURATION, 12000)
        values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, id)
        values.put(GalleryStore.GalleryColumns.LocalColumns.SIZE, id)
        values.put(
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE,
            GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, data)
        values.put(GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME, name + type)
        values.put(GalleryStore.GalleryColumns.LocalColumns.TITLE, name)
        values.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID, id)
        values.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_NAME, Dir.CAMERA_DIR_NAME)
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_ADDED, SearchInfoHelper.time / 1000)
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN, SearchInfoHelper.time)
        values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED, SearchInfoHelper.time / 1000)
        values.put(GalleryStore.GalleryColumns.LocalColumns.LATITUDE, 39.9818992614746)
        values.put(GalleryStore.GalleryColumns.LocalColumns.LONGITUDE, 116.310699462891)
        InsertReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setConvert {
                values
            }
            .build().exec()
        Log.d(TAG, "detailedDescription-addImage:$values")
        return data
    }

    fun delete() {
        DeleteReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.OCR_PAGES)
            .build().exec()
        LocalMediaHelper.delete()
        LabelHelper.delete()
    }

    fun addOcr(data: String, isSync: Int) {
        val cv = ContentValues().apply {
            put(GalleryStore.OcrPages.DATA, data)
            put(GalleryStore.OcrPages.INVALID, 0)
            put(GalleryStore.OcrPages.IS_RECYCLED, 0)
            put(GalleryStore.OcrPages.IS_SYNC, isSync)
            put(GalleryStore.OcrPages.CONTENT, CONTENT)
            put(GalleryStore.OcrPages.MODEL_VERSION, 0)
        }
        InsertReq.Builder()
            .setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.OCR_PAGES)
            .setConvert {
                cv
            }
            .build().exec()
    }
}