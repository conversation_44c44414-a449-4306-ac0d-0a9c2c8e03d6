/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - LabelAlbumSetModelTest.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/09/16
 ** Author: luyao.Tan@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_LABEL
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** luyao.Tan@Apps.Gallery3D		2020/09/16		1.0		OPLUS_FEATURE_LABEL
 *********************************************************************************/

package com.oplus.gallery.business_lib.model.model

import com.oplus.autotest.olt.testlib.annotations.CaseId
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.annotations.TestType
import com.oplus.gallery.business_lib.data.helper.LabelHelper
import com.oplus.gallery.business_lib.model.data.label.LabelDictionary
import com.oplus.gallery.utils.TestConstants.NOEXTERNAL
import java.util.Objects
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class LabelAlbumSetModelTest {

    @Before
    fun setUp() {
        LabelHelper.delete()
        LabelHelper.addLabels()
    }

    @After
    fun tearDown() {
        LabelHelper.delete()
    }

    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("A")
    @CaseId("AT_APP_Gallery_01650")
    fun should_return_correct_data_when_LabelAlbumSetModel_reload() {
        val targetLabelAlbumSetList = LabelHelper.getLabelAlbumSetListFromTargetData()
        val dbLabelAlbumSetList = LabelHelper.getLabelAlbumSetListFromDB()
        Assert.assertEquals(targetLabelAlbumSetList.size, dbLabelAlbumSetList.size)
        targetLabelAlbumSetList.forEach { targetLabelAlbumSetItem ->
            for (dbLabelAlbumSetItem in dbLabelAlbumSetList) {
                if (!Objects.equals(LabelDictionary.getLabelName(targetLabelAlbumSetItem.sceneId), dbLabelAlbumSetItem.name)) continue
                Assert.assertEquals(targetLabelAlbumSetItem.coverId, dbLabelAlbumSetItem.coverItems[0].mediaId)
                Assert.assertEquals(targetLabelAlbumSetItem.count, dbLabelAlbumSetItem.count)
                break
            }
        }
    }
}


