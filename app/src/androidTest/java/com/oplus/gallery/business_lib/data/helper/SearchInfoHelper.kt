/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SearchInfoHelper.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2021/01/14
 ** Author: wujiafeng 80264395
 ** TAG: OPLUS_FEATURE_DATA
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wujiafeng 80264395	2021/01/14		1.0		OPLUS_FEATURE_DATA
 *********************************************************************************/

package com.oplus.gallery.business_lib.data.helper

import android.content.ContentValues
import android.database.Cursor
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.dbaccess.DataAccess
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.utils.TestTimeUtils
import org.junit.Assert

object SearchInfoHelper {
    private val TAG = SearchInfoHelper::class.simpleName
    val time: Long = System.currentTimeMillis()
    val date: String = TestTimeUtils.longToString(time)
    var LATITUDE = 39
    var LONGITUDE = 116

    /**
     * 批量插入性能测试数据
     */
    fun bulkInsertLocalMediaData(count: Int) {
        //1.bulk insert data
        val bulkInsertReq = BulkInsertReq.Builder().setDaoType(IDao.DaoType.GALLERY)
            .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
            .setConvert {
                val cvList: MutableList<ContentValues> = ArrayList()
                for (i in 0 until count) {
                    val values = ContentValues()
                    val date = TestTimeUtils.longToString(time)
                    val name = "$date-$i"
                    val type = ".jpeg"
                    values.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, i)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.SIZE, i)
                    values.put(
                        GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE,
                        GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE_IMAGE)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.DATA, LocalMediaHelper.PATH_CAMERA + name + type)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME, name + type)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.TITLE, name)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID, i)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_NAME, Dir.CAMERA_DIR_NAME)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_ADDED, time / 1000)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_TAKEN, time)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.DATE_MODIFIED, time / 1000)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.LATITUDE, 39.9818992614746)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.LONGITUDE, 116.310699462891)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.MIME_TYPE, MimeTypeUtils.MIME_TYPE_IMAGE_JPEG)
                    values.put(GalleryStore.GalleryColumns.LocalColumns.DURATION, 0)
                    cvList.add(values)
                }
                cvList.toTypedArray()
            }.build()
        val bulkInsert = DataAccess.getAccess().bulkInsert(bulkInsertReq)
        Assert.assertEquals(count, bulkInsert)
    }

    fun deleteData() {
        FaceHelper.delete()
        LabelHelper.delete()
        LocalMediaHelper.delete()
        LocationHelper.delete()
        MemoriesHelper.delete()
        RecycleMediaHelper.delete()
        OcrHelper.delete()
    }

    /**
     * 能力开放-中子-搜索详情实体类
     */
    @Suppress("UseDataClass")
    class SearchInfoEntry(cursor: Cursor) {
        var id = cursor.getInt(cursor.getColumnIndex("_id"))
        var mediaId = cursor.getInt(cursor.getColumnIndex("media_id"))
        var data = cursor.getString(cursor.getColumnIndex("_data"))
        var dateTaken = cursor.getLong(cursor.getColumnIndex("datetaken"))
        var dateModified = cursor.getLong(cursor.getColumnIndex("date_modified"))
        var faceName = cursor.getString(cursor.getColumnIndex("face_name"))
        var address = cursor.getString(cursor.getColumnIndex("address"))
        var tags = cursor.getString(cursor.getColumnIndex("tags"))
        var ocr = cursor.getString(cursor.getColumnIndex("ocr"))
        var bucketName = cursor.getString(cursor.getColumnIndex("bucket_name"))
        var bucketId = cursor.getInt(cursor.getColumnIndex("bucket_id"))
        var displayName = cursor.getString(cursor.getColumnIndex("_display_name"))
        var title = cursor.getString(cursor.getColumnIndex("title"))
        var size = cursor.getLong(cursor.getColumnIndex("_size"))
        var mediaType = cursor.getInt(cursor.getColumnIndex("media_type"))
        var mimeType = cursor.getString(cursor.getColumnIndex("mime_type"))
        var duration = cursor.getInt(cursor.getColumnIndex("duration"))
        var longitude = cursor.getInt(cursor.getColumnIndex("longitude"))
        var latitude = cursor.getInt(cursor.getColumnIndex("latitude"))
        var festivalName = cursor.getString(cursor.getColumnIndex("festival_name"))
        var memoryName = cursor.getString(cursor.getColumnIndex("memory_name"))
        var tagflags = cursor.getInt(cursor.getColumnIndex("tagflags"))
    }
}