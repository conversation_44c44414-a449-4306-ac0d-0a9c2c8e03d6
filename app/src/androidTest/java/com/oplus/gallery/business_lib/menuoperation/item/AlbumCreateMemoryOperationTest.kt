/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumCreateMemoryOperationTest.kt
 ** Description:一键生成回忆菜单操作的接口测试
 **
 **
 ** Version: 1.0
 ** Date:2020/10/20
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/10/20	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import android.os.Bundle
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.annotations.TestType
import com.oplus.gallery.business_lib.data.helper.LocalMediaHelper
import com.oplus.gallery.business_lib.data.helper.MemoriesHelper
import com.oplus.gallery.framework.abilities.data.model.AlbumModel
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local
import com.oplus.gallery.business_lib.model.data.memories.utils.MemoriesScannerHelper
import com.oplus.gallery.utils.TestConstants.NOEXTERNAL
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AlbumCreateMemoryOperationTest {

    companion object {
        const val TAG = "AlbumCreateMemoryOperationTest"
        const val PATH = "/storage/emulated/0/DCIM/Camera/"
        const val CAMERA_ALBUM_NAME = "相机"
    }

    @Before
    fun setUp() {
        deleteData()
        LocalMediaHelper.addCommonImage()
    }

    @After
    fun tearDown() {
        deleteData()
    }

    /**
     *  测试执行“一键生成回忆”菜单操作后，数据库中回忆数据正确
     * 【given】给定某个图集的albumModel
     * 【when】对这个图集执行 “一键生成回忆” 菜单操作
     * 【then】查询 memories_set 表，查看数据是否正确
     */
    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("B")
    fun should_return_correct_data_when_AlbumCreateMemoryOperation_execute_() {
        // given
        val operation = AlbumCreateMemoryOperation(false, HashMap<String, Any?>(), null)
        val bundle = Bundle().apply {
            putString(DataRepository.KEY_PATH_STR, Local.PATH_ALBUM_CAMERA_ANY.toString())
        }
        operation.albumModel = DataRepository.getAlbumModel(DataRepository.LocalAlbumModelGetter.TYPE_CAMERA_ALBUM, bundle) as AlbumModel
        val count = operation.albumModel.getMemoriesItemCount()
        val dateRange = MemoriesScannerHelper.DateRange()
        dateRange.mStart = 123
        dateRange.mEnd = 456

        // when
        operation.createMemories(dateRange)

        // then
        val resultMemoryEntries = MemoriesHelper.getMemoryEntriesByName(CAMERA_ALBUM_NAME)
        Assert.assertEquals(count, resultMemoryEntries.size)
        for (memoryEntry in resultMemoryEntries) {
            Assert.assertEquals(CAMERA_ALBUM_NAME, memoryEntry.setName)
        }
    }

    private fun deleteData() {
        LocalMediaHelper.delete()
        MemoriesHelper.delete()
    }
}