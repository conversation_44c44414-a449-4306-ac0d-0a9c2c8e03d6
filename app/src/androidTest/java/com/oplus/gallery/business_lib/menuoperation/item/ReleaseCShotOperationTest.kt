/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ReleaseCShotOperationTest.kt
 ** Description:
 **
 **
 ** Version: 1.0
 ** Date:2020/10/26
 ** Author:chenzengxin@Apps.Gallery3D
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D   	2020/10/26	   1.0         build this module
 ********************************************************************************/
package com.oplus.gallery.business_lib.menuoperation.item

import android.os.ConditionVariable
import com.oplus.gallery.business_lib.data.helper.CShotHelper
import com.oplus.gallery.business_lib.data.helper.LocalMediaHelper
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.framework.abilities.mediadbsync.sync.MediaSyncManager
import com.oplus.gallery.standard_lib.file.CopyBatchFileRequest
import com.oplus.gallery.standard_lib.file.TestFileUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import com.oplus.gallery.utils.TestConstants.PATH_NO_MEDIA
import com.oplus.gallery.utils.TestLog
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class ReleaseCShotOperationTest {

    companion object {
        const val TAG = "ReleaseCShotOperationTest"
        const val TEST_DIR = "/storage/emulated/0/DCIM/Camera/Cshot/1234567890/"
        const val TEST_DIR_AFTER_RELEASE = "/storage/emulated/0/DCIM/Camera/"

        // 1-img-cshot-1.jpg to 1-img-cshot-10.jpg
        const val TEST_COUNT = 10
        const val ORIGIN_SET_PATH = "test"
        const val CSHOT_ID_NOT_CSHOT = 0L
        const val TIME_OUT = 2000L
    }

    private val conditionVariable = ConditionVariable(true)

    @Before
    fun setUp() {
        LocalMediaHelper.delete()
        TestFileUtils.deleteFile(ContextGetter.context, TEST_DIR, false)
        val copyFileList = listOf(
            CopyBatchFileRequest(
                PATH_NO_MEDIA,
                TEST_DIR,
                TestFileUtils.ResourceNamePrefix.ONE_IMG_CSHOT,
                TEST_COUNT,
                true
            )
        )
        TestFileUtils.copyFile(ContextGetter.context, copyFileList)
        MediaSyncManager.getInstance().executeFullSync()
    }

    @After
    fun tearDown() {
        TestFileUtils.deleteFile(ContextGetter.context, TEST_DIR, true)
        TestFileUtils.deleteFile(ContextGetter.context, TEST_DIR_AFTER_RELEASE, true)
        LocalMediaHelper.delete()
    }

    /**
     *  测试执行“解除連拍”菜单操作后，数据库中回忆数据正确
     * 【given】给定一個連拍的 cshotId
     * 【when】对这些图集执行 “解除連拍” 菜单操作
     * 【then】查看数据是否正确
     */
    @Test
    @Ignore("fixme chenzhenxing 偶现失败, 等待修复")
    fun should_return_correct_data_when_ReleaseCShotOperation_execute() {
        // given
        val cshotEntries = CShotHelper.getAllCShotEntry()
        cshotEntries.forEach { cshotEntry ->
            TestLog.d(TAG, "should_return_correct_data_when_ReleaseCShotOperation_execute, " +
                "cshotEntry:$cshotEntry")
        }
        Assert.assertEquals(TEST_COUNT, cshotEntries.size)
        val firstCShotEntry = cshotEntries[0]
        val bestPictureId = firstCShotEntry.mediaId
        val selectedPaths = ArrayList<Path>()
        for (cshotEntry in cshotEntries) {
            selectedPaths.add(LocalImage.ITEM_PATH.getChild(cshotEntry.mediaId))
        }
        val inLockMode = false
        val originSetPath = ORIGIN_SET_PATH
        val cshotId = firstCShotEntry.cshotId
        val paramMap = MenuActionGetter.releaseCShot.builder
            .setCShotId(cshotId)
            .setBestPicMediaId(bestPictureId)
            .setInLockedMode(inLockMode)
            .setOriginSetPath(originSetPath)
            .setPaths(selectedPaths)
            .build()
        var resultCode = MenuAction.RESULT_INVALID

        // when
        conditionVariable.close()
        MenuOperationManager.doAction(MenuAction.RELEASE_CSHOT, paramMap, onCompleted = { code, _ ->
            resultCode = code
            conditionVariable.open()
        })
        conditionVariable.block(TIME_OUT)

        // then
        val resultCShotEntries = CShotHelper.getAllCShotEntryByMediaIds(cshotEntries.map { it.mediaId })
        resultCShotEntries.forEach { resultCShotEntry ->
            TestLog.d(TAG, "should_return_correct_data_when_ReleaseCShotOperation_execute, " +
                "resultCShotEntry:$resultCShotEntry")
        }
        Assert.assertEquals(resultCode, MenuAction.RESULT_SUCCESS)
        Assert.assertEquals(TEST_COUNT, resultCShotEntries.size)
        for (cshotEntry in resultCShotEntries) {
            Assert.assertEquals(CSHOT_ID_NOT_CSHOT, cshotEntry.cshotId)
        }
    }
}