/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - CShotAlbumModelTest
 ** Description:
 ** Version: 1.0
 ** Date: 2020/09/25
 ** Author: zhengyirui@Apps.Gallery3D
 ** TAG: OPLUS_FEATURE_CLASSIFIED
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** zhengyirui@Apps.Gallery3D   2020/09/25    1.0        OPLUS_FEATURE_CLASSIFIED
 *************************************************************************************************/

package com.oplus.gallery.business_lib.model.model

import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.annotations.TestType
import com.oplus.gallery.business_lib.data.helper.LocalMediaHelper
import com.oplus.gallery.framework.abilities.data.model.CShotAlbumModel
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.local.set.ClassifiedAlbum.Companion.getAllBucketId
import com.oplus.gallery.business_lib.model.data.local.set.ClassifiedCShotAlbum
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.utils.TestConstants.NOEXTERNAL
import com.oplus.gallery.utils.TestLog
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized

@RunWith(Parameterized::class)
class CShotAlbumModelTest(
    private val bucketIdList: List<Int>,
    private val expectCount: Int,
) {
    companion object {
        private const val TAG = "CShotAlbumModelTest"
        private const val TYPE_CSHOT_ALBUM = "/Local/CShotAlbum"
        private const val TEST_COUNT_PER_BUCKET_ID = 10
        private val BUCKET_ID_LIST: List<Int> = listOf(1001, 1002, 1003)

        @Parameterized.Parameters
        @JvmStatic
        @Suppress("ArrayPrimitive")
        fun parameters(): Collection<*> {
            val allBucketId: Int = getAllBucketId(ClassifiedCShotAlbum.CSHOT_BUCKET_PATH)
            val buckIdListSize: Int = BUCKET_ID_LIST.size
            val bucketId: Int = BUCKET_ID_LIST[0]
            return listOf(
                arrayOf(listOf(allBucketId), buckIdListSize),
                arrayOf(BUCKET_ID_LIST, buckIdListSize),
                arrayOf(listOf(bucketId), 1)
            )
        }
    }

    @Before
    fun setUp() {
        LocalMediaHelper.delete()
        BUCKET_ID_LIST.forEachIndexed { index, bucketId ->
            LocalMediaHelper.addCShotAlbum(
                bucketId,
                bucketId.toString(),
                bucketId,
                TEST_COUNT_PER_BUCKET_ID,
                index * TEST_COUNT_PER_BUCKET_ID
            )
        }
    }

    @After
    fun tearDown() {
        LocalMediaHelper.delete()
    }

    /**
     * 前提：传入bucketId列表
     * 验证：getCount()是bucketId列表大小，getSubMediaItem每项mediaId和filePath正确
     */
    @Ignore
    @Test
    @TestType(NOEXTERNAL)
    @CasePrioritization("B")
    fun should_return_correct_data_when_ClassifiedCShotAlbumSet_reload() {
        //Wait: 有概率性问题，解决后再去掉Ignore @郑一锐
        val model = CShotAlbumModel(TYPE_CSHOT_ALBUM,
            bucketIdList = bucketIdList,
            bucketName = TextUtil.EMPTY_STRING,
            SourceConstants.Local.PATH_ALBUM_ANY_CSHOT,
            true
            ).apply {
            reload()
        }
        val count = model.getCount()
        Assert.assertEquals(expectCount, count)
        checkItems(model.getItems(0, count))
    }

    private fun checkItems(items: List<MediaItem>) {
        val mediaInfoList = LocalMediaHelper.query()
        for (mediaItem in items) {
            TestLog.d(TAG, "mediaItem: $mediaItem")
            for (mediaInfo in mediaInfoList) {
                if (mediaInfo.mediaId == mediaItem.mediaId) {
                    mediaInfo.assertEquals(mediaItem)
                    break
                }
            }
        }
    }
}