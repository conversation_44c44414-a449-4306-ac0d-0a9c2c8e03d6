/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AccountManagerTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : Date: 2024/12/11
 ** Author      : W9002848 Pengcheng Lin
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                       <date>      <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** W9002848 Pengcheng Lin       2024/12/11      1.0        Interface testing
 *********************************************************************************/
package com.oplus.gallery.cloudsync_lib

import androidx.test.rule.ActivityTestRule
import com.coloros.gallery3d.app.MainActivity
import com.oplus.autotest.olt.testlib.annotations.CaseId
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.common.BasicData
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil.getBoolean
import com.oplus.gallery.foundation.util.multiprocess.MultiProcessSpUtil.getString
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.ocloudsync.account.AccountManager
import com.oplus.gallery.utils.TestAdbUtils.stopOtherApp
import com.oplus.gallery.utils.TestConstants.PKG_ACCOUNT
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test

class AccountManagerTest {
    val TAG = AccountManagerTest::class.simpleName

    @Rule
    @JvmField
    val activityRule = ActivityTestRule(MainActivity::class.java)

    companion object {
        private val targetContext = BasicData.getTargetContext()
        /**
         * sp key：用于记录当前的登入状态
         * copy from AccountManager.AccountSpHelper.KEY_LOGIN
         */
        private const val KEY_LOGIN = "KEY_LOGIN"
        /**
         * sp key：用于记录曾经是否有登入过
         * copy from AccountManager.AccountSpHelper.KEY_HAD_LOGGED_IN
         */
        private const val KEY_HAD_LOGGED_IN = "KEY_HAD_LOGGED_IN"
        /**
         * sp key：用于记录账号信息，确保相册后台启动场景不拉起账号也能获取相对准确的账号信息
         * copy from AccountManager.AccountSpHelper.KEY_ACCOUNT_INFO
         */
        private const val KEY_ACCOUNT_INFO = "KEY_ACCOUNT_INFO"
    }

    @Before
    fun setUp() {
        stopOtherApp(PKG_ACCOUNT)
    }

    @After
    fun tearDown() {
        stopOtherApp(PKG_ACCOUNT)
        activityRule.activity.finish()
    }

    /**
     * 跨系统耦合依赖测试之验证相册云服务账号退出后，设置测试缓存数据再次调用登录，测试缓存数据应清除，登录状态为false.
     * 【given】接口调用
     * 【when】 AccountManager.isLogin() & AccountManager.getAccountInfo
     * 【then】验证相册云服务账号退出后，设置测试缓存数据再次调用登录，测试缓存数据应清除，登录状态为false.
     */
    @Test
    @CaseId("AT_APP_Gallery_10629")
    @CasePrioritization("A")
    fun should_onLogin_fail_when_setTestDataToSP() {
        //1、先退出登陆，并清除登陆缓存数据
        AccountManager.onLogout(true)
        Assert.assertFalse(AccountManager.isLogin)
        checkAccountInfoData()
        //2、设置登陆缓存数据，并触发登陆测试
        val setAccountInfo = AccountManager.AccountInfo(
            true, "011G138", "GB2312", "CHAIN",
            "200", "OK", "http://oppo.com", "1"
        )
        val setAccountInfoJson = JsonUtil.toJson(setAccountInfo)
        MultiProcessSpUtil.save(KEY_ACCOUNT_INFO, setAccountInfoJson)
        AccountManager.onLogin()
        //3、校验设置的测试登陆数据应清除
        val getAccountInfo = AccountManager.getAccountInfo()
        val accountInfoGetJson = JsonUtil.toJson(getAccountInfo)
        Assert.assertNotEquals(setAccountInfoJson, accountInfoGetJson)
        Assert.assertTrue(AccountManager.isLogin)
        checkAccountInfoData()
    }

    /**
     * 跨系统耦合依赖测试之验证相册云服务账号先登录再退出（清除登录缓存），检验缓存数据被清除.
     * 【given】接口调用
     * 【when】 AccountManager.onLogout(false)
     * 【then】验证相册云服务账号先登录再退出（清除登录缓存），检验缓存数据被清除.
     */
    @Test
    @CaseId("AT_APP_Gallery_10630")
    @CasePrioritization("A")
    fun should_onLogout_success_when_when_incoming_false() {
        //1、调用AccountManager.onLogin()后，校验缓存数据
        AccountManager.onLogin()
        Assert.assertTrue(getBoolean(KEY_LOGIN, true))
        Assert.assertTrue(getBoolean(KEY_HAD_LOGGED_IN, true))
        checkAccountInfoData()

        //2、调用AccountManager.onLogout(false)后，校验缓存数据
        AccountManager.onLogout(false)
        Assert.assertFalse(AccountManager.isLogin)
        Assert.assertFalse(getBoolean(KEY_LOGIN, true))
        Assert.assertTrue(getBoolean(KEY_HAD_LOGGED_IN, false))
        checkAccountInfoData()
    }

    /**
     * 跨系统耦合依赖测试之验证相册云服务账号先登录再退出（清除登录缓存），检验缓存数据被清除.
     * 【given】接口调用
     * 【when】 AccountManager.onLogout(true)
     * 【then】验证相册云服务账号先登录再退出（清除登录缓存），检验缓存数据被清除.
     */
    @Test
    @CaseId("AT_APP_Gallery_10631")
    @CasePrioritization("A")
    fun should_onLogout_success_when_incoming_true() {
        //1、调用AccountManager.onLogin()后，校验缓存数据
        AccountManager.onLogin()
        Assert.assertTrue(getBoolean(KEY_LOGIN, true))
        Assert.assertTrue(getBoolean(KEY_HAD_LOGGED_IN, true))
        checkAccountInfoData()

        //2、调用AccountManager.onLogout(true)后，校验缓存数据
        AccountManager.onLogout(true)
        Assert.assertFalse(AccountManager.isLogin)
        Assert.assertFalse(getBoolean(KEY_LOGIN, true))
        Assert.assertEquals(EMPTY_STRING, getString(KEY_ACCOUNT_INFO, EMPTY_STRING))
        checkAccountInfoData()
    }

    private fun checkAccountInfoData() {
        Assert.assertFalse(AccountManager.isLoginReadFromRemote)
        val accountInfo = AccountManager.getAccountInfo()
        Assert.assertNotNull(accountInfo)
        Assert.assertFalse(accountInfo.isLogin)
        Assert.assertEquals(accountInfo.token, EMPTY_STRING)
        Assert.assertEquals(accountInfo.userId, EMPTY_STRING)
        Assert.assertEquals(accountInfo.userName, EMPTY_STRING)
        Assert.assertEquals(accountInfo.resultCode, EMPTY_STRING)
        Assert.assertEquals(accountInfo.resultMsg, EMPTY_STRING)
        Assert.assertEquals(accountInfo.avatarUrl, EMPTY_STRING)
        Assert.assertEquals(accountInfo.status, EMPTY_STRING)
    }
}