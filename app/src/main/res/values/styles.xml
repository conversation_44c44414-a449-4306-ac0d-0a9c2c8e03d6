<resources>
    <!-- Base application theme. -->
    <style name="AppTheme" parent="@style/CommonDefaultTheme">
        <!--重写toolbar的菜单按钮-->
        <item name="actionOverflowButtonStyle">@style/app_OverflowButtonStyle</item>
        <item name="selectSystemForceDarkType">1</item>
        //Bugid：4928966 一个暂时性的规避方案：平板相册键盘适配需求合入，该方案则须剔除
        //屏蔽焦点的显示效果（一个灰色浮层）
        <item name="android:defaultFocusHighlightEnabled">false</item>
    </style>

    <style name="app_OverflowButtonStyle">
        <item name="android:src">@drawable/app_ic_action_more_selector</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="android:scaleType">center</item>
    </style>
</resources>