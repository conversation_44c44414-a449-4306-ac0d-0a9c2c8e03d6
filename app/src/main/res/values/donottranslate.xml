<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_platform_key" translatable="false">ATBFAiB6haALGgWqi8mP8q0hXHD4dgo0rPbEIBjVwtSiiFz16QIhAKS8WVa047+11mbaE5nrb2HtZ140aLPZcctCxqqdGgoXX1NA62Vwb25hLHNldFRlbXBvcmFyeUF1dG9CcmlnaHRuZXNzQWRqdXN0bWVudCxLZXlndWFyZE1hbmFnZXI6AAAA</string>
    <!-- 有使用到sau服务和sau jar包-->
    <string name="app_permission_key" translatable="false">j4zyIv+PVyehP+y7J3nhRlRBEX3hRsiLLyNTsxxzvIGCCfrp3DeLfRjUFti7OBnkHGvJY6pmXXb7xIrYZOnirSa83HbTrPHSkptnvoNgzVKLe0I0kt9BE5fmkpIy2Xem12KT4jVitHHjQ4h3QO5uxdrjQmJt7DQOjVIffeJRfYtQQoJ+hF5z7corNt8FOdO6g+2tUqoQ5OED5BbIHTj95WutsJ0j8PfIZfm1kvxUpN+NjMwrjMtQKTWlbmyFoDS2J3xJahx3MTabaEdn+IOhlDydSZdwbqoZcSQkbEbQzzcrke58IIx/rXeduWneczkvrRyDVmNY4olrzDEaekNW1g== 1 com.coloros.gallery3d 64AAFAF1D5BC9155A9E417A849E4F8EDA1D0D1341667C28ED7C443C76F820B9A 2917</string>

    <!-- 一加内销升级到S的时候因为分区需要对数据进行授权 -->
    <string name="op_change_permission" translatable="false">false</string>
    <!-- 通过读取支持云服务的地区来决定是否需要显示云服务开关:cn-中国,jp-日本,tw-中国台湾,th-泰国,id-印尼,ph-菲律宾,kn-柬埔寨,in-印度,sg-新加坡,
    my-马来西亚,vn-越南,apc-亚太，在OS12.1及以上OS系统会将中国香港，缅甸，老挝，尼泊尔，柬埔寨4个区域打点结果获取为apc，因为云服务之前支持柬埔寨，所以需要配置apc -->
    <array name="cloud_sync_accept_region" translatable="false">
        <item>cn</item>
        <item>jp</item>
        <item>tw</item>
        <item>th</item>
        <item>id</item>
        <item>ph</item>
        <item>kn</item>
        <item>in</item>
        <item>sg</item>
        <item>my</item>
        <item>vn</item>
        <item>apc</item>
    </array>
    <bool name="property_domestic" translatable="false">true</bool>
    <!--AppPlatform 接口提权权限校验授权码-->
    <string name="osdk_security_code" translatable="false">ATBFAiEA5LjBRTZOxbNyyBW1UkMzi+ZLrwzLS21E651AtRC3absCID6GMrsnVlqrUpi4FrSwxGcSKU1L9lwc8R7hpRChzV0CZn1cVU9wbHVzb3NEaXNwbGF5TWFuYWdlci5zZXRUZW1wb3JhcnlBdXRvQnJpZ2h0bmVzc0FkanVzdG1lbnQ6AAAA</string>
    <string name="asset_meicam_license">assets:/license/760-836-90aed46b-866e-4ba9-b1a0-bd3343fa1623.lic</string>
    <string name="app_feature_pm_sticker" translatable="false" tools:ignore="ResourceName">com.oneplus.gallery.pmf</string>
    <array name="package_name_pm_res" translatable="false">
        <item>com.oneplus.gallery.pmres</item>
    </array>
    <string name="oaf_auth_code" translatable="false">ATBFAiEAuGhRf+u0bEwe2W6gVmvy6Fv8o6XT3badhWlyRsHl89sCIFmXld4KS4Ydx7uFnkZOxhqToFhh2hOBvSlTY+ksZVq5caiRf/gAAAA=</string>
</resources>