/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - GlobalCameraReceiver.kt
 ** Description: 全局监听摄像头的状态变化广播
 ** Version: 1.0
 ** Date: 2024/9/25
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/9/25     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.coloros.gallery3d.app.globalbroadcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.app.AppConstants.Action.ACTION_CAMERA_STATE
import com.oplus.gallery.standard_lib.scene.limit.CAMERA_STATE_CHANGED
import com.oplus.gallery.standard_lib.scene.limit.SceneDispatcher
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 全局监听摄像头的状态变化广播
 */
class GlobalCameraReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        if (ACTION_CAMERA_STATE == intent?.action) {
            SceneDispatcher.sendScene(CAMERA_STATE_CHANGED)
        }
    }

    companion object {
        private const val TAG = "GlobalCameraReceiver"
        fun register(): GlobalCameraReceiver {
            GLog.d(TAG) { "register " }
            val cameraStateReceiver = GlobalCameraReceiver()
            BroadcastDispatcher.registerReceiver(ContextGetter.context, cameraStateReceiver, IntentFilter(ACTION_CAMERA_STATE))
            return cameraStateReceiver
        }

        fun unregister(context: Context, receiver: GlobalCameraReceiver?) {
            if (receiver != null) {
                BroadcastDispatcher.unregisterReceiver(ContextGetter.context, receiver)
                GLog.d(TAG) { "unregister " }
            }
        }
    }
}