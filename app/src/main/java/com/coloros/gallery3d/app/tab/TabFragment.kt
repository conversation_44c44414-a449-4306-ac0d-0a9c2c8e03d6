/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TabFragment.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/07/17
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** yegua<PERSON><PERSON>@Apps.Gallery		2020/07/17		1.0		OPLUS_FEATURE_TAB
 *********************************************************************************/
package com.coloros.gallery3d.app.tab

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Paint
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import android.widget.FrameLayout.LayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.animation.doOnStart
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.doOnAttach
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.coloros.gallery3d.R
import com.coloros.gallery3d.app.anim.BottomReplacementAnimator
import com.coloros.gallery3d.app.sidepane.ISelectedPositionSetter
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.coui.appcompat.segmentbutton.COUISegmentButtonLayout
import com.oplus.gallery.basebiz.app.OnFragmentVisibilityChange
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.TAB_FRAGMENT
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.initMaskBlur
import com.oplus.gallery.basebiz.helper.refreshBlur
import com.oplus.gallery.basebiz.helper.restrict.RestrictWatermarkDownloadHelper
import com.oplus.gallery.basebiz.helper.restrict.RestrictWatermarkPopupHelper
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.adjustControlIconColor
import com.oplus.gallery.basebiz.sidepane.isClose
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.sidepane.isSliding
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.sidepane.isVisible
import com.oplus.gallery.basebiz.uikit.animation.MainSelectionSpringAnimHelper
import com.oplus.gallery.basebiz.uikit.animation.MainToolBarFadeOutAnimator
import com.oplus.gallery.basebiz.uikit.controller.TintTranslucentElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintBlurDrawableElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintMainTabIconDrawableElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintMainTabIconDrawableElement.Companion.tintColorDisable
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintStatusElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.TintTextViewElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.ToolbarSubTextElement
import com.oplus.gallery.basebiz.uikit.controller.toolbar.ToolbarTitleElement
import com.oplus.gallery.basebiz.uikit.fragment.IClickSelectedTabAgainCallback
import com.oplus.gallery.basebiz.uikit.fragment.IFragmentCallback
import com.oplus.gallery.basebiz.uikit.fragment.IMainTabController
import com.oplus.gallery.basebiz.uikit.fragment.IMenuClickListener
import com.oplus.gallery.basebiz.uikit.fragment.ISelectionModeCallback
import com.oplus.gallery.basebiz.uikit.fragment.ITabContentFragment
import com.oplus.gallery.basebiz.uikit.fragment.ITimelineTabButtonStateCallback
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.basebiz.widget.BlurButtonWrapperView
import com.oplus.gallery.basebiz.widget.MainTabToolbar
import com.oplus.gallery.basebiz.widget.MultiLayerImageView
import com.oplus.gallery.business_lib.helper.TimelineTrackHelper
import com.oplus.gallery.business_lib.helper.ToolbarHelper
import com.oplus.gallery.business_lib.helper.ToolbarHelper.setupToolbar
import com.oplus.gallery.business_lib.model.selection.SelectionData
import com.oplus.gallery.business_lib.safebox.SafeBoxHelper
import com.oplus.gallery.business_lib.timeline.viewmodel.TimelinePreLoader
import com.oplus.gallery.business_lib.util.TimelineUtils
import com.oplus.gallery.foundation.gstartup.CPUIdleDetector
import com.oplus.gallery.foundation.taskscheduling.ext.launch
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_ENTER_SAFE_BOX_FROM_BOTTOM_TAB
import com.oplus.gallery.foundation.tracing.constant.TimelineTrackConstant.Value.E_MODE_STARTUP
import com.oplus.gallery.foundation.tracing.constant.TimelineTrackConstant.Value.E_MODE_TAB_CHANGE
import com.oplus.gallery.foundation.ui.helper.BackgroundBlurUtils
import com.oplus.gallery.foundation.ui.helper.enablePressFeedback
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.ui.systembar.toolbar.BasicTintAnimationElement.Companion.tintColorBlack
import com.oplus.gallery.foundation.ui.systembar.toolbar.BasicTintAnimationElement.Companion.tintColorWhite
import com.oplus.gallery.foundation.ui.systembar.toolbar.DirectionAnimationDriver
import com.oplus.gallery.foundation.ui.systembar.toolbar.DirectionAnimationElement
import com.oplus.gallery.foundation.ui.systembar.toolbar.IProgressChanger
import com.oplus.gallery.foundation.ui.systembar.toolbar.LightNightAnimationDriver
import com.oplus.gallery.foundation.ui.systembar.toolbar.TintDrawableAnimationElement
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.ext.isHighlight
import com.oplus.gallery.foundation.util.ext.isRtl
import com.oplus.gallery.foundation.util.ext.tintOriginalDrawable
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SENIOR_PICKED
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.ITaskManageAbility
import com.oplus.gallery.framework.abilities.taskmanage.RealShowTimeInstrument
import com.oplus.gallery.framework.abilities.taskmanage.config.TaskConfigStrategy
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.scene.limit.ENTER_MAIN_TAB
import com.oplus.gallery.standard_lib.scene.limit.SceneDispatcher
import com.oplus.gallery.standard_lib.ui.toolbar.behavior.BehaviorHelper.LOCATION_SIZE
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.timelinepage.TimelineTabFragment
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext
import com.oplus.gallery.basebiz.R as BasebizR

@RouterNormal(TAB_FRAGMENT)
class TabFragment : TemplateFragment(),
    ISelectionModeCallback,
    IFragmentCallback,
    ISidePaneListener,
    IMainTabController,
    ILoadTaskOwner {

    override val id: String = TaskOwnerConst.TAB
    override val lifeCycleEvent: Lifecycle.Event
        get() = lifeEvent
    override val container: String? = null
    override val affinities: Set<String>? = null
    override val linkages: Set<String>? = null
    override val taskConfigStrategy: TaskConfigStrategy by lazy {
        TaskConfigStrategy()
    }

    private val tabMenuIds = listOf(R.id.timeline, R.id.all_album)

    private lateinit var viewPager: ViewPager2
    private lateinit var fragmentAdapter: TabFragmentAdapter
    private var bottomTabSegmentNavigationView: LinearLayout? = null
    private var bottomTabSegmentLeft: FrameLayout? = null
    private var bottomTabSegmentRight: FrameLayout? = null

    private var bottomSegmentWrapperView: BlurButtonWrapperView? = null
    private val bottomTabSegmentButton: COUISegmentButtonLayout?
        get() = bottomSegmentWrapperView?.getButton()

    private var filterButtonWrapperView: BlurButtonWrapperView? = null
    private var timelineFilterHighlightButton: ImageView? = null

    private var switchButtonWrapperView: BlurButtonWrapperView? = null
    private var timelinePresentationSwitchHighlightButton: ImageView? = null

    private var sortButtonWrapperView: BlurButtonWrapperView? = null
    private var albumAddButtonWrapperView: BlurButtonWrapperView? = null

    var currentTabIndex: Int = TAB_INDEX_INVALID
    private var isSelectionMode: Boolean = false
    private var disableAccessibilityViews = mutableListOf<View>()
    private var selectedPositionSetter: ISelectedPositionSetter? = null

    private var bottomReplacementAnimator: BottomReplacementAnimator? = null
    private var mainToolBarFadeAnimator: MainToolBarFadeOutAnimator? = null

    private var mainTabToolbar: MainTabToolbar? = null

    private val topTranslucentView: ImageView? by lazy { view?.findViewById(BasebizR.id.topTranslucentView) }
    private val topBlurMask: ImageView? by lazy { view?.findViewById(BasebizR.id.topTranslucentBlurLayer) }
    private val bottomTranslucentView: ImageView? by lazy { view?.findViewById(BasebizR.id.bottomTranslucentView) }
    private var bottomTintElements: MutableList<DirectionAnimationElement> = mutableListOf()

    private var bottomTranslucentAnim: ValueAnimator? = null

    private val segmentIvButtonMarginHorizontal: Int by lazy {
        resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_segment_iv_button_margin_horizontal)
    }
    private val segmentLayoutPaddingHorizontal: Int by lazy {
        resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_segment_layout_padding_horizontal)
    }

    private var isTopTranslucentViewShowing = false
    private var isBottomTranslucentViewShowing = false
    private var isPageChangeAnimating = false

    private var isDarkMode = false
    // 底部沉浸式蒙层渐变动画控制器
    private val lightNightAnimDriver: LightNightAnimationDriver by lazy { LightNightAnimationDriver() }
    private val directionAnimDriver: DirectionAnimationDriver by lazy {
        object : DirectionAnimationDriver() {
            override val progressAnimator: ValueAnimator = springProgressAnim
        }
    }

    /**
     * 用于数据埋点
     * 去往到不同的界面是否是通过点击？true:通过点击；false:通过滑动
     * onPageSelected先于onTabSelected ： 滑动  onPageSelected之前有onPageScrolled（设置selectTabFromClick为false）,保证了来自滑动
     * onTabSelected先于onPageSelected ： 点击  onPageSelected执行完之后才执行onPageScrolled设置selectTabFromClick为false）,不会影响selectTabFromClick值的判断
     */
    private var selectTabFromClick = false

    /*
    是否冷启动 <p>
    出于性能考虑，我们会在onPause时关闭viewPager的离屏加载，在onResume时打开viewPager的离屏加载，但是冷启动情况下，
    需要延迟设置viewPager的offscreenPageLimit，避免发现页和所有图集提前加载，拖慢冷启动速度
    这一变量会在冷启动后，开始加载图集tab页时置为true
     */
    private var isColdLaunch = true

    /**
     * 限定水印下载器
     * */
    private var restrictWatermarkPopupHelper: RestrictWatermarkPopupHelper? = null

    private var buttonStateCallback: ITimelineTabButtonStateCallback? = null

    /**标记回前台时是否需要纠正ViewPager:
     * 用于判断从后台回到前台时,是否有发生屏幕变化(大小屏切换、横竖屏切换、分屏等)
     * 当屏幕发生变化时,viewPager容易发生闪现时间轴和图片,造成闪屏现象
     * 所以需要在回前台时,进行纠正
     * bugId:9339936 外销机:竖屏-16.0图集tab--滑动到我的图集分组--随机点击某个图集进详情--切换到横屏--返回,图集tab闪现时间轴
     */
    private var checkViewPagerWhenEnterFg: Boolean = false

    private val isLightOs by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_PRODUCT_LIGHT)
    }

    private val isSeniorPickedSupported by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SENIOR_PICKED)
    }

    private val onPageChangeListener = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            if (!isResumed) {
                GLog.d(TAG, LogFlag.DL) {
                    "onPageSelected, error, fragment isResumed=false. $currentTabIndex->$position"
                }
                return
            }
            // eap中存在极个别情况position返回为-1情况，判断当position不在tabMenuIds范围内时，返回
            if (position !in tabMenuIds.indices) {
                GLog.w(TAG, LogFlag.DL) {
                    "onPageSelected: position: $position not in tabMenuIds.indices ${tabMenuIds.indices}, " +
                            "viewpager size: ${viewPager.width},${viewPager.height}"
                }
                return
            }
            enterNeedAnimation = false
            bottomTabSegmentButton?.onPageSelected(position)
            updateSegmentIvButton(position)
            updateTopBottomMaskColor(position)
            if (currentTabIndex != position) {
                fragmentAdapter.getFragment(currentTabIndex)?.apply {
                    fragmentState?.removeParent()
                    onVisibilityChange(isVisible = false)
                }
                fragmentAdapter.getFragment(position)?.apply {
                    val tag = tag ?: let {
                        GLog.w(TAG, "onPageSelected: fragment tag is null, fragment may destroyed, return")
                        return@apply
                    }
                    fragmentState?.setParent(
                        tag,
                        Lifecycle.State.RESUMED,
                        <EMAIL>,
                        Lifecycle.State.RESUMED
                    )
                    onVisibilityChange(isVisible = true)
                }

                if (currentTabIndex == -1) {
                    TimelineTrackHelper.trackMainPageSource(position + 1, E_MODE_STARTUP)
                } else {
                    TimelineTrackHelper.trackMainPageSource(position + 1, E_MODE_TAB_CHANGE)
                }

                GLog.d(TAG, LogFlag.DL) {
                    "onPageSelected, change currentTabIndex. $currentTabIndex->$position"
                }
                currentTabIndex = position

                // 非 “可见&&展开” 状态，则需要同步tab选中状态到侧边栏
                if ((sidePaneAccessor.isVisible() && sidePaneAccessor.isOpen()).not()) {
                    selectedPositionSetter?.syncTabSelectedPosition(currentTabIndex)
                }
            }

            updateToolbar()
            checkPageImmersiveState(position = 0, isForceStart = true, isNeedAnimation = false)
        }

        override fun onPageScrollStateChanged(state: Int) {
            super.onPageScrollStateChanged(state)
            bottomTabSegmentButton?.onPageScrollStateChanged(state)
        }

        override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            super.onPageScrolled(position, positionOffset, positionOffsetPixels)
            selectTabFromClick = false
            bottomTabSegmentButton?.onPageScrolled(position, positionOffset, positionOffsetPixels)
        }
    }

    private fun TemplateFragment.onVisibilityChange(isVisible: Boolean) {
        if (this is OnFragmentVisibilityChange) {
            contentView?.let { onVisibilityChange(isVisible, it) }
        }
    }

    override fun onEnterBySidePane() {
        // doNothing
    }

    override fun onExitBySidePane() {
        if (isSelectionMode) {
            // 从侧边导航栏切换到图集时,tab要是处于编辑模式时,需要退出编辑模式
            exitSelectionMode()
        }
    }

    fun setSelectedPositionSetter(setter: ISelectedPositionSetter?) {
        selectedPositionSetter = setter
    }

    override fun getLayoutId(): Int = BasebizR.layout.main_tab_layout

    override fun onCreate(savedInstanceState: Bundle?) {
        context?.getAppAbility<ITaskManageAbility>()?.use {
            it.register(this)
        }
        super.onCreate(savedInstanceState)
        isSupportImmersive = true
        this.activity?.let {
            // 是否是暗色模式
            isDarkMode = isDark(it)
            //限定水印弹窗功能
            restrictWatermarkPopupHelper = RestrictWatermarkPopupHelper(it)
        } ?: {
            GLog.e(TAG, LogFlag.DL) { "activity = null" }
        }
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        mainTabToolbar = view.findViewById(BasebizR.id.mainTabToolbar)
        fragmentAdapter = TabFragmentAdapter(childFragmentManager, lifecycle)

        initViewPager(view)
        initBottomTabNavigation()
        initToolbar()
        initTranslucentMask()
    }

    private fun initTranslucentMask() {
        topTranslucentView?.background?.alpha = 0
        bottomTranslucentView?.alpha = 0f
        topBlurMask?.apply {
            background.alpha = 0
            viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    layoutParams = layoutParams?.apply {
                        val timelineMarginTop = resources.getDimensionPixelSize(BasebizR.dimen.main_timeline_margin_top)
                        val toolbarHeight = resources.getDimensionPixelSize(BasebizR.dimen.main_tab_toolbar_min_height)
                        val extraHeight = resources.getDimensionPixelSize(BasebizR.dimen.main_tab_top_blur_mask_extra_height)
                        // 通过定制顶部模糊遮罩的高度，来避免遮挡时间轴的月标签
                        this.height = statusBarHeight() + timelineMarginTop + toolbarHeight + extraHeight
                    }
                    viewTreeObserver.removeOnGlobalLayoutListener(this)
                }
            })
        }
    }

    private fun initViewPager(view: View) {
        viewPager = view.findViewById<ViewPager2>(com.oplus.gallery.albumsetpage.R.id.view_pager).apply {
            registerOnPageChangeCallback(onPageChangeListener)
            adapter = fragmentAdapter
            isSaveEnabled = false

            CPUIdleDetector.doOnCPUIdle(requireActivity(), LOAD_CACHE_TAB_PAGE_DELAY) {
                // 延迟加载图集Tab和发现Tab，减少应用启动时间
                if (isColdLaunch) {
                    enablePreLoadPage()
                    isColdLaunch = false
                }
            }
            if (currentTabIndex != TAB_INDEX_INVALID) {
                setCurrentItem(currentTabIndex, false)
                currentTabIndex = TAB_INDEX_INVALID
            }

            getRecyclerView()?.let {
                it.overScrollMode = RecyclerView.OVER_SCROLL_NEVER
                it.addOnLayoutChangeListener { _, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                    val changed = (left != oldLeft) || (top != oldTop) || (right != oldRight) || (bottom != oldBottom)
                    if (changed && !sidePaneAccessor.isSliding()) {
                        // 若是侧边导航栏展开收起，由于会布局频繁变化，不需要实时设置，已经在展开收起结束处单独处理,
                        correctViewPagerPosition("onLayoutChanged")
                    }
                }
            }
        }

        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let {
                view.doOnAttach { _ ->
                    setViewPagerSlideEnabled(!it.isVisible())
                }
            }
        }
    }

    /**
     * 通过设置离屏加载数量offscreenPageLimit，预加载tab
     * 轻量项目上只离屏一个tab
     */
    private fun enablePreLoadPage() {
        // 轻量项目上只离屏一个tab
        if (isLightOs) {
            viewPager.offscreenPageLimit = 1
        } else {
            viewPager.offscreenPageLimit = fragmentAdapter.itemCount
        }
    }

    /**
     * 禁用离屏加载
     * tab页处于后台时，减少离屏加载的数量，减轻负载，避免争抢上层页面的资源
     * 比如进入大图后
     */
    private fun disablePreLoadPage() {
        viewPager.offscreenPageLimit = ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT
    }

    private fun initBottomTabNavigation() {
        val startTime = System.currentTimeMillis()
        bottomTabSegmentNavigationView = (LayoutInflater.from(context)
            .inflate(BasebizR.layout.main_tab_bottom_segment_navigation_layout, null)) as? LinearLayout
        bottomTabSegmentNavigationView?.apply {
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT).apply { gravity = Gravity.BOTTOM }
            activity?.findViewById<FrameLayout>(BasebizR.id.container)?.addView(this)
            // 不让点击事件透传到下面的view
            setOnTouchListener { _, event -> isShouldInterceptedEvent(event) }
        }

        initSegmentNavigationViewGroup()
        updateSegmentIvButton(viewPager.currentItem)
        bottomTabSegmentButton?.apply {
            // 分段按钮文本设置及与ViewPage联动
            val tabMenuTitles = arrayOf(
                resources.getString(BasebizR.string.main_fragment_title_timeline),
                resources.getString(BasebizR.string.main_fragment_title_all_album)
            )
            setSegmentButtons(tabMenuTitles)
            setOnSelectedSegmentChangeListener { positionFrom, positionTo, progress ->
                isPageChangeAnimating = (progress != 0F) && (progress != 1F)

                // 这里是一个动画切换过程，虽然存在重复调用，但是是Tab和ViewPager动画同步所必要的
                viewPager.currentItem = positionTo
                updateSegmentIvButton(positionTo)
                updateTopBottomMaskColor(positionTo)
            }

            // 单击、长按等
            initTabSegmentButtonClickListeners()
        }

        recordFirstDrawTime()
        GLog.d(TAG, LogFlag.DL) { "initBottomTabNavigation, cost time = ${GLog.getTime(startTime)}" }
    }

    /**
     * 底栏Tab Navigation是否拦截事件，不让点击事件透传到下面的view
     */
    private fun isShouldInterceptedEvent(event: MotionEvent): Boolean {
        val bottomTabNaviNonNull = bottomTabSegmentNavigationView ?: return false
        val location = IntArray(LOCATION_SIZE) { -1 }
        var right = -1
        if ((filterButtonWrapperView?.isVisible == true)
            || (timelineFilterHighlightButton?.isVisible == true)
            || (sortButtonWrapperView?.isVisible == true)
        ) {
            bottomTabSegmentLeft?.getLocationInWindow(location)
        } else {
            bottomSegmentWrapperView?.getLocationInWindow(location)
            right = location[0] + (bottomSegmentWrapperView?.width ?: 0) + DISABLE_TOUCH_THRESHOLD
        }
        var left = location[0] + DISABLE_TOUCH_THRESHOLD
        val top = location[1] + DISABLE_TOUCH_THRESHOLD
        val bottom = top + bottomTabNaviNonNull.height + DISABLE_TOUCH_THRESHOLD

        if ((switchButtonWrapperView?.isVisible == true)
            || (timelinePresentationSwitchHighlightButton?.isVisible == true)
            || (albumAddButtonWrapperView?.isVisible == true)
        ) {
            bottomTabSegmentRight?.getLocationInWindow(location)
            right = location[0] + (bottomTabSegmentRight?.width ?: 0) + DISABLE_TOUCH_THRESHOLD
        }

        val touchX = event.rawX.toInt()
        val touchY = event.rawY.toInt()

        if (bottomTabNaviNonNull.isRtl) {
            val temp = left
            left = right
            right = temp
        }

        return (touchX in left..right) && (touchY in top..bottom)
    }

    private fun initSegmentNavigationViewGroup() {
        bottomTabSegmentNavigationView?.apply {
            bottomTabSegmentLeft = findViewById(BasebizR.id.fl_tab_left)
            bottomTabSegmentRight = findViewById(BasebizR.id.fl_tab_right)
            bottomSegmentWrapperView = findViewById<BlurButtonWrapperView?>(BasebizR.id.segmentWrapperView)?.apply {
                getButton<COUISegmentButtonLayout>()?.let { updateSegmentDrawDelegate(it) }
            }
            filterButtonWrapperView = findViewById(BasebizR.id.filterButtonWrapperView)
            timelineFilterHighlightButton = findViewById(BasebizR.id.iv_segment_filter_highlight)
            switchButtonWrapperView = findViewById(BasebizR.id.switchButtonWrapperView)
            timelinePresentationSwitchHighlightButton = findViewById(BasebizR.id.iv_segment_present_switcher_highlight)
            sortButtonWrapperView = findViewById(BasebizR.id.albumSortWrapperView)
            albumAddButtonWrapperView = findViewById(BasebizR.id.albumAddWrapperView)
            initMenuClickListener()
            initTimelineTabStateCallback()
        }

        // 依赖SidePane的状态，放在 post 中，即完成绘制后去更新，此时 SidePane 的展开/收起状态才是正确的
        bottomTabSegmentNavigationView?.post { updateSegmentNavigationLayoutIfNeed() }
    }

    /**
     * 初始化底部分段按钮
     */
    private fun updateSegmentDrawDelegate(segmentButton: COUISegmentButtonLayout) {
        val contextNonNull = context ?: return
        // 自定义分段按钮的 indicator 和整体背景的画笔
        segmentButton.setSegmentButtonDrawDelegate(object : COUISegmentButtonLayout.SegmentButtonDrawDelegate {

            override fun needProxy(): Boolean = true

            override fun setCustomBackrgoundPaint(): Paint {
                // 控件默认的背景色会影响背景质感模糊的混色，需要去除
                return getPaint(BasebizR.color.common_transparent)
            }

            override fun setCustomIndicatorPaint(): Paint {
                val isBottomImmersive = currentTabFragment()?.isBottomImmersiveCurrently() ?: false
                return getPaint(
                    when {
                        !isBottomImmersive -> BasebizR.color.base_blur_indicator_bg_color_not_immersive
                        !BackgroundBlurUtils.isColorBlurEnable() -> BasebizR.color.base_blur_indicator_bg_color_blur_disable
                        else -> BasebizR.color.base_blur_indicator_bg_color_immersive
                    }
                )
            }

            private fun getPaint(@ColorRes paintColor: Int) = Paint(1).apply {
                style = Paint.Style.FILL
                isAntiAlias = true
                color = contextNonNull.getColor(paintColor)
            }
        })
        segmentButton.invalidate()
    }

    /**
     * 按钮点击事件初始化
     */
    private fun initMenuClickListener() {
        val timelineMenuClickListener = fragmentAdapter.getFragment(TAB_INDEX_FIRST) as? IMenuClickListener
        timelineMenuClickListener?.let {
            filterButtonWrapperView?.setOnClickListenerWithPressFeedback {
                it.onMenuClick(BasebizR.id.filterButtonWrapperView)
            }
            timelineFilterHighlightButton?.setOnClickListenerWithPressFeedback {
                it.onMenuClick(BasebizR.id.iv_segment_filter_highlight)
            }
            switchButtonWrapperView?.setOnClickListenerWithPressFeedback {
                it.onMenuClick(BasebizR.id.switchButtonWrapperView)
            }
            timelinePresentationSwitchHighlightButton?.setOnClickListenerWithPressFeedback {
                it.onMenuClick(BasebizR.id.iv_segment_present_switcher_highlight)
            }
        }

        val albumMenuClickListener = fragmentAdapter.getFragment(TAB_INDEX_SECOND) as? IMenuClickListener
        albumMenuClickListener?.let {
            sortButtonWrapperView?.setOnClickListenerWithPressFeedback { it.onMenuClick(BasebizR.id.albumSortWrapperView) }
            albumAddButtonWrapperView?.setOnClickListenerWithPressFeedback { it.onMenuClick(BasebizR.id.albumAddWrapperView) }
        }
    }

    override fun onCreateTintElementsIfNeed(
        lightNightDriver: IProgressChanger<Boolean>,
        directionAnimDriver: IProgressChanger<Boolean>,
        elementList: MutableList<DirectionAnimationElement>
    ) {
        super.onCreateTintElementsIfNeed(lightNightDriver, directionAnimDriver, elementList)
        val contentFragment = currentTabFragment() ?: return
        val owner = this

        activity?.let {
            elementList.add(TintStatusElement(it, lightNightDriver, false).apply { register(owner) })
        }

        // 顶部蒙层出现消失动画
        listOfNotNull(topBlurMask, topTranslucentView).forEach {
            val showTranslucent = contentFragment.isTopImmersiveCurrently()
            val isNeedAnim = (isTopTranslucentViewShowing != showTranslucent)
            isTopTranslucentViewShowing = showTranslucent
            elementList.add(
                object : TintTranslucentElement(
                    translucentView = it,
                    changer = directionAnimDriver,
                    isNeedAnimation = isNeedAnim,
                    shouldTranslucentVisible = showTranslucent
                ) {
                    override fun updateAlpha(progress: Float) {
                        // 顶部蒙层 View 可能带有模糊效果，不能通过 View.setAlpha 改变透明度，否则会导致模糊视图变黑
                        it.background?.alpha = (progress * ALPHA_255.toFloat()).toInt()
                    }
                }.apply {
                    register(owner)
                }
            )
        }

        onCreateToolbarTintElement(lightNightDriver, directionAnimDriver, elementList)
    }

    private fun onCreateToolbarTintElement(
        lightNightAnimDriver: IProgressChanger<Boolean>,
        directionAnimDriver: IProgressChanger<Boolean>,
        elementList: MutableList<DirectionAnimationElement>
    ) {
        val owner = this
        mainTabToolbar?.apply {
            elementList.add(ToolbarTitleElement(this, lightNightAnimDriver).apply { register(owner) })
            elementList.add(ToolbarSubTextElement(context, this, lightNightAnimDriver).apply { register(owner) })
            cancelButtonWrapperView.let {
                val button = it.getButton<TextView>() ?: return@let
                val blurLayer = it.blurLayerView
                elementList.add(
                    TintTextViewElement(
                        context,
                        button,
                        lightNightAnimDriver,
                        lightColorRes = BasebizR.color.base_toolbar_title_color
                    ).apply { register(owner) })
                elementList.add(TintBlurDrawableElement(context, blurLayer.background, directionAnimDriver).apply { register(owner) })
            }

            if (isInSelectionMode()) {
                return
            }
            // 顶栏搜索按钮
            searchButtonWrapperView.let { buttonWrapper ->
                val button = buttonWrapper.getButton<MultiLayerImageView>() ?: return@let
                val icon = button.getLayerIcon() ?: return@let
                val blurLayer = buttonWrapper.blurLayerView

                elementList.add(object : TintDrawableAnimationElement(icon, lightNightAnimDriver) {
                    override val startColor: Int = tintColorWhite
                    override val endColor: Int = context.getColor(com.support.appcompat.R.color.coui_color_primary_neutral)
                }.apply { register(owner) })
                elementList.add(TintBlurDrawableElement(context, blurLayer.background, directionAnimDriver).apply {
                    register(owner)
                })
            }

            // 顶栏下拉菜单按钮 沉浸式着色
            overflowButtonWrapperView.let { buttonWrapper ->
                val button = buttonWrapper.getButton<MultiLayerImageView>() ?: return@let
                val blurLayer = buttonWrapper.blurLayerView
                button.getLayerIcon()?.let { icon ->
                    elementList.add(
                        object : TintDrawableAnimationElement(icon, lightNightAnimDriver) {
                            override val startColor: Int = tintColorWhite
                            override val endColor: Int = context.getColor(com.support.appcompat.R.color.coui_color_primary_neutral)
                        }.apply {
                            register(owner)
                        }
                    )
                    elementList.add(TintBlurDrawableElement(context, blurLayer.background, directionAnimDriver).apply { register(owner) })
                }
            }
        }
    }

    /**
     * 顶部沉浸式状态发生变化
     */
    override fun onImmersiveStateSwitched(isCurrentImmersive: Boolean) {
        super.onImmersiveStateSwitched(isCurrentImmersive)
        refreshViewBlur(refreshBottom = false)
    }

    private fun checkBottomImmersiveTint(isForceStart: Boolean = false, isNeedAnimation: Boolean = true) {
        val contentFragment = currentTabFragment() ?: return
        val isBottomImmersive = contentFragment.isBottomImmersiveCurrently()

        if (isBottomTranslucentViewShowing != isBottomImmersive) {
            onBottomImmersiveStateChanged(isBottomImmersive)
        }
        if ((isBottomTranslucentViewShowing == isBottomImmersive) && !isForceStart) return
        isBottomTranslucentViewShowing = isBottomImmersive

        registerAnimElement(isBottomImmersive, isNeedAnimation)

        startBottomImmersiveTint(isBottomImmersive, isNeedAnimation)
    }

    private fun registerAnimElement(isBottomImmersive: Boolean, needAnimation: Boolean) {
        val contextNonNull = context ?: return
        val lifecycleOwner = this@TabFragment

        bottomTintElements.forEach { it.unregister() }
        bottomTintElements.clear()

        // 底部沉浸式蒙层透明度动画
        bottomTranslucentView?.takeIf { !isTopOrBottomAnimating() }?.let {
            bottomTintElements.add(
                TintTranslucentElement(
                    translucentView = it,
                    changer = directionAnimDriver,
                    isNeedAnimation = needAnimation,
                    shouldTranslucentVisible = isBottomImmersive && !isSelectionMode
                ).apply {
                    register(lifecycleOwner)
                }
            )
        }

        getBottomViewsToBlur().forEach { blurViewWrapper ->
            val blurLayer = blurViewWrapper.blurLayerView
            // 模糊按钮背景着色
            bottomTintElements.add(
                TintBlurDrawableElement(contextNonNull, blurLayer.background, directionAnimDriver).apply {
                    register(lifecycleOwner)
                }
            )

            val button = blurViewWrapper.getButton<MultiLayerImageView>() ?: return@forEach
            // 模糊按钮 icon 内容着色
            button.getLayerIcon()?.let { ic ->
                bottomTintElements.add(
                    TintMainTabIconDrawableElement(ic, lightNightAnimDriver, isDarkMode) {
                        blurViewWrapper.isVisible && blurViewWrapper.isEnabled.not()
                    }.apply {
                        register(lifecycleOwner)
                    }
                )
            }
        }
    }

    /**
     * 开始底部区域沉浸式着色动画, 由子 Fragment（时间轴、图集）触发
     */
    override fun startBottomImmersiveTint(isBottomImmersive: Boolean, needAnimation: Boolean) {
        val contextNonNull = context ?: return
        refreshSegmentButtonTextColor(contextNonNull, isBottomImmersive)
        bottomTabSegmentButton?.let { updateSegmentDrawDelegate(it) }

        context?.let {
            lightNightAnimDriver.start(it, !isBottomImmersive, needAnimation)
            directionAnimDriver.start(!isBottomImmersive, needAnimation)
        }
    }

    /**
     * 检查底部Tab按钮是否禁用
     */
    override fun refreshBottomButtonDisableState(isPageEmpty: Boolean, isPersonalFiltered: Boolean, isFilterMenuShowed: Boolean) {
        switchButtonWrapperView?.apply {
            isEnabled = (isPageEmpty.not()) && (isPersonalFiltered.not())
            getButton<MultiLayerImageView>()?.getLayerIcon()?.tintOriginalDrawable(getIconTintColor())
        }

        if (isFilterMenuShowed) return

        filterButtonWrapperView?.apply {
            isEnabled = (isPageEmpty.not()) || (isPageEmpty && isPersonalFiltered)
            getButton<MultiLayerImageView>()?.getLayerIcon()?.tintOriginalDrawable(getIconTintColor())
        }
    }

    private fun View.getIconTintColor(): Int {
        val isBottomImmersive = currentTabFragment()?.isBottomImmersiveCurrently() ?: false
        return when {
            !this.isEnabled -> tintColorDisable
            isBottomImmersive || isDarkMode -> tintColorWhite
            else -> tintColorBlack
        }
    }

    private fun isDark(context: Context): Boolean {
        val currentNightMode = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return currentNightMode == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 底部沉浸式状态发生变化, 由子 Fragment（时间轴、图集）触发
     */
    override fun onBottomImmersiveStateChanged(isBottomImmersive: Boolean) {
        refreshViewBlur(refreshTop = false)
    }

    override fun isToolBarAnimating(): Boolean = mainToolBarFadeAnimator?.isAnimating() ?: false

    override fun getToolbar(): MainTabToolbar? = mainTabToolbar

    /**
     * 刷新底部分段按钮的文字颜色
     */
    private fun refreshSegmentButtonTextColor(context: Context, isBottomImmersive: Boolean) {
        bottomTabSegmentButton?.apply {
            /**
             * bottomTabSegmentButton 底部布局使用 PreInflateViewHelper.getCacheViewOrInflate 进行了加载线程优化
             * 这里通过 segmentCount 判断公共的 COUISegmentButtonLayout 是否已经初始化，避免异常
             */
            if (segmentCount == 0) return@apply
            setSegmentSelectedTextColor(
                context.getColor(
                    when {
                        COUIDarkModeUtil.isNightMode(context) -> BasebizR.color.base_white
                        isBottomImmersive -> BasebizR.color.base_blur_segment_selected_button_text_color
                        else -> BasebizR.color.base_blur_disable_segment_selected_button_text_color
                    }
                )
            )
            setSegmentUnselectedTextColor(
                context.getColor(
                    when {
                        COUIDarkModeUtil.isNightMode(context) -> BasebizR.color.base_white
                        isBottomImmersive -> BasebizR.color.base_blur_segment_unselected_button_text_color
                        else -> BasebizR.color.base_blur_disable_segment_unselected_button_text_color
                    }
                )
            )
        }
    }

    private fun initTimelineTabStateCallback() {
        buttonStateCallback = fragmentAdapter.getFragment(TAB_INDEX_FIRST) as? ITimelineTabButtonStateCallback
    }

    /**
     * 获取底栏需要沉浸式模糊效果的 View 集合, 包括分段导航栏
     */
    private fun getBottomViewsToBlur() = listOfNotNull(
        filterButtonWrapperView,
        switchButtonWrapperView,
        sortButtonWrapperView,
        albumAddButtonWrapperView,
        bottomSegmentWrapperView
    )

    /**
     * 更新底部tab导航栏layout，如果需要
     *
     * - 针对侧边栏展开/收起时，布局变化
     */
    private fun updateSegmentNavigationLayoutIfNeed() {
        if (sidePaneAccessor.isSupportSidePane().not()) {
            GLog.w(TAG, LogFlag.DL) { "[updateSegmentNavigationLayoutIfNeed] SidePane not supported!" }
            return
        }
        GLog.d(TAG, LogFlag.DL) {
            "[updateSegmentNavigationLayoutIfNeed] SidePane isVisible = ${sidePaneAccessor.isVisible()}, " +
                    "isOpen = ${sidePaneAccessor.isOpen()}"
        }
        // 侧边栏是否可见&展开
        val isSidePaneOpen = sidePaneAccessor.isVisible() && sidePaneAccessor.isOpen()
        bottomSegmentWrapperView?.visibility = if (isSidePaneOpen) View.GONE else View.VISIBLE
        checkBottomTabRightVisibility()

        bottomTabSegmentNavigationView?.apply {
            this.gravity = if (isSidePaneOpen) Gravity.CENTER_VERTICAL or Gravity.END else Gravity.CENTER
            if (ResourceUtils.isRTL(context)) {
                updatePadding(left = if (isSidePaneOpen) segmentLayoutPaddingHorizontal else 0)
                bottomTabSegmentRight?.updateMargin(right = if (isSidePaneOpen) segmentIvButtonMarginHorizontal else 0)
            } else {
                updatePadding(right = if (isSidePaneOpen) segmentLayoutPaddingHorizontal else 0)
                bottomTabSegmentRight?.updateMargin(left = if (isSidePaneOpen) segmentIvButtonMarginHorizontal else 0)
            }
        }
    }

    /**
     * 检查是否显示底部Tab的右侧区域
     */
    private fun checkBottomTabRightVisibility() {
        // 支持精选 || 不支持侧边栏 ，默认显示，直接返回
        if (isSeniorPickedSupported || sidePaneAccessor.isSupportSidePane().not()) return

        val isTimeLineTab = currentTabIndex != TAB_INDEX_SECOND
        val isSidePaneOpen = sidePaneAccessor.isVisible() && sidePaneAccessor.isOpen()

        // 时间轴页&&侧边栏展开 不显示，其余显示
        bottomTabSegmentRight?.isVisible = (isTimeLineTab && isSidePaneOpen).not()
    }

    /**
     * 分段按钮只有照片和图集两个Tab，选中照片时tab时，两侧为 筛选和年月日视图切换，选中图集tab时，两侧为图集排序和新建图集
     *
     * @Note
     * 1. 筛选按钮: 照片页 && 非精选视图 -> 显示，否则不显示;
     * 2. 视图切换按钮： 照片页 && 非精选视图（精选视图会显示视图切换高亮按钮） -> 显示，否则不显示;
     * 3. 不支持精选视图: 屏蔽视图切换按钮
     */
    private fun updateSegmentIvButton(curPosition: Int) {
        checkBottomTabRightVisibility()
        val isFirstTab = curPosition == TAB_INDEX_FIRST
        filterButtonWrapperView?.isVisible = (isFirstTab && (buttonStateCallback?.isPickedPresentation() == false))
        timelineFilterHighlightButton?.isVisible =
            (isFirstTab && (buttonStateCallback?.isPickedPresentation() == false) && (filterButtonWrapperView?.isHighlight() == true))
        switchButtonWrapperView?.isVisible =
            isSeniorPickedSupported && isFirstTab && (buttonStateCallback?.isPickedPresentation() == false)
        timelinePresentationSwitchHighlightButton?.isVisible =
            isSeniorPickedSupported && isFirstTab && (switchButtonWrapperView?.isHighlight() == true)
        sortButtonWrapperView?.visibility = if (isFirstTab.not()) View.VISIBLE else View.INVISIBLE
        albumAddButtonWrapperView?.visibility = if (isFirstTab.not()) View.VISIBLE else View.INVISIBLE
    }

    private fun updateTopBottomMaskColor(curPosition: Int) {
        val contextNonNull = context ?: return
        val isFirstTab = curPosition == TAB_INDEX_FIRST
        val isGradientBlurDisabled = BackgroundBlurUtils.isGradientBlurEnable().not()
        if (isFirstTab) {
            val isDarkAndGradientBlurDisabled = isGradientBlurDisabled && COUIDarkModeUtil.isNightMode(contextNonNull)
            topTranslucentView?.setBackgroundResource(
                if (isDarkAndGradientBlurDisabled) {
                    // 按照UX要求，目前仅照片页【暗色&不支持移轴模糊】这种情况，TopMask透明度单独设置，其余先维持现状
                    BasebizR.drawable.base_photo_top_mask_repeat_blur_disable
                } else {
                    BasebizR.drawable.base_photo_top_mask_repeat
                }
            )
            bottomTranslucentView?.setBackgroundResource(BasebizR.drawable.base_photo_bottom_mask_repeat)
        } else {
            if (isGradientBlurDisabled) {
                topTranslucentView?.setBackgroundResource(BasebizR.drawable.base_album_top_mask_repeat_not_support_blur)
                bottomTranslucentView?.setBackgroundResource(BasebizR.drawable.base_album_bottom_mask_repeat_not_support_blur)
            } else {
                topTranslucentView?.setBackgroundResource(BasebizR.drawable.base_album_top_mask_repeat)
                bottomTranslucentView?.setBackgroundResource(BasebizR.drawable.base_album_bottom_mask_repeat)
            }
        }
    }

    /**
     * 记录第一帧的绘制时间
     */
    private fun recordFirstDrawTime() {
        if (RealShowTimeInstrument.DEBUG.not()) return
        val segmentButton = bottomTabSegmentButton ?: return
        segmentButton.viewTreeObserver.addOnDrawListener(object : ViewTreeObserver.OnDrawListener {
            override fun onDraw() {
                if (isFirstDraw) {
                    RealShowTimeInstrument.endRecord(clazzSimpleName)
                    isFirstDraw = false
                }
                AppScope.launch<Any?>((Dispatchers.Main as CoroutineContext), CoroutineStart.DEFAULT, {
                    segmentButton.viewTreeObserver.removeOnDrawListener(this)
                    null
                })
            }
        })
    }

    override fun enterSelectionMode(): Int {
        val id = (fragmentAdapter.getFragment(currentTabIndex) as? ISelectionModeCallback)?.enterSelectionMode() ?: SelectionData.INVALID_ID
        if (id != SelectionData.INVALID_ID) {
            // tab页进入编辑模式时toolbar也需要进入编辑模式
            setSelectionMode(selectionMode = true)
        }
        return id
    }

    override fun exitSelectionMode() {
        (fragmentAdapter.getFragment(currentTabIndex) as? ISelectionModeCallback)?.exitSelectionMode()
        setSelectionMode(selectionMode = false)
    }

    override fun isInSelectionMode() = isSelectionMode

    /**
     * @param backFromItem 是否是点击了SidePaneEventType.ITEM_CLICK，又点回SidePaneEventType.HEADER_CLICK
     * 中大屏下，此值为true时，不执行onClickSelectedTabAgain()
     *
     * - 此回顶逻辑针对16.0改版前，只能在侧边栏展开状态下点击切换Tab页的回顶逻辑，改版后，侧边栏收起状态下，可以点击，
     * 底部的Tab按钮切换，但无需此回顶逻辑，因为底部Tab按钮有自己的双击回顶逻辑；
     *
     * -- 目前只能通过侧边栏状态进行判断区分：侧边栏Close状态下，此方法的回调必然来自底部Tab切换后同步状态给侧边栏，非侧边栏Tab点击,可以直接忽略其回顶逻辑，
     * 见[canScrollToTop]；
     */
    fun refresh(index: Int, backFromItem: Boolean) {
        GLog.d(TAG, LogFlag.DL) {
            "[refresh] index:$currentTabIndex->$index ,backFromItem = $backFromItem , createViewPager=${this::viewPager.isInitialized}"
        }
        if (this::viewPager.isInitialized) {
            if (currentTabIndex == index) {
                val contextNonNull = context ?: return
                if (canScrollToTop(contextNonNull, backFromItem)) {
                    (fragmentAdapter.getFragment(currentTabIndex) as? IClickSelectedTabAgainCallback)?.onClickSelectedTabAgain()
                }
                return
            }
            // 在切换之前当前fragment先退出选择模式
            exitSelectionMode()
            viewPager.setCurrentItem(index, false)
        } else {
            currentTabIndex = index
        }
    }

    /**
     * 检查是否需要回顶
     *
     * - 目前仅 [refresh] 中调用，其他地方慎用
     */
    private fun canScrollToTop(context: Context, backFromItem: Boolean): Boolean {
        if (backFromItem) return false

        // 此条件针对16.0改版后：侧边栏收起后，[refresh]方法的回调必然来自底部Tab切换后同步状态给侧边栏,非侧边栏Tab点击,可以直接忽略其回顶逻辑
        val isSidePaneOpen = sidePaneAccessor.isVisible() && sidePaneAccessor.isOpen()

        val isMiddleAndLargeScreen = ScreenUtils.isMiddleAndLargeScreen(context)
        val isCurrentTabVisible = fragmentAdapter.getFragment(currentTabIndex)?.isVisible ?: false
        return isSidePaneOpen && isCurrentTabVisible && isMiddleAndLargeScreen
    }

    private fun initTabSegmentButtonClickListeners() {
        val segmentCount = bottomTabSegmentButton?.segmentCount
        if (segmentCount != null && segmentCount > 0) {
            for (index in 0 until segmentCount) {
                bottomTabSegmentButton?.getSegmentAt(index)?.let {
                    // 单击
                    setTabSegmentItemViewClickListener(it)

                    // 长按
                    if (isSupportSafeBoxAlbum()) {
                        setTabSegmentItemViewLongClickListener(it)
                    }
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setTabSegmentItemViewClickListener(segmentItemView: TextView) {
        /**
         * 点击 COUISegmentButtonLayout 的Item，点击事件执行顺讯如下：
         *      单击事件 -> selectionChange -> isSelectd = true -> onCLick
         *
         * COUI给的解决方案是在ACTION_DONN时记录选择状态，根据选择状态处理业务逻辑
         */
        segmentItemView.setOnTouchListener { view, event ->
            when (event.action) {
                // 记录按下时选中状态
                MotionEvent.ACTION_DOWN -> view.tag = view.isSelected
            }
            // 不拦截，让事件继续传递
            false
        }
        segmentItemView.setOnClickListener { view ->
            val hasSelectedOnDown = view?.tag as? Boolean ?: false
            if (hasSelectedOnDown.not()) return@setOnClickListener

            // 按下时，已经是选中状态，才执行 onClick
            fragmentAdapter.getFragment(currentTabIndex)?.onClickSelectedTabAgain()
        }
    }

    private fun setTabSegmentItemViewLongClickListener(segmentItemView: TextView) {
        segmentItemView.isHapticFeedbackEnabled = true
        segmentItemView.setOnLongClickListener { view ->
            if (view.isSelected.not()) return@setOnLongClickListener false
            if (!isSupportUserCustomSafeBox()) {
                ToastUtil.showShortToast(BasebizR.string.base_feature_is_disabled_tip)
            } else if (!isSelectionMode) {
                SafeBoxHelper.startSafeBox(activity, ALBUMS_ACTION_ENTER_SAFE_BOX_FROM_BOTTOM_TAB)
            }
            true
        }
    }

    private fun initToolbar() {
        mainTabToolbar?.let {
            toolBarMarginAdjust?.bindView(it)
        }
        setupToolbar(isBackEnabled = false, topPaddingType = ToolbarHelper.TYPE_TAB, isSupportImmersive = true)
        activity?.setTitle(BasebizR.string.base_toolbar_default_title)
        (activity as? AppCompatActivity)?.supportActionBar?.setDisplayHomeAsUpEnabled(false)
        mainTabToolbar?.apply {
            searchButtonWrapperView.setOnClickListenerWithPressFeedback { currentTabFragment()?.onTopSearchClick() }
            cancelButtonWrapperView.setOnClickListenerWithPressFeedback { currentTabFragment()?.onTopCancelClick() }
            overflowButtonWrapperView.setOnClickListenerWithPressFeedback { currentTabFragment()?.onTopOverflowClick() }
            statusBarHeight()
        }
    }

    override fun onResume() {
        super.onResume()
        ///检查一下是否要弹框，存在下载了没有弹起 然后页面被杀掉的情况， onResume里面没有执行弹起动作
        restrictWatermarkPopupHelper?.checkWatermarkPopupIsShow()
        if (isColdLaunch.not()) {
            enablePreLoadPage()
        }
        context?.getAppAbility<ITaskManageAbility>()?.use {
            it.switchToForeground(this)
        }
        downloadRestrictWatermark()
        bindToolbarToActivity()
        refreshNavigationBarColor()
        restoreAccessibility(disableAccessibilityViews)

        if (TimelineUtils.DRAW_DEBUG == TimelineUtils.DRAW_DEBUG_RECT) {
            TimelineUtils.refreshDebugCheckExtraWidthHeight()
        }
        SceneDispatcher.sendScene(ENTER_MAIN_TAB)
        checkViewPagerWhenEnterFg = false
    }

    /**
     * 限定水印元数据及资源下载
     */
    private fun downloadRestrictWatermark() {
        context?.let {
            lifecycleScope.launch(Dispatchers.IO) {
                RestrictWatermarkDownloadHelper.downloadRestrictWatermark(it)
            }
        }
    }

    /**
     * 获取当前的 TabFragment
     */
    private fun currentTabFragment() =
        ((fragmentAdapter.getFragment(currentTabIndex))?.takeIf { it.isAdded && it.context != null } as? ITabContentFragment)

    override fun onBackPressed(): Boolean {
        return if (this::fragmentAdapter.isInitialized && !fragmentAdapter.getFragment(viewPager.currentItem)!!.onBackPressed()) {
            false
        } else {
            super.onBackPressed()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        childFragmentManager.fragments.forEach {
            it.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun setSelectionMode(selectionMode: Boolean) {
        GLog.d(TAG) { "setSelectionMode isSelectionMode:$isSelectionMode selectionMode:$selectionMode" }
        // BugId:9450559, 极限操作，进入选择模式，禁用底栏分段按钮(分段按钮禁用不会自带置灰效果)
        bottomTabSegmentButton?.isEnabled = selectionMode.not()

        if (isSelectionMode == selectionMode) return
        val contentFragment = currentTabFragment() ?: return
        val isTopImmersive = contentFragment.isTopImmersiveCurrently()
        val isBottomImmersive = contentFragment.isBottomImmersiveCurrently()

        isSelectionMode = selectionMode
        refreshNavigationBarColor()

        // 进入选择模式时，主页不支持滑动；退出选择模式时,若是无侧边栏,则需要支持左右滑动(底部有Tab栏),若是有侧边栏,则不需要支持(底部无Tab栏)
        setViewPagerSlideEnabled(!selectionMode && !sidePaneAccessor.isVisible())

        // 从分享返回后弹窗等场景，如选中照片分享到便签，此时页面不在 resume 状态执行不了动画，会导致弹窗消失前，页面一直停留在选择模式状态，此时应该直接把动画调整到结束态
        val endAnimImmediately = !isInForeground()

        mainToolBarFadeAnimator?.cancelAnim()
        mainToolBarFadeAnimator = mainTabToolbar?.let {
            MainToolBarFadeOutAnimator(it, isTopImmersive, selectionMode).apply {
                cancelAnim()
                setFadeOutEndListener {
                    updateToolbar(force = endAnimImmediately)
                    checkToolbarTint(position = 0, isForceStart = true, isNeedAnimation = false, skipForegroundCheck = true)
                }
                setFadeInEndListener {
                    mainTabToolbar?.apply {
                        setTitle(contentFragment.getToolbarTitle())
                        searchButtonWrapperView.isVisible = (selectionMode.not()) && (contentFragment.isShowEmptyPage().not())
                    }
                    // 动画过程中上下滑动列表触发沉浸式变化，会导致着色和模糊被动画覆盖，因此动画结束后需要重新检查
                    checkToolbarTint(position = 0, isForceStart = true, isNeedAnimation = false, skipForegroundCheck = true)
                    refreshViewBlur(refreshBottom = false)
                }
                if (endAnimImmediately) jumpToEndState() else start()
            }
        }

        // 底部导航栏和工具栏替换动画，需要注意 tabContentFragment 对应的是子 Fragment 实例(时间轴/图集)
        fragmentAdapter.getFragment(viewPager.currentItem)?.let { tabContentFragment ->
            tabContentFragment.bottomMenuBar?.menu?.findItem(BasebizR.id.action_encrypt)?.isVisible = isSupportSafeBoxAlbum()

            val bottomButtonsContainer = bottomTabSegmentNavigationView ?: return@let
            val bottomButtons = getBottomAnimButton()
            val bottomMenuBar = tabContentFragment.bottomMenuBar ?: return@let

            if (tabContentFragment !is ITabContentFragment) return@let
            val bottomMenuBarContainer = tabContentFragment.getBottomMenuBarContainer() ?: return@let
            bottomReplacementAnimator?.cancelAnim()
            bottomReplacementAnimator = BottomReplacementAnimator(
                bottomButtons,
                bottomButtonsContainer,
                bottomMenuBar,
                bottomMenuBarContainer,
                selectionMode,
                isBottomImmersive
            ).apply {
                setFadeInEndListener {
                    // 动画过程中上下滑动列表触发沉浸式变化，会导致着色和模糊被动画覆盖，因此动画结束后需要重新检查
                    checkBottomImmersiveTint(isForceStart = true, isNeedAnimation = false)
                    refreshViewBlur(refreshTop = false)
                }
                if (endAnimImmediately) jumpToEndState() else start()
            }
        }

        doBottomTranslucentAnim(selectionMode, isBottomImmersive)
    }

    /**
     * 底部蒙层跟随选择模式切换的出现消失动画
     */
    private fun doBottomTranslucentAnim(selectionMode: Boolean, isBottomImmersive: Boolean) {
        bottomTranslucentAnim?.cancel()
        if (!isBottomImmersive) {
            return
        }
        bottomTranslucentAnim = if (selectionMode) {
            MainSelectionSpringAnimHelper.createFadeOutSpringAnim()
        } else {
            MainSelectionSpringAnimHelper.createFadeInSpringAnim()
        }.apply {
            duration = BOTTOM_TRANSLUCENT_ANIM_DURATION
            addUpdateListener {
                bottomTranslucentView?.alpha = (it.animatedValue as? Float) ?: 0f
            }
            doOnStart {
                if (!selectionMode) {
                    bottomTranslucentView?.alpha = 0f
                    bottomTranslucentView?.isVisible = true
                }
            }
            start()
        }
    }

    /**
     * 获取选择模式切换时，底栏渐变动画的目标按钮
     */
    private fun getBottomAnimButton(): List<BlurButtonWrapperView> {
        // 是否支持侧边栏且是展开状态
        val isSidePaneOpen = sidePaneAccessor.isSupportSidePane() && sidePaneAccessor.isVisible() && sidePaneAccessor.isOpen()

        // 是否处于精选视图（精选视图，筛选和精选按钮会隐藏，显示精选高亮按钮显示）
        val isInPicked = buttonStateCallback?.isPickedPresentation() ?: false
        val list = mutableListOf<BlurButtonWrapperView?>()
        if (isSidePaneOpen.not()) {
            list.add(bottomSegmentWrapperView)
        }
        if (currentTabIndex == TAB_INDEX_FIRST) {
            if (isSeniorPickedSupported && isInPicked.not()) {
                list.add(switchButtonWrapperView)
            }
            if (isInPicked.not()) {
                list.add(filterButtonWrapperView)
            }
        }
        if (currentTabIndex == TAB_INDEX_SECOND) {
            list.add(albumAddButtonWrapperView)
            list.add(sortButtonWrapperView)
        }
        return list.filterNotNull()
    }

    override fun updateToolbar(force: Boolean) {
        if (!force) {
            if (isToolBarAnimating() || sidePaneAccessor.isSliding() || !isResumed) {
                return
            }
            if (mainTabToolbar?.isInLayout == true) {
                // toolbar 在布局中，直接更新可能会导致标题的高度没有正确更新
                return
            }
        }
        val currentTabFragment = currentTabFragment() ?: return
        val isSelectMode = isInSelectionMode()
        mainTabToolbar?.apply {
            setTitle(currentTabFragment.getToolbarTitle())
            searchButtonWrapperView.isVisible = (isSelectMode.not()) && (currentTabFragment.isShowEmptyPage().not())
            overflowButtonWrapperView.isVisible = isSelectMode.not()
            cancelButtonWrapperView.isVisible = isSelectMode
            val subtitle = currentTabFragment.getToolbarSubtitle()
            val subtitleSize = resources.getDimensionPixelSize(
                if (isSelectionMode) {
                    BasebizR.dimen.main_tab_toolbar_subtitle_text_size_select_mode
                } else {
                    BasebizR.dimen.main_tab_toolbar_subtitle_text_size_normal
                }
            ).toFloat()
            // 更新标题、子标题和地址信息
            setSubtitle(subtitle)
            setSubtitleVisible(subtitle.isNotBlank())
            setSubTextSize(subtitleSize)
            /*  设置按钮内部的对齐方式，目前2种方式。
             *  1，沉浸式，改为居上对齐。
             *  2，日期模式，改为居中对齐。 */
            val gravity = if (TimelinePreLoader.isImmersivePresentation.get() == true) Gravity.TOP else Gravity.CENTER
            setButtonsGravity(gravity)
            // 更新标题样式，注意不要随意setTextColor，以免造成文本颜色切换动画跳闪
            setTitleTextAppearanceType(currentTabFragment.getToolbarTitleAppearanceType())
        }
    }

    override fun checkPageImmersiveState(position: Int, isForceStart: Boolean, isNeedAnimation: Boolean) {
        GTrace.traceBegin("$TAG.checkPageImmersiveState")
        if (fragmentAdapter.getFragment(currentTabIndex)?.isAdded == false) {
            return
        }
        if (sidePaneAccessor.isSliding()) {
            return
        }
        if (sidePaneAccessor.isClose()) {
            currentTabFragment()?.let {
                sidePaneAccessor?.setControlIconTint(!it.isTopImmersiveCurrently())
            }
        }
        // 冷启动阶段不需要动画，直接出现就可以了
        val needAnim = isNeedAnimation && !isDarkMode && isColdLaunch.not()
        checkToolbarTint(position, isForceStart, needAnim)
        checkBottomImmersiveTint(isForceStart, needAnim)
        GTrace.traceEnd()
    }

    override fun currentShouldToolbarTint(): Boolean = currentTabFragment()?.isTopImmersiveCurrently() ?: false

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        if (isResumed) {
            fragmentAdapter.getFragment(currentTabIndex)?.currentShouldToolbarTint()?.let { shouldToolbarTint ->
                sidePaneAccessor.adjustControlIconColor(context, shouldToolbarTint)
            }
        }
    }

    override fun getToolbarBottomY(): Int = mainTabToolbar?.bottom ?: 0

    /**
     * 刷新页面模糊状态
     */
    private fun refreshViewBlur(refreshTop: Boolean = true, refreshBottom: Boolean = true) {
        val contentFragment = currentTabFragment() ?: return
        val isBottomImmersive = contentFragment.isBottomImmersiveCurrently()
        val isTopImmersive = contentFragment.isTopImmersiveCurrently()

        if (refreshBottom) {
            getBottomViewsToBlur().forEach {
                it.refreshBlur(isImmersive = isBottomImmersive)
                it.blurLayerView.requestLayout()
            }
        }
        if (!refreshTop) {
            return
        }

        mainTabToolbar?.apply {
            listOfNotNull(
                searchButtonWrapperView,
                overflowButtonWrapperView,
                cancelButtonWrapperView
            ).forEach {
                it.refreshBlur(isImmersive = isTopImmersive)
                it.blurLayerView.requestLayout()
            }
        }

        topBlurMask?.initMaskBlur(isTopImmersive)
    }

    /**
     *  纠正viewpager当前tab内容显示不全或显示两个tab的内容
     * 问题现象：
     *
     * 1.大屏横屏-图集tab--点击进入图集详情页--切换竖屏--返回图集tab，会显示两tab的内容，即显示图集tab和照片或发现tab的内容
     *
     * 2.大屏横屏-图集tab--点击进入图集详情页--切换竖屏-切小屏-小屏横竖屏--返回图集tab，会显示两tab的内容，即显示图集tab和照片或发现tab的内容
     *
     * 3.RTl模式下,侧边栏展开收起时,屏幕内也会显示两个tab的内容
     *
     * 4.照片页，熄屏，放置一段时间（发现页被回收，但照片页、图集页都没回收），亮屏,屏幕内也会显示两个tab的内容
     *
     * 5.bugId:9339936:外销机:竖屏-16.0图集tab--滑动到我的图集分组--随机点击某个图集进详情--切换到横屏--返回,图集tab闪现时间轴
     */
    private fun correctViewPagerPosition(from: String) {
        val recyclerView = viewPager.getRecyclerView()
        GLog.d(TAG, LogFlag.DL) { "correctViewPagerPosition:cur=${viewPager.currentItem},from=$from,rv=$recyclerView" }
        recyclerView?.scrollToPosition(viewPager.currentItem)
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : FragmentSystemBarStyle(this) {
        override fun onInit() {
            setMockNaviBarEnable(true)
            refreshNavigationBarColor()
        }

        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            mainTabToolbar?.apply {
                layoutParams = (layoutParams as? LayoutParams)?.apply {
                    setMargins(leftMargin, statusBarHeight(), rightMargin, bottomMargin)
                }
            }
            setNaviBarColor(Color.TRANSPARENT)
            refreshNavigationBarColor()
            windowInsets.naviBarInsets().apply {
                // 底部沉浸显示
                setContentPadding(left, 0, right, 0)

                // 底部Tab弥补沉浸
                bottomTabSegmentNavigationView?.updateMargin(
                    bottom = when {
                        // 无虚拟键（全面屏手势） && 手势导航条显示：导航条高度 + 设计额外增加高度
                        isFullScreenGesture -> bottom + resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_bar_margin_bottom_extra)

                        // 无虚拟键（全面屏手势） && 手势导航条隐藏
                        isFullScreenGestureAndNoBar -> resources.getDimensionPixelSize(BasebizR.dimen.main_tab_bottom_bar_margin_bottom)

                        // 其他：导航条高度
                        else -> bottom
                    }
                )
            }
        }
    }

    override fun onPause() {
        super.onPause()
        isColdLaunch = false
        // tab页处于后台时，减少离屏加载的数量，减轻负载，避免争抢上层页面的资源
        disablePreLoadPage()
        context?.getAppAbility<ITaskManageAbility>()?.use {
            it.switchToBackground(this)
        }
        disableAccessibility((view as? ViewGroup), disableAccessibilityViews)
    }

    override fun onDestroy() {
        super.onDestroy()
        context?.getAppAbility<ITaskManageAbility>()?.use {
            it.unregister(this)
        }
        bottomReplacementAnimator?.cancelAnim()
        mainToolBarFadeAnimator?.cancelAnim()
        bottomTranslucentAnim?.cancel()

        lightNightAnimDriver.cancel()
        directionAnimDriver.cancel()
    }

    private fun ViewPager2.getRecyclerView(): RecyclerView? {
        return getChildAt(0) as? RecyclerView
    }

    private fun isSupportSafeBoxAlbum(): Boolean {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM)
    }

    private fun isSupportUserCustomSafeBox(): Boolean {
        return ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX)
    }

    override fun onStop() {
        super.onStop()
        checkViewPagerWhenEnterFg = true
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        if (checkViewPagerWhenEnterFg && uiConfig.windowWidth.isChanged()) {
            correctViewPagerPosition("onAppUiStateChanged")
        }
    }

    override fun createSidePaneListener(): ISidePaneListener = this

    override fun onSidePaneStateChanged(newState: SidePaneState, oldState: SidePaneState) {
        setViewPagerSlideEnabled(!isSelectionMode && !newState.isVisible())
        correctViewPagerPosition("onSidePaneStateChanged")
        refreshNavigationBarColor()
        updateSegmentNavigationLayoutIfNeed()
    }

    /**
     * 沉浸后，除了图集选择模式以外，虚拟导航栏背景都是透明
     */
    private fun refreshNavigationBarColor() {
        if (!isResumed) return
        if ((fragmentAdapter.getFragment(currentTabIndex) is TimelineTabFragment)) {
            setMockNaviBarColor(Color.TRANSPARENT)
            return
        }
        context?.let {
            setMockNaviBarColor(if (isSelectionMode) it.getColor(com.support.appcompat.R.color.coui_color_bottom_bar) else Color.TRANSPARENT)
        }
    }

    /**
     * 启用或禁用viewpager左右滑动
     *  场景1：进入选择模式时，主页不支持滑动；
     *  场景2：退出选择模式时,若是无侧边栏,则需要支持左右滑动(底部有Tab栏),若是有侧边栏,则不需要支持(底部无Tab栏)
     *  场景3：大屏-图集tab-进入选择模式-切小屏或分屏，图集tab仍是编辑模式，不可左右滑动切换tab
     */
    private fun setViewPagerSlideEnabled(isEnable: Boolean) {
        viewPager.isUserInputEnabled = isEnable
    }

    private fun View.setOnClickListenerWithPressFeedback(onClick: () -> Unit) {
        setOnClickListener {
            /**
             * 极端操作场景不响应 onClick ：
             * 1. BUG[9378980] 进入选择模式，顶栏/底栏在执行动画时
             * 2. BUG[9568353] 页面正在切换（执行动画中）
             */
            if (DoubleClickUtils.isFastDoubleClick() || isTopOrBottomAnimating() || isPageChangeAnimating) {
                GLog.w(TAG, LogFlag.DL) {
                    "[OnClick] Not invoked for fast double click or " +
                            "isTopOrBottomAnimating = ${isTopOrBottomAnimating()} / isPageChangeAnimating = $isPageChangeAnimating!"
                }
                return@setOnClickListener
            }
            onClick()
        }

        // 增加点击时按压态缩放动效
        COUIPressFeedbackHelper(this, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK).enablePressFeedback(this)
    }

    /**
     * 顶栏/底栏是否在整执行动画
     */
    private fun isTopOrBottomAnimating(): Boolean {
        return (mainToolBarFadeAnimator?.isAnimating() == true) || (bottomReplacementAnimator?.isAnimating() == true)
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        if (isResumed && (context != null) && (ResourceUtils.isRTL(context))) {
            // RTl模式下,侧边栏展开收起时,屏幕内也会显示两个tab的内容,需要重新定位刷新
            correctViewPagerPosition("onSidePaneSliding")
        }
    }

    override fun isSidePaneEnabled(): Boolean = sidePaneAccessor.isSupportSidePane()

    companion object {
        private const val TAG = "TabFragment"

        /**
         * 无效的Tab页索引
         */
        private const val TAB_INDEX_INVALID = -1

        /**
         * 第1个tab页的索引
         */
        private const val TAB_INDEX_FIRST = 0

        /**
         * 第2个tab页的索引
         */
        private const val TAB_INDEX_SECOND = 1

        /**
         * 加载缓存tab页的延迟时间
         */
        private const val LOAD_CACHE_TAB_PAGE_DELAY = 400L

        /**
         * 获取异步加载view缓存的等待时间
         *
         * 冷启动时，我们会预加载view，极端情况下，可能去获取的时候还没有预加载完成，这里会等待300ms
         */
        private const val WAIT_FOR_PRE_INFLATE_TIME_OUT = 300L

        /**
         * 限定水印弹起action
         * */
        private const val RESTRICT_WATERMARK_DOWNLOAD = "oplus.intent.action.gallery3d.RESTRICT_WATERMARK_DOWNLOAD"

        private const val BOTTOM_TRANSLUCENT_ANIM_DURATION = 300L

        private const val ALPHA_255 = 255
        private const val DISABLE_TOUCH_THRESHOLD = 10
    }
}