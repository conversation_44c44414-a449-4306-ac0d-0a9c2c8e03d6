/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SDCardReceiver.kt
 * Description:
 * Version: 1.0
 * Date: 2020/10/13
 ** Author: <EMAIL>
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * <PERSON>Peng@Apps.Gallery3D      2020/11/2      1.0          build this module
 *************************************************************************************************/
package com.coloros.gallery3d.app.globalbroadcast;

import static com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil.SD_MOUNT_RECOMMEND_UPDATING_URI;
import static kotlinx.coroutines.BuildersKt.launch;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.oplus.gallery.business_lib.api.ApiDmManager;
import com.oplus.gallery.business_lib.cache.diskcache.SDCardMediaCacheManager;
import com.oplus.gallery.business_lib.model.config.allowlist.AlbumAllowListConfig;
import com.oplus.gallery.business_lib.model.data.base.utils.SearchSuggestionProviderUtil;
import com.oplus.gallery.business_lib.model.data.base.buckets.BucketIdsCacheHelper;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment;
import com.oplus.gallery.framework.abilities.search.ISearchAbility;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.app.ScopeDomainKt;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import kotlin.coroutines.CoroutineContext;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

public class SDCardReceiver extends BroadcastReceiver {
    private static final String TAG = "SDCardReceiver";

    public static SDCardReceiver register(Context context) {
        SDCardReceiver receiver = new SDCardReceiver();
        IntentFilter filter = new IntentFilter(Intent.ACTION_MEDIA_EJECT);
        filter.addAction(Intent.ACTION_MEDIA_UNMOUNTED);
        filter.addAction(Intent.ACTION_MEDIA_MOUNTED);
        filter.addDataScheme("file");
        BuildersKt.launch(AppScope.INSTANCE, (CoroutineContext) Dispatchers.getIO(), CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    BroadcastDispatcher.INSTANCE.registerReceiver(context, receiver, filter);
                    GLog.d(TAG, "register SDCardReceiver");
                    return null;
                });
        return receiver;
    }

    public static void unregister(Context context, SDCardReceiver receiver) {
        if (receiver != null) {
            BroadcastDispatcher.INSTANCE.unregisterReceiver(context, receiver);
            GLog.d(TAG, "unregister SDCardReceiver");
        }
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        GLog.d(TAG, "onReceive action:" + action);
        ApiDmManager.getScanDM().setSDCardStateChanged(true);
        // add for bug:1100375
        SDCardMediaCacheManager.setSdcardState(OplusEnvironment.getexternalSdState());
        if (Intent.ACTION_MEDIA_EJECT.equals(action) || Intent.ACTION_MEDIA_UNMOUNTED.equals(action)) {
            SDCardMediaCacheManager.clearCacheData();
            notifySdCardChange(false);
        } else if (Intent.ACTION_MEDIA_MOUNTED.equals(action)) {
            notifySdCardChange(true);
        }
    }

    private void notifySdCardChange(boolean isMount) {
        launch(
                AppScope.INSTANCE,
                (CoroutineContext) ScopeDomainKt.getSINGLE_UN_BUSY(Dispatchers.INSTANCE),
                CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    OplusEnvironment.notifySDCardChange();
                    BucketIdsCacheHelper.notifySDCardChange();
                    AlbumAllowListConfig.getInstance().notifyChange();
                    ISearchAbility searchAbility = ((GalleryApplication) ContextGetter.context).getAppAbility(ISearchAbility.class);
                    if (searchAbility != null) {
                        searchAbility.notifyDataDirty(SD_MOUNT_RECOMMEND_UPDATING_URI);
                        IOUtils.closeQuietly(searchAbility);
                    }
                    if (isMount) {
                        DataManager.updateSDCardNotifier();
                    }
                    return null;
                });
    }

}
