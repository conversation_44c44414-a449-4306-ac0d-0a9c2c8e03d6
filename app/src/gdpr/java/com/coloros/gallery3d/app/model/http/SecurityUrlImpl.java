/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SecurityUrlImpl.java
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/11/20
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                      <date>      <version>            <desc>
 ** ------------------------------------------------------------------------------
 ** h<PERSON><PERSON><PERSON>@Apps.Gallery        2020/02/24      1.0          OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.coloros.gallery3d.app.model.http;

import com.oplus.gallery.business_lib.model.http.ISecurityUrl;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.foundation.util.text.TextUtil;

public class SecurityUrlImpl implements ISecurityUrl {

    public static final String TAG = "SecurityUrlImpl";

    public SecurityUrlImpl() {
        GLog.d(TAG, "SecurityUrlImpl, gdpr");
    }

    @Override
    public String getVideoEditorHostName() {
        return FeatureUtils.getVideoEditorUrl();
    }

    @Override
    public String getUserProfileHostName() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getStickerHostName() {
        return FeatureUtils.getStickerUrl();
    }

    @Override
    public String getCloudHostName() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getCloudDns() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getOCloudAiRepairHostName() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getOCloudAiPhotoHostName() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getCloudKitHost() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getCloudKitApiHost() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getAiCloudHostName() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getOfflineCloudAnnounceDomainName() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getSharedDnsHost() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getSharedDefaultHost() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getSharedFaqHost() {
        return TextUtil.EMPTY_STRING;
    }

    @Override
    public String getFilingInfoHost() {
        return TextUtil.EMPTY_STRING;
    }
}
