<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="316dp"
    android:height="336dp"
    android:viewportWidth="316"
    android:viewportHeight="336">
  <group>
    <clip-path
        android:pathData="M0,0h316v336h-316z"/>
    <path
        android:pathData="M0,0h316v336h-316z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="57.65"
            android:startY="-38.59"
            android:endX="282.65"
            android:endY="349.36"
            android:type="linear">
          <item android:offset="0" android:color="#FFFAFAFA"/>
          <item android:offset="1" android:color="#FFF2F3F4"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M0,0h316v336h-316z">
      <aapt:attr name="android:fillColor">
        <gradient
            android:startX="57.65"
            android:startY="-38.59"
            android:endX="282.65"
            android:endY="349.36"
            android:type="linear">
          <item android:offset="0" android:color="#FFFAFAFA"/>
          <item android:offset="1" android:color="#FFF2F3F4"/>
        </gradient>
      </aapt:attr>
    </path>
    <group>
      <clip-path
          android:pathData="M0,0h316v336h-316z"/>
      <path
          android:pathData="M43,30H110V98H43V30Z">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="43"
              android:startY="30"
              android:endX="110"
              android:endY="98"
              android:type="linear">
            <item android:offset="0" android:color="#FFF9F9F9"/>
            <item android:offset="1" android:color="#FFEAF2F6"/>
          </gradient>
        </aapt:attr>
      </path>
      <group>
        <clip-path
            android:pathData="M43,30h67v68h-67z"/>
        <path
            android:pathData="M84.25,39.74C91.42,38 98.65,42.41 100.39,49.58C101.06,52.33 100.47,55.64 99.1,58.78C99.12,58.91 99.14,59.05 99.16,59.18L101.66,78.15C102.35,83.33 98.7,88.09 93.52,88.78C88.33,89.46 83.57,85.81 82.89,80.63L81.62,70.97L79,71.55C77.18,71.95 75.4,70.73 75.12,68.89L74.35,63.89L72.02,61.92C71.66,61.61 71.56,61.09 71.79,60.67L74.4,55.86H74.41C72.68,48.7 77.08,41.48 84.25,39.74Z"
            android:fillColor="#DDE7EE"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M66.56,111.96L74.5,158.37L126.73,151.48L122.34,74.77L61.57,82.78L61.57,82.78C57.16,83.29 53.52,86.31 52.09,90.36L29.69,140.03L47.79,148.21L66.56,111.96Z"
            android:fillColor="#DDE5EB"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M113.33,153.45L89.38,156.61L79.33,80.4L103.37,77.27L113.33,153.45Z"
            android:fillColor="#EDF2F9"/>
        <path
            android:pathData="M87.47,39.17C93.36,39.23 98.62,43.25 100.22,48.91C101.41,53.08 100.53,57.18 98.33,60.35L95.46,58.14C96.28,57.41 96.8,56.34 96.8,55.15C96.8,52.95 95.01,51.16 92.81,51.16C90.6,51.16 88.82,52.95 88.82,55.15C88.82,55.88 89.01,56.56 89.35,57.15L89.23,57.17L86.46,57.53L86.11,54.25C86.05,53.65 85.71,53.12 85.19,52.81C84.54,52.43 84.18,51.69 84.27,50.94L84.95,45.28L75.23,46.57C77.45,42.2 82,39.12 87.47,39.17Z"
            android:fillColor="#C1CBD6"
            android:fillType="evenOdd"/>
      </group>
      <path
          android:pathData="M179,98H316V166H179V98Z">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="229.5"
              android:startY="166"
              android:endX="263"
              android:endY="91"
              android:type="linear">
            <item android:offset="0" android:color="#B2E6EFF5"/>
            <item android:offset="1" android:color="#00E6EFF5"/>
          </gradient>
        </aapt:attr>
      </path>
      <group>
        <clip-path
            android:pathData="M179,98h137v68h-137z"/>
        <path
            android:pathData="M242.51,126.23C242.51,119.07 236.7,113.26 229.54,113.26C222.37,113.26 216.56,119.07 216.56,126.24C216.56,129.6 218.08,133.37 220.44,136.43C220.31,136.98 220.25,137.55 220.25,138.14L220.25,151.13C220.25,155.47 223.76,158.99 228.11,158.99C232.45,158.99 235.98,155.47 235.98,151.13L235.98,142.92L240.01,143.28C241.81,143.44 243.37,142.03 243.4,140.23L243.5,135.31L245.49,133.13C245.81,132.78 245.84,132.26 245.57,131.88L242.44,127.59C242.48,127.15 242.51,126.69 242.51,126.23Z"
            android:fillColor="#D6DFE9"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M210.96,123.12C213.07,123.12 215.03,122.5 216.68,121.43C216.18,122.84 215.9,124.35 215.9,125.93C215.9,129.43 217.5,133.37 219.98,136.54C221.29,135.64 222.47,134.57 223.49,133.36C223.5,133.36 223.51,133.36 223.51,133.36C223.06,132.71 222.8,131.92 222.8,131.07C222.8,128.82 224.62,127 226.87,127C229.11,127 230.93,128.82 230.93,131.07C230.93,131.09 230.93,131.12 230.93,131.15C235.72,128.48 239.16,123.68 239.98,118.03C237.55,114.74 233.65,112.6 229.24,112.6C226.18,112.6 223.36,113.63 221.12,115.36C221.35,114.48 221.48,113.55 221.48,112.6C221.48,106.79 216.77,102.08 210.96,102.08C205.14,102.08 200.43,106.79 200.43,112.6C200.43,118.41 205.14,123.12 210.96,123.12Z"
            android:fillColor="#C9D3DD"
            android:fillType="evenOdd"/>
        <path
            android:pathData="M224.32,136.65C225.93,136.65 227.24,135.35 227.24,133.74C227.24,132.13 225.93,130.82 224.32,130.82C222.71,130.82 221.41,132.13 221.41,133.74C221.41,135.35 222.71,136.65 224.32,136.65Z"
            android:fillColor="#E6EAEF"/>
        <path
            android:pathData="M279.94,165.58L287.52,154.23C287.68,153.97 287.68,153.63 287.49,153.38C287.2,152.97 286.58,152.97 286.28,153.37L282.14,158.88L286.02,149.39C286.63,147.9 285.93,146.2 284.46,145.55L283.83,145.28C282.48,144.68 280.89,145.19 280.12,146.45L273.33,157.52C273.1,157.9 272.69,158.16 272.24,158.15C271.54,158.14 271.03,157.6 271,156.96L270.84,153.73C270.79,152.61 270.03,151.64 268.95,151.31C268.85,151.28 268.74,151.35 268.72,151.46L267.52,159.1C267.35,160.17 267.38,161.26 267.6,162.33L269.06,169.21L268.86,171.34C272.49,171.27 275.53,173.66 279.14,173.51L279.94,165.58Z"
            android:fillColor="#E1E9F1"/>
        <path
            android:pathData="M247.62,149.43C252,149.64 255.61,152.76 256.56,156.9L263.18,175.39C259.45,177.43 255.89,179.74 252.38,182.12L247.95,205.16H208.32L204.11,180.45C199.89,178.98 195.68,177.48 191.54,175.93L198.38,156.83C199.39,152.58 203.21,149.42 207.77,149.42C207.99,149.42 208.21,149.43 208.42,149.45L208.43,149.43H246.85C246.95,149.42 247.05,149.42 247.15,149.42C247.26,149.42 247.36,149.42 247.46,149.43H247.62L247.62,149.43Z"
            android:fillColor="#F8F8F8"
            android:fillType="evenOdd"/>
      </group>
      <path
          android:pathData="M316,30H0V29H316V30Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="0"
              android:startY="30.5"
              android:endX="316"
              android:endY="30.5"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.37" android:color="#FFD1E3F1"/>
            <item android:offset="0.62" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M43,166.5L1,166.5L1,165.5L43,165.5L43,166.5Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="1"
              android:startY="166.5"
              android:endX="43"
              android:endY="166.5"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.53" android:color="#FFD1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M316,166.5L178,166.5L178,165.5L316,165.5L316,166.5Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="178"
              android:startY="166.5"
              android:endX="316"
              android:endY="166.5"
              android:type="linear">
            <item android:offset="0" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M316,234H0L0,233H316V234Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="0"
              android:startY="234.5"
              android:endX="316"
              android:endY="234.5"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.22" android:color="#FFD1E3F1"/>
            <item android:offset="0.56" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M316,98.5H0V97.5H316V98.5Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="0"
              android:startY="99"
              android:endX="316"
              android:endY="99"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.14" android:color="#FFD1E3F1"/>
            <item android:offset="0.79" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M178,276L178,-60L179,-60L179,276L178,276Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="177.5"
              android:startY="-60"
              android:endX="177.5"
              android:endY="276"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.17" android:color="#FFD1E3F1"/>
            <item android:offset="0.77" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M246,276L246,166.5L247,166.5L247,276L246,276ZM246,98L246,-60L247,-60L247,98L246,98Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="245.5"
              android:startY="-60"
              android:endX="245.5"
              android:endY="276"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.4" android:color="#FFD1E3F1"/>
            <item android:offset="0.62" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M110,98L110,-60L111,-60L111,98L110,98Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="109.5"
              android:startY="-60"
              android:endX="109.5"
              android:endY="98"
              android:type="linear">
            <item android:offset="0.21" android:color="#00D1E3F1"/>
            <item android:offset="1" android:color="#FFD1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M110,276L110,234L111,234L111,276L110,276Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="109.5"
              android:startY="234"
              android:endX="109.5"
              android:endY="276"
              android:type="linear">
            <item android:offset="0" android:color="#FFD1E3F1"/>
            <item android:offset="0.5" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M42,276L42,-60L43,-60L43,276L42,276Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="41.5"
              android:startY="-17.58"
              android:endX="41.5"
              android:endY="276"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.32" android:color="#FFD1E3F1"/>
            <item android:offset="0.89" android:color="#FFD1E3F1"/>
            <item android:offset="0.94" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M129.09,153.1L159.3,210.7H99L129.09,153.1Z"
          android:fillColor="#E9F0F5"/>
      <path
          android:pathData="M131.75,149.73L120.14,172.05L119.26,171.59L130.87,149.27L131.75,149.73Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="131.75"
              android:startY="149.73"
              android:endX="120.14"
              android:endY="172.05"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.16" android:color="#FFD1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M126.97,147.92L162.24,215.07L161.36,215.54L126.08,148.38L126.97,147.92Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="126.97"
              android:startY="147.92"
              android:endX="162.24"
              android:endY="215.07"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.07" android:color="#FFD1E3F1"/>
            <item android:offset="0.92" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M99,131.95L139.95,210.25H58.21L99,131.95Z"
          android:fillColor="#E9F2F9"/>
      <path
          android:pathData="M165.6,210.7L51.3,210.7L51.3,209.7L165.6,209.7L165.6,210.7Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="51.3"
              android:startY="211.2"
              android:endX="165.6"
              android:endY="211.2"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.06" android:color="#FFD1E3F1"/>
            <item android:offset="0.94" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M96.8,125.87L143.02,214.97L142.13,215.43L95.91,126.33L96.8,125.87Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="96.8"
              android:startY="125.87"
              android:endX="143.02"
              android:endY="214.97"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.1" android:color="#FFD1E3F1"/>
            <item android:offset="0.94" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M100.73,126.77L54.51,215.87L55.4,216.33L101.62,127.23L100.73,126.77Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient
              android:startX="100.73"
              android:startY="126.77"
              android:endX="54.51"
              android:endY="215.87"
              android:type="linear">
            <item android:offset="0" android:color="#00D1E3F1"/>
            <item android:offset="0.1" android:color="#FFD1E3F1"/>
            <item android:offset="0.94" android:color="#FFD1E3F1"/>
            <item android:offset="1" android:color="#00D1E3F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M141.3,145.8C147.21,145.8 152,141.01 152,135.1C152,129.19 147.21,124.4 141.3,124.4C135.39,124.4 130.6,129.19 130.6,135.1C130.6,141.01 135.39,145.8 141.3,145.8ZM141.3,146.8C147.76,146.8 153,141.56 153,135.1C153,128.64 147.76,123.4 141.3,123.4C134.84,123.4 129.6,128.64 129.6,135.1C129.6,141.56 134.84,146.8 141.3,146.8Z"
          android:fillColor="#D3E4F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M44.5,98.25C44.5,99.49 43.49,100.5 42.25,100.5C41.01,100.5 40,99.49 40,98.25C40,97.01 41.01,96 42.25,96C43.49,96 44.5,97.01 44.5,98.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M42.25,99.5C42.94,99.5 43.5,98.94 43.5,98.25C43.5,97.56 42.94,97 42.25,97C41.56,97 41,97.56 41,98.25C41,98.94 41.56,99.5 42.25,99.5ZM42.25,100.5C43.49,100.5 44.5,99.49 44.5,98.25C44.5,97.01 43.49,96 42.25,96C41.01,96 40,97.01 40,98.25C40,99.49 41.01,100.5 42.25,100.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M44.5,166.25C44.5,167.49 43.49,168.5 42.25,168.5C41.01,168.5 40,167.49 40,166.25C40,165.01 41.01,164 42.25,164C43.49,164 44.5,165.01 44.5,166.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M42.25,167.5C42.94,167.5 43.5,166.94 43.5,166.25C43.5,165.56 42.94,165 42.25,165C41.56,165 41,165.56 41,166.25C41,166.94 41.56,167.5 42.25,167.5ZM42.25,168.5C43.49,168.5 44.5,167.49 44.5,166.25C44.5,165.01 43.49,164 42.25,164C41.01,164 40,165.01 40,166.25C40,167.49 41.01,168.5 42.25,168.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M44.5,233.25C44.5,234.49 43.49,235.5 42.25,235.5C41.01,235.5 40,234.49 40,233.25C40,232.01 41.01,231 42.25,231C43.49,231 44.5,232.01 44.5,233.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M42.25,234.5C42.94,234.5 43.5,233.94 43.5,233.25C43.5,232.56 42.94,232 42.25,232C41.56,232 41,232.56 41,233.25C41,233.94 41.56,234.5 42.25,234.5ZM42.25,235.5C43.49,235.5 44.5,234.49 44.5,233.25C44.5,232.01 43.49,231 42.25,231C41.01,231 40,232.01 40,233.25C40,234.49 41.01,235.5 42.25,235.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M112.5,233.25C112.5,234.49 111.49,235.5 110.25,235.5C109.01,235.5 108,234.49 108,233.25C108,232.01 109.01,231 110.25,231C111.49,231 112.5,232.01 112.5,233.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M110.25,234.5C110.94,234.5 111.5,233.94 111.5,233.25C111.5,232.56 110.94,232 110.25,232C109.56,232 109,232.56 109,233.25C109,233.94 109.56,234.5 110.25,234.5ZM110.25,235.5C111.49,235.5 112.5,234.49 112.5,233.25C112.5,232.01 111.49,231 110.25,231C109.01,231 108,232.01 108,233.25C108,234.49 109.01,235.5 110.25,235.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M112.5,98.25C112.5,99.49 111.49,100.5 110.25,100.5C109.01,100.5 108,99.49 108,98.25C108,97.01 109.01,96 110.25,96C111.49,96 112.5,97.01 112.5,98.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M110.25,99.5C110.94,99.5 111.5,98.94 111.5,98.25C111.5,97.56 110.94,97 110.25,97C109.56,97 109,97.56 109,98.25C109,98.94 109.56,99.5 110.25,99.5ZM110.25,100.5C111.49,100.5 112.5,99.49 112.5,98.25C112.5,97.01 111.49,96 110.25,96C109.01,96 108,97.01 108,98.25C108,99.49 109.01,100.5 110.25,100.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M180.5,98.25C180.5,99.49 179.49,100.5 178.25,100.5C177.01,100.5 176,99.49 176,98.25C176,97.01 177.01,96 178.25,96C179.49,96 180.5,97.01 180.5,98.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M178.25,99.5C178.94,99.5 179.5,98.94 179.5,98.25C179.5,97.56 178.94,97 178.25,97C177.56,97 177,97.56 177,98.25C177,98.94 177.56,99.5 178.25,99.5ZM178.25,100.5C179.49,100.5 180.5,99.49 180.5,98.25C180.5,97.01 179.49,96 178.25,96C177.01,96 176,97.01 176,98.25C176,99.49 177.01,100.5 178.25,100.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M180.5,166.25C180.5,167.49 179.49,168.5 178.25,168.5C177.01,168.5 176,167.49 176,166.25C176,165.01 177.01,164 178.25,164C179.49,164 180.5,165.01 180.5,166.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M178.25,167.5C178.94,167.5 179.5,166.94 179.5,166.25C179.5,165.56 178.94,165 178.25,165C177.56,165 177,165.56 177,166.25C177,166.94 177.56,167.5 178.25,167.5ZM178.25,168.5C179.49,168.5 180.5,167.49 180.5,166.25C180.5,165.01 179.49,164 178.25,164C177.01,164 176,165.01 176,166.25C176,167.49 177.01,168.5 178.25,168.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M180.5,234.25C180.5,235.49 179.49,236.5 178.25,236.5C177.01,236.5 176,235.49 176,234.25C176,233.01 177.01,232 178.25,232C179.49,232 180.5,233.01 180.5,234.25Z"
          android:fillColor="#F7F7F8"/>
      <path
          android:pathData="M178.25,235.5C178.94,235.5 179.5,234.94 179.5,234.25C179.5,233.56 178.94,233 178.25,233C177.56,233 177,233.56 177,234.25C177,234.94 177.56,235.5 178.25,235.5ZM178.25,236.5C179.49,236.5 180.5,235.49 180.5,234.25C180.5,233.01 179.49,232 178.25,232C177.01,232 176,233.01 176,234.25C176,235.49 177.01,236.5 178.25,236.5Z"
          android:fillColor="#D1E3F1"
          android:fillType="evenOdd"/>
    </group>
  </group>
</vector>
