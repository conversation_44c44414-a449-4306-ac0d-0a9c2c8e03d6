/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - WidgetImageFileCacheService.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/11/16
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2021/11/16        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.widgetpage.image

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.foundation.cache.diskcache.BaseFileCacheService
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants
import com.oplus.gallery.foundation.util.graphic.BitmapSaveUtils
import com.oplus.gallery.standard_lib.file.File

object WidgetImageFileCacheService : BaseFileCacheService<Uri, Bitmap>() {
    private const val DIR = "widget_image"
    private const val MAX_FILE_COUNT = 50
    private const val MAX_TOTAL_FILE_SIZE = 1024 * 1024 * 100L // 100MB
    private const val MIN_CLEAN_INTERVAL_AFTER_USE = 5000
    private const val COMPRESS_QUALITY = 90
    override val maxFileCount = MAX_FILE_COUNT
    override val maxTotalFileSize = MAX_TOTAL_FILE_SIZE
    override val minCleanIntervalAfterUse = MIN_CLEAN_INTERVAL_AFTER_USE

    override fun getDir(context: Context): File = File(context.cacheDir, DIR)

    override fun onGetCache(context: Context, file: File): Uri? {
        if (file.exists()) {
            return getUriFromFile(context, file)
        }
        return null
    }

    override fun onPutCache(context: Context, file: File, data: Bitmap): Uri {
        // 这里建议额外进行压缩，不要传过大的图片过来
        BitmapSaveUtils.saveBitmap(
            data,
            data.colorSpace,
            file,
            Bitmap.CompressFormat.JPEG,
            COMPRESS_QUALITY
        )
        return getUriFromFile(context, file)
    }

    private fun getUriFromFile(context: Context, file: File): Uri {
        return GalleryFileProvider.getUriFromFile(
            context,
            file,
            AppConstants.Package.PACKAGE_NAME_LAUNCHER,
            AppBrandConstants.Package.PACKAGE_NAME_ASSISTANTSCREEN
        )
    }
}