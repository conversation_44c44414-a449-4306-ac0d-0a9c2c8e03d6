# 桌面卡片业务
1. 功能介绍
    在桌面或负一屏中创建一个卡片显示相册的图片内容
2. 名词说明
    widget code: 卡片唯一标识，由card type、card id、host id组成，卡片服务回调会传给这个参数，推送卡片刷新的时候同样以此为参数
    mode: 模式，卡片添加后需要选择模式才会轮播照片，目前有精选模式和自选模式，精选模式共用轮播列表，自动生成；自选模式独立轮播列表，用户自行添加
    display list: 轮播列表，卡片轮播的一组照片，有独立轮播列表（多个卡片各自有自己的轮播列表）和共用轮播列表（多个卡片共用同一个轮播列表）
    display list id: 轮播列表id，轮播列表id相同的多个照片组成一个轮播列表，独立轮播列表取widget code；共用轮播列表取预定义的常量
3. 包含的主要特性：
   - [特性1](/docs/特性1.md)
   - [特性2](/docs/特性2.md)
   - [特性3](/docs/特性3.md)
4. 包含的重要模块：
   - [模块1](/docs/模块1.md)
   - [模块2](/docs/模块2.md)
   - [模块3](/docs/模块3.md)
# 模组结构介绍
 - [架构设计](/docs/架构设计.md)
 - [模块设计](/docs/模块设计.md)
 - [对外接口](/docs/对外接口.md)
# 编译及安装
 {如何配置、编译、发布此工程，如与主工程无异，则删除此章节}
# 版本说明
 {如与主工程无异，则删除此章节}
 [ReleaseNotices](/docs/ReleaseNotices.md)
# 参考文档
- build.gradle 卡片UI引擎库[参考文档](https://hio.oppo.com/app/ozone/ColorOSDev/gotoOzoneCircleKbItemDetail?page=ozone&source=index&enc_kbi_id=158030195_157662084)