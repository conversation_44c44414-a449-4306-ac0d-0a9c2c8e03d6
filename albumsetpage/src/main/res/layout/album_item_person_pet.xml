<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.oplus.gallery.basebiz.widget.SlotView
        android:id="@+id/base_album_set_item"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:forceDarkAllowed="false"
        android:scaleType="centerCrop"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:markIconHeight="@dimen/base_album_set_item_cover_mark_icon_height"
        app:markIconMarginEnd="@dimen/base_album_set_item_cover_mark_icon_margin_end"
        app:markIconMarginTop="@dimen/base_album_set_item_cover_mark_icon_margin_top"
        app:markIconWidth="@dimen/base_album_set_item_cover_mark_icon_width" />

    <TextView
        android:id="@+id/albumset_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_album_set_item_title_margin_top"
        android:ellipsize="end"
        android:forceDarkAllowed="false"
        android:maxLines="1"
        android:textAppearance="@style/gTextAppearanceSmallButton"
        android:textColor="?attr/gColorPrimaryNeutral"
        android:textDirection="locale"
        app:layout_constraintEnd_toEndOf="@id/base_album_set_item"
        app:layout_constraintStart_toStartOf="@id/base_album_set_item"
        app:layout_constraintTop_toBottomOf="@id/base_album_set_item" />

    <TextView
        android:id="@+id/albumset_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:forceDarkAllowed="false"
        android:maxLines="1"
        android:textAppearance="@style/gTextAppearanceCaption"
        android:textColor="?attr/gColorSecondNeutral"
        android:textDirection="locale"
        app:layout_constraintEnd_toEndOf="@id/albumset_title"
        app:layout_constraintStart_toStartOf="@id/albumset_title"
        app:layout_constraintTop_toBottomOf="@id/albumset_title" />

</androidx.constraintlayout.widget.ConstraintLayout>