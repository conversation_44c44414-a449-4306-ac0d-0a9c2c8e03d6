<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appbar_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/couiColorBackgroundElevatedWithCard"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical"
    app:elevation="0dp"
    app:layout_behavior="@string/common_hideable_divider_toolbar_behavior">

    <com.oplus.gallery.albumsetpage.selection.pictures.ui.SelectionToolBar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/common_toolbar_panel_min_height"
        app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_editor_menu_item_icon_height"
        android:layout_marginBottom="@dimen/base_editor_menu_adjust_item_top_tips_margin_top"
        android:clipChildren="false"
        android:clipToPadding="false">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/thumbnail_view"
            android:layout_width="@dimen/album_move_to_drawable_single_drawable_size"
            android:layout_height="@dimen/album_move_to_drawable_single_drawable_size"
            android:layout_marginStart="@dimen/common_toolbar_large_title_padding_start"
            android:layout_marginTop="@dimen/base_dialog_old_hidden_collection_image_top"
            android:scaleType="centerCrop"
            app:enablePressAnim="false"
            app:layout_constraintDimensionRatio="w,1:1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
            android:id="@+id/tv_thumbView_nums"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_highlight_material_title_margin_start"
            android:ellipsize="end"
            android:forceDarkAllowed="false"
            android:maxLines="1"
            android:textSize="@dimen/album_tool_bar_selected_items_title_size"
            android:textAppearance="@style/gTextAppearanceSmallButton"
            android:textColor="?attr/gColorPrimaryNeutral"
            android:textDirection="locale"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/thumbnail_view"
            app:layout_constraintTop_toTopOf="parent"
            app:textSizeLevel="G4" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_divider_height"
        android:layout_gravity="center_horizontal"
        android:alpha="0"
        android:background="@color/common_divider_line_color"
        android:forceDarkAllowed="false" />
</com.google.android.material.appbar.AppBarLayout>