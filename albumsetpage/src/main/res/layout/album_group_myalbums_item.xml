<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:importantForAccessibility="no"
    android:orientation="vertical"
    tools:parentTag="android.widget.LinearLayout">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="@dimen/common_media_type_title_text_height"
            android:layout_weight="1"
            android:focusable="true"
            style="@style/mainTabAlbumHeadLeftTitle"
            android:gravity="center_vertical|start"
            android:importantForAccessibility="yes"
            android:paddingHorizontal="@dimen/album_set_tab_group_title_padding_left"
            android:text="@string/model_my_album"
            android:textColor="?attr/gColorPrimaryNeutral"
            android:textDirection="locale" />

        <ImageView
            android:id="@+id/my_album_set_enter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:paddingHorizontal="@dimen/my_album_fragment_enter_icon_padding_horizontal"
            android:paddingVertical="@dimen/my_album_fragment_enter_icon_padding_vertical"
            android:clickable="true"
            android:focusable="true"
            android:src="@drawable/my_album_set_enter_icon" />
    </LinearLayout>


    <com.oplus.gallery.foundation.ui.widget.NestScrollRecyclerView
        android:id="@+id/my_album_set_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>