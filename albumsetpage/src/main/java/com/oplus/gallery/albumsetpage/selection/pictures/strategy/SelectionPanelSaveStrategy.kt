/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SelectionPanelSaveStrategy
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2023/3/8 15:03
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2023/3/8		  1.0		 SelectionPanelSaveStrategy
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.selection.pictures.strategy

import android.app.Activity
import android.content.res.Resources
import android.os.Bundle
import androidx.lifecycle.Lifecycle
import com.oplus.gallery.business_lib.model.selection.PathQuickSelectionModel
import com.oplus.gallery.business_lib.selectionpage.SelectInputData

/**
 * 轻量选图面板
 * 添加对应业务勾选策略
 */
interface SelectionPanelSaveStrategy {
    /**
     * 处理轻量选图保存业务接口
     *
     * @param activity 传递的Activity
     * @param arguments 传递的参数Bundle
     * @param resource 资源集
     * @param selectInputData 构造的选择selectInputData
     * @param selectionModel
     * @param returnSave 策略返回不执行后续操作
     * @param callBackBundle 回调给调用端Bundle
     *        回调给调用端Bundle主要包含：返回的状态类型[KEY_RESULT_CODE]
     *                              : 返回的列表类型的数据[KEY_RESULT_DATA]
     *                              : 返回单个的数据[KEY_RESULT_DATA]
     *                              : 返回的对应业务无效的图片张数[KEY_RESULT_ERROR_PHOTO_COUNT]
     */
    @Suppress("LongParameterList")
    fun dealSave(
        activity: Activity,
        lifecycle: Lifecycle,
        arguments: Bundle?,
        resource: Resources,
        selectInputData: SelectInputData,
        selectionModel: PathQuickSelectionModel,
        returnSave: () -> Unit,
        callBackBundle: (Bundle) -> Unit
    )
}