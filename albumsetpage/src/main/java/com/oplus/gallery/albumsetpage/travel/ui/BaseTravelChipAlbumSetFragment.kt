/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseTravelChipAlbumSetFragment.kt
 ** Description: 旅行图集列表页面基类
 ** Version: 1.0
 ** Date: 2025/4/10
 ** Author: ********
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** ********                        2025/4/10     1.0          create
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.travel.ui

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.core.graphics.Insets
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.component.responsiveui.ResponsiveUIModel
import com.coui.component.responsiveui.layoutgrid.MarginType
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.maptravel.ui.MapTravelTabFragment
import com.oplus.gallery.albumsetpage.travel.viewmodel.TravelAlbumSetViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.basebiz.sidepane.SidePaneAnimationConfig
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.SidePaneWithAlbumAnimation
import com.oplus.gallery.basebiz.sidepane.adjustControlIconColor
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isClose
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.activity.findMapContainerId
import com.oplus.gallery.basebiz.uikit.controller.TintTranslucentElement
import com.oplus.gallery.basebiz.uikit.fragment.base.IFragmentStack
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_TRAVEL_SECOND
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumSetFragment
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.ui.systembar.IFragmentSystemBarController
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.ui.systembar.toolbar.LightNightAnimationDriver
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.Number
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.basebiz.R as BaseBizR

/**
 * 旅行图集列表页面基类
 */
open class BaseTravelChipAlbumSetFragment : BaseAlbumSetFragment() {

    /**
     * 响应式 UI 模型，用来计算栅格相关的参数
     */
    protected var responsiveUIModel: ResponsiveUIModel? = null

    /**
     * 是否侧边栏收起
     */
    protected val isSidePaneClosed: Boolean
        get() = sidePaneAccessor.isSupportSidePane() && sidePaneAccessor.isClose()

    /**
     * 底部模糊是否已显示
     */
    private var isBottomTranslucentViewShowing = false

    /**
     * 侧边栏展开收起动画联动基础列表动画管理
     */
    private var sidePaneAnim: SidePaneWithAlbumAnimation? = null

    /**
     * 底部沉浸式蒙层渐变动画
     */
    private var bottomTranslucentAnim: TintTranslucentElement? = null

    /**
     * 底部模糊视图
     */
    private val bottomTranslucentView: ImageView? by lazy { view?.findViewById(com.oplus.gallery.basebiz.R.id.bottomTranslucentView) }

    /**
     * 底部沉浸式蒙层渐变动画控制器
     */
    private val progressChanger: LightNightAnimationDriver by lazy { LightNightAnimationDriver() }

    /**
     * 仅用于Fragment create时创建ViewModel
     * 在Fragment处于detach状态时调用会引发异常，业务代码不建议直接使用该方法获得ViewModel
     */
    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> {
        return ViewModelProvider(this)[TravelAlbumSetViewModel::class.java]
    }

    /**
     * 添加了底部模糊视图
     */
    override fun getLayoutId(): Int {
        return R.layout.albumsetpage_travel_fragment_normal_album_set
    }

    /**
     * 底部是否进入沉浸模式
     */
    fun isBottomImmersiveCurrently(): Boolean {
        GLog.d(logTag, LogFlag.DL, "isBottomImmersiveCurrently")
        if ((parentFragment as? TravelAlbumSetTabFragment)?.parentTabIsShow != true) {
            GLog.d(logTag, LogFlag.DL, "isBottomImmersiveCurrently. no map tab.")
            return false
        }
        val recyclerView = this.recyclerView ?: return false

        val distanceToListBottom = recyclerView.let {
            // 滑动到列表底部还需要的距离
            it.computeVerticalScrollRange() - (it.computeVerticalScrollOffset() + it.computeVerticalScrollExtent() + it.scrollY)
        }
        GLog.d(logTag, LogFlag.DL, "isBottomImmersiveCurrently. distanceToListBottom: $distanceToListBottom, " +
                "recyclerView.paddingBottom: ${recyclerView.paddingBottom}")
        return distanceToListBottom > recyclerView.paddingBottom
    }

    /**
     * 检查底部沉浸式色调
     */
    private fun checkBottomImmersiveTint(isForceStart: Boolean = false, isNeedAnimation: Boolean = true) {
        GLog.d(logTag, LogFlag.DL, "checkBottomImmersiveTint")
        val translucentView = bottomTranslucentView ?: return
        val isBottomImmersive = isBottomImmersiveCurrently()

        if ((isBottomTranslucentViewShowing == isBottomImmersive) && !isForceStart) return
        isBottomTranslucentViewShowing = isBottomImmersive

        // 取消注册，防止出现野元素在监听动画变化
        bottomTranslucentAnim?.unregister()
        bottomTranslucentAnim = TintTranslucentElement(
            translucentView = translucentView,
            changer = progressChanger,
            isNeedAnimation = true,
            shouldTranslucentVisible = isBottomImmersive
        ).apply {
            register(this@BaseTravelChipAlbumSetFragment)
        }

        progressChanger.start(!isBottomImmersive, isNeedAnimation)
    }

    /**
     * 页面销毁，资源释放
     */
    override fun onDestroy() {
        super.onDestroy()
        // 取消底部沉浸式蒙层动画
        bottomTranslucentAnim?.unregister()
        progressChanger.cancel()
    }

    /**
     * 更新RecyclerView底部底部padding
     *
     * - 设计为距离底部Tab顶部 40dp，需要适配导航栏的高度，虚拟键和全屏手势二者高度存在差异，虚拟键时底部Tab栏位置被往上顶，
     * 另外还要考虑，虚拟键时，RecycleView的底部margin也有变化，因此需要虚拟键下需要弥补的padding为 ：Tab栏高度 - Tab栏top_padding
     */
    private fun updateRecyclerViewPadding(naviBarInsets: Insets) {
        val bottomNaviHeight = resources.getDimensionPixelSize(BaseBizR.dimen.common_bottom_navigation_menu_height)
        val bottomBarHeight: Int = when {
            // 全屏手势(手势指示条显示)
            isFullScreenGesture -> bottomNaviHeight + naviBarInsets.bottom
            // 全屏手势(手势指示条隐藏)
            isFullScreenGestureAndNoBar -> bottomNaviHeight + resources.getDimensionPixelSize(BaseBizR.dimen.main_tab_bottom_bar_margin_bottom_extra)
            // 虚拟键
            else -> naviBarInsets.bottom
        }

        val bottomPadding = resources.getDimensionPixelSize(BaseBizR.dimen.main_album_fragment_recycler_view_padding_bottom)
        val bottomPaddingExtra = if (hasVirtualKey()) {
            resources.getDimensionPixelSize(BaseBizR.dimen.main_album_fragment_recycler_view_padding_bottom_extra)
        } else {
            0
        }
        recyclerView?.updatePadding(bottom = bottomBarHeight + bottomPadding + bottomPaddingExtra)
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        responsiveUIModel = context?.let {
            ResponsiveUIModel(it, resources.displayMetrics.widthPixels, Number.NUMBER_0).chooseMargin(MarginType.MARGIN_LARGE)
        }
        super.doViewCreated(view, savedInstanceState)
        sidePaneAccessor?.takeIf { isSidePaneEnabled() }?.let {
            sidePaneAnim = recyclerView?.let { SidePaneWithAlbumAnimation(this, it) }
        }
        recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                checkBottomImmersiveTint()
                (parentFragment as? TravelAlbumSetTabFragment)?.checkPageImmersiveState(recyclerView.computeVerticalScrollOffset())
            }
        })
    }

    private fun getListSpanCount(): Int = (recyclerView?.layoutManager as? GridLayoutManager)?.spanCount ?: 1

    protected open fun getListItemWidth(): Int {
        return (recyclerView?.layoutManager as? GridLayoutManager)?.getChildAt(0)?.width ?: 0
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        GLog.d(logTag, LogFlag.DL) { "onSidePaneSlideStart: isResumed:$isResumed newState=$newState $clazzSimpleName" }
        if (isResumed && newState.isFlat()) {
            sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint())
            val currentSpanCount = getListSpanCount()
            context?.let {
                refreshLayoutManager(it)
            }
            sidePaneAnim?.startAnimation(
                SidePaneAnimationConfig(
                    currentSpanCount = currentSpanCount,
                    nextSpanCount = getListSpanCount(),
                    currentItemWidth = getListItemWidth(),
                    nextItemWidth = layoutDetail.itemWidth,
                    slideWidth = sidePaneAccessor.getSlideWidth(),
                    gapWidth = resources.getDimensionPixelOffset(com.oplus.gallery.basebiz.R.dimen.base_album_fragment_item_view_horizontal_gap),
                    newState = newState
                )
            )
        }
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        sidePaneAnim?.animating(newState, slideProgress)
    }

    /**
     * @param newState 动画开始后,侧边栏的终点状态
     */
    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        super.onSidePaneSlideEnd(newState)
        context?.let { refreshLayoutManager(it) }
    }

    /**
     * 这里需要将基类预留的状态栏 top padding 给去掉
     */
    override fun getSystemBarStyle() = object : BaseListSystemBarStyle() {
        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            super.onUpdate(windowInsets, isForeground)
            val controller = fragment as IFragmentSystemBarController
            windowInsets.naviBarInsets().apply {
                val bottomPadding: Int = if (controller.hasVirtualKey()) bottom else 0
                controller.setContentPadding(left, top, right, bottomPadding)
            }
            if (isForeground) {
                setStatusBarAppearance(isStatusBarLightAppearance())
                sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint())
            }
            updateRecyclerViewPadding(windowInsets.naviBarInsets())
        }
    }

    /**
     * 点击某一个具体的图集
     * @param position 图集位置
     * @param viewData 图集视图数据
     * @param clickType 点击类型
     *  0：点击的是itemView
     *  1：点击的是itemView的checkbox
     */
    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
        GLog.d(logTag, LogFlag.DL) { "onItemClick: viewData = $viewData" }
        //旅程这里的视图架构嵌套了三层
        (parentFragment?.parentFragment as? MapTravelTabFragment)?.resetSeamlessAnimRootNodeConfig(
            resources.configuration.orientation,
            resources.configuration.screenWidthDp,
            resources.configuration.screenHeightDp
        ) ?: (parentFragment as? TravelAlbumSetTabFragment)?.resetSeamlessAnimRootNodeConfig(
            resources.configuration.orientation,
            resources.configuration.screenWidthDp,
            resources.configuration.screenHeightDp
        )
        val floatingWindowOffset = IntArray(2)
        if (isFloatingWindowMode()) view?.rootView?.getLocationOnScreen(floatingWindowOffset)
        val clickedItemView = recyclerAdapter.getItemByIndex(position)
        val radius = baseListViewModel?.getStyle(StyleType.TYPE_THUMB_STYLE)?.getFloat(StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS) ?: 0f
        val titleBarBottomY = (parentFragment as? TravelAlbumSetTabFragment)?.getTravelChipGroupBottomY() ?: 0f
        viewData?.let { data ->
            activity?.supportFragmentManager?.start(
                postCard = PostCard(RouterConstants.RouterName.TRAVEL_DETAIL_FRAGMENT),
                resId = activity.findMapContainerId(),
                data = Bundle().also {
                    it.putParcelable(KEY_ALBUM_VIEW_DATA, data)
                    FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                        context,
                        it,
                        clickedItemView,
                        TriggerViewRectGetter(clickedItemView, floatingWindowOffset, titleBarBottomY, false),
                        ENTRANCE_TRAVEL_SECOND,
                        radius
                    )
                },
                anim = SEAMLESS_ANIM_ARRAY,
                addToBackStack = true,
                fragmentStack = activity as IFragmentStack
            )
        }
    }

    override fun isPageItemSelectable(): Boolean = false

    override fun isSupportDrop() = DragHelper.isSupportDrop()
}