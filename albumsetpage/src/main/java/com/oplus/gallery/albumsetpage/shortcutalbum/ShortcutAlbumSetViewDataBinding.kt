/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ShortcutViewDataBinding.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2025/4/3
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2025/4/3     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.albumsetpage.shortcutalbum

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.allalbum.item.ShortcutViewData
import com.oplus.gallery.albumsetpage.allalbum.ui.ALBUM_TYPE_TAB_SHORTCUT_ALBUM
import com.oplus.gallery.albumsetpage.allalbum.ui.OnAlbumClickListener
import com.oplus.gallery.albumsetpage.selection.albums.ui.AlbumSetMenuManager.Companion.ALBUM_MOVEABLE_SHORTCUT
import com.oplus.gallery.albumsetpage.selection.albums.ui.AlbumSetMenuManager.Companion.ORIGIN_ARGUMENTS_BUNDLE_KEY
import com.oplus.gallery.albumsetpage.selection.albums.ui.MenuActionCallbackParams
import com.oplus.gallery.albumsetpage.selection.albums.ui.MenuActionCallbackParams.Companion.KEY_SELECTED_ALBUM_VIEW_DATA
import com.oplus.gallery.basebiz.sidepane.ISidePaneAccessor
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.SidePaneAnimationConfig
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.SidePaneWithAlbumAnimation
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.AlbumViewData.Companion.SUPPORT_IS_ADDED
import com.oplus.gallery.basebiz.widget.AlbumRecycleView
import com.oplus.gallery.business_lib.helper.BottomMenuHelper.Companion.ACTION_MOVE_IN_SHORTCUT_ALBUM
import com.oplus.gallery.business_lib.helper.BottomMenuHelper.Companion.ACTION_MOVE_OUT_SHORTCUT_ALBUM
import com.oplus.gallery.business_lib.helper.BottomMenuHelper.Companion.ACTION_STATE_COMPLETED
import com.oplus.gallery.business_lib.helper.BottomMenuHelper.Companion.ACTION_STATE_START
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.updateMarginRelative
import com.oplus.gallery.framework.abilities.data.model.BaseModel.Companion.COUNT_UNINITIALIZED
import com.oplus.gallery.framework.abilities.data.sortablemodel.sort.AlbumMovable
import com.oplus.gallery.standard_lib.baselist.view.BaseViewDataBinding
import com.oplus.gallery.standard_lib.baselist.view.BaseViewHolder
import com.oplus.gallery.standard_lib.baselist.view.ItemClickListener
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.basebiz.R as basebizR

/**
 * 常用图集View,实现VM，常用图集Item点击和长按事件
 *
 * <AUTHOR>
 * @since v1.0
 */
class ShortcutAlbumSetViewDataBinding(
    context: Context,
    private val onCallbackGetter: MainAlbumTabShortcutCallbackGetter,
    private val shortcutAlbumItemListener: OnAlbumClickListener<AlbumViewData>,
) : BaseViewDataBinding<ShortcutViewData, BaseViewHolder<ShortcutViewData>>(context), ISidePaneListener {
    private var adjustTxtView: COUITextView? = null
    private var titleTxtView: COUITextView? = null
    private var shortcutAlbumLayout: ConstraintLayout? = null
    private var recyclerView: RecyclerView? = null
    private var currentTotalCount = COUNT_UNINITIALIZED

    private var shortcutAlbumSetViewModel: ShortcutAlbumSetViewModel? = null
    private val lifecycleOwner by lazy { onCallbackGetter.onLifecycleOwnerGet() }

    private var sidePaneAnim: SidePaneWithAlbumAnimation? = null
    var onSidePaneAccessorGet: (() -> ISidePaneAccessor?)? = null
    private val sidePaneAccessor: ISidePaneAccessor?
        get() = onSidePaneAccessorGet?.invoke()

    private val isResumed: Boolean
        get() = lifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)

    private val mainTabShortcutRvSection: BaseShortcutRVSection<ShortcutAlbumSetViewModel> =
        object : BaseShortcutRVSection<ShortcutAlbumSetViewModel>(null, onCallbackGetter, shortcutAlbumItemListener) {
            override fun onCreateViewModel(): ShortcutAlbumSetViewModel {
                return ViewModelProvider(onCallbackGetter.onStoreOwnerGet())[ShortcutAlbumSetViewModel::class.java].apply {
                    shortcutAlbumSetViewModel = this
                }
            }

            override fun getTag(): String {
                return TAG_UI_SECTION
            }

            override fun createItemViewClickListener(): ItemClickListener<AlbumViewData> {
                return object : ItemClickListener<AlbumViewData> {
                    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
                        shortcutAlbumItemListener.onItemClick(position, viewData, clickType, itemView)
                    }
                }
            }

            override fun onItemLongClick(position: Int, view: View, viewData: AlbumViewData?): Boolean {
                val itemView = view.findViewById<ImageView>(basebizR.id.base_album_set_item)
                itemView.setTag(ORIGIN_ARGUMENTS_BUNDLE_KEY, Bundle().apply {
                    putSerializable(ALBUM_MOVEABLE_SHORTCUT, shortcutAlbumSetViewModel as? AlbumMovable)
                })
                return shortcutAlbumItemListener.onItemLongClick(
                    position,
                    itemView,
                    viewData,
                    ALBUM_TYPE_TAB_SHORTCUT_ALBUM
                )
            }

            override fun onTotalCountChanged(lastTotalCount: Int?, totalCount: Int) {
                if ((currentTotalCount == COUNT_UNINITIALIZED) && (totalCount > 0)) {
                    // 首次加载数据后,才显示标题,避免冷启动进入后,标题显示了内容还未显示
                    setTitleVisibility(true)
                }
            }
        }

    /**
     * 横屏时刷新layout
     */
    fun notifyLayoutChanged() {
        mainTabShortcutRvSection.refreshLayoutManager()
        mainTabShortcutRvSection.notifyDataSetChanged()
        //资源根据小中大屏自动切换值
        val marginHorizontal = context.resources.getDimensionPixelOffset(basebizR.dimen.album_set_tab_commonly_used_module_padding_horizontal)
        shortcutAlbumLayout?.updateMarginRelative(start = marginHorizontal, end = marginHorizontal)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<ShortcutViewData> {
        GLog.d(TAG, LogFlag.DL) { "onCreateViewHolder viewType:$viewType" }
        setMenuActionListener()
        val view = LayoutInflater.from(parent.context).inflate(R.layout.album_group_shortcut_item, parent, false).apply {
            titleTxtView = findViewById(R.id.tv_title)
            adjustTxtView = findViewById(R.id.tv_adjust)
            shortcutAlbumLayout = findViewById(R.id.shortcutLayout)
            findViewById<AlbumRecycleView>(R.id.recycler_view)?.let { rv ->
                mainTabShortcutRvSection.attach(rv)
                recyclerView = rv
                sidePaneAccessor?.takeIf { it.isSupportSidePane() }?.let {
                    sidePaneAnim = SidePaneWithAlbumAnimation(lifecycleOwner, rv)
                }
            }
        }
        adjustTxtView?.setOnClickListener {
            if (DoubleClickUtils.isFastDoubleClick()) {
                return@setOnClickListener
            }
            onCallbackGetter.onClickAdjustCallbackGet()
        }

        return BaseViewHolder(view, this)
    }

    private fun setTitleVisibility(isVisible: Boolean) {
        titleTxtView?.isVisible = isVisible
        adjustTxtView?.isVisible = isVisible
    }

    private fun setMenuActionListener() {
        onCallbackGetter.onMenuActionCallbackLiveDataGet().observe(lifecycleOwner) { result ->
            val state = result.bundle?.getInt(MenuActionCallbackParams.KEY_MENU_ACTION_STATE)
            val viewData = result.bundle?.getParcelable<AlbumViewData?>(KEY_SELECTED_ALBUM_VIEW_DATA)
            /*
             * 此方法主要用于图集列表移除当前列表的操作，进而控制在有loading框时不刷新数据，直到框消失后
             * （包含但不仅限于移入移除其他人物图集、设为私密、删除、恢复、添加到、合并等）
             */
            shortcutAlbumSetViewModel?.needSkipRefreshData = (state == ACTION_STATE_START)
            if (state == ACTION_STATE_COMPLETED) {
                completeMenuActionCallback(result.actionType, viewData)
            }
        }
    }

    override fun copyLayout(context: Context): BaseViewDataBinding<ShortcutViewData, BaseViewHolder<ShortcutViewData>> = this

    override fun onBindViewHolder(itemViewHolder: BaseViewHolder<ShortcutViewData>, position: Int, viewData: ShortcutViewData?) {
        // do nothing
    }

    /**
     * 改变viewData的移入常用图集的状态，如果是设为私密，需要移出常用图集
     */
    private fun completeMenuActionCallback(actionType: String?, viewData: AlbumViewData?) {
        when (actionType) {
            ACTION_MOVE_IN_SHORTCUT_ALBUM -> viewData?.supportedAbilities?.putBoolean(SUPPORT_IS_ADDED, true)
            ACTION_MOVE_OUT_SHORTCUT_ALBUM -> viewData?.supportedAbilities?.putBoolean(SUPPORT_IS_ADDED, false)

            else -> {
                GLog.w(TAG, LogFlag.DL) {
                    "changeViewDataAddToShortCutState actionType = $actionType,viewDataName = ${viewData?.title} "
                }
            }
        }
    }

    fun updateSafeBoxEntryIfChanged() {
        shortcutAlbumSetViewModel?.updateSafeBoxStatus()
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        if (isResumed && newState.isFlat()) {
            val currentSpanCount = getListSpanCount()
            val layoutDetail = mainTabShortcutRvSection.refreshLayoutManager()
            val nextSpanCount = layoutDetail?.spanCount ?: getListSpanCount()
            if (currentSpanCount == layoutDetail?.spanCount) {
                GLog.d(getTag(), LogFlag.DL) { "onSidePaneSlideStart: newState=$newState spanCount=$nextSpanCount equal, skip anim" }
                return
            }
            sidePaneAnim?.startAnimation(
                SidePaneAnimationConfig(
                    currentSpanCount = currentSpanCount,
                    nextSpanCount = nextSpanCount,
                    currentItemWidth = getListItemWidth(),
                    nextItemWidth = layoutDetail?.itemWidth ?: 0,
                    slideWidth = sidePaneAccessor.getSlideWidth(),
                    gapWidth = context.resources.getDimensionPixelOffset(basebizR.dimen.shortcut_view_layout_detail_gap_width),
                    newState = newState
                )
            )
        }
    }

    private fun getListSpanCount(): Int = (recyclerView?.layoutManager as? GridLayoutManager)?.spanCount ?: 1

    private fun getListItemWidth(): Int {
        return (recyclerView?.layoutManager as? GridLayoutManager)?.getChildAt(0)?.width ?: 0
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        sidePaneAnim?.animating(newState, slideProgress)
    }

    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        sidePaneAnim?.endAnimation(newState)
        if (!isResumed && newState.isFlat()) {
            mainTabShortcutRvSection.refreshLayoutManager()
        }
    }

    companion object {
        private const val TAG = "ShortcutAlbumSetViewDataBinding"
        private const val TAG_UI_SECTION = "ShortcutRVSection"
    }
}

/**
 * 参数太多，超过显6个限制,将callback单独拎出来
 *
 * @param onMenuActionCallbackLiveDataGet 执行菜单项时,状态回调
 * @param onClickAdjustCallbackGet 点击调整按钮的回调
 */
class MainAlbumTabShortcutCallbackGetter(
    val onStoreOwnerGet: (() -> ViewModelStoreOwner),
    onLifecycleOwnerGet: (() -> LifecycleOwner),
    onParentWidthGet: (() -> Int),
    val onMenuActionCallbackLiveDataGet: (() -> LiveData<MenuActionCallbackParams>),
    val onClickAdjustCallbackGet: () -> Unit,
    isPictureEmpty: ((Boolean) -> Unit)? = null,
) : ShortcutAlbumCallbackGetter(onLifecycleOwnerGet, onParentWidthGet, isPictureEmpty)