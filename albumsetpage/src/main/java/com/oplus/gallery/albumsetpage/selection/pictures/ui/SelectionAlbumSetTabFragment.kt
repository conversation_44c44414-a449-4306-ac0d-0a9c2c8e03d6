/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SelectionAlbumSetTabFragment.kt
 * Description: 选图的所有图集页面
 *
 * Version: 1.0
 * Date: 2022/08/16
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                     <date>          <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery       2022/08/16        1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.albumsetpage.selection.pictures.ui

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.allalbum.ui.AllAlbumSetFooterViewDataBinding
import com.oplus.gallery.albumsetpage.allalbum.view.AllAlbumSetItemGapDecoration
import com.oplus.gallery.albumsetpage.allalbum.viewmodel.AllAlbumSetViewModel
import com.oplus.gallery.albumsetpage.base.BaseAlbumSetTabFragment
import com.oplus.gallery.albumsetpage.selection.pictures.viewmodel.SelectionAlbumSetTabViewModel
import com.oplus.gallery.basebiz.constants.IntentConstant.TrackConstant.ALBUM_NAVIGATION_TRACK_PAGE_ID
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_ALBUM_FRAGMENT
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_OTHER_ALBUM_SET_TAB_FRAGMENT
import com.oplus.gallery.basebiz.helper.createFragmentByPostCard
import com.oplus.gallery.basebiz.uikit.AlbumTitleViewData
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentStartType
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.basebiz.widget.AlbumRecycleView
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.selectionpage.ISelectionPage
import com.oplus.gallery.business_lib.selectionpage.KEY_SELECTION_DATA
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment.Companion.KEY_ALBUM_MODEL_BUNDLE
import com.oplus.gallery.business_lib.ui.view.AllAlbumSetViewDataBinding
import com.oplus.gallery.business_lib.ui.view.TitleViewDataBinding
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_MEDIA_ALBUM
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils.toDp
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.KEY_MODE
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.MODE_MEDIA_TYPE_ALL
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.MODE_MEDIA_TYPE_SUPPORT_ALBUM_NOT_EMPTY_ALL
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.basebiz.R as BasebizR

@RouterNormal(RouterConstants.RouterName.SELECTION_ALBUM_SET_TAB_FRAGMENT)
class SelectionAlbumSetTabFragment : BaseAlbumSetTabFragment(), ISelectionPage {
    override val supportDragItem = true
    override lateinit var selectInputData: SelectInputData
    override val recyclerViewPaddingBottom: Int
        get() {
            return if (selectInputData.selectMulti) {
                resources.getDimensionPixelSize(BasebizR.dimen.main_selection_album_set_fragment_padding_bottom_append) +
                        resources.getDimensionPixelSize(BasebizR.dimen.main_selection_timeline_multi_padding_bottom)
            } else super.recyclerViewPaddingBottom
        }
    private lateinit var customContext: Context

    override fun getLayoutId() = R.layout.album_fragment_selection_album_set_tab

    /**
     * 媒体类型
     */
    private var selectionAlbumSetFooterViewDataBinding: AllAlbumSetFooterViewDataBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        customContext = refreshCustomContext()
        selectInputData = arguments?.getParcelable(KEY_SELECTION_DATA)!!
    }

    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> {
        return ViewModelProvider(this)[SelectionAlbumSetTabViewModel::class.java]
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        baseListViewModel?.setViewData(
            AlbumViewData(
                id = TextUtil.EMPTY_STRING,
                position = 0,
                modelType = TextUtil.EMPTY_STRING,
                isMediaAlbum = true,
                version = 0,
                totalCount = 0,
                title = TextUtil.EMPTY_STRING
            ),
            // new 一个Bundle避免里边被修改
            Bundle(selectInputData.modelBundle).apply {
                putInt(KEY_MODE, getInt(KEY_MODE, MODE_MEDIA_TYPE_ALL) or MODE_MEDIA_TYPE_SUPPORT_ALBUM_NOT_EMPTY_ALL)
            }
        )
        (baseListViewModel as? AllAlbumSetViewModel)?.onExtraFixHeadListCountChangedListener = { count ->
            setExtraFixHeadListCount(count)
        }

        canShowSelectionMode = false

        recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                listScrollOffset = recyclerView.computeVerticalScrollOffset()
                toolbarSetter?.setListScrollPosition(listScrollOffset)
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == COUIRecyclerView.SCROLL_STATE_IDLE) {
                    toolbarSetter?.setListScrollStateIdle(recyclerView, recyclerView.computeVerticalScrollOffset())
                }
            }
        })
        (recyclerView as? AlbumRecycleView)?.apply {
            onOverScrollListener = object : AlbumRecycleView.OnOverScrollListener {
                override fun onOverScrolled(scrollX: Int, scrollY: Int, clampedX: Boolean, clampedY: Boolean) {
                    if (listScrollOffset <= 0) {
                        toolbarSetter?.setListScrollPosition(scrollY)
                    }
                }
            }
        }
        refreshRecyclerPadding()
        recyclerView?.updatePadding(
            left = customContext.resources.getDimensionPixelSize(BasebizR.dimen.main_all_album_fragment_recycler_view_horizontal_padding),
            right = customContext.resources.getDimensionPixelSize(BasebizR.dimen.main_all_album_fragment_recycler_view_horizontal_padding)
        )
        // 初始化媒体类型视图容器FooterView
        onFooterViewCreated()
    }

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        // do nothing
    }

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        super.onSetUpViewModelStyle(viewModel)
        viewModel.getStyle(StyleType.TYPE_THUMB_STYLE).apply {
            put(
                StyleData.KEY_THUMB_SIZE_TYPE,
                getThumbSizeType(layoutDetail.itemWidth)
            )
        }.apply {
            viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, this)
        }
        viewModel.getStyle(StyleType.TYPE_GRID_THUMB_STYLE).apply {
            put(
                StyleData.KEY_THUMB_SIZE_TYPE,
                getThumbSizeType(layoutDetail.itemWidth)
            )
        }.apply {
            viewModel.addStyle(StyleType.TYPE_GRID_THUMB_STYLE, this)
        }
    }

    override fun getThumbSizeType(itemWidth: Int): Int {
        return selectInputData.thumbnailType ?: ThumbnailSizeUtils.getMicroThumbnailKey(itemWidth)
    }

    override fun initLayoutDetail(context: Context): LayoutDetail {
        return super.initLayoutDetail(customContext)
    }

    override fun addItemDecoration() {
        recyclerView?.addItemDecoration(AllAlbumSetItemGapDecoration(customContext, layoutDetail as GridLayoutDetail))
    }

    override fun getAdapterItemConfigs(): List<ItemConfig<AlbumViewData>> {
        return mutableListOf<ItemConfig<AlbumViewData>>().apply {
            context?.let { context ->
                val normalDataBinding = AllAlbumSetViewDataBinding(
                    context,
                    stylePool = baseListViewModel,
                    isMaskVisibleGet = { isMaskVisible() },
                    checkboxAnimEnableGet = { checkboxAnimEnable() }
                )
                add(ItemConfig(AlbumViewData::class.java, normalDataBinding))
                add(
                    ItemConfig(
                        AlbumTitleViewData::class.java,
                        TitleViewDataBinding(
                            context,
                            stylePool = baseListViewModel,
                            { getAdapterTotalCount() },
                            { isMaskVisible() },
                            { checkboxAnimEnable() },
                        )
                    )
                )
            }
        }
    }

    override fun getContentWidth(): Int {
        return (parentFragment?.parentFragment?.parentFragment as? PanelDialog)?.contentWidth ?: super.getContentWidth()
    }

    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int) {
        GLog.d(TAG, "onItemClick: viewData: $viewData")
        if (DoubleClickUtils.isFastDoubleClick()) return
        viewData ?: return
        if (viewData.modelType == DataRepository.LocalAlbumModelGetter.TYPE_OTHER_ALBUM_SET) {
            dealOtherAlbumSetClick()
        } else if (viewData.modelType != DataRepository.LocalAlbumModelGetter.TYPE_TITLE_ALBUM) {
            dealAlbumClick(viewData)
        }
    }

    private fun dealOtherAlbumSetClick() {
        val args = Bundle(arguments)
        (parentFragment?.parentFragment?.parentFragment as? SelectionPanelDialog)?.also { panelDialog ->
            (createFragmentByPostCard(PostCard(SELECTION_OTHER_ALBUM_SET_TAB_FRAGMENT)) as? SelectionOtherAlbumSetTabFragment)?.also { realFragment ->
                realFragment.arguments = args
                panelDialog.replacePanel(realFragment)
            }
        } ?: run {
            startByStack<SelectionOtherAlbumSetTabFragment>(
                startType = FragmentStartType.REPLACE,
                postCard = PostCard(SELECTION_OTHER_ALBUM_SET_TAB_FRAGMENT),
                resId = BasebizR.id.base_fragment_container,
                anim = DEFAULT_ANIM_ARRAY,
                data = args
            )
        }
    }

    /**
     * 无图可选图集数据时,显示空页面
     */
    override fun refreshEmptyView() {
        val albumSetCount = allAlbumSetViewModel.totalSize
        val hasAlbumSet = albumSetCount > 0
        when {
            hasAlbumSet -> hideEmptyPageView()
            // 只在查询到无图集时显示空页面提示，查询完成前（-1）不显示，避免界面闪现空页面提示
            albumSetCount == 0 -> showEmptyPageView()
        }
    }

    private fun dealAlbumClick(viewData: AlbumViewData) {
        val args = Bundle(arguments).also {
            it.putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
        }
        (parentFragment?.parentFragment?.parentFragment as? SelectionPanelDialog)?.also { panelDialog ->
            (createFragmentByPostCard(PostCard(SELECTION_ALBUM_FRAGMENT)) as? SelectionAlbumFragment)?.also { realFragment ->
                realFragment.arguments = args
                panelDialog.replacePanel(realFragment)
            }
        } ?: run {
            startByStack<SelectionAlbumFragment>(
                startType = FragmentStartType.REPLACE,
                postCard = PostCard(SELECTION_ALBUM_FRAGMENT),
                resId = BasebizR.id.base_fragment_container,
                anim = DEFAULT_ANIM_ARRAY,
                data = args
            )
        }
    }

    override fun isSupportEmptyPage() = true

    override fun getEmptyPageTitleRes() = BasebizR.string.base_no_album_tips

    /**
     * @inheritDoc
     *
     * 拦截所有的长按事件
     */
    override fun dispatchLongClick(position: Int): Boolean = true

    /**
     * 创建FooterView，用来装载媒体类型视图
     */
    override fun onFooterViewCreated() {
        context?.let { contextNotNull ->
            selectionAlbumSetFooterViewDataBinding ?: let {
                selectionAlbumSetFooterViewDataBinding = AllAlbumSetFooterViewDataBinding(contextNotNull, lifecycleScope).apply {
                    // 点击回调
                    onFooterItemClickCallback = { typeIndex, fileCount  ->
                        virtualAlbumClick(typeIndex)
                    }
                    // 插入媒体类型视图
                    recyclerAdapter.addFooterView(this)
                }
            }
        } ?: GLog.e(TAG, LogFlag.DL, "onFooterViewCreated in SelectionAlbumSetTab, context is null ")
        // 注册媒体类型观察者
        allAlbumSetViewModel.virtualAlbumSetItemEntryList.observe(this) {
            selectionAlbumSetFooterViewDataBinding?.updateAlbumSet(it)
        }
    }

    /**
     * 媒体类型点击
     */
    private fun virtualAlbumClick(typeIndex: Int) {
        val virtualEntryBuilder = allAlbumSetViewModel.virtualEntryBuilder ?: return
        val bundle = Bundle(arguments).apply {
            // 媒体类型详情列表页面需要的参数
            this.putString(ALBUM_NAVIGATION_TRACK_PAGE_ID, CUR_PAGE_MEDIA_ALBUM)
            this.putParcelable(KEY_ALBUM_VIEW_DATA, virtualEntryBuilder.getJumpAlbumViewData(typeIndex))
            this.putBundle(KEY_ALBUM_MODEL_BUNDLE, virtualEntryBuilder.getAlbumModelBundle(typeIndex))
        }
        (parentFragment?.parentFragment?.parentFragment as? SelectionPanelDialog)?.also { panelDialog ->
            (createFragmentByPostCard(PostCard(SELECTION_ALBUM_FRAGMENT)) as? SelectionAlbumFragment)?.also { realFragment ->
                realFragment.arguments = bundle
                panelDialog.replacePanel(realFragment)
            }
        } ?: run {
            startByStack<SelectionAlbumFragment>(
                // 路由参数
                startType = FragmentStartType.REPLACE,
                postCard = PostCard(SELECTION_ALBUM_FRAGMENT),
                resId = BasebizR.id.base_fragment_container,
                anim = DEFAULT_ANIM_ARRAY,
                data = bundle
            )
        }
    }

    /**
     * 选择页不需要显示菜单
     */
    override fun initToolbar(view: View) = Unit

    /**
     * 选择页不需要显示菜单
     */
    override fun refreshToolbar() = Unit

    /**
     * 不需要菜单
     */
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        if (!isResumed) return
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        customContext = refreshCustomContext()
        super.onAppUiStateChanged(config)
    }

    private fun refreshCustomContext(): Context {
        return requireContext().createConfigurationContext(
            Configuration(requireContext().resources.configuration).also {
                it.screenWidthDp = getContentWidth().toDp
            }
        )
    }

    override fun refreshLayoutManager(context: Context) {
        super.refreshLayoutManager(customContext)
    }

    /**
     * 不处理menu，menu由SelectionTabFragment处理
     */
    override fun onOptionsItemSelected(item: MenuItem): Boolean = false

    override fun isSidePaneEnabled(): Boolean = false

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : FragmentSystemBarStyle(this) {
        override fun onInit() {
            setMockNaviBarEnable(false)
        }
    }

    companion object {
        private const val TAG = "SelectionAlbumSetTabFragment"
    }
}