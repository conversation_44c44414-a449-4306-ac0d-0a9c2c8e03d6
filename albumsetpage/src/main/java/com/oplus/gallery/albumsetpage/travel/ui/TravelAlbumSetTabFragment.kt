/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: TravelAlbumSetTabFragment
 ** Description: 旅程图集列表 TAB 页面
 **
 ** Version: 1.0
 ** Date: 2025/4/16
 ** Author: ********
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  ********                     2025/4/16        1.0        旅程图集列表 TAB 页面
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.travel.ui

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.forEachIndexed
import androidx.core.view.updatePadding
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.chip.COUIChipGroup
import com.coui.appcompat.viewpager.COUIViewPager2
import com.coui.component.responsiveui.ResponsiveUIModel
import com.coui.component.responsiveui.layoutgrid.MarginType
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.travel.TravelAlbumSetTabAdapter
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.isClose
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.uikit.fragment.IFragmentImmersiveJudger
import com.oplus.gallery.basebiz.uikit.fragment.IMapTravelTabController
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.business_lib.helper.ToolbarHelper
import com.oplus.gallery.business_lib.helper.ToolbarHelper.setupToolbar
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelDBTableHelper
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.app.AppConstants.Number
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as BaseBizR

/**
 * “旅程图集”页面的主页面，管理“全部”和“具体年份” TAB
 */
@RouterNormal(RouterConstants.RouterName.TRAVEL_ALBUM_SET_TAB_FRAGMENT)
open class TravelAlbumSetTabFragment : TemplateFragment(), IFragmentImmersiveJudger {

    /**
     * 纸片组父容器
     */
    private var chipGroupWrapper: View? = null
    /**
     * 纸片组（单选）
     */
    private var chipGroup: COUIChipGroup? = null

    /**
     * 上一次加载的年份组
     */
    private var lastTravelYears = listOf<String>()

    /**
     * 响应式 UI 模型，用来计算栅格相关的参数
     */
    private var responsiveUIModel: ResponsiveUIModel? = null

    /**
     * 是否侧边栏收起
     */
    private val isSidePaneClosed: Boolean
        get() = sidePaneAccessor.isSupportSidePane() && sidePaneAccessor.isClose()

    /**
     * 分组纸片选中状态的监听器
     */
    private val chipGroupCheckedStateChangeListener = COUIChipGroup.OnCheckedStateChangeListener { chipGroup, checkedIds ->
        if (checkedIds.isEmpty()) {
            GLog.w(TAG, LogFlag.DL) { "chipGroupCheckedStateChangeListener checkedIds is null" }
            return@OnCheckedStateChangeListener
        }
        // 这里是单选，所以取第一个就行
        val checkedChipId = checkedIds[Number.NUMBER_0]
        viewPager?.let { pager ->
            chipGroup.forEachIndexed { index, view ->
                if (view.id == checkedChipId) {
                    pager.currentItem = index
                }
            }
        }
    }

    /**
     * 获取父 Fragment 作为 tab 持有者，父 fragment 为 MapTravelTabFragment,如果为空，则表明当前父 Fragment 不是
     */
    private val parentTabController by lazy { parentFragment as? IMapTravelTabController }

    /**
     * 用于判断父 Fragment 中的底部分段按钮是否显示，用户后续控制底部蒙层是否显示
     */
    val parentTabIsShow: Boolean by lazy { parentTabController != null }

    private val fragmentAdapter: TravelAlbumSetTabAdapter by lazy { TravelAlbumSetTabAdapter(childFragmentManager, lifecycle) }
    private var viewPager: COUIViewPager2? = null

    override fun getLayoutId(): Int = R.layout.albumsetpage_travel_album_tab_layout

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        responsiveUIModel = context?.let {
            ResponsiveUIModel(it, resources.displayMetrics.widthPixels, Number.NUMBER_0).chooseMargin(MarginType.MARGIN_LARGE)
        }
        super.doViewCreated(view, savedInstanceState)
        initToolbar()
        chipGroup = view.findViewById(R.id.travel_chip_group)
        chipGroupWrapper = view.findViewById(R.id.chip_group_wrapper)
        chipGroup?.setOnCheckedStateChangeListener(chipGroupCheckedStateChangeListener)
        context?.let { initChipGroup(it) }
        initViewPager(view)
    }

    /**
     * 当删除某个图集所有照片返回到 tab 页时，需要刷新页面
     */
    override fun onResume() {
        super.onResume()
        AppScope.launch(Dispatchers.Default) {
            val travelYears = TravelDBTableHelper.loadTravelAlbumYears().map { TimeUtils.getYearDate(it) }.distinct().sortedDescending()
            if (lastTravelYears == travelYears) {
                GLog.d(TAG, LogFlag.DL, "onResume. no need change. last: $lastTravelYears, cur: $travelYears")
                return@launch
            }
            lastTravelYears = travelYears
            GLog.d(TAG, LogFlag.DL) { "onResume. travelYears: $travelYears, chips: ${chipGroup?.chipCount}" }
            withContext(Dispatchers.Main) {
                if (travelYears.isNotEmpty()) {
                    if (travelYears.size == Number.NUMBER_1) {
                        chipGroupWrapper?.visibility = View.GONE
                    } else {
                        chipGroupWrapper?.visibility = View.VISIBLE
                    }
                    chipGroup?.removeAllChips(false)
                    val fragmentRouterList = mutableMapOf<Int, String>()
                    (layoutInflater.inflate(R.layout.albumsetpage_item_chip_travel, chipGroup, false) as COUIChip).apply {
                        text = context?.getString(BaseBizR.string.main_filter_all)
                        chipGroup?.addChip(this, false)
                        fragmentRouterList[Number.NUMBER_0] = RouterConstants.RouterName.TRAVEL_ALL_FRAGMENT
                    }
                    travelYears.onEach { year ->
                        (layoutInflater.inflate(R.layout.albumsetpage_item_chip_travel, chipGroup, false) as COUIChip).apply {
                            val tabTitle = year + TextUtil.BLANK_STRING + resources.getString(R.string.main_tab_album_set_text_year)
                            text = tabTitle
                            chipGroup?.addChip(this, false)
                            fragmentRouterList[year.toInt()] = RouterConstants.RouterName.TRAVEL_YEAR_FRAGMENT
                        }
                    }
                    fragmentAdapter.setData(fragmentRouterList)
                    chipGroup?.getChipAt(Number.NUMBER_0)?.id?.let { chipGroup?.check(it) }
                } else {
                    exitCurrentFragment()
                }
            }
        }
    }

    private fun initToolbar() {
        setupToolbar(
            isBackEnabled = false,
            topPaddingType = ToolbarHelper.TYPE_TAB,
            isSupportImmersive = true
        )
        toolbar?.let { toolbar ->
            (activity as? AppCompatActivity)?.supportActionBar?.setDisplayHomeAsUpEnabled(true)
            toolbar.isTitleCenterStyle = false
            toolbar.title = getText(com.oplus.gallery.basebiz.R.string.travel_group_title)
        }
        bindToolbarToActivity()
    }

    /**
     * 侧边栏功能启用，支持的情况下回去跳转标题栏的 margin start，避免标题栏和侧边栏图标重叠
     */
    override fun isSidePaneEnabled(): Boolean = sidePaneAccessor.isSupportSidePane()

    /**
     * 初始化 ViewPager，包括禁止触摸事件
     */
    private fun initViewPager(view: View) {
        viewPager = view.findViewById<COUIViewPager2>(R.id.view_pager).apply {
            // 禁用用户触摸事件，目前切换 tab 只能由分组纸片切换
            isUserInputEnabled = false
            adapter = fragmentAdapter
            // 在 FragmentStateAdapter 的 restoreState 方法调用时要确保 mSavedStates 为空，不然这里会报错，无法从下一级页面返回。
            isSaveEnabled = false
            // 页面切换动画的时长，默认值为500 ms，观感上较慢,这里设为100，后面再调整
            duration = VIEWPAGER_ANIMATION_DURATION
        }
    }

    /**
     * 初始化Recycler的布局配置，如边距、间距、header、footer等
     * W < 600dp
     * 侧边栏收起时：
     *      边距：16dp
     *      全部显示 2 列
     *
     * 600dp <= W <= 840dp 全部显示 3 列
     * 侧边栏收起时：
     *      边距：按照栅格，左右缩进各 1 栏
     *      全部显示 3 列
     * 侧边栏展开时：W < 600dp（减去侧边栏宽度）
     *      边距：卡片左右两侧间距为 24dp
     *      全部显示 2 列
     *
     * W > 860dp 全部显示 4 列
     * 侧边栏收起时：
     *      按照栅格 左右缩近各 2 栏
     * 侧边栏展开时：
     *      卡片左右距离两侧间距为 40dp
     */
    private fun initChipGroup(context: Context) {
        val parentWidth = getCurrentAppUiConfig().windowWidth.current
        val responsiveGridNum = context.resources.getInteger(BaseBizR.integer.basebiz_travel_album_set_edge_columns_num)
        val itemEdgeWidth = context.resources.getDimensionPixelSize(BaseBizR.dimen.basebiz_travel_album_set_fragment_item_edge_width)
        val edgeWidth = if (parentWidth < ScreenUtils.dpToPixel(Number.NUMBER_600)) { // 小屏 16dp
            itemEdgeWidth
        } else {
            if (isSidePaneClosed) { // 中大屏，侧边栏收起时，中屏一个栅格宽度，大屏两个栅格宽度
                responsiveUIModel?.calculateGridWidth(responsiveGridNum) ?: itemEdgeWidth
            } else { // 中大屏，侧边栏展开时，中屏 24dp，大屏 40dp
                itemEdgeWidth
            }
        }
        chipGroupWrapper?.updatePadding(
            left = edgeWidth,
            right = edgeWidth
        )
    }

    /**
     * 横竖屏切换需要刷新布局
     */
    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        GLog.d(TAG, LogFlag.DL) { "onAppUiStateChanged,windowWidth:${uiConfig.windowWidth} windowHeight:${uiConfig.windowHeight}" }
        // 屏幕宽有变化都重置ViewData缓存，再重新加载缓存
        if (uiConfig.windowWidth.isChanged() || uiConfig.screenMode.isChanged()) {
            context?.let { initChipGroup(it) }
        }
    }

    /**
     * 侧边栏状态监听
     */
    override fun createSidePaneListener(): ISidePaneListener? {
        return object : ISidePaneListener {
            override fun onSidePaneSlideEnd(newState: SidePaneState) {
                context?.let { initChipGroup(it) }
            }
        }
    }

    /**
     * 检查页面的沉浸式状态，需要在上述各给ViewPager中子Fragment的列表的滚动中调用
     */
    fun checkPageImmersiveState(
        position: Int = 0,
        isForceStart: Boolean = false,
        isNeedAnimation: Boolean = true
    ) {
        GLog.d(TAG, LogFlag.DL, "checkPageImmersiveState mainTabController $parentTabController")
        parentTabController?.checkPageImmersiveState(position, isForceStart, isNeedAnimation)
    }

    /**
     * 当前 fragment 底部是是沉浸态
     */
    override fun isBottomImmersiveCurrently(): Boolean {
        viewPager?.currentItem?.let {
            val fragment = fragmentAdapter.getFragmentWithPosition(it) as? BaseTravelChipAlbumSetFragment
            return fragment?.isBottomImmersiveCurrently() ?: false
        }
        return false
    }

    /**
     * 当前 fragment 顶部是否为沉浸式
     */
    override fun isTopImmersiveCurrently(): Boolean {
        return false
    }

    fun getTravelChipGroupBottomY(): Float {
        return (chipGroupWrapper?.bottom ?: 0).toFloat()
    }

    companion object {
        private const val TAG = "TravelAlbumSetTabFragment"
        private const val VIEWPAGER_ANIMATION_DURATION = 100
    }
}