/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SelectionPreviewBtnFragment.kt
 * Description: 选图的预览按钮
 *
 * Version: 1.0
 * Date: 2022/08/16
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                     <date>          <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery       2022/08/16        1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.albumsetpage.selection.pictures.ui

import android.app.Activity
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.selection.pictures.viewmodel.SelectionPreviewBtnViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SELECTION_PREVIEW_PANEL_DIALOG
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.selection.NotifyState
import com.oplus.gallery.business_lib.model.selection.OnSelectionListener
import com.oplus.gallery.business_lib.selectionpage.ISelectionPage
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_SELECTION_DATA
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.business_lib.selectionpage.SelectionFrom
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.framework.abilities.data.model.EmptyAlbumModel
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.basebiz.R as BasebizR

@RouterNormal(RouterConstants.RouterName.SELECTION_PREVIEW_BTN_FRAGMENT)
class SelectionPreviewBtnFragment : BaseFragment(), View.OnClickListener, OnSelectionListener<Path>, ISelectionPage {
    val viewModel by viewModels<SelectionPreviewBtnViewModel> { defaultViewModelProviderFactory }
    private var btnPreviewSelectedItems: TextView? = null
    private var btnPreviewSelectedSave: Button? = null
    private var layoutOutsidePreviewSelectedItems: ViewGroup? = null
    private var layoutPreviewSelectedItems: ViewGroup? = null
    // 底部与导航栏的间隔
    private var lastButtomBarMargin = 0

    override lateinit var selectInputData: SelectInputData
    override val isPageClickPenetrableEnabled: Boolean = false

    override fun getLayoutId(): Int = R.layout.album_fragment_selection_preview_btn

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectInputData = arguments?.getParcelable(KEY_SELECTION_DATA)!!
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        layoutOutsidePreviewSelectedItems = view.findViewById(R.id.preview_selected_bottom_outside_layout)
        layoutPreviewSelectedItems = view.findViewById(R.id.preview_selected_bottom_layout)
        btnPreviewSelectedItems = view.findViewById(R.id.btn_preview_selected_items)
        btnPreviewSelectedSave = view.findViewById(R.id.btn_save)
        layoutPreviewSelectedItems?.setOnClickListener(this)
        btnPreviewSelectedItems?.setOnClickListener(this)
        btnPreviewSelectedSave?.setOnClickListener(this)

        viewModel.setDataModel(EmptyAlbumModel)
        viewModel.enterSelectionMode(selectInputData.selectionDataId)
        viewModel.registerOnSelectionListener(this)

        layoutOutsidePreviewSelectedItems?.visibility = if (selectInputData.selectMulti) View.VISIBLE else View.GONE
        changeBtnEnable()

        if (activity is SelectionActivity) {
            view.post {
                // 第三方进入选图时慢一点调用，防止获取不到底部导航的高度
                updateButtomBarMargin()
            }
        } else {
            updateButtomBarMargin()
        }
        updateButtomBarHeight()
    }

    /**
     * 面板底部操作栏手势条颜色、边距不一致，添加底部Padding解决
     */
    fun updateButtomBarMargin() {
        val currentButtomBarMargin = getCurrentGestureBarHeight()
        if (currentButtomBarMargin != lastButtomBarMargin) {
            layoutOutsidePreviewSelectedItems?.setPadding(0, 0, 0, currentButtomBarMargin)
            lastButtomBarMargin = currentButtomBarMargin
        }
    }

    /**
     * 面板底部操作栏 （分组列表控件改造），常规屏上下留有 8 dp间距，小横屏，中大屏无此间距
     */
    private fun updateButtomBarHeight() {
        layoutPreviewSelectedItems?.apply {
            if (isLandscapeAndSmallScreen() || ScreenUtils.isMiddleAndLargeScreenWidth(this.context)) {
                val paddingTopAndBottom = resources.getDimensionPixelSize(BasebizR.dimen.base_selection_preview_bottom_bar_padding_top_bottom)
                this.setPadding(this.paddingLeft, paddingTopAndBottom, this.paddingRight, paddingTopAndBottom)
            } else {
                var paddingTopAndBottom = resources.getDimensionPixelSize(BasebizR.dimen.base_selection_preview_bottom_bar_padding_top_bottom)
                val paddingTopAndBottomOffset =
                    resources.getDimensionPixelSize(BasebizR.dimen.base_selection_preview_bottom_bar_padding_top_bottom_offset)
                paddingTopAndBottom += paddingTopAndBottomOffset
                this.setPadding(this.paddingLeft, paddingTopAndBottom, this.paddingRight, paddingTopAndBottom)
            }
            this.requestLayout()
        }
    }

    private fun getCurrentGestureBarHeight(): Int = if (hasVirtualKey()) 0 else bottomNaviBarHeight()

    override fun onClick(v: View) {
        if (DoubleClickUtils.isFastDoubleClick(CLICK_INTERVAL)) return
        val activity = activity ?: return
        when (v.id) {
            R.id.btn_preview_selected_items -> {
                Starter.DialogFragmentStarter<PanelDialog>(
                    fm = activity.supportFragmentManager,
                    bundle = arguments?.also {
                        it.putString(KEY_REQUEST_KEY, REQUEST_KEY_SELECT_SAVE)
                    },
                    postCard = PostCard(SELECTION_PREVIEW_PANEL_DIALOG),
                ).start()?.also { dialog ->
                    dialog.setFragmentResultListener(REQUEST_KEY_SELECT_SAVE) { _, bundle ->
                        if (bundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) return@setFragmentResultListener
                        (parentFragment as? SelectionPanelDialog)?.let {
                            it.resultBundle = bundle
                            it.dismiss()
                        }
                    }
                }
            }
            R.id.btn_save -> {
                if (selectInputData.fromWhere == SelectionFrom.EXTERNAL) {
                    (activity as? SelectionActivity)?.run { dealSave() }
                    return
                }
                (parentFragment as? SelectionPanelDialog)?.dealSave()
            }
            else -> GLog.d(TAG, "onClick: no response $v")
        }
    }

    private fun changeBtnEnable() {
        val hasSelected  = viewModel.getSelectedItemCount() > 0
        btnPreviewSelectedItems?.isEnabled = hasSelected
        btnPreviewSelectedSave?.isEnabled = hasSelected
        changePreviewSelectedText(viewModel.getSelectedItemCount())
    }

    private fun changePreviewSelectedText(count: Int) {
        val baseString = if ((count == 0) || !selectInputData.selectMulti) {
            resources.getString(com.oplus.gallery.basebiz.R.string.base_selection_preview_btn_text)
        } else {
            resources.getQuantityString(BasebizR.plurals.base_selection_preview_btn_text_with_counts, count, count)
        }
        btnPreviewSelectedItems?.let {
            it.text = baseString
        }
    }

    override fun onSelectItemsChange(positions: Set<Int>, selected: Boolean)  = Unit

    override fun onSelectItemChange(position: Int, selected: Boolean) = Unit

    override fun onSelectionChange(item: Path?, state: NotifyState) {
        changeBtnEnable()
        changePreviewSelectedText(viewModel.getSelectedItemCount())
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        if (uiConfig.accentColor.isChanged()) {
            (btnPreviewSelectedSave as? COUIButton)?.refresh()
        }
        //横竖屏切换 或者 大小屏切换后
        if (uiConfig.windowWidth.isChanged() || uiConfig.windowHeight.isChanged()) {
            updateButtomBarHeight()
        }
    }

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        super.onSystemBarChanged(windowInsets)
        updateButtomBarMargin()
    }

    override fun onDestroy() {
        viewModel.exitSelectionMode()
        super.onDestroy()
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : FragmentSystemBarStyle(this) {
        override fun onInit() {
            setMockNaviBarEnable(parentFragment !is PanelFragment)
            setNaviBarColor(Color.TRANSPARENT)
        }

        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            setNaviBarColor(Color.TRANSPARENT)

            if (parentFragment is PanelDialog) return
            setStatusBarAppearance(!COUIDarkModeUtil.isNightMode(context))
            windowInsets.naviBarInsets().apply {
                val bottomPadding: Int = if (hasVirtualKey()) bottom else 0
                setContentPadding(left, top, right, bottomPadding)
            }
        }
    }

    /**
     * 当前是否是小横屏状态
     */
    private fun isLandscapeAndSmallScreen(): Boolean {
        val config = getCurrentAppUiConfig()
        val isLandscape = config.orientation.current == Configuration.ORIENTATION_LANDSCAPE
        val isSmallScreen = config.screenMode.current == AppUiResponder.ScreenMode.SMALL
        return isLandscape && isSmallScreen
    }

    companion object {
        private const val TAG = "SelectionPreviewBtnFragment"

        private const val REQUEST_KEY_SELECT_SAVE = "selectionPreviewSave.requestKey"
        private const val CLICK_INTERVAL = 500
    }
}
