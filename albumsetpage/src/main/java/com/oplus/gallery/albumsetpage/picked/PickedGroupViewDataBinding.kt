/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PickedGroupViewDataBinding.kt
 ** Description: 精选图集组
 ** Version: 1.0
 ** Date: 2025/4/3
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2025/4/3     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.albumsetpage.picked

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.InsetDrawable
import android.graphics.drawable.LayerDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GalleryLinearLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.allalbum.item.PickedGroupViewData
import com.oplus.gallery.albumsetpage.picked.viewmodel.PickedAlbumSetViewModel
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.LABEL_FRAGMENT
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.MAP_TRAVEL_FRAGMENT
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.MEMORIES_ALBUM_SET_FRAGMENT
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.PERSON_PET_ALBUM_SET_FRAGMENT
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.TRAVEL_ALBUM_SET_TAB_FRAGMENT
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.activity.findMapContainerId
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentStartType
import com.oplus.gallery.basebiz.uikit.fragment.base.IFragmentStack
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_LABEL
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_MEMORIES
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_PERSON_PET
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_TRAVEL
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.business_lib.BusinessLibHelper.isPositiveOrder
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.location.set.MapLocationAlbum
import com.oplus.gallery.business_lib.ui.fragment.BaseTimeNodeFragment
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.updateMarginRelative
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.framework.abilities.map.IMapAbility
import com.oplus.gallery.framework.abilities.map.MapConstants
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.baselist.view.BaseViewDataBinding
import com.oplus.gallery.standard_lib.baselist.view.BaseViewHolder
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_SUB_STYLE
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_BACKGROUND
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_BACKGROUND_DEFAULT
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_BACKGROUND_PAINT_COLOR
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_LAYOUT_CORNER_RADIUS
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_LAYOUT_GAP_BETWEEN_CHILD
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_LAYOUT_PADDING_BORDER
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_LAYOUT_STYLE
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_SIZE_HEIGHT
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_SIZE_TYPE
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_SIZE_WIDTH
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_STROKE_PAINT_COLOR
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.KEY_THUMB_STROKE_WIDTH
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.STYLE_CIRCLE
import com.oplus.gallery.standard_lib.graphics.StyleData.Companion.STYLE_ROUND
import com.oplus.gallery.basebiz.R as BasebizR
import com.oplus.gallery.foundation.ui.R as LibUIR

/**
 * 精选图集组
 */
class PickedGroupViewDataBinding(
    context: Context,
    private val activity: FragmentActivity?,
    private val fragment: BaseFragment?,
    private val onStoreOwnerGet: (() -> ViewModelStoreOwner),
    private val onLifecycleOwnerGet: (() -> LifecycleOwner),
    private val onParentWidthGet: (() -> Int),
    private val isInterceptItemClick: (() -> Boolean),
) : BaseViewDataBinding<PickedGroupViewData, BaseViewHolder<PickedGroupViewData>>(context) {

    private val pickedAlbumSetViewModel by lazy {
        ViewModelProvider(onStoreOwnerGet())[PickedAlbumSetViewModel::class.java].apply {
            onSetUpViewModelStyle(this)
        }
    }
    private val albumSetAdapter: PickedAlbumSetAdapter by lazy {
        PickedAlbumSetAdapter(context, pickedItemClickListener, onParentWidthGet, ::getEmptyDrawable)
    }
    private var pickedTitleTextView: COUITextView? = null
    private var pickedAlbumSetRecyclerView: COUIRecyclerView? = null
    private var pickedGroupItemDecoration: PickedGroupItemDecoration? = null
    /**
     * 是否第一次加载数据
     */
    private var isFirstLoadData = true
    /**
     * 精选item点击事件
     */
    private val pickedItemClickListener = object : PickedAlbumSetItemClickedListener {
        override fun onPickedAlbumSetItemClick(pickedAlbumSetType: PickedAlbumSetType, clickedItemView: View?) {
            if (isInterceptItemClick()) {
                GLog.d(TAG, "pickedItemClickListener, out view scroll, pickedAlbumSetType=$pickedAlbumSetType")
                return
            }
            when (pickedAlbumSetType) {
                PickedAlbumSetType.LABEL -> startByStack(LABEL_FRAGMENT, clickedItemView, ENTRANCE_LABEL)

                PickedAlbumSetType.PERSON_PET -> startByStack(PERSON_PET_ALBUM_SET_FRAGMENT, clickedItemView, ENTRANCE_PERSON_PET)

                PickedAlbumSetType.TRAVEL -> processToJumpMapTravelOrOnlyTravel(clickedItemView)

                PickedAlbumSetType.MEMORIES -> startByStack(MEMORIES_ALBUM_SET_FRAGMENT, clickedItemView, ENTRANCE_MEMORIES)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<PickedGroupViewData> {
        GLog.d(TAG, LogFlag.DL) { "[onCreateViewHolder] on create view holder." }
        val view = LayoutInflater.from(parent.context).inflate(R.layout.album_group_picked_item, parent, false).apply {
            pickedTitleTextView = findViewById(R.id.tv_title)
            pickedAlbumSetRecyclerView = findViewById(R.id.picked_album_set_recycler_view)
        }
        pickedAlbumSetViewModel.pickedBindingLiveData.observe(onLifecycleOwnerGet()) { listData ->
            GLog.d(TAG, LogFlag.DL) { "onCreateViewHolder:observe size=${listData.size} isFirst=$isFirstLoadData" }
            albumSetAdapter.updateData(listData)
            if (isFirstLoadData) {
                pickedTitleTextView?.isVisible = true
                isFirstLoadData = false
            }
        }

        pickedAlbumSetRecyclerView?.apply {
            adapter = albumSetAdapter
            pickedGroupItemDecoration = PickedGroupItemDecoration(
                LinearLayoutManager.HORIZONTAL,
                ResourceUtils.isRTL(context),
                context.resources.getDimensionPixelSize(R.dimen.picked_album_set_item_gap),
                context.resources.getDimensionPixelSize(R.dimen.picked_album_set_item_edgwidth)
            )
            pickedGroupItemDecoration?.let { addItemDecoration(it) }
            layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            setItemClickableWhileOverScrolling(false)
            setItemClickableWhileSlowScrolling(false)
        }
        pickedAlbumSetViewModel.refreshData("onCreate")
        return BaseViewHolder(view, this)
    }

    /**
     * 获取空图集的drawable
     */
    private fun getEmptyDrawable(bindingData: PickedBindingData): Drawable? {
        return bindingData.pickedAlbumType?.let { pickedAlbumSetViewModel.getEmptyDrawable(it) }
    }

    override fun onBindViewHolder(itemViewHolder: BaseViewHolder<PickedGroupViewData>, position: Int, viewData: PickedGroupViewData?) = Unit

    override fun copyLayout(context: Context): BaseViewDataBinding<PickedGroupViewData, BaseViewHolder<PickedGroupViewData>> = this

    fun notifyLayoutChanged() {
        pickedAlbumSetRecyclerView?.let {
            pickedGroupItemDecoration?.let { itemDecoration -> it.removeItemDecoration(itemDecoration) }
            pickedGroupItemDecoration = PickedGroupItemDecoration(
                LinearLayoutManager.HORIZONTAL,
                ResourceUtils.isRTL(context),
                context.resources.getDimensionPixelSize(R.dimen.picked_album_set_item_gap),
                context.resources.getDimensionPixelSize(R.dimen.picked_album_set_item_edgwidth)
            )
            pickedGroupItemDecoration?.let { itemDecoration ->
                it.addItemDecoration(itemDecoration)
            }
        }
        albumSetAdapter.notifyDataSetChanged()
        val marginHorizontal = context.resources.getDimensionPixelOffset(R.dimen.picked_album_set_horizontal_margin)
        pickedTitleTextView?.updateMarginRelative(start = marginHorizontal, end = marginHorizontal)
    }

    private fun startByStack(routerName: String, clickedItemView: View?, entrance: Int) {
        fragment ?: return
        /**
         * 当前ViewDataBing所在的 Fragment 是被 TabFragment 包裹，转场动效是回调的 TabFragment 的 onCreateAnimation() 方法
         * 所以这里需要修改 parentFragment 中记录的动效的方向信息
         */
        (fragment.parentFragment as? BaseFragment)?.resetSeamlessAnimRootNodeConfig(
            fragment.resources.configuration.orientation,
            fragment.resources.configuration.screenWidthDp,
            fragment.resources.configuration.screenHeightDp
        )
        val floatingWindowOffset = IntArray(2)
        if (fragment.isFloatingWindowMode()) fragment.view?.rootView?.getLocationOnScreen(floatingWindowOffset)
        activity?.supportFragmentManager?.start(
            startType = FragmentStartType.REPLACE,
            postCard = PostCard(routerName),
            resId = BasebizR.id.base_fragment_container,
            anim = SEAMLESS_ANIM_ARRAY,
            addToBackStack = true,
            fragmentStack = activity as IFragmentStack,
            data = Bundle().apply {
                FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                    context,
                    this,
                    clickedItemView,
                    TriggerViewRectGetter(clickedItemView, floatingWindowOffset),
                    entrance,
                    context.resources.getDimension(R.dimen.picked_album_set_gird_cover_corner_radius),
                    R.id.image_album_set_cover
                )
            }
        )
    }


    private fun processToJumpMapTravelOrOnlyTravel(clickedItemView: View?) {
        //判断是否支持地图
        val mapAbility = context.getAppAbility<IMapAbility>()
        val supportMap = mapAbility?.use {
            it.isMapSupported()
        } ?: false
        if (supportMap) {
            //支持地图时，跳转MapTravelTabFragment，默认跳转到Travel页
            jumpToMapTravelTabFragment(clickedItemView)
        } else {
            //不支持地图时，直接启动单个TravelAlbumSetTabFragment
            jumpToTravelPageImpl(TRAVEL_ALBUM_SET_TAB_FRAGMENT, clickedItemView, Bundle())
        }
    }

    private fun jumpToMapTravelTabFragment(clickedItemView: View?) {
        val bundle = Bundle().apply {
            //默认跳转travel页
            putBoolean(IntentConstant.AlbumConstant.KEY_TARGET_MAP, false)
            //准备Map页的bundle数据
            val path = SourceConstants.Local.PATH_ALBUM_LOCATION_TIMENODE.getChild(Path.SEGMENT_ALL)
            val mapLocationAlbum: MapLocationAlbum =
                DataManager.getMediaObject(path) as MapLocationAlbum
            mapLocationAlbum.setOrder(mapLocationAlbum.getDefaultOrder(isPositiveOrder()))
            putString(
                IntentConstant.AlbumConstant.KEY_MEDIA_PATH,
                mapLocationAlbum.path.toString()
            )
            putString(BaseTimeNodeFragment.MAP_PAGE_TITLE, context.getString(com.oplus.gallery.basebiz.R.string.main_map))
            //设置进入mapView时的默认缩放比例
            putFloat(IntentConstant.AlbumConstant.KEY_MAP_DEFAULT_ZOOM, MapConstants.ZOOM_LEVEL_5KM)
        }
        AlbumsActionTrackHelper.trackAndSendEnterMapPage(AlbumsActionTackConstant.Value.ALBUM_ACTION_ENTER_MAP_FROM_TRAVEL)
        jumpToTravelPageImpl(MAP_TRAVEL_FRAGMENT, clickedItemView, bundle)
    }

    private fun jumpToTravelPageImpl(targetPageRouterName: String, clickedItemView: View?, bundle: Bundle) {
        fragment ?: return
        (fragment.parentFragment as? BaseFragment)?.resetSeamlessAnimRootNodeConfig(
            fragment.resources.configuration.orientation,
            fragment.resources.configuration.screenWidthDp,
            fragment.resources.configuration.screenHeightDp
        )
        val floatingWindowOffset = IntArray(2)
        if (fragment.isFloatingWindowMode()) fragment.view?.rootView?.getLocationOnScreen(floatingWindowOffset)
        activity?.supportFragmentManager?.start(
            postCard = PostCard(targetPageRouterName),
            resId = activity.findMapContainerId(),
            anim = SEAMLESS_ANIM_ARRAY,
            addToBackStack = true,
            fragmentStack = activity as IFragmentStack,
            data = bundle.apply {
                FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                    context,
                    this,
                    clickedItemView,
                    TriggerViewRectGetter(clickedItemView, floatingWindowOffset),
                    ENTRANCE_TRAVEL,
                    context.resources.getDimension(R.dimen.picked_album_set_gird_cover_corner_radius),
                    R.id.image_album_set_cover
                )
            }
        )
    }

    private fun onSetUpViewModelStyle(viewModel: PickedAlbumSetViewModel) {
        val itemWidth = context.resources.getDimension(R.dimen.picked_album_set_item_width).toInt()
        val thumbStyleData = StyleData().apply {
            put(KEY_THUMB_LAYOUT_STYLE, STYLE_ROUND)
            put(KEY_THUMB_SIZE_TYPE, ThumbnailSizeUtils.getMicroThumbnailKey(itemWidth))
            put(KEY_THUMB_LAYOUT_CORNER_RADIUS, context.resources.getDimension(R.dimen.picked_album_set_gird_cover_corner_radius))
            put(KEY_THUMB_STROKE_WIDTH, context.resources.getDimension(R.dimen.picked_album_set_round_drawable_frame_stroke_width))
            put(KEY_THUMB_STROKE_PAINT_COLOR, context.resources.getColor(BasebizR.color.common_round_drawable_frame_stroke_color, null))
            put(KEY_THUMB_BACKGROUND_PAINT_COLOR, COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCardBackground))
            put(KEY_THUMB_BACKGROUND, ColorDrawable(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCardBackground)))
        }
        val personPetThumbStyleData = getPersonPetThumbnailStyle(thumbStyleData, itemWidth)
        val travelThumbStyleData = getTravelThumbnailStyle(thumbStyleData, itemWidth)
        val memoriesThumbStyleData = travelThumbStyleData.copy().apply {
            put(
                KEY_THUMB_BACKGROUND_DEFAULT, LayerDrawable(
                    arrayOf(
                        ColorDrawable(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCardBackground)),
                        InsetDrawable(
                            context.resources.getDrawable(R.drawable.album_picked_memories_empty_status, context.theme),
                            context.resources.getDimensionPixelSize(R.dimen.picked_album_set_gird_cover_corner_radius)
                        )
                    )
                )
            )
        }
        val labelThumbStyleData = travelThumbStyleData.copy().apply {
            put(
                KEY_THUMB_BACKGROUND_DEFAULT, LayerDrawable(
                    arrayOf(
                        ColorDrawable(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCardBackground)),
                        InsetDrawable(
                            context.resources.getDrawable(R.drawable.album_picked_label_empty_status, context.theme),
                            context.resources.getDimensionPixelSize(R.dimen.picked_album_set_gird_cover_corner_radius)
                        )
                    )
                )
            )
        }

        viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, thumbStyleData)
        viewModel.addStyle(StyleType.TYPE_PICKED_PERSON_PET_THUMB_STYLE, personPetThumbStyleData)
        viewModel.addStyle(StyleType.TYPE_PICKED_TRAVEL_THUMB_STYLE, travelThumbStyleData)
        viewModel.addStyle(StyleType.TYPE_PICKED_MEMORIES_THUMB_STYLE, memoriesThumbStyleData)
        viewModel.addStyle(StyleType.TYPE_PICKED_LABEL_THUMB_STYLE, labelThumbStyleData)
    }

    /**
     * 人物与宠物封面缩略图样式
     */
    private fun getPersonPetThumbnailStyle(thumbStyleData: StyleData, itemWidth: Int): StyleData {
        return thumbStyleData.copy().apply {
            put(KEY_THUMB_LAYOUT_GAP_BETWEEN_CHILD, context.resources.getDimension(R.dimen.picked_album_set_grid_child_thumbnail_rect_gap))
            put(KEY_THUMB_LAYOUT_PADDING_BORDER, context.resources.getDimension(R.dimen.picked_album_set_grid_child_rect_padding))
            put(KEY_THUMB_SIZE_WIDTH, itemWidth)
            put(KEY_THUMB_SIZE_HEIGHT, itemWidth)
            put(
                KEY_THUMB_BACKGROUND_DEFAULT, LayerDrawable(
                    arrayOf(
                        ColorDrawable(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCardBackground)),
                        InsetDrawable(
                            context.resources.getDrawable(R.drawable.album_picked_person_pet_empty_status, context.theme),
                            context.resources.getDimensionPixelSize(R.dimen.picked_album_set_gird_cover_corner_radius)
                        )
                    )
                )
            )
            // 人物封面由四张缩图构成，subStyleData为四张缩图所用的样式
            put(KEY_SUB_STYLE, thumbStyleData.copy().apply {
                put(KEY_THUMB_LAYOUT_STYLE, STYLE_CIRCLE)
                put(KEY_THUMB_SIZE_TYPE, ThumbnailSizeUtils.TYPE_FACE_THUMBNAIL)
                put(
                    KEY_THUMB_BACKGROUND_PAINT_COLOR,
                    COUIContextUtil.getAttrColor(context, LibUIR.attr.gColorPressBackground)
                )
                put(
                    KEY_THUMB_BACKGROUND,
                    ColorDrawable(COUIContextUtil.getAttrColor(context, LibUIR.attr.gColorPressBackground))
                )
            })
        }
    }

    /**
     * 旅程封面缩略图样式
     */
    private fun getTravelThumbnailStyle(styleData: StyleData, itemWidth: Int): StyleData {
        return styleData.copy().apply {
            put(KEY_THUMB_LAYOUT_GAP_BETWEEN_CHILD, context.resources.getDimension(R.dimen.picked_album_set_grid_child_thumbnail_rect_gap))
            put(KEY_THUMB_LAYOUT_PADDING_BORDER, context.resources.getDimension(R.dimen.picked_album_set_grid_child_rect_padding))
            put(KEY_THUMB_SIZE_WIDTH, itemWidth)
            put(KEY_THUMB_SIZE_HEIGHT, itemWidth)
            put(
                KEY_THUMB_BACKGROUND_DEFAULT, LayerDrawable(
                    arrayOf(
                        ColorDrawable(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCardBackground)),
                        InsetDrawable(
                            context.resources.getDrawable(R.drawable.album_picked_travel_empty_status, context.theme),
                            context.resources.getDimensionPixelSize(R.dimen.picked_album_set_gird_cover_corner_radius)
                        )
                    )
                )
            )
            // 旅程封面由四张缩图构成，subStyleData为四张缩图所用的样式
            put(KEY_SUB_STYLE, styleData.copy().apply {
                put(KEY_THUMB_LAYOUT_CORNER_RADIUS, context.resources.getDimension(R.dimen.picked_album_set_grid_child_thumbnail_corner_radius))
                put(
                    KEY_THUMB_BACKGROUND_PAINT_COLOR,
                    COUIContextUtil.getAttrColor(context, LibUIR.attr.gColorPressBackground)
                )
                put(
                    KEY_THUMB_BACKGROUND,
                    ColorDrawable(COUIContextUtil.getAttrColor(context, LibUIR.attr.gColorPressBackground))
                )
            })
        }
    }

    companion object {
        private const val TAG = "PickedGroupViewDataBinding"
    }
}