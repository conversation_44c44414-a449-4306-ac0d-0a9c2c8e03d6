/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonPetGroupAlbumSetFragment.kt
 ** Description: 人宠群组列表页：人物与宠物列表页点击群组右方的更多进入的群组列表页
 **
 ** Version: 1.0
 ** Date: 2025/04/14
 ** Author: xiaxudong@OppoGallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiaxudong@OppoGallery3D        2025/04/14   1.0          init
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.personpet.ui

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.animation.Animation
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.checkbox.COUICheckBox.SELECT_ALL
import com.coui.appcompat.checkbox.COUICheckBox.SELECT_NONE
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.personpet.PersonPetGroupAlbumSetViewDataBinding
import com.oplus.gallery.albumsetpage.personpet.PersonPetGroupViewData
import com.oplus.gallery.albumsetpage.personpet.viewmodel.PersonPetGroupAlbumSetViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.sidepane.SidePaneAnimationConfig
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.SidePaneWithAlbumAnimation
import com.oplus.gallery.basebiz.sidepane.adjustControlIconColor
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isClose
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_PERSON_PET_SECOND
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_CLOSE_ENTER
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_OPEN_EXIT
import com.oplus.gallery.basebiz.uikit.fragment.transition.SlideTransitionAnimation.Companion.SLIDE_CLOSE_ENTER_ANIMATION
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants
import com.oplus.gallery.business_lib.model.data.person.set.PersonAlbum
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATED_GROUP_EXIST
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATE_EMPTY_GROUP
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_FAIL
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_SUCCESS
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PERSON_PET_GROUP_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.pet.set.PetAlbum
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA_LIST
import com.oplus.gallery.business_lib.selectionpage.SelectAlbumInputData
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumSetFragment
import com.oplus.gallery.business_lib.viewmodel.AlbumSetViewModel.Companion.SELECTED_SIZE_ONE
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.ui.dialog.SingleEditDialog
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.onWidthChanged
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.display.ScreenUtils.isLand
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonPetModelGetter.Companion.TYPE_PERSON_PET_ALBUM_SET
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.Number
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.toolbar.actionview.MenuButton
import com.oplus.gallery.standard_lib.ui.toolbar.actionview.MenuCheckButton
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 人宠群组列表页：人物与宠物列表页点击群组右方的更多进入的群组列表页
 */
@RouterNormal(RouterConstants.RouterName.PERSON_PET_GROUP_ALBUM_SET_FRAGMENT)
class PersonPetGroupAlbumSetFragment : BaseAlbumSetFragment() {

    override val needExitWhenEmpty: Boolean
        get() = true

    /**
     * 选择菜单
     * 在没有数据时置灰
     */
    private var selectMenu: MenuItem? = null

    /**
     * 全选按钮
     */
    private var selectAllCheckButton: MenuCheckButton? = null

    /**
     * 取消按钮
     */
    private var cancelButton: MenuButton? = null

    /**
     * 是否侧边栏收起
     */
    private val isSidePaneClosed: Boolean
        get() = sidePaneAccessor.isSupportSidePane() && sidePaneAccessor.isClose()

    /**
     * 侧边栏展开收起动画联动基础列表动画管理
     */
    private var sidePaneAnim: SidePaneWithAlbumAnimation? = null

    /**
     * 合照重命名弹框
     */
    private var groupNameEditDialog: SingleEditDialog? = null

    /**
     * 重命名弹窗确认按钮监听
     */
    private val mListener: SingleEditDialog.ConfirmListener = object : SingleEditDialog.ConfirmListener {
        override fun onSaved(text: String?) {
            text ?: run {
                GLog.w(logTag, LogFlag.DL, "onSaved, text is null")
                return
            }
            AppScope.launch(Dispatchers.IO) { renameGroup(text) }
            groupNameEditDialog?.dismiss()
            groupNameEditDialog = null
            AlbumsActionTrackHelper.trackAndSendMenuOperationClickForPet(changeName = TextUtil.EMPTY_STRING)
            // 重命名后需要退出选择模式
            exitSelectionMode()
        }

        override fun onCancelled() {
            groupNameEditDialog?.dismiss()
            groupNameEditDialog = null
        }

        private fun renameGroup(text: String) {
            val groupId = (baseListViewModel as? PersonPetGroupAlbumSetViewModel)?.getSelectedAlbumGroupId() ?: 0L
            ApiDmManager.getScanDM().renameGroup(ContextGetter.context, groupId, text, TYPE_PERSON_PET_GROUP_ALBUM_SET)
        }
    }

    /**
     * 横竖屏切换后，需要刷新列表布局
     */
    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        config.onWidthChanged {
            groupNameEditDialog?.updateIfNeed()
            context?.let { refreshLayoutManager(it) }
        }
    }

    /**
     * 设置标题栏标题：合照
     */
    override fun refreshToolbar() {
        super.refreshToolbar()
        if (showInternalToolbar) {
            toolbar?.setTitle(BasebizR.string.group_photo)
        }
    }

    /**
     * 底部菜单栏：重命名、解散合照
     */
    override fun getBottomBarMenuId(): Int = R.menu.albumsetpage_selection_person_pet_group_album_set_split_tab

    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> =
        ViewModelProvider(this)[PersonPetGroupAlbumSetViewModel::class.java].apply { tagPrefix = TAG_PREFIX }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        baseListViewModel?.setViewData(
            AlbumViewData(
                id = SourceConstants.Local.PATH_SET_PERSON_PET_GROUP_ANY.toString(),
                position = 0,
                modelType = DataRepository.PersonPetModelGetter.TYPE_PERSON_PET_GROUP_ALBUM_SET,
                isMediaAlbum = true,
                version = 0,
                totalCount = 0,
                title = ""
            )
        )
        // 当底部菜单栏出现时，需要更新列表底部 Padding
        needBottomPaddingWhenEdit = true
        baseListViewModel?.totalSizeLiveData?.observe(viewLifecycleOwner) { totalSize ->
            selectMenu?.let { it.isEnabled = totalSize > Number.NUMBER_0 }
        }
        (recyclerView as? COUIRecyclerView)?.let {
            it.setItemClickableWhileSlowScrolling(false)
            it.setItemClickableWhileOverScrolling(false)
        }
    }

    /**
     * 设置列表样式
     */
    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        super.onSetUpViewModelStyle(viewModel)
        context?.let {
            val roundThumbStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
                put(
                    StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS,
                    it.resources.getDimension(R.dimen.albumsetpage_person_pet_group_album_set_item_corner_radius)
                )
                put(StyleData.KEY_THUMB_SIZE_TYPE, ThumbnailSizeUtils.TYPE_THUMBNAIL)
                put(StyleData.KEY_THUMB_STROKE_WIDTH, it.resources.getDimension(R.dimen.albumsetpage_person_pet_group_album_set_item_stroke_width))
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, it.getColor(R.color.albumsetpage_person_pet_group_item_drawable_frame_stroke_color))
                put(StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR, COUIContextUtil.getColor(it, R.color.albumsetpage_person_pet_group_bg_color))
            }
            viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, roundThumbStyleData)
        }
    }

    /**
     * 初始化Recycler的布局配置，如边距、间距、header、footer等
     *
     * 列表距离标题栏: 16dp. itemTopGap(8dp)+toolbarGap(8dp)
     * 列表 Item gap vertical：16dp. itemTopGap(8dp)+itemBottomGap(8dp)
     */
    override fun initLayoutDetail(context: Context): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        parentWidth = getContentWidth()
        edgeWidth = context.resources.getDimensionPixelSize(R.dimen.albumsetpage_person_pet_group_album_set_item_edge_margin)
        gapWidth = context.resources.getDimensionPixelSize(R.dimen.albumsetpage_person_pet_group_album_set_item_gap_horizontal)
        spanCount = if (ScreenUtils.isMiddleScreenWidth(context)) { // 中屏
            // 正常中屏（折叠屏展开、平板竖屏）和小屏横屏
            if (isSidePaneClosed || context.isLand()) {
                Number.NUMBER_2
            } else {
                Number.NUMBER_1
            }
        } else if (ScreenUtils.isLargeScreenWidth(context)) {
            if (isSidePaneClosed) {
                Number.NUMBER_3
            } else {
                Number.NUMBER_2
            }
        } else {
            context.resources.getInteger(BasebizR.integer.basebiz_person_pet_group_columns_num)
        }
    }.build().apply {
        itemDecorationGapPx.top = context.resources.getDimension(R.dimen.albumsetpage_person_pet_group_album_set_item_gap_vertical)
        itemDecorationGapPx.bottom = context.resources.getDimension(R.dimen.albumsetpage_person_pet_group_album_set_item_gap_vertical)
    }

    private fun getListSpanCount(): Int = (recyclerView?.layoutManager as? GridLayoutManager)?.spanCount ?: 1

    private fun getListItemWidth(): Int {
        return (recyclerView?.layoutManager as? GridLayoutManager)?.getChildAt(0)?.width ?: 0
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        GLog.d(logTag, LogFlag.DL) { "onSidePaneSlideStart: isResumed:$isResumed newState=$newState $clazzSimpleName" }
        if (isResumed && newState.isFlat()) {
            sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint())
            val currentSpanCount = getListSpanCount()
            context?.let {
                refreshLayoutManager(it)
            }
            sidePaneAnim?.startAnimation(
                SidePaneAnimationConfig(
                    currentSpanCount = currentSpanCount,
                    nextSpanCount = getListSpanCount(),
                    currentItemWidth = getListItemWidth(),
                    nextItemWidth = layoutDetail.itemWidth,
                    slideWidth = sidePaneAccessor.getSlideWidth(),
                    gapWidth = resources.getDimensionPixelOffset(com.oplus.gallery.basebiz.R.dimen.base_album_fragment_item_view_horizontal_gap),
                    newState = newState
                )
            )
        }
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        sidePaneAnim?.animating(newState, slideProgress)
    }

    /**
     * 按item类型,配置对应的viewDataBinding
     * 如：AlbumViewData->AlbumViewDataBinding,
     *    CardCaseBannerViewData->CardCaseBannerViewDataBinding
     * @return List<ItemConfig<TViewData>>
     */
    override fun getAdapterItemConfigs(): List<ItemConfig<AlbumViewData>> {
        return mutableListOf<ItemConfig<AlbumViewData>>().apply {
            context?.let { context ->
                add(
                    ItemConfig(
                        PersonPetGroupViewData::class.java,
                        PersonPetGroupAlbumSetViewDataBinding(
                            context,
                            stylePool = baseListViewModel,
                            isMaskVisibleGet = { isMaskVisible() },
                            checkboxAnimEnableGet = { checkboxAnimEnable() }
                        )
                    )
                )
            }
        }
    }

    override fun dispatchLongClick(position: Int): Boolean {
        return isInSelectionMode()
    }

    /**
     * 合照图集列表点击某个图集
     * 分为选中状态和普通状态
     * 普通状态：进入图集详情
     * 选中状态：弹出菜单
     */
    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
        if (isInSelectionMode()) {
            viewData?.apply {
                baseListViewModel?.toggleItemSelection(this.position)
            }
        } else {
            resetSeamlessAnimRootNodeConfig(
                resources.configuration.orientation,
                resources.configuration.screenWidthDp,
                resources.configuration.screenHeightDp
            )
            val floatingWindowOffset = IntArray(2)
            if (isFloatingWindowMode()) view?.rootView?.getLocationOnScreen(floatingWindowOffset)
            val clickedItemView = recyclerAdapter.getItemByIndex(position)
            startByStack<PersonPetDetailAlbumFragment>(
                resId = BasebizR.id.base_fragment_container,
                fragmentClass = PersonPetDetailAlbumFragment::class.java,
                anim = SEAMLESS_ANIM_ARRAY,
                data = Bundle().also {
                    it.putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
                    FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                        context,
                        it,
                        clickedItemView,
                        TriggerViewRectGetter(clickedItemView, floatingWindowOffset),
                        ENTRANCE_PERSON_PET_SECOND
                    )
                }
            )
        }
    }

    override fun onFragmentTransitionStart(animation: Animation, animId: Int) {
        super.onFragmentTransitionStart(animation, animId)
        /**
         * 解散合照时，会自动关闭合照详情页，回到人宠详情页。因为ViewModel中的contentChangeListener触发的时机比转场动画早，
         * 而contentChangeListener又会触发重新刷新数据，所以需要在当前页面打开合照详情页时，自己退场的时候就关闭对 contentChange 的响应，
         * 当重新回到当前页后，在onResume() 中触发重新加载数据
         */
        if (animId == SEAMLESS_ANIM_OPEN_EXIT) {
            baseListViewModel?.needSkipRefreshData = true
        }
    }

    override fun onFragmentTransitionEnd(animation: Animation, animId: Int) {
        super.onFragmentTransitionEnd(animation, animId)
        if ((animId == SEAMLESS_ANIM_CLOSE_ENTER)
            || (animId == SLIDE_CLOSE_ENTER_ANIMATION)) {
            baseListViewModel?.needSkipRefreshData = false
        }
    }

    /**
     * 选择数量变化，会触发全选按钮状态变化和底部操作按钮状态变化
     */
    override fun onSelectionStateChange() {
        super.onSelectionStateChange()
        baseListViewModel?.let { viewModel ->
            val selectedCount = viewModel.getSelectedItemCount()
            if (isInSelectionMode()) {
                updateToolbarEditTitle(
                    defaultTitleResId = BasebizR.string.base_title_select_image,
                    count = selectedCount
                )
                updateSelectAllButton()
            }
            updateMenuBySelectCount(selectedCount)
            if ((selectedCount == viewModel.totalSize) || (selectedCount == 0)) {
                recyclerAdapter.notifyItemRangeChanged(0, recyclerAdapter.itemCount)
            }
        }
    }

    /**
     * 根据选中项的数量更新菜单状态，单选支持重命名，多选态不支持重命名
     * @param selectedCount 选中项个数
     */
    private fun updateMenuBySelectCount(selectedCount: Int) {
        bottomMenuBar?.menu?.let { menu ->
            changeMenuItemEnable(menu.findItem(R.id.action_rename), (selectedCount == SELECTED_SIZE_ONE))
            changeMenuItemEnable(menu.findItem(R.id.action_disband_group), (selectedCount > Number.NUMBER_0))
        }
    }

    /**
     * 改变MenuItem的isEnabled状态
     */
    private fun changeMenuItemEnable(menuItem: MenuItem, isEnabled: Boolean) {
        if (menuItem.isEnabled != isEnabled) {
            menuItem.isEnabled = isEnabled
        }
    }

    /**
     * 更新全选按钮状态
     */
    private fun updateSelectAllButton() {
        val lastSelectedStatus = selectAllCheckButton?.state
        selectAllCheckButton?.state = if (isSelectAll()) SELECT_ALL else SELECT_NONE
        if ((lastSelectedStatus != selectAllCheckButton?.state)) {
            // 按钮由全选<->取消反选变化时，由于文本长度不一致，导致显示不全，所以需要重新触发更新
            activity?.invalidateOptionsMenu()
        }
    }

    /**
     * 创建标题栏菜单
     */
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (!isResumed) return
        toolbar?.clearMenu()
        updateToolbar(isInSelectionMode())
        cancelButton = null
        selectAllCheckButton = null
        selectMenu = null
        if (isInSelectionMode()) {
            inflater.inflate(BasebizR.menu.base_opt_album_selection, menu)
            menu.findItem(BasebizR.id.action_select_all)?.let { menuItem ->
                selectAllCheckButton = menuItem.actionView as? MenuCheckButton
                selectAllCheckButton?.let { checkButton ->
                    checkButton.state = if (isSelectAll()) SELECT_ALL else SELECT_NONE
                    checkButton.setOnClickListener {
                        if (DoubleClickUtils.isFastDoubleClick()) return@setOnClickListener
                        onOptionsItemSelected(menuItem)
                    }
                }
            }
            menu.findItem(BasebizR.id.action_cancel)?.let { menuItem ->
                cancelButton = menuItem.actionView as? MenuButton
                cancelButton?.setOnClickListener {
                    if (DoubleClickUtils.isFastDoubleClick()) return@setOnClickListener
                    onOptionsItemSelected(menuItem)
                }
            }
        } else {
            inflater.inflate(R.menu.albumsetpage_opt_person_pet_group_set, menu)
            selectMenu = menu.findItem(R.id.action_select)
            selectMenu?.let { it.isEnabled = (baseListViewModel?.totalSize ?: Number.NUMBER_0) > Number.NUMBER_0 }
        }
    }

    /**
     * 刷新Toolbar中标题、返回按钮的状态
     * @param isInSelectionMode 是否在选择模式
     */
    private fun updateToolbar(isInSelectionMode: Boolean) {
        if (isInSelectionMode) {
            toolbar?.isTitleCenterStyle = true
            setDisplayHomeAsUpEnabled(false)
        } else {
            setDisplayHomeAsUpEnabled(true)
            toolbar?.isTitleCenterStyle = false
            updateToolbarBackMenuItem()
        }
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)
        if (!isResumed) return
        menu.findItem(R.id.action_select)?.isVisible = !isInSelectionMode()
    }

    /**
     * 点击某个菜单项
     */
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!isResumed) return false
        when (item.itemId) {
            R.id.action_select -> {
                AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                    enterSelect = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE,
                )
                enterSelectionMode()
            }

            BasebizR.id.action_cancel -> exitSelectionMode()

            R.id.action_add -> startPersonPetSelectionFragment(requireActivity())

            BasebizR.id.action_select_all -> if (isSelectAll().not()) selectAll() else unselectAll()

            R.id.action_search -> {
                Starter.ActivityStarter(
                    this,
                    postCard = PostCard(RouterConstants.RouterName.SEARCH_ACTIVITY)
                ).start()
            }

            else -> super.onOptionsItemSelected(item)
        }
        return true
    }

    private fun startPersonPetSelectionFragment(activity: FragmentActivity) {
        Starter.DialogFragmentStarter<PanelDialog>(
            fm = activity.supportFragmentManager,
            bundle = SelectAlbumInputData(
                title = getString(BasebizR.string.choose_people_and_pets),
                fromPath = TextUtil.EMPTY_STRING,
                moveCount = 0,
                modelType = TYPE_PERSON_PET_ALBUM_SET,
                selectMulti = true
            ).createBundle().also {
                it.putString(KEY_REQUEST_KEY, REQUEST_KEY_CREATE_PERSON_PET_GROUP)
            },
            postCard = PostCard(RouterConstants.RouterName.SELECTION_ALBUM_PANEL_DIALOG),
        ).start()?.apply {
            setFragmentResultListenerSafety(REQUEST_KEY_CREATE_PERSON_PET_GROUP) { _, bundle ->
                if (bundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) return@setFragmentResultListenerSafety
                val stringPathList = bundle.getStringArray(KEY_RESULT_DATA_LIST) ?: return@setFragmentResultListenerSafety
                lifecycleScope.launch(Dispatchers.IO) {
                    val classifiedMap = mutableMapOf<Int, ArrayList<Path>>()
                    /**
                     * 对选中的图集列表进行分类存储到map中，以图集类型作为key，值为path list,作为创建合照的参数
                     * 比如：选中的图集中可能同时包含人物图集和宠物图集
                     */
                    stringPathList.toList().forEach { pathStr ->
                        val path = Path.fromString(pathStr)
                        when (path.`object` as? MediaSet) {
                            is PersonAlbum -> classifiedMap.getOrPut(GroupHelper.TYPE_PERSON_ALBUM_SET) { ArrayList() }.add(path)
                            is PetAlbum -> classifiedMap.getOrPut(GroupHelper.TYPE_PET_ALBUM_SET) { ArrayList() }.add(path)
                            else -> GroupHelper.NORMAL_INVALID_CODE
                        }
                    }
                    val resultPair = ApiDmManager.getScanDM().createPersonPetGroup(activity, classifiedMap)
                    // 此处给ui层根据状态码进行不同的呈现
                    withContext(Dispatchers.Main) {
                        when (resultPair.second) {
                            // 创建合照失败
                            CREATE_PERSON_GROUP_RESULT_CODE_FAIL -> {
                                GLog.e(TAG, LogFlag.DL) { "create group fail." }
                                ToastUtil.showShortToast(BasebizR.string.photo_group_created_fail)
                            }
                            // 创建已存在的合照成功弹Toast
                            CREATE_PERSON_GROUP_RESULT_CODE_CREATED_GROUP_EXIST -> {
                                GLog.d(TAG, LogFlag.DL) { "create exist group success." }
                                ToastUtil.showShortToast(BasebizR.string.photo_group_created)
                            }
                            // 创建空的合照提示无合照
                            CREATE_PERSON_GROUP_RESULT_CODE_CREATE_EMPTY_GROUP -> {
                                GLog.e(TAG, LogFlag.DL) { "haven’t group photo." }
                                ToastUtil.showShortToast(R.string.person_pet_group_empty)
                            }
                            // 需要创建空的合照或创建新合照不弹Toast
                            CREATE_PERSON_GROUP_RESULT_CODE_SUCCESS -> GLog.e(TAG, LogFlag.DL) { "create new group." }
                        }
                    }
                    AlbumsActionTrackHelper.trackAndSendMenuOperationClickForPet(createPersonPetGroupType = "manual")
                }
            }
        }
    }

    /**
     * 进入选中状态
     */
    override fun onEnterSelection() {
        super.onEnterSelection()
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener {
                updateSelectionTitleLater = false
                updateToolbarEditTitle(
                    defaultTitleResId = BasebizR.string.base_title_select_image,
                    count = baseListViewModel?.getSelectedItemCount() ?: 0
                )
                activity?.invalidateOptionsMenu()
            }
            start()
        }
    }

    /**
     * 退出选中状态
     */
    override fun onExitSelection() {
        super.onExitSelection()
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener {
                updateSelectionTitleLater = true
                toolbar?.apply {
                    title = getString(BasebizR.string.group_photo)
                }
                activity?.invalidateOptionsMenu()
            }
            start()
        }
    }

    /**
     * 底部菜单栏点击事件
     */
    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        super.onBottomMenuBarItemClicked(menuItem)
        when (menuItem.itemId) {
            R.id.action_rename -> {
                val albumName = (baseListViewModel as? PersonPetGroupAlbumSetViewModel)?.getSelectedAlbumName()
                groupNameEditDialog = activity?.let { activity ->
                    SingleEditDialog.Builder().setContext(activity)
                        .setTitle(resources.getString(BasebizR.string.name_the_group_photo))
                        .setHintText(resources.getString(BasebizR.string.main_face_group_edit_hint))
                        .setInputEmptyToastResId(BasebizR.string.main_face_group_edit_hint)
                        .setInputErrorToastResId(BasebizR.string.person_pet_group_name_invalid_and_re_input)
                        .setInputEmojiErrorToastResId(BasebizR.string.person_pet_group_name_invalid_and_re_input)
                        .setSpecialCharToastResId(BasebizR.string.person_pet_group_name_invalid_and_re_input)
                        .setListener(mListener)
                        .setContent(albumName)
                        .create()
                }
                groupNameEditDialog?.show()
            }
            R.id.action_disband_group -> {
                AppScope.launch(Dispatchers.Main) {
                    withContext(Dispatchers.IO) {
                        val groupIdList = (baseListViewModel as? PersonPetGroupAlbumSetViewModel)?.getSelectedAlbumGroupIdList()
                        if (groupIdList.isNullOrEmpty()) return@withContext
                        disbandPersonPetGroups(groupIdList)
                        exitSelectionMode()
                        AlbumsActionTrackHelper.trackAndSendMenuOperationClickForPet(dissolveGroup = TextUtil.EMPTY_STRING)
                    }
                }
            }
        }
    }

    override fun isSupportEmptyPage() = false

    override fun getEmptyPageIconAnimRes(): Int = BasebizR.raw.base_empty_view_normal

    override fun getEmptyPageTitleRes(): Int = R.string.person_pet_group_empty

    /**
     * 空状态副标题
     */
    override fun getEmptyPageSubtitleRes(): Int {
        return R.string.person_pet_group_empty_desc
    }

    /**
     * 解散合照
     */
    private fun disbandPersonPetGroups(groupIdList: List<Long>) {
        ApiDmManager.getScanDM().disbandPersonPetGroups(ContextGetter.context, groupIdList, TYPE_PERSON_PET_GROUP_ALBUM_SET)
    }

    companion object {
        private const val TAG_PREFIX = "LIST"
        private const val TAG = "PersonPetGroupAlbumSetFragment"
        /**
         * 跳转到选图集fragment时候设置的requestKey,表明是从创建合照的业务跳转到选图集fragment
         */
        private const val REQUEST_KEY_CREATE_PERSON_PET_GROUP = "createPersonPetGroup.requestKey"
    }
}