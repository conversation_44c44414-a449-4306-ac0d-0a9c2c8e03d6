/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EditMyAlbumSetItemDecoration.kt
 ** Description : 常用图集编辑态，我的图集和媒体类型，更多项目的间距
 ** Version     : 1.0
 ** Date        : 2025/05/24 09:42
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>           <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/05/24      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.albumsetpage.shortcutedit.ui

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.oplus.gallery.standard_lib.baselist.view.GridItemGapDecoration
import kotlin.math.ceil

/**
 * 常用图集编辑态，我的图集和媒体类型，更多项目的间距。
 * header和footer不添加额外的间距，由item布局使用padding自定义。
 * */
class EditMyAlbumSetItemDecoration(detail: EditMyGridLayoutDetail) : GridItemGapDecoration(detail) {

    override fun getHeaderItemOffsets(outRect: Rect, view: View, parent: RecyclerView, itemPosition: Int) {
        super.getHeaderItemOffsets(outRect, view, parent, itemPosition)
        outRect.top = 0
        outRect.bottom = 0

        val itemCount = (parent.adapter)?.itemCount ?: return
        val headerCount = layoutDetail.headerCount
        if (itemCount == headerCount && itemPosition == (headerCount - 1)) {
            // 只有header，则最后一个header增加下边距
            addSpaceToLastRow(outRect)
        }
    }

    override fun getFooterItemOffsets(outRect: Rect, view: View, parent: RecyclerView, itemPosition: Int) {
        super.getFooterItemOffsets(outRect, view, parent, itemPosition)
        outRect.top = 0
        outRect.bottom = 0

        val itemCount = (parent.adapter)?.itemCount ?: return
        // 走到这里来，说明有Footer。
        if (itemPosition == (itemCount - 1)) {
            // 最后一个 footer
            addSpaceToLastRow(outRect)
        }
    }

    override fun getNormalItemOffsets(outRect: Rect, view: View, parent: RecyclerView, itemPosition: Int) {
        super.getNormalItemOffsets(outRect, view, parent, itemPosition)
        val spanCount = layoutDetail.spanCount
        if (itemPosition < (spanCount + layoutDetail.headerCount)) {
            // 首行不设置上间距，交给header处理。
            outRect.top = 0
        }

        if (isLastRow(spanCount, parent, itemPosition)) {
            // 最后一行增加底部间距
            addSpaceToLastRow(outRect)
        }
    }

    private fun isLastRow(spanCount: Int, parent: RecyclerView, itemPosition: Int): Boolean {
        val itemCount = (parent.adapter)?.itemCount ?: return false
        val realItemCount = itemCount - layoutDetail.headerCount - layoutDetail.footerCount
        // 计算总行数（向上取整）
        val totalRows = ceil(realItemCount.toDouble() / spanCount).toInt()
        // 计算当前行
        val currentRow = ceil(itemPosition.toDouble() / spanCount).toInt()

        return currentRow == totalRows
    }

    private fun addSpaceToLastRow(outRect: Rect) {
        (layoutDetail as? EditMyGridLayoutDetail)?.bottomSpace?.let {
            outRect.bottom = it
        }
    }
}