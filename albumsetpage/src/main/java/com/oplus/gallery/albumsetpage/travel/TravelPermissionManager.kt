/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: TravelPermissionManager
 ** Description: 旅程图集权限管理
 **
 ** Version: 1.0
 ** Date: 2025/4/28
 ** Author: W9058271
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  W9058271                     2025/4/16        1.0        created
 *********************************************************************************/

package com.oplus.gallery.albumsetpage.travel

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.TextView
import com.coui.appcompat.couiswitch.COUISwitch
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.providers.AppSettings
import com.oplus.gallery.basebiz.R as BaseBizR

/**
 * 旅程图集权限管理
 */
object TravelPermissionManager {

    private const val SCENE_SERVICE_PERMISSION_ACTION = "oplus.intent.action.sceneservice.REQUEST_ALL_PERMISSION"
    private const val DEEP_THINKER_PERMISSION_ACTION = "oplus.intent.action.deepthinker.THIRD_APPLICATION_PERMISSION_SWITCH_ACTIVITY"
    private const val ACTION = "com.android.settings.MANUFACTURER_APPLICATION_SETTING"
    private const val CATEGORY = "android.intent.category.DEFAULT"
    private const val PKG_SCENESERVICE = "com.coloros.sceneservice"
    private const val PKG_DEEPTHINKER = "com.oplus.deepthinker"
    private const val SETTINGS_TITLE = "com.android.settings.title"
    private const val SETTINGS_SUMMARY = "com.android.settings.summary"
    private const val SETTINGS_SUMMARY_JUMP_HINT = "com.android.settings.summary_jump_hint"
    private const val KEY_SCENESERVICE = "key_com_coloros_sceneservice"
    private const val KEY_DEEPTHINKER = "key_com_oplus_deepthinker"
    private const val VALUE_OPEN = 1
    private const val VALUE_CLOSE = 0

    const val PERMISSION_REQUEST_CODE_DEEP_THINKER = 0X2
    const val PERMISSION_REQUEST_CODE_SCENE_SERVICE = 0X1

    /**
     * 检查旅程图集所需权限
     */
    fun checkPermission(context: Context) {
        // 如果两个增强服务都开启了
        val sceneServiceStatus = getSceneServiceStatus(context)
        val deepThinkerStatus = getDeepThinkerStatus(context)
        if (sceneServiceStatus && deepThinkerStatus) {
            checkDeepThinkerPermission(context)
        } else {
            showDialog(context, sceneServiceStatus, deepThinkerStatus)
        }
    }

    /**
     * 显示弹窗
     */
    @Suppress("LongMethod")
    private fun showDialog(context: Context, sceneServiceStatus: Boolean, deepThinkerStatus: Boolean) {
        val contentView = LayoutInflater.from(context).inflate(R.layout.albumsetpage_travel_aiblity_service_panel, null)
        val dialog = COUIAlertDialogBuilder(context).apply {
            setView(contentView)
            setTitle(R.string.albumsetpage_travel_permission_dialog_title)
            setPositiveButton(BaseBizR.string.base_permission_statement_agree_and_use) { _, _ ->
                checkDeepThinkerPermission(context)
                if (sceneServiceStatus.not()) {
                    modifySceneServiceStatus(context, VALUE_OPEN)
                }
                if (deepThinkerStatus.not()) {
                    modifyDeepThinkerStatus(context, VALUE_OPEN)
                }
            }
            setNegativeButton(BaseBizR.string.base_permission_statement_disagree, null)
        }.show()
        val positiveBtn = dialog.getButton(DialogInterface.BUTTON_POSITIVE)

        if (sceneServiceStatus.not()) {
            contentView.findViewById<View>(R.id.ll_scene_service).visibility = View.VISIBLE
            val tvSceneServiceName = contentView.findViewById<TextView>(R.id.tv_scene_service_name)
            val tvSceneServiceDesc = contentView.findViewById<TextView>(R.id.tv_scene_service_desc)
            val switchSceneService = contentView.findViewById<COUISwitch>(R.id.switch_sceneservice)
            val sceneServiceNameAndDesc = getNameAndDescByPkg(context, PKG_SCENESERVICE)
            tvSceneServiceName.text = sceneServiceNameAndDesc.first
            tvSceneServiceDesc.text = sceneServiceNameAndDesc.second
            tvSceneServiceDesc.movementMethod = LinkMovementMethod.getInstance()
            switchSceneService.isChecked = true
            switchSceneService.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    modifySceneServiceStatus(context, VALUE_OPEN)
                } else {
                    modifySceneServiceStatus(context, VALUE_CLOSE)
                }
                changeAgreeBtnStatus(context, positiveBtn)
            }
        }
        if (deepThinkerStatus.not()) {
            contentView.findViewById<View>(R.id.ll_deep_thinker).visibility = View.VISIBLE
            val tvDeepThinkerName = contentView.findViewById<TextView>(R.id.tv_deep_thinker_name)
            val tvDeepThinkerDesc = contentView.findViewById<TextView>(R.id.tv_deep_thinker_desc)
            val switchDeepthinker = contentView.findViewById<COUISwitch>(R.id.switch_deepthinker)
            switchDeepthinker.isChecked = true
            switchDeepthinker.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    modifyDeepThinkerStatus(context, VALUE_OPEN)
                } else {
                    modifyDeepThinkerStatus(context, VALUE_CLOSE)
                }
                changeAgreeBtnStatus(context, positiveBtn)
            }
            val deepThinkerNameAndDesc = getNameAndDescByPkg(context, PKG_DEEPTHINKER)
            tvDeepThinkerName.text = deepThinkerNameAndDesc.first
            tvDeepThinkerDesc.text = deepThinkerNameAndDesc.second
            tvDeepThinkerDesc.movementMethod = LinkMovementMethod.getInstance()
        }

        if (sceneServiceStatus.not() && deepThinkerStatus.not()) {
            contentView.findViewById<View>(R.id.divider).visibility = View.VISIBLE
        }
    }

    /**
     * 修改同意按钮的状态。只要有一个没同意，则置灰不可点击
     */
    private fun changeAgreeBtnStatus(context: Context, positiveBtn: Button?) {
        positiveBtn?.isEnabled = getSceneServiceStatus(context) && getDeepThinkerStatus(context)
    }

    /**
     * 检查智慧能力增强服务的定位权限
     */
    private fun checkDeepThinkerPermission(context: Context) {
        if (context is Activity) {
            context.startActivityForResult(Intent(DEEP_THINKER_PERMISSION_ACTION).apply {
                `package` = PKG_DEEPTHINKER
            }, PERMISSION_REQUEST_CODE_DEEP_THINKER)
        }
    }

    /**
     * 检查智慧数据增强服务的定位权限
     */
    fun checkSceneServicePermission(context: Context) {
        if (context is Activity) {
            context.startActivityForResult(Intent(SCENE_SERVICE_PERMISSION_ACTION).apply {
                `package` = PKG_SCENESERVICE
            }, PERMISSION_REQUEST_CODE_SCENE_SERVICE)
        }
    }

    /**
     * 通过包名获取对应应用的名字和服务描述
     */
    private fun getNameAndDescByPkg(context: Context, pkg: String): Pair<String?, SpannableStringBuilder?> {
        val intent = Intent(ACTION).apply {
            addCategory(CATEGORY)
            setPackage(pkg)
        }
        val resolveInfo = context.packageManager.queryIntentActivities(intent, PackageManager.GET_META_DATA)
            .getOrNull(AppConstants.Number.NUMBER_0)
        val metaData = resolveInfo?.activityInfo?.metaData
        val summaryResId = metaData?.getInt(SETTINGS_SUMMARY)
        val jumpHintResId = metaData?.getInt(SETTINGS_SUMMARY_JUMP_HINT)
        val titleResId = metaData?.getInt(SETTINGS_TITLE)

        val summary = summaryResId?.let { getResString(context, pkg, it) }
        val jumpHint = jumpHintResId?.let { getResString(context, pkg, it) }
        val title = titleResId?.let { getResString(context, pkg, it) }

        val ssb = SpannableStringBuilder(summary + jumpHint)
        ssb.setSpan(
            ForegroundColorSpan(Color.RED),
            summary.orEmpty().length,
            (summary + jumpHint).length,
            Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )
        val clickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                context.startActivity(Intent(ACTION).apply {
                    addCategory(CATEGORY)
                    `package` = pkg
                    resolveInfo?.activityInfo?.let {
                        setComponent(
                            ComponentName(
                                it.packageName,
                                it.name
                            )
                        )
                    }
                })
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
            }
        }
        ssb.setSpan(clickableSpan, summary.orEmpty().length, (summary + jumpHint).length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        return Pair(title, ssb)
    }

    /**
     * 通过资源 ID 获取字符串
     * @param context [Context]
     * @param packageName 对应应用的包名
     * @param resId 资源 ID
     *
     * @return 对应资源 ID 的字符串
     */
    private fun getResString(context: Context, packageName: String, resId: Int): String {
        if (resId != AppConstants.Number.NUMBER_0) {
            val packageManager = context.packageManager
            val resourcesForApplication = packageManager.getResourcesForApplication(packageName)
            return resourcesForApplication.getString(resId)
        }
        return TextUtil.EMPTY_STRING
    }

    /**
     * 获取场景智能开关状态
     * @param context [Context]
     *
     * @return true 开启；false 关闭
     */
    private fun getSceneServiceStatus(context: Context): Boolean {
        return AppSettings.Secure.getInt(context.contentResolver, KEY_SCENESERVICE) == VALUE_OPEN
    }

    /**
     * 修改场景智能增强服务的开关状态
     */
    private fun modifySceneServiceStatus(context: Context, value: Int) {
        AppSettings.Secure.putInt(context.contentResolver, KEY_SCENESERVICE, value)
    }

    /**
     * 获取场景智能开关状态
     * @param context [Context]
     *
     * @return true 开启；false 关闭
     */
    private fun getDeepThinkerStatus(context: Context): Boolean {
        return AppSettings.System.getInt(context.contentResolver, KEY_DEEPTHINKER) == VALUE_OPEN
    }

    /**
     * 修改 DT 增强服务的开关状态
     */
    private fun modifyDeepThinkerStatus(context: Context, value: Int) {
        AppSettings.System.putInt(context.contentResolver, KEY_DEEPTHINKER, value)
    }
}