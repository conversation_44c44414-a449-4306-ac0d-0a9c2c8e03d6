/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MyAlbumsViewDataBinding.kt
 ** Description:我的图集组
 ** Version: 1.0
 ** Date: 2025/4/3
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2025/4/3     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.albumsetpage.allalbum.viewdata

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.view.updatePaddingRelative
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GalleryGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.allalbum.item.MyAlbumsViewData
import com.oplus.gallery.albumsetpage.allalbum.ui.ALBUM_TYPE_TAB_MY_ALBUMS
import com.oplus.gallery.albumsetpage.allalbum.ui.OnAlbumClickListener
import com.oplus.gallery.albumsetpage.allalbum.viewmodel.MyAlbumSetViewModel
import com.oplus.gallery.albumsetpage.myalbum.MyGridItemDecoration
import com.oplus.gallery.albumsetpage.selection.albums.ui.AlbumSetMenuManager.Companion.ALBUM_MOVEABLE_SHORTCUT
import com.oplus.gallery.albumsetpage.selection.albums.ui.AlbumSetMenuManager.Companion.ORIGIN_ARGUMENTS_BUNDLE_KEY
import com.oplus.gallery.albumsetpage.selection.albums.ui.AlbumSetMenuManager.Companion.SHORT_CUT_COUNT
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment.Companion.DEFAULT_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.base.FragmentStartType
import com.oplus.gallery.basebiz.uikit.fragment.base.IFragmentStack
import com.oplus.gallery.basebiz.util.AlbumCardListUtils
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.ui.view.ResponsiveCardAlbumSetViewDataBinding
import com.oplus.gallery.business_lib.viewmodel.base.DifferNotifyCallback
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.base.SectionDiffer
import com.oplus.gallery.business_lib.viewmodel.base.isNullOrEmpty
import com.oplus.gallery.business_lib.viewmodel.style.StylePool
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.findFirstVisiblePosition
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_MY_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.model.BaseModel.Companion.COUNT_UNINITIALIZED
import com.oplus.gallery.framework.abilities.data.model.BaseModel.Companion.INVALID_INDEX
import com.oplus.gallery.framework.abilities.data.sortablemodel.sort.AlbumMovable
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.baselist.adapter.config.AbsAdapterConfigProvider
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.BaseListAdapter
import com.oplus.gallery.standard_lib.baselist.view.BaseListItemAnimator
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.baselist.view.BaseViewDataBinding
import com.oplus.gallery.standard_lib.baselist.view.BaseViewHolder
import com.oplus.gallery.standard_lib.baselist.view.OnAnimationListener
import com.oplus.gallery.standard_lib.baselist.view.OnItemListener
import com.oplus.gallery.standard_lib.bean.ViewData
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.basebiz.R as basebizR
import com.oplus.gallery.foundation.ui.R as foundationR

/**
 * 我的图集列表组：图集列表
 */
class MyAlbumsViewDataBinding(
    private val lifecycleFields: MainAlbumTabMyAlbumLifecycleFields,
    private val onParentWidthGet: (() -> Int),
    private val albumItemListener: OnAlbumClickListener<AlbumViewData>
) : BaseViewDataBinding<MyAlbumsViewData, BaseViewHolder<MyAlbumsViewData>>(lifecycleFields.context), OnItemListener<AlbumViewData> {

    private var titleParentView: ViewGroup? = null
    private var title: TextView? = null
    private var enterImage: ImageView? = null
    private var recyclerView: COUIRecyclerView? = null
    private var layoutDetail: LayoutDetail? = null
    private var recyclerAdapter: BaseListAdapter<AlbumViewData>?  = null
    private var visibleStart = INVALID_INDEX
    private var visibleEnd = INVALID_INDEX
    private var myAlbumSetViewModel: MyAlbumSetViewModel? = null

    /** differ监听、处理differ动画 */
    private val diffUpdateListener: DiffCallback<AlbumViewData> by lazy { DiffCallback(recyclerAdapter) }

    /**
     * 是否第一次加载数据
     */
    private var isFirstLoadData = true
    /**
     * 是否已经注册liveData数据观察者
     */
    private var isRegisteredDataObserver = false

    private val getContentWidth
        get() = onParentWidthGet.invoke()

    /**
     * 横向列表的行数（spanCount)
     */
    private var currentRows: Int = DEFAULT_PAGE_ITEM_ROWS

    /**
     * 图集项是否可以在一页（屏）显示
     */
    private var isShowingInOnePage: Boolean = false

    /**
     * diff执行之前,记录位置
     * 安卓原生水平RV，只要在当前可见item之前有增删diff，执行diff动画，RV就会滚屏
     * 临时方案：记录原先firstVisiblePost,动画执行前.布局之后,也就是onScroll idle时,给纠正回来.
     * 终极方案：还是得看看动画器为啥会导致滚屏
     */
    private var firstVisiblePosBeforeDiff = INVALID_INDEX
    /**
     * 监听滑动状态改变横向列表的左右边距
     * 当显示内容不足一屏时，列表居中显示；
     * 当显示内容超过一屏时，列表在最后一页左边缘漏出，前面页右边缘漏出
     */
    private val onScrollerListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)
            // 一页内显示时已经在数据变化时处理了边距 这里不处理滚动时边距变化
            if (isShowingInOnePage) return
            if (recyclerView.scrollState == COUIRecyclerView.SCROLL_STATE_IDLE) {
                restoreRVPositionBeforeDiffIfNeed(recyclerView)
            }
        }
    }

    /**
     * 允许RecyclerView滑动，默认允许，当长按常用图集弹出popWindow时禁止
     */
    private var enabledRecyclerViewScroll = true

    /**
     * 监听到长按图集项不松手时，禁止RecyclerView滑动
     */
    private val onItemTouchListener = object : RecyclerView.SimpleOnItemTouchListener() {
        override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
            return enabledRecyclerViewScroll.not()
        }
    }

    /**
     * diff执行之前,记录位置
     * 安卓原生R水平V，只要在当前可见item之前有增删diff，执行diff动画，RV就会滚屏
     * 临时方案：记录原先firstVisiblePost,动画执行前.布局之后,也就是onScroll idle时,给纠正回来.
     * 终极方案：还是得看看动画器为啥会导致滚屏
     */
    private fun restoreRVPositionBeforeDiffIfNeed(recyclerView: RecyclerView?) {
        val scrollPos = firstVisiblePosBeforeDiff
        firstVisiblePosBeforeDiff = INVALID_INDEX
        if (scrollPos != INVALID_INDEX) {
            // 需要post一帧,如果不post,那么不会生效,因为当前这一帧布局结束后RV内部会将此位置重置为-1，所以需要在post在下帧才能生效
            recyclerView?.post {
                GLog.d(TAG) { "restorePositionBeforeDiffIfNeed: scroll=${recyclerView.findFirstVisiblePosition()}->$scrollPos" }
                /* 为了确保最后一列仅有一个item时(当前页显示完整的2列,第3列露出一点),不会滚列，
                         即使当前findFirstVisiblePosition==scrollPos，也要执行scrollToPosition*/
                recyclerView.scrollToPosition(scrollPos)
            }
        }
    }

    class DiffCallback<TViewData : ViewData>(val recyclerAdapter: BaseListAdapter<TViewData>?) :
        DifferNotifyCallback<BaseListViewHolder<TViewData>>(recyclerAdapter as RecyclerView.Adapter<BaseListViewHolder<TViewData>>) {
        override fun getItemPosition(position: Int): Int {
            return position + (recyclerAdapter?.headerCount ?: 0)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<MyAlbumsViewData> {
        return BaseViewHolder(LayoutInflater.from(context).inflate(R.layout.album_group_myalbums_item, parent, false), this).apply {
            initView(view)
            initViewModel()
            myAlbumSetViewModel?.setVisibleRange(visibleStart, visibleEnd)
        }
    }

    override fun copyLayout(context: Context): BaseViewDataBinding<MyAlbumsViewData, BaseViewHolder<MyAlbumsViewData>> = this

    override fun onBindViewHolder(itemViewHolder: BaseViewHolder<MyAlbumsViewData>, position: Int, viewData: MyAlbumsViewData?) {
        // doNothing
    }

    private fun initView(view: View) {
        titleParentView = view.findViewById(R.id.title_parent_view)
        title = view.findViewById(R.id.tv_title)
        enterImage = view.findViewById(R.id.my_album_set_enter)
        recyclerView = view.findViewById(R.id.my_album_set_recyclerView)
        enterImage?.setOnClickListener {
            if (recyclerView?.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                GLog.d(TAG, DL) {
                    "enterImageClick, recyclerView scrollState is not idle，return."
                }
                return@setOnClickListener
            }
            startMyAlbumSetFragment()
        }
        initRecyclerView()
    }

    private fun initRecyclerView() {
        val gridLayoutDetail = makeLayoutDetail(context, currentRows)
        layoutDetail = gridLayoutDetail
        recyclerAdapter = BaseListAdapter<AlbumViewData>(
            gridLayoutDetail,
            makeAdapterConfigProvider()
        ).apply {
            itemViewClickListener = this@MyAlbumsViewDataBinding
            itemLongClickListener = this@MyAlbumsViewDataBinding
        }
        recyclerView?.apply {
            isMotionEventSplittingEnabled = false
            layoutManager = GalleryGridLayoutManager(this, gridLayoutDetail)
            (layoutManager as GalleryGridLayoutManager).orientation = LinearLayoutManager.HORIZONTAL
            addOnScrollListener(onScrollerListener)
            adapter = recyclerAdapter
            addItemDecoration()
            itemAnimator = BaseListItemAnimator().also { animator ->
                animator.addAnimationListener(object : OnAnimationListener {
                    override fun onAnimationFinished() {
                        GLog.d(TAG, DL) { "initRecyclerView onAnimationsFinished" }
                        /* 动画过程中,正好数据回来后,diff=null,adapter.refreshViewHolder因为有diff会跳过数据刷新,所以需要动画结束后刷新数据
                           场景:bugID 9365179 图集tab-图集详情--选择模式-复制到-选图列表-新建图集--返回到图集tab-图集tab中新建的图集item为空白
                         */
                        recyclerAdapter?.setDataSet(refresh = true)
                    }
                })
            }
            addOnItemTouchListener(onItemTouchListener)
            setItemClickableWhileOverScrolling(false)
            setItemClickableWhileSlowScrolling(false)
        }
        recyclerAdapter?.apply {
            onVisibleRangeChangedListener = { visibleStart, visibleEnd ->
                <EMAIL> = visibleStart
                <EMAIL> = visibleEnd
                myAlbumSetViewModel?.setVisibleRange(visibleStart, visibleEnd)
            }
            onRefreshStateChangedListener = { canRefresh ->
                myAlbumSetViewModel?.isActive = canRefresh
            }
        }
    }

    /**
     * 设置RV子项的间距
     */
    private fun addItemDecoration() {
        layoutDetail ?: return
        recyclerView?.takeIf { it.isComputingLayout.not() }?.addItemDecoration(
            MyGridItemDecoration(layoutDetail as MyAlbumGridLayoutDetail, ResourceUtils.isRTL(context))
        ) ?: GLog.w(TAG, DL) {
            "addItemDecoration, isComputingLayout = ${recyclerView?.isComputingLayout}"
        }
    }

    private fun makeAdapterConfigProvider(): AbsAdapterConfigProvider = object : AbsAdapterConfigProvider() {
        override val configs: MutableList<ItemConfig<*>> = mutableListOf<ItemConfig<*>>().apply {
            layoutDetail?.let { detail ->
                add(
                    ItemConfig(
                        AlbumViewData::class.java,
                        ResponsiveCardAlbumSetViewDataBinding(detail, context = context, stylePool = StylePool())
                    )
                )
            }
        }
    }

    /**
     * 进入我的图集页
     */
    private fun startMyAlbumSetFragment() {
        lifecycleFields.activity?.supportFragmentManager?.start(
            startType = FragmentStartType.REPLACE,
            postCard = PostCard(RouterConstants.RouterName.MY_ALBUM_SET_TAB_FRAGMENT),
            resId = basebizR.id.base_fragment_container,
            anim = DEFAULT_ANIM_ARRAY,
            addToBackStack = true,
            fragmentStack = lifecycleFields.activity as IFragmentStack
        )
    }

    private fun initViewModel() {
        myAlbumSetViewModel = ViewModelProvider(lifecycleFields.onStoreOwnerGet())[MyAlbumSetViewModel::class.java].apply {
            setUpViewModelStyle(this)
        }
        myAlbumSetViewModel?.apply {
            setViewData(
                AlbumViewData(
                    id = "",
                    position = 0,
                    modelType = TYPE_MY_ALBUM_SET,
                    isMediaAlbum = false,
                    version = 0,
                    totalCount = 0,
                    title = ""
                )
            )
        }
        if (isRegisteredDataObserver) return else isRegisteredDataObserver = true
        setLiveDataObserver()
    }

    /**
     * 注册MyAlbumSetViewModel中的Active范围数据的观察者
     */
    private fun setLiveDataObserver() {
        myAlbumSetViewModel?.activeInfoLiveData?.observe(lifecycleFields.onLifecycleOwnerGet()) { activeDataInfo ->
            if (myAlbumSetViewModel?.totalSize == COUNT_UNINITIALIZED) {
                GLog.w(TAG, DL) { "setLiveDataObserver data is not reloaded" }
                return@observe
            }
            val lastTotalCount = recyclerAdapter?.totalCount
            val totalCount = myAlbumSetViewModel?.totalSize ?: 0
            GLog.d(TAG, DL) {
                "setLiveDataObserver.totalCount:$lastTotalCount->$totalCount first=$isFirstLoadData,${activeDataInfo.differ}"
            }
            // refresh可能触发多次，避免totalCount和lastTotalCount都为0的场景adapter不必要的刷新，可能会recycleView打断上一次的动画
            if (!isFirstLoadData && (totalCount == 0) && (lastTotalCount == 0)) {
                return@observe
            }
            var needNotifyDataSetChanged = false
            val differ = activeDataInfo.differ
            if ((totalCount == 0) || (lastTotalCount != totalCount) || isFirstLoadData) {
                if (isFirstLoadData) {
                    titleParentView?.isVisible = true
                }
                isFirstLoadData = false
                recyclerAdapter?.totalCount = totalCount
                /*
                 * 有diff时，后面通过动画更新ViewHolder
                 * 无diff时，直接notifyDataSetChanged更新ViewHolder，使holder与totalCount同步，避免crash
                 */
                if (differ.isNullOrEmpty()) {
                    needNotifyDataSetChanged = true
                }
                updateLayoutIfNeed(totalCount)
            }

            // 有diff时此处不刷新holder，而在动画结束时刷新
            recyclerAdapter?.setDataSet(
                activeDataInfo.activeViewDataArray.asList(),
                refresh = differ.isNullOrEmpty()
            )

            if (needNotifyDataSetChanged) {
                recyclerAdapter?.notifyDataSetChanged()
            } else {
                saveRvFirstVisiblePosIfNeed(differ)
                differ?.dispatchUpdateEventsTo(diffUpdateListener)
            }
        }
    }

    private fun saveRvFirstVisiblePosIfNeed(differ: SectionDiffer<MediaSet?>?) {
        differ ?: return
        val firstVisiblePos = recyclerView.findFirstVisiblePosition()
        if (differ.hasDiffBeforePosition(firstVisiblePos)) {
            firstVisiblePosBeforeDiff = firstVisiblePos
        }
    }

    /**
     * 随图集项的变化需要更新布局（会触发行数变化，【一屏显示/一屏以上显示】切换的场景）
     * @param totalCount 图集项总数
     */
    private fun updateLayoutIfNeed(totalCount: Int) {
        /*
         当当前行数与期望行数不一致（比如1->2->3行的转换），
         且有数据时（触发liveData变动可能只是初始化了visibleRange,并没有执行完reload任务,此时totalCount为0->-1），去刷新布局
         */
        val nextRows = getNextRows(totalCount)
        if ((currentRows != nextRows) && (totalCount > 0)) {
            currentRows = nextRows
            notifyLayoutChanged()
        }
        val needShowInOnePage = (totalCount <= AlbumCardListUtils.getOnePageCount(context, getContentWidth))
        if (isShowingInOnePage == needShowInOnePage) {
            return
        }
        isShowingInOnePage = needShowInOnePage
        val recyclerViewWidth = if (isShowingInOnePage) {
            getContentWidth - getRecyclerPadding() * EDGE_NUMBER
        } else {
            getRecyclerViewWidth()
        }
        refreshLayoutManager(recyclerViewWidth)
        recyclerAdapter?.notifyItemRangeChanged(0, totalCount)
        GLog.d(TAG, DL) {
            "updateLayoutIfNeed.totalCount:$totalCount,currentRows:$currentRows,nextRows:$nextRows" +
                    "needShowInOnePage:$needShowInOnePage,isShowingInOnePage:$isShowingInOnePage"
        }
    }

    private fun setUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        val itemWidth = context.resources.getDimension(basebizR.dimen.label_album_set_item_cover_width).toInt()
        val roundThumbStyleData = StyleData().apply {
            put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
            put(
                StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS,
                context.resources.getDimension(basebizR.dimen.label_album_set_item_cover_corners_radius)
            )
            put(StyleData.KEY_THUMB_SIZE_TYPE, ThumbnailSizeUtils.getMicroThumbnailKey(itemWidth))
            put(
                StyleData.KEY_THUMB_STROKE_WIDTH,
                context.resources.getDimension(foundationR.dimen.common_round_drawable_frame_stroke_width)
            )
            put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, context.getColor(basebizR.color.common_round_drawable_frame_stroke_color))
            put(StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR, COUIContextUtil.getAttrColor(context, foundationR.attr.gColorPressBackground))
        }
        viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, roundThumbStyleData)
        val drawableThumbStyleData = roundThumbStyleData.copy().apply {
            put(StyleData.KEY_THUMB_STROKE_WIDTH, context.resources.getDimension(foundationR.dimen.common_round_drawable_frame_stroke_width))
        }
        viewModel.addStyle(StyleType.TYPE_DRAWABLE_THUMB_STYLE, drawableThumbStyleData)
    }

    private fun makeLayoutDetail(
        context: Context,
        pageRows: Int,
        recyclerViewWidth: Int = getRecyclerViewWidth()
    ): MyAlbumGridLayoutDetail = MyAlbumGridLayoutDetail.MyAlbumHorizontalGapsBuilder(
        pageRows = pageRows,
        pageColumns = AlbumCardListUtils.getSpanCount(context, getContentWidth)
    ).apply {
        parentWidth = recyclerViewWidth
        edgeWidth = context.resources.getDimensionPixelOffset(R.dimen.my_album_recyclerView_edge)
        gapWidth = context.resources.getDimensionPixelOffset(R.dimen.my_album_recyclerView_gap)
    }.build().apply {
        itemDecorationGapPx.top = context.resources.getDimension(basebizR.dimen.base_album_set_fragment_item_view_top_gap)
        itemDecorationGapPx.bottom = context.resources.getDimension(basebizR.dimen.base_album_set_fragment_item_view_bottom_gap)
        itemDecorationGapPx.left = context.resources.getDimension(R.dimen.my_album_recyclerView_left_gap)
        itemDecorationGapPx.right = context.resources.getDimension(R.dimen.my_album_recyclerView_right_gap)
    }

    /**
     * 虚假的recyclerView宽度，用于计算recyclerView的item宽度
     */
    private fun getRecyclerViewWidth(): Int {
        return (getContentWidth
                - context.resources.getDimensionPixelOffset(R.dimen.my_album_show_in_next_page)
                - getRecyclerPadding() * EDGE_NUMBER)
    }

    /**
     * 获取recyclerView的padding,小屏 16dp,中大屏24dp
     */
    private fun getRecyclerPadding(): Int = context.resources.getDimensionPixelOffset(R.dimen.my_album_recyclerView_padding)

    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
        if (recyclerView?.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
            GLog.d(getTag(), DL) {
                "onItemClick, recyclerView is scroll, position=$position, viewData=$viewData"
            }
            return
        }
        albumItemListener.onItemClick(position, viewData, clickType, itemView)
    }

    override fun onItemLongClick(position: Int, view: View, viewData: AlbumViewData?): Boolean {
        val itemView = view.findViewById<View>(basebizR.id.card_albumset_item_view)
        itemView.setTag(ORIGIN_ARGUMENTS_BUNDLE_KEY, Bundle().apply {
            putSerializable(ALBUM_MOVEABLE_SHORTCUT, myAlbumSetViewModel as? AlbumMovable)
            putInt(SHORT_CUT_COUNT, myAlbumSetViewModel?.shortCutAlbumSetInfo?.shortCutAlbumSetIds?.size ?: 0)
        })
        return albumItemListener.onItemLongClick(position, itemView, viewData, ALBUM_TYPE_TAB_MY_ALBUMS)
    }

    /**
     * 更改RV可滑动状态
     * @param isEnable true: 可滑动，false: 不可滑动
     */
    fun setRecyclerViewScrollEnabled(isEnable: Boolean) {
        enabledRecyclerViewScroll = isEnable
    }

    /**
     * 在屏幕尺寸、侧边栏展开收起时，RV宽度发生变化，触发刷新
     */
    fun notifyLayoutChanged(forceNotify: Boolean = false) {
        recyclerView?.updatePaddingRelative(start = getRecyclerPadding(), end = getRecyclerPadding())
        refreshLayoutManager()
        if (forceNotify) recyclerAdapter?.notifyDataSetChanged()
        val paddingHorizontal = context.resources.getDimensionPixelOffset(R.dimen.picked_album_set_horizontal_margin)
        title?.updatePaddingRelative(start = paddingHorizontal, end = paddingHorizontal)
    }

    /**
     * 刷新RV布局
     */
    private fun refreshLayoutManager(recyclerViewWidth: Int = getRecyclerViewWidth()) {
        makeLayoutDetail(context, currentRows, recyclerViewWidth).let { newLayoutDetail ->
            layoutDetail?.apply {
                spanCount = newLayoutDetail.spanCount
                itemWidth = newLayoutDetail.itemWidth
            }
            (recyclerView?.layoutManager as? GridLayoutManager)?.apply {
                (layoutDetail as? MyAlbumGridLayoutDetail)?.replaceGaps((newLayoutDetail as? MyAlbumGridLayoutDetail)?.itemHorizontalGaps)
                removeAllItemDecorations()
                addItemDecoration()
                spanCount = newLayoutDetail.spanCount
            }
        }
    }

    /**
     * 得到当前横向列表应该显示的行数,之后与交互确定小于一屏的个数时，此方法会再次修改
     * 当前没有数据时，行数不变; 有数据时，显示一到三行
     */
    private fun getNextRows(totalCount: Int): Int {
        return if (totalCount < VALID_PAGE_ITEM_ROWS) {
            currentRows
        } else if (totalCount < DEFAULT_PAGE_ITEM_ROWS) {
            totalCount
        } else {
            DEFAULT_PAGE_ITEM_ROWS
        }
    }

    /**
     * 移除之前RV的item间距
     */
    private fun removeAllItemDecorations() {
        recyclerView?.takeIf { (it.itemDecorationCount > 0) && (it.isComputingLayout.not()) }?.let {
            for (index in (it.itemDecorationCount - 1) downTo 0) {
                it.removeItemDecoration(it.getItemDecorationAt(index))
            }
        } ?: GLog.w(TAG, DL) {
            "removeAllItemDecorations, itemDecorationCount = ${recyclerView?.itemDecorationCount}"
        }
    }

    override fun getTag(): String {
        return TAG
    }

    private companion object {
        private const val TAG = "MyAlbumsViewDataBinding"

        //默认每页行数
        private const val DEFAULT_PAGE_ITEM_ROWS = 3

        //最小的有效行数
        private const val VALID_PAGE_ITEM_ROWS = 1

        //一屏显示时RV宽度减去边距，边距的系数（左、右 -> 2）
        private const val EDGE_NUMBER = 2
    }
}

/**
 * 参数太多，超过显6个限制,将callback单独拎出来
 */
data class MainAlbumTabMyAlbumLifecycleFields(
    val context: Context,
    val activity: FragmentActivity?,
    val onStoreOwnerGet: (() -> ViewModelStoreOwner),
    val onLifecycleOwnerGet: (() -> LifecycleOwner)
)