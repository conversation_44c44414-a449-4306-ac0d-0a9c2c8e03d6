/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SelectionTabFragment.kt
 * Description: 选图的Tab页
 *
 * Version: 1.0
 * Date: 2022/08/16
 * Author: <EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>                     <date>          <version>    <desc>
 * ------------------------------------------------------------------------------
 * h<PERSON><PERSON><PERSON>@Apps.Gallery       2022/08/16        1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.albumsetpage.selection.pictures.ui

import android.graphics.Color
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.tablayout.COUITabLayout
import com.coui.appcompat.tablayout.COUITabLayoutMediator
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.appcompat.viewpager.COUIViewPager2
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.selection.pictures.viewmodel.SelectionTabViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.business_lib.helper.ToolbarAnimationHelper
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.selection.NotifyState
import com.oplus.gallery.business_lib.model.selection.OnSelectionCallback
import com.oplus.gallery.business_lib.selectionpage.ISelectionPage
import com.oplus.gallery.business_lib.selectionpage.KEY_SELECTION_DATA
import com.oplus.gallery.business_lib.selectionpage.SelectInputData
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment

@RouterNormal(RouterConstants.RouterName.SELECTION_TAB_FRAGMENT)
class SelectionTabFragment : BaseFragment(), ISelectionPage, OnSelectionCallback<Path>, TemplateFragment.IToolbarSetter {
    private lateinit var fragmentAdapter: SelectionTabAdapter

    private var toolbar: COUIToolbar? = null
    private var tabLayout: COUITabLayout? = null
    private var viewPager: COUIViewPager2? = null
    private val viewModel by viewModels<SelectionTabViewModel>()
    private var divider: View? = null
    private var toolbarAnimationHelper: ToolbarAnimationHelper? = null
    private var currentScrollPosition: Int = 0

    override lateinit var selectInputData: SelectInputData

    override fun getLayoutId(): Int = R.layout.album_fragment_selection_tab

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        selectInputData = arguments?.getParcelable(KEY_SELECTION_DATA)!!
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        toolbar = view.findViewById(R.id.toolbar)
        divider = view.findViewById(R.id.divider)
        tabLayout = view.findViewById(R.id.tab_layout)
        viewPager = view.findViewById(R.id.view_pager)

        setupToolbar()

        toolbarAnimationHelper = ToolbarAnimationHelper(view)

        setupViewPager()

        viewModel.enterSelectionMode(selectInputData.selectionDataId)
        viewModel.registerOnSelectionDataCallback(this)
    }

    private fun setupToolbar() {
        setHasOptionsMenu(true)
        toolbar?.isTitleCenterStyle = true
        (toolbar as? SelectionToolBar)?.havePaddingTop = parentFragment !is PanelFragment
        (activity as? AppCompatActivity)?.also {
            it.setSupportActionBar(toolbar)
            it.supportActionBar?.setDisplayHomeAsUpEnabled(false)
        }
    }

    private fun setupViewPager() {
        fragmentAdapter = SelectionTabAdapter(childFragmentManager, lifecycle) {
            it.arguments = arguments
        }
        viewPager?.let {
            it.isSaveEnabled = false
            it.adapter = fragmentAdapter
            it.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    for (i in 0 until fragmentAdapter.itemCount) {
                        val isCurrent = i == position
                        fragmentAdapter.getFragment(i).apply {
                            bindToolbarSetter(if (isCurrent) this@SelectionTabFragment else null)
                            if (!isCurrent) {
                                setHasOptionsMenu(false)
                            }
                        }
                    }
                }
            })
            // 延迟加载图集Tab和发现Tab，减少应用启动时间
            it.postDelayed({ it.offscreenPageLimit = fragmentAdapter.itemCount }, DELAY_VIEW_PAGER_SET_OFF_SCREEN_PAGE_LIMIT)
            tabLayout?.let { layout ->
                COUITabLayoutMediator(layout, it) { tab, position ->
                    tab.setText(fragmentAdapter.classes[position].first)
                }.attach()
            }
        }
    }

    private fun setToolbarTitle(count: Int = 0) {
        val title = selectInputData.title ?: if ((count == 0) || !selectInputData.selectMulti) {
            resources.getString(com.oplus.gallery.basebiz.R.string.base_title_select_image)
        } else {
            resources.getQuantityString(com.oplus.gallery.basebiz.R.plurals.base_title_has_select, count, count)
        }
        toolbar?.let {
            it.title = title
        }
    }

    override fun onSelectionChange(item: Path?, state: NotifyState) {
        setToolbarTitle(viewModel.getSelectedItemCount(false))
    }

    /**
     * 创建menu，但执行[onOptionsItemSelected]的位置在持有本类对象的容器中，如[SelectionActivity]
     */
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        if (!isResumed) return
        menu.clear()
        inflater.inflate(com.oplus.gallery.basebiz.R.menu.common_menu_cancel_none, menu)
        setToolbarTitle(viewModel.getSelectedItemCount(false))
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : FragmentSystemBarStyle(this) {
        override fun onInit() {
            setMockNaviBarEnable(parentFragment !is PanelFragment)
            setNaviBarColor(Color.TRANSPARENT)
        }

        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            setNaviBarColor(Color.TRANSPARENT)

            if (parentFragment is PanelFragment) return
            windowInsets.naviBarInsets().apply {
                val statusHeight = statusBarHeight()
                val bottomPadding: Int = if (hasVirtualKey()) bottom else 0
                setContentPadding(left, statusHeight, right, bottomPadding)
            }
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        correctViewPagerPosition()
    }

    /**
     *  纠正viewpager当前tab内容显示不全或显示两个tab的内容
     * 问题现象：
     * RTL布局（如设置为阿拉伯语言等）相册---进入 轻量选图 （如新建图集）---手机横屏，会显示两tab的内容，即显示图集tab和照片的内容
     */
    private fun correctViewPagerPosition() {
        viewPager?.let {
            it.getRecyclerView()?.scrollToPosition(it.currentItem)
        }
    }

    private fun COUIViewPager2.getRecyclerView(): RecyclerView? {
        return getChildAt(0) as? RecyclerView
    }

    override fun onDestroy() {
        viewModel.exitSelectionMode()
        super.onDestroy()
    }

    companion object {
        private const val DELAY_VIEW_PAGER_SET_OFF_SCREEN_PAGE_LIMIT = 600L
    }

    override fun rebindActivity() = Unit

    override fun setSelectionMode(selectionMode: Boolean) = Unit

    override fun setMenuEnable(menuId: Int, isEnabled: Boolean) = Unit

    override fun setTitle(charSequence: CharSequence?) = Unit

    override fun setTitleAppearance(id: Int) = Unit

    override fun setTitleTextColor(color: Int) = Unit

    override fun setSubtitle(subtitle: CharSequence?) = Unit

    override fun setIsTitleCenterStyle(isTitleCenterStyle: Boolean) = Unit

    override fun setListScrollPosition(scrollPosition: Int) {
        currentScrollPosition = scrollPosition
        toolbarAnimationHelper?.onListScrolled(scrollPosition)
    }

    override fun setListScrollStateIdle(view: View, scrollPosition: Int) = Unit

    override fun setDividerVisible(isVisible: Boolean) {
        divider?.visibility = if (isVisible) View.VISIBLE else View.INVISIBLE
    }

    override fun setRedDot(menuId: Int, num: Int) = Unit
}