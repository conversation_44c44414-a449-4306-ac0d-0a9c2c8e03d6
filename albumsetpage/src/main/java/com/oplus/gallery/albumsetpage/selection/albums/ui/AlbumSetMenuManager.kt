/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumSetMenuManager.kt
 ** Description:图集长按菜单管理类
 ** Version: 1.0
 ** Date: 2025/4/29
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery     2025/4/29     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.albumsetpage.selection.albums.ui

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import com.oplus.gallery.albumsetpage.allalbum.ui.ALBUM_TYPE_TAB_MY_ALBUMS
import com.oplus.gallery.albumsetpage.allalbum.ui.ALBUM_TYPE_TAB_SHORTCUT_ALBUM
import com.oplus.gallery.albumsetpage.allalbum.ui.IAlbumType
import com.oplus.gallery.albumsetpage.shortcutalbum.BlurViewHelper
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.AlbumViewData.Companion.SUPPORT_IS_ADDED
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.menuoperation.helper.AlbumHideOnTimeLinePageHelper
import com.oplus.gallery.business_lib.model.data.cachealbum.set.CacheAlbumHelper
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ALL_PICTURE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ALL_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CAMERA_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CARD_ID_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CLEAN_UP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CSHOT_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.DOLBY_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ENHANCE_TEXT_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.FAST_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.FAVORITE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.GIF_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.LOG_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.MAP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.OLIVE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.PANORAMA_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.PORTRAIT_BLUR_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.RAW_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.RECYCLE_BIN_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ROOT_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SAFE_BOX_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SCREEN_SHOT_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SLOW_MOTION_ALBUM_ID
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ALBUM_SAFE_BOX
import kotlinx.coroutines.CoroutineScope
import com.support.appcompat.R as couiR

/**
 * 图集长按菜单管理类
 */
class AlbumSetMenuManager(val lifecycleFields: LifecycleFields) {
    /**
     * 弹出菜单popupWindow
     */
    private val popupMenuHelper by lazy {
        MenuPopupWindow<MenuItemEntry>()
    }

    /**
     * 是否支持私密图集
     */
    private val isSupportAlbumSafeBox by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ALBUM_SAFE_BOX)
    }

    private val clickMenuImpl by lazy { AlbumSetMenuActionImpl(lifecycleFields) }
    private var blurViewHelper: BlurViewHelper? = null

    /** 菜单对上层的回调：上层通过livedata,监听功能状态*/
    val menuActionCallbackLiveData by lazy { clickMenuImpl.menuActionCallbackLiveData }

    /**
     * 获取图集不显示在照片页的状态
     */
    private fun getAlbumHideOnTimeLinePageState(viewData: AlbumViewData): MenuItemEntry {
        //isAlbumHide=true 表示不显示在照片页的图集表中有数据，说明当前图集没有在照片页中，菜单不显示在照片页为选中状态
        val isAlbumHide = AlbumHideOnTimeLinePageHelper.findAlbumHideOnTimeLinePageItem(viewData.albumId)
        val menuTextColor = if (isAlbumHide) R.color.shortcut_album_menu_text_selected_color else R.color.shortcut_album_menu_text_default_color
        return MenuItemEntry(R.string.base_album_hide_on_time_line_page, menuTextColor, isAlbumHide)
    }

    /**
     * 根据图集移入常用图集的状态显示相应的菜单项
     */
    private fun getAlbumMovableEntry(viewData: AlbumViewData): MenuItemEntry {
        return if (viewData.supportedAbilities?.getBoolean(SUPPORT_IS_ADDED) == true) {
            moveOutShortcutAlbumEntry
        } else {
            moveInShortcutAlbumEntry
        }
    }

    /**
     * 1、最近项目、相机、视频长按没有反应。
     * 2、截屏录屏  -》移出常用图集、不显示在图片页
     * 3、媒体类型,更多项目（私密、删除、建议）、个人收藏、地图、卡包 -》 移出常用图集
     * 4、其他图集 -》不显示在照片页、私密、重命名、移出常用图集、删除
     */
    private fun getMenuList(viewData: AlbumViewData): List<MenuItemEntry> {
        val menuList: MutableList<MenuItemEntry> = mutableListOf()
        when (viewData.albumId) {
            // 最近项目、相机、视频长按没有反应
            ALL_PICTURE_ALBUM_ID,
            CAMERA_ALBUM_ID,
            ALL_VIDEO_ALBUM_ID -> return menuList

            //截屏录屏长按 移出常用图集、不显示在图片页
            SCREEN_SHOT_ALBUM_ID -> {
                menuList.add(getAlbumHideOnTimeLinePageState(viewData))
                menuList.add(getAlbumMovableEntry(viewData))
            }
            //媒体类型,更多项目（私密、删除、建议）、个人收藏、地图、卡包 移出常用图集
            SAFE_BOX_ALBUM_ID,
            CLEAN_UP_ALBUM_ID,
            RECYCLE_BIN_ALBUM_ID,
            //媒体类型
            OLIVE_ALBUM_ID,
            DOLBY_ALBUM_ID,
            GIF_ALBUM_ID,
            PORTRAIT_BLUR_ALBUM_ID,
            RAW_ALBUM_ID,
            CSHOT_ALBUM_ID,
            SLOW_MOTION_ALBUM_ID,
            LOG_VIDEO_ALBUM_ID,
            FAST_VIDEO_ALBUM_ID,
            PANORAMA_ALBUM_ID,
            //个人收藏、地图
            FAVORITE_ALBUM_ID,
            MAP_ALBUM_ID -> { menuList.add(getAlbumMovableEntry(viewData)) }
            //卡证档案
            CARD_ID_ALBUM_ID -> {
                menuList.add(getAlbumHideOnTimeLinePageState(viewData))
                if (isSupportAlbumSafeBox) menuList.add(safeBoxEntry)
                if (CacheAlbumHelper.supportCardCase().not()) {
                    menuList.add(renameEntry)
                }
                menuList.add(getAlbumMovableEntry(viewData))
                menuList.add(deleteEntry)
            }
            //超级文本，不能重命名，虚拟图集，没有不显示在照片页的选项
            ENHANCE_TEXT_ALBUM_ID -> {
                if (isSupportAlbumSafeBox) menuList.add(safeBoxEntry)
                menuList.add(getAlbumMovableEntry(viewData))
                menuList.add(deleteEntry)
            }
            //根目录没有不显示在照片页的功能
            ROOT_ALBUM_ID -> {
                if (isSupportAlbumSafeBox) menuList.add(safeBoxEntry)
                menuList.add(renameEntry)
                menuList.add(getAlbumMovableEntry(viewData))
                menuList.add(deleteEntry)
            }
            //普通图集
            else -> {
                menuList.add(getAlbumHideOnTimeLinePageState(viewData))
                if (isSupportAlbumSafeBox) menuList.add(safeBoxEntry)
                menuList.add(renameEntry)
                menuList.add(getAlbumMovableEntry(viewData))
                menuList.add(deleteEntry)
            }
        }
        return menuList
    }

    /**
     * @param floatingViewBackgroundColor
     * 场景：长按我的图集的item,item背景的颜色
     */
    @SuppressLint("ClickableViewAccessibility")
    fun showMenuIfNeed(
        anchorView: View,
        viewData: AlbumViewData?,
        containerView: ViewGroup,
        offsetX: Int,
        floatingViewBackgroundColor: Int,
        popupWindowDismissListener: (() -> Unit)
    ) {
        viewData ?: run {
            GLog.d(TAG, DL) { "showMenuIfNeed: s $viewData" }
            return
        }
        getMenuList(viewData).takeIf { it.isNotEmpty() }?.let { menuList ->
            clickMenuImpl.selectedAlbumViewData = viewData
            clickMenuImpl.setMenuArguments(anchorView.getTag(ORIGIN_ARGUMENTS_BUNDLE_KEY) as? Bundle?)
            popupMenuHelper.showMenuWindow(anchorView, menuList, clickMenuImpl)
            popupMenuHelper.setOnPopupWindowDismissListener(onDismissListener = {
                popupWindowDismissListener.invoke()
                blurViewHelper?.hide()
            })
            blurViewHelper = BlurViewHelper(anchorView, containerView)
            blurViewHelper?.show(offsetX, floatingViewBackgroundColor)
        } ?: GLog.d(TAG, DL) { "showMenuIfNeed: album not support edit $viewData" }
    }
    /** FloatingView up时，需要恢复原始大小*/
    fun startFloatingViewReleaseAnimation() {
        blurViewHelper?.startFloatingViewReleaseAnimation()
    }

    fun clear() {
        clickMenuImpl.clear()
        popupMenuHelper.dismiss()
    }

    companion object {
        private const val TAG = "AlbumSetMenuManager"
        const val ALBUM_MOVEABLE_SHORTCUT = "album_moveable_shortcut"
        const val SHORT_CUT_COUNT = "short_cut_count"

        // 视图携带参数的key
        val ORIGIN_ARGUMENTS_BUNDLE_KEY = R.id.base_view_arg_bundle_key

        private val safeBoxEntry by lazy {
            MenuItemEntry(R.string.base_safe_encryption_nemu, R.color.shortcut_album_menu_text_default_color)
        }
        private val renameEntry by lazy {
            MenuItemEntry(R.string.base_rename_photo, R.color.shortcut_album_menu_text_default_color)
        }
        private val moveOutShortcutAlbumEntry by lazy {
            MenuItemEntry(R.string.shortcut_album_move_out_nemu, couiR.color.coui_color_error)
        }
        private val deleteEntry by lazy {
            MenuItemEntry(R.string.base_comfirm_delete, couiR.color.coui_color_error)
        }
        private val moveInShortcutAlbumEntry by lazy {
            MenuItemEntry(R.string.add_to_short_cut_album_set, R.color.shortcut_album_menu_text_default_color)
        }

        /**
         * 不支持长按的图集:如虚拟图集
         *  to do zwr---
         */
        fun isUnsupportLongClick(@IAlbumType type: Int, albumViewData: AlbumViewData?): Boolean {
            albumViewData ?: return true
            if ((type == ALBUM_TYPE_TAB_SHORTCUT_ALBUM) || type == ALBUM_TYPE_TAB_MY_ALBUMS) {
                return when (albumViewData.albumId) {
                    ALL_PICTURE_ALBUM_ID,
                    CAMERA_ALBUM_ID,
                    ALL_VIDEO_ALBUM_ID -> true

                    else -> false
                }
            }
            return true
        }
    }
}

data class LifecycleFields(
    val activity: BaseActivity?,
    val lifecycleScope: CoroutineScope,
    val lifecycle: Lifecycle,
)