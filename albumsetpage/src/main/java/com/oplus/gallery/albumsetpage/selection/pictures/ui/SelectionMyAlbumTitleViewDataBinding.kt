/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AlbumSetEditHeaderViewDataBinding.kt
 ** Description : 我的图集标题，以Header的形式添加。
 ** Version     : 1.0
 ** Date        : 2025/04/28 11:04
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>           <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/04/28      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.selection.pictures.ui

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.baselist.view.ListViewDataBinding
import com.oplus.gallery.standard_lib.bean.ViewData

/**
 * 我的图集标题，以head形式add到列表中
 */
class SelectionMyAlbumTitleViewDataBinding(context: Context) : ListViewDataBinding<ViewData>(context) {
    private var flTitle: View? = null
    override fun onCreateView(parent: ViewGroup, viewType: Int): View {
        return LayoutInflater.from(parent.context).inflate(R.layout.album_set_my_albums_title, parent, false).apply {
            flTitle = findViewById(R.id.flTitle)
        }
    }

    override fun copyLayout(context: Context): ListViewDataBinding<ViewData> {
        return this
    }

    override fun onBindViewHolder(itemViewHolder: BaseListViewHolder<ViewData>, position: Int, viewData: ViewData?): Unit = Unit

    fun setVisible(isVisible: Boolean) {
        flTitle?.isVisible = isVisible
    }
}