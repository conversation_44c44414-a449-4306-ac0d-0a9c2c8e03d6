/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MenuPopupWindow.kt
 ** Description:长按弹出菜单帮助类
 ** Version: 1.0
 ** Date: 2025/4/10
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/4/3     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.albumsetpage.selection.albums.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.res.ColorStateList
import android.view.MotionEvent
import android.view.View
import android.widget.PopupWindow
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.poplist.PopupMenuConfigRule
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils

/**
 * 弹出菜单帮助类
 */
class MenuPopupWindow<T : MenuItemEntry> {

    private var popupWindow: COUIPopupListWindow? = null

    fun showMenuWindow(
        anchorView: View,
        menuList: List<T>,
        onMenuClickCallback: OnMenuClickCallback<T>? = null
    ) {
        if (menuList.isEmpty()) {
            GLog.d(TAG, DL) { "showMenuWindow: item not support long press" }
            return
        }

        val popupMenuItemList = ArrayList<PopupListItem>(menuList.size).apply {
            val builder = PopupListItem.Builder()
            menuList.forEach { menuItem ->
                builder.setTitle(anchorView.context.resources.getString(menuItem.contentStringId))
                    .setIsEnable(true)
                    .setSubMenuItemList(null)
                builder.setIsChecked(menuItem.isChecked)
                builder.setTitleColorList(anchorView.context.getColor(menuItem.colorResId).let { ColorStateList.valueOf(it) })
                add(builder.build())
            }
        }
        show(anchorView, menuList, popupMenuItemList, onMenuClickCallback)
    }

    private fun isShowing(): Boolean = popupWindow?.isShowing ?: false

    fun dismiss() {
        popupWindow?.dismiss()
    }

    fun setOnPopupWindowDismissListener(onDismissListener: PopupWindow.OnDismissListener) {
        popupWindow?.setOnDismissListener(onDismissListener)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun show(
        anchorView: View,
        menuList: List<T>,
        popupMenuItemList: ArrayList<PopupListItem>,
        onMenuClickCallback: OnMenuClickCallback<T>?
    ) {
        if (isShowing()) dismiss()
        if (popupWindow == null) {
            popupWindow = COUIPopupListWindow(anchorView.context).apply {
                setDismissTouchOutside(true)
                setUseBackgroundBlur(false)
            }
        }
        popupWindow?.apply {
            //解决点击蒙层事件没有传递下去的问题
            setTouchInterceptor { _, event ->
                if (event.action == MotionEvent.ACTION_DOWN) {
                    val rootView = (anchorView.context as? Activity)?.window?.decorView
                    // 传递事件到根布局
                    rootView?.dispatchTouchEvent(event)
                    false
                } else {
                    false
                }
            }
            setOnItemClickListener { _, _, pos, _ ->
                // 处理菜单点击逻辑
                onMenuClickCallback?.onMenuClicked(menuList[pos])
                popupWindow?.takeIf { it.isShowing }?.dismiss()
            }
            itemList = popupMenuItemList
            dismiss()
            if ((anchorView as? PopupMenuConfigRule)?.barrierDirection == PopupMenuConfigRule.BARRIER_FROM_LEFT) {
                setGlobalOffset(ResourceUtils.dp2px(anchorView.context, OFFSET_LEFT_X), OFFSET_Y)
            } else {
                setGlobalOffset(ResourceUtils.dp2px(anchorView.context, OFFSET_RIGHT_X), OFFSET_Y)
            }
            //取消默认窗口屏障、取消默认居中对齐，自定义规则需要继承PopupMenuConfigRule，自己实现
            setNonApplicationType(false, false)
            show(anchorView)
        }
    }

    companion object {
        private const val TAG = "MenuPopupWindow"
        private const val OFFSET_LEFT_X = -50f
        private const val OFFSET_RIGHT_X = 50f
        private const val OFFSET_Y = 0
    }
}

interface OnMenuClickCallback<T : MenuItemEntry> {
    fun onMenuClicked(menuEntry: T)
}

data class MenuItemEntry(@StringRes val contentStringId: Int, @ColorRes val colorResId: Int, var isChecked: Boolean = false)