/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonPetAlbumSetFragment.kt
 ** Description: 人物与宠物列表页：人宠群组列表 + 人宠图集列表
 **
 ** Version: 1.0
 ** Date: 2025/04/07
 ** Author: xiaxudong@OppoGallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiaxudong@OppoGallery3D        2025/04/07   1.0          init
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.personpet.ui

import android.app.Activity
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.updateLayoutParams
import androidx.core.view.updateMargins
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GalleryGridLayoutManager
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.personpet.PersonPetAlbumSetHeaderViewDataBinding
import com.oplus.gallery.albumsetpage.personpet.PersonPetAlbumSetViewDataBinding
import com.oplus.gallery.albumsetpage.personpet.PersonPetAlbumViewData
import com.oplus.gallery.albumsetpage.personpet.PersonPetGroupViewData
import com.oplus.gallery.albumsetpage.personpet.view.PersonPetAlbumSetListItemAnimator
import com.oplus.gallery.albumsetpage.personpet.viewmodel.PersonPetAlbumSetViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.NetworkFloatingViewHelper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper.SUPPORT_SELECTED
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isFlat
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_PERSON_PET_THIRD
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_OPEN_EXIT
import com.oplus.gallery.basebiz.uikit.fragment.transition.SlideTransitionAnimation
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.basebiz.uikit.isSelected
import com.oplus.gallery.basebiz.util.AlbumCardListUtils
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.basebiz.widget.FloatingTipsView
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.helper.PermissionDialogHelper
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PERSON_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PERSON_PET_GROUP_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper.TYPE_PET_ALBUM_SET
import com.oplus.gallery.business_lib.model.data.personpet.utils.PersonPetDataHelper
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA_LIST
import com.oplus.gallery.business_lib.selectionpage.SelectAlbumInputData
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumSetFragment
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.authorizing.ui.ListItemInfo
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.ui.dialog.SingleEditDialog
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.math.MathUtils.ONE
import com.oplus.gallery.foundation.util.math.MathUtils.ZERO
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PET_CLASSIFY
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonPetModelGetter.Companion.TYPE_PERSON_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonPetModelGetter.Companion.TYPE_PERSON_PET_GROUP_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonPetModelGetter.Companion.TYPE_PET_ALBUM
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.baselist.adapter.config.AbsAdapterConfigProvider
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.BaseListAdapter
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as BaseBizR

/**
 * 人物与宠物列表页：人宠群组列表 + 人宠图集列表
 */
@RouterNormal(RouterConstants.RouterName.PERSON_PET_ALBUM_SET_FRAGMENT)
class PersonPetAlbumSetFragment : BaseAlbumSetFragment() {
    override val needExitWhenEmpty: Boolean = false
    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_PERSON_ALBUM_SET_FRAGMENT
    private var isFirstShowDialog = true
    private var isGroupSelectionMode = false
    private var personPetNameEditDialog: SingleEditDialog? = null
    private val onHeaderSelectItemChange = { selectedCount: Int ->
        onSelectionStateChange()
    }
    private val onHeaderSelectionModeChange = { isSelectionMode: Boolean ->
        isGroupSelectionMode = isSelectionMode
        val result = if (isSelectionMode) {
            changeShowSelectionMode(false)
            super.enterSelectionMode()
        } else {
            super.exitSelectionMode()
        }
    }
    private val confirmListener = object : SingleEditDialog.ConfirmListener {
        override fun onSaved(text: String?) {
            isFirstShowDialog = false
            text ?: run {
                GLog.w(logTag, DL, "onSaved, text is null")
                return
            }
            getSelectedItem { albumViewData ->
                AppScope.launch(Dispatchers.IO) { renameGroup(albumViewData, text) }
                AlbumsActionTrackHelper.trackAndSendMenuOperationClickForPet(changeName = EMPTY_STRING)
                personPetNameEditDialog?.dismiss()
                personPetNameEditDialog = null
                // 重命名后需要退出选择模式
                if (isResumed) {
                    exitSelectionMode()
                }
            }
        }

        override fun onCancelled() {
            // No need to do something
            personPetNameEditDialog?.dismiss()
            personPetNameEditDialog = null
        }

        private fun getAlbumType(modelType: String): Int = when (modelType) {
            TYPE_PERSON_ALBUM -> TYPE_PERSON_ALBUM_SET
            TYPE_PET_ALBUM -> TYPE_PET_ALBUM_SET
            TYPE_PERSON_PET_GROUP_ALBUM -> TYPE_PERSON_PET_GROUP_ALBUM_SET
            else -> TYPE_PERSON_ALBUM_SET
        }

        private fun getPersonPetId(albumViewData: AlbumViewData): Long {
            return when (albumViewData) {
                is PersonPetGroupViewData -> albumViewData.groupId
                is PersonPetAlbumViewData -> albumViewData.personPetId
                else -> {
                    GLog.d(TAG, "getAlbumId: viewData clz type not support!!")
                    return 0L
                }
            }
        }

        private fun renameGroup(albumViewData: AlbumViewData, text: String) {
            val personPetId = getPersonPetId(albumViewData)
            ApiDmManager.getScanDM().renameGroup(ContextGetter.context, personPetId, text, getAlbumType(albumViewData.modelType))
        }
    }

    private val networkFloatingViewAsync = AsyncObj(lifecycleScope) {
        NetworkFloatingViewHelper.initNetWorkViewByType(
            type = NetworkFloatingViewHelper.TipsType.PERSON,
            context = requireContext(),
            onClickCallback = fun(_: NetworkFloatingViewHelper.ClickType) {
                refreshNetworkFloatingView()
            }
        ).apply {
            view?.post {
                activity ?: return@post
                if ((activity?.isDestroyed == true) || (activity?.isFinishing == true)) return@post
                val lp = CoordinatorLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                lp.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                (view as? ViewGroup)?.addView(this, lp)
                updateFloatingTipsMargin()
            }
        }
    }

    private fun FloatingTipsView.updateFloatingTipsMargin() {
        layoutParams?.apply {
            updateLayoutParams<ViewGroup.MarginLayoutParams> {
                val leftOrRightMargin = resources.getDimensionPixelSize(BaseBizR.dimen.main_floating_network_margin_horizontal)
                updateMargins(
                    left = leftOrRightMargin,
                    right = leftOrRightMargin,
                    bottom = if (ScreenUtils.isMiddleAndLargeScreen(context)) {
                        resources.getDimensionPixelSize(BaseBizR.dimen.main_floating_layout_margin_bottom_height)
                    } else {
                        resources.getDimensionPixelSize(com.support.snackbar.R.dimen.coui_snack_bar_margin_bottom)
                    }
                )
            }
        }
    }

    override fun onFragmentTransitionStart(animation: Animation, animId: Int) {
        super.onFragmentTransitionStart(animation, animId)
        /**
         * 解散合照时，会自动关闭合照详情页，回到人宠详情页。因为ViewModel中的contentChangeListener触发的时机比转场动画早，
         * 而contentChangeListener又会触发重新刷新数据，所以需要在当前页面打开合照详情页时，自己退场的时候就关闭对 contentChange 的响应，
         * 当重新回到当前页后，在onResume() 中触发重新加载数据
         */
        if (animId == SEAMLESS_ANIM_OPEN_EXIT) {
            headerViewDataBinding?.setSkipLoadData(true)
        }
    }

    override fun onFragmentTransitionEnd(animation: Animation, animId: Int) {
        super.onFragmentTransitionEnd(animation, animId)
        if ((animId == SeamlessTransitionAnimation.SEAMLESS_ANIM_CLOSE_ENTER)
            || (animId == SlideTransitionAnimation.SLIDE_CLOSE_ENTER_ANIMATION)
        ) {
            headerViewDataBinding?.setSkipLoadData(false)
        }
    }

    private var headerViewDataBinding: PersonPetAlbumSetHeaderViewDataBinding? = null

    private val isSupportPetClassify by lazy { ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY) }

    private fun gotoGroupAlbumSetPage() {
        startByStack<PersonPetGroupAlbumSetFragment>(
            resId = BaseBizR.id.base_fragment_container,
            fragmentClass = PersonPetGroupAlbumSetFragment::class.java,
            anim = DEFAULT_ANIM_ARRAY,
        )
    }

    override fun getLayoutId() = R.layout.album_fragment_person_pet_set

    override fun refreshCOUIScrollbar() {}

    private fun startPersonPetSelectionFragment() {
        val activity = requireActivity()
        Starter.DialogFragmentStarter<PanelDialog>(
            fm = activity.supportFragmentManager,
            bundle = SelectAlbumInputData(
                title = resources.getString(getPersonPetSelectTitleResId()),
                fromPath = EMPTY_STRING,
                moveCount = 0,
                modelType = DataRepository.PersonPetModelGetter.TYPE_PERSON_PET_ALBUM_SET,
                selectMulti = true
            ).createBundle().also {
                it.putString(KEY_REQUEST_KEY, REQUEST_KEY_CREATE_PERSON_PET_GROUP)
            },
            postCard = PostCard(RouterConstants.RouterName.SELECTION_ALBUM_PANEL_DIALOG),
        ).start()?.apply {
            setFragmentResultListenerSafety(REQUEST_KEY_CREATE_PERSON_PET_GROUP) { _, bundle ->
                if (bundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) return@setFragmentResultListenerSafety
                val stringPathList = bundle.getStringArray(KEY_RESULT_DATA_LIST) ?: return@setFragmentResultListenerSafety
                lifecycleScope.launch(Dispatchers.IO) {
                    /**
                     * 对选中的图集列表进行分类存储到map中，以图集类型作为key，值为path list,作为创建合照的参数
                     * 比如：选中的图集中可能同时包含人物图集和宠物图集
                     */
                    val paths = stringPathList.map { Path.fromString(it) }
                    createGroupAndExitSelectionMode(paths)
                }
            }
        }
    }

    private var showMoreButton = true
    override fun getBottomBarMenuId(): Int = if (isGroupSelectionMode) {
        R.menu.albumsetpage_selection_person_pet_group_album_set_split_tab
    } else {
        R.menu.album_selection_all_person_pet_album_set_split_tab
    }

    override fun getEmptyPageIconAnimRes(): Int = BaseBizR.raw.base_empty_view_normal

    override fun getEmptyPageTitleRes(): Int = BaseBizR.string.base_no_photo_or_video_tips
    override fun getEmptyPageSubtitleRes(): Int = if (isSupportPetClassify) {
        BaseBizR.string.people_and_pets_empty_state_desc
    } else {
        BaseBizR.string.base_no_face_album_detail_tips
    }

    private fun refreshNetworkFloatingView() {
        networkFloatingViewAsync.getIt { networkFloatingView ->
            NetworkFloatingViewHelper.refreshNetworkFloatingView(networkFloatingView, lifecycleScope) {
                shouldShowNetworkFloatingView()
            }
        }
    }

    private suspend fun shouldShowNetworkFloatingView(): Boolean {
        return !isInSelectionMode()
                && NetworkFloatingViewHelper.isFirstClosedPersonAlbumNetworkTip()
                && (!NetworkPermissionManager.isUseOpenNetwork)
                /* 浮窗弹出的逻辑是如果权限通过了才轮到判断联网，人物图集页同意的权限有人脸扫描和资源模型更新，
                但是人脸扫描的权限申请没有申请联网，所以这里只需要判断资源模型下载的权限是否通过 */
                && isPrivacyAuthorizedSuspend(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
    }

    override fun refreshToolbar() {
        bindToolbarToActivity()
        if (!isInSelectionMode()) {
            toolbar?.title = getString(getPersonPetTitleResId())
        }
        toolbar?.isTitleCenterStyle = false
    }

    override fun isShowEmptyPageSubtitle(): Boolean = true

    override fun initLayoutDetail(context: Context): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        val contentWidth = getContentWidth()
        parentWidth = contentWidth
        edgeWidth = context.resources.getDimensionPixelSize(BaseBizR.dimen.base_person_pet_group_title_padding_h)
        gapWidth = context.resources.getDimensionPixelSize(BaseBizR.dimen.base_album_set_fragment_item_view_horizontal_gap)
        spanCount = AlbumCardListUtils.getFaceSpanCount(context, contentWidth)
    }.build().apply {
        headerCount = ONE
        itemDecorationGapPx.top = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_top_gap)
        itemDecorationGapPx.bottom = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_bottom_gap)
    }

    /**
     * 获取当前屏幕(减去侧边栏）的宽度
     */
    override fun getContentWidth(): Int {
        var slideWidth = 0
        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let { slideWidth = if (it.isFlat() && it.isOpen()) it.getSlideWidth() else ZERO }
        }
        return getCurrentAppUiConfig().windowWidth.current - slideWidth
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        GLog.d(logTag, DL) { "onSidePaneSlideStart: isResumed:$isResumed newState=$newState $clazzSimpleName" }
        headerViewDataBinding?.onSidePaneSlideStart()
        context?.takeIf { isResumed && newState.isFlat() }?.let {
            headerViewDataBinding?.onLayoutChanged()
            notifyConfigurationChangedIfNeed()
        }
    }

    /**
     * 根据是否支持宠物分类来获取标题文案
     */
    private fun getPersonPetTitleResId(): Int {
        return if (isSupportPetClassify) {
            when {
                PersonPetDataHelper.isOnlyPet() -> com.oplus.gallery.framework.datatmp.R.string.pet
                PersonPetDataHelper.isOnlyPerson() -> BaseBizR.string.model_face_album_set
                else -> BaseBizR.string.person_pet_group_title
            }
        } else {
            BaseBizR.string.model_face_album_set
        }
    }

    /**
     * 根据是否支持宠物分类来获取选择模式下的标题文案
     */
    private fun getPersonPetSelectTitleResId(): Int {
        return if (isSupportPetClassify) {
            when {
                PersonPetDataHelper.isOnlyPet() -> BaseBizR.string.choose_pets
                PersonPetDataHelper.isOnlyPerson() -> BaseBizR.string.base_select_face_album
                else -> BaseBizR.string.choose_people_and_pets
            }
        } else {
            BaseBizR.string.base_select_face_album
        }
    }

    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        headerViewDataBinding?.onSidePaneSlideEnd()
    }

    private fun notifyConfigurationChangedIfNeed() {
        if (layoutDetail.spanCount == AlbumCardListUtils.getFaceSpanCount(context, getContentWidth())) {
            return
        }
        layoutDetail = initLayoutDetail(requireContext())
        (recyclerView?.layoutManager as? GalleryGridLayoutManager)?.let {
            (layoutDetail as? GridLayoutDetail)?.replaceGaps((layoutDetail as GridLayoutDetail).itemHorizontalGaps)
            removeAllItemDecorations()
            addItemDecoration()
            it.spanCount = layoutDetail.spanCount
        }
        recyclerAdapter.let { it.notifyItemRangeChanged(ZERO, it.itemCount) }
    }

    override fun dispatchLongClick(position: Int): Boolean {
        return isInSelectionMode()
    }

    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
        // 点击进入人物/宠物详情页的事件处理，先把空实现移植过来。
        viewData?.let {
            if (isInSelectionMode()) {
                if (it is PersonPetAlbumViewData) {
                    baseListViewModel?.toggleItemSelection(viewData.position)
                }
                return@let
            }
            // 跳转人物/宠物/合照详情页
            gotoDetailFragment(viewData, itemView)
        }
    }

    private fun gotoDetailFragment(viewData: AlbumViewData, itemView: View?) {
        val currentAlbumId = when (viewData) {
            is PersonPetGroupViewData -> viewData.groupId
            is PersonPetAlbumViewData -> viewData.personPetId
            else -> {
                GLog.d(TAG, "gotoDetailFragment: viewData clz type not support!!!")
                return
            }
        }

        val isNeedSpringTransition = (viewData is PersonPetGroupViewData)
        if (isNeedSpringTransition) {
            resetSeamlessAnimRootNodeConfig(
                resources.configuration.orientation,
                resources.configuration.screenWidthDp,
                resources.configuration.screenHeightDp
            )
        }
        val floatingWindowOffset = IntArray(2)
        if (isFloatingWindowMode()) view?.rootView?.getLocationOnScreen(floatingWindowOffset)
        startByStack<PersonPetDetailAlbumFragment>(
            resId = BaseBizR.id.base_fragment_container,
            fragmentClass = PersonPetDetailAlbumFragment::class.java,
            anim = if (isNeedSpringTransition) SEAMLESS_ANIM_ARRAY else DEFAULT_ANIM_ARRAY,
            data = Bundle().also {
                it.putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
                if (isNeedSpringTransition) {
                    FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                        context,
                        it,
                        itemView,
                        TriggerViewRectGetter(itemView, floatingWindowOffset),
                        ENTRANCE_PERSON_PET_THIRD
                    )
                }
            }
        )
        GLog.d(TAG, "gotoDetailFragment: person pet album set: $currentAlbumId")
    }

    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> = ViewModelProvider(this)[PersonPetAlbumSetViewModel::class.java]

    override fun getAdapterItemConfigs(): List<ItemConfig<AlbumViewData>> {
        return mutableListOf<ItemConfig<AlbumViewData>>().apply {
            context?.let { context ->
                val dataBinding = PersonPetAlbumSetViewDataBinding(context, baseListViewModel, { isMaskVisible() }, { checkboxAnimEnable() })
                add(ItemConfig(PersonPetAlbumViewData::class.java, dataBinding))
            }
        }
    }

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        context?.let {
            val thumbStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_CIRCLE)
                put(StyleData.KEY_THUMB_SIZE_TYPE, getThumbSizeType(layoutDetail.itemWidth))
                put(
                    StyleData.KEY_THUMB_STROKE_WIDTH,
                    resources.getDimension(com.oplus.gallery.foundation.ui.R.dimen.common_round_drawable_frame_stroke_width)
                )
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, resources.getColor(BaseBizR.color.common_round_drawable_frame_stroke_color, null))
                put(
                    StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR,
                    COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPressBackground)
                )
            }
            viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, thumbStyleData)
            val gridThumbStyleData = thumbStyleData.copy().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
                put(StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS, resources.getDimension(BaseBizR.dimen.base_album_item_cover_image_corner_radius))
                put(StyleData.KEY_THUMB_SIZE_TYPE, getThumbSizeType(layoutDetail.itemWidth))
                put(StyleData.KEY_THUMB_LAYOUT_PADDING_BORDER, resources.getDimension(BaseBizR.dimen.common_grid_drawable_child_rect_padding))
                put(StyleData.KEY_THUMB_SIZE_WIDTH, layoutDetail.itemWidth)
                put(StyleData.KEY_THUMB_SIZE_HEIGHT, layoutDetail.itemWidth)
                put(StyleData.KEY_THUMB_LAYOUT_GAP_BETWEEN_CHILD, resources.getDimension(BaseBizR.dimen.common_grid_drawable_child_rect_gap))
                put(
                    StyleData.KEY_THUMB_BACKGROUND,
                    ColorDrawable(COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPressBackground))
                )
                //其他图集封面由四个封面组成，subStyleData为这四个子封面的样式
                put(StyleData.KEY_SUB_STYLE, thumbStyleData.copy().apply {
                    put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_CIRCLE)
                    put(
                        StyleData.KEY_THUMB_BACKGROUND,
                        ColorDrawable(COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPressBackground))
                    )
                })
            }
            viewModel.addStyle(StyleType.TYPE_GRID_THUMB_STYLE, gridThumbStyleData)
        }
    }

    override fun getThumbSizeType(itemWidth: Int): Int {
        return ThumbnailSizeUtils.TYPE_FACE_THUMBNAIL
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        headerViewDataBinding = getHeaderView()
        baseListViewModel?.setViewData(
            AlbumViewData(
                id = EMPTY_STRING,
                position = 0,
                modelType = DataRepository.PersonPetModelGetter.TYPE_PERSON_PET_ALBUM_SET,
                isMediaAlbum = true,
                version = 0,
                totalCount = 0,
                title = EMPTY_STRING
            )
        )
        needBottomPaddingWhenEdit = false
        recyclerView?.apply {
            itemAnimator = PersonPetAlbumSetListItemAnimator().also {
                it.onAnimationsFinished = {
                    GLog.d(logTag, DL) { "initRecyclerView onAnimationsFinished refresh=${recyclerAdapter.isNeedRefreshAfterAnimateFinished}" }
                    /*动画过程中,正好加载数据回来后,diff=null,adapter.refreshViewHolder因为有diff会跳过数据刷新,所以动画结束后刷新数据,避免新增或变更的item不刷新
                      场景:人宠图集重命名后，无法立刻显示新名称，需要退出刷新后显示
                     */
                    recyclerAdapter.setDataSet(refresh = recyclerAdapter.isNeedRefreshAfterAnimateFinished)
                    recyclerAdapter.isNeedRefreshAfterAnimateFinished = false
                }
            }
            (this as COUIRecyclerView).let { rv ->
                rv.setItemClickableWhileOverScrolling(false)
                rv.setItemClickableWhileSlowScrolling(false)
            }
            isClickable = !isGroupSelectionMode
        }
    }

    private fun changeShowSelectionMode(isFromSelfChange: Boolean) {
        canShowSelectionMode = isFromSelfChange
        canShowCheckedMode = isFromSelfChange
        recyclerView?.isNestedScrollingEnabled = isFromSelfChange
    }

    override fun onPostRegisterLiveDataObserver() {
        super.onPostRegisterLiveDataObserver()
        (baseListViewModel as? PersonPetAlbumSetViewModel)?.apply {
            createGroupResultLiveData.observe(this@PersonPetAlbumSetFragment) {
                // 此处给ui层根据状态码进行不同的呈现
                when (it) {
                    // 创建合照失败
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_FAIL -> {
                        GLog.e(TAG, DL) { "create group fail." }
                        ToastUtil.showShortToast(BaseBizR.string.photo_group_created_fail)
                    }
                    // 创建已存在的合照成功弹Toast
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATED_GROUP_EXIST -> {
                        GLog.d(TAG, DL) { "create exist group success." }
                        ToastUtil.showShortToast(BaseBizR.string.photo_group_created)
                    }
                    // 创建空的合照提示无合照
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATE_EMPTY_GROUP -> {
                        GLog.e(TAG, DL) { "haven’t group photo." }
                        ToastUtil.showShortToast(R.string.person_pet_group_empty)
                    }
                    // 需要创建空的合照或创建新合照不弹Toast
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_SUCCESS -> GLog.e(TAG, DL) { "create new group." }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        notifyConfigurationChangedIfNeed()
        refreshNetworkFloatingView()
    }

    override fun onTotalCountChanged(totalCount: Int) {
        super.onTotalCountChanged(totalCount)
        refreshNetworkFloatingView()
    }

    override fun onViewDataChanged(viewDataArray: Array<AlbumViewData?>) {
        val needShowMoreButton = viewDataArray.isNotEmpty()
        if (showMoreButton != needShowMoreButton) {
            showMoreButton = !showMoreButton
            activity?.invalidateOptionsMenu()
        }
        if (recyclerAdapter.totalCount <= ONE) {
            headerViewDataBinding?.hide()
            return
        }
        if ((recyclerAdapter.headerCount <= ZERO)) {
            addHeaderView()
        }
        if (isInSelectionMode()) return
        val title = getString(getPersonPetTitleResId())
        toolbar?.title = title
        headerViewDataBinding?.setFaceTitle(title)
    }

    private fun getItemSize(): Pair<Int, Int> {
        val pageWidth = getContentWidth()
        if (pageWidth < requireContext().resources.getDimensionPixelSize(BaseBizR.dimen.face_album_set_medium_width)) {
            val edgeWidth = requireContext().resources.getDimensionPixelSize(BaseBizR.dimen.base_person_pet_group_title_padding_h)
            val width = pageWidth - (edgeWidth * 2)
            return Pair(width, (width * HEADER_ITEM_RATE).toInt())
        } else {
            val height = requireContext().resources.getDimensionPixelSize(R.dimen.albumsetpage_person_pet_group_album_set_item_height)
            val width = requireContext().resources.getDimensionPixelSize(R.dimen.albumsetpage_person_pet_group_album_set_item_width)
            return Pair(width, height)
        }
    }

    private fun addHeaderView() {
        view?.post {
            getHeaderView()?.let {
                it.updateLayoutSize()
                recyclerAdapter.addHeaderView(it)
                recyclerAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun getHeaderView(): PersonPetAlbumSetHeaderViewDataBinding? {
        if (!isSupportPetClassify) return null
        headerViewDataBinding = headerViewDataBinding ?: PersonPetAlbumSetHeaderViewDataBinding(
            this, null, this::getItemSize,
            this@PersonPetAlbumSetFragment::gotoGroupAlbumSetPage,
            this@PersonPetAlbumSetFragment::startPersonPetSelectionFragment,
            onViewHideAnimEnd = { removeHeader() }
        ).also {
            it.init()
            it.setSelectItemChangeListener(onHeaderSelectItemChange)
            it.setSelectionModeChangeListener(onHeaderSelectionModeChange)
        }
        return headerViewDataBinding
    }

    private fun removeHeader() {
        recyclerAdapter.apply {
            if (recyclerAdapter.headerCount <= ZERO) return
            recyclerAdapter.notifyItemRangeRemoved(ZERO, headerCount)
            recyclerAdapter.removeHeaders()
        }
        headerViewDataBinding?.setSelectItemChangeListener(null)
        headerViewDataBinding?.setSelectionModeChangeListener(null)
        headerViewDataBinding = null
    }

    override fun needHandleTouchEvent(): Boolean {
        return !isGroupSelectionMode
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (!isResumed) return
        toolbar?.clearMenu()
        if (isInSelectionMode()) {
            toolbar?.isTitleCenterStyle = true
            setDisplayHomeAsUpEnabled(false)
            inflater.inflate(BaseBizR.menu.base_opt_album_cancel, menu)
            menu.findItem(BaseBizR.id.action_cancel)?.let { menuItem ->
                (menuItem.actionView as? COUITextView)?.apply {
                    this.setOnClickListener {
                        if (!ClickUtil.clickable()) return@setOnClickListener
                        onOptionsItemSelected(menuItem)
                    }
                }
            }
        } else {
            setDisplayHomeAsUpEnabled(true)
            toolbar?.isTitleCenterStyle = false
            if ((baseListViewModel?.totalSize ?: 0) <= 0) return
            inflater.inflate(R.menu.main_opt_all_person_pet_set, menu)
            updateToolbarBackMenuItem()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!isResumed) return false
        when (item.itemId) {
            R.id.action_select -> {
                AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                    enterSelect = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE,
                )
                enterSelectionMode()
                return true
            }

            BaseBizR.id.action_cancel -> {
                exitSelectionMode()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)
        if (!isResumed) return
        menu.findItem(R.id.action_select)?.isVisible = !isInSelectionMode() and showMoreButton
    }

    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        super.onBottomMenuBarItemClicked(menuItem)
        val trackCallerEntry = TrackCallerEntry(
            page = trackPage,
            path = baseListViewModel?.trackPath,
            albumsActionCurrentPage = getUserActionCurPage(AlbumsActionTackConstant.TYPE_ALBUMS_ACTION)
        )
        when (menuItem.itemId) {
            // 合并
            R.id.action_merge_person_pet_album -> {
                (baseListViewModel as? PersonPetAlbumSetViewModel)?.getCurrentOperateAlbumSetType()?.let {
                    menuHelper?.doMergePersonPetAlbumAction(
                        albumSetType = it,
                        trackCallerEntry = trackCallerEntry,
                        BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                    )
                }
            }
            // 重命名
            R.id.action_rename_person_pet_album -> getSelectedItem { renameAlbum(it.title, it.modelType) }
            // 移除
            R.id.action_remove -> {
                menuHelper?.doRemovePersonPetAlbumAction(
                    trackCallerEntry = trackCallerEntry,
                    isSupportPetClassify,
                    BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                )
            }
            // 创建合照
            R.id.action_create_group -> createGroupAndExitSelectionMode()
            // 合照重命名
            R.id.action_rename -> getSelectedItem { renameAlbum(it.title, it.modelType) }
            // 解散合照
            R.id.action_disband_group -> headerViewDataBinding?.disbandPersonPetGroups()
        }
    }

    private fun createGroupAndExitSelectionMode(paths: List<Path>? = null) {
        val selectedPaths = paths ?: baseListViewModel?.getSelectedItems()?.toList() ?: emptyList()
        if (isInSelectionMode()) {
            exitSelectionMode()
        }
        (baseListViewModel as? PersonPetAlbumSetViewModel)?.manualCreatePersonPetGroup(selectedPaths)
    }

    private fun renameAlbum(nowTitle: String?, modelType: String) {
        isFirstShowDialog = true
        // 同profileClickCallback，不能直接使用albumViewData
        personPetNameEditDialog = activity?.let { activity ->
            SingleEditDialog.Builder()
                .setContext(activity)
                .setTitle(getRenameDialogTitle(modelType))
                .setHintText(getRenameDialogHint(modelType))
                .setInputEmptyToastResId(getRenameDialogHintResId(modelType))
                .setInputErrorToastResId(getRenameDialogErrorResId(modelType))
                .setInputEmojiErrorToastResId(getRenameDialogErrorResId(modelType))
                .setSpecialCharToastResId(getRenameDialogErrorResId(modelType))
                .setListener(confirmListener)
                .setContent(nowTitle)
                .create()
        }
        if (!isResumed) return
        personPetNameEditDialog?.show()
    }

    private fun getSelectedItem(callback: (data: AlbumViewData) -> Unit) {
        lifecycleScope.launch(Dispatchers.IO) {
            val data = if (isGroupSelectionMode) {
                headerViewDataBinding?.getAdapterData()?.find { it.isSelected() }
            } else {
                recyclerAdapter.getData().find { it.isSelected() }
            }
            withContext(Dispatchers.Main) {
                data?.let { callback.invoke(data) }
            }
        }
    }

    private fun getRenameDialogTitle(modelType: String) = when (modelType) {
        TYPE_PERSON_ALBUM -> resources.getString(com.oplus.gallery.basebiz.R.string.base_face_cover_name)
        TYPE_PET_ALBUM -> resources.getString(com.oplus.gallery.basebiz.R.string.name_the_pets)
        TYPE_PERSON_PET_GROUP_ALBUM -> resources.getString(com.oplus.gallery.basebiz.R.string.name_the_group_photo)
        else -> resources.getString(com.oplus.gallery.basebiz.R.string.base_face_cover_name)
    }

    private fun getRenameDialogHint(modelType: String) = when (modelType) {
        TYPE_PERSON_ALBUM -> resources.getString(com.oplus.gallery.basebiz.R.string.main_face_edit_hint)
        TYPE_PET_ALBUM -> resources.getString(com.oplus.gallery.basebiz.R.string.main_face_pet_edit_hint)
        TYPE_PERSON_PET_GROUP_ALBUM -> resources.getString(com.oplus.gallery.basebiz.R.string.main_face_group_edit_hint)
        else -> resources.getString(com.oplus.gallery.basebiz.R.string.main_face_edit_hint)
    }

    private fun getRenameDialogHintResId(modelType: String) = when (modelType) {
        TYPE_PERSON_ALBUM -> com.oplus.gallery.basebiz.R.string.main_face_person_edit_hint
        TYPE_PET_ALBUM -> com.oplus.gallery.basebiz.R.string.main_face_pet_edit_hint
        TYPE_PERSON_PET_GROUP_ALBUM -> com.oplus.gallery.basebiz.R.string.main_face_group_edit_hint
        else -> com.oplus.gallery.basebiz.R.string.main_face_edit_hint
    }

    private fun getRenameDialogErrorResId(modelType: String) = when (modelType) {
        TYPE_PERSON_ALBUM -> com.oplus.gallery.basebiz.R.string.person_name_invalid_and_re_input
        TYPE_PET_ALBUM -> com.oplus.gallery.basebiz.R.string.pet_name_invalid_and_re_input
        TYPE_PERSON_PET_GROUP_ALBUM -> com.oplus.gallery.basebiz.R.string.person_pet_group_name_invalid_and_re_input
        else -> com.oplus.gallery.basebiz.R.string.person_name_invalid_and_re_input
    }

    override fun enterSelectionMode(): Int {
        changeShowSelectionMode(true)
        headerViewDataBinding?.enterSelectionMode()
        isGroupSelectionMode = false
        return super.enterSelectionMode()
    }

    override fun exitSelectionMode() {
        changeShowSelectionMode(true)
        headerViewDataBinding?.exitSelectionMode()
        isGroupSelectionMode = false
        super.exitSelectionMode()
    }

    override fun onEnterSelection() {
        super.onEnterSelection()
        (recyclerView?.layoutManager as? GalleryGridLayoutManager)?.setScrollVerticallyEnable(!isGroupSelectionMode)
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener { refreshToolbar(true) }
            start()
        }
        bottomMenuBar?.menu?.apply { findItem(R.id.action_create_group)?.isVisible = isSupportPetClassify }
    }

    override fun onExitSelection() {
        super.onExitSelection()
        (recyclerView?.layoutManager as? GalleryGridLayoutManager)?.setScrollVerticallyEnable(!isGroupSelectionMode)
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener { refreshToolbar(false) }
            start()
        }
        bottomMenuBar?.menu?.let {
            it.findItem(R.id.action_merge_person_pet_album)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
            it.findItem(R.id.action_create_group)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
            it.findItem(R.id.action_remove)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
            it.findItem(R.id.action_rename_person_pet_album)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
            it.findItem(R.id.action_rename)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
            it.findItem(R.id.action_disband_group)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
        }
        refreshNetworkFloatingView()
    }

    private fun refreshToolbar(isSelectionMode: Boolean) {
        if (isSelectionMode) {
            toolbarFadeAnimator?.updateSelectionTitleLater = false
            updateToolbarEditTitle(
                defaultTitleResId = getPersonPetSelectTitleResId(),
                count = if (isGroupSelectionMode) {
                    headerViewDataBinding?.getSelectedCount() ?: ZERO
                } else {
                    baseListViewModel?.getSelectedItemCount() ?: ZERO
                }
            )
            refreshNetworkFloatingView()
        } else {
            toolbarFadeAnimator?.updateSelectionTitleLater = true
            toolbar?.title = getString(getPersonPetTitleResId())
        }
        activity?.invalidateOptionsMenu()
    }

    override fun onSelectionStateChange() {
        baseListViewModel?.let { viewModel ->
            val selectedCount = viewModel.getSelectedItemCount()
            val isFromSameMediaSet = (viewModel as? PersonPetAlbumSetViewModel)?.isFromSameAlbumSet()
            bottomMenuBar?.menu?.apply {
                if (isGroupSelectionMode) {
                    findItem(R.id.action_disband_group)?.isEnabled = ((headerViewDataBinding?.getSelectedCount() ?: ZERO) >= SELECTED_SIZE_ONE)
                    findItem(R.id.action_rename)?.isEnabled = ((headerViewDataBinding?.getSelectedCount() ?: ZERO) == SELECTED_SIZE_ONE)
                } else {
                    findItem(R.id.action_rename_person_pet_album)?.isEnabled = (selectedCount == SELECTED_SIZE_ONE)
                    findItem(R.id.action_remove)?.isEnabled = (selectedCount >= SELECTED_SIZE_ONE)
                    (selectedCount > SELECTED_SIZE_ONE).let {
                        findItem(R.id.action_merge_person_pet_album)?.isEnabled = it && (isFromSameMediaSet == true)
                        findItem(R.id.action_create_group)?.isEnabled = it
                    }
                }
            }
            if (isGroupSelectionMode || getString(getPersonPetTitleResId()) != toolbar?.title) {
                updateToolbarEditTitle(
                    defaultTitleResId = getPersonPetSelectTitleResId(),
                    count = if (isGroupSelectionMode) {
                        headerViewDataBinding?.getSelectedCount() ?: ZERO
                    } else {
                        viewModel.getSelectedItemCount()
                    }
                )
            }
        }
    }

    override fun getUserActionCurPage(trackType: String?): String? {
        return when (trackType) {
            AlbumsActionTackConstant.TYPE_ALBUMS_ACTION -> AlbumsActionTackConstant.Value.ALBUMS_ACTION_FACE_ALBUM_SET_PAGE_VALUE
            else -> LaunchExitPopupConstant.Value.FACE_ALBUM_SET_PAGE
        }
    }

    override fun getListAdapter(): BaseListAdapter<AlbumViewData> = object : BaseListAdapter<AlbumViewData>(
        layoutDetail,
        object : AbsAdapterConfigProvider() {
            override val configs: MutableList<ItemConfig<*>> = mutableListOf<ItemConfig<*>>().apply { addAll(getAdapterItemConfigs()) }
        }) {
        override fun rebindViewHolder(baseListViewHolder: BaseListViewHolder<AlbumViewData>, position: Int, newViewData: AlbumViewData?) {
            if (isGroupSelectionMode) {
                newViewData?.supportedAbilities?.putBoolean(SUPPORT_SELECTED, true)
            }
            super.rebindViewHolder(baseListViewHolder, position, newViewData)
        }
    }

    override fun onBottomMenuHeightChanged(height: Int) {
        recyclerView?.takeIf { height != it.paddingBottom }?.apply {
            setPadding(paddingLeft, paddingTop, paddingRight, height)
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        headerViewDataBinding?.onAppUiStateChanged(config)
        if (config.orientation.isChanged()) {
            networkFloatingViewAsync.getIt {
                it.updateFloatingTipsMargin()
            }
        }
    }

    override fun checkIfNeedShowPrivacyDialog() {
        activity?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            val personPrivacy = it.isPrivacyAuthorized(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN)
            val downloadPrivacy = it.isPrivacyAuthorized(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
            when {
                (personPrivacy == null) || (downloadPrivacy == null) -> return
                !personPrivacy && !downloadPrivacy -> {
                    context?.apply {
                        showAllPrivacyDialog(this)
                    }
                }

                !personPrivacy && downloadPrivacy -> {
                    context?.apply {
                        showPersonPrivacyDialog(this)
                    }
                }

                personPrivacy && !downloadPrivacy -> {
                    context?.apply {
                        showDownloadPrivacyDialog(this)
                    }
                }

                else -> return
            }
        }
    }

    private fun showDownloadPrivacyDialog(context: Context) {
        PermissionDialogHelper.showDownloadPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
                    NetworkPermissionManager.openNetwork(context)
                    refreshNetworkFloatingView()
                }
            })
    }

    private fun showPersonPrivacyDialog(context: Context) {
        PermissionDialogHelper.showPersonPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN)
                }
            })
    }

    private fun showAllPrivacyDialog(context: Context) {
        PermissionDialogHelper.showAllPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickCallbackListListener {
                override fun onPositiveClick(itemList: List<ListItemInfo>?) {
                    itemList?.forEach { items ->
                        when (items.key) {
                            ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN -> {
                                if (items.isChecked) {
                                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN)
                                }
                            }

                            ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD -> {
                                if (items.isChecked) {
                                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
                                    NetworkPermissionManager.openNetwork(context)
                                    refreshNetworkFloatingView()
                                }
                            }
                        }
                    }
                }
            })
    }

    private fun setPrivacy(privacy: String) {
        activity?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            it.authorizePrivacy(privacy)
        }
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    override fun onDestroy() {
        personPetNameEditDialog?.dismiss()
        super.onDestroy()
    }

    private companion object {
        private const val TAG = "PersonPetAlbumSetFragment"

        //合照Item宽高比
        private const val HEADER_ITEM_RATE = 0.7317073170731707
        private const val SELECTED_SIZE_ONE = 1

        /**
         * 跳转到选图集fragment时候设置的requestKey,表明是从创建合照的业务跳转到选图集fragment
         */
        private const val REQUEST_KEY_CREATE_PERSON_PET_GROUP = "createPersonPetGroup.requestKey"
    }
}