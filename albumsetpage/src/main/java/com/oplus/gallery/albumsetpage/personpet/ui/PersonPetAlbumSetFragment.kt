/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonPetAlbumSetFragment.kt
 ** Description: 人物与宠物列表页：人宠群组列表 + 人宠图集列表
 **
 ** Version: 1.0
 ** Date: 2025/04/07
 ** Author: xiaxudong@OppoGallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiaxudong@OppoGallery3D        2025/04/07   1.0          init
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.personpet.ui

import android.app.Activity
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.updateLayoutParams
import androidx.core.view.updateMargins
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.textview.COUITextView
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.personpet.PersonPetAlbumSetAdapter
import com.oplus.gallery.albumsetpage.personpet.PersonPetAlbumSetHeaderViewDataBinding
import com.oplus.gallery.albumsetpage.personpet.PersonPetAlbumSetViewDataBinding
import com.oplus.gallery.albumsetpage.personpet.PersonPetAlbumViewData
import com.oplus.gallery.albumsetpage.personpet.PersonPetGroupViewData
import com.oplus.gallery.albumsetpage.personpet.viewmodel.PersonPetAlbumSetViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.NetworkFloatingViewHelper
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.basebiz.widget.FloatingTipsView
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.helper.PermissionDialogHelper
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.person.utils.GroupHelper
import com.oplus.gallery.business_lib.selectionpage.KEY_REQUEST_KEY
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_CODE
import com.oplus.gallery.business_lib.selectionpage.KEY_RESULT_DATA_LIST
import com.oplus.gallery.business_lib.selectionpage.SelectAlbumInputData
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumSetFragment
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.authorizing.ui.ListItemInfo
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.math.MathUtils.ZERO
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.privacy.IPrivacyAuthorizingAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.baselist.adapter.config.AbsAdapterConfigProvider
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.AsyncObj
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.ui.util.UIConfigUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.oplus.gallery.basebiz.R as BaseBizR

/**
 * 人物与宠物列表页：人宠群组列表 + 人宠图集列表
 */
@RouterNormal(RouterConstants.RouterName.PERSON_PET_ALBUM_SET_FRAGMENT)
class PersonPetAlbumSetFragment : BaseAlbumSetFragment() {
    companion object {
        private const val TAG = "PersonPetAlbumSetFragment"
        private const val SELECTED_SIZE_ONE = 1
        /**
         * 跳转到选图集fragment时候设置的requestKey,表明是从创建合照的业务跳转到选图集fragment
         */
        private const val REQUEST_KEY_CREATE_PERSON_PET_GROUP = "createPersonPetGroup.requestKey"
    }

    private var isFirstLoad = true

    override val needExitWhenEmpty: Boolean = false
    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_PERSON_ALBUM_SET_FRAGMENT

    private val networkFloatingViewAsync = AsyncObj(lifecycleScope) {
        NetworkFloatingViewHelper.initNetWorkViewByType(
            type = NetworkFloatingViewHelper.TipsType.PERSON,
            context = requireContext(),
            onClickCallback = fun(_: NetworkFloatingViewHelper.ClickType) {
                refreshNetworkFloatingView()
            }
        ).apply {
            view?.post {
                activity ?: return@post
                if ((activity?.isDestroyed == true) || (activity?.isFinishing == true)) return@post
                val lp = CoordinatorLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                lp.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                (view as? ViewGroup)?.addView(this, lp)
                updateFloatingTipsMargin()
            }
        }
    }

    private fun FloatingTipsView.updateFloatingTipsMargin() {
        layoutParams?.apply {
            updateLayoutParams<ViewGroup.MarginLayoutParams> {
                val leftOrRightMargin = resources.getDimensionPixelSize(BaseBizR.dimen.main_floating_network_margin_horizontal)
                updateMargins(
                    left = leftOrRightMargin,
                    right = leftOrRightMargin,
                    bottom = if (ScreenUtils.isMiddleAndLargeScreen(context)) {
                        resources.getDimensionPixelSize(BaseBizR.dimen.main_floating_layout_margin_bottom_height)
                    } else {
                        resources.getDimensionPixelSize(com.support.snackbar.R.dimen.coui_snack_bar_margin_bottom)
                    }
                )
            }
        }
    }

    private val headerViewDataBinding by lazy {
        PersonPetAlbumSetHeaderViewDataBinding(this, null,
            this@PersonPetAlbumSetFragment::gotoGroupAlbumSetPage,
            this@PersonPetAlbumSetFragment::startPersonPetSelectionFragment).apply {
            setOnViewChangedListener {
                recyclerAdapter.notifyItemChanged(0)
            }
        }
    }

    private fun gotoGroupAlbumSetPage() {
        startByStack<PersonPetGroupAlbumSetFragment>(
            resId = BaseBizR.id.base_fragment_container,
            fragmentClass = PersonPetGroupAlbumSetFragment::class.java,
            anim = DEFAULT_ANIM_ARRAY,
        )
    }

    private fun startPersonPetSelectionFragment() {
        val activity = requireActivity()
        Starter.DialogFragmentStarter<PanelDialog>(
            fm = activity.supportFragmentManager,
            bundle = SelectAlbumInputData(
                title = resources.getString(BaseBizR.string.choose_people_and_pets),
                fromPath = TextUtil.EMPTY_STRING,
                moveCount = 0,
                modelType = DataRepository.PersonPetModelGetter.TYPE_PERSON_PET_ALBUM_SET,
                selectMulti = true
            ).createBundle().also {
                it.putString(KEY_REQUEST_KEY, REQUEST_KEY_CREATE_PERSON_PET_GROUP)
            },
            postCard = PostCard(RouterConstants.RouterName.SELECTION_ALBUM_PANEL_DIALOG),
        ).start()?.apply {
            setFragmentResultListenerSafety(REQUEST_KEY_CREATE_PERSON_PET_GROUP) { _, bundle ->
                if (bundle.getInt(KEY_RESULT_CODE) != Activity.RESULT_OK) return@setFragmentResultListenerSafety
                val stringPathList = bundle.getStringArray(KEY_RESULT_DATA_LIST) ?: return@setFragmentResultListenerSafety
                lifecycleScope.launch(Dispatchers.IO) {
                    /**
                     * 对选中的图集列表进行分类存储到map中，以图集类型作为key，值为path list,作为创建合照的参数
                     * 比如：选中的图集中可能同时包含人物图集和宠物图集
                     */
                    val paths = stringPathList.map { Path.fromString(it) }
                    (baseListViewModel as? PersonPetAlbumSetViewModel)?.manualCreatePersonPetGroup(paths)
                }
            }
        }
    }

    private var showMoreButton = true
    override fun getBottomBarMenuId(): Int = R.menu.album_selection_all_person_pet_album_set_split_tab
    override fun getEmptyPageIconAnimRes(): Int = BaseBizR.raw.base_empty_view_normal

    override fun getEmptyPageTitleRes(): Int = BaseBizR.string.base_no_photo_or_video_tips
    override fun getEmptyPageSubtitleRes(): Int = BaseBizR.string.people_and_pets_empty_state_desc

    private fun refreshNetworkFloatingView() {
        networkFloatingViewAsync.getIt { networkFloatingView ->
            NetworkFloatingViewHelper.refreshNetworkFloatingView(networkFloatingView, lifecycleScope) {
                shouldShowNetworkFloatingView()
            }
        }
    }

    private suspend fun shouldShowNetworkFloatingView(): Boolean {
        return !isInSelectionMode()
                && NetworkFloatingViewHelper.isFirstClosedPersonAlbumNetworkTip()
                && (!NetworkPermissionManager.isUseOpenNetwork)
                /* 浮窗弹出的逻辑是如果权限通过了才轮到判断联网，人物图集页同意的权限有人脸扫描和资源模型更新，
                但是人脸扫描的权限申请没有申请联网，所以这里只需要判断资源模型下载的权限是否通过 */
                && isPrivacyAuthorizedSuspend(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
    }

    override fun refreshToolbar() {
        bindToolbarToActivity()
        if (!isInSelectionMode()) {
            toolbar?.title = getString(BaseBizR.string.person_pet_group_title)
        }
        toolbar?.isTitleCenterStyle = false
    }

    override fun isShowEmptyPageSubtitle(): Boolean = true

    override fun initLayoutDetail(context: Context): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        parentWidth = getContentWidth()
        edgeWidth = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_horizontal_gap).toInt()
        gapWidth = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_horizontal_gap).toInt()
        val itemGap = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_horizontal_gap)
        val minItemWidth = context.resources.getDimension(BaseBizR.dimen.base_album_set_list_min_item_width)
        val maxItemWidth = context.resources.getDimension(BaseBizR.dimen.base_album_set_list_max_item_width)
        // 默认边距为0，当后续需求需要更改边距时，可修改此处
        val edge = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_horizontal_page_margin)
        val listConfig = UIConfigUtils.getItemMaxConfig(getContentWidth().toFloat(), edge * 2, itemGap, minItemWidth..maxItemWidth)
        spanCount = listConfig.column
    }.build().apply {
        headerCount = 1
        itemDecorationGapPx.top = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_top_gap)
        itemDecorationGapPx.bottom = context.resources.getDimension(BaseBizR.dimen.base_album_set_fragment_item_view_bottom_gap)
    }

    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int) {
        // 点击进入人物/宠物详情页的事件处理，先把空实现移植过来。
        viewData?.let {
            if (isInSelectionMode()) {
                if (it is PersonPetAlbumViewData) {
                    baseListViewModel?.toggleItemSelection(viewData.position)
                }
                return@let
            }
            // 跳转人物/宠物/合照详情页
            gotoDetailFragment(viewData)
        }
    }

    private fun gotoDetailFragment(viewData: AlbumViewData) {
        val currentAlbumId = when (viewData) {
            is PersonPetGroupViewData -> viewData.groupId
            is PersonPetAlbumViewData -> viewData.personPetId
            else -> {
                GLog.d(TAG, "gotoDetailFragment: viewData clz type not support!!!")
                return
            }
        }
        startByStack<PersonPetDetailAlbumFragment>(
            resId = BaseBizR.id.base_fragment_container,
            fragmentClass = PersonPetDetailAlbumFragment::class.java,
            anim = DEFAULT_ANIM_ARRAY,
            data = Bundle().also {
                it.putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
            }
        )
        GLog.d(TAG, "gotoDetailFragment: person pet album set: $currentAlbumId")
    }

    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> =
        ViewModelProvider(this).get(PersonPetAlbumSetViewModel::class.java)

    override fun getAdapterItemConfigs(): List<ItemConfig<AlbumViewData>> {
        return mutableListOf<ItemConfig<AlbumViewData>>().apply {
            context?.let { context ->
                val dataBinding = PersonPetAlbumSetViewDataBinding(context, baseListViewModel, { isMaskVisible() }, { checkboxAnimEnable() })
                add(ItemConfig(PersonPetAlbumViewData::class.java, dataBinding))
            }
        }
    }

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        context?.let {
            val thumbStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_CIRCLE)
                put(StyleData.KEY_THUMB_SIZE_TYPE, getThumbSizeType(layoutDetail.itemWidth))
                put(
                    StyleData.KEY_THUMB_STROKE_WIDTH,
                    resources.getDimension(com.oplus.gallery.foundation.ui.R.dimen.common_round_drawable_frame_stroke_width)
                )
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, resources.getColor(BaseBizR.color.common_round_drawable_frame_stroke_color, null))
                put(
                    StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR,
                    COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPressBackground)
                )
            }
            viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, thumbStyleData)
            val gridThumbStyleData = thumbStyleData.copy().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
                put(StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS, resources.getDimension(BaseBizR.dimen.base_album_item_cover_image_corner_radius))
                put(StyleData.KEY_THUMB_SIZE_TYPE, getThumbSizeType(layoutDetail.itemWidth))
                put(StyleData.KEY_THUMB_LAYOUT_PADDING_BORDER, resources.getDimension(BaseBizR.dimen.common_grid_drawable_child_rect_padding))
                put(StyleData.KEY_THUMB_SIZE_WIDTH, layoutDetail.itemWidth)
                put(StyleData.KEY_THUMB_SIZE_HEIGHT, layoutDetail.itemWidth)
                put(StyleData.KEY_THUMB_LAYOUT_GAP_BETWEEN_CHILD, resources.getDimension(BaseBizR.dimen.common_grid_drawable_child_rect_gap))
                put(
                    StyleData.KEY_THUMB_BACKGROUND,
                    ColorDrawable(COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPressBackground))
                )
                //其他图集封面由四个封面组成，subStyleData为这四个子封面的样式
                put(StyleData.KEY_SUB_STYLE, thumbStyleData.copy().apply {
                    put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_CIRCLE)
                    put(
                        StyleData.KEY_THUMB_BACKGROUND,
                        ColorDrawable(COUIContextUtil.getAttrColor(context, com.oplus.gallery.foundation.ui.R.attr.gColorPressBackground))
                    )
                })
            }
            viewModel.addStyle(StyleType.TYPE_GRID_THUMB_STYLE, gridThumbStyleData)
        }
    }

    override fun getThumbSizeType(itemWidth: Int): Int {
        return ThumbnailSizeUtils.TYPE_FACE_THUMBNAIL
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        baseListViewModel?.setViewData(
            AlbumViewData(
                id = TextUtil.EMPTY_STRING,
                position = 0,
                modelType = DataRepository.PersonPetModelGetter.TYPE_PERSON_PET_ALBUM_SET,
                isMediaAlbum = true,
                version = 0,
                totalCount = 0,
                title = TextUtil.EMPTY_STRING
            )
        )
        needBottomPaddingWhenEdit = false
    }



    override fun onPostRegisterLiveDataObserver() {
        super.onPostRegisterLiveDataObserver()
        (baseListViewModel as? PersonPetAlbumSetViewModel)?.apply {
            createGroupResultLiveData.observe(this@PersonPetAlbumSetFragment) {
                if (isInSelectionMode()) {
                    exitSelectionMode()
                }
                // 此处给ui层根据状态码进行不同的呈现
                when (it) {
                    // 创建合照失败
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_FAIL -> {
                        GLog.e(TAG, LogFlag.DL) { "create group fail." }
                        ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.photo_group_created_fail)
                    }
                    // 创建已存在的合照成功弹Toast
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATED_GROUP_EXIST -> {
                        GLog.d(TAG, LogFlag.DL) { "create exist group success." }
                        ToastUtil.showShortToast(com.oplus.gallery.basebiz.R.string.photo_group_created)
                    }
                    // 需要创建空的合照或创建新合照不弹Toast
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_SUCCESS,
                    GroupHelper.CREATE_PERSON_GROUP_RESULT_CODE_CREATE_EMPTY_GROUP -> { GLog.e(TAG, LogFlag.DL) { "create new group." } }
                }
            }
        }
    }

    override fun dispatchLongClick(position: Int): Boolean {
        return isInSelectionMode()
    }

    override fun onResume() {
        super.onResume()
        refreshNetworkFloatingView()
    }

    override fun onTotalCountChanged(totalCount: Int) {
        super.onTotalCountChanged(totalCount)
        refreshNetworkFloatingView()
    }

    override fun onViewDataChanged(viewDataArray: Array<AlbumViewData?>) {
        if ((recyclerAdapter.headerCount <= ZERO) && recyclerAdapter.totalCount > ZERO) {
            addHeaderView()
        }
        val needShowMoreButton = viewDataArray.isNotEmpty()
        if (showMoreButton != needShowMoreButton) {
            showMoreButton = !showMoreButton
            activity?.invalidateOptionsMenu()
        }
    }

    private fun addHeaderView() {
        if (!isFirstLoad) return
        isFirstLoad = false
        recyclerAdapter.addHeaderView(headerViewDataBinding)
        recyclerAdapter.notifyDataSetChanged()
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (!isResumed) return
        toolbar?.clearMenu()
        if (isInSelectionMode()) {
            toolbar?.isTitleCenterStyle = true
            setDisplayHomeAsUpEnabled(false)
            inflater.inflate(BaseBizR.menu.base_opt_album_cancel, menu)
            menu.findItem(BaseBizR.id.action_cancel)?.let { menuItem ->
                (menuItem.actionView as? COUITextView)?.apply {
                    this.setOnClickListener {
                        if (!ClickUtil.clickable()) return@setOnClickListener
                        onOptionsItemSelected(menuItem)
                    }
                }
            }
        } else {
            setDisplayHomeAsUpEnabled(true)
            toolbar?.isTitleCenterStyle = false
            if ((baseListViewModel?.totalSize ?: 0) <= 0) return
            inflater.inflate(R.menu.main_opt_all_person_pet_set, menu)
            updateToolbarBackMenuItem()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!isResumed) return false
        when (item.itemId) {
            R.id.action_select -> {
                AlbumsActionTrackHelper.trackAndSendAlbumsAction(
                    enterSelect = AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE,
                )
                enterSelectionMode()
            }

            BaseBizR.id.action_cancel -> exitSelectionMode()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)
        if (!isResumed) return
        menu.findItem(R.id.action_select)?.isVisible = !isInSelectionMode() and showMoreButton
    }

    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        super.onBottomMenuBarItemClicked(menuItem)
        val trackCallerEntry = TrackCallerEntry(
            page = trackPage,
            path = baseListViewModel?.trackPath,
            albumsActionCurrentPage = getUserActionCurPage(AlbumsActionTackConstant.TYPE_ALBUMS_ACTION)
        )
        when (menuItem.itemId) {
            // 合并
            R.id.action_merge_person_pet_album -> {
                (baseListViewModel as? PersonPetAlbumSetViewModel)?.getCurrentOperateAlbumSetType()?.let {
                    menuHelper?.doMergePersonPetAlbumAction(
                        albumSetType = it,
                        trackCallerEntry = trackCallerEntry,
                        BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                    )
                }
            }
            // 移除
            R.id.action_remove -> {
                menuHelper?.doRemovePersonPetAlbumAction(
                    trackCallerEntry = trackCallerEntry,
                    BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                )
            }
            // 创建合照
            R.id.action_create_group -> (baseListViewModel as? PersonPetAlbumSetViewModel)?.manualCreatePersonPetGroup()
        }
    }

    override fun onEnterSelection() {
        super.onEnterSelection()
        headerViewDataBinding.onSelectionStateChange(true)
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener { refreshToolbar(true) }
            start()
        }
    }

    override fun onExitSelection() {
        super.onExitSelection()
        headerViewDataBinding.onSelectionStateChange(false)
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener { refreshToolbar(false) }
            start()
        }
        bottomMenuBar?.menu?.let {
            it.findItem(R.id.action_merge_person_pet_album)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
            it.findItem(R.id.action_create_group)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
            it.findItem(R.id.action_remove)?.let { menuItem ->
                if (menuItem.isEnabled) {
                    menuItem.isEnabled = false
                }
            }
        }
        refreshNetworkFloatingView()
    }

    private fun refreshToolbar(isSelectionMode: Boolean) {
        if (isSelectionMode) {
            toolbarFadeAnimator?.updateSelectionTitleLater = false
            updateToolbarEditTitle(
                defaultTitleResId = BaseBizR.string.choose_people_and_pets,
                count = baseListViewModel?.getSelectedItemCount() ?: 0
            )
            refreshNetworkFloatingView()
        } else {
            toolbarFadeAnimator?.updateSelectionTitleLater = true
            toolbar?.title = getString(BaseBizR.string.person_pet_group_title)
        }
        activity?.invalidateOptionsMenu()
    }

    override fun onSelectionStateChange() {
        baseListViewModel?.let { viewModel ->
            val selectedCount = viewModel.getSelectedItemCount()
            val isFromSameMediaSet = (viewModel as? PersonPetAlbumSetViewModel)?.isFromSameAlbumSet()
            bottomMenuBar?.menu?.apply {
                (selectedCount >= SELECTED_SIZE_ONE).let {
                    findItem(R.id.action_remove)?.isEnabled = it
                }
                (selectedCount > SELECTED_SIZE_ONE).let {
                    if (isFromSameMediaSet == true) findItem(R.id.action_merge_person_pet_album)?.isEnabled = it
                    findItem(R.id.action_create_group)?.isEnabled = it
                }
            }
            if (getString(BaseBizR.string.person_pet_group_title) != toolbar?.title) {
                updateToolbarEditTitle(
                    defaultTitleResId = BaseBizR.string.choose_people_and_pets,
                    count = viewModel.getSelectedItemCount()
                )
            }
        }
    }

    override fun getUserActionCurPage(trackType: String?): String? {
        return when (trackType) {
            AlbumsActionTackConstant.TYPE_ALBUMS_ACTION -> AlbumsActionTackConstant.Value.ALBUMS_ACTION_FACE_ALBUM_SET_PAGE_VALUE
            else -> LaunchExitPopupConstant.Value.FACE_ALBUM_SET_PAGE
        }
    }

    override fun getListAdapter(): PersonPetAlbumSetAdapter<AlbumViewData> =
        PersonPetAlbumSetAdapter(
            layoutDetail,
            object : AbsAdapterConfigProvider() {
                override val configs: MutableList<ItemConfig<*>> = mutableListOf<ItemConfig<*>>().apply {
                    addAll(getAdapterItemConfigs())
                }
            }
        )

    override fun onBottomMenuHeightChanged(height: Int) {
        recyclerView?.takeIf { height != it.paddingBottom }?.apply {
            setPadding(paddingLeft, paddingTop, paddingRight, height)
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        if (config.orientation.isChanged()) {
            networkFloatingViewAsync.getIt {
                it.updateFloatingTipsMargin()
            }
        }
    }

    override fun checkIfNeedShowPrivacyDialog() {
        activity?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            val personPrivacy = it.isPrivacyAuthorized(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN)
            val downloadPrivacy = it.isPrivacyAuthorized(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
            when {
                (personPrivacy == null) || (downloadPrivacy == null) -> return
                !personPrivacy && !downloadPrivacy -> {
                    context?.apply {
                        showAllPrivacyDialog(this)
                    }
                }

                !personPrivacy && downloadPrivacy -> {
                    context?.apply {
                        showPersonPrivacyDialog(this)
                    }
                }

                personPrivacy && !downloadPrivacy -> {
                    context?.apply {
                        showDownloadPrivacyDialog(this)
                    }
                }

                else -> return
            }
        }
    }

    private fun showDownloadPrivacyDialog(context: Context) {
        PermissionDialogHelper.showDownloadPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
                    NetworkPermissionManager.openNetwork(context)
                    refreshNetworkFloatingView()
                }
            })
    }

    private fun showPersonPrivacyDialog(context: Context) {
        PermissionDialogHelper.showPersonPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickListener {
                override fun onPositiveClick() {
                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN)
                }
            })
    }

    private fun showAllPrivacyDialog(context: Context) {
        PermissionDialogHelper.showAllPrivacyDialog(
            context,
            object : PermissionDialogHelper.PositiveClickCallbackListListener {
                override fun onPositiveClick(itemList: List<ListItemInfo>?) {
                    itemList?.forEach { items ->
                        when (items.key) {
                            ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN -> {
                                if (items.isChecked) {
                                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_FACE_SCAN)
                                }
                            }

                            ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD -> {
                                if (items.isChecked) {
                                    setPrivacy(ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD)
                                    NetworkPermissionManager.openNetwork(context)
                                    refreshNetworkFloatingView()
                                }
                            }
                        }
                    }
                }
            })
    }

    private fun setPrivacy(privacy: String) {
        activity?.getAppAbility<IPrivacyAuthorizingAbility>()?.use {
            it.authorizePrivacy(privacy)
        }
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()
}