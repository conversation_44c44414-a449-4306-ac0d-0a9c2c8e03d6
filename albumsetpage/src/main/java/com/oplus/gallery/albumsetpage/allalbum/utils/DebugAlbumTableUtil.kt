/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AlbumTableTestUntil
 ** Description: 测试从图集表中获取图集列表相关接口
 ** Version: 1.0
 ** Date: 2025/5/12
 ** Author: <PERSON><PERSON><PERSON><PERSON>@Rom.Apps.Gallery
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2025/5/12     1.0         OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.albumsetpage.allalbum.utils

import android.content.ContentValues
import com.oplus.gallery.business_lib.model.data.cachealbum.set.AlbumEntryListConvert
import com.oplus.gallery.foundation.database.album.buildAlbumContentValues
import com.oplus.gallery.foundation.database.album.buildBucketContentValues
import com.oplus.gallery.foundation.database.album.entry.AlbumEntry
import com.oplus.gallery.foundation.database.album.entry.BucketEntry
import com.oplus.gallery.foundation.database.helper.Constants
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetQueryOperation.QUERY_COUNT
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ALL_MEDIA
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_LOCAL_ALL_MEDIA
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_IMAGE
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_IMAGE_EXCLUDE_GIF
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_JPEG
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_JPEG_AND_HEIF
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_LOCAL_IMAGE
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_LOCAL_VIDEO
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneCondition.SUPPORT_ONLY_REAL_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneGroup.SCENE_MEDIA_TYPE
import com.oplus.gallery.foundation.database.helper.Constants.Album.AlbumSetSceneGroup.SCENE_SHORTCUT_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_ALL_PICTURE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CAMERA_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CARD_CASE_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_CLEANUP_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_MAP_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_RECYCLE_BIN_ALBUM
import com.oplus.gallery.foundation.database.helper.Constants.Album.SpecialAlbumFlag.FLAG_SAFE_BOX_ALBUM
import com.oplus.gallery.foundation.database.store.CacheStore
import com.oplus.gallery.foundation.database.store.CacheStore.AlbumSetSceneQuery.DISTINCT_TRUE
import com.oplus.gallery.foundation.database.util.DatabaseUtils
import com.oplus.gallery.foundation.dbaccess.convert.CountAllConvert
import com.oplus.gallery.foundation.dbaccess.dao.CacheDbDao
import com.oplus.gallery.foundation.dbaccess.dao.IDao
import com.oplus.gallery.foundation.dbaccess.req.BulkInsertReq
import com.oplus.gallery.foundation.dbaccess.req.QueryReq
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils

object DebugAlbumTableUtil {
    private const val TAG = "AlbumTableTestUtil"
    private val PROJECTION = arrayOf(
        CacheStore.AlbumColumns.INDEX_ID,
        CacheStore.AlbumColumns.ALBUM_ID,
        CacheStore.AlbumColumns.ALBUM_PATH,
        CacheStore.AlbumColumns.DISPLAY_NAME,
        CacheStore.AlbumColumns.BUCKET_IDS,
        CacheStore.AlbumColumns.BUCKET_PATHS,
        CacheStore.AlbumColumns.ITEM_SORT_TYPE,
        CacheStore.AlbumColumns.TOTAL_COUNT,
        CacheStore.AlbumColumns.TRASHED_COUNT,
        CacheStore.AlbumColumns.FLAGS,
        CacheStore.AlbumColumns.ORDERED_POSITION,
        CacheStore.AlbumColumns.ORDERED_SUB_POSITION,
        CacheStore.AlbumColumns.DATE_TAKEN,
        CacheStore.AlbumColumns.DATE_MODIFIED,
        CacheStore.AlbumColumns.COVER_INFO,
    )

    @JvmStatic
    private fun queryMyAlbumData(sceneType: Int) {
        runCatching {
            val count = queryMyAlbumCount(sceneType)
            val entryList = QueryReq.Builder<MutableList<AlbumEntry>>().apply {
                setDaoType(IDao.DaoType.CACHE)
                setTableType(CacheDbDao.TableType.ALBUM_SCENE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_GROUP_KEY, Constants.Album.AlbumSetSceneGroup.SCENE_MY_ALBUM.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_CONDITION_KEY, sceneType.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_EXCLUDE_ALBUM_FLAGS_KEY, getExcludeMyAlbumFlagsKey(sceneType))
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_DISTINCT_KEY, DISTINCT_TRUE)
                setProjection(PROJECTION)
                setConvert(AlbumEntryListConvert())
                setLimit("0,$count")
            }.build().exec()
            GLog.d(TAG, DL) { "queryAlbumCount: count:$count->${entryList.size}" }
        }.onFailure {
        }
    }

    @JvmStatic
    private fun queryMyAlbumCount(sceneType: Int): Int {
        runCatching {
            return QueryReq.Builder<Int>().apply {
                setDaoType(IDao.DaoType.CACHE)
                setTableType(CacheDbDao.TableType.ALBUM_SCENE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_GROUP_KEY, Constants.Album.AlbumSetSceneGroup.SCENE_MY_ALBUM.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_CONDITION_KEY, sceneType.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_EXCLUDE_ALBUM_FLAGS_KEY, getExcludeMyAlbumFlagsKey(sceneType))
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_DISTINCT_KEY, DISTINCT_TRUE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_QUERY_OPERATION_KEY, "${Constants.Album.AlbumSetQueryOperation.QUERY_COUNT}")
                setConvert(CountAllConvert())
            }.build().exec()
        }.onFailure {
        }
        return -1
    }

    /**
     * 调试我的图集接口
     */
    @JvmStatic
    fun debugMyAlbumInterface() {
        if (GProperty.DEBUG.not()) {
            return
        }
        GLog.d(TAG, DL) { "query: SUPPORT_ONLY_REAL_ALBUM" }
        queryMyAlbumData(SUPPORT_ONLY_REAL_ALBUM)

        GLog.d(TAG, DL) { "query: SUPPORT_ALL_MEDIA" }
        queryMyAlbumData(SUPPORT_ALL_MEDIA)

        GLog.d(TAG, DL) { "query: SUPPORT_LOCAL_ALL_MEDIA" }
        queryMyAlbumData(SUPPORT_LOCAL_ALL_MEDIA)

        GLog.d(TAG, DL) { "query: MEMORIES SUPPORT_ONLY_JPEG" }
        queryMyAlbumData(SUPPORT_ONLY_JPEG)

        GLog.d(TAG, DL) { "query: MEMORIES SUPPORT_ONLY_JPEG_AND_HEIF" }
        queryMyAlbumData(SUPPORT_ONLY_JPEG_AND_HEIF)

        GLog.d(TAG, DL) { "query: SUPPORT_ONLY_IMAGE" }
        queryMyAlbumData(SUPPORT_ONLY_IMAGE)

        GLog.d(TAG, DL) { "query: SUPPORT_ONLY_LOCAL_IMAGE" }
        queryMyAlbumData(SUPPORT_ONLY_LOCAL_IMAGE)

        GLog.d(TAG, DL) { "query: SUPPORT_ONLY_IMAGE_EXCLUDE_GIF" }
        queryMyAlbumData(SUPPORT_ONLY_IMAGE_EXCLUDE_GIF)

        GLog.d(TAG, DL) { "query: SUPPORT_ONLY_LOCAL_VIDEO" }
        queryMyAlbumData(SUPPORT_ONLY_LOCAL_VIDEO)
    }

    /**
     * 不包含哪些图集
     */
    @JvmStatic
    private fun getExcludeMyAlbumFlagsKey(sceneType: Int): String {
        return (FLAG_MAP_ALBUM or FLAG_ALL_PICTURE_ALBUM or FLAG_CAMERA_ALBUM).toString()
    }


    /**
     * 调试常用图集接口
     */
    @JvmStatic
    fun debugShortcutAlbumInterface() {
        if (GProperty.DEBUG.not()) {
            return
        }
        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: SUPPORT_ALL_MEDIA" }
        queryShortAlbumData(SUPPORT_ALL_MEDIA)

        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: SUPPORT_LOCAL_ALL_MEDIA" }
        queryShortAlbumData(SUPPORT_LOCAL_ALL_MEDIA)

        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: SUPPORT_ONLY_IMAGE" }
        queryShortAlbumData(SUPPORT_ONLY_IMAGE)

        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: SUPPORT_ONLY_IMAGE_EXCLUDE_GIF" }
        queryShortAlbumData(SUPPORT_ONLY_IMAGE_EXCLUDE_GIF)

        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: MEMORIES SUPPORT_ONLY_JPEG" }
        queryShortAlbumData(SUPPORT_ONLY_JPEG)

        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: MEMORIES SUPPORT_ONLY_JPEG_AND_HEIF" }
        queryShortAlbumData(SUPPORT_ONLY_JPEG_AND_HEIF)

//        queryShortAlbumData(SUPPORT_ONLY_REAL_ALBUM)

        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: SUPPORT_ONLY_LOCAL_VIDEO" }
        queryShortAlbumData(SUPPORT_ONLY_LOCAL_VIDEO)

        GLog.d(TAG, DL) { "debugShortcutAlbumInterface query: SUPPORT_ONLY_LOCAL_IMAGE" }
        queryShortAlbumData(SUPPORT_ONLY_LOCAL_IMAGE)
    }

    @JvmStatic
    private fun queryShortAlbumData(sceneType: Int) {
        runCatching {
            val count = queryShortAlbumCount(sceneType)
            val entryList = QueryReq.Builder<MutableList<AlbumEntry>>().apply {
                setDaoType(IDao.DaoType.CACHE)
                setTableType(CacheDbDao.TableType.ALBUM_SCENE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_GROUP_KEY, SCENE_SHORTCUT_ALBUM.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_CONDITION_KEY, sceneType.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_EXCLUDE_ALBUM_FLAGS_KEY, getExcludeShortcutFlagsKey(sceneType))
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_INCLUDE_ALBUM_FLAGS_KEY, getIncludeShortcutFlagsKey(sceneType))
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_DISTINCT_KEY, DISTINCT_TRUE)
                setProjection(PROJECTION)
                setConvert(AlbumEntryListConvert())
                setLimit("0,$count")
            }.build().exec()
            GLog.d(TAG, DL) { "queryShortAlbumData: count:$count->${entryList?.size}" }
        }.onFailure {
            GLog.e(TAG, DL, it) { "queryShortAlbumData: err" }
        }
    }

    @JvmStatic
    private fun queryShortAlbumCount(sceneType: Int): Int {
        runCatching {
            return QueryReq.Builder<Int>().apply {
                setDaoType(IDao.DaoType.CACHE)
                setTableType(CacheDbDao.TableType.ALBUM_SCENE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_GROUP_KEY, SCENE_SHORTCUT_ALBUM.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_CONDITION_KEY, sceneType.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_EXCLUDE_ALBUM_FLAGS_KEY, getExcludeShortcutFlagsKey(sceneType))
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_INCLUDE_ALBUM_FLAGS_KEY, getIncludeShortcutFlagsKey(sceneType))
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_DISTINCT_KEY, DISTINCT_TRUE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_QUERY_OPERATION_KEY, "$QUERY_COUNT")
                setConvert(CountAllConvert())
            }.build().exec()
        }.onFailure {
            GLog.e(TAG, DL, it) { "queryShortAlbumCount: err" }
        }
        return -1
    }

    /**
     * 不包含哪些图集
     */
    @JvmStatic
    private fun getExcludeShortcutFlagsKey(sceneType: Int): String {
        if (sceneType == SUPPORT_ALL_MEDIA) {
            return ""
        }
        // 选图不需要显示地图图集、私密图集、清理建议和回收站
        return (FLAG_MAP_ALBUM or FLAG_SAFE_BOX_ALBUM or FLAG_CLEANUP_ALBUM or FLAG_RECYCLE_BIN_ALBUM).toString()
    }

    @JvmStatic
    private fun getIncludeShortcutFlagsKey(sceneType: Int): String {
        if (sceneType == SUPPORT_ALL_MEDIA) {
            return (FLAG_CARD_CASE_ALBUM or FLAG_MAP_ALBUM or FLAG_SAFE_BOX_ALBUM or FLAG_CLEANUP_ALBUM or FLAG_RECYCLE_BIN_ALBUM).toString()
        }
        // 选图不需要包含固定图集
        return ""
    }

    /**
     * 调试媒体类型图集接口
     */
    @JvmStatic
    fun debugMediaTypeAlbumInterface() {
        if (GProperty.DEBUG.not()) {
            return
        }
        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: SUPPORT_ALL_MEDIA" }
        queryMediaTypeAlbumData(SUPPORT_ALL_MEDIA)

        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: SUPPORT_LOCAL_ALL_MEDIA" }
        queryMediaTypeAlbumData(SUPPORT_LOCAL_ALL_MEDIA)

        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: SUPPORT_ONLY_IMAGE" }
        queryMediaTypeAlbumData(SUPPORT_ONLY_IMAGE)

        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: SUPPORT_ONLY_IMAGE_EXCLUDE_GIF" }
        queryMediaTypeAlbumData(SUPPORT_ONLY_IMAGE_EXCLUDE_GIF)

        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: MEMORIES SUPPORT_ONLY_JPEG" }
        queryMediaTypeAlbumData(SUPPORT_ONLY_JPEG)

        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: MEMORIES SUPPORT_ONLY_JPEG_AND_HEIF" }
        queryMediaTypeAlbumData(SUPPORT_ONLY_JPEG_AND_HEIF)

//        queryShortAlbumData(SUPPORT_ONLY_REAL_ALBUM)

        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: SUPPORT_ONLY_LOCAL_VIDEO" }
        queryMediaTypeAlbumData(SUPPORT_ONLY_LOCAL_VIDEO)

        GLog.d(TAG, DL) { "debugMediaTypeAlbumInterface query: SUPPORT_ONLY_LOCAL_IMAGE" }
        queryMediaTypeAlbumData(SUPPORT_ONLY_LOCAL_IMAGE)
    }

    @JvmStatic
    private fun queryMediaTypeAlbumData(sceneType: Int) {
        runCatching {
            val count = queryMediaTypeAlbumCount(sceneType)
            val entryList = QueryReq.Builder<MutableList<AlbumEntry>>().apply {
                setDaoType(IDao.DaoType.CACHE)
                setTableType(CacheDbDao.TableType.ALBUM_SCENE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_GROUP_KEY, SCENE_MEDIA_TYPE.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_CONDITION_KEY, sceneType.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_DISTINCT_KEY, DISTINCT_TRUE)
                setProjection(PROJECTION)
                setConvert(AlbumEntryListConvert())
                setLimit("0,$count")
            }.build().exec()
            GLog.d(TAG, DL) { "queryMediaTypeAlbumData: count:$count->${entryList?.size}" }
        }.onFailure {
            GLog.e(TAG, DL, it) { "queryShortAlbumData: err" }
        }
    }

    @JvmStatic
    private fun queryMediaTypeAlbumCount(sceneType: Int): Int {
        runCatching {
            return QueryReq.Builder<Int>().apply {
                setDaoType(IDao.DaoType.CACHE)
                setTableType(CacheDbDao.TableType.ALBUM_SCENE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_GROUP_KEY, SCENE_MEDIA_TYPE.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_SCENE_CONDITION_KEY, sceneType.toString())
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_DISTINCT_KEY, DISTINCT_TRUE)
                addUriParameter(CacheStore.AlbumSetSceneQuery.ALBUM_SET_QUERY_OPERATION_KEY, "$QUERY_COUNT")
                setConvert(CountAllConvert())
            }.build().exec()
        }.onFailure {
            GLog.e(TAG, DL, it) { "queryMediaTypeAlbumCount: err" }
        }
        return -1
    }

    /**动态插入数据,用于调试图集*/
    @JvmStatic
    fun insertAlbumsToTable(): Int {
        val albumEntries: List<AlbumEntry> = mutableListOf<AlbumEntry>().apply {
            val bucketPath = "/testZwr"
            AlbumEntry(GProperty.DEBUG_ALBUM_ID, "testZwr", bucketPath, Constants.Album.AlbumTableFlag.FLAG_REAL_ALBUM).apply {
                OplusEnvironment.getAvailableRootPaths().forEach { root ->
                    addBucketId(FilePathUtils.getBucketIdWithParentPath(root + bucketPath))
                }
                if (GProperty.DEBUG_BUCKET_PATH != "[]") {
                    addBucketPath(GProperty.DEBUG_BUCKET_PATH)
                }
                totalCount = 2
                add(this)
            }
        }
        var insertCount = 0
        val values = arrayOfNulls<ContentValues>(albumEntries.size)
        albumEntries.forEachIndexed { index, albumEntry ->
            values[index] = buildAlbumContentValues(albumEntry).apply {
                if (albumEntry.bucketPathSet.isEmpty()) {
                    put(CacheStore.AlbumColumns.BUCKET_PATHS, JsonUtil.toJson(albumEntry.bucketPathSet))
                }
            }
        }
        runCatching {
            BulkInsertReq.Builder()
                .setDaoType(IDao.DaoType.CACHE)
                .setTableType(CacheDbDao.TableType.ALBUM)
                .setConvert { values }
                .build()
                .exec()?.let { insertCount = it }
        }.onFailure {
            GLog.w(TextUtil.EMPTY_STRING, TAG, it, DL) { "insertAlbumsToTable,err" }
        }
        return insertCount
    }

    /**动态插入数据,用于调试图集*/
    @JvmStatic
    fun insertBucket(): Int {
        val bucketPath = "/.AllPicture"
        val bucketEntry = BucketEntry(GProperty.DEBUG_ALBUM_ID, "最近项目", bucketPath).apply {
            count = 1
            imageCount = 1
            imageLocalCount = 1
            videoLocalCount = 1
            gifCount = 1
            jpegCount = 1
            heifCount = 1
        }
        var insertCount = 0
        val values = arrayOfNulls<ContentValues>(1)

        values[0] = buildBucketContentValues(bucketEntry)
        runCatching {
            BulkInsertReq.Builder()
                .setDaoType(IDao.DaoType.CACHE)
                .setTableType(CacheDbDao.TableType.ALBUM_COUNT)
                .addUriParameter(DatabaseUtils.NO_NOTIFY, DatabaseUtils.VALUE_TRUE)
                .setConvert { values }
                .build()
                .exec()?.let { insertCount = it }
        }.onFailure {
            GLog.w(TextUtil.EMPTY_STRING, TAG, it, DL) { "insertBucket,err" }
        }
        return insertCount
    }
}