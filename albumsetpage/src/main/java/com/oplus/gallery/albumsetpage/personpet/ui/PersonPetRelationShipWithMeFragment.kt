/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PersonPetRelationShipWithMeFragment.kt
 ** Description: 人物与宠物详情页更多菜单点击与我的关系后出现的页面
 **
 ** Version: 1.0
 ** Date: 2025/04/18
 ** Author: xiaxudong@OppoGallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiaxudong@OppoGallery3D        2025/04/18   1.0          init
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.personpet.ui

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.chip.COUIChipGroup
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.personpet.viewmodel.PersonPetRelationshipWithMeViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.business_lib.model.data.pet.RelationShipType
import com.oplus.gallery.business_lib.personpet.KEY_RELATIONSHIP_WITH_ME_INFO_DATA
import com.oplus.gallery.foundation.ui.dialog.SingleEditWithCountDialog
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 人物与宠物详情页更多菜单点击与我的关系后出现的页面
 */
@RouterNormal(RouterConstants.RouterName.PERSON_PET_RELATIONSHIP_WITH_ME_FRAGMENT)
class PersonPetRelationShipWithMeFragment : TemplateFragment(), PanelFragment.PanelFragmentCallback {
    private var defaultRelationshipChipGroup: COUIChipGroup? = null
    private var inputRelationshipName: String? = TextUtil.EMPTY_STRING
    private var inputRelationshipType: Int = RelationShipType.NONE.ordinal
    private var customRelationshipSingleEditDialog: SingleEditWithCountDialog? = null
    private var panelFragment: PanelFragment? = null
    private var relationshipViewModel: PersonPetRelationshipWithMeViewModel? = null
    private var isChipGroupIniting = false

    override fun getLayoutId(): Int = R.layout.person_pet_relationship_with_me_layout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        relationshipViewModel = ViewModelProvider(this).get(PersonPetRelationshipWithMeViewModel::class.java)
        relationshipViewModel?.relationshipWithMeInfoData = arguments?.getParcelable(KEY_RELATIONSHIP_WITH_ME_INFO_DATA)
        relationshipViewModel?.relationshipWithMeInfoData?.let { relationshipWithMeInfoData ->
            inputRelationshipName =
                if (relationshipWithMeInfoData.relationshipName == TextUtil.EMPTY_STRING) {
                    resources.getString(R.string.main_tab_album_set_text_none)
                } else {
                    relationshipWithMeInfoData.relationshipName
                }
            inputRelationshipType = relationshipWithMeInfoData.relationShipType
        }
        relationshipViewModel?.currentRelationName = inputRelationshipName
        relationshipViewModel?.currentRelationType = inputRelationshipType
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        initToolbar()
        initRelationshipWithMeChipGroup(view)
        initCustomRelationShipView(view)
    }

    private fun initToolbar() {
        panelFragment?.apply {
            setMenuLeftBtnListener {
                val panelDialog = parentFragment as? PersonPetRelationShipWithMePanelDialog
                panelDialog?.dismiss()
                true
            }
            setMenuRightBtnListener {
                val panelDialog = parentFragment as? PersonPetRelationShipWithMePanelDialog
                relationshipViewModel?.currentRelationName?.let { finalRelationName ->
                    if ((finalRelationName == inputRelationshipName) && (relationshipViewModel?.currentRelationType == inputRelationshipType)) {
                        GLog.d(
                            TAG,
                            LogFlag.DL
                        ) {
                            "setMenuRightBtnListener panelDialog dismiss, finalRelationName=$finalRelationName, " +
                                    "inputRelationshipName=$inputRelationshipName, " +
                                    "currentRelationType=${relationshipViewModel?.currentRelationType}, " +
                                    "inputRelationshipType=$inputRelationshipType"
                        }
                        panelDialog?.dismiss()
                        return@let
                    }
                    relationshipViewModel?.currentRelationType?.let { currentRelationType ->
                        panelDialog?.dealSave(
                            finalRelationName,
                            currentRelationType
                        )
                    }
                } ?: panelDialog?.dealSave(resources.getString(R.string.main_tab_album_set_text_none), INVALID_TYPE)
                panelDialog?.dismiss()
                true
            }
        }
    }

    private fun initRelationshipWithMeChipGroup(view: View) {
        defaultRelationshipChipGroup = view.findViewById(R.id.default_relationShip_chip_group)
        // 将纸片chip添加到ChipGroup中
        resources.getStringArray(R.array.default_relationship_with_me).apply {
            defaultRelationshipChipGroup?.let { chipGroup ->
                addIntoChipGroup(chipGroup)
                // 设置chipgroup为单选模式
                chipGroup.isSelectionRequired = true
                chipGroup.isSingleSelection = true
            }
        }
        // 配置chip的状态,比如设置当前的chip的check状态
        defaultRelationshipChipGroup?.let { chipGroup ->
            (0 until chipGroup.chipCount).forEach { index ->
                val chip = chipGroup.getChipAt(index)
                if ((inputRelationshipType != RelationShipType.CUSTOM.ordinal) && (chip.text.toString() == inputRelationshipName)) {
                    isChipGroupIniting = true
                    chip.isChecked = true
                }

                // 将index和chip的名称对应起来, index需要和RelationShipType匹配
                relationshipViewModel?.defaultRelationshipMappingMaps?.set(chip.text.toString(), index + 1)
            }
        }
        isChipGroupIniting = false
    }

    private fun initCustomRelationShipView(view: View) {
        val contentLayout = view.findViewById<COUICardListSelectedItemLayout>(R.id.content_layout)
        val customTitle = view.findViewById<TextView>(R.id.custom_title)
        contentLayout.setOnClickListener {
            showCustomRelationshipDialog { customName ->
                defaultRelationshipChipGroup?.clearCheck()
                customTitle.text = customName
                relationshipViewModel?.currentRelationName = customName
                relationshipViewModel?.currentRelationType = RelationShipType.CUSTOM.ordinal
            }
        }
        if (inputRelationshipType == RelationShipType.CUSTOM.ordinal)  customTitle.text = inputRelationshipName
    }

    private fun showCustomRelationshipDialog(saveCallback: (String) -> Unit) {
        var content = relationshipViewModel?.customRelationshipViewTitle
        if ((inputRelationshipType == RelationShipType.CUSTOM.ordinal)
            && relationshipViewModel?.customRelationshipViewTitle.equals(TextUtil.EMPTY_STRING)
        ) {
            content = inputRelationshipName.toString()
        }
        customRelationshipSingleEditDialog = SingleEditWithCountDialog.Builder()
            .setContext(requireContext())
            .setTitle(resources.getString(R.string.main_tab_album_set_text_custom))
            .setHintText(resources.getString(BasebizR.string.enter_relationship_with_me))
            .setContent(content)
            .setInputErrorToastResId(BasebizR.string.invalid_relationship_and_re_input)
            .setListener(object : SingleEditWithCountDialog.ConfirmListener {
                override fun onSaved(text: String?) {
                    relationshipViewModel?.customRelationshipViewTitle = text
                    text?.let { saveCallback.invoke(it) }
                    dismissAndReleaseDialog()
                }

                override fun onCancelled() {
                    dismissAndReleaseDialog()
                }
            }).create()
        if (isDestroy().not())  customRelationshipSingleEditDialog?.show()
    }

    private fun isDestroy(): Boolean {
        return activity?.let {
            it.isFinishing || it.isDestroyed
        } ?: false
    }

    private fun dismissAndReleaseDialog() {
        customRelationshipSingleEditDialog?.apply {
            dismiss()
            customRelationshipSingleEditDialog = null
        }
    }

    private fun Array<String>.addIntoChipGroup(group: COUIChipGroup) {
        forEach {
            (layoutInflater.inflate(
                R.layout.peson_pet_relation_ship_with_me_item_chip_selection, group, false
            ) as COUIChip).apply {
                text = it
                setOnCheckedChangeListener { _, isChecked ->
                    if (isChipGroupIniting) return@setOnCheckedChangeListener
                    relationshipViewModel?.currentRelationName = text.toString()
                    relationshipViewModel?.currentRelationType = relationshipViewModel?.defaultRelationshipMappingMaps?.get(it) ?: INVALID_TYPE
                }
                group.addChip(this, false)
            }
        }
    }

    override fun onGetPanelFragment(panelFragment: PanelFragment?) {
        this.panelFragment = panelFragment
    }

    companion object {
        private const val TAG = "PersonPetRelationShipWithMeFragment"
        private const val INVALID_TYPE = -1
    }
}