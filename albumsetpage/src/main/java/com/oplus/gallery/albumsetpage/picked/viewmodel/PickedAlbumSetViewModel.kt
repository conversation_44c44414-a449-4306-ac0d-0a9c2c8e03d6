/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PickedAlbumViewModel
 ** Description: 精选图集列表ViewModel
 ** Version: 1.0
 ** Date: 2025/4/11
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                   <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 *  <EMAIL>        2025/4/11       1.0
 *************************************************************************************************/

package com.oplus.gallery.albumsetpage.picked.viewmodel

import android.app.Application
import android.graphics.drawable.Drawable
import android.os.Bundle
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.albumsetpage.picked.PickedAlbumSetType
import com.oplus.gallery.albumsetpage.picked.PickedBindingData
import com.oplus.gallery.albumsetpage.picked.PickedTaskConfigStrategy
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.task.BaseThumbnailTask
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.personpet.utils.PersonPetDataHelper
import com.oplus.gallery.business_lib.travel.TravelSupportCheckerImpl
import com.oplus.gallery.business_lib.viewmodel.base.BaseViewModel
import com.oplus.gallery.business_lib.viewmodel.loader.FIFOTaskManager
import com.oplus.gallery.business_lib.viewmodel.loader.FIFOThumbDispatcher
import com.oplus.gallery.business_lib.viewmodel.style.IStylePool
import com.oplus.gallery.business_lib.viewmodel.style.StylePool
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PET_CLASSIFY
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.LabelModelGetter.Companion.TYPE_LABEL_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.MemoriesModelGetter.Companion.TYPE_MEMORIES_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.PersonPetModelGetter.Companion.TYPE_PERSON_PET_GROUP_COMPLEX_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.TravelModelGetter.Companion.TYPE_TRAVEL_ALL_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.model.AlbumSetModel
import com.oplus.gallery.framework.abilities.data.model.BaseModel
import com.oplus.gallery.framework.abilities.data.model.ComplexSetModel
import com.oplus.gallery.framework.abilities.data.model.OnContentChangedListener
import com.oplus.gallery.framework.abilities.data.model.PersonPetGroupAlbumSetModel
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.ITaskManageAbility
import com.oplus.gallery.framework.abilities.taskmanage.config.TaskConfigStrategy
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask
import com.oplus.gallery.framework.abilities.taskmanage.task.AbsLoadTask.Companion.CONTENT_CHANGE_ENTRANCE
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.framework.datatmp.R
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.graphics.StyleDrawableMaker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.min
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 精选图集列表ViewModel，用于展示精选图集列表的数据逻辑处理
 * 1、加载精选图集列表数据（人物与宠物、旅程、精彩回忆、智能分类）
 * 2、精选图集列表缩图类型设置
 * 3、精选图集跳转对应列表
 */
class PickedAlbumSetViewModel(application: Application) : BaseViewModel(application), IStylePool by StylePool(), ILoadTaskOwner {

    override val id: String = TaskOwnerConst.PICKED

    override val lifeCycleEvent: Lifecycle.Event
        get() = lifeEvent

    override val linkages: Set<String>? by lazy {
        setOf(
            TaskOwnerConst.MEMORIES_ALBUM_SET,
            TaskOwnerConst.LABEL_ALBUM_SET,
            TaskOwnerConst.TRAVEL_ALBUM_SET,
            TaskOwnerConst.PERSON_PET_GROUP_ALBUM_SET,
            TaskOwnerConst.PERSON_PET_ALBUM_SET,
            TaskOwnerConst.PERSON_ALBUM_SET
        )
    }

    override val affinities: Set<String>? = null
    override val container: String? = null

    override val taskConfigStrategy: TaskConfigStrategy by lazy {
        PickedTaskConfigStrategy()
    }

    // 空图集时的drawable
    private val emptyDrawableMap = mutableMapOf<PickedAlbumSetType, Drawable?>()
    private val thumbTaskManager = FIFOTaskManager<BaseThumbnailTask>()
    private val thumbnailDispatcher by lazy { FIFOThumbDispatcher(context, decodeSession, thumbTaskManager) }

    private val modelListenerMaps = ConcurrentHashMap<BaseModel<MediaSet>, OnContentChangedListener>()
    private val modelMaps = ConcurrentHashMap<String, BaseModel<MediaSet>>()

    private val cachePickedBindingDataList = CopyOnWriteArrayList<PickedBindingData>()
    private val internalPickedBindingLiveData = MutableLiveData<MutableList<PickedBindingData>>()
    val pickedBindingLiveData: LiveData<MutableList<PickedBindingData>> get() = internalPickedBindingLiveData
    private var isContentChange = true

    /**
     * 是否处于活跃状态
     * contentChangedListener回调时如果isActive为true,才加载数据
     */
    private var isActive = true

    init {
        setupDefaultData()
    }

    private fun setupDefaultData() {
        cachePickedBindingDataList.apply {
            if (TravelSupportCheckerImpl.isSupportTravel()) {
                add(PickedBindingData(getAlbumSetTitle(PickedAlbumSetType.TRAVEL), PickedAlbumSetType.TRAVEL))
            }
            add(PickedBindingData(getAlbumSetTitle(PickedAlbumSetType.PERSON_PET), PickedAlbumSetType.PERSON_PET))
            add(PickedBindingData(getAlbumSetTitle(PickedAlbumSetType.MEMORIES), PickedAlbumSetType.MEMORIES))
            add(PickedBindingData(getAlbumSetTitle(PickedAlbumSetType.LABEL), PickedAlbumSetType.LABEL))
        }
        GLog.d(TAG, LogFlag.DL) { "setupDefaultData: size=${cachePickedBindingDataList.size}" }
    }

    fun refreshData(entrance: String) {
//        loadTravelData(entrance)
        loadPersonPetData(entrance)
        loadMemoriesData(entrance)
        loadLabelData(entrance)
    }

    private fun loadTravelData(entrance: String) {
        viewModelScope.launch(Dispatchers.IO) {
            loadAlbumSetModelData(modelType = TYPE_TRAVEL_ALL_ALBUM_SET, albumSetType = PickedAlbumSetType.TRAVEL, entrance)
        }
    }

    private fun loadPersonPetData(entrance: String) {
        viewModelScope.launch(Dispatchers.IO) {
            loadAlbumSetModelData(modelType = TYPE_PERSON_PET_GROUP_COMPLEX_SET, albumSetType = PickedAlbumSetType.PERSON_PET, entrance)
        }
    }

    private fun loadMemoriesData(entrance: String) {
        viewModelScope.launch(Dispatchers.IO) {
            loadAlbumSetModelData(modelType = TYPE_MEMORIES_ALBUM_SET, albumSetType = PickedAlbumSetType.MEMORIES, entrance)
        }
    }

    private fun loadLabelData(entrance: String) {
        viewModelScope.launch(Dispatchers.IO) {
            loadAlbumSetModelData(modelType = TYPE_LABEL_ALBUM_SET, albumSetType = PickedAlbumSetType.LABEL, entrance)
        }
    }

    private fun loadAlbumSetModelData(
        modelType: String,
        albumSetType: PickedAlbumSetType,
        entrance: String,
    ) {
        val bundle = Bundle()
        bundle.putBoolean(DataRepository.KEY_IS_POSITIVE_ORDER, isPositiveOrderFromSetting())
        val model = modelMaps.computeIfAbsent(modelType) {
            getModel(modelType, bundle).apply {
                val listener = OnContentChangedListener { _, _ ->
                    GLog.d(TAG, LogFlag.DL) { "loadAlbumSetModelData: onContentChanged isActive=$isActive modelType=${getModelType()} ${this@apply}" }
                    isContentChange = true
                    if (isActive) {
                        viewModelScope.launch(Dispatchers.IO) {
                            setPositiveOrder(isPositiveOrderFromSetting())
                            loadPickedAlbumSetData(this@apply, modelType, albumSetType, CONTENT_CHANGE_ENTRANCE)
                        }
                    }
                }
                registerContentChangedListener(listener)
                modelListenerMaps[this] = listener
            }
        }
        model.setPositiveOrder(isPositiveOrderFromSetting())
        loadPickedAlbumSetData(model, modelType, albumSetType, entrance)
    }

    private fun getModel(modelType: String, bundle: Bundle): BaseModel<MediaSet> {
        val model = when (modelType) {
            TYPE_PERSON_PET_GROUP_COMPLEX_SET -> {
                ComplexSetModel(
                    modelName = getTag(),
                    modelType = TYPE_PERSON_PET_GROUP_COMPLEX_SET,
                    allowAsynchronousRefresh = false,
                    isPositiveOrder = isPositiveOrderFromSetting(),
                    models = arrayOf(
                        DataRepository.getAlbumSetModel(DataRepository.PersonPetModelGetter.TYPE_PERSON_PET_GROUP_ALBUM_SET, bundle),
                        DataRepository.getAlbumSetModel(
                            DataRepository.PersonPetModelGetter.TYPE_PERSON_PET_ALBUM_SET,
                            Bundle(bundle).apply {
                                putBoolean(DataRepository.KEY_IS_POSITIVE_ORDER, isPositiveOrderFromSetting())
                            }
                        )
                    )
                )
            }

            else -> DataRepository.getAlbumSetModel(modelType, bundle)
        }
        return model
    }

    /**
     * 1.加载图集列表:即mediaSetList
     * 2.根据mediaSetList,构建girdThumbnailTask(等所有封面解码完后才通知ui刷新,减少单个刷新)
     * 3.封面有变化后，才通知ui刷新，避免首次扫描时，生成大量的回忆或者智能或人物图集,不间歇的刷新ui，导致无法点击进入图集列表
     */
    private fun loadPickedAlbumSetData(
        model: BaseModel<MediaSet>,
        modelType: String,
        albumSetType: PickedAlbumSetType,
        entrance: String,
    ) {
        val task = object : AbsLoadTask<Unit>() {
            override val name: String = taskConfigStrategy.getTaskName(modelType)
            override val taskOwner: ILoadTaskOwner = this@PickedAlbumSetViewModel
            override val entrance: String = entrance
            override val extra: String
                get() = modelType

            override fun onRunning(): Unit? {
                GTrace.trace("Picked#$modelType") {
                    model.reload()
                    val count = model.getCount()
                    val reloadCount = min(PICKED_THUMB_COUNT, count)
                    val mediaSetList = loadMediaSet(modelType, model, reloadCount)
                    val oldBindingData = getOldBindingData(albumSetType)
                    val newBindingData = buildBindingDataIfNeed(albumSetType, mediaSetList, reloadCount, oldBindingData)
                    // 首次加载或者需要更新data,则都需要更新UI
                    val needUpdateData = (internalPickedBindingLiveData.value == null) || (oldBindingData != newBindingData)
                    if (needUpdateData) {
                        updateCacheBindData(newBindingData)
                        postViewData()
                    }
                    GLog.d(TAG, LogFlag.DL) {
                        "onRunning.end type=$modelType,count=$count-${mediaSetList?.size},from=$entrance,isUpdate=$needUpdateData,model=$model"
                    }
                }
                isContentChange = false
                return null
            }
        }
        task.priority = taskConfigStrategy.getPriority(this@PickedAlbumSetViewModel, modelType)
        context.getAppAbility<ITaskManageAbility>()?.use {
            it.submitTask(task)
        }
    }

    private fun updateCacheBindData(newBindingData: PickedBindingData) {
        for (index in cachePickedBindingDataList.indices) {
            if (newBindingData.pickedAlbumType == cachePickedBindingDataList[index].pickedAlbumType) {
                cachePickedBindingDataList[index] = newBindingData
                return
            }
        }
    }

    private fun postViewData() {
        internalPickedBindingLiveData.postValue(cachePickedBindingDataList)
    }

    private fun getOldBindingData(albumSetType: PickedAlbumSetType): PickedBindingData? {
        return cachePickedBindingDataList.find { it.pickedAlbumType == albumSetType }
    }

    /**
     * 获取复合model中model列表
     */
    private fun ComplexSetModel.getModelSet(): Array<AlbumSetModel> {
        return modelSet
    }

    private fun loadMediaSet(
        type: String,
        model: BaseModel<MediaSet>,
        reloadCount: Int,
    ): MutableList<MediaSet>? {
        if (reloadCount == 0) {
            GLog.d(TAG, LogFlag.DL) { "[loadMediaSet] maxReloadCount==0 return, type=$type" }
            return null
        }
        val mediaSetList = mutableListOf<MediaSet>()
        when (type) {
            TYPE_PERSON_PET_GROUP_COMPLEX_SET -> {
                val modelSet = (model as ComplexSetModel).getModelSet()
                val itemList = (modelSet[0] as PersonPetGroupAlbumSetModel).getFilterEmptyGroupItems(0, 1)
                if (itemList.isEmpty()) {
                    mediaSetList.addAll(modelSet[1].getItems(0, reloadCount))
                } else {
                    mediaSetList.addAll(itemList)
                    mediaSetList.addAll(modelSet[1].getItems(0, reloadCount - 1))
                }
            }

            else -> mediaSetList.addAll(model.getItems(0, reloadCount))
        }
        return mediaSetList
    }

    private fun buildBindingDataIfNeed(
        albumSetType: PickedAlbumSetType,
        mediaSetList: MutableList<MediaSet>?,
        maxCoverCount: Int,
        oldBindingData: PickedBindingData?
    ): PickedBindingData {
        val coverItems = buildCoverItems(mediaSetList, maxCoverCount)
        if (coverItems.isNullOrEmpty()) {
            return createBindingData(albumSetType, null, null)
        }
        val coverFilePaths = mutableListOf<String>()
        coverItems.forEach { coverFilePaths.add(it.filePath) }
        if (oldBindingData?.needUpdateCover(coverFilePaths) == false) {
            return oldBindingData
        }
        val thumbnailTask = prepareCoverThumbnailTask(albumSetType, coverItems, oldBindingData?.thumbnail?.content)
        return createBindingData(albumSetType, thumbnailTask, coverFilePaths)
    }

    private fun createBindingData(
        albumSetType: PickedAlbumSetType,
        thumbnailTask: BaseThumbnailTask?,
        coverFilePaths: List<String>?
    ): PickedBindingData {
        return PickedBindingData(
            title = getAlbumSetTitle(albumSetType),
            pickedAlbumType = albumSetType,
            thumbnail = thumbnailTask,
            coverFilePaths = coverFilePaths
        )
    }

    private fun prepareCoverThumbnailTask(
        albumSetType: PickedAlbumSetType,
        coverItems: List<MediaItem>,
        oldDrawable: Drawable? = null
    ): BaseThumbnailTask? {
        return thumbnailDispatcher.createGridThumbnailTask(
            mediaItemList = coverItems,
            thumbStyle = getStyleData(albumSetType)
        ).apply {
            fakeDrawable = oldDrawable
            doRetryTask = { thumbTaskManager.addTask(task = it) }
            thumbTaskManager.addTask(task = this)
        }
    }

    fun getEmptyDrawable(albumSetType: PickedAlbumSetType): Drawable? {
        return emptyDrawableMap.computeIfAbsent(albumSetType) {
            StyleDrawableMaker(getStyleData(albumSetType)).generateGridDrawable(drawableList = arrayOfNulls(0))
        }
    }

    private fun getStyleData(albumSetType: PickedAlbumSetType): StyleData {
        val viewStyle = when (albumSetType) {
            PickedAlbumSetType.TRAVEL -> getStyle(StyleType.TYPE_PICKED_TRAVEL_THUMB_STYLE)
            PickedAlbumSetType.PERSON_PET -> getStyle(StyleType.TYPE_PICKED_PERSON_PET_THUMB_STYLE)
            PickedAlbumSetType.MEMORIES -> getStyle(StyleType.TYPE_PICKED_MEMORIES_THUMB_STYLE)
            PickedAlbumSetType.LABEL -> getStyle(StyleType.TYPE_PICKED_LABEL_THUMB_STYLE)
        }
        return viewStyle
    }

    private fun getAlbumSetTitle(albumSetType: PickedAlbumSetType): CharSequence {
        return when (albumSetType) {
            PickedAlbumSetType.TRAVEL -> context.getString(BasebizR.string.travel_group_title)
            PickedAlbumSetType.PERSON_PET -> context.getString(getPersonPetTitleResId())
            PickedAlbumSetType.MEMORIES -> context.getString(BasebizR.string.main_album_group_recommend_memories_title)
            PickedAlbumSetType.LABEL -> context.getString(BasebizR.string.label_group_title)
        }
    }

    /**
     * 根据是否支持宠物分类来获取标题文案
     */
    private fun getPersonPetTitleResId(): Int {
        return if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PET_CLASSIFY)) {
            when {
                PersonPetDataHelper.isOnlyPerson() -> BasebizR.string.model_face_album_set
                PersonPetDataHelper.isOnlyPet() -> R.string.pet
                else -> BasebizR.string.person_pet_group_title
            }
        } else {
            BasebizR.string.model_face_album_set
        }
    }

    private fun buildCoverItems(
        mediaSetList: MutableList<MediaSet>?,
        maxCoverCount: Int
    ): MutableList<MediaItem>? {
        mediaSetList ?: return null
        val coverItems = mutableListOf<MediaItem>()
        mediaSetList.forEach { mediaSet ->
            mediaSet ?: return@forEach
            mediaSet.coverItems?.takeIf { it.isNotEmpty() }?.let { albumCovers ->
                coverItems.add(albumCovers[0])
            }
            val newCoverCount = coverItems.size
            if (newCoverCount == maxCoverCount) {
                return coverItems
            }
            if (coverItems.size > maxCoverCount) {
                return coverItems.subList(0, maxCoverCount)
            }
        }
        return coverItems
    }

    override fun onCreate() {
        GLog.d(TAG, LogFlag.DL) { "[onCreate] call register taskOwner" }
        context.getAppAbility<ITaskManageAbility>()?.use {
            it.register(this)
        }
        super.onCreate()
    }

    override fun onStart() {
        super.onStart()
        GLog.d(TAG, LogFlag.DL) { "[onStart]" }
        isActive = true
    }

    override fun onResume() {
        super.onResume()
        GLog.d(TAG, LogFlag.DL) { "[onResume] isContentChang=$isContentChange" }
        context.getAppAbility<ITaskManageAbility>()?.use {
            it.switchToForeground(this)
            if (isContentChange) {
                isContentChange = false
                refreshData(entrance = "onResume")
            }
        }
    }

    override fun onPause() {
        super.onPause()
        context.getAppAbility<ITaskManageAbility>()?.use {
            it.switchToBackground(this)
        }
    }

    override fun onStop() {
        super.onStop()
        GLog.d(TAG, LogFlag.DL) { "[onStop]" }
        isActive = false
    }

    override fun onCleared() {
        super.onCleared()
        GLog.d(TAG, LogFlag.DL) { "[onCleared]" }
        clearAllStyle()
        releaseData()
        unregisterModelListeners()
    }

    override fun onDestroy() {
        super.onDestroy()
        context.getAppAbility<ITaskManageAbility>()?.use {
            it.unregister(this)
        }
        thumbnailDispatcher.release()
        clearAllStyle()
    }

    private fun releaseData() {
        freeViewDataArrays(cachePickedBindingDataList)
    }

    private fun freeViewDataArrays(bindingDataList: List<PickedBindingData?>) {
        bindingDataList.forEach(::freeViewData)
    }

    private fun freeViewData(viewData: PickedBindingData?) {
        viewData ?: return
        // remove from queue
        viewData.thumbnail?.let { thumbTaskManager.removeTask(task = it) }
        // reset
        viewData.thumbnail?.reset()
        viewData.thumbnail = null
    }

    private fun unregisterModelListeners() {
        modelListenerMaps.forEach {
            it.key.unregisterContentChangedListener(it.value)
        }
    }

    companion object {
        private const val TAG = "PickedAlbumSetViewModel"
        private const val PICKED_THUMB_COUNT = 4
    }
}