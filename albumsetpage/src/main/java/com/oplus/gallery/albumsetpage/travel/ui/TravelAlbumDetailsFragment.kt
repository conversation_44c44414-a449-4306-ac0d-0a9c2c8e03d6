/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TravelAlbumDetailsFragment.kt
 ** Description: 旅行图集详情界面
 ** Version: 1.0
 ** Date: 2025/4/17
 ** Author: ********
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** ********                       2025/4/17    1.0           created
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.travel.ui

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.travel.viewdata.TravelAlbumHeaderViewDataBinding
import com.oplus.gallery.albumsetpage.travel.viewdata.TravelAlbumViewData
import com.oplus.gallery.albumsetpage.travel.viewmodel.TravelAlbumDetailsViewModel
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.sidepane.ISidePaneAnimation
import com.oplus.gallery.basebiz.sidepane.SidePaneDependencyAnimation
import com.oplus.gallery.basebiz.sidepane.isClose
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.travel.utils.TravelAlbumFunctionHelper
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment
import com.oplus.gallery.business_lib.ui.view.ItemViewData
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.ui.dialog.SingleEditDialog
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.onWidthChanged
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.DisplayUtils
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.graphics.StyleData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as BasebizR

/**
 * 旅行图集详情界面
 */
@RouterNormal(RouterConstants.RouterName.TRAVEL_DETAIL_FRAGMENT)
class TravelAlbumDetailsFragment : BaseAlbumFragment() {

    /**
     * 是否支持个性化筛选
     */
    override val isSupportedPersonalFilter: Boolean = true

    override val supportOneTouchShare = true
    private var travelHeaderViewDataBinding: TravelAlbumHeaderViewDataBinding? = null
    private var sidePaneWithHeaderAnim: ISidePaneAnimation? = null

    /**
     * 是否大屏（以屏幕宽度判断）
     */
    private val isLargeScreen: Boolean
        get() = ScreenUtils.isLargeScreenWidth(requireContext())

    /**
     * 是否侧边栏收起
     */
    private val isSidePaneClosed: Boolean
        get() = sidePaneAccessor.isSupportSidePane() && sidePaneAccessor.isClose()

    /**
     * 是否是小横屏:普通手机/折叠屏副屏（合起） 横屏
     */
    private val isLandscapeAndSmallScreen: Boolean
        get() {
            val config = getCurrentAppUiConfig()
            val isLandscape = config.orientation.current == Configuration.ORIENTATION_LANDSCAPE
            val isSmallScreen = config.screenMode.current == AppUiResponder.ScreenMode.SMALL
            return isLandscape && isSmallScreen
        }

    /**
     * header 和侧边栏动画联动
     */
    private val onHeadViewCreated: ((view: View) -> Unit) = { headView ->
        sidePaneWithHeaderAnim = travelHeaderViewDataBinding?.let {
            val headerAnim = recyclerView?.let { recyclerView ->
                SidePaneDependencyAnimation(
                    headView, recyclerView, headView.findViewById(R.id.travel_cover)
                )
            }
            sidePaneAnim?.removeDependencyAnimation(sidePaneWithHeaderAnim)
            sidePaneAnim?.addDependencyAnimation(headerAnim)
            headerAnim
        }
    }

    /**
     * 重命名弹框
     */
    private var renameDialog: SingleEditDialog? = null

    /**
     * 重命名弹框确认监听
     */
    private val renameDialogConfirmListener: SingleEditDialog.ConfirmListener =
        object : SingleEditDialog.ConfirmListener {
            override fun onSaved(text: String?) {
                text ?: run {
                    GLog.w(logTag, LogFlag.DL, "onSaved, text is null")
                    return
                }
                AppScope.launch(Dispatchers.Main) {
                    withContext(Dispatchers.IO) {
                        val travelId = (albumViewData as? TravelAlbumViewData)?.travelId
                        if (travelId.isNullOrBlank().not()) {
                            context?.let { TravelAlbumFunctionHelper.renameTravelAlbumName(it, travelId.orEmpty(), text) }
                        }
                    }
                    toolbarSetter?.setTitle(text)
                    albumViewData?.title = text
                    travelHeaderViewDataBinding?.setHeaderTitle(text)
                }
                renameDialog?.dismiss()
                renameDialog = null
            }

            override fun onCancelled() {
                renameDialog?.dismiss()
                renameDialog = null
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        needScrollToBottom = false
    }

    override fun initLayoutDetail(context: Context): LayoutDetail {
        return GridLayoutDetail.HorizontalGapsBuilder().apply {
            val itemGap = context.resources.getDimension(BasebizR.dimen.base_album_fragment_item_view_horizontal_gap)
            parentWidth = getContentWidth()
            gapWidth = itemGap.toInt()

            // 大屏无侧边7列，其余5列
            spanCount = if (isLargeScreen && isSidePaneClosed) SPAN_COUNT_LARGE_NO_SIDE_PANE else SPAN_COUNT_DEFAULT
        }.build().apply {
            footerCount = AppConstants.Number.NUMBER_0
            headerCount = AppConstants.Number.NUMBER_1
            itemDecorationGapPx.top = context.resources.getDimension(BasebizR.dimen.main_memories_detail_fragment_item_padding_top)
            itemDecorationGapPx.bottom = context.resources.getDimension(BasebizR.dimen.main_memories_detail_fragment_item_padding_bottom)
        }
    }

    /**
     * 注册 LiveData 观察者
     */
    override fun onPostRegisterLiveDataObserver() {
        super.onPostRegisterLiveDataObserver()
        (baseListViewModel as? TravelAlbumDetailsViewModel)?.also { travelDetailsViewModel ->
            travelDetailsViewModel.travelHeaderLiveData.observe(this@TravelAlbumDetailsFragment) { albumViewData ->
                albumViewData?.also albumViewDataLabel@{
                    travelHeaderViewDataBinding?.let {
                        it.albumViewData = albumViewData
                        when {
                            recyclerAdapter.headerCount == 0 -> {
                                updateTravelCoverLayout()
                                recyclerAdapter.addHeaderView(it)
                                recyclerAdapter.notifyItemRangeInserted(0, recyclerAdapter.headerCount)
                            }

                            else -> {
                                travelHeaderViewDataBinding?.setThumbnailLoaded()
                                recyclerAdapter.notifyItemChanged(0)
                            }
                        }
                        return@albumViewDataLabel
                    } ?: let {
                        GLog.d(logTag, LogFlag.DL) { "travelViewDataBinding $albumViewData" }
                        context?.let { contextNotNull ->
                            travelHeaderViewDataBinding = TravelAlbumHeaderViewDataBinding(contextNotNull, albumViewData, onHeadViewCreated,
                                baseListViewModel, lifecycleScope)
                            updateTravelCoverLayout()
                            travelHeaderViewDataBinding?.let { recyclerAdapter.addHeaderView(it) }
                            recyclerAdapter.setDataSet(refresh = true)
                            travelHeaderViewDataBinding?.setThumbnailLoaded()
                        }
                    }
                }
            }
        }
    }

    override fun onCreateViewModel(): ListViewModel<MediaItem, ItemViewData> {
        return ViewModelProvider(this)[TravelAlbumDetailsViewModel::class.java].apply {
            isSupportedPersonalFilter = <EMAIL>
        }
    }

    /**
     * 重命名图集
     */
    override fun renameAlbum(viewData: AlbumViewData?, trackCallerEntry: TrackCallerEntry) {
        renameDialog = activity?.let { activity ->
            SingleEditDialog.Builder()
                .setContext(activity)
                .setTitle(activity.getString(com.oplus.gallery.basebiz.R.string.base_rename_album))
                .setHintText(activity.getString(com.oplus.gallery.basebiz.R.string.base_edit_text_hint))
                .setInputErrorToastResId(com.oplus.gallery.basebiz.R.string.base_album_rename_invalid_string)
                .setListener(renameDialogConfirmListener)
                .setContent(viewData?.title ?: EMPTY_STRING)
                .create()
        }
        renameDialog?.show()
    }

    override fun getTalkBackPosition(): (position: Int) -> Int = { position -> position }

    /**
     * 处理长按事件
     * 因旅行详情有head，所以下方图片列表的position是比没有head时大，那么长按是需减掉headCount得到与数据对应的的position
     */
    override fun dispatchLongClick(position: Int): Boolean {
        return super.dispatchLongClick(position - recyclerAdapter.headerCount)
    }

    /**
     * 封面尺寸规则
     *  直板机高度为 360dp，照片 5 列
     *  直板机横屏高度为 180dp，照片 5 列
     *  中屏：高度为 360dp，侧边栏展开、收起 右侧照片展示 5 列
     *  大屏：高度为 450dp
     *      侧边栏展开，右侧照片展示 5 列
     *      侧边栏收起，右侧照片展示 7 列
     */
    private fun updateTravelCoverLayout() {
        val coverHeight: Float = when {
            isLargeScreen -> resources.getDimension(BasebizR.dimen.basebiz_travel_details_header_height_large)

            isLandscapeAndSmallScreen -> resources.getDimension(BasebizR.dimen.basebiz_travel_details_header_height_landscape_small)

            else -> resources.getDimension(BasebizR.dimen.basebiz_travel_details_header_height)
        }

        travelHeaderViewDataBinding?.apply {
            this.setTravelCoverLayoutParams(
                FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    coverHeight.toInt()
                )
            )
        }
    }

    override fun getUserActionCurPage(trackType: String?): String = LaunchExitPopupConstant.Value.TRAVEL_ALBUM_PAGE

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaItem, ItemViewData>) {
        super.onSetUpViewModelStyle(viewModel)
        context?.let {
            val travelDetailCoverStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_SIZE_TYPE, ThumbnailSizeUtils.TYPE_THUMBNAIL)
                put(
                    StyleData.KEY_THUMB_STROKE_WIDTH,
                    resources.getDimension(com.oplus.gallery.foundation.ui.R.dimen.common_round_drawable_frame_stroke_width)
                )
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, resources.getColor(BasebizR.color.common_round_drawable_frame_stroke_color, null))
                put(
                    StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR, DisplayUtils.getDarkColorWithNightMode(
                        it, resources.getColor(BasebizR.color.standard_default_bg_color_for_transparent, null)
                    )
                )
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
            }
            viewModel.addStyle(StyleType.TYPE_TRAVEL_DETAIL_THUMB_STYLE, travelDetailCoverStyleData)
        }
    }

    override fun getListItemWidth(): Int {
        val headCount = if (travelHeaderViewDataBinding == null) 0 else 1
        return (recyclerView?.layoutManager as? GridLayoutManager)?.getChildAt(headCount)?.width ?: 0
    }

    /**
     * 横竖屏切换的时候刷新封面 header 高度
     */
    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        config.onWidthChanged {
            updateTravelCoverLayout()
        }
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    companion object {

        /**
         * 图集详情列数： 大屏无侧边(侧边收起)7列，其余5列
         */
        private const val SPAN_COUNT_DEFAULT = 5
        private const val SPAN_COUNT_LARGE_NO_SIDE_PANE = 7
    }
}