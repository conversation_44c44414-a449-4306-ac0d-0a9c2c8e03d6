/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description: 我的图集底部菜单功能工具类
 **
 ** Version: 1.0
 ** Date: 2025/5/19
 ** Author: 80407954@OppoGallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80407954@OppoGallery3D  2025/5/19  1.0        我的图集底部菜单功能工具类
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.myalbum

import android.content.Context
import android.content.res.ColorStateList
import android.view.View
import androidx.lifecycle.Lifecycle
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.oplus.gallery.albumsetpage.allalbum.viewmodel.MyAlbumSetViewModel
import com.oplus.gallery.albumsetpage.shortcutalbum.ShortcutAlbumSetViewModel.Companion.ALBUM_MAX_COUNT
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.helper.BottomMenuHelper.Companion.ACTION_STATE_COMPLETED
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MoveAlbumAction
import com.oplus.gallery.business_lib.menuoperation.helper.AlbumHideOnTimeLinePageHelper
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaAlbum
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ENHANCE_TEXT_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.FAVORITE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.MAP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ROOT_ALBUM_ID
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MENU_ITEM_RENAME_ALBUM_VALUE
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.standard_lib.ui.util.ToastUtil

/**
 * 我的图集页面底部菜单的工具类
 */
class MyAlbumBottomMenuHelper(
    private val myAlbumSetViewModel: MyAlbumSetViewModel?,
    private val bottomMenuHelper: BottomMenuHelper?,
    private val myAlbumFragmentContext: Context,
    private val lifecycle: Lifecycle
) {
    /**
     * 当前执行的菜单操作
     */
    private var actionType = DEFAULT_ACTION

    /**
     * 更多菜单的弹窗
     */
    private var popupWindow: COUIPopupListWindow? = null

    /**
     * 删除图集项（添加到回收站）
     */
    fun doSelectionRecycleAction(
        trackCallerEntry: TrackCallerEntry,
        onRemoveActionCallback: ((path: List<Path>?, state: Int) -> Unit)
    ) {
        bottomMenuHelper?.doSelectionRecycleAction(
            actionId = BottomMenuHelper.ACTION_RECYCLE_ALBUM,
            trackCallerEntry = trackCallerEntry,
            selectedAlbumName = myAlbumSetViewModel?.getSelectSingleAlbumName(),
            callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, onRemoveActionCallback)
        )
    }

    /**
     * 重命名
     */
    fun doRenameAlbumAction(trackCallerEntry: TrackCallerEntry) {
        AlbumsActionTrackHelper.trackAndSendMenuOperationClick(itemId = ALBUMS_ACTION_CLICK_MENU_ITEM_RENAME_ALBUM_VALUE)
        myAlbumFragmentContext.let { ctx ->
            bottomMenuHelper?.doRenameAlbumAction(
                context = ctx,
                trackCallerEntry = trackCallerEntry,
                albumPath = myAlbumSetViewModel?.getSelectedItems()?.toList()?.takeIf { it.isNotEmpty() }?.get(0)
            )
        }
    }

    /**
     * 移入侧边栏
     */
    fun doMoveInSidePaneAction(trackCallerEntry: TrackCallerEntry) {
        myAlbumSetViewModel?.apply {
            bottomMenuHelper?.doMoveAlbumAction(
                operationType = MoveAlbumAction.OperationType.MOVE_TO_SIDEPANE,
                selectedItems = getSelectedItems(),
                albumMovable = this,
                trackCallerEntry = trackCallerEntry
            )
        }
    }

    /**
     * 移入私密保险箱
     */
    fun doAlbumSafeBoxAction(
        trackCallerEntry: TrackCallerEntry,
        lifecycle: Lifecycle,
        callbackWrapper: BottomMenuHelper.LifecycleCallbackWrapper? = null
    ) {
        bottomMenuHelper?.doAlbumSafeBoxAction(
            myAlbumFragmentContext,
            lifecycle,
            trackCallerEntry = trackCallerEntry,
            callbackWrapper = callbackWrapper
        )
    }

    /**
     * 弹出更多菜单
     */
    fun showMorePopupWindow(anchorItemView: View?, trackCallerEntry: TrackCallerEntry) {
        if (popupWindow?.isShowing == true) popupWindow?.dismiss()
        myAlbumSetViewModel?.apply {
            if (anchorItemView == null) {
                GLog.e(TAG, LogFlag.DL) { "showMorePopupWindow, moreMenuId is invalid" }
                return
            }
            val shortCutAlbumSetPaths = shortCutAlbumSetInfo.shortCutAlbumSetPaths
            val needMoveAlbumPath = getSelectedItems() - shortCutAlbumSetPaths
            val moreItemList = getMenuItemList(needMoveAlbumPath)
            if (popupWindow == null) {
                popupWindow = COUIPopupListWindow(myAlbumFragmentContext).apply {
                    setDismissTouchOutside(true)
                    setUseBackgroundBlur(false)
                }
            }
            popupWindow?.apply {
                itemList = moreItemList
                setOnItemClickListener { _, _, pos, _ ->
                    // 处理菜单点击逻辑
                    onMoreMenuClick(itemList[pos], trackCallerEntry, needMoveAlbumPath, shortCutAlbumSetPaths)
                    popupWindow?.takeIf { it.isShowing }?.dismiss()
                }
                dismiss()
                show(anchorItemView)
            }
        }
    }

    /**
     * 更多菜单中菜单项的点击事件
     */
    private fun onMoreMenuClick(
        item: PopupListItem,
        trackCallerEntry: TrackCallerEntry,
        needMoveAlbumPath: Set<Path>,
        shortCutAlbumSetPaths: Set<Path>
    ) {
        if (item.title.isEmpty() || item.isEnable.not()) return
        if (item.title == myAlbumFragmentContext.resources?.getString(R.string.add_to_short_cut_album_set))  {
            if (item.isChecked) {
                doMoveOutShortCutAction(trackCallerEntry)
            } else if (couldMoveInShortCut(needMoveAlbumPath.size, shortCutAlbumSetPaths.size)) {
                doMoveInShortCutAction(trackCallerEntry, needMoveAlbumPath)
            }
        } else if (item.title == myAlbumFragmentContext.resources?.getString(R.string.base_album_hide_on_time_line_page)) {
            myAlbumSetViewModel?.apply {
                if (item.isChecked) {
                    doShowOnTimelinePageAction(trackCallerEntry, getNeedDoHideOrShowActionAlbum(getSelectedItems()).second)
                } else {
                    doHideOnTimelinePageAction(trackCallerEntry, getNeedDoHideOrShowActionAlbum(getSelectedItems()).second)
                }
            }
        } else {
            GLog.e(TAG, LogFlag.DL) { "onMoreMenuClick, item title is invalid" }
        }
    }

    /**
     * 获取更多菜单的菜单项列表
     */
    private fun getMenuItemList(needMoveAlbumPath: Set<Path>): ArrayList<PopupListItem> {
        val moreItemList = ArrayList<PopupListItem>()
        myAlbumSetViewModel?.apply {
            val needDoHideOrShowActionAlbumPair = getNeedDoHideOrShowActionAlbum(getSelectedItems())
            val addToShortCutMenuItem = getMenuItem(
                context.resources.getString(R.string.add_to_short_cut_album_set),
                isAllAlbumAddedToShortCut(needMoveAlbumPath)
            )
            val doHideOrShowMenuItem = getMenuItem(
                context.resources.getString(R.string.base_album_hide_on_time_line_page),
                needDoHideOrShowActionAlbumPair.first,
                listOf(FAVORITE_ALBUM_ID, ENHANCE_TEXT_ALBUM_ID, MAP_ALBUM_ID, ROOT_ALBUM_ID).none { id ->
                    id in needDoHideOrShowActionAlbumPair.second
                }
            )
            moreItemList.apply {
                add(addToShortCutMenuItem)
                add(doHideOrShowMenuItem)
            }
        }
        return moreItemList
    }

    /**
     * 固定到常用图集
     * @param needMoveAlbumPath 需要添加到常用图集的图集
     */
    private fun doMoveInShortCutAction(trackCallerEntry: TrackCallerEntry, needMoveAlbumPath: Set<Path>) {
        actionType = MY_ALBUM_MOVE_IN_SHORT_CUT
        myAlbumSetViewModel?.apply {
            bottomMenuHelper?.doMoveAlbumAction(
                operationType = MoveAlbumAction.OperationType.MOVE_IN_SHORTCUT_ALBUM_SET,
                selectedItems = needMoveAlbumPath,
                albumMovable = this,
                trackCallerEntry = trackCallerEntry,
                callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onActionCompletedCallback)
            )
        }
    }

    /**
     * 移出常用图集
     */
    private fun doMoveOutShortCutAction(trackCallerEntry: TrackCallerEntry) {
        actionType = MY_ALBUM_MOVE_OUT_SHORT_CUT
        myAlbumSetViewModel?.apply {
            bottomMenuHelper?.doMoveAlbumAction(
                operationType = MoveAlbumAction.OperationType.MOVE_OUT_SHORTCUT_ALBUMSET,
                selectedItems = getSelectedItems(),
                albumMovable = this,
                trackCallerEntry = trackCallerEntry,
                callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onActionCompletedCallback)
            )
        }
    }

    /**
     * 选中图集项是否可以添加到常用图集
     * 需要添加的图集已经在常用图集中时，提示用户，不执行二次添加
     * 需要添加的图集数量大于剩余常用图集数量时，提示用户并放弃添加
     * @param needMovePathSize 需要添加的图集数量
     * @param shortCutAlbumPathSize 常用图集数量
     * @return 是否应该执行添加操作
     */
    private fun couldMoveInShortCut(needMovePathSize: Int, shortCutAlbumPathSize: Int): Boolean {
        if ((needMovePathSize + shortCutAlbumPathSize) > ALBUM_MAX_COUNT) {
            ToastUtil.showShortToast(
                myAlbumFragmentContext.resources?.getString(R.string.short_cut_album_set_max_count_desc)
            )
            return false
        }
        return true
    }

    /**
     * 在照片页隐藏图集
     * @param albumIdList 需要隐藏在照片页的图集id列表
     */
    private fun doHideOnTimelinePageAction(trackCallerEntry: TrackCallerEntry, albumIdList: List<Int>) {
        actionType = MY_ALBUM_HIDE_ON_TIME_LINE_PAGE
        myAlbumSetViewModel?.apply {
            bottomMenuHelper?.doHideOnTimelinePageAction(
                actionType = MenuAction.ALBUM_HIDE_ON_TIME_LINE_PAGE,
                albumIdList = albumIdList,
                trackCallerEntry = trackCallerEntry,
                actionCallback = ::onActionCompletedCallback
            )
        }
    }

    /**
     * 在照片页显示图集
     * @param albumIdList 需要显示在照片页的图集id列表
     */
    private fun doShowOnTimelinePageAction(trackCallerEntry: TrackCallerEntry,  albumIdList: List<Int>) {
        actionType = MY_ALBUM_SHOW_ON_TIME_LINE_PAGE
        myAlbumSetViewModel?.apply {
            bottomMenuHelper?.doShowOnTimelinePageAction(
                actionType = MenuAction.ALBUM_SHOW_ON_TIME_LINE_PAGE,
                albumIdList = albumIdList,
                trackCallerEntry = trackCallerEntry,
                actionCallback = ::onActionCompletedCallback
            )
        }
    }

    /**
     * 选中图集是否已经全部不在照片页中显示
     * @param pathSet 选中图集列表
     * @return first: 选中图集是否全部不在照片页中显示 second: 选中项图集的albumId
     */
    private fun getNeedDoHideOrShowActionAlbum(pathSet: Set<Path>): Pair<Boolean, List<Int>> {
        //isAlbumHide=true 表示选中图集已经全部在照片页的图集表中有数据，说明当前图集没有在照片页中，菜单不显示在照片页为选中状态
        var isAllAlbumHide = true
        val needHideAlbumIds = mutableListOf<Int>()
        val needShowAlbumIds = mutableListOf<Int>()
        pathSet.forEach { path ->
            (((path.`object`) as? MediaAlbum)?.albumId)?.let { albumId ->
                if (AlbumHideOnTimeLinePageHelper.findAlbumHideOnTimeLinePageItem(albumId)) {
                    needShowAlbumIds.add(albumId)
                } else {
                    isAllAlbumHide = false
                    needHideAlbumIds.add(albumId)
                }
            }
        }
        //如果选中图集已经全部不显示在照片页，进行反选-显示在照片页；如果有部分图集显示在照片页，将这些图集执行不显示的操作
        return Pair(
            isAllAlbumHide,
            if (isAllAlbumHide) needShowAlbumIds else  needHideAlbumIds
        )
    }

    /**
     * 移出/移入常用图集，显示/不显示在照片页完成回调。弹窗告知用户添加成功
     */
    private fun onActionCompletedCallback(path: List<Path>?, state: Int) {
        if (state == ACTION_STATE_COMPLETED) {
            runOnUiThread {
                when (actionType) {
                    MY_ALBUM_HIDE_ON_TIME_LINE_PAGE -> {
                        ToastUtil.showShortToast(
                            myAlbumFragmentContext.resources?.getString(R.string.base_album_hide_on_time_line_page_success))
                    }
                    MY_ALBUM_SHOW_ON_TIME_LINE_PAGE -> {
                        ToastUtil.showShortToast(
                            myAlbumFragmentContext.resources?.getString(R.string.base_album_hide_on_time_line_page_cancel))
                    }
                    MY_ALBUM_MOVE_IN_SHORT_CUT -> {
                        ToastUtil.showShortToast(
                            myAlbumFragmentContext.resources?.getString(R.string.already_add_to_short_cut_album_set))
                    }
                    MY_ALBUM_MOVE_OUT_SHORT_CUT -> {
                        ToastUtil.showShortToast(
                            myAlbumFragmentContext.resources?.getString(R.string.already_remove_from_short_cut_album_set))
                    }
                    else -> GLog.w(TAG, LogFlag.DL) { "onActionCompletedCallback actionType is $actionType" }
                }
            }
        }
    }

    /**
     * 选中图集是否已经全部固定到常用图集
     * @param needMoveAlbumPath 需要添加的图集列表
     */
    private fun isAllAlbumAddedToShortCut(needMoveAlbumPath: Set<Path>): Boolean = (needMoveAlbumPath.isEmpty())

    /**
     * 获取更多菜单的菜单项
     * @param title 菜单标题
     * @param isChecked 是否选中(选中时，菜单颜色会变化并显示勾选）
     * @return 弹出菜单的菜单项
     */
    private fun getMenuItem(title: String, isChecked: Boolean = false, isEnable: Boolean = true): PopupListItem {
        val builder = PopupListItem.Builder()
        val item = builder.reset().setTitle(title)
            .setIsEnable(isEnable)
            .setSubMenuItemList(null)
            .setIsChecked(isChecked)
            .build()
        if (item.isEnable) {
            item.titleColorList = myAlbumFragmentContext.getColor(
                if (isChecked) {
                    R.color.shortcut_album_menu_text_selected_color
                } else {
                    R.color.shortcut_album_menu_text_default_color
                }
            ).let { ColorStateList.valueOf(it) }
        }
        return item
    }

    private companion object {
        private const val TAG = "MyAlbumBottomMenuHelper"
        private const val DEFAULT_ACTION = 0
        private const val MY_ALBUM_MOVE_IN_SHORT_CUT = 1
        private const val MY_ALBUM_MOVE_OUT_SHORT_CUT = 2
        private const val MY_ALBUM_HIDE_ON_TIME_LINE_PAGE = 3
        private const val MY_ALBUM_SHOW_ON_TIME_LINE_PAGE = 4
    }
}