/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ShortcutUtils.kt
 ** Description : 工具类
 ** Version     : 1.0
 ** Date        : 2025/05/10 15:27
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>           <desc>
 ** ------------------------------------------------------------------------------
 ** zhang<PERSON><PERSON>@oppo.com     2025/05/10      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.albumsetpage.base.util

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.task.BaseThumbnailTask
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.AlbumViewData.Companion.SUPPORT_IS_SELF_ALBUM
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_CARD
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_CARD_OF_MY_ALBUM_SET
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.business_lib.model.data.base.source.SourceConstants.Local.PATH_ALBUM_CARD_CASE_ANY
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment
import com.oplus.gallery.business_lib.viewmodel.loader.AlbumThumbDispatcher
import com.oplus.gallery.foundation.database.helper.Constants
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_CARD_CASE_ALBUM
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.graphics.StyleData

val albumInfoMap = hashMapOf<Int, Pair<Int, Int>>().apply {
    // 最近项目
    this[Constants.Album.VirtualAlbum.ALL_PICTURE_ALBUM_ID] = Pair(
        INVALID_INT,
        R.string.model_title_recent_items
    )
    // 相机
    this[Constants.Album.VirtualAlbum.CAMERA_ALBUM_ID] = Pair(
        INVALID_INT,
        R.string.main_title_camera
    )
    // 视频
    this[Constants.Album.VirtualAlbum.ALL_VIDEO_ALBUM_ID] = Pair(
        INVALID_INT,
        R.string.model_title_mediatype_video
    )
    // 截屏录屏
    this[Constants.Album.VirtualAlbum.SCREEN_SHOT_ALBUM_ID] = Pair(
        INVALID_INT,
        R.string.main_title_screen_shots
    )
    // 个人收藏
    this[Constants.Album.VirtualAlbum.FAVORITE_ALBUM_ID] = Pair(
        INVALID_INT,
        R.string.main_title_favorite_album
    )
    // 实况
    this[Constants.Album.VirtualAlbum.OLIVE_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_olive,
        R.string.model_title_olive
    )
    // 地图
    this[Constants.Album.VirtualAlbum.MAP_ALBUM_ID] = Pair(
        INVALID_INT,
        R.string.main_map
    )
    // 卡证档案
    this[Constants.Album.VirtualAlbum.CARD_ID_ALBUM_ID] = Pair(
        INVALID_INT,
        R.string.main_title_card_case
    )
    // 杜比视界
    this[Constants.Album.VirtualAlbum.DOLBY_ALBUM_ID] = Pair(
        R.drawable.main_ic_dolby_vision,
        R.string.main_title_dolby_vision_album
    )
    // 人像
    this[Constants.Album.VirtualAlbum.PORTRAIT_BLUR_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_portrait_blur,
        R.string.main_title_mediatype_portrait_blur
    )
    // GIF
    this[Constants.Album.VirtualAlbum.GIF_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_gif,
        R.string.main_title_mediatype_for_gif
    )
    // 连拍
    this[Constants.Album.VirtualAlbum.CSHOT_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_burst,
        R.string.main_title_mediatype_cshot
    )
    // RAW
    this[Constants.Album.VirtualAlbum.RAW_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_raw,
        R.string.main_title_mediatype_raw
    )
    // 全景
    this[Constants.Album.VirtualAlbum.PANORAMA_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_panorama,
        R.string.main_title_mediatype_panorama
    )
    // 延时拍摄
    this[Constants.Album.VirtualAlbum.FAST_VIDEO_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_timelapse,
        R.string.main_title_mediatype_fastvideo
    )
    // 慢动作
    this[Constants.Album.VirtualAlbum.SLOW_MOTION_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_slow_motion,
        R.string.main_title_mediatype_slow_motion
    )

    // LOG
    this[Constants.Album.VirtualAlbum.LOG_VIDEO_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_log_video,
        R.string.main_title_mediatype_log_video
    )

    // 私密图集
    this[Constants.Album.VirtualAlbum.SAFE_BOX_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_encrypt,
        R.string.main_title_safe_box
    )
    // 清理建议
    this[Constants.Album.VirtualAlbum.CLEAN_UP_ALBUM_ID] = Pair(
        R.drawable.main_ic_photo_clean_dark,
        R.string.main_title_photo_clean
    )
    // 最近删除
    this[Constants.Album.VirtualAlbum.RECYCLE_BIN_ALBUM_ID] = Pair(
        R.drawable.main_ic_list_delete,
        R.string.main_title_recycle
    )
}

const val INVALID_INT = -1

/**
 * 根据albumId获取虚拟图集的资源信息
 * */
fun findResByAlbumId(albumId: Int): Pair<Int, Int> {
    return albumInfoMap[albumId] ?: Pair(INVALID_INT, INVALID_INT)
}

/**
 * 根据 Drawable 资源创建封面任务
 * */
fun createDrawableThumTask(
    context: Context,
    resId: Int,
    thumbnailDispatcher: AlbumThumbDispatcher,
    thumbStyle: StyleData
): BaseThumbnailTask? {
    val drawable = AppCompatResources.getDrawable(context, resId) ?: return null
    return thumbnailDispatcher.createDrawableThumbnailTask(drawable, thumbStyle)
}

/**
 * 跳转随身卡包页面
 * */
fun enterCardCaseAlbum(fragment: BaseFragment?, itemView: View?, radius: Float, toolBarBottomY: Float = 0f) {
    fragment ?: return
    val title = fragment.context?.resources?.getString(R.string.main_title_card_case)
    val floatingWindowOffset = IntArray(2)
    if (fragment.isFloatingWindowMode()) fragment.view?.rootView?.getLocationOnScreen(floatingWindowOffset)
    fragment.startByStack<BaseAlbumFragment>(
        postCard = PostCard(RouterConstants.RouterName.CARD_CASE_FRAGMENT),
        anim = SEAMLESS_ANIM_ARRAY,
        data = Bundle().apply {
            putParcelable(
                KEY_ALBUM_VIEW_DATA,
                AlbumViewData(
                    PATH_ALBUM_CARD_CASE_ANY.toString(),
                    0,
                    TYPE_CARD_CASE_ALBUM,
                    true,
                    0,
                    0,
                    title,
                    supportedAbilities = Bundle().apply {
                        putBoolean(SUPPORT_IS_SELF_ALBUM, true)
                    }
                )
            )
            itemView?.let {
                val entrance = if (it.width < it.height) ENTRANCE_CARD else ENTRANCE_CARD_OF_MY_ALBUM_SET
                FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                    fragment.context,
                    this,
                    it,
                    TriggerViewRectGetter(it, floatingWindowOffset, toolBarBottomY),
                    entrance,
                    radius
                )
            }
        }
    )
}

internal fun getShortcutRVSpanCount(context: Context, shortcutLayoutWidth: Int): Int {
    var countInter = context.resources.getInteger(R.integer.base_album_set_list_column_small_screen)
    context.let { contextIt ->
        when {
            COUIResponsiveUtils.isSmallScreen(contextIt, shortcutLayoutWidth) -> {
                countInter = context.resources.getInteger(R.integer.base_album_set_list_column_small_screen)
            }

            COUIResponsiveUtils.isMediumScreen(contextIt, shortcutLayoutWidth) -> {
                countInter = context.resources.getInteger(R.integer.base_album_set_list_column_medium_screen)
            }

            COUIResponsiveUtils.isLargeScreen(contextIt, shortcutLayoutWidth) -> {
                countInter = context.resources.getInteger(R.integer.base_album_set_list_column_large_screen)
            }
        }
    }
    return countInter
}