/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description: 我的图集 列表
 **
 ** Version: 1.0
 ** Date: 2025/3/19
 ** Author: 80407954@OppoGallery3D
 ** TAG: 我的图集 列表
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80407954@OppoGallery3D  2025/3/19  1.0        我的图集-我的 tab
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.allalbum.ui

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.annotation.ColorInt
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox.SELECT_ALL
import com.coui.appcompat.checkbox.COUICheckBox.SELECT_NONE
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.material.bottomnavigation.BottomNavigationItemView
import com.coui.appcompat.theme.COUIThemeUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.allalbum.viewmodel.MyAlbumSetViewModel
import com.oplus.gallery.albumsetpage.base.util.enterCardCaseAlbum
import com.oplus.gallery.business_lib.helper.MapPageJumper
import com.oplus.gallery.albumsetpage.myalbum.MyAlbumBottomMenuHelper
import com.oplus.gallery.basebiz.app.OnFragmentVisibilityChange
import com.oplus.gallery.basebiz.constants.IntentConstant.TrackConstant.ALBUM_NAVIGATION_TRACK_PAGE_ID
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.sidepane.SidePaneAnimationConfig
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.SidePaneWithAlbumAnimation
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isFlat
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.sidepane.isVisible
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper.trackAndSendAlbumsAction
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.AlbumViewData.Companion.SUPPORT_IS_SELF_ALBUM
import com.oplus.gallery.basebiz.uikit.AlbumViewData.Companion.SUPPORT_IS_THIRD_APP_ALBUM
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.IMyAlbumToolbarCustomize
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_MY_ALBUM_SET_SECOND
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.basebiz.util.AlbumCardListUtils
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.business_lib.cardcase.utils.CardCaseUtils
import com.oplus.gallery.business_lib.drag.CardItemDragHelper
import com.oplus.gallery.business_lib.drag.DraggableListAdapter
import com.oplus.gallery.business_lib.helper.BottomMenuHelper
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumSetFragment
import com.oplus.gallery.business_lib.ui.view.EditableCardAlbumSetViewDataBinding
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.viewmodel.AlbumSetViewModel.Companion.SELECTED_SIZE_ONE
import com.oplus.gallery.business_lib.viewmodel.base.ListViewModel
import com.oplus.gallery.business_lib.viewmodel.style.StyleType
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.database.helper.Constants
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.FAVORITE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.MAP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SCREEN_SHOT_ALBUM_ID
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.TYPE_ALBUMS_ACTION
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_NOT_COMMONLY_USE_ALBUM_PAGE_VALUE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.NOT_COMMONLY_USED_ALBUM_SET_PAGE
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ALBUM_SAFE_BOX
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_MY_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.sortablemodel.bean.DragData
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_PAGE_UNKNOWN
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_SIDE_PANE_PAGE
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.KEY_FROM_PAGE
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.graphics.StyleData
import com.oplus.gallery.standard_lib.ui.toolbar.actionview.MenuButton
import com.oplus.gallery.standard_lib.ui.toolbar.actionview.MenuCheckButton
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as BasebizR
import com.oplus.gallery.foundation.ui.R as foundationR
import com.support.appcompat.R as supportR

/**
 * "我的图集"页面的“我的”tab页
 */
@RouterNormal(RouterConstants.RouterName.MY_ALBUM_SET_FRAGMENT)
class MyAlbumSetFragment : BaseAlbumSetFragment(), IMyAlbumToolbarCustomize, ViewPagerPageChangeCallBack, OnFragmentVisibilityChange {

    override val supportDragItem = true

    private var myAlbumSetViewModel: MyAlbumSetViewModel? = null

    /**
     * 全选按钮
     */
    private var selectAllCheckButton: MenuCheckButton? = null

    /**
     * 侧边栏展开收起动画联动基础列表动画管理
     */
    private var sidePaneAnim: SidePaneWithAlbumAnimation? = null

    /**
     * 是否需要toolbar的刷新动画
     */
    private var isToolbarAnimationNeeded = true

    /**
     * 取消按钮
     */
    private var cancelButton: MenuButton? = null

    /**
     * 和share_tab共用的Tab页面的分割线
     */
    private var divider: View? = null

    /**
     * 记录从哪个页面跳转至这个页面：来源
     */
    private var fromPage: Int = FROM_PAGE_UNKNOWN

    private var myAlbumBottomMenuHelper: MyAlbumBottomMenuHelper? = null

    override fun getBottomBarMenuId(): Int = R.menu.my_album_set_bottom_menu

    override fun getLayoutId(): Int = R.layout.my_album_set_layout

    override fun getEmptyPageTitleRes(): Int = BasebizR.string.base_no_album_tips

    override fun onCreateViewModel(): ListViewModel<MediaSet, AlbumViewData> {
        return ViewModelProvider(this)[MyAlbumSetViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initArguments()
    }

    /**
     * 获得初始化Fragment的参数
     */
    private fun initArguments() {
        arguments?.apply { fromPage = getInt(KEY_FROM_PAGE, FROM_PAGE_UNKNOWN) }
    }

    override fun refreshToolbar() {
        /*
        bugId: 9397218
        之前的修改中重写该方法时，去掉了rebindActivity方法,造成了进入图集详情后，MainActivity绑定了下一级页面的Toolbar，
        回到我的图集页后没有重新绑定，切换tab、变换选择态时toolbar刷新失效。
        又因为父类这个方法调用rebindActivity会刷新返回箭头，造成侧边栏进入我的图集页面场景下切换tab时箭头闪烁，所以这里只需要重新绑定toolbar就行
         */
        if (showInternalToolbar) {
            toolbarSetter?.setSelectionMode(isInSelectionMode())
            (activity as? AppCompatActivity)?.setSupportActionBar(toolbar)
        }
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        myAlbumSetViewModel = baseListViewModel as? MyAlbumSetViewModel
        doSetViewData()
        canShowSelectionMode = false
        needBottomPaddingWhenEdit = true
        val owner = this@MyAlbumSetFragment
        myAlbumSetViewModel?.apply {
            isClearDraggingPositionMap.observe(owner) { clear ->
                GLog.d(TAG, DL) { "afterViewCreated clearDraggingPositionMap=$clear" }
                if (clear) { (recyclerAdapter as? DraggableListAdapter)?.clearDraggingPositionMap() }
            }
            extraFixHeadListCount.observe(owner) { count ->
                setExtraFixHeadListCount(count)
            }
            <EMAIL>?.let { fragmentContext ->
                myAlbumBottomMenuHelper = MyAlbumBottomMenuHelper(myAlbumSetViewModel, menuHelper, fragmentContext, lifecycle)
            }
        }
        sidePaneAccessor?.takeIf { isSidePaneEnabled() }?.let {
            sidePaneAnim = recyclerView?.let { rv ->
                SidePaneWithAlbumAnimation(this, rv)
            }
        }
        recyclerView?.let { rv ->
            (recyclerAdapter as? DraggableListAdapter)?.let { draggable ->
                itemDragHelper = CardItemDragHelper(
                    context?.resources?.getDimensionPixelOffset(BasebizR.dimen.base_album_item_sub_cover_image_corner_radius) ?: 0,
                    draggable
                ).apply { attach(rv) }
            }
            (rv as? COUIRecyclerView)?.apply {
                setItemClickableWhileSlowScrolling(false)
                setItemClickableWhileOverScrolling(false)
            }
        }
        addRVScrollListener()
    }

    private fun addRVScrollListener() {
       recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                // 内容向上滚动时，展示分割线
                updateDividerVisibility()
            }
        })
    }

    /**
     * 是否需要返回箭头
     */
    private fun isNeedHomeAsUp(): Boolean {
        return !((fromPage == FROM_SIDE_PANE_PAGE) && sidePaneAccessor.isVisible())
    }

    /**
     * 刷新分割线的显隐状态
     */
    private fun updateDividerVisibility() {
        recyclerView?.let { rv ->
            divider?.isVisible = (rv.computeVerticalScrollOffset() > 0)
        }
    }

    override fun refreshCOUIScrollbar() {
        recyclerView?.let { RecyclerViewScrollbarHelper.setupAutoHideScrollbars(it) }
    }

    /**
     * 监听到tab开始切换
     */
    override fun onPageScrollStart() {
        recyclerView?.let { RecyclerViewScrollbarHelper.hideScrollbars(it) }
    }

    override fun onResume() {
        super.onResume()
        setHasOptionsMenu(true)
        updateDividerVisibility()
    }

    private fun doSetViewData() {
        baseListViewModel?.setViewData(
            AlbumViewData(
                id = "",
                position = 0,
                modelType = TYPE_MY_ALBUM_SET,
                isMediaAlbum = false,
                version = 0,
                totalCount = 0,
                title = ""
            )
        )
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (!isResumed) return
        menu.clear()
        updateOptionsMenu(menu, inflater)
        updateToolbar(isInSelectionMode())
    }

    /**
     * 刷新标题栏菜单状态
     */
    private fun updateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        cancelButton = null
        selectAllCheckButton = null
        if (isInSelectionMode()) {
            inflater.inflate(BasebizR.menu.base_opt_album_selection, menu)
            menu.findItem(BasebizR.id.action_select_all)?.let { menuItem ->
                selectAllCheckButton = menuItem.actionView as? MenuCheckButton
                selectAllCheckButton?.let { checkButton ->
                    checkButton.state = if (isSelectAll()) SELECT_ALL else SELECT_NONE
                    checkButton.setOnClickListener {
                        if (DoubleClickUtils.isFastDoubleClick()) return@setOnClickListener
                        onOptionsItemSelected(menuItem)
                    }
                }
            }
            menu.findItem(BasebizR.id.action_cancel)?.let { menuItem ->
                cancelButton = menuItem.actionView as? MenuButton
                cancelButton?.setOnClickListener {
                    if (DoubleClickUtils.isFastDoubleClick()) return@setOnClickListener
                    onOptionsItemSelected(menuItem)
                }
            }
        } else {
            inflater.inflate(R.menu.my_album_set_toolbar_menu, menu)
        }
    }

    /**
     * 刷新Toolbar中标题、返回按钮的状态
     * @param isInSelectionMode 是否在选择模式
     */
    private fun updateToolbar(isInSelectionMode: Boolean) {
        if (isInSelectionMode) {
            toolbar?.isTitleCenterStyle = true
            setDisplayHomeAsUpEnabled(false)
        } else {
            toolbar?.apply {
                title = getText(R.string.model_my_album)
                isTitleCenterStyle = false
            }
            setDisplayHomeAsUpEnabled(isNeedHomeAsUp())
            if (isNeedHomeAsUp()) updateToolbarBackMenuItem()
        }
    }

    override fun onEnterSelection() {
        super.onEnterSelection()
        startToolbarAnimationWhenEnterSelection()
        recyclerAdapter.isMaskVisible = true
    }

    override fun onExitSelection() {
        super.onExitSelection()
        // 正常退出选择模式时走动画，若切换tab到共享tab，则不走动画
        if (isToolbarAnimationNeeded) {
            startToolbarAnimationWhenExitSelection()
        } else {
            activity?.invalidateOptionsMenu()
            isToolbarAnimationNeeded = true
        }
        makeBottomMenuItemUnEnable()
        recyclerAdapter.isMaskVisible = false
    }

    /**
     * 使底部菜单按钮全部进入不可用状态
     */
    private fun makeBottomMenuItemUnEnable() {
        bottomMenuBar?.menu?.let { menu ->
            changeMenuItemEnable(menu.findItem(BasebizR.id.action_rename_album), false)
            changeMenuItemEnable(menu.findItem(BasebizR.id.action_recycle_album), false)
            changeMenuItemEnable(menu.findItem(BasebizR.id.action_move_to_sidepane), false)
            changeMenuItemEnable(menu.findItem(BasebizR.id.action_encrypt_album), false)
            changeMenuItemEnable(menu.findItem(BasebizR.id.action_more_info), false)
        }
    }

    /**
     * Toolbar进入选择模式时的动画
     */
    private fun startToolbarAnimationWhenEnterSelection() {
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener {
                updateSelectionTitleLater = false
                updateToolbarEditTitle(
                    defaultTitleResId = BasebizR.string.base_title_select_album,
                    count = baseListViewModel?.getSelectedItemCount() ?: SELECT_NONE
                )
                activity?.invalidateOptionsMenu()
            }
            start()
        }
    }

    /**
     * Toolbar退出选择模式时的动画
     */
    private fun startToolbarAnimationWhenExitSelection() {
        toolbarFadeAnimator?.apply {
            cancelAnimation()
            setToolbarAnimationListener {
                updateSelectionTitleLater = true
                toolbar?.title = getString(R.string.model_my_album)
                activity?.invalidateOptionsMenu()
            }
            start()
        }
    }

    override fun onSelectionStateChange() {
        super.onSelectionStateChange()
        val selectedCount = baseListViewModel?.getSelectedItemCount() ?: 0
        if (isInSelectionMode()) {
            updateToolbarEditTitle(defaultTitleResId = BasebizR.string.base_title_select_album, count = selectedCount)
            updateSelectAllButton()
        }
        updateMenuBySelectCount(selectedCount)
    }

    /**
     * 根据选中项的数量更新菜单状态，单选支持重命名、删除、添加私密、添加到侧边栏，多选态不支持重命名
     * @param selectedCount 选中项个数
     */
    private fun updateMenuBySelectCount(selectedCount: Int) {
        bottomMenuBar?.menu?.let { menu ->
            myAlbumSetViewModel?.let { viewModel ->
                (selectedCount >= SELECTED_SIZE_ONE).let { isEnabled ->
                    changeMenuItemEnable(
                        menu.findItem(BasebizR.id.action_rename_album),
                        (selectedCount == SELECTED_SIZE_ONE) && (myAlbumSetViewModel?.isSupportRenameForSelectedAlbum() == true)
                    )
                    changeMenuItemEnable(menu.findItem(BasebizR.id.action_recycle_album), isEnabled && viewModel.isSelectAlbumSupportDelete())
                    changeMenuItemEnable(menu.findItem(BasebizR.id.action_encrypt_album), isEnabled && viewModel.isSelectAlbumSupportDelete())
                    changeMenuItemEnable(
                        menu.findItem(BasebizR.id.action_move_to_sidepane),
                        isEnabled && viewModel.canMoveInSidePaneForSelectedAlbum()
                    )
                    changeMenuItemEnable(menu.findItem(BasebizR.id.action_more_info), isEnabled)
                }
            }
        }
    }

    /**
     * 更新全选按钮状态
     */
    private fun updateSelectAllButton() {
        val lastSelectedStatus = selectAllCheckButton?.state
        selectAllCheckButton?.state = if (isSelectAll()) SELECT_ALL else SELECT_NONE
        if ((lastSelectedStatus != selectAllCheckButton?.state)) {
            // 按钮由全选<->取消反选变化时，由于文本长度不一致，导致显示不全，所以需要重新触发更新
            activity?.invalidateOptionsMenu()
        }
    }

    /**
     * 改变MenuItem的isEnabled状态
     */
    private fun changeMenuItemEnable(menuItem: MenuItem, isEnabled: Boolean) {
        if (menuItem.isEnabled != isEnabled) {
            menuItem.isEnabled = isEnabled
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!isResumed) return false
        when (item.itemId) {
            R.id.edit_album -> {
                trackAndSendAlbumsAction(enterSelect = ALBUMS_ACTION_CLICK_ENTER_SELECT_PRESS_SELECT_VALUE)
                enterSelectionMode()
            }

            R.id.action_add_new_album -> {
                val trackCallerEntry = TrackCallerEntry(trackPage, baseListViewModel?.trackPath)
                menuHelper?.doCreateAlbumAction(this@MyAlbumSetFragment, trackCallerEntry)
            }

            BasebizR.id.action_cancel -> exitSelectionMode()

            BasebizR.id.action_select_all -> if (isSelectAll().not()) selectAll() else unselectAll()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onBottomMenuBarItemClicked(menuItem: MenuItem) {
        super.onBottomMenuBarItemClicked(menuItem)
        menuHelper ?: return
        if (DoubleClickUtils.isFastDoubleClick()) return
        val trackCallerEntry = TrackCallerEntry(
            page = trackPage,
            path = baseListViewModel?.trackPath,
            albumsActionCurrentPage = getUserActionCurPage(TYPE_ALBUMS_ACTION)
        )
        when (menuItem.itemId) {
            BasebizR.id.action_recycle_album -> {
                myAlbumBottomMenuHelper?.doSelectionRecycleAction(
                    trackCallerEntry,
                    ::onRemoveActionCallback
                )
            }

            BasebizR.id.action_rename_album -> myAlbumBottomMenuHelper?.doRenameAlbumAction(trackCallerEntry)

            BasebizR.id.action_move_to_sidepane -> myAlbumBottomMenuHelper?.doMoveInSidePaneAction(trackCallerEntry)

            BasebizR.id.action_encrypt_album -> {
                myAlbumBottomMenuHelper?.doAlbumSafeBoxAction(
                    trackCallerEntry,
                    lifecycle,
                    callbackWrapper = BottomMenuHelper.LifecycleCallbackWrapper(lifecycle, ::onRemoveActionCallback)
                )
            }

            BasebizR.id.action_more_info -> {
                myAlbumBottomMenuHelper?.showMorePopupWindow(
                    bottomMenuBar?.findViewById<BottomNavigationItemView>(menuItem.itemId),
                    trackCallerEntry
                )
            }
        }
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        config.apply {
            if (windowWidth.isChanged() || windowHeight.isChanged() || screenMode.isChanged()) {
                updateMenuItemVisibility()
                if (isResumed && !isInSelectionMode()) {
                    setDisplayHomeAsUpEnabled(isNeedHomeAsUp())
                }
            }
        }
    }

    /**
     * 更新底部菜单选项中侧边栏和私密保险箱的显示状态
     */
    private fun updateMenuItemVisibility() {
        bottomMenuBar?.menu?.let { menu ->
            menu.findItem(BasebizR.id.action_move_to_sidepane)?.isVisible = sidePaneAccessor?.isVisible() == true

            lifecycleScope.launch(Dispatchers.IO) {
                val isAlbumSafeBoxSupported = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_ALBUM_SAFE_BOX)
                withContext(Dispatchers.Main) {
                    menu.findItem(BasebizR.id.action_encrypt_album)?.isVisible = isAlbumSafeBoxSupported
                }
            }
        }
    }

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
    // 虚拟按键在父fragment适配的，这里不再需要修改底部间距
    }

    override fun showBottomMenuBar() {
        createBottomMenuIfNeed()
        bottomMenuBar?.let {
            it.show()
            menuHelper?.bindMenuView(it)
            it.setBackgroundColor(COUIThemeUtils.getThemeAttrColor(context, supportR.attr.couiColorBackgroundElevated))
            setParentMockNaviBarColor(COUIThemeUtils.getThemeAttrColor(context, supportR.attr.couiColorBackgroundElevated))
            recyclerView?.updatePadding(bottom = bottomMenuHeight)
        }
        updateMenuItemVisibility()
    }

    override fun hideBottomMenuBar() {
        bottomMenuBar?.hide()
        setParentMockNaviBarColor(COUIThemeUtils.getThemeAttrColor(context, foundationR.attr.gColorBackgroundWithCard))
        recyclerView?.updatePadding(bottom = recyclerViewPaddingBottom)
    }

    /**
     * 虚拟按键是在父布局适配的，所以需要调父布局方法修改色值
     */
    private fun setParentMockNaviBarColor(@ColorInt color: Int) {
        (parentFragment as? MyAlbumSetTabFragment)?.setMockNaviBarColor(color)
    }

    override fun getUserActionCurPage(trackType: String?): String {
        return when (trackType) {
            TYPE_ALBUMS_ACTION -> ALBUMS_ACTION_NOT_COMMONLY_USE_ALBUM_PAGE_VALUE
            else -> NOT_COMMONLY_USED_ALBUM_SET_PAGE
        }
    }

    override fun supportClickStatusBar(): Boolean = true

    override fun onStatusBarClicked() {
        // 拖拽过程中不支持回顶
        if ((recyclerAdapter as? DraggableListAdapter)?.isDragging == true) {
            GLog.d(TAG, LogFlag.DL) { "onStatusBarClicked. isDragging. skip" }
            return
        }
        super.onStatusBarClicked()
    }

    override fun onClickSelectedTabAgain() {
        onStatusBarClicked()
    }

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    override fun customizeToolbar(toolbar: COUIToolbar) {
        this.toolbar = toolbar
    }

    override fun customizeDividerUnderToolbar(divider: View) {
        this.divider = divider
    }

    override fun onDragEnd(from: DragData, to: DragData) {
        super.onDragEnd(from, to)
        myAlbumSetViewModel?.notifyUserOrderUpdated(from, to)
    }

    override fun getAdapterItemConfigs(): List<ItemConfig<AlbumViewData>> {
        return mutableListOf<ItemConfig<AlbumViewData>>().apply {
            context?.let { context ->
                add(
                    ItemConfig(
                        AlbumViewData::class.java,
                        EditableCardAlbumSetViewDataBinding(
                            context = context,
                            stylePool = baseListViewModel,
                            isMaskVisibleGet = { isMaskVisible() },
                            checkboxAnimEnableGet = { checkboxAnimEnable() }
                        )
                    )
                )
            }
        }
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        GLog.d(TAG, DL) {
            "onSidePaneSlideStart: isResumed:$isResumed newState=$newState $clazzSimpleName"
        }
        if (isResumed && newState.isFlat()) {
            val currentSpanCount = getListSpanCount()
            val currentItemWidth = getListItemWidth()
            context?.let { context ->
                refreshLayoutManager(context)
            }
            sidePaneAnim?.startAnimation(
                SidePaneAnimationConfig(
                    currentSpanCount = currentSpanCount,
                    nextSpanCount = getListSpanCount(),
                    currentItemWidth = currentItemWidth,
                    nextItemWidth = layoutDetail.itemWidth,
                    slideWidth = sidePaneAccessor.getSlideWidth(),
                    gapWidth = resources.getDimensionPixelOffset(R.dimen.my_album_recyclerView_gap),
                    newState = newState
                )
            )
        }
    }

    private fun getListItemWidth(): Int {
        return (recyclerView?.layoutManager as? GridLayoutManager)?.getChildAt(0)?.width ?: 0
    }

    private fun getListSpanCount(): Int = (recyclerView?.layoutManager as? GridLayoutManager)?.spanCount ?: 1

    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        sidePaneAnim?.endAnimation(newState)
        if (!isResumed && newState.isFlat()) {
            context?.let {
                refreshLayoutManager(it)
            }
        }
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        sidePaneAnim?.animating(newState, slideProgress)
    }

    override fun initLayoutDetail(context: Context): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        parentWidth = getContentWidth()
        edgeWidth = context.resources.getDimensionPixelOffset(com.oplus.gallery.basebiz.R.dimen.card_album_recyclerView_edge)
        gapWidth = context.resources.getDimensionPixelOffset(R.dimen.my_album_recyclerView_gap)
        spanCount = AlbumCardListUtils.getSpanCount(context, getContentWidth())
    }.build().apply {
        itemDecorationGapPx.top = context.resources.getDimension(BasebizR.dimen.base_album_set_fragment_item_view_top_gap)
        itemDecorationGapPx.bottom = context.resources.getDimension(BasebizR.dimen.base_album_set_fragment_item_view_bottom_gap)
    }

    /**
     * 获取当前屏幕(减去侧边栏）的宽度
     */
    override fun getContentWidth(): Int {
        var slideWidth = 0
        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let {
                slideWidth = if (it.isFlat() && it.isOpen()) it.getSlideWidth() else 0
            }
        }
        return getCurrentAppUiConfig().windowWidth.current - slideWidth
    }

    override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
        GLog.d(TAG, DL) { "onItemClick: viewData: $viewData" }
        viewData ?: return
        if (isInSelectionMode()) {
            val isSupportRecycle = (viewData.supportedAbilities?.getBoolean(SlotOverlayHelper.SUPPORT_DELETE) ?: false ||
                    isScreenShotOrFavorite(viewData.albumId)) || isMap(viewData.albumId)
            if (!isSupportRecycle) {
                GLog.d(TAG, DL) { "onItemClick, this album cannot be selected" }
                return
            }
            baseListViewModel?.toggleItemSelection(viewData.position)
        } else {
            /**
             * 当前 Fragment 是被 MyAlbumSetTabFragment 包裹，转场动效是回调的 MyAlbumSetTabFragment 的 onCreateAnimation() 方法
             * 所以这里需要修改 parentFragment 的记录的进场动效的方向信息
             */
            (parentFragment as? BaseFragment)?.resetSeamlessAnimRootNodeConfig(
                resources.configuration.orientation,
                resources.configuration.screenWidthDp,
                resources.configuration.screenHeightDp
            )
            val radius = context?.resources?.getDimension(BasebizR.dimen.base_album_item_sub_cover_image_corner_radius) ?: 0f
            //跳转随身卡包页
            if (CardCaseUtils.isCardIDAlbum(viewData.albumId)) {
                enterCardCaseAlbum(
                    this,
                    recyclerAdapter.getItemByIndex(position),
                    radius = radius
                )
                return
            } else if (viewData.albumId == Constants.Album.VirtualAlbum.MAP_ALBUM_ID) {
                jumpToMapTravelTabFragment(
                    this,
                    recyclerAdapter.getItemByIndex(position),
                    radius
                )
                return
            }
            startCommonAlbum(viewData, position)
        }
    }


    private fun jumpToMapTravelTabFragment(fragment: BaseFragment?, itemView: View?, radius: Float) {
        val mapJumper = MapPageJumper()
        //这里的进入页面的动效有点不对，后续需要定位原因，（初步怀疑是这个containner的id导致）
        mapJumper.enterMapLocationAlbum(fragment, itemView = itemView, radius = radius)
    }


    /**
     * 判断是否是截屏或收藏图集
     */
    private fun isScreenShotOrFavorite(albumId: Int): Boolean {
        return ((albumId == SCREEN_SHOT_ALBUM_ID) || (albumId == FAVORITE_ALBUM_ID))
    }

    /**
     * 判定是否是地图图集
     */
    private fun isMap(albumId: Int): Boolean {
        return albumId == MAP_ALBUM_ID
    }

    /**
     * 点击进入普通的图集页面，包括自建、三方图集、系统图集
     */
    private fun startCommonAlbum(viewData: AlbumViewData, position: Int) {
        val floatingWindowOffset = IntArray(2)
        if (isFloatingWindowMode()) view?.rootView?.getLocationOnScreen(floatingWindowOffset)
        val clickedItemView = recyclerAdapter.getItemByIndex(position)
        startByStack<BaseAlbumFragment>(
            postCard = PostCard(RouterConstants.RouterName.ALBUM_FRAGMENT),
            anim = SEAMLESS_ANIM_ARRAY,
            data = Bundle().apply {
                val isSelfAlbum = viewData.supportedAbilities?.getBoolean(SUPPORT_IS_SELF_ALBUM) ?: false
                val isThirdAppAlbum = viewData.supportedAbilities?.getBoolean(SUPPORT_IS_THIRD_APP_ALBUM) ?: false
                if (isSelfAlbum) {
                    // 用于数据埋点,自建图集
                    putString(ALBUM_NAVIGATION_TRACK_PAGE_ID, LaunchExitPopupConstant.Value.CUR_PAGE_USER_CREATE_ALBUM)
                } else if (isThirdAppAlbum) {
                    putString(ALBUM_NAVIGATION_TRACK_PAGE_ID, LaunchExitPopupConstant.Value.CUR_PAGE_THIRD_APP_ALBUM)
                }
                putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
                FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                    context,
                    this,
                    clickedItemView,
                    TriggerViewRectGetter(clickedItemView, floatingWindowOffset),
                    ENTRANCE_MY_ALBUM_SET_SECOND
                )
            }
        )
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : FragmentSystemBarStyle(this) {
        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            setNaviBarColor(Color.TRANSPARENT)
            windowInsets.naviBarInsets().apply {
                if (bottomMenuBar?.isVisible() == true) {
                    recyclerView?.updatePadding(bottom = bottomMenuHeight)
                } else {
                    recyclerView?.updatePadding(bottom = recyclerViewPaddingBottom)
                }
            }
        }
    }

    override fun onSetUpViewModelStyle(viewModel: ListViewModel<MediaSet, AlbumViewData>) {
        context?.let { context ->
            val roundThumbStyleData = StyleData().apply {
                put(StyleData.KEY_THUMB_LAYOUT_STYLE, StyleData.STYLE_ROUND)
                put(
                    StyleData.KEY_THUMB_LAYOUT_CORNER_RADIUS,
                    context.resources.getDimension(BasebizR.dimen.label_album_set_item_cover_corners_radius)
                )
                put(
                    StyleData.KEY_THUMB_SIZE_TYPE,
                    ThumbnailSizeUtils.getMicroThumbnailKey(resources.getDimensionPixelOffset(BasebizR.dimen.label_album_set_item_cover_width))
                )
                put(
                    StyleData.KEY_THUMB_STROKE_WIDTH,
                    context.resources.getDimension(foundationR.dimen.common_round_drawable_frame_stroke_width)
                )
                put(StyleData.KEY_THUMB_STROKE_PAINT_COLOR, context.getColor(BasebizR.color.common_round_drawable_frame_stroke_color))
                put(
                    StyleData.KEY_THUMB_BACKGROUND_PAINT_COLOR,
                    COUIContextUtil.getAttrColor(context, foundationR.attr.gColorPressBackground)
                )
            }
            viewModel.addStyle(StyleType.TYPE_THUMB_STYLE, roundThumbStyleData)
            viewModel.addStyle(StyleType.TYPE_DRAWABLE_THUMB_STYLE, roundThumbStyleData)
        }
    }

    override fun onVisibilityChange(isVisible: Boolean, view: View) {
        //当切换分组到共享tab时，需要退出选择模式
        if (isInSelectionMode() && isVisible.not()) {
            isToolbarAnimationNeeded = isVisible
            exitSelectionMode()
        }
    }

    private companion object {
        private const val TAG = "MyAlbumSetFragment"
    }
}