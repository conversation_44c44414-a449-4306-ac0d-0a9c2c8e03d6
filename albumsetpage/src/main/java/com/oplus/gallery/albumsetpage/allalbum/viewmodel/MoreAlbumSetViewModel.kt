/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MoreAlbumSetViewModel.kt
 ** Description: 更多图集ViewModel
 ** Version: 1.0
 ** Date: 2025/4/23
 ** Author: <PERSON><PERSON><PERSON>@Apps.Gallery
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>@Apps.Gallery       2025/4/23         1.0
 ********************************************************************************/
package com.oplus.gallery.albumsetpage.allalbum.viewmodel

import android.app.Application
import android.os.Bundle
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.albumsetpage.albumset.entry.MediaAlbumViewData
import com.oplus.gallery.albumsetpage.allalbum.ui.VirtualEntryBuilder
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.util.AlbumPasswordSettingsUtil
import com.oplus.gallery.business_lib.photoclean.PhotoCleanHelper
import com.oplus.gallery.business_lib.photoclean.PhotoCleanHelper.isSupportPhotoClean
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CLEAN_UP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.RECYCLE_BIN_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SAFE_BOX_ALBUM_ID
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.ViewBehavior.SHOW_SAFE_BOX_ALBUM
import com.oplus.gallery.framework.abilities.data.DataRepository
import com.oplus.gallery.framework.abilities.data.DataRepository.LocalAlbumModelGetter.Companion.TYPE_MORE_ALBUM_SET
import com.oplus.gallery.framework.abilities.data.DataRepository.PhotoCleanAlbumGetter.Companion.TYPE_PHOTO_CLEAN_MODEL
import com.oplus.gallery.framework.abilities.data.model.AlbumSetModel
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

open class MoreAlbumSetViewModel(application: Application) : BaseFooterAlbumSetViewModel<AlbumSetModel>(application), ILoadTaskOwner {
    override val id: String = TaskOwnerConst.MORE_ALBUM_SET
    override val container: String = TaskOwnerConst.MORE_ALBUM_SET

    // 私密图集
    private var currentSafeBoxViewData: MediaAlbumViewData? = null

    // 清理建议
    private var currentPhotoCleanViewData: MediaAlbumViewData? = null

    // Album表中的图集,目前只有回收站图集（最近删除）
    private var currentRecycleAlbum: List<MediaAlbumViewData>? = null

    private var isFirstLoaded = false

    private var needVerifyPassword: Boolean? = null

    // 最近项目是否为空
    private var isPictureEmpty: Boolean = false

    override fun onCreateModel(arguments: Bundle?): AlbumSetModel {
        return DataRepository.getAlbumSetModel(TYPE_MORE_ALBUM_SET, arguments ?: Bundle()) as AlbumSetModel
    }

    override fun getReloadTaskName(): String {
        return TASK_NAME
    }

    override fun postAlbumViewData(mediaAlbums: List<MediaAlbumViewData>) {
        viewModelScope.launch(Dispatchers.ReloadTask) {
            currentRecycleAlbum = mediaAlbums
            if (!isFirstLoaded) {
                isFirstLoaded = true
                /*
                * 首次加载,若photoClean的任务执行的比safeBox的任务要早,则清理建议不会显示,所以使用reloadTask单线程确保按序执行
                * 因为私密图集和清理建议的数据构建是按序的，所以私密图集的数据拿到后，可以不用刷新页面。待清理建议加载后统一刷新。
                * */
                updateSafeBoxEntryAsyncIfChanged(false)
                updatePhotoCleanEntryIfChanged()
            } else {
                buildViewData("postAlbumViewData")
            }
        }
    }

    /**
     * 清理建议数据更新
     */
    private fun updatePhotoCleanEntryIfChanged() {
        if (!isSupportPhotoClean(context)) {
            return
        }
        val currentShowRedDot = PhotoCleanHelper.isPhotoCleanMenuEntryTipsRedDotShowing()
        updatePhotoCleanEntry(showPhotoCleanRedDot(currentShowRedDot))
    }

    /**
     * 需要确保currentPhotoCleanViewData赋值不存在多线程问题
     */
    private fun updatePhotoCleanEntry(currentShowRedDot: Boolean) {
        val entry = VirtualEntryBuilder.copyAndUpdatePhotoCleanEntry(currentShowRedDot)
        val viewData = MediaAlbumViewData(
            id = "",
            modelType = TYPE_PHOTO_CLEAN_MODEL,
            title = context.getString(entry.mediaTypeTextId),
            count = 0,
            mediaEntry = entry,
        )
        viewData.albumId = CLEAN_UP_ALBUM_ID
        onUpdateViewData(viewData)
        currentPhotoCleanViewData = viewData
        buildViewData("updatePhotoCleanEntry")
    }

    /**
     * 更新清理建议的红点 currentPhotoCleanViewData赋值可能会存在多线程问题,所以需要抛到同一个线程中执行
     */
    fun updatePhotoCleanRedDotAsyncIfNeed() {
        viewModelScope.launch(Dispatchers.ReloadTask) {
            val currentShowRedDot = PhotoCleanHelper.isPhotoCleanMenuEntryTipsRedDotShowing()
            if (currentShowRedDot == currentPhotoCleanViewData?.mediaEntry?.showRedDot) {
                GLog.d(TAG, DL) { "updatePhotoCleanRedDotIfNeed, not need to update redDot $currentShowRedDot" }
                return@launch
            }
            GLog.d(TAG, DL) { "updatePhotoCleanRedDotIfNeed, update redDot $currentShowRedDot" }
            updatePhotoCleanEntry(showPhotoCleanRedDot(currentShowRedDot))
        }
    }

    /**
     * 更新最近项目数量
     */
    fun updateRecycleBinAlbumVisibilityAsync(isEmpty: Boolean) {
        viewModelScope.launch(Dispatchers.ReloadTask) {
            if (isPictureEmpty != isEmpty) {
                isPictureEmpty = isEmpty
                if (isFirstLoaded) {
                    buildViewData("updateRecycleBinAlbumVisibilityAsync")
                }
            }
        }
    }

    protected open fun showPhotoCleanRedDot(currentShowRedDot: Boolean): Boolean = currentShowRedDot

    /**
     * currentSafeBoxViewData 赋值可能会存在多线程问题,所以需要抛到同一个线程中执行
     */
    fun updateSafeBoxEntryAsyncIfChanged(needRefresh: Boolean = true) {
        viewModelScope.launch(Dispatchers.ReloadTask) {
            val isSupportSafeBoxAlbum = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAFE_BOX_ALBUM)
            val isShowSafeBoxAlbum = ConfigAbilityWrapper.getBoolean(SHOW_SAFE_BOX_ALBUM, defValue = true, expDefValue = true)
            GLog.d(TAG, DL) { "updateSafeBoxEntryIfChanged, update $isSupportSafeBoxAlbum $isShowSafeBoxAlbum" }
            if (isSupportSafeBoxAlbum && isShowSafeBoxAlbum) {
                // 不显示数量, 用 0 与 1 省去多余的 livedata 更新, 并确保 redDotChanged 正确传送
                val entry = VirtualEntryBuilder.updateSafeBoxAlbum(0, false)
                val viewData = MediaAlbumViewData(
                    id = "",
                    modelType = DataRepository.SafeBoxAlbumGetter.TYPE_SAFE_BOX_MODEL,
                    title = context.getString(entry.mediaTypeTextId),
                    count = 0,
                    mediaEntry = entry,
                )
                viewData.albumId = SAFE_BOX_ALBUM_ID
                onUpdateViewData(viewData)
                currentSafeBoxViewData = viewData
            } else {
                currentSafeBoxViewData = null
            }
            if (needRefresh) {
                buildViewData("updateSafeBoxEntryAsyncIfChanged")
            }
        }
    }

    override fun onResume() {
        val currentContentIsChange = isContentChange
        super.onResume()
        // 这里不直接判断isContentChange,是因为super.onResume()内会改变isContentChange的值
        if (needVerifyPassword != null && !currentContentIsChange) {
            // 检查是否需要验证隐私密码有无变化。有变化重新构造数据修改最近删除的showFileCount变量
            viewModelScope.launch(Dispatchers.ReloadTask) {
                val currentNeedVerifyPassword = AlbumPasswordSettingsUtil.needVerifyPrivacyPassword(context)
                GLog.d(TAG, DL) { "onResume: isChangePwd=$currentNeedVerifyPassword != ${<EMAIL>}" }
                if (currentNeedVerifyPassword != <EMAIL>) {
                    buildViewData("onResume")
                }
            }
        }
    }

    /**
     * 数据构造
     */
    private fun buildViewData(entrance: String) {
        val newData = ArrayList<MediaAlbumViewData>()
        currentSafeBoxViewData?.let { newData.add(it) }
        val recycleAlbum = currentRecycleAlbum?.find {
            it.albumId == RECYCLE_BIN_ALBUM_ID
        }
        // 回收站数量和最近项目的数量同时为0不显示最近删除和清理建议
        val recycleAlbumIsHide = (recycleAlbum?.totalCount == 0) && isPictureEmpty
        GLog.d(TAG, DL, "[buildViewData]  entrance:$entrance, recycleAlbumIsHide:$recycleAlbumIsHide")
        currentPhotoCleanViewData?.takeIf { !recycleAlbumIsHide }?.let { newData.add(it) }
        currentRecycleAlbum?.takeIf { !recycleAlbumIsHide }?.let { newData.addAll(it) }
        updateVirtualAlbumData(newData)
    }

    protected open fun updateVirtualAlbumData(newData: List<MediaAlbumViewData>) {
        needVerifyPassword = AlbumPasswordSettingsUtil.needVerifyPrivacyPassword(context)
        // 添加 index
        newData.forEachIndexed { index, data ->
            data.position = index
            if (data.albumId == RECYCLE_BIN_ALBUM_ID) {
                data.mediaEntry.showFileCount = needVerifyPassword != true
                data.mediaEntry.showFileStatusIcon = needVerifyPassword == true
            }
        }
        GLog.d(TAG, DL) {
            "updateVirtualAlbumData:size:${internalAlbumsLiveData.value?.size}->${newData.size} needVerifyPassword=$needVerifyPassword," +
                    "$currentSafeBoxViewData;$currentPhotoCleanViewData;${currentRecycleAlbum?.firstOrNull()}"
        }
        runOnUiThread(this) {
            internalAlbumsLiveData.value = newData
            if (internalTotalCountLiveData.value != newData.size) {
                internalTotalCountLiveData.value = newData.size
            }
        }
    }

    override fun getTag(): String {
        return TAG
    }

    companion object {
        private const val TASK_NAME = "MoreAlbumLoadDataTask"
        private const val TAG = "MoreAlbumSetViewModel"
    }
}