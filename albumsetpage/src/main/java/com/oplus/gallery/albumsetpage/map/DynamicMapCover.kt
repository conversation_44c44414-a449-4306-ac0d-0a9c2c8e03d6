/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DynamicMapCover.kt
 ** Description : 动态地图封面获取类，主要封装动态地图封面业务逻辑
 **
 ** Version     : 1.0
 ** Date        : 2025/4/7
 ** Author      : huangyuanwang
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80243592 Huangyuanwang      2024/6/20     1.0        Add KFile header describe
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.map

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.location.LocationManager
import android.net.Uri
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import com.oplus.gallery.basebiz.mappage.MapPageManager
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.util.SystemConfigs
import com.oplus.gallery.business_lib.BusinessLibHelper
import com.oplus.gallery.business_lib.helper.MapPageJumper
import com.oplus.gallery.foundation.database.store.GalleryStore
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.framework.abilities.map.IMapAbility
import com.oplus.gallery.framework.abilities.map.LatLng
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.util.os.ContextGetter.context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class DynamicMapCover(val lifecycle: Lifecycle) : LifecycleEventObserver {

    companion object {

        private const val TAG = "DynamicMapCover"

        private const val SUB_PATH = "position"

        /**
         * 内销首次进入相册的场景下，给地图动态封面初始化延迟4s再加载布局，留给MapComFrame拷贝地图显示必须资源的时间
         */
        private const val MAP_VIEW_INIT_DELAY = 4000L
        const val QUERY_ARG_LAT = "lat"
        const val QUERY_ARG_LNG = "lng"

        /**
         * 如果要关闭该功能，则直接将这个只修改为false即可
         */
        const val DYNAMIC_FEATURE_ON = true

        fun getBaseUri(): Uri {
            val result = GalleryStore.DynamicMapCover.getContentUri()
            return result
        }
    }

    init {
        lifecycle.addObserver(this)
    }

    private val canDynamicMapCover: Boolean by lazy {
        val result = context.getAppAbility<IMapAbility>()?.use {
            it.isSnapshotSupported()
        } ?: false
        GLog.d(TAG, LogFlag.DL, "canDynamicMapCover $result")
        result
    }

    private var currentEvent: Lifecycle.Event = Lifecycle.Event.ON_ANY

    private val mapPageManager by lazy { MapPageManager() }

    private val locationManager by lazy { context.getSystemService(Context.LOCATION_SERVICE) as LocationManager }

    private var isLocating = false

    /**
     * 标记是否已经调用过onDestroy
     * monkey场景下偶现Destroy时Lifecycle.Event.ON_DESTROY回调不执行
     * 在MainTabAlbumSetFragment中onDestroy时调用此类的onDestroy方法，用以标记放置重复执行
     */
    private var isOnDestroyCalled = false

    private var isOnResumeCalled = false

    private var isOnPauseCalled = false

    private var bitmapCallbacks: MutableList<BitmapCallback> = mutableListOf<BitmapCallback>().apply {
        add(object : BitmapCallback {
            override fun onBitmapResult(position: LatLng, bitMap: Bitmap?, isDynamic: Boolean) {
                GLog.d(TAG, LogFlag.DL, "internal BitmapResultCallback invoked position $position, bitmap $bitMap")
                //通知uri，bitmap存在变化
                val bitmapValid = (bitMap != null) && !bitMap.isRecycled
                if (!position.latitude.isNaN() && !position.longitude.isNaN() && bitmapValid) {
                    val originalUri = getBaseUri()
                    val uriWithParams = originalUri.buildUpon()
                        .appendPath(SUB_PATH)
                        .appendQueryParameter(QUERY_ARG_LAT, position.latitude.toString())
                        .appendQueryParameter(QUERY_ARG_LNG, position.longitude.toString())
                        .build()
                    GLog.d(TAG, LogFlag.DL, " notify uri $uriWithParams")
                    context.contentResolver.notifyChange(uriWithParams, null)
                }
            }
        })
    }

    private var mapContainer: ViewGroup? = null

    private var captureMapRegionListener = object : IMapAbility.CaptureMapRegionListener {
        override fun onCaptureMapRegion(location: LatLng, bitmap: Bitmap?) {
            bitmap?.let {
                //回调
                GLog.d(TAG, LogFlag.DL, "onCaptureMapRegion: location $location bitmap $bitmap")
                MapCoverBitmapCache.putToCache(location, it)
                bitmapCallbacks.forEach { callback ->
                    callback.onBitmapResult(location, bitmap, true)
                }
                mapContainer?.visibility = View.GONE
                isLocating = false
                onDestroy("onCaptureMapRegion Suc")
            }
        }
    }


    fun configMapViewContainer(mapViewLayout: ViewGroup): DynamicMapCover {
        GLog.d(TAG, LogFlag.DL, "configMapViewContainer, DynamicMapCover:$this")
        mapContainer = mapViewLayout
        lifecycle.coroutineScope.launch(Dispatchers.IO) {
            val bitmapRequest = DynamicBitmapRequest(null, false, true)
            if (!canTriggerLoadMapCover()) {
                val defaultCover = MapCoverBitmapCache.getDefaultCoverBitmap()
                runOnUiThread(lifecycle.coroutineScope) {
                    processBitmapResultReturned(bitmapRequest, defaultCover)
                }
            }
        }
        return this
    }

    private fun canInitMapSdk(): Boolean {
        val mapAgreed = MapPageJumper.checkMapAgreed(context)
        // 相册设置中的联网权限是否已经开启
        val accessNetworkEnabled = NetworkPermissionManager.isUseOpenNetwork
        GLog.d(TAG, LogFlag.DL, "canInitMapSdk, "
                + "DYNAMIC_FEATURE_ON: $DYNAMIC_FEATURE_ON, canDynamicMapCover: $canDynamicMapCover, "
                + "agreed: $mapAgreed, accessNetworkEnabled: $accessNetworkEnabled"
        )
        return DYNAMIC_FEATURE_ON && canDynamicMapCover && mapAgreed && accessNetworkEnabled
    }

    private fun canTriggerLoadMapCover(): Boolean {
        return canInitMapSdk() && checkLocationPermissionGranted()
    }

    /**
     * 初始化地图SDK和MapView的逻辑
     */
    suspend fun initSdkAndTriggerCoverLoad(needTriggerCoverLoad: Boolean) {
        if (!canInitMapSdk()) {
            GLog.d(TAG, LogFlag.DL, "initSdkAndTriggerCoverLoad, cannot init map sdk")
            return
        }
        mapContainer?.let {
            val isMapSdkInited = isMapSdkInited(it.context.applicationContext)
            if (!isMapSdkInited) {
                mapPageManager.initMapSdk(it.context.applicationContext)
            }
            val granted = checkLocationPermissionGranted()
            if (!granted || !needTriggerCoverLoad) {
                GLog.d(TAG, LogFlag.DL) {
                    "initSdkAndTriggerCoverLoad, don't load cover, granted: $granted, needTriggerCoverLoad: $needTriggerCoverLoad"
                }
                return
            }
            // 内销且首次进入相册时，delay 4s 后再去加载地图
            if (SystemConfigs.isRegionCN && BusinessLibHelper.isFirstEnterGallery && !isMapSdkInited) {
                GLog.i(TAG, LogFlag.DL, "initSdkAndTriggerCoverLoad, init map view after a delay")
                delay(MAP_VIEW_INIT_DELAY)
            }
            runOnUiThread(lifecycle.coroutineScope) {
                mapPageManager.initExplorerPageMapView(it, captureMapRegionListener)
                val bitmapRequest = DynamicBitmapRequest(null, false, true)
                trigerLoadDynamicCover(bitmapRequest)
            }
        } ?: run {
            GLog.w(TAG, LogFlag.DL, "initSdkAndTriggerCoverLoad, mapContainer is null")
        }
    }

    fun isMapSdkInited(context: Context?): Boolean {
        val result = context?.let {
            mapPageManager.isMapSdkInited(it)
        } ?: false
        return result
    }

    fun configBitmapCallback(callback: BitmapCallback): DynamicMapCover {
        bitmapCallbacks.add(callback)
        return this
    }

    fun trigerLoadDynamicCover(bitmapRequest: DynamicBitmapRequest): BitmapProcessCode {
        val location = bitmapRequest.latLng
        val bitMapFromCache = if (location != null) {
            MapCoverBitmapCache.getFromCache(location)
        } else {
            null
        }
        if (bitMapFromCache != null) {
            processBitmapResultReturned(bitmapRequest, bitMapFromCache)
            return BitmapProcessCode.RESULT_SUC_FROM_CACHE
        }
        if (bitmapRequest.onlyFromCache) {
            processBitmapResultReturned(bitmapRequest, null)
            return BitmapProcessCode.RESULT_SUC_FROM_CACHE
        }
        if (!bitmapRequest.dynamic) {
            val defaultCover = MapCoverBitmapCache.getDefaultCoverBitmap()
            processBitmapResultReturned(bitmapRequest, defaultCover)
            return BitmapProcessCode.RESULT_SUC_DEFAULT_COVER
        } else {
            if (isLocating) {
                GLog.w(TAG, LogFlag.DL, "getBitmap isLocating, just return null")
                return BitmapProcessCode.RESULT_FAILED_LAST_REQUEST_IN_PROCESS
            } else {
                GLog.d(TAG, LogFlag.DL, "getBitmap triger location")
                mapPageManager.locateInCurrentPosition()
                isLocating = true
                return BitmapProcessCode.RESULT_SUC_FROM_DYNAMIC
            }
        }
    }

    /**
     * 处理返回的bitmap和回调成功
     */
    private fun processBitmapResultReturned(bitmapRequest: DynamicBitmapRequest, bitmap: Bitmap?) {
        val location = bitmapRequest.latLng
        bitmapCallbacks.forEach {
            it.onBitmapResult(location ?: LatLng(Double.NaN, Double.NaN), bitmap, bitmapRequest.dynamic)
        }
        mapContainer?.visibility = View.GONE
        isLocating = false
    }


    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        currentEvent = event
        when (event) {
            Lifecycle.Event.ON_START -> processOnResume()
            Lifecycle.Event.ON_RESUME -> processOnResume()
            Lifecycle.Event.ON_PAUSE -> processOnPause()
            Lifecycle.Event.ON_STOP -> processOnPause()
            Lifecycle.Event.ON_DESTROY -> {
                onDestroy("onStateChange onDestroy")
            }

            else -> {}
        }
    }


    private fun checkLocationPermissionGranted(): Boolean {
        val fineGranted = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        val coarseGranted = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        val locationEnabled = locationManager.isLocationEnabled
        GLog.d(TAG, LogFlag.DL) {
            "checkPermissionGranted, fineGranted:$fineGranted, coarseGranted:$coarseGranted, locationEnabled:$locationEnabled"
        }
        return (fineGranted || coarseGranted) && locationEnabled
    }

    private fun processOnResume() {
        GLog.d(TAG, LogFlag.DL, "processOnResume this $this, isOnResumeCalled:$isOnResumeCalled, isOnPauseCalled:$isOnPauseCalled")
        if (!isOnResumeCalled) {
            isOnResumeCalled = true
            mapPageManager.onResume()
        }
        isOnPauseCalled = false
    }

    private fun processOnPause() {
        GLog.d(TAG, LogFlag.DL, "processOnPause this $this, isOnResumeCalled:$isOnResumeCalled, isOnPauseCalled:$isOnPauseCalled")
        if (!isOnPauseCalled) {
            isOnPauseCalled = true
            mapPageManager.onPause()
        }
        isOnResumeCalled = false
    }

    private fun processOnDestroy() {
        GLog.d(TAG, LogFlag.DL, "processOnDestroy this $this")
        mapPageManager.onDestroy()
        bitmapCallbacks.clear()
        mapContainer = null
    }

    fun onDestroy(entrance: String) {
        GLog.d(TAG, LogFlag.DL, "onDestroy $entrance this $this, isOnDestroyCalled $isOnDestroyCalled, DynamicMapCover $this")
        if (!isOnDestroyCalled) {
            isOnDestroyCalled = true
            processOnDestroy()
            lifecycle.removeObserver(this)
        }
    }


    interface BitmapCallback {

        fun onBitmapResult(lng: LatLng, bitMap: Bitmap?, isDynamic: Boolean)
    }


    data class DynamicBitmapRequest(val latLng: LatLng?, val onlyFromCache: Boolean, val dynamic: Boolean)

    enum class BitmapProcessCode {
        /**
         * 获取的结果是从缓存中来
         */
        RESULT_SUC_FROM_CACHE,

        /**
         * 获取的结果是默认封面
         */
        RESULT_SUC_DEFAULT_COVER,

        /**
         * 获取的结果需要从
         */
        RESULT_SUC_FROM_DYNAMIC,

        /**
         * 获取的结果是从缓存中来, 缓存中没找到数据，返回为null
         */
        RESULT_FAILED_FROM_CACHE,

        /**
         * 获取结果时，由于不支持动态功能整体条件不匹配（比如内西澳），返回bitmap为null
         */
        RESULT_FAILED_CONDITION_NOT_MEET,

        /**
         * 获取结果时，由于不支持动态，返回bitmap为null
         */
        RESULT_FAILED_PERMISSION_NOT_MEET,

        /**
         * 取结果时，上一次还在定位中，这次先不返回结果
         */
        RESULT_FAILED_LAST_REQUEST_IN_PROCESS
    }
}