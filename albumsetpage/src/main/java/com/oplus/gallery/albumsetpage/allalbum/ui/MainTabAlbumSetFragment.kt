/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MainTabAlbumSetFragment.kt
 ** Description: 图集tab主页
 **
 ** Version: 1.0
 ** Date: 2025/04/01
 ** Author: zhong<PERSON><PERSON>@Apps.Gallery
 ** TAG: OPLUS_FEATURE_TAB
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** z<PERSON><PERSON><PERSON>@Apps.Gallery		2025/04/01		1.0		OPLUS_FEATURE_TAB
 *********************************************************************************/
package com.oplus.gallery.albumsetpage.allalbum.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.Insets
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GalleryLinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.oplus.gallery.albumsetpage.R
import com.oplus.gallery.albumsetpage.allalbum.item.MediaGroupViewData
import com.oplus.gallery.albumsetpage.allalbum.item.MoreGroupViewData
import com.oplus.gallery.albumsetpage.allalbum.item.MyAlbumsViewData
import com.oplus.gallery.albumsetpage.allalbum.item.PickedGroupViewData
import com.oplus.gallery.albumsetpage.allalbum.item.ShortcutViewData
import com.oplus.gallery.albumsetpage.allalbum.viewdata.MainAlbumTabMyAlbumLifecycleFields
import com.oplus.gallery.albumsetpage.allalbum.viewdata.MediaGroupViewDataBinding
import com.oplus.gallery.albumsetpage.allalbum.viewdata.MoreGroupViewDataBinding
import com.oplus.gallery.albumsetpage.allalbum.viewdata.MyAlbumsViewDataBinding
import com.oplus.gallery.albumsetpage.allalbum.viewmodel.MainTabAlbumSetViewModel
import com.oplus.gallery.albumsetpage.base.util.enterCardCaseAlbum
import com.oplus.gallery.albumsetpage.map.DynamicMapCover
import com.oplus.gallery.albumsetpage.picked.PickedGroupViewDataBinding
import com.oplus.gallery.albumsetpage.selection.albums.ui.AlbumSetMenuManager
import com.oplus.gallery.albumsetpage.selection.albums.ui.LifecycleFields
import com.oplus.gallery.albumsetpage.shortcutalbum.MainAlbumTabShortcutCallbackGetter
import com.oplus.gallery.albumsetpage.shortcutalbum.ShortcutAlbumSetViewDataBinding
import com.oplus.gallery.albumsetpage.shortcutedit.ui.EditShortcutAlbumSetFragment
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.TrackConstant.ALBUM_NAVIGATION_TRACK_PAGE_ID
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.SlotOverlayHelper
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.PageLayoutDisabledAdjust
import com.oplus.gallery.basebiz.sidepane.RESTORE_DELAY_TIME
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.adjustControlIconColor
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.track.AlbumsActionTrackHelper
import com.oplus.gallery.basebiz.uikit.AlbumViewData
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.findFullScreenContainerId
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.basebiz.uikit.fragment.IMainTabController
import com.oplus.gallery.basebiz.uikit.fragment.IMenuClickListener
import com.oplus.gallery.basebiz.uikit.fragment.ITabContentFragment
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_MY_ALBUM_SET
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_NORMAL
import com.oplus.gallery.basebiz.uikit.fragment.transition.FragmentTransitionParameterHelper.ENTRANCE_RECYCLE
import com.oplus.gallery.basebiz.uikit.fragment.transition.SeamlessTransitionAnimation.Companion.SEAMLESS_ANIM_ARRAY
import com.oplus.gallery.basebiz.uikit.fragment.transition.TriggerViewRectGetter
import com.oplus.gallery.basebiz.uikit.toolbar.OverflowMenuItem
import com.oplus.gallery.basebiz.util.AlbumPasswordSettingsUtil
import com.oplus.gallery.basebiz.viewgallery.viewaction.UncaughtViewAction.Companion.KEY_ALBUM_VIEW_DATA
import com.oplus.gallery.basebiz.widget.AlbumRecycleView
import com.oplus.gallery.basebiz.widget.BlurButtonWrapperView
import com.oplus.gallery.basebiz.widget.MainTabToolbar
import com.oplus.gallery.basebiz.widget.MultiLayerImageView
import com.oplus.gallery.basebiz.widget.PopupMenuConfigOffsetXRule
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cardcase.utils.CardCaseUtils
import com.oplus.gallery.business_lib.cloudsync.OnCloudSwitchListener
import com.oplus.gallery.business_lib.helper.MapPageJumper
import com.oplus.gallery.business_lib.menuoperation.MenuAction
import com.oplus.gallery.business_lib.menuoperation.MenuActionGetter
import com.oplus.gallery.business_lib.menuoperation.MenuOperationManager
import com.oplus.gallery.business_lib.menuoperation.track.TrackCallerEntry
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.photoclean.PhotoCleanHelper
import com.oplus.gallery.business_lib.safebox.SafeBoxHelper
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment
import com.oplus.gallery.business_lib.ui.fragment.BaseAlbumFragment.Companion.KEY_ALBUM_MODEL_BUNDLE
import com.oplus.gallery.business_lib.util.draganddrop.DragHelper
import com.oplus.gallery.business_lib.util.draganddrop.DropAlbumSetManager
import com.oplus.gallery.foundation.database.album.orderedBottomVirtualAlbumIds
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ALL_PICTURE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.ALL_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CAMERA_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CLEAN_UP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.CSHOT_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.FAST_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.FAVORITE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.GIF_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.LOG_VIDEO_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.MAP_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.OLIVE_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.PANORAMA_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.PORTRAIT_BLUR_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.RAW_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.RECYCLE_BIN_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SAFE_BOX_ALBUM_ID
import com.oplus.gallery.foundation.database.helper.Constants.Album.VirtualAlbum.SLOW_MOTION_ALBUM_ID
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_CSHOT
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_FAST_VIDEO
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_GIF
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_LOG_VIDEO
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_OLIVE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_PANORAMA
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_PORTRAIT
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_RAW
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_MEDIA_TYPE_SLO_MO
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_ALL
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_CAMERA
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_CLEAN
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_DELETE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_FAVORITE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_MAP
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_PRIVATE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_SELF
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_CLICK_TYPE_VIDEO
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_ENTER_SAFE_BOX_FROM_SAFE_BOX_ALBUM
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_MEDIA_ALBUM_VALUE
import com.oplus.gallery.foundation.tracing.constant.AlbumsActionTackConstant.Value.ALBUMS_ACTION_RECYCLE_ALBUM_VALUE
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant
import com.oplus.gallery.foundation.tracing.constant.LaunchExitPopupConstant.Value.CUR_PAGE_MEDIA_ALBUM
import com.oplus.gallery.foundation.tracing.constant.MenuFrameworkTrackConstant
import com.oplus.gallery.foundation.tracing.constant.SearchTrackConstant
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.debug.LogFlag.Companion.DL
import com.oplus.gallery.foundation.util.ext.findFirstVisiblePosition
import com.oplus.gallery.foundation.util.ext.isActivityInvalid
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.album.UpgradeAlbumSetTable.GROUP_ID_MORE_GROUP
import com.oplus.gallery.framework.abilities.config.IConfigListener
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.SINGLE_UN_BUSY
import com.oplus.gallery.standard_lib.baselist.adapter.config.AbsAdapterConfigProvider
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.BaseViewHolder
import com.oplus.gallery.standard_lib.baselist.view.ItemClickListener.Companion.CLICK_AREA_ITEM_VIEW
import com.oplus.gallery.standard_lib.baselist.view.ItemGapDecoration
import com.oplus.gallery.standard_lib.baselist.view.MultiTypeListAdapter
import com.oplus.gallery.standard_lib.bean.ViewData
import com.oplus.gallery.standard_lib.file.Dir
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.oplus.gallery.basebiz.R as baseR
import com.support.appcompat.R as appcompatR
import com.support.toolbar.R as supportR

@RouterNormal(RouterConstants.RouterName.MAIN_TAB_ALBUM_SET_FRAGMENT)
class MainTabAlbumSetFragment : TemplateFragment(), ISidePaneListener, ITabContentFragment, IMenuClickListener {
    private val logTag: String = TAG
    private var listScrollOffset = 0
    private var recyclerView: RecyclerView? = null
    private var mainTabVM: MainTabAlbumSetViewModel? = null
    private var dropManager: DropAlbumSetManager? = null
    private var shortcutAlbumSetViewDataBinding: ShortcutAlbumSetViewDataBinding? = null
    private var myAlbumsViewDataBinding: MyAlbumsViewDataBinding? = null
    private var footerMoreTypeVDB: MoreGroupViewDataBinding? = null
    private var footerMediaGroupVDB: MediaGroupViewDataBinding? = null
    private var pickedGroupViewDataBinding: PickedGroupViewDataBinding? = null
    private val layoutDetail: LayoutDetail by lazy {
        LayoutDetail().apply {
            itemDecorationGapPx.top =
                context?.resources?.getDimension(baseR.dimen.main_tab_album_set_fragment_item_view_top_gap) ?: 0f
        }
    }
    private var recyclerAdapter: MultiTypeListAdapter<ViewData, BaseViewHolder<ViewData>>? = null
    override val trackPage = MenuFrameworkTrackConstant.Value.VALUE_MAIN_TAB_ALBUM_SET_FRAGMENT
    private var isFirstLoadData = true

    private var recyclerAlbumViewData: AlbumViewData? = null
    private var resultLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            GLog.d(TAG, DL) { "resultLauncher privacy password result ${it.resultCode}" }
            if (it.resultCode == Activity.RESULT_OK) {
                recyclerAlbumViewData?.let { viewData -> startCommonAlbum(viewData, needTrack = false) }
            }
        }

    /** 长按:选中某个图集,弹出蒙层和菜单*/
    private val albumMenuManager by lazy {
        AlbumSetMenuManager(LifecycleFields(activity as? BaseActivity, mainTabVM?.viewModelScope ?: AppScope, lifecycle))
    }

    private var dynamicMapCover: DynamicMapCover? = null

    // 允许RecyclerView滑动(包括viewpager、图集首页列表、我的图集列表的滑动)，默认允许，当长按常用图集弹出popWindow时禁止
    private var isRecyclerViewScrollEnabled = true

    // 是否是第一次onResume被调用
    private var isFirstTimeOnResumeCalled: Boolean = true

    // 设置页切换私密图集启用状态监听
    private var safeBoxSwitchChangeListener = IConfigListener {
        GLog.d(TAG, DL) { "updateSafeBox: configId=$it" }
        footerMoreTypeVDB?.updateSafeBoxEntryIfChanged()
        shortcutAlbumSetViewDataBinding?.updateSafeBoxEntryIfChanged()
    }

    private val albumClickListener by lazy {
        object : OnAlbumClickListener<AlbumViewData> {
            override fun onItemLongClick(position: Int, view: View, viewData: AlbumViewData?, type: Int): Boolean {
                GLog.d(TAG, DL) { "onItemLongClick:$position $viewData" }
                viewData ?: return false
                if (context.isActivityInvalid()) {
                    GLog.d(TAG, DL) { "showMenuIfNeed: activity is null || finishing || destroyed" }
                    return false
                }
                if (AlbumSetMenuManager.isUnsupportLongClick(type, viewData).not()) {
                    activity?.findViewById<ViewGroup>(baseR.id.base_fragment_container)?.let { container ->
                        //中大屏，侧边栏的宽度
                        val slideWidth = getCurrentAppUiConfig().windowWidth.current - getContentWidth()
                        val popupMenuConfigRule = (view as? PopupMenuConfigOffsetXRule)
                        popupMenuConfigRule?.popupMenuRuleEnabled = true
                        popupMenuConfigRule?.setOffsetX(slideWidth)
                        albumMenuManager.showMenuIfNeed(view, viewData, container, slideWidth, getFloatingViewBackgroundColor(type)) {
                            setRecyclerViewScrollEnabled(true)
                            popupMenuConfigRule?.popupMenuRuleEnabled = false
                        }
                        setRecyclerViewScrollEnabled(false)
                    }
                }
                return true
            }

            override fun onItemClick(position: Int, viewData: AlbumViewData?, clickType: Int, itemView: View?) {
                GLog.d(TAG, DL) { "onAlbumItemClick,item:$viewData" }
                viewData ?: return
                if (isInterceptItemClick()) {
                    GLog.d(TAG, DL) { "onAlbumItemClick, recyclerView scrollState is not idle， item:$viewData" }
                    return
                }
                val radius = context?.resources?.getDimension(baseR.dimen.base_album_item_sub_cover_image_corner_radius) ?: 0f
                if (!startSpecialAlbumIfNeed(viewData, clickType, itemView, radius)) {
                    itemView?.let {
                        val entrance = if (it.width  < it.height) ENTRANCE_NORMAL else ENTRANCE_MY_ALBUM_SET
                        resetSeamlessRootNodeOrientationState()
                        startCommonAlbum(
                            viewData = viewData,
                            clickType = clickType,
                            clickedItemView = itemView,
                            entrance,
                            radius = radius
                        )
                    }
                }
                if ((clickType == FOOTER_MORE_PROJECT) && (viewData.albumId == CLEAN_UP_ALBUM_ID)) {
                    footerMoreTypeVDB?.updatePhotoCleanRedDotIfNeed()
                }
                trackAlbumClickByAlbumType(viewData)
            }
        }
    }

    /**
     * 二期item改回正方形封面后,需要删除
     */
    private fun getFloatingViewBackgroundColor(type: Int): Int =
        when (type) {
            ALBUM_TYPE_TAB_MY_ALBUMS -> COUIContextUtil.getAttrColor(context, appcompatR.attr.couiColorCardBackground)
            else -> 0
        }

    /**
     * 根据不同的图集类型进行埋点追踪
     *
     * @param viewData 图集视图数据，包含图集类型和相关信息
     */
    private fun trackAlbumClickByAlbumType(viewData: AlbumViewData) {
        val mediaSet: MediaSet? = DataManager.getMediaSet(Path.fromString(viewData.id))
        val type: String = when (viewData.albumId) {
            ALL_PICTURE_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_ALL
            CAMERA_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_CAMERA
            ALL_VIDEO_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_VIDEO
            SAFE_BOX_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_PRIVATE
            CLEAN_UP_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_CLEAN
            RECYCLE_BIN_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_DELETE
            FAVORITE_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_FAVORITE
            MAP_ALBUM_ID -> ALBUMS_ACTION_CLICK_TYPE_MAP
            OLIVE_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_OLIVE
            PANORAMA_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_PANORAMA
            CSHOT_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_CSHOT
            PORTRAIT_BLUR_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_PORTRAIT
            FAST_VIDEO_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_FAST_VIDEO
            SLOW_MOTION_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_SLO_MO
            RAW_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_RAW
            LOG_VIDEO_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_LOG_VIDEO
            GIF_ALBUM_ID -> ALBUMS_ACTION_CLICK_MEDIA_TYPE_GIF
            else -> {
                if (mediaSet?.isSelfAlbum == true) {
                    ALBUMS_ACTION_CLICK_TYPE_SELF
                } else {
                    TextUtil.EMPTY_STRING
                }
            }
        }
        if (type == ALBUMS_ACTION_CLICK_TYPE_SELF) {
            viewData.title?.let { AlbumsActionTrackHelper.trackAlbumSetClick(type, it) }
        } else {
            if (type.isEmpty()) return
            AlbumsActionTrackHelper.trackAlbumSetClick(type)
        }
    }

    private var isSharedAlbumEntranceShow: Boolean = false
    private val cloudSwitchChangeListener = object : OnCloudSwitchListener {
        override fun onCloudSwitchChange(switchType: OnCloudSwitchListener.SwitchType, open: Boolean) {
            if (OnCloudSwitchListener.SwitchType.SHARED_ALBUM == switchType) {
                isSharedAlbumEntranceShow = open
            }
        }
    }

    private val addNewButton: BlurButtonWrapperView? by lazy { activity?.findViewById(baseR.id.albumAddWrapperView) }
    private val newCreateAlbumMenu: NewCreateAlbumMenu? by lazy { activity?.let { NewCreateAlbumMenu(it) } }

    // 顶栏下拉菜单
    private var popupListWindow: COUIPopupListWindow? = null

    private val mainTabToolbar: MainTabToolbar? by lazy { (parentFragment as? IMainTabController)?.getToolbar() }
    private val overflowButtonWrapperView: BlurButtonWrapperView? by lazy { mainTabToolbar?.overflowButtonWrapperView }

    private val mainTabController by lazy { parentFragment as? IMainTabController }

    override fun onResume() {
        super.onResume()
        initSdkAndTriggerCoverLoadIfNeed()
        recyclerView?.scrollY?.let { toolbarSetter?.setListScrollPosition(it) }
    }

    override fun isNeedPredictiveBack(): Boolean = true

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        initRecyclerView(view)
        initViewModel()
        initSidePaneAdjust()
        initDropDrag()
        initCloudShareAlbumSwitchState()
        initDynamicMapCover(view)
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        GLog.d(logTag, DL) { "onAppUiStateChanged,windowWidth:${uiConfig.windowWidth} windowHeight:${uiConfig.windowHeight}" }
        // 屏幕横竖屏切换，图集列表和图集item的间隙改变
        if (uiConfig.windowWidth.isChanged()) {
            shortcutAlbumSetViewDataBinding?.notifyLayoutChanged()
            myAlbumsViewDataBinding?.notifyLayoutChanged(true)
            footerMediaGroupVDB?.notifyLayoutChanged()
            footerMoreTypeVDB?.notifyLayoutChanged()
            pickedGroupViewDataBinding?.notifyLayoutChanged()
        }
    }
    private fun initDynamicMapCover(view: View) {
        if (!DynamicMapCover.DYNAMIC_FEATURE_ON) {
            GLog.d(TAG, "initDynamicMapCover dynamic feature off, no need to init")
            return
        }
        dynamicMapCover = DynamicMapCover(this.lifecycle)
        val mapContainer = view.findViewById<ConstraintLayout>(baseR.id.map_layout)
        dynamicMapCover?.configMapViewContainer(mapContainer)
    }

    private fun initRecyclerView(view: View) {
        recyclerView = view.findViewById(R.id.recycler_view)
        //使用activity的context,由于此fragment的layout是预加载的,context为Application,binding使用此context去inflate时,列表显示空白
        val ctx = context ?: view.context
        recyclerView?.let { rv ->
            rv.isMotionEventSplittingEnabled = false
            rv.layoutManager = GalleryLinearLayoutManager(ctx)
            rv.adapter = MultiTypeListAdapter<ViewData, BaseViewHolder<ViewData>>(layoutDetail, makeAdapterConfigProvider(ctx)).apply {
                recyclerAdapter = this
            }
            addItemDecoration()
            rv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    listScrollOffset = recyclerView.computeVerticalScrollOffset()
                    toolbarSetter?.setListScrollPosition(listScrollOffset)
                    mainTabController?.checkPageImmersiveState(recyclerView.computeVerticalScrollOffset())
                }

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == COUIRecyclerView.SCROLL_STATE_IDLE) {
                        toolbarSetter?.setListScrollStateIdle(recyclerView, recyclerView.computeVerticalScrollOffset())
                    }
                }
            })
            (rv as? AlbumRecycleView)?.let {
                it.onOverScrollListener = object : AlbumRecycleView.OnOverScrollListener {
                    override fun onOverScrolled(scrollX: Int, scrollY: Int, clampedX: Boolean, clampedY: Boolean) {
                        if (listScrollOffset <= 0) {
                            toolbarSetter?.setListScrollPosition(scrollY)
                            mainTabController?.checkPageImmersiveState(scrollY)
                        }
                    }
                }
            } ?: GLog.d(TAG, DL) { "initRecyclerView: rv is not albumRV" }
            rv.addOnItemTouchListener(object : RecyclerView.SimpleOnItemTouchListener() {
                override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                    return !isRecyclerViewScrollEnabled
                }

                override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
                    if (!isRecyclerViewScrollEnabled && (e.action == MotionEvent.ACTION_UP)) {
                        albumMenuManager.startFloatingViewReleaseAnimation()
                    }
                }
            })
            rv.addOnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                val isWidthChanged = v.width != (oldRight - oldLeft)
                mainTabController?.checkPageImmersiveState(position = 0, isForceStart = isWidthChanged, isNeedAnimation = false)
            }
        }
    }

    /**
     * 场景：图集首页长按【常用图集】和【我的图集】时，弹出蒙层和菜单，此时不松手进行滑动，需要禁止下层view滑动事件，
     * 包括viewpager、图集首页列表、我的图集列表的滑动
     * @param isEnable true: 允许滑动，false: 禁止滑动
     */
    private fun setRecyclerViewScrollEnabled(isEnable: Boolean) {
        isRecyclerViewScrollEnabled = isEnable
        myAlbumsViewDataBinding?.setRecyclerViewScrollEnabled(isEnable)
        activity?.findViewById<ViewPager2>(R.id.view_pager)?.isUserInputEnabled = isEnable
    }

    private fun addItemDecoration() {
        recyclerView?.takeIf { it.isComputingLayout.not() }?.let {
            it.addItemDecoration(
                object : ItemGapDecoration(layoutDetail) {
                    override fun getNormalItemOffsets(outRect: Rect, view: View, parent: RecyclerView, itemPosition: Int) {
                        if (itemPosition > 0) {
                            // 首行不设置上间距
                            outRect.top = layoutDetail.itemDecorationGapPx.top.toInt()
                        }
                    }
                }
            )
        }
    }

    private fun makeAdapterConfigProvider(context: Context) = object : AbsAdapterConfigProvider() {
        override val configs: MutableList<ItemConfig<*>> = mutableListOf<ItemConfig<*>>().apply {
            val lifecycle = { this@MainTabAlbumSetFragment }
            add(
                ItemConfig(
                    ShortcutViewData::class.java,
                    ShortcutAlbumSetViewDataBinding(
                        context,
                        MainAlbumTabShortcutCallbackGetter(
                            lifecycle,
                            lifecycle,
                            ::getContentWidth,
                            { albumMenuManager.menuActionCallbackLiveData },
                            { onAdjustButtonClick() },
                            isPictureEmpty = { isPictureEmpty ->
                                mainTabVM?.setIsPictureEmptyLiveData(isPictureEmpty)
                            }
                        ),
                        albumClickListener
                    ).apply {
                        shortcutAlbumSetViewDataBinding = this
                        onSidePaneAccessorGet = onSidePaneAccessorGetter()
                    }
                )
            )
            add(
                ItemConfig(
                    MyAlbumsViewData::class.java,
                    MyAlbumsViewDataBinding(
                        MainAlbumTabMyAlbumLifecycleFields(context, activity, lifecycle, lifecycle),
                        ::getContentWidth,
                        albumClickListener
                    ).apply {
                        myAlbumsViewDataBinding = this
                    }
                )
            )
            add(
                ItemConfig(
                    PickedGroupViewData::class.java, PickedGroupViewDataBinding(
                        context,
                        activity,
                        this@MainTabAlbumSetFragment,
                        lifecycle,
                        lifecycle,
                        ::getContentWidth,
                        ::isInterceptItemClick
                    ).apply {
                        pickedGroupViewDataBinding = this
                    }
                )
            )
            add(
                ItemConfig(
                    MediaGroupViewData::class.java,
                    MediaGroupViewDataBinding(context, lifecycle, lifecycle, albumClickListener, ::getContentWidth).apply {
                        footerMediaGroupVDB = this
                        onSidePaneAccessorGet = onSidePaneAccessorGetter()
                    }
                )
            )
            val moreGroupViewDataBinding = MoreGroupViewDataBinding(context, lifecycle, lifecycle, albumClickListener, ::getContentWidth)
            footerMoreTypeVDB = moreGroupViewDataBinding
            setOnFooterMoreCountChangedListener()
            moreGroupViewDataBinding.onSidePaneAccessorGet = onSidePaneAccessorGetter()
            add(ItemConfig(MoreGroupViewData::class.java, moreGroupViewDataBinding))
        }

        private fun onSidePaneAccessorGetter() = if (isSidePaneEnabled()) ::sidePaneAccessor else null
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initViewModel() {
        mainTabVM = ViewModelProvider(this)[MainTabAlbumSetViewModel::class.java]
        mainTabVM?.initAsync()
        mainTabVM?.isPictureEmptyLiveData?.let { footerMoreTypeVDB?.setIsPictureEmptyLiveData(it) }
        mainTabVM?.activeInfoLiveData?.observe(this) { activeDataInfo ->
            val lastTotalCount = recyclerAdapter?.totalCount
            val totalCount = activeDataInfo.totalCount
            GLog.d(logTag, DL) { "initViewModel.totalCount:$lastTotalCount->$totalCount first=$isFirstLoadData ${recyclerAdapter != null}" }
            // refresh可能触发多次，避免totalCount和lastTotalCount都为0的场景adapter不必要的刷新，可能会recycleView打断上一次的动画
            if (!isFirstLoadData && (totalCount == 0) && (lastTotalCount == 0)) {
                return@observe
            }
            if ((totalCount == 0) || (lastTotalCount != totalCount) || isFirstLoadData) {
                isFirstLoadData = false
                recyclerAdapter?.totalCount = totalCount
                refreshEmptyView()
            }
            // 分组不需要diff动画:异常场景:当前拖动分组排序,拖动到首位时,动画补位异常,分组没必要动画,先禁掉diff动画
            recyclerAdapter?.setDataSet(activeDataInfo.activeViewDataArray, refresh = true)
        }
    }

    private fun isInterceptItemClick(): Boolean {
        return recyclerView?.scrollState != RecyclerView.SCROLL_STATE_IDLE
    }

    /**
     * 更多项目数量刷新时刷新空布局
     * mainTabVM.activeInfoLiveData数据可能返回更多项目。但是更多项目由于各种开关控制也不会显示，此时也应该展示空布局
     */
    private fun setOnFooterMoreCountChangedListener() {
        footerMoreTypeVDB?.totalCountLiveData?.observe(this) {
            // 只有列表的totalCount为1才需要刷新
            if (recyclerAdapter?.totalCount == 1) {
                refreshEmptyView()
            }
        }
    }

    private fun isShowEmptyView(): Boolean {
        val totalCount = recyclerAdapter?.totalCount ?: 0
        return when {
            (totalCount > 1) -> false
            // 列表数量为1而且不是更多项目，不显示空布局
            (totalCount == 1) && (mainTabVM?.findGroupViewData(GROUP_ID_MORE_GROUP) == null) -> false
            // 列表数量为1是更多项目，继续判断更多项目的totalCount
            (totalCount == 1) && (footerMoreTypeVDB?.totalCountLiveData?.value != 0) -> false
            else -> true
        }
    }

    /**
     * 无图集数据时,显示空页面
     */
    private fun refreshEmptyView() {
        if (isShowEmptyView()) {
            showEmptyPageView()
        } else {
            hideEmptyPageView()
        }
    }

    private fun initDropDrag() {
        if (isSupportDrop()) {
            contentView?.let {
                dropManager = DropAlbumSetManager(this)
                dropManager?.bindDropView(it)
            }
        }
    }

    private fun initSidePaneAdjust() {
        val accessor = sidePaneAccessor
        if ((accessor != null) && isSidePaneEnabled()) {
            contentView?.let {
                PageLayoutDisabledAdjust(this, accessor) { RESTORE_DELAY_TIME }.bindView(it)
            }
        }
    }

    private fun initCloudShareAlbumSwitchState() {
        lifecycleScope.launch(Dispatchers.SINGLE_UN_BUSY) {
            isSharedAlbumEntranceShow = ApiDmManager.getCloudSyncDM().isSharedAlbumEntranceShow()
            ApiDmManager.getCloudSyncDM().registerSwitchListener(cloudSwitchChangeListener)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dynamicMapCover?.onDestroy("onDestroy")
        // onDestroy中不能使用lifecycleScope，协程不会执行
        AppScope.launch(Dispatchers.SINGLE_UN_BUSY) {
            ApiDmManager.getCloudSyncDM().unregisterSwitchListener(cloudSwitchChangeListener)
        }
        popupListWindow?.dismiss()
    }

    private fun startRecyclerAlbumDecryptIfNeed(viewData: AlbumViewData, clickType: Int, itemView: View?, radius: Float) {
        clickAlbumTrack(viewData)
        resetSeamlessRootNodeOrientationState()
        lifecycleScope.launch(Dispatchers.CPU) {
            val needVerifyPassword = AlbumPasswordSettingsUtil.needVerifyPrivacyPassword(context)
            GLog.d(TAG, DL) { "startRecyclerAlbumDecryptIfNeed, needVerifyPassword = $needVerifyPassword" }
            withContext(Dispatchers.Main) {
                if (needVerifyPassword) {
                    runCatching {
                        <EMAIL> = viewData
                        resultLauncher.launch(context?.let { AlbumPasswordSettingsUtil.makeDecryptIntent(it) })
                    }.onFailure {
                        GLog.e(TAG, "makeDecryptIntent fail ${it.message}")
                    }
                } else {
                    startCommonAlbum(
                        viewData = viewData,
                        clickType = clickType,
                        clickedItemView = itemView,
                        ENTRANCE_RECYCLE,
                        radius = radius,
                        needTrack = false
                    )
                }
            }
        }
    }

    private fun onClickSafeBoxMenuEntry(activity: FragmentActivity) {
        val isSupportUserCustomSafeBox: Boolean = ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_USER_CUSTOM_SAFE_BOX)
        if (!isSupportUserCustomSafeBox) {
            ToastUtil.showShortToast(baseR.string.base_feature_is_disabled_tip)
            return
        }
        SafeBoxHelper.startSafeBox(activity, ALBUMS_ACTION_ENTER_SAFE_BOX_FROM_SAFE_BOX_ALBUM)
    }

    override fun isSidePaneEnabled(): Boolean = sidePaneAccessor.isSupportSidePane()

    override fun currentShouldToolbarTint(): Boolean = isTopImmersiveCurrently()

    override fun isTopImmersiveCurrently(): Boolean {
        var scrollDistance = recyclerView?.computeVerticalScrollOffset() ?: 0
        if (scrollDistance <= 0) {
            scrollDistance = recyclerView?.scrollY ?: 0
        }
        return scrollDistance > safeGetDimensionPixelSize(baseR.dimen.main_tab_toolbar_bottom_padding)
    }

    override fun isBottomImmersiveCurrently(): Boolean {
        val recyclerView = this.recyclerView ?: return false

        val distanceToListBottom = recyclerView.let {
            // 滑动到列表底部还需要的距离
            it.computeVerticalScrollRange() - (it.computeVerticalScrollOffset() + recyclerView.computeVerticalScrollExtent() + recyclerView.scrollY)
        }
        return distanceToListBottom > safeGetDimensionPixelSize(baseR.dimen.main_album_fragment_recycler_view_padding_bottom)
    }

    override fun getToolbarTitle(presentationType: String?): String {
        return context?.getString(baseR.string.main_fragment_title_all_album) ?: TextUtil.EMPTY_STRING
    }

    override fun getToolbarTitleColor(): Int? {
        val contextNonNull = context ?: return null
        return contextNonNull.getColor(
            if (isTopImmersiveCurrently()) {
                com.support.appcompat.R.color.coui_color_label_primary_dark
            } else {
                com.support.appcompat.R.color.coui_color_label_primary_light
            }
        )
    }

    override fun getToolbarTitleAppearanceType(presentationType: String?): Int = MainTabToolbar.TYPE_TITLE_TEXT_APPEARANCE_PRIMARY

    override fun getToolbarSubtitle(presentationType: String?): String = TextUtil.EMPTY_STRING

    override fun onTopSearchClick() = goToSearchActivity()

    override fun onTopOverflowClick() = showOverFlowMenu()

    private fun showOverFlowMenu() {
        val contextNonNull = context ?: return
        val overflowButtonNonNull = overflowButtonWrapperView?.getButton<MultiLayerImageView>() ?: return
        if (activity.isActivityInvalid()) {
            return
        }

        popupListWindow?.let {
            if (it.isShowing) {
                it.dismiss()
            } else {
                it.show(overflowButtonNonNull)
                updateIconPressedEffect(
                    overflowButtonNonNull,
                    isPressed = true
                )
            }
            return
        }

        val itemList = mutableListOf(
            OverflowMenuItem.ITEM_SETTING
        ).map { item ->
            PopupListItem.Builder()
                .setId(item.id)
                .setTitle(contextNonNull.getString(item.titleRes))
                .build()
        }

        // 弹出下拉弹窗
        popupListWindow = COUIPopupListWindow(contextNonNull).apply {
            this.itemList = itemList
            setOnItemClickListener { _, _, position, _ ->
                if (this.isShowing && this.itemList?.get(position)?.hasSubMenu() != true) {
                    dismiss()
                }
                when (itemList[position].id) {
                    OverflowMenuItem.ITEM_SETTING.id -> goToSettings()
                    else -> {}
                }
            }
            setUseBackgroundBlur(true)
            show(overflowButtonNonNull)
            updateIconPressedEffect(overflowButtonNonNull, isPressed = true)
            setOnDismissListener {
                updateIconPressedEffect(
                    overflowButtonNonNull,
                    isPressed = false
                )
            }
        }
    }

    /**
     * 顶部“更多”、底部“筛选”图标按压效果实现
     *
     * @param isPressed 是否按压态
     * @param iconView 图标View
     */
    private fun updateIconPressedEffect(iconView: ImageView?, isPressed: Boolean) {
        if ((iconView is MultiLayerImageView).not()) {
            GLog.w(TAG, DL) { "[updateIconPressedEffect] Icon view is not MultiLayerImageView!" }
            return
        }
        (iconView as MultiLayerImageView).updatePressEffect(isPressed)
    }

    /**
     * 跳转搜索页
     */
    private fun goToSearchActivity() {
        Starter.ActivityStarter(context, Bundle().apply {
            putString(IntentConstant.SearchConstant.KEY_JUMP_SOURCE, SearchTrackConstant.Value.USER_FROM_ALBUM)
        }, PostCard(RouterConstants.RouterName.SEARCH_ACTIVITY)).start()
    }

    /**
     * 跳转设置页
     */
    private fun goToSettings() {
        /**
         * MarkBy TangHui： trackPath 依赖图集页实现，待 @zhongwenren  补全，这里先传一个类名字符串
         *
         * 原逻辑这里，需要传入ViewModel的trackPath，依赖图集页实现，
         * val trackCallerEntry = TrackCallerEntry(trackPage, baseListViewModel?.trackPath)
         */
        val trackCallerEntry = TrackCallerEntry(trackPage, this::class.java.simpleName)
        activity?.let {
            MenuActionGetter.goToSetting.builder
                .setActivity(it)
                .setTrackCallerEntry(trackCallerEntry)
                .build().apply {
                    MenuOperationManager.doAction(MenuAction.GO_TO_SETTING, this)
                }
        }
    }

    override fun onMenuClick(id: Int) {
        when (id) {
            baseR.id.albumSortWrapperView -> doSortAction()
            baseR.id.albumAddWrapperView -> doAddNewAction()
            else -> GLog.w(TAG, DL) { "[onMenuClick] Invalid view id!" }
        }
    }

    /**
     * 图集排序
     */
    private fun doSortAction() {
        Starter.DialogFragmentStarter<PanelDialog>(
            fm = parentFragmentManager,
            bundle = Bundle(),
            postCard = PostCard(RouterConstants.RouterName.ALBUM_GROUP_SORT_DIALOG),
        ).start()
    }

    /**
     * 新建图集或新建共享图集
     */
    private fun doAddNewAction() {
        if (DoubleClickUtils.isFastDoubleClick(CLICK_INTERVAL)) return

        /**
         * MarkBy TangHui：
         *
         * trackPath 依赖图集页实现，这里先传一个类名字符串
         * - 原逻辑这里，需要传入ViewModel的trackPath，依赖图集页实现，
         *   val trackCallerEntry = TrackCallerEntry(trackPage, baseListViewModel?.trackPath)
         */
        val trackCallerEntry = TrackCallerEntry(trackPage, this::class.java.simpleName)

        // 未开启共享图集
        if (isSharedAlbumEntranceShow.not()) {
            doCreateAlbumAction(trackCallerEntry)
            return
        }

        // 已开启共享图集
        addNewButton?.getButton<MultiLayerImageView>()?.let {
            newCreateAlbumMenu?.onMenuItemClick = { index ->
                when (index) {
                    NewCreateAlbumMenu.GROUP_FIRST_INDEX_CREATE_SHARE -> doCreateShareAlbum(trackCallerEntry)

                    NewCreateAlbumMenu.GROUP_FIRST_INDEX_CREATE_ALBUM -> doCreateAlbumAction(trackCallerEntry)

                    else -> GLog.w(TAG, DL) { "[doAddNewAction] Invalid add new index!" }
                }
            }

            newCreateAlbumMenu?.onMenuShowOrDismiss = { isShow ->
                updateIconPressedEffect(it, isPressed = isShow)
            }
            newCreateAlbumMenu?.showMenu(it)
        }
    }

    /**
     * 新建图集
     */
    private fun doCreateAlbumAction(trackCallerEntry: TrackCallerEntry) {
        MenuOperationManager.doAction(
            MenuAction.CREATE_ALBUM,
            MenuActionGetter.createAlbum.builder
                .setFragment(this@MainTabAlbumSetFragment)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            { result, resultMap: Map<String, Any>? ->
                GLog.d(TAG, DL) { "[doCreateAlbumAction] result = $result, resultMap = ${resultMap?.size}" }
            }
        )
    }

    /**
     * 新建共享图集
     */
    private fun doCreateShareAlbum(trackCallerEntry: TrackCallerEntry) {
        MenuOperationManager.doAction(
            MenuAction.CREATE_SHARE_ALBUM,
            MenuActionGetter.createShareAlbum.builder
                .setActivity(<EMAIL> as? BaseActivity)
                .setTrackCallerEntry(trackCallerEntry)
                .build(),
            { result, resultMap: Map<String, Any>? ->
                GLog.d(TAG, DL) { "[doCreateShareAlbum] result = $result, resultMap = ${resultMap?.size}" }
            }
        )
    }

    override fun getEmptyPageTitleRes(): Int = baseR.string.base_no_album_tips

    override fun isSupportEmptyPage(): Boolean = true

    override fun isSupportDrop() = DragHelper.isSupportDrop()

    override fun getLayoutId(): Int = R.layout.albumset_tab_fragment

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : FragmentSystemBarStyle(this) {
        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            if (isForeground) {
                setStatusBarAppearance(isStatusBarLightAppearance())
                sidePaneAccessor.adjustControlIconColor(context, currentShouldToolbarTint())
            }
            updateRecyclerViewPadding(windowInsets.naviBarInsets())
        }
    }

    /**
     * 更新RecyclerView底部底部padding
     *
     * - 设计为距离底部Tab顶部 40dp，需要适配导航栏的高度，虚拟键和全屏手势二者高度存在差异，虚拟键时底部Tab栏位置被往上顶，
     * 另外还要考虑，虚拟键时，RecycleView的底部margin也有变化，因此需要虚拟键下需要弥补的padding为 ：Tab栏高度 - Tab栏top_padding
     */
    private fun updateRecyclerViewPadding(naviBarInsets: Insets) {
        val bottomNaviHeight = safeGetDimensionPixelSize(baseR.dimen.common_bottom_navigation_menu_height)
        val bottomBarHeight: Int = when {
            // 全屏手势(手势指示条显示)
            isFullScreenGesture -> bottomNaviHeight + naviBarInsets.bottom
            // 全屏手势(手势指示条隐藏)
            isFullScreenGestureAndNoBar -> bottomNaviHeight + safeGetDimensionPixelSize(baseR.dimen.main_tab_bottom_bar_margin_bottom_extra)
            // 虚拟键
            else -> naviBarInsets.bottom
        }

        val bottomPadding = safeGetDimensionPixelSize(baseR.dimen.main_album_fragment_recycler_view_padding_bottom)
        val bottomPaddingExtra = if (hasVirtualKey()) {
            safeGetDimensionPixelSize(baseR.dimen.main_album_fragment_recycler_view_padding_bottom_extra)
        } else {
            0
        }
        val topPadding = safeGetDimensionPixelSize(supportR.dimen.toolbar_min_height) + statusBarHeight()
        recyclerView?.updatePadding(top = topPadding, bottom = bottomBarHeight + bottomPadding + bottomPaddingExtra)
    }

    private fun getAlbumTypeForTrack(viewData: AlbumViewData): String {
        if (orderedBottomVirtualAlbumIds.contains(viewData.albumId)) {
            if (viewData.albumId == RECYCLE_BIN_ALBUM_ID) {
                return ALBUMS_ACTION_RECYCLE_ALBUM_VALUE
            }
            return ALBUMS_ACTION_MEDIA_ALBUM_VALUE
        }
        val isSelfAlbum = viewData.supportedAbilities?.getBoolean(AlbumViewData.SUPPORT_IS_SELF_ALBUM, false) ?: false
        val bucketPath =
            viewData.supportedAbilities?.getString(SlotOverlayHelper.SUPPORT_BUCKET_PATH, TextUtil.EMPTY_STRING) ?: TextUtil.EMPTY_STRING
        return if (isSelfAlbum) {
            AlbumsActionTackConstant.Value.ALBUMS_ACTION_USER_CREATE_ALBUM_VALUE
        } else if (bucketPath.isNotBlank() && !bucketPath.startsWith(Dir.DCIM.bucketPath, true)) {
            AlbumsActionTackConstant.Value.ALBUMS_ACTION_NON_SYSTEM_ALBUM_VALUE
        } else {
            AlbumsActionTackConstant.Value.ALBUMS_ACTION_SYSTEM_ALBUM_VALUE
        }
    }

    /**
     * 跳转到常用图集编辑
     */
    private fun onAdjustButtonClick() {
        GLog.d(TAG, DL) { "onAdjustButtonClick" }
        val mActivity = activity ?: return
        startByStack<EditShortcutAlbumSetFragment>(
            resId = mActivity.findFullScreenContainerId(),
            postCard = PostCard(RouterConstants.RouterName.EDIT_SHORTCUT_ALBUM_SET_FRAGMENT),
            anim = DEFAULT_ANIM_ARRAY,
            data = Bundle().apply {
                putBoolean(IntentConstant.AlbumConstant.KEY_HIDE_INTERNAL_TOOLBAR, true)
            }
        )
    }

    private fun startCommonAlbum(
        viewData: AlbumViewData,
        clickType: Int = CLICK_AREA_ITEM_VIEW,
        clickedItemView: View? = null,
        entrance: Int = ENTRANCE_NORMAL,
        needTrack: Boolean = true,
        radius: Float = 0f
    ) {
        if (needTrack) {
            clickAlbumTrack(viewData)
        }
        val routerName = getRouterName(viewData)
        val floatingWindowOffset = IntArray(2)
        if (isFloatingWindowMode()) view?.rootView?.getLocationOnScreen(floatingWindowOffset)
        val animArray = if ((clickType == FOOTER_MEDIA_TYPE) || (clickType == FOOTER_MORE_PROJECT) || clickedItemView == null) {
            DEFAULT_ANIM_ARRAY
        } else {
            SEAMLESS_ANIM_ARRAY
        }
        startByStack<BaseAlbumFragment>(
            postCard = PostCard(routerName),
            data = Bundle().apply {
                val isSelfAlbum = viewData.supportedAbilities?.getBoolean(AlbumViewData.SUPPORT_IS_SELF_ALBUM) ?: false
                val isThirdAppAlbum = viewData.supportedAbilities?.getBoolean(AlbumViewData.SUPPORT_IS_THIRD_APP_ALBUM) ?: false
                if (isSelfAlbum) {
                    // 用于数据埋点,自建图集
                    putString(ALBUM_NAVIGATION_TRACK_PAGE_ID, LaunchExitPopupConstant.Value.CUR_PAGE_USER_CREATE_ALBUM)
                } else if (isThirdAppAlbum) {
                    putString(ALBUM_NAVIGATION_TRACK_PAGE_ID, LaunchExitPopupConstant.Value.CUR_PAGE_THIRD_APP_ALBUM)
                } else if (orderedBottomVirtualAlbumIds.contains(viewData.albumId)) {
                    putString(ALBUM_NAVIGATION_TRACK_PAGE_ID, CUR_PAGE_MEDIA_ALBUM)
                    if (viewData.albumId == CSHOT_ALBUM_ID) {
                        putBundle(KEY_ALBUM_MODEL_BUNDLE, VirtualEntryBuilder.getCShotModelBundle(context))
                    }
                }
                putParcelable(KEY_ALBUM_VIEW_DATA, viewData)
                FragmentTransitionParameterHelper.putTransitionParameterInBundle(
                    context,
                    this,
                    clickedItemView,
                    TriggerViewRectGetter(clickedItemView, floatingWindowOffset, getToolBarBottomY()),
                    entrance,
                    radius
                )
            },
            anim = animArray
        )
    }

    private fun getToolBarBottomY(): Float {
        return mainTabController?.getToolbarBottomY()?.toFloat() ?: 0f
    }

    private fun getRouterName(viewData: AlbumViewData) = when (viewData.albumId) {
        RECYCLE_BIN_ALBUM_ID -> RouterConstants.RouterName.RECYCLE_FRAGMENT
        else -> RouterConstants.RouterName.ALBUM_FRAGMENT
    }

    /**
     * 当前 Fragment 是被 TabFragment 包裹，转场动效是回调的 TabFragment 的 onCreateAnimation() 方法
     * 所以这里需要修改 parentFragment 中记录的动效的方向信息
     */
    private fun resetSeamlessRootNodeOrientationState() {
        (parentFragment as? BaseFragment)?.resetSeamlessAnimRootNodeConfig(
            resources.configuration.orientation,
            resources.configuration.screenWidthDp,
            resources.configuration.screenHeightDp
        )
    }

    private fun startSpecialAlbumIfNeed(viewData: AlbumViewData, clickType: Int, itemView: View?, radius: Float): Boolean {
        if (CardCaseUtils.isCardIDAlbum(viewData.albumId)) {
            resetSeamlessRootNodeOrientationState()
            // 卡证档案,普通随身卡包的albumId 也是CARD_CASE_ALBUM_ID,所以不可通过此来判断
            enterCardCaseAlbum(
                this,
                itemView,
                radius,
                getToolBarBottomY()
            )
            return true
        }
        if (viewData.albumId == MAP_ALBUM_ID) {
            // 地图图集点击跳转页面实现
            enterMapLocationAlbum(this, itemView, radius, getToolBarBottomY())
            return true
        }
        when (viewData.albumId) {
            //最近删除
            RECYCLE_BIN_ALBUM_ID -> startRecyclerAlbumDecryptIfNeed(viewData, clickType, itemView, radius)
            //私密图集
            SAFE_BOX_ALBUM_ID -> activity?.let { onClickSafeBoxMenuEntry(it) }
            //清理建议
            CLEAN_UP_ALBUM_ID -> activity?.let { PhotoCleanHelper.startPhotoClean(it) }
            else -> return false
        }
        return true
    }

    private fun clickAlbumTrack(viewData: AlbumViewData) {
        AlbumsActionTrackHelper.trackAndSendAlbumsClick(
            currentPage = getUserActionCurPage(AlbumsActionTackConstant.TYPE_ALBUMS_ACTION),
            path = viewData.id,
            albumName = viewData.title,
            albumType = getAlbumTypeForTrack(viewData)
        )
        AlbumsActionTrackHelper.trackAndSendAlbumsAction(
            albumName = viewData.title,
            totalCount = viewData.totalCount.toString()
        )
    }

    /**
     * 地图详情页面
     * */
    private fun enterMapLocationAlbum(fragment: BaseFragment?, itemView: View?, radius: Float, toolBarBottomY: Float = 0f) {
        val mapJumper = MapPageJumper()
        //这里是处理从常用图集/我的图集 点击进入地图旅程页面的逻辑
        mapJumper.enterMapLocationAlbum(fragment, itemView = itemView, radius = radius, toolBarBottomY = toolBarBottomY)
    }

    /**
     * 添加侧边栏监听器
     */
    override fun createSidePaneListener(): ISidePaneListener = this

    /**
     * 侧边栏开始发生改变
     */
    override fun onSidePaneSlideStart(newState: SidePaneState) {
        shortcutAlbumSetViewDataBinding?.onSidePaneSlideStart(newState)
        // 侧边栏展开收起时，需要更新图集项宽度
        myAlbumsViewDataBinding?.notifyLayoutChanged(true)
        footerMediaGroupVDB?.onSidePaneSlideStart(newState)
        footerMoreTypeVDB?.onSidePaneSlideStart(newState)
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        shortcutAlbumSetViewDataBinding?.onSidePaneSliding(newState, slideProgress)
        footerMediaGroupVDB?.onSidePaneSliding(newState, slideProgress)
        footerMoreTypeVDB?.onSidePaneSliding(newState, slideProgress)
    }

    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        shortcutAlbumSetViewDataBinding?.onSidePaneSlideEnd(newState)
        footerMediaGroupVDB?.onSidePaneSlideEnd(newState)
        footerMoreTypeVDB?.onSidePaneSlideEnd(newState)
    }

    /**
     * BUG:9294081
     * 现象：在折叠屏内屏打开相册，回到外屏，查看相册，再回到内屏，发现两次查看内屏的【我的图集】的图集项宽度不一样
     * 原因：图集tab页的onAppUIConfigChanged方法发生在更新宽度之前，拿到了错误的宽度
     * 解决：在onSidePaneWidthChanged中更新我的图集模块的布局
     * @param newWidth 侧边栏宽度
     */
    override fun onSidePaneWidthChanged(newWidth: Int) {
        super.onSidePaneWidthChanged(newWidth)
        myAlbumsViewDataBinding?.notifyLayoutChanged(true)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        SafeBoxHelper.registerSafeBoxSwitchChangeListener(safeBoxSwitchChangeListener)
    }

    override fun onDetach() {
        super.onDetach()
        SafeBoxHelper.unRegisterSafeBoxSwitchChangeListener(safeBoxSwitchChangeListener)
    }

    private fun Context.isValidActivity(): Boolean {
        return if (this is Activity) {
            !isFinishing && !isDestroyed
        } else {
            false
        }
    }

    private fun safeGetDimensionPixelSize(resId: Int): Int {
        return context?.resources?.getDimensionPixelSize(resId) ?: 0
    }

    /**
     * 在当前tab页，点击当前tab页的回调
     */
    override fun onClickSelectedTabAgain() {
        onStatusBarClicked()
    }

    override fun supportClickStatusBar(): Boolean = true

    override fun onStatusBarClicked() {
        recyclerView?.let { recyclerView ->
            val firstVisiblePosition = recyclerView.layoutManager?.findFirstVisiblePosition() ?: 0
            //先直接跳转到当前可见的第一个 Item
            recyclerView.scrollToPosition(firstVisiblePosition.coerceAtMost(recyclerView.childCount))
            recyclerView.post {
                // 再使用动画平滑滚动到顶部
                recyclerView.smoothScrollToPosition(0)
            }
        }
    }

    private fun initSdkAndTriggerCoverLoadIfNeed() {
        GLog.d(TAG, LogFlag.DL, "initSdkAndTriggerCoverLoadIfNeed, isFirstTimeOnResumeCalled: $isFirstTimeOnResumeCalled")
        val needTriggerCoverLoad = isFirstTimeOnResumeCalled
        dynamicMapCover?.let {
            val isMapSdkInited = it.isMapSdkInited(context?.applicationContext)
            if (isMapSdkInited && !needTriggerCoverLoad) {
                GLog.d(TAG, LogFlag.DL, "initSdkAndTriggerCoverLoadIfNeed, jump initSdkAndTriggerCoverLoad")
                return@let
            }
            lifecycleScope.launch(Dispatchers.IO) {
                it.initSdkAndTriggerCoverLoad(needTriggerCoverLoad)
            }
        }
        isFirstTimeOnResumeCalled = false
    }

    companion object {
        private const val TAG = "MainTabAlbumSetFragment"

        /**
         * MarkBy TangHui：原悬浮按钮点击间隔设置的500，这里先照搬，待图集开发的同学确认 @zhongwenren
         */
        private const val CLICK_INTERVAL = 500
    }
}