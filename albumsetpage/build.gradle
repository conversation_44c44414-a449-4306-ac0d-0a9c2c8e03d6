plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}

apply from: "${rootDir}/gradle/pageCommon.gradle"

// 这部分一定要在android的前面
ext {
    mavenDescription = "相册图集TAB页业务"
}

android {
    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion
    resourcePrefix project.name + "_"
    namespace 'com.oplus.gallery.albumsetpage'

    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            matchingFallbacks = ['release', 'debug']
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            matchingFallbacks = ['release']
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    //忽略，olint扫描项:代码混淆风险
    lintOptions {
        disable  "MinifyEnabled"
    }
}

dependencies {
    implementation project(':foundation:libeffect')
    implementation fileTree(dir: "libs", include: ["*.jar"])

    kapt "com.oplus.gallery.router_lib:compiler:$routerCompilerVersion"
    implementation "com.oplus.gallery.router_lib:annotations:$routerAnnotationsVersion"
    implementation "androidx.core:core-ktx:$coreKtxVersion"
    implementation "androidx.appcompat:appcompat:$appcompatVersion"
    implementation "androidx.viewpager2:viewpager2:$viewpager2Version"
    implementation "com.oplus.appprovider:settings-compat:${appproviderSettingsCompatVersion}"
    implementationProject(':foundation:libauthorizing')
    implementationProject(':foundation:libcodec')
    implementationProject(':foundation:libcache')
    implementationProject(':foundation:libdbaccess')
    implementationProject(':foundation:libeffect')
    implementationProject(':foundation:libgstartup')
    implementationProject(':foundation:librouter')
    implementationProject(':foundation:libtaskscheduling')
    implementationProject(':foundation:libtracing')
    implementationProject(':foundation:libui')
    implementationProject(':foundation:libuikit')
    implementationProject(':foundation:libutil')
    implementationProject(':foundation:libtransform')
    implementationProject(':framework:business_lib')
    implementationProject(':foundation:libauthorizing')

    implementationProject(':framework')
    implementationProject(':framework:abilityapi')
    implementationProject(':framework:transformability')
    implementationProject(':framework:authorizingability')
    implementationProject(':basebiz')

    //Marked by zhangwenming 将data部分及相关依赖临时迁移到framework层而添加，后面整改时，需要去掉。
    implementationProject(':framework:dataabilitytmp')
    implementationProject(':framework:draganddropability')
}