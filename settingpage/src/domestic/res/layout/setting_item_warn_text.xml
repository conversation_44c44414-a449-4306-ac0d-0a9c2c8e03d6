<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:coui="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coui_preference"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center_vertical"
    android:minHeight="@dimen/support_preference_min_height"
    android:paddingStart="@dimen/support_preference_title_padding_start"
    android:paddingEnd="@dimen/support_preference_title_padding_end">

    <TextView
        android:id="@android:id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/setting_pip_title_text_size"
        style="@style/mediumFontTextStyle" />

</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>