<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/common_app_name">

    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory >
        <com.coui.appcompat.preference.COUIPreference
            android:defaultValue="false"
            app:isBackgroundAnimationEnabled="false"
            android:key="pref_key_information_share_with_function_content"
            app:isPreferenceVisible="false"
            app:couiShowDivider="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIBottomPreference />
</androidx.preference.PreferenceScreen>