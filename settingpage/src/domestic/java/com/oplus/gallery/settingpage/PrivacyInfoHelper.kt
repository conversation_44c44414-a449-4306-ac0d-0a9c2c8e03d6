/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: -PrivacyInfoHelper.kt
 ** Description:  个人信息明示清单辅助类
 ** Version: 1.0
 ** Date : 2024-09-30 10:30:10
 ** Author: w9023870  lixu
 **
 ** ---------------------Revision History: ---------------------
 ** w9023870  lixu       2024-09-30  1.0     build this module
 ****************************************************************/
package com.oplus.gallery.settingpage

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.customer.feedback.sdk.model.RequestData
import com.heytap.usercenter.accountsdk.AccountAgent
import com.oplus.gallery.basebiz.R
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.start
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.privacy.FeedBackRequestDataType
import com.oplus.gallery.framework.abilities.privacy.PrivacyDetailData
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.APP_CODE
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_AAID
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_BURIED_INFORMATION
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_APP_VERSION
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_BRAND
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_IDENTIFY_CODE
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_LANGUAGE_AND_REGIONAL_SETTINGS
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_OPERATOR_INFORMATION
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_OS_VERSION
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_ROM_VERSION
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_DEVICE_TYPE
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_ERROR_LOG_REPORT
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_EXPRESSAGE_TEL_NUMBER
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_FACE
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_FEEDBACK_CONTEXT_ATTACHMENT
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_FILE
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_LOCATION
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_NAME
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_OAID
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_PHONE_TYPE
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_TEXT
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_TOKEN
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.KEY_UDID
import com.oplus.gallery.framework.abilities.privacy.PrivacyPersonalInfoHelper.SLASHES_CONTENT
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard

/**
 * 个人信息明示清单辅助类
 */
object PrivacyInfoHelper {
    /**
     * 是否显示功能更新与素材资源下载内容
     */
    const val KEY_HAS_INFORMATION = "key_has_information"

    /**
     * 收集个人信息明示清单列表的keyId
     */
    const val KEY_PRIVACY_INFO_COLLECT_ITEM_ID = "key_privacy_info_collect_item_id"

    private const val SEMICOLON_CN = "；" // 分号，使用中文符号
    private const val TAG = "PrivacyInfoHelper"

    private val isRealme: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REALME_BRAND, false, false)
    }

    /**
     * 获取收集信息说明的相关描述
     */
    @JvmStatic
    fun getPrivacyInfoDetailDatas(context: Context?): Map<String, PrivacyInfoDetailData> {
        val resources = context?.resources ?: return mapOf()
        val questionContact = resources.getString(R.string.question_contact)
        val helpAndFeedback = resources.getString(R.string.help_and_feedback)
        val improvePicture = resources.getString(R.string.improve_picture)
        val repairPicture = resources.getString(R.string.repair_picture)
        val userEditorFunction = resources.getString(R.string.user_editor_function)
        val locationInformationUse = resources.getString(R.string.location_information_use)
        val locationDisplay = resources.getString(R.string.location_display)
        val userManagerPicture = resources.getString(R.string.user_manager_picture)
        val duidPurpose = resources.getString(R.string.duid_purpose)
        val duidCase = resources.getString(R.string.duid_case)
        val deviceBrandPurpose = resources.getString(R.string.device_brand_purpose)
        val deviceBrandCase = resources.getString(R.string.device_brand_case)
        val devicePurpose = resources.getString(R.string.device_purpose)
        val deviceCase = resources.getString(R.string.device_case)
        val romPurpose = resources.getString(R.string.rom_purpose)
        val romCase = resources.getString(R.string.rom_case)
        val appVersionPurpose = resources.getString(R.string.app_version_purpose)
        val appVersionCase = resources.getString(R.string.app_version_case)
        val textInPictureConvert = resources.getString(R.string.text_in_picture_convert)
        val superTextIdentify = resources.getString(R.string.super_text_identify)
        val longPressTextIdentify = resources.getString(R.string.long_press_text_identify)
        val pictureUserPurpose = if (isRealme) {
            resources.getString(R.string.realme_picture_user_purpose)
        } else {
            resources.getString(R.string.picture_user_purpose)
        }
        val aiUserName = if (isRealme) {
            resources.getString(R.string.picture_user_ai_aihd_realme)
        } else {
            resources.getString(R.string.picture_user_ai_common)
        }
        val aiUserAndCommon = if (isRealme) {
            resources.getString(R.string.realme_picture_user_ai_user, aiUserName)
        } else {
            resources.getString(R.string.picture_user_ai_user_update, aiUserName)
        }
        val pictureUserAction = resources.getString(R.string.picture_user_action)
        val analysisWithHelp = resources.getString(R.string.analysis_with_help)
        val purposeInviteContact = resources.getString(R.string.purpose_invite_contact)
        val sceneShareAlbumSet = resources.getString(R.string.scene_share_album_set)
        val purposeDeviceIdentification = resources.getString(R.string.purpose_device_identification)
        val sceneAiRepair = resources.getString(R.string.scene_ai_repair)
        val purposeToken = resources.getString(R.string.purpose_token)
        val sceneToken = resources.getString(R.string.scene_token)
        val duidPurpose2 = resources.getString(R.string.duid_purpose_2)
        val duidCase2 = resources.getString(R.string.duid_case_2)
        val purposePhoneNumber = resources.getString(R.string.purpose_phone_number)
        val scenePhoneNumber = resources.getString(R.string.scene_phone_number)
        return mapOf(
            KEY_EXPRESSAGE_TEL_NUMBER to PrivacyInfoDetailData(purposePhoneNumber, scenePhoneNumber, personalInformationContent = SLASHES_CONTENT),
            KEY_NAME to PrivacyInfoDetailData(purposeInviteContact, sceneShareAlbumSet, personalInformationContent = SLASHES_CONTENT),
            KEY_FACE to PrivacyInfoDetailData(improvePicture, repairPicture, userEditorFunction),
            KEY_LOCATION to PrivacyInfoDetailData(locationInformationUse, locationDisplay, userManagerPicture),
            KEY_DEVICE_IDENTIFY_CODE to PrivacyInfoDetailData(duidPurpose2, duidCase2),
            KEY_DEVICE_BRAND to PrivacyInfoDetailData(deviceBrandPurpose, deviceBrandCase),
            KEY_DEVICE_OS_VERSION to PrivacyInfoDetailData(duidPurpose, duidCase),
            KEY_PHONE_TYPE to PrivacyInfoDetailData(duidPurpose, duidCase),
            KEY_DEVICE_TYPE to PrivacyInfoDetailData(devicePurpose, deviceCase),
            KEY_DEVICE_ROM_VERSION to PrivacyInfoDetailData(romPurpose, romCase),
            KEY_DEVICE_APP_VERSION to PrivacyInfoDetailData(appVersionPurpose, appVersionCase),
            KEY_DEVICE_OPERATOR_INFORMATION to PrivacyInfoDetailData(appVersionPurpose, appVersionCase),
            KEY_DEVICE_LANGUAGE_AND_REGIONAL_SETTINGS to PrivacyInfoDetailData(appVersionPurpose, appVersionCase),
            KEY_TOKEN to PrivacyInfoDetailData(purposeToken, sceneToken, personalInformationContent = SLASHES_CONTENT),
            KEY_UDID to PrivacyInfoDetailData(purposeDeviceIdentification, sceneShareAlbumSet, personalInformationContent = SLASHES_CONTENT),
            KEY_OAID to PrivacyInfoDetailData(purposeDeviceIdentification, sceneShareAlbumSet, personalInformationContent = SLASHES_CONTENT),
            KEY_AAID to PrivacyInfoDetailData(purposeDeviceIdentification, sceneAiRepair, personalInformationContent = SLASHES_CONTENT),
            KEY_TEXT to PrivacyInfoDetailData(textInPictureConvert, superTextIdentify, longPressTextIdentify),
            KEY_FILE to PrivacyInfoDetailData(pictureUserPurpose, aiUserAndCommon, pictureUserAction),
            KEY_ERROR_LOG_REPORT to PrivacyInfoDetailData(analysisWithHelp, helpAndFeedback, personalInformationContent = SLASHES_CONTENT),
            KEY_BURIED_INFORMATION to PrivacyInfoDetailData(
                analysisWithHelp,
                helpAndFeedback,
                personalInformationContent = SLASHES_CONTENT
            ),
            KEY_FEEDBACK_CONTEXT_ATTACHMENT to PrivacyInfoDetailData(
                analysisWithHelp,
                helpAndFeedback,
                personalInformationContent = SLASHES_CONTENT
            )
        )
    }

    /**
     * 获取内容信息显示的内容情况
     */
    @JvmStatic
    fun getPrivacyInfoInformationContentDetailData(keyId: String?): PrivacyInfoInformationContentDetailData {
        return when (keyId) {
            // 设备品牌不显示：功能更新与素材资源下载、生成与地点相关的精彩回忆、我的照片
            KEY_DEVICE_BRAND -> {
                PrivacyInfoInformationContentDetailData(
                    showInformationShareWithFunction = false,
                    showInformationShareWithLocation = false,
                    showMainTimelineTabButtonMyPhoto = false
                )
            }
            // 设备型号不显示：我的反馈和我的照片
            KEY_DEVICE_TYPE -> {
                PrivacyInfoInformationContentDetailData(
                    showMyFeedback = false,
                    showMainTimelineTabButtonMyPhoto = false
                )
            }
            // ROM 版本不显示：我的反馈、共享图集
            KEY_DEVICE_ROM_VERSION -> PrivacyInfoInformationContentDetailData(showMyFeedback = false, showInformationShareNotice = false)
            //APP 版本/运营商信息/语言和地区设置不显示：我的反馈
            KEY_DEVICE_APP_VERSION,
            KEY_DEVICE_OPERATOR_INFORMATION,
            KEY_DEVICE_LANGUAGE_AND_REGIONAL_SETTINGS -> PrivacyInfoInformationContentDetailData(showMyFeedback = false)

            else -> PrivacyInfoInformationContentDetailData()
        }
    }

    /**
     * 跳转主界面
     */
    @JvmStatic
    fun startMainActivity(context: Context?) {
        context?.apply {
            Starter.ActivityStarter(
                startContext = this,
                postCard = PostCard(RouterConstants.RouterName.MAIN_ACTIVITY)
            ).start()
        }
    }

    /**
     *  跳转对应的Fragment界面
     */
    @JvmStatic
    fun startFragment(activity: BaseActivity, routerName: String, data: Bundle? = null): Fragment? {
        return activity.supportFragmentManager.start(
            resId = R.id.base_fragment_container,
            postCard = PostCard(routerName),
            data = data,
            fragmentStack = activity,
            addToBackStack = true
        )
    }

    /**
     * 跳转设置的账号界面
     */
    @JvmStatic
    fun startAccountSettingActivity(context: Context?) {
        context?.apply {
            AccountAgent.startAccountSettingActivity(this, APP_CODE)
        }
    }

    /**
     * 处理RequestData list 数据
     *
     * @param requestDataList List<RequestData>
     */
    @JvmStatic
    fun dealFeedbackRequestDataList(requestDataList: List<RequestData>) {
        requestDataList.forEach {
            when (it) {
                is RequestData.OpenId -> PrivacyPersonalInfoHelper.insertFeedBackDataNumberContentInIO(FeedBackRequestDataType.OPENID)
                is RequestData.Brand -> PrivacyPersonalInfoHelper.insertFeedBackDataNumberContentInIO(FeedBackRequestDataType.BRAND)
                is RequestData.Model -> PrivacyPersonalInfoHelper.insertFeedBackDataNumberContentInIO(FeedBackRequestDataType.MODEL)
                is RequestData.Os -> PrivacyPersonalInfoHelper.insertFeedBackDataNumberContentInIO(FeedBackRequestDataType.OS)
                is RequestData.Contact -> PrivacyPersonalInfoHelper.insertPhoneNumber(it.content)
                is RequestData.Log -> PrivacyPersonalInfoHelper.insertFeedBackDataNumberContentInIO(FeedBackRequestDataType.LOG)
                is RequestData.Statistics -> PrivacyPersonalInfoHelper.insertFeedBackDataNumberContentInIO(FeedBackRequestDataType.STATISTICS)
                is RequestData.Feedback -> PrivacyPersonalInfoHelper.insertFeedBackDataNumberContentInIO(FeedBackRequestDataType.FEEDBACK)
                else -> GLog.d(TAG, LogFlag.DL, "dealFeedbackRequestDataList, RequestData = $it")
            }
        }
    }

    /**
     * 获取功能更新与素材资源下载详情数据
     *
     * @param context 上下文
     * @param timePosition 选择的时间位置
     * @param keyId 个人信息明示清单列表的keyId
     */
    @JvmStatic
    fun getInformationShareWithFunctionSummary(context: Context?, timePosition: Int, keyId: String?): String {
        val dataList =
            PrivacyPersonalInfoHelper.queryInformationShareWithFunctionFileNameList(context, timePosition, keyId)
                ?: return TextUtil.EMPTY_STRING
        return getDataString(dataList)
    }

    /**
     * 获取手机号码详情数据
     *
     * @param context 上下文
     * @param timePosition 选择的时间位置
     */
    @JvmStatic
    fun getPhoneNumberSummary(context: Context?, timePosition: Int, limit: Int? = null): String {
        val dataList = PrivacyPersonalInfoHelper.queryPhoneNumberList(context, timePosition, limit) ?: return TextUtil.EMPTY_STRING
        return getDataString(dataList)
    }

    /**
     * 获取姓名详情数据
     *
     * @param context 上下文
     * @param timePosition 选择的时间位置
     */
    @JvmStatic
    fun getNameSummary(context: Context?, timePosition: Int, limit: Int? = null): String {
        val dataList = PrivacyPersonalInfoHelper.queryNameList(context, timePosition, limit) ?: return TextUtil.EMPTY_STRING
        return getDataString(dataList)
    }

    /**
     * 获取 Token 详情数据
     *
     * @param context 上下文
     * @param timePosition 选择的时间位置
     */
    @JvmStatic
    fun getTokenSummary(context: Context?, timePosition: Int, limit: Int? = null): String {
        val dataList = PrivacyPersonalInfoHelper.queryTokenList(context, timePosition, limit) ?: return TextUtil.EMPTY_STRING
        return getDataString(dataList)
    }

    /**
     * 获取 UDID 详情数据
     *
     * @param context 上下文
     * @param timePosition 选择的时间位置
     */
    @JvmStatic
    fun getUdidSummary(context: Context?, timePosition: Int, limit: Int? = null): String {
        val dataList = PrivacyPersonalInfoHelper.queryUdidList(context, timePosition, limit) ?: return TextUtil.EMPTY_STRING
        return getDataString(dataList)
    }

    /**
     * 获取 OAID 详情数据
     *
     * @param context 上下文
     * @param timePosition 选择的时间位置
     */
    @JvmStatic
    fun getOaidSummary(context: Context?, timePosition: Int, limit: Int? = null): String {
        val dataList = PrivacyPersonalInfoHelper.queryOaidList(context, timePosition, limit) ?: return TextUtil.EMPTY_STRING
        return getDataString(dataList)
    }

    /**
     * 获取 AAID 详情数据
     *
     * @param context 上下文
     * @param timePosition 选择的时间位置
     */
    @JvmStatic
    fun getAaidSummary(context: Context?, timePosition: Int, limit: Int? = null): String {
        val dataList = PrivacyPersonalInfoHelper.queryAaidList(context, timePosition, limit) ?: return TextUtil.EMPTY_STRING
        return getDataString(dataList)
    }

    private fun getDataString(dataList: List<PrivacyDetailData>): String {
        if (dataList.isEmpty()) {
            return TextUtil.EMPTY_STRING
        }
        val stringBuffer = StringBuffer()
        for (index in dataList.indices) {
            val value = dataList[index].value
            if (value.isEmpty()) continue
            stringBuffer.append(dataList[index].time)
            stringBuffer.append(TextUtil.BLANK_STRING)
            stringBuffer.append(value)
            stringBuffer.append(SEMICOLON_CN)
            // 如果index是最后一个数据，且不是同一天的数据，则需要加上两个换行符，造成空行的效果
            if ((index < dataList.size - 1) && (dataList[index].date != dataList[index + 1].date)
                && dataList[index + 1].value.isNotEmpty()) {
                stringBuffer.append("\r\n\r\n")
            }
        }
        return stringBuffer.toString()
    }
}