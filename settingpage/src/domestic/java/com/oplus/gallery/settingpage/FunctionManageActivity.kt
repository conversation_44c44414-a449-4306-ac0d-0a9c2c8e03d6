/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - FunctionSettingActivity.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/5/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2022/5/1        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.settingpage

import android.os.Bundle
import android.view.LayoutInflater
import androidx.lifecycle.lifecycleScope
import androidx.preference.Preference
import androidx.preference.SwitchPreference
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SETTING_FUNCTION
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.NaviPaddingActivitySystemBarStyle
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.business_lib.cloudsync.CloudSyncOpenEntrance
import com.oplus.gallery.business_lib.cloudsync.OnCloudSwitchListener
import com.oplus.gallery.business_lib.ui.fragment.BasePreferenceWithAppbarFragment
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.ui.systembar.ActivitySystemBarStyle
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.settingpage.function.FunctionConfig
import com.oplus.gallery.standard_lib.app.UI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@RouterNormal(SETTING_FUNCTION)
class FunctionManageActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(com.oplus.gallery.basebiz.R.layout.base_activity_common_fragment_layout)
        val functionSettingFragment = FunctionSettingFragment()
        supportFragmentManager.findFragmentByTag(FUNCTION_SETTING_FRAGMENT) ?: let {
            supportFragmentManager.beginTransaction().apply {
                replace(com.oplus.gallery.basebiz.R.id.settingContent, functionSettingFragment, FUNCTION_SETTING_FRAGMENT)
                commitAllowingStateLoss()
            }
        }
    }

    override fun getSystemBarStyle(): ActivitySystemBarStyle = NaviPaddingActivitySystemBarStyle(this)

    class FunctionSettingFragment : BasePreferenceWithAppbarFragment() {
        private val functionConfig by lazy { FunctionConfig() }
        private var cloudSwitchListener: OnCloudSwitchListener? = null

        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            addPreferencesFromResource(R.xml.setting_function)
        }

        override fun onResume() {
            super.onResume()
            updateCloudPreference()
        }

        override fun onDestroy() {
            super.onDestroy()
            ApiDmManager.getCloudSyncDM().unregisterSwitchListener(cloudSwitchListener)
        }

        override fun getTitle() = getString(R.string.setting_pref_function)

        private fun updateCloudPreference() {
            findPreference<SwitchPreference>(PREF_FUNCTION_CLOUD_SYNC)?.run {
                <EMAIL> = functionConfig.needShowCloudFunctionItem()
                isChecked = ApiDmManager.getCloudSyncDM().isCloudServiceFunctionEnable()
                onPreferenceChangeListener = object : Preference.OnPreferenceChangeListener {
                    override fun onPreferenceChange(preference: Preference?, newValue: Any?): Boolean {
                        val enable = newValue as? Boolean ?: return false
                        if (enable) {
                            setCloudFunctionEnable(preference, enable)
                        } else {
                            if (ApiDmManager.getCloudSyncDM().isAlbumSyncSwitchOpen() || ApiDmManager.getSharedSyncDM().isSharedAlbumSwitchOpen()) {
                                showDisableCloudFunctionDialog(preference)
                            } else {
                                setCloudFunctionEnable(preference, enable)
                            }
                        }
                        return false
                    }
                }
            }
        }

        private fun setCloudFunctionEnable(preference: Preference?, enable: Boolean) {
            lifecycleScope.launch(Dispatchers.UI) {
                (preference as? SwitchPreference)?.isChecked = enable
            }
            ApiDmManager.getCloudSyncDM().setCloudServiceFunctionEnable(enable)
        }

        private fun showDisableCloudFunctionDialog(preference: Preference?) {
            context?.let {
                ConfirmDialog.Builder(it).apply {
                    setView(LayoutInflater.from(it).inflate(R.layout.setting_disable_cloud_function_dialog, null))
                    setNeutralButton(
                        com.oplus.gallery.basebiz.R.string.common_close
                    ) { _, _ ->
                        handleCloudServiceClose(preference)
                    }
                    setNegativeButton(com.oplus.gallery.basebiz.R.string.common_cancel)
                    setTitle(R.string.setting_disable_cloud_service_dialog_title)
                    setCancelable(false)
                }.build().show()
            }
        }

        private fun handleCloudServiceClose(preference: Preference?) {
            closeCloudSync {
                closeSharedAlbum()
                setCloudFunctionEnable(preference, false)
            }
        }

        private fun closeCloudSync(callback: (() -> Unit)) {
            ApiDmManager.getCloudSyncDM().run {
                if (isAlbumSyncSwitchOpen()) {
                    /*
                      关闭云同步开关的情况，不一定就直接关闭了（本地有缩图的情况会有下载原图弹窗），需要等待开关关闭后再让功能开关修改生效
                     */
                    cloudSwitchListener = object : OnCloudSwitchListener {
                        override fun onCloudSwitchChange(
                            switchType: OnCloudSwitchListener.SwitchType,
                            open: Boolean
                        ) {
                            if ((switchType == OnCloudSwitchListener.SwitchType.ALBUM_SYNC) && !open) {
                                callback.invoke()
                                unregisterSwitchListener(this)
                                cloudSwitchListener = null
                            }
                        }
                    }.apply {
                        registerSwitchListener(this)
                    }
                    activity?.let {
                        setAlbumSyncSwitch(
                            it,
                            open = false,
                            CloudSyncOpenEntrance.SETTING,
                            showCloseConfirmDialog = false
                        )
                    }
                } else {
                    callback.invoke()
                }
            }
        }

        private fun closeSharedAlbum() {
            ApiDmManager.getSharedSyncDM().run {
                if (isSharedAlbumSwitchOpen()) {
                    setSharedAlbumSwitchDirectly(false)
                }
            }
        }

        companion object {
            private const val PREF_FUNCTION_CLOUD_SYNC = "pref_function_cloud_sync"
        }
    }

    companion object {
        private const val FUNCTION_SETTING_FRAGMENT = "FunctionSettingFragment"
    }
}