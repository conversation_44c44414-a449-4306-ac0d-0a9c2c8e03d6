/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - PrivacyPolicyViewContentImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>       2024/10/21        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.settingpage

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import com.coui.appcompat.snackbar.COUISnackBar
import com.coui.component.privacypolicy.COUIPrivacyPolicyView
import com.coui.component.privacypolicy.PrivacyPolicySpanBuilder
import com.coui.component.privacypolicy.body
import com.coui.component.privacypolicy.section
import com.coui.component.privacypolicy.sectionTitle
import com.coui.component.privacypolicy.smallTitle
import com.coui.component.privacypolicy.spanBuilder
import com.coui.component.privacypolicy.title
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.api.ApiDmManager
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.safeStartActivity
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_COMPOSITION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_PRIVACY_IS_SUPPORT_CAPTION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_SHOW_SPECIAL_COMPANY_NAME
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.standard_lib.file.File
import java.io.FileOutputStream

/**
 * 内销 个人信息保护政策 主体内容
 */
class PrivacyPolicyViewContentImpl(
    private val context: Context,
    private val view: View
) : PrivacyPolicyActivity.IPrivacyPolicyViewContent {

    private val privacyPolicyJavascriptInterface: PrivacyPolicyJavascriptInterface by lazy {
        PrivacyPolicyJavascriptInterface(context, Handler(Looper.getMainLooper()), view)
    }

    companion object {
        private const val TAG = "PrivacyPolicyViewContentImpl_Domestic"
        private val PRIVATE_POLICY_PARENT_FOLDER = OplusEnvironment.getInternalPath() + File.separator + "Documents"
        private const val MAX_SNACK_BAR_AUTO_DISAPPEAR_TIME = 2000
        private const val ACTION_FILE_MANAGER_BROWSER_FILE = "oppo.filemanager.intent.action.BROWSER_FILE"
        private const val EXTRA_FILE_MANAGER_BROWSER_FILE = "CurrentDir"
        private const val URL_PREFIX_HTTP = "http"
        private val isShowSpecialCompanyName = ConfigAbilityWrapper.getBoolean(IS_SHOW_SPECIAL_COMPANY_NAME)
        private val isRealmeBrand = ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND)
    }


    override fun getPrivacyContent(privacyPolicyView: COUIPrivacyPolicyView) {
        privacyPolicyView.apply {
            displayPrivatePolicyTitle(context)
            displayPrivatePolicyOne(context)
            displayPrivatePolicyTwo(context)
            displayPrivatePolicyThree(context)
            displayPrivatePolicyFour(context)
            displayPrivatePolicyFive(context)
            displayPrivatePolicySix(context)
            displayPrivatePolicySeven(context)
            displayPrivatePolicyEight(context)
            displayPrivatePolicyNine(context)
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicyTitle(context: Context) {
        title { context.getString(R.string.settings_privacy_statement_title_gallery) }
        section {
            body {
                val policySpanBuilder = context.getString(R.string.settings_privacy_statement_title_gallery01).spanBuilder(this)
                policySpanBuilder.append(context.getString(R.string.settings_privacy_statement_title_gallery02))
                policySpanBuilder.setLink(context.getString(R.string.settings_privacy_statement_title_gallery02)) {
                    privacyPolicyJavascriptInterface.download()
                }
            }
            if (isRealmeBrand) {
                body { context.getString(R.string.realme_settings_privacy_statement_title_gallery03) }
                body { context.getString(R.string.realme_settings_privacy_statement_title_gallery04) }
                body { context.getString(R.string.realme_settings_privacy_statement_title_gallery05) }
                body { context.getString(R.string.realme_settings_privacy_statement_title_gallery06) }
            } else {
                body { context.getString(R.string.settings_privacy_statement_title_gallery05) }
                body { context.getString(R.string.settings_privacy_statement_title_gallery06) }
            }
        }

        section {
            body { context.getString(R.string.settings_privacy_statement_update_title) }
            body { context.getString(R.string.settings_privacy_statement_update_content3) }
            body { context.getString(R.string.settings_privacy_statement_update_content4) }
        }

        section {
            body {

                String.format(
                    context.getString(R.string.settings_privacy_statement_title_gallery13), context.getString(
                        if (isRealmeBrand) {
                            R.string.settings_privacy_statement_013_002_gallery_realme
                        } else {
                            R.string.settings_privacy_statement_013_002_gallery
                        }
                    ), context.getString(R.string.settings_privacy_statement_title_gallery14)
                )
            }
            body {
                if (isRealmeBrand) {
                    context.getString(R.string.settings_privacy_statement_title_gallery15_realme)
                } else {
                    context.getString(R.string.settings_privacy_statement_title_gallery15)
                }
            }
            body {
                if (isRealmeBrand) {
                    StringBuilder(
                        context.getString(R.string.settings_privacy_statement_title_gallery16_realme)
                    )
                } else {
                    StringBuilder(
                        context.getString(R.string.settings_privacy_statement_title_gallery16)
                    )
                }
            }
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicyOne(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_1_0_0_0_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_1_0_0_0_gallery02) }
            smallTitle { context.getString(R.string.settings_privacy_statement_1_1_0_0_gallery01) }
            smallTitle { context.getString(R.string.settings_privacy_statement_1_2_0_0_gallery01) }
            smallTitle { context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery01) }
            subParagraph1()
            subParagraph2()
            subParagraph3()
            subParagraph4()
        }
    }

    private fun COUIPrivacyPolicyView.Section.subParagraph1() {
        smallTitle {
            val policySpanBuilder = context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery02).spanBuilder(this)
            policySpanBuilder.append(context.getString(R.string.settings_privacy_statement_collection_of_person_info_gallery))
                .append(context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery03))
                .append(context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery04))
            policySpanBuilder.setLink(context.getString(R.string.settings_privacy_statement_collection_of_person_info_gallery)) {
                privacyPolicyJavascriptInterface.goToCollectionOfPersonInfo()
            }
        }
        body {
            StringBuilder(context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery05))
                .append(context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery06))
                .append(context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery07))
        }
        body { context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery08) }
        body { context.getString(R.string.settings_privacy_statement_1_3_0_0_gallery09) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_0_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_0_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_0_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_0_gallery04) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_1_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_1_gallery02) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_2_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_2_gallery02) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_3_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_3_gallery02) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_5_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_5_gallery02) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_6_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_6_gallery02) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_7_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_7_gallery02) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_8_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_1_8_gallery02) }

        if (ConfigAbilityWrapper.getBoolean(FEATURE_PRIVACY_IS_SUPPORT_CAPTION)) {
            smallTitle { context.getString(R.string.settings_privacy_statement_1_3_1_9_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_1_3_1_9_gallery02) }
        }
    }

    private fun COUIPrivacyPolicyView.Section.subParagraph2() {
        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_0_gallery01) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_2_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_2_2_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_2_2_gallery03) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_3_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_2_3_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_2_3_gallery03) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_4_gallery01) }
        if (isRealmeBrand) {
            body { context.getString(R.string.realme_settings_privacy_statement_1_3_2_4_gallery02) }
            smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_5_gallery01) }
            body { context.resources.getString(R.string.realme_settings_privacy_statement_1_3_2_5_gallery02) }
        } else {
            body {
                context.getString(R.string.settings_privacy_statement_1_3_2_4_gallery02)
                    .spanBuilder(this)
                    .setBold(context.getString(R.string.settings_privacy_statement_1_3_2_4_gallery03))
            }

            smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_5_gallery01) }
            body {
                context.getString(R.string.settings_privacy_statement_1_3_2_5_gallery03)
                    .spanBuilder(this)
                    .setBold(context.getString(R.string.settings_privacy_statement_1_3_2_5_gallery04))
            }
        }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_6_gallery01) }
        body {
            context.getString(R.string.settings_privacy_statement_1_3_2_6_gallery03)
                .spanBuilder(this)
                .setBold(context.getString(R.string.settings_privacy_statement_1_3_2_6_gallery04))
                .setBold(context.getString(R.string.settings_privacy_statement_1_3_2_6_gallery05))
        }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_7_gallery01) }
        body {
            context.getString(R.string.settings_privacy_statement_1_3_2_7_gallery03)
                .spanBuilder(this)
                .setBold(context.getString(R.string.settings_privacy_statement_1_3_2_7_gallery04))
        }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_8_gallery01) }
        body {
            context.getString(R.string.settings_privacy_statement_1_3_2_8_gallery03)
                .spanBuilder(this)
                .setBold(context.getString(R.string.settings_privacy_statement_1_3_2_8_gallery04))
        }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery01) }
        body {
            //AI 是否支持AI构图功能
            if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_COMPOSITION)) {
                //AI 是否支持AI补光功能
                if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_LIGHTING)) {
                    context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03_v4)
                } else {
                    context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03_v2)
                }
            } else {
                //AI 是否支持AI补光功能
                if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_LIGHTING)) {
                    context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03_v3)
                } else {
                    context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03)
                }
            }
                .spanBuilder(this)
                .setBold(context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery04))
        }
    }

    private fun COUIPrivacyPolicyView.Section.subParagraph3() {
        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_3_0_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_3_0_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_3_1_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_3_1_gallery02) }
    }

    @Suppress("LongMethod")
    private fun COUIPrivacyPolicyView.Section.subParagraph4() {
        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_0_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_0_gallery02) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_1_gallery00) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_1_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_1_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_1_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_1_gallery04) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_1_gallery05) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_2_gallery00) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_2_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_2_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_2_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_2_gallery04) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_2_gallery05) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_3_gallery00) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_3_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_3_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_3_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_3_gallery04) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_3_gallery05) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_4_gallery00) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_4_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_4_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_4_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_4_gallery04) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_4_gallery05) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_4_gallery06) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery00) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery04) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery05) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery06) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery00) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery04) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery05) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery06) }

        smallTitle { context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery00) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery01) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery02) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery03) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery04) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery05) }
        body { context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery06) }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicyTwo(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_2_0_0_0_gallery01) }
            smallTitle { context.getString(R.string.settings_privacy_statement_2_1_0_0_gallery01) }
            body {
                StringBuilder(context.getString(R.string.settings_privacy_statement_2_1_0_0_gallery02))
                    .append(context.getString(R.string.settings_privacy_statement_2_1_0_0_gallery03))
            }

            smallTitle { context.getString(R.string.settings_privacy_statement_2_2_0_0_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_2_2_0_0_gallery02) }
            body { context.getString(R.string.settings_privacy_statement_2_2_0_0_gallery03) }

            smallTitle { context.getString(R.string.settings_privacy_statement_2_2_1_0_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_2_2_1_0_gallery02) }

            smallTitle { context.getString(R.string.settings_privacy_statement_2_2_2_0_gallery01) }


            smallTitle { context.getString(R.string.settings_privacy_statement_2_2_3_0_gallery01) }
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicyThree(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_3_0_0_0_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_3_0_0_0_gallery02) }
            body { context.getString(R.string.settings_privacy_statement_3_0_0_0_gallery03) }
            body {
                val policySpanBuilder = PrivacyPolicySpanBuilder(this, "")
                policySpanBuilder.append(context.getString(R.string.settings_privacy_statement_3_0_0_0_gallery04))
                    .append(context.getString(R.string.settings_privacy_statement_thrid_party_info_sharing_gallery))
                    .append(context.getString(R.string.settings_privacy_statement_3_0_0_0_gallery05))
                policySpanBuilder.setLink(context.getString(R.string.settings_privacy_statement_thrid_party_info_sharing_gallery)) {
                    privacyPolicyJavascriptInterface.goToThirdPartyInfoSharing()
                }
            }
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicyFour(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_4_0_0_0_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_4_0_0_0_gallery02) }
            body {
                StringBuilder(context.getString(R.string.settings_privacy_statement_4_0_0_0_gallery03))
                    .append(context.getString(R.string.settings_privacy_statement_4_0_0_0_gallery04))
            }
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicyFive(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery01) }
            body {
                StringBuilder(context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery02))
                    .append(context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery03))
                    .append(context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery04))
            }
            body { context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery05) }
            body { context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery06) }
            body { context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery07) }
            body { context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery08) }
            body { context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery09) }
            body { context.getString(R.string.settings_privacy_statement_5_0_0_0_gallery10) }
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicySix(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_6_0_0_0_gallery01) }
            body {
                StringBuilder(context.getString(R.string.settings_privacy_statement_6_0_0_0_gallery02))
                    .append(context.getString(R.string.settings_privacy_statement_6_0_0_0_gallery03))
            }
            body {
                StringBuilder(context.getString(R.string.settings_privacy_statement_6_0_0_0_gallery04))
                    .append(context.getString(R.string.settings_privacy_statement_6_0_0_0_gallery05))
            }
            body { context.getString(R.string.settings_privacy_statement_6_0_0_0_gallery06) }
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicySeven(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_7_0_0_0_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_7_0_0_0_gallery02) }
        }
    }

    private fun COUIPrivacyPolicyView.displayPrivatePolicyEight(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_8_0_0_0_gallery01) }
            body { context.getString(R.string.settings_privacy_statement_8_0_0_0_gallery02) }
            body { context.getString(R.string.settings_privacy_statement_8_0_0_0_gallery03) }
        }
    }

    @SuppressLint("StringFormatInvalid")
    @Suppress("LongMethod")
    private fun COUIPrivacyPolicyView.displayPrivatePolicyNine(context: Context) {
        section {
            sectionTitle { context.getString(R.string.settings_privacy_statement_9_0_0_0_gallery01) }
            body {
                String.format(
                    context.getString(R.string.settings_privacy_statement_9_0_0_0_gallery02),
                    context.getString(R.string.setting_pref_personanl_privacy_policy_title)
                ).spanBuilder(this)
                    .setLink(context.getString(R.string.setting_pref_personanl_privacy_policy_title)) {
                        privacyPolicyJavascriptInterface.goToSystemPrivacyPolicy()
                    }
            }
            body { context.getString(R.string.settings_privacy_statement_9_0_0_0_gallery05) }
            body {
                val linkStr = PrivacyLinkImpl().getDataPlatformLink()

                val resourceStringId = if (isRealmeBrand) {
                    R.string.settings_privacy_statement_130_gallery_realme
                } else {
                    R.string.settings_privacy_statement_130_gallery
                }

                val policySpanBuilder = String.format(
                    context.getString(resourceStringId), linkStr
                ).spanBuilder(this)

                policySpanBuilder.setWebLink(setupWizardDone = true, link = linkStr)
            }
            body { context.getString(R.string.settings_privacy_statement_9_0_0_0_gallery06) }
            body {
                context.getString(
                    if (isRealmeBrand) {
                        R.string.settings_privacy_statement_company_name_gallery_realme
                    } else {
                        R.string.settings_privacy_statement_company_name_gallery
                    }
                )
            }
            body {
                context.getString(
                    if (isRealmeBrand) {
                        R.string.settings_privacy_statement_company_address_gallery_realme
                    } else {
                        R.string.settings_privacy_statement_company_address_gallery
                    }
                )
            }
            body {
                context.getString(
                    if (isRealmeBrand) {
                        R.string.settings_privacy_statement_post_code_gallery_realme
                    } else {
                        R.string.settings_privacy_statement_post_code_gallery
                    }
                )
            }
        }
    }

    class PrivacyPolicyJavascriptInterface(
        val context: Context,
        private val handler: Handler,
        private val rootView: View?
    ) {
        fun download() {
            downloadPolicy()
        }

        private fun downloadPolicy() {
            val fileName =
                context.resources.getString(R.string.settings_privacy_statement_title_gallery) +
                        "_" +
                        context.resources.getString(co.polarr.processing.R.string.app_name) + ".txt"
            val filePath = PRIVATE_POLICY_PARENT_FOLDER + File.separator + fileName
            val policyFile = FileOperationUtils.createNewFile(filePath)
            if (policyFile != null) {
                val startTime = System.currentTimeMillis()
                FileOutputStream(filePath).apply {
                    writePrivatePolicyToFileStepOne(this)
                    writePrivatePolicyToFileStepOneExcess(this)
                    writePrivatePolicyToFileStepTwo(this)
                    writePrivatePolicyToFileStepThree(this)
                    writePrivatePolicyToFileStepFour(this)
                    writePrivatePolicyToFileStepFive(this)
                    handler.post {
                        showSnackBar(policyFile.parentFile)
                    }
                }
                GLog.d(TAG, LogFlag.DL) { "downloadPolicy cost time=" + (System.currentTimeMillis() - startTime) }
            }
        }

        private fun writePrivatePolicyToFileStepOne(file: FileOutputStream) {
            file.apply {
                write(context.resources.getString(R.string.settings_privacy_statement_title_gallery).toByteArray())
                write("\r\n".toByteArray())
                if (isRealmeBrand) {
                    write(context.resources.getString(R.string.realme_settings_privacy_statement_title_gallery03).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.realme_settings_privacy_statement_title_gallery04).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.realme_settings_privacy_statement_title_gallery05).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.realme_settings_privacy_statement_title_gallery06).toByteArray())
                    write("\r\n".toByteArray())
                } else {
                    write(context.resources.getString(R.string.settings_privacy_statement_title_gallery05).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.settings_privacy_statement_title_gallery06).toByteArray())
                    write("\r\n".toByteArray())
                }
                write(context.resources.getString(R.string.settings_privacy_statement_update_title).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_update_content3).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_update_content4).toByteArray())
                write("\r\n".toByteArray())
                val youAndUs =
                    String.format(
                        context.resources.getString(R.string.settings_privacy_statement_title_gallery13), context.resources.getString(
                            if (isRealmeBrand) {
                                R.string.settings_privacy_statement_013_002_gallery_realme
                            } else {
                                R.string.settings_privacy_statement_013_002_gallery
                            }
                        ), context.resources.getString(R.string.settings_privacy_statement_title_gallery14)
                    )
                write(youAndUs.toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_title_gallery14).toByteArray())
                write("\r\n".toByteArray())
                if (isRealmeBrand) {
                    write(context.resources.getString(R.string.settings_privacy_statement_title_gallery15_realme).toByteArray())
                } else {
                    write(context.resources.getString(R.string.settings_privacy_statement_title_gallery15).toByteArray())
                }
                write("\r\n".toByteArray())

                if (isRealmeBrand) {
                    write(context.resources.getString(R.string.settings_privacy_statement_title_gallery16_realme).toByteArray())
                } else {
                    write(context.resources.getString(R.string.settings_privacy_statement_title_gallery16).toByteArray())
                }
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_0_0_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_1_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_2_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
            }
        }

        private fun writePrivatePolicyToFileStepOneExcess(file: FileOutputStream) {
            file.apply {
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery02).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_collection_of_person_info_gallery).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery03).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery05).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery06).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery07).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery08).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_0_0_gallery09).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_0_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_0_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_1_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_1_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_2_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_2_gallery02).toByteArray())
                write("\r\n".toByteArray())
            }
        }

        private fun writePrivatePolicyToFileStepTwo(file: FileOutputStream) {
            file.apply {
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_3_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_3_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_5_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_5_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_6_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_6_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_7_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_7_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_8_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_8_gallery02).toByteArray())
                write("\r\n".toByteArray())
                if (ConfigAbilityWrapper.getBoolean(FEATURE_PRIVACY_IS_SUPPORT_CAPTION)) {
                    write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_9_gallery01).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.settings_privacy_statement_1_3_1_9_gallery02).toByteArray())
                    write("\r\n".toByteArray())
                }
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_2_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_2_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_2_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_3_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_3_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_3_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_4_gallery01).toByteArray())
                write("\r\n".toByteArray())
                if (isRealmeBrand) {
                    write(context.resources.getString(R.string.realme_settings_privacy_statement_1_3_2_4_gallery02).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_5_gallery01).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.realme_settings_privacy_statement_1_3_2_5_gallery02).toByteArray())
                    write("\r\n".toByteArray())
                } else {
                    write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_4_gallery02).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_5_gallery01).toByteArray())
                    write("\r\n".toByteArray())
                    write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_5_gallery03).toByteArray())
                    write("\r\n".toByteArray())
                }
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_6_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_6_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_7_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_7_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_8_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_8_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_2_9_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(
                    //AI 是否支持AI构图功能
                    (if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_COMPOSITION)) {
                        //AI 是否支持AI补光功能
                        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_LIGHTING)) {
                            context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03_v4)
                        } else {
                            context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03_v2)
                        }
                    } else {
                        //AI 是否支持AI补光功能
                        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_LIGHTING)) {
                            context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03_v3)
                        } else {
                            context.getString(R.string.settings_privacy_statement_1_3_2_9_gallery03)
                        }
                    }).toByteArray()
                )
                write("\r\n".toByteArray())
            }
        }

        @Suppress("LongMethod")
        private fun writePrivatePolicyToFileStepThree(file: FileOutputStream) {
            file.apply {
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_3_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_3_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_3_1_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_3_1_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_1_gallery00).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_1_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_1_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_1_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_1_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_1_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_2_gallery00).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_2_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_2_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_2_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_2_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_2_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_3_gallery00).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_3_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_3_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_3_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_3_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_3_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_4_gallery00).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_4_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_4_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_4_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_4_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_4_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_1_3_4_4_gallery06).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery00).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_5_gallery06).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery00).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_6_gallery06).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery00).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_1_3_4_7_gallery06).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_1_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_1_0_0_gallery02).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_1_0_0_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_2_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_2_0_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_2_0_0_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_2_1_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_2_1_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_2_2_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_2_2_3_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
            }
        }

        private fun writePrivatePolicyToFileStepFour(file: FileOutputStream) {
            file.apply {
                write(context.resources.getString(R.string.settings_privacy_statement_3_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_3_0_0_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_3_0_0_0_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_3_0_0_0_gallery04).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_thrid_party_info_sharing_gallery).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_3_0_0_0_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_4_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_4_0_0_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_4_0_0_0_gallery03).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_4_0_0_0_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery02).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery03).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery04).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery06).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery07).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery08).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery09).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_5_0_0_0_gallery10).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_6_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_6_0_0_0_gallery02).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_6_0_0_0_gallery03).toByteArray())
                write("\r\n".toByteArray())
            }
        }

        @SuppressLint("StringFormatInvalid")
        @Suppress("LongMethod")
        private fun writePrivatePolicyToFileStepFive(file: FileOutputStream) {
            file.apply {
                write(context.resources.getString(R.string.settings_privacy_statement_6_0_0_0_gallery04).toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_6_0_0_0_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_6_0_0_0_gallery06).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_7_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_7_0_0_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_8_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_8_0_0_0_gallery02).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_8_0_0_0_gallery03).toByteArray())
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_9_0_0_0_gallery01).toByteArray())
                write("\r\n".toByteArray())
                write(
                    String.format(
                        context.getString(R.string.settings_privacy_statement_9_0_0_0_gallery02),
                        context.getString(R.string.setting_pref_personanl_privacy_policy_title)
                    ).toByteArray()
                )
                write("\r\n".toByteArray())
                write(context.resources.getString(R.string.settings_privacy_statement_9_0_0_0_gallery05).toByteArray())
                write("\r\n".toByteArray())
                write(
                    String.format(
                        context.resources.getString(
                            if (isRealmeBrand) {
                                R.string.settings_privacy_statement_130_gallery_realme
                            } else {
                                R.string.settings_privacy_statement_130_gallery
                            }
                        ),
                        PrivacyLinkImpl().getDataPlatformLink()
                    ).toByteArray()
                )
                write("\r\n".toByteArray())
                write(context.getString(R.string.settings_privacy_statement_9_0_0_0_gallery06).toByteArray())
                write("\r\n".toByteArray())
                write(
                    context.resources.getString(
                        if (isRealmeBrand) {
                            R.string.settings_privacy_statement_company_name_gallery_realme
                        } else {
                            R.string.settings_privacy_statement_company_name_gallery
                        }
                    ).toByteArray()
                )
                write("\r\n".toByteArray())
                write(
                    context.resources.getString(
                        if (isRealmeBrand) {
                            R.string.settings_privacy_statement_company_address_gallery_realme
                        } else {
                            R.string.settings_privacy_statement_company_address_gallery
                        }
                    ).toByteArray()
                )

                write("\r\n".toByteArray())
                write(
                    context.resources.getString(
                        if (isRealmeBrand) {
                            R.string.settings_privacy_statement_post_code_gallery_realme
                        } else {
                            R.string.settings_privacy_statement_post_code_gallery
                        }
                    ).toByteArray()
                )
                write("\r\n".toByteArray())
                flush()
                close()
            }
        }

        private fun showSnackBar(file: File) {
            rootView?.let {
                val snackBar = COUISnackBar.make(
                    it, context.resources.getString(R.string.setting_personanl_privacy_policy_saved), MAX_SNACK_BAR_AUTO_DISAPPEAR_TIME
                )
                snackBar.apply {
                    setOnAction(
                        context.resources.getString(R.string.setting_personanl_privacy_policy_check)
                    ) {
                        val intent = Intent().apply {
                            action = ACTION_FILE_MANAGER_BROWSER_FILE
                            putExtra(EXTRA_FILE_MANAGER_BROWSER_FILE, file.path)
                        }
                        context.safeStartActivity(intent)
                    }
                    show()
                }
            }
        }

        fun goToThirdPartyInfoSharing() {
            Starter.ActivityStarter(
                context, Bundle(), PostCard(RouterConstants.RouterName.SETTING_THIRD_PARTY_INFO_SHARING)
            ).start()
        }

        fun goToCollectionOfPersonInfo() {
            Starter.ActivityStarter(
                context, Bundle(), PostCard(RouterConstants.RouterName.SETTING_COLLECTION_OF_PERSONAL_COLLECT_INFO)
            ).start()
        }

        /**
         * 跳转系统隐私政策
         */
        fun goToSystemPrivacyPolicy() {
            ApiDmManager.getSettingDM().getPrivacyNoticeIntent()?.let {
                context.startActivity(it)
            }
        }
    }
}