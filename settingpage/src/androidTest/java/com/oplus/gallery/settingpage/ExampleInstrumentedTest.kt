/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ExampleInstrumentedTest.kt
 ** Description : Instrumented test, which will execute on an Android device.
 **               See [testing documentation](http://d.android.com/tools/testing).
 ** Version     : 1.0
 ** Date        : 2024/6/11
 ** Author      : W9002848
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>        2020/7/16     1.0        Interface testing
 ** W9002848                    2024/6/11     1.1        And KFile header describe
 *********************************************************************************/
package com.oplus.gallery.settingpage

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4

import org.junit.Test
import org.junit.runner.RunWith

import org.junit.Assert.*

@RunWith(AndroidJUnit4::class)
class ExampleInstrumentedTest {
    @Test
    fun useAppContext() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.coloros.gallery3d.setting.test", appContext.packageName)
    }
}