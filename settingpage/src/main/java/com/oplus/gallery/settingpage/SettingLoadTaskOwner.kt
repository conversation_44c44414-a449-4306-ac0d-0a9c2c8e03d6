/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : SettingLoadTaskOwner.kt
 ** Description : 设置页面taskOwner实现
 ** Version     : 1.0
 ** Date        : 2023/09/22
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>            2023/09/22      1.0     OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.settingpage

import androidx.lifecycle.Lifecycle
import com.oplus.gallery.framework.abilities.taskmanage.ILoadTaskOwner
import com.oplus.gallery.framework.abilities.taskmanage.config.TaskConfigStrategy
import com.oplus.gallery.framework.abilities.taskmanage.task.TaskOwnerConst

/**
 * TaskManager中需要记录的任务Owner对象
 * 虽然本身setting页面并没有task，但是由于其他页面的一些逻辑可能被setting影响，所以还是需要setting在创建的时候把自己添加到taskManager中
 * 当前的实现是很简单的，如果后续真的有task，需要再调整
 */
class SettingLoadTaskOwner : ILoadTaskOwner {
    private var lifeEvent: Lifecycle.Event = Lifecycle.Event.ON_ANY
    private val taskStrategy = TaskConfigStrategy()

    override val id: String = TaskOwnerConst.SETTING
    override val lifeCycleEvent: Lifecycle.Event
        get() = lifeEvent
    override val linkages: Set<String>? = null
    override val affinities: Set<String>? = null
    override val container: String? = null
    override val taskConfigStrategy: TaskConfigStrategy = taskStrategy

    /**
     * 更新生命周期的状态
     * @param event 生命周期的状态
     */
    fun setLifeCycleEvent(event: Lifecycle.Event) {
        lifeEvent = event
    }
}