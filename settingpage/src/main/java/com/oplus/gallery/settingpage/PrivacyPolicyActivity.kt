/***********************************************************
 ** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: -PrivacyPolicyActivity.kt
 ** Description:  A privacy policy page for Gallery
 ** Version: 1.0
 ** Date : 2021-08-31 14:12:28
 ** Author: ********  caojie
 **
 ** ---------------------Revision History: ---------------------
 ** ********  caojie       2021/08/31  1.0     build this module
 ****************************************************************/
package com.oplus.gallery.settingpage

import android.graphics.Color
import android.os.Bundle
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.ScrollView
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.component.privacypolicy.COUIPrivacyPolicyView
import com.google.android.material.appbar.AppBarLayout
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.basebiz.uikit.activity.PaddingActivitySystemBarStyle
import com.oplus.gallery.business_lib.helper.ToolbarAnimationHelper
import com.oplus.gallery.foundation.ui.systembar.ActivitySystemBarStyle
import com.oplus.gallery.router_lib.annotations.RouterNormal

/**
 * 个人信息保护政策 界面
 */
@RouterNormal(RouterConstants.RouterName.SETTING_PRIVACY_POLICY)
class PrivacyPolicyActivity : BaseActivity() {

    private var rootView: LinearLayout? = null
    private var toolbarAnimationHelper: ToolbarAnimationHelper? = null

    private val privacyPolicyView: COUIPrivacyPolicyView by lazy { findViewById(R.id.privacy_policy) }
    private val privacyScrollview: ScrollView by lazy { findViewById(R.id.privacy_scrollview) }

    override fun getSystemBarStyle(): ActivitySystemBarStyle = object : PaddingActivitySystemBarStyle(this) {
        override fun onInit() {
            super.onInit()
            setNaviBarColor(Color.TRANSPARENT)
        }
    }

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        super.onSystemBarChanged(windowInsets)
        val bottomPadding = if (hasVirtualKey()) {
            resources.getDimensionPixelSize(R.dimen.setting_privacy_policy_container_padding_bottom)
        } else {
            resources.getDimensionPixelSize(R.dimen.setting_privacy_policy_container_padding_bottom) + bottomNaviBarHeight(true)
        }
        privacyPolicyView.apply {
            setPadding(paddingLeft, paddingTop, paddingRight, bottomPadding)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.setting_privacy_policy_activity_layout)
        initViews()
        initWebView()
    }

    private fun initViews() {
        rootView = findViewById(R.id.root_view)
        (rootView?.parent as? ViewGroup)?.clipChildren = false
        findViewById<COUIToolbar>(R.id.toolbar).apply {
            setNavigationContentDescription(androidx.appcompat.R.string.abc_action_bar_up_description)
            setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
            setNavigationOnClickListener {
                onBackPressed()
            }
        }
        toolbarAnimationHelper = ToolbarAnimationHelper(findViewById<AppBarLayout>(com.oplus.gallery.basebiz.R.id.appbar_layout))
        privacyScrollview.setOnScrollChangeListener { _, _, scrollY, _, _ ->
            toolbarAnimationHelper?.onListScrolled(scrollY)
        }
    }

    private fun initWebView() {
        privacyPolicyView.apply {
            PrivacyPolicyViewContentImpl(this@PrivacyPolicyActivity, rootView).getPrivacyContent(this)
        }
    }

    /**
     * 用户协议获取主体内容的接口，被PrivacyPolicyViewContentImpl实现，
     * getPrivacyContent方法在内外销中获取不同的内容
     */
    interface IPrivacyPolicyViewContent {
        /**
         * 获取协议的主体内容
         * @param privacyPolicyView 承载协议的控件
         */
        fun getPrivacyContent(privacyPolicyView: COUIPrivacyPolicyView)
    }
}