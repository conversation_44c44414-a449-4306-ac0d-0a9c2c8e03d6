/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : IntelliFuncSwitchHelper.kt
 ** Description : 智能编辑推荐 入口 帮助类
 ** Version     : 1.0
 ** Date        : 2024/5/9
 ** Author      : <EMAIL>
 ** TAG         :
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                  <date>      <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/5/9     1.0         first created
 *********************************************************************************/
package com.oplus.gallery.settingpage

import android.app.Activity
import android.os.Bundle
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.notification.GalleryNotificationDisplayManager
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkAuthorizationInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.NetworkInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor.Companion.KEY_CACHE_PLUGIN_CONFIG_ID
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor.Companion.KEY_IS_SKIP_CONFIRM
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPrivacyInterceptor
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ConfirmInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.foundation.util.ext.runOnWorkThread
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.FLAW_RECOGNIZED_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.INTELLIGENT_PLUGIN_SIZE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_UNIT_CONFIG_AVAILABLE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_FLAW_RECOGNIZE_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_INTELLI_FUNC_RECOMMEND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.ViewBehavior.INTELLIGENT_EDITING_RECOMMENDATION
import com.oplus.gallery.framework.abilities.download.AIUnitDownloadWord
import com.oplus.gallery.framework.abilities.download.IAIUnitDownloadAbility
import com.oplus.gallery.framework.abilities.download.canDownload
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 智能推荐功能开关辅助类，负责开关的控制以及缺陷检测模型的下载
 * @param activity Activity
 */
class IntelliFuncSwitchHelper(
    private val activity: Activity
) {
    private val notificationDisplayManager by lazy {
        GalleryNotificationDisplayManager(activity)
    }

    private val chains: RealInterceptorChain by lazy {
        val interceptors = getInterceptors {
            notificationDisplayManager.update(it)
        }
        RealInterceptorChain(
            interceptors,
            onSuccessCallback = {},
            onFailCallback = {
                setIntelliFuncEnable(false)
            }
        )
    }

    private var isNotificationShowing = false

    private val flawPluginState by lazy {
        AIUnitPluginState(
            AIUnitPlugin.INTELLI_FUNC.downloadPlugin,
            FLAW_RECOGNIZED_DOWNLOAD_STATE,
            UPDATE_FLAW_RECOGNIZE_SHOW_TIMESTAMP
        )
    }

    private val isSwitchOpen get() = ConfigAbilityWrapper.getBoolean(INTELLIGENT_EDITING_RECOMMENDATION)

    private val downloadWord by lazy {
        AIUnitDownloadWord(
            R.string.setting_intelligent_recognition_mobile_network_dialog_title,
            R.string.setting_intelligent_recognition_update_desc,
            R.string.setting_intelligent_recognition_install_desc,
            R.string.setting_intelligent_recognition_download_fail_dialog_title,
            R.string.setting_intelligent_recognition_toast_install_fail,
            R.string.setting_intelligent_recognition_updating,
            R.string.setting_intelligent_recognition_dialog_download_title,
            R.string.setting_intelligent_recognition_update_finish,
            R.string.setting_intelligent_recognition_download_finish
        )
    }

    /**
     * 是否支持智能推荐
     */
    val isSupport: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_INTELLI_FUNC_RECOMMEND, false)
    }

    /**
     * 开关变化时触发
     */
    var onSwitch: ((Boolean) -> Unit)? = null

    /**
     * 通知智能推荐的开关变化
     * @param isOpened 开关是否打开
     */
    fun notifyPreferenceChanged(isOpened: Boolean) {
        if (isOpened && flawPluginState.canDownload()) {
            chains.proceed(Bundle())
        } else {
            setIntelliFuncEnable(isOpened)
        }
    }

    fun onStart() {
        onSwitch?.invoke(isSwitchOpen)
        updateConfig {
            autoDownloadIfNeed()
        }
    }

    private fun autoDownloadIfNeed() {
        updateDownloadState { isDownloading ->
            // 开关打开时，如果插件可以下载，且插件没有在下载中，则触发下载
            if (isNotificationShowing.not() && isSwitchOpen && !isDownloading && flawPluginState.canDownload()) {
                runOnUiThread {
                    chains.proceed(Bundle())
                }
            }
        }
    }

    private fun updateDownloadState(block: (isDownloading: Boolean) -> Unit) {
        val context = activity.application
        context.getAppAbility<IAIUnitDownloadAbility>()?.use {
            it.queryDownloadState(activity.application, flawPluginState.pluginId.requestID) { state, fullSize ->
                GLog.d(TAG, LogFlag.DF, "state:$state, fullSize:$fullSize")
                if (fullSize > 0) {
                    context.getAppAbility<ISettingsAbility>()?.use { settingAbility ->
                        settingAbility.setPluginSize(INTELLIGENT_PLUGIN_SIZE, fullSize)
                    }
                }
                block.invoke(state == STATE_OF_DOWNLOADING)
            }
        }
    }

    private fun getInterceptors(postNotificationAction: (NotificationAction) -> Unit) =
        listOf(
            createDownloadNotifyAction(postNotificationAction),
            // 没有联网
            NetworkInterceptor(postNotificationAction),
            // 网络权限
            NetworkAuthorizationInterceptor(
                R.string.setting_intelligent_recognition_no_network_tips_content,
                postNotificationAction
            ),
            // AIUnit用户须知拦截器
            AIUnitPrivacyInterceptor(activity),
            AIUnitModelDownloadInterceptor(
                activity,
                flawPluginState,
                downloadWord,
                postNotificationAction,
                enableUI = { NetworkMonitor.isWifiValidated().not() },
                startDownload = {
                    setIntelliFuncEnable(true)
                }
            )
        )

    private fun createDownloadNotifyAction(updateUI: (NotificationAction) -> Unit): ConfirmInterceptor {
        return object : ConfirmInterceptor(updateUI) {

            override fun onCheckCondition(param: Bundle): Boolean {
                // 开关打开时，模型未下载或者下载状态异常
                return isSwitchOpen.not() || NetworkMonitor.isWifiValidated() || flawPluginState.updatable()
            }

            override fun createConfirmDialogAction(confirmCallback: NotificationAction.IConfirmCallback): NotificationAction.ConfirmDialogAction {
                isNotificationShowing = true
                val size = ConfigAbilityWrapper.getLong(INTELLIGENT_PLUGIN_SIZE, DEFAULT_PLUGIN_SIZE)
                val sizeValue = FilePathUtils.getUnitValue(activity, size)
                val message = activity.getString(R.string.setting_intelligent_notification_message, sizeValue)
                GLog.d(TAG, LogFlag.DF, "[createConfirmDialogAction] size:$size")
                return NotificationAction.ConfirmDialogAction(
                    titleString = activity.getString(R.string.setting_intelligent_recognition_mobile_network_dialog_title),
                    messageString = message,
                    negativeButtonTextResId = com.oplus.gallery.basebiz.R.string.common_cancel,
                    positiveButtonTextResId =  com.oplus.gallery.foundation.ui.R.string.download_notification_channel_name,
                    confirmCallback = confirmCallback
                )
            }

            override fun onAgreedProcessChain(chain: IInterceptor.IChain<Bundle, Unit>) {
                isNotificationShowing = false
                chain.param.putBoolean(KEY_IS_SKIP_CONFIRM, true)
                chain.param.putString(KEY_CACHE_PLUGIN_CONFIG_ID, INTELLIGENT_PLUGIN_SIZE)
                super.onAgreedProcessChain(chain)
            }

            override fun onNeutral() {
                super.onNeutral()
                isNotificationShowing = false
                setIntelliFuncEnable(false)
            }
        }
    }

    private fun setIntelliFuncEnable(enable: Boolean) {
        runOnUiThread {
            onSwitch?.invoke(enable)
        }
        runOnWorkThread {
            activity.getAppAbility<ISettingsAbility>()?.use {
                it.setIntelliFuncEnable(enable)
            }
        }
    }

    fun destroy() {
        GLog.d(TAG, LogFlag.DF) { "destroy" }
        onSwitch = null
        AppScope.launch(Dispatchers.IO) {
            chains.destroy()
        }
    }

    private fun updateConfig(callback: suspend () -> Unit) {
        AppScope.launch(Dispatchers.IO) {
            activity.getAppAbility<ISettingsAbility>()?.use {
                it.updateBlockingConfig(
                    activity,
                    FLAW_RECOGNIZED_DOWNLOAD_STATE,
                    IS_AGREE_AI_UNIT_PRIVACY,
                    IS_AI_UNIT_CONFIG_AVAILABLE
                )
            }
            callback.invoke()
        }
    }

    companion object {
        private const val TAG = "IntelliFuncSwitchHelper"

        private const val ONE_MB_BYTE = 1024 * 1024f

        private const val STATE_OF_DOWNLOADING = 4

        private const val DEFAULT_PLUGIN_SIZE = 47383616L

        private val DEBUG_MOBILE_NETWORK
            get() = GallerySystemProperties.getBoolean("debug.gallery.mobile_network", false)
    }
}