/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description: 共享图集页
 **
 ** Version: 1.0
 ** Date: 2025/3/27
 ** Author: 80407954@OppoGallery3D
 ** TAG: xx
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80407954@OppoGallery3D  2025/3/27  1.0        我的图集-共享 tab
 *********************************************************************************/
package com.oplus.gallery.sharedalbumpage.ui

import android.content.Context
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.oplus.gallery.sharedalbumpage.ui.widget.SharedGridLayoutManager
import androidx.recyclerview.widget.COUILinearLayoutManager
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.constants.RouterConstants.RouterName.SHARED_DYNAMIC_PANEL_DIALOG
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.sidepane.ISidePaneAccessor
import com.oplus.gallery.basebiz.sidepane.ISidePaneAccessorGetter
import com.oplus.gallery.basebiz.sidepane.ISidePaneListener
import com.oplus.gallery.basebiz.sidepane.SidePaneAnimationConfig
import com.oplus.gallery.basebiz.sidepane.SidePaneState
import com.oplus.gallery.basebiz.sidepane.SidePaneWithAlbumAnimation
import com.oplus.gallery.basebiz.sidepane.getSlideWidth
import com.oplus.gallery.basebiz.sidepane.isOpen
import com.oplus.gallery.basebiz.sidepane.isSupportSidePane
import com.oplus.gallery.basebiz.sidepane.isVisible
import com.oplus.gallery.basebiz.uikit.fragment.IClickSelectedTabAgainCallback
import com.oplus.gallery.basebiz.uikit.fragment.IMyAlbumToolbarCustomize
import com.oplus.gallery.basebiz.util.AlbumCardListUtils
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.gallery.cloudsyncpage.sharedalbum.R
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.uikit.responsiveui.IAppUiObserver
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.findFirstVisiblePosition
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.sharedalbumpage.ext.showAlbumMediaPage
import com.oplus.gallery.sharedalbumpage.ext.showSelectPicturePage
import com.oplus.gallery.sharedalbumpage.push.OnPushProcessorCallBack
import com.oplus.gallery.sharedalbumpage.push.SharedAlbumMessage
import com.oplus.gallery.sharedalbumpage.push.SharedAlbumPushProcessor
import com.oplus.gallery.sharedalbumpage.ui.SharedAlbumMainTabFragment.Companion.ELLIPSIS_INVITE_MSG_COUNT
import com.oplus.gallery.sharedalbumpage.ui.SharedAlbumMainTabFragment.Companion.MAX_INVITE_MSG_COUNT
import com.oplus.gallery.sharedalbumpage.ui.binding.SharedAlbumViewDataBinding
import com.oplus.gallery.sharedalbumpage.ui.viewdata.SharedAlbumViewData
import com.oplus.gallery.sharedalbumpage.view.SharedAlbumEditDialogHelper
import com.oplus.gallery.sharedalbumpage.viewmodel.OnSharedAlbumChangeListener
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumListViewModel
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumMainTabViewModel
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumMainViewModel
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_PAGE_UNKNOWN
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.FROM_SIDE_PANE_PAGE
import com.oplus.gallery.standard_lib.app.AppConstants.FromInternalPage.KEY_FROM_PAGE
import com.oplus.gallery.standard_lib.app.AppConstants.TabPagerAction.SHOW_DYNAMIC_PANEL
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.GridItemGapDecoration
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.sharealbum.track.SharedAlbumTrackConstant
import com.oplus.sharealbum.track.SharedAlbumTrackHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 我的图集-共享tab
 * 搬运自SharedAlbumListFragment,后续会进行改造。
 */
@RouterNormal(RouterConstants.RouterName.SHARE_ALBUM_SET_FRAGMENT)
class SharedAlbumSetFragment :
    BaseSharedAlbumTabListFragment<SharedAlbumResponse, SharedAlbumViewData, SharedAlbumListViewModel>(),
    OnSharedAlbumChangeListener, IClickSelectedTabAgainCallback, IMyAlbumToolbarCustomize,
    IAppUiObserver, ISidePaneListener, OnPushProcessorCallBack {

    /**
     * 和[我的]tab共用的Tab页面的分割线
     */
    private var divider: View? = null
    private var pageStartTime: Long = 0
    private var toolbar: COUIToolbar? = null
    private val mainViewModel by activityViewModels<SharedAlbumMainViewModel>()
    // 共享图集列表页和动态页面共用一个viewModel，改版之后列表页和动态页面没有共同的父Fragment来持有它，所以这里同mainViewModel一样处理
    private val mainTabViewModel by activityViewModels<SharedAlbumMainTabViewModel>()

    private val refreshAlbumInfoObserver = Observer<Boolean> { shouldRefresh ->
        if (shouldRefresh) {
            GLog.d(TAG, LogFlag.DL) { "afterViewCreated, refreshAlbumInfo" }
            refreshData()
        }
    }

    /**
     * 动态面板
     */
    private var dynamicPanel: DialogFragment? = null

    /**
     * 侧边栏展开收起动画联动基础列表动画管理
     */
    private var sidePaneAnim: SidePaneWithAlbumAnimation? = null

    /**
     * 记录从哪个页面跳转至这个页面：来源
     */
    private var fromPage: Int = FROM_PAGE_UNKNOWN

    private val sidePaneAccessor: ISidePaneAccessor?
        get() = (activity as? ISidePaneAccessorGetter)?.getSidePaneAccessor()

    override fun onCreateViewModel(): SharedAlbumListViewModel {
        return ViewModelProvider(this).get(SharedAlbumListViewModel::class.java)
    }

    /**
     * 当接收到共享图集邀请时会跳转到我的图集页面共享tab并显示动态面板
     */
    private fun startDynamicPanelIfNeed() {
        if (arguments?.getBoolean(SHOW_DYNAMIC_PANEL, false) == true) {
            view?.post { startDynamicPanel() }
        }
    }

    override fun supportClickStatusBar(): Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply { fromPage = getInt(KEY_FROM_PAGE, FROM_PAGE_UNKNOWN) }
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        mainViewModel.addOnSharedAlbumChangeListener(this)
        mainTabViewModel.apply {
            refreshAlbumInfo.observe(this@SharedAlbumSetFragment, refreshAlbumInfoObserver)
            showCreateAlbumDialogInfo.observe(this@SharedAlbumSetFragment) {
                if (it) showCreateAlbumDialog()
            }
            refreshRedPointInfo.observe(this@SharedAlbumSetFragment) {
                updateRedPointUi(it.inviteMsgCount, it.hasNew)
            }
        }
        recyclerView?.let {
            it.updatePadding(top = it.paddingTop)
            (it as? COUIRecyclerView)?.let { rv ->
                rv.setItemClickableWhileOverScrolling(false)
                rv.setItemClickableWhileSlowScrolling(false)
            }
        }
        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let { accessor ->
                accessor.registerSlideListener(this)
                accessor.registerStateListener(this)
            }
        }
        sidePaneAccessor?.takeIf { isSidePaneEnabled() }?.let {
            sidePaneAnim = recyclerView?.let { rv ->
                SidePaneWithAlbumAnimation(this, rv)
            }
        }
        SharedAlbumPushProcessor.registerPushProcessor(this)
        addRVScrollListener()
        startDynamicPanelIfNeed()
    }

    private fun addRVScrollListener() {
        recyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                // 内容向上滚动时，展示分割线
                updateDividerVisibility()
            }
        })
    }

    /**
     * 更新动态菜单项小红点的显示状态，如果存在未读消息，则显示小红点（有共享邀请时显示数量）否则隐藏
     * @param inviteMsgCount 未读消息数量（共享邀请数量）
     * @param hasNew 是否有新消息
     */
    private fun updateRedPointUi(inviteMsgCount: Int, hasNew: Boolean) {
        toolbar?.apply {
            menu?.findItem(R.id.action_start_dynamic_panel) ?: return
            if (inviteMsgCount > 0) {
                setRedDot(
                    R.id.action_start_dynamic_panel,
                    if (inviteMsgCount > MAX_INVITE_MSG_COUNT) ELLIPSIS_INVITE_MSG_COUNT else inviteMsgCount
                )
            } else if (hasNew) {
                setRedDot(R.id.action_start_dynamic_panel, NEW_MESSAGE_FLAG)
            } else {
                setRedDot(R.id.action_start_dynamic_panel, NONE_MESSAGE_FLAG)
            }
        }
    }

    /**
     * 刷新分割线的显隐状态
     */
    private fun updateDividerVisibility() {
        recyclerView?.let { rv ->
            divider?.isVisible = (rv.computeVerticalScrollOffset() > 0)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dynamicPanel?.dismiss()
        mainTabViewModel.refreshAlbumInfo.removeObserver(refreshAlbumInfoObserver)
        SharedAlbumPushProcessor.unregisterPushProcessor(this)
        mainViewModel.removeOnSharedAlbumChangeListener(this)
        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let { accessor ->
                accessor.unregisterSlideListener(this)
                accessor.unregisterStateListener(this)
            }
        }
    }

    override fun onSharedAlbumCreate(album: SharedAlbumResponse) {
        baseListViewModel?.addAlbumToList(album)
    }

    override fun onSharedAlbumDelete(albumId: String) {
        baseListViewModel?.deleteAlbumFromList(albumId)
    }

    override fun onSharedAlbumChange(album: SharedAlbumResponse) {
        baseListViewModel?.updateAlbumFromList(album)
    }

    override fun firstLoadData() {
        showLoadingView()
        baseListViewModel?.refresh()
    }

    override fun getAdapterItemConfigs(): List<ItemConfig<SharedAlbumViewData>> {
        return mutableListOf<ItemConfig<SharedAlbumViewData>>().apply {
            layoutDetail?.let { detail ->
                context?.let { context ->
                    add(
                        ItemConfig(
                            SharedAlbumViewData::class.java,
                            SharedAlbumViewDataBinding(context, this@SharedAlbumSetFragment, detail)
                        )
                    )
                }
            }
        }
    }

    override fun onLoadDataFinish(dataList: List<SharedAlbumViewData>?) {
        super.onLoadDataFinish(dataList)
        hideLoadingView()
    }

    override fun getLayoutId() = R.layout.share_album_set_layout

    override fun initLayoutDetail(): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        parentWidth = getContentWidth()
        edgeWidth = context?.resources?.getDimensionPixelOffset(com.oplus.gallery.basebiz.R.dimen.card_album_recyclerView_edge) ?: 0
        gapWidth = resources.getDimensionPixelOffset(R.dimen.shared_album_tab_gap_in_my_album_fragment)
        //折叠屏可能变化
        spanCount = AlbumCardListUtils.getSpanCount(context, parentWidth)
    }.build().apply {
        itemDecorationGapPx.top = resources.getDimension(R.dimen.shared_album_item_gap_top_in_my_album)
        itemDecorationGapPx.bottom = resources.getDimension(R.dimen.shared_album_item_gap_bottom_in_my_album)
    }

    override fun getLayoutManager(recyclerView: RecyclerView, context: Context, layoutDetail: LayoutDetail): RecyclerView.LayoutManager = when {
        (layoutDetail is GridLayoutDetail) -> SharedGridLayoutManager(recyclerView, layoutDetail)
        else -> COUILinearLayoutManager(context)
    }

    override fun onStatusBarClicked() {
        super.onStatusBarClicked()
        recyclerView?.let { rv ->
            val firstVisiblePosition = rv.layoutManager?.findFirstVisiblePosition() ?: 0
            rv.scrollToPosition(firstVisiblePosition.coerceAtMost(rv.childCount))
            rv.post {
                rv.smoothScrollToPosition(0)
            }
        }
    }

    /**
     * 获取当前屏幕(减去侧边栏）的宽度
     */
    private fun getContentWidth(): Int {
        var slideWidth = 0
        if (isSidePaneEnabled()) {
            sidePaneAccessor?.let { sidePane ->
                slideWidth = if (sidePane.isOpen()) sidePane.getSlideWidth() else 0
            }
        }
        return getCurrentAppUiConfig().windowWidth.current - slideWidth
    }

    private fun isSidePaneEnabled(): Boolean = sidePaneAccessor.isSupportSidePane()

    override fun onEmptyViewClick() {
        mainTabViewModel.refreshCreateSharedAlbumDialog()
    }

    override fun onItemClick(position: Int, viewData: SharedAlbumViewData?, clickType: Int, itemView: View?) {
        val album = viewData?.data
        if (null == album) {
            GLog.e(TAG, LogFlag.DL) { "onItemClick album is null" }
            return
        }
        showAlbumMediaPage(album)
    }

    override fun addItemDecoration() {
        val itemDecoration = GridItemGapDecoration(layoutDetail as GridLayoutDetail)
        recyclerView?.addItemDecoration(itemDecoration)
    }

    override fun onResume() {
        super.onResume()
        setHasOptionsMenu(true)
        updateDividerVisibility()
        lifecycleScope.launch {
            val hasNetPermission = withContext(Dispatchers.IO) {
                NetworkPermissionManager.isUseOpenNetwork && NetworkMonitor.isNetworkValidated()
            }
            if (hasNetPermission && isAdded) {
                mainTabViewModel.apply {
                    refreshDynamic()
                    refreshRedPoint()
                }
            }
        }
        pageStartTime = System.currentTimeMillis()
    }

    override fun onPause() {
        super.onPause()
        val pageStayTime = System.currentTimeMillis() - pageStartTime
        val isBlank = (currentPageStatus == PAGE_STATUS_EMPTY)
        SharedAlbumTrackHelper.trackAlbumsPage(SharedAlbumTrackConstant.Value.ALBUM_TAB_SHARED_ALBUM, isBlank, pageStayTime)
    }

    override fun onClickSelectedTabAgain() {
        recyclerView?.smoothScrollToPosition(0)
    }

    override fun customizeToolbar(toolbar: COUIToolbar) {
        this.toolbar = toolbar
    }

    override fun customizeDividerUnderToolbar(divider: View) {
        this.divider = divider
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (!isResumed) return
        menu.clear()
        inflater.inflate(R.menu.share_album_set_toolbar_menu, menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!isResumed) return false
        when (item.itemId) {

            R.id.action_add_new_album -> showCreateAlbumDialog()

            R.id.action_start_dynamic_panel -> startDynamicPanel()
        }
        return super.onOptionsItemSelected(item)
    }

    /**
     * 拉下动态面板
     */
    private fun startDynamicPanel() {
        dynamicPanel = Starter.DialogFragmentStarter<PanelDialog>(
            fm = activity?.supportFragmentManager,
            postCard = PostCard(SHARED_DYNAMIC_PANEL_DIALOG)
        ).start()
    }

    private fun showCreateAlbumDialog() {
        SharedAlbumEditDialogHelper.showCreateDialog(requireContext(), null, SharedAlbumTrackConstant.Value.ALBUMS_PAGE) {
            mainViewModel.onSharedAlbumCreated(it)
            // 跳转到图集详情页
            activity?.showSelectPicturePage { list ->
                showAlbumMediaPage(it, list)
            }
        }
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        GLog.d(TAG, LogFlag.DL) {
            "onAppUiStateChanged,windowWidth:${uiConfig.windowWidth} windowHeight:${uiConfig.windowHeight}"
        }
        if (uiConfig.windowWidth.isChanged() || uiConfig.screenMode.isChanged()) {
            updateLayoutWhenConfigChange()
            (activity as? AppCompatActivity)?.supportActionBar?.setDisplayHomeAsUpEnabled(isNeedHomeAsUp())
        }
    }

    /**
     * 是否需要返回箭头
     */
    private fun isNeedHomeAsUp(): Boolean {
        return !((fromPage == FROM_SIDE_PANE_PAGE) && sidePaneAccessor.isVisible())
    }

    override fun onSidePaneSlideStart(newState: SidePaneState) {
        super.onSidePaneSlideStart(newState)
        GLog.d(TAG, LogFlag.DL) {
            "onSidePaneSlideStart: isResumed:$isResumed newState=$newState $clazzSimpleName"
        }
        if (isResumed && newState.isFlat()) {
            val currentSpanCount = getListSpanCount()
            val currentItemWidth = getListItemWidth()
            context?.let {
                refreshLayoutManager()
            }
            sidePaneAnim?.startAnimation(
                SidePaneAnimationConfig(
                    currentSpanCount = currentSpanCount,
                    nextSpanCount = getListSpanCount(),
                    currentItemWidth = currentItemWidth,
                    nextItemWidth = layoutDetail?.itemWidth ?: 0,
                    slideWidth = sidePaneAccessor.getSlideWidth(),
                    gapWidth = resources.getDimensionPixelOffset(R.dimen.shared_album_tab_gap_in_my_album_fragment),
                    newState = newState
                )
            )
        }
    }

    private fun getListItemWidth(): Int {
        return (recyclerView?.layoutManager as? GridLayoutManager)?.getChildAt(0)?.width ?: 0
    }

    private fun getListSpanCount(): Int = (recyclerView?.layoutManager as? GridLayoutManager)?.spanCount ?: 1

    override fun onSidePaneSlideEnd(newState: SidePaneState) {
        sidePaneAnim?.endAnimation(newState)
        if (!isResumed && newState.isFlat()) {
            context?.let {
                refreshLayoutManager()
            }
        }
    }

    override fun onSidePaneSliding(newState: SidePaneState, slideProgress: Float) {
        sidePaneAnim?.animating(newState, slideProgress)
    }

    /**
     * 横竖屏变化、折叠栏展开收起时刷新布局
     */
    private fun updateLayoutWhenConfigChange() {
        refreshLayoutManager()
        recyclerAdapter?.notifyItemRangeChanged(
            0,
            baseListViewModel?.viewDataLiveData?.value?.size ?: 0
        )
    }

    override fun onPushProcessor(sharedAlbumMessage: SharedAlbumMessage) {
        mainTabViewModel.refreshRedPoint()
        mainTabViewModel.refreshDynamic()
        //刷新图集列表页
        when (sharedAlbumMessage.type) {
            SharedAlbumMessage.TYPE_ADD_ITEM,
            SharedAlbumMessage.TYPE_DELETE_ITEM,
            SharedAlbumMessage.TYPE_ACCEPT_INVITE,
            SharedAlbumMessage.TYPE_DELETE_INVITEE,
            SharedAlbumMessage.TYPE_QUIT,
            SharedAlbumMessage.TYPE_DELETE_ALBUM,
            SharedAlbumMessage.TYPE_CHANGE_ATTRIBUTE -> {
                mainTabViewModel.refreshAlbum()
            }
            else ->
                GLog.d(TAG, LogFlag.DL) { "onPushProcess,type = ${sharedAlbumMessage.type}" }
        }
    }

    companion object {
        private const val TAG = "SharedAlbumSetFragment"

        private const val NONE_MESSAGE_FLAG = -1

        private const val NEW_MESSAGE_FLAG = 0
    }
}