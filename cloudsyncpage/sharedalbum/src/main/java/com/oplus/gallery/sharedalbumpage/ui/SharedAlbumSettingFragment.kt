/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumSettingFragment
 ** Description:
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/30        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.ui

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.preference.Preference
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.preference.COUIRecommendedPreference
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.R as BaseR
import com.oplus.gallery.basebiz.sidepane.ISidePaneWithViewStateAdjust
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.gallery.cloudsyncpage.sharedalbum.R
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.LocaleUtils
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.router_lib.Starter
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.router_lib.meta.PostCard
import com.oplus.gallery.sharedalbumpage.SharedAlbumPageConstant
import com.oplus.gallery.sharedalbumpage.push.OnPushProcessorCallBack
import com.oplus.gallery.sharedalbumpage.push.SharedAlbumMessage
import com.oplus.gallery.sharedalbumpage.push.SharedAlbumPushProcessor
import com.oplus.gallery.sharedalbumpage.util.exitSharedAlbum
import com.oplus.gallery.sharedalbumpage.view.SharedAlbumEditDialogHelper
import com.oplus.gallery.sharedalbumpage.viewmodel.OnSharedAlbumChangeListener
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumMainViewModel
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumSettingViewModel
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.sharealbum.bean.FetchDocResponse
import com.oplus.sharealbum.bean.SharedAlbumResult
import com.oplus.sharealbum.track.SharedAlbumTrackConstant
import com.oplus.sharealbum.track.SharedAlbumTrackHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@RouterNormal(path = RouterConstants.RouterName.SHARED_ALBUM_SETTING_FRAGMENT)
class SharedAlbumSettingFragment : BaseSharedAlbumFragment(), View.OnClickListener, OnPushProcessorCallBack, OnSharedAlbumChangeListener {

    private lateinit var btnDeleteOrQuite: COUIButton
    private val mainViewModel: SharedAlbumMainViewModel by activityViewModels()
    private val viewModel: SharedAlbumSettingViewModel by viewModels()
    private val viewData: SettingViewData = SettingViewData()
    private var isMasterUser: Boolean = true

    override fun getLayoutId(): Int {
        return R.layout.shared_album_fragment_setting
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mainViewModel.onLoginStatusInfo.observe(this) {
            if (it.not()) {
                GLog.w(TAG, "onCreate is not login")
                exitSharedAlbum(this)
            }
        }
        mainViewModel.checkIsOnline().observe(this) {
            if (!it) {
                ToastUtil.showLongToast(R.string.shared_album_state_enable)
                exitSharedAlbum(this)
            }
        }
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        btnDeleteOrQuite = view.findViewById(R.id.btn_delete_or_quite)
        btnDeleteOrQuite.setOnClickListener(this)
        initData()
    }

    private fun initData() {
        if (null == arguments) {
            activity?.onBackPressed()
            return
        }
        mainViewModel.addOnSharedAlbumChangeListener(this)
        val json = requireArguments().getString(SharedAlbumPageConstant.BUNDLE_CURRENT_ALBUM)
        val sharedAlbumRes = JsonUtil.fromJson(json, SharedAlbumResponse::class.java)
        if (null == sharedAlbumRes) {
            activity?.onBackPressed()
            return
        }
        isMasterUser = sharedAlbumRes.master
        viewModel.currentAlbum = sharedAlbumRes
        viewData.setAlbum(sharedAlbumRes)
        btnDeleteOrQuite.text = viewData.buttonText
        createSharedSettingPreferenceFragment().let { sharedSettingFragment ->
            childFragmentManager.beginTransaction()
                .replace(R.id.shared_album_setting_container, sharedSettingFragment, SharedAlbumSettingPreferenceFragment.TAG)
                .commitAllowingStateLoss()
            sharedSettingFragment.setToolBarAdjuster(toolBarMarginAdjust)
        }
        viewModel.exitAlbumResult.observe(this) { handleExitAlbumResult(it) }
        SharedAlbumPushProcessor.registerPushProcessor(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        mainViewModel.removeOnSharedAlbumChangeListener(this)
        SharedAlbumPushProcessor.unregisterPushProcessor(this)
    }

    override fun onPushProcessor(sharedAlbumMessage: SharedAlbumMessage) {
        AppScope.launch(Dispatchers.UI) {
            if ((viewModel.currentAlbum?.albumId == sharedAlbumMessage.album?.albumId)
                && ((sharedAlbumMessage.type == SharedAlbumMessage.TYPE_DELETE_INVITEE)
                        || (sharedAlbumMessage.type == SharedAlbumMessage.TYPE_DELETE_ALBUM))
            ) {
                (activity as BaseActivity).pop(<EMAIL>)
            }
        }
    }

    private fun handleExitAlbumResult(code: Int) {
        when (code) {
            SharedAlbumResult.SUCCESS -> {
                ToastUtil.showShortToast(viewData.successText)
                viewModel.currentAlbum?.albumId?.let {
                    mainViewModel.onSharedAlbumDelete(it)
                    activity?.onBackPressed()
                }
            }
            SharedAlbumResult.NETWORK_ERROR -> ToastUtil.showShortToast(viewData.networkErrorText)
            else -> ToastUtil.showShortToast(R.string.shared_album_error_retry)
        }
    }

    private fun createSharedSettingPreferenceFragment(): SharedAlbumSettingPreferenceFragment {
        return (childFragmentManager.findFragmentByTag(SharedAlbumSettingPreferenceFragment.TAG) as? SharedAlbumSettingPreferenceFragment)
            ?: SharedAlbumSettingPreferenceFragment()
    }

    override fun onClick(view: View) {
        if (ClickUtil.clickable()) {
            showDeleteOrQuiteDialog()
        }
    }

    private fun showDeleteOrQuiteDialog() {
        if (!NetworkMonitor.isNetworkValidated()) {
            ToastUtil.showShortToast(viewData.notNetworkText)
            viewModel.trackSettingPagerBtnClick(viewData.trackButtonName, null)
            return
        }
        showExitDialog()
    }

    private fun showExitDialog() {
        ConfirmDialog.Builder(requireContext())
            .setMessage(viewData.dialogMessage)
            .setNeutralButton(viewData.dialogButtonText) { dialog, _ ->
                dialog.dismiss()
                deleteOrQuite()
                viewModel.trackSettingPagerBtnClick(viewData.trackButtonName, SharedAlbumTrackConstant.Value.CONFIRM)
            }
            .setNegativeButton(BaseR.string.common_cancel) { dialog, _ ->
                dialog.dismiss()
                viewModel.trackSettingPagerBtnClick(viewData.trackButtonName, SharedAlbumTrackConstant.Value.CANCEL)
            }
            .setTitle(viewData.dialogTitle)
            .build()
            .show()
    }

    private fun deleteOrQuite() {
        val albumId = viewModel.currentAlbum?.albumId
        if (albumId.isNullOrEmpty()) {
            GLog.d(TAG, "deleteOrQuite albumId = $albumId")
            return
        }
        val master = viewModel.currentAlbum?.master ?: false
        viewModel.deleteOrQuitAlbum(albumId, master)
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        menu.clear()
    }

    override fun onSharedAlbumCreate(album: SharedAlbumResponse) = Unit

    override fun onSharedAlbumDelete(albumId: String) = Unit

    override fun onSharedAlbumChange(album: SharedAlbumResponse) {
        album.albumName?.let {
            if (isMasterUser) {
                viewData.dialogTitle = getString(com.oplus.gallery.basebiz.R.string.base_delete_selected_this_album, it)
                viewData.dialogMessage = getString(R.string.shared_album_delete_all_no_content, it)
            } else {
                viewData.dialogTitle = getString(com.oplus.gallery.basebiz.R.string.base_exit_this_album, it)
            }
        }
    }

    private inner class SettingViewData {
        var buttonText: String = TextUtil.EMPTY_STRING
        var notNetworkText: String = TextUtil.EMPTY_STRING
        var networkErrorText: String = TextUtil.EMPTY_STRING
        var dialogTitle: String = TextUtil.EMPTY_STRING
        var dialogMessage: String = TextUtil.EMPTY_STRING
        var dialogButtonText: Int = 0
        var successText: String = TextUtil.EMPTY_STRING
        var trackButtonName: String = TextUtil.EMPTY_STRING

        fun setAlbum(currentAlbum: SharedAlbumResponse) {
            if (currentAlbum.master) {
                buttonText = getString(R.string.shared_album_delete_album)
                notNetworkText = getString(R.string.shared_album_no_network_unable_delete_album)
                networkErrorText = getString(R.string.shared_album_network_error_unable_delete_album)
                dialogTitle = getString(com.oplus.gallery.basebiz.R.string.base_delete_selected_this_album, currentAlbum.albumName)
                dialogMessage = getString(R.string.shared_album_delete_all_no_content, currentAlbum.albumName)
                dialogButtonText = R.string.shared_album_delete
                successText = getString(R.string.shared_album_deleted_album, currentAlbum.albumName)
                trackButtonName = SharedAlbumTrackConstant.Value.DELETE
            } else {
                buttonText = getString(R.string.shared_album_quit_album)
                notNetworkText = getString(R.string.shared_album_no_network_unable_exit_album)
                networkErrorText = getString(R.string.shared_album_network_error_unable_exit_album)
                dialogTitle = getString(com.oplus.gallery.basebiz.R.string.base_exit_this_album, currentAlbum.albumName)
                dialogMessage = getString(com.oplus.gallery.basebiz.R.string.base_exit_this_album_description)
                dialogButtonText = BaseR.string.base_runtime_permission_runtime_cancel
                successText = getString(R.string.shared_album_exit_album, currentAlbum.albumName)
                trackButtonName = SharedAlbumTrackConstant.Value.QUITE
            }
        }
    }

    class SharedAlbumSettingPreferenceFragment : COUIPreferenceWithAppbarFragment() {

        private val mainViewModel: SharedAlbumMainViewModel by activityViewModels()
        private val viewModel: SharedAlbumSettingViewModel by lazy { createViewModel() }
        private var renameAlbumTitlePreference: COUIJumpPreference? = null
        private var albumFaqPreference: COUIRecommendedPreference? = null
        private var toolBarMarginAdjust: ISidePaneWithViewStateAdjust? = null

        override fun getTitle(): String {
            return getString(BaseR.string.base_permission_setting)
        }

        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            mainViewModel.onLoginStatusInfo.observe(this) {
                if (it.not()) {
                    GLog.w(TAG, "onCreate is not login")
                    exitSharedAlbum(this)
                }
            }
            mainViewModel.checkIsOnline().observe(this) {
                if (!it) {
                    ToastUtil.showLongToast(R.string.shared_album_state_enable)
                    exitSharedAlbum(this)
                }
            }
        }

        private fun createViewModel(): SharedAlbumSettingViewModel {
            return ViewModelProvider(requireParentFragment())[SharedAlbumSettingViewModel::class.java]
        }

        override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
            super.onViewCreated(view, savedInstanceState)
            toolbar.setNavigationOnClickListener {
                activity?.onBackPressed()
            }
            toolBarMarginAdjust?.bindView(toolbar)
            refreshBottomFaq()
        }

        override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
            super.onCreatePreferences(savedInstanceState, rootKey)
            initPreferences()
        }

        private fun initPreferences() {
            addPreferencesFromResource(R.xml.sharedalbum_setting_preference)
            renameAlbumTitlePreference = findPreference(KEY_RENAME_ALBUM_TITLE)
            albumFaqPreference = findPreference(KEY_ALBUM_FAQ)
            renameAlbumTitlePreference?.assignment = viewModel.currentAlbum?.albumName ?: TextUtil.EMPTY_STRING
        }

        @SuppressLint("FragmentLiveDataObserve")
        private fun refreshBottomFaq() {
            if (!LocaleUtils.isChinaMainland(requireContext())) {
                return
            }
            val observer = Observer<List<FetchDocResponse.ChildrenBean>> { data ->
                val list = data.map { item -> createRecommendedEntity(item) }
                if (list.isNotEmpty()) {
                    albumFaqPreference?.setData(list)
                }
            }
            viewModel.faqData.observe(this@SharedAlbumSettingPreferenceFragment, observer)
            viewModel.reqFaqData()
        }

        private fun createRecommendedEntity(item: FetchDocResponse.ChildrenBean): COUIRecommendedPreference.RecommendedEntity {
            return COUIRecommendedPreference.RecommendedEntity(item.content) {
                SharedAlbumTrackHelper.trackSettingPagerFaqClick(item.content, item.url, null, null)
                Starter.ActivityStarter(
                    context,
                    Bundle().apply {
                        putString(RouterConstants.RouterKey.KEY_URL, item.url ?: TextUtil.EMPTY_STRING)
                    },
                    PostCard(RouterConstants.RouterName.COMMON_WEB_ACTIVITY)
                ).start()
            }
        }

        override fun onPreferenceTreeClick(preference: Preference?): Boolean {
            if ((KEY_RENAME_ALBUM_TITLE == preference?.key) && ClickUtil.clickable()) {
                onActionRenameAlbum()
            }
            return false
        }

        private fun onActionRenameAlbum() {
            viewModel.trackSettingPagerBtnClick(SharedAlbumTrackConstant.Value.RENAME, null)
            SharedAlbumEditDialogHelper.showCreateDialog(requireContext(), viewModel.currentAlbum, null) {
                mainViewModel.onSharedAlbumChange(it)
                renameAlbumTitlePreference?.assignment = it.albumName
            }
        }

        fun setToolBarAdjuster(toolBarMarginAdjust: ISidePaneWithViewStateAdjust?) {
            this.toolBarMarginAdjust = toolBarMarginAdjust
        }

        override fun onDestroy() {
            super.onDestroy()
            toolBarMarginAdjust = null
        }

        companion object {
            const val TAG = "SharedAlbumSettingPreferenceFragment"
            private const val KEY_RENAME_ALBUM_TITLE = "rename_album_title"
            private const val KEY_ALBUM_FAQ = "shared_album_faq"
        }
    }

    companion object {
        private const val TAG = "SharedAlbumSettingFragment"
    }
}