/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - DynamicViewData
 ** Description:
 ** Version: 1.0
 ** Date : 2022/4/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/4/20        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.ui.viewdata

import com.oplus.sharealbum.bean.ActivityItem
import com.oplus.sharealbum.bean.InviteMsg

abstract class DynamicViewData(val viewType: Int, open val data: Any) {
    companion object {
        /**
         * 标题
         */
        const val VIEW_TYPE_TITLE = 101

        /**
         * 邀请消息
         */
        const val VIEW_TYPE_INVITE_MSG = 102

        /**
         * 查看更多
         */
        const val VIEW_TYPE_EXPAND = 103

        /**
         * 动态消息
         */
        const val VIEW_TYPE_ACTIVITY = 104
    }
}

data class TitleViewData(override var data: String) : DynamicViewData(VIEW_TYPE_TITLE, data)
data class InviteMsgViewData(override var data: InviteMsg) : DynamicViewData(VIEW_TYPE_INVITE_MSG, data)
data class ExpandViewData(override var data: Boolean) : DynamicViewData(VIEW_TYPE_EXPAND, data)
data class ActivityViewData(override var data: ActivityItem) : DynamicViewData(VIEW_TYPE_ACTIVITY, data)


