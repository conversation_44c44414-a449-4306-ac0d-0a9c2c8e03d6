/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - DynamicInviteRecordViewDataBinding
 ** Description: 动态消息
 ** Version: 1.0
 ** Date : 2025/3/26
 ** Author: zhogn<PERSON><PERSON>@Apps.Gallery
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  zhogn<PERSON><PERSON>@Apps.Gallery             2025/3/26        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.ui.binding

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.oplus.gallery.cloudsyncpage.sharedalbum.R
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.sharedalbumpage.ui.viewdata.ActivityViewData
import com.oplus.gallery.sharedalbumpage.ui.viewdata.BaseSharedAlbumViewData
import com.oplus.gallery.sharedalbumpage.ui.viewdata.DynamicViewData
import com.oplus.gallery.sharedalbumpage.util.format
import com.oplus.gallery.standard_lib.baselist.view.BaseListViewHolder
import com.oplus.gallery.standard_lib.baselist.view.ListViewDataBinding
import com.oplus.sharealbum.bean.ActivityItem
import org.json.JSONObject

class DynamicInviteRecordViewDataBinding(
    context: Context,
    val fragment: Fragment
) : ListViewDataBinding<BaseSharedAlbumViewData<DynamicViewData>>(context) {

    override fun onCreateView(parent: ViewGroup, viewType: Int): View {
        return LayoutInflater.from(context).inflate(R.layout.recycler_invite_record, parent, false)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseListViewHolder<BaseSharedAlbumViewData<DynamicViewData>> {
        view = onCreateView(parent, viewType)
        return InviteRecordViewHolder(view, this)
    }

    override fun onBindViewHolder(
        itemViewHolder: BaseListViewHolder<BaseSharedAlbumViewData<DynamicViewData>>,
        position: Int,
        viewData: BaseSharedAlbumViewData<DynamicViewData>?
    ) {
        (itemViewHolder as? InviteRecordViewHolder)?.bind(viewData?.data as? ActivityViewData)
    }

    override fun copyLayout(context: Context) =
        DynamicInviteRecordViewDataBinding(context, fragment)
}


class InviteRecordViewHolder(view: View, val dynamicViewDataBinding: DynamicInviteRecordViewDataBinding) :
    BaseListViewHolder<BaseSharedAlbumViewData<DynamicViewData>>(view, dynamicViewDataBinding) {

    private val inviteUsernameView = view.findViewById<TextView>(R.id.tv_invite_username)
    private val inviteContentView = view.findViewById<TextView>(R.id.tv_invite_content)
    private val inviteTimeView = view.findViewById<TextView>(R.id.tv_invite_time)
    private val imageView = view.findViewById<ImageView>(R.id.iv_invite_avatar)

    fun bind(viewData: ActivityViewData?) {
        viewData?.data?.apply {
            inviteTimeView.text = format(view.context, createTime)
            runCatching {
                msg?.let {
                    JSONObject(it)
                }?.let {
                    parseJson2BindData(this, it)
                }
            }.onFailure {
                GLog.e("InviteRecordViewHolder", "bind error: ${it.message}")
            }
        }
    }

    private fun parseJson2BindData(item: ActivityItem, obj: JSONObject) {
        val operator = obj.optJSONObject("operator")
        var operatorUserAvatar: String? = TextUtil.EMPTY_STRING
        var operatorUserName: String? = TextUtil.EMPTY_STRING
        if (null != operator) {
            operatorUserAvatar = operator.optString("image")
            operatorUserName = operator.optString("userName")
        }

        Glide.with(dynamicViewDataBinding.fragment)
            .load(operatorUserAvatar)
            .placeholder(R.drawable.album_ic_default_user)
            .error(R.drawable.album_ic_default_user)
            .into(imageView)

        inviteUsernameView.text = operatorUserName
        val album: JSONObject? = obj.optJSONObject("atlas")
        val albumName = album?.optString("atlasName") ?: TextUtil.EMPTY_STRING

        val ctx = itemView.context
        val content: String = when (item.activityType) {
            ActivityItem.TYPE_ACCEPT_INVITE -> ctx.getString(R.string.shared_album_joined, albumName)
            ActivityItem.TYPE_TICK_MEMBER -> ctx.getString(R.string.shared_album_moved_out, albumName)
            ActivityItem.TYPE_ACTIVE_EXIT -> ctx.getString(R.string.shared_album_from_exit, albumName)
            ActivityItem.TYPE_DISMISS_ATLAS -> ctx.getString(R.string.shared_album_disbanded, albumName)
            ActivityItem.TYPE_ADD_CONTENT -> ctx.getString(R.string.shared_album_upload_project_to, albumName)
            ActivityItem.TYPE_DELETE_CONTENT -> ctx.getString(R.string.shared_album_from_delete_project, albumName)
            ActivityItem.TYPE_CHANGE_ATTR -> {
                val changeType = obj.optInt("changeType")
                if (changeType == 1) {
                    val early = obj.optString("early")
                    val now = obj.optString("now")
                    ctx.getString(R.string.shared_album_rename_to, early, now)
                } else {
                    TextUtil.EMPTY_STRING
                }
            }

            else -> TextUtil.EMPTY_STRING
        }
        inviteContentView.text = content
    }
}