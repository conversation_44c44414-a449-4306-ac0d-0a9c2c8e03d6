/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumMainTabViewModel
 ** Description:
 ** Version: 1.0
 ** Date : 2022/5/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/5/26        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.gallery.business_lib.viewmodel.base.BaseViewModel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.ocloudsync.CloudSyncStatusManager
import com.oplus.gallery.sharedalbumpage.model.SharedAlbumDynamicRepository
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.sharealbum.bean.NewMsgResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SharedAlbumMainTabViewModel(application: Application) : BaseViewModel(application) {

    private val dynamicRepository by lazy { SharedAlbumDynamicRepository() }

    private val refreshAlbumLiveData = MutableLiveData<Boolean>()
    private val refreshDynamicLiveData = MutableLiveData<Boolean>()
    private val refreshRedPointLiveData = MutableLiveData<NewMsgResponse>()
    private val showCreateAlbumDialogLiveData = MutableLiveData<Boolean>()

    val refreshAlbumInfo: LiveData<Boolean> = refreshAlbumLiveData
    val refreshDynamicInfo: LiveData<Boolean> = refreshDynamicLiveData
    val refreshRedPointInfo: LiveData<NewMsgResponse> = refreshRedPointLiveData
    val showCreateAlbumDialogInfo: LiveData<Boolean> = showCreateAlbumDialogLiveData

    fun refreshAlbum() {
        refreshAlbumLiveData.postValue(true)
    }

    fun refreshDynamic() {
        refreshDynamicLiveData.postValue(true)
    }

    fun refreshRedPoint() {
        AppScope.launch(Dispatchers.IO) {
            dynamicRepository.queryNewMsg({
                if (it != null) {
                    refreshRedPointLiveData.postValue(it)
                } else {
                    GLog.e(TAG, "refreshRedPoint fail: res is null")
                }
            }, { code, msg ->
                GLog.e(TAG, "refreshRedPoint fail: code = $code, msg = $msg")
            })
        }
        //刷新图集tab页共享图集红点
        CloudSyncStatusManager.getInstance().notifySharedAlbumStatusChanged()
    }

    fun refreshCreateSharedAlbumDialog() {
        showCreateAlbumDialogLiveData.postValue(true)
    }

    companion object {
        private const val TAG = "SharedAlbumMainTabViewModel"
    }
}