/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - BaseSharedAlbumListFragment
 ** Description:
 ** Version: 1.0
 ** Date : 2022/3/31
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/31        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.ui

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.contains
import androidx.recyclerview.widget.COUILinearLayoutManager
import androidx.recyclerview.widget.GalleryGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.emptyview.COUIEmptyStateView
import com.oplus.gallery.basebiz.uikit.fragment.BaseFragment
import com.oplus.gallery.cloudsyncpage.sharedalbum.R
import com.oplus.gallery.foundation.ui.widget.pullrefresh.DefaultFooter
import com.oplus.gallery.foundation.ui.widget.pullrefresh.DefaultHeader
import com.oplus.gallery.foundation.ui.widget.pullrefresh.EventForwardingHelper
import com.oplus.gallery.foundation.ui.widget.pullrefresh.IPullHandler
import com.oplus.gallery.foundation.ui.widget.pullrefresh.PullCallBack
import com.oplus.gallery.foundation.ui.widget.pullrefresh.PullHandler
import com.oplus.gallery.foundation.ui.widget.pullrefresh.PullLayout
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.sharedalbumpage.ui.widget.SharedAlbumDividerDecoration
import com.oplus.gallery.sharedalbumpage.ui.widget.SharedAlbumLoadingView
import com.oplus.gallery.sharedalbumpage.viewmodel.BaseSharedAlbumListViewModel
import com.oplus.gallery.standard_lib.baselist.adapter.config.AbsAdapterConfigProvider
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.gallery.standard_lib.baselist.view.BaseListAdapter
import com.oplus.gallery.standard_lib.baselist.view.BaseListItemAnimator
import com.oplus.gallery.standard_lib.baselist.view.GridItemGapDecoration
import com.oplus.gallery.standard_lib.baselist.view.ItemClickListener
import com.oplus.gallery.standard_lib.baselist.view.OnAnimationListener
import com.oplus.gallery.standard_lib.bean.ViewData
import com.oplus.gallery.standard_lib.ui.util.ClickUtil
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.sharealbum.bean.SharedAlbumResult
import com.oplus.gallery.basebiz.R as BaseR

abstract class BaseSharedAlbumTabListFragment<TData, TViewData : ViewData, VM : BaseSharedAlbumListViewModel<TData, TViewData>> :
    BaseFragment(),
    ItemClickListener<TViewData>,
    IPullHandler {

    private val emptyPageView: COUIEmptyStateView by lazy {
        COUIEmptyStateView(requireContext())
    }

    protected var recyclerAdapter: BaseListAdapter<TViewData>? = null
    protected var layoutDetail: LayoutDetail? = null

    private var root: FrameLayout? = null
    private var loadingView: SharedAlbumLoadingView? = null
    private var bounceLayout: PullLayout? = null
    private var pullDownRefreshRoot: FrameLayout? = null
    protected var recyclerView: RecyclerView? = null
    protected var baseListViewModel: VM? = null

    protected var currentPageStatus = PAGE_STATUS_DEFAULT

    private val bounceHandler: PullHandler by lazy {
        PullHandler()
    }

    override fun getLayoutId(): Int = R.layout.base_fragment_shared_album_tab_list

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)

        baseListViewModel = onCreateViewModel()

        root = view.findViewById(R.id.base_fragment_shared_album_root)
        loadingView = view.findViewById(R.id.loadingView)
        bounceLayout = view.findViewById(R.id.bounce_layout)
        pullDownRefreshRoot = view.findViewById(R.id.pull_down_refresh_root)
        recyclerView = view.findViewById(R.id.recycler_view)
        layoutDetail = initLayoutDetail()
        recyclerAdapter = getListAdapter()

        initBounceLayout()
        initRecyclerView()

        recyclerAdapter?.itemClickListener = object : ItemClickListener<TViewData> {
            override fun onItemClick(position: Int, viewData: TViewData?, clickType: Int, itemView: View?) {
                if (ClickUtil.clickable()) {
                    <EMAIL>(position, viewData, clickType, itemView)
                }
            }
        }
        firstLoadData()

        baseListViewModel?.let { vm ->
            vm.viewDataLiveData?.observe(this) { data ->
                val newData = data ?: emptyList()
                recyclerAdapter?.let {
                    it.setDataSet(data = newData, refresh = true)
                    if (it.totalCount != newData.size) {
                        it.totalCount = newData.size
                        it.notifyDataSetChanged()
                    }
                }

                bounceLayout?.setRefreshCompleted()
                if (data.isNullOrEmpty()) {
                    showEmptyView()
                } else {
                    hideNoDataEmptyView()
                }
                onLoadDataFinish(data)
            }

            vm.errorCodeListData?.observe(this) {
                if (it == SharedAlbumResult.NETWORK_ERROR) {
                    showWeakNetView()
                } else {
                    showErrorView()
                }
                bounceLayout?.setRefreshCompleted()
            }

            vm.coverRefreshLiveData?.observe(this) {
                recyclerAdapter?.setDataSet(data = it ?: emptyList(), refresh = true)
            }

            vm.isNetworkAvailable?.observe(this) {
                if ((currentPageStatus == PAGE_STATUS_NO_NET_WORK) && it) {
                    refreshData()
                }
            }
        }
    }

    open fun onLoadDataFinish(dataList: List<TViewData>?) {
    }

    open fun firstLoadData() {
        refreshData()
    }

    protected open fun getLayoutManager(
        recyclerView: RecyclerView,
        context: Context,
        layoutDetail: LayoutDetail
    ): RecyclerView.LayoutManager = when {
        (layoutDetail is GridLayoutDetail) -> GalleryGridLayoutManager(recyclerView, layoutDetail)
        else -> COUILinearLayoutManager(context)
    }

    private fun initBounceLayout() {
        val headerViewHeight = resources.getDimensionPixelSize(R.dimen.default_height)
        val headerTopPadding = resources.getDimensionPixelOffset(com.oplus.gallery.foundation.ui.R.dimen.pull_refresh_head_top_padding)
        val headerBottomPadding = resources.getDimensionPixelOffset(com.oplus.gallery.foundation.ui.R.dimen.pull_refresh_head_bottom_padding)
        val headerTopMargin = headerViewHeight + headerTopPadding

        val footerViewHeight = resources.getDimensionPixelSize(com.oplus.gallery.foundation.ui.R.dimen.default_footer_loading_height)
        val footerTopPadding = headerTopPadding
        val footerBottomPadding = headerBottomPadding
        val footerTopMargin = footerViewHeight + footerTopPadding

        val listMaxTop = resources.getDimensionPixelOffset(com.oplus.gallery.foundation.ui.R.dimen.pull_refresh_down_fragment_max_drag_distance)

        bounceLayout?.apply {
            setPullHandler(this@BaseSharedAlbumTabListFragment, bounceLayout?.getChildAt(0))
            setHeaderView(DefaultHeader(requireContext()), pullDownRefreshRoot)
            setFooterView(DefaultFooter(requireContext()), pullDownRefreshRoot)
            setEventForwardingHelper(object : EventForwardingHelper {
                override fun notForwarding(downX: Float, downY: Float, moveX: Float, moveY: Float): Boolean {
                    return true
                }
            })
            setPullCallBack(object : PullCallBack {
                override fun startRefresh() {
                    GLog.d(TAG, "initBounceLayout, startRefresh")
                    refreshData()
                }

                override fun startLoadingMore() {
                    GLog.d(TAG, "initBounceLayout, startLoadingMore")
                    loadMoreData()
                }
            })

            headerDragDistanceThreshold = headerViewHeight + headerBottomPadding + headerTopPadding
            footerDragDistanceThreshold = footerViewHeight + footerBottomPadding + footerTopPadding
            maxDragDistance = listMaxTop
        }

        (pullDownRefreshRoot?.layoutParams as? FrameLayout.LayoutParams)?.apply {
            topMargin = -headerTopMargin
            bottomMargin = -footerTopMargin
        }
    }

    override fun canChildPull(v: View): Boolean {
        return canPullUpRefresh() && bounceHandler.canChildPull(v)
    }

    override fun canChildDrag(v: View): Boolean {
        return canPullDownRefresh() && bounceHandler.canChildDrag(v)
    }

    open fun canPullUpRefresh(): Boolean {
        return baseListViewModel?.let {
            (!it.isReloading && !it.isComplete())
        } ?: false
    }

    open fun canPullDownRefresh(): Boolean {
        return baseListViewModel?.isReloading != true
    }

    open fun initRecyclerView() {
        recyclerAdapter?.let { recyclerAdapter ->
            recyclerView?.apply {
                isMotionEventSplittingEnabled = false
                layoutDetail?.let { layoutDetail -> layoutManager = getLayoutManager(this, context, layoutDetail) }
                addItemDecoration()
                adapter = recyclerAdapter
                itemAnimator = BaseListItemAnimator().also { animator ->
                    animator.addAnimationListener(object : OnAnimationListener {
                        override fun onAnimationFinished() {
                            recyclerAdapter.setDataSet(refresh = true)
                        }
                    })
                }
            }
        }
    }

    open fun addItemDecoration() {
        val itemDecoration = when {
            (layoutDetail is GridLayoutDetail) -> GridItemGapDecoration(layoutDetail as GridLayoutDetail)
            else -> layoutDetail?.let { SharedAlbumDividerDecoration(requireContext()) }
        }
        itemDecoration?.let {
            recyclerView?.let { recyclerView ->
                recyclerView.addItemDecoration(itemDecoration)
                recyclerView.addItemDecoration(object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                        val position = parent.getChildAdapterPosition(view)
                        val itemCount = parent.adapter?.itemCount ?: 0
                        if (itemCount > 0 && position == itemCount - 1) {
                            outRect.bottom = bottomNaviBarHeight()
                        } else {
                            outRect.bottom = 0
                        }
                    }
                })
            }
        }
    }

    open fun getListAdapter(): BaseListAdapter<TViewData>? {
        return layoutDetail?.let {
            BaseListAdapter(
                it,
                object : AbsAdapterConfigProvider() {
                    override val configs: MutableList<ItemConfig<*>> = mutableListOf<ItemConfig<*>>().apply {
                        addAll(getAdapterItemConfigs())
                    }
                })
        }
    }

    /**
     * 按item类型,配置对应的viewDataBinding
     * 如：AlbumViewData->AlbumViewDataBinding,
     *    CardCaseBannerViewData->CardCaseBannerViewDataBinding
     * @return List<ItemConfig<TViewData>>
     */
    abstract fun getAdapterItemConfigs(): List<ItemConfig<TViewData>>

    /**
     * 初始化LayoutDetail
     */
    abstract fun initLayoutDetail(): LayoutDetail

    /**
     * 创建viewModel
     */
    abstract fun onCreateViewModel(): VM

    open fun refreshLayoutManager() {
        // grid更新网格列数
        (recyclerView?.layoutManager as? GridLayoutManager)?.let {
            val newLayoutDetail = initLayoutDetail()
            layoutDetail?.apply {
                spanCount = newLayoutDetail.spanCount
                itemWidth = newLayoutDetail.itemWidth
                (this as? GridLayoutDetail)?.replaceGaps((newLayoutDetail as? GridLayoutDetail)?.itemHorizontalGaps)
            }
            removeAllItemDecorations()
            addItemDecoration()
            it.spanCount = newLayoutDetail.spanCount
        }
    }

    private fun removeAllItemDecorations() {
        recyclerView?.takeIf { it.itemDecorationCount > 0 }?.let {
            for (index in it.itemDecorationCount - 1 downTo 0) {
                it.removeItemDecoration(it.getItemDecorationAt(index))
            }
        }
    }

    fun refreshData() {
        baseListViewModel?.let { vm ->
            if (vm.isReloading) {
                return
            }
            if (NetworkMonitor.isNetworkValidated()) {
                vm.refresh()
            } else {
                val hasData = recyclerAdapter?.let { it.itemCount > 0 }
                if (hasData == true) {
                    ToastUtil.showShortToast(R.string.shared_album_no_network_load_failed)
                } else {
                    showNoNetWorkView()
                }
                bounceLayout?.let {
                    it.post {
                        it.setRefreshCompleted()
                    }
                }
            }
        }
    }

    fun loadMoreData() {
        if (baseListViewModel?.isReloading == true) {
            return
        }

        if (NetworkMonitor.isNetworkValidated()) {
            baseListViewModel?.loadMore()
        } else {
            ToastUtil.showShortToast(R.string.shared_album_no_network_load_failed)
            bounceLayout?.post {
                bounceLayout?.setRefreshCompleted()
            }
        }
    }

    open fun isSupportEmptyPage(): Boolean = true

    open fun onEmptyViewClick() {
        refreshData()
    }

    open fun onErrorViewClick(pageStatus: Int) {
        if (pageStatus == PAGE_STATUS_NO_NET_WORK) {
            //无网络跳转到wifi设置页
            jumpToWifiSetting()
        } else {
            refreshData()
        }
    }

    private fun jumpToWifiSetting() {
        val intent = Intent()
        runCatching {
            intent.action = Settings.ACTION_WIFI_SETTINGS
            startActivity(intent)
        }.onFailure { t ->
            GLog.e(TAG, "jumpToWifiSetting fail: ${t.message}")
            runCatching {
                intent.action = Settings.ACTION_WIRELESS_SETTINGS
                startActivity(intent)
            }.onFailure { t1 ->
                GLog.e(TAG, "jumpToWifiSetting fail: ${t1.message}")
            }
        }
    }

    fun showLoadingView() {
        loadingView?.apply {
            visibility = View.VISIBLE
            bringToFront()
        }
    }

    fun hideLoadingView() {
        loadingView?.visibility = View.GONE
    }

    /**
     * 显示空页面
     */
    fun showEmptyView() {
        if (isSupportEmptyPage()) {
            modifiedStyleToEmpty(emptyPageView)
            bounceLayout?.visibility = View.GONE
            currentPageStatus = PAGE_STATUS_EMPTY
            addEmptyPageViewAndPlay()
        }
    }

    /**
     * 显示无网络
     */
    fun showNoNetWorkView() {
        hideLoadingView()
        if (isSupportEmptyPage() && (currentPageStatus != PAGE_STATUS_NO_NET_WORK)) {
            modifiedStyleToNoNetwork(emptyPageView)
            bounceLayout?.visibility = View.GONE
            currentPageStatus = PAGE_STATUS_NO_NET_WORK
            addEmptyPageViewAndPlay()
        } else {
            ToastUtil.showShortToast(R.string.shared_album_no_network_load_failed)
        }
    }

    /**
     * 显示服务异常
     */
    fun showErrorView() {
        if (isSupportEmptyPage() && (currentPageStatus != PAGE_STATUS_SERVICE_ERROR)) {
            modifiedStyleToError(emptyPageView)
            bounceLayout?.visibility = View.GONE
            currentPageStatus = PAGE_STATUS_SERVICE_ERROR
            addEmptyPageViewAndPlay()
        } else {
            ToastUtil.showShortToast(R.string.shared_album_service_error)
        }
    }

    /**
     * 显示网络异常
     */
    fun showWeakNetView() {
        if (isSupportEmptyPage() && (currentPageStatus != PAGE_STATUS_WEAK_NET)) {
            modifiedStyleToWeakNet(emptyPageView)
            bounceLayout?.visibility = View.GONE
            currentPageStatus = PAGE_STATUS_WEAK_NET
            addEmptyPageViewAndPlay()
        } else {
            ToastUtil.showShortToast(R.string.shared_album_network_error)
        }
    }

    private fun addEmptyPageViewAndPlay() {
        if (root?.contains(emptyPageView) == true) {
            emptyPageView.bringToFront()
        } else {
            root?.addView(emptyPageView, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))
        }
        emptyPageView.playAnimation()
    }

    fun hideNoDataEmptyView() {
        bounceLayout?.visibility = View.VISIBLE
        currentPageStatus = PAGE_STATUS_DEFAULT
        if (!isSupportEmptyPage()) {
            return
        }
        root?.removeView(emptyPageView)
    }

    protected open fun modifiedStyleToNoNetwork(emptyView: COUIEmptyStateView) {
        emptyView.apply {
            setAnimRes(BaseR.raw.base_empty_view_network)
            titleText = getString(com.oplus.gallery.basebiz.R.string.base_sync_download_toast_disconnect)
            actionText = getString(com.oplus.gallery.basebiz.R.string.base_permission_setting)
            setOnButtonClickListener {
                if (ClickUtil.clickable()) {
                    onErrorViewClick(PAGE_STATUS_NO_NET_WORK)
                }
            }
        }
    }

    protected open fun modifiedStyleToEmpty(emptyView: COUIEmptyStateView) {
        emptyView.apply {
            setAnimRes(BaseR.raw.base_empty_view_no_group)
            titleText = getString(R.string.shared_album_no_album)
            subtitleText = getString(R.string.shared_album_timeline_empty_summary)
            actionText = getString(BaseR.string.base_create_local_album)
            setOnButtonClickListener {
                if (ClickUtil.clickable()) {
                    onEmptyViewClick()
                }
            }
        }
    }

    protected open fun modifiedStyleToError(emptyView: COUIEmptyStateView) {
        emptyView.apply {
            setAnimRes(BaseR.raw.base_empty_view_loading)
            titleText = getString(R.string.shared_album_service_error)
            actionText = getString(R.string.shared_album_sync_retry)
            setOnButtonClickListener {
                if (ClickUtil.clickable()) {
                    onErrorViewClick(PAGE_STATUS_SERVICE_ERROR)
                }
            }
        }
    }

    protected open fun modifiedStyleToWeakNet(emptyView: COUIEmptyStateView) {
        emptyView.apply {
            setAnimRes(BaseR.raw.base_empty_view_network)
            titleText = getString(R.string.shared_album_network_error)
            actionText = getString(R.string.shared_album_sync_retry)
            setOnButtonClickListener {
                if (ClickUtil.clickable()) {
                    onErrorViewClick(PAGE_STATUS_WEAK_NET)
                }
            }
        }
    }

    companion object {
        private const val TAG = "BaseSharedAlbumListFragment"

        /**
         * 默认状态
         */
        const val PAGE_STATUS_DEFAULT = 100

        /**
         * 空状态
         */
        const val PAGE_STATUS_EMPTY = 101

        /**
         * 服务异常状态
         */
        const val PAGE_STATUS_SERVICE_ERROR = 102

        /**
         * 无网络状态
         */
        const val PAGE_STATUS_NO_NET_WORK = 103

        /**
         * 网络异常状态
         */
        const val PAGE_STATUS_WEAK_NET = 104
    }
}