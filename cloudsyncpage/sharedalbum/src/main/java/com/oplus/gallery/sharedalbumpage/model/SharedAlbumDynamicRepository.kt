/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumDynamicRepository
 ** Description:
 ** Version: 1.0
 ** Date : 2022/4/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/4/20        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.model

import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.sharealbum.bean.NewMsgResponse
import com.oplus.sharealbum.bean.QueryActivityResponse
import com.oplus.sharealbum.bean.ReportActivityReadLastResponse
import com.oplus.sharealbum.bean.ReportActivityReadLastRequest
import com.oplus.sharealbum.bean.SharedAlbumRequest
import com.oplus.sharealbum.bean.SharedAlbumResult
import com.oplus.sharealbum.net.ApiResponse
import com.oplus.sharealbum.net.SharedAlbumHttpManager
import com.oplus.sharealbum.net.executeExt
import com.oplus.sharealbum.service.ISharedAlbumService
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import retrofit2.Response

class SharedAlbumDynamicRepository {

    fun queryActivityList(
        successCallback: (QueryActivityResponse) -> Unit,
        errorCallback: (Int, String?) -> Unit
    ) {
        val service = SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
        val mergeResponse = runBlocking {
            val allActivity = async {
                runCatching {
                    service.queryAllActivity(SharedAlbumRequest()).execute()
                }.getOrNull()
            }
            val unprocessedInviteMsg = async {
                runCatching {
                    service.queryUnprocessedInviteMsg(SharedAlbumRequest()).execute()
                }.getOrNull()
            }
            mergeResponse(allActivity.await(), unprocessedInviteMsg.await())
        }

        if (mergeResponse == null) {   //fail
            errorCallback.invoke(SharedAlbumResult.DEFAULT_ERROR, TextUtil.EMPTY_STRING)
        } else {    //success
            successCallback.invoke(mergeResponse)
        }
    }


    private fun mergeResponse(
        allActivityResponse: Response<ApiResponse<QueryActivityResponse>>?,
        unprocessedInviteMsgResponse: Response<ApiResponse<QueryActivityResponse>>?
    ): QueryActivityResponse? {
        val allActivityData = allActivityResponse.let {
            if (it?.isSuccessful == true && it.body()?.isSuccess == true) {
                it.body()!!.data
            } else {
                null
            }
        }
        val unprocessedInviteMsgData = unprocessedInviteMsgResponse.let {
            if (it?.isSuccessful == true && it.body()?.isSuccess == true) {
                it.body()!!.data
            } else {
                null
            }
        }

        return if (allActivityData == null && unprocessedInviteMsgData == null) {
            //为空表示失败
            null
        } else {
            QueryActivityResponse(unprocessedInviteMsgData?.msgs, allActivityData?.albumIds, allActivityData?.activities)
        }
    }

    /**
     * 是否有新消息
     */
    fun queryNewMsg(successCallback: (NewMsgResponse?) -> Unit, errorCallback: (Int, String?) -> Unit) {
        SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
            .hasNewMsg(SharedAlbumRequest())
            .executeExt(successCallback, errorCallback)
    }

    /**
     * 消息上报已读
     */
    fun reportActivityReadLast(
        albumIds: Set<String>,
        last: Long,
        successCallback: (ReportActivityReadLastResponse?) -> Unit,
        errorCallback: (Int, String?) -> Unit
    ) {
        val req = ReportActivityReadLastRequest(albumIds, last)
        SharedAlbumHttpManager.instance.create(ISharedAlbumService::class.java)
            .reportActivityReadLast(req)
            .executeExt(successCallback, errorCallback)
    }
}