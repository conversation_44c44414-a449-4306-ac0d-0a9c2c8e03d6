/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumListFragment
 ** Description:
 ** Version: 1.0
 ** Date : 2022/3/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>             2022/3/11        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.ui

import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.uikit.fragment.IClickSelectedTabAgainCallback
import com.oplus.gallery.basebiz.util.AlbumCardListUtils
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.gallery.cloudsyncpage.sharedalbum.R
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.sharedalbumpage.ext.showAlbumMediaPage
import com.oplus.gallery.sharedalbumpage.ui.binding.SharedAlbumViewDataBinding
import com.oplus.gallery.sharedalbumpage.ui.viewdata.SharedAlbumViewData
import com.oplus.gallery.sharedalbumpage.viewmodel.OnSharedAlbumChangeListener
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumListViewModel
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumMainTabViewModel
import com.oplus.gallery.sharedalbumpage.viewmodel.SharedAlbumMainViewModel
import com.oplus.gallery.standard_lib.baselist.adapter.config.ItemConfig
import com.oplus.gallery.standard_lib.baselist.bean.GridLayoutDetail
import com.oplus.gallery.standard_lib.baselist.bean.LayoutDetail
import com.oplus.sharealbum.track.SharedAlbumTrackConstant
import com.oplus.sharealbum.track.SharedAlbumTrackHelper
import com.oplus.gallery.basebiz.R as BaseR

@RouterNormal(RouterConstants.RouterName.SHARED_ALBUM_HOME_TAB_FRAGMENT)
open class SharedAlbumListFragment :
    BaseSharedAlbumTabListFragment<SharedAlbumResponse, SharedAlbumViewData, SharedAlbumListViewModel>(), OnSharedAlbumChangeListener,
    IClickSelectedTabAgainCallback {

    private var pageStartTime: Long = 0

    private val mainViewModel by activityViewModels<SharedAlbumMainViewModel>()
    private val mainTabViewModel by lazy {
        parentFragment?.let {
            ViewModelProvider(it).get(SharedAlbumMainTabViewModel::class.java)
        }
    }

    override fun onCreateViewModel(): SharedAlbumListViewModel {
        return ViewModelProvider(this).get(SharedAlbumListViewModel::class.java)
    }

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        mainViewModel.addOnSharedAlbumChangeListener(this)

        mainTabViewModel?.refreshAlbumInfo?.observe(this) {
            if (it) {
                GLog.d(TAG, "afterViewCreated, refreshAlbumInfo")
                refreshData()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mainViewModel.removeOnSharedAlbumChangeListener(this)
    }

    override fun onSharedAlbumCreate(album: SharedAlbumResponse) {
        baseListViewModel?.addAlbumToList(album)
    }

    override fun onSharedAlbumDelete(albumId: String) {
        baseListViewModel?.deleteAlbumFromList(albumId)
    }

    override fun onSharedAlbumChange(album: SharedAlbumResponse) {
        baseListViewModel?.updateAlbumFromList(album)
    }

    override fun firstLoadData() {
        showLoadingView()
        baseListViewModel?.refresh()
    }

    override fun onLoadDataFinish(dataList: List<SharedAlbumViewData>?) {
        super.onLoadDataFinish(dataList)
        hideLoadingView()
    }

    override fun initLayoutDetail(): LayoutDetail = GridLayoutDetail.HorizontalGapsBuilder().apply {
        parentWidth = getCurrentAppUiConfig().windowWidth.current
        edgeWidth = resources.getDimensionPixelOffset(BaseR.dimen.card_album_recyclerView_edge)
        gapWidth = resources.getDimensionPixelOffset(R.dimen.shared_album_tab_gap_in_my_album_fragment)
        spanCount = AlbumCardListUtils.getSpanCount(context, parentWidth)
    }.build().apply {
        itemDecorationGapPx.top = resources.getDimension(R.dimen.shared_album_item_gap_top_in_my_album)
        itemDecorationGapPx.bottom = resources.getDimension(R.dimen.shared_album_item_gap_bottom_in_my_album)
    }

    override fun getAdapterItemConfigs(): List<ItemConfig<SharedAlbumViewData>> {
        return mutableListOf<ItemConfig<SharedAlbumViewData>>().apply {
            context?.let { context ->
                add(
                    ItemConfig(
                        SharedAlbumViewData::class.java,
                        SharedAlbumViewDataBinding(context, this@SharedAlbumListFragment)
                    )
                )
            }
        }
    }

    override fun onEmptyViewClick() {
        mainTabViewModel?.refreshCreateSharedAlbumDialog()
    }

    override fun onItemClick(position: Int, viewData: SharedAlbumViewData?, clickType: Int, itemView: View?) {
        val album = viewData?.data
        if (null == album) {
            GLog.e(TAG, "onItemClick album is null")
            return
        }
        showAlbumMediaPage(album)
    }

    override fun onResume() {
        super.onResume()
        pageStartTime = System.currentTimeMillis()
    }

    override fun onPause() {
        super.onPause()
        val pageStayTime = System.currentTimeMillis() - pageStartTime
        val isBlank = (currentPageStatus == PAGE_STATUS_EMPTY)
        SharedAlbumTrackHelper.trackAlbumsPage(SharedAlbumTrackConstant.Value.ALBUM_TAB_SHARED_ALBUM, isBlank, pageStayTime)
    }

    override fun onClickSelectedTabAgain() {
        recyclerView?.smoothScrollToPosition(0)
    }

    companion object {
        private const val TAG = "SharedAlbumListFragment"

        private const val SPAN_COUNT = 3
    }
}