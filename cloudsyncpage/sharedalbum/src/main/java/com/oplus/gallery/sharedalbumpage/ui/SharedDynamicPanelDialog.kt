/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -
 ** Description: 共享图集-动态面板
 **
 ** Version: 1.0
 ** Date: 2025/4/15
 ** Author: 80407954@OppoGallery3D
 ** TAG: xx
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80407954@OppoGallery3D  2025/4/15  1.0        create this class
 *********************************************************************************/
package com.oplus.gallery.sharedalbumpage.ui

import android.os.Bundle
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.standard_lib.ui.panel.PanelDialog
import com.oplus.gallery.standard_lib.ui.panel.PanelFragment
import com.support.appcompat.R as appcompatR

/**
 * 共享-动态面板
 */
@RouterNormal(path = RouterConstants.RouterName.SHARED_DYNAMIC_PANEL_DIALOG)
class SharedDynamicPanelDialog : PanelDialog(), PanelDialog.PanelDialogDismissCallback {
    init {
        // 设置panel始终最大高度显示
        setIsShowInMaxHeight(true)
    }

    private val sharedAlbumDynamicFragment by lazy { SharedAlbumDynamicFragment.newInstance(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setPanelFragment(
            PanelFragment.Builder()
                .setContentFragment(sharedAlbumDynamicFragment)
                .setBackgroundColor(COUIContextUtil.getAttrColor(context, appcompatR.attr.couiColorBackgroundElevatedWithCard))
                .setNavColor(COUIContextUtil.getAttrColor(context, appcompatR.attr.couiColorBackgroundElevatedWithCard))
                .create()
        )
    }

    override fun dialogClosed() {
        dialog?.dismiss()
    }
}
