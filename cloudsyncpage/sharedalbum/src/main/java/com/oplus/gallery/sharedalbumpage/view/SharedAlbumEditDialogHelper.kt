/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - SharedAlbumEditDialogHelper
 ** Description:
 ** Version: 1.0
 ** Date : 2022/3/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  chenjie@A<PERSON>.KeKeCloud            2022/3/30        1.0             Add
 **************************************************************************************************/
package com.oplus.gallery.sharedalbumpage.view

import android.content.Context
import android.content.DialogInterface
import android.text.InputFilter
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.widget.doOnTextChanged
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUIInputView
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.R as BaseR
import com.oplus.gallery.cloudsyncpage.sharedalbum.R
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.business_lib.sharedalbum.SharedAlbumResponse
import com.oplus.gallery.foundation.ui.dialog.ConfirmDialog
import com.oplus.sharealbum.bean.SharedAlbumResult
import com.oplus.sharealbum.helper.SharedAlbumConfigHelper
import com.oplus.sharealbum.net.ApiResponse
import com.oplus.sharealbum.track.SharedAlbumTrackConstant
import com.oplus.sharealbum.track.SharedAlbumTrackHelper
import com.oplus.sharealbum.usecase.SharedAlbumCreateUserCase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 共享图集创建及重命名弹窗
 */
object SharedAlbumEditDialogHelper {

    private const val TAG = "SharedAlbumEditDialogHelper"
    private const val MAX_INPUT_LENGTH = 15
    private var dialog: AlertDialog? = null

    fun dismissDialog() {
        if (dialog?.isShowing == true) {
            dialog?.dismiss()
        }
        dialog = null
    }

    /**
     * 显示创建共享图集弹窗
     * @param response 当前图集信息，为null表示创建新图集，不为null表示重命名图集标题
     */
    fun showCreateDialog(context: Context, response: SharedAlbumResponse?, trackPageId: String?, callback: (SharedAlbumResponse) -> Unit) {
        if (!trackPageId.isNullOrEmpty()) {
            // 新建图集按钮点击事件
            SharedAlbumTrackHelper.trackCreateAlbumBtnClick(trackPageId)
        }
        val builder = COUIAlertDialogBuilder(context, R.style.SharedAlbumEditDialog).apply {
            if (null != response) {
                setTitle(R.string.shared_album_rename_album)
                setPositiveButton(BaseR.string.common_save, null)
            } else {
                setTitle(R.string.shared_album_new_album_share_title)
                setPositiveButton(BaseR.string.base_album_selection_next, null)
            }
            setNegativeButton(BaseR.string.base_all_file_permission_refuse, null)
        }
        dialog = builder.show()
        initButton(dialog, response, trackPageId, callback)
        initEditText(dialog, response)
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }

    private fun initEditText(alertDialog: AlertDialog?, res: SharedAlbumResponse?) {
        val inputView = alertDialog?.findViewById<COUIInputView>(R.id.input_first)
        inputView?.editText?.let {
            if (null != res && !res.albumName.isNullOrEmpty()) {
                var albumName: CharSequence = res.albumName!!
                if (albumName.length > MAX_INPUT_LENGTH) {
                    albumName = albumName.subSequence(0, MAX_INPUT_LENGTH)
                }
                it.setText(albumName)
                it.setSelection(albumName.length)
            }
            val emojiString = alertDialog.context.getString(BaseR.string.common_toast_file_name_illegal)
            val illegalString =
                alertDialog.context.resources.getString(BaseR.string.common_invalid_character) + "\n \\ / : * ? \" < > |"
            TextUtil.addEmojiInputFilter(it, emojiString)
            TextUtil.addIllgalFileNameInputFilter(it, illegalString)
            TextUtil.addInputFilter(it, InputFilter.LengthFilter(MAX_INPUT_LENGTH))
            it.doOnTextChanged { text, _, _, _ ->
                val enable = !text.isNullOrEmpty()
                alertDialog.getButton(DialogInterface.BUTTON_POSITIVE)?.isEnabled = enable
            }
            it.isFocusable = true
            it.requestFocus()
        }
    }

    private fun initButton(
        alertDialog: AlertDialog?,
        res: SharedAlbumResponse?,
        trackPageId: String?,
        callback: (SharedAlbumResponse) -> Unit
    ) {
        alertDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.let {
            it.isEnabled = null != res
            it.setOnClickListener { onDialogPositiveButtonClick(alertDialog, res, trackPageId, callback) }
        }
        alertDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.let {
            it.setOnClickListener {
                alertDialog.dismiss()
                if (!trackPageId.isNullOrEmpty()) {
                    SharedAlbumTrackHelper.trackCreateAlbumPagerClick(trackPageId, SharedAlbumTrackConstant.Value.CANCEL, "")
                }
            }
        }
    }

    private fun onDialogPositiveButtonClick(
        alertDialog: AlertDialog,
        res: SharedAlbumResponse?,
        trackPageId: String?,
        callback: (SharedAlbumResponse) -> Unit
    ) {
        val inputView = alertDialog.findViewById<COUIInputView>(R.id.input_first)
        val editable = inputView?.editText?.text
        val content = editable?.toString()?.trim() ?: TextUtil.EMPTY_STRING
        if (content.isEmpty()) {
            return
        }
        // 判断是否更改了图集名称
        if (content == res?.albumName) {
            alertDialog.dismiss()
            callback(res)
            return
        }
        // 判断网络状态是否连接
        if (!NetworkMonitor.isNetworkConnected()) {
            if (null == res) {
                ToastUtil.showShortToast(R.string.shared_album_no_network_next)
            } else {
                ToastUtil.showShortToast(R.string.shared_album_no_network_save)
            }
            return
        }
        // 判断网络状态是否可用
        if (!NetworkMonitor.isNetworkValidated()) {
            if (null == res) {
                ToastUtil.showShortToast(R.string.shared_album_network_error_next)
            } else {
                ToastUtil.showShortToast(R.string.shared_album_network_error_save)
            }
            return
        }
        // 弹窗按钮进入提交状态且不可点击
        updateButton(alertDialog, null == res, false)
        AppScope.launch {
            withContext(Dispatchers.IO) {
                val userCase = SharedAlbumCreateUserCase { onResult(alertDialog, null == res, it, trackPageId, callback) }
                if (null == res) {
                    userCase.create(content)
                } else {
                    userCase.update(res, content)
                }
            }
        }
    }

    private fun updateButton(alertDialog: AlertDialog, isCreated: Boolean, enable: Boolean) {
        alertDialog.getButton(DialogInterface.BUTTON_POSITIVE)?.let {
            it.isEnabled = enable
            if (enable) {
                it.setText(if (isCreated) BaseR.string.base_album_selection_next else BaseR.string.common_save)
            } else {
                it.setText(R.string.shared_album_is_submitting)
            }
        }
        alertDialog.getButton(DialogInterface.BUTTON_NEGATIVE)?.isEnabled = enable
    }

    private fun onResult(
        dialog: AlertDialog,
        isCreated: Boolean,
        result: ApiResponse<SharedAlbumResponse>,
        trackPageId: String?,
        callback: (SharedAlbumResponse) -> Unit
    ) {
        AppScope.launch(Dispatchers.Main) {
            GLog.d(TAG, "onResult code = ${result.code}")
            if (!result.isSuccess) {
                updateButton(dialog, isCreated, true)
            }
            if (!trackPageId.isNullOrEmpty()) {
                val opResult = if (result.isSuccess) SharedAlbumTrackConstant.Value.SUCCESS else "${result.code}"
                SharedAlbumTrackHelper.trackCreateAlbumPagerClick(trackPageId, SharedAlbumTrackConstant.Value.SAVE, opResult)
            }
            when (result.code) {
                SharedAlbumResult.SUCCESS -> {
                    result.data?.let { callback(it) }
                    dialog.dismiss()
                }
                SharedAlbumResult.TIME_OUT_ERROR -> ToastUtil.showShortToast(R.string.shared_album_submission_timed_out_retry)
                SharedAlbumResult.NETWORK_ERROR -> {
                    if (isCreated) {
                        ToastUtil.showShortToast(R.string.shared_album_network_error_next)
                    } else {
                        ToastUtil.showShortToast(R.string.shared_album_network_error_save)
                    }
                }
                SharedAlbumResult.NAME_REVIEW_FAILED -> {
                    val inputView = dialog.findViewById<COUIInputView>(R.id.input_first)
                    inputView?.showError(dialog.context.getString(R.string.shared_album_name_violation_re_enter))
                }
                SharedAlbumResult.TOO_MANY_USER_GALLERY -> {
                    val maxAlbumCount = SharedAlbumConfigHelper.getConfig().maxAlbumCount
                    val message =
                        dialog.context.resources.getQuantityString(R.plurals.shared_album_album_is_full, maxAlbumCount, maxAlbumCount)
                    ToastUtil.showShortToast(message)
                }
                else -> ToastUtil.showShortToast(R.string.shared_album_error_retry)
            }
        }
    }

    /**
     * 检查是否有联网权限
     *
     * @param context 弹窗依附于context
     * @param onAllowed 允许使用联网权限的结果回调
     */
    fun checkPrivacyPermission(context: Context, onAllowed: (() -> Unit)? = null) {
        if (NetworkPermissionManager.isUseOpenNetwork.not()) {
            ConfirmDialog.Builder(context)
                .setPositiveButton(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_option_allow) { dialog, which ->
                    NetworkPermissionManager.isUseOpenNetwork = true
                    onAllowed?.invoke()
                    dialog.dismiss()
                }
                .setNegativeButton(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_option_refuse) { dialog, which ->
                    dialog.dismiss()
                }
                .setMessage(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_share_album)
                .setTitle(com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_request_network_title)
                .setCancelable(false)
                .build()
                .show()
        } else {
            onAllowed?.invoke()
        }
    }
}