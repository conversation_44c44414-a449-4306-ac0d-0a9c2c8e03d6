/********************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - JUnit5 Test Class.java.java.kt
 * Description:
 * Version: 1.0
 * Date : 2022/8/9
 * Author: xiewujie@Apps.Gallery3D
 * TAG: NoNetworkAlertInterceptor
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>    <desc>
 * ------------------------------------------------------------------------------
 * xiewu<PERSON><PERSON>@Apps.Gallery3D      2022/08/09    1.0         first created
</desc></version></date></author> */
package com.oplus.gallery.pictureeditorpage.app.supertext.docconvert

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

internal class NoNetworkAlertInterceptorTest {

    private val context = mockk<Context>()
    private val interceptor =
        NoNetworkAlertInterceptor<DocType>(
            context
        )

    @Before
    fun setup() {
        mockkStatic(NetworkMonitor::class)
        ContextGetter.context = mockk<Application> {
            every { getSystemService(Context.CONNECTIVITY_SERVICE) } returns mockk<ConnectivityManager>(relaxed = true)
        }
        NetworkMonitor
    }

    @Test
    @Ignore(value = "待负责人处理")
    fun should_return_true_when_network_is_unAvailable() {
        // given
        every { NetworkMonitor.isNetworkValidated() } returns false
        // when
        val result = interceptor.shouldIntercept(null)
        // then
        Assert.assertTrue(result)
    }

    @Test
    @Ignore(value = "待负责人处理")
    fun should_return_false_when_network_is_available() {
        // given
        every { NetworkMonitor.isNetworkValidated() } returns true
        // when
        val result = interceptor.shouldIntercept(null)
        // then
        Assert.assertFalse(result)
    }
}
