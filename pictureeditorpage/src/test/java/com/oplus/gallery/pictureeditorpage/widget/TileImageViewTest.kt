/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - TileImageViewTest.kt
 * Description:
 * Version:
 * Date: 2022/5/1
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2022/5/1     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.pictureeditorpage.widget

import com.oplus.gallery.business_lib.model.data.base.item.LocalImage
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkClass
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

@Ignore
class TileImageViewTest {

    @MockK
    lateinit var tileImageView: com.oplus.gallery.pictureeditorpage.gl.widget.TileImageView

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun should_return_true_when_isSupportRegionDecoder_with_support_decode() {
        // given
        val model = mockkClass(com.oplus.gallery.pictureeditorpage.adapter.PhotoDataAdapter::class)
        val item = mockkClass(LocalImage::class)
        val decoder = mockkClass(com.oplus.gallery.pictureeditorpage.predecode.PreTileDecoder::class)
        every { tileImageView.isSupportRegionDecoder(any(), any()) } answers { callOriginal() }
        every { model.getCurrentIndex() } answers { 0 }
        every { model.getPreTileDecoder(any()) } answers { decoder }
        every { decoder.isSupportMultiRegionDecode } answers { true }
        every { item.supportedOperations } answers { Constants.IMediaSupportOperations.OPERATION_SUPPORT_MULTI_REGION_DECODER }

        // when
        val ans: Boolean = tileImageView.isSupportRegionDecoder(model, item)

        // then
        Assert.assertEquals(ans, true)
    }

    @Test
    fun should_return_true_when_isSupportRegionDecoder_with_null_decoder() {
        // given
        val model = mockkClass(com.oplus.gallery.pictureeditorpage.adapter.PhotoDataAdapter::class)
        val item = mockkClass(LocalImage::class)
        every { tileImageView.isSupportRegionDecoder(any(), any()) } answers { callOriginal() }
        every { model.getCurrentIndex() } answers { 0 }
        every { model.getPreTileDecoder(any()) } answers { null }
        every { item.supportedOperations } answers { Constants.IMediaSupportOperations.OPERATION_SUPPORT_MULTI_REGION_DECODER }

        // when
        val ans: Boolean = tileImageView.isSupportRegionDecoder(model, item)

        // then
        Assert.assertEquals(ans, true)
    }

    @Test
    fun should_return_true_when_isSupportRegionDecoder_with_null_model() {
        // given
        val item = mockkClass(LocalImage::class)
        every { tileImageView.isSupportRegionDecoder(any(), any()) } answers { callOriginal() }
        every { item.supportedOperations } answers { Constants.IMediaSupportOperations.OPERATION_SUPPORT_MULTI_REGION_DECODER }

        // when
        val ans: Boolean = tileImageView.isSupportRegionDecoder(null, item)

        // then
        Assert.assertEquals(ans, true)
    }

    @Test
    fun should_return_false_when_isSupportRegionDecoder_with_not_support_file() {
        // given
        val model = mockkClass(com.oplus.gallery.pictureeditorpage.adapter.PhotoDataAdapter::class)
        val item = mockkClass(LocalImage::class)
        val decoder = mockkClass(com.oplus.gallery.pictureeditorpage.predecode.PreTileDecoder::class)
        every { tileImageView.isSupportRegionDecoder(any(), any()) } answers { callOriginal() }
        every { model.getCurrentIndex() } answers { 0 }
        every { model.getPreTileDecoder(any()) } answers { decoder }
        every { decoder.isSupportMultiRegionDecode } answers { true }
        every { item.supportedOperations } answers { 0 }

        // when
        val ans: Boolean = tileImageView.isSupportRegionDecoder(model, item)

        // then
        Assert.assertEquals(ans, false)
    }

    @Test
    fun should_return_false_when_isSupportRegionDecoder_with_not_support_decoder() {
        // given
        val model = mockkClass(com.oplus.gallery.pictureeditorpage.adapter.PhotoDataAdapter::class)
        val item = mockkClass(LocalImage::class)
        val decoder = mockkClass(com.oplus.gallery.pictureeditorpage.predecode.PreTileDecoder::class)
        every { tileImageView.isSupportRegionDecoder(any(), any()) } answers { callOriginal() }
        every { model.getCurrentIndex() } answers { 0 }
        every { model.getPreTileDecoder(any()) } answers { decoder }
        every { decoder.isSupportMultiRegionDecode } answers { false }
        every { item.supportedOperations } answers { Constants.IMediaSupportOperations.OPERATION_SUPPORT_MULTI_REGION_DECODER }

        // when
        val ans: Boolean = tileImageView.isSupportRegionDecoder(model, item)

        // then
        Assert.assertEquals(ans, false)
    }

    @Test
    fun should_return_false_when_isSupportRegionDecoder_with_null_item() {
        // given
        val model = mockkClass(com.oplus.gallery.pictureeditorpage.adapter.PhotoDataAdapter::class)
        val decoder = mockkClass(com.oplus.gallery.pictureeditorpage.predecode.PreTileDecoder::class)
        every { tileImageView.isSupportRegionDecoder(any(), any()) } answers { callOriginal() }
        every { model.getCurrentIndex() } answers { 0 }
        every { model.getPreTileDecoder(any()) } answers { decoder }
        every { decoder.isSupportMultiRegionDecode } answers { true }

        // when
        val ans: Boolean = tileImageView.isSupportRegionDecoder(model, null)

        // then
        Assert.assertEquals(ans, false)
    }
}