/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MosaicEntryTest.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/9/21
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2022/9/21      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.pictureeditorpage.app.mosaic.processor

import org.junit.Assert.assertEquals
import org.junit.Test

class MosaicEntryTest {

    @Test
    fun `should return right result when testUpdateBrushItemSizeForAutoBurnished with specify pxScale and rect`() {
        // Given
        val entry = MosaicEntry(MosaicEntry.Pattern.BURNISHED, 0, 0)

        // when
        val actual = entry.calculateBrushItemSizeForAutoBurnishedRect(100, 2800, 1000)

        // then
        assertEquals(0.1f, actual)
    }
}