/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - PreScreenNailCacheTest.kt
 * Description:
 * Version: 1.0
 * Date: 2022/3/14
 * Author: Duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * Duchengsong@Apps.Gallery3D 2022/3/14     1.0              OPLUS_FEATURE_APP_LOG_MONITOR
 **************************************************************************************************/
package com.oplus.gallery.pictureeditorpage.predecode

import android.graphics.Bitmap
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.standard_lib.scheduler.Worker
import io.mockk.MockKAnnotations
import io.mockk.mockk
import io.mockk.mockkClass
import io.mockk.spyk
import io.mockk.every
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.util.concurrent.Future

class PreScreenNailCacheTest {

    private lateinit var preScreenNailCache: com.oplus.gallery.pictureeditorpage.predecode.PreScreenNailCache

    private val path = Path.fromString("/test")

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        preScreenNailCache = spyk(recordPrivateCalls = true)
    }

    @Test
    fun should_get_same_future_when_pushPreScreenNailFuture_with_normal_future() {
        // given
        val future = mockkClass(Worker::class) as Future<Bitmap>

        // when
        preScreenNailCache.pushPreScreenNailFuture(path, future)
        val ansFuture = preScreenNailCache.cacheScreenNailFuture
        val ansPath = preScreenNailCache.cacheScreenNailPath

        // then
        Assert.assertEquals(ansFuture, future)
        Assert.assertEquals(ansPath, path)
    }

    @Test
    fun should_get_null_when_resetPreDecoder_with_normal_cache() {
        // given
        val future = mockkClass(Worker::class) as Future<Bitmap>
        preScreenNailCache.pushPreScreenNailFuture(path, future)

        // when
        preScreenNailCache.resetPreDecoder()
        val ansFuture = preScreenNailCache.cacheScreenNailFuture
        val ansPath = preScreenNailCache.cacheScreenNailPath

        // then
        Assert.assertEquals(ansFuture, null)
        Assert.assertEquals(ansPath, null)
    }

    @Test
    fun should_get_bitmap_when_popPreScreenNail_with_normal_cache_future() {
        // given
        val bitmap = mockk<Bitmap>()
        val future = mockkClass(Worker::class) as Future<Bitmap>
        preScreenNailCache.pushPreScreenNailFuture(path, future)
        every { future.get() } returns bitmap
        every { future.isCancelled } returns false
        every { bitmap.isRecycled } returns false

        // when
        val ans = preScreenNailCache.popPreScreenNail(path)

        // then
        Assert.assertEquals(ans, bitmap)
    }

    @Test
    fun should_get_null_when_popPreScreenNail_with_null_future() {
        // given
        val bitmap = mockk<Bitmap>()
        preScreenNailCache.pushPreScreenNailFuture(path, null)
        every { bitmap.isRecycled } returns false

        // when
        val ans = preScreenNailCache.popPreScreenNail(path)

        // then
        Assert.assertEquals(ans, null)
    }

    @Test
    fun should_get_null_when_popPreScreenNail_with_different_path() {
        // given
        val bitmap = mockk<Bitmap>()
        val path2 = Path.fromString("/test2")
        val future = mockkClass(Worker::class) as Future<Bitmap>
        preScreenNailCache.pushPreScreenNailFuture(path2, future)
        every { future.get() } returns bitmap
        every { future.isCancelled } returns false
        every { bitmap.isRecycled } returns false

        // when
        val ans = preScreenNailCache.popPreScreenNail(path)

        // then
        Assert.assertEquals(ans, null)
    }

    @Test
    fun should_get_null_when_popPreScreenNail_with_cancelled_future() {
        // given
        val bitmap = mockk<Bitmap>()
        val future = mockkClass(Worker::class) as Future<Bitmap>
        preScreenNailCache.pushPreScreenNailFuture(path, future)
        every { future.get() } returns bitmap
        every { future.isCancelled } returns true
        every { bitmap.isRecycled } returns false

        // when
        val ans = preScreenNailCache.popPreScreenNail(path)

        // then
        Assert.assertEquals(ans, null)
    }

    @Test
    fun should_get_null_when_popPreScreenNail_with_recycled_bitmap() {
        // given
        val bitmap = mockk<Bitmap>()
        val future = mockkClass(Worker::class) as Future<Bitmap>
        preScreenNailCache.pushPreScreenNailFuture(path, future)
        every { future.get() } returns bitmap
        every { future.isCancelled } returns false
        every { bitmap.isRecycled } returns true

        // when
        val ans = preScreenNailCache.popPreScreenNail(path)

        // then
        Assert.assertEquals(ans, null)
    }

    @Test
    fun should_get_null_when_popPreScreenNail_with_future_no_return_value() {
        // given
        val future = mockkClass(Worker::class) as Future<Bitmap>
        preScreenNailCache.pushPreScreenNailFuture(path, future)
        every { future.get() } returns null
        every { future.isCancelled } returns false

        // when
        val ans = preScreenNailCache.popPreScreenNail(path)

        // then
        Assert.assertEquals(ans, null)
    }
}