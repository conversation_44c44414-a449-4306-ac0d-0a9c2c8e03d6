/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BeautyFacePreProcessor.java
 ** Description:
 ** Version: 1.0
 ** Date : 2018/01/18
 ** Author: kexiuhua@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                 <data>     <version>  <desc>
 **  kexiuhua@Apps.Gallery3D  2018/01/18        build this module
 ****************************************************************/
package com.oplus.gallery.pictureeditorpage.base.processor.beauty;


import static com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.BEAUTY_COMPONENT_VERSION;
import static com.oplus.gallery.framework.abilities.download.Constants.INVALID_VERSION;
import static com.oplus.gallery.pictureeditorpage.base.processor.beauty.IFaceDetectEngine.RESULT_SUCCEED;

import android.graphics.Bitmap;

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper;
import com.oplus.gallery.photoeditor.editingvvm.component.beauty.BeautyModelConfig;
import com.oplus.gallery.pictureeditorpage.frame.processor.Processor;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.pictureeditorpage.base.EditorPhotoDataAdapter;
import com.oplus.gallery.pictureeditorpage.frame.processor.AsyncLoader;
import com.oplus.gallery.pictureeditorpage.frame.processor.TaskManager;

import java.io.File;

public class BeautyFacePreProcessor extends Processor {
    private static final String TAG = "BeautyFacePreProcessor";
    private IFaceDetectEngine mBFEngine;
    private AsyncLoader.Future mFaceDetectFuture;
    private OnFaceDetectListener mFaceDetectListener;

    public BeautyFacePreProcessor(TaskManager loader) {
        super(loader);
        if (isBeautyDownloaded()) {
            loadLibs();
            mBFEngine = new BeautyFacePreProcessEngine();
        } else {
            mBFEngine = new FaceDetectorEngine();
        }
    }

    @Override
    public void initialize() {
        mBFEngine.initEngine();
    }

    public BeautyFacePreProcessEngine getBFEngine() {
        if (mBFEngine instanceof BeautyFacePreProcessEngine) {
            return (BeautyFacePreProcessEngine)mBFEngine;
        } else {
            return null;
        }
    }

    @Override
    public void release() {
        mBFEngine.release();
        if (mFaceDetectFuture != null) {
            mFaceDetectFuture.cancel();
        }
    }

    public void setOnFaceDetectListener(OnFaceDetectListener listener) {
        mFaceDetectListener = listener;
    }

    public void startFaceDetect(EditorPhotoDataAdapter adapter) {
        if (mFaceDetectFuture != null) {
            mFaceDetectFuture.cancel();
        }
        FaceDetectTask job = new FaceDetectTask(mBFEngine, adapter);
        mFaceDetectFuture = mLoader.submitQueueTask(job, new AsyncLoader.JobCallback<Integer>() {
            @Override
            public void onDone(Integer state) {
                if (state != null) {
                    int value = state.intValue();
                    if ((value == RESULT_SUCCEED) || (value == BeautyFacePreProcessEngine.RESULT_FAILED)) {
                        boolean hasFace = mBFEngine.hasFaces();
                        if (mFaceDetectListener != null) {
                            mFaceDetectListener.onFaceDetected(hasFace);
                        }
                    }
                }
            }
        });
        if (mFaceDetectFuture == null) {
            GLog.w(TAG, "startFaceDetect, summit FaceDetectTask failed!");
        }
    }

    public void startFaceDetect(Bitmap bitmap) {
        if (mFaceDetectFuture != null) {
            mFaceDetectFuture.cancel();
        }
        FaceDetectTask job = new FaceDetectTask(mBFEngine, bitmap);
        mFaceDetectFuture = mLoader.submitQueueTask(job, new AsyncLoader.JobCallback<Integer>() {
            @Override
            public void onDone(Integer state) {
                if (state != null) {
                    int value = state.intValue();
                    if (value == RESULT_SUCCEED) {
                        boolean hasFace = mBFEngine.hasFaces();
                        if (mFaceDetectListener != null) {
                            mFaceDetectListener.onFaceDetected(hasFace);
                        }
                        return;
                    }
                    if (mFaceDetectListener != null) {
                        mFaceDetectListener.onFaceDetected(false);
                    }
                }
            }
        });
        if (mFaceDetectFuture == null) {
            GLog.w(TAG, "startFaceDetect, summit FaceDetectTask failed!");
        }
    }

    /**
     * 已下载美颜SO文件时，返回true。否则返回false。
     */
    private boolean isBeautyDownloaded() {
        return ConfigAbilityWrapper.getInt(BEAUTY_COMPONENT_VERSION, INVALID_VERSION) != INVALID_VERSION;
    }

    /**
     * 加载美颜SO库文件。
     */
    private void loadLibs() {
        BeautyModelConfig config = new BeautyModelConfig();
        String path = config.getDefaultComponentDirPath().getAbsolutePath();
        for (String beautySource : BeautyModelConfig.Companion.getBEAUTY_SOURCES()) {
            System.load(path + File.separator + beautySource);
        }
    }
}
