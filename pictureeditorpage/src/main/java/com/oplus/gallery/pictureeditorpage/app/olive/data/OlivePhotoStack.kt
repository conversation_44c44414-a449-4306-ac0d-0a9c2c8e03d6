/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OlivePhotoStack.kt
 ** Description: livephoto的缓冲栈
 ** Version: 1.0
 ** Date: 2024/1/31
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2024/1/31      1.0        created
 *********************************************************************************/

package com.oplus.gallery.pictureeditorpage.app.olive.data

import android.content.Context
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.olive_decoder.OLivePhoto
import com.oplus.gallery.pictureeditorpage.frame.ExtendCacheStack

/**
 * livephoto的缓冲栈
 */
class OlivePhotoStack(
    context: Context
) : ExtendCacheStack<OLiveCacheEntry, OLiveSaveEntry>(context, CACHE_NAME) {

    override val tag: String = TAG

    override fun save(inputData: OLiveCacheEntry?, oldSaveEntry: OLiveSaveEntry?): OLiveSaveEntry? {
        return inputData?.let { entry ->
            OLiveSaveEntry(
                entry.olivePhoto,
                entry.coverTimeMs
            )
        }
    }

    override fun load(inputData: OLiveCacheEntry?, saveEntry: OLiveSaveEntry): OLiveCacheEntry? {
        return inputData ?: OLiveCacheEntry().apply {
            olivePhoto = saveEntry.olivePhoto
            coverTimeMs = saveEntry.coverTimeMs
        }
    }

    override fun isAllVaild(): Boolean {
        return oliveHasChanged()
    }

    /**
     * 缓存栈中的实况图片信息是否有变化
     * 1.如果当前index - 1如果为null 表示栈中出现了一个初始化的缓存，直接返回true
     * 2.如果当前缓存和前一个缓存的时间戳不一样，表示缓存栈中加入了新的实况图片缓存，返回true
     * 3.如果当前缓存和前一个缓存的时间戳一样了，表示缓存栈中实况图片没有变化，返回false。
     */
    private fun oliveHasChanged(): Boolean {
        val lastIndex: Int = getCurrentIndex() - 1
        val lastEntry: OLiveCacheEntry? = peek(lastIndex)
        if (lastEntry == null) {
            GLog.d(TAG, "last is null, current is Initialized cache")
            return true
        }
        val currentEntry: OLiveCacheEntry? = peek()
        if ((currentEntry != null) && (currentEntry.coverTimeMs !== lastEntry.coverTimeMs)) {
            GLog.d(
                TAG,
                "currentEntry CoverTimeMs:%${currentEntry.coverTimeMs} different from lastEntry CoverTimeMs:${lastEntry.coverTimeMs}!"
            )
            return true
        }
        GLog.d(TAG, "olive no change occurred")
        return false
    }

    companion object {
        private const val TAG = "OlivePhotoStack"

        private const val CACHE_NAME = "livephoto_"
    }
}

/**
 * livephoto存储到栈中的实体对象，用于还原 OLiveCacheEntry
 *
 * @param olivePhoto 源文件解析的livephoto信息
 * @param coverTimeMs 封面时间戳
 */
data class OLiveSaveEntry(
    var olivePhoto: OLivePhoto?,
    val coverTimeMs: Long?
)