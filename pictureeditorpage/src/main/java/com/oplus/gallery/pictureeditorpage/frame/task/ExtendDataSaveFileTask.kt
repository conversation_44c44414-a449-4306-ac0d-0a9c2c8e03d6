/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ExtendDataSaveFileTask.kt
 ** Description: 包含拓展数据的文件保存任务
 ** Version: 1.0
 ** Date : 2023/8/16
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2023/08/16    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.pictureeditorpage.frame.task

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper
import com.oplus.gallery.pictureeditorpage.frame.data.SaveFileTaskInfo
import java.util.function.Supplier

/**
 * 包含拓展数据的文件保存任务
 * @param context Context
 * @param saveFileTaskInfo 保存文件需要的信息
 * @param bitmapSupplier 需要保存的Bitmap提供器
 * @param extendDataWriters 拓展数据的写入器列表
 * @param callback 监听器
 */
open class ExtendDataSaveFileTask(
    context: Context,
    saveFileTaskInfo: SaveFileTaskInfo,
    bitmapSupplier: Supplier<Bitmap?>,
    private val extendDataWriters: ExtendDataWriterList,
    callback: SaveJobCallback
) : SaveFileTask(context, saveFileTaskInfo, bitmapSupplier, callback) {

    override fun onPrepare(): Boolean {
        return super.onPrepare() && extendDataWriters.prepare()
    }

    override fun writeOtherData(fileUri: Uri) {
        super.writeOtherData(fileUri)
        extendDataWriters.writeExtendData(fileUri)
    }

    override fun insertTagFlags(tagFlags: Int): Int {
        return super.insertTagFlags(tagFlags) or extendDataWriters.getExtendDataTagFlags()
    }

    override fun isSpaceAvailable(bitmap: Bitmap): Boolean {
        val totalSize = bitmap.allocationByteCount + extendDataWriters.getNeedStorageSpace()
        return StorageLimitHelper.hasEnoughStorageSpace(OplusEnvironment.StorageType.PHONE_STORAGE, totalSize.toLong())
    }

    companion object {
        private const val TAG = "ExtendDataSaveFileTask"
    }
}