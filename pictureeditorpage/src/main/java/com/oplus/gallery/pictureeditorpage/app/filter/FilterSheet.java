/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - FilterSheet.java
 ** Description:
 ** Version: 1.0
 ** Date : 2017/11/30
 ** Author: kexiuhua@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                 <data>     <version>  <desc>
 **  kexiuhua@Apps.Gallery3D  2017/11/30        build this module
 ****************************************************************/
package com.oplus.gallery.pictureeditorpage.app.filter;

import com.oplus.gallery.pictureeditorpage.base.RenderSheet;
import com.oplus.gallery.pictureeditorpage.frame.processor.AsyncLoader;
import com.oplus.gallery.pictureeditorpage.app.filter.processor.FilterLoaderCallback;
import com.oplus.gallery.pictureeditorpage.app.filter.common.FilterManager;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseUIController;
import com.oplus.gallery.pictureeditorpage.app.filter.processor.FilterProcessor;
import com.oplus.gallery.pictureeditorpage.app.filter.processor.FilterEntry;
import com.oplus.gallery.photoeditor.gl.texure.Texture;
import com.oplus.gallery.photoeditor.common.Config;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState;

import java.util.List;

public class FilterSheet extends RenderSheet<FilterProcessor> {
    private static final String TAG = "FilterSheet";
    private FilterManager mFilterManager;
    private FilterEntry mCurrentEntry;
    private EditorFilterUIController mController;

    public FilterSheet() {
        super();
        mShowPreview = Config.Preview.FILTER_PREVIEW_ENABLE;
    }

    @Override
    protected String getSheetTag() {
        return TAG;
    }


    @Override
    protected void onForeCreate() {
        mProcessor = new FilterProcessor(mLoader);
    }

    private class OnFilterChooseListener implements EditorFilterUIController.OnFilterChosenListener {
        @Override
        public void onFilterChosen(FilterEntry entry) {
            if (entry == null) {
                return;
            }
            if (((entry.getStates() == null) || (entry.getCurrentProgress() == 0)) && (mTexture != null)) {
                mCurrentEntry = entry;
                mProcessor.cancelJob();
                //CANT release output texture, cause Engine MUST use this one
                mTexture.revert(false);
                updateOperationUI();
                invalidate();
                return;
            }
            if (mProcessor != null) {
                mCurrentEntry = entry;
                mProcessor.doEffect(mTexture, entry);
            }
        }
    }

    private class OFilterLoaderCallback implements FilterLoaderCallback {
        @Override
        public void onUpdateFilterList(final List<FilterEntry> list) {
            if ((list != null) && !list.isEmpty() && isCreated()) {
                mPictureContext.getActivity().runOnUiThread(new UpdateFilterRunnable(list));
            }
        }
        @Override
        public void onInvalidate() {
            if (isCreated()) {
                mPictureContext.getActivity().runOnUiThread(new InvalidateFilterRunnable());
            }
        }
    }

    private class InvalidateFilterRunnable implements Runnable {
        @Override
        public void run() {
            if (mController != null) {

                mController.invalidate(mFilterManager.getFilters());
            }
        }
    }

    private class UpdateFilterRunnable implements Runnable {
        private List<FilterEntry> mFilterList;

        UpdateFilterRunnable(List<FilterEntry> list) {
            mFilterList = list;
        }

        @Override
        public void run() {
            if (mController != null) {
                mController.updateFilters(mFilterList);
            }
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mFilterManager = new FilterManager(mLoader, mPictureContext.getResources());
        if (mEditorBase instanceof EditorFilterState) {
            EditorBaseUIController baseUIController = mEditorBase.getUIController();
            if (baseUIController instanceof EditorFilterUIController) {
                mController = (EditorFilterUIController) baseUIController;
                mController.setOnFilterChosenListener(new OnFilterChooseListener());
                mFilterManager.registerCallback(new OFilterLoaderCallback());
                mFilterManager.loadFilters(mAdapter);
            }
        }
    }

    @Override
    protected void resumeJob() {
        if ((mCurrentEntry != null) && (mTexture != null)) {
            mProcessor.doEffect(mTexture, mCurrentEntry);
        }
    }

    @Override
    protected boolean doSaveJob(Texture texture, AsyncLoader.JobCallback<Texture> callback) {
        if (mCurrentEntry != null) {
            return mProcessor.doEffectJob(texture, mCurrentEntry, callback);
        }
        return false;
    }

    @Override
    public int onSave(EditorBaseState.OnSavedListener listener) {
        return super.onSave(listener);
    }
}
