/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PanoramicSegmentationClient.kt
 ** Description:智能消除图像全景分割的客户端
 ** Version: 1.0
 ** Date : 2023/10/2
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2023/10/02    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.eliminate.engine.ai

import android.content.Context
import android.graphics.Bitmap
import androidx.annotation.WorkerThread
import com.oplus.aiunit.core.data.DetectName.VISION_PICTURE_SEGMENTATION
import com.oplus.aiunit.core.protocol.common.ErrorCode
import com.oplus.aiunit.eraser.client.SegmentationClient
import com.oplus.aiunit.eraser.data.SegmentationResult
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.ext.toByteArray
import com.oplus.gallery.photoeditor.editingvvm.eliminate.track.EliminateTrackHelper
import com.oplus.gallery.photoeditor.track.EditExceptionType
import com.oplus.gallery.pictureeditorpage.app.eliminate.engine.ai.EliminateCompleteClient.Companion.SENSITIVE_CONTENT_CODE
import kotlin.math.max
import kotlin.math.min

typealias EliminateDetectCallback = (PanoramicSegmentationInfo?) -> Unit

/**
 * 智能消除图像全景分割的客户端，封装对AIUnit SDK 全景分割接口的调用
 */
class PanoramicSegmentationClient(private val context: Context) {

    private val client = SegmentationClient(context)

    /**
     * 是否运行的本地算法
     */
    val isLocalRunType
        get() = AISettings.getDetectData(context, VISION_PICTURE_SEGMENTATION).runType == RUN_TYPE_LOCAL

    fun init() {
        // 初始化，预留给端侧算法的接口
    }

    /**
     * 执行图像检测（全景分割），返回分割后的图像掩码数据
     * @param image 检测的图像Bitmap
     */
    fun detect(image: Bitmap): PanoramicSegmentationInfo? {
        return runCatching {
            val start = System.currentTimeMillis()
            val processResult = client.process(image) ?: let {
                GLog.e(TAG, "[detect] error, processResult is null")
                return null
            }
            trackErrorEvent(processResult)
            if (isSizeSupport(image.width, image.height)) {
                val segmentResult = parseMask(processResult).also {
                    GLog.d(TAG, "[detect] cost time ${GLog.getTime(start)}ms")
                } ?: run {
                    GLog.e(TAG, "[detect] parseMask error, process result is $processResult")
                    null
                }
                PanoramicSegmentationInfo(
                    segmentResult,
                    processResult.isContentSensitive(),
                    processResult.isContentDoubtful(),
                    true,
                    processResult.getCode(),
                    processResult.getSpecificCode()
                )
            } else {
                GLog.d(TAG, "[detect] image size is not support,width:${image.width},height:${image.height}")
                // 如果图片宽高比例大于2.5，则不解析分割结果，直接填充null
                PanoramicSegmentationInfo(
                    null,
                    processResult.isContentSensitive(),
                    processResult.isContentDoubtful(),
                    false,
                    processResult.getCode(),
                    processResult.getSpecificCode()
                )
            }
        }.onFailure {
            GLog.e(TAG, "[detect] error", it)
        }.getOrNull()
    }

    private fun trackErrorEvent(result: SegmentationResult) {
        if (result.specificCode > 0) {
            EliminateTrackHelper.trackEliminateErrorEvent(
                EditExceptionType.CLOUD_EXCEPTION.ordinal,
                result.specificCode,
                result.msg
            )
            return
        }
        if (result.code > 0) {
            EliminateTrackHelper.trackEliminateErrorEvent(EditExceptionType.AIUNIT_EXCEPTION.ordinal, result.code, result.msg)
        }
    }

    /**
     * 停止当前的分割算法
     */
    @WorkerThread
    fun stop() {
        client.stop()
    }

    /**
     * 释放全景分割的资源
     */
    @WorkerThread
    fun release() {
        client.destroy()
    }

    private fun parseMask(result: SegmentationResult): SegmentResult? {
        val mask = result.bitmap ?: return null
        val tags = result.data?.tags?.en ?: return null
        val ids = result.data?.ids ?: return null
        val maskArray = mask.toByteArray()
        val areas = parseLayerArea(maskArray, tags.size)
        val layers = ids.mapIndexed { index, flag ->
            tags[index]?.let { SegmentationLayer(areas[flag], flag, it) }
        }.filterNotNull().toList()
        GLog.d(TAG, "[parseMask] layers:$layers")
        return SegmentResult(mask, maskArray, SegmentationMetadata(layers, result.data))
    }

    companion object {
        private const val TAG = "PanoramicSegmentationClient"
        private const val ELIMINATE_SEGMENTATION_IMAGE_RATIO = 2.5
        private const val RUN_TYPE_LOCAL = 0

        /**
         * 图像分割是否支持此图像比例
         * @param imageWidth 图像的宽
         * @param imageHeight 图像的高
         * @return 是否支持
         */
        private fun isSizeSupport(imageWidth: Int, imageHeight: Int): Boolean {
            val minLen = min(imageWidth, imageHeight)
            val maxLen = max(imageWidth, imageHeight)
            if ((minLen <= 0) || (maxLen <= 0)) {
                return false
            }
            return maxLen / minLen.toFloat() <= ELIMINATE_SEGMENTATION_IMAGE_RATIO
        }

        private fun SegmentationResult.isContentSensitive(): Boolean {
            return ((specificCode == SENSITIVE_CONTENT_CODE) && (bitmap == null)) || (specificCode == ErrorCode.kErrorOperationNoPerm.value())
        }

        /**
         *  判断图片是否存疑
         */
        private fun SegmentationResult.isContentDoubtful(): Boolean {
            /**
             * 跟服务端那边同步了，如果是拒识他们会返回603，不返回数据，如果是存疑，依旧返回603，但是有数据
             * 兼容旧版本处理不了602的问题；
             */
            return (specificCode == SENSITIVE_CONTENT_CODE) && (bitmap != null)
        }

        /**
         * 取code
         */
        private fun SegmentationResult.getCode(): Int {
            return code
        }

        /**
         * 取specificCode
         */
        private fun SegmentationResult.getSpecificCode(): Int {
            return specificCode
        }
    }
}
