/**************************************************************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - AbsPreTileDecoder.java
 ** Description: Only decode tile but not upload it
 ** Version: 1.0
 ** Date : 2016/03/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: --------------------------------------------------------
 **  <author>                      <data>         <version >     <desc>
 **  <EMAIL>    2016/03/10     1.0            build this module
 **
 *************************************************************************************************/
package com.oplus.gallery.pictureeditorpage.predecode;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Rect;
import android.util.DisplayMetrics;
import android.util.LongSparseArray;
import android.view.WindowManager;

import com.oplus.breakpad.BreakpadTombstone;
import com.oplus.gallery.addon.graphics.BitmapFactoryWrapper;
import com.oplus.gallery.basebiz.imagerequest.ImageRequester;
import com.oplus.gallery.business_lib.model.data.base.MediaObject;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.LocalImage;
import com.oplus.gallery.business_lib.model.data.person.set.FaceItem;
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools;
import com.oplus.gallery.foundation.util.ext.BitmapFactoryKt;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.math.MathUtil;
import com.oplus.gallery.business_lib.util.IBitmapEffectProcessor;
import com.oplus.gallery.pictureeditorpage.gl.widget.TileImageView;
import com.oplus.gallery.photoeditor.util.Picture3dUtil;
import com.oplus.gallery.standard_lib.codec.DecodeUtils;
import com.oplus.gallery.standard_lib.codec.IRegionDecoder;
import com.oplus.gallery.standard_lib.codec.ImageData;
import com.oplus.gallery.standard_lib.scheduler.Job;
import com.oplus.gallery.standard_lib.scheduler.JobContext;
import com.oplus.gallery.standard_lib.scheduler.JobContextStub;
import com.oplus.gallery.standard_lib.scheduler.session.WorkerSession;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.util.debug.DumpUtil;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GTrace;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static com.oplus.breakpad.BreakpadUtil.generateKey;
import static com.oplus.breakpad.NativeCrashGuardKt.runNativeGuarding;
import static com.oplus.gallery.business_lib.model.data.base.utils.Constants.IMediaSupportOperations.OPERATION_SUPPORT_MULTI_REGION_DECODER;
import static com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING;

public abstract class AbsPreTileDecoder {

    /**
     * render will use this Lock
     */
    public final Object mDecodeLock = new Object();
    public final int[] mLevel;
    public final int mLevelCount;
    public final int mImageHeight;
    public final int mImageWidth;
    public volatile boolean mDecodingComplete = false;
    public LongSparseArray<PreTile> mDecodedTileList;

    protected final int mTileSize;
    protected final int mViewWidth;
    protected final int mViewHeight;
    protected volatile boolean mNeedClear = false;
    protected Rect mTileRange = new Rect();
    protected Path mPath;
    protected Boolean mEnableDiskCache = true;
    protected long mDateModifiedInSec;
    protected BitmapPools.IBitmapPool mBitmapPool;
    protected IRegionDecoder mRegionDecoder;
    protected WorkerSession mSession;
    protected Future<LongSparseArray<PreTile>> mTileDecode;

    private static final String TAG = "AbsPreTileDecoder";
    private static final int TIMEOUT = 100;
    private final PreTilePool mPreTilePool;
    private final boolean mNeedLowQualityCache;
    private final LinkedBlockingQueue<TileBitmapCache> mCacheTileQueue = new LinkedBlockingQueue<>();
    private volatile boolean mDecodingStarted = false;
    private IBitmapEffectProcessor mBitmapEffectProcessor;
    private long mCopyDataTime = 0; // this use for test

    private Future<ImageData> mPutTileDataJob;

    public AbsPreTileDecoder(Path path, long dateModifiedInSec, IRegionDecoder decoder, WorkerSession session, int thumbWidth,
                             int viewWidth, int viewHeight, BitmapPools.IBitmapPool pool, boolean needLowQualityCache, PreTilePool preTilePool) {
        mPath = path;
        mDateModifiedInSec = dateModifiedInSec;
        mRegionDecoder = decoder;
        mSession = session;
        if ((viewWidth == 0) || (viewHeight == 0)) {
            DisplayMetrics metrics = new DisplayMetrics();
            WindowManager wm = (WindowManager) ContextGetter.context.getSystemService(Context.WINDOW_SERVICE);
            wm.getDefaultDisplay().getRealMetrics(metrics);
            viewWidth = metrics.widthPixels;
            viewHeight = metrics.heightPixels;
        }
        mViewWidth = viewWidth;
        mViewHeight = viewHeight;
        mImageWidth = decoder.getWidth();
        mImageHeight = decoder.getHeight();
        mTileSize = TileImageView.getTileSize();
        mTileRange.set(0, 0, mImageWidth, mImageHeight);
        mLevelCount = Math.max(0, MathUtil.ceilLog2(mImageWidth / (float) thumbWidth));
        mLevel = new int[2];
        float f1 = Math.min((float) mViewWidth / (float) mImageWidth, (float) mViewHeight / (float) mImageHeight);
        mLevel[0] = MathUtil.clamp(MathUtil.floorLog2(1f / f1), 0, mLevelCount);
        float f2 = Math.min((float) mViewWidth / (float) mImageHeight, (float) mViewHeight / (float) mImageWidth);
        mLevel[1] = MathUtil.clamp(MathUtil.floorLog2(1f / f2), 0, mLevelCount);
        mBitmapPool = pool;
        mNeedLowQualityCache = needLowQualityCache;
        mPreTilePool = preTilePool;
    }

    public void setDiskCacheEnable(Boolean enable) {
        mEnableDiskCache = enable;
    }

    public void setBitmapEffectProcessor(IBitmapEffectProcessor processor) {
        mBitmapEffectProcessor = processor;
    }

    public int getUseLevel() {
        return Math.min(mLevel[0], mLevel[1]);
    }

    protected void resetDecodeFlag() {
        synchronized (mDecodeLock) {
            if (mNeedClear) {
                mDecodingStarted = false;
                mDecodingComplete = false;
            }
        }
    }

    protected boolean isDecodingStart() {
        synchronized (mDecodeLock) {
            return mDecodingStarted;
        }
    }

    protected void clearDecodedTiles(LongSparseArray<PreTile> list) {
        synchronized (mDecodeLock) {
            resetDecodeFlag();
            if (list == null) {
                return;
            }
            int size = list.size();
            for (int i = 0; i < size; i++) {
                PreTile tile = (PreTile) list.valueAt(i);
                if (tile == null) {
                    continue;
                }
                tile.mTileState = PreTile.STATE_RECYCLED;
                // 子类PreTileDecoder会调用该方法用以回收tile.子类PreTileDecoderOnBg完全重写该方法。
                if (mPreTilePool != null) {
                    mPreTilePool.recycleTile(tile);
                }
            }
            list.clear();
        }
    }

    public void startDecode() {
        synchronized (mDecodeLock) {
            if (mPutTileDataJob != null) {
                mPutTileDataJob.cancel(false);
                mPutTileDataJob = null;
            }
            if (mTileDecode != null) {
                mTileDecode.cancel(false);
                mTileDecode = null;
            }
            mTileDecode = mSession.submit(new TileDecode(mPreTilePool, mNeedLowQualityCache));
            mDecodingStarted = true;
            mNeedClear = false;
        }
    }

    public void stopDecode(boolean clear) {
        synchronized (mDecodeLock) {
            mNeedClear = clear;
            if (mTileDecode != null) {
                mTileDecode.cancel(false);
                mTileDecode = null;
            }
            if (mNeedClear) {
                clearDecodedTiles(mDecodedTileList);
                mDecodedTileList = null;
            } else {
                if (!mDecodingComplete) {
                    mDecodingStarted = false;
                }
            }
            if (mNeedClear && (mPutTileDataJob != null)) {
                mPutTileDataJob.cancel(false);
                mPutTileDataJob = null;
            }
        }
    }

    /**
     * zhangpeng 10bit
     */
    protected ImageData getTileBitmapFromDisk(PreTile tile) {
        /*if (PreTileUtils.GET_TILE_FROM_DISK) {
//            TileParam tileParam = new TileParam(tile.mX, tile.mY, tile.mTileLevel,
//                    tile.mImageWidth, tile.mImageHeight);
            return ImageRequester.requestTileBitmap(mPath, tileParam, mDateModifiedInSec, mBitmapPool,
                    JobContextStub.INSTANCE, isYuv());
        }*/
        return null;
    }

    private ImageData getTileFromCached(PreTile tile) {
        ImageData imageData = getTileBitmapFromDisk(tile);
        if ((imageData != null) && (imageData.getBitmap() != null)) {
            if (PreTileUtils.DUMP_CACHE_BITMAP) {
                DumpUtil.dumpBitmap(imageData.getBitmap(), mPath.getSuffix() + "-" + tile.mX + "-" + tile.mY);
            }
        }
        return imageData;
    }

    private ImageData getTile(PreTile tile, int size, IRegionDecoder decoder) {
        Bitmap bitmap = null;
        int x = tile.mX;
        int y = tile.mY;
        int level = tile.mTileLevel;
        int tW = size << level;
        int tH = size << level;
        Rect wantRegion = new Rect(x, y, x + tW, y + tH);

        IRegionDecoder regionDecoder = null;
        synchronized (AbsPreTileDecoder.this) {
            if (decoder != null) {
                regionDecoder = decoder;
            } else {
                regionDecoder = mRegionDecoder;
            }
            if (regionDecoder == null) {
                return null;
            }
        }
        GTrace.traceBegin("PreTile " + mPath);
        bitmap = (mBitmapPool == null) ? null : mBitmapPool.getBitmap(size, size, Config.ARGB_8888);
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(size, size, Config.ARGB_8888);
        }
        bitmap.eraseColor(Color.TRANSPARENT);

        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPreferredConfig = Config.ARGB_8888;
        options.inSampleSize = (1 << level);
        options.inBitmap = bitmap;
        /*
        only MTK has options.inPostProc
        This is a post-affect switch to processing sharpness of image
        if turn this switch on, decoder will spread little more time to
        process the sharpness of image synchronously
        */
        BitmapFactoryWrapper.setInPostProc(options, true);
        ImageData data = null;
        try {
            GTrace.traceBegin("PreTile.decode");
            final IRegionDecoder finalRegionDecoder = regionDecoder;
            BreakpadTombstone tombstone = regionDecoder.getTombstone();
            data = runNativeGuarding(regionDecoder.getDecoderName(), generateKey(tombstone), () ->
                    finalRegionDecoder.decodeRegion(wantRegion, options, false))
                    .onNativeFailure(() ->
                            {
                                String filePath = EMPTY_STRING;
                                if (tombstone != null) {
                                    filePath = tombstone.getFilePath();
                                }
                                GLog.w(TAG, "getTile: crash file = " + PathMask.INSTANCE.mask(filePath)
                                        + " , rect = " + wantRegion + " , options = " + BitmapFactoryKt.asString(options));
                            }
                    )
                    .getOrNull();
            if (data != null) {
                bitmap = data.getBitmap();
            }
            processBitmap(bitmap, regionDecoder);
            clampBitmap(bitmap, options, data);
            GTrace.traceEnd();
        } catch (Exception e) {
            GLog.e(TAG, "getTile", e);
        } finally {
            if ((options.inBitmap != null) && (options.inBitmap != bitmap)) {
                if (mBitmapPool != null) {
                    mBitmapPool.recycle(options.inBitmap);
                }
                options.inBitmap = null;
            }
        }
        if (mEnableDiskCache) {
            putTileToCache(tile, bitmap, data);
        }
        GTrace.traceEnd();
        return data;
    }

    private void processBitmap(Bitmap bitmap, IRegionDecoder regionDecoder) {
        if (mBitmapEffectProcessor != null) {
            GTrace.traceBegin("PreTile.decode.effectProcess");
            try {
                mBitmapEffectProcessor.processBitmap(
                        bitmap, mBitmapPool, regionDecoder.getWidth(), regionDecoder.getHeight());
            } catch (Exception e) {
                GLog.e(TAG, "effectProcess error, errMsg: ", e);
            }
            GTrace.traceEnd();
        }
    }


    private void clampBitmap(Bitmap bitmap, BitmapFactory.Options options, ImageData data) {
        if ((data != null) && (data.getBitmap() != null)) { // Draw clamp border
            int bitmapWidth = data.getFrameWidth();
            int bitmapHeight = data.getFrameHeight();
            int optionWidth = options.outWidth;
            int optionHeight = options.outHeight;
            boolean widthChanged = (bitmapWidth != optionWidth);
            boolean heightChanged = (bitmapHeight != optionHeight);
            if (widthChanged || heightChanged) {
                bitmap = BitmapUtils.clampBitmap(bitmap, 0, 0, optionWidth, optionHeight);
                data.setBitmap(bitmap);
            }
        }
    }

    private void putTileToCache(PreTile tile, Bitmap bitmap, ImageData data) {
        if (!PreTileUtils.GET_TILE_FROM_DISK || (data == null)) {
            return;
        }
        if (!PreTileUtils.SUPPORT_AYSNC_DISK) {
            /*
             * byte[] array = null;
             * CacheType cacheType = CacheType.TILE_BLOB_CACHE;
             * if (isYuv()) {
             *      array = TileCacheRequest.getPutTileArray(data);
             *      cacheType = CacheType.TILE_YUV_BLOB_CACHE;
             * } else {
             *      array = BitmapUtils.compressToBytes(bitmap);
             * }
             * TileCacheKey cacheKey = new TileCacheKey(mPath, cacheType, tile.mX,
             *      tile.mY, tile.mTileLevel, tile.mImageWidth, tile.mImageHeight, mDateModifiedInSec);
             * DiskCacheManagerService.putImageData(cacheKey, array);
            */
        } else {
            supportSyncDisk(tile, bitmap, data);
        }
    }

    private void supportSyncDisk(PreTile tile, Bitmap bitmap, ImageData data) {
        if (tile.mTileState == PreTile.STATE_RECYCLED) {
            return;
        }
        long time = System.currentTimeMillis();
        /*
        it seems hard to synchronized when bitmap reused and write disk cache
        so, here bitmap will be copied
        */
        if (bitmap != null) {
            data = new ImageData(bitmap);
            if (bitmap.getConfig() != Bitmap.Config.HARDWARE) {
                bitmap = bitmap.copy(bitmap.getConfig(), true);
            } else {
                bitmap = bitmap.copy(bitmap.getConfig(), false);
            }
            data.setBitmap(bitmap);
        }
        tile = tile.copy();
        TileBitmapCache tileBitmapCache = new TileBitmapCache(tile, data);
        mCopyDataTime += (System.currentTimeMillis() - time);
        synchronized (mDecodeLock) {
            if (!mNeedClear) {
                mCacheTileQueue.offer(tileBitmapCache);
                if (mPutTileDataJob == null) {
                    mPutTileDataJob = mSession.submit(new PutTileData(), null);
                }
            }
        }
    }

    private static class TileBitmapCache {
        int mX;
        int mY;
        int mLevel;
        ImageData mImageData;
        PreTile mPreTile;

        TileBitmapCache(PreTile preTile, ImageData imageData) {
            mX = preTile.mX;
            mY = preTile.mY;
            mLevel = preTile.mTileLevel;
            mImageData = imageData;
            mPreTile = preTile;
        }
    }

    protected class PutTileData implements Job<ImageData> {

        @Override
        public ImageData call(JobContext jc) {
            if (jc.isCancelled()) {
                return null;
            }
            return putTileData(jc);
        }

        private ImageData putTileData(JobContext jc) {
            long startTime = System.currentTimeMillis();
            long writeTime = 0;
            GLog.d(TAG, "putTileData");
            while (!mCacheTileQueue.isEmpty() || !mDecodingComplete) {
                if (jc.isCancelled()) {
                    break;
                }
                TileBitmapCache tileBitmapCache = null;
                try {
                    /*
                    wait but for times up to judge DecodingComplete,
                    cause it done all job, but DecodingComplete has not changed
                    */
                    tileBitmapCache = mCacheTileQueue.poll(TIMEOUT, TimeUnit.MILLISECONDS);
                    if (tileBitmapCache != null) {
                        long time = System.currentTimeMillis();
                        ImageData imageData = tileBitmapCache.mImageData;
                        byte[] array = null;
                        Bitmap bitmap = imageData.getBitmap();
                        if (bitmap == null) {
                            return null;
                        }
                        if (mNeedLowQualityCache) {
                            array = BitmapUtils.compressToBytes(bitmap);
                        } else {
                            array = BitmapUtils.compressToBytes(
                                    bitmap,
                                    Bitmap.CompressFormat.PNG);
                        }
                        /*
                         * cacheKey = new TileCacheKey(mPath, CacheType.TILE_BLOB_CACHE, tileBitmapCache.mX,
                         *      tileBitmapCache.mY, tileBitmapCache.mLevel, mImageWidth, mImageHeight, mDateModifiedInSec);
                         */
                        if (array != null) {
                            // DiskCacheManagerService.putImageData(cacheKey, array);
                            writeTime += System.currentTimeMillis() - time;
                        }

                    }
                } catch (Exception e) {
                    GLog.e(TAG, "putTileData", e);
                }
            }
            GLog.d(TAG, "putTileData time:" + (System.currentTimeMillis() - startTime)
                    + ", writeTime:" + writeTime);
            mCacheTileQueue.clear();
            return null;
        }
    }

    protected abstract void onUpdatedTiles(LongSparseArray<PreTile> list);

    protected abstract void onTileLoaded(PreTile tile);

    protected class TileDecode implements Job<LongSparseArray<PreTile>> {
        private final Object mDecodeMonitor = new Object();
        private boolean mOpaque = true;
        private PreTilePool mPreTilePool = null;

        public TileDecode(PreTilePool preTilePool, boolean opaque) {
            mPreTilePool = preTilePool;
            mOpaque = opaque;
        }

        @Override
        public LongSparseArray<PreTile> call(JobContext jc) {
            if (jc.isCancelled()) {
                return null;
            }
            return decodeTiles(jc);
        }

        @SuppressWarnings({"rawtypes", "unchecked"})
        private LongSparseArray<PreTile> decodeTiles(JobContext jc) {
            long startTime = System.currentTimeMillis();
            LongSparseArray tileList = new LongSparseArray();
            Rect rect = mTileRange;
            int level = getUseLevel();
            int size = mTileSize << level;
            int texWidth = mTileSize;
            int texHeight = mTileSize;
            TileQueue decodeQueue = new TileQueue();
            //decode Tile
            for (int y = rect.top, bottom = rect.bottom; y < bottom; y += size) {
                int tileHeight = Math.min(mTileSize, ((mImageHeight - y) >> level));
                for (int x = rect.left, right = rect.right; x < right; x += size) {
                    if (jc.isCancelled() || mNeedClear) {
                        clearDecodedTiles(tileList);
                        return null;
                    }
                    int tileWidth = Math.min(mTileSize, ((mImageWidth - x) >> level));
                    // 有PreTilePool则复用，没有则创建新的PreTile
                    PreTile tile = (mPreTilePool == null) ? null : mPreTilePool.getTile();
                    if (tile == null) {
                        tile = new PreTile(x, y, texWidth, texHeight, tileWidth, tileHeight,
                                level, mImageWidth, mImageHeight, mBitmapPool);
                    } else {
                        tile.update(x, y, texWidth, texHeight, tileWidth, tileHeight,
                                level, mImageWidth, mImageHeight);
                    }
                    tile.setOpaque(mOpaque);
                    ImageData data = null;
                    if (mEnableDiskCache) {
                        data = getTileFromCached(tile);
                    }
                    if (data != null) {
                        Bitmap bitmap = data.getBitmap();
                        bitmap = DecodeUtils.ensureGLCompatibleBitmap(bitmap);
                        if (bitmap == null) {
                            data.setBitmap(bitmap);
                            tile.mDecodedData = data;
                            tile.mDecodedTile = bitmap;
                            tile.mTileState = PreTile.STATE_DECODED;
                            onTileLoaded(tile);
                        } else {
                            bitmap = DecodeUtils.ensureGLCompatibleBitmap(bitmap);
                            if (bitmap != null) {
                                data.setBitmap(bitmap);
                                tile.mDecodedData = data;
                                tile.mDecodedTile = bitmap;
                                tile.mTileState = PreTile.STATE_DECODED;
                                onTileLoaded(tile);
                            } else {
                                tile.mTileState = PreTile.STATE_RECYCLED;
                                GLog.w(TAG, "PreDecode failed!");
                            }
                        }
                        tileList.put(makeTileKey(tile.mX, tile.mY, tile.mTileLevel), tile);
                    } else {
                        decodeQueue.push(tile);
                    }
                }
            }
            //decode from Original picture, maybe do it on MultiThreads
            if (!decodeQueue.isEmpty()) {
                boolean finished = false;
                ArrayList<DecodeThread> tList = initDecodeThreads(decodeQueue, tileList);
                boolean cancelled = false;
                while (!finished) {
                    if (jc.isCancelled() || mNeedClear) {
                        cancelled = true;
                        interruptThreads(tList);
                        synchronized (mDecodeMonitor) {
                            while (!isFinished(tList)) {
                                try {
                                    mDecodeMonitor.wait();
                                } catch (Exception e) {
                                    GLog.w(TAG, e);
                                }
                            }
                        }
                        break;
                    }
                    PreTile tile = null;
                    synchronized (AbsPreTileDecoder.this) {
                        tile = decodeQueue.pop();
                    }
                    if (tile != null) {
                        decodeFromOriginal(tile, tileList, mRegionDecoder);
                    } else {
                        synchronized (mDecodeMonitor) {
                            while (!isFinished(tList)) {
                                try {
                                    mDecodeMonitor.wait();
                                } catch (Exception e) {
                                    GLog.w(TAG, e);
                                }
                            }
                        }
                        finished = true;
                    }
                }
                if (cancelled) {
                    clearDecodedTiles(tileList);
                    return null;
                }
                interruptThreads(tList);
            }
            GLog.d(TAG, "decoded time:" + (System.currentTimeMillis() - startTime)
                    + ", mSize:" + tileList.size() + ", mCopyDataTime:" + mCopyDataTime
                    + ", this:" + AbsPreTileDecoder.this);
            //update list
            if (jc.isCancelled() || mNeedClear) {
                GLog.d(TAG, "Decode interrupted!");
                clearDecodedTiles(tileList);
                return null;
            }
            onUpdatedTiles(tileList);
            return tileList;
        }

        @SuppressWarnings({"unchecked"})
        /**
         * decode tile from original picture
         *
         * @param tile
         * @param list
         * @return void
         */
        private void decodeFromOriginal(PreTile tile, LongSparseArray<PreTile> tileList, IRegionDecoder decoder) {
            // 大图滑动浏览可能有功耗问题, 暂时屏蔽
            /*CPUPerformanceManager.setAction(CPUPerformanceManager.ACTION_ANIMATION, CPUPerformanceManager.TIMEOUT_100MS);*/
            Bitmap bitmap = null;
            ImageData data = getTile(tile, mTileSize, decoder);
            if (data != null) {
                bitmap = data.getBitmap();
                if (bitmap == null) {
                    data.setBitmap(bitmap);
                    tile.mDecodedData = data;
                    tile.mDecodedTile = bitmap;
                    tile.mTileState = PreTile.STATE_DECODED;
                    onTileLoaded(tile);
                } else {
                    bitmap = DecodeUtils.ensureGLCompatibleBitmap(bitmap);
                    if (bitmap != null) {
                        data.setBitmap(bitmap);
                        tile.mDecodedData = data;
                        tile.mDecodedTile = bitmap;
                        tile.mTileState = PreTile.STATE_DECODED;
                        onTileLoaded(tile);
                    } else {
                        tile.mTileState = PreTile.STATE_RECYCLED;
                        GLog.w(TAG, "PreDecode failed!");
                    }
                }
            } else {
                tile.mTileState = PreTile.STATE_RECYCLED;
                GLog.w(TAG, "PreDecode failed!");
            }
            synchronized (AbsPreTileDecoder.this) {
                tileList.put(makeTileKey(tile.mX, tile.mY, tile.mTileLevel), tile);
            }
        }

        private ArrayList<DecodeThread> initDecodeThreads(TileQueue decodeQueue, LongSparseArray<PreTile> tileList) {
            ArrayList<DecodeThread> tList = new ArrayList<DecodeThread>();
            int num = 1;
            for (int i = 0; i < num; i++) {
                DecodeThread decodeThread = new DecodeThread(decodeQueue, tileList);
                decodeThread.setName("PreTileDecoder-" + i);
                decodeThread.start();
                tList.add(decodeThread);
            }
            return tList;
        }

        private void interruptThreads(ArrayList<DecodeThread> tList) {
            for (DecodeThread t : tList) {
                t.interrupt();
            }
        }

        private boolean isFinished(ArrayList<DecodeThread> tList) {
            boolean finished = true;
            for (DecodeThread t : tList) {
                finished &= t.isFinished();
            }
            return finished;
        }

        private class DecodeThread extends Thread {
            private final TileQueue mDecodeQueue;
            private final LongSparseArray<PreTile> mTileList;
            private boolean mFinished = false;
            private boolean mInterrupt = false;

            DecodeThread(TileQueue queue, LongSparseArray<PreTile> list) {
                mDecodeQueue = queue;
                mTileList = list;
            }

            @Override
            public void run() {
                try {
                    IRegionDecoder decoder = null;
                    if (Picture3dUtil.isSupportMultiThreadRegionDecode()) {
                        decoder = mRegionDecoder;
                    } else {
                        if (mPath != null) {
                            MediaObject obj = mPath.getObject();
                            if (obj != null) {
                                if (obj instanceof FaceItem) {
                                    obj = ((FaceItem) obj).getRefItem();
                                }
                                if (obj instanceof LocalImage) {
                                    LocalImage item = (LocalImage) obj;
                                    boolean supportMultiDecoder = (item.getSupportedOperations() & OPERATION_SUPPORT_MULTI_REGION_DECODER) != 0;
                                    supportMultiDecoder = supportMultiDecoder && mRegionDecoder.isSupportMultiRegionDecode();
                                    if (supportMultiDecoder) {
                                        Job<IRegionDecoder> job = ImageRequester.requestLargeImage(mSession, item);
                                        if (job != null) {
                                            decoder = job.call(JobContextStub.INSTANCE);
                                        }
                                    } else {
                                        decoder = mRegionDecoder;
                                    }
                                }
                            }
                        }
                    }
                    if (decoder != null) {
                        while (!mFinished) {
                            PreTile tile = null;
                            synchronized (AbsPreTileDecoder.this) {
                                tile = mDecodeQueue.pop();
                            }
                            if ((tile != null) && !mInterrupt) {
                                decodeFromOriginal(tile, mTileList, decoder);
                            } else {
                                finish();
                            }
                        }
                    }
                    if ((decoder != mRegionDecoder) && (decoder != null)) {
                        decoder.destroyDecoder();
                    }
                } catch (Exception e) {
                    GLog.w(TAG, e);
                } finally {
                    finish();
                }
            }

            @Override
            public void interrupt() {
                super.interrupt();
                mInterrupt = true;
            }

            public boolean isFinished() {
                synchronized (mDecodeMonitor) {
                    return mFinished;
                }
            }

            private void finish() {
                synchronized (mDecodeMonitor) {
                    mFinished = true;
                    mDecodeMonitor.notify();
                }
            }
        }
    }

    protected static class TileQueue {
        private final HashMap<PreTile, PreTile> mMap = new HashMap<PreTile, PreTile>();
        private PreTile mHead;

        public void clean() {
            mMap.clear();
            mHead = null;
        }

        public boolean isEmpty() {
            return mHead == null;
        }

        public PreTile pop() {
            PreTile tile = mHead;
            if (tile != null) {
                mHead = tile.mNext;
                tile.mNext = null;
                mMap.remove(tile);
            }
            return tile;
        }

        public boolean push(PreTile tile) {
            if ((tile == null) || mMap.containsKey(tile)) {
                return false;
            }
            mMap.put(tile, tile);
            boolean wasEmpty = mHead == null;
            tile.mNext = mHead;
            mHead = tile;
            return wasEmpty;
        }
    }

    protected static long makeTileKey(int x, int y, int level) {
        long result = x;
        result = (result << 16) | y;
        result = (result << 16) | level;
        return result;
    }

    public boolean isSupportMultiRegionDecode() {
        return mRegionDecoder.isSupportMultiRegionDecode();
    }
}
