/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - GLMenuItem.java
 * * Description: menu item in GLActionBar or GLSplitMenu.
 * * Version: 1.0
 * * Date : 2017/06/08
 * * Author: xinyang.Hu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  xinyang.Hu@Apps.Gallery3D       2017/06/08    1.0     build this module
 ****************************************************************/

package com.oplus.gallery.pictureeditorpage.gl.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MenuItem;
import android.view.animation.Interpolator;

import com.oplus.gallery.foundation.opengl.renderer.GLCanvas;
import com.oplus.gallery.photoeditor.R;
import com.oplus.gallery.foundation.ui.animation.AnimationTime;
import com.oplus.gallery.foundation.ui.animation.LinearlyAnimation;
import com.oplus.gallery.standard_lib.ui.util.AnimUtil;
import com.oplus.gallery.foundation.util.typeface.TypefaceUtil;

public class GLMenuItem extends GLTextViewV2 {

    public static final int ANIMATION_DURATION = (int) (150 * AnimUtil.getAnimatorDurationScale());
    private static final Interpolator ANIMATOR_INTERPOLATOR = new Interpolator() {
        @Override
        public float getInterpolation(float t) {
            t -= 1f;
            return t * t * t * t * t + 1f;
        }
    };
    private boolean mIsSplitMenu = false;
    private LinearlyAnimation mAnimation;
    private Drawable mDrawable;
    private int mIconResourcesId;

    public GLMenuItem(Context context, boolean isSplitMenu, MenuItem item) {
        super(context, isSplitMenu, item);
        mIsSplitMenu = isSplitMenu;
    }

    @Override
    protected void initParameter(Context context, GLTextViewV2 glTextView, boolean isSplitMenu, MenuItem item) {
        glTextView.setSingleLine();
        glTextView.setEllipsize(TextUtils.TruncateAt.END);
        if (isSplitMenu) {
            GLWidgetSpec.GLSplitMenuSpec spec = new GLWidgetSpec().getGLSplitMenuSpec(context);
            boolean hasIcon = (item != null) && (item.getIcon() != null);
            if (hasIcon) {
                int itemTextDrawablePadding = spec.getMenuItemTopDrawablePadding();
                int itemTopPadding = spec.getMenuItemTopPadding();
                glTextView.setCompoundDrawablePadding(itemTextDrawablePadding);
                glTextView.setPadding(spec.getMenuItemLeftPadding(), itemTopPadding, spec.getMenuItemLeftPadding(), 0);
                glTextView.setTextSize(context.getResources().getDimensionPixelOffset(R.dimen.picture3d_gl_split_menu_item_default_text_size));
            } else {
                glTextView.setGravity(Gravity.CENTER);
                glTextView.setTextSize(context.getResources().getDimensionPixelOffset(R.dimen.picture3d_gl_split_menu_item_no_icon_text_size));
            }
            ColorStateList colorStateList = context.getColorStateList(com.oplus.gallery.basebiz.R.color.picture3d_split_tab_text_color_selector);
            glTextView.setTextColor(colorStateList, false);
            glTextView.setFakeBoldText(false);
            glTextView.setTypeface(TypefaceUtil.INSTANCE.getSansSerifRegular());
        } else {
            GLWidgetSpec.GLActionBarSpec spec = new GLWidgetSpec().getGLActionBarSpec(context);
            final int padding = spec.getMenuItemTextPadding();
            glTextView.setPadding(padding, padding, padding, padding);
            glTextView.setMaxWidth(spec.getMenuItemMaxWidth());
            glTextView.setMinWidth(spec.getMenuItemMinWidth());
            glTextView.setTextSize(context.getResources().getDimensionPixelOffset(R.dimen.picture3d_gl_action_bar_menu_item_default_text_size));
            ColorStateList colorStateList = context.getColorStateList(R.color.picture_actionbar_menu_text_color_selector);
            glTextView.setTextColor(colorStateList, false);
            glTextView.setFakeBoldText(false);
            glTextView.setTypeface(TypefaceUtil.INSTANCE.getSansSerifMedium());
        }
    }

    @Override
    public void setText(CharSequence text) {
        super.setText(text);
    }

    public void setIcon(Drawable drawable) {
        mDrawable = drawable;
        if (mIsSplitMenu) {
            setDrawable(null, drawable, null, null);
        } else {
            setDrawable(drawable, null, null, null);
        }
    }

    public Drawable getIcon() {
        return mDrawable;
    }

    public void updateIcon(int iconId, Drawable drawable) {
        if (iconId == mIconResourcesId) {
            return;
        }
        if (drawable != null) {
            mIconResourcesId = iconId;
            updateIcon(drawable);
        }
    }

    public void updateIcon(int iconId) {
        if (iconId == mIconResourcesId) {
            return;
        }

        Drawable drawable = mContext.getDrawable(iconId);
        updateIcon(iconId, drawable);
    }

    @Override
    public void render(GLCanvas canvas) {
        LinearlyAnimation animation = mAnimation;
        if (animation != null) {
            boolean more = false;
            int top = bounds().top;
            if (animation.isActive()) {
                long currentTime = AnimationTime.get();
                more |= animation.calculate(currentTime);
                top = animation.get();
            }
            bounds().set(bounds().left, top, bounds().right, top + bounds().height());
            if (more) {
                invalidate();
            }
        }
        super.render(canvas);
    }

    public void updateIcon(Drawable drawable) {
        if ((drawable != null) && !drawable.equals(mDrawable)) {
            setIcon(drawable);
            if (isVisible()) {
                updateItemTexture();
            }
        }
    }

    public GLMenuItem buildSwitchAnimation(boolean isUp, int startDelay) {
        int from = isUp ? bounds().bottom : bounds().top;
        int to = isUp ? bounds().top : bounds().bottom;
        LinearlyAnimation animation = new LinearlyAnimation(from, to, ANIMATION_DURATION);
        bounds().set(bounds().left, animation.get(), bounds().right, animation.get() + bounds().height());
        animation.setInterpolator(ANIMATOR_INTERPOLATOR);
        if (isUp) {
            animation.setDelay(startDelay);
        }
        mAnimation = animation;
        return this;
    }

    public void cancelSwitchAnimation() {
        LinearlyAnimation animation = mAnimation;
        if (animation != null) {
            animation.forceStop();
            bounds().set(bounds().left, animation.get(), bounds().right, animation.get() + bounds().height());
        }
        mAnimation = null;
    }

    public boolean isSwitchAnimationActive() {
        LinearlyAnimation animation = mAnimation;
        return (animation != null) && animation.isActive();
    }

    public void startSwitchAnimation() {
        LinearlyAnimation animation = mAnimation;
        if (animation != null) {
            animation.start();
        }
    }

    public int getItemId() {
        return getId();
    }

    public void setItemId(int itemId) {
        setId(itemId);
    }

    public boolean isVisible() {
        return getVisibility() == VISIBLE;
    }

    public void setVisible(boolean visible) {
        boolean visibleChanged = (visible ? VISIBLE : INVISIBLE) != getVisibility();
        if (visible) {
            setVisibility(VISIBLE);
        } else {
            setVisibility(INVISIBLE);
        }
        if (visibleChanged) {
            forceLayout();
        }
    }

    //implement by subClass
    public void updateWhenScreenChange(boolean isLandMode, boolean isMultiWindowMode) {

    }
}
