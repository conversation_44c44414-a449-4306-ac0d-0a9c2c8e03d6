/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SaveFileTaskFactory.kt
 ** Description:创建SaveFileTask的工厂类
 ** Version: 1.0
 ** Date : 2023/8/16
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2023/08/16    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.pictureeditorpage.frame.task

import android.content.Context
import android.graphics.Bitmap
import com.oplus.gallery.business_lib.model.config.SaveConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.watermark.file.isUnsavablePattern
import com.oplus.gallery.pictureeditorpage.app.olive.OLiveSaveFileTask
import com.oplus.gallery.pictureeditorpage.app.olive.data.OLiveCacheEntry
import com.oplus.gallery.photoeditor.editingvvm.olive.OLiveThumbnailItem
import com.oplus.gallery.pictureeditorpage.app.parameter.ParameterExtendDataWriter
import com.oplus.gallery.pictureeditorpage.app.watermark.WatermarkExtendDataWriter
import com.oplus.gallery.pictureeditorpage.app.watermark.WatermarkImagePack
import com.oplus.gallery.pictureeditorpage.base.EditorPhotoDataAdapter
import com.oplus.gallery.pictureeditorpage.base.EditorPhotoDataAdapter.TextureDrum
import com.oplus.gallery.pictureeditorpage.frame.data.EditCacheType
import com.oplus.gallery.pictureeditorpage.frame.data.SaveFileTaskInfo
import com.oplus.gallery.pictureeditorpage.frame.data.SourceBitmapProfile
import com.oplus.gallery.pictureeditorpage.hdrimage.HdrImageEntry
import com.oplus.gallery.pictureeditorpage.hdrimage.HdrImageExtendDataWriter

/**
 * 创建SaveFileTask的工厂类
 */
object SaveFileTaskFactory {

    private const val TAG = "SaveFileTaskFactory"

    /**
     * 创建SaveFileTask
     * @param context Context
     * @param adapter EditorPhotoDataAdapter
     * @param callback 监听器
     * @return SaveFileTask
     */
    @JvmStatic
    fun create(context: Context, adapter: EditorPhotoDataAdapter, callback: SaveFileTask.SaveJobCallback): SaveFileTask {
        val colorSpace = adapter.textureDrum.imagePack.colorSpace
        val sourceUri = adapter.dataSourceUri
        val saveFileInfo = SaveFileTaskInfo(
            SourceBitmapProfile(sourceUri, colorSpace),
            adapter.recycleOriginPicture,
            adapter.deleteOriginPictureAfterEdited,
            SaveConfig.COMPRESS_QUALITY,
            null,
            adapter.modelType,
        )
        val bitmap = adapter.peek()?.bitmap
        //markby 朱昊 adapter中的isDisable方法已经没有了，需要林辉重新实现一下,这里先占位避免报错
        val isJustSaveOLive = adapter.isStackAllValid(EditCacheType.OLIVE_PHOTO)
        return if (isJustSaveOLive) {
            createOLiveSaveFileTask(context, adapter, saveFileInfo, bitmap, callback)
                ?: SaveFileTask(context, saveFileInfo, { bitmap }, callback)
        } else {
            // 如果有拓展数据则创建ExtendDataSaveFileTask，否则SaveFileTask
            createExtendDataWriterList(context, adapter, bitmap)?.let {
                ExtendDataSaveFileTask(context, saveFileInfo, { bitmap }, it, callback)
            } ?: SaveFileTask(context, saveFileInfo, { bitmap }, callback)
        }
    }

    private fun createOLiveSaveFileTask(
        context: Context,
        adapter: EditorPhotoDataAdapter,
        saveFileTaskInfo: SaveFileTaskInfo,
        bitmap: Bitmap?,
        callback: SaveFileTask.SaveJobCallback
    ): SaveFileTask? {
        if (bitmap == null) {
            GLog.w(TAG) { "<createOLiveSaveFileTask> bitmap is empty! return null task, need use default saveFileTask!" }
            return null
        }
        val textureDrum: TextureDrum = adapter.textureDrum
        val editorCacheEntry = textureDrum.getPostExtendResource(EditCacheType.OLIVE_PHOTO) as? OLiveCacheEntry
        editorCacheEntry?.let {
            val coverTime = it.coverTimeMs ?: -1L
            val coverItem = OLiveThumbnailItem(bitmap, coverTime)
            return OLiveSaveFileTask(context, saveFileTaskInfo, textureDrum.imagePack, coverItem, adapter.dataSourcePath, callback)
        }
        GLog.w(TAG) { "<createOLiveSaveFileTask> olive entry is empty! return null task, need use default saveFileTask!" }
        return null
    }

    private fun createExtendDataWriterList(context: Context, adapter: EditorPhotoDataAdapter, bitmap: Bitmap?): ExtendDataWriterList? {
        val extendDataWriters = ArrayList<IExtendDataWriter>()
        // 处理水印的拓展数据
        processForWatermark(context, adapter)?.let {
            extendDataWriters.add(it)
        }
        // 处理localHdr的拓展数据
        processForHdrImage(context, adapter, bitmap)?.let {
            extendDataWriters.add(it)
        }
        // 处理扩展数据中参数化数据的部分
        processForParameterData(context)?.also {
            extendDataWriters.add(it)
        }
        return if (extendDataWriters.isEmpty()) {
            null
        } else {
            ExtendDataWriterList(extendDataWriters)
        }
    }

    private fun processForWatermark(context: Context, adapter: EditorPhotoDataAdapter): IExtendDataWriter? {
        val imagePack = adapter.textureDrum.imagePack as? WatermarkImagePack
        return imagePack?.info?.let {
            if (imagePack.info?.params?.pattern.isUnsavablePattern()) return null
            WatermarkExtendDataWriter(context, it, imagePack.captureBitmap)
        }
    }

    private fun processForHdrImage(context: Context, adapter: EditorPhotoDataAdapter, bitmap: Bitmap?): IExtendDataWriter? {
        val hdrImageEntry = adapter.textureDrum.getPostExtendResource(EditCacheType.HDR_IMAGE) as? HdrImageEntry
        return hdrImageEntry?.fullDrawable?.let {
            HdrImageExtendDataWriter(context, bitmap, it)
        } ?: let {
            null
        }
    }

    private fun processForParameterData(context: Context): IExtendDataWriter? {
        return ParameterExtendDataWriter(context)
    }
}