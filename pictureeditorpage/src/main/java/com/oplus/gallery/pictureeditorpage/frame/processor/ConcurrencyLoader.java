/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ConcurrencyLoader.java
 * * Description:
 * * Version: 1.0
 * * Date : 2017/11/21
 * * Author: kexiuhua@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  kexiuhua@Apps.Gallery3D  2017/11/21        build this module
 ****************************************************************/

package com.oplus.gallery.pictureeditorpage.frame.processor;

public class ConcurrencyLoader {
    private static final int CORE_POOL_SIZE = 1;
    private static final int MAX_POOL_SIZE = 4;
    private final AsyncLoader mLoader;

    public ConcurrencyLoader() {
        mLoader = new AsyncLoader(CORE_POOL_SIZE, MAX_POOL_SIZE, "concurrency");
    }

    public void shutDown() {
        mLoader.shutdown();
    }

    public <T> AsyncLoader.Future<T> submit(AsyncLoader.Job<T> job, AsyncLoader.JobCallback<T> callback) {
        return mLoader.summit(job, callback);
    }

    public <T> AsyncLoader.Future<T> submit(AsyncLoader.Job<T> job) {
        return mLoader.summit(job, null);
    }

    public void removeTasks(Class<?> jobClass) {
        mLoader.removeTasks(jobClass);
    }

    public void clearTasks() {
        mLoader.clearTasks();
    }
}
