/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorRotateClipUIController.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/08
 * * Author: xinyang.Hu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  xinyang.Hu@Apps.Gallery3D       2017/11/08    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/

package com.oplus.gallery.pictureeditorpage.app.beauty;

import static com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolderKt.setViewSelectedState;
import static com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Key;
import static com.oplus.gallery.pictureeditorpage.base.processor.beauty.BeautyEntry.BeautyType.ONE_KEY_BEAUTY;
import static com.oplus.gallery.pictureeditorpage.base.processor.beauty.BeautyEntry.BeautyType.TEETH_WHITE;
import static com.oplus.gallery.foundation.util.math.MathUtils.ZERO_POINT_FIVE_F;

import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.oplus.gallery.addon.os.VibratorUtils;
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorColorAnimViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuAdapter;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorViewAlphaAnimViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.Selectable;
import com.oplus.gallery.business_lib.template.editor.anim.EditorColorAnimation;
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation;
import com.oplus.gallery.business_lib.template.editor.anim.EditorViewAlphaAnimation;
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData;
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView;
import com.oplus.gallery.basebiz.widget.EditorMenuItemView;
import com.oplus.gallery.foundation.tracing.constant.CommonTrackConstant;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.photoeditor.editingvvm.adjustment.EffectConfig;
import com.oplus.gallery.photoeditor.R;
import com.oplus.gallery.pictureeditorpage.base.OnEditorStateIconClickListener;
import com.oplus.gallery.pictureeditorpage.base.processor.beauty.BeautyEntry;
import com.oplus.gallery.pictureeditorpage.common.PictureEditTrackHelper;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseUIController;
import com.oplus.gallery.photoeditor.widget.RuleScrollerView;
import com.oplus.gallery.pictureeditorpage.PictureContext;
import com.oplus.gallery.standard_lib.ui.SuitableSizeTextView;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.collections.CollectionsKt;


public class EditorBeautyUIController extends EditorBaseUIController
        implements RuleScrollerView.OnSelectValueChangeListener,
        EditorLinearListView.OnAdsorptionChangeListener, EditorLinearListView.OnCenterViewChangedListener {
    private static final String TAG = "EditorBeautyUIController";
    private static final int ITEM_TYPE_VERTICAL = 0;
    private static final int ITEM_TYPE_HORIZONTAL = 1;
    private static final int ITEM_TYPE_VERTICAL_AUTO_BEAUTY = 2;
    private static final int ITEM_TYPE_HORIZONTAL_AUTO_BEAUTY = 3;
    private static final int BEAUTY_MAX_PROGRESS = 100;
    private static final int BEAUTY_MIDDLE_PROGRESS = 50;
    private static final int BEAUTY_SKIN_BRIGHT_MAX_PROGRESS = 10;
    private static final int BEAUTY_SKIN_SOFTEN_MAX_PROGRESS = 65;
    private static final int BEAUTY_DEBLEMISH_MAX_PROGRESS = 80;
    private static final int BEAUTY_SLENDER_FACE_MAX_PROGRESS = 45;
    private static final int BEAUTY_ENLARGEMENT_EYE_MAX_PROGRESS = 60;
    private static final int BEAUTY_EYE_BRIGHT_MAX_PROGRESS = 80;
    private static final int BEAUTY_DEPOUCH_MAX_PROGRESS = 88;
    private static final int ITEM_CLICK_FROM_DEFAULT = -1;
    /**
     * 滑动列表停止后,自动点击
     */
    private static final int ITEM_CLICK_FROM_LIST_SCROLL = 1;
    /**
     * 用户点击
     */
    private static final int ITEM_CLICK_FROM_USER = 2;
    private final Map<Integer, BeautyEntry> mConfigMaps = new HashMap();
    private final Map<BeautyEntry, Integer> mEntryLevelMap = new HashMap();
    // 原来的mEntryLevel为了避免底层重复调用美颜SDK在切换设置项的时候会clear()掉原来的配置信息，但是我们UI层又需要一个这样的数据来做UI的逻辑(比如显示对比按钮)，所以新增了这个map。
    private final Map<BeautyEntry, Integer> mUIEntryLevelMap = new HashMap<>();
    private EditorMenuAdapter mBeautyMenuAdapter;
    private OnEditorStateIconClickListener mOnStateIconClickListener;
    private SuitableSizeTextView mNoFaceTipView;
    private BeautyEntry mCurrentEntry;
    private BeautyEntry mLastEntry = null;
    private List<EditorMenuItemViewData> mData = null;
    private int mLastProgress;

    private OnBeautyParamListener mOnBeautyParamListener;

    private int mCurIndex;
    private boolean mHasFace = false;
    private int mCurrentPosition = 0;
    private int mItemClickedFrom = ITEM_CLICK_FROM_DEFAULT;

    public EditorBeautyUIController(PictureContext context, ViewGroup rootView, EditorBaseState state) {
        super(context, rootView, state);
        mReconfigureViewIds = CollectionsKt.listOf(
                R.id.toolbar_layout,
                R.id.menu_item_list,
                R.id.scroller_view
        );
    }

    public interface OnBeautyParamListener {
        void onProgressChanged(BeautyEntry entry, Map<BeautyEntry, Integer> oneKeyMap);
    }

    @Override
    public boolean canSelectValueChange() {
        return true;
    }

    @Override
    public void onStartChangeValue() {
        if (mListView == null) {
            return;
        }
        mListView.setTouchable(false);
    }

    @Override
    public void onSelectValueChanged(int value, boolean isFromUser) {
        if (!isFromUser || (mLastProgress == value)) {
            return;
        }
        updateBeautyStateWithRuleScroller(value);
        mLastProgress = value;
        int curIndex = mRuleScrollerView.getScrollerIndex();

        if (curIndex != mCurIndex) {
            mCurIndex = curIndex;
            VibratorUtils.vibrateInSubThread(mContext, VibratorUtils.EffectType.VIBRATE_TYPE_WEAKEST, true);
        }
    }


    @Override
    public void onItemUnselected(View view, int position, Object item) {
        super.onItemUnselected(view, position, item);
        if ((mRuleScrollerView == null) || (view == null)) {
            return;
        }
        ((EditorMenuItemViewData) item).setCenterText(TextUtil.EMPTY_STRING);
        mBeautyMenuAdapter.updateItemView(position);
    }

    @Override
    public void onChangeValueComplete(int finalValue) {
        if (mListView == null) {
            return;
        }
        mListView.setTouchable(true);
    }


    @Override
    public int getTitle() {
        return R.string.picture3d_editor_text_beauty;
    }

    @Override
    protected int getToolBarContainerWidthDimenId() {
        return R.dimen.picture3d_editor_toolbar_width_landscape;
    }

    @Override
    protected int getToolBarContainerHeightWidthDimenId() {
        return R.dimen.picture3d_beauty_container_bar_height;
    }

    @Override
    public int getToolbarLayoutId(@NonNull AppUiResponder.AppUiConfig config) {
        return EditorUIConfig.isEditorLandscape(config)
                ? R.layout.picture3d_editor_adjust_toolbar_landscape
                : R.layout.picture3d_editor_adjust_toolbar;
    }

    @Override
    public void onViewCreated() {
        mListView = mToolBarContainer.findViewById(R.id.menu_item_list);
        mListView.setHorizontalFlingFriction(EditorLinearListView.ADJUST_FRICTION_COEFFICIENT);
        mData = EditorUIConfig.initEditorMenuAdapterData(mContext,
                R.array.picture3d_editor_array_beauty_state_id_array,
                R.array.picture3d_editor_array_beauty_state_icon_array,
                R.array.picture3d_editor_array_beauty_state_text_array);

        initializeConfig();
        initAdapter();

        mListView.setDisplayInCenter(true);
        mListView.setAdapter(mBeautyMenuAdapter);
        mListView.setOnAdsorptionChangeListener(this);
        mListView.setOnCenterViewChangedListener(this);

        mRuleScrollerView = mToolBarContainer.findViewById(R.id.scroller_view);
        mRuleScrollerView.setOnSelectValueChangeListener(this);

        mLastEntry = mCurrentEntry;

        if (mCurrentEntry != null) {
            mCurrentEntry.setBeautyState(BeautyEntry.BeautyState.DISABLE);
            mData.get(mCurrentEntry.getOrder()).setDisableStyle(true);
            mBeautyMenuAdapter.select(mCurrentPosition);
            updateCurrentIndex(mCurrentPosition);
        }
    }

    private void initAdapter() {
        mBeautyMenuAdapter = new EditorMenuAdapter(mContext, mData) {

            private float mDisableGoneAlpha = mResources.getFloat(com.oplus.gallery.basebiz.R.dimen.base_editor_menu_state_disable_gone_alpha);
            private float mDisableDefaultAlpha = mResources.getFloat(com.oplus.gallery.basebiz.R.dimen.base_editor_menu_state_disable_alpha);
            private float mHighlightAlpha = mResources.getFloat(com.oplus.gallery.basebiz.R.dimen.base_editor_menu_state_default_alpha);
            @Override
            public int getItemLayoutId(int viewType) {
                return ((viewType == ITEM_TYPE_VERTICAL) || (viewType == ITEM_TYPE_VERTICAL_AUTO_BEAUTY))
                    ? com.oplus.gallery.basebiz.R.layout.base_editor_menu_adjust_item_layout_vertical
                    : com.oplus.gallery.basebiz.R.layout.base_editor_menu_adjust_item_layout;
            }

            @Override
            public int getItemViewType(int position) {
                boolean isVertical = getOrientation() == LinearLayout.VERTICAL;
                if (position == 0) {
                    return isVertical ? ITEM_TYPE_VERTICAL_AUTO_BEAUTY : ITEM_TYPE_HORIZONTAL_AUTO_BEAUTY;
                }
                return isVertical ? ITEM_TYPE_VERTICAL : ITEM_TYPE_HORIZONTAL;
            }

            @Override
            public void bindData(BaseRecycleViewHolder viewHolder, int position, EditorMenuItemViewData item) {
                super.bindData(viewHolder, position, item);
                EditorMenuItemView menuItemView = getMenuItemView(viewHolder);
                menuItemView.setNormalDrawFlag(EditorMenuItemView.DRAW_BACKGROUND_COLOR
                        | EditorMenuItemView.DRAW_CENTER_ICON);
                menuItemView.setSelectedDrawFlag(EditorMenuItemView.DRAW_BACKGROUND_COLOR
                        | EditorMenuItemView.DRAW_CENTER_ICON
                        | EditorMenuItemView.DRAW_CENTER_TEXT);

                if (item.getViewId() == R.id.picture3d_editor_id_beauty_one_key_beauty) {
                    getDotView(viewHolder).setVisibility(View.INVISIBLE);
                    setViewSelectedState(viewHolder, item.isDisableStyle() ? Selectable.UNSELECTED : Selectable.SELECTED);
                    if (item.isDisableStyle()) {
                        menuItemView.setTextColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_text_color));
                        menuItemView.setIconResource(R.drawable.picture3d_editor_ic_beauty_one_key_beauty_selector);
                    } else {
                        menuItemView.setIconResource(R.drawable.picture3d_editor_ic_beauty_one_key_beauty_state_on);
                        menuItemView.setTextColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_black));
                    }
                } else {
                    menuItemView.setItemBackgroundColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask));
                    menuItemView.setTextColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_text_color));
                    if (!item.isDisableStyle()) {
                        setViewSelectedState(viewHolder, Selectable.SELECTED);
                    } else if ((mCurrentPosition == position) && (mItemClickedFrom == ITEM_CLICK_FROM_USER)) {
                        setViewSelectedState(viewHolder, Selectable.UNSELECTED);
                    } else {
                        setViewSelectedState(viewHolder, Selectable.DISABLE);
                    }
                }
                if (mCurrentPosition == position) {
                    mItemClickedFrom = ITEM_CLICK_FROM_DEFAULT;
                }
            }

            @Override
            public BaseRecycleViewHolder createViewHolder(View itemView, int viewType) {
                BaseRecycleViewHolder holder = null;
                if ((viewType == ITEM_TYPE_VERTICAL_AUTO_BEAUTY) || (viewType == ITEM_TYPE_HORIZONTAL_AUTO_BEAUTY)) {
                    EditorColorAnimViewHolder animHolder = new EditorColorAnimViewHolder(
                        itemView,
                        new EditorColorAnimation(),
                        new EditorPressAnimation());
                    holder = animHolder;
                    setSupportPressAnim(animHolder);

                    animHolder.setSelectedAnimEnable(true);
                    animHolder.setSelectedAnimView(animHolder);
                    animHolder.setDisableColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask));
                    animHolder.setUnselectedColor(mContext.getColor(com.oplus.gallery.basebiz.R.color.base_editor_menu_item_mask));
                    animHolder.setSelectedColor(mContext.getColor(R.color.picture3d_editor_menu_bounder_color));
                } else {
                    EditorViewAlphaAnimViewHolder animHolder = new EditorViewAlphaAnimViewHolder(
                        itemView,
                        new EditorViewAlphaAnimation(),
                        new EditorPressAnimation());
                    holder = animHolder;
                    setSupportPressAnim(animHolder);

                    animHolder.setSelectedAnimEnable(true);
                    animHolder.setSelectedAnimView(animHolder);
                    animHolder.setDisableAlpha(mDisableGoneAlpha);
                    animHolder.setUnselectedAlpha(mDisableDefaultAlpha);
                    animHolder.setSelectedAlpha(mHighlightAlpha);
                }
                return holder;
            }
        };

        mBeautyMenuAdapter.setSelectedVibratorEnable(true);
        mBeautyMenuAdapter.setItemClickListener(this);
        mBeautyMenuAdapter.setCanUnselectCurrentPosition(false);
    }

    /**
     * 1. Update current beauty entry's progress
     * 2. If current entry is one key beauty, update other buttons' UI with dot.
     * If current entry is not one key beauty send message to adapter and update current button's UI with dot.
     * 3. Calculate value and do beauty effect base on the current progress.
     *
     * @param progress current rule scroller view progress
     */
    private void updateBeautyStateWithRuleScroller(int progress) {
        if (mCurrentEntry != null) {
            GLog.d(TAG, "updateAdjustStateWithRuleScroller mCurrentEntry:"
                    + mCurrentEntry.toString() + " progress:" + progress);
            mCurrentEntry.setCurrentProgress(progress);
            updateBeautyDotUI();

            boolean isValueModified = mCurrentEntry.isModified();
            EditorMenuItemViewData itemDate = mData.get(mCurrentEntry.getOrder());
            if (itemDate != null) {
                itemDate.setTopTipsShow(isValueModified);
                itemDate.setCenterText(String.valueOf(mCurrentEntry.getCurrentProgress()));
                setOneKeyBeautyStateByProgress(mCurrentEntry.getCurrentProgress());
                mBeautyMenuAdapter.updateItemView(mCurrentEntry.getOrder());
            }
            changedProgressAndUpdateUI(progress);

            //do effect.
            if (mOnBeautyParamListener != null) {
                mOnBeautyParamListener.onProgressChanged(mCurrentEntry, mEntryLevelMap);
            }
        }
    }

    private void setOneKeyBeautyStateByProgress(int progress) {
        if (mCurrentEntry.getOrder() != BeautyEntry.getOrderByBeautyType(ONE_KEY_BEAUTY)) {
            return;
        }

        if ((progress > 0) && (mCurrentEntry.getBeautyState() != BeautyEntry.BeautyState.ENABLE)) {
            updateBeautyState(mCurrentEntry, mCurrentPosition, mData.get(mCurrentEntry.getOrder()));
        } else if ((progress <= 0) && (mCurrentEntry.getBeautyState() != BeautyEntry.BeautyState.DISABLE)) {
            updateBeautyState(mCurrentEntry, mCurrentPosition, mData.get(mCurrentEntry.getOrder()));
        }
    }

    /**
     * This method will update non-one-key-beauty-buttons' UI with dots on top of them.
     */
    private void updateBeautyDotUI() {
        if ((mCurrentEntry == null) || !mCurrentEntry.getType().equals(ONE_KEY_BEAUTY) || mConfigMaps.isEmpty()) {
            GLog.e(TAG, "updateAdjustmentDotUI mCurrentEntry " + mCurrentEntry);
            return;
        }
        for (BeautyEntry entry : mConfigMaps.values()) {
            boolean isValueModified = entry.isModified();
            EditorMenuItemViewData itemDate = mData.get(entry.getOrder());
            if ((itemDate != null) && (itemDate.isTopTipsShow() != isValueModified)) {
                itemDate.setTopTipsShow(isValueModified);
                mBeautyMenuAdapter.updateItemView(entry.getOrder());
            }
        }
    }

    private void initializeConfig() {
        int[] ids = ResourceUtils.getResourceIdArrays(mContext, R.array.picture3d_editor_array_beauty_state_id_array);
        if ((ids == null) || (ids.length <= 0)) {
            GLog.w(TAG, "initializeConfig, ids is empty!");
            return;
        }


        BeautyEntry entry = null;
        for (int id : ids) {
            BeautyEntry.BeautyType type = null;
            if (id == R.id.picture3d_editor_id_beauty_one_key_beauty) {
                type = ONE_KEY_BEAUTY;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_whitening) {
                type = BeautyEntry.BeautyType.SKIN_BRIGHT;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_SKIN_BRIGHT_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_dermabrasion) {
                type = BeautyEntry.BeautyType.SKIN_SOFTEN;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_SKIN_SOFTEN_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_eliminate_acne) {
                type = BeautyEntry.BeautyType.DEBLEMISH;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_DEBLEMISH_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_face_lift) {
                type = BeautyEntry.BeautyType.SLENDER_FACE;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_SLENDER_FACE_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_enlarge_eyes) {
                type = BeautyEntry.BeautyType.ENLARGEMENT_EYE;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_ENLARGEMENT_EYE_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_brighten_eyes) {
                type = BeautyEntry.BeautyType.EYE_BRIGHT;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_EYE_BRIGHT_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_eliminate_under_eye_bags) {
                type = BeautyEntry.BeautyType.DEPOUCH;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_DEPOUCH_MAX_PROGRESS);
            } else if (id == R.id.picture3d_editor_id_beauty_whiten_teeth) {
                type = TEETH_WHITE;
                entry = new BeautyEntry(type, 0, BEAUTY_MAX_PROGRESS, BEAUTY_MIDDLE_PROGRESS, 0, BEAUTY_MAX_PROGRESS);
            }

            if (type != null) {
                mConfigMaps.put(id, entry);
            }
        }
        mCurrentEntry = mConfigMaps.get(R.id.picture3d_editor_id_beauty_one_key_beauty);
    }

    @Override
    public void destroyView() {
        super.destroyView();
        removeExtraViewFromRootView(mNoFaceTipView, false);
    }

    @Override
    public void onItemClick(View view, int position, Object item) {
        if (view == null) {
            return;
        }

        updateBeautyState(mConfigMaps.get(view.getId()), position, (EditorMenuItemViewData) item);
    }

    private void updateCurrentIndex(int position) {
        mCurrentPosition = position;
        if (mListView != null) {
            mListView.setKeepFocusItemPosition(position);
        }
    }

    private void updateBeautyState(BeautyEntry entry, int position, EditorMenuItemViewData itemViewData) {
        if ((itemViewData == null) || (entry == null)) {
            return;
        }
        if (mItemClickedFrom != ITEM_CLICK_FROM_LIST_SCROLL) {
            mItemClickedFrom = ITEM_CLICK_FROM_USER;
        }
        mCurrentEntry = entry;
        updateCurrentIndex(position);
        BeautyEntry.BeautyState state = mCurrentEntry.getBeautyState();
        boolean lastStateIsEnable = (state == BeautyEntry.BeautyState.ENABLE);

        if (mLastEntry == mCurrentEntry) {
            boolean currentEnable = !lastStateIsEnable;
            itemViewData.setDisableStyle(!currentEnable);
            if (!currentEnable) {
                itemViewData.setCenterText(TextUtil.EMPTY_STRING);
            }
            switchBeautyState(position, mCurrentEntry);
            int currProgress = mCurrentEntry.getCurrentProgress();
            changedProgressAndUpdateUI(currProgress);
            if (mOnBeautyParamListener != null) {
                mOnBeautyParamListener.onProgressChanged(mCurrentEntry, mEntryLevelMap);
            }
            mBeautyMenuAdapter.updateItemView(position);
        }
        updateRuleView(position, itemViewData.getViewId());
        mLastEntry = mCurrentEntry;
    }

    /*
     * unselected -> selected
     * selected -> diasable_select
     * disable_select -> selected
     * */
    private void switchBeautyState(int position, BeautyEntry entry) {
        if ((mBeautyMenuAdapter == null) || (mRuleScrollerView == null)
                || (entry == null)) {
            GLog.e(TAG, "switchBeautyState: mBeautyMenuDataAdapter: " + mBeautyMenuAdapter
                    + ", mRuleScrollerView: " + mRuleScrollerView
                    + ", Entry: " + entry);
            return;
        }
        BeautyEntry.BeautyType currentBeautyType = mCurrentEntry.getType();
        BeautyEntry.BeautyState state = entry.getBeautyState();
        switch (state) {
            case DISABLE:
                entry.setBeautyState(BeautyEntry.BeautyState.ENABLE);
                if (ONE_KEY_BEAUTY.equals(currentBeautyType) && (mCurrentEntry.getCurrentProgress() == 0)) {
                    mCurrentEntry.setCurrentProgress(mCurrentEntry.getMiddleProgress());
                    EditorMenuItemViewData oneKeyBeautyItem = mData.get(BeautyEntry.getOrderByBeautyType(ONE_KEY_BEAUTY));
                    oneKeyBeautyItem.setCenterText(String.valueOf(mCurrentEntry.getCurrentProgress()));
                    updateBeautyDotUI();
                }
                break;
            case ENABLE:
                entry.setBeautyState(BeautyEntry.BeautyState.DISABLE);
                if (ONE_KEY_BEAUTY.equals(currentBeautyType)) {
                    mCurrentEntry.setCurrentProgress(0);
                    updateBeautyDotUI();
                }
                break;
            default:
                break;
        }

    }

    @Override
    public void setOnStateIconClickListener(OnEditorStateIconClickListener listener) {
        mOnStateIconClickListener = listener;
    }

    public void onFaceDetected(final boolean hasFace) {
        mHasFace = hasFace;
        if (Thread.currentThread().getId() == mContext.getMainLooper().getThread().getId()) {
            showNoFaceTip(!hasFace);
        } else if (mRootView != null) {
            mRootView.post(() -> {
                showNoFaceTip(!hasFace);
                mListView.setEnabled(hasFace);
            });
        }
    }

    private void showNoFaceTip(boolean show) {
        if (isDestroyed()) {
            return;
        }
        if (show) {
            if (mNoFaceTipView == null) {
                mNoFaceTipView = (SuitableSizeTextView) mLayoutInflater.inflate(R.layout.picture3d_editor_no_face_tip_layout, mRootView, false);
                RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT);
                lp.bottomMargin = mContext.getResources().getDimensionPixelSize(R.dimen.picture3d_no_face_tip_margin_bottom);
                lp.leftMargin = mContext.getResources().getDimensionPixelSize(R.dimen.picture3d_no_face_tip_margin_start);
                lp.rightMargin = mContext.getResources().getDimensionPixelSize(R.dimen.picture3d_no_face_tip_margin_end);
                lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
                lp.addRule(RelativeLayout.CENTER_HORIZONTAL);
                mNoFaceTipView.setLayoutParams(lp);
                mNoFaceTipView.setText(R.string.picture3d_editor_text_face_beauty_no_face_detected);
            }
            addExtraViewToRootView(mNoFaceTipView, true, true);
        } else {
            if (mNoFaceTipView != null) {
                removeExtraViewFromRootView(mNoFaceTipView, false);
            }
        }
    }

    protected void changedProgressAndUpdateUI(int progress) {
        if (mCurrentEntry == null) {
            return;
        }
        BeautyEntry.BeautyType currentType = mCurrentEntry.getType();
        if (ONE_KEY_BEAUTY.equals(mCurrentEntry.getType())) {
            oneKeyBeautyUpdateLevelMap(progress);
        } else {
            calculateSingleBeautyValue(progress, currentType);
        }
    }

    private void calculateSingleBeautyValue(int progress, BeautyEntry.BeautyType currentType) {
        BeautyEntry oneKeyEntry = mConfigMaps.get(R.id.picture3d_editor_id_beauty_one_key_beauty);
        if (!TEETH_WHITE.equals(currentType) && (oneKeyEntry != null)) {
            oneKeyEntry.setCurrentProgress(0);
            oneKeyEntry.setBeautyState(BeautyEntry.BeautyState.DISABLE);
            EditorMenuItemViewData oneKey = mData.get(BeautyEntry.getOrderByBeautyType(ONE_KEY_BEAUTY));
            if ((oneKey != null) && (!oneKey.isDisableStyle())) {
                oneKey.setDisableStyle(true);
                mBeautyMenuAdapter.updateItemView(BeautyEntry.getOrderByBeautyType(ONE_KEY_BEAUTY));
            }
        }
        mCurrentEntry.setCurrentProgress(progress);
        mCurrentEntry.updateLevel();
        singleBeautyUpdateLevelMap();
    }

    private void oneKeyBeautyUpdateLevelMap(int progress) {
        Iterator<Map.Entry<Integer, BeautyEntry>> iterator = mConfigMaps.entrySet().iterator();
        mEntryLevelMap.clear();
        while (iterator.hasNext()) {
            Map.Entry<Integer, BeautyEntry> item = iterator.next();
            int viewId = item.getKey();
            BeautyEntry entry = item.getValue();
            BeautyEntry.BeautyType entryType = entry.getType();

            if (ONE_KEY_BEAUTY.equals(entryType)) {
                continue;
            }
            if (TEETH_WHITE.equals(entryType)) {
                // do not care about teeth_white in one key beauty.
                mEntryLevelMap.put(entry, entry.getCurrentLevel());
                mUIEntryLevelMap.put(entry, entry.getCurrentLevel());
                continue;
            }
            calculateEntryValue(progress, viewId);
            entry.setBeautyState(BeautyEntry.BeautyState.ENABLE);
            EditorMenuItemViewData itemViewData = mData.get(entry.getOrder());
            if (itemViewData != null) {
                boolean changed = false;
                if (itemViewData.isDisableStyle()) {
                    itemViewData.setDisableStyle(false);
                    changed = true;
                }
                if (itemViewData.isTopTipsShow() != entry.isModified()) {
                    itemViewData.setTopTipsShow(entry.isModified());
                    changed = true;
                }
                if (changed) {
                    mBeautyMenuAdapter.updateItemView(entry.getOrder());
                }
            }
            mEntryLevelMap.put(entry, entry.getCurrentLevel());
            mUIEntryLevelMap.put(entry, entry.getCurrentLevel());
        }
    }

    public void doBeautyStatistic() {

        if (mConfigMaps.isEmpty()) {
            GLog.e(TAG, "doOneKeyBeautyStatistic: config map is null!");
            return;
        }
        StringBuilder statisticsStr = new StringBuilder();
        Iterator<Map.Entry<Integer, BeautyEntry>> iterator = mConfigMaps.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<Integer, BeautyEntry> item = iterator.next();
            BeautyEntry beautyEntry = item.getValue();
            if ((beautyEntry == null) || (beautyEntry.getType() == null)) {
                continue;
            }

            statisticsStr.append(EffectConfig.getBeautyStatisticsItemByIndex(beautyEntry.getOrder()));
            statisticsStr.append(CommonTrackConstant.SYMBOL_STAR);
            statisticsStr.append(beautyEntry.getBeautyState().equals(BeautyEntry.BeautyState.DISABLE)
                    ? BeautyEntry.DISABLE_PROGRESS // which is -1000
                    : beautyEntry.getCurrentProgress());
            statisticsStr.append(CommonTrackConstant.SYMBOL_VERTICAL_LINE);
        }

        int len = statisticsStr.length();
        if (len != 0) {
            //remove last "|"
            statisticsStr.delete(len - 1, len);
        }
    }

    private void singleBeautyUpdateLevelMap() {
        mEntryLevelMap.clear();
        if (BeautyEntry.BeautyState.DISABLE.equals(mCurrentEntry.getBeautyState())) {
            mEntryLevelMap.put(mCurrentEntry, mCurrentEntry.getDefaultValue());
            mUIEntryLevelMap.put(mCurrentEntry, mCurrentEntry.getDefaultValue());
        } else {
            mEntryLevelMap.put(mCurrentEntry, mCurrentEntry.getCurrentLevel());
            mUIEntryLevelMap.put(mCurrentEntry, mCurrentEntry.getCurrentLevel());
        }
    }

    private void calculateEntryValue(int oneKeyProgress, int beautyId) {
        BeautyEntry beautyEntry = mConfigMaps.get(beautyId);
        if (beautyEntry == null) {
            GLog.e(TAG, "updateEntryValue: mConfigMaps cannot find current beauty entry");
            return;
        }

        int value = beautyEntry.calculateLinerValue(oneKeyProgress);
        beautyEntry.setCurrentLevel(value);
        float singleBeautyProgress = oneKeyProgress * beautyEntry.getOneKeyBeautyMaxProgress() / BEAUTY_MAX_PROGRESS;
        beautyEntry.setCurrentProgress((int) (singleBeautyProgress + ZERO_POINT_FIVE_F));
        GLog.d(TAG, "updateEntryValue otherBeautyProgress: " + beautyEntry.getCurrentProgress());
    }

    public void setOnBeautyParamListener(OnBeautyParamListener listener) {
        mOnBeautyParamListener = listener;
    }

    public BeautyEntry getEntry() {
        return mCurrentEntry;
    }

    public Map<BeautyEntry, Integer> getBeautyLevelMap() {
        return mEntryLevelMap;
    }

    public Map<BeautyEntry, Integer> getUIBeautyLevelMap() {
        return mUIEntryLevelMap;
    }

    public Map<Integer, BeautyEntry> getBeautyConfigMap() {
        return mConfigMaps;
    }

    @Override
    public void adsorptionComplete(View view, int position) {
        if (!mHasFace) {
            return;
        }
        int viewId = view.getId();
        mCurrentEntry = mConfigMaps.get(viewId);
        if ((mCurrentEntry == null) || (mLastEntry == mCurrentEntry)) {
            return;
        }
        view.performClick();
        mItemClickedFrom = ITEM_CLICK_FROM_LIST_SCROLL;
    }

    private void updateRuleView(int position, int viewId) {
        int currProgress = mCurrentEntry.getCurrentProgress();
        EditorMenuItemViewData itemDate = mData.get(position);
        boolean ruleChanged = false;
        if (mRuleScrollerView.getCurrentValue() != currProgress) {
            ruleChanged = true;
        }
        mRuleScrollerView.setCurrentValue(currProgress);
        if (mCurrentEntry.getOrder() == BeautyEntry.getOrderByBeautyType(ONE_KEY_BEAUTY)) {
            mRuleScrollerView.setEnabled(true);
        } else {
            mRuleScrollerView.setEnabled(!itemDate.isDisableStyle());
        }
        if (ruleChanged && !itemDate.isDisableStyle()) {
            mRuleScrollerView.updateWithAnimation();
        }
    }

    @Override
    public void onCenterViewChanged(View view, int oldPosition, int newPosition) {
        if (!mListView.isScrollingFromUser()) {
            return;
        }
        post(() -> adsorptionComplete(view, newPosition));
    }
}
