/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.pictureeditorpage.adapter;

import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Rect;

import com.oplus.breakpad.BreakpadTombstone;
import com.oplus.gallery.addon.graphics.BitmapFactoryWrapper;
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools;
import com.oplus.gallery.foundation.util.ext.BitmapFactoryKt;
import com.oplus.gallery.foundation.util.mask.PathMask;
import com.oplus.gallery.foundation.util.math.MathUtil;
import com.oplus.gallery.business_lib.util.IBitmapEffectProcessor;
import com.oplus.gallery.pictureeditorpage.gl.widget.TileImageView;
import com.oplus.gallery.pictureeditorpage.screennail.BitmapScreenNail;
import com.oplus.gallery.photoeditor.screennail.ScreenNail;
import com.oplus.gallery.standard_lib.codec.IRegionDecoder;
import com.oplus.gallery.standard_lib.codec.ImageData;
import com.oplus.gallery.standard_lib.codec.player.AVPlayer;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GTrace;

import static com.oplus.breakpad.BreakpadUtil.generateKey;
import static com.oplus.breakpad.NativeCrashGuardKt.runNativeGuarding;
import static com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING;

public class TileImageViewAdapter implements TileImageView.Model {
    private static final String TAG = "TileImageViewAdapter";
    protected ScreenNail mScreenNail;
    private boolean mOwnScreenNail;
    private IRegionDecoder mRegionDecoder;
    private AVPlayer mAVPlayer;
    private IBitmapEffectProcessor mBitmapEffectProcessor;
    protected int mImageWidth;
    protected int mImageHeight;
    private int mLevelCount;
    private boolean mFailedToLoad;
    private boolean mStopDecode = false;

    public TileImageViewAdapter() {
    }

    public TileImageViewAdapter(Bitmap bitmap, IRegionDecoder regionDecoder, boolean opaque) {
        updateScreenNail(new BitmapScreenNail(new ImageData(bitmap), BitmapScreenNail.STYLE_DARK, opaque), true);
        mRegionDecoder = regionDecoder;
        mImageWidth = regionDecoder.getWidth();
        mImageHeight = regionDecoder.getHeight();
        mLevelCount = calculateLevelCount();
    }

    public void setBitmapEffectProcessor(IBitmapEffectProcessor processor) {
        mBitmapEffectProcessor = processor;
    }

    public synchronized void clear() {
        updateScreenNail(null, false);
        mImageWidth = 0;
        mImageHeight = 0;
        mLevelCount = 0;
        mRegionDecoder = null;
        mAVPlayer = null;
        mFailedToLoad = false;
    }

    public synchronized void setScreenNail(Bitmap bitmap, int width, int height, boolean opaque) {
        updateScreenNail(new BitmapScreenNail(new ImageData(bitmap), BitmapScreenNail.STYLE_DARK, opaque), true);
        mImageWidth = width;
        mImageHeight = height;
        mRegionDecoder = null;
        mAVPlayer = null;
        mLevelCount = 0;
        mFailedToLoad = false;
    }

    public synchronized void setScreenNail(ScreenNail screenNail, int width, int height) {
        updateScreenNail(screenNail, false);
        mImageWidth = width;
        mImageHeight = height;
        mRegionDecoder = null;
        mAVPlayer = null;
        mLevelCount = 0;
    }

    private void updateScreenNail(ScreenNail screenNail, boolean own) {
        if (mScreenNail != null && mOwnScreenNail) {
            mScreenNail.recycle();
        }
        mScreenNail = screenNail;
        mOwnScreenNail = own;
    }

    protected synchronized void setRegionDecoder(IRegionDecoder decoder) {
        mRegionDecoder = decoder;
        mAVPlayer = null;
        mImageWidth = decoder.getWidth();
        mImageHeight = decoder.getHeight();
        mLevelCount = calculateLevelCount();
        mFailedToLoad = false;
    }

    public synchronized void setRegionDecoder(IRegionDecoder decoder, int width, int height) {
        mRegionDecoder = decoder;
        mAVPlayer = null;
        mImageWidth = width;
        mImageHeight = height;
        mLevelCount = calculateLevelCount();
        mFailedToLoad = false;
    }

    public synchronized void setAVPlayer(AVPlayer player, int width, int height) {
        mRegionDecoder = null;
        mAVPlayer = player;
        mImageWidth = width;
        mImageHeight = height;
        mLevelCount = 0;
        mFailedToLoad = (player == null) || (player.getLoadingState() == AVPlayer.LoadingState.FAILED);
    }

    /**
     * only for CropImage Use
     *
     * @return void
     */
    public void stopDecode() {
        mStopDecode = true;
    }

    private synchronized int calculateLevelCount() {
        if (mScreenNail.getWidth() <= 0) {
            return 0;
        }
        return Math.max(0, MathUtil.ceilLog2((float) mImageWidth / mScreenNail.getWidth()));
    }

    // Gets a sub image on a rectangle of the current photo. For example,
    // (1, 50, 50, 100, 3, pool) means to get the region located
    // at (50, 50) with sample level 1 (ie, down sampled by 2^1) and the
    // target tile size (after sampling) 100 with border 3.
    //
    // From this spec, we can infer the actual tile size to be
    // 100 + 3x2 = 106, and the size of the region to be extracted from the
    // photo to be 200 with border 6.
    //
    // As a result, we should decode region (50-6, 50-6, 250+6, 250+6) or
    // (44, 44, 256, 256) from the original photo and down sample it to 106.
    // LiuJunChao modify for MPO file
    @Override
    public ImageData getTile(int index, int level, int x, int y, int tileWidth, int tileHeight,
                             int borderSize, BitmapPools.IBitmapPool pool) {
        return getTile(null, index, level, x, y, tileWidth, tileHeight,
                borderSize, pool);
    }

    @Override
    public ImageData getTile(IRegionDecoder decoder, int index, int level, int x, int y, int tileWidth, int tileHeight,
                             int borderSize, BitmapPools.IBitmapPool pool) {
        int b = borderSize << level;
        int tW = tileWidth << level;
        int tH = tileHeight << level;

        Rect wantRegion = new Rect(x - b, y - b, x + tW + b, y + tH + b);

        boolean needClear = false;
        IRegionDecoder regionDecoder = null;

        synchronized (this) {
            if (decoder == null) {
                regionDecoder = mRegionDecoder;
            } else {
                regionDecoder = decoder;
            }
            if (regionDecoder == null) {
                return null;
            }

            // We need to clear a reused bitmap, if wantRegion is not fully
            // within the image.
            needClear = !new Rect(0, 0, mImageWidth, mImageHeight).contains(wantRegion);
        }
        GTrace.traceBegin("Tile");
        final int sW = tileWidth + 2 * borderSize;
        final int sH = tileHeight + 2 * borderSize;
        Bitmap bitmap = (pool == null) ? null : pool.getBitmap(sW, sH, Config.ARGB_8888);
        if (bitmap != null) {
            if (needClear) {
                bitmap.eraseColor(Color.TRANSPARENT);
            }
        } else {
            bitmap = Bitmap.createBitmap(sW, sH, Config.ARGB_8888);
            bitmap.eraseColor(Color.TRANSPARENT);
        }

        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPreferredConfig = Config.ARGB_8888;
        options.inSampleSize = (1 << level);
        options.inBitmap = bitmap;
        /*
         * only MTK has options.inPostProc
         * This is a post-affect switch to processing sharpness of image
         * if turn this switch on, decoder will spread little more time to
         * process the sharpness of image synchronously
         */
        BitmapFactoryWrapper.setInPostProc(options, true);
        ImageData imageData = null;
        try {
            if (mStopDecode) {
                return null;
            }
            GTrace.traceBegin("Tile.decode");
            if (index == 0) {
                final IRegionDecoder finalRegionDecoder = regionDecoder;
                BreakpadTombstone tombstone = finalRegionDecoder.getTombstone();
                imageData = runNativeGuarding(finalRegionDecoder.getDecoderName(), generateKey(tombstone), () ->
                        finalRegionDecoder.decodeRegion(wantRegion, options, true))
                        .onNativeFailure(() -> {
                                    String filePath = EMPTY_STRING;
                                    if (tombstone != null) {
                                        filePath = tombstone.getFilePath();
                                    }
                                    GLog.w(TAG, "getTile: crash file = " + PathMask.INSTANCE.mask(filePath)
                                            + " , rect = " + wantRegion + " , options = " + BitmapFactoryKt.asString(options));
                                }
                        )
                        .getOrNull();
            }
            GTrace.traceEnd();
            if (imageData == null) {
                GLog.w(TAG, "fail in decoding region");
                return null;
            }
            bitmap = imageData.getBitmap();
            if (mBitmapEffectProcessor != null) {
                GTrace.traceBegin("Tile.decode.effectProcess");
                try {
                    mBitmapEffectProcessor.processBitmap(bitmap, pool,
                            regionDecoder.getWidth(), regionDecoder.getHeight());
                } catch (Exception e) {
                    GLog.e(TAG, "effectProcess error, errMsg: ", e);
                }
                GTrace.traceEnd();
            }
            if (bitmap != null) { // Draw clamp border
                GTrace.traceBegin("Tile.decode.clampBitmap");
                int bitmapWidth = bitmap.getWidth();
                int bitmapHeight = bitmap.getHeight();
                int optionWidth = options.outWidth;
                int optionHeight = options.outHeight;
                boolean widthChanged = bitmapWidth != optionWidth;
                boolean heightChanged = bitmapHeight != optionHeight;
                if (widthChanged || heightChanged) {
                    bitmap = BitmapUtils.clampBitmap(bitmap, 0, 0, optionWidth, optionHeight);
                    imageData.setBitmap(bitmap);
                }
                GTrace.traceEnd();
            }
        } catch (Exception e) {
            GLog.e(TAG, "getTile ", e);
        } finally {
            if (options.inBitmap != bitmap && options.inBitmap != null) {
                if (pool != null) {
                    pool.recycle(options.inBitmap);
                }
                options.inBitmap = null;
            }
            GTrace.traceEnd();
        }
        return imageData;
    }

    @Override
    public synchronized AVPlayer getPlayer() {
        return mAVPlayer;
    }

    @Override
    public synchronized ScreenNail getScreenNail() {
        return mScreenNail;
    }

    @Override
    public synchronized int getImageHeight() {
        return mImageHeight;
    }

    @Override
    public synchronized int getImageWidth() {
        return mImageWidth;
    }

    @Override
    public synchronized int getLevelCount() {
        return mLevelCount;
    }

    public synchronized void setFailedToLoad() {
        mFailedToLoad = true;
    }

    /*
     * (non-Javadoc)
     * @see com.oplus.gallery.ui.TileImageView.Model#isFailedToLoad()
     */
    @Override
    public synchronized boolean isFailedToLoad() {
        return mFailedToLoad;
    }
}
