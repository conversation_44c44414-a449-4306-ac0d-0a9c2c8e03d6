/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - HdrImageExtendDataWriter.kt
 ** Description: HdrImage拓展数据的写入者，负责将HdrImage的灰度图和metaData写入文件
 ** Version: 1.0
 ** Date : 2023/8/16
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewu<PERSON><PERSON>@Apps.Gallery3D      2023/08/16    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.pictureeditorpage.hdrimage

import android.annotation.TargetApi
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import com.oplus.gallery.addon.graphics.OplusImageHdrWrapper
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.addon.graphics.toGainmap
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_LINEAR_MASK
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_LOCAL_HDR_META_DATA
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.fileaccess.extension.OpenFileMode
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.pictureeditorpage.frame.task.IExtendDataWriter
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageType

/***
 * HdrImage拓展数据的写入者，负责将HdrImage的灰度图和metaData写入文件
 * 目前支持的HdrType 包括：LHDR、UHDR
 */
class HdrImageExtendDataWriter(
    private val context: Context,
    private val bitmap: Bitmap?,
    private val hdrDrawable: HdrImageDrawable
) : IExtendDataWriter {

    override fun getExtendDataTagFlags(): Int {
        return hdrDrawable.getType().tagFlag
    }

    override fun prepare(): Boolean {
        return when (hdrDrawable.getType()) {
            HdrImageType.LHDR -> true
            HdrImageType.UHDR -> {
                // 低于U版本的机器上，此处跳过，返回true
                return if (ApiLevelUtil.isAtLeastAndroidU()) {
                    prepareUHdrExtendData()
                } else true
            }
        }
    }

    override fun writeExtendData(fileUri: Uri): Boolean {
        return when (hdrDrawable.getType()) {
            HdrImageType.LHDR -> writeLHdrExtendData(fileUri)
            HdrImageType.UHDR -> true
        }
    }

    private fun writeLHdrExtendData(fileUri: Uri): Boolean {
        return OplusImageHdrWrapper.alpha8BitmapToByteBuffer(hdrDrawable.grayImage)?.let { maskImage ->
            FileExtendedContainer().use { container ->
                container.setDataSource(context, fileUri, OpenFileMode.MODE_READ_AND_WRITE)
                val editor = container.editor()
                if (editor == null) {
                    GLog.w(TAG) { "[writeExtendData] skip commit editor , cause editor is null" }
                    return@use false
                }
                editor.addExtensionData(EXTEND_KEY_LOCAL_HDR_LINEAR_MASK, maskImage)
                (hdrDrawable.metadata.metadata as? ByteArray)?.let {
                    editor.addExtensionData(EXTEND_KEY_LOCAL_HDR_META_DATA, it)
                } ?: kotlin.run {
                    GLog.w(TAG) { "[writeExtendData] skip commit editor , metadata is not equal ByteArray" }
                    return@use false
                }
                editor.commit()
                true
            }
        } ?: false
    }

    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun prepareUHdrExtendData(): Boolean {
        bitmap ?: return false
        val ultraHdrInfo = (hdrDrawable.metadata.metadata as? UltraHdrInfo) ?: return false
        bitmap.gainmap = ultraHdrInfo.toGainmap(hdrDrawable.grayImage)
        return true
    }

    override fun getNeedStorageSpace(): Int {
        return hdrDrawable.metadata.size() + hdrDrawable.grayImage.allocationByteCount
    }

    companion object {
        private const val TAG = "HdrImageExtendDataWriter"
    }
}