/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditorBaseSuperTextUIController
 ** Description: 超级文本1.0和2.0的UIController基类
 ** Version: 1.0
 ** Date : 2023/6/2
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2023/06/02    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.supertext

import android.view.View
import android.view.ViewGroup
import com.oplus.gallery.foundation.ui.dialog.LoadingHelper
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.pictureeditorpage.PictureContext
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.pictureeditorpage.app.supertext.docconvert.DocItemDialog
import com.oplus.gallery.pictureeditorpage.base.EditorNavigateUIController
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState
import com.oplus.gallery.standard_lib.ui.util.ToastUtil

/**
 * 超级文本1.0和2.0的controller基类，将共用的逻辑抽离出来方便复用
 */
abstract class EditorBaseSuperTextUIController(
    val context: PictureContext,
    val root: ViewGroup?,
    val state: EditorBaseState?
) : EditorNavigateUIController(context, root, state) {

    private var docItemDialog: DocItemDialog? = null

    /**
     * 显示转文档面板
     */
    fun showDocConvertDialog() {
        if (context.activity.isFinishing || context.activity.isDestroyed) {
            GLog.e(TAG, "[showDocConvertDialog] fail, because activity is finishing or destroyed")
            return
        }
        docItemDialog ?: let {
            docItemDialog = DocItemDialog(context.activity)
        }
        docItemDialog?.onStartConvertListener = {
            val docConvertingDialogHelper =
                LoadingHelper(mContext, it.loadingTitleId)
            (mEditorState as? EditorBaseSuperTextState)?.convertDocument(
                docType = it,
                onConvertStart = {
                    docConvertingDialogHelper.showDialog(0)
                },
                onConvertFinish = {
                    docConvertingDialogHelper.hideDialog(0)
                })
        }
        docItemDialog?.show(getDocConvertAnchorView())
    }

    /**
     * 获取转文档弹窗锚点的View
     * @return 转文档弹窗锚点的View
     */
    protected abstract fun getDocConvertAnchorView(): View?

    /**
     * 转文档的弹窗是否正在显示
     * @return 是否正在显示
     */
    protected fun isDocConvertDialogShowing(): Boolean = docItemDialog?.isDialogShowing() ?: false

    protected fun updateConvertDialogPosition() {
        docItemDialog?.updateAnchorview(getDocConvertAnchorView())
    }

    open fun toastConvertFail() {
        ToastUtil.showShortToast(R.string.picture3d_super_text_convert_doc_fail)
    }

    companion object {
        private const val TAG = "EditorBaseSuperTextUIController"
        const val DIALOG_DOC_CONVERTING = 1
    }
}