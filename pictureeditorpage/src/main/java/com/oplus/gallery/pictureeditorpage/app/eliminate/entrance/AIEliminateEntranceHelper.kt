/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AIEliminateEntranceHelper.kt
 ** Description : 智能消除 权限弹窗、网络弹窗拦截
 ** Version     : 1.0
 ** Date        : 2023/11/08
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                  <date>      <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2023/11/08     1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.eliminate.entrance

import android.content.Context
import android.graphics.RectF
import android.os.Bundle
import android.util.Size
import com.oplus.gallery.business_lib.aiunitdownload.AIUnitFeatureConfigHelper
import com.oplus.gallery.business_lib.aiunitdownload.AIUnitPrivacyInterceptor
import com.oplus.gallery.business_lib.aiunitdownload.EntranceListener
import com.oplus.gallery.business_lib.aiunitdownload.PermissionCheckBase
import com.oplus.gallery.business_lib.aiunitdownload.RefuseNextAction
import com.oplus.gallery.business_lib.ui.dialog.authoring.NetworkEnableDialogInterceptor
import com.oplus.gallery.business_lib.ui.dialog.authoring.PrivacyDialogInterceptor
import com.oplus.gallery.business_lib.ui.dialog.authoring.UserAgreementDialogInterceptor
import com.oplus.gallery.business_lib.ui.dialog.chain.AlertChainManager
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlert.Companion.ALERT_END_TYPE
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlertChainFinishCallback
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.ext.runOnUiThread
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.UserProfile.Privacy.AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.ai.AIEliminateModelUpdater
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.pictureeditorpage.app.eliminate.entrance.AIEliminateOfflineInterceptor.Companion.ALERT_TYPE_OFF_LINE
import com.oplus.gallery.pictureeditorpage.app.eliminate.entrance.AIEliminateQueuingExperienceInterceptor.Companion.ALERT_TYPE_QUEUE_EXPERIENCE
import com.oplus.gallery.pictureeditorpage.app.eliminate.interceptor.AIEliminateModelDownloadInterceptor
import com.oplus.gallery.pictureeditorpage.app.eliminate.interceptor.AIEliminateModelDownloadInterceptor.Companion.ALERT_TYPE_AI_ELIMINATE_MODE_DOWNLOAD

internal class AIEliminateEntranceHelper(private val context: Context) : PermissionCheckBase(context) {

    private var aiEliminateEntranceListener: EntranceListener? = null

    private val chainFinishCallback = object : IAlertChainFinishCallback {
        override fun finish(alertType: Int) {
            if (isChainFinished) {
                GLog.d(TAG) { "[finish]:isDestroyed" }
                return
            }
            if (AIEliminateModelUpdater.isUpdating) {
                GLog.d(TAG) { "[finish]:isUpdating" }
                return
            }
            GLog.d(TAG) { "[finish]:alertType->$alertType" }
            when (alertType) {
                ALERT_END_TYPE, ALERT_TYPE_AI_ELIMINATE_MODE_DOWNLOAD -> aiEliminateEntranceListener?.onEnter()

                ALERT_TYPE_OFF_LINE, ALERT_TYPE_QUEUE_EXPERIENCE -> {
                    aiEliminateEntranceListener?.onRefuse(RefuseNextAction.ACTION_USE_MAGIC_ELIMINATE)
                }

                else -> aiEliminateEntranceListener?.onRefuse()
            }
            finishChain()
        }
    }

    fun checkAiEliminateEntrance(imageSize: Size, displayRectF: RectF) {
        if (AIEliminateModelUpdater.isUpdating) {
            GLog.d(TAG) { "checkAiEliminateEntrance is updating" }
            return
        }
        createChainManager(imageSize, displayRectF).apply {
            alertChainManager = this
            setFinishCallback(chainFinishCallback)
            isChainFinished = false
            AIUnitFeatureConfigHelper.writeDetectStateWithTimeout(context) {
                runOnUiThread {
                    proceed(alertChainDataDeliverer)
                }
            }
        }
    }

    private fun createChainManager(imageSize: Size, displayRect: RectF): AlertChainManager<Bundle> {
        return AlertChainManager.create<Bundle>().apply {
            // 下架拦截
            addInterceptor(AIEliminateOfflineInterceptor(context))
            // 用户须知检查
            addInterceptor(UserAgreementDialogInterceptor(context))
            // 隐私权限检查
            addInterceptor(
                PrivacyDialogInterceptor(
                    context,
                    AUTHORIZE_REQUEST_SERVER_UPDATE_AND_DOWNLOAD,
                    com.oplus.gallery.framework.abilities.authorizing.R.string.authorizing_eliminate_statement
                )
            )
            // 图片尺寸限制弹窗，对于过小或过大尺寸的照片，不予消除
            addInterceptor(EliminateImageSizeInterceptor(context, imageSize))
            // 无网络权限弹窗
            addInterceptor(
                NetworkEnableDialogInterceptor(
                    context = context,
                    messageResID = R.string.picture3d_editor_text_eliminate_no_network_tips_content,
                    titleResID = R.string.picture3d_editor_text_eliminate_no_network_tips_title
                )
            )
            // 未写入AI消除配置参数时弹窗
            addInterceptor(AIEliminateConfigInterceptor(context, displayRect))
            // 排队弹窗
            addInterceptor(AIEliminateQueuingExperienceInterceptor(context))
            // 登录拦截
            addInterceptor(AIEliminateLoginInterceptor(context))
            // AIUnit用户须知拦截器
            addInterceptor(AIUnitPrivacyInterceptor(context))
            // AI消除模型是否需要下载拦截
            addInterceptor(AIEliminateModelDownloadInterceptor(context))
            // 消除和分割接口是否可用的拦截器
            addInterceptor(AIEliminateAvailableInterceptor(context))
            // AIUnit外销等待域名下载的拦截器
            addInterceptor(AIUnitURLWaitingInterceptor(context, displayRect))
        }
    }

    fun removePassersDownloadRequestIfNeed() {
        AIEliminateModelUpdater.removeDownloadRequestIfNeed()
    }

    fun setAiEliminateEntranceListener(listener: EntranceListener?) {
        this.aiEliminateEntranceListener = listener
    }

    companion object {
        private const val TAG = "AIEliminateEntranceHelper"
    }
}