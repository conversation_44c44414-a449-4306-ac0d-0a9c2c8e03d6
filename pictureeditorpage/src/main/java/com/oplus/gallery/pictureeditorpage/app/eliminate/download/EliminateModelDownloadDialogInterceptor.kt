/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EliminateModelDownloadDialogInterceptor.kt
 ** Description: 消除模型下载弹窗的拦截器
 ** Version: 1.0
 ** Date : 2023/9/30
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2023/09/30    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.eliminate.download

import android.content.Context
import android.os.Bundle
import android.view.KeyEvent
import android.widget.Toast
import com.oplus.gallery.basebiz.permission.NetworkPermissionManager
import com.oplus.gallery.basebiz.R as BaseR
import com.oplus.gallery.business_lib.ui.dialog.authoring.AuthorizationAlertInterceptor
import com.oplus.gallery.business_lib.ui.dialog.authoring.DialogInterceptors.MOBILE_WARN_DIALOG_TYPE
import com.oplus.gallery.business_lib.ui.dialog.chain.AlertChainDataDeliverer
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlert
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlertChainFinishCallback
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlertInterceptor
import com.oplus.gallery.business_lib.ui.dialog.chain.IAlertNegativeListener
import com.oplus.gallery.business_lib.ui.dialog.chain.IKeyListener
import com.oplus.gallery.business_lib.ui.dialog.chain.ProgressDialogChainAlert
import com.oplus.gallery.foundation.util.thread.MainSwitchHandler
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.framework.abilities.download.processor.ProcessResult
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.ai.ModelDownloadListener
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.ai.ModelInstallListener
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.pictureeditorpage.app.aifilter.ui.AiFilterDialogHelper
import com.oplus.gallery.pictureeditorpage.app.aifilter.ui.AiFilterDialogHelper.Companion.MODEL_MODEL_MAX_PROGRESS
import com.oplus.gallery.pictureeditorpage.app.eliminate.entrance.NormalEliminateEntranceHelper
import com.oplus.gallery.pictureeditorpage.app.eliminate.entrance.NormalEliminateEntranceHelper.Companion.MODEL_DOWNLOAD_DIALOG_TYPE
import com.oplus.gallery.pictureeditorpage.app.eliminate.entrance.NormalEliminateEntranceHelper.Companion.MODEL_UPDATE_DIALOG_TYPE
import com.oplus.gallery.standard_lib.util.network.NetworkMonitor

internal class EliminateModelDownloadDialogInterceptor(
    private val context: Context,
    private val sourceDownloader: IEliminateSourceDownloader,
    private val downloadObserver: DownloadObserver,
    private val onNetworkRefuse: () -> Unit
) : AuthorizationAlertInterceptor() {

    private var isDestroy = false
    private val title: String = context.resources.getString(R.string.picture3d_ai_filter_dialog_download_title)
    private var isCancelDownload = false
    private val networkListener: NetworkMonitor.NetworkListener =
        NetworkMonitor.NetworkListener { connectType, isValidated ->
            if ((connectType == NetworkMonitor.ConnectType.MOBILE) && isValidated && (!NetworkPermissionManager.isAllowDownloadOnMobile)) {
                GLog.d(TAG, "[onStateChange]:mobile available")
                reset()
                onNetworkRefuse.invoke()
            }
        }

    override fun shouldIntercept(alertChainDataDeliverer: AlertChainDataDeliverer<Bundle>?): Boolean {
        GLog.d(
            TAG, "EliminateModelDownloadDialogInterceptor.shouldIntercept, alertChainDataDeliverer?.currentAlertType = " +
                    "${alertChainDataDeliverer?.currentAlertType}, EliminatePenHelper.isSourceReady = ${sourceDownloader.isSourceReady}"
        )
        return ((alertChainDataDeliverer?.currentAlertType == MODEL_UPDATE_DIALOG_TYPE)
                || (alertChainDataDeliverer?.currentAlertType == MOBILE_WARN_DIALOG_TYPE))
                || (!sourceDownloader.isSourceReady)
    }

    override fun execute(
        next: IAlertInterceptor.IAlertChain<Bundle>,
        alert: IAlert,
        callback: IAlertChainFinishCallback?,
        alertChainDataDeliverer: AlertChainDataDeliverer<Bundle>?
    ) {
        super.execute(next, alert, callback, alertChainDataDeliverer)
        download(alert as? ProgressDialogChainAlert)
    }

    override fun destroy() {
        super.destroy()
        isDestroy = true
    }

    override fun createAlert(): ProgressDialogChainAlert {
        return ProgressDialogChainAlert.Builder(context)
            .buildMaxProgress(MODEL_MODEL_MAX_PROGRESS)
            .buildManualCancelable(true)
            .buildAlertType(MODEL_DOWNLOAD_DIALOG_TYPE)
            .buildTitle(title)
            .buildNegative(context.getString(BaseR.string.common_cancel))
            .buildNegativeListener(object : IAlertNegativeListener<ProgressDialogChainAlert> {
                override fun onNegative(alert: ProgressDialogChainAlert) {
                    GLog.d(TAG, "[onNegative]: AlertType->${alert.type()}")
                    cancelDownload(alert.type())
                }
            })
            .buildKeyListener(object : IKeyListener<ProgressDialogChainAlert> {
                override fun onKey(alert: ProgressDialogChainAlert, keyCode: Int, event: KeyEvent?): Boolean {
                    GLog.d(TAG, "[onKey]: 4==keyCode->$keyCode,1==${event?.action} alertType->${alert.type()}")
                    if ((keyCode == KeyEvent.KEYCODE_BACK) && (event?.action == KeyEvent.ACTION_UP)) {
                        cancelDownload(alert.type())
                    }
                    return false
                }
            }).create()
    }

    private fun cancelDownload(alertType: Int) {
        reset()
        callback?.finish(alertType)
    }

    private fun reset() {
        isCancelDownload = true
        sourceDownloader.cancelDownload(downloadObserver.downloadTaskId)
        downloadObserver.setDownloadListener(null)
        sourceDownloader.removeDownloadObserver(downloadObserver)
        NetworkMonitor.removeListener(networkListener)
        alert?.dismiss()
    }

    private fun download(progressAlert: ProgressDialogChainAlert?) {
        GLog.d(TAG, "[startDownlaodModel]: AlertType->${progressAlert?.type()}")
        NetworkMonitor.addListener(networkListener)
        downloadObserver.setDownloadListener(object : ModelDownloadListener {

            override fun onDownloading(bytesRead: Long, contentLength: Long) {
                val currentProgress = if (contentLength > 0) (bytesRead * MODEL_MODEL_MAX_PROGRESS / contentLength).toInt() else {
                    0
                }
                if (progressAlert?.getProgress() != currentProgress) {
                    progressAlert?.updateProgress(currentProgress)
                }
            }

            override fun onDownloadSuccess(version: Int) {
                NetworkMonitor.removeListener(networkListener)
                GLog.d(TAG, "[onDownloadSuccess]")
                if (isCancel()) {
                    GLog.d(TAG, "[onDownloadSuccess]:isCancelDownload->$isCancelDownload")
                    return
                }
                alertChainDataDeliverer?.data?.putInt(NormalEliminateEntranceHelper.KEY_MODEL_DOWNLOAD_VERSION, version)
                MainSwitchHandler.getInstance().post {
                    nextNode?.proceed(callback, alertChainDataDeliverer)
                    progressAlert?.dismiss()
                }
            }

            override fun onDownloadFail(code: Int) {
                NetworkMonitor.removeListener(networkListener)
                GLog.d(TAG, "[onDownloadFail]: code->$code AlertType->${progressAlert?.type()}")
                if (isCancel()) {
                    GLog.d(TAG, "[onDownloadFail]:isCancelDownload->$isCancelDownload")
                    return
                }
                MainSwitchHandler.getInstance().post {
                    progressAlert?.dismiss()
                    Toast.makeText(context, R.string.picture3d_ai_filter_toast_download_fail, Toast.LENGTH_SHORT).show()
                    callback?.finish(progressAlert?.type() ?: MODEL_DOWNLOAD_DIALOG_TYPE)
                }
            }

            override fun onDownloadCancel() = Unit
        })
        sourceDownloader.downloadModel(context.applicationContext, downloadObserver)
    }

    private fun isCancel(): Boolean = isCancelDownload || isDestroy

    companion object {
        private const val TAG = "EliminateModelDownloadDialogInterceptor"
    }
}

/**
 * 下载观察者
 */
class DownloadObserver : CommonModelLoadingTemplate.ModelLoadingListener {
    private var downloadListener: ModelDownloadListener? = null
    private var installListener: ModelInstallListener? = null
    var downloadTaskId: Int? = 0

    fun setDownloadListener(downloadListener: ModelDownloadListener?) {
        this.downloadListener = downloadListener
    }

    fun setInstallListener(installListener: ModelInstallListener?) {
        this.installListener = installListener
    }

    override fun onProgress(progress: Int) {
        downloadListener?.onDownloading(progress.toLong(), MODEL_MODEL_MAX_PROGRESS.toLong())
    }

    override fun onFileDownloadSuccess() {
        downloadListener?.onDownloadSuccess(MagicEliminateSourceDownloader().cloudVersion)
    }

    override fun onFileDownloadFailure(result: ProcessResult) {
        downloadListener?.onDownloadFail(result.code.ordinal)
    }

    override fun onSuccess() {
        installListener?.onInstallSuccess()
    }

    override fun onFailure(result: ProcessResult) {
        GLog.d(NormalEliminateEntranceHelper.TAG, "onSourceCheckFail->$installListener")
        var delayTime = 0L
        if (null == installListener) {
            delayTime = AiFilterDialogHelper.INSTALL_FAIL_DELAY_TIME
        }
        MainSwitchHandler.getInstance().postDelayed({
            installListener?.onInstallFail(result.code.ordinal)
        }, delayTime)
    }
}