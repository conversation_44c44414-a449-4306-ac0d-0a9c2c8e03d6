/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : GifSynthesisViewModel.kt
 ** Description : 合成GIF使用的ViewModel
 ** Version     : 1.0
 ** Date        : 2023/02/16
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2023/02/16      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.gifsynthesis.viewmodel

import android.app.Application
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.RectF
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.util.Size
import android.util.SizeF
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.oplus.gallery.basebiz.constants.IntentConstant.GifSynthesisConstant.JUMP_FORM_CSHOT
import com.oplus.gallery.basebiz.constants.IntentConstant.GifSynthesisConstant.JUMP_FORM_MULTIPLE_SELECTION
import com.oplus.gallery.basebiz.constants.IntentConstant.GifSynthesisConstant.JUMP_FORM_OLIVE
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisSpecificationChecker.SYNTHESIS_COUNT_THRESHOLD_MAX
import com.oplus.gallery.business_lib.gifsynthesis.GifSynthesisSpecificationChecker.SYNTHESIS_COUNT_THRESHOLD_MIN
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.utils.ImageTypeUtils
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_HDR_TRANSFORM_DATA
import com.oplus.gallery.foundation.codec.extend.HdrTransformData
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct
import com.oplus.gallery.foundation.fileaccess.FileAccessManager
import com.oplus.gallery.foundation.fileaccess.FileConstants
import com.oplus.gallery.foundation.fileaccess.data.OpenFileRequest
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.clipMaxFitSize
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.getRectFWithContainerByRatio
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.foundation.util.ext.safeUse
import com.oplus.gallery.foundation.util.ext.toSize
import com.oplus.gallery.foundation.util.ext.toSizeF
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.frameextractor_lib.IMediaMetadataRetriever
import com.oplus.gallery.framework.abilities.caching.CacheOperation
import com.oplus.gallery.framework.abilities.caching.StorageQuality
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.crop.CropParams
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKey
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.framework.app.withAbility
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.olive_decoder.OLivePhoto
import com.oplus.gallery.olive_decoder.livephoto.MicroVideo
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.OliveVideoEdgeEditorImp
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.OliveVideoEdgeEditorImp.Companion.DEFAULT_DISPLAY_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.IOliveVideoEngine
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.OliveTransformConfig
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.OliveVideoEngine
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.VideoSourceDescriptor
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.ClipRatio
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.ENCODING_FAILED
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.FRAME_INTERVAL
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.HIGH_DEFINITION
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.OLIVE_PHOTO_TMP_DIR
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.PLAY_INTERVAL_IN_SECOND_DEFAULT
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.PLAY_INTERVAL_IN_SECOND_THRESHOLD_MAX
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.PLAY_INTERVAL_IN_SECOND_THRESHOLD_MIN
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.SAVE_FAILED
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.STANDARD_DEFINITION
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.GifSynthesisConstants.SUPER_DEFINITION
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.IGifSynthesisPreviewOperator
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.OperateMode
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.PlayOrder
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.common.GifConvertEngine
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.common.GifSynthesisDataManager
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.common.GifSynthesisDataManager.OliveThumbnailData
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.common.GifSynthesisListener
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.common.GifSynthesisUtils
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.common.OliveThumbnailParser
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.dataloader.AsyncGifContentFactory
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.dataloader.GifContentFactory
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.dataloader.GifContentType
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.ext.CoroutineParameter
import com.oplus.gallery.pictureeditorpage.app.gifsynthesis.ext.requestDecodeAllSelectionPhoto
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.CPU
import com.oplus.gallery.standard_lib.app.MeicamEdit
import com.oplus.gallery.standard_lib.codec.player.effect.EffectUtil
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_10_SEC_IN_MS
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.channels.trySendBlocking
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.system.measureTimeMillis
import kotlin.collections.reversed as ktReversed

/**
 * 合成GIF使用的ViewModel
 *
 * 可通过调用[updatePreview***]方法来控制预览区域的资源播放间隔/显示比例/播放顺序/播放暂停
 */
class GifSynthesisViewModel(application: Application) : BaseLoadingViewModel(application) {
    /**
     * 预览视图 资源播放间隔， 默认0.5s
     */
    var previewPlayIntervalInSecond: Float = PLAY_INTERVAL_IN_SECOND_DEFAULT

    /**
     * 预览视图 显示比例， 默认原始比例
     */
    internal var previewDisplayRatio: ClipRatio = ClipRatio.RATIO_ORIGINAL
        private set

    /**
     * 预览视图 播放顺序， 默认正序播放
     */
    internal var previewPlayOrder: PlayOrder = PlayOrder.ORDER_POSITIVE
        private set

    /**
     * 预览视图 是否正在播放， 默认播放
     */
    internal var previewIsPlaying: Boolean = true
        private set

    /**
     * 保存时，选择的清晰度， 默认标清
     * */
    internal var saveDefinition: String = STANDARD_DEFINITION
        private set

    /**
     * 资源集第一个资源的尺寸
     *
     * 将在更新资源集时为其赋值
     * 用于计算选择画质界面的保存尺寸的计算
     */
    var firstResSize = SizeF(0f, 0f)
        private set

    /**
     * 当生命周期变更时是否需要自动更新预览播放状态。
     *
     * 用于熄屏/亮屏、home到桌面/回到应用 等场景中途暂停播放/回到相册预览gif界面继续播放
     *
     * 默认true，因为默认就是一进入就要播放，所以只要用户没有手动操作预览界面导致不播放（如进入添加照片页面需要暂停播放），那就再次回到预览界面时继续播放
     */
    private var shouldAutoUpdatePlayStateWhenLifecycleChanged = true

    /**
     * 上次用于预览的资源
     * 添加该成员变量便于回收上次的资源
     */
    private var lastPreviewContent: Bitmap? = null
        set(value) {
            if (value == field) return
            field?.recycle()
            field = value
        }

    /**
     * 合成gif 预览操作
     * 可对预览视图设置 宽高比、间隔、播放顺序 等
     */
    private val previewOperator: IGifSynthesisPreviewOperator<*> by lazy {
        when (gifSynthesisData?.jumpType) {
            JUMP_FORM_OLIVE -> {
                GifSynthesisPreviewOperator<OliveThumbnailData>(coroutineScope = this, JUMP_FORM_OLIVE).apply {
                    currentResourceCallback = { oliveThumbnailData, _ ->
                        if (oliveThumbnailData.path.toString().isNotEmpty()) {
                            notifyPreviewContentChanged(oliveThumbnailData.path)
                        } else if (oliveThumbnailData.timeMs >= 0) {
                            notifyPreviewContentChanged(oliveThumbnailData.timeMs)
                        }
                    }
                }
            }

            else -> {
                GifSynthesisPreviewOperator<Path>(coroutineScope = this).apply {
                    currentResourceCallback = { path, _ ->
                        notifyPreviewContentChanged(path)
                    }
                }
            }
        }
    }

    /**
     * 预览区域裁剪比例的预览操作模式
     * View层可监听此LiveData来驱动预览区域的可见界面及网格线
     */
    internal val previewDisplayRatioMode: LiveData<OperateMode> get() = _previewDisplayRatioMode
    private val _previewDisplayRatioMode = MutableLiveData<OperateMode>()

    /**
     * 预览视图当前的内容资源
     */
    internal val previewContentResource: LiveData<Bitmap> get() = _previewContentResource
    private val _previewContentResource = MutableLiveData<Bitmap>()

    /**
     * 预览视图所有的内容资源
     * */
    internal val previewResData: LiveData<List<Path>> get() = _previewResData
    private val _previewResData = MutableLiveData<List<Path>>()

    /**
     * 预览视图所有的内容资源 for olive
     * */
    internal val previewForOliveResData: LiveData<List<OliveThumbnailData>> get() = _previewForOliveResData
    private val _previewForOliveResData = MutableLiveData<List<OliveThumbnailData>>()

    /**
     * gif 合成功能状态
     */
    internal val gifSynthesisStatus: LiveData<GifSynthesisStatus> get() = _gifSynthesisStatus
    private val _gifSynthesisStatus = MutableLiveData<GifSynthesisStatus>()

    /**
     * gif 合成sdk能力
     */
    private var gifConvertEngine: GifConvertEngine? = null

    /**
     * gif合成GifSynthesisDataManager.gifSynthesisData实例，用于获取manager的相关属性
     */
    private var gifSynthesisData: GifSynthesisDataManager.GifSynthesisData? = null

    /**
     * olive图片解析器
     */
    private var oliveThumbnailParser: OliveThumbnailParser? = null

    private var oliveVideoEngine: IOliveVideoEngine? = null

    /**剪辑时长之后的olive解码后的video参数*/
    private var videoInfo: VideoInfo? = null

    /**
     * 内容工厂，负责资源的加载、缓存、销毁。
     */
    private val contentFactory: GifContentFactory by lazy {
        AsyncGifContentFactory(
            application,
            session,
            viewModelScope
        )
    }

    /**
     * 磁盘获取bitmap,进行GIF合成前处理任务
     */
    private var getDiskCacheBitmapDisposeJob: Job? = null

    /**
     * 获取处理后的bitmap，送sdk进行合成的任务
     */
    private var convertBitmapListToGifJob: Job? = null

    /**
     * 当前合成是否被取消
     */
    private var cancelGifSynthesisFlag = false

    /**
     * 当前合成是否成功
     */
    @Volatile
    private var gifSynthesisCompletedFlag = false

    /**
     * 计算画质尺寸
     * @param originSize 原始尺寸，默认为资源集的第一个资源的尺寸[firstResSize]
     * @param sizeCallback 将计算的标清、高清、超清的宽高尺寸回调用于显示
     */
    internal fun calculateDefinitionSize(
        originSize: SizeF = firstResSize,
        sizeCallback: (standardDefinitionSize: Size, highDefinitionSize: Size, superDefinitionSize: Size) -> Unit
    ) {
        if (originSize.height == 0f) {
            GLog.w(TAG) { "calculateDefinitionSize originSize is invalid:$originSize" }
            return
        }
        val ratio = if (previewDisplayRatio == ClipRatio.RATIO_ORIGINAL) {
            originSize.width / originSize.height
        } else previewDisplayRatio.value
        // 按照指定比例 从原始尺寸获取一个平铺的最大的尺寸
        val fitSize = originSize.clipMaxFitSize(ratio)
        val maxSizeLen = fitSize.maxLen()
        if (maxSizeLen == 0f) {
            GLog.w(TAG, "calculateDefinitionSize maxSizeLen is 0")
            return
        }
        // 计算等比缩放比例
        val standardScale = RES_SAVE_LONG_SIDE_LENGTH_IN_STANDARD_DEFINITION / maxSizeLen
        val highScale = RES_SAVE_LONG_SIDE_LENGTH_IN_HIGH_DEFINITION / maxSizeLen
        val superScale = RES_SAVE_LONG_SIDE_LENGTH_IN_SUPER_DEFINITION / maxSizeLen
        // 计算不同画质的尺寸
        val standardW = standardScale * fitSize.width
        val standardH = standardScale * fitSize.height
        val highW = highScale * fitSize.width
        val highH = highScale * fitSize.height
        val superW = superScale * fitSize.width
        val superH = superScale * fitSize.height
        // 回调结果
        sizeCallback(
            SizeF(standardW, standardH).toSize(),
            SizeF(highW, highH).toSize(),
            SizeF(superW, superH).toSize()
        )
    }

    /**
     * 设置保存时选择的分辨率类型
     * @param type 分辨率类型，分标清, 高清和超清
     * */
    internal fun updateSavingDefinitionType(type: String) {
        this.saveDefinition = type
    }

    /**
     * 设置预览视图2张资源播放间隔的时间
     * 更新之后，预览区域前后两张资源播放间隔的时间为当前设置值
     *
     * @param timeInSecond 前后两张资源播放间隔的时间，以s为单位，
     *                     ∈[[PLAY_INTERVAL_IN_SECOND_THRESHOLD_MIN],[PLAY_INTERVAL_IN_SECOND_THRESHOLD_MAX]]
     */
    internal fun updatePreviewPlayInterval(mode: OperateMode, timeInSecond: Float) {
        previewOperator.updatePlayInterval(
            mode,
            timeInSecond.coerceIn(PLAY_INTERVAL_IN_SECOND_THRESHOLD_MIN, PLAY_INTERVAL_IN_SECOND_THRESHOLD_MAX)
        ) {
            this.previewPlayIntervalInSecond = it
        }
    }

    /**
     * 设置预览视图预览区域 对应比例。 此时会对资源进行裁剪
     * 更新后，将移除裁剪框遮罩
     *
     * @param mode 操作模式,EDIT只影响编辑时预览效果，APPLY影响预览效果和生成效果
     * @param clipRatio 裁剪比例 [mode]=[OperateMode.CANCEL]时可以随便传值，内部会还原为上次应用的值
     */
    internal fun updatePreviewDisplayRatio(mode: OperateMode, clipRatio: ClipRatio) {
        previewOperator.updateDisplayRatio(mode, clipRatio) {
            this.previewDisplayRatio = it
            _previewDisplayRatioMode.postValue(mode)
        }
    }

    /**
     * 设置预览视图资源集,for olive
     * 更新之后，预览区将显示为当前资源集的资源
     * list的数量需要限制在[2,50]
     *
     * @param resource 资源集 [mode]=[OperateMode.CANCEL]时可以随便传值，内部会还原为上次应用的值
     * @param playStartIndex 播放从哪个资源开始播放, 仅当[mode]为[OperateMode.APPLY]时有效
     * @param resultCallback 结果回调
     */
    private fun updatePreviewResDataForOlive(
        mode: OperateMode,
        resource: List<OliveThumbnailData>,
        playStartIndex: Int? = 0,
        resultCallback: ((Boolean) -> Unit)? = null
    ) {
        val resourceSize = resource.size
        if ((resourceSize > SYNTHESIS_COUNT_THRESHOLD_MAX) || (resourceSize < SYNTHESIS_COUNT_THRESHOLD_MIN)) {
            GLog.d(TAG) { "[updatePreviewResDataForOlive] resourceSize is invalid: $resourceSize" }
            resultCallback?.invoke(false)
            return
        }
        (previewOperator as IGifSynthesisPreviewOperator<OliveThumbnailData>).updateResData(mode, resource, playStartIndex) {
            if (mode != OperateMode.EDIT) {
                shouldAutoUpdatePlayStateWhenLifecycleChanged = true
            }
            launch(Dispatchers.IO) {
                val bitmap = getCacheBitmapForOlive(it.first())
                bitmap?.let { bmp ->
                    // 资源集第一个资源的宽高
                    firstResSize = Size(bmp.width, bmp.height).toSizeF()
                    // 通知资源集变更了
                    _previewForOliveResData.postValue(resource)
                    resultCallback?.invoke(true)
                    if (bmp.isRecycled.not()) bmp.recycle()
                }
            }
        }
    }

    private suspend fun getCacheBitmapForOlive(oliveThumbnailData: OliveThumbnailData): Bitmap? {
        val bitmap = if (oliveThumbnailData.path.toString().isNotEmpty()) {
            getResourceFromDisCache(oliveThumbnailData.path)
        } else if (oliveThumbnailData.timeMs >= 0) {
            getBitmapResourceFromDisCache(oliveThumbnailData.timeMs)
        } else {
            GLog.e(TAG) { "getBitmapFromDisCacheForOlive,firstData is null" }
            null
        }
        return bitmap
    }

    /**
     * 提供非挂载的获取单张磁盘缓存bitmap
     */
    private fun getCacheBitmapResForOlive(oliveThumbnailData: OliveThumbnailData): Bitmap? {
        val bitmap = if (oliveThumbnailData.path.toString().isNotEmpty()) {
            LocalMediaDataHelper.getLocalMediaItem(oliveThumbnailData.path)?.let { mediaItem ->
                getResFromDiskCache(mediaItem)
            }
        } else if (oliveThumbnailData.timeMs >= 0) {
            getBitmapResFromDisCache(oliveThumbnailData.timeMs)
        } else {
            null
        }
        return bitmap
    }

    /**
     * 设置预览视图资源集
     * 更新之后，预览区将显示为当前资源集的资源
     * list的数量需要限制在[2,50]
     *
     * @param resource 资源集 [mode]=[OperateMode.CANCEL]时可以随便传值，内部会还原为上次应用的值
     * @param playStartIndex 播放从哪个资源开始播放, 仅当[mode]为[OperateMode.APPLY]时有效
     * @param resultCallback 结果回调
     */
    private fun updatePreviewResData(
        mode: OperateMode,
        resource: List<Path>,
        playStartIndex: Int? = 0,
        resultCallback: ((Boolean) -> Unit)? = null
    ) {
        val resourceSize = resource.size
        if ((resourceSize > SYNTHESIS_COUNT_THRESHOLD_MAX) || (resourceSize < SYNTHESIS_COUNT_THRESHOLD_MIN)) {
            GLog.d(TAG) { "[updateResData] resourceSize is invalid: $resourceSize" }
            resultCallback?.invoke(false)
            return
        }
        (previewOperator as IGifSynthesisPreviewOperator<Path>).updateResData(mode, resource, playStartIndex) {
            if (mode != OperateMode.EDIT) {
                shouldAutoUpdatePlayStateWhenLifecycleChanged = true
            }
            launch(Dispatchers.IO) {
                val bitmap = getResourceFromDisCache(it[0])
                bitmap?.let { bmp ->
                    // 资源集第一个资源的宽高
                    firstResSize = Size(bmp.width, bmp.height).toSizeF()
                    // 通知资源集变更了
                    _previewResData.postValue(resource)
                    resultCallback?.invoke(true)
                    bmp.recycle()
                }
            }
        }
    }

    /**
     * 依据传入的Path去更新gif预览区域的内容
     * 只传入一个Path,只是查看某一个资源，而不是预览gif，所以需要外部先停止预览区域的播放，如未关闭，确保关闭。
     *
     * @param path 资源Path
     */
    internal fun updatePreviewResDataFromPath(path: Path) {
        // 如未关闭预览区域gif的播放，则要确保关闭
        if (previewIsPlaying) {
            updatePreviewPlayState(false, fromUser = true)
        }

        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(path)
        mediaItem ?: run {
            GLog.e(TAG, "[updatePreviewResDataFromPath] mediaItem is null!")
            return
        }
        requestDecodeAllSelectionPhoto(
            CoroutineParameter(
                coroutineName = UPDATE_PREVIEW_RES_DATA_FROM_PATH,
                coroutineDispatcher = Dispatchers.CPU
            ),
            shouldShowDialog = false,
            onExecute = {
                withTimeoutOrNull(TIME_10_SEC_IN_MS.toLong()) {
                    startLoadingResource(mediaItem, GifContentSizeType.PreviewThumb, GifContentType.PreviewThumbnail)
                }
            },
            onSuccess = {
                GLog.e(TAG, "updatePreviewResDataFromPath, onSuccess")
                notifyPreviewContentChanged(path)
            },
            onError = { GLog.e(TAG) { "[updatePreviewResDataFromPath] requestDecodeAllSelectionPhoto error !!" } }
        )
    }

    /**
     * 依据传入的timeMs去更新gif预览区域的内容
     * 只传入一个timeMs,只是查看某一个资源，而不是预览gif，所以需要外部先停止预览区域的播放，如未关闭，确保关闭。
     *
     * @param timeMs 时间戳
     */
    internal fun updatePreviewResDataFromTimeMs(timeMs: Long) {
        // 如未关闭预览区域gif的播放，则要确保关闭
        if (previewIsPlaying) {
            updatePreviewPlayState(false, fromUser = true)
        }

        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(gifSynthesisData?.itemPath))
        mediaItem ?: run {
            GLog.e(TAG, "[updatePreviewResDataFromPath] mediaItem is null!")
            return
        }
        requestDecodeAllSelectionPhoto(
            CoroutineParameter(
                coroutineName = UPDATE_PREVIEW_RES_DATA_FROM_PATH,
                coroutineDispatcher = Dispatchers.CPU
            ),
            shouldShowDialog = false,
            onExecute = {
                withTimeoutOrNull(TIME_10_SEC_IN_MS.toLong()) {
                    startLoadingBitmap(mediaItem, timeMs, GifContentSizeType.PreviewThumb, GifContentType.PreviewThumbnail, oliveThumbnailParser)
                }
            },
            onSuccess = {
                GLog.e(TAG, "updatePreviewResDataFromPath, onSuccess")
                notifyPreviewContentChanged(timeMs)
            },
            onError = { GLog.e(TAG) { "[updatePreviewResDataFromPath] requestDecodeAllSelectionPhoto error !!" } }
        )
    }

    /**
     * 设置预览视图播放顺序
     * 更新之后，预览区的资源播放顺序将会按照[order]设置的顺序播放
     *
     * @param order 播放顺序 [mode]=[OperateMode.CANCEL]时可以随便传值，内部会还原为上次应用的值
     */
    internal fun updatePreviewPlayOrder(mode: OperateMode, order: PlayOrder) {
        previewOperator.updatePlayOrder(mode, order) {
            this.previewPlayOrder = it
        }
    }

    /**
     * 设置预览视图播放状态
     * 更新之后，预览区域的资源播放状态将依据[play]的值更新为播放/暂停
     *
     * @param play true:播放， false:暂停
     * @param fromUser 是否是在预览界面通过用户手动操作导致的播放暂停，默认false。如进入添加照片页面需要暂停播放、取消或确认时需要继续播放 等属于true
     */
    internal fun updatePreviewPlayState(play: Boolean, fromUser: Boolean = false) {
        previewOperator.updatePlayState(play) {
            this.previewIsPlaying = it
            if (fromUser) {
                shouldAutoUpdatePlayStateWhenLifecycleChanged = it
            }
        }
    }

    /**
     * 依据传入的gifSynthesisData去加载gif合成的资源，
     * 该方法将会依据传入的data中的跳转类型来决定加载哪种资源;
     * @param mode 操作模式, EDIT:编辑模式， APPLY:应用 CANCEL:取消
     * @param gifSynthesisData 加载gif合成的数据源
     * @param completableDeferred 用来标识一个任务是否完成，并唤醒等待的任务
     * */
    internal fun loadBitmapData(
        mode: OperateMode,
        gifSynthesisData: GifSynthesisDataManager.GifSynthesisData,
        completableDeferred: CompletableDeferred<Boolean>? = null,
        resultCallback: ((Boolean) -> Unit)
    ) {
        this.gifSynthesisData = gifSynthesisData
        when (gifSynthesisData.jumpType) {
            JUMP_FORM_CSHOT -> {
                updateBitmapFromMediaSet(mode, gifSynthesisData.originSetPath, gifSynthesisData.filterCShotPath, resultCallback = resultCallback)
            }
            JUMP_FORM_MULTIPLE_SELECTION -> updateBitmapFromPaths(mode, gifSynthesisData.picturePathList, resultCallback = resultCallback)
            JUMP_FORM_OLIVE -> {
                val mediaItem = Path.fromString(gifSynthesisData.itemPath).`object` as? MediaItem
                mediaItem ?: run {
                    GLog.e(TAG, LogFlag.DF) { "updateBitmapFromOlive mediaItem is null!" }
                    return
                }
                viewModelScope.launch(Dispatchers.IO) {
                    updateBitmapFromOlive(
                        mode,
                        loadOliveThumbnailDataList(mediaItem),
                        completableDeferred = completableDeferred,
                        resultCallback = resultCallback
                    )
                }
            }

            else -> GLog.w(TAG, "[loadBitmapData] invalid jumpType, no need loading gif synthesis data!")
        }
    }

    internal fun updateBitmapFromOlive(
        mode: OperateMode,
        oliveThumbnailDataList: List<OliveThumbnailData>,
        playStartIndex: Int? = 0,
        completableDeferred: CompletableDeferred<Boolean>? = null,
        resultCallback: ((Boolean) -> Unit)? = null
    ): Job {
        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(gifSynthesisData?.itemPath))
        mediaItem ?: run {
            GLog.e(TAG, LogFlag.DF) { "updateBitmapFromOlive mediaItem is null!" }
            resultCallback?.invoke(false)
            return Job().apply { cancel() }
        }
        gifSynthesisData?.oliveThumbnailDataList = oliveThumbnailDataList
        val sizeType = GifContentSizeType.PreviewThumb
        return viewModelScope.launch(Dispatchers.CPU) {
            val time = System.currentTimeMillis()
            oliveThumbnailDataList.map { oliveThumbnailData ->
                val completeDeferred = CompletableDeferred<Boolean>()
                async {
                    if (oliveThumbnailData.path.toString().isNotEmpty()) {
                        val selectMediaItem = LocalMediaDataHelper.getLocalMediaItem(oliveThumbnailData.path, true)
                        selectMediaItem ?: run {
                            GLog.e(TAG, "[updateBitmapFromOlive] selectMediaItem is null!")
                            resultCallback?.invoke(false)
                            return@async
                        }
                        // 自定义选图需要使用path解码，此处最终调用 decodeOriginal 输出 P3（SDR） 图片
                        startLoadingResource(selectMediaItem, GifContentSizeType.PreviewThumb, GifContentType.PreviewThumbnail)
                    } else if (oliveThumbnailData.timeMs >= 0) {
                        // 视频抽帧需要使用时间戳解码, 该方法最终调用 OliveVideoEngine::grabThumbnailAsync 输出SRGB（SDR）图片
                        startLoadingBitmap(
                            mediaItem,
                            oliveThumbnailData.timeMs,
                            sizeType,
                            GifContentType.PreviewThumbnail,
                            oliveThumbnailParser,
                            completeDeferred
                        )
                    }
                }
                // 由于美摄的抽帧接口不支持并发，所以供预览用的大缩图抽帧解码任务，需要等待前一个完成后再执行，否刚会有tombstone问题
                if (oliveThumbnailData.timeMs >= 0) completeDeferred.await()
            }
            GLog.w(TAG) {
                "[updateBitmapFromOlive] cost time = ${GLog.getTime(time)}, oliveThumbnailDataList = ${oliveThumbnailDataList.size}"
            }
            completableDeferred?.complete(true)
            updatePreviewResDataForOlive(mode, resource = oliveThumbnailDataList, playStartIndex, resultCallback)
        }
    }

    /**
     * 解码原始
     * @param mode 操作模式, EDIT:编辑模式， APPLY:应用 CANCEL:取消
     * @param originSetPath
     * @param filterCShotPath 用于过滤哪些连拍图片需要展示
     * @param playStartIndex 播放从哪个资源开始播放, 仅当[mode]为[OperateMode.APPLY]时有效
     * @param resultCallback 结果回调
     * */
    private fun updateBitmapFromMediaSet(
        mode: OperateMode,
        originSetPath: String,
        filterCShotPath: MutableList<Path>,
        playStartIndex: Int? = 0,
        resultCallback: ((Boolean) -> Unit)? = null
    ) {
        val sizeType = GifContentSizeType.PreviewThumb
        val resourceList = ArrayList<ResourceKey>()
        viewModelScope.launch(Dispatchers.CPU) {

            val mediaItemList = async {
                loadMediaItemList(originSetPath)
            }
            mediaItemList.await()?.apply {
                /**
                 * 首次进入连拍GIF合成页需要给filterCShotPath赋值，filterCShotPath会在选择图片勾选中更新
                 */
                if (filterCShotPath.isEmpty()) {
                    filterCShotPath.addAll(this.map { it.path })
                }

                val deferredTasks = this.map {
                    async {
                        startLoadingResource(it, sizeType, GifContentType.PreviewThumbnail)
                    }
                }
                deferredTasks.all {
                    it.await() != null
                }
                deferredTasks.forEachIndexed { index, deferred ->
                    try {
                        withTimeout(TIME_10_SEC_IN_MS.toLong()) {
                            deferred.await()?.apply {
                                resourceList.add(this)
                            }
                        }
                    } catch (ex: TimeoutCancellationException) {
                        GLog.d(TAG, "[updateBitmapFromPaths] TimeoutCancellationException $index")
                    }
                }
                updatePreviewResData(mode, filterCShotPath, playStartIndex, resultCallback)
            }
        }
    }

    /**
     * 依据传入的Path列表去更新gif合成的资源。
     * 该方法将会依据Path解码缩图，拿到缩图Bitmap后交由[IGifSynthesisPreviewOperator.updateResData]处理并驱动更新显示内容
     *
     * @param mode 操作模式, EDIT:编辑模式， APPLY:应用 CANCEL:取消
     * @param paths 资源PathList
     * @param playStartIndex 播放从哪个资源开始播放, 仅当[mode]为[OperateMode.APPLY]时有效
     * @param resultCallback 结果回调
     */
    internal fun updateBitmapFromPaths(
        mode: OperateMode,
        paths: List<Path>,
        playStartIndex: Int? = 0,
        resultCallback: ((Boolean) -> Unit)? = null
    ): Job {
        val resourceList = ArrayList<ResourceKey>()
        val time = System.currentTimeMillis()
        return viewModelScope.launch(Dispatchers.CPU) {
            val deferredTasks = paths.mapNotNull { path ->
                val mediaItem = LocalMediaDataHelper.getLocalMediaItem(path, true)
                mediaItem ?: run {
                    GLog.e(TAG, "[updateBitmapFromPaths] mediaItem is null!")
                    resultCallback?.invoke(false)
                    return@mapNotNull null
                }
                async {
                    startLoadingResource(mediaItem, GifContentSizeType.PreviewThumb, GifContentType.PreviewThumbnail)
                }
            }
            deferredTasks.all {
                it.await() != null
            }
            deferredTasks.forEachIndexed { index, deferred ->
                try {
                    withTimeout(TIME_10_SEC_IN_MS.toLong()) {
                        deferred.await()?.apply {
                            resourceList.add(this)
                        }
                    }
                } catch (ex: TimeoutCancellationException) {
                    GLog.d(TAG, "[updateBitmapFromPaths] TimeoutCancellationException $index")
                }
            }
            GLog.d(TAG) {
                "[updateBitmapFromPaths] cost time = ${GLog.getTime(time)}, resourceListSize = ${resourceList.size}"
            }
            updatePreviewResData(mode, paths, playStartIndex, resultCallback)
        }
    }

    /**
     * 解析单个MediaItem图片
     * @param mediaItem 待解析的文件
     * @param sizeType 要加载图片的大小
     * @param contentType 要加载图片的类型
     * 会回调创建好的resourceKey，利用这个解Bitmap
     * */
    private suspend fun startLoadingResource(
        mediaItem: MediaItem,
        sizeType: GifContentSizeType,
        contentType: GifContentType
    ) = suspendCancellableCoroutine<ResourceKey?> {
        contentFactory.startLoadResource(mediaItem, sizeType.loadType, contentType) { key ->
            it.resume(key)
        }
    }

    /**
     * 解析单个时间戳图片
     * @param mediaItem 待解析的olive图对应的MediaItem
     * @param timeMs 视频帧对应的时间戳
     * @param sizeType 要加载图片的大小
     * @param contentType 要加载图片的类型
     */
    private suspend fun startLoadingBitmap(
        mediaItem: MediaItem,
        timeMs: Long,
        sizeType: GifContentSizeType,
        contentType: GifContentType,
        oliveThumbnailParser: OliveThumbnailParser?,
        completableDeferred: CompletableDeferred<Boolean>? = null
    ) = suspendCancellableCoroutine<Bitmap?> {
        contentFactory.startLoadBitmap(mediaItem, timeMs, sizeType.loadType, contentType, oliveThumbnailParser) { key ->
            completableDeferred?.complete(true)
            it.resume(key)
        }
    }

    /**
     * 依据Path去通知预览内容变更了
     *
     * @param path 待解析的Path
     *
     * 依据[path]去获取mediaItem,再依据mediaItem从磁盘缓存中获取bitmap
     * 拿到bitmap后回调给用于显示内容的ImageView去显示
     * 之后再回收上次用于预览显示的bitmap对象
     */
    private fun notifyPreviewContentChanged(path: Path) {
        launch(Dispatchers.IO) {
            getResourceFromDisCache(path)?.let { bitmap ->
                withContext(Dispatchers.Main) {
                    _previewContentResource.value = bitmap
                    lastPreviewContent = bitmap
                }
            }
        }
    }

    /**
     * 依据Bitmap去通知预览内容变更了
     *
     * @param timeMs 已经得到的视频帧时间戳
     *
     * 拿到bitmap后回调给用于显示内容的ImageView去显示
     * 之后再回收上次用于预览显示的bitmap对象
     */
    private fun notifyPreviewContentChanged(timeMs: Long) {
        launch(Dispatchers.IO) {
            val bitmap = getBitmapResourceFromDisCache(timeMs)
            bitmap?.let { bitmap ->
                withContext(Dispatchers.Main) {
                    _previewContentResource.value = bitmap
                    lastPreviewContent = bitmap
                }
            }
        }
    }

    /**
     * 依据Path去解析
     * @param path 待解析的Path
     * 会回调创建好的bitmap
     * */
    private suspend fun getResourceFromDisCache(path: Path): Bitmap? {
        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(path)
        mediaItem ?: run {
            GLog.e(TAG, "[getResourceFromDisCache] mediaItem is null!")
            return null
        }
        return getResourceFromDisCache(mediaItem)
    }

    private suspend fun initOliveThumbnailParser(mediaItem: MediaItem, deferred: CompletableDeferred<Boolean>) {
        val oLiveDecode = OLiveDecode.create(mediaItem.filePath)
        val oLivePhoto: OLivePhoto? = oLiveDecode.decode()
        oLivePhoto?.let { oLivePhoto ->
            oLivePhoto.microVideo?.let { initVideoInfo(it) }
            withContext(Dispatchers.MeicamEdit) {
                tryInitOliveVideoEngine(mediaItem.contentUri, oLivePhoto)
            }
            oliveThumbnailParser = OliveThumbnailParser(
                mediaItem.contentUri,
                mediaItem.filePath,
                oLivePhoto.microVideo?.offset ?: 0L,
                oLivePhoto.microVideo?.length ?: 0L,
                oliveVideoEngine
            )
        }
        deferred.complete(true)
    }

    private fun tryInitOliveVideoEngine(sourceUri: Uri, olivePhoto: OLivePhoto) {
        val initEngineTimeCost = measureTimeMillis {
            val oliveVideoEngine = OliveVideoEngine(context)

            val microVideo = olivePhoto.microVideo.getOrLog(TAG, "microVideo") ?: return

            oliveVideoEngine.initFromVideo(
                sourceUri,
                VideoSourceDescriptor.MIX(offset = microVideo.offset, length = microVideo.length),
                OliveTransformConfig(
                    getHdrTransformData(sourceUri),
                    needFXTransformToSdr = true,
                    forceSdr = true
                )
            )?.also { initError ->
                    GLog.e(TAG, LogFlag.DF, "[tryInitOliveVideoEngine] init engine failed: $initError")
                    return
                }

            removeOliveVideoFuzzyEdge(oliveVideoEngine)

            this.oliveVideoEngine = oliveVideoEngine
        }
        GLog.d(TAG, "[tryInitOliveVideoEngine] init engine cost: $initEngineTimeCost")
    }

    /**
     * 对有模糊边缘的视频需要去掉模糊边缘，
     * 通过对视频进行放大来去掉四周的模糊边缘。
     *
     * @param oliveVideoEngine Olive视频编辑器
     */
    private fun removeOliveVideoFuzzyEdge(oliveVideoEngine: IOliveVideoEngine) {
        val uri = oliveVideoEngine.sourceUri.getOrLog(TAG, "[removeOliveVideoFuzzyEdge] sourceUri") ?: return
        val descriptor = oliveVideoEngine.sourceDescriptor.getOrLog(TAG, "[removeOliveVideoFuzzyEdge] sourceDescriptor") ?: return
        val offset = (descriptor as? VideoSourceDescriptor.MIX)?.offset ?: AppConstants.Number.NUMBER_MINUS_1.toLong()

        val eisInfo = EffectUtil.getEisInfo(uri = uri, offset = offset)
        val scale = eisInfo?.eisCropFactor
        if (scale != null) {
            val displayPercent = if (scale[0] > 0) scale[0] else DEFAULT_DISPLAY_PERCENT
            //使用放大的方式去模糊边缘，带水印视频放大视频内容，不带水印放大整个视频
            val videoDisplayRect = context.getAppAbility<IWatermarkMasterAbility>()?.newWatermarkFileOperator(uri)?.use {
                it.getAiMasterWatermarkExtInfo()?.videoDisplayRect
            }
            if (DEFAULT_DISPLAY_PERCENT.compareTo(displayPercent) < 0) {
                oliveVideoEngine.getVideoEdgeEditor()?.scaleVideoContent(videoDisplayRect, scale[0])
            }
        }
    }

    /**
     * 获取影像的下变换参数
     *
     * @param sourceUri olive文件的uri
     *
     * @return 返回从文件获取到的下变换参数，如果文件不是支持影像拍摄的支持下变换效果的照片，则没有这种参数数据
     */
    private fun getHdrTransformData(sourceUri: Uri): HdrTransformData? {
        return FileExtendedContainer().use {
            it.setDataSource(context, sourceUri)
            it.getExtensionData(EXTEND_KEY_HDR_TRANSFORM_DATA)?.run {
                HdrTransformDataStruct(this).toData()
            }
        }
    }

    private fun isNeedPartClip(): Boolean {
        return videoInfo?.let { it.videoStartUs < it.videoEndUs } ?: false
    }

    private fun initVideoInfo(microVideo: MicroVideo) {
        microVideo.let {
            val offset = it.offset
            val length = it.length
            val videoStartUs = it.videoStartUs ?: 0L
            val videoEndUs = it.videoEndUs ?: 0L
            videoInfo = VideoInfo(offset, length, videoStartUs, videoEndUs)
        }
    }

    private fun createTempFileForExportOliveVideo(olivePhoto: OLivePhoto, mediaItem: MediaItem): File {
        val fType: String? = olivePhoto.microVideo?.mime?.let(MimeTypeUtils::getSuffixByMimeType)
        val targetName = "${mediaItem.dateModifiedInSec}_${mediaItem.mediaId}${TextUtil.DOT}$fType"

        val parentDir = File(context.externalCacheDir ?: context.cacheDir, OLIVE_PHOTO_TMP_DIR)
        if (!parentDir.exists()) parentDir.mkdirs()
        val tmpFile = File(parentDir, targetName)
        val isTmpFileExist = tmpFile.exists()
        if (isTmpFileExist) return tmpFile // 如果文件存在，直接使用该缓存文件
        tmpFile.createNewFile()
        return tmpFile
    }

    private fun exportTempOliveVideoForOriginal(decoder: OLiveDecode, tmpFile: File): File? {
        val openFileRequest = OpenFileRequest.Builder().apply {
            setModeType(FileConstants.FileModeType.MODE_WRITE)
            setFile(tmpFile.absolutePath)
        }.builder()
        FileAccessManager.getInstance().getOutStream(context, openFileRequest)?.use {
            val isSuccess = decoder.exportVideo(it)
            GLog.w(TAG, "exportTempOliveVideo: isSuccess =$isSuccess")
            if (isSuccess) return tmpFile
        }
        GLog.w(TAG, "exportTempOliveVideo: exportVideo() failed.")
        return null
    }


    /**
     * 依据时间戳timeMs去解析
     * @param timeMs 待解析的视频帧时间戳
     * 会回调创建好的bitmap
     * */
    private suspend fun getBitmapResourceFromDisCache(timeMs: Long): Bitmap? {
        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(gifSynthesisData?.itemPath))
        mediaItem ?: run {
            GLog.e(TAG, "[getBitmapResourceFromDisCache] mediaItem is null!")
            return null
        }
        return oliveThumbnailParser?.getThumbnailBitmap(mediaItem, timeMs, ThumbnailSizeUtils.TYPE_LARGE_SHORTEST_SIZE_1280) ?: let {
            GLog.e(TAG) { "[getBitmapResourceFromDisCache] failed to get bitmap." }
            null
        }
    }

    /**
     * 提供非挂起的获取单张磁盘缓存bitmap,依据时间戳timeMs去解析
     * @param timeMs 待解析的视频帧时间戳
     * 会回调创建好的bitmap
     * */
    internal fun getBitmapResFromDisCache(timeMs: Long): Bitmap? {
        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(gifSynthesisData?.itemPath))
        mediaItem ?: run {
            GLog.e(TAG, "[getResourceFromDisCache] mediaItem is null!")
            return null
        }
        var bitmap: Bitmap?
        runBlocking {
            bitmap = oliveThumbnailParser?.getThumbnailBitmap(mediaItem, timeMs, ThumbnailSizeUtils.TYPE_LARGE_SHORTEST_SIZE_1280) ?: let {
                GLog.e(TAG) { "[getBitmapResFromDisCache] failed to get bitmap." }
                null
            }
        }
        return bitmap
    }

    /**
     * 解析单个MediaItem图片
     * @param mediaItem 待解析的文件
     * 会回调创建好的bitmap
     * */
    private suspend fun getResourceFromDisCache(
        mediaItem: MediaItem
    ) = suspendCancellableCoroutine<Bitmap?> {
        val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem.toOriginalItem())
        val options = ResourceGetOptions(
            inThumbnailType = ThumbnailSizeUtils.TYPE_LARGE_SHORTEST_SIZE_1280,
            inCacheOperation = CacheOperation.ReadWriteDiskCache,
            inCropParams = CropParams.noCrop(),
            inStorageQuality = StorageQuality.NOT_CARE
        )
        resourceKey?.let { key ->
            contentFactory.getResource(key, options)?.apply {
                val rotationBitmap = BitmapUtils.rotateBitmap(this, mediaItem.rotation, true)
                it.resume(rotationBitmap)
            }
        } ?: run {
            GLog.e(TAG, "[getResourceFromDisCache] get resource failed!")
            it.resume(null)
        }
    }

    /**
     * 提供非挂载的获取单张磁盘缓存bitmap
     * */
    internal fun getResFromDiskCache(mediaItem: MediaItem): Bitmap? {
        val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem.toOriginalItem())
        val options = ResourceGetOptions(
            inThumbnailType = ThumbnailSizeUtils.TYPE_LARGE_SHORTEST_SIZE_1280,
            inCacheOperation = CacheOperation.ReadWriteDiskCache,
            inCropParams = CropParams.noCrop(),
            inStorageQuality = StorageQuality.NOT_CARE
        )
        return resourceKey?.let { key ->
            val tempBitmap = contentFactory.getResource(key, options)
            BitmapUtils.rotateBitmap(tempBitmap, mediaItem.rotation, true)
        } ?: run {
            GLog.e(TAG, "[getResFromDiskCache] get resource failed!")
            null
        }
    }

    private suspend fun loadOliveThumbnailDataList(mediaItem: MediaItem): List<OliveThumbnailData> {
        val oliveThumbnailDataList = mutableListOf<OliveThumbnailData>()
        runCatching {
            val deferred = CompletableDeferred<Boolean>()
            initOliveThumbnailParser(mediaItem, deferred)
            deferred.await()
            val oliveVideoEngine = oliveVideoEngine ?: run {
                return oliveThumbnailDataList
            }
            val startMs = TimeUnit.MICROSECONDS.toMillis(videoInfo?.videoStartUs ?: 0L)
            var duration: Long = 0L
            val endMs: Long
            if (isNeedPartClip()) {
                duration = TimeUnit.MICROSECONDS.toMillis(videoInfo?.let { (it.videoEndUs - it.videoStartUs) } ?: 0L)
                endMs = TimeUnit.MICROSECONDS.toMillis(videoInfo?.videoEndUs ?: 0L)
            } else {
                IMediaMetadataRetriever.create(IMediaMetadataRetriever.MediaMetadataRetrieverVendor.TBL).safeUse { retriever ->
                    FileAccessManager.getInstance().openFile(ContextGetter.context, oliveVideoEngine.sourceUri)?.safeUse { pfd ->
                        val descriptor = oliveVideoEngine.sourceDescriptor
                        if (descriptor is VideoSourceDescriptor.MIX) {
                            retriever.setDataSource(pfd.fileDescriptor, descriptor.offset, descriptor.length)
                        } else {
                            retriever.setDataSource(pfd.fileDescriptor)
                        }
                        duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLong() ?: 0L
                    }.getOrLog(TAG, "[loadOliveThumbnailDataList] openFile")
                }
                endMs = duration
            }
            val frameCount = duration / FRAME_INTERVAL
            // 这里应该使用闭区间[]，而不是半开闭区间[)，因为frameCount得到的是倍数，那么段数就应该是frameCount+1，所以取帧的时候应该要能取到frameCount
            for (i in 0..frameCount) {
                val timeMs = startMs + (i * FRAME_INTERVAL)
                oliveThumbnailDataList.add(OliveThumbnailData(timeMs = timeMs))
            }
            if ((duration % FRAME_INTERVAL) > 0L) {
                oliveThumbnailDataList.add(OliveThumbnailData(timeMs = endMs))
            }
            GLog.d(TAG, LogFlag.DF) { "loadOliveThumbnailDataList, duration=$duration,frame size=${oliveThumbnailDataList.size}" }
        }.onFailure {
            GLog.e(TAG) { "<loadOliveThumbnailDataList> got error: ${it.message}" }
        }
        return oliveThumbnailDataList
    }

    private suspend fun loadMediaItemList(
        originSetPath: String
    ) = suspendCoroutine<List<MediaItem>?> {
        val setPath = Path.fromString(originSetPath)
        val mediaSet = DataManager.getMediaSet(setPath)
        val mediaItemList: ArrayList<MediaItem> = ArrayList()
        mediaSet?.apply {
            /**
             * 产品需求：如果连拍照片数量超过50张，就选取前50张；
             * */
            val itemCount = count.coerceAtMost(SYNTHESIS_COUNT_THRESHOLD_MAX)
            val items = getSubMediaItem(0, itemCount)
            items.forEach {
                mediaItemList.add(it)
            }
            it.resume(mediaItemList)
        } ?: run {
            GLog.e(TAG, "[loadMediaItemList] load mediaSet failed!")
            it.resume(null)
        }
    }

    /**
     * gif响应合成
     * @param size 当前选择的分辨率的 size
     * */
    suspend fun gifSynthesis(size: Size) {
        _previewResData.value?.let { listPath ->
            gifSynthesisStatusFlowData(listPath, size)
                .flowOn(Dispatchers.CPU)
                .collect {
                    _gifSynthesisStatus.value = it
                }
        }

        _previewForOliveResData.value?.let { listPath ->
            gifSynthesisStatusForOliveFlowData(listPath, size)
                .flowOn(Dispatchers.CPU)
                .collect {
                    _gifSynthesisStatus.value = it
                }
        }
    }

    /**
     * 取消gif合成,需要放在io中，有删除文件操作
     */
    fun cancelGifSynthesis() {
        //如果成功了不响应取消，极端边界情况，成功和取消动作毫秒级差距
        if (gifSynthesisCompletedFlag) return

        updatePreviewPlayState(play = true, fromUser = true)
        cancelGifSynthesisFlag = true
        updateSavingDefinitionType(STANDARD_DEFINITION)
        viewModelScope.launch(Dispatchers.IO) {
            gifConvertEngine?.cancelGifSynthesis()
        }
        getDiskCacheBitmapDisposeJob?.apply {
            cancel()
            null
        }
        convertBitmapListToGifJob?.apply {
            cancel()
            null
        }
    }

    /**
     * 根据用户所选顺序，获取最终需要处理的listPath集合
     * @param listPath 处理前的listPath
     * @return List<Path> 最终需要处理的pathList集合
     */
    private fun getGifSynthesisPathListByOrder(listPath: List<Path>): List<Path> {
        return when (previewPlayOrder) {
            PlayOrder.ORDER_POSITIVE -> listPath
            PlayOrder.ORDER_REVERSE -> listPath.ktReversed()
            PlayOrder.ORDER_FETCH -> {
                arrayListOf<Path>().apply {
                    addAll(listPath)
                    if (listPath.size > SYNTHESIS_COUNT_THRESHOLD_MIN) {
                        addAll(listPath.subList(1, listPath.size - 1).ktReversed())
                    }
                }
            }
        }
    }

    /**
     * 根据用户所选顺序，获取最终需要处理的OliveThumbnailData集合
     * @param oliveThumbnailDataList 处理前的OliveThumbnailDataList
     * @return List<OliveThumbnailData> 最终需要处理的OliveThumbnailDataList集合
     */
    private fun getGifSynthesisPathListForOliveByOrder(oliveThumbnailDataList: List<OliveThumbnailData>): List<OliveThumbnailData> {
        return when (previewPlayOrder) {
            PlayOrder.ORDER_POSITIVE -> oliveThumbnailDataList
            PlayOrder.ORDER_REVERSE -> oliveThumbnailDataList.ktReversed()
            PlayOrder.ORDER_FETCH -> {
                arrayListOf<OliveThumbnailData>().apply {
                    addAll(oliveThumbnailDataList)
                    if (oliveThumbnailDataList.size > SYNTHESIS_COUNT_THRESHOLD_MIN) {
                        addAll(oliveThumbnailDataList.subList(1, oliveThumbnailDataList.size - 1).ktReversed())
                    }
                }
            }
        }
    }

    /**
     * 获取磁盘缓存图片进行解码，同时按照符合GIF合成规格对bitmap填边裁剪缩放等操作，送入合成缓冲区
     * @param gifSynthesisPathList 最终待处理的pathList
     * @param viewPortRectF 原视口
     * @param clipViewPortRectF 裁剪视口，待渲染缩放视口
     * @param size 最终处理后的bitmap尺寸
     * @param gifSynthesisListener 合成回调
     */
    private fun getDiskCacheBitmapDispose(
        gifSynthesisPathList: List<Path>,
        viewPortRectF: RectF,
        clipViewPortRectF: RectF,
        size: Size,
        gifSynthesisListener: GifSynthesisListener
    ) {
        gifSynthesisPathList.forEach {
            if (cancelGifSynthesisFlag) return
            /**
             * it.`object` as? MediaItem 存在弱引用回收问题
             */
            LocalMediaDataHelper.getLocalMediaItem(it)?.let { mediaItem ->
                val cacheBitmap = getResFromDiskCache(mediaItem)
                cacheBitmap?.let {
                    val disposedBitmap =
                        GifSynthesisUtils.singleBitmapGifSynthesisDispose(cacheBitmap, viewPortRectF, clipViewPortRectF, size)
                    cacheBitmap.recycle()
                    disposedBitmap?.let {
                        //给缓冲区投喂数据
                        gifConvertEngine?.feedBitmapIntoBuffer(disposedBitmap)
                    } ?: kotlin.run {
                        GLog.d(TAG) { "[getDiskCacheBitmapDispose] disposedBitmap is null" }
                        gifSynthesisListener.onGifSynthesisFailed(SAVE_FAILED, EMPTY_STRING)
                        return
                    }
                } ?: kotlin.run {
                    GLog.d(TAG) { "[getDiskCacheBitmapDispose] cacheBitmap is null" }
                    gifSynthesisListener.onGifSynthesisFailed(SAVE_FAILED, EMPTY_STRING)
                    return
                }
            } ?: kotlin.run {
                GLog.d(TAG) { "[getDiskCacheBitmapDispose] mediaItem is null" }
                gifSynthesisListener.onGifSynthesisFailed(ENCODING_FAILED, EMPTY_STRING)
                return
            }
        }
    }

    /**
     * 获取磁盘缓存图片进行解码，同时按照符合GIF合成规格对bitmap填边裁剪缩放等操作，送入合成缓冲区
     * @param gifSynthesisOliveThumbnailDataList 最终待处理的OliveThumbnailDataList
     * @param viewPortRectF 原视口
     * @param clipViewPortRectF 裁剪视口，待渲染缩放视口
     * @param size 最终处理后的bitmap尺寸
     * @param gifSynthesisListener 合成回调
     */
    private fun getDiskCacheBitmapForOliveDispose(
        gifSynthesisOliveThumbnailDataList: List<OliveThumbnailData>,
        viewPortRectF: RectF,
        clipViewPortRectF: RectF,
        size: Size,
        gifSynthesisListener: GifSynthesisListener
    ) {
        gifSynthesisOliveThumbnailDataList.forEach { oliveThumbnailData ->
            if (cancelGifSynthesisFlag) return
            val cacheBitmap = getCacheBitmapResForOlive(oliveThumbnailData)
            cacheBitmap?.let {
                val disposedBitmap =
                    GifSynthesisUtils.singleBitmapGifSynthesisDispose(cacheBitmap, viewPortRectF, clipViewPortRectF, size)
                if (cacheBitmap.isRecycled.not()) cacheBitmap.recycle()
                disposedBitmap?.let {
                    //给缓冲区投喂数据
                    gifConvertEngine?.feedBitmapIntoBuffer(disposedBitmap)
                } ?: kotlin.run {
                    GLog.d(TAG) { "[getDiskCacheBitmapForOliveDispose] disposedBitmap is null" }
                    gifSynthesisListener.onGifSynthesisFailed(SAVE_FAILED, EMPTY_STRING)
                    return
                }
            }
        }
    }

    /**
     * 合成入口调用
     * @param oliveThumbnailDataList 处理前的OliveThumbnailData集合
     * @param size 最终处理后的bitmap尺寸
     * @return Flow<GifSynthesisStatus> 合成状态流
     */
    private fun gifSynthesisStatusForOliveFlowData(oliveThumbnailDataList: List<OliveThumbnailData>, size: Size): Flow<GifSynthesisStatus> {
        return callbackFlow<GifSynthesisStatus> {
            cancelGifSynthesisFlag = false
            gifSynthesisCompletedFlag = false
            trySendBlocking(GifSynthesisStatus.GifSynthesisStart)
            val firstBitmap = getCacheBitmapResForOlive(oliveThumbnailDataList.first())
            firstBitmap?.let {
                /**
                 * 1.根据播放顺序处理listPath,获取预处理得gifSynthesisPathList
                 * 2.viewPortRectF 原视口, clipViewPortRectF 裁剪视口，待渲染缩放视口
                 */
                val gifSynthesisOliveThumbnailDataList = getGifSynthesisPathListForOliveByOrder(oliveThumbnailDataList)

                val viewPortRectF = RectF(0f, 0f, firstBitmap.width.toFloat(), firstBitmap.height.toFloat())
                val clipViewPortRectF: RectF = if (previewDisplayRatio == ClipRatio.RATIO_ORIGINAL) {
                    viewPortRectF
                } else {
                    viewPortRectF.getRectFWithContainerByRatio(previewDisplayRatio.value)
                }
                val frameInterval = 1f / previewPlayIntervalInSecond
                gifSynthesisForOliveExecute(
                    gifSynthesisOliveThumbnailDataList,
                    frameInterval,
                    viewPortRectF,
                    clipViewPortRectF,
                    size,
                    object : GifSynthesisListener {
                        override fun onGifSynthesisProgress(progress: Double) {
                            trySendBlocking(GifSynthesisStatus.GifSynthesisProgress(progress))
                        }

                        override fun onGifSynthesisCompleted(
                            filePath: String,
                            fileName: String,
                            costTimeSecond: Float,
                            frameInterval: Float
                        ) {
                            //因为合成和取消是异步的，而成功回调出去展示Toast前有一些耗时操作
                            if (cancelGifSynthesisFlag.not()) {
                                gifSynthesisCompletedFlag = true
                                trySendBlocking(
                                    GifSynthesisStatus.GifSynthesisCompleted(
                                        filePath,
                                        fileName,
                                        costTimeSecond,
                                        frameInterval
                                    )
                                )
                            }
                            channel.close()
                        }

                        override fun onGifSynthesisFailed(errorCode: Int, fileName: String) {
                            trySendBlocking(GifSynthesisStatus.GifSynthesisFailed(errorCode, fileName))
                            channel.close()
                            cancelGifSynthesis()
                        }

                        override fun onGifSynthesisTimeOut() {
                            trySendBlocking(GifSynthesisStatus.GifSynthesisTimeOut)
                            channel.close()
                            cancelGifSynthesis()
                        }
                    })
            }
            awaitClose {
                GLog.d(TAG, "[gifSynthesisStatusFlowData] awaitClose")
            }
        }.conflate()
    }

    /**
     * 合成入口调用
     * @param listPath 处理前的listPath
     * @param size 最终处理后的bitmap尺寸
     * @return Flow<GifSynthesisStatus> 合成状态流
     */
    private fun gifSynthesisStatusFlowData(listPath: List<Path>, size: Size): Flow<GifSynthesisStatus> {
        return callbackFlow<GifSynthesisStatus> {
            cancelGifSynthesisFlag = false
            gifSynthesisCompletedFlag = false
            trySendBlocking(GifSynthesisStatus.GifSynthesisStart)
            val mediaItem = LocalMediaDataHelper.getLocalMediaItem(listPath.first())
            mediaItem?.let {
                val firstBitmap = getResFromDiskCache(it)
                firstBitmap?.let {
                    /**
                     * 1.根据播放顺序处理listPath,获取预处理得gifSynthesisPathList
                     * 2.viewPortRectF 原视口, clipViewPortRectF 裁剪视口，待渲染缩放视口
                     */
                    val gifSynthesisPathList = getGifSynthesisPathListByOrder(listPath)

                    val viewPortRectF = RectF(0f, 0f, firstBitmap.width.toFloat(), firstBitmap.height.toFloat())
                    val clipViewPortRectF: RectF = if (previewDisplayRatio == ClipRatio.RATIO_ORIGINAL) {
                        viewPortRectF
                    } else {
                        viewPortRectF.getRectFWithContainerByRatio(previewDisplayRatio.value)
                    }
                    val frameInterval = 1f / previewPlayIntervalInSecond
                    gifSynthesisExecute(
                        gifSynthesisPathList,
                        frameInterval,
                        viewPortRectF,
                        clipViewPortRectF,
                        size,
                        object : GifSynthesisListener {
                            override fun onGifSynthesisProgress(progress: Double) {
                                trySendBlocking(GifSynthesisStatus.GifSynthesisProgress(progress))
                            }

                            override fun onGifSynthesisCompleted(
                                filePath: String,
                                fileName: String,
                                costTimeSecond: Float,
                                frameInterval: Float
                            ) {
                                //因为合成和取消是异步的，而成功回调出去展示Toast前有一些耗时操作
                                if (cancelGifSynthesisFlag.not()) {
                                    gifSynthesisCompletedFlag = true
                                    trySendBlocking(
                                        GifSynthesisStatus.GifSynthesisCompleted(
                                            filePath,
                                            fileName,
                                            costTimeSecond,
                                            frameInterval
                                        )
                                    )
                                }
                                channel.close()
                            }

                            override fun onGifSynthesisFailed(errorCode: Int, fileName: String) {
                                trySendBlocking(GifSynthesisStatus.GifSynthesisFailed(errorCode, fileName))
                                channel.close()
                                cancelGifSynthesis()
                            }

                            override fun onGifSynthesisTimeOut() {
                                trySendBlocking(GifSynthesisStatus.GifSynthesisTimeOut)
                                channel.close()
                                cancelGifSynthesis()
                            }
                        })
                } ?: kotlin.run {
                    GLog.d(TAG) { "[gifSynthesisStatusFlowData] first bitmap is null" }
                    cancelGifSynthesis()
                    trySendBlocking(GifSynthesisStatus.GifSynthesisFailed(SAVE_FAILED, EMPTY_STRING))
                    channel.close()
                }
            } ?: kotlin.run {
                GLog.d(TAG) { "[gifSynthesisStatusFlowData] first mediaItem is null" }
                cancelGifSynthesis()
                trySendBlocking(GifSynthesisStatus.GifSynthesisFailed(SAVE_FAILED, EMPTY_STRING))
                channel.close()
            }
            awaitClose {
                GLog.d(TAG, "[gifSynthesisStatusFlowData] awaitClose")
            }
        }.conflate()
    }

    /**
     * 内部合成调用  frameInterval : gif合成帧间隔  1 / frameInterval 为帧率
     * @param gifSynthesisPathList 最终待处理的pathList
     * @param fps 帧率
     * @param viewPortRectF 原视口
     * @param clipViewPortRectF 裁剪视口，待渲染缩放视口
     * @param size 最终处理后的bitmap尺寸
     * @param gifSynthesisListener 合成回调
     */
    private fun gifSynthesisExecute(
        gifSynthesisPathList: List<Path>,
        fps: Float,
        viewPortRectF: RectF,
        clipViewPortRectF: RectF,
        size: Size,
        gifSynthesisListener: GifSynthesisListener
    ) {
        val filePathStr = GifSynthesisUtils.getGifSynthesisFilePathString()
        filePathStr ?: let {
            // 如果文件路径获取失败，则直接回调合成失败
            gifSynthesisListener.onGifSynthesisFailed(SAVE_FAILED, EMPTY_STRING)
            return
        }

        gifConvertEngine = GifConvertEngine(
            gifSynthesisPathList.size,
            filePathStr,
            fps,
            isEnableDither(saveDefinition),
            gifSynthesisListener
        )

        getDiskCacheBitmapDisposeJob = async {
            getDiskCacheBitmapDispose(gifSynthesisPathList, viewPortRectF, clipViewPortRectF, size, gifSynthesisListener)
        }

        convertBitmapListToGifJob = async {
            gifConvertEngine?.convertBitmapListToGif()
        }
    }

    /**
     * 内部合成调用  frameInterval : gif合成帧间隔  1 / frameInterval 为帧率
     * @param gifSynthesisOliveThumbnailDataList 最终待处理的OliveThumbnailDataList
     * @param fps 帧率
     * @param viewPortRectF 原视口
     * @param clipViewPortRectF 裁剪视口，待渲染缩放视口
     * @param size 最终处理后的bitmap尺寸
     * @param gifSynthesisListener 合成回调
     */
    private fun gifSynthesisForOliveExecute(
        gifSynthesisOliveThumbnailDataList: List<OliveThumbnailData>,
        fps: Float,
        viewPortRectF: RectF,
        clipViewPortRectF: RectF,
        size: Size,
        gifSynthesisListener: GifSynthesisListener
    ) {
        val filePathStr = GifSynthesisUtils.getGifSynthesisFilePathString()
        filePathStr ?: let {
            // 如果文件路径获取失败，则直接回调合成失败
            gifSynthesisListener.onGifSynthesisFailed(SAVE_FAILED, EMPTY_STRING)
            return
        }

        gifConvertEngine = GifConvertEngine(
            gifSynthesisOliveThumbnailDataList.size,
            filePathStr,
            fps,
            isEnableDither(saveDefinition),
            gifSynthesisListener
        )

        getDiskCacheBitmapDisposeJob = async {
            getDiskCacheBitmapForOliveDispose(gifSynthesisOliveThumbnailDataList, viewPortRectF, clipViewPortRectF, size, gifSynthesisListener)
        }

        convertBitmapListToGifJob = async {
            gifConvertEngine?.convertBitmapListToGif()
        }
    }

    /**
     * GI合成时是否需要开Dither，防止背景颜色干扰主体
     * @param type 保存时选择的清晰度类型
     * @return 返回Gif合成SDK是否需要开启Dither
     * */
    private fun isEnableDither(type: String): Boolean {
        return when (type) {
            STANDARD_DEFINITION, HIGH_DEFINITION -> false
            SUPER_DEFINITION -> true
            else -> false
        }
    }

    override fun onResume() {
        super.onResume()
        GLog.d(TAG) { "onResume autoPlay=$shouldAutoUpdatePlayStateWhenLifecycleChanged previewIsPlaying=$previewIsPlaying" }
        if (shouldAutoUpdatePlayStateWhenLifecycleChanged && previewIsPlaying.not()) {
            updatePreviewPlayState(true)
        }
    }

    override fun onPause() {
        super.onPause()
        GLog.d(TAG) { "onPause" }
        updatePreviewPlayState(false)
    }

    override fun onCleared() {
        updatePreviewPlayState(false)
        lastPreviewContent = null
        contentFactory.release()
        previewOperator.clear()
        _previewContentResource.value?.recycle()
        (_previewResData.value as? ArrayList<Path>)?.clear()
        (_previewForOliveResData.value as? ArrayList<*>)?.clear()
        GLog.d(TAG) { "onCleared" }
        super.onCleared()
    }

    /**
     * 为跳转大图预先解码缩图，优化跳转大图显示速度
     * ThumbnailSizeUtils.getFullThumbnailKey() : 大图缩图的key
     * @param uri GIF合成成功后的 图片 uri
     */
    internal fun preDecodeThumbnailForViewGallery(uri: Uri): Bitmap? {
        val intent = Intent()
        intent.setDataAndType(uri, MimeTypeUtils.MIME_TYPE_IMAGE_ANY)
        val path = DataManager.findPathByUri(uri, intent.type) ?: run {
            GLog.d(TAG) { "[preDecodeThumbnailForViewGallery] path is null" }
            return null
        }

        val mediaItem = LocalMediaDataHelper.getLocalMediaItem(path) ?: run {
            GLog.d(TAG) { "[preDecodeThumbnailForViewGallery] mediaItem is null" }
            return null
        }
        val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem.toOriginalItem()) ?: let {
            GLog.w(TAG) { "[preDecodeThumbnailForViewGallery] failed to create resourceKey, path=${mediaItem.path}" }
            return null
        }
        val isSupportLossLessCache = (MimeTypeUtils.isBmp(mediaItem.mimeType) || ImageTypeUtils.supportLosslessCache(mediaItem))
        val options = ResourceGetOptions(
            inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey(),
            inCacheOperation = CacheOperation.ReadWriteAllCache,
            inCropParams = CropParams.noCrop(),
            inStorageQuality = if (isSupportLossLessCache) StorageQuality.LOSSLESS else StorageQuality.NOT_CARE
        )
        context.withAbility<IResourcingAbility, Bitmap?> {
            return it?.requestBitmap(resourceKey, options, null)?.result
        } ?: let {
            GLog.w(TAG) { "[preDecodeThumbnailForViewGallery] failed to request bitmap, resourceKey=$resourceKey" }
        }
        return null
    }

    override fun getTag(): String {
        return TAG
    }

    /**
     * 要加载的内容的size
     */
    enum class GifContentSizeType(internal val loadType: Int) {
        MicroThumb(GifContentFactory.LOAD_TYPE_MICRO_THUMBNAIL),
        PreviewThumb(GifContentFactory.LOAD_TYPE_THUMBNAIL)
    }

    /**
     * Gif sdk合成状态回调
     */
    sealed class GifSynthesisStatus {
        /**
         * Gif合成状态[GifSynthesisStart] 开始
         * Gif合成状态[GifSynthesisFailed] 失败
         * Gif合成状态[GifSynthesisTimeOut] [TimeUtils.TIME_14_SEC_IN_MS]超时
         * Gif合成状态[GifSynthesisProgress] 进度
         * Gif合成状态[GifSynthesisCompleted] 功能
         *    filePath: Gif生成的文件绝对路径
         *    fileName: Gif生成的文件名称
         *    costTimeSecond: Gif合成 SDK耗时 单位秒。带小数
         *    frameInterval : gif合成帧间隔  1 / frameInterval 为帧率
         */
        object GifSynthesisStart : GifSynthesisStatus()
        class GifSynthesisFailed(val errorCode: Int, val fileName: String) : GifSynthesisStatus()
        object GifSynthesisTimeOut : GifSynthesisStatus()
        class GifSynthesisProgress(val progress: Double) : GifSynthesisStatus()
        class GifSynthesisCompleted(
            val filePath: String,
            val fileName: String,
            val costTimeSecond: Float,
            val frameInterval: Float
        ) : GifSynthesisStatus()
    }

    /**
     * 剪辑之后的olive图片参数
     */
    private data class VideoInfo(val offset: Long = 0L, val length: Long = 0L, val videoStartUs: Long = 0L, val videoEndUs: Long = 0L)

    private companion object {
        private const val TAG = "GifSynthesisViewModel"
        private const val UPDATE_PREVIEW_RES_DATA_FROM_PATH = "updatePreviewResDataFromPath"

        /**
         * 标清画质 保存的最长边 边长
         */
        private const val RES_SAVE_LONG_SIDE_LENGTH_IN_STANDARD_DEFINITION = 640

        /**
         * 高清画质 保存的最长边 边长
         */
        private const val RES_SAVE_LONG_SIDE_LENGTH_IN_HIGH_DEFINITION = 960

        /**
         * 超清画质 保存的最长边 边长
         */
        private const val RES_SAVE_LONG_SIDE_LENGTH_IN_SUPER_DEFINITION = 1080
    }
}