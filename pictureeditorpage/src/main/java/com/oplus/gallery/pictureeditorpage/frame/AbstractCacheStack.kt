/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AbstractCacheStack.kt
 ** Description: 实现默认的栈操作，并抽象出实体保存[save]和实体加载的[load]
 ** Version: 1.0
 ** Date : 2023/8/9
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:AbstractCacheStack
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewujie@Apps.Gallery3D      2023/08/09    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.pictureeditorpage.frame

import android.content.Context
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.pictureeditorpage.frame.data.EditCacheEntry
import com.oplus.gallery.pictureeditorpage.frame.data.FilesCacheManager
import kotlin.collections.removeLast as ktRemoveLast

/**
 * 实现默认的栈操作，并抽象出实体保存[save]和实体加载的[load]方法，简化子类实现栈操作的复杂度
 *
 */
abstract class AbstractCacheStack<in T, out R, S>(
    context: Context,
    cacheName: String
) : IPhotoEditCacheStack<T, R> {

    /**
     * TAG
     */
    protected abstract val tag: String

    private val saveEntryList = ArrayList<S?>()

    private val filesCacheManager: FilesCacheManager by lazy {
        FilesCacheManager(context, hashCode().toString(), cacheName)
    }

    private var ptr = PTR_INITIALIZE_VALUE

    @Synchronized
    override fun push(input: T?): R? {
        while (ptr < saveEntryList.size - 1) {
            saveEntryList.ktRemoveLast()
            filesCacheManager.deleteCurrentFile()
        }
        val saveEntry = save(input) ?: let {
            GLog.e(tag, "[push] error because saveEntry fail")
            null
        }
        saveEntryList.add(saveEntry)
        ptr = saveEntryList.size - 1
        return saveEntry?.let { load(input, it) }
    }

    override fun fix(fixData: T?, index: Int): R? {
        if ((index < 0) || (index >= saveEntryList.size)) {
            GLog.e(tag, "[fix] error, illegal index:$index")
            return null
        }
        val saveEntry = save(fixData, saveEntryList[index]) ?: let {
            GLog.e(tag, "[fix] error because saveEntry fail")
            null
        }
        saveEntryList[index] = saveEntry
        return saveEntry?.let { load(fixData, it) }
    }

    @Synchronized
    override fun pop(): R? {
        if (saveEntryList.isEmpty()) {
            GLog.e(tag, "[pop] error because saveEntryList is empty")
            return null
        }
        val saveEntry = saveEntryList.ktRemoveLast() ?: return null
        ptr = saveEntryList.size - 1
        val result = load(null, saveEntry)
        if (result != null) {
            filesCacheManager.deleteCurrentFile()
        }
        return result
    }

    @Synchronized
    override fun peek(): R? {
        if (saveEntryList.isEmpty() || (ptr < 0)) {
            GLog.e(tag, "[peek] error because saveEntryList is empty or ptr < 0")
            return null
        }
        if (ptr >= saveEntryList.size) {
            ptr = saveEntryList.size - 1
        }
        val saveEntry = saveEntryList[ptr] ?: return null
        return load(null, saveEntry)
    }

    @Synchronized
    override fun peek(index: Int): R? {
        if ((index < 0) || index >= saveEntryList.size) {
            GLog.e(tag, "[peek] error because index $index is illegal, allSize:${saveEntryList.size}")
            return null
        }
        val saveEntry = saveEntryList[index] ?: return null
        return load(null, saveEntry)
    }

    @Synchronized
    override fun redo(): R? {
        if (saveEntryList.isEmpty()) {
            GLog.e(tag, "[redo] error because saveEntryList is empty")
            return null
        }
        ptr++
        if (ptr >= saveEntryList.size) {
            ptr = saveEntryList.size - 1
        }
        val saveResult = saveEntryList[ptr] ?: return null
        return load(null, saveResult)
    }

    @Synchronized
    override fun undo(): R? {
        if (saveEntryList.isEmpty()) {
            GLog.e(tag, "[undo] error because saveEntryList is empty")
            return null
        }
        if (ptr < 0) {
            ptr = PTR_INITIALIZE_VALUE
        } else if (ptr == 0) {
            GLog.e(tag, "[undo] error because ptr == 0")
            return null
        }
        ptr--
        val saveResult = saveEntryList[ptr] ?: return null
        return load(null, saveResult)
    }

    @Synchronized
    override fun canUndo(): Boolean {
        return ptr > PTR_INITIALIZE_VALUE + 1
    }

    @Synchronized
    override fun canRedo(): Boolean {
        return ptr < saveEntryList.size - 1
    }

    override fun getCurrentIndex(): Int {
        return ptr
    }

    @Synchronized
    override fun clear() {
        filesCacheManager.clear()
    }

    protected open fun requestFilePath(): String {
        val filePath = filesCacheManager.currentFilePath
        FileOperationUtils.createNewFile(filePath)
        return filePath
    }

    /**
     * 通过调一次currentFilePath 让mCacheIndex自增，但是不创建对应文件
     */
    protected open fun requestEmptyFile(): String {
        return filesCacheManager.currentFilePath
    }

    /**
     * 从保存的实体中加载出栈的数据
     * @param saveEntry 保存在栈列表中的实体，需要从此实体加载出栈的数据
     * @return 出栈的实体
     */
    internal abstract fun load(inputData: T?, saveEntry: S): R?

    /**
     * 保存实体
     * @param inputData T 入栈的数据
     * @param oldSaveEntry 保存在栈中的数据
     * @return 保存到实际栈列表的实体
     */
    internal abstract fun save(inputData: T?, oldSaveEntry: S? = null): S?

    companion object {
        const val PTR_INITIALIZE_VALUE = -1
    }
}

/**
 * 拓展数据的缓冲栈，使用EditCacheEntry作为入栈和出栈的数据结构
 */
abstract class ExtendCacheStack<T : EditCacheEntry, S>(
    context: Context,
    cacheName: String
) : AbstractCacheStack<T, T, S>(context, cacheName) {

    /**
     * 栈是否有变化
     */
    open fun isAllVaild(): Boolean {
        return false
    }
}
