/*
 * Copyright (C) 2012 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.pictureeditorpage.screennail;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.RectF;

import com.oplus.gallery.foundation.opengl.renderer.GLCanvas;
import com.oplus.gallery.foundation.ui.animation.AnimationTime;
import com.oplus.gallery.photoeditor.gl.texure.TiledTexture;
import com.oplus.gallery.photoeditor.screennail.ScreenNail;
import com.oplus.gallery.standard_lib.codec.ImageData;
import com.oplus.gallery.foundation.util.math.MathUtil;
import com.oplus.gallery.standard_lib.ui.util.AnimUtil;

// This is a ScreenNail wraps a Bitmap. There are some extra functions:
//
// - If we need to draw before the bitmap is available, we draw a rectange of
// placeholder color (gray).
//
// - When the the bitmap is available, and we have drawn the placeholder color
// before, we will do a fade-in animation.
public class TiledScreenNail implements ScreenNail {
    private static final int STYLE_DARK = 0;
    private static final int STYLE_LIGHT = 1;
    @SuppressWarnings("unused")
    private static final String TAG = "TiledScreenNail";
    // The duration of the fading animation in milliseconds
    private static final int DURATION = (int) (180 * AnimUtil.getAnimatorDurationScale());
    // These are special values for mAnimationStartTime
    private static final long ANIMATION_NOT_NEEDED = -1;
    private static final long ANIMATION_NEEDED = -2;
    private static final long ANIMATION_DONE = -3;
    private static final float RATIO_4_3 = 0.75f;
    private static int sMaxSide = 960;
    // This gets overridden by bitmap_screennail_placeholder
    // in GalleryUtils.initialize
    private int mPlaceholderColor;
    private boolean mDrawPlaceholder = true;
    private int mWidth;
    private int mHeight;
    private long mAnimationStartTime = ANIMATION_NOT_NEEDED;
    private Bitmap mBitmap;
    private ImageData mData;
    private TiledTexture mTexture;
    private boolean mOpaque;
    private Context mContext;

    public TiledScreenNail(Context context, ImageData imageData, int style, boolean opaque) {
        mContext = context;
        mData = imageData;
        mWidth = imageData.getFrameWidth();
        mHeight = imageData.getFrameHeight();
        mBitmap = imageData.getBitmap();
        mOpaque = opaque;
        mTexture = new TiledTexture(imageData);
        if (mContext != null) {
            switch (style) {
                case STYLE_DARK:
                    mPlaceholderColor = mContext.getResources().getColor(com.oplus.gallery.basebiz.R.color.base_reverse_placeholder_color, null);
                    break;
                case STYLE_LIGHT:
                    mPlaceholderColor = mContext.getResources().getColor(com.oplus.gallery.basebiz.R.color.base_placeholder_color, null);
                    break;
                default:
                    mPlaceholderColor = mContext.getResources().getColor(com.oplus.gallery.basebiz.R.color.base_reverse_placeholder_color, null);
                    break;
            }
        }

    }

    public TiledScreenNail(Context context, int width, int height, int placeholderColor, boolean opaque) {
        mContext = context;
        setSize(width, height);
        mPlaceholderColor = placeholderColor;
        mOpaque = opaque;
    }

    private void setSize(int width, int height) {
        if (width == 0 || height == 0) {
            width = sMaxSide;
            height = (int) (sMaxSide * RATIO_4_3);
        }
        float scale = Math.min(1, (float) sMaxSide / Math.max(width, height));
        mWidth = Math.round(scale * width);
        mHeight = Math.round(scale * height);
    }

    // Combines the two ScreenNails.
    // Returns the used one and recycle the unused one.
    public ScreenNail combine(ScreenNail other) {
        if (other == null) {
            return this;
        }

        if (!(other instanceof TiledScreenNail)) {
            recycle();
            return other;
        }

        // Now both are TiledScreenNail. Move over the information about width,
        // height, and Bitmap, then recycle the other.
        TiledScreenNail newer = (TiledScreenNail) other;
        mWidth = newer.mWidth;
        mHeight = newer.mHeight;
        if (newer.mTexture != null) {
            if (mBitmap != null) {
                // FIXME: 2020/9/7
                //GalleryBitmapPool.getInstance().put(mBitmap);
            }
            if (mTexture != null) {
                mTexture.recycle();
            }
            mBitmap = newer.mBitmap;
            mTexture = newer.mTexture;
            mData = newer.mData;
            newer.mBitmap = null;
            newer.mTexture = null;
        }
        newer.recycle();
        return this;
    }

    public void updatePlaceholderSize(int width, int height) {
        if (mData != null) {
            return;
        }
        if (width == 0 || height == 0) {
            return;
        }
        setSize(width, height);
    }

    @Override
    public int getWidth() {
        return mWidth;
    }

    @Override
    public int getHeight() {
        return mHeight;
    }

    @Override
    public void noDraw() {
    }

    @Override
    public void recycle() {
        if (mTexture != null) {
            mTexture.recycle();
            mTexture = null;
        }
        if (mBitmap != null) {
            //GalleryBitmapPool.getInstance().put(mBitmap);
            // FIXME: 2020/9/7
            mBitmap = null;
        }
        if (mData != null) {
            mData.recycle();
        }
    }

    @Override
    public void draw(GLCanvas canvas, int x, int y, int width, int height) {
        if (mTexture == null || !mTexture.isReady()) {
            if (mAnimationStartTime == ANIMATION_NOT_NEEDED) {
                mAnimationStartTime = ANIMATION_NEEDED;
            }
            if (mDrawPlaceholder) {
                canvas.fillRect(x, y, width, height, mPlaceholderColor);
            }
            return;
        }

        if (mAnimationStartTime == ANIMATION_NEEDED) {
            mAnimationStartTime = AnimationTime.get();
        }

        mTexture.setOpaque(mOpaque);
        if (isAnimating()) {
            mTexture.drawMixed(canvas, mPlaceholderColor, getRatio(), x, y,
                    width, height);
        } else {
            mTexture.draw(canvas, x, y, width, height);
        }
    }

    @Override
    public void draw(GLCanvas canvas, RectF source, RectF dest) {
        if (mTexture == null || !mTexture.isReady()) {
            canvas.fillRect(dest.left, dest.top, dest.width(), dest.height(),
                    mPlaceholderColor);
            return;
        }

        mTexture.draw(canvas, source, dest);
    }

    public boolean isAnimating() {
        // The TiledTexture may not be uploaded completely yet.
        // In that case, we count it as animating state and we will draw
        // the placeholder in TileImageView.
        if (mTexture == null || !mTexture.isReady()) {
            return true;
        }
        if (mAnimationStartTime < 0) {
            return false;
        }
        if (AnimationTime.get() - mAnimationStartTime >= DURATION) {
            mAnimationStartTime = ANIMATION_DONE;
            return false;
        }
        return true;
    }

    private float getRatio() {
        float r = (float) (AnimationTime.get() - mAnimationStartTime) / DURATION;
        return MathUtil.clamp(1.0f - r, 0.0f, 1.0f);
    }

    public boolean isShowingPlaceholder() {
        return (mBitmap == null) || isAnimating();
    }

    public TiledTexture getTexture() {
        return mTexture;
    }

    public Bitmap getBitmap() {
        return mBitmap;
    }

    public ImageData getImageData() {
        return mData;
    }

    @Override
    public boolean isContentValid() {
        return (mTexture != null) && mTexture.isContentValid();
    }
}
