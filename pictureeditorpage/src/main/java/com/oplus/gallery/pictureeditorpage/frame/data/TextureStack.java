/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - TextureStack.java
 * * Description:
 * * Version: 1.0
 * * Date : 2017/11/20
 * * Author: kexiuhua@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  kexiuhua@Apps.Gallery3D  2017/11/20        build this module
 ****************************************************************/

package com.oplus.gallery.pictureeditorpage.frame.data;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import com.oplus.decoder.Image;
import com.oplus.gallery.pictureeditorpage.frame.IPhotoEditCacheStack;
import com.oplus.gallery.standard_lib.codec.DecodeUtils;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.foundation.util.debug.GLog;
import java.util.List;
import java.util.Stack;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TextureStack implements IPhotoEditCacheStack<Bitmap, ResultState> {
    public static final int EMPTY_STACK_FLAG = -1;

    private static final String TAG = "TextureStack";

    private static final String CACHE_NAME = "texture_";
    private static final int PTR_INITIALIZE_VALUE = -1;
    private final Stack<TextureEntry> mStack = new Stack<>();
    private final FilesCacheManager mCacheManager;
    private final boolean mLosslessCache;
    private int mPtr = PTR_INITIALIZE_VALUE;

    public TextureStack(Context context, String parent) {
        this(context, parent, false);
    }

    public TextureStack(Context context, String parent, boolean losslessCache) {
        mCacheManager = new FilesCacheManager(context, parent, CACHE_NAME);
        mLosslessCache = losslessCache;
    }

    public synchronized void recover() {
        List<String> list = mCacheManager.recover();
        if ((list != null) && !list.isEmpty()) {
            for (String filePath : list) {
                if (!TextUtils.isEmpty(filePath)) {
                    TextureEntry entry = new TextureEntry();
                    entry.setFilePath(filePath);
                    mStack.push(entry);
                }
            }
        }
    }

    @Nullable
    @Override
    public ResultState push(@NotNull Bitmap bitmap) {
        return push(bitmap, TextureStack.EMPTY_STACK_FLAG);
    }

    public synchronized ResultState push(@NonNull Bitmap bitmap, int flag) {
        int maxPtr = mStack.size() - 1;
        while ((mPtr < maxPtr) && (maxPtr >= 0)) {
            TextureEntry entry = mStack.pop();
            if (entry == null) {
                return null;
            }
            loadResultState(entry, entry.getLoadState());
            //delete cache file
            String filepath = mCacheManager.deleteCurrentFile();
            if (TextUtils.isEmpty(filepath) || !filepath.equalsIgnoreCase(entry.getFilePath())) {
                GLog.d(TAG, "pop, cache not match stack");
            }
            maxPtr = mStack.size() - 1;
        }
        TextureEntry entry = new TextureEntry();
        entry.setFilePath(mCacheManager.getCurrentFilePath());
        entry.getSaveState().setState(saveBitmap(bitmap, entry));
        entry.setFlag(flag);

        TextureEntry result = mStack.push(entry);
        mPtr = mStack.size() - 1;
        if (result != null) {
            result.getSaveState().setFlag(flag);
        }
        return ((result != null) ? result.getSaveState() : null);
    }

    @Override
    public synchronized ResultState pop() {
        if (mStack.isEmpty()) {
            return null;
        }
        TextureEntry entry = mStack.pop();
        if (entry == null) {
            GLog.d(TAG, "entry is null");
            mPtr = mStack.size() - 1;
            return null;
        }
        loadResultState(entry, entry.getLoadState());
        //delete cache file
        String filepath = mCacheManager.deleteCurrentFile();
        if (TextUtils.isEmpty(filepath) || !filepath.equalsIgnoreCase(entry.getFilePath())) {
            GLog.d(TAG, "pop, cache not match stack");
        }
        mPtr = mStack.size() - 1;
        return entry.getLoadState();
    }

    @Override
    public synchronized ResultState peek() {
        if (mPtr >= mStack.size()) {
            mPtr = mStack.size() - 1;
            GLog.w(TAG, "peek, Ptr is too high, it had happen somethings not expected!");
        }
        if ((mStack.isEmpty()) || (mPtr < 0)) {
            GLog.w(TAG, "peek, mStack empty or mPtr < 0! mPtr = " + mPtr);
            return null;
        }
        return buildResultState(mStack.get(mPtr));
    }

    @Override
    public synchronized ResultState peek(int index) {
        if ((mStack.size() <= index) || (index < 0)) {
            return null;
        }
        return buildResultState(mStack.get(index));
    }

    @Override
    public synchronized ResultState undo() {
        if (mStack.isEmpty()) {
            return null;
        }
        if (mPtr < 0) {
            mPtr = PTR_INITIALIZE_VALUE;
            return null;
        } else if (mPtr == 0) {
            /*
            mPtr point to 0 is a valid layer,
            this layer stored the original texture,
            but UNDO is forbidden
            */
            return null;
        }
        mPtr--;
        return buildResultState(mStack.get(mPtr));
    }

    private ResultState buildResultState(TextureEntry entry) {
        if (entry == null) {
            GLog.w(TAG, "buildResultState, entry is null! mPtr = " + mPtr + ", size = " + mStack.size());
            return null;
        }
        //entry not hold Bitmap ref, so create one
        ResultState resultState = new ResultState(entry.hashCode());
        loadResultState(entry, resultState);
        resultState.setFlag(entry.getFlag());
        return resultState;
    }

    private boolean loadResultState(TextureEntry entry, ResultState resultState) {
        if ((entry == null) || (resultState == null)) {
            GLog.w(TAG, "loadResultState, entry is null or resultState is null ! mPtr = "
                + mPtr + ", size = " + mStack.size());
            return false;
        }
        resultState.setWidth(entry.getWidth());
        resultState.setHeight(entry.getHeight());
        loadBitmap(entry, resultState);
        return true;
    }

    public synchronized ResultState resetInitState() {
        if (mStack.isEmpty()) {
            return null;
        }
        return buildResultState(mStack.get(0));
    }

    @Override
    public synchronized ResultState redo() {
        if (mStack.isEmpty()) {
            return null;
        }
        mPtr++;
        if (mPtr >= mStack.size()) {
            mPtr = mStack.size() - 1;
            return null;
        }
        return buildResultState(mStack.get(mPtr));
    }

    @Override
    public synchronized boolean canUndo() {
        return mPtr > (PTR_INITIALIZE_VALUE + 1);
    }

    @Override
    public synchronized boolean canRedo() {
        return (mPtr < (mStack.size() - 1));
    }

    @Override
    public synchronized int getCurrentIndex() {
        return mPtr;
    }

    @Override
    public ResultState fix(Bitmap bitmap, int index) {
        // TextureStack 不支持fix接口
        GLog.w(TAG, "[fix] TextureStack unsupport fix");
        return null;
    }

    public final boolean isEmpty() {
        return mStack.isEmpty();
    }

    public synchronized void clear() {
        mCacheManager.clear();
        for (TextureEntry entry : mStack) {
            entry.clear();
        }
        mStack.clear();
    }

    private int saveBitmap(@NonNull Bitmap bitmap, TextureEntry entry) {
        int state = ResultState.STATE_ERROR;
        if (TextUtils.isEmpty(entry.getFilePath())) {
            return state;
        }
        if (!mCacheManager.isDirMk()) {
            return state;
        }
        long time = System.currentTimeMillis();
        entry.setColorSpace(bitmap.getColorSpace());
        try {
            if (!mLosslessCache) {
                if (Image.saveBitmapAsBmp(bitmap, entry.getFilePath())) {
                    state = ResultState.STATE_SUCCEED;
                }
            } else {
                entry.setWidth(bitmap.getWidth());
                entry.setHeight(bitmap.getHeight());
                if (Image.saveBitmapAsStream(bitmap, entry.getFilePath())) {
                    state = ResultState.STATE_SUCCEED;
                }
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            GLog.d(TAG, "saveBitmap, time: " + GLog.getTime(time));
        }
        return state;
    }

    private void loadBitmap(TextureEntry entry, ResultState resultState) {
        if ((entry == null) || (resultState == null)) {
            GLog.e(TAG, "loadBitmap. entry:" + entry + "resultState:" + resultState);
            return;
        }
        String filePath = entry.getFilePath();
        resultState.setState(ResultState.STATE_ERROR);
        if (!TextUtils.isEmpty(filePath)) {
            long time = System.currentTimeMillis();
            try {
                Bitmap bitmap = null;
                if (!mLosslessCache) {
                    bitmap = BitmapFactory.decodeFile(filePath);
                } else {
                    bitmap = Bitmap.createBitmap(resultState.getWidth(), resultState.getHeight(), Bitmap.Config.ARGB_8888);
                    if (!Image.readBitmapFromStream(bitmap, filePath)) {
                        //bitmap is invalid
                        bitmap = null;
                    }
                }
                if (bitmap != null) {
                    bitmap = DecodeUtils.ensureGLCompatibleBitmap(bitmap);
                    bitmap.setColorSpace(entry.getColorSpace());
                    GLog.d(TAG, "loadBitmap, : " + bitmap.getColorSpace());
                }
                if (bitmap != null) {
                    resultState.setState(ResultState.STATE_SUCCEED);
                    resultState.setBitmap(bitmap);
                }
            } catch (IllegalArgumentException e) {
                GLog.w(TAG, e);
            } finally {
                GLog.d(TAG, "loadBitmap, time: " + GLog.getTime(time));
            }
        }
    }

    public void saveTmpFile(Bitmap bitmap) {
        String filePath = mCacheManager.getTmpFilePath();
        if (TextUtils.isEmpty(filePath)) {
            return;
        }
        long time = System.currentTimeMillis();
        try {
            if (!mLosslessCache) {
                Image.saveBitmapAsBmp(bitmap, filePath);
            } else {
                Image.saveBitmapAsStream(bitmap, filePath);
            }
        } catch (Exception e) {
            GLog.w(TAG, e);
        } finally {
            GLog.d(TAG, "saveTmpFile, time: " + GLog.getTime(time));
        }
    }

    public synchronized Bitmap loadTmpFile() {
        String filePath = mCacheManager.getTmpFilePath();
        if (TextUtils.isEmpty(filePath)) {
            return null;
        }
        File file = new File(filePath);
        if (!file.exists() || !file.isFile()) {
            return null;
        }

        long time = System.currentTimeMillis();
        try {
            Bitmap bitmap = null;
            if (!mLosslessCache) {
                bitmap = BitmapFactory.decodeFile(filePath);
            } else {
                if (mPtr >= mStack.size()) {
                    GLog.e(TAG, "loadTmpFile, mNeedAlphaChannel=true, mPtr=" + mPtr + ">=mStack.size()");
                    return null;
                }
                TextureEntry entry = mStack.get(mPtr);
                if (entry == null) {
                    GLog.e(TAG, "loadTmpFile, mNeedAlphaChannel=true, entry==null.");
                    return null;
                }
                bitmap = Bitmap.createBitmap(
                    entry.getWidth(),
                    entry.getHeight(),
                    Bitmap.Config.ARGB_8888
                );
                if (!Image.readBitmapFromStream(bitmap, filePath)) {
                    //bitmap is invalid
                    bitmap = null;
                }
            }
            if (bitmap != null) {
                bitmap = DecodeUtils.ensureGLCompatibleBitmap(bitmap);
            }
            return bitmap;
        } catch (IllegalArgumentException e) {
            GLog.w(TAG, e);
        } finally {
            GLog.d(TAG, "loadTmpFile, time: " + GLog.getTime(time));
        }
        return null;
    }

    public void deleteTmpFile() {
        String filePath = mCacheManager.getTmpFilePath();
        if (TextUtils.isEmpty(filePath)) {
            return;
        }
        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            FileOperationUtils.deleteQuietly(file);
        }
    }

    public boolean hasTmpFile() {
        String filePath = mCacheManager.getTmpFilePath();
        if (TextUtils.isEmpty(filePath)) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }
}
