/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorPreviewState.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/07
 * * Author: xinyang.Hu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  xinyang.Hu@Apps.Gallery3D       2017/11/07    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/

package com.oplus.gallery.pictureeditorpage.app.preview;

import static com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value;
import static com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value.PAGE_CLICK_ITEM_AI_FILTER;
import static com.oplus.gallery.photoeditor.editingvvm.watermark.track.WatermarkTrackConstant.Value.RESULT_TYPE_EXIT;

import android.text.TextUtils;
import android.util.Size;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import com.oplus.gallery.business_lib.aiunitdownload.EntranceListener;
import com.oplus.gallery.business_lib.aiunitdownload.RefuseNextAction;
import com.oplus.gallery.foundation.ui.animation.AnimationHelper;
import com.oplus.gallery.foundation.ui.animator.GestureAnimator;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.photoeditor.base.PhotoEditorView;
import com.oplus.gallery.photoeditor.base.PhotoPreCheck;
import com.oplus.gallery.photoeditor.common.Config;
import com.oplus.gallery.photoeditor.gl.widget.GLView;
import com.oplus.gallery.pictureeditorpage.EditablePhotoPage;
import com.oplus.gallery.pictureeditorpage.PictureContext;
import com.oplus.gallery.photoeditor.R;
import com.oplus.gallery.pictureeditorpage.app.adjustment.EditorAdjustmentState;
import com.oplus.gallery.pictureeditorpage.app.aifilter.AiFilterSheet;
import com.oplus.gallery.pictureeditorpage.app.aifilter.EditorAiFilterState;
import com.oplus.gallery.photoeditor.common.AiFilterPreCheck;
import com.oplus.gallery.pictureeditorpage.app.aifilter.ui.AiFilterDialogHelper;
import com.oplus.gallery.pictureeditorpage.app.aifilter.ui.CheckAiFilterCallback;
import com.oplus.gallery.pictureeditorpage.app.airepair.AiRepairSheet;
import com.oplus.gallery.pictureeditorpage.app.airepair.EditorAiRepairState;
import com.oplus.gallery.pictureeditorpage.app.beauty.EditorBeautyState;
import com.oplus.gallery.pictureeditorpage.app.blur.EditorBlurringState;
import com.oplus.gallery.pictureeditorpage.app.border.EditorBorderState;
import com.oplus.gallery.pictureeditorpage.app.doodle.EditorDoodleState;
import com.oplus.gallery.pictureeditorpage.app.eliminate.EditorEliminateState;
import com.oplus.gallery.pictureeditorpage.app.eliminate.entrance.EliminateEntranceHelper;
import com.oplus.gallery.pictureeditorpage.app.filter.EditorFilterState;
import com.oplus.gallery.pictureeditorpage.app.mosaic.EditorMosaicState;
import com.oplus.gallery.pictureeditorpage.app.olive.EditorOliveState;
import com.oplus.gallery.pictureeditorpage.app.pmsticker.EditorPmStickerState;
import com.oplus.gallery.pictureeditorpage.app.rotateclip.EditorRotateClipState;
import com.oplus.gallery.pictureeditorpage.app.sticker.EditorStickerState;
import com.oplus.gallery.pictureeditorpage.app.text.EditorTextState;
import com.oplus.gallery.pictureeditorpage.app.watermark.EditorWatermarkState;
import com.oplus.gallery.pictureeditorpage.app.watermark.WatermarkImagePack;
import com.oplus.gallery.pictureeditorpage.app.watermark.WatermarkOperationRecord;
import com.oplus.gallery.pictureeditorpage.app.watermark.track.WatermarkTrackHelper;
import com.oplus.gallery.pictureeditorpage.base.EditorNavigateState;
import com.oplus.gallery.pictureeditorpage.base.EditorNavigateUIController;
import com.oplus.gallery.pictureeditorpage.base.EditorPhotoDataAdapter;
import com.oplus.gallery.pictureeditorpage.base.OnEditorStateIconClickListener;
import com.oplus.gallery.pictureeditorpage.common.PictureEditTrackHelper;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseUIController;
import com.oplus.gallery.pictureeditorpage.frame.EditorStateManager;
import com.oplus.gallery.pictureeditorpage.frame.Sheet;
import com.oplus.gallery.pictureeditorpage.frame.data.ImagePack;
import com.oplus.gallery.pictureeditorpage.gl.widget.GLSplitMenu;
import com.oplus.gallery.pictureeditorpage.scene.PhotoEditSceneSet;
import com.oplus.gallery.pictureeditorpage.state.ActivityState;
import com.oplus.gallery.standard_lib.app.AppScope;
import com.oplus.gallery.standard_lib.ui.util.ClickUtil;
import com.oplus.gallery.standard_lib.ui.util.ToastUtil;

import kotlin.Unit;
import kotlinx.coroutines.BuildersKt;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.Dispatchers;

public class EditorPreviewState extends EditorNavigateState implements OnEditorStateIconClickListener {
    public static final String TYPE_PREVIEW = "Preview";
    private static final String TAG = "EditorPreviewState";
    private static final int MSG_SHOW_LOADING_DELAY = 300;
    private EditorPreviewUIController mPreviewUIController;
    private EditorStateManager.OnLoadingTouchListener mOnLoadingListener = null;
    private AiFilterDialogHelper mAiFilterDialogHelper = null;
    private boolean mHasEditAnimalStart = false;
    private EliminateEntranceHelper mEliminateEntranceHelper = null;

    public EditorPreviewState(PictureContext pictureContext, ViewGroup rootView, PhotoEditorView photoEditorView) {
        super(TYPE_PREVIEW, pictureContext, rootView, photoEditorView);
    }

    @Override
    protected EditorNavigateUIController createNavigateUIController() {
        return new EditorPreviewUIController(mPictureContext, mRootView, this);
    }

    @Override
    protected Sheet createSheet() {
        return onCreateSheet();
    }

    protected Sheet onCreateSheet() {
        return new PreviewSheet();
    }

    @Override
    protected EditorBaseUIController createUIController() {
        mPreviewUIController = new EditorPreviewUIController(mPictureContext, mRootView, this);
        mPreviewUIController.setOnStateIconClickListener(this);
        mPreviewUIController.setOnLoadingTouchListener(() -> {
            if (mOnLoadingListener != null) {
                mOnLoadingListener.onLoadingTouch();
            }
        });
        return mPreviewUIController;
    }

    @Override
    public void create() {
        super.create();
    }

    public void firstStartFaceDetect() {
        if (mSheet != null && (mSheet instanceof PreviewSheet)) {
            ((PreviewSheet) mSheet).firstStartFaceDetect();
        }
    }

    @Override
    public void onFaceDetected(boolean hasFace) {
        super.onFaceDetected(hasFace);
    }

    @Override
    protected void switchState(int id, int position) {
        String itemId = null;
        if (id == R.id.picture3d_editor_id_sticker) {
            EditorStickerState stkState = new EditorStickerState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(stkState);
            itemId = Value.PAGE_CLICK_ITEM_ID_STICKER;
        } else if (id == R.id.picture3d_editor_id_olive) {
            EditorOliveState oliveState = new EditorOliveState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(oliveState);
            itemId = Value.PAGE_CLICK_ITEM_ID_OLIVE;
        } else if (id == R.id.picture3d_editor_id_rotate_clip) {
            EditorRotateClipState rotateState = new EditorRotateClipState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(rotateState);
            itemId = Value.PAGE_CLICK_ITEM_ID_ROTATE;
        } else if (id == R.id.picture3d_editor_id_filter) {
            EditorFilterState filterState = new EditorFilterState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(filterState);
            itemId = Value.PAGE_CLICK_ITEM_ID_FILTER;
        } else if (id == R.id.picture3d_editor_id_adjust) {
            EditorAdjustmentState adjustState = new EditorAdjustmentState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(adjustState);
            itemId = Value.PAGE_CLICK_ITEM_ID_ADJUST_NEW;
        } else if (id == R.id.picture3d_editor_id_ai_repair) {
            onAiRepairClick();
            itemId = Value.PAGE_CLICK_ITEM_ID_AI_REPAIR;
        } else if (id == R.id.picture3d_editor_id_beauty) {
            onBeautyClick();
            itemId = Value.PAGE_CLICK_ITEM_ID_BEAUTY;
        } else if (id == R.id.picture3d_editor_id_doodle_text) {
            EditorTextState textState = new EditorTextState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(textState);
            itemId = Value.PAGE_CLICK_ITEM_ID_TEXT;
        } else if (id == R.id.picture3d_editor_id_doodle) {
            EditorDoodleState doodleState = new EditorDoodleState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(doodleState);
            itemId = Value.PAGE_CLICK_ITEM_ID_DOODLE;
        } else if (id == R.id.picture3d_editor_id_mosaic) {
            onMosaicClick();
            itemId = Value.PAGE_CLICK_ITEM_ID_MOSAIC;
        } else if (id == R.id.picture3d_editor_id_eliminate_pen) {
            onEliminatePenClick();
            itemId = Value.PAGE_CLICK_ITEM_ID_ELIMINATE;
        } else if (id == R.id.picture3d_editor_id_blurring) {
            EditorBlurringState blurringState = new EditorBlurringState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(blurringState);
            itemId = Value.PAGE_CLICK_ITEM_ID_BLURRING;
        } else if (id == R.id.picture3d_editor_id_ai_filter) {
            onAiFilterClick();
            itemId = PAGE_CLICK_ITEM_AI_FILTER;
        } else if (id == R.id.picture3d_editor_id_pm_sticker) {
            EditorBaseState editorState = new EditorPmStickerState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(editorState);
            itemId = Value.PAGE_CLICK_ITEM_ID_PM_STICKER;
        } else if (id == R.id.picture3d_editor_id_watermark) {
            EditorBaseState editorState = new EditorWatermarkState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(editorState);
            itemId = Value.PAGE_CLICK_ITEM_ID_WATERMARK;
        } else if (id == R.id.picture3d_editor_id_border) {
            EditorBaseState editorState = new EditorBorderState(mPictureContext, mRootView, mPhotoEditorView);
            changeState(editorState);
            itemId = Value.PAGE_CLICK_ITEM_ID_BORDER;
        } else {
            GLog.d(TAG, "can not response this! view id = " + id);
        }
        if (!TextUtils.isEmpty(itemId)) {
            trackPhotoLabel(itemId);
        }
        if (mPictureContext != null) {
            ActivityState activityState = mPictureContext.getStateManager().getTopState();
            if (activityState instanceof EditablePhotoPage) {
                setGLActionSplitBarInvisible();
            }
        }
    }

    private void onEliminatePenClick() {
        GestureAnimator animator = getGestureAnimator();
        int imageWidth = (int) animator.getImageWidth();
        int imageHeight = (int) animator.getImageHeight();
        Size imageSize = new Size(imageWidth, imageHeight);
        mEliminateEntranceHelper = new EliminateEntranceHelper(mPictureContext.getActivity());
        mEliminateEntranceHelper.start(imageSize, getGestureAnimator().getDisplayRect(), new EntranceListener() {
            @Override
            public void onEnter() {
                GLog.d(TAG, "onEliminatePenClick [onEnter]");
                EditorEliminateState eliminatePenState = new EditorEliminateState(mPictureContext, mRootView, mPhotoEditorView);
                changeState(eliminatePenState);
            }

            @Override
            public void onRefuse(RefuseNextAction reason) {
                GLog.d(TAG, "onEliminatePenClick [onRefused]");
            }

            @Override
            public void onRetryLoadSo() {
                GLog.d(TAG, "onEliminatePenClick [onRetryLoadSo]");
                mEliminateEntranceHelper.start(imageSize, getGestureAnimator().getDisplayRect(), this);
            }
        });
    }

    @Override
    protected void beforeStateChanged(EditorBaseState changState) {
        super.beforeStateChanged(changState);
        PreviewSheet previewSheet = ((PreviewSheet) mSheet);
        previewSheet.fireBeforeChangeStateEvent(changState.getClass());
    }

    private void trackPhotoLabel(String itemId) {
        BuildersKt.launch(AppScope.INSTANCE, Dispatchers.getIO(), CoroutineStart.DEFAULT,
                (coroutineScope, continuation) -> {
                    String sceneNameStr = PictureEditTrackHelper.getPhotoLabels(getEditablePhotoPage().getFilePath(),
                            mContext);
                    if (!TextUtils.isEmpty(sceneNameStr)) {
                        GLog.d(TAG, "trackPhotoLabel # sceneNameStr: " + sceneNameStr);
                    }
                    return Unit.INSTANCE;
                });
    }

    private void checkAiFilterDialog() {
        mAiFilterDialogHelper = new AiFilterDialogHelper(mPictureContext.getActivity());
        mAiFilterDialogHelper.setCheckAiFilterCallback(new CheckAiFilterCallback() {
            @Override
            public void onEnter() {
                GLog.d(TAG, "checkAiFilterDialog [onEnter]");
                EditorAiFilterState aiFilterState = new EditorAiFilterState(mPictureContext, mRootView, mPhotoEditorView);
                changeState(aiFilterState);
            }

            @Override
            public void onRefuse() {
                GLog.d(TAG, "checkAiFilterDialog [onRefused]");
            }
        });
        mAiFilterDialogHelper.checkAiFilterPermissionsDialog();
    }

    private void onMosaicClick() {
        if (mSheet instanceof PreviewSheet) {
            GLog.d(TAG, "onMosaicClick: ");
            EditorMosaicState mosaicState = new EditorMosaicState(mPictureContext, mRootView, mPhotoEditorView,
                    ((PreviewSheet) mSheet).getPreCheck(PhotoPreCheck.CODE_MOSAIC).checkSensitivePicture(),
                    ((PreviewSheet) mSheet).getSensitiveMosaicExecutor());
            changeState(mosaicState);
        }
    }

    private void onAiRepairClick() {
        if (mSheet instanceof PreviewSheet) {
            final PreviewSheet sheet = ((PreviewSheet) mSheet);
            sheet.setPreCheckListener(PhotoPreCheck.CODE_AI_REPAIR, hasFace -> {
                mPhotoEditorView.revert(false);
                GLog.d(TAG, "AiRepairPreState.PreCheck success and start change state");
                EditorAiRepairState aiRepairState = new EditorAiRepairState(mPictureContext, mRootView, mPhotoEditorView);
                changeState(aiRepairState);
            });
            sheet.preCheck(PhotoPreCheck.CODE_AI_REPAIR);
        }
    }

    private void onBeautyClick() {
        if (mSheet instanceof PreviewSheet) {
            if (((PreviewSheet) mSheet).getPreCheck(PhotoPreCheck.CODE_BEAUTY_REPAIR).checkFace()) {
                GLog.d(TAG, "BeautyPreState.PreCheck success and start change state");
                mPhotoEditorView.revert(false);
                EditorBeautyState beautyState = new EditorBeautyState(mPictureContext, mRootView, mPhotoEditorView);
                changeState(beautyState);
            } else {
                ToastUtil.showShortToast(R.string.picture3d_editor_text_face_beauty_no_face_detected);
            }
        }
    }

    private void onAiFilterClick() {
        if (mSheet instanceof PreviewSheet) {
            final PreviewSheet sheet = ((PreviewSheet) mSheet);
            int status = sheet.preCheck(PhotoPreCheck.CODE_AI_FILTER);
            if (status == AiFilterPreCheck.CHECK_SUCCESS) {
                checkAiFilterDialog();
            } else {
                showAiFilterDialog(status);
            }
        }
    }

    private void showAiFilterDialog(int status) {
        switch (status) {
            case AiFilterPreCheck.CHECK_MIME_TYPE_FAIL:
                ToastUtil.showShortToast(mContext.getString(R.string.picture3d_ai_filter_toast_pic_format_unsupport));
                break;
            case AiFilterPreCheck.CHECK_SIZE_FAIL:
                ToastUtil.showShortToast(mContext.getString(R.string.picture3d_ai_filter_toast_pic_size_unsupport));
                break;
            default:
                break;
        }
    }

    @Override
    public boolean isTouchable() {
        return Config.Animation.EFFECT_PHOTO_TOUCHABLE_IN_PREVIEW;
    }

    @Override
    public boolean isScrollable() {
        return Config.Animation.EFFECT_PHOTO_SCROLLABLE_IN_PREVIEW;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return super.onTouchEvent(event);
    }

    @Override
    public void clickCancel(View view) {
        GLog.d(TAG, "clickCancel, name = " + TYPE_PREVIEW + ", this = " + this);
        if ((mSheet != null) && mSheet.hasModified() && !mHasEditAnimalStart) {
            showCancelDialog(mContext, view, (dialog, which) -> exit());
        } else {
            // 延迟上报埋点,否则Local HDR提亮效果在返回大图时可能会闪烁一下.
            mRootView.post(() -> mPreviewUIController
                    .startReport(getEditablePhotoPage().getImageUri(), null));

            exit();
        }
    }

    @Override
    public void clickDone() {
        // 防止重复点击
        if (!ClickUtil.clickable()) {
            GLog.w(TAG, "[clickDone] Click repeat !!!");
            return;
        }
        PhotoEditSceneSet photoEditSceneSet = mEditablePage.getSceneSet();
        SaveDialogInfo saveDialogInfo = photoEditSceneSet.getSaveDialogInfo();
        getUIController().showSaveDialogIfNeed(saveDialogInfo, new SaveDialogCallbackImp());
    }

    public void setHasEditAnimalStart(boolean isStart) {
        mHasEditAnimalStart = isStart;
    }

    public void pause(boolean isActivityPause) {
        pause(isActivityPause, false, false, null);
    }

    public void pause(
            boolean isActivityPause,
            boolean hasAnimal,
            boolean isSavedCompleted,
            AnimationHelper.OnAnimalEditEndListener onAnimalEditOutListener
    ) {
        GLog.d(TAG, "isActivityPause = " + isActivityPause + " hasAnimal " + hasAnimal + " isSavedCompleted " + isSavedCompleted);
        if (hasAnimal) {
            mPictureContext.getGLRoot().lockRenderThread();
            try {
                setHasEditAnimalStart(true);
                if (mPreviewUIController != null) {
                    mPreviewUIController.startPauseAnim(isActivityPause);
                }
                mPictureContext.getSplitMenuManager().getGLSplitMenu().setAnimalEditOutListener(onAnimalEditOutListener);
                mPictureContext.getGLActionBarHelper().getGLActionBar().setVisibility(GLView.VISIBLE);
                mPictureContext.getGLActionBarHelper().getGLActionBar().startEditOutAlphaAnimation(AnimationHelper.ALPHA_EDIT_DURATION, AnimationHelper.ALPHA_DELAY_TIME_133);
                GLSplitMenu glSplitMenu = mPictureContext.getSplitMenuManager().getGLSplitMenu();
                glSplitMenu.setVisibility(GLView.VISIBLE);
                glSplitMenu.startEditOutAlphaAnimation(AnimationHelper.ALPHA_EDIT_DURATION, AnimationHelper.ALPHA_DELAY_TIME_133);
                mPhotoEditorView.overridePreviewVisibleRect(this.mEditablePage.queryPhotoPageVisibleRect());
                if (mSheet != null) {
                    mSheet.onPause();
                }
            } finally {
                mPictureContext.getGLRoot().unlockRenderThread();
            }
        } else {
            super.pause(isActivityPause);
        }
    }

    @Override
    public boolean onBackPressed() {
        if ((mSheet != null) && mSheet.hasModified() && !mHasEditAnimalStart) {
            showCancelDialog(mContext, null, (dialog, which) -> exit());
        } else {
            exit();
        }
        return true;
    }

    @Override
    protected void dialogCancelClickStatistics() {
        AiFilterSheet.Companion.addEditParamActions();
        trackWatermarkData(RESULT_TYPE_EXIT);
        mPreviewUIController.startReport(getEditablePhotoPage().getImageUri(), null);
    }

    @Override
    public void onCompareTouchDown() {
        super.onCompareTouchDown();
    }

    private void setGLActionSplitBarInvisible() {
        if ((mPictureContext == null) || mHasEditAnimalStart) {
            return;
        }
        if (mPictureContext.getGLActionBarHelper().getGLActionBar().getVisibility() == GLView.VISIBLE) {
            mPictureContext.getGLActionBarHelper().getGLActionBar().setVisibility(GLView.INVISIBLE);
        }
        if (mPictureContext.getSplitMenuManager().getGLSplitMenu().getVisibility() == GLView.VISIBLE) {
            mPictureContext.getSplitMenuManager().getGLSplitMenu().setVisibility(GLView.INVISIBLE);
        }
    }

    @Override
    public void destroy() {
        super.destroy();
        if (null != mAiFilterDialogHelper) {
            mAiFilterDialogHelper.destroy();
        }
        // 退出编辑的时候断开路人消除下载模型的连接
        if (mEliminateEntranceHelper != null) {
            mEliminateEntranceHelper.removePassersDownloadRequestIfNeed();
            mEliminateEntranceHelper = null;
        }
    }

    @Override
    protected boolean isNeedExitAnim() {
        return mEnterAnimationEnable;
    }

    @Override
    public void onEditorStateIconClick(View view, int position) {
        super.onEditorStateIconClick(view, position);
        int viewId = view.getId();
        if (viewId == R.id.picture3d_editor_id_rotate_clip) {
            // 裁剪做revert，且需要带动画
            mPhotoEditorView.revert(true);
        } else if ((viewId != R.id.picture3d_editor_id_ai_repair)
                && (viewId != R.id.picture3d_editor_id_beauty)) {
            // 目前ai修复和美颜要判断是否有人脸才能进入,不能进入的情况默认不做revert
            mPhotoEditorView.revert(false);
        }
        // 哈苏一期在预览界面可以编辑水印，相应点击事件
        if (viewId == R.id.picture3d_editor_id_hasselblad_watermark) {
            ((PreviewSheet) mSheet).processWatermark();
        }
    }

    private void trackWatermarkData(int editType) {
        EditorPhotoDataAdapter adapter = mEditablePage.getEditorPhotoDataAdapter();
        WatermarkOperationRecord watermarkOperationRecord = adapter.getWatermarkOperationRecord();
        ImagePack imagePack = adapter.getTextureDrum().getImagePack();
        if (imagePack instanceof WatermarkImagePack) {
            WatermarkTrackHelper.trackWatermarkData(
                    editType,
                    (WatermarkImagePack) imagePack,
                    watermarkOperationRecord.getCurrentOperation().ordinal()
            );
        }
    }

    public void refreshOliveState(boolean isDisable) {
        mPreviewUIController.refreshOliveState(isDisable);
    }
}