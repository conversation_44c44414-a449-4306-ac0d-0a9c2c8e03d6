/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ApplyStyleCache.kt
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2020/12/08
 ** Author: <EMAIL>
 ** TAG: OPLUS_FEATURE_SENIOR_EDITOR
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>		2020/12/08		1.0		OPLUS_FEATURE_AI_FILTER
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.aifilter.common

class ApplyStyleCache {
    var cacheId: Int = 0
    var cachePosition: Int = 0
    var hasCacheTask = false

    fun cacheApplyStyleInfo(id: Int, position: Int) {
        cacheId = id
        cachePosition = position
        hasCacheTask = true
    }

    fun clearApplyStyleInfo() {
        hasCacheTask = false
    }
}