/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - EditorPreviewState.java
 * * Description: XXXXXXXXXXXXXXXXXXXXX.
 * * Version: 1.0
 * * Date : 2017/11/07
 * * Author: xinyang.Hu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  xinyang.Hu@Apps.Gallery3D       2017/11/07    1.0     build this module
 * *  BBB       2016/11/25     1.1     add some components
 ****************************************************************/

package com.oplus.gallery.pictureeditorpage.app.blur;

import android.view.ViewGroup;

import com.oplus.gallery.photoeditor.editingvvm.blur.BlurEntry;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState;
import com.oplus.gallery.pictureeditorpage.PictureContext;
import com.oplus.gallery.photoeditor.common.Config;
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseUIController;
import com.oplus.gallery.photoeditor.base.PhotoEditorView;
import com.oplus.gallery.pictureeditorpage.common.PictureEditTrackHelper;
import com.oplus.gallery.pictureeditorpage.frame.Sheet;

import java.util.Map;
import java.util.Set;

import static com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Key;
import static com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant.Value;

public class EditorBlurringState extends EditorBaseState {

    private static final String TYPE_NAME = "Blurring";

    public EditorBlurringState(PictureContext pictureContext, ViewGroup rootView, PhotoEditorView photoEditorView) {
        super(TYPE_NAME, pictureContext, rootView, photoEditorView);
    }

    @Override
    public void create() {
        super.create();
        this.mEditablePage.setGLNeverLayoutYet(true);
    }

    @Override
    protected Sheet createSheet() {
        return new BlurSheet();
    }

    @Override
    protected EditorBaseUIController createUIController() {
        return new EditorBlurringUIController(mPictureContext, mRootView, this);
    }

    @Override
    public boolean isTouchable() {
        return Config.Animation.EFFECT_PHOTO_TOUCHABLE_IN_BLUR;
    }

    @Override
    public boolean isScrollable() {
        return Config.Animation.EFFECT_PHOTO_SCROLLABLE_IN_BLUR;
    }

    @Override
    public void clickDone() {
        EditorBaseUIController uiController = getUIController();
        if ((uiController != null) && (uiController instanceof EditorBlurringUIController) && mSheet.hasModified()) {
            PictureEditTrackHelper.pushSheetEventId(Integer.parseInt(Value.PAGE_CLICK_ITEM_ID_BLURRING));
        }
        super.clickDone();
    }
}
