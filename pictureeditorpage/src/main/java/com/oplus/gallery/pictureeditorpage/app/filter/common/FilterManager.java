/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - FilterManager.java
 ** Description:
 ** Version: 1.0
 ** Date : 2017/12/03
 ** Author: kexiuhua@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                 <data>     <version>  <desc>
 **  kexiuhua@Apps.Gallery3D  2017/12/03        build this module
 ****************************************************************/
package com.oplus.gallery.pictureeditorpage.app.filter.common;

import android.content.res.Resources;
import android.graphics.Bitmap;

import com.oplus.gallery.photoeditor.R;
import com.oplus.gallery.pictureeditorpage.app.filter.processor.FilterLoader;
import com.oplus.gallery.pictureeditorpage.app.filter.processor.FilterLoaderCallback;
import com.oplus.gallery.pictureeditorpage.base.EditorPhotoDataAdapter;
import com.oplus.gallery.pictureeditorpage.app.filter.processor.FilterEntry;
import com.oplus.gallery.pictureeditorpage.frame.processor.AsyncLoader;
import com.oplus.gallery.pictureeditorpage.frame.processor.TaskManager;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.ArrayList;
import java.util.List;

public class FilterManager {
    private static final String TAG = "FilterManager";
    private static final int STATE_NONE = 0;
    private static final int STATE_LOADING = 1;
    private static final int STATE_LOADED = 2;
    private static final int STATE_LOADED_FAILED = 3;
    private static final int MICRO_BITMAP_SIZE = 256;
    private final List<FilterLoaderCallback> mCallbackList = new ArrayList<>();
    private final TaskManager mLoader;
    private final Resources mRes;
    // change the MICRO_BITMAP_SIZE from 256 to get the imageview pixel size to fix the MP bug
    // need optimize later by crop the drawable in fllterRoundedImageView:onDraw()
    private final int mMicroBitmapSize ;
    private List<FilterEntry> mFilterList;
    private int mLoaded = STATE_NONE;
    private AsyncLoader.Future mCurrentFuture;

    public FilterManager(TaskManager loader, Resources res) {
        mLoader = loader;
        mRes = res;
        int bitmapSize = mRes.getDimensionPixelSize(R.dimen.picture3d_filter_image_item_width);
        if ((bitmapSize <= 0) || (bitmapSize > MICRO_BITMAP_SIZE)) {
            mMicroBitmapSize = MICRO_BITMAP_SIZE;
        } else {
            mMicroBitmapSize = bitmapSize;
        }
    }

    public void registerCallback(FilterLoaderCallback callback) {
        synchronized (FilterManager.this) {
            if (!mCallbackList.contains(callback)) {
                mCallbackList.add(callback);
            }
        }
    }

    public void unRegisterCallback(FilterLoaderCallback callback) {
        synchronized (FilterManager.this) {
            if (mCallbackList.contains(callback)) {
                mCallbackList.remove(callback);
            }
        }
    }

    public List<FilterEntry> getFilters() {
        List<FilterEntry> list = new ArrayList<>();
        synchronized (FilterManager.this) {
            if (mFilterList != null) {
                list.addAll(mFilterList);
            }
        }
        return list;
    }

    public synchronized void loadFilters(Bitmap srcBitmap) {
        if (mLoaded == STATE_NONE) {
            mLoaded = STATE_LOADING;
            loadMicroBitmap(srcBitmap);
        }
    }

    public synchronized void loadFilters(EditorPhotoDataAdapter adapter) {
        GLog.d(TAG, "loadFilters");
        if (mLoaded == STATE_NONE) {
            mLoaded = STATE_LOADING;
            loadMicroBitmap(adapter);
        }
    }

    private void loadMicroBitmap(EditorPhotoDataAdapter adapter) {
        if (adapter == null) {
            GLog.w(TAG, "loadMicroBitmap, cacheManager is null!");
            return;
        }
        AsyncLoader.Future future = mLoader.submitPoolTask(new MicroBitmap(adapter, mMicroBitmapSize), new AsyncLoader.JobCallback<Bitmap>() {
            @Override
            public void onDone(Bitmap bitmap) {
                if (bitmap != null) {
                    startFilterJob(bitmap);
                } else {
                    mLoaded = STATE_LOADED_FAILED;
                }
            }
        });
        if (future == null) {
            mLoaded = STATE_LOADED_FAILED;
            GLog.w(TAG, "loadMicroBitmap, future is null!");
        }
    }

    private void loadMicroBitmap(Bitmap srcBitmap) {
        if (srcBitmap == null) {
            mLoaded = STATE_LOADED_FAILED;
            GLog.w(TAG, "loadMicroBitmap, srcBitmap is null!");
            return;
        }
        int width = srcBitmap.getWidth();
        int height = srcBitmap.getHeight();
        if ((width <= mMicroBitmapSize) && (height <= mMicroBitmapSize)) {
            startFilterJob(srcBitmap.copy(srcBitmap.getConfig(), srcBitmap.isMutable()));
            return;
        }
        AsyncLoader.Future future = mLoader.submitPoolTask(new MicroBitmap(srcBitmap, mMicroBitmapSize), new AsyncLoader.JobCallback<Bitmap>() {
            @Override
            public void onDone(Bitmap bitmap) {
                if (bitmap != null) {
                    startFilterJob(bitmap);
                } else {
                    mLoaded = STATE_LOADED_FAILED;
                }
            }
        });
        if (future == null) {
            mLoaded = STATE_LOADED_FAILED;
            GLog.w(TAG, "loadMicroBitmap, future is null!");
        }
    }

    private void startFilterJob(final Bitmap bitmap) {
        if (bitmap == null) {
            mLoaded = STATE_LOADED_FAILED;
            GLog.w(TAG, "startFilterJob, bitmap is null!");
            return;
        }
        if (mCurrentFuture != null) {
            mCurrentFuture.cancel();
        }
        mCurrentFuture = mLoader.submitPoolTask(new FilterLoader(mRes, bitmap, new FilterLoaderCallback() {
            //1. update filter list
            @Override
            public void onUpdateFilterList(List<FilterEntry> list) {
                synchronized (FilterManager.this) {
                    if (list == null) {
                        mFilterList = new ArrayList<>();
                        mLoaded = STATE_LOADED_FAILED;
                    } else {
                        mFilterList = list;
                    }
                    dispatchUpdateList(getFilters());
                }
                GLog.d(TAG, "loadFilters, updateList!");
            }

            //2. update filter bitmap
            @Override
            public void onInvalidate() {
                synchronized (FilterManager.this) {
                    mLoaded = STATE_LOADED;
                    dispatchInvalidate();
                }
                GLog.d(TAG, "loadFilters, invalidate!");
            }
        }));
        if (mCurrentFuture == null) {
            mLoaded = STATE_LOADED_FAILED;
            GLog.w(TAG, "startFilterJob, mCurrentFuture is null!");
        }
    }

    private void dispatchUpdateList(List<FilterEntry> list) {
        synchronized (FilterManager.this) {
            for (FilterLoaderCallback callback : mCallbackList) {
                if (callback != null) {
                    callback.onUpdateFilterList(list);
                }
            }
        }
    }

    private void dispatchInvalidate() {
        synchronized (FilterManager.this) {
            for (FilterLoaderCallback callback : mCallbackList) {
                if (callback != null) {
                    callback.onInvalidate();
                }
            }
        }
    }

    public void release() {
        if (mCurrentFuture != null) {
            mCurrentFuture.cancel();
        }
    }

    static class MicroBitmap implements AsyncLoader.Job<Bitmap> {

        private Bitmap mSrcBitmap;
        private EditorPhotoDataAdapter mAdapter;
        private int mBitmapSize;

        private MicroBitmap(Bitmap srcBitmap, int microBitmapSize) {
            if (srcBitmap != null) {
                mSrcBitmap = srcBitmap.copy(srcBitmap.getConfig(), srcBitmap.isMutable());
            }
            mBitmapSize = microBitmapSize;
        }

        private MicroBitmap(EditorPhotoDataAdapter adapter, int microBitmapSize) {
            mAdapter = adapter;
            mBitmapSize = microBitmapSize;
        }


        @Override
        public boolean onPrepare() {
            return (mSrcBitmap != null) || (mAdapter != null);
        }

        @Override
        public Bitmap onRun(AsyncLoader.JobContext jc) {
            Bitmap bitmap = null;
            if (mAdapter != null) {
                bitmap = mAdapter.getCurrentBitmapRef();
                if (bitmap != null) {
                    bitmap = BitmapUtils.resizeAndCropCenter(bitmap, mBitmapSize, false);
                } else {
                    GLog.w(TAG, "MicroBitmap, onRun load bitmap failed!");
                }
            } else {
                bitmap = BitmapUtils.resizeAndCropCenter(mSrcBitmap, mBitmapSize, false);
            }
            return bitmap;
        }

        @Override
        public boolean onRelease() {
            return false;
        }
    }
}
