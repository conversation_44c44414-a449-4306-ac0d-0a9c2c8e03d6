/***********************************************************
 ** Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - BeautyEntry.java
 ** Description:
 ** Version: 1.0
 ** Date : 2017/12/21
 ** Author: kexiuhua@Apps.Gallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>                 <data>     <version>  <desc>
 **  kexiuhua@Apps.Gallery3D  2017/12/21        build this module
 ****************************************************************/
package com.oplus.gallery.pictureeditorpage.base.processor.beauty;


import static com.oplus.gallery.photoeditor.editingvvm.adjustment.EffectConfig.ProgressType.TYPE_POSITIVE_100;

import com.arcsoft.livebroadcast.ArcSpotlightBeautyGPU;
import com.oplus.gallery.foundation.util.math.MathUtils;
import com.oplus.gallery.foundation.util.text.TextUtil;
import com.oplus.gallery.photoeditor.editingvvm.adjustment.EffectConfig;

import kotlin.Suppress;

public class BeautyEntry {

    public enum BeautyType {
        ONE_KEY_BEAUTY(null,
                LEVEL_AUTO,
                LEVEL_100,
                LEVEL_50,
                -1),
        ENLARGEMENT_EYE(ArcSpotlightBeautyGPU.BeautyFeatureGPU.EnlargementEye,
                0.0f,
                54f,
                30f,
                3),
        SKIN_SOFTEN(ArcSpotlightBeautyGPU.BeautyFeatureGPU.SkinSoften,
                0.0f,
                50f,
                35f,
                0),
        SKIN_BRIGHT(ArcSpotlightBeautyGPU.BeautyFeatureGPU.SkinBright,
                0.0f,
                60f,
                36f,
                1),
        DEPOUCH(ArcSpotlightBeautyGPU.BeautyFeatureGPU.Depouch,
                0.0f,
                100f,
                60f,
                -1),
        EYE_BRIGHT(ArcSpotlightBeautyGPU.BeautyFeatureGPU.EyeBright,
                EffectConfig.ValueType.TYPE_100.minValue(),
                EffectConfig.ValueType.TYPE_100.maxValue(),
                50f,
                4),
        SLENDER_FACE(ArcSpotlightBeautyGPU.BeautyFeatureGPU.SlenderFace,
                0.0f,
                65f,
                35f,
                2),
        TEETH_WHITE(ArcSpotlightBeautyGPU.BeautyFeatureGPU.TeethWhite,
                0.0f,
                86f,
                53f,
                -1),
        DEBLEMISH(ArcSpotlightBeautyGPU.BeautyFeatureGPU.Deblemish,
                EffectConfig.ValueType.TYPE_100.minValue(),
                EffectConfig.ValueType.TYPE_100.maxValue(),
                50f,
                5);

        private final ArcSpotlightBeautyGPU.BeautyFeatureGPU mBeautyFeature;
        private final float mMinValue;
        private final float mMaxValue;
        private final float mMiddleValue;
        private final int mNativeFeedbackIndex;

        BeautyType(ArcSpotlightBeautyGPU.BeautyFeatureGPU beautyFeature,
                   float minValue, float maxValue, float middleValue, int nativeFeedbackIndex) {
            mBeautyFeature = beautyFeature;
            mMinValue = minValue;
            mMaxValue = maxValue;
            mMiddleValue = middleValue;
            mNativeFeedbackIndex = nativeFeedbackIndex;
        }

        public ArcSpotlightBeautyGPU.BeautyFeatureGPU getBeautyFeature() {
            return mBeautyFeature;
        }

        public int getNativeFeedbackIndex() {
            return mNativeFeedbackIndex;
        }

    }

    public static final int DISABLE_PROGRESS = -1000;
    public static final int LEVEL_AUTO = 0;
    public static final int LEVEL_50 = 50;
    public static final int LEVEL_100 = 100;
    public static final float FLOAT_0 = 0.0f;
    public static final float FLOAT_100 = 100f;
    public static final int INVALID_ID = -1;
    private BeautyType mType = null;
    private final int mMinProgress;
    private final int mMaxProgress;
    private final int mMiddleProgress;
    private final int mDefaultProgress;
    /**
     * 美颜项item对应的顺序id
     */
    private int mOrder;
    /**
     * top tip提示文案key值
     */
    private String mHintTextKey = TextUtil.EMPTY_STRING;
    private float mOneKeyBeautyMaxProgress;
    /**
     * 是否是美颜单项
     */
    private boolean mIsSingleItem;
    private int mCurrentProgress;
    private int mCurrentLevel;
    private int mPrevLevel;
    private BeautyState mBeautyState;
    private int mIndex;//AI证件照，用来保存新顺序，效果同order

    /**
     *美颜类型，IPU美颜使用，与虹软美颜的BeautyType类型不同
     */
    private String mBeautyType;

    public boolean isModified() {
        return (mDefaultProgress != mCurrentProgress);
    }

    public void setPrevLevel(int preLevel) {
        mPrevLevel = preLevel;
    }

    public int getPrevLevel() {
        return mPrevLevel;
    }

    public boolean getIsSingleItem() {
        return mIsSingleItem;
    }

    public String getHintTextKey() {
        return mHintTextKey;
    }

    public enum BeautyState {
        ENABLE,
        DISABLE
    }

    /**
     * 根据BeautyType获取美颜项的顺序id
     * @param type 虹软的美颜类型
     * @return 美颜项的顺序id
     */
    public static int getOrderByBeautyType(BeautyType type) {
        int order = -1;
        if (type == BeautyType.ONE_KEY_BEAUTY) {
            order = EffectConfig.BEAUTY_ONE_KEY_BEAUTY;
        } else if (type == BeautyType.ENLARGEMENT_EYE) {
            order = EffectConfig.BEAUTY_ENLARGEMENT_EYE;
        } else if (type == BeautyType.SKIN_SOFTEN) {
            order = EffectConfig.BEAUTY_SKIN_SOFTEN;
        } else if (type == BeautyType.SKIN_BRIGHT) {
            order = EffectConfig.BEAUTY_SKIN_BRIGHT;
        } else if (type == BeautyType.DEPOUCH) {
            order = EffectConfig.BEAUTY_DEPOUCH;
        } else if (type == BeautyType.EYE_BRIGHT) {
            order = EffectConfig.BEAUTY_EYE_BRIGHT;
        } else if (type == BeautyType.SLENDER_FACE) {
            order = EffectConfig.BEAUTY_SLENDER_FACE;
        } else if (type == BeautyType.TEETH_WHITE) {
            order = EffectConfig.BEAUTY_TEETH_WHITE;
        } else if (type == BeautyType.DEBLEMISH) {
            order = EffectConfig.BEAUTY_DEBLEMISH;
        }
        return order;
    }


    public BeautyEntry(BeautyType type, int minProgress, int maxProgress, int middleProgress, int defaultProgress, float oneKeyBeautyMaxProgress) {
        mType = type;
        mOrder = getOrderByBeautyType(type);
        mMinProgress = minProgress;
        mMaxProgress = maxProgress;
        mMiddleProgress = middleProgress;
        mDefaultProgress = defaultProgress;
        mCurrentProgress = mDefaultProgress;
        mOneKeyBeautyMaxProgress = oneKeyBeautyMaxProgress;
        setPrevLevel(mDefaultProgress);
        mBeautyState = BeautyState.ENABLE;
        if (type == BeautyType.ONE_KEY_BEAUTY) {
            mIsSingleItem = false;
        } else {
            mIsSingleItem = true;
        }
    }

    @Suppress(names = "LongParameterList")
    public BeautyEntry(int order,
                       int minProgress,
                       int maxProgress,
                       int middleProgress,
                       String hintTextId,
                       boolean isSingleItem,
                       String type) {
        mOrder = order;
        if (isSingleItem) {
            mMinProgress = minProgress;
            mMaxProgress = maxProgress;
            mMiddleProgress = middleProgress;
        } else {
            mMinProgress = TYPE_POSITIVE_100.minProgress();
            mMaxProgress = TYPE_POSITIVE_100.maxProgress();
            mMiddleProgress = TYPE_POSITIVE_100.middleProgress();
        }
        mDefaultProgress = TYPE_POSITIVE_100.minProgress();
        mHintTextKey = hintTextId;
        mCurrentProgress = mDefaultProgress;
        mPrevLevel = mDefaultProgress;
        mBeautyState = BeautyState.ENABLE;
        mIsSingleItem = isSingleItem;
        mBeautyType = type;
    }

    public void setCurrentProgress(int progress) {
        mCurrentProgress = progress;
    }

    public int getCurrentProgress() {
        return mCurrentProgress;
    }

    public BeautyType getType() {
        return mType;
    }

    public String getBeautyType() {
        return mBeautyType;
    }

    public int updateLevel() {
        int progress = mCurrentProgress;
        boolean prev = progress < mMiddleProgress;
        int minP = prev ? mMinProgress : mMiddleProgress;
        int maxP = prev ? mMiddleProgress : mMaxProgress;
        float minV = EffectConfig.ValueType.TYPE_100.minValue();
        float maxV = EffectConfig.ValueType.TYPE_100.maxValue();
        if (mType != null) {
            minV = prev ? mType.mMinValue : mType.mMiddleValue;
            maxV = prev ? mType.mMiddleValue : mType.mMaxValue;
        }
        float value = MathUtils.getClampedMappingPercent(progress, minP, maxP);
        mCurrentLevel = (int) MathUtils.getLinearMappingValue(value, minV, maxV);
        return mCurrentLevel;
    }

    public int calculateLinerValue(int progress) {
        float percent = (progress / FLOAT_100);
        float maxValue = EffectConfig.ValueType.TYPE_100.maxValue();
        if (mType != null) {
            maxValue = mType.mMaxValue * (mOneKeyBeautyMaxProgress / LEVEL_100);
        }
        return (int) MathUtils.getLinearMappingValue(percent, FLOAT_0, maxValue);
    }

    public float getOneKeyBeautyMaxProgress() {
        return mOneKeyBeautyMaxProgress;
    }

    public int getCurrentLevel() {
        return mCurrentLevel;
    }

    public int getDefaultProgress() {
        return mDefaultProgress;
    }

    public int getDefaultValue() {
        return calculateLinerValue(mDefaultProgress);
    }

    public int getOrder() {
        return mOrder;
    }

    public int getIndex() {
        return mIndex;
    }

    public void setIndex(int index) {
        this.mIndex = index;
    }

    public boolean isActivated() {
        return ((mBeautyState != BeautyState.DISABLE) && isModified());
    }

    public void setBeautyState(BeautyState beautyState) {
        mBeautyState = beautyState;
    }

    public BeautyState getBeautyState() {
        return mBeautyState;
    }

    public void setCurrentLevel(int currentLevel) {
        mCurrentLevel = currentLevel;
    }

    public int getMiddleProgress() {
        return mMiddleProgress;
    }
    public int getMinProgress() {
        return mMinProgress;
    }
    public int getMaxProgress() {
        return mMaxProgress;
    }

    @Override
    public String toString() {
        return "BeautyEntry{"
                + "mType=" + mType
                + ", mOrder=" + mOrder
                + ", mMinProgress=" + mMinProgress
                + ", mMaxProgress=" + mMaxProgress
                + ", mOneKeyBeautyMaxProgress=" + mOneKeyBeautyMaxProgress
                + ", mMiddleProgress=" + mMiddleProgress
                + ", mCurrentProgress=" + mCurrentProgress
                + ", mDefaultProgress=" + mDefaultProgress
                + ", mHintTextKey=" + mHintTextKey
                + ", mCurrentLevel=" + mCurrentLevel
                + ", mPrevLevel=" + mPrevLevel
                + ", mBeautyState=" + mBeautyState
                + ", mIsSingleItem=" + mIsSingleItem + '}';
    }

    public BeautyEntry copy() {
        BeautyEntry entry = null;
        if (mType == null) {
            entry = new BeautyEntry(mOrder, mMinProgress, mMaxProgress, mMiddleProgress,
                    mHintTextKey, mIsSingleItem, mBeautyType);
        } else {
            entry = new BeautyEntry(mType, mMinProgress, mMaxProgress, mMiddleProgress,
                    mDefaultProgress, mOneKeyBeautyMaxProgress);
        }
        entry.setCurrentProgress(mCurrentProgress);
        entry.setCurrentLevel(mCurrentLevel);
        entry.setPrevLevel(mPrevLevel);
        entry.setBeautyState(mBeautyState);
        entry.setIndex(mIndex);
        return entry;
    }
}
