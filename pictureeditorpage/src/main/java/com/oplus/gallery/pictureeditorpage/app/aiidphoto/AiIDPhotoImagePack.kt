/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AiIDPhotoImagePack.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2022/12/28
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lima<PERSON>@Apps.Gallery              2022/12/28      1.0		   INIT
 *********************************************************************************/

package com.oplus.gallery.pictureeditorpage.app.aiidphoto

import android.graphics.Bitmap
import com.oplus.gallery.pictureeditorpage.frame.data.ImagePack
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.common.CropRect
import com.oplus.gallery.pictureeditorpage.base.processor.beauty.BeautyEntry
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoBackgroundEntry
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry

class AiIDPhotoImagePack(
    bitmap: Bitmap?,
    var photoSizeEntry: PhotoSizeEntry?,
    var photoBackgroundEntry: PhotoBackgroundEntry?,
    var photoBeautyEntry: PhotoBeautyEntry?,
    var cropRect: CropRect?,
    /**
     * 当前显示的实时效果图，每次添加完特效后更新，生命周期跟随AiIDPhotoImagePack，在AiIDPhotoSheet destroy时释放销毁
     * 与第一个参数bitmap区分，第一个参数bitmap为传入的原始资源图
     * 使用场景： 用于对比场景（不必重新走算法）
     */
    var currentEffectBitmap: Bitmap? = null
) : ImagePack(bitmap)

data class PhotoBeautyEntry(
    var beautyEntry: BeautyEntry?,
    var beautyEntryMap: MutableMap<BeautyEntry, Int>?,
    var centerPosition: Int = 0
)