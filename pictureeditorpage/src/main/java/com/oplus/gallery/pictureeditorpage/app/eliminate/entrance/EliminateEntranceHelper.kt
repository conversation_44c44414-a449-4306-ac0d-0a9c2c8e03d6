/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EliminateEntranceHelper.kt
 ** Description : 消除功能 入口 帮助类
 ** Version     : 1.0
 ** Date        : 2023/11/08
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                  <date>      <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2023/11/08     1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.eliminate.entrance

import android.content.Context
import android.util.Size
import android.graphics.RectF
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.aiunitdownload.EntranceListener
import com.oplus.gallery.business_lib.aiunitdownload.RefuseNextAction
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE

internal class EliminateEntranceHelper(private val context: Context) {
    private val isSupportAiEliminate by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE)
    }
    private val aiEliminateEntranceHelper by lazy {
        AIEliminateEntranceHelper(context)
    }
    private val normalEliminateEntranceHelper by lazy {
        NormalEliminateEntranceHelper(context)
    }

    fun start(imageSize: Size, displayRect: RectF, listener: EntranceListener) {
        if (isSupportAiEliminate) {
            aiEliminateEntranceHelper.setAiEliminateEntranceListener(object : EntranceListener {
                override fun onEnter() {
                    listener.onEnter()
                }

                override fun onRefuse(reason: RefuseNextAction?) {
                    when (reason) {
                        RefuseNextAction.ACTION_USE_MAGIC_ELIMINATE -> {
                            normalEliminateEntranceHelper.setNormalEliminateEntranceListener(listener)
                            normalEliminateEntranceHelper.checkNormalEliminateEntrance(imageSize)
                        }

                        else -> listener.onRefuse(reason)
                    }
                }

                override fun onRetryLoadSo() {
                    listener.onRetryLoadSo()
                }
            })
            aiEliminateEntranceHelper.checkAiEliminateEntrance(imageSize, displayRect)
        } else {
            normalEliminateEntranceHelper.setNormalEliminateEntranceListener(listener)
            normalEliminateEntranceHelper.checkNormalEliminateEntrance(imageSize)
        }
    }

    fun removePassersDownloadRequestIfNeed() {
        if (isSupportAiEliminate) {
            aiEliminateEntranceHelper.removePassersDownloadRequestIfNeed()
            aiEliminateEntranceHelper.setAiEliminateEntranceListener(null)
        }
    }
}
