package com.oplus.gallery.pictureeditorpage.app.enhance.common;

import static com.oplus.gallery.foundation.database.util.ConstantUtils.EQUAL_TO;
import static com.oplus.gallery.foundation.util.text.TextUtil.DOWN_LINE;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.ParcelFileDescriptor;

import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;

import com.oplus.gallery.business_lib.model.config.SaveConfig;
import com.oplus.gallery.business_lib.model.data.base.utils.Constants;
import com.oplus.gallery.business_lib.supertext.SuperTextExt;
import com.oplus.gallery.foundation.codec.supertext.SuperTextCodecFactory;
import com.oplus.gallery.foundation.codec.supertext.SuperTextOptions;
import com.oplus.gallery.foundation.codec.supertext.SuperTextProperties;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.dbaccess.convert.CursorConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.IDao;
import com.oplus.gallery.foundation.dbaccess.req.QueryReq;
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag;
import com.oplus.gallery.foundation.exif.raw.ExifUtils;
import com.oplus.gallery.foundation.exif.utils.ExifTagParser;
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment.StorageType;
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper;
import com.oplus.gallery.pictureeditorpage.frame.data.SaveFileTaskInfo;
import com.oplus.gallery.pictureeditorpage.frame.data.SourceBitmapProfile;
import com.oplus.gallery.pictureeditorpage.frame.processor.AsyncLoader;
import com.oplus.gallery.pictureeditorpage.frame.task.OriginPhotoSaveFileTask;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.util.io.IOUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;

import java.nio.charset.StandardCharsets;

public class EnhanceTextSaveFileTask extends OriginPhotoSaveFileTask {
    private static final String TAG = "EnhanceTextSaveFileTask";
    private static final String THUMB_GLOBAL_ID_KEY = "thumb_globalid";
    private static final String[] TAGFLAGS_PREFIXES = ContextGetter.context.getResources().getStringArray(
            com.oplus.gallery.basebiz.R.array.base_tagflags_prefixes);

    private final SuperTextImagePack mSuperTextImagePack;
    private final String mFilePath;

    public EnhanceTextSaveFileTask(SuperTextImagePack imagePack,
                                   String filePath,
                                   Context context,
                                   Uri sourceUri,
                                   String modelType,
                                   SaveJobCallback callback) {
        super(context, new SaveFileTaskInfo(new SourceBitmapProfile(sourceUri, imagePack.getColorSpace()),
                false, false, SaveConfig.COMPRESS_QUALITY, imagePack.getBitmap(), modelType), callback);
        mSuperTextImagePack = imagePack;
        mFilePath = filePath;
    }

    @SuppressLint("DefaultLocale")
    @Override
    public Bitmap onRun(AsyncLoader.JobContext jc) {
        if ((mSaveFolderName == null) || !mSaveFolderName.exists() || !mSaveFolderName.isDirectory()) {
            GLog.w(TAG, "onRun, mSaveFolderName error! " + mSaveFolderName);
            if (mCallback != null) {
                mCallback.onTip(SAVE_FAILED_ERROR);
            }
            return null;
        }

        Bitmap bitmap = (mBitmapRef != null) ? mBitmapRef.get() : null;

        if (bitmap == null) {
            GLog.w(TAG, "onRun, bitmap is null!");
            if (mCallback != null) {
                mCallback.onTip(SAVE_FAILED_ERROR);
            }
            return null;
        }
        int bitmapSize = bitmap.getAllocationByteCount();
        boolean isSpaceAvailable = StorageLimitHelper.hasEnoughStorageSpace(StorageType.PHONE_STORAGE, bitmapSize);
        if (!isSpaceAvailable) {
            GLog.d(TAG, String.format("onRun, bitmapSize: %d, bitmap.w: %d, bitmap.h: %d",
                    bitmapSize, bitmap.getWidth(), bitmap.getHeight()));
            if (mCallback != null) {
                mCallback.onTip(NO_SPACE);
            }
            return null;
        }
        /*
         * 超级文本的编码方式与Local HDR、人像景深、水印等文件扩展格式的编码方式不同
         * 需要在保存成超级文本的格式前，擦除目标文件中文件扩展信息（且之后不能迁移旧的扩展信息，会破坏超级文本的格式）
         */
        FileExtendedContainer.eraseExtensionData(mContext, mSourceUri);
        return saveBitmapToFile();
    }

    @Nullable
    private Bitmap saveBitmapToFile() {
        long time = System.currentTimeMillis();
        GTrace.traceBegin(TAG + ".saveBitmapToFile");
        File file = new File(mFilePath);
        boolean fileExist = file.exists();
        final long lastDateModified = file.lastModified();
        Bitmap effectBitmap = null;
        ExifUtils.ExifEntry exifData = null;
        ParcelFileDescriptor fileDescriptor = null;
        int tagFlags = Constants.CameraMode.FLAG_UNKNOWN;
        try {
            ContentResolver resolver = mContext.getContentResolver();
            fileDescriptor = resolver.openFileDescriptor(mSourceUri, "rw");
            if (fileDescriptor != null) {
                SuperTextProperties properties = mSuperTextImagePack.getProperties();
                int orientation = getOrientation(mSourceUri);
                effectBitmap = BitmapUtils.rotateBitmap(properties.getEffectBitmap(), -orientation, false);
                SuperTextExt.rotateCorrectVertex(-orientation, properties.getCorrectVertex());
                properties.setEffectBitmap(effectBitmap);

                exifData = ExifUtils.readExif(fileDescriptor.getFileDescriptor());
                if ((exifData != null) && (effectBitmap != null)) {
                    tagFlags = updateFileTagFlags(exifData, tagFlags);
                    exifData.setImageWidth(String.valueOf(effectBitmap.getWidth()));
                    exifData.setImageLength(String.valueOf(effectBitmap.getHeight()));
                }
                if (!properties.isSuperText()) {
                    properties.buildDataBlockInfo((int) file.getFile().length());
                }
                String filePath = file.getAbsolutePath();
                SuperTextCodecFactory.create(filePath, -1, true).encodeFile(fileDescriptor.getFileDescriptor(), new SuperTextOptions(properties));
                properties.setSuperText(true);
            }
        } catch (Exception e) {
            GLog.e(TAG, "saveBitmapToFile ", e);
            fileExist = false;
        } finally {
            IOUtils.closeQuietly(fileDescriptor);
            GLog.d(TAG, "saveBitmapToFile cost time = " + (System.currentTimeMillis() - time) + " ms");
            GTrace.traceEnd();
        }
        // 更新exif和数据库
        updateExifInfo(exifData);
        if (effectBitmap != null) {
            updateDB(file, lastDateModified, effectBitmap.getWidth(), effectBitmap.getHeight(), tagFlags);
        }
        callback(fileExist, effectBitmap);
        return null;
    }

    @VisibleForTesting
    public int updateFileTagFlags(ExifUtils.ExifEntry exifData, int tagFlags) {
        byte[] userCommentByteArray = exifData.getUserCommentByteArray();
        if (userCommentByteArray != null) {
            String userComment = new String(userCommentByteArray, StandardCharsets.US_ASCII).trim();
            int oldTagFlags = ExifTagParser.parseTagFlags(userComment, TAGFLAGS_PREFIXES);
            // 保存为超级文本编码格式后，抹除掉之前的扩展文件格式tagFlags（注意：人像模式不可进行超级文本编辑，按理人像模式不会走这个逻辑，但为了统一处理还是加上）
            int tagsToRemove = Constants.MultiCameraMode.EXTENDED_FILE_FLAGS;
            if ((oldTagFlags != OplusExifTag.INVALID) && userComment.contains(DOWN_LINE + oldTagFlags)) {
                tagFlags = oldTagFlags & (~tagsToRemove);
                // 添加为FLAG_ENHANCE_TEXT，当做是超级文本1.0格式
                tagFlags |= Constants.CameraMode.FLAG_ENHANCE_TEXT;
                // 这里用下划线+tagFlags数字来匹配，避免极端场景瘦身图哈希值跟tagFlags完全一样
                userComment = userComment.replace(DOWN_LINE + oldTagFlags, DOWN_LINE + tagFlags);
            }
            exifData.setUserComment(userComment);
        }
        return tagFlags;
    }

    private void callback(boolean isSuccess, Bitmap savedBitmap) {
        GLog.d(TAG, "callback isSuccess = " + isSuccess);
        if (isSuccess) {
            if (mCallback != null) {
                mCallback.onTip(SAVE_SUCCESS);
                mCallback.onComplete(mSourceUri, mItemPath, mAlbumPath, mFilePath, savedBitmap);
            }
        } else {
            if (mCallback != null) {
                mCallback.onTip(SAVE_FAILED_ERROR);
            }
        }
    }

    private void updateExifInfo(ExifUtils.ExifEntry exifData) {
        if ((mSourceUri == null) || (exifData == null)) {
            GLog.w(TAG, "updateExifInfo, mSourceUri or exifData is null, return");
            return;
        }
        ParcelFileDescriptor fileDescriptor = null;
        try {
            fileDescriptor = mContext.getContentResolver().openFileDescriptor(mSourceUri, "rw");
            if (fileDescriptor == null) {
                GLog.e(TAG, "updateDB fileDescriptor is null !");
                return;
            }
            ExifUtils.writeExif(fileDescriptor.getFileDescriptor(), exifData);
        } catch (Exception e) {
            GLog.e(TAG, "writeExif error:", e);
        } finally {
            // fileDescriptor.close can auto update MediaProvider in R
            IOUtils.closeQuietly(fileDescriptor);
        }
    }

    private int getOrientation(Uri uri) {
        long mediaId = ContentUris.parseId(uri);
        if (mediaId < 0) {
            GLog.e(TAG, "getOrientation mediaId < 0");
            return 0;
        }
        Cursor cursor = null;
        try {
            cursor = new QueryReq.Builder<Cursor>()
                    .setDaoType(IDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setProjection(new String[]{GalleryStore.GalleryColumns.LocalColumns.ORIENTATION})
                    .setWhere(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID + EQUAL_TO)
                    .setWhereArgs(new String[]{String.valueOf(mediaId)})
                    .setConvert(new CursorConvert())
                    .build().exec();
            if ((cursor != null) && cursor.moveToNext()) {
                int orientation = cursor.getInt(cursor.getColumnIndexOrThrow(GalleryStore.GalleryColumns.LocalColumns.ORIENTATION));
                GLog.d(TAG, "getOrientation orientation = " + orientation);
                return orientation;
            }
        } catch (Exception e) {
            GLog.e(TAG, "getOrientation", e);
        } finally {
            IOUtils.closeQuietly(cursor);
        }
        return 0;
    }
}