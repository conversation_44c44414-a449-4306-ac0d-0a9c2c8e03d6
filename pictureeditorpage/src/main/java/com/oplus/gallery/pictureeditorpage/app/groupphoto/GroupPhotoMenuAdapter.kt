/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - GroupPhotoMenuAdapter
 ** Description: 合影按钮列表适配器.
 ** Version: 1.0
 ** Date : 2023/12/19
 ** Author: <EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wenhao.Deng   2023/12/19      1.0        created
 ***************************************************************/
package com.oplus.gallery.pictureeditorpage.app.groupphoto

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import com.oplus.gallery.basebiz.R as BaseR
import com.oplus.gallery.basebiz.widget.EditorMenuItemView
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuAdapter
import com.oplus.gallery.business_lib.template.editor.adapter.EditorMenuItemBorderAnimViewHolder
import com.oplus.gallery.business_lib.template.editor.anim.EditorMenuItemBorderAnimation
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.photoeditor.R

/**
 * 合影按钮列表适配器
 */
class GroupPhotoMenuAdapter(context: Context?, data: List<EditorMenuItemViewData?>?) :
    EditorMenuAdapter<EditorMenuItemViewData?>(context, data) {
    private val unselectedAlpha = 0f
    private val selectedAlpha = Color.alpha(
        mContext.getColor(BaseR.color.base_editor_menu_item_out_border_stroke_color)
    ) * 1f / EditorUIConfig.MAX_ALPHA_VALUE
    private val defaultBgColor = mContext.getColor(BaseR.color.base_editor_menu_item_mask)

    private val enableColor = mContext.getColor(R.color.picture3d_group_photo_enable_color)

    private val disableColor = mContext.getColor(R.color.picture3d_group_photo_disable_color)

    /**
     * 当按钮置灰时功能按钮的默认背景色
     */
    private val cannotSelectColor = mContext.getColor(R.color.picture3d_group_photo_item_background_color_cannot_select)

    /**
     * 图标投影颜色
     */
    private val iconShadowColor = mContext.getColor(BaseR.color.picture_editor_group_photo_icon_shadow_color)

    /**
     * 图标边框描边颜色
     */
    private val roundBoardColor = mContext.getColor(BaseR.color.picture_editor_group_photo_round_board_color)

    /**
     * 功能按钮背景色为渐变色，通过drawable直接绘制
     */
    private val itemBackgroundDrawable = mContext.getDrawable(R.drawable.picture3d_editor_ic_group_photo_select_background)

    /**
     * 按钮在开启时的disable状态图
     */
    private val disableDrawable = mContext.getDrawable(R.drawable.picture3d_editor_ic_group_photo_select_disable_background)

    /**
     * 功能按钮在置灰是图标资源Id
     */
    private val itemIconId = R.drawable.picture3d_editor_ic_group_photo_subtract_can_not_kick

    override fun bindData(viewHolder: BaseRecycleViewHolder, position: Int, item: EditorMenuItemViewData) {
        super.bindData(viewHolder, position, item)
        val menuItemView = getMenuItemView(viewHolder)
        val menuTitleView = getNameTextView(viewHolder).apply {
            // 将菜单项设置为跑马灯效果
            isSingleLine = true
            ellipsize = TextUtils.TruncateAt.MARQUEE
            marqueeRepeatLimit = INVALID_VALUE
            isSelected = true
            setText(item.textId)
        }
        menuItemView.isDrawBorder = true
        menuItemView.forceDrawIconShadow = true
        menuItemView.selectedDrawFlag = (EditorMenuItemView.DRAW_BACKGROUND_COLOR
                or EditorMenuItemView.DRAW_CENTER_ICON
                or EditorMenuItemView.DRAW_CENTER_TEXT
                or EditorMenuItemView.DRAW_BACKGROUND_DRAWABLE)
        menuItemView.rounderBorderColor = roundBoardColor
        menuItemView.defaultShadowColor = iconShadowColor
        // 可点击时--白色；不可点击时--灰色
        var color = if (menuItemView.isEnabled) enableColor else disableColor
        if (item.isSelected) {
            if (item.isEnable) {
                // 之前item背景颜色为单一颜色，现在是渐变色，所以直接绘制drawable
                menuItemView.itemBackgroundDrawable = itemBackgroundDrawable
            } else {
                menuItemView.itemBackgroundDrawable = disableDrawable
            }
        } else {
            if (item.isEnable) {
                menuItemView.itemBackgroundColor = defaultBgColor
            } else {
                menuItemView.itemBackgroundColor = cannotSelectColor
            }
        }
        if (!item.isSelectable) {
            menuItemView.setIconResource(itemIconId)
            menuItemView.itemBackgroundColor = cannotSelectColor
            color = disableColor
        }
        menuTitleView.setTextColor(color)
    }

    /**
     * 设置人脸高清按钮置灰样式但是可以点击
     * @param isSupportExpressionOpt 是否支持闭眼修复，不支持的话菜单项只有一个超清按钮
     */
    fun updateFaceUltraButtonGrayOut(isSupportExpressionOpt: Boolean) {
        val itemIndex = if (isSupportExpressionOpt) {
            GPOptimizationOption.OPTION_RESOLUTION.pos
        } else {    // 不支持闭眼修复，则超分按钮的位置固定为0
            0
        }
        getMenuData(itemIndex)?.isSelectable = false
    }

    /**
     * 更新item的enable状态
     */
    fun setItemEnable(enable: Boolean) {
        data.forEach {
            it?.isEnable = enable
        }
    }

    override fun createViewHolder(itemView: View, viewType: Int): BaseRecycleViewHolder {
        val holder = EditorMenuItemBorderAnimViewHolder(
            itemView,
            EditorMenuItemBorderAnimation(),
            EditorPressAnimation()
        )
        setSupportPressAnim(holder)
        holder.setSelectedAnimEnable(true)
        holder.setSelectedAnimView(holder)
        holder.setDisableAlpha(unselectedAlpha)
        holder.setUnselectedAlpha(unselectedAlpha)
        holder.setSelectedAlpha(selectedAlpha)
        return holder
    }

    /**
     * 用户点击按钮时切换按钮状态、分发点击事件
     */
    override fun itemClickEvent(view: View?, position: Int, id: Int) {
        if (DoubleClickUtils.isFastDoubleClick()) {
            return
        }

        if (isInRange(mData, position).not()) {
            return
        }

        mData[position]?.let { item ->
            mItemClickListener?.onItemClick(view, position, item)
            if (item.isSelectable.not()) {
                return
            }
            // 切换select状态
            item.isSelected = item.isSelected.not()
            updateItemView(position)    // 切按钮样式
            GLog.d(TAG) { "[itemClickEvent] click item, item position=$position. selected=${item.isSelected}." }
            if (item.isSelected) {
                // 根据切换后的状态，分发点击事件
                mItemClickListener?.onItemSelected(view, position, item)
            } else {
                mItemClickListener?.onItemUnselected(view, position, item)
            }
        }
    }

    companion object {
        private const val TAG = "GroupPhotoMenuAdapter"
    }
}