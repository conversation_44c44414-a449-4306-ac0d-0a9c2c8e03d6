/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EditorAiIDPhotoBackgroundUIController.kt
 ** Description :
 **
 ** Version     : 1.0
 ** Date        : 2024/6/20
 ** Author      : W9002848 Pengcheng Lin
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9002848 Pengcheng Lin      2024/6/20     1.0        Add KFile header describe
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.aiidphoto.background

import android.content.Context
import android.content.res.Resources
import android.view.View
import android.view.ViewGroup
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig.isEditorLandscape
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter
import com.oplus.gallery.business_lib.template.editor.adapter.ImageMenuAdapter
import com.oplus.gallery.business_lib.template.editor.data.ImageViewData
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.EditorAiIDPhotoBaseState
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseUIController
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoBackgroundEntry
import com.oplus.gallery.pictureeditorpage.common.PictureEditTrackHelper
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.pictureeditorpage.PictureContext
import com.oplus.gallery.foundation.tracing.constant.PictureTrackConstant
import com.oplus.gallery.photoeditor.R

class EditorAiIDPhotoBackgroundUIController(
    context: PictureContext?,
    rootView: ViewGroup?,
    state: EditorBaseState?
) : EditorBaseUIController(context, rootView, state) {

    companion object {
        private const val TAG = "AiIDPhotoBackgroundUIController"
    }

    private var backgroundColors: ArrayList<PhotoBackgroundEntry>
    private lateinit var adapter: ImageMenuAdapter
    var onBackgroundChangeListener: OnBackgroundChangeListener? = null
    private var lastPosition = 0
    private var currentPosition = 0

    init {
        backgroundColors = initializeData(mContext)
        mReconfigureViewIds = listOf(
            R.id.toolbar_layout,
            R.id.color_list
        )
    }

    override fun getTitle(): Int {
        return R.string.picture3d_aiidphoto_background
    }

    override fun getToolbarLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return if (isEditorLandscape(config)) {
            R.layout.picture3d_editor_background_toolbar_landscape
        } else {
            R.layout.picture3d_editor_background_toolbar
        }
    }

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, layoutId, config)
        (mEditorState as? EditorAiIDPhotoBaseState)?.updateTipIconLayoutParams()
    }

    override fun onViewCreated() {
        mListView = mToolBarContainer.findViewById(R.id.color_list)
        mListView.keepLastFocusItem(true)
        mListView.setEnableAdjustToSuitableSpacing(true)
        val data = ArrayList<ImageViewData>()
        backgroundColors.forEach {
            data.add(
                ImageViewData(
                    viewId = Resources.ID_NULL,
                    iconResId = it.resourceId
                )
            )
        }
        adapter = ImageMenuAdapter(mContext, data)
        mListView.adapter = adapter
        adapter.select(0)
        adapter.setCanUnselectCurrentPosition(false)
        adapter.setItemClickListener(object : BaseRecyclerAdapter.OnItemClickListener<ImageViewData> {

            override fun onItemClick(view: View?, position: Int, item: ImageViewData?) {
                PictureEditTrackHelper.trackAiIDPhotoSubpageBtnClick(
                    PictureTrackConstant.Value.AIIDPHOTO_SUBPAGE_EDIT_PAGE_NAME_BACKGROUND,
                    PictureEditTrackHelper.getAiIDPhotoBackgroundValue(item!!.iconResId)
                )
            }

            override fun onItemSelected(view: View?, position: Int, item: ImageViewData?) {
                changeBackground(backgroundColors[position], position)
            }

            override fun onItemUnselected(view: View?, position: Int, item: ImageViewData?) {
                // do nothing
            }
        })
    }

    private fun changeBackground(entry: PhotoBackgroundEntry?, position: Int) {
        lastPosition = currentPosition
        currentPosition = position
        onBackgroundChangeListener?.onBackgroundChange(entry)
    }

    private fun initializeData(context: Context): ArrayList<PhotoBackgroundEntry> {
        val data = ArrayList<PhotoBackgroundEntry>()
        val icons = ResourceUtils.getResourceIdArrays(context, R.array.picture3d_editor_array_background_color_icon_array)
        for (icon in icons) {
            data.add(
                PhotoBackgroundEntry(
                    icon
                )
            )
        }
        return data
    }

    fun setSelected(photoBackgroundEntry: PhotoBackgroundEntry?) {
        for (i in 0 until backgroundColors.size) {
            if (backgroundColors[i].resourceId == photoBackgroundEntry?.resourceId) {
                lastPosition = i
                currentPosition = i
                adapter.select(i)
                mListView.scrollToPosition(i)
                break
            }
        }
    }

    fun revertSelection() {
        currentPosition = lastPosition
        adapter.select(currentPosition)
    }

    fun enableSelection(enable: Boolean) {
        updateDoneIconButtonClickable(enable)
    }

    fun getCurrentBackgroundEntry(): PhotoBackgroundEntry? {
        return backgroundColors[currentPosition]
    }

    interface OnBackgroundChangeListener {
        fun onBackgroundChange(entry: PhotoBackgroundEntry?)
    }
}