/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : VideoUtils.kt
 ** Description : 大图的VideoUtils
 ** Version     : 1.0
 ** Date        : 2020/09/20
 ** Author      : duchengsong@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** duchengsong@Apps.Gallery3D           2020/09/20   1.0         build this module
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.util;
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils;
import com.oplus.gallery.foundation.util.debug.GLog;

public final class VideoUtils {

    private static final String TAG = "VideoUtils";

    private static final String[] VIDEO_SUGGEST_ONLY_SINGLE_CODEC_PLATFORM = new String[]{
            "lito",  //SDM7250
            "holi",  //SDM4359
            "bengal", //SDM6115
    };

    private static boolean sOnlySupportSingleCodec = false;
    private static String sPlatformMessage;

    private static boolean sIsInited = false;

    private VideoUtils() {
    }

    public static void initSystemFeature() {
        sPlatformMessage = FeatureUtils.getBoardPlatform();
        GLog.v(TAG, " isMTKPlatform = " + FeatureUtils.isMTKPlatform()
                + ",current PlatformMessage = " + sPlatformMessage);
        sOnlySupportSingleCodec = supportSingleCodecCheck();

        sIsInited = true;
    }

    private static void checkInit() {
        if (!sIsInited) {
            initSystemFeature();
        }
    }

    private static boolean supportSingleCodecCheck() {
        for (String platform : VIDEO_SUGGEST_ONLY_SINGLE_CODEC_PLATFORM) {
            if (sPlatformMessage.equals(platform)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isOnlySupportSingleCodec() {
        checkInit();
        return sOnlySupportSingleCodec;
    }
}
