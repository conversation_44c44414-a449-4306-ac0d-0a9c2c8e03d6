/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - MagicEraserJob.java
 * * Description:
 * * Version: 1.0
 * * Date : 2017/12/04
 * * Author: kexiuhua@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>                 <data>     <version>  <desc>
 * *  kexiuhua@Apps.Gallery3D  2017/12/04        build this module
 ****************************************************************/
package com.oplus.gallery.pictureeditorpage.app.mosaic.task;

import static com.oplus.gallery.photoeditor.common.Config.Loader.PHOTO_MAX_LENGTH_MEDIUM;
import static com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.EliminatePath.ELIMINATE_LIMIT_SIZE;

import android.graphics.Bitmap;
import android.graphics.PointF;
import android.graphics.RectF;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.gallery.foundation.cache.memorycache.BitmapPools;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GProperty;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.foundation.util.graphic.BitmapCreator;
import com.oplus.gallery.foundation.util.graphic.BitmapUtils;
import com.oplus.gallery.foundation.util.graphic.ImageUtils;
import com.oplus.gallery.photoeditor.editingvvm.eliminate.engine.EliminatePath;
import com.oplus.gallery.pictureeditorpage.frame.processor.GLESLoader;
import com.oplus.gallery.photoeditor.gl.renderer.GLES20Canvas;
import com.oplus.gallery.photoeditor.gl.texure.Texture;
import com.oplus.gallery.pictureeditorpage.base.loader.UpdateTextureTask;
import com.oplus.gallery.pictureeditorpage.base.processor.effect.RenderEngine;

import java.util.List;
import java.util.Locale;

public class MagicEraserJob implements GLESLoader.GLJob<Texture> {
    private static final String TAG = "MagicEraserJob";
    private static final float BRUSH_SCALE_FACTOR = 0.6f;
    private Texture mTexture;
    private EliminatePath mEntry;
    private Bitmap mBitmap;
    private RectF mDrawBound = new RectF();

    public MagicEraserJob(Texture texture, Bitmap bitmap, EliminatePath eraserEntry) {
        mTexture = texture;
        mBitmap = bitmap;
        mEntry = eraserEntry;
    }

    @Override
    public boolean onPrepare(GLESLoader.GLJobContext jc) {
        if ((mTexture == null) || (mBitmap == null) || (mEntry == null)) {
            return false;
        }
        List<PointF> points = mEntry.getPoints();
        RectF drawBound = mEntry.getDrawingOutBound();
        if ((points == null) || points.isEmpty() || (drawBound == null) || drawBound.isEmpty()) {
            return false;
        }
        mDrawBound.set(drawBound);
        return true;
    }

    @Override
    public Texture onRun(GLESLoader.GLJobContext jc) {
        if ((jc == null) || jc.isCancelled()) {
            GLog.d(TAG, "onRun, cancelled!");
            return null;
        }
        GLES20Canvas canvas = jc.getGLCanvas();
        if (canvas == null) {
            GLog.w(TAG, "onRun, canvas is null!");
            return null;
        }
        long time = System.currentTimeMillis();
        if (!mTexture.isContentValid()) {
            GTrace.traceBegin("RenderJob.upload");
            mTexture.upload(canvas);
            GTrace.traceEnd();
        }
        int id = mTexture.getBackupId();
        if (id == GLES20Canvas.INVALID_TEXTURE) {
            GLog.w(TAG, "onRun, Texture Id is invalid!");
            return null;
        }
        int outputId = mTexture.getId();
        int width = mTexture.getWidth();
        int height = mTexture.getHeight();
        if (!mTexture.isTextureChanged()) {
            outputId = GLES20Canvas.genTexture(width, height);
            if (outputId == GLES20Canvas.INVALID_TEXTURE) {
                GLog.w(TAG, "onRun, genTexture id is invalid!");
                return null;
            }
            GLES20Canvas.copyTexture(id, outputId, width, height);
            GLog.d(TAG, String.format(Locale.ENGLISH, "onRun, create outputId: %d, width: %d, height: %d",
                outputId, width, height));
        }
        if (id == outputId) {
            GLog.w(TAG, "onRun, input texture equal output texture!");
            return null;
        }
        GTrace.traceBegin("RenderJob.effect");
        float radius = getStrokeWidth();
        createAFitTextureEraseBitmap(radius);
        mTexture.updateBitmap(mBitmap);
        // 拿到消除后的bitmap更新出去
        new UpdateTextureTask(mTexture, mBitmap).onRun(jc);
        GTrace.traceEnd();
        if (GProperty.getPHOTOEDIT_DEBUG()) {
            GLog.d(TAG, String.format(Locale.ENGLISH,
                "onRun, id: %d, outputId: %d, width: %d, height: %d, path.radius: %f,  job: %s, time: %s",
                id, outputId, width, height, radius, this, GLog.getTime(time)));
        }
        return mTexture;
    }

    @Override
    public boolean onRelease() {
        if (mTexture.isTextureChanged()) {
            long time = System.currentTimeMillis();
            GLog.d(TAG, "onRelease, time: " + GLog.getTime(time));
        }
        return false;
    }

    /**
     * 生成一个适合当前texture的擦除图片
     */
    private void createAFitTextureEraseBitmap(float radius) {
        //图片缩小至4k图流程，因为消除SDK限制最长边为4096，大于4096会报错，首先需要判断图片是否大于4K图，大于则以最长边为4096进行缩小，消除后在恢复原图宽高
        int bitmapLongSide = Math.max(mBitmap.getWidth(), mBitmap.getHeight());

        Bitmap scaledBitmap = mBitmap;
        if (bitmapLongSide > PHOTO_MAX_LENGTH_MEDIUM) {
            scaledBitmap = BitmapUtils.resizeByLongSide(mBitmap, new BitmapCreator() {
                @Nullable
                @Override
                public Bitmap create(int width, int height, @NonNull Bitmap.Config config) {
                    GLog.d(TAG, String.format(Locale.ENGLISH, "createAFitTextureEraseBitmap,magic width: %d, height: %d",
                        width, height));
                    return BitmapPools.getBitmap(width, height, config);
                }
            }, PHOTO_MAX_LENGTH_MEDIUM, false, false);
        }

        // 执行消除，当前使用消除算法为自研算法，传入的参数是原bitmap和坐标列，返回的是消除后的bitmap(原图)
        RenderEngine.magicEraserOneTime(scaledBitmap, mEntry, radius);

        if (bitmapLongSide > PHOTO_MAX_LENGTH_MEDIUM) {
            mBitmap = BitmapUtils.resizeByLongSide(scaledBitmap, new BitmapCreator() {
                @Nullable
                @Override
                public Bitmap create(int width, int height, @NonNull Bitmap.Config config) {
                    if (width == mBitmap.getWidth() && height == mBitmap.getHeight()) {
                        return mBitmap;
                    }
                    return null;
                }
            }, bitmapLongSide, true, false);
            if (mBitmap != scaledBitmap) {
                BitmapPools.recycle(scaledBitmap);
            }
        }
        Texture texture = mTexture;
        Bitmap bitmap = mBitmap;
        if ((texture == null) || (bitmap == null)) {
            GLog.e(TAG, LogFlag.DL, "createAFitTextureEraseBitmap, bitmap or texture is null.");
            return;
        }

        // 对图片进行scale处理，和泼辣处理输出保持一致
        int textureWidth = texture.getWidth();
        int textureHeight = texture.getHeight();
        int bitmapWidth = bitmap.getWidth();
        int bitmapHeight = bitmap.getHeight();
        // 如果返回照片大于当前texture，需要进行放缩
        if ((textureWidth < bitmapWidth) && (textureHeight < bitmapHeight)) {
            float suggestScale = ImageUtils.scaleImage(bitmapWidth, bitmapHeight,
                textureWidth, textureHeight,
                ImageUtils.SCALE_MODE_INSIDE);

            int suggestWidth = (int) (bitmapWidth * suggestScale);
            int suggestHeight = (int) (bitmapHeight * suggestScale);

            GLog.w(TAG, String.format(Locale.ENGLISH,
                "[onRun] bitmap is too large for canvas to upload, resize from (%d, %d) to (%d, %d)",
                textureWidth, textureHeight, suggestWidth, suggestHeight));
            mBitmap = Bitmap.createScaledBitmap(bitmap, suggestWidth, suggestHeight, true);
        }
    }

    private float getStrokeWidth() {
        int strokeWidth = mEntry.getSize().getStrokeWidth();
        int width = mTexture.getWidth();
        int height = mTexture.getHeight();
        float scale = Math.min(width / mDrawBound.width(), height / mDrawBound.height());
        float size = strokeWidth * scale * BRUSH_SCALE_FACTOR;
        size = Math.max(size, ELIMINATE_LIMIT_SIZE);
        return size;
    }
}
