/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.pictureeditorpage.state;

import static com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_ACTION_FLAG;
import static com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_GET_ALBUM;
import static com.oplus.gallery.basebiz.constants.IntentConstant.PicturePageConstant.KEY_GET_CONTENT;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.os.BatteryManager;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.Window;
import android.view.WindowManager;

import com.oplus.gallery.basebiz.uikit.activity.BaseActivity;
import com.oplus.gallery.business_lib.helper.FirstNotificationHelper;
import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.set.MediaSet;
import com.oplus.gallery.foundation.uikit.app.ZoomWindowManager;
import com.oplus.gallery.foundation.uikit.broadcast.bus.BroadcastDispatcher;
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.GTrace;
import com.oplus.gallery.foundation.util.systemcore.IntentUtils;
import com.oplus.gallery.pictureeditorpage.EditablePhotoPage;
import com.oplus.gallery.pictureeditorpage.PictureContext;
import com.oplus.gallery.pictureeditorpage.SelectionManager;
import com.oplus.gallery.pictureeditorpage.gl.actionbar.GLActionBarHelper;
import com.oplus.gallery.pictureeditorpage.gl.actionbar.IGLActionBarHelper;
import com.oplus.gallery.pictureeditorpage.gl.widget.GLActionBar;
import com.oplus.gallery.photoeditor.gl.widget.GLRoot;
import com.oplus.gallery.pictureeditorpage.gl.widget.GLSplitMenu;
import com.oplus.gallery.photoeditor.gl.widget.GLView;
import com.oplus.gallery.pictureeditorpage.splitmenu.SplitMenuManager;
import com.oplus.gallery.standard_lib.thread.ThreadPool;

import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Objects;

public abstract class ActivityState implements ZoomWindowManager.ZoomWindowState {
    public static final String KEY_MEDIA_SET_PATH = "media-set-path";
    public static final String KEY_SET_TITLE = "set-title";
    public static final String KEY_SEARCH_KEYWORDS = "search_keywords";
    public static final String KEY_SEARCH_TYPE = "search_type";
    public static final String KEY_FROM_SEARCH = "from-search";

    public static final String KEY_SPREAD_ANIMATION = "spread_animation";
    public static final String KEY_MEDIA_ITEM_PATH = "media-item-path";
    public static final String KEY_RETURN_INDEX_HINT = "return-index-hint";
    public static final String KEY_SHOW_BACK_TITLE = "show_back_title";
    public static final String KEY_SUPPORT_LOSS_LESS_CACHE = "support_loss_less_cache";
    // Page action flags
    public static final int ACTION_FLAG_STANDARD = 0;
    public static final int ACTION_FLAG_ACTION_MODE = 2;
    public static final int ACTION_FLAG_INDETERMINATE = -1;
    // Page request code
    public static final int REQUEST_SLIDESHOW = 1;

    public static final int MSG_UPDATE_OPTION_MENU = 2;

    protected static final int FLAG_HIDE_ACTION_BAR = 1 << 0;
    protected static final int FLAG_HIDE_STATUS_BAR = 1 << 1;
    protected static final int FLAG_SCREEN_ON_WHEN_PLUGGED = 1 << 2;
    protected static final int FLAG_SCREEN_ON_ALWAYS = 1 << 3;
    protected static final int FLAG_ORIENTATION_UNSPECIFIED = 1 << 5;

    private static final int SCREEN_ON_FLAGS = (WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    private static final String TAG = "ActivityState";
    private static final int STATE_INVALID = -1;
    private static final int STATE_SHOW = 0;
    private static final int STATE_HIDE = 1;
    private static int stateC;
    private static int stateActivityC;

    private static boolean sIsFinishPickAlbum = false;
    public boolean mIsFinishing = false;

    protected PictureContext mPictureContext;
    protected BaseActivity mActivity;
    protected Window mWindow;
    protected ResultEntry mReceivedResults;
    protected ResultEntry mResult;
    protected Handler mHandler;
    protected Bundle mData;
    protected int mActionFlags = ACTION_FLAG_INDETERMINATE;
    protected int mFlags;
    protected int mLoadingBits = 0;
    protected boolean mNoHomeGoBack;
    protected boolean mSyncedSuccess = false;
    // for search
    protected boolean mIsFromSearch = false;
    protected int mSearchType;
    protected ArrayList<CharSequence> mSearchKeywords = null;

    protected FirstNotificationHelper.OnDestroyListener mOnDestroyListener;

    protected static class ResultEntry {
        public int mRequestCode;
        public int mResultCode = Activity.RESULT_CANCELED;
        public Intent mResultData;
    }

    protected SelectionManager mLocalSelectionManager;
    protected SelectionManager mSelectionManager;
    protected boolean mHasSelectionModeChange = false;
    protected boolean mGetContent;
    protected boolean mGetAlbum;
    protected boolean mIsActive = false;
    protected MediaSet mMediaSet;
    //protected ActionModeHandler mActionModeHandler;
    // add by tangxl
    protected int mSupportShowAsAction;
    protected int mSupportShowAsActionBar = 0;

    // add by tangxl
    protected int mMenuId = -1;
    protected int mSplitMenuId = SplitMenuManager.INVALID_ID;
    protected CharSequence mBackTitle;
    protected CharSequence mTitle;
    protected boolean mIsShowBackTitle = false;

    private boolean mDestroyed = false;
    private boolean mPlugged = false;
    private boolean mIsResumed = false;
    private boolean mIsStarted = false;
    private int mMyActionBarState = STATE_INVALID;
    private ZoomWindowManager mZoomWindowManager;
    private CharSequence mPreTitle = null;
    private CharSequence mPreSubTitle = null;

    BroadcastReceiver mPowerIntentReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            if (Intent.ACTION_BATTERY_CHANGED.equals(action)) {
                boolean plugged = (0 != intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0));
                if (plugged != mPlugged) {
                    mPlugged = plugged;
                    setScreenOnFlags();
                }
            }
        }
    };

    protected ActivityState() {
    }

    void initialize(PictureContext pictureContext, Bundle data) {
        mPictureContext = pictureContext;
        mActivity = pictureContext.getActivity();
        mWindow = mActivity.getWindow();
        mData = data;
    }

    public Bundle getData() {
        return mData;
    }

    public MediaSet getMediaSet() {
        return mMediaSet;
    }

    protected void setContentPane(GLView content) {
        mPictureContext.getGLRoot().setContentPane(content);
    }

    public GLRoot getGLRoot() {
        return mPictureContext.getGLRoot();
    }

    public void onBackPressed() {
        mPictureContext.getStateManager().finishState(ActivityState.this);
    }

    protected void setStateResult(int resultCode, Intent data) {
        if (mResult == null) {
            return;
        }
        mResult.mResultCode = resultCode;
        mResult.mResultData = data;
    }

    protected void onConfigurationChanged(Configuration config) {
        // do nothing
    }

    protected void onSaveState(Bundle outState) {
        // do nothing
    }

    protected void onStateResult(int requestCode, int resultCode, Intent data) {
        // do nothing
    }

    protected void onRequestPermissionResult(int requestCode, String[] permissions, int[] grantResults) {
        // do nothing
    }

    protected void setLoadingBit(int loadTaskBit) {
        mLoadingBits |= loadTaskBit;
    }

    protected void clearLoadingBit(int loadTaskBit) {
        mLoadingBits &= ~loadTaskBit;
    }

    public void onSelectionChange(Path path, boolean selected) {
        if ((mSelectionManager == null) || !mSelectionManager.inSelectionMode()) {
            return;
        }
        updateSelectTitle();
        updateActionBar();
    }

    protected void updateActionBar() {
        GLog.v(TAG, "updateActionBar():" + isUpdateMenuEnable());
        if (!isUpdateMenuEnable()) {
            return;
        }
        mPictureContext.invalidateGLOptionMenu();
    }

    protected int updateSelectTitle() {
        return 0;
    }

    void create(Bundle data, Bundle storedState) {
        stateC++;
        mActionFlags = data.getInt(KEY_ACTION_FLAG, ACTION_FLAG_STANDARD);
        // for search
        mSearchType = data.getInt(KEY_SEARCH_TYPE);
        mIsFromSearch = data.getBoolean(KEY_FROM_SEARCH, false);
        mSearchKeywords = data.getCharSequenceArrayList(KEY_SEARCH_KEYWORDS);
        GLog.v(TAG, "create\t stateC = " + stateC + ", mIsFromSearch = " + mIsFromSearch);
        onCreate(data, storedState);
    }

    protected void updateActionNavigationMode() {
        GTrace.traceBegin(TAG + ".updateActionNavigationMode");
        IGLActionBarHelper glActionBarHelper = mPictureContext.getGLActionBarHelper();
        if (glActionBarHelper.isInSelectedMode()) {
            return;
        }
        switch (mActionFlags) {
            case ACTION_FLAG_STANDARD:
                GLog.v(TAG, "[updateActionNavigationMode] mActionFlags is ACTION_FLAG_STANDARD");
                glActionBarHelper.setGLDisplayOptions(true, true);
                setBackTitle();
                break;
            case ACTION_FLAG_ACTION_MODE:
                GLog.v(TAG, "[updateActionNavigationMode] mActionFlags is ACTION_FLAG_ACTION_MODE");
                glActionBarHelper.setGLDisplayOptions(false, true);
                break;
            default:
                GLog.v(TAG, "[updateActionNavigationMode] mActionFlags is " + mActionFlags);
                break;
        }
        GTrace.traceEnd();
    }

    protected void onCreate(Bundle data, Bundle storedState) {
        mGetContent = data.getBoolean(KEY_GET_CONTENT, false);
        mGetAlbum = data.getBoolean(KEY_GET_ALBUM, false);
        mGetContent = false;
        mGetAlbum = false;
        mTitle = data.getString(KEY_SET_TITLE);
        mBackTitle = data.getCharSequence(IntentUtils.NAVIGATE_UP_TITLE_TEXT);
        mIsShowBackTitle = data.getBoolean(EditablePhotoPage.KEY_SHOW_BACK_TITLE);
        mZoomWindowManager = mPictureContext.requireZoomWindowManager();
        if (null != mZoomWindowManager) {
            mZoomWindowManager.registerZoomWindowObserver(this);
        }
    }

    void setScreenOnFlags() {
        GTrace.traceBegin(TAG + ".setScreenOnFlags");
        final WindowManager.LayoutParams params = mWindow.getAttributes();
        if ((0 != (mFlags & FLAG_SCREEN_ON_ALWAYS))
                || (mPlugged && 0 != (mFlags & FLAG_SCREEN_ON_WHEN_PLUGGED))) {
            params.flags |= SCREEN_ON_FLAGS;
        } else {
            params.flags &= ~SCREEN_ON_FLAGS;
        }
        mWindow.setAttributes(params);
        GTrace.traceEnd();
    }

    void pause() {
        if (!mIsResumed) {
            return;
        }
        mIsResumed = false;
        stateActivityC--;
        GLog.v(TAG, "pause\t stateActivityC = " + stateActivityC + ", " + this);
        saveMyActionBarState();
        if (0 != (mFlags & FLAG_SCREEN_ON_WHEN_PLUGGED)) {
            BroadcastDispatcher.INSTANCE.unregisterReceiver(mActivity, mPowerIntentReceiver);
        }
        onPause();
    }

    void start() {
        if (mIsStarted) {
            return;
        }

        mIsStarted = true;
        onStart();
    }

    void stop() {
        if (!mIsStarted) {
            return;
        }
        mIsStarted = false;
        onStop();
    }

    protected void onActivityResume() {
        // do nothing
    }

    protected void onActivityPause() {
        // do nothing
    }

    protected void focusChange(boolean hasFocus) {
        // do nothing
    }

    protected void onStop() {
        // do nothing
    }

    protected void onStart() {
        // do nothing
    }

    protected void onPause() {
        // do nothing
    }

    protected void updateOrientation() {
        if ((mFlags & FLAG_ORIENTATION_UNSPECIFIED) != 0) {
            mActivity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED);
        } else {
            mActivity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    private void saveMyActionBarState() {
        IGLActionBarHelper glActionBarHelper = mPictureContext.getGLActionBarHelper();
        if (glActionBarHelper == null) {
            mMyActionBarState = STATE_INVALID;
            return;
        }

        if (glActionBarHelper.getVisibility() == GLView.VISIBLE) {
            mMyActionBarState = STATE_SHOW;
        } else {
            mMyActionBarState = STATE_HIDE;
        }
    }

    private void updateMyActionBarState() {
        GTrace.traceBegin(TAG + ".updateMyActionBarState");
        IGLActionBarHelper glActionBarHelper = mPictureContext.getGLActionBarHelper();
        if (glActionBarHelper == null || !isUpdateMenuEnable()) {
            return;
        }

        switch (mMyActionBarState) {
            case STATE_INVALID:
                if ((mFlags & FLAG_HIDE_ACTION_BAR) == 0) {
                    glActionBarHelper.setVisibility(GLView.VISIBLE);
                    mActivity.showStatusBar();
                } else {
                    glActionBarHelper.setVisibility(GLView.INVISIBLE);
                    mActivity.hideStatusBar();
                }
                break;

            case STATE_SHOW:
                glActionBarHelper.setVisibility(GLView.VISIBLE);
                mActivity.showStatusBar();
                break;

            case STATE_HIDE:
                glActionBarHelper.setVisibility(GLView.INVISIBLE);
                mActivity.hideStatusBar();
                break;

            default:
                throw new IllegalArgumentException("error tab visibility state!");
        }
        GTrace.traceEnd();
    }

    public boolean isResumed() {
        return mIsResumed;
    }

    // should only be called by StateManager
    void resume() {
        resume(true);
    }

    /**
     * Resume activity state
     *
     * @param isForeground Whether the activity state is truly foreground when resume.
     */
    void resume(boolean isForeground) {
        if (mIsResumed) {
            onWindowFocusChanged(isForeground);
            return;
        }
        ResultEntry entry = mReceivedResults;
        if (entry != null) {
            mReceivedResults = null;
            ActivityState oldTopState = mPictureContext.getStateManager().getTopState();
            onStateResult(entry.mRequestCode, entry.mResultCode, entry.mResultData);
            //in onStateResult, may be will create a new ActivityState and resume it,
            //so after onStateResult we need check whether the oldTopState is top state,
            //if not, do not need resume the oldTopState
            if (!mPictureContext.getStateManager().isTopState(oldTopState)) {
                return;
            }
        }

        mIsResumed = true;
        stateActivityC++;
        GLog.v(TAG, "resume\t stateActivityC = " + stateActivityC + ", " + this);
        if (isUpdateMenuEnable()) {
            updateActionNavigationMode();
            updateMyActionBarState();
        }

        if (!((this instanceof EditablePhotoPage))) {
            updateOrientation();
        }

        setScreenOnFlags();
        boolean lightsOut = ((mFlags & FLAG_HIDE_STATUS_BAR) != 0);
        mPictureContext.getGLRoot().setLightsOutMode(lightsOut, false);
        if (0 != (mFlags & FLAG_SCREEN_ON_WHEN_PLUGGED)) {
            // we need to know whether the device is plugged in to do this
            // correctly
            final IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_BATTERY_CHANGED);
            BroadcastDispatcher.INSTANCE.registerReceiver(mActivity, mPowerIntentReceiver, filter);
        }

        onResume();
        mPictureContext.invalidateGLOptionMenu();
        // the transition store should be cleared after resume;
        mPictureContext.getTransitionStore().clear();
        if (isForeground) {
            onWindowFocusChanged(isForeground);
        }
    }

    // a subclass of ActivityState should override the method to resume itself
    protected void onResume() {
    }

    protected boolean onCreateGLOptionsMenu(GLActionBar glActionBar, GLSplitMenu glSplitMenu) {
        IGLActionBarHelper glActionBarHelper = mPictureContext.getGLActionBarHelper();
        glActionBarHelper.resetBackTitleText();
        switch (mActionFlags) {
            case ACTION_FLAG_STANDARD:
                glActionBarHelper.setGLDisplayOptions(!mNoHomeGoBack, true);
                setBackTitle();
                break;
            default:
                break;
        }
        return true;
    }

    protected boolean onItemSelected(MenuItem item) {
        return false;
    }

    protected boolean onItemSelected(GLView item) {
        return false;
    }

    protected boolean onItemLongPress(GLView item) {
        return false;
    }

    void destroy() {
        if (mDestroyed) {
            return;
        }
        stateC--;
        GLog.v(TAG, "destroy\t stateC = " + stateC);
        mDestroyed = true;
        if (null != mZoomWindowManager) {
            mZoomWindowManager.unregisterZoomWindowObserver(this);
        }
        onDestroy();
    }

    protected void onDestroy() {

    }

    boolean isDestroyed() {
        return mDestroyed;
    }

    public boolean isFinishing() {
        return mIsFinishing;
    }

    // add by LiuJunChao 2013-11-26 for: ModifyList.SHOW_STATE_BAR_OVERLAY
    public void onWindowFocusChanged(boolean hasFocus) {

    }

    @Override
    public void onFloatingWindowStateChanged(boolean isFloatingWindowMode) {

    }

    protected boolean isUpdateMenuEnable() {
        return mPictureContext.getStateManager().isUpdateMenuEnable(this);
    }

    protected void setLowMemory(boolean b) {

    }

    protected void setTitle(int resId) {
        setTitle(mPictureContext.getActivity().getString(resId));
    }

    protected void setTitle(CharSequence title) {
        if (Objects.equals(mPreTitle, title)) {
            return;
        }
        if (mPictureContext.getGLActionBarHelper() != null) {
            mPictureContext.getGLActionBarHelper().setTitle(title);
        }
    }

    protected void setTitle(CharSequence title, ActivityState state) {
        if (Objects.equals(mPreTitle, title)) {
            return;
        }
        if (mPictureContext.getStateManager() != null && mPictureContext.getStateManager().isTopState(state)) {
            setTitle(title);
        }
    }

    protected void setSubTitle(CharSequence subTitle) {
        if (Objects.equals(mPreSubTitle, subTitle)) {
            return;
        }
        if (mPictureContext.getGLActionBarHelper() != null) {
            mPictureContext.getGLActionBarHelper().setSubTitle(subTitle);
        }
    }

    protected void setSubTitle(CharSequence title, ActivityState state) {
        if (Objects.equals(mPreSubTitle, title)) {
            return;
        }
        if (mPictureContext.getStateManager() != null && mPictureContext.getStateManager().isTopState(state)) {
            setSubTitle(title);
        }
    }

    public CharSequence getTitle() {
        IGLActionBarHelper glActionBarHelper = mPictureContext.getGLActionBarHelper();
        if (glActionBarHelper != null) {
            return glActionBarHelper.getTitle();
        }
        return null;
    }

    private void setBackTitle() {
        if (mNoHomeGoBack) {
            return;
        }
        Intent intent = mActivity.getIntent();
        String action = intent.getAction();
        IGLActionBarHelper helper = mPictureContext.getGLActionBarHelper();
        if (helper != null) {
            if (!TextUtils.isEmpty(mBackTitle)) {
                helper.setBackTitle(mBackTitle);
            } else {
                if ((mBackTitle == null) && (action != Intent.ACTION_MAIN) && !mIsShowBackTitle) {
                    String extraBackTitle = GLActionBarHelper.getContentDescriptonById(mActivity, intent);
                    if (TextUtils.isEmpty(extraBackTitle)) {
                        helper.setGLDisplayOptions(false, true);
                    } else {
                        helper.setBackTitle(extraBackTitle);
                    }
                } else {
                    helper.setBackTitle(com.oplus.gallery.basebiz.R.string.common_back);
                }
            }
        }
    }

    public boolean isFaceMode() {
        return false;
    }

    public void dump(String prefix, FileDescriptor fd, PrintWriter writer, String[] args) {
    }

    public void setNavigationBarColor(int color) {
        if (mPictureContext != null) {
            StateManager stateManager = mPictureContext.getStateManager();
            if ((stateManager != null) && stateManager.isTopState(this)) {
                mActivity.setNaviBarColor(color);
            } else {
                GLog.w(TAG, "setNavigationBarColor, is not top state: " + this);
            }
        }
    }

    protected void finish() {
        if (mPictureContext != null) {
            StateManager stateManager = mPictureContext.getStateManager();
            if (stateManager != null) {
                stateManager.finishState(this);
            } else {
                GLog.w(TAG, "finish. state manager is null!");
            }
        } else {
            GLog.w(TAG, "finish, mActivity is null!");
        }
    }

    public static void setIsFinishPickAlbum(boolean isFinish) {
        sIsFinishPickAlbum = isFinish;
    }

    public boolean getIsFinishPickAlbum() {
        return sIsFinishPickAlbum;
    }

    /**
     * Return true if need GLThread keep running when onPause coming,
     * or GLThread will be suspend after activity pausing
     */
    protected boolean isKeepSurfaceOnWhenPaused() {
        return false;
    }

    public boolean needKeepColorGamut() {
        return false;
    }

    public class UpdateSelectedStateJob implements ThreadPool.Job<Void> {
        @Override
        public Void run(ThreadPool.JobContext jc) {
            return updateSelectedState();
        }

        private Void updateSelectedState() {
            if (mSelectionManager != null) {
                mSelectionManager.updateClickedSet();
                mSelectionManager.updateClickedPosition();
                mHandler.sendEmptyMessage(MSG_UPDATE_OPTION_MENU);
            }
            return null;
        }
    }

    protected String getUserActionCurrentPageValue() {
        return null;
    }

    protected void onAppUiStateChanged(AppUiResponder.AppUiConfig config) {

    }
}