/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - BaseRecycleAdapter.java
 * * Description: BaseRecycleAdapter for ColorRecycleView.
 * * Version: 1.0
 * * Date : 2017/11/04
 * * Author: xinyang.Hu@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  xinyang.Hu@Apps.Gallery3D       2017/11/04    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.pictureeditorpage.common.adapter;

import android.content.Context;
import android.util.SparseIntArray;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.COUIRecyclerView;

import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder;
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolderKt;
import com.oplus.gallery.business_lib.template.editor.adapter.EditorAnimViewHolder;
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation;
import com.oplus.gallery.business_lib.template.editor.anim.IPressAnimation;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.photoeditor.common.Config;
import com.oplus.gallery.standard_lib.ui.util.ClickUtil;

import java.util.List;

@Deprecated
public abstract class BaseEditRecycleAdapter<T> extends COUIRecyclerView.Adapter<BaseRecycleViewHolder> {
    public static final int INVALID_VALUE = -1;
    protected boolean mIsFastClickEnabled = false;
    protected int mLastClickViewId = View.NO_ID;
    protected int mCurrentSelection = INVALID_VALUE;
    protected int mCurrentSelectedViewId = INVALID_VALUE;
    protected List<T> mData;
    protected Context mContext;
    protected LayoutInflater mInflater;
    protected OnItemClickListener<T> mItemClickListener;
    private static final String TAG = "BaseEditRecycleAdapter";
    private boolean mIsEnable = true;
    private boolean mIsClickable = true;
    private SparseIntArray mViewIdMap = new SparseIntArray();

    public BaseEditRecycleAdapter(Context context, List<T> data) {
        mContext = context;
        mInflater = LayoutInflater.from(context);
        mData = data;
    }

    public BaseRecycleViewHolder createViewHolder(View itemView, int viewType) {
        EditorAnimViewHolder holder = new EditorAnimViewHolder(itemView, null, new EditorPressAnimation());
        setSupportPressAnim(holder);
        return holder;
    }

    final protected void setSupportPressAnim(BaseRecycleViewHolder holder) {
        View itemView = holder.itemView;
        if (holder instanceof IPressAnimation) {
            ((IPressAnimation) holder).setPressAnimView(getAnimItemView(itemView));
            itemView.setOnTouchListener((view, motionEvent) -> {
                switch (motionEvent.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        ((IPressAnimation) holder).doDownPressAnim();
                        break;
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        ((IPressAnimation) holder).doUpPressAnim();
                        break;
                    default:
                        break;
                }
                return false;
            });
        } else {
            GLog.d(TAG, "setSupportPressAnim: holder need implement interface IPressAnimation");
        }
    }

    final protected View getAnimItemView(View itemView) {
        View animView = itemView;
        if (itemView instanceof ViewGroup) {
            View view = itemView.findViewById(com.oplus.gallery.basebiz.R.id.base_editorMenuItem_item_icon);
            if (view != null) {
                animView = view;
            }
        }
        return animView;
    }

    @Override
    public BaseRecycleViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = mInflater.inflate(getItemLayoutId(viewType), parent, false);
        final BaseRecycleViewHolder viewHolder = createViewHolder(itemView, viewType);
        if (mItemClickListener != null) {
            viewHolder.itemView.setOnClickListener(view -> {
                final int viewId = view.getId();
                if (checkClickEvent(viewId)) {
                    return;
                }
                if (Config.GLog.DEBUG_CLICK_MENU) {
                    GLog.d(TAG, "onClick, viewId: " + viewId);
                }
                mLastClickViewId = viewId;
                int position = viewHolder.getLayoutPosition();
                int id = view.getId();
                itemClickEvent(view, position, id);
            });
        }
        return viewHolder;
    }

    @Override
    public void onViewRecycled(@NonNull BaseRecycleViewHolder holder) {
        super.onViewRecycled(holder);
        BaseRecycleViewHolderKt.onViewRecycled(holder);
    }

    protected void itemClickEvent(View view, int position, int id) {
        T item = mData.get(position);
        if (item != null) {
            int lastClickPosition = mCurrentSelection;
            mItemClickListener.onItemClick(view, position, item);
            if (mCurrentSelection != position) {
                mCurrentSelection = position;
                mItemClickListener.onItemSelected(view, position, item);
            } else if (supportUnselected()) {
                mItemClickListener.onItemUnSelected(mCurrentSelectedViewId);
                mCurrentSelection = INVALID_VALUE;
                mCurrentSelectedViewId = INVALID_VALUE;
            } else {
                mCurrentSelection = position;
            }
            mCurrentSelectedViewId = id;
            if (lastClickPosition != INVALID_VALUE) {
                notifyItemChanged(lastClickPosition);
            }
            if (mCurrentSelection != INVALID_VALUE) {
                notifyItemChanged(mCurrentSelection);
            }
        }
    }

    protected boolean checkClickEvent(int viewId) {
        if ((!mIsFastClickEnabled && !ClickUtil.clickable()) && (viewId != View.NO_ID) && (viewId == mLastClickViewId)) {
            if (Config.GLog.DEBUG_CLICK_MENU) {
                GLog.d(TAG, "onClick, viewId: " + viewId + ", mLastClickViewId: " + mLastClickViewId
                    + ", mIsFastClickEnabled: " + mIsFastClickEnabled);
            } else {
                GLog.d(TAG, "onClick, fast click or repeat click, ignore this one!");
            }
            return true;
        }
        return false;
    }

    @Override
    public void onBindViewHolder(BaseRecycleViewHolder holder, int position) {
        bindData(holder, position, mData.get(position));
        mViewIdMap.put(holder.itemView.getId(), position);
        holder.itemView.setSelected((position == mCurrentSelection) && isSelectable(holder));
        setItemEnable(holder.itemView, mIsEnable);
        setItemClickable(holder.itemView, mIsClickable);
    }

    @Override
    public int getItemCount() {
        return (mData != null) ? mData.size() : 0;
    }

    public void setData(List<T> data) {
        mData = data;
        notifyDataSetChanged();
    }

    public void setSelection(int position) {
        int lastPosition = mCurrentSelection;
        mCurrentSelection = position;
        if (lastPosition != INVALID_VALUE) {
            notifyItemChanged(lastPosition);
        }
        if (position != INVALID_VALUE) {
            notifyItemChanged(position);
        }
    }

    public void cancelSelection(int position) {
        // Text adapter which has multiple selected button will need this method.
    }

    public final int getSelection() {
        return mCurrentSelection;
    }

    public void clearSelection() {
        mCurrentSelectedViewId = INVALID_VALUE;
        mCurrentSelection = INVALID_VALUE;
        notifyDataSetChanged();
    }

    public List<T> getData() {
        return mData;
    }

    protected T getMenuData(int index) {
        if (isInRange(mData, index)) {
            return mData.get(index);
        } else {
            GLog.d(TAG, "getMenuData IndexOutOfBounds");
            return null;
        }
    }

    public <T> boolean isInRange(List<T> list, int position) {
        return (list != null) && (position >= 0) && (position < list.size());
    }

    /**
     * 列表中，item的layout布局
     * @param viewType item类型
     * @return 返回布局xml的id
     */
    public abstract int getItemLayoutId(int viewType);

    /**
     * onBindViewHolder->bindData,更新数据时调用
     * @param viewHolder item的viewHolder
     * @param position item对应的索引
     * @param item 数据
     */
    public abstract void bindData(BaseRecycleViewHolder viewHolder, int position, T item);

    public abstract boolean supportUnselected();

    /**
     * item 是否可选的 ，返回false不可选
     * @param viewHolder item的viewHolder
     * @return false:表示item不可选中 true:表示可选
     */
    public abstract boolean isSelectable(BaseRecycleViewHolder viewHolder);

    public void setEnable(boolean enable) {
        if (mIsEnable != enable) {
            mIsEnable = enable;
            notifyDataSetChanged();
        }
    }

    public void setClickable(boolean clickable) {
        if (mIsClickable != clickable) {
            mIsClickable = clickable;
            notifyDataSetChanged();
        }
    }

    public void setFastClickEnabled(boolean enabled) {
        mIsFastClickEnabled = enabled;
    }

    private void setItemEnable(View item, boolean enable) {
        if (item != null) {
            item.setEnabled(enable);
            setChildrenEnable(item, enable);
        }
    }

    private void setItemClickable(View item, boolean clickable) {
        if (item != null) {
            item.setClickable(clickable);
            setChildrenClickable(item, clickable);
        }
    }

    private void setChildrenEnable(View view, boolean enable) {
        if (view == null) {
            return;
        }
        if (view.isEnabled() == enable) {
            return;
        }
        if (view instanceof ViewGroup) {
            int childCount = ((ViewGroup) view).getChildCount();
            for (int i = 0; i < childCount; i++) {
                final View child = ((ViewGroup) view).getChildAt(i);
                child.setEnabled(enable);
                setChildrenEnable(child, enable);
            }
        } else {
            view.setEnabled(enable);
        }
    }

    private void setChildrenClickable(View view, boolean clickable) {
        if (view == null) {
            return;
        }
        if (view.isClickable() == clickable) {
            return;
        }
        if (view instanceof ViewGroup) {
            int childCount = ((ViewGroup) view).getChildCount();
            for (int i = 0; i < childCount; i++) {
                final View child = ((ViewGroup) view).getChildAt(i);
                child.setClickable(clickable);
                setChildrenClickable(child, clickable);
            }
        } else {
            view.setClickable(clickable);
        }
    }

    public void setItemClickListener(OnItemClickListener<T> listener) {
        mItemClickListener = listener;
    }

    public interface OnItemClickListener<T> {
        void onItemClick(View view, int position, T item);

        void onItemSelected(View view, int position, T item);

        void onItemUnSelected(int viewId);
    }
}
