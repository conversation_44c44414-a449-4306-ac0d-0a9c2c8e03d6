/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EditorAiIDPhotoBaseState.kt
 ** Description :
 **
 ** Version     : 1.0
 ** Date        : 2024/6/20
 ** Author      : W9002848 Pengcheng Lin
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9002848 Pengcheng Lin      2024/6/20     1.0        Add KFile header describe
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.aiidphoto

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.common.AiIDPhotoManager
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.common.AiIDPhotoPreviewManager
import com.oplus.gallery.photoeditor.base.PhotoEditorView
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState
import com.oplus.gallery.pictureeditorpage.frame.PhotoEditorUIScheme
import com.oplus.gallery.pictureeditorpage.PictureContext
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.common.AiIDPhotoBeautyManager

abstract class EditorAiIDPhotoBaseState(
    editorType: String?,
    pictureContext: PictureContext?,
    rootView: ViewGroup?,
    photoEditorView: PhotoEditorView?
) :
    EditorBaseState(editorType, pictureContext, rootView, photoEditorView) {
    lateinit var aiIDPhotoManager: AiIDPhotoManager
    lateinit var aiIDPhotoPreviewManager: AiIDPhotoPreviewManager
    var aiIDPhotoBeautyManager: AiIDPhotoBeautyManager? = null

    var stateExitListener: (() -> Unit)? = null
    private val tipIconView: ImageView? by lazy {
        mRootView.findViewById(R.id.picture3d_ai_id_photo_tips) as? ImageView
    }

    override fun clickCancel(view: View?) {
        super.clickCancel(view)
        stateExitListener?.invoke()
    }

    override fun clickDone() {
        super.clickDone()
        stateExitListener?.invoke()
    }

    fun updateTipIconLayoutParams() {
        (tipIconView?.layoutParams as? RelativeLayout.LayoutParams)?.apply {
            rightMargin = mContext.resources.getDimensionPixelOffset(
                if (PhotoEditorUIScheme.isEditorLandscape(mContext)) {
                    R.dimen.picture3d_editor_aiidphoto_tips_margin_right_landscape
                } else {
                    R.dimen.picture3d_editor_aiidphoto_tips_margin_right
                }
            )
        }
    }
}