/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - PortraitBlurFilterAlgoBootstrapConfig.kt
 ** Description: 人像虚化滤镜算法引导配置文件，仅配置滤镜特效映射表.
 ** Version: 1.0
 ** Date : 2024/7/9
 ** Author: <EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** <PERSON><PERSON>.Deng   2024/7/9      1.0        created
 ***************************************************************/
package com.oplus.gallery.pictureeditorpage.app.portraitblur

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoBootstrapConfig
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst
import com.oplus.gallery.framework.abilities.editing.bootstrap.InputSourceType
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect

class PortraitBlurFilterAlgoBootstrapConfig : IAlgoBootstrapConfig, IAlgoConst {
    override fun onSetupAdd(): Map<AvEffect, String> {
        val isSupportIpuFilter = ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IPU_FILTER, false)
        return mapOf(
            AvEffect.WatermarkEffect to formatAlgoInfoKey(
                source = IAlgoConst.OPPO,
                algoName = IAlgoConst.WATERMARK,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.WATERMARK_META_PROVIDER
            ),
            AvEffect.FilterEffect to formatAlgoInfoKey(
                source = if (isSupportIpuFilter) IAlgoConst.OPPO else IAlgoConst.MEICAM,
                algoName = if (isSupportIpuFilter) IAlgoConst.FILTER_IPU else IAlgoConst.FILTER,
                version = 1L,
                inputType = InputSourceType.GPU,
                implementation = if (isSupportIpuFilter) IAlgoConst.IPU else TextUtil.EMPTY_STRING,
                metaProviderPath = if (isSupportIpuFilter) IAlgoConst.FILTER_IPU_META_PROVIDER else IAlgoConst.FILTER_META_PROVIDER
            )
        )
    }

    override fun onSetupUpdate(): Map<AvEffect, String> {
        return mapOf()
    }

    override fun onSetupRemove(): Set<AvEffect> {
        return setOf()
    }
}