/***********************************************************
 * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:   - EditorPortraitBlurUIController
 * Description: build this module
 * Version: 1.0
 * Date : 2022/03/16
 * Author: wudanyang@Apps.Gallery3D
 *
 * ---------------------Revision History: ---------------------
 * <author>                <data>     <version>       <desc>
 *  wudanyang             2022/03/16       1         create
 ******************************************************************/
package com.oplus.gallery.pictureeditorpage.app.portraitblur

import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.animation.Interpolator
import android.widget.TextView
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
import com.oplus.gallery.foundation.ui.animation.AnimationHelper
import com.oplus.gallery.foundation.ui.dialog.LoadingHelper
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.cpu.CPUPerformanceManager
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.display.ScreenUtils.isLand
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.widget.RuleScrollerView.OnSelectValueChangeListener
import com.oplus.gallery.pictureeditorpage.PictureContext
import com.oplus.gallery.pictureeditorpage.base.EditorNavigateUIController
import com.oplus.gallery.pictureeditorpage.common.EditorAnimationUtils
import com.oplus.gallery.pictureeditorpage.frame.EditorBaseState
import com.oplus.gallery.basebiz.R as BaseR

class EditorPortraitBlurUIController(
    context: PictureContext,
    private val toolbarContainerView: ViewGroup?,
    state: EditorBaseState?
) : EditorNavigateUIController(context, toolbarContainerView, state),
    OnSelectValueChangeListener {

    var onSelectValueChanged: ((Int) -> Unit)? = null
    var getApertureByIndex: ((Int) -> Float)? = null
    private var scaleView: TextView? = null
    private var lastValue: Int = INVALID_RULER_INDEX
    private var defaultInterpolator: Interpolator = COUIEaseInterpolator()
    private var hasRulerChanged = false
    private var isStartedExitAnim = false

    /**
     * 由于虚化比较耗时，执行效果时机 需要根据 机型判断
     * 针对普通，中等，高性能平台 改变执行效果时机
     * 中等、高性能：实时执行效果
     * 普通：抬手时再执行效果
     */
    private val applyEffectWhenTouchUp by lazy {
        val isNormal = CPUPerformanceManager.cpuPerformanceLevel == CPUPerformanceManager.CPUPerformanceLevel.NORMAL
        GLog.d(TAG, "applyEffectWhenTouchUp  $isNormal")
        isNormal
    }

    init {
        mReconfigureViewIds = listOf(
            R.id.toolbar_layout,
            R.id.icon_text,
            R.id.scroller_view,
            R.id.icon_text_layout,
            R.id.icon_text_icon,
            R.id.text_strong,
            R.id.text_weak
        )
    }

    override fun onViewCreated() {
        initViews()
    }

    override fun getTitle(): Int {
        return BaseR.string.picture3d_editor_text_portrait_blur
    }

    override fun getToolbarLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return if (EditorUIConfig.isEditorLandscape(config)) {
            R.layout.picture3d_editor_portrait_blur_toolbar_landscape
        } else {
            R.layout.picture3d_editor_portrait_blur_toolbar
        }
    }

    private fun initViews() {
        scaleView = toolbarContainerView?.findViewById(R.id.icon_text)
        mTitleBarView?.updateButton(mContext.getString(R.string.picture3d_button_save))
        mTitleBarView?.setTitleAndButtonMarquee()
        mRuleScrollerView = toolbarContainerView?.findViewById(R.id.scroller_view)
        mRuleScrollerView?.apply {
            startValue = MIN_VALUE_RULER
            endValue = MAX_VALUE_RULER
            setOnSelectValueChangeListener(this@EditorPortraitBlurUIController)
            viewTreeObserver.addOnPreDrawListener(object : ViewTreeObserver.OnPreDrawListener {
                override fun onPreDraw(): Boolean {
                    viewTreeObserver.removeOnPreDrawListener(this)
                    GLog.d(TAG, "addOnPreDrawListener startEnterAnim")
                    startEnterAnim()
                    return true
                }
            })
        }
    }

    /**
     * 光圈坐标转换为调节尺坐标
     */
    fun convertToRulerValueByApertureIndex(apertureIndex: Int): Int {
        return MAX_VALUE_RULER - apertureIndex
    }

    private fun convertToApertureIndexByRulerValue(rulerValue: Int): Int {
        return MAX_VALUE_RULER - rulerValue
    }

    /**
     * 设置小白点位置和初始刻度值
     */
    fun setDefaultValue(index: Int, value: Float?) {
        lastValue = index
        mRuleScrollerView?.post {
            mRuleScrollerView?.apply {
                isEnabled = true
                setDefaultIndex(index)
                currentValue = index
            }
            setSuitableValue(value)
        }
    }

    override fun canSelectValueChange(): Boolean = true

    override fun onStartChangeValue() {
    }

    override fun onSelectValueChanged(value: Int, isFromUser: Boolean) {
        if (isFromUser && (lastValue != value)) {
            VibratorUtils.vibrateInSubThread(mContext, VibratorUtils.EffectType.VIBRATE_TYPE_WEAKEST, true)
            lastValue = value
            hasRulerChanged = true
            val index = convertToApertureIndexByRulerValue(value)
            setSuitableValue(getApertureByIndex?.invoke(index))
            if (!applyEffectWhenTouchUp) {
                onSelectValueChanged?.invoke(index)
            }
        }
    }

    /**
     * 去掉小数部分： .0
     * 防止滑动过程中，当光圈值超过10时，有小数点出现跳动现象
     */
    private fun setSuitableValue(value: Float?) {
        value?.let {
            if ((it.compareTo(MIN_REMOVE_POINT_BLUR_VALUE) >= 0) && (it.minus(it.toInt()).compareTo(0) == 0)) {
                scaleView?.text = it.toInt().toString()
            } else {
                scaleView?.text = it.toString()
            }
        }
    }

    override fun onChangeValueComplete(finalValue: Int) {
        if (applyEffectWhenTouchUp && (hasRulerChanged || ((lastValue != INVALID_RULER_INDEX) && (lastValue != finalValue)))) {
            val index = convertToApertureIndexByRulerValue(finalValue)
            onSelectValueChanged?.invoke(index)
            hasRulerChanged = false
        }
    }

    override fun getToolBarContainerWidthDimenId(): Int {
        return R.dimen.picture3d_editor_toolbar_width_landscape
    }

    override fun isComparable(): Boolean = false

    override fun showDialog(which: Int) {
        when (which) {
            DIALOG_TYPE_ON_SAVING -> showSavingDialog()
            else -> GLog.w(TAG, "showDialog, unKnow type $which")
        }
    }

    override fun hideDialog(which: Int) {
        when (which) {
            DIALOG_TYPE_ON_SAVING -> hideSavingDialog()
            else -> GLog.w(TAG, "hideDialog, unKnow type $which")
        }
    }

    private fun showSavingDialog() {
        mEditorState.loadingHelper.showDialog(LoadingHelper.PROGRESS_DIALOG_SHOW_DELAY_TIME_300_MS)
    }

    private fun hideSavingDialog() {
        mEditorState.loadingHelper.hideDialog(0)
    }


    /**
     * 入场动画
     */
    fun startEnterAnim() {
        val animList = listOf(
            EditorAnimationUtils.createTranslationAnimator(
                mToolbarView,
                !mContext.isLand(),
                TRANSLATION_FADE_IN_ANIMATION_FROM,
                TRANSLATION_FADE_IN_ANIMATION_TO,
                ANIMATION_FADE_IN_DURATION_MS,
                defaultInterpolator
            ),
            EditorAnimationUtils.createAlphaAnimator(
                toolbarContainerView,
                true,
                ANIMATION_FADE_IN_DURATION_MS,
                defaultInterpolator
            )
        )
        EditorAnimationUtils.startAnimationSet(
            animList,
            ANIMATION_FADE_IN_DURATION_MS,
            AnimationHelper.ALPHA_DELAY_TIME_133,
            null
        )
    }

    fun isExited(): Boolean {
        return isStartedExitAnim
    }

    /**
     * 退出动画
     */
    fun startExitAnim() {
        isStartedExitAnim = true
        val animList = listOf(
            EditorAnimationUtils.createTranslationAnimator(
                mToolbarView,
                !mContext.isLand(),
                TRANSLATION_FADE_OUT_ANIMATION_FROM,
                TRANSLATION_FADE_OUT_ANIMATION_TO,
                ANIMATION_FADE_OUT_DURATION_MS,
                defaultInterpolator
            ),
            EditorAnimationUtils.createAlphaAnimator(
                toolbarContainerView,
                false,
                ANIMATION_FADE_OUT_DURATION_MS,
                defaultInterpolator
            )
        )
        EditorAnimationUtils.startAnimationSet(
            animList,
            ANIMATION_FADE_OUT_DURATION_MS,
            AnimationHelper.ALPHA_DELAY_TIME_0,
            null
        )
    }

    override fun destroyView() {
        super.destroyView()
        mRuleScrollerView?.setOnSelectValueChangeListener(null)
        VibratorUtils.terminate()
    }

    override fun getFunctionMenu(): EditorLinearListView? = null

    companion object {
        const val MIN_VALUE_RULER = 0
        const val MAX_VALUE_RULER = 21
        const val DIALOG_TYPE_ON_SAVING = 1
        const val MIN_REMOVE_POINT_BLUR_VALUE = 10
        private const val TAG = "EditorPortraitBlurUIController"
        private const val INVALID_RULER_INDEX = -1
        private const val ANIMATION_FADE_IN_DURATION_MS = 300
        private const val ANIMATION_FADE_OUT_DURATION_MS = 150
        private const val TRANSLATION_FADE_IN_ANIMATION_FROM = 30
        private const val TRANSLATION_FADE_OUT_ANIMATION_FROM = 0
        private const val TRANSLATION_FADE_IN_ANIMATION_TO = 0
        private const val TRANSLATION_FADE_OUT_ANIMATION_TO = 30
    }
}