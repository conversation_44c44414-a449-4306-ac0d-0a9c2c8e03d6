/*********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : DiySizeContentFragment.kt
 ** Description :
 **
 ** Version     : 1.0
 ** Date        : 2024/6/20
 ** Author      : ******** Pengcheng Lin
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                    <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** ******** Pengcheng Lin      2024/6/20     1.0        Add KFile header describe
 *********************************************************************************/
package com.oplus.gallery.pictureeditorpage.app.aiidphoto.size.panel

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_INPUT_LENGTH_LIMIT
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_MILLIMETER_HEIGHT_MAX_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_MILLIMETER_HEIGHT_MIN_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_MILLIMETER_WIDTH_MAX_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_MILLIMETER_WIDTH_MIN_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_NO_COMPRESS_SIZE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_PIXEL_HEIGHT_MAX_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_PIXEL_HEIGHT_MIN_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_PIXEL_WIDTH_MAX_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_PIXEL_WIDTH_MIN_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_SIZE_MAX_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.DEFAULT_SIZE_MIN_VALUE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.SIZE_TYPE_MILLIMETER
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.SIZE_TYPE_PIXEL
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.SIZE_UNIT_MILLIMETER
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.SIZE_UNIT_PIXEL
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.processor.PhotoSizeEntry.Companion.TEMPLATE_TYPE_OTHER
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.size.panel.DiySizePanelFragment.Companion.ARGS_HEIGHT
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.size.panel.DiySizePanelFragment.Companion.ARGS_SIZE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.size.panel.DiySizePanelFragment.Companion.ARGS_TYPE
import com.oplus.gallery.pictureeditorpage.app.aiidphoto.size.panel.DiySizePanelFragment.Companion.ARGS_WIDTH

class DiySizeContentFragment : Fragment() {

    companion object {
        const val TAG = "DiySizeContentFragment"

        const val INPUT_NUMBER_ZERO = "0"

        const val FOCUS_POSITION_WIDTH = 0
        const val FOCUS_POSITION_HEIGHT = 1
        const val FOCUS_POSITION_SIZE = 2

        const val MSG_WHAT_WIDTH = 0
        const val MSG_WHAT_HEIGHT = 1
        const val MSG_WHAT_SIZE = 2

        const val SET_ERROR_DELAYED_TIME = 50L

        private const val INDEX_WIDTH_ITEM = 1
        private const val INDEX_HEIGHT_ITEM = 2
        private const val INDEX_SIZE_ITEM = 3
        //软键盘弹出延时时间,毫秒
        private const val SHOW_SOFT_INPUT_DELAY_TIME = 300L

        @JvmStatic
        val instance: DiySizeContentFragment
            get() = DiySizeContentFragment()
    }

    private lateinit var containerScrollView: ScrollView
    private lateinit var contentContainer: LinearLayout
    private lateinit var widthEditText: COUIEditText
    private lateinit var heightEditText: COUIEditText
    private lateinit var sizeEditText: COUIEditText
    private lateinit var widthErrorText: TextView
    private lateinit var heightErrorText: TextView
    private lateinit var sizeErrorText: TextView

    private var listener: Listener? = null
    private var isPixel = true
    private var isWidthGreater = false
    private var focusAt = FOCUS_POSITION_WIDTH

    private var errorStateHandler =
        object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                val editText = msg.obj as COUIEditText
                editText.isErrorState = true
                checkIfReady()
            }
        }

    private val widthFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
        checkWidthInputVal(hasFocus, false)
        checkIfWidthGreaterThanHeight(hasFocus)
        if (hasFocus) {
            focusAt = FOCUS_POSITION_WIDTH
            if (widthEditText.length() >= DEFAULT_INPUT_LENGTH_LIMIT) {
                checkWidthInputVal(clearOrCheck = false, onlyCheckGreaterError = false)
            }
        }
    }

    private val heightFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
        checkHeightInputVal(hasFocus, false)
        checkIfWidthGreaterThanHeight(hasFocus)
        if (hasFocus) {
            focusAt = FOCUS_POSITION_HEIGHT
            if (heightEditText.length() >= DEFAULT_INPUT_LENGTH_LIMIT) {
                checkHeightInputVal(clearOrCheck = false, onlyCheckGreaterError = false)
            }
        }
    }

    private val sizeFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
        checkSizeInputVal(hasFocus, false)
        if (hasFocus) {
            focusAt = FOCUS_POSITION_SIZE
            if (sizeEditText.length() >= DEFAULT_INPUT_LENGTH_LIMIT) {
                checkSizeInputVal(clearOrCheck = false, onlyCheckGreaterError = false)
            }
        }
    }

    private val widthTextWatcher: TextWatcher = object : TextWatcher {
        override fun onTextChanged(inputText: CharSequence, start: Int, before: Int, count: Int) {
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

        override fun afterTextChanged(s: Editable) {
            checkFirstNumNotZero(s, widthEditText)
            checkInputLength(s)
            if (s.isEmpty()) {
                setErrorState(widthEditText, false)
            }
            checkWidthInputVal(clearOrCheck = false, onlyCheckGreaterError = true)
        }
    }

    private val heightTextWatcher: TextWatcher = object : TextWatcher {
        override fun onTextChanged(inputText: CharSequence, start: Int, before: Int, count: Int) {
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

        override fun afterTextChanged(s: Editable) {
            checkFirstNumNotZero(s, heightEditText)
            checkInputLength(s)
            if (s.isEmpty()) {
                setErrorState(heightEditText, false)
            }
            checkHeightInputVal(clearOrCheck = false, onlyCheckGreaterError = true)
        }
    }

    private val sizeTextWatcher: TextWatcher = object : TextWatcher {
        override fun onTextChanged(inputText: CharSequence, start: Int, before: Int, count: Int) {
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

        override fun afterTextChanged(s: Editable) {
            checkFirstNumNotZero(s, sizeEditText)
            checkInputLength(s)
            if (s.isEmpty()) {
                setErrorState(sizeEditText, false)
            }
            checkSizeInputVal(clearOrCheck = false, onlyCheckGreaterError = true)
        }
    }

    private val onGlobalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
        (containerScrollView.parent as? FrameLayout)?.let {
            if (it.height < containerScrollView.height) {
                it.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = containerScrollView.height
                }
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rootView = inflater.inflate(R.layout.picture3d_aiidphoto_size_fragment_layout, container, false)
        initViews(rootView)
        loadParams(rootView)
        initListeners()
        return rootView
    }

    override fun onResume() {
        super.onResume()
        autoShowKeyboard()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if ((newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) && COUIResponsiveUtils.isSmallScreenDp(newConfig.screenHeightDp)) {
            //处理小屏横竖屏切换时键盘弹出挤压containerScrollView的高度,导致底部显示空白问题;
            containerScrollView?.updateLayoutParams<FrameLayout.LayoutParams> {
                val toolbarHeight =
                    resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_min_height) +
                            resources.getDimensionPixelSize(com.support.tablayout.R.dimen.tablayout_small_layout_height) +
                            resources.getDimensionPixelSize(com.support.panel.R.dimen.coui_bottom_sheet_margin_bottom_smallland_default) +
                            resources.getDimensionPixelSize(R.dimen.picture3d_editor_panel_viewpager_margin_top) +
                            resources.getDimensionPixelSize(R.dimen.picture3d_editor_panel_viewpager_margin_bottom)
                height = ResourceUtils.dp2px(context, newConfig.screenHeightDp.toFloat()) - toolbarHeight
            }
        } else {
            containerScrollView?.updateLayoutParams<FrameLayout.LayoutParams> {
                height = contentContainer.height + contentContainer.marginTop
            }
        }
    }

    private fun initViews(rootView: View) {
        containerScrollView = rootView.findViewById(R.id.sl_container)
        widthEditText = rootView.findViewById(R.id.cet_width)
        heightEditText = rootView.findViewById(R.id.cet_height)
        sizeEditText = rootView.findViewById(R.id.cet_size)
        widthErrorText = rootView.findViewById(R.id.tv_width_error)
        heightErrorText = rootView.findViewById(R.id.tv_height_error)
        sizeErrorText = rootView.findViewById(R.id.tv_size_error)
        contentContainer = rootView.findViewById(R.id.content_container)
        //处理折叠屏键盘弹出时挤压containerScrollView的父布局高度,导致隐藏键盘时底部显示空白问题
        (containerScrollView.parent as? FrameLayout)?.let {
            it.viewTreeObserver.addOnGlobalLayoutListener(onGlobalLayoutListener)
        }
    }

    private fun initListeners() {
        ErrorMsgAnimHelper(widthEditText, widthErrorText)
        ErrorMsgAnimHelper(heightEditText, heightErrorText)
        ErrorMsgAnimHelper(sizeEditText, sizeErrorText)
        widthEditText.onFocusChangeListener = widthFocusChangeListener
        heightEditText.onFocusChangeListener = heightFocusChangeListener
        sizeEditText.onFocusChangeListener = sizeFocusChangeListener
        widthEditText.addTextChangedListener(widthTextWatcher)
        heightEditText.addTextChangedListener(heightTextWatcher)
        sizeEditText.addTextChangedListener(sizeTextWatcher)
    }

    private fun loadParams(rootView: View) {
        isPixel = arguments?.getInt(ARGS_TYPE) == SIZE_TYPE_PIXEL
        val unit: String
        if (isPixel) {
            unit = SIZE_UNIT_PIXEL
            widthEditText.hint = "$DEFAULT_PIXEL_WIDTH_MIN_VALUE - $DEFAULT_PIXEL_WIDTH_MAX_VALUE"
            heightEditText.hint = "$DEFAULT_PIXEL_HEIGHT_MIN_VALUE - $DEFAULT_PIXEL_HEIGHT_MAX_VALUE"
        } else {
            unit = SIZE_UNIT_MILLIMETER
            widthEditText.hint = "$DEFAULT_MILLIMETER_WIDTH_MIN_VALUE - $DEFAULT_MILLIMETER_WIDTH_MAX_VALUE"
            heightEditText.hint = "$DEFAULT_MILLIMETER_HEIGHT_MIN_VALUE - $DEFAULT_MILLIMETER_HEIGHT_MAX_VALUE"
        }
        sizeEditText.hint = "$DEFAULT_SIZE_MIN_VALUE - $DEFAULT_SIZE_MAX_VALUE"
        val widthUnitText = rootView.findViewById<TextView>(R.id.tv_width_unit)
        widthUnitText.text = unit
        val heightUnitText = rootView.findViewById<TextView>(R.id.tv_height_unit)
        heightUnitText.text = unit
        if (arguments?.getInt(ARGS_WIDTH) != 0) {
            widthEditText.setText(arguments?.getInt(ARGS_WIDTH).toString())
        }
        if (arguments?.getInt(ARGS_HEIGHT) != 0) {
            heightEditText.setText(arguments?.getInt(ARGS_HEIGHT).toString())
        }
        if (arguments?.getInt(ARGS_SIZE) != 0) {
            sizeEditText.setText(arguments?.getInt(ARGS_SIZE).toString())
        }
        COUICardListHelper.setItemCardBackground(
            rootView.findViewById(R.id.width_item),
            INDEX_WIDTH_ITEM
        )
        COUICardListHelper.setItemCardBackground(
            rootView.findViewById(R.id.height_item),
            INDEX_HEIGHT_ITEM
        )
        COUICardListHelper.setItemCardBackground(
            rootView.findViewById(R.id.size_item),
            INDEX_SIZE_ITEM
        )
    }

    @Suppress("LongParameterList")
    private fun checkInputVal(
        clearOrCheck: Boolean,
        editText: COUIEditText,
        textView: TextView,
        ceiling: Int,
        floor: Int,
        ceilingStrResId: Int,
        floorStrResId: Int,
        onlyCheckGreaterError: Boolean
    ) {
        if (!clearOrCheck) {
            if (editText.text.isNullOrEmpty()) return
            val inputVal = Integer.parseInt(editText.text.toString())
            val isGreater = inputVal > ceiling
            val isLess = inputVal < floor
            val isError = isGreater || (isLess && !onlyCheckGreaterError)
            setErrorState(editText, isError)
            textView.text = when {
                isGreater ->
                    activity?.resources?.getQuantityString(ceilingStrResId, ceiling, ceiling)
                isLess && !onlyCheckGreaterError ->
                    activity?.resources?.getQuantityString(floorStrResId, floor, floor)
                else ->
                    ""
            }
        } else {
            setErrorState(editText, false)
        }
    }

    private fun setErrorState(editText: COUIEditText, isError: Boolean) {
        val msgWhat = when (editText) {
            widthEditText -> MSG_WHAT_WIDTH
            heightEditText -> MSG_WHAT_HEIGHT
            else -> MSG_WHAT_SIZE
        }
        errorStateHandler.removeMessages(msgWhat)
        if (isError) {
            val msg = Message.obtain()
            msg.what = msgWhat
            msg.obj = editText
            errorStateHandler.sendMessageDelayed(msg, SET_ERROR_DELAYED_TIME)
        } else {
            editText.isErrorState = false
            checkIfReady()
        }
    }

    private fun isErrorState(editText: COUIEditText): Boolean {
        val msgWhat = when (editText) {
            widthEditText -> MSG_WHAT_WIDTH
            heightEditText -> MSG_WHAT_HEIGHT
            else -> MSG_WHAT_SIZE
        }
        return errorStateHandler.hasMessages(msgWhat) || editText.isErrorState
    }

    private fun checkIfWidthGreaterThanHeight(clearOrCheck: Boolean) {
        if (clearOrCheck) {
            if (isWidthGreater) {
                setErrorState(heightEditText, false)
                isWidthGreater = false
            }
        } else {
            if (widthEditText.text.isNullOrEmpty() ||
                heightEditText.text.isNullOrEmpty() ||
                isErrorState(heightEditText)
            ) {
                return
            }
            val width = Integer.parseInt(widthEditText.text.toString())
            val height = Integer.parseInt(heightEditText.text.toString())
            if (width > height) {
                heightErrorText.text = activity?.resources?.getString(R.string.picture3d_aiidphoto_hint_with_larger_than_height)
                setErrorState(heightEditText, true)
                isWidthGreater = true
            }
        }
    }

    private fun checkWidthInputVal(clearOrCheck: Boolean, onlyCheckGreaterError: Boolean) {
        if (isPixel) {
            checkInputVal(
                clearOrCheck,
                widthEditText,
                widthErrorText,
                DEFAULT_PIXEL_WIDTH_MAX_VALUE,
                DEFAULT_PIXEL_WIDTH_MIN_VALUE,
                R.plurals.picture3d_aiidphoto_hint_width_smaller_than_pixel,
                R.plurals.picture3d_aiidphoto_hint_width_larger_than_pixel,
                onlyCheckGreaterError
            )
        } else {
            checkInputVal(
                clearOrCheck,
                widthEditText,
                widthErrorText,
                DEFAULT_MILLIMETER_WIDTH_MAX_VALUE,
                DEFAULT_MILLIMETER_WIDTH_MIN_VALUE,
                R.plurals.picture3d_aiidphoto_hint_width_smaller_than_millimeter,
                R.plurals.picture3d_aiidphoto_hint_width_larger_than_millimeter,
                onlyCheckGreaterError
            )
        }
    }

    private fun checkHeightInputVal(clearOrCheck: Boolean, onlyCheckGreaterError: Boolean) {
        if (isPixel) {
            checkInputVal(
                clearOrCheck,
                heightEditText,
                heightErrorText,
                DEFAULT_PIXEL_HEIGHT_MAX_VALUE,
                DEFAULT_PIXEL_HEIGHT_MIN_VALUE,
                R.plurals.picture3d_aiidphoto_hint_height_smaller_than_pixel,
                R.plurals.picture3d_aiidphoto_hint_height_larger_than_pixel,
                onlyCheckGreaterError
            )
        } else {
            checkInputVal(
                clearOrCheck,
                heightEditText,
                heightErrorText,
                DEFAULT_MILLIMETER_HEIGHT_MAX_VALUE,
                DEFAULT_MILLIMETER_HEIGHT_MIN_VALUE,
                R.plurals.picture3d_aiidphoto_hint_height_smaller_than_millimeter,
                R.plurals.picture3d_aiidphoto_hint_height_larger_than_millimeter,
                onlyCheckGreaterError
            )
        }
    }

    private fun checkSizeInputVal(clearOrCheck: Boolean, onlyCheckGreaterError: Boolean) {
        checkInputVal(
            clearOrCheck,
            sizeEditText,
            sizeErrorText,
            DEFAULT_SIZE_MAX_VALUE,
            DEFAULT_SIZE_MIN_VALUE,
            R.plurals.picture3d_aiidphoto_hint_size_smaller_than_kb,
            R.plurals.picture3d_aiidphoto_hint_size_larger_than_kb,
            onlyCheckGreaterError
        )
    }

    private fun checkIfReady(): Boolean {
        val existEmpty: Boolean = widthEditText.text.isNullOrEmpty() || heightEditText.text.isNullOrEmpty()
        if (existEmpty) {
            listener?.onReady(false, hasInput())
            return false
        }
        val existError = isErrorState(widthEditText) || isErrorState(heightEditText) || isErrorState(sizeEditText)
        listener?.onReady(!existError, hasInput())
        return !existError
    }

    private fun checkInputLength(s: Editable) {
        if (s.length >= DEFAULT_INPUT_LENGTH_LIMIT) {
            ToastUtil.showShortToast(R.string.picture3d_aiidphoto_hint_exceed_limit)
        }
    }

    private fun checkFirstNumNotZero(s: Editable, editText: COUIEditText) {
        val text = s.toString()
        if (text.isNotEmpty() && text.substring(0, 1) == INPUT_NUMBER_ZERO) {
            editText.setText(text.substring(1))
        }
    }

    private fun hasInput(): Boolean {
        return !(widthEditText.text.isNullOrEmpty() && heightEditText.text.isNullOrEmpty())
    }

    fun clear() {
        containerScrollView.scrollTo(0, 0)
        widthEditText.requestFocus()
        widthEditText.text?.clear()
        heightEditText.text?.clear()
        sizeEditText.setText("$DEFAULT_SIZE_MAX_VALUE")
    }

    fun requestFocus(): View {
        var view: EditText = widthEditText
        when (focusAt) {
            FOCUS_POSITION_WIDTH -> view = widthEditText
            FOCUS_POSITION_HEIGHT -> view = heightEditText
            FOCUS_POSITION_SIZE -> view = sizeEditText
        }
        view.requestFocus()
        view.isFocusable = true
        view.isFocusableInTouchMode = true
        return view
    }

    fun hasFocus(): Boolean {
        return widthEditText.hasFocus() || heightEditText.hasFocus() || sizeEditText.hasFocus()
    }

    fun setListener(listener: Listener) {
        this.listener = listener
    }

    fun finalCheck(): Boolean {
        checkWidthInputVal(clearOrCheck = false, onlyCheckGreaterError = false)
        checkHeightInputVal(clearOrCheck = false, onlyCheckGreaterError = false)
        checkIfWidthGreaterThanHeight(false)
        checkSizeInputVal(clearOrCheck = false, onlyCheckGreaterError = false)
        return checkIfReady()
    }

    fun getDiyEntry(): PhotoSizeEntry {
        var width = 0
        var height = 0
        val title = resources.getString(R.string.picture3d_aiidphoto_diy)
        if (!widthEditText.text.isNullOrEmpty()) {
            width = Integer.parseInt(widthEditText.text.toString())
        }
        if (!heightEditText.text.isNullOrEmpty()) {
            height = Integer.parseInt(heightEditText.text.toString())
        }
        val size = if (!sizeEditText.text.isNullOrEmpty()) {
            Integer.parseInt(sizeEditText.text.toString())
        } else {
            DEFAULT_NO_COMPRESS_SIZE
        }

        return if (isPixel) {
            PhotoSizeEntry(
                title,
                SIZE_TYPE_PIXEL,
                width,
                height,
                size,
                true,
                TEMPLATE_TYPE_OTHER
            )
        } else {
            PhotoSizeEntry(
                title,
                SIZE_TYPE_MILLIMETER,
                width,
                height,
                size,
                true,
                TEMPLATE_TYPE_OTHER
            )
        }
    }

    /**
     * 移除view的一些监听
     */
    private fun removeListeners() {
        if (::widthEditText.isInitialized) {
            widthEditText.onFocusChangeListener = null
            heightEditText.onFocusChangeListener = null
            sizeEditText.onFocusChangeListener = null
            widthEditText.removeTextChangedListener(widthTextWatcher)
            heightEditText.removeTextChangedListener(heightTextWatcher)
            sizeEditText.removeTextChangedListener(sizeTextWatcher)
            (containerScrollView.parent as? FrameLayout)?.viewTreeObserver?.removeOnGlobalLayoutListener(onGlobalLayoutListener)
            widthEditText.removeCallbacks(runnable)
        }
    }

    /**
     * 弹出软键盘 runnable
     */
    private val runnable by lazy {
        Runnable {
            widthEditText.isFocusable = true
            widthEditText.isFocusableInTouchMode = true
            widthEditText.requestFocus()
            val inputMethodManager = widthEditText.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.showSoftInput(widthEditText, 0)
        }
    }

    /**
     * 自动弹出软键盘
     */
    private fun autoShowKeyboard() {
        widthEditText.postDelayed(runnable, SHOW_SOFT_INPUT_DELAY_TIME)
    }

    /**
     * 移除view相关的监听放到这里
     */
    override fun onDestroyView() {
        super.onDestroyView()
        removeListeners()
    }

    interface Listener {
        fun onReady(isReady: Boolean, hasInput: Boolean)
    }
}