<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/title_bar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/collage_gl_action_bar_menu_items_collage_toolbar_layout_height">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="@string/collage_activity_collage"
        android:textColor="@color/collage_toolbar_title_text_color"
        android:textSize="@dimen/collage_title_text_size" />

    <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
        android:id="@+id/action_cancel"
        style="@style/CollageBottomActionBarText"
        android:layout_marginStart="@dimen/collage_bottom_action_bar_text_margin_horizontal"
        android:text="@string/common_cancel"
        android:gravity="center_horizontal"
        android:hint="@string/hint_button_talkback"
        android:layout_centerVertical="true" />

    <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
        android:id="@+id/action_save"
        style="@style/CollageBottomActionBarText"
        android:layout_marginEnd="@dimen/collage_bottom_action_bar_text_margin_horizontal"
        android:text="@string/common_save"
        android:gravity="center_horizontal"
        android:hint="@string/hint_button_talkback"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true" />
</RelativeLayout>
