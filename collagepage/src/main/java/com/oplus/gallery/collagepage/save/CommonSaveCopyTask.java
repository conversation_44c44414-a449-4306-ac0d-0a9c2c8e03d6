/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.oplus.gallery.collagepage.save;

import android.annotation.SuppressLint;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.provider.MediaStore;
import android.provider.MediaStore.Images;

import com.oplus.gallery.business_lib.model.data.base.Path;
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem;
import com.oplus.gallery.business_lib.model.data.base.source.DataManager;
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper;
import com.oplus.gallery.collagepage.Config;
import com.oplus.gallery.collagepage.Constant;
import com.oplus.gallery.collagepage.cobox.utils.GLog;
import com.oplus.gallery.foundation.exif.raw.ExifUtils;
import com.oplus.gallery.foundation.fileaccess.data.FileResponse;
import com.oplus.gallery.foundation.fileaccess.helper.ContentValuesHelper;
import com.oplus.gallery.foundation.util.storage.StorageLimitHelper;
import com.oplus.gallery.business_lib.helper.BitmapSaveHelper;
import com.oplus.gallery.foundation.util.media.MediaStoreScannerHelper;
import com.oplus.gallery.foundation.util.storage.OplusEnvironment.StorageType;
import com.oplus.gallery.foundation.util.storage.SandBoxUtils;
import com.oplus.gallery.foundation.database.store.GalleryStore;
import com.oplus.gallery.foundation.database.util.MediaStoreUtils;
import com.oplus.gallery.foundation.dbaccess.convert.IConvert;
import com.oplus.gallery.foundation.dbaccess.dao.GalleryDbDao;
import com.oplus.gallery.foundation.dbaccess.dao.MediaStoreDbDao;
import com.oplus.gallery.foundation.dbaccess.req.InsertReq;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.standard_lib.file.utils.FilePathUtils;
import com.oplus.gallery.foundation.util.media.MimeTypeUtils;

import java.io.FileNotFoundException;
import java.sql.Date;
import java.text.SimpleDateFormat;

/**
 * Asynchronous task for saving edited photo as a new copy.
 */
public class CommonSaveCopyTask extends AsyncTask<Bitmap, Void, Uri> {


    public static final int SAVE_SUCCESS = 0;
    public static final int NO_SPACE = 1;
    public static final int SAVE_FAILED_DIMENSION_TOO_LARGE = 2;
    public static final int SAVE_FAILED_ERROR = 3;
    private static final String TAG = "CommonSaveCopyTask";
    private static final int WIDTH_RATIO = 10;
    private final Context mContext;
    private final Callback mCallback;
    private final String mSaveFileName;
    private File mSaveFile;
    private File mSaveFolderName;
    private long mSavingTimestamp;
    private Path mItemPath;
    private Path mAlbumPath;

    /**
     * Callback for the completed asynchronous task.
     */
    public interface Callback {
        void onTip(int what);

        void onComplete(Uri result, String filePath, Path itemPath, Path albumPath);
    }

    @SuppressLint("SimpleDateFormat")
    public CommonSaveCopyTask(Context context, Callback callback) {
        super();
        mContext = context;
        mCallback = callback;
        mSavingTimestamp = System.currentTimeMillis();
        mSaveFileName = new SimpleDateFormat(Config.Collage.TIME_STAMP_NAME).format(new Date(mSavingTimestamp));
        mSaveFolderName = new File(Config.ResourceParse.COLLAGE_SAVE_DIRECTORY);
    }

    /**
     * The task should be executed with one given bitmap to be saved.
     */
    @SuppressWarnings("checkstyle:MagicNumber")
    @Override
    protected Uri doInBackground(Bitmap... params) {
        // Support larger dimensions for photo saving.
        Bitmap bitmap = params[0];
        if (bitmap == null) {
            return null;
        }
        long bitmapSize = bitmap.getAllocationByteCount();
        boolean isSpaceAvailable = StorageLimitHelper.hasEnoughStorageSpace(StorageType.PHONE_STORAGE, bitmapSize);
        if (!isSpaceAvailable) {
            GLog.d(TAG, "doInBackground, bitmapSize: " + bitmapSize
                    + ", bitmap.w: " + bitmap.getWidth() + ", bitmap.h: " + bitmap.getHeight());
            mCallback.onTip(NO_SPACE);
            return null;
        }
        // Use the default save directory if the source directory cannot be saved.
        boolean fileExist = true;
        FileResponse fileResponse = null;
        Uri uri = null;
        ContentValues contentValues = getBaseContentValues();
        try {
            fileResponse = BitmapSaveHelper.saveAfterCollage(
                    mContext, bitmap, mSaveFolderName, mSaveFileName,
                    Config.Collage.SAVE_COLLAGE_COMPRESS_FORMAT, contentValues, mSavingTimestamp, Config.Collage.SAVE_COLLAGE_COMPRESS_QUALITY);
        } catch (FileNotFoundException exp) {
            GLog.printTrace(TAG, "[saveBitmap] Cannot save bitmap", exp);
            fileExist = false;
        } catch (Exception e) {
            GLog.printTrace(TAG, "[saveBitmap] Cannot save bitmap", e);
        }
        if (fileResponse == null) {
            return null;
        }
        mSaveFile = fileResponse.getFile();
        if (mSaveFile != null) {
            uri = fileResponse.getUri();
            insertLocalMedia(mSaveFile, uri, contentValues);
            MediaStoreScannerHelper.scanFileByMediaStoreSingle(mContext, mSaveFile.getAbsolutePath(), false);
            preloadMediaItem(uri);
            GLog.i(TAG, "[doInBackground] Insert " + uri + "(file:" + mSaveFile + ") to Media Database");
        } else {
            GLog.e(TAG, "[doInBackground] Bitmap compress failed, uri is null: " + fileExist);
            if (mCallback != null) {
                int bitmapWidth = bitmap.getWidth();
                int bitmapHeight = bitmap.getHeight();
                if (fileExist && ((bitmapWidth > Config.JoinCollage.SAVE_COLLAGE_WIDTH_LIMIT)
                        || (bitmapHeight > (bitmapWidth * WIDTH_RATIO)))) {
                    GLog.d(TAG, "doInBackground, bitmapSize: " + bitmapSize
                            + ", bitmap.w: " + bitmapWidth + ", bitmap.h: " + bitmapHeight);
                    mCallback.onTip(SAVE_FAILED_DIMENSION_TOO_LARGE);
                } else {
                    mCallback.onTip(SAVE_FAILED_ERROR);
                }
            }
        }
        if (!bitmap.isRecycled()) {
            bitmap.recycle();
        }

        return uri;
    }

    @Override
    protected void onPostExecute(Uri result) {
        if (result == null) {
            GLog.e(TAG, "[onPostExecute] Bitmap save failed, url is null");
        } else {
            GLog.v(TAG, "[onPostExecute] Url has been inserted!");
            if (mCallback != null) {
                mCallback.onTip(SAVE_SUCCESS);
            }
        }
        if (mCallback != null) {
            mCallback.onComplete(result, (mSaveFolderName != null) ? mSaveFolderName.getAbsolutePath() : null, mItemPath, mAlbumPath);
        }
    }

    protected void insertLocalMedia(final File file, Uri uri, final ContentValues contentValues) {
        if ((uri != null) && (contentValues != null)) {
            final long mediaID = ContentUris.parseId(uri);
            new InsertReq.Builder().setDaoType(GalleryDbDao.DaoType.GALLERY)
                    .setTableType(GalleryDbDao.TableType.LOCAL_MEDIA)
                    .setConvert(new IConvert<Void, ContentValues>() {
                        @Override
                        public ContentValues convert(Void aVoid) {
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.DATA, file.getAbsolutePath());
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.SIZE, file.length());
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.DISPLAY_NAME, file.getName());
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_ID, mediaID);
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.MEDIA_TYPE, MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE);
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_NAME, FilePathUtils.getBucketName(mSaveFile));
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.BUCKET_ID, FilePathUtils.getBucketId(mSaveFile));
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.IS_PENDING, ContentValuesHelper.IS_PENDING_FALSE);
                            contentValues.put(GalleryStore.GalleryColumns.LocalColumns.VOLUME_NAME, FilePathUtils.getLocalVolumeName(mSaveFile.getAbsolutePath()));
                            return contentValues;
                        }
                    }).build().exec();
        }
    }

    private void preloadMediaItem(Uri uri) {
        if (uri == null) {
            GLog.e(TAG, "preloadMediaItem, uri == null");
            return;
        }
        MediaItem mediaItem = LocalMediaDataHelper.preloadMediaItem(uri);
        if (mediaItem != null) {
            mItemPath = mediaItem.getPath();
        }
        if (mItemPath != null) {
            mAlbumPath = DataManager.getDefaultSetOf(mItemPath);
        }
        GLog.d(TAG, "preloadMediaItem, mItemPath = " + mItemPath + ", mAlbumPath = " + mAlbumPath);
    }

    private ContentValues getBaseContentValues() {
        long now = mSavingTimestamp / Constant.MILLISECOND_IN_SECOND;
        final ContentValues values = new ContentValues();
        values.put(Images.Media.TITLE, mSaveFileName);
        values.put(Images.Media.MIME_TYPE, MimeTypeUtils.MIME_TYPE_IMAGE_JPEG);
        values.put(Images.Media.DATE_TAKEN, mSavingTimestamp);
        values.put(Images.Media.DATE_MODIFIED, now);
        values.put(Images.Media.DATE_ADDED, now);
        values.put(Images.Media.ORIENTATION, 0);
        return values;
    }
}
