/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - SolutionItemMenuAdapter.kt
 * Description:
 * Version:
 * Date:
 * Author: duchengsong@Apps.Gallery3D
 * TAG: OPLUS_FEATURE_APP_LOG_MONITOR
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                       <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * duchengsong@Apps.Gallery3D      2021/8/23     1.0     OPLUS_FEATURE_APP_LOG_MONITOR
 *************************************************************************************************/
package com.oplus.gallery.collagepage.cobox.view

import android.content.Context
import android.content.res.Resources
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewGroup
import androidx.annotation.VisibleForTesting
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter
import com.oplus.gallery.business_lib.template.editor.adapter.EditorAnimViewHolder
import com.oplus.gallery.business_lib.template.editor.adapter.Selectable
import com.oplus.gallery.business_lib.template.editor.adapter.setViewSelectedState
import com.oplus.gallery.business_lib.template.editor.anim.EditorDrawableAlphaAnimation
import com.oplus.gallery.business_lib.template.editor.anim.EditorPressAnimation
import com.oplus.gallery.collagepage.Config
import com.oplus.gallery.collagepage.R
import com.oplus.gallery.collagepage.cobox.dataset.CollageDataManager
import com.oplus.gallery.collagepage.cobox.dataset.CollageSolution
import com.oplus.gallery.collagepage.cobox.dataset.loader.MenuItemThumbLoader
import com.oplus.gallery.collagepage.cobox.utils.FIFOMap
import com.oplus.gallery.collagepage.cobox.utils.GLog

open class CollageSolutionItemMenuAdapter(
    context: Context,
    val solutionClassification: Int = -1,
    data: List<CollageSolutionItemViewData>?
) : BaseRecyclerAdapter<CollageSolutionItemViewData>(context, data) {

    private var currentScale = 1F

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    var menuItemWidth = ViewGroup.LayoutParams.WRAP_CONTENT
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    var menuItemHeight = ViewGroup.LayoutParams.WRAP_CONTENT
    private var menuItemPadding = 0

    private var outBorderDrawable: Drawable? = null
    private var defaultThumb: Drawable? = null
    private var thumbCache: FIFOMap<CollageSolution, Drawable>? = null

    private val normalHeight by lazy {
        context.resources.getDimensionPixelSize(R.dimen.collage_horizontallist_item_height)
    }
    private val normalWidth by lazy {
        context.resources.getDimensionPixelSize(R.dimen.collage_horizontallist_item_width)
    }

    init {
        defaultThumb = context.getDrawable(R.drawable.collage_solution_menu_item_default_thumb)
        outBorderDrawable = context.getDrawable(R.drawable.collage_solution_menu_item_frame)?.mutate()
        thumbCache = FIFOMap(Config.Collage.SOLUTION_THUMB_CACHE_SIZE)

        val res = context.resources
        when (solutionClassification) {
            Config.Collage.TEMPLATE_CLASSIFICATION, Config.Collage.POSTER_CLASSIFICATION, Config.Collage.JOINT_CLASSIFICATION -> {
                menuItemWidth = res.getDimensionPixelSize(R.dimen.collage_horizontallist_item_width)
                menuItemHeight = res.getDimensionPixelSize(R.dimen.collage_horizontallist_item_height)
                menuItemPadding = res.getDimensionPixelSize(R.dimen.collage_horizontallist_item_padding)
            }
            else -> GLog.e(TAG, "init:Type $solutionClassification no match!")
        }
    }

    override fun getItemLayoutId(viewType: Int): Int {
        return Resources.ID_NULL
    }

    override fun createItemView(): View {
        return MenuListItem(mContext).apply {
            setSize(menuItemWidth, menuItemHeight)
            setupCustomStyle(outBorderDrawable?.constantState?.newDrawable(), defaultThumb)
            isForceDarkAllowed = false
            allowSelectionOverlayIgnorePadding(true)
            setPadding(menuItemPadding, menuItemPadding, menuItemPadding, menuItemPadding)
            setForceDrawOutBorder(true)
        }
    }

    override fun createViewHolder(itemView: View, viewType: Int): BaseRecycleViewHolder {
        return object : EditorAnimViewHolder(itemView, EditorDrawableAlphaAnimation(), EditorPressAnimation()) {
            override fun updateAnimValue(value: Any) {
                (value as? Int)?.let {
                    (itemView as? MenuListItem)?.setOutBorderAlpha(it)
                }
            }
        }.apply {
            setSelectedAnimView(this)
            setSelectedAnimEnable(true)
            (itemView as? MenuListItem)?.setOutBorderAlpha(0)
        }
    }

    override fun bindData(viewHolder: BaseRecycleViewHolder, position: Int, item: CollageSolutionItemViewData) {
        val itemView = (viewHolder.itemView as? MenuListItem) ?: return

        itemView.setSize(menuItemWidth, menuItemHeight)

        if (itemView.position != position) {
            val solution = item.collageSolution
            if (solution != null) {
                val cache: BitmapDrawable? = getCacheThumb(solution)
                itemView.position = position

                if (cache == null) {
                    itemView.setMenuItemThumb(null)
                    val dataManager = CollageDataManager.getInstance()
                    dataManager?.taskManager?.asynLoader?.submitConcurrencyTask(
                        MenuItemThumbLoader(mContext, solution, itemView, thumbCache)
                    )
                } else {
                    itemView.setMenuItemThumb(cache)
                }
            }
        }
        viewHolder.setViewSelectedState(if (itemView.isSelected) Selectable.SELECTED else Selectable.UNSELECTED)
    }

    public override fun getMenuData(index: Int): CollageSolutionItemViewData {
        return super.getMenuData(index)
    }

    fun clearCache() {
        thumbCache?.clear()
    }

    private fun getCacheThumb(key: CollageSolution?): BitmapDrawable? {
        return thumbCache?.get(key) as? BitmapDrawable?
    }

    /**
     * 可根据传入的大小比例，对item宽高进行精准控制，不局限于目前这两档宽高设置。
     */
    fun resize(scale: Float) {
        if (scale == currentScale) return
        currentScale = scale
        menuItemHeight = (normalHeight * scale).toInt()
        menuItemWidth = (normalWidth * scale).toInt()
        notifyDataSetChanged()
    }

    companion object {
        private const val TAG = "CollageSolutionItemMenuAdapter"
    }
}