/***********************************************************
** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/

package com.oplus.gallery.collagepage.cobox.render;

import android.graphics.RectF;
import android.view.MotionEvent;

import com.oplus.gallery.collagepage.cobox.kernel.Transform;

/**
 * Detect masked region
 *
 * <AUTHOR>
 * @date 2014-12-18 10:19:33
 */
public class RegionDetecter extends AbsDetecter {

    private static final String TAG = "RegionDetecter";

    private Transform mTransform = null;

    public RegionDetecter() {

    }

    public RegionDetecter(Transform parentTransform) {
        mTransform = parentTransform;
    }

    public void setTransfrom(Transform transform) {
        mTransform = transform;
    }

    /**
     * Detect the point is standing inside of the picture
     *
     * @param x
     * @param y
     * @return
     */
    public boolean inArea(float x, float y) {
        RectF box = mTransform.getAABBBox();
        return box.contains(x, y);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return inArea(event.getX(), event.getY());
    }
}
