/***********************************************************
 * * Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.gallery.collagepage.cobox.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint.Align;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.oplus.gallery.collagepage.Config;
import com.oplus.gallery.collagepage.Constant;
import com.oplus.gallery.collagepage.R;
import com.oplus.gallery.collagepage.cobox.utils.TextUtils;
import com.oplus.gallery.standard_lib.ui.view.anim.Smoother2;

/**
 * <AUTHOR>
 * @date 2015-04-10 11:19:24
 */
public class TabSwitcher extends View {

    private static final String TAG = "TabSwitcher";
    private static final int MAX_ALPHA_INT = 255;
    private static final int HINT_COLOR_MASK = 0x00FFFFFF;

    private GradientDrawable mDblThumb = new GradientDrawable();
    private Smoother2 mThumbTranslateSmoother = null;
    private int mTabCount = 1;
    private int mCurrentTabIndex = 0;
    private CharSequence[] mItemTexts = null;
    private TextPaint mTextPaint = null;
    private int mTextColor = Color.BLACK;
    private int mTextHintColor = Color.WHITE;
    private GradientDrawable mBackgroundDrawable = new GradientDrawable();
    private float mRadius = 0;
    private float mRadii[] = new float[]{0, 0, 0, 0, 0, 0, 0, 0};

    private OnTabChangedListener mOnTabChangedListener = null;

    private boolean mCurrentTabIndexDirty = true;

    public interface OnTabChangedListener {
        void onTabChanged(int tab);
    }

    public TabSwitcher(Context context) {
        this(context, null);
    }

    public TabSwitcher(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TabSwitcher(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        int paddingTop = getPaddingTop();
        int paddingBottom = getPaddingBottom();
        int paddingLeft = getPaddingStart();
        int paddingRight = getPaddingEnd();

        setupDefaultValues();

        mThumbTranslateSmoother = new Smoother2(
                Config.Animation.TAB_SWITCHER_SCROLL_ANIMATION_FACTOR,
                Config.Animation.TAB_SWITCHER_SCROLL_ANIMATION_ERROR);
        mThumbTranslateSmoother.setBypassed(Config.Animation.BYPASS_TAB_SWITCHER_ANIMATION);
        mTextPaint = new TextPaint();
        mTextPaint.setTextAlign(Align.CENTER);

        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CollageTabSwitcher, defStyle, 0);
        int loopSize = typedArray.getIndexCount();
        for (int i = 0; i < loopSize; i++) {
            int attributeKey = typedArray.getIndex(i);
            if (attributeKey == R.styleable.CollageTabSwitcher_collage_backgroundColor) {
                int backgrounColor = typedArray.getColor(attributeKey, Color.BLACK);
                mBackgroundDrawable.setColor(backgrounColor);
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_thumbColor) {
                int thumbColor = typedArray.getColor(attributeKey, Color.YELLOW);
                mDblThumb.setColor(thumbColor);
                //                mDblThumb.setBounds(0, 0, mDblThumb.getIntrinsicWidth(), mDblThumb.getIntrinsicHeight());
                mDblThumb.setState(ENABLED_STATE_SET);
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_tabCornerRadius) {
                mRadius = typedArray.getFloat(attributeKey, 0);
                mBackgroundDrawable.setCornerRadius(mRadius);
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_items) {
                mItemTexts = typedArray.getTextArray(attributeKey);
                mTabCount = mItemTexts.length;
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_textSize) {
                float textSize = mTextPaint.getTextSize();
                textSize = typedArray.getDimension(attributeKey, textSize);
                mTextPaint.setTextSize(textSize);
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_itemTextColor) {
                mTextColor = typedArray.getColor(attributeKey, mTextColor);
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_itemHintTextColor) {
                mTextHintColor = typedArray.getColor(attributeKey, mTextHintColor);
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_paddingLeft) {
                paddingLeft = typedArray.getDimensionPixelOffset(attributeKey, paddingLeft);
            } else if (attributeKey == R.styleable.CollageTabSwitcher_collage_paddingRight) {
                paddingRight = typedArray.getDimensionPixelOffset(attributeKey, paddingRight);
            }
        }
        typedArray.recycle();

        setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom);
        setBackground(mBackgroundDrawable);
    }

    private void setupDefaultValues() {
        mItemTexts = getResources().getTextArray(R.array.collage_tab_switcher_items);
        mTabCount = mItemTexts.length;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int backgroundWidth = 0;
        int backgroundHeight = 0;
        int widthSpecMode = MeasureSpec.getMode(widthMeasureSpec);
        int heightSpecMode = MeasureSpec.getMode(heightMeasureSpec);
        int widthSpecSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightSpecSize = MeasureSpec.getSize(heightMeasureSpec);
        int measuredWidth = 0;
        int measuredHeight = 0;

        Drawable background = getBackground();
        if (background != null) {
            backgroundWidth = background.getIntrinsicWidth();
            backgroundHeight = background.getIntrinsicHeight();
        }

        // Width
        switch (widthSpecMode) {
            case MeasureSpec.UNSPECIFIED:
                measuredWidth = backgroundWidth;
                break;
            case MeasureSpec.AT_MOST:
                measuredWidth = (backgroundWidth < widthSpecSize) ? backgroundWidth : widthSpecSize;
                break;
            case MeasureSpec.EXACTLY:
                measuredWidth = widthSpecSize;
                break;
            default:
                break;
        }

        // Height
        switch (heightSpecMode) {
            case MeasureSpec.UNSPECIFIED:
                measuredHeight = backgroundHeight;
                break;
            case MeasureSpec.AT_MOST:
                measuredHeight = (backgroundHeight < heightSpecSize) ? backgroundHeight : heightSpecSize;
                break;
            case MeasureSpec.EXACTLY:
                measuredHeight = heightSpecSize;
                break;
            default:
                break;
        }

        setMeasuredDimension(measuredWidth, measuredHeight);
        mDblThumb.setBounds(0, 0, (int) ((float) measuredWidth / (float) mTabCount), measuredHeight);

        mCurrentTabIndexDirty = true;
        //        redirectThumbPosition();
    }

    private void redirectThumbPosition() {
        float[] anchorPoints = computeAnchorPoint();
        float finalX = computeCurrentIndexAndPosition(anchorPoints);
        mThumbTranslateSmoother.setDestinationValue(
                finalX,
                (getHeight() - mDblThumb.getBounds().height()) * Constant.ONE_HALF_FLOAT);
        invalidate();
    }

    private final float computeCurrentIndexAndPosition(float[] anchorPoints) {
        float finalX = mThumbTranslateSmoother.getDestinationValueX();
        int pointsCount = anchorPoints.length;
        int index = 0;
        float minDistance = Float.MAX_VALUE;

        for (int i = 0; i < pointsCount; i++) {
            float distance = Math.abs(finalX - anchorPoints[i]);
            if (distance < minDistance) {
                minDistance = distance;
                index = i;
            }
        }

        mCurrentTabIndex = index;
        return anchorPoints[index];
    }

    private final float getUnitWidth() {
        return (getWidth() - getPaddingStart() - getPaddingEnd()) / (float) mTabCount;
    }

    /**
     * Compute thumb center anchor points
     *
     * @return
     */
    private final float[] computeAnchorPoint() {
        float viewWidth = getWidth() - getPaddingStart() - getPaddingEnd();
        float unit = viewWidth / (float) mTabCount;
        float pointsCount = mTabCount;
        float[] anchorPoints = new float[mTabCount];

        for (int i = 0; i < pointsCount; i++) {
            anchorPoints[i] = unit * (i + Constant.ONE_HALF_FLOAT) + getPaddingStart();
        }

        return anchorPoints;
    }

    private final int getTabIndexByPosition(float x, float[] anchorPoints) {
        int pointsCount = anchorPoints.length;
        for (int i = 0; i < pointsCount - 1; i++) {
            float border = (anchorPoints[i + 1] + anchorPoints[i]) * 0.5f;
            if (x < border) {
                return i;
            }
        }

        return pointsCount - 1;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int viewHeight = getHeight();
        float[] anchorPoints = computeAnchorPoint();
        float pointsCount = anchorPoints.length;
        float textBaseline = TextUtils.getTextVerticalCenterBaseline(viewHeight, mTextPaint);

        if (mCurrentTabIndexDirty) {
            mCurrentTabIndexDirty = false;
            mThumbTranslateSmoother.setDestinationValue(anchorPoints[mCurrentTabIndex], mThumbTranslateSmoother.getDestinationValueY());
        }

        canvas.save();

        // draw background text
        if (mItemTexts != null) {
            mTextPaint.setAlpha(MAX_ALPHA_INT);
            mTextPaint.setColor(mTextColor);
            for (int i = 0; i < pointsCount; i++) {
                int textLength = mItemTexts[i].length();
                canvas.drawText(mItemTexts[i],
                        0, textLength,
                        (int) (anchorPoints[i]), (int) textBaseline,
                        mTextPaint);
            }
        }

        // draw thumb
        Rect thumbBounds = mDblThumb.getBounds();
        int thumbWidth = thumbBounds.width();
        int thumbHeight = thumbBounds.height();
        float thumbX = 0;
        float thumbY = 0;

        if (mThumbTranslateSmoother.smooth()) {
            invalidate();
        }

        thumbX = mThumbTranslateSmoother.getCurrentValueX() - thumbWidth / 2f;
        thumbY = (viewHeight - thumbHeight) / 2f;

        if (mRadius != 0) {
            if (mCurrentTabIndex == 0) {
                mRadii[0] = mRadius;
                mRadii[1] = mRadius;
                mRadii[2] = 0;
                mRadii[3] = 0;
                mRadii[4] = 0;
                mRadii[5] = 0;
                mRadii[6] = mRadius;
                mRadii[7] = mRadius;
            } else if (mCurrentTabIndex == mTabCount - 1) {
                mRadii[0] = 0;
                mRadii[1] = 0;
                mRadii[2] = mRadius;
                mRadii[3] = mRadius;
                mRadii[4] = mRadius;
                mRadii[5] = mRadius;
                mRadii[6] = 0;
                mRadii[7] = 0;
            } else {
                mRadii[0] = 0;
                mRadii[1] = 0;
                mRadii[2] = 0;
                mRadii[3] = 0;
                mRadii[4] = 0;
                mRadii[5] = 0;
                mRadii[6] = 0;
                mRadii[7] = 0;
            }
        }
        mDblThumb.setCornerRadii(mRadii);
        canvas.save();
        canvas.translate(thumbX, thumbY);
        mDblThumb.draw(canvas);
        canvas.restore();

        // draw hint text
        if (mItemTexts != null) {
            float currentX = mThumbTranslateSmoother.getCurrentValueX();
            int index = getTabIndexByPosition(currentX, anchorPoints);
            CharSequence currentText = mItemTexts[index];
            int textLength = currentText.length();
            float currentAlpha = (1 - Math.abs(currentX - anchorPoints[index]) / (getUnitWidth() * 0.5f));
            int alphaBits = ((int) Math.abs(currentAlpha * MAX_ALPHA_INT)) << 24;

            mTextPaint.setColor((mTextHintColor & HINT_COLOR_MASK) | alphaBits);
            canvas.save();
            canvas.drawText(currentText,
                    0, textLength,
                    currentX, textBaseline,
                    mTextPaint);
            canvas.restore();
        }

        canvas.restore();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean state = false;
        Rect thumbBound = mDblThumb.getBounds();
        float viewWidth = getWidth();
        //        float viewHeight = getHeight();
        float thumbWidth = thumbBound.width();
        //        float thumbHeight = (thumbBound == null) ? 0 : thumbBound.height();
        float x = event.getX();
        //        float y = event.getY();

        // Overscroll animation defination
        if (Config.Animation.BYPASS_TAB_SWITCHER_OVERSCROLL_ANIMATION) {
            float offsetX = thumbWidth / 2f;
            //            float offsetY = thumbHeight / 2f;
            x = (x < offsetX) ? offsetX : ((x > viewWidth - offsetX) ? viewWidth - offsetX : x);
            //            y = (y < offsetY) ? offsetY : ((y > viewHeight - offsetY) ? viewHeight - offsetY : y);
        } else {
            x = (x < 0) ? 0 : ((x > viewWidth) ? viewWidth : x);
            //            y = (y < 0) ? 0 : ((y > viewHeight) ? viewHeight : y);
        }

        if (Config.Animation.BYPASS_TAB_SWITCHER_ANIMATION) {
            // Operation without any animation
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    mThumbTranslateSmoother.setDestinationValue(
                            x,
                            mThumbTranslateSmoother.getDestinationValueY());
                    redirectThumbPosition();
                    fireEvent();
                    break;
                default:
                    break;
            }
            state = true;
        } else {
            // Operation with animations
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                case MotionEvent.ACTION_MOVE:
                    mThumbTranslateSmoother.setDestinationValue(
                            x,
                            mThumbTranslateSmoother.getDestinationValueY());
                    state = true;
                    break;
                case MotionEvent.ACTION_CANCEL:
                case MotionEvent.ACTION_UP:
                    mThumbTranslateSmoother.setDestinationValue(
                            x,
                            mThumbTranslateSmoother.getDestinationValueY());
                    redirectThumbPosition();
                    fireEvent();
                    state = true;
                    break;
                default:
                    break;
            }
        }
        invalidate();
        return state;
    }

    private void fireEvent() {
        if (mOnTabChangedListener != null) {
            mOnTabChangedListener.onTabChanged(mCurrentTabIndex);
        }
    }

    public void setOnTabChangedListener(OnTabChangedListener listener) {
        mOnTabChangedListener = listener;
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        if (enabled) {
            mDblThumb.setState(ENABLED_STATE_SET);
        } else {
            mDblThumb.setState(EMPTY_STATE_SET);
        }
    }

    public int getSelection() {
        return mCurrentTabIndex;
    }

    public void setSelection(int index) {
        if (index >= mTabCount) {
            return;
        }

        mCurrentTabIndex = index;
        mCurrentTabIndexDirty = true;
        invalidate();
    }
}
