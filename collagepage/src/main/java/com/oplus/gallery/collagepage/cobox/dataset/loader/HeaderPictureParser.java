/***********************************************************
** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.oplus.gallery.collagepage.cobox.dataset.loader;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BitmapFactory.Options;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.NinePatchDrawable;
import android.util.DisplayMetrics;

import com.oplus.gallery.collagepage.Config;
import com.oplus.gallery.collagepage.cobox.kernel.Transform;
import com.oplus.gallery.collagepage.cobox.render.Picture;
import com.oplus.gallery.collagepage.cobox.render.view.HeaderPicture;
import com.oplus.gallery.collagepage.cobox.utils.GLog;
import com.oplus.gallery.collagepage.cobox.utils.TextUtils;
import com.oplus.gallery.foundation.util.graphic.ImageUtils;

import org.xmlpull.v1.XmlPullParser;

import java.io.IOException;
import java.io.InputStream;

/**
 * Header picture parser
 *
 * <AUTHOR>
 * @date 2015-04-18 11:47:42
 */
public class HeaderPictureParser extends PictureParser {

    private static final String TAG = "HeaderPictureParser";

    @Override
    protected Picture onPictureParse(Picture picture, XmlPullParser parser) {
        HeaderPicture headerPicture = (HeaderPicture) picture;

        float width = Float.valueOf((parser.getAttributeValue(null, Config.ResourceParse.ATTR_WIDTH)));
        float height = Float.valueOf((parser.getAttributeValue(null, Config.ResourceParse.ATTR_HEIGHT)));
        RectF translate = headerPicture.getTransform().mTranslate;

        translate.left = 0;
        translate.right = width;
        translate.top = 0;
        translate.bottom = height;
        return headerPicture;
    }

    @Override
    protected Picture onPictureDecode(Picture picture, AssetManager assetManager) {
        if (picture != null) {
            String src = TextUtils.concatPathWithRoot(picture.getSrc());
            boolean isNinePatchImage = ImageUtils.isNinePatchImage(src);
            Transform transform = picture.getTransform();
            int width = (int) transform.getWidth();
            int height = (int) transform.getHeight();
            Bitmap result = null;
            Options opts = new Options();

            try (InputStream sin = assetManager.open(src)) {
                if (isNinePatchImage) {
                    // Detect src bitmap size
                    opts.inJustDecodeBounds = true;
                    BitmapFactory.decodeStream(sin, null, opts);

                    // Setup downsample size
                    result = Bitmap.createBitmap(width, height,
                            Config.Render.BITMAP_DISPLAY_COLOR_SIZE);
                    opts.inPreferredConfig = Config.Render.BITMAP_DISPLAY_COLOR_SIZE;
                    opts.inJustDecodeBounds = false;
                    opts.inSampleSize = ImageUtils.adviseSampleSize(
                            opts.outWidth, opts.outHeight,
                            width, height, Config.ResourceParse.LOADER_PHOTO_QUALITY_DECODE);
                    opts.inBitmap = result;
                    opts.inTempStorage = getDecodingBuffer();

                    { // Decode bitmap
                        sin.reset();
                        Bitmap ninePatchBitmap = BitmapFactory.decodeStream(sin, null, opts);
                        if (ninePatchBitmap.getNinePatchChunk() == null) {
                            sin.reset();
                            result = BitmapFactory.decodeStream(sin, null, opts);
                            result = Bitmap.createScaledBitmap(result, width, height, true);
                            if ((opts.inBitmap != null) && !opts.inBitmap.isRecycled()) {
                                opts.inBitmap.recycle();
                                opts.inBitmap = null;
                            }
                        } else {
                            @SuppressWarnings("deprecation")
                            NinePatchDrawable drawable = new NinePatchDrawable(
                                    ninePatchBitmap,
                                    ninePatchBitmap.getNinePatchChunk(),
                                    new Rect(), "Asset 9-patch");
                            Canvas canvas = new Canvas(result);
                            drawable.setTargetDensity(DisplayMetrics.DENSITY_DEFAULT);
                            drawable.setBounds(0, 0, width, height);
                            drawable.setAlpha(transform.getAlpha());
                            canvas.drawColor(Color.TRANSPARENT);
                            drawable.draw(canvas);
                            drawable = null;
                            canvas = null;
                        }
                        opts = null;
                    }
                } else {
                    // Detect src bitmap size
                    opts.inJustDecodeBounds = true;
                    sin.reset();
                    BitmapFactory.decodeStream(sin, null, opts);

                    // Setup downsample size & scale factor
                    opts.inPreferredConfig = Config.Render.BITMAP_DISPLAY_COLOR_SIZE;
                    opts.inJustDecodeBounds = false;
                    opts.inSampleSize = ImageUtils.adviseSampleSize(
                            opts.outWidth, opts.outHeight,
                            width, height, Config.ResourceParse.LOADER_PHOTO_QUALITY_DECODE);
                    opts.inTempStorage = getDecodingBuffer();

                    // Decode bitmap
                    sin.reset();
                    result = BitmapFactory.decodeStream(sin, null, opts);
                    result = Bitmap.createScaledBitmap(result, width, height, true);
                    if ((opts.inBitmap != null) && !opts.inBitmap.isRecycled()) {
                        opts.inBitmap.recycle();
                        opts.inBitmap = null;
                    }
                }

            } catch (IOException e) {
                GLog.e(TAG, "[onPictureDecode] Cannot decode resource bitmap, e = " + e);
            } finally {
                picture.setResourceBitmap(result);
                picture.postInvalidate();
            }
        }
        return picture;
    }

}
