/***********************************************************
 * * Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.gallery.collagepage.cobox.render;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.RectF;

import com.oplus.gallery.collagepage.Config;
import com.oplus.gallery.collagepage.cobox.dataset.PictureDrum;
import com.oplus.gallery.collagepage.cobox.kernel.Transform;
import com.oplus.gallery.collagepage.cobox.utils.GLog;

/**
 * Picture unit
 *
 * <AUTHOR>
 * @date 2014-12-09 11:00:35
 */
public class Picture extends Renderable {

    private static final String TAG = "Picture";
    private static final int DEBUG_PAINT_COLOR = 0x7700FF00;
    private static final int DEBUG_TEXT_OFFSET = 30;

    private String mID = null;
    private String mSrc = null;
    private boolean mIsFillPolygon = true;
    private Type mType = Type.UNDEFINE;
    private PictureDrum mPictureDrum = null;
    private Paint mDebugPaint = null;
    private Transform mBackupTransform = null;
    private OnClickedListener mOnClickedListener = null;
    private OnSelectedListener mOnSelectedListener = null;
    private OnHoveredListener mOnHoveredListener = null;
    private OnLongPressedListener mOnLongPressedListener = null;

    public enum Type {
        BACKGROUND("Background", 0),
        MASK("Mask", 1),
        FOREGROUND("Foreground", 2),
        FRAME("Frame", 3),
        WIDGET("Widget", 4),
        HEADER("Header", 5),
        FOOTER("Footer", 6),
        VERTICAL("Vertical", 7),
        HORIZONTAL("Horizontal", 8),
        UNDEFINE("Undefine", -1);

        private String mName = null;
        private int mPriority = 0;

        Type(String name, int priority) {
            mName = name;
            mPriority = priority;
        }

        public int getPriority() {
            return mPriority;
        }

        public String getName() {
            return mName;
        }
    }

    /**
     * Bitmap wrap function
     *
     * <AUTHOR>
     * @date 2014-12-12 15:12:28
     */
    public enum WrapMode {
        /**
         * No function work on Bitmap
         */
        None,

        /**
         * Repeat the edge single pixel line to fill render rectangle
         */
        Clamp,

        /**
         * Repeat all Bitmap to fill render rectangle
         */
        Repeat
    }

    /**
     * <AUTHOR>
     * @date 2015-04-23 15:03:52
     */
    public interface OnClickedListener {
        void onClicked(Picture picture, float x, float y);
    }

    /**
     * <AUTHOR>
     * @date 2015-04-23 15:03:52
     */
    public interface OnSelectedListener {
        void onSelected(Picture picture);
    }

    /**
     * <AUTHOR>
     * @date 2015-04-23 15:03:52
     */
    public interface OnHoveredListener {
        void onHovered(Picture picture, float x, float y);
    }

    /**
     * <AUTHOR>
     * @date 2015-05-08 15:02:40
     */
    public interface OnLongPressedListener {
        void onLongPressed(Picture picture, float x, float y);
    }

    public Picture() {
        mPictureDrum = new PictureDrum();
        mDebugPaint = new Paint();
        mDebugPaint.setStyle(Style.FILL);
        mDebugPaint.setColor(DEBUG_PAINT_COLOR);
    }

    public final String getSrc() {
        return mSrc;
    }

    public final String getID() {
        return mID;
    }

    public final void setID(String id) {
        mID = id;
    }

    public final void setSrc(String src) {
        mSrc = src;
    }

    public final void setFillPolygon(boolean isFillPolygon) {
        mIsFillPolygon = isFillPolygon;
    }

    public final void setType(Type type) {
        mType = type;
    }

    /**
     * Transparency, value range from 0 to 255
     *
     * @param alpha
     */
    public final void setAlpha(int alpha) {
        getTransform().setAlpha(alpha);
    }

    public final void setDepth(int depth) {
        getTransform().setDepth(depth);
    }

    /**
     * Angle of picture, value range from -360 to 360
     *
     * @param rotate
     */
    public final void setRotate(float rotate) {
        getTransform().setRotate(rotate);
    }

    public final void setLeft(float left) {
        getTransform().setLeft(left);
    }

    public final void setTop(float top) {
        getTransform().setTop(top);
    }

    public final void setRight(float right) {
        getTransform().setRight(right);
    }

    public final void setBottom(float bottom) {
        getTransform().setBottom(bottom);
    }

    public final void setPaddingLeft(float left) {
        getTransform().setPaddingLeft(left);
    }

    public final void setPaddingTop(float top) {
        getTransform().setPaddingTop(top);
    }

    public final void setPaddingRight(float right) {
        getTransform().setPaddingRight(right);
    }

    public final void setPaddingBottom(float bottom) {
        getTransform().setPaddingBottom(bottom);
    }

    public void setAlignParentLeft(boolean enabled) {
        getTransform().setAlignParentLeft(enabled);
    }

    public void setAlignParentRight(boolean enabled) {
        getTransform().setAlignParentRight(enabled);
    }

    public void setAlignParentTop(boolean enabled) {
        getTransform().setAlignParentTop(enabled);
    }

    public void setAlignParentBottom(boolean enabled) {
        getTransform().setAlignParentBottom(enabled);
    }

    public void setWidth(float width) {
        Transform transform = getTransform();
        transform.setLeft(0);
        transform.setRight(width);
    }

    public float getWidth() {
        return getTransform().getWidth();
    }

    public void setHeight(float height) {
        Transform transform = getTransform();
        transform.setTop(0);
        transform.setBottom(height);
    }

    public float getHeight() {
        return getTransform().getHeight();
    }

    public final boolean isFillPolygon() {
        return mIsFillPolygon;
    }

    public final Type getType() {
        return mType;
    }

    public PictureDrum getPictureDrum() {
        return mPictureDrum;
    }

    public void setResourceBitmap(Bitmap result) {
        mPictureDrum.mImage = result;
    }

    @Override
    public void recycle() {
        super.recycle();
        if (mPictureDrum != null) {
            mPictureDrum.recycle();
        }
    }

    @Override
    public String toString() {
        return String.format(this.getClass().getSimpleName() + "(ID = %s, IsFillPolygon = %s, Transform = %s)",
                mID, Boolean.toString(mIsFillPolygon), getTransform().toString());
    }

    ///////////////
    //   Render  //
    ///////////////

    private void drawDebug(Canvas canvas) {
        // Debug Transfrom Area
        Transform transform = getTransform();
        GLog.drawBlock(canvas, transform.mTranslate, Config.GLog.PICTURE_BLOCK_COLOR);
        GLog.drawText(canvas,
                transform.mTranslate.left + DEBUG_TEXT_OFFSET, transform.mTranslate.top + DEBUG_TEXT_OFFSET,
                TAG, Config.GLog.BLOCK_TEXT_SIZE, Config.GLog.BLOCK_TEXT_COLOR);

        // Debug Touch Area
        RectF touchArea = transform.getAABBBox();
        GLog.drawBlock(canvas, touchArea, Config.GLog.TOUCH_AREA_BLOCK_COLOR);
    }

    @Override
    public boolean updateTransform(long time, long deltaTime) {
        // Do nothing
        return false;
    }

    @Override
    public void onOffscreenStart() {
        // Do nothing
    }

    @Override
    @SuppressLint("WrongCall")
    public boolean draw(Canvas canvas) {
        return onDraw(canvas);
    }

    @Override
    public boolean onDraw(Canvas canvas) {
        // Do nothing
        readyToRender();
        return false;
    }

    @Override
    public boolean drawOverlay(Canvas canvas) {
        return onDrawOverlay(canvas);
    }

    @Override
    public boolean onDrawOverlay(Canvas canvas) {
        canvas.save();
        drawDebug(canvas);
        canvas.restore();
        return false;
    }

    @Override
    public void onOffscreenEnd() {
        // Do nothing
    }

    public void reset() {
        if (mBackupTransform != null) {
            getTransform().concat(mBackupTransform);
        }
    }

    public void makeBackup() {
        mBackupTransform = new Transform();
        mBackupTransform.concat(getTransform());
    }

    ///////////////
    //   Event   //
    ///////////////

    protected void fireOnClickedEvent(final float x, final float y) {
        if (mOnClickedListener != null) {
            mOnClickedListener.onClicked(this, x, y);
        }
    }

    public void setOnClickedListener(OnClickedListener listener) {
        mOnClickedListener = listener;
    }

    protected void fireOnSelectedEvent() {
        if (mOnSelectedListener != null) {
            mOnSelectedListener.onSelected(this);
        }
    }

    public void setOnSelectedListener(OnSelectedListener listener) {
        mOnSelectedListener = listener;
    }

    protected void fireOnHoveredEvent(final float x, final float y) {
        if (mOnHoveredListener != null) {
            mOnHoveredListener.onHovered(this, x, y);
        }
    }

    public void setOnHoveredListener(OnHoveredListener listener) {
        mOnHoveredListener = listener;
    }

    protected void fireOnHoveredLeaveEvent(final float x, final float y) {
        if (mOnHoveredListener != null) {
            mOnHoveredListener.onHovered(null, x, y);
        }
    }

    protected void fireOnLongPressedEvent(final float x, final float y) {
        if (mOnLongPressedListener != null) {
            mOnLongPressedListener.onLongPressed(this, x, y);
        }
    }

    public void setOnLongPressedListener(OnLongPressedListener listener) {
        mOnLongPressedListener = listener;
    }

}
