/***********************************************************
** Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.oplus.gallery.collagepage.view;

import android.graphics.Canvas;
import android.graphics.PointF;
import android.graphics.PorterDuff.Mode;
import android.graphics.RectF;

import com.oplus.gallery.collagepage.Config;
import com.oplus.gallery.collagepage.cobox.kernel.Transform;
import com.oplus.gallery.collagepage.cobox.render.CoBox;
import com.oplus.gallery.collagepage.cobox.render.FixedCollageLayout;
import com.oplus.gallery.collagepage.cobox.render.Page;
import com.oplus.gallery.collagepage.cobox.render.Picture;
import com.oplus.gallery.collagepage.cobox.render.Picture.OnHoveredListener;
import com.oplus.gallery.collagepage.cobox.render.Picture.OnSelectedListener;
import com.oplus.gallery.collagepage.cobox.render.Renderable;
import com.oplus.gallery.collagepage.cobox.render.view.FloatPicture;
import com.oplus.gallery.collagepage.cobox.render.view.MaskedPicture;
import com.oplus.gallery.foundation.util.graphic.ImageUtils;

import java.util.Iterator;
import java.util.List;

/**
 * Template Collage Page for logical layer
 *
 * <AUTHOR>
 * @date 2015-04-23 10:09:19
 */
public class CollageTemplatePage extends FixedCollageLayout implements Page,
        OnSelectedListener,
        OnHoveredListener {

    private static final String TAG = "CollageTemplatePage";

    private boolean mIsFiredSolutionLoadedEvent = false;
    private FloatPicture mFloatPicture = null;
    private MaskedPicture mActivedPicture = null; // The first DOWN picture
    private MaskedPicture mDestinationPicture = null; // Waiting for WRAPing picuture

    public CollageTemplatePage() {
        super();
    }

    @Override
    protected void onInflatedCompleted() {
        super.onInflatedCompleted();
        checkFloatPicture();
    }

    private void checkFloatPicture() {
        if (mFloatPicture == null) {
            mFloatPicture = new FloatPicture();
            mFloatPicture.setDepth(Config.TemplateCollage.FLOAT_PICTURE_DEFAULT_DEPTH);
            addRenderable(mFloatPicture);
        }
    }

    @Override
    protected void onResetReadyToRender() {
        super.onResetReadyToRender();
        mIsFiredSolutionLoadedEvent = false;
    }

    @Override
    protected void setCoBox(CoBox box) {
        super.setCoBox(box);
        checkFloatPicture();
    }

    /**
     * Bind selected masked picture to floating picture
     * and show this floating picture
     *
     * @param maskedPicture
     */
    private void bindFloatPicture(MaskedPicture maskedPicture) {
        mActivedPicture = maskedPicture;

        if (mActivedPicture != null) {
            // [ReadLock][Get transform and photo]
            mActivedPicture.readLock();
            try {
                // Setup float picture transform
                if (mFloatPicture != null) {
                    RectF floatPictureTranslate = mFloatPicture.getTransform().getTranslateRect();
                    RectF activedPictureTranslate = mActivedPicture.getTransform().getTranslateRect();
                    float imageWidth = mActivedPicture.getPictureDrum().mPhoto.getWidth();
                    float imageHeight = mActivedPicture.getPictureDrum().mPhoto.getHeight();
                    float floatPictureScale = ImageUtils.scaleImage(
                            (int) imageWidth,
                            (int) imageHeight,
                            (int) activedPictureTranslate.width(),
                            (int) activedPictureTranslate.height(),
                            ImageUtils.SCALE_MODE_INSIDE);

                    floatPictureTranslate.set(
                            floatPictureTranslate.left, floatPictureTranslate.top,
                            floatPictureTranslate.left + floatPictureScale * imageWidth,
                            floatPictureTranslate.top + floatPictureScale * imageHeight);
                    mFloatPicture.setPhoto(mActivedPicture.getPictureDrum().mPhoto);
                    mFloatPicture.hide();
                }
            } finally {
                mActivedPicture.readUnlock();
            }

            // Hide photo of the actived masked picture
            mActivedPicture.hidePhoto();
        }
    }

    /**
     * Unbind selected masked picture from floating picture
     * and hide this floating picture
     */
    private void unbindFloatPicture() {
        if (mActivedPicture != null) {
            mActivedPicture.writeLock();
            try {
                // Swap photo
                if (mDestinationPicture != null) {
                    // [WriteLock][Modify photo of picture]
                    mDestinationPicture.swapPhoto(mActivedPicture);
                }

                // [WriteLock][Modify photo paint alpha]
                mActivedPicture.showPhoto();
            } finally {
                mActivedPicture.writeUnlock();
            }

            mFloatPicture.setPhoto(null);
            mActivedPicture = null;
            mDestinationPicture = null;
        }
    }

    private void moveFloatPictureTo(float x, float y) {
        boolean isPhotoValid = false;

        if (mFloatPicture == null) {
            return;
        }

        if (mActivedPicture != null) {
            mActivedPicture.readLock();
            try {
                // [ReadLock][Read photo of mActivedPicture]
                isPhotoValid = mFloatPicture.getPictureDrum().isPhotoValid();
            } finally {
                mActivedPicture.readUnlock();
            }
        } else {
            isPhotoValid = mFloatPicture.getPictureDrum().isPhotoValid();
        }

        if ((mFloatPicture != null) && isPhotoValid) {
            PointF orginal = getTransform().unprojectToOrginal(x, y);
            Transform transform = mFloatPicture.getTransform();
            float pictureWidth = transform.getWidth() * transform.mScale;
            float pictureHeight = transform.getHeight() * transform.mScale;
            transform.mTranslate.offsetTo(
                    orginal.x - pictureWidth / 2f,
                    orginal.y - pictureHeight / 2f);
            mFloatPicture.show();
        }
    }

    @Override
    public void onRenderableAdded(Renderable renderable) {
        super.onRenderableAdded(renderable);
        if (renderable instanceof MaskedPicture) {
            MaskedPicture picture = (MaskedPicture) renderable;
            picture.setOnSelectedListener(this);
            picture.setOnHoveredListener(this);
        }
    }

    @Override
    public boolean updateTransform(long time, long deltaTime) {
        return super.updateTransform(time, deltaTime);
    }

    @Override
    public boolean onDraw(Canvas canvas) {
        boolean hasMoreFrames = super.onDraw(canvas);
        if (Config.Animation.ENABLE_ALL_LOADED_DEBUT) {
            if (!isAllChildrenLoaded()) {
                canvas.drawColor(Config.Render.RENDER_CLEAR_COLOR, Mode.DST);
                mIsFiredSolutionLoadedEvent = false;
            } else {
                if (!mIsFiredSolutionLoadedEvent) {
                    fireOnSolutionLoadedCompleted();
                    mIsFiredSolutionLoadedEvent = true;
                    invaliate();
                }
            }
        }
        return hasMoreFrames;
    }

    private boolean isAllChildrenLoaded() {
        boolean isAllChildrenLoaded = true;
        List<Renderable> childrenList = getChildren();
        Iterator<Renderable> childItr = childrenList.iterator();
        while (childItr.hasNext()) {
            Renderable child = childItr.next();
            boolean isReadyToRender = child.isReadyToRender();
            isAllChildrenLoaded &= isReadyToRender;
        }
        return isAllChildrenLoaded;
    }

    @Override
    public void onSelected(Picture picture) {
        if (picture instanceof MaskedPicture) {
            bindFloatPicture((MaskedPicture) picture);
        }
    }

    @Override
    public void onHovered(Picture picture, float x, float y) {
        if (picture instanceof MaskedPicture) {
            mDestinationPicture = (MaskedPicture) picture;
        } else if (picture == null) {
            mDestinationPicture = mActivedPicture;
        }
    }

    @Override
    public boolean onHover(float x, float y) {
        moveFloatPictureTo(x, y);
        return super.onHover(x, y);
    }

    @Override
    public boolean onHoverLeave(float x, float y) {
        unbindFloatPicture();
        return super.onHoverLeave(x, y);
    }

    @Override
    public void onPageLoaded() {
        // TODO Auto-generated method stub
    }

    @Override
    public void onPageResume() {
        // TODO Auto-generated method stub
    }

    @Override
    public void onPagePause() {
        // TODO Auto-generated method stub
    }

    @Override
    public void onPageUnloaded() {
        // TODO Auto-generated method stub
    }

    @Override
    public void setPageEnabled(boolean enabled) {
        // TODO Auto-generated method stub
    }

    @Override
    public boolean getPageEnabled() {
        // TODO Auto-generated method stub
        return true;
    }

    @Override
    public boolean isBusy() {
        // TODO Auto-generated method stub
        return false;
    }

    @Override
    public void setOnBusyListener(OnBusyListener listener) {
        // TODO Auto-generated method stub
    }

}
