<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/photo_editor_ui_framework"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:splitMotionEvents="false">

    <RelativeLayout
        android:id="@+id/default_preview_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/toolbar_container"
        android:layout_alignParentTop="true"
        android:layout_marginTop="@dimen/picture3d_editor_preview_margin_top" />

    <View
        android:id="@+id/left_safe_area"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true" />

    <View
        android:id="@+id/right_safe_area"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true" />

    <View
        android:id="@+id/right_masking"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_above="@id/toolbar_container"
        android:layout_alignParentRight="true"
        android:background="@color/picture3d_editor_title_bar_background"
        android:visibility="gone" />

    <com.oplus.gallery.photoeditor.widget.EditorCompareButton
        android:id="@+id/compare_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@id/default_preview_area"
        android:layout_alignParentEnd="true"
        android:layout_gravity="bottom"
        android:layout_marginEnd="@dimen/picture3d_editor_compare_button_margin_end_portrait" />

    <com.oplus.gallery.photoeditor.widget.layout.ControlledRelativeLayout
        android:id="@+id/toolbar_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/picture3d_bottom_container_bar_height"
        android:layout_gravity="bottom"
        android:layout_above="@+id/title_bar_container"
        android:layout_marginTop="@dimen/picture3d_editor_toolbar_container_margin_top"
        android:forceDarkAllowed="false" />

    <com.oplus.gallery.photoeditor.widget.layout.ControlledRelativeLayout
        android:id="@+id/title_bar_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/picture3d_bottom_action_bar_height"
        android:layout_alignParentBottom="true"
        android:layout_gravity="top"
        android:paddingTop="@dimen/picture3d_editor_title_bar_container_margin_top" />

</RelativeLayout>