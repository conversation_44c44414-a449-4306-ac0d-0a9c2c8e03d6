<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingBottom="@dimen/picture3d_group_photo_guide_padding_bottom">

    <com.coui.appcompat.dialog.widget.COUIMaxHeightNestedScrollView
        style="@style/COUIAlertDialogCustomScrollViewStyleWithButton"
        android:layout_marginBottom="@dimen/picture3d_ai_repair_introduction_button_margin_top">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <com.coui.appcompat.viewpager.COUIViewPager2
                android:id="@+id/pager_aihd"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </LinearLayout>
    </com.coui.appcompat.dialog.widget.COUIMaxHeightNestedScrollView>

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/btn_aihd_ok"
        style="?attr/couiButtonColorfulDefaultStyle"
        android:layout_width="@dimen/base_large_btn_width"
        android:layout_height="@dimen/base_large_btn_height"
        android:layout_gravity="center"
        android:contentDescription="@string/common_ok"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/common_ok"
        android:textSize='@dimen/base_multiple_page_guides_button_text_size' />
</LinearLayout>