<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/style_item_divider_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/picture3d_editor_watermark_master_style_dividing_margin_top"
        android:layout_marginBottom="@dimen/picture3d_editor_watermark_master_style_dividing_margin_bottom"
        android:layout_marginHorizontal="@dimen/picture3d_editor_watermark_master_style_dividing_margin_horizontal"
        android:src="@drawable/dividing_line_land"
        android:visibility="gone" />

    <com.coui.appcompat.cardview.COUICardView
        android:id="@+id/style_item_card_view"
        android:layout_width="@dimen/picture3d_editor_watermark_master_style_list_item_land_width"
        android:layout_height="@dimen/picture3d_editor_watermark_master_style_list_item_land_height"
        android:background="@drawable/watermark_master_style_list_item_background"
        android:forceDarkAllowed="false"
        app:cardCornerRadius="@dimen/picture3d_editor_watermark_master_style_list_item_border_out_radius">

        <com.coui.appcompat.cardview.COUICardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/picture3d_editor_watermark_master_style_list_item_padding_land"
            android:backgroundTint="@color/picture3d_editor_watermark_style_item_background_color"
            app:cardCornerRadius="@dimen/picture3d_editor_watermark_master_style_list_item_in_radius_land">

            <ImageView
                android:id="@+id/style_item_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ImageView
                android:id="@+id/style_item_mask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:alpha="0.4"
                android:background="@color/black"
                android:visibility="gone" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/style_item_loading"
                android:layout_width="@dimen/picture3d_editor_watermark_master_style_item_loading_width_land"
                android:layout_height="@dimen/picture3d_editor_watermark_master_style_item_loading_height_land"
                android:layout_gravity="center"
                android:visibility="gone"
                app:lottie_autoPlay="true"
                app:lottie_fileName="icon-loading.json"
                app:lottie_loop="true"
                app:lottie_speed="4" />

            <com.coui.appcompat.progressbar.COUICircularProgressBar
                android:id="@+id/download_progress_bar"
                style="@style/Widget.COUI.COUICircularProgressBar.Medium.OnImage"
                android:layout_width="@dimen/picture3d_editor_watermark_master_style_item_loading_width_land"
                android:layout_height="@dimen/picture3d_editor_watermark_master_style_item_loading_height_land"
                android:layout_gravity="center"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/style_item_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/picture3d_editor_ic_watermark_master_edit"
                android:visibility="gone" />
        </com.coui.appcompat.cardview.COUICardView>

    </com.coui.appcompat.cardview.COUICardView>
</LinearLayout>