<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingBottom="@dimen/picture3d_eliminate_tips_content_padding_bottom"
    tools:ignore="ResourceName">

    <com.oplus.gallery.foundation.ui.coui.LimitSizeSquareView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/picture3d_eliminate_tips_content_text_padding_start"
        android:layout_marginEnd="@dimen/picture3d_eliminate_tips_content_text_padding_end"
        app:maxSize="@dimen/picture3d_eliminate_tips_anim_vertical_size"
        app:minSize="@dimen/picture3d_group_photo_guide_round_frame_min_size">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.coui.appcompat.imageview.COUIRoundImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/picture3d_ai_eliminate_introduction_anim_bg"
                app:couiBorderRadius="@dimen/picture3d_eliminate_tips_content_anim_radius"
                app:couiType="round" />

            <com.oplus.anim.EffectiveAnimationView
                android:id="@+id/effective_anim_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:anim_loop="true"
                app:anim_repeatMode="restart" />
        </FrameLayout>
    </com.oplus.gallery.foundation.ui.coui.LimitSizeSquareView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/picture3d_eliminate_tips_content_text_margin_top"
        android:minHeight="@dimen/picture3d_eliminate_tips_content_min_height"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_introduction_icon"
            android:layout_width="@dimen/picture3d_editor_image_quality_enhance_icon_vertical_size"
            android:layout_height="@dimen/picture3d_editor_image_quality_enhance_icon_vertical_size"
            android:layout_gravity="center_horizontal"
            android:contentDescription="@null"
            android:scaleType="centerCrop" />

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/tv_introduction_title"
            style="@style/COUIAlertDialogTitleStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:layout_marginTop="@dimen/picture3d_editor_image_quality_enhance_title_text_margin_top"
            android:paddingStart="@dimen/picture3d_eliminate_tips_content_text_padding_start"
            android:paddingEnd="@dimen/picture3d_eliminate_tips_content_text_padding_end"
            android:textColor="?attr/couiColorLabelPrimary"
            tools:text="@string/picture3d_editor_text_eliminate_ai_circle_selection" />

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/tv_introduction_desc"
            style="@style/COUIAlertDialogMessageStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/picture3d_eliminate_tips_content_desc_text_margin_top"
            android:gravity="center_horizontal"
            android:textColor="?attr/couiColorSecondNeutral"
            tools:text="@string/picture3d_editor_text_eliminate_introduction_circle_selection_desc" />
    </LinearLayout>
</LinearLayout>