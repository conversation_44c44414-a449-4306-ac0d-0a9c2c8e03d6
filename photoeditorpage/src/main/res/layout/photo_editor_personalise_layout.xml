<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/photo_editor_personalise_framework"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:forceDarkAllowed="false"
    android:splitMotionEvents="false">

    <RelativeLayout
        android:id="@+id/rl_preview_area_background"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/cl_tool_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/rl_preview_and_tool_container_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/picture3d_editor_personalise_preview_margin_top"
        app:layout_constraintBottom_toTopOf="@id/rl_bottom_tool_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/rl_preview_container_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/picture3d_editor_personalise_preview_margin_horizontal"
        android:layout_marginTop="@dimen/picture3d_editor_personalise_preview_margin_top"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toTopOf="@id/cl_tool_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/cl_tool_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/picture3d_editor_personalise_bg_color"
        app:layout_constraintBottom_toTopOf="@id/rl_bottom_tool_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
            android:id="@+id/lv_picture_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginTop="@dimen/picture3d_editor_personalise_picture_list_margin_top"
            android:layout_marginBottom="@dimen/picture3d_editor_personalise_picture_list_margin_bottom"
            android:orientation="horizontal"
            android:visibility="gone"
            app:base_itemSpacing="@dimen/picture3d_editor_personalise_picture_list_menu_common_spacing" />

        <com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
            android:id="@+id/lv_text_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginHorizontal="@dimen/picture3d_editor_personalise_text_list_menu_margin_horizontal"
            android:layout_marginTop="@dimen/picture3d_editor_personalise_text_list_menu_margin_top"
            android:layout_marginBottom="@dimen/picture3d_editor_personalise_text_list_menu_margin_bottom"
            android:orientation="vertical"
            android:visibility="gone"
            app:base_itemSpacing="@dimen/picture3d_editor_personalise_text_list_menu_common_spacing"
            app:layout_constraintHeight_max="@dimen/picture3d_editor_personalise_text_list_menu_max_height"
            app:noPaddingFadingEdge="0dp" />

        <LinearLayout
            android:id="@+id/rl_background_color_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginTop="@dimen/picture3d_editor_personalise_bg_color_list_margin_top"
            android:layout_marginBottom="@dimen/picture3d_editor_personalise_bg_color_list_margin_bottom"
            android:visibility="gone">

            <com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget.WatermarkPersonalizedEditMenuItemView
                android:id="@+id/fl_white_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/spring_festival_watermark_text_margin_left_right"
                android:layout_marginRight="@dimen/spring_festival_watermark_text_margin_left_right"
                android:clickable="true">

                <ImageView
                    android:id="@+id/iv_white_color_ring"
                    android:layout_width="@dimen/picture3d_editor_personalise_bg_color_item_ring_length"
                    android:layout_height="@dimen/picture3d_editor_personalise_bg_color_item_ring_length"
                    android:layout_gravity="center"
                    android:background="@drawable/picture3d_editor_personalise_ring_drawable" />

                <ImageView
                    android:id="@+id/iv_white_color"
                    android:layout_width="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_height="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_gravity="center"
                    android:scaleType="center"
                    android:src="@drawable/picture3d_editor_personalise_white_round" />

            </com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget.WatermarkPersonalizedEditMenuItemView>

            <com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget.WatermarkPersonalizedEditMenuItemView
                android:id="@+id/fl_black_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/spring_festival_watermark_text_margin_left_right"
                android:layout_marginRight="@dimen/spring_festival_watermark_text_margin_left_right"
                android:clickable="true">

                <ImageView
                    android:id="@+id/iv_black_color_ring"
                    android:layout_width="@dimen/picture3d_editor_personalise_bg_color_item_ring_length"
                    android:layout_height="@dimen/picture3d_editor_personalise_bg_color_item_ring_length"
                    android:layout_gravity="center"
                    android:background="@drawable/picture3d_editor_personalise_ring_drawable" />

                <ImageView
                    android:id="@+id/iv_black_color"
                    android:layout_width="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_height="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_gravity="center"
                    android:scaleType="center"
                    android:src="@drawable/picture3d_editor_personalise_black_round" />
            </com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget.WatermarkPersonalizedEditMenuItemView>

            <com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget.WatermarkPersonalizedEditMenuItemView
                android:id="@+id/fl_colorful_color"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/spring_festival_watermark_text_margin_left_right"
                android:layout_marginRight="@dimen/spring_festival_watermark_text_margin_left_right"
                android:clickable="true">

                <ImageView
                    android:id="@+id/iv_colorful_color_ring"
                    android:layout_width="@dimen/picture3d_editor_personalise_bg_color_item_ring_length"
                    android:layout_height="@dimen/picture3d_editor_personalise_bg_color_item_ring_length"
                    android:layout_gravity="center"
                    android:background="@drawable/picture3d_editor_personalise_ring_drawable" />

                <com.coui.appcompat.imageview.COUIRoundImageView
                    android:id="@+id/iv_colorful_color"
                    android:layout_width="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_height="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_gravity="center"
                    android:scaleType="center"
                    android:src="@drawable/picture3d_ai_eliminate_introduction_anim_bg" />

                <ImageView
                    android:id="@+id/iv_colorful_color_cover_ring"
                    android:layout_width="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_height="@dimen/picture3d_editor_personalise_bg_color_item_image_length"
                    android:layout_gravity="center"
                    android:background="@drawable/picture3d_editor_personalise_colorful_round" />
            </com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget.WatermarkPersonalizedEditMenuItemView>

        </LinearLayout>

        <com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
            android:id="@+id/lv_texture_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginTop="@dimen/picture3d_editor_personalise_picture_list_margin_top"
            android:layout_marginBottom="@dimen/picture3d_editor_personalise_picture_list_margin_bottom"
            android:orientation="horizontal"
            android:visibility="gone"
            app:base_itemSpacing="@dimen/picture3d_editor_personalise_picture_list_menu_common_spacing" />

        <com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
            android:id="@+id/lv_restrict_bg_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginTop="@dimen/picture3d_editor_personalise_picture_list_margin_top"
            android:layout_marginBottom="@dimen/picture3d_editor_personalise_picture_list_margin_bottom"
            android:orientation="horizontal"
            android:visibility="gone"
            app:base_itemSpacing="@dimen/picture3d_editor_personalise_picture_list_menu_common_spacing" />

        <include
            layout="@layout/photo_editor_personalise_tool_container_composition_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginTop="@dimen/picture3d_editor_personalise_composition_list_menu_margin_top"
            android:layout_marginBottom="@dimen/picture3d_editor_personalise_composition_list_menu_margin_bottom" />

    </FrameLayout>

    <RelativeLayout
        android:id="@+id/rl_bottom_tool_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/picture3d_editor_personalise_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <include
            android:id="@+id/il_bottom_tool_layout"
            layout="@layout/photo_editor_personalise_bottom_tool_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>