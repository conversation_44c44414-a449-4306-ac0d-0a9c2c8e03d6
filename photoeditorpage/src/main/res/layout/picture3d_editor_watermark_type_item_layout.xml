<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/watermark_chip_layout_height">

    <com.coui.appcompat.button.COUIButton
        style="?attr/couiSmallButtonColorStyle"
        android:id="@+id/watermark_chip"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/watermark_chip_layout_height"
        android:drawablePadding="@dimen/picture3d_editor_privacy_watermark_red_point_text_padding"
        android:lineSpacingMultiplier="@dimen/picture3d_editor_watermark_list_item_line_spacing"
        android:paddingStart="@dimen/watermark_normal_chip_text_padding_start"
        android:paddingEnd="@dimen/watermark_normal_chip_text_padding_end"
        android:textSize="@dimen/watermark_hasselblad_chip_text_size"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:drawableRadius="@dimen/watermark_normal_chip_text_drawable_radius" />

    <com.coui.appcompat.reddot.COUIHintRedDot
        android:id="@+id/watermark_red_dot"
        android:layout_width="@dimen/picture3d_editor_privacy_watermark_red_point_with"
        android:layout_height="@dimen/picture3d_editor_privacy_watermark_red_point_height"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginRight="@dimen/picture3d_editor_privacy_watermark_red_point_top"
        android:layout_marginTop="@dimen/picture3d_editor_privacy_watermark_red_point_right"
        app:couiHintRedPointMode="ponitOnly" />

</androidx.constraintlayout.widget.ConstraintLayout>
