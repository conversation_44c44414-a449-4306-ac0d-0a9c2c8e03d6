<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
        android:id="@+id/editor_id_text_action_cancel"
        style="@style/PictureBottomActionBarTextDone"
        android:layout_marginStart="@dimen/picture3d_bottom_action_bar_text_button_margin_start"
        android:gravity="center_horizontal"
        android:hint="@string/hint_button_talkback"
        android:text="@string/common_cancel" />

    <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
        android:id="@+id/editor_id_text_action_save"
        style="@style/PictureBottomActionBarTextDone"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/picture3d_bottom_action_bar_text_button_margin_start"
        android:enabled="false"
        android:gravity="center_horizontal"
        android:hint="@string/hint_button_talkback"
        android:text="@string/picture3d_button_save" />

    <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
        android:id="@+id/editor_id_text_action_recover"
        style="@style/PictureBottomActionBarTextDone"
        android:textColor="@drawable/picture3d_editor_recover_text_selector"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/picture3d_bottom_action_bar_text_button_margin_start"
        android:enabled="false"
        android:text="@string/picture3d_button_recover" />

    <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
        android:id="@+id/editor_id_text_action_done"
        style="@style/PictureBottomActionBarTextDone"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/picture3d_bottom_action_bar_text_button_margin_start"
        android:text="@string/picture3d_aiidphoto_done"
        android:visibility="invisible" />

    <RelativeLayout
        android:id="@+id/editor_id_center_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:orientation="horizontal">

        <com.oplus.gallery.standard_lib.ui.SuitableSizeTextView
            android:id="@+id/editor_id_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:ellipsize="end"
            android:gravity="center"
            android:textAppearance="@style/gTextAppearanceHeadline6"
            android:textColor="@color/picture3d_editor_clickable_menu_text_color_disable"
            android:visibility="gone"
            android:clickable="true"
            app:textSizeLevel="G2" />

        <ImageView
            android:id="@+id/editor_id_action_restore"
            style="@style/PictureBottomActionBarIcon"
            android:layout_centerHorizontal="true"
            android:contentDescription="@string/talkback_picture_button_reset"
            android:src="@drawable/picture3d_editor_ic_restore"
            android:visibility="gone" />

        <com.oplus.gallery.photoeditor.widget.layout.InterceptableLinearLayout
            android:id="@+id/editor_id_undo_redo_bar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:visibility="gone">

            <ImageButton
                android:id="@+id/editor_id_action_undo"
                style="@style/PictureBottomActionBarIcon"
                android:layout_marginEnd="@dimen/picture3d_editor_undo_redo_bar_padding"
                android:contentDescription="@string/talkback_picture_undo"
                android:src="@drawable/picture3d_editor_ic_undo_selector" />

            <ImageButton
                android:id="@+id/editor_id_action_redo"
                style="@style/PictureBottomActionBarIcon"
                android:layout_marginStart="@dimen/picture3d_editor_undo_redo_bar_padding"
                android:contentDescription="@string/talkback_picture_button_reset"
                android:src="@drawable/picture3d_editor_ic_redo_selector" />
        </com.oplus.gallery.photoeditor.widget.layout.InterceptableLinearLayout>

    </RelativeLayout>

    <ViewStub
        android:id="@+id/editor_id_menu_stub"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</RelativeLayout>
