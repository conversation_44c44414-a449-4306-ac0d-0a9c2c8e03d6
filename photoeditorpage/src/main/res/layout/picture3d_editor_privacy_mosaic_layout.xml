<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/privacy_mosaic_layout"
    android:layout_width="@dimen/picture3d_privacy_mosaic_layout_long_size"
    android:layout_marginTop="@dimen/picture3d_mosaic_toolbar_margin"
    android:layout_marginStart="@dimen/picture3d_privacy_mosaic_margin"
    android:layout_height="@dimen/picture3d_privacy_mosaic_layout_short_size"
    android:paddingTop="@dimen/picture3d_privacy_mosaic_icon_padding_top"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/privacy_mosaic_view"
        android:layout_width="@dimen/picture3d_privacy_mosaic_icon_size"
        android:layout_height="@dimen/picture3d_privacy_mosaic_icon_size"
        android:layout_gravity="center_vertical"
        android:clickable="true"
        android:contentDescription="@null"
        android:src="@drawable/ic_privacy_mosaic_button" />

    <View
        android:layout_width="@dimen/picture3d_privacy_mosaic_dividing_line_width"
        android:layout_height="@dimen/picture3d_privacy_mosaic_dividing_line_length"
        android:layout_gravity="center_vertical"
        android:layout_marginHorizontal="@dimen/picture3d_privacy_mosaic_dividing_line_margin"
        android:background="@color/picture3d_privacy_mosaic_dividing_line_color" />
</LinearLayout>
