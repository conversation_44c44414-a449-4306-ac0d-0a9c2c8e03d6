<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.oplus.gallery.standard_lib.ui.toolbar.GToolBar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_min_height"
        android:background="?attr/couiColorBackgroundWithCard" />

    <EditText
        android:layout_below="@id/toolbar"
        android:layout_marginTop="@dimen/picture3d_ocr_text_margin_top"
        android:background="@null"
        android:layout_marginBottom="@dimen/picture3d_ocr_text_margin_bottom"
        android:paddingHorizontal="@dimen/picture3d_ocr_text_padding_left_right"
        android:id="@+id/text_content"
        android:gravity="top|start"
        android:layout_width="match_parent"
        android:ellipsize="start"
        android:lineSpacingMultiplier="1.4"
        android:textSize="@dimen/picture3d_ocr_result_text_size"
        android:layout_height="match_parent"
        android:textAlignment="viewStart"/>

    <com.coui.appcompat.bottomnavigation.COUINavigationView
        android:id="@+id/navigation_tool"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        app:couiNaviMenu="@menu/picture3d_ocr_navigation_tool"
        app:navigationType="tool" />

</RelativeLayout>