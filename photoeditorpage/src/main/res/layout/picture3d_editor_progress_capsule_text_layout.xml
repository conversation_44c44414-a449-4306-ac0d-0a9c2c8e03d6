<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:parentTag="android.widget.FrameLayout">

    <com.coui.appcompat.button.COUILoadingButton
        android:id="@+id/progress_capsule_tv_title_loading"
        style="@style/picture3d_progress_capsule_loading_state_loading_style"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="start|center_vertical"
        android:maxLines="1"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        android:textColor="@color/picture3d_progress_capsule_default_progress_color" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/progress_capsule_tv_title_normal"
        style="@style/picture3d_progress_capsule_loading_state_loading_style"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:maxLines="1"
        android:paddingStart="0dp"
        android:textColor="@color/picture3d_progress_capsule_default_progress_color"
        android:visibility="gone" />

</merge>