<resources xmlns:tools="http://schemas.android.com/tools">

    <declare-styleable name="picture3d_RedPoint">
        <attr name="picture3d_redPointColor" format="color|reference" />
        <attr name="picture3d_redPointRadius" format="dimension|reference" />
        <attr name="picture3d_redPointTopMargin" format="dimension|reference" />
        <attr name="picture3d_redPointRightMargin" format="dimension|reference" />
    </declare-styleable>

    <declare-styleable name="picture3d_StickerIconView">
        <attr name="picture3d_thumbnail" format="reference" />
        <attr name="picture3d_thumbnailHorizontalPadding" format="dimension|reference" />
        <attr name="picture3d_thumbnailVerticalPadding" format="dimension|reference" />
        <attr name="picture3d_backgroundDrawable" format="reference" />
        <attr name="picture3d_selectedDrawable" format="reference" />
        <attr name="picture3d_forceDrawSelectedDrawable" format="boolean" />
        <attr name="picture3d_selectedGap" format="reference" />
        <attr name="picture3d_selectedLineWidth" format="reference" />
        <attr name="picture3d_normalDrawable" format="reference" />
        <attr name="picture3d_maskColor" format="color|reference" />
        <attr name="picture3d_viewOutlineRadius" format="dimension|reference" />
    </declare-styleable>

    <declare-styleable name="picture3d_AlphaAnimRelativeLayout">
        <attr name="picture3d_disabledAlpha" format="float|reference" />
        <attr name="picture3d_pressedAlpha" format="float|reference" />
        <attr name="picture3d_defaultAlpha" format="float|reference" />
    </declare-styleable>

    <declare-styleable name="picture3d_EditorMenuItemView">
        <attr name="picture3d_dotRadius" format="dimension|reference" />
        <attr name="picture3d_dotColor" format="color|reference" />
        <attr name="picture3d_cornerRadius" format="dimension|reference" />
        <attr name="picture3d_bounderCornerRadius" format="dimension|reference" />
        <attr name="picture3d_cornerPointRadius" format="dimension|reference" />
        <attr name="picture3d_cornerPointColor" format="color|reference" />
        <attr name="picture3d_cornerPointOffsetX" format="dimension|reference" />
        <attr name="picture3d_cornerPointOffsetY" format="dimension|reference" />
        <attr name="picture3d_backgroundStrokeWidth" format="dimension|reference" />
        <attr name="picture3d_bounderColor" format="color|reference" />
        <attr name="picture3d_itemBackgroundColor" format="color|reference" />
        <attr name="picture3d_iconPadding" format="dimension|reference" />
        <attr name="picture3d_iconWidth" format="dimension|reference" />
        <attr name="picture3d_iconHeight" format="dimension|reference" />
        <attr name="picture3d_imageMarginTop" format="dimension|reference" />
        <attr name="picture3d_imageWidth" format="dimension|reference" />
        <attr name="picture3d_imageHeight" format="dimension|reference" />
        <attr name="picture3d_valueTextColor" format="color|reference" />
        <attr name="picture3d_valueTextSize" format="dimension|reference" />
        <attr name="picture3d_valueText" format="string|reference" />
        <attr name="picture3d_foregroundColor" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="picture3d_EditorBottomActionBar">
        <attr name="picture3d_actionType">
            <enum name="image" value="0" />
            <enum name="text" value="1" />
            <enum name="menu" value="2" />
        </attr>
        <attr name="picture3d_actionUseText" format="boolean|reference" />
        <attr name="picture3d_actionUseMenu" format="boolean|reference" />
        <attr name="picture3d_titleText" format="string|reference" />
        <attr name="picture3d_titleDrawable" format="reference" />
        <attr name="picture3d_titleTintColor" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="picture3d_HorizontalListView">
        <attr name="picture3d_itemSpacing" format="dimension|reference" />
        <attr name="picture3d_enable" format="boolean|reference" />
    </declare-styleable>

    <declare-styleable name="picture3d_RuleScrollerView">
        <attr name="rule_orientation">
            <enum name="horizontal" value="0" />
            <enum name="vertical" value="1" />
        </attr>
        <attr name="startValue" format="integer" />
        <attr name="endValue" format="integer" />
        <attr name="currentValue" format="integer" />
        <attr name="minPreciseLength" format="dimension" />
        <attr name="minPreciseValue" format="integer" />
        <attr name="minScaleValue" format="float|reference" />
        <attr name="wholeLineColor" format="color" />
        <attr name="lineColor" format="color" />
        <attr name="textColor" format="color" />
        <attr name="textSize" format="dimension" />
        <attr name="lineHeight" format="dimension" />
        <attr name="maxLineHeight" format="dimension" />
        <attr name="pointTop" format="dimension" />
        <attr name="indicatorHeight" format="dimension" />
        <attr name="wholeMinSpaceNumber" format="integer" />
        <attr name="unitText" format="string" />
        <attr name="floatingTextMargin" format="dimension" />
        <attr name="pointRadius" format="dimension" />
        <attr name="centerTextVisible" format="boolean" />
        <attr name="isUnevenScales" format="boolean" />
        <attr name="interval" format="integer" />
    </declare-styleable>

    <declare-styleable name="picture3d_HorizontalAdapterView">
        <attr name="picture3d_itemSpacing" />
        <attr name="picture3d_enable" />
    </declare-styleable>

    <declare-styleable name="picture3d_RoundImageView">
        <attr name="picture3d_corner_radius" format="dimension" />
        <attr name="android:scaleType" />
    </declare-styleable>

    <declare-styleable name="picture3d_GLRootView">
        <attr name="picture3d_glRootViewZOrderOnTop" format="boolean" />
    </declare-styleable>

    <declare-styleable name="picture_ThumbnailSeekBar">
        <attr name="picture_thumbScaleType">
            <enum name="fitXY" value="1" />
            <enum name="fitCenter" value="2" />
            <enum name="centerCrop" value="3" />
        </attr>
        <attr name="picture_touchBias" format="dimension|reference" />
        <attr name="picture_thumbWidth" format="dimension|reference" />
        <attr name="picture_thumbHeight" format="dimension|reference" />
        <attr name="picture_indicatorBorderSize" format="dimension|reference" />
        <attr name="picture_indicator" format="reference" />
        <attr name="picture_placeholder" format="reference" />
        <attr name="picture_progress" format="float" />
    </declare-styleable>

    <declare-styleable name="picture_SetWallpaperBtn">
        <attr name="picture_textColor" format="color" />
        <attr name="picture_groundingColor" format="color" />
        <attr name="picture_textSize" format="dimension" />
        <attr name="picture_textLeft" format="string" />
        <attr name="picture_textRight" format="string" />
        <attr name="picture_groundingAlpha" format="integer" />
    </declare-styleable>


    <declare-styleable name="picture_BothWaySeekBar">
        <attr name="picture_seekBarThumbColor" format="color" />
        <attr name="picture_seekBarProgressColor" format="color" />
        <attr name="picture_seekBarBackgroundColor" format="color" />
        <attr name="picture_seekBarThumbRadius" format="dimension" />
        <attr name="picture_seekBarThumbScaleRadius" format="dimension" />
        <attr name="picture_seekBarProgressRadius" format="dimension" />
        <attr name="picture_seekBarProgressScaleRadius" format="dimension" />
        <attr name="picture_seekBarBackgroundRadius" format="dimension" />
        <attr name="picture_seekBarThumbBackgroundRadius" format="dimension" />
        <attr name="picture_seekBarThumbBackgroundScaleRadius" format="dimension" />
        <attr name="picture_seekBarThumbBackgroundShadowRadius" format="dimension" />
        <attr name="picture_seekBarSecondaryProgressColor" format="color" />
    </declare-styleable>

    <declare-styleable name="picture_FilterRoundedImageView">
        <attr name="picture_outCornerRadius" format="dimension|reference" />
        <attr name="picture_imageCornerRadius" format="dimension|reference" />
        <attr name="picture_imageWidth" format="dimension|reference" />
        <attr name="picture_imageHeight" format="dimension|reference" />
    </declare-styleable>

    <declare-styleable name="OliveTrimView">
        <attr name="oliveTrimFrameHeight" format="dimension"/>
        <attr name="oliveTrimHeight" format="dimension"/>
        <attr name="oliveTriWindowWidth" format="dimension"/>
        <attr name="oliveTrimPaddingOfBorder" format="dimension"/>
        <attr name="oliveTrimLeftPaddingOfBorder" format="dimension" />
        <attr name="oliveTrimRightPaddingOfBorder" format="dimension" />
        <attr name="oliveTrimEditorBackground" format="color"/>
        <attr name="oliveAutoMoveGap" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="EditorDownloadProgressButton">
        <attr name="defaultBackgroundColor" format="color|reference"/>
        <attr name="progressBackgroundColor" format="color|reference"/>
        <attr name="finishBackgroundColor" format="color|reference"/>
        <attr name="borderStrokeWidth" format="dimension|reference"/>
        <attr name="borderStrokeColor" format="color|reference"/>
    </declare-styleable>

    <declare-styleable name="OliveStatusView">
       <attr name="orientation">
            <enum name="horizontal" value="0" />
            <enum name="vertical" value="1" />
        </attr>
    </declare-styleable>

    <declare-styleable name="StatusView">
        <attr name="status_text" format="string|reference" />
        <attr name="status_drawable" format="reference" />
        <attr format="reference" name="anim_rawRes"/>
    </declare-styleable>

    <declare-styleable name="RuleNameView">
        <attr name="ruleNameHeight" format="dimension" />
        <attr name="ruleName_textSize" format="dimension" />
    </declare-styleable>

    <declare-styleable name="RuleDescriptionView">
        <attr name="ruleDescriptionFrameHeight" format="dimension" />
        <attr name="ruleDescription_textSize" format="dimension" />
    </declare-styleable>
    <declare-styleable name="AiCompositionRatioView">
        <attr name="isSmallLandscape" format="boolean" />
    </declare-styleable>

    <declare-styleable name="RangeBarView">
        <!-- Drawable相关属性 -->
        <attr name="android:drawableStart" />
        <attr name="rangeBarViewDrawableWidth" format="dimension|reference" />
        <attr name="rangeBarViewDrawableHeight" format="dimension|reference" />
        <attr name="rangeBarViewDrawableTopPadding" format="dimension|reference" />
        <attr name="rangeBarViewDrawableCenterInVertical" format="boolean" />
        <attr name="rangeBarViewBackgroundColor" format="color|reference"/>

        <!--        分割线相关属性-->
        <attr name="rangeBarViewDividerHeight" format="dimension|reference" />
        <attr name="rangeBarViewDividerWidth" format="dimension|reference" />
        <attr name="rangeBarViewDividerColor" format="color|reference" />
        <attr name="rangeBarViewDividerPaddingStart" format="dimension|reference" />

        <!--        刻度相关属性-->
        <attr name="rangeBarViewScaleCount" format="integer" />
        <attr name="rangeBarViewScaleLength" format="dimension|reference" />
        <attr name="rangeBarViewScaleWidth" format="dimension|reference" />
        <attr name="rangeBarViewScaleMarginStart" format="dimension|reference" />

        <!--        选择器相关属性-->
        <attr name="rangeBarViewSelectorLength" format="dimension|reference" />
        <attr name="rangeBarViewSelectorWidth" format="dimension|reference" />
        <attr name="rangeBarViewSelectedAreaScaleRate" format="float" />

        <attr name="android:text" />
        <attr name="android:textStyle" />
        <attr name="android:textSize" />
        <attr name="android:textColor" />
        <attr name="android:fontFamily" />
    </declare-styleable>
</resources>
