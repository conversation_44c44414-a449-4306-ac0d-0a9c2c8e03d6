<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="picture3d_PictureActivityTheme" parent="@style/CommonDefaultTheme">
        <item name="android:windowDisablePreview">true</item>
        <!-- Fixme 暂时去掉window半透明背景样式，让大图页可以横屏-->
        <!--<item name="android:windowBackground">@color/common_transparent</item>-->
        <!--<item name="android:windowIsTranslucent">true</item>-->
        <item name="android:actionModeCloseDrawable">@null</item>
        <item name="android:actionModeCloseButtonStyle">@null</item>
        <item name="android:navigationBarColor">@color/common_transparent</item>
        <item name="android:statusBarColor">@color/common_transparent</item>
    </style>

    <style name="picture3d_EditorMenuItemTextStyle.Clickable" parent="@style/picture3d_EditorMenuItemTextStyle">
        <item name="android:textColor">@color/picture3d_editor_menu_clickable_item_text_color</item>
    </style>

    <style name="picture3d_EditorMenuItemTextStyle.Selectable.Red" parent="@style/picture3d_EditorMenuItemTextStyle">
        <item name="android:textColor">@color/picture3d_editor_menu_selectable_text_color_red</item>
    </style>

    <style name="picture3d_EditorMenuItemTextStyle">
        <item name="android:maxLines">2</item>
        <item name="android:minLines">@integer/picture3d_editor_menu_item_text_min_lines</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textAppearance">@style/gTextAppearanceCaption</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">@color/g_color_white</item>
        <item name="textSizeLevel">G2</item>
    </style>

    <style name="picture3d_preview_navigation_item_text">
        <item name="android:textSize">10dp</item>
    </style>

    <style name="picture3d_Theme.EditorText.Dialog" parent="@style/Theme.COUI.Dialog.Alert">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowActionModeOverlay">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:buttonBarStyle">@style/ButtonBar.COUI</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>
        <item name="android:textAppearance">@style/TextAppearance.COUI</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.COUI.Inverse</item>
        <item name="android:listPreferredItemPaddingLeft">16dip</item>
        <item name="android:listPreferredItemPaddingRight">16dip</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowTitleStyle">@style/DialogWindowTitle.COUI</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowMinWidthMajor">56%</item>
        <item name="android:windowMinWidthMinor">100%</item>
        <item name="android:textColorPrimaryDisableOnly">@color/coui_primary_text_disable_only
        </item>
    </style>

    <style name="picture3d_progress_capsule_loading_state_loading_style">
        <item name="android:textAppearance">@style/couiTextAppearanceButton</item>
        <item name="android:paddingLeft">0dp</item>
        <item name="android:paddingRight">0dp</item>
        <item name="brightness">0.8</item>
        <item name="drawableColor">@color/common_transparent</item>
        <item name="isShowLoadingText">true</item>
    </style>

    <style name="picture3d_eliminate_state_cancel_button_style" parent="EditorRippleAnim">
        <item name="android:textAppearance">@style/couiTextAppearanceButton</item>
    </style>

    <style name="picture3d_StickerRecycleDialogStyle.UpDown.NoActionBar" parent="picture3d_StickerRecycleDialogStyle.UpDown"></style>

    <style name="picture3d_StickerRecycleDialogStyle.UpDown" parent="@style/CommonDefaultTheme">
        <item name="android:windowAnimationStyle">@style/picture3d_ActivityDialogAnimation.UpDown</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:navigationBarColor">@color/picture3d_sticker_recycle_bin_window_background_color
        </item>
        <item name="android:windowBackground">@color/picture3d_sticker_recycle_bin_window_background_color
        </item>
    </style>

    <style name="picture3d_ActivityDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/coui_open_slide_enter</item>
        <item name="android:windowExitAnimation">@anim/coui_close_slide_exit</item>
    </style>

    <style name="picture3d_ActivityDialogAnimation.UpDown">
        <item name="android:windowEnterAnimation">@anim/coui_push_up_enter_activitydialog</item>
        <item name="android:windowExitAnimation">@anim/coui_push_down_exit_activitydialog</item>
    </style>

    <style name="horizontal_single_clock_time_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/picture_horizontal_single_clock_text_height</item>
        <item name="android:textSize">@dimen/picture_horizontal_single_time_text_size</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/picture_clock_text_color</item>
    </style>

    <style name="Theme.Transparent" parent="@style/CommonDefaultTheme">
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="PictureBottomActionBarTextDone" parent="EditorRippleAnim">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingHorizontal">
            @dimen/picture3d_editor_title_bar_btn_padding_horizontal
        </item>
        <item name="android:paddingBottom">
            @dimen/picture3d_editor_title_bar_btn_padding_bottom
        </item>
        <item name="android:paddingTop">
            @dimen/picture3d_editor_title_bar_btn_padding_top
        </item>
        <item name="android:textAppearance">@style/gTextAppearanceHeadline6</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@drawable/picture3d_editor_text_selector</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="textSizeLevel">G2</item>
    </style>

    <style name="PictureBottomActionBarIcon" parent="EditorRippleAnim">
        <item name="android:layout_width">@dimen/picture3d_bottom_action_bar_icon_size
        </item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:scaleType">centerInside</item>
    </style>

    <style name="PictureBottomActionBarUndo" parent="EditorRippleAnim">
        <item name="android:layout_width">@dimen/picture3d_bottom_action_bar_icon_size
        </item>
        <item name="android:layout_height">@dimen/picture3d_bottom_action_bar_icon_size
        </item>
        <item name="android:scaleType">centerInside</item>
    </style>

    <style name="PictureEditorEliminateTipsIcon" parent="EditorRippleAnim">
        <item name="android:layout_width">@dimen/picture3d_eliminate_tips_icon_size</item>
        <item name="android:layout_height">@dimen/picture3d_eliminate_tips_icon_size</item>
    </style>

    <style name="PictureEditorTipsIcon" parent="EditorRippleAnim">
        <item name="android:layout_width">@dimen/picture3d_tips_icon_size</item>
        <item name="android:layout_height">@dimen/picture3d_tips_icon_size</item>
    </style>

    <style name="PictureEditorDelStickerTitleIcon" parent="EditorRippleAnim">
        <item name="android:layout_width">wrap_content
        </item>
        <item name="android:layout_height">@dimen/common_toolbar_icon_height
        </item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:layout_gravity">center_vertical</item>
    </style>


    <style name="CustomEditDialogWithInputCount" parent="COUIAlertDialog.BottomAssignment">
        <item name="customContentLayout">
            @layout/picture3d_edit_custom_with_input_count_alert_dialog_layout
        </item>
    </style>

    <style name="CustomEditDialogWithoutInputCount" parent="COUIAlertDialog.BottomAssignment">
        <item name="customContentLayout">
            @layout/picture3d_edit_custom_without_input_count_alert_dialog_layout
        </item>
    </style>

    <style name="StickerManagementToolbarTitle" parent="@style/TextAppearance.COUI.AppCompatSupport.Toolbar.Title">
        <item name="textSize">16sp</item>
    </style>

    <style name="StickerManagementToolbarSubtitle" parent="@style/TextAppearance.COUI.AppCompatSupport.Toolbar.SubTitle">
        <item name="textSize">12sp</item>
    </style>

    <!--AI画质增强入口 START -->
    <style name="picture3d_EditorRecommendTextStyle">
        <item name="android:maxLines">2</item>
        <item name="android:minLines">1</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textAppearance">@style/gTextAppearanceCaption</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">@color/common_white</item>
        <item name="textSizeLevel">G2</item>
        <item name="android:textSize">12sp</item>
    </style>
    <!--AI画质增强入口 END -->

    <!--保存弹窗的颜色主题风格设置 -->
    <style name="SaveDialogColorThemeStyle">
        <item name="couiColorError">@color/coui_dialog_button_text_color_center</item>
    </style>

    <style name="WatermarkCameraSwitchTheme" parent="Theme.COUI.Main">
        <item name="couiColorPrimary">@color/picture3d_editor_watermark_camera_color</item>
        <item name="couiColorControls">@color/picture3d_editor_watermark_camera_switch_unchecked_color</item>
    </style>

    <style name="WatermarkCameraSwitchThemeNoHassel" parent="Theme.COUI.Main">
        <item name="couiColorPrimary">@color/picture3d_editor_watermark_camera_switch_color_not_hassel</item>
        <item name="couiColorControls">@color/picture3d_editor_watermark_camera_switch_unchecked_color</item>
    </style>

    <style name="WatermarkCameraCOUITipsTheme" parent="Theme.COUI.Main">
        <item name="isCOUIDarkTheme">true</item>
    </style>

    <!--AI功能功能介绍面板中动画对比文案背景样式 -->
    <style name="AIFuncIntroductionDialogCompareTxtStyle">
        <item name="android:maxLines">1</item>
        <item name="android:minLines">1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textAlignment">center</item>
        <item name="android:paddingStart">3dp</item>
        <item name="android:paddingTop">1dp</item>
        <item name="android:paddingEnd">3dp</item>
        <item name="android:paddingBottom">1dp</item>
        <item name="android:textAppearance">@style/couiTextTag</item>
        <item name="android:background">@drawable/picture3d_introduce_after_before_background</item>
    </style>

    <style name="AiCompositionRuleDescriptionStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:fontFamily">OPPO Sans 4.0 SC</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">0dp</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:letterSpacing">0.08</item>
        <item name="android:textColor">@color/ai_composition_rules_des_text_color</item>
        <item name="android:textFontWeight">500</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="AiCompositionRuleDescription_Dot_Style">
        <item name="android:layout_width">@dimen/photo_editor_ai_composition_rules_des_dot_size</item>
        <item name="android:layout_height">@dimen/photo_editor_ai_composition_rules_des_dot_size</item>
        <item name="android:layout_marginStart">@dimen/photo_editor_ai_composition_rules_des_dot_margin</item>
        <item name="android:layout_marginEnd">@dimen/photo_editor_ai_composition_rules_des_dot_margin</item>
        <item name="android:background">@drawable/photo_editor_ai_composition_rules_dot</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="AiLightingSeekBarStyle">
        <item name="android:min">0</item>
        <item name="android:max">100</item>
        <item name="couiSeekBarBackGroundEnlargeScale">1.4</item>
        <item name="couiSeekBarProgressEnlargeScale">1</item>
        <item name="couiSeekBarBackgroundHeight">@dimen/picture3d_editor_ai_lighting_seek_bar_background_height</item>
        <item name="couiSeekBarBackgroundRadius">@dimen/picture3d_editor_ai_lighting_seek_bar_background_height</item>
        <item name="couiSeekBarProgressHeight">@dimen/picture3d_editor_ai_lighting_seek_bar_progress_height</item>
        <item name="couiSeekBarProgressPaddingHorizontal">@dimen/picture3d_editor_ai_lighting_seek_bar_progress_radius</item>
        <item name="couiSeekBarProgressRadius">@dimen/picture3d_editor_ai_lighting_seek_bar_progress_radius</item>
        <item name="couiSeekBarDeformation">false</item>
    </style>


    <!--olive实况声音开关文案背景样式 -->
    <style name="olive_EditorSoundSwitchTextStyle">
        <item name="android:maxLines">2</item>
        <item name="android:minLines">1</item>
        <item name="android:textAlignment">center</item>
        <item name="android:fontFamily">sans-serif-regular</item>
        <item name="android:textAppearance">@style/couiTextAppearanceDescription</item>
        <item name="android:layout_marginLeft">@dimen/photoeditorpage_olive_text_margin</item>
        <item name="android:layout_marginRight">@dimen/photoeditorpage_olive_text_margin</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">@color/olive_editor_status_text_color_select</item>
        <item name="textSizeLevel">G2</item>
        <item name="android:textSize">10sp</item>
    </style>

    <string name="CircleImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </string>
</resources>