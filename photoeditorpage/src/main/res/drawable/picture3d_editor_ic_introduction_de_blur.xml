<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="36dp"
    android:height="36dp"
    android:autoMirrored="true"
    android:viewportWidth="36"
    android:viewportHeight="36">

    <path
        android:fillAlpha="0.2"
        android:fillColor="#000000"
        android:fillType="evenOdd"
        android:pathData="M26.453,28.705C28.645,28.365 30.753,27.35 32.441,25.661C36.672,21.43 36.672,14.571 32.441,10.34C30.754,8.652 28.649,7.638 26.459,7.296C26.523,7.358 26.586,7.42 26.649,7.484C27.583,8.417 28.366,9.444 29,10.533C29.66,10.907 30.281,11.375 30.844,11.938C34.192,15.286 34.192,20.715 30.844,24.063C30.28,24.627 29.657,25.096 28.996,25.469C28.363,26.557 27.581,27.581 26.649,28.512C26.584,28.577 26.519,28.642 26.453,28.705Z"
        android:strokeAlpha="0.2" />

    <path
        android:fillAlpha="0.5"
        android:fillColor="#000000"
        android:fillType="evenOdd"
        android:pathData="M18.588,28.834C21.361,28.835 24.135,27.777 26.251,25.661C30.482,21.43 30.482,14.57 26.251,10.339C24.133,8.221 21.355,7.163 18.579,7.166C19.736,7.936 20.763,8.886 21.619,9.976C22.725,10.393 23.763,11.047 24.653,11.937C28.001,15.286 28.001,20.715 24.653,24.063C23.764,24.951 22.729,25.604 21.626,26.021C20.77,27.112 19.744,28.063 18.588,28.834Z"
        android:strokeAlpha="0.5" />

    <path
        android:fillColor="#000000"
        android:pathData="M11.384,18.002m7.661,7.661a10.834,10.834 90,1 1,-15.322 -15.322a10.834,10.834 90,1 1,15.322 15.322" />

</vector>
