/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ${FILE_NAME}
 * * Description: build this module.
 * * Version: 1.0
 * * Date : 2020/02/27
 * * Author: GuangJin.Ye@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  GuangJin.Ye@Apps.Gallery3D       2020/02/27    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.repair.ocloud;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import androidx.preference.PreferenceManager;

import com.oplus.gallery.basebiz.permission.NetSendPrivacyPermissionRule;
import com.oplus.gallery.foundation.networkaccess.callback.AppResultCallback;
import com.oplus.gallery.framework.abilities.download.IDownloadAbility;
import com.oplus.gallery.framework.app.GalleryApplication;
import com.oplus.gallery.photoeditor.editingvvm.repair.common.ReturnCode;
import com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.data.ConfigResponse;
import com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.data.ResponseData;
import com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.listener.OnRequestListener;
import com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.paramsencrypt.enums.EncryptionAlgorithmEnum;
import com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.paramsencrypt.factory.ParamsFactory;
import com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.paramsencrypt.vo.WrappedParamsRequest;
import com.oplus.gallery.photoeditor.editingvvm.repair.util.SupportUtil;
import com.oplus.gallery.standard_lib.file.File;
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils;
import com.oplus.gallery.standard_lib.util.os.ContextGetter;
import com.oplus.gallery.foundation.security.Md5Utils;
import com.oplus.gallery.foundation.util.debug.GLog;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

public class EncryptProtocol {

    private static final String TAG = "EncryptProtocol";
    private static final String PREFERENCE_PUBLIC_KEY = "pref_public_key";
    private static final String PREFERENCE_ALGORITHM = "pref_algorithm";
    private static final int BUFFER_SIZE = 1024;
    private static final int RANDOM_LENGTH = 5;
    private final String mRandomKey;
    private Context mContext;
    private ProtocolConfig mConfig;
    private WrappedParamsRequest mParamsRequest;

    public EncryptProtocol(Context context) {
        mContext = context;
        mRandomKey = UUID.randomUUID().toString().substring(0, RANDOM_LENGTH);
        checkProtocol();
    }

    public static void initProtocol(Context context, OnRequestListener listener) {
        requestProtocol(context, listener, new AppResultCallback<ResponseData<ConfigResponse>>() {
            @Override
            public void onSuccess(ResponseData<ConfigResponse> responseData) {
                if (responseData == null) {
                    GLog.e(TAG, "responseData is null.");
                    if (listener != null) {
                        listener.onError(ReturnCode.Error.CONFIG_IS_NULL, null);
                    }
                    return;
                }
                if (!saveConfigToSharedPreferences(responseData.getData())) {
                    if (listener != null) {
                        listener.onError(ReturnCode.Error.CONFIG_IS_NULL, null);
                    }
                    return;
                }
                if (listener != null) {
                    listener.onSuccess();
                }
            }

            @Override
            public void onFailed(int code, String msg) {
                if (listener != null) {
                    listener.onError(code, msg);
                }
                GLog.e(TAG, "onFailed. code = " + code + ", msg = " + msg);
            }
        });
    }

    public void updateProtocol(OnRequestListener listener) {
        requestProtocol(mContext, listener, new AppResultCallback<ResponseData<ConfigResponse>>() {
            @Override
            public void onSuccess(ResponseData<ConfigResponse> responseData) {
                if (responseData == null) {
                    GLog.e(TAG, "responseData is null.");
                    if (listener != null) {
                        listener.onError(ReturnCode.Error.CONFIG_IS_NULL, null);
                    }
                    return;
                }
                if (!saveProtocolConfig(responseData.getData())) {
                    if (listener != null) {
                        listener.onError(ReturnCode.Error.CONFIG_IS_NULL, null);
                    }
                    return;
                }
                if (listener != null) {
                    listener.onSuccess();
                }
            }

            @Override
            public void onFailed(int code, String msg) {
                if (listener != null) {
                    listener.onError(code, msg);
                }
                GLog.e(TAG, "onFailed. code = " + code + ", msg = " + msg);
            }
        });
    }

    private static void requestProtocol(Context context, OnRequestListener listener, AppResultCallback<ResponseData<ConfigResponse>> callback) {
        String stdId = SupportUtil.getStdId();
        if (TextUtils.isEmpty(stdId)) {
            if (listener != null) {
                listener.onError(ReturnCode.Error.STDID_IS_NULL, null);
            }
            GLog.e(TAG, "requestProtocol, STDID_IS_NULL");
            return;
        }
        if (!SupportUtil.isAddressValid()) {
            if (listener != null) {
                listener.onError(ReturnCode.Error.PARAM_ERROR, null);
            }
            return;
        }
        IDownloadAbility downloadAbility = ((GalleryApplication) context.getApplicationContext()).getAppAbility(IDownloadAbility.class);
        if (downloadAbility == null) {
            if (listener != null) {
                listener.onError(ReturnCode.Error.PARAM_ERROR, null);
            }
            GLog.e(TAG, "requestProtocol, ability is null");
            return;
        }
        downloadAbility.enqueue(context, new AiRepairProtocolRequest(context, new NetSendPrivacyPermissionRule(), stdId), callback);
        downloadAbility.close();
    }

    public boolean saveProtocolConfig(ConfigResponse configResponse) {
        if ((configResponse != null) && !TextUtils.isEmpty(configResponse.getAlgorithm())
                && !TextUtils.isEmpty(configResponse.getPublicKey())) {
            if (mConfig == null) {
                mConfig = new ProtocolConfig();
            }
            mConfig.setAlgorithm(configResponse.getAlgorithm());
            mConfig.setPublicKey(configResponse.getPublicKey());
           return saveConfigToSharedPreferences(configResponse);
        }
        return false;
    }

    private static boolean saveConfigToSharedPreferences(ConfigResponse configResponse) {
        if ((configResponse != null) && !TextUtils.isEmpty(configResponse.getAlgorithm())
                && !TextUtils.isEmpty(configResponse.getPublicKey())) {
            SharedPreferences.Editor ed = PreferenceManager.getDefaultSharedPreferences(ContextGetter.context).edit();
            ed.putString(PREFERENCE_ALGORITHM, configResponse.getAlgorithm());
            ed.putString(PREFERENCE_PUBLIC_KEY, configResponse.getPublicKey());
            ed.apply();
            return true;
        }
        return false;
    }

    private boolean initBuiltinConfig() {
        SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(mContext);
        String algorithm = sp.getString(PREFERENCE_ALGORITHM, null);
        String publicKey = sp.getString(PREFERENCE_PUBLIC_KEY, null);
        if (TextUtils.isEmpty(algorithm) || TextUtils.isEmpty(publicKey)) {
            return false;
        }

        if (mConfig == null) {
            mConfig = new ProtocolConfig();
        }
        mConfig.setAlgorithm(algorithm);
        mConfig.setPublicKey(publicKey);
        return true;
    }

    public int initParamsRequest(byte[] data) {
        if (data == null) {
            return ReturnCode.Error.IMAGE_DATA_IS_NULL;
        }

        if (mConfig == null) {
            return ReturnCode.Error.CONFIG_IS_NULL;
        }

        long currentTimeMillis = System.currentTimeMillis();
        try {
            mParamsRequest = ParamsFactory.create(EncryptionAlgorithmEnum.getAlgorithmByValue(mConfig.getAlgorithm()),
                    mConfig.getPublicKey(), SupportUtil.getStdId(), String.valueOf(currentTimeMillis), data);
            return ReturnCode.SUCCESS;
        } catch (Exception e) {
            GLog.e(TAG, "initParamsRequest", e);
        }
        return ReturnCode.Error.ENCRYPT_FAILED;
    }

    public byte[] getEncryptedBytes() {
        if (mParamsRequest == null) {
            GLog.e(TAG, "getEncryptedBytes. mParamsRequest is null.");
            return null;
        }
        return mParamsRequest.getEncryptedBytes();
    }

    public void release() {
        setEncryptedBytes(null);
    }

    public void setEncryptedBytes(byte[] data) {
        if (mParamsRequest == null) {
            GLog.e(TAG, "setEncryptedBytes. mParamsRequest is null.");
            return;
        }
        mParamsRequest.setEncryptedBytes(data);
    }

    public Map<String, String> getBuiltinParams() {
        if (mParamsRequest == null) {
            GLog.e(TAG, "getBuiltinParams. mParamsRequest is null.");
            return null;
        }
        Map<String, String> params = new HashMap<String, String>();
        if (mParamsRequest.getHeaders() == null) {
            GLog.e(TAG, "getBuiltinParams. mParamsRequest.getHeaders() is null.");
            return null;
        }
        Iterator<Map.Entry<String, String>> it = mParamsRequest.getHeaders().entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, String> entry = it.next();
            params.put(entry.getKey(), entry.getValue());
        }
        return params;
    }

    public String getPhotoId(String imagePath) {
        if (mConfig == null) {
            GLog.d(TAG,"getPhotoId, config is null, return");
            return null;
        }
        if (!FileOperationUtils.isFileExist(imagePath)) {
            GLog.d(TAG,"getPhotoId, file not exist, return");
            return null;
        }
        String md5 = Md5Utils.getMd5(new File(imagePath));
        return md5 + mRandomKey;
    }

    public void checkProtocol() {
        if (!initBuiltinConfig()) {
            updateProtocol(null);
        }
    }

    private class ProtocolConfig {
        private String mPublicKey;
        private String mAlgorithm;

        public String getPublicKey() {
            return mPublicKey;
        }

        public void setPublicKey(String publicKey) {
            this.mPublicKey = publicKey;
        }

        public String getAlgorithm() {
            return mAlgorithm;
        }

        public void setAlgorithm(String algorithm) {
            this.mAlgorithm = algorithm;
        }
    }
}
