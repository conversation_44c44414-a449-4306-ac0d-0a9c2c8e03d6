/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - TransformConstraint.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/10/21
 * Author: xie<PERSON><PERSON><PERSON>@Apps.Gallery
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery		2024/10/21		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.photoeditor.animationcontrol.animator

import android.graphics.RectF
import android.util.Size

/**
 * 变换的约束
 */
interface TransformConstraint {

    /**
     * 显示区域
     */
    val displayRect: RectF

    /**
     * 内容的尺寸
     */
    val contentSize: Size

    /**
     * 内容的区域，小于等于[contentSize],用于计算默认的变换位置，优先级大于[contentSize]
     */
    val contentRegion: RectF? get() = null
}

/**
 * 可变的变换约束
 *
 * @param displayRect 显示区域
 * @param contentSize 内容尺寸
 * @param contentRegion 内容区域
 */
data class MutableTransformConstraint(
    override val displayRect: RectF = RectF(),
    override var contentSize: Size = Size(0, 0),
    override val contentRegion: RectF = RectF()
) : TransformConstraint {

    /**
     * 从指定的[sourceConstraint]中拷贝到本身
     *
     * @param sourceConstraint 拷贝的的对象来源
     */
    fun copyFrom(sourceConstraint: TransformConstraint) {
        displayRect.set(sourceConstraint.displayRect)
        contentSize = sourceConstraint.contentSize
        sourceConstraint.contentRegion?.let { contentRegion.set(it) } ?: contentRegion.setEmpty()
    }
}