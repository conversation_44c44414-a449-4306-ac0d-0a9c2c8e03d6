/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ${FILE_NAME}
 * * Description: build this module.
 * * Version: 1.0
 * * Date : 2020/02/27
 * * Author: GuangJin.Ye@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  GuangJin.Ye@Apps.Gallery3D       2020/02/27    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.data;

import com.google.gson.annotations.SerializedName;

public class ResponseData<T> {
    @SerializedName("code")
    private int mCode;
    @SerializedName("msg")
    private String mMessage;
    @SerializedName("data")
    private T mData;

    public int getCode() {
        return mCode;
    }

    public void setCode(int code) {
        this.mCode = code;
    }

    public String getMessage() {
        return mMessage;
    }

    public void setMessage(String message) {
        this.mMessage = message;
    }

    public T getData() {
        return mData;
    }

    public void setData(T data) {
        this.mData = data;
    }
}