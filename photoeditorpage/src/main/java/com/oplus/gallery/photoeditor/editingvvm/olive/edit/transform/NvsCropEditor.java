/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - NvsCropEditor
 ** Description: 美摄视频旋转裁剪
 ** Version: 1.0
 ** Date : 2024/09/13
 ** Author: dingyong@Apps.Gallery3D
 ** OPLUS Java File Skip Rule:FileLength,MethodLength
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2024/09/13    1.0         first created
 ********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform;
import android.text.TextUtils;
import android.util.Size;

import com.meicam.sdk.NvsVideoClip;
import com.meicam.sdk.NvsVideoFx;
import com.oplus.gallery.foundation.util.debug.GLog;
import com.oplus.gallery.foundation.util.debug.LogFlag;
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty;
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.FxNameKey;
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.Transform2DPropertyKey;
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.Transform2DPropertyValue;

import java.util.Arrays;

/**
 * 此类是美摄的视频旋转裁剪类，美摄那边都是用java实现。为了方便后续和美摄联调，该类继续保留用java实现
 */
public class NvsCropEditor {

    private static final String TAG = "NvsCropEditor";

    private NvsVideoClip mVideoClip;
    private NvsVideoFx mTransform2D;
    private NvsVideoFx mCropFx;

    public NvsCropEditor(NvsVideoClip videoClip) {
        this.mVideoClip = videoClip;
    }

    public void setVideoClip(NvsVideoClip videoClip) {
        this.mVideoClip = videoClip;
    }

    /**
     * 传入变换矩阵，美摄内部实现变换效果
     * @param matrix 变换矩阵
     * @param outputSize 裁剪之后视频的宽高
     */
    public void applyEffect(float[] matrix, Size outputSize) {
        if (mVideoClip == null) {
            GLog.e(TAG, LogFlag.DL, "applyEffect mVideoClip is null ");
            return;
        }
        if (matrix == null) {
            return;
        }
        mVideoClip.setRawFilterProcessesMode(NvsVideoClip.RAW_FILTER_PROCESSES_MODE_VARIANT_IMAGE_WITH_FILL_MODE_USED);
        mVideoClip.enablePropertyVideoFx(true);
        NvsVideoFx proProperty = mVideoClip.getPropertyVideoFx();
        if (proProperty != null) {
            proProperty.setMenuVal(Transform2DPropertyKey.FILL_MODE, Transform2DPropertyValue.FILL);
        }
        if (mTransform2D == null) {
            mTransform2D = mVideoClip.appendRawBuiltinFx(FxNameKey.TRANSFORM_2D);
        }
        if (mCropFx == null) {
            mCropFx = mVideoClip.appendRawBuiltinFx(FxNameKey.CROP);
        }
        mCropFx.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_LEFT, -outputSize.getWidth() / 2d);
        mCropFx.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_RIGHT, outputSize.getWidth() / 2d);
        mCropFx.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_TOP, outputSize.getHeight() / 2d);
        mCropFx.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_BOTTOM, -outputSize.getHeight() / 2d);

        String matrixString = Arrays.toString(matrix).replace("[", "").replace("]", "");
        mTransform2D.setStringVal(Transform2DPropertyKey.CUSTOM_MATRIX, matrixString);
    }

    /**
     * 移除特效
     */
    public void removeRawFx() {
        removeRawFx(FxNameKey.TRANSFORM_2D);
        removeRawFx(FxNameKey.CROP);
    }

    private void removeRawFx(String rawFxName) {
        if (mVideoClip == null) {
            return;
        }
        int rawFxCount = mVideoClip.getRawFxCount();
        if (rawFxCount <= 0) {
            return;
        }
        for (int fxIndex = 0; fxIndex < rawFxCount; fxIndex++) {
            NvsVideoFx rawFx = mVideoClip.getRawFxByIndex(fxIndex);
            if ((rawFx != null) && TextUtils.equals(rawFx.getBuiltinVideoFxName(), rawFxName)) {
                mVideoClip.removeRawFx(fxIndex);
                return;
            }
        }
    }
}
