/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveSlowMotionAnimation.kt
 ** Description: OLive（动态照片）慢动作动效
 ** Version: 1.0
 ** Date: 2025/06/13
 ** Author: <EMAIL>
 ** -
 ** ---------------------Revision History: ---------------------
 **  <author>    <date>    <version >    <desc>
 **  <EMAIL>    2025/6/13    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.graphics.Rect
import android.view.View
import androidx.core.graphics.toRectF
import androidx.core.view.isVisible
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.foundation.archv2.bus.IViewBus
import com.oplus.gallery.foundation.util.ext.updateMargin
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_UI_CONFIG
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.REPLY_TOPIC_PREVIEW_DISPLAY_RECT_CHANGED
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.OLiveSlowMotionBar

/**
 * 慢动作动效
 */
class OliveSlowMotionAnimation(
    private val activity: BaseActivity,
    private val vBus: IViewBus,
    private val rootView: View,
    private val toolbarLayout: View,
    private val oliveSlowMotionView: OLiveSlowMotionBar,
) {
    private var currentState: OLiveSlowMotionState = OLiveSlowMotionState.CLOSED

    private val previewArea by lazy {
        rootView.findViewById<View>(R.id.default_preview_area)
    }

    /**
     * 获取当前的UI配置
     */
    private val oliveUiConfig: IOLiveEditViewUIConfig
        get() = vBus.get<IOLiveEditViewUIConfig>(TOPIC_OLIVE_UI_CONFIG) ?: IOLiveEditViewUIConfig.getOliveUIConfig(activity.getCurrentAppUiConfig())

    /**
     * 获取慢动作组件的bottomMargin 不可见状态
     */
    private val slowMotionViewStartMargin: Int
        get() = oliveUiConfig.getSlowMotionViewMarginBottomWhenGone(activity.resources)

    /**
     * 获取慢动作组件的bottomMargin 可见状态
     */
    private val slowMotionViewEndMargin: Int
        get() = oliveUiConfig.getSlowMotionViewMarginBottomWhenVisible(activity.resources)

    /**
     * 获取缩图轴组件的bottomMargin 慢动作组件不可见状态
     */
    private val toolbarLayoutStartMargin: Int
        get() = oliveUiConfig.getThumbnailViewMarginBottomWithoutSlowMotion(activity.resources)

    /**
     * 获取缩图轴组件的bottomMargin 慢动作组件可见状态
     */
    private val toolbarLayoutEndMargin: Int
        get() = oliveUiConfig.getThumbnailViewMarginBottomWithSlowMotion(activity.resources)

    /**
     * 获取预览区组件的bottomMargin 慢动作组件不可见状态
     */
    private val previewStartMargin: Int
        get() = oliveUiConfig.getPreviewAreaMarginBottom(activity.resources)

    /**
     * 获取预览区组件的bottomMargin 慢动作组件可见状态
     */
    private val previewEndMargin: Int
        get() = oliveUiConfig.getPreviewAreaMarginBottomWhenSlowMotionVisible(activity.resources)

    /**
     * 动画对象
     */
    private val anim = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener { animation ->
            (animation.animatedValue as Float).let { value ->
                // 慢动作调节组件位置
                var factor = (value / SLOW_MOTION_POSITION_ANIMATION_FACTOR).coerceIn(0f, 1f)
                ((slowMotionViewEndMargin - slowMotionViewStartMargin) * factor + slowMotionViewStartMargin).let { marginBottom ->
                    oliveSlowMotionView.updateMargin(bottom = marginBottom.toInt())
                }

                // 慢动作调节组件透明度
                factor = value
                oliveSlowMotionView.alpha = factor

                // 缩图轴组件位置
                factor = (value / THUMBNAIL_POSITION_ANIMATION_FACTOR).coerceIn(0f, 1f)
                ((toolbarLayoutEndMargin - toolbarLayoutStartMargin) * factor + toolbarLayoutStartMargin).let { marginBottom ->
                    toolbarLayout.updateMargin(bottom = marginBottom.toInt())
                }

                // 预览区位置
                ((previewEndMargin - previewStartMargin) * factor + previewStartMargin).let { marginBottom ->
                    vBus.notifyOnce(
                        REPLY_TOPIC_PREVIEW_DISPLAY_RECT_CHANGED,
                        (Rect(previewArea.left, previewArea.top, previewArea.right, previewArea.bottom - marginBottom.toInt()).toRectF())
                    )
                }
            }
        }
        addListener(object : AnimatorListenerAdapter() {

            override fun onAnimationStart(animation: Animator, isReverse: Boolean) {
                if (!isReverse) {
                    oliveSlowMotionView.isVisible = true
                    oliveSlowMotionView.alpha = 0f
                }
            }

            override fun onAnimationEnd(animation: Animator, isReverse: Boolean) {
                if (isReverse) {
                    oliveSlowMotionView.isVisible = false
                }
            }
        })
    }

    /**
     * 根据慢动作状态启动动效
     * @param state 慢动作状态
     */
    fun startByState(state: OLiveSlowMotionState, needAnim: Boolean) {
        if ((state == currentState) && (state == OLiveSlowMotionState.OPENING)) {
            return
        }
        if ((currentState == OLiveSlowMotionState.OPENING) && (state == OLiveSlowMotionState.OPEN)) {
            currentState = state
            return
        }
        currentState = state
        cancel()
        when (currentState) {
            OLiveSlowMotionState.OPENING,
            OLiveSlowMotionState.OPEN -> {
                if (oliveSlowMotionView.isVisible.not()) {
                    anim.duration = if (needAnim) ANIMATION_DURATION else 0
                    anim.start()
                }
            }

            OLiveSlowMotionState.CLOSED -> {
                if (oliveSlowMotionView.isVisible) {
                    anim.duration = if (needAnim) ANIMATION_DURATION else 0
                    anim.reverse()
                }
            }
        }
    }

    /**
     * 取消动画
     */
    fun cancel() {
        if (anim.isRunning) {
            anim.cancel()
        }
    }

    companion object {
        /**
         * 动效总时长
         */
        private const val ANIMATION_DURATION = 733L

        /**
         * 慢动作位置动效时长占总时长的比例
         */
        private const val SLOW_MOTION_POSITION_ANIMATION_FACTOR = 0.6371f

        /**
         * 缩图轴位置动效时长占总时长的比例
         */
        private const val THUMBNAIL_POSITION_ANIMATION_FACTOR = 0.7053f
    }
}