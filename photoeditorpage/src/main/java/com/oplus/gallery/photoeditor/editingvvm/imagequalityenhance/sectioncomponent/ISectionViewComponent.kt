/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : ISectionViewComponent.kt
 ** Description : Section的View组件
 ** Version     : 1.0
 ** Date        : 2024/06/24
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/06/24      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.sectioncomponent

/**
 * Section的View组件
 *
 * 控制简单但内部逻辑复杂的可使用该接口去实现使用
 * 何为控制简单？ 就是只需要设置一些属性即可，简单的触发位置更新
 * 何为内部逻辑复杂？ 就是需要处理一些复杂的逻辑，比如，位置的更新需要判断横竖屏/大小屏等逻辑显示在不同位置
 */
internal interface ISectionViewComponent {

    /**
     * 控制控件是否可用， @see[View.isEnabled()]
     */
    var isEnable: Boolean

    /**
     * 控制控件是否可见， @see[View.isVisible()]]
     */
    var isVisible: Boolean

    /**
     * 是否有内容，依据内容View是否为null来判断
     */
    fun hasContent(): Boolean

    /**
     * 确保组件View
     */
    fun ensureComponentView()

    /**
     * 更新位置
     */
    fun updateViewPosition()

    /**
     * 销毁
     */
    fun destroy()
}