/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AiCompositionEnterOrExitAnimation
 ** Description:
 ** Version: 1.0
 ** Date : 2024-12-11
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  chenwenjun                      2024-12-11    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aicomposition.vm.editabilityhelper.animation

import android.graphics.RectF
import com.oplus.gallery.foundation.opengl.transform.CompoundMatrixProxy
import com.oplus.gallery.foundation.opengl.transform.Matrix
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.toRectF
import com.oplus.gallery.foundation.util.graphic.ImageUtils
import com.oplus.gallery.framework.abilities.editing.editor.EditorResultState
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.AiCompositionHelper.getFilteredParametricOperatingRecords
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.AiCompositionHelper.runOnNextTextureRenderEnd
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.EffectRequestData
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationCommand
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.editingvvm.preview.TextureDiff
import com.oplus.gallery.photoeditor.editingvvm.subscribeUntilConsumed
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord
import com.oplus.gallery.photoeditor.editingvvm.transform.asTransformRecord

/**
 * 带前置参数化编辑记录的情况下, 进入Ai出Ai构图页面, 因为要临时的移除/加回前置参数化编辑内容
 * 所以涉及一些动画
 *
 * 这里只处理进入/退出AI构图页面, 在有前置参数化编辑项需要剔除的场景下, 进出动效问题
 * 只有2个public方法: enterAnimation, exitAnimation
 */
class AiCompositionEnterOrExitAnimation(
    private val editingVM: EditingVM,
) {
    private val vmBus = editingVM.viewModelBus

    /**
     * 进入AI构图的时候,若有其他参数化编辑, 尤其旋转裁剪的时候, 需要如下的入场动画, 完成从裁剪图到内容图的流畅转换
     *
     * 注意: 这个函数要在当前mainTexture显示的是'内容图'(所列的preParametricRecordList已经去除且渲染出来了)以后, 再调用
     *
     * @param onNoTransformTextureUpdated 无参数话的图已经更新的回调
     */
    fun enterAnimation(onNoTransformTextureUpdated: () -> Unit) {
        // 获取前置参数化编辑记录
        val parametricRecordList = vmBus.getFilteredParametricOperatingRecords()
        GLog.d(TAG, LogFlag.DL) { "enterAnimation, recordSize=${parametricRecordList.size}" }
        val firstOrNull =
            parametricRecordList.firstOrNull {
                it.effectName == AvEffect.TransformEffect.name ||
                        it.effectName == AvEffect.AiCompositionEffect.name
            }

        val transformRecord = when (firstOrNull?.effectName) {
            AvEffect.TransformEffect.name -> firstOrNull

            AvEffect.AiCompositionEffect.name -> {
                firstOrNull.split().firstOrNull { it.effectName == AvEffect.TransformEffect.name }
            }

            else -> null
        }

        (transformRecord as? TransformRecord)?.let {
            // 只需要移除效果
            GLog.d(TAG, LogFlag.DL) { "enterAnimation. cancel TransformRecord#$transformRecord" }
            vmBus?.notifyOnce(TopicID.Operating.REPLY_TOPIC_OPERATING_CANCEL_EFFECT, transformRecord, true)
            runOnNextTextureUpdated {
                GLog.d(TAG, LogFlag.DL) { "enterAnimation. No transform texture updated." }
                applyTransform(it)
                vmBus?.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.Revert)
                onNoTransformTextureUpdated.invoke()
            }
        } ?: run {
            GLog.d(TAG, LogFlag.DL) { "enterAnimation. No transform. skip." }
            onNoTransformTextureUpdated.invoke()
        }
    }

    /**
     * 页面退出, 前面 enterAnimation 的动作反向做一次
     *
     * @param onTextureUpdated 退出动画完成后的回调
     */
    fun exitAnimation(onTextureUpdated: () -> Unit) {
        GLog.d(TAG, LogFlag.DL) { "exitAnimation, -->  animation.step1" }
        val requestCode = editingVM.sessionProxy.invalidate()
        // 监听texture_diff事件然后revertTransform把管线的图还原成和当前预览图一样的大小和位置
        runOnNextTextureUpdated(requestCode) {
            revertTransform(false)
            GLog.d(TAG, LogFlag.DL) { "exitAnimation, -->  animation.step2" }
        }
        // 监听renderEnd然后退出页面，这个时候遮罩会消失。
        vmBus?.runOnNextTextureRenderEnd(requestCode) {
            GLog.d(TAG, LogFlag.DL) { "exitAnimation, -->  animation.step3" }
            onTextureUpdated.invoke()
        }
    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // private utils area

    private fun revertTransform(withAnimation: Boolean) {
        vmBus?.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.Revert)
        if (withAnimation.not()) {
            vmBus?.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.ForceFinishAnimation)
        }
    }


    /**
     * 应用[record]的变换
     * 1. 更新裁剪区域
     * 2. 应用变换矩阵
     * 3. ZoomToCenter, record所记录的预览区域和现在内容显示的预览区域可能不一致（比如裁剪后竖屏保存，横屏进入），
     *    需要重新移动图片到中心
     * @param record TransformRecord
     */
    private fun applyTransform(record: TransformRecord) {
        val transformRecord = record.asTransformRecord()
        val animationProperties = vmBus?.get<PreviewAnimationProperties>(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES) ?: return
        postAnimationCommand(
            PreviewAnimationCommand.UpdateClipRect(transformRecord.clipRect.toRectF()),
            PreviewAnimationCommand.UpdateContentTransform(transformRecord.getContentTransform(animationProperties)),
            PreviewAnimationCommand.ZoomToCenter,
            PreviewAnimationCommand.ForceFinishAnimation
        )
    }

    /**
     * 获取内容变换矩阵，record中存储的Matrix是fullTransform(语义同：[PreviewAnimationProperties.fullTransform]),需要根据现在的约束区域计算contentTransform
     * @return 内容变换矩阵，同[PreviewAnimationProperties.contentTransform]
     */
    private fun TransformRecord.getContentTransform(animationProperties: PreviewAnimationProperties): Matrix {
        val imageRect = this.imageRect.toRectF()
        val deltaScale = 1 / ImageUtils.scaleImage(
            imageRect.width(),
            imageRect.height(),
            animationProperties.fullContentVisibleRect.width(),
            animationProperties.fullContentVisibleRect.height(),
            ImageUtils.SCALE_MODE_INSIDE
        )

        val compoundMatrix = CompoundMatrixProxy()
        compoundMatrix.set(matrix)
        compoundMatrix.scale(deltaScale, deltaScale, deltaScale)
        val clipRect = RectF(animationProperties.clipRect.final)
        if (axisAngleTransform != null) {
            compoundMatrix.axisAngleTransform = axisAngleTransform
            // imageTransform已经是finalMatrix了，再加上axisAngleTransform会多做一次镜像，下面对final做一次镜像抵消掉axisAngleTransform
            compoundMatrix.makeFinalMatrix(clipRect.centerX(), clipRect.centerY())
            compoundMatrix.set(compoundMatrix.finalMatrix)
        }
        return compoundMatrix
    }

    /**
     * 发送动画指令给预览页，预览页执行动画变换
     * @param command 动画指令
     */
    private fun postAnimationCommand(vararg command: PreviewAnimationCommand) {
        command.forEach {
            vmBus?.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND, it)
        }
    }

    /**
     * 下一次纹理更新后，执行指定的函数体[block],依赖此时机的一般发生在纹理更新后，需要立刻更新变换参数，否则图片和Transform不对应会闪屏
     */
    private fun runOnNextTextureUpdated(requestCode: Int? = null, block: () -> Unit) {
        val code = requestCode ?: vmBus?.get<EffectRequestData>(TopicID.Pipeline.TOPIC_PIPELINE_EFFECT_REQUEST)?.requestCode ?: let {
            GLog.e(TAG, LogFlag.DL) { "runOnNextTextureUpdated. Error, can't get current requestCode" }
            block.invoke()
            return
        }
        vmBus?.subscribeUntilConsumed<TextureDiff>(TopicID.Preview.TOPIC_PREVIEW_TEXTURE_DIFF, false) {
            val responseCode = vmBus.get<EditorResultState>(TopicID.Pipeline.TOPIC_PIPELINE_EDITOR_RESULT_STATE)?.responseCode
            if (code == responseCode) {
                GLog.d(TAG, LogFlag.DF) { "runOnNextTextureUpdated. Handle texture updated event,code:$responseCode" }
                block.invoke()
                true
            } else {
                GLog.d(TAG, LogFlag.DF) {
                    "runOnNextTextureUpdated. Ignore this response, requestCode:$code, responseCode:$responseCode"
                }
                false
            }
        }
    }

    companion object {
        private const val TAG = "AiCompositionEnterOrExitAnimation"
    }
}