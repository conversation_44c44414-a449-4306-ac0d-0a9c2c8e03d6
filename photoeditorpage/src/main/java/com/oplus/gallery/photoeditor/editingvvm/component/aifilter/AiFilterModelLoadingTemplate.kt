/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AiFilterModelLoadingTemplate.kt
 ** Description : AI调色模型下载模板类
 ** Version     : 1.0
 ** Date        : 2024/3/11
 ** Author      : W9009912
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** W9009912                           2024/2/23      1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.aifilter

import android.content.Context
import com.oplus.gallery.framework.abilities.download.template.DoublePathModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.abilities.download.template.ModelName

/**
 * AI调色模型下载模板类
 */
class AiFilterModelLoadingTemplate(context: Context, modelConfig: ModelConfig) : DoublePathModelLoadingTemplate(context, modelConfig) {

    override val tag: String = TAG

    override val modelName: String = ModelName.AI_FILTER_SOURCE

    companion object {
        private const val TAG = "AiFilterModelLoadingTemplate"
    }
}