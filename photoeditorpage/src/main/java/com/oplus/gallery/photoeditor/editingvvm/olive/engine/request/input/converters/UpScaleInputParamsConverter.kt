/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : UpScaleInputParamsConverter.kt
 ** Description : olive2.0
 ** Version     : 1.0
 ** Date        : 2024/09/10
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>          2024/09/10    1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.engine.request.input.converters

import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.processor.IProcessor
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.processor.UpScaleProcessorImpl
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.request.input.RequestData

class UpScaleInputParamsConverter : IInputConverter {

    override fun convert(srcData: Any): IProcessor.IParams? {
        val data = (srcData as? RequestData) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[convert] check your code, srcData convert fail. return null" }
            return null
        }

        val originalBitmap = data.originalInfo.originalBitmap
        val inputFrame = data.inputBitmap

        /**
         * 背景：olive视频与封面图size虽然是不同的，但比例是相同。只有input进来的图 比原图size小才需要处理
         * 故：
         * 1.当input的帧图与原图比值不一致时说明原图有画框水印
         * 2.当原图与input的帧图比值为小于或者等于1时，即大小一致或者input质量更高时没必要处理，走异常返回逻辑即可
         */
        val upScale = (originalBitmap.width.div(inputFrame.width.toFloat()))
            .coerceAtMost(originalBitmap.height.div(inputFrame.height.toFloat()))
            .takeIf { it > NO_SCALE }
            ?: return null

        return UpScaleProcessorImpl.Params(
            id = data.id,
            inputBitmap = inputFrame,
            upScale = upScale
        )
    }

    private companion object {
        const val TAG = "UpScaleParamsConverter"
        const val NO_SCALE = 1f
    }
}