/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - MenuVM
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.menu

import android.graphics.drawable.Drawable
import androidx.annotation.VisibleForTesting
import com.oplus.aiunit.core.data.UnitState
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper.getBoolean
import com.oplus.gallery.basebiz.helper.restrict.RestrictButtonConfigUtils
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.data.CornerTipType
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.isNullOrEmpty
import com.oplus.gallery.foundation.util.systemcore.FeatureUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_BLUR_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_REFLECTION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IMAGE_QUALITY_ENHANCE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_BEST_TAKE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_COMPOSITION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEBLUE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEGLARE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEREFLECTION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_GRAFFITI
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_BLURRING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_BORDER_CAPTION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_ELIMINATE_PEN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IPU_FILTER
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PORTRAIT_BLUR_AFTER_OS16
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_RM_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAVE_OLIVE
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.effect.IAvEffectInfo
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.REPLY_TOPIC_GET_CURRENT_HASSEL_WATERMARK_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.REPLY_TOPIC_OPERATE_HASSEL_WATERMARK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.TOPIC_MENU_RESTRICT_WATERMARK_CORNER_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_MENU_AUTO_SELECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.REPLY_TOPIC_MENU_LOAD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.REPLY_TOPIC_MENU_SELECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.REPLY_TOPIC_UPDATE_MENU_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.TOPIC_MENU_DATA_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.TOPIC_MENU_EFFECT_LIST
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.TOPIC_MENU_IS_PARTITION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.TOPIC_MENU_MENU_LIST
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.TOPIC_MENU_SELECTED_STRATEGY
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.TOPIC_MENU_UPDATE_WATERMARK_CORNER
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_MODIFY_STATE
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.UnitReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.HasselWatermarkStatus
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.ParametricDataSource
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveSupportCheckStrategy.Companion.isSupportOliveEdit
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.operating.NullRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingMemo
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingVM.Companion.ALL_OPERATING_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.repair.util.SupportUtil
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unregisterDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.oplus.ocs.camera.ipuapi.IPUClient

/**
 * 负责编辑菜单管理
 * @param editingVM 主ViewModel
 */
internal class MenuVM(editingVM: EditingVM) : EditingSubVM(editingVM) {
    /**
     * 点击菜单时，若是数据没准备好记录此menuId，待环境准备好再响应
     */
    private var pendingStrategyId = MENU_ID_INVALID

    private val loadMenuRep = object : UnitReplier<Int>() {
        override fun onSingleReply(arg: Int) {
            notifyLoadMenu(arg)
        }
    }

    private val selectMenuRep = object : UnitReplier<Int>() {
        override fun onSingleReply(arg: Int) {
            notifySelectMenu(arg)
        }
    }

    private val avEffectInfoListRep = object : UnitReplier<List<IAvEffectInfo>>() {
        override fun onSingleReply(arg: List<IAvEffectInfo>) {
            avEffectInfoListPub?.publish(arg)
        }
    }

    // 当前选中的菜单strategyID，区分[PageManagementVM]中的strategyID，后者变更直接切换页面，前者会做切换前的预处理
    private var selectedStrategyIDPub: PublisherChannel<Int>? = null

    // 实际显示的菜单列表（筛选后的），MenuItem为IAvEffectInfo的进一步封装
    private var menuListPub: PublisherChannel<MultiMenuListViewData>? = null
    private var isMenuPartitionPub: PublisherChannel<Boolean>? = null
    private var dataStatusPub: PublisherChannel<DataStatus>? = null

    private var watermarkCornerPub: PublisherChannel<Drawable>? = null

    // 算法支持的元数据，对应一级菜单
    private var avEffectInfoListPub: PublisherChannel<List<IAvEffectInfo>>? = null

    private var updateMenuItemPub: PublisherChannel<Int>? = null
    private val isSupportIpuFilter by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_IPU_FILTER, false)
    }
    private val updateMenuReply = object : UnitReplierChannel {
        override fun onReply(vararg args: Any) {
            updateMenItem()
        }
    }

    private val imagePackObserver: TObserver<EditingImagePack> = {
        onImagePackChanged(it)
    }

    /**
     * record发生变化更新
     */
    private val onModifyStateChanged: TObserver<Boolean> = {
        updateMenItem()
    }

    private val operatingMemoObserver: TObserver<OperatingMemo> = {
        onOperatingMemoChanged(it)
    }

    /**
     * 水印角标的数据，first为角标ID，second为角标Drawable
     */
    private var watermarkCornerInfo: Pair<String?, Drawable?>? = null

    init {
        vmBus?.apply {
            selectedStrategyIDPub = register(TOPIC_MENU_SELECTED_STRATEGY)
            menuListPub = register(TOPIC_MENU_MENU_LIST)
            isMenuPartitionPub = register(TOPIC_MENU_IS_PARTITION)
            dataStatusPub = register(TOPIC_MENU_DATA_STATUS)
            avEffectInfoListPub = registerDuplex(TOPIC_MENU_EFFECT_LIST, avEffectInfoListRep)
            register(REPLY_TOPIC_MENU_LOAD, loadMenuRep)
            register(REPLY_TOPIC_MENU_SELECT, selectMenuRep)
            updateMenuItemPub = registerDuplex(REPLY_TOPIC_UPDATE_MENU_ITEM, updateMenuReply)
            watermarkCornerPub = register(TOPIC_MENU_UPDATE_WATERMARK_CORNER)
        }
    }

    override fun onCreate() {
        super.onCreate()
        vmBus?.apply {
            subscribeT(TOPIC_OPERATING_MODIFY_STATE, onModifyStateChanged)
        }
        isMenuPartitionPub?.publish(EditingMenuPartitioner.IS_MENU_PARTITION)

        initWatermarkCornerInfo()
    }

    /**
     * 获取水印的角标Drawable
     */
    private fun initWatermarkCornerInfo() {
        editingVM.launch(Dispatchers.IO) {
            watermarkCornerInfo = RestrictButtonConfigUtils.getButtonCornerMarkAndCornerId(app)
            if (watermarkCornerInfo == null) {
                GLog.d(TAG, LogFlag.DL) { "[getWatermarkCornerDrawable] watermarkCornerDrawableInfo is null" }
                return@launch
            }
            watermarkCornerInfo?.second?.let {
                watermarkCornerPub?.publish(it)
            }
        }
    }

    /**
     * 切换菜单项
     * @param strategyID 菜单的viewId，也是页面的strategyID，详见[R.array.picture3d_editor_array_preview_state_id_array]
     */
    private fun notifySelectMenu(strategyID: Int) {
        val strategyName = runCatching { app.resources.getResourceEntryName(strategyID) }.getOrNull()
        GLog.d(TAG, LogFlag.DL) { "[notifySelectMenu] strategyName=$strategyName strategyID=$strategyID" }
        if (pendingStrategyId != MENU_ID_INVALID) {
            pendingStrategyId = strategyID
            GLog.w(TAG, LogFlag.DL) { "[notifySelectMenu] strategyName=$strategyName pending strategyID=$pendingStrategyId" }
            return
        }
        updateStrategyIdIfDataReady(strategyID)
        clearWaterMarkCorner(strategyID)
    }

    /**
     * 当水印按键被点击后，清除水印角标
     */
    private fun clearWaterMarkCorner(strategyID: Int) {
        if (strategyID != R.id.strategy_watermark_master) return
        val cornerId = watermarkCornerInfo?.first ?: return
        val item = getDataById(strategyID) ?: return
        if (item.cornerTipType != CornerTipType.RESTRICT_WATERMARK) return

        vmBus?.notifyOnce(TOPIC_MENU_RESTRICT_WATERMARK_CORNER_ID, cornerId)
        item.cornerTipType = CornerTipType.NONE
        updateMenItem()
        watermarkCornerInfo = null

        editingVM.launch(Dispatchers.IO) {
            RestrictButtonConfigUtils.setRestrictWatermarkButtonTipsAlreadyShowed()
        }
    }

    private fun updateStrategyIdIfDataReady(strategyID: Int) {
        if (checkDataReadyOrNot()) {
            pendingStrategyId = MENU_ID_INVALID
            if (strategyID == R.id.strategy_hasselblad_watermark) {
                // OS13.1项目哈苏水印为单独图标，但是添加删除是在一级页完成的
                vmBus?.notifyOnce(REPLY_TOPIC_OPERATE_HASSEL_WATERMARK)
            } else {
                selectedStrategyIDPub?.publish(strategyID)
            }
            // 通知关闭loading
            dataStatusPub?.publish(DataStatus.READY)
        } else {
            pendingStrategyId = strategyID
            // 通知开启loading
            GLog.w(TAG, LogFlag.DL) { "[updateStrategyIdIfDataReady] pending strategyID=$pendingStrategyId" }
            dataStatusPub?.publish(DataStatus.NOT_READY)
        }
    }

    private fun onImagePackChanged(imagePack: EditingImagePack) {
        if (imagePack.watermarkInfo != null) {
            vmBus?.unsubscribe(TopicID.Operating.TOPIC_IMAGE_PACK, imagePackObserver)
            checkDataAfterDataChanged()
        } else {
            GLog.d(TAG, LogFlag.DL) { "[onImagePackChanged] imagePack.watermarkInfo is null " }
        }
    }

    private fun checkDataAfterDataChanged() {
        GLog.d(TAG, LogFlag.DL) { "[onImagePackChanged] pendingId=$pendingStrategyId  " }
        if ((pendingStrategyId != MENU_ID_INVALID) && checkDataReadyOrNot()) {
            updateStrategyIdIfDataReady(pendingStrategyId)
        } else {
            GLog.d(TAG, LogFlag.DL) { "[onImagePackChanged] pendingId=$pendingStrategyId or  subscribeIfDataNotReady not ready" }
        }
    }

    private fun onOperatingMemoChanged(operatingMemo: OperatingMemo) {
        if (operatingMemo.hasRecoverEffects) {
            vmBus?.unsubscribe(TopicID.Operating.TOPIC_OPERATING_OPERATING_MEMO, operatingMemoObserver)
            checkDataAfterDataChanged()
        } else {
            GLog.d(TAG, LogFlag.DL) { "[onOperatingMemoChanged] has not recover effects " }
        }
    }

    private fun checkDataReadyOrNot(): Boolean {
        val watermarkInfo = vmBus?.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK)?.watermarkInfo
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        var isWatermarkValid = true

        if (watermarkInfo == null) {
            isWatermarkValid = false
            GLog.d(TAG, LogFlag.DL) { "[checkDataReadyOrNot] watermarkInfo is null, waiting" }
            vmBus?.subscribeT(TopicID.Operating.TOPIC_IMAGE_PACK, imagePackObserver)
        }

        val operatingMemo = vmBus?.get<OperatingMemo>(TopicID.Operating.TOPIC_OPERATING_OPERATING_MEMO)
        val needToRecover = isSupportParametric || isSupportIpuFilter
        val hasRecoverEffects = if (needToRecover) {
            (operatingMemo?.hasRecoverEffects ?: false) && isWatermarkValid
        } else {
            isWatermarkValid
        }
        //数据化项目进入二级界面需要进行拦截，如果不支持数据化但是进行了文件信息的读取(耗时)，这里也要做拦截等待信息读取完成
        GLog.d(TAG, LogFlag.DL) { "[checkDataReadyOrNot] operatingMemo:$operatingMemo," +
                "isSupportParametric:$isSupportParametric,isSupportIpuFilter:$isSupportIpuFilter,hasRecoverEffects:$hasRecoverEffects" +
                "operatingMemo?.hasRecoverEffects:${operatingMemo?.hasRecoverEffects},isWatermarkValid:$isWatermarkValid" }
        if (!hasRecoverEffects) {
            GLog.d(TAG, LogFlag.DL) { "[checkDataReadyOrNot] hasRecoverEffects condition not met, waiting." }
            vmBus?.subscribeT(TopicID.Operating.TOPIC_OPERATING_OPERATING_MEMO, operatingMemoObserver)
            return false
        }
        return true
    }

    /**
     * 不同的strategyID，会去load不同的menu和算法支持
     * @param strategyID 策略id
     */
    private fun notifyLoadMenu(strategyID: Int) {
        vmBus?.apply {
            val avEffectInfoList = get<List<IAvEffectInfo>>(TOPIC_MENU_EFFECT_LIST)
            if (avEffectInfoList.isNullOrEmpty()) {
                // 监听算法支持列表，获取后过滤并刷新
                subscribe<List<IAvEffectInfo>>(TOPIC_MENU_EFFECT_LIST) {
                    checkAlgoSupportability(strategyID, it)
                }
            } else {
                // 直接过滤不支持的
                avEffectInfoList?.let {
                    checkAlgoSupportability(strategyID, it)
                }
            }
        }
    }

    private fun checkAlgoSupportability(strategyID: Int, it: List<IAvEffectInfo>) {
        if (strategyID == R.id.strategy_editing_page) {
            resolveMenuPartition(
                getLocalMenu().filterUnsupported(
                    it,
                    getWhitelist()
                ).filterDuplicate().checkOfflineStatus().markNewFunction(watermarkCornerInfo?.second)
            )
        } else {
            val isSupport = it.any { info -> info.match(strategyID) }
            if (isSupport.not()) {
                GLog.e(TAG, LogFlag.DL) { "checkAlgoSupportability: Algo for $strategyID is not supported." }
            }
        }
    }

    private fun resolveMenuPartition(menuList: List<EditorMenuItemViewData>) {
        vmBus?.apply {
            val partitionMenuList = EditingMenuPartitioner(editingVM.getApplication()).splitMenu(menuList)
            val isMenuPartition = partitionMenuList.size > 1
            if (get<Boolean>(TOPIC_MENU_IS_PARTITION) != isMenuPartition) {
                isMenuPartitionPub?.publish(isMenuPartition)
            }
            GLog.d(TAG, LogFlag.DL) {
                "[resolveMenuPartition] isMenuPartition:$isMenuPartition. menuListSize=${partitionMenuList.size}"
            }
            menuListPub?.publish(MultiMenuListViewData(partitionMenuList))

            autoSelectMenuIfNeed(isMenuPartition, partitionMenuList)
        }
    }

    /**
     * 菜单数据确认后尝试自动点击某个菜单项。
     * 需要进编辑的入参携带相关参数，这里的自动点击逻辑才会生效。
     */
    private fun autoSelectMenuIfNeed(isMenuPartition: Boolean, partitionMenuList: List<List<EditorMenuItemViewData>>) {
        vmBus?.get<Int>(TOPIC_MENU_AUTO_SELECT)?.let { strategyID ->  // 进编辑参数指定选中菜单项
            // 需要分区 且 自动点击的菜单项在智能菜单页，则先进入智能菜单页，后面再选中子菜单项
            if ((isMenuPartition) && (partitionMenuList[AI_MENU_INDEX].find { it.viewId == strategyID } != null)) {
                notifySelectMenu(R.id.strategy_ai_assistant)
            } else {
                // 无需分区 或者 自动点击的菜单项不在智能页，则直接在一级页选中功能即可
                notifySelectMenu(strategyID)
            }
        }
    }

    /**
     * 返回到一级菜单页更新按钮的状态
     */
    private fun updateMenItem() {
        getDataById(R.id.strategy_olive)?.isDisableStyle = isNeedDisableStyleForOlive()
        getDataById(R.id.strategy_hasselblad_watermark)?.also {
          vmBus?.notifyOnce<HasselWatermarkStatus>(REPLY_TOPIC_GET_CURRENT_HASSEL_WATERMARK_STATUS)?.apply {
              it.isSelectable = isHasselWatermarkEditable
              it.isSelected = hasHasselWatermark && isHasselWatermarkEditable
          }
        }
        getDataById(R.id.strategy_portrait_blur)?.isDisableStyle = isNeedDisableStyleForPortraitBlur()
        listOf(R.id.strategy_olive, R.id.strategy_hasselblad_watermark, R.id.strategy_portrait_blur).onEach {
            getNeedRefreshPositionForId(it)?.let { position ->
                updateMenuItemPub?.publish(position)
            }
        }
    }

    /**
     * 是否需要将实况按钮置灰
     * 只要操作栈中有不在白名单中的操作都需要灰化实况按钮
     */
    private fun isNeedDisableStyleForOlive(): Boolean {
        //不支持olive编辑打通时，在操作非实况编辑的特效后需要灰化实况按钮
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false).not()) {
            val topOperateRecord = vmBus?.notifyOnce<OperatingRecord>(TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_TOP_RECORD)
            GLog.d(TAG, LogFlag.DL) { "isNeedDisableStyleForOlive: $topOperateRecord" }
            return if ((topOperateRecord == null) || (topOperateRecord is NullRecord)) {
                false
            } else {
                topOperateRecord.effectName != AvEffect.OliveEffect.name
            }
        }

        //olive编辑打通时，白名单中的编辑操作不需要灰化入口
        val operateRecords = vmBus?.notifyOnce<List<OperatingRecord>>(
            REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, ALL_OPERATING_RECORDS
        )
        if (operateRecords == null) {
            return false
        }
        return isSupportOliveEdit(operateRecords).not()
    }

    /**
     * 通过对应的id找到对应的position
     */
    private fun getNeedRefreshPositionForId(strategy: Int): Int? {
        val viewData: MultiMenuListViewData? = vmBus?.get<MultiMenuListViewData>(TOPIC_MENU_MENU_LIST)
        viewData?.menuList?.forEach {
            return it.indexOfFirst { editorViewData -> editorViewData.viewId == strategy }
        }
        return null
    }

    /**
     * 通过对应的id找到对应的EditorMenuItemViewData
     */
    private fun getDataById(strategyId: Int): EditorMenuItemViewData? {
        val viewData: MultiMenuListViewData = vmBus?.get<MultiMenuListViewData>(TOPIC_MENU_MENU_LIST) ?: return null
        viewData.menuList.forEach {
            it.forEach { editorMenuItemViewData ->
                if (editorMenuItemViewData.viewId == strategyId) {
                    return editorMenuItemViewData
                }
            }
        }
        return null
    }

    /**
     * 是否需要将人像景深按钮置灰，只要存在非参数化操作就需要将按钮置灰
     */
    private fun isNeedDisableStyleForPortraitBlur(): Boolean {
        val operateRecords = vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, ALL_OPERATING_RECORDS
        )
        return operateRecords?.any { it.isParametric.not() } ?: false
    }

    /**
     * 读取本地的配置，去除不需要的
     */
    private fun getLocalMenu(): List<EditorMenuItemViewData> {
        return EditorUIConfig.initEditorMenuAdapterData(
            editingVM.getApplication(),
            R.array.picture3d_editor_array_preview_state_id_array,
            R.array.picture3d_editor_array_preview_state_icon_array,
            R.array.picture3d_editor_array_preview_state_text_array,
            R.array.picture3d_editor_array_preview_state_item_id_array
        )
    }

    /**
     * 过滤算法不支持的菜单
     */
    private fun List<EditorMenuItemViewData>.filterUnsupported(
        list: List<IAvEffectInfo>,
        whitelist: Set<Int>
    ): List<EditorMenuItemViewData> {
        // 如果算法插件不支持，那就不显示该菜单项
        val supported = mutableListOf<EditorMenuItemViewData>()
        forEach {
            if (it.isEffectSupport().not()) {
                return@forEach
            }
            if (whitelist.contains(it.viewId)) {
                supported.add(it)
            } else {
                list.forEach { avEffectInfo ->
                    if (avEffectInfo.match(it.viewId)) {
                        supported.add(it)
                    }
                }
            }
        }
        return supported
    }

    /**
     * 检查算法是否下线，更新数据源
     */
    private fun List<EditorMenuItemViewData>.checkOfflineStatus(): List<EditorMenuItemViewData> {
        forEach {
            when (it.viewId) {
                R.id.strategy_ai_deblur -> {
                    it.isDisableStyle = ConfigAbilityWrapper.getInt(AI_REPAIR_DE_BLUR_DETECT_STATE) == UnitState.STATE_UNAVAILABLE_OFFLINE
                }

                R.id.strategy_ai_dereflection -> {
                    it.isDisableStyle =
                        ConfigAbilityWrapper.getInt(AI_REPAIR_DE_REFLECTION_DETECT_STATE) == UnitState.STATE_UNAVAILABLE_OFFLINE
                }

                R.id.strategy_image_quality_enhance -> {
                    it.isDisableStyle =
                        ConfigAbilityWrapper.getInt(IMAGE_QUALITY_ENHANCE_DETECT_STATE) == UnitState.STATE_UNAVAILABLE_OFFLINE
                }

                R.id.strategy_ai_composition -> {
                    // “AI构图”入口检查
                    it.isDisableStyle = ConfigAbilityWrapper.getInt(AI_COMPOSITION_DETECT_STATE) == UnitState.STATE_UNAVAILABLE_OFFLINE
                }

                R.id.strategy_ai_best_take -> {
                    it.isDisableStyle = ConfigAbilityWrapper.getInt(AI_BEST_TAKE_DETECT_STATE) == UnitState.STATE_UNAVAILABLE_OFFLINE
                }

                R.id.strategy_ai_lighting -> {
                    // “AI补光”入口检查
                    it.isDisableStyle = ConfigAbilityWrapper.getInt(AI_LIGHTING_DETECT_STATE) == UnitState.STATE_UNAVAILABLE_OFFLINE
                }
                else -> Unit
            }
        }
        return this
    }

    /**
     * 此算法是否支持，不支持此算法时，对应的编辑项不应该显示
     */
    private fun EditorMenuItemViewData.isEffectSupport(): Boolean {
        return when (viewId) {
            R.id.strategy_ai_deglare -> getBoolean(FEATURE_IS_SUPPORT_AI_DEGLARE)
            R.id.strategy_ai_defog -> IPUClient.isFeatureSupport(app, IPUClient.IPUFeature.AI_DEHAZE)
            R.id.strategy_ai_graffiti -> getBoolean(FEATURE_IS_SUPPORT_AI_GRAFFITI)
            R.id.strategy_portrait_blur -> {
                getBoolean(FEATURE_IS_SUPPORT_PORTRAIT_BLUR_AFTER_OS16)
                        && editingVM.sessionProxy.matchesTagFlag(OplusExifTag.EXIF_TAG_PORTRAIT_BLUR)
                        && editingVM.sessionProxy.matchesModel()
            }
            R.id.strategy_ai_deblur -> getBoolean(FEATURE_IS_SUPPORT_AI_DEBLUE)
            R.id.strategy_ai_dereflection -> getBoolean(FEATURE_IS_SUPPORT_AI_DEREFLECTION)
            R.id.strategy_image_quality_enhance -> getBoolean(FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE)
            R.id.strategy_border -> getBoolean(FEATURE_IS_SUPPORT_BORDER_CAPTION)
            R.id.strategy_rm_specific_image_quality_enhance -> getBoolean(FEATURE_IS_SUPPORT_RM_IMAGE_QUALITY_ENHANCE)
            R.id.strategy_rm_ai_scenery -> true
            R.id.strategy_eliminate_pen -> {
                getBoolean(FEATURE_IS_SUPPORT_ELIMINATE_PEN) &&
                    getBoolean(FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE).not()
            }
            R.id.strategy_ai_eliminate -> getBoolean(FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE)
            R.id.strategy_watermark -> false
            R.id.strategy_watermark_master -> getBoolean(FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK)
            R.id.strategy_watermark_camera -> false
            R.id.strategy_hasselblad_watermark -> false
            R.id.strategy_pm_sticker -> FeatureUtils.isSupportPmSticker
            R.id.strategy_sticker -> FeatureUtils.isSupportPmSticker.not()
            R.id.strategy_olive -> {
                val mediaItem: MediaItem? = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
                getBoolean(FEATURE_IS_SUPPORT_OLIVE) &&
                    ((mediaItem?.isOlivePhoto() == true) || editingVM.sessionProxy.matchesTagFlag(OplusExifTag.EXIF_TAG_OLIVE))
            }

            R.id.strategy_blur -> getBoolean(FEATURE_IS_SUPPORT_BLURRING, defValue = true, expDefValue = true)
            R.id.strategy_repair -> {
                SupportUtil.isSupportRepair()
                    && getBoolean(FEATURE_IS_SUPPORT_AI_DEBLUE).not()
                    && getBoolean(FEATURE_IS_SUPPORT_AI_DEREFLECTION).not()
            }
            R.id.strategy_ai_composition -> getBoolean(FEATURE_IS_SUPPORT_AI_COMPOSITION)
            R.id.strategy_ai_best_take -> getBoolean(FEATURE_IS_SUPPORT_AI_BEST_TAKE)
            R.id.strategy_ai_lighting -> getBoolean(FEATURE_IS_SUPPORT_AI_LIGHTING)

            else -> true
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unregisterT(TOPIC_MENU_SELECTED_STRATEGY, selectedStrategyIDPub)
            unregisterT(TOPIC_MENU_MENU_LIST, menuListPub)
            unregisterT(TOPIC_MENU_IS_PARTITION, isMenuPartitionPub)
            unregisterT(TOPIC_MENU_DATA_STATUS, dataStatusPub)
            unregisterDuplexT(TOPIC_MENU_EFFECT_LIST, avEffectInfoListRep, avEffectInfoListPub)
            unregister(REPLY_TOPIC_MENU_LOAD, loadMenuRep)
            unregister(REPLY_TOPIC_MENU_SELECT, selectMenuRep)
            unregisterT(REPLY_TOPIC_UPDATE_MENU_ITEM, updateMenuItemPub)
            unsubscribe(TOPIC_OPERATING_MODIFY_STATE, onModifyStateChanged)
            unregisterT(TOPIC_MENU_UPDATE_WATERMARK_CORNER, watermarkCornerPub)
        }
    }

    internal companion object {
        const val TAG = "MenuVM"
        private const val MENU_ID_INVALID = -1

        /**
         * 基础编辑菜单position
         * 菜单数据会一次全部取出来，再去根据基础、AI功能划分成两个列表（如果需要分区）。
         * 分别在[MenuSection]和[AiAssistantSection]上显示。
         * 其中基础菜单在pos=0的列表位置，智能菜单在pos=1的位置。
         * 如果无需分区，基础功能、AI功能都将出现在基础功能区（MenuSection）
         *
         * @see MultiMenuListViewData
         */
        internal const val BASE_MENU_INDEX = 0

        /**
         * 智能编辑菜单position
         */
        internal const val AI_MENU_INDEX = 1
    }
}

private fun List<EditorMenuItemViewData>.filterDuplicate(): List<EditorMenuItemViewData> {
    // 过滤重复功能（如ai消除1.0、ai消除2.0）
    return this
}

/**
 * 标记新功能，并在菜单显示小红点
 */
private fun List<EditorMenuItemViewData>.markNewFunction(watermarkCornerDrawable: Drawable?): List<EditorMenuItemViewData> {
    val isSupportAiFaceHd = getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_FACE_HD)
    val isSupportWatermarkMaster = getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK)
    forEach { item ->
        when (item.viewId) {
            R.id.strategy_rm_specific_image_quality_enhance -> {
                takeIf { isSupportAiFaceHd }?.let {
                    item.textId = R.string.picture3d_editor_text_ai_face_hd
                    item.iconResId = R.drawable.picture3d_editor_ic_ai_face_hd
                    item.isIconTopTipsShow = true
                    item.cornerTipType = CornerTipType.BATE
                }
            }

            R.id.strategy_ai_graffiti -> item.isCornerTipsShow = isFirstUseAIGraffiti()

            R.id.strategy_watermark_master -> {
                if (isSupportWatermarkMaster && (watermarkCornerDrawable != null)) {
                    item.textId = R.string.picture_editor_text_watermark
                    item.iconResId = R.drawable.photo_editor_ic_watermark
                    item.isIconTopTipsShow = true
                    item.cornerTipType = CornerTipType.RESTRICT_WATERMARK
                    item.cornerDrawable = watermarkCornerDrawable
                }
            }

            else -> Unit
        }
    }
    return this
}

@VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
internal fun isFirstUsingFunction(function: String): Boolean {
    return ConfigAbilityWrapper.getBoolean(function, defValue = true, expDefValue = false)
}

@VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
internal fun isFirstUseAIGraffiti(): Boolean {
    return ConfigAbilityWrapper.getBoolean(
        ConfigID.Business.Editor.AIGraffiti.IS_FIRST_USEAI_GRAFFITI_USED_TIME,
        defValue = true,
        expDefValue = false
    )
}

/**
 * AvEffect和Strategy的映射关系
 * @param strategyId 详见[R.array.picture3d_editor_array_preview_state_id_array]
 */
internal fun IAvEffectInfo.match(strategyId: Int): Boolean {
    return effect == getMatchEffect(strategyId)
}

/**
 * 获取strategyID匹配的AvEffect
 */
internal fun getMatchEffect(strategyID: Int): AvEffect? {
    return when (strategyID) {
        R.id.strategy_beauty -> AvEffect.BeautyEffect
        R.id.strategy_portrait_blur -> AvEffect.PortraitBlurEffect
        R.id.strategy_mosaic -> AvEffect.MosaicEffect
        R.id.strategy_adjustment -> AvEffect.AdjustEffect
        R.id.strategy_filter -> AvEffect.FilterEffect
        R.id.strategy_transform -> AvEffect.TransformEffect
        R.id.strategy_watermark -> AvEffect.WatermarkEffect
        R.id.strategy_watermark_master -> AvEffect.WatermarkEffect
        R.id.strategy_watermark_camera -> AvEffect.WatermarkEffect
        R.id.strategy_hasselblad_watermark -> AvEffect.WatermarkEffect
        R.id.strategy_ai_deblur -> AvEffect.AiRepairEffect
        R.id.strategy_ai_dereflection -> AvEffect.AiRepairEffect
        R.id.strategy_image_quality_enhance -> AvEffect.ImageQualityEnhanceEffect
        R.id.strategy_blur -> AvEffect.BlurEffect
        R.id.strategy_sticker -> AvEffect.StickerEffect
        R.id.strategy_text -> AvEffect.TextEffect
        R.id.strategy_doodle -> AvEffect.DoodleEffect
        R.id.strategy_border -> AvEffect.BorderEffect
        R.id.strategy_rm_specific_image_quality_enhance -> AvEffect.AihdEffect
        R.id.strategy_rm_ai_scenery -> AvEffect.AISceneryEffect
        R.id.strategy_ai_eliminate -> AvEffect.AiEliminateEffect
        R.id.strategy_eliminate_pen -> AvEffect.MagicEliminateEffect
        R.id.strategy_pm_sticker -> AvEffect.PmStickerEffect
        R.id.strategy_olive -> AvEffect.OliveEffect
        R.id.strategy_repair -> AvEffect.RepairEffect
        R.id.strategy_ai_graffiti -> AvEffect.AiGraffitiEffect
        R.id.strategy_ai_composition -> AvEffect.AiCompositionEffect
        R.id.strategy_ai_best_take -> AvEffect.AiBestTakeEffect
        R.id.strategy_ai_lighting -> AvEffect.AiLightingEffect
        R.id.strategy_ai_deglare -> AvEffect.AiRepairRealmeEffect
        R.id.strategy_ai_defog -> AvEffect.AiRepairRealmeEffect
        else -> null
    }
}

/**
 * 豁免白名单，不走管线
 */
internal fun getWhitelist(): Set<Int> {
    return setOf(
        R.id.strategy_olive,
        // 这里先默认豁免插件判断，展示入口先，回头接入插件后移除
        R.id.strategy_ai_best_take)
}

/**
 * 菜单Item，IAvEffectInfo的封装
 */
data class MenuItem(
    /**
     * 策略id
     */
    val strategyID: Int,

    /**
     * 算法元数据
     */
    val effectInfo: IAvEffectInfo? = null
)

/**
 * 编辑首页多级菜单viewData
 * @param menuList 编辑项列表
 */
internal data class MultiMenuListViewData(
    val menuList: List<List<EditorMenuItemViewData>>
)

/**
 * 数据状态
 */
internal enum class DataStatus {
    /**
     * 数据准备完毕
     */
    READY,

    /**
     * 数据收集中
     */
    NOT_READY
}