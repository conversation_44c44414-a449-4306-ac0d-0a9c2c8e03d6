/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OLiveSlowMotionBar.kt
 ** Description: OLive慢动作调节组件
 ** Version: 1.0
 ** Date: 2025/06/13
 ** Author: <EMAIL>
 ** -
 ** ---------------------Revision History: ---------------------
 **  <author>    <date>    <version >    <desc>
 **  <EMAIL>    2025/6/13    1.0    build this module
 ****************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.core.view.isVisible
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.olive.OLiveSlowMotionState
import com.oplus.gallery.photoeditor.editingvvm.olive.Scale
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.Event
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.RangeBarView
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.RangeBarView.SelectedAreaListener

/**
 * OLive慢动作调节组件
 */
class OLiveSlowMotionBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : FrameLayout(context, attrs, defStyleAttr, defStyleRes) {

    /**
     * 慢动作操作组件
     */
    private val slowMotionView: RangeBarView

    /**
     * 慢动作遮罩组件 阻隔操作
     */
    private val slowMotionMaskView: OLiveMaskView

    /**
     * 是否在拖动把手中
     */
    val isDragging get() = slowMotionView.isDragging

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.olive_edit_slow_motion_bar_layout, this, true)
        slowMotionView = view.findViewById(R.id.olive_edit_id_range_bar)
        slowMotionMaskView = view.findViewById(R.id.olive_edit_slow_motion_mask)
    }

    /**
     * 更新慢动作组件的宽度
     * @param width 宽度
     */
    fun updateWidth(width: Int) {
        (layoutParams as RelativeLayout.LayoutParams).width = width
        requestLayout()
    }

    /**
     * 更新慢动作组件的缩放比例
     * @param scale 缩放比例
     */
    fun updateLayoutSize(scale: Scale) {
        scaleX = scale.scaleX
        scaleY = scale.scaleY
    }

    /**
     * 更新RangeBar的状态
     * @param state 慢动作状态
     * @param startPercent 开始位置百分比
     * @param endPercent 结束位置百分比
     */
    fun updateState(
        state: OLiveSlowMotionState,
        multiple: Float,
        startPercent: Float,
        endPercent: Float,
    ) {
        when (state) {
            OLiveSlowMotionState.OPENING -> slowMotionView.migrateState(Event.TO_LOADING)
            OLiveSlowMotionState.OPEN -> {
                slowMotionView.setSelectedAreaRate(multiple)
                slowMotionView.migrateState(Event.LOAD_SUCCESS)
                slowMotionView.setSelectedAreaScope(startPercent, endPercent)
            }

            OLiveSlowMotionState.CLOSED -> slowMotionView.migrateState(Event.CLOSE)
        }
    }

    /**
     * 初始化RangeBar的状态
     * @param state 慢动作状态
     * @param startPercent 开始位置百分比
     * @param endPercent 结束位置百分比
     */
    fun initState(
        state: OLiveSlowMotionState,
        multiple: Float,
        startPercent: Float,
        endPercent: Float,
    ) {
        when (state) {
            OLiveSlowMotionState.OPENING -> slowMotionView.migrateState(Event.TO_LOADING)
            OLiveSlowMotionState.OPEN -> {
                slowMotionView.setSelectedAreaRate(multiple)
                slowMotionView.migrateState(Event.LOAD_SUCCESS)
                slowMotionView.setSelectedAreaScope(startPercent, endPercent)
            }

            else -> Unit
        }
    }

    /**
     * 设置慢动作位置改变监听
     */
    fun setSlowMotionPositionListener(listener: SelectedAreaListener) {
        slowMotionView.selectedAreaListener = listener
    }

    /**
     * 蒙层View是否显示
     * @param isVisible 显示状态
     */
    fun setMaskViewVisible(isVisible: Boolean) {
        slowMotionMaskView.isVisible = isVisible
    }

    /**
     * 设置慢动作View的透明度
     */
    fun setSlowMotionViewAlpha(alpha: Float) {
        slowMotionView.alpha = alpha
    }
}
