/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditingFragment
 ** Description: 编辑fragment
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor

import android.content.Intent
import android.graphics.Color
import android.graphics.ColorSpace
import android.os.Bundle
import android.util.Size
import android.view.View
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.oplus.gallery.basebiz.constants.RouterConstants
import com.oplus.gallery.basebiz.uikit.fragment.TemplateFragment
import com.oplus.gallery.business_lib.model.data.base.item.LocalMediaItem
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.PageLaunchException
import com.oplus.gallery.foundation.archv2.SectionPageManager
import com.oplus.gallery.foundation.archv2.bus.BrokerConfig
import com.oplus.gallery.foundation.archv2.bus.IViewBus
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.bus.VVMBus
import com.oplus.gallery.foundation.archv2.bus.VVMBusBroker
import com.oplus.gallery.foundation.archv2.compositepage.INVALID
import com.oplus.gallery.foundation.archv2.lifecycle.SectionLifecycleDispatcher
import com.oplus.gallery.foundation.archv2.lifecycle.VMLifecycleDispatcher
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.ui.systembar.FragmentSystemBarStyle
import com.oplus.gallery.foundation.ui.systembar.IFragmentSystemBarController
import com.oplus.gallery.foundation.ui.systembar.naviBarInsets
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.SRGB
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.framework.abilities.editing.editor.EditorRequestResult
import com.oplus.gallery.framework.abilities.editing.editor.EditorResultState
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_BACK_TYPE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Output.REPLY_TOPIC_COLLECT_INTENT_FOR_LAUNCH
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Output.REPLY_TOPIC_COLLECT_INTENT_RESULT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_EDITOR_RESULT_STATE
import com.oplus.gallery.photoeditor.editingvvm.adjustment.AdjustmentStrategy
import com.oplus.gallery.photoeditor.editingvvm.aiassistant.AiAssistantStrategy
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.aibesttake.AIBestTakeStrategy
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.AiCompositionStrategy
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.AiCompositionStrategy.AiCompositionSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.aigraffiti.AIGraffitiStrategy
import com.oplus.gallery.photoeditor.editingvvm.aihd.AihdStrategy
import com.oplus.gallery.photoeditor.editingvvm.ailighting.AiLightingSpecialStrategy
import com.oplus.gallery.photoeditor.editingvvm.ailighting.AiLightingStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeFogMongoSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeGlareSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeGlareStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeFogStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeBlurSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeBlurStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeReflectionSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIDeReflectionStrategy
import com.oplus.gallery.photoeditor.editingvvm.aiscenery.AISceneryStrategy
import com.oplus.gallery.photoeditor.editingvvm.beauty.BeautyStrategy
import com.oplus.gallery.photoeditor.editingvvm.blur.BlurStrategy
import com.oplus.gallery.photoeditor.editingvvm.border.BorderStrategy
import com.oplus.gallery.photoeditor.editingvvm.doodle.DoodleStrategy
import com.oplus.gallery.photoeditor.editingvvm.editingpage.EditingPageStrategy
import com.oplus.gallery.photoeditor.editingvvm.eliminate.AIEliminateSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.eliminate.AIEliminateStrategy
import com.oplus.gallery.photoeditor.editingvvm.eliminate.MagicEliminateStrategy
import com.oplus.gallery.photoeditor.editingvvm.eliminate.PasserbyEliminateSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.filter.FilterStrategy
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.ImageQualityEnhanceSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.ImageQualityEnhanceStrategy
import com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.ImageQualityEnhanceTransformEntryStrategy
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.mosaic.MosaicStrategy
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveStrategy
import com.oplus.gallery.photoeditor.editingvvm.operating.BackType
import com.oplus.gallery.photoeditor.editingvvm.output.IntentResult
import com.oplus.gallery.photoeditor.editingvvm.pmsticker.PmStickerStrategy
import com.oplus.gallery.photoeditor.editingvvm.portraitblur.PortraitBlurSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.portraitblur.PortraitBlurStrategy
import com.oplus.gallery.photoeditor.editingvvm.repair.RepairStrategy
import com.oplus.gallery.photoeditor.editingvvm.sticker.StickerStrategy
import com.oplus.gallery.photoeditor.editingvvm.subscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.text.TextStrategy
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformStrategy
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.watermark.PrivacyWatermarkStrategy
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkStrategy
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.WatermarkCameraStrategy
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.PrivacyWatermarkMasterStrategy
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterStrategy
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.WatermarkPersonalizedEditStrategy
import com.oplus.gallery.photoeditor.track.EditErrorCode
import com.oplus.gallery.photoeditor.track.EditExceptionType
import com.oplus.gallery.photoeditor.track.EditorErrorTrackHelper
import com.oplus.gallery.router_lib.annotations.RouterNormal
import com.oplus.gallery.basebiz.R as BaseR

@RouterNormal(RouterConstants.RouterName.EDITING_FRAGMENT)
internal class EditingFragment : TemplateFragment(),
    ISectionBus<EditingFragment, EditingVM> {

    private var strategyNtf: NotifierChannel<Unit>? = null
    private val launchPageObserver: TObserver<Int> = { launchPage(it) }
    private val backObserver: TObserver<BackType> = { back(it) }
    private val resultStateObserver: TObserver<EditorResultState> = {
        if (it.requestResult == EditorRequestResult.RESULT_FAILURE_BY_NOT_SUPPORT_ASSET) {
            mainVM.viewModelBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, NotificationAction.ToastAction(BaseR.string.common_loading_failure))
            trackEditException(it)
            finish()
        }
    }

    /**
     * 当前编辑配置的页面策略集合
     */
    private val pageStrategies = mapOf(
        Pair(R.id.strategy_ai_assistant, AiAssistantStrategy()),
        Pair(R.id.strategy_editing_page, EditingPageStrategy()),
        Pair(R.id.strategy_beauty, BeautyStrategy()),
        Pair(R.id.strategy_portrait_blur, PortraitBlurStrategy()),
        Pair(R.id.strategy_specific_portrait_blur, PortraitBlurSpecificStrategy()),
        Pair(R.id.strategy_rm_specific_image_quality_enhance, AihdStrategy()),
        Pair(R.id.strategy_rm_ai_scenery, AISceneryStrategy()),
        Pair(R.id.strategy_mosaic, MosaicStrategy()),
        Pair(R.id.strategy_adjustment, AdjustmentStrategy()),
        Pair(R.id.strategy_olive, OliveStrategy()),
        Pair(R.id.strategy_filter, FilterStrategy()),
        Pair(R.id.strategy_watermark, WatermarkStrategy()),
        Pair(R.id.strategy_watermark_master, WatermarkMasterStrategy()),
        Pair(R.id.strategy_watermark_camera, WatermarkCameraStrategy()),
        Pair(R.id.strategy_transform, TransformStrategy()),
        Pair(R.id.strategy_image_quality_enhance, ImageQualityEnhanceStrategy()),
        Pair(R.id.strategy_specific_image_quality_enhance, ImageQualityEnhanceSpecificStrategy()),
        Pair(R.id.strategy_image_quality_enhance_transform_entry, ImageQualityEnhanceTransformEntryStrategy()),
        Pair(R.id.strategy_ai_deblur, AIDeBlurStrategy()),
        Pair(R.id.strategy_specific_ai_deblur, AIDeBlurSpecificStrategy()),
        Pair(R.id.strategy_ai_dereflection, AIDeReflectionStrategy()),
        Pair(R.id.strategy_specific_ai_dereflection, AIDeReflectionSpecificStrategy()),
        Pair(R.id.strategy_specific_ai_lighting, AiLightingSpecialStrategy()),
        Pair(R.id.strategy_text, TextStrategy()),
        Pair(R.id.strategy_blur, BlurStrategy()),
        Pair(R.id.strategy_sticker, StickerStrategy()),
        Pair(R.id.strategy_doodle, DoodleStrategy()),
        Pair(R.id.strategy_border, BorderStrategy()),
        Pair(R.id.strategy_ai_eliminate, AIEliminateStrategy()),
        Pair(R.id.strategy_specific_passerby_eliminate, PasserbyEliminateSpecificStrategy()),
        Pair(R.id.strategy_specific_ai_eliminate, AIEliminateSpecificStrategy()),
        Pair(R.id.strategy_eliminate_pen, MagicEliminateStrategy()),
        Pair(R.id.strategy_pm_sticker, PmStickerStrategy()),
        Pair(R.id.strategy_privacy_watermark, PrivacyWatermarkStrategy()),
        Pair(R.id.strategy_privacy_watermark_master, PrivacyWatermarkMasterStrategy()),
        Pair(R.id.strategy_repair, RepairStrategy()),
        Pair(R.id.strategy_watermark_personalized_edit, WatermarkPersonalizedEditStrategy()),
        Pair(R.id.strategy_ai_graffiti, AIGraffitiStrategy()),
        Pair(R.id.strategy_ai_composition, AiCompositionStrategy()),
        Pair(R.id.strategy_ai_best_take, AIBestTakeStrategy()),
        Pair(R.id.strategy_specific_ai_best_take, AIBestTakeSpecificStrategy()),
        Pair(R.id.strategy_specific_ai_composition, AiCompositionSpecificStrategy()),
        Pair(R.id.strategy_ai_lighting, AiLightingStrategy()),
        Pair(R.id.strategy_ai_deglare, AIDeGlareStrategy()),
        Pair(R.id.strategy_specific_ai_deglare, AIDeGlareSpecificStrategy()),
        Pair(R.id.strategy_ai_defog, AIDeFogStrategy()),
        Pair(R.id.strategy_specific_ai_defog, AIDeFogMongoSpecificStrategy()),
    )

    private val sectionPageManager: SectionPageManager by lazy {
        SectionPageManager(
            sectionBus = this,
            strategies = pageStrategies
        )
    }

    private val broker: VVMBusBroker = VVMBusBroker(this).apply {
        config(BrokerConfig(allowTopicReuse = false))
    }

    /**
     * 当前屏幕的显示色域
     */
    val getDisplayColorSpace: ColorSpace by lazy { getDisplayColorSpace() }

    private val vvmBus: VVMBus = broker.createBus()

    private val vmLifecycleDispatcher: VMLifecycleDispatcher by lazy { VMLifecycleDispatcher() }

    override val sectionLifecycleDispatcher: SectionLifecycleDispatcher by lazy { SectionLifecycleDispatcher() }

    override val hostInstance: EditingFragment = this

    override val rootView: View by lazy { requireView().findViewById(R.id.gallery_root) }

    override val mainVM: EditingVM by lazy {
        ViewModelProvider(this)[EditingVM::class.java].also { editingVM ->
            editingVM.pageStrategies = pageStrategies
            editingVM.viewModelBus = this.vvmBus
            editingVM.vmLifecycleDispatcher = vmLifecycleDispatcher
        }
    }

    override fun getLayoutId(): Int = R.layout.picture_fragment_photo_editor

    override val viewBus: IViewBus = vvmBus

    override fun doViewCreated(view: View, savedInstanceState: Bundle?) {
        super.doViewCreated(view, savedInstanceState)
        lifecycle.addObserver(sectionLifecycleDispatcher)
        lifecycle.addObserver(vmLifecycleDispatcher)
        initializeViewModel()
        //进入编辑页加载用户配置的水印样式信息
        WatermarkStyleUserConfigLoaderImpl.getInstance().loadUserConfig()
    }

    private fun initializeViewModel() {
        // 以下调用顺序如非必要，不要调整
        mainVM.apply {
            viewModelBus?.apply {
                // 监听进入、切换页面
                strategyNtf = subscribeDuplexT(TOPIC_MANAGEMENT_STRATEGY_ID, launchPageObserver)
                // 监听页面返回
                subscribeT(TOPIC_OPERATING_BACK_TYPE, backObserver)
                // 监听解码失败退出编辑
                subscribeT(TOPIC_PIPELINE_EDITOR_RESULT_STATE, resultStateObserver)
            }
            // 收集入参
            collectInputArguments(requireActivity(), activity?.intent ?: IntentUtils.EMPTY_INTENT)
            // 必要时入场loading（如三方进编辑）
            loadingIfNecessary()

            createSession()
            notifyCreateSink()
        }
    }

    private fun getDisplayColorSpace(): ColorSpace {
        return if (activity?.window?.isWideColorGamut == true) DISPLAY_P3 else SRGB
    }

    private fun trackEditException(resultState: EditorResultState) {
        val strategyId = vvmBus.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID) ?: INVALID
        vvmBus.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.imageUri?.let { uri ->
            DataManager.getMediaObject(uri) as? LocalMediaItem
        }?.apply {
            EditorErrorTrackHelper.trackEditException(
                EditExceptionType.PHOTO_EXCEPTION.ordinal,
                EditErrorCode.DECODING_EXCEPTION.ordinal,
                resultState.requestResult.resultMsg,
                strategyId,
                this
            )
        }
    }

    /**
     * 启动页面
     * @param id 页面策略id，见[R.array.picture3d_editor_array_preview_state_id_array]
     */
    @Throws(PageLaunchException::class)
    private fun launchPage(id: Int) {
        sectionPageManager.launch(id)
    }

    private fun back(backType: BackType) {
        sectionPageManager.back(
            before = {
                strategyNtf?.notify(it)
            },
            after = { isPageStackEmpty ->
                // 页面栈如果完全退出，此时执行后续退出activity、跳转大图逻辑
                when {
                    (((BackType.LAST_PAGE == backType) && isPageStackEmpty) || (BackType.FINISH_PAGE == backType)) -> finish()
                    ((BackType.TO_PHOTO_PAGE == backType) && isPageStackEmpty) -> {
                        startPhotoPage()
                        finish()
                    }
                }
            }
        )
    }

    private fun getCurrentWindowSize(): Size {
        getCurrentAppUiConfig().let {
            return Size(it.windowWidth.current, it.windowHeight.current)
        }
    }

    private fun finish() {
        if (activity == null || activity?.isFinishing == true) {
            GLog.d(TAG, LogFlag.DL, "[finish] return cause activity is null or isFinishing")
            return
        }
        mainVM.viewModelBus?.apply {
            notifyOnce<IntentResult>(REPLY_TOPIC_COLLECT_INTENT_RESULT, getCurrentWindowSize())?.also {
                activity?.setResult(it.resultCode, it.resultData)
            }
        }
        sectionPageManager.finish()
        activity?.finish()
    }

    private fun startPhotoPage() {
        activity?.let {
            mainVM.viewModelBus?.notifyOnce<Intent?>(REPLY_TOPIC_COLLECT_INTENT_FOR_LAUNCH)?.also { intent ->
                it.startActivity(intent)
                it.overridePendingTransition(
                    com.support.appcompat.R.anim.coui_open_slide_enter,
                    R.anim.picture3d_open_slide_exit
                )
            }
        }
    }

    override fun onPause() {
        super.onPause()
        broker.pause()
    }

    override fun onResume() {
        super.onResume()
        broker.resume()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        GLog.d(TAG, LogFlag.DL) { "onDestroyView." }
        // 销毁Session
        mainVM.destroySession()
        mainVM.viewModelBus?.apply {
            unsubscribeDuplexT(TOPIC_MANAGEMENT_STRATEGY_ID, strategyNtf, launchPageObserver)
            // 监听页面返回
            unsubscribe(TOPIC_OPERATING_BACK_TYPE, backObserver)
            // 监听解码失败退出编辑
            unsubscribe(TOPIC_PIPELINE_EDITOR_RESULT_STATE, resultStateObserver)
        }
        lifecycle.removeObserver(sectionLifecycleDispatcher)
        lifecycle.removeObserver(vmLifecycleDispatcher)
        //退出编辑页清除内存中用户配置的水印样式缓存
        WatermarkStyleUserConfigLoaderImpl.getInstance().clearMemCache()
    }

    override fun onDestroy() {
        super.onDestroy()
        GLog.d(TAG, LogFlag.DL) { "onDestroy." }
        broker.shutdown()
        // 最后兜底再remove一次，对应非正常退出场景（切换语言、暗色模式等）
        sectionLifecycleDispatcher.removeAllObserver()
        vmLifecycleDispatcher.removeAllObserver()
    }

    override fun onKeyDown(keyCode: Int): Boolean {
        return if (sectionLifecycleDispatcher.notifyKeyDown(keyCode)) {
            true
        } else {
            super.onKeyDown(keyCode)
        }
    }

    override fun onKeyUp(keyCode: Int): Boolean {
        return if (sectionLifecycleDispatcher.notifyKeyUp(keyCode)) {
            true
        } else {
            super.onKeyUp(keyCode)
        }
    }

    override fun onKeyLongPress(keyCode: Int): Boolean {
        return if (sectionLifecycleDispatcher.notifyKeyLongPress(keyCode)) {
            true
        } else {
            super.onKeyLongPress(keyCode)
        }
    }

    override fun onKeyMultiple(keyCode: Int, repeatCount: Int): Boolean {
        return if (sectionLifecycleDispatcher.notifyKeyMultiple(keyCode, repeatCount)) {
            true
        } else {
            super.onKeyMultiple(keyCode, repeatCount)
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        sectionLifecycleDispatcher.notifyWindowFocusChanged(hasFocus)
    }

    override fun onSystemBarChanged(windowInsets: WindowInsetsCompat) {
        super.onSystemBarChanged(windowInsets)
        onAppUiStateChanged(getCurrentAppUiConfig())
    }

    override fun getSystemBarStyle(): FragmentSystemBarStyle = object : EditingFragmentSystemBarStyle(this) {
        override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
            super.onUpdate(windowInsets, isForeground)
            if (getCurrentAppUiConfig().isInMultiWindow.current) {
                windowInsets.naviBarInsets().let {
                    setContentPadding(it.left, 0, it.right, 0)
                }
            } else {
                // 将分屏情况下设置的padding还原
                setContentPadding(0, 0, 0, 0)
            }
        }
    }

    override fun onAppUiStateChanged(uiConfig: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(uiConfig)
        sectionLifecycleDispatcher.notifyAppUiStateChanged(uiConfig)
    }

    override fun onBackPressed(): Boolean {
        return if (sectionLifecycleDispatcher.notifyBackPressed(false)) {
            false
        } else {
            super.onBackPressed()
        }
    }

    companion object {
        const val TAG = "EditingFragment"
    }
}

open class EditingFragmentSystemBarStyle(
    fragment: Fragment
) : FragmentSystemBarStyle(fragment) {
    override fun onUpdate(windowInsets: WindowInsetsCompat, isForeground: Boolean) {
        if (isForeground) {
            val controller = fragment as IFragmentSystemBarController
            controller.setStatusBarAppearance(isLight = false)
            controller.setNaviBarColor(Color.TRANSPARENT)
        }
    }
}