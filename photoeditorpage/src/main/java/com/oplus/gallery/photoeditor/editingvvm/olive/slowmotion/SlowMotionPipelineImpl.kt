/*******************************************************************************
 *  Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 *  All rights reserved.
 *   File: - SlowMotionPipelineImpl.kt
 *  Description:
 *   Version: 1.0
 *  Date: 2025/06/12
 *  Author: <EMAIL>
 *  TAG: OPLUS_ARCH_EXTENDS
 *  ------------------------------- Revision History: ----------------------------
 *  <author>                        <date>       <version>    <desc>
 *  ------------------------------------------------------------------------------
 *  h<PERSON><PERSON><PERSON>@Apps.Gallery        2025/06/12      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion

import android.app.Application
import androidx.core.graphics.createBitmap
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.editing.IEditingAbility
import com.oplus.gallery.framework.abilities.editing.asset.IAvAsset
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoBootstrapConfig
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst
import com.oplus.gallery.framework.abilities.editing.bootstrap.InputSourceType
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.session.IEditingSession
import com.oplus.gallery.framework.abilities.editing.track.IAvClip
import com.oplus.gallery.framework.abilities.editing.track.IAvTrack
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.SessionName
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.SlowMotionPipelineImpl.SlowMotionResult.Companion.FAIL_RESULT
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.SlowMotionRecord.Companion.KEY_SLOW_MOTION_RESULT
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.SlowMotionRecord.Companion.RESULT_KEY_RESPONSE_CODE
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.SlowMotionRecord.Companion.RESULT_KEY_RESPONSE_ERROR_MSG
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.SlowMotionRecord.Companion.RESULT_KEY_RESPONSE_RESULT_VIDEOS
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.collections.component1
import kotlin.collections.component2
import kotlin.coroutines.resume

class SlowMotionPipelineImpl(
    private val editingVM: EditingVM,
) : ISlowMotionPipeline {
    private val app = editingVM.getApplication<Application>()
    private var avClip: IAvClip? = null

    private var slowMotionSession: IEditingSession? = null

    /**
     * 创建补光算法子管线
     */
    override fun initSession() {
        slowMotionSession = app.getAppAbility<IEditingAbility>()?.use {
            it.createSession(
                algoBootstrapConfig = SlowMotionAlgoBootstrapConfig()
            ) {
                sessionName = SessionName.SLOW_MOTION_ALGO
                isForeground = true
                longPhotoMinRatio = Config.Loader.LOADER_PHOTO_MAX_RATIO
                photoMaxLength = Config.Render.getMaxTextureSize()
            }
        }?.apply {
            val avTrack = editor.addTrack(TRACK_NAME_SLOW_MOTION, IAvTrack.AvTrackType.VIDEO)
            val avAsset = editor.addAsset(createBitmap(1, 1))
            avClip = editor.addClip(avTrack, avAsset)

            effectManager.supportedVideoEffects()
        }
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun start(record: SlowMotionRecord): SlowMotionResult {
        val avClip = avClip ?: return FAIL_RESULT
        val slowMotionSession = slowMotionSession ?: return FAIL_RESULT
        val effectUsage = slowMotionSession.editor.addEffect(avClip, AvEffect.SlowMotionEffect) {
            record.argumentsForAlgo().forEach { (key, value) ->
                setArg(key, value)
            }
        }

        GLog.d(TAG, LogFlag.DF) { "[start] SlowMotion algorithm started, timeout: ${SLOW_MOTION_TIMEOUT}ms" }

        return withTimeoutOrNull(SLOW_MOTION_TIMEOUT) {
            suspendCancellableCoroutine<SlowMotionResult> {
                effectUsage.addValueObserver(KEY_SLOW_MOTION_RESULT) { key, value ->
                    val result = (value as? Map<String, *>)?.let { SlowMotionResult.fromMap(it) } ?: run {
                        GLog.e(TAG, LogFlag.DF) { "[start] value is invalid:$value" }
                        it.resume(FAIL_RESULT)
                        return@addValueObserver
                    }
                    it.resume(result)
                }
            }
        } ?: run {
            GLog.e(TAG, LogFlag.DF) { "[start] SlowMotion algorithm timeout after ${SLOW_MOTION_TIMEOUT}ms" }
            FAIL_RESULT
        }
    }

    override fun closeSession() {
        val session = slowMotionSession ?: return
        app.getAppAbility<IEditingAbility>()?.closeSession(session.sessionId)
    }

    class SlowMotionResult(
        val code: Int,
        val msg: String?,
        val videoPaths: List<String>?,
    ) {
        companion object {
            val FAIL_RESULT = SlowMotionResult(-1, null, null)


            @Suppress("UNCHECKED_CAST")
            fun fromMap(map: Map<String, *>): SlowMotionResult {
                return SlowMotionResult(
                    map[RESULT_KEY_RESPONSE_CODE] as? Int ?: -1,
                    map[RESULT_KEY_RESPONSE_ERROR_MSG] as? String,
                    map[RESULT_KEY_RESPONSE_RESULT_VIDEOS] as? List<String>,
                )
            }
        }
    }

    /**
     * AI补光算法配置器
     */
    class SlowMotionAlgoBootstrapConfig : IAlgoBootstrapConfig, IAlgoConst {
        override fun onSetupAdd(): Map<AvEffect, String> {
            return mapOf(
                AvEffect.SlowMotionEffect to formatAlgoInfoKey(
                    IAlgoConst.OPPO,
                    IAlgoConst.SLOW_MOTION,
                    1L,
                    InputSourceType.CPU,
                    metaProviderPath = IAlgoConst.SLOW_MOTION_PROVIDER
                )
            )
        }

        override fun onSetupUpdate(): Map<AvEffect, String> = mapOf()

        override fun onSetupRemove(): Set<AvEffect> = setOf()
    }

    companion object {
        private const val TAG = "SlowMotionPipelineImpl"
        private const val TRACK_NAME_SLOW_MOTION = "SlowMotion"

        /**
         * 慢动作算法超时时间，30秒
         * 参考其他算法处理的超时时间，如敏感检测解析30s，AI构图20s等
         */
        private const val SLOW_MOTION_TIMEOUT = 30_000L
    }
}