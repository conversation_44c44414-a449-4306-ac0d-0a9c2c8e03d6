/********************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIBestTakeSessionProxy
 ** Description:最佳表情专用管线代理类
 ** Version: 1.0
 ** Date: 2025-03-08
 ** Author: wudanyang@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** wudanyang@Apps.Gallery3D    2025-03-08     1.0
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aibesttake

import android.app.Application
import android.graphics.Bitmap
import androidx.annotation.WorkerThread
import androidx.collection.ArrayMap
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.framework.abilities.editing.IEditingAbility
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoBootstrapConfig
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AI_BEST_TAKE
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.OPPO
import com.oplus.gallery.framework.abilities.editing.bootstrap.InputSourceType
import com.oplus.gallery.framework.abilities.editing.editor.PipelineEditMode
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.session.IEditingSession
import com.oplus.gallery.framework.abilities.editing.track.IAvClip
import com.oplus.gallery.framework.abilities.editing.track.IAvTrack
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.editingvvm.SessionName

/**
 * AI最佳表情专用的的管线会话代理
 * @param app 上下文
 */
class AIBestTakeSessionProxy(private val app: Application) {

    private var bestTakeSession: IEditingSession? = null
    private var avClip: IAvClip? = null
    private var effectUsage: IAvEffectUsage? = null
    private var cancelCallback: ((Any?) -> Unit)? = null

    /**
     * 创建session
     * @param inputBitmap 输入图，这里其实不用输入，但为了让管线顺利运转，输入一张缩图，最小程度开销
     */
    @WorkerThread
    fun createSession(inputBitmap: Bitmap) {
        bestTakeSession = app.getAppAbility<IEditingAbility>()?.use {
            it.createSession(
                algoBootstrapConfig = AiBestTakeAlgoBootstrapConfig()
            ) {
                sessionName = SessionName.AI_BEST_TAKE
                isForeground = true
                pipelineEditMode = PipelineEditMode.SDR_DISPLAY_MODE
                longPhotoMinRatio = Config.Loader.LOADER_PHOTO_MAX_RATIO
                photoMaxLength = Config.Render.getMaxTextureSize()
            }
        }?.apply {
            val track = editor.addTrack(AI_BEST_TAKE_TRACK, IAvTrack.AvTrackType.VIDEO)
            val asset = editor.addAsset(inputBitmap)
            effectManager.supportedVideoEffects()
            // 先addSink再addClip，切记
            avClip = editor.addClip(track, asset)
        }
    }

    /**
     * 添加一次性effect，监听到指定key的value变化后自动移除，用于执行检测类操作
     * @param avEffect 需要添加的effect
     * @param params effect参数
     * @param key 监听的key
     * @param callback 监听后回调
     */
    @WorkerThread
    fun addObservableEffect(avEffect: AvEffect, params: ArrayMap<String, Any>, key: String, callback: (Any?) -> Unit) {
        val editor = bestTakeSession?.editor.getOrLog(TAG, "addObservableEffect") ?: return
        // 如果上个特效仍在处理中，不再提交新特效，业务调用方要做好任务提交管理
        if (effectUsage != null) {
            GLog.w(TAG, LogFlag.DL) { "addObservableEffect: effectUsage not null , last effect is processing." }
            callback(null)
            return
        }
        avClip?.also { clip ->
            val effect = editor.addEffect(clip, avEffect) {
                params.forEach { (key, value) ->
                    setArg(key, value)
                }
            }
            effectUsage = effect
            cancelCallback = callback
            effect.addValueObserver(key) { _, value ->
                editor.removeEffect(clip, effect, true)
                effectUsage = null
                cancelCallback = null
                callback(value)
            }
        }
    }

    /**
     * 取消特效
     */
    fun cancelEffect() {
        effectUsage?.also { usage ->
            avClip?.also { avClip ->
                GLog.d(TAG, LogFlag.DL) { "[cancelEffect] cancel effect:${usage.effect.name}" }
                bestTakeSession?.editor?.cancelEffectRendering(avClip, usage)
            }
        }
        cancelCallback?.invoke(null)
    }

    /**
     * 关闭Session
     */
    @WorkerThread
    fun destroySession() {
        bestTakeSession?.apply {
            app.getAppAbility<IEditingAbility>()?.closeSession(sessionId)
        }
    }

    companion object {
        private const val TAG = "AIBestTakeSessionProxy"
        private const val AI_BEST_TAKE_TRACK = "AiBestTakeTrack"
    }
}

/**
 * 插件引导程序（只配置必要的最佳表情插件）
 */
private class AiBestTakeAlgoBootstrapConfig : IAlgoBootstrapConfig, IAlgoConst {
    override fun onSetupAdd(): Map<AvEffect, String> {
        return mapOf(
            AvEffect.AiBestTakeEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AI_BEST_TAKE,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.AI_BEST_TAKE_PROVIDER
            )
        )
    }

    override fun onSetupUpdate(): Map<AvEffect, String> {
        return mapOf()
    }

    override fun onSetupRemove(): Set<AvEffect> {
        return setOf()
    }
}