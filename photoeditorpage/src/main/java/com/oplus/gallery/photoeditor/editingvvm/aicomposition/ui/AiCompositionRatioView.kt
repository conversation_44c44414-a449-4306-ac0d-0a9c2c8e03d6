/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AiCompositionRatioView
 ** Description: AI 构图比例显示控件
 **
 ** Version: 1.0
 ** Date: 2025/02/22
 ** Author: 80348695
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  80348695                          2025/02/22  1.0        build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aicomposition.ui

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.RectF
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.gallery.photoeditor.R

/**
 * AI 构图比例显示控件
 */
class AiCompositionRatioView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    /**
     * 比例名称显示的 TextView
     */
    private val ratioTextView: TextView

    /**
     * 是否为小横屏模式
     */
    private val isSmallLandscapeMode: Boolean

    /**
     * 淡入淡出动画集合
     */
    private val animatorSet = AnimatorSet()

    /**
     * 淡入动画
     */
    private val fadeInAnimator = createFadeAnimator(FLOAT_ZERO, FLOAT_ONE)

    /**
     * 淡出动画
     */
    private val fadeOutAnimator = createFadeAnimator(FLOAT_ONE, FLOAT_ZERO)

    /**
     * 停留动画
     */
    private val delayAnimator = createFadeAnimator(FLOAT_ONE, FLOAT_ONE)

    /**
     * 背景控件
     */
    private var aiCompositionBackgroundView: AiCompositionBackgroundView? = null

    /**
     * 背景圆角半径
     */
    private var cornerRadius: Float = 0f

    /**
     * 背景颜色
     */
    private var backgroundColor: Int = 0


    init {
        // 初始透明度为 0
        alpha = FLOAT_ZERO
        // 获取自定义属性
        context.obtainStyledAttributes(attrs, R.styleable.AiCompositionRatioView, defStyleAttr, 0).use { typedArray ->
            isSmallLandscapeMode = typedArray.getBoolean(R.styleable.AiCompositionRatioView_isSmallLandscape, false)
        }

        // 根据是否为小横屏加载不同布局
        val layoutResId = getLayoutResourceId()
        LayoutInflater.from(context).inflate(layoutResId, this, true)

        // 初始化比例名称容器背景
        initRatioBackground()

        // 初始化比例名称 TextView
        ratioTextView = findViewById(R.id.ai_composition_ratio_text)
    }

    /**
     * 启动淡入，停留，淡出动画
     */
    fun startShowAnimationWithFadeOut() {
        if (animatorSet.isRunning) {
            animatorSet.cancel()
        }
        setupAnimatorDurations()
        animatorSet.apply {
            playSequentially(fadeInAnimator, delayAnimator, fadeOutAnimator)
            start()
        }
    }

    /**
     * 设置显示的文本
     * @param textResId 文本资源 ID
     */
    fun setRatioText(textResId: Int) {
        ratioTextView.setText(textResId)
        ratioTextView.post {
            updateBackground()
        }
    }

    /**
     * 更新比例显示控件位置
     *
     * @param clipRect 裁剪矩形区域
     */
    fun updatePosition(clipRect: RectF) {
        if (clipRect.isEmpty || clipRect.width() <= 0 || clipRect.height() <= 0) {
            return
        }
        updateLayoutParams<MarginLayoutParams> {
            val topMarginClip = getTopMargin()
            topMargin = clipRect.top.toInt() + topMarginClip
            leftMargin = clipRect.left.toInt()
            rightMargin = (parent as ViewGroup).width - clipRect.right.toInt()
        }
    }

    /**
     * 销毁
     */
    fun destroy() {
        animatorSet.cancel()
    }

    /**
     * 根据是否为小横屏获取布局资源 ID
     *
     * @return 布局资源 ID
     */
    private fun getLayoutResourceId(): Int {
        return if (isSmallLandscapeMode) {
            R.layout.photo_editor_ai_composition_ratio_view_landscape
        } else {
            R.layout.photo_editor_ai_composition_ratio_view
        }
    }

    /**
     * 获取圆角资源 ID
     */
    private fun getCornerRadiusResourceId(): Float {
        return if (isSmallLandscapeMode) {
            context.resources.getDimension(R.dimen.photo_editor_ai_composition_ratio_corner_radius_landscape)
        } else {
            context.resources.getDimension(R.dimen.photo_editor_ai_composition_ratio_corner_radius)
        }
    }

    /**
     * 初始化比例名称容器背景
     */
    private fun initRatioBackground() {
        aiCompositionBackgroundView = findViewById<AiCompositionBackgroundView>(R.id.ai_composition_ratio_back_ground_view)
        cornerRadius = getCornerRadiusResourceId()
        backgroundColor = context.getColor(R.color.photo_editor_ai_composition_ratio_bg_color)
    }

    /**
     * 创建淡入淡出动画
     *
     * @param startAlpha 动画起始透明度
     * @param endAlpha 动画结束透明度
     * @return 创建好的 ObjectAnimator
     */
    private fun createFadeAnimator(startAlpha: Float, endAlpha: Float): ObjectAnimator {
        return ObjectAnimator.ofFloat(this, ANIMATOR_ALPHA, startAlpha, endAlpha).apply {
            interpolator = COUIEaseInterpolator()
        }
    }

    /**
     * 设置动画持续时间
     */
    private fun setupAnimatorDurations() {
        fadeInAnimator.duration = ANIMATION_DURATION
        delayAnimator.duration = ALPHA_DELAY
        fadeOutAnimator.duration = ANIMATION_DURATION
    }

    /**
     * 更新背景
     */
    private fun updateBackground() {
        aiCompositionBackgroundView?.updateLayoutParams<ViewGroup.LayoutParams> {
            width = ratioTextView.width
        }
        aiCompositionBackgroundView?.drawBackground(backgroundColor, cornerRadius)
    }

    /**
     * 根据是否为小横屏获取顶部边距
     *
     * @return 顶部边距值
     */
    private fun getTopMargin(): Int {
        return if (isSmallLandscapeMode) {
            context.resources.getDimensionPixelSize(R.dimen.photo_editor_ai_composition_ratio_top_margin_landscape)
        } else {
            context.resources.getDimensionPixelSize(R.dimen.photo_editor_ai_composition_ratio_top_margin)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animatorSet.cancel()
    }

    companion object {
        /**
         * 动画持续时间
         */
        private const val ANIMATION_DURATION = 200L

        /**
         * 停留时间
         */
        private const val ALPHA_DELAY = 1000L

        /**
         * 透明度为 0
         */
        private const val FLOAT_ZERO = 0f

        /**
         * 透明度为 1
         */
        private const val FLOAT_ONE = 1f

        /**
         * 动画属性：透明度
         */
        private const val ANIMATOR_ALPHA = "alpha"
    }
}