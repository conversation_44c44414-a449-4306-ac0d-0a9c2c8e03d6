/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : COEAnimListener
 ** Description : COE动效的 动画回调 包装
 ** Version     : 1.0
 ** Date        : 2025/3/25
 ** Author      : gary@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80411420        2025/3/25    1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe

import androidx.annotation.CallSuper
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.animations.AbsCOEAnimation
import com.oplus.vfxsdk.naive.parse.COERenderer

/**
 * COE动效的 监听回调 包装
 */
open class COEAnimListener() : COERenderer.IAnimListener {

    /**
     * 动画生命周期
     * 这里 会把coe动画丢进来 然后在对应生命周期会触发生命周期方法
     * 动画结束后从里面移除
     */
    private val animationActionsMap = HashMap<String, AbsCOEAnimation>()

    /**
     * Put action
     * 把对应动画放到动画map里面去
     * 在到达指定生命周期后会从map里面取并触发改动画生命周期
     * @param name 名称
     * @param absCOEAnimation 动画
     */
    fun putAction(name: String, absCOEAnimation: AbsCOEAnimation) {
        animationActionsMap[name] = absCOEAnimation
    }

    /**
     * On anim end 动画生命周期:结束
     *
     * @param key 动画key
     */
    @CallSuper
    override fun onAnimEnd(key: String) {
        animationActionsMap[key]?.onEnd()
        animationActionsMap.remove(key)
    }

    /**
     * On anim start 动画生命周期:开始
     *
     * @param key 动画key
     */
    @CallSuper
    override fun onAnimStart(key: String) {
        animationActionsMap[key]?.onStart()
    }

    /**
     * On anim update
     * 动画更新回调
     *
     * @param key 动画key
     * @param time 动画时间
     * @param values 动画参数值, 按照 animListenedList 声明的监听列表顺序组织数据
     * 例如： animListenedList = ["a", "b", "c"], 且各自在算法中对应类型为 float， vec2, vec3， 则values：Float[6]
     */
    @CallSuper
    override fun onAnimUpdate(key: String, time: Double, vararg values: Float) {
        animationActionsMap[key]?.onUpdate(time, *values)
    }

    /**
     * Destroy
     * 销毁内部数据
     */
    open fun destroy() {
        animationActionsMap.clear()
    }
}