/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EliminateInteractiveView.kt
 ** Description : 智能消除页交互视图（操作提示，捏合提示和功能引导图标）
 ** Version     : 1.0
 ** Date        : 2023/10/27
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                  <date>      <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2023/10/27     1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.eliminate.view

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import android.widget.RelativeLayout
import androidx.annotation.AttrRes
import androidx.annotation.StringRes
import androidx.annotation.StyleRes
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.isVisible
import com.coui.appcompat.cardview.COUICardView
import com.oplus.gallery.business_lib.template.editor.EditorAnimationUtils
import com.oplus.gallery.foundation.ui.animation.AnimationHelper.PHOTO_ALPHA_INTERPOLATOR
import com.oplus.gallery.foundation.ui.animation.AnimationHelper.PHOTO_SCALE_INTERPOLATOR
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.standard_lib.ui.SuitableSizeTextView

class EliminateInteractiveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr, defStyleRes) {

    val eliminateTipsButton: ImageButton by lazy {
        findViewById(R.id.eliminate_tips_button)
    }

    private val eliminateTipsTextView by lazy {
        findViewById<SuitableSizeTextView>(R.id.eliminate_tips_tv)
    }

    private val eliminateTipsCardView by lazy {
        findViewById<COUICardView>(R.id.eliminate_tips_card_view)
    }

    private var hasShowMoveTips = false

    private var animSet: AnimatorSet? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.picture3d_editor_eliminate_interactive_view, this, true)
    }

    /**
     * 引导按钮是否启用
     */
    var isTipsButtonEnable
        get() = eliminateTipsButton.isEnabled
        set(value) {
            eliminateTipsButton.isEnabled = value
        }

    fun setOnTipsClickListener(listener: OnClickListener?) {
        eliminateTipsButton.setOnClickListener(listener)
    }

    /**
     * 显示操作提示内容
     *
     * @param resId 内容的资源id
     * @param needDelay 是否需要延迟显示
     */
    fun showTipsContent(@StringRes resId: Int, needDelay: Boolean = false) {
        val content = context.resources.getString(resId)
        if (hasShowMoveTips) {
            return
        }
        hasShowMoveTips = resId == R.string.picture3d_editor_text_eliminate_interactive_suggestion_move
        eliminateTipsCardView.let { cardView ->
            eliminateTipsTextView.text = content
            // 显示动画
            val showAnimator = createShowAnimator(cardView)
            // 消失动画
            val hideAnimator = EditorAnimationUtils.createAlphaAnimator(
                cardView,
                false,
                INTERACTIVE_VIEW_ALPHA_ANIM_HIDE_DURATION,
                PHOTO_ALPHA_INTERPOLATOR,
                INTERACTIVE_VIEW_CONTENT_SHOW_TIME
            )

            animSet = AnimatorSet().apply {
                playSequentially(showAnimator, hideAnimator)
                doOnStart { cardView.visibility = View.VISIBLE }
                doOnEnd { cardView.visibility = View.GONE }
                if (isRunning) {
                    cancel()
                }
                if (needDelay) {
                    startDelay = INTERACTIVE_VIEW_CONTENT_ANIM_SHOW_DELAY_TIME
                }
                start()
            }
        }
    }

    fun hideTipsContent() {
        if (eliminateTipsCardView.isVisible) {
            if (animSet?.isRunning == true) {
                animSet?.cancel()
            }
            EditorAnimationUtils.createAlphaAnimator(
                eliminateTipsCardView,
                false,
                INTERACTIVE_VIEW_ALPHA_ANIM_HIDE_DURATION,
                PHOTO_ALPHA_INTERPOLATOR
            ).start()
        }
    }

    private fun createShowAnimator(view: View): AnimatorSet {
        return AnimatorSet().apply {
            val xScale =
                ObjectAnimator.ofFloat(view, View.SCALE_X, INTERACTIVE_VIEW_SCALE_ANIM_FROM_VALUE, INTERACTIVE_VIEW_SCALE_ANIM_TO_VALUE)
                    .apply {
                        duration = INTERACTIVE_VIEW_ALPHA_ANIM_SHOW_DURATION.toLong()
                        interpolator = PHOTO_SCALE_INTERPOLATOR
                    }
            val yScale =
                ObjectAnimator.ofFloat(view, View.SCALE_Y, INTERACTIVE_VIEW_SCALE_ANIM_FROM_VALUE, INTERACTIVE_VIEW_SCALE_ANIM_TO_VALUE)
                    .apply {
                        duration = INTERACTIVE_VIEW_ALPHA_ANIM_SHOW_DURATION.toLong()
                        interpolator = PHOTO_SCALE_INTERPOLATOR
                    }
            val alpha = EditorAnimationUtils.createAlphaAnimator(
                view,
                true,
                INTERACTIVE_VIEW_ALPHA_ANIM_SHOW_DURATION,
                PHOTO_ALPHA_INTERPOLATOR
            )
            playTogether(xScale, yScale, alpha)
        }
    }

    companion object {
        private const val TAG = "EliminateInteractiveView"

        private const val INTERACTIVE_VIEW_CONTENT_ANIM_SHOW_DELAY_TIME = 3000L
        private const val INTERACTIVE_VIEW_CONTENT_SHOW_TIME = 3000
        private const val INTERACTIVE_VIEW_ALPHA_ANIM_SHOW_DURATION = 500
        private const val INTERACTIVE_VIEW_ALPHA_ANIM_HIDE_DURATION = 300
        private const val INTERACTIVE_VIEW_SCALE_ANIM_FROM_VALUE = 1.2F
        private const val INTERACTIVE_VIEW_SCALE_ANIM_TO_VALUE = 1.0F
    }
}