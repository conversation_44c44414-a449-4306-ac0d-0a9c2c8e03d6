/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - SessionName
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2025/1/1 11:28
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2025/1/1		  1.0		 SessionName
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm

/**
 * 在此记录编辑事务的名称
 */
object SessionName {
    /**
     * 编辑的主session
     */
    const val EDITOR_MAIN = "editor_main_session"

    /**
     * 滤镜取缩图session
     */
    const val FILTER_THUMBNAIL = "filter_thumbnail_session"

    /**
     * 获取原始图的缩图session
     */
    const val COLLECT_ORIGIN_THUMBNAIL = "collect_origin_thumbnail_session"

    /**
     * Olive 抽帧去水印session
     */
    const val OLIVE_REMOVE_WATERMARK = "olive_remove_watermark_session"

    /**
     * Olive 视频播放水印背景 session
     */
    const val OLIVE_VIDEO_EDITOR_WATERMARK = "olive_video_editor_watermark_session"

    /**
     * Olive图，获取原图的水印信息
     */
    const val OLIVE_GET_ORIGIN_WATERMARK_DATA = "olive_get_origin_watermark_data_session"

    /**
     * 水印三级页中，对水印的参数设置session，如大师水印中水印参数的设置
     */
    const val WATERMARK_CAMERA_SETTING = "watermark_camera_setting_session"

    /**
     * 获取内容图 session
     */
    const val GET_CONTENT_IMAGE = "get_content_image_session"

    /**
     * 人像景深虚化编辑session
     */
    const val OLD_EDITOR_COMPOSITE_PORTRAIT_BLUR = "old_editor_composite_portrait_blur_session"

    /**
     * 老编辑使用水印的session
     */
    const val OLD_EDITOR_WATERMARK_EDITOR = "old_editor_watermark_editor_session"

    /**
     * 老编辑 ipu 美颜 session
     */
    const val OLD_EDITOR_IPU_BEAUTY = "old_editor_ipu_beauty_session"

    /**
     * Ai构图 请求算法的 session
     */
    const val AI_COMPOSITION_ALGO = "ai_composition_session"

    /**
     * 自动调节 请求算法的 session
     */
    const val AUTO_ADJUST_ALGO = "auto_adjust_session"

    /**
     * 最佳表情 请求算法的 session
     */
    const val AI_BEST_TAKE = "ai_best_take"

    /**
     * 获取中转站/文件管理/三分应用拖拽图片进贴纸编辑的图 session
     */
    const val GET_EXTERNAL_DROP_PICTURE = "get_external_drop_picture_session"

    /**
     * Ai补光 请求算法的session
     */
    const val AI_LIGHTING_ALGO = "ai_lighting_session"

    /**
     * 慢动作 请求算法的session
     */
    const val SLOW_MOTION_ALGO = "slow_motion_session"
}