/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IOliveVideoTransformEditor
 ** Description: IOliveVideoTransformEditor
 ** Version: 1.0
 ** Date : 2024/7/25
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2024/07/25    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform

import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.IOliveVideoEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.data.OliveVideoEditTransEffects

interface IOliveVideoTransformEditor : IOliveVideoEditor {
    /**
     * 旋转裁剪
     * @param transEffects  OliveVideoEditTransEffects
     */
    fun setTransform(transEffects: OliveVideoEditTransEffects)

    /**
     * 是否有旋转裁剪
     */
    fun isHasTransform(): Boolean

    /**
     * 监听timeline大小变化
     */
    fun setOnVideoSizeChangeListener(listener: ((w: Int, h: Int) -> Unit)?)
}