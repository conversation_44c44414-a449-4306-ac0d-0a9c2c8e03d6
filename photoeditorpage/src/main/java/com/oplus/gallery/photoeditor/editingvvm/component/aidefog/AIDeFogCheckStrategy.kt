/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AIDeFogCheckStrategy.kt
 ** Description: xxx
 ** Version: 1.0
 ** Date : 2025/6/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** gongziyao      2025/6/26       1.0          created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.aidefog

import android.content.Context
import android.os.Bundle
import android.util.Size
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitModelDownloadInterceptor.Companion.MODEL_DOWNLOAD_SUCCESS
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.IInterceptor
import com.oplus.gallery.foundation.uiflowinterceptor.RealInterceptorChain
import com.oplus.gallery.photoeditor.editingvvm.component.aideglare.AiDeGlareImageSizeInterceptor

/**
 * realme AI去雾蒙功能支持性检查策略
 */
class AIDeFogCheckStrategy(
    private val imageSize: Size
) : ISupportCheckStrategy {
    private var chain: RealInterceptorChain? = null

    override fun checkSupportability(
        context: Context,
        function: (isSupport: Boolean) -> Unit,
        postNotificationAction: (NotificationAction) -> Unit
    ) {
        val interceptors = getInterceptors(context, postNotificationAction)
        chain = RealInterceptorChain(
            interceptors,
            onSuccessCallback = {
                // 如果当次是模型下载成功的场景，返回false,不做后续进二级页的事务
                val isModelDownloadSuccess = it.getBoolean(MODEL_DOWNLOAD_SUCCESS)
                function(isModelDownloadSuccess.not())
            },
            onFailCallback = { function(false) }
        )
        chain?.proceed(Bundle())
    }

    private fun getInterceptors(context: Context, postNotificationAction: (NotificationAction) -> Unit): List<IInterceptor<Bundle, Unit>> =
        listOf(
            // 图片尺寸限制弹窗，对于过小或过大尺寸的照片
            AiDeGlareImageSizeInterceptor(imageSize, postNotificationAction)
        )

    override fun destroy() {
        chain?.destroy()
        chain = null
    }
}