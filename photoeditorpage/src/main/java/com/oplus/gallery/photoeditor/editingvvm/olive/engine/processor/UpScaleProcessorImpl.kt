/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : UpScaleProcessorImpl.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/08/14
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** tanluya<PERSON>@Apps.Gallery          2024/08/14      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.engine.processor

import android.graphics.Bitmap
import android.os.Bundle
import androidx.annotation.WorkerThread
import com.oplus.breakpad.BreakpadUtil
import com.oplus.breakpad.runNativeGuarding
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.size
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.jni.UpScale
import kotlin.system.measureTimeMillis

/**
 * 放大bitmap处理器
 * 此类只做SDK算法的封装接入，基础入参转换，不做他用
 *
 * olive1.0版本 封面优化方案使用
 *
 * 来源：自研算法
 * 相册SDK封装接入：李运廷（80369408）
 * 基础so算法可联系：王博巍（80378830）
 */
internal class UpScaleProcessorImpl : IProcessor {

    override fun getType(): IProcessor.ProcessorType = IProcessor.ProcessorType.UP_SCALE

    /**
     * UpScale 这里给默认支持吧，实际是否支持要看UpScale.upScaleBitmap是否报错
     * ps：原始逻辑如此
     */
    override fun isSupport(): Boolean {
        return true
    }

    /**
     * 图片放大
     * 说明：JNI的upScaleBitmap接口，后面4个参数详情
     * 参数1：放大倍数（当前接口仅支持放大，倍数不能小于1）
     * 参数2： sharpening strength (0-256), 0 lowest sharpening (no sharpening), 256 highest sharpening
     * 参数3： 缩放的方式（Bicubic，Bilinear）
     * 参数4： 线程数 - 只有这几个数值：1，2，4，8，最大只能到8
     */
    @WorkerThread
    override fun requestProcess(
        requestParams: IProcessor.IParams,
        responseCallback: (requestId: Long, result: Bitmap?, extData: Bundle?) -> Unit
    ) {
        val params = (requestParams as? Params) ?: return
        val requestId = params.id
        var resultBitmap: Bitmap = params.inputBitmap

        var isSuccess = false
        val consuming = measureTimeMillis {
            runNativeGuarding(TAG, BreakpadUtil.generateKey(ID, 0L)) {
                UpScale.upScaleBitmap(
                    params.inputBitmap,
                    params.upScale,
                    UP_SCALE_BITMAP_MAX_SHARPEN,
                    UpScale.ScaleMethod.BICUBIC,
                    UP_SCALE_BITMAP_MAX_THREAD_COUNT
                )
            }.onNativeFailure {
                GLog.e(TAG, "[upScaleBitmap] failed. id: ${params.id}")
            }.getOrNull()?.let {
                isSuccess = true
                BitmapUtils.trySetBitmapColorSpace(it, params.inputBitmap.colorSpace, "$TAG.requestProcess")
                resultBitmap = it
            }
        }

        GLog.d(TAG, LogFlag.DL) {
            "[requestProcess] process finish. process success = $isSuccess, consuming = ${consuming}ms" +
                    " upScale = ${params.upScale}]" +
                    " inputBitmap = ${params.inputBitmap}[${params.inputBitmap.size()}]" +
                    " outputBitmap = $resultBitmap[${resultBitmap.size()}]"
        }


        responseCallback.invoke(requestId, resultBitmap, null)
    }

    data class Params(
        var id: Long,
        val timestamp: Long = System.currentTimeMillis(),
        val inputBitmap: Bitmap,
        val upScale: Float
    ) : IProcessor.IParams()

    private companion object {
        const val TAG = "UpScaleProcessorImpl"
        const val ID = "ParamsV1Id"
        private const val UP_SCALE_BITMAP_MAX_SHARPEN = 256

        /**
         * 此线程数，指的是同一张SDK会多条线程处理
         */
        private const val UP_SCALE_BITMAP_MAX_THREAD_COUNT = 4
    }
}
