/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveVideoEditWatermarkConverter
 ** Description: olive视频编辑水印的转换器。用于把水印封面的操作记录，转换成olive视频需要的数据类
 ** Version: 1.0
 ** Date : 2024/9/25
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2024/9/25    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark

import android.content.Context
import android.graphics.Bitmap
import android.graphics.RectF
import android.util.Size
import com.oplus.gallery.business_lib.helper.BitmapSaveHelper
import com.oplus.gallery.business_lib.model.config.SaveConfig.COMPRESS_QUALITY
import com.oplus.gallery.business_lib.util.readFileList
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.security.Md5Utils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.graphic.BitmapSaveUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.storage.OplusEnvironment
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.editing.IEditingAbility
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.session.IEditingSession
import com.oplus.gallery.framework.abilities.editing.sink.DefaultBitmapSink
import com.oplus.gallery.framework.abilities.editing.sink.IAvSink
import com.oplus.gallery.framework.abilities.editing.track.IAvTrack
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsageScope
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_CUR_PIC_BLUR
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.editingvvm.EditingAlgoBootstrapConfig
import com.oplus.gallery.photoeditor.editingvvm.SessionName
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.data.OliveVideoEditWatermarkEffects
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord.Companion.KEY_WATERMARK_AI_MASTER_IMAGE_DISPLAY_RECT
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.codec.DecodeUtils
import com.oplus.gallery.standard_lib.file.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.regex.Pattern
import kotlin.coroutines.resume

/**
 * olive视频编辑水印的转换器
 * 用于把水印封面的操作记录，转换成olive视频需要的数据类
 */
internal class OliveVideoEditWatermarkConverter(private val context: Context) {

    /**
     * 一个数（包含负数、小数）的正则表达式
     */
    private val numberPattern: Pattern by lazy {
        val regex = "-?\\d+\\.?\\d*"
        Pattern.compile(regex)
    }

    /**
     * 创建olive视频编辑水印的效果数据类。
     * 异步方法，仅画框水印涉及
     *
     * @param videoThumbnail 封面位置的视频帧（此处为添加水印流程，是不带水印的图片）
     * @param watermarkRecord 水印操作记录
     * @param filePath 实况照片文件路径
     * @param lastRecordHashCode 上一次操作记录对应的哈希值。因为调节和滤镜会改变预览图颜色，故需要作为命中缓存条件之一。
     * @param callback 回执转换后的效果数据
     */
    internal fun createEditWatermarkEffects(
        videoThumbnail: Pair<Long, Bitmap?>?,
        watermarkRecord: WatermarkRecord,
        filePath: String?,
        lastRecordHashCode: Int?,
        callback: (effects: OliveVideoEditWatermarkEffects?) -> Unit
    ) {
        val bitmap = videoThumbnail?.second
        bitmap?.takeUnless { it.isRecycled } ?: run {
            GLog.w(TAG, LogFlag.DL, "[createEditWatermarkEffects] invalid bitmap")
            callback.invoke(null)
            return
        }
        val md5 = Md5Utils.getMd5(getStringForVerify(videoThumbnail.first, bitmap, watermarkRecord, filePath, lastRecordHashCode))
        val startTime = System.currentTimeMillis()
        getCacheDirectory().readFileList().find {
            it.name.startsWith(md5)
        }?.also { file ->
            GLog.d(TAG, LogFlag.DL, "[createEditWatermarkEffects] from cache, parse time: ${GLog.getTime(startTime)}.")
            AppScope.launch(Dispatchers.IO) {
                callback.invoke(parseEffectsByFile(file))
            }
            return
        }
        addWatermarkToBitmap(bitmap, watermarkRecord) { outputBitmap, displayRectF ->
            // addWatermarkToBitmap内部切换到主线程了，但createHollowFile有文件操作，需要切换到IO线程
            AppScope.launch(Dispatchers.IO) {
                if ((outputBitmap == null) || outputBitmap.isRecycled || (displayRectF == null)) {
                    GLog.w(TAG, LogFlag.DL, "[createEditWatermarkEffects] hollow watermark bitmap failed. displayRectF: $displayRectF")
                    callback.invoke(null)
                    return@launch
                }
                val file = createHollowFile(spliceHollowFileName(md5, displayRectF), outputBitmap)
                if ((file == null) || file.exists().not()) {
                    GLog.w(TAG, LogFlag.DL, "[createEditWatermarkEffects] save hollow watermark bitmap failed")
                    callback.invoke(null)
                    return@launch
                }
                GLog.d(TAG, LogFlag.DL, "[createEditWatermarkEffects] from new, parse time: ${GLog.getTime(startTime)}.")
                callback.invoke(
                    OliveVideoEditWatermarkEffects(
                        file.absolutePath,
                        Size(outputBitmap.width, outputBitmap.height),
                        displayRectF
                    )
                )
            }
        }
    }

    /**
     * 获取需要校验的信息，对应的字符串
     *
     * 缓存命中条件：
     * 1. 视频宽高尺寸相同
     * 2. 水印样式完全相同（模糊底色用到了图片本身，此场景时，还需要是同一个实况图）
     */
    private fun getStringForVerify(
        timeUs: Long,
        bitmap: Bitmap,
        watermarkRecord: WatermarkRecord,
        filePath: String?,
        lastRecordHashCode: Int?
    ): String {
        val verifyStr = StringBuilder("${bitmap.width}$${bitmap.height}").append(WATERMARK_EFFECT_VERSION)
        watermarkRecord.watermarkInfo?.aiWatermarkMasterParams?.apply {
            verifyStr.append(pluginName).append(styleId).append(styleJsonObject.toString())
                .append(watermarkMasterStyle).append(overlayDisplayRectF).append(imageDisplayRectF)

            /* 水印底色为虚化背景时，需使用视频帧的封面图片。不同实况图，虚化背景都不一致
             * 所以，虚化底色时，需要是同一个文件，且封面位置相同，图片颜色相同，才能命中缓存 */
            if (watermarkMasterStyle?.background?.backgroundType == BG_TYPE_CUR_PIC_BLUR) {
                verifyStr.append(filePath).append(timeUs).append(lastRecordHashCode ?: TextUtil.EMPTY_STRING)
            }
        }
        watermarkRecord.watermarkInfo?.device?.apply {
            verifyStr.append(deviceName).append(isHasselDevice)
        }
        watermarkRecord.watermarkInfo?.content?.also {
            verifyStr.append(it.toString())
        }
        if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
            GLog.d(TAG, LogFlag.DL, "[createEditWatermarkEffects] verifyStr: $verifyStr")
        }
        return verifyStr.toString()
    }

    internal fun clear() {
        getCacheDirectory().readFileList(true).forEachIndexed { index, file ->
            if (index >= MAX_CACHE_FILE_NUM) FileOperationUtils.deleteExistFile(file)
        }
    }

    private fun initAddWatermarkSession(): IEditingSession? {
        return context.getAppAbility<IEditingAbility>()?.use {
            it.createSession(
                algoBootstrapConfig = EditingAlgoBootstrapConfig()
            ) {
                sessionName = SessionName.OLIVE_VIDEO_EDITOR_WATERMARK
                isForeground = true
                longPhotoMinRatio = Config.Loader.LOADER_PHOTO_MAX_RATIO
                photoMaxLength = Config.Render.getMaxTextureSize()
            }
        }
    }

    private fun closeAddWatermarkSession(watermarkSession: IEditingSession?) {
        watermarkSession?.let { session ->
            context.getAppAbility<IEditingAbility>()?.use {
                it.closeSession(session.sessionId)
            }
        }
    }

    /**
     * 获取原图在最终水印图中的显示区域
     */
    private suspend fun getImageDisplayRectF(usage: IAvEffectUsage): RectF = suspendCancellableCoroutine { continuation ->
        val startTime = System.currentTimeMillis()
        var observer: IAvEffectUsage.ValueObserver? = null
        observer = IAvEffectUsage.ValueObserver { _, value ->
            GLog.d(TAG, LogFlag.DL) {
                "[getImageDisplayRectF] parse time: ${GLog.getTime(startTime)}. value: $value, isActive: ${continuation.isActive}"
            }
            if ((value is RectF) && continuation.isActive) {
                continuation.resume(value)
                observer?.also { usage.removeValueObserver(KEY_WATERMARK_AI_MASTER_IMAGE_DISPLAY_RECT, it) }
            }
        }
        usage.addValueObserver(KEY_WATERMARK_AI_MASTER_IMAGE_DISPLAY_RECT, observer)
    }

    /**
     * 获取图片
     */
    private suspend fun getWatermarkBitmap(session: IEditingSession): Bitmap = suspendCancellableCoroutine { continuation ->
        val startTime = System.currentTimeMillis()
        var sink: IAvSink? = null
        sink = DefaultBitmapSink(
            Config.Loader.PHOTO_MAX_LENGTH_HIGHER,
            Config.Loader.PHOTO_MAX_LENGTH_HIGHER
        ) { bitmap, width, height, responseCode ->
            GLog.d(TAG, LogFlag.DL) {
                "[getWatermarkBitmap] parse time: ${GLog.getTime(startTime)}." +
                    " outputBitmap: $bitmap, size: [$width, $height], responseCode: $responseCode"
            }
            if (continuation.isActive) {
                continuation.resume(bitmap)
                sink?.also { session.config.sinkConfig.removeSink(IAvSink.AvSinkType.BITMAP, it) }
            }
        }
        session.config.sinkConfig.addAsOnceFrameSink(IAvSink.AvSinkType.BITMAP, sink)
    }

    /**
     * 给指定的图片添加当前水印信息
     *
     * @param inputBitmap 待添加水印的图
     * @param watermarkRecord 水印操作记录
     * @param resultBlock 添加水印完成后回调，返回水印图，以及原图片在水印图上的偏移量
     */
    private fun addWatermarkToBitmap(
        inputBitmap: Bitmap,
        watermarkRecord: WatermarkRecord,
        resultBlock: (outputBitmap: Bitmap?, displayRectF: RectF?) -> Unit
    ) {
        AppScope.launch(Dispatchers.Main) {
            GLog.d(TAG, LogFlag.DL, "[addWatermarkToBitmap]")
            val watermarkSession = initAddWatermarkSession()

            var usage: IAvEffectUsage? = null
            val effectUsageScope: IAvEffectUsageScope.() -> Unit = {
                watermarkRecord.split().forEach { opRecord ->
                    watermarkSession?.effectManager?.supportedVideoEffects()?.find {
                        it.effect.name == opRecord.effectName
                    }?.supportedArguments().let { effectArguments ->
                        opRecord.argumentsForAlgo(effectArguments).forEach { (key, value) ->
                            setArg(key, value)
                        }
                    }
                }
            }
            watermarkSession?.apply {
                val track = editor.addTrack(TRACK_NAME_HOLLOW_WATERMARK, IAvTrack.AvTrackType.VIDEO)
                val asset = editor.addAsset(inputBitmap)
                val clip = editor.addClip(track, asset)
                val effect = AvEffect.getEffect(watermarkRecord.effectName) ?: return@apply
                usage = editor.addEffect(clip, effect, effectUsageScope = effectUsageScope, render = true)
            }

            val displayRectF = usage?.let {
                async { getImageDisplayRectF(it) }
            }?.await()
            val bitmap = watermarkSession?.let {
                async { getWatermarkBitmap(it) }
            }?.await()
            closeAddWatermarkSession(watermarkSession)
            resultBlock.invoke(bitmap, displayRectF)
        }
    }

    /**
     * 拼接镂空水印图文件的名称（例：a27c2d3434a34d9b75418e46b91613ca_30.0x238.0x1110.0x1678.0.png）
     *
     * @param md5 校验水印的相关参数信息，拼接字串的md5
     * @param displayRectF 视频帧在水印图的位置矩阵
     * @return 返回拼接后的名称
     */
    private fun spliceHollowFileName(md5: String, displayRectF: RectF): String {
        var name = md5
        name += TextUtil.DOWN_LINE
        name += "${displayRectF.left}$MULTIPLY${displayRectF.top}$MULTIPLY${displayRectF.right}$MULTIPLY${displayRectF.bottom}"
        name += BitmapSaveHelper.FORMAT_NAME_PNG
        return name
    }

    /**
     * 从文件、文件名，解析出对应的效果数据
     *
     * @param file 缓存镂空图文件
     */
    private fun parseEffectsByFile(file: File): OliveVideoEditWatermarkEffects? {
        val size = DecodeUtils.decodeBounds(file.absolutePath, null).let {
            Size(it.outWidth, it.outHeight)
        }

        val inputStr = file.name.split(TextUtil.DOWN_LINE).last()
        val matcher = numberPattern.matcher(inputStr)
        val numList = mutableListOf<Float>()
        var outputStr = TextUtil.EMPTY_STRING
        while (matcher.find()) {
            val num = runCatching {
                matcher.group(0).toFloat()
            }.getOrDefault(MathUtils.ZERO_F)
            numList.add(num)
            outputStr += "$num${TextUtil.DOWN_LINE}"
        }
        GLog.d(TAG, LogFlag.DL, "[parseEffectsByFile] inputStr: $inputStr, outputStr: $outputStr")
        val rectF = if (numList.size >= MathUtils.FOUR) {
            val left = numList[MathUtils.ZERO]
            val top = numList[MathUtils.ONE]
            val right = numList[MathUtils.TWO]
            val bottom = numList[MathUtils.THREE]
            RectF(left, top, right, bottom)
        } else null

        return if ((size == null) || (rectF == null)) null else OliveVideoEditWatermarkEffects(file.absolutePath, size, rectF)
    }

    /**
     * 创建镂空水印图文件
     *
     * @param name 文件名
     * @param bitmap 镂空图
     * @return 返回生成的文件对象
     */
    private fun createHollowFile(name: String, bitmap: Bitmap): File? {
        kotlin.runCatching {
            val tempFile = File(getCacheDirectory(), "${Thread.currentThread().id}$name")
            val saveFile = BitmapSaveUtils.saveBitmap(bitmap, bitmap.colorSpace, tempFile, Bitmap.CompressFormat.PNG, COMPRESS_QUALITY)
            val targetFile = File(getCacheDirectory(), name)
            val isSuccess = saveFile?.renameTo(targetFile)
            if (isSuccess == true) {
                return targetFile
            }
        }
        return null
    }

    /**
     * 获取水印缓存文件目录
     */
    private fun getCacheDirectory(): File {
        val cacheDirectory = File(OplusEnvironment.getDataFileDir(), WATERMARK_CACHE_DIR_NAME)
        if (cacheDirectory.exists().not() && cacheDirectory.mkdirs().not()) {
            GLog.e(TAG, LogFlag.DL) {
                "[getCacheDirectory] olive video watermark cache directory: ${PathMask.mask(cacheDirectory.absolutePath)}, mkdirs failed"
            }
        }
        return cacheDirectory
    }

    companion object {
        private const val TAG = "OliveVideoEditWatermarkConverter"
        private const val MULTIPLY = "x"

        /**
         * 镂空图添加水印的轨道名称
         */
        private const val TRACK_NAME_HOLLOW_WATERMARK = "HollowWatermarkBitmap"

        /**
         * 缓存文件存放目录
         */
        private const val WATERMARK_CACHE_DIR_NAME = "watermark_cache_dir"

        /**
         * 可缓存的最大文件数
         */
        private const val MAX_CACHE_FILE_NUM = 15

        /**
         * 水印图的版本号，当之前的水印不适用时，可以升级该版本号，使之前的水印失效，重新生成新的水印
         */
        private const val WATERMARK_EFFECT_VERSION = 1
    }
}