/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - BaseEditingSection
 ** Description: 编辑Section基类
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.content.res.Resources
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.animation.addListener
import androidx.core.view.contains
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAnimationUtils
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.ui.EditorUIExecutor
import com.oplus.gallery.business_lib.template.editor.ui.IEditorUIScheme
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.section.Section
import com.oplus.gallery.foundation.ui.gesture.detector.ScaleRotateDetector
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.EnterAlpha
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationCommand
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.widget.GestureRecognizerView
import java.lang.Math.min

/**
 * 编辑Section基类
 * @param sectionBus 总线
 */
internal abstract class BaseEditingSection(
    val sectionBus: ISectionBus<EditingFragment, EditingVM>
) : Section(sectionBus), IEditorUIScheme {
    protected var sectionContentView: View? = null
    protected var sectionContainerView: ViewGroup? = null
    protected val activity = sectionBus.hostInstance.requireActivity() as BaseActivity
    protected val resources = sectionBus.hostInstance.context?.resources
    protected val context = sectionBus.hostInstance.context
    protected val layoutInflater by lazy {
        activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    }
    protected val vBus = sectionBus.viewBus
    protected val editorUIExecutor = EditorUIExecutor()

    /**
     * 预览动画的属性，除了PreviewSection，其余地方禁止修改
     */
    protected val animationProperties: PreviewAnimationProperties
        get() {
            return sectionBus.viewBus.get(TOPIC_PREVIEW_ANIMATION_PROPERTIES) ?: PreviewAnimationProperties.empty()
        }
    private var enterAnimators: ArrayList<Animator> = arrayListOf()
    private var exitAnimators: ArrayList<Animator> = arrayListOf()
    private var animatorSet: AnimatorSet? = null
    private var editorAppUiConfig: EditorAppUiConfig? = null
    private var orientation: Int = INVALID_INDEX

    override fun onResume() {
        super.onResume()
        GLog.d(TAG, "onResume ${this::class.simpleName}")
    }

    override fun onPause() {
        super.onPause()
        GLog.d(TAG, "onPause ${this::class.simpleName}")
    }

    override fun onStart() {
        super.onStart()
        GLog.d(TAG, "onStart ${this::class.simpleName}")
    }

    override fun onStop() {
        super.onStop()
        GLog.d(TAG, "onStop ${this::class.simpleName}")
    }

    override fun onCreate() {
        super.onCreate()
        GLog.d(TAG, "onCreate ${this::class.simpleName}")
        getContainerViewID().takeIf {
            it != Resources.ID_NULL
        }?.also { containerViewId ->
            sectionBus.rootView.findViewById<ViewGroup>(containerViewId).also {
                sectionContainerView = it
            }
        }
        editorUIExecutor.init(activity)
        // 加载布局
        getContentLayoutId(sectionBus.hostInstance.getCurrentAppUiConfig()).takeIf {
            it != Resources.ID_NULL
        }?.also { layoutId ->
            layoutInflater.inflate(layoutId, sectionContainerView, false)?.also { content ->
                sectionContentView = content
                sectionContainerView?.addView(content)
            }
        }
        editorUIExecutor.setScheme(this, null)
        addSectionContentView()
    }

    private fun addSectionContentView() {
        // 二级页入场动画，统一在基类加一下
        if (getCurrentStrategy()?.pageLevel == 1) {
            sectionContentView?.also {
                addEnterAnimator(
                    EditorAnimationUtils.createAlphaAnimator(
                        it,
                        true,
                        EnterAlpha.DURATION,
                        EnterAlpha.INTERPOLATOR,
                        EnterAlpha.START_DELAY
                    )
                )
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        GLog.d(TAG, "onDestroy ${this::class.simpleName}")
        sectionContentView?.let {
            sectionContainerView?.removeView(it)
            sectionContentView = null
        }
    }

    override fun onPageAssembled() {
        super.onPageAssembled()
        GLog.d(TAG, "onPageAssembled ${this::class.simpleName}")
        if (animatorSet?.isRunning == true) {
            animatorSet?.cancel()
        }
        // 有动画跑动画，没动画直接设置可见性
        sectionContentView?.visibility = View.VISIBLE
        animatorSet = EditorAnimationUtils.startAnimationSet(enterAnimators, null)

        // 二级返回一级需要触发一下app ui状态，暂时在这个地方触发，mark jiepengpeng
        onAppUiStateChanged(sectionBus.hostInstance.getCurrentAppUiConfig())
    }

    override fun onPageDisassembled() {
        super.onPageDisassembled()
        GLog.d(TAG, "onPageDisassembled ${this::class.simpleName}")
        if (animatorSet?.isRunning == true) {
            animatorSet?.cancel()
        }
        // 有动画跑动画，没动画直接设置可见性
        animatorSet = EditorAnimationUtils.startAnimationSet(exitAnimators) {
            sectionContentView?.visibility = View.INVISIBLE
        }
        if (animatorSet == null) {
            sectionContentView?.visibility = View.INVISIBLE
        }
    }

    override fun isAnimating(): Boolean {
        return animatorSet?.isRunning ?: false
    }

    override fun waitUntilAnimStop(func: () -> Unit) {
        animatorSet?.addListener(onEnd = { func.invoke() })
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        val editConfig = editorAppUiConfig?.also {
            it.updateConfig(config, false)
        } ?: EditorAppUiConfig(config, false).also {
            editorAppUiConfig = it
        }
        updateTitleDisplay()
        editorUIExecutor.onAppUiConfigChanged(editConfig)
    }

    /**
     * @param textView 中间button文本显示的内容
     * @param maxButtonWidth 中间button最大长度
     * @param isSetMaxWidth 是否设置最大长度
     */
    private fun setTextViewMarqueeAttribute(textView: TextView, maxButtonWidth: Int, isSetMaxWidth: Boolean) {
        textView.ellipsize = TextUtils.TruncateAt.MARQUEE
        textView.marqueeRepeatLimit = 1
        textView.isSelected = true
        textView.setSingleLine()
        if (isSetMaxWidth) {
            textView.maxWidth = maxButtonWidth
        }
    }

    /**
     * 中间标题恢复默认最大长度设置
     * @param textView 中间button文本显示的内容
     * @param maxButtonWidth 中间button最大长度
     * @param isSetMaxWidth 是否设置最大长度
     */
    private fun restoreTextViewMarqueeAttribute(textView: TextView, maxButtonWidth: Int, isSetMaxWidth: Boolean) {
        textView.ellipsize = TextUtils.TruncateAt.END
        textView.marqueeRepeatLimit = 0
        textView.isSelected = true
        textView.setSingleLine()
        if (isSetMaxWidth) {
            textView.maxWidth = maxButtonWidth
        }
    }

    /**
     * 调整中间标题文本最大显示的最大宽度
     *  横屏、大屏：沿用原来规则
     *  竖屏：取window显示区域的最小边距
     */
    private fun adjustedBtnMaxWidth(): Int {
        val appUiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
        val isLandScape = EditorUIConfig.isEditorLandscape(appUiConfig)
        val isLargeScreen = (appUiConfig.screenMode.current == AppUiResponder.ScreenMode.LARGE)
                || (WindowSizeForm.getSizeForm(appUiConfig) == WindowSizeForm.STANDARD_PORTRAIT_MIDDLE)
        if (isLandScape || isLargeScreen) {
            return sectionBus.rootView.resources.getDimensionPixelSize(
                R.dimen.picture3d_editor_image_quality_enhance_progress_bar_max_width
            )
        }
        return min(sectionBus.rootView.resources.displayMetrics.widthPixels, sectionBus.rootView.resources.displayMetrics.heightPixels)
    }

    /**
     * 显示标题超长滚动的效果
     */
    private fun updateTitleDisplay() {
        val centerView = sectionBus.rootView.findViewById<View>(R.id.editor_id_center_view)
        if (centerView != null) {
            (centerView.layoutParams as? RelativeLayout.LayoutParams)?.apply {
                val appUiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
                val isLandScape = EditorUIConfig.isEditorLandscape(appUiConfig)
                val isLargeScreen =
                    (appUiConfig.screenMode.current == AppUiResponder.ScreenMode.LARGE)
                            || (WindowSizeForm.getSizeForm(appUiConfig) == WindowSizeForm.STANDARD_PORTRAIT_MIDDLE)
                if ((isLandScape || isLargeScreen)) {
                    restoreTextViewMarqueeAttribute(
                        centerView.findViewById<View>(R.id.editor_id_title) as TextView,
                        adjustedBtnMaxWidth(),
                        true
                    )
                } else {
                    removeRule(RelativeLayout.START_OF)
                    removeRule(RelativeLayout.END_OF)
                    val maxButtonWidth: Int =
                        sectionBus.rootView.resources.getDimensionPixelSize(R.dimen.picture3d_bottom_action_bar_text_button_max_width)
                    val buttonMargin: Int =
                        sectionBus.rootView.resources.getDimensionPixelSize(R.dimen.picture3d_bottom_action_bar_text_button_margin_start)
                    marginStart = maxButtonWidth + buttonMargin
                    marginEnd = maxButtonWidth + buttonMargin
                    setTextViewMarqueeAttribute(centerView.findViewById<View>(R.id.editor_id_title) as TextView, maxButtonWidth, true)
                }
                centerView?.layoutParams = this
            }
        }
    }

    protected fun isEditorOrientationChanged(orientation: Int): Boolean {
        val diff = orientation != this.orientation
        this.orientation = orientation
        return diff
    }

    /**
     * 添加入场动画animator
     * @param animator 每个view对应的animator
     */
    protected fun addEnterAnimator(animator: ObjectAnimator) {
        enterAnimators.add(animator)
    }

    /**
     * 清除入场动画list中的数据
     */
    protected fun clearEnterAnimator() {
        enterAnimators.clear()
    }

    /**
     * 添加出场动画animator
     * @param animator 每个view对应的animator
     */
    protected fun addExitAnimator(animator: ObjectAnimator) {
        exitAnimators.add(animator)
    }

    /**
     * 清除退场动画list中的数据
     */
    protected fun clearExitAnimator() {
        exitAnimators.clear()
    }

    protected fun getCurrentStrategy(): IEditingStrategy? {
        return vBus.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID)?.let {
            sectionBus.mainVM.pageStrategies[it]
        }
    }

    /**
     * 在 id/photo_editor_ui_framework 容器中，添加子组件
     *
     * @param view 被添加的组件
     * @param frontView 是指盖在被添加的组件的上一层的组件。可用于确定即将被添加组件的位置，为空则默认放到最上层。
     */
    protected fun addExtraViewToBaseUIView(view: View, frontView: View? = null) {
        if (view.parent != null) {
            GLog.w(TAG) { "[addExtraViewToBaseUIView] view already has parent, add failed" }
            return
        }
        val baseUIView = sectionBus.rootView.findViewById<RelativeLayout>(R.id.photo_editor_ui_framework)
        // 位于自己上层的View的下标位置
        val frontViewIndex = frontView?.let { baseUIView.indexOfChild(it) } ?: INVALID_INDEX
        if (frontViewIndex >= 0) {
            baseUIView.addView(view, frontViewIndex)
        } else {
            baseUIView.addView(view)
        }
    }

    /**
     * 延迟一帧执行runnable
     */
    protected fun post(runnable: Runnable) {
        sectionBus.rootView.post(runnable)
    }

    /**
     * 延迟一定时间执行runnable
     */
    protected fun postDelayed(runnable: Runnable, delayTime: Long) {
        sectionBus.rootView.postDelayed(runnable, delayTime)
    }

    /**
     * 在 id/photo_editor_ui_framework 容器中，移除子组件
     *
     * @param view 被移除的组件
     */
    protected fun removeExtraViewFromBaseUIView(view: View) {
        val baseUIView = sectionBus.rootView.findViewById<RelativeLayout>(R.id.photo_editor_ui_framework)
        baseUIView.removeView(view)
    }

    /**
     * 在 id/default_preview_area 容器中，添加子组件
     *
     * @param view 被添加的组件
     */
    protected fun addExtraViewToPreviewArea(view: View) {
        if (view.parent != null) {
            GLog.w(TAG) { "[addExtraViewToBaseView] view already has parent, add failed" }
            return
        }
        sectionBus.rootView.findViewById<ViewGroup>(R.id.default_preview_area)?.addView(view)
    }

    /**
     * 在 id/default_preview_area 容器中，移除子组件
     *
     * @param view 被移除的组件
     */
    protected fun removeExtraViewFromPreviewArea(view: View) {
        sectionBus.rootView.findViewById<ViewGroup>(R.id.default_preview_area)?.removeView(view)
    }

    /**
     *  在 id/photo_editor_ui_framework 容器中，查找子组件
     *  @param view 要被查找的组件
     *  @return 找到返回true；反之false
     */
    protected fun findExtraViewFromBaseUIView(view: View): Boolean {
        val baseUIView = sectionBus.rootView.findViewById<RelativeLayout>(R.id.photo_editor_ui_framework)
        return baseUIView.contains(view)
    }

    /**
     * 该section对应的容器高度
     * 竖屏时需要动态修改
     */
    open fun getContainerViewHeightResourceId(): Int = Resources.ID_NULL

    /**
     * 该section对应的容器宽度
     * 横屏时需要动态的修改
     */
    open fun getContainerViewWidthResourceId(): Int = Resources.ID_NULL

    final override fun getRootView(): ViewGroup? {
        return sectionContainerView
    }

    /**
     * 子类需要提供section的容器view id
     */
    open fun getContainerViewID(): Int = Resources.ID_NULL

    /**
     * 所属容器下的内容layout id
     */
    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int = Resources.ID_NULL

    override fun getReconfigureViewIds(): List<Int> = emptyList()

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        sectionContainerView?.let { containerView ->
            val isLandscape = EditorUIConfig.isEditorLandscape(config.appUiConfig)
            resetContainerViewParams(containerView, isLandscape)
        }
    }

    private fun resetContainerViewParams(containerView: ViewGroup, isLandscape: Boolean) {
        val layoutParam = containerView.layoutParams
        if (isLandscape && (getContainerViewWidthResourceId() != Resources.ID_NULL)) {
            layoutParam.width = containerView.context.resources.getDimensionPixelOffset(getContainerViewWidthResourceId())
        } else if (isLandscape.not() && (getContainerViewHeightResourceId() != Resources.ID_NULL)) {
            layoutParam.height = containerView.context.resources.getDimensionPixelOffset(getContainerViewHeightResourceId())
        }
        containerView.layoutParams = layoutParam
    }

    /**
     * 显示加载弹窗，用于保存之类的等待耗时操作的场景
     */
    protected fun showLoadingDialog(delayTime: Long = LOADING_DIALOG_SHOW_DELAY_TIME) {
        vBus.notifyOnce(
            TOPIC_NOTIFICATION_ACTION, NotificationAction.LoadingDialogAction(
                isShowing = true,
                delayTime = delayTime,
                displayRect = animationProperties.previewArea
            )
        )
    }

    /**
     * 隐藏加载弹窗
     */
    protected fun hideLoadingDialog(stayTime: Long = LOADING_DIALOG_HIDE_STAY_TIME) {
        vBus.notifyOnce(
            TOPIC_NOTIFICATION_ACTION, NotificationAction.LoadingDialogAction(
                isShowing = false,
                stayTime = stayTime
            )
        )
    }

    /**
     * 对预览图进行缩放旋转的手势监听器
     */
    protected open inner class PreviewScaleRotateGestureListener : GestureRecognizerView.SimpleGestureListener {

        private var lastScale = 1f

        override fun onLongPress(e: MotionEvent): Boolean = false

        override fun onDown(touchX: Float, touchY: Float): Boolean = false

        override fun onScroll(distanceX: Float, distanceY: Float): Boolean {
            vBus.notifyOnce<Boolean>(
                TOPIC_PREVIEW_ANIMATION_COMMAND,
                PreviewAnimationCommand.ContentScroll(distanceX, distanceY)
            )
            return true
        }

        override fun onScaleAndRotateBegin(pivotX: Float, pivotY: Float, angle: Float, scale: Float): Boolean {
            lastScale = scale
            return super.onScaleAndRotateBegin(pivotX, pivotY, angle, scale)
        }

        override fun onScaleAndRotate(pivotX: Float, pivotY: Float, angle: Float, scale: Float, detector: ScaleRotateDetector): Boolean {
            vBus.notifyOnce<Boolean>(
                TOPIC_PREVIEW_ANIMATION_COMMAND,
                PreviewAnimationCommand.ContentScale(scale / lastScale, pivotX, pivotY)
            )
            lastScale = scale
            return true
        }

        override fun onUp(touchX: Float, touchY: Float): Boolean {
            vBus.notifyOnce<Boolean>(
                TOPIC_PREVIEW_ANIMATION_COMMAND,
                PreviewAnimationCommand.SnapBack
            )
            return true
        }
    }

    companion object {
        private const val TAG = "BaseEditingSection"
        private const val INVALID_INDEX = -1
        const val LOADING_DIALOG_SHOW_DELAY_TIME: Long = 500L
        const val LOADING_DIALOG_HIDE_STAY_TIME: Long = 500L
    }
}