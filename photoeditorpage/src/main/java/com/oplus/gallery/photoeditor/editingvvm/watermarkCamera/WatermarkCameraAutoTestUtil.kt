/**************************************************************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - WatermarkCameraAutoTestUtil.kt
 * Description:  通过uriList来获取文件，并写入对应目录
 * Version: 1.0
 * Date: 2025/3/25
 * Author: sunwenli
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * sunwenli 2025/3/25     1.0              create
 **************************************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.watermarkCamera

import android.app.Application
import android.net.Uri
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.restrict.RestrictButtonConfigUtils
import com.oplus.gallery.business_lib.template.editor.data.ItemStatus
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_LUMO_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_ONEPLUS_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_OPPO_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.abilities.watermark.IWatermarkStyleLoader
import com.oplus.gallery.framework.abilities.watermark.util.WatermarkStyleDataProcessor
import com.oplus.gallery.framework.abilities.watermarkmaster.entry.RestrictWatermarkResourceEntry
import com.oplus.gallery.framework.app.GalleryApplication
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.WatermarkStyleItemViewData
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import java.nio.file.Files
import java.nio.file.Paths

/**
 * 相机自动化测试的工具类。
 * 相册收到相机发送的自动化消息后，需要将所有的水印资源打包发送给相机
 */
class WatermarkCameraAutoTestUtil(
    val context: Application,
) {

    private fun createEmptyFile(directory: String, fileName: String, message: String?) {
        // 创建路径
        val path = Paths.get(directory, fileName)
        var file: File? = null
        kotlin.runCatching {
            // 如果文件不存在，则创建一个空文件
            if (!Files.exists(path)) {
                Files.createFile(path)
                GLog.d(TAG, "File created successfully: $path")
            }
            if (message != null) {
                file = File(path.toString())
                file?.appendText("$message \n")
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "createEmptyFile failed. reason: $it" }
        }
    }

    private fun clearDirectory(directoryPath: String) {
        val directory = File(directoryPath)

        // 检查是否是目录，并且目录是否存在
        if (directory.exists() && directory.isDirectory) {
            // 遍历目录中的所有文件和子目录
            val files = directory.listFiles()

            files?.forEach { file ->
                if (file.isDirectory) {
                    // 如果是子目录，递归调用清空方法
                    clearDirectory(file.absolutePath)
                } else {
                    // 如果是文件，删除文件
                    file.delete()
                }
            }
            // 删除空目录
            if (directory.listFiles().isNullOrEmpty()) {
                directory.delete()
            }
        }
    }

    /**
     * 根据uriList来获取文件，并写入对应目录
     */
    private fun readResourcesFromUris(resourceUris: ArrayList<Uri>, subPath: String) {
        createWatermarkResourceFolder(subPath)

        for (uri in resourceUris) {
            readUriWriteToFile(uri, subPath)
        }
    }

    private fun readUriWriteToFile(uri: Uri, path: String) {
        kotlin.runCatching {
            // 根据uri获取文件名并生成绝对路径
            val uriContents = uri.toString().split("/")
            val fileName = uriContents[uriContents.size - 1]
            val resPath = "$path/$fileName"
            // 根据绝对路径创建空文件
            val file = File(resPath)
            file.createNewFile()
            // 根据uri打开输入流
            val inputStream = context.contentResolver.openInputStream(uri)
            inputStream?.use { stream ->
                // 创建输出流用于写入数据
                FileOutputStream(resPath).use { output ->
                    // 将数据从输入流复制到输出流
                    val buffer = ByteArray(NUMBER_1024)
                    var bytesRead: Int
                    while (stream.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                    }
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL) { "readUriWriteToFile-aiMaster failed. reason: $it" }
        }
    }

    private fun createWatermarkResourceFolder(folderPath: String) {
        val folder = File(folderPath)
        if (!folder.exists()) {
            folder.mkdirs()
        }
        clearWatermarkResourceFolderFiles(folder)
    }

    private fun clearWatermarkResourceFolderFiles(folder: File) {
        // 检查是否是目录，并且目录是否存在
        if (folder.exists() && folder.isDirectory) {
            // 遍历目录中的所有文件和子目录
            folder.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    // 递归清理子目录
                    clearWatermarkResourceFolderFiles(file)
                }
                // 删除文件并处理删除结果
                file.delete()
            }
        }
    }

    private fun convertStringArrayToUriList(stringArray: Array<String>): ArrayList<Uri> {
        val uriList = ArrayList<Uri>()
        for (str in stringArray) {
            val uri = Uri.parse(str)
            uriList.add(uri)
        }
        return uriList
    }

    /**
     * 获取所有的图片和视频水印资源
     */
    fun getAllPhotoAndVideoListViewData(
        watermarkStyleLoader: IWatermarkStyleLoader?,
        isFromCameraEditType: Boolean,
        restrictStyleListViewData: MutableList<WatermarkStyleItemViewData>,
        allVideoListViewData: List<WatermarkStyleItemViewData>,
        callback: (
            Pair<MutableList<WatermarkStyleItemViewData>,
                List<WatermarkStyleItemViewData>>
        ) -> Unit
    ) {
        val allPhotoListViewData = mutableListOf<WatermarkStyleItemViewData>()
        val watermarkMasterAbility = ContextGetter.context.getAppAbility<IWatermarkMasterAbility>() ?: run {
            GLog.e(WatermarkMasterVM.TAG, LogFlag.DL) { "[getAllPhotoAndVideoListViewData] ability is null" }
            callback.invoke(Pair(mutableListOf(), listOf()))
            return
        }
        watermarkMasterAbility.use { ability ->
            var loader = watermarkStyleLoader
            if (loader == null) {
                loader = ability.newWatermarkStyleLoader()
            }
            loader.loadBuiltInAllStyles(
                AppScope, isFromCameraEditType,
                ConfigAbilityWrapper.getBoolean(IS_OPPO_BRAND),
                ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND),
                ConfigAbilityWrapper.getBoolean(IS_ONEPLUS_BRAND),
                ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not(),
                isFromCameraEditType.not()
                    && RestrictButtonConfigUtils.isOnLineTime().not(),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)
            ) { styleMap ->
                styleMap.keys.forEach {
                    allPhotoListViewData.add(WatermarkStyleItemViewData(it.styleId, it.title, it.description))
                }
                allPhotoListViewData.addAll(restrictStyleListViewData)
                callback.invoke(Pair(allPhotoListViewData, allVideoListViewData))
            }
        }
    }

    /**
     * 将获取到的的图片和视频水印资源分为两类，一类是本地存在不需要下载的，一类是需要联网下载的
     */
    private fun loadAllStyleListViewDataAsync(
        styleLoader: IWatermarkStyleLoader?,
        allPhotoAndVideoListViewData: Pair<MutableList<WatermarkStyleItemViewData>, List<WatermarkStyleItemViewData>>,
        getMaterialEntitys: (styleId: String) -> MutableList<RestrictWatermarkResourceEntry>,
        checkMaterialDownloadedStatus: (styleId: String, MutableList<RestrictWatermarkResourceEntry>) -> ItemStatus,
        callback: (
            photoDownloadedDataList: MutableList<WatermarkStyleItemViewData>?,
            photoNeedDownLoadDataList: MutableList<WatermarkStyleItemViewData>?,
            videoDownloadDataList: MutableList<WatermarkStyleItemViewData>?,
            videoNeedDownloadDataList: MutableList<WatermarkStyleItemViewData>?
        ) -> Unit,
    ) {
        val watermarkMasterAbility = ContextGetter.context.getAppAbility<IWatermarkMasterAbility>() ?: run {
            GLog.e(TAG, LogFlag.DL) { "[loadAllStyleListViewDataAsync] ability is null" }
            callback.invoke(null, null, null, null)
            return
        }
        var watermarkStyleLoader = styleLoader
        watermarkMasterAbility.use { ability ->
            if (watermarkStyleLoader == null) {
                watermarkStyleLoader = ability.newWatermarkStyleLoader()
            }
            val photoDownloadedDataList: MutableList<WatermarkStyleItemViewData> = java.util.ArrayList<WatermarkStyleItemViewData>()
            val photoNeedDownLoadDataList: MutableList<WatermarkStyleItemViewData> = java.util.ArrayList<WatermarkStyleItemViewData>()
            val videoDownLoadedDataList: MutableList<WatermarkStyleItemViewData> = java.util.ArrayList<WatermarkStyleItemViewData>()
            val videoNeedDownLoadDataList: MutableList<WatermarkStyleItemViewData> = java.util.ArrayList<WatermarkStyleItemViewData>()
            allPhotoAndVideoListViewData.first.forEach {
                val entityList = getMaterialEntitys(it.styleId)
                val status = checkMaterialDownloadedStatus(it.styleId, entityList)
                if (status == ItemStatus.DEFAULT) {
                    photoDownloadedDataList.add(WatermarkStyleItemViewData(it.styleId, it.title, it.description))
                } else {
                    photoNeedDownLoadDataList.add(WatermarkStyleItemViewData(it.styleId, it.title, it.description))
                }
            }
            allPhotoAndVideoListViewData.second.forEach {
                val entityList = getMaterialEntitys(it.styleId)
                val status = checkMaterialDownloadedStatus(it.styleId, entityList)
                if (status == ItemStatus.DEFAULT) {
                    videoDownLoadedDataList.add(WatermarkStyleItemViewData(it.styleId, it.title, it.description))
                } else {
                    videoNeedDownLoadDataList.add(WatermarkStyleItemViewData(it.styleId, it.title, it.description))
                }
            }
            GLog.i(TAG, LogFlag.DL) {
                "[loadAllStyleListViewDataAsync] photoDownloadedDataList: ${photoDownloadedDataList.size}" +
                    " needDownLoadedDataList: ${photoNeedDownLoadDataList.size} " +
                    "videoDownLoadedDataList: ${videoDownLoadedDataList.size}  " +
                    "videoNeedDownLoadDataList: ${videoNeedDownLoadDataList.size}"
            }
            callback.invoke(photoDownloadedDataList, photoNeedDownLoadDataList, videoDownLoadedDataList, videoNeedDownLoadDataList)
        }
    }

    private fun loadStyleJson(styleId: String, loader: IWatermarkStyleLoader): WatermarkCameraSettingOperator? {
        val source = WatermarkStyleDataProcessor.getStyleSource(styleId) ?: let {
            GLog.e(TAG, LogFlag.DL, "[loadStyleJson] styleId:$styleId, styleSource is null")
            return null
        }
        // 相机编辑不应该受相册编辑的影响
        return loader.loadStyleSync(source, true)?.let {
            WatermarkCameraSettingOperator(context, it.first)
        }
    }

    /**
     * 将所有的水印数据发送给相机
     * @param filePath 相机传来的，需要将水印资源写入该路径
     */
    fun sendAllStyleDataToCamera(
        filePath: String,
        watermarkStyleLoader: IWatermarkStyleLoader?,
        allPhotoAndVideoListViewData: Pair<MutableList<WatermarkStyleItemViewData>, List<WatermarkStyleItemViewData>>,
        getMaterialEntitys: (styleId: String) -> MutableList<RestrictWatermarkResourceEntry>,
        checkMaterialDownloadedStatus: (styleId: String, MutableList<RestrictWatermarkResourceEntry>) -> ItemStatus,
        getUriStringList: (operator: WatermarkCameraSettingOperator?) -> Array<String>?
    ) {
        GLog.d(TAG, LogFlag.DL, "[sendAllStyleDataToCamera] filePath = $filePath")
        clearDirectory(filePath)
        loadAllStyleListViewDataAsync(
            watermarkStyleLoader,
            allPhotoAndVideoListViewData,
            getMaterialEntitys,
            checkMaterialDownloadedStatus
        ) { photoDownLoadDataList, photoNeedDownLoadDataList, videoDataList, videoNeedDownloadList ->
            (context.applicationContext as? GalleryApplication)?.getAppAbility(IWatermarkMasterAbility::class.java)?.use { ability ->
                val loader = watermarkStyleLoader ?: ability.newWatermarkStyleLoader()
                AppScope.launch(Dispatchers.IO) {
                    suspend fun processData(dataList: MutableList<WatermarkStyleItemViewData>?, type: String) {
                        dataList?.forEach { data ->
                            val uriLoader = async { loadStyleJson(data.styleId, loader) }
                            val uriStringList = getUriStringList(uriLoader.await())
                            uriStringList?.let {
                                val path =
                                    "$filePath${TextUtil.LEFT_SLASH}$type${TextUtil.LEFT_SLASH}${data.styleId}${TextUtil.LEFT_SLASH}photo_watermark"
                                readResourcesFromUris(
                                    convertStringArrayToUriList(it),
                                    path
                                )
                            }
                        }
                    }
                    processData(photoDownLoadDataList, "photo")
                    processData(videoDataList, "video")
                    val result = when {
                        photoNeedDownLoadDataList?.isEmpty() == true && videoNeedDownloadList?.isEmpty() == true -> "success"
                        else -> "fail"
                    }
                    createEmptyFile(filePath, result, null)
                    photoNeedDownLoadDataList?.forEach { data ->
                        createEmptyFile(filePath, "fail", data.styleId)
                    }
                    videoNeedDownloadList?.forEach { data ->
                        createEmptyFile(filePath, "fail", data.styleId)
                    }
                }
            }
        }
    }

    companion object {
        private const val TAG = "WatermarkCameraAutoTestUtil"
        private const val NUMBER_1024 = 1024
    }
}
