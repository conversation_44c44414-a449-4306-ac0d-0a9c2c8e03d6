/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditingAlgoBootstrap
 ** Description:
 **
 ** Version: 1.0
 ** Date: 2024/5/22 12:44
 ** Author: Wanglian
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** Wanglian                        2024/5/22		  1.0		 EditingAlgoBootstrap
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AIEDITPICTURE
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AIELIMINATE
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AIGRAFFITI
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AI_LIGHTING
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AIHD
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AIREPAIR
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AIWATERMARK
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AI_BEST_TAKE
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.AI_COMPOSITION
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.BEAUTY
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.BLUR
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.BORDER
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.MAGICELIMINATE
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.MEICAM
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.MOSAIC
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.OPPO
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.PORTRAIT_BLUR
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.REPAIR
import com.oplus.gallery.framework.abilities.editing.bootstrap.IAlgoConst.Companion.TEXTURE_BLEND
import com.oplus.gallery.framework.abilities.editing.bootstrap.InputSourceType
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect

/**
 * 编辑事务特效算法映射配置表
 */
class EditingAlgoBootstrapConfig : BaseAlgoBootstrapConfig(), IAlgoConst {

    @Suppress("LongMethod")
    override fun onBaseSetupAdd(): Map<AvEffect, String> {
        return mapOf(
            AvEffect.BeautyEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = if (isSupportIpuBeauty) IAlgoConst.BEAUTY_IPU else BEAUTY,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.BEAUTY_META_PROVIDER
            ),
            AvEffect.PortraitBlurEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = PORTRAIT_BLUR,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.PORTRAIT_BLUR_PROVIDER
            ),
            AvEffect.MosaicEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = MOSAIC,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.MOSAIC_META_PROVIDER
            ),
            AvEffect.AiRepairEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AIREPAIR,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.AIREPAIR_META_PROVIDER
            ),
            AvEffect.AiRepairRealmeEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AIEDITPICTURE,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.AIEDITPICTURE_META_PROVIDER
            ),
            AvEffect.ImageQualityEnhanceEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = IMAGE_QUALITY_ENHANCE,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.IMAGE_QUALITY_ENHANCE_META_PROVIDER
            ),
            AvEffect.AiCompositionEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AI_COMPOSITION,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.IMAGE_AI_COMPOSITION_PROVIDER
            ),
            AvEffect.AiWatermarkEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AIWATERMARK,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.AIWATERMARK_META_PROVIDER
            ),
            AvEffect.AiEliminateEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AIELIMINATE,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.AIELIMINATE_META_PROVIDER
            ),
            AvEffect.MagicEliminateEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = MAGICELIMINATE,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.MAGICELIMINATE_META_PROVIDER
            ),
            AvEffect.BlurEffect to formatAlgoInfoKey(
                source = MEICAM,
                algoName = BLUR,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.BLUR_META_PROVIDER
            ),
            AvEffect.StickerEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = TEXTURE_BLEND,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.TEXTURE_BLEND_META_PROVIDER
            ),
            AvEffect.DoodleEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = TEXTURE_BLEND,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.TEXTURE_BLEND_META_PROVIDER
            ),
            AvEffect.BorderEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = BORDER,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.BORDER_META_PROVIDER
            ),
            AvEffect.TextureBlendEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = TEXTURE_BLEND,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.TEXTURE_BLEND_META_PROVIDER
            ),
            AvEffect.TextEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = TEXTURE_BLEND,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.TEXTURE_BLEND_META_PROVIDER
            ),
            AvEffect.PmStickerEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = TEXTURE_BLEND,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.TEXTURE_BLEND_META_PROVIDER
            ),
            AvEffect.AihdEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AIHD,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.AIHD_META_PROVIDER
            ),
            AvEffect.RepairEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = REPAIR,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.REPAIR_META_PROVIDER
            ),
            AvEffect.AiGraffitiEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AIGRAFFITI,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.AI_GRAFFITI_META_PROVIDER
            ),
            AvEffect.AiBestTakeEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AI_BEST_TAKE,
                version = 1L,
                inputType = InputSourceType.GPU,
                metaProviderPath = IAlgoConst.AI_BEST_TAKE_PROVIDER
            ),
            AvEffect.AiLightingEffect to formatAlgoInfoKey(
                source = OPPO,
                algoName = AI_LIGHTING,
                version = 1L,
                inputType = InputSourceType.CPU,
                metaProviderPath = IAlgoConst.AI_LIGHTING_META_PROVIDER
            )
        )
    }

    companion object {
        private val isSupportIpuBeauty: Boolean by lazy {
            ConfigAbilityWrapper.getBoolean(
                ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IPU_BEAUTY,
                false
            )
        }
    }
}