/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveVM
 ** Description: 实况页面的ViewModel
 ** Version: 1.0
 ** Date : 2024/5/20
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2024/5/20    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive

import android.annotation.SuppressLint
import android.annotation.TargetApi
import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.Gainmap
import android.graphics.Rect
import android.graphics.RectF
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import android.util.ArrayMap
import android.util.Size
import androidx.annotation.WorkerThread
import com.oplus.gallery.addon.graphics.UltraHdrInfo
import com.oplus.gallery.addon.graphics.toGainmap
import com.oplus.gallery.addon.graphics.toUltraHdrInfo
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper.getBoolean
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isUhdrPhoto
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.archv2.bus.ReplierChannel
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_HDR_TRANSFORM_DATA
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct
import com.oplus.gallery.foundation.codec.extend.toNotNullData
import com.oplus.gallery.foundation.opengl.glcontext.GLContextRender
import com.oplus.gallery.foundation.opengl2.render.renderer.helper.RenderHelper
import com.oplus.gallery.foundation.opengl2.texture.BitmapTexture
import com.oplus.gallery.foundation.opengl2.texture.GainmapTexture
import com.oplus.gallery.foundation.opengl2.texture.RawTexture
import com.oplus.gallery.foundation.opengl2.texture.TexConfig
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GTrace
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_HLG
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3_HLG
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.isHdrGamma
import com.oplus.gallery.foundation.util.ext.cropBitmap
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.foundation.util.ext.scaleBitmap
import com.oplus.gallery.foundation.util.ext.size
import com.oplus.gallery.foundation.util.ext.toConfig
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.math.MathUtil
import com.oplus.gallery.foundation.util.media.MimeTypeUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.colormanagement.IColorManagementAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM
import com.oplus.gallery.framework.abilities.editing.IEditingAbility
import com.oplus.gallery.framework.abilities.editing.asset.IAvAsset
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata
import com.oplus.gallery.framework.abilities.editing.asset.IAvAssetMetadata.ExtendedDataKey.COLOR_SPACE
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.session.IEditingSession
import com.oplus.gallery.framework.abilities.editing.sink.DefaultBitmapWithHdrSink
import com.oplus.gallery.framework.abilities.editing.sink.IAvSink
import com.oplus.gallery.framework.abilities.editing.sink.IBitmapSink
import com.oplus.gallery.framework.abilities.editing.sink.emptySink
import com.oplus.gallery.framework.abilities.editing.track.IAvTrack
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsageScope
import com.oplus.gallery.framework.abilities.watermark.file.PhoneInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.olive_decoder.utils.AppConst
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.common.Config.Loader.PREVIEW_PHOTO_MAX_LENGTH
import com.oplus.gallery.photoeditor.editingvvm.BaseAlgoBootstrapConfig
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.SessionName
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_ORIGIN_WATERMARK_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_PHONE_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_LOAD_THUMBNAIL_LIST
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_SUBMIT_COVER_TO_PIPELINE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_LEFT_HANDLE_TIMEMS_WITH_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_RIGHT_HANDLE_TIMEMS_WITH_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_FIXED_SLIDING_TIME_STAMP
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.REPLY_TOPIC_OLIVE_UPDATE_UI_CONFIG
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_EXPORT_PHOTO_DATA
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_IS_HDR_VIDEO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_UI_CONFIG
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Olive.TOPIC_OLIVE_UI_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_IMAGE_PACK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.REPLY_TOPIC_CHANGE_PREVIEW_SIZE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO
import com.oplus.gallery.photoeditor.editingvvm.UnitNotifier
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.UnitReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.colorspace.adjustColorSpace
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.OLivePhotoDataSource
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.ParametricDataSource
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.OliveVideoEdgeEditorImp.Companion.DEFAULT_DISPLAY_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.IOliveVideoEngine
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.OliveImageProcessingEngine
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.processor.proxdr.plugins.dwonload.OliveCoverProXDRModelDownloader
import com.oplus.gallery.photoeditor.editingvvm.olive.engine.request.input.RequestData
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.ISlowMotionPipeline
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.SlowMotionPipelineImpl
import com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion.SlowMotionRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.operating.ExitPageListener
import com.oplus.gallery.photoeditor.editingvvm.operating.ImageReplaceRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingAction
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingVM.Companion.ALL_OPERATING_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.operating.priority
import com.oplus.gallery.photoeditor.editingvvm.output.OriginFileProcessType
import com.oplus.gallery.photoeditor.editingvvm.output.OutputVM
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode.Companion.SAVE_FAILED_GET_FRAME_IMAGE
import com.oplus.gallery.photoeditor.editingvvm.output.ResultCode.Companion.SAVE_SUCCESS
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_COLOR_SPACE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_CONTEXT
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_EFFECT_BITMAP
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_HDR_IMAGE_CONTENT
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_OLIVE_VIDEO_ENGINE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_ORIGIN_FILE_PROCESS_TYPE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_SAVE_TYPE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveFileTaskFactory
import com.oplus.gallery.photoeditor.editingvvm.output.SaveType
import com.oplus.gallery.photoeditor.editingvvm.output.task.SaveTaskException
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.unregisterDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeR
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord.Companion.asBitmap
import com.oplus.gallery.photoeditor.editingvvm.watermark.utils.WatermarkReadUtil
import com.oplus.gallery.photoeditor.track.OLiveEditorTracker
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.UI
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageType
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.drawable.IHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.UHdrMetadataPack
import com.oplus.gallery.standard_lib.codec.drawable.isHdrContentValid
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.ui.util.ToastUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_1_MS_IN_US
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.math.roundToLong
import kotlin.collections.reversed as ktReversed

/**
 * 实况编辑页的ViewModel
 * @param editingVM 主ViewModel
 */
internal class OliveVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    /**
     * olive图片解析器
     */
    private var oliveThumbnailLoader: OLiveThumbnailBitmapLoader? = null

    /**
     * olive封面后处理引擎
     * 当前支持
     * 1.优化图片质量
     * 2.添加proXDR效果
     */
    private var oliveImageProcessingEngine: OliveImageProcessingEngine? = null

    /**
     * 用于通知 Section，做UI刷新
     */
    private var oliveUiPub: PublisherChannel<OliveUiBean>? = null

    private var shoOliveVideoNtf: UnitNotifier? = null

    /**
     * 实况照片基础信息
     */
    private var oliveUiConfigPub: PublisherChannel<IOLiveEditViewUIConfig>? = null
    private var isHdrVideoPub: PublisherChannel<Boolean>? = null
    private var exportPhotoDataPub: PublisherChannel<SaveData>? = null
    private var initiallyBean: OliveUiBean? = null
    private var bitmapWithHdrSink: IBitmapSink? = null

    /**
     * 视频播放器
     */
    private val oliveVideoEngine: IOliveVideoEngine? by lazy {
        vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
    }

    /**
     * 设置封面正在处理时，用户是否有点击完成按钮
     * 用途：封面处理完成后续接用户原操作
     * 目的：封面保存正确的同时，续接用户原操作
     * 范围：只能标识封面处理时用户是否点击了封面
     * 使用场景：
     * 1.封面处理完成后，是否需要自动走界面完成逻辑
     */
    private var isClickedDoneWhenCoverProcessing: Boolean = false

    /**
     * 标记完成按钮的Topic是否已注册
     */
    private var actionConfirmTopicRegistered = false
    private var mediaItem: MediaItem? = null
    private var lastRequestId: Long? = null
    private var slowMotionPipeline: ISlowMotionPipeline? = null

    /**
     * 移除水印管线map
     * 防止刚进入实况编辑取封面帧与滑动选帧，两个地方同时需要去水印造成相互干扰，将每个去水印的管线逻辑单独记录
     */
    private val removeWatermarkSessionMap: MutableMap<Int, Pair<IEditingSession?, DefaultBitmapWithHdrSink>> = mutableMapOf()

    /**
     * 用于接收 Section 的消息，做UI数据更新
     */
    private val oliveUiRep = object : UnitReplier<OliveUiBean>() {
        override fun onSingleReply(arg: OliveUiBean) {
            when (arg.id) {
                OliveBeanId.RECOVER -> notifyRecoverData()

                OliveBeanId.COVER -> notifyUpdateCover()

                OliveBeanId.OLIVE_ENABLE_STATUS -> notifyChangeSwitchStatus(arg.oliveEnable)

                OliveBeanId.OLIVE_SOUND_STATUS -> notifyChangeSoundStatus(arg.oliveSoundEnable)

                OliveBeanId.OLIVE_EXPORT_IMAGE -> notifyOliveExportImage()

                OliveBeanId.OLIVE_SLOW_MOTION -> notifyOliveSlowMotion()

                OliveBeanId.TOTAL_DURATION -> notifyUpdateTotalDuration(arg.totalDuration)

                else -> GLog.w(TAG, LogFlag.DL) { "[oliveUiRep] do nothing. id: ${arg.id}" }
            }
        }
    }

    private val updateUiConfigRep = object : UnitReplier<AppUiResponder.AppUiConfig>() {
        override fun onSingleReply(arg: AppUiResponder.AppUiConfig) {
            updateUiConfig(arg)
        }
    }

    private val loadThumbnailListRep = object : UnitReplier<Long>() {
        override fun onSingleReply(arg: Long) {
            notifyLoadThumbnailList(arg)
        }
    }

    private val updateSlidingTimeMsWithPercentRep = object : UnitReplierChannel {
        override fun onReply(vararg args: Any) {
            val selectPosPercent = args[0] as? Float ?: return
            val isForced = if (args.size > 1) {
                args[1] as? Boolean ?: false
            } else false
            notifyUpdateSlidingTimeMsWithPercent(selectPosPercent, isForced)
        }
    }

    /**
     * OLIVE剪辑左把手变化时更新
     */
    private val updateLeftHandleTimeMsWithPercentRep = object : UnitReplier<Float>() {
        override fun onSingleReply(arg: Float) {
            notifyUpdateLeftHandleTimeMsWithPercent(arg)
        }
    }

    /**
     * OLIVE剪辑右把手变化时更新
     */
    private val updateRightHandleTimeMsWithPercentRep = object : UnitReplier<Float>() {
        override fun onSingleReply(arg: Float) {
            notifyUpdateRightHandleTimeMsWithPercent(arg)
        }
    }

    private val submitCoverToPipelineRep = object : ReplierChannel<Unit> {
        override fun onReply(vararg args: Any): Unit? {
            if (args.size < 2) {
                GLog.w(TAG, LogFlag.DF) { "[submitCoverToPipelineRep] Returning null due to args size < 2" }

                return null
            }

            val coverInfo = args[0] as? OliveCoverInfo
            if (coverInfo == null) {
                GLog.w(TAG, LogFlag.DF) { "[submitCoverToPipelineRep] Returning null due to cover not being a bitmap" }
                return null
            }

            @Suppress("UNCHECKED_CAST")
            val onSubmit = args[1] as? (() -> Unit)
            if (onSubmit == null) {
                GLog.w(TAG, LogFlag.DF) { "[submitCoverToPipelineRep] Returning null due to onSubmit not being a function" }
                return null
            }

            submitCoverToPipeline(coverInfo.coverBitmap, coverInfo.gainMap, onSubmit)
            return null // 如果这里也需要记录日志，可以根据实际情况添加
        }
    }

    private val previewSizeRep = object : ReplierChannel<Rect?> {
        override fun onReply(vararg args: Any): Rect? {
            if (args.size < 2) return null
            val size = args[1] as? Rect ?: return null
            val previewAreaMarginBottom = vmBus?.get<IOLiveEditViewUIConfig>(TOPIC_OLIVE_UI_CONFIG)
                ?.getPreviewAreaMarginBottom(app.resources) ?: return null
            GLog.d(TAG, LogFlag.DL) { "[previewSizeRep] oldSize: $size, previewAreaMarginBottom: $previewAreaMarginBottom" }
            size.bottom -= previewAreaMarginBottom
            return size
        }
    }

    /**
     * 操作栏的完成按钮点击回调
     */
    private val confirmRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            notifyConfirm(arg)
        }
    }

    /**
     * 操作栏的取消按钮点击回调
     */
    private val cancelRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            notifyCancel(arg)
        }
    }

    /**
     * 更新slidingTime的回调
     * 当用户滑动进度条时，如果选中了p帧，会做时间戳修正，修正后的时间需要同步给外部，保证设为封面帧选中的时间戳和用户之前滑动的时间戳一致
     */
    private val updateSlidingTimeMsRep = object : UnitReplier<Long>() {
        override fun onSingleReply(arg: Long) {
            notifyUpdateSlidingTimeMs(arg)
        }
    }

    /**
     * 初始化原始封面图的任务
     */
    private var downloadOriginalBitmapDeferredJob: Deferred<Any?>? = null

    /**
     * 封面图是否在渲染中，从获取到封面图后开始，提交到管线且渲染后结束
     */
    private var isCovertRendering = false

    override fun onCreate() {
        super.onCreate()
        vmBus?.apply {
            oliveUiPub = registerDuplex(TOPIC_OLIVE_UI_STATE, oliveUiRep)
            oliveUiConfigPub = register(TOPIC_OLIVE_UI_CONFIG)
            isHdrVideoPub = register(TOPIC_OLIVE_IS_HDR_VIDEO)
            exportPhotoDataPub = register(TOPIC_OLIVE_EXPORT_PHOTO_DATA)
            register(REPLY_TOPIC_OLIVE_UPDATE_UI_CONFIG, updateUiConfigRep)
            register(REPLY_TOPIC_OLIVE_LOAD_THUMBNAIL_LIST, loadThumbnailListRep)
            register(REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT, updateSlidingTimeMsWithPercentRep)
            register(REPLY_TOPIC_OLIVE_UPDATE_LEFT_HANDLE_TIMEMS_WITH_PERCENT, updateLeftHandleTimeMsWithPercentRep)
            register(REPLY_TOPIC_OLIVE_UPDATE_RIGHT_HANDLE_TIMEMS_WITH_PERCENT, updateRightHandleTimeMsWithPercentRep)
            register(REPLY_TOPIC_OLIVE_SUBMIT_COVER_TO_PIPELINE, submitCoverToPipelineRep)
            register(REPLY_TOPIC_OLIVE_UPDATE_FIXED_SLIDING_TIME_STAMP, updateSlidingTimeMsRep)
            shoOliveVideoNtf = subscribeR(REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO)
        }
        // 触发xdr 插件更行
        OliveCoverProXDRModelDownloader.tryUpdate()
    }

    override fun onResume() {
        super.onResume()
        vmBus?.apply {
            register(REPLY_TOPIC_CHANGE_PREVIEW_SIZE, previewSizeRep)
            register(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            register(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
            actionConfirmTopicRegistered = true
        }
    }

    override fun onPause() {
        super.onPause()
        // 如果在onDestroy再解注册的话来不及，SectionA切换SectionB，会先走B的onCreate，再走A的onDestroy，所以要在onPause里面解注册
        vmBus?.apply {
            unregister(REPLY_TOPIC_CHANGE_PREVIEW_SIZE, previewSizeRep)
            unregister(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            unregister(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
            actionConfirmTopicRegistered = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseCovertBitmap()
        bitmapWithHdrSink?.also {
            editingVM.sessionProxy.removeSink(it.sinkType, it)
            bitmapWithHdrSink = null
        }
        oliveThumbnailLoader?.close()
        OliveCoverProXDRModelDownloader.destroy()
        oliveImageProcessingEngine?.release()
        vmBus?.apply {
            unregisterDuplexT(TOPIC_OLIVE_UI_STATE, oliveUiRep, oliveUiPub)
            unregisterT(TOPIC_OLIVE_UI_CONFIG, oliveUiConfigPub)
            unregisterT(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE, oliveUiConfigPub)
            unregisterT(TOPIC_OLIVE_IS_HDR_VIDEO, isHdrVideoPub)
            unregisterT(TOPIC_OLIVE_EXPORT_PHOTO_DATA, exportPhotoDataPub)

            unregister(REPLY_TOPIC_OLIVE_UPDATE_UI_CONFIG, updateUiConfigRep)
            unregister(REPLY_TOPIC_OLIVE_LOAD_THUMBNAIL_LIST, loadThumbnailListRep)
            unregister(REPLY_TOPIC_OLIVE_UPDATE_SLIDING_TIMEMS_WITH_PERCENT, updateSlidingTimeMsWithPercentRep)
            unregister(REPLY_TOPIC_OLIVE_UPDATE_LEFT_HANDLE_TIMEMS_WITH_PERCENT, updateLeftHandleTimeMsWithPercentRep)
            unregister(REPLY_TOPIC_OLIVE_UPDATE_RIGHT_HANDLE_TIMEMS_WITH_PERCENT, updateRightHandleTimeMsWithPercentRep)
            unregister(REPLY_TOPIC_OLIVE_SUBMIT_COVER_TO_PIPELINE, submitCoverToPipelineRep)
            unregister(REPLY_TOPIC_OLIVE_UPDATE_FIXED_SLIDING_TIME_STAMP, updateSlidingTimeMsRep)
            unsubscribeR(REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO, shoOliveVideoNtf)
        }
        closeAllRemoveWatermarkSession()
        initiallyBean = null
    }

    /**
     * 释放封面图，此方法会在释放bitmap时加上锁，因为此bitmap在GL线程还可能会被使用，以确保多线程安全
     */
    private fun releaseCovertBitmap() {
        val covertBitmap = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.coverBitmap ?: return
        if (covertBitmap == getOLiveOriginalCoverItem()?.bitmap) {
            return
        }
        synchronized(covertBitmap) {
            covertBitmap.recycle()
        }
    }

    private fun closeAllRemoveWatermarkSession() {
        for (id in removeWatermarkSessionMap.keys) {
            closeRemoveWatermarkSession(id)
        }
        removeWatermarkSessionMap.clear()
    }

    private fun closeRemoveWatermarkSession(removeWatermarkSessionId: Int) {
        GLog.d(TAG, LogFlag.DL, "closeRemoveWatermarkSession  removeWatermarkSessionId: $removeWatermarkSessionId")
        removeWatermarkSessionMap.remove(removeWatermarkSessionId)?.let {
            it.first?.let { session ->
                session.config.sinkConfig.removeSink(IAvSink.AvSinkType.BITMAP, it.second)
                app.getAppAbility<IEditingAbility>()?.closeSession(session.sessionId)
            }
        }
    }

    private fun notifyUpdateSlidingTimeMs(timeMS: Long) {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        bean.slidingTimeMs = timeMS
        bean.id = OliveBeanId.NONE
        oliveUiPub?.publish(bean)
        GLog.d(TAG, "notifyUpdateSlidingTimeMs timeMs = $timeMS")
    }

    /**
     * 从操作栈获取恢复数据
     */
    private fun getRecoverUIFromRecord(): OliveUiBean {
        val lastRecord =
            vmBus?.notifyOnce<OperatingRecord>(REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC, AvEffect.OliveEffect.name)
        val uiBean = lastRecord?.asOliveRecord()?.asOliveUiBean() ?: OliveUiBean()
        GLog.d(TAG, LogFlag.DL) { "[getRecoverUIFromRecord] uiBean: $uiBean, lastRecord: $lastRecord" }
        return uiBean
    }


    /**
     * 通知更新实况图的总时长ms
     */
    private fun notifyUpdateTotalDuration(duration: Long) {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        if ((bean.totalDuration == duration) || (duration == 0L)) {
            GLog.w(TAG, LogFlag.DL) { "[notifyUpdateTotalDuration] duration is not change" }
            return
        }

        bean.id = OliveBeanId.TOTAL_DURATION
        bean.totalDuration = duration
        // 如果视频时长解析出来后，裁剪框右把手位置还未赋值过，则默认使用总时长作为右边界
        if (bean.endTime < 0) bean.endTime = duration
        initiallyBean?.apply { if (endTime < 0) endTime = duration }
        oliveUiPub?.publish(bean)
    }

    /**
     * 通知更新olive定制的UiConfig
     */
    private fun updateUiConfig(appUiConfig: AppUiResponder.AppUiConfig) {
        appUiConfig.apply {
            val oliveUiConfig = vmBus?.get<IOLiveEditViewUIConfig>(TOPIC_OLIVE_UI_CONFIG)
            if (oliveUiConfig == null) {
                GLog.d(TAG, LogFlag.DL) { "[updateUiConfig] init oliveUiConfig" }
                oliveUiConfigPub?.publish(IOLiveEditViewUIConfig.getOliveUIConfig(this))
                return
            }

            // 因为onUIConfigChanged中的isChanged数据会频繁刷新，layout时记录当前布局和界面状态，如果状态相同不刷新
            if (isChanged() && isConfigChanged(oliveUiConfig.appUiConfig)) {
                GLog.d(TAG, LogFlag.DL) { "[updateUiConfig] isChanged" }
                oliveUiConfigPub?.publish(IOLiveEditViewUIConfig.getOliveUIConfig(this))
            }
        }
    }

    private fun AppUiResponder.AppUiConfig.isConfigChanged(currentUiConfig: AppUiResponder.AppUiConfig?): Boolean {
        return currentUiConfig?.let {
            (this.orientation.current != currentUiConfig.orientation.current)
                    || (this.windowWidth.current != currentUiConfig.windowWidth.current)
                    || (this.windowHeight.current != currentUiConfig.windowHeight.current)
        } ?: true
    }

    /**
     * 通知初始化数据
     */
    private fun notifyRecoverData() {
        val olivePhotoDataSource = vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[notifyRecoverData] olivePhoto is not invalid, finish self" }
            notifyFinishSelfWithToast()
            return
        }

        val oliveVideoEngine = vmBus.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[notifyRecoverData] oliveVideoEngine is not invalid." }
            notifyFinishSelfWithToast()
            return
        }

        val olivePhoto = olivePhotoDataSource.oLivePhoto
        // 优先去点击过复原按钮后的dateSource
        val oliveInfo: OLiveThumbnailBitmapLoader.OliveInfo? = vmBus.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.let {
            val imageUri = it.recoverUri ?: it.imageUri
            OLiveThumbnailBitmapLoader.OliveInfo(
                imageUri = imageUri,
                filePath = it.recoverFilePath ?: (it.filePath ?: TextUtil.EMPTY_STRING),
                offset = olivePhoto.microVideo?.offset ?: 0L,
                length = olivePhoto.microVideo?.length ?: 0L,
                coverTimeUs = olivePhoto.coverTimeInUs,
                primaryPhotoTimeInUs = olivePhoto.primaryPhotoTimeInUs,
                oliveDecode = olivePhotoDataSource.oLiveDecode,
                oliveVideoEngine = oliveVideoEngine,
                isHdrVideoOlive = oliveVideoEngine.retrieveInfo().videoColorSpace.isHdrGamma()
                        && ApiLevelUtil.isAtLeastAndroidU()
            )
        }

        this.oliveThumbnailLoader = oliveInfo?.let(::OLiveThumbnailBitmapLoader)
        isHdrVideoPub?.publish(oliveInfo?.isHdrVideoOlive == true)

        this.oliveImageProcessingEngine = oliveInfo?.let {
            OliveImageProcessingEngine(it.imageUri, it.filePath, it.isHdrVideoOlive)
        }

        val bean = getRecoverUIFromRecord().apply {
            val coverT = olivePhoto.coverTimeInUs.div(OLiveEditConstant.TIME_MS_TO_US)
            val originalT = olivePhoto.primaryPhotoTimeInUs.takeUnless {
                it == AppConst.INVALID_VALUE.toLong()
            }?.div(OLiveEditConstant.TIME_MS_TO_US) ?: coverT
            // 获取当前实况和声音开关状态
            val oliveEnableFromPhoto = olivePhoto.oliveEnable ?: true
            val oliveSoundEnableFromPhoto = olivePhoto.oliveSoundEnable ?: true
            if (startTime < 0) {
                oliveEnable = oliveEnableFromPhoto
                oliveSoundEnable = oliveSoundEnableFromPhoto
            }
            if (coverTimeMs < 0) coverTimeMs = coverT
            if (originalTimeMs < 0) originalTimeMs = originalT
            if (startTime < 0) startTime = TimeUnit.MICROSECONDS.toMillis(olivePhoto.microVideo?.videoStartUs ?: 0L)
            if (endTime < 0) {
                endTime = olivePhoto.microVideo?.videoEndUs?.let { TimeUnit.MICROSECONDS.toMillis(it) } ?: totalDuration
            }
            // 当前滑块位置，默认选中封面圆点位置
            slidingTimeMs = coverTimeMs
        }

        updateAdsorbentList(bean)
        initiallyBean = bean.copy()
        bean.id = OliveBeanId.RECOVER
        GLog.d(TAG, LogFlag.DL) { "[notifyRecoverData] olivePhoto: $olivePhoto, bean: $bean" }
        bean.hasImageCoverChanged = olivePhotoDataSource.oLiveDecode.isCoverChanged()
        bean.hasSupportRestoreOriginalExt = olivePhotoDataSource.hasSupportRestoreOriginalExt
        oliveUiPub?.publish(bean)

        initCoverBitmap { coverBitmap, hdrImageContent ->
            bean.coverBitmap = coverBitmap
            bean.coverGainMap = hdrImageContent?.grayImage
            bean.hdrMeta = hdrImageContent?.metadata?.metadata
            bean.hdrImageType = hdrImageContent?.metadata?.getHdrType()
            // 该操作比较耗时，放在子线程来调用
            bean.isLongRunningCoverProcessor = (oliveImageProcessingEngine?.isLongRunningProcessor() == true)
            launch(Dispatchers.UI) {
                bean.id = OliveBeanId.INIT_COVER
                oliveUiPub?.publish(bean)
            }
        }
        initOriginalBitmap(bean.originalTimeMs)
    }

    /**
     * 更新需要吸附的位置
     *
     * @param uiBean 待更新的UiBean
     */
    private fun updateAdsorbentList(uiBean: OliveUiBean) {
        val list = arrayListOf<Long>()
        if (uiBean.coverTimeMs >= 0) {
            list.add(uiBean.coverTimeMs)
        }
        if (uiBean.originalTimeMs >= 0) {
            list.add(uiBean.originalTimeMs)
        }
        uiBean.adsorbentList = list
    }

    /**
     * 初始化封面图:
     * 1.准备封面图，只是加载出来，并不会送到管线，预览区域仍使用当前预览图
     * 2.准备原始图: 相机默认大图
     */
    private fun initOriginalBitmap(originalCoverTimeMs: Long) {
        if (getOLiveOriginalCoverItem()?.isValid() == true) {
            GLog.d(TAG, LogFlag.DF) { "[initOriginalBitmap] cached original cover item is valid, will ignore download." }
            return
        }

        val startTime = System.currentTimeMillis()
        downloadOriginalBitmapDeferredJob = async(Dispatchers.IO) {
            val originalCoverItem = downloadOriginalBitmap(originalCoverTimeMs)
            vmBus?.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK)?.also { imagePack ->
                val oldCoverItem = imagePack.oliveOriginalCoverItem
                oldCoverItem?.release()
                imagePack.oliveOriginalCoverItem = originalCoverItem
                GLog.d(TAG, LogFlag.DL) {
                    "initOriginalBitmap downloadOriginalBitmap bitmap:${originalCoverItem.bitmap.getBitmapInfo()}," +
                            "gainMap:${originalCoverItem.gainBitmap.getBitmapInfo()}, coverTime:${originalCoverItem.timeMs}" +
                            "costTime = ${GLog.getTime(startTime)}ms, releaseOld:${oldCoverItem != null}"
                }
            }
        }
    }

    /**
     * 初始化当前封面帧，这里需要的是未经过参数化处理过的内容图
     * 如果当前图没有经过参数化操作过，那我们直接从管线里面获取到
     * 如果有经过参数化操作过，我们需要先从操作栈中将图应用的参数化效果回退回去，得到输出图之后，再将参数化效果恢复回去。
     *
     * @param coverResult
     */
    private fun initCoverBitmap(coverResult: (Bitmap?, hdrImageContent: IHdrImageContent?) -> Unit) {
        val recordList = findRecentParametricRecordExcludeOLive()
        if (recordList.isEmpty()) {
            getPreviewCoverFromPipeline(coverResult)
        } else {
            getPreviewCoverWithoutParametricEffect(recordList, coverResult)
        }
    }

    /**
     * 从管线sink中获取当前预览的图，如果有参数化操作，这里是叠加了参数化效果的图。
     *
     */
    private fun getPreviewCoverFromPipeline(coverResult: (Bitmap, hdrImageContent: IHdrImageContent?) -> Unit) {
        val startTime = System.currentTimeMillis()
        bitmapWithHdrSink = DefaultBitmapWithHdrSink(
            PREVIEW_PHOTO_MAX_LENGTH,
            PREVIEW_PHOTO_MAX_LENGTH,
            onBitmapReceive = { bitmap, hdrImageContent ->
                bitmapWithHdrSink?.also {
                    editingVM.sessionProxy.removeSink(it.sinkType, it)
                    bitmapWithHdrSink = null
                }
                GLog.d(TAG, LogFlag.DL) {
                    "[getPreviewCoverFromPipeline], bitmap wh=${bitmap.width},${bitmap.height}, cost time=${GLog.getTime(startTime)}"
                }
                coverResult.invoke(bitmap, hdrImageContent)
            })
        bitmapWithHdrSink?.let {
            editingVM.sessionProxy.addAsOnceFrameSink(IAvSink.AvSinkType.BITMAP, sink = it)
        }
    }

    /**
     * 判定当前操作栈中，是否有做过参数化保存相关的操作。
     */
    private fun findRecentParametricRecordExcludeOLive(): List<OperatingRecord> {
        val recordsAfterNonparametric = vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORDS_AFTER_NONPARAMETRIC, EMPTY_STRING
        )?.filter { it.isJustPackingBox.not() } ?: emptyList()
        return recordsAfterNonparametric.filter {
            (it.effectName != AvEffect.OliveEffect.name) && (it.effectName != AvEffect.WatermarkEffect.name)
        }
    }

    /**
     * 获取去除了参数化效果的预览内容图。
     *
     * @param coverResult
     */
    private fun getPreviewCoverWithoutParametricEffect(
        parametricRecordList: List<OperatingRecord>,
        coverResult: (Bitmap, hdrImageContent: IHdrImageContent?) -> Unit,
    ) {
        bitmapWithHdrSink = DefaultBitmapWithHdrSink(
            PREVIEW_PHOTO_MAX_LENGTH,
            PREVIEW_PHOTO_MAX_LENGTH,
            onBitmapReceive = { bitmap, hdrImageContent ->
                GLog.d(TAG, LogFlag.DL) {
                    "[getPreviewCoverWithoutParametricEffect], effectBmpSink bitmap wh=${bitmap.width},${bitmap.height}"
                }
                bitmapWithHdrSink?.also {
                    editingVM.sessionProxy.removeSink(it.sinkType, it)
                    bitmapWithHdrSink = null
                }
                coverResult.invoke(bitmap, hdrImageContent)
                val priorityRecordList = parametricRecordList.sortedBy { record -> record.priority() }.ktReversed()
                    .filter { record -> record.isJustPackingBox.not() }
                editingVM.sessionProxy.batchAddEffect(priorityRecordList, emptySink())
            })
        bitmapWithHdrSink?.let {
            editingVM.sessionProxy.batchRemoveEffect(parametricRecordList, sink = it)
        }
    }

    /**
     * 获取原图(去水印，但是如果是旧版本改过封面的图则无法去水印)
     * - 1.通过OLiveDecode判断该图片文件的封面是否更改过
     * - 2.通过扩展信息判断该文件是否添加了可还原封面的标记（当前流程【新流程】切换封面的时候会加上该标记，用以与旧版本区分）
     * - 3.获取原图
     *     - 3.1 如果当前封面就是原图所在帧（即 原图）
     *         - 3.1.1 如果封面有被动过，说明是旧版本给切换回的原封面（没有扩展信息了），走旧逻辑，复制预览图
     *         - 3.1.2 否则如果封面没有被动过，说明就是真正的原图，走新流程，从管线获取去水印之后的原图
     *     - 3.2 如果当前封面不是原图所在帧
     *         - 3.2.1 如果有支持恢复原图的扩展信息，说明是来自新版本的切过封面的图（那么原始图就是去过水印的图），OliveDecode.getOriginalStream获取去水印之后的原图
     *         - 3.2.2 如果无支持恢复原图的扩展信息，说明是旧版本切换了封面的图（已经没有原始扩展信息），走旧逻辑从视频截取对应封面帧
     */
    private suspend fun downloadOriginalBitmap(timeMs: Long): OLiveCoverItem = suspendCoroutine {
        getNoWatermarkOriginalBitmap(timeMs = timeMs) { coverTime, coverBitmap, hdrContent ->
            if (coverBitmap == null) {
                GLog.e(TAG, LogFlag.DL, "downloadOriginalBitmap result is null")
                it.resume(OLiveCoverItem(null, coverTime, null, null))
            } else {
                GLog.d(TAG, LogFlag.DL, "downloadOriginalBitmap from getNoWatermarkOriginalCoverBitmap")
                it.resume(OLiveCoverItem(coverBitmap, coverTime, hdrContent?.grayImage, hdrContent?.metadata?.metadata))
            }
        }
    }

    private fun getNoWatermarkOriginalBitmap(
        timeMs: Long,
        resultBlock: (coverTime: Long, cover: Bitmap?, hdrContent: IHdrImageContent?) -> Unit,
    ) {
        // 项目化还原的图片
        val targetSize = PREVIEW_PHOTO_MAX_LENGTH
        val originalFilePath = getOriginalFilePath()
        if (originalFilePath.isNullOrEmpty()) {
            getNoWatermarkOriginalCoverBitmap(targetSize) { coverBitmap, hdrContent ->
                resultBlock.invoke(timeMs, coverBitmap, hdrContent)
            }
        } else {
            getNoWatermarkOriginalBitmapByFilePath(originalFilePath, targetSize, resultBlock)
        }
    }

    private fun getNoWatermarkOriginalBitmapByFilePath(
        filePath: String,
        targetSize: Int,
        resultBlock: (coverTime: Long, cover: Bitmap?, hdrContent: IHdrImageContent?) -> Unit,
    ) {
        val oLiveDecode = OLiveDecode.create(filePath)
        val olivePhoto = oLiveDecode.decode()
        val coverTime = olivePhoto?.coverTimeInUs?.div(OLiveEditConstant.TIME_MS_TO_US) ?: -1L
        val originalCoverTimeMs = olivePhoto?.primaryPhotoTimeInUs.takeUnless {
            it == AppConst.INVALID_VALUE.toLong()
        }?.div(OLiveEditConstant.TIME_MS_TO_US) ?: coverTime
        getNoWatermarkOriginalCoverBitmap(oLiveDecode, true, filePath, targetSize) { coverBitmap, hdrContent ->
            resultBlock.invoke(originalCoverTimeMs, coverBitmap, hdrContent)
        }
    }

    /**
     * 背景：
     * 1.olive1.0 相机的原始大图（UIBean里的originalBitmap）封面业务使用的为olive视频的抽帧图(无水印)，这个不是原始大图
     * 2.1.0版本封面编辑，olive设置回相机原始大图封面时无法达到相机默认图效果 -- 产品效果表现上无法满足
     * 3.olive1.0 编辑替换了原始封面后，会将相机原始大图封面数据存入图片文件中（有水印的位图信息） --- olive1.0的方案
     * 4.olive2.0 算法需要取相机原始大图作为输入图做算法优化，需要取原始大图
     * 5.编辑二级界面设置回相机大图原始封面时需要不带水印的位图，回到一级界面再根据文件里水印信息加上，否则会导致编辑水印功能无法去除水印
     * 6.因编辑水印功能的存在：会存在原始图是悬浮水印，新封面图被设置成了画框水印 两张图水印不一致问题 最终文件读取表现的为画框水印
     * 7.水印分为：水印在图片内容上的悬浮水印：如相机的普通水印；水印在图片内容外的画框水印：如相机的哈苏水印
     *   其中当前的所有画框水印，替换封面前后信息及位置（目前不支持画框水印的方位替换）一致，删除其水印的本质是直接裁减掉对应区域
     *
     * 方案：
     * 1.解决背景里的2与4：将1中的原始大图替换为从文件里读取出3存储的默认大图 -- 背景里的5无法满足
     * 2.背景5，6：需要全链路打通 sdk需要修改支持水印备份及读取功能 -- 可能存在三方兼容个问题
     *  A.原始大图的水印信息需要和图片信息一样，一对一备份
     *  B.备份的原始图全部去除封面，并在文件上打上无水印原始图的相关标识
     * 3.兼容：历史遗留图及其他情况 使用算法跑过的抽帧图
     *
     * 当前实现-优先保证功能：
     * 1.特定情况下使用方案1中的文件解出的大图
     * 2.其他使用走方案3抽帧优化图
     *
     * 遗留问题2，待坤的后续需求接入
     */
    @Suppress("LongMethod")
    @Synchronized
    @WorkerThread
    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun getNoWatermarkOriginalCoverBitmap(
        targetSize: Int,
        resultBlock: (cover: Bitmap?, hdrContent: IHdrImageContent?) -> Unit,
    ) {
        val oliveDecode = vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)?.oLiveDecode ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getNoWatermarkOriginalCoverBitmap] olivePhotoDataSource is null" }
            resultBlock.invoke(null, null)
            return
        }
        return getNoWatermarkOriginalCoverBitmap(oliveDecode, false, null, targetSize, resultBlock)
    }

    @Suppress("LongMethod")
    @Synchronized
    @WorkerThread
    @TargetApi(Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    private fun getNoWatermarkOriginalCoverBitmap(
        oLiveDecode: OLiveDecode,
        isOriginalOlive: Boolean,
        originalFilePath: String?,
        targetSize: Int,
        resultBlock: (cover: Bitmap?, hdrContent: IHdrImageContent?) -> Unit,
    ) {
        val startTime = System.currentTimeMillis()
        val loader = oliveThumbnailLoader ?: run {
            resultBlock.invoke(null, null)
            GLog.e(TAG, LogFlag.DL, "[getNoWatermarkOriginalCoverBitmap] loader is null")
            return
        }

        val originalCoverBitmap = loader.getOriginalCoverBitmap(targetSize, oLiveDecode) ?: let {
            resultBlock.invoke(null, null)
            GLog.e(TAG, LogFlag.DL) { "[getNoWatermarkOriginalCoverBitmap] getOriginalCoverBitmap is null" }
            return
        }

        /**
         * 从原图中将GainMap获取出来，并与 originalCoverBitmap 组合到一起。
         */
        GLog.d(TAG) { "getNoWatermarkOriginalCoverBitmap start gainmap." }
        val gainMapInfo = oliveThumbnailLoader?.getOriginalGainMapInfo(oLiveDecode)
        val originalGainmap = gainMapInfo?.second?.toGainmap(gainMapInfo.first)
        originalCoverBitmap.gainmap = originalGainmap

        val isCoverChanged = oLiveDecode.isCoverChanged()
        // 通过扩展信息判断该文件是否添加了可还原封面的标记（当前流程【新流程】切换封面的时候会加上该标记，用以与旧版本区分）
        val hasNoWatermarkOriginalBitmapFlag =
            vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)?.hasSupportRestoreOriginalExt ?: true
        val watermarkBean =
            createOriginalCoverWatermarkBean(isOriginalOlive, originalFilePath, Size(originalCoverBitmap.width, originalCoverBitmap.height))

        val watermarkInfo = watermarkBean?.first ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getNoWatermarkOriginalCoverBitmap] can not get watermarkInfo,return" }
            resultBlock.invoke(null, null)
            return
        }

        val hasWatermark = watermarkInfo.hasWatermark()

        GLog.d(TAG, LogFlag.DL) {
            "[getNoWatermarkOriginalCoverBitmap] " +
                    "hasNoWatermarkOriginalBitmapFlag = $hasNoWatermarkOriginalBitmapFlag, " +
                    "hasWatermark = $hasWatermark, " +
                    "isCoverChanged = $isCoverChanged, " +
                    "costTime = ${System.currentTimeMillis() - startTime}ms"
        }

        when {
            /**
             * 使用原图
             * 1.无水印时
             * 2.存储的图是无水印的
             *
             * 返回：解码出的原始大图
             */
            hasWatermark.not() -> {
                resultBlock.invoke(
                    originalCoverBitmap,
                    gainMapInfo?.run {
                        HdrImageDrawable(first, UHdrMetadataPack(second))
                    }
                )
            }

            /**
             * 使用原图删除水印后得到无水印图
             * 未换过封面的水印图
             *
             * 返回：删除水印后的原始图片,
             * 注意：
             * 1.删除流程失败时返回null,
             * 2.如果返回的不为null且带水印就需要排查管线的删除流程
             */
            isCoverChanged.not() -> {
                removeWatermark(originalCoverBitmap, watermarkBean) { coverBitmap, hdrContent ->
                    originalGainmap?.gainmapContents?.recycle()
                    originalCoverBitmap.recycle()
                    resultBlock.invoke(coverBitmap, hdrContent)
                }
            }

            (hasNoWatermarkOriginalBitmapFlag) -> {
                if (originalGainmap == null) {
                    resultBlock.invoke(originalCoverBitmap, null)
                    return
                }

                /**
                 * 有[hasNoWatermarkOriginalBitmapFlag],其保存的原始封面图无水印，但是gainmap是有水印的。如果要去掉gainmap的水印
                 * 可以用带有水印的当前封面图来做。
                 */
                val coverBitmap = loader.getCoverBitmap(oLiveDecode, targetSize) ?: let {
                    GLog.e(TAG) { "[getNoWatermarkOriginalCoverBitmap] failed for coverBitmap is null." }
                    resultBlock.invoke(null, null)
                    return
                }
                coverBitmap.gainmap = originalGainmap
                removeWatermark(coverBitmap, watermarkBean) { resultBitmap, hdrContent ->
                    coverBitmap.recycle()
                    resultBitmap?.recycle()
                    resultBlock.invoke(originalCoverBitmap, hdrContent)
                }
            }

            else -> {
                /**
                 * 使用抽帧图作为原始封面
                 * 1.换过封面 + 是悬浮水印 + 是olive1.0带水印版本
                 * 2.换过封面 + 非原始封面添加了水印 （原始封面没水印） -- 优先保证功能正确吧
                 * 3.换过封面 + 原始大图是悬浮水印，新图是画框水印 -- 画框本来是可以删除的 但是这个场景的存在也只能抽帧了
                 * 3.其他情况
                 * 返回：增强/放大后的抽帧图 --质量不如原图
                 */
                val oliveUiBean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: let {
                    GLog.e(TAG, LogFlag.DL) { "[getNoWatermarkOriginalCoverBitmap] oliveUiBean is null" }
                    resultBlock.invoke(null, null)
                    return
                }
                val inputVideoFrame = loader.getFrameBitmapByTimeFromOliveVideo(
                    getOLiveMediaItem(loader.oliveInfo.imageUri),
                    timeUs = oliveUiBean.originalTimeMs * TIME_1_MS_IN_US,
                    targetSize = PREVIEW_PHOTO_MAX_LENGTH
                ) ?: let {
                    GLog.e(TAG, LogFlag.DL) { "[getNoWatermarkOriginalCoverBitmap] getOriginalCoverBitmap is null" }
                    resultBlock.invoke(null, null)
                    return
                }

                val coverProcessingEngine = oliveImageProcessingEngine ?: run {
                    // 返回抽帧图吧，起码有图看，要不然黑屏
                    resultBlock.invoke(inputVideoFrame, null)
                    GLog.e(TAG, LogFlag.DL, "[getNoWatermarkOriginalCoverBitmap] downloadPreviewBitmap engine is null")
                    return
                }

                /**
                 * MarkedBy 檀路遥：@张文明 抽帧图作为原图的文件，再也无法复现往日uhdr的荣光么
                 */
                val originGainMap = null
                val originUHdrMetadataPack = null

                /**
                 * 对图片进行质量，效果优化
                 */
                val requestData = RequestData(
                    id = oliveUiBean.originalTimeMs,
                    originalInfo = RequestData.OriginalInfo(
                        originalBitmap = originalCoverBitmap,
                        originGainMap = originGainMap,
                        originUHdrMetadataPack = originUHdrMetadataPack
                    ),
                    inputBitmap = inputVideoFrame,
                    imageUri = coverProcessingEngine.imageUri,
                    isUhdrPhoto = getOLiveMediaItem(coverProcessingEngine.imageUri)?.isUhdrPhoto() ?: false,
                    callBack = { requestId, outputData ->
                        val resultBitmap = outputData.contentBitmap ?: inputVideoFrame
                        resultBlock.invoke(resultBitmap, null)
                        if (resultBitmap != inputVideoFrame) {
                            inputVideoFrame.recycle()
                        }
                    }
                )
                requestData.createRequest(requestData, showRequestLongLoading)
                    .let(coverProcessingEngine::requestProcess)
            }
        }
    }

    private fun createRemoveWatermarkSession(): IEditingSession? {
        val removeWatermarkSession = app.getAppAbility<IEditingAbility>()?.use { ability ->
            ability.createSession(
                algoBootstrapConfig = BaseAlgoBootstrapConfig(),
                sessionEditingState = {
                    GLog.d(TAG, LogFlag.DL) { "[createRemoveWatermarkSession] sessionEditingState = $it" }
                }
            ) {
                sessionName = SessionName.OLIVE_REMOVE_WATERMARK
                isForeground = true
                pipelineEditMode = editingVM.pipelineEditMode
                longPhotoMinRatio = Config.Loader.LOADER_PHOTO_MAX_RATIO
                photoMaxLength = Config.Render.getMaxTextureSize()
            }
        }
        return removeWatermarkSession
    }

    @WorkerThread
    @Synchronized
    private fun removeWatermark(
        watermarkBitmap: Bitmap,
        watermarkBean: Pair<WatermarkInfo, Bitmap?>,
        resultBlock: (cover: Bitmap?, hdrContent: IHdrImageContent?) -> Unit,
    ) {
        val removeWatermarkSessionId = resultBlock.hashCode()
        GLog.d(TAG, LogFlag.DL) { "[removeWatermark] removeWatermarkSessionId=$removeWatermarkSessionId" }

        val phoneInfo = vmBus?.get<PhoneInfo>(TopicID.InputArguments.TOPIC_PHONE_INFO) ?: PhoneInfo()
        val record = WatermarkRecord.createRemoveWatermarkRecord(watermarkBean.first.copy(), phoneInfo, watermarkBean.second)
        val removeWatermarkSession = createRemoveWatermarkSession()
        val effectUsageScope: IAvEffectUsageScope.() -> Unit = {
            record.split().forEach { opRecord ->
                removeWatermarkSession
                    ?.effectManager
                    ?.supportedVideoEffects()
                    ?.find { it.effect.name == opRecord.effectName }
                    ?.supportedArguments()
                    .let { effectArguments ->
                        opRecord.argumentsForAlgo(effectArguments).forEach { (key, value) ->
                            setArg(key, value)
                        }
                    }
            }
        }

        val editor = removeWatermarkSession?.editor ?: return
        val track = editor.addTrack("removeWatermark", IAvTrack.AvTrackType.VIDEO)
        val asset = editor.addAsset(watermarkBitmap)
        if (ApiLevelUtil.isAtLeastAndroidU() && (watermarkBitmap.gainmap != null)) {
            val uhdrInfo = Pair(watermarkBitmap.gainmap!!.gainmapContents, watermarkBitmap.gainmap!!.toUltraHdrInfo())
            asset.metadataAccessor.setMetadata(
                IAvAssetMetadata.MetadataType.EXTENDED,
                IAvAssetMetadata.ExtendedDataKey.ULTRA_HDR_INFO,
                uhdrInfo
            )
        }

        val removeWatermarkBitmapSink = DefaultBitmapWithHdrSink(PREVIEW_PHOTO_MAX_LENGTH, PREVIEW_PHOTO_MAX_LENGTH) { bitmap, hdrImageContent ->
            resultBlock.invoke(bitmap, hdrImageContent)
            closeRemoveWatermarkSession(removeWatermarkSessionId)
        }

        editor.withRenderingOnce(removeWatermarkBitmapSink.sinkDescriptionKey) {
            val clip = editor.addClip(track, asset)
            val effect = AvEffect.getEffect(record.effectName) ?: return@withRenderingOnce
            editor.addEffect(clip, effect, effectUsageScope = effectUsageScope, render = true)
            removeWatermarkSession.config.sinkConfig.addAsDefaultSink(IAvSink.AvSinkType.BITMAP, removeWatermarkBitmapSink)
        }

        removeWatermarkSessionMap[removeWatermarkSessionId] = Pair(removeWatermarkSession, removeWatermarkBitmapSink)
    }

    private fun getParametricWatermarkBean(filePath: String, bitmapSize: Size): Pair<WatermarkInfo, Bitmap?>? {
        val editingAbility = app.getAppAbility<IEditingAbility>() ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getParametricWatermarkBean], returns null for editingAbility is null." }
            return null
        }
        val session = editingAbility.createSession(
            algoBootstrapConfig = BaseAlgoBootstrapConfig()
        ) {
            sessionName = SessionName.OLIVE_GET_ORIGIN_WATERMARK_DATA
            isForeground = true
            longPhotoMinRatio = Config.Loader.LOADER_PHOTO_MAX_RATIO
            photoMaxLength = Config.Render.getMaxTextureSize()
        }

        val uri = File(filePath).toUri()
        val asset = session.editor.addAsset(uri, filePath, IAvAsset.AvAssetType.VIDEO)
        val watermarkBean = WatermarkReadUtil(app).getWatermarkDatas(asset.metadataAccessor, 0, bitmapSize)
        editingAbility.closeSession(session.sessionId)

        return watermarkBean
    }

    /**
     * 通知加载缩图列表数据
     *
     * 场景：底部SeekBar(OliveTrimView) 进度条的背景图
     *
     * @param count 可显示的缩图数量
     */
    private fun notifyLoadThumbnailList(count: Long) {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        val duration = bean.totalDuration.takeIf {
            it > 0
        } ?: return GLog.w(TAG, LogFlag.DL) { "[notifyLoadThumbnailList] total duration is invalid" }
        val loader = oliveThumbnailLoader ?: return
        launch(Dispatchers.Main) {
            val mediaItem = withContext(Dispatchers.IO) { getOLiveMediaItem(loader.oliveInfo.imageUri) } ?: return@launch
            val timeList = MathUtil.equalDivisionNumber(duration, count)
            val oliveThumbnailList = createTempOLiveThumbnailList(timeList)
            val operatingRecords =
                vmBus?.notifyOnce<List<OperatingRecord>>(REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, ALL_OPERATING_RECORDS)
                    ?.filter { it.isJustPackingBox.not() } ?: emptyList()
            // 由于美摄在低端机型获取缩略图较慢，这里先发送空的缩略图列表，让缩略图轴先显示
            oliveUiPub?.publish(bean.apply {
                id = OliveBeanId.THUMBNAIL_LIST
                thumbnailList = convertThumbnailDataList(oliveThumbnailList)
            })

            oliveThumbnailList.forEachIndexed { index, oliveThumbnailItem ->
                val thumbnailBitmapPair = loader.getThumbnailBitmap(mediaItem, oliveThumbnailItem.timeMs, operatingRecords)
                oliveThumbnailItem.bitmap = thumbnailBitmapPair?.second
                /**
                 * 如果缩图是视频中加载出来，为了加快显示，再每次加载到缩图时都通知UI更新，否则如果都是用的缓存就等到最后统一一次通知UI更新，
                 * 防止短时间大量的更新时间冲击UI更新的监听导致缩图轴的图片无法显示
                 */
                thumbnailBitmapPair?.first.takeIf { it == OLiveThumbnailBitmapLoader.THUMBNAIL_BITMAP_FROM_ORIGINAL }?.also {
                    oliveUiPub?.publish(bean.apply {
                        id = OliveBeanId.THUMBNAIL_LIST
                        thumbnailList = convertThumbnailDataList(oliveThumbnailList)
                    })
                }
            }
            /**
             * 最后通知一次更新，使得通过缓存获取的图片更新到缩图轴显示
             */
            oliveUiPub?.publish(bean.apply {
                id = OliveBeanId.THUMBNAIL_LIST
                thumbnailList = convertThumbnailDataList(oliveThumbnailList)
            })
        }
    }

    private fun getOLiveMediaItem(uri: Uri): MediaItem? {
        if (mediaItem != null) {
            return mediaItem
        }
        mediaItem = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
        if (mediaItem != null) {
            return mediaItem
        }
        mediaItem = DataManager.findPathByUri(uri, MimeTypeUtils.MIME_TYPE_IMAGE_ANY)?.let { mediaItemPath ->
            DataManager.getMediaObject(mediaItemPath) as? MediaItem
        }
        return mediaItem
    }

    /**
     * 创建一个临时的，无图的，缩图列表对象
     */
    private fun createTempOLiveThumbnailList(timeList: List<Long>): List<OLiveThumbnailItem> {
        return timeList.map { OLiveThumbnailItem(null, it) }
    }

    /**
     * 临时列表进行转换，返回缩图列表
     */
    private fun convertThumbnailDataList(
        thumbnailList: List<OLiveThumbnailItem>,
    ): List<OLiveThumbnailItem> {
        // 加载一个空
        var lastBitmap: Bitmap? = null
        return thumbnailList.map { thumbnail ->
            // 后续的如果遇到空的，使用前一个图
            thumbnail.bitmap?.also {
                lastBitmap = it
            }

            // 优先本身 -> 上一张图 -> 封面图
            val bitmap = thumbnail.bitmap ?: lastBitmap ?: vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.coverBitmap?.let {
                if (it.isRecycled) null else it.copy(it.getConfigSafely(), it.isMutable)
            }
            return@map OLiveThumbnailItem(bitmap, thumbnail.timeMs)
        }
    }

    /**
     * 通知根据传入的百分比，更新滚动时间戳
     *
     * @param selectPosPercent 选帧框位置时间戳的百分比
     * @param isForced 是否强制更新。为true时，即使等情况，也会执行一次通知
     */
    private fun notifyUpdateSlidingTimeMsWithPercent(selectPosPercent: Float, isForced: Boolean): Boolean {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        val seekToTimeMs = bean.totalDuration.takeIf { it > 0 }?.times(selectPosPercent)?.roundToLong()
        if ((seekToTimeMs == null) || (isForced.not() && (bean.slidingTimeMs == seekToTimeMs))) {
            return false
        }

        oliveUiPub?.publish(bean.apply {
            id = OliveBeanId.SLIDING
            slidingTimeMs = seekToTimeMs
        })
        return true
    }

    private fun notifyUpdateLeftHandleTimeMsWithPercent(selectPosPercent: Float): Boolean {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        val leftHandleTime = bean.totalDuration.takeIf { it > 0 }?.times(selectPosPercent)?.roundToLong() ?: return false
        GLog.d(TAG, LogFlag.DL) { "[notifyUpdateLeftHandleTimeMsWithPercent] leftTime=$leftHandleTime, selectPosPercent=$selectPosPercent" }
        bean.id = OliveBeanId.START_TIME
        bean.startTime = leftHandleTime
        if (bean.endTime == OLiveEditConstant.INVALID_TIME_MS) {
            bean.endTime = bean.totalDuration
        }
        oliveUiPub?.publish(bean)
        return true
    }

    private fun notifyUpdateRightHandleTimeMsWithPercent(selectPosPercent: Float): Boolean {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: OliveUiBean()
        val rightHandleTime = bean.totalDuration.takeIf { it > 0 }?.times(selectPosPercent)?.roundToLong() ?: return false
        GLog.d(
            TAG,
            LogFlag.DL
        ) { "[notifyUpdateRightHandleTimeMsWithPercent] rightTime=$rightHandleTime selectPosPercent=$selectPosPercent" }

        bean.id = OliveBeanId.END_TIME
        bean.endTime = rightHandleTime
        if (bean.startTime == OLiveEditConstant.INVALID_TIME_MS) {
            bean.startTime = 0L
        }
        oliveUiPub?.publish(bean)
        return true
    }

    /**
     * 通知更新封面
     * 场景：点击设置封面后
     * 注意：这里传入的封面图 来源于sf的缓存buffer，其图片质量依赖播放器解码输出帧图的大小
     */
    private fun notifyUpdateCover() {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            GLog.d(TAG, LogFlag.DL) { "[notifyUpdateCover], OliveUiBean = $bean" }

            // 滑动时间和封面时间一致，不需要更新封面
            if (bean.coverTimeMs == bean.slidingTimeMs) {
                vmBus.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_IS_TOUCHABLE, true)
                GLog.d(TAG, LogFlag.DL) { "[notifyUpdateCover] slidingTime equal coverTime, no need to update cover" }
                return
            }

            /**
             * MarkedBy TanLuYao ：这里原始逻辑，存在数据一致性问题，目前引用太多，改动风险大 待有缘人优化
             * OliveUiBean里存了coverTimeMs与coverBitmap两个强关联需要一一对应的数据，但是这里先更新coverTimeMs，实际的bitmap并没更新；
             * 界面显示及更新是否显示封面图依赖的却是coverTimeMs，目前section里显示封面图相关逻辑复杂，界面跳变bug根因与此处存在直接关系
             */
            bean.coverTimeMs = bean.slidingTimeMs
            updateAdsorbentList(bean)
            bean.id = OliveBeanId.ADSORBENT_LIST
            oliveUiPub?.publish(bean)

            // 触发设置封面Loading动画
            bean.isCoverProcessing = true
            triggerLoadingDialogVisibilityUpdateIfNeed()
            // 处理封面图之前，先校验插件是否都准备好
            launch(Dispatchers.IO) {
                processingOptimizeBitmap(bean.coverTimeMs, bean) { result, gainmap, hdrMeta, _ ->
                    <EMAIL>(Dispatchers.Main) {
                        doUpdateCover(bean, result, gainmap, hdrMeta)
                    }
                }
            }
        }
    }

    private fun doUpdateCover(bean: OliveUiBean, newCover: Bitmap?, newGainmap: Bitmap?, hdrMeta: Any?) {
        // 切换到主线程进行旧的bitmap 回收，避免绘制时出现异常
        val oldBitmap = bean.coverBitmap
        bean.coverBitmap = newCover
        // 标志设为封面活动结束，isCoverProcessing状态重置
        bean.isCoverProcessing = false
        if ((newGainmap != null) && newGainmap.isRecycled.not()) {
            bean.coverGainMap = newGainmap
        } else {
            GLog.w(TAG, LogFlag.DL) { "[doUpdateCover] newGainmap is ${newGainmap?.isRecycled}" }
            bean.coverGainMap = null
        }
        // 设为封面结束，关闭loading动画
        triggerLoadingDialogVisibilityUpdateIfNeed()
        bean.id = OliveBeanId.COVER
        bean.hdrMeta = hdrMeta
        oliveUiPub?.publish(bean)
        val originalCoverItem = getOLiveOriginalCoverItem()
        if ((oldBitmap != newCover) && (oldBitmap != originalCoverItem?.bitmap)) {
            oldBitmap?.recycle()
        }
        vmBus?.notifyOnce(TopicID.Preview.TOPIC_PREVIEW_IS_TOUCHABLE, true)
    }

    /**
     * 通知实况开关状态变更
     * 场景：点击实况开关后
     */
    private fun notifyChangeSwitchStatus(onChangeEnable: Boolean) {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            bean.id = OliveBeanId.OLIVE_ENABLE_STATUS
            bean.oliveEnable = onChangeEnable
            GLog.d(TAG, LogFlag.DL) { "[notifyChangeSwitchStatus] oliveEnable = ${bean.oliveEnable}" }
            oliveUiPub?.publish(bean)
        }
    }

    /**
     * 通知声音开关状态变更
     * 场景：点击声音开关后
     */
    private fun notifyChangeSoundStatus(onChangeEnable: Boolean) {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            bean.id = OliveBeanId.OLIVE_SOUND_STATUS
            bean.oliveSoundEnable = onChangeEnable
            GLog.d(TAG, LogFlag.DL) { "[notifyChangeSoundStatus] oliveSoundEnable = ${bean.oliveSoundEnable}" }
            oliveUiPub?.publish(bean)
        }
    }

    private fun getOriginalFilePath(): String? {
        val parametricDataSource = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)
        if (parametricDataSource?.isSupportParametric == false) {
            return null
        }

        val originalFilePath = parametricDataSource?.pathEntity?.getOriginalFilePath()
        if (TextUtils.isEmpty(originalFilePath)) {
            return null
        }
        if (File(originalFilePath).exists().not()) {
            return null
        }
        return originalFilePath
    }

    private fun getInputBitmap(timeMs: Long, resultCallback: (Bitmap?) -> Unit) {
        val loader = oliveThumbnailLoader ?: let {
            GLog.e(TAG, LogFlag.DL) { "[getInputBitmap], null for thumbnail loader is null" }
            resultCallback.invoke(null)
            return
        }

        val retrieverBitmap = loader.getFrameBitmapByTimeFromOliveVideo(
            getOLiveMediaItem(loader.oliveInfo.imageUri), timeMs * TIME_1_MS_IN_US, PREVIEW_PHOTO_MAX_LENGTH,
        )
        val videoDisplayRectF = getOriginVideoDisplayRectF(retrieverBitmap?.size())
        val record = if (isOriginFileHasWatermark()) {
            createOriginFileWatermarkRecord(videoDisplayRectF)
        } else {
            createRemoveWatermarkRecord(videoDisplayRectF)
        }

        var resultBitmap = retrieverBitmap
        if ((resultBitmap != null) && (videoDisplayRectF != null) && (record?.watermarkInfo != null)) {
            resultBitmap = convertToUhdrIfNeed(retrieverBitmap)

            if (resultBitmap != null) {
                removeWatermark(resultBitmap, Pair(record.watermarkInfo, record.capture.asBitmap())) { result, hdrContent ->
                    // 如果图片是uhdr的，则需要合成1010102的图进行后续处理
                    val gainmap = hdrContent?.let {
                        (it.metadata as UHdrMetadataPack).metadata.toGainmap(it.grayImage)
                    }
                    resultBitmap = if (gainmap != null) combineBitmap(result, gainmap) else result

                    resultCallback.invoke(resultBitmap?.let(::cropBitmapFuzzyEdgeIfNeed))
                }
            } else {
                GLog.w(TAG, LogFlag.DL) { "getInputBitmap: resultBitmap is null." }
                resultCallback.invoke(null)
            }
        } else {
            resultCallback.invoke(resultBitmap?.let(::cropBitmapFuzzyEdgeIfNeed))
        }
    }

    /**
     * 裁减fov效果的模糊边缘
     * @param inputBitmap 原始图
     * @return 返回无模糊边缘效果的bitmap
     */
    private fun cropBitmapFuzzyEdgeIfNeed(inputBitmap: Bitmap): Bitmap {
        // 如果视频有裁剪，需要对抽帧图片也要做裁剪
        val videoDisplayScale = oliveVideoEngine?.getVideoEdgeEditor()?.scalePercent?.takeIf {
            it.compareTo(DEFAULT_DISPLAY_PERCENT) > 0
        } ?: return inputBitmap.also {
            GLog.d(TAG, LogFlag.DL) { "[cropFovFuzzyEdgeIfNeed] displayPercent is illegal. return inputBitmap bitmap" }
        }

        /**
         * 直接裁剪会导致新图比例和原图不能完全一致，这里先对原图进行放大，然后裁切原图大小的图保存前后大小比例完全一致
         * 避免由于加画框水印后出现的四周黑线
         */
        return inputBitmap.scaleBitmap(videoDisplayScale, true)?.let {
            //基于中心点向外裁切原始图片的宽高大小
            val fovRect = Rect().apply {
                left = ((it.width - inputBitmap.width) / AppConstants.Number.NUMBER_2).coerceAtLeast(0)
                top = ((it.height - inputBitmap.height) / AppConstants.Number.NUMBER_2).coerceAtLeast(0)
                right = left + inputBitmap.width
                bottom = top + inputBitmap.height
            }

            //裁剪
            it.cropBitmap(
                x = fovRect.left.coerceAtLeast(0),
                y = fovRect.top.coerceAtLeast(0),
                outWidth = fovRect.width(),
                outHeight = fovRect.height(),
                canRecycleSource = true
            )
        } ?: inputBitmap.also {
            GLog.d(TAG, LogFlag.DL) { "[cropBitmapFuzzyEdgeIfNeed] cropBitmap err. return inputBitmap bitmap" }
        }
    }

    /**
     * 判断如果图片是1010102的hdr图，则转换成uhdr
     *
     * @param inputBitmap 传入的图片
     *
     * @return 当输入图片是1010102的，则需要执行下变换成uhdr，反之则直接返回原来的图片
     */
    private fun convertToUhdrIfNeed(inputBitmap: Bitmap?): Bitmap? {
        if (ApiLevelUtil.isAtLeastAndroidU() && (inputBitmap?.config == Bitmap.Config.RGBA_1010102)) {
            // 由于美摄从HDR视频中抽取的帧是BT2020的色域，但是CPU下变换算法需要的只能是P3-HLG色域的，故此处需要转换色域
            val p3HlgBitmap = convertP3Hlg(inputBitmap)

            if (p3HlgBitmap != null) {
                // 获取下变换参数
                val hdrTransformData = (editingVM.sessionProxy.getMetadata(
                    IAvAssetMetadata.MetadataType.EXTENDED,
                    EXTEND_KEY_HDR_TRANSFORM_DATA
                ) as? ByteArray)?.let { HdrTransformDataStruct(it) }.toNotNullData()
                // olive视频抽出来的帧，在做下变换的时候不需要使用点灯，视频和封面不同，点灯主要针对优化olive封面过曝
                val processorData = hdrTransformData.copy(lightUpEnable = false)
                return RenderHelper.transformToUhdr(processorData, p3HlgBitmap)
            } else {
                GLog.e(TAG, LogFlag.DL) { "convertToUhdrIfNeed: p3HlgBitmap is null." }
                return null
            }
        } else {
            return inputBitmap
        }
    }

    /**
     * 将图片转换成p3-hlg的图片
     *
     * @param bitmap 输入的图片
     *
     * @return 返回转换成的p3-hlg的图片
     */
    private fun convertP3Hlg(bitmap: Bitmap): Bitmap? = GTrace.trace("$TAG.convertToUhdrIfNeed.p3HlgBitmap") {
        GLContextRender.runOnGLContext {
            val renderArgs = RenderHelper.obtainArgs()
            val inputTexture = BitmapTexture(bitmap).apply {
                load()
            }
            val gamutTexture = RenderHelper.transformColorSpace(renderArgs, inputTexture, DISPLAY_P3_HLG!!)
            gamutTexture.toBitmap().apply {
                RenderHelper.release(renderArgs)
            }
        }
    }

    /**
     * 合成1010102的hdr bitmap
     *
     * @param inputBitmap 输出的sdr图
     * @param gainmap 增益
     *
     * @return 合成的1010102的uhdr bitmap
     */
    @SuppressLint("NewApi")
    private fun combineBitmap(inputBitmap: Bitmap?, gainmap: Gainmap?): Bitmap? {
        if ((inputBitmap != null) && (gainmap != null)) {
            return GLContextRender.runOnGLContext {
                val renderArgs = RenderHelper.obtainArgs()
                val combineHdr = RenderHelper.combineHdr(
                    renderArgs,
                    BitmapTexture(inputBitmap),
                    GainmapTexture(gainmap),
                    gainmap.displayRatioForFullHdr
                )
                val transformOutTexture = RawTexture(combineHdr.width, combineHdr.height, BT2020_HLG!!, TexConfig.RGBA_1010102)
                RenderHelper.transformColorSpace(renderArgs, combineHdr, transformOutTexture.displayColorSpace, transformOutTexture)
                transformOutTexture.toBitmap().apply {
                    RenderHelper.release(renderArgs)
                }
            }
        }
        return null
    }

    private fun createOriginalCoverWatermarkBean(
        isOriginalOlive: Boolean,
        originalFilePath: String?,
        bitmapSize: Size,
    ): Pair<WatermarkInfo, Bitmap?>? {
        return if (isOriginalOlive) {
            if (originalFilePath.isNullOrEmpty()) {
                GLog.e(TAG, LogFlag.DL) { "[createOriginalCoverWatermarkBean] isOriginalOlive is true,but originalFilePath is null" }
                return null
            } else {
                getParametricWatermarkBean(originalFilePath, bitmapSize)
            }
        } else {
            val record = createRemoveWatermarkRecord()
            val watermarkInfo = record?.watermarkInfo ?: let {
                GLog.e(TAG, LogFlag.DL) { "[createOriginalCoverWatermarkBean] imagePack is null" }
                return null
            }
            Pair(watermarkInfo, record.capture.asBitmap())
        }
    }

    /**
     * 创建移除水印record
     * @param imageDisplayRectF ai水印大师的类型时图片在水印中的位置，主要给视频帧去水印用。因为视频帧的位置是videoDisplayRectF，这时抽帧后需要用图片去水印逻辑。对应参数也要替换
     */
    private fun createRemoveWatermarkRecord(imageDisplayRectF: RectF? = null): WatermarkRecord? {
        val imagePack = vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[removeWatermark] imagePack is null, return null" }
            return null
        }
        var watermarkInfo = vmBus.get<WatermarkInfo>(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT)
        imageDisplayRectF?.takeIf { it.isEmpty.not() }?.let {
            watermarkInfo =
                watermarkInfo?.copy(aiWatermarkMasterParams = watermarkInfo?.aiWatermarkMasterParams?.copy(imageDisplayRectF = imageDisplayRectF))
        }

        val phoneInfo = vmBus.get<PhoneInfo>(TOPIC_PHONE_INFO) ?: PhoneInfo()
        return WatermarkRecord.createRemoveWatermarkRecord(watermarkInfo, phoneInfo, imagePack.captureBitmap)
    }

    private fun isOriginFileHasWatermark(): Boolean {
        val watermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_ORIGIN_WATERMARK_INFO)
        return (watermarkInfo?.isAiMasterWatermark() == true)
    }

    private fun createOriginFileWatermarkRecord(imageDisplayRectF: RectF? = null): WatermarkRecord? {
        var watermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_ORIGIN_WATERMARK_INFO) ?: return null
        val phoneInfo = vmBus.get<PhoneInfo>(TOPIC_PHONE_INFO) ?: PhoneInfo()
        imageDisplayRectF?.takeIf { it.isEmpty.not() }?.let {
            watermarkInfo =
                watermarkInfo.copy(aiWatermarkMasterParams = watermarkInfo.aiWatermarkMasterParams?.copy(imageDisplayRectF = imageDisplayRectF))
        }
        return WatermarkRecord.createRemoveWatermarkRecord(watermarkInfo, phoneInfo, null)
    }

    private fun getOriginVideoDisplayRectF(videoSize: Size?): RectF? {
        if (videoSize == null) return null
        // 项目化保存的livephoto，若是原始文件没有videoDisplayRectF，说明livephoto无水印，无需再去水印
        val watermarkInfo =
            vmBus?.get<WatermarkInfo>(TOPIC_ORIGIN_WATERMARK_INFO) ?: vmBus?.get<WatermarkInfo>(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT)

        watermarkInfo?.apply {
            return if (isAiMasterWatermark()
                && (aiWatermarkMasterParams?.watermarkMasterStyle?.isOnlyFrameWatermark() == true)
                && (aiWatermarkMasterParams?.videoDisplayRectF?.isEmpty?.not() == true)
            ) {
                aiWatermarkMasterParams?.videoDisplayRectF?.apply {
                    if (right > videoSize.width) {
                        GLog.d(TAG, LogFlag.DL, "[getOriginVideoDisplayRectF] $right != ${videoSize.width}, reset right")
                        right = videoSize.width.toFloat()
                    }

                    if (bottom > videoSize.height) {
                        GLog.d(TAG, LogFlag.DL, "[getOriginVideoDisplayRectF] $bottom != ${videoSize.height}, reset bottom")
                        bottom = videoSize.height.toFloat()
                    }
                }
            } else {
                null
            }
        }
        return null
    }

    /**
     * 优化timeMs时刻的bitmap, 会根据是否支持hdr等因素执行BitmapQualityWorker/ProXDRWorker/HdrVideoFrameTransformWorker
     * 等算法，并通过resultCallback返回优化后的图片
     * @param timeMs：取帧时刻
     * @param oliveBean：Olive 信息
     * @param resultCallback：包含优化后图片的回调接口
     */
    @Suppress("LongMethod")
    private fun processingOptimizeBitmap(
        timeMs: Long,
        oliveBean: OliveUiBean,
        resultCallback: (Bitmap?, Bitmap?, Any?, Int?) -> Unit,
    ) {
        getInputBitmap(timeMs) { inputBitmap ->
            inputBitmap ?: run {
                resultCallback(null, null, null, null)
                return@getInputBitmap
            }
            val coverProcessingEngine = oliveImageProcessingEngine ?: run {
                // 返回抽帧图吧，起码有图看，要不然黑屏
                resultCallback.invoke(inputBitmap, null, null, null)
                GLog.e(TAG, LogFlag.DL, "[processingOptimizeBitmap] engine is null")
                return@getInputBitmap
            }
            /**
             * 对图片进行质量，效果优化
             */
            launch(Dispatchers.Main) {
                downloadOriginalBitmapDeferredJob?.await()
                val originalCoverItem = getOLiveOriginalCoverItem()

                /**
                 * Marked by tanluyao, 这里里如果originalBitmap不合法，是不是可以让算法只跑upscale算法？
                 */
                val oirignalBitmap = originalCoverItem?.bitmap
                if ((oirignalBitmap == null) || (originalCoverItem.isValid().not())) {
                    GLog.e(TAG, LogFlag.DL) { "[processingOptimizeBitmap] failed, for original CoverItem is not valid." }
                    resultCallback.invoke(null, null, null, null)
                    return@launch
                }
                /*
                   优化封面图
                   此处逻辑会导致拉起导出时Loading动画卡顿，原因暂时未知
                 */
                if (oliveBean.slidingTimeMs == originalCoverItem.timeMs) {
                    GLog.e(TAG, LogFlag.DL) {
                        "[processingOptimizeBitmap] slidingTimeMs: ${oliveBean.slidingTimeMs}, originalCoverItem.timeMs: ${originalCoverItem.timeMs}"
                    }
                    lastRequestId = originalCoverItem.timeMs
                    resultCallback(originalCoverItem.bitmap, originalCoverItem.gainBitmap, originalCoverItem.hdrMeta, null)
                    return@launch
                }
                val originUHdrMetadataPack = getOLiveOriginalHdrMetadataPack()
                lastRequestId = timeMs
                val requestData = RequestData(
                    id = timeMs,
                    originalInfo = RequestData.OriginalInfo(
                        originalBitmap = originalCoverItem.bitmap,
                        originGainMap = originalCoverItem.gainBitmap,
                        originUHdrMetadataPack = originUHdrMetadataPack
                    ),
                    inputBitmap = inputBitmap,
                    imageUri = coverProcessingEngine.imageUri,
                    isUhdrPhoto = getOLiveMediaItem(coverProcessingEngine.imageUri)?.isUhdrPhoto() ?: false,
                    callBack = { requestId, outputData ->
                        val resultBitmap = outputData.contentBitmap ?: inputBitmap
                        // 只有返回的封面是最后一次设置的封面才通知界面更新，否则丢弃掉过程数据，因为用户的选择已改变
                        if (lastRequestId == requestId) {
                            ContextGetter.context.getAppAbility<IColorManagementAbility>()?.use {
                                BitmapUtils.trySetBitmapColorSpace(
                                    resultBitmap, it.colorSpace, "Olive processingOptimizeBitmap ini colorSpace"
                                )
                                resultBitmap.adjustColorSpace()
                            }
                            resultCallback.invoke(resultBitmap, outputData.gainmap, outputData.uHdrMetadataPack?.metadata, outputData.resultCode)
                        } else {
                            GLog.d(TAG, LogFlag.DL) {
                                "[processingOptimizeBitmap] requestId!=lastRequestId," +
                                        "requestId = $requestId, lastRequestId = $lastRequestId. ignore"
                            }
                        }
                        if (resultBitmap != inputBitmap) inputBitmap.recycle()
                    }
                )
                requestData.createRequest(requestData, showRequestLongLoading).let(coverProcessingEngine::requestProcess)
            }
        }
    }

    /**
     * 优化timeMs时刻timeline的bitmap, 会根据是否支持hdr等因素执行BitmapQualityWorker/ProXDRWorker/HdrVideoFrameTransformWorker
     * 等算法，并通过resultCallback返回优化后的图片(此接口仅允许在非主线程中调用)，取图接口：grabThumbnailSync
     * @param timeMs：取帧时刻
     */
    private suspend fun processTimelineFrameBitmap(timeMs: Long): OliveExportInfo? {
        val videoMaxLength = oliveVideoEngine?.getVideoSize()?.maxLen() ?: 0
        // 当前olive中视频是否为hdr资源文件
        val isHdrOliveVideo = oliveVideoEngine?.retrieveInfo()?.displayColorSpace?.isHdrGamma() ?: false
        // 当前设备是否支持下变换
        val isSupportHdrTransform = getBoolean(FEATURE_IS_SUPPORT_PHOTO_PAGE_HDR_VIDEO_OLIVE_TRANSFORM)
        GLog.d(TAG, LogFlag.DL, "[processTimelineFrameBitmap] isHdrOliveVideo: $isHdrOliveVideo, isSupportHdrTransform: $isSupportHdrTransform")

        // 对于视频为HDR格式且设备支持下变换的设备，需要抽帧格式为HDR,否则抽帧格式为SDR(timeline中取出的图片均无水印，无需做水印处理)
        val frameBitmap = if (isHdrOliveVideo && isSupportHdrTransform && ApiLevelUtil.isAtLeastAndroidU()) {
            oliveThumbnailLoader?.oliveInfo?.oliveVideoEngine?.grabThumbnailAsync(
                timeMs * MILLIS_TIME_BASE, videoMaxLength,
                Bitmap.Config.RGBA_1010102, ColorSpaceExt.BT2020_HLG
            )?.second
        } else {
            oliveThumbnailLoader?.oliveInfo?.oliveVideoEngine?.grabThumbnailAsync(timeMs * MILLIS_TIME_BASE, videoMaxLength)?.second
        } ?: run {
            GLog.e(TAG, LogFlag.DL, "[processTimelineFrameBitmap], grabThumbnailSync is null")
            return null
        }

        // 1.处理引擎为null，直接返回取到的图片
        val oliveImageProcessingEngine = oliveImageProcessingEngine ?: run {
            GLog.e(TAG, LogFlag.DL, "[processTimelineFrameBitmap], engine is null, return timeline frame bitmap")
            return null
        }
        // 2.准备request data数据，通知管线执行优化算法
        return processHdrBitmap(timeMs, frameBitmap, oliveImageProcessingEngine)
    }

    /**
     * 管线执行优化算法
     * @param timeMs 算法处理引擎
     * @param inputBitmap 算法优化的图片
     * @param oliveImageProcessingEngine
     * @return OliveExportInfo 优化后的Info
     */
    private suspend fun processHdrBitmap(
        timeMs: Long,
        inputBitmap: Bitmap,
        oliveImageProcessingEngine: OliveImageProcessingEngine,
    ): OliveExportInfo {
        // 如果封面图为null，直接返回取到的图片
        val originalCoverItem = getOLiveOriginalCoverItem()
        if (originalCoverItem?.bitmap == null) {
            GLog.e(TAG, LogFlag.DL, "[processTimelineFrameBitmap] originalCoverItem is null, return timeline frame bitmap")
            return OliveExportInfo(inputBitmap, null, null, null)
        }
        return suspendCoroutine { continuation ->
            val requestData = RequestData(
                id = timeMs,
                originalInfo = RequestData.OriginalInfo(
                    originalBitmap = originalCoverItem.bitmap,
                    originGainMap = originalCoverItem.gainBitmap,
                    originUHdrMetadataPack = getOLiveOriginalHdrMetadataPack()
                ),
                inputBitmap = inputBitmap,
                imageUri = oliveImageProcessingEngine.imageUri,
                isUhdrPhoto = getOLiveMediaItem(oliveImageProcessingEngine.imageUri)?.isUhdrPhoto() ?: false,
                callBack = { _, outputData ->
                    val resultBitmap = outputData.contentBitmap ?: inputBitmap
                    app.getAppAbility<IColorManagementAbility>()?.use {
                        BitmapUtils.trySetBitmapColorSpace(
                            resultBitmap,
                            it.colorSpace,
                            "Olive processTimelineFrameBitmap ini colorSpace"
                        )
                        resultBitmap.adjustColorSpace()
                    }
                    continuation.resume(
                        OliveExportInfo(
                            resultBitmap,
                            outputData.gainmap,
                            outputData.uHdrMetadataPack?.metadata,
                            outputData.resultCode
                        )
                    )
                    if (resultBitmap != inputBitmap) {
                        inputBitmap.recycle()
                    }
                }
            )
            requestData.createRequest(requestData, showRequestLongLoading).let(oliveImageProcessingEngine::requestProcess)
        }
    }

    /**
     * olive处理过程中显示loading
     * request执行过程中判断自身是否耗时，如果耗时，发起显示loading的回调
     */
    private val showRequestLongLoading: (() -> Unit) = {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            // 旧逻辑仅在 recover 初始化阶段判断优化算法是否为耗时操作，现在因为workers动态配置，所以需要重新判断优化算法的耗时情况
            bean.isLongRunningCoverProcessor = true
            triggerLoadingDialogVisibilityUpdateIfNeed()
            // 触发worker loading后恢复耗时状态判断
            bean.isLongRunningCoverProcessor = false
        }
    }

    /**
     * 1.根据当前seekbar  position时间戳，从retrieve 中获取原始帧，并执行对应的帧优化处理
     * 2.拿到封面后，需要执行换图特效，让效果作用在帧上，拿到输出结果后即为需要保存的照片，当前可以一帧执行完，可以不送到preview上
     * 3.不管成功还是失败，需要将操作栈管线还原，还原可以不渲染
     */
    private fun notifyOliveExportImage() {
        vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
            if (bean.isExportProcessing) {
                GLog.d(TAG, LogFlag.DL) { "[notifyOliveExportImage], exportImage is doing" }
                return
            }

            GLog.d(TAG, LogFlag.DL) { "[notifyOliveExportImage], exportImage time:${bean.slidingTimeMs}, OliveUiBean = $bean" }
            bean.isExportProcessing = true
            triggerLoadingDialogVisibilityUpdateIfNeed()

            launch(Dispatchers.IO) {
                kotlin.runCatching {
                    processingOptimizeBitmap(bean.slidingTimeMs, bean) { bitmap, gainMap, metadata, resultCode ->
                        // 通过编辑管线给 retrieve 抽出来的图添加特效
                        val resultBitmap = bitmap ?: kotlin.run {
                            saveExportStatusDisplay(isSuccess = false)
                            return@processingOptimizeBitmap
                        }
                        applyEffectsToExportedImage(bean, resultBitmap, gainMap, metadata) { bitmap, gainBitmap, metadataInfo ->
                            <EMAIL>(Dispatchers.Main) {
                                saveExportedBitmapResult(bitmap, gainBitmap, metadataInfo, resultCode)
                            }
                        }
                    }
                }.onFailure { exception ->
                    saveExportStatusDisplay(isSuccess = false)
                    trackExportPhotoEvent(null, null, null, (exception as? SaveTaskException)?.code)
                }
                bean.isExportProcessing = false
            }
        }
    }

    private fun notifyOliveSlowMotion() {
        if (slowMotionPipeline != null) {
            GLog.w(TAG, LogFlag.DF) { "notifyOliveSlowMotion: slowMotionPipeline is not null. cannot run it twice." }
            return
        }
        slowMotionPipeline = SlowMotionPipelineImpl(editingVM)
        slowMotionPipeline?.initSession()

        // marked by: hucanhua 2025/6/13 需要写视频的路径
        val slowMotionRecord = SlowMotionRecord("/")
        launch {
            val result = slowMotionPipeline?.start(slowMotionRecord)

            slowMotionPipeline?.closeSession()
            slowMotionPipeline = null

            withContext(Dispatchers.UI) {
                vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
                    bean.id = OliveBeanId.OLIVE_SLOW_MOTION
                    oliveUiPub?.publish(bean)
                }
            }
        }
    }

    /**
     * 给olive导出的图片应用特效
     * @param oliveUiBean olive编辑流程状态数据对象
     * @param resultBitmap 待应用特效的输入主图
     * @param gainMap 待应用特效的输入增益图
     * @param metadata hdr图增益信息
     * @param resultCallback 图片应用特效后返回的主图增益图和增益信息
     */
    @Suppress("LongMethod")
    private fun applyEffectsToExportedImage(
        oliveUiBean: OliveUiBean,
        resultBitmap: Bitmap,
        gainMap: Bitmap?,
        metadata: Any?,
        resultCallback: (Bitmap?, Bitmap?, Any?) -> Unit,
    ) {
        if (resultBitmap.isRecycled) {
            GLog.e(TAG, LogFlag.DL) { "[applyEffectsToExportedImage], bitmap is recycled" }
            resultCallback(null, null, null)
            return
        }

        val recordsAfterNonparametric = vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORDS_AFTER_NONPARAMETRIC, EMPTY_STRING
        )?.filter { it.isJustPackingBox.not() } ?: emptyList()

        val recordList = recordsAfterNonparametric.filter {
            it.effectName !in listOf(AvEffect.WatermarkEffect.name, AvEffect.ImageReplaceEffect.name, AvEffect.OliveEffect.name)
        }.toMutableList()

        // 不需要对导出的图片做额外的效果,返回原始的retrieve抽帧数据即可
        if (recordList.isEmpty()) {
            resultCallback(resultBitmap, gainMap, metadata)
            return
        }

        // 将特效按照优先级排序
        val priorityRecordList = recordList.sortedBy { record -> record.priority() }.ktReversed()

        editingVM.submitOnGLIdle {
            val outputWidthIntArray = gainMap?.let { intArrayOf(resultBitmap.width, gainMap.width) } ?: intArrayOf(resultBitmap.width)
            val outputHeightIntArray = gainMap?.let { intArrayOf(resultBitmap.height, gainMap.height) } ?: intArrayOf(resultBitmap.height)
            val colorSpaceArray = gainMap?.let { arrayOf(resultBitmap.colorSpace, gainMap.colorSpace) } ?: arrayOf(resultBitmap.colorSpace)
            val formatArray = gainMap?.let {
                arrayOf(resultBitmap.getConfigSafely(), gainMap.getConfigSafely())
            } ?: arrayOf(resultBitmap.getConfigSafely())

            val overlayTexture = BitmapTexture(resultBitmap, canRecycle = true).apply {
                load()
                force()
            }
            val overlayGainMapTexture = gainMap?.takeIf { it.isRecycled.not() }?.let {
                BitmapTexture(it, canRecycle = true).apply {
                    load()
                    force()
                }
            }

            val hdrInfoMap = oliveUiBean.hdrImageType?.let { type ->
                metadata?.let { meta ->
                    editingVM.sessionProxy.convert(type, meta)
                }
            }
            val oliveRecord = oliveUiBean.asOliveRecord(hdrInfoMap).copy(
                textureId = overlayTexture.textureId,
                gainMapTextureId = overlayGainMapTexture?.textureId,
                outputSourceWidthIntArray = outputWidthIntArray,
                outputSourceHeightIntArray = outputHeightIntArray,
                colorSpaceArray = colorSpaceArray,
                formatArray = formatArray,
            )

            var defaultBitmapWithHdrSink: DefaultBitmapWithHdrSink? = null
            defaultBitmapWithHdrSink = DefaultBitmapWithHdrSink(
                PREVIEW_PHOTO_MAX_LENGTH,
                PREVIEW_PHOTO_MAX_LENGTH,
                onBitmapReceive = { bitmap, hdrImageContent ->
                    GLog.d(TAG, LogFlag.DL) { "[applyEffectsToExportedImage], effectBmpSink bitmap wh=${bitmap.width},${bitmap.height}" }
                    resultCallback(bitmap, hdrImageContent?.grayImage, hdrImageContent?.metadata?.metadata)

                    // 恢复主编辑现场
                    editingVM.sessionProxy.removeEffect(oliveRecord, render = false)
                    defaultBitmapWithHdrSink?.let { editingVM.sessionProxy.removeSink(it.sinkType, it) }

                    editingVM.submitOnGLIdle {
                        overlayTexture.recycle()
                        overlayGainMapTexture?.recycle()
                    }
                })
            editingVM.sessionProxy.insertSingleEffectOnce(oliveRecord, priorityRecordList.first(), defaultBitmapWithHdrSink)
        }
    }

    /**
     * 将导出的图片进行保存
     * @param resultBitmap 保存的主图
     * @param gainmap 保存的增益图
     * @param metadata 保存的增益信息
     * @param resultCode 埋点信息code
     */
    private fun saveExportedBitmapResult(
        resultBitmap: Bitmap?,
        gainmap: Bitmap?,
        metadata: Any?,
        resultCode: Int?,
    ) {
        val metadataPack: IHdrMetadataPack? = (metadata as? UltraHdrInfo)?.let { UHdrMetadataPack(it) }
        val saveData = collectOliveExportData(resultBitmap, gainmap, metadataPack) ?: kotlin.run {
            saveExportStatusDisplay(isSuccess = false)
            return
        }

        launch(Dispatchers.IO) {
            val task = SaveFileTaskFactory().createTask(SaveType.OLIVE_EXPORT_IMAGE, saveData)
            task.run()

            val resultUri = saveData.find<Uri>(SaveData.KEY_RESULT_URI, OutputVM.TAG)
            if (resultUri != null) exportPhotoDataPub?.publish(saveData)
            saveExportStatusDisplay(resultUri != null)
            trackExportPhotoEvent(saveData, resultBitmap, resultUri, resultCode)
        }
    }

    /**
     * 导出成功/失败的状态显示
     * @param isSuccess true/false 导出成功/失败
     */
    private fun saveExportStatusDisplay(isSuccess: Boolean = true) {
        GLog.d(TAG, LogFlag.DL) { "[saveExportStatusDisplay], isSuccess = $isSuccess" }
        launch(Dispatchers.Main.immediate) {
            triggerLoadingDialogVisibilityUpdateIfNeed(true)
            if (isSuccess) {
                vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE)?.also { bean ->
                    bean.id = OliveBeanId.OLIVE_EXPORT_IMAGE
                    oliveUiPub?.publish(bean)
                }
            } else {
                ToastUtil.showShortToast(R.string.picture3d_editor_olive_export_fail)
            }
        }
    }

    /**
     * olive导出图片时的埋点
     * @param saveData SaveData 导出的数据
     * @param bitmap Bitmap? 导出的图片
     * @param codeType Int 导出的结果
     */
    private fun trackExportPhotoEvent(saveData: SaveData?, bitmap: Bitmap?, resultUri: Uri?, resultCode: Int?) {
        val mimeType = if (resultUri != null) app.contentResolver.getType(resultUri) else ""
        val videoInfo = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)?.retrieveInfo()
        val isHDR = if (saveData?.find<IHdrImageContent>(KEY_HDR_IMAGE_CONTENT, TAG) != null) 1 else 0
        val codeType = if (resultUri != null) SAVE_SUCCESS else resultCode
        OLiveEditorTracker.trackOLiveExportPhotoEvent(mimeType, bitmap, isHDR, codeType, videoInfo)
    }

    /**
     * 为Olive超清导出收集SaveData
     * @param bitmap：选中并优化后的图片
     * @param grayMap：hdr增益图
     * @param metaData：hdr meta数据
     */
    private fun collectOliveExportData(
        bitmap: Bitmap?,
        grayMap: Bitmap?,
        metaData: IHdrMetadataPack?,
    ): SaveData? {
        val effectBitmap = bitmap ?: run {
            trackExportPhotoEvent(null, null, null, SAVE_FAILED_GET_FRAME_IMAGE)
            return null
        }

        return vmBus?.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.let {
            val data = ArrayMap<String, Any>()
            data[KEY_DATA_SOURCE] = it
            data[KEY_EFFECT_BITMAP] = effectBitmap
            data[KEY_COLOR_SPACE] = editingVM.sessionProxy.getMetadata(IAvAssetMetadata.MetadataType.EXTENDED, COLOR_SPACE) as ColorSpace
            data[KEY_CONTEXT] = app
            // 如果grayMap与metaData不同时为空，则认为当前取出的图片不支持hdr功能，不保存hdr数据
            if ((grayMap != null) && (metaData != null)) {
                data[KEY_HDR_IMAGE_CONTENT] = HdrImageDrawable(grayMap, metaData)
            }
            data[KEY_SAVE_TYPE] = SaveType.NORMAL
            data[KEY_ORIGIN_FILE_PROCESS_TYPE] = OriginFileProcessType.NONE
            data[KEY_OLIVE_VIDEO_ENGINE] = oliveVideoEngine
            GLog.d(TAG, LogFlag.DL) { "[collectOliveExportData] saveType=${data[KEY_SAVE_TYPE]}, processType=${data[KEY_ORIGIN_FILE_PROCESS_TYPE]}" }
            SaveData(data)
        } ?: kotlin.run {
            GLog.w(TAG, LogFlag.DL) { "[collectOliveExportData], get DataSource is null" }
            trackExportPhotoEvent(null, null, null, SAVE_FAILED_GET_FRAME_IMAGE)
            null
        }
    }

    /**
     * 实况照片，视频部分解析失败时，退出实况编辑，并弹Toast提示
     */
    private fun notifyFinishSelfWithToast() {
        launch(Dispatchers.UI) {
            // 退出实况编辑
            vmBus?.notifyOnce(
                REPLY_TOPIC_OPERATING_ACTION, OperatingAction(R.id.editor_id_text_action_cancel)
            )
            // 弹Toast提示
            vmBus?.notifyOnce(
                TOPIC_NOTIFICATION_ACTION, NotificationAction.ToastAction(R.string.picture3d_editor_olive_photo_error_hint_broken)
            )
        }
    }

    /**
     * 此为全屏阻断式loadingDialog
     * 用途：当点击完成/取消等功能按钮，必要信息未准备好是，可显示
     * 当前使用场景：
     * 1.olive封面未处理好时，点击完成保存
     * 2.olive选图导出过程中
     * @return Boolean loadingDialog操作状态 true显示 false不显示
     */
    private fun triggerLoadingDialogVisibilityUpdateIfNeed(forceHiding: Boolean = false): Boolean {
        // 当编辑基础数据为null时，直接退出界面
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[notifyConfirm] OliveUiBean is null. maybe a bug, return " }
            return false
        }

        /**
         * 可以显示全屏loading弹框的并行条件
         * 1.当封面处于优化中 -- 1.0(upScale)和 2.0(CoverEnhancer) 算法都会有此状态
         * 2.当前使用的算法是耗时算法（2.0 CoverEnhancer）--详见 [OliveUiBean.isLongRunningCoverProcessor]
         * 3.外部业务不要求loading强制消失时--如数据异常等场景需要loading强制消失以保证界面可操作
         * 4.Olive超清导出图片时，需要此loading界面
         */
        return if ((bean.isCoverProcessing || bean.isLongRunningCoverProcessor || bean.isExportProcessing) && forceHiding.not()) {
            vmBus.notifyOnce(TOPIC_NOTIFICATION_ACTION, NotificationAction.LoadingDialogAction(isShowing = true, outsideCancelable = false))
            true
        } else {
            GLog.d(TAG, LogFlag.DL) {
                "[triggerLoadingDialogVisibilityUpdateIfNeed] bean.isCoverProcessing: ${bean.isCoverProcessing}," +
                        " bean.isLongRunningCoverProcessor: ${bean.isLongRunningCoverProcessor}, " +
                        " bean.isExportProcessing: ${bean.isExportProcessing}, forceHiding,not: ${forceHiding.not()}"
            }
            vmBus.notifyOnce(
                TOPIC_NOTIFICATION_ACTION,
                NotificationAction.LoadingDialogAction(isShowing = false, stayTime = LOADING_DIALOG_STAY_TIME, outsideCancelable = false)
            )
            false
        }
    }

    /**
     * 当必要时，再走一次点击完成按钮逻辑
     * 场景：
     * 1.封面处理中点击了完成，在封面处理完成后 呼起
     */
    private fun callDoneButtonActionAgainIfNeed() {
        if (isClickedDoneWhenCoverProcessing.not()) {
            return
        }
        isClickedDoneWhenCoverProcessing = false
        /**
         * MarkedBy 檀路遥： to 吴丹阳
         * 背景：当前编辑框架Section生命周期有时序问题，需要onPause时将TOPIC_OPERATING_ACTION_CONFIRM topic反注册
         * 导致home出界面或者界面被遮挡期间notifyOnce的“完成action”事件无法通知到OliveVM
         * 场景：封面优化loading期间点击完成然后home出相册，等待优化完成后，再进入相册，界面中间的LoadingDialog不消失，但olive编辑界面却被退出了
         * 方案：
         * 当actionConfirm（TOPIC_OPERATING_ACTION_CONFIRM） topic没注册时，只是通知LoadingDialog消失，不走完成按钮的通知，优先保证功能正常使用
         * 后面编辑框架时序优化后去掉相关逻辑，只保留REPLY_TOPIC_OPERATING_ACTION的通知即可
         */
        if (actionConfirmTopicRegistered.not()) {
            triggerLoadingDialogVisibilityUpdateIfNeed()
        } else {
            // 当处理完封面后，延续用户上一次的动作
            vmBus?.notifyOnce(
                REPLY_TOPIC_OPERATING_ACTION,
                OperatingAction(R.id.editor_id_text_action_done, "olive edit call done action again")
            )
        }
    }

    private fun submitCoverToPipeline(
        bitmap: Bitmap,
        gainMap: Bitmap?,
        onRenderEnd: (() -> Unit)? = null,
    ) {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: return
        isCovertRendering = true
        editingVM.submitOnGLIdle {
            // 点击取消之后，主线程会触发onDestroy直接释放bitmap，需要上锁保证访问bitmap安全
            val overlayTexture = synchronized(bitmap) {
                if (bitmap.isRecycled) {
                    isCovertRendering = false
                    GLog.e(TAG, LogFlag.DL) { "[submitCoverToPipeline] bitmap is recycled" }
                    return@submitOnGLIdle
                }
                BitmapTexture(bitmap).apply {
                    load()
                    force()
                }
            }
            val overlayGainMapTexture = gainMap?.takeIf { it.isRecycled.not() }?.let {
                BitmapTexture(it).apply {
                    load()
                    force()
                }
            }

            if ((bitmap.isRecycled) || (gainMap?.isRecycled == true)) {
                GLog.e(TAG, LogFlag.DL) { "[submitCoverToPipeline] bitmap or is gainMap recycled" }
                isCovertRendering = false
                return@submitOnGLIdle
            }

            // 获取图片的宽高
            val outputWidthIntArray = gainMap?.let { intArrayOf(bitmap.width, gainMap.width) } ?: intArrayOf(bitmap.width)
            val outputHeightIntArray = gainMap?.let { intArrayOf(bitmap.height, gainMap.height) } ?: intArrayOf(bitmap.height)
            val colorSpaceArray = gainMap?.let { arrayOf(bitmap.colorSpace, gainMap.colorSpace) } ?: arrayOf(bitmap.colorSpace)
            val formatArray = gainMap?.let { arrayOf(bitmap.getConfigSafely(), gainMap.getConfigSafely()) } ?: arrayOf(bitmap.getConfigSafely())

            launch(Dispatchers.Main) {
                val hdrInfoMap = bean.hdrImageType?.let { type ->
                    bean.hdrMeta?.let { meta ->
                        editingVM.sessionProxy.convert(type, meta)
                    }
                }
                emitOnRenderEnd(
                    bean.asOliveRecord(hdrInfoMap).copy(
                        textureId = overlayTexture.textureId,
                        gainMapTextureId = overlayGainMapTexture?.textureId,
                        outputSourceWidthIntArray = outputWidthIntArray,
                        outputSourceHeightIntArray = outputHeightIntArray,
                        colorSpaceArray = colorSpaceArray,
                        formatArray = formatArray,
                    )
                ) {
                    isCovertRendering = false
                    editingVM.submitOnGLIdle {
                        overlayTexture.reuse()
                        overlayGainMapTexture?.reuse()
                    }
                    triggerLoadingDialogVisibilityUpdateIfNeed(true)
                    onRenderEnd?.invoke()
                    callDoneButtonActionAgainIfNeed()
                }
            }
        }
    }

    /**
     * 点击完成前回调
     */
    private fun notifyConfirm(callback: ExitPageListener) {
        // 当编辑基础数据为null时，直接退出界面
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: let {
            GLog.e(TAG, LogFlag.DL) { "[notifyConfirm] OliveUiBean is null. maybe a bug, exit olive page " }
            triggerLoadingDialogVisibilityUpdateIfNeed(true)
            callback.onExitPage(isConfirm = false)
            return
        }

        // 当封面图正在处理中时，需要等待处理完成后才可以执行退出逻辑
        if (isCovertRendering || (bean.isCoverProcessing && bean.isLongRunningCoverProcessor)) {
            isClickedDoneWhenCoverProcessing = true
            triggerLoadingDialogVisibilityUpdateIfNeed()
            GLog.d(TAG, LogFlag.DL) {
                "[notifyConfirm] the cover is being processed(isCovertRendering = $isCovertRendering). you need to wait for it to complete. return"
            }
            callback.onExitPage(isConfirm = false, exitPage = false)
            return
        }
        isClickedDoneWhenCoverProcessing = false

        val hasCoverChanged = hasCoverChanged()
        val hasOliveEnableChanged = hasOliveEnableChanged()
        val hasOliveSoundEnableChanged = hasOliveSoundEnableChanged()
        val hasDurationChanged = hasDurationChanged()
        OLiveEditorTracker.trackOLiveEditAction(bean, hasCoverChanged, hasDurationChanged, hasOliveEnableChanged, hasOliveSoundEnableChanged)
        if (hasCoverChanged && hasOliveEnableChanged.not() && hasOliveSoundEnableChanged.not() && hasDurationChanged.not()) {
            GLog.d(TAG, LogFlag.DL) { "[notifyConfirm] only hasCoverChanged" }
            // 如果只有封面变化了，则直接退出，因为设置封面的时候已经提了管线。
            notifySwitchImagePreviewModel()
            callback.onExitPage(isConfirm = true)
            return
        }
        if (hasOliveEnableChanged || hasOliveSoundEnableChanged || hasDurationChanged) {
            GLog.d(TAG, LogFlag.DL) {
                "[notifyConfirm] hasOliveEnableChanged = $hasOliveEnableChanged, hasOliveSoundEnableChanged = $hasOliveSoundEnableChanged" +
                        "hasDurationChanged = $hasDurationChanged"
            }
            notifyConfirmWhenRecordChanged(bean, callback)
            return
        }

        // 没有发生变化
        GLog.d(TAG, LogFlag.DL) { "[notifyConfirm] has modified" }
        triggerLoadingDialogVisibilityUpdateIfNeed(true)
        // 切换到图片预览模式
        notifySwitchImagePreviewModel()
        callback.onExitPage(isConfirm = false)
    }

    /**
     * 切换到图片预览模式
     * 实况照片页面如果拖动了下面的缩图轴，但是又没有设置封面（设置封面后会主动切换到图片预览模式），会进入视频模式，
     * 在退出的时候需要将切换到图片预览模式
     */
    private fun notifySwitchImagePreviewModel() {
        shoOliveVideoNtf?.notify(false)
    }

    /**
     * 將需要更新的变更记录提交管线
     * @param bean UI状态属性
     * @param callback 退出页面监听事件
     */
    private fun notifyConfirmWhenRecordChanged(bean: OliveUiBean, callback: ExitPageListener) {
        getCoverBitmap(bean) { bitmap, gainmap ->
            launch(Dispatchers.Main) {
                // 切换到图片预览模式
                notifySwitchImagePreviewModel()
                if ((bitmap == null) || bitmap.isRecycled) {
                    callback.onExitPage(isConfirm = false)
                } else {
                    submitCoverToPipeline(bitmap, gainmap) {
                        callback.onExitPage(isConfirm = true)
                    }
                }
            }
        }
    }

    private fun getCoverBitmap(bean: OliveUiBean, block: (srcBitmap: Bitmap?, gainMap: Bitmap?) -> Unit) {
        if ((bean.coverBitmap == null) || (bean.coverBitmap?.isRecycled == true)) {
            block.invoke(null, null)
            GLog.e(TAG) { "[getCoverBitmap] error, coverBitmap recycled :${bean.coverBitmap?.isRecycled}" }
        } else {
            block.invoke(bean.coverBitmap, bean.coverGainMap)
        }
    }

    /**
     * 点击取消前回调
     */
    private fun notifyCancel(callback: ExitPageListener) {
        // 切换到图片预览模式
        notifySwitchImagePreviewModel()
        callback.onExitPage(false)
    }

    private fun hasCoverChanged(): Boolean {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: return false
        return initiallyBean?.let {
            bean.coverTimeMs != it.coverTimeMs
        } ?: true
    }

    /**
     * olive播放状态是否改变
     * @return
     */
    private fun hasOliveEnableChanged(): Boolean {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: return false
        return initiallyBean?.let { bean.oliveEnable != it.oliveEnable } ?: true
    }

    /**
     * olive声音状态是否改变
     * @return
     */
    private fun hasOliveSoundEnableChanged(): Boolean {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: return false
        return initiallyBean?.let { bean.oliveSoundEnable != it.oliveSoundEnable } ?: true
    }

    private fun hasDurationChanged(): Boolean {
        val bean = vmBus?.get<OliveUiBean>(TOPIC_OLIVE_UI_STATE) ?: return false
        return initiallyBean?.let {
            (bean.startTime != it.startTime) || (bean.endTime != it.endTime)
        } ?: true
    }

    /**
     * 获取当前Olive的原始封面信息
     * @return
     */
    private fun getOLiveOriginalCoverItem(): OLiveCoverItem? {
        vmBus?.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK)?.also { imagePack ->
            return imagePack.oliveOriginalCoverItem
        }

        return null
    }

    /**
     * 当前UHDR解码是不支持改变metadata的，因此整个编辑期间使用同一份metadata。
     *
     * @return
     */
    private fun getOLiveOriginalHdrMetadataPack(): UHdrMetadataPack? {
        /**
         * 当图片是UHDR土拍时，需要携带相关参数
         */
        val hdrDrawable = vmBus?.get<IHdrImageContent>(TopicID.InputArguments.TOPIC_INPUTS_HDR_DRAWABLE)?.takeIf {
            it.isHdrContentValid() && (it.metadata.getHdrType() == HdrImageType.UHDR)
        }
        return (hdrDrawable?.metadata as? UHdrMetadataPack)
    }

    /**
     * 仅用于打印和调试使用
     * @return
     */
    private fun Bitmap?.getBitmapInfo(): String {
        if (this == null) return EMPTY_STRING

        return "size($width:$height), config:$config"
    }

    companion object {
        const val TAG = "OliveVM"
        const val INVALID_TEXTURE_ID = -1
        private const val MILLIS_TIME_BASE: Int = 1000
        private const val LOADING_DIALOG_STAY_TIME = 500L
    }
}

/**
 * Olive图片的封面帧信息
 */
internal data class OliveCoverInfo(
    /**
     * 封面的内容Bitmap
     */
    val coverBitmap: Bitmap,
    /**
     * 封面内容对应的gainMap图
     */
    val gainMap: Bitmap?,
)

internal data class OliveExportInfo(
    val resultBitmap: Bitmap?,
    val gainMap: Bitmap?,
    val metadata: UltraHdrInfo?,
    val resultCode: Int?,
)

/**
 * OperatingRecord转成OliveRecord
 * @return [OliveRecord]
 */
private fun OperatingRecord.asOliveRecord(): OliveRecord {
    val map = this.arguments
    return OliveRecord(
        coverTime = (map[OliveRecord.COVER_TIME] as? Long) ?: OLiveEditConstant.INVALID_TIME_MS,
        coverBitmapHash = (map[OliveRecord.COVER_BITMAP_HASH] as? Int) ?: AppConstants.Number.NUMBER_0,
        originalTime = (map[OliveRecord.ORIGINAL_TIME] as? Long) ?: OLiveEditConstant.INVALID_TIME_MS,
        startTime = (map[OliveRecord.START_TIME] as? Long) ?: OLiveEditConstant.INVALID_TIME_MS,
        endTime = (map[OliveRecord.END_TIME] as? Long) ?: OLiveEditConstant.INVALID_TIME_MS,
        slidingTime = (map[OliveRecord.SLIDING_TIME] as? Long) ?: OLiveEditConstant.INVALID_TIME_MS,
        hasShowUnSupportTips = (map[OliveRecord.HAS_SHOW_UNSUPPORT_TIPS] as? Boolean) ?: false,
        hasImageCoverChanged = (map[OliveRecord.HAS_IMAGE_COVER_CHANGED] as? Boolean) ?: false,
        hasSupportRestoreOriginalExt = (map[OliveRecord.HAS_SUPPORT_RESTORE_ORIGINAL_EXTENSION] as? Boolean) ?: false,
        hdrInfoMap = map[ImageReplaceRecord.ARGUMENT_KEY_REPLACE_SOURCE_HDR_INFO] as? Map<String, Any?>,
        hdrImageType = map[OliveRecord.ARGUMENT_KEY_HDR_TYPE] as? HdrImageType,
        hdrMeta = map[OliveRecord.ARGUMENT_KEY_HDR_META],
        colorSpaceArray = map[ImageReplaceRecord.ARGUMENT_KEY_REPLACE_MAP_COLOR_SPACE] as? Array<ColorSpace?>,
        formatArray = (map[ImageReplaceRecord.ARGUMENT_KEY_REPLACE_MAP_FORMAT] as? Array<Int>)?.map { nativeInt ->
            nativeInt.toConfig()
        }?.toTypedArray(),
        oliveEnable = (map[OliveRecord.OLIVE_SWITCH_CHANGE] as? Boolean) ?: true,
        oliveSoundEnable = (map[OliveRecord.OLIVE_SOUND_CHANGE] as? Boolean) ?: true
    )
}

/**
 * OliveUiBean 转成 OliveRecord
 * @param hdrInfoMap hdr信息的集合，由于OliveUiBean和OliveRecord无法调用到转换方法，所以这里由业务方转换好传入
 * @return [OliveRecord]
 */
private fun OliveUiBean.asOliveRecord(hdrInfoMap: Map<String, Any?>? = null): OliveRecord {
    return OliveRecord(
        coverTime = this.coverTimeMs,
        coverBitmapHash = this.coverBitmap.hashCode(),
        originalTime = this.originalTimeMs,
        startTime = this.startTime,
        endTime = this.endTime,
        slidingTime = this.slidingTimeMs,
        hasShowUnSupportTips = this.hasShowUnSupportTips,
        hasImageCoverChanged = this.hasImageCoverChanged,
        hasSupportRestoreOriginalExt = this.hasSupportRestoreOriginalExt,
        hdrInfoMap = hdrInfoMap,
        hdrMeta = this.hdrMeta,
        hdrImageType = this.hdrImageType,
        oliveEnable = this.oliveEnable,
        oliveSoundEnable = this.oliveSoundEnable
    )
}