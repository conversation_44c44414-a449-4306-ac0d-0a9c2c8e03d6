/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : EraseExtensionTask.kt
 ** Description : 擦除扩展信息任务类
 ** Version     : 1.0
 ** Date        : 2024/07/10
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/07/10      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.output.task.common

import android.content.Context
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainerExt
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_OLIVE_TMP_FILE_PATH
import com.oplus.gallery.photoeditor.editingvvm.output.task.ISaveTask
import com.oplus.gallery.standard_lib.file.File
import java.io.FileInputStream
import java.io.FileOutputStream

/**
 * 擦除扩展信息任务类
 *
 * @param eraseFilePathKey 要擦除扩展信息的文件path的key
 */
internal class EraseExtensionTask(
    override val saveData: SaveData,
    private val eraseFilePathKey: String = SaveData.KEY_FILE_PATH,
    private val needRestore: Boolean = false
) : ISaveTask {

    override val name = TAG
    override fun run() {
        val context = saveData.find<Context>(SaveData.KEY_CONTEXT, TAG) ?: return
        val filePath = saveData.find<String>(eraseFilePathKey, TAG) ?: return
        //保存删除扩展数据前的文件
        if (needRestore) {
            saveFileToTmp(context, filePath)
        }
        FileExtendedContainerExt.eraseExtensionData(context, filePath)
    }

    private fun saveFileToTmp(context: Context, filePath: String) {
        val parentDir = File(context.externalCacheDir ?: context.cacheDir, OLIVE_PHOTO_SAVE_TMP_DIR)
        if (!parentDir.exists()) parentDir.mkdirs()
        val tmpFile = File(parentDir, File(filePath).name)
        if (!tmpFile.exists()) tmpFile.createNewFile()
        FileInputStream(filePath).use { input ->
            FileOutputStream(tmpFile.absolutePath).use { output ->
                if (input.copyTo(output) != 0L) {
                    saveData.data[KEY_OLIVE_TMP_FILE_PATH] = tmpFile.absolutePath
                }
            }
        }
    }

    private companion object {
        private const val TAG = "EraseExtensionTask"
        private const val OLIVE_PHOTO_SAVE_TMP_DIR = "olive_photo_save_tmp"
    }
}