/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIRepairStatus.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/5/17
 ** Author: xiewu<PERSON>e@Apps.Gallery3D
 ** TAG: AIRepairStatus
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2024/05/17    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair

/**
 * AI修复的状态体，描述AI修复的状态
 * @param state 状态
 * @param repairType 修复的类型
 * @param data 只读变量，数据的载体（比如去反光的mask），key在内部定义，
 */
internal data class AIRepairStatus(val state: AIRepairState, val repairType: AIRepairType, val data: Map<String, Any>) {

    companion object {
        const val KEY_DE_REFLECTION_MASK = "key_de_reflection_mask"
        const val KEY_REQUEST_IMAGE = "key_request_image"
    }
}

/**
 * 是否是错误，所有的错误都应该定义在这里
 */
internal val AIRepairState.isError: Boolean
    get() {
        return when (this) {
            AIRepairState.REACH_DAY_LIMIT,
            AIRepairState.ERROR_TIMEOUT,
            AIRepairState.ERROR_SERVICE_BUSY,
            AIRepairState.ERROR_CONTENT_NOT_SUPPORT,
            AIRepairState.ERROR_REPAIR_BAN,
            AIRepairState.ERROR_UNKNOWN,
            AIRepairState.ERROR_NETWORK -> true
            else -> false
        }
    }

/**
 * 是否是错误或者取消的状态
 */
internal val AIRepairState.isErrorOrCancel: Boolean
    get() {
        return isError
            || this == AIRepairState.CANCEL
    }

/**
 * 是否是AI修复结束状态，包含修复成功、取消修复、修复错误
 */
internal val AIRepairState.isFinished: Boolean
    get() {
        return isError
            || (this == AIRepairState.CANCEL)
            || (this == AIRepairState.EFFECT_END)
    }

/**
 * AI修复的状态，描述执行AI修复过程中的状态
 */
internal enum class AIRepairState {
    /**
     * 初始化状态
     */
    INITIAL,

    /**
     * 修复开始
     */
    START,

    /**
     * 执行过程中被取消
     */
    CANCEL,

    /**
     * 成功拿到修复结果
     */
    RESULT_OK,

    /**
     * 修复动效结束
     */
    EFFECT_END,

    /**
     * 超过次数限制
     */
    REACH_DAY_LIMIT,

    /**
     * 执行超时
     */
    ERROR_TIMEOUT,

    /**
     * 网络异常
     */
    ERROR_NETWORK,

    /**
     * 服务器繁忙
     */
    ERROR_SERVICE_BUSY,

    /**
     * 内容不支持（敏感照片）
     */
    ERROR_CONTENT_NOT_SUPPORT,

    /**
     * 功能禁用，比如触发了太多次拒识
     */
    ERROR_REPAIR_BAN,

    /**
     * 未知异常
     */
    ERROR_UNKNOWN
}

/**
 * AI修复支持的类型
 */
enum class AIRepairType {
    /**
     * 去反光
     */
    DEREFLECTION,

    /**
     * 去模糊
     */
    DEBLURING,

    /**
     * 去眩光
     */
    DEGLARE,

    /**
     * 去雾
     */
    DEFOG
}