/*********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - RangeBarStateMachine.kt
 ** Description: 区间选择器状态机
 ** Version: 1.0
 ** Date: 2025/6/12
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>        2025/6/12      1.0        created
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar

import android.util.Log
import com.oplus.gallery.photoeditor.statemachine.IEvent
import com.oplus.gallery.photoeditor.statemachine.IState
import com.oplus.gallery.photoeditor.statemachine.StateMachine
import com.oplus.gallery.photoeditor.statemachine.StateProcess

/**
 * rangeBar状态机, 用于控制rangeBar的交互状态，比如加载中，加载成功，动画过度等状态
 */
class RangeBarStateMachine(initState: State) : StateMachine<State, Event>(initState) {
    private val stateProcesses: MutableMap<Int, StateProcess<State, Event>> = mutableMapOf()

    fun addProcess(stateProcess: StateProcess<State, Event>) {
        stateProcesses[hash(stateProcess.from, stateProcess.event)] = stateProcess
    }

    private fun hash(state: State, event: Event): Int {
        return arrayOf(state, event).contentHashCode()
    }


    override fun onEvent(event: Event) {
        stateProcesses[hash(state, event)]?.also {
            it.func()
            state = it.to
            fireStateChange()
        } ?: run {
            Log.e(TAG, "onEvent: the event: $event cannot match stateProcess. current state: $state")
        }
    }

    companion object {
        const val TAG = "RangeBarStateMachine"
    }
}

/**
 * 状态迁移的事件
 */
enum class Event : IEvent {
    TO_NORMAL,            // 有数据，直接去normal状态
    TO_LOADING,           // 数据加载中事件
    LOAD_SUCCESS,         // 数据加载成功事件
    ANIM_TO_NORMAL,       // 动画去normal状态
}

/**
 * 状态机状态
 */
enum class State : IState {
    LOADING,       // 加载中状态
    TRANSITION,    // 加载成功过度状态
    NORMAL,        // 正常交互状态
}

/**
 * 状态机示意，有括号的为event
 * NORMAL
 *  ↓ (TO_LOADING)
 * LOADING → (LOAD_SUCCESS) → TRANSITION → (ANIM_TO_NORMAL) → NORMAL
 */
