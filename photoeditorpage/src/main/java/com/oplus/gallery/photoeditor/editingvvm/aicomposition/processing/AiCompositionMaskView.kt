/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MaskView.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/11/26 17:44
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/11/26      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aicomposition.processing

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.oplus.gallery.foundation.ui.gesture.GestureRecognizer
import com.oplus.gallery.foundation.ui.gesture.OnComplexGestureListener
import com.oplus.gallery.foundation.ui.gesture.detector.ScaleRotateDetector
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.widget.GestureRecognizerView
import kotlin.math.abs
import kotlin.math.atan2

/**
 * 遮罩控件
 */
class AiCompositionMaskView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.DITHER_FLAG)
    private val strokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        strokeWidth = context.resources.getDimension(R.dimen.photo_editor_ai_composition_maskview_hollow_shadow_width)
        style = Paint.Style.STROKE
        color = context.resources.getColor(R.color.photo_editor_ai_composition_line_color, null)
        setShadowLayer(strokeWidth, 0f, 0f, context.resources.getColor(R.color.photo_editor_ai_composition_mask_line_color, null))
    }
    private val boundF = RectF()
    private val hollowRectF = RectF()
    private var hollowEnable = true
    private val radiusAnimator = ValueAnimator()
    private var backgroundColor: Int = context.resources.getColor(R.color.picture3d_editor_background_editable, null)
    private var shouldDrawBorder: Boolean = false

    private var touchState = TouchState.UNDEFINED


    /**
     * 手势滑动监听器
     */
    private var flingListener: OnSlideListener? = null

    /**
     * 水平滑动距离累计值
     */
    private var accumulatedScrollX = 0f

    /**
     * 标记单次滑动是否触发过翻页，防止单次滑动重复回调onScroll触发多次翻页
     */
    private var isPageChanged = false

    /**
     * 长按监听器
     */
    private var longPressListener: OnLongPressListener? = null

    private var simpleGestureListener: GestureRecognizerView.SimpleGestureListener? = null

    /**
     * 标记是否正在进行长按
     */
    private var isLongPressing = false

    /**
     * 设置手势监听器，用于回调部分手势事件
     */
    fun setSimpleGestureListener(listener: GestureRecognizerView.SimpleGestureListener) {
        this.simpleGestureListener = listener
    }


    /**
     * 手势识别
     */
    private val gestureDetector: GestureRecognizer = GestureRecognizer(context, object : OnComplexGestureListener {

        override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
            if (touchState == TouchState.SCALING) {
                simpleGestureListener?.onScroll(distanceX, distanceY)
                return true
            }
            //如果不在预览区域，则不响应手势事件
            if (!hollowRectF.contains(e2.x, e2.y)) return false
            // 计算滑动角度，取绝对值得到的角度，更符合设计要求
            val angle = Math.toDegrees(atan2(abs(distanceY), abs(distanceX)).toDouble())
            if (angle > ANGLE_TOLERANCE) {
                // 角度不符合要求，清零累计滑动距离
                accumulatedScrollX = 0f
                return false
            }
            // 如果是水平滑动，则更新累计滑动距离
            accumulatedScrollX += distanceX
            when {
                accumulatedScrollX < -ResourceUtils.dp2pxF(context, SCROLL_THRESHOLD) && !isPageChanged -> {
                    when (layoutDirection) {
                        //RTL模式下，滑动方向与正常相反
                        LAYOUT_DIRECTION_RTL -> flingListener?.onSlideLeft()
                        else -> flingListener?.onSlideRight()
                    }
                    isPageChanged = true
                    accumulatedScrollX = 0f
                }

                accumulatedScrollX > ResourceUtils.dp2pxF(context, SCROLL_THRESHOLD) && !isPageChanged -> {
                    when (layoutDirection) {
                        //RTL模式下，滑动方向与正常相反
                        LAYOUT_DIRECTION_RTL -> flingListener?.onSlideRight()
                        else -> flingListener?.onSlideLeft()
                    }
                    isPageChanged = true
                    accumulatedScrollX = 0f
                }
            }
            return true
        }

        override fun onUpOrCancel(event: MotionEvent): Boolean {
            if (touchState == TouchState.SCALING) {
                shouldDrawBorder = false
                backgroundColor = context.resources.getColor(R.color.picture3d_editor_background_editable, null)
                invalidate()
                simpleGestureListener?.onUp(event.x, event.y)
            }
            if (isLongPressing) {
                longPressListener?.onLongPressFinish()
                isLongPressing = false
            }
            isPageChanged = false
            touchState = TouchState.UNDEFINED
            return true
        }

        override fun onHover(x: Float, y: Float): Boolean {
            return false
        }

        override fun onHoverLeave(x: Float, y: Float): Boolean {
            return false
        }

        override fun onDown(e: MotionEvent): Boolean {
            // 处理按下事件
            accumulatedScrollX = 0f
            isPageChanged = false
            touchState = TouchState.CLICKING
            return true
        }

        override fun onShowPress(e: MotionEvent) {
        }

        override fun onSingleTapUp(e: MotionEvent): Boolean {
            // 当手指抬起时重置翻页标记
            isPageChanged = false
            return false
        }

        override fun onLongPress(e: MotionEvent) {
            // 处理长按事件
            longPressListener?.onLongPressBegin(e)
            isLongPressing = true
        }

        override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
            return false
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            return false
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            return false
        }

        override fun onDoubleTapEvent(e: MotionEvent): Boolean {
            return false
        }

        override fun onScaleRotateBegin(
            scalePivotX: Float,
            scalePivotY: Float,
            angle: Float,
            rotatePivotX: Float,
            rotatePivotY: Float,
            scale: Float,
            detector: ScaleRotateDetector?
        ): Boolean {
            GLog.d(TAG, LogFlag.DL) { "onScaleRotateBegin" }
            backgroundColor = context.resources.getColor(R.color.picture3d_editor_background_editable_not, null)
            shouldDrawBorder = true
            invalidate()
            touchState = TouchState.SCALING
            parent.requestDisallowInterceptTouchEvent(true)
            return simpleGestureListener?.onScaleAndRotateBegin(scalePivotX, scalePivotY, angle, scale) == true
        }

        override fun onScaleRotate(
            scalePivotX: Float,
            scalePivotY: Float,
            angle: Float,
            rotatePivotX: Float,
            rotatePivotY: Float,
            scale: Float,
            detector: ScaleRotateDetector
        ): Boolean {
            return simpleGestureListener?.onScaleAndRotate(scalePivotX, scalePivotY, angle, scale, detector) == true
        }

        override fun onScaleRotateEnd(
            scalePivotX: Float,
            scalePivotY: Float,
            angle: Float,
            rotatePivotX: Float,
            rotatePivotY: Float,
            scale: Float,
            detector: ScaleRotateDetector?
        ): Boolean {
            GLog.d(TAG, LogFlag.DL) { "onScaleRotateEnd" }
            return simpleGestureListener?.onScaleAndRotateEnd(scalePivotX, scalePivotY, angle, scale) == true
        }
    })


    private var cornerRadius: Float = 0f
        set(value) {
            field = value
            invalidate()
        }


    /**
     * 设置遮罩镂空区域
     */
    fun setHollowRect(rect: RectF) {
        hollowRectF.set(rect)
        invalidate()
    }

    /**
     * 设置是否生效遮罩镂空区域
     */
    fun setHollowEnable(enable: Boolean) {
        GLog.e(TAG, LogFlag.DL) { "[setHollowEnable] enable = $enable" }
        hollowEnable = enable
        invalidate()
    }

    /**
     * 更新遮罩镂空区圆角
     *
     * @param radius 圆角角度
     * @param immediately 是否立即生效，否则会展示过渡动画
     */
    fun updateCornerRadius(radius: Float, immediately: Boolean) {
        if (radiusAnimator.isRunning) {
            radiusAnimator.cancel()
        }
        if ((cornerRadius == radius) || (radius < 0f)) {
            return
        }
        if (immediately) {
            cornerRadius = radius
            return
        }
        radiusAnimator.setFloatValues(cornerRadius, radius)
        radiusAnimator.duration = RADIUS_DURATION
        radiusAnimator.start()
        radiusAnimator.addUpdateListener {
            cornerRadius = it.animatedValue as Float
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        return gestureDetector.onTouchEvent(event)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        boundF.set(0f, 0f, measuredWidth.toFloat(), measuredHeight.toFloat())
        if (hollowRectF.isEmpty) {
            hollowRectF.set(0f, 0f, measuredWidth.toFloat(), measuredHeight.toFloat())
        }
    }

    override fun onDraw(canvas: Canvas) {
        val layerID = canvas.saveLayer(boundF, paint)
        canvas.drawColor(backgroundColor)
        if (!hollowEnable) return
        // 绘制阴影 绘制阴影的时候不用考虑圆角，因为阴影是矩形
        if (shouldDrawBorder) {
            canvas.drawRoundRect(hollowRectF, 0f, 0f, strokePaint)
        }

        paint.setXfermode(CLEAR_MODE)
        if (cornerRadius > 0f) {
            canvas.drawRoundRect(hollowRectF, cornerRadius, cornerRadius, paint)
        } else {
            canvas.drawRect(hollowRectF, paint)
        }
        paint.setXfermode(null)
        canvas.restoreToCount(layerID)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        radiusAnimator.cancel()
        radiusAnimator.removeAllListeners()
        flingListener = null
    }

    /**
     * 手势滑动监听器接口
     */
    interface OnSlideListener {
        /**
         * 手势左滑监听
         *
         */
        fun onSlideLeft()

        /**
         * 手势右滑监听
         *
         */
        fun onSlideRight()
    }

    /**
     * 长按监听器接口
     */
    interface OnLongPressListener {
        /**
         * 长按开始
         *
         */
        fun onLongPressBegin(event: MotionEvent)

        /**
         * 长按结束
         *
         */
        fun onLongPressFinish()
    }

    /**
     * 设置手势滑动监听器
     *
     * @param listener 手势滑动监听器
     */
    fun setOnSlideListener(listener: OnSlideListener) {
        flingListener = listener
    }

    /**
     * 设置长按监听器
     *
     * @param listener 长按监听器
     */
    fun setOnLongPressListener(listener: OnLongPressListener) {
        longPressListener = listener
    }

    enum class TouchState {
        /**
         * 默认状态
         */
        UNDEFINED,

        /**
         * 缩放状态
         */
        SCALING,

        /**
         * 双指滚动状态
         */
        SCROLLING,

        /**
         * 点击状态，如果是缩放或移动，会很快设置为SCALING或SCROLLING
         */
        CLICKING
    }

    companion object {
        private const val TAG = "MaskView"
        private val CLEAR_MODE = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
        private const val RADIUS_DURATION = 300L

        /*翻页滑动的像素值*/
        private const val SCROLL_THRESHOLD = 25f

        /*翻页滑动的角度容差*/
        private const val ANGLE_TOLERANCE = 70.0
    }
}