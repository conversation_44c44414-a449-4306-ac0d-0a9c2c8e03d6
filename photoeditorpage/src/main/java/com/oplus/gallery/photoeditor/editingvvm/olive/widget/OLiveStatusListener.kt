/*********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OLiveStatusListener.kt
 ** Description: OLive（动态照片）实况状态开关发生变化的回调
 ** Version: 1.0
 ** Date: 2024/11/14
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2024/11/14      1.0        created
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.widget

import com.oplus.gallery.photoeditor.editingvvm.olive.OLiveSlowMotionState

/**
 * OLive（动态照片）实况状态开关发生变化的回调
 */
interface OLiveStatusListener {

    /**
     * 实况设置状态变化
     * @param onEnableChange 是否开启
     */
    fun onSwitchChange(onEnableChange: Boolean)

    /**
     * 声音设置状态变化
     * @param onEnableChange 是否开启
     */
    fun onSoundChange(onEnableChange: Boolean)

    /**
     * 慢动作设置状态变化
     * @param slowMotionState 慢动作开关状态 [OLiveSlowMotionState.CLOSED] [OLiveSlowMotionState.OPENING]
     */
    fun onSlowMotionStateChange(slowMotionState: OLiveSlowMotionState)

    /**
     * 设置封面
     */
    fun onCoverChange()

    /**
     * 导出照片
     */
    fun onExportPhoto()
}