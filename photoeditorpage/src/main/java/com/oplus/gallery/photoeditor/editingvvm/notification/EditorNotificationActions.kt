/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - EditorNotificationActions.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/26
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/4/26        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.notification

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.text.BidiFormatter
import android.util.Size
import androidx.annotation.StringRes
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uiflowinterceptor.UserAgreementInterceptor
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.uiflowinterceptor.ToastInterceptor
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.foundation.util.ext.minLen
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.notification.NoRemindNormalDialogAction.Companion.KEY_IS_REMEMBER_CHOICE_CHECKED
import com.oplus.gallery.photoeditor.editingvvm.notification.SaveDialogAction.Companion.KEY_IS_REMEMBER_CHOICE_CHECKED
import java.util.Locale
import com.oplus.gallery.basebiz.R as BaseR
import com.oplus.gallery.framework.abilities.authorizing.R as AuthorizeR

/**
 * 尺寸限制toast
 */
class MediaSizeLimitInterceptor(
    private val context: Context,
    private val mediaSize: Size,
    private val minLength: Int,
    private val maxLength: Int,
    @StringRes
    private val minSizeToastMsgResId: Int? = null,
    updateUI: (NotificationAction) -> Unit
) : ToastInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        return mediaSize.maxLen() <= maxLength && mediaSize.minLen() >= minLength
    }

    override fun createToastAction(): NotificationAction.ToastAction {
        val toastMsgResId = if (mediaSize.maxLen() > maxLength) {
            R.string.picture3d_editor_text_image_is_too_big
        } else {
            minSizeToastMsgResId ?: R.string.picture3d_editor_text_image_is_too_small
        }
        val toastMsg = BidiFormatter.getInstance(Locale.getDefault()).unicodeWrap(context.getString(toastMsgResId))
        return NotificationAction.ToastAction(textString = toastMsg)
    }
}

/**
 * 宽高比例限制toast
 */
class MediaRatioLimitInterceptor(
    private val context: Context,
    private val mediaSize: Size,
    private val maxSupportedRatio: Int,
    @StringRes
    private val toastMsgResId: Int,
    updateUI: (NotificationAction) -> Unit
) : ToastInterceptor(updateUI) {
    override fun onCheckCondition(param: Bundle): Boolean {
        return ((mediaSize.maxLen().toFloat() / mediaSize.minLen()) <= maxSupportedRatio)
    }

    override fun createToastAction(): NotificationAction.ToastAction {
        val toastMsg = BidiFormatter.getInstance(Locale.getDefault()).unicodeWrap(context.getString(toastMsgResId))
        return NotificationAction.ToastAction(textString = toastMsg)
    }
}

/**
 * AI功能 外销隐私拦截
 */
class AIFuncExportPrivacyInterceptor(
    context: Context,
    private val configId: String,
    private val contentStringId: Int,
    updateUI: (NotificationAction) -> Unit
) : UserAgreementInterceptor(
    context,
    UserAgreementParams.ContentAndLink(
        contentStringId = contentStringId,
        privacyContainerLinkStringId = AuthorizeR.string.authorizing_check_details_msg_1,
        agreementAndPrivacyContainerLinkStringId = AuthorizeR.string.authorizing_check_details_msg_2
    ),
    updateUI
) {
    override fun onCheckCondition(param: Bundle): Boolean =
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN) ||
                ConfigAbilityWrapper.getBoolean(configId, false)

    override fun onAgreed(activity: Activity) {
        super.onAgreed(activity)
        context.getAppAbility<ISettingsAbility>()?.use {
            it.authorizeExportPrivacy(configId)
        }
    }
}

/**
 * AI功能 外销隐私拦截
 */
class AIFuncRealmeExportPrivacyInterceptor(
    context: Context,
    private val configId: String,
    private val contentStringId: Int,
    updateUI: (NotificationAction) -> Unit
) : UserAgreementInterceptor(
    context,
    UserAgreementParams.ContentWithNoNeedAgreement(
       contentStringId
    ),
    updateUI
) {
    override fun onCheckCondition(param: Bundle): Boolean =
        ConfigAbilityWrapper.getBoolean(IS_REGION_CN) ||
                ConfigAbilityWrapper.getBoolean(configId, false)

    override fun onAgreed(activity: Activity) {
        super.onAgreed(activity)
        context.getAppAbility<ISettingsAbility>()?.use {
            it.authorizeExportPrivacy(configId)
        }
    }
}

/**
 * 放弃修改确认弹窗
 */
class DiscardChangeConfirmDialogAction(
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = R.string.picture3d_editor_text_abandon_current_modify,
    cancelable = true,
    negativeButtonTextResId = BaseR.string.common_cancel,
    neutralButtonTextResId = R.string.picture3d_editor_text_abandon_amend,
    confirmCallback = confirmCallback
)

/**
 * 保存弹窗
 */
open class SaveDialogAction(
    titleResId: Int = R.string.picture3d_editor_save_dialog_title,
    msgResId: Int? = R.string.picture3d_editor_save_dialog_msg,
    positiveButtonTextResId: Int? = R.string.picture3d_editor_save_dialog_as_new,
    negativeButtonTextResId: Int? = BaseR.string.common_cancel,
    neutralButtonTextResId: Int? = R.string.picture3d_editor_save_dialog_overwrite_original,
    /**
     * 是否显示“记住此次选择，可在相册设置中修改保存方式”的复选框
     */
    private val isShowCheckBox: Boolean? = true,
    /**
     * 当 [isShowCheckBox] 为true时，存储状态的bundle，例如通过 [KEY_IS_REMEMBER_CHOICE_CHECKED] 获取复选框状态
     */
    private val bundle: Bundle = Bundle(),
    confirmCallback: ISaveConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    dialogStyleResId = com.support.dialog.R.style.COUIAlertDialog_List_Bottom,
    colorThemeResId = R.style.SaveDialogColorThemeStyle,
    titleResId = titleResId,
    messageResId = msgResId,
    cancelable = true,
    positiveButtonTextResId = positiveButtonTextResId,
    negativeButtonTextResId = negativeButtonTextResId,
    neutralButtonTextResId = neutralButtonTextResId,
    confirmCallback = object : IConfirmCallback {
        override fun onPositiveButtonClicked(activity: Activity) {
            confirmCallback.onPositiveButtonClicked(isChecked())
        }

        override fun onNegativeButtonClicked() {
            confirmCallback.onNegativeButtonClicked(isChecked())
        }

        override fun onNeutralButtonClicked() {
            confirmCallback.onNeutralButtonClicked(isChecked())
        }

        private fun isChecked(): Boolean {
            return bundle.getBoolean(KEY_IS_REMEMBER_CHOICE_CHECKED)
        }
    }
) {
    override val viewData: ViewData?
        get() = if (isShowCheckBox == true) CheckBoxViewData(bundle) else null

    /**
     * “记住此次选择，可在相册设置中修改保存方式”的复选框
     */
    class CheckBoxViewData(val bundle: Bundle) : ViewData()

    companion object {
        /**
         * 是否选中“记住此次选择，可在相册设置中修改保存方式”的复选框
         */
        const val KEY_IS_REMEMBER_CHOICE_CHECKED = "key_is_remember_choice_checked"
    }

    interface ISaveConfirmCallback {
        fun onPositiveButtonClicked(isChecked: Boolean): Unit = Unit
        fun onNegativeButtonClicked(isChecked: Boolean): Unit = Unit
        fun onNeutralButtonClicked(isChecked: Boolean): Unit = Unit
    }
}

/**
 * 带 “不再提醒” 勾选框的确认（底部只有一个知道了）弹框
 */
class NoRemindNormalDialogAction(
    titleResId: Int,
    messageResId: Int,
    /**
     * 是否显示“不再提醒”的复选框
     */
    private val isShowCheckBox: Boolean = true,
    /**
     * 当 [isShowCheckBox] 为true时，存储状态的bundle，例如通过 [KEY_IS_REMEMBER_CHOICE_CHECKED] 获取复选框状态
     */
    private val bundle: Bundle = Bundle(),
    confirmCallback: ISecurityConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    dialogStyleResId = com.support.dialog.R.style.COUIAlertDialog_List_Bottom,
    titleResId = titleResId,
    messageResId = messageResId,
    positiveButtonTextResId = com.oplus.gallery.basebiz.R.string.common_ok,
    confirmCallback = object : IConfirmCallback {
        override fun onPositiveButtonClicked(activity: Activity) {
            confirmCallback.onPositiveButtonClicked(isChecked())
        }

        private fun isChecked(): Boolean {
            return bundle.getBoolean(KEY_IS_REMEMBER_CHOICE_CHECKED)
        }
    }
) {
    override val viewData: ViewData?
        get() = if (isShowCheckBox) CheckBoxViewData(bundle) else null

    /**
     * “记住此次选择，可在相册设置中修改保存方式”的复选框
     */
    class CheckBoxViewData(val bundle: Bundle) : ViewData()

    companion object {
        /**
         * 是否选中“不再提醒”的复选框
         */
        const val KEY_IS_REMEMBER_CHOICE_CHECKED = "key_is_remember_choice_checked"
    }

    interface ISecurityConfirmCallback {
        fun onPositiveButtonClicked(isChecked: Boolean): Unit = Unit
    }
}

/**
 * 还原弹框
 * --取消--|--还原--
 */
class RestoreDialogAction(
    titleResId: Int,
    messageResId: Int,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = titleResId,
    messageResId = messageResId,
    positiveButtonTextResId = R.string.picture3d_editor_text_reset,
    negativeButtonTextResId = BaseR.string.common_cancel,
    confirmCallback = confirmCallback
)


/**
 * 重试弹框
 * --退出--|--重试--
 */
class RetryDialogAction(
    titleResId: Int,
    confirmCallback: IConfirmCallback,
    negativeButtonTextResId: Int = BaseR.string.common_quit
) : NotificationAction.ConfirmDialogAction(
    titleResId = titleResId,
    positiveButtonTextResId = BaseR.string.common_retry,
    negativeButtonTextResId = negativeButtonTextResId,
    confirmCallback = confirmCallback
)

/**
 * 网络断开弹框
 */
class NoNetworkDialogAction(
    confirmCallback: IConfirmCallback
) : GotItDialogAction(
    titleResId = R.string.picture3d_editor_text_network_cancel,
    confirmCallback = confirmCallback
)

/**
 * 网络异常弹框
 */
class NetworkErrorDialogAction(
    confirmCallback: IConfirmCallback
) : GotItDialogAction(
    titleResId = R.string.picture3d_editor_text_network_error,
    confirmCallback = confirmCallback
)

/**
 * "知道了" 弹框
 * --知道了--
 */
open class GotItDialogAction(
    titleResId: Int,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = titleResId,
    positiveButtonTextResId = BaseR.string.common_ok,
    confirmCallback = confirmCallback
)

/**
 * 携带详细描述的"知道了" 弹框
 * --知道了--
 */
open class GotItMessageDialogAction(
    titleResId: Int,
    messageResId: Int,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = titleResId,
    messageResId = messageResId,
    positiveButtonTextResId = BaseR.string.common_ok,
    confirmCallback = confirmCallback
)

/**
 * 复原按钮弹窗
 */
class RecoverConfirmDialogAction(
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = R.string.picture3d_button_recover_title_new,
    messageResId = R.string.picture3d_button_recover_message_new,
    negativeButtonTextResId = BaseR.string.common_cancel,
    neutralButtonTextResId = R.string.picture3d_button_recover,
    confirmCallback = confirmCallback
)

/**
 * 可以传标题,取消,确认资源Id的通用弹窗
 */
class NormalDialogAction(
    titleResId: Int,
    positiveButtonTextResId: Int,
    negativeButtonTextResId: Int,
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = titleResId,
    positiveButtonTextResId = positiveButtonTextResId,
    negativeButtonTextResId = negativeButtonTextResId,
    confirmCallback = confirmCallback
)

/**
 * 取消/退出弹窗
 */
class ExitConfirmDialogAction(
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = R.string.picture3d_editor_exit_title_new,
    messageResId = R.string.picture3d_editor_exit_subtitle_new,
    negativeButtonTextResId = BaseR.string.common_cancel,
    neutralButtonTextResId = BaseR.string.common_quit,
    confirmCallback = confirmCallback
)

/**
 * 取消/继续调整弹窗
 */
class AdjustmentConfirmDialogAction(
    confirmCallback: IConfirmCallback
) : NotificationAction.ConfirmDialogAction(
    titleResId = R.string.picture3d_editor_text_continue_adjust_msg,
    negativeButtonTextResId = BaseR.string.common_cancel,
    neutralButtonTextResId = R.string.picture3d_editor_text_continue_adjust,
    confirmCallback = confirmCallback
)

