/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ComponentVM
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component

import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.RemoteModelInfoManager
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPlugin
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.AIUnitPluginState
import com.oplus.gallery.basebiz.uiflowinterceptor.aiunit.SceneNamePluginState
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_BEST_TAKE_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_COMPOSITION_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_DEREFLECTION_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_DE_BLUR_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_GRAFFITI_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_LIGHTING_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_DEGLARE_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_BLUR_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.AI_REPAIR_DE_REFLECTION_DETECT_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.DETECTOR_DOWN_NAME_KEY_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AGREE_AI_UNIT_PRIVACY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_AI_UNIT_CONFIG_AVAILABLE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_AUTO_MOSAIC_MODEL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_DOWNLOAD_PASSERBY_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_AUTO_MOSAIC_MODEL
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.IS_NEED_UPDATE_PASSERS_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.RM_AI_DE_BLUR_DOWNLOAD_STATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_BEST_TAKE_MODEL_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_COMPOSITION_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_DEBLUR_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_DEGLARE_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_DEREFLECTION_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_GRAFFITI_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_AI_LIGHTING_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_IMAGE_QUALITY_ENHANCE_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.UPDATE_RM_AI_DEBLUR_SHOW_TIMESTAMP
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IPU_BEAUTY
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_RM_AI_DEBLUE
import com.oplus.gallery.framework.abilities.download.DownloadStateListener
import com.oplus.gallery.framework.abilities.download.IAIUnitDownloadAbility
import com.oplus.gallery.framework.abilities.download.SceneNamePluginID
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate.ModelLoadingListener
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Component.REPLY_TOPIC_COMPONENT_CHECK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Component.REPLY_TOPIC_FETCH_REMOTE_MODEL_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Component.TOPIC_MODEL_DOWNLOAD_PROGRESS_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.component.aifilter.AiFilterModelLoader
import com.oplus.gallery.photoeditor.editingvvm.component.beauty.BeautyModelLoader
import com.oplus.gallery.photoeditor.editingvvm.component.beauty.BeautyModelSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.besttake.BestTakeComponentSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.ai.AIEliminatePluginState
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.normal.EliminatePenModelLoader
import com.oplus.gallery.photoeditor.editingvvm.detect.DetectingArg
import com.oplus.gallery.photoeditor.editingvvm.detect.DetectingResult
import com.oplus.gallery.photoeditor.editingvvm.detect.FaceState
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.standard_lib.app.UI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 编辑组件下载的主要驱动模块，它的主要职责有：
 * 读取并管理编辑器业务的所有可下载组件，记录组件的下载、安装状态
 * 接收来自其他SubVM的组件下载事件触发，并对涉及的组件进行状态检查和下载安装
 * 在组件的下载、安装过程中，通过NotificationSection与用户进行状态交互
 * @param editingVM 主VM
 */
internal class ComponentVM(editingVM: EditingVM) : EditingSubVM(editingVM) {
    private val checkSupportabilityRep = object : UnitReplier<ComponentCheckArg>() {
        override fun onSingleReply(arg: ComponentCheckArg) {
            checkSupportability(arg)
        }
    }

    private val fetchRemoteModelInfoRep = object : UnitReplier<String>() {
        override fun onSingleReply(arg: String) {
            fetchRemoteModelInfo(arg)
        }
    }

    private val downloadStateListener: DownloadStateListener = { isAvailable ->
        if (isAvailable) {
            writeAIUnitConfig()
        } else {
            GLog.e(TAG, "[downloadStateListener] all download state is invalid")
        }
    }

    private val aiDeblurPluginState by lazy {
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_RM_AI_DEBLUE)) {
            AIUnitPluginState(
                AIUnitPlugin.RM_AI_DEBLUR_LOCAL.downloadPlugin,
                RM_AI_DE_BLUR_DOWNLOAD_STATE,
                UPDATE_RM_AI_DEBLUR_SHOW_TIMESTAMP
            )
        } else {
            AIUnitPluginState(
                AIUnitPlugin.AI_DEBLUR.downloadPlugin,
                AI_DE_BLUR_DOWNLOAD_STATE,
                UPDATE_AI_DEBLUR_SHOW_TIMESTAMP
            )
        }
    }

    private val progressItems by lazy {
        listOf(
            // AI消除
            ModelDownloadStateItem(
                R.id.strategy_ai_eliminate,
                AIEliminatePluginState()
            ),
            // AI去模糊插件下载状态
            ModelDownloadStateItem(
                R.id.strategy_ai_deblur,
                aiDeblurPluginState
            ),
            // AI去反光插件的下载状态
            ModelDownloadStateItem(
                R.id.strategy_ai_dereflection,
                AIUnitPluginState(
                    AIUnitPlugin.AI_DEREFLECTION.downloadPlugin,
                    AI_DEREFLECTION_DOWNLOAD_STATE,
                    UPDATE_AI_DEREFLECTION_SHOW_TIMESTAMP
                )
            ),
            // AI画质增强插件的下载状态
            ModelDownloadStateItem(
                R.id.strategy_image_quality_enhance,
                SceneNamePluginState(
                    AIUnitPlugin.IMAGE_QUALITY_ENHANCE.downloadPlugin,
                    IS_NEED_DOWNLOAD_IMAGE_QUALITY_ENHANCE,
                    IS_NEED_UPDATE_IMAGE_QUALITY_ENHANCE,
                    UPDATE_IMAGE_QUALITY_ENHANCE_SHOW_TIMESTAMP
                )
            ),
            // AI涂鸦插件的下载状态
            ModelDownloadStateItem(
                R.id.strategy_ai_graffiti,
                AIUnitPluginState(
                    AIUnitPlugin.AI_GRAFFITI.downloadPlugin,
                    AI_GRAFFITI_DOWNLOAD_STATE,
                    UPDATE_AI_GRAFFITI_SHOW_TIMESTAMP
                )
            ),
            // AI构图插件的下载状态
            ModelDownloadStateItem(
                R.id.strategy_ai_composition,
                AIUnitPluginState(
                    AIUnitPlugin.AI_COMPOSITION.downloadPlugin,
                    AI_COMPOSITION_DOWNLOAD_STATE,
                    UPDATE_AI_COMPOSITION_SHOW_TIMESTAMP
                )
            ),
            // AI最佳表情插件的下载状态
            ModelDownloadStateItem(
                R.id.strategy_ai_best_take,
                AIUnitPluginState(
                    AIUnitPlugin.AI_BEST_TAKE.downloadPlugin,
                    AI_BEST_TAKE_DOWNLOAD_STATE,
                    UPDATE_AI_BEST_TAKE_MODEL_SHOW_TIMESTAMP
                )
            ),
            // AI补光插件的下载状态
            ModelDownloadStateItem(
                R.id.strategy_ai_lighting,
                AIUnitPluginState(
                    AIUnitPlugin.AI_LIGHTING.downloadPlugin,
                    AI_LIGHTING_DOWNLOAD_STATE,
                    UPDATE_AI_LIGHTING_SHOW_TIMESTAMP
                )
            ),
            // AI去眩光插件的下载状态
            ModelDownloadStateItem(
                R.id.strategy_ai_deglare,
                AIUnitPluginState(
                    AIUnitPlugin.AI_DEGLARE.downloadPlugin,
                    AI_DEGLARE_DOWNLOAD_STATE,
                    UPDATE_AI_DEGLARE_SHOW_TIMESTAMP
                )
            )
        )
    }

    private var progressItemPub: PublisherChannel<List<ModelDownloadStateItem>>? = null
    // 模型支持检查策略的字典映射
    private var supportCheckStrategyMap = mutableMapOf<Int, ISupportCheckStrategy>()
    private var downloadAbility: IAIUnitDownloadAbility? = null

    init {
        vmBus?.apply {
            register(REPLY_TOPIC_COMPONENT_CHECK, checkSupportabilityRep)
            register(REPLY_TOPIC_FETCH_REMOTE_MODEL_INFO, fetchRemoteModelInfoRep)
            progressItemPub = register(TOPIC_MODEL_DOWNLOAD_PROGRESS_ITEM)
        }
    }

    override fun onCreate() {
        super.onCreate()
        progressItemPub?.publish(progressItems)
        downloadAbility = app.getAppAbility<IAIUnitDownloadAbility>()
        downloadAbility?.registerDownloadStateListener(app, downloadStateListener)
    }

    override fun onResume() {
        super.onResume()
        if (ConfigAbilityWrapper.getBoolean(IS_AI_UNIT_CONFIG_AVAILABLE)) {
            launch(Dispatchers.IO) {
                writeAIUnitConfig()
            }
        } else {
            // aiunit的本地状态不依赖配置刷新
            updateAIUnitLocalState()
        }
        // 每次回到前台都要检查一下aiunit的配置，避免后台清除aiunit数据导致配置过时
        downloadAbility?.updateDownloadState(app)
    }

    private fun updateAIUnitLocalState() {
        launch(Dispatchers.IO) {
            app.getAppAbility<ISettingsAbility>()?.use {
                it.updateBlockingConfig(app, IS_AGREE_AI_UNIT_PRIVACY)
            }
        }
    }

    private fun writeAIUnitConfig() {
        val start = System.currentTimeMillis()
        app.getAppAbility<ISettingsAbility>()?.use {
            // AI消除、马赛克、AI修复和AIUnit隐私政策的配置
            it.updateBlockingConfig(
                app,
                IS_NEED_DOWNLOAD_AI_ELIMINATE,
                IS_NEED_DOWNLOAD_PASSERBY_ELIMINATE,
                AI_REPAIR_DE_BLUR_DETECT_STATE,
                AI_REPAIR_DE_REFLECTION_DETECT_STATE,
                IS_NEED_DOWNLOAD_IMAGE_QUALITY_ENHANCE,
                IS_NEED_DOWNLOAD_AUTO_MOSAIC_MODEL,
                IS_NEED_UPDATE_PASSERS_ELIMINATE,
                IS_NEED_UPDATE_AI_ELIMINATE,
                IS_NEED_UPDATE_AUTO_MOSAIC_MODEL,
                IS_AGREE_AI_UNIT_PRIVACY,
                AI_DEREFLECTION_DOWNLOAD_STATE,
                AI_GRAFFITI_DOWNLOAD_STATE,
                AI_DE_BLUR_DOWNLOAD_STATE,
                RM_AI_DE_BLUR_DOWNLOAD_STATE,
                AI_COMPOSITION_DETECT_STATE,
                AI_COMPOSITION_DOWNLOAD_STATE,
                AI_BEST_TAKE_DETECT_STATE,
                AI_BEST_TAKE_DOWNLOAD_STATE,
                AI_LIGHTING_DETECT_STATE,
                AI_LIGHTING_DOWNLOAD_STATE,
                AI_DEGLARE_DOWNLOAD_STATE
            )
        }
        GLog.d(TAG, LogFlag.DF, "[writeAIUnitConfig] cost time:${GLog.getTime(start)} ms")
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unregister(REPLY_TOPIC_COMPONENT_CHECK, checkSupportabilityRep)
            unregister(REPLY_TOPIC_FETCH_REMOTE_MODEL_INFO, fetchRemoteModelInfoRep)
            unregisterT(TOPIC_MODEL_DOWNLOAD_PROGRESS_ITEM, progressItemPub)
        }
        supportCheckStrategyMap.forEach { (_, supportCheckStrategy) ->
            supportCheckStrategy.destroy()
        }
        downloadAbility?.unregisterDownloadStateListener(downloadStateListener)
        downloadAbility?.close()
        downloadAbility = null
    }

    /**
     * 拉取云端模型信息
     * @param entrance 入口，用于日志打印
     */
    private fun fetchRemoteModelInfo(entrance: String) {
        RemoteModelInfoManager.fetch(editingVM.getApplication(), null, false, entrance)
    }

    /**
     * 检查该功能模型是否完整，如果当前模型不完整，将发起下载请求
     * @param componentCheckArg 策略检查参数
     */
    private fun checkSupportability(componentCheckArg: ComponentCheckArg) {
        val function = componentCheckArg.function
        when (componentCheckArg.strategyID) {
            R.id.strategy_ai_filter -> CommonModelSupportCheckStrategy(AiFilterModelLoader(app))
            R.id.strategy_eliminate_pen -> CommonModelSupportCheckStrategy(EliminatePenModelLoader(app))
            R.id.strategy_ai_best_take -> BestTakeComponentSupportCheckStrategy()
            R.id.strategy_beauty -> {
                if (!ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_IPU_BEAUTY, false)) {
                    BeautyModelSupportCheckStrategy(
                        remoteModelLoader = BeautyModelLoader(app),
                        loadingListener = vmBus?.get<ModelLoadingListener>(TopicID.Menu.TOPIC_MENU_MENU_LIST_DOWNLOAD_LISTENER),
                        detectFace = { faceDetectCallback ->
                            vmBus?.notifyOnce(
                                TopicID.Detecting.TOPIC_DETECTING_BY_STRATEGY,
                                DetectingArg(R.id.strategy_beauty) {
                                    faceDetectCallback((it as? DetectingResult.Face)?.faceState == FaceState.FACE)
                                }
                            )
                        }
                    )
                } else {
                    null
                }
            }
            else -> null
        }?.also { strategy ->
            supportCheckStrategyMap.remove(componentCheckArg.strategyID)?.destroy()
            supportCheckStrategyMap[componentCheckArg.strategyID] = strategy

            strategy.checkSupportability(app, function) { action ->
                GLog.d(TAG, "checkSupportability: action = $action")
                launch(Dispatchers.UI) {
                    vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
                }
            }
        } ?: function(true)
    }

    internal companion object {
        const val TAG = "ComponentVM"
    }
}

/**
 * [REPLY_TOPIC_COMPONENT_CHECK] 的订阅者的入参
 */
internal data class ComponentCheckArg(
    val strategyID: Int,
    val function: (isSupport: Boolean) -> Unit
)