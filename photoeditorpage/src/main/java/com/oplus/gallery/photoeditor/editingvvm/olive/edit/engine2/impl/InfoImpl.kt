/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - InfoImpl.kt
 ** Description: IInfo 实现类
 ** Version: 1.0
 ** Date: 2025/06/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>        2025/06/19  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.impl

import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.util.Rational
import android.util.Size
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.IInfo
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.VideoInfo

/**
 * IInfo 实现类
 * @param timeline 要进行编辑的 timeline 对象
 */
class InfoImpl : IInfo {
    override fun getSize(): Size {
        return Size(0, 0)
    }

    override fun getFps(): Rational {
        return Rational.NaN
    }

    override fun getInfo(): VideoInfo {
        return VideoInfo()
    }

    override suspend fun getEffectThumbnail(timeUs: Long, maxLength: Int, config: Bitmap.Config?, colorSpace: ColorSpace?): Pair<Long, Bitmap?> {
        return Pair(0, null)
    }

    override fun getNoEffectFrame(timeUs: Long, isHdrVideoOlive: Boolean): Bitmap? {
        return null
    }
}