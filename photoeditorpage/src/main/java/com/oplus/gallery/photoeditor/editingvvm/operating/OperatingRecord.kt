/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OperatingRecord
 ** Description:
 ** Version: 1.0
 ** Date : 2024/3/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.operating

import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.toRectF
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.effect.SupportEffectInfo
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_0
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_1
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_2
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_3
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_4
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_5
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord.Companion.PRIORITY_6
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord.Companion.CMD_WATERMARK_REMOVE
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_0
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_1
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_3
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_4
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_5
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_6
import kotlin.collections.reversed as ktReversed

/**
 * 操作记录，每个业务需要根据自身的业务特性，继承OperatingRecord实现属于自己的操作记录
 * 需要注意实现类的几个属性设置：
 *
 * [isModifiable] 是否可修改，主要用来判断当前业务操作属于单一特效、还是复合特效（Mark by wudanyang: 名字后面提交少的时候改一下）：
 * isModifiable = true时，对应单一特效：
 * 添加或插入时，只创建一个effectUsage；
 * 删除时，只删除一个effectUsage；
 * 撤销时，找到撤销record之前的同名record，修改effectUsage，找不到的话说明撤销的record已经是最后一条了，直接删除effectUsage；
 * 重做时，找到重做record之前的同名record，修改effectUsage，找不到的话说明重做的record是第一条，创建一个effectUsage；
 *
 * isModifiable = false时，对应多特效：
 * 添加或插入时，每次创建一个新的effectUsage，如果record中包含多个子record，每个子record创建一个effectUsage；
 * 删除时，每次移除一个effectUsage，如果record包含多个子record，逐一移除子record对应的effectUsage；
 * 撤销时，同删除；
 * 重做时，同添加；
 *
 * [isParametric] 是否参数化（和isModifiable没有必然关系！！！）：
 * isParametric = true时：
 * 目前对应特效：[AvEffect.FilterEffect]、[AvEffect.OliveEffect]、[AvEffect.WatermarkEffect]、[AvEffect.AdjustEffect]、[AvEffect.TRANSFORM_EFFECT]
 * 添加或插入时，找到之前最后一步非参数化record之后的同名record，修改effectUsage为新record，找不到的话说明是第一条，创建一个effectUsage；
 * 删除时，找到之前最后一步非参数化record之后的同名record，修改effectUsage为旧record，找不到的话说明已经是最后一条了，直接删除effectUsage；
 * 撤销时，同删除；
 * 重做时，同添加；
 *
 * isParametric = false时：
 * 对应非参数化特效：[AvEffect.BEAUTY_EFFECT]等
 * 和参数化的差异点主要在找lastRecord的逻辑上，只要找次级栈中最后一个活跃record
 *
 * [hasEffect] 该操作是否有实际的效果
 * 特例：[AvEffect.BEAUTY_EFFECT]中的人脸检测没有实际效果
 *
 * [isSameEffect] 是否相同特效，用于查找关联lastRecord作判断
 * 特例：[AvEffect.MosaicEffect]中的手动马赛克，down、move、up属于相同特效，但新的一组的down开始，不属于相同特效
 *
 * [plusAssign] 合并策略，两个record两两合并的策略，结合[ICombineStrategy]实现业务自身的合并策略
 *
 * [split] 拆分策略，对应合并策略，将record拆分成多个子record
 *
 * [argumentsForAlgo] 最终提供给算法的参数，需要业务过滤掉属于业务自身的arguments，只提供算法所必须的arguments
 */
open class OperatingRecord(
    /**
     * 特效名，必须定义在[AvEffect.name]
     */
    @Expose
    @SerializedName("effectName")
    val effectName: String,

    /**
     * 参数（包含业务UI需要的参数，和传给算法的参数）
     */
    @Expose
    @SerializedName("arguments")
    val arguments: ArrayMap<String, Any>,

    /**
     * 协议版本号
     */
    @Expose
    @SerializedName("version")
    val version: Float = 0f,

    /**
     * 当前record是否只是一个'空包装箱'?
     *
     *  即
     *   false: 常态, 正常的record: 自己以及自己的子record(若有)都正常对应effect;
     *   true: 自己只是一个'空包装箱子',
     *         通常被用来将多个字record合并成一个record(这样undo-redo的时候单步操作多个effect), 而自己是只是个'空箱子'.
     */
    val isJustPackingBox: Boolean = false
) {
    /**
     * 是否可修改，是则会基于原有记录修改effects，否则会新建effects
     * true的场景如：裁剪旋转、调节、美颜等单特效等
     * false的场景如：混合特效（贴纸、文字）、消除、马赛克等
     */
    open val isModifiable: Boolean = true

    /**
     * 是否参数化操作
     */
    open val isParametric: Boolean = false

    /**
     * Record可以包含子record.但是必须满足:
     * 1. list最后一个是当前的record自己;
     * 2. list的顺序保证是 数组下标从小到大 对应的 从旧到新的record
     */
    val operatingRecordList = mutableListOf<OperatingRecord>()

    /**
     * 合并，子类需要自己实现合并策略
     * @param operatingRecord 待合并的记录
     */
    open operator fun plusAssign(operatingRecord: OperatingRecord) {
        // 同名参数，新的覆盖旧的，默认逻辑，可自行实现合并规则
        arguments += operatingRecord.arguments
    }

    /**
     * Record可以包含子record.但是必须满足:
     * 1. list最后一个是当前的record自己;
     * 2. list的顺序保证是 数组下标从小到大 对应的 从旧到新的record
     */
    open fun split(): List<OperatingRecord> {
        // 这里需要业务record的key跟算法元数据的key一一对应
        return operatingRecordList + this@OperatingRecord
    }

    /**
     * 在执行 redo 的时候, 默认规则下 应该按照正序拿 operatingRecordList
     * -- 因为 operatingRecordList 按照 "数组下标从小到大 对应的 从旧到新的record" 编排
     */
    fun splitNormalOrRedo(): List<OperatingRecord> {
        return split()
    }

    /**
     * 在执行 undo 的时候, 默认规则下 应该按照逆序拿 operatingRecordList
     * -- 因为 operatingRecordList 按照 "数组下标从小到大 对应的 从旧到新的record" 编排
     */
    fun splitForUndo(): List<OperatingRecord> {
        return split().ktReversed()
    }

    /**
     * object类型的数据在json序列化和反序列化之后可能会出现类型不一致，这里统一进行调整
     */
    open fun standardize() {
        arguments.forEach { (key, value) ->
            if (value is Double) {
                arguments[key] = (value as Number).toFloat()
            }
        }
    }

    /**
     * 过滤出算法参数（UI参数不参与算法运行，只用于UI还原），同时拆分操作
     * @param algoArguments 算法支持参数
     * @return 实际参数
     */
    open fun argumentsForAlgo(algoArguments: List<AvEffect.Argument>? = null): Map<String, Any> {
        algoArguments ?: let {
            GLog.d(TAG, LogFlag.DL) { "[splitAndFilter] don't filter because algoArguments is null" }
            return arguments
        }
        if (algoArguments.isEmpty()) {
            GLog.d(TAG, LogFlag.DL) { "[splitAndFilter] don't filter because algoArguments is empty" }
            return arguments
        }
        return arguments.filter {
            algoArguments.any { arg ->
                (it.key == arg.handle) || (it.key == MEICAM_LICENCE_FILE_PATH) || (it.key == GLOBAL_KEY_EXECUTE_COMMAND)
            }
        }
    }

    /**
     * 该操作是否有效果，true时有效果、计入hasModified()，false时无效果，不计入hasModified()
     */
    open val hasEffect: Boolean = true

    /**
     * 是否为相同effect
     * @param record 对比的操作记录
     */
    open fun isSameEffect(record: OperatingRecord): Boolean {
        return effectName == record.effectName
    }

    /**
     * 把 arguments 里对应 key 的 value 从 List 转成 RectF
     */
    protected fun convertArgumentsValuesFromListToRect(arguments: MutableMap<String, Any>, key: String) {
        (arguments[key] as? ArrayList<Float>)?.let { arguments[key] = it.toRectF() }
    }

    /**
     * 检查操作记录和传入的本机支持效果是否匹配
     * @return 匹配返回 true，不匹配返回 false
     */
    open fun checkIfSubEffectSupport(info: SupportEffectInfo): Boolean = true

    companion object {
        const val TAG = "OperatingRecord"

        /**
         * 全局的对特效操作命令的Key
         */
        const val GLOBAL_KEY_EXECUTE_COMMAND = "global_key_execute_command"

        /**
         * key值 - 美摄SDK证书路径
         */
        const val MEICAM_LICENCE_FILE_PATH = "meicam_licence_file_path"

        /**
         * 算法调节项范围的最小值的索引位置
         */
        const val CONTINUOUS_INDEX_MIN = 0

        /**
         * 算法调节项范围的最大值的索引位置
         */
        const val CONTINUOUS_INDEX_MAX = 1

        /**
         * 算法调节项范围的默认值的索引位置
         */
        const val CONTINUOUS_INDEX_DEFAULT = 2

        /**
         * 融合算法命令
         */
        const val CMD_TEXTURE_BLEND = "cmd_texture_blend"

        /**
         * 融合算法key值
         */
        const val KEY_OVERLAY = "key_overlay"

        /**
         * overlay gain
         */
        const val KEY_OVERLAY_GAIN = "key_overlay_gain"

        /**
         * 融合算法的id唯一值
         *
         * 说明：
         * 正常情况下，传给算法的参数相同，得到的效果是相同的。所以管线是将传给算法的参数拼接后，获取哈希值来作为缓存图的key。
         * 但融合算法有点特殊，它传的参数主要是一个纹理id，但纹理id相同的时候，不一定图片就一定相同。（例：上次纹理释放掉了，下次重新生成纹理时）。
         * 这就导致无法区分每次操作记录的差异性。所以需要新增一个算法参数做区分，这里就用构建操作记录时的时间戳。
         */
        const val KEY_TEXTURE_BLEND_ID = "key_texture_blend_id"

        /**
         * 标记融合图会变淡，按照算法要求需要单独传的key值，value为true
         */
        const val KEY_PRE_MULTIPLY = "key_pre_multiply"

        /**
         * 混合的比例
         */
        const val KEY_MIX_RATIO = "key_mix_ratio"

        /**
         * 特效绘制是否需要缓存。
         *
         * 缓存是按照usage存储的，一个usage存储一份，如果usage一直在变动中，则无需存储，直到usage入栈才需要存储。
         * - 参数化的无需存储过程数据
         * - 非参数化，但是使用同一个usage不断编辑的，也无需存储变动中的缓存
         */
        const val KEY_SHOULD_CACHED_FOR_USER = "should_cached_for_user"

        /**
         * 记录进入滤镜界面时的类型
         */
        const val KEY_OLD_FILTER_TYPE = "key_old_filter_type"

        /**
         * 记录进入滤镜界面时的值
         */
        const val KEY_OLD_FILTER_VALUE = "key_old_filter_value"

        const val PRIORITY_0 = NUMBER_0
        const val PRIORITY_1 = NUMBER_1
        const val PRIORITY_2 = NUMBER_2
        const val PRIORITY_3 = NUMBER_3
        const val PRIORITY_4 = NUMBER_4
        const val PRIORITY_5 = NUMBER_5
        const val PRIORITY_6 = NUMBER_6
        const val PRIORITY_MAX = PRIORITY_6
    }
}

/**
 * 代表null对象的记录，目前只用于总线通知
 */
object NullRecord : OperatingRecord(TextUtil.EMPTY_STRING, arrayMapOf())

/**
 * 参数化编辑记录
 * @param cmd 操作名
 * @param arguments 操作参数
 */
open class ParametricRecord(
    cmd: String,
    arguments: ArrayMap<String, Any>,
    version: Float = 0f
) : OperatingRecord(cmd, arguments, version) {
    override val isParametric: Boolean = true
}

/**
 * 动态参数化编辑记录
 * 即动态决定是否参数化
 */
open class DynamicParametricRecord(
    effectName: String,
    arguments: ArrayMap<String, Any>,
    version: Float = 0f,
    isJustPackingBox: Boolean = false
) : OperatingRecord(effectName, arguments, version, isJustPackingBox)

/**
 * 参数化编辑的优先级
 * 水印虽然是参数化编辑项，但是不参与排序
 * 优先级高的先执行，数值越大优先级越高
 */
fun OperatingRecord.priority(isSupportParametric: Boolean = true): Int {
    return when (effectName) {
        AvEffect.WatermarkEffect.name -> {
            // 去除水印优先级仅次于换封面图，添加水印优先级最低
            if (arguments[OperatingRecord.GLOBAL_KEY_EXECUTE_COMMAND] == CMD_WATERMARK_REMOVE) {
                PRIORITY_4
            } else {
                PRIORITY_0
            }
        }

        AvEffect.TransformEffect.name -> {
            if (isSupportParametric) {
                PRIORITY_1
            } else {
                // 不支持项目化保存的场景不存在优先级，无需insert，直接定义为0
                PRIORITY_0
            }
        }

        AvEffect.AdjustEffect.name -> {
            if (isSupportParametric) {
                PRIORITY_2
            } else {
                // 不支持项目化保存的场景不存在优先级，无需insert，直接定义为0
                PRIORITY_0
            }
        }

        AvEffect.FilterEffect.name -> {
            if (isSupportParametric) {
                PRIORITY_3
            } else { // 不支持项目化保存的场景不存在优先级，无需insert，直接定义为0
                PRIORITY_0
            }
        }
        // 更换封面优先级最高
        AvEffect.OliveEffect.name -> PRIORITY_5
        AvEffect.ImageReplaceEffect.name -> PRIORITY_6
        else -> PRIORITY_0
    }
}

/**
 * 非参数化编辑记录
 * @param cmd 操作名
 * @param arguments 操作参数
 */
open class NonParametricRecord(
    cmd: String,
    arguments: ArrayMap<String, Any>
) : OperatingRecord(cmd, arguments) {

    override val isParametric: Boolean = false
}

/**
 * 二级页撤销回退需要入空栈（如文字编辑、标记等），为无参的空操作记录，且不走管线
 *
 * 场景：
 * 文字、标记二级页中，需要显示撤销恢复按钮。（如增加文字组件、移动文字组件、删除文字组件等二级页的内部操作）
 *
 * 说明：
 * 为了让撤销恢复按钮，能够正常显示，做了如下逻辑：
 * 1. 当业务内部逻辑的栈 push 的时候，同步给操作栈入个空栈。
 * 2. 当点击撤销恢复按钮时，则同步执行业务内部逻辑的栈的撤销恢复逻辑
 */
open class EmptyRecord(
    cmd: String
) : NonParametricRecord(
    cmd = cmd,
    arguments = arrayMapOf()
)

/**
 * 合并策略接口
 */
interface ICombineStrategy {

    /**
     * 两个操作合并为一个
     * @param oldRecord 旧操作
     * @param newRecord 新操作
     * @return 合并后的操作
     */
    fun combine(oldRecord: OperatingRecord, newRecord: OperatingRecord): OperatingRecord
}

/**
 * 合并策略：两个操作相加，旧+新，保留旧record
 */
class PlusAssignToFirst : ICombineStrategy {
    override fun combine(oldRecord: OperatingRecord, newRecord: OperatingRecord): OperatingRecord {
        oldRecord += newRecord
        return oldRecord
    }
}

/**
 * 合并策略：两个操作相加，新+旧，保留新record
 */
class PlusAssignToLast : ICombineStrategy {
    override fun combine(oldRecord: OperatingRecord, newRecord: OperatingRecord): OperatingRecord {
        newRecord += oldRecord
        return newRecord
    }
}

/**
 * 合并策略：只保留新的操作
 */
class PickLast : ICombineStrategy {
    override fun combine(oldRecord: OperatingRecord, newRecord: OperatingRecord): OperatingRecord {
        return newRecord
    }
}

/**
 * 转换器接口
 */
internal interface IValueConverter<T> {
    /**
     * 转换值
     * @param value 待转换的值
     * @return 转换后的值
     */
    fun convert(value: Any?): T?
}

/**
 * ArrayList<Double>转FloatArray的转换器
 */
internal class DoubleArrayListToFloatArrayConverter : IValueConverter<FloatArray> {
    override fun convert(value: Any?): FloatArray? {
        return (value as? ArrayList<*>)?.let {
            it.map { (it as Double).toFloat() }.toFloatArray()
        }
    }
}

/**
 * Float转Int的转换器
 */
internal class FloatToIntConverter : IValueConverter<Int> {
    override fun convert(value: Any?): Int? {
        return (value as? Float)?.toInt()
    }
}