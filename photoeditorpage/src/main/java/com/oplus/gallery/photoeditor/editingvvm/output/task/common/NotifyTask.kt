/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  NotifyTask.kt
 * * Description : notify task
 * * Version     : 1.0
 * * Date        : 2024/10/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.output.task.common

import android.net.Uri
import com.oplus.gallery.foundation.util.ext.parseId
import com.oplus.gallery.framework.abilities.notify.INotifyExternalAbility
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.output.OriginFileProcessType
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_ORIGIN_FILE_PROCESS_TYPE
import com.oplus.gallery.photoeditor.editingvvm.output.SaveData.Companion.KEY_RESULT_URI
import com.oplus.gallery.photoeditor.editingvvm.output.task.ISaveTask
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 通知文管更新快捷方式的task
 */
internal class NotifyTask(override val saveData: SaveData) : ISaveTask {
    override val name: String = "NotifyTask"

    override fun run() {
        val newUri = saveData.data[KEY_RESULT_URI] as? Uri
        val oldUri = (saveData.data[KEY_DATA_SOURCE] as? DataSource)?.imageUri
        val type = saveData.data[KEY_ORIGIN_FILE_PROCESS_TYPE]
        if ((oldUri != null) && (newUri != null) && (type != OriginFileProcessType.NONE)) {
            ContextGetter.context.getAppAbility<INotifyExternalAbility>()?.notifyMediaChange(
                hashMapOf(Pair(oldUri.parseId(), newUri.parseId()))
            )
        }
    }
}