/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - WatermarkCameraSection.kt
 ** Description: 相机进入相册编辑选择水印类型样式页面切片.
 ** Version: 1.0
 ** Date : 2024/8/26
 ** Author: <PERSON><PERSON>.<EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wen<PERSON>.Deng   2024/8/26      1.0        created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.watermarkCamera

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.KeyguardManager
import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Rect
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnPreDrawListener
import android.widget.CompoundButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import androidx.recyclerview.widget.GalleryLinearLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIInEaseInterpolator
import com.coui.appcompat.animation.COUIOutEaseInterpolator
import com.coui.appcompat.cardview.COUICardView
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.couiswitch.COUISwitch
import com.coui.appcompat.tooltips.COUIToolTips
import com.google.android.material.chip.ChipGroup
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.template.editor.EditorAnimationUtils
import com.oplus.gallery.business_lib.template.editor.EditorAppUiConfig
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter
import com.oplus.gallery.business_lib.template.editor.data.ItemStatus
import com.oplus.gallery.business_lib.template.editor.widget.EditorLinearListView
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.ScreenUtils
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_HASSEL_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl.Companion.LAST_SAVE_CAMERA_STYLE_ID_KEY
import com.oplus.gallery.framework.abilities.watermark.conifg.WatermarkStyleUserConfigLoaderImpl.Companion.LAST_SAVE_CAMERA_VIDEO_STYLE_ID_KEY
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle
import com.oplus.gallery.framework.abilities.watermark.util.WatermarkMasterInfoUtils
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.BaseEditingSection
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_IS_STYLE_INVALID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_LOAD_RESTRICT_STYLE_LIST
import com.oplus.gallery.photoeditor.editingvvm.TopicID.WatermarkMaster.REPLY_WATERMARK_MASTER_UPDATE_OPERATOR_BY_STYLE_ID
import com.oplus.gallery.photoeditor.editingvvm.UnitNotifier
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.InputArgumentsVM
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.subscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeR
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.VIDEO_WATERMARK_COMMON_TYPE
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.VIDEO_WATERMARK_HASSEL_TEXT_TYPE
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.WATERMARK_COMMON_TYPE
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.WATERMARK_FRAME_TYPE
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.WATERMARK_HASSEL_TEXT_TYPE
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.WATERMARK_HASSEL_TYPE
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.WATERMARK_TEXT_SCOPE_ALL
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.WATERMARK_TEXT_SCOPE_PHOTO
import com.oplus.gallery.photoeditor.editingvvm.watermarkCamera.CameraWatermarkSettingFields.Companion.WATERMARK_TEXT_SCOPE_VIDEO
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterTopic
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterUIBean
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterUiBeanId
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM.Companion.CAMERA_WATERMARK_PHOTO_STYLE
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.WatermarkMasterVM.Companion.CAMERA_WATERMARK_VIDEO_STYLE
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.ItemMaskClickListener
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.adapter.WatermarkMasterStyleAdapter
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.WatermarkStyleItemViewData
import com.oplus.gallery.photoeditor.editingvvm.watermarkmaster.data.WatermarkTypeGroupData
import com.oplus.gallery.photoeditor.widget.CameraListNestedScrollView
import com.oplus.gallery.photoeditor.widget.CameraNestedRecyclerView
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2
import com.oplus.gallery.standard_lib.ui.util.UIConfigUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter

/**
 * 相机进入相册编辑选择水印类型样式页面切片
 */
internal class WatermarkCameraSection(
    sectionBus: ISectionBus<EditingFragment, EditingVM>
) : BaseEditingSection(sectionBus) {
    // View 相关
    private var cameraLayout: ConstraintLayout? = null
    private var backBtn: ImageView? = null
    private var photoWatermarkChip: COUIChip? = null
    private var videoWatermarkChip: COUIChip? = null
    private var watermarkChipGroup: ChipGroup? = null
    private var cameraWatermarkSwitch: COUISwitch? = null

    /**
     * 是否是页面初始化时的按钮切换
     */
    private var initSwitchCheck = true
    private var cameraSwitchText: TextView? = null
    private var typeListMask: View? = null
    private var scrollView: CameraListNestedScrollView? = null
    private var tipsIcon: ImageView? = null
    private var toolTips: COUIToolTips? = null
    private val portraitMargin by lazy {
        resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_title_margin_horizontal) ?: 0
    }
    private val mediumMargin by lazy {
        resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_title_margin_horizontal_medium) ?: 0
    }
    private val largeMargin by lazy {
        resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_title_margin_horizontal_large) ?: 0
    }
    private val landMargin by lazy {
        resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_style_list_item_padding) ?: 0
    }
    private val edge by lazy {
        resources?.getDimension(R.dimen.picture3d_editor_watermark_camera_switch_margin_horizontal_edge) ?: 0F
    }
    private val gap by lazy {
        resources?.getDimension(R.dimen.picture3d_editor_watermark_camera_switch_margin_horizontal_gap) ?: 0F
    }
    private val horizontalMarginItemDecoration by lazy {
        object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                super.getItemOffsets(outRect, view, parent, state)
                val position = parent.getChildAdapterPosition(view)
                val itemCount = parent.adapter?.itemCount ?: return
                if ((position == 0) || (position == (itemCount - 1))) {
                    if ((context?.let { ScreenUtils.isMiddleScreenWidthAndHeight(it) } ?: false).not()
                        && (context?.let { ScreenUtils.isLargeScreen(it) } ?: false).not()
                        && EditorUIConfig.isEditorLandscape(lastAppUiConfig)) {
                        outRect.left = 0
                        outRect.right = 0
                        return
                    }
                    val horizontalMargin = when (getConfigMode(lastAppUiConfig)) {
                        LARGE_MODE -> {
                            // 大屏，左右item边距40dp,边距36+4dp选中框
                            resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_type_list_padding_horizontal_large)
                        }

                        PORTRAIT_MODE -> {
                            // 小屏左右边距16dp，边距12+4dp选中框
                            resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_type_list_padding_horizontal)
                        }

                        MEDIUM_MODE -> {
                            // 中屏，左右边距24dp，边距20+4dp选中框
                            resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_type_list_padding_horizontal_medium)
                        }

                        else -> {
                            //默认16dp，边距12+4dp选中框
                            resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_type_list_padding_horizontal)
                        }
                    } ?: return
                    val isLTR = resources?.configuration?.layoutDirection == View.LAYOUT_DIRECTION_LTR
                    var startMargin = if (isLTR) outRect.left else outRect.right
                    var endMargin = if (isLTR) outRect.right else outRect.left
                    if (position == 0) {
                        // 开头item，从左至右加左边距，从右至左加右边距
                        startMargin = horizontalMargin
                    }
                    if (position == itemCount - 1) {
                        // 末尾item，从左至右加右边距，从右至左加左边距
                        endMargin = horizontalMargin
                    }

                    if (isLTR) {
                        outRect.left = startMargin
                        outRect.right = endMargin
                    } else {
                        outRect.right = startMargin
                        outRect.left = endMargin
                    }
                }
            }
        }
    }

    /**
     * 哈苏水印
     */
    private var hasselLayout: LinearLayout? = null
    private var hasselListView: CameraNestedRecyclerView? = null
    private var hasselStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * 个性画框 - 摄影语录系列
     */
    private var masterSignLayout: LinearLayout? = null
    private var masterSignListView: CameraNestedRecyclerView? = null
    private var masterSignStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * 个性画框 - 复古相机系列
     */
    private var retroCameraLayout: LinearLayout? = null
    private var retroCameraListView: CameraNestedRecyclerView? = null
    private var retroCameraStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * 个性画框 - 胶卷系列
     */
    private var filmLayout: LinearLayout? = null
    private var filmListView: CameraNestedRecyclerView? = null
    private var filmStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * 个性画框 - 品牌系列
     */
    private var brandLayout: LinearLayout? = null
    private var brandListView: CameraNestedRecyclerView? = null
    private var brandStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * realme品牌印记 - 真我时刻
     */
    private var realmeBrandLayout: LinearLayout? = null
    private var realmeBrandListView: CameraNestedRecyclerView? = null
    private var realmeBrandStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * realme品牌印记 - 灵感影像
     */
    private var inspirationLayout: LinearLayout? = null
    private var inspirationListView: CameraNestedRecyclerView? = null
    private var inspirationStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * realme品牌印记 - 独家记忆
     */
    private var memoryLayout: LinearLayout? = null
    private var memoryListView: CameraNestedRecyclerView? = null
    private var memoryStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * 文字水印
     */
    private var textLayout: LinearLayout? = null
    private var textListView: CameraNestedRecyclerView? = null
    private var textStyleAdapter: WatermarkMasterStyleAdapter? = null

    /**
     * 限定水印系列适配器列表
     * */
    private val restrictStyleAdapterList: MutableList<Pair<String?, WatermarkMasterStyleAdapter?>> = mutableListOf()

    /**
     * 限定水印系列分组数据列表
     * */
    private val typeList = mutableListOf<WatermarkTypeGroupData>()

    /**
     * 滚动列表container view
     * */
    private var scrollContainer: LinearLayout? = null

    /**
     * 样式分组view
     * */
    private var styleGroupViewList = mutableListOf<View>()

    /**
     * 限定样式分组列表
     * */
    private var styleGroupList = mutableListOf<CameraNestedRecyclerView>()

    /**
     * 相机是否支持限定水印，应该依据相机的meta-data：ai.master.watermark.version来
     * 版本管理链接：https://odocs.myoas.com/sheets/Ee32M21bMLIP2jA2/MODOC
     */
    private var isSupportRestrict = false

    private var currentPhotoStyle: String? = null
    private var currentVideoStyle: String? = null

    // 记录开关关闭前的样式
    private var lastPhotoStyle: String? = null
    private var lastVideoStyle: String? = null

    // 记录上次点击下载的样式。下载完成后，需要切换到最后一次点击下载的样式上
    private var lastClickDownloadStyleId: String? = null

    private var lastAppUiConfig: AppUiResponder.AppUiConfig? = null

    // 发布通道
    private var watermarkUiBeanNtf: UnitNotifier? = null
    private var styleViewDataNtf: NotifierChannel<MutableList<WatermarkStyleItemViewData>>? = null
    private var cameraAutoTestPathNtf: NotifierChannel<Unit>? = null

    // 获取值
    private var cameraWatermarkSetting: MutableMap<String, Any?>? = null

    //是否首次进入WatermarkCameraSection页面，首次进入和从下一级页面返回页面元素动效不一样
    private var firstTimeEntering: Boolean = false
    private var enteringPersonalizedSection: Boolean = false

    private var alphaAnimator: ObjectAnimator? = null
    private var translateAnimator: ObjectAnimator? = null
    private var animatorSet: AnimatorSet? = null

    private var cameraSwitchCardView: COUICardView? = null
    private var cameraStyleItemTitleImage: ImageView? = null
    private var cameraFrameStyleTitle: TextView? = null
    private var cameraSeriesMasterSignTitle: TextView? = null
    private var cameraSeriesRetroCameraTitle: TextView? = null
    private var cameraSeriesFilmTitle: TextView? = null
    private var cameraSeriesBrandTitle: TextView? = null
    private var cameraTextTitle: TextView? = null
    private var cameraRestrictTitle: TextView? = null

    /**
     * 页面是否正在退出中
     */
    @Volatile
    private var isClosing = false

    // 监听器
    private val onBackBtnClickListener = View.OnClickListener {
        closeCameraSection()
    }

    // feature判断
    private val isSupportHasselWatermark: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_HASSEL_WATERMARK, defValue = false, expDefValue = false)
    }

    private val cameraIpuWatermarkMasterVersion: Int by lazy {
        WatermarkMasterInfoUtils.CAMERA_IPU_WATERMARK_MASTER_VERSION
    }

    private val isRealmeBrand: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND, defValue = false, expDefValue = false)
    }

    private var watermarkUiBeanObserver: TObserver<WatermarkMasterUIBean> = {
        when (it.id) {
            WatermarkMasterUiBeanId.INIT_DATA -> initRestrictData(it)
            WatermarkMasterUiBeanId.PERSONALIZED_EDIT_DONE -> onPersonalizedEditDone(it.applyStyleId)
            WatermarkMasterUiBeanId.UPDATE_STYLE_ITEM_STATUS -> {
                it.watermarkStyleId?.also { styleId ->
                    updateStyleItemStatus(styleId, it.watermarkStyleStatus)
                }
            }

            WatermarkMasterUiBeanId.UPDATE_STYLE_ITEM_LOADING_PROGRESS -> {
                it.watermarkStyleId?.also { styleId ->
                    updateStyleItemLoadingProgress(styleId, it.watermarkStyleProgress)
                }
            }

            WatermarkMasterUiBeanId.RESTRICT_TYPE_VISIBILITY_CHANGED -> onRestrictTypeVisibilityChanged(it.needShowRestrictType)
            WatermarkMasterUiBeanId.REFRESH_RESTRICT_WATERMARK_STYLE -> refreshRestrictWatermarkStyle(it.watermarkStyleListViewData)
            else -> Unit
        }
    }

    /**
     * 初始化限定水印分组数据
     * */
    private fun initRestrictData(bean: WatermarkMasterUIBean) {
        val types = bean.watermarkTypeListViewData ?: return
        val styles = bean.watermarkStyleListViewData ?: return
        val groups = styles.groupBy { it.seriesId }
        typeList.clear()
        for (type in types) {
            val buttonInfo = type.buttonInfo?.takeIf { it.seriesId.isNotEmpty() } ?: continue
            val styleList = groups[buttonInfo.seriesId] ?: continue

            typeList.add(
                WatermarkTypeGroupData(
                    buttonInfo.seriesId,
                    buttonInfo.buttonId,
                    type.text,
                    styleList
                )
            )
        }
        initRestrictStyleLayout()
    }

    private val itemMaskClickListener = object : ItemMaskClickListener {
        override fun onItemMaskClick(position: Int, item: WatermarkStyleItemViewData) {
            if (isClosing) {
                GLog.w(TAG, LogFlag.DF) { "onItemMaskClick isClosing $isClosing，return" }
                return
            }
            if (item.status == ItemStatus.DEFAULT) {
                // DEFAULT场景响应onItemClick即可
                GLog.w(TAG, LogFlag.DL, "[onItemMaskClick] item.status is DEFAULT no need response, return")
                return
            }

            if (item.status == ItemStatus.READY_LOAD) {
                lastClickDownloadStyleId = item.styleId
            }

            if (TextUtils.isEmpty(item.styleId).not() && (item.status == ItemStatus.DEFAULT)) {
                enteringPersonalizedSection = true
            }
            watermarkUiBeanNtf?.notify(
                WatermarkMasterUIBean(
                    id = WatermarkMasterUiBeanId.ON_WATERMARK_STYLE_MASK_CLICK,
                    watermarkStyleId = item.styleId,
                    watermarkStyleStatus = item.status
                )
            )
        }
    }

    override fun onCreate() {
        super.onCreate()
        val keyguardManager = activity.getSystemService(Context.KEYGUARD_SERVICE) as? KeyguardManager
        val isDisplayOnLock = keyguardManager?.isKeyguardLocked ?: false
        if (isDisplayOnLock) {
            activity.setShowWhenLocked(true)
        }
        firstTimeEntering = true
        lastAppUiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
        registerAndSubscribeTopic()
        initView()
        setMarginsBasedOnMode()
        initCameraWatermarkSetting()
        adaptSysNaviBar()
    }

    override fun isAnimating(): Boolean {
        return animatorSet?.isRunning ?: false
    }

    override fun waitUntilAnimStop(func: () -> Unit) {
        animatorSet?.addListener(onEnd = { func.invoke() })
    }

    override fun onDestroy() {
        unsubscribeAndUnregisterTopic()
        /**
         * onDestroy和removeExtraViewFromBaseUIView注释掉
         * 主要解决退出水印设置页返回相机时，view会被马上移除，没有位移动画出现闪黑问题
         */
        /*super.onDestroy()
        cameraLayout?.also { removeExtraViewFromBaseUIView(it) }*/
    }

    override fun getContainerViewID(): Int = R.id.photo_editor_ui_framework

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int = PORTRAIT_LAYOUT_ID

    override fun onUiConfigChanged(baseActivity: BaseActivity, layoutId: Int, config: EditorAppUiConfig) {
        super.onUiConfigChanged(baseActivity, layoutId, config)
        val currentAppUiConfig = config.appUiConfig
        lastAppUiConfig?.let {
            if (currentAppUiConfig.isChanged(it)) {
                GLog.d(TAG, LogFlag.DL) { "[onUiConfigChanged]: change config" }
                reInitView(layoutId)
            }
        }
        lastAppUiConfig = currentAppUiConfig
        setMarginsBasedOnMode()
        adaptSysNaviBar()
    }

    override fun onBackPressed(isBackHandled: Boolean): Boolean {
        closeCameraSection()
        return true
    }

    private fun initView() {
        cameraLayout = sectionContainerView?.findViewById<ConstraintLayout>(R.id.camera_layout)?.apply {
            backBtn = findViewById<ImageView?>(R.id.watermark_camera_back_btn).apply {
                setOnClickListener(onBackBtnClickListener)
            }
            cameraSwitchText = findViewById(R.id.watermark_camera_switch_tv)
            watermarkChipGroup = findViewById(R.id.camera_chip_group)
            photoWatermarkChip = findViewById<COUIChip?>(R.id.watermark_camera_pic_chip).apply {
                setOnClickListener { v -> watermarkChipGroup?.check(v.id) }
                setOnCheckedChangeListener(OnPhotoChipCheckedListener())
            }
            videoWatermarkChip = findViewById<COUIChip?>(R.id.watermark_camera_video_chip).apply {
                setOnClickListener { v -> watermarkChipGroup?.check(v.id) }
                setOnCheckedChangeListener(OnVideoChipCheckedListener())
            }
            cameraWatermarkSwitch = findViewById<COUISwitch>(R.id.watermark_camera_switch).apply {
                setOnCheckedChangeListener(OnWatermarkSwitchCheckedChange())
            }
            if (isSupportHasselWatermark.not()) {
                val theme = cameraWatermarkSwitch?.context?.theme
                theme?.applyStyle(R.style.WatermarkCameraSwitchThemeNoHassel, true)
            } else {
                val theme = cameraWatermarkSwitch?.context?.theme
                theme?.applyStyle(R.style.WatermarkCameraSwitchTheme, true)
            }
            cameraWatermarkSwitch?.refresh()
            typeListMask = findViewById<View?>(R.id.watermark_camera_type_list_mask).apply {
                setOnClickListener {}
            }
            scrollView = findViewById(R.id.sv_style_list)
            tipsIcon = findViewById<ImageView>(R.id.title_right_icon).apply {
                if (isRealmeBrand) {
                    visibility = View.GONE
                }
                setOnClickListener { view ->
                    toolTips?.showWithDirection(view, COUIToolTips.ALIGN_BOTTOM) ?: kotlin.run {
                        /**
                         * COUIToolTips暗色只能通过切换暗色模式来控制，要保持COUIToolTips为暗色，又不影响context的模式，
                         * 这里先创建临时的context和Configuration，强制设置为暗色主题，供COUIToolTips初始化
                         */
                        val tempConfig = Configuration(context.resources.configuration).apply {
                            uiMode = (uiMode and Configuration.UI_MODE_NIGHT_MASK.inv()) or Configuration.UI_MODE_NIGHT_YES
                        }
                        val tempContext = context.createConfigurationContext(tempConfig).apply { setTheme(R.style.WatermarkCameraCOUITipsTheme) }
                        toolTips = COUIToolTips(tempContext, COUIToolTips.MODE_INFO).apply {
                            setDismissOnTouchOutside(true)
                            videoWatermarkChip?.let {
                                if (it.isChecked) {
                                    setContent(
                                        resources.getString(
                                            R.string.picture3d_editor_personalise_not_support_watermark_hdr_60_frames_and_above
                                        )
                                    )
                                }
                            }
                            photoWatermarkChip?.let {
                                if (it.isChecked) {
                                    if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN, false, false)) {
                                        setContent(
                                            context.resources.getString(
                                                R.string.picture3d_editor_personalise_not_support_watermark
                                            )
                                        )
                                    } else {
                                        setContent(
                                            context.resources.getString(
                                                R.string.picture3d_editor_personalise_not_support_watermark_not_domestic
                                            )
                                        )
                                    }
                                }
                            }
                            showWithDirection(view, COUIToolTips.ALIGN_BOTTOM)
                        }
                    }
                }
            }
        }
        watermarkUiBeanNtf?.notify(WatermarkMasterUIBean(id = WatermarkMasterUiBeanId.INIT_DATA))
    }

    private fun setTipsContent(context: Context, isPhotoTab: Boolean) {
        toolTips?.let {
            if (isPhotoTab) {
                if (ConfigAbilityWrapper.getBoolean(IS_REGION_CN, false, false)) {
                    it.setContent(context.resources.getString(R.string.picture3d_editor_personalise_not_support_watermark))
                } else {
                    it.setContent(context.resources.getString(R.string.picture3d_editor_personalise_not_support_watermark_not_domestic))
                }
            } else {
                it.setContent(context.resources.getString(R.string.picture3d_editor_personalise_not_support_watermark_hdr_60_frames_and_above))
            }
        }
    }

    private fun initCameraWatermarkSetting() {
        cameraWatermarkSetting?.let { cameraSetting ->
            val commonWatermarkOpen = cameraSetting.getOrDefault(InputArgumentsVM.KEY_CAMERA_COMMON_WATERMARK_OPEN, false)
            val frameWatermarkOpen = cameraSetting.getOrDefault(InputArgumentsVM.KEY_CAMERA_FRAME_WATERMARK_OPEN, false)
            val isCapturePhoto = cameraSetting.getOrDefault(InputArgumentsVM.KEY_CAMERA_WATERMARK_CAPTURE_MODE, true)

            /**
             * 相机是否支持限定水印直出，需要判断相机meta中水印版本号是否大于等于2
             */
            isSupportRestrict = (cameraIpuWatermarkMasterVersion > NUMBER_2)
            val textScope =
                cameraSetting.getOrDefault(InputArgumentsVM.KEY_CAMERA_COMMON_WATERMARK_TEXT_SCOPE, WATERMARK_TEXT_SCOPE_PHOTO) as? String?
            val cameraPhotoStyleId =
                (cameraSetting.getOrDefault(CAMERA_WATERMARK_PHOTO_STYLE, null) as? String?)?.takeIf { it.isNullOrBlank().not() }
            val cameraVideoStyleId =
                (cameraSetting.getOrDefault(CAMERA_WATERMARK_VIDEO_STYLE, null) as? String?)?.takeIf { it.isNullOrBlank().not() }
            val cameraAutoTestPath = cameraSetting.getOrDefault(InputArgumentsVM.AI_MASTER_WATERMARK_AUTO_TEST_RES_PATH, null)
            if (cameraAutoTestPath != null) {
                isSupportRestrict = true
                cameraAutoTestPathNtf?.notify(cameraAutoTestPath)
            }
            GLog.d(TAG, LogFlag.DL) {
                "[initCameraWatermarkSetting] cameraSetting: ($commonWatermarkOpen, $frameWatermarkOpen," +
                        "isCapturePhoto:$isCapturePhoto, cameraIpuWatermarkMasterVersion:${cameraIpuWatermarkMasterVersion}, " +
                        "isSupportRestrict:$isSupportRestrict, textScope:$textScope, cameraPhotoStyleId:$cameraPhotoStyleId, " +
                        "cameraVideoStyleId:$cameraVideoStyleId, cameraAutoTestPath:$cameraAutoTestPath)"
            }
            val oldWatermarkType = if (commonWatermarkOpen == true) {
                // 老哈苏机型通用水印对应 hassel_text_style_2，非哈苏机型通用水印对应text_style_2
                if (isSupportHasselWatermark) WATERMARK_HASSEL_TEXT_TYPE else WATERMARK_COMMON_TYPE
            } else if (frameWatermarkOpen == true) {
                // 老哈苏机型哈苏水印对应 hassel_style_1，非哈苏机型画框水印默认选中personalize_masterSign_1
                if (isSupportHasselWatermark) WATERMARK_HASSEL_TYPE else WATERMARK_FRAME_TYPE
            } else null
            //从相机设置页进入相册的，chip的选中只依赖当前拍照类型是照片或者视频，不依赖样式
            if (isCapturePhoto == true) {
                photoWatermarkChip?.isChecked = true
            } else {
                videoWatermarkChip?.isChecked = true
            }
            oldWatermarkType?.let {
                compatibleWithOldWatermark(textScope, it)
                notifyBroadcastCameraSettings()
            } ?: run {
                updateWatermarkSelection(cameraPhotoStyleId, cameraVideoStyleId)
            }
            initSwitchCheck = false
        }
    }

    private fun compatibleWithOldWatermark(textScope: String?, oldWatermarkType: String) {
        when (textScope) {
            WATERMARK_TEXT_SCOPE_ALL -> {
                // 老版本相机，使用于图片和视频，默认选中图片chip，打开开关
                currentPhotoStyle = oldWatermarkType
                currentVideoStyle = oldWatermarkType
                cameraWatermarkSwitch?.isChecked = photoWatermarkChip?.isChecked == true
            }

            WATERMARK_TEXT_SCOPE_VIDEO -> {
                // 老版本相机仅使用于视频
                currentVideoStyle = oldWatermarkType
                cameraWatermarkSwitch?.isChecked = videoWatermarkChip?.isChecked == true
            }

            else -> {
                // 老版本相机仅使用于图片,或没有应用类型，默认选中照片水印
                currentPhotoStyle = oldWatermarkType
                cameraWatermarkSwitch?.isChecked = photoWatermarkChip?.isChecked == true
            }
        }
    }

    /**
     * 检查并返回有效的水印id
     *
     * @param isVideo 是否为视频水印
     * @param styleId 水印样式id
     *
     * @return first-返回的水印是否有变更，second-返回有效的水印样式id
     */
    private fun checkAndReturnValidStyleId(isVideo: Boolean, styleId: String?): Pair<Boolean, String?> {
        if (styleId.isNullOrBlank()) return Pair(false, null)

        // 判断是否已超期水印
        val isStyleInvalid = vBus.notifyOnce<Boolean>(REPLY_WATERMARK_MASTER_IS_STYLE_INVALID, styleId)
        if (isStyleInvalid != true) return Pair(false, styleId)

        // Bug-8422968，已和产品对齐，如果水印超期，使用首个默认水印
        val newStyleId = if (isVideo) {
            if (isSupportHasselWatermark) VIDEO_WATERMARK_HASSEL_TEXT_TYPE else VIDEO_WATERMARK_COMMON_TYPE
        } else {
            if (isSupportHasselWatermark) WATERMARK_HASSEL_TYPE else WATERMARK_FRAME_TYPE
        }
        return Pair(true, newStyleId)
    }

    private fun updateWatermarkSelection(cameraPhotoStyleId: String?, cameraVideoStyleId: String?) {
        // 老版本水印类型不存在，使用大师水印styleId
        val (isPhotoChanged, photoStyleId) = checkAndReturnValidStyleId(false, cameraPhotoStyleId)
        val (isVideoChanged, videoStyleId) = checkAndReturnValidStyleId(true, cameraVideoStyleId)
        currentPhotoStyle = photoStyleId
        currentVideoStyle = videoStyleId
        photoStyleId?.let {
            vBus.notifyOnce(REPLY_WATERMARK_MASTER_UPDATE_OPERATOR_BY_STYLE_ID, it)
            if (isPhotoChanged) selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = true)
        }
        videoStyleId?.let {
            vBus.notifyOnce(REPLY_WATERMARK_MASTER_UPDATE_OPERATOR_BY_STYLE_ID, it)
            if (isVideoChanged) selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = true)
        }
        // 1，图片水印类型不为null,照片chip是选中状态，打开开关 2，视频水印类型不为null，视频chip是选中状态，打开开关
        if (photoWatermarkChip?.isChecked == true) {
            cameraWatermarkSwitch?.isChecked = photoStyleId.isNullOrBlank().not()
        } else {
            cameraWatermarkSwitch?.isChecked = videoStyleId.isNullOrBlank().not()
        }
        if (photoStyleId.isNullOrBlank().not()) {
            if (videoStyleId.isNullOrBlank()) {
                queryLastStyle(true)
            }
        } else if (videoStyleId.isNullOrBlank().not()) {
            if (photoStyleId.isNullOrBlank()) {
                queryLastStyle(false)
            }
        } else {
            // 没有水印类型，默认选中图片chip，开关关闭, 选中上次记忆的样式
            currentPhotoStyle = null
            currentVideoStyle = null
            queryLastStyle(true)
            queryLastStyle(false)
        }
    }

    /**
     * 查询上次的水印样式
     *
     * @param isVideo 是否查询视频水印
     */
    private fun queryLastStyle(isVideo: Boolean) {
        val styleIdKey = if (isVideo) LAST_SAVE_CAMERA_VIDEO_STYLE_ID_KEY else LAST_SAVE_CAMERA_STYLE_ID_KEY
        val queryStyleId = WatermarkStyleUserConfigLoaderImpl.getInstance().queryUserConfig(styleIdKey, true)?.styleId
        val (isChanged, styleId) = checkAndReturnValidStyleId(isVideo, queryStyleId)
        styleId?.also {
            if (isVideo) {
                lastVideoStyle = it
            } else {
                lastPhotoStyle = it
            }
            vBus.notifyOnce(REPLY_WATERMARK_MASTER_UPDATE_OPERATOR_BY_STYLE_ID, it)
            selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = isChanged)
        }
    }

    private fun reInitView(layoutId: Int) {
        val rePhotoSelected = photoWatermarkChip?.isChecked == true
        val reVideoSelected = videoWatermarkChip?.isChecked == true
        sectionContentView?.let {
            removeExtraViewFromBaseUIView(it)
            sectionContentView = null
        }
        sectionContentView = (editorUIExecutor.getLayoutFromLayoutCache(layoutId) ?: let {
            layoutInflater.inflate(layoutId, sectionContainerView, false)
        }) as? ConstraintLayout
        sectionContentView?.let { addExtraViewToBaseUIView(it) }
        initView()
        clearSelectedItem()
        // 横竖屏切换页面重新init后，adapter重置了，开关没有打开的情况下不会选中上次选中样式
        if (rePhotoSelected) {
            val selectStyle = currentPhotoStyle ?: lastPhotoStyle
            selectStyle?.let { selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = false) }
        }
        if (reVideoSelected) {
            val selectStyle = currentVideoStyle ?: lastVideoStyle
            selectStyle?.let { selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = false) }
        }
        photoWatermarkChip?.isChecked = rePhotoSelected
        videoWatermarkChip?.isChecked = reVideoSelected
    }

    private fun adaptSysNaviBar() {
        activity.setStatusBarAppearance(isLight = false)
        activity.setNaviBarColor(Color.TRANSPARENT)
        val rightNavBarH = sectionBus.hostInstance.rightNaviBarHeight(false)
        val leftNavBarH = sectionBus.hostInstance.leftNaviBarHeight(false)
        (cameraLayout?.layoutParams as? ViewGroup.MarginLayoutParams)?.let {
            it.leftMargin = leftNavBarH
            it.rightMargin = rightNavBarH
            cameraLayout?.layoutParams = it
        }
    }

    private fun registerAndSubscribeTopic() {
        vBus.apply {
            cameraWatermarkSetting = vBus.get<MutableMap<String, Any?>>(TopicID.InputArguments.TOPIC_CAMERA_WATERMARK_SETTING)
            watermarkUiBeanNtf = subscribeDuplexT(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN, watermarkUiBeanObserver)
            styleViewDataNtf = vBus.subscribeR(WatermarkMasterTopic.TOPIC_STYLE_VIEW_DATA)
            cameraAutoTestPathNtf = subscribeR(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_CAMERA_AUTO_TEST)
        }
    }

    private fun unsubscribeAndUnregisterTopic() {
        vBus.apply {
            unsubscribeDuplexT(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_UI_BEAN, watermarkUiBeanNtf, watermarkUiBeanObserver)
            unsubscribeR(WatermarkMasterTopic.TOPIC_STYLE_VIEW_DATA, styleViewDataNtf)
            unsubscribeR(TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_CAMERA_AUTO_TEST, cameraAutoTestPathNtf)
        }
    }

    inner class OnWatermarkSwitchCheckedChange : CompoundButton.OnCheckedChangeListener {
        override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
            if (isChecked) {
                typeListMask?.visibility = View.GONE
                if (photoWatermarkChip?.isChecked == true) {
                    val photoStyle = currentPhotoStyle ?: lastPhotoStyle
                    // 此为超期限时水印时，就会为true。需要切换到默认样式
                    val isStyleInvalid = photoStyle?.let {
                        vBus.notifyOnce<Boolean>(REPLY_WATERMARK_MASTER_IS_STYLE_INVALID, it) == true
                    } == true
                    val selectedStyle = if (photoStyle.isNullOrBlank() || isStyleInvalid) {
                        // 老哈苏机型哈苏水印对应 hassel_style_1，非哈苏机型画框水印默认选中personalize_masterSign_1
                        if (isSupportHasselWatermark) WATERMARK_HASSEL_TYPE else WATERMARK_FRAME_TYPE
                    } else photoStyle
                    currentPhotoStyle = selectedStyle
                    selectedStyleItem(
                        styleId = selectedStyle,
                        shouldScrollToPosition = true,
                        notifyBroadcast = initSwitchCheck.not() || isStyleInvalid
                    )
                    currentPhotoStyle?.let {
                        WatermarkStyleUserConfigLoaderImpl.getInstance().saveUserConfig(it, true)
                    }
                } else if (videoWatermarkChip?.isChecked == true) {
                    val videoStyle = currentVideoStyle ?: lastVideoStyle
                    // 此为超期限时水印时，就会为true。需要切换到默认样式
                    val isStyleInvalid = videoStyle?.let {
                        vBus.notifyOnce<Boolean>(REPLY_WATERMARK_MASTER_IS_STYLE_INVALID, it) == true
                    } == true
                    val selectedStyle = if (videoStyle.isNullOrBlank() || isStyleInvalid) {
                        // 老哈苏机型视频哈苏水印对应 video_hassel_text_style_2，非哈苏机型视频水印默认选中 video_text_style_2
                        if (isSupportHasselWatermark) VIDEO_WATERMARK_HASSEL_TEXT_TYPE else VIDEO_WATERMARK_COMMON_TYPE
                    } else videoStyle
                    currentVideoStyle = selectedStyle
                    selectedStyleItem(
                        selectedStyle,
                        shouldScrollToPosition = true,
                        notifyBroadcast = initSwitchCheck.not() || isStyleInvalid
                    )
                    currentVideoStyle?.let {
                        WatermarkStyleUserConfigLoaderImpl.getInstance().saveUserConfig(it, true)
                    }
                }
            } else {
                if (photoWatermarkChip?.isChecked == true) {
                    if (currentPhotoStyle.isNullOrBlank()) {
                        // 开关关闭，当前没有选中项，说明来自chip切换，需要将上一次选中的项置为选中状态
                        lastPhotoStyle?.let { selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = false) }
                    } else {
                        // 当前有选中项，说明主动关闭开关，需要将当前选中项置为未选中状态，并记录上一次选中项
                        lastPhotoStyle = currentPhotoStyle
                        currentPhotoStyle = null
                    }
                }
                if (videoWatermarkChip?.isChecked == true) {
                    if (currentVideoStyle.isNullOrBlank()) {
                        // 当前没有选中项，说明来自chip切换，需要将上一次选中的项置为选中状态
                        lastVideoStyle?.let { selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = false) }
                    } else {
                        // 当前有选中项，说明主动关闭开关，需要将当前选中项置为未选中状态，并记录上一次选中项
                        lastVideoStyle = currentVideoStyle
                        currentVideoStyle = null
                    }
                }
                if (initSwitchCheck.not()) {
                    notifyBroadcastCameraSettings()
                }
                typeListMask?.visibility = View.VISIBLE
            }
        }
    }

    private fun updateStyleItemStatus(styleId: String, status: ItemStatus) {
        // 水印素材下载完成后，如果是最后一次点击选中项，执行选中逻辑
        if ((status == ItemStatus.DEFAULT) && (lastClickDownloadStyleId != null) && (styleId == lastClickDownloadStyleId)) {
            clearSelectedItem()
            if (photoWatermarkChip?.isChecked == true) {
                currentPhotoStyle = styleId
            } else if (videoWatermarkChip?.isChecked == true) {
                currentVideoStyle = styleId
            }
            selectedStyleItem(styleId = styleId, shouldScrollToPosition = true)
        }
        getStyleAdapterByStyleId(styleId)?.updateItemStatus(styleId, status)
    }

    private fun updateStyleItemLoadingProgress(styleId: String, progress: Float) {
        getStyleAdapterByStyleId(styleId)?.updateItemLoadingProgress(styleId, progress)
    }

    /**
     * 限定水印类型可见性变化
     */
    private fun onRestrictTypeVisibilityChanged(needShow: Boolean) {
        if (isSupportRestrict && needShow) {
            styleViewDataNtf?.notify(R.string.picture_editor_text_watermark_master_restrict, false)?.let { data ->
                restrictStyleAdapterList.forEach {
                    it.second?.data = data.filter { itemData -> itemData.seriesId == it.first }
                    currentPhotoStyle?.let { id ->
                        it.second?.selectedByStyleId(id)
                    }
                }
            }
            styleGroupViewList.forEach {
                it.visibility = View.VISIBLE
            }
        } else {
            styleGroupViewList.forEach {
                it.visibility = View.GONE
            }
        }
    }

    private fun refreshRestrictWatermarkStyle(dataList: MutableList<WatermarkStyleItemViewData>?) {
        if (dataList?.isNotEmpty() == true) {
            restrictStyleAdapterList.forEach {
                it.second?.data = dataList.filter { itemData -> itemData.seriesId == it.first }
            }
        } else {
            styleGroupViewList.forEach {
                it.visibility = View.GONE
            }
        }
        ///默认选中上一次选中的水印样式,vm onResume里面有个刷新水印数据导致选中状态丢失，这里补上
        setStyleMark()
    }


    /**
     * 设置使用中的项
     * */
    private fun setStyleMark() {
        val selectStyle = currentPhotoStyle ?: lastPhotoStyle
        selectStyle?.let { selectedStyleItem(it, shouldScrollToPosition = true, notifyBroadcast = false) }
    }

    inner class OnStyleItemClickListener : BaseRecyclerAdapter.OnItemClickListener<WatermarkStyleItemViewData> {
        override fun onItemClick(view: View?, position: Int, item: WatermarkStyleItemViewData?) {
            GLog.d(TAG, LogFlag.DF) { "styleItem Click ${item?.styleId}" }
            if (isClosing) {
                GLog.w(TAG, LogFlag.DF) { "onItemClick isClosing $isClosing，return" }
                return
            }
            item?.styleId?.let {
                if (TextUtils.isEmpty(item.styleId).not() && (item.status == ItemStatus.DEFAULT)) {
                    enteringPersonalizedSection = true
                    watermarkUiBeanNtf?.notify(
                        WatermarkMasterUIBean(
                            id = WatermarkMasterUiBeanId.ON_WATERMARK_STYLE_MASK_CLICK,
                            watermarkStyleId = item.styleId,
                            watermarkStyleStatus = item.status
                        )
                    )
                }
            }
        }

        override fun onItemSelected(view: View, position: Int, item: WatermarkStyleItemViewData?) {
            GLog.d(TAG, LogFlag.DF) { "styleItem Selected ${item?.itemId}" }
        }

        override fun onItemUnselected(view: View?, position: Int, item: WatermarkStyleItemViewData?) {
            GLog.d(TAG, LogFlag.DF) { "styleItem Unselected ${item?.itemId}" }
        }
    }

    inner class OnPhotoChipCheckedListener : CompoundButton.OnCheckedChangeListener {
        override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
            context?.let {
                if (isChecked) {
                    setTipsContent(it, true)
                    cameraSwitchText?.setText(R.string.picture_editor_text_watermark_camera_pic)
                    initPhotoStyleLayout()
                    val isPhotoSelected = currentPhotoStyle.isNullOrBlank().not()
                    if (cameraWatermarkSwitch?.isChecked == isPhotoSelected) {
                        // 开关状态和之前一致，不会触发onCheckedChanged，不能再次选中，这里主动selectedStyleItem
                        (currentPhotoStyle ?: lastPhotoStyle)?.let {
                            selectedStyleItem(
                                styleId = it,
                                shouldScrollToPosition = true,
                                notifyBroadcast = false
                            )
                        }
                    } else {
                        // 开关状态不一致，修改开关为选中状态，通过onCheckedChanged触发选中
                        cameraWatermarkSwitch?.isChecked = isPhotoSelected
                    }
                }
            }
        }
    }

    inner class OnVideoChipCheckedListener : CompoundButton.OnCheckedChangeListener {
        override fun onCheckedChanged(buttonView: CompoundButton?, isChecked: Boolean) {
            context?.let {
                if (isChecked) {
                    setTipsContent(it, false)
                    cameraSwitchText?.setText(R.string.picture_editor_text_watermark_camera_video)
                    initVideoStyleLayout()
                    val isVideoSelected = currentVideoStyle.isNullOrBlank().not()
                    if (cameraWatermarkSwitch?.isChecked == isVideoSelected) {
                        // 开关状态和之前一致，不会触发onCheckedChanged，不能再次选中，这里主动selectedStyleItem
                        (currentVideoStyle ?: lastVideoStyle)?.let {
                            selectedStyleItem(
                                styleId = it,
                                shouldScrollToPosition = true,
                                notifyBroadcast = false
                            )
                        }
                    } else {
                        // 开关状态不一致，修改开关为选中状态，通过onCheckedChanged触发选中
                        cameraWatermarkSwitch?.isChecked = isVideoSelected
                    }
                }
            }
        }
    }

    override fun onPageAssembled() {
        if (animatorSet?.isRunning == true) {
            animatorSet?.cancel()
        }
        if (firstTimeEntering) {
            firstTimeEntering = false
            super.onPageAssembled()
        } else {
            // 个性编辑页返回水印设置页需要触发一下app ui状态，防止进入个性编辑页横竖屏旋转后返回水印设置页面没刷新
            onAppUiStateChanged(sectionBus.hostInstance.getCurrentAppUiConfig())
            // 有动画跑动画，没动画直接设置可见性
            sectionContainerView?.let { containerView ->
                sectionContentView?.also { contentView ->
                    contentView.visibility = View.VISIBLE
                    contentView.translationX = -containerView.width.toFloat()
                    alphaAnimator = EditorAnimationUtils.createAlphaAnimator(
                        contentView,
                        true,
                        SLIDE_TRANSLATE_FADE_IN_DURATION,
                        COUIInEaseInterpolator()
                    )
                    translateAnimator = EditorAnimationUtils.createTranslationAnimator(
                        contentView,
                        false,
                        -containerView.width,
                        0,
                        SLIDE_TRANSLATE_FADE_IN_DURATION,
                        COUIInEaseInterpolator()
                    )
                    animatorSet = AnimatorSet()
                    animatorSet?.let {
                        it.playTogether(alphaAnimator, translateAnimator)
                        it.addListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                contentView.alpha = 1f
                                contentView.translationX = 0f
                            }
                        })
                        it.start()
                    } ?: let {
                        contentView.alpha = 1f
                        contentView.translationX = 0f
                    }
                }
            }
        }
    }

    override fun onPageDisassembled() {
        if (animatorSet?.isRunning == true) {
            animatorSet?.cancel()
        }
        if (enteringPersonalizedSection) {
            enteringPersonalizedSection = false
            // 有动画跑动画，没动画直接设置可见性
            sectionContainerView?.let { containerView ->
                sectionContentView?.also { contentView ->
                    alphaAnimator = EditorAnimationUtils.createAlphaAnimator(
                        contentView,
                        false,
                        SLIDE_TRANSLATE_FADE_OUT_DURATION,
                        COUIOutEaseInterpolator()
                    )
                    translateAnimator = EditorAnimationUtils.createTranslationAnimator(
                        contentView,
                        false,
                        0,
                        -containerView.width,
                        SLIDE_TRANSLATE_FADE_OUT_DURATION,
                        COUIOutEaseInterpolator()
                    )
                    animatorSet = AnimatorSet()
                    animatorSet?.let {
                        it.playTogether(alphaAnimator, translateAnimator)
                        it.addListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                contentView.visibility = View.INVISIBLE
                                contentView.alpha = 1f
                                contentView.translationX = 0f
                            }
                        })
                        it.start()
                    } ?: let {
                        contentView.visibility = View.INVISIBLE
                        contentView.alpha = 1f
                        contentView.translationX = 0f
                    }
                }
            }
        }
    }

    private fun closeCameraSection() {
        GLog.d(TAG, LogFlag.DL, "MasterStyleDebug:[closeCameraSection] back pressed")
        isClosing = true
        // 对于相机进入的情况来说，在编辑Activity的onDestroy清除缓存有点晚了，有时候快速切换进入和退出时，onDestroy会跑在下次onCreate之后。
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use {
            it.clearCache()
        }
        watermarkUiBeanNtf?.notify(WatermarkMasterUIBean(id = WatermarkMasterUiBeanId.WATERMARK_CAMERA_FINISH))
    }

    private fun onPersonalizedEditDone(applyStyleId: String? = null) {
        if (WatermarkMasterStyle.isVideoStyle(applyStyleId)) {
            currentVideoStyle = applyStyleId
        } else {
            currentPhotoStyle = applyStyleId
        }
        if ((photoWatermarkChip?.isChecked == true) && currentPhotoStyle.isNullOrBlank().not()) {
            notifyStyleSelected(currentPhotoStyle)
            currentPhotoStyle?.let {
                clearSelectedItem()
                selectedStyleItem(it, true)
            }
        } else if ((videoWatermarkChip?.isChecked == true) && currentVideoStyle.isNullOrBlank().not()) {
            notifyStyleSelected(currentVideoStyle)
            currentVideoStyle?.let {
                clearSelectedItem()
                selectedStyleItem(it, true)
            }
        }
    }

    private fun notifyStyleSelected(styleId: String? = null) {
        if (GProperty.DEBUG_AI_WATERMARK_MASTER_LOGGER) {
            GLog.d(TAG, LogFlag.DL, "MasterStyleDebug:[notifyStyleSelected] styleId:$styleId")
        }
        watermarkUiBeanNtf?.notify(
            WatermarkMasterUIBean(
                id = WatermarkMasterUiBeanId.ON_WATERMARK_STYLE_SELECTED,
                watermarkStyleId = styleId
            )
        )
    }

    private fun notifyBroadcastCameraSettings() {
        watermarkUiBeanNtf?.notify(
            WatermarkMasterUIBean(
                id = WatermarkMasterUiBeanId.BROADCAST_CURRENT_SELECTED_STYLE,
                videoStyleId = currentVideoStyle,
                watermarkStyleId = currentPhotoStyle
            )
        )
    }

    private fun getRestrictStyleAdapter(titleId: Int, seriesId: String?): WatermarkMasterStyleAdapter? {
        context?.let {
            styleViewDataNtf?.notify(titleId, false)?.let { data ->
                val finalData = data.filter { it.seriesId == seriesId }.toMutableList()
                return WatermarkMasterStyleAdapter(
                    context,
                    R.layout.picture3d_editor_watermark_camera_style_list_item_layout,
                    android.R.color.transparent,
                    finalData,
                    fromCamera = true
                ).apply {
                    setItemMaskClickListener(itemMaskClickListener)
                    setCanUnselectCurrentPosition(false)
                    setItemClickListener(OnStyleItemClickListener())
                    setFastClickEnabled(true)
                }
            }
        }
        return null
    }

    private fun getStyleAdapter(titleId: Int, forVideo: Boolean = false): WatermarkMasterStyleAdapter? {
        context?.let {
            styleViewDataNtf?.notify(titleId, forVideo)?.let { data ->
                return WatermarkMasterStyleAdapter(
                    context,
                    R.layout.picture3d_editor_watermark_camera_style_list_item_layout,
                    android.R.color.transparent,
                    data,
                    fromCamera = true
                ).apply {
                    setItemMaskClickListener(itemMaskClickListener)
                    setCanUnselectCurrentPosition(false)
                    setItemClickListener(OnStyleItemClickListener())
                    setFastClickEnabled(true)
                }
            }
        }
        return null
    }

    private fun initPhotoStyleLayout() {
        if (isSupportHasselWatermark) {
            initHasselStyleLayout()
        } else {
            cameraLayout?.findViewById<LinearLayout>(R.id.camera_hassel_style_layout)?.visibility = View.GONE
        }
        initMasterSignStyleLayout()
        initRetroCameraStyleLayout()
        initFilmStyleLayout()
        initBrandStyleLayout()
        if (isRealmeBrand) {
            cameraLayout?.findViewById<LinearLayout>(R.id.camera_realme_brand_style_layout)?.visibility = View.VISIBLE
            cameraLayout?.findViewById<LinearLayout>(R.id.camera_series_brand_layout)?.visibility = View.GONE
            initRealmeBrandStyleLayout()
            initInspirationStyleLayout()
            initMemoryStyleLayout()
        }
        initTextStyleLayout()
        cameraLayout?.findViewById<LinearLayout>(R.id.camera_frame_style_layout)?.visibility = View.VISIBLE
        setRestrictVisibility(true)
    }

    private fun initVideoStyleLayout() {
        if (isSupportHasselWatermark) {
            initVideoHasselTextStyleLayout()
        } else {
            cameraLayout?.findViewById<LinearLayout>(R.id.camera_hassel_style_layout)?.visibility = View.GONE
        }
        initVideoTextStyleLayout()
        cameraLayout?.findViewById<LinearLayout>(R.id.camera_restrict_style_layout)?.visibility = View.GONE
        cameraLayout?.findViewById<LinearLayout>(R.id.camera_frame_style_layout)?.visibility = View.GONE
        cameraLayout?.findViewById<LinearLayout>(R.id.camera_realme_brand_style_layout)?.visibility = View.GONE
        setRestrictVisibility(false)
    }

    /**
     * 限定水印控件可见性
     * @param visible 是否可见
     * */
    private fun setRestrictVisibility(visible: Boolean) {
        //隐藏标题
        setRestrictGroupTitleVisibility(visible)
        styleGroupViewList.forEach {
            it.visibility = if (visible) View.VISIBLE else View.GONE
        }
    }

    /**
     * 限定水印分组标题可见性
     * @param visible 是否可见
     * */
    private fun setRestrictGroupTitleVisibility(visible: Boolean) {
        cameraLayout?.findViewById<TextView>(R.id.camera_restrict_style_group_title)?.visibility =
            if (visible) View.VISIBLE else View.GONE
    }

    private fun clearSelectedItem() {
        hasselStyleAdapter?.clearSingleSelectedPosition()
        hasselStyleAdapter?.notifyDataSetChanged()
        masterSignStyleAdapter?.clearSingleSelectedPosition()
        masterSignStyleAdapter?.notifyDataSetChanged()
        retroCameraStyleAdapter?.clearSingleSelectedPosition()
        retroCameraStyleAdapter?.notifyDataSetChanged()
        filmStyleAdapter?.clearSingleSelectedPosition()
        filmStyleAdapter?.notifyDataSetChanged()
        brandStyleAdapter?.clearSingleSelectedPosition()
        brandStyleAdapter?.notifyDataSetChanged()
        realmeBrandStyleAdapter?.clearSingleSelectedPosition()
        realmeBrandStyleAdapter?.notifyDataSetChanged()
        inspirationStyleAdapter?.clearSingleSelectedPosition()
        inspirationStyleAdapter?.notifyDataSetChanged()
        memoryStyleAdapter?.clearSingleSelectedPosition()
        memoryStyleAdapter?.notifyDataSetChanged()
        textStyleAdapter?.clearSingleSelectedPosition()
        textStyleAdapter?.notifyDataSetChanged()
        restrictStyleAdapterList.forEach {
            it.second?.clearSingleSelectedPosition()
            it.second?.notifyDataSetChanged()
        }
        lastClickDownloadStyleId = null
    }

    private fun selectedStyleItem(styleId: String, shouldScrollToPosition: Boolean = false, notifyBroadcast: Boolean = true) {
        val selectedStyleAdapter = getStyleAdapterByStyleId(styleId)
        selectedStyleAdapter?.let {
            it.selectedByStyleId(styleId)
            if (shouldScrollToPosition) {
                val listView = getListViewByStyleAdapter(it) ?: return@let
                listView.scrollToPosition(it.selectedPosition)
                val sv = scrollView ?: return@let
                sv.viewTreeObserver.addOnPreDrawListener(object : OnPreDrawListener {
                    override fun onPreDraw(): Boolean {
                        sv.viewTreeObserver.removeOnPreDrawListener(this)
                        val scrollViewLocation = IntArray(2)
                        sv.getLocationOnScreen(scrollViewLocation)
                        val scrollViewTop = scrollViewLocation[1]
                        val scrollViewBottom = scrollViewTop + sv.height
                        val listViewLocation = IntArray(2)
                        listView.getLocationOnScreen(listViewLocation)
                        val listViewTop = listViewLocation[1]
                        val listViewBottom = listViewTop + listView.height
                        if ((listViewTop < scrollViewTop) || listViewBottom > scrollViewBottom) {
                            // listView不在scrollView范围内才滑动
                            if (isFrameWatermarkListView(listView)) {
                                val seriesViewGroup = listView.parent as ViewGroup
                                val styleViewGroup = seriesViewGroup.parent as ViewGroup
                                sv.smoothScrollTo(0, styleViewGroup.top + seriesViewGroup.top)
                            } else {
                                sv.smoothScrollTo(0, (listView.parent as ViewGroup).top)
                            }
                        }
                        return true
                    }
                })
            }
        }

        lastClickDownloadStyleId = null
        /**
         * 点击样式直接跳转个性编辑页，这里不需要再做一次加水印操作
         */
        if (notifyBroadcast) {
            notifyBroadcastCameraSettings()
        }
    }

    private fun getStyleAdapterByStyleId(styleId: String): WatermarkMasterStyleAdapter? {
        restrictStyleAdapterList.forEach { pair ->
            if (pair.second?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) {
                return pair.second
            }
        }
        return when {
            (hasselStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> hasselStyleAdapter
            (masterSignStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> masterSignStyleAdapter
            (retroCameraStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> retroCameraStyleAdapter
            (filmStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> filmStyleAdapter
            (brandStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> brandStyleAdapter
            (realmeBrandStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> realmeBrandStyleAdapter
            (inspirationStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> inspirationStyleAdapter
            (memoryStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> memoryStyleAdapter
            (textStyleAdapter?.getPositionByStyleId(styleId)?.takeIf { it >= 0 } != null) -> textStyleAdapter
            else -> null
        }
    }

    private fun getListViewByStyleAdapter(styleAdapter: WatermarkMasterStyleAdapter): EditorLinearListView? {
        styleGroupList.forEach { listview ->
            if (listview.adapter === styleAdapter) {
                return listview
            }
        }
        return when {
            hasselListView?.adapter === styleAdapter -> hasselListView
            masterSignListView?.adapter === styleAdapter -> masterSignListView
            retroCameraListView?.adapter === styleAdapter -> retroCameraListView
            filmListView?.adapter === styleAdapter -> filmListView
            brandListView?.adapter === styleAdapter -> brandListView
            realmeBrandListView?.adapter === styleAdapter -> realmeBrandListView
            inspirationListView?.adapter === styleAdapter -> inspirationListView
            memoryListView?.adapter === styleAdapter -> memoryListView
            textListView?.adapter === styleAdapter -> textListView
            else -> null
        }
    }

    private fun isFrameWatermarkListView(listView: EditorLinearListView): Boolean {
        return when {
            (listView === masterSignListView) || (listView === retroCameraListView)
                    || (listView === filmListView) || (listView === brandListView) -> true

            else -> false
        }
    }

    private fun initHasselStyleLayout() {
        cameraLayout?.apply {
            hasselLayout = cameraLayout?.findViewById(R.id.camera_hassel_style_layout)
            hasselListView = cameraLayout?.findViewById(R.id.camera_hassel_style_list)
            hasselListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            hasselListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            hasselStyleAdapter = getStyleAdapter(R.string.picture_editor_text_watermark_master_hasselblad)
            hasselListView?.adapter = hasselStyleAdapter
        }
    }

    private fun initVideoHasselTextStyleLayout() {
        cameraLayout?.apply {
            hasselLayout = cameraLayout?.findViewById(R.id.camera_hassel_style_layout)
            hasselListView = cameraLayout?.findViewById(R.id.camera_hassel_style_list)
            hasselListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            hasselListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            hasselStyleAdapter = getStyleAdapter(R.string.picture_editor_text_watermark_master_hasselblad, true)
            hasselListView?.adapter = hasselStyleAdapter
        }
    }

    private fun initMasterSignStyleLayout() {
        cameraLayout?.apply {
            masterSignLayout = cameraLayout?.findViewById(R.id.camera_series_master_sign_layout)
            masterSignListView = cameraLayout?.findViewById(R.id.camera_series_master_sign_list)
            masterSignListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            masterSignListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            masterSignStyleAdapter = getStyleAdapter(R.string.pitcure_edtior_text_watermark_master_seal)
            masterSignListView?.adapter = masterSignStyleAdapter
        }
    }

    private fun initRetroCameraStyleLayout() {
        cameraLayout?.apply {
            retroCameraLayout = cameraLayout?.findViewById(R.id.camera_series_retro_camera_layout)
            retroCameraListView = cameraLayout?.findViewById(R.id.camera_series_retro_camera_list)
            retroCameraListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            retroCameraListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            retroCameraStyleAdapter = getStyleAdapter(R.string.pitcure_edtior_text_watermark_classic_camera)
            retroCameraListView?.adapter = retroCameraStyleAdapter
        }
    }

    private fun initFilmStyleLayout() {
        cameraLayout?.apply {
            filmLayout = cameraLayout?.findViewById(R.id.camera_series_film_layout)
            filmListView = cameraLayout?.findViewById(R.id.camera_series_film_list)
            filmListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            filmListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            filmStyleAdapter = getStyleAdapter(R.string.picture_editor_text_watermark_master_submission_series)
            filmListView?.adapter = filmStyleAdapter
        }
    }

    private fun initBrandStyleLayout() {
        cameraLayout?.apply {
            brandLayout = cameraLayout?.findViewById(R.id.camera_series_brand_layout)
            brandListView = cameraLayout?.findViewById(R.id.camera_series_brand_list)
            brandListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            brandListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            brandStyleAdapter = getStyleAdapter(R.string.pitcure_edtior_text_watermark_brand_power)
            brandListView?.adapter = brandStyleAdapter
        }
    }

    private fun initRealmeBrandStyleLayout() {
        cameraLayout?.apply {
            realmeBrandLayout = cameraLayout?.findViewById(R.id.camera_realme_brand_layout)
            realmeBrandListView = cameraLayout?.findViewById(R.id.camera_realme_brand_list)
            realmeBrandListView?.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            realmeBrandListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            realmeBrandStyleAdapter = getStyleAdapter(R.string.pitcure_edtior_text_watermark_realme_brand_power)
            realmeBrandListView?.adapter = realmeBrandStyleAdapter
        }
    }

    private fun initInspirationStyleLayout() {
        cameraLayout?.apply {
            inspirationLayout = cameraLayout?.findViewById(R.id.camera_series_inspiration_layout)
            inspirationListView = cameraLayout?.findViewById(R.id.camera_series_inspiration_list)
            inspirationListView?.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            inspirationListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            inspirationStyleAdapter = getStyleAdapter(R.string.pitcure_edtior_text_watermark_inspiration_photo)
            inspirationListView?.adapter = inspirationStyleAdapter
        }
    }

    private fun initMemoryStyleLayout() {
        cameraLayout?.apply {
            memoryLayout = cameraLayout?.findViewById(R.id.camera_memory_layout)
            memoryListView = cameraLayout?.findViewById(R.id.camera_memory_list)
            memoryListView?.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            memoryListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            memoryStyleAdapter = getStyleAdapter(R.string.pitcure_edtior_text_watermark_exclusive_memory)
            memoryListView?.adapter = memoryStyleAdapter
        }
    }

    private fun initTextStyleLayout() {
        cameraLayout?.apply {
            textLayout = cameraLayout?.findViewById(R.id.camera_text_style_layout)
            textListView = cameraLayout?.findViewById(R.id.camera_text_style_list)
            textListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            textListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            textStyleAdapter = getStyleAdapter(R.string.picture_editor_text_watermark_master_text)
            textListView?.adapter = textStyleAdapter
        }
    }

    private fun initVideoTextStyleLayout() {
        cameraLayout?.apply {
            textLayout = cameraLayout?.findViewById(R.id.camera_text_style_layout)
            textListView = cameraLayout?.findViewById(R.id.camera_text_style_list)
            textListView?.layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            textListView?.apply {
                removeItemDecoration(horizontalMarginItemDecoration)
                addItemDecoration(horizontalMarginItemDecoration)
            }
            textStyleAdapter = getStyleAdapter(R.string.picture_editor_text_watermark_master_text, true)
            textListView?.adapter = textStyleAdapter
        }
    }

    /**
     * item listview初始化
     * @param context 上下文
     * @param group 类型组数据
     * @param horizontalMarginItemDecoration 水平边距
     * @param hidePaddingBottom 是否隐藏下边距
     * */
    private fun createStyleItemView(
        context: Context,
        group: WatermarkTypeGroupData,
        horizontalMarginItemDecoration: RecyclerView.ItemDecoration,
        hidePaddingBottom: Boolean = false
    ): View {
        val itemView = LayoutInflater.from(context).inflate(
            R.layout.picture3d_editor_watermark_camera_restrict_item,
            null,
            false
        )

        if (hidePaddingBottom) {
            val paddingLeft = itemView.paddingLeft
            val paddingTop = itemView.paddingTop
            val paddingRight = itemView.paddingRight
            val paddingBottom = 0 // 新的底部填充值
            itemView.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
        }
        val titleView = itemView.findViewById<TextView>(R.id.camera_restrict_item_style_title)
        val itemListView = itemView.findViewById<CameraNestedRecyclerView>(R.id.camera_restrict_item_style_list)
        itemListView.apply {
            layoutManager = GalleryLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            removeItemDecoration(horizontalMarginItemDecoration)
            addItemDecoration(horizontalMarginItemDecoration)
        }

        titleView.text = group.title ?: context.getText(R.string.picture_editor_text_watermark_master_restrict)

        return itemView
    }

    /**
     * 限定水印分组列表view初始化
     * */
    private fun initRestrictStyleLayout() {
        cameraLayout?.apply {
            scrollContainer = cameraLayout?.findViewById(R.id.sv_style_list_container)
            clearData()
            if (typeList.isEmpty()) {
                setRestrictGroupTitleVisibility(false)
                return
            }
            setRestrictGroupTitleVisibility(true)
            val reversedList = typeList.asReversed()

            for ((index, group) in reversedList.withIndex()) {
                val itemView = createStyleItemView(context, group, horizontalMarginItemDecoration, index == 0)
                val itemListView = itemView.findViewById<CameraNestedRecyclerView>(R.id.camera_restrict_item_style_list)
                vBus.notifyOnce<Boolean>(REPLY_WATERMARK_MASTER_LOAD_RESTRICT_STYLE_LIST)

                val adapter = getRestrictStyleAdapter(R.string.picture_editor_text_watermark_master_restrict, seriesId = group.seriesId)
                restrictStyleAdapterList.add(Pair(group.seriesId, adapter))
                itemListView.adapter = adapter
                styleGroupList.add(itemListView)
                scrollContainer?.addView(itemView, 1)
                styleGroupViewList.add(itemView)
            }
        }
        //初始化可能导致mark丢失，这里重新设置一下
        setStyleMark()
    }

    /**
     * 清除view数据 避免重复添加
     * */
    private fun clearData() {
        repeat(restrictStyleAdapterList.size) {
            if ((scrollContainer?.childCount ?: 0) > 1) {
                scrollContainer?.removeViewAt(1)
            }
        }
        restrictStyleAdapterList.clear()
        styleGroupViewList.clear()
        styleGroupList.clear()
    }

    private fun setMarginsBasedOnMode() {
        cameraSwitchCardView = cameraLayout?.findViewById<COUICardView>(R.id.watermark_camera_switch_card_view)
        cameraStyleItemTitleImage = cameraLayout?.findViewById<ImageView>(R.id.camera_style_item_title_image)
        cameraFrameStyleTitle = cameraLayout?.findViewById<TextView>(R.id.camera_frame_style_title)
        cameraSeriesMasterSignTitle = cameraLayout?.findViewById<TextView>(R.id.camera_series_master_sign_title)
        cameraSeriesRetroCameraTitle = cameraLayout?.findViewById<TextView>(R.id.camera_series_retro_camera_title)
        cameraSeriesFilmTitle = cameraLayout?.findViewById<TextView>(R.id.camera_series_film_title)
        cameraSeriesBrandTitle = cameraLayout?.findViewById<TextView>(R.id.camera_series_brand_title)
        cameraTextTitle = cameraLayout?.findViewById<TextView>(R.id.camera_text_style_title)
        cameraRestrictTitle = cameraLayout?.findViewById<TextView>(R.id.camera_restrict_style_title)
        when (getConfigMode(lastAppUiConfig)) {
            LARGE_MODE -> {
                setViewMargin(cameraSwitchCardView, largeMargin)
                setViewMargin(cameraStyleItemTitleImage, largeMargin)
                setViewMargin(cameraFrameStyleTitle, largeMargin)
                setViewMargin(cameraSeriesMasterSignTitle, largeMargin)
                setViewMargin(cameraSeriesRetroCameraTitle, largeMargin)
                setViewMargin(cameraSeriesFilmTitle, largeMargin)
                setViewMargin(cameraSeriesBrandTitle, largeMargin)
                setViewMargin(cameraTextTitle, largeMargin)
                setViewMargin(cameraRestrictTitle, largeMargin)
            }

            MEDIUM_MODE -> {
                // 折叠屏大小屏切换需要重新设置宽度
                val windowWidth = lastAppUiConfig?.windowWidth?.current ?: 0
                cameraSwitchCardView?.layoutParams?.width = LinearLayout.LayoutParams.MATCH_PARENT
                scrollView?.layoutParams?.width = windowWidth
                setViewMargin(cameraSwitchCardView, mediumMargin)
                setViewMargin(cameraStyleItemTitleImage, mediumMargin)
                setViewMargin(cameraFrameStyleTitle, mediumMargin)
                setViewMargin(cameraSeriesMasterSignTitle, mediumMargin)
                setViewMargin(cameraSeriesRetroCameraTitle, mediumMargin)
                setViewMargin(cameraSeriesFilmTitle, mediumMargin)
                setViewMargin(cameraSeriesBrandTitle, mediumMargin)
                setViewMargin(cameraTextTitle, mediumMargin)
                setViewMargin(cameraRestrictTitle, mediumMargin)
            }

            LAND_MODE -> {
                val windowWidth = lastAppUiConfig?.windowWidth?.current ?: 0
                val gridWidth = UIConfigUtils.getRequireGridWidth(windowWidth.toFloat(), edge, gap, COL_COUNT, REQUIRED_COL_COUNT).toInt()
                cameraSwitchCardView?.layoutParams?.width = gridWidth
                scrollView?.layoutParams?.width = gridWidth +
                        (resources?.getDimensionPixelSize(R.dimen.picture3d_editor_watermark_camera_type_list_padding_horizontal_land) ?: 0)

                setViewMargin(cameraSwitchCardView, landMargin)
                setViewMargin(cameraStyleItemTitleImage, landMargin)
                setViewMargin(cameraFrameStyleTitle, landMargin)
                setViewMargin(cameraSeriesMasterSignTitle, landMargin)
                setViewMargin(cameraSeriesRetroCameraTitle, landMargin)
                setViewMargin(cameraSeriesFilmTitle, landMargin)
                setViewMargin(cameraSeriesBrandTitle, landMargin)
                setViewMargin(cameraTextTitle, landMargin)
                setViewMargin(cameraRestrictTitle, landMargin)

                cameraLayout?.requestLayout()
            }

            else -> {
                val windowWidth = lastAppUiConfig?.windowWidth?.current ?: 0
                cameraSwitchCardView?.layoutParams?.width = LinearLayout.LayoutParams.MATCH_PARENT
                scrollView?.layoutParams?.width = windowWidth

                setViewMargin(cameraSwitchCardView, portraitMargin)
                setViewMargin(cameraStyleItemTitleImage, portraitMargin)
                setViewMargin(cameraFrameStyleTitle, portraitMargin)
                setViewMargin(cameraSeriesMasterSignTitle, portraitMargin)
                setViewMargin(cameraSeriesRetroCameraTitle, portraitMargin)
                setViewMargin(cameraSeriesFilmTitle, portraitMargin)
                setViewMargin(cameraSeriesBrandTitle, portraitMargin)
                setViewMargin(cameraTextTitle, portraitMargin)
                setViewMargin(cameraRestrictTitle, portraitMargin)

                cameraLayout?.requestLayout()
            }
        }
    }

    private fun setViewMargin(view: View?, margin: Int) {
        (view?.layoutParams as? ViewGroup.MarginLayoutParams)?.apply {
            rightMargin = margin
            leftMargin = margin
        }
    }

    private fun getConfigMode(config: AppUiResponder.AppUiConfig?): Int {
        val isLandscape = EditorUIConfig.isEditorLandscape(config)
        val isMedium = context?.let { ScreenUtils.isMiddleScreenWidthAndHeight(it) } ?: false
        val isLarge = context?.let { ScreenUtils.isLargeScreen(it) } ?: false
        val isInMultiWindow = config?.isInMultiWindow?.current == true
        val isInFloatingWindow = config?.isInFloatingWindow?.current == true
        return when {
            isInMultiWindow || isInFloatingWindow -> PORTRAIT_MODE
            isLarge -> LARGE_MODE
            isMedium -> MEDIUM_MODE
            isLandscape -> LAND_MODE
            else -> PORTRAIT_MODE
        }
    }

    internal companion object {
        private const val TAG = "WatermarkCameraSection"
        private const val SLIDE_TRANSLATE_FADE_IN_DURATION = 350
        private const val SLIDE_TRANSLATE_FADE_OUT_DURATION = 250
        private const val PORTRAIT_MODE = 0
        private const val MEDIUM_MODE = 1
        private const val LARGE_MODE = 2
        private const val LAND_MODE = 3
        private val PORTRAIT_LAYOUT_ID = R.layout.picture3d_editor_watermark_camera
        val SPEC = SectionSpec(WatermarkCameraSection::class.java)
        private const val COL_COUNT = 8
        private const val REQUIRED_COL_COUNT = 6
    }
}
