/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PmStickerUiBean
 ** Description:
 ** Version: 1.0
 ** Date : 2024/6/27
 ** Author: pxijin
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  pixijin                      2024/6/27    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.pmsticker

/**
 * Pm贴纸的UI属性
 */
internal data class PmStickerUiBean(
    /**
     * 事件属性Id
     */
    var id: PmStickerBeanUiId = PmStickerBeanUiId.NONE,

    /**
     * 贴纸数据集
     */
    var stickerItems: List<PmStickerItem>? = null,
)

/**
 * 接收/发送 UiBean 事件的Id
 */
enum class PmStickerBeanUiId {
    NONE,
    INIT
}
