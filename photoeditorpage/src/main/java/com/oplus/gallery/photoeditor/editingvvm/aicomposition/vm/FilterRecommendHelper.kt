/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** File        : FilterRecommendHelper.kt
 ** Description : 滤镜推荐
 ** Version     : 1.0
 ** Date        : 2024/11/19 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>                  <data>    <version>  <desc>
 **  <EMAIL>     2024/11/18  1.0        build this module
 ***********************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aicomposition.vm

import android.content.res.Resources
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.function.FunctionSwitchManager.isSwitchEnable
import com.oplus.gallery.business_lib.function.FunctionSwitcherConfig
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.text.JsonUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.filter.FilterConstant
import com.oplus.gallery.photoeditor.editingvvm.filter.IpuExtraInfo
import com.oplus.gallery.standard_lib.util.os.ContextGetter

internal object FilterRecommendHelper {
    private const val TAG = "FilterRecommendHelper"

    /*二级标签ID-人像*/
    private val LABEL_IDS_2ND_FOR_PORTRAIT = listOf(
        85, 86, 100, 101, 159, 168, 207, 219, 226, 246, 254, 266, 284,
        343, 347, 399, 414, 429, 468, 489, 501, 511, 624, 633, 637, 653,
        654, 671, 672, 682, 683, 689, 699, 712, 713, 716, 718, 727, 743,
        745, 768, 799, 807, 826, 834, 861, 900, 925, 1045, 1402, 1423, 1431,
        1435, 1443, 1448, 1462, 1463, 1473, 1476, 1486, 1513, 1517, 1527, 1556, 1581, 1584
    )

    /*二级标签ID-食物*/
    private val LABEL_IDS_2ND_FOR_FOOD = listOf(
        28, 29, 30, 31, 73, 74, 75, 76, 138, 139, 140, 235, 242, 257, 259, 305,
        362, 383, 386, 390, 394, 395, 396, 397, 425, 435, 437, 446, 451, 462, 463,
        469, 472, 475, 479, 491, 500, 504, 514, 519, 528, 530, 535, 547, 588, 604,
        621, 626, 639, 662, 669, 686, 687, 698, 711, 721, 725, 728, 730, 738, 755,
        771, 773, 775, 780, 782, 783, 789, 793, 794, 801, 805, 819, 820, 829, 833,
        867, 888, 896, 912, 926, 942, 944, 958, 960, 962, 963, 973, 984, 991, 996,
        1016, 1018, 1019, 1031, 1034, 1056, 1065, 1069, 1084, 1088, 1089, 1106, 1107,
        1108, 1115, 1116, 1124, 1125, 1135, 1137, 1143, 1145, 1147, 1151, 1153, 1172,
        1173, 1180, 1183, 1185, 1198, 1233, 1234, 1240, 1241, 1246, 1263, 1275, 1277,
        1286, 1293, 1323, 1325, 1327, 1332, 1340, 1341, 1342, 1351, 1365, 1367, 1380,
        1381, 1403, 1411, 1416, 1420, 1438, 1459, 1461, 1487, 1495, 1506, 1507, 1512,
        1515, 1532, 1557, 1566, 1589, 1594, 1616
    )

    /*二级标签ID-饮品*/
    private val LABEL_IDS_2ND_FOR_DRINKS = listOf(
        32, 33, 105, 314, 333, 423, 509, 526, 536, 680, 692,
        756, 758, 761, 785, 928, 1003, 1005, 1291, 1484
    )

    /*二级标签ID-建筑*/
    private val LABEL_IDS_2ND_FOR_BUILDING = listOf(
        12, 59, 60, 61, 62, 63, 142, 147, 160, 163, 200, 215, 244, 304, 317, 338, 340,
        369, 443, 458, 476, 488, 726, 803, 858, 936, 939, 950, 983, 1006, 1017, 1028,
        1029, 1044, 1066, 1098, 1129, 1138, 1197, 1206, 1215, 1216, 1364, 1499, 1573
    )

    /*二级标签ID-地标*/
    private val LABEL_IDS_2ND_FOR_LANDMARKS = listOf(
        703, 714, 1004, 1052, 1057, 1077, 1104, 1130, 1134, 1141, 1160, 1188, 1189, 1207,
        1212, 1219, 1222, 1225, 1227, 1228, 1230, 1250, 1257, 1269, 1272, 1302, 1305, 1307,
        1310, 1316, 1328, 1331, 1333, 1334, 1337, 1339, 1344, 1348, 1349, 1353, 1357, 1358,
        1359, 1360, 1361, 1362, 1370, 1371, 1374, 1378
    )

    /*二级标签ID-偏暖色调风景*/
    private val LABEL_IDS_2ND_FOR_WARM_LANDSCAPE = listOf(
        58, 1470
    )
    /*二级标签ID-偏冷色调风景*/
    private val LABEL_IDS_2ND_FOR_COLD_LANDSCAPE = listOf(
        9, 10, 11, 54, 56, 57, 110, 111, 137, 146, 177, 194, 198, 249, 281, 282, 285, 300,
        330, 349, 391, 403, 416, 432, 459, 461, 486, 495, 510, 537, 545, 572, 587, 638, 656,
        670, 677, 720, 724, 740, 746, 747, 796, 809, 840, 852, 873, 876, 881, 882, 887, 890,
        904, 927, 930, 947, 972, 979, 992, 1043, 1090, 1102, 1103, 1111, 1126, 1142, 1170, 1214,
        1252, 1300, 1320, 1355, 1369, 1375, 1388, 1392, 1406, 1422, 1444, 1450, 1466, 1471, 1479,
        1480, 1483, 1488, 1496, 1516, 1518, 1537, 1560, 1569
    )

    /*二级标签ID-植物*/
    private val LABEL_IDS_2ND_FOR_PLANT = listOf(
        5, 6, 7, 8, 51, 52, 104, 149, 164, 224, 271, 336, 339, 388, 393, 412, 440, 448, 478, 505,
        617, 645, 657, 660, 664, 751, 757, 786, 877, 880, 891, 895, 902, 906, 938, 964, 988, 989,
        1002, 1011, 1014, 1024, 1054, 1067, 1068, 1074, 1082, 1083, 1117, 1149, 1167, 1169, 1204,
        1209, 1220, 1244, 1260, 1264, 1265, 1266, 1280, 1326, 1346, 1356, 1379, 1514, 1523, 1533,
        1592, 1607, 1610, 1622
    )

    /*二级标签ID-旅行出行*/
    private val LABEL_IDS_2ND_FOR_TRAVEL = listOf(
        112, 172, 178, 195, 199, 217, 230, 234, 238, 354, 450, 525, 531, 598, 717, 1047, 1184
    )
    /*滤镜推荐的数量*/
    private const val RECOMMEND_SIZE = 5
    /*IPU滤镜icon*/
    private val IPU_FILTER_ICONS = mapOf(
        FilterConstant.BIN_BLACK_WHITE to R.drawable.picture3d_ai_composition_filter_blackandwhite,
        FilterConstant.BIN_FRESH to R.drawable.picture3d_ai_composition_filter_cc,
        FilterConstant.BIN_COLD to R.drawable.picture3d_ai_composition_filter_cool,
        FilterConstant.BIN_CYBERPUNK to R.drawable.picture3d_ai_composition_filter_cyberpunk,
        FilterConstant.BIN_TASTY to R.drawable.picture3d_ai_composition_filter_gourmet,
        FilterConstant.BIN_SENLIN to R.drawable.picture3d_ai_composition_filter_kodak,
        FilterConstant.BIN_MEICAM to R.drawable.picture3d_ai_composition_filter_meicam,
        FilterConstant.BIN_MONO to R.drawable.picture3d_ai_composition_filter_mono,
        FilterConstant.BIN_HIGH_GRAY to R.drawable.picture3d_ai_composition_filter_morandi,
        FilterConstant.BIN_NATURE to R.drawable.picture3d_ai_composition_filter_natural,
        FilterConstant.BIN_RETRO to R.drawable.picture3d_ai_composition_filter_nc,
        FilterConstant.BIN_TRANSPARENT to R.drawable.picture3d_ai_composition_filter_nh,
        FilterConstant.BIN_HUMANITIES to R.drawable.picture3d_ai_composition_filter_serenity,
        FilterConstant.BIN_WARM to R.drawable.picture3d_ai_composition_filter_warm,
        FilterConstant.BIN_BRIGHT to R.drawable.picture3d_ai_composition_filter_eterna
    )
    /*非IPU滤镜icon*/
    private val NORMAL_FILTER_ICONS = mapOf(
        FilterConstant.TBL_KEY_FILTER_BLACK_WHITE to R.drawable.picture3d_ai_composition_filter_blackandwhite,
        FilterConstant.TBL_KEY_FILTER_FRESH to R.drawable.picture3d_ai_composition_filter_cc,
        FilterConstant.TBL_KEY_FILTER_COOL to R.drawable.picture3d_ai_composition_filter_cool,
        FilterConstant.TBL_KEY_FILTER_CYBERPUNK to R.drawable.picture3d_ai_composition_filter_cyberpunk,
        FilterConstant.TBL_KEY_FILTER_FOOD to R.drawable.picture3d_ai_composition_filter_gourmet,
        FilterConstant.TBL_KEY_FILTER_FOREST to R.drawable.picture3d_ai_composition_filter_kodak,
        FilterConstant.TBL_KEY_FILTER_MASTER_FAIRY_TALE to R.drawable.picture3d_ai_composition_filter_meicam,
        FilterConstant.TBL_KEY_FILTER_MONO to R.drawable.picture3d_ai_composition_filter_mono,
        FilterConstant.TBL_KEY_FILTER_HIGH_GRAY to R.drawable.picture3d_ai_composition_filter_morandi,
        FilterConstant.TBL_KEY_FILTER_NATURE to R.drawable.picture3d_ai_composition_filter_natural,
        FilterConstant.TBL_KEY_FILTER_VINTAGE to R.drawable.picture3d_ai_composition_filter_nc,
        FilterConstant.TBL_KEY_FILTER_CLEAR to R.drawable.picture3d_ai_composition_filter_nh,
        FilterConstant.TBL_KEY_FILTER_WARM_PORTRAIT_BLUR to R.drawable.picture3d_ai_composition_filter_warm,
        FilterConstant.TBL_KEY_FILTER_BRIGHT to R.drawable.picture3d_ai_composition_filter_eterna
    )

    /*滤镜参数数据类*/
    data class FilterArgument(
        /*滤镜信息*/
        val filter: Filter,
        /*滤镜生成使用参数*/
        val argument: AvEffect.Argument,
    ) {
        override fun toString(): String = "filterName:${filter.name}，extras:${argument.extras}"
    }

    /*滤镜信息类*/
    data class Filter(
        /*滤镜名称*/
        val name: String,
        /*滤镜大师类型*/
        val masterType: Int,
        /*滤镜icon资源ID*/
        val iconId: Int = Resources.ID_NULL,
    )

    /*滤镜推荐列表类*/
    data class FilterRecommend(
        /*二级标签列表，用于使用某滤镜的需满足的二级标签条件*/
        val secondLabelIds: List<Int>,
        /*滤镜列表*/
        val filters: List<String>
    ) {
        /**
         * 判断当前标签是否匹配推荐滤镜
         *
         * @param orgLabelIds 图片原始标签列表
         *
         * @return 是否匹配
         */
        fun isMatch(orgLabelIds: List<Int>): Boolean {
            return if (secondLabelIds.isEmpty() || filters.isEmpty()) {
                //二级标签为空或者滤镜列表为空，匹配的必要条件不满足，直接返回false走默认的推荐滤镜
                false
            } else {
                /**
                 *  1、二级标签列表不为空，则二级标签列表里面有值命中图片标签时，二级标签满足匹配条件
                 *  2、如果二级列表匹配上，则直接返回匹配结果，否则继续判断一级标签是否匹配
                 */
                secondLabelIds.firstOrNull { orgLabelIds.contains(it) } != null
            }
        }
    }

    /*人像拍摄场景非IPU滤镜推荐列表*/
    private val NORMAL_PORTRAIT_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = LABEL_IDS_2ND_FOR_PORTRAIT,
        filters = listOf(
            FilterConstant.TBL_KEY_FILTER_MASTER_SCENERY,
            FilterConstant.TBL_KEY_FILTER_MASTER_PORTRAIT,
            FilterConstant.TBL_KEY_FILTER_MASTER_FAIRY_TALE,
            FilterConstant.TBL_KEY_FILTER_BRIGHT,
            FilterConstant.TBL_KEY_FILTER_FILM
        )
    )

    /*美食拍摄场景非IPU滤镜推荐列表*/
    private val NORMAL_FOOD_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = (LABEL_IDS_2ND_FOR_FOOD + LABEL_IDS_2ND_FOR_DRINKS),
        filters = listOf(
            FilterConstant.TBL_KEY_FILTER_FOOD,
            FilterConstant.TBL_KEY_FILTER_BRIGHT,
            FilterConstant.TBL_KEY_FILTER_MASTER_FAIRY_TALE,
            FilterConstant.TBL_KEY_FILTER_WARM_PORTRAIT_BLUR,
            FilterConstant.TBL_KEY_FILTER_FILM
        )
    )

    /*建筑拍摄场景非IPU滤镜推荐列表*/
    private val NORMAL_BUILDING_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = (LABEL_IDS_2ND_FOR_BUILDING + LABEL_IDS_2ND_FOR_LANDMARKS),
        filters = listOf(
            FilterConstant.TBL_KEY_FILTER_MASTER_SCENERY,
            FilterConstant.TBL_KEY_FILTER_BRIGHT,
            FilterConstant.TBL_KEY_FILTER_COOL,
            FilterConstant.TBL_KEY_FILTER_WARM_PORTRAIT_BLUR,
            FilterConstant.TBL_KEY_FILTER_FILM
        )
    )

    /*风景拍摄场景非IPU滤镜推荐列表*/
    private val NORMAL_LANDSCAPE_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = (
                LABEL_IDS_2ND_FOR_WARM_LANDSCAPE
                        + LABEL_IDS_2ND_FOR_COLD_LANDSCAPE
                        + LABEL_IDS_2ND_FOR_PLANT
                        + LABEL_IDS_2ND_FOR_TRAVEL
                ),
        filters = listOf(
            FilterConstant.TBL_KEY_FILTER_MASTER_SCENERY,
            FilterConstant.TBL_KEY_FILTER_MASTER_FAIRY_TALE,
            FilterConstant.TBL_KEY_FILTER_BRIGHT,
            FilterConstant.TBL_KEY_FILTER_WARM_PORTRAIT_BLUR,
            FilterConstant.TBL_KEY_FILTER_FILM
        )
    )

    /*其他拍摄场景非IPU滤镜推荐列表*/
    private val NORMAL_OTHERS_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = listOf(),
        filters = listOf(
            FilterConstant.TBL_KEY_FILTER_MASTER_SCENERY,
            FilterConstant.TBL_KEY_FILTER_MASTER_PORTRAIT,
            FilterConstant.TBL_KEY_FILTER_MASTER_FAIRY_TALE,
            FilterConstant.TBL_KEY_FILTER_BRIGHT,
            FilterConstant.TBL_KEY_FILTER_NATURE
        )
    )

    /*人像拍摄场景IPU滤镜推荐列表*/
    private var IPU_PORTRAIT_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = LABEL_IDS_2ND_FOR_PORTRAIT,
        filters = listOf(
            FilterConstant.BIN_FRESH,
            FilterConstant.BIN_RETRO,
            FilterConstant.BIN_TRANSPARENT,
            FilterConstant.BIN_BRIGHT,
            FilterConstant.BIN_MEICAM
        )
    )

    /*美食拍摄场景IPU滤镜推荐列表*/
    private var IPU_FOOD_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = (LABEL_IDS_2ND_FOR_FOOD + LABEL_IDS_2ND_FOR_DRINKS),
        filters = listOf(
            FilterConstant.BIN_TASTY,
            FilterConstant.BIN_BRIGHT,
            FilterConstant.BIN_MEICAM,
            FilterConstant.BIN_TRANSPARENT,
            FilterConstant.BIN_WARM
        )
    )

    /*建筑拍摄场景IPU滤镜推荐列表*/
    private var IPU_BUILDING_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = (LABEL_IDS_2ND_FOR_BUILDING + LABEL_IDS_2ND_FOR_LANDMARKS),
        filters = listOf(
            FilterConstant.BIN_RETRO,
            FilterConstant.BIN_TRANSPARENT,
            FilterConstant.BIN_BRIGHT,
            FilterConstant.BIN_HUMANITIES,
            FilterConstant.BIN_COLD
        )
    )

    /*风景拍摄场景偏暖色调IPU滤镜推荐列表*/
    private var IPU_LANDSCAPE_WARM_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = LABEL_IDS_2ND_FOR_WARM_LANDSCAPE,
        filters = listOf(
            FilterConstant.BIN_RETRO,
            FilterConstant.BIN_TRANSPARENT,
            FilterConstant.BIN_BRIGHT,
            FilterConstant.BIN_MEICAM,
            FilterConstant.BIN_WARM
        )
    )


    /*风景拍摄场景其他IPU滤镜推荐列表*/
    private var IPU_LANDSCAPE_OTHERS_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = (
                LABEL_IDS_2ND_FOR_COLD_LANDSCAPE
                        + LABEL_IDS_2ND_FOR_PLANT
                        + LABEL_IDS_2ND_FOR_TRAVEL
                ),
        filters = listOf(
            FilterConstant.BIN_FRESH,
            FilterConstant.BIN_RETRO,
            FilterConstant.BIN_TRANSPARENT,
            FilterConstant.BIN_BRIGHT,
            FilterConstant.BIN_MEICAM
        )
    )

    /*其他拍摄场景IPU滤镜推荐列表*/
    private var IPU_OTHERS_FILTER_RECOMMEND = FilterRecommend(
        secondLabelIds = listOf(),
        filters = listOf(
            FilterConstant.BIN_FRESH,
            FilterConstant.BIN_BRIGHT,
            FilterConstant.BIN_MEICAM,
            FilterConstant.BIN_HUMANITIES,
            FilterConstant.BIN_NATURE
        )
    )

    /*滤镜元数据参数字典，通过滤镜名称获取滤镜参数*/
    private val filterArgumentsInfos: MutableMap<String, AvEffect.Argument> = mutableMapOf()

    /*滤镜显示名称字典，通过滤镜id拿到对应的显示名称*/
    private val filterDisplayNames: MutableMap<String, Filter> = mutableMapOf()

    /**
     * 获取滤镜推荐参数列表
     *
     * @param labelIds 标签ID列表
     * @param editingVM 主vm
     *
     * @return 推荐列表
     */
    fun getFilterRecommendArguments(labelIds: List<Int>, editingVM: EditingVM): List<FilterArgument> {
        val idList: List<String> = getFilterRecommendList(labelIds, editingVM)
        //根据滤镜推荐ID列表得到滤镜显示名称列表和滤镜参数列表
        val filterList: MutableList<FilterArgument> = mutableListOf()
        idList.forEach { filterId ->
            filterDisplayNames[filterId]?.let { filter  ->
                filterArgumentsInfos[filterId]?.let { arg ->
                    filterList.add(FilterArgument(filter, arg))
                }
            }
        }
        GLog.d(TAG, LogFlag.DL) {"filterList: $filterList"}
        return filterList
    }

    /**
     * 获取滤镜推荐列表
     *
     * @param labelIds 标签ID列表
     * @param editingVM 主vm
     *
     * @return 推荐列表
     */
    private fun getFilterRecommendList(labelIds: List<Int>, editingVM: EditingVM): List<String> {
        //初始化获取手机滤镜信息
        val filterEffectInfo = editingVM.sessionProxy.getAvEffectInfo(AvEffect.FilterEffect) ?: let {
            GLog.d(TAG, LogFlag.DL) { "getFilterRecommendList. filterEffectInfo is null. skip." }
            return emptyList()
        }
        //过滤掉一些特殊的不支持的滤镜
        val arguments: List<AvEffect.Argument> = filterEffectInfo.supportedArguments().filter { argument ->
            checkFilterValidation(argument.name)
        }
        //初始化获取当前支持的所有滤镜参数
        arguments.associateBy { it.name }.let {
            filterArgumentsInfos.putAll(it)
        }
        //初始化滤镜名称字典
        arguments.forEach { arg ->
            val extras = arg.extras
            val masterType: Int
            val filterName: String
            if (extras.isNotEmpty()) {
                //ipu滤镜
                val ipuExtraInfo = JsonUtil.fromJson(extras, IpuExtraInfo::class.java)
                val filterIconId = IPU_FILTER_ICONS[arg.name] ?: R.drawable.picture3d_ai_composition_filter_default
                ipuExtraInfo?.let {
                    filterName = it.mFilterName
                    masterType = it.mMasterType
                    filterDisplayNames[arg.name] = Filter(filterName, masterType, filterIconId)
                }
            } else {
                //美摄滤镜
                val filterIconId = NORMAL_FILTER_ICONS[arg.name] ?: R.drawable.picture3d_ai_composition_filter_default
                filterName = FilterConstant.FILTER_CONFIG[arg.name]?.let { ContextGetter.context.getString(it) }.toString()
                masterType = 0
                filterDisplayNames[arg.name] = Filter(filterName, masterType, filterIconId)
            }
        }
        val supportedFilters: MutableList<String> = mutableListOf()
        filterEffectInfo.supportedArguments().map { it.name }.let { idList ->
            supportedFilters.addAll(idList)
        }
        //判断手机是否支持IPU
        val isIPU = if (filterEffectInfo.supportedArguments().isNotEmpty()) {
            filterEffectInfo.supportedArguments()[0].extras.isNotEmpty()
        } else {
            false
        }
        //获取内置初始化的推荐列表
        val recommendList: MutableList<String> = if (isIPU) {
            getIPUFilterRecommendList(labelIds).toMutableList()
        } else {
            getNoIPUFilterRecommendList(labelIds).toMutableList()
        }
        //（过滤后的支持滤镜列表+补偿列表）取出前5个返回
        return (recommendList.filter { supportedFilters.contains(it) } + supportedFilters.filter { !recommendList.contains(it) }).take(RECOMMEND_SIZE)
    }


    /**
     * 检查设备是否支持某一滤镜
     *
     * @param filter 滤镜id
     *
     */
    private fun checkFilterValidation(filter: String): Boolean {
        return checkJiangWenFilterValid(filter)
            && checkHassleFilterValid(filter)
    }

    /**
     * 检查是否支持姜文滤镜，这个是通过后台开关控制的
     *
     * @param filter 滤镜id
     */
    private fun checkJiangWenFilterValid(filter: String): Boolean {
        return (!FilterConstant.CUSTOM_FILTER.contains(filter)
            || isSwitchEnable(FunctionSwitcherConfig.PREFERENCE_JIANGWEN_FILTER_ENABLE))
    }

    /**
     * 检查是否支持哈苏滤镜
     *
     * @param filter 滤镜id
     */
    private fun checkHassleFilterValid(filter: String): Boolean {
        return !FilterConstant.HASSEL_FILTER.contains(filter)
            || ConfigAbilityWrapper.getBoolean(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_HASSEL_WATERMARK)
    }

    /**
     * 获取IPU滤镜推荐列表
     *
     * @param labelIds 标签ID列表
     *
     * @return 推荐列表
     */
    private fun getIPUFilterRecommendList(labelIds: List<Int>): List<String> {
        if (IPU_PORTRAIT_FILTER_RECOMMEND.isMatch(labelIds)) {
            return IPU_PORTRAIT_FILTER_RECOMMEND.filters
        }
        if (IPU_FOOD_FILTER_RECOMMEND.isMatch(labelIds)) {
            return IPU_FOOD_FILTER_RECOMMEND.filters
        }
        if (IPU_BUILDING_FILTER_RECOMMEND.isMatch(labelIds)) {
            return IPU_BUILDING_FILTER_RECOMMEND.filters
        }
        if (IPU_LANDSCAPE_WARM_FILTER_RECOMMEND.isMatch(labelIds)) {
            return IPU_LANDSCAPE_WARM_FILTER_RECOMMEND.filters
        }
        if (IPU_LANDSCAPE_OTHERS_FILTER_RECOMMEND.isMatch(labelIds)) {
            return IPU_LANDSCAPE_OTHERS_FILTER_RECOMMEND.filters
        }
        return IPU_OTHERS_FILTER_RECOMMEND.filters
    }

    /**
     * 获取非IPU滤镜推荐列表
     *
     * @param labelIds 标签ID列表
     *
     * @return 推荐列表
     */
    private fun getNoIPUFilterRecommendList(labelIds: List<Int>): List<String> {
        if (NORMAL_PORTRAIT_FILTER_RECOMMEND.isMatch(labelIds)) {
            return NORMAL_PORTRAIT_FILTER_RECOMMEND.filters
        }
        if (NORMAL_FOOD_FILTER_RECOMMEND.isMatch(labelIds)) {
            return NORMAL_FOOD_FILTER_RECOMMEND.filters
        }
        if (NORMAL_BUILDING_FILTER_RECOMMEND.isMatch(labelIds)) {
            return NORMAL_BUILDING_FILTER_RECOMMEND.filters
        }
        if (NORMAL_LANDSCAPE_FILTER_RECOMMEND.isMatch(labelIds)) {
            return NORMAL_LANDSCAPE_FILTER_RECOMMEND.filters
        }
        return NORMAL_OTHERS_FILTER_RECOMMEND.filters
    }
}