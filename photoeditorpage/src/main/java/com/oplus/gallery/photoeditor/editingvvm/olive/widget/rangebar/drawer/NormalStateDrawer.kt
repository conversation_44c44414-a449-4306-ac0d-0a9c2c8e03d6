/*********************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - NormalStateDrawer.kt
 ** Description: 正常态绘制器
 ** Version: 1.0
 ** Date: 2025/6/16
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>        2025/6/16      1.0        created
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.drawer

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.view.MotionEvent
import android.view.View
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.RangeBarView.Companion.ANIMATION_DURATION_FADE_IN
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.RangeBarView.Companion.ANIMATION_DURATION_SCALE_DOWN
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.RangeBarView.Companion.MAX_ALPHA
import com.oplus.gallery.photoeditor.editingvvm.olive.widget.rangebar.RangeBarView.SelectAreaEvent
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * 正常状态绘制器
 * 负责绘制正常状态下的刻度线、选择器、文本和指示器
 * 使用基于时间的动画实现平滑的过渡效果
 */
class NormalStateDrawer(view: View, startTime: Long) : BaseStateDrawer(view, startTime) {
    /**
     * 选择器长度
     */
    private var selectorLineLength = view.resources.getDimension(R.dimen.olive_edit_range_bar_selector_length)

    /**
     * 选中区域指示器和文本之间的距离（单边）
     */
    private val indicatorTextPadding = view.resources.getDimension(R.dimen.olive_edit_range_bar_indicator_line_padding)

    /**
     * 选中区域文本和指示器之间最小距离（单边）
     */
    private val minTextPadding = view.resources.getDimension(R.dimen.olive_edit_range_bar_indicator_min_text_padding)

    /**
     * 触摸事件热区大小
     */
    private val touchSlop = view.resources.getDimension(R.dimen.olive_edit_range_bar_indicator_touch_slop)

    /**
     * 选择器文本
     */
    var selectedAreaText = view.resources.getString(R.string.photoeditorpage_olive_slow_motion_scope)

    /**
     * 选择器区间相对正常区间的倍率
     */
    var selectedAreaScaleRate = 1f

    /**
     * 开始的位置(0f-1f)
     */
    var startRatio: Float = 0f

    /**
     * 结束的位置(0f-1f)
     */
    var endRatio: Float = 0f

    /**
     * 是否正在拖拽中
     */
    val isDragging get() = isDraggingStart || isDraggingEnd

    /**
     * 是否正在拖动开始位置
     */
    private var isDraggingStart = false

    /**
     * 是否正在拖动结束位置
     */
    private var isDraggingEnd = false

    /**
     * 选择器画笔(两个把手)
     */
    private val selectorPaint = Paint().apply {
        color = Color.WHITE
        strokeWidth = view.resources.getDimension(R.dimen.olive_edit_range_bar_selector_width)
        strokeCap = Paint.Cap.ROUND
    }

    /**
     * 选中区域画笔
     */
    private val selectedAreaPaint = Paint().apply {
        color = view.resources.getColor(R.color.picture3d_olive_slow_motion_range_bar_select_area_color)
        strokeWidth = view.resources.getDimension(R.dimen.olive_edit_range_bar_selector_width)
    }

    /**
     * 选中区域文本的指示器画笔
     */
    private val indicatorLinePaint = Paint().apply {
        color = view.resources.getColor(R.color.picture3d_olive_slow_motion_range_bar_select_indicator_color)
        strokeWidth = view.resources.getDimension(R.dimen.olive_edit_range_bar_indicator_line_width)
    }

    /**
     * 选中区域文本画笔
     */
    var textPaint = Paint().apply {
        color = view.resources.getColor(R.color.picture3d_olive_slow_motion_range_bar_select_text_color)
        textSize = view.resources.getDimension(R.dimen.olive_edit_range_bar_selector_text_size)
        textAlign = Paint.Align.CENTER
    }

    /**
     * 缓动插值器
     */
    private val fastOutSlowInInterpolator = FastOutSlowInInterpolator()

    /**
     * 选择更新回调接口
     */
    fun interface SelectedAreaChangeListener {
        /**
         * 选择区域的事件
         * @param event 选择区域的事件类型
         * @param isSelectStart 是否选择开始的位置
         * @param startPosition 开始的位置
         * @param endPosition 结束的位置
         */
        fun onEvent(event: SelectAreaEvent, isSelectStart: Boolean, startPosition: Float, endPosition: Float)
    }

    var selectedAreaChangeListener: SelectedAreaChangeListener? = null

    override fun draw(canvas: Canvas, rectF: RectF) {
        // 计算动画进度
        val scaleMoveProgress = fastOutSlowInInterpolator.getInterpolation(
            calculateProgress(ANIMATION_DURATION_SCALE_DOWN, getElapsedTime())
        )
        val fadeProgress = fastOutSlowInInterpolator.getInterpolation(
            calculateProgress(ANIMATION_DURATION_FADE_IN, getElapsedTime())
        )

        // 计算当前刻度线的位置
        val currentScaleTop = calculateScalePosition(scaleMoveProgress, view.height / 2f)

        val (scaleCount, scaleStart) = getScaleCountAndStartX(rectF)

        // 绘制基础刻度线
        drawBaseScales(canvas, currentScaleTop, scaleCount, scaleStart)

        // 绘制选中区域
        val (selectedAreaStartX, selectedAreaEndX) = calculateSelectedAreaBounds(scaleCount, scaleStart)
        drawSelectedArea(canvas, selectedAreaStartX, selectedAreaEndX, currentScaleTop, fadeProgress)

        // 绘制选择器
        val selectorStartY = view.height / 2 - selectorLineLength / 2
        drawSelector(canvas, selectedAreaStartX, selectedAreaEndX, selectorStartY, fadeProgress)

        // 绘制文本和指示器
        drawTextAndIndicators(canvas, selectedAreaStartX, selectedAreaEndX, selectorStartY, fadeProgress)

        // 如果动画未完成，请求下一帧重绘
        if (scaleMoveProgress < 1f || fadeProgress < 1f) {
            view.invalidate()
        }
    }

    /**
     * 计算刻度线的当前位置
     * @param scaleMoveProgress 移动动画进度
     * @param centerY 中心Y坐标
     * @return Float 当前刻度线的Y坐标
     */
    private fun calculateScalePosition(scaleMoveProgress: Float, centerY: Float): Float {
        return if (scaleMoveProgress < 1f) {
            val targetScaleTop = centerY - scaleLength / 2
            targetScaleTop + (centerY - targetScaleTop) * scaleMoveProgress
        } else {
            centerY
        }
    }

    /**
     * 绘制基础刻度线
     */
    private fun drawBaseScales(canvas: Canvas, currentScaleTop: Float, scaleCount: Int, scaleStart: Float) {
        scalePaint.alpha = MAX_ALPHA
        for (i in 0 until scaleCount) {
            val x = scaleStart + i * scaleSpacing
            canvas.drawLine(x, currentScaleTop, x, currentScaleTop + scaleLength, scalePaint)
        }
    }

    /**
     * 计算选中区域的边界
     * @return Pair<Float, Float> 返回(起始X坐标, 结束X坐标)
     */
    private fun calculateSelectedAreaBounds(scaleCount: Int, scaleStart: Float): Pair<Float, Float> {
        val scaleAreaWidth = scaleCount * scaleSpacing
        val selectedAreaStartX = scaleStart + scaleAreaWidth * startRatio
        val selectedAreaEndX = scaleStart + scaleAreaWidth * endRatio
        return Pair(selectedAreaStartX, selectedAreaEndX)
    }

    /**
     * 绘制选中区域
     */
    private fun drawSelectedArea(
        canvas: Canvas,
        selectedAreaStartX: Float,
        selectedAreaEndX: Float,
        currentScaleTop: Float,
        fadeProgress: Float,
    ) {
        // 绘制选中区域背景
        val selectedAreaRect = RectF(selectedAreaStartX, currentScaleTop, selectedAreaEndX, currentScaleTop + scaleLength)
        selectedAreaPaint.alpha = MAX_ALPHA
        canvas.drawRect(selectedAreaRect, selectedAreaPaint)

        val selectorCenterX = (selectedAreaStartX + selectedAreaEndX) / 2

        // 绘制选中区域内的刻度线
        val selectedAreaSpacing = scaleSpacing * (1 + (selectedAreaScaleRate - 1) * fadeProgress)

        val selectedScaleCount = (abs(selectedAreaEndX - selectedAreaStartX) / selectedAreaSpacing).roundToInt()

        for (i in 0 until selectedScaleCount) {
            val rightX = selectorCenterX + i * selectedAreaSpacing
            val leftX = selectorCenterX - i * selectedAreaSpacing
            if (leftX >= selectedAreaStartX) {
                canvas.drawLine(leftX, currentScaleTop, leftX, currentScaleTop + scaleLength, scalePaint)
            }
            if (rightX <= selectedAreaEndX) {
                canvas.drawLine(rightX, currentScaleTop, rightX, currentScaleTop + scaleLength, scalePaint)
            }
        }
    }

    /**
     * 绘制选择器
     */
    private fun drawSelector(canvas: Canvas, selectedAreaStartX: Float, selectedAreaEndX: Float, selectorStartY: Float, fadeProgress: Float) {
        selectorPaint.alpha = (fadeProgress * MAX_ALPHA).toInt()

        // 绘制开始线
        canvas.drawLine(selectedAreaStartX, selectorStartY, selectedAreaStartX, selectorStartY + selectorLineLength, selectorPaint)

        // 绘制结束线
        canvas.drawLine(selectedAreaEndX, selectorStartY, selectedAreaEndX, selectorStartY + selectorLineLength, selectorPaint)
    }

    /**
     * 绘制文本和指示器
     */
    private fun drawTextAndIndicators(
        canvas: Canvas,
        selectedAreaStartX: Float,
        selectedAreaEndX: Float,
        selectorStartY: Float,
        fadeProgress: Float
    ) {
        // 设置文本画笔透明度
        textPaint.alpha = (fadeProgress * MAX_ALPHA).toInt()

        // 计算文本位置
        val textBounds = Rect()
        textPaint.getTextBounds(selectedAreaText, 0, selectedAreaText.length, textBounds)
        val selectedAreaWidth = selectedAreaEndX - selectedAreaStartX
        if (selectedAreaWidth < textBounds.width() + minTextPadding * 2) {
            return
        }
        val textX = selectedAreaStartX + selectedAreaWidth / 2
        val textY = selectorStartY + textBounds.height()

        // 绘制文本
        canvas.drawText(selectedAreaText, textX, textY, textPaint)

        // 绘制指示器
        indicatorLinePaint.alpha = (fadeProgress * MAX_ALPHA).toInt()
        val indicatorY = textY + (textPaint.ascent() + textPaint.descent()) / 2 + indicatorLinePaint.strokeWidth / 2
        val indicatorLeftX = textX - textBounds.width() / 2 - indicatorTextPadding
        val indicatorRightX = textX + textBounds.width() / 2 + indicatorTextPadding

        // 绘制指向开始选择器的指示器
        canvas.drawLine(indicatorLeftX, indicatorY, selectedAreaStartX, indicatorY, indicatorLinePaint)

        // 绘制指向结束选择器的指示器
        canvas.drawLine(indicatorRightX, indicatorY, selectedAreaEndX, indicatorY, indicatorLinePaint)
    }

    fun onTouchEvent(event: MotionEvent, rectF: RectF): Boolean {
        val (scaleCount, scaleStartX) = getScaleCountAndStartX(rectF)
        val scaleAreaWidth = scaleCount * scaleSpacing

        return when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 检测选择器点击
                val startX = scaleStartX + startRatio * scaleAreaWidth
                val endX = scaleStartX + endRatio * scaleAreaWidth

                isDraggingStart = abs(event.x - startX) < touchSlop
                isDraggingEnd = abs(event.x - endX) < touchSlop
                when {
                    isDraggingStart -> selectedAreaChangeListener?.onEvent(SelectAreaEvent.START, true, startRatio, endRatio)
                    isDraggingEnd -> selectedAreaChangeListener?.onEvent(SelectAreaEvent.START, false, startRatio, endRatio)
                }
                true
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDraggingStart) {
                    // 拖动开始选择器（保持原有边界逻辑）
                    val newRatio = ((event.x - scaleStartX) / scaleAreaWidth).coerceIn(0f, 1f)
                    startRatio = newRatio
                    if (newRatio >= endRatio) {
                        endRatio = newRatio
                    }
                    selectedAreaChangeListener?.onEvent(SelectAreaEvent.UPDATED, true, startRatio, endRatio)
                    view.invalidate()
                    true
                } else if (isDraggingEnd) {
                    // 拖动结束选择器（保持原有边界逻辑）
                    val newRatio = ((event.x - scaleStartX) / scaleAreaWidth).coerceIn(0f, 1f)
                    endRatio = newRatio
                    if (newRatio <= startRatio) {
                        startRatio = newRatio
                    }
                    selectedAreaChangeListener?.onEvent(SelectAreaEvent.UPDATED, false, startRatio, endRatio)
                    view.invalidate()
                    true
                } else {
                    false
                }
            }

            MotionEvent.ACTION_UP -> {
                if (isDraggingStart || isDraggingEnd) {
                    selectedAreaChangeListener?.onEvent(SelectAreaEvent.END, isDraggingStart, startRatio, endRatio)
                }
                isDraggingStart = false
                isDraggingEnd = false
                true
            }

            MotionEvent.ACTION_CANCEL -> {
                isDraggingStart = false
                isDraggingEnd = false
                true
            }

            else -> true
        }
    }
}