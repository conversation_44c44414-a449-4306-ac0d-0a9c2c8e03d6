/*********************************************************************************
 ** Copyright (C), 2023-2033, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : TransformEffectComponent.kt
 ** Description : 裁剪页面动效View的组件
 ** Version     : 1.0
 ** Date        : 2024/07/01
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/07/01      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.sectioncomponent.component

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.RectF
import android.view.LayoutInflater
import android.view.View
import androidx.core.graphics.toRect
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.cache.memorycache.BitmapPools
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.imagequalityenhance.ImageQualityEnhanceSection
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import kotlin.system.measureTimeMillis

/**
 * 裁剪页面动效View的组件
 */
internal class TransformEffectComponent(
    sectionBus: ISectionBus<EditingFragment, EditingVM>,
    layoutInflater: LayoutInflater,
    attachView: (View) -> Unit,
    detachView: (View) -> Unit
) : BaseEffectComponent(sectionBus, layoutInflater, attachView, detachView) {
    /**
     * Marked by dandy 这里放Section处理不是很合理，应该要放到VM中处理完再通知给Section
     * 下一笔修改：VM中处理这些图像，监听ImageQualityEnhanceStatus，完后只通知HIDE/SHOW，也就是说effect只由这个HIDE/SHOW来驱动显示或消失
     *
     * 更新 动效的区域、图片大小， 并返回用于渲染的Bitmap
     * 画质强强页面不需要更新区域；裁剪页面由于显示区域会变化，所以动效的显示区域及Bitmap需要变更
     *
     * 图片裁剪页面，裁剪的算法见以下文档：
     * https://odocs.myoas.com/docs/m4kMLxRp2zUWNeqD/ 《裁剪页面-画质增强-扫描动画-Bitmap的裁剪》访问密码 tpakpk
     *
     * @param src 原显示的图
     * @param renderBitmapCallback 结果回调，返回用于渲染的bitmap
     * Marked by dandy 返回用于渲染的Bitmap看怎么回收？
     */
    override fun updateEffectArea(src: Bitmap, renderBitmapCallback: (Bitmap) -> Unit) {
        // 画质增强页面 直接使用原图让sdk渲染，无需更新渲染区域和bitmap

        val previewAnimationProperties = sectionBus.viewBus.get<PreviewAnimationProperties>(
            TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
        ) ?: let {
            // 裁剪页面 gestureAnimator都没有，则不返回渲染的bitmap，也就不会渲染
            GLog.w(ImageQualityEnhanceSection.TAG) { "[updateEffectArea] gestureAnimator is null, skip show effect." }
            return
        }
        measureTimeMillis {
            // 图片自身坐标系中 图片的矩形区域
            val originalImgRectF = RectF(0f, 0f, src.width.toFloat(), src.height.toFloat())
            if (originalImgRectF.isEmpty) {
                // 原始宽高 <= 0 , 异常，不返回渲染的bitmap
                GLog.w(ImageQualityEnhanceSection.TAG) { "[updateEffectArea] originalImgRectF.isEmpty=$originalImgRectF, skip show effect." }
                return
            }
            // 手机坐标系中 缩放裁剪后的 图片的矩形区域
            val finalImgRectF = previewAnimationProperties.contentDrawingOutBound.final
            if (finalImgRectF.isEmpty) {
                // 图片显示的宽高 <= 0 , 异常，不返回渲染的bitmap
                GLog.w(ImageQualityEnhanceSection.TAG) { "[updateEffectArea] finalImgRectF.isEmpty=$finalImgRectF, skip show effect." }
                return
            }
            // 手机坐标系中 缩放裁剪后的 展示出来的那部分矩形区域
            val finalImgShowRectF = previewAnimationProperties.clipRect.current

            // 图片在手机坐标系中的矩形大小 映射到 图片自身坐标系中的矩形大小 缩放的倍数
            val ratio = finalImgRectF.width() / originalImgRectF.width()
            // 以下计算的是 手机坐标系中缩放裁剪后展示出来的那个矩形区域 是在图片自身坐标系中的哪个矩形区域
            val l = (finalImgShowRectF.left - finalImgRectF.left) / ratio
            val t = (finalImgShowRectF.top - finalImgRectF.top) / ratio
            val r = l + finalImgShowRectF.width() / ratio
            val b = t + finalImgShowRectF.height() / ratio
            val originalShowRectF = RectF(l, t, r, b)
            val originalShowRectWidth = originalShowRectF.width().toInt()
            val originalShowRectHeight = originalShowRectF.height().toInt()

            // 裁剪出原图中展示出来的那部分 图片，用于渲染
            val renderBmp = BitmapPools.getBitmap(originalShowRectWidth, originalShowRectHeight, Bitmap.Config.ARGB_8888)
                ?: Bitmap.createBitmap(originalShowRectWidth, originalShowRectHeight, Bitmap.Config.ARGB_8888)
            val clipCanvas = Canvas(renderBmp)
            clipCanvas.drawBitmap(src, originalShowRectF.toRect(), Rect(0, 0, originalShowRectWidth, originalShowRectHeight), null)

            // 设置动效的显示区域和图片在手机中显示的区域一致
            effectView?.contentDrawingOutBoundGetter = {
                finalImgShowRectF
            }
            effectView?.setOriginImageSize(renderBmp.width, renderBmp.height)

            renderBitmapCallback(renderBmp)
        }.also {
            GLog.d(ImageQualityEnhanceSection.TAG) { "[updateEffectArea] cost time $it ms." }
        }
    }
}