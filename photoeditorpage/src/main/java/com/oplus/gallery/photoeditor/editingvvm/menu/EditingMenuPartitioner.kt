/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - EditingMenuPartitioner.kt
 ** Description: 编辑菜单分区器，负责对菜单进行分区(基础区和智能区）
 ** Version: 1.0
 ** Date : 2024/4/29
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:EditingMenuPartitioner
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  xiewu<PERSON>e@Apps.Gallery3D      2024/04/29    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.menu

import android.content.Context
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.systemcore.GallerySystemProperties
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_BEST_TAKE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEBLUE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_DEREFLECTION
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_GRAFFITI
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_AI_LIGHTING
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_RM_IMAGE_QUALITY_ENHANCE
import com.oplus.gallery.photoeditor.R

/**
 * 编辑菜单分区器，负责对菜单进行分区(基础区和智能区）
 */
internal class EditingMenuPartitioner(private val context: Context) {

    private val debugMenuCmd
        get() = GallerySystemProperties.getInt("debug.photoEdit.menu.model", DEBUG_PHOTO_EDIT_MENU_DEFAULT)

    /**
     * 对菜单进行分区，分别返回基础区和智能区的菜单列表
     * @param supportMenuList 支持的菜单列表，此菜单是已过滤过的，即不会同时出现“AI修复”和“修复”，“AI消除”和“消除”
     * @return 返回分区后的菜单列表
     */
    fun splitMenu(supportMenuList: List<EditorMenuItemViewData>): List<List<EditorMenuItemViewData>> {
        return  if (isSupportPartition(supportMenuList)) {
            splitMenuInternal(supportMenuList)
        } else {
            listOf(supportMenuList)
        }
    }

    /**
     * 只有需要分区才会进入这个逻辑
     */
    private fun splitMenuInternal(menuList: List<EditorMenuItemViewData>): List<List<EditorMenuItemViewData>> {
        val basicMenuList = mutableListOf<EditorMenuItemViewData>()
        val aiMenuList = mutableListOf<EditorMenuItemViewData>()

        basicMenuList.add(createAIEntranceItemViewData())    // 对于需要分区的菜单，主菜单项上需要显示副菜单入口
        menuList.forEach {
            when {
                it.isAIMenuItem() -> aiMenuList.add(it)
                else -> basicMenuList.add(it)
            }
        }
        return listOf(basicMenuList, aiMenuList)
    }

    /**
     * AI类菜单项首页入口的ItemViewData
     */
    private fun createAIEntranceItemViewData(): EditorMenuItemViewData {
        return EditorUIConfig.initEditorMenuAdapterData(
            context,
            R.array.picture3d_editor_array_ai_assistant_id_array,
            R.array.picture3d_editor_array_ai_assistant_icon_array,
            R.array.picture3d_editor_array_ai_assistant_text_array,
            R.array.picture3d_editor_array_ai_assistant_item_id_array
        )[0]
    }

    private fun isSupportPartition(supportMenuList: List<EditorMenuItemViewData>): Boolean {
        // 调试模式，强制菜单分区
        if (debugMenuCmd == DEBUG_PHOTO_EDIT_MENU_FORCE_PARTITION) {
            GLog.d(TAG) { "[isSupportAIPartition] enter debug model, force partition" }
            return true
        }
        // 调试模式，强制菜单不分区
        if (debugMenuCmd == DEBUG_PHOTO_EDIT_MENU_NO_PARTITION) {
            GLog.d(TAG) { "[isSupportAIPartition] enter debug model, don't partition" }
            return false
        }
        return supportMenuList.count {
            it.isAIMenuItem()
        } >= MIN_PARTITION_AI_ITEM_COUNT
    }

    private fun EditorMenuItemViewData.isAIMenuItem(): Boolean {
        return when (viewId) {
            R.id.strategy_image_quality_enhance,
            R.id.strategy_ai_deblur,
            R.id.strategy_ai_dereflection,
            R.id.strategy_ai_eliminate,
            R.id.strategy_rm_specific_image_quality_enhance,
            R.id.strategy_rm_ai_scenery,
            R.id.strategy_ai_graffiti,
            R.id.strategy_ai_composition,
            R.id.strategy_group_photo,
            R.id.strategy_ai_best_take,
            R.id.strategy_ai_lighting,
            R.id.strategy_ai_deglare,
            R.id.strategy_ai_defog -> true

            else -> false
        }
    }

    companion object {
        private const val TAG = "EditingMenuPartitioner"
        private const val MIN_PARTITION_AI_ITEM_COUNT = 3

        /**
         * 默认的分区模式：adb shell setprop debug.photoEdit.menu.model 0
         */
        private const val DEBUG_PHOTO_EDIT_MENU_DEFAULT = 0

        /**
         * 强制菜单分区：adb shell setprop debug.photoEdit.menu.model 1
         */
        private const val DEBUG_PHOTO_EDIT_MENU_FORCE_PARTITION = 1

        /**
         * 强制菜单不分区：adb shell setprop debug.photoEdit.menu.model 2
         */
        private const val DEBUG_PHOTO_EDIT_MENU_NO_PARTITION = 2

        /**
         * 获取默认的分区类型，分区或不分区，取决于支持AI功能项的数量 >= 3
         * 分区 = true，不区分 = false
         */
        val IS_MENU_PARTITION: Boolean by lazy { getDefaultMenuPartition() }

        private fun getDefaultMenuPartition(): Boolean {
            return listOf(
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_DEBLUE),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_DEREFLECTION),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_IMAGE_QUALITY_ENHANCE),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_DEVICE_SUPPORT_AI_ELIMINATE),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_RM_IMAGE_QUALITY_ENHANCE),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_GRAFFITI),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_BEST_TAKE),
                ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_AI_LIGHTING)
            ).filter { it }.size >= MIN_PARTITION_AI_ITEM_COUNT
        }
    }
}