/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveVideoEdgeEditorImp
 ** Description: OliveVideoEdgeEditorImp
 ** Version: 1.0
 ** Date : 2024/11/26
 ** Author: zhangweichao@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** zhangweichao@Apps.Gallery3D      2024/11/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge

import android.graphics.RectF
import android.util.Size
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsVideoClip
import com.meicam.sdk.NvsVideoFx
import com.meicam.sdk.NvsVideoTrack
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.math.MathUtil.alignment4
import com.oplus.gallery.foundation.util.math.MathUtil.alignment2
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.FxNameKey.TRANSFORM_2D
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.Transform2DPropertyKey.ANCHOR_X
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.Transform2DPropertyKey.ANCHOR_Y
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.Transform2DPropertyKey.SCALE_X
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.constant.OliveVideoEditEffectsProperty.Transform2DPropertyKey.SCALE_Y
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.app.AppConstants.Number.NUMBER_2

class OliveVideoEdgeEditorImp(
    private val nvsContext: NvsStreamingContext,
    private val nvsTimeline: NvsTimeline?,
    private val nvsVideoTrack: NvsVideoTrack?,
    private val nvsVideoClip: NvsVideoClip?
) : IOliveVideoEdgeEditor  {
    private var nvsVideoFx: NvsVideoFx? = null
    private var nvsVideoScaleFx: NvsVideoFx? = null
    /**
     * 当前对视频内容的放大倍数
     */
    override var scalePercent: Float = DEFAULT_DISPLAY_PERCENT

    private var onVideoSizeChanged: ((w: Int, h: Int) -> Unit)? = null

    override var oliveVideoEdgeEditorEnable: Boolean = true
        set(value) {
            field = value
            if (value.not()) {
                resetScaleToDefault()
            }
        }

    override fun removeFuzzyEdge(displayPercent: Float) {
        if (displayPercent <= 0) return
        val videoClip = nvsVideoClip ?: return

        val videoSize = getVideoSize()
        if ((videoSize.width == 0) || (videoSize.height == 0)) {
            GLog.e(TAG, LogFlag.DL) { "[scaleVideoContent] video's width or height is 0" }
            return
        }

        // 更改视频总体尺寸，需要进行字节对齐，美摄要求宽4字节对齐，高2字节对齐，加水印要求高4字节对齐
        val width = (videoSize.width * displayPercent).alignment4()
        val height = (videoSize.height * displayPercent).alignment2()

        // 裁切，原点在视频的中心。
        val number2 = NUMBER_2.toDouble()
        val left = -width / number2
        val right = width / number2
        val top = height / number2
        val bottom = -height / number2

        //裁切视频边缘模糊部分
        videoClip.setRawFilterProcessesMode(NvsVideoClip.RAW_FILTER_PROCESSES_MODE_VARIANT_IMAGE_WITH_FILL_MODE_USED)
        videoClip.enablePropertyVideoFx(true)
        if (nvsVideoFx == null) {
            nvsVideoFx = videoClip.appendRawBuiltinFx(OliveVideoEditEffectsProperty.FxNameKey.CROP)
        }
        nvsVideoFx?.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_LEFT, left)
        nvsVideoFx?.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_RIGHT, right)
        nvsVideoFx?.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_TOP, top)
        nvsVideoFx?.setFloatVal(OliveVideoEditEffectsProperty.TransformCropPropertyKey.BOUNDING_BOTTOM, bottom)

        nvsContext.seekTimeline(nvsTimeline, 0, NvsStreamingContext.VIDEO_PREVIEW_SIZEMODE_LIVEWINDOW_SIZE, 0)
        changeVideoSize(width, height)
    }

    override fun scaleVideoContent(contentRect: RectF?, scale: Float) {
        if (oliveVideoEdgeEditorEnable.not()) {
            GLog.e(TAG, LogFlag.DL) { "disable scale" }
            return
        }
        // 检查原始视频尺寸是否有效
        val videoSize = getVideoSize()
        if ((videoSize.width == 0) || (videoSize.height == 0)) {
            GLog.e(TAG, LogFlag.DL) { "video's width or height is 0" }
            return
        }

        if (scale.compareTo(DEFAULT_DISPLAY_PERCENT) <= 0) {
            GLog.e(TAG, LogFlag.DL) { "[scaleVideoContent] scale is default no need to scale" }
            return
        }

        resetScaleToDefault()
        val contentVideoRect = if (contentRect?.isEmpty?.not() == true) {
            contentRect
        } else {
            RectF(0f, 0f, videoSize.width.toFloat(), videoSize.height.toFloat())
        }

        // 应用 2D 变换
        nvsVideoClip?.appendBuiltinFx(TRANSFORM_2D)?.let { transform2D ->
            nvsVideoScaleFx = transform2D
            transform2D.regional = true

            // 计算原始视频中心点
            val originalCenterX = videoSize.width / NUMBER_2
            val originalCenterY = videoSize.height / NUMBER_2

            // 计算归一化的区域坐标(NDC)
            val normalizedLeft = (contentVideoRect.left - originalCenterX) / originalCenterX
            val normalizedRight = (contentVideoRect.right - originalCenterX) / originalCenterX
            val normalizedTop = (originalCenterY - contentVideoRect.top) / originalCenterY
            val normalizedBottom = (originalCenterY - contentVideoRect.bottom) / originalCenterY

            // 设置变换区域
            transform2D.region = floatArrayOf(
                normalizedLeft, normalizedTop,      // 左上
                normalizedLeft, normalizedBottom,    // 左下
                normalizedRight, normalizedBottom,   // 右下
                normalizedRight, normalizedTop       // 右上
            )

            // 计算并设置锚点
            val anchorX = ((contentVideoRect.centerX() - originalCenterX) / NUMBER_2).toDouble()
            val anchorY = ((contentVideoRect.centerY() - originalCenterY) / NUMBER_2).toDouble()
            transform2D.setFloatVal(ANCHOR_X, anchorX)
            transform2D.setFloatVal(ANCHOR_Y, anchorY)

            // 应用缩放
            transform2D.setFloatVal(SCALE_X, scale.toDouble())
            transform2D.setFloatVal(SCALE_Y, scale.toDouble())
            scalePercent = scale
        } ?: GLog.e(TAG, LogFlag.DL) { "[scaleVideoContent] Failed to apply 2D transform: videoClip or transform2D is null" }
    }

    /**
     * 获取视频当前大小
     */
    private fun getVideoSize(): Size {
        return nvsTimeline?.videoRes?.let { videoRes ->
            Size(videoRes.imageWidth, videoRes.imageHeight)
        } ?: Size(0, 0)
    }

    /**
     * 恢复视频比例到默认大小
     */
    override fun resetScaleToDefault() {
        removeFx(nvsVideoClip, nvsVideoScaleFx)
        scalePercent = DEFAULT_DISPLAY_PERCENT
    }

    private fun changeVideoSize(width: Int, height: Int) {
        val newWidth = (width / AppConstants.Number.NUMBER_4f).toInt() * AppConstants.Number.NUMBER_4
        val newHeight = (height / AppConstants.Number.NUMBER_2f).toInt() * AppConstants.Number.NUMBER_2
        nvsTimeline?.changeVideoSize(newWidth, newHeight)
        onVideoSizeChanged?.invoke(newWidth, newHeight)
        GLog.d(TAG, LogFlag.DL, "changeVideoSize width * height = $width *  $height newWidth * newHeight=$newWidth * $newHeight")
    }

    override fun setOnVideoSizeChangeListener(listener: ((w: Int, h: Int) -> Unit)?) {
        onVideoSizeChanged = listener
    }

    override fun release() {
        removeAllFx()
    }

    private fun removeFx(clip: NvsVideoClip?, videoFx: NvsVideoFx?) {
        val count = clip?.fxCount ?: return
        for (i in 0 until count) {
            if (clip.getFxByIndex(i) == videoFx) {
                clip.removeFx(i)
            }
        }
    }

    override fun removeAllFx() {
        val track: NvsVideoTrack = nvsTimeline?.getVideoTrackByIndex(0) ?: return
        for (i in 0 until track.clipCount) {
            val clip = track.getClipByIndex(i) ?: continue
            clip.removeAllVideoFx()
        }
    }

    companion object {
        private const val TAG = "OliveVideoEdgeEditorImp"
        const val DEFAULT_DISPLAY_PERCENT = 1.0f
    }
}