/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AiCompositionResult
 ** Description: AI构图(灵感构图)算法处理结果
 **
 ** Version: 1.0
 ** Date: 2024/11/06
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  chen<PERSON><PERSON>@oppo.com  2024/11/06  1.0        build this module
 *********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.aicomposition.bean

import android.graphics.Bitmap
import com.oplus.aiunit.core.protocol.common.ErrorCode
import com.oplus.gallery.photoeditor.constant.AiUnitErrorCode

/**
 * AI构图(灵感构图)算法处理结果
 */
data class AiCompositionResult(
    /**
     * 结果码
     */
    val code: Int = -1,

    /**
     * 云侧返回的特殊结果码
     */
    val specificCode: Int = -1,

    /**
     * 结果描述信息
     */
    val msg: String? = null,

    /**
     * 基于'内容图'叠加了自动调节效果的bitmap
     */
    var bitmap: Bitmap? = null,

    /**
     * 旋转角度
     */
    val rotation: Float = 0f,

    /**
     * 垂直梯形校正角度
     */
    val tiltX: Float = 0f,

    /**
     * 水平梯形校正角度
     */
    val tiltY: Float = 0f,

    /**
     * 构图结果
     */
    val cropInfoList: List<AiCompositionCropInfo>,
) {
    companion object {
        const val TAG = "AiCompositionResult"
    }
}


fun AiCompositionResult.isSuccess(): Boolean {
    return code == ErrorCode.kErrorNone.value()
}

/**
 * 构图异常类型
 * 1、网络异常
 * 2、构图失败
 * 3、照片存在风险
 * 4、该功能当前使用人数过多
 * 5、已达当天使用次数限制
 */
fun AiCompositionResult.errorType(): AiCompositionErrorType {
    return when {
        code == ErrorCode.KErrorNetworkUnavailable.value() ||
                code == ErrorCode.kErrorTimeOut.value() ||
                code == ErrorCode.kErrorNoInternet.value() -> AiCompositionErrorType.ERROR_NETWORK

        specificCode == AiUnitErrorCode.SERVER_UNKNOWN_ERROR.code -> AiCompositionErrorType.ERROR_COMPOSITION_FAIL
        specificCode == AiUnitErrorCode.CONTENT_REFUSE.code -> AiCompositionErrorType.ERROR_SENSITIVE_CONTENT
        specificCode == AiUnitErrorCode.ERROR_SERVICE_BUSY.code -> AiCompositionErrorType.ERROR_SERVICE_BUSY
        specificCode == AiUnitErrorCode.ERROR_REACH_DAY_LIMIT.code -> AiCompositionErrorType.ERROR_REACH_DAY_LIMIT
        else -> AiCompositionErrorType.ERROR_COMPOSITION_FAIL
    }
}

enum class AiCompositionErrorType {
    /**
     * 网络异常
     */
    ERROR_NETWORK,

    /**
     * 构图失败
     */
    ERROR_COMPOSITION_FAIL,

    /**
     * 照片存在风险，无法提供构图服务
     */
    ERROR_SENSITIVE_CONTENT,

    /**
     * 该功能当前使用人数过多
     */
    ERROR_SERVICE_BUSY,

    /**
     * 达到每日使用次数上限
     */
    ERROR_REACH_DAY_LIMIT,

    /**
     * AI构图功能下线
     */
    COMPOSITION_OFFLINE,

    /**
     * 敏感内容拒识惩罚 （触发了太多次拒识，功能禁用）
     */
    CONTENT_REFUSE_PUNISH
}

