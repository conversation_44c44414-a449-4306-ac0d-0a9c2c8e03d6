/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : OnGetConsumeTimeListener.java
 ** Description : 获取耗时的监听器
 ** Version     : 1.0
 ** Date        : 2024/3/11
 ** Author      : chenzengxin@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** chenzengxin@Apps.Gallery3D          2024/3/11     1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.repair.ocloud.listener;

/**
 * 文件上传耗时的监听器
 */
public interface OnGetConsumeTimeListener {
    /**
     * 获取开始时间和结束时间
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    void onGetBeginAndEndTime(long beginTime, long endTime);
}