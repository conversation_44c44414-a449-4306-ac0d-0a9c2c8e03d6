/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - TransformVM
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.transform

import android.app.Activity
import android.graphics.Rect
import android.graphics.RectF
import android.util.Size
import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.data.EditorMenuItemViewData
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.archv2.bus.ReplierChannel
import com.oplus.gallery.foundation.opengl.transform.Matrix
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.toList
import com.oplus.gallery.foundation.util.graphic.ImageUtils
import com.oplus.gallery.foundation.util.graphic.ImageUtils.SCALE_MODE_INSIDE
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAVE_OLIVE
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.SingleArgReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLAY_TOPIC_SUB_PAGE_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_RESTORE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.REPLY_TOPIC_CHANGE_PREVIEW_SIZE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_IS_PREVIEW_LOADED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_CURRENT_TEXTURE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_RENDER_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_SURFACE_SIZE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.REPLY_TOPIC_CROP_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.REPLY_TOPIC_TRANSFORM_ACTION_TOPICS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.REPLY_TOPIC_TRANSFORM_CLICK_RATIO_SELECTOR_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.TOPIC_HAS_REMEMBERED_TRANSFORM_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.TOPIC_ORIGIN_TRANSFORM_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.TOPIC_TRANSFORM_EXIT_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.TOPIC_TRANSFORM_UI_STATE
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.UnitReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.adjustment.AdjustRecord
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.ParametricDataSource
import com.oplus.gallery.photoeditor.editingvvm.notification.RestoreDialogAction
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.ExitPageListener
import com.oplus.gallery.photoeditor.editingvvm.operating.FindRecordParams
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingResultListener
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingType
import com.oplus.gallery.photoeditor.editingvvm.operating.RecordFindType
import com.oplus.gallery.photoeditor.editingvvm.operating.STACK_INDEX_TWO
import com.oplus.gallery.photoeditor.editingvvm.operating.SubPageState
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationCommand
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationState
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewRenderState
import com.oplus.gallery.photoeditor.editingvvm.subscribeUntilConsumed
import com.oplus.gallery.photoeditor.editingvvm.unregisterDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT

/**
 * 旋转裁剪页面的ViewModel
 * @param editingVM 主ViewModel
 */
internal class TransformVM(editingVM: EditingVM) : EditingSubVM(editingVM) {
    /**
     * 是否支持olive编辑
     */
    private val isSupportSaveOlive: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false)
    }

    /**
     * 裁剪旋转切片的UI属性集
     */
    private var transformPub: PublisherChannel<TransformUIBean>? = null

    /**
     * 裁剪变换的记录器，负责撤销和恢复历史的裁剪变换操作
     */
    private val transformRemember: TransformRemember? by lazy {
        vmBus?.let { TransformRemember(vmBus) }
    }

    /**
     * entry map key是viewId，value是对应的entry
     */
    private val configMap: ArrayMap<Int, TransformEntry> = arrayMapOf()

    private var menuList: List<EditorMenuItemViewData> = emptyList()

    /**
     * 初始ui状态的数据
     */
    private var initUIBean: TransformUIBean? = null

    /**
     * 校正按钮列表id
     */
    private val tiltMenuIdArray by lazy { ResourceUtils.getResourceIdArrays(app.resources,
        R.array.picture3d_editor_array_calibration_state_id_array) }

    /**
     * 校正 按钮id
     */
    private val tiltMenuId by lazy { tiltMenuIdArray[0] }

    /**
     * 垂直 按钮id
     */
    private val verticalTiltMenuId by lazy { tiltMenuIdArray[1] }

    /**
     * 水平 按钮id
     */
    private val horizontalTiltMenuId by lazy { tiltMenuIdArray[2] }

    private val animationProperties get() = vmBus?.get<PreviewAnimationProperties>(TOPIC_PREVIEW_ANIMATION_PROPERTIES)

    /**
     * 用于接收 UI 的消息，做数据更新
     */
    private val transformRep = object : UnitReplier<TransformUIBean>() {
        override fun onSingleReply(arg: TransformUIBean) {
            when (arg.id) {
                TransformBeanId.RECOVER_UI -> notifyRecoverData()
                //旋转
                TransformBeanId.ROTATE_ORIENTATION -> notifyRotateOrientation(arg.rotateOrientation, arg.isFromUser)
                // 镜像
                TransformBeanId.MIRROR_ORIENTATION -> notifyMirrorOrientation(arg.mirrorOrientation, arg.isFromUser)
                //比例
                TransformBeanId.RATIO_ENTRY -> notifyRatioEntry(arg.ratio)

                else -> GLog.d(TAG, LogFlag.DL) { "[transformRep] do nothing. id: ${arg.id}" }
            }
        }
    }

    /**
     * 操作栏的完成按钮点击回调
     */
    private val confirmRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            subscribeUntilRecordRemoved {
                exitConfirmedPub?.publish(true)
                isDestroyed = true
                if (hasModify() || (transformRemember?.hasRemmemberTranformEffect == true)) {
                    // 如果有修改，即需要入栈时，需要等图片被裁剪后再退出页面，否则在退场动画执行时更新纹理会中断退场动画
                    emitTransformRecord {
                        arg.onExitPage(isConfirm = true)
                    }
                } else {
                    // 不需要入栈，直接退出
                    restoreRememberEffect(true) { isChange ->
                        revertTransform(isChange.not())
                        if (isChange) {
                            waitNextRenderFrame { arg.onExitPage(isConfirm = false) }
                        } else {
                            arg.onExitPage(isConfirm = false)
                        }
                    }
                }
            }
        }
    }

    /**
     * 操作栏的取消按钮点击回调
     */
    private val cancelRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            notifyCancel(arg)
        }
    }

    /**
     * 操作栏的恢复按钮点击回调
     */
    private val restoreRep = object : ReplierChannel<Unit> {

        override fun onReply(vararg args: Any) {
            if (args.size <= 1) return
            val recordSize = args[0] as? Int ?: 0
            val listener = args[1] as? OperatingResultListener ?: return
            if (recordSize == 0) {
                notifyRestore(listener)
            } else {
                notifyRestoreDialogAction({

                    // 1. 这里会让逻辑回到OperatingVM.restore()，先处理操作栈.
                    listener.onResult(true)
                    runOnNextTextureLoaded {
                        notifyRestore()
                        notifyOriginTransformRecord()
                    }
                }, { listener.onResult(false) })
            }
        }
    }

    /**
     * 次级页状态（画质增强）
     */
    private val subPageStateRep = object : UnitReplierChannel {

        override fun onReply(vararg args: Any) {
            val subPageState = args[0] as? SubPageState ?: return
            val listener = args[1] as? OperatingResultListener
            when (subPageState) {
                SubPageState.ENTER -> {
                    if (hasModify()) {
                        // 进入三级页时，保存裁剪变换操作(入栈)
                        emitTransformRecord {
                            listener?.onResult(true)
                        }
                    } else {
                        // 进入三级页时，裁剪页没有修改，直接进入
                        listener?.onResult(false)
                    }
                }

                SubPageState.CANCEL_EXIT -> {
                    // 从三级页取消退出到裁剪页时，由于进入的时候入栈了裁剪记录，退出的时候需要出栈，恢复现场
                    removeTransformEffect(true) {
                        notifyOriginTransformRecord()
                    }
                }

                SubPageState.CONFIRM_EXIT -> {
                    val gesstureProperty = vmBus?.get<PreviewAnimationProperties>(TOPIC_PREVIEW_ANIMATION_PROPERTIES)
                    val imageRect = gesstureProperty?.imageRect
                    GLog.d(TAG, LogFlag.DL) { "[subPageStateRep] CONFIRM_EXIT , $imageRect, ${gesstureProperty?.previewArea}" }
                    // 画质增强后, 图片的size可能会变化, 需要更新裁剪框的size.
                    notifyUpdateClipRect()
                }
            }
        }
    }

    /**
     * 裁剪页页面退出的动作
     */
    private var exitConfirmedPub: PublisherChannel<Boolean>? = null
    private var hasRememberedTransformEffectPub: PublisherChannel<Boolean>? = null
    private var cropRspPub: PublisherChannel<CropOperation>? = null
    private val cropRsp = object : SingleArgReplierChannel<CropOperation, Unit>() {
        override fun onSingleReply(cropAction: CropOperation) {
            GLog.d(TAG, LogFlag.DL) { "[CropRsp] cmz.debug, val: $cropAction" }
            // 裁剪
            cropRspPub?.publish(cropAction)
        }
    }

    private var ratioSelectorClickRspPub: PublisherChannel<Boolean>? = null
    private val ratioSelectorClickRsp = object : SingleArgReplierChannel<Boolean, Unit>() {
        override fun onSingleReply(ratioSelectorSwitch: Boolean) {
            ratioSelectorClickRspPub?.publish(ratioSelectorSwitch)
        }
    }

    private val actionTopicsRep = object : UnitReplier<Boolean>() {
        override fun onSingleReply(arg: Boolean) {
            if (arg) registerActionTopics() else unregisterActionTopics()
        }
    }

    /**
     * 预览区域的设置
     */
    private val previewAreaSettingRep = object : ReplierChannel<Rect?> {
        override fun onReply(vararg args: Any): Rect? {
            if (args.size < PREVIEW_AREA_SETTING_MIN_ARGUS) {
                GLog.e(TAG, LogFlag.DL, "[previewAreaRep] error, args less than 2")
                return null
            }
            val isEditorLandscape = args[0] as? Boolean ?: return null
            val size = args[1] as? Rect ?: return null
            return getPagePreviewArea(isEditorLandscape, size)
        }
    }

    override fun onCreate() {
        super.onCreate()
        vmBus?.apply {
            transformPub = registerDuplex(TOPIC_TRANSFORM_UI_STATE, transformRep)
            cropRspPub = registerDuplex(REPLY_TOPIC_CROP_ACTION, cropRsp)
            exitConfirmedPub = register(TOPIC_TRANSFORM_EXIT_ACTION)
            hasRememberedTransformEffectPub = register(TOPIC_HAS_REMEMBERED_TRANSFORM_EFFECT)
            ratioSelectorClickRspPub = registerDuplex(REPLY_TOPIC_TRANSFORM_CLICK_RATIO_SELECTOR_ACTION, ratioSelectorClickRsp)
            register(REPLY_TOPIC_TRANSFORM_ACTION_TOPICS, actionTopicsRep)
            register(REPLAY_TOPIC_SUB_PAGE_STATE, subPageStateRep)
            register(REPLY_TOPIC_CHANGE_PREVIEW_SIZE, previewAreaSettingRep)
        }
        removeRememberEffect()
    }

    private fun hasModify(): Boolean {
        val animationProperties = animationProperties?.getOrLog("[hasModify] animationProperties is null.") ?: return false
        val currentBean = vmBus?.get<TransformUIBean>(TOPIC_TRANSFORM_UI_STATE) ?: TransformUIBean()
        // 1. 裁剪页本身的未入栈操作判定：裁剪页在点页面的‘完成按钮’以前，操作都未入栈，所以判定是否修改了不能通过栈，而需要通过当前页面的记录
        val changeFromCurSection = (animationProperties.contentPose.isChange)
            || (currentBean != initUIBean)
            || (animationProperties.clipRect.isChange)

        // 2. 裁剪页判断是否做过编辑，不仅仅要看裁剪页本身，还要看当前栈是否有修改（因为可能三级栈带回修改到二级栈来）
        val changeFromOperatingStack = vmBus?.get<Boolean>(TopicID.Operating.TOPIC_OPERATING_MODIFY_STATE) ?: false

        // 3. 当前页面是否还未入栈的修改记录 + 多级栈中当前顶栈中是否有修改记录？ --> 共同才能确认是否为‘hasChanged’状态。
        val hasModify = changeFromOperatingStack || changeFromCurSection
        return hasModify
    }

    private fun notifyUpdateClipRect() {
        val uiBean = vmBus?.get<TransformUIBean>(TOPIC_TRANSFORM_UI_STATE) ?: TransformUIBean()
        uiBean.id = TransformBeanId.UPDATE_CLIP_RECT
        transformPub?.publish(uiBean)
    }

    /**
     * 等待transformRecord移除后再执行confirmSave内操作
     * 执行block有两种情况
     * 一种是进入后没有找到transformRecord，也即transformRemember.hasRememberedTransformRecord为false
     * 另外一种则是进入后有transformRecord，这个时候会先发一个arg是true的通知，等applyEffect（移除特效）之后再发送一个false事件
     * 如果进入时就已经收到并且值为false，那么直接执行保存逻辑
     * 否则进入时，值为true或者还未收到该值，那么就等待监听，直到值为false，执行保存逻辑
     */
    private fun subscribeUntilRecordRemoved(confirmSave: () -> Unit) {
        val hasRemembered = vmBus?.get<Boolean>(TOPIC_HAS_REMEMBERED_TRANSFORM_EFFECT)
        if (hasRemembered == false) {
            confirmSave.invoke()
        } else {
            vmBus?.subscribeUntilConsumed<Boolean>(TOPIC_HAS_REMEMBERED_TRANSFORM_EFFECT, true) {
                GLog.d(TAG, LogFlag.DL, "subscribeUntilAnimationEnd: $it")
                if (it.not()) {
                    confirmSave.invoke()
                    true
                } else {
                    false
                }
            }
        }
    }

    /**
     * 下一次纹理刷新时调用[block]
     *
     * @param block 执行的函数体
     */
    private fun runOnNextTextureLoaded(block: () -> Unit) {
        vmBus?.subscribeUntilConsumed<Boolean>(TOPIC_IS_PREVIEW_LOADED, false) {
            block.invoke()
            true
        }
    }

    /**
     * 获取此页面的预览区域
     * @param isLandscape 是否横屏
     * @param originPreviewArea 原始的预览区域
     * @return 新的预览区域
     */
    private fun getPagePreviewArea(isLandscape: Boolean, originPreviewArea: Rect): Rect {
        val touchExpand = app.resources.getDimensionPixelOffset(com.oplus.gallery.foundation.ui.R.dimen.common_touchable_offset)
        val clipHorizontalMargin = Config.Render.getClipHorizontalMargin()
        return if (isLandscape) {
            // 横屏下，底部预留空白的触摸区域
            Rect(
                originPreviewArea.left,
                originPreviewArea.top,
                originPreviewArea.right,
                originPreviewArea.bottom - touchExpand
            )
        } else {
            // 竖屏，左右预留空白的触摸区域
            Rect(
                originPreviewArea.left + clipHorizontalMargin,
                originPreviewArea.top,
                originPreviewArea.right - clipHorizontalMargin,
                originPreviewArea.bottom
            )
        }
    }

    /**
     * 注册Action话题
     */
    private fun registerActionTopics() {
        GLog.d(TAG, LogFlag.DF, "registerActionTopics")
        vmBus?.apply {
            register(TOPIC_OPERATING_ACTION_RESTORE, restoreRep)
            register(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            register(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
        }
    }

    /**
     * 反注册Action话题
     */
    private fun unregisterActionTopics() {
        GLog.d(TAG, LogFlag.DF, "unregisterActionTopics")
        // 如果在onDestroy再解注册的话来不及，SectionA切换SectionB，会先走B的onCreate，再走A的onDestroy，所以要在onPause里面解注册
        vmBus?.apply {
            unregister(TOPIC_OPERATING_ACTION_RESTORE, restoreRep)
            unregister(TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            unregister(TOPIC_OPERATING_ACTION_CANCEL, cancelRep)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unregisterDuplexT(TOPIC_TRANSFORM_UI_STATE, transformRep, transformPub)
            unregisterDuplexT(REPLY_TOPIC_CROP_ACTION, cropRsp, cropRspPub)
            unregisterDuplexT(REPLY_TOPIC_TRANSFORM_CLICK_RATIO_SELECTOR_ACTION, ratioSelectorClickRsp, ratioSelectorClickRspPub)
            unregister(REPLY_TOPIC_TRANSFORM_ACTION_TOPICS, actionTopicsRep)
            unregister(REPLAY_TOPIC_SUB_PAGE_STATE, subPageStateRep)
            unregister(REPLY_TOPIC_CHANGE_PREVIEW_SIZE, previewAreaSettingRep)
            unregisterT(TOPIC_TRANSFORM_EXIT_ACTION, exitConfirmedPub)
            unregisterT(TOPIC_HAS_REMEMBERED_TRANSFORM_EFFECT, hasRememberedTransformEffectPub)
        }
    }

    /**
     * 通知初始化数据，如果支持项目化保存，则需要从操作栈恢复初始数据
     */
    private fun notifyRecoverData() {
        menuList = EditorUIConfig.initEditorMenuAdapterData(
            editingVM.getApplication(),
            R.array.picture3d_editor_array_calibration_state_id_array,
            R.array.picture3d_editor_array_calibration_state_icon_array,
            R.array.picture3d_editor_array_calibration_state_text_array
        )
        val rotateDefaultValue = app.resources.getInteger(R.integer.picture3d_rotate_clip_rotate_default_value)
        val rotateStartValue = app.resources.getInteger(R.integer.picture3d_rotate_clip_rotate_start_value)
        val rotateEndValue = app.resources.getInteger(R.integer.picture3d_rotate_clip_rotate_end_value)
        val tiltDefaultValue = app.resources.getInteger(R.integer.picture3d_rotate_clip_tilt_default_value)
        val tiltStartValue = app.resources.getInteger(R.integer.picture3d_rotate_clip_tilt_start_value)
        val tiltEndValue = app.resources.getInteger(R.integer.picture3d_rotate_clip_tilt_end_value)
        val rotateTransformEntry = TransformEntry(rotateDefaultValue, rotateStartValue, rotateEndValue, rotateDefaultValue)
        val horizontalTiltTransformEntry = TransformEntry(tiltDefaultValue, tiltStartValue, tiltEndValue, tiltDefaultValue)
        val verticalTiltTransformEntry = TransformEntry(tiltDefaultValue, tiltStartValue, tiltEndValue, tiltDefaultValue)
        // 初始化configMap数据
        configMap[tiltMenuId] = rotateTransformEntry
        configMap[horizontalTiltMenuId] = horizontalTiltTransformEntry
        configMap[verticalTiltMenuId] = verticalTiltTransformEntry
        val isSupportParametric = vmBus?.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        val uiBean = if (isSupportParametric) {
            // 操作栈中恢复数据
            vmBus?.notifyOnce<OperatingRecord>(REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC, AvEffect.TransformEffect.name)
                ?.asTransformRecord()?.asTransformUIBean()
                ?: TransformUIBean()
        } else {
            TransformUIBean()
        }
        // 给TransformUIBean设置configMap和menuData
        configMap[tiltMenuId]?.apply {
            currentProgress = uiBean.ruleRotateAngle
            enable = uiBean.ruleRotateAngleSwitch
        }
        configMap[horizontalTiltMenuId]?.apply {
            currentProgress = uiBean.horizontalTilt
            enable = uiBean.horizontalTiltSwitch
        }
        configMap[verticalTiltMenuId]?.apply {
            currentProgress = uiBean.verticalTilt
            enable = uiBean.verticalTiltSwitch
        }
        uiBean.configMap = configMap
        menuList.find { it.viewId == tiltMenuId }?.let {
            updateMenuItemViewDataByTransformEntry(rotateTransformEntry, it)
        }
        menuList.find { it.viewId == horizontalTiltMenuId }?.let {
            updateMenuItemViewDataByTransformEntry(horizontalTiltTransformEntry, it)
        }
        menuList.find { it.viewId == verticalTiltMenuId }?.let {
            updateMenuItemViewDataByTransformEntry(verticalTiltTransformEntry, it)
        }
        uiBean.menuData = menuList
        initUIBean = uiBean.copy()
        GLog.d(TAG, LogFlag.DL) { "[notifyInitData] isSupportParametric: $isSupportParametric, uiBean: $uiBean" }
        publishData(uiBean)
    }

    private fun publishData(uiBean: TransformUIBean) {
        if (uiBean.rotateOrientation != RotateOrientation.ROTATE_TOP) {
            uiBean.id = TransformBeanId.ROTATE_ORIENTATION
            transformPub?.publish(uiBean)
        }

        if (uiBean.mirrorOrientation != MirrorOrientation.MIRROR_NONE) {
            uiBean.id = TransformBeanId.MIRROR_ORIENTATION
            transformPub?.publish(uiBean)
        }

        uiBean.id = TransformBeanId.RECOVER_UI
        transformPub?.publish(uiBean)
    }

    private fun updateMenuItemViewDataByTransformEntry(
        transformEntry: TransformEntry,
        menuItemViewData: EditorMenuItemViewData
    ) {
        transformEntry.getProgressRatio().apply {
            menuItemViewData.loadProgress = this
            menuItemViewData.oldLoadProgress = this
        }
        if (transformEntry.enable.not()) {
            menuItemViewData.isDisableStyle = true
            menuItemViewData.centerText = TextUtil.EMPTY_STRING
        }
    }

    /**
     * 移除参数化记忆的效果，和[restoreRememberEffect]配合使用，移除效果包含
     * 1. 裁剪旋转变换
     * 2. 调节选项的暗角
     */
    private fun removeRememberEffect() {
        transformRemember?.let {
            hasRememberedTransformEffectPub?.publish(it.hasRemmemberTranformEffect)
        }
        removeTransformEffect(false) {
            notifyOriginTransformRecord()
            hasRememberedTransformEffectPub?.publish(false)
        }
        removeVignetteEffect()
    }

    /**
     * 恢复参数化记忆的效果，和[removeRememberEffect]配合使用，恢复效果包含
     * 1. 裁剪旋转变换
     * 2. 调节选项的暗角
     *
     * @param render 是否渲染上屏
     * @param onRestored 恢复后的监听
     */
    private fun restoreRememberEffect(render: Boolean, onRestored: (isChange: Boolean) -> Unit = {}) {
        transformRemember?.restoreTransformEffect(render) {
            onRestored.invoke(it)
        }
        restoreVignetteEffect(render)
    }

    /**
     * 从栈中获取非参数化操作前的第一个调整操作记录
     */
    private fun getAdjustRecord(): AdjustRecord? {
        return (vmBus?.notifyOnce<OperatingRecord>(
            REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC,
            AvEffect.AdjustEffect.name
        ) as? AdjustRecord)?.copy()
    }

    /**
     * 移除暗角效果,和[restoreVignetteEffect]搭配使用
     */
    private fun removeVignetteEffect() {
        val adjustRecord = getAdjustRecord() ?: return
        if (adjustRecord.removeVignette()) {
            emitRecord(adjustRecord, OperatingType.ONLY_EFFECT)
        }
    }

    /**
     * 恢复暗角效果，和[removeVignetteEffect]配对使用
     */
    private fun restoreVignetteEffect(render: Boolean) {
        val adjustRecord = getAdjustRecord() ?: return
        if (adjustRecord.hasVignette().not()) {
            return
        }
        emitRecord(adjustRecord, OperatingType.ONLY_EFFECT, render)
    }

    /**
     * 移除裁剪变换的效果，动画结束之后还原，避免动画过程更新纹理中断动画
     *
     * @param isRemoveRecord 移除效果的时候，是否把操作栈的记录移除
     * @param onRemoved 复原之后的监听
     */
    private fun removeTransformEffect(isRemoveRecord: Boolean, onRemoved: () -> Unit = {}) {
        vmBus?.subscribeUntilConsumed<PreviewAnimationState>(TOPIC_PREVIEW_ANIMATION_STATE, false) {
            if (it == PreviewAnimationState.AnimationEnd) {
                if (isDestroyed.not()) {
                    transformRemember?.removeTransformEffect(onRemoved, isRemoveRecord)
                }
                true
            } else {
                false
            }
        }
    }

    /**
     * 初始化数据后，将当前的记录保存，用作旋转裁剪后撤销和反撤销用。
     */
    private fun notifyOriginTransformRecord() {
        if (isSupportSaveOlive.not()) {
            return
        }
        if (vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)?.isOlivePhoto() == false) {
            return
        }
        vmBus?.notifyOnce<Unit>(TOPIC_ORIGIN_TRANSFORM_RECORD, true)
    }

    /**
     * 通知更新镜像的方向
     *
     * @param mirror 两种状态：水平镜像、非镜像
     */
    private fun notifyMirrorOrientation(mirror: MirrorOrientation, isFromUser: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[notifyMirrorOrientation] mirror: $mirror" }
        val bean = vmBus?.get<TransformUIBean>(TOPIC_TRANSFORM_UI_STATE) ?: TransformUIBean()
        if (bean.mirrorOrientation == mirror) {
            GLog.w(TAG, LogFlag.DL) { "[notifyMirrorOrientation] mirror is same" }
            return
        }
        bean.id = TransformBeanId.MIRROR_ORIENTATION
        bean.mirrorOrientation = mirror
        // 梯形校正需求中，需要点击镜像时，校正和水平两个菜单的值取反
        bean.configMap[tiltMenuId]?.currentProgress =
            -(bean.configMap[tiltMenuId]?.currentProgress ?: 0)
        bean.configMap[horizontalTiltMenuId]?.currentProgress =
            -(bean.configMap[horizontalTiltMenuId]?.currentProgress ?: 0)
        bean.isFromUser = isFromUser
        transformPub?.publish(bean)
    }

    /**
     * 通知更新旋转方向
     *
     * @param rotate 对应旋转按钮的左右上下4个方向
     */
    private fun notifyRotateOrientation(rotate: RotateOrientation, isFromUser: Boolean) {
        GLog.d(TAG, LogFlag.DL) { "[notifyRotateOrientation] rotate: $rotate" }
        val bean = vmBus?.get<TransformUIBean>(TOPIC_TRANSFORM_UI_STATE) ?: TransformUIBean()
        if (bean.rotateOrientation == rotate) {
            GLog.w(TAG, LogFlag.DL) { "[notifyRotateOrientation] rotate is same" }
            return
        }
        bean.id = TransformBeanId.ROTATE_ORIENTATION
        bean.rotateOrientation = rotate
        bean.isFromUser = isFromUser
        transformPub?.publish(bean)
    }

    /**
     * 通知更新裁剪比例
     *
     * @param ratio 比例信息
     */
    private fun notifyRatioEntry(ratio: Ratio) {
        GLog.d(TAG, LogFlag.DL) { "[notifyRatioEntry] ratioEntry: $ratio" }
        val bean = vmBus?.get<TransformUIBean>(TOPIC_TRANSFORM_UI_STATE) ?: TransformUIBean()
        if (bean.ratio == ratio) {
            GLog.w(TAG, LogFlag.DL) { "[notifyRatioEntry] ratioEntry is same" }
            return
        }
        bean.id = TransformBeanId.RATIO_ENTRY
        bean.ratio = ratio
        transformPub?.publish(bean)
    }

    /**
     * 点击恢复前回调
     */
    private fun notifyRestore(callback: OperatingResultListener? = null) {
        GLog.d(TAG, LogFlag.DL) { "[notifyRestore] click" }
        val uiBean = TransformUIBean()

        // 先把其他ui元素给复原了，最后才处理图片本身的复原
        uiBean.id = TransformBeanId.RECOVER_CLICKED
        uiBean.configMap = configMap.onEach { (_, value) ->
            value.currentProgress = value.defaultProgress
            value.enable = true
        }
        uiBean.menuData = menuList
        transformPub?.publish(uiBean)

        callback?.onResult(true)
    }

    /**
     * 提交变换记录，此操作会收集变换参数并提交到管线
     * @param onTextureUpdated 纹理更新的时候回调
     */
    private fun emitTransformRecord(onTextureUpdated: () -> Unit = {}) {
        val property = animationProperties?.getOrLog(TAG, "[emitRecord] gestureAnimator is null.") ?: return
        restoreRememberEffect(false)
        val bean = vmBus?.get<TransformUIBean>(TOPIC_TRANSFORM_UI_STATE) ?: TransformUIBean()
        val surfaceSize = vmBus?.get<Size>(TOPIC_PREVIEW_SURFACE_SIZE)
        val matrix = Matrix().also {
            it.set(property.fullTransform.final)
        }
        val tiltMatrixInvert = Matrix().also {
            property.fullPose.final.tiltTransform.transform.invert(it)
        }
        val matrixWithoutTilt = Matrix().also {
            it.set(property.contentTransform.final)
            it.preConcat(tiltMatrixInvert)
        }.toFullImageTransform(property.imageRect, property.fullContentVisibleRect)
        val clipRect = RectF(property.clipRect.final)
        val imageRect = RectF(property.imageRect)
        val ruleAngle = bean.configMap[tiltMenuId]
        val horizontalTilt = bean.configMap[horizontalTiltMenuId]
        val verticalTilt = bean.configMap[verticalTiltMenuId]
        val ruleAngleSwitch = ruleAngle?.enable ?: true
        val ruleAngleValue = ruleAngle?.currentProgress ?: 0
        val horizontalSwitch = horizontalTilt?.enable ?: true
        val horizontalValue = horizontalTilt?.currentProgress ?: 0
        val verticalSwitch = verticalTilt?.enable ?: true
        val verticalValue = verticalTilt?.currentProgress ?: 0
        val record = TransformRecord(
            // 算法必要的5个参数 (见TBL算法插件仓库 TransformAlgoImpl.execute->checkAndGetTransformIO->getTransformArguments; + drawTexture;)
            matrix = matrix,
            clipRect = clipRect.toList(),
            imageRect = imageRect.toList(),
            viewportWidth = surfaceSize?.width ?: 0,
            viewportHeight = surfaceSize?.height ?: 0,
            // 裁剪编辑页UI还原必要的4个参数 (见 TransformUIBean)
            rotate = bean.rotateOrientation,
            ruleAngle = ruleAngleValue,
            ruleAngleSwitch = ruleAngleSwitch,
            horizontalTilt = horizontalValue,
            horizontalTiltSwitch = horizontalSwitch,
            verticalTilt = verticalValue,
            verticalTiltSwitch = verticalSwitch,
            isHorizontalMirror = bean.mirrorOrientation.isHorizontalMirror,
            ratio = bean.ratio,
            // 高阶UI还原可能会用到的参数
            axisAngleTransform = property.fullPose.final.compositeRotate,
            matrixWithoutTilt = matrixWithoutTilt
        ).also {
            it.hasQualityEnhanceEffect = hasQualityEnhanceEffect()
        }
        // 提交记录到管线后，需要等待渲染完成再返回
        emitOnRenderEnd(record) {
            onTextureUpdated.invoke()
        }
        vmBus?.subscribeUntilConsumed<ITexture>(TOPIC_PREVIEW_CURRENT_TEXTURE, false) {
            revertTransform(false)
            true
        }
    }

    private fun Matrix.toFullImageTransform(imageRect: RectF, fullContentVisibleRect: RectF): Matrix {
        val scale = ImageUtils.scaleImage(
            imageRect.width(),
            imageRect.height(),
            fullContentVisibleRect.width(),
            fullContentVisibleRect.height(),
            SCALE_MODE_INSIDE
        )
        val imageTransform = Matrix.obtain()
        imageTransform.set(this)
        imageTransform.scale(scale, scale, scale)
        return imageTransform
    }

    /**
     * 是否添加过画质增强效果
     *
     * @return true:添加了画质增强效果; false: 未添加画质增强效果
     */
    private fun hasQualityEnhanceEffect(): Boolean {
        // 获取最后的画质增强堆栈记录
        val param = FindRecordParams(
            stackIndex = STACK_INDEX_TWO,
            findType = RecordFindType.FIND_LAST,
            predicate = { it.effectName == AvEffect.ImageQualityEnhanceEffect.name }
        )
        val qualityRecordList = vmBus?.notifyOnce<List<OperatingRecord>>(TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS, param)

        return (qualityRecordList?.isNotEmpty() == true)
    }

    private fun revertTransform(withAnimation: Boolean) {
        vmBus?.notifyOnce(TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.Revert)
        if (withAnimation.not()) {
            vmBus?.notifyOnce(TOPIC_PREVIEW_ANIMATION_COMMAND, PreviewAnimationCommand.ForceFinishAnimation)
        }
    }

    /**
     * 点击取消，准备退出前回调
     */
    private fun notifyCancel(callback: ExitPageListener) {
        GLog.d(TAG, LogFlag.DL) { "[notifyCancel] cancel btn click" }
        isDestroyed = true
        exitConfirmedPub?.publish(false)
        restoreRememberEffect(true) { isChange ->
            revertTransform(isChange.not())
            if (isChange) {
                waitNextRenderFrame { callback.onExitPage(isConfirm = false) }
            } else {
                callback.onExitPage(isConfirm = false)
            }
        }
    }

    /**
     * 等待下一个GL线程帧
     *
     * @param block 下一帧GL线程帧要执行的函数体
     */
    private fun waitNextRenderFrame(block: () -> Unit) {
        vmBus?.subscribeUntilConsumed<PreviewRenderState.RenderEnd>(TOPIC_PREVIEW_RENDER_STATE, false) {
            block.invoke()
            true
        }
    }

    /**
     * 重置弹框
     */
    private fun notifyRestoreDialogAction(nextAction: () -> Unit, cancelAction: () -> Unit) {
        val action = RestoreDialogAction(
            titleResId = R.string.picture3d_editor_text_image_quality_enhance_dialog_title_revert_image,
            messageResId = R.string.picture3d_editor_text_image_quality_enhance_dialog_hint_revert_image,
            object : NotificationAction.IConfirmCallback {

                override fun onPositiveButtonClicked(activity: Activity) {
                    nextAction.invoke()
                }

                override fun onNegativeButtonClicked() {
                    cancelAction.invoke()
                }
            })

        vmBus?.notifyOnce(TopicID.Notification.TOPIC_NOTIFICATION_ACTION, action)
    }

    companion object {
        private const val PREVIEW_AREA_SETTING_MIN_ARGUS = 2
        const val TAG = "TransformVM"
        const val FLOAT_FACTOR = 1E-3f
        const val ONE_HUNDRED = 100

        const val INVALID_REQUEST_CODE = -1
    }
}

/**
 * 镜像方向
 * MIRROR_NONE：未镜像状态
 * MIRROR_HORIZONTAL：被水平方向镜像状态
 */
enum class MirrorOrientation(val isHorizontalMirror: Boolean) {
    MIRROR_NONE(false),
    MIRROR_HORIZONTAL(true);

    /**
     * 执行一次水平镜像
     * @return 返回镜像后的方向类型
     */
    fun horizontalMirror(): MirrorOrientation {
        return when (this) {
            MIRROR_NONE -> MIRROR_HORIZONTAL
            MIRROR_HORIZONTAL -> MIRROR_NONE
        }
    }
}

/**
 * 裁剪动作的UI通知
 */
data class CropOperation(
    val shouldForceHideCapsuleBtn: Boolean,
    val cropped: Boolean,
    val clipFrame: Size
)

/**
 * 图片方向
 * 初始时，图片头朝上方
 * 顺时针90度角，图片头朝右
 * 顺时针180度角，图片头朝下
 * 顺时针270度角，图片头朝左
 */
enum class RotateOrientation(val angle: Int) {
    ROTATE_TOP(MathUtils.ZERO),
    ROTATE_RIGHT(MathUtils.DEG_90I),
    ROTATE_BOTTOM(MathUtils.DEG_180I),
    ROTATE_LEFT(MathUtils.DEG_270I);

    /**
     * 执行一次逆时针90度旋转
     * @return 返回旋转后的方向类型
     */
    fun anticlockwiseRotate(): RotateOrientation {
        return when (this) {
            ROTATE_TOP -> ROTATE_LEFT
            ROTATE_LEFT -> ROTATE_BOTTOM
            ROTATE_BOTTOM -> ROTATE_RIGHT
            ROTATE_RIGHT -> ROTATE_TOP
        }
    }
}

/**
 * 图片显示比例
 */
enum class Ratio(val value: Float) {
    RATIO_FREE(Config.RATIO.RATIO_FREE),
    RATIO_ORIGINAL(Config.RATIO.RATIO_ORIGINAL),
    RATIO_1X1(Config.RATIO.RATIO_SQUARE),
    RATIO_4X3(Config.RATIO.RATIO_4_X_3),
    RATIO_3X4(Config.RATIO.RATIO_3_X_4),
    RATIO_16X9(Config.RATIO.RATIO_16_X_9),
    RATIO_9X16(Config.RATIO.RATIO_9_X_16),
    RATIO_3X2(Config.RATIO.RATIO_3_X_2),
    RATIO_2X3(Config.RATIO.RATIO_2_X_3),
    RATIO_MOVIE(Config.RATIO.RATIO_MOVIE),
    RATIO_XPAN(Config.RATIO.RATIO_XPAN);
}

/**
 * TransformRecord转成TransformUIBean
 * @return [TransformUIBean]
 */
private fun TransformRecord.asTransformUIBean(): TransformUIBean {
    return TransformUIBean(
        mirrorOrientation = if (isHorizontalMirror) {
            MirrorOrientation.MIRROR_HORIZONTAL
        } else {
            MirrorOrientation.MIRROR_NONE
        },
        rotateOrientation = this.rotate,
        ruleRotateAngle = this.ruleAngle,
        ruleRotateAngleSwitch = this.ruleAngleSwitch,
        ratio = this.ratio,
        horizontalTilt = this.horizontalTilt,
        horizontalTiltSwitch = this.horizontalTiltSwitch,
        verticalTilt = this.verticalTilt,
        verticalTiltSwitch = this.verticalTiltSwitch
    )
}