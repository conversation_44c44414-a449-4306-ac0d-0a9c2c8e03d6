/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - StickerSelectionBean
 ** Description:
 ** Version: 1.0
 ** Date : 2024/5/27
 ** Author: pxijin
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  pixijin                      2024/5/27    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.sticker

import com.oplus.gallery.photoeditor.editingvvm.sticker.data.StickerItem

/**
 * 进入贴纸选择模式删除、增加、编辑时的UI事件集实体
 */
data class StickerSelectionBean(
    /**
     * 选择模式操作的Id
     */
    var id: StickerSelectionBeanId = StickerSelectionBeanId.NONE,

    /**
     * 最近贴纸数据类
     */
    var recentStickerItems: List<StickerItem>? = null,

    /**
     * 自定义贴纸数据类
     */
    var customStickerItems: List<StickerItem>? = null,

    /**
     * 删除的贴纸数据类
     */
    var deletingStickerItem: StickerItem? = null,

    /**
     * 最近贴纸是否全选
     */
    var isRecentSelectAll: Boolean = false,

    /**
     * 单击最近贴纸的Id
     */
    var recentStickerSingleIndex: Int = 0,

    /**
     * 自定义贴纸是否全选
     */
    var isCustomSelectAll: Boolean = false,

    /**
     * 单击自定义的贴纸的Id
     */
    var customSickerSingleIndex: Int = 0

)

/**
 * 接收/发送 StickerSelectionBean事件Id
 */
enum class StickerSelectionBeanId {
    NONE,
    RECENT_STICKER_ITEMS,
    CUSTOM_STICKER_ITEMS,
    DELETE_RECENT_STICKERS,
    DELETE_RECENT_STICKER,
    CLICK_RECENT_SELECT_ALL,
    CLEAR_RECENT_SELECT_STATE,
    CLICK_RECENT_STICKER_SINGLE,
    CLICK_CUSTOM_SELECT_ALL,
    CHANGE_SELECT_ITEM_VISIBLE,
    DELETE_CUSTOM_STICKERS,
    DELETE_CUSTOM_STICKER,
    CLEAR_CUSTOM_SELECT_STATE,
    CLICK_CUSTOM_STICKER_SINGLE
}