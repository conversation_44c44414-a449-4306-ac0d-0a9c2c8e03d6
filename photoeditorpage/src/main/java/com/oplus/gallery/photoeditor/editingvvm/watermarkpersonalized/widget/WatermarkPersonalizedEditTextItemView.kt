/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : WatermarkPersonalizedTextEditItemView
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/19
 ** Author      : <PERSON><PERSON>.Zhuang@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>.<PERSON><PERSON>@Apps.Gallery3D     2023/12/07      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget

import android.content.Context
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.ShapeDrawable.ShaderFactory
import android.graphics.drawable.shapes.RoundRectShape
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.WatermarkPersonalizedEditVM
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.bean.ItemType
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.bean.WatermarkPersonalizedEditTextItem

/**
 * 文本编辑列表item项自定义View实例
 */
class WatermarkPersonalizedEditTextItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    //文本编辑列表item项默认背景
    private var defaultBackground = context.resources.getDrawable(R.drawable.picture3d_editor_personalise_text_list_item_bg, null)
    //当前item项显示类型-normal、simple、loading
    private var itemType = ItemType.NONE

    private var mClickableSpanListener: OnClickableSpanListener? = null

    /**
     * 更新item内容显示状态
     */
    fun updateContent(item: WatermarkPersonalizedEditTextItem, greetingTitle: String) {
        when (item.type) {
            ItemType.NORMAL,
            ItemType.SIMPLE -> {
                if ((itemType == ItemType.NONE) || (itemType == ItemType.LOADING)) {
                    removeAllViews()
                    LayoutInflater.from(context).inflate(R.layout.picture3d_editor_personalise_text_list_normal_item, this, true)
                    background = defaultBackground
                    findViewById<LinearLayout>(R.id.ll_btn_area)?.visibility = View.GONE
                }
                if (item.type == ItemType.SIMPLE) {
                    val description = findViewById<TextView>(R.id.tv_item_simple_description)
                    findViewById<RelativeLayout>(R.id.rl_text_area)?.visibility = View.GONE
                    description?.visibility = View.VISIBLE
                    description?.text = item.description
                    findViewById<LinearLayout>(R.id.ll_btn_area)?.visibility = View.GONE
                } else {
                    findViewById<RelativeLayout>(R.id.rl_text_area)?.visibility = View.VISIBLE
                    findViewById<TextView>(R.id.tv_item_simple_description)?.visibility = View.GONE
                    findViewById<TextView>(R.id.tv_item_title)?.text = item.title
                    findViewById<TextView>(R.id.tv_item_description)?.text = item.description

                    when (item.title) {
                        context?.getString(R.string.picture_editor_text_watermark_custom) -> {
                            findViewById<LinearLayout>(R.id.ll_btn_area)?.visibility = View.VISIBLE
                            findViewById<ImageView>(R.id.img_btn_edit)?.setImageResource(R.drawable.photoedit_personalise_text_list_edit)
                        }

                        greetingTitle -> {
                            findViewById<LinearLayout>(R.id.ll_btn_area)?.visibility = View.VISIBLE
                            findViewById<ImageView>(R.id.img_btn_edit)?.setImageResource(R.drawable.photoedit_personalise_text_list_refresh_gray)
                        }

                        else -> findViewById<LinearLayout>(R.id.ll_btn_area)?.visibility = View.GONE
                    }
                }
            }
            ItemType.LOADING -> {
                if (itemType != ItemType.LOADING) {
                    removeAllViews()
                    LayoutInflater.from(context).inflate(R.layout.picture3d_editor_personalise_text_list_loading_item, this, true)
                    val colors = intArrayOf(STROKE_START, STROKE_END)
                    background = createDrawable(
                        colors,
                        resources.getDimensionPixelSize(R.dimen.picture3d_editor_personalise_text_list_menu_bg_radius).toFloat(),
                        resources.getDimensionPixelSize(R.dimen.picture3d_editor_personalise_text_list_menu_bg_stroke).toFloat()
                    )
                }
                findViewById<TextView>(R.id.tv_loading_item_description)?.text = item.description
            }
            else -> {}
        }
        itemType = item.type
    }

    /**
     * 创建一个渐变颜色边框，且中间部分可以是透明状态
     * @param colors 渐变颜色数据集
     * @param radius 外边框圆角大小
     * @param strokeWidth 渐变颜色边框宽度
     * @return 返回一个渐变颜色边框的ShapeDrawable
     */
    private fun createDrawable(colors: IntArray, radius: Float, strokeWidth: Float): ShapeDrawable {
        val outerR = floatArrayOf(radius, radius, radius, radius, radius, radius, radius, radius)
        // 内部矩形与外部矩形的距离
        val inset = RectF(strokeWidth, strokeWidth, strokeWidth, strokeWidth)
        // 内部矩形弧度
        val innerRadius = radius - strokeWidth
        val innerRadii =
            floatArrayOf(innerRadius, innerRadius, innerRadius, innerRadius, innerRadius, innerRadius, innerRadius, innerRadius)
        val rr = RoundRectShape(outerR, inset, innerRadii)
        val shaderFactory: ShaderFactory = object : ShaderFactory() {
            override fun resize(width: Int, height: Int): Shader {
                return LinearGradient(0f, 0f, width.toFloat(), height.toFloat(), colors, null, Shader.TileMode.CLAMP)
            }
        }
        val shapeDrawable = ShapeDrawable(rr)
        shapeDrawable.shaderFactory = shaderFactory
        return shapeDrawable
    }

    fun setOnClickableSpanListener(listener: OnClickableSpanListener?) {
        mClickableSpanListener = listener
    }

    interface OnClickableSpanListener {
        fun onSpanClick()
    }

    companion object {
        const val TAG = "WatermarkPersonalizedEditTextItemView"
        private val STROKE_START = Color.parseColor("#08D73E")
        private val STROKE_END = Color.parseColor("#1BD7F1")
    }
}