/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PageManagementVM
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.pagecontainer

import android.util.Size
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.basebiz.helper.function.restrict.ISupportCheckStrategy
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.toOriginalItem
import com.oplus.gallery.foundation.archv2.bus.IViewModelBus
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.IntObserver
import com.oplus.gallery.photoeditor.editingvvm.IntReplier
import com.oplus.gallery.photoeditor.editingvvm.StringObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Component.REPLY_TOPIC_COMPONENT_CHECK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Component.REPLY_TOPIC_FETCH_REMOTE_MODEL_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Detecting.TOPIC_DETECTING_BY_STRATEGY
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.TOPIC_REMOVE_WATERMARK_ENTER_SUBPAGE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Hdr.TOPIC_HDR_SUPPORT_CHECK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_EDIT_TYPE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STATUS_BAR_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_TYPE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.REPLY_TOPIC_MENU_LOAD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Menu.TOPIC_MENU_SELECTED_STRATEGY
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Notification.TOPIC_NOTIFICATION_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_OLIVE_SUPPORT_CHECK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_ENTER_SUB
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_MODIFY_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
import com.oplus.gallery.photoeditor.editingvvm.aigraffiti.AIGraffitiSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.beauty.data.InputFuncType
import com.oplus.gallery.photoeditor.editingvvm.component.CommonModelSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.ComponentCheckArg
import com.oplus.gallery.photoeditor.editingvvm.component.portraitblur.PortraitBlurSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.TransformSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.aicomposition.AiCompositionSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.aideblur.AIDeBlurCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.aidefog.AIDeFogCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.aideglare.AIDeGlareCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.aidereflection.AIDeReflectionStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.aihd.AihdSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.ailighting.AiLightingSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.aiscenery.AIScenerySupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.beauty.BeautySupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.besttake.BestTakeSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.ai.AIEliminateSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.eliminate.normal.EliminatePenModelLoader
import com.oplus.gallery.photoeditor.editingvvm.component.imagequalityenhance.ImageQualityEnhanceSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.component.mosaic.AutoMosaicConfigHelper
import com.oplus.gallery.photoeditor.editingvvm.component.repair.RepairSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.detect.DetectingArg
import com.oplus.gallery.photoeditor.editingvvm.detect.DetectingResult
import com.oplus.gallery.photoeditor.editingvvm.detect.FaceState
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.hdr.HdrCheckArg
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveSupportCheckStrategy
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.vm.OliveVideoEditVM.OliveCheckArg
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unregisterDuplexT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeR
import com.oplus.gallery.photoeditor.editingvvm.watermark.RemoveWatermarkArg
import com.oplus.gallery.standard_lib.app.UI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 页面管理ViewModel，负责页面相关的业务逻辑，例如：菜单的加载及切换页面前的环境检测
 * @param editingVM 主ViewModel
 */
internal class PageManagementVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    /**
     * 切换页ID
     */
    private var strategyIDPub: PublisherChannel<Int>? = null

    /**
     * 页面策略类型
     */
    private var strategyTypePub: PublisherChannel<StrategyType>? = null

    /**
     * 状态栏状态，目前只有显示与隐藏
     */
    private var statusBarStatePub: PublisherChannel<Boolean>? = null

    private var componentCheckNtf: NotifierChannel<Unit>? = null

    private var detectingNtf: NotifierChannel<Unit>? = null

    private var hdrCheckNft: NotifierChannel<Unit>? = null

    private var oliveCheckNft: NotifierChannel<Unit>? = null

    private var removeWatermarkNft: NotifierChannel<Unit>? = null

    private val strategySelectedObserver: IntObserver = { onStrategySelected(it) }

    private val enterEditorObserver: StringObserver = { enterEditor(it) }

    private val strategyIDRep = object : IntReplier<Unit>() {
        override fun onSingleReply(arg: Int) {
            strategyIDPub?.publish(arg)
        }
    }
    private var supportCheckStrategy: ISupportCheckStrategy? = null

    init {
        vmBus?.apply {
            strategyIDPub = registerDuplex(TOPIC_MANAGEMENT_STRATEGY_ID, strategyIDRep)
            statusBarStatePub = register(TOPIC_MANAGEMENT_STATUS_BAR_STATE)
        }
    }

    override fun onCreate() {
        super.onCreate()
        vmBus?.apply {
            strategyTypePub = register(TOPIC_MANAGEMENT_STRATEGY_TYPE)

            subscribeT(TOPIC_MENU_SELECTED_STRATEGY, strategySelectedObserver)
            subscribeT(TOPIC_INPUTS_EDIT_TYPE, enterEditorObserver)

            detectingNtf = subscribeR(TOPIC_DETECTING_BY_STRATEGY)
            componentCheckNtf = subscribeR(REPLY_TOPIC_COMPONENT_CHECK)
            hdrCheckNft = subscribeR(TOPIC_HDR_SUPPORT_CHECK)
            oliveCheckNft = subscribeR(TOPIC_OLIVE_SUPPORT_CHECK)
            removeWatermarkNft = subscribeR(TOPIC_REMOVE_WATERMARK_ENTER_SUBPAGE)
        }
    }

    private suspend fun detectByAlgo(strategyID: Int): DetectingResult? {
        return suspendCoroutine { continuation ->
            var isResumed = false
            detectingNtf?.notify(DetectingArg(strategyID) { detectingResult ->
                GLog.d(TAG, LogFlag.DL) { "detectByAlgo: detectingResult = $detectingResult" }
                // 防呆
                if (!isResumed) {
                    runCatching { continuation.resume(detectingResult) }
                    isResumed = true
                } else {
                    GLog.w(TAG, LogFlag.DL) { "detectByAlgo: continuation is resumed, illegal callback." }
                }
            })
        }
    }

    private suspend fun warnHDRLossAfterEditing(strategyID: Int): Boolean {
        return suspendCoroutine { continuation ->
            var isResumed = false
            hdrCheckNft?.notify(HdrCheckArg(strategyID) { isContinuable ->
                GLog.d(TAG, LogFlag.DL) { "warnHDRLossAfterEditing: isContinuable = $isContinuable" }
                // 防呆
                if (!isResumed) {
                    runCatching { continuation.resume(isContinuable) }
                    isResumed = true
                } else {
                    GLog.w(TAG, LogFlag.DL) { "warnHDRLossAfterEditing: continuation is resumed, illegal callback." }
                }
            })
        }
    }

    private suspend fun warnOliveLossAfterEditing(strategyID: Int): Boolean {
        return suspendCoroutine { continuation ->
            var isResumed = false
            oliveCheckNft?.notify(OliveCheckArg(strategyID) { isContinuable ->
                GLog.d(TAG, LogFlag.DL) { "warnOliveLossAfterEditing: isContinuable = $isContinuable" }
                // 防呆
                if (!isResumed) {
                    runCatching { continuation.resume(isContinuable) }
                    isResumed = true
                } else {
                    GLog.w(TAG, LogFlag.DL) { "warnOliveLossAfterEditing: continuation is resumed, illegal callback." }
                }
            })
        }
    }

    private suspend fun checkSupportabilityExceptComponent(strategyID: Int, detectingResult: DetectingResult): Boolean {
        return suspendCoroutine { continuation ->
            var isResumed = false
            checkSupportability(strategyID, detectingResult) { isContinuable ->
                GLog.d(TAG, LogFlag.DL) { "checkSupportabilityExceptComponent: isContinuable = $isContinuable" }
                // 防呆
                if (!isResumed) {
                    runCatching { continuation.resume(isContinuable) }
                    isResumed = true
                } else {
                    GLog.w(TAG, LogFlag.DL) { "checkSupportabilityExceptComponent: continuation is resumed, illegal callback." }
                }
            }
        }
    }

    private suspend fun checkComponentSupportability(strategyID: Int): Boolean {
        return suspendCoroutine { continuation ->
            var isResumed = false
            // 检查线上插件依赖是否满足
            componentCheckNtf?.notify(ComponentCheckArg(strategyID) { isContinuable ->
                GLog.d(TAG, LogFlag.DL) { "checkComponentSupportability: isContinuable = $isContinuable" }
                // 防呆
                if (!isResumed) {
                    runCatching { continuation.resume(isContinuable) }
                    isResumed = true
                } else {
                    GLog.w(TAG, LogFlag.DL) { "checkComponentSupportability: continuation is resumed, illegal callback." }
                }
            })
        }
    }

    private suspend fun removeWatermarkBeforeLaunching(strategyID: Int): Boolean {
        return suspendCoroutine { continuation ->
            var isResumed = false
            removeWatermarkNft?.notify(RemoveWatermarkArg(strategyID) {
                // 防呆
                if (!isResumed) {
                    runCatching { continuation.resume(true) }
                    isResumed = true
                } else {
                    GLog.w(TAG, LogFlag.DL) { "removeWatermarkBeforeLaunching: continuation isResumed, illegal callback." }
                }
            })
        }
    }

    private fun onStrategySelected(strategyID: Int) {
        GLog.d(TAG, LogFlag.DL) { "onStrategySelected: strategyID = $strategyID" }
        launch(Dispatchers.UI) {

            val curStrategyID = vmBus?.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID)
            GLog.d(TAG, LogFlag.DL) { "onStrategySelected: curStrategyID = $curStrategyID" }

            // 预检测模块，需要跑算法的才放这边（如人脸扫描，部分功能需要图片包含人脸才能支持）
            val detectingResult = detectByAlgo(strategyID) ?: DetectingResult.Face(FaceState.UNKNOWN)

            // HDR检查，所有编辑项都需要
            var isContinuable = warnHDRLossAfterEditing(strategyID)
            if (isContinuable.not()) {
                GLog.d(TAG, LogFlag.DL) { "onStrategySelected: intercepted by HDR Loss Warning." }
                return@launch
            }

            // 通用检查（非Component部分）
            isContinuable = checkSupportabilityExceptComponent(strategyID, detectingResult)
            if (isContinuable.not()) {
                GLog.d(TAG, LogFlag.DL) { "onStrategySelected: intercepted by Common Supportability Checking." }
                return@launch
            }

            // Component支持检查
            isContinuable = checkComponentSupportability(strategyID)
            if (isContinuable.not()) {
                GLog.d(TAG, LogFlag.DL) { "onStrategySelected: intercepted by Component Supportability Checking." }
                return@launch
            }

            // olive检查，所有编辑项都需要
            isContinuable = warnOliveLossAfterEditing(strategyID)
            if (isContinuable.not()) {
                GLog.d(TAG, LogFlag.DL) { "onStrategySelected: intercepted by olive Loss Warning." }
                return@launch
            }

            // 去除预览页水印，Mark by wudanyang: @jiepengpeng 这里看看能不能挪个地方
            isContinuable = removeWatermarkBeforeLaunching(strategyID)
            if (isContinuable.not()) {
                GLog.d(TAG, LogFlag.DL) { "onStrategySelected: intercepted by Watermark Removing." }
                return@launch
            }

            val newStrategyID = vmBus?.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID)
            GLog.d(TAG, LogFlag.DL) { "onStrategySelected: curId: $curStrategyID vs newId: $newStrategyID" }
            // 已经发生跳转(curStrategyID != newStrategyID)，或者重复strategy(strategyID == newStrategyID)都不跳转了
            if ((curStrategyID == newStrategyID) && (strategyID != newStrategyID)) {
                notifyToNewStrategyID(strategyID)
            } else {
                GLog.d(TAG, LogFlag.DL) {
                    "onStrategySelected: Switching to same page or old page is changed, ignore enter sub."
                }
            }
        }
    }

    private fun notifyToNewStrategyID(strategyID: Int) {
        // 通知创建次级栈
        vmBus?.notifyOnce(REPLY_TOPIC_OPERATING_ENTER_SUB)
        strategyIDPub?.publish(strategyID)
    }

    /**
     * 预检查，Mark by wudanyang: 这里需要把下载的放到ComponentVM里面@zhegnyirui @chenwenjun
     * @param strategyID 页面策略id
     * @param detectingResult 检测结果，用于支撑判断，比如人脸检测结果
     * @param function 是否支持回调
     */
    private fun checkSupportability(strategyID: Int, detectingResult: DetectingResult, function: (isSupport: Boolean) -> Unit) {
        when (strategyID) {
            R.id.strategy_ai_eliminate -> {
                AIEliminateSupportCheckStrategy(
                    mediaSize = getImagePreviewSize(),
                    onNormalEliminateCheckSupportability = {
                        CommonModelSupportCheckStrategy(EliminatePenModelLoader(app))
                    }
                )
            }
            R.id.strategy_ai_best_take -> BestTakeSupportCheckStrategy(getImagePreviewSize(), detectingResult)
            R.id.strategy_ai_deblur -> AIDeBlurCheckStrategy(getImagePreviewSize())
            R.id.strategy_ai_dereflection -> AIDeReflectionStrategy(getImagePreviewSize())
            R.id.strategy_beauty -> BeautySupportCheckStrategy(detectingResult, InputFuncType.EDIT) { vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM) }
            R.id.strategy_image_quality_enhance -> ImageQualityEnhanceSupportCheckStrategy(getImagePreviewSize())
            R.id.strategy_olive -> OliveSupportCheckStrategy(vmBus)
            R.id.strategy_rm_specific_image_quality_enhance -> AihdSupportCheckStrategy(getImagePreviewSize())
            R.id.strategy_rm_ai_scenery -> AIScenerySupportCheckStrategy()
            R.id.strategy_repair -> RepairSupportCheckStrategy(detectingResult)
            R.id.strategy_ai_graffiti -> AIGraffitiSupportCheckStrategy(getImagePreviewSize())
            R.id.strategy_transform -> TransformSupportCheckStrategy()
            R.id.strategy_ai_composition -> AiCompositionSupportCheckStrategy(getOriginImageSize(), vmBus)
            R.id.strategy_ai_lighting -> AiLightingSupportCheckStrategy(detectingResult, getOriginImageSize(), vmBus)
            R.id.strategy_portrait_blur -> PortraitBlurSupportCheckStrategy(vmBus)
            R.id.strategy_ai_deglare -> AIDeGlareCheckStrategy(getImagePreviewSize())
            R.id.strategy_ai_defog -> AIDeFogCheckStrategy(getImagePreviewSize())
            else -> null
        }?.also {
            supportCheckStrategy = it
            it.checkSupportability(app, function) { action ->
                vmBus?.notifyOnce(TOPIC_NOTIFICATION_ACTION, action)
            }
        } ?: function(true)
    }

    /**
     * 取出一张图在编辑框架内，当前最新的尺寸。
     * 若经过了裁剪编辑，返回的是裁剪后的size。
     *
     * 特别提示：
     * 因一张图不管多大，进入编辑页后，会将长边上限压到4096以内（目前是这个值，但是时机受参数调整控制），对应的短边等比例缩小。
     *  *   ps：长截屏会有特殊处理流程：不压长边到4096.
     */
    private fun getImagePreviewSize(): Size {
        return vmBus?.get<PreviewAnimationProperties>(TOPIC_PREVIEW_ANIMATION_PROPERTIES)?.let {
            Size(it.imageWidth.toInt(), it.imageHeight.toInt())
        } ?: Size(0, 0)
    }

    /**
     * 取出当前正在编辑的图对应原图的size
     */
    private fun getOriginImageSize(): Size {
        return vmBus?.get<MediaItem>(TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM)?.toOriginalItem()?.let {
            Size(it.width, it.height)
        } ?: Size(0, 0)
    }

    /**
     * 判断当前图片是否经过了任意编辑
     */
    private fun imageEdited(): Boolean {
        val edited = vmBus?.get<Boolean>(TOPIC_OPERATING_MODIFY_STATE) ?: false
        return edited
    }

    private fun enterEditor(editType: String?) {
        editingVM.apply {
            val strategyID = when (editType) {
                PhotoEditorType.PORTRAIT_BLUR.tag -> R.id.strategy_specific_portrait_blur
                PhotoEditorType.SUPER_TEXT.tag -> R.id.strategy_specific_super_text
                PhotoEditorType.AI_REPAIR_DEBLUR.tag -> R.id.strategy_specific_ai_deblur
                PhotoEditorType.AI_REPAIR_DEREFLECTION.tag -> R.id.strategy_specific_ai_dereflection
                PhotoEditorType.AI_LIGHTING.tag -> R.id.strategy_specific_ai_lighting
                PhotoEditorType.PASSERBY_ELIMINATE.tag -> R.id.strategy_specific_passerby_eliminate
                PhotoEditorType.AI_ELIMINATE.tag -> R.id.strategy_specific_ai_eliminate
                PhotoEditorType.AI_COMPOSITION.tag -> R.id.strategy_specific_ai_composition
                PhotoEditorType.IMAGE_QUALITY_ENHANCE.tag -> {
                    // realme AI超清与AI超清像素功能重叠，realme上跳AI超清，oppo上跳AI超清像素
                    if (ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND)) {
                        R.id.strategy_rm_specific_image_quality_enhance
                    } else {
                        R.id.strategy_specific_image_quality_enhance
                    }
                }
                PhotoEditorType.AI_SCENERY.tag -> R.id.strategy_rm_ai_scenery
                PhotoEditorType.PRIVACY_WATERMARK.tag -> R.id.strategy_privacy_watermark_master
                PhotoEditorType.WATERMARK_CAMERA.tag -> R.id.strategy_watermark_camera
                PhotoEditorType.AI_BESTTAKE.tag -> R.id.strategy_specific_ai_best_take
                PhotoEditorType.PORTRAIT_BLUR.tag -> R.id.strategy_specific_portrait_blur
                else -> R.id.strategy_editing_page
            }
            // 超级文本2.0识文页不隐藏状态栏
            notifyStatusBarStateChanged(strategyID == R.id.strategy_specific_super_text)
            vmBus?.apply {
                // 触发页面切换
                strategyIDPub?.publish(strategyID)

                if (strategyID == R.id.strategy_editing_page) {
                    strategyTypePub?.publish(StrategyType.EDITING_PAGE)
                    // 加载菜单
                    notifyOnce(REPLY_TOPIC_MENU_LOAD, strategyID)
                    // 进入编辑一级页面时拉取云端模型信息作为预加载，后续选中菜单时可以节省时间
                    notifyOnce(REPLY_TOPIC_FETCH_REMOTE_MODEL_INFO, TAG)
                    // 写入自动打码模型是否需要下载或者更新
                    launch(Dispatchers.IO) {
                        AutoMosaicConfigHelper.writeAutoMosaicDownloadConfig(getApplication())
                    }
                } else {
                    strategyTypePub?.publish(StrategyType.SPECIFIC_PAGE)
                    GLog.d(TAG, LogFlag.DL) { "enterEditor: strategyID = $strategyID" }
                }
            }
        }
    }

    /**
     * 通知沉浸式状态改变
     * @param shouldShow true:显示状态栏;false:隐藏状态栏
     */
    private fun notifyStatusBarStateChanged(shouldShow: Boolean) {
        statusBarStatePub?.publish(shouldShow)
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unsubscribe(TOPIC_MENU_SELECTED_STRATEGY, strategySelectedObserver)
            unsubscribe(TOPIC_INPUTS_EDIT_TYPE, enterEditorObserver)

            unsubscribeR(REPLY_TOPIC_COMPONENT_CHECK, componentCheckNtf)
            unsubscribeR(TOPIC_HDR_SUPPORT_CHECK, hdrCheckNft)
            unsubscribeR(TOPIC_OLIVE_SUPPORT_CHECK, oliveCheckNft)
            unsubscribeR(TOPIC_REMOVE_WATERMARK_ENTER_SUBPAGE, removeWatermarkNft)
            unsubscribeR(TOPIC_DETECTING_BY_STRATEGY, detectingNtf)
            unregisterDuplexT(TOPIC_MANAGEMENT_STRATEGY_ID, strategyIDRep, strategyIDPub)
            unregisterT(TOPIC_MANAGEMENT_STATUS_BAR_STATE, statusBarStatePub)
            unregisterT(TOPIC_MANAGEMENT_STRATEGY_TYPE, strategyTypePub)
        }
        supportCheckStrategy?.destroy()
        supportCheckStrategy = null
    }

    internal companion object {
        const val TAG = "PageManagementVM"
    }
}

/**
 * 是否独立编辑页面（区别于普通编辑页）
 * 独立编辑页很多动作可以省略
 * @return 如果是独立编辑的话返回true，反之false
 */
fun IViewModelBus.isSpecificStrategy(): Boolean {
    val strategy = get<StrategyType>(TOPIC_MANAGEMENT_STRATEGY_TYPE)
    if (strategy == StrategyType.SPECIFIC_PAGE) {
        return true
    }
    return false
}

/**
 * 页面策略类型
 */
enum class StrategyType {
    /**
     * 普通编辑页
     */
    EDITING_PAGE,

    /**
     * 特定编辑页
     */
    SPECIFIC_PAGE
}