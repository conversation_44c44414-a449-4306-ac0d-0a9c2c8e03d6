/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: -OliveVideoEditVM
 ** Description: OliveVideoEditVM
 ** Version: 1.0
 ** Date : 2024/7/30
 ** Author: dingyong@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  dingyong@Apps.Gallery3D      2024/07/23    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.vm

import android.app.Application
import android.graphics.Bitmap
import android.graphics.RectF
import android.graphics.SurfaceTexture
import android.net.Uri
import android.text.TextUtils
import android.util.Rational
import android.util.Size
import androidx.core.util.component1
import androidx.core.util.component2
import com.oplus.gallery.addon.os.VibratorUtils
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isOlivePhoto
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IEditItemSupportFormat.SUPPORT_FORMAT_NONE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IEditItemSupportFormat.SUPPORT_FORMAT_OLIVE
import com.oplus.gallery.business_lib.model.data.base.utils.Constants.IEditItemSupportFormat.SUPPORT_FORMAT_PRO_XDR
import com.oplus.gallery.business_lib.util.OliveVideoEditUtils
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.codec.extend.EXTEND_KEY_HDR_TRANSFORM_DATA
import com.oplus.gallery.foundation.codec.extend.HdrTransformDataStruct
import com.oplus.gallery.foundation.exif.oplus.OplusExifTag
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.opengl2.texture.OesRawTexture
import com.oplus.gallery.foundation.opengl2.texture.TexConfig
import com.oplus.gallery.foundation.ui.notification.NotificationAction
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_IS_LIVE_PHOTO_SUPPORT_FILTER
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.ext.maxLen
import com.oplus.gallery.foundation.util.ext.minLen
import com.oplus.gallery.foundation.util.ext.toList
import com.oplus.gallery.foundation.util.systemcore.ResourceUtils
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_LUMO_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PROJECT_SAVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_SAVE_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_ONEPLUS_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_OPPO_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REALME_BRAND
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.IS_REGION_CN
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Common.SystemInfo.PRODUCT_BRAND
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.watermark.IWatermarkMasterAbility
import com.oplus.gallery.framework.abilities.watermark.file.PhoneInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_CUR_PIC_BLUR
import com.oplus.gallery.framework.abilities.watermark.masterstyle.WatermarkMasterStyle
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.EmptyReplier
import com.oplus.gallery.photoeditor.editingvvm.SingleArgReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Adjust.TOPIC_ADJUST_AUTO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.EditingWatermark.TOPIC_WATERMARK_RECORD_FOR_SUBPAGE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_ORIGIN_WATERMARK_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Management.TOPIC_MANAGEMENT_STRATEGY_ID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.REPLY_TOPIC_CHECK_PLAY_VIDEO_FPS_PROXY
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_OLIVE_IS_EDITING
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_OLIVE_IS_VIDEO_CHANGED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_OLIVE_SUPPORT_CHECK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_PREVIEW_OLIVE_VIDEO_TEXTURE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OLiveVideoEdit.TOPIC_SHOULD_TOGGLE_OLIVE_VIDEO_WHEN_LONG_PRESSED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_IMAGE_PACK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_OPERATING_MEMO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_WATERMARK_RECORD_RECOVER_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_IS_PREVIEW_LOADED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_HDR_SDR_RATIO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_INVALIDATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_SURFACE_SIZE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.TOPIC_ORIGIN_TRANSFORM_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transform.TOPIC_TRANSFORM_UI_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.WatermarkMaster.TOPIC_PRIVACY_WATERMARK_MASTER_DATA_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.WatermarkMaster.TOPIC_WATERMARK_MASTER_VIDEO_DISPLAY_RECT
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.adjustment.AdjustRecord
import com.oplus.gallery.photoeditor.editingvvm.aicomposition.AiCompositionRecord
import com.oplus.gallery.photoeditor.editingvvm.filter.FilterRecord
import com.oplus.gallery.photoeditor.editingvvm.filter.asFilterRecord
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.DataSource
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.OLivePhotoDataSource
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.olive.OLiveEditConstant.INVALID_TIME_MS
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveRecord
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveRecord.Companion.COVER_TIME
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveRecord.Companion.OLIVE_ENABLE
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveRecord.Companion.OLIVE_SOUND_ENABLE
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveSupportCheckStrategy.Companion.isSupportOliveEdit
import com.oplus.gallery.photoeditor.editingvvm.olive.OliveVM
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.OliveVideoEdgeEditorImp.Companion.DEFAULT_DISPLAY_PERCENT
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.IOliveVideoEngine
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.OliveTransformConfig
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.OliveVideoEngine
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.OliveVideoInfo.Companion.BIT_COUNT_SDR
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine.VideoSourceDescriptor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.utils.OliveVideoEditAdjustUtils
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.utils.OliveVideoEditFilterUtils
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.utils.OliveVideoEditFilterUtils.createEditFilterEffects
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.utils.OliveVideoEditTransformUtils
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark.OliveVideoEditWatermarkConverter
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.operating.FindRecordParams
import com.oplus.gallery.photoeditor.editingvvm.operating.NullRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingMemo
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_CANCEL_EXIT_SUB_PAGE
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_COMPLETE_EXIT_SUB_PAGE
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_ENTER_SUBPAGE
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_NOTIFY_RECORD_STEP
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_PERFORM_REDO
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordWithInvoker.Companion.INVOKE_TYPE_PERFORM_UNDO
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingVM.Companion.ALL_OPERATING_RECORDS
import com.oplus.gallery.photoeditor.editingvvm.operating.RecordFindType
import com.oplus.gallery.photoeditor.editingvvm.operating.STACK_INDEX_ALL
import com.oplus.gallery.photoeditor.editingvvm.pagecontainer.isSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.preview.LongPressEvent
import com.oplus.gallery.photoeditor.editingvvm.preview.OliveVideoSize
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformRecord
import com.oplus.gallery.photoeditor.editingvvm.transform.TransformUIBean
import com.oplus.gallery.photoeditor.editingvvm.transform.asTransformRecord
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.editingvvm.watermark.PrivacyDataStatus
import com.oplus.gallery.photoeditor.editingvvm.watermark.WatermarkRecord
import com.oplus.gallery.photoeditor.track.OLiveEditorTracker
import com.oplus.gallery.photoeditor.util.isSupport
import com.oplus.gallery.standard_lib.app.AppConstants
import com.oplus.gallery.standard_lib.codec.player.effect.EffectUtil
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_1_MS_IN_US
import com.oplus.gallery.standard_lib.util.chrono.TimeUtils.TIME_1_SEC_IN_MS
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID
import kotlin.math.min
import kotlin.system.measureTimeMillis

/**
 * 负责OLive视频编辑的VM, 这个个VM是全局的。
 * @param editingVM 主ViewModel
 */
internal class OliveVideoEditVM(editingVM: EditingVM) : EditingSubVM(editingVM) {
    private var oliveVideoEngine: IOliveVideoEngine? = null
    private var videoDisplayRectPub: PublisherChannel<RectF?>? = null
    private var oliveIsEditingPub: PublisherChannel<Boolean>? = null
    private var invalidatePub: PublisherChannel<Boolean>? = null
    private var lastCurrentStepRecord: OperatingRecord? = null
    private var oliveVideoSize: OliveVideoSize? = null
    private var oliveCheckData: OliveCheckData? = null


    /**
     * 第一次进入裁剪也页面的记录，反撤销时候如果操作栈中没有裁剪的记录了，用这个原始的记录来还原裁剪
     */
    private var originTransformRecord: TransformRecord? = null

    /**
     * 实况视频水印的效果数据的转换器。可把封面水印的Record，转换为视频添加移除水印的Effects
     */
    private var watermarkConverter: OliveVideoEditWatermarkConverter? = null

    /**
     * 当前正在添加水印record对应的HashCode, 用于规避Live Photo水印未加载完成,
     * 就长按播放预览图显示没有水印图片和上下边距被裁剪图片，视觉上卡顿问题
     */
    private var currentLoadingHashCode: Int? = null

    /**
     *  当前水印操作的标识。解决场景：前一个水印还没添加完成，下一个已经走了remove移除
     */
    private var currentWatermarkActionFlag: String? = null

    /*
     * 是否支持olive编辑
     */
    private val isSupportSaveOlive: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false)
    }

    private val videoPreviewTextureListener = SurfaceTexture.OnFrameAvailableListener {
        if (oliveVideoEngine == null) return@OnFrameAvailableListener
        videoPreviewTexture?.let {
            invalidatePub?.publish(true)

            if (needWaitVideoTexture.not()) return@OnFrameAvailableListener
            needWaitVideoTexture = false
            if (needShowVideoTexture) {
                vmBus?.notifyOnce(REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO, true)
            } else {
                GLog.d(TAG, LogFlag.DL, "videoPreviewTextureListener needShowVideoTexture is false")
            }
        }
    }

    /**
     * Olive视频预览的TextureId
     */
    private var videoPreviewTexture: OesRawTexture? = null
        set(value) {
            if (field == value) return
            delayDoubleReuseOesTex(field)
            field = value
            needWaitVideoTexture = true
            if (value != null) {
                oliveVideoTexturePub?.publish(value)
            }
        }

    /**
     * OliveVideoEngine 引擎
     */
    private var oliveVideoEnginePub: PublisherChannel<IOliveVideoEngine>? = null

    /**
     * 视频帧
     */
    private var oliveVideoTexturePub: PublisherChannel<ITexture>? = null

    /**
     * 通知显示视频帧前是否应该等待视频栈
     * 当视频帧发生了变化，需要等视频帧出来之后再通知预览页面显示视频帧，防止显示上一次的视频帧
     */
    @Volatile
    private var needWaitVideoTexture = false

    /**
     * 是否要展示视频帧
     * 长按时需要展示，抬手后不应该展示
     */
    @Volatile
    private var needShowVideoTexture = false

    /**
     * 是否设置过帧率和尺寸
     */
    private var hasSetVideoFpsAndSize = false

    /**
     * 操作栈的监听处理
     */
    private val stepRecordObserver: TObserver<OperatingRecordWithInvoker> = { currentRecord ->
        // 监听操作栈记录处理特效结果
        setVideoEffectWhenStepRecord(currentRecord)
    }

    /**
     * 获取GestureAnimator
     */
    private val animationProperties: PreviewAnimationProperties? by lazy {
        vmBus?.get<PreviewAnimationProperties>(TOPIC_PREVIEW_ANIMATION_PROPERTIES)
    }

    /**
     * 校验预览是否更新帧率
     */
    private val checkOliveFpsAndSizeRep = object : UnitReplier<Boolean>() {
        override fun onSingleReply(arg: Boolean) {
            notifyCheckOliveFpsAndProxy(arg)
        }
    }

    /**
     * 第一次进入旋转页面的记录大小监听
     */
    private val originTransformRecordObserver = object : UnitReplier<Boolean>() {
        override fun onSingleReply(arg: Boolean) {
            notifyOriginTransform(arg)
        }
    }

    /**
     * 监听项目化复原
     */
    private val onOperatingMemoObserver: TObserver<OperatingMemo> = {
        onOperatingMemoChanged(it)
    }

    private val originWatermarkInfoObserver: TObserver<WatermarkInfo> = { watermarkInfo ->
        // 只需要判断是否需要去水印
        // livephoto 视频目前只支持画框类水印，非画框类应该去掉画框水印
        val isFrameTypeWatermark = watermarkInfo.aiWatermarkMasterParams?.watermarkMasterStyle?.let {
            it.isOnlyFrameWatermark()
        } ?: false
        if (watermarkInfo.isAiMasterWatermark() && isFrameTypeWatermark) {
            val phoneInfo = vmBus?.get<PhoneInfo>(TopicID.InputArguments.TOPIC_PHONE_INFO) ?: PhoneInfo()
            val record = WatermarkRecord.createRemoveWatermarkRecord(watermarkInfo, phoneInfo, null)
            onWatermarkRecordRecoverEffect(record, "originWatermarkInfoObserver")
        }
    }

    private val imagePackObserver: TObserver<EditingImagePack> = {
        it.watermarkInfo?.apply {
            GLog.d(TAG, LogFlag.DL) {
                "[imagePackObserver] Params.videoDisplayRectF: ${aiWatermarkMasterParams?.videoDisplayRectF}," +
                    " Extend.videoDisplayRect: ${aiWatermarkFileExtendInfo?.videoDisplayRect}"
            }
            val engine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
            val videoDisplayRectF = aiWatermarkMasterParams?.videoDisplayRectF
            if (engine?.getWatermarkEditor()?.getWithoutWatermarkVideoSize() != null) {
                // 已经有值，应该是项目化设置的，无需再设置
                GLog.d(TAG, LogFlag.DL, "WithoutWatermarkVideoSize has value, no need set again")
                return@apply
            }
            if (videoDisplayRectF != null) {
                engine?.getWatermarkEditor()
                    ?.updateWithoutWatermarkVideoSize(Size(videoDisplayRectF.width().toInt(), videoDisplayRectF.height().toInt()))
                updateVideoDisplayRect(videoDisplayRectF)
            } else {
                engine?.getWatermarkEditor()?.updateWithoutWatermarkVideoSize(engine.getVideoSize())
            }
        }
    }

    private val onWatermarkRecordForSubpageChangeObserver: TObserver<OperatingRecordWithInvoker> = {
        onWatermarkRecordForSubpageChange(it)
    }

    private val onWatermarkRecordRecoverEffectObserver: TObserver<WatermarkRecord> = {
        onWatermarkRecordRecoverEffect(it, "onWatermarkRecordRecoverEffectObserver")
    }

    /**
     * 初始化原始的裁剪记录，进入裁剪页还原裁剪记录后调用
     */
    private fun initOriginTransformRecord() {
        GLog.d(TAG, LogFlag.DF, "initOriginTransformRecord")
        if (originTransformRecord != null) {
            return
        }
        animationProperties?.let { properties ->
            val bean = vmBus?.get<TransformUIBean>(TOPIC_TRANSFORM_UI_STATE) ?: TransformUIBean()
            val surfaceSize = vmBus?.get<Size>(TOPIC_PREVIEW_SURFACE_SIZE)
            val matrix = properties.fullTransform.default
            val clipRect = RectF(properties.clipRect.default)
            val imageRect = RectF(properties.imageRect)
            originTransformRecord = TransformRecord(
                axisAngleTransform = properties.fullPose.default.compositeRotate,
                rotate = bean.rotateOrientation,
                ruleAngle = bean.ruleRotateAngle,
                ratio = bean.ratio,
                matrix = matrix,
                clipRect = clipRect.toList(),
                imageRect = imageRect.toList(),
                viewportWidth = surfaceSize?.width ?: 0,
                viewportHeight = surfaceSize?.height ?: 0,
                isHorizontalMirror = bean.mirrorOrientation.isHorizontalMirror,
            )
            if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
                GLog.d(TAG, LogFlag.DF, "notifyOriginTransform onAnimationStop originTransformRecord = $originTransformRecord")
            }
        }
    }

    init {
        GLog.d(TAG, LogFlag.DL, "init")
        vmBus?.apply {
            oliveVideoEnginePub = register(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
            oliveVideoTexturePub = register(TOPIC_PREVIEW_OLIVE_VIDEO_TEXTURE)
            invalidatePub = register(TOPIC_PREVIEW_INVALIDATE)
            GLog.d(TAG, LogFlag.DL, "init subscribeT  topic_operating_record")
        }
    }

    override fun onCreate() {
        super.onCreate()
        vmBus?.apply {
            register(TOPIC_OLIVE_SUPPORT_CHECK, checkSupportOliveRep)
            register(TOPIC_OLIVE_IS_VIDEO_CHANGED, isVideoChangedRep)
            register(TOPIC_SHOULD_TOGGLE_OLIVE_VIDEO_WHEN_LONG_PRESSED, onLongPressRep)
            register(REPLY_TOPIC_CHECK_PLAY_VIDEO_FPS_PROXY, checkOliveFpsAndSizeRep)
            subscribeT(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE, ::onOlivePhotoEditInfoChanged)
            subscribeT(TOPIC_WATERMARK_RECORD_FOR_SUBPAGE, onWatermarkRecordForSubpageChangeObserver)
            videoDisplayRectPub = register(TOPIC_WATERMARK_MASTER_VIDEO_DISPLAY_RECT)
            oliveIsEditingPub = register(TOPIC_OLIVE_IS_EDITING)
        }
        if (isSupportSaveOlive.not()) {
            return
        }
        vmBus?.apply {
            // 操作栈
            subscribeT(TOPIC_OPERATING_RECORD, stepRecordObserver)
            // 复原
            subscribeT(TOPIC_OPERATING_OPERATING_MEMO, onOperatingMemoObserver)
            //自动调节
            register(TOPIC_ADJUST_AUTO, autoAdjustObserver)
            // 第一次进入裁剪页面的旋转参数记录
            register(TOPIC_ORIGIN_TRANSFORM_RECORD, originTransformRecordObserver)
            subscribeT(TOPIC_WATERMARK_RECORD_RECOVER_EFFECT, onWatermarkRecordRecoverEffectObserver)
            subscribeT(TOPIC_IMAGE_PACK, imagePackObserver)
            subscribeT(TOPIC_ORIGIN_WATERMARK_INFO, originWatermarkInfoObserver)
        }
    }

    private val isVideoChangedRep = object : EmptyReplier<Boolean>() {
        override fun onEmptyReply(): Boolean {
            val engine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE) ?: return false
            return getOriginVideoSize() != engine.getVideoSize()
        }
    }

    private val checkSupportOliveRep = object : UnitReplier<OliveCheckArg>() {
        override fun onSingleReply(arg: OliveCheckArg) {
            checkOliveSupport(arg)
        }
    }

    private val onLongPressRep = object : SingleArgReplierChannel<LongPressEvent, Boolean>() {
        override fun onSingleReply(arg: LongPressEvent): Boolean {
            return onPreviewLongPressed(arg)
        }
    }

    private fun updatePreviewSurfaceTexture(engine: IOliveVideoEngine) {
        // 大小发生变化后将上一个Texture释放掉，重新创建一个。
        val oliveVideoSizeChangeListener: ((w: Int, h: Int) -> Unit) = { w, h ->
            GLog.d(TAG, LogFlag.DL, "updatePreviewSurfaceTexture setOnVideoSizeChangeListener")
            editingVM.submitOnGLIdle {
                val videoInfo = engine.retrieveInfo()
                // 如果视频的bit数大于8，就默认用10的，屏幕的bit数目前不会超过10
                val texConfig = if (videoInfo.componentBitCount > BIT_COUNT_SDR) TexConfig.RGBA_1010102 else TexConfig.ARGB_8888
                // videoPreviewTexture重写创建不用销毁旧的，纹理会被RendererAdapter管理
                val videoPreviewTexture = OesRawTexture(w, h, texConfig, videoInfo.displayColorSpace).apply {
                    load()
                }

                launch(Dispatchers.Main) {
                    val isPlaying = engine.isPlayed
                    if (isPlaying) {
                        engine.stop()
                    }
                    videoPreviewTexture.surfaceTexture?.let { surfaceTexture ->
                        engine.updatePreviewSurfaceTexture(surfaceTexture)
                        videoPreviewTexture.onFrameAvailableListener = videoPreviewTextureListener
                    }
                    if (isPlaying) {
                        engine.play {
                            <EMAIL> = videoPreviewTexture
                        }
                    } else {
                        <EMAIL> = videoPreviewTexture
                    }
                }
            }
        }
        engine.getTransformEditor()?.setOnVideoSizeChangeListener { w, h ->
            engine.getWatermarkEditor()?.updateWithoutWatermarkVideoSize(Size(w, h))
            oliveVideoSizeChangeListener.invoke(w, h)
        }
        engine.getWatermarkEditor()?.setOnVideoSizeChangeListener(oliveVideoSizeChangeListener)
    }

    private fun updateOliveVideoPlayRange(engine: IOliveVideoEngine) {
        vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)?.oLivePhoto?.microVideo?.let {
            engine.setPlayRange(it.videoStartUs ?: 0L, it.videoEndUs ?: Long.MAX_VALUE)
        }
    }

    private fun onOlivePhotoEditInfoChanged(dataSource: OLivePhotoDataSource) {
        GLog.d(TAG, "[onOlivePhotoEditInfoChanged] called on thread: ${Thread.currentThread().name}")
        if (oliveVideoEngine != null) {
            GLog.w(TAG, "[onOlivePhotoEditInfoChanged] oliveVideoEngine already init")
            return
        }
        tryInitOliveVideoEngine(dataSource)
        createVideoPreviewSink()
    }

    private fun tryInitOliveVideoEngine(dataSource: OLivePhotoDataSource) {
        val initEngineTimeCost = measureTimeMillis {
            val oliveVideoEngine = OliveVideoEngine(app)

            // 获取下变换参数
            val hdrTransformData = (editingVM.sessionProxy.getMetadata(
                MetadataType.EXTENDED,
                EXTEND_KEY_HDR_TRANSFORM_DATA
            ) as? ByteArray)?.let { HdrTransformDataStruct(it).toData() }

            val microVideo = (dataSource.originalOLivePhoto ?: dataSource.oLivePhoto).microVideo.getOrLog(TAG, "microVideo") ?: return

            oliveVideoEngine.initFromVideo(
                sourceUri = dataSource.originalOliveUri ?: dataSource.oliveUri,
                VideoSourceDescriptor.MIX(offset = microVideo.offset, length = microVideo.length),
                OliveTransformConfig(hdrTransformData) { vmBus?.get(TOPIC_PREVIEW_HDR_SDR_RATIO) ?: 1.0f }
            )?.also { initError ->
                GLog.e(TAG, LogFlag.DF, "[tryInitOliveVideoEngine] init engine failed: $initError")
                return
            }
            updatePreviewSurfaceTexture(oliveVideoEngine)
            updateOliveVideoPlayRange(oliveVideoEngine)
            updateVideoPreviewCrop(oliveVideoEngine)

            this.oliveVideoEngine = oliveVideoEngine

            oliveVideoEnginePub?.publish(oliveVideoEngine)

            // 实例化olive校验数据类
            val fpsRational = oliveVideoEngine.gatVideoFps()
            val videoSize = oliveVideoEngine.getVideoSize()
            oliveCheckData = OliveCheckData(fpsRational, videoSize)
        }
        GLog.d(TAG, "[tryInitOliveVideoEngine] init engine cost: $initEngineTimeCost")
    }

    /**
     * 对有模糊边缘的视频需要去掉模糊边缘
     *
     * @param oliveVideoEngine 视频编辑器
     */
    private fun updateVideoPreviewCrop(oliveVideoEngine: OliveVideoEngine) {
        val uri = oliveVideoEngine.sourceUri.getOrLog(OliveVM.TAG, "[updateVideoPreviewCrop] sourceUri") ?: return
        val descriptor = oliveVideoEngine.sourceDescriptor.getOrLog(OliveVM.TAG, "[updateVideoPreviewCrop] sourceDescriptor") ?: return
        val offset = (descriptor as? VideoSourceDescriptor.MIX)?.offset ?: AppConstants.Number.NUMBER_MINUS_1.toLong()

        //做过裁剪的图不需要对模糊边缘进行处理
        val recordsAfterNonparametric = vmBus?.notifyOnce<List<OperatingRecord>>(
            TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORDS_AFTER_NONPARAMETRIC, TextUtil.EMPTY_STRING
        )
        if (recordsAfterNonparametric?.any { it.effectName == AvEffect.TRANSFORM_EFFECT } == true) {
            return
        }

        /**
         * 对视频四周有模糊边缘的视频通过放大视频去掉模糊。
         * 1.有水印的实况放大视频内容区域，
         * 2.无水印的实况图片放大整个视频
         */
        val eisInfo = EffectUtil.getEisInfo(uri = uri, offset = offset)
        eisInfo?.eisCropFactor?.let { scale ->
            if (DEFAULT_DISPLAY_PERCENT.compareTo(scale[0]) >= 0) {
                return
            }

            val mediaItem = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM) ?: return
            val videoContentRect = editingVM.getApplication<Application>().getAppAbility<IWatermarkMasterAbility>()
                ?.newWatermarkFileOperator(mediaItem.contentUri)?.use {
                    it.getAiMasterWatermarkExtInfo()?.videoDisplayRect
                }
            oliveVideoEngine.getVideoEdgeEditor()?.scaleVideoContent(videoContentRect, scale[0])
        }

        val videoMinLen = oliveVideoEngine.getVideoSize().minLen()
        if (isNeedCrop(uri) && (videoMinLen > 0)) {
            val videoMaxLen = oliveVideoEngine.getVideoSize().maxLen()
            val percent = oliveVideoEngine.retrieveInfo().size.let {
                min(it.maxLen().toFloat() / videoMaxLen, it.minLen().toFloat() / videoMinLen)
            }
            /**
             * 视频实际尺寸比显示尺寸小，应该是经过了字节对齐，
             * 美摄在显示时会将加大的部分，用透明像素补充，这样在加水印的空白像素就表现为黑边。现在扩大一点将透明像素裁剪掉
             */
            if ((percent > 0) && (DEFAULT_DISPLAY_PERCENT.compareTo(percent) > 0)) {
                oliveVideoEngine.getVideoEdgeEditor()?.removeFuzzyEdge(percent)
            }
        }
    }

    /**
     * 视频有水印的图不需要对模糊边缘进行处理
     */
    private fun isNeedCrop(uri: Uri): Boolean {
        ContextGetter.context.getAppAbility<IWatermarkMasterAbility>()?.use { ability ->
            ability.newWatermarkFileOperator(uri)?.use { operator ->
                val extInfo = operator.getAiMasterWatermarkExtInfo()
                // 不带水印的需要裁剪
                val styleId = extInfo?.styleId ?: return true
                val watermarkInfo = operator.readWatermarkInfo()
                val isBrandSame = ConfigAbilityWrapper.getString(PRODUCT_BRAND) == watermarkInfo.content?.make
                if (WatermarkMasterStyle.isBrandSeriesStyle(styleId) && isBrandSame.not()) {
                    // 当原图处理的需要裁剪
                    return true
                }

                if (WatermarkMasterStyle.isBuiltInStyle(
                        styleId, ConfigAbilityWrapper.getBoolean(IS_OPPO_BRAND),
                        ConfigAbilityWrapper.getBoolean(IS_REALME_BRAND),
                        ConfigAbilityWrapper.getBoolean(IS_ONEPLUS_BRAND),
                        ConfigAbilityWrapper.getBoolean(IS_REGION_CN).not(),
                        ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_LUMO_WATERMARK)
                    ) && extInfo.isOnlyFrameWatermark()
                ) {
                    return false
                }
            }
        }
        return true
    }

    private fun createVideoPreviewSink() {
        val engine = oliveVideoEngine ?: return
        val (width, height) = engine.getVideoSize()
        editingVM.submitOnGLIdle {
            val videoInfo = engine.retrieveInfo()
            // 如果视频的bit数大于8，就默认用10的，屏幕的bit数目前不会超过10
            val texConfig = if (videoInfo.componentBitCount > BIT_COUNT_SDR) TexConfig.RGBA_1010102 else TexConfig.ARGB_8888
            val videoPreviewTexture = OesRawTexture(width, height, texConfig, videoInfo.displayColorSpace).apply {
                load()
            }
            launch(Dispatchers.Main) {
                videoPreviewTexture.surfaceTexture?.let { surfaceTexture ->
                    engine.setPreviewSurfaceTexture(surfaceTexture)
                    videoPreviewTexture.onFrameAvailableListener = videoPreviewTextureListener
                }
                <EMAIL> = videoPreviewTexture
            }
        }
        oliveVideoSize = OliveVideoSize(width, height)
    }

    private fun onPreviewLongPressed(event: LongPressEvent): Boolean {
        oliveVideoEngine?.let {
            if (it.hasInit && shouldToggleOliveVideo()) {
                if (currentLoadingHashCode == null) {
                    toggleOliveVideoPreview(it, event)
                } else {
                    GLog.d(TAG, LogFlag.DL) { "[onPreviewLongPressed] not show video,currentLoadingHashCode: $currentLoadingHashCode" }
                }
                return true
            }
        }
        return false
    }

    /**
     * 在支持OLive编辑的页面中，且所有历史编辑都支持Olive
     */
    private fun shouldToggleOliveVideo(): Boolean {
        // mark by jiepengpeng 这里有问题，不应该在previewVM中用watermark业务VM
        if (vmBus?.get<PrivacyDataStatus>(TOPIC_PRIVACY_WATERMARK_MASTER_DATA_STATUS) == PrivacyDataStatus.LOADING) return false
        if (!OLIVE_PREVIEW_STRATEGY_WHITE_LIST.contains(vmBus?.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID))) return false
        if (getOliveStatus().oliveEnable.not()) {
            GLog.d(TAG, LogFlag.DL, "[shouldToggleOliveVideo] oliveEnable false")
            return false
        }
        // 不支持livephoto编辑2.0时，只有在“实况编辑”这个页面支持长按播放，“实况编辑”内部监听了长按逻辑
        return isSupportSaveOlive && isContainUnsupportedOliveEditOperation().not()
    }

    /**
     * 获取实况和声音开关
     * @return OliveStatusBean  实况和声音开关状态
     */
    private fun getOliveStatus(): OliveStatusBean {
        var oliveEnable = true
        var oliveSoundEnable = true
        val olivePhoto = vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)?.oLivePhoto
        olivePhoto?.let {
            val lastRecord =
                vmBus?.notifyOnce<OperatingRecord>(REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC, AvEffect.OliveEffect.name)
            if (lastRecord != null) {
                oliveEnable = lastRecord.arguments[OLIVE_ENABLE] as Boolean
                oliveSoundEnable = lastRecord.arguments[OLIVE_SOUND_ENABLE] as Boolean
            } else {
                oliveEnable = it.oliveEnable ?: true
                oliveSoundEnable = it.oliveSoundEnable ?: true
            }
        }
        GLog.d(TAG, LogFlag.DL, "getOliveStatus  oliveEnable=$oliveEnable,  oliveEnable=$oliveSoundEnable")
        // SOTA:OLive关声音
        val hasAudio = OliveVideoEditUtils.hasAudioData(vmBus?.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.filePath ?: "") ?: true
        return OliveStatusBean(oliveEnable, hasAudio && oliveSoundEnable)
    }

    private fun toggleOliveVideoPreview(oliveVideoEngine: IOliveVideoEngine, event: LongPressEvent) {
        val operatingMemo = vmBus?.get<OperatingMemo>(TOPIC_OPERATING_OPERATING_MEMO)
        if ((vmBus?.get<Boolean>(TOPIC_IS_PREVIEW_LOADED) != true)
            || (operatingMemo == null) || (operatingMemo.hasRecoverEffects.not() && vmBus.isSpecificStrategy().not())
        ) {
            GLog.d(TAG, LogFlag.DL, "toggleOliveVideoPreview  previewLoaded false")
            return
        }
        when (event) {
            is LongPressEvent.Begin -> {
                needShowVideoTexture = true
                oliveVideoEngine.setMute(getOliveStatus().oliveSoundEnable.not())
                adjustOliveVideoFpsAndSizeIfNeed(oliveVideoEngine, true)
                oliveVideoEngine.play {
                    if (needWaitVideoTexture) {
                        GLog.d(TAG, LogFlag.DL, "[toggleOliveVideoPreview]  needWaitVideoTexture true")
                    } else {
                        vmBus.notifyOnce(REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO, true)
                    }
                }
                VibratorUtils.vibrate(app, VibratorUtils.EffectType.VIBRATE_TYPE_WEAK)
                when (vmBus.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID)) {
                    R.id.strategy_editing_page -> {
                        OLiveEditorTracker.trackOLivePlayEvent(OLiveEditorTracker.OLivePlayType.LONG_PRESS_PHOTO_EDIT_FIRST_LEVEL)
                    }

                    else -> OLiveEditorTracker.trackOLivePlayEvent(OLiveEditorTracker.OLivePlayType.LONG_PRESS_PHOTO_EDIT_SECOND_LEVEL)
                }
            }

            is LongPressEvent.Finish -> {
                needShowVideoTexture = false
                needWaitVideoTexture = false
                vmBus.notifyOnce(REPLY_TOPIC_PREVIEW_SHOW_OLIVE_VIDEO, false)
                oliveVideoEngine.stop()
                adjustOliveVideoFpsAndSizeIfNeed(oliveVideoEngine, false)
            }
        }
    }

    /**
     * 通知校验olive视频播放的帧率和尺寸
     * @param isUpdate 是否更新
     */
    private fun notifyCheckOliveFpsAndProxy(isUpdate: Boolean) {
        oliveVideoEngine?.apply {
            adjustOliveVideoFpsAndSizeIfNeed(this, isUpdate)
        }
    }

    /**
     * 针对不同动效等级进行帧率和尺寸修改
     * @param oliveVideoEngine olivevideo引擎
     * @param isUpdate 是否更改
     */
    private fun adjustOliveVideoFpsAndSizeIfNeed(oliveVideoEngine: IOliveVideoEngine, isUpdate: Boolean) {
        // 当未设置过且不需要更新时直接返回
        if (!isUpdate && !hasSetVideoFpsAndSize) return
        val data = oliveCheckData ?: return
        GLog.d(TAG, LogFlag.DL) {
            "[adjustOliveVideoFpsIfNeed],fps=${data.originalFps}, videoSize=${data.videoSize}, " +
                    "isUpdate=$isUpdate, hasSetVideoFpsAndSize=$hasSetVideoFpsAndSize"
        }

        // 处理帧率修改
        if (data.originalFps.toInt() > VIDEO_DEFAULT_FPS) {
            val targetRational = if (isUpdate) Rational(VIDEO_DEFAULT_FPS, 1) else data.originalFps
            val fpsResult = oliveVideoEngine.changeVideoFps(targetRational)
            GLog.d(TAG, LogFlag.DL) { "[adjustOliveVideoFpsIfNeed] changeFps:$fpsResult, currentFps:${oliveVideoEngine.gatVideoFps()}" }
        }

        // 针对4K视频进行分辨率修改
        if (maxOf(data.videoSize.width, data.videoSize.height) >= VIDEO_SIZE_THRESHOLD_FOR_4K) {
            videoPreviewTexture?.let { texture ->
                val sizeLevel = if (isUpdate) 2 else 1
                val updateSizeResult = oliveVideoEngine.updatePreviewSurfaceTexture(texture.surfaceTexture, sizeLevel)
                GLog.d(TAG, LogFlag.DL) { "[adjustOliveVideoFpsIfNeed] updateSizeResult:$updateSizeResult" }
            }
        }

        // 更新状态
        hasSetVideoFpsAndSize = isUpdate
    }

    /**
     * 预检查当前编辑项是否支持弹出olive效果消失弹窗
     * @param function 是否支持回调
     */
    @Suppress("ComplexCondition")
    private fun checkOliveSupport(arg: OliveCheckArg) {
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_SAVE_OLIVE, false).not()) {
            arg.function(true)
            return
        }
        val supportOliveList = ResourceUtils.getResourceIdArrays(
            app, if (DEBUG_IS_LIVE_PHOTO_SUPPORT_FILTER) {
                R.array.picture3d_editor_array_support_olive_state_id
            } else {
                R.array.picture3d_editor_array_support_olive_remove_filter_state_id
            }
        ).toList()
        val supportXdrList = ResourceUtils.getResourceIdArrays(app, R.array.picture3d_editor_array_support_pro_xdr_state_id).toList()
        var supportFormat = if (supportOliveList.contains(arg.strategyID)) SUPPORT_FORMAT_OLIVE else SUPPORT_FORMAT_NONE
        supportFormat = supportFormat or if (supportXdrList.contains(arg.strategyID)) SUPPORT_FORMAT_PRO_XDR else SUPPORT_FORMAT_NONE
        val olivePhotoDataSource = vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)
        val oliveVideoEngine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
        val mediaItem: MediaItem? = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)

        /**
         * 经过产品确认，损坏的实况图，点裁剪，滤镜，调节，大师水印，AI构图这几个支持olive编辑的统一提示实况照片已损坏，无法编辑
         *
         * 判断是损坏的olive图：1.带olive的tag，2.olivePhotoDataSource或oliveVideoEngine为空
         */
        if ((supportFormat.isSupport(SUPPORT_FORMAT_OLIVE))) {
            // 损坏的olive图
            val isDamagedOliveFive = (((mediaItem?.isOlivePhoto() == true) || editingVM.sessionProxy.matchesTagFlag(OplusExifTag.EXIF_TAG_OLIVE)))
                    && ((olivePhotoDataSource == null) || (oliveVideoEngine == null))

            // Ai图像助手不是具体编辑项，不需要提示
            if (isDamagedOliveFive.not() || arg.strategyID == R.id.strategy_ai_assistant) {
                arg.function(true)
                return
            }
            arg.function(false)
            vmBus?.notifyOnce(
                TopicID.Notification.TOPIC_NOTIFICATION_ACTION,
                NotificationAction.ToastAction(R.string.picture3d_editor_olive_photo_error_hint_broken)
            )
            return
        }
        arg.function(true)
    }

    private fun isContainUnsupportedOliveEditOperation(): Boolean {
        val param = FindRecordParams(stackIndex = STACK_INDEX_ALL, findType = RecordFindType.FIND_ALL)
        val operateRecords =
            vmBus?.notifyOnce<List<OperatingRecord>>(REPLY_TOPIC_OPERATING_FIND_SPECIFIC_RECORDS, param) ?: return true
        return isSupportOliveEdit(operateRecords).not()
    }

    private val autoAdjustObserver = object : UnitReplier<AdjustRecord>() {
        override fun onSingleReply(arg: AdjustRecord) {
            vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)?.let { engine ->
                if (lastCurrentStepRecord != arg) {
                    setVideoAdjustEffect(engine, arg)
                }
            }
        }
    }

    private fun onWatermarkRecordForSubpageChange(operatingRecordWithInvoker: OperatingRecordWithInvoker) {
        val engine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
        if (engine == null) {
            GLog.d(TAG, LogFlag.DF, "onWatermarkChange is null so return")
            return
        }
        val record = if (operatingRecordWithInvoker.invokerType == INVOKE_TYPE_ENTER_SUBPAGE) {
            (operatingRecordWithInvoker.operatingRecord as WatermarkRecord).let {
                WatermarkRecord.createRemoveWatermarkRecord(it.watermarkInfo, it.phoneInfo, null)
            }
        } else {
            operatingRecordWithInvoker.operatingRecord as WatermarkRecord
        }
        setVideoWatermarkEffect(engine, record, "onWatermarkRecordForSubpageChange")
    }

    private fun onWatermarkRecordRecoverEffect(watermarkRecord: WatermarkRecord, invokeType: String) {
        val engine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
        if (engine == null) {
            GLog.d(TAG, LogFlag.DF, "onWatermarkRecordRecoverEffect is null so return")
            return
        }
        setVideoWatermarkEffect(engine, watermarkRecord, invokeType)
    }

    /**
     * 监听项目化复原
     */
    private fun onOperatingMemoChanged(operatingMemo: OperatingMemo) {
        if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PROJECT_SAVE, false).not()) {
            //不支持项目化不执行
            GLog.d(TAG, LogFlag.DL, "onOperatingMemoChanged not support_project_save")
            return
        }
        if (operatingMemo.hasRecoverEffects.not()) {
            //没有复原完成不执行
            GLog.d(TAG, LogFlag.DL, "onOperatingMemoChanged not hasRecoverEffects")
            return
        }
        // 拿到复原的数据
        val recordsDataSource = operatingMemo.recoverDataSource?.records ?: run {
            GLog.d(TAG, LogFlag.DL, "onOperatingMemoChanged operatingMemo  recoverDataSource records is null")
            return
        }
        // 数据为null
        if (recordsDataSource.isEmpty()) {
            GLog.d(TAG, LogFlag.DL, "onOperatingMemoChanged recordsDataSource is null")
            return
        }
        GLog.d(TAG, LogFlag.DF, "onOperatingMemoChanged recordsDataSource is ${recordsDataSource.size}")
        val engine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE) ?: return
        for (operatingRecord in recordsDataSource) {
            when (operatingRecord) {
                // 滤镜
                is FilterRecord -> {
                    val record = operatingRecord.asFilterRecord()
                    setVideoFilterEffect(engine, record)
                    GLog.d(TAG, LogFlag.DL, "onOperatingMemoChanged FilterRecord record = $record ")
                }
                // 调节
                is AdjustRecord -> {
                    val record = operatingRecord.copy()
                    setVideoAdjustEffect(engine, record)
                    GLog.d(TAG, LogFlag.DL, "onOperatingMemoChanged AdjustRecord record = $record ")
                }
                // 旋转裁剪
                is TransformRecord -> {
                    val record = operatingRecord.asTransformRecord()
                    setVideoTransform(engine, record)
                    GLog.d(TAG, LogFlag.DL, "onOperatingMemoChanged TransformRecord record = $record ")
                }
                // 实况照片
                is OliveRecord -> {
                    /**
                     * 待实现
                     */
                }
            }
        }
        // 已经处理完成后，需要将事件反注册掉。
        vmBus.unsubscribe(TOPIC_OPERATING_OPERATING_MEMO, onOperatingMemoObserver)
    }

    /**
     * 操作栈的每一步操作后设置特效
     * @param operatingRecordWithInvoker OperatingRecordWithInvoker
     */
    private fun setVideoEffectWhenStepRecord(operatingRecordWithInvoker: OperatingRecordWithInvoker) {
        val engine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
        if (engine == null) {
            GLog.d(TAG, LogFlag.DF, "oliveVideoEngine is null so return")
            return
        }
        val currentRecord = operatingRecordWithInvoker.operatingRecord
        // 防止重复操作
        if (lastCurrentStepRecord == currentRecord) {
            GLog.d(TAG, LogFlag.DL, "setVideoEffectWhenStepRecord lastCurrentRecord=currentRecord so return")
            return
        }
        if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
            GLog.d(
                TAG, LogFlag.DL, "setVideoEffectWhenStepRecord "
                    + " operatingRecord = ${operatingRecordWithInvoker.operatingRecord}"
                    + " invokerType = ${operatingRecordWithInvoker.invokerType} "
            )
        }
        when (operatingRecordWithInvoker.invokerType) {
            // 反撤销
            INVOKE_TYPE_PERFORM_REDO -> {
                GLog.d(TAG, LogFlag.DF, "setVideoEffectWhenStepRecord perform_redo")
                processSetVideoEffects(currentRecord, engine, operatingRecordWithInvoker)
            }
            // 撤销
            INVOKE_TYPE_PERFORM_UNDO -> {
                GLog.d(TAG, LogFlag.DF, "setVideoEffectWhenStepRecord perform_undo")
                cancelOrUndoVideoEffect(engine, INVOKE_TYPE_PERFORM_UNDO)
            }
            // 下一步
            INVOKE_TYPE_NOTIFY_RECORD_STEP -> {
                GLog.d(TAG, LogFlag.DF, "setVideoEffectWhenStepRecord notify_record_step")
                processSetVideoEffects(currentRecord, engine, operatingRecordWithInvoker)
            }
            //点击完成 退出子页面
            INVOKE_TYPE_COMPLETE_EXIT_SUB_PAGE -> {
                val isPersonalizedWatermark = (vmBus?.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID) == R.id.strategy_watermark_personalized_edit)
                GLog.d(TAG, LogFlag.DF, "setVideoEffectWhenStepRecord complete_exit_sub_pag, isPersonalizedWatermark: $isPersonalizedWatermark")
                // 水印个性编辑页，点完成前已经有效果，这里不需要设置效果
                if (isPersonalizedWatermark.not()) {
                    processSetVideoEffects(currentRecord, engine, operatingRecordWithInvoker)
                }
            }
            //点击取消 退出子页面
            INVOKE_TYPE_CANCEL_EXIT_SUB_PAGE -> {
                val isPersonalizedWatermark = (vmBus?.get<Int>(TOPIC_MANAGEMENT_STRATEGY_ID) == R.id.strategy_watermark_personalized_edit)
                GLog.d(TAG, LogFlag.DF, "setVideoEffectWhenStepRecord cancel_exit_sub_page, isPersonalizedWatermark: $isPersonalizedWatermark")
                if (isPersonalizedWatermark) {
                    // 水印个性编辑页的取消，需要恢复为栈顶的水印样式
                    val topRecord = vmBus?.notifyOnce<OperatingRecord>(TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_TOP_RECORD)
                    (topRecord as? WatermarkRecord)?.also {
                        setVideoWatermarkEffect(engine, it, "setVideoEffectWhenStepRecord")
                    }
                } else {
                    cancelOrUndoVideoEffect(engine, INVOKE_TYPE_CANCEL_EXIT_SUB_PAGE)
                }
            }
        }
        lastCurrentStepRecord = currentRecord
    }

    /**
     * 处理视频效果
     *
     * @param record 当前操作记录，可能是 AiCompositionRecord 或普通记录
     * @param engine 视频引擎
     * @param operatingRecordWithInvoker 操作记录和调用类型
     */
    private fun processSetVideoEffects(
        record: OperatingRecord,
        engine: IOliveVideoEngine,
        operatingRecordWithInvoker: OperatingRecordWithInvoker
    ) {
        fun applyEffects(record: OperatingRecord, resetWatermark: Boolean = false) {
            if (resetWatermark) {
                // 需要先去水印
                resetWatermarkIfNeed(record, true)
            }
            // 设置视频特效
            setVideoEffect(record, engine)
            if (resetWatermark) {
                // 撤销时在 cancelOrUndoVideoEffect 里会更新水印信息，仅需要处理反撤销
                resetWatermarkIfNeed(record, false)
            }
        }

        // 根据调用类型决定是否需要处理水印
        val resetWatermark = operatingRecordWithInvoker.invokerType == INVOKE_TYPE_PERFORM_REDO

        // 构图的本质是 滤镜、调节、旋转裁减
        if (record is AiCompositionRecord) {
            // 对 AiCompositionRecord 的每个子记录应用效果
            record.split().forEach { applyEffects(it, resetWatermark) }
        } else {
            // 普通记录直接应用效果
            applyEffects(record, resetWatermark)
        }
    }

    private fun resetWatermarkIfNeed(operatingRecord: OperatingRecord, isOnlyRemoveWatermark: Boolean) {
        val engine = vmBus?.get<IOliveVideoEngine>(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE)
        if (engine == null) {
            GLog.d(TAG, LogFlag.DF, "resetWatermarkIfNeed is null so return")
            return
        }
        if (operatingRecord is TransformRecord) {
            // size可能发生变化需要重新生成水印
            vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
                if (it.watermarkInfo?.hasWatermark() == true) {
                    val phoneInfo = vmBus.get<PhoneInfo>(TopicID.InputArguments.TOPIC_PHONE_INFO) ?: PhoneInfo()
                    val record = WatermarkRecord.createAddWatermarkRecord(it.watermarkInfo, phoneInfo)
                    setVideoWatermarkEffect(engine, record, "resetWatermarkIfNeed_Transform", isOnlyRemoveWatermark)
                }
            }
        } else if ((operatingRecord is AdjustRecord) || (operatingRecord is FilterRecord)) {
            // 调节和滤镜发生变化，如果水印用的是虚化底色，需要重新生成水印
            vmBus?.get<EditingImagePack>(TOPIC_IMAGE_PACK)?.also {
                if (it.watermarkInfo?.hasWatermark() == true
                    && it.watermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle?.background?.backgroundType == BG_TYPE_CUR_PIC_BLUR
                ) {
                    val phoneInfo = vmBus.get<PhoneInfo>(TopicID.InputArguments.TOPIC_PHONE_INFO) ?: PhoneInfo()
                    val record = WatermarkRecord.createAddWatermarkRecord(it.watermarkInfo, phoneInfo)
                    setVideoWatermarkEffect(engine, record, "resetWatermarkIfNeed_AdjustOrFilter", isOnlyRemoveWatermark)
                }
            }
        }
    }

    /**
     * 取消或者撤销特效
     * 找到操作中最近的某一个操作栈，然后将数据设置到引擎
     */
    private fun cancelOrUndoVideoEffect(engine: IOliveVideoEngine, invokeType: String) {
        GLog.d(TAG, LogFlag.DF, "cancelOrUndoVideoEffect invokeType = $invokeType")
        val allRecords = vmBus?.notifyOnce<List<OperatingRecord>>(
            REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, ALL_OPERATING_RECORDS
        )
        // 操作栈中没有记录，则重置所有的特效
        if (allRecords.isNullOrEmpty()) {
            GLog.d(TAG, LogFlag.DF, "cancelOrUndoVideoEffect allRecords is empty")
            resetVideoAllEffect(engine)
            return
        }

        // 需要先去水印，执行编辑特效，执行加水印
        removeWatermarkFirst(allRecords, engine)

        cancelOrUndoFilter(allRecords, engine)
        cancelOrUndoAdjust(allRecords, engine)
        cancelOrUndoTransform(allRecords, engine)
        // 点击取消时不需要恢复水印，因为退出二级页面时已经在onWatermarkRecordForSubpageChange()处理过了，不需要重复执行恢复水印的操作
        if (invokeType != INVOKE_TYPE_CANCEL_EXIT_SUB_PAGE) {
            cancelOrUndoWatermark(allRecords, engine)
        }
        cancelOrUndoPlayRange(allRecords, engine)
    }

    private fun removeWatermarkFirst(allRecords: List<OperatingRecord>, engine: IOliveVideoEngine) {
        var currentWatermarkRecord: WatermarkRecord? = null
        for (record in allRecords) {
            if (record is WatermarkRecord) {
                currentWatermarkRecord = record
                break
            }
        }
        if (currentWatermarkRecord == null) {
            // 操作栈中无水印操作，则需要看原始图片是否带水印
            resetWatermarkEffect(engine, "removeWatermarkFirst", true)
        } else {
            setVideoWatermarkEffect(engine, currentWatermarkRecord, "removeWatermarkFirst", true)
        }
    }

    private fun cancelOrUndoWatermark(allRecords: List<OperatingRecord>, engine: IOliveVideoEngine) {
        var currentWatermarkRecord: WatermarkRecord? = null
        for (record in allRecords) {
            if (record is WatermarkRecord) {
                currentWatermarkRecord = record
                break
            }
        }
        if (currentWatermarkRecord == null) {
            // 操作栈中无水印操作，则需要看原始图片是否带水印
            resetWatermarkEffect(engine, "cancelOrUndoWatermark")
        } else {
            setVideoWatermarkEffect(engine, currentWatermarkRecord, "cancelOrUndoWatermark")
        }
    }

    private fun cancelOrUndoPlayRange(allRecords: List<OperatingRecord>, engine: IOliveVideoEngine) {
        GLog.d(TAG, LogFlag.DF, "cancelOrUndoPlayRange")
        setVideoPlayRange(engine, allRecords.firstOrNull { it is OliveRecord } as? OliveRecord?)
    }

    private fun cancelOrUndoTransform(allRecords: List<OperatingRecord>, engine: IOliveVideoEngine) {
        var currentTransformRecord: TransformRecord? = null
        for (record in allRecords) {
            if (record is TransformRecord) {
                currentTransformRecord = record
                break
            }
        }
        setVideoTransform(engine, currentTransformRecord)
    }

    private fun cancelOrUndoAdjust(allRecords: List<OperatingRecord>, engine: IOliveVideoEngine) {
        var currentAdjustRecord: AdjustRecord? = null
        for (record in allRecords) {
            if (record is AdjustRecord) {
                currentAdjustRecord = record
                break
            }
        }
        setVideoAdjustEffect(engine, currentAdjustRecord)
    }

    /**
     * 取消或者撤销滤镜
     */
    private fun cancelOrUndoFilter(allRecords: List<OperatingRecord>, engine: IOliveVideoEngine) {
        var currentFilterRecord: FilterRecord? = null
        for (record in allRecords) {
            if (record is FilterRecord) {
                currentFilterRecord = record
                break
            }
        }
        setVideoFilterEffect(engine, currentFilterRecord)
    }

    /**
     * 设置特效
     * @param currentRecord OperatingRecord 当前的操作栈记录
     * @param engine IOliveVideoEngine  引擎
     */
    private fun setVideoEffect(currentRecord: OperatingRecord, engine: IOliveVideoEngine) {
        if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
            GLog.d(TAG, LogFlag.DF, "setVideoEffect currentRecord $currentRecord")
        }
        when (currentRecord) {
            is NullRecord -> resetVideoAllEffect(engine)
            is FilterRecord -> setVideoFilterEffect(engine, currentRecord)
            is AdjustRecord -> setVideoAdjustEffect(engine, currentRecord)
            is TransformRecord -> {
                setVideoTransform(engine, currentRecord)
                oliveVideoEngine?.getVideoEdgeEditor()?.oliveVideoEdgeEditorEnable = false
            }
            is OliveRecord -> setVideoPlayRange(engine, currentRecord)
            is WatermarkRecord -> setVideoWatermarkEffect(engine, currentRecord, "setVideoEffect")
        }
    }

    private fun setVideoPlayRange(engine: IOliveVideoEngine, currentRecord: OliveRecord?) {
        GLog.d(TAG, LogFlag.DF, "setVideoPlayRange currentRecord = $currentRecord")
        val microVideo = vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)?.oLivePhoto?.microVideo
        val startUs = currentRecord?.startTime?.takeIf { it != INVALID_TIME_MS }?.let { it * TIME_1_MS_IN_US }
            ?: microVideo?.videoStartUs ?: 0L
        val endUs = currentRecord?.endTime?.takeIf { it != INVALID_TIME_MS }?.let { it * TIME_1_MS_IN_US }
            ?: microVideo?.videoEndUs ?: Long.MAX_VALUE
        engine.setPlayRange(startUs, endUs)
    }

    private fun resetVideoAllEffect(oliveVideoEngine: IOliveVideoEngine?) {
        GLog.d(TAG, LogFlag.DF, "resetVideoAllEffect")
        oliveVideoEngine?.apply {
            resetWatermarkEffect(this, "resetVideoAllEffect", true)
            // 重置滤镜
            resetFilter(this)
            // 重置调节
            resetAdjust(this)
            // 重置旋转裁剪
            resetTransform(this)
            // 重置水印
            resetWatermarkEffect(this, "resetVideoAllEffect")

            // 重置实况照片
            setVideoPlayRange(this, null)
        }
    }

    /**
     * 设置滤镜
     * @param oliveVideoEngine IOliveVideoEngine
     * @param filterRecord FilterRecord
     */
    private fun setVideoFilterEffect(oliveVideoEngine: IOliveVideoEngine?, filterRecord: FilterRecord?) {
        if (filterRecord == null) {
            val filterEffects = OliveVideoEditFilterUtils.createOriginalImageFilterEffects()
            oliveVideoEngine?.getFilterEditor()?.setFilter(filterEffects)
            GLog.d(TAG, LogFlag.DF, "setVideoFilterEffect filterRecord is null")
            return
        }
        if (TextUtils.isEmpty(filterRecord.handle) || TextUtils.isEmpty(filterRecord.name)) {
            val asFilterRecord = filterRecord.asFilterRecord()
            val filterEffects = createEditFilterEffects(asFilterRecord)
            if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
                GLog.d(TAG, LogFlag.DF, "setVideoFilterEffect isInvalidFilter asFilterRecord = $asFilterRecord")
            } else {
                GLog.d(TAG, LogFlag.DF, "setVideoFilterEffect isInvalidFilter")
            }
            oliveVideoEngine?.getFilterEditor()?.setFilter(filterEffects)
            return
        }
        val filterEffects = createEditFilterEffects(filterRecord)
        if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
            GLog.d(TAG, LogFlag.DF, "setVideoFilterEffect currentRecord = $filterRecord")
        } else {
            GLog.d(TAG, LogFlag.DF, "setVideoFilterEffect currentRecord")
        }
        oliveVideoEngine?.getFilterEditor()?.setFilter(filterEffects)
    }

    /**
     * 设置调节
     * @param oliveVideoEngine IOliveVideoEngine
     * @param adjustRecord AdjustRecord
     */
    private fun setVideoAdjustEffect(oliveVideoEngine: IOliveVideoEngine?, adjustRecord: AdjustRecord?) {
        if (adjustRecord == null) {
            oliveVideoEngine?.getAdjustEditor()?.setAdjust(OliveVideoEditAdjustUtils.crateOriginalImageAdjustRecord())
            GLog.d(TAG, LogFlag.DL, "setVideoAdjustEffect record is null ")
            return
        }
        if (OliveVideoEditAdjustUtils.isisInvalidAdjustRecord(adjustRecord)) {
            val asAdjustRecord = adjustRecord.copy()
            val effects = OliveVideoEditAdjustUtils.adjustRecordToOliveEditAdjust(asAdjustRecord)
            oliveVideoEngine?.getAdjustEditor()?.setAdjust(effects)
            if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
                GLog.d(TAG, LogFlag.DL, "setVideoAdjustEffect isNoAdjust asAdjustRecord = $asAdjustRecord")
            } else {
                GLog.d(TAG, LogFlag.DL, "setVideoAdjustEffect isNoAdjust asAdjustRecord ")
            }
            return
        }
        val record = OliveVideoEditAdjustUtils.adjustRecordToOliveEditAdjust(adjustRecord)
        oliveVideoEngine?.getAdjustEditor()?.setAdjust(record)
        if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
            GLog.d(TAG, LogFlag.DF, "setVideoAdjustEffect currentRecord = $adjustRecord")
        } else {
            GLog.d(TAG, LogFlag.DF, "setVideoAdjustEffect currentRecord")
        }
    }

    private fun setVideoTransform(oliveVideoEngine: IOliveVideoEngine?, transformRecord: TransformRecord?) {
        val originVideoSize: Size = getOriginWithoutWatermarkVideoSize()
        if (transformRecord == null) {
            val record = OliveVideoEditTransformUtils.createTransformRecord(originTransformRecord, originVideoSize)
            oliveVideoEngine?.getTransformEditor()?.setTransform(record)
            if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
                GLog.d(TAG, LogFlag.DF, "setVideoTransform currentRecord is null originTransformRecord = $originTransformRecord")
            } else {
                GLog.d(TAG, LogFlag.DF, "setVideoTransform currentRecord is null")
            }
            return
        }
        if (OliveVideoEditTransformUtils.isInvalidTransformRecord(transformRecord)) {
            val asTransformRecord = transformRecord.asTransformRecord()
            val record = OliveVideoEditTransformUtils.createTransformRecord(asTransformRecord, originVideoSize)
            oliveVideoEngine?.getTransformEditor()?.setTransform(record)
            if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
                GLog.d(TAG, LogFlag.DF, "setVideoTransform isInvalidTransformRecord asTransformRecord = $asTransformRecord")
            } else {
                GLog.d(TAG, LogFlag.DF, "setVideoTransform isInvalidTransformRecord ")
            }
            return
        }
        val record = OliveVideoEditTransformUtils.createTransformRecord(transformRecord, originVideoSize)
        oliveVideoEngine?.getTransformEditor()?.setTransform(record)
        if (DEBUG_OPEN_OLIVE_VIDEO_EDIT_LOG) {
            GLog.d(TAG, LogFlag.DF, "setVideoTransform  transformRecord = $transformRecord")
        } else {
            GLog.d(TAG, LogFlag.DF, "setVideoTransform transformRecord ")
        }
    }

    /**
     * 设置水印
     *
     * @param oliveVideoEngine IOliveVideoEngine
     * @param watermarkRecord WatermarkRecord
     */
    private fun setVideoWatermarkEffect(
        oliveVideoEngine: IOliveVideoEngine?,
        watermarkRecord: WatermarkRecord?,
        invokeFrom: String,
        isOnlyRemoveWatermark: Boolean = false
    ) {
        if (oliveVideoEngine == null) {
            GLog.d(TAG, LogFlag.DF, "[setVideoWatermarkEffect] oliveVideoEngine is null invokeFrom:$invokeFrom")
            return
        }

        /*
         * 当视频段添加水印时，需要先从视频段，取一帧位于封面时间戳的无水印的图。
         * 所以，要在添加水印之前，需要先执行一次视频移除水印。
         * 【即使视频无水印，多调一次移除接口，也是不会异常的，所以这里无需考虑此种场景问题】
         */
        oliveVideoEngine.getWatermarkEditor()?.removeWatermark(getOriginVideoSize(), getOriginVideoDisplayRect(), invokeFrom)
        oliveVideoEngine.getWatermarkEditor()?.updateWithoutWatermarkVideoSize(oliveVideoEngine.getVideoSize())
        updateVideoDisplayRect(null)
        val actionFlag = UUID.randomUUID().toString()
        currentWatermarkActionFlag = actionFlag


        if (isOnlyRemoveWatermark) {
            GLog.d(TAG, LogFlag.DF, "[setVideoWatermarkEffect] isOnlyRemoveWatermark invokeFrom=$invokeFrom")
            return
        }

        if (watermarkRecord == null) {
            GLog.w(TAG, LogFlag.DL, "[setVideoWatermarkEffect] watermarkRecord is null, invokeFrom=$invokeFrom")
            return
        }

        if (watermarkRecord.isAddWatermarkRecord().not()) {
            GLog.w(TAG, LogFlag.DL, "[setVideoWatermarkEffect] watermarkRecord is not isAddWatermarkRecord, invokeFrom=$invokeFrom")
            scaleVideoContentIfNeed()
            return
        }

        // livephoto 视频目前只支持画框类水印，非画框类应该去掉画框水印
        val isFrameTypeWatermark = watermarkRecord.watermarkInfo?.aiWatermarkMasterParams?.watermarkMasterStyle?.let {
            watermarkRecord.watermarkInfo.isAiMasterWatermark() && it.isOnlyFrameWatermark()
        } ?: false

        if (isFrameTypeWatermark) {
            val recordHashCode = watermarkRecord.hashCode()
            updateOliveIsEditing(recordHashCode)
            if (watermarkConverter == null) {
                watermarkConverter = OliveVideoEditWatermarkConverter(app)
            }
            getVideoFrameAtCoverTime(oliveVideoEngine) { videoThumbnail ->
                val filePath = vmBus?.get<DataSource>(TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE)?.filePath
                // 如果上次编辑的是调节或虚化，会改变预览图颜色。此时添加水印，需要考虑水印虚化底色的场景，要同步刷新底色。
                val lastRecordHashCode = vmBus?.notifyOnce<List<OperatingRecord>>(
                    REPLY_TOPIC_OPERATING_FIND_ALL_OPERATING_RECORDS, ALL_OPERATING_RECORDS
                )?.firstOrNull()?.takeIf {
                    (it is AdjustRecord) || (it is FilterRecord)
                }?.hashCode()
                watermarkConverter?.createEditWatermarkEffects(videoThumbnail, watermarkRecord, filePath, lastRecordHashCode) { effects ->
                    if ((currentLoadingHashCode == null) || (currentLoadingHashCode != recordHashCode)) {
                        GLog.w(TAG, LogFlag.DL) {
                            "[setVideoWatermarkEffect] no need addWatermark, currentLoadingHashCode is $currentLoadingHashCode," +
                                " callback hashCode: ${watermarkRecord.hashCode()} invokeFrom=$invokeFrom"
                        }
                        return@createEditWatermarkEffects
                    }
                    // IOliveVideoEngine的接口需要统一在主线程调用
                    launch(Dispatchers.Main) {
                        if (effects == null) {
                            GLog.w(TAG, LogFlag.DL, "[setVideoWatermarkEffect] effects is null, invokeFrom=$invokeFrom")
                            if (recordHashCode == currentLoadingHashCode) {
                                updateOliveIsEditing(null)
                            }
                            return@launch
                        }

                        // 由于这个地方是异步的, 在添加水印时是不是已经进入下一次的remove操作，此时也不需要添加水印
                        if (currentWatermarkActionFlag != actionFlag) {
                            GLog.w(TAG, LogFlag.DL, "[setVideoWatermarkEffect] current action has been changed, skipping addWatermark.")
                            if (recordHashCode == currentLoadingHashCode) {
                                updateOliveIsEditing(null)
                            }
                            return@launch
                        }

                        oliveVideoEngine.getWatermarkEditor()?.addWatermark(effects, invokeFrom)
                        updateVideoDisplayRect(effects.videoDisplayRectF)
                        //在设置完成水印效果后根据视频是否有模糊边缘对视频进行缩放
                        scaleVideoContentIfNeed(effects.videoDisplayRectF)
                        if (recordHashCode == currentLoadingHashCode) {
                            updateOliveIsEditing(null)
                        }
                    }
                }
            }
        } else {
            //在设置完成水印效果后根据视频是否有模糊边缘对视频进行缩放
            scaleVideoContentIfNeed()
        }
    }

    private fun updateOliveIsEditing(hashCode: Int?) {
        GLog.d(TAG, LogFlag.DL, "[updateOliveIsEditing] oliveIsEditing: ${hashCode != null}")
        currentLoadingHashCode = hashCode
        oliveIsEditingPub?.publish(currentLoadingHashCode != null)
    }

    /**
     * 对视频显示内容进行缩放
     */
    private fun scaleVideoContentIfNeed(contentRect: RectF? = null) {
        val videoEdgeEditor = oliveVideoEngine?.getVideoEdgeEditor() ?: return
        if (videoEdgeEditor.scalePercent.compareTo(DEFAULT_DISPLAY_PERCENT) > 0) {
            videoEdgeEditor.scaleVideoContent(contentRect, videoEdgeEditor.scalePercent)
        }
    }

    /**
     * 获取视频帧封面位置图片
     */
    private fun getVideoFrameAtCoverTime(oliveVideoEngine: IOliveVideoEngine?, callback: (videoThumbnail: Pair<Long, Bitmap?>?) -> Unit) {
        launch(Dispatchers.Main) {
            val timeInUs = vmBus?.notifyOnce<OperatingRecord>(
                REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC,
                AvEffect.OliveEffect.name
            )?.let {
                (it.arguments[COVER_TIME] as? Long)?.times(TIME_1_SEC_IN_MS)
            }?.takeUnless { it < 0 } ?: let {
                vmBus?.get<OLivePhotoDataSource>(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)?.oLivePhoto?.coverTimeInUs
            }
            val maxLen = oliveVideoEngine?.getWithoutWatermarkVideoSize()?.maxLen()
            GLog.d(TAG, LogFlag.DL, "[getVideoFrameCoverImage] timeInUs: $timeInUs, maxLen: $maxLen")
            val frame = if ((timeInUs != null) && (maxLen != null)) oliveVideoEngine.grabThumbnailAsync(timeInUs, maxLen) else null
            callback.invoke(frame)
        }
    }

    private fun updateVideoDisplayRect(videoDisplayRectF: RectF?) {
        GLog.d(TAG, LogFlag.DL, "[updateVideoDisplayRect] videoDisplayRectF: $videoDisplayRectF")
        videoDisplayRectPub?.publish(videoDisplayRectF)
    }

    private fun resetFilter(oliveVideoEngine: IOliveVideoEngine) {
        oliveVideoEngine.getFilterEditor().setFilter(OliveVideoEditFilterUtils.createOriginalImageFilterEffects())
    }

    private fun resetAdjust(oliveVideoEngine: IOliveVideoEngine) {
        oliveVideoEngine.getAdjustEditor().setAdjust(OliveVideoEditAdjustUtils.crateOriginalImageAdjustRecord())
    }

    private fun resetWatermarkEffect(oliveVideoEngine: IOliveVideoEngine, invokeType: String, isOnlyRemoveWatermark: Boolean = false) {
        // 恢复成最初样式
        val originWatermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_ORIGIN_WATERMARK_INFO)
        val watermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT) ?: vmBus?.get<WatermarkInfo>(
            TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT
        )

        val phoneInfo = vmBus?.get<PhoneInfo>(TopicID.InputArguments.TOPIC_PHONE_INFO) ?: PhoneInfo()
        val record = if (watermarkInfo?.hasWatermark() == true) {
            WatermarkRecord.createAddWatermarkRecord(watermarkInfo, phoneInfo)
        } else {
            WatermarkRecord.createRemoveWatermarkRecord(originWatermarkInfo, phoneInfo, null)
        }
        setVideoWatermarkEffect(oliveVideoEngine, record, invokeType, isOnlyRemoveWatermark)
    }

    private fun resetTransform(oliveVideoEngine: IOliveVideoEngine) {
        GLog.d(TAG, LogFlag.DL, "resetTransform originTransformRecord = $originTransformRecord")
        val record = OliveVideoEditTransformUtils.createTransformRecord(originTransformRecord, getOriginWithoutWatermarkVideoSize())
        oliveVideoEngine.getTransformEditor()?.setTransform(record)
    }

    private fun getOriginVideoSize(): Size {
        return Size(oliveVideoSize?.width ?: 0, oliveVideoSize?.height ?: 0)
    }

    private fun getOriginWithoutWatermarkVideoSize(): Size {
        // 应该先取项目化的
        vmBus?.get<WatermarkInfo>(TOPIC_ORIGIN_WATERMARK_INFO)?.let { watermarkInfo ->
            return watermarkInfo.aiWatermarkFileExtendInfo?.videoDisplayRect?.takeIf { it.isEmpty.not() }
                ?.let { Size(it.width().toInt(), it.height().toInt()) }
                ?: getOriginVideoSize()
        }

        val watermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT)
        return watermarkInfo?.aiWatermarkFileExtendInfo?.videoDisplayRect?.takeIf { it.isEmpty.not() }
            ?.let { Size(it.width().toInt(), it.height().toInt()) }
            ?: getOriginVideoSize()
    }

    private fun getOriginVideoDisplayRect(): RectF {
        vmBus?.get<WatermarkInfo>(TOPIC_ORIGIN_WATERMARK_INFO)?.let { watermarkInfo ->
            return watermarkInfo.aiWatermarkFileExtendInfo?.videoDisplayRect ?: RectF(0f, 0f, 0f, 0f)
        }

        val watermarkInfo = vmBus?.get<WatermarkInfo>(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT)
        return watermarkInfo?.aiWatermarkFileExtendInfo?.videoDisplayRect ?: RectF(0f, 0f, 0f, 0f)
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unregister(TOPIC_OLIVE_SUPPORT_CHECK, checkSupportOliveRep)
            unregister(TOPIC_OLIVE_IS_VIDEO_CHANGED, isVideoChangedRep)
            unregister(TOPIC_SHOULD_TOGGLE_OLIVE_VIDEO_WHEN_LONG_PRESSED, onLongPressRep)
            unregister(REPLY_TOPIC_CHECK_PLAY_VIDEO_FPS_PROXY, checkOliveFpsAndSizeRep)
            unregisterT(TOPIC_PREVIEW_OLIVE_VIDEO_ENGINE, oliveVideoEnginePub)
            unregisterT(TOPIC_PREVIEW_OLIVE_VIDEO_TEXTURE, oliveVideoTexturePub)
            unregisterT(TOPIC_PREVIEW_INVALIDATE, invalidatePub)
            unsubscribe(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE, ::onOlivePhotoEditInfoChanged)
            unsubscribe(TOPIC_WATERMARK_RECORD_FOR_SUBPAGE, onWatermarkRecordForSubpageChangeObserver)
            unregisterT(TOPIC_WATERMARK_MASTER_VIDEO_DISPLAY_RECT, videoDisplayRectPub)
            unregisterT(TOPIC_OLIVE_IS_EDITING, oliveIsEditingPub)
        }

        /*释放olive视频相关资源*/
        releaseOliveVideoEngine()

        if (isSupportSaveOlive.not()) {
            return
        }

        vmBus?.apply {

            unsubscribe(TOPIC_OPERATING_RECORD, stepRecordObserver)
            unsubscribe(TOPIC_OPERATING_OPERATING_MEMO, onOperatingMemoObserver)
            unsubscribe(TOPIC_WATERMARK_RECORD_RECOVER_EFFECT, onWatermarkRecordRecoverEffectObserver)
            unsubscribe(TOPIC_IMAGE_PACK, imagePackObserver)
            unsubscribe(TOPIC_ORIGIN_WATERMARK_INFO, originWatermarkInfoObserver)
            unregister(TOPIC_ADJUST_AUTO, autoAdjustObserver)
            unregister(TOPIC_ORIGIN_TRANSFORM_RECORD, originTransformRecordObserver)
        }
        originTransformRecord = null
        watermarkConverter?.clear()
        watermarkConverter = null
    }

    private fun releaseOliveVideoEngine() {
        oliveVideoEngine?.getTransformEditor()?.setOnVideoSizeChangeListener(null)
        oliveVideoEngine?.getWatermarkEditor()?.setOnVideoSizeChangeListener(null)
        oliveVideoEngine?.destroy()
        oliveVideoEngine = null
        editingVM.submitOnGLIdle {
            GLog.d(TAG, LogFlag.DL, "[releaseOliveVideoEngine] reuse videoPreviewTexture")
            videoPreviewTexture?.reuse()
            videoPreviewTexture = null
        }
    }

    private fun notifyOriginTransform(needSetVide: Boolean) {
        if (vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)?.isOlivePhoto() == false) {
            return
        }
        initOriginTransformRecord()
        // AI构图发起的旋转裁切不需要重新设置视频
        if (needSetVide) {
            originTransformRecord?.let {
                setVideoTransform(oliveVideoEngine, it)
            }
        }
        lastCurrentStepRecord = originTransformRecord
    }

    /**
     * 延迟兜底回收Oes纹理。
     *
     * 由于框架限制，纹理最终发到渲染链需要经过多次的post，会导致这期间产生了多个纹理没有释放，这里是兜底策略
     */
    private fun delayDoubleReuseOesTex(texture: OesRawTexture?) {
        texture ?: return
        launch {
            delay(DELAY_REUSE_OES_TEXTURE_TIME_MS)
            editingVM.doOnGL {
                GLog.d(TAG, LogFlag.DL) { "delayDoubleReuseOesTex: $texture" }
                texture.reuse()
            }
        }
    }

    /**
     * olive edit检测的入参
     */
    internal data class OliveCheckArg(
        val strategyID: Int,
        val function: (isSupport: Boolean) -> Unit
    )

    /**
     * olive 实况和声音开关状态
     */
    internal data class OliveStatusBean(
        var oliveEnable: Boolean = true,
        var oliveSoundEnable: Boolean = true
    )

    /**
     * olive校验数据类
     */
    internal data class OliveCheckData(
        /**
         * 原始帧率
         */
        var originalFps: Rational,

        /**
         * 分辨率
         */
        var videoSize: Size
    )


    companion object {
        const val TAG = "OliveVideoEditVM"
        const val LOSE_OLIVE_HDR_DIALOG_TYPE = 0
        const val LOSE_OLIVE_DIALOG_TYPE = 1
        private val OLIVE_PREVIEW_STRATEGY_WHITE_LIST = buildList {
            add(R.id.strategy_editing_page)
            add(R.id.strategy_transform)
            add(R.id.strategy_adjustment)
            if (ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_PHOTO_EDITOR_WATERMARK)) {
                add(R.id.strategy_watermark_master)
                add(R.id.strategy_privacy_watermark_master)
                add(R.id.strategy_watermark_personalized_edit)
            }

            if (DEBUG_IS_LIVE_PHOTO_SUPPORT_FILTER) {
                add(R.id.strategy_filter)
            }
        }

        /**
         * 延迟回收纹理的时间，单位ms
         */
        private const val DELAY_REUSE_OES_TEXTURE_TIME_MS = 1500L

        /**
         * 4K 视频分辨率阈值
         */
        private const val VIDEO_SIZE_THRESHOLD_FOR_4K = 3840

        /**
         * 视频默认帧率
         */
        private const val VIDEO_DEFAULT_FPS = 30
    }
}
