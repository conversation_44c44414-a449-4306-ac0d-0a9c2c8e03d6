/*******************************************************************************
 *  Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 *  All rights reserved.
 *   File: - ISlowMotionPipeline.kt
 *  Description:
 *   Version: 1.0
 *  Date: 2025/06/12
 *  Author: <EMAIL>
 *  TAG: OPLUS_ARCH_EXTENDS
 *  ------------------------------- Revision History: ----------------------------
 *  <author>                        <date>       <version>    <desc>
 *  ------------------------------------------------------------------------------
 *  huca<PERSON><PERSON>@Apps.Gallery        2025/06/12      1.0        OPLUS_ARCH_EXTENDS
 ********************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.olive.slowmotion

import kotlinx.coroutines.flow.Flow

interface ISlowMotionPipeline {
    /**
     * 执行初始化Session
     */
    fun initSession()

    /**
     * 开始执行慢动作
     */
    suspend fun start(record: SlowMotionRecord): Flow<T>

    /**
     * 执行关闭Session
     */
    fun closeSession()
}