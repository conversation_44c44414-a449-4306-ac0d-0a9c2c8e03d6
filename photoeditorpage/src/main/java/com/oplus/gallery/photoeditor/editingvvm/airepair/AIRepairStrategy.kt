/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIRepairStrategy.kt
 ** Description: AI修复页的组合策略
 ** Version: 1.0
 ** Date : 2024/4/25
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:AIRepairStrategy
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2024/04/25    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair

import android.content.res.Resources
import android.graphics.Rect
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.Config.Animation.EFFECT_PHOTO_TOUCHABLE_IN_AI_REPAIR
import com.oplus.gallery.photoeditor.editingvvm.BaseAIStrategy
import com.oplus.gallery.photoeditor.editingvvm.ICustomPreviewMargin
import com.oplus.gallery.photoeditor.editingvvm.notification.NotificationSection
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingSection
import com.oplus.gallery.photoeditor.editingvvm.pagecontainer.PageContainerSection
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewSection
import com.oplus.gallery.photoeditor.editingvvm.transition.TransitionSection
import com.oplus.gallery.basebiz.R as BaseR

/***
 * AI修复页的组合策略
 */
abstract class AIRepairStrategy : BaseAIStrategy() {

    override val sectionSpecs: List<SectionSpec> = listOf(
        PageContainerSection.SPEC,
        NotificationSection.SPEC,
        OperatingSection.SPEC,
        PreviewSection.SPEC,
        TransitionSection.SPEC,
        AIRepairSection.SPEC
    )

    override val isComparable: Boolean = true

    override val showDialogWhenCancel: Boolean = false

    override val isPhotoTouchable: Boolean = EFFECT_PHOTO_TOUCHABLE_IN_AI_REPAIR

    override val isPhotoScrollable: Boolean = EFFECT_PHOTO_TOUCHABLE_IN_AI_REPAIR

    override val isCompareBelowPreview: Boolean = true

    override val isUndoable: Boolean = false

    override val customPreviewMargin: ICustomPreviewMargin?
        get() = innerCustomPreviewMargin

    private val innerCustomPreviewMargin: ICustomPreviewMargin by lazy {
        object : ICustomPreviewMargin {
            override fun calculateMargins(resources: Resources, appUiConfig: AppUiResponder.AppUiConfig): Rect {
                var left = 0
                var right = 0
                var top = 0
                var bottom = 0
                val isLargeScreen = (appUiConfig.screenMode.current == AppUiResponder.ScreenMode.LARGE)
                val pixelValue = fun(dimenID: Int): Int {
                    return resources.getDimensionPixelSize(dimenID)
                }
                if (isLargeScreen) { // 折叠大屏
                    top = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_large_margin_top)
                    bottom = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_large_margin_bottom)
                    right = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_large_margin_end)
                } else { // 其他屏
                    val isLandScape = EditorUIConfig.isEditorLandscape(appUiConfig)
                    if (isLandScape) { // 横屏
                        top = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_landscape_margin_top)
                        bottom = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_landscape_margin_bottom)
                        right = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_landscape_margin_end)
                    } else { // 竖屏
                        if ((strategyID == R.id.strategy_ai_deblur)
                            || (strategyID == R.id.strategy_specific_ai_deblur)
                        ) {
                            left = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_margin_start)
                            right = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_margin_end)
                        }
                        top = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_margin_top)
                        bottom = pixelValue(R.dimen.picture3d_editor_image_quality_enhance_preview_margin_bottom)
                    }
                }
                return Rect(left, top, right, bottom)
            }
        }
    }
}

class AIDeBlurStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_ai_deblur

    override val strategyNameRes: Int = BaseR.string.title_ai_repair_blur
}

/***
 * 直接进入AI修复页去反光的组合策略，其pageLevel = 0，作为独立的页面存在
 */
class AIDeBlurSpecificStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_specific_ai_deblur

    override val strategyNameRes: Int = BaseR.string.title_ai_repair_blur

    override val pageLevel: Int = 0
}

class AIDeReflectionStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_ai_dereflection

    override val strategyNameRes: Int = BaseR.string.title_ai_repair_deReflection
}

class AIDeReflectionSpecificStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_specific_ai_dereflection

    override val strategyNameRes: Int = BaseR.string.title_ai_repair_deReflection

    override val pageLevel: Int = 0
}

/***
 * AI 去炫光
 */
class AIDeGlareStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_ai_deglare

    override val strategyNameRes: Int = BaseR.string.title_ai_deglare
}

/***
 * 直接进入AI编辑图片页去炫光的组合策略，其pageLevel = 0，作为独立的页面存在
 */
class AIDeGlareSpecificStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_specific_ai_deglare

    override val strategyNameRes: Int = BaseR.string.title_ai_deglare

    override val pageLevel: Int = 0
}

class AIDeFogStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_ai_defog

    override val strategyNameRes: Int = BaseR.string.title_ai_deFogMongo
}

class AIDeFogMongoSpecificStrategy : AIRepairStrategy() {

    override val strategyID: Int = R.id.strategy_specific_ai_defog

    override val strategyNameRes: Int = BaseR.string.title_ai_deFogMongo

    override val pageLevel: Int = 0
}