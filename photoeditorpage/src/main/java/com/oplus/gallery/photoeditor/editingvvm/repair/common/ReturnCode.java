/***********************************************************
 * * Copyright (C), 2010-2020, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  - ${FILE_NAME}
 * * Description: build this module.
 * * Version: 1.0
 * * Date : 2020/02/27
 * * Author: GuangJin.Ye@Apps.Gallery3D
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>	<data> 	  <version >	   <desc>
 * *  GuangJin.Ye@Apps.Gallery3D       2020/02/27    1.0     build this module
 ****************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.repair.common;

public class ReturnCode {

    public static final int SUCCESS = 0;
    public static final int ERROR = 1 << 10;//1024

    public interface Error {
        int CONFIG_IS_NULL = ERROR + 1;
        int PARAM_ERROR = ERROR + 2;
        int IMAGE_DATA_IS_NULL = ERROR + 3;
        int ENCRYPT_FAILED = ERROR + 4;
        int STRING_EMPTY = ERROR + 5;
        int ID_IS_NULL = ERROR + 6;
        int BITMAP_IS_NULL = ERROR + 7;
        int STDID_IS_NULL = ERROR + 8;
        int EXPIRED_KEY_PROCESSED = ERROR + 9;
        int RESPONSE_IS_NULL = ERROR + 10;
        int REQUEST_EXCEPTION = ERROR + 11;
        /**
         * 网络能力不存在
         */
        int DOWNLOAD_ABILITY_NOT_EXIST = ERROR + 12;
    }

    public interface Http {
        int SUCCESS = 200;
        int EXPIRED_KEY = 399;
        int TIMESTAMP_OUT_OF_DATE = 443;
        int PICTURE_TOO_LARGE = 444;
        int LLEGAL_TOKEN = 445;
        int UNSUPPORT_FORMAT = 446;
        int SIGNATURE_ERROR = 447;
        int PICTURE_HANDLER_REQUEST_ERROR = 448;
        int UPLOAD_PICTURE_TIMEOUT = 449;
        int PARAMETER_ERROR = 450;
        int PICTURE_HANDLER_SERVER_ERROR = 502;
        int PICTURE_HANDLER_SERVER_TIMEOUT = 504;
        int REQUEST_LIMITED = 555;
        int SERVER_INNER_ERROR = 556;
        int PICTURE_SERVER_INNER_ERROR = 557;
    }
}
