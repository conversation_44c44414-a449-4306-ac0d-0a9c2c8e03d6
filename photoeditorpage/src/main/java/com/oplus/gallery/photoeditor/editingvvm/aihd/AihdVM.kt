/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AihdVM
 ** Description: AihdVM.
 ** Version: 1.0
 ** Date : 2024/7/29
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** chenshimin  2024/7/29      1.0        created
 ***************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.aihd

import android.graphics.Bitmap
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.runOnUIThreadImmediate
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.misc.AvResolution
import com.oplus.gallery.framework.abilities.editing.sink.DefaultBitmapSink
import com.oplus.gallery.framework.abilities.editing.sink.DefaultOutputSink
import com.oplus.gallery.framework.abilities.editing.sink.IAvSink
import com.oplus.gallery.framework.abilities.editing.sink.emptySink
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.common.Config.Loader.OUTPUT_PHOTO_MAX_LENGTH
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.OperatingAction.TOPIC_OPERATING_ACTION_BACK_KEY
import com.oplus.gallery.photoeditor.editingvvm.UnitReplier
import com.oplus.gallery.photoeditor.editingvvm.aihd.track.AihdTrackConstant
import com.oplus.gallery.photoeditor.editingvvm.aihd.track.AihdTrackHelper
import com.oplus.gallery.photoeditor.editingvvm.aihd.utils.AihdReadUtil
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.BackKeyListener
import com.oplus.gallery.photoeditor.editingvvm.operating.ExitPageListener
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecord
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingType
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingViewState
import com.oplus.gallery.photoeditor.editingvvm.subscribeOnce
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeR
import com.oplus.gallery.photoeditor.util.setEnable
import com.oplus.gallery.photoeditor.util.setVisible

internal class AihdVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    private var currentStatus: AihdStatus = AihdStatus.STATUS_IDLE
    private var currentEffectUsage: IAvEffectUsage? = null
    private var effectBitmapPub: PublisherChannel<Bitmap>? = null
    private var effectBmpSink: DefaultBitmapSink? = null
    private var uiBeanPub: PublisherChannel<AihdUiBean>? = null
    private var statusChangePub: PublisherChannel<AihdStatus>? = null
    private var aihdRecord: OperatingRecord? = null
    private var isExiting: Boolean = false
    private var requestParams: AihdParamsBean? = null
    private val confirmRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            GLog.d(TAG, LogFlag.DL) { "confirmRep" }
            AihdTrackHelper.trackAction(AihdTrackConstant.Value.RESULT_SAVE, 0, 0, 0, requestParams)
            arg.onExitPage(isConfirm = true)
        }
    }

    /**
     * 是否内销
     */
    val isDomestic: Boolean by lazy {
        ConfigAbilityWrapper.getBoolean(ConfigID.Common.SystemInfo.IS_REGION_CN)
    }

    /**
     * 通知预览区域禁止操作
     */
    private var preViewTouchablePub: NotifierChannel<Unit>? = null

    /**
     * 通知预览区域禁止操作
     */
    private var preViewCOUIScrollablePub: NotifierChannel<Unit>? = null

    /**
     * 退出超清功能返回上一级编辑
     */
    private val exitRep = object : UnitReplier<ExitPageListener>() {
        override fun onSingleReply(arg: ExitPageListener) {
            if (getLastStatus() == AihdStatus.STATUS_IDLE) {
                GLog.d(TAG, LogFlag.DL) { "[exitRep] Initialization not yet completed" }
                return
            }
            GLog.d(TAG, LogFlag.DL) { "[exitRep]" }
            isExiting = true
            if (getLastStatus() != AihdStatus.STATUS_SUCCESS) {
                uiBeanPub?.publish(AihdUiBean(status = AihdStatus.STATUS_CANCEL))
                onStatusChange(AihdStatus.STATUS_CANCEL)
                cancelOptimize()
            }
            AihdTrackHelper.trackAction(AihdTrackConstant.Value.RESULT_CANCEL, 0, 0, 0, requestParams)
            arg.onExitPage(false)
        }
    }

    /**
     * 返回键点击监听
     */
    private val backPressedRep = object : UnitReplier<BackKeyListener>() {
        override fun onSingleReply(arg: BackKeyListener) {
            if (getLastStatus() == AihdStatus.STATUS_IDLE) {
                GLog.d(TAG, LogFlag.DL) { "[backPressedRep] Initialization not yet completed" }
                return
            }
            arg.onBack()
        }
    }

    /**
     * 超清功能操作命令
     */
    private val actionRep = object : UnitReplier<AihdVmBean>() {
        override fun onSingleReply(arg: AihdVmBean) {
            when (arg.action) {
                AihdVmAction.EXECUTE -> checkAndExecute()

                AihdVmAction.CANCEL -> cancelOptimize()

                AihdVmAction.RESET -> onStatusChange(AihdStatus.STATUS_IDLE)
            }
        }
    }

    private fun cancelOptimize() {
        GLog.d(TAG, LogFlag.DL) { "cancelOptimize" }
        aihdRecord?.let { cancelEffect(it, false) }
    }

    private val resultObserver = object : IAvEffectUsage.ValueObserver {
        override fun onValueChanged(key: String, value: Any?) {
            val resultMap = value as? Map<String, Any?> ?: run {
                GLog.e(TAG, "[resultObserver] value is invalid:$value")
                return
            }
            val resultCode = resultMap[AIHD_RESULT_CODE] as Int
            GLog.d(TAG, LogFlag.DL) { "result Code :$resultCode" }
            if (resultCode == RESULT_SUCCESS) {
                aihdRecord?.let {
                    emitRecord(it, OperatingType.ONLY_RECORD)
                }
                onStatusChange(AihdStatus.STATUS_SUCCESS)
                uiBeanPub?.publish(AihdUiBean(status = AihdStatus.STATUS_SUCCESS, requestParams = requestParams))
            } else {
                if (isExiting.not()) {
                    if (resultCode != ERROR_CODE_CANCEL) {
                        aihdRecord?.let {
                            undoRecord(it, render = true)
                        }
                    }
                    val refuseType = resultMap[AIHD_REFUSE_TYPE] as Int
                    val msg = resultMap[AIHD_RESULT_MSG] as String
                    uiBeanPub?.publish(
                        AihdUiBean(
                            status = AihdStatus.STATUS_FAIL,
                            errorCode = resultCode,
                            refuseType = refuseType,
                            message = msg,
                            requestParams = requestParams
                        )
                    )
                    onStatusChange(AihdStatus.STATUS_FAIL)
                }
            }
        }
    }
    private val progressObserver = IAvEffectUsage.ValueObserver { _, value ->
        val progress = value as Int
        GLog.d(TAG, LogFlag.DL) { "[progressObserver] progress:$progress" }
        uiBeanPub?.publish(AihdUiBean(status = AihdStatus.STATUS_UPDATE_PROCESS, progress = progress))
    }

    // 监听管线变化
    private val onEffectUsageChanged: TObserver<IAvEffectUsage> = { effectUsage ->
        GLog.d(TAG, LogFlag.DL) { "[onEffectUsageChanged] effectUsage is $effectUsage" }

        if (effectUsage.effect == AvEffect.AihdEffect) {
            removeObserver()
            currentEffectUsage = effectUsage
            effectUsage.addValueObserver(AIHD_RESPONSE_RESULT, resultObserver)
            effectUsage.addValueObserver(AIHD_PROGRESS, progressObserver)
        } else {
            removeObserver()
        }
    }

    init {
        vmBus?.apply {
            statusChangePub = register(TopicID.Aihd.TOPIC_AIHD_STATUS, AihdStatus.STATUS_IDLE)
            register(TopicID.Aihd.REPLY_TOPIC_AIHD_VM_ACTION, actionRep)
            uiBeanPub = register(TopicID.Aihd.TOPIC_AIHD_UI_BEAN)
            effectBitmapPub = register(TopicID.Aihd.REPLY_TOPIC_EFFECT_BITMAP)
            preViewTouchablePub = subscribeR(TopicID.Preview.TOPIC_PREVIEW_IS_TOUCHABLE)
            preViewCOUIScrollablePub = subscribeR(TopicID.Preview.TOPIC_PREVIEW_IS_SCROLLABLE)
            subscribeT(TopicID.Operating.TOPIC_OPERATING_EFFECT_USAGE, onEffectUsageChanged)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            unregisterT(TopicID.Aihd.TOPIC_AIHD_STATUS, statusChangePub)
            unregisterT(TopicID.Aihd.TOPIC_AIHD_UI_BEAN, uiBeanPub)
            unregisterT(TopicID.Aihd.REPLY_TOPIC_EFFECT_BITMAP, uiBeanPub)
            unregister(TopicID.Aihd.REPLY_TOPIC_AIHD_VM_ACTION, actionRep)
            unsubscribeR(TopicID.Preview.TOPIC_PREVIEW_IS_TOUCHABLE, preViewTouchablePub)
            unsubscribeR(TopicID.Preview.TOPIC_PREVIEW_IS_SCROLLABLE, preViewCOUIScrollablePub)
            unsubscribe(TopicID.Operating.TOPIC_OPERATING_EFFECT_USAGE, onEffectUsageChanged)
        }
    }

    private fun removeObserver() {
        currentEffectUsage?.removeValueObserver(AIHD_RESPONSE_RESULT, resultObserver)
        currentEffectUsage?.removeValueObserver(AIHD_PROGRESS, progressObserver)
        currentEffectUsage = null
    }

    private fun checkAndExecute() {
        isExiting = false
        downloadPreviewBitmap { previewBitmap ->
            if (previewBitmap == null) {
                uiBeanPub?.publish(
                    AihdUiBean(
                        status = AihdStatus.STATUS_FAIL,
                        errorCode = AihdResultCode.ERROR_CODE_OTHER,
                        message = "previewBitmap is null",
                        requestParams = requestParams
                    )
                )
                onStatusChange(AihdStatus.STATUS_FAIL)
                GLog.e(TAG, LogFlag.DL) { "[checkAndExecute] error, can't not previewBitmap" }
                return@downloadPreviewBitmap
            }
            execute()
        }
    }

    private fun execute() {
        GLog.d(TAG, LogFlag.DL) { "[execute]" }
        val startTime = System.currentTimeMillis()
        effectBmpSink = DefaultBitmapSink(
            Config.Loader.PREVIEW_PHOTO_MAX_LENGTH,
            Config.Loader.PREVIEW_PHOTO_MAX_LENGTH,
            onBitmapReceive = { bitmap, _, _, _ ->
                GLog.d(TAG, LogFlag.DL) {
                    "[execute], effectBmpSink bitmap wh=${bitmap.width},${bitmap.height}, cost time=${GLog.getTime(startTime)}"
                }
                startOptimize(bitmap)
                effectBmpSink?.also { editingVM.sessionProxy.removeSink(it.sinkType, it) }
            }).also {
            editingVM.sessionProxy.addAsOnceFrameSink(IAvSink.AvSinkType.BITMAP, sink = it)
        }
    }

    private fun startOptimize(bitmap: Bitmap) {
        effectBitmapPub?.publish(bitmap)
        runOnUIThreadImmediate {
            // 开始入栈
            startEmitRecord(bitmap)
            uiBeanPub?.publish(AihdUiBean(status = AihdStatus.STATUS_START))
            onStatusChange(AihdStatus.STATUS_START)
        }
    }

    private fun startEmitRecord(bitmap: Bitmap) {
        val mediaItem: MediaItem? = vmBus?.get<MediaItem>(TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM)
        mediaItem?.apply {
            AihdReadUtil(app, isDomestic).apply {
                GLog.d(TAG, "[startEmitRecord] ONLY_EFFECT")
                val params = createAihdParams(editingVM.sessionProxy, bitmap)
                val record = AihdRecord(params)
                emitRecord(record, OperatingType.ONLY_EFFECT)
                requestParams = params
                aihdRecord = record
            }
        }
    }

    private fun onStatusChange(status: AihdStatus) {
        currentStatus = status
        statusChangePub?.publish(status)
        // 更新菜单栏的的可操作性。（若正在处理， 则禁用菜单中的’完成‘按钮）
        updatePreviewAreaOperateEnable(status.isProcessing.not())
        updateTitleBarEnableState(status)
    }

    private fun getLastStatus(): AihdStatus? {
        return vmBus?.get<AihdStatus>(TopicID.Aihd.TOPIC_AIHD_STATUS)
    }

    /**
     * 更新预览区域是否可以操作
     *  用于：禁用图片预览区域的操作
     *  @param enable
     */
    private fun updatePreviewAreaOperateEnable(enable: Boolean) {
        preViewTouchablePub?.notify(enable)
        preViewCOUIScrollablePub?.notify(enable)
    }

    /**
     * 更新标题栏状态
     * @param status 标题栏是否可操作
     */
    private fun updateTitleBarEnableState(status: AihdStatus) {
        updateTitleBarEnableStateInner(status)
    }

    /**
     * 需要要从编辑管线取图的编辑功能，用这个函数等待管线供图
     * 这里还不是最终方案， 所以没有跟AIRepairVM提取公用函数到父类
     */
    private fun downloadPreviewBitmap(block: (Bitmap?) -> Unit) {
        val start = System.currentTimeMillis()
        vmBus?.subscribeOnce<Boolean>(TopicID.Preview.TOPIC_IS_PREVIEW_LOADED) {
            var requestCode = -1
            var outputSink = emptySink()
            outputSink = DefaultOutputSink(
                AvResolution(OUTPUT_PHOTO_MAX_LENGTH, OUTPUT_PHOTO_MAX_LENGTH)
            ) { bitmap, _, responseCode ->
                if (requestCode == responseCode) {
                    GLog.d(
                        TAG,
                        LogFlag.DL
                    ) { "[downloadPreviewBitmap] cost time ${GLog.getTime(start)} ms, bitmap:$bitmap" }
                    block.invoke(bitmap)
                    // 一定removeSink，否则内存泄露
                    editingVM.sessionProxy.removeSink(IAvSink.AvSinkType.OUTPUT, outputSink)
                }
            }
            requestCode = editingVM.sessionProxy.addAsOnceFrameSink(IAvSink.AvSinkType.OUTPUT, outputSink) ?: 0
        }
    }

    override fun onResume() {
        super.onResume()
        vmBus?.apply {
            register(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            register(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL, exitRep)
            register(TOPIC_OPERATING_ACTION_BACK_KEY, backPressedRep)
        }
    }

    override fun onPause() {
        super.onPause()
        vmBus?.apply {
            unregister(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CONFIRM, confirmRep)
            unregister(TopicID.OperatingAction.TOPIC_OPERATING_ACTION_CANCEL, exitRep)
            unregister(TOPIC_OPERATING_ACTION_BACK_KEY, backPressedRep)
        }
    }

    private fun updateTitleBarEnableStateInner(status: AihdStatus) {
        GLog.d(TAG, LogFlag.DL) { "[updateTitleBarEnableStateInner] status:$status" }
        val newViewState = vmBus?.get<OperatingViewState>(TopicID.Operating.TOPIC_OPERATING_VIEW_STATE)?.run {
            val isSuccess = status == AihdStatus.STATUS_SUCCESS
            val isIdle = status == AihdStatus.STATUS_IDLE
            copy(
                doneBtnFlags = doneBtnFlags.setEnable(status.isProcessingAndPrepare.not(), true),
                cancelFlags = cancelFlags.setEnable(isIdle.not(), true),
                compareBtnFlags = compareBtnFlags.setVisible(isSuccess, false)
            )
        } ?: let {
            GLog.e(EditingSubVM.TAG, "[updateTitleBarEnableState] can't update because can't find OperatingViewState")
            return
        }
        vmBus.notifyOnce(TopicID.Operating.TOPIC_OPERATING_VIEW_STATE, newViewState)
    }

    companion object {
        const val TAG = "AihdVM"
        const val AIHD_RESULT_CODE = "aihd_result_code"

        // 拒识码
        const val AIHD_REFUSE_TYPE = "aihd_refuse_type"

        // AI 超清处理结果信息
        const val AIHD_RESULT_MSG = "aihd_result_msg"

        // AI 超清处理结果
        const val AIHD_RESPONSE_RESULT = "aihd_response_result"

        // AI 超清处理进度
        const val AIHD_PROGRESS = "aihd_progress"

        // AI 超清处理成功
        const val RESULT_SUCCESS = 0

        /**没有错误*/
        const val ERROR_CODE_NORMAL = 0

        /**网络异常*/
        const val ERROR_CODE_NETWORK = 1

        /**敏感内容拒识*/
        const val ERROR_CODE_SENSITIVE = 2

        /**服务器繁忙*/
        const val ERROR_CODE_SERVER = 3

        /**其他异常*/
        const val ERROR_CODE_OTHER = 4

        /**取消*/
        const val ERROR_CODE_CANCEL = 9

        /**无网络*/
        const val ERROR_CODE_NO_NETWORK = 5

        /**超时*/
        const val ERROR_CODE_TIMEOUT = 6

        /**排队过长*/
        const val ERROR_CODE_BUSY = 7
    }
}