/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : AnalyzingAnimation
 ** Description : coe的动画
 ** Version     : 1.0
 ** Date        : 2025/4/9
 ** Author      : gary@Apps.Gallery3D
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** 80411420        2025/4/9    1.0    build this module
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.animations

import android.graphics.Bitmap
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.COEViewWrapper
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.bean.COEParams
import com.oplus.gallery.photoeditor.editingvvm.ailighting.ui.coe.bean.COEResult
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.vfxsdk.common.Animator

/**
 * 分析中动效部分
 * @property bitmap 图片内容
 * @property hdrParams hdr参数集,无hdr传null即可
 * @property onAnimateStart 动画开始回调
 */
class AnalyzingAnimation(
    private val bitmap: Bitmap,
    private val hdrParams: HdrImageDrawable? = null,
    private val onAnimateStart: () -> Unit = {},
) : AbsCOEAnimation(ANIMATION_NAME) {

    /**
     * 是否Hdr
     */
    private var hdr = false

    override fun onStart() {
        super.onStart()
        onAnimateStart()
    }

    override fun collectParamsForCalculate(params: COEParams) {
        params.paramsBitmapSize[0] = bitmap.width.toFloat()
        params.paramsBitmapSize[1] = bitmap.height.toFloat()
    }

    /**
     * 更新内部hdr信息
     * @param coeViewWrapper coe动效的包装类
     * @param result coe图片位置参数等计算结果
     * @param enableHDR 是否启用hdr
     */
    override fun updateHdr(coeViewWrapper: COEViewWrapper, result: COEResult, enableHDR: Boolean) {
        hdr = enableHDR && hdrParams != null
        hdrParams?.also {
            updateHDRParams(coeViewWrapper, result, HDR_PARAMS_KEY_SUFFIX, it)
        }
        enableHDR(coeViewWrapper, result, hdr)
    }

    override fun onTrigger(coeViewWrapper: COEViewWrapper, result: COEResult) {
        updatePosition(coeViewWrapper, result)
        coeViewWrapper.updatePicture(TEXTURE_NAME, coeViewWrapper.createCOETexture(bitmap, hdr))
        coeViewWrapper.coeView.getRenderer()?.apply {
            setAnimMode(name, Animator.AnimMode.ONCE)
            playAnim(name)
        }
    }

    companion object {
        /**
         * 分析中 动效的key
         * 动效名称,动效对应材质,动效hdr参数后缀
         */
        private const val ANIMATION_NAME = "loading"

        /**
         * key值 图片材质参数
         */
        private const val TEXTURE_NAME = "uPicTex1"

        /**
         * hdr参数key值 后缀
         */
        private const val HDR_PARAMS_KEY_SUFFIX = "1"
    }
}