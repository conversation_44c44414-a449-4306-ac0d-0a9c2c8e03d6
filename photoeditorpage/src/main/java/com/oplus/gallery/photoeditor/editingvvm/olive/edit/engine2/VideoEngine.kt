/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - VideoEngine.kt
 ** Description: 视频引擎，处理timeline初始化，提供编辑，信息获取，播放功能
 ** Version: 1.0
 ** Date: 2025/06/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>        2025/06/19  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2

import android.content.Context
import android.graphics.ColorSpace
import android.util.Size
import com.meicam.sdk.NvsAVFileInfo
import com.meicam.sdk.NvsAudioResolution
import com.meicam.sdk.NvsRational
import com.meicam.sdk.NvsStreamingContext
import com.meicam.sdk.NvsStreamingContext.STREAMING_CONTEXT_FLAG_COMPACT_MEMORY_MODE
import com.meicam.sdk.NvsStreamingContext.STREAMING_CONTEXT_FLAG_ENABLE_HDR_DISPLAY_WHEN_SUPPORTED
import com.meicam.sdk.NvsStreamingContext.STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT
import com.meicam.sdk.NvsTimeline
import com.meicam.sdk.NvsVideoClip
import com.meicam.sdk.NvsVideoResolution
import com.meicam.sdk.NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
import com.meicam.sdk.NvsVideoResolution.VIDEO_RESOLUTION_BIT_DEPTH_AUTO
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_BT2020
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_PRIMARIES_DISPLAY_P3
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_HLG
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_SDR_VIDEO
import com.meicam.sdk.NvsVideoStreamInfo.COLOR_TRANSFER_ST2084
import com.meicam.sdk.NvsVideoStreamInfo.VIDEO_ROTATION_270
import com.meicam.sdk.NvsVideoStreamInfo.VIDEO_ROTATION_90
import com.meicam.sdk.NvsVideoTrack
import com.oplus.gallery.abilities.transform.channel.livephoto.OliveHdrVideoFx
import com.oplus.gallery.abilities.transform.channel.livephoto.TransformConfig
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.hdrtransform.HdrTransformFactory
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_HLG
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.BT2020_PQ
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.DISPLAY_P3
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.SRGB
import com.oplus.gallery.foundation.util.ext.ColorSpaceExt.isHdrGamma
import com.oplus.gallery.foundation.util.ext.getOrLog
import com.oplus.gallery.foundation.util.math.MathUtil
import com.oplus.gallery.foundation.util.version.ApiLevelUtil
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_EDIT_VIDEO_HDR_BRIGHTEN
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.impl.EditImpl
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.impl.InfoImpl
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2.impl.PlayImpl
import com.oplus.gallery.standard_lib.codec.retriever.MeicamContextAliveCounter

/**
 * 视频引擎
 * @param context 上下文
 */
class VideoEngine(private val context: Context) : IVideoEngine {

    /**
     * 在NvsStreamingContext关闭释放资源后，需要对nvsContext变量进行置null处理
     */
    private val nvsContext: NvsStreamingContext? by lazy {
        MeicamContextAliveCounter.requestContext(context, STREAMING_CONTEXT_FLAG, TAG)
    }

    /**
     * 是否视频编辑支持 hdr 提亮
     */
    private val isSupportEditVideoHdrBrighten: Boolean by lazy {
        ApiLevelUtil.isAtLeastAndroidU() && ConfigAbilityWrapper.getBoolean(FEATURE_IS_SUPPORT_EDIT_VIDEO_HDR_BRIGHTEN)
    }

    /**
     * 美摄 timeline 对象
     */
    var nvsTimeline: NvsTimeline? = null

    /**
     * 编辑对象
     */
    private var editImpl: IEdit? = null

    /**
     * 信息对象
     */
    private var infoImpl: IInfo? = null

    /**
     * 播放对象
     */
    private var playImpl: IPlay? = null

    override fun createTimeline(clip: Clip, videoTransformConfig: VideoTransformConfig) {
        val safeNvsContext = nvsContext.getOrLog("createTimeline, nvsContext") ?: return
        val referenceClip = getReferenceClip(clip)
        val (videoInfo, timelineInfo) = referenceClip.path?.let { getVideoAndNvsInfo(it, videoTransformConfig) }
            .getOrLog("createTimeline, info") ?: return

        val safeTimeline = buildClip(safeNvsContext, clip, timelineInfo).getOrLog("createTimeline, timeline") ?: return
        nvsTimeline = safeTimeline
        configVideoClipIfNeed(safeTimeline.getVideoTrackByIndex(0).getClipByIndex(0), videoInfo.displayColorSpace, videoTransformConfig)

        playImpl = PlayImpl()
        infoImpl = InfoImpl()
        editImpl = EditImpl(safeNvsContext, safeTimeline)
    }

    /**
     * 获取参考的 clip
     * 预览虽然由多个视频挂载多个 clip 实现，但是由于预览的一致性，这些视频的分辨率都是一致的，不需要把每个 clip 的信息都解析出来，只找一个做为参考即可
     * 两种情况: clip里只有一个视频；clip里有多个视频，取第一个；
     */
    private fun getReferenceClip(clip: Clip): Clip {
        return clip.childClips?.get(0) ?: clip
    }

    /**
     * 获取视频信息以及构造 timeline 的 info
     * @param videoPath 视频文件路径
     * @param videoTransformConfig 视频下变换配置
     * @return Pair<VideoInfo, NvsTimelineInfo> first 是视频信息，second 是构造 timeline 的 info
     */
    private fun getVideoAndNvsInfo(videoPath: String, videoTransformConfig: VideoTransformConfig): Pair<VideoInfo, NvsTimelineInfo>? {
        val avFileInfo: NvsAVFileInfo = nvsContext?.getAVFileInfo(videoPath, NvsStreamingContext.AV_FILEINFO_EXTRA_INFO)
            .getOrLog("getVideoAndNvsInfo, avFileInfo") ?: return null

        val duration = avFileInfo.getVideoStreamDuration(0)
        val size = avFileInfo.getVideoStreamDimension(0).run { Size(width, height) }
        val rotation = avFileInfo.getVideoStreamRotation(0)
        val bitCount = avFileInfo.getVideoStreamComponentBitCount(0)
        val codecType = avFileInfo.getVideoStreamCodecType(0)
        val hdrType = avFileInfo.getVideoStreamHDRType(0)
        val colorPrimaries = avFileInfo.getVideoStreamColorPrimaries(0)
        val colorTransfer = avFileInfo.getVideoStreamColorTranfer(0)
        val fps = avFileInfo.getVideoStreamFrameRate(0)

        // 视频的原始色域
        val videoColorSpace = getColorSpace(colorPrimaries, colorTransfer)
        // 需要显示的色域。如果能够显示HDR，或者本来就不是HDR的色域，则使用原始的视频色域；否则使用美摄默认支持的SRGB色域
        val displayColorSpace = if (isSupportEditVideoHdrBrighten || !videoColorSpace.isHdrGamma()) {
            videoColorSpace
        } else {
            SRGB
        }
        val videoResolution = NvsVideoResolution().apply {
            val isRotated = rotation == VIDEO_ROTATION_90 || rotation == VIDEO_ROTATION_270
            val imageHeightTemp = if (isRotated) size.width else size.height
            val imageWidthTemp = if (isRotated) size.height else size.width

            //此处代码是为了适配美摄对olive操作时图片要求宽对齐4高对齐2
            imageHeight = MathUtil.getAlignedStrideUpToMultipleOf2(imageHeightTemp)
            imageWidth = MathUtil.getAlignedStrideUpToMultipleOf4(imageWidthTemp)

            imagePAR = avFileInfo.getVideoStreamPixelAspectRatio(0)
            // 如果视频显示的是HDR，并且能够显示HDR时，需要设置成VIDEO_RESOLUTION_BIT_DEPTH_AUTO; 当需要强制设置SDR timeline时，位深为8bit
            bitDepth = if (displayColorSpace.isHdrGamma() && !videoTransformConfig.forceSdr) {
                VIDEO_RESOLUTION_BIT_DEPTH_AUTO
            } else {
                VIDEO_RESOLUTION_BIT_DEPTH_8_BIT
            }
        }
        val audioResolution = NvsAudioResolution().apply {
            val hasAudioStream = avFileInfo.audioStreamCount > 0
            sampleRate = if (hasAudioStream) avFileInfo.getAudioStreamSampleRate(0) else SAMPLE_RETE
            channelCount = if (hasAudioStream) avFileInfo.getAudioStreamChannelCount(0) else CHANNEL_COUNT
        }
        val videoInfo = VideoInfo(duration, size, rotation, bitCount, codecType, hdrType, videoColorSpace, displayColorSpace)
        val nvsTimelineInfo = NvsTimelineInfo(videoResolution, audioResolution, fps)
        return Pair(videoInfo, nvsTimelineInfo)
    }

    /**
     * 根据 colorPrimaries 和 colorTransfer 获取对应的色彩空间
     */
    private fun getColorSpace(colorPrimaries: Int, colorTransfer: Int): ColorSpace {
        return when {
            (colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTransfer == COLOR_TRANSFER_HLG) -> BT2020_HLG
            (colorPrimaries == COLOR_PRIMARIES_BT2020) && (colorTransfer == COLOR_TRANSFER_ST2084) -> BT2020_PQ
            (colorPrimaries == COLOR_PRIMARIES_DISPLAY_P3) && (colorTransfer == COLOR_TRANSFER_SDR_VIDEO) -> DISPLAY_P3
            else -> null
        } ?: SRGB
    }

    /**
     * 构造timeline需要的信息对象
     * @param videoResolution 视频分辨率对象
     * @param audioResolution 音频分辨率对象
     * @param fps 帧率
     */
    private data class NvsTimelineInfo(
        val videoResolution: NvsVideoResolution,
        val audioResolution: NvsAudioResolution,
        val fps: NvsRational
    )

    /**
     * 根据 clip，创建 timeline
     *
     * @param nvsContext 美摄上下文
     * @param clip timeline 构建的描述对象
     * @param timelineInfo timeline的配置信息
     * @return timeline 创建的 timeline
     *
     * mark by chenzengxin 还有1.变速曲线 2.音频track 需要添加
     */
    private fun buildClip(
        nvsContext: NvsStreamingContext,
        clip: Clip,
        timelineInfo: NvsTimelineInfo,
    ): NvsTimeline? {
        val curTimeline = nvsContext.createTimeline(timelineInfo.videoResolution, timelineInfo.fps, timelineInfo.audioResolution)
        val videoTrack = curTimeline.appendVideoTrack()

        // 只有一个 path 的情况
        appendClip(videoTrack, clip)

        // 有多个 path 的情况
        clip.childClips?.forEach {
            appendClip(videoTrack, it)
        }
        return curTimeline
    }

    /**
     * 将相册定义的叶子 clip 添加到 track 上
     * @param videoTrack 美摄的 video track
     * @param clip 叶子 clip，用于 append nvs clip
     */
    private fun appendClip(videoTrack: NvsVideoTrack, clip: Clip) {
        clip.path?.let { path ->
            clip.range?.let { range ->
                videoTrack.appendClip(path, range.first, range.last)
            }
        }
    }

    /**
     * 在某些情况下配置 videoClip
     * 1. 外部入参要求使用转下变换
     * 2. 当需要显示为P3色域时，禁止色域转换，因为美摄会把sdr视频转为sRGB
     * @param nvsVideoClip 美摄的 clip 对象
     * @param displayColorSpace 用于显示的色域
     * @param videoTransformConfig 视频的下变换配置
     */
    private fun configVideoClipIfNeed(nvsVideoClip: NvsVideoClip?, displayColorSpace: ColorSpace, videoTransformConfig: VideoTransformConfig) {
        val isUseGalleryTransform = (videoTransformConfig.hdrTransformData != null)
                && HdrTransformFactory.isSupportHdrTransform()
                && ApiLevelUtil.isAtLeastAndroidU()
                && displayColorSpace.isHdrGamma()
                && videoTransformConfig.needFXTransformToSdr
        if (isUseGalleryTransform) {
            val transformConfig = TransformConfig(true, videoTransformConfig.hdrSdrRatioFunc)
            videoTransformConfig.hdrTransformData?.let { nvsVideoClip?.appendCustomFx(OliveHdrVideoFx(it, transformConfig)) }
            nvsVideoClip?.disableHDRTonemappingToSDR(true)
        } else if (displayColorSpace == DISPLAY_P3) {
            nvsVideoClip?.disableClipColorPrimariesConvert(true)
        }
    }

    override fun hasInit(): Boolean {
        return (nvsContext != null) && (nvsTimeline != null)
    }

    override fun getIEdit(): IEdit? = editImpl

    override fun getIInfo(): IInfo? = infoImpl

    override fun getIPlay(): IPlay? = playImpl

    override fun release() {
    }

    companion object {
        private const val TAG = "VideoEngine"
        private const val STREAMING_CONTEXT_FLAG = STREAMING_CONTEXT_FLAG_COMPACT_MEMORY_MODE or
                STREAMING_CONTEXT_FLAG_SUPPORT_4K_EDIT or
                STREAMING_CONTEXT_FLAG_ENABLE_HDR_DISPLAY_WHEN_SUPPORTED
        private const val SAMPLE_RETE: Int = 44100
        private const val CHANNEL_COUNT: Int = 2
    }
}