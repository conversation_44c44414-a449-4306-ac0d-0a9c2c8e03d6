/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - AIRepairTrackHelper.kt
 ** Description : AI修复埋点工具类.
 ** Version     : 1.0
 ** Date        : 2024/07/01
 ** Author      : <EMAIL>
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>     2024/07/01      1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair.track

import android.graphics.Bitmap
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.foundation.archv2.compositepage.INVALID
import com.oplus.gallery.foundation.tracing.Tracker
import com.oplus.gallery.foundation.tracing.constant.AIEditGalleryError
import com.oplus.gallery.foundation.tracing.constant.AIEditTrackerConstant
import com.oplus.gallery.foundation.tracing.constant.AIEditTrackerConstant.EVENT_ID_ENHANCE_REPAIR_DEBLUR
import com.oplus.gallery.foundation.tracing.constant.AIEditTrackerConstant.PICTURE_EDIT_TYPE
import com.oplus.gallery.foundation.util.text.TextUtil.EMPTY_STRING
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairResult
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairType
import com.oplus.gallery.photoeditor.track.EditExceptionType
import com.oplus.gallery.photoeditor.track.EditorErrorTrackHelper

/**
 * AI修复埋点Helper
 */
internal object AIRepairTrackHelper {

    /**
     * 记录修复事件埋点数据
     * @param info AI修复（去反光、模糊）操作记录对象
     * @param inputBitmap 管线预览图片
     * @param currentRepairType 当前操作类型：去反光 or 去模糊
     * @param strategyID 当前操作selection的strategyID
     */
    @JvmStatic
    fun recordRepairEventData(
        info: AIRepairTrackInfo,
        inputBitmap: Bitmap,
        currentRepairType: AIRepairType?,
        strategyID: Int?
    ) {
        info.strategyID = strategyID ?: INVALID
        info.imageWidthHeight = (inputBitmap.width * inputBitmap.height).toLong()
        info.repairType = when (currentRepairType) {
            AIRepairType.DEBLURING -> AIEditTrackerConstant.Value.AI_REPAIR_DE_BLUR_TYPE
            AIRepairType.DEREFLECTION -> AIEditTrackerConstant.Value.AI_REPAIR_DE_REFLECTION_TYPE
            AIRepairType.DEGLARE -> AIEditTrackerConstant.Value.AI_REPAIR_DE_GLARE_TYPE
            AIRepairType.DEFOG -> AIEditTrackerConstant.Value.AI_REPAIR_DE_FOG_MONGO_TYPE
            else -> AIEditTrackerConstant.Value.ERROR_INVALID_VALUE
        }
        recordRepairEnterTypeEventData(info, strategyID)
    }

    /**
     * 记录进入修复方式
     * @param info AI修复（去反光、模糊）操作记录对象
     * @param strategyID 当前操作selection的strategyID
     */
    private fun recordRepairEnterTypeEventData(info: AIRepairTrackInfo, strategyID: Int?) {
        info.enterType = AIEditTrackerConstant.Value.ENTER_TYPE_MENU
        val tempStrategyID = strategyID ?: return
        info.enterType = when (tempStrategyID) {
            R.id.strategy_ai_deblur -> AIEditTrackerConstant.Value.ENTER_TYPE_MENU
            R.id.strategy_specific_ai_deblur -> AIEditTrackerConstant.Value.ENTER_TYPE_RECOMMEND
            R.id.strategy_ai_dereflection -> AIEditTrackerConstant.Value.ENTER_TYPE_MENU
            R.id.strategy_specific_ai_dereflection -> AIEditTrackerConstant.Value.ENTER_TYPE_RECOMMEND
            R.id.strategy_specific_ai_lighting -> AIEditTrackerConstant.Value.ENTER_TYPE_RECOMMEND
            R.id.strategy_ai_deglare -> AIEditTrackerConstant.Value.ENTER_TYPE_MENU
            R.id.strategy_specific_ai_deglare -> AIEditTrackerConstant.Value.ENTER_TYPE_RECOMMEND
            R.id.strategy_ai_defog -> AIEditTrackerConstant.Value.ENTER_TYPE_MENU
            R.id.strategy_specific_ai_defog -> AIEditTrackerConstant.Value.ENTER_TYPE_RECOMMEND
            else -> AIEditTrackerConstant.Value.ENTER_TYPE_MENU
        }
    }

    /**
     * 单次修复功能事件（每次退出修复功能时触发，包括保存）
     * @param isSave 是否点击完成按钮
     * @param info AI修复（去反光、模糊）操作记录对象
     */
    @JvmStatic
    fun trackRepairExitEvent(isSave: Boolean, info: AIRepairTrackInfo) {
        if (info.cnt < 1) {
            return
        }
        Tracker.trackStore.createSingleTrack(
            event = EVENT_ID_ENHANCE_REPAIR_DEBLUR,
            type = PICTURE_EDIT_TYPE,
            func = { track ->
                track.putProperty(AIEditTrackerConstant.Key.IMG_WH, info.imageWidthHeight)
                track.putProperty(AIEditTrackerConstant.Key.COST_TIME, info.costTime)
                track.putProperty(AIEditTrackerConstant.Key.IS_SAVE_IMG, isSave)
                track.putProperty(AIEditTrackerConstant.Key.ENTER_TYPE, info.enterType)
                track.putProperty(AIEditTrackerConstant.Key.AI_TYPE, info.repairType)
                track.save()
            }
        )
    }

    /**
     * 构造相册端导致的修复异常事件数据
     *
     * @param info 操作记录对象
     * @param error 本地相册操作失败枚举
     */
    @JvmStatic
    fun trackGalleryError(mediaItem: MediaItem, info: AIRepairTrackInfo?, error: AIEditGalleryError) {
        info?.let {
            EditorErrorTrackHelper.trackEditException(
                EditExceptionType.PHOTO_EXCEPTION.ordinal, error.code, error.msg, it.strategyID, mediaItem
            )
        }
    }

    /**
     * 构造相册端导致的修复异常事件数据
     *
     * @param mediaItem 资源对象
     * @param info 操作记录对象
     * @param result 插件返回的结果
     */
    @JvmStatic
    fun trackAIUnitErrorEvent(mediaItem: MediaItem, info: AIRepairTrackInfo?, result: AIRepairResult?) {
        var errorType: Int = INVALID
        var errorCode = INVALID
        var errorMsg = EMPTY_STRING
        result?.apply {
            errorType = when {
                specificCode > 0 -> EditExceptionType.CLOUD_EXCEPTION.ordinal
                (code > 0) -> EditExceptionType.AIUNIT_EXCEPTION.ordinal
                else -> return
            }
            errorCode = when {
                specificCode > 0 -> specificCode
                (code > 0) -> code
                else -> return
            }
            errorMsg = msg ?: EMPTY_STRING
        } ?: run {
            errorType = EditExceptionType.AIUNIT_EXCEPTION.ordinal
            errorCode = INVALID
            errorMsg = EditExceptionType.AIUNIT_EXCEPTION.name
        }

        info?.let {
            EditorErrorTrackHelper.trackEditException(
                errorType, errorCode, errorMsg, info.strategyID, mediaItem
            )
        }
    }
}

/**
 * AI修复（去反光、模糊）操作记录对象
 */
internal class AIRepairTrackInfo {
    var strategyID: Int = INVALID
    var imageWidthHeight = 0L // 图片宽度*高度
    var startTime = 0L // 修复开始时间
    var costTime = 0L // 算法完成耗时时间
    var repairType: Int = AIEditTrackerConstant.Value.AI_REPAIR_DE_BLUR_TYPE  // 操作类型：去反光、模糊
    var enterType = 0 // 入口类型
    var cnt = 0 // 修复次数
}