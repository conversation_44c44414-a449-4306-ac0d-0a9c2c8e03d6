/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - PrintMatUtil
 ** Description: 打印工具，将矩阵，点，线，rect转换成可直接在matlab中处理的格式
 ** Version: 1.0
 ** Date : 2025/2/17
 ** Author: youpeng@Apps.Gallery3D
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  youpeng@Apps.Gallery3D      2025/02/17    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.animationcontrol

import android.graphics.PointF
import android.graphics.RectF
import com.oplus.gallery.foundation.opengl.transform.Line
import com.oplus.gallery.foundation.opengl.transform.Polygon
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_EDIT_TILT
import com.oplus.gallery.foundation.util.debug.LogFlag

object PrintMatUtil {
    private const val TAG = "PrintMatUtil"
    private var switch = false
    private var printTag = TAG
    private val pointSet by lazy { HashSet<PointF>() }
    private val lineSet by lazy { HashSet<Line>() }
    private val rectFSet by lazy { HashSet<RectF>() }
    private val polygonSet by lazy { HashSet<Polygon>() }

    @JvmStatic
    fun switch(tag: String? = null, open: Boolean) {
        printTag = tag ?: TAG
        switch = open
        if (open.not()) {
            pointSet.clear()
            lineSet.clear()
            rectFSet.clear()
            polygonSet.clear()
        }
        GLog.d(printTag, LogFlag.DL) { "=============PrintMatUtil: switch:$switch,tag:$printTag================" }
    }

    @JvmStatic
    fun PointF.addPrint() {
        pointSet.add(this)
    }

    @JvmStatic
    fun List<PointF>.addPrint() {
        pointSet.addAll(this)
    }

    @JvmStatic
    fun Line.addPrint() {
        lineSet.add(this)
    }

    @JvmStatic
    fun RectF.addPrint() {
        rectFSet.add(this)
    }

    @JvmStatic
    fun Polygon.addPrint() {
        polygonSet.add(this)
    }

    @JvmStatic
    fun printPointMatString() {
        if (DEBUG_EDIT_TILT && switch) {
            GLog.d(printTag, LogFlag.DL) { "printPointMatString start: ===========" }
            GLog.d(printTag, LogFlag.DL) { pointSet.joinToString("\n") { "${it.x},${it.y}" } }
            GLog.d(printTag, LogFlag.DL) { "printPointMatString end: ===========" }
        } else {
            GLog.d(printTag, LogFlag.DL) { "printPointMatString open debugLog " }
        }
    }

    @JvmStatic
    fun printLineMatString() {
        if (DEBUG_EDIT_TILT && switch) {
            GLog.d(printTag, LogFlag.DL) { "printLineMatString start: ===========" }
            GLog.d(printTag, LogFlag.DL) { lineSet.joinToString("\n") { "${it.startPoint.x},${it.startPoint.y},${it.endPoint.x},${it.endPoint.y}," } }
            GLog.d(printTag, LogFlag.DL) { "printLineMatString end: ===========" }
        } else {
            GLog.d(printTag, LogFlag.DL) { "printLineMatString open debugLog " }
        }
    }

    @JvmStatic
    fun printRectFMatString() {
        if (DEBUG_EDIT_TILT && switch) {
            val rectFString =
                rectFSet.joinToString("\n") { "${it.left},${it.bottom},${it.right},${it.bottom},${it.right},${it.top},${it.left},${it.top}" }
            val polygonStr = StringBuilder()
            polygonSet.forEachIndexed { index, polygon ->
                polygonStr.append("\n")
                polygonStr.append(polygon.vertices.joinToString { "${it.x},${it.y}" })
            }
            GLog.d(printTag, LogFlag.DL) { "printRectFMatString start: ===========" }
            GLog.d(printTag, LogFlag.DL) { "$rectFString\n$polygonStr" }
            GLog.d(printTag, LogFlag.DL) { "printRectFMatString end: ===========" }
        } else {
            GLog.d(printTag, LogFlag.DL) { "printRectFMatString open debugLog " }
        }
    }
}