/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - AIRepairSection.kt.
 ** Description: AI修复的页面切片
 ** Version: 1.0
 ** Date : 2024/4/25
 ** Author: xiewujie@Apps.Gallery3D
 ** TAG:AIRepairSection
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery3D      2024/04/25    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.airepair

import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageButton
import android.widget.RelativeLayout
import androidx.core.view.isVisible
import com.oplus.gallery.business_lib.template.editor.EditorAnimationUtils
import com.oplus.gallery.business_lib.template.editor.EditorUIConfig
import com.oplus.gallery.business_lib.template.editor.ui.WindowSizeForm
import com.oplus.gallery.foundation.archv2.ISectionBus
import com.oplus.gallery.foundation.archv2.section.SectionSpec
import com.oplus.gallery.foundation.ui.animation.AnimationHelper
import com.oplus.gallery.foundation.uikit.responsiveui.AppUiResponder
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty.DEBUG_REMOVE_EFFECT
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.display.RefreshRateUtils
import com.oplus.gallery.foundation.util.display.RefreshRateUtils.DEFAULT_SCREEN_FRAME_RATE
import com.oplus.gallery.foundation.util.display.RefreshRateUtils.PLAY_VIDEO_SCREEN_FRAME_RATE
import com.oplus.gallery.photoeditor.EditingFragment
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.BaseEditingSection
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_CANCEL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_CLICK_DETAIL_BUTTON
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_EFFECT_END
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_INTRODUCTION_CLICK
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.REPLY_TOPIC_AI_REPAIR_NOTIFY_WINDOWS_FOCUS_CHANGED
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.TOPIC_AI_REPAIR_INTRODUCTION_VIEW_DATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.AIRepair.TOPIC_AI_REPAIR_REPAIR_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_MODIFY_STATE
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairStatus.Companion.KEY_DE_REFLECTION_MASK
import com.oplus.gallery.photoeditor.editingvvm.airepair.AIRepairStatus.Companion.KEY_REQUEST_IMAGE
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.subscribeForSubscriber
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeForSubscriber
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.widget.layout.AIWatermarkView
import com.oplus.gallery.standard_lib.ui.util.DoubleClickUtils
import com.oplus.gallery.standard_lib.util.os.ContextGetter
import kotlin.math.min

/**
 * AI修复了功能的页面切片，包含去模糊、去反光
 */
internal class AIRepairSection(
    sectionBus: ISectionBus<EditingFragment, EditingVM>
) : BaseEditingSection(sectionBus) {

    private var effectView: AIRepairEffectView? = null

    private var stateView: AIRepairStateView? = null

    /** 触发引导页的独立布局 */
    private var interactiveViewLayout: View? = null

    /** 触发引导页的按键 */
    private var interactiveBtn: ImageButton? = null

    /** 显性水印的布局 */
    private var waterMarkViewLayout: View? = null

    private val aiRepairStatusObserver: TObserver<AIRepairStatus> = {
        onRepairStatusChanged(it)
    }

    private val introductionViewDataList: TObserver<List<AIRepairIntroductionViewData>> = {
        GLog.d(TAG, "[introductionViewDataList] size:${it.size}")
        showIntroduction(it)
    }

    private val onModifyStateChanged: TObserver<Boolean> = {
        GLog.d(TAG, "[onModifyStateChanged] is change :$it")
        ensureWatermarkView()
        waterMarkViewLayout?.isVisible = it
    }

    override fun getContainerViewWidthResourceId(): Int {
        return R.dimen.picture3d_editor_ai_repair_toolbar_width_landscape
    }

    override fun getContainerViewHeightResourceId(): Int {
        return R.dimen.picture3d_ai_repair_container_bar_height
    }

    override fun getContainerViewID(): Int {
        return R.id.toolbar_container
    }

    override fun getContentLayoutId(config: AppUiResponder.AppUiConfig): Int {
        return if (EditorUIConfig.isEditorLandscape(config)) {
            R.layout.photo_editor_ai_repair_toolbar_landscape
        } else {
            R.layout.photo_editor_ai_repair_toolbar
        }
    }

    override fun getReconfigureViewIds(): List<Int> {
        return listOf(R.id.ai_repair_list)
    }

    override fun onCreate() {
        super.onCreate()
        initInteractiveView()
        subscribeTopics()
        RefreshRateUtils.setDeviceRefreshRate(sectionBus.hostInstance.activity, PLAY_VIDEO_SCREEN_FRAME_RATE)
    }

    override fun onAppUiStateChanged(config: AppUiResponder.AppUiConfig) {
        super.onAppUiStateChanged(config)
        stateView?.post {
            updateStateViewPosition()
        }
        updateCompareButtonPosition()
        interactiveViewLayout?.post { updateInteractiveViewPosition() }
        waterMarkViewLayout?.post { updateWatermarkViewPosition() }
    }

    private fun subscribeTopics() {
        vBus.apply {
            subscribeForSubscriber(TOPIC_AI_REPAIR_REPAIR_STATUS, TAG, aiRepairStatusObserver)
            subscribeForSubscriber(TOPIC_AI_REPAIR_INTRODUCTION_VIEW_DATE, TAG, introductionViewDataList)

            subscribeT(TOPIC_OPERATING_MODIFY_STATE, onModifyStateChanged)
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        vBus.notifyOnce(REPLY_TOPIC_AI_REPAIR_NOTIFY_WINDOWS_FOCUS_CHANGED, hasFocus)
    }

    private fun initInteractiveView() {
        val baseUIView = sectionBus.rootView.findViewById<RelativeLayout>(R.id.photo_editor_ui_framework)
        interactiveViewLayout = layoutInflater.inflate(R.layout.picture3d_editor_tips_interactive_view, baseUIView, false).apply {
            addExtraViewToBaseUIView(this)
        }
        interactiveBtn = interactiveViewLayout?.findViewById<ImageButton>(R.id.tips_button)?.also {
            it.isEnabled = false
            it.setOnClickListener {
                if (DoubleClickUtils.isFastDoubleClick()) {
                    return@setOnClickListener
                }
                vBus.notifyOnce(REPLY_TOPIC_AI_REPAIR_CLICK_DETAIL_BUTTON, Unit)
            }
        }
        updateInteractiveViewPosition()
        EditorAnimationUtils.startAlphaAnimation(
            interactiveViewLayout,
            true,
            AnimationHelper.ALPHA_DELAY_TIME_133,
            AnimationHelper.ALPHA_EDITABLE_BOTTOM_MENU_DURATION,
            AnimationHelper.PHOTO_ALPHA_INTERPOLATOR,
            null
        )
    }

    private fun updateInteractiveViewPosition() {
        val appUiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
        interactiveViewLayout?.layoutParams = RelativeLayout.LayoutParams(
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
        ).apply {
            if ((appUiConfig.screenMode.current == AppUiResponder.ScreenMode.SMALL)
                && (appUiConfig.orientation.current == Configuration.ORIENTATION_LANDSCAPE)
                && (WindowSizeForm.getSizeForm(appUiConfig) == WindowSizeForm.TABLET_LANDSCAPE).not()
            ) {
                addRule(RelativeLayout.ALIGN_END, R.id.default_preview_area)
                marginEnd = activity.resources.getDimensionPixelSize(R.dimen.picture3d_ai_repair_tips_margin_end_small_landscape)
                topMargin = activity.resources.getDimensionPixelSize(R.dimen.picture3d_ai_repair_tips_margin_top_small_landscape)
            } else {
                marginEnd = activity.resources.getDimensionPixelSize(R.dimen.picture3d_ai_repair_tips_margin_end)
                topMargin = activity.resources.getDimensionPixelSize(R.dimen.picture3d_ai_repair_tips_margin_top)
                if (EditorUIConfig.isEditorLandscape(appUiConfig)) {
                    addRule(RelativeLayout.ALIGN_END, R.id.toolbar_container)
                } else {
                    addRule(RelativeLayout.ALIGN_PARENT_END)
                }
            }
        }
    }

    private fun ensureWatermarkView() {
        if (waterMarkViewLayout != null) {
            return
        }
        val layoutParams = RelativeLayout.LayoutParams(
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
        )
        waterMarkViewLayout = AIWatermarkView(sectionBus.rootView.context).also {
            addExtraViewToBaseUIView(it)
            it.visibility = View.GONE
            it.layoutParams = layoutParams
        }
        updateWatermarkViewPosition()
    }

    private fun updateWatermarkViewPosition() {
        val waterMarkViewLayout = waterMarkViewLayout ?: return
        val layoutParams = waterMarkViewLayout.layoutParams as? RelativeLayout.LayoutParams ?: let {
            GLog.e(TAG, "[updateWatermarkViewPosition] error, can't find layoutParams")
            return
        }

        layoutParams.apply {
            // watermarkView放置在底部titleBar上方
            addRule(RelativeLayout.ABOVE, R.id.title_bar_container)
            addRule(RelativeLayout.CENTER_HORIZONTAL)

            val appUiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
            val isLandScape = EditorUIConfig.isEditorLandscape(appUiConfig)
            val isLargeScreen = (appUiConfig.screenMode.current == AppUiResponder.ScreenMode.LARGE)
            if (isLandScape || isLargeScreen) {
                // 大屏/横屏
                height = activity.resources.getDimensionPixelSize(R.dimen.picture3d_editor_group_photo_hint_height_landscape)
                bottomMargin = 0
            } else {
                // 竖屏
                height = activity.resources.getDimensionPixelSize(R.dimen.picture3d_editor_group_photo_hint_height)
                val titleBarContainer = sectionBus.rootView.findViewById<RelativeLayout>(R.id.title_bar_container)
                val titleContainerPaddingTop = titleBarContainer.paddingTop
                bottomMargin = -titleContainerPaddingTop
            }
        }
        waterMarkViewLayout.layoutParams = layoutParams
    }

    private fun showIntroduction(introductionViewData: List<AIRepairIntroductionViewData>) {
        AIRepairIntroductionBuilder(sectionBus.hostInstance.requireContext(), introductionViewData).apply {
            setOnDismissListener {
                vBus.notifyOnce(REPLY_TOPIC_AI_REPAIR_INTRODUCTION_CLICK, Unit)
            }
            show()
        }
    }

    private fun onRepairStatusChanged(status: AIRepairStatus) {
        GLog.d(TAG, "[onRepairStatusChanged] status:$status")
        updateEffectView(status)
        updateRepairStateView(status)
    }

    private fun updateEffectView(status: AIRepairStatus) {
        when {
            status.state == AIRepairState.START -> {
                ensureEffectView()
                val image = status.data[KEY_REQUEST_IMAGE] as? Bitmap
                if (image != null) {
                    effectView?.setImageBitmap(image)
                    effectView?.startRecognizeAnimation()
                }
            }

            status.state == AIRepairState.RESULT_OK -> {
                if (DEBUG_REMOVE_EFFECT) {
                    vBus.notifyOnce(REPLY_TOPIC_AI_REPAIR_EFFECT_END, Unit)
                    return
                }
                when (status.repairType) {
                    AIRepairType.DEREFLECTION -> {
                        (status.data[KEY_DE_REFLECTION_MASK] as? Bitmap)?.let { mask ->
                            effectView?.setReflectionMask(mask)
                            effectView?.startDeReflectionAnimation()
                        }
                    }

                    AIRepairType.DEBLURING,
                    AIRepairType.DEGLARE,
                    AIRepairType.DEFOG -> effectView?.startDeblurAnimation()
                }
            }

            status.state.isErrorOrCancel -> effectView?.clearContent()

            else -> Unit
        }
    }

    private fun updateRepairStateView(status: AIRepairStatus) {
        status.state.also {
            when {
                it == AIRepairState.START -> {
                    ensureRepairStateView()
                    stateView?.show()
                    updateStateViewPosition()
                    updateViewOperability(false)
                    when (status.repairType) {
                        AIRepairType.DEBLURING -> stateView?.enterDebluringState()

                        AIRepairType.DEREFLECTION -> stateView?.enterDeReflectionState()

                        AIRepairType.DEGLARE -> stateView?.enterDeglareingState()

                        AIRepairType.DEFOG -> stateView?.enterDeFogMongoState()
                    }
                }

                it.isFinished -> {
                    stateView?.hide()
                    updateViewOperability(true)
                    updateCompareButtonPosition()
                }

                else -> Unit
            }
        }
    }

    private fun ensureEffectView() {
        if (effectView != null) {
            return
        }
        if (DEBUG_REMOVE_EFFECT) {
            GLog.d(TAG, LogFlag.DF, "[ensureEffectView] enter remove effect debug model")
            return
        }
        val context = sectionBus.hostInstance.context ?: let {
            GLog.e(TAG, "[ensureEffectView] can't create effect's view because context from fragment is null")
            return
        }
        effectView = AIRepairEffectView(context).also {
            addExtraViewToBaseUIView(it)
            it.init()
            it.contentDrawingOutBoundGetter = {
                vBus.get<PreviewAnimationProperties>(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES)?.contentDrawingOutBound?.final
            }
            it.onEffectEnd = {
                // 动画结束，通知VM层改变修复状态
                vBus.notifyOnce(REPLY_TOPIC_AI_REPAIR_EFFECT_END, Unit)
            }
            vBus.get<PreviewAnimationProperties>(TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES)?.let { gestureProperty ->
                it.setOriginImageSize(gestureProperty.imageRect.width().toInt(), gestureProperty.imageRect.height().toInt())
            }
        }
        // 菜单的层级需要比动效view的层级高
        sectionContainerView?.bringToFront()
    }

    private fun updateStateViewPosition() {
        val stateView = stateView ?: return
        applyBelowImageViewRule(stateView)
        stateView.bringToFront()
    }

    private fun updateCompareButtonPosition() {
        val compareButton = sectionBus.rootView.findViewById<View>(R.id.compare_button)
        (compareButton.layoutParams as? RelativeLayout.LayoutParams)?.apply {
            removeRule(RelativeLayout.ALIGN_BOTTOM)
            removeRule(RelativeLayout.BELOW)
            addRule(RelativeLayout.ABOVE, R.id.editor_ai_watermark_root_view)
            topMargin = 0
            marginEnd = sectionBus.rootView.resources.getDimensionPixelSize(
                R.dimen.picture3d_ai_repair_compare_button_margin_end_portrait
            )
            bottomMargin =
                sectionBus.rootView.resources.getDimensionPixelSize(R.dimen.picture3d_editor_toolbar_container_margin_end)
            compareButton?.layoutParams = this
        }
    }

    private fun applyBelowImageViewRule(view: View) {
        val resources = sectionBus.rootView.resources
        val margin = resources.getDimensionPixelSize(R.dimen.picture3d_ai_repair_state_view_margin)
        val topMargin = vBus.get<PreviewAnimationProperties>(
            TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
        )?.contentDrawingOutBound?.final?.let { imageRect ->
            val marginImage = imageRect.bottom.toInt() + margin
            min(marginImage, getMaxMarginTop())
        } ?: let {
            GLog.e(TAG, "[updateStateViewPosition] can't not calculate topMargin because drawingOutBound is null")
            return
        }
        (view.layoutParams as? MarginLayoutParams)?.let {
            it.topMargin = topMargin
            view.layoutParams = it
        }
    }

    private fun getMaxMarginTop(): Int {
        val resources = sectionBus.rootView.resources
        val titleBar = sectionBus.rootView.findViewById<View>(R.id.title_bar_container)
        val titleBarPosition = Rect().also {
            titleBar.getGlobalVisibleRect(it)
        }
        // 显性水印高度
        val aiWatermarkHeight = resources.getDimensionPixelSize(R.dimen.picture3d_ai_repair_watermark_height)
        // 胶囊的上下间距
        val margin = resources.getDimensionPixelSize(R.dimen.picture3d_ai_repair_state_view_margin)
        // 胶囊的高度
        val stateViewHeight = resources.getDimensionPixelSize(R.dimen.picture3d_progress_capsule_state_layout_height)
        return titleBarPosition.top - aiWatermarkHeight - margin - stateViewHeight
    }

    private fun ensureRepairStateView() {
        if (stateView != null) {
            return
        }
        val context = sectionBus.hostInstance.context ?: let {
            GLog.e(TAG, "[ensureEffectView] can't create effect's view because context from fragment is null")
            return
        }
        val layoutParams = RelativeLayout.LayoutParams(
            adjustedWidth(),
            LayoutParams.WRAP_CONTENT
        ).also {
            it.addRule(RelativeLayout.CENTER_HORIZONTAL)
        }
        stateView = AIRepairStateView(context).also {
            addExtraViewToBaseUIView(it)
            it.layoutParams = layoutParams
            it.setActionNextClickListener {
                vBus.notifyOnce(REPLY_TOPIC_AI_REPAIR_CANCEL)
            }
        }
    }

    /**
     * 调整胶囊宽度
     *  横屏、大屏：沿用原来规则
     *  竖屏：取window显示区域的最小边距
     */
    private fun adjustedWidth(): Int {
        val appUiConfig = sectionBus.hostInstance.getCurrentAppUiConfig()
        val isLandScape = EditorUIConfig.isEditorLandscape(appUiConfig)
        val isLargeScreen = (appUiConfig.screenMode.current == AppUiResponder.ScreenMode.LARGE)
            || (WindowSizeForm.getSizeForm(appUiConfig) == WindowSizeForm.STANDARD_PORTRAIT_MIDDLE)
        if (isLandScape || isLargeScreen) {
            return ContextGetter.context.resources.getDimensionPixelSize(
                R.dimen.picture3d_progress_capsule_width
            )
        }
        return min(
            ContextGetter.context.resources.displayMetrics.widthPixels,
            ContextGetter.context.resources.displayMetrics.heightPixels
        )
    }

    private fun updateViewOperability(operable: Boolean) {
        interactiveBtn?.isEnabled = operable
    }

    override fun onDestroy() {
        super.onDestroy()
        RefreshRateUtils.setDeviceRefreshRate(sectionBus.hostInstance.activity, DEFAULT_SCREEN_FRAME_RATE)
        vBus.apply {
            unsubscribeForSubscriber(TOPIC_AI_REPAIR_REPAIR_STATUS, TAG, aiRepairStatusObserver)
            unsubscribeForSubscriber(TOPIC_AI_REPAIR_INTRODUCTION_VIEW_DATE, TAG, introductionViewDataList)

            unsubscribe(TOPIC_OPERATING_MODIFY_STATE, onModifyStateChanged)
        }
        interactiveViewLayout?.let { removeExtraViewFromBaseUIView(it) }
        waterMarkViewLayout?.let { removeExtraViewFromBaseUIView(it) }
        effectView?.let {
            it.destroy()
            removeExtraViewFromBaseUIView(it)
        }
        stateView?.let { removeExtraViewFromBaseUIView(it) }
    }

    internal companion object {
        val SPEC = SectionSpec(AIRepairSection::class.java)
        const val TAG = "AIRepairSection"
    }
}