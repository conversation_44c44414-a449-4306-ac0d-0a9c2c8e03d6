/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - IEdit.kt
 ** Description: 视频编辑的接口
 ** Version: 1.0
 ** Date: 2025/06/19
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>      <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>       2025/06/19  1.0          OPLUS_ARCH_EXTENDS
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive.edit.engine2

import android.util.Rational
import com.oplus.gallery.business_lib.menuoperation.item.exportvideo.IExportVideoCallback
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.adjust.IVideoAdjustEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.edge.IVideoEdgeEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.filter.IVideoFilterEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.transform.IVideoTransformEditor
import com.oplus.gallery.photoeditor.editingvvm.olive.edit.watermark.IVideoWatermarkEditor
import java.util.Hashtable

/**
 * 视频编辑的接口
 */
interface IEdit {
    /**
     * 更改预览的视频帧率
     * @param fpsRational 要更改的帧率值
     */
    fun changeVideoFps(fpsRational: Rational): Boolean

    /**
     * 获取慢动作的编辑器
     * @return IOliveVideoFilterEditor
     */
    fun getSlowMotionEditor(): ISlowMotionEditor

    /**
     * 获取滤镜的编辑器
     * @return IOliveVideoFilterEditor
     */
    fun getFilterEditor(): IVideoFilterEditor

    /**
     * 获取调节的编辑器
     * @return IOliveVideoAdjustEditor
     */
    fun getAdjustEditor(): IVideoAdjustEditor

    /**
     * 获取旋转裁剪的编辑器
     * @return IOliveVideoTransformEditor
     */
    fun getTransformEditor(): IVideoTransformEditor?

    /**
     * 获取视频模糊边缘编辑的编辑器
     * @return IOliveVideoEdgeEditor
     */
    fun getVideoEdgeEditor(): IVideoEdgeEditor?

    /**
     * 获取水印的编辑器
     * @return IOliveVideoWatermarkEditor
     */
    fun getWatermarkEditor(): IVideoWatermarkEditor?

    /**
     * 导出视频
     * @param filePath 路径
     */
    fun exportVideo(videoType: Int, filePath: String?, configuration: Hashtable<String, Any>? = null)

    /**
     * 注册导出回调接口
     * @param exportVideoCallback IExportVideoCallback
     */
    fun registerExportVideoCallback(exportVideoCallback: IExportVideoCallback)

    /**
     * 反注册导出回调接口
     */
    fun unRegisterExportVideoCallback()
}