/*******************************************************************************
 * Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - TransformRemember.kt
 * Description:
 *
 * Version: 1.0
 * Date: 2024/11/21
 * Author: xiewu<PERSON><EMAIL>
 * TAG: OPLUS_ARCH_EXTENDS
 * ------------------------------- Revision History: ----------------------------
 * <author>						<date>			<version>		<desc>
 * ------------------------------------------------------------------------------
 * x<PERSON><PERSON><PERSON><PERSON>@Apps.Gallery		2024/11/21		1.0			OPLUS_ARCH_EXTENDS
 ******************************************************************************/

package com.oplus.gallery.photoeditor.editingvvm.transform

import android.graphics.RectF
import com.oplus.gallery.foundation.archv2.bus.IBusSubscriber
import com.oplus.gallery.foundation.opengl.transform.CompoundMatrixProxy
import com.oplus.gallery.foundation.opengl.transform.Matrix
import com.oplus.gallery.foundation.opengl.transform.TiltTransform
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.toRectF
import com.oplus.gallery.foundation.util.graphic.ImageUtils
import com.oplus.gallery.foundation.util.graphic.ImageUtils.SCALE_MODE_INSIDE
import com.oplus.gallery.framework.abilities.editing.editor.EditorResultState
import com.oplus.gallery.framework.abilities.editing.effect.AvEffect
import com.oplus.gallery.framework.abilities.editing.track.usage.IAvEffectUsage
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_CANCEL_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_CANCEL_EFFECT_AND_POP_RECORD
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.REPLY_TOPIC_OPERATING_RECORD_STEP
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_EDITOR_RESULT_STATE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_EFFECT_REQUEST
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_COMMAND
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_ANIMATION_PROPERTIES
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Preview.TOPIC_PREVIEW_TEXTURE_DIFF
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.ParametricDataSource
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.EffectRequestData
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingRecordAction
import com.oplus.gallery.photoeditor.editingvvm.operating.OperatingType
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationCommand
import com.oplus.gallery.photoeditor.editingvvm.preview.PreviewAnimationProperties
import com.oplus.gallery.photoeditor.editingvvm.preview.TextureDiff
import com.oplus.gallery.photoeditor.editingvvm.subscribeUntilConsumed

/**
 * 负责裁剪变换效果的移除和恢复
 * @param bus IBusSubscriber
 */
class TransformRemember(private val bus: IBusSubscriber) {

    /**
     * 是否有参数化记忆的裁剪记录
     * @return yes or no
     */
    val hasRemmemberTranformEffect: Boolean = getTargetTransformRecord() != null

    /**
     * 移除变换的记录，即取消原来的裁剪旋转变换效果
     * @param onRemoved 效果被移除后的回调
     * @param isRemoveRecord 移除效果的时候，是否把操作栈的记录移除
     */
    fun removeTransformEffect(onRemoved: () -> Unit = {}, isRemoveRecord: Boolean = false) {
        GLog.d(TAG, LogFlag.DF, "[removeTransformEffect]")
        val transformRecord = getTargetTransformRecord() ?: let {
            onRemoved.invoke()
            return
        }
        if (isRemoveRecord) {
            // 需要移除记录，则将效果和record移除
            bus.notifyOnce(REPLY_TOPIC_OPERATING_CANCEL_EFFECT_AND_POP_RECORD, transformRecord, true)
        } else {
            // 只需要移除效果
            bus.notifyOnce(REPLY_TOPIC_OPERATING_CANCEL_EFFECT, transformRecord, true)
        }
        runOnNextTextureUpdated {
            applyTransform(transformRecord)
            onRemoved.invoke()
        }
    }

    /**
     * 恢复被移除的裁剪旋转记录，搭配[removeTransformEffect]使用
     * @param render 恢复效果是否渲染
     * @param onRestored 被恢复后的回调
     */
    fun restoreTransformEffect(render: Boolean = true, onRestored: (isChange: Boolean) -> Unit) {
        GLog.d(TAG, LogFlag.DF, "[restoreTransformEffect] render:$render")
        val transformRecord = getTargetTransformRecord() ?: let {
            onRestored.invoke(false)
            return
        }
        bus.notifyOnce<IAvEffectUsage>(
            REPLY_TOPIC_OPERATING_RECORD_STEP,
            OperatingRecordAction(transformRecord, OperatingType.ONLY_EFFECT, render = render)
        )
        runOnNextTextureUpdated { onRestored.invoke(true) }
    }

    /**
     * 下一次纹理更新后，执行指定的函数体[block],依赖此时机的一般发生在纹理更新后，需要立刻更新变换参数，否则图片和Transform不对应会闪屏
     */
    private fun runOnNextTextureUpdated(block: () -> Unit) {
        val requestCode = bus.get<EffectRequestData>(TOPIC_PIPELINE_EFFECT_REQUEST)?.requestCode ?: let {
            GLog.e(TAG, "[runOnNextTextureUpdated] error, can't get current requestCode")
            block.invoke()
            return
        }
        bus.subscribeUntilConsumed<TextureDiff>(TOPIC_PREVIEW_TEXTURE_DIFF, false) {
            val responseCode = bus.get<EditorResultState>(TOPIC_PIPELINE_EDITOR_RESULT_STATE)?.responseCode ?: 0
            if (requestCode <= responseCode) {
                GLog.d(TAG, LogFlag.DF, "[runOnNextTextureUpdated] handle texture updated event,code:$responseCode")
                block.invoke()
                true
            } else {
                GLog.d(TAG, LogFlag.DF, "[runOnNextTextureUpdated] ignore this response, requestCode:$requestCode, responseCode:$responseCode")
                false
            }
        }
    }

    /**
     * 应用[record]的变换
     * 1. 更新裁剪区域
     * 2. 应用变换矩阵
     * 3. ZoomToCenter, record所记录的预览区域和现在内容显示的预览区域可能不一致（比如裁剪后竖屏保存，横屏进入），
     *    需要重新移动图片到中心
     * @param record TransformRecord
     */
    private fun applyTransform(record: TransformRecord) {
        val transformRecord = record.asTransformRecord()
        val animationProperties = bus.get<PreviewAnimationProperties>(TOPIC_PREVIEW_ANIMATION_PROPERTIES) ?: return
        val verticalTilt = transformRecord.verticalTiltForApplyTransform
        val horizontalTilt = transformRecord.horizontalTiltForApplyTransform
        postAnimationCommand(
            PreviewAnimationCommand.UpdateClipRect(transformRecord.clipRect.toRectF()),
            PreviewAnimationCommand.UpdateContentTransform(transformRecord.getContentTransform(animationProperties)),
            PreviewAnimationCommand.TiltContentTransform(
                -verticalTilt.toFloat(),
                horizontalTilt.toFloat(),
                constraintScale = 0f
            ),
            PreviewAnimationCommand.ZoomToCenter,
            PreviewAnimationCommand.ForceFinishAnimation
        )
    }

    /**
     * 获取内容变换矩阵，record中存储的Matrix是fullTransform(语义同：[PreviewAnimationProperties.fullTransform]),需要根据现在的约束区域计算contentTransform
     * @return 内容变换矩阵，同[PreviewAnimationProperties.contentTransform]
     */
    private fun TransformRecord.getContentTransform(animationProperties: PreviewAnimationProperties): Matrix {
        val imageRect = this.imageRect.toRectF()
        val deltaScale = 1 / ImageUtils.scaleImage(
            imageRect.width(),
            imageRect.height(),
            animationProperties.fullContentVisibleRect.width(),
            animationProperties.fullContentVisibleRect.height(),
            SCALE_MODE_INSIDE
        )

        val compoundMatrix = CompoundMatrixProxy()
        compoundMatrix.set(matrixWithoutTilt)
        compoundMatrix.scale(deltaScale, deltaScale, deltaScale)
        val clipRect = RectF(animationProperties.clipRect.final)
        if (axisAngleTransform != null) {
            compoundMatrix.axisAngleTransform = axisAngleTransform
            // imageTransform已经是finalMatrix了，再加上axisAngleTransform会多做一次镜像，下面对final做一次镜像抵消掉axisAngleTransform
            compoundMatrix.makeFinalMatrix(clipRect.centerX(), clipRect.centerY())
            compoundMatrix.set(compoundMatrix.finalMatrix)
        }
        // 这里需要赋值一个空的带图片宽高的tiltTransform，否则执行UpdateContent会出现异常
        compoundMatrix.tiltTransform = TiltTransform(0f, 0f, imageRect.width(), imageRect.height())
        return compoundMatrix
    }

    /**
     * 发送动画指令给预览页，预览页执行动画变换
     * @param command 动画指令
     */
    private fun postAnimationCommand(vararg command: PreviewAnimationCommand) {
        command.forEach {
            bus.notifyOnce(TOPIC_PREVIEW_ANIMATION_COMMAND, it)
        }
    }

    /**
     * 获取距离非参数化操作(参数化记忆只能针对参数化的record)最近的裁剪变换记录
     * @return TransformRecord?
     */
    private fun getTargetTransformRecord(): TransformRecord? {
        val isSupportParametric = bus.get<ParametricDataSource>(TOPIC_INPUTS_PARAMETRIC_DATA_SOURCE)?.isSupportParametric ?: false
        if (isSupportParametric.not()) {
            GLog.d(TAG, LogFlag.DL, "[getTargetTransformRecord] don't support parametric, do return null")
            return null
        }
        return bus.notifyOnce<TransformRecord>(REPLY_TOPIC_OPERATING_FIND_RECORD_UNTIL_NON_PARAMETRIC, AvEffect.TRANSFORM_EFFECT)
    }

    companion object {
        private const val TAG = "TransformRemember"
    }
}