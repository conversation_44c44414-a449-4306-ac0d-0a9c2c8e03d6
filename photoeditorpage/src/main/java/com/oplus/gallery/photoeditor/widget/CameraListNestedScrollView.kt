/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - CameraListNestedScrollView.kt
 ** Description: 相机设置页嵌套滑动控件.
 ** Version: 1.0
 ** Date : 2024/10/31
 ** Author: Wen<PERSON>.<EMAIL>.OppoGallery3D
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** Wenhao.Deng   2024/10/31      1.0        created
 ***************************************************************/
package com.oplus.gallery.photoeditor.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.core.view.ViewCompat
import androidx.core.widget.NestedScrollView
import com.oplus.gallery.foundation.util.math.MathUtil.roundToInt
import kotlin.math.abs

/**
 * 相机设置页嵌套滑动控件
 */
class CameraListNestedScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : NestedScrollView(context, attrs, defStyleAttr) {
    private var mLastXIntercept = 0f
    private var mLastYIntercept = 0f
    private var touchSlop: Int = 0
    private var initialTouchY = 0

    init {
        val viewConfiguration: ViewConfiguration = ViewConfiguration.get(context)
        touchSlop = viewConfiguration.scaledTouchSlop
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        var intercepted = false
        val x = ev.x
        val y = ev.y
        val canScrollVertically = canScrollVertically(1) || canScrollVertically(-1)
        when (ev.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                // 手指按下屏幕，不拦截事件，停止手指滑动和惯性滑动
                intercepted = false
                initialTouchY = ev.y.roundToInt()
                stopNestedScroll(ViewCompat.TYPE_TOUCH)
                stopNestedScroll(ViewCompat.TYPE_NON_TOUCH)
                super.onInterceptTouchEvent(ev)
            }

            MotionEvent.ACTION_MOVE -> {
                // 手指在屏幕上移动，纵向位移大于横向位移时，拦截事件
                val deltaX = x - mLastXIntercept
                val deltaY = y - mLastYIntercept
                val dy = y - initialTouchY
                intercepted = canScrollVertically && (abs(dy) > touchSlop) && (abs(deltaX) < abs(deltaY))
            }

            MotionEvent.ACTION_UP -> intercepted = false
        }
        mLastXIntercept = x
        mLastYIntercept = y
        return intercepted
    }
}