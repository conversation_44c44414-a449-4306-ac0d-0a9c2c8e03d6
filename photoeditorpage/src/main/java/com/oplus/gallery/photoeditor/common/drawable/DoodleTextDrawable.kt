/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - DoodleTextDrawable.kt
 ** Description: 文字编辑
 ** Version: 1.0
 ** Date : 2023/11/07
 ** Author: <EMAIL>
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>       <desc>
 ** ------------------------------------------------------------------------------
 **  <EMAIL>      2023/10/24        1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.common.drawable

import android.content.Context
import android.graphics.Matrix

import com.oplus.gallery.photoeditor.base.processor.entry.ColorSizeEntry

class DoodleTextDrawable(
    context: Context,
    color: ColorSizeEntry.ColorEntry,
    backgroundColor: ColorSizeEntry.ColorEntry,
    type: DrawableType,
    imageMatrix: Matrix,
    defaultText: String
) : TextDrawable(context, color, backgroundColor, type, imageMatrix, defaultText)