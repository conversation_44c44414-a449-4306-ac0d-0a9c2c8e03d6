/***************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - EliminatePenModelLoader.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/16
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------------------------------------------
 **  <author>                       <data>         <version >     <desc>
 **  <EMAIL>    2024/4/16        1.0           Add
 **************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.component.eliminate.normal

import android.content.Context
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper
import com.oplus.gallery.foundation.fileaccess.utils.FileOperationUtils
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.text.TextUtil
import com.oplus.gallery.framework.abilities.config.ISettingsAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.Editor.PhotoEditor.ELIMINATE_PEN_VERSION_IGNORE_COUNT
import com.oplus.gallery.framework.abilities.download.template.CommonModelLoadingTemplate
import com.oplus.gallery.framework.abilities.download.template.ModelConfig
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.component.BaseRemoteModelLoader
import com.oplus.gallery.standard_lib.app.AppScope
import com.oplus.gallery.standard_lib.file.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 消除笔v2模型加载器
 */
internal class EliminatePenModelLoader(context: Context) : BaseRemoteModelLoader(context) {
    override val networkAuthorizationDialogMessageResId = R.string.picture3d_authorizing_request_network_eliminate_pen
    override val mobileNetworkDialogTitleResId = R.string.picture3d_eliminate_pen_mobile_dialog_title
    override val modelConfig: ModelConfig = EliminatePenModelConfig()
    override val modelLoadingTemplate: CommonModelLoadingTemplate = EliminatePenModelLoadingTemplate(context, modelConfig)
    override val refuseUpgradeLimit: Int = REFUSE_UPGRADE_MAX_COUNT

    override fun getRefuseUpgradeCountFromStorage(): String {
        return ConfigAbilityWrapper.getString(ELIMINATE_PEN_VERSION_IGNORE_COUNT) ?: TextUtil.EMPTY_STRING
    }

    override fun setRefuseUpgradeCountToStorage(count: String) {
        context.getAppAbility<ISettingsAbility>()?.use {
            it.setEliminateIgnoreVersionCount(count)
        }
    }

    override fun loadDynamically(isPreviousDownloaded: Boolean): Pair<Boolean, Boolean> {
        runCatching {
            // Marked by zhengyirui 依赖CvInpaintingImpl
            /*CvInpaintingImpl.getModelVersion()*/
            GLog.d(TAG, "loadDynamically: getModelVersion success return")
            return Pair(true, false)
        }.onFailure {
            GLog.w(TAG, "loadDynamically: has not loaded yet. error= ${it.message}")
        }
        val currentPath = getCurrentComponentPath()
        val isSourceExist = isSourceExist(currentPath)
        GLog.d(TAG, "loadDynamically: isSourceExist = $isSourceExist")
        if (!isSourceExist) return Pair(false, false)

        runCatching {
            val soFileWithAbsPath = currentPath + File.separator + EliminatePenModelConfig.FILE_NAME_LIB_ELIMINATE_PEN
            val isSoFileSrcExist = File(soFileWithAbsPath).exists()
            GLog.d(TAG, "loadDynamically:soFileWithAbsPath= $soFileWithAbsPath, isSoFileSrcExist=$isSoFileSrcExist")
            System.load(soFileWithAbsPath)
        }.onFailure {
            GLog.w(TAG, "loadDynamically: error=${it.message}  isPreviousDownloaded = $isPreviousDownloaded")
            if (isPreviousDownloaded) {
                clearOldVersion(currentPath)
                clearOldVersion(modelConfig.modelDownloadDir.absolutePath)
                context.getAppAbility<ISettingsAbility>()?.use { settingsAbility ->
                    settingsAbility.setEliminateLocalVersion(0)
                }
                // 因为是失败后主动的重试，所以EliminatePenModelLoadingTemplate.version 不需要置为-1
                isSourceExistAndChecked = false
                GLog.w(TAG, "loadDynamically: need retry load so")
                return Pair(false, true)
            }
            return Pair(false, false)
        }
        clearOldVersion(currentPath)
        GLog.d(TAG, "loadDynamically: success")
        return Pair(true, false)
    }

    override fun getCurrentComponentPath(): String {
        val defaultPath = modelConfig.defaultComponentDirPath.absolutePath
        return ConfigAbilityWrapper.getString(ConfigID.Business.Editor.PhotoEditor.ELIMINATE_PEN_COMPONENT_SAVE_PATH) ?: defaultPath
    }

    override fun isSourceExist(path: String?): Boolean {
        if (path.isNullOrEmpty()) return false

        val file = File(path + File.separator + EliminatePenModelConfig.FILE_NAME_LIB_ELIMINATE_PEN)
        if (!file.exists()) {
            GLog.w(TAG, "isSourceExist: file is not exists")
            return false
        }
        return true
    }

    private fun clearOldVersion(currentPath: String?) {
        if (currentPath.isNullOrEmpty()) {
            GLog.w(TAG, "clearOldVersion: currentPath is null or empty, return")
            return
        }
        AppScope.launch(Dispatchers.IO) {
            val defaultPath = modelConfig.defaultComponentDirPath.absolutePath
            val subPath = modelConfig.subComponentDirPath.absolutePath
            if (currentPath == defaultPath) {
                FileOperationUtils.deleteFolderFile(subPath, true)
            } else {
                FileOperationUtils.deleteFolderFile(defaultPath, true)
            }
        }
    }

    companion object {
        private const val TAG = "EliminatePenModelLoader"
        private const val REFUSE_UPGRADE_MAX_COUNT = 2
    }
}