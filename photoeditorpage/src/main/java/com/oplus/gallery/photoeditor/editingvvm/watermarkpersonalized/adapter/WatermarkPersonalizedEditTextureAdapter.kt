/**************************************************************************************************
 ** Copyright (C), 2025-2035, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - WatermarkPersonalizedEditTextureAdapter
 ** Description: 限定水印底纹列表适配器
 ** Version: 1.0
 ** Date : 2025/3/16
 ** Author: <EMAIL>
 ** TAG:限定水印底纹列表适配器
 *
 ** ------------------------------- Revision History: ---------------------------------------------
 ** <author>                      <date>        <version>        <desc>
 ** -----------------------------------------------------------------------------------------------
 ** <EMAIL>     2025/3/16      1.0            Add new file.
 *************************************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.adapter

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.coui.appcompat.cardview.COUICardView
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecycleViewHolder
import com.oplus.gallery.business_lib.template.editor.adapter.BaseRecyclerAdapter
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_GRADIENT
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_PURE_COLOR
import com.oplus.gallery.framework.abilities.watermark.masterstyle.BG_TYPE_URL
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.WatermarkPersonalizedEditSection
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.bean.WatermarkPersonalizedEditTextureItem
import com.oplus.gallery.photoeditor.editingvvm.watermarkpersonalized.widget.WatermarkPersonalizedEditMenuItemView

/**
 * 限定水印底纹编辑列表适配器
 */
class WatermarkPersonalizedEditTextureAdapter(
    context: Context,
    data: MutableList<WatermarkPersonalizedEditTextureItem>,
    val layoutId: Int
) : BaseRecyclerAdapter<WatermarkPersonalizedEditTextureItem>(
    context,
    data
) {
    /**
     * 当前所有item是否可点击
     */
    private var isAllItemClickable = true

    override fun getItemLayoutId(viewType: Int): Int {
        return R.layout.picture3d_editor_personalise_texture_list_item
    }

    override fun bindData(
        viewHolder: BaseRecycleViewHolder,
        position: Int,
        item: WatermarkPersonalizedEditTextureItem
    ) {
        val parent = viewHolder.itemView as LinearLayout
        val pictureRing = parent.findViewById<ImageView>(R.id.iv_picture_ring)
        val picture = parent.findViewById<ImageView>(R.id.iv_picture)
        val cardView = parent.findViewById<COUICardView>(R.id.card_view_picture)
        item.textTureText?.let { parent.findViewById<TextView>(R.id.logo_tv)?.text = it }
        if ((layoutId == WatermarkPersonalizedEditSection.PORTRAIT_LAYOUT_ID)) {
            parent.findViewById<View>(R.id.picture_item_head)?.visibility =
                if (position == 0) View.VISIBLE else View.GONE
            parent.findViewById<View>(R.id.picture_item_footer)?.visibility =
                if (position == (data.size - 1)) View.VISIBLE else View.GONE
        }
        cardView.setCardBackgroundColor(
            ContextCompat.getColor(mContext, R.color.picture3d_editor_personalise_logo_item_background_dark)
        )
        if (item.isSelected) {
            pictureRing.visibility = View.VISIBLE
        } else {
            pictureRing.visibility = View.INVISIBLE
        }

        if ((item.backgroundType == BG_TYPE_URL) || (item.backgroundType == BG_TYPE_GRADIENT)) {
            item.thumbnailBitmap?.let { picture?.setImageBitmap(it) }
        } else if (item.backgroundType == BG_TYPE_PURE_COLOR) {
            item.color?.let { picture?.setBackgroundColor(Color.parseColor(it)) }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseRecycleViewHolder {
        val itemView = if (getItemLayoutId(viewType) != Resources.ID_NULL) {
            mInflater.inflate(getItemLayoutId(viewType), parent, false)
        } else {
            createItemView()
        }

        val viewHolder = createViewHolder(itemView, viewType)

        val itemLayout = viewHolder.itemView as LinearLayout
        val pictureView =
            itemLayout.findViewById<WatermarkPersonalizedEditMenuItemView>(R.id.edit_picture_item_layout)
        pictureView.setOnTouchListener(object : View.OnTouchListener {
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        pictureView.startScaleAnimation(FULL_SCALE, SCALE_FACTOR)
                        val viewId = v.id
                        if (isAllItemClickable.not() || checkClickEvent(viewId)) {
                            GLog.d(TAG, LogFlag.DL) { "[onTouch] click to be blocked, isAllItemClickable: $isAllItemClickable, viewId: $viewId" }
                            return true
                        }
                        mLastClickViewId = viewId
                        itemClickEvent(v, viewHolder.layoutPosition, viewId)
                    }

                    MotionEvent.ACTION_UP,
                    MotionEvent.ACTION_CANCEL -> pictureView.startScaleAnimation(SCALE_FACTOR, FULL_SCALE)
                }
                return true
            }
        })
        return viewHolder
    }

    /**
     * 设置所有item是否可点击
     * @param isClickable 是否可点击
     */
    fun setAllItemClickable(isClickable: Boolean) {
        isAllItemClickable = isClickable
    }

    internal companion object {
        const val TAG = "WatermarkPersonalizedEditPictureAdapter"

        /**
         * item缩小值
         */
        private const val SCALE_FACTOR: Float = 0.95F

        /**
         * item默认值
         */
        private const val FULL_SCALE: Float = 1F
    }
}