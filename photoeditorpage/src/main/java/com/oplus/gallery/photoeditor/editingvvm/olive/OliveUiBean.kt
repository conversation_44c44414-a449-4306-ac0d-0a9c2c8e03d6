/********************************************************************************
 ** Copyright (C), 2024-2034, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - OliveUiBean
 ** Description: 实况切片的UI属性
 ** Version: 1.0
 ** Date : 2024/5/31
 ** Author: lizhi
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 ** lizhi                           2024/5/31    1.0          first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.olive

import android.graphics.Bitmap
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageType

/**
 * 实况编辑页，UI的状态属性
 *
 * @param hasImageCoverChanged 图片封面是否更改过
 * @param hasSupportRestoreOriginalExt 扩展数据中是否标记了支持还原原始图
 */
data class OliveUiBean(
    var id: OliveBeanId = OliveBeanId.NONE,
    // 当前封面在olive视频里对应的时间
    var coverTimeMs: Long = OLiveEditConstant.INVALID_TIME_MS,
    // 封面图
    var coverBitmap: Bitmap? = null,
    // 当前cover所对应的GainMap信息。
    var coverGainMap: Bitmap? = null,
    // 相机默认原始大图在olive视频里对应的时间
    var originalTimeMs: Long = OLiveEditConstant.INVALID_TIME_MS,
    // seekbar  position
    var slidingTimeMs: Long = OLiveEditConstant.INVALID_TIME_MS,
    // olive视频时长
    var totalDuration: Long = OLiveEditConstant.INVALID_DURATION,
    // 界面底部缩图轴图片列表
    var thumbnailList: List<OLiveThumbnailItem> = emptyList(),
    // 储存的seekbar滑动后需要的吸附到特定位置的参数
    var adsorbentList: List<Long> = emptyList(),
    // 裁减-标定播放区间开始位置
    var startTime: Long = OLiveEditConstant.INVALID_TIME_MS,
    // 裁减-标定播放区间结束位置
    var endTime: Long = OLiveEditConstant.INVALID_TIME_MS,
    var hasShowUnSupportTips: Boolean = false,
    // 是否换过封面
    var hasImageCoverChanged: Boolean = false,
    // 是否是无水印的原始大图
    var hasSupportRestoreOriginalExt: Boolean = false,

    /**
     * 当前使用的是否为耗时的封面处理器
     * 用途：用于判断ui上是否支持显示图片右下角的loadingView
     * 目的：达到较好的ui交互体验，避免不同算法处理事件差异带来闪问题
     * 范围：只用于图片右下角loadingView是否显示使用
     * 场景：
     * 1.设为为封面--如果是upscale算法，处理的很快，不需要显示loadingView
     */
    var isLongRunningCoverProcessor: Boolean = false,

    /**
     * 当前封面的处理状态
     * 用途：用于判断当前封面是否处于处理状态/封面处理器是否处工作状态
     * 目的：识别设为封面动作整条链路是否完成与界面loadingView是否显示不耦合
     * 范围：只用于标记封面处理状态/设置为封面整个动作链路是否完成
     * 场景：
     * 1.设为为封面--封面优化处理
     */
    var isCoverProcessing: Boolean = false,

    /**
     * Olive超清导出的处理状态
     * 用途：用于判断当前Olive图是否处于超清导出的状态
     * 目的：能够获取导出功能的执行状态，可以控制一部分UI状态（loading显示等）
     * 范围：只用于标记Olive导出处理状态/是否完成
     * 场景：
     * Olive超清导出
     */
    var isExportProcessing: Boolean = false,

    /**
     * 设置封面的时候是否切换到图片显示模式
     */
    var isSetCoverSwitchDisplayImageMode: Boolean = true,

    /**
     * 实况开关状态
     */
    var oliveEnable: Boolean = true,

    /**
     * 声音开关状态
     */
    var oliveSoundEnable: Boolean = true,

    /**
     * hdr的增益信息
     */
    var hdrMeta: Any? = null,

    /**
     * hdr的类型
     */
    var hdrImageType: HdrImageType? = null,
) {
    override fun toString(): String {
        var thumbnailStr = ""
        thumbnailList.forEach {
            thumbnailStr += "${it.timeMs}, "
        }
        var adsorbentStr = ""
        adsorbentList.forEach {
            adsorbentStr += "$it, "
        }
        return "OliveUiBean(id=$id, coverTimeMs=$coverTimeMs, originalTimeMs=$originalTimeMs, slidingTimeMs=$slidingTimeMs," +
                " totalDuration=$totalDuration, startTime=$startTime, endTime=$endTime," +
                " thumbnailList=$thumbnailStr, adsorbentList=$adsorbentStr, hasShowUnSupportTips=$hasShowUnSupportTips," +
                " hasImageCoverChanged=$hasImageCoverChanged, hasSupportRestoreOriginalExt=$hasSupportRestoreOriginalExt)," +
                " coverBitmap = $coverBitmap, coverGainmap:$coverGainMap" +
                " isLongRunningCoverProcessor = $isLongRunningCoverProcessor, isCoverProcessing = $isCoverProcessing," +
                " isSetCoverSwitchDisplayImageMode = $isSetCoverSwitchDisplayImageMode," +
                " oliveEnable = $oliveEnable, oliveSoundEnable = $oliveSoundEnable"
    }
}

/**
 * 接收/发送 UiBean 事件的ID
 *
 * 按照原始逻辑，注意：不要再A事件ID下做不相关的B业务数据更新，会导致业务数据混乱
 */
enum class OliveBeanId {
    NONE,

    /**
     * 初始化数据动作
     */
    RECOVER,

    /**
     * 完成当前封面初始化的动作。
     *
     */
    INIT_COVER,

    /**
     * 更新封面
     * 使用场景：
     * 1.通知VM，更新封面时间及获取封面图
     * 2.通知Section 将准备好的coverBitmap 显示在界面上
     */
    COVER,

    /**
     * 更新滑动：从逻辑看这个代表的是seekBar的position/progress
     * 使用场景：
     * 1.通知Section显示当前滑动到位置对应的界面
     */
    SLIDING,

    /**
     * olive视频时长
     */
    TOTAL_DURATION,

    THUMBNAIL_LIST,

    ADSORBENT_LIST,

    /**
     * olive2.0视频播放区间标定-起始时间
     */
    START_TIME,

    /**
     * olive2.0视频播放区间标定-结束事件
     */
    END_TIME,

    /**
     * 实况开关状态变更
     * 使用场景：
     * 1.通知VM，更新实况开关状态
     */
    OLIVE_ENABLE_STATUS,

    /**
     * 声音开关状态变更
     * 使用场景：
     * 1.通知VM，更新声音开关状态
     */
    OLIVE_SOUND_STATUS,

    /**
     * 导出超清图
     * 使用场景：
     * 1.通知VM，开始执行导出超清图功能
     */
    OLIVE_EXPORT_IMAGE,

    /**
     * 慢动作
     */
    OLIVE_SLOW_MOTION
}