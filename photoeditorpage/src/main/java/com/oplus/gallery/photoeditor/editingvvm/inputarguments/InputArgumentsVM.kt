/********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - InputArgumentsVM
 ** Description:
 ** Version: 1.0
 ** Date : 2024/1/26
 ** Author: wudanyang
 ** TAG:
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>    <desc>
 ** ------------------------------------------------------------------------------
 **  wudanyang                      2024/1/26    1.0         first created
 ********************************************************************************/
package com.oplus.gallery.photoeditor.editingvvm.inputarguments

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.ColorSpace
import android.graphics.Rect
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.util.Size
import androidx.core.net.toUri
import com.oplus.andes.photos.kit.utils.ConstantUtil.COMMA
import com.oplus.gallery.basebiz.constants.IntentConstant
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_BRIGHTEN_GRAY_IMAGE
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_BRIGHTEN_METADATA
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_EXTRAS
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_FILE_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_FROM_PHOTO
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_IMAGE_URI
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_MEDIA_ITEM_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_MEDIA_SET_PATH
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_ROTATION
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_SKILL
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_SUPPORT_LOSS_LESS_CACHE
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_EDIT_THUMBNAIL
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_HASSEL_WATERMARK_EDITABLE
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_HAS_HASSEL_WATERMARK
import com.oplus.gallery.basebiz.constants.IntentConstant.EditablePhotoConstant.KEY_PHOTO_POSITION_FOR_EDITOR
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_CHAIN_FROM
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_CAMERA
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_FROM_SELL
import com.oplus.gallery.basebiz.constants.IntentConstant.ViewGalleryConstant.KEY_MEDIA_FROM
import com.oplus.gallery.basebiz.helper.ConfigAbilityWrapper.getBoolean
import com.oplus.gallery.basebiz.uikit.activity.BaseActivity
import com.oplus.gallery.business_lib.model.config.PhotoEditorType
import com.oplus.gallery.business_lib.model.data.base.Path
import com.oplus.gallery.business_lib.model.data.base.item.MediaItem
import com.oplus.gallery.business_lib.model.data.base.item.isLhdrPhoto
import com.oplus.gallery.business_lib.model.data.base.item.isUhdrPhoto
import com.oplus.gallery.business_lib.model.data.base.source.DataManager
import com.oplus.gallery.business_lib.model.data.base.utils.Constants
import com.oplus.gallery.business_lib.model.data.local.utils.LocalMediaDataHelper
import com.oplus.gallery.business_lib.model.data.location.camera.CameraUtil
import com.oplus.gallery.business_lib.photoeditor.ParametricProjectDataHelper
import com.oplus.gallery.foundation.archv2.bus.NotifierChannel
import com.oplus.gallery.foundation.archv2.bus.PublisherChannel
import com.oplus.gallery.foundation.cache.ThumbnailSizeUtils
import com.oplus.gallery.foundation.codec.extend.COLOR_SPACE
import com.oplus.gallery.foundation.database.util.MediaStoreUtils
import com.oplus.gallery.foundation.exif.utils.ExifTagParser
import com.oplus.gallery.foundation.fileaccess.GalleryFileProvider
import com.oplus.gallery.foundation.fileaccess.extension.FileExtendedContainer
import com.oplus.gallery.foundation.opengl2.texture.ITexture
import com.oplus.gallery.foundation.ui.transition.ITransitionBoundsProvider
import com.oplus.gallery.foundation.util.brandconfig.AppBrandConstants
import com.oplus.gallery.foundation.util.debug.GLog
import com.oplus.gallery.foundation.util.debug.GProperty
import com.oplus.gallery.foundation.util.debug.LogFlag
import com.oplus.gallery.foundation.util.ext.getConfigSafely
import com.oplus.gallery.foundation.util.graphic.BitmapUtils
import com.oplus.gallery.foundation.util.mask.PathMask
import com.oplus.gallery.foundation.util.math.Math2DUtil
import com.oplus.gallery.foundation.util.math.MathUtils
import com.oplus.gallery.foundation.util.multiprocess.ITransBitmap
import com.oplus.gallery.foundation.util.systemcore.ActivityManagerUtils
import com.oplus.gallery.foundation.util.systemcore.IntentUtils
import com.oplus.gallery.foundation.util.systemcore.PackageInfoUtils
import com.oplus.gallery.foundation.util.text.TextUtil.SPLIT_COMMA_SEPARATOR
import com.oplus.gallery.framework.abilities.config.IConfigAbility
import com.oplus.gallery.framework.abilities.config.keys.ConfigID
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_LOCAL_HDR_EDIT
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_OLIVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_PROJECT_SAVE
import com.oplus.gallery.framework.abilities.config.keys.ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_UHDR_EDIT
import com.oplus.gallery.framework.abilities.editing.IEditingAbility
import com.oplus.gallery.framework.abilities.editing.asset.IAvAsset
import com.oplus.gallery.framework.abilities.editing.asset.MetadataType
import com.oplus.gallery.framework.abilities.editing.session.IEditingSession
import com.oplus.gallery.framework.abilities.editing.sink.DefaultBitmapWithHdrSink
import com.oplus.gallery.framework.abilities.editing.sink.IAvSink
import com.oplus.gallery.framework.abilities.editing.track.IAvTrack
import com.oplus.gallery.framework.abilities.open.OpenAbilityConstant
import com.oplus.gallery.framework.abilities.open.OpenAbilityConstant.Companion.EDITOR_AUTO_SELECT_KEY
import com.oplus.gallery.framework.abilities.parameterization.ParametricExtendEntity
import com.oplus.gallery.framework.abilities.resourcing.IResourcingAbility
import com.oplus.gallery.framework.abilities.resourcing.key.ResourceKeyFactory
import com.oplus.gallery.framework.abilities.resourcing.options.ResourceGetOptions
import com.oplus.gallery.framework.abilities.watermark.file.PhoneInfo
import com.oplus.gallery.framework.abilities.watermark.file.WatermarkInfo
import com.oplus.gallery.framework.app.getAppAbility
import com.oplus.gallery.olive_decoder.OLiveDecode
import com.oplus.gallery.olive_decoder.OLivePhoto
import com.oplus.gallery.photoeditor.R
import com.oplus.gallery.photoeditor.common.Config
import com.oplus.gallery.photoeditor.editingvvm.EditingSubVM
import com.oplus.gallery.photoeditor.editingvvm.EditingVM
import com.oplus.gallery.photoeditor.editingvvm.EmptyReplier
import com.oplus.gallery.photoeditor.editingvvm.SessionName
import com.oplus.gallery.photoeditor.editingvvm.TObserver
import com.oplus.gallery.photoeditor.editingvvm.TopicID
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.REPLY_TOPIC_COLLECT_PHOTO_PAGE_ACTION
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.REPLY_TOPIC_INPUTS_COLLECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_CAMERA_WATERMARK_SETTING
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_EXIT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_HASSEL_WATERMARK_STATUS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_EDIT_TYPE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_HDR_DRAWABLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_IS_SUPPORT_HDR_EDIT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_IS_SUPPORT_LOSSLESS
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_MEDIA_ITEM
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_INPUTS_THUMBNAIL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_MENU_AUTO_SELECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_ORIGIN_HDR_DRAWABLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_ORIGIN_THUMBNAIL
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_ORIGIN_WATERMARK_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_PHONE_INFO
import com.oplus.gallery.photoeditor.editingvvm.TopicID.InputArguments.TOPIC_USE_P3_COLOR_GAMUT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Operating.TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_HDR_DRAWABLE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Pipeline.TOPIC_PIPELINE_MAIN_TEXTURE
import com.oplus.gallery.photoeditor.editingvvm.TopicID.Transition.REPLY_TOPIC_ENTER_TRANSITION_COLLECT
import com.oplus.gallery.photoeditor.editingvvm.UnitReplierChannel
import com.oplus.gallery.photoeditor.editingvvm.get
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.exit.ExitRuleImpl
import com.oplus.gallery.photoeditor.editingvvm.inputarguments.exit.IExitRule
import com.oplus.gallery.photoeditor.editingvvm.notifyOnce
import com.oplus.gallery.photoeditor.editingvvm.operating.EditingImagePack
import com.oplus.gallery.photoeditor.editingvvm.operating.RecoverPreviewManager.Companion.isSupportParametric
import com.oplus.gallery.photoeditor.editingvvm.pagecontainer.isSpecificStrategy
import com.oplus.gallery.photoeditor.editingvvm.subscribeR
import com.oplus.gallery.photoeditor.editingvvm.subscribeT
import com.oplus.gallery.photoeditor.editingvvm.unregisterT
import com.oplus.gallery.photoeditor.editingvvm.unsubscribeR
import com.oplus.gallery.photoeditor.editingvvm.watermark.utils.WatermarkReadUtil
import com.oplus.gallery.photoeditor.util.Picture3dUtil.PHOTO_EDITOR_INVOKER
import com.oplus.gallery.standard_lib.app.multiapp.MultiAppUtils
import com.oplus.gallery.standard_lib.codec.drawable.HdrImageDrawable
import com.oplus.gallery.standard_lib.codec.drawable.IHdrImageContent
import com.oplus.gallery.standard_lib.codec.drawable.IHdrMetadataPack
import com.oplus.gallery.standard_lib.file.File
import com.oplus.gallery.standard_lib.util.os.ContextGetter.context
import com.oplus.ocs.camera.ipuapi.process.watermark.entity.WatermarkAddressBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream

/**
 * 输入参数解析管理器，进入编辑页时，隔离外部参数，把外部参数转换成编辑内部开关
 * @param editingVM 主ViewModel
 */
internal class InputArgumentsVM(editingVM: EditingVM) : EditingSubVM(editingVM) {

    private val collectRep = object : UnitReplierChannel {
        override fun onReply(vararg args: Any) {
            if (args.size < 2) return
            val activity = args[0] as? BaseActivity ?: return
            val intent = args[1] as? Intent ?: return
            collect(activity, intent)
        }
    }

    private val collectPhotoPageActionRep = object : EmptyReplier<String>() {
        override fun onEmptyReply(): String {
            return notifyCollectPhotoPageAction()
        }
    }

    /**
     * 跳转到指定编辑页面
     */
    private var editTypePub: PublisherChannel<String>? = null
    private var phoneInfoPub: PublisherChannel<PhoneInfo>? = null
    private var hasselWatermarkStatusPub: PublisherChannel<HasselWatermarkStatus>? = null

    /**
     * 编辑图片的缩略图
     */
    private var thumbnailPub: PublisherChannel<Bitmap>? = null

    /**
     * 原图的缩图，用与项目化保存的对比
     */
    private var originThumbnailPub: PublisherChannel<Bitmap>? = null

    /**
     * hdr的数据
     */
    private var hdrImageDrawablePub: PublisherChannel<IHdrImageContent>? = null

    /**
     * 原始图片的hdr数据
     */
    private var originHdrImageDrawablePub: PublisherChannel<IHdrImageContent>? = null

    /**
     * 编辑退出时的行为
     */
    private var exitPub: PublisherChannel<Exit>? = null

    /**
     * 大图进编辑位置规则封装
     */
    private var photoPositionForEditor: ITransitionBoundsProvider? = null

    /**
     * 从编辑外部传来的数据信息，包括uri、文件路径等
     */
    private var dataSourcePub: PublisherChannel<DataSource>? = null

    /**
     * 解析的实况照片基础信息
     */
    private var olivePhotoDataSourcePub: PublisherChannel<OLivePhotoDataSource>? = null

    /**
     * 从dataSource解析出mediaItem
     */
    private var mediaItemPub: PublisherChannel<MediaItem>? = null

    /**
     * 是否支持无损缓存
     */
    private var isSupportLosslessCachePub: PublisherChannel<Boolean>? = null

    /**
     * 是否支持HDR编辑
     */
    private var isSupportHdrEditPub: PublisherChannel<Boolean>? = null

    /**
     * 进入编辑页后需要自动点击的菜单项
     */
    private var menuAutoSelectPub: PublisherChannel<Int>? = null

    /**
     * 获取水印信息后需要发布更新水印特效
     */
    private var recoverWatermarkPub: PublisherChannel<WatermarkInfo?>? = null

    /**
     * 更新水印地理位置信息后需要发布更新水印特效
     */
    private var updateWatermarkEffectPub: PublisherChannel<WatermarkInfo?>? = null

    /**
     * 项目化保存后，原始图片的水印信息
     */
    private var originWatermarkInfoPub: PublisherChannel<WatermarkInfo>? = null

    /**
     * 从相机跳转相册水印设置页传来的数据信息,旧版本的用户设置水印信息
     */
    private var cameraWatermarkSettingPub: PublisherChannel<MutableMap<String, Any?>>? = null

    /**
     * 是否需要使用P3色域，水印通过该属性判断是否需要使用P3色域的资源和颜色
     */
    private var useP3ColorGamutPub: PublisherChannel<Boolean>? = null

    /**
     * 图片编辑是否来自大图
     */
    private var isFromPhotoPage = false

    /**
     * 编辑完是否要删除原图
     * 截屏进编辑，截屏会携带此参数
     */
    private var isDeleteOriginPictureAfterEdited = false

    /**
     * 是否从相机进入编辑
     * 缩略图上划->点击编辑
     */
    private var isFromCamera = false

    /**
     * 原始图解析session，记录以备异常释放
     */
    private var originThumbnailSession: IEditingSession? = null

    /**
     * 获取原始图的sink，记录以备异常释放
     */
    private var originThumbnailSink: DefaultBitmapWithHdrSink? = null

    /**
     * 收集动画所需要的位置信息 ，传递给 Transition 包括 图片进入时的在大图位置 图片退出时在大图位置
     */
    private var collectEnterTransitionNtf: NotifierChannel<Unit>? = null

    /**
     * 是否要解析原始图片的水印信息
     * 在若是不支持项目化复原则不需要解析原始图的水印信息
     */
    private var needDecodeOriginWatermark = true

    /**
     * 大图传过来的位置信息
     */
    private var visibleRect: Rect? = null

    private val resourcingAbility: IResourcingAbility? by lazy {
        editingVM.getApplication<Application>().getAppAbility<IResourcingAbility>()
    }

    private val pipelineHdrObserver: TObserver<HdrImageDrawable?> = {
        updateHdrDrawableIfNeed(it)
    }

    private val mainTextureObserver: TObserver<ITexture> = {
        GLog.d(TAG, "mainTextureObserver $it")
        onMainTextChanged(it)
    }

    init {
        vmBus?.apply {
            editTypePub = register(TOPIC_INPUTS_EDIT_TYPE)
            phoneInfoPub = register(TOPIC_PHONE_INFO)
            hasselWatermarkStatusPub = register(TOPIC_HASSEL_WATERMARK_STATUS)
            thumbnailPub = register(TOPIC_INPUTS_THUMBNAIL)
            originThumbnailPub = register(TOPIC_ORIGIN_THUMBNAIL)
            hdrImageDrawablePub = register(TOPIC_INPUTS_HDR_DRAWABLE)
            originHdrImageDrawablePub = register(TOPIC_ORIGIN_HDR_DRAWABLE)
            dataSourcePub = register(TOPIC_INPUTS_DATA_SOURCE)
            olivePhotoDataSourcePub = register(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE)
            mediaItemPub = register(TOPIC_INPUTS_MEDIA_ITEM)
            recoverWatermarkPub = register(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT)
            updateWatermarkEffectPub = register(TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT)
            isSupportLosslessCachePub = register(TOPIC_INPUTS_IS_SUPPORT_LOSSLESS)
            isSupportHdrEditPub = register(TOPIC_INPUTS_IS_SUPPORT_HDR_EDIT)
            cameraWatermarkSettingPub = register(TOPIC_CAMERA_WATERMARK_SETTING)
            exitPub = register(TOPIC_EXIT)
            menuAutoSelectPub = register(TOPIC_MENU_AUTO_SELECT)
            originWatermarkInfoPub = register(TOPIC_ORIGIN_WATERMARK_INFO)

            register(REPLY_TOPIC_INPUTS_COLLECT, collectRep)
            register(REPLY_TOPIC_COLLECT_PHOTO_PAGE_ACTION, collectPhotoPageActionRep)
            useP3ColorGamutPub = register(TOPIC_USE_P3_COLOR_GAMUT)
        }
    }

    override fun onCreate() {
        vmBus?.apply {
            subscribeT(TOPIC_PIPELINE_HDR_DRAWABLE, pipelineHdrObserver)
            subscribeT(TOPIC_PIPELINE_MAIN_TEXTURE, mainTextureObserver)
            collectEnterTransitionNtf = subscribeR(REPLY_TOPIC_ENTER_TRANSITION_COLLECT)
        }
    }

    /**
     * 收集输入参数
     * @param activity
     * @param intent
     */
    internal fun collect(activity: BaseActivity, intent: Intent = IntentUtils.EMPTY_INTENT) {
        val extras = intent.extras
        if (extras == null) {
            GLog.w(TAG, "[collect] intent.extras is null, intent is $intent")
            return
        }
        collectForEditType(intent).also { editType ->
            // 开启跳转编辑首页流程，根据不同editType跳转不同编辑首页
            editTypePub?.publish(editType)

            // 相机水印设置页，无需收集任何图片信息
            if (editType == PhotoEditorType.WATERMARK_CAMERA.tag) {
                collectCameraDataSource(intent)
                collectPhotoUseP3ColorGamut(true)
                return@collect
            }
        }

        collectDataSource(intent, extras).also {
            if (it == null) return@collect
            dataSourcePub?.publish(it)
            // 解析的实况照片 OLivePhoto 信息
            decodeOlivePhoto(it)
            // 解析原始图的缩图，用于对比
            collectForThumbnailFromOriginFile(it)
        }

        launch {
            WatermarkReadUtil.initCameraAddressResolver(app)
        }

        collectForLosslessCache(extras).also { isSupport ->
            isSupportLosslessCachePub?.publish(isSupport)
        }

        collectForPhotoEditorPosition(extras)?.also { positionStrategy ->
            photoPositionForEditor = positionStrategy
            val windowSize =
                Size(activity.getCurrentAppUiConfig().windowWidth.current, activity.getCurrentAppUiConfig().windowHeight.current)
            positionStrategy.getEnterBounds(windowSize).let { visibleRect ->
                this.visibleRect = visibleRect
            }
        }

        collectForMenuAutoSelect(extras)?.let {
            menuAutoSelectPub?.publish(it)
        }

        collectForThumbnail(extras)

        collectForHdr(extras)

        collectForEnterTransition(visibleRect, photoPositionForEditor)

        collectForMediaFrom(extras)

        collectForDealOriginFileAfterEdit(activity, extras)

        collectForExit(intent, extras)

        collectForHdrEdit()

        collectPhoneInfo()

        collectHasselWatermarkStatus(extras)
    }

    /**
     * 收集当前图片是否需要使用P3色域
     * @param isFromCamera 是否来自相机
     */
    private fun collectPhotoUseP3ColorGamut(isFromCamera: Boolean = false) {
        val isUseP3ColorGamut = if (isFromCamera) {
            // 相机进入，获取相机传过来的ai_master_watermark_p3_enable
            vmBus?.get<MutableMap<String, Any?>>(TOPIC_CAMERA_WATERMARK_SETTING)?.getOrDefault(
                AI_MASTER_WATERMARK_P3_ENABLE, false
            ) as? Boolean ?: false
        } else {
            // 编辑进入，通过获取图片的色域判断是否使用P3色域
            editingVM.sessionProxy.getMetadata(
                MetadataType.EXTENDED,
                COLOR_SPACE
            ) as? ColorSpace == ColorSpace.get(ColorSpace.Named.DISPLAY_P3)
        }
        GLog.d(TAG, LogFlag.DL) { "[collectPhotoUseP3ColorGamut] isFromCamera $isFromCamera, isUseP3ColorGamut $isUseP3ColorGamut" }
        useP3ColorGamutPub?.publish(isUseP3ColorGamut)
    }

    private fun collectForEnterTransition(visibleRect: Rect?, photoPositionForEditor: ITransitionBoundsProvider?) {
        if (visibleRect != null && photoPositionForEditor != null) {
            collectEnterTransitionNtf?.notify(visibleRect, photoPositionForEditor)
        } else {
            GLog.d(TAG, LogFlag.DL) {
                "[collectForEnterTransition] not notify visibleRect $visibleRect photoPositionForEditor $photoPositionForEditor"
            }
        }
    }

    private fun collectHasselWatermarkStatus(bundle: Bundle) {
        bundle.getBundle(KEY_EDIT_EXTRAS)?.also { extras ->
            val hasHasselWatermark = extras.getBoolean(KEY_HAS_HASSEL_WATERMARK, false)
            val isHasselWatermarkEditable = extras.getBoolean(KEY_HASSEL_WATERMARK_EDITABLE, false)
            hasselWatermarkStatusPub?.publish(HasselWatermarkStatus(hasHasselWatermark, isHasselWatermarkEditable))
        }
    }

    private fun collectPhoneInfo() {
        app.getAppAbility<IConfigAbility>()?.use { config ->
            val isRealmeBrand = config.getBooleanConfig(ConfigID.Common.SystemInfo.IS_REALME_BRAND) ?: false
            val isOneplusBrand = config.getBooleanConfig(ConfigID.Common.SystemInfo.IS_ONEPLUS_BRAND) ?: false
            val isRegionCN = config.getBooleanConfig(ConfigID.Common.SystemInfo.IS_REGION_CN) ?: false
            val isTablet = config.getBooleanConfig(ConfigID.Common.SystemInfo.IS_TABLET) ?: false
            val marketName = config.getStringConfig(ConfigID.Common.SystemInfo.MARKET_NAME) ?: Build.MODEL
            val firstApiLevel = config.getIntConfig(ConfigID.Common.SystemInfo.FIRST_API_LEVEL) ?: Build.VERSION.SDK_INT
            val isSupportedIpuWatermark = config.getBooleanConfig(ConfigID.Business.FeatureSwitch.FEATURE_IS_SUPPORT_IPU_WATERMARK) ?: false

            phoneInfoPub?.publish(
                PhoneInfo(
                    marketName = marketName,
                    isRealmeBrand = isRealmeBrand,
                    isOneplusBrand = isOneplusBrand,
                    isRegionCN = isRegionCN,
                    isTablet = isTablet,
                    firstApiLevel = firstApiLevel,
                    isSupportedIpuWatermark = isSupportedIpuWatermark
                )
            )
        }
    }

    private fun collectForDealOriginFileAfterEdit(activity: Activity, bundle: Bundle) {
        var callingPackage = activity.callingPackage
        if (TextUtils.isEmpty(callingPackage)) {
            callingPackage = ActivityManagerUtils.reflectGetReferrer(activity)
        }
        // 由于此参数比较敏感，只有系统应用可以设置
        if (PackageInfoUtils.isSystemApp(callingPackage, activity)) {
            isDeleteOriginPictureAfterEdited = bundle.getBoolean(KEY_DELETE_ORIGIN_PICTURE, false)
        }
    }

    private fun collectForMediaFrom(bundle: Bundle) {
        isFromCamera = KEY_FROM_CAMERA.equals(bundle.getString(KEY_MEDIA_FROM), ignoreCase = true)
    }

    private fun collectForExit(intent: Intent, bundle: Bundle) {
        var isShareOperate = false
        bundle.getString(KEY_FINISH_OPERATE)?.let {
            it.split(SPLIT_COMMA_SEPARATOR).let { list ->
                isShareOperate = list.contains(FINISH_OPERATE_SHARE)
            }
        }
        val mediaSetPath = vmBus?.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.also {
            val mediaItem = getMediaItem(it) ?: return@also
            GLog.d(TAG, LogFlag.DF) {
                "[collectForExit] item[path=${PathMask.mask(mediaItem.name)}," +
                    "tagFlags=${Constants.CameraMode.getTagFlagDesc(mediaItem.tagFlags)}]"
            }
            updateMediaItem(mediaItem)
        }?.mediaSetPath
        val editType = vmBus?.get<String>(TOPIC_INPUTS_EDIT_TYPE)

        val isInternalSdcard = bundle.getBoolean(KEY_IS_INTERNAL_SDCARD, true)
        val chainFrom = IntentUtils.getStringExtra(intent, KEY_CHAIN_FROM)
        val editInvoker = IntentUtils.getStringExtra(intent, PHOTO_EDITOR_INVOKER)
        val isFromSell = KEY_FROM_SELL.equals(bundle.getString(KEY_MEDIA_FROM), ignoreCase = true)
        exitPub?.publish(
            Exit(
                isDeleteOriginPictureAfterEdited = isDeleteOriginPictureAfterEdited,
                rule = ExitRuleImpl(
                    isShareOperate,
                    isFromPhotoPage,
                    mediaSetPath,
                    isFromSell,
                    editInvoker,
                    editType,
                    chainFrom,
                    isInternalSdcard
                )
            )
        )
    }

    private fun collectForHdrEdit() {
        val mediaItem = vmBus?.get<MediaItem>(TOPIC_INPUTS_MEDIA_ITEM)
        // 是否支持Hdr编辑，要分别判断LocalHdr和UHdr,当支持UHdr编辑且是UHdr图 或是 支持LocalHdr编辑且是LocalHdr图 才认为是支持Hdr编辑
        val isSupportHdrImageEdit = mediaItem?.let {
            (getBoolean(FEATURE_IS_SUPPORT_LOCAL_HDR_EDIT) && mediaItem.isLhdrPhoto())
                || (getBoolean(FEATURE_IS_SUPPORT_UHDR_EDIT) && mediaItem.isUhdrPhoto())
        } ?: false
        isSupportHdrEditPub?.publish(isSupportHdrImageEdit)
    }

    private fun collectForPhotoEditorPosition(bundle: Bundle): ITransitionBoundsProvider? {
        return (bundle.getParcelable(KEY_PHOTO_POSITION_FOR_EDITOR) as? ITransitionBoundsProvider)
    }

    private fun collectForMenuAutoSelect(bundle: Bundle): Int? {
        /**
         * 外部进入编辑一级页后，需要自动点击进入的功能页
         * 卖场接口定的有问题，从外部跳转和大图跳转的参数位置有差异。先这样处理吧
         */
        if (isFromPhotoPage) {
            bundle.getBundle(KEY_EDIT_EXTRAS)?.getInt(EDITOR_AUTO_SELECT_KEY, MathUtils.ZERO)
        } else {
            bundle.getInt(EDITOR_AUTO_SELECT_KEY, MathUtils.ZERO)
        }?.let { input ->
            return when (input) {
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_ELIMINATE -> R.id.strategy_ai_eliminate
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_DEBLUR -> R.id.strategy_ai_deblur
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_DEREFLECT -> R.id.strategy_ai_dereflection
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_IMG_ENHANCE -> R.id.strategy_image_quality_enhance
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AIHD -> R.id.strategy_rm_specific_image_quality_enhance
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_SCENERY -> R.id.strategy_rm_ai_scenery
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_ROTATE_CLIP -> R.id.strategy_transform
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_ASSISTANT -> R.id.strategy_ai_assistant
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_COMPOSITION -> R.id.strategy_ai_composition
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_BEST_TAKE -> R.id.strategy_ai_best_take
                OpenAbilityConstant.EDITOR_AUTO_SELECT_VALUE_AI_LIGHTING -> R.id.strategy_ai_lighting
                else -> null
            }?.also {
                GLog.d(TAG, LogFlag.DL) { "[collectForMenuAutoSelect] autoSelect: input=$input, strategy=$it." }
            }
        }
        return null
    }

    private fun notifyCollectPhotoPageAction(): String {
        val mediaSetPath = vmBus?.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.mediaSetPath
        return if ((isFromPhotoPage.not() && isFromCamera)) {
            AppBrandConstants.Action.ACTION_REVIEW_CAMERA
        } else if (CameraUtil.isFromExtLockCameraAndUnlockNow(mediaSetPath)) {
            IntentConstant.ViewGalleryConstant.ACTION_REVIEW
        } else {
            Intent.ACTION_VIEW
        }
    }

    private fun collectForEditType(intent: Intent): String {
        when (IntentUtils.getStringExtra(intent, KEY_FEATURE)) {
            EDIT_SKILL_AI_ID_PHOTO_VALUE -> return EDIT_SKILL_AI_ID_PHOTO_VALUE
            EDIT_SKILL_AI_REPAIR -> return EDIT_SKILL_AI_REPAIR
        }
        val editSkill = IntentUtils.getStringExtra(intent, KEY_EDIT_SKILL)
        if (TextUtils.isEmpty(editSkill).not()) {
            return editSkill
        }

        return ""
    }

    private fun collectCameraDataSource(intent: Intent) {
        val cameraSourceList: MutableMap<String, Any?> = mutableMapOf()
        intent.apply {
            getBooleanExtra(KEY_CAMERA_COMMON_WATERMARK_OPEN, false).let {
                cameraSourceList[KEY_CAMERA_COMMON_WATERMARK_OPEN] = it
            }
            getBooleanExtra(KEY_CAMERA_FRAME_WATERMARK_OPEN, false).let {
                cameraSourceList[KEY_CAMERA_FRAME_WATERMARK_OPEN] = it
            }
            getIntegerArrayListExtra(KEY_CAMERA_COMMON_WATERMARK_TEXT_SOURCE)?.let {
                cameraSourceList[KEY_CAMERA_COMMON_WATERMARK_TEXT_SOURCE] = it
            }
            getStringExtra(KEY_CAMERA_COMMON_WATERMARK_TEXT_SIZE)?.let {
                cameraSourceList[KEY_CAMERA_COMMON_WATERMARK_TEXT_SIZE] = it
            }
            getStringExtra(KEY_CAMERA_COMMON_WATERMARK_TEXT_POSITION).let {
                cameraSourceList[KEY_CAMERA_COMMON_WATERMARK_TEXT_POSITION] = it
            }
            getStringExtra(KEY_CAMERA_COMMON_WATERMARK_TEXT_SCOPE).let {
                cameraSourceList[KEY_CAMERA_COMMON_WATERMARK_TEXT_SCOPE] = it
            }
            getStringExtra(KEY_CAMERA_WATERMARK_GROUP_NAME)?.let {
                cameraSourceList[KEY_CAMERA_WATERMARK_GROUP_NAME] = it
            }
            getStringExtra(KEY_CAMERA_WATERMARK_PHOTO_STYLE)?.let {
                cameraSourceList[KEY_CAMERA_WATERMARK_PHOTO_STYLE] = it
            }
            getStringExtra(KEY_CAMERA_WATERMARK_VIDEO_STYLE)?.let {
                cameraSourceList[KEY_CAMERA_WATERMARK_VIDEO_STYLE] = it
            }
            getBooleanExtra(KEY_CAMERA_WATERMARK_CAPTURE_MODE, true).let {
                cameraSourceList[KEY_CAMERA_WATERMARK_CAPTURE_MODE] = it
            }
            getStringExtra(AI_MASTER_WATERMARK_AUTO_TEST_RES_PATH)?.let {
                cameraSourceList[AI_MASTER_WATERMARK_AUTO_TEST_RES_PATH] = it
            }
            getStringArrayExtra(AI_MASTER_WATERMARK_FORBIDDEN_STYLES)?.let {
                val forbiddenStyles = it.joinToString(COMMA)
                cameraSourceList[AI_MASTER_WATERMARK_FORBIDDEN_STYLES] = forbiddenStyles
            }
            getBooleanExtra(AI_MASTER_WATERMARK_P3_ENABLE, true).let {
                cameraSourceList[AI_MASTER_WATERMARK_P3_ENABLE] = it
            }
        }
        cameraWatermarkSettingPub?.publish(cameraSourceList)
    }

    private fun collectDataSource(intent: Intent, bundle: Bundle): DataSource? {
        IntentUtils.getBooleanExtra(intent, KEY_EDIT_FROM_PHOTO, false).also {
            isFromPhotoPage = it
        }
        var imageUri = if (isFromPhotoPage) {
            IntentUtils.getStringExtra(intent, KEY_EDIT_IMAGE_URI)?.toUri()
        } else {
            if (vmBus?.get<String>(TOPIC_INPUTS_EDIT_TYPE) == EDIT_SKILL_AI_ID_PHOTO_VALUE) {
                IntentUtils.getParcelableExtra(intent, KEY_AI_ID_PHOTO_URI)
            } else {
                intent.data
            }
        }

        val filePath = bundle.getString(KEY_EDIT_FILE_PATH)
        val mediaItemPath = bundle.getString(KEY_EDIT_MEDIA_ITEM_PATH)
        val mediaSetPath = bundle.getString(KEY_EDIT_MEDIA_SET_PATH)
        val rotation = bundle.getInt(KEY_EDIT_ROTATION)
        if (imageUri == null && mediaItemPath != null) {
            DataManager.getMediaObject(Path.fromString(mediaItemPath))?.let { mediaObject ->
                imageUri = mediaObject.contentUri
            }
        }
        val projectEntity = filePath?.let { ParametricProjectDataHelper.queryProjectEntityByFilePath(it) }
        val recoverUri = projectEntity?.getOriginalFileUri(editingVM.getApplication())
        return imageUri?.let {
            DataSource(
                imageUri = it,
                filePath = filePath,
                mediaItemPath = mediaItemPath,
                mediaSetPath = mediaSetPath,
                rotation = rotation,
                recoverUri = recoverUri
            )
        }?.also {
            GLog.d(TAG) {
                "collectDataSource, uri=${PathMask.mask(it.imageUri.toString())}, rotation:$rotation, filePath=${PathMask.mask(filePath)}, " +
                    "itemPath=$mediaItemPath, setPath=$mediaSetPath"
            }
        } ?: kotlin.run {
            GLog.e(TAG) { "[collectDataSource]  No image file detected for edit, intent is $intent " }
            null
        }
    }

    private fun decodeOlivePhoto(source: DataSource) {
        if (getBoolean(FEATURE_IS_SUPPORT_OLIVE).not()) return

        launch(Dispatchers.IO) {
           // 文件路径不为空时使用路径，为空时尝试通过uri来获取文件路径，如果uri获取不到路径，做返回处理
            val olivePath = source.filePath
                ?: (getFilePathByUri(source.imageUri)?.also { filePath ->
                    withContext(Dispatchers.Main) {
                        dataSourcePub?.publish(source.copy(filePath = filePath))
                    }
                } ?: return@launch)
            val effectOliveDecode = OLiveDecode.create(olivePath)
            if (effectOliveDecode.isLivePhoto().not()) {
                // 如果效果图不是olive，则也不应执行初始化olive
                GLog.w(TAG, LogFlag.DL) { "[decodeOlivePhoto] isLivePhoto = false. return" }
                return@launch
            }
            // 如果效果图不是olive，则也不应执行初始化olive
            val effectOlivePhoto = effectOliveDecode.decode() ?: return@launch

            // 通过扩展信息判断该文件是否添加了可还原封面的标记（当前流程【新流程】切换封面的时候会加上该标记，用以与旧版本区分）
            val hasSupportRestoreOriginalExt = FileExtendedContainer().use { extendContainer ->
                if (source.filePath?.let { extendContainer.setDataSource(it) } ?: extendContainer.setDataSource(context, source.imageUri)) {
                    (extendContainer.getExtensionData(EXTENSION_ITEM_NAME_SUPPORT_RESTORE_ORIGINAL) != null).also {
                        GLog.d(TAG, LogFlag.DL) { "[decodeOlivePhoto] hasSupportRestoreOriginalExt = $it" }
                    }
                } else {
                    GLog.e(TAG, LogFlag.DL) { "[decodeOlivePhoto] open filePath failed, cannot judge whether support restore original or not." }
                    false
                }
            }
            // 特殊Path 不支持项目化缓存还原
            val isSpecificStrategy = vmBus?.isSpecificStrategy() == true
            if (isSpecificStrategy) {
                GLog.d(TAG, LogFlag.DL) { "[decodeOlivePhoto] isSpecificStrategy = true" }
            }

            val isSupportProject = getBoolean(FEATURE_IS_SUPPORT_PROJECT_SAVE, false) && isSpecificStrategy.not()
            val filePath = source.filePath ?: getFilePathByUri(source.imageUri)
            val (originalOliveUri, originalOLiveDecode, originalOLivePhoto) = if (isSupportProject && filePath != null) {
                ParametricProjectDataHelper.queryProjectEntityByFilePath(filePath)?.getOriginalFilePath()
            } else {
                null
            }?.let {
                val uri = GalleryFileProvider.fromFile(context, File(it))
                val decode = OLiveDecode.create(it)
                val photo = decode.decode() ?: return@let null
                Triple(uri, decode, photo)
            } ?: Triple(null, null, null)

            olivePhotoDataSourcePub?.publish(
                OLivePhotoDataSource(
                    originalOliveUri = originalOliveUri,
                    originalOLiveDecode = originalOLiveDecode,
                    originalOLivePhoto = originalOLivePhoto,
                    oliveUri = source.imageUri,
                    oLiveDecode = effectOliveDecode,
                    oLivePhoto = effectOlivePhoto,
                    hasSupportRestoreOriginalExt = hasSupportRestoreOriginalExt
                )
            )

            val privateWatermark = vmBus?.get<String>(TOPIC_INPUTS_EDIT_TYPE) == PhotoEditorType.PRIVACY_WATERMARK.tag
            // 此变量的赋值在IO线程，使用的地方在vm线程，可能存在多线程问题，但目前在使用此变量的业务上，管线执行逻辑比较长，暂时不会有问题
            needDecodeOriginWatermark = (originalOliveUri != null) && privateWatermark.not()
        }
    }

    /**
     * 通过uri获取文件bytes
     * 由于可能存在三方进入编辑界面没有传文件路径，只有uri的情况，需要通过uri来获取文件bytes
     *
     * @param uri 图片uri
     */
    fun getBytesFromUri(uri: Uri): ByteArray? {
        return runCatching {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                val byteBuffer = ByteArrayOutputStream()
                byteBuffer.use {
                    val buffer = ByteArray(BYTE_ARRAY_LIMIT_VALUE)
                    var bytesRead: Int = -1
                    // 从输入流中读取数据并写入 ByteArrayOutputStream
                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        byteBuffer.write(buffer, 0, bytesRead)
                    }
                    byteBuffer.toByteArray()
                }
            }
        }.onFailure {
            GLog.e(TAG, LogFlag.DL, "[getBytesFromUri],  execute error ", it)
        }.getOrNull()
    }

    /**
     * 通过uri获取文件路径
     * 由于可能存在三方进入编辑界面没有传文件路径，只有uri的情况，需要通过uri来获取文件路径
     *
     * @param uri 图片uri
     */
    @SuppressLint("Range")
    private fun getFilePathByUri(uri: Uri): String? {
        val cursor = context.contentResolver.query(
            uri, arrayOf(
                MediaStoreUtils.DATA
            ),
            null, null
        )

        cursor?.use {
            if (cursor.moveToFirst() && cursor.getColumnIndex(MediaStoreUtils.DATA) != -1) {
                return MultiAppUtils.convertToLocalPath(it.getString(cursor.getColumnIndex(MediaStoreUtils.DATA).coerceAtLeast(0)))
            }
        }

        GLog.e(TAG, LogFlag.DL) { "[getFilePathByUri], can not find file path" }
        return null
    }

    private fun collectForLosslessCache(bundle: Bundle): Boolean {
        return bundle.getBoolean(KEY_EDIT_SUPPORT_LOSS_LESS_CACHE, false)
    }

    private fun collectForThumbnail(bundle: Bundle) {
        bundle.getBitmap(KEY_EDIT_THUMBNAIL).also { bitmap ->
            if ((bitmap == null) || bitmap.isRecycled) {
                GLog.d(TAG, LogFlag.DF, "collectForThumbnail bitmap is isRecycled: ${bitmap?.isRecycled}")
                launch {
                    collectForThumbnailFromUri().also { thumbnail ->
                        if (thumbnail == null) {
                            GLog.e(TAG, "[collectForThumbnail] thumbnail is null ")
                        } else {
                            thumbnailPub?.publish(thumbnail)
                        }
                    }
                }
            } else {
                /**
                 * 这里因为图片直接从binder中获取，thumbnailPub后续会持续更新数据，后续如果更新可能无法锁定binder的这块内存，
                 * 所以需要在获取的时候直接拷贝一份，使用拷贝的，旧的不能回收，该bitmap为大图内存缓存中的缩图
                 */
                val copyBmp = bitmap.copy(bitmap.getConfigSafely(), bitmap.isMutable)
                thumbnailPub?.publish(copyBmp)
            }
        }
    }

    /**
     * 从Bundle里获取hdr图
     */
    private fun collectForHdr(bundle: Bundle) {
        val grayImage: Bitmap = bundle.getBitmap(KEY_EDIT_BRIGHTEN_GRAY_IMAGE) ?: return

        if (grayImage.isRecycled) {
            GLog.e(TAG, LogFlag.DL) { "collectForHdr grayImage isRecycled true" }
            return
        }

        val metadata: IHdrMetadataPack = bundle.getParcelable(KEY_EDIT_BRIGHTEN_METADATA) ?: return
        val rotation = bundle.getInt(KEY_EDIT_ROTATION)

        /**
         * 旋转照片，使得增益图的方向和主图一致
         * 这里 recycle = true 因为图片直接从binder中获取，需要主动recycle一下，加快内存释放
         */
        var rotatedBitmap = BitmapUtils.rotateBitmap(grayImage, rotation, grayImage.config, true)
        if (grayImage == rotatedBitmap) {
            /**
             * 这里因为图片直接从binder中获取，需要主动recycle一下，加快内存释放，
             * hdrImageDrawablePub后续会持续更新数据，后续如果更新可能无法锁定binder的这块内存，
             * 所以需要在获取的时候直接拷贝一份，使用拷贝的，回收旧的
             */
            rotatedBitmap = grayImage.copy(grayImage.getConfigSafely(), grayImage.isMutable)
            BitmapUtils.recycleSilently(grayImage)
        }

        val hdrImageDrawable = HdrImageDrawable(rotatedBitmap, metadata)
        hdrImageDrawablePub?.publish(hdrImageDrawable)
    }

    /**
     * 从uri加载缩略图
     */
    private fun collectForThumbnailFromUri(): Bitmap? {
        return vmBus?.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.let {
            val mediaItem = getMediaItem(it) ?: let {
                GLog.e(TAG, LogFlag.DL, "[collectForThumbnailFromUri] error, can't get mediaItem")
                return null
            }
            val resourceKey = ResourceKeyFactory.createResourceKey(mediaItem) ?: return@let null
            val options = ResourceGetOptions().apply {
                this.inThumbnailType = ThumbnailSizeUtils.getFullThumbnailKey()
            }
            resourcingAbility?.requestBitmap(resourceKey, options)?.result
        }
    }

    /**
     * 这里创建一个用于获取原始对比图的session，用完即弃
     * 输出分辨率为960，内存开销可控
     */
    private fun collectForThumbnailFromOriginFile(dataSource: DataSource?) {
        // 支持参数化，且属于常规编辑，才收集原始图缩图用于对比
        val editType = vmBus?.get<String>(TOPIC_INPUTS_EDIT_TYPE)
        if (isSupportParametric(editType).not()) {
            GLog.d(TAG, LogFlag.DL, "[collectForThumbnailFromOriginFile] not Parametric or Parametric Entry!")
            return
        }
        launch {
            dataSource?.filePath?.let { path ->
                val projectEntity = ParametricProjectDataHelper.queryProjectEntityByFilePath(path)
                val originFileUri = projectEntity?.getOriginalFileUri(editingVM.getApplication()) ?: return@launch
                GLog.d(TAG, LogFlag.DL) { "collectForThumbnailFromOriginFile: create session for thumbnail decode" }
                originThumbnailSession = app.getAppAbility<IEditingAbility>()?.use { ability ->
                    ability.createSession {
                        sessionName = SessionName.COLLECT_ORIGIN_THUMBNAIL
                        isForeground = true
                        pipelineEditMode = editingVM.pipelineEditMode
                        longPhotoMinRatio = Config.Loader.LOADER_PHOTO_MAX_RATIO
                        photoMaxLength = Config.Render.getMaxTextureSize()
                    }
                }?.apply {
                    val track = editor.addTrack("OriginFileThumbnailTrack", IAvTrack.AvTrackType.VIDEO)
                    val asset = editor.addAsset(originFileUri, null, IAvAsset.AvAssetType.VIDEO)
                    val maxLength = ThumbnailSizeUtils.getTargetSizeInType(ThumbnailSizeUtils.getFullThumbnailKey())
                    originThumbnailSink = DefaultBitmapWithHdrSink(maxLength, maxLength, onBitmapReceive = { bitmap, hdrImageContent ->

                        if (needDecodeOriginWatermark) {
                            val (watermarkInfo) = WatermarkReadUtil(app).getWatermarkDatas(asset.metadataAccessor, Math2DUtil.DEG_0I)
                            originWatermarkInfoPub?.publish(watermarkInfo)
                            watermarkInfo.printWatermarkInfo()
                        }

                        hdrImageContent?.let { originHdrImageDrawablePub?.publish(it) }
                        originThumbnailPub?.publish(bitmap)

                        originThumbnailSink?.also {
                            originThumbnailSession?.config?.sinkConfig?.removeSink(IAvSink.AvSinkType.OUTPUT, it)
                            originThumbnailSink = null
                        }
                        originThumbnailSession?.sessionId?.also {
                            app.getAppAbility<IEditingAbility>()?.closeSession(it)
                            originThumbnailSession = null
                        }
                    }).also {
                        editor.withRenderingOnce(it.sinkDescriptionKey) {
                            config.sinkConfig.addAsOnceFrameSink(sinkType = IAvSink.AvSinkType.BITMAP, it)
                            addClip(track, asset)
                        }
                    }
                }
            }
        }
    }

    /**
     * 从Bundle里获取缩略图
     */
    private fun Bundle.getBitmap(key: String): Bitmap? {
        return runCatching {
            getBinder(key)?.let {
                ITransBitmap.Stub.asInterface(it).bitmap
            }
        }.onFailure {
            GLog.e(TAG) { "[collectForPreviewBitmap] get thumbnail failed, error: $it" }
        }.getOrNull()
    }

    private fun updateHdrDrawableIfNeed(hdrImageDrawable: HdrImageDrawable?) {
        vmBus?.unsubscribe(TOPIC_PIPELINE_HDR_DRAWABLE, pipelineHdrObserver)
        vmBus?.get<IHdrImageContent>(TOPIC_INPUTS_HDR_DRAWABLE) ?: let {
            // 非大图入编辑场景不会有hdr drawable，需要从管线中获取
            GLog.d(TAG, "pipelineHdrObserver $hdrImageDrawable")
            if (hdrImageDrawable != null) {
                hdrImageDrawablePub?.publish(hdrImageDrawable)
            }
        }
    }

    private fun onMainTextChanged(texture: ITexture) {
        vmBus?.unsubscribe(TOPIC_PIPELINE_MAIN_TEXTURE, mainTextureObserver)
        vmBus?.get<DataSource>(TOPIC_INPUTS_DATA_SOURCE)?.let {
            updateWatermarkInfo(it, texture)
        }
    }

    /**
     * 更新mediaItem
     *
     * 顺便更新 是否为hdr视频的Olive 值
     */
    private fun updateMediaItem(mediaItem: MediaItem) {
        mediaItemPub?.publish(mediaItem)
    }

    private fun updateWatermarkInfo(data: DataSource? = null, texture: ITexture) {
        launch {
            var mediaItem: MediaItem? = null
            var photoRotation: Int = Math2DUtil.DEG_0I
            data?.let {
                mediaItem = getMediaItem(data) ?: return@let
                mediaItem?.let { updateMediaItem(it) }
                photoRotation = getOrientation(data.imageUri, mediaItem)
            }
            WatermarkReadUtil(app).also {
                // 非相机进入，从图片拓展信息中获取是否为P3色域
                collectPhotoUseP3ColorGamut(false)
                val (watermarkInfo, capture) = it.getWatermarkDatas(
                    editingVM.sessionProxy, mediaItem, photoRotation, Size(texture.width, texture.height)
                )
                updateWatermark(watermarkInfo, capture)
                watermarkInfo.content?.locationArray?.also { locationArray ->
                    it.updateLocation(locationArray, ::updateWatermarkAddress)
                }
            }
        }
    }

    /**
     * 更新水印上的地址信息
     * 由于该方法是异步执行的，赋值时需要切换到UI线程
     */
    private fun updateWatermarkAddress(cameraAddress: WatermarkAddressBean?, address: String?) {
        launch(Dispatchers.Main.immediate) {
            val imagePack = vmBus?.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK) ?: EditingImagePack()
            imagePack.watermarkInfo = imagePack.watermarkInfo?.copy(
                content = imagePack.watermarkInfo?.content?.copy(
                    location = address,
                    cameraAddress = cameraAddress
                )
            )
            updateWatermarkEffectPub?.publish(imagePack.watermarkInfo)
            GLog.d(TAG, LogFlag.DL) { "[updateWatermarkAddress]" }
            imagePack.watermarkInfo?.printWatermarkInfo()
        }
    }

    private fun updateWatermark(watermarkInfo: WatermarkInfo?, capture: Bitmap?) {
        launch(Dispatchers.Main.immediate) {
            val imagePack = vmBus?.get<EditingImagePack>(TopicID.Operating.TOPIC_IMAGE_PACK) ?: EditingImagePack()
            imagePack.captureBitmap = capture
            imagePack.watermarkInfo = watermarkInfo
            recoverWatermarkPub?.publish(watermarkInfo)
            vmBus?.notifyOnce(TopicID.Operating.TOPIC_IMAGE_PACK, imagePack)
            GLog.d(TAG, LogFlag.DL) { "[updateWatermark]" }
        }
    }

    /**
     * 通过uri获取图片角度，如果在uri获取不到角度的情况下，通过mediaItem获取角度
     * @param uri 图片的uri
     * @param mediaItem 图片对应的MediaItem
     * @return 图片的角度
     */
    private fun getOrientation(uri: Uri, mediaItem: MediaItem?): Int {
        var orientation = WatermarkReadUtil.getOrientationWithIdUri(uri)
        if (orientation == WatermarkReadUtil.INVALID_ORIENTATION) {
            uri.path?.apply {
                orientation = WatermarkReadUtil.getOrientationWithFilePath(this)
            }
        }
        if (mediaItem != null && orientation == WatermarkReadUtil.INVALID_ORIENTATION) {
            orientation = mediaItem.rotation
        }
        return orientation
    }

    private fun getMediaItem(dataSource: DataSource): MediaItem? {
        var mediaItem: MediaItem? = null
        if (dataSource.mediaItemPath.isNullOrEmpty().not()) {
            mediaItem = LocalMediaDataHelper.getLocalMediaItem(Path.fromString(dataSource.mediaItemPath))
        }
        if (mediaItem == null) {
            val mediaObject = DataManager.findPathByUri(dataSource.imageUri, null)?.let { DataManager.getMediaObject(it) }
            if (mediaObject is MediaItem) {
                mediaItem = mediaObject
            }
        }

        /**
         * 由于一些特殊场景(如互传过来的照片，立刻进编辑)，获取mediaItem时tagFlags获取不到，所以按需单独获取下tagFlags
         */
        if (mediaItem?.isSync == false) {
            kotlin.runCatching {
                context.contentResolver.openFileDescriptor(dataSource.imageUri, "r")?.use {
                    val exifInterface = ExifInterface(it.fileDescriptor)
                    val tagFlagFromExif = ExifTagParser.getImageTagFlag(
                        exifInterface, it.fileDescriptor, null, context.resources.getStringArray(
                            com.oplus.gallery.framework.datatmp.R.array.base_tagflags_prefixes
                        )
                    )
                    mediaItem.tagFlags = tagFlagFromExif
                    GLog.d(TAG, LogFlag.DL) { "[getMediaItem],tagFlagFromExif is：$tagFlagFromExif" }
                }
            }.onFailure {
                GLog.e(TAG, LogFlag.DL) { "[getMediaItem], openFileDescriptor error，uri:${dataSource.imageUri}, exception:${it.message}" }
            }
        }
        return mediaItem
    }

    override fun onDestroy() {
        super.onDestroy()
        vmBus?.apply {
            originThumbnailSink?.also {
                originThumbnailSession?.config?.sinkConfig?.removeSink(IAvSink.AvSinkType.OUTPUT, it)
                originThumbnailSink = null
            }
            originThumbnailSession?.sessionId?.also {
                app.getAppAbility<IEditingAbility>()?.closeSession(it)
                originThumbnailSession = null
            }

            unsubscribe(TOPIC_PIPELINE_MAIN_TEXTURE, mainTextureObserver)
            unsubscribe(TOPIC_PIPELINE_HDR_DRAWABLE, pipelineHdrObserver)

            unregisterT(TOPIC_HASSEL_WATERMARK_STATUS, hasselWatermarkStatusPub)
            unregisterT(TOPIC_PHONE_INFO, phoneInfoPub)
            unregisterT(TOPIC_INPUTS_EDIT_TYPE, editTypePub)
            unregisterT(TOPIC_INPUTS_THUMBNAIL, thumbnailPub)
            unregisterT(TOPIC_ORIGIN_THUMBNAIL, originThumbnailPub)
            unregisterT(TOPIC_INPUTS_HDR_DRAWABLE, hdrImageDrawablePub)
            unregisterT(TOPIC_ORIGIN_HDR_DRAWABLE, originHdrImageDrawablePub)
            unregisterT(TOPIC_INPUTS_DATA_SOURCE, dataSourcePub)
            unregisterT(TOPIC_OLIVE_OLIVE_PHOTO_DATA_SOURCE, olivePhotoDataSourcePub)
            unregisterT(TOPIC_INPUTS_MEDIA_ITEM, mediaItemPub)
            unregisterT(TOPIC_INPUTS_IS_SUPPORT_LOSSLESS, isSupportLosslessCachePub)
            unregisterT(TOPIC_INPUTS_IS_SUPPORT_HDR_EDIT, isSupportHdrEditPub)
            unregisterT(TOPIC_EXIT, exitPub)
            unregisterT(TOPIC_OPERATING_RECOVER_WATERMARK_EFFECT, recoverWatermarkPub)
            unregisterT(TOPIC_OPERATING_UPDATE_WATERMARK_EFFECT, updateWatermarkEffectPub)
            unregisterT(TOPIC_CAMERA_WATERMARK_SETTING, cameraWatermarkSettingPub)
            unregisterT(TOPIC_ORIGIN_WATERMARK_INFO, originWatermarkInfoPub)

            unsubscribeR(REPLY_TOPIC_ENTER_TRANSITION_COLLECT, collectEnterTransitionNtf)

            unregister(REPLY_TOPIC_INPUTS_COLLECT, collectRep)
            unregisterT(TOPIC_USE_P3_COLOR_GAMUT, useP3ColorGamutPub)
        }
    }

    /**
     * 编辑退出时的行为
     * @param isDeleteOriginPictureAfterEdited: 编辑完是否要删除原图
     * @param rule 退出编辑页的规则
     */
    internal data class Exit(val isDeleteOriginPictureAfterEdited: Boolean, val rule: IExitRule)

    companion object {
        const val TAG = "InputArgumentsVM"

        const val BYTE_ARRAY_LIMIT_VALUE = 1024

        /**
         * AI证件照编辑
         */
        const val EDIT_SKILL_AI_ID_PHOTO_VALUE = "aiidphoto"

        /**
         * AI修复编辑
         */
        const val EDIT_SKILL_AI_REPAIR = "ai_repair"

        /**
         * AI证件照的uri
         */
        const val KEY_AI_ID_PHOTO_URI = "aiidphotouri"

        private const val KEY_FEATURE = "feature"
        private const val KEY_FINISH_OPERATE = "finish_operate"
        private const val FINISH_OPERATE_SAVE = "save"
        private const val FINISH_OPERATE_SHARE = "share"

        const val KEY_IS_INTERNAL_SDCARD = "isInternalSdcard"
        const val SHARE_SOURCE = "shareSource"
        const val TABTOOLBAR_USER_ACTION_EDIT_CAMERA_VALUE = "edit_camera"
        private const val KEY_DELETE_ORIGIN_PICTURE = "DeleteOriginPictureAfterEdited"

        /**
         * OlivePhoto编辑目录
         */
        private const val EDIT_OLIVE_PHOTO_DIR = "edit_olive_photo"
        private const val ORIGIN_THUMBNAIL_TRACK = "OriginThumbnailTrack"
        private const val EXTENSION_ITEM_NAME_SUPPORT_RESTORE_ORIGINAL = "olive.supportRestoreOriginal"

        const val KEY_CAMERA_COMMON_WATERMARK_OPEN = "common_watermark_open"
        const val KEY_CAMERA_FRAME_WATERMARK_OPEN = "frame_watermark_open"
        const val KEY_CAMERA_COMMON_WATERMARK_TEXT_SOURCE = "common_watermark_text_source"
        const val KEY_CAMERA_COMMON_WATERMARK_TEXT_SIZE = "common_watermark_text_size"
        const val KEY_CAMERA_COMMON_WATERMARK_TEXT_POSITION = "common_watermark_text_position"
        const val KEY_CAMERA_COMMON_WATERMARK_TEXT_SCOPE = "common_watermark_text_scope"
        const val KEY_CAMERA_WATERMARK_GROUP_NAME = "watermark_style_group_name"
        const val KEY_CAMERA_WATERMARK_PHOTO_STYLE = "camera_watermark_photo_style_id"
        const val KEY_CAMERA_WATERMARK_VIDEO_STYLE = "camera_watermark_video_style_id"
        const val KEY_CAMERA_WATERMARK_CAPTURE_MODE = "ai_master_watermark_is_capture_mode"
        const val AI_MASTER_WATERMARK_AUTO_TEST_RES_PATH = "ai_master_watermark_auto_test_res_path"
        // 相机通知需要隐藏水印样式
        const val AI_MASTER_WATERMARK_FORBIDDEN_STYLES = "ai_master_watermark_forbidden_styles"
        // 相机通知是否需要使用P3色域水印资源
        const val AI_MASTER_WATERMARK_P3_ENABLE = "ai_master_watermark_p3_enable"
    }
}

/**
 * 图片的哈苏水印状态
 * @param hasHasselWatermark 当前水印是否是哈苏水印
 * @param isHasselWatermarkEditable 根据水印相关信息，判断能否编辑水印
 *        图片中：水印机型不为哈苏联名机型 或者 水印拍摄数据为空 都不能编辑水印
 */
internal data class HasselWatermarkStatus(val hasHasselWatermark: Boolean, val isHasselWatermarkEditable: Boolean)

/**
 * 编辑的图片文件来源（Mark by wudanyang：这里要加一个原始图filePath和非参数化编辑后的图片filePath）
 * @param imageUri: 文件uri
 * @param filePath: 文件路径
 * @param mediaSetPath: 媒体库路径
 * @param mediaItemPath: 媒体库集合路径
 * @param rotation: 旋转角度
 */
internal data class DataSource(
    val imageUri: Uri,
    val filePath: String?,
    val mediaItemPath: String?,
    val mediaSetPath: String?,
    val rotation: Int,
    val recoverUri: Uri? = null,
    val recoverFilePath: String? = null,
    var hasContentEdit: Boolean = false
) {
    override fun toString(): String {
        return buildString {
            append("DataSource(")
            append(
                listOfNotNull(
                    "imageUri=$imageUri",
                    "filePath=${if (GProperty.DEBUG_EDITING_ABILITY) filePath else PathMask.mask(filePath)}",
                    "mediaItemPath=$mediaItemPath",
                    "mediaSetPath=$mediaSetPath",
                    "rotation=$rotation",
                    "recoverUri=$recoverUri",
                    "recoverFilePath=$recoverFilePath",
                    "hasContentEdit=$hasContentEdit",
                ).joinToString(", ")
            )
            append(")")
        }
    }
}

/**
 * 参数化数据源
 * @param operatingStr 参数化操作栈的 json 字符串
 * @param isParametric 进入编辑的文件是否为参数化
 * @param pathEntity 参数化的路径信息
 */
internal data class ParametricDataSource(
    val operatingStr: String,
    val isParametric: Boolean,
    val pathEntity: ParametricExtendEntity? = null,
    val isSupportParametric: Boolean = true
)

/**
 * 针对整个编辑的olive图片源的共享数据: 原始数据，当前图（效果图），按需获取即可
 *
 * 原始图信息，指向项目化缓存的原始文件，主要用于整个编辑需要原始图信息
 * @param originalOliveUri  缓存图地址 可null
 * @param originalOLiveDecode originalOliveUri的解码实例
 * @param originalOLivePhoto originalOliveUri解析结果
 *
 * 当前图信息
 * @param oliveUri olive文件的uri
 * @param oLiveDecode oliveUri的解码实例
 * @param oLivePhoto oliveUri解析结果
 */
internal data class OLivePhotoDataSource(
    // 原始图 视频信息
    val originalOliveUri: Uri?,
    val originalOLiveDecode: OLiveDecode?,
    val originalOLivePhoto: OLivePhoto?,

    // 当前图的信息
    val oliveUri: Uri,
    val oLiveDecode: OLiveDecode,
    val oLivePhoto: OLivePhoto,
    /**
     * 标记备份的olive原始大图（换封面时备份的初始图）是否存储的为无水印图，用以与旧版本区分
     * olive1.0版本存储的可能会带水印
     * olive2.0储存的是无水印图
     *
     * 来源：从原始olive文件扩展信息里读取的
     */
    val hasSupportRestoreOriginalExt: Boolean = false
)

/**
 * 收集intent的类型
 */
internal enum class IntentType {
    /**
     * 收集intent是为了给上一个页面用（通过setResult方式）
     */
    FOR_RESULT,

    /**
     * 收集intent是为了给下一个页面用，启动大图
     */
    FOR_LAUNCH_PHOTO_PAGE,

    /**
     * 收集intent是为了给下一个页面用，启动分享页面
     */
    FOR_LAUNCH_SHARE
}

/**
 * 编辑完成的后续操作
 */
internal enum class FinishOperation {

    /**
     * 编辑完保存文件
     */
    TO_PHOTO_PAGE,

    /**
     * 编辑完分享图片
     */
    TO_SHARE,

    /**
     * 关闭编辑页
     */
    FINISH
}

/**
 * 取消的后续操作
 */
internal enum class CancelOperation {

    /**
     * 取消
     */
    CANCEL,

    /**
     * 关闭编辑页
     */
    FINISH
}